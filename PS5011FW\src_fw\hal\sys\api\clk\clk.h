#ifndef _CLK_H_
#define _CLK_H_

#include "typedef.h"
#include "hal/sys/reg/sys_pd0_reg.h"
#include "common/math_op.h"
#include "clk_api.h"

#if (PS5017_EN)
typedef struct ClockPLLParameter {
} ClockPLLParameter_t;

#define M_SET_CLK_CTRL(CTRL, SRC, SRC_MASK, OFFSET)	do{ \
    												R32_SYS0_CLK[(CTRL)] &=  (~(SRC_MASK << OFFSET)); \
													R32_SYS0_CLK[(CTRL)] |=  (((SRC) & SRC_MASK) << OFFSET) ; \
													} while(0)

#define	M_CLK_SET_PCIE_SYSTEM_CLOCK(VAL)			(R32_SYS0_CLK[R32_SYS0_CLK_CTRL4] = (((VAL) & PCIE_SYSCLK_FREQ_MASK) << PCIE_SYSCLK_FREQ_SHIFT))
#define M_GET_CLK_CTRL_CPU_CLK_SEL() 				((R32_SYS0_CLK[R32_SYS0_CLK_CTRL0] & (CR_CPU_CLK_SEL_MASK << CR_CPU_CLK_SEL_SHIFT)) >> CR_CPU_CLK_SEL_SHIFT)
#define M_GET_CLK_CTRL_LZSS_CLK_SEL() 				((R32_SYS0_CLK[R32_SYS0_CLK_CTRL1] & (CR_LZSS_CLK_SEL_MASK << CR_LZSS_CLK_SEL_SHIFT)) >> CR_LZSS_CLK_SEL_SHIFT)
#define M_GET_CLK_CTRL_AES_CLK_SEL() 				((R32_SYS0_CLK[R32_SYS0_CLK_CTRL1] & (CR_AES_CLK_SEL_MASK << CR_AES_CLK_SEL_SHIFT)) >> CR_AES_CLK_SEL_SHIFT)
#define M_GET_CLK_CTRL_SHA_CLK_SEL() 				((R32_SYS0_CLK[R32_SYS0_CLK_CTRL1] & (CR_SHA_CLK_SEL_MASK << CR_SHA_CLK_SEL_SHIFT)) >> CR_SHA_CLK_SEL_SHIFT)
#define M_GET_CLK_CTRL_SYS_CLK_SEL() 				((R32_SYS0_CLK[R32_SYS0_CLK_CTRL0] & (CR_SYS_CLK_SEL_MASK << CR_SYS_CLK_SEL_SHIFT)) >> CR_SYS_CLK_SEL_SHIFT)
#define M_GET_CLK_CTRL_ECC_CLK_SEL() 				((R32_SYS0_CLK[R32_SYS0_CLK_CTRL2] & (CR_ECC_CLK_SEL_MASK << CR_ECC_CLK_SEL_SHIFT)) >> CR_ECC_CLK_SEL_SHIFT)
#define M_GET_CLK_CTRL_COP0_RATE_SEL() 				((R32_SYS0_CLK[R32_SYS0_CLK_CTRL0] & (CR_COP0_RATE_SEL_MASK << CR_COP0_RATE_SEL_SHIFT)) >> CR_COP0_RATE_SEL_SHIFT)
#define M_GET_CLK_CTRL_FLHR_CLK_SEL() 				((R32_SYS0_CLK[R32_SYS0_CLK_CTRL2] & (CR_FLHR_CLK_SEL_MASK << CR_FLHR_CLK_SEL_SHIFT)) >> CR_FLHR_CLK_SEL_SHIFT)
#define M_GET_CLK_CTRL_FLHW_CLK_SEL() 				((R32_SYS0_CLK[R32_SYS0_CLK_CTRL2] & (CR_FLHW_CLK_SEL_MASK << CR_FLHW_CLK_SEL_SHIFT)) >> CR_FLHW_CLK_SEL_SHIFT)

#define M_GET_CLK_CTRL_CPU_DIV() 					((R32_SYS0_CLK[R32_SYS0_CLK_CTRL0] & (CR_CPU_DIV_MASK << CR_CPU_DIV_SHIFT)) >> CR_CPU_DIV_SHIFT)
#define M_GET_CLK_CTRL_LZSS_DIV() 					((R32_SYS0_CLK[R32_SYS0_CLK_CTRL1] & (CR_LZSS_DIV_MASK << CR_LZSS_DIV_SHIFT)) >> CR_LZSS_DIV_SHIFT)
#define M_GET_CLK_CTRL_AES_DIV() 					((R32_SYS0_CLK[R32_SYS0_CLK_CTRL1] & (CR_AES_DIV_MASK << CR_AES_DIV_SHIFT)) >> CR_AES_DIV_SHIFT)
#define M_GET_CLK_CTRL_SHA_DIV() 					((R32_SYS0_CLK[R32_SYS0_CLK_CTRL1] & (CR_SHA_DIV_MASK << CR_SHA_DIV_SHIFT)) >> CR_SHA_DIV_SHIFT)
#define M_GET_CLK_CTRL_SYS_DIV() 					((R32_SYS0_CLK[R32_SYS0_CLK_CTRL0] & (CR_SYS_DIV_MASK << CR_SYS_DIV_SHIFT)) >> CR_SYS_DIV_SHIFT)
#define M_GET_CLK_CTRL_ECC_DIV() 					((R32_SYS0_CLK[R32_SYS0_CLK_CTRL2] & (CR_ECC_DIV_MASK << CR_ECC_DIV_SHIFT)) >> CR_ECC_DIV_SHIFT)

#else /* (PS5017_EN) */

#define	CLOCK_PLL_N_LOWERBOUND						(2)
#define	CLOCK_PLL_N_UPPERBOUND						(15)
#define CLOCK_IP_CENTER1_NOUSE_NUM					(5)
#define CLOCK_FLASH_INTERFACE_DIV_DOUBLE_IDX		(7)
#define CLOCK_CPU_CLOCK_RATE_LIMIT_FOR_ACCESS_ROM	(333) //333MHz

typedef struct ClockPLLParameter {
	U8 N1					: 4;
	U8 N2					: 4;
	U8 ubM;
	U8 S					: 4;
	U8 Band					: 3;
	U8 btReferenceSelect	: 1;
	U16 uwPLLCLKOUT1;
} ClockPLLParameter_t;

#define M_GET_CLK_CTRL_DIV_VAL(CENTER_IP,VAL)		(VAL = (R8_SYS0_CLK[(CENTER_IP)] & (CR_CTR0_DIV_0_MASK )))
#define M_CLOCK_GET_DOUBLE_DIV(Idx) 				((CLOCK_DIV_1 == (Idx)) ? CLOCK_DIV_2 : (((Idx) + 1) * 2))
#if (!ASIC)
#define	M_CHK_PLL_LOCK()							(R32_SYS0_CLK[R32_SYS0_CLK_CFG0] & (SR_PLL_LOCK_MASK << SR_PLL_LOCK_SHIFT))
#define	M_CHK_CCU_CTR_LOCK()						(R32_SYS0_CLK[R32_SYS0_CLK_CFG0] & (CCU_CTR_LOCK_MASK << CCU_CTR_LOCK_SHIFT))
#endif /* ASIC */
#endif /* (PS5017_EN) */

AOM_INIT void ClockSwitchMode(CLOCK_FLH_IF_t FLHIFSel);
#endif /* _CLK_H_ */
