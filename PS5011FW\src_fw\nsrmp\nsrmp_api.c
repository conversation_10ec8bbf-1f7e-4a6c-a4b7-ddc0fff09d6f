
#define _NSRMP_API_C_

#include <stdio.h>
#include <string.h>
#include "nsrmp/nsrmp_api.h"
#include "nsrmp/hal_nsrmp.h"
#include "hal/apu/apu_api.h"
#include "nvme_api/misc/shr_hal_debug.h"
#include "nvme_api/pic/shr_hal_pic_uartt.h"

#if (HOST_MODE == NVME)
static void insertion_sort(volatile U16 *uw_arr, U16 uw_len)
{
	U32 ul_i, ul_j, ul_temp;

	for (ul_i = 1; ul_i < uw_len; ul_i++) {
		ul_temp = uw_arr[ul_i];

		for (ul_j = ul_i; ul_j > 0 && uw_arr[ul_j - 1] > ul_temp; ul_j--) {
			uw_arr[ul_j] = uw_arr[ul_j - 1];
		}
		uw_arr[ul_j] = ul_temp;
	}

	return;
}

static void swap(volatile U16 *uw_a, volatile U16 *uw_b)
{
	*uw_a = *uw_a ^ *uw_b;
	*uw_b = *uw_a ^ *uw_b;
	*uw_a = *uw_a ^ *uw_b;

	return;
}

static void update_hw_ns_oft_tbl(NSRMP_API_STRUCT_PTR p_nsrmp_api)
{
	U32 ul_i;

	for (ul_i = 0; ul_i < NS_NUM; ul_i++) {
		HAL_APU_SET_NS_OFS_NODE(ul_i, p_nsrmp_api->ns_oft_tbl[ul_i].start);
	}

	return;
}

static void update_hw_ns_pool_tbl(NSRMP_API_STRUCT_PTR p_nsrmp_api, U8 ub_del_nsid_0_base)
{
	U32 ul_i;
	U16 uw_sort_len, uw_swap_cnt;
	U32 ul_ns_pool_swap_base_a, ul_ns_pool_swap_base_b;

	uw_swap_cnt = 0;
	uw_sort_len = PMD_NUM;

	for (ul_i = ub_del_nsid_0_base + 1; ul_i < NS_NUM; ul_i++) {
		uw_swap_cnt += p_nsrmp_api->ns_oft_tbl[ul_i].len;
	}

	if (0 == uw_swap_cnt) {
		return;
	}

	ul_ns_pool_swap_base_a = HAL_APU_GET_NS_POOL_BASE() + (p_nsrmp_api->ns_oft_tbl[ub_del_nsid_0_base].start * sizeof(U16));
	ul_ns_pool_swap_base_b = HAL_APU_GET_NS_POOL_BASE() + (p_nsrmp_api->ns_oft_tbl[ub_del_nsid_0_base + 1].start * sizeof(U16));

	for (ul_i = 0; ul_i < uw_swap_cnt; ul_i++) {
		swap((volatile U16 *)(ul_ns_pool_swap_base_a + ul_i * sizeof(U16)), (volatile U16 *)(ul_ns_pool_swap_base_b + ul_i * sizeof(U16)));
	}

	// calculate free nodes of ns pool
	for (ul_i = 0; ul_i < NS_NUM; ul_i++) {
		if (p_nsrmp_api->ns_oft_tbl[ul_i].valid) {
			uw_sort_len -= p_nsrmp_api->ns_oft_tbl[ul_i].len;
		}
	}

	uw_sort_len += p_nsrmp_api->ns_oft_tbl[ub_del_nsid_0_base].len;

	// sort from first free ns pool node
	insertion_sort((volatile U16 *)(ul_ns_pool_swap_base_b + ((uw_swap_cnt - p_nsrmp_api->ns_oft_tbl[ub_del_nsid_0_base].len) * sizeof(U16))), uw_sort_len);

	return;
}


static void update_hw_inverse_tbl(NSRMP_API_STRUCT_PTR p_nsrmp_api)
{
	U32 ul_i, ul_j;
	U16 uw_ns_pool_idx, uw_rep_pmd;

	uw_ns_pool_idx = 0;

	for (ul_i = 0; ul_i < NS_NUM; ul_i++) {
		if (p_nsrmp_api->ns_oft_tbl[ul_i].valid) {
			uw_ns_pool_idx = p_nsrmp_api->ns_oft_tbl[ul_i].start;

			for (ul_j = 0; ul_j < p_nsrmp_api->ns_oft_tbl[ul_i].len; ul_j++) {
				uw_rep_pmd = HAL_APU_GET_NS_POOL_NODE(uw_ns_pool_idx);

				HAL_APU_SET_NS_INVERSE_NODE(uw_rep_pmd, ul_i, ul_j);

				uw_ns_pool_idx++;
			}
		}
	}

	return;
}

void nsrmp_api_init(NSRMP_API_STRUCT_PTR p_nsrmp_api)
{
	U32 ul_i;

	p_nsrmp_api->ns_pool_free_idx = 0;

	for (ul_i = 0; ul_i < NS_NUM; ul_i++) {
		p_nsrmp_api->ns_oft_tbl[ul_i].valid = FALSE;
		p_nsrmp_api->ns_oft_tbl[ul_i].start = 0;
		p_nsrmp_api->ns_oft_tbl[ul_i].len = 0;
	}

#if 0
	// set NS1 as default NS
	p_nsrmp_api->ns_oft_tbl[0].valid = TRUE;
	p_nsrmp_api->ns_oft_tbl[0].start = 0;
	p_nsrmp_api->ns_oft_tbl[0].len = PMD_NUM;
#endif

	update_hw_ns_oft_tbl(p_nsrmp_api);

	return;
}

void nsrmp_api_ns_tables_init(NSRMP_API_STRUCT_PTR p_nsrmp_api)
{
	ApuLRSramInit();

	return;
}

void nsrmp_api_set_ns(NSRMP_API_STRUCT_PTR p_nsrmp_api, U8 ub_nsid_0_base, U16 uw_4gb_unit)
{
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, p_nsrmp_api->ns_oft_tbl[ub_nsid_0_base].valid == FALSE);

	HAL_APU_SET_NS_OFS_NODE(ub_nsid_0_base, p_nsrmp_api->ns_pool_free_idx);

	p_nsrmp_api->ns_oft_tbl[ub_nsid_0_base].start = p_nsrmp_api->ns_pool_free_idx;

	p_nsrmp_api->ns_pool_free_idx += uw_4gb_unit;

	p_nsrmp_api->ns_oft_tbl[ub_nsid_0_base].len = uw_4gb_unit;

	p_nsrmp_api->ns_oft_tbl[ub_nsid_0_base].valid = TRUE;

	update_hw_ns_oft_tbl(p_nsrmp_api);

	update_hw_inverse_tbl(p_nsrmp_api);

	return;

}
void nsrmp_api_del_ns(NSRMP_API_STRUCT_PTR p_nsrmp_api, U8 ub_nsid_0_base)
{
	U32 ul_i;

	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, p_nsrmp_api->ns_oft_tbl[ub_nsid_0_base].valid == TRUE);

	// update NS pool table
	update_hw_ns_pool_tbl(p_nsrmp_api, ub_nsid_0_base);

	// update NS offset table
	for (ul_i = ub_nsid_0_base + 1; ul_i < NS_NUM; ul_i++) {
		if (p_nsrmp_api->ns_oft_tbl[ul_i].valid) {
			p_nsrmp_api->ns_oft_tbl[ul_i].start -= p_nsrmp_api->ns_oft_tbl[ub_nsid_0_base].len;
		}
	}

	// update free node start index
	p_nsrmp_api->ns_pool_free_idx -= p_nsrmp_api->ns_oft_tbl[ub_nsid_0_base].len;

	// invalid deleted NS
	p_nsrmp_api->ns_oft_tbl[ub_nsid_0_base].valid = FALSE;

	p_nsrmp_api->ns_oft_tbl[ub_nsid_0_base].len = 0;

	update_hw_ns_oft_tbl(p_nsrmp_api);

	update_hw_inverse_tbl(p_nsrmp_api);

	return;
}
U32 nsrmp_api_fw_lca_search(NSRMP_API_STRUCT_PTR p_nsrmp_api, U8 ub_nsid, U32 ul_lca)
{
	//U8 ub_ns_num; ub_i;
	U32 ul_lca_high, ul_lca_low, ul_ns_pool_idx;

	ul_lca_high = ((ul_lca & BITMSK(9, 20)) >> 20);
	ul_lca_low = (ul_lca & BITMSK(20, 0));


	if (!p_nsrmp_api->ns_oft_tbl[ub_nsid - 1].valid) {
		//uart_printf("\nnsrmp: search pool invalid!");
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, 0);
	}

	ul_ns_pool_idx = p_nsrmp_api->ns_oft_tbl[ub_nsid - 1].start + ul_lca_high;
	/*
		uart_printf("\nLCA:  %x\n", ul_lca);
		uart_printf("high: %x\n", ul_lca_high);
		uart_printf("low:  %x\n", ul_lca_low);
		uart_printf("s:    %x\n", p_nsrmp_api->ns_oft_tbl[ub_nsid + 1].start);
		uart_printf("lca:  %x\n", ul_lca_high);
		uart_printf("idx:  %x\n", ul_ns_pool_idx);
	*/
	return ((HAL_APU_GET_NS_POOL_NODE(ul_ns_pool_idx) << 20) | ul_lca_low);
}
#endif

