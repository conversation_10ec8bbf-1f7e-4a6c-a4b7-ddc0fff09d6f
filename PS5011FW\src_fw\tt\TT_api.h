#ifndef _TT_API_H_
#define _TT_API_H_

#include "setup.h"
#include "typedef.h"
#include "aom/aom_api.h"
#include "hal/sys/api/rtt/rtt_api.h"

#define	M_CELSIUS_TO_KELVIN(TEMPERATURE)	    (TEMPERATURE.B.btSign?(TT_KELVIN-TEMPERATURE.B.Degree):(TT_KELVIN+TEMPERATURE.B.Degree))
#define M_TT_CHECK_READ_TEMPERATURE_DONE()		(INVALID_TAG_ID == gTT.uwFlashTemperatureTagID)

#define TT_DETECT_INTERVAL_SECOND	(1)

#define TT_TEMP_OFFSET_EXTERNAL_TO_FLASH	    (0	) 	// from extern thermal sensor to flash.
#define TT_TEMP_OFFSET_ONDIE_TO_FLASH	        (0x80+0x19)		// from On-die thermal sensor to flash.
#define TT_HYSTERESIS_THRESHOLD	                (2)
#define TT_HYSTERESIS_OFFSET	                (4)
#define TT_FORUMLA_VERSION                      (1)
#if (PS5017_EN)
#define M_TT_CONVERT_THROUGHPUT_TO_APU_DELAY_UNIT(MB_PER_SEC)		(7812/(MB_PER_SEC)) //change Host throughput MB/sec to APU delay,  APU delay=((1000000000*4)/((MB_PER_SEC)*1024*500))
#define TT_READ_WRITE_DELAY_VALUE_MULTIPLE	(2) //TT_READ_WRITE_DELAY_VALUE_MULTIPLE*value must below 65536 to avoid overflow case
#define TT_PROTECT_START_HOST_THOUGHPUT    (50) //TMT protect mode init Target 50MB/s 
#define TT_READ_WRITE_DELAY_VALUE_START_TARGET (M_TT_CONVERT_THROUGHPUT_TO_APU_DELAY_UNIT(600)) // Set Start APU Delay 600MBps
#define TT_INFOBLK_DELAY_SETTING_MAX_INDEX (15)
#define TT_INFOBLK_DELAY_SETTING_MIN_INDEX (0)
#define TT_INFOBLK_DELAY_INDEX_VALUE_LIGHT (1)
#define TT_MTQ_DELAY_VALUE_UNIT            (0x1000)
#endif /* (PS5017_EN) */
#define TT_KELVIN	                            (273)
#define TT_NORMAL_THRESHOLD                     (25+TT_KELVIN)
#define TT_WARNING_THRESHOLD	                (gTT.uwTMT1Threshold)
#define TT_CRITICAL_THRESHOLD		            (gTT.uwTMT2Threshold)
#define TT_OVER_THRESHOLD	                    (gTT.uwTMT2Threshold)
#define TT_UNDER_THRESHOLD	                    (0+TT_KELVIN)
#define TT_FATAL_CONTROLLER_THRESHOLD                  (120+TT_KELVIN)
#define TT_PROTECT_CONTROLLER_THRESHOLD                (110+TT_KELVIN)
#define TT_PROTECT_FLASH_THRESHOLD                (80+TT_KELVIN)
#define TT_TMT2_FLASH_THRESHOLD                   (gTT.uwTMT2Threshold)
#define TT_TMT1_FLASH_THRESHOLD                   (gTT.uwTMT1Threshold)
#define TT_TMT1_HYSTERESIS_FLASH_THRESHOLD        (gTT.uwTMT1Threshold-(gTT.ubHysteresisThreshold*TT_HYSTERESIS_OFFSET))
#define TT_TMT1_HYSTERESIS_MIDDLE_FLASH_THRESHOLD      (gTT.uwTMT1Threshold-((gTT.ubHysteresisThreshold*TT_HYSTERESIS_OFFSET)/2))
#define TT_STABLE_FLASH_THRESHOLD	                (gTT.uwTMT2Threshold)
#define TT_CHECK_THERMAL_INTERVAL	10

#define TT_STABLE_SECOND	                    (120)

#define TT_MTQ_DELAY_VALUE_MAX	                        (0xF000)
#define TT_MTQ_DELAY_VALUE_HEAVY	                    (0x2000)
#define TT_MTQ_DELAY_VALUE_LIGHT	                    (0x1000)
#define TT_MTQ_DELAY_VALUE_POWER_STATE_0		                (0)
#define TT_MTQ_DELAY_VALUE_POWER_STATE_1		                (0x5000)
#define TT_MTQ_DELAY_VALUE_POWER_STATE_2		                (0xA000)

#define TT_READ_WRITE_DELAY_VALUE_MAX 	(0xF)
#define TT_READ_WRITE_DELAY_VALUE_HEAVY	(2)
#define TT_READ_WRITE_DELAY_VALUE_LIGHT	(1)
#define TT_READ_WRITE_DELAY_POWER_STATE_0	(0)
#define TT_READ_WRITE_DELAY_POWER_STATE_1	(20)
#define TT_READ_WRITE_DELAY_POWER_STATE_2	(40)

#define TT_START_DELAY_TIME_MILLISECOND	1000

#define TT_DEFAULT_MAX_TEMPERATURE	(0xFF)	// -127
#define TT_DEFAULT_MIN_TEMPERATURE	(0x7F)	// 127
#define TT_INVALID_TEMPERATURE_KELVIN (0xFFFF)

#define TT_DRIVE_LOG_SET_DELAY	0
#define TT_DRIVE_LOG_SENSOR_ERROR	1

#define TT_ZQCL	(25)

typedef enum TTMTQHostDelayEnum {
	TT_MTQ_HOST_DELAY_NONE,
	TT_MTQ_HOST_DELAY_PROTECT,
	TT_MTQ_HOST_DELAY_INCREASE_LIGHT,
	TT_MTQ_HOST_DELAY_INCREASE_HEAVY,
	TT_MTQ_HOST_DELAY_DECREASE_LIGHT,
	TT_MTQ_HOST_DELAY_DECREASE_HEAVY,
	TT_MTQ_HOST_DELAY_POWER_STATE_0,
	TT_MTQ_HOST_DELAY_POWER_STATE_1,
	TT_MTQ_HOST_DELAY_POWER_STATE_2,
	TT_MTQ_HOST_DELAY_GC,  // 9
	TT_MTQ_HOST_DELAY_GC_XFER_DATA_IN_1,
	TT_MTQ_HOST_DELAY_GC_XFER_DATA_IN_2,
	TT_MTQ_HOST_DELAY_GC_XFER_DATA_IN_3,
	TT_MTQ_HOST_DELAY_GC_RESTART,
	TT_MTQ_HOST_DELAY_GC_WL_FINISH,
	TT_MTQ_HOST_DELAY_GC_SUSTAIN_BG,//15
	TT_MTQ_HOST_DELAY_GC_SUSTAIN_INIT,
	TT_MTQ_HOST_DELAY_GC_FTLGC_MODIFY_DELAY,
	TT_MTQ_HOST_DELAY_GC_FINDSRC_DELAY,
	TT_MTQ_HOST_DELAY_GC_BACKUPGCSA_DELAY,
	TT_MTQ_HOST_DELAY_GC_INSERTGCSA_DELAY,// 20
	TT_MTQ_HOST_DELAY_GC_FTLGC_TUNE_DELAY,
	TT_MTQ_HOST_DELAY_AVERAGE_GC,
	TT_MTQ_HOST_DELAY_COPY_UNIT,
	TT_MTQ_HOST_DELAY_PLN_SCP,
	TT_MTQ_HOST_DELAY_LPM3_BACKUP
} TTMTQHostDelayEnum_t;

typedef enum TTThermalSensorEnum {
	TT_THERMAL_SENSOR_INIT,
	TT_THERMAL_SENSOR_GET_TEMPERATURE,
} TTThermalSensorEnum_t;

typedef enum TTTMTEnum {
	TT_TMT_ROUGH_DISPATCH,
	TT_TMT_FATAL,
	TT_TMT_PROTECT,
	TT_TMT_TMT2,
	TT_TMT_TMT1,
	TT_TMT_FINETUNE_TMT2,
	TT_TMT_FINETUNE_TMT1,
	TT_TMT_RESUME
} TTTMTEnum_t;

typedef enum TTDelaySrcEnum {
	TT_DELAY_SRC_TT,
	TT_DELAY_SRC_LPM,
	TT_DELAY_SRC_TOTAL_NUM,
} TTDelaySrcEnum_t;

typedef enum TTClockIPEnum {
	TT_CLOCK_IP0_SYSTEM,
	TT_CLOCK_IP1_CPU,
	TT_CLOCK_IP5_ECC,
	TT_CLOCK_IP_TOTAL_NUM,
} TTClockIPEnum_t;

typedef enum TTDelaySettingEnum {
	TT_DELAY_SETTING_DEFAULT,
	TT_DELAY_SETTING_POWER_STATE_1,
	TT_DELAY_SETTING_POWER_STATE_2,
	TT_DELAY_SETTING_PROTECT,
	TT_DELAY_SETTING_TOTAL_NUM,
} TTDelaySettingEnum_t;

typedef union TTTemperatureCelsius {
	U8 ubAll;
	struct {
		U8 Degree: 7;
		U8 btSign: 1;
	} B;
} TTTemperatureCelsius_t;

typedef struct THERMAL_THROTTLING {
	U64 uoCriticalWarningCompositeTemperatureStartTimeInMillisecond;
	U64 uoTMTStartTimeInMillisecond;
	U64 uoPollingTimeInMillisecond;
	RTTSelfTimeoutCondition_t ThermalSensorTimer;
	U32 ulStableStartTimeInSecond;
	U16 uwReadDelay[TT_DELAY_SRC_TOTAL_NUM];
	U16 uwWriteDelay[TT_DELAY_SRC_TOTAL_NUM];
	U16 uwMTQDelay[TT_DELAY_SRC_TOTAL_NUM];

	U16 uwExternalMaxDelayTime;
	U16 uwFlashTemperatureTagID;
	U8 ubConsecutiveOverTMTThresholdCnt;
	TTTemperatureCelsius_t ubCurrentTemperatureOnDie;
	TTTemperatureCelsius_t ubCurrentTemperatureExternal;
	TTThermalSensorEnum_t ThermalSensorState;
	union {
		U8 ubAll;
		struct {
			U8 CriticalOrWarning: 2; // 0:none, 1:warning, 2:Citical
			U8 btEnableTT: 1;
			U8 btInitDone: 1;
			U8 btTemperatureValid: 1;
			U8 btEnableFlashTemperature: 1;
			U8 : 2;
		} B;
	} ubOthers;

	union {
		U8 ubAll;
		struct {
			U8 btProtect: 1;	// 1st priority
			U8 btPowerState2: 1; // 2nd priority
			U8 btPowerState1: 1; // 3rd priority
			U8 : 5;
		} B;
	} ubClockSelect;

	//---  MP parameter --- //
	U16 uwMaxTMTValue;
	U16 uwMinTMTValue;
	U8 ubHysteresisThreshold;
	U16 uwTMT1Threshold;
	U16 uwTMT2Threshold;
	U16 uwOverThreshold;
	U16 uwUnderThreshold;
	U16 uwRWLockThreshold;
	U16 uwStableThreshold;
	U16 uwNandShutDownTemperature;
	TTTemperatureCelsius_t ubTemperatureOffsetExternalToFlash;	// from ext. sensor to flash.
	TTTemperatureCelsius_t ubTemperatureOffsetLowExternalToFlash;
	TTTemperatureCelsius_t ubTemperatureOffsetHighExternalToFlash;
	TTTemperatureCelsius_t ubTemperatureOffsetOnDieToFlash;	// from on-die sensor to flash.
	U8 ubFormulaLowerOffset;
	U8 ubFormulaUpperOffset;
	U8 ubFormulaConstant;
	U8 ubI2CSlaveAddr;
	U8 ubCheckThermalInterval;
	U8 ubTriggerTimer;
#if (PS5017_EN)
	U8 ubTMT1MaxMTQDelay;
	U8 ubTMT1MaxAPUReadDelay;
	U8 ubTMT1MaxAPUWriteDelay;
	U8 ubTMT2MaxMTQDelay;
	U8 ubTMT2MaxAPUReadDelay;
	U8 ubTMT2MaxAPUWriteDelay;
	U8 ubTMTInfoBlkMaxDelaySettingEn;
	U8 ubTMTInfoBlkDelaySettingIndex;
#endif /* (PS5017_EN) */
	struct {
		U16 uwReadDelay;
		U16 uwWriteDelay;
		U16 uwMTQDelay;
		struct {
			U8 ubSrc;
			U8 ubDiv;
		} ClockIP[TT_CLOCK_IP_TOTAL_NUM];
	} DelaySetting[TT_DELAY_SETTING_TOTAL_NUM];
} THERMAL_THROTTLING_t;
AOM_SYSTEM_AREA_INIT void TTSetTTVariablesFromInfoBlk(U32 *pulSystemBlkTableAddr, U32 *pulNvmeConfigAddr);
extern THERMAL_THROTTLING_t gTT;
AOM_TT void TTKelvinToCelsius(TTTemperatureCelsius_t *ubCelsius, U16 uwKelvin);

#if (!BURNER_MODE_EN)
AOM_INIT void TTSaveDriveLog(U8 ubEvent,  U16 uwMTQDelayTime, U16 uwWriteDelayTime, U16 uwReadDelayTime, U8 ubDelaySrc, U8 ubAddMode);  // consider Power state transition time, pls don't change overlay. -YK
AOM_TT void TTAdjustPerformance(TTMTQHostDelayEnum_t MTQHostDelay);
AOM_TT U16 TTGetThermalSensorTemperature(void);
AOM_TT void TTInitVariable(void);
AOM_INIT_2 void TTInitVariableTagID(void);
AOM_TT void TTThermalSensorInit(void);
AOM_TT void TTTriggerFlashTemperature(void);
AOM_TT U8 TTGetFlashTemperature(TTTemperatureCelsius_t *pubTemp);
AOM_TT void TTCalculateTMT(U64 uoOperationTime);
AOM_TT void TTCalculateCriticalWarningTime(U64 uoOperationTime);
void TT(void);
AOM_TT void TTNandZQCLErrorRecord(void);
#endif  /* (!BURNER_MODE_EN) */
#endif /* _TT_H_ */
