/****************************************************************************
*
*  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
*  All rights reserved
*
*  The content of this document is confidential and shall be applied
*  subject to the terms and conditions of the license agreement and
*  other applicable laws. Any unauthorized access, use or disclosure
*  of this document is strictly prohibited and may be punishable
*  under laws.
*
*  VUC_MicronGetBEC.c
*
*
*
****************************************************************************/
/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "media_scan/media_scan_api.h"
#include "VUC_MicronGetBEC.h"
#include "nvme_api/nvme/shr_hal_nvme_vuc_api.h"
#include "hal/dmac/dmac_pop_cmd.h"

/*
 * ---------------------------------------------------------------------------------------------------
 *   extern global variables
 * ---------------------------------------------------------------------------------------------------
 */


/*
 * ---------------------------------------------------------------------------------------------------
 *   private prototypes
 * ---------------------------------------------------------------------------------------------------
 */
/*
 * ---------------------------------------------------------------------------------------------------
 *   private
 * ---------------------------------------------------------------------------------------------------
 */


/*
 * ---------------------------------------------------------------------------------------------------
 *   public
 * ---------------------------------------------------------------------------------------------------
 */
#if (VUC_MICRON_NICKS_EN)
#if IM_N28
void VUCMicronGetBEC (U32 ulPayloadAddr)
{
	DMACParam_t DMACParam = {{0}};
	U32 *pulBECRecord;
	U8 ubCh, ubCE, ubErrorLevel;
	pulBECRecord = (U32 *)(ulPayloadAddr);

	// clean return address
	DMACParam.DMACSetValue.ulDestAddr = ulPayloadAddr;
	DMACParam.DMACSetValue.ul32ByteNum = SIZE_IN_32B(SIZE_8KB);
	gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
	DMACSetValue(&DMACParam, gulDMACDirectWaitDone_Callback, 0);
	while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
		DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
	}

	for (ubCh = 0; ubCh < MAX_CHANNEL; ubCh++) {
		for (ubCE = 0; ubCE < MAX_CE_PER_CHANNEL; ubCE++) {
			if (ubCh < gFlhEnv.ubChannelExistNum) {
				if (ubCE < gFlhEnv.ubCENumberInCh[ubCh]) {
					for (ubErrorLevel = 0; ubErrorLevel < MEDIASCAN_N28_BEC_TYPE_CNT; ubErrorLevel++) {
						U32 ulLocation = (ubCE * MAX_CHANNEL + ubCh) * (MICRON_VUC_BEC_PAYLOAD_SIZE_PER_DIE / SIZE_4B);
						U8 ubGlobalCE = ubCE * gFlhEnv.ubChannelExistNum + ubCh;
						pulBECRecord[(ulLocation + ubErrorLevel)] = MediaScanHandleErrorBitNum(MEDIA_SCAN_GET_ERROR_BIT_NUM, ubGlobalCE, ubErrorLevel);
					}
				}
			}
		}
	}
}
#else /* IM_N28 */
void VUCMicronGetBEC (U32 ulPayloadAddr)
{
	DMACParam_t DMACParam = {{0}};
	U32 *pulBECRecord;
	U8 ubCh, ubCE, ubErrorLevel, ubLUN;
	GETBECResponseHEADER_t *pResponseHeader;
	pResponseHeader = (GETBECResponseHEADER_t *)ulPayloadAddr;
	U32 ulLocation;
	U8 ubGlobalCE;
	U8 ubGlobalDie;
	pulBECRecord = (U32 *)(ulPayloadAddr + MICRON_VUC_RESPONSE_HEADER_LENGTH);

	// clean return address
	DMACParam.DMACSetValue.ulDestAddr = ulPayloadAddr;
	//DMACParam.DMACSetValue.ul32ByteNum = SIZE_IN_32B(SIZE_2KB);
	DMACParam.DMACSetValue.ul32ByteNum = SIZE_IN_32B_CEILING(SIZE_2KB + 12);
	gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
	DMACSetValue(&DMACParam, gulDMACDirectWaitDone_Callback, 0);
	while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
		DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
	}

	pResponseHeader->ubResponseHeaderFormatVersion = 0x00;
	pResponseHeader->ubResponseDataFotmatVersion = MICRON_VUC_RESPONSE_FORMAT_BINARY;  // Data type : binary
	pResponseHeader->uwCMDClass = MICRON_VUC_GET_BEC_COMMAND_CLASS;
	pResponseHeader->uwCMDCode = MICRON_VUC_GET_BEC_COMMAND_CODE;
	pResponseHeader->uwStatus = 0; //
	pResponseHeader->ulDataPayloadSize = MICRON_VUC_BEC_PAYLOAD_SIZE; // 32die * BEC type * 4 Byte(Per BEC)

	for (ubCE = 0; ubCE < (gFlhEnv.ubCENumber / gFlhEnv.ubChannelExistNum); ubCE++) {
		for (ubCh = 0; ubCh < gFlhEnv.ubChannelExistNum; ubCh++) {
			ubGlobalCE = (ubCE * gFlhEnv.ubChannelExistNum  + ubCh);
			for (ubLUN = 0; ubLUN < gFlhEnv.ubLUNperTarget ; ubLUN++) {
				ubGlobalDie = (ubGlobalCE * gFlhEnv.ubLUNperTarget) + ubLUN;
				ulLocation = ((U32)ubGlobalCE + gFlhEnv.ubCENumber * ubLUN) * (MICRON_VUC_BEC_PAYLOAD_SIZE_PER_DIE / SIZE_4B);
				for (ubErrorLevel = 0; ubErrorLevel < MEDIA_SCAN_BEC_RECORD_MAX_IDX; ubErrorLevel++) {
					pulBECRecord[(ulLocation + ubErrorLevel)] = gpVTDBUF->MediaScan.ulErrorBitRecordForLDPCFrame[ubGlobalDie][ubErrorLevel];
				}
			}
		}
	}
}

#endif /* IM_N28 */
#endif /*VUC_MICRON_NICKS_EN*/
