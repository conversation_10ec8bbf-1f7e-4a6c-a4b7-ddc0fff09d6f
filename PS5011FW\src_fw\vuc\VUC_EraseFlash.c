#include "burner/Burner_api.h"
#include "hal/pic/uart/uart_api.h"
#include "nvme_api/nvme/shr_hal_nvme.h"
#include "host/VUC_handler.h"
#include "vuc/VUC_command.h"
#include "vuc/VUC_EraseFlash.h"
#include "ftl/ftl_api.h"

void VUC_EraseFlash(VUC_OPT_HCMD_PTR_t pCmd)
{
	if (TRUE == gFlhEnv.ulFlashDefaultType.BitMap.btNoSupport) {
		pCmd->ubState = CMD_ERROR;
		gVUCVar.ubVucCmdErrStatus = HOST_ERROR_FEATURE;
		return;
	}

	U8 ubSLCMode, ubA2_or_DA, ubCE, ubRuleIndex;
	U16 uwBlock;
	U32 ulPCA = 0;

	FlashAccessInfo_t FlaInfo = {0};

	M_UART(VUC_, "\nVUC_ERASE_BLOCK");

	ubSLCMode = pCmd->vuc_sqcmd.vendor.DirectEraseFlash.CmdMode >> 1;// 0: Normal , 1: else
	ubA2_or_DA = ubSLCMode & BIT0;// 0: A2 , 1: DA

	if (VUC_ERASE_BLOCK == pCmd->vuc_sqcmd.vendor.DirectEraseFlash.ubFeature) {
		uwBlock = pCmd->vuc_sqcmd.vendor.DirectEraseFlash.uwBlock;
		ubCE = pCmd->vuc_sqcmd.vendor.DirectEraseFlash.ubCE;

		FlaInfo.ubChannel = ubCE % gFlhEnv.ubChannelExistNum;
		FlaInfo.ubFlashCE = ubCE / gFlhEnv.ubChannelExistNum;
		FlaInfo.ubPlane = (U8)(uwBlock & gubBurstsPerBankMask);
		FlaInfo.ubLUN = (uwBlock >> gubBurstsPerBankLog) >> gPCARule_Block.ubBit_No;
		FlaInfo.ulBlock = (uwBlock >> gubBurstsPerBankLog) & gPCARule_Block.ulMask;

		ubRuleIndex = (ubSLCMode == FALSE) ? COP0_PCA_RULE_0 : COP0_PCA_RULE_2;

		ulPCA = FTLReverseCop0CieInPCA(FlaGetPCA(FlaInfo.ubPlane, FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, FlaInfo.uwPage, FlaInfo.ulBlock, ubRuleIndex));
	}
	else if (VUC_ERASE_PCA == pCmd->vuc_sqcmd.vendor.DirectEraseFlash.ubFeature) {
		ulPCA = pCmd->vuc_sqcmd.vendor.DirectEraseFlash.ulPCA;
	}

	if (FALSE == ubSLCMode) {
		BurnerEraseBLK(XLC_MODE, MODE_D3, NULL, ulPCA);
	}
	else {
		BurnerEraseBLK(SLC_A2_MODE, MODE_D3, NULL, ulPCA);
	}

	BurnerWaitCQDone(guwBurnerBGTag);
}
