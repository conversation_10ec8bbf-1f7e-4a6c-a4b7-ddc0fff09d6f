/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "setup.h"
#if VRLC_IN_MMO
#include "VRLC/VRLC_api.h"
#include "VRLC/VRLC.h"
#include "tempco_api.h"
#include "hal/fip/fip_api.h"
#include "hal/fip/fip.h"
#include "hal/pic/uart/uart_api.h"
U8 gubTempcoProbeVersion = TEMPCO_ES_SAMPLE_MPPR;
TEMPCO_META gTempcoMeta = {
	/*	{
			0x080, 0x081, 0x007, 0x00f, 0x017, 0x01f, 0x027, 0x02f, 0x037, 0x03f,
			0x040, 0x042, 0x044, 0x046, 0x048, 0x04a, 0x04c, 0x0d0, 0x140, 0x140,
			0x142, 0x143, 0x144, 0x206, 0x24e,
		},*/
	{
		0x007, 0x00f, 0x017, 0x01f, 0x027, 0x02f, 0x037, 0x03f,
		0x040, 0x042, 0x044, 0x046, 0x048, 0x04a, 0x04c, 0x141,
	},
	{
		{
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		},
		{
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			0x02, 0x02, 0x02, 0x02, 0x02, 0x03, 0x03, 0x00,
		},
		{
			0x00, 0x01, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00,
			0x05, 0x05, 0x05, 0x05, 0x05, 0x06, 0x06, 0x00,
		},
		{
			0x01, 0x01, 0x01, 0x01, 0x00, 0xFF, 0xFF, 0xFF,
			0x07, 0x07, 0x07, 0x07, 0x08, 0x09, 0x09, 0x00,
		},
		{
			0x01, 0x02, 0x02, 0x01, 0xFF, 0xFF, 0xFF, 0xFF,
			0x08, 0x08, 0x08, 0x09, 0x0A, 0x0B, 0x0B, 0x00,
		},
	},
	TEMPCO_META_LASTMARK
};
U8 TempcoNandImprintCommand(void)
{
	U8 ubDataCnt, ubMPPR;
	U16 uwData;
	U8 ubTimeout = FALSE;
	while (R32_FCTL_CH[CH0][R32_FCTL_MT_TRIG] & CHK_QUEUE_7_TO_0_MT_BUSY_MASK);
	while ((R32_FCTL_CH[CH0][R32_FCTL_MT_STATE] & WP_STATE_SHIFT_MASK) != (WP_STATE_IDLE));
	while (R32_FCTL_CH[CH0][R32_FCTL_FPU_TRIG] & CHK_FPU_BUSY);
	while (MTQ_INT_RDY_AUTOPOLL_ALLCLEAR != R32_FCTL_CH[CH0][R32_FCTL_INT_RDY]);

	FIPWaitFIPInternalReady(CH0, GERNAL_TIMEOUT_THRESHOLD);
	FlaCEControl(CH0, CE0, ENABLE);
	REG32 *pFlaReg = R32_FCTL_CH[CH0];
	pFlaReg[R32_FCTL_PIO_CMD] = 0xED;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
	FIPWaitFIPInternalReady( CH0, GERNAL_TIMEOUT_THRESHOLD);
	pFlaReg[R32_FCTL_PIO_CMD] = 0x70;
	while ((U8)(pFlaReg[R32_FCTL_PIO_DAT]) != 0xE0);
	pFlaReg[R32_FCTL_PIO_CMD] = 0x05;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x04;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x08;	//offset 2052
	pFlaReg[R32_FCTL_PIO_CMD] = 0xE0;
	pFlaReg[R32_FCTL_HS_MODE] &= CLR_DAT_MID;

	/*nand imprint command data present
	8Byte per parameter
	2052:2059 format revision
	2060:2067 reticle Wave ID
	2068:2075 USTP
	2076:2083 FAB information
	2084:2091 MPPR
	2092:2099 FAB conversion ID
	2100:2107 test revision year
	2108:2115 test revision subrev
	2116:2123 test-data-year
	2124:2131 test-data-month
	2132:2139 test-data-day

	 1 byte data 2byte data bar
	 3 byte data 4byte data bar
	 5 byte data 6byte data bar
	 7 byte data 8byte data bar
	 EX: 0x04 0xFC
	 	0x04 0xFC
	 	0x04 0xFC
	 	0x04 0xFC*/
	for (ubDataCnt = 0; ubDataCnt < TEMPCO_IMPRINT_DATA_SIZE ; ubDataCnt++) {
		__asm("DSB");
		if ( FAIL == FlaWaitFlashReady(GERNAL_TIMEOUT_THRESHOLD)) {
			ubTimeout = TRUE;
		}
		if (ubDataCnt == 0) {
			pFlaReg[R32_FCTL_HS_MODE] |= PIO_DAT_FIRST_BIT;
		}
		else if (ubDataCnt == TEMPCO_IMPRINT_DATA_SIZE - 1) {
			pFlaReg[R32_FCTL_HS_MODE] |= PIO_DAT_LAST_BIT;
		}
		__asm("DSB");
		uwData = (U16)pFlaReg[R32_FCTL_PIO_DAT];
		if (ubDataCnt % TEMPCO_IMPRINT_DATA_SHIFT == 0) {
			UartPrintf("\nByte :%d, Data:%l", ubDataCnt, uwData);
			if (ubDataCnt == TEMPCO_MPPR_OFFSET) {
				ubMPPR = uwData;
			}
		}
		pFlaReg[R32_FCTL_HS_MODE] &= CLR_DAT_MID;
	}
	if (TRUE == ubTimeout) {
		gFlhEnvMP.ulFailedCEBMP |= BIT(gubLogicalCEMapPhysicalCE[CH0][CE0]);
	}
	FlaCEControl(CH0, CE0, DISABLE);
	return ubMPPR;
}

void TempcoChangeTableValue(void)
{
	U8 ubTempcoLevelTableIndex = 0, ubTempcoTrimIndex, ubReadBackValue;
	if (gubTempcoProbeVersion >= TEMPCO_ES_SAMPLE_MPPR) {
		gTempcoMeta.uwTrimAddr[TEMPCO_OFFSET_PV_INDEX] = TEMPCO_ES_SAMPLE_ADDRESS;
	}
	for (ubTempcoTrimIndex = 0; ubTempcoTrimIndex < TEMPCO_TRIM_NUM; ++ubTempcoTrimIndex) {
		FlaGetMLBi(CH0, CE0, gTempcoMeta.uwTrimAddr[ubTempcoTrimIndex], &ubReadBackValue);
		for (ubTempcoLevelTableIndex = 0; ubTempcoLevelTableIndex < TEMPCO_EC_LEVEL_NUM; ubTempcoLevelTableIndex++) {
			gpTempcoMeta->ubTrimValue[ubTempcoLevelTableIndex] [ubTempcoTrimIndex] += ubReadBackValue;
		}
	}
}
#endif /* VRLC_IN_MMO */
