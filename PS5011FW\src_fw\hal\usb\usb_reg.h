#ifndef _USB_REG_H_
#define _USB_REG_H_

#if (USB == HOST_MODE)
// mem
#include "common/mem.h"

/*Refer to PS5017_RegSpec_USB.doc*/

// base address : 0xF800_0000
#define R32_USB							((volatile U32 *) USB3_REG_ADDRESS)
// USB PHY base address : 0xF801_5000, USB use R32_COMBOPHY

//------------------------------
//USB - ucmu_glb_reg
//------------------------------
#define	R32_USB_GLB_CFG_0			(0x00000000 >> 2)
#define		EN_INTF0					(0)
#define		EN_INTF0_INTF1				(1)
#define		EN_INTF0_INTF2				(2)
#define		EN_INTF0_INTF1_INTF2		(3)
#define		CR_INTF_MSK					(BIT_MASK(2))	// BIT [1:0]
//			[3:2] reserved
#define		EN_INTF0_ATTR1				(1)
#define		EN_INTF1_ATTR1				(2)
#define		EN_INTF2_ATTR1				(4)
#define		CR_INTF_ALT_SFT				(4)
#define		CR_INTF_ALT_MSK				(BIT_MASK(3))	// BIT [6:4]
//			[7] reserved
#define		CR_AHB_RESP_ERR				(BIT8)
//			[15:9] reserved
#define		CR_FASTSIM					(BIT16)
//			[31:17] reserved

#define	R32_USB_GLB_CFG_1			(0x00000004 >> 2)
#define		CR_EP1_IFTP					(BIT0)
#define		CR_EP2_IFTP					(BIT1)
#define		CR_EP3_IFTP					(BIT2)
#define		CR_EP4_IFTP					(BIT3)
#define		CR_EP5_IFTP					(BIT4)
#define		CR_EP6_IFTP					(BIT5)
#define		CR_EP7_IFTP					(BIT6)
#define		CR_EP8_IFTP					(BIT7)
#define		CR_EP1_EN					(BIT8)
#define		CR_EP2_EN					(BIT9)
#define		CR_EP3_EN					(BIT10)
#define		CR_EP4_EN					(BIT11)
#define		CR_EP5_EN					(BIT12)
#define		CR_EP6_EN					(BIT13)
#define		CR_EP7_EN					(BIT14)
#define		CR_EP8_EN					(BIT15)
#define		INTERFACE_0					(0)
#define		INTERFACE_1					(1)
#define		INTERFACE_2					(2)
#define		CR_EP1_INTF_SFT				(16)
#define		CR_EP1_INTF_MSK				(BIT_MASK(2))	// BIT [17:16]
#define		CR_EP2_INTF_SFT				(18)
#define		CR_EP2_INTF_MSK				(BIT_MASK(2))	// BIT [19:18]
#define		CR_EP3_INTF_SFT				(20)
#define		CR_EP3_INTF_MSK				(BIT_MASK(2))	// BIT [21:20]
#define		CR_EP4_INTF_SFT				(22)
#define		CR_EP4_INTF_MSK				(BIT_MASK(2))	// BIT [23:22]
#define		CR_EP5_INTF_SFT				(24)
#define		CR_EP5_INTF_MSK				(BIT_MASK(2))	// BIT [25:24]
#define		CR_EP6_INTF_SFT				(26)
#define		CR_EP6_INTF_MSK				(BIT_MASK(2))	// BIT [27:26]
#define		CR_EP7_INTF_SFT				(28)
#define		CR_EP7_INTF_MSK				(BIT_MASK(2))	// BIT [29:28]
#define		CR_EP8_INTF_SFT				(30)
#define		CR_EP8_INTF_MSK				(BIT_MASK(2))	// BIT [31:30]

#define	R32_USB_GLB_CFG_2			(0x00000008 >> 2)
#define		CR_BOT_TX_EP_MSK			(BIT_MASK(4,0))	// BIT [3:0]
#define		CR_BOT_RX_EP_SFT			(4)
#define		CR_BOT_RX_EP_MSK			(BIT_MASK(4))	// BIT [7:4]
#define		CR_UASP_TX_EP_SFT			(8)
#define		CR_UASP_TX_EP_MSK			(BIT_MASK(4))	// BIT [11:8]
#define		CR_UASP_RX_EP_SFT			(12)
#define		CR_UASP_RX_EP_MSK			(BIT_MASK(4))	// BIT [15:12]
#define		CR_UASP_CMD_EP_SFT			(16)
#define		CR_UASP_CMD_EP_MSK			(BIT_MASK(4))	// BIT [19:16]
#define		CR_UASP_ST_EP_SFT			(20)
#define		CR_UASP_ST_EP_MSK			(BIT_MASK(4))	// BIT [23:20]
#define		CR_INTF1_TX_EP_SFT			(24)
#define		CR_INTF1_TX_EP_MSK			(BIT_MASK(4))	// BIT [27:24]
#define		CR_INTF1_RX_EP_SFT			(28)
#define		CR_INTF1_RX_EP_MSK			(BIT_MASK(4))	// BIT [31:28]

#define	R32_USB_GLB_CFG_3			(0x0000000C >> 2)
#define		CR_INTF2_TX_EP_MSK			(BIT_MASK(4))	// BIT [3:0]
#define		CR_INTF2_RX_EP_SFT			(4)
#define		CR_INTF2_RX_EP_MSK			(BIT_MASK(4))	// BIT [7:4]
#define		CR_UASP_IN_INTF0			(0)
#define		CR_UASP_IN_INTF1			(1)
#define		CR_UASP_IN_INTF2			(2)
//			[15:8] reserved
#define		CR_UASP_INTF_SFT			(16)
#define		CR_UASP_INTF_MSK			(BIT_MASK(2))	// BIT [17:16]
#define		CR_APU_NRDY					(BIT18)
//			[31:19] reserved

#define	R32_USB_GLB_CPSR			(0x00000010 >> 2)
#define		CR_OP00_ARSP_EN				(BIT0)			// default 1
#define		CR_OP03_ARSP_EN				(BIT1)			// default 0
#define		CR_OP1E_ARSP_EN				(BIT2)			// default 1
#define		CR_OP35_ARSP_EN				(BIT3)			// default 1
#define		CR_OP91_ARSP_EN				(BIT4)			// default 1
#define		CR_OP1A_ARSP_EN				(BIT5)			// default 1
#define		CR_OP5A_ARSP_EN				(BIT6)			// default 1
//			[7] reserved
#define		CR_CMD_GRP0_ATTR			(BIT8)			// default 0
#define		CR_CMD_GRP1_ATTR			(BIT9)			// default 1
#define		CR_CMD_GRP2_ATTR			(BIT10)			// default 0
//			[12:11] reserved
#define		CR_RTN_MODE_PAGE			(BIT13)			// default 1
#define		CR_SCSI_NO_MEDIA			(BIT14)			// default 0
#define		CR_AUTO_CPL_DIS				(BIT15)			// default 0
#define		CR_IO_ATTR2SYNC				(BIT16)			// default 0
#define		CR_EP0_ATTR2SYNC			(BIT17)			// default 1
//			[18] reserved
#define		CR_INTF1_ATTR2SYNC			(BIT19)			// default 0
#define		CR_INTF2_ATTR2SYNC			(BIT20)			// default 0
#define		CR_INTF_CPSR_EN				(BIT21)			// default 1
#define		CR_FW_IO_FUA				(BIT22)			// default 0
//			[23] reserved
#define		CR_MAX_LUN_NUM_SFT			(24)
#define		CR_MAX_LUN_NUM_MSK			(BIT_MASK(4))	// BIT [27:24]
#define		CR_SCSI_TRAN_LEN_8B			(0x00)
#define		CR_SCSI_TRAN_LEN_16B		(0x01)
#define		CR_SCSI_TRAN_LEN_32B		(0x02)
#define		CR_SCSI_TRAN_LEN_64B		(0x03)
#define		CR_SCSI_TRAN_LEN_128B		(0x04)
#define		CR_SCSI_TRAN_LEN_256B		(0x05)
#define		CR_SCSI_TRAN_LEN_512B		(0x06)
#define		CR_SCSI_TRAN_LEN_1KB		(0x07)
#define		CR_SCSI_TRAN_LEN_2KB		(0x08)
#define		CR_SCSI_TRAN_LEN_4KB		(0x09)
#define		CR_SCSI_TRAN_LEN_8KB		(0x0A)
#define		CR_SCSI_TRAN_LEN_16KB		(0x0B)
#define		CR_SCSI_TRAN_LEN_32KB		(0x0C)
#define		CR_SCSI_TRAN_LEN_64KB		(0x0D)
#define		CR_SCSI_TRAN_LEN_U_SFT		(28)
#define		CR_SCSI_TRAN_LEN_U_MSK		(BIT_MASK(4))	// BIT [31:28]

#define	R32_USB_GLB_RST				(0x00000018 >> 2)
#define		CR_CPSR_RSTN				(BIT0)
#define		CR_CCPL_RSTN				(BIT1)
#define		CR_ARSP_RSTN				(BIT2)
#define		CR_TXDMA_RSTN				(BIT3)
#define		CR_RXDMA_RSTN				(BIT4)
#define		CR_CMDQ_RSTN				(BIT5)
//			[6] reserved
#define		CR_APU_CMD_RSTN				(BIT7)
#define		CR_APU_DAT_RSTN				(BIT8)
//			[31:9] reserved

#define	R32_USB_GLB_INT0_ST_0		(0x00000020 >> 2)
#define		SR_SDPP_RCV_INT0_ST			(BIT0)
#define		SR_INTF0_ALT_INT0_ST		(BIT1)
#define		SR_INTF1_ALT_INT0_ST		(BIT2)
#define		SR_INTF2_ALT_INT0_ST		(BIT3)
#define		SR_LTM_EN_INT0_ST			(BIT4)
#define		SR_U2_EN_INT0_ST			(BIT5)
#define		SR_U1_EN_INT0_ST			(BIT6)
#define		SR_INTF0_SO_INT0_ST			(BIT7)
#define		SR_INTF1_SO_INT0_ST			(BIT8)
#define		SR_INTF2_SO_INT0_ST			(BIT9)
#define		SR_ISOCH_DLY_INT0_ST		(BIT10)
#define		SR_SET_SEL_INT0_ST			(BIT11)
#define		SR_PORT_CFG_INT0_ST			(BIT12)
#define		SR_PORT_CAP_INT0_ST			(BIT13)
#define		SR_VND_TEST_INT0_ST			(BIT14)
#define		SR_SET_U2T_INT0_ST			(BIT15)
#define		SR_SET_LINK_INT0_ST			(BIT16)
#define		SR_IN_U3_INT0_ST			(BIT17)
#define		SR_IN_U2_INT0_ST			(BIT18)
#define		SR_IN_U1_INT0_ST			(BIT19)
#define		SR_IN_U0_INT0_ST			(BIT20)
#define		SR_IN_INA_INT0_ST			(BIT21)
#define		SR_H_RST_INT0_ST			(BIT22)
#define		SR_W_RST_INT0_ST			(BIT23)
#define		SR_EP1_HALT_INT0_ST			(BIT24)
#define		SR_EP2_HALT_INT0_ST			(BIT25)
#define		SR_EP3_HALT_INT0_ST			(BIT26)
#define		SR_EP4_HALT_INT0_ST			(BIT27)
#define		SR_EP5_HALT_INT0_ST			(BIT28)
#define		SR_EP6_HALT_INT0_ST			(BIT29)
#define		SR_EP7_HALT_INT0_ST			(BIT30)
#define		SR_EP8_HALT_INT0_ST			(BIT31)

#define	R32_USB_GLB_INT0_ST_1		(0x00000024 >> 2)
#define		SR_IN_LBCP_INT0_ST			(BIT0)
#define		SR_ERRLMT_HSLNK_INT0_ST		(BIT1)
#define		SR_ERRLMT_SSLNK_INT0_ST		(BIT2)
#define		SR_ERRLMT_LSYM_INT0_ST		(BIT3)
#define		SR_ERRLMT_8B10B_INT0_ST		(BIT4)
#define		SR_SS_LLU_ERR_INT0_ST		(BIT5)
//			[15:6] reserved
#define		SR_PHY_SLP_INT0_ST			(BIT16)
#define		SR_PHY_SUS_INT0_ST			(BIT17)
#define		SR_PHY_WK_INT0_ST			(BIT18)
#define		SR_PMU_BLK_INT0_ST			(BIT19)
#define		SR_SPD_ENUM_INT0_ST			(BIT20)
#define		SR_CPSR_FAIL_INT0_ST		(BIT21)
#define		SR_RXDMA_FAIL_INT0_ST		(BIT22)
#define		SR_TXDMA_FAIL_INT0_ST		(BIT23)
#define		SR_TXRAM_ERR_INT0_ST		(BIT24)
#define		SR_RXRAM_ERR_INT0_ST		(BIT25)
#define		SR_CMDQ_RR_INT0_ST			(BIT26)
//			[31:27] reserved

#define	R32_USB_GLB_INT0_EN_0		(0x00000028 >> 2)
#define		CR_SDPP_RCV_INT0_EN			(BIT0)
#define		CR_INTF0_ALT_INT0_EN		(BIT1)
#define		CR_INTF1_ALT_INT0_EN		(BIT2)
#define		CR_INTF2_ALT_INT0_EN		(BIT3)
#define		CR_LTM_EN_INT0_EN			(BIT4)
#define		CR_U2_EN_INT0_EN			(BIT5)
#define		CR_U1_EN_INT0_EN			(BIT6)
#define		CR_INTF0_SO_INT0_EN			(BIT7)
#define		CR_INTF1_SO_INT0_EN			(BIT8)
#define		CR_INTF2_SO_INT0_EN			(BIT9)
#define		CR_ISOCH_DLY_INT0_EN		(BIT10)
#define		CR_SET_SEL_INT0_EN			(BIT11)
#define		CR_PORT_CFG_INT0_EN			(BIT12)
#define		CR_PORT_CAP_INT0_EN			(BIT13)
#define		CR_VND_TEST_INT0_EN			(BIT14)
#define		CR_SET_U2T_INT0_EN			(BIT15)
#define		CR_SET_LINK_INT0_EN			(BIT16)
#define		CR_IN_U3_INT0_EN			(BIT17)
#define		CR_IN_U2_INT0_EN			(BIT18)
#define		CR_IN_U1_INT0_EN			(BIT19)
#define		CR_IN_U0_INT0_EN			(BIT20)
#define		CR_IN_INA_INT0_EN			(BIT21)
#define		CR_H_RST_INT0_EN			(BIT22)
#define		CR_W_RST_INT0_EN			(BIT23)
#define		CR_EP1_HALT_INT0_EN			(BIT24)
#define		CR_EP2_HALT_INT0_EN			(BIT25)
#define		CR_EP3_HALT_INT0_EN			(BIT26)
#define		CR_EP4_HALT_INT0_EN			(BIT27)
#define		CR_EP5_HALT_INT0_EN			(BIT28)
#define		CR_EP6_HALT_INT0_EN			(BIT29)
#define		CR_EP7_HALT_INT0_EN			(BIT30)
#define		CR_EP8_HALT_INT0_EN			(BIT31)

#define	R32_USB_GLB_INT0_EN_1		(0x0000002C >> 2)
#define		CR_IN_LBCP_INT0_EN			(BIT0)
#define		CR_ERRLMT_HSLNK_INT0_EN		(BIT1)
#define		CR_ERRLMT_SSLNK_INT0_EN		(BIT2)
#define		CR_ERRLMT_LSYM_INT0_EN		(BIT3)
#define		CR_ERRLMT_8B10B_INT0_EN		(BIT4)
#define		SR_SS_LLU_ERR_INT0_EN		(BIT5)

//			[15:6] reserved
#define		CR_PHY_SLP_INT0_EN			(BIT16)
#define		CR_PHY_SUS_INT0_EN			(BIT17)
#define		CR_PHY_WK_INT0_EN			(BIT18)
#define		CR_PMU_BLK_INT0_EN			(BIT19)
#define		CR_SPD_ENUM_INT0_EN			(BIT20)
#define		CR_CPSR_FAIL_INT0_EN		(BIT21)
#define		CR_RXDMA_FAIL_INT0_EN		(BIT22)
#define		CR_TXDMA_FAIL_INT0_EN		(BIT23)
#define		CR_TXRAM_ERR_INT0_EN		(BIT24)
#define		CR_RXRAM_ERR_INT0_EN		(BIT25)
#define		CR_CMDQ_RR_INT0_EN			(BIT26)
#define		CR_PARERR_INT_EN			(BIT27)
#define		CR_SZERR_INT_EN				(BIT28)
//			[31:29] reserved

#define	R32_USB_GLB_INT0_DETEN_0	(0x00000030 >> 2)
#define		CR_SDPP_RCV_INT0_DETEN		(BIT0)
#define		CR_INTF0_ALT_INT0_DETEN		(BIT1)
#define		CR_INTF1_ALT_INT0_DETEN		(BIT2)
#define		CR_INTF2_ALT_INT0_DETEN		(BIT3)
#define		CR_LTM_EN_INT0_DETEN		(BIT4)
#define		CR_U2_EN_INT0_DETEN			(BIT5)
#define		CR_U1_EN_INT0_DETEN			(BIT6)
#define		CR_INTF0_SO_INT0_DETEN		(BIT7)
#define		CR_INTF1_SO_INT0_DETEN		(BIT8)
#define		CR_INTF2_SO_INT0_DETEN		(BIT9)
#define		CR_ISOCH_DLY_INT0_DETEN		(BIT10)
#define		CR_SET_SEL_INT0_DETEN		(BIT11)
#define		CR_PORT_CFG_INT0_DETEN		(BIT12)
#define		CR_PORT_CAP_INT0_DETEN		(BIT13)
#define		CR_VND_TEST_INT0_DETEN		(BIT14)
#define		CR_SET_U2T_INT0_DETEN		(BIT15)
#define		CR_SET_LINK_INT0_DETEN		(BIT16)
#define		CR_IN_U3_INT0_DETEN			(BIT17)
#define		CR_IN_U2_INT0_DETEN			(BIT18)
#define		CR_IN_U1_INT0_DETEN			(BIT19)
#define		CR_IN_U0_INT0_DETEN			(BIT20)
#define		CR_IN_INA_INT0_DETEN		(BIT21)
#define		CR_H_RST_INT0_DETEN			(BIT22)
#define		CR_W_RST_INT0_DETEN			(BIT23)
#define		CR_EP1_HALT_INT0_DETEN		(BIT24)
#define		CR_EP2_HALT_INT0_DETEN		(BIT25)
#define		CR_EP3_HALT_INT0_DETEN		(BIT26)
#define		CR_EP4_HALT_INT0_DETEN		(BIT27)
#define		CR_EP5_HALT_INT0_DETEN		(BIT28)
#define		CR_EP6_HALT_INT0_DETEN		(BIT29)
#define		CR_EP7_HALT_INT0_DETEN		(BIT30)
#define		CR_EP8_HALT_INT0_DETEN		(BIT31)

#define	R32_USB_GLB_INT0_DETEN_1	(0x00000034 >> 2)
#define		CR_IN_LBCP_INT0_DETEN		(BIT0)
#define		CR_ERRLMT_HSLNK_INT0_DETEN	(BIT1)
#define		CR_ERRLMT_SSLNK_INT0_DETEN	(BIT2)
#define		CR_ERRLMT_LSYM_INT0_DETEN	(BIT3)
#define		CR_ERRLMT_8B10B_INT0_DETEN	(BIT4)
#define		SR_SS_LLU_ERR_INT0_DETEN	(BIT5)

//			[15:6] reserved
#define		CR_PHY_SLP_INT0_DETEN		(BIT16)
#define		CR_PHY_SUS_INT0_DETEN		(BIT17)
#define		CR_PHY_WK_INT0_DETEN		(BIT18)
#define		CR_PMU_BLK_INT0_DETEN		(BIT19)
#define		CR_SPD_ENUM_INT0_DETEN		(BIT20)
#define		CR_CPSR_FAIL_INT0_DETEN		(BIT21)
#define		CR_RXDMA_FAIL_INT0_DETEN	(BIT22)
#define		CR_TXDMA_FAIL_INT0_DETEN	(BIT23)
#define		CR_TXRAM_ERR_INT0_DETEN		(BIT24)
#define		CR_RXRAM_ERR_INT0_DETEN		(BIT25)
#define		CR_CMDQ_RR_INT0_DETEN		(BIT26)
//			[31:27] reserved

#define	R32_USB_GLB_INT1_ST			(0x00000040 >> 2)
#define		SR_CPSR_INT1_INF1_ST		(BIT0)
#define		SR_CPSR_INT1_INF2_ST		(BIT1)
//			[2] reserved
#define		SR_CMDQ_RR_INT1_ST			(BIT3)
//			[31:4] reserved

#define	R32_USB_GLB_INT1_EN			(0x00000044 >> 2)
#define		CR_CPSR_INT1_INF1_EN		(BIT0)
#define		CR_CPSR_INT1_INF2_EN		(BIT1)
//			[2] reserved
#define		CR_CMDQ_RR_INT1_EN			(BIT3)
//			[31:4] reserved

#define	R32_USB_GLB_INT1_DETEN		(0x00000048 >> 2)
#define		CR_CPSR_INT1_INF1_DETEN		(BIT0)
#define		CR_CPSR_INT1_INF2_DETEN		(BIT1)
//			[2] reserved
#define		CR_CMDQ_RR_INT1_DETEN		(BIT3)
//			[31:4] reserved

#define	R32_USB_GLB_TLU_ACT			(0x0000004C >> 2)
#define		SR_AHB_ACT					(BIT0)
#define		SR_TLU_ACT					(BIT1)
#define		SR_APU_ACT					(BIT2)
#define		SR_CMDQ_ACT					(BIT3)
//			[31:4] reserved

#define	R32_USB_GLB_CMDQ_CFG_0		(0x00000050 >> 2)
#define		UASP_CMD_SIZE_64B			(0)
#define		UASP_CMD_SIZE_128B			(1)
#define		UASP_CMD_SIZE_1KB			(2)
#define		CMD_SIZE_MSK				(BIT_MASK(2))	// BIT[1:0]
#define		CTAG_CMDQ_16_3				(0)
#define		CTAG_CMDQ_32_3				(1)
#define		CTAG_CMDQ_64_3				(2)
#define		CTAG_CMDQ_128_3				(3)
#define		CTAG_CMDQ_SFT				(2)
#define		CTAG_CMDQ_MSK				(BIT_MASK(2))	// BIT[3:2]

#define	R32_USB_GLB_CPL_0			(0x00000054 >> 2)
#define		CR_CPL_POSTPONE				(BIT0)
//			[31:1] reserved

#define	R32_USB_GLB_CPSR_0			(0x00000058 >> 2)
#define		CR_AUTO_LOCK_EN				(BIT0)
#define		CR_CMD_RSV_CHK_EN			(BIT1)
//			[16:2] reserved
#define		CR_SCSI_MEDIA_ALT			(BIT17)
#define		CR_SCSI_PRVT_STS			(BIT18)
#define		CR_SCSI_PRVT_CLR			(BIT19)
#define		SR_SCSI_PRVT				(BIT20)
//			[31:21] reserved

#define	R32_USB_GLB_LCA_LUN0		(0x0000005C >> 2)

#define	R32_USB_GLB_LCA_LUN1		(0x00000060 >> 2)

#define	R32_USB_GLB_LCA_LUN2		(0x00000064 >> 2)
//------------------------------
//USB - ucmu_pmu_reg
//------------------------------
#define	R32_USB_PMU_SPD_0			(0x00000100 >> 2)
#define		HIGH_SPEED					(0x00)
#define		FULL_SPEED					(0x01)
#define		LOW_SPEED					(0x02)
#define		SUPER_SPEED					(0x04)
#define		SUPER_PLS_SPEED				(0x0C)
#define		CR_EXP_SPD_MSK				(BIT_MASK(4))	// BIT [3:0]
//			[4] reserved
#define		CR_EXP_LANE					(BIT5)			// default: 1
#define		CR_LANE_SEL					(BIT6)
#define		CR_CONNECT					(BIT7)
#define		SR_ENUM_SPD_SFT				(8)
#define		SR_ENUM_SPD_MSK				(BIT_MASK(4))	// BIT [11:8]
//			[12] reserved
#define		SR_ENUM_LANE				(BIT13)
//			[15:14] reserved
#define		SR_SS_DET_PASS				(BIT16)
#define		SR_SS_POLL_PASS				(BIT17)
#define		SR_SS_DET_FAIL				(BIT18)
#define		SR_SS_POLL_FAIL				(BIT19)
#define		CR_PLL_EN					(BIT24)
#define		SR_PHY_PLL_LOCK				(BIT25)
#define		SR_VBUS						(BIT26)
//			[31:27] reserved

#define	R32_USB_PMU_SPD_1			(0x00000104 >> 2)
#define		CR_SS_LANE_DET_EN			(BIT0)
#define		CR_SS_TXDET_DIS				(BIT1)
#define		CR_SS_WRST_PRST_EN			(BIT2)
#define		CR_LPM_EN					(BIT3)
#define		CR_PRST_WKARND_EN			(BIT4)
#define		CR_CLK_GATED_EN				(BIT5)
#define		CR_RCOSC_WKARND_EN			(BIT6)
//			[7] reserved
#define		CR_SW_BLK_EN				(BIT8)
#define		CR_WRST_BLK_EN				(BIT9)
//			[15:10] reserved
#define		CR_SS_DIS_CNT_LMT_SFT		(16)
#define		CR_SS_DIS_CNT_LMT_MSK		(BIT_MASK(4))	// BIT [19:16]
#define		SR_SS_DIS_CNT_SFT			(20)
#define		SR_SS_DIS_CNT_MSK			(BIT_MASK(4))	// BIT [23:20]
#define		LFPS_TIMEOUT_16US			(0x00)
#define		LFPS_TIMEOUT_50US			(0x01)
#define		LFPS_TIMEOUT_300US			(0x02)
#define		LFPS_TIMEOUT_750US			(0x03)
#define		LFPS_TIMEOUT_1MS			(0x04)
#define		LFPS_TIMEOUT_2MS			(0x05)
#define		CR_SS_LANE_DET_TMR_LMT_SFT	(24)
#define		CR_SS_LANE_DET_TMR_LMT_MSK	(BIT_MASK(3))	// BIT [26:24]
//			[31:27] reserved

#define	R32_USB_PMU_ST_0			(0x00000110 >> 2)
#define		LOCK_SUB_MSK				(BIT_MASK(2))	// BIT [1:0]
#define		CONN_SUB_SFT				(2)
#define		CONN_SUB_MSK				(BIT_MASK(3))	// BIT [4:2]
#define 	DET_SUB_SFT					(5)
#define 	DET_SUB_MSK					(BIT_MASK(3))	// BIT [7:5]
//			[17:8] reserved
#define		SWITCH_SUB_SFT				(18)
#define		SWITCH_SUB_MSK				(BIT_MASK(3))	// BIT [20:18]
#define		WARMRST_SUB_SFT				(21)
#define		WARMRST_SUB_MSK				(BIT_MASK(3))	// BIT [23:21]
#define		PMU_MAIN_SFT				(24)
#define		PMU_MAIN_MSK				(BIT_MASK(4))	// BIT [27:24]
//			[30:28] reserved
#define		CR_PMU_BLK_RLS				(BIT31)

#define	R32_USB_PMU_ST_1			(0x00000114 >> 2)
#define		UNLOCK_SUB_MSK				(BIT_MASK(2))	// BIT [1:0]
#define		UNCONN_SUB_SFT				(2)
#define		UNCONN_SUB_MSK				(BIT_MASK(3))	// BIT [4:2]
#define		UNDET_SUB_SFT				(5)
#define		UNDET_SUB_MSK				(BIT_MASK(3))	// BIT [7:5]
//			[31:8] reserved

#define	R32_USB_PMU_BZ				(0x00000118 >> 2)
#define		SR_USB_ACT					(BIT0)
//			[15:1] reserved
#define		SR_PW_ST_SUS				(BIT16)
#define		SR_PW_ST_PD					(BIT17)
//			[31:18] reserved

#define	R32_USB_PMU_LPM_0			(0x0000011C >> 2)
#define		CR_OP_RC_OFF				(BIT0)
#define		CR_OP_XTAL_OFF				(BIT1)
#define		CR_OP_SNOOZE				(BIT2)
#define		CR_LPM_REJECT				(BIT3)
#define		CR_LPM_FORBID				(BIT4)
#define		CR_CLKG_IN_CMDQ_ACT			(BIT5)
//			[7:6] reserved
#define		CR_PMU_WAKE_T2P_EN			(BIT8)
#define		CR_PMU_WAKE_TLU_EN			(BIT9)
#define		CR_PMU_WAKE_UTMI_EN			(BIT10)
#define		CR_PMU_WAKE_PIPE_EN			(BIT11)
#define		CR_PMU_LTSSM_INV_EN			(BIT12)
#define		CR_PMU_BLK_INT0_WKE_EN		(BIT13)
//			[23:14]	TBD
//			[31:24] reserved

#define	R32_USB_PMU_WK_EVT			(0x00000120 >> 2)
//			[7:0 reserved
#define		CR_PMU_WAKE_T2P_EVT			(BIT8)
#define		CR_PMU_WAKE_TLU_EVT			(BIT9)
#define		CR_PMU_WAKE_UTMI_EVT		(BIT10)
#define		CR_PMU_WAKE_PIPE_EVT		(BIT11)
#define		CR_PMU_LTSSM_INV_EVT		(BIT12)
#define		CR_PMU_BLK_INT0_EVT			(BIT13)
//			[23:14]	TBD
//			[31:24] reserved

#define R32_USB_PD50_USB_RSV		(0x00000124 >> 2)
//------------------------------
//USB - ucmu_tlu_reg
//------------------------------
#define	R32_USB_TLU_ERR_CTRL		(0x00000200 >> 2)
#define		CR_CMD_LOCK_CLR				(BIT0)
#define		CR_RXDMA_PARERR_INJ			(BIT1)
#define		CR_TXDMA_PARERR_INJ			(BIT2)
#define		CR_RXDMA_PARERR_2APU_INJ	(BIT3)

//			[15:5] reserved
#define		CR_CPSR_STOP				(BIT16)
#define		CR_ARSP_STOP				(BIT17)
#define		CR_CCPL_STOP				(BIT18)
#define		CR_TXDMA_STOP				(BIT19)
#define		CR_RXDMA_STOP				(BIT20)
//			[31:21] reserved

#define	R32_USB_TLU_ERR_ST			(0x00000204 >> 2)
#define		SR_CMD_LOCK					(BIT0)
#define		SR_TASK_SET_FULL			(BIT1)
#define		SR_TAG_OVER					(BIT2)
#define		SR_BOT_IVLD_ERR				(BIT3)
#define		SR_UAS_IVLD_ERR				(BIT4)
#define		SR_CMD_SIZE_ERR				(BIT5)
#define		SR_BOT_SIG_ERR				(BIT6)
#define		SR_UAS_SID_ERR				(BIT7)
#define		SR_UAS_IUID_ERR				(BIT8)
#define		SR_CPSR_BUSY				(BIT9)			// USB_WORKAROUND_NUMP_EN
#define		SR_ARSP_BUSY				(BIT10)
#define		SR_CCPL_BUSY				(BIT11)
#define		SR_TXDMA_BUSY				(BIT12)
#define		SR_RXDMA_BUSY				(BIT13)
//			[15:14] reserved
#define		SR_TAG_OVER_NUM_SFT			(16)
#define		SR_TAG_OVER_NUM_MSK			(BIT_MASK(8))	// BIT [23:16]
#define		SR_CMDQ_CCPL_RR_FAIL		(BIT24)
#define		SR_CMDQ_ARSP_RR_FAIL		(BIT25)
#define		SR_CMDQ_APUD_RR_FAIL		(BIT26)
#define		SR_CMDQ_APUC_RR_FAIL		(BIT27)
#define		SR_CMDQ_CMU_RR_FAIL			(BIT28)
//			[31:29] reserved

#define	R32_USB_TLU_ST_0			(0x00000208 >> 2)
#define		SR_CSW_STATUS_MSK			(BIT_MASK(2))	// BIT [1:0]
#define		SR_INTF1_VLD				(BIT2)
#define		SR_INTF2_VLD				(BIT3)
#define		SR_CMD_POP					(BIT4)
#define		SR_CMD_POP_ERR				(BIT5)
#define		SR_CPSR_ACT					(BIT6)
//			[11:7] reserved
#define		SR_CPSR_ST_SFT				(12)
#define		SR_CPSR_ST_MSK				(BIT_MASK(5))	// BIT [16:12]
#define		SR_CCPL_ST_SFT				(17)
#define		SR_CCPL_ST_MSK				(BIT_MASK(7))	// BIT [23:17]
#define		SR_ARSP_ST_SFT				(24)
#define		SR_ARSP_ST_MSK				(BIT_MASK(8))	// BIT [31:24]

#define	R32_USB_TLU_ST_1			(0x0000020C >> 2)

#define	R32_USB_TLU_FLUSH			(0x00000210 >> 2)
#define		SR_UAPU_INS_ST_MSK			(BIT_MASK(12))	// BIT [11:0]
#define		SR_UAPU_TD_ST_SFT			(12)
#define		SR_UAPU_TD_ST_MSK			(BIT_MASK(6))	// BIT [17:12]
#define		SR_UAPU_CPL_ST_SFT			(18)
#define		SR_UAPU_CPL_ST_MSK			(BIT_MASK(6))	// BIT [23:18]
#define		SR_UAPU_DEL_ST_SFT			(24)
#define		SR_UAPU_DEL_ST_MSK			(BIT_MASK(6))	// BIT [29:24]
#define		CR_FLUSH_ALL_CMD			(BIT30)
#define		CR_FLUSH_ALL_TASK			(BIT31)

#define	R32_USB_TLU_CMDQ_CFG		(0x00000214 >> 2)
#define		CR_OCP_SET					(BIT0)
#define		CR_ARSP_SET					(BIT1)
#define		CR_ACPL_SET					(BIT2)
#define		CR_TD_SET					(BIT3)
#define		CR_CPL_SET					(BIT4)
//			[7:5] reserved
#define		CR_OCP_CLR					(BIT8)
#define		CR_ARSP_CLR					(BIT9)
#define		CR_ACPL_CLR					(BIT10)
#define		CR_TD_CLR					(BIT11)
#define		CR_CPL_CLR					(BIT12)
//			[15:13] reserved
#define		CR_CMDQ_CTAG_SFT			(16)
#define		CR_CMDQ_CTAG_MSK			(BIT_MASK(8))	// BIT [23:16]
#define		CR_OCP_TYPE					(BIT24)
//			[31:25] reserved

#define	R32_USB_TLU_CPSR_0			(0x00000218 >> 2)

#define	R32_USB_TLU_CPSR_1			(0x0000021C >> 2)
#define		CR_CPSR_CMD_INC				(BIT0)
#define		CR_CPSR_CMD_CLR				(BIT1)
//			[7:2] reserved
#define		SR_UASP_EN					(BIT8)
//			[31:9] reserved

#define	R32_USB_TLU_CPSR_3			(0x00000220 >> 2)
#define		CR_LUN0_SNSKEY_MSK			(BIT_MASK(4))	// BIT [3:0]
//			[7:4] reserved
#define		CR_LUN0_ASC_SFT				(8)
#define		CR_LUN0_ASC_MSK				(BIT_MASK(8))	// BIT [15:8]
#define		CR_LUN0_ASCQ_SFT			(16)
#define		CR_LUN0_ASCQ_MSK			(BIT_MASK(8))	// BIT [23:16]
//			[31:24] reserved

#define	R32_USB_TLU_CPSR_4			(0x00000224 >> 2)
#define		CR_LUN1_SNSKEY_MSK			(BIT_MASK(4))	// BIT [3:0]
//			[7:4] reserved
#define		CR_LUN1_ASC_SFT				(8)
#define		CR_LUN1_ASC_MSK				(BIT_MASK(8))	// BIT [15:8]
#define		CR_LUN1_ASCQ_SFT			(16)
#define		CR_LUN1_ASCQ_MSK			(BIT_MASK(8))	// BIT [23:16]
//			[31:24] reserved

#define	R32_USB_TLU_CPSR_5			(0x00000228 >> 2)
#define		CR_LUN2_SNSKEY_MSK			(BIT_MASK(4))	// BIT [3:0]
//			[7:4] reserved
#define		CR_LUN2_ASC_SFT				(8)
#define		CR_LUN2_ASC_MSK				(BIT_MASK(8))	// BIT [15:8]
#define		CR_LUN2_ASCQ_SFT			(16)
#define		CR_LUN2_ASCQ_MSK			(BIT_MASK(8))	// BIT [23:16]
//			[31:24] reserved

#define	R32_USB_TLU_DMA				(0x0000022C >> 2)
#define		CR_RXDMA_AZ_EN				(BIT0)
#define		CR_TXDMA_AZ_EN				(BIT1)
#define		CR_DUMMY_EN					(BIT2)
#define		CR_DUMMY_ALL				(BIT3)
#define		CR_DUMMY_EN_LUN				(BIT4)
//			[7:5] reserved
#define		CR_DUMMY_CTAG_SFT			(8)
#define		CR_DUMMY_CTAG_MSK			(BIT_MASK(8))	// BIT [15:8]
#define		CR_DUMMY_LUN_SFT			(16)
#define		CR_DUMMY_LUN_MSK			(BIT_MASK(4))	// BIT [19:16]
//			[31:20] reserved

#define	R32_USB_TLU_DMA_ST			(0x00000230 >> 2)
#define		SR_TXDMA_INT_CTAG_MSK		(BIT_MASK(4))	// BIT [3:0]
#define		SR_RXDMA_INT_CTAG_SFT		(8)
#define		SR_RXDMA_INT_CTAG_MSK		(BIT_MASK(8))	// BIT [15:8]
//			[31:16] reserved

#define	R32_USB_TLU_STALL			(0x00000234 >> 2)
#define 	CR_CMU_PTR_CLRREQ1			(BIT0)
#define 	CR_CMU_PTR_CLRREQ2			(BIT1)
#define 	CR_CMU_PTR_CLRREQ3			(BIT2)
#define 	CR_CMU_PTR_CLRREQ4			(BIT3)
#define 	CR_CMU_PTR_CLRREQ5			(BIT4)
#define 	CR_CMU_PTR_CLRREQ6			(BIT5)
#define 	CR_CMU_PTR_CLRREQ7			(BIT6)
#define 	CR_CMU_PTR_CLRREQ8			(BIT7)
#define		CR_EP2_STALL_1				(BIT8)
#define		CR_EP4_STALL_1				(BIT9)
#define		CR_EP6_STALL_1				(BIT10)
#define		CR_EP8_STALL_1				(BIT11)
//			[31:12] reserved

#define	R32_USB_TLU_PKT_0			(0x00000238 >> 2)
#define		CR_PKT_EP_MSK				(BIT_MASK(4))	// BIT [3:0]
#define		CR_PKT_SID_SFT				(4)
#define		CR_PKT_SID_MSK				(BIT_MASK(8))	// BIT [11:4]
#define		CR_PKT_PKTSZ_SFT			(12)
#define		CR_PKT_PKTSZ_MSK			(BIT_MASK(11))	// BIT [22:12]
#define		CR_PKT_AZ					(BIT23)
#define		CR_PKT_STALL				(BIT24)
#define		CR_PKT_LST					(BIT25)
//			[30:26] reserved
#define		CR_PKT_WEN					(BIT31)

#define	R32_USB_TLU_PKT_1			(0x0000023C >> 2)
#define		CR_PKT_PTR_MSK				(BIT_MASK(4))	// BIT [3:0]
#define		SR_PKT_PKTSZ_SFT			(4)
#define		SR_PKT_PKTSZ_MSK			(BIT_MASK(11))	// BIT [14:4]
#define		SR_PKT_LST					(BIT15)
#define		SR_PKT_SID_SFT				(16)
#define		SR_PKT_SID_MSK				(BIT_MASK(8))	// BIT [23:16]
#define		SR_PKT_AZ					(BIT24)
//			[31:25] reserved

#define	R32_USB_TLU_EP0TX_0			(0x00000240 >> 2)
#define		CR_EP0TX_WPTR_INC			(BIT0)
//			[2:1] reserved
#define		CR_EP0TX_SRL_ADR_CLR		(BIT3)
//			[4] reserved
#define		SR_EP0TX_EMPTY				(BIT5)
#define		SR_EP0TX_FULL				(BIT6)
#define		SR_EP0TX_VALID				(BIT7)
//			[12:8] reserved
#define		SR_EP0TX_WPTR_SFT			(13)
#define		SR_EP0TX_WPTR_MSK			(BIT_MASK(5))	// BIT [17:13]
#define		SR_EP0TX_RPTR_SFT			(18)
#define		SR_EP0TX_RPTR_MSK			(BIT_MASK(5))	// BIT [22:18]
//			[23] reserved
#define		SR_EP0TX_SRL_ADR_SFT		(24)
#define		SR_EP0TX_SRL_ADR_MSK		(BIT_MASK(8))	// BIT [31:24]

#define	R32_USB_TLU_EP0TX_1			(0x00000244 >> 2)

#define	R32_USB_TLU_EP0RX_0			(0x00000248 >> 2)
#define		CR_EP0RX_WPTR_INC			(BIT0)
//			[1] reserved
#define		SR_EP0RX_WPTR_IVLD			(BIT2)
#define		CR_EP0RX_SRL_ADR_CLR		(BIT3)
//			[4] reserved
#define		SR_EP0RX_EMPTY				(BIT5)
#define		SR_EP0RX_FULL				(BIT6)
#define		SR_EP0RX_VALID				(BIT7)
//			[12:8] reserved
#define		SR_EP0RX_WPTR_SFT			(13)
#define		SR_EP0RX_WPTR_MSK			(BIT_MASK(5))	// BIT [17:13]
#define		SR_EP0RX_RPTR_SFT			(18)
#define		SR_EP0RX_RPTR_MSK			(BIT_MASK(5))	// BIT [22:18]
//			[23] reserved
#define		SR_EP0RX_SRL_ADR_SFT		(24)
#define		SR_EP0RX_SRL_ADR_MSK		(BIT_MASK(8))	// BIT [31:24]

#define	R32_USB_TLU_EP0RX_1			(0x0000024C >> 2)

#define	R32_USB_TLU_EP1_0			(0x00000250 >> 2)
#define		CR_EP1_WPTR_INC				(BIT0)
#define		CR_EP1_RPTR_INC				(BIT1)
#define		CR_EP1_WPTR_IVLD			(BIT2)
#define		CR_EP1_SRL_ADR_CLR			(BIT3)
//			[4] reserved
#define		SR_EP1_EMPTY				(BIT5)
#define		SR_EP1_FULL					(BIT6)
#define		SR_EP1_VALID				(BIT7)
//			[11:8] reserved
#define		SR_EP1_WPTR_SFT				(12)
#define		SR_EP1_WPTR_MSK				(BIT_MASK(4))	// BIT [15:12]
#define		SR_EP1_RPTR_SFT				(16)
#define		SR_EP1_RPTR_MSK				(BIT_MASK(4))	// BIT [19:16]
//			[20] reserved
#define		SR_EP1_WPTR_MSB				(BIT21)
#define		SR_EP1_RPTR_MSB				(BIT22)
//			[23] reserved
#define		SR_EP1_SRL_ADR_SFT			(24)
#define		SR_EP1_SRL_ADR_MSK			(BIT_MASK(8))	// BIT [31:24]


#define	R32_USB_TLU_EP1_1			(0x00000254 >> 2)

#define	R32_USB_TLU_EP2_0			(0x00000258 >> 2)
#define		CR_EP2_RPTR_INC				(BIT0)
#define		CR_EP2_WVPTR_INC			(BIT1)
#define		CR_EP2_WVPTR_IVLD			(BIT2)
#define		CR_EP2_SRL_ADR_CLR			(BIT3)
#define		SR_EP2_WVFULL				(BIT4)
#define		SR_EP2_EMPTY				(BIT5)
#define		SR_EP2_FULL					(BIT6)
#define		SR_EP2_VAlID				(BIT7)
#define 	SR_EP2_WVPTR_SFT			(8)
#define		SR_EP2_WVPTR_MSK			(BIT_MASK(4))	// BIT [11:8]
#define		SR_EP2_WPTR_SFT				(12)
#define		SR_EP2_WPTR_MSK				(BIT_MASK(4))	// BIT [15:12]
#define		SR_EP2_RPTR_SFT				(16)
#define		SR_EP2_RPTR_MSK				(BIT_MASK(4))	// BIT [19:16]
#define		SR_EP2_WVPTR_MSB			(BIT20)
#define		SR_EP2_WPTR_MSB				(BIT21)
#define		SR_EP2_RPTR_MSB				(BIT22)
//			[23] reserved
#define		SR_EP2_SRL_ADR_SFT			(24)
#define		SR_EP2_SRL_ADR_MSK			(BIT_MASK(8))	// BIT [31:24]

#define	R32_USB_TLU_EP2_1			(0x0000025C >> 2)

#define	R32_USB_TLU_EP3_0			(0x00000260 >> 2)
#define		CR_EP3_WPTR_INC				(BIT0)
#define		CR_EP3_RPTR_INC				(BIT1)
#define		CR_EP3_WPTR_IVLD			(BIT2)
#define		CR_EP3_SRL_ADR_CLR			(BIT3)
//			[4] reserved
#define		SR_EP3_EMPTY				(BIT5)
#define		SR_EP3_FULL					(BIT6)
#define		SR_EP3_VAlID				(BIT7)
//			[11:8] reserved
#define		SR_EP3_WPTR_SFT				(12)
#define		SR_EP3_WPTR_MSK				(BIT_MASK(4))	// BIT [15:12]
#define		SR_EP3_RPTR_SFT				(16)
#define		SR_EP3_RPTR_MSK				(BIT_MASK(4))	// BIT [19:16]
//			[20] reserved
#define		SR_EP3_WPTR_MSB				(BIT21)
#define		SR_EP3_RPTR_MSB				(BIT22)
//			[23] reserved
#define		SR_EP3_SRL_ADR_SFT			(24)
#define		SR_EP3_SRL_ADR_MSK			(BIT_MASK(8))	// BIT [31:24]

#define	R32_USB_TLU_EP3_1			(0x00000264 >> 2)

#define	R32_USB_TLU_EP4_0			(0x00000268 >> 2)
#define		CR_EP4_RPTR_INC				(BIT0)
#define		CR_EP4_WVPTR_INC			(BIT1)
#define		CR_EP4_WVPTR_IVLD			(BIT2)
#define		CR_EP4_SRL_ADR_CLR			(BIT3)
#define		SR_EP4_WVFULL				(BIT4)
#define		SR_EP4_EMPTY				(BIT5)
#define		SR_EP4_FULL					(BIT6)
#define		SR_EP4_VAlID				(BIT7)
#define		SR_EP4_WVPTR_SFT			(8)
#define		SR_EP4_WVPTR_MSK			(BIT_MASK(4))	// BIT [11:8]
#define		SR_EP4_WPTR_SFT				(12)
#define		SR_EP4_WPTR_MSK				(BIT_MASK(4))	// BIT [15:12]
#define		SR_EP4_RPTR_SFT				(16)
#define		SR_EP4_RPTR_MSK				(BIT_MASK(4))	// BIT [19:16]
#define		SR_EP4_WVPTR_MSB			(BIT20)
#define		SR_EP4_WPTR_MSB				(BIT21)
#define		SR_EP4_RPTR_MSB				(BIT22)
//			[23] reserved
#define		SR_EP4_SRL_ADR_SFT			(24)
#define		SR_EP4_SRL_ADR_MSK			(BIT_MASK(8))	// BIT [31:24]

#define	R32_USB_TLU_EP4_1			(0x0000026C >> 2)

#define	R32_USB_TLU_EP5_0			(0x00000270 >> 2)
#define		CR_EP5_WPTR_INC				(BIT0)
#define		CR_EP5_RPTR_INC				(BIT1)
#define		CR_EP5_WPTR_IVLD			(BIT2)
#define		CR_EP5_SRL_ADR_CLR			(BIT3)
//			[4] reserved
#define		SR_EP5_EMPTY				(BIT5)
#define		SR_EP5_FULL					(BIT6)
#define		SR_EP5_VAlID				(BIT7)
//			[11:8] reserved
#define		SR_EP5_WPTR_SFT				(12)
#define		SR_EP5_WPTR_MSK				(BIT_MASK(4))	// BIT [15:12]
#define		SR_EP5_RPTR_SFT				(16)
#define		SR_EP5_RPTR_MSK				(BIT_MASK(4))	// BIT [19:16]
//			[20] reserved
#define		SR_EP5_WPTR_MSB				(BIT21)
#define		SR_EP5_RPTR_MSB				(BIT22)
//			[23] reserved
#define		SR_EP5_SRL_ADR_SFT			(24)
#define		SR_EP5_SRL_ADR_MSK			(BIT_MASK(8))	// BIT [31:24]

#define	R32_USB_TLU_EP5_1			(0x00000274 >> 2)

#define	R32_USB_TLU_EP6_0			(0x00000278 >> 2)
#define		CR_EP6_RPTR_INC				(BIT0)
#define		CR_EP6_WVPTR_INC			(BIT1)
#define		CR_EP6_WVPTR_IVLD			(BIT2)
#define		CR_EP6_SRL_ADR_CLR			(BIT3)
#define		SR_EP6_WVFULL				(BIT4)
#define		SR_EP6_EMPTY				(BIT5)
#define		SR_EP6_FULL					(BIT6)
#define		SR_EP6_VALID				(BIT7)
#define 	SR_EP6_WVPTR_SFT			(8)
#define		SR_EP6_WVPTR_MSK			(BIT_MASK(4))	// BIT [11:8]
#define		SR_EP6_WPTR_SFT				(12)
#define		SR_EP6_WPTR_MSK				(BIT_MASK(4))	// BIT [15:12]
#define		SR_EP6_RPTR_SFT				(16)
#define		SR_EP6_RPTR_MSK				(BIT_MASK(4))	// BIT [19:16]
#define		SR_EP6_WVPTR_MSB			(BIT20)
#define		SR_EP6_WPTR_MSB				(BIT21)
#define		SR_EP6_RPTR_MSB				(BIT22)
//			[23] reserved
#define		SR_EP6_SRL_ADR_SFT			(24)
#define		SR_EP6_SRL_ADR_MSK			(BIT_MASK(8))	// BIT [31:24]

#define	R32_USB_TLU_EP6_1			(0x0000027C >> 2)

#define	R32_USB_TLU_EP7_0			(0x00000280 >> 2)
#define		CR_EP7_WPTR_INC				(BIT0)
#define		CR_EP7_RPTR_INC				(BIT1)
#define		CR_EP7_WPTR_IVLD			(BIT2)
#define		CR_EP7_SRL_ADR_CLR			(BIT3)
//			[4] reserved
#define		SR_EP7_EMPTY				(BIT5)
#define		SR_EP7_FULL					(BIT6)
#define		SR_EP7_VAlID				(BIT7)
//			[11:8] reserved
#define		SR_EP7_WPTR_SFT				(12)
#define		SR_EP7_WPTR_MSK				(BIT_MASK(4))	// BIT [15:12]
#define		SR_EP7_RPTR_SFT				(16)
#define		SR_EP7_RPTR_MSK				(BIT_MASK(4))	// BIT [19:16]
//			[20] reserved
#define		SR_EP7_WPTR_MSB				(BIT21)
#define		SR_EP7_RPTR_MSB				(BIT22)
//			[23] reserved
#define		SR_EP7_SRL_ADR_SFT			(24)
#define		SR_EP7_SRL_ADR_MSK			(BIT_MASK(8))	// BIT [31:24]

#define	R32_USB_TLU_EP7_1			(0x00000284 >> 2)

#define	R32_USB_TLU_EP8_0			(0x00000288 >> 2)
#define		CR_EP8_RPTR_INC				(BIT0)
#define		CR_EP8_WVPTR_INC			(BIT1)
#define		CR_EP8_WVPTR_IVLD			(BIT2)
#define		CR_EP8_SRL_ADR_CLR			(BIT3)
#define		SR_EP8_WVFULL				(BIT4)
#define		SR_EP8_EMPTY				(BIT5)
#define		SR_EP8_FULL					(BIT6)
#define		SR_EP8_VAlID				(BIT7)
#define 	SR_EP8_WVPTR_SFT			(8)
#define		SR_EP8_WVPTR_MSK			(BIT_MASK(4))	// BIT [11:8]
#define		SR_EP8_WPTR_SFT				(12)
#define		SR_EP8_WPTR_MSK				(BIT_MASK(4))	// BIT [15:12]
#define		SR_EP8_RPTR_SFT				(16)
#define		SR_EP8_RPTR_MSK				(BIT_MASK(4))	// BIT [19:16]
#define		SR_EP8_WVPTR_MSB			(BIT20)
#define		SR_EP8_WPTR_MSB				(BIT21)
#define		SR_EP8_RPTR_MSB				(BIT22)
//			[23] reserved
#define		SR_EP8_SRL_ADR_SFT			(24)
#define		SR_EP8_SRL_ADR_MSK			(BIT_MASK(8))	// BIT [31:24]

#define	R32_USB_TLU_EP8_1			(0x0000028C >> 2)

#define	R32_USB_RXRAM_ERR			(0x00000290 >> 2)
//			[15:0] reserved
#define		SR_RXRAM_ERR_ADDR_SFT		(16)
#define		SR_RXRAM_ERR_ADDR_MSK		(BIT_MASK(14))	// BIT [29:16]
#define		SR_RXRAM_ERR				(BIT30)
#define		CR_RXRAM_ERR_CLR			(BIT31)

#define	R32_USB_CMDRAM_ERR_0		(0x00000294 >> 2)
#define		CR_CMDQ_ECC_ERR_INS_MSK		(BIT_MASK(8))	// BIT [7:0]
#define		SR_CMDQ_RR_FAIL_CTAG_SFT	(8)
#define		SR_CMDQ_RR_FAIL_CTAG_MSK	(BIT_MASK(8))	// BIT [15:8]
#define		SR_CMDQ_RR_ADDR_SFT			(16)
#define		SR_CMDQ_RR_ADDR_MSK			(BIT_MASK(9))	// BIT [24:16]
//			[28:25] reserved
#define		CR_CMDQ_RRC_CFG_SFT			(29)
#define		CR_CMDQ_RRC_CFG_MSK			(BIT_MASK(2))	// BIT [30:29]
#define		CR_CMDQ_RR_FAIL_CLR			(BIT31)

#define	R32_USB_CMDRAM_ERR_1		(0x00000298 >> 2)
#define		SR_CMDQ_RR_COR_LOC_MSK		(BIT_MASK(24))	// BIT [23:0]
#define		SR_CMDQ_RR_FAIL_SFT			(24)
#define		SR_CMDQ_RR_FAIL_MSK			(BIT_MASK(4))	// BIT [27:24]
#define		SR_CMDQ_RR_VALID_SFT		(28)
#define		SR_CMDQ_RR_VALID_MSK		(BIT_MASK(4))	// BIT [31:28]

#define	R32_USB_TLU_CMDQ_OCP_0		(0x000002A0 >> 2)

#define	R32_USB_TLU_CMDQ_OCP_1		(0x000002A4 >> 2)

#define	R32_USB_TLU_CMDQ_OCP_2		(0x000002A8 >> 2)

#define	R32_USB_TLU_CMDQ_OCP_3		(0x000002AC >> 2)

#define	R32_USB_TLU_CMDQ_ARSP_0		(0x000002B0 >> 2)

#define	R32_USB_TLU_CMDQ_ARSP_1		(0x000002B4 >> 2)

#define	R32_USB_TLU_CMDQ_ARSP_2		(0x000002B8 >> 2)

#define	R32_USB_TLU_CMDQ_ARSP_3		(0x000002BC >> 2)

#define	R32_USB_TLU_CMDQ_ACPL_0		(0x000002C0 >> 2)

#define	R32_USB_TLU_CMDQ_ACPL_1		(0x000002C4 >> 2)

#define	R32_USB_TLU_CMDQ_ACPL_2		(0x000002C8 >> 2)

#define	R32_USB_TLU_CMDQ_ACPL_3		(0x000002CC >> 2)

#define	R32_USB_TLU_CMDQ_TD_0		(0x000002D0 >> 2)

#define	R32_USB_TLU_CMDQ_TD_1		(0x000002D4 >> 2)

#define	R32_USB_TLU_CMDQ_TD_2		(0x000002D8 >> 2)

#define	R32_USB_TLU_CMDQ_TD_3		(0x000002DC >> 2)

#define	R32_USB_TLU_CMDQ_CPL_0		(0x000002E0 >> 2)

#define	R32_USB_TLU_CMDQ_CPL_1		(0x000002E4 >> 2)

#define	R32_USB_TLU_CMDQ_CPL_2		(0x000002E8 >> 2)

#define	R32_USB_TLU_CMDQ_CPL_3		(0x000002EC >> 2)

#define	R32_USB_TLU_CMDQ_FLAG		(0x000002F0 >> 2)
#define		SR_CMDQ_SET_OCP				(BIT0)
#define		SR_CMDQ_SET_ARSP			(BIT1)
#define		SR_CMDQ_SET_ACPL			(BIT2)
#define		SR_CMDQ_SET_TD				(BIT3)
#define		SR_CMDQ_SET_CPL				(BIT4)
#define		SR_CMDQ_INTF1_OCP			(BIT8)
#define		SR_CMDQ_INTF1_ARSP			(BIT9)
#define		SR_CMDQ_INTF1_ACPL			(BIT10)
#define		SR_CMDQ_INTF1_TD			(BIT11)
#define		SR_CMDQ_INTF1_CPL			(BIT12)
#define		SR_CMDQ_INTF2_OCP			(BIT16)
#define		SR_CMDQ_INTF2_ARSP			(BIT17)
#define		SR_CMDQ_INTF2_ACPL			(BIT18)
#define		SR_CMDQ_INTF2_TD			(BIT19)
#define		SR_CMDQ_INTF2_CPL			(BIT20)

#define	R32_USB_TLU_RXDMA_INFO		(0x000002F4 >> 2)
#define		SR_RXDMA_CTAG_MSK			(BIT_MASK(8))	// BIT [7:0]
#define		SR_RXDMA_DCNT_SFT			(8)
#define		SR_RXDMA_DCNT_MSK			(BIT_MASK(8))	// BIT [15:8]
#define		SR_RXDMA_PAR_ERR			(BIT16)
#define		SR_RXDMA_SIZE_ERR			(BIT17)
#define		SR_RXDMA_CLR_ERR			(BIT18)
#define		SR_RXDMA_RST_ERR			(BIT19)

#define	R32_USB_TLU_TXDMA_INFO		(0x000002F8 >> 2)
#define		SR_TXDMA_CTAG_MSK			(BIT_MASK(8))	// BIT [7:0]
#define		SR_TXDMA_DCNT_SFT			(8)
#define		SR_TXDMA_DCNT_MSK			(BIT_MASK(8))	// BIT [15:8]
#define		SR_TXDMA_PAR_ERR			(BIT16)
#define		SR_TXDMA_SV_ERR				(BIT17)
#define		SR_TXDMA_CLR_ERR			(BIT18)
#define		SR_TXDMA_RST_ERR			(BIT19)

#define	R32_USB_TLU_UAPU_STS		(0x000002FC >> 2)
#define		SR_SQC_CS_SFT				(0)
#define		SR_SQC_CS_MSK				(BIT_MASK(11))	// BIT [10:0]
#define		SR_RDC_CS_SFT				(11)
#define		SR_RDC_CS_MSK				(BIT_MASK(9))	// BIT [19:11]
#define		SR_WB_CS_SFT				(20)
#define		SR_WB_CS_MSK				(BIT_MASK(6))	// BIT [25:20]
#define		SR_RB_CS_SFT				(26)
#define		SR_RB_CS_MSK				(BIT_MASK(6))	// BIT [31:26]

//------------------------------
//USB - ucmu_ss_hs_reg
//------------------------------
#define	R32_USB_EP0_RES				(0x00000300 >> 2)
#define		SR_EP0_SDPP_VLD				(BIT0)
//			[15:1] reserved
#define		FORCE_NRDEY_NAK_RESP		(0)
#define		NORMAL_RESP					(1)
#define		FORCE_STALL_RESP			(2)
#define		CR_EPC_STS_RES_SFT			(16)
#define		CR_EPC_STS_RES_MSK			(BIT_MASK(2))	// BIT [17:16]
//			[23:18] reserved
#define		CR_EPC_DAT_RES_SFT			(24)
#define		CR_EPC_DAT_RES_MSK			(BIT_MASK(2))	// BIT [25:24]

#define	R32_USB_EP0_SETUP_0			(0x00000308 >> 2)

#define	R32_USB_EP0_SETUP_1			(0x0000030C >> 2)

#define	R32_USB_DEV_HALT			(0x00000310 >> 2)
#define		SR_EP1_HALT					(BIT0)
#define		SR_EP2_HALT					(BIT1)
#define		SR_EP3_HALT					(BIT2)
#define		SR_EP4_HALT					(BIT3)
#define		SR_EP5_HALT					(BIT4)
#define		SR_EP6_HALT					(BIT5)
#define		SR_EP7_HALT					(BIT6)
#define		SR_EP8_HALT					(BIT7)
#define		CR_EP1_STALL_AWX			(BIT8)
#define		CR_EP2_STALL_AWX			(BIT9)
#define		CR_EP3_STALL_AWX			(BIT10)
#define		CR_EP4_STALL_AWX			(BIT11)
#define		CR_EP5_STALL_AWX			(BIT12)
#define		CR_EP6_STALL_AWX			(BIT13)
#define		CR_EP7_STALL_AWX			(BIT14)
#define		CR_EP8_STALL_AWX			(BIT15)
#define		CR_EP1_HALT_RST				(BIT16)
#define		CR_EP2_HALT_RST				(BIT17)
#define		CR_EP3_HALT_RST				(BIT18)
#define		CR_EP4_HALT_RST				(BIT19)
#define		CR_EP5_HALT_RST				(BIT20)
#define		CR_EP6_HALT_RST				(BIT21)
#define		CR_EP7_HALT_RST				(BIT22)
#define		CR_EP8_HALT_RST				(BIT23)
//			[31:24] reserved

#define	R32_USB_DEV_ST_0			(0x00000314 >> 2)
//			[7:0] reserved
#define		SR_DEV_ADDR_SFT				(8)
#define		SR_DEV_ADDR_MSK				(BIT_MASK(7))	// BIT [14:8]
//			[15] reserved
#define		SR_DEV_CFG					(BIT16)
#define		SR_SS_DEV_LTMEN				(BIT17)
#define		SR_SS_DEV_U1EN				(BIT18)
#define		SR_SS_DEV_U2EN				(BIT19)
#define		SR_HS_DEV_L1_RW				(BIT20)
#define		SR_HS_DEV_L2_RW				(BIT21)
#define		SR_INTF0_ALT				(BIT22)
#define		SR_INTF1_ALT				(BIT23)
#define		SR_INTF2_ALT				(BIT24)
#define		SR_SS_INTF_FSS_SFT			(25)
#define		SR_SS_INTF_FSS_MSK			(BIT_MASK(3))	// BIT [27:25]
#define		SR_SS_INTF_FRW_SFT			(28)
#define		SR_SS_INTF_FRW_MSK			(BIT_MASK(3))	// BIT [30:28]
#define		SR_SS_UP_U2_ONLY			(BIT31)

#define	R32_USB_DEV_ST_1			(0x00000318 >> 2)
#define		SR_SS_ISOCH_DLY				(BIT_MASK(16))	// BIT [15:0]
#define		SR_SS_DEV_U1PEL_SFT			(16)
#define		SR_SS_DEV_U1PEL_NSK			(BIT_MASK(8))	// BIT [23:16]
#define		SR_SS_DEV_U1SEL_SFT			(24)
#define		SR_SS_DEV_U1SEL_MSK			(BIT_MASK(8))	// BIT [31:24]

#define	R32_USB_DEV_ST_2			(0x0000031C >> 2)
#define		SR_SS_DEV_U2PEL_MSK			(BIT_MASK(16))	// BIT [15:0]
#define		SR_SS_DEV_U2SEL_SFT			(16)
#define		SR_SS_DEV_U2SEL_MSK			(BIT_MASK(16))	// BIT [31:16]

#define	R32_USB_SS_LMP_VD_0			(0x00000320 >> 2)

#define	R32_USB_SS_LMP_VD_1			(0x00000324 >> 2)

#define	R32_USB_SS_LMP_VD_2			(0x00000328 >> 2)
#define		SR_SS_VD_TEST_MSK			(BIT_MASK(8))	// BIT [7:0]
//			[31:8] reserved

#define	R32_USB_SS_LMP				(0x0000032C >> 2)
#define		SR_SS_CAP_NUM_HP_MSK		(BIT_MASK(8))	// BIT [7:0]
#define		SR_SS_CAP_TB_SFT			(8)
#define		SR_SS_CAP_TB_MSK			(BIT_MASK(4))	// BIT [11:8]
#define		SR_SS_CAP_DIR_SFT			(12)
#define		SR_SS_CAP_DIR_MSK			(BIT_MASK(2))	// BIT [13:12]
#define		SR_SS_CAP_SPD				(BIT14)
//			[15] reserved
#define		SR_SS_HUB_DEPTH_SFT			(16)
#define		SR_SS_HUB_DEPTH_MSK			(BIT_MASK(3))	// BIT [18:16]
#define		SR_SS_FORCE_LPM_ACK			(BIT19)
#define		SR_SS_U2_INA_TO_SFT			(20)
#define		SR_SS_U2_INA_TO_MSK			(BIT_MASK(8))	// BIT [27:20]
#define		SR_SS_LPM_DONE				(BIT28)
#define		SR_SS_CFG_SPD				(BIT29)
//			[31:30] reserved


#define	R32_USB_HS_LPM				(0x00000330 >> 2)
#define		SR_HS_LPM_BLNK_ST_MSK		(BIT_MASK(4))	// BIT [3:0]
#define		SR_HS_LPM_BESL_SFT			(4)
#define		SR_HS_LPM_BESL_MSK			(BIT_MASK(4))	// BIT [7:4]
//			[31:30] reserved

#define	R32_USB_SS_LTM_0			(0x00000334 >> 2)
#define		CR_P2PM_EP_BUSY				(BIT0)
#define		CR_SS_DEV_LTMC				(BIT1)
//			[3:2] reserved
#define		CR_LT_ACTIVE_BELT_SFT		(4)
#define		CR_LT_ACTIVE_BELT_MSK		(BIT_MASK(12))	// BIT [15:4]
#define		CR_LT_IDLE_BELT_SFT			(16)
#define		CR_LT_IDLE_BELT_MSK			(BIT_MASK(12))	// BIT [27:16]
//			[31:28] reserved

#define	R32_USB_SS_LTM_1			(0x00000338 >> 2)
#define		CR_SS_DN_SEND				(BIT0)
#define		SR_SS_DN_SEND				(BIT1)
#define		CR_SS_DN_LTM				(BIT2)
#define		CR_SS_DN_HP_TYPE			(BIT3)
#define		CR_SS_DN_DPP_ADDR			(BIT_MASK(10))	// BIT [13:4]
//			[15:14] reserved
#define		CR_SS_DN_HP_SID_SFT			(16)
#define		CR_SS_DN_HP_SID_MSK			(BIT_MASK(16))	// BIT [31:16]

#define	R32_USB_SS_LTM_2			(0x0000033C >> 2)

#define	R32_USB_LNK_ST				(0x00000350 >> 2)
#define		SR_SS_LTSSM_DIS				(BIT0)
#define		SR_SS_LTSSM_INA				(BIT1)
#define		SR_SS_LTSSM_RXDET			(BIT2)
#define		SR_SS_LTSSM_POLL			(BIT3)
#define		SR_SS_LTSSM_RCV				(BIT4)
#define		SR_SS_LTSSM_U0				(BIT5)
#define		SR_SS_LTSSM_U1				(BIT6)
#define		SR_SS_LTSSM_U2				(BIT7)
#define		SR_SS_LTSSM_U3				(BIT8)
#define		SR_SS_LTSSM_HRST			(BIT9)
#define		SR_SS_LTSSM_LB				(BIT10)
#define		SR_SS_LTSSM_COMP			(BIT11)
//			[15:12] reserved
#define		SR_HS_LNK_L0				(BIT16)
#define		SR_HS_LNK_L1				(BIT17)
#define		SR_HS_LNK_L2				(BIT18)
#define		SR_HS_LNK_L3				(BIT19)
//			[23:20] reserved
#define		SR_USB_RXDET_F_SFT			(24)
#define		SR_USB_RXDET_F_MSK			(BIT_MASK(2))	// BIT [25:24]
#define		SR_DET_POL_LFPS				(BIT26)
//			[31:27] reserved

#define	R32_USB_DEV_LPM				(0x00000354 >> 2)
#define		SR_HRST_ST					(BIT0)
#define		CR_HRST_DONE				(BIT1)
#define		SR_WRST_ST					(BIT2)
#define		CR_SS_ENTER_U1				(BIT8)
#define		CR_SS_ENTER_U2				(BIT9)
#define		CR_SS_NO_LGO_U1				(BIT10)
#define		CR_SS_NO_LGO_U2				(BIT11)
#define		CR_SS_LXU_U1				(BIT12)
#define		CR_SS_LXU_U2L1				(BIT13)
//			[23:14] reserved
#define		CR_DEV_RESUME				(BIT24)
#define		CR_SS_P2M_LINK_RTN			(BIT25)
#define		CR_EXIT_DIS_EVT				(BIT26)
#define		UTMI_SUSPENDM				(BIT27)
#define		MAC_POWERDOWN_0_SFT			(28)
#define		MAC_POWERDOWN_0_MSK			(BIT_MASK(2))	// BIT [29:28]
#define		MAC_POWERDOWN_1_SFT			(30)
#define		MAC_POWERDOWN_1_MSK			(BIT_MASK(2))	// BIT [31:30]

#define	R32_USB_DES_LEN_0			(0x00000360 >> 2)
#define		CR_DEV_LEN_MSK				(BIT_MASK(8))	// BIT [7:0]
#define		CR_CFG_LEN_SFT				(8)
#define		CR_CFG_LEN_MSK				(BIT_MASK(8))	// BIT [15:8]
#define		CR_BOS_LEN_SFT				(16)
#define		CR_BOS_LEN_MSK				(BIT_MASK(8))	// BIT [23:16]
#define		CR_STR0_EN					(BIT24)
#define		CR_GETMAXLUN_EN				(BIT25)
//			[31:26] reserved

#define	R32_USB_DES_LEN_1			(0x00000364 >> 2)
#define		CR_STR1_LEN_MSK				(BIT_MASK(8))	// BIT [7:0]
#define		CR_STR2_LEN_SFT				(8)
#define		CR_STR2_LEN_MSK				(BIT_MASK(8))	// BIT [15:8]
#define		CR_STR3_LEN_SFT				(16)
#define		CR_STR3_LEN_MSK				(BIT_MASK(8))	// BIT [23:16]
//			[31:24] reserved

#define	R32_USB_DES_LEN_2			(0x00000368 >> 2)
#define		CR_STR7_LEN_MSK				(BIT_MASK(8))	// BIT [7:0]
#define		CR_STR8_LEN_SFT				(8)
#define		CR_STR8_LEN_MSK				(BIT_MASK(8))	// BIT [15:8]
#define		CR_STRA_LEN_SFT				(16)
#define		CR_STRA_LEN_MSK				(BIT_MASK(8))	// BIT [23:16]
//			[31:24] reserved

#define	R32_USB_ERR_CLR				(0x00000370 >> 2)
#define		CR_LOSS_SYM_CLR_EVT			(BIT0)
#define		CR_8B10B_CLR_EVT			(BIT1)
#define		CR_SS_LNK_ERR_CLR_EVT		(BIT2)
#define		CR_HS_LNK_ERR_CLR_EVT		(BIT3)
//			[15:4] reserved
#define		CR_RSYNC_EP1_PTR			(BIT16)
#define		CR_RSYNC_EP2_PTR			(BIT17)
#define		CR_RSYNC_EP3_PTR			(BIT18)
#define		CR_RSYNC_EP4_PTR			(BIT19)
#define		CR_RSYNC_EP5_PTR			(BIT20)
#define		CR_RSYNC_EP6_PTR			(BIT21)
#define		CR_RSYNC_EP7_PTR			(BIT22)
#define		CR_RSYNC_EP8_PTR			(BIT23)
//			[31:24] reserved

#define	R32_USB_ERR_LMT				(0x00000374 >> 2)
//			[3:0] reserved
#define		LOSS_ERR_LMT_0Fh			(0x00)
#define		LOSS_ERR_LMT_1Fh			(0x01)
#define		LOSS_ERR_LMT_2Fh			(0x02)
#define		LOSS_ERR_LMT_FFh			(0x0F)
#define		CR_LOSS_SYM_LMT_SFT			(4)
#define		CR_LOSS_SYM_LMT_MSK			(BIT_MASK(4))	// BIT [7:4]
//			[11:8] reserved
#define		CR_8B10B_ERR_LMT_0Fh		(0x00)
#define		CR_8B10B_ERR_LMT_1Fh		(0x01)
#define		CR_8B10B_ERR_LMT_2Fh		(0x02)
#define		CR_8B10B_ERR_LMT_FFh		(0x0F)
#define		CR_8B10B_ERR_LMT_SFT		(12)
#define		CR_8B10B_ERR_LMT_MSK		(BIT_MASK(4))	// BIT [15:12]
//			[19:16] reserved
#define		LNK_ERR_LMT_0Fh				(0x00)
#define		LNK_ERR_LMT_1Fh				(0x01)
#define		LNK_ERR_LMT_2Fh				(0x02)
#define		LNK_ERR_LMT_FFh				(0x0F)
#define		CR_SS_LNK_ERR_LMT_SFT		(20)
#define		CR_SS_LNK_ERR_LMT_MSK		(BIT_MASK(4))	// BIT [23:20]
//			[27:24] reserved
#define		CR_HS_LNK_ERR_LMT_SFT		(28)
#define		CR_HS_LNK_ERR_LMT_MSK		(BIT_MASK(4))	// BIT [31:28]

#define	R32_USB_ERR_ST_0			(0x00000378 >> 2)
#define		SR_LOSS_SYM_NUM_MSK			(BIT_MASK(8))	// BIT [7:0]
#define		SR_8B10B_ERR_NUM_SFT		(8)
#define		SR_8B10B_ERR_NUM_MSK		(BIT_MASK(8))	// BIT [15:8]
#define		SR_SS_LINK_ERR_NUM_SFT		(16)
#define		SR_SS_LINK_ERR_NUM_MSK		(BIT_MASK(8))	// BIT [23:16]
#define		SR_HS_LINK_ERR_NUM_SFT		(24)
#define		SR_HS_LINK_ERR_NUM_MSK		(BIT_MASK(4))	// BIT [27:24]
//			[31:28] reserved

#define	R32_USB_ERR_ST_1			(0x0000037C >> 2)
#define		SR_BAD_LC_P					(BIT0)
#define		SR_LDN_RTN_P				(BIT1)
#define		SR_INIT_DONE				(BIT2)
#define		SR_HPCRC_ERR				(BIT3)
#define		SR_3LBAD_ERR				(BIT4)
#define		SR_RXHDR_ERR				(BIT5)
#define		SR_OVFL_ERR					(BIT6)
#define		SR_ACKSEQ_ERR				(BIT7)
#define		SR_LCRD_ERR					(BIT8)
#define		SR_ADV_ERR					(BIT9)
#define		SR_LGD_FATERR				(BIT10)
#define		SR_LCRD_FATERR				(BIT11)
#define		SR_HP_TO					(BIT12)
//			[15:13] reserved
#define		SR_RMT_HDR_CRD_SFT			(16)
#define		SR_RMT_HDR_CRD_MSK			(BIT_MASK(3))	// BIT [18:16]
//			[19] reserved
#define		SR_LOCAL_HDR_CRD_SFT		(20)
#define		SR_LOCAL_HDR_CRD_MSK		(BIT_MASK(3))	// BIT [22:20]
//			[23] reserved
#define		SR_RMT_HDR_CRD_TYPA_SFT		(24)
#define		SR_RMT_HDR_CRD_TYPA_MSK		(BIT_MASK(3))	// BIT [26:24]
//			[27] reserved
#define		SR_LOCAL_HDR_CRD_TYPA_SFT	(28)
#define		SR_LOCAL_HDR_CRD_TYPA_MSK	(BIT_MASK(3))	// BIT [30:28]
//			[31] reserved

#define	R32_USB_ERR_ST_2			(0x00000380 >> 2)
//			[15:0] reserved
#define		SR_SS_8B10B_ERRFLG			(BIT16)
#define		SR_SS_LOSS_SYM_ERRFLG		(BIT17)
#define		SR_SS_EB_OVFL_ERRFLG		(BIT18)
#define		SR_SS_EB_UDFL_ERRFLG		(BIT19)
#define		SR_SS_ADD_SKP_ERRFLG		(BIT20)
#define		SR_SS_RMV_SKP_ERRFLG		(BIT21)
#define		SR_SS_LFPS_DIS_ERRFLG		(BIT22)
#define		SR_DESKEW_OVFLAG			(BIT23)			// [CP 190916] For by2, checking two lanes are aligned(otherwise, dropped into one lane)

#define		SR_U0_2RVY_CNT_SFT			(24)
#define		SR_U0_2RVY_CNT_MSK			(BIT_MASK(8))	// BIT [31:24]

#define	R32_USB_TXRAM_ERR			(0x000003E0 >> 2)
//			[15:0] reserved
#define		SR_TXRAM_ERR_ADDR_SFT		(16)
#define		SR_TXRAM_ERR_ADDR_MSK		(BIT_MASK(14))	// BIT [29:16]
#define		SR_TXRAM_ERR				(BIT30)
#define		CR_TXRAM_ERR_CLR			(BIT31)
//------------------------------
//USB - ucmu_ss_op_reg
//------------------------------
#define	R32_USB_SS_PLU_CFG_0		(0x00000400 >> 2)
#define	R32_USB_SS_PLU_CFG_1		(0x00000404 >> 2)
#define	R32_USB_SS_LLU_CFG_0		(0x00000440 >> 2)
#define	R32_USB_SS_MAC_CFG_0		(0x00000480 >> 2)
#define	R32_USB_SS_MAC_CFG_1		(0x00000484 >> 2)
#define	R32_USB_SS_MAC_CFG_2		(0x00000488 >> 2)
#define 	CR_SKP_16B                  (BIT28)

#define	R32_USB_SS_MAC_CFG_3		(0x0000048C >> 2)

#define	R32_USB_SS_MAC_CFG_4		(0x00000490 >> 2)
#define		CR_CP_EN					(BIT5)	// default: 0
#define		CR_LBPM2TSEQ_DLY_SFT		(18)
#define		CR_LBPM2TSEQ_DLY_MSK		(BIT_MASK(2))	// BIT [19:18] default: 0
#define 	CR_FPGA_INTEL_WA			(BIT20)	// default: 0
#define		CR_TSEQ_DLY_SFT				(24)
#define		CR_TSEQ_DLY_MSK				(BIT_MASK(2))	// BIT [25:24], default: 1
#define 	CR_RX_POLARITY_0			(BIT28)	// default: 0
#define 	CR_RX_POLARITY_1			(BIT29)	// default: 0

#define	R32_USB_SS_PMU_CFG_0		(0x000004C0 >> 2)
//------------------------------
//USB - ucmu_hs_op_reg
//------------------------------
#define	R32_USB_HS_PLU_CFG_0		(0x00000500 >> 2)
#define	R32_USB_HS_MAC_CFG_0		(0x00000580 >> 2)

#endif /* (USB == HOST_MODE) */

#endif /* _USB_REG_H_ */
