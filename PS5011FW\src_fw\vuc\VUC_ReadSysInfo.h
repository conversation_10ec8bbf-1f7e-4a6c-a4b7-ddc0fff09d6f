#ifndef _VUC_READSYSINFO_H_
#define _VUC_READSYSINFO_H_

#include "typedef.h"
#include "table/sys_block/sys_block_api.h"
#include "err_handle/rma_log_api.h"
#include "vuc/VUC_SmartRescue_api.h"

#define VENDER_READ_SYSINFO_SIZE (4)
#define	BURNER_VENDOR_BUFFER_LENGTH (0x40000)
#define FIRST_CODEBLOCK_IN_CE0	(0)
#define SECOND_CODEBLOCK_IN_CE0	(1)
#if (OPEN_BLOCK_RSMAP_EN)
#define VUC_FREE_BUF_POOL_SIZE_IN_4K	(24)
#else /*(OPEN_BLOCK_RSMAP_EN)*/
#define VUC_FREE_BUF_POOL_SIZE_IN_4K	(36)
#endif /*(OPEN_BLOCK_RSMAP_EN)*/
#define VUC_WRITE_CACHE_SIZE_IN_4K		(4)
#define VUC_WRITE_BUF_UNIT_IN_32K		(15)
#define VUC_WRITE_BUF_UNIT_IN_16K		(14)
#define VUC_WRITE_BUF_ONLY_FLASH		(1)
#define VUC_WRITE_BUF_FLASH_AND_CACHE	(2)

#define UVC_READSYSTEMINFO_IDENTIFY_VERSION_70DEGREE	0
#define UVC_READSYSTEMINFO_IDENTIFY_VERSION_85DEGREE	1

#define VUC_READSYSINFO_SYS_CLOCK_SHIFT_BIT	(1)
//MP Capability
#define VUC_READSYSTEMINFO_MP_CAPABILITY_EARLY_FINAL_BICS4_BIT	BIT(0)
#define VUC_READSYSTEMINFO_MP_CAPABILITY_DRIVE_RE_BY_CHANNEL		BIT(1)
#define VUC_READSYSTEMINFO_MP_CAPABILITY_REINITIAL_KEEP_PSID_BIT	BIT(2)
/* Read System Info Structure */

#define VUC_GET_SYSTEM_INFO_DATA_NORMAL_MODE	(0)
#define VUC_GET_SYSTEM_INFO_DATA_RMA_LOG_MODE	(1)

#define VUC_TCG_SSC_OPAL	(0)
#define VUC_TCG_SSC_PYRITE	(1)

#define VUC_FW_SUPPORT_FEATURE_FAKE_BB_BIT				BIT(0)
#define VUC_FW_SUPPORT_FEATURE_NO_LOADSWITCH_BIT		BIT(1)
#define VUC_FW_SUPPORT_FEATURE_PARAMETER_TABLE_VUC_BIT	BIT(2)
#define VUC_FW_SUPPORT_FEATURE_DETECT_D1D3_BIT	   		BIT(3)
#define VUC_FW_SUPPORT_FEATURE_PARAMETER_OVERLOAD		BIT(5)
#define VUC_FW_SUPPORT_FEATURE_3D_RANDOMIZER			BIT(6)
#define VUC_FW_SUPPORT_FEATURE_LIST_BAD_SUBFEA2			BIT(7)

#define VUC_FW_SUPPORT_FEATURE_LIST					(VUC_FW_SUPPORT_FEATURE_DETECT_D1D3_BIT | VUC_FW_SUPPORT_FEATURE_3D_RANDOMIZER | VUC_FW_SUPPORT_FEATURE_LIST_BAD_SUBFEA2)

#if PS5021_EN
#define VUC_GER_SYS_INFO_RUNNING_MODE_BURNER    (1)
#define VUC_GER_SYS_INFO_RUNNING_MODE_RDT    (2)
#define VUC_GER_SYS_INFO_RUNNING_MODE_FW    (3)
#endif /*PS5021_EN*/
typedef struct {
	/* 0~31 : Flash Configuration */
	U8 ubCE_num;
	U8 ubCH_num;
	U8 ubBank_num;
	U8 ubMakerCode;
	U8 ubSizePerDie;
	// 0: TSB A19nm 08G, B: Micron L95B+
	U8 ubFlashProcess1;
	// 19: TSB A19nm, 95: Micron L95B+, 3: BiCs3, 16: B16, 4:V4
	U8 ubFlashProcess2;
	// A: TSB A19nm, L:  Micron L95B+, B: TSH Bics2/3/4, B: Micron B16/B17, N: Micron N18/N2X, V: Hynix V4
	U8 ubFlashProcess3;
	U8 ubBitPerCell;
	U8 ubPageSize;
	U16 uwPageNumPerBlock;
	U16 uwPhyBlockNumPerCE;
	U8 ubPhyBlockSize;
	U8 ubDieNumPerCE;
	U8 ubPlaneNumSupport;
	U8 ubPseudoPlane;
	U8 ubTransferModeSupport;
	U8 ubFlahNoSupport;
	U16 uwPhyBoundaryPerDie;
	// RSV_X_Y :  X=sector, Y=start byte
	U8 ubReserved1[2];		// Bytes [22:23]
	U32 ulFlashCE0_to_32Bitmap[2];

	/* 32~55 : Flash Setting */
	U8 LDPC_ECC_Mode;
	U8 ubPlaneNumEnable;
	U8 ubTransferModeEnable;
	U8 ubVBShift;
	U16 uwTotalUnitListSize;
	U16 uwTotalUnit;
	U16 uwTotalD1UnitNum;
	U16 uwD1MaxUnitNum;
	U16 uwMinimumOperationD1UnitNum;
	U16 uwP2L_Size_of_VB_D1;
	U16 uwP2L_Size_of_VB_D2_or_D3;
	U16 uwTotablL2PSize;
	U32 ulTotalPTENum;

	/* 56~87 : System setting */
	U16 uwCPUClock;
	U16 uwSystemClock;
	U16 uwSecurityIPClock;
	U16 uwFlashClock;
	U16 uwSPIClock;
	U16 uwECCClock;
	U16 uwZIPClock;
	U16 uwZQClock;
	U16 uwSMSClock;
	U16 uwWriteBufSize;
	U8 ubCESemaphoreNum;
	U8 ubDQReverse[4];
	U8 ubCEDecoder;
	U8 ubFIOVoltage;
	U8 ubExternalThermalSensor;
	struct {
		U8 btDPSSupport	: 1;
		U8 btDPSEn : 1;
		U8 ubReserved	: 6;
	} ubFIPDPSFeature;
	/* 85 FWSupportFeature */
	union {
		U8 ubAll;
		struct {
			U8 btDetectFakeBBIC : 1;
			U8 btDetectLoadSwitchVUC : 1;
			U8 btSupportParameterTableVUC : 1;  //JGS
			U8 btDetectD1D3_EN: 1;
			U8 btFetchPMICRegValue : 1;
			U8 ubParameterOverload	:	1;
			U8 ubTrim3DRandomizerRequestFlag :	1;
			U8 btListBadSubFea2	: 1;
		} B;
	} ubFWSupportFeature;
	union {
		U8 ubAll;
		struct {
			U8 btTXOpenRXShort : 1;
			U8 btSupport4TBSpareLCA : 1;
			U8 btReserv : 6;
		} B;
	} ubFWSupportFeature2;
	U8 ubReserved2[1];

	/* 88~127 : Version Record */
	U8 ubBurnerVersionUsed[4];
	U32 ulRDTVersionUsed;
	U64 uoPreviousFWVersion;
	U16 uwPreviousFWCompileTimeHour;
	U16 uwPreviousFWCompileTimeDay;
	U16 uwPreviousFWCompileTimeMonth;
	U16 uwPreviousFWCompileTimeYear;
	U32 ulBurnerVersionRequired;
	U32 ulChipIdentifier[2];
	U16 uwFWAssertCode;
	U16 uwFWErrCondition;

	/* 128~191 : Table address */
	U8 ubCodeBlockCopy_0_AddrCE;
	U8 ubCodeBlockCopy_0_AddrPBlock;
	U8 ubCodeBlockCopy_1_AddrCE;
	U8 ubCodeBlockCopy_1_AddrPBlock;
	U8 ubCodeBlockCopy_2_AddrCE;
	U8 ubCodeBlockCopy_2_AddrPBlock;
	U8 ubCodeBlockCopy_3_AddrCE;
	U8 ubCodeBlockCopy_3_AddrPBlock;
	U8 ubCodeBlockCopy_4_AddrCE;
	U8 ubCodeBlockCopy_4_AddrPBlock;
	U8 ubCodeBlockCopy_5_AddrCE;
	U8 ubCodeBlockCopy_5_AddrPBlock;
	U8 ubCodeBlockCopy_6_AddrCE;
	U8 ubCodeBlockCopy_6_AddrPBlock;
	U8 ubCodeBlockCopy_7_AddrCE;
	U8 ubCodeBlockCopy_7_AddrPBlock;
	U8 ubSystemPTBlockCopy_0_AddrCE;
	U8 ubSystemPTBlockCopy_0_AddrPBlock;
	U8 ubSystemPTBlockCopy_1_AddrCE;
	U8 ubSystemPTBlockCopy_1_AddrPBlock;
	U8 ubRUTBlockCopy_0_AddrCE;
	U8 ubRUTBlockCopy_0_AddrPBlock;
	U8 ubRUTBlockCopy_1_AddrCE;
	U8 ubRUTBlockCopy_1_AddrPBlock;
	U8 ubReserved3[40];		// Bytes [152:191]

	/* 192~255 : DDR Configuration(FW code mode) */
	U32 ulHMBSize;
	U32 ulExtraErrorInfo;
	U32 ulFailedCEBMP;
	U32 ulSLCPool4kNum;
	U32 ulClearSLCPoolThreshold;
	U32 ul4kEntrysPerSLCUnit;
	U16 uwSLCMaxPECnt;
	U16 uwD1FreeUnitCnt;
	U16 uwD3FreeUnitCnt;
	U8	ubTotalRsvUnitCnt;
	U8 ubReadyBenchmark;  // Byte 233
	U8 ubReserved4[8];
	U32 ulRMALogRevision;
	U16 uwRMALogAssertReason[RMALOG_ASSERT_REASON_NUM];

	/* 256~511 : System status & log */
	U16 uwFWInternalVersionHour;
	U16 uwFWInternalVersionDate;
	U16 uwFWInternalVersionMonth;
	U16 uwFWInternalVersionYear;
	U8 ubReserved5;			// Byte 264
	U8 ubDiskInitFail;
	U16 uwDiskHWStatus;
	U8 ubWriteProtect;
	U8 ubFTL_ERR_PATH;
	U16 uwGPIOStatusBMP;		// Bytes [270:271]
	U32 ulFWCodeUpdateCount;
	U8 ubSecurityState;
	U8 ubReserved6;			// Byte 277
	U8 ubSVN_VER[8];
	U16 uwReserved3;		// Bytes [286:287]
	U32 ulPowerCycleCount;
	U32 ulAbnormalPowerCycleCount;
	U32 ulFWInternalPowerCycleCount;
	U32 ulPowerOnTime;
	U16 uwBGFlushTime;
	U16 uwMaxBGFlushTime;
	U32 ulFlashIPResetCount;
	U32 ulHostE3DErrorCount;
	U32 ulFlashE3DErrorCount;
	U32 ulDDR_ECC_ErrorCount;
	U32 ulDBUF_ECC_ErrorCount;
	U32 ulGCforTableTriggerCount;
	U32 ulGCforDataTriggerCount_D1;
	U32 ulGCforDataTriggerCount_D2_or_D3;
	U32 ulGCforDataTriggerCount_DynamicD1;
	U8 ubGCBlockRateofData_D1;
	U8 ubGCBlockRateofData_D2_or_D3;
	U8 ubGCBlockRateofData_DynamicD1;
	U8 ubVendorAESSetKeyStatus1;
	U8 ubReserved7[5];		// Bytes [348:352]
	struct {
		U8 btTcgSupport						: 1;
		U8 btTcgOpalSSCSupport				: 1;
		U8 btTcgPyriteSSCSupport			: 1;
		U8 btTcgPyriteSSCVersion1Support	: 1;
		U8 btTcgPyriteSSCVersion2Support	: 1;
		U8									: 3;
	} TcgSupport;		// Byte 353
	struct {
		U8 btTcgEn							: 1;
		U8 btTcgSSC							: 1;
		U8 TcgPyriteSSCVersion				: 2;
		U8									: 4;
	} TcgConfig;		//Byte 354
	U8 ubVendorAESSetKeyStatus2;
	U32 ulWearLevelingCheckCount_D1;
	U32 ulWearLevelingTriggerCount_D1;
	U8 ubWearLevelingBlockRate_D1;
	U8 ubReserved8[3];		// Bytes [365:367]
	U32 ulWearLevelingCheckCount_D2_or_D3;
	U32 ulWearLevelingTriggerCount_D2_or_D3;
	U8 ubWearLevelingBlockRate_D2_or_D3;
	U8 ubVUCProtectMode;
	U8 ubVUCProtectStatus;
	U8 ubCodeSignStatus;
	U32 ulCodeSignKeyIdx;
	U16 uwVUCProtectSecureKeyIdx;
	U8 ubMPCapability[4];
	U8 ubSupportVUCDirectedRead;
#if PS5021_EN
	U8 ubReserved9[4];
	U8 ubRunningMode;
#else /*PS5021_EN*/
#if RDT_MODE_EN
	U8 ubFWInternalVersion[4];	// Bytes [418:419]
	U8 ubReserved9[1];
#else
	U8 ubReserved9[5];
#endif
#endif /*PS5021_EN*/
	U8 ubFWSubVersion[4];
	U8 ubICVersion[6];
	U8 ubICVersion_HardwareIteration;
	U8 ubICVersion_BootCodeIteration;
	U8 ubSystemInfoFormatVersion[2];
	U8 ubTrueFWVersion[8];
	U8 ubReserved10[2];		// Bytes [418:419]

	/* DBUF Configuration */
	U8 ubReserved11[90];	// Bytes[420:509]
	U16 IntegrityWord_sector0;

}
Sector_0_CommomInfo_t;
TYPE_SIZE_CHECK(Sector_0_CommomInfo_t, 512);

#define VUC_READ_SYSTEM_INFO_VUC_PROTECT_MODE_NO_SUPPORT    (0)
#define VUC_READ_SYSTEM_INFO_VUC_PROTECT_MODE_V1            (1) //Obsolete, not supported
#define VUC_READ_SYSTEM_INFO_VUC_PROTECT_MODE_V2            (2)

#define VUC_READ_SYSTEM_INFO_VUC_PROTECT_STATE_LOCK         (1)
#define VUC_READ_SYSTEM_INFO_VUC_PROTECT_STATE_ENGINEERING  (2)
#define VUC_READ_SYSTEM_INFO_VUC_PROTECT_STATE_SECURITY     (3)
#define VUC_READ_SYSTEM_INFO_VUC_PROTECT_STATE_NOT_PROTECT  (4) //It's the same as if VUC Protect Mode is 0, Not supported

typedef struct {
	/* 0~151 : Flash status log */
	U32 ulMaxEC_D1;
	U32 ulMaxEC_D2_or_D3;
	U32 ulAverageEC_D1;
	U32 ulAverageEC_D2_or_D3;
	U32 ulMinEC_D1;
	U32 ulMinEC_D2_or_D3;
	U64 uoTotalFlashEC_D1;
	U64 uoTotalFlashEC_D2_or_D3;
	U64 uoTotalFlashProgramCnt_D1;
	U64 uoTotalFlashProgramCnt_D2_or_D3;
	U64 uoTotalFlashReadCnt;
	U64 uoTotalFlashWriteCnt;
	U64 uoReadFlashUNCRetryokCnt_D1;
	U64 uoReadFlashUNCRetryokCnt_D2_or_D3;
	U32 ulReadFlashUNCRetryfailCnt_D1;
	U32 ulReadFlashUNCRetryfailCnt_D2_or_D3;
	U32 ulRaidECCRecoveryokCnt_D1;
	U32 ulRaidECCRecoveryokCnt_D2_or_D3;
	U32 ulRaidECCRecoveryfailCnt_D1;
	U32 ulRaidECCRecoveryfailCnt_D2_or_D3;
	U16 uwLogicalGoodBlockCnt_D1;
	U16 uwLogicalGoodBlockCnt_D2_or_D3;
	U16 uwTotalEarlyBadPhysicalBlockCnt;
	U16 uwTotalLaterBadPhysicalBlockCnt;
	U16 uwTotalReadFailBlockCnt_D1;
	U16 uwTotalReadFailBlockCnt_D2_or_D3;
	U16 uwTotalProgramFailBlockCnt_D1;
	U16 uwTotalProgramFailBlockCnt_D2_or_D3;
	U16 uwTotalEraseFailBlockCnt_D1;
	U16 uwTotalEraseFailBlockCnt_D2_or_D3;
	U32 ulRaidECCEntry;
	U32 ulReadDisturbReadVerifyCnt;
	U32 ulD1PECycle;
	U16 uwD3PECycle;
	U8 ubReserved1[2];		// Bytes [146:147]
	U32 ulReadDisturbForceSwapCnt;

	/* 152~255 : Data Packet log */
	U32 ulRandomReadCmd_4KCount;
	U32 ulRandomReadCmd_8KCount;
	U32 ulRandomReadCmd_16KCount;
	U32 ulRandomReadCmd_32KCount;
	U32 ulRandomReadCmd_64KCount;
	U32 ulRandomReadCmd_Other;
	U32 ulSequentialReadCmd_32KCount;
	U32 ulSequentialReadCmd_64KCount;
	U32 ulSequentialReadCmd_128KCount;
	U32 ulSequentialReadCmd_1MCount;
	U32 ulSequentialReadCmd_4MCount;
	U32 ulSequentialReadCmd_8MCount;
	U32 ulSequentialReadCmd_Other;
	U32 ulRandomWriteCmd_4KCount;
	U32 ulRandomWriteCmd_8KCount;
	U32 ulRandomWriteCmd_16KCount;
	U32 ulRandomWriteCmd_32KCount;
	U32 ulRandomWriteCmd_64KCount;
	U32 ulRandomWriteCmd_Other;
	U32 ulSequentialWriteCmd_32KCount;
	U32 ulSequentialWriteCmd_64KCount;
	U32 ulSequentialWriteCmd_128KCount;
	U32 ulSequentialWriteCmd_1MCount;
	U32 ulSequentialWriteCmd_4MCount;
	U32 ulSequentialWriteCmd_8MCount;
	U32 ulSequentialWriteCmd_Other;

	/* 256~309 : Drive info */
	U64 uoMaxPCAValue;
	U32 ulVUCBufferBase;
	U32 ulVUCBufferSize;
	U64 uoHostReadCount;
	U64 uoHostWriteCount;
	U64 uoBackgroundReadCount;
	struct {
		U8 btBGEN		: 1;
		U8 btFlashSSCEN : 1;
		U8 btAESEN		: 1;
		U8 btDataZIPEN	: 1;
		U8 btXZIPEN		: 1;
		U8 btZQCEN		: 1;
		U8 btNandZQCLEn	: 1;
	} ubFWSetting;

#if (!SMART_RESCUE_EN)
	U8 ubReserved2[7];		// Bytes [297:303]
	U16 uwVBSize; 			//MB
	U8 ubReserved2_1[4];		// Bytes [306:309]
#else /*(!SMART_RESCUE_EN)*/
	U8 ubReserved2[5];		// Bytes [297:301]

	/* 302~309 : Smart Rescue info */
	struct {
		U16 uwParseSize; //MB
		U16 uwUnitSize; //MB
		U16 uwTransferSize; //KB
		U16 uwTotalParsingCount; //total VB number
	} SmartRescue;
#endif /*(!SMART_RESCUE_EN)*/

	/* 310~415 : RDT information */
	U16 uwScanLogBitmap;
	U16 uwRDT_DBTlogPageCount;
	U16 uwRDT_ERLlogPageCount;
	U16 uwRDT_TDLlogPageCount;
	U16 uwRDT_FMLlogPageCount;
	U16 uwRDT_RMLlogPageCount;
	U16 uwRDT_PRLlogPageCount;

#if (RDT_MODE_EN || RDT_BURNER_MODE_EN)
	U8 ubPortID[20];                // Bytes [324:343] // kingston
	U8 ubReserved3_1[1];            // Bytes [344]
	U8 ubRDTMPCapability;           // Bytes [345]
	U16 uwAssertFailInfo;           // Bytes [346:347]
	U8 ubReserved3_2[4];            // Bytes [348:351]
	U16 uwRDT_STLlogPageCount;      // Bytes [352:353]
	U16 uwRDT_TMLlogPageCount;      // Bytes [354:355]
	U32 ulAssertFailInterrupt;      // Bytes [356:359]
	U64 ullAssertFailDBMsg;         // Bytes [360:367]
	U8 ulBgassdSocketId[16];        // Bytes [368:383]
	U8 ulBgassdPowerId;             // Bytes [384]
	U8 ubAssertFailRDTState;        // Bytes [385]
	U16 uwAssertFailCTLTemperature; // Bytes [386:387]
	U32 ulAssertFailTime;           // Bytes [388:391]
	U8 ubAssertFailFlashTestLoop;   // Bytes [392]
	U8 ubAssertFailFlashTestCycle;  // Bytes [393]
	U16 uwRDT_BEClogPageCount;      // Bytes [394:395]
	U8 ubReserved3_3[2];            // Bytes [396:397]
	U16 uwRDT_SCANWlogPageCount;    // Bytes [398:399]
	U8 ubReserved3_4[9];            // Bytes [400:408]
	U8 ubInitFailType;              // Bytes [409]
	U16 uwRDT_VRLClogPageCount;     // Bytes [410:411]
#if RDT_RECORD_ECC_DISTRIBUTION
	U32 ulRDT_EDLlogPageCount;		// Bytes [412:415]
#else
	U8 ubReserved3_5[4];			// Bytes [412:415]
#endif
#else /* (RDT_MODE_EN || RDT_BURNER_MODE_EN) */
	U8 ubReserved3_1[21];		// Bytes [324:344]
	U8 ubPERSTSupport;
	U8 ubReserved3_2[56];		// Bytes [346:401]
	U8 ubSideBandIOTestSupport;	// Bytes [402]
	U8 ubReserved3[13];		// Bytes [403:415]
#endif /* (RDT_MODE_EN || RDT_BURNER_MODE_EN) */
	/* 416~511 : Efuse Configure */
	U8 ubReserved4[94];		// Bytes [416:509]
	U16 IntegrityWord_sector1;

}
Sector_1_CommomInfo_t;
TYPE_SIZE_CHECK(Sector_1_CommomInfo_t, 512);

typedef struct {
	/* 0 - 283 : Event Info (284 Byte) */
	U32 ul001;                              // 000-003
	U32 ul003;                              // 004-007
	U32 ul004;                              // 008-011
	U32 ul006;                              // 012-015
	U32 ul007;                              // 016-019
	U32 ul008;                              // 020-023
	U32 ul009;                              // 024-027
	U32 ul00A;                              // 028-031
	U32 ul00F;                              // 032-035
	U32 ul010;                              // 036-039
	U32 ul012;                              // 040-043
	U32 ul013;                              // 044-047
	U32 ul10b_to_8b_DecodeErrCount;         // 048-051
	U32 ulDisparityErrCount;                // 052-055
	U32 ulFISErr;                           // 056-059
	U32 ulHandShakeErr;                     // 060-063
	U32 ulE3DErrInterrupt;                  // 064-067
	U32 ulSATAReadUncorrect4KAddrInterrupt; // 068-071
	U32 ulSYNCEscapeInDataFIS;              // 072-075
	U32 ulDataFISLengthExceed;              // 076-079
	U32 ulSCTWriteCmdTimeout;               // 080-083
	U32 ulSCTReadCmdTimeout;                // 084-087
	U64 uoSATAReadUncorrectLBAAddr;         // 088-095
	U64 uoE3DLBAAddr;                       // 096-103
	U16 uwWriteUNCCmdCount;                 // 104-105
	U16 uwBGStartThreshold;                 // 106-107
	U32 ulLinkLostCnt;                      // 108-111
	U32 ulHostReadCRCErrCnt;                // 112-115
	U32 ulHostH2DRERRCnt;                   // 116-119
	U32 ulHostWriteCRCErrCnt;               // 120-123
	U32 ulHostCmdTimeoutCnt;                // 124-127
	U64 uoReadVerifyFailLBA;                // 128-135
	U32 ulLastCmdIsShutdownCnt;             // 136-139
	U32 ulPowerSavingCnt;                   // 140-143
	U32 ulPowerDropCnt;                     // 144-147
	U32 ulPowerCycleCnt;                    // 148-151
	U32 ulUnsafeShutdownCnt;                // 152-155
	U32 ulLastUnsafeShutdown;               // 156-159
	U8  ubEventInfoReserved[124];           // 160-283

	/* 284 -347 : HW Phy Event Info (64 Byte) */
	U16 uwErrPhyEvent[32]; // 284-347

	/* 348 - 371 : LPM Info (24 Byte) */
	U8  ubDIPMMode;                    // 348
	U8  ubDIPMAutoWakeP2SEnable;       // 349
	U8  ubDIPMAutoWakeP2SThreshold;    // 350
	U8  ubExtremeModeNotSendDIPM;      // 351
	U64 uoAutoIdlePowerSavingCount;    // 352-359
	U64 uoDeviceSleepPowerSavingCount; // 360-367
	U16 uwDIPMThreshold;               // 368-369
	U16 uwAP2SThreshold;               // 370-371

	/* 372 - 461 : Others Info (90 Byte) */
	U8  ubPMICVendor;             // 372
	U8  ubASICType;				  // 373
	U8  ubOtherInfoReserved1[2];  // 374-375
	U32 ulSPORD2HDelayTime;       // 376-379
	U8  ubOtherInfoReserved2[62]; // 380-441

	/* 462 - 509 : ATA Info (48 Byte) */
	U8  ubHPAState;               // 442
	U8  ubSelfTestState;          // 443
	U8  ubSMARTEnable;            // 444
	U8  ubOfflineMode;            // 445
	U16 uwDCOState;               // 446-447
	U64 uoHPASize;                // 448-455
	U64 uoDCOSize;                // 456-463
	U64 uoReadNativeMaxLBA;       // 464-471
	U8  ubSecurityStatus;         // 472
	U8  ubSecurityLastEraseMode;  // 473
	U8  ubOfflineStatus;          // 474
	U8  ubCurrentCompletePercent; // 475
	U8  ubATAInfoReserved1[4];    // 476-479
	U64 uoCurrentSelfTestLBA;     // 480-487
	U64 uoSelfTestFailLBA;        // 488-495
	U32 ulLastReceiveCmdTime;     // 496-499
	U32 ulLPMEscapeTime;          // 500-503
	U16 uwStandbyTimerPeriod;     // 504-505
	U8  ubATAInfoReserved2[4];    // 506-509

	/* 510-511 : Integrity Word (2 Byte) */
	U16 IntegrityWord_sector2_sata; // 510-511

}
Sector_2_SATAInfo_t;
TYPE_SIZE_CHECK(Sector_2_SATAInfo_t, 512);

typedef struct {
	/* 0~7 : apu fatal error count */
	U8 ubDelFailCount;
	U8 ubDBRespErrCount;
	U8 ubFIFOSramErrCount;
	U8 ubLogSramErrCount;
	U8 ubPrePBNotfullCount;
	U8 ubMisPBNotfullCount;
	U8 ubWCVldtCQErrCount;
	U8 ubIDXSramErrCount;

	/* 8~19 : NFE error counter */
	U8 ubCmgrFcqSramErrCount;
	U8 ubCmgrTdqSramErrCount;
	U8 ubCmgrBufSramErrCount;
	U8 ubCmgrZ0InfoBufSramErrCount;
	U8 ubCmgrPnidxBufSramErrCount;
	U8 ubCmgrErrlogBufSramErrCount;
	U8 ubCmgrz0rlbnBufSramErrCount;
	U8 ubCmgrz1rlbnBufSramErrCount;
	U8 ubwchSramErrCount;
	U8 ubrchSramErrCount;
	U8 ubpnSramErrCount;
	U8 ubnlfsmErrCount;

	/* 20~31 : other */
	U8 ubReadE3DCount;
	U8 ubReserved1[11];		// Bytes [21:31]

	/* 32~51 : apu event count */
	U32 ulCmdLogFullCount;
	U32 ulKickErrCount;
	U32 ulSubReadErrCount;
	U32 ulFuaLockCount;
	U32 ulAutoAllocLockCount;

	/* 52~127 : nvme event count */
	U32 ulLinkupRisingCount;
	U32 ulLinkupFallingCount;
	U32 ulLinkdownCount;
	U32 ulnrmlShutdownCount;
	U32 ulabptShutdownCount;
	U32 ulpflrCount;
	U32 ulvflrCount;
	U32 ulCCenCount;
	U32 ulCCdisCount;
	U32 ulNSSRCount;
	U32 ulBmeRisingCount;
	U32 ulBmeFailingCount;
	U32 ulPerstRisingCount;
	U32 ulPerstFailingCount;
	U32 ulBpReadCount;
	U8 ubReserved2[16];		// Bytes [112:127]

	/* 128~139 : Cmd Err counter */
	U32 ulWriteCmdErrCount;
	U32 ulReaduncErrCount;
	U32 ulReadCmdErrCount;

	/* 140~191 : NL Err */
	U32 ulWriteAbnAddrCount;
	U32 uls0wdErrCount;
	U32 uls1wdErrCount;
	U32 ulm0rdErrCount;
	U32 ulm1rdErrCount;
	U32 ulm0axiwErrCount;
	U32 ulm1axiwErrCount;
	U32 ulm0axirErrCount;
	U32 ulm1axirErrCount;
	U32 ulErrCCount;
	U32 ulErrDCount;
	U32 ulErrECount;
	U32 ulErrFCuont;

	/* 192~223 : Other Counter */
	U32 ulTimeOutCount;
	U32 ulWriteUncCmdCount;
	U32 ulReadCRCCount;
	U32 ulnrmlShutdownNotemptyCount;
	U32 ulnrmlShutdownHasIoqCount;
	U8 ubReserved3[12];		// Bytes [212:223]

	/* 224~255 : Other Indo */
	U8 ubE3Dlba[6];
	U16 uwE3Dnisd;
	U32 ulMaxResetTime;
	U32 ulMaxNotResetTime;
	U16 uwPCIEPhySettingVersion;
	U16 uwPCIEPhySettingSubVersion;
	U32 ulBARTime;
	U32 ulAdminTime;
	U32 ulIOTime;
	U32 ulCCENReadyTime;
	U16 uwPS3Time;
	U16 uwPS4Time;
	U8  ubBGSleep0Time;
	U8  ubBGSleep1Time;
	U8  btLPMMeasure : 1;
	U8  btECExcludeBadUnit : 1;
	U8  : 6;
	U8 ubReserved4[243];	// Bytes [267:509]
	U16 IntegrityWord_sector2_nvme;

}
Sector_2_NVMEInfo_t;
TYPE_SIZE_CHECK(Sector_2_NVMEInfo_t, 512);

typedef struct {
	/* 0~383 : Flash Statistic log */
	U32 ulTotalReadRetryFlowExecutionCount[32];
	U32 ulTotalReadScuessCountBeforeDPS[32];
	U32 ulTotalReadScuessCountAfterDPS[32];

	U8 ubReserved[126];		// Bytes [384:509]
	U16 IntegrityWord_sector3;
}
Sector_3_NANDInfo_1_t;
TYPE_SIZE_CHECK(Sector_3_NANDInfo_1_t, 512);

typedef struct {
	/* 0~383 : Flash Statistic log */
	U32 ulTotalRAIDRecoveryFlowExecutionCount[32];
	U32 ulTotalSoftBitRetryCount[32];
	U32 ulTotalHardBitRetryCount[32];

	U8 ubReserved[126];		// Bytes [384:509]
	U16 IntegrityWord_sector4;

}
Sector_4_NANDInfo_2_t;
TYPE_SIZE_CHECK(Sector_4_NANDInfo_2_t, 512);

typedef struct {
	/* 0~383 : Flash Statistic log */
	U8 ubReserved1[384];	// Bytes [0:383]

	/* 384~397 : NAND Pad setting */
	U16 uwFlashClockRead;
	U16 uwFlashClockWrite;
	FLH_ODT_t ubPadODT;
	PAD_VOLTAGE_t ubPadDriveFRDY;
	PAD_VOLTAGE_t ubPadDriveWP;
	PAD_VOLTAGE_t ubPadDriveCE;
	PAD_VOLTAGE_t ubPadDriveALE[4];
	PAD_VOLTAGE_t ubPadDriveCLE[4];
	PADParam_t ubPadDriveWE[4];
	PADParam_t ubPadDriveRE[4];
	PADParam_t ubPadDriveDQS[4];
	PADParam_t ubPadDriveDATA[4];
	FPUACTiming_t FpuACTiming;

	U8  ubReserved[62];
	U16 IntegrityWord_sector5;
}
Sector_5_NANDInfo_3_t;
TYPE_SIZE_CHECK(Sector_5_NANDInfo_3_t, 512);

typedef struct {
	/* 0~511 : DDR Info and Err log */
	U8 ubEfuseBank3CurrentConfig[32];
	U8 ubEfuseBank4CurrentConfig[32];
	U8 ubEfuseBank7CurrentConfig[32];
	U8 ubEfuseBank3ExpectConfig[32];
	U8 ubEfuseBank4ExpectConfig[32];
	U8 ubEfuseBank7ExpectConfig[32];
	U64 uoCustomerFWName;   // Bytes [192:199]
	U8 ubReserved[128];		// Bytes [200:327]
	U16 auwCodeBankSwitchCnt[AOM_MAX_NUM];
	U64 uoRetryVersion;
	U64 uoDecodingVersion;
	U8 ubRetryTableWriteByMP;
	U8 ubIdentifyTableVersion;
	U16 uwSmartTemperature;
	U16 uwInternalTemperature;
	U16 uwMTQDelayValue;
	U16 uwAPUReadDelayValue;
	U16 uwAPUWriteDelayValue;
	U8 ubTTFormulaVersion;
	U8 ubReserved1[65];		// Bytes [445:509]
	U16 IntegrityWord_sector6;
}
Sector_6_EfuseInfo_t;
TYPE_SIZE_CHECK(Sector_6_EfuseInfo_t, 512);

typedef struct {
	U64 uo_1st_OldFWVersion;
	U64 uo_2nd_OldFWVersion;
	U64 uo_3rd_to_62nd_OldFWVersion[60];
	U64 uo_63rd_OldFWVersion;
	U8 ubReserved[6];		// Bytes [504:509]
	U16 IntegrityWord_sector7;
}
Sector_7_FWUpdateHistoryInfo_t;
TYPE_SIZE_CHECK(Sector_7_FWUpdateHistoryInfo_t, 512);

typedef struct {
	Sector_0_CommomInfo_t Sector0_Info;
	Sector_1_CommomInfo_t Sector1_Info;
	union {
		Sector_2_SATAInfo_t Sector2_sata_Info;
		Sector_2_NVMEInfo_t Sector2_nvme_Info;
	};
	Sector_3_NANDInfo_1_t Sector3_Info;
	Sector_4_NANDInfo_2_t Sector4_Info;
	Sector_5_NANDInfo_3_t Sector5_Info;
	Sector_6_EfuseInfo_t Sector6_Info;
	Sector_7_FWUpdateHistoryInfo_t Sector7_Info;
}
System_Info_Table_t;
TYPE_SIZE_CHECK(System_Info_Table_t, 4096);

#if ((USB == HOST_MODE) && RDT_MODE_EN)
AOM_VUC_3 void VUC_RDT_ReadSysInfo(VUC_OPT_HCMD_PTR pCmd);
#endif

#if (USB == HOST_MODE)
AOM_VUC_3 void VUC_ReadSysInfo(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD);
#else /* (USB == HOST_MODE) */
AOM_VUC_3 void VUC_ReadSysInfo(VUC_OPT_HCMD_PTR pCmd);
#endif /* (USB == HOST_MODE) */
AOM_VUC_3 void VUC_GetSystemInfoData(U32 ulCurrentPhysicalMemoryAddr, U8 ubMode);
AOM_ERROR_HANDLE3 void VUC_SaveAssertReason(U32 ulCurrentPhysicalMemoryAddr, U8 ubMode);//this function is RMA LOG Related function must in ATCM

#endif /* _VUC_READSYSINFO_H_ */
