#ifndef _E21_XZIP_REG_H_
#define _E21_XZIP_REG_H_

#include "setup.h"
#include "typedef.h"

#include "mem.h"

//--------------------5011 XZIP wrapper IP Register Define--------------------
#define XZIP_REG_BASE										(XZIP_REG_ADDRESS)

#define R8_XZIP												((volatile U8 *)  XZIP_REG_BASE)
#define R16_XZIP											((volatile U16 *) XZIP_REG_BASE)
#define R32_XZIP											((volatile U32 *) XZIP_REG_BASE)
#define R64_XZIP											((volatile U64 *) XZIP_REG_BASE)

//--------------------5011 XZIP Register Macro--------------------

#define R16_XZIP_FW_CTRL_0									(0x0004 >> 1)
#define R32_XZIP_FW_CTRL_0									(0x0004 >> 2)
#define 	FW_CLK_GAT_EN_SHIFT								(0)
#define 	FW_CLK_GAT_EN_MASK								(BIT_MASK(1))
#define 	FW_WAIT_PCA_SHIFT								(1)
#define 	FW_WAIT_PCA_MASK								(BIT_MASK(1))
#define 	FW_SET_READ_RSLT_MISS_SHIFT						(2)
#define 	FW_SET_READ_RSLT_MISS_MASK						(BIT_MASK(1))
#define 	FW_SET_PGRM_RSLT_MISS_SHIFT						(3)
#define 	FW_SET_PGRM_RSLT_MISS_MASK						(BIT_MASK(1))
#define 	FW_SET_GEN_ENTRY_MISS_SHIFT                     (4)
#define 	FW_SET_GEN_ENTRY_MISS_MASK                      (BIT_MASK(1))
#define		CHANGE_EA_EN_SHIFT								(8)
#define		CHANGE_EA_EN_MASK								(BIT_MASK(1))
#define 	FW_INT_SRAM_PARITY_SHIFT						(9)
#define 	FW_INT_SRAM_PARITY_MASK							(BIT_MASK(1))
#define 	FW_INT_WAIT_PCA_TIMEOUT_SHIFT					(12)
#define 	FW_INT_WAIT_PCA_TIMEOUT_MASK					(BIT_MASK(1))

#define R16_XZIP_FW_RESULT_0								(0x06 >> 1)
#define 	GEN_INST_EQUAL_ZERO_SHIFT						(1)
#define 	GEN_INST_EQUAL_ZERO_MASK						(BIT_MASK(1))
#define		ENTRY_FULL_SHIFT								(2)
#define		ENTRY_FULL_MASK									(BIT_MASK(1))
#define		XZIP_BUSY_ING_SHIFT								(5)
#define		XZIP_BUSY_ING_MASK								(BIT_MASK(1))
#define		XZIP_BUSY_ING_BIT								(BIT5)
#define		NOW_WAITING_PCA_SHIFT							(6)
#define		NOW_WAITING_PCA_MASK							(BIT_MASK(1))
#define 	GEN_INST_DIFF_CNT_SHIFT							(7)
#define 	GEN_INST_DIFF_CNT_MASK							(BIT_MASK(9))

#define R8_XZIP_FW_CTRL_1									(0x0008)
#define 	GEN_INST_CLR_SHIFT								(0)
#define 	GEN_INST_CLR_MASK								(BIT_MASK(1))

#define M_XZIP_TRIGGER_GEN_INST_CLR()						(R8_XZIP[R8_XZIP_FW_CTRL_1] |= (BIT0 & GEN_INST_CLR_MASK) << GEN_INST_CLR_SHIFT)

#define R16_XZIP_PCA_INT_CNT								(0x000A >> 1)
#define		PCA_INTRP_CNT_SHIFT								(0)
#define		PCA_INTRP_CNT_MASK								(BIT_MASK(16))

#define M_SET_XZIP_PCA_INTRP_CNT(x)							(R16_XZIP[R16_XZIP_PCA_INT_CNT] = (x))
#define M_GET_XZIP_PCA_INTRP_CNT()							(R16_XZIP[R16_XZIP_PCA_INT_CNT])

#define R32_XZIP_SRAM_FAIL_STATE							(0xC >> 2)
#define 	SRAM_FAIL_STATE_SHIFT							(0)
#define 	SRAM_FAIL_STATE_MASK							(BIT_MASK(6))
#define 	M_RRC_INFO_SHIFT								(6)
#define 	M_RRC_INFO_MASK									(BIT_MASK(14))
#define		SRCH_XZIP_CMD_EMPTY_SHIFT						(23)
#define 	SRCH_XZIP_CMD_EMPTY_MASK						(BIT_MASK(1))
#define		ENTRY_NUM_SHIFT									(24)
#define     ENTRY_NUM_MASK									(BIT_MASK(8))

#define	M_SET_XZIP_ENTRY_NUM(x)								(R32_XZIP[R32_XZIP_SRAM_FAIL_STATE] = ((x & ENTRY_NUM_MASK) << ENTRY_NUM_SHIFT) | (~(ENTRY_NUM_MASK << ENTRY_NUM_SHIFT)))
#define M_GET_XZIP_ENTRY_NUM()								((R32_XZIP[R32_XZIP_SRAM_FAIL_STATE] >> ENTRY_NUM_SHIFT) & ENTRY_NUM_MASK)
#define M_SET_XZIP_SRCH_XZIP_CMD_EMPTY()					(R32_XZIP[R32_XZIP_SRAM_FAIL_STATE] |= ((BIT0 & SRCH_XZIP_CMD_EMPTY_MASK) << SRCH_XZIP_CMD_EMPTY_SHIFT))
#define M_CLEAR_XZIP_SRCH_XZIP_CMD_EMPTY()					(R32_XZIP[R32_XZIP_SRAM_FAIL_STATE] &= ~((BIT0 & SRCH_XZIP_CMD_EMPTY_MASK) << SRCH_XZIP_CMD_EMPTY_SHIFT))

#define R16_XZIP_FAIL_STATUS								(0x0010 >> 1)
#define 	CPU_LOCK_FAIL_SHIFT								(0)
#define 	CPU_LOCK_FAIL_MASK								(BIT_MASK(1))
#define 	CPU_UNLOCK_FAIL_SHIFT							(1)
#define 	CPU_UNLOCK_FAIL_MASK							(BIT_MASK(1))
#define 	SEARCH_LOCK_FAIL_SHIFT							(2)
#define 	SEARCH_LOCK_FAIL_MASK							(BIT_MASK(1))
#define 	LOCK_CNT_OVER_SHIFT								(3)
#define 	LOCK_CNT_OVER_MASK								(BIT_MASK(1))
#define 	LOCK_CNT_UNDER_SHIFT							(4)
#define 	LOCK_CNT_UNDER_MASK								(BIT_MASK(1))
#define 	LOCK_NON_VALID_SHIFT							(5)
#define 	LOCK_NON_VALID_MASK								(BIT_MASK(1))
#define 	UNLOCK_NON_VALID_SHIFT							(6)
#define 	UNLOCK_NON_VALID_MASK							(BIT_MASK(1))
#define 	SRAM_READ_FAIL_SHIFT							(7)
#define 	SRAM_READ_FAIL_MASK								(BIT_MASK(1))
#define 	BMU_LOCK_FAIL_SHIFT								(8)
#define 	BMU_LOCK_FAIL_MASK								(BIT_MASK(1))
#define 	BMU_UNLOCK_FAIL_SHIFT							(9)
#define 	BMU_UNLOCK_FAIL_MASK							(BIT_MASK(1))
#define 	INSERT_PCA_NOT_VALID_SHIFT						(10)
#define 	INSERT_PCA_NOT_VALID_MASK						(BIT_MASK(1))
#define 	DMAC_RSLT_FAIL_SHIFT							(11)
#define 	DMAC_RSLT_FAIL_MASK								(BIT_MASK(1))

#define	M_SET_XZIP_CPU_LOCK_FAIL()							(R16_XZIP[R16_XZIP_FAIL_STATUS] |= (CPU_LOCK_FAIL_MASK) << CPU_LOCK_FAIL_SHIFT)
#define	M_CLEAR_XZIP_CPU_LOCK_FAIL()						(R16_XZIP[R16_XZIP_FAIL_STATUS] &= ~((CPU_LOCK_FAIL_MASK) << CPU_LOCK_FAIL_SHIFT))
#define	M_SET_XZIP_CPU_UNLOCK_FAIL()						(R16_XZIP[R16_XZIP_FAIL_STATUS] |= (CPU_UNLOCK_FAIL_MASK) << CPU_UNLOCK_FAIL_SHIFT)
#define	M_CLEAR_XZIP_CPU_UNLOCK_FAIL()						(R16_XZIP[R16_XZIP_FAIL_STATUS] &= ~((CPU_UNLOCK_FAIL_MASK) << CPU_UNLOCK_FAIL_SHIFT))
#define	M_SET_XZIP_SEARCH_LOCK_FAIL()						(R16_XZIP[R16_XZIP_FAIL_STATUS] |= (SEARCH_LOCK_FAIL_MASK) << SEARCH_LOCK_FAIL_SHIFT)
#define	M_CLEAR_XZIP_SEARCH_LOCK_FAIL()						(R16_XZIP[R16_XZIP_FAIL_STATUS] &= ~((SEARCH_LOCK_FAIL_MASK) << SEARCH_LOCK_FAIL_SHIFT))
#define	M_SET_XZIP_LOCK_CNT_OVER()						 	(R16_XZIP[R16_XZIP_FAIL_STATUS] |= (LOCK_CNT_OVER_MASK) << LOCK_CNT_OVER_SHIFT)
#define	M_CLEAR_XZIP_LOCK_CNT_OVER()						(R16_XZIP[R16_XZIP_FAIL_STATUS] &= ~((LOCK_CNT_OVER_MASK) << LOCK_CNT_OVER_SHIFT))
#define	M_SET_XZIP_LOCK_CNT_UNDER()							(R16_XZIP[R16_XZIP_FAIL_STATUS] |= (LOCK_CNT_UNDER_MASK) << LOCK_CNT_UNDER_SHIFT)
#define	M_CLEAR_XZIP_LOCK_CNT_UNDER()						(R16_XZIP[R16_XZIP_FAIL_STATUS] &= ~((LOCK_CNT_UNDER_MASK) << LOCK_CNT_UNDER_SHIFT))
#define	M_SET_XZIP_LOCK_NON_VALID()							(R16_XZIP[R16_XZIP_FAIL_STATUS] |= (LOCK_NON_VALID_MASK) << LOCK_NON_VALID_SHIFT)
#define	M_CLEAR_XZIP_LOCK_NON_VALID()						(R16_XZIP[R16_XZIP_FAIL_STATUS] &= ~((LOCK_NON_VALID_MASK) << LOCK_NON_VALID_SHIFT))
#define	M_SET_XZIP_UNLOCK_NON_VALID()						(R16_XZIP[R16_XZIP_FAIL_STATUS] |= (UNLOCK_NON_VALID_MASK) << UNLOCK_NON_VALID_SHIFT)
#define	M_CLEAR_XZIP_UNLOCK_NON_VALID()						(R16_XZIP[R16_XZIP_FAIL_STATUS] &= ~((UNLOCK_NON_VALID_MASK) << UNLOCK_NON_VALID_SHIFT))
#define	M_SET_XZIP_SRAM_READ_FAIL()							(R16_XZIP[R16_XZIP_FAIL_STATUS] |= (SRAM_READ_FAIL_MASK) << SRAM_READ_FAIL_SHIFT)
#define	M_CLEAR_XZIP_SRAM_READ_FAIL()						(R16_XZIP[R16_XZIP_FAIL_STATUS] &= ~(SRAM_READ_FAIL_MASK) << SRAM_READ_FAIL_SHIFT))
#define	M_SET_XZIP_BMU_LOCK_FAIL()							(R16_XZIP[R16_XZIP_FAIL_STATUS] |= (BMU_LOCK_FAIL_MASK) << BMU_LOCK_FAIL_SHIFT)
#define	M_CLEAR_XZIP_BMU_LOCK_FAIL()						(R16_XZIP[R16_XZIP_FAIL_STATUS] &= ~(BMU_LOCK_FAIL_MASK) << BMU_LOCK_FAIL_SHIFT))
#define	M_SET_XZIP_BMU_UNLOCK_FAIL()						(R16_XZIP[R16_XZIP_FAIL_STATUS] |= (BMU_UNLOCK_FAIL_MASK) << BMU_UNLOCK_FAIL_SHIFT)
#define	M_CLEAR_XZIP_BMU_UNLOCK_FAIL()						(R16_XZIP[R16_XZIP_FAIL_STATUS] &= ~(BMU_UNLOCK_FAIL_MASK) << BMU_UNLOCK_FAIL_SHIFT))
#define	M_SET_XZIP_INSERT_PCA_NOT_VALID()					(R16_XZIP[R16_XZIP_FAIL_STATUS] |= (INSERT_PCA_NOT_VALID_MASK) << INSERT_PCA_NOT_VALID_SHIFT)
#define	M_CLEAR_XZIP_INSERT_PCA_NOT_VALID()					(R16_XZIP[R16_XZIP_FAIL_STATUS] &= ~(INSERT_PCA_NOT_VALID_MASK) << INSERT_PCA_NOT_VALID_SHIFT))
#define	M_SET_XZIP_DMAC_RSLT_FAIL()							(R16_XZIP[R16_XZIP_FAIL_STATUS] |= (DMAC_RSLT_FAIL_MASK) << DMAC_RSLT_FAIL_SHIFT)
#define	M_CLEAR_XZIP_DMAC_RSLT_FAIL()						(R16_XZIP[R16_XZIP_FAIL_STATUS] &= ~(DMAC_RSLT_FAIL_MASK) << DMAC_RSLT_FAIL_SHIFT))

#define R32_XZIP_HW_CFG_1									(0x0014 >> 2)
#define		GEN_ENTRY_RULE_SHIFT							(5)
#define		GEN_ENTRY_RULE_MASK								(BIT_MASK(3))
#define		WAIT_PCA_INDEX_SHIFT							(24)
#define		WAIT_PCA_INDEX_MASK								(BIT_MASK(8))

#define M_SET_XZIP_GEN_ENTRY_RULE(x)						(R32_XZIP[R32_XZIP_HW_CFG_1] &= (~(GEN_ENTRY_RULE_MASK << GEN_ENTRY_RULE_SHIFT)));\
															(R32_XZIP[R32_XZIP_HW_CFG_1] |= ((x & GEN_ENTRY_RULE_MASK) << GEN_ENTRY_RULE_SHIFT))
#define M_GET_XZIP_GEN_ENTRY_RULE()							((R32_XZIP[R32_XZIP_HW_CFG_1] >> GEN_ENTRY_RULE_SHIFT) & GEN_ENTRY_RULE_MASK)
#define M_SET_XZIP_WAIT_PCA_INDEX(x)						(R32_XZIP[R32_XZIP_HW_CFG_1] &= (~(WAIT_PCA_INDEX_MASK << WAIT_PCA_INDEX_SHIFT)));\
															(R32_XZIP[R32_XZIP_HW_CFG_1] |= ((x & WAIT_PCA_INDEX_MASK) << WAIT_PCA_INDEX_SHIFT))
#define M_GET_XZIP_WAIT_PCA_INDEX()							((R32_XZIP[R32_XZIP_HW_CFG_1] >> WAIT_PCA_INDEX_SHIFT) & WAIT_PCA_INDEX_MASK)

#define R32_XZIP_MRT_ID_AND_EXE_CMD							(0x0018 >> 2)
#define		EXE_CMD_CRC_SHIFT								(24)
#define 	EXE_CMD_CRC_MASK								(BIT_MASK(1))
#define		EXE_CMD_COP1_SHIFT								(25)
#define		EXE_CMD_COP1_MASK								(BIT_MASK(1))
#define		EXE_CMD_LU_SHIFT								(26)
#define		EXE_CMD_LU_MASK									(BIT_MASK(1))
#define		EXE_CMD_ADJ_SHIFT								(27)
#define		EXE_CMD_ADJ_MASK								(BIT_MASK(1))
#define		EXE_CMD_CNT_DIFF_SHIFT							(28)
#define		EXE_CMD_CNT_DIFF_MASK							(BIT_MASK(1))

#define M_SET_EXE_CMD_CRC()									(R32_XZIP[R32_XZIP_MRT_ID_AND_EXE_CMD] |= (EXE_CMD_CRC_MASK << EXE_CMD_CRC_SHIFT))
#define M_CLEAR_EXE_CMD_CRC()								(R32_XZIP[R32_XZIP_MRT_ID_AND_EXE_CMD] &= ~(EXE_CMD_CRC_MASK << EXE_CMD_CRC_SHIFT))
#define M_SET_EXE_CMD_COP1()								(R32_XZIP[R32_XZIP_MRT_ID_AND_EXE_CMD] |= (EXE_CMD_COP1_MASK << EXE_CMD_COP1_SHIFT))
#define M_CLEAR_EXE_CMD_COP1()								(R32_XZIP[R32_XZIP_MRT_ID_AND_EXE_CMD] &= ~(EXE_CMD_COP1_MASK << EXE_CMD_COP1_SHIFT))
#define M_SET_EXE_CMD_LU()									(R32_XZIP[R32_XZIP_MRT_ID_AND_EXE_CMD] |= (EXE_CMD_LU_MASK << EXE_CMD_LU_SHIFT))
#define M_CLEAR_EXE_CMD_LU()								(R32_XZIP[R32_XZIP_MRT_ID_AND_EXE_CMD] &= ~(EXE_CMD_LU_MASK << EXE_CMD_LU_SHIFT))
#define M_SET_EXE_CMD_ADJ()									(R32_XZIP[R32_XZIP_MRT_ID_AND_EXE_CMD] |= (EXE_CMD_ADJ_MASK << EXE_CMD_ADJ_SHIFT))
#define M_CLEAR_EXE_CMD_ADJ()								(R32_XZIP[R32_XZIP_MRT_ID_AND_EXE_CMD] &= ~(EXE_CMD_ADJ_MASK << EXE_CMD_ADJ_SHIFT))
#define M_SET_EXE_CMD_CNT_DIFF()							(R32_XZIP[R32_XZIP_MRT_ID_AND_EXE_CMD] |= (EXE_CMD_CNT_DIFF_MASK << EXE_CMD_CNT_DIFF_SHIFT))
#define M_CLEAR_EXE_CMD_CNT_DIFF()							(R32_XZIP[R32_XZIP_MRT_ID_AND_EXE_CMD] &= ~(EXE_CMD_CNT_DIFF_MASK << EXE_CMD_CNT_DIFF_SHIFT))

#define M_CHECK_XZIP_EXE_CMD_CRC()							((R32_XZIP[R32_XZIP_MRT_ID_AND_EXE_CMD] >> EXE_CMD_CRC_SHIFT) & EXE_CMD_CRC_MASK)
#define M_CHECK_XZIP_EXE_CMD_COP1()							((R32_XZIP[R32_XZIP_MRT_ID_AND_EXE_CMD] >> EXE_CMD_CRC_SHIFT) & EXE_CMD_CRC_MASK)
#define M_CHECK_XZIP_EXE_CMD_LU()							((R32_XZIP[R32_XZIP_MRT_ID_AND_EXE_CMD] >> EXE_CMD_LU_SHIFT) & EXE_CMD_LU_MASK)
#define M_CHECK_XZIP_EXE_CMD_ADJ()							((R32_XZIP[R32_XZIP_MRT_ID_AND_EXE_CMD] >> EXE_CMD_ADJ_SHIFT) & EXE_CMD_ADJ_MASK)
#define M_CHECK_XZIP_EXE_CMD_CNT_DIFF()						((R32_XZIP[R32_XZIP_MRT_ID_AND_EXE_CMD] >> EXE_CMD_CNT_DIFF_SHIFT) & EXE_CMD_CNT_DIFF_MASK)

#define R8_XZIP_STAGE_CTRL                                  (0x01C)
#define     XZIP_DO_CRC_CMP_SEND_RSLT_EN					(BIT0)  //When finish do_crc_cmp stage, 1: send_req to next stage ; 0: block for debug.
#define     XZIP_DO_DMAC_CMP_SEND_RSLT_EN					(BIT1)  //When finish do_dmac_cmp stage, 1: send_req to next stage ; 0: block for debug.
#define     XZIP_DO_BMU_LOCK_SEND_RSLT_EN					(BIT2)  //When finish do_bmu_stage , 1: send_req to CQ ; 0: block for debug.
#define     XZIP_DO_ADJ_LEN_SEND_RSLT_EN					(BIT3)  //When finish do_adj_len cmd, 1: send_req to CQ ; 0: block for debug.
#define     XZIP_DO_PCA_DIFF_SEND_RSLT_EN					(BIT4)  //When finish pca_diff cmd, 1: send_req to CQ ; 0: block for debug.
#define     XZIP_DO_INST_PCA_SEND_RSLT_EN					(BIT5)  //When finish insert_pca cmd, 1: send_req to CQ ; 0: block for debug.
#define     XZIP_DO_SRCH_PCA_SEND_RSLT_EN					(BIT6)  //When finish search_pca cmd, 1: send_req to CQ ; 0: block for debug.
#define     XZIP_DO_XZIP_LOCK_SEND_RSLT_EN					(BIT7)  //When finish xzip_lock/xzip_unlock cmd, 1: send_req to CQ ; 0: block for debug.

#define R8_XZIP_ADJ_LEN_CNT_OUT								(0x1E)

#define R8_XZIP_DB_LED										(0x1F)

#define R8_XZIP_FPGA_SEL									(0x22)

#define R8_XZIP_DB_EXP_HIT_LED								(0x23)

#define R32_XZIP_VALID_BIT_031_000                          (0x120 >> 2)  //RO
#define     XZIPL_VALID_BIT(N)								(R32_XZIP[R32_XZIP_VALID_BIT_031_000 + ((((N) >> 5) << 2) >> 2)] )    //every 32 entries as 1 group

#define R32_XZIP_USE_BIT_031_000                            (0x140 >> 2)  //RO
#define     XZIPL_USE_BIT(N)								(R32_XZIP[R32_XZIP_USE_BIT_031_000 + ((((N) >> 5) << 2) >> 2)] )      //every 32 entries as 1 group

/*
#define R32_XZIP_RSLT_DATA_M_31_0								(0x0020 >> 2)

#define R32_XZIP_DATA										(0x0024 >> 2)
#define 	XZIP_SRCH_DATA_S_32_SHIFT							(0)
#define 	XZIP_SRCH_DATA_S_32_MASK							(BIT_MASK(1))
#define 	XZIP_RSLT_DATA_M_39_32_SHIFT						(1)
#define 	XZIP_RSLT_DATA_M_39_32_MASK						(BIT_MASK(8))
#define 	XZIP_RSLT_DATA_M_3_0_SHIFT							(9)
#define 	XZIP_RSLT_DATA_M_3_0_MASK							(BIT_MASK(4))
#define 	XZIP_FIRST_HIT_31_24_SHIFT							(24)
#define 	XZIP_FIRST_HIT_31_24_MASK							(BIT_MASK(8))

#define R32_XZIP_COP1_CMD_DATA_S_31_0							(0x0028 >> 2)

#define R32_XZIP_COP1_CMD_DATA_S_63_32						(0x002C >> 2)

#define R32_XZIP_DMAC_RSLT_DATA_S_31_0							(0x0030 >> 2)

#define R32_XZIP_DMAC_RSLT_DATA_S_63_32						(0x0034 >> 2)

#define R32_XZIP_CPU_CMD_DATA_S_31_0							(0x0038 >> 2)

#define R32_XZIP_CPU_CMD_DATA_S_63_32							(0x003C >> 2)

#define R32_XZIP_CMD_DATA_M_31_0								(0x0040 >> 2)

#define R32_XZIP_CMD_DATA_M_63_32							(0x0044 >> 2)

#define R32_XZIP_COP1_RSLT_DATA_M_31_0							(0x0048 >> 2)

#define R32_XZIP_COP1_RSLT_DATA_M_63_32						(0x004C >> 2)

#define R32_XZIP_CPU_RSLT_DATA_M_31_0							(0x0050 >> 2)

#define R32_XZIP_CPU_RSLT_DATA_M_63_32							(0x0054 >> 2)

#define R32_XZIP_DMAC_CMD_DATA_M_31_0						(0x0058 >> 2)

#define R32_XZIP_DMAC_CMD_DATA_M_63_32						(0x005C >> 2)

#define R32_XZIP_ERR_INFO_DATA_M_31_0							(0x0060 >> 2)

#define R32_XZIP_ERR_INFO_DATA_M_63_32							(0x0064 >> 2)

#define R32_XZIP_INTF_VLD_RDY								(0x0068 >> 2)
#define 	XZIP_SEARCH_VALID_S_SHIFT							(0)
#define 	XZIP_SEARCH_VALID_S_MASK							(BIT_MASK(1))
#define 	XZIP_SEARCH_READY_S_SHIFT							(1)
#define 	XZIP_SEARCH_READY_S_MASK							(BIT_MASK(1))
#define 	XZIP_RESULT_VALID_M_SHIFT							(2)
#define 	XZIP_RESULT_VALID_M_MASK							(BIT_MASK(1))
#define 	XZIP_RESULT_READY_M_SHIFT							(3)
#define 	XZIP_RESULT_READY_M_MASK							(BIT_MASK(1))
#define 	COP1_CMD_VALID_S_SHIFT							(4)
#define 	COP1_CMD_VALID_S_MASK							(BIT_MASK(1))
#define 	COP1_CMD_READY_S_SHIFT							(5)
#define 	COP1_CMD_READY_S_MASK							(BIT_MASK(1))
#define 	DMAC_RESULT_VALID_S_SHIFT							(6)
#define 	DMAC_RESULT_VALID_S_MASK							(BIT_MASK(1))
#define 	DMAC_RESULT_READY_S_SHIFT							(7)
#define 	DMAC_RESULT_READY_S_MASK							(BIT_MASK(1))
#define 	CPU_CMD_VALID_S_SHIFT							(8)
#define 	CPU_CMD_VALID_S_MASK								(BIT_MASK(1))
#define 	CPU_CMD_READY_S_SHIFT							(9)
#define 	CPU_CMD_READY_S_MASK							(BIT_MASK(1))
#define 	XZIP_CMD_VALID_M_SHIFT							(10)
#define 	XZIP_CMD_VALID_M_MASK							(BIT_MASK(1))
#define 	XZIP_CMD_READY_M_SHIFT							(11)
#define 	XZIP_CMD_READY_M_MASK							(BIT_MASK(1))
#define 	COP1_RESULT_VALID_M_SHIFT							(12)
#define 	COP1_RESULT_VALID_M_MASK							(BIT_MASK(1))
#define 	COP1_RESULT_READY_M_SHIFT							(13)
#define 	COP1_RESULT_READY_M_MASK							(BIT_MASK(1))
#define 	CPU_RESULT_VALID_M_SHIFT							(14)
#define 	CPU_RESULT_VALID_M_MASK							(BIT_MASK(1))
#define 	CPU_RESULT_READY_M_SHIFT							(15)
#define 	CPU_RESULT_READY_M_MASK							(BIT_MASK(1))
#define 	DMAC_CMD_VALID_M_SHIFT							(16)
#define 	DMAC_CMD_VALID_M_MASK							(BIT_MASK(1))
#define 	DMAC_CMD_READY_M_SHIFT							(17)
#define 	DMAC_CMD_READY_M_MASK							(BIT_MASK(1))
#define 	ERR_INFO_VALID_M_SHIFT							(18)
#define 	ERR_INFO_VALID_M_MASK							(BIT_MASK(1))
#define 	ERR_INFO_READY_M_SHIFT							(19)
#define 	ERR_INFO_READY_M_MASK							(BIT_MASK(1))
#define 	XZIP_RSP_VALID_S_SHIFT							(20)
#define 	XZIP_RSP_VALID_S_MASK								(BIT_MASK(1))
#define 	XZIP_RSP_READY_S_SHIFT							(21)
#define 	XZIP_RSP_READY_S_MASK							(BIT_MASK(1))
#define 	M_REQ_SHIFT									(22)
#define 	M_REQ_MASK									(BIT_MASK(1))
#define 	M_ACK_SHIFT									(23)
#define 	M_ACK_MASK									(BIT_MASK(1))
#define 	M_RDACK_SHIFT									(24)
#define 	M_RDACK_MASK									(BIT_MASK(1))
#define 	M_ADDR_6_0_SHIFT								(25)
#define 	M_ADDR_6_0_MASK								(BIT_MASK(7))

#define R32_XZIP_INTERFACE_RAM_1								(0x006C >> 2)
#define 	M_ADDR_13_7_SHIFT								(0)
#define 	M_ADDR_13_7_MASK								(BIT_MASK(7))

#define R32_XZIP_M_STRB									(0x0070 >> 2)

#define R32_XZIP_M_WDATA_0									(0x0074 >> 2)

#define R32_XZIP_M_WDATA_1									(0x0078 >> 2)

#define R32_XZIP_M_WDATA_2									(0x007C >> 2)

#define R32_XZIP_M_WDATA_3									(0x0080 >> 2)

#define R32_XZIP_M_WDATA_4									(0x0084 >> 2)

#define R32_XZIP_M_WDATA_5									(0x0088 >> 2)

#define R32_XZIP_M_WDATA_6									(0x008C >> 2)

#define R32_XZIP_M_WDATA_7									(0x0090 >> 2)

#define R32_XZIP_M_RDATA_0									(0x0094 >> 2)

#define R32_XZIP_M_RDATA_1									(0x0098 >> 2)

#define R32_XZIP_M_RDATA_2									(0x009C >> 2)

#define R32_XZIP_M_RDATA_3									(0x00A0 >> 2)

#define R32_XZIP_M_RDATA_4									(0x00A4 >> 2)

#define R32_XZIP_M_RDATA_5									(0x00A8 >> 2)

#define R32_XZIP_M_RDATA_6									(0x00AC >> 2)

#define R32_XZIP_M_RDATA_7									(0x0120 >> 2)

#define R32_XZIP_VLD_BIT_1									(0x0124 >> 2)
*/

#define R16_XZIP_PB_ADDR									(0x460>>1)
#define 	XZIP_PB_ADDR_SHIFT								(0)
#define 	XZIP_PB_ADDR_MASK								(BIT_MASK(10))

#endif /*_E21_XZIP_REG_H_*/
