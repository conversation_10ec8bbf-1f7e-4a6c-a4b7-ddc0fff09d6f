/**************************************************************************/
/*                                                                        */
/*           Copyright (c) 2000-2015 by Phison Electronics Corp.          */
/*                                                                        */
/**************************************************************************/

/**************************************************************************/
/*                                                                                                                        */
/*  APPLICATION INTERFACE DEFINITION                       RELEASE                               */
/*                                                                                                                        */
/*    shr_hal_nvme_vuc_api.h                                      GNU C                                        */
/*                                                           X.X                                                         */
/*  AUTHOR                                                                                                           */
/*                                                                                                                        */
/*                                                                                                                        */
/*  DESCRIPTION                                                                                                   */
/*                                                                                                                        */
/*    This file defines the basic Application Interface (API) base on nvme spec.              */
/*                                                                                                                       */
/*                                                                                                                       */
/*  RELEASE HISTORY                                                                                            */
/*                                                                                                                       */
/*    DATE              NAME                      DESCRIPTION                                             */
/*                                                                                                                       */
/*                                                                                                                       */
/**************************************************************************/
//shr_hal_nvme_vuc_api.h
#ifndef _SHR_HAL_NVME_VUC_API_H_
#define _SHR_HAL_NVME_VUC_API_H_

#include "aom/aom_api.h"
#include "nvme_api/nvme/shr_hal_nvme_other.h"

//#ifdef EXTERN
//#undef EXTERN
//#endif

//#ifdef _SHR_HAL_NVME_VUC_API_C_
//#define EXTERN
//#else
//#define EXTERN  extern
//#endif

#if (VUC_MICRON_NICKS_EN)
#define MICRON_VUC_RESPONSE_HEADER_LENGTH	(12)
#define MICRON_VUC_COMMAND_HEADER_LENGTH	(12)

#define MICRON_VUC_RESPONSE_FORMAT_JSON		(1)
#define MICRON_VUC_RESPONSE_FORMAT_BINARY	(2)
#endif /*(VUC_MICRON_NICKS_EN)*/

#if SUPPORT_VUC_VPG_EN
// CFA VPG Operation code
#define VUC_VPG_OPC_GET_INFO           (0x0F)  // Get VPG Management information.
#define VUC_VPG_OPC_START_REC          (0x01)  // Start VPG recording. Change to VPG State.
#define VUC_VPG_OPC_TERMINATE_REC      (0x02)  // Terminate VPG recording. Change to Non-VPG state
#define VUC_VPG_OPC_OPEN_STREAM        (0x03)  // Allocate AU for VPG recording.
#define VUC_VPG_OPC_SUSPEND_REC        (0x04)  // Suspend VPG recording all opened AUs.
#define VUC_VPG_OPC_RESUME_REC         (0x05)  // Resume VPG recording all suspended AUs.

//CFA VPG statuse
#define VPG_STATE_TERMINATE            (0)
#define VPG_STATE_ENABLE               (1)
#define VPG_STATE_SUSPEND              (2)
//CFA stream ID status
#define VPG_STREAM_STATE_FREE          (0)
#define VPG_STREAM_STATE_OPEN          (1)

//Max stream openable number
#define VUC_VPG_MAX_STREAM_NUBMER          (2)

//size
//#define VUC_VPG_AU_SIZE_SC             (0x40000) //128M
#define VUC_VPG_AU_SIZE_SC             (0xFFFF) //32M (wait sony new spec
#define VUC_VPG_GU_SIZE_SC             (0x10000) ///32M
#define VUC_VPG_RU_SIZE_SC             (0x1000) ////2M

#define VUC_VPG_OFS_AU                    (2)     //OFS AU unit
#define VUC_VPG_OFS_LBA                    (VUC_VPG_AU_SIZE_SC *VUC_VPG_OFS_AU)     //OFS start LBA address
//continuous allocate au count
#define VUC_VPG_STREAM_AU_MAX          (0x2)     // one stream max AU count 


#define  VUC_PS0PowerSet                  (0x19) //W
#define  VUC_PS1PowerSet                  (0x12) //W
#define  VUC_PS2PowerSet                  (0xa)//W     //If not support VPG in this power state, set to 0000h	

/*
 *  The data structure of VPG stream ID info
 */
typedef struct vpg_stream_id_struct    VPG_STREAM_ID_STRUCT_T, *VPG_STREAM_ID_STRUCT_PTR;

struct vpg_stream_id_struct {    /* 16B */
	U64    uoStartLBA;
	U32    ulWrittenLBA;
	U8     ubVpgStreamStatus;
	U8     ubOpenAUCount;
	U16    uwReserved;
};

/*
 *  The data structure of VPG Management Information
 */
typedef struct vpg_info_struct    VPG_INFO_STRUCT_T, *VPG_INFO_STRUCT_PTR;


struct vpg_info_struct {    /* 4096B */

	// offset 0
	U16    uwCFASig;
	U16    uwVPGLevel;                           //If CFexpress memory card support VPG400, this value to be 0190h Undefined level/value shall not be used
	U16    uwPS0PowerSet;
	U16    uwPS1PowerSet;                       //If not support VPG in this power state, set to 0000h
	U16    uwPS2PowerSet;                       //If not support VPG in this power state, set to 0000h
	U16    uwPS3PowerSet;                       //If not support VPG in this power state, set to 0000h
	U16    uwPS4PowerSet;                       //If not support VPG in this power state, set to 0000h
	U16    uwPS5PowerSet;                       //If not support VPG in this power state, set to 0000h
	U16    uwPS6PowerSet;                       //If not support VPG in this power state, set to 0000h
	U16    uwPS7PowerSet;                       //If not support VPG in this power state, set to 0000h
	U16    uwPS8PowerSet;                       //If not support VPG in this power state, set to 0000h
	U16    uwPS9PowerSet;                       //If not support VPG in this power state, set to 0000h
	U16    uwPS10PowerSet;                      //If not support VPG in this power state, set to 0000h
	U16    uwPS11PowerSet;                      //If not support VPG in this power state, set to 0000h
	U16    uwPS12PowerSet;                      //If not support VPG in this power state, set to 0000h
	U16    uwPS13PowerSet;                      //If not support VPG in this power state, set to 0000h
	U16    uwPS14PowerSet;                      //If not support VPG in this power state, set to 0000h
	U16    uwPS15PowerSet;                      //If not support VPG in this power state, set to 0000h
	U16    uwPS16PowerSet;                      //If not support VPG in this power state, set to 0000h
	U16    uwPS17PowerSet;                      //If not support VPG in this power state, set to 0000h
	U16    uwPS18PowerSet;                      //If not support VPG in this power state, set to 0000h
	U16    uwPS19PowerSet;                      //If not support VPG in this power state, set to 0000h
	U16    uwPS20PowerSet;                      //If not support VPG in this power state, set to 0000h
	U16    uwPS21PowerSet;                      //If not support VPG in this power state, set to 0000h
	U16    uwPS22PowerSet;                      //If not support VPG in this power state, set to 0000h
	U16    uwPS23PowerSet;                      //If not support VPG in this power state, set to 0000h
	U16    uwPS24PowerSet;                      //If not support VPG in this power state, set to 0000h
	U16    uwPS25PowerSet;                      //If not support VPG in this power state, set to 0000h
	U16    uwPS26PowerSet;                      //If not support VPG in this power state, set to 0000h
	U16    uwPS27PowerSet;                      //If not support VPG in this power state, set to 0000h
	U16    uwPS28PowerSet;                      //If not support VPG in this power state, set to 0000h
	U16    uwPS29PowerSet;                      //If not support VPG in this power state, set to 0000h
	U16    uwPS30PowerSet;                      //If not support VPG in this power state, set to 0000h
	U16    uwPS31PowerSet;                      //If not support VPG in this power state, set to 0000h

	// offset 68
	U16    uwVpgStatus;                          //VPG status (0h:terminate, 1h:enable, 2h:suspend) offset:68-69
	U16    uwMaxStream;                          //max openable number of VPG AU. 2 or more      offset:70-71
	U16    uwCurrentStream;                      //Current Stream. Opened number of VPG AU.     offset:72-73
	U16    uwAUSize;                             //From 32MB to 512 MB sector sector unit.               offset:74-75
	U32    ulReserved1;                         // offset:76-79
	U32    ulGUSize;                             //10000h fixed. (Sector Unit)  offset:80-83
	U16    wuRUSize;                             //1000h fixed. (Sector Unit)  offset:84-85
	U16    wuOFS;                                //offset:86-87
	VPG_STREAM_ID_STRUCT_T     VPGStreamID[VUC_VPG_MAX_STREAM_NUBMER];      //Stream ID status max is 16byte*32 group
	U8     ubReserved2[4008 - (VUC_VPG_MAX_STREAM_NUBMER * 16)];
};
LIB_SIZE_CHECK(VPG_INFO_STRUCT_T, SIZE_4KB);
#endif /* SUPPORT_VUC_VPG_EN */

#if SUPPORT_VUC_VPG_EN
typedef struct vpg_save_info_struct    VPG_SAVE_INFO_STRUCT_T, *VPG_SAVE_INFO_STRUCT_PTR;

struct vpg_save_info_struct {    /* 4096B */

	// offset 0
	U16    uwVpgStatus;                          //VPG status (0h:terminate, 1h:enable, 2h:suspend) offset:68-69
	U16    uwCurrentStream;                      //Current Stream. Opened number of VPG AU.     offset:72-73
	VPG_STREAM_ID_STRUCT_T     VPGStreamID[VUC_VPG_MAX_STREAM_NUBMER];      //Stream ID status max is 16byte*32 group

};

//EXTERN void nvme_vuc_vpg (OPT_HCMD_PTR pCmd);
AOM_NRW_2 extern void vpg_get_info_flow (VPG_INFO_STRUCT_PTR ulPhyAddr);
AOM_NRW_2 extern void vpg_start_rec_flow(OPT_HCMD_PTR pCmd);
AOM_NRW_2 extern void vpg_terminate_flow(OPT_HCMD_PTR pCmd);
AOM_NRW_2 extern void vpg_open_stream_flow(U32 ulStartLBA, U32 ulAUCount);
AOM_NRW_2 extern void vpg_suspend_rec_flow(OPT_HCMD_PTR pCmd);
AOM_NRW_2 extern void vpg_resume_flow(OPT_HCMD_PTR pCmd);
AOM_NRW_2 extern void vuc_vpg_open_stream_check_status (OPT_HCMD_PTR pCmd);
AOM_NRW_2 extern void vuc_vpg_check_status (OPT_HCMD_PTR pCmd);
AOM_NRW_2 extern void vpg_get_info_api (OPT_HCMD_PTR pCmd);
AOM_NRW_2 extern void vpg_start_rec_api(OPT_HCMD_PTR pCmd);
AOM_NRW_2 extern void vpg_terminate_rec_api(OPT_HCMD_PTR pCmd);
AOM_NRW_2 extern void vpg_open_stream_api(OPT_HCMD_PTR pCmd);
AOM_NRW_2 extern void vpg_suspend_rec_api(OPT_HCMD_PTR pCmd);
AOM_NRW_2 extern void vpg_resume_rec_api(OPT_HCMD_PTR pCmd);
AOM_NRW_2 extern void nvme_vuc_vpg (OPT_HCMD_PTR pCmd);
#endif /* SUPPORT_VUC_VPG_EN */
AOM_NRW_2 extern void nvme_vendor_power (OPT_HCMD_PTR pCmd);
AOM_VUC extern void nvme_vuc_dispatch(OPT_HCMD_PTR pCmd);
AOM_VUC_KINGSTON void nvme_kingston_vuc_dispatch(OPT_HCMD_PTR pCmd);
AOM_VUC_3 void nvme_micron_vuc_dispatch(OPT_HCMD_PTR pCmd);
AOM_NRW_2 U8 NVMEVUCCheckWriteProtect(OPT_HCMD_PTR pCmd);
AOM_NRW_2 extern void nvme_vucsp_record_sq(OPT_HCMD_PTR_t pCmd);
AOM_VUC extern void nvme_vucsp_get_sq(OPT_HCMD_PTR_t pCmd);
AOM_VUC extern void nvme_vucsp_get_cq(OPT_HCMD_PTR_t pCmd);
AOM_NRW_2 extern void nvme_vucsp_fetch_cq(OPT_HCMD_PTR_t pCmd);
AOM_NRW_2 extern void nvme_sec_send_vendor_api(OPT_HCMD_PTR pCmd);
AOM_NRW_2 extern void nvme_sec_receive_vendor_api(OPT_HCMD_PTR pCmd);

#endif /* _SHR_HAL_NVME_VUC_API_H_ */
