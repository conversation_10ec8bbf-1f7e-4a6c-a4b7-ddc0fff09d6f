#ifndef S17_RAIDECC_REG_H_
#define S17_RAIDECC_REG_H_

#include "typedef.h"
#include "mem.h"
#include "symbol.h"

#define RAIDECC_REG_BASE (BVCI_RAIDECC_REG_ADDRESS)

#define R8_RAIDECC      ((volatile U8 *) RAIDECC_REG_BASE)
#define R16_RAIDECC     ((volatile U16 *) RAIDECC_REG_BASE)
#define R32_RAIDECC     ((volatile U32 *) RAIDECC_REG_BASE)
#define R64_RAIDECC     ((volatile U64 *) RAIDECC_REG_BASE)

#define R32_RAIDECC_MODE_0					(0x0000 >> 2)
#define 	CPU_SEL_SHIFT						    (24)
#define		CPU_SEL_MASK						    (BIT_MASK(3))
#define		RAIDECC_COR_NORMAL_MODE_SHIFT		    (20)
#define	    RAIDECC_COR_NORMAL_MODE_MASK			(BIT_MASK(1))
#define	    RAIDECC_BPT_SEL_SHIFT					(16)
#define	    RAIDECC_BPT_SEL_MASK					(BIT_MASK(1))
#define	    RAIDECC_BMU_PRE_LOAD_EN_SHIFT			(14)
#define	    RAIDECC_BMU_PRE_LOAD_EN_MASK			(BIT_MASK(1))
#define 		SET_RAIDECC_BMU_PRE_LOAD_ENABLE     (0x1 << RAIDECC_BMU_PRE_LOAD_EN_SHIFT)
#define		RAIDECC_BMU_FORCE_LOAD_EN_SHIFT			(13)
#define		RAIDECC_BMU_FORCE_LOAD_EN_MASK			(BIT_MASK(1))
#define		RAIDECC_BMU_FW_FREE_SHIFT				(12)
#define		RAIDECC_BMU_FW_FREE_MASK				(BIT_MASK(1))
#define		RAIDECC_BMU_EN_SHIFT					(11)
#define		RAIDECC_BMU_EN_MASK						(BIT_MASK(1))
#define		RAIDECC_32B_EN_SHIFT					(10)
#define		RAIDECC_32B_EN_MASK						(BIT_MASK(1))
#define		RAIDECC_PAGESIZE_SHIFT					(8)
#define		RAIDECC_PAGESIZE_MASK					(BIT_MASK(2))
#define			SET_RAIDECC_4KB_PAGE             	(0x0 << RAIDECC_PAGESIZE_SHIFT)
#define 		SET_RAIDECC_8KB_PAGE             	(0x1 << RAIDECC_PAGESIZE_SHIFT)
#define 		SET_RAIDECC_12KB_PAGE            	(0x2 << RAIDECC_PAGESIZE_SHIFT)
#define 		SET_RAIDECC_16KB_PAGE            	(0x3 << RAIDECC_PAGESIZE_SHIFT)
#define		RAIDECC_PEC_CHK_EN_SHIFT				(7)
#define		RAIDECC_PEC_CHK_EN_MASK					(BIT_MASK(1))
#define		RAIDECC_TAG_CHK_VLD_AUTO_CLR_SHIFT		(6)
#define		RAIDECC_TAG_CHK_VLD_AUTO_CLR_MASK		(BIT_MASK(1))
#define		RAIDECC_PBUF_INIT_SHIFT					(2)
#define		RAIDECC_PBUF_INIT_MASK					(BIT_MASK(1))
#define		RAIDECC_PBUF_LOAD_SHIFT					(1)
#define		RAIDECC_PBUF_LOAD_MASK					(BIT_MASK(1))



#define R32_RAIDECC_EX_PB_SEL				(0x0094 >> 2)
#define	RAIDECC_IN_PB_VLD_SHIFT						(26)
#define	RAIDECC_IN_PB_VLD_MASK						(BIT_MASK(1))
#define	RAIDECC_IN_PB_NUM_SHIFT						(24)
#define	RAIDECC_IN_PB_NUM_MASK						(BIT_MASK(2))
#define	RAIDECC_EX_PB_NUM_SHIFT						(16)
#define	RAIDECC_EX_PB_NUM_MASK						(BIT_MASK(5))
#define	RAIDECC_EX_PB_TAG_SHIFT						(0)
#define	RAIDECC_EX_PB_TAG_MASK						(BIT_MASK(9))//as tag num is over 256, related variables need to revised from U8 to U16
#endif /* S17_RAIDECC_REG_H_ */
