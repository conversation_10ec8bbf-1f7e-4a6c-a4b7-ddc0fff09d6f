/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include <string.h>
#include "hal/fip/fip_api.h"
#include "hal/fip/fip.h"
#include "retry/retry_api.h"
#include "debug/debug.h"

/*
 * ---------------------------------------------------------------------------------------------------
 *   public
 * ---------------------------------------------------------------------------------------------------
 */

#if ((FW_CATEGORY_FLASH == FLASH_V7_TLC)||(FW_CATEGORY_FLASH == FLASH_V6_TLC)||(FW_CATEGORY_FLASH == FLASH_V8_TLC) || (FW_CATEGORY_FLASH == FLASH_V7_QLC) || (FW_CATEGORY_FLASH == FLASH_V5_TLC))//Reip Porting 3D-V7 QLC Add//Jeffrey Porting 3D-V8 TLC Add
void FipGetUniqueIDHynix(U8 ubChannel, U8 ubFlashCE, U8 ubDie, U8 *pubBufAddr)
{
	U16 uwUniqueIDSize = 256;
	U16 uwDatatmp;
	U8 *pubUniqueID = (U8 *)(pubBufAddr);
	REG32 *pFlaReg = (REG32 *)R32_FCTL_CH[ubChannel];
	U16 uwi;

	memset((void *)(pubBufAddr), 0x00, DEF_512B);
	FlaResetAllCE(0xFC);
	FlaCEControl(ubChannel, ubFlashCE, ENABLE);

	pFlaReg[R32_FCTL_PIO_CMD] = 0x02;
	pFlaReg[R32_FCTL_PIO_CMD] = 0x19;
	pFlaReg[R32_FCTL_PIO_CMD] = 0xDA;
	pFlaReg[R32_FCTL_PIO_CMD] = 0x00;

	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
#if (FW_CATEGORY_FLASH == FLASH_V7_TLC)
	pFlaReg[R32_FCTL_PIO_ADR] = 0x99;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x13;
#elif (FW_CATEGORY_FLASH == FLASH_V6_TLC)
	pFlaReg[R32_FCTL_PIO_ADR] = 0xF9;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x01;
#elif (FW_CATEGORY_FLASH == FLASH_V7_QLC)	//Reip Porting 3D-V7 QLC Add
	pFlaReg[R32_FCTL_PIO_ADR] = 0x01;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x12;
#elif (FW_CATEGORY_FLASH == FLASH_V8_TLC)//Jeffrey Porting 3D V8 TLC Add
	pFlaReg[R32_FCTL_PIO_ADR] = 0x51;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x12;
#elif (FW_CATEGORY_FLASH == FLASH_V5_TLC)//Jeffrey Porting 3D V5 TLC Add
	pFlaReg[R32_FCTL_PIO_ADR] = 0x71;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x01;
#endif
	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
	pFlaReg[R32_FCTL_PIO_CMD] = 0x30;
	if ( FAIL == FlaWaitFlashReady(GERNAL_TIMEOUT_THRESHOLD)) {
		gFlhEnvMP.ulFailedCEBMP |= BIT(gubLogicalCEMapPhysicalCE[ubChannel][ubFlashCE]);
#if (!BURNER_MODE_EN)
		M_FW_CRITICAL_ASSERT(ASSERT_HAL_FIP_0x06BB, FALSE);//This means check status timeout , something wrong
#endif /*(!BURNER_MODE_EN)*/
	}
	//wait 1ms
	M_RTT_IDLE_MS(1);
	pFlaReg[R32_FCTL_HS_MODE] |= SET_DAT_DEFAULT;
	if (gFlhEnv.ubCurrentInterface == LEGACY_INTERFACE) {
		for (uwi = 0; uwi < uwUniqueIDSize; uwi++) {
			pubUniqueID[uwi] = pFlaReg[R32_FCTL_PIO_DAT];
		}
	}
	else if ((gFlhEnv.ubCurrentInterface == TOGGLE_INTERFACE) || (gFlhEnv.ubCurrentInterface == TOGGLE2_INTERFACE)) {
		for (uwi = 0; uwi < uwUniqueIDSize; uwi += 2) {
			pFlaReg[R32_FCTL_HS_MODE] &= CLR_DAT_MID;
			if (uwi == 0) {
				pFlaReg[R32_FCTL_HS_MODE] |= PIO_DAT_FIRST_BIT;
			}
			else if (uwi == (uwUniqueIDSize - 2)) {
				pFlaReg[R32_FCTL_HS_MODE] |= PIO_DAT_LAST_BIT;
			}
			uwDatatmp = (U16)pFlaReg[R32_FCTL_PIO_DAT];
			pubUniqueID[uwi] = (U8)(uwDatatmp & 0xFF);
			pubUniqueID[uwi + 1] = (U8)((uwDatatmp >> 8) & 0xFF);
		}
	}
	pFlaReg[R32_FCTL_HS_MODE] |= SET_DAT_DEFAULT;
	FlaCEControl(ubChannel, ubFlashCE, DISABLE);
	FlaResetAllCE(0xFC);

	//UartPrintf("\nChannel%d - CE%d - UNIQUE ID", ubChannel, ubFlashCE);
	//M_UART(UART_RDT_DBG_LEVEL, "\nChannel%d - CE%d - UNIQUE ID", ubChannel, ubFlashCE);
	//for (uwi = 0; uwi < 16; uwi++) {
	//UartPrintf(" %b", pubUniqueID[uwi]);
	//	M_UART(UART_RDT_DBG_LEVEL, " %b", pubUniqueID[uwi]);
	//}
}
#endif
