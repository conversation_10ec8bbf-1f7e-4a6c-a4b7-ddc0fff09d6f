#ifndef SRC_FW_LPM3LOADER_H_
#define SRC_FW_LPM3LOADER_H_

#include "env.h"
#include "setup.h"
#include "typedef.h"
#include "hal/sys/api/pmu/pmu_api.h"
#include "burner/IDpage.h"
#include "burner/codepointer.h"
#include "common/boot_api.h"

//Loader Use temp
#define LPM3_FPU_SIZE				(0x0040) //Unit: 2Byte
// -----------------------------------------------------------------
// FPU definitions.
// -----------------------------------------------------------------
// Read
#if MICRON_FSP_EN
#if ((IM_B47R) || IM_B37R || (IM_N48R))
#define LPM3_FPU_PTR_C3B_C00_A6_C30										(0x0000)
#else    /* ((IM_B47R) || (IM_N48R)) */
#define LPM3_FPU_PTR_CDA_C00_A5_C30										(0x0000)
#endif   /* ((IM_B47R) || (IM_N48R)) */
#else
#define LPM3_FPU_PTR_CA2_C00_A5_C30										(0x0000)
#endif
#define LPM3_FPU_PTR_C00_A5_C30											(0x0002)
// Read DMA
#define LPM3_FPU_PTR_C05_A5_CE0_DR										(0x0010)
#define LPM3_FPU_PTR_C05_A2_CE0_DR										(0x0020)
// Poll Status
#define LPM3_FPU_PTR_C70_POL_MK40_C00									(0x0030)

extern U16 LPM3FPUContent[LPM3_FPU_SIZE];

//==========================================================
//  TIMEOUT threshold Define  (Unit:ms)
//==========================================================
#if (!ASIC)
#define GENERAL_TIMEOUT_THRESHOLD	(100 * RTT_PER_MS)
#else
#define GENERAL_TIMEOUT_THRESHOLD	(50 * RTT_PER_MS)
#endif

#define LPM3_ALL_CE0_PER_CHANNEL_EN (0x01010101)
#define LPM3_ALL_CE_DIS             (0x00000000)

#define LPM3_DEFAULT_DMAC_TAGID		(0x57)  //use for init cop1 SRAM

typedef struct {
	U16 uwPage;										//page index to read
	U16 uwBlock;									//block index to read

	U8  ubCH;										//operation channel index
	U8  ubCE;										//operation ce index for ubCH
	U8  ubFrameCnt;									//number of DMA frame to read, 0 means no operation
	U8  ubRsv;

	U32 ulBufBase;									//read buffer base

	union {
		U32 ulAll;
		struct {
			U32	btIsReadError: 1;					// 1 read operation error, skip DMA read operation
			U32	btIsErasedPage: 1;					// 1 is erase page
			U32	btIsUNC: 1;							// 1 is UNC
			U32	btIsCRC: 1;							// 1 is CRC
			U32	btIsTimeout: 1;
			U32	ubRsv: 3;
			U32 ulRsv: 24;
		} Status;
	} BitMap;
} ParallelReadStruct; //16Byte

#if LPM_USE_PD0_DBUF_RAM_PD_256KB_FOR_LPM3
#define M_LPM_GET_BACKUP_RTT0_VALUE()		(((U64 *)LPM_RTT0_BACKUP_TEMP_ADDR)[0])
#else /* LPM_USE_PD0_DBUF_RAM_PD_256KB_FOR_LPM3 */
#define M_LPM_GET_BACKUP_RTT0_VALUE()	(((U64 *)LPM_RTT0_BACKUP_ADDR)[0])
#endif /* LPM_USE_PD0_DBUF_RAM_PD_256KB_FOR_LPM3 */

//******************************************************************//
//						Function									//
//******************************************************************//
U8 LPM3HandleFlashCorePowerOffTime(void);
void LPM3LoaderMain(void);
void LPM3SwitchMuxToI2CSlave(void);

extern volatile U32 gulLPMLoader;

#endif /* SRC_FW_LPM3LOADER_H_ */
