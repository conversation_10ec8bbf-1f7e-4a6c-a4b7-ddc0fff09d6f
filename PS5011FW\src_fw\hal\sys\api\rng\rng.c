#include "rng_api.h"
#include "rng.h"

U32 RngGetRandomValue(void)
{
#if PS5017_EN
	do {
		// Start random
		M_CLEAR_RNG_ALL_CHANNEL_DONE_STATUS();

		// Wait all done
		while (0 == M_GET_RNG_STATUS_ALL_DONE()) ;
		// Wait check done
		while (0 == M_GET_RNG_STATUS_CHECK_DONE()) ;

	} while (0 != M_GET_RNG_STATUS_CHECK_ERROR());
#else /* PS5017_EN */
	// Wait
	while (0 == M_GET_RNG_STATUS()) ;
#endif /* PS5017_EN */

	return (U32)M_GET_RNG_VALUE();
}
