#include "itc.h"
#include "itc_api.h"
#include "hal/sys/api/angc/angc_api.h"

#if (PS5013_EN)
U8 gubInterruptPriority[ITC_MAX_INTERRUPT_GROUP] = {
	ITC_GROUP_ID_0_SYSTEM_0,		ITC_GROUP_ID_1_SYSTEM_1,	ITC_GROUP_ID_2_TIMER,	ITC_GROUP_ID_3_PARITY_ERROR,
	ITC_GROUP_ID_4_NVME_UFS_APU,	ITC_GROUP_ID_5_PCIE,		ITC_GROUP_ID_8_MR,		ITC_GROUP_ID_7_BMU,
	ITC_GROUP_ID_6_COP1,			ITC_GROUP_ID_9_DMAC,		ITC_GROUP_ID_10_COP0,	ITC_GROUP_ID_11_PIC,
	ITC_GROUP_ID_12_D2H,			ITC_GROUP_ID_13_AHB,		ITC_GROUP_ID_14_RS,		ITC_GROUP_ID_15_TDBG,
	ITC_GROUP_ID_16_CPU,			ITC_GROUP_ID_17_ITC,		ITC_GROUP_ID_18_UNI,	ITC_GROUP_ID_19_AXI,
	ITC_GROUP_ID_20_COP0_MR
};
#endif /* (PS5013_EN) */

//-------------------------------------------------------------------
// Function : InterruptMaskAllGroup
// Description :
// Input  : N/A
// return : N/A
//-------------------------------------------------------------------
void InterruptMaskAllGroup(void)
{
	// Set FIQ Mask to 1 to disable all interrupt
	// MASK_ITC_FIQ_ALL;
	M_ITC_MASK_FIQ_ALL();

	// Set IRQ Mask to 1 to disable all interrupt
	M_ITC_MASK_IRQ_ALL();
}

//-------------------------------------------------------------------
// Function : InterruptInit
// Description :
// Input  : N/A
// return : N/A
//-------------------------------------------------------------------
void InterruptInit(void)
{
	U32 ulInterruptEnableBitmap = 0;
	// Mask to 1 to disable all interrupt
	InterruptMaskAllGroup();

#if (PS5021_EN)
	// Clear VDT status before enable VDT interrupt
	U32 ulVDTStatus = M_ANGC_GET_VDT_VOUT_STATUS();
	M_ANGC_CLR_VDT_VOUT_STATUS(ulVDTStatus);
	M_ITC_CLEAR_IRQ_INT(INTERRUPT_EVENT_VDT);
#endif /* (PS5021_EN) */

#if (PS5013_EN)
	// Set Interrupt Priority
	for (U8 ubInterruptCnt = 0; ubInterruptCnt < ITC_MAX_INTERRUPT_GROUP; ++ubInterruptCnt) {
		M_ITC_SET_SYS1_INT_PRIORITY_CFG(ubInterruptCnt, gubInterruptPriority[ubInterruptCnt]);
	}

	// Enable Interrupt
	ulInterruptEnableBitmap = ITC_GROUP_ID_3_PARITY_ERROR_BIT | ITC_GROUP_ID_7_BMU_BIT |
		ITC_GROUP_ID_9_DMAC_BIT | ITC_GROUP_ID_12_D2H_BIT |
		ITC_GROUP_ID_2_TIMER_BIT | ITC_GROUP_ID_6_COP1_BIT |
		ITC_GROUP_ID_19_AXI_BIT | ITC_GROUP_ID_1_SYSTEM_1_BIT;
	if (BMU_BUG_WORKAROUND_EN) {
		ulInterruptEnableBitmap |= ITC_GROUP_ID_8_MR_BIT;
	}
	if (VDT_USE_ISR_EN) {
		ulInterruptEnableBitmap |= ITC_GROUP_ID_0_SYSTEM_0_BIT;
	}
#elif (PS5017_EN || PS5021_EN)
	ulInterruptEnableBitmap = ITC_COLLECT_WITHOUT_HOST;
#endif /* (PS5013_EN) */

	InterruptEnable(ulInterruptEnableBitmap);
}

//-------------------------------------------------------------------
// Function : InterruptEnable
// Description :
// Input  : U32 ulIntGroupIdBitMap
// return : N/A
//-------------------------------------------------------------------

void InterruptEnable(U32 ulIntGroupIdBitMap)
{
	U32 ulFIQBitmap = 0;
#if (PS5013_EN)
	ulFIQBitmap = ITC_GROUP_ID_2_TIMER_BIT;
#elif (PS5017_EN)
#if (UART_VUC_MODE_EN)
	ulFIQBitmap = INTERRUPT_EVENT_SYS_2  | INTERRUPT_EVENT_PIC_UART;
#else
	ulFIQBitmap = INTERRUPT_EVENT_SYS_2;
#endif
#elif (PS5021_EN)
	ulFIQBitmap = INTERRUPT_EVENT_US_BASED;
#endif /* (PS5013_EN) */

	ulFIQBitmap &= ulIntGroupIdBitMap;
	if (ulFIQBitmap) {
		M_ITC_UNMASK_FIQ_BITMAP(ulFIQBitmap);
		ulIntGroupIdBitMap &= ~ulFIQBitmap;
	}
	M_ITC_UNMASK_IRQ_BITMAP(ulIntGroupIdBitMap);
}

//-------------------------------------------------------------------
// Function : InterruptDisable
// Description :
// Input  : U32 ulIntGroupIdBitMap
// return : N/A
//-------------------------------------------------------------------
void InterruptDisable(U32 ulIntGroupIdBitMap)
{
	U32 ulFIQBitmap = 0;
#if (PS5013_EN)
	ulFIQBitmap = ITC_GROUP_ID_2_TIMER_BIT;
#elif (PS5017_EN)
	ulFIQBitmap = INTERRUPT_EVENT_SYS_2;
#elif (PS5021_EN)
	ulFIQBitmap = INTERRUPT_EVENT_US_BASED;
#endif /* (PS5013_EN) */

	ulFIQBitmap &= ulIntGroupIdBitMap;
	if (ulFIQBitmap) {
		M_ITC_MASK_FIQ_BITMAP(ulFIQBitmap);
		ulIntGroupIdBitMap &= ~ulFIQBitmap;
	}
	M_ITC_MASK_IRQ_BITMAP(ulIntGroupIdBitMap);
}

