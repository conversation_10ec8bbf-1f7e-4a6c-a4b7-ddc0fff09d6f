#include "retry/retry.h"
#include "retry/retry_api.h"
#include "retry/retry_hb.h"
#include "retry_kioxia_bics5_tlc_S17_neutral_hb.h"
#include "hal/pic/uart/uart_api.h"
#include "retry/retry_version_api.h" // Retry Version
#include "table/initinfo_vt/initinfo_vt.h" // VT
#include "ftl/ftl_api.h" // FW Timer
#include "hal/fip/fpu.h"

#if (PS5017_EN && (FLASH_BICS5TLC == FW_CATEGORY_FLASH) && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER))
/*
 * ----------------------------------------------------------------------------
 *   Definitions
 * ----------------------------------------------------------------------------
 */

/*
 * ----------------------------------------------------------------------------
 *   Enum
 * ----------------------------------------------------------------------------
 */

/*
 * ----------------------------------------------------------------------------
 *   Data Types
 * ----------------------------------------------------------------------------
 */

/*
 * ----------------------------------------------------------------------------
 *   Macros
 * ----------------------------------------------------------------------------
 */

/*
 * ----------------------------------------------------------------------------
 *   Extern Global Variables
 * ----------------------------------------------------------------------------
 */
/*
 *	BiCS5 Kioxia Flash
 *  89h : M,M,L,M, 8Ah: U,U,U
 *  weak in err_hdl_fpl_toshiba_bics_hb_retry.c
 */
const volatile U8 gubRetryReadRetryTableParameterIdxToPageType[HBIT_RETRY_SELECT_NON_SLC_FEA_DATA_NUM] = {
	HB_LRU_MIDDLE, HB_LRU_MIDDLE, HB_LRU_LOWER, HB_LRU_MIDDLE, HB_LRU_UPPER, HB_LRU_UPPER, HB_LRU_UPPER
};
/*
 *	BiCS5 Kioxia Flash
 *  Transition 0 to 6
 *  P0 | P1 | P2 | P3 | P4 | P5 | P6
 *  MID| UP | MID| LOW| UP | MID| UP
 *  weak in err_hdl_fpl_hardbir_retry.c
 */
const volatile U8 gubRetryTransitionToPageType[HBIT_RETRY_SELECT_NON_SLC_FEA_DATA_NUM] = { // Before BiCS5
	MIDDLE_PAGE_SEL, UPPER_PAGE_SEL, MIDDLE_PAGE_SEL, LOWER_PAGE_SEL, UPPER_PAGE_SEL, MIDDLE_PAGE_SEL, UPPER_PAGE_SEL
};

/*
 * ----------------------------------------------------------------------------
 *   Static Global Variables
 * ----------------------------------------------------------------------------
 */
AOM_RETRY_LOAD_TABLE const U8 gubKioxiaBICS5TLCHBRetryData512G[RETRY_KIOXIA_BICS5_TLC_512G_STEP_NUM * HBIT_RETRY_TLC_FEA_DATA_NUM] = {
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 0 for Initial Read
	0xFF, 0xFF, 0xFB, 0xFE, 0xFC, 0xFE, 0x03, // 1st
	0xF8, 0xFD, 0xF7, 0x00, 0xF8, 0xFC, 0xFC,// 2nd
	0xF4, 0xFB, 0x01, 0xFB, 0xF6, 0xF6, 0xFA,// 3rd
	0xF4, 0xF9, 0x02, 0xF8, 0xF3, 0xFA, 0xFF,// 4th
	0xEE, 0xF7, 0xF9, 0xFC, 0x01, 0x02, 0x04,// 5th
	0xF8, 0x02, 0xFD, 0x01, 0xFC, 0x00, 0x07,// 6th
	0xFB, 0xFC, 0xFC, 0xF6, 0xFE, 0xF9, 0xF7,// 7th
	0xFD, 0xFF, 0xFE, 0xF7, 0xF9, 0xF8, 0xFC,// 8th
	0xF0, 0xF8, 0xF4, 0xF5, 0xF8, 0xF3, 0xF0,// 9th
	0xF5, 0xF6, 0xF3, 0xF1, 0xFB, 0xF8, 0xF4,// 10th
	0xF9, 0xF3, 0xF8, 0xE9, 0xF7, 0xF1, 0xEE,// 11th
	0xF6, 0xF5, 0xFA, 0xEF, 0xF4, 0xEE, 0xE9,// 12th
	0xF0, 0xF1, 0xF1, 0xEB, 0xFB, 0xF6, 0xF2,// 13th
	0xF8, 0xF5, 0xF5, 0xE6, 0xF8, 0xEC, 0xE0,// 14th
	0xF8, 0xF8, 0xF6, 0xF1, 0xFD, 0xF4, 0xEC,// 15th
	0xF6, 0xFE, 0xEF, 0xFA, 0xF8, 0xF1, 0xE7,// 16th
	0xFC, 0xFC, 0xF0, 0xF2, 0xF5, 0xF3, 0xE9,// 17th
	0xFE, 0xFA, 0xFF, 0xEE, 0x00, 0xF8, 0xF0,// 18th
	0xF7, 0xF1, 0xF2, 0xE5, 0xFD, 0xF4, 0xE5,// 19th
	0xF5, 0xEF, 0xEE, 0xE1, 0x00, 0xF8, 0xEC,// 20th
	0xF6, 0xF3, 0xEC, 0xE2, 0xF6, 0xEA, 0xDD,// 21st
	0xEC, 0xED, 0xEA, 0xE4, 0xF4, 0xE8, 0xD8,// 22nd
	0xEE, 0xEF, 0xE8, 0xE8, 0xF8, 0xEE, 0xE4,// 23rd
	0xEA, 0xF4, 0x04, 0xF6, 0xFB, 0xF2, 0xE1,// 24th
	0xEA, 0xEF, 0x06, 0xF0, 0xF9, 0xF0, 0xDD,// 25th
	0xEF, 0xF2, 0x08, 0xF2, 0xF3, 0xF1, 0xF3,// 26th
	0xEA, 0xED, 0x0A, 0xED, 0xF1, 0xEB, 0xEF,// 27th
	0xF8, 0xFB, 0xE6, 0xF4, 0xF6, 0xF4, 0xF5,// 28th
	0xF9, 0xFB, 0xE4, 0xF0, 0xF9, 0xF3, 0xED,// 29th
	0xF6, 0xF9, 0x0C, 0xEC, 0xFC, 0xF2, 0xE6,// 30th
};

AOM_RETRY_LOAD_TABLE const U8 gubKioxiaBICS5SLCHBRetryData512G[RETRY_KIOXIA_BICS5_SLC_512G_STEP_NUM * HBIT_RETRY_SLC_FEA_DATA_NUM] = {
	0x00, 0xF8, 0xE8, 0x08, 0x18, 0xD8, 0x28, 0x38,	// 0th ~ 7th
};

U8 gubSlcHbitRetryData[HB_RETRY_MAX_SLC_STEP_NUM * HBIT_RETRY_SLC_FEA_DATA_NUM] = {
	0x00, 0xF8, 0xE8, 0x08, 0x18, 0xD8, 0x28, 0x38,	// 0th ~ 7th
};

/*
 * ----------------------------------------------------------------------------
 *   Private Prototypes
 * ----------------------------------------------------------------------------
 */
AOM_RETRY_LOAD_TABLE NO_INLINE void HBRetryInitTable(void);

/*
 * ----------------------------------------------------------------------------
 *   Private
 * ----------------------------------------------------------------------------
 */

INLINE void HBRetryInitRegisterRetryTable(void)
{
	gpHBParameterArray[FPL_ADDR_TLC_RULE].pubRetryTable = (U8 *)(&gubHbitRetryData);
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubTotalRetryCnt = RETRY_KIOXIA_BICS5_TLC_512G_STEP_NUM;//default Retry table
	gpHBParameterArray[FPL_ADDR_SLC_RULE].pubRetryTable = (U8 *)(&gubSlcHbitRetryData);
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubTotalRetryCnt = RETRY_KIOXIA_BICS5_SLC_512G_STEP_NUM;//default Retry table
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubCurrentStep = HB_READ_START_STEP;
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubLastSuccessIndex = 0;
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubRetryParameterSize = HBIT_RETRY_TLC_FEA_DATA_NUM;
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubParameterNumPerFPU = PARAMETER_NUM_PER_FPU;
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubCurrentStep = HB_READ_START_STEP;
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubLastSuccessIndex = 0;
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubRetryParameterSize = HBIT_RETRY_SLC_FEA_DATA_NUM;
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubParameterNumPerFPU = PARAMETER_NUM_PER_FPU;
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubScratchTableIdx = INVALID_RETRY_TABLE_INDEX;
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubScratchTableIdx = INVALID_RETRY_TABLE_INDEX;
}

INLINE void HBRetryInitAdjustVthFPU(void)
{
	// Register get feature fpu
	gFpuEntryList.fpu_get_feature[0] = FPU_CMD(0xD4); // Get feature cmd
	gFpuEntryList.fpu_get_feature[3] = FPU_END;

	// Register read and compare feature data fpu
	gFpuEntryList.fpu_read_and_compare_feature_data[0] = FPU_CMD(0x00); // Switch to read mode
	gFpuEntryList.fpu_read_and_compare_feature_data[10] = FPU_END;
}

/*
 * ------------------------------------
 * 		Over Wirte Weak Function
 * ------------------------------------
 */

void HBRetryInitParameter(void)
{
	M_FW_ASSERT(ASSERT_HARDBIT_RETRY_0x0D81, (ID_TOSHIBA == gpOtherInfo->ubMakerCode) && (RETRY_KIOXIA_FLASH_PROCESS_BICS5_TLC_512G == gpOtherInfo->ubProcess));
	HBRetryInitRegisterRetryTable();
	HBRetryInitAdjustVthFPU();
	HBRetryInitTable();
}

void HBRetryInitTable(void)
{
	U8 ubHBRetryDataStepNum = RETRY_KIOXIA_BICS5_TLC_512G_STEP_NUM;//default Retry table step num
	U8 ubSLCHBRetryDataStepNum = RETRY_KIOXIA_BICS5_SLC_512G_STEP_NUM;//default SLC Retry table step num

	if (RETRY_DIE_512G == gpRetry->RetryVersion.Retry.Idx.ubMonoDie) {
		// TLC RR Table
		memcpy((&gubHbitRetryData), (&gubKioxiaBICS5TLCHBRetryData512G), (RETRY_KIOXIA_BICS5_TLC_512G_STEP_NUM * HBIT_RETRY_TLC_FEA_DATA_NUM));
		ubHBRetryDataStepNum = RETRY_KIOXIA_BICS5_TLC_512G_STEP_NUM;
		// Add Scratch Table
		//		gpHBParameterArray[FPL_ADDR_TLC_RULE].ubScratchTableIdx = HBIT_RETRY_BICS4_HDR_TLC_256G_STEP_NUM;
		//		memcpy((void *)(&gubHbitRetryData[HBIT_RETRY_BICS4_HDR_TLC_256G_STEP_NUM * HBIT_RETRY_TLC_FEA_DATA_NUM]), (void *)(&gubToshibaBICS4HDRScratchTable256G), HBIT_RETRY_BICS4_HDR_256G_SCRATCH_TABLE_NUM * HBIT_RETRY_TLC_FEA_DATA_NUM);
		//		ubHBRetryDataStepNum += HBIT_RETRY_BICS4_HDR_256G_SCRATCH_TABLE_NUM;
		// SLC RR Table
		memcpy((&gubSlcHbitRetryData), (&gubKioxiaBICS5SLCHBRetryData512G), (RETRY_KIOXIA_BICS5_SLC_512G_STEP_NUM * HBIT_RETRY_SLC_FEA_DATA_NUM));
		ubSLCHBRetryDataStepNum = RETRY_KIOXIA_BICS5_SLC_512G_STEP_NUM;
	}

	gpHBParameterArray[FPL_ADDR_TLC_RULE].pubRetryTable = (U8 *)(&gubHbitRetryData);
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubTotalRetryCnt = ubHBRetryDataStepNum;
	gpHBParameterArray[FPL_ADDR_SLC_RULE].pubRetryTable = (U8 *)(&gubSlcHbitRetryData);
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubTotalRetryCnt = ubSLCHBRetryDataStepNum;
}
#endif /* (PS5017_EN && (FLASH_BICS5TLC == FW_CATEGORY_FLASH) && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER)) */

