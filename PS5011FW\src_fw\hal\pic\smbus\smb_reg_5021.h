#ifndef _E21_SMB_REG_H_
#define _E21_SMB_REG_H_

#include "setup.h"
#include "typedef.h"
#include "symbol.h"
#include "mem.h"

#define SMBM_REG_BASE		(PIC_SMBM_REG_ADDRESS)
#define SMBS_REG_BASE		(PIC_SMBS_REG_ADDRESS)

#define R8_SMBM                             ((volatile U8  *)SMBM_REG_BASE)
#define R16_SMBM                            ((volatile U16 *)SMBM_REG_BASE)
#define R32_SMBM                            ((volatile U32 *)SMBM_REG_BASE)
#define R64_SMBM                            ((volatile U64 *)SMBM_REG_BASE)

#define R32_SMBMW_INT_EN					(0x04 >> 1)

//==============================================================================
#define R8_SMBS                             ((volatile U8  *)SMBS_REG_BASE)
#define R16_SMBS                            ((volatile U16 *)SMBS_REG_BASE)
#define R32_SMBS                            ((volatile U32 *)SMBS_REG_BASE)
#define R64_SMBS                            ((volatile U64 *)SMBS_REG_BASE)

#define R8_SMBS_CTRL                         (0x00)
#define     SMBS_SW_RESET_BIT                            (BIT0)

#define R8_SMBS_CTRL_EXT                     (0x01)
#define     SMBS_FORCE_NACK_BIT                      (BIT6)

#define R16_SMBS_INT_EN                       (0x04 >> 1)
#define     SMBS_INT_EN_MASK                    (BIT_MASK(12))

#define R16_SMBS_INT_STS                      (0x06 >> 1)
#define     SMBS_RS_DET_BIT                          (BIT2)
#define     SMBS_INT_STS_MASK                    (BIT_MASK(12))

#define R16_SMBS_SLV_ADDR_0                   (0x10 >> 1)
#define R16_SMBS_SLV_ADDR_1                   (0x12 >> 1)
#define R16_SMBS_SLV_ADDR_2                   (0x14 >> 1)
#define     SLAVE_ADDR                          (0xE0)      // 0x70 << 1

#define R8_SMBS_RX_BYTE                       (0x20)
#define     M_GET_SMBS_RX_BYTE(N)                   (R8_SMBS[R8_SMBS_RX_BYTE+N])

#define R8_SMBS_TX_BYTE                       (0x40)

#define R32_SMBS_TX_BUF_STS                   (0x60 >> 2)

#define R8_SMBS_RX_VLD_CNT                    (0x68)

#define R8_SMBS_BUF_VLD_CTRL                  (0x6C)
#define     SMBS_RX_VLD_CLR_BIT                     (BIT0)
#define     SMBS_TX_VLD_SET_BIT                     (BIT1)

#define R32_SMBS_CLK_LOW_CNT                  (0x90 >> 2)
#define     SMBS_AUTO_RST_EN_BIT                              (BIT31)
#define     SMBS_SMBCLK_LOW_COUNT_MASK            (BIT_MASK(31))
#define     SMBS_TIMEOUT_CNT                  (0x98968) // set timeout cnt 25ms (smbus spec.) / 40ns (25MHz) = 625000 = 0x98968

#endif /* _E21_SMB_REG_H_ */
