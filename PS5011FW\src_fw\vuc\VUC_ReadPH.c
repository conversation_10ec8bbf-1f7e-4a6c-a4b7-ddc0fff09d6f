#include "ftl/ftl_api.h"
#include "hal/cop0/cop0_api.h"
#include "hal/cop1/cop1_api.h"
#include "hal/dmac/dmac_api.h"
#include "hal/dmac/dmac_pop_cmd.h"
#include "hal/pic/uart/uart_api.h"
#include "init/fw_init.h"
#include "nvme_api/nvme/shr_hal_nvme.h"
#include "table/dbt/dbt_api.h"
#include "vuc/VUC_ReadPH.h"
#include "vuc/VUC_ScanPH.h"

#if (PHBLOCK_EN)
U8 VUCReadPHGetPHFromDBTHeader(U32 ulBufAddr)
{
	U8 ubResult = PASS;
	SystemAreaBlockStatus_t BlkStatus = {0};
	//Read DBT Header by System Area read API
	DBTBlockHeader_t *pDBTBlkHeader = (DBTBlockHeader_t *)ulBufAddr;
	DBTHeaderSector0_t *pDBTHeader = (DBTHeaderSector0_t *) & (pDBTBlkHeader->DBTHeaderSector0);
	BlkStatus = DBTReadPlane(ulBufAddr, SPARE_LCA_DBT_HEADER, 0, USE_COP0, SYSTEM_AREA_WAIT_COP0_IDLE_AND_LOCK); // Need to include dbt_api.h

	if (FAIL == BlkStatus.Info.btStatus) {
		ubResult = PH_2ND_READ_DBT_HEADER_FAIL; // Check for RDT choose PH block bug
	}
	else {
		gSystemArea.uoDBTRevision = pDBTHeader->uoDBTRevision;
		if ((INVALID_BLOCK != pDBTHeader->PH[0].uwAll) && (INVALID_BLOCK != pDBTHeader->PH[1].uwAll)) {
			M_UART(PH_, "\nHave PH.");
			gPH.Info.B.btPHExist = TRUE;
			if (pDBTHeader->PH[0].uwAll == pDBTHeader->PH[1].uwAll) {
				ubResult = PH_CHOOSE_THE_SAME_ONE;
			}
			else {
				gPH.PHBlk[0] = pDBTHeader->PH[0];
				gPH.PHBlk[1] = pDBTHeader->PH[1];
			}
		}
		else if ((INVALID_BLOCK == pDBTHeader->PH[0].uwAll) && (INVALID_BLOCK == pDBTHeader->PH[1].uwAll)) {
			M_UART(PH_, "\nPFM no PH.");
			gPH.Info.B.btPHExist = FALSE;
			gPH.PHBlk[0].uwAll = INVALID_BLOCK;
			gPH.PHBlk[1].uwAll = INVALID_BLOCK;
			ubResult = PH_NOT_EXIST;
		}
		else {
			// One PH blk exists, another is disappear
			ubResult = PH_LOST;
		}
	}

	return ubResult;
}

#if (RDT_MODE_EN)
void VUCReadPH(VUC_OPT_HCMD_PTR_t pCmd)
{
	U8 ubSubfeature, ubRet;

	ubSubfeature = pCmd->vuc_sqcmd.vendor.ReadPH.ubSubFeature;

	if (gPH.Info.B.btScannedDone == FALSE) {
		VUCScanPH(READ_BUF_BASE);
	}

	ubRet = VUCReadPHLog(ubSubfeature, pCmd->ulCurrentPhysicalMemoryAddr);

	if (ubRet) {
		pCmd->ubState = CMD_ERROR;
		return;
	}

	pCmd->ubState = FW_PROCESS_DONE;
}
#else /* (RDT_MODE_EN) */
void VUCReadPH(VUC_OPT_HCMD_PTR_t pCmd)
{
	U8 ubSubfeature = pCmd->vuc_sqcmd.vendor.ReadPH.ubSubFeature;

	if (PH_UART_EN) {
		M_UART(VUC_, "\n [PHInfo] VUCReadPH %d-%d", pCmd->ulTotalLCA, pCmd->ulDoneLCA);
	}
#if (HOST_MODE == NVME)
	DMACParam_t DMACParameter;
	if (0 == pCmd->ulDoneLCA) {
#if (BURNER_MODE_EN)
		SystemAreaScanBlk();	// Get DBT Blk

		if (0 != gSystemArea.ubDBTBlockNum) {
			if (PASS == VUCReadPHGetPHFromDBTHeader(pCmd->ulCurrentPhysicalMemoryAddr)) {
				gPH.ulHostBufAddr = pCmd->ulCurrentPhysicalMemoryAddr;
			}
			else {
				pCmd->ubState = CMD_ERROR;
				return;
			}
		}
		else {
			pCmd->ubState = CMD_ERROR;
			return;
		}
#else /*(BURNER_MODE_EN)*/
		COP1APIST3CReleaseCache(FWLB_VENDER_SIZE_IN_4K);
		BufferAllocateFWLBPBLink(FWLB_VENDER_USAGE_BIT, ALLOCATE_NO_USE_CTAG, ALLOCATE_NO_USE_LCA);
		gPH.ulHostBufAddr = M_LB_TO_ADDR(LB_ID_FW, gFWLBMgr.Type[FWLB_VENDER_USAGE].uwLBOffset);
		M_SIM_LB_ADDR_TO_VADDR(gPH.ulHostBufAddr);
		if (PH_UART_EN) {
			M_UART(VUC_, "\n [PHInfo] FTLAllocateFWLBPBLink Done");
		}
#endif /*(BURNER_MODE_EN)*/
		if (FALSE == gPH.Info.B.btScannedDone) {
			VUCScanPH(gPH.ulHostBufAddr);
		}
		if (FAIL == VUCReadPHLog(ubSubfeature, gPH.ulHostBufAddr)) {
			pCmd->ubState = CMD_ERROR;
			return;
		}

		DMACParameter.ulSourceAddr = gPH.ulHostBufAddr;
		DMACParameter.ulDestAddr = pCmd->ulCurrentPhysicalMemoryAddr;
		DMACParameter.ul32ByteNum = SIZE_IN_32B(BC_4KB) ;
		gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
		DMACCopy(DMAC_MODE_NORMAL_QUEUE, &DMACParameter, gulDMACDirectWaitDone_Callback, 0);
		while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
			DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
		}
		if (PH_UART_EN) {
			M_UART(VUC_, "\n [PHInfo] Copy S:%x to D:%x", DMACParameter.ulSourceAddr, DMACParameter.ulDestAddr);
			M_UART(VUC_, " PTN %b", *(U8 *)(DMACParameter.ulSourceAddr));
		}

	}
	else {

		DMACParameter.ulSourceAddr = gPH.ulHostBufAddr + (U32)BC_4KB * pCmd->ulDoneLCA;
		DMACParameter.ulDestAddr = pCmd->ulCurrentPhysicalMemoryAddr;
		DMACParameter.ul32ByteNum = SIZE_IN_32B(BC_4KB) ;
		gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
		DMACCopy(DMAC_MODE_NORMAL_QUEUE, &DMACParameter, gulDMACDirectWaitDone_Callback, 0);
		while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
			DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
		}
		if (PH_UART_EN) {
			M_UART(VUC_, "\n [PHInfo] Copy S:%x to D:%x", DMACParameter.ulSourceAddr, DMACParameter.ulDestAddr);
			M_UART(VUC_, " PTN %b", *(U8 *)(DMACParameter.ulSourceAddr));
		}
	}
#if (!BURNER_MODE_EN)
	if (pCmd->ulDoneLCA == pCmd->ulTotalLCA - 1) {
		BufferFreeFWLBPBLink(FWLB_VENDER_USAGE_BIT);
		COP1APIST3CCollectCache(FWLB_VENDER_SIZE_IN_4K);
		if (PH_UART_EN) {
			M_UART(VUC_, "\n [PHInfo] FTLFreeFWLBPBLink Done");
		}
	}
	pCmd->ubState = FW_PROCESS_DONE;
#endif /*(!BURNER_MODE_EN)*/
#else /* (HOST_MODE == NVME) */
#if (BURNER_MODE_EN)
	U32 ulCmdTransferDataSize = (U32)(DEF_4B * pCmd->vuc_sqcmd.vendor.ReadPH.ulLength);

	if (VUC_PH_LOG_SIZE != ulCmdTransferDataSize) {
		pCmd->ubState = CMD_ERROR;
		return;
	}
	SystemAreaScanBlk();	// Get DBT Blk

	if (0 != gSystemArea.ubDBTBlockNum) {
		if (PASS == VUCReadPHGetPHFromDBTHeader(pCmd->ulCurrentPhysicalMemoryAddr)) {
			gPH.ulHostBufAddr = pCmd->ulCurrentPhysicalMemoryAddr;
		}
		else {
			pCmd->ubState = CMD_ERROR;
			return;
		}
	}
	else {
		pCmd->ubState = CMD_ERROR;
		return;
	}
#endif /*(BURNER_MODE_EN)*/
	if (FALSE == gPH.Info.B.btScannedDone) {
		VUCScanPH(pCmd->ulCurrentPhysicalMemoryAddr);
	}
	if (FAIL == VUCReadPHLog(ubSubfeature, pCmd->ulCurrentPhysicalMemoryAddr)) {
		pCmd->ubState = CMD_ERROR;
		return;
	}
	pCmd->ubState = FW_PROCESS_DONE;
#endif /* (HOST_MODE == NVME) */

}
#endif /* (RDT_MODE_EN) */
U8 VUCReadPHLog(U8 ubSubfeature, U32 ulDstAddr)
{
	U8 ubTotal4kNum, ub4kCnt;
	DMACParam_t DMACParameter;
	gubLeavePreformatFlag = 0;
	U8 ubCEPtr, ubChannelPtr, ubBlkPtr, ubPCARuleMode;
	U16 uwPageInBlkPtr = VUC_PH_NO_LOG_FOUND;
	PCA_t ulFWPCA;
	U32 ulLocalPCA, ulCompareLCA;
	U8 ubLocalLPCRC = 0;
	ubTotal4kNum = VUC_PH_LOG_SIZE / FRAME_SIZE;

	if (VUC_PH_MP_LOG == ubSubfeature) {
		uwPageInBlkPtr = gPH.uwMPLogPage;
		ulCompareLCA = SPARE_LCA_PH_MP_LOG;

		if (PH_UART_EN) {
			M_UART(VUC_, "\n [PHInfo] Read MP_LOG P %d", uwPageInBlkPtr);
		}

	}
	else if (VUC_PH_RDT_LOG == ubSubfeature) {
		uwPageInBlkPtr = gPH.uwRDTLogPage;
		ulCompareLCA = SPARE_LCA_PH_RDT_LOG;

		if (PH_UART_EN) {
			M_UART(VUC_, "\n [PHInfo] Read RDT_LOG P %d", uwPageInBlkPtr);
		}

	}
	else if (VUC_PH_EC == ubSubfeature) {
		uwPageInBlkPtr = gPH.uwECLogPage;
		ulCompareLCA = SPARE_LCA_PH_EC_LOG;

		if (PH_UART_EN) {
			M_UART(VUC_, "\n [PHInfo] Read EC_LOG P %d", uwPageInBlkPtr);
		}

	}
#if VRLC_EN
	else if (VUC_PH_VRLC == ubSubfeature) {
		uwPageInBlkPtr = gPH.uwVRLCLogPage;
		ulCompareLCA = SPARE_LCA_PH_VRLC_LOG;

		if (PH_UART_EN) {
			M_UART(VUC_, "\n [PHInfo] Read VRLC P %d", uwPageInBlkPtr);
		}

	}
#endif /* VRLC_EN */

	if (VUC_PH_NO_LOG_FOUND == uwPageInBlkPtr) {
		if (PH_UART_EN) {
			M_UART(VUC_, "\n [PHInfo] Log empty !");
		}

		DMACParameter.DMACSetValue.ulDestAddr = ulDstAddr;
		DMACParameter.DMACSetValue.ulLowValue = 0;
		DMACParameter.DMACSetValue.ulHighValue = 0;
		DMACParameter.DMACSetValue.ul32ByteNum = SIZE_IN_32B(VUC_PH_LOG_SIZE);
		DMACParameter.DMACSetValue.btValidate = FALSE;
		gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
		DMACSetValue(&DMACParameter, gulDMACDirectWaitDone_Callback, 0);
		while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
			DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
		}
	}
	else {
		U32 ulBufAddr[VUC_PH_LOG_SIZE / FRAME_SIZE] = {0};
		for (ub4kCnt = 0; ub4kCnt < ubTotal4kNum; ++ub4kCnt) {
			ulBufAddr[ub4kCnt] = ulDstAddr + ((U32)ub4kCnt * BC_4KB);
		}
		ubBlkPtr = gPH.PHBlk[gPH.Info.B.btActiveBlk].Info.ubBlockOrder;
		ubCEPtr = gPH.PHBlk[gPH.Info.B.btActiveBlk].Info.CEperCH;
		ubChannelPtr = gPH.PHBlk[gPH.Info.B.btActiveBlk].Info.Channel;
		ubPCARuleMode = (gFlhEnv.ulFlashDefaultType.BitMap.CellType == FLH_SLC) ? COP0_PCA_RULE_0 : COP0_PCA_RULE_2;
		ub4kCnt = 0;
		while (ub4kCnt < ubTotal4kNum) {
			ulLocalPCA = M_GET_PCA(0, M_DIV(ubBlkPtr, gubBurstsPerBank), uwPageInBlkPtr, ubCEPtr, ubChannelPtr, M_MOD(ubBlkPtr, gubBurstsPerBank), 0, ubPCARuleMode);
			M_FWPCA_SET(ulFWPCA.ulAll, ulLocalPCA, gPCAInfo.ubMaxZByte, 0, ubLocalLPCRC);
			FTLCOP0ReadN4kEntry_AOM_INIT_2((FTLCOP0ReadN4kEntryMode_t) {
				.ubSetMode = R_GETLCA_FROM_L4KTABLE, .ubParaMode = COP0_R_SYSTEM_INITIAL_SCAN, .ub4KNum = gub4kEntrysPerPlane, .ubPrivate = NULL
			}, ulFWPCA, &ulBufAddr[ub4kCnt]);
			ub4kCnt += gub4kEntrysPerPlane;
			uwPageInBlkPtr++;
		}
		if (ulCompareLCA != gulTieoutLCA[TIEOUT_SINGLE_4K_LCA_OFFSET]) {
			return FAIL;
		}
	}
	gubLeavePreformatFlag = 1;
	return PASS;
}
#endif /* (PHBLOCK_EN) */
