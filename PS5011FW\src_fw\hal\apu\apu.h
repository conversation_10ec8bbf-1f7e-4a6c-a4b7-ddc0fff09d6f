#ifndef	_APU_H_
#define	_APU_H_

#include "typedef.h"

#define APU_SRAM_LS_EN          (0)
#define NVME_BYPASS_DEPCHK      (0)
#define APU_AUTO_SRCH_EN        (1)
#define WR_AUTO_CPL_EN          (1)
#define SYNC_WR_AUTO_CPL_EN     (0)

#define	APU_DEP_TBL_LCA_LENGTH		(1024 * 128 - 1) //Range length Max is (2^17-1) LCAs in a Dep Table Entry
AOM_NRW_4 U8 APUDeleteCmdFIFO (CTAG_t ctag);

#if (PS5021_EN)
#define	M_APU_SET_DEL_REQ_INFO_TAG(TAG)				(R32_APU[R32_APU_DEP_REQ_CTRL] = (((((TAG)) & APU_DEL_REQ_INFO_TAG_MASK) << APU_DEL_REQ_INFO_TAG_SHIFT) | (R32_APU[R32_APU_DEP_REQ_CTRL] & ~(APU_DEL_REQ_INFO_TAG_MASK<<APU_DEL_REQ_INFO_TAG_SHIFT))))
#else /*(PS5021_EN)*/
#define	M_APU_SET_DEL_REQ_INFO_TAG(TAG)				(R8_APU[R8_APU_DEL_REQ_INFO_TAG] = ((TAG)))
#endif /*(PS5021_EN)*/

#define	M_APU_SET_DEL_REQ_MODE(MODE)					(R8_APU[R8_APU_DEP_REQ] |= (((MODE) << APU_DEL_REQ_INFO_MODE_SHIFT) & APU_DEL_REQ_INFO_MODE_BIT)) //- Select the APU_DEL_REQ_INFO_TAG is DTAG(0b) or CTAG(1b).
#define	M_APU_SET_DEL_REQ_ATTR(ATTR)					(R8_APU[R8_APU_DEP_REQ] |= (((ATTR) & APU_DEL_REQ_INFO_ATTR_MASK) << APU_DEL_REQ_INFO_ATTR_SHIFT))//- Attr[1:0]: read(2'b00)/write(2'b01)/trim(2'b11)

#if (PS5021_EN || PS5017_EN)
#define M_APU_SET_INSERT_REQ_INFO_LCA(LCA)			(R32_APU[R32_APU_INSERT_REQ_INFO_LCA] = ((LCA) & BITMSK(30,0)))
#else /* (PS5021_EN || PS5017_EN) */
#define M_APU_SET_INSERT_REQ_INFO_LCA(LCA)			(R32_APU[R32_APU_INSERT_REQ_INFO_LCA] = (U32)LCA)
#endif /* (PS5021_EN || PS5017_EN) */
#define M_APU_SET_INSERT_REQ_INFO_NLC(NLC)			do { \
														R16_APU[R16_APU_INSERT_REQ_INFO_NLC] = ((U16)(NLC)); \
														R8_APU[R8_APU_DEP_REQ_INSERT_INFO] = (((NLC) >> 16) & APU_INSERT_REQ_INFO_NLC_1_BIT); \
													} while (0)
#define M_APU_SET_INSERT_REQ_INFO_NSID(NSID)			(R8_APU[R8_APU_DEP_REQ_INSERT_INFO] |= (((NSID) & APU_INSERT_REQ_INFO_NSID_MASK) << APU_INSERT_REQ_INFO_NSID_SHIFT))

#if (PS5021_EN || PS5017_EN)
#define M_APU_SET_INSERT_REQ_ATTR(ATTR)				(R8_APU[R8_APU_INSERT_REQ_INFO_ATTR] |= (((ATTR) & APU_INSERT_REQ_INFO_ATTR_MASK) << APU_INSERT_REQ_INFO_ATTR_SHIFT))
#else /* (PS5021_EN || PS5017_EN) */
#define M_APU_SET_INSERT_REQ_ATTR(ATTR)				(R8_APU[R8_APU_DEP_REQ_INSERT_INFO] |= (((ATTR) & APU_INSERT_REQ_INFO_ATTR_MASK) << APU_INSERT_REQ_INFO_ATTR_SHIFT))
#endif /* (PS5021_EN || PS5017_EN) */

#if (PS5021_EN)

#define M_APU_SET_INSERT_REQ_INFO_CTAG(CTAG)			(R16_APU[R16_APU_INSERT_REQ_INFO_CTAG] = (((((CTAG_t)CTAG) & APU_INSERT_REQ_INFO_CTAG_MASK) << APU_INSERT_REQ_INFO_CTAG_SHIFT) | (R16_APU[R16_APU_INSERT_REQ_INFO_CTAG] & BITMSK(7,0))))
#else /* (PS5021_EN) */
#define M_APU_SET_INSERT_REQ_INFO_CTAG(CTAG)			(R8_APU[R8_APU_INSERT_REQ_INFO_CTAG] = (CTAG_t)CTAG)   // E13 & S17
#endif /*(PS5021_EN)*/

#define M_APU_SET_DEP_REQ_INSERT_INFO(LCA,NLC,NSID,ATTR,CTAG)	do { \
														R8_APU[R8_APU_DEP_REQ_INSERT_INFO] = 0;\
														M_APU_SET_INSERT_REQ_INFO_LCA(LCA); \
														M_APU_SET_INSERT_REQ_INFO_NLC(NLC); \
														M_APU_SET_INSERT_REQ_INFO_NSID(NSID); \
														M_APU_SET_INSERT_REQ_ATTR(ATTR); \
														M_APU_SET_INSERT_REQ_INFO_CTAG(CTAG); \
													} while (0)

#if PS5017_EN
#define HAL_APU_GET_DEP_TABLE_VLD_BIT(x)			(R8_APU[R8_APU_DEP_TABLE_VLD_BIT + ((x) >> 3)] & BIT((x) % 8))
#endif /* PS5017_EN */

#if (PS5021_EN)
#define M_APU_GET_DEP_RD_RSLT_SRAM_ATTR(INDEX)          ((R64_APU[R64_APU_DEP_RD_RSLT_SRAM_OFFSET + ((INDEX)%128)*4 + ((INDEX)>>7)] >> APU_DEP_RD_RSLT_SRAM_ATTR_SHIFT) & APU_DEP_RD_RSLT_SRAM_ATTR_MASK)//- attr[1:0]: read(2'b00)/write(2'b01)/trim(2'b11).
#define M_APU_GET_DEP_RD_RSLT_SRAM_CTAG(INDEX)          ((R64_APU[R64_APU_DEP_RD_RSLT_SRAM_OFFSET + ((INDEX)%128)*4 + ((INDEX)>>7)] >> APU_DEP_RD_RSLT_SRAM_CTAG_SHIFT) & APU_DEP_RD_RSLT_SRAM_CTAG_MASK)//- Ctag[7:0] : command tag.
#define M_APU_GET_DEP_RD_RSLT_SRAM_VLD(INDEX)           ((R64_APU[R64_APU_DEP_RD_RSLT_SRAM_OFFSET + ((INDEX)%128)*4 + ((INDEX)>>7)] >> APU_DEP_RD_RSLT_VLD_SHIFT) & APU_DEP_RD_RSLT_VLD_MASK)//- Dependency table valid bit.
#else /* (PS5021_EN) */
#define M_APU_GET_DEP_RD_RSLT_SRAM_ATTR(INDEX)          ((R64_APU[R64_APU_DEP_RD_RSLT_SRAM_OFFSET + ((INDEX)%32)*4 + ((INDEX)>>5)] >> APU_DEP_RD_RSLT_SRAM_ATTR_SHIFT) & APU_DEP_RD_RSLT_SRAM_ATTR_MASK)//- attr[1:0]: read(2'b00)/write(2'b01)/trim(2'b11).
#define M_APU_GET_DEP_RD_RSLT_SRAM_CTAG(INDEX)          ((R64_APU[R64_APU_DEP_RD_RSLT_SRAM_OFFSET + ((INDEX)%32)*4 + ((INDEX)>>5)] >> APU_DEP_RD_RSLT_SRAM_CTAG_SHIFT) & APU_DEP_RD_RSLT_SRAM_CTAG_MASK)//- Ctag[7:0] : command tag.
#define M_APU_GET_DEP_RD_RSLT_SRAM_VLD(INDEX)           ((R64_APU[R64_APU_DEP_RD_RSLT_SRAM_OFFSET + ((INDEX)%32)*4 + ((INDEX)>>5)] >> APU_DEP_RD_RSLT_VLD_SHIFT) & APU_DEP_RD_RSLT_VLD_MASK)//- Dependency table valid bit.
#endif /* (PS5021_EN) */

#define M_APU_SET_TIMER_MS_CNT_MAX(SYSCLK)        (R32_APU[R32_APU_TMR_MS_CNT_MAX] = ((SYSCLK / 1000) & APU_TMR_MS_CNT_MAX_MASK)) // (SYS_CLK in Hz / 1ms) = count in ms 
#define M_APU_SET_TIMER_100NS_CNT_MAX(SYSCLK)     (R8_APU[R8_APU_TMR_100NS_CNT_MAX] = ((SYSCLK / (10 * 1000 * 1000)) & APU_TMR_100NS_CNT_MAX_MASK))  // (SYS_CLK in Hz * 100/ 1ns) = count in 100ns#endif /* _APU_API_H_ */
#endif	/* _APU_H_ */
