#ifndef _RETRY_SAMSUNG_V6_TLC_E13_NEUTRAL_HB_H_
#define _RETRY_SAMSUNG_V6_TLC_E13_NEUTRAL_HB_H_
/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */

#include "env.h"
//V7 USE V6 RDT Setting
#if (((PS5013_EN) || (PS5017_EN)) && ((CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)))
/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */

#define HBIT_RETRY_SAMSUNG_TLC_512G_STEP_NUM			(33 + 1)
#define HBIT_RETRY_SAMSUNG_SLC_512G_STEP_NUM			(33 + 1)

/*
 * ---------------------------------------------------------------------------------------------------
 *  enum
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */

#endif /* ((PS5013_EN) && (FLASH_HYNIXV6_TLC == FW_CATEGORY_FLASH) && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER)) */
#endif /* _RETRY_HYNIX_V6_TLC_E13_NEUTRAL_HB_H_ */
