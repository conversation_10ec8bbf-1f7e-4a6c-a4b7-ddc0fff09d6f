#ifndef _VUC_DETECT_TXRX_H_
#define _VUC_DETECT_TXRX_H_
#include "symbol.h"
#include "aom/aom_api.h"
#include "host/VUC_handler.h"
#include "env.h"

#if (NVME == HOST_MODE)
#define VUC_DETECT_TXRX_TX_OPEN  (0x00)
#define VUC_DETECT_TXRX_RX_SHORT (0x01)
#define VUC_DETECT_TXRX_CPHY (0x07)
#define VUC_DETECT_TXRX_LANE0 (0x15)
#define VUC_DETECT_TXRX_LANE1 (0x35)
#define VUC_DETECT_TXRX_LANE2 (0x62)
#define VUC_DETECT_TXRX_LANE3 (0x73)
#define PHY_DEBUG_PORT_SETTING (0xCE4614)
#define PHY_DEBUG_PORT_OPTION (0xCE4608)

#define ALL_LAND_ZERO (0)
#define SOME_LAND_NONZERO (1)

#define R32_PHY_DEBUG_PORT_SETTING ((REG32 *) PHY_DEBUG_PORT_SETTING)
#define R32_PHY_DEBUG_PORT_LANE ((REG32 *) PHY_DEBUG_PORT_OPTION)

#define M_PHY_PSEL_DEBUG_PORT_SWITCH()	 (R32_PHY_DEBUG_PORT_SETTING[0] &=~(BIT21|BIT20))
#define M_PHY_CLR_PSEL_LANE()	 (R32_PHY_DEBUG_PORT_LANE[0] &= 0x00FFFF)
#define M_PHY_SET_PSEL_LANE(X)	 (R32_PHY_DEBUG_PORT_LANE[0] |= (X<<16))


#if BOOTLOADER_EN
AOM_VUC void VUCDetectTxRx(VUC_OPT_HCMD_PTR pCmd);
AOM_VUC void VUCDetectTxOpen(VUC_OPT_HCMD_PTR pCmd);
AOM_VUC void VUCDetectRxShort(VUC_OPT_HCMD_PTR pCmd);
AOM_VUC void VUCDetectTxOpenSetLane(U8 ubLane);
#else /* BOOTLOADER_EN */
AOM_NRW_4 void VUCDetectTxRx(VUC_OPT_HCMD_PTR pCmd);
AOM_NRW_4 void VUCDetectTxOpen(VUC_OPT_HCMD_PTR pCmd);
AOM_NRW_4 void VUCDetectRxShort(VUC_OPT_HCMD_PTR pCmd);
AOM_NRW_4 void VUCDetectTxOpenSetLane(U8 ubLane);
#endif /* BOOTLOADER_EN */


#endif /*(NVME == HOST_MODE)*/
#endif /* _VUC_DETECT_TXRX_H_ */
