#include "rs/open_block_rsmap_api.h"
#include "setup.h"
#include "typedef.h"
#include "buffer/buffer.h"
#include "hal/fip/fip_api.h"
#include "hal/fip/fip.h"
#include "hal/rs/rs_api.h"
#include "hal/bmu/bmu_api.h"
#include "hal/cop0/cop0_api.h"
#include "hal/dmac/dmac_pop_cmd.h"
#include "hal/sys/api/misc/misc_api.h"
#include "hal/bmu/bmu_api.h"
#include "cpu/cpu_cache_api.h"
#include "hal/rs/rs.h"
#include "cpu/cpu_api.h"
#include "raideccmap/raideccmap.h"
#include "raideccmap/raideccmap_inline_api.h"


#define OPEN_BLOCK_RSMAP_RS_DATA_IN_LB_OFFSET_NUM              (gub4kEntrysPerPlane)
#define OPEN_BLOCK_RSMAP_RS_SPARE_DATA_SIZE                    (16)
#define OPEN_BLOCK_RSMAP_RS_SPARE_DATA_SIZE_PER_PLANE          (OPEN_BLOCK_RSMAP_RS_SPARE_DATA_SIZE * gub4kEntrysPerPlane)    // 64B
#define OPEN_BLOCK_RSMAP_RS_PARITY_DATA_SIZE				   (gub4kEntrysPerPlane * SIZE_4KB)

#define OPEN_BLOCK_RSMAP_GC_PLANES_PER_GR_UNIT	(gFlhEnv.uwPagePerBlock)

typedef enum {
	OPEN_BLOCK_RSMAP_SUB_FLOW_DOING,
	OPEN_BLOCK_RSMAP_SUB_FLOW_DONE,
} OpenBlockRSMapSubFlowResult_t;

typedef enum {
	OPEN_BLOCK_RSMAP_INIT,
	OPEN_BLOCK_RSMAP_CORE_FLOW,
	OPEN_BLOCK_WAIT_PROGRAM_PARITY_DONE,
	OPEN_BLOCK_RSMAP_WAIT_DMAC_COPY_DONE,
	OPEN_BLOCK_RSMAP_PROGRAM_RS_PARITY_FROM_BACKUP_DATA,
	OPEN_BLOCK_RSMAP_PROGRAM_RS_WAIT_PARITY_FROM_BACKUP_DATA,
	OPEN_BLOCK_RSMAP_FINISH,
} OpenBlockRSMapStateEnum_t;

typedef enum {
	OPEN_BLOCK_RSMAP_PROGRAM_PARITY_DATA_INIT,
	OPEN_BLOCK_RSMAP_PROGRAM_PARITY_DATA,
	OPEN_BLOCK_RSMAP_PROGRAM_WAIT_PROGRAM_PARITY_DATA,
	OPEN_BLOCK_RSMAP_PROGRAM_FINISH
} OpenBlockRSMapProgramState_t;

typedef struct {
	U32 ulExternalPBFlag;
	RaidECCMapBufInformation_t RSBufInformation;
	U8 ubEncodedPBCnt;
	U8 ubParityEncodeCnt;
	U8 ubVirtualPBIdx;
	U8 ubEncodedPBIdx;
} BackupRSRegInformation_t;
typedef struct {
	U8 ubRecordLBIdx;
	struct {
		U8 ubProgramParityDoing;
	} Flag;
} OpenBlockRSBackup_t;
typedef struct {
	struct {
		U8 ubRecordLBOffsetIdx;
	} Buf;
	U8 ubDMACCopyCnt;
} OpenBlockRSSaveLoadInformation_t;

typedef struct {
	U16 uwFWParityTagIdx;
	U8  ubHWParityTagIdx;
	U8 ubIsProgramParityInGR;
	U8 ubProcessParityDone;
	U8 ubLastPageEncodeDone;
	BackupRSRegInformation_t BackupRegInformation;
	OpenBlockRSMapProgramState_t State;
} OpenBlockRSMapDescription_t;
typedef struct {
	U64 uoProgramParityDoneBMP;
	U8 ubDoing;
	U8 ubDoingIdx;
	U8 ubRecordIdx;
	U8 ubProgramIdx;
	U8 ubProgramParityCnt;
	struct {
		U16 uwTagID;
		U8 	ubDoing;
		U8  ubProgramWithMultiPlane;
	} Program;
	struct {
		U8 ubProgramParity;
		U8 ubForceProgramParity;
		U8 ubRSForceSaveDoing;
	} Flag;
	OpenBlockRSSaveLoadInformation_t SaveLoadInformation;
	OpenBlockRSMapDescription_t   RSDescription[RAIDECCMAP_OPEN_BLOCK_DESCRIPTION_NUM];
	OpenBlockRSMapStateEnum_t State;
} OpenBlockRSMapInformation_t;

static void OpenBlockRSMapAllChannelSetForceEmpty(void);
static U8 OpenBlockRSMapCheckNeedGCOpenBlockRSUnit(void);

#if (OPEN_BLOCK_RSMAP_EN)
static OpenBlockRSMapInformation_t gOpenBlockRSMapInformation;

static void OpenBlockRSMapProgramBackupParityData_Callback(TIEOUT_FORMAT_t uoResult)
{
	U16 uwTagId = (U16)uoResult.HL.B32_to_B63.Info.uwTAG & TAG_MASK;
	while (M_DB_GET_RD_CNT(DB_COP0_CQ) < (PARITY_BACKUP_P4K_WORKAROUND_CQ_CNT + 1));
	M_DB_TRIGGER_READ_CNT(DB_COP0_CQ, PARITY_BACKUP_P4K_WORKAROUND_CQ_CNT);
	if (TagCheckDone(COP0_CMD_TABLE_ID, uwTagId)) {
		gOpenBlockRSMapInformation.State = OPEN_BLOCK_RSMAP_FINISH;
	}
}

static void OpenBlockMapDMACSaveDone_CallBack(DMACCQRst_t *DMACCQRst)
{
	gOpenBlockRSMapInformation.SaveLoadInformation.ubDMACCopyCnt--;
}

static void OpenBlockRSMapVarInit(OpenBlockRSInitVarEnum_t InitMode, OpenBlockRSMapInformation_t *pOpenBlockRSMapInformation)
{
	U8 ubIdx = 0;
	pOpenBlockRSMapInformation->ubDoingIdx   = 0;
	pOpenBlockRSMapInformation->ubRecordIdx  = 0;
	pOpenBlockRSMapInformation->ubProgramIdx = 0;
	pOpenBlockRSMapInformation->ubProgramParityCnt = 0;
	pOpenBlockRSMapInformation->uoProgramParityDoneBMP = 0;
	// Init Flag
	pOpenBlockRSMapInformation->Flag.ubProgramParity = FALSE;
	pOpenBlockRSMapInformation->Flag.ubForceProgramParity = FALSE;
	pOpenBlockRSMapInformation->SaveLoadInformation.ubDMACCopyCnt = 0;
	pOpenBlockRSMapInformation->Program.ubProgramWithMultiPlane = FALSE;

	if (OPEN_BLOCK_RSMAP_INIT_VAR_FOR_FW_INIT == InitMode) {
		pOpenBlockRSMapInformation->Program.uwTagID = TAG_ID_ALLOCATE;
		pOpenBlockRSMapInformation->Program.ubDoing = 0;
		pOpenBlockRSMapInformation->State			 = OPEN_BLOCK_RSMAP_INIT;
		pOpenBlockRSMapInformation->Flag.ubRSForceSaveDoing = FALSE;
		pOpenBlockRSMapInformation->SaveLoadInformation.Buf.ubRecordLBOffsetIdx = 0;
		//YTTTTT
		if (FALSE) { //!gpVT->GR.ubCurrentGRIsSLCMode) {
			BufferAllocateFWLBPBLink( FWLB_OPEN_BLOCK_RSMAP_BACKUP_BIT, ALLOCATE_NO_USE_CTAG, ALLOCATE_NO_USE_LCA);
		}
	}

	for (ubIdx = 0; ubIdx < RAIDECCMAP_OPEN_BLOCK_DESCRIPTION_NUM; ubIdx++) {
		pOpenBlockRSMapInformation->RSDescription[ubIdx].State = OPEN_BLOCK_RSMAP_PROGRAM_PARITY_DATA_INIT;
		pOpenBlockRSMapInformation->RSDescription[ubIdx].ubIsProgramParityInGR = FALSE;
		pOpenBlockRSMapInformation->RSDescription[ubIdx].ubProcessParityDone = FALSE;
		pOpenBlockRSMapInformation->RSDescription[ubIdx].ubLastPageEncodeDone  = FALSE;
	}
}

static OpenBlockRSMapSubFlowResult_t OpenBlockRSMapSendProgramBackupParityData(U16 *puwTagId, PCA_t ulPCA, U16 uwDataLBOffset, U32 ulSpareAddr, U8 ubBackupIdx, U8 ubMultiPlaneMode)
{
	COP0WriteSQPara_t WriteSQParameter = {{0}};
	COP0Status_t eCOP0Status;
	cmd_table_t uoCallbackInformation = {(U32)OpenBlockRSMapProgramBackupParityData_Callback, {0}};
	BUF_TYPE_t pulBufParameter[FRAMES_PER_PAGE] = {{0}};
	FWSetPara_t pulFWSet[FRAMES_PER_PAGE] = {{0}};
	U32 pulLCA[FRAMES_PER_PAGE] = {0};
	U8 ub4kEntryIdx = 0;
#if PS5017_EN
	U32 ulIRAMOffsetBase = DBUF_GC_BACKUP_OPEN_BLOCK_RSMAP_SPARE_BASE + (ubBackupIdx * OPEN_BLOCK_RSMAP_RS_SPARE_DATA_SIZE_PER_PLANE);	// E19
#else /*PS5017_EN*/
	U32 ulIRAMOffsetBase = GC_BACKUP_OPEN_BLOCK_RSMAP_SPARE_OFF + (ubBackupIdx * OPEN_BLOCK_RSMAP_RS_SPARE_DATA_SIZE_PER_PLANE);
#endif /*PS5017_EN*/
	U8 ubLocalZByte;
	BMUCmdResult_t BMUResult;
	U8 ubStatus;
	U8 ubFrame2P4KRandomValue;
	U8 ubFrame3P4KRandomValue;
	P4KTable16B_t *pulP4KTablePtr = 0;
	OpenBlockRSMapSubFlowResult_t Rseult = OPEN_BLOCK_RSMAP_SUB_FLOW_DONE;

	M_FWPCA_ZBYTE_GET(ubLocalZByte, ulPCA.ulAll);

#if PS5017_EN
	WriteSQParameter.ulIRAMOffset = (ulIRAMOffsetBase);  // E19
	//WriteSQParameter.ulIRAMOffset = ((IRAM_AXI_BASE + ulIRAMOffsetBase));
#else /*PS5017_EN*/
	WriteSQParameter.ulIRAMOffset = ((IRAM_AXI_BASE + ulIRAMOffsetBase));
#endif /*PS5017_EN*/
	// Copy Spare data to IRAM
	ARM_DCACHE_INVALIDATE_RANGE((U32)ulSpareAddr, OPEN_BLOCK_RSMAP_RS_SPARE_DATA_SIZE_PER_PLANE);
	memcpy((U32 *)(WriteSQParameter.ulIRAMOffset), (U32 *)ulSpareAddr, OPEN_BLOCK_RSMAP_RS_SPARE_DATA_SIZE_PER_PLANE);

	pulP4KTablePtr = (P4KTable16B_t *) (WriteSQParameter.ulIRAMOffset);
	ubFrame2P4KRandomValue = (U8)(pulP4KTablePtr[(2)].Para0x0C.BitMap.RAND);
	ubFrame3P4KRandomValue = (U8)(pulP4KTablePtr[(3)].Para0x0C.BitMap.RAND);
	for (ub4kEntryIdx = 0; ub4kEntryIdx < gub4kEntrysPerPlane; ub4kEntryIdx++) {
		do {
			ubStatus = BMUAPICmdGetLBNA(BMU_CMD_NEED_CQ, LB_ID_FW, (uwDataLBOffset + ub4kEntryIdx), &BMUResult);
		} while (BMU_CMD_STATUS_SUCCESS != ubStatus);
		pulBufParameter[ub4kEntryIdx].A.ulBUF_ADR = M_FIP_GET_BACKUP_P4K_WORKAROUND_ADDR(M_BMU_GET_PB_FROM_GET_LBNA_CQ(&BMUResult), ubFrame2P4KRandomValue, ubFrame3P4KRandomValue);
#if PS5017_EN
		pulFWSet[ub4kEntryIdx].ulFWSet = DBUF_GC_BACKUP_OPEN_BLOCK_RSMAP_SPARE_IDX + ub4kEntryIdx; // E19
#else /*PS5017_EN*/
		pulFWSet[ub4kEntryIdx].ulFWSet = ulIRAMOffsetBase + (OPEN_BLOCK_RSMAP_RS_SPARE_DATA_SIZE * ub4kEntryIdx);
#endif /*PS5017_EN*/
		M_ZINFO_FROM_ZBYTE_GET(pulFWSet[ub4kEntryIdx].ubZInfo, ubLocalZByte);
		pulLCA[ub4kEntryIdx] = SPARE_LCA_PARITY;
	}

	COP0API_FillCOP0WriteSQUserData0Para(COP0_W_RAIDECC_PARITY_PROGRAM, &WriteSQParameter);
	WriteSQParameter.UserData0.ubMultiPlaneMode = ubMultiPlaneMode;
	WriteSQParameter.UserData0.btSLCMode = gSystmAreaFWSettingFromInfoBlk.OpenBlkRaidECCMap.Setup.Flag.btSLCProgram;
	WriteSQParameter.UserData0.uwTagID = *puwTagId;
	WriteSQParameter.UserData0.ubDataDef |= COP0_CONVPAGE_EN_BIT;
	WriteSQParameter.UserData0.ubAttrMTTemplate = COP0_MT_TEMP_BUF_LBPB_COPY_UNIT_CRC_DIS_LCACMP_EN;
	WriteSQParameter.UserData0.btBackupP4KWorkaroundProgram = TRUE;
	WriteSQParameter.ulPCA = ulPCA;                      // PCA
	WriteSQParameter.BufVld.ubBufType = BUF_TYPE_A;
	WriteSQParameter.BufVld.pulBufInfoPtr = pulBufParameter;
	WriteSQParameter.pulFWSetPtr = pulFWSet;
	WriteSQParameter.pulLCAPtr = pulLCA;
	WriteSQParameter.uoWriteConvRsInfo.ubRSTarget = RS_OPEN_BLOCK_RSMAP_PARITY;
	WriteSQParameter.uoWriteConvRsInfo.btConvOnly = TRUE;
	WriteSQParameter.UserData0.btParityLCA = TRUE;
	WriteSQParameter.uoWriteConvRsInfo.btConvPageEn = TRUE;
	if (FALSE == RANDOMIZER_3D_AGITATION_RULE) {
		WriteSQParameter.ulSeed.Seed = RS_PARITY_RANDOM_SEED;
	}

	do { /* parasoft-suppress BD-PB-CC "eCOP0Status.btSQFull will be update in COP0API_SendWriteSQ"*/
		eCOP0Status = COP0API_SendWriteSQ(&WriteSQParameter, &uoCallbackInformation);

		if ((eCOP0Status.btSQFull) && (!gSystmAreaFWSettingFromInfoBlk.OpenBlkRaidECCMap.Setup.Flag.btSLCProgram)) {
			FWCop0Waiting();
			RSDelegateCmd();
		}
		else {
			break;
		}
	} while (eCOP0Status.btSQFull);

	*puwTagId = WriteSQParameter.UserData0.uwTagID;

	if (!eCOP0Status.btSendCmdSuccess) {
		Rseult = OPEN_BLOCK_RSMAP_SUB_FLOW_DOING;
	}
	return Rseult;
}

static OpenBlockRSMapSubFlowResult_t OpenBlockRSMapCheckEncodeDone(OpenBlockRSMapDescription_t *pRSDescription)
{
	U8 ubCurrentRSTag = pRSDescription->ubHWParityTagIdx;
	U8 ubFWEncodedCnt;
	U8 ubEffectiveEncodeCnt = gubPlanesPerSuperPage + 1;

	if (pRSDescription->ubIsProgramParityInGR) {
		ubFWEncodedCnt = RaidECCMapCalateLastEncodeCnt(RS_DATA_ENCODE, ubCurrentRSTag) + 1;
	}
	else {
		ubFWEncodedCnt = RaidECCMapCalateOBEncodeCnt(ubCurrentRSTag);
	}

	OpenBlockRSMapSubFlowResult_t Result = OPEN_BLOCK_RSMAP_SUB_FLOW_DONE;

	gRaidECCMap.ubDebugEncodeCnt = ((ubFWEncodedCnt == ubEffectiveEncodeCnt) ? ubFWEncodedCnt : (ubFWEncodedCnt % ubEffectiveEncodeCnt)); // temp solution
	if (!RaidECCmapCheckRaidECCEncodeDone(ubCurrentRSTag, gRaidECCMap.ubDebugEncodeCnt)) {
		Result = OPEN_BLOCK_RSMAP_SUB_FLOW_DOING;
	}

#if PS5017_EN
	if ((pRSDescription->ubIsProgramParityInGR) && (!M_FIP_CHECK_FORCE_EMPTY(gFlhEnv.ubChannelExistNum - 1, gFlhEnv.ubCENumberInCh[gFlhEnv.ubChannelExistNum - 1] - 1))) {
		Result = OPEN_BLOCK_RSMAP_SUB_FLOW_DOING;
	}
#endif /*PS5017_EN*/

	return Result;
}

static void OpenBlockMapSaveExternalParity(U8 ubExternalIdx, U32 ulBufAddrData, U32 ulBufAddrSpare, U8 ubCurrentRSTag)
{
	DMACParam_t DMACParameter;
	U32 ulSrcParityData = (gFWLBMgr.Type[FWLB_RS_DATA].uwLBOffset + (ubExternalIdx * OPEN_BLOCK_RSMAP_RS_DATA_IN_LB_OFFSET_NUM));
	U8 ubVirtualPBIdx;

	for (ubVirtualPBIdx = RAIDECCMAP_GR_VIRTUAL_PB_OFFSET_V2; ubVirtualPBIdx < RAIDECCMAP_GCGR_VIRTUAL_PB_OFFSET_V2; ++ubVirtualPBIdx) {
		M_FIP_SET_RS_PB_SELECT(ubVirtualPBIdx);
		if (M_FIP_IS_PARITY_BUF_EXIST()) {
			if (ubCurrentRSTag == M_FIP_GET_RS_TAG_BY_PB()) {
				break;
			}
		}
	}
	// Copy Spare data to BufAddrSpare
	ARM_DCACHE_INVALIDATE_RANGE((U32)gpulRaidECCSpare + (ubVirtualPBIdx * OPEN_BLOCK_RSMAP_RS_SPARE_DATA_SIZE_PER_PLANE), OPEN_BLOCK_RSMAP_RS_SPARE_DATA_SIZE_PER_PLANE);
	memcpy((U32 *)(ulBufAddrSpare), (U32 *)((U32)gpulRaidECCSpare + (ubVirtualPBIdx * OPEN_BLOCK_RSMAP_RS_SPARE_DATA_SIZE_PER_PLANE)), OPEN_BLOCK_RSMAP_RS_SPARE_DATA_SIZE_PER_PLANE);
	ARM_DCACHE_CLEAN_RANGE((ulBufAddrSpare), OPEN_BLOCK_RSMAP_RS_SPARE_DATA_SIZE_PER_PLANE);
	//DMAC Copy
	DMACParameter.ulSourceAddr = M_LB_TO_ADDR(LB_ID_FW, ulSrcParityData);
	DMACParameter.ulDestAddr   = ulBufAddrData;
	DMACParameter.ul32ByteNum  = SIZE_IN_32B(OPEN_BLOCK_RSMAP_RS_PARITY_DATA_SIZE);
	DMACCopy(DMAC_MODE_HIGH_QUEUE, &DMACParameter, (U32)OpenBlockMapDMACSaveDone_CallBack, NULL);

	gOpenBlockRSMapInformation.SaveLoadInformation.ubDMACCopyCnt++;
}

void OpenBlockRSMapAllChannelSetForceEmpty(void)
{
	U8 ubChannel = 0;
	for (ubChannel = 0; ubChannel < (1 << gPCARule_Channel.ubBit_No); ubChannel++) {
		FlaMTQManualAbort(ubChannel, NEED_ABORT_DMA);
	}
}

static void OpenBlockRSMapAllChannelClearForceEmpty(U8 ubCheck)
{
	U8 ubChannelIdx = 0;
	for (ubChannelIdx = 0; ubChannelIdx < (1 << gPCARule_Channel.ubBit_No); ubChannelIdx++) {
		FIPResumeMTQManualAbort(ubChannelIdx);
		FIPClearChannelForceEmpty(ubChannelIdx, ubCheck);
	}
}

static OpenBlockRSMapSubFlowResult_t OpenBlockRSMapProgramBackupParityFlow(U8 ubProgramIdx, U16 *puwTagID, U8 ubMultiPlaneMode)
{
	U16 uwRSBaseLBOffset = gFWLBMgr.Type[FWLB_OPEN_BLOCK_RSMAP_BACKUP].uwLBOffset;
	U16 uwLBOffset = uwRSBaseLBOffset + (ubProgramIdx * OPEN_BLOCK_RSMAP_RS_DATA_IN_LB_OFFSET_NUM);
	U32 ulRSSpareInDBufAddr = DBUF_RS_SPARE_DATA_BACKUP + (ubProgramIdx * OPEN_BLOCK_RSMAP_RS_SPARE_DATA_SIZE_PER_PLANE);
	OpenBlockRSMapSubFlowResult_t Result;
	PCA_t ulPCA = {0};
	U32 ulLocalPCA;
	U8 ubLocalCrossFrame;
	U8 ubLocalZByte;
	U8 ubLocalLPCRC;
	ulLocalPCA = M_GET_VCA_PLANE(gpVT->OpenBlockRS.uwUnit.B.uwUnit, gpVT->OpenBlockRS.ulPlaneIdx);
	M_FWPCA_PCA_SET(ulPCA.ulAll, ulLocalPCA);
	ubLocalZByte = gPCAInfo.ubMaxZByte;
	ubLocalCrossFrame = FALSE;
	ubLocalLPCRC = 0;
	M_FWPCA_SET(ulPCA.ulAll, ulLocalPCA, ubLocalZByte, ubLocalCrossFrame, ubLocalLPCRC);
	Result = OpenBlockRSMapSendProgramBackupParityData(puwTagID, ulPCA, uwLBOffset, ulRSSpareInDBufAddr, ubProgramIdx, ubMultiPlaneMode);
	if (OPEN_BLOCK_RSMAP_SUB_FLOW_DONE == Result) {
		gpVT->OpenBlockRS.ulPlaneIdx++;
	}
	return Result;
}

void OpenBlockRSMapInit(OpenBlockRSInitVarEnum_t InitMode)
{
	OpenBlockRSMapVarInit(InitMode, &gOpenBlockRSMapInformation);
}

void OpenBlockRSMapTriggerFlow(void)
{
	gOpenBlockRSMapInformation.ubDoing = TRUE;
	gOpenBlockRSMapInformation.Flag.ubRSForceSaveDoing = TRUE;
	M_UART(RAIDECCMAP_DEBUG_, "\nSet OB doing");
}

U8 OpenBlockRSMapIsFlowDoing(void)
{
	return (gOpenBlockRSMapInformation.ubDoing);
}

U8 OpenBlockRSMapCheckProgramParityFlag(void)
{
	U8 ubResult = FALSE;
	if (OPEN_BLOCK_RSMAP_EN) {
		U8 ubCheckEncodedDoneIdx;
		OpenBlockRSMapInformation_t *pOpenBlockRSMapInformation = &gOpenBlockRSMapInformation;
		if (gOpenBlockRSMapInformation.Flag.ubProgramParity) {
			for (ubCheckEncodedDoneIdx = pOpenBlockRSMapInformation->ubDoingIdx; ubCheckEncodedDoneIdx < pOpenBlockRSMapInformation->ubRecordIdx; ubCheckEncodedDoneIdx++) {
				OpenBlockRSMapDescription_t *pCheckRSDescription = &pOpenBlockRSMapInformation->RSDescription[ubCheckEncodedDoneIdx];
				if (pCheckRSDescription->ubIsProgramParityInGR) {
					ubResult = TRUE;
					break;
				}
			}
		}
	}
	return ubResult;
}

U8 OpenBlockRSMapGetCurrentParityTag(void)
{
	U8 ubParityTag = 0;
	U8 ubCheckEncodedDoneIdx;
	OpenBlockRSMapInformation_t *pOpenBlockRSMapInformation = &gOpenBlockRSMapInformation;
	for (ubCheckEncodedDoneIdx = pOpenBlockRSMapInformation->ubDoingIdx; ubCheckEncodedDoneIdx < pOpenBlockRSMapInformation->ubRecordIdx; ubCheckEncodedDoneIdx++) {
		OpenBlockRSMapDescription_t *pCheckRSDescription = &pOpenBlockRSMapInformation->RSDescription[ubCheckEncodedDoneIdx];
		if (pCheckRSDescription->ubIsProgramParityInGR) {
			ubParityTag = pCheckRSDescription->ubHWParityTagIdx;
			break;
		}
	}
	return ubParityTag;
}

void OpenBlockRSMapForceProgramParity(void)
{
	gOpenBlockRSMapInformation.Flag.ubForceProgramParity = TRUE;
}

void OpenBlockRSMapRecordRSDescription(U16 uwFWParityTagIdx, U8 ubHWParityTagIdx, U8 ubIsProgramParityInGR, U8 ubLastPageEncodeDone)
{
	OpenBlockRSMapDescription_t *pRSDescription = &gOpenBlockRSMapInformation.RSDescription[gOpenBlockRSMapInformation.ubRecordIdx];
	pRSDescription->uwFWParityTagIdx = uwFWParityTagIdx;
	pRSDescription->ubHWParityTagIdx = ubHWParityTagIdx;
	pRSDescription->ubIsProgramParityInGR = ubIsProgramParityInGR;
	pRSDescription->ubLastPageEncodeDone = ubLastPageEncodeDone;
	gOpenBlockRSMapInformation.ubRecordIdx ++;
	if (ubIsProgramParityInGR) {
		gOpenBlockRSMapInformation.Flag.ubProgramParity = TRUE;
	}
}

static OpenBlockRSMapSubFlowResult_t OpenBlockRSMapBackupRSFlow(OpenBlockRSBackupModeEnum_t ubMode)
{
	OpenBlockRSMapInformation_t *pOpenBlockRSMapInformation = &gOpenBlockRSMapInformation;
	OpenBlockRSMapSubFlowResult_t ubFlowResult = OPEN_BLOCK_RSMAP_SUB_FLOW_DOING;
	do {
		U8 ubDoingFlow = TRUE;
		U8 ubCheckEncodedDoneIdx;
		M_RAIDECC_WAIT_RAIDECC_IDLE();
		/*******************************************************************************************************************
		 * 	Check Encode Cnt Done
		********************************************************************************************************************/
		for (ubCheckEncodedDoneIdx = pOpenBlockRSMapInformation->ubDoingIdx; ubCheckEncodedDoneIdx < pOpenBlockRSMapInformation->ubRecordIdx; ubCheckEncodedDoneIdx++) {
			OpenBlockRSMapDescription_t *pCheckRSDescription = &pOpenBlockRSMapInformation->RSDescription[ubCheckEncodedDoneIdx];
			if (OPEN_BLOCK_RSMAP_SUB_FLOW_DONE != OpenBlockRSMapCheckEncodeDone(pCheckRSDescription)) {
				ubDoingFlow = FALSE;
			}
			else {
				if (((MULTI_PLANE_PROTECTION) ? FALSE : (pCheckRSDescription->ubIsProgramParityInGR)) || ((pOpenBlockRSMapInformation->ubRecordIdx - 1) == ubCheckEncodedDoneIdx)) {
					break;
				}
			}
		}

		if (ubDoingFlow) {
			U8 ubBreakLoop = FALSE;
			U8 ubAlreadyClearForceEmpty = FALSE;
			OpenBlockRSMapDescription_t *pCurrentRSDescription = 0;
			OpenBlockRSMapAllChannelSetForceEmpty();
			do {
				pCurrentRSDescription = &pOpenBlockRSMapInformation->RSDescription[pOpenBlockRSMapInformation->ubDoingIdx];
				if (MULTI_PLANE_PROTECTION) {
					if ((0 == (pOpenBlockRSMapInformation->ubDoingIdx % PLANE_BANK_NUM)) && (0 != pOpenBlockRSMapInformation->uoProgramParityDoneBMP)) {
						ubBreakLoop = TRUE;
						for (U8 ubIdx = 0; ubIdx < pOpenBlockRSMapInformation->ubDoingIdx; ubIdx++) {
							U8 ubParityTagIdx = pOpenBlockRSMapInformation->RSDescription[ubIdx].ubHWParityTagIdx;
							if (pOpenBlockRSMapInformation->uoProgramParityDoneBMP & BIT(ubParityTagIdx)) {
								if (FIPCheckMTQQueueIsProgramParityForOpenBlockRaidECC(gFlhEnv.ubChannelExistNum - 1, gFlhEnv.ubCENumberInCh[gFlhEnv.ubChannelExistNum - 1] - 1, ubParityTagIdx)) {
									OpenBlockRSMapAllChannelClearForceEmpty(FALSE);
									ubBreakLoop = FALSE;
									ubAlreadyClearForceEmpty = TRUE;
									break;
								}
							}
						}
						break;
					}
				}
				/*******************************************************************************************************************
				 * 			RS Force Save Flow
				********************************************************************************************************************/
				RaidECCMapBufInformation_t *pRaidECCBufInformation = &(pCurrentRSDescription->BackupRegInformation.RSBufInformation);
				U8 *pRecordLBOffsetIdx = &pOpenBlockRSMapInformation->SaveLoadInformation.Buf.ubRecordLBOffsetIdx;
				U8 ubCurrentRSTag = pCurrentRSDescription->ubHWParityTagIdx;
				U16 uwRSBaseLBOffset = gFWLBMgr.Type[FWLB_OPEN_BLOCK_RSMAP_BACKUP].uwLBOffset;
				U16 uwLBOffset = uwRSBaseLBOffset + ((*pRecordLBOffsetIdx) * OPEN_BLOCK_RSMAP_RS_DATA_IN_LB_OFFSET_NUM);
				U32 ulRSDataInDBufAddr = M_LB_TO_ADDR(LB_ID_FW, uwLBOffset);
				U32 ulRSSpareInDBufAddr = DBUF_RS_SPARE_DATA_BACKUP + ((*pRecordLBOffsetIdx) * OPEN_BLOCK_RSMAP_RS_SPARE_DATA_SIZE_PER_PLANE);
				M_ARM_CHECK_RAIDECC_REG_REQUEST_START();
				M_RAIDECC_WAIT_RAIDECC_IDLE();	// wait RS IP idle
				RaidECCMapGetBufIndex(pRaidECCBufInformation, ubCurrentRSTag);
				if (RAIDECCMAP_INTERNAL_BUF == pRaidECCBufInformation->Loc) {
					M_RAIDECC_WAIT_RAIDECC_IDLE();	// wait RS IP idle
					if (OPEN_BLOCK_RSMAP_PERFORMANCE_ANALYSIS_EN) {
						M_SET_GPIO_O(4, (gubGPIO[4] ^= BIT0));
					}
					RaidECCForceSave(pRaidECCBufInformation->ubBufIdx, ulRSDataInDBufAddr, ulRSSpareInDBufAddr, TRUE);
					if (OPEN_BLOCK_RSMAP_PERFORMANCE_ANALYSIS_EN) {
						M_SET_GPIO_O(4, (gubGPIO[4] ^= BIT0));
					}
				}
				else if (RAIDECCMAP_EXTERNAL_BUF == pRaidECCBufInformation->Loc ) {
					OpenBlockMapSaveExternalParity(pRaidECCBufInformation->ubBufIdx, ulRSDataInDBufAddr, ulRSSpareInDBufAddr, ubCurrentRSTag);
					while (gOpenBlockRSMapInformation.SaveLoadInformation.ubDMACCopyCnt) {
						DMACDelegateCmd( DMAC_MODE_HIGH_QUEUE);
					}
				}
				M_ARM_CHECK_RAIDECC_REG_REQUEST_END();
				(*pRecordLBOffsetIdx)++;
				pOpenBlockRSMapInformation->ubDoingIdx ++;
				if (MULTI_PLANE_PROTECTION) {
					if (pCurrentRSDescription->ubIsProgramParityInGR) {
						pOpenBlockRSMapInformation->uoProgramParityDoneBMP |= BIT(ubCurrentRSTag);
						pOpenBlockRSMapInformation->ubProgramParityCnt--;
					}
					if (0 == (pOpenBlockRSMapInformation->ubProgramParityCnt % PLANE_BANK_NUM)) {
						break;
					}
				}
			} while (pOpenBlockRSMapInformation->ubDoingIdx <= ubCheckEncodedDoneIdx);
			if (ubAlreadyClearForceEmpty) {
				break;
			}
			else if (ubBreakLoop) {
				OpenBlockRSMapAllChannelClearForceEmpty(TRUE);
				break;
			}
			else {
				U8 ubIndex = (MULTI_PLANE_PROTECTION) ? (pOpenBlockRSMapInformation->ubDoingIdx - 1) : ubCheckEncodedDoneIdx;
				pCurrentRSDescription = &pOpenBlockRSMapInformation->RSDescription[ubIndex];
				if ((pCurrentRSDescription->ubIsProgramParityInGR)
					|| (0 == pOpenBlockRSMapInformation->ubProgramParityCnt)
					|| (pOpenBlockRSMapInformation->ubDoingIdx == pOpenBlockRSMapInformation->ubRecordIdx)) {
					if ((FALSE == MULTI_PLANE_PROTECTION) && pCurrentRSDescription->ubIsProgramParityInGR) {/* parasoft-suppress BD-PB-CC "Manual Switch for MULTI_PLANE_PROTECTION" */
						pOpenBlockRSMapInformation->ubProgramParityCnt--;
					}
					OpenBlockRSMapAllChannelClearForceEmpty(FALSE);
				}
				else {
					OpenBlockRSMapAllChannelClearForceEmpty(TRUE);
				}
			}
		}
		else {
			break;
		}
	} while (pOpenBlockRSMapInformation->ubDoingIdx < pOpenBlockRSMapInformation->ubRecordIdx);

	if (pOpenBlockRSMapInformation->ubDoingIdx == pOpenBlockRSMapInformation->ubRecordIdx) {
		ubFlowResult = OPEN_BLOCK_RSMAP_SUB_FLOW_DONE;
	}

	return ubFlowResult;
}

void OpenBlockRSMapBackupRSForce(void)
{
	if (OPEN_BLOCK_RSMAP_EN) {
		if (M_DB_CHECK_EMPTY(DB_FLH_MSG_CQ) && M_DB_CHECK_EMPTY(DB_ERR_MSG_CQ)) {
			if (FIPCheckChannelForceEmpty(M_MOD((gubCENumber - 1), gubPlanesPerBurst))) {
				if (OpenBlockRSMapIsFlowDoing()) {
					while (gOpenBlockRSMapInformation.State <= OPEN_BLOCK_WAIT_PROGRAM_PARITY_DONE) {
						OpenBlockRSMapFlow(OPEN_BLOCK_RSMAP_BACKUP_RS_FORCE_DONE);
						FWErrRecorder();
						RSDelegateCmd();
						if (MULTI_PLANE_PROTECTION) {
							if ((0 == FIPGetNonExecuteMTNum(gFlhEnv.ubChannelExistNum - 1, gFlhEnv.ubCENumberInCh[gFlhEnv.ubChannelExistNum - 1] - 1)) || (!M_DB_CHECK_EMPTY(DB_FLH_MSG_CQ)) ) {
								break;
							}
						}
					}
				}
			}
		}
	}
}

void OpenBlockRSMapFlow(OpenBlockRSBackupModeEnum_t ubMode)
{
	OpenBlockRSMapInformation_t *pOpenBlockRSMapInformation = &gOpenBlockRSMapInformation;
	switch (pOpenBlockRSMapInformation->State) {
	case OPEN_BLOCK_RSMAP_INIT: {
			U8 ubIdx = 0;
			for (ubIdx = 0; ubIdx < pOpenBlockRSMapInformation->ubRecordIdx; ubIdx++) {
				OpenBlockRSMapDescription_t *pCurrentRSDescription = &pOpenBlockRSMapInformation->RSDescription[ubIdx];
				if (pCurrentRSDescription->ubIsProgramParityInGR) {
					pOpenBlockRSMapInformation->ubProgramParityCnt++;
				}
			}
			if (OPEN_BLOCK_RSMAP_PERFORMANCE_ANALYSIS_EN) {
				M_SET_GPIO_O(0, (gubGPIO[0] ^= BIT0));
			}
			pOpenBlockRSMapInformation->State = OPEN_BLOCK_RSMAP_CORE_FLOW;
			if (OPEN_BLOCK_RSMAP_PERFORMANCE_ANALYSIS_EN) {
				M_SET_GPIO_O(1, (gubGPIO[1] ^= BIT0));
			}
		}
	// no break
	case OPEN_BLOCK_RSMAP_CORE_FLOW: {
			OpenBlockRSMapSubFlowResult_t ubBackupRSResult;
			ubBackupRSResult = OpenBlockRSMapBackupRSFlow(ubMode);
			if (OPEN_BLOCK_RSMAP_SUB_FLOW_DONE == ubBackupRSResult) {
				if (MULTI_PLANE_PROTECTION) {
					pOpenBlockRSMapInformation->State = OPEN_BLOCK_WAIT_PROGRAM_PARITY_DONE;
				}
				else {
					pOpenBlockRSMapInformation->Flag.ubProgramParity = FALSE;
					pOpenBlockRSMapInformation->State = OPEN_BLOCK_RSMAP_WAIT_DMAC_COPY_DONE;
					break;
				}
				if (OPEN_BLOCK_RSMAP_PERFORMANCE_ANALYSIS_EN) {
					M_SET_GPIO_O(1, (gubGPIO[1] ^= BIT0));
				}
			}
			else {
				break;
			}
		}
	// no break
	case OPEN_BLOCK_WAIT_PROGRAM_PARITY_DONE: {
			if (0 == pOpenBlockRSMapInformation->uoProgramParityDoneBMP) {
				pOpenBlockRSMapInformation->State = OPEN_BLOCK_RSMAP_WAIT_DMAC_COPY_DONE;
			}
			else {
				RSDelegateCmd();
				if (0 == pOpenBlockRSMapInformation->uoProgramParityDoneBMP) { /* parasoft-suppress BD-PB-CC "eCOP0Status.btSQFull will be update in OpenBlockRaidECCClearProgramParityDoneBMP"*/
					pOpenBlockRSMapInformation->State = OPEN_BLOCK_RSMAP_WAIT_DMAC_COPY_DONE;
				}
				else {
					for (U8 ubIdx = 0; ubIdx < pOpenBlockRSMapInformation->ubRecordIdx; ubIdx++) {
						U8 ubParityTagIdx = pOpenBlockRSMapInformation->RSDescription[ubIdx].ubHWParityTagIdx;
						if (pOpenBlockRSMapInformation->uoProgramParityDoneBMP & BIT64(ubParityTagIdx)) {
							if (FIPCheckMTQQueueIsProgramParityForOpenBlockRaidECC(gFlhEnv.ubChannelExistNum - 1, gFlhEnv.ubCENumberInCh[gFlhEnv.ubChannelExistNum - 1] - 1, ubParityTagIdx)) {
								OpenBlockRSMapAllChannelClearForceEmpty(FALSE);
							}
						}
					}
					break;
				}
			}
		}
	case OPEN_BLOCK_RSMAP_WAIT_DMAC_COPY_DONE:
		if (gOpenBlockRSMapInformation.SaveLoadInformation.ubDMACCopyCnt) {
			break;
		}
		pOpenBlockRSMapInformation->Flag.ubRSForceSaveDoing 	= FALSE;
		OpenBlockRSMapVarInit( OPEN_BLOCK_RSMAP_INIT_VAR_FOR_FLOW_INIT, pOpenBlockRSMapInformation );
		if (OPEN_BLOCK_RSMAP_BACKUP_RS_FORCE_DONE == ubMode) {
			break;
		}
		else {
#if(MICRON_FSP_EN)
			U8 ubPlaneFlushNum = FTLGetPlaneFlushNum( FALSE, (gpVT->GR.ulPlaneIndex - 1));
			if (( (ubPlaneFlushNum / gubBurstsPerBank ) == pOpenBlockRSMapInformation->SaveLoadInformation.Buf.ubRecordLBOffsetIdx) || (pOpenBlockRSMapInformation->Flag.ubForceProgramParity)) {
#else
			if ((BUF_OPEN_BLK_RAIDECCMAP_BACKUP_NUM == pOpenBlockRSMapInformation->SaveLoadInformation.Buf.ubRecordLBOffsetIdx) || (pOpenBlockRSMapInformation->Flag.ubForceProgramParity)) {
#endif
				//			if ((BUF_OPEN_BLK_RAIDECCMAP_BACKUP_NUM == pOpenBlockRSMapInformation->SaveLoadInformation.Buf.ubRecordLBOffsetIdx) || (pOpenBlockRSMapInformation->Flag.ubForceProgramParity)) {
				pOpenBlockRSMapInformation->State = OPEN_BLOCK_RSMAP_PROGRAM_RS_PARITY_FROM_BACKUP_DATA;
			}
			else {
				pOpenBlockRSMapInformation->State = OPEN_BLOCK_RSMAP_FINISH;
				break;
			}
		}
	//no break
	case OPEN_BLOCK_RSMAP_PROGRAM_RS_PARITY_FROM_BACKUP_DATA: {
			U16 *puwTagID = &pOpenBlockRSMapInformation->Program.uwTagID;
#if(MICRON_FSP_EN)
			U8 ubProgramRSNum = pOpenBlockRSMapInformation->SaveLoadInformation.Buf.ubRecordLBOffsetIdx;
#else
			U8 ubProgramRSNum = BUF_OPEN_BLK_RAIDECCMAP_BACKUP_NUM;
#endif
			U8 *pubProgramRSIdx = &pOpenBlockRSMapInformation->Program.ubDoing;
			U8 ubMultiPlaneMode;
			OpenBlockRSMapSubFlowResult_t ubResult = OPEN_BLOCK_RSMAP_SUB_FLOW_DONE;
			if (OPEN_BLOCK_RSMAP_PERFORMANCE_ANALYSIS_EN) {
				M_SET_GPIO_O(5, (gubGPIO[5] ^= BIT0));
			}
			while ((*pubProgramRSIdx) < ubProgramRSNum) {
				U8 ubRecordProgramWithMultiPlane = FALSE;
				if (0 == M_FW_GET_PLANE(gpVT->OpenBlockRS.ulPlaneIdx)) {
					// First Plane
					if (*pubProgramRSIdx + gubBurstsPerBank <= ubProgramRSNum) {
						pOpenBlockRSMapInformation->Program.ubProgramWithMultiPlane = TRUE;
					}
				}

				if (pOpenBlockRSMapInformation->Program.ubProgramWithMultiPlane) {
					ubRecordProgramWithMultiPlane = TRUE;
					if ((gubBurstsPerBank - 1) == M_FW_GET_PLANE(gpVT->OpenBlockRS.ulPlaneIdx)) {
						ubMultiPlaneMode = MULTIPLANE_END;
						pOpenBlockRSMapInformation->Program.ubProgramWithMultiPlane = FALSE;
					}
					else {
						ubMultiPlaneMode = MULTIPLANE_ING;
					}
				}
				else {
					ubMultiPlaneMode = MULTIPLANE_NO_USE;
				}

				ubResult = OpenBlockRSMapProgramBackupParityFlow(*pubProgramRSIdx, puwTagID, ubMultiPlaneMode);
				if (OPEN_BLOCK_RSMAP_SUB_FLOW_DOING == ubResult) {
					pOpenBlockRSMapInformation->Program.ubProgramWithMultiPlane = ubRecordProgramWithMultiPlane;
					TagCOP0SetCmdPending((*puwTagID), TRUE);
					break;
				}
				(*pubProgramRSIdx)++;
			}
			if (ubProgramRSNum == *pubProgramRSIdx) {
				pOpenBlockRSMapInformation->SaveLoadInformation.Buf.ubRecordLBOffsetIdx = 0;
				pOpenBlockRSMapInformation->State = OPEN_BLOCK_RSMAP_PROGRAM_RS_WAIT_PARITY_FROM_BACKUP_DATA;
				TagCOP0SetCmdPending((*puwTagID), FALSE);
			}
		}
	// no break
	case OPEN_BLOCK_RSMAP_PROGRAM_RS_WAIT_PARITY_FROM_BACKUP_DATA:
		break;
	case OPEN_BLOCK_RSMAP_FINISH:
		if (OPEN_BLOCK_RSMAP_PERFORMANCE_ANALYSIS_EN) {
			if (gubGPIO[5] == 1) {
				M_SET_GPIO_O(5, (gubGPIO[5] ^= BIT0));
			}
		}
		// ReInit Var
		pOpenBlockRSMapInformation->Program.uwTagID = TAG_ID_ALLOCATE;
		pOpenBlockRSMapInformation->Program.ubDoing = 0;
		pOpenBlockRSMapInformation->ubDoing = FALSE;
		M_UART(RAIDECCMAP_DEBUG_, "\nOB doing done");
		pOpenBlockRSMapInformation->State = OPEN_BLOCK_RSMAP_INIT;
		if (OPEN_BLOCK_RSMAP_PERFORMANCE_ANALYSIS_EN) {
			M_SET_GPIO_O(0, (gubGPIO[0] ^= BIT0));
		}
		break;
	}
}

U8 OpenBlockRSMapCheckNeedGCOpenBlockRSUnit(void)
{
	return (gpVT->OpenBlockRS.ulPlaneIdx + OPEN_BLOCK_RSMAP_GC_PLANES_PER_GR_UNIT * ((MULTI_PLANE_PROTECTION) ? PLANE_BANK_NUM : 1) >= ((!gSystmAreaFWSettingFromInfoBlk.OpenBlkRaidECCMap.Setup.Flag.btSLCProgram) ? gulPlanesPerUnit : gulFastPagePlanesPerUnit));
}

/*
*    Moudle name : OpenBlockRSMapCheckAddOldUnitToFreePool()
*
*    Concept : Add gpVT->OpenBlockRS.uwOldUnit/gpVT->OpenBlockRS.uwParityUnitOfPreviousGR to free pool
*
*    Mode :
*    OPEN_BLOCK_RSMAP_SPOR_GET_RETRUN_RESERVED_TABLE_UNIT_MODE : Return borrowed table resesrved unit. (borrow when )
*    OPEN_BLOCK_RSMAP_INIT_MODE                                : Original mode, return gpVT->OpenBlockRS.uwOldUnit
*    OPEN_BLOCK_RSMAP_NORMAL_MODE                              : Original mode, return gpVT->OpenBlockRS.uwOldUnit
*    OPEN_BLOCK_RSMAP_SPOR_OLD_GR_MODE                         : Return gpVT->OpenBlockRS.uwParityUnitOfPreviousGR
*    OPEN_BLOCK_RSMAP_SPOR_OLD_GR_NO_SAVE_VT_MODE              : Return gpVT->OpenBlockRS.uwParityUnitOfPreviousGR, but don't save VT
*
*/
U8 OpenBlockRSMapCheckAddOldUnitToFreePool(Unit_t uwUnit, OpenBlockRSInitMode_t ubInitMode)
{
	U8 ubResult = TRUE;
	U8 ubDoingAddFreeUnit = FALSE;
	U8 ubNeedUpdateVT = FALSE;

	if (READ_VERIFY_EN) {
		if (OPEN_BLOCK_RSMAP_SPOR_GET_RETRUN_RESERVED_TABLE_UNIT_MODE == ubInitMode) {
			if (gpVT->ReadVerify.ubNowDoingEvent & BIT(READ_VERIFY_SRC_SPOR_OLD_GR)) {
				if ( gpVT->OpenBlockRS.uwOldUnit.uwAll == gpVT->OpenBlockRS.uwParityUnitOfPreviousGR.uwAll) {
					gpVT->OpenBlockRS.uwOldUnit.uwAll = DEFAULT_UNIT_NUM;
					return TRUE;
				}
				else if (DEFAULT_UNIT_NUM == gpVT->OpenBlockRS.uwOldUnit.uwAll) {
					return TRUE;
				}
				else if (DEFAULT_UNIT_NUM == gpVT->OpenBlockRS.uwParityUnitOfPreviousGR.uwAll) {
					ubInitMode = OPEN_BLOCK_RSMAP_INIT_MODE;
				}
			}
			else {
				gpVT->OpenBlockRS.uwParityUnitOfPreviousGR.uwAll = DEFAULT_UNIT_NUM;
				ubInitMode = OPEN_BLOCK_RSMAP_INIT_MODE;
			}
		}
	}

	if (OPEN_BLOCK_RSMAP_SPOR_OLD_GR_MODE == ubInitMode) {
		ubDoingAddFreeUnit = TRUE;
		ubNeedUpdateVT     = TRUE;
	}
	else if (OPEN_BLOCK_RSMAP_SPOR_OLD_GR_NO_SAVE_VT_MODE  == ubInitMode) {
		ubDoingAddFreeUnit = TRUE;
		ubNeedUpdateVT     = FALSE;
	}
	else {
		if (DEFAULT_UNIT_NUM != gpVT->OpenBlockRS.uwOldUnit.uwAll) {
			if ((gpVT->OpenBlockRS.uwLastGRUnit.B.uwUnit == uwUnit.B.uwUnit) || (OPEN_BLOCK_RSMAP_INIT_MODE == ubInitMode) || (OPEN_BLOCK_RSMAP_SPOR_GET_RETRUN_RESERVED_TABLE_UNIT_MODE == ubInitMode)) {
				ubDoingAddFreeUnit = TRUE;
				if (OPEN_BLOCK_RSMAP_NORMAL_MODE == ubInitMode) {
					ubNeedUpdateVT = TRUE;
				}
			}
		}
	}

	if (ubDoingAddFreeUnit) {
		U8 ubAddUnitCheck = FALSE;
		if (ubNeedUpdateVT) {
			// save VT
			if (FALSE == gInitInfoVTHdlMgr.btLock) {
				if (InitInfoVTSetEvent(SAVE_INIT_INFO_VT_EVENT_OPEN_BLOCK_RAIDECC_UNIT)) {
					ubAddUnitCheck = TRUE;
				}
			}
		}
		else {
			ubAddUnitCheck = TRUE;
		}

		if (ubAddUnitCheck) {
			if ((OPEN_BLOCK_RSMAP_SPOR_OLD_GR_MODE == ubInitMode) || (OPEN_BLOCK_RSMAP_SPOR_OLD_GR_NO_SAVE_VT_MODE  == ubInitMode) ) {
				if (DEFAULT_UNIT_NUM != gpVT->OpenBlockRS.uwParityUnitOfPreviousGR.uwAll) {
					if (gpVT->OpenBlockRS.uwParityUnitOfPreviousGR.uwAll == gpVT->OpenBlockRS.uwOldUnit.uwAll) {
						gpVT->OpenBlockRS.uwOldUnit.uwAll = DEFAULT_UNIT_NUM;
					}
					FTLAddFreeUnit (MODE_OPEN_BLOCK_RSMAP_UNIT, FALSE, &gpVT->OpenBlockRS.uwParityUnitOfPreviousGR, ADD_FREE_OPEN_BLOCK_RS);
				}
				gpVT->OpenBlockRS.uwParityUnitOfPreviousGR.uwAll = DEFAULT_UNIT_NUM;
				gpVT->OpenBlockRS.ulPreviousBaseDecodePlaneIdx = 0;
			}
			else if (OPEN_BLOCK_RSMAP_SPOR_GET_RETRUN_RESERVED_TABLE_UNIT_MODE == ubInitMode) {
				FTLAddFreeUnit (MODE_SPOR_OPEN_BLOCK_RSMAP_PARITY_UNIT, FALSE, &gpVT->OpenBlockRS.uwOldUnit, ADD_FREE_OPEN_BLOCK_RS);
				gpVT->OpenBlockRS.uwOldUnit.uwAll 		= DEFAULT_UNIT_NUM;
				gpVT->OpenBlockRS.uwLastGRUnit.uwAll 	= DEFAULT_UNIT_NUM;
				gpVT->OpenBlockRS.ulBaseDecodePlaneIdxForLastGRUnitOfOldUnit = 0;

			}
			else {
				M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, 0 != gpVT->OpenBlockRS.uwOldUnit.B.uwUnit);
				if (DEFAULT_UNIT_NUM != gpVT->OpenBlockRS.uwOldUnit.uwAll) {
					FTLAddFreeUnit (MODE_OPEN_BLOCK_RSMAP_UNIT, FALSE, &gpVT->OpenBlockRS.uwOldUnit, ADD_FREE_OPEN_BLOCK_RS);
				}
				if ((gpVT->OpenBlockRS.uwOldUnit.uwAll == gpVT->OpenBlockRS.uwParityUnitOfPreviousGR.uwAll) && (FALSE == gpVT->FTL.btSPORDoing)) {
					gpVT->OpenBlockRS.uwParityUnitOfPreviousGR.uwAll = DEFAULT_UNIT_NUM;
				}
				gpVT->OpenBlockRS.uwOldUnit.uwAll 		= DEFAULT_UNIT_NUM;
				gpVT->OpenBlockRS.uwLastGRUnit.uwAll 	= DEFAULT_UNIT_NUM;
				gpVT->OpenBlockRS.ulBaseDecodePlaneIdxForLastGRUnitOfOldUnit = 0;
			}
		}
		ubResult = ubAddUnitCheck;
	}
	return ubResult;
}

U8 OpenBlockRSMapIsOldUnitCheckDone(void)
{
	return ((DEFAULT_UNIT_NUM == gpVT->OpenBlockRS.uwOldUnit.uwAll) &&  (0 == (gpVT->ReadVerify.ubNowDoingEvent & BIT(READ_VERIFY_SRC_SPOR_OLD_GR))));
}

U8 OpenBlockRSMapNeedToGetNewUnit(OpenBlockRSInitMode_t ubInitMode)
{
	U8 ubResult = FALSE;
#if OPEN_BLOCK_RSMAP_EN
	if (FALSE == gpVT->FTL.btSPORDoing) {
		gpVT->OpenBlockRS.ulPreviousBaseDecodePlaneIdx = gpVT->OpenBlockRS.ulBaseDecodePlaneIdx;
		gpVT->OpenBlockRS.uwParityUnitOfPreviousGR = gpVT->OpenBlockRS.uwUnit;
	}
	if (OpenBlockRSMapCheckNeedGCOpenBlockRSUnit()) {
		gpVT->OpenBlockRS.uwOldUnit = gpVT->OpenBlockRS.uwUnit;
		gpVT->OpenBlockRS.ulBaseDecodePlaneIdxForLastGRUnitOfOldUnit = gpVT->OpenBlockRS.ulBaseDecodePlaneIdx;
		if (OPEN_BLOCK_RSMAP_NORMAL_MODE == ubInitMode) {
			gpVT->OpenBlockRS.uwLastGRUnit = gpVT->GR.uwUnit[gpVT->GR.ubUnitIndex];
		}
		else {
			gpVT->OpenBlockRS.uwLastGRUnit.uwAll = DEFAULT_UNIT_NUM;
		}
		if (OPEN_BLOCK_RSMAP_SPOR_GET_RETRUN_RESERVED_TABLE_UNIT_MODE == ubInitMode) {
			FTLGetFreeUnit(MODE_D3, MODE_SPOR_OPEN_BLOCK_RSMAP_PARITY_UNIT, MODE_NON_STATIC, MODE_MIN_EC, &gpVT->OpenBlockRS.uwUnit);
			gpVTDBUF->PowerOnInit.SPORControl.btOpenBlkRaidECCParityGetUnitMode = OPEN_BLK_RAIDECCMAP_GET_UNIT_IN_TABLE_RESERVED_UNIT_MODE;
		}
		else {
			FTLGetFreeUnit(MODE_D3, MODE_OPEN_BLOCK_RSMAP_UNIT, MODE_NON_STATIC, MODE_MIN_EC, &gpVT->OpenBlockRS.uwUnit);
			gpVTDBUF->PowerOnInit.SPORControl.btOpenBlkRaidECCParityGetUnitMode = OPEN_BLK_RAIDECCMAP_GET_UNIT_IN_PARITY_UNIT_MODE;
		}
		gpVT->OpenBlockRS.ulPlaneIdx = 0;
		//init tag and bitmap
		gEraseUnitInfo.OpenBlockRS.uwEraseTagId = TAG_ID_ALLOCATE;
		memset(gEraseUnitInfo.OpenBlockRS.ubEraseBitMap, 0xFF, sizeof(gEraseUnitInfo.OpenBlockRS.ubEraseBitMap));
		ubResult = TRUE;

	}
	gpVT->OpenBlockRS.ulBaseDecodePlaneIdx = gpVT->OpenBlockRS.ulPlaneIdx;
#endif /*OPEN_BLOCK_RSMAP_EN*/
	return ubResult;
}

U8 OpenBlockRSMapEraseOpenBlockRSUnit(OpenBlockRSInitMode_t ubInitMode)
{
	U8 ubResult = RETURN_SUCCESS;
	U8 ubNeedWait = FALSE;
	if (OPEN_BLOCK_RSMAP_INIT_MODE == ubInitMode) {
		ubNeedWait = TRUE;
	}
	if (FALSE == FTLEraseUnit_Handler(MODE_D3, gpVT->OpenBlockRS.uwUnit, &gEraseUnitInfo.OpenBlockRS.uwEraseTagId, ubNeedWait, gEraseUnitInfo.OpenBlockRS.ubEraseBitMap, NULL, FALSE)) {
		ubResult = RETURN_FAIL;
	}
	return ubResult;
}

U8 OpenBlockRaidECCMapCheckCurrentStateIdle(void)
{
	U8 ubResult = FALSE;
	if ((gpVT->OpenBlockRS.ulPlaneIdx % guwSuperPagesPerUnit) == 0) {
		ubResult = TRUE;
	}
	return ubResult;
}

U8 OpenBlockRaidECCMapReturnIsCopyBusy(void)
{
	return (OPEN_BLOCK_RSMAP_WAIT_DMAC_COPY_DONE == gOpenBlockRSMapInformation.State);
}

U8 RaidECCReturnIsOpenBlockRaidECCProgramBusy(void)
{
	return (OPEN_BLOCK_RSMAP_PROGRAM_RS_WAIT_PARITY_FROM_BACKUP_DATA == gOpenBlockRSMapInformation.State);
}

void OpenBlockRaidECCClearProgramParityDoneBMP(U8 ubParityTagIdx)
{
	gOpenBlockRSMapInformation.uoProgramParityDoneBMP &= ~(BIT64(ubParityTagIdx));
}
void OpenBlkRaidECCMapClearOpenBlkDoing(void)
{
	gOpenBlockRSMapInformation.ubDoing = FALSE;
}

U8 OpenBlockRaidECCMapGetInformationForceSaveDoing(void)
{
	return (gOpenBlockRSMapInformation.Flag.ubRSForceSaveDoing);
}

void OpenBlockRaidECCMapCheckFlag(U8 ubMode, U32 ulUnitPTR, U8 ubIsSLCMode)
{
	//*************************************************************************************************************************************
	//		open block RS flow
	//*************************************************************************************************************************************
	U16 uwPage = 0;
	U8 ubPlaneBank = 0;
	RAIDECCMAPPlaneIdx2PhysicalInline(ulUnitPTR, &uwPage, &ubPlaneBank, ubIsSLCMode);

	//Check Need Program RS or not
	if (!ubIsSLCMode) {
		if ((RS_MODE_NORMAL == ubMode) || (RS_SPOR_DATA_ENCODE == ubMode)) {	// GR need protect
			// TLC
			if (ubPlaneBank >= (gubPlanesPerSuperPage - RAIDECCMAP_PLANE_PROTECT_NUM)) {

				gRS.ubRecordOpenBlockRSFlag = TRUE;	// Last Page In Super Page
#if MICRON_FSP_EN
				U8 ubPlaneFlushNum;
				U8 ubResidualPlaneNum;
				ubPlaneFlushNum = FTLGetPlaneFlushNum( ubIsSLCMode, ulUnitPTR );
				ubResidualPlaneNum = FTLResidualPlaneNumInWindow(ubIsSLCMode, ulUnitPTR);
#if ((IM_B47R) || IM_B37R || (IM_N48R_NEED_CHECK))
				if ((1 == ubResidualPlaneNum) && (ubPlaneBank == (gubPlanesPerSuperPage - 1)))
#else
				if (((ulUnitPTR % ubPlaneFlushNum) == (ubPlaneFlushNum - 1)) && (ubPlaneBank == (gubPlanesPerSuperPage - 1)))
#endif
#else /* MICRON_FSP_EN */
				if ((uwPage % gubLMUNumber) == (gubLMUNumber - 1) && ((MULTI_PLANE_PROTECTION) ? (ubPlaneBank == (gubPlanesPerSuperPage - 1)) : TRUE))
#endif /* MICRON_FSP_EN */
				{
					// Last FSP Page In FSP Super Page
					// Backup Parity
					gRS.ubTriggerOpenBlockRSFlag = TRUE;
					if (gRS.ubLastEncodePageFlag) {
						OpenBlockRSMapForceProgramParity();
					}
				}
			}
		}
	}

}
#endif /* (OPEN_BLOCK_RSMAP_EN) */

