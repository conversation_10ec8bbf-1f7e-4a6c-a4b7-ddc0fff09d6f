#ifndef _SYS_PD0_REG_5017_H_
#define _SYS_PD0_REG_5017_H_

#include "typedef.h"
#include "mem.h"
#include "symbol.h"

/*
 *  +----------------------------------------------------------------+
 *  |					System PD0 Register                          |
 *  | 				Offset:0xF8006000 ~ 0x0xF8008000                 |
 *  |----------------------------------------------------------------|
 *  | MISCL              | 0x6000 ~ 0x60FF | SYS0_MISCL_CTRL_BASE    |
 *  | PADC      	     | 0x6100 ~ 0x61FF | SYS0_PADC_CTRL_BASE     |
 *  | MUX        	     | 0x6200 ~ 0x62FF | SYS0_MUX_CTRL_BASE      |
 *  | ANGC        	     | 0x6300 ~ 0x63FF | SYS0_ANGC_BASE          |
 *  | CLK       	     | 0x6400 ~ 0x64FF | SYS0_CLK_BASE           |
 *  | PMU                | 0x6500 ~ 0x65FF | SYS0_PMU_BASE           |
 *  | RRAM               | 0x6600 ~ 0x66FF | SYS0_RRAM_BASE          |
 *  | RTT                | 0x6700 ~ 0x67FF | SYS0_RTT_CTRL_BASE      |
 *  | CPHY               | 0x6800 ~ 0x68FF | SYS0_CPHY_BASE      	 |
 *  | MISCH              | 0x7000 ~ 0x70FF | SYS0_MISCH_CTRL_BASE    |
 *  +----------------------------------------------------------------+
 */


/*
 *  +-----------------------------------------------------------------------+
 *  |					MISCL      								            |
 *  +-----------------------------------------------------------------------+
 */
#define	SYS0_MISCL_OFFSET							(0x00000000) //0x0000~0x00FF
#define	SYS0_MISCL_CTRL_BASE						(SYSTEM_PD0_REG_ADDRESS + SYS0_MISCL_OFFSET)

#define	R8_SYS0_MISCL								((REG8   *) SYS0_MISCL_CTRL_BASE)
#define	R16_SYS0_MISCL								((REG16  *) SYS0_MISCL_CTRL_BASE)
#define	R32_SYS0_MISCL								((REG32  *) SYS0_MISCL_CTRL_BASE)
#define	R64_SYS0_MISCL								((REG64  *) SYS0_MISCL_CTRL_BASE)

#define	R32_SYS0_SYS_HWCFG							((0x00) >> 2)                                       //Default: 0x00000000
#define		SR_SYS_HWCFG_SHIFT						(0)
#define		SR_SYS_HWCFG_MASK						BIT_MASK(11)//E17, [10]: Disable OTF function on AES DBUF path
#define		SR_SYS_HWCFG							(SR_SYS_HWCFG_MASK << SR_SYS_HWCFG_SHIFT)		//except BIT9, other BIT only can be overwritten to 1
#define		DISABLE_JTAG_FUNCTION_BIT				(BIT0)
#define		DISABLE_UART_FUNCTION_BIT				(BIT1)
#define		DISABLE_I2CS_FUNCTION_BIT				(BIT2)
#define		DISABLE_JTAG_UNLOCK_FUNCTION_BIT		(BIT3)
#define		DISABLE_AES_KEY_READ_FUNCTION_BIT		(BIT4)
#define		DISABLE_AES_OTF_FUNCTION_BIT			(BIT5)
#define		DISABLE_AES_OFL_FUNCTION_BIT			(BIT6)
#define		DISABLE_SHA_FUNCTION_BIT				(BIT7)
#define		DISABLE_PKE_FUNCTION_BIT				(BIT8)
#define		DISABLE_SPI_SCRAMBLE_FUNCTION_BIT		(BIT9)

#define	R32_SYS0_SYS_GPIO_LH						((0x04) >> 2)                                       //Default: 0x00FFFDBF
#define		SYS_SR_GPIO_LH_SHIFT					(0)
#define		SYS_SR_GPIO_LH_MASK						BIT_MASK(13)//E17, E13 should be 13, [12:0]
#define		SYS_SR_GPIO_LH                         	(SYS_SR_GPIO_LH_MASK << SYS_SR_GPIO_LH_SHIFT)

#define	R32_SYS0_SYS_HW_SETTING1					((0x08) >> 2)
#define		CR_SATA_SSC_ON_BIT						(BIT0)
#define		CR_DEC_ODE_BIT							(BIT1)
#define		CR_SATA_XTAL_SEL_BIT					(BIT2)			//E17, equal to CR_CPHY_XTAL_SEL_BIT
//#define		CR_PCIE_LANE_SHIFT					(5)				//E17, [5] cr_fphya_psw_en
//#define		CR_PCIE_LANE_MASK					(BIT_MASK(2))
//#define		CR_PCIE_LANE						(CR_PCIE_LANE_MASK << CR_PCIE_LANE_SHIFT)
//#define 		CR_PCIE_LANE_1						(0)
//#define 		CR_PCIE_LANE_2						(1)
//#define 		CR_PCIE_LANE_4						(2)
//#define 		CR_PCIE_LANE_1						(3)
#define		CR_SATA_OOB_GEN_SHIFT					(8)
#define		CR_SATA_OOB_GEN_MASK					(BIT_MASK(2))
#define		CR_SATA_OOB_GEN 						(CR_SATA_OOB_GEN_MASK << CR_SATA_OOB_GEN_SHIFT)
#define		SATA_LINK_GEN3							(0)
#define		SATA_LINK_GEN2 							(1)
#define		SATA_LINK_GEN1 							(2)
#define		OOB_DEFAULT_OFF							(3)

#define R32_SYS0_SYS_HW_SETTING2					((0x0C) >> 2)
#define 	SYS_SATA_OOB_GEN0_TRIM_SHIFT			(0)
#define		SYS_SATA_OOB_GEN0_TRIM_MASK				(BIT_MASK(4))
#define		SYS_SATA_OOB_GEN0_TRIM   				(SYS_SATA_OOB_GEN0_TRIM_MASK << SYS_SATA_OOB_GEN0_TRIM_SHIFT)
#define 	SYS_SATA_OOB_GEN1_TRIM_SHIFT			(4)
#define		SYS_SATA_OOB_GEN1_TRIM_MASK				(BIT_MASK(4))
#define		SYS_SATA_OOB_GEN1_TRIM   				(SYS_SATA_OOB_GEN1_TRIM_MASK << SYS_SATA_OOB_GEN1_TRIM_SHIFT)

#define	R32_SYS0_SYS_CHIP_ID0						((0x10) >> 2)                                       //Default: 0x00000000

#define	R32_SYS0_SYS_CHIP_ID1						((0x14) >> 2)                                       //Default: 0x00000000

//#define	R32_SYS0_SYS_FPGA_VAR						((0x18) >> 2)//E17, remove
//#define		FPGA_VAR								(BIT0)

#define	R32_SYS0_SYS_PCIE_PMU_STS					((0x20) >> 2)                                       //Default: 0x00000000
#define		SR_PCIE_PMU_PG_ASSERT_BIT				(BIT0)

#define	R8_SYS0_SYS_RRAM_CFG						((0x30) >> 0)
#define	R32_SYS0_SYS_RRAM_CFG						((0x30) >> 2)
#define		SYS_CR_RRAM_IDX_SHIFT					(0)
#define		SYS_CR_RRAM_IDX_MASK 					(BIT_MASK(4))
#define		SYS_CR_RRAM_IDX 						(SYS_CR_RRAM_IDX_MASK << SYS_CR_RRAM_IDX_SHIFT)
#define		CR_RRM_RRC_CFG_SHIFT					(5)
#define		CR_RRM_RRC_CFG_MASK						(BIT_MASK(3))
#define		CR_RRM_RRC_CFG 							(CR_RRM_RRC_CFG_MASK << CR_RRM_RRC_CFG_SHIFT)
#define		CR_RRAM_INIT_BIT						(BIT8)
#define		CR_RRAM_RRC_PASS_INJ_BIT				(BIT9)
#define		CR_RRAM_RRC_LS_EN_BIT 					(BIT10)
#define		CR_RRAM_GAT_EN_BIT						(BIT11)
#define		CR_RRAM_PERR_CLR_BIT					(BIT12)
#define		SR_RRAM_PERR_VLD_BIT					(BIT29)	//E17
#define		SR_RRAM_RRCNT_SHIFT						(30)	//E17
#define		SR_RRAM_RRCNT_MASK						(BIT_MASK(2))
#define		SR_RRAM_RRCNT 							(SR_RRAM_RRCNT_MASK << SR_RRAM_RRCNT_SHIFT)
#define		SR_RRAM_PERR_ADDR_SHIFT					(16)
#define		SR_RRAM_PERR_ADDR_MASK					(BIT_MASK(9))
#define		SR_RRAM_PERR_ADDR 						(SR_RRAM_PERR_ADDR_MASK << SR_RRAM_PERR_ADDR_SHIFT)

#define	R32_SYS0_SYS_SATA_FSIM						((0x38) >> 2)                                       //Default: 0x00000000
#define		CR_F_SIM_EN_BIT							(BIT0)

#define	R32_SYS0_SYS_SIM_CTRL0						((0x40) >> 2)                                       //Default: 0xFFFF0000
#define R8_SYS0_SYS_SIM_CTRL0						(0x40 >> 0)
#define	R8_SYS0_SYS_CR_SIM_CTRL0					(R8_SYS0_SYS_SIM_CTRL0 + 0)
#define	R8_SYS0_SYS_CR_SIM_CTRL1					(R8_SYS0_SYS_SIM_CTRL0 + 1)
#define	R8_SYS0_SYS_CR_SIM_CTRL2					(R8_SYS0_SYS_SIM_CTRL0 + 2)
#define	R8_SYS0_SYS_CR_SIM_CTRL3					(R8_SYS0_SYS_SIM_CTRL0 + 3)

#define	R32_SYS0_SYS_SIM_CTRL1						((0x44) >> 2)                                       //Default: 0xFFFF0000
#define R8_SYS0_SYS_SIM_CTRL1						(0x44 >> 0)
#define	R8_SYS0_SYS_CR_SIM_CTRL4					(R8_SYS0_SYS_SIM_CTRL1 + 0)
#define	R8_SYS0_SYS_CR_SIM_CTRL5					(R8_SYS0_SYS_SIM_CTRL1 + 1)
#define	R8_SYS0_SYS_CR_SIM_CTRL6					(R8_SYS0_SYS_SIM_CTRL1 + 2)
#define	R8_SYS0_SYS_CR_SIM_CTRL7					(R8_SYS0_SYS_SIM_CTRL1 + 3)

#define	R32_SYS0_SYS_SIM_CTRL2						((0x48) >> 2)                                       //Default: 0x000000FF
#define	R8_SYS0_SYS_SIM_CTRL2						((0x48) >> 0)
#define	R8_SYS0_SYS_CR_SIM_CTRL8					(R8_SYS0_SYS_SIM_CTRL2 + 0)

#define R32_SYS0_SYS_MEM_RME0                       ((0x50) >> 2)                                       //Default: 0x00000000
#define     SYS_SR_PD1_ROM_RME_BIT                  (BIT0)
#define		SYS_CR_PD50_RAM_RME_BIT					(BIT16)
#define     SYS_SR_PMU_ROM_RME_BIT                  (BIT24)

#define	R32_SYS0_SYS_MEM_RME1						((0x54) >> 2)
#define		SYS_SR_PD0_RAM_RME_SHIFT				(0)
#define		SYS_SR_PD0_RAM_RME_MASK					(BIT_MASK(8))//E17
//#define		SYS_SR_PD0_RAM_RME 						(SYS_SR_PD0_RAM_RME_MASK << SYS_SR_PD0_RAM_RME_SHIFT)

#define R32_SYS0_SYS_MEM_RM                         ((0x58) >> 2)  //  54->58                           //Default: 0x00000000
#define     SYS_SR_PD1_ROM_RM_SHIFT                 (0)
#define     SYS_SR_PD1_ROM_RM_MASK                  (BIT_MASK(2))
#define     SYS_SR_PD1_ROM_RM                       (SYS_SR_PD1_ROM_RM_MASK << SYS_SR_PD1_ROM_RM_SHIFT)
#define     SYS_SR_PD0_RAM_RM_SHIFT                 (8)
#define     SYS_SR_PD0_RAM_RM_MASK                  (BIT_MASK(2))
#define     SYS_SR_PD0_RAM_RM                       (SYS_SR_PD0_RAM_RM_MASK << SYS_SR_PD0_RAM_RM_SHIFT)
#define     SYS_CR_PD50_RAM_RM_SHIFT                (16)
#define     SYS_CR_PD50_RAM_RM_MASK                 (BIT_MASK(2))
#define     SYS_CR_PD50_RAM_RM                      (SYS_CR_PD50_RAM_RM_MASK << SYS_CR_PD50_RAM_RM_SHIFT)
#define     SYS_SR_PMU_ROM_RM_SHIFT                 (24)
#define     SYS_SR_PMU_ROM_RM_MASK                  (BIT_MASK(2))
#define     SYS_SR_PMU_ROM_RM                       (SYS_SR_PMU_ROM_RM_MASK << SYS_SR_PMU_ROM_RM_SHIFT)

//#define R32_SYS0_SYS_FPGA_UNI_TX_DLY				((0x60) >> 2)//E17, remove
//#define     FPGA_UNI_TX_DLY_SHIFT					(0)
//#define     FPGA_UNI_TX_DLY_MASK					(BIT_MASK(20))
//#define     FPGA_UNI_TX_DLY							(FPGA_UNI_TX_DLY_MASK << FPGA_UNI_TX_DLY_SHIFT)
//#define     FPGA_UFS_DBG_BIT						(BIT31)

/*
 *  +-----------------------------------------------------------------------+
 *  |					PADC      									        |
 *  +-----------------------------------------------------------------------+
 */
#define SYS0_PADC_OFFSET							(0x00000100) //0x0100~0x01FF
#define SYS0_PADC_CTRL_BASE							(SYSTEM_PD0_REG_ADDRESS + SYS0_PADC_OFFSET)

#define R8_SYS0_PADC								((REG8  *) SYS0_PADC_CTRL_BASE)
#define R16_SYS0_PADC								((REG16 *) SYS0_PADC_CTRL_BASE)
#define R32_SYS0_PADC								((REG32 *) SYS0_PADC_CTRL_BASE)
#define R64_SYS0_PADC								((REG64 *) SYS0_PADC_CTRL_BASE)

#define R32_SYS0_PAD_CTRL_REG0						((0x00) >> 2)                                        //Default: 0xD4041001
#define		CR_FLH_HZ_N_BIT							(BIT24)	// E17
/*// different usage in E17
#define		XDEVSLP_I_BIT							(BIT1)
#define		CR_DEVSLP_SO_BIT						(BIT2)
#define		CR_DEVSLP_CO_BIT						(BIT3)
#define		CR_DEVSLP_PD_BIT						(BIT6)
#define		XPERSTN_I_BIT							(BIT9)
#define		CR_PERSTN_SO_BIT						(BIT10)
#define		CR_PERSTN_CO_BIT						(BIT11)
#define		CR_PERSTN_PD_BIT						(BIT14)
#define		CR_CLKREQB_OE_MODE0_BIT					(BIT16)
#define		XCLKREQB_I_BIT							(BIT17)
#define		CR_CLKREQB_OE_BIT						(BIT18)
#define		CR_CLKREQB_OE_MODE1_BIT					(BIT19)
#define		CR_CLKPEQB_PD_BIT						(BIT22)
#define		CR_CLKREQB_SO_BIT						(BIT24)
#define		CR_CLKREQB_CO_BIT						(BIT25)
#define 		CTRSIOB_IN							(SET_BIT30)	// ref. FIP spec 5.35 ZQ calibration boot-up manual flow
#define		CR_FLH_HZ_N_BIT							(BIT30)
#define		CR_FLH_INI_H_EN_BIT						(BIT31)*/

#define R32_SYS0_PAD_CTRL_REG1						((0x04) >> 2)                                        //Default: 0x0000D400
#define		XCLKREQB_I_BIT							(BIT0)
#define 	CR_CLKREQB_EN_BIT						(BIT8)	// E17
#define 	CR_CLKREQB_EN_MODE0_BIT					(BIT9)  // E17
#define		XCLKREQB_I_SHIFT					    (0)

/*// different usage in E17
#define		XTMS_OE_BIT								(BIT0)
#define		XTMS_I_BIT								(BIT1)
#define		XTMS_I_SHIFT							(1)
#define		CR_TMS_U_DB_SHIFT						(2)
#define		CR_TMS_U_DB_MASK						(BIT_MASK(2))
#define		CR_TMS_U_DB								(CR_TMS_U_DB_MASK << CR_TMS_U_DB_SHIFT)
#define		CR_TMS_DV_SHIFT							(4)
#define		CR_TMS_DV_MASK							(BIT_MASK(2))
#define		CR_TMS_DV								(CR_TMS_DV_MASK << CR_TMS_DV_SHIFT)
#define		CR_TMS_SR_BIT							(BIT6)
#define		CR_TMS_S_CB_BIT							(BIT7)
#define		XTCK_OE_BIT								(BIT8)
#define		XTCK_I_BIT								(BIT9)
#define		XTCK_I_SHIFT							(9)
#define		CR_TCK_U_DB_SHIFT						(10)
#define		CR_TCK_U_DB_MASK						BIT_MASK(2)
#define		CR_TCK_U_DB								(CR_TCK_U_DB_MASK << CR_TCK_U_DB_SHIFT)
#define		CR_TCK_DV_SHIFT							(12)
#define		CR_TCK_DV_MASK							BIT_MASK(2)
#define		CR_TCK_DV								(CR_TCK_DV_MASK << CR_TCK_DV_SHIFT)
#define		CR_TCK_SR_BIT							(BIT14)
#define		CR_TCK_S_CB_BIT							(BIT15)
#define		XLED_I_BIT								(BIT17)
#define		CR_LED_OE_BIT							(BIT18)
#define		CR_LED_SO_BIT							(BIT20)
#define		CR_LED_CO_BIT							(BIT21)
#define		CR_LED_PD_BIT							(BIT22)
#define		XEXTRSTB_I_BIT							(BIT25)*/

#define R32_SYS0_PAD_CTRL_REG3						((0x0C) >> 2)										 //Default: 0x80x000xh
#define 	CR_XTMS_I_BIT							(BIT0)
#define 	CR_TMS_CONOF_SONOF_SHIFT				(1)
#define 	CR_TMS_CONOF_SONOF_MASK 				(BIT_MASK(2))
#define 	CR_TMS_CONOF_SONOF						(CR_TMS_CONOF_SONOF_MASK << CR_TMS_CONOF_SONOF_SHIFT)
#define 	CR_TMS_PU5P5_PU75_SHIFT 				(3)
#define 	CR_TMS_PU5P5_PU75_MASK					(BIT_MASK(2))
#define 	CR_TMS_PU5P5_PU75						(CR_TMS_PU5P5_PU75_MASK << CR_TMS_PU5P5_PU75_SHIFT)
#define 	CR_TMS_PU75								(1)//E17, [4:3] 01: 75K ohm
#define 	CR_TMS_PU5P5							(2)//E17, [4:3] 10: 5.5K ohm
#define 	CR_TMS_PD5P5_PD75_SHIFT 				(5)
#define 	CR_TMS_PD5P5_PD75_MASK					(BIT_MASK(2))
#define 	CR_TMS_PD5P5_PD75						(CR_TMS_PD5P5_PD75_MASK << CR_TMS_PD5P5_PD75_SHIFT)
#define 	CR_TMS_SR_BIT							(BIT7)
#define 	CR_TMS_EN_BIT							(BIT8)
#define 	CR_TMS_IODRV1_IODRV0_SHIFT				(11)
#define 	CR_TMS_IODRV1_IODRV0_MASK				(BIT_MASK(2))
#define 	CR_TMS_IODRV1_IODRV0					(CR_TMS_IODRV1_IODRV0_MASK << CR_TMS_IODRV1_IODRV0_SHIFT)
#define 	CR_TMS_CFG_SHIFT						(14)
#define 	CR_TMS_CFG_MASK 						(BIT_MASK(2))
#define 	CR_TMS_CFG								(CR_TMS_CFG_MASK << CR_TMS_CFG_SHIFT)
#define 	CR_XTCK_I_BIT							(BIT16)
#define		CR_XTCK_I_SHIFT							(16)
#define 	CR_TCK_CONOF_SONOF_SHIFT				(17)
#define 	CR_TCK_CONOF_SONOF_MASK 				(BIT_MASK(2))
#define 	CR_TCK_CONOF_SONOF						(CR_TCK_CONOF_SONOF_MASK << CR_TCK_CONOF_SONOF_SHIFT)
#define 	CR_TCK_PU5P5_PU75_SHIFT 				(19)
#define 	CR_TCK_PU5P5_PU75_MASK					(BIT_MASK(2))
#define 	CR_TCK_PU5P5_PU75						(CR_TCK_PU5P5_PU75_MASK << CR_TCK_PU5P5_PU75_SHIFT)
#define 	CR_TCK_PU75								(1)//E17, [20:19] 01: 75K ohm
#define 	CR_TCK_PU5P5							(2)//E17, [20:19] 10: 5.5K ohm
#define 	CR_TCK_PD5P5_PD75_SHIFT 				(21)
#define 	CR_TCK_PD5P5_PD75_MASK					(BIT_MASK(2))
#define 	CR_TCK_PD5P5_PD75						(CR_TCK_PD5P5_PD75_MASK << CR_TCK_PD5P5_PD75_SHIFT)
#define 	CR_TCK_SR_BIT							(BIT23)
#define 	CR_TCK_EN_BIT							(BIT24)
#define 	CR_TCK_IODRV1_IODRV0_SHIFT				(27)
#define 	CR_TCK_IODRV1_IODRV0_MASK				(BIT_MASK(2))
#define 	CR_TCK_IODRV1_IODRV0					(CR_TCK_IODRV1_IODRV0_MASK << CR_TCK_IODRV1_IODRV0_SHIFT)
#define 	CR_TCK_CFG_SHIFT						(30)
#define 	CR_TCK_CFG_MASK 						(BIT_MASK(2))
#define 	CR_TTCK_CFG 							(CR_TCK_CFG_MASK << CR_TCK_CFG_SHIFT)

/*//-------GPIO-------//    // E13
#define R32_SYS0_PAD_GPIO_CTRL0						((0x08) >> 2)                                        //Default: 0x000000D4
#define		CR_GPIO_GB_IO1_BIT						(BIT2)
#define		CR_GPIO_DV_IO1_SHIFT					(4)
#define		CR_GPIO_DV_IO1_MASK						BIT_MASK(2)
#define		CR_GPIO_DV_IO1							(CR_GPIO_DV_IO1_MASK << CR_GPIO_DV_IO1_SHIFT)
#define		CR_GPIO_SR_IO1_BIT						(BIT6)
#define		CR_GPIO_S_CB_IO1_BIT					(BIT7)
#define		CR_GPIO_O_MD_SHIFT						(8)
#define		CR_GPIO_O_MD_MASK						BIT_MASK(3)
#define		CR_GPIO_O_MD							(CR_GPIO_O_MD_MASK << CR_GPIO_O_MD_SHIFT)

#define R32_SYS0_PAD_GPIO_DATA						((0x0C) >> 2)                                        //Default: 0x003FFFFF
#define		XGPIO_I_MASK							(BIT_MASK(12))
#define		XGPIO_I									(XGPIO_I_MASK)

#define R32_SYS0_PAD_GPIO_CTRL1						((0x10) >> 2)                                        //Default: 0x10101010
#define R8_SYS0_PAD_GPIO_CTRL1						((0x10) >> 0)
#define R8_SYS0_PAD_GPIO_0							(R8_SYS0_PAD_GPIO_CTRL1 + 0)
#define R8_SYS0_PAD_GPIO_1							(R8_SYS0_PAD_GPIO_CTRL1 + 1)
#define R8_SYS0_PAD_GPIO_2							(R8_SYS0_PAD_GPIO_CTRL1 + 2)
#define R8_SYS0_PAD_GPIO_3							(R8_SYS0_PAD_GPIO_CTRL1 + 3)

#define R32_SYS0_PAD_GPIO_CTRL2						((0x14) >> 2)                                        //Default: 0x10421010
#define R8_SYS0_PAD_GPIO_4							(R8_SYS0_PAD_GPIO_CTRL1 + 4)
#define R8_SYS0_PAD_GPIO_5							(R8_SYS0_PAD_GPIO_CTRL1 + 5)
#define R8_SYS0_PAD_GPIO_6							(R8_SYS0_PAD_GPIO_CTRL1 + 6)
#define R8_SYS0_PAD_GPIO_7							(R8_SYS0_PAD_GPIO_CTRL1 + 7)

#define R32_SYS0_PAD_GPIO_CTRL3						((0x18) >> 2)                                        //Default: 0x10104010
#define R8_SYS0_PAD_GPIO_8							(R8_SYS0_PAD_GPIO_CTRL1 + 8)
#define R8_SYS0_PAD_GPIO_9							(R8_SYS0_PAD_GPIO_CTRL1 + 9)
#define R8_SYS0_PAD_GPIO_10							(R8_SYS0_PAD_GPIO_CTRL1 + 10)
#define R8_SYS0_PAD_GPIO_11							(R8_SYS0_PAD_GPIO_CTRL1 + 11)

#define R32_SYS0_PAD_GPIO_CTRL4						((0x1C) >> 2)                                        //Default: 0x10101010
#define R8_SYS0_PAD_GPIO_12							(R8_SYS0_PAD_GPIO_CTRL1 + 12)
#define R8_SYS0_PAD_GPIO_13							(R8_SYS0_PAD_GPIO_CTRL1 + 13)
#define R8_SYS0_PAD_GPIO_14							(R8_SYS0_PAD_GPIO_CTRL1 + 14)
#define R8_SYS0_PAD_GPIO_15							(R8_SYS0_PAD_GPIO_CTRL1 + 15)
#define		CR_GPIO_O_BIT							(BIT0)												//used to control GPIO output data
#define		XGPIO_I_BIT								(BIT1)												//used to control GPIO signals
#define		CR_GPIO_OE_BIT							(BIT2)												//used to enable GPIO output.
#define		CR_GPIO_PU_75K_BIT						(BIT4)
#define		CR_GPIO_PU_55K_BIT						(BIT5)
#define		CR_GPIO_PD_75K_BIT						(BIT6)
#define 	XGPIO_I_SHIFT							(1)

enum PAD_SETTING {
	PULL_UP_75K     = CR_GPIO_PU_75K_BIT,
	PULL_UP_55K     = CR_GPIO_PU_55K_BIT,
	PULL_UP_5K		= (CR_GPIO_PU_75K_BIT | CR_GPIO_PU_55K_BIT),
	PULL_DOWN_75K   = CR_GPIO_PD_75K_BIT,
	PAD_SETTING_MSK = (CR_GPIO_PU_75K_BIT | CR_GPIO_PU_55K_BIT | CR_GPIO_PD_75K_BIT)
};
*/

// GPIO - E17 different offset
#define R32_SYS0_PAD_GPIO_DAT						((0x14) >> 2)										 //Default: 0x003FFFFF
#define 	XGPIO_I_SHIFT							(0)
#define 	XGPIO_I_MASK							(BIT_MASK(13))
#define 	XGPIO_I 								(XGPIO_I_MASK << XGPIO_I_SHIFT)

#define R32_SYS0_PAD_GPIO_CTRL0 					((0x18) >> 2)										 //Default: 0x10101010
#define R8_SYS0_PAD_GPIO_CTRL0						((0x18) >> 0)
#define R8_SYS0_PAD_GPIO_0							(R8_SYS0_PAD_GPIO_CTRL0 + 0)
#define R8_SYS0_PAD_GPIO_1							(R8_SYS0_PAD_GPIO_CTRL0 + 1)
#define R8_SYS0_PAD_GPIO_2							(R8_SYS0_PAD_GPIO_CTRL0 + 2)
#define R8_SYS0_PAD_GPIO_3							(R8_SYS0_PAD_GPIO_CTRL0 + 3)

#define R32_SYS0_PAD_GPIO_CTRL1 					((0x1C) >> 2)										 //Default: 0x10421010
#define R8_SYS0_PAD_GPIO_4							(R8_SYS0_PAD_GPIO_CTRL0 + 4)
#define R8_SYS0_PAD_GPIO_5							(R8_SYS0_PAD_GPIO_CTRL0 + 5)
#define R8_SYS0_PAD_GPIO_6							(R8_SYS0_PAD_GPIO_CTRL0 + 6)
#define R8_SYS0_PAD_GPIO_7							(R8_SYS0_PAD_GPIO_CTRL0 + 7)

#define R32_SYS0_PAD_GPIO_CTRL2 					((0x20) >> 2)										 //Default: 0x10104010
#define R8_SYS0_PAD_GPIO_8							(R8_SYS0_PAD_GPIO_CTRL0 + 8)
#define R8_SYS0_PAD_GPIO_9							(R8_SYS0_PAD_GPIO_CTRL0 + 9)
#define R8_SYS0_PAD_GPIO_10 						(R8_SYS0_PAD_GPIO_CTRL0 + 10)
#define R8_SYS0_PAD_GPIO_11 						(R8_SYS0_PAD_GPIO_CTRL0 + 11)

#define R32_SYS0_PAD_GPIO_CTRL3 					((0x24) >> 2)										 //Default: 0x10101010
#define R8_SYS0_PAD_GPIO_12 						(R8_SYS0_PAD_GPIO_CTRL0 + 12)
#define R8_SYS0_PAD_GPIO_13 						(R8_SYS0_PAD_GPIO_CTRL0 + 13)
#define R8_SYS0_PAD_GPIO_14 						(R8_SYS0_PAD_GPIO_CTRL0 + 14)
#define R8_SYS0_PAD_GPIO_15 						(R8_SYS0_PAD_GPIO_CTRL0 + 15)
#define 	CR_GPIO_EN_BIT							(BIT0)
#define 	XGPIO_I_BIT 							(BIT1)												//used to control GPIO signals
#define 	CR_GPIO_PU_75K_BIT						(BIT3)
#define 	CR_GPIO_PU_5P5K_BIT						(BIT4)
#define 	CR_GPIO_PD_75K_BIT						(BIT5)
#define 	CR_GPIO_PD_5P5K_BIT						(BIT6)


#define R32_SYS0_PAD_SDIO_CTRL                      ((0x28) >> 2)                                        //Default: 0x3F000000
#define     CR_SDIO_PMODE                           (BIT0)
#define     CR_SDIO_1V8_PMODE                       (SET_BIT0)
#define     CR_EN_SDCLK_GB                          (CLR_BIT24)
#define     CR_EN_SDCMD_GB                          (CLR_BIT25)
#define     CR_EN_SDIO_GB                           (CLR_BIT26 | CLR_BIT27 | CLR_BIT28 | CLR_BIT29)
#define     CR_DIS_SDCLK_GB                         (BIT24)
#define     CR_DIS_SDCMD_GB                         (BIT25)
#define     CR_DIS_SDIO_GB                          (BIT26 | BIT27 | BIT28 | BIT29)
#define	  CR_DIS_GATING_ALL_SDIO			   		((CR_DIS_SDCLK_GB | CR_DIS_SDCMD_GB | CR_DIS_SDIO_GB))
#define	  CR_EN_GATING_ALL_SDIO			   			(~(CR_DIS_GATING_ALL_SDIO))

#define R32_SYS0_PAD_SDCLK_CTRL                     ((0x2C) >> 2)                                        //Default: 0x0000000x
#define     SET_SDCLK_VCM                           (BIT25)
#define     CLR_SDCLK_VCM                           (CLR_BIT25)
#define     CR_SDIO_CFGCAP_SHIFT                    (13)
#define		CR_SDIO_CFGCAP_MASK 					(BIT_MASK(1))

#define R32_SYS0_PAD_SDCMD_CTRL                     ((0x30) >> 2)
#define R8_SYS0_PAD_SDCMD_CTRL                      ((0x30) >> 0)
#define R8_SYS0_PAD_SDCMD_0							(R8_SYS0_PAD_SDCMD_CTRL + 0)                        // 0xF800_6130
#define R8_SYS0_PAD_SDCMD_1							(R8_SYS0_PAD_SDCMD_CTRL + 1)
#define R8_SYS0_PAD_SDCMD_2							(R8_SYS0_PAD_SDCMD_CTRL + 2)                        // 0xF800_6132
#define R8_SYS0_PAD_SDCMD_3							(R8_SYS0_PAD_SDCMD_CTRL + 3)
#define CLR_SD_PAD_CTRL                             (0xFFF0FF1F)
#define SYS_PAD_SRP_SHIFT							(16)
#define SYS_PAD_SRN_SHIFT							(18)
#define SYS_PAD_IODRV_SHIFT							(5)

#define R32_SYS0_PAD_SDDAT0_CTRL                    ((0x34) >> 2)
#define R8_SYS0_PAD_SDDAT0_CTRL                     ((0x34) >> 0)
#define R8_SYS0_PAD_SDDAT0_0						(R8_SYS0_PAD_SDDAT0_CTRL + 0)                        // 0xF800_6134
#define R8_SYS0_PAD_SDDAT0_1						(R8_SYS0_PAD_SDDAT0_CTRL + 1)
#define R8_SYS0_PAD_SDDAT0_2						(R8_SYS0_PAD_SDDAT0_CTRL + 2)                        // 0xF800_6136
#define R8_SYS0_PAD_SDDAT0_3						(R8_SYS0_PAD_SDDAT0_CTRL + 3)

#define R32_SYS0_PAD_SDDAT1_CTRL                    ((0x38) >> 2)
#define R8_SYS0_PAD_SDDAT1_CTRL                     ((0x38) >> 0)
#define R8_SYS0_PAD_SDDAT1_0						(R8_SYS0_PAD_SDDAT1_CTRL + 0)                        // 0xF800_6138
#define R8_SYS0_PAD_SDDAT1_1						(R8_SYS0_PAD_SDDAT1_CTRL + 1)
#define R8_SYS0_PAD_SDDAT1_2						(R8_SYS0_PAD_SDDAT1_CTRL + 2)                        // 0xF800_613A
#define R8_SYS0_PAD_SDDAT1_3						(R8_SYS0_PAD_SDDAT1_CTRL + 3)

#define R32_SYS0_PAD_SDDAT2_CTRL                    ((0x3C) >> 2)
#define R8_SYS0_PAD_SDDAT2_CTRL                     ((0x3C) >> 0)
#define R8_SYS0_PAD_SDDAT2_0						(R8_SYS0_PAD_SDDAT2_CTRL + 0)                        // 0xF800_613C
#define R8_SYS0_PAD_SDDAT2_1						(R8_SYS0_PAD_SDDAT2_CTRL + 1)
#define R8_SYS0_PAD_SDDAT2_2						(R8_SYS0_PAD_SDDAT2_CTRL + 2)                        // 0xF800_613E
#define R8_SYS0_PAD_SDDAT2_3						(R8_SYS0_PAD_SDDAT2_CTRL + 3)

#define R32_SYS0_PAD_SDDAT3_CTRL                    ((0x40) >> 2)
#define R8_SYS0_PAD_SDDAT3_CTRL                     ((0x40) >> 0)
#define R8_SYS0_PAD_SDDAT3_0						(R8_SYS0_PAD_SDDAT3_CTRL + 0)                        // 0xF800_6140
#define R8_SYS0_PAD_SDDAT3_1						(R8_SYS0_PAD_SDDAT3_CTRL + 1)
#define R8_SYS0_PAD_SDDAT3_2						(R8_SYS0_PAD_SDDAT3_CTRL + 2)                        // 0xF800_6142
#define R8_SYS0_PAD_SDDAT3_3						(R8_SYS0_PAD_SDDAT3_CTRL + 3)

#define R32_SYS0_PAD_USBCC_CTRL 					((0x44) >> 2)										 //Default: 0x10101010
#define 	CR_USBCC_PD								(BIT0)
#define 	CR_USBCC_CONOF_SONOF_SHIFT				(1)
#define 	CR_USBCC_CONOF_SONOF_MASK				(BIT_MASK(2))
#define 	CR_USBCC_GB								(BIT3)
#define 	CR_USBCC_EV12							(BIT4)
#define 	CR_USBCC_BYPASS							(BIT5)
#define 	CR_USBCC_DESEL_SHIFT					(6)
#define 	CR_USBCC_DESEL_MASK						(BIT_MASK(2))
#define 	CR_USBCC_CFG_SHIFT						(8)
#define 	CR_USBCC_CFG_MASK						(BIT_MASK(11))
#define 	CR_USBCC_CFG_VBG						(BIT23)
#define 	CR_USBCC_PIN_VOUT_CC1					(BIT24)
#define 	CR_USBCC_PIN_VOUT_CC2					(BIT25)

#define 	M_USBCC_PD_ON()							(R32_SYS0_PADC[R32_SYS0_PAD_USBCC_CTRL] &= (~CR_USBCC_PD))
#define 	M_USBCC_PD_OFF()						(R32_SYS0_PADC[R32_SYS0_PAD_USBCC_CTRL] |= CR_USBCC_PD)
#define 	M_USBCC_VOUT_CC1()						((R32_SYS0_PADC[R32_SYS0_PAD_USBCC_CTRL] & CR_USBCC_PIN_VOUT_CC1) ? 1 : 0)
#define 	M_USBCC_VOUT_CC2()						((R32_SYS0_PADC[R32_SYS0_PAD_USBCC_CTRL] & CR_USBCC_PIN_VOUT_CC2) ? 1 : 0)

enum PAD_SETTING {
	PULL_UP_75K 	= CR_GPIO_PU_75K_BIT,
	PULL_UP_5P5K 	= CR_GPIO_PU_5P5K_BIT,
	PULL_UP_5K		= (CR_GPIO_PU_75K_BIT | CR_GPIO_PU_5P5K_BIT),
	PULL_DOWN_75K	= CR_GPIO_PD_75K_BIT,
	PULL_DOWN_5P5K	= CR_GPIO_PD_5P5K_BIT,
	PULL_DOWN_5K	= (CR_GPIO_PD_75K_BIT | CR_GPIO_PD_5P5K_BIT),
	PAD_SETTING_MSK = (CR_GPIO_PU_75K_BIT | CR_GPIO_PU_5P5K_BIT | CR_GPIO_PD_75K_BIT | CR_GPIO_PD_5P5K_BIT)
};

/*
 *  +-----------------------------------------------------------------------+
 *  |					MUX      									        |
 *  +-----------------------------------------------------------------------+
 */
#define	SYS0_MUX_OFFSET								(0x00000200) //0x0200~0x02FF
#define	SYS0_MUX_CTRL_BASE							(SYSTEM_PD0_REG_ADDRESS + SYS0_MUX_OFFSET)

#define	R8_SYS_MUX									((REG8  *) SYS0_MUX_CTRL_BASE)
#define	R16_SYS_MUX									((REG16 *) SYS0_MUX_CTRL_BASE)
#define	R32_SYS_MUX									((REG32 *) SYS0_MUX_CTRL_BASE)
#define	R64_SYS_MUX									((REG64 *) SYS0_MUX_CTRL_BASE)

#define	R32_SYS0_SYS_MUX_CTRL0						((0x00) >> 2)                                         //Default: 0x1001A100    Initialized: 0xFFFFFFFF
#define		CR_MUX_GPIO_0_BIT						(BIT0)
#define		CR_MUX_GPIO_1_BIT						(BIT1)
#define		CR_MUX_GPIO_2_BIT						(BIT2)
#define		CR_MUX_GPIO_3_BIT						(BIT3)
#define		CR_MUX_GPIO_4_BIT						(BIT4)
#define		CR_MUX_GPIO_5_BIT						(BIT5)
#define		CR_MUX_GPIO_6_BIT						(BIT6)
#define		CR_MUX_GPIO_7_BIT						(BIT7)
#define		CR_MUX_GPIO_8_BIT						(BIT8)
#define		CR_MUX_GPIO_9_BIT						(BIT9)
#define		CR_MUX_GPIO_10_BIT						(BIT10)
#define		CR_MUX_GPIO_11_BIT						(BIT11)
#define		CR_MUX_GPIO_12_BIT						(BIT12)
#define		CR_MUX_GPIO_13_BIT						(BIT13)
#define		CR_MUX_GPIO_14_BIT						(BIT14)
#define		CR_MUX_GPIO_15_BIT						(BIT15)
#define		CR_MUX_GPIO_16_BIT						(BIT16)
#define		CR_MUX_GPIO_17_BIT						(BIT17)
#define		CR_MUX_GPIO_18_BIT						(BIT18)
#define		CR_MUX_GPIO_19_BIT						(BIT19)
#define		CR_MUX_GPIO_20_BIT						(BIT20)
#define		CR_MUX_GPIO_21_BIT						(BIT21)
#define		CR_MUX_GPIO_22_BIT						(BIT22)
#define		CR_MUX_GPIO_23_BIT						(BIT23)
#define		CR_MUX_GPIO_24_BIT						(BIT24)
#define		CR_MUX_GPIO_ALL_SHIFT					(0)
#define		CR_MUX_GPIO_ALL_MASK					(BIT_MASK(25))
#define		CR_MUX_PHY_SPDSEL_BIT					(BIT26)
#define		CR_MUX_GPIO_OE_BIT						(BIT29)
#define		CR_MUX_PD_OE_BIT						(BIT30)
#define		CR_MUX_SPI_CS_OE_BIT					(BIT31)

#define	R32_SYS0_SYS_MUX_CTRL1						((0x04) >> 2)                                         //Default: 0x00000020    Initialized: 0xFFFFFFFF
#define		CR_MUX_FLH_WPB_BIT						(BIT5)
#define		CR_MUX_FLH_COSO_BIT						(BIT6)
#define		CR_MUX_FLH_ODT_BIT						(BIT7)
#define		CR_MUX_LED_OE_SHIFT						(8)
#define		CR_MUX_LED_OE_MASK						(BIT_MASK(2))
#define		CR_MUX_LED_OE							(CR_MUX_LED_OE_MASK << CR_MUX_LED_OE_SHIFT)
#define			LED_OE_CTRL_BY_PADC					(0)
#define			LED_OE_CTRL_BY_APU					(1)
#define			LED_OE_CTRL_BY_LED					(2)
#define		CR_MUX_JTAG_SHIFT						(14)
#define		CR_MUX_JTAG_MASK						(BIT_MASK(2))
#define		CR_MUX_JTAG								(CR_MUX_JTAG_MASK << CR_MUX_JTAG_SHIFT)
#define		CR_MUX_DB_SHIFT							(16)
#define		CR_MUX_DB_MASK							(BIT_MASK(13))
#define		CR_MUX_DB								(CR_MUX_DB_MASK << CR_MUX_DB_SHIFT)

#define	R32_SYS0_SYS_MUX_CTRL2						((0x08) >> 2)                                         //Default: 0x00000000    Initialized: 0xFFFFFFFF
#define		CR_TESTMODE_DEBUG_BIT					(BIT0)

#define	R32_SYS0_SYS_MUX_CTRL3						((0x10) >> 2)                                         //Default: 0x00000000    Initialized: 0xFFFFFFFF
#define		XDB_PSEL_SHIFT							(0)
#define		XDB_PSEL_MASK							BIT_MASK(8)
#define		XDB_PSEL								(XDB_PSEL_MASK << XDB_PSEL_SHIFT)
#define		XDB_TSEL_SHIFT							(8)
#define		XDB_TSEL_MASK							BIT_MASK(6)
#define		XDB_TSEL								(XDB_TSEL_MASK << XDB_TSEL_SHIFT)
#define		XDB_FPGA_SEL_EN_BIT						(BIT16)                                        //Default: 0x00000000    Initialized: 0xFFFFFFFF
#define		XDB_ROTATE_SHIFT						(24)
#define		XDB_ROTATE_MASK							BIT_MASK(2)

/*
 *  +-----------------------------------------------------------------------+
 *  |					ANGC      									        |
 *  +-----------------------------------------------------------------------+
 */
#define	SYS0_ANGC_OFFSET							(0x00000300) //0x0300~0x03FF
#define	SYS0_ANGC_BASE								(SYSTEM_PD0_REG_ADDRESS + SYS0_ANGC_OFFSET)

#define	R8_SYS0_ANGC								((REG8  *) SYS0_ANGC_BASE)
#define	R16_SYS0_ANGC								((REG16 *) SYS0_ANGC_BASE)
#define	R32_SYS0_ANGC								((REG32 *) SYS0_ANGC_BASE)
#define	R64_SYS0_ANGC								((REG64 *) SYS0_ANGC_BASE)

#define	R32_SYS0_VDT_CTRL0							(0x00 >> 2)
#define		CR_VDT_PD0_CFG_BIT						(BIT0)
#define		CR_VDT_PD1_CFG_BIT						(BIT1)
#define		CR_VDT_PD20_CFG_BIT						(BIT2)
#define		CR_VDT_PD31_CFG_BIT						(BIT3)
#define		CR_VDT_PD51_CFG_BIT						(BIT4)
#define		CR_VDT_VPHY18_CFG_BIT					(BIT5)
#define		CR_VDT_AVDDT_CFG_BIT					(BIT6)
#define		CR_VDT_FLH_CFG_SHIFT					(8)				// CR_VDT_FC_CFG_SHIFT
#define		CR_VDT_FLH_CFG_MASK						(BIT_MASK(2))
#define		CR_VDT_FIO12_CFG_SHIFT					(10)
#define		CR_VDT_FIO12_CFG_MASK					(BIT_MASK(2))
#define		CR_VDT_FIO18_CFG_SHIFT					(12)
#define		CR_VDT_FIO18_CFG_MASK					(BIT_MASK(2))
#define		CR_VDT_VCCQP12_CFG_SHIFT				(14)
#define		CR_VDT_VCCQP12_CFG_MASK					(BIT_MASK(2))
#define		CR_VDT_GPIO18_CFG_SHIFT					(16)
#define		CR_VDT_GPIO18_CFG_MASK					(BIT_MASK(2))
#define		CR_VDT_CORE_CFG_SHIFT					(18)           //KC: CR_VDT_VCCAH_CFG_SHIFT-->CR_VDT_CORE_CFG_SHIFT
#define		CR_VDT_CORE_CFG_MASK					(BIT_MASK(2))  //KC: CR_VDT_VCCAH_CFG_MASK-->CR_VDT_CORE_CFG_MASK
#define		CR_VDT_VCCQP18_CFG_SHIFT				(22)
#define		CR_VDT_VCCQP18_CFG_MASK					(BIT_MASK(2))
#define		CR_VDT_VDDI33A_CFG_SHIFT				(24)
#define		CR_VDT_VDDI33A_CFG_MASK					(BIT_MASK(2))
#define		CR_VDT_TRIM_MODE_SHIFT					(26)
#define		CR_VDT_TRIM_MODE_MASK					(BIT_MASK(3))
#define		CR_VDT_SD_STATE_SEL_SHIFT				(30)
#define		CR_VDT_SD_STATE_SEL_MASK				(BIT_MASK(2))

#define	R32_SYS0_VDT_CTRL1							(0x04 >> 2)
#define		CR_VDT_PD0_PD_BIT						(BIT0)
#define		CR_VDT_PD1_PD_BIT						(BIT1)
#define		CR_VDT_PD20_PD_BIT						(BIT2)
#define		CR_VDT_PD31_PD_BIT						(BIT3)
#define		CR_VDT_PD51_PD_BIT						(BIT4)
#define		CR_VDT_VPHY18_PD_BIT					(BIT5)
#define		CR_VDT_AVDDT_PD_BIT						(BIT6)
#define		CR_VDT_EFU_PS18_PD_BIT					(BIT7)  //KC:CR_VDT_EFU_PRDY_PD_BIT-->CR_VDT_EFU_PS18_PD_BIT
#define		CR_VDT_FLH_PD_BIT						(BIT8)  //KC: CR_VDT_FC_PD_BIT-->CR_VDT_FLH_PD_BIT
#define		CR_VDT_FIO12_PD_BIT						(BIT9)
#define		CR_VDT_FIO_IO0_PD_BIT					(BIT10) //KC: CR_VDT_FIO_PRDY_PD_BIT-->CR_VDT_FIO_IO0_PD_BIT
#define		CR_VDT_FIO18_PD_BIT						(BIT11)
#define		CR_VDT_VCCQP12_PD_BIT					(BIT12)
#define		CR_VDT_GPIO_IO0_PD_BIT   				(BIT13) //KC:CR_VDT_GPIO_PRDY_PD_BIT-->CR_VDT_GPIO_IO0_PD_BIT
#define		CR_VDT_GPIO18_PD_BIT					(BIT14)
#define		CR_VDT_CORE_PD_BIT						(BIT15) //KC:CR_VDT_VCCAH_PD_BIT-->CR_VDT_CORE_PD_BIT
#define		CR_VDT_VCCQP18_PD_BIT					(BIT17)
#define		CR_VDT_VDDI33A_PD_BIT					(BIT18)
#define		CR_VDT_SD_STATE_PD_BIT					(BIT19)
#define		CR_VDT_HIO_PRDY_PD_BIT					(BIT20)
#define		CR_VDT_SEL_VDT_HIO_SHIFT				(30)
#define		CR_VDT_SEL_VDT_HIO_MASK					(BIT_MASK(2))
#define R8_SYS0_ANGC_VDT_CTRL                       (0x06)
#define     CR_SET_VDT_DISABLE                      (SET_BIT3)
#define     CR_SET_VDT_ENABLE                       (CLR_BIT3)

#define	R32_SYS0_VDT_STS							(0x08 >> 2)
#define		XVDT_PD0_PG_BIT							(BIT0)
#define		XVDT_PD1_PG_BIT							(BIT1)
#define		XVDT_PD20_PG_BIT						(BIT2)
#define		XVDT_PD31_PG_BIT						(BIT3)
#define		XVDT_PD51_PG_BIT						(BIT4)
#define		XVDT_VPHY18_PG_BIT						(BIT5)
#define		XVDT_AVDDT_PG_BIT						(BIT6)
#define		XVDT_EFU_CORE_PG_BIT					(BIT7) //KC:XVDT_EFU_PRDY_BIT-->XVDT_EFU_CORE_PG_BIT
#define		XVDT_FIO12_PG_BIT						(BIT8)
#define		XVDT_FIO_IO0_PG_BIT						(BIT9) //KC:XVDT_FIO_PRDY_BIT-->XVDT_FIO_IO0_PG_BIT
#define		XVDT_FIO18_PG_BIT						(BIT10)
#define		XVDT_FLH_PG_BIT  						(BIT11)//KC:XVDT_FC_PG_BIT-->XVDT_FLH_PG_BIT
#define		XVDT_VCCQP12_PG_BIT						(BIT12)
#define		XVDT_GPIO_PRDY_BIT						(BIT13)
#define		XVDT_GPIO18_PG_BIT						(BIT14)
#define		XVDT_CORE_PG_BIT						(BIT15)//XVDT_VCCAH_PG_BIT-->XVDT_CORE_PG_BIT
#define		XVDT_VCCQP18_PG_BIT						(BIT17)
#define		XVDT_VDDI33A_PG_BIT						(BIT18)
#define		XVDT_SD_STATE_PG_BIT					(BIT19)
#define		XVDT_HIO_PRDY_BIT						(BIT20)
#define		XVDT_PG_BIT								(BIT21)
#define		XVDT_VCCQL_PG_BIT						(BIT22)
#define		XVDT_TEST_OUT_SHIFT						(26)  //KC: (BIT26)--> (26)
#define		XVDT_TEST_OUT_MASK						(BIT_MASK(6))

#define	R32_SYS0_VDT_TRIM							(0x0C >> 2)
#define		CR_VDT_FIO_TRIM_SHIFT					(0)
#define		CR_VDT_FIO_TRIM_MASK					(BIT_MASK(6))
#define		CR_VDT_FIO18_TRIM_SHIFT					(6)  //KC: (BIT6)  --> (6)
#define		CR_VDT_FIO18_TRIM_MASK					(BIT_MASK(6))
#define		CR_VDT_FC_TRIM_SHIFT					(12) //KC: (BIT12) --> (12)
#define		CR_VDT_FC_TRIM_MASK						(BIT_MASK(6))
#define		CR_VDT_TRIM_SHIFT						(18) //KC: (BIT18) --> (18) //KC: CR_VDT_VDT_TRIM_SHIFT-->CR_VDT_TRIM_SHIFT.
#define		CR_VDT_TRIM_MASK						(BIT_MASK(6)) //CR_VDT_VDT_TRIM_MASK-->CR_VDT_TRIM_MASK

//KC: add these bit offsets, found in 0x38 in E13
#define	R32_SYS0_VDT_REG_CTRL0  					(0x10 >> 2)  //KC: R32_SYS0_REG_CTRL0-->R32_SYS0_VDT_REG_CTRL0	
#define 	CR_REG_SEL_POWER_BIT                    (BIT0)
#define 	CR_REG_ILIMIT_EN_BIT                    (BIT2)
#define 	CR_REG_ILIMIT_LVL_BIT                   (BIT3)
#define 	CR_REG_EN_OCP_BIT                       (BIT7)
#define 	CR_REG_CFG_SHIFT                        (8)
#define 	CR_REG_CFG_MASK                         (BIT_MASK(24))

//KC: add these bits offsets. Similar reg. is found in 0x30 in E13
#define	R32_SYS0_VDT_REG_CTRL1						(0x14 >> 2)  //KC:R32_SYS0_REG_CTRL1-->R32_SYS0_VDT_REG_CTRL1
#define 	CR_REG_FIO_EN_BIT                       (BIT0)
#define 	CR_REG_STB_FIO_BIT                      (BIT1)  //KC: bit 5 in 0x38 in E13
#define 	CR_REG_FIO_WARMUP_DIS_BIT               (BIT2)
#define 	CR_REG_FIO_STRT_DIS_BIT                 (BIT3)  //KC: Not sure that it is the same with BIT17 in 0x30 in E13
#define 	CR_REG_FIO_LVL_BIT                      (BIT4)
#define 	CR_REG_FIO_SEL_MODE_BIT                 (BIT6)
#define 	CR_REG_FIO_DISCHARGE_DIS_BIT            (BIT7)
#define		CR_REG_PHY_EN_BIT				        (BIT16)
#define 	CR_REG_PHY_STB_BIT                      (BIT17) //KC: bit 4 in 0x38 in E13
#define		CR_REG_PHY_WARMUP_DIS_BIT               (BIT18)
#define		CR_REG_PHY_STRT_DIS_BIT				    (BIT19)
#define 	CR_REG_PHY_LVL_BIT                      (BIT20)
#define 	CR_REG_HIO_SEL_MODE_BIT                 (BIT22)
#define 	CR_REG_HIO_DISCHARGE_DIS_BIT            (BIT23)
#define 	CR_REG_HIO18_DISCHARGE_DIS_BIT          (BIT31)

#define R8_SYS0_ANGC_REG_CTRL                       (0x16)
#define     CR_SET_DISCHARGE_ENABLE                 (CLR_BIT7)
#define     CR_SET_DISCHARGE_DISABLE                (SET_BIT7)
#define     CR_SEL_1V8_MODE                         (CLR_BIT6)
#define     CR_SEL_3V3_MODE                         (SET_BIT6)
#define     CHK_VDD3V3                              (CHK_BIT6)

//KC: add these bits offsets.
#define	R32_SYS0_VDT_REG_CTRL2						(0x18 >> 2)   //KC: R32_SYS0_REG_CTRL2-->R32_SYS0_VDT_REG_CTRL2
#define 	CR_REG_CORE_STB_BIT                     (BIT1)
#define 	CR_REG_CORE_WARMUP_DIS_BIT              (BIT2)
#define 	CR_REG_CORE_LVL_SHIFT                   (13)
#define 	CR_REG_CORE_LVL_MASK                    (BIT_MASK(2))
#define 	CR_REG_V18_WARMUP_DIS_BIT               (BIT16)
#define 	CR_REG_HIO_WARMUP_DIS_BIT               (BIT17)
#define 	CR_REG_HIO18_WARMUP_DIS_BIT             (BIT18)
#define 	CR_REG_V18_DIS_SOFT_BIT                 (BIT19)
#define 	CR_REG_HIO_DIS_SOFT_BIT                 (BIT20)
#define 	CR_REG_HIO18_DIS_SOFT_BIT               (BIT21)
#define 	CR_REG_CFG_OCP_SHIFT                    (24)
#define 	CR_REG_CFG_OCP_MASK                     (BIT_MASK(8))

//KC: add these bits offsets. Similar reg. is found in 0x10 in E13
#define	R32_SYS0_VDT_REG_TRIM						(0x1C >> 2)  //KC: R32_SYS0_REG_TRIM-->R32_SYS0_VDT_REG_TRIM
#define 	CR_REG_CORE_TRIM_SHIFT                  (0)
#define		CR_REG_CORE_TRIM_MASK					(BIT_MASK(5))
#define 	CR_REG_FIO_TRIM_SHIFT                   (5)
#define		CR_REG_FIO_TRIM_MASK					(BIT_MASK(5))
#define 	CR_REG_PHY_TRIM_SHIFT                   (10)
#define		CR_REG_PHY_TRIM_MASK					(BIT_MASK(5))
#define 	CR_REG_V18_TRIM_SHIFT                   (15)
#define		CR_REG_V18_TRIM_MASK					(BIT_MASK(5))
#define 	CR_REG_VHIO_TRIM_SHIFT                  (20)
#define		CR_REG_VHIO_TRIM_MASK					(BIT_MASK(5))
#define  	CR_REG_VHIO18_TRIM_SHIFT                (25)
#define		CR_REG_VHIO18_TRIM_MASK					(BIT_MASK(5))

#define	R32_SYS0_VDT_TS_CTRL0						(0x20 >> 2)	// R32_SYS0_TS_CTRL0
#define		CR_TS_ENABLE_BIT						(BIT0)
#define		CR_TS_RESET_BIT							(BIT1)
#define		CR_TS_INTCLK_SEL_BIT					(BIT3)
#define		CR_TS_CHOP_EN_BIT						(BIT4)
#define		CR_TS_CLK_SEL_SHIFT						(5)
#define		CR_TS_CLK_SEL_MASK						(BIT_MASK(2))
#define		CR_TS_CLK_CFG_BIT						(BIT7)
#define		CR_TS_CURVE_TRIM_SHIFT					(8)
#define		CR_TS_CURVE_TRIM_MASK					(BIT_MASK(7))
#define		SR_TS_TCODE_SHIFT						(16)
#define		SR_TS_TCODE_MASK						(BIT_MASK(10))
#define		SR_TS_TCODE_SIGN_BIT					(BIT26)
#define		SR_TS_TCODE_RDY_BIT						(BIT27)
#define		CR_TS_BYPASS_MODE_BIT					(BIT30)
#define		CR_TS_DEC_SEL_BIT						(BIT31)

#define	R32_SYS0_VDT_TS_CTRL1						(0x24 >> 2)  // KC:R32_SYS0_TS_CTRL1-->R32_SYS0_VDT_TS_CTRL1 & add the following offsets
#define		CR_TS_TCODE_TARGET1_SHIFT				(0)
#define		CR_TS_TCODE_TARGET1_MASK				(BIT_MASK(10))
#define		CR_TS_TCODE_SIGN_TARGET1_BIT			(BIT10)
#define		SR_TS_TEMP_HIGH1_BIT					(BIT15)
#define		CR_TS_TCODE_TARGET2_SHIFT				(16)
#define		CR_TS_TCODE_TARGET2_MASK				(BIT_MASK(10))
#define		CR_TS_TCODE_SIGN_TARGET2_BIT			(BIT26)
#define		SR_TS_TEMP_HIGH2_BIT					(BIT31)

//KC: add these bits offsets. Similar reg. is found in 0x0C in E13
#define	R32_SYS0_TS_TRIM							(0x28 >> 2)  //KC: R32_SYS0_TS_TRIM-->XXR32_SYS0_VDT_TRIMXX-->R32_SYS0_TS_TRIM
#define		CR_TS_DEC_TRIM_SHIFT					(0)
#define		CR_TS_DEC_TRIM_MASK						(BIT_MASK(10))
#define		CR_TS_DEC_TRIM_EN_BIT					(BIT10)
#define		CR_TS_DEC_DENOM_SHIFT					(11)
#define		CR_TS_DEC_DENOM_MASK					(BIT_MASK(12))
#define		CR_TS_DEC_DENOM_TRIM_EN_BIT				(BIT23)

////KC: add these bits offsets. Similar reg. is found in 0x28 in E13
#define	R32_SYS0_OSC_SELFT_CODE						(0x2C >> 2)
#define     SR_LFOSC_SELFT_CODE_SHIFT               (0)            //KC: not in 13
#define     SR_LFOSC_SELFT_CODE_MASK                (BIT_MASK(7))  //KC: not in 13
#define		SR_LFOSC_SELFT_RDY_BIT					(BIT8)         //KC: not in 13
#define		SR_OSC_SELFT_CODE_SHIFT					(16)
#define		SR_OSC_SELFT_CODE_MASK					(BIT_MASK(8))
#define		SR_OSC_SELFT_RDY_BIT					(BIT24)

#define	R32_SYS0_LFOSC_CTRL							(0x30 >> 2)   //KC: NOT in spec&code of E13, skip these offsets

////KC: add these bits offsets. Similar reg. is found in 0x20 in E13
#define	R32_SYS0_OSC_CTRL0							(0x34 >> 2)
#define     CR_OSC_TRIM_MODE_BIT					(BIT1)
#define     CR_OSC_BYPASS_MODE_BIT					(BIT2)
#define		CR_OSC_CFG_BASE_SHIFT					(3)
#define		CR_OSC_CFG_BASE_MASK					(BIT_MASK(4))
#define     CR_OSC_SELFT_RESET_BIT                  (BIT8)        //KC: E13 found in 0x28 bit offset 15
#define		CR_OSC_SELFT_DEC_SEL_BIT				(BIT9)        //KC: E13 found in 0x28 bit offset 12
#define		CR_OSC_SELFT_OSC_SEL_SHIFT              (10)          //KC: in E13, it is a bit (0x28 bit 11)
#define		CR_OSC_SELFT_OSC_SEL_MASK               (BIT_MASK(2))
#define		CR_OSC_SELFT_PERIOD_SHIFT				(12)          //KC: E13 found in 0x28 offset 30
#define		CR_OSC_SELFT_PERIOD_MASK				(BIT_MASK(2))
#define		CR_OSC_SELFT_DEC_SHIFT					(16)          //KC: E13 found in 0x28 offset 16
#define		CR_OSC_SELFT_DEC_MASK					(BIT_MASK(12))

#define	R32_SYS0_OSC_CTRL1							(0x38 >> 2)
#define		CR_OSC1_EN_BIT							(BIT0)
#define		CR_OSC2_EN_BIT							(BIT1)
#define		CR_OSC3_EN_BIT							(BIT2)
#define		CR_OSC1_SEL_SHIFT						(8)
#define		CR_OSC1_SEL_MASK						(BIT_MASK(2))
#define		CR_OSC2_SEL_SHIFT						(10)
#define		CR_OSC2_SEL_MASK						(BIT_MASK(2))
#define		CR_OSC3_SEL_SHIFT						(12)
#define		CR_OSC3_SEL_MASK						(BIT_MASK(2))
#define     CR_OSC1_SEL1400_BIT                     (BIT14)
#define     CR_OSC1_SEL1400_SHIFT                   (14)
#define     CR_OSC2_SEL1400_BIT                     (BIT15)
#define     CR_OSC2_SEL1400_SHIFT                   (15)
#define		CR_OSC1_UPD_BIT							(BIT16)
#define		CR_OSC2_UPD_BIT							(BIT17)
#define		CR_OSC3_UPD_BIT							(BIT18)
#define		CR_OSC1_SSC_EN_BIT						(BIT24)
#define		CR_OSC2_SSC_EN_BIT						(BIT25)
#define		CR_OSC1_DIS_BIT							(BIT27)
#define		CR_OSC2_DIS_BIT							(BIT28)
#define		CR_OSC3_DIS_BIT							(BIT29)
#define		CR_OSC1_SSC_FLAG_BIT					(BIT30)
#define		CR_OSC2_SSC_FLAG_BIT					(BIT31)

#define	R32_SYS0_OSC_CTRL2							(0x3C >> 2)
#define		CR_OSC1_CFG_SHIFT						(0)            //KC: found in 0x24 offset 8 in E13
#define		CR_OSC1_CFG_MASK						(BIT_MASK(6))
#define		CR_OSC2_CFG_SHIFT						(8)            //KC: found in 0x24 offset 24 in E13
#define		CR_OSC2_CFG_MASK						(BIT_MASK(6))
#define		CR_OSC3_CFG_SHIFT						(16)
#define		CR_OSC3_CFG_MASK						(BIT_MASK(6))
#define		CR_OSC1_SSC_CFG_SHIFT					(24)
#define		CR_OSC1_SSC_CFG_MASK					(BIT_MASK(8))

#define	R32_SYS0_OSC_CTRL3							(0x40 >> 2)
#define		CR_OSC1_TC_TRIM_SHIFT					(0)
#define		CR_OSC1_TC_TRIM_MASK					(BIT_MASK(6))
#define		CR_OSC2_TC_TRIM_SHIFT					(6)
#define		CR_OSC2_TC_TRIM_MASK					(BIT_MASK(6))
#define		CR_OSC3_TC_TRIM_SHIFT					(12)
#define		CR_OSC3_TC_TRIM_MASK					(BIT_MASK(6))
#define		CR_OSC2_SSC_CFG_SHIFT					(24)
#define		CR_OSC2_SSC_CFG_MASK					(BIT_MASK(8))

//KC:  add offsets. Similar reg. is found in 0x2C in E13
#define	R32_SYS0_OSC_TRIM0							(0x44 >> 2)
#define		CR_OSC1_SEL1200_TRIM_SHIFT				(0)
#define		CR_OSC1_SEL1200_TRIM_MASK				(BIT_MASK(8))
#define		CR_OSC1_SEL800_TRIM_SHIFT				(8)
#define		CR_OSC1_SEL800_TRIM_MASK				(BIT_MASK(8))
#define		CR_OSC1_SEL667_TRIM_SHIFT				(16)
#define		CR_OSC1_SEL667_TRIM_MASK				(BIT_MASK(8))
#define		CR_OSC1_SEL533_TRIM_SHIFT				(24)
#define		CR_OSC1_SEL533_TRIM_MASK				(BIT_MASK(8))
#define		CR_OSC1_SELxxx_TRIM_BITNUM				(8)
#define 	CR_OSC1_SELxxx_TRIM_MASK				(BIT_MASK(8))

#define	R32_SYS0_OSC_TRIM1							(0x48 >> 2) //KC: skip these offsets
#define	R32_SYS0_OSC_TRIM2							(0x4C >> 2) //KC: skip these offsets
#define		CR_LFOSC_TRIM_SHIFT						(24)
#define		CR_LFOSC_TRIM_MASK						(BIT_MASK(7))
#define	R32_SYS0_OSC_TRIM3							(0x50 >> 2) //KC: skip these offsets

//KC: add these bits offsets.
#define	R32_SYS0_FILTER_TRIM_PHY_CFG_VLD					(0x54 >> 2)  //R32_SYS0_FILTER_TRIM-->R32_SYS0_FILTER_TRIM_PHY_CFG_VLD
#define		CR_PHY_CFG_VLD_BIT						(BIT0)
#define		CR_SATA_QFN88_BIT						(BIT1)
#define		CR_FILTER_PERSTN_TRIM1_SEL_SHIFT		(2)
#define		CR_FILTER_PERSTN_TRIM1_SEL_MASK			(BIT_MASK(2))
#define		CR_FILTER_PERSTN_TRIM2_SEL_SHIFT		(4)
#define		CR_FILTER_PERSTN_TRIM2_SEL_MASK			(BIT_MASK(2))
#define		CR_FILTER_PERSTN_TRIM1_SHIFT			(6)
#define		CR_FILTER_PERSTN_TRIM1_MASK				(BIT_MASK(3))
#define		CR_FILTER_PERSTN_TRIM2_SHIFT			(9)
#define		CR_FILTER_PERSTN_TRIM2_MASK				(BIT_MASK(3))
#define		CR_FILTER_CLKREQB_TRIM1_SEL_SHIFT		(12)
#define		CR_FILTER_CLKREQB_TRIM1_SEL_MASK		(BIT_MASK(2))
#define		CR_FILTER_CLKREQB_TRIM2_SEL_SHIFT		(14)
#define		CR_FILTER_CLKREQB_TRIM2_SEL_MASK		(BIT_MASK(2))
#define		CR_FILTER_CLKREQB_TRIM1_SHIFT			(16)
#define		CR_FILTER_CLKREQB_TRIM1_MASK			(BIT_MASK(3))
#define		CR_FILTER_CLKREQB_TRIM2_SHIFT			(19)
#define		CR_FILTER_CLKREQB_TRIM2_MASK			(BIT_MASK(3))
#define		CR_FILTER_DEVSLP_TRIM1_SEL_SHIFT		(22)
#define		CR_FILTER_DEVSLP_TRIM1_SEL_MASK			(BIT_MASK(2))
#define		CR_FILTER_DEVSLP_TRIM2_SEL_SHIFT		(24)
#define		CR_FILTER_DEVSLP_TRIM2_SEL_MASK			(BIT_MASK(2))
#define		CR_FILTER_DEVSLP_TRIM1_SHIFT			(26)
#define		CR_FILTER_DEVSLP_TRIM1_MASK				(BIT_MASK(3))
#define		CR_FILTER_DEVSLP_TRIM2_SHIFT			(29)
#define		CR_FILTER_DEVSLP_TRIM2_MASK				(BIT_MASK(3))

//KC: add these bits offsets.
#define	R32_SYS0_FILTER_CFG							(0x58 >> 2)  //KC: R32_SYS0_FILTER_CTRL-->R32_SYS0_FILTER_CFG
#define		CR_FILTER_PERSTN_SEL_SHIFT				(0)
#define		CR_FILTER_PERSTN_SEL_MASK				(BIT_MASK(2))
#define		CR_FILTER_CLKREQB_SEL_SHIFT				(2)
#define		CR_FILTER_CLKREQB_SEL_MASK				(BIT_MASK(2))
#define		CR_FILTER_DEVSLP_SEL_SHIFT				(4)
#define		CR_FILTER_DEVSLP_SEL_MASK				(BIT_MASK(2))
#define     CR_FILTER_LVS_SEL_BIT                   (BIT6)
#define     CR_FILTER_LVS_CONFIG_PIN_SHIFT          (8)
#define     CR_FILTER_LVS_CONFIG_PIN_MASK           (BIT_MASK(3))

#define	R32_SYS0_REG_FPHYA_CTRL						(0x5C >> 2)

#define	R32_SYS0_ZQCAL_CFG							(0x60 >> 2)
#define		CR_K_BYPASS_RES_TRIM_BIT				(BIT0)
#define		CR_K_EN_RES_TRIM_BIT					(BIT1)
#define		CR_K_PMODE_BIT							(BIT2)
#define		CR_K_RES_TRIM_CHK_BIT					(BIT3)
#define		CR_K_ZQ_CONFIG_SHIFT					(8)
#define		CR_K_ZQ_CONFIG_MASK						(BIT_MASK(4))
#define		CR_K_ZQ_VREF_DN_SHIFT					(16)
#define		CR_K_ZQ_VREF_DN_MASK					(BIT_MASK(2))
#define		CR_K_ZQ_VREF_UP_SHIFT					(18)
#define		CR_K_ZQ_VREF_UP_MASK					(BIT_MASK(2))
#define		CR_CLK_CAL_SEL_BIT						(BIT24)

#define	R32_SYS0_ZQCAL_TRIM							(0x64 >> 2)
#define		CR_K_ZQ_RES_TRIM_IN_SHIFT				(0)
#define		CR_K_ZQ_RES_TRIM_IN_MASK				(BIT_MASK(7))

#define	R32_SYS0_REG_CTRL3							(0x70 >> 2)

/*
 *  +-----------------------------------------------------------------------+
 *  |					CLK      									        |
 *  +-----------------------------------------------------------------------+
 */
#define	SYS0_CLK_OFFSET								(0x00000400) //0x0400~0x04FF
#define	SYS0_CLK_BASE								(SYSTEM_PD0_REG_ADDRESS + SYS0_CLK_OFFSET)

#define	R8_SYS0_CLK									((REG8  *) SYS0_CLK_BASE)
#define	R16_SYS0_CLK								((REG16 *) SYS0_CLK_BASE)
#define	R32_SYS0_CLK								((REG32 *) SYS0_CLK_BASE)
#define	R64_SYS0_CLK								((REG64 *) SYS0_CLK_BASE)

#define	R32_SYS0_CLK_CTRL0							(0x00 >> 2)
#define		CR_REFCLK_SEL_SHIFT						(0)
#define		CR_REFCLK_SEL_MASK						(BIT_MASK(2))
#define     CR_SYS_CLK_SEL_SHIFT                    (2)
#define     CR_SYS_CLK_SEL_MASK                     (BIT_MASK(2))
#define     CR_SYS_DIV_SHIFT                        (4)
#define     CR_SYS_DIV_MASK                         (BIT_MASK(4))
#define     CR_COP0_RATE_SEL_SHIFT                  (8)
#define     CR_COP0_RATE_SEL_MASK                   (BIT_MASK(2))
#define     CR_RCOSC_CLK_SEL_BIT                    (BIT10)
#define     CR_ZQ_CLK_SEL_SHIFT                     (11)
#define     CR_ZQ_CLK_SEL_MASK                      (BIT_MASK(2))
#define     CR_ROM_CLK_SEL_BIT                      (BIT16)
#define     CR_CPU_CLK_SEL_SHIFT                    (17)
#define     CR_CPU_CLK_SEL_MASK                     (BIT_MASK(2))
#define 	CR_CPU_DIV_SHIFT                        (20)
#define 	CR_CPU_DIV_MASK                         (BIT_MASK(4))
#define 	CR_PIC_CLK_SEL_BIT                      (BIT25)
#define 	CR_PIC_DIV_SHIFT                        (28)
#define 	CR_PIC_DIV_MASK                         (BIT_MASK(4))

#define	R32_SYS0_CLK_CTRL1							(0x04 >> 2)
#define 	CR_AES_CLK_SEL_SHIFT                    (1)
#define 	CR_AES_CLK_SEL_MASK                     (BIT_MASK(2))
#define 	CR_AES_DIV_SHIFT                        (4)
#define 	CR_AES_DIV_MASK                         (BIT_MASK(4))
#define 	CR_SHA_CLK_SEL_SHIFT                    (9)
#define 	CR_SHA_CLK_SEL_MASK                     (BIT_MASK(2))
#define 	CR_SHA_DIV_SHIFT                        (12)
#define 	CR_SHA_DIV_MASK                         (BIT_MASK(4))
#define 	CR_SM4_CLK_SEL_SHIFT                    (17)
#define 	CR_SM4_CLK_SEL_MASK                     (BIT_MASK(2))
#define 	CR_SM4_DIV_SHIFT                        (20)
#define 	CR_SM4_DIV_MASK                         (BIT_MASK(4))
#define 	CR_LZSS_CLK_SEL_SHIFT                   (25)
#define 	CR_LZSS_CLK_SEL_MASK                    (BIT_MASK(2))
#define 	CR_LZSS_DIV_SHIFT                       (28)
#define 	CR_LZSS_DIV_MASK                        (BIT_MASK(4))

#define	R32_SYS0_CLK_CTRL2							(0x08 >> 2)
#define 	CR_FLHR_CLK_SEL_SHIFT                   (1)
#define 	CR_FLHR_CLK_SEL_MASK                    (BIT_MASK(2))
#define 	CR_FLHW_CLK_SEL_SHIFT                   (9)
#define 	CR_FLHW_CLK_SEL_MASK                    (BIT_MASK(2))
#define 	CR_ECC_CLK_SEL_SHIFT                    (17)
#define 	CR_ECC_CLK_SEL_MASK                     (BIT_MASK(2))
#define 	CR_ECC_DIV_SHIFT                        (19)             //KC: svn3078: (20)-->(19)
#define 	CR_ECC_DIV_MASK                         (BIT_MASK(5))    //KC: svn3078: (4)-->(5)

#define	R32_SYS0_CLK_CTRL3							(0x0C >> 2)
#define 	CR_SPI_CLK_SEL_BIT                      (BIT1)
#define 	CR_SPI_DIV_SHIFT                        (4)
#define 	CR_SPI_DIV_MASK                         (BIT_MASK(4))
#define 	CR_TS_CLK_SEL_BIT                       (BIT8)
#define 	CR_TS_DIV_SHIFT                         (12)
#define 	CR_TS_DIV_MASK                          (BIT_MASK(4))
#define 	CR_TRACE_CLK_SEL_BIT                    (BIT16)
#define 	CR_TRACE_DIV_SHIFT                      (20)
#define 	CR_TRACE_DIV_MASK                       (BIT_MASK(4))

#define	R32_SYS0_CLK_CTRL4							(0x10 >> 2)
#define 	PCIE_SYSCLK_FREQ_SHIFT                  (0)
#define 	PCIE_SYSCLK_FREQ_MASK                   (BIT_MASK(9))

#define	R32_SYS0_CLK_CTRL5							(0x14 >> 2)

#define	R32_SYS0_CLK_CTRL6							(0x18 >> 2)
#define 	CR_CPU_CKEN_BIT                         (BIT0)
#define 	CR_AES_CKEN_BIT                         (BIT1)
#define 	CR_SHA_CKEN_BIT                         (BIT2)
#define 	CR_LZSS_CKEN_BIT                        (BIT3)
#define 	CR_SPI_CKEN_BIT                         (BIT4)
#define 	CR_PIC_CKEN_BIT                         (BIT5)
#define 	CR_FLHR_CKEN_SHIFT                      (6)
#define 	CR_FLHR_CKEN_MASK                       (BIT_MASK(2))
#define 	CR_FLHW_CKEN_SHIFT                      (8)
#define 	CR_FLHW_CKEN_MASK                       (BIT_MASK(2))
#define 	CR_ECC_CKEN_BIT                         (BIT10)
#define 	CR_COR_CKEN_BIT                         (BIT11)
#define 	CR_ZQ_CKEN_BIT                          (BIT12)
#define 	CR_SM4_CKEN_BIT                         (BIT13)
#define 	CR_SATA_CKEN_BIT                        (BIT15)
#define 	CR_TS_CKEN_BIT                          (BIT16)
#define 	CR_TRACE_CKEN_BIT                       (BIT17)
#define 	CR_SYS_CKEN_BIT                         (BIT18)
#define 	CR_NVME_CKEN_BIT                        (BIT19)
#define 	CR_HOST_CKEN_BIT                        (BIT20)
#define 	CR_SD_CKEN_BIT                          (BIT21)
#define 	CR_CREG_CKEN                            (BIT22)       //KC:svn3078 
#define 	CR_D2H_CKEN                             (BIT23)       //KC:svn3078 
#define 	CR_USB_PD1_CKEN                         (BIT24)       //KC:svn3078
#define 	CR_HOST_F_PD50_CKEN                     (BIT25)       //KC:svn3078

/*
 *  +-----------------------------------------------------------------------+
 *  |					PMU      									        |
 *  +-----------------------------------------------------------------------+
 */
#define	SYS0_PMU_OFFSET								(0x00000500)
#define	SYS0_PMU_BASE								(SYSTEM_PD0_REG_ADDRESS + SYS0_PMU_OFFSET)

#define R8_SYS_PMU									((REG8  *) SYS0_PMU_BASE)
#define R16_SYS_PMU									((REG16 *) SYS0_PMU_BASE)
#define R32_SYS_PMU									((REG32 *) SYS0_PMU_BASE)
#define R64_SYS_PMU									((REG64 *) SYS0_PMU_BASE)

#define	R32_SYS0_PMU_PD_CTRL									((0x00) >> 2)
#define		CR_PMU_EXPWR_EN_BIT									(BIT0)
#define		CR_PMU_REF_GCK										(BIT2)
#define		CR_PMU_PD_F_BIT										(BIT15)
#define		CR_PMU_WU_F_BIT										(BIT31)

#define	R32_SYS0_PMU_WAKEUP_FLAG								((0x04) >> 2)
#define		WAKEUP_F_BIT										(BIT0)
#define		CR_FW_T_F_BIT										(BIT31)

#define	R32_SYS0_PMU_COP_CMD_ADR0								((0x08) >> 2)
#define		CR_ADR_FW_SHIFT										(0)
#define		CR_ADR_FW_MASK										(BIT_MASK(16))
#define		CR_ADR_FW											(CR_ADR_FW_MASK << CR_ADR_FW_SHIFT)
#define		CR_ADR_CKG_SHIFT									(16)
#define		CR_ADR_CKG_MASK										(BIT_MASK(16))
#define		CR_ADR_CKG											(CR_ADR_CKG_MASK << CR_ADR_CKG_SHIFT)

#define	FW_SELECT_MODE_TOGGLE2_533								(0x0009)	// E17
#define	FW_SELECT_MODE_TOGGLE2_667								(0x000A)	// E17

#define	R32_SYS0_PMU_COP_CMD_ADR1								((0x0C) >> 2)
#define		CR_ADR_SD_SHIFT										(0)
#define		CR_ADR_SD_MASK										(BIT_MASK(16))
#define		CR_ADR_SD											(CR_ADR_SD_MASK << CR_ADR_SD_SHIFT)
#define		CR_ADR_WU_SHIFT										(16)
#define		CR_ADR_WU_MASK										(BIT_MASK(16))
#define		CR_ADR_WU											(CR_ADR_WU_MASK << CR_ADR_WU_SHIFT)

#define	SYSTEM_WAKE_UP_LPM_1									(0x0006)
#define	SYSTEM_WAKE_UP_LPM_2									(0x0007)
#define	SYSTEM_WAKE_UP_LPM_3									(0x0008)

#define	SYSTEM_SHUT_DOWN_LPM_1									(0x0003)
#define	SYSTEM_SHUT_DOWN_LPM_2									(0x0004)
#define	SYSTEM_SHUT_DOWN_LPM_3									(0x0005)

#define	R32_SYS0_PMU_ASYNC_RST_CTRL								((0x10) >> 2)
#define	R8_SYS0_PMU_ASYNC_RST_CTRL								(0x10)
#define		PMU_AS_WAKE_EVT_MASK_GPIO_HIGH_BIT					(BIT0)
#define		PMU_AS_WAKE_EVT_MASK_GPIO_LOW_BIT					(BIT1)
#define		PMU_AS_WAKE_EVT_MASK_DEVSLP_BIT						(BIT2)
#define		PMU_AS_WAKE_EVT_MASK_PERSTN_BIT						(BIT3)
#define		PMU_AS_WAKE_EVT_MASK_CLKREQB_BIT					(BIT4)
#define		PMU_AS_WAKE_EVT_MASK_PD_51_WAKE_BIT					(BIT5)
#define		NFE_INTR_EVENT_BIT                  				(BIT6)
#define		PMU_PCIE_WAKE_EVENT_BIT			                    (BIT7)
#define		SR_PD51_OFF_AS_WAKE_BIT								(BIT8)
#define		PMU_AS_WAKE_EVT_MASK_SD_WAKEUP_EVT2_BIT 			(BIT9)
#define		PMU_AS_WAKE_EVT_MASK_SD_WAKEUP_EVT1_BIT 			(BIT10)
#define		PMU_AS_WAKE_EVT_MASK_SD_WAKEUP_EVT0_BIT 			(BIT11)
#define		PMU_AS_WAKE_EVT_MASK_USB_WAKEUP_EVT_BIT 			(BIT12)
#define		PMU_AS_WAKE_EVT_MASK_CPHY_WAKE_EVT_BIT 			    (BIT13)

#define		RCOSC_AS_WAKE_EVT_MASK_PMU_WAKE_FLAG				(BIT18) // E13 (BIT10)
#define		RCOSC_AS_WAKE_EVT_MASK_SR_PD51_WAKE_AS_N	    	(BIT21) // U17
#define		RCOSC_AS_WAKE_EVT_MASK_UTMI_AS_WAKE_EVT				(BIT23) // U17
#define		RCOSC_AS_WAKE_EVT_MASK_PIPE_AS_WAKE_EVT				(BIT24) // U17
#define		PMU_AS_WAKE_MSK_BIT									(BIT30) // E13 (BIT16)
#define		RCOSC_AS_WAKE_MSK_BIT								(BIT31) // E13 (BIT17)

#define	RCOSC_AS_WAKE_EVT_MSK_SHIFT								(16)
#define	RCOSC_AS_WAKE_EVT_MSK_MASK								BIT_MASK(12)
#define	RCOSC_AS_WAKE_EVT										(RCOSC_AS_WAKE_EVT_MSK_MASK >> RCOSC_AS_WAKE_EVT_MSK_SHIFT)
#define	PMU_AS_WAKE_EVT_MSK_SHIFT								(0)
#define	PMU_AS_WAKE_EVT_MSK_MASK								BIT_MASK(16)
#define	PMU_AS_WAKE_EVT_MSK										(PMU_AS_WAKE_EVT_MSK_MASK >> PMU_AS_WAKE_EVT_MSK_SHIFT)

/*// E13
#define		PMU_AS_WAKE_EVT_MASK_MPHY_REST_N_BIT				(BIT7)
#define		RCOSC_AS_WAKE_EVT_MASK_PMU_WAKE_FLAG                (BIT10)
#define		NFE_INTR_EVENT_BIT									(BIT24)
#define		PMU_PCIE_WAKE_EVENT_BIT								(BIT25)
#define		SR_PD51_OFF_AS_WAKE_BIT								(BIT28)*/

#define R8_SYS0_RCOSC_AS_WAKE_EVT_MASK							(0x12)
#define		RCOSC_AS_WAKE_EVT_MASK_GPIO_HIGH_BIT				(BIT0)
#define		RCOSC_AS_WAKE_EVT_MASK_GPIO_LOW_BIT					(BIT1)
#define		RCOSC_AS_WAKE_EVT_MASK_PMU_WAKE_BIT					(BIT2)
//#define		RCOSC_AS_WAKE_EVT_MASK_DEVSLP_BIT					(BIT2) //E13 spec is ~pmu_wake_f_w in 0x11, E17 HW is the same but in 0x12
#define		RCOSC_AS_WAKE_EVT_MASK_PERSTN_BIT					(BIT3)
#define		RCOSC_AS_WAKE_EVT_MASK_CLKREQB_BIT					(BIT4)
#define		RCOSC_AS_WAKE_EVT_MASK_PD_51_WAKE_BIT				(BIT5)
#define		RCOSC_AS_WAKE_EVT_MASK_DEVSLP_SA_BIT				(BIT6)
#define		RCOSC_AS_WAKE_EVT_MASK_UTMI_WAKE_BIT				(BIT7)
//#define		RCOSC_AS_WAKE_EVT_MASK_UNI_WAKE_BIT					(BIT6) // E13
//#define		RCOSC_AS_WAKE_EVT_MASK_MPHY_REST_N_BIT				(BIT7) // E13

#define	R32_SYS0_PMU_RST_CTRL									((0x14) >> 2)
#define     CR_RST_CPU_BIT                                      (BIT0)
#define		CR_RST_N_PTM_BIT									(BIT2)
#define		CR_RST_N_FLH_SYS_BIT								(BIT3)
#define		CR_RST_N_FLH_ECC_BIT								(BIT4)
#define		CR_RST_N_FLH_MDLL_BIT								(BIT5)
#define		CR_RST_N_ALL_BIT									(BIT7)      // whole chip reset
#define		CR_RST_N_PIC_BIT									(BIT8)
#define		CR_RST_N_COP0_BIT									(BIT9)      // COP0 reset
#define		CR_RST_N_COP1_BIT									(BIT10)     // COP1 reset
//#define		CR_RST_N_AXI_BIT									(BIT11)     // AXI reset //E17, move to PD0 MISCH offset 0h
#define		CR_RST_N_SEC_BIT									(BIT12)
#define		CR_RST_N_SPI_BIT									(BIT13)
#define		CR_RST_N_MR_BIT										(BIT14)
#define		CR_RST_N_DMAC_BIT									(BIT15)     // DMAC reset
#define		CR_RST_N_HOST_BIT									(BIT16)     // HOST (NVME) reset
#define		CR_RST_N_HOST_ND_BIT								(BIT17)      // HOST (NVME) reset
#define		CR_RST_N_CPHY_BIT									(BIT18)
#define		CR_RST_N_HLLR_BIT									(BIT19)
//#define		CR_RST_N_AHB_BIT									(BIT20) //E17 HW Remove
#define		CR_RST_N_USB_BIT									(BIT21)
#define		CR_RST_N_PCIE_BIT									(BIT22)
#define		CR_RST_N_NVME_BIT									(BIT23)
#define		CR_RST_N_FPHY_SHIFT 								(24)
#define		CR_RST_N_FPHY_MASK 									BIT_MASK(2)
#define		CR_RST_N_FPHY 										(CR_RST_N_FPHY_MASK << CR_RST_N_FPHY_SHIFT)
#define		CR_RST_N_WDT_BIT									(BIT28)
#define		CR_RST_N_ZIP_BIT									(BIT29)
#define		CR_RST_N_BMU_BIT									(BIT30)     // BMU reset
#define		CR_RST_N_DBUF_BIT									(BIT31)     // DBUF reset

#define SRST_IP_FW_INIT											(CR_RST_N_COP0_BIT | CR_RST_N_COP1_BIT | CR_RST_N_BMU_BIT | CR_RST_N_DMAC_BIT | \
																CR_RST_N_PIC_BIT | CR_RST_N_SEC_BIT | CR_RST_N_SPI_BIT | CR_RST_N_WDT_BIT | \
																CR_RST_N_ZIP_BIT | CR_RST_N_MR_BIT | CR_RST_N_DBUF_BIT)

#define	R32_SYS0_PMU_MODE_EN									((0x18) >> 2)
#define	R16_SYS0_PMU_MODE_EN									((0x18) >> 1)
#define		MD_PRAM_EN_BIT										(BIT0)
#define		MD_DEV_TRI_BIT										(BIT1)
#define		MD_VDT_TEST_BIT										(BIT2)
#define		MD_EA4_EN_BIT										(BIT3)
#define		MD_EA3_EN_BIT										(BIT4)
#define		MD_EA2_EN_BIT										(BIT5)
#define		MD_EA1_EN_BIT										(BIT6)
#define		MD_SPD_TEST_BIT										(BIT7)
#define		MD_ICE_EN_BIT										(BIT8)
#define		MD_SPI_EN_BIT										(BIT9)
#define		MD_ROM_EN_BIT										(BIT10)
#define			MSK_PMU_MD										(MD_PRAM_EN_BIT | MD_ICE_EN_BIT | MD_SPI_EN_BIT | MD_ROM_EN_BIT)
#define     SR_1CH_MODE                                         (BIT14)
#define		SR_1CH_MD_SHIFT										(14) //E17 HW Change to SR_1CH_MODE
#define		SR_1CH_MD_MASK										(BIT_MASK(1)) //E17 HW Change to SR_1CH_MODE
#define		SR_1CH_MD											(SR_1CH_MD_MASK << SR_1CH_MD_SHIFT) //E17 HW Change to SR_1CH_MODE
/*// E13
#define		SR_UFS_FBM_BIT										(BIT11)
#define		SR_XHMD_SHIFT										(12)
#define		SR_XHMD_MASK										(BIT_MASK(2))
#define		SR_XHMD												(SR_XHMD_MASK << SR_XHMD_SHIFT)
#define		SR_4CH_F_BIT										(BIT14)
#define		SR_SYS_PLL_F_BIT									(BIT15)
#define		FPGA_LD_ROM_EN_BIT									(BIT16)*/
#define		ATCM_INITIAL_DIS_BIT								(BIT16)
#define		MD_UARTROM_BIT										(BIT17)
#define		MD_GIO_TEST_BIT										(BIT18)
#define		SR_FORCE_BOOT_MODE_BIT								(BIT19)
#define		MD_PTM_BIT_BIT										(BIT20)
#define		CR_CPU_STALL_BIT									(BIT24)

#define R32_SYS0_PMU_WU_SEL										((0x1C) >> 2)                                         //Default: 0x00000000    Initialized: 0xFFFFFFFF
#define R16_SYS0_PMU_WU_SEL_CR_WAKE_EN							((0x1C) >> 1)
#define		CR_WAKE_EN_CLKREQB_FALL_BIT							(BIT0)
#define		CR_WAKE_EN_CLKREQB_RISE_BIT							(BIT1)
#define		CR_WAKE_EN_CLKREQB_FALLING_EDGE_BIT					(BIT2)
#define		CR_WAKE_EN_CLKREQB_RISING_EDGE_BIT					(BIT3)
#define		CR_WAKE_EN_GPIO_FALL_BIT							(BIT4)
#define		CR_WAKE_EN_GPIO_RISE_BIT							(BIT5)
#define		CR_WAKE_EN_GPIO_FALLING_EDGE_BIT					(BIT6)
#define		CR_WAKE_EN_GPIO_RISING_EDGE_BIT						(BIT7)
#define		CR_WAKE_EN_RTT_WU_EN_BIT							(BIT8)
#define		CR_WAKE_EN_PCIE_WU_EN_BIT							(BIT9)
#define		CR_WAKE_EN_NFE_WU_EN_BIT							(BIT10)
#define		CR_WAKE_EN_ITC_WU_EN_BIT							(BIT11)
#define		CR_WAKE_EN_PMU_WU_F_EN_BIT							(BIT12)
#define		CR_WAKE_EN_SATA_WU_EN_BIT							(BIT13)
#define		CR_WAKE_EN_DEVSLP_FALLING_EDGE_BIT					(BIT14)
#define		CR_WAKE_EN_DEVSLP_RISING_EDGE_BIT					(BIT15)
#define		CR_WAKE_EN_PIC_WU_EN_BIT							(BIT16)
#define		CR_WAKE_EN_USB_WU_EN								(BIT17)
#define		CR_WAKE_EN_SD_WU_EVT0_EN							(BIT18)
#define		CR_WAKE_EN_SD_WU_EVT1_EN							(BIT19)
#define		CR_WAKE_EN_SD_WU_EVT2_EN							(BIT20)
#define		CR_WAKE_EN_JTAG_WU_EN							    (BIT21)
#define		CR_WAKE_EN_SPHY_WU_EN							    (BIT22)
#define		CR_WAKE_EN_CPHY_WU_EN							    (BIT23)

#define	R32_SYS0_PMU_PG_CTRL									((0x20) >> 2)                                         //Default: 0x0000E0FC    Initialized: 0xFFFFFFFF
#define R8_SYS0_PMU_PG_CTRL										(0x20)
#define		CR_FIO_PG_PD0_BIT									(BIT3)
#define		CR_IO1_33_PG_BIT								    (BIT4)
#define		CR_IO1_18_PG_BIT								    (BIT5)
#define		CR_FIO12_PG_PD1_BIT									(BIT6)
#define		CR_FIO18_PG_PD0_BIT									(BIT7)
#define		CR_PD_LV_MSK_BIT									(BIT8)
#define		CR_PD_LV_MSK_PRE_BIT								(BIT9)
#define		CR_PD0_PG_BIT										(BIT10)	// E17
#define		CR_IO0_18_PG_BIT									(BIT11) // CR_IO18_PG_BIT in E13
#define		CR_PD51_PG_BIT										(BIT12)
#define		CR_PD20_PG_BIT										(BIT13) // E17
#define		CR_PD1_PG_BIT										(BIT14)
#define		CR_PD31_PG_BIT										(BIT15)	// E17
#define		CR_PD0_RST_BYPASS_BIT								(BIT16) // CR_PD_LV_PG_BYPASS_BIT in E13
#define		PMU_HOST_MSK_BIT									(BIT24)
#define		PMU_HOST_MSK_REQ_BIT								(BIT25)
#define		SR_HOST_STS_BIT										(BIT31)

#define	R32_SYS0_PMU_CTRL0										((0x24) >> 2)
#define	R8_SYS0_PMU_CTRL0										(0x24)
#define		PMU_LV_DISABLE_BIT									(BIT0)
#define     PMU_PD1_PD_GPIO_EN_BIT								(BIT1)
/*// E13
#define		LOWK_VPHY_SHIFT										(2)
#define		LOWK_VPHY_MASK										(BIT_MASK(3))
#define		LOWK_VPHY											(LOWK_VPHY_MASK << LOWK_VPHY_SHIFT)
#define 	LOWK_CORE_SHIFT										(5)
#define 	LOWK_CORE_MASK										(BIT_MASK(3))
#define 	LOWK_CORE											(LOWK_CORE_MASK << LOWK_CORE_SHIFT)*/
#define		PMU_LV_GPIO_EN_BIT									(BIT8)
#define     PMU_FIO_PD_GPIO_EN_BIT								(BIT9)
#define		CR_LV_STRB_BIT								        (BIT16) // E17
#define		LOWK_VPHY_W_OR_BIT								    (BIT17) // E17
#define		LOWK_CORE_WIRE_OR_BIT								(BIT18)
#define		CR_OP_CORE_STB_EN_BIT								(BIT19)
#define     CR_OP_VPHY_STB_EN_BIT								(BIT21)
#define		CR_OP_FIO_STB_EN_BIT								(BIT22)
#define     PMU_VDT_ON_DIS_BIT								    (BIT24) // E17
#define     CR_OP_VPHY_DIS_SOFT_EN_BIT							(BIT25)
#define		CR_OP_FIO_DIS_SOFT_EN_BIT							(BIT26)
#define     CR_OP_CORE_WARMUP_PD_BIT							(BIT27)
#define		CR_OP_VPHY_WARMUP_PD_BIT							(BIT28)
#define		CR_OP_FIO_WARMUP_PD_BIT								(BIT29)

#define	R32_SYS0_PMU_CTRL1										((0x28) >> 2)
#define		CR_GPIO_IO1_STB_BIT								    (BIT16)
#define		CR_OP_GPIO_IO1_STB_BIT								(BIT17)

#define	R32_SYS0_PMU_MEMPD_CTRL									((0x2C) >> 2)
#define		CR_OP_EXROM_EN_BIT									(BIT0)  // -ICache enable in Ext. ROM mode - PS5011 no ICache
#define		CR_OP_PD1_PD_BIT									(BIT8)
#define		CR_OP_PD31_PD_BIT									(BIT9) // E17
#define		CR_OP_PD50_PD_BIT									(BIT10)
#define		CR_OP_PD51_PD_BIT									(BIT11)
#define		CR_OP_SPHY_PD_BIT									(BIT12) // E17
#define		CR_OP_SYSREF_SEL_BIT								(BIT13) // E17
#define		CR_OP_VDT_WU_BIT									(BIT14)
//#define		CR_OP_PD1_REG_PD_BIT								(BIT15) // E13
#define		CR_OP_PD0_RAM_DS_BIT								(BIT16)
#define		CR_OP_PD1_RAM_DS_BIT								(BIT17)
//#define		CR_OP_PD50_RAM_DS_BIT								(BIT18)
//#define		CR_OP_PD51_RAM_DS_BIT								(BIT19)
#define		CR_OP_PD0_DBF_RAM_DS_SHIFT							(20)
#define		CR_OP_PD0_DBF_RAM_DS_MASK							(BIT_MASK(2))
#define		CR_OP_PD0_DBF_RAM_DS								(CR_OP_PD0_DBF_RAM_DS_MASK << CR_OP_PD0_DBF_RAM_DS_SHIFT)
//#define		CR_OP_PD1_CPU_ROM_DS_BIT							(BIT23)
#define		CR_OP_PD0_RAM_SD_BIT								(BIT24) // E17
#define		CR_OP_PD1_RAM_SD_BIT								(BIT25) // E17
/* // E13
#define		CR_OP_PD50_RAM_PD_BIT								(BIT26)
#define		CR_OP_PD51_RAM_PD_BIT								(BIT27)
#define     CR_OP_PD0_DBUF_RAM_PD_256KB                         (BIT28)
#define     CR_OP_PD0_DBUF_RAM_PD_8KB                           (BIT29)
#define     CR_OP_PD0_DBUF_RAM_PD_184KB                         (BIT30)
#define		CR_OP_PD0_DBUF_RAM_PD_SHIFT							(28)
#define		CR_OP_PD0_DBUF_RAM_PD_MASK							(BIT_MASK(3))
#define		CR_OP_PD0_DBUF_RAM_PD								(CR_OP_PD0_DBUF_RAM_PD_MASK << CR_OP_PD0_DBUF_RAM_PD_SHIFT) */
#define		CR_OP_PRAM_EN_BIT									(BIT31)

#define	R32_SYS0_PMU_WKCNT_VAL									((0x30) >> 2)
#define		SR_SM_CNT_SHIFT 									(0)
#define		SR_SM_CNT_MASK 										BIT_MASK(24)
#define		SR_SM_CNT											(SR_SM_CNT_MASK << SR_SM_CNT_SHIFT)
#define		CR_WK_CNT_SHIFT										(24)
#define		CR_WK_CNT_MASK										BIT_MASK(4)
#define		CR_WK_CNT											(CR_WK_CNT_MASK << CR_WK_CNT_SHIFT)
#define		SM_CNT_TO_W_BIT										(BIT31)

#define	R32_SYS0_PMU_ISO_CTRL									((0x34) >> 2)
#define		CR_RST_PD1_EN_BIT									(BIT0)
#define		CR_RST_PD50_EN_BIT									(BIT2)
#define		CR_RST_PD51_EN_BIT									(BIT3)
#define		CR_RST_PD31_EN_BIT									(BIT5)
#define		SR_PDOFF_RST_N_BIT									(BIT7)
#define		CR_ISO_PD1_EN_BIT									(BIT8)
#define		CR_ISO_PD50_EN_BIT									(BIT10)
#define		CR_ISO_PD51_EN_BIT									(BIT11)
#define		CR_ISO_PD31_EN_BIT									(BIT13)
#define		CR_PD_PD1_EN_BIT									(BIT16)
#define		CR_PD_PD50_EN_BIT									(BIT18)
#define		CR_PD_PD51_EN_BIT									(BIT19)
#define		CR_PD_PD20_EN_BIT									(BIT20)
#define		CR_PD_PD31_EN_BIT									(BIT21)
#define		PMU_FLH_INI_N_SHIFT									(24)
#define 		CTRSTBYB_IN										(SET_BIT24)		// ref. FIP spec 5.35 ZQ calibration boot-up manual flow
#define		PMU_FLH_INI_N_MASK									BIT_MASK(2)
#define		PMU_FLH_INI_N										(PMU_FLH_INI_N_MASK << PMU_FLH_INI_N_SHIFT)
#define		CR_SATA_PHY_PG_BIT									(BIT29)
#define		CR_SATA_PHY_ISO_BIT									(BIT30)

#define	R32_SYS0_REF_CKEN										((0x38) >> 2)
#define		CR_REF_CKEN_BIT										(BIT0)
#define		PMU_RST_PD1_ENB_BIT									(BIT1)
#define		PMU_RST_PD50_ENB_BIT								(BIT2)
#define		PMU_RST_PD51_ENB_BIT								(BIT3)
#define		PD01_PG_W_BIT										(BIT4)
#define		CR_XTAL_PD_BIT										(BIT8)
#define		CR_CMN_PG_BIT										(BIT24)
#define		CR_SATA_PG_BIT										(BIT25)
/* // E13
#define		CR_SATA_RC_PD_BIT									(BIT14)
#define		CR_RCOSC_PD_BIT										(BIT15)
#define		CR_REG_REG_EN_SHIFT									(16)
#define		CR_REG_REG_EN_MASK									(BIT_MASK(2))
#define		CR_REG_REG_EN										(CR_REG_REG_EN_MASK << CR_REG_REG_EN_SHIFT)
#define		CR_REG_PSW_EN_SHIFT									(24)
#define		CR_REG_PSW_EN_MASK									(BIT_MASK(2))
#define		CR_REG_PSW_EN										(CR_REG_PSW_EN_MASK << CR_REG_PSW_EN_SHIFT)*/

#define	R32_SYS0_CPU_RAM_EN										((0x40) >> 2)
#define		CR_RST_N_CPU0_INDEP_BIT								(BIT3)
#define		RST_N_CPU_SYS_BIT									(BIT7)
#define		CR_RST_N_CPU_BIT									(BIT15)
#define		CR_RST_N_POTRST										(BIT16)
#define		CR_RST_N_TRST										(BIT17)
#define		CR_RST_N_FLH_CTRL_SHIFT								(24)
#define		CR_RST_N_FLH_CTRL_MASK								(BIT_MASK(2))
#define		CR_RST_N_FLH_CTRL									(CR_RST_N_FLH_CTRL_MASK << CR_RST_N_FLH_CTRL_SHIFT)

#define	R32_SYS0_AES_KEY_CTRL									((0x44) >> 2)
#define	R8_SYS0_AES_KEY_CTRL									((0x44) >> 0)
#define		PMU_FLH_OE_BIT										(BIT0) // E17 - Just control by PMU
#define		PMU_KEY_DIS_MSK_BIT									(BIT8) // E17 
#define		PMU_AES_DECKEY_BYTE0_SHIFT							(16)
#define		PMU_AES_DECKEY_BYTE0_MASK							BIT_MASK(8)
#define		PMU_AES_DECKEY_BYTE0								((U32)(PMU_AES_DECKEY_BYTE0_MASK << PMU_AES_DECKEY_BYTE0_SHIFT))
#define		PMU_AES_FORCE_ENB_BIT								(BIT24)

#define	R32_SYS0_PMU_CTRL2										((0x48) >> 2)
#define		CUR_PMU_SM_SHIFT							        (0)
#define		CUR_PMU_SM_MASK							            BIT_MASK(15)
#define		CUR_PMU_SM								            ((U32)(CUR_PMU_SM_MASK << CUR_PMU_SM_SHIFT))
#define		SR_PMU_ST_LV_BIT									(BIT31)

#define	R32_SYS0_PMU_CTRL3										((0x4C) >> 2)                                         //Default: 0x00000000    Initialized: 0xFFFFFFFF
#define		CR_PMU_LV_SLEW_CNT_SHIFT							(0)
#define		CR_PMU_LV_SLEW_CNT_MASK								BIT_MASK(8)
#define		CR_PMU_LV_SLEW_CNT									((U32)(CR_PMU_LV_SLEW_CNT_MASK << CR_PMU_LV_SLEW_CNT_SHIFT))
#define		CR_PLL_PD2DIS_DELAY_CNT_SHIFT						(8)
#define		CR_PLL_PD2DIS_DELAY_CNT_MASK						BIT_MASK(6)
#define		CR_PLL_PD2DIS_DELAY_CNT								((U32)(CR_PLL_PD2DIS_DELAY_CNT_MASK << CR_PLL_PD2DIS_DELAY_CNT_SHIFT))
#define		CR_PLL_PD2DIS_DELAY_CNT2_SHIFT						(16)
#define		CR_PLL_PD2DIS_DELAY_CNT2_MASK						BIT_MASK(6)
#define		CR_PLL_PD2DIS_DELAY_CNT2							((U32)(CR_PLL_PD2DIS_DELAY_CNT2_MASK << CR_PLL_PD2DIS_DELAY_CNT2_SHIFT))
#define		PCIE_MD_STD_LV12_BIT								(BIT24)
#define		SPD_MD_SEL_BIT									    (BIT25)
#define		SPD_PCI_PD51_PD_BIT									(BIT27)
#define		XPHY_PLL_LOCK_BIT									(BIT31)

#define	R32_SYS0_PMU_COP_MEMPD_CTRL								((0x50) >> 2)                                         //Default: 0x00000000    Initialized: 0xFFFFFFFF
//#define		CR_PD0_RAM_DS_BIT									(BIT0)  // E13
#define		CR_PD1_RAM_DS_BIT									(BIT1)
//#define		CR_PD50_RAM_DS_BIT									(BIT2)  // E13
//#define		CR_PD51_RAM_DS_BIT									(BIT3)  // E13
#define		CR_PD0_DBUF_RAM_DS_SHIFT							(4)
#define		CR_PD0_DBUF_RAM_DS_MASK								(BIT_MASK(2))
#define		CR_PD0_DBUF_RAM_DS									(CR_PD0_DBUF_RAM_DS_MASK << CR_PD0_DBUF_RAM_DS_SHIFT)
//#define		CR_PD1_CPU_ROM_DS_BIT								(BIT7)	// E13
//#define		CR_PD0_RAM_PD_BIT									(BIT8)  // E13
#define		CR_PD1_RAM_SD_BIT									(BIT9)
/* // E13
#define		CR_PD50_RAM_PD_BIT									(BIT10)
#define		CR_PD51_RAM_PD_BIT									(BIT11)
#define		CR_PD0_DBF_RAM_PD_SHIFT								(12)
#define		CR_PD0_DBF_RAM_PD_MASK								(BIT_MASK(3))
#define		CR_PD0_DBF_RAM_PD									(CR_PD0_DBUF_RAM_PD_MASK << CR_PD0_DBUF_RAM_PD_SHIFT)
#define		CR_PD0_RAM_LS_BIT									(BIT16)*/
#define		CR_PD1_RAM_LS_BIT									(BIT17)
#define		CR_PD0_DBF_RAM_LS_BIT								(BIT18)
//#define		CR_PD51_RAM_LS_BIT									(BIT19)	// E13
#define		CR_PMU_RAM_LS_EN_BIT								(BIT21)
#define		CR_CLKREQB_BYPASS_BIT								(BIT24)
#define		CR_CLKREQB_DBC_SEL_SHIFT							(30)
#define		CR_CLKREQB_DBC_SEL_MASK								BIT_MASK(2)
#define		CR_CLKREQB_DBC_SEL									(CR_CLKREQB_DBC_SEL_MASK << CR_CLKREQB_DBC_SEL_SHIFT)

#define	R32_SYS0_PMU_ITC_WU_EVENT_STS							((0x54) >> 2)                                         //Default: 0x00000000    Initialized: 0xFFFFFFFF
#define		PMU_ITC_STS_SHIFT									(0)
#define		PMU_ITC_STS_MASK									BIT_MASK(24)
#define		PMU_ITC_STS											((U32)(PMU_ITC_STS_MASK << PMU_ITC_STS_SHIFT))
#define			PMU_ITC_STS_CLKREQB_FALLING_EDGE_BIT			(BIT0)
#define			PMU_ITC_STS_CLKREQB_RISING_EDGE_BIT				(BIT1)
#define			PMU_ITC_STS_GPIO_FALLING_EDGE_BIT				(BIT2)
#define			PMU_ITC_STS_GPIO_RISING_EDGE_BIT				(BIT3)
#define			PMU_ITC_STS_PCIE_WU_EVENT_BIT					(BIT4)
#define			PMU_ITC_STS_NFE_WU_EVENT_BIT					(BIT5)
#define			PMU_ITC_STS_USB_WU_EVENT_BIT	                (BIT6)	// E17
#define			PMU_ITC_STS_SPHY_WU_EVENT_BIT		            (BIT7)  // E17
#define			PMU_ITC_STS_SATA_WU_EVENT_BIT					(BIT8)
#define			PMU_ITC_STS_CPHY_WU_EVENT_BIT					(BIT9)
#define			PMU_ITC_STS_DEVSLP_FALLING_EDGE_BIT				(BIT11)
#define			PMU_ITC_STS_DEVSLP_RISING_EDGE_BIT				(BIT12)
#define			PMU_ITC_STS_SRAM_PARITY_ERROR_BIT				(BIT13)
#define         PMU_ITC_STS_PIC_WU_EVENT_BIT                    (BIT14) // E17
#define         PMU_ITC_STS_RTT0_WU_EVENT_BIT                   (BIT15) // E17
#define         PMU_ITC_STS_JTAG_WU_EVENT_BIT                   (BIT16) // E17
#define			PMU_ITC_STS_PMU_ROM_PARITY_ERROR_BIT			(BIT17)
#define			PMU_ITC_STS_SD_WU_EVENT0_BIT	                (BIT18) // E17
#define			PMU_ITC_STS_SD_WU_EVENT1_BIT	                (BIT19) // E17
#define			PMU_ITC_STS_SD_WU_EVENT2_BIT	                (BIT20) // E17

#define	R32_SYS0_PMU_RRAM_BOOT									((0x58) >> 2)
#define		CR_ADR_RRAMBOOT_SHIFT								(0)
#define		CR_ADR_RRAMBOOT_MASK								BIT_MASK(16)
#define		CR_ADR_RRAMBOOT										((U32)(CR_ADR_RRAMBOOT_MASK << CR_ADR_RRAMBOOT_SHIFT))
#define		CR_RRAM_BOOT_EN_BIT									(BIT16)

#define	R32_SYS0_PMU_CPU_STATUS_CTRL							((0x5C) >> 2)
//#define		NCLKSTOPPED_W_BIT									(BIT0) // E13
//#define		NWFEPIPESTOPPED_W_BIT								(BIT1) // E13
#define		SYSTEM_CLOCK_GATING_BIT								(BIT7)
#define		CPU_ACTIVE_STATUS_BIT								(BIT16)
#define		CR_LEAVE_LPM_BIT									(BIT24) // 1: Stop to execute power down mode.

#define	R32_SYS0_PMU_SATA_STATUS_CTRL							((0x60) >> 2)
#define		CR_DVSLP_BYPASS_BIT									(BIT0)  // E17
#define		CR_OP_FLH_VREF_OFF_BIT								(BIT8)
#define		CR_OP_LPM2_DLL_OFF_BIT								(BIT9)  // E17
#define		CR_OP_GPIO_PD_BIT								    (BIT10) // E17
#define		CR_CPU_CKSEL_BIT									(BIT12) // 0:CPU CLK is SYS_CLK x2, 1:CPU CLK Same as SYS_CLK.
#define		PMU_BACKUP_RESTORE_PARITY_ERROR_SHIFT				(16)
#define		PMU_BACKUP_RESTORE_PARITY_ERROR_MASK				BIT_MASK(8)
#define		PMU_BACKUP_RESTORE_PARITY_ERROR						((U32)(PMU_BACKUP_RESTORE_PARITY_ERROR_MASK << PMU_BACKUP_RESTORE_PARITY_ERROR_SHIFT))

#define	R32_SYS0_PMU_FLASH_OFF_CTRL								((0x64) >> 2)
#define		CR_FLASH_OFFTIME_SHIFT								(0)
#define		CR_FLASH_OFFTIME_MASK								BIT_MASK(16)
#define		CR_FLASH_OFFTIME									((U32)(CR_FLASH_OFFTIME_MASK << CR_FLASH_OFFTIME_SHIFT))
//#define		CR_FLASHOFF_START_CNT_BIT							(BIT16) // E13
#define		FLASH_CNT_NON_READY_BIT								(BIT24)
#define		FLASH_OFF_MODE_SEL_BIT								(BIT25)

#define	R32_SYS0_PMU_DELAY_CTRL2								((0x68) >> 2)
#define		CR_PLL_PD2DIS_DELAY3_SHIFT							(0)
#define		CR_PLL_PD2DIS_DELAY3_MASK							BIT_MASK(6)
#define		CR_PLL_PD2DIS_DELAY3								((U32)(CR_PLL_PD2DIS_DELAY3_MASK << CR_PLL_PD2DIS_DELAY3_SHIFT))
#define		CR_PLL_PD2DIS_DELAY4_SHIFT							(8)
#define		CR_PLL_PD2DIS_DELAY4_MASK							BIT_MASK(6)
#define		CR_PLL_PD2DIS_DELAY4								((U32)(CR_PLL_PD2DIS_DELAY4_MASK << CR_PLL_PD2DIS_DELAY4_SHIFT))
#define		CR_PLL_PD2DIS_DELAY5_SHIFT							(16)
#define		CR_PLL_PD2DIS_DELAY5_MASK							(BIT_MASK(6))
#define		CR_PLL_PD2DIS_DELAY5								(CR_PLL_PD2DIS_DELAY5_MASK << CR_PLL_PD2DIS_DELAY5_SHIFT)

#define	R32_SYS0_PMU_SATA_ROSC_CAL								((0x6C) >> 2)
//#define		PMU_FLL_CMD_MODE_CALCULATE							(0x00) //E17 HW Remove, remove FLL  circuit
//#define     PMU_FLL_CMD_MODE_OSC_BIT							(BIT0) //E17 HW Remove, remove FLL  circuit
//#define     PMU_FLL_CMD_MODE_PHY_RC_BIT							(BIT1) //E17 HW Remove, remove FLL  circuit
//#define		PMU_FLL_CMD_SHIFT									(0) //E17 HW Remove, remove FLL  circuit
//#define		PMU_FLL_CMD_MASK									BIT_MASK(2) //E17 HW Remove, remove FLL  circuit
//#define		PMU_FLL_CMD											(PMU_FLL_CMD_MASK << PMU_FLL_CMD_SHIFT) //E17 HW Remove, remove FLL  circuit
//#define		PMU_FLL_S_P_BIT										(BIT8) //E17 HW Remove, remove FLL  circuit
//#define		SATA_FLL_CLK_STS_BIT								(BIT15) //E17 HW Remove, remove FLL  circuit
#define		CR_FORCE_REFCLK_NOGATE_BIT							(BIT24)
#define		CR_FORCE_LFOSC_ON_BIT							    (BIT25) //E17 HW change name from CR_FORCE_SATA_RC_NO_PD_BIT to CR_FORCE_LFOSC_ON_BIT
#define		CR_FORCE_CPHY_RC_ON_BIT							    (BIT26)

#define R32_SYS0_PMU_HOST_MODE_INFO								((0x70) >> 2)
#define		LEGACY_SD_PCIE_SD_OR_BIT                            (BIT0) // E17
/*// E13
#define		SR_HMODE_PCIE_EFUSE_MASK							(BIT_MASK(4))
#define		SR_HMODE_PCIE_EFUSE									(SR_HMODE_PCIE_EFUSE_MASK << SR_HMODE_PCIE_EFUSE_SHIFT)
#define		SR_HMODE_SATA_EFUSE_SHIFT							(4)
#define		SR_HMODE_SATA_EFUSE_MASK							(BIT_MASK(4))
#define		SR_HMODE_SATA_EFUSE									(SR_HMODE_SATA_EFUSE_MASK << SR_HMODE_SATA_EFUSE_SHIFT)
#define		SR_HMODE_UFS_EFUSE_SHIFT							(8)
#define		SR_HMODE_UFS_EFUSE_MASK								(BIT_MASK(4))
#define		SR_HMODE_UFS_EFUSE									(SR_HMODE_UFS_EFUSE_MASK << SR_HMODE_UFS_EFUSE_SHIFT)*/
#define		SR_HMODE_PCIE_SD_BIT								(BIT8)
#define		SR_HMODE_LEGACYE_SD_BIT								(BIT9)
#define		SR_HMODE_SD_BIT								        (BIT10)
#define		SR_HMODE_USB_BIT									(BIT11)
#define		SR_HMODE_PCIE_BIT									(BIT12)
#define		SR_HMODE_SATA_BIT									(BIT13)
#define		SR_HMODE_SD_NORMAL_BIT								(BIT14)
#define     SATA_REF_LOCK_BIT                                   (BIT16)
#define		SR_HMODE_0_EFUSE_SHIFT							    (27)
#define		SR_HMODE_0_EFUSE_MASK							    (BIT_MASK(2))
#define		SR_HMODE_0_EFUSE									(SR_HMODE_0_EFUSE_MASK << SR_HMODE_0_EFUSE_SHIFT)
#define		SR_HMODE_1_EFUSE_SHIFT							    (29)
#define		SR_HMODE_1_EFUSE_MASK							    (BIT_MASK(2))
#define		SR_HMODE_1_EFUSE									(SR_HMODE_1_EFUSE_MASK << SR_HMODE_1_EFUSE_SHIFT)

#define R32_SYS0_PMU_WU_EVENT_STS								((0x74) >> 2)
#define		GPIO_LEVEL_LOW										(BIT4)
#define     GPIO_WFP_GROUP										(BIT6)
#define     PMU_RTT_WAKE_P                                      (BIT8)
#define     PCIE_WAKE_EVENT_W                                   (BIT9)
#define     NFE_WAKE_EVENT_W                                    (BIT10)
#define     SR_SATA_WAKEUP_WRP                                  (BIT13)
#define     SR_DEVSLP_WFP                                       (BIT14)
#define     SR_DEVSLP_WRP                                       (BIT15)
#define		SR_WAKE_EVENT_SHIFT									(0)
#define		SR_WAKE_EVENT_MASK									(BIT_MASK(32))
#define		SR_WAKE_EVENT										(SR_WAKE_EVENT_MASK << SR_WAKE_EVENT_SHIFT)

#define	R32_SYS0_PMU_PHY_POWER_CTRL								((0x78) >> 2)
#define		CR_VDT_INITIAL_EN									(BIT0)
#define		CR_MASKROM_CHK_EN								    (BIT1)
#define		CR_SPI_ROM_AES										(BIT2)
#define		CR_FORCE_FIO_PMODE  								(BIT3)
//#define		CR_PRAM_SW_CRC_CHK_EN								(BIT3) //E13
#define		FORCE_FIO_PMODE_VLD  								(BIT4)
#define		SR_2CH_F              								(BIT5)
#define		CR_PMU_VDT_ON_DIS									(BIT6)
#define		CR_LVCC									            (BIT7)
#define		CR_PHYPWOK_EN_BIT									(BIT16)

#define	R32_SYS0_PMU_POWER_DROP_INFO							((0x7C) >> 2)
#define		PMU_ROM_CODE_VERSION_SHIFT							(0)
#define		PMU_ROM_CODE_VERSION_MASK							(BIT_MASK(8))
#define		PMU_ROM_CODE_VERSION								(PMU_ROM_CODE_VERSION_MASK << PMU_ROM_CODE_VERSION_SHIFT)
#define		CORE_VDT_CNT_SHIFT									(8)
#define		CORE_VDT_CNT_MASK									(BIT_MASK(4))
#define		CORE_VDT_CNT										(CORE_VDT_CNT_MASK << CORE_VDT_CNT_SHIFT)
#define		CORE_VDT_CNT_CLEAR_BIT								(BIT15)
#define		FLH_VDT_CNT_SHIFT									(16)
#define		FLH_VDT_CNT_MASK									(BIT_MASK(4))
#define		FLH_VDT_CNT											(FLH_VDT_CNT_MASK << FLH_VDT_CNT_SHIFT)
#define		FLASH_VDT_CNT_CLEAR_BIT								(BIT23)
//#define		AESKEY_DONT_BACKUP_BIT								(BIT24) // E13
#define		PMU_HW_VERSION_SHIFT								(28)
#define		PMU_HW_VERSION_MASK									(BIT_MASK(4))
#define		PMU_HW_VERSION										(PMU_HW_VERSION_MASK << PMU_HW_VERSION_SHIFT)

#define R32_SYS0_PMU_RSTN0_CTL									((0x80) >> 2)
#define		CR_PCIE_PERSTN_BIT									(BIT0)
#define		CR_PCIE_RESETN_BIT									(BIT1)
#define		CR_PERSTN_DBC_SEL_SHIFT								(8)
#define		CR_PERSTN_DBC_SEL_MASK								(BIT_MASK(2))
#define		CR_PERSTN_DBC_SEL									(CR_PERSTN_DBC_SEL_MASK << CR_PERSTN_DBC_SEL_SHIFT)
#define		CR_PERSTN_DBC_SEL_2CYCLES							(0)
#define		CR_PERSTN_DBC_SEL_64CYCLES							(1)
#define		CR_PERSTN_DBC_SEL_768CYCLES							(2)
//#define 	CR_PERSTN_DBC_SEL_768CYCLES 						(3)
#define		CR_PCIE_RST_MODE_SHIFT								(10)
#define		CR_PCIE_RST_MODE_MASK								(BIT_MASK(2))
#define		CR_PCIE_RST_MODE									(CR_PCIE_RST_MODE_MASK << CR_PCIE_RST_MODE_SHIFT)
#define		CR_PCIE_RST_MODE_FW_AND_EXTERNAL_CONTROL			(0x00)
#define		CR_PCIE_RST_MODE_FW_CONTROL							(0x01) //E17 add, E13: CR_PCIE_RST_MODE_BIT
#define		CR_PCIE_RST_MODE_EXTERNAL_CONTROL				    (0x03) //E17 Add CR_PCIE_RST_MODE_EXTERNAL_CONTROL,
#define		SR_PCIE_PERSTN_BIT									(BIT12)
#define		SR_PCIE_RESETN_BIT									(BIT13)
#define		PERSTN_DBC_BIT										(BIT14)
#define		CR_CLKREQB_DET_EN_BIT								(BIT16)
#define		PMU_CR_DEVSLP_DET_EN_BIT							(BIT17) // conflict with gpio R32_SYS1_GPIO[R32_SYS1_SYS_EDGE_DET_EN] CR_DEVSLP_DET_EN_BIT
#define		CR_PEREST_DET_EN_BIT								(BIT18)
//#define		CR_UNI_HIB8EXT_DET_EN_BIT							(BIT19) // E13
#define		CR_PD51_WAKE_DET_EN_BIT								(BIT20)
//#define		CR_MPHY_RSTB_DET_EN_BIT								(BIT21) // E13
#define		CR_CPHY_RESETN										(BIT24)
#define		CR_USB_RSTN_MODE									(BIT25)
#define		SR_CPHY_RESETN										(BIT26)

#define     M_PMU_CHECK_PERSTN_DEBOUNCE()                       (r32_SYS_PMU[R32_SYS0_PMU_RSTN0_CTL] & PERSTN_DBC_BIT)

#define R32_SYS0_DBUF_SD_CONTROL								((0x84) >> 2)
#define		CR_OP_PD0_DBF_RAM_SD_SHIFT							(0)
#define		CR_OP_PD0_DBF_RAM_SD_MASK							(BIT_MASK(2))
#define		CR_OP_PD0_DBF_RAM_SD								(CR_OP_PD0_DBF_RAM_SD_MASK << CR_OP_PD0_DBF_RAM_SD_SHIFT)
#define         CR_OP_PD0_DBUF_RAM_PD_64KB_0                    (BIT0) //E17
#define         CR_OP_PD0_DBUF_RAM_PD_64KB_1                    (BIT1) //E17 
#define         CR_OP_PD0_DBUF_RAM_PD_128KB                     (BIT0|BIT1) //E17 
#define		CR_PD0_DBF_RAM_SD_SHIFT								(8)
#define		CR_PD0_DBF_RAM_SD_MASK								(BIT_MASK(2))
#define		CR_PD0_DBF_RAM_SD									(CR_PD0_DBF_RAM_SD_MASK << CR_PD0_DBF_RAM_SD_SHIFT)

#define R32_SYS0_LOWK_CTRL									    ((0x88) >> 2)
#define		LOWK_FIO_SHIFT										(0)
#define		LOWK_FIO_MASK										(BIT_MASK(3))
#define		LOWK_FIO											(LOWK_FIO_MASK << LOWK_FIO_SHIFT)
#define		LOWK_VPHY_SHIFT										(8)
#define		LOWK_VPHY_MASK										(BIT_MASK(3))
#define		LOWK_VPHY											(LOWK_VPHY_MASK << LOWK_VPHY_SHIFT)
#define 	LOWK_CORE_SHIFT										(16)
#define 	LOWK_CORE_MASK										(BIT_MASK(3))
#define 	LOWK_CORE											(LOWK_CORE_MASK << LOWK_CORE_SHIFT)

#define R32_SYS0_PMU_ATCM_BACKUP_SADDR							((0x8C) >> 2)
#define ATCM_BACKUP_SOURCE_ADDRESS_SHIFT						(0)
#define ATCM_BACKUP_SOURCE_ADDRESS_MASK							BIT_MASK(32)
#define ATCM_BACKUP_SOURCE_ADDRESS								(ATCM_BACKUP_SOURCE_ADDRESS_MASK << ATCM_BACKUP_SOURCE_ADDRESS_SHIFT)

#define R32_SYS0_PMU_ATCM_BACKUP_DADDR							((0x90) >> 2)
#define ATCM_BACKUP_DESTINATION_ADDRESS_SHIFT					(0)
#define ATCM_BACKUP_DESTINATION_ADDRESS_MASK					BIT_MASK(32)
#define ATCM_BACKUP_DESTINATION_ADDRESS							(ATCM_BACKUP_DESTINATION_ADDRESS_MASK << ATCM_BACKUP_DESTINATION_ADDRESS_SHIFT)

#define R32_SYS0_PMU_BTCM_BACKUP_SADDR							((0x94) >> 2)
#define BTCM_BACKUP_SOURCE_ADDRESS_SHIFT						(0)
#define BTCM_BACKUP_SOURCE_ADDRESS_MASK							BIT_MASK(32)
#define BTCM_BACKUP_SOURCE_ADDRESS								(BTCM_BACKUP_SOURCE_ADDRESS_MASK << BTCM_BACKUP_SOURCE_ADDRESS_SHIFT)

#define R32_SYS0_PMU_BTCM_BACKUP_DADDR							((0x98) >> 2)
#define BTCM_BACKUP_DESTINATION_ADDRESS_SHIFT					(0)
#define BTCM_BACKUP_DESTINATION_ADDRESS_MASK					BIT_MASK(32)
#define BTCM_BACKUP_DESTINATION_ADDRESS							(BTCM_BACKUP_DESTINATION_ADDRESS_MASK << BTCM_BACKUP_DESTINATION_ADDRESS_SHIFT)

#define R32_SYS0_PMU_SEC_BACKUP_SADDR							((0x9C) >> 2)
#define SEC_BACKUP_SOURCE_ADDRESS_SHIFT							(0)
#define SEC_BACKUP_SOURCE_ADDRESS_MASK							BIT_MASK(32)
#define SEC_BACKUP_SOURCE_ADDRESS								(SEC_BACKUP_SOURCE_ADDRESS_MASK << SEC_BACKUP_SOURCE_ADDRESS_SHIFT)

#define R32_SYS0_PMU_SEC_BACKUP_DADDR							((0xA0) >> 2)
#define SEC_BACKUP_DESTINATION_ADDRESS_SHIFT					(0)
#define SEC_BACKUP_DESTINATION_ADDRESS_MASK						BIT_MASK(32)
#define SEC_BACKUP_DESTINATION_ADDRESS							(SEC_BACKUP_DESTINATION_ADDRESS_MASK << SEC_BACKUP_DESTINATION_ADDRESS_SHIFT)

#define R32_SYS0_PMU_BACKUP_LENGTH0								((0xA4) >> 2)
#define		ATCM_BACKUP_LENGTH_SHIFT							(0)
#define		ATCM_BACKUP_LENGTH_MASK								(BIT_MASK(16))
#define		ATCM_BACKUP_LENGTH									(ATCM_BACKUP_LENGTH_MASK << ATCM_BACKUP_LENGTH_SHIFT)
#define		BTCM_BACKUP_LENGTH_SHIFT							(16)
#define		BTCM_BACKUP_LENGTH_MASK								(BIT_MASK(16))
#define		BTCM_BACKUP_LENGTH									(BTCM_BACKUP_LENGTH_MASK << BTCM_BACKUP_LENGTH_SHIFT)

#define R32_SYS0_PMU_BACKUP_LENGTH1								((0xA8) >> 2)
#define		SEC_BACKUP_LENGTH_SHIFT								(0)
#define		SEC_BACKUP_LENGTH_MASK								(BIT_MASK(16))
#define		SEC_BACKUP_LENGTH									(SEC_BACKUP_LENGTH_MASK << SEC_BACKUP_LENGTH_SHIFT)
#define		AES_BK_EN_BIT										(BIT16)
#define		BTCM_BK_EN_BIT										(BIT17)
#define		ATCM_BK_EN_BIT										(BIT18)
#define		CR_OP_ROM_WU_BIT									(BIT19)
#define		ATCM_RESTORE_SEL_BIT								(BIT20)
#define		ATCM_RESTORE_EN_BIT									(BIT21)
#define		CR_OP_CKEN_BIT									    (BIT22)

#define R32_SYS0_PMU_COP_LV_CMD_ADR								((0xAC) >> 2)
#define		CR_ADR_NV_SHIFT										(0)
#define		CR_ADR_NV_MASK										(BIT_MASK(16))
#define		CR_ADR_NV											(CR_ADR_NV_MASK << CR_ADR_NV_SHIFT)
#define		CR_ADR_LV_SHIFT										(16)
#define		CR_ADR_LV_MASK										(BIT_MASK(16))
#define		CR_ADR_LV											(CR_ADR_LV_MASK << CR_ADR_LV_SHIFT)

#define R32_SYS0_PMU_GPIO_WU_SEL								((0xB0) >> 2)
#define		CR_GPIO_DET_EN_BIT									(BIT8)
#define		CR_GPIO_DET_EN1_BIT									(BIT9)
#define		CR_GPIO_DET_EN2_BIT									(BIT10)
#define		CR_GPIO_DET_EN3_BIT									(BIT11)
#define		CR_GPIO_DBC_SEL1_SHIFT								(12)
#define		CR_GPIO_DBC_SEL1_MASK								(BIT_MASK(2))
#define		CR_GPIO_DBC_SEL1									(CR_GPIO_DBC_SEL1_MASK << CR_GPIO_DBC_SEL1_SHIFT)
#define			CR_GPIO_DBC_SEL1_2us							(0)
#define			CR_GPIO_DBC_SEL1_64us							(1)
#define		CR_GPIO_DBC_SEL_SHIFT								(14)
#define		CR_GPIO_DBC_SEL_MASK								(BIT_MASK(2))
#define		CR_GPIO_DBC_SEL										(CR_GPIO_DBC_SEL1_MASK << CR_GPIO_DBC_SEL1_SHIFT)
#define			CR_GPIO_DBC_SEL_2us								(0)
#define			CR_GPIO_DBC_SEL_64us							(1)
#define		CR_GPIO_DET_SEL_SHIFT								(16)
#define		CR_GPIO_DET_SEL_MASK								(BIT_MASK(4))
#define		CR_GPIO_DET_SEL										(CR_GPIO_DET_SEL_MASK << CR_GPIO_DET_SEL_SHIFT)
#define		CR_GPIO_DET_SEL1_SHIFT								(20)
#define		CR_GPIO_DET_SEL1_MASK								(BIT_MASK(4))
#define		CR_GPIO_DET_SEL1									(CR_GPIO_DET_SEL1_MASK << CR_GPIO_DET_SEL1_SHIFT)
#define		CR_GPIO_DET_SEL2_SHIFT								(24)
#define		CR_GPIO_DET_SEL2_MASK								(BIT_MASK(4))
#define		CR_GPIO_DET_SEL2									(CR_GPIO_DET_SEL2_MASK << CR_GPIO_DET_SEL2_SHIFT)
#define		CR_GPIO_DET_SEL3_SHIFT								(28)
#define		CR_GPIO_DET_SEL3_MASK								(BIT_MASK(4))
#define		CR_GPIO_DET_SEL3									(CR_GPIO_DET_SEL3_MASK << CR_GPIO_DET_SEL3_SHIFT)

#define R32_SYS0_PMU_SPI_MODE_CRC_STS							((0xB4) >> 2)
#define		CRC_CHECK_ERR_BIT									(BIT0)

#define R32_SYS0_PMU_CPU_ROM_MISR_OUT_0_31						((0xB8) >> 2)

#define R32_SYS0_PMU_CPU_ROM_MISR_OUT_32_63						((0xBC) >> 2)

#define R32_SYS0_PMU_MODE_CTL   								((0xC0) >> 2)
#define		PMU_CPURBIST_EN_BIT									(BIT0)
#define		PMU_CPURBIST_DONE_BIT								(BIT1)
#define		PMU_CPURBIST_FAIL_BIT								(BIT2)
#define		CR_REG_TRIM_VLD_BIT									(BIT8)
#define		CR_REG_DLL_FG_BIT									(BIT9)
#define		SLFCHK_RESTART_NUM_SHIFT							(16)
#define		SLFCHK_RESTART_NUM_MASK								(BIT_MASK(2))
#define		SLFCHK_RESTART_NUM									(SLFCHK_RESTART_NUM_MASK << SLFCHK_RESTART_NUM_SHIFT)
#define		MD_DBG_MODE_EN_BIT									(BIT24)
#define		MD_GIO_MODE_EN_BIT									(BIT25)

#define R32_SYS0_PMU_BACKUP_TADDR								((0xC4) >> 2)
#define		ATCM_BACKUP_TARGET_ADDRESS_SHIFT					(0)
#define		ATCM_BACKUP_TARGET_ADDRESS_MASK						(BIT_MASK(32))
#define		ATCM_BACKUP_TARGET_ADDRESS							(ATCM_BACKUP_TARGET_ADDRESS_MASK << ATCM_BACKUP_TARGET_ADDRESS_SHIFT)

#define R32_SYS0_PMU_IP_CKEN									((0xC8) >> 2)
#define		CR_IP_CKEN_SHIFT								    (0)
#define		CR_IP_CKEN_MASK								        (BIT_MASK(32))
#define		CR_IP_CKEN										    (CR_IP_CKEN_MASK << CR_IP_CKEN_SHIFT)

/*
 *  +-----------------------------------------------------------------------+
 *  |					RRAM      									        |
 *  +-----------------------------------------------------------------------+
 */
#define	SYS0_RRAM_OFFSET							(0x00000600) //0x0600~0x06FF
#define	SYS0_RRAM_BASE								(SYSTEM_PD0_REG_ADDRESS + SYS0_RRAM_OFFSET)

#define	R8_SYS0_RRAM								((REG8  *) SYS0_RRAM_BASE)
#define	R16_SYS0_RRAM								((REG16 *) SYS0_RRAM_BASE)
#define	R32_SYS0_RRAM								((REG32 *) SYS0_RRAM_BASE)

/*
 *  +-----------------------------------------------------------------------+
 *  |					RTT      								            |
 *  +-----------------------------------------------------------------------+
 */
#define	SYS0_RTT_OFFSET								(0x00000700)  //0x0700~0x07FF
#define	SYS0_RTT_CTRL_BASE							(SYSTEM_PD0_REG_ADDRESS + SYS0_RTT_OFFSET)

#define	R8_SYS0_RTT									((REG8  *) SYS0_RTT_CTRL_BASE)
#define	R16_SYS0_RTT								((REG16 *) SYS0_RTT_CTRL_BASE)
#define	R32_SYS0_RTT								((REG32 *) SYS0_RTT_CTRL_BASE)
#define	R64_SYS0_RTT								((REG64 *) SYS0_RTT_CTRL_BASE)

#define	R32_SYS0_RTT_TO_CTRL						((0x00) >> 2)                                         //Default: 0x00000000
#define		CR_RTT_BASE_UPDATE_BIT					(BIT5)
#define		CR_RTT_LATCH_BIT						(BIT6)
#define		CR_RTT_TO_VLD_BIT						(BIT15)//E17: BIT15, E13: (BIT7)
#define		CR_RTT_EN_BIT							(BIT31)

#define	R64_SYS0_RTT_TO_OFS							((0x08) >> 3)                                         //Default: 0x00000000
#define	R32_SYS0_RTT_TO_OFSL						((0x08) >> 2)                                         //Default: 0x00000000
#define	R32_SYS0_RTT_TO_OFSH						((0x0C) >> 2)                                         //Default: 0x00000000

#define	R64_SYS0_RTT_TO_LMT							((0x10) >> 3)
#define	R32_SYS0_RTT_TO_LMTL						((0x10) >> 2)                                         //Default: 0xFFFFFFFF
#define	R32_SYS0_RTT_TO_LMTH						((0x14) >> 2)                                         //Default: 0xFFFFFFFF

#define	R64_SYS0_RTT_TMR							((0x20) >> 3)
#define	R32_SYS0_RTT_TMRL							((0x20) >> 2)                                         //Default: 0x00000000
#define	R32_SYS0_RTT_TMRH							((0x24) >> 2)                                         //Default: 0x00000000

#define	R64_SYS0_RTT_BASE							((0x30) >> 3)
#define	R32_SYS0_RTT_BASEL							((0x30) >> 2)                                         //Default: 0x00000000
#define	R32_SYS0_RTT_BASEH							((0x34) >> 2)                                         //Default: 0x00000000

#define	R64_SYS0_RTT_LATCH							((0x40) >> 3)
#define	R32_SYS0_RTT_LATCHL							((0x40) >> 2)                                         //Default: 0x00000000
#define	R32_SYS0_RTT_LATCHH							((0x44) >> 2)                                         //Default: 0x00000000

/*
 *  +-----------------------------------------------------------------------+
 *  |					SPHY      								            |
 *  +-----------------------------------------------------------------------+
 */
#define	SYS0_SPHY_OFFSET							(0x00000800) //0x0800~0x08FF
#define	SYS0_SPHY_BASE								(SYSTEM_PD0_REG_ADDRESS + SYS0_SPHY_OFFSET)

#define	R8_SYS0_SPHY								((REG8  *) SYS0_SPHY_BASE)
#define	R16_SYS0_SPHY								((REG16 *) SYS0_SPHY_BASE)
#define	R32_SYS0_SPHY								((REG32 *) SYS0_SPHY_BASE)
#define	R64_SYS0_SPHY								((REG64 *) SYS0_SPHY_BASE)

#define	R32_SYS0_SATA_PHY_MISC4						(0x5C >> 2)
#define		SPHY_MISC4_SHIFT						(0)
#define		SPHY_MISC4_MASK							(BIT_MASK(16))

#define	R32_SYS0_SATA_PHY_PI_PPM					(0x60 >> 2)
#define		NX_PI_PPM_OUT_SHIFT						(0)
#define		NX_PI_PPM_OUT_MASK						(BIT_MASK(16))
#define		NX_RX_DETECT_SSC_BIT					(BIT30)
#define		NX_PI_PPM_DONE_BIT						(BIT31)

#define R8_SYS0_SATA_PHY_COMRESET                   (0x64 >> 0)
#define	R32_SYS0_SATA_PHY_COMRESET					(0x64 >> 2)
#define		NX_DIV_INIT_COMRESET_SHIFT				(0)
#define		NX_DIV_INIT_COMRESET_MASK				(BIT_MASK(8))
#define		NX_DIV_COMRESET_DONE_BIT				(BIT31)

#define	R32_SYS0_SATA_PHY_D10P2						(0x68 >> 2)
#define		NX_PI_PPM_D10P2_SHIFT					(0)
#define		NX_PI_PPM_D10P2_MASK					(BIT_MASK(16))
#define		NX_DIV_INIT_D10P2_SHIFT					(16)
#define		NX_DIV_INIT_D10P2_MASK					(BIT_MASK(8))
#define		NX_DIV_D10P2_DONE_BIT					(BIT31)
#define R8_SYS0_NX_PI_PPM1_D10P2					(0x68)
#define R8_SYS0_NX_PI_PPM2_D10P2					(0x69)
#define R8_SYS0_NX_DIV_INIT_D10P2					(0x6A)

#define	R32_SYS0_SATA_PHY_CTRL						(0x6C >> 2)
#define		CR_SATA_GEN_SHIFT						(0)
#define		CR_SATA_GEN_MASK						(BIT_MASK(2))
#define		CR_SATA_GEN_SEL_BIT						(BIT4)
#define		CR_SPHY_SATA_INIT_DONE_BIT				(BIT8)
#define		CR_SPHY_TRIM_VLD_BIT					(BIT24)
#define		PRE_SATA_ISO_BIT						(BIT31)

#define	R32_SYS0_SATA_PHY_TRIM_DW1					(0x70 >> 2)

#define	R32_SYS0_SATA_PHY_TRIM_DW2					(0x74 >> 2)

#define	R32_SYS0_SATA_PHY_PMA_GEN1_DW1				(0x78 >> 2)

#define	R32_SYS0_SATA_PHY_PMA_GEN1_DW2				(0x7C >> 2)
#define		CR_SATA_PMA_GEN1_DW2_B31_28_SHIFT		(28)
#define		CR_SATA_PMA_GEN1_DW2_B31_28_MASK		BIT_MASK(4)

#define	R32_SYS0_SATA_PHY_PMA_GEN1_DW3				(0x80 >> 2)
#define		CR_SATA_PMA_GEN1_DW3_B3_0_SHIFT			(0)
#define		CR_SATA_PMA_GEN1_DW3_B3_0_MASK			BIT_MASK(4)

#define	R32_SYS0_SATA_PHY_PMA_GEN1_DW4				(0x84 >> 2)

#define	R32_SYS0_SATA_PHY_PMA_GEN1_DW5				(0x88 >> 2)

#define	R32_SYS0_SATA_PHY_PMA_GEN1_DW6				(0x8C >> 2)
#define		CR_SATA_PMA_GEN1_DW6_B3_0_SHIFT			(0)
#define		CR_SATA_PMA_GEN1_DW6_B3_0_MASK			BIT_MASK(4)

#define	R32_SYS0_SATA_PHY_PMA_GEN2_DW1				(0x90 >> 2)

#define	R32_SYS0_SATA_PHY_PMA_GEN2_DW2				(0x94 >> 2)
#define		CR_SATA_PMA_GEN2_DW2_B31_28_SHIFT		(28)
#define		CR_SATA_PMA_GEN2_DW2_B31_28_MASK		BIT_MASK(4)

#define	R32_SYS0_SATA_PHY_PMA_GEN2_DW3				(0x98 >> 2)
#define		CR_SATA_PMA_GEN2_DW3_B3_0_SHIFT			(0)
#define		CR_SATA_PMA_GEN2_DW3_B3_0_MASK			BIT_MASK(4)

#define	R32_SYS0_SATA_PHY_PMA_GEN2_DW4				(0x9C >> 2)

#define	R32_SYS0_SATA_PHY_PMA_GEN2_DW5				(0xA0 >> 2)

#define	R32_SYS0_SATA_PHY_PMA_GEN2_DW6				(0xA4 >> 2)
#define		CR_SATA_PMA_GEN2_DW6_B3_0_SHIFT			(0)
#define		CR_SATA_PMA_GEN2_DW6_B3_0_MASK			BIT_MASK(4)

#define	R32_SYS0_SATA_PHY_PMA_GEN3_DW1				(0xA8 >> 2)

#define	R32_SYS0_SATA_PHY_PMA_GEN3_DW2				(0xAC >> 2)
#define		CR_SATA_PMA_GEN3_DW2_B31_28_SHIFT		(28)
#define		CR_SATA_PMA_GEN3_DW2_B31_28_MASK		BIT_MASK(4)

#define	R32_SYS0_SATA_PHY_PMA_GEN3_DW3				(0xB0 >> 2)
#define		CR_SATA_PMA_GEN3_DW3_B3_0_SHIFT			(0)
#define		CR_SATA_PMA_GEN3_DW3_B3_0_MASK			BIT_MASK(4)

#define	R32_SYS0_SATA_PHY_PMA_GEN3_DW4				(0xB4 >> 2)

#define	R32_SYS0_SATA_PHY_PMA_GEN3_DW5				(0xB8 >> 2)

#define	R32_SYS0_SATA_PHY_PMA_GEN3_DW6				(0xBC >> 2)
#define		CR_SATA_PMA_GEN3_DW6_B3_0_SHIFT			(0)
#define		CR_SATA_PMA_GEN3_DW6_B3_0_MASK			BIT_MASK(4)

#define	R32_SYS0_SATA_PHY_PCS_DW1					(0xC0 >> 2)
#define R16_SYS0_SATA_PHY_PCS_DW1_L                 (0xC0 >> 1)
#define		CR_SATA_PHY_PCS_DW1_B11_0_SHIFT			(0)
#define		CR_SATA_PHY_PCS_DW1_B11_0_MASK			BIT_MASK(12)

#define	R32_SYS0_SATA_PHY_PCS_DW2					(0xC4 >> 2)

#define	R32_SYS0_SATA_PHY_PCS_DW3					(0xC8 >> 2)

#define	R32_SYS0_SATA_PHY_PCS_DW4					(0xCC >> 2)
#define		CR_SPHY_SATA_PARTIAL_BIT				(BIT8)
#define		CR_SPHY_SATA_SLUMBER_BIT				(BIT9)
#define		CR_SATA_PHY_PCS_DW4_B27_16_SHIFT		(16)
#define		CR_SATA_PHY_PCS_DW4_B27_16_MASK			BIT_MASK(12)

#define	R32_SYS0_SATA_PHY_MISC_DW1					(0xD0 >> 2)

#define	R32_SYS0_SATA_PHY_MISC_DW2					(0xD4 >> 2)

#define	R32_SYS0_SATA_PHY_MISC_DW3					(0xD8 >> 2)

#define	R32_SYS0_SATA_PHY_MISC2_DW1					(0xDC >> 2)

#define	R32_SYS0_SATA_PHY_MISC2_DW2					(0xE0 >> 2)

#define	R32_SYS0_SATA_PHY_MISC3_DW1					(0xE4 >> 2)
#define	R8_SYS0_SATA_PHY_MISC3_DW1_B15_8			(0xE5)
#define	R8_SYS0_SATA_PHY_MISC3_DW1_B23_16			(0xE6)
#define	R8_SYS0_SATA_PHY_MISC3_DW1_B31_24			(0xE7)

#define	R32_SYS0_SATA_PHY_NXTAL_GEN1_DW1			(0xE8 >> 2)

#define	R32_SYS0_SATA_PHY_NXTAL_GEN1_DW2			(0xEC >> 2)

#define	R32_SYS0_SATA_PHY_NXTAL_GEN2_DW1			(0xF0 >> 2)

#define	R32_SYS0_SATA_PHY_NXTAL_GEN2_DW2			(0xF4 >> 2)

#define	R32_SYS0_SATA_PHY_NXTAL_GEN3_DW1			(0xF8 >> 2)

/*
 *  +-----------------------------------------------------------------------+
 *  |					CPHY      									        |
 *  +-----------------------------------------------------------------------+
 */
#define	SYS0_CPHY_OFFSET							(0x00000800) //0x0800~0x08FF
#define	SYS0_CPHY_BASE								(SYSTEM_PD0_REG_ADDRESS + SYS0_CPHY_OFFSET)

#define	R8_SYS0_CPHY								((REG8  *) SYS0_CPHY_BASE)
#define	R16_SYS0_CPHY								((REG16 *) SYS0_CPHY_BASE)
#define	R32_SYS0_CPHY								((REG32 *) SYS0_CPHY_BASE)
#define	R64_SYS0_CPHY								((REG64 *) SYS0_CPHY_BASE)

#define R32_SYS0_CREG_DLY_CTRL						(0x00 >> 2)
#define     CR_CREG_DLY_CNT_SHIFT                   (0)
#define		CR_CREG_DLY_CNT_MASK					(BIT_MASK(12))
#define     CR_CREG_BUS_WIDTH_BIT	                (BIT16)
#define     CR_CREG_FLUSH_BIT		                (BIT24)
#define     CR_CREG_SM_RST_BIT		                (BIT25)

#define R32_SYS0_REFCK_CTRL							(0x04 >> 2)
#define     CR_SEL_REFCK			                (BIT0)

#define	R32_SYS0_CPHY_MISC4							(0x5C >> 2)
#define     SR_CPHY_AUTO_TRACK_DONE_BIT             (BIT0)
#define     SR_CPHY_AUTO_RXSSC_DET_BIT              (BIT1)
#define     SR_CPHY_REF_LOCK_S_BIT                  (BIT2)
#define		SR_CPHY_AUTO_PLL_DIV_SHIFT				(8)
#define		SR_CPHY_AUTO_PLL_DIV_MASK				(BIT_MASK(8))
#define		SR_CPHY_AUTO_PI_INIT_SHIFT				(16)
#define		SR_CPHY_AUTO_PI_INIT_MASK				(BIT_MASK(8))

#define	R32_SYS0_CPHY_PI_PPM						(0x60 >> 2)
#define		SR_CPHY_NX_PI_PPM_OUT_SHIFT				(0)
#define		SR_CPHY_NX_PI_PPM_OUT_MASK				(BIT_MASK(16))
#define		SR_CPHY_NX_RX_DETECT_SSC_BIT			(BIT30)
#define		SR_CPHY_NX_PI_PPM_DONE_BIT				(BIT31)

#define R8_SYS0_CPHY_COMRESET                   	(0x64 >> 0)
#define	R32_SYS0_CPHY_COMRESET						(0x64 >> 2)
#define		SR_CPHY_NX_DIV_INIT_COMRESET_SHIFT		(0)
#define		SR_CPHY_NX_DIV_INIT_COMRESET_MASK		(BIT_MASK(8))
#define		SR_CPHY_NX_DIV_COMRESET_DONE_BIT		(BIT31)

#define	R32_SYS0_CPHY_D10P2							(0x68 >> 2)
#define		SR_CPHY_NX_PI_PPM_D10P2_SHIFT			(0)
#define		SR_CPHY_NX_PI_PPM_D10P2_MASK			(BIT_MASK(16))
#define		SR_CPHY_NX_DIV_INIT_D10P2_SHIFT			(16)
#define		SR_CPHY_NX_DIV_INIT_D10P2_MASK			(BIT_MASK(8))
#define		SR_CPHY_NX_DIV_D10P2_DONE_BIT			(BIT31)
#define R8_SYS0_SR_CPHY_NX_PI_PPM1_D10P2			(0x68)
#define R8_SYS0_SR_CPHY_NX_PI_PPM2_D10P2			(0x69)
#define R8_SYS0_SR_CPHY_NX_DIV_INIT_D10P2			(0x6A)

#define	R32_SYS0_CPHY_CTRL0							(0x6C >> 2)
#define		CR_SATA_GEN_SHIFT						(0)
#define		CR_SATA_GEN_MASK						(BIT_MASK(2))
#define		CR_SATA_GEN_SEL_BIT						(BIT4)
#define		CR_CPHY_SATA_INIT_DONE_BIT				(BIT8)
#define		CR_CPHY_TRIM_VLD_BIT					(BIT24)
#define		PRE_SATA_ISO_BIT						(BIT31)

#define R32_SYS0_SYS_CPHY_CTRL1                     (0x70 >> 2)  //  70->74                           //Default: 0x01010000
#define     CR_CPHY_FARA							(BIT0)
#define     CR_CPHY_FARD							(BIT1)
#define     CR_CPHY_NEARA							(BIT2)
#define     CR_CPHY_NEARD							(BIT3)
#define     CR_CPHY_U2U3_RCOSC_SEL					(BIT16)
#define     CR_CPHY_SATA_REF_EN						(BIT24)
#define     CR_CPHY_SATA_SSC_ON						(BIT31)

#define	R32_SYS0_CPHY_TRIM0							(0x7C >> 2)
#define	R32_SYS0_CPHY_TRIM1							(0x80 >> 2)
#define	R32_SYS0_CPHY_TRIM2							(0x84 >> 2)
#define	R32_SYS0_CPHY_TRIM3							(0x88 >> 2)
#define	R32_SYS0_CPHY_TRIM4							(0x8C >> 2)
#define	R32_SYS0_CPHY_TRIM5							(0x90 >> 2)
#define		CR_CPHY_TRIM_SHIFT						(0)
#define		CR_CPHY_TRIM_MASK						(BIT_MASK(32))

#define	R32_SYS0_CPHY_RSV0							(0xF0 >> 2)
#define	R32_SYS0_CPHY_RSV1							(0xF4 >> 2)
#define	R32_SYS0_CPHY_RSV2							(0xF8 >> 2)
#define	R32_SYS0_CPHY_RSV3							(0xFC >> 2)
#define		CR_CPHY_RSV_SHIFT						(0)
#define		CR_CPHY_RSV_MASK						(BIT_MASK(32))

/*
 *  +-----------------------------------------------------------------------+
 *  |					MISCH      									        |
 *  +-----------------------------------------------------------------------+
 */
#define	SYS0_MISCH_OFFSET							(0x00001000) //0x1000~0x10FF
#define	SYS0_MISCH_CTRL_BASE						(SYSTEM_PD0_REG_ADDRESS + SYS0_MISCH_OFFSET)

#define	R8_SYS0_MISCH								((REG8   *) SYS0_MISCH_CTRL_BASE)
#define	R16_SYS0_MISCH								((REG16  *) SYS0_MISCH_CTRL_BASE)
#define	R32_SYS0_MISCH								((REG32  *) SYS0_MISCH_CTRL_BASE)
#define	R64_SYS0_MISCH								((REG64  *) SYS0_MISCH_CTRL_BASE)

#define	R32_SYS0_SYS_HBA_SYS_SRST					((0x00) >> 2)                                       //Default: 0x00000000
#define		HBA_SYS_PD1_SRST_BIT					(BIT0)
#define		HBA_SYS_PD51_SRST_BIT					(BIT1)
#define		HBA_SYS_PD50_SRST_BIT					(BIT2)
#define		CR_RST_N_AXI_BIT						(BIT3)//E17, old->SYS0_PMU_RST (offset 14h), BIT11
#define		CR_RST_N_AHB_BIT 						(BIT4)//E17, old->SYS0_PMU_RST (offset 14h), BIT20 

// FTLADD
#define	R32_SYS0_SYS_PCAE2E_CTRL					((0x04) >> 2)
#define		CR_SYS_PCAE2E_EN_BIT					(BIT0)
#define 	CR_SYS_PCAE2E_CFG_SHIFT 				(1)
#define 	CR_SYS_PCAE2E_CFG_MASK 					(BIT_MASK(2))
#define 	CR_SYS_PCAE2E_CFG 						(CR_SYS_PCAE2E_CFG_MASK << CR_SYS_PCAE2E_CFG_SHIFT)

#define R32_SYS0_SYS_DBUF_INIT						((0x08) >> 2)//E17, only 32K base
#define 	CR_DBUF_PD0_32K_INIT_START_BIT 			(BIT0)
#define 	CR_DBUF_PD0_32K_INIT_END_BIT			(BIT1)
#define 	CR_SD_TPRAM_INIT_START_BIT 				(BIT8)
#define 	CR_SD_TPRAM_INIT_END_BIT 				(BIT9)

#define R32_SYS0_SYS_GPIO_O_3_0						((0x10) >> 2)
#define 	CR_GPIO_O0_BIT							(BIT0)
#define 	CR_GPIO_O1_BIT							(BIT8)
#define 	CR_GPIO_O2_BIT							(BIT16)
#define 	CR_GPIO_O3_BIT							(BIT24)

#define R32_SYS0_SYS_GPIO_O_7_4						((0x14) >> 2)
#define 	CR_GPIO_O4_BIT							(BIT0)
#define 	CR_GPIO_O5_BIT							(BIT8)
#define 	CR_GPIO_O6_BIT							(BIT16)
#define 	CR_GPIO_O7_BIT							(BIT24)

#define R32_SYS0_SYS_GPIO_O_11_8					((0x18) >> 2)
#define 	CR_GPIO_O8_BIT							(BIT0)
#define 	CR_GPIO_O9_BIT							(BIT8)
#define 	CR_GPIO_O10_BIT							(BIT16)
#define 	CR_GPIO_O11_BIT							(BIT24)

#define R32_SYS0_SYS_GPIO_O_12						((0x1C) >> 2)
#define 	CR_GPIO_O12_BIT							(BIT0)
#define 	CR_TMS_O_BIT							(BIT8)
#define 	CR_TCK_O_BIT							(BIT16)
//#define	CR_MPHYRSTN_O_BIT						(BIT24)//E17, [31:17] reserved

#define R32_SYS0_SYS_GPIO_O_ALL						((0x20) >> 2)
#define 	CR_GPIO_O_SHIFT							(0)
#define 	CR_GPIO_O_MASK							(BIT_MASK(13))
#define 	CR_GPIO_O								(CR_GPIO_O_MASK << CR_GPIO_O_SHIFT)

#define R32_SYS0_SYS_GPIO_I_ALL 					((0x24) >> 2)
#define 	SYS_GPIO_I_ALL_XGPIO_I_SHIFT 			(0)
#define 	SYS_GPIO_I_ALL_XGPIO_I_MASK 			(BIT_MASK(13))
#define 	SYS_GPIO_I_ALL_XGPIO_I 					(SYS_GPIO_I_ALL_XGPIO_I_MASK << SYS_GPIO_I_ALL_XGPIO_I_SHIFT)
#define M_GET_GPIO_INPUT(x)							((R32_SYS0_MISCH[R32_SYS0_SYS_GPIO_I_ALL] & BITMSK(1,x)) >> x)

#define R32_SYS0_SYS_PAD_I							((0x28) >> 2) // Depends on padc_reg.h
#define 	SYS_PAD_I_XEXTRSTB_I_BIT				(BIT0)
#define 	SYS_PAD_I_XDEVSLP_I_BIT 				(BIT1)
#define 	SYS_PAD_I_XPERSTN_I_BIT 				(BIT2)
#define 	SYS_PAD_I_XCLKREQB_I_BIT 				(BIT3)
#define 	SYS_PAD_I_XLED_I_BIT 					(BIT4)
#define 	SYS_PAD_I_XTMS_I_BIT					(BIT5)
#define 	SYS_PAD_I_XTCK_I_BIT 					(BIT6)
//#define	SYS_PAD_I_XLVCC_I_BIT					(BIT7)//E17, [31:7] reserved

//#define R32_SYS0_SYS_SPI_USE						((0x30) >> 2)//E17, remove
//#define	CR_SPI_MASTER_USE_BIT					(BIT0)
//#define	CR_SPI_SLAVE_USE_BIT					(BIT1)

#define R32_SYS0_SYS_PD0_MISCH_CTRL 				((0x34) >> 2)
#define 	CR_DBUF_PD0_GAT_EN_BIT					(BIT0)

//#define R32_SYS0_SYS_IP_CLKEN_SEL 						((0x38) >> 2)//E17, remove
//#define		CR_BMU_SYS_F_PD1_RST_N_CLK_EN_SEL_BIT		(BIT0)
//#define		CR_ZIP_LZSS_F_PD1_RST_N_CLK_EN_SEL_BIT		(BIT1)
//#define		CR_ZIP_SYS_F_PD1_RST_N_CLK_EN_SEL_BIT		(BIT2)
//#define		CR_DMAC_SYS_F_PD1_RST_N_CLK_EN_SEL_BIT		(BIT3)
//#define		CR_COP1_SYS_F_PD1_RST_N_CLK_EN_SEL_BIT		(BIT4)
//#define		CR_SEC_SYS_F_PD1_RST_N_CLK_EN_SEL_BIT		(BIT5)
//#define		CR_SEC_PKE_F_PD1_RST_N_CLK_EN_SEL_BIT		(BIT6)
//#define		CR_SEC_SHA_F_PD1_RST_N_CLK_EN_SEL_BIT		(BIT7)
//#define		CR_SEC_SM4_F_PD1_RST_N_CLK_EN_SEL_BIT		(BIT8)
//#define		CR_SEC_AES_F_PD1_RST_N_CLK_EN_SEL_BIT		(BIT9)
//#define		CR_CPU_SYS_F_PD1_RST_N_CLK_EN_SEL_BIT		(BIT10)
//#define		CR_CPU_CORE_F_PD1_RST_N_CLK_EN_SEL_BIT		(BIT11)
//#define		CR_CPU_TRACE_F_PD1_RST_N_CLK_EN_SEL_BIT 	(BIT12)
//#define		CR_FLH_CTRL_F_PD1_RST_N_CLK_EN_SEL_SHIFT	(16)
//#define		CR_FLH_CTRL_F_PD1_RST_N_CLK_EN_SEL_MASK 	(BIT_MASK(4))
//#define		CR_FLH_CTRL_F_PD1_RST_N_CLK_EN_SEL			(CR_FLH_CTRL_F_PD1_RST_N_CLK_EN_SEL_MASK << CR_FLH_CTRL_F_PD1_RST_N_CLK_EN_SEL_SHIFT)
//#define		CR_FLH_ECC_F_PD1_RST_N_CLK_EN_SEL_BIT		(BIT20)
//#define		CR_FLH_SYS_F_PD1_RST_N_CLK_EN_SEL_BIT		(BIT21)
//#define		CR_FLH_MDLL_F_PD1_RST_N_CLK_EN_SEL_SHIFT	(22)
//#define		CR_FLH_MDLL_F_PD1_RST_N_CLK_EN_SEL_MASK 	(BIT_MASK(2))
//#define		CR_FLH_MDLL_F_PD1_RST_N_CLK_EN_SEL			(CR_FLH_MDLL_F_PD1_RST_N_CLK_EN_SEL_MASK << CR_FLH_MDLL_F_PD1_RST_N_CLK_EN_SEL_SHIFT)
//#define		CR_FLH_CORE_F_PD1_RST_N_CLK_EN_SEL			(BIT24)
//#define		CR_COP0_SYS_F_PD1_RST_N_CLK_EN_SEL			(BIT25)

#endif  /* _SYS_PD0_REG_5017_H_ */
