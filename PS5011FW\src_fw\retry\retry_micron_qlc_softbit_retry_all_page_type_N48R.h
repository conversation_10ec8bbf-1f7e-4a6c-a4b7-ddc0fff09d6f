#ifndef _RETRY_MICRON_QLC_SOFTBIT_RETRY_ALL_PAGE_TYPE_N48_H_
#define _RETRY_MICRON_QLC_SOFTBIT_RETRY_ALL_PAGE_TYPE_N48_H_


#include "setup.h"
#include "hal/fip/fip_api.h"
#include "common/fw_common.h"

#if (((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC) && (TRUE == IM_N48R)) || (((CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC)) && ((TRUE == IM_V6)||(TRUE == IM_V7)||(TRUE == IM_V8)||(TRUE == IM_V5))) || ((CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_TLC) && ((TRUE == IM_BICS5)||(TRUE == IM_BICS6)||(TRUE == IM_BICS8))) || ((CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_QLC) && (TRUE == IM_BICS6_QLC)))//Duson Porting BICS5 Add//Reip Porting 3D-V7 QLC Add//Jeffrey Porting 3D V8 TLC Add//zerio BICS8 Add//zerio bics6 qlc add
//Dylan for V6 RDT porting,for Error: L6218E: Undefined symbol gSBTask (referred from read_retry.o),Tentative modification
#define SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX	(4)
#define SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH		(4)
#define SB_MICRON_SET_FEATURE_FPU_DATA_P1			(SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX)
#define SB_MICRON_SET_FEATURE_FPU_DATA_P2			(SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (1 << 1))
#define SB_MICRON_SET_FEATURE_FPU_DATA_P3			(SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (2 << 1))
#define SB_MICRON_SET_FEATURE_FPU_DATA_P4			(SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (3 << 1))
#define SB_MICRON_SET_FEATURE_FPU_END_IDX			(SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH << 1))

#define SB_MICRON_CHECK_FEATURE_FPU_START_IDX		(2)
#define SB_MICRON_GET_FEATURE_FPU_END_IDX			(3)

#define RETRY_SB_MT_FAIL_RETRY_CNT_MAX            (0xF)

#define NORMAL_MODE    (0)
#define INVERSE_MODE   (1)

#define SB_MICRON_SPECIFIC_NOT_USING					(0)
#define SB_MICRON_SPECIFIC_RETRY_TABLE				(1)
#define SB_MICRON_SPECIFIC_READ_OFFSET				(2)

#define SWITCH_CLK__NORMAL_MODE            (0)
#define SWITCH_CLK__LOW_CLK_MODE         (1)
#define SB_ARC_MAX_ROUND_CNT				(4)


#define SB_MICRON_FEATURE_PARAM_P1_INDEX			(0)
#define SB_MICRON_FEATURE_PARAM_P2_INDEX			(1)
#define SB_MICRON_FEATURE_PARAM_P3_INDEX			(2)
#define SB_MICRON_FEATURE_PARAM_P4_INDEX			(3)
#define SB_MICRON_FEATURE_PARAM_ARC_RESULT			(SB_MICRON_FEATURE_PARAM_P3_INDEX)

#define RETRY_SB_BACKUP_RESTORE_TO_DRAMIRAM       (1)
#define RETRY_SB_BACKUP_RESTORE_TO_ONLYDRAM       (0)
#define RETRY_SB_BACKUP_RESTORE_TO_ONLYDRAM_AND_KEEP       (2)
#define RETRY_SB_BACKUP_RESTORE_FROM_ONLYDRAM       (3)
#define RETRY_SB_BACKUP_RESTORE_TO_DRAMIRAM_AND_KEEP       (4)

#define RETRY_SB_ENHANCE_CORRECTIVE_READ_THRESHOLD	(0x4D)
#define RETRY_SB_NOT_CORRECTIVE_READ				(0)
#define RETRY_SB_2BIT_CORRECTIVE_READ				(1)
#define RETRY_SB_4BIT_CORRECTIVE_READ				(3)

#define EH_DEBUG_UART_EN 	(FALSE)

#if (TRUE == RETRY_MICRON_NICKS_ALL_PAGE_TYPE_SB_SUPPORT)	//Phison Flow temp use the same SB read flow with Micron Nicks
#define RETRY_SB_MICRON_ERROR_RECOVERY_READ_RETRY_ARC_STEP	(11)
#define RETRY_SB_MICRON_ERROR_RECOVERY_READ_RETRY_ARC_STEP_NUM	(4)	//Should sync with HBIT_RETRY_MICRON_ARC_STEP_NUM
#define RETRY_SB_MICRON_ERROR_RECOVERY_READ_RETRY_ARC_SB_STEP	(RETRY_SB_MICRON_ERROR_RECOVERY_READ_RETRY_ARC_STEP + RETRY_SB_MICRON_ERROR_RECOVERY_READ_RETRY_ARC_STEP_NUM)
#define RETRY_SB_MICRON_ERROR_RECOVERY_BLK_REFRESH_STEP	(12)
#endif	/*(TRUE == RETRY_MICRON_NICKS_ALL_PAGE_TYPE_SB_SUPPORT)*/

#define RETRY_SB_MICRON_READ_VOLTAGE_OFFSETS	(19)
#define RETRY_SB_MICRON_PREFIX_READ_RETRY_NUM	(8 + 1)		// RR0 + RR1 ~ RR8

#define RETRY_SB_FLOW_HB_MICRON_MIN_SW_NUM	(3)

#define RETRY_SB_MICRON_SET_MLBI_NUM	(18)


typedef enum SBPAGETYPEEnum {
	RETRY_SB_MICRON_SLC_PAGE = 0,
	RETRY_SB_MICRON_MLC_LOWER_PAGE,
	RETRY_SB_MICRON_MLC_UPPER_PAGE,
	RETRY_SB_MICRON_QLC_LOWER_PAGE,
	RETRY_SB_MICRON_QLC_UPPER_PAGE,
	RETRY_SB_MICRON_QLC_EXTRA_PAGE,
	RETRY_SB_MICRON_QLC_TOP_PAGE
} SBPAGETYPEEnum_t;

typedef enum SBDPSENGINEPAGETYPEEnum {
	RETRY_SB_DSP_ENGINE_LOWER_PAGE = 0,
	RETRY_SB_DSP_ENGINE_UPPER_PAGE,
	RETRY_SB_DSP_ENGINE_EXTRA_PAGE,
	RETRY_SB_DSP_ENGINE_TOP_PAGE
} SBDPSENGINEPAGETYPEEnum_t;

typedef enum SoftBitFourthStateEnum {
	RETRY_SB_4TH_STATE_SB_READ_LEVEL_BY_DELTA_TABLE_SET_FEATURE = 1,                                         //(1)
	RETRY_SB_4TH_STATE_SB_READ_LEVEL_BY_DELTA_TABLE_GET_FEATURE,
	RETRY_SB_4TH_STATE_SB_READ_LEVEL_BY_DELTA_TABLE_CHECK_FEATURE,

} SoftBitFourthStateEnum_t;


typedef enum SoftBitSubStateEnum {
	RETRY_SB_SUBSTATE_DET_COARSE_LVL__INIT = 1,							// 1

	//for Coarse Tuning Version2 in Phison Flow
	RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_SET_INIT_READ_LEVEL,
	RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_SET_READ_LEVEL,
	RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_SELECT_READ_SHARED_PAGE,
	//State blow should stay continuous
	RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_READ_SHARED_LOWER_PAGE,
	RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_READ_SHARED_UPPER_PAGE,
	RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_READ_SHARED_EXTRA_PAGE,
	//State above should stay continuous
	RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_DSP_TRIGGER,

	RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_CHK_COARSE_TABLE,
	RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_IDLE_AND_WAIT,

	RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_INIT, // 11
	RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_SET_READ_LEVEL_OFFSET,
	RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_ENABLE_AUTO_READ_CALIBRATION,
	RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_HB_READ,
	RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_HB_CORR_2K,

	RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_INIT,
	RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_SET_ARC_CALIBRATION_PERSISTENCE,
	RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_HB, // 18
	RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_SB1,
	RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_SB2,
	RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_SB3,
	RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_SB4,
	RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_IDLE_OR_WAIT,

	RETRY_SB_SUBSTATE_SB_DECODE_WITH_DEFAULT_LLR_INIT,
	RETRY_SB_SUBSTATE_SB_DECODE_WITH_DEFAULT_LLR_SB_CORR,

	RETRY_SB_SUBSTATE_SB_DECODE_WITH_ADT_LLR_INIT,
	RETRY_SB_SUBSTATE_SB_DECODE_WITH_ADT_LLR_SB_CORR,

	RETRY_SB_SUBSTATE_SB_DSP2_INIT, //28
	RETRY_SB_SUBSTATE_SB_DSP2_READ_TARGET_PAGE,
	RETRY_SB_SUBSTATE_SB_DSP2_GET_FEATURE_OF_ARC_RESULT,
	RETRY_SB_SUBSTATE_SB_DSP2_SET_READ_LEVEL_FOR_NEXT_WORDLINE_I,
	RETRY_SB_SUBSTATE_SB_DSP2_SET_READ_LEVEL_FOR_NEXT_WORDLINE_II,
	RETRY_SB_SUBSTATE_SB_DSP2_ENABLE_ARC_DISABLE_PERSISTENCE,
	RETRY_SB_SUBSTATE_SB_DSP2_READ_NEXT_WORDLINE_SHARE_PAGE_I,
	RETRY_SB_SUBSTATE_SB_DSP2_READ_NEXT_WORDLINE_SHARE_PAGE_II,
	RETRY_SB_SUBSTATE_SB_DSP2_GEN_SB6_SB7,
	RETRY_SB_SUBSTATE_SB_DSP2_DISABLE_ARC_ENABLE_PERSISTENCE,
	RETRY_SB_SUBSTATE_SB_DSP2_READ_LEVEL_HB,
	RETRY_SB_SUBSTATE_SB_DSP2_READ_LEVEL_SB1,
	RETRY_SB_SUBSTATE_SB_DSP2_READ_LEVEL_SB2,
	RETRY_SB_SUBSTATE_SB_DSP2_READ_LEVEL_SB3,
	RETRY_SB_SUBSTATE_SB_DSP2_READ_LEVEL_SB4,
	RETRY_SB_SUBSTATE_SB_DSP2_SB_TRIG_SBC_FOR_ADT_LLR,
	RETRY_SB_SUBSTATE_SB_DSP2_INT_SB_CORR,
	RETRY_SB_SUBSTATE_SB_DSP2_SB_CORR,

	RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_WITH_SPECIFIC_RETRY_TABLE_READ_OFFSET_INIT, //46
	RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_WITH_SPECIFIC_RETRY_TABLE_READ_OFFSET_SET_FEATURE,
	RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_WITH_SPECIFIC_RETRY_TABLE_READ_OFFSET_ENABLE_AUTO_READ_CALIBRATION,
	RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_WITH_SPECIFIC_RETRY_TABLE_READ_OFFSET_HB_READ,
	RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_WITH_SPECIFIC_RETRY_TABLE_READ_OFFSET_HB_CORR_2K,

	RETRY_SB_SUBSTATE_WITH_OPT_READ_LEVEL__AFTER_RAIDECC__INIT,

	RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_INIT,
	RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_ARC,
	//State blow should stay continuous
	RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_READ_OFFSET_LOWER,
	RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_READ_OFFSET_UPPER,
	RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_READ_OFFSET_EXTRA,
	//State above should stay continuous
	RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_SB_RR_OFFSET,
	RETRY_SB_SUBSTATE_SET_DEFAULT_CORRECTIVE_READ_SETTING,
	RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_SB_READ_OFFSET,
	RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_RETRY_TABLE,

	RETRY_SB_SUBSTATE_SET_WORDLINE_BYPASS_SET_FEATURE, //61
	RETRY_SB_SUBSTATE_SET_WORDLINE_BYPASS_GET_FEATURE,
	RETRY_SB_SUBSTATE_SET_WORDLINE_BYPASS_CHECK_FEATURE,

	//Phison Flow Clear Prefix Read Offset
	RETRY_SB_SUBSTATE_CLEAR_PREFIX_READ_OFFSET,

	//Micron Nicks2 flow
	RETRY_SB_SUBSTATE_HB_RAW_READ_INIT,
	RETRY_SB_SUBSTATE_HB_RAW_READ,
	RETRY_SB_SUBSTATE_HB_RAW_READ_SET_7TH_ADDRESS,
	RETRY_SB_SUBSTATE_HB_RAW_READ_SET_4b_CR_MIN1SW_READ_OFFSET,
	RETRY_SB_SUBSTATE_HB_RAW_READ_SBC_HB_CORR_2K,

	//N48R Flow New
	RETRY_SB_SUBSTATE_HB_4K_FRAME_READ_INIT, //70
	RETRY_SB_SUBSTATE_HB_4K_FRAME_READ_SET_PREFIX_READ_RETRY,
	RETRY_SB_SUBSTATE_HB_4K_FRAME_READ_SET_PREFIX_READ_RETRY_BY_MIN_SW,
	RETRY_SB_SUBSTATE_HB_4K_FRAME_READ_SET_PREFIX_READ_RETRY_BY_MIN_SW_VALLY_TRACK,
	RETRY_SB_SUBSTATE_HB_4K_FRAME_READ_OP_ONLY,
	RETRY_SB_SUBSTATE_HB_4K_FRAME_READ_OP_DMA_DECODE,
	RETRY_SB_SUBSTATE_HB_READ_SET_CR_2B_4B_ENHANCE_CR,

	RETRY_SB_SUBSTATE_SB_READ_INIT,
	RETRY_SB_SUBSTATE_SB_RAW_READ_SET_7TH_ADDRESS,
	RETRY_SB_SUBSTATE_SB_READ_HB,
	RETRY_SB_SUBSTATE_SB_READ_SB1,
	RETRY_SB_SUBSTATE_SB_READ_SB2,
	RETRY_SB_SUBSTATE_SB_READ_IDLE_OR_WAIT,
	RETRY_SB_SUBSTATE_SB_READ_SET_ARC_CALIBRATION_PERSISTENCE,
	RETRY_SB_SUBSTATE_SB_READ_SET_CR_2B_4B_ENHANCE_CR,

	RETRY_SB_SUBSTATE_SB_DECODE_INIT,
	RETRY_SB_SUBSTATE_SB_DECODE_SB_CORR,

	RETRY_SB_SUBSTATE_NULL = 0xFF
} SoftBitSubStateEnum_t;

/*
 *  SOFTBIT 3RD STATE MACHINE
 *  =====================
 */
typedef enum SoftBitThirdStateEnum {
	RETRY_SB_3RD_STATE_HB_READ_INIT = 1,
	RETRY_SB_3RD_STATE_HB_READ_DIRECT_READ_OP,
	RETRY_SB_3RD_STATE_HB_READ_DIRECT_READ_DMA,

	RETRY_SB_3RD_STATE_SET_READ_LEVEL_OFFSET_INIT,
	//State blow should stay continuous
	RETRY_SB_3RD_STATE_SET_READ_LEVEL_OFFSET_I,
	RETRY_SB_3RD_STATE_SET_READ_LEVEL_OFFSET_II,
	RETRY_SB_3RD_STATE_SET_READ_LEVEL_OFFSET_III,
	RETRY_SB_3RD_STATE_SET_READ_LEVEL_OFFSET_IV,
	//State above should stay continuous

	//State blow should stay continuous
	RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_I,
	RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_II,
	RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_III,
	RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_IV,
	//State above should stay continuous

	//State blow should stay continuous
	RETRY_SB_3RD_STATE_CHECK_READ_LEVEL_OFFSET_I,
	RETRY_SB_3RD_STATE_CHECK_READ_LEVEL_OFFSET_II,
	RETRY_SB_3RD_STATE_CHECK_READ_LEVEL_OFFSET_III,
	RETRY_SB_3RD_STATE_CHECK_READ_LEVEL_OFFSET_IV,
	//State above should stay continuous

	RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_SET_FEATURE, // 17
	RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_GET_FEATURE, // 18
	RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_CHECK_FEATURE, // 19

	RETRY_SB_3RD_STATE_SETTING_RETRY_TABLE_SET_FEATURE,
	RETRY_SB_3RD_STATE_SETTING_RETRY_TABLE_GET_FEATURE,
	RETRY_SB_3RD_STATE_SETTING_RETRY_TABLE_CHECK_FEATURE,

	RETRY_SB_3RD_STATE_SB_READ_SB1_INIT,
	RETRY_SB_3RD_STATE_SB_READ_SB1_DELTA_SET_FEATURE,
	RETRY_SB_3RD_STATE_SB_READ_SB1_DELTA_READ,
	RETRY_SB_3RD_STATE_SB_READ_SB1_DELTA_DMA,

	RETRY_SB_3RD_STATE_SB_READ_SB2_INIT, //27
	RETRY_SB_3RD_STATE_SB_READ_SB2_DELTA_SET_FEATURE,
	RETRY_SB_3RD_STATE_SB_READ_SB2_DELTA_READ,
	RETRY_SB_3RD_STATE_SB_READ_SB2_DELTA_DMA,
	RETRY_SB_3RD_STATE_SB_READ_SB2_INIT_2,
	RETRY_SB_3RD_STATE_SB_READ_SB2_PLUS_MINUS_1_DELTA_SET_FEATURE,
	RETRY_SB_3RD_STATE_SB_READ_SB2_PLUS_MINUS_1_DELTA_READ,
	RETRY_SB_3RD_STATE_SB_READ_SB2_PLUS_MINUS_1_DELTA_DMA_WITH_XNOR,

	RETRY_SB_3RD_STATE_SB_READ_SB3_INIT,
	RETRY_SB_3RD_STATE_SB_READ_SB3_READ,
	RETRY_SB_3RD_STATE_SB_READ_SB3_DMA,

	RETRY_SB_3RD_STATE_SB_READ_SB4_INIT, //38
	RETRY_SB_3RD_STATE_SB_READ_SB4_READ,
	RETRY_SB_3RD_STATE_SB_READ_SB4_DMA,

	RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_FROM_ARC_INIT,
	//State blow should stay continuous
	RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_FROM_ARC_I,
	RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_FROM_ARC_II,
	RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_FROM_ARC_III,
	RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_FROM_ARC_IV,
	//State above should stay continuous

	//State blow should stay continuous
	RETRY_SB_3RD_STATE_UPDATE_ARC_READ_LEVEL_OFFSET_TABLE_I,
	RETRY_SB_3RD_STATE_UPDATE_ARC_READ_LEVEL_OFFSET_TABLE_II,
	RETRY_SB_3RD_STATE_UPDATE_ARC_READ_LEVEL_OFFSET_TABLE_III,
	RETRY_SB_3RD_STATE_UPDATE_ARC_READ_LEVEL_OFFSET_TABLE_IV,
	//State above should stay continuous

	RETRY_SB_3RD_STATE_SB_CLEAR_SB_OFFSET_INIT, //50
	RETRY_SB_3RD_STATE_SB_CLEAR_SB_OFFSET_DELTA_SET_FEATURE,

	//Phison Flow Clear Prefix Read Offset with Dummy Read CMD
	RETRY_SB_3RD_STATE_HB_READ_CLEAR_PREFIX_DUMMY_OP,

	//For  N48
	RETRY_SB_3RD_STATE_SETTING_CORRECTIVE_READ_SET_FEATURE,
	RETRY_SB_3RD_STATE_SETTING_CORRECTIVE_READ_GET_FEATURE,
	RETRY_SB_3RD_STATE_SETTING_CORRECTIVE_READ_CHECK_FEATURE,

	RETRY_SB_3RD_STATE_CLEAR_RR_OFFSET_SET_FEATURE,
	RETRY_SB_3RD_STATE_CLEAR_RR_OFFSET_GET_FEATURE,
	RETRY_SB_3RD_STATE_CLEAR_RR_OFFSET_CHECK_FEATURE,

	RETRY_SB_3RD_HB_READ_DIRECT_READ_FOR_TURBO_RAIN,

	RETRY_SB_3RD_STATE_NULL = 0xFF
} SoftBitThirdStateEnum_t;


typedef enum SBMICRONSTAGEEnum {
	RETRY_SB_MICRON_STAGE_1 = 0,
	RETRY_SB_MICRON_STAGE_2,
	RETRY_SB_MICRON_STAGE_3,
	RETRY_SB_MICRON_STAGE_4,
	RETRY_SB_MICRON_STAGE_5,
	RETRY_SB_MICRON_STAGE_6,
	RETRY_SB_MICRON_STAGE_7,
	RETRY_SB_MICRON_STAGE_NUM
} SBMICRONSTAGEEnum_t;

typedef enum SBMICRONSLCMLCSTEPEnum {
	RETRY_SB_MICRON_SLC_MLC_STEP_READ_RETRY0 = 0, 			//1.1, step 0
	RETRY_SB_MICRON_SLC_MLC_STEP_PREFIX_RR1,				//2.1, step 1
	RETRY_SB_MICRON_SLC_MLC_STEP_PREFIX_RR2,				//2.2, step 2
	RETRY_SB_MICRON_SLC_MLC_STEP_PREFIX_RR3,				//2.3, step 3
	RETRY_SB_MICRON_SLC_MLC_STEP_PREFIX_RR4,				//2.4, step 4
	RETRY_SB_MICRON_SLC_MLC_STEP_PREFIX_RR5,				//2.5, step 5
	RETRY_SB_MICRON_SLC_MLC_STEP_PREFIX_RR6,				//2.6, step 6
	RETRY_SB_MICRON_SLC_MLC_STEP_PREFIX_RR7,				//2.7, step 7
	RETRY_SB_MICRON_SLC_MLC_STEP_PREFIX_RR8,				//2.8, step 8
	RETRY_SB_MICRON_SLC_MLC_STEP_MIN1SW_PRFIX_RR_ARC1,		//3.1, step 9
	RETRY_SB_MICRON_SLC_MLC_STEP_MIN1SW_PRFIX_RR_ARC3_0,	//3.2, step 10
	RETRY_SB_MICRON_SLC_MLC_STEP_MIN1SW_PRFIX_RR_ARC3_1,	//3.3, step 11
	RETRY_SB_MICRON_SLC_MLC_STEP_MIN1SW_PRFIX_RR_ARC3_2,	//3.4, step 12
	RETRY_SB_MICRON_SLC_MLC_STEP_PREFIX_RR_ARC2_SBSBR1,		//4.1, step 13
	RETRY_SB_MICRON_SLC_MLC_STEP_PREFIX_RR_ARC2_SBSBR2,		//4.2, step 14
	RETRY_SB_MICRON_SLC_MLC_STEP_NUM
} SBMICRONSLCMLCSTEPEnum_t;

typedef enum SBMICRONQLCSTEPEnum {
	RETRY_SB_MICRON_STEP_NORMAL_READ			= 0,							//1.0, step 0
	RETRY_SB_MICRON_STEP_PERSISTENT_FINE_CALIB,		 						//1.1, step 1
	RETRY_SB_MICRON_STEP_EXPRESS_SBSBR_1_2,									//1.2, step 2
	RETRY_SB_MICRON_STEP_CURCBC_P1_FINE_CALIB,								//2.1, step 3
	RETRY_SB_MICRON_STEP_EXPRESS_SBSBR_2_2,									//2.2, step 4
	RETRY_SB_MICRON_STEP_CURCBC_N1_FINE_CALIB,								//2.3, step 5
	RETRY_SB_MICRON_STEP_EXPRESS_SBSBR_2_4,									//2.4, step 6
	RETRY_SB_MICRON_STEP_CURCBC_P2_FINE_CALIB,								//2.5, step 7
	RETRY_SB_MICRON_STEP_CURCBC_N2_FINE_CALIB,								//2.6, step 8
	RETRY_SB_MICRON_STEP_CURCBC_P3_FINE_CALIB,								//2.7, step 9
	RETRY_SB_MICRON_STEP_CRTRIM_SELECTION,									//3.1, step 10
	RETRY_SB_MICRON_STEP_ENHANCED_TRIM_APPLICATION,							//3.2, step 11
	RETRY_SB_MICRON_STEP_PREFIX_RR1_FINE_CALIB_PERSISTENT_FINE_CALIB,		//4.1, step 12
	RETRY_SB_MICRON_STEP_PREFIX_RR2_FINE_CALIB_PERSISTENT_FINE_CALIB,		//4.2, step 13
	RETRY_SB_MICRON_STEP_PREFIX_RR3_FINE_CALIB_PERSISTENT_FINE_CALIB,		//4.3, step 14
	RETRY_SB_MICRON_STEP_PREFIX_RR4_FINE_CALIB_PERSISTENT_FINE_CALIB,		//4.4, step 15
	RETRY_SB_MICRON_STEP_PREFIX_RR5_FINE_CALIB_PERSISTENT_FINE_CALIB,		//4.5, step 16
	RETRY_SB_MICRON_STEP_PREFIX_RR6_FINE_CALIB_PERSISTENT_FINE_CALIB,		//4.6, step 17
	RETRY_SB_MICRON_STEP_PREFIX_RR7_FINE_CALIB_PERSISTENT_FINE_CALIB,		//4.7, step 18
	RETRY_SB_MICRON_STEP_PREFIX_RR8_FINE_CALIB_PERSISTENT_FINE_CALIB,		//4.8, step 19
	RETRY_SB_MICRON_STEP_MIN1SW_PRFIX_RR_FINE_CALIB_PERSISTENT_FINE_CALIB,	//5.1a, step 20
	RETRY_SB_MICRON_STEP_MIN1SW_PRFIX_RR_CALIBRATED_OFFSET_CR_2B,			//5.1b, step 21
	RETRY_SB_MICRON_STEP_MIN2SW_PRFIX_RR_FINE_CALIB_PERSISTENT_FINE_CALIB,	//5.2a, step 22
	RETRY_SB_MICRON_STEP_MIN2SW_PRFIX_RR_CALIBRATED_OFFSET_CR_2B,			//5.2b, step 23
	RETRY_SB_MICRON_STEP_MIN3SW_PRFIX_RR_FINE_CALIB_PERSISTENT_FINE_CALIB,	//5.3a, step 24
	RETRY_SB_MICRON_STEP_MIN3SW_PRFIX_RR_CALIBRATED_OFFSET_CR_2B,			//5.3b, step 25
	RETRY_SB_MICRON_STEP_RESOTRE_POR_TRIM,									//5.4, step 26
	RETRY_SB_MICRON_STEP_MIN1SW_PRFIX_RR_SBSBR1_CR_2b,						//6.1, step 27
	RETRY_SB_MICRON_STEP_MIN1SW_PRFIX_RR_CR_4b,								//6.2, step 28
	RETRY_SB_MICRON_STEP_MIN1SW_PRFIX_RR_SBSBR1_CR_4b,						//6.3, step 29
	RETRY_SB_MICRON_STEP_MIN1SW_PRFIX_RR_SBSBR2_CR_4b,						//6.4, step 30
	RETRY_SB_MICRON_STEP_NUM
} SBMICRONQLCSTEPEnum_t;

typedef struct SoftBitLLR40Bits   SoftBitLLR40Bits_t;
struct SoftBitLLR40Bits {      // use union type to optimize.
	U8 ubLLR8Bits[5];
};

typedef struct SoftBitLLRTable   SoftBitLLRTable_t;
struct SoftBitLLRTable {      // use union type to optimize.
	union {
		struct {
			U32 ulLLR32bits[10];
		} AccessBy32bits;
		struct {
			U8 ubLLR8bits[40];
		} AccessBy8bits;
		SoftBitLLR40Bits_t LLR40Bits[8];
	} LLRTable;
};

typedef struct LLRTable40BitsAccess   LLRTable40BitsAccess_t;
struct LLRTable40BitsAccess {      // use union type to optimize.
	union {
		U64 uoAll;
		SoftBitLLR40Bits_t LLR40Bits;
	} LLRTable;
};

typedef struct ReadOffset4Param   ReadOffset4Param_t;
struct ReadOffset4Param {      // use union type to optimize.
	union {
		U32 ulAll;
		struct {
			U8 ubParam[4];
		} Param;
	} ReadOffset4Param;
};

typedef struct SoftBitMinSWInfo   SoftBitMinSWInfo_t;
struct SoftBitMinSWInfo {      // use union type to optimize.
	//Record For Stage1 Stage3
	U8 ubMinSWStep[RETRY_SB_FLOW_HB_MICRON_MIN_SW_NUM];
	U16 uwMinSWValue[RETRY_SB_FLOW_HB_MICRON_MIN_SW_NUM];
	ReadOffset4Param_t SWReadOffset[RETRY_SB_FLOW_HB_MICRON_MIN_SW_NUM];
	//Record For Stage5 Stage6
	U8 ubCurrentUsingMinSWIdx;
	ReadOffset4Param_t MinSWReadOffset;
	U16 uwMinSWValueForStage5_6;
	U8 ubMinSWIdxForStage5_6;

};

extern SoftBitMinSWInfo_t gSBMinSWInfo;
#endif /*(((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)) && ((TRUE == RETRY_MICRON_NICKS_ALL_PAGE_TYPE_SB_SUPPORT) && (TRUE == IM_N48R)))*/
#endif /*_RETRY_MICRON_QLC_SOFTBIT_RETRY_ALL_PAGE_TYPE_N48_H_*/