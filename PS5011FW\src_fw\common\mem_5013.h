/** @file mem_5013.h
 *  @brief
 *
 *
 *  <AUTHOR>  @bug No know bugs.
 */

#ifndef _MEM_5013_H_
#define _MEM_5013_H_
#include "setup.h"

#if ASIC
#define REFERENCE_CLOCK								(40000000)

#define UART_REFERENCE_CLOCK						(REFERENCE_CLOCK)

#define CPU_MHZ										(667000000)
#define CPU_CLK_US									(CPU_MHZ/(1000*1000))

#else   /* ASIC */

// FPGA
#define REFERENCE_CLOCK								(40000000)

#define UART_REFERENCE_CLOCK						(REFERENCE_CLOCK)

#define CPU_MHZ										(40000000)
#define CPU_CLK_US									(CPU_MHZ/(1000*1000))

#endif /* ASIC */

#define BMU_LB_CNT									(1024)

//
// define HW register base address - refer to PS5011_addmap.xlsx
//

//============================================================
// AXI
//============================================================
// E13: ATCM 160KB
#define ATCM_RAM_ADDRESS							(0x00000000)
#define ATCM_RAM_SIZE								(0x00028000)
#define ATCM_ORIGINAL_SIZE							(0x00020000)

#if OVERLAY_EN
#if (FPGA_BOARD == FPGA_USE_V7)
#define ATCM_CODE_BANK_ADDRESS                      (0x00020000)
#else /* (FPGA_BOARD == FPGA_USE_V7) */
#define ATCM_CODE_BANK_ADDRESS                      (0x00020000)
#endif /* (FPGA_BOARD == FPGA_USE_V7) */

#define ATCM_CODE_BANK_SIZE                         (0x00008000)
#else /*OVERLAY_EN*/
#define ATCM_CODE_BANK_ADDRESS                      (0x00020000)
#define ATCM_CODE_BANK_SIZE                         (0x00020000)
#endif /*OVERLAY_EN*/

#define RMA_LOG_SAVE_FIP_REG_ADDR					(0x00025800)

// ROM 64KB
#define	ROM_ADDRESS									(0x00070000)
#define	ROM_SIZE									(0x00010000)
#define	ROM_AA_AES_KEY_BASE							(0x0007BDE4)
#define	ROM_BB_AES_KEY_BASE							(0x0007C5C0)
#define	ROM_AA_RSA_KEY_BASE							(0x0007CA70)
#define	ROM_BB_RSA_KEY_BASE							(0x0007DF60)

// 執行RSA algorithm KAT (Known Answer Test)所需內容
// N, R, RR, N0為RSA Algorithm所定義的 key information
#define ROM_AA_RSA_KAT_1V5_N						(0x0007BFBE)
#define ROM_BB_RSA_KAT_1V5_N						(0x0007C7A0)
#define ROM_AA_RSA_KAT_1V5_R						(0x0007C0BE)
#define ROM_BB_RSA_KAT_1V5_R						(0x0007C8A0)
#define ROM_AA_RSA_KAT_1V5_RR						(0x0007C1BE)
#define ROM_BB_RSA_KAT_1V5_RR						(0x0007C9A0)
#define ROM_AA_RSA_KAT_1V5_N0						(0x0007C3BE)
#define ROM_BB_RSA_KAT_1V5_N0						(0x0007CBA0)
#define ROM_AA_RSA_KAT_1V5_MESSAGE					(0x0007BF3E)
#define ROM_BB_RSA_KAT_1V5_MESSAGE					(0x0007C720)
#define ROM_AA_RSA_KAT_1V5_SIGN						(0x0007C2BE)
#define ROM_BB_RSA_KAT_1V5_SIGN						(0x0007CAA0)

// BTCM0 16KB
#define BTCM0_RAM_ADDRESS							(0x00080000)
#define BTCM0_RAM_SIZE								(0x00004000)
#define BTCM0_GET_TEMPERATURE_SIZE					(0x800)
#define BTCM0_GET_TEMPERATURE_ADDR					(BTCM0_RAM_ADDRESS + BTCM0_RAM_SIZE - BTCM0_GET_TEMPERATURE_SIZE)	// last 2k of BTCM

#if (MICRON_FSP_EN)
#if (TT_TEMPERATURE_DATA_LOCATION == TT_TEMPERATURE_DATA_IN_FWLB_HOST_TABLE)
#if (IM_N48R)
#define	BTCM0_EXTEND_CODE_SIZE						(0x1980)
#else /* (IM_N48R) */
#define	BTCM0_EXTEND_CODE_SIZE						(0x1800)
#endif /* (IM_N48R) */
#define BTCM0_EXTEND_CODE_ADDR						(BTCM0_RAM_ADDRESS + BTCM0_RAM_SIZE - BTCM0_EXTEND_CODE_SIZE)
#else /* (TT_TEMPERATURE_DATA_LOCATION == TT_TEMPERATURE_DATA_IN_FWLB_HOST_TABLE) */
#define	BTCM0_EXTEND_CODE_SIZE						(0x1000)
#define BTCM0_EXTEND_CODE_ADDR						(BTCM0_GET_TEMPERATURE_ADDR - BTCM0_EXTEND_CODE_SIZE)
#endif /* (TT_TEMPERATURE_DATA_LOCATION == TT_TEMPERATURE_DATA_IN_FWLB_HOST_TABLE) */
#else /* (MICRON_FSP_EN) */
#define	BTCM0_EXTEND_CODE_SIZE						(0x1000)
#define BTCM0_EXTEND_CODE_ADDR						(BTCM0_GET_TEMPERATURE_ADDR - BTCM0_EXTEND_CODE_SIZE)
#endif /* MICRON_FSP_EN */

// BTCM1 64KB
#define BTCM1_RAM_ADDRESS							(0x000C0000)
#define BTCM1_RAM_SIZE								(0x00010000)
#define BTCM1_COMMON_OFFSET							(0x00000000)
#define BTCM1_DEBUG_INFO_OFFSET						(0x0000DFA0)
#define BTCM1_COMMON_SIZE							(0x0000DFA0)
#define BTCM1_DEBUG_INFO_SIZE						(0x00000060)
#define BTCM1_DEBUG_INFO_ADDRESS					(BTCM1_RAM_ADDRESS + BTCM1_DEBUG_INFO_OFFSET)
#define BTCM1_VT_OFFSET								(0x0000E000)
#define BTCM1_VT_SIZE								(0x00001000)
#define BTCM1_STACK_OFFSET							(0x0000F000)
#define BTCM1_STACK_SIZE							(0x00001000)
#define DECLARE_DEBUG_INFO_FORCE_ADDR				FORCE_ADDR(0x000CDFA0)
#define DECLARE_VT_FORCE_ADDR						FORCE_ADDR(0x000CE000)

// CPU DCache 4KB
#define BTCM_DCACHE_RAM_ADDRESS						(0x000D0000)
#define BTCM_DCACHE_SIZE							(0x00001000)

// Andes ICCM
#define ANDES_ICCM_ADDRESS                          (0x00281000)
#define ANDES_ICCM_SIZE                             (0x00005000)

// Andes DCCM
#define ANDES_DCCM_ADDRESS                          (0x00286000)
#define ANDES_DCCM_SIZE                             (0x00007000)

// COP 0 RAM 128KB
#define COP0_RAM_ADDRESS							(0x00270000)
#define COP0_RAM_SIZE								(0x00020000)

#define COP0_VP_VP2_RAM_SIZE						(0x0000C800)
#define COP0_OPT_RAM_ADDRESS						(0x0027E000)
#define COP0_OPT_LINK_RAM_SIZE					    (0x00002800)

#define COP0_OPTD_RAM_ADDRESS						(0x00286000)
#define COP0_OPTD_RAM_SIZE							(0x00007000)

// flash IRAM 64KB
#define FLASH_IRAM_ADDRESS							(0x00290000)
#define FLASH_IRAM_SIZE								(0x00010000)

// Reserved 6kb
#define RESERVED_ADDRESS							(0x002A0000)
#define RESERVED_SIZE								(0x00001800)

// COP1 RAM 256KB
#define COP1_RAM_ADDRESS							(0x002A1800)
#define COP1_RAM_SIZE								(0x0001A100)
#define COP1_RAM_SIZE_SECTION1_FOR_SAVE			    (0x00016100)
#define COP1_GCBLK_RAM_ADDRESS					    (0x002B7900)
#define COP1_GCBLK_RAM_FOR_SAVE					    (0x00004000)

// BMU RAM 64KB
#define BMU_RAM_ADDRESS								(0x002E1800)
#define BMU_RAM_SIZE								(0x00010000)

// XZIP register address 4KB
#define XZIP_REG_ADDRESS							(0x002F0000) //S17 redefined
#define XZIP_REG_SIZE								(0x00001000)

// E13: SEC KEY RAM 2KB
#define SEC_KEY_RAM_ADDRESS							(0x002F1800)
#define SEC_KEY_RAM_SIZE							(0x00000800)

// SEC PKE RAM 8KB
#define SEC_PKE_RAM_ADDRESS							(0x002F2000)
#define SEC_PKE_RAM_SIZE							(0x00002000)

// MR RAM 16KB, MR的16K RAM, E13看的到, E17不能直接看到
#define MR_RAM_ADDRESS								(0x002F4000)
#define MR_RAM_SIZE									(0x00004000)

// AXI2AHB 4MB
#define AXI2AHB_ADDRESS								(0x00800000)
#define AXI2AHB_SIZE								(0X00400000)

// E13: COP 0 register address 128KB
#define COP0_REG_ADDRESS							(0x00C00000)
#define COP0_REG_SIZE								(0x00020000)

// flash register address 512KB
#define FLASH_REG_ADDRESS							(0x00C20000)
#define FLASH_REG_SIZE								(0x00080000)

#define FIP_CON_REG_ADDRESS							(FLASH_REG_ADDRESS)
#define FIP_CH0_REG_ADDRESS							(FIP_CON_REG_ADDRESS + 0x400)
#define FIP_CH1_REG_ADDRESS							(FIP_CH0_REG_ADDRESS + 0x200)
#define FIP_CH2_REG_ADDRESS							(FIP_CH1_REG_ADDRESS + 0x200)
#define FIP_CH3_REG_ADDRESS							(FIP_CH2_REG_ADDRESS + 0x200)
#define FIP_ALL_REG_ADDRESS							(FIP_CH3_REG_ADDRESS + 0x200)
#define FIP_CON_REG_SIZE							(0x00000400)
#define FIP_CH_REG_SIZE								(0x00000200)

#define FIP_DCTL0_REG_ADDRESS						(FIP_ALL_REG_ADDRESS   + 0x400)
#define FIP_DCTL1_REG_ADDRESS						(FIP_DCTL0_REG_ADDRESS + 0x200)
#define FIP_DCTL_REG_SIZE							(0x00000400)

#define BVCI_RAIDECC_REG_ADDRESS					(0x00C50000)
#define BVCI_RAIDECC_REG_SIZE						(0x00000200)
#define BVCI_RAIDECC_BPT_RAM_ADDRESS				(0x00C60000)
#define BVCI_RAIDECC_BPT1_RAM_ADDRESS				(0x00C61100)
#define BVCI_RAIDECC_BPT1_RAM_SIZE_FOR_SAVE			(0x00000200)
#define BVCI_RAIDECC_BPT_RAM_SIZE					(0x00002400)
#define BVCI_RAIDECC_INTERNAL_BUFFER_ADDRESS_BASE	(0x00C70000)

#define BVCI_RS_REG_ADDRESS							(0x00C50000)
#define BVCI_RS_REG_SIZE							(0x00000200)
#define BVCI_RS_BPT_RAM_ADDRESS						(0x00C60000)
#define BVCI_RS_BPT1_RAM_ADDRESS					(0x00C61100)
#define BVCI_RS_BPT1_RAM_SIZE_FOR_SAVE				(0x00000200)
#define BVCI_RS_BPT_RAM_SIZE						(0x00002400)
#define BVCI_RS_INTERNAL_BUFFER_ADDRESS_BASE		(0x00C70000)

// E13: cop 1 register address 128KB
#define COP1_REG_ADDRESS							(0x00CA0000)
#define COP1_REG_SIZE								(0x00020000)
#define COP1_REG_SIZE_FOR_SAVE						(0x00000600)

// E13: SPI register address 4KB
#define SPI_REG_ADDRESS								(0x00CC0000)
#define SPI_REG_SIZE								(0x00001000)

// D2H register address 4KB
#define D2H_REG_ADDRESS								(0x00CC1000)
#define D2H_REG_SIZE								(0x00001000)

// NVME register address 136KB
#define NVME_REG_ADDRESS							(0x00CC2000)
#define NVME_REG_SIZE								(0x00022000)

// COMB PHY register address 4KB
#define	COMB_PHY_REG_ADDRESS						(0x00CE4000)
#define	COMB_PHY_REG_SIZE							(0x00001000)

// UNIPRO PMU register address 128B
#define UNIPRO_PMU_REG_ADDRESS						(0x00CE5F80)
#define UNIPRO_PMU_REG_SIZE							(0x00000080)

// PCIE2 register address 4KB
#define PCIE2_REG_ADDRESS							(0x00CE6000)
#define PCIE2_REG_SIZE								(0x00001000)
#define PCIE2_REG_ADDRESS_FOR_SAVE					(0x00CE6800)
#define PCIE2_REG_SIZE_FOR_SAVE						(0x00000100)

// UNIPRO register address 8KB
#define UNIPRO_REG_ADDRESS							(0x00CE7000)
#define UNIPRO_REG_SIZE								(0x00002000)

// UFS register address 8KB
#define UFS_REG_ADDRESS								(0x00CE9000)
#define UFS_REG_SIZE								(0x00002000)

// APU register address 20KB
#define APU_REG_ADDRESS								(0x00CEB000)
#define APU_REG_SIZE								(0x00005000)
#define APU_REG_SIZE_FOR_SAVE_1						(0x00000A00)
#define APU_REG_SIZE_FOR_CMD						(0x00000800)
#define APU_REG_ADDRESS_FOR_SAVE_2					(0x00CEC1F0)
#define APU_REG_SIZE_FOR_SAVE_2						(0x00003E10)

// PCIE register address 16KB
#define	PCIE_REG_ADDRESS							(0x00CF0000)
#define	PCIE_REG_SIZE								(0x00004000)
#define PCIE_REG_SIZE_FOR_SAVE						(0x00002000)

// PCIE PLDA Control/Status address 4KB
#define PCIE_PLDA_REG_ADDRESS						(0x00CF0000)
#define PCIE_PLDA_REG_SIZE							(0x00001000)

// PCIE configure space register address 4KB
#define PCIE_CFGREG_ADDRESS							(0x00CF1000)
#define PCIE_CFGREG_SIZE							(0x00001000)

// E13: SEC register address 8KB
#define SECURITY_REG_ADDRESS						(0x00CF4000)
#define SECURITY_REG_SIZE							(0x00002000)

// BMU register address 4KB
#define BMU_REG_ADDRESS								(0x00CF6000)
#define BMU_REG_SIZE								(0x00001000)

// E3D512
#define E3D512_REG_ADDRESS							(0x00CF6F00)

// Doorbell register address 2KB
#define DOORBELL_REG_ADDRESS						(0x00907000)
#define DOORBELL_REG_SIZE							(0x00000800)

// MR register address 2KB
#define MR_REG_ADDRESS								(0x00CF7800)
#define MR_REG_SIZE									(0x00000800)

// SATA register address 8KB
#define SATA_REG_ADDRESS							(0x00CF8000)
#define SATA_REG_SIZE								(0x00002000)

// CPU DCACHE AXI slave 8MB
#define	CPU_DCACHE_AXI_SLAVE_ADDRESS				(0x00D50000)
#define CPU_DCACHE_AXI_SLAVE_SIZE					(0x00800000)

// DBUF PB 1.75MB for ZIP
#define DBUF_PB_FOR_ZIP_RAM_ADDRESS					(0x02000000)
#define DBUF_PB_FOR_ZIP_RAM_SIZE					(0x001C0000)

// UFS RAM 4KB
#define UFS_RAM_ADDRESS								(0x03200000)
#define UFS_RAM_SIZE								(0x00001000)

// DUMMY REMAP 32MB
#define DUMMY_REMAP_ADDRESS							(0x06000000)
#define DUMMY_REMAP_SIZE							(0x02000000)

// DBUF LB for ZIP 32MB
#define DBUF_LB_FOR_ZIP_RAM_ADDRESS					(0x12000000)
#define DBUF_LB_FOR_ZIP_RAM_SIZE					(0x02000000)

// DBUF PB 1.75MB
#define DBUF_PB_RAM_ADDRESS							(0x22000000)
#define DBUF_PB_RAM_SIZE							(0x001C0000)

// Defined by FW
#define VBRMP_OFFSET								(0x001AE000)	// VBRMP Table: Size = 8K ,   0x221AE000~0x221B0000
#define RUT_L2_OFFSET								(0x001B0000)	// RUT L2 Table: Size = 64K , 0x221B0000~0x221C0000

// DBUF PB Bank0 256KB for Veritcal Write / Read
#define DBUF_PB_BANK0_256KB_RAM_ADDRESS				(0x23000000)
#define DBUF_PB_BANK0_256KB_RAM_SIZE				(0x00040000)

// DBUF PB Bank0 8KB for Veritcal Write / Read
#define DBUF_PB_BANK0_8KB_RAM_ADDRESS				(0x23040000)
#define DBUF_PB_BANK0_8KB_RAM_SIZE					(0x00002000)

// DBUF PB Bank0 184KB for Veritcal Write / Read
#define DBUF_PB_BANK0_184KB_RAM_ADDRESS				(0x23042000)
#define DBUF_PB_BANK0_184KB_RAM_SIZE				(0x0002E000)

// DBUF LB 32MB
#define DBUF_LB_RAM_ADDRESS							(0x32000000)
#define DBUF_LB_RAM_SIZE							(0x02000000)

// E13: SPI RAM 32MB
#define SPI_RAM_ADDRESS								(0x34000000)
#define SPI_RAM_SIZE								(0x02000000)

/*
***************************************************************************************************
*  Host AXI Slave
***************************************************************************************************
*/
// D2H Zone 1
#define D2H_Z1_RAM_ADDRESS							(0x40000000)
#define D2H_Z1_RAM_SIZE								(0xA0000000)

// D2H Zone 2
#define D2H_Z2_RAM_ADDRESS							(0xE0000000)
#define	D2H_Z2_RAM_SIZE								(0x02000000)

//*************************************************
// AHB / AXI2AHB
//*************************************************
// system power domain 0 (PD0) register address 8KB
#define SYSTEM_PD0_REG_ADDRESS						(AXI2AHB_ADDRESS + 0)
#define SYSTEM_PD0_REG_SIZE							(0x00002000)

// system power domain 1 (PD1) register address 8KB
#define SYSTEM_PD1_REG_ADDRESS						(AXI2AHB_ADDRESS + 0x2000)
#define SYSTEM_PD1_REG_SIZE							(0x00002000)

// PIC register address 8KB
#define PIC_REG_ADDRESS								(AXI2AHB_ADDRESS + 0x4000)
#define PIC_REG_SIZE								(0x00002000)

// DZIP register address 4KB
#define DZIP_REG_ADDRESS							(AXI2AHB_ADDRESS + 0x6000)
#define DZIP_REG_SIZE								(0x00001000)

// DZIP RAM address 16KB
#define DZIP_RAM_ADDRESS							(AXI2AHB_ADDRESS + 0x7000)
#define DZIP_RAM_SIZE								(0x00004000)

//CPU Reg
#define CPU_REG_ADDRESS								(AXI2AHB_ADDRESS + 0x10000)
#define CPU_REG_SIZE								(0x00010000)

//Coresight REG
#define CORESIGHT_REG_ADDRESS						(AXI2AHB_ADDRESS + 0x80000)
#define CORESIGHT_SIZE								(0x00050000)

//Doorbell Info
#define DOORBELL_INFO_ADDRESS						(AXI2AHB_ADDRESS + 0x107000)
#define DOORBELL_INFO_SIZE							(0x00000800)

//***************************************
// Begin of Boot code definition
//***************************************
//4KB
#define FLHENV_STRUCT_BASE      DBUF_PB_RAM_ADDRESS 								//0x22000000
#define	FLHENV_STRUCT_SIZE		DEF_128B
#define FW_CODEINFO_BASE        (FLHENV_STRUCT_BASE + FLHENV_STRUCT_SIZE)	//0x22000080  //Structures used by Boot Code and is passed on to Burner/Firmware for their reference
#if BOOTLOADER_EN
#define FW_CODEINFO_SIZE        DEF_64B
#define BOOTLOADER_INFO_BASE    (FW_CODEINFO_BASE + FW_CODEINFO_SIZE)   //0x220000C0
#define BOOTLOADER_INFO_SIZE    DEF_64B
#define EFUSE_PARAM_BASE        (BOOTLOADER_INFO_BASE + BOOTLOADER_INFO_SIZE)   //0x22000100  //Structures used by Boot Code and is passed on to Burner/Firmware for their reference
#else /* BOOTLOADER_EN */
#define FW_CODEINFO_SIZE		DEF_128B
#define EFUSE_PARAM_BASE       	(FW_CODEINFO_BASE   + FW_CODEINFO_SIZE)		//0x22000100  //Structures used by Boot Code and is passed on to Burner/Firmware for their reference
#endif /* BOOTLOADER_EN */
#define EFUSE_BANK2_BASE		(EFUSE_PARAM_BASE)
#define EFUSE_BANK3_BASE		(EFUSE_PARAM_BASE + DEF_32B)
#define EFUSE_BANK4_BASE		(EFUSE_PARAM_BASE + DEF_32B*2)
#define EFUSE_BANK7_BASE		(EFUSE_PARAM_BASE + DEF_32B*3)
#define	EFUSE_PARAM_SIZE		DEF_128B
#define SECTION_INFO_BASE		(EFUSE_PARAM_BASE   + EFUSE_PARAM_SIZE)		//0x22000180  //Structures used by Boot Code and is passed on to Burner/Firmware for their reference
#define	SECTION_INFO_SIZE		DEF_128B

#if BOOTLOADER_EN
#define BOOTLOADER_SECTION_INFO_BASE	(SECTION_INFO_BASE + SECTION_INFO_SIZE) //0x2200_0200
#define BOOTLOADER_SECTION_INFO_SIZE 	(DBUF_FW_SECTION_SIZE)

//1 used for LPM back up gFWSlotFWSection
#define FW_SECTION_SLOT_INFO_BASE	(BOOTLOADER_SECTION_INFO_BASE + BOOTLOADER_SECTION_INFO_SIZE)   //0x2200_0228
#define FW_SECTION_SLOT_INFO_SIZE 	(sizeof(FW_SLOT_t))

#define FLHENV_STRUCT_MP_BASE       (FW_SECTION_SLOT_INFO_BASE + FW_SECTION_SLOT_INFO_SIZE) //0x2200_0298
#define FLHENV_STRUCT_MP_SIZE       (sizeof(FLH_ENV_STRUCT_MP_t))

#define BOOT_FREE_BUF_BASE          (FLHENV_STRUCT_MP_BASE + FLHENV_STRUCT_MP_SIZE) //0x2200_02B8
#define	BOOT_FREE_BUF_SIZE          (DEF_512B - BOOTLOADER_SECTION_INFO_SIZE - FW_SECTION_SLOT_INFO_SIZE - FLHENV_STRUCT_MP_SIZE) // 328Bytes

#define HOST_INFO_BASE				(BOOT_FREE_BUF_BASE + BOOT_FREE_BUF_SIZE)   //0x2200_0400
#define	HOST_INFO_SIZE				DEF_128B
#else /* BOOTLOADER_EN */
#define BOOT_SYSTEM_INFO_BASE	(SECTION_INFO_BASE + SECTION_INFO_SIZE)		//0x22000200
#define BOOT_DEBUG_INFO_BASE 	(BOOT_SYSTEM_INFO_BASE + DEF_256B)
#define	BOOT_SYSTEM_INFO_SIZE	DEF_512B
#define HOST_INFO_BASE			(BOOT_SYSTEM_INFO_BASE + BOOT_SYSTEM_INFO_SIZE)     //0x22000400
#define	HOST_INFO_SIZE			DEF_128B
#endif /* BOOTLOADER_EN */

#define FLH_MISC_BASE			(HOST_INFO_BASE + HOST_INFO_SIZE)			//0x22000480
#define BOOT_CODE_MISC_SIZE		(DEF_KB(2) + DEF_512B + DEF_384B)

//8KB
#define BOOT_IDPAGE_BASE 		(FLH_MISC_BASE 		+ BOOT_CODE_MISC_SIZE)  //0x22001000  //The ID Table will be kept here after it has been found and loaded from NAND
#define BOOT_IDPAGE_SIZE		DEF_KB(4)
#define BOOT_CODEPTR_BASE		(BOOT_IDPAGE_BASE   + BOOT_IDPAGE_SIZE)		//0x22002000  //The Code Pointer will be kept here after it has been found and loaded from NAND
#define BOOT_CODEPTR_SIZE		DEF_KB(4)

//7KB
#define BOOT_CODESIGN_BASE	    (BOOT_CODEPTR_BASE  + BOOT_CODEPTR_SIZE)	//S17:0x22004000 , E13:0x22003000  // The CodeSign Header will be kept here after it has been found and loaded from NAND
#define BOOT_CODESIGN_SIZE		DEF_KB(1) //for double sign backup
#define BOOT_DIGEST_BASE        (BOOT_CODESIGN_BASE + BOOT_CODESIGN_SIZE)	//S17:0x22004400 , E13:0x22003400  // use to store Signature from FW
#define BOOT_DIGEST_SIZE		DEF_512B

#define RSA_KEY_BASE			(BOOT_DIGEST_BASE+ BOOT_DIGEST_SIZE)  //S17:0x22004600 , E13:0x22003600  // store key data from NAND (n e n0 r rr )
#define KEY_N_OFFSET		    (0)
#define KEY_e_OFFSET		    (256)
#define KEY_n0_OFFSET		    (260)
#define KEY_R_OFFSET		    (264)
#define KEY_RR_OFFSET		    (520)
#define RSA_KEY_SIZE			(DEF_KB(1) + DEF_256B)
#define SHA_RESULT_BASE		    (RSA_KEY_BASE       + RSA_KEY_SIZE)  		//S17:0x22004D00 , E13:0x22003B00  // When Boot code need to calculate SHA256, put here
#define SHA_RESULT_SIZE			DEF_256B

#define DIGEST_DECRYPT_BASE  	(SHA_RESULT_BASE   + SHA_RESULT_SIZE)	   	//S17:0x22004E00 , E13:0x22003C00  // When Boot code need to decrypt RSA put here
#define DIGEST_DECRYPT_SIZE		DEF_512B

#define SECURITY_TEMP_BASE		(DIGEST_DECRYPT_BASE  	+ DIGEST_DECRYPT_SIZE) //S17:0x22005000 , E13:0x22003E00  //  use for security buffer
#define SECURITY_TEMP_SIZE      (DEF_KB(1) + DEF_512B)
// End of Boot code definition

#if BOOTLOADER_EN
#define BOOTLOADER_SIGNATURE_TEMP_BASE		(SECURITY_TEMP_BASE + SECURITY_TEMP_SIZE)  //E13:0x2200_4400 //  signature page before execute
#define BOOTLOADER_SIGNATURE_TEMP_SIZE		(DEF_KB(3))

#define BOOTLOADER_CRC32_RESULT_BASE		(BOOTLOADER_SIGNATURE_TEMP_BASE + BOOTLOADER_SIGNATURE_TEMP_SIZE)   //S17:0x2200AC00 , E13:0x2200_5000
#define BOOTLOADER_CRC32_RESULT_SIZE		(DEF_KB(1))

#define BOOTLOADER_FREE_BUF_BASE        	(BOOTLOADER_CRC32_RESULT_BASE + BOOTLOADER_CRC32_RESULT_SIZE) //S17:0x2200B000 , E13:0x2200_5400
#define BOOTLOADER_FREE_BUF_SIZE        	(DEF_KB(107))

#define BOOTLOADER_READ_BUF_BASE        	(BOOTLOADER_FREE_BUF_BASE + BOOTLOADER_FREE_BUF_SIZE)   //0x2202_0000
#define BOOTLOADER_READ_BUF_SIZE        	(DEF_KB(64))    //for loading code from code block 4-CH parallelly

#define BOOTLOADER_BUF_RESERVE_SIZE     	(DEF_KB(64))    //program buffer

#define BOOTLOADER_XMODEM_RECEIVE_BASE     	(BOOTLOADER_READ_BUF_BASE + BOOTLOADER_READ_BUF_SIZE + BOOTLOADER_BUF_RESERVE_SIZE) //0x2204_0000
#define BOOTLOADER_XMODEM_RECEIVE_SIZE     	(DEF_KB(1024))  //original 1M + HostBuffer(256K) + VendorProcessBuffer(256K)
#define BOOTLOADER_LOAD_CODE_BUF_SIZE    	(DEF_KB(512))

#define BOOTLOADER_HOST_BUFFER_BASE     	(BOOTLOADER_XMODEM_RECEIVE_BASE + BOOTLOADER_XMODEM_RECEIVE_SIZE) //0x2214_0000
#define BOOTLOADER_HOST_BUFFER_SIZE     	(DEF_KB(256))

#define BOOTLOADER_VENDOR_PROCESS_BASE1  	(BOOTLOADER_HOST_BUFFER_BASE + BOOTLOADER_HOST_BUFFER_SIZE) //0x2218_0000
#define BOOTLOADER_VENDOR_PROCESS_SIZE1  	(DEF_KB(104))

#define BOOTLOADER_RRT_BASE					(0x2219A000)   // related to DBUF_RETRY_RR_TABLE
#define BOOTLOADER_RRT_SIZE					(0x4000) 	   // related to DBUF_RETRY_RR_TABLE_SIZE

#define BOOTLOADER_VENDOR_PROCESS_BASE2  	(BOOTLOADER_RRT_BASE + BOOTLOADER_RRT_SIZE)	// 0x2219E000
#define BOOTLOADER_VENDOR_PROCESS_SIZE2  	(DEF_KB(256) - BOOTLOADER_RRT_SIZE - BOOTLOADER_VENDOR_PROCESS_SIZE1) // vendor process buffer size total 256K
#endif /* BOOTLOADER_EN */

#if (BURNER_MODE_EN || RDT_MODE_EN || UART_VUC_MODE_EN)
#define	BURNER_FREE_BASE						(DBUF_PB_RAM_ADDRESS)
#define	BURNER_FREE_SIZE						(0x4000)

#define	BURNER_BANKING_BASE						(BURNER_FREE_BASE + BURNER_FREE_SIZE)
#define	BURNER_BNKING_SIZE						(0x20000)

#define	BURNER_VENDOR_BUF_BASE					(BURNER_BANKING_BASE+BURNER_BNKING_SIZE)
#define	BURNER_VENDOR_BUFFER_SIZE				(0x40000)

#define	BURNER_HOST_BIN_FILE_BASE				(BURNER_VENDOR_BUF_BASE+BURNER_VENDOR_BUFFER_SIZE)
#define	BURNER_HOST_BIN_FILE_SIZE				(0x100000)

#define	BURNER_HOST_BUFFER_BASE					(BURNER_HOST_BIN_FILE_BASE+BURNER_HOST_BIN_FILE_SIZE)
#define	BURNER_HOST_BUFFER_SIZE					(0x40000)

#define	BURNER_STATIC_AREA_BASE					(BURNER_HOST_BUFFER_BASE+BURNER_HOST_BUFFER_SIZE)
#define	BURNER_STATIC_AREA_SIZE					(0x1C000)


// ISP_Flash use
#define SECURITY_TARGET_SIZE                DEF_KB(1)
#define BURNER_IDPAGE_SIZE		            DEF_KB(4)
#define PROGRAM_BUF_SIZE		            DEF_KB(32)
#define READ_BUF_SIZE			            DEF_KB(32)
#define CODE_POINTER_DATA_SIZE	            DEF_KB(4)

#if BOOTLOADER_EN
#define BURNER_BOOTLOADER_CODESIGN_SIZE     DEF_KB(1)   // If double sign, 512 * 2 = 1024
#define BURNER_FW_CODESIGN_SIZE             DEF_KB(1)   // If double sign, 512 * 2 = 1024
#define BURNER_CODESIGN_TOTAL_SIZE	        (BURNER_BOOTLOADER_CODESIGN_SIZE + BURNER_FW_CODESIGN_SIZE)
#define RSA_KEY_SIZE			            (DEF_KB(1) + DEF_256B)
#define SECURITY_TARGET_BASE	    (BURNER_HOST_BUFFER_BASE)							//0x22164000
#define BURNER_CODESIGN_BASE 	    (SECURITY_TARGET_BASE + SECURITY_TARGET_SIZE)		//0x22164400
#define BURNER_IDPAGE_BASE 		    (BURNER_CODESIGN_BASE + BURNER_CODESIGN_TOTAL_SIZE) //0x22164C00
#define PROGRAM_BUF_BASE		    (BURNER_IDPAGE_BASE + BURNER_IDPAGE_SIZE)			//0x22165C00
#define READ_BUF_BASE			    (PROGRAM_BUF_BASE + PROGRAM_BUF_SIZE)				//0x2216DC00
#define CODE_POINTER_DATA_BASE	    (READ_BUF_BASE + READ_BUF_SIZE)						//0x22175C00
#define BURNER_RSA_KEY_BASE		    (CODE_POINTER_DATA_BASE + CODE_POINTER_DATA_SIZE)	//0x22176C00
#define BURNER_RSA_KEY_NEXT_BASE    (BURNER_RSA_KEY_BASE + RSA_KEY_SIZE)                //0x22177100
#define BURNER_PROGRAM_RRT_BASE		(BURNER_HOST_BUFFER_BASE + DEF_KB(80))
#else /* BOOTLOADER_EN */
#define BURNER_CODESIGN_SIZE	DEF_KB(1)		// If double sign, 512 * 2 = 1024
#define SECURITY_TARGET_BASE	(BURNER_HOST_BUFFER_BASE)							//0x22164000
#define BURNER_CODESIGN_BASE 	(SECURITY_TARGET_BASE + SECURITY_TARGET_SIZE)		//0x22164400
#define BURNER_IDPAGE_BASE 		(BURNER_CODESIGN_BASE + BURNER_CODESIGN_SIZE)		//0x22164800
#define PROGRAM_BUF_BASE		(BURNER_IDPAGE_BASE + BURNER_IDPAGE_SIZE)			//0x22165800
#define READ_BUF_BASE			(PROGRAM_BUF_BASE + PROGRAM_BUF_SIZE)				//0x2216D800
#define CODE_POINTER_DATA_BASE	(READ_BUF_BASE + READ_BUF_SIZE)						//0x22175800
#define BURNER_RSA_KEY_BASE		(CODE_POINTER_DATA_BASE + CODE_POINTER_DATA_SIZE)	//0x22176800
#endif /* BOOTLOADER_EN */
#endif /* (BURNER_MODE_EN || RDT_MODE_EN || UART_VUC_MODE_EN) */

// DBT use
#if (BURNER_MODE_EN || RDT_MODE_EN)
#define DBT_BASE_ADDR						(BURNER_HOST_BIN_FILE_BASE)
#define DBT_TEMP_READ_BUF_ADDR				(BURNER_HOST_BIN_FILE_BASE + DBT_MAX_SIZE_IN_BYTE)
#define DBT_TEMP_READ_BUF_SIZE_IN_4K		(0x20)
#define INFO_BLK_BASE_ADDR					(DBUF_PB_RAM_ADDRESS + (2*SIZE_4KB))   // 4 KB
#define DBT_SCAN_UNIT_NUM					(2)
#define PREFORMAT_HOST_TABLE_BUFFER			(0x22000000)
#define PREFORMAT_HOST_TABLE_BUFFER_SIZE	(FWLB_HOST_TABLE_SIZE_IN_4K * SIZE_4KB)
#define PREFORMAT_TCG_BUF_ADDR				(0x22150000)
#define PREFORMAT_TCG_BUF_SIZE_IN_4K		(0x14)
#if (MST_MODE_EN)//Duson Porting BICS5 Add //Reip Porting 3D-V7 QLC Add//ems mst add--karl//zerio bics6 qlc add
#define OTP_RRT_TMP_SIZE        DEF_KB(20)
#define OTP_MAN_STRUCT_SIZE     DEF_KB(6)
#define HYNIX_SLC_RRT_SIZE      DEF_KB(2)
#define HYNIX_TLC_RRT_SIZE      DEF_KB(12)

#define OTP_RRT_TMP_BASE        (BURNER_HOST_BUFFER_BASE - OTP_MAN_STRUCT_SIZE)
#define OTP_MAN_BASE            (OTP_RRT_TMP_BASE)
#define HYNIX_SLC_RRT_BASE      (OTP_MAN_BASE + OTP_MAN_STRUCT_SIZE)
#define HYNIX_TLC_RRT_BASE      (HYNIX_SLC_RRT_BASE + HYNIX_SLC_RRT_SIZE)
#endif /* (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC) */
#else /* (BURNER_MODE_EN || RDT_MODE_EN) */
#define DBT_BASE_ADDR						(DBUF_PB_RAM_ADDRESS)
#define DBT_TEMP_READ_BUF_ADDR				(DBT_BASE_ADDR + DBT_MAX_SIZE_IN_BYTE)
#define DBT_TEMP_READ_BUF_SIZE_IN_4K		(0x20)
#define INFO_BLK_BASE_ADDR					(DBUF_PB_RAM_ADDRESS + (2*SIZE_4KB))   // 4 KB
#define DBT_SCAN_UNIT_NUM					(2)
#define PREFORMAT_HOST_TABLE_BUFFER			(0x22100000)
#define PREFORMAT_HOST_TABLE_BUFFER_SIZE	(FWLB_HOST_TABLE_SIZE_IN_4K * SIZE_4KB)
#define PREFORMAT_TCG_BUF_ADDR              (0x22150000)
#define PREFORMAT_TCG_BUF_SIZE_IN_4K        (0xFFFFFFFF)
#define BURNER_VENDOR_BUF_BASE				(0x22000000) // temp sol auther MH
#if (FLASH_HYNIXV6_TLC == FW_CATEGORY_FLASH)
#define OTP_RRT_TMP_BASE        			(DBUF_DRIVE_LOG) //temporary use then free
#define OTP_RRT_TMP_SIZE      			  	DEF_KB(20)
#define OTP_MAN_BASE            			(DBUF_DRIVE_LOG)
#define OTP_MAN_STRUCT_SIZE    			 	DEF_KB(6)
#define HYNIX_SLC_RRT_BASE      			(DBUF_RETRYTABLE) //(DBUF_STATIC_BASE_ADDR)
#define HYNIX_SLC_RRT_SIZE     				DEF_KB(2)
#define HYNIX_TLC_RRT_BASE      			(HYNIX_SLC_RRT_BASE+0x800)
#define HYNIX_TLC_RRT_SIZE     				DEF_KB(12)
#endif /* (FLASH_HYNIXV6_TLC == FW_CATEGORY_FLASH) */
#endif /* (BURNER_MODE_EN || RDT_MODE_EN) */

#define DBT_MAX_SIZE_IN_BYTE			(0x80000)

// RUT use
#define PREFORMAT_RUT_LAYER1			(0x1000)
#define PREFORMAT_RUT_LAYER2			(0x10000)
#define RUT_SPARE_TABLE					(0x7800)
#define SCORE_BASE_ADDR                 (DBT_BASE_ADDR + DBT_MAX_SIZE_IN_BYTE)
#define SCORE_SIZE						(0x4000) //4Bytes * 4k Blks = 16k Bytes
#define RUT_ELEMENT_BASE_ADDR    		(SCORE_BASE_ADDR + SCORE_SIZE)
#define RUT_ELEMENT_SIZE                (0x20000) //82Bytes * 4k Blks = 328k Bytes
#define RUT_LAYER_BASE_ADDR				(RUT_ELEMENT_BASE_ADDR + RUT_ELEMENT_SIZE)
#define RUT_LAYER_SIZE					(0x200)
#define PB_SCORE_BASE_ADDR				(RUT_LAYER_BASE_ADDR + RUT_LAYER_SIZE)
#define PB_SCORE_SIZE					(0x1000)
//================================================
#define RUT_LAYER1_ADDR							(PB_SCORE_BASE_ADDR + PB_SCORE_SIZE)   // 4 KB
#define RUT_LAYER2_ADDR							(RUT_LAYER1_ADDR + PREFORMAT_RUT_LAYER1)   // 64 KB, end
#define RUT_SPARE_TABLE_ADDR            		(RUT_LAYER2_ADDR + PREFORMAT_RUT_LAYER2)  // 30KB
#define DBT_RUT_BLOCK_HEADER_ADDR       		(RUT_SPARE_TABLE_ADDR + RUT_SPARE_TABLE)  //4KB
#define DBT_RUT_BLOCK_HEADER_SIZE				(0x1000)
#define READ_VERIFY_BUF							(DBT_RUT_BLOCK_HEADER_ADDR + DBT_RUT_BLOCK_HEADER_SIZE) //16KB read verify when preformat write
#define READ_VERIFY_BUF_SIZE					(0x4000)
#define INFO_BLK_RETRY_TABLE_BASE_ADDR			(READ_VERIFY_BUF + READ_VERIFY_BUF_SIZE)   // 12 KB
#define INFO_BLK_RETRY_TABLE_SIZE				(0x3000)
#define INFO_BLK_SOFTBIT_TABLE_BASE_ADDR		(INFO_BLK_RETRY_TABLE_BASE_ADDR + INFO_BLK_RETRY_TABLE_SIZE)   // 16 KB
#define INFO_BLK_SOFTBIT_TABLE_SIZE             (0x4000)
#define INFO_BLK_UNIT_LIST_BUF					(INFO_BLK_SOFTBIT_TABLE_BASE_ADDR + INFO_BLK_SOFTBIT_TABLE_SIZE)
#define INFO_BLK_UNIT_LIST_BUF_SIZE				(0x4000)
#define INFO_BLK_ERASE_COUNT_BUF				(INFO_BLK_UNIT_LIST_BUF + INFO_BLK_UNIT_LIST_BUF_SIZE)
#define INFO_BLK_RUT_READ_BUF					(INFO_BLK_ERASE_COUNT_BUF + DBUF_ERASE_CNT_SIZE)
#define INFO_BLK_RUT_READ_BUF_SIZE				(0x4000)
#define INFO_BLK_PARITY_MAP_BUF					(INFO_BLK_RUT_READ_BUF + INFO_BLK_RUT_READ_BUF_SIZE)
#define INFO_BLK_PARITY_MAPBUF_SIZE				(0x2000)
#define PREFORMAT_BUILD_SPARE_TABLE_BUF			(INFO_BLK_PARITY_MAP_BUF + INFO_BLK_PARITY_MAPBUF_SIZE)
#define PREFORMAT_BUILD_SPARE_TABLE_BUF_SIZE	(0x8000)

#if VRLC_EN
#define PREFORMAT_4K_ALIGN_RESERVED_BUF_SIZE		(SIZE_4KB - RUT_LAYER_SIZE - 0x800)
#define PREFORMAT_VRLC_TABLE_BUF_ADDR               (PREFORMAT_BUILD_SPARE_TABLE_BUF + PREFORMAT_BUILD_SPARE_TABLE_BUF_SIZE + PREFORMAT_4K_ALIGN_RESERVED_BUF_SIZE)
#define PREFORMAT_VRLC_TABLE_SIZE                   (FWLB_VRLC_TABLE_SIZE_IN_4K * SIZE_4KB)
#define PREFORMAT_VRLC_GET_FEATURE_BUF_ADDR		    (PREFORMAT_VRLC_TABLE_BUF_ADDR + PREFORMAT_VRLC_TABLE_SIZE)
#define PREFORMAT_VRLC_GET_FEATURE_BUF_SIZE		    (MT_RETRY_NUM * SIZE_4KB) // 16 * 4KB
#define PREFORMAT_VRLC_MMO_WRITE_READ_DATA_BUF		(PREFORMAT_VRLC_GET_FEATURE_BUF_ADDR + PREFORMAT_VRLC_GET_FEATURE_BUF_SIZE)
#define PREFORMAT_VRLC_MMO_WRITE_READ_DATA_BUF_SIZE	(PLANE_SIZE)
#define PREFORMAT_VRLC_MMO_RDT_LOG_BUF              (PREFORMAT_VRLC_MMO_WRITE_READ_DATA_BUF + PREFORMAT_VRLC_MMO_WRITE_READ_DATA_BUF_SIZE)
#define PREFORMAT_VRLC_MMO_RDT_LOG_BUF_SIZE         (LUN_NUMBER * PLANE_SIZE)
#endif /* VRLC_EN */

// Scan Dll
#define SCAN_GEN_PATTERN_SIZE	DEF_KB(16)
#if (RDT_MODE_EN)
#define SCAN_PROGRAM_BUF_SIZE	(0x8000)
#define SCAN_READ_BUF_SIZE		(0xC000)
#else /* (RDT_MODE_EN) */
#define SCAN_PROGRAM_BUF_SIZE	DEF_KB(16 << 2)
#define SCAN_READ_BUF_SIZE		DEF_KB(16 << 2)
#endif /* (RDT_MODE_EN) */

#if (BURNER_MODE_EN || RDT_MODE_EN)
#define SCAN_GEN_PATTERN_BASE	(BURNER_HOST_BIN_FILE_BASE)							//0x22064000
#define SCAN_PROGRAM_BUF_BASE	(SCAN_GEN_PATTERN_BASE + SCAN_GEN_PATTERN_SIZE)		//0x22074000
#define SCAN_READ_BUF_BASE		(SCAN_PROGRAM_BUF_BASE + SCAN_PROGRAM_BUF_SIZE)		//0x22084000
#else
#define SCAN_GEN_PATTERN_BASE	(DBUF_PB_RAM_ADDRESS)
#define SCAN_PROGRAM_BUF_BASE	(DBUF_PB_RAM_ADDRESS + 0x10000)
#define SCAN_READ_BUF_BASE		(DBUF_PB_RAM_ADDRESS + 0x20000)
#endif /* (BURNER_MODE_EN || RDT_MODE_EN) */

//*****************************************************
// Boot code memory arrangement
//*****************************************************
//==================Boot code (IPL) use memory ====================
#define	IPL_SYSTEM_INFO_BASE							(DBUF_PB_RAM_ADDRESS)
#define	IPL_SYSTEM_INFO_SIZE							(0x1000)

#define	IPL_CODE_BLOCK_INFO_BASE						(IPL_SYSTEM_INFO_BASE+IPL_SYSTEM_INFO_SIZE)
#define	IPL_CODE_BLOCK_INFO_SIZE						(0x2000)

#define	IPL_CODE_SIGNED_INFO_BASE						(IPL_CODE_BLOCK_INFO_BASE+IPL_CODE_BLOCK_INFO_SIZE)
#define	IPL_CODE_SIGNED_INFO_SIZE						(0x1000)

#define	IPL_FREE_BUFFER_BASE							(IPL_CODE_SIGNED_INFO_BASE+IPL_CODE_SIGNED_INFO_SIZE)
#define	IPL_FREE_BUFFER_SIZE							(0x3C000)

#define	IPL_CODE_FROM_CODE_BLOCK_OR_BURNER_BASE			(IPL_FREE_BUFFER_BASE+IPL_FREE_BUFFER_SIZE)
#define	IPL_CODE_FROM_CODE_BLOCK_OR_BURNER_SIEZE		(0x100000)

#define	IPL_HOST_BUFFER_BASE							(IPL_CODE_FROM_CODE_BLOCK_OR_BURNER_BASE+IPL_CODE_FROM_CODE_BLOCK_OR_BURNER_SIEZE) //S17: 0x22141000
#define	IPL_HOST_BUFFER_SIZE							(0x40000)

#define	IPL_VENDOR_BUFFER_BASE							(IPL_HOST_BUFFER_BASE+IPL_HOST_BUFFER_SIZE) //S17: 0x22181000
#define	IPL_VENDOR_BUFFER_SIZE							(0x40000)

#define	CODESIGN_TYPE_OFFSET							(0x20)
#define	SECTION_INFO_OFFSET								(0x80)
#define	CODESIGN_SIZE									(DEF_512B)

//*****************************************************
#define PMD_BITMAP_SIZE								(SIZE_64B) // Maximum support to 2TB
#define PGD_SIZE									(SIZE_2KB) // Maximum support to 2TB

//****************************************
//		DBUF
//****************************************
// Static
#define DBUF_RETRY_RR_TABLE_SIZE					(RETRY_HB_RRT_IN_DBUF ? 0x4000 : 0) // 16KB
#if (IM_N48R && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER))
#define DBUF_RETRY_LRU_TABLE_SIZE					(RETRY_HB_LRU_IN_DBUF? (0x1000) : (0))
#else /* (IM_N48R && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER)) */
#define DBUF_RETRY_LRU_TABLE_SIZE					(RETRY_HB_LRU_IN_DBUF? (0x3000) : (0))
#endif /* (IM_N48R && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER)) */
#define DBUF_STATIC_UNIT_LIST_SIZE					(0)//13 off
#if DRIVE_LOG_HIGH_RIORITY_EVENT_EN
#define DBUF_DRIVE_LOG_SIZE							(0x3000)	// 12KB
#define DBUF_DRIVE_LOG_SIZE_HIGH					(0x1000)	// 4KB
#define DBUF_DRIVE_LOG_SIZE_BEFORE_INIT_DONE		(0xC00)		// 3KB
#define DBUF_DRIVE_LOG_SIZE_BEFORE_INIT_DONE_HIGH	(0x400)		// 1KB
#else /*DRIVE_LOG_HIGH_RIORITY_EVENT_EN*/
#define DBUF_DRIVE_LOG_SIZE							(0x4000)	// 16KB
#define DBUF_DRIVE_LOG_SIZE_BEFORE_INIT_DONE		(0x1000)	// 4KB
#endif /*DRIVE_LOG_HIGH_RIORITY_EVENT_EN*/
#define DBUF_TOTAL_DRIVE_LOG_SIZE					(0x4000)	// 16KB
#define DBUF_TOTAL_DRIVE_LOG_SIZE_BEFORE_INIT_DONE	(0x1000)	// 4KB
#define DBUF_RETRY_2ND_BUF_BEFORE_INIT_DONE_SIZE	(0x3000)	// 12KB
#define DBUF_VBRMP_SIZE								(0x2000)	// 8KB
#define DBUF_RUT_SIZE								(0x8000)	// 32KB
#define	DBUF_HB_RETRY_SIZE							(0x1000)	// 4KB
#define DBUF_ERASE_CNT_SIZE							(0x4000)	// 16KB
#define DBUF_PTE_BITMAP_SIZE						(0x4000)	// 16KB
#define DBUF_VTDBUF_SIZE							(0x3000)	// 12KB
#define DBUF_GR_XZIP_FIRST_HIT_TABLE_SIZE			(0x3000)	// 12KB
#define DBUF_CMD_LIFETIME_SIZE						(0x1000)	// 4096B
#define DBUF_PGD_SIZE								(PGD_SIZE)	// 4KB, S17 4TB should set 4K
#define DBUF_PMD_BITMAP_BACKUP_SIZE					(PMD_BITMAP_SIZE)	// 128B
#define DBUF_PMD_BITMAP_SIZE						(PMD_BITMAP_SIZE)	// 128B
#define DBUF_TABLEGC_SOURCE_PMD_BMP_SIZE			(PMD_BITMAP_SIZE)	// 128B
#define DBUF_VC_ZERO_UNIT_SEARCH_RESULT_SIZE		(0x0020)	// 32B
#define	DBUF_VC_GC_SOURCE_SEARCH_RESULT_SIZE		(0x0020)	// 32B
#define DBUF_LPM3_BTCM_BACKUP_SIZE					(0x0200)	// 512B
#define DBUF_TRIM_PMD_BITMAP_SIZE					(PMD_BITMAP_SIZE)	// 128B
#define DBUF_RS_SPARE_DATA_BACKUP_SIZE				(0x0180)	// 384B
#if (ONE_CE_EN && (FW_CATEGORY_FLASH == FLASH_BICS5TLC))
#define DBUF_RS_SPARE_SIZE							(0x0780)	// 1920B
#define DBUF_NO_USE_3_SIZE                          (0)    		// 0B
#else /*(ONE_CE_EN && (FW_CATEGORY_FLASH == FLASH_BICS5TLC))*/
#define DBUF_RS_SPARE_SIZE							(0x0500)	// 1280B
#define DBUF_NO_USE_3_SIZE                          (0x0280)    // 640B
#endif /*(ONE_CE_EN && (FW_CATEGORY_FLASH == FLASH_BICS5TLC))*/
#define DBUF_WORDLINE_FOLDING_TABLE_SIZE            (0x0860)    // 2144B
#define DBUF_SYSTEM_AREA_HOST_INIT_SIZE				(0x0260)	// 608B
#define DBUF_MIN_EC_SEARCH_RESULT_SIZE				(0x0040)	// 64B
#define DBUF_MAX_READ_CNT_UNIT_SEARCH_RESULT_SIZE	(0x0040)	// 64B
#define DBUF_NO_USE_SIZE							(0x0080)	// 128B
#define DBUF_TEMP_INIT_FREE_UNIT_CHECK_SIZE			(0x0200)	// 512B

#define DBUF_SIZE									(DBUF_PB_RAM_SIZE)					// 1792KB
#define	DBUF_4K_NUM									(DBUF_SIZE >> 12)					// DBUF_SIZE/4096
#define DBUF_STATIC_SIZE							(0x1F000 + DBUF_RETRY_LRU_TABLE_SIZE + DBUF_RETRY_RR_TABLE_SIZE)	// 124KB+LRU_Table_InDUBF+RRT_InDBUF
#define DBUF_STATIC_4K_NUM							(DBUF_STATIC_SIZE >> 12)			// DBUF_STATIC_SIZE/4096
#define DBUF_VBRMP_RUTL2_SIZE						(DBUF_VBRMP_SIZE + DBUF_RUT_SIZE)	// VBRMP (8KB) + RUT layer2 (32KB)
#define DBUF_VBRMP_RUTL2_4K_NUM						(DBUF_VBRMP_RUTL2_SIZE >> 12)		// DBUF_VBRMP_RUTL2_SIZE/4096
#define DBUF_BMU_PB_SIZE							(DBUF_SIZE - DBUF_STATIC_SIZE)		// BMU 1668KB + static 124KB = 1792KB
#define DBUF_BMU_PB_4K_NUM							(DBUF_BMU_PB_SIZE >> 12)			// DBUF_BMU_PB_SIZE/4096
#define DBUF_STATIC_OFFSET							(DBUF_BMU_PB_SIZE)
#define DBUF_VBRMP_RUTL2_OFFSET						(DBUF_STATIC_OFFSET + DBUF_STATIC_SIZE)

#define DBUF_BASE_ADDR 								(DBUF_PB_RAM_ADDRESS)
#define DBUF_STATIC_BASE_ADDR						(DBUF_BASE_ADDR + DBUF_STATIC_OFFSET)

#define DBUF_MICRON_WORDLINE_BYPASS_INFO_OFFSET		(0x2000)
#define DBUF_READ_DISTURB_PRDH_INFO_OFFSET			(0x3000)

/*************************************************************************************************************
 *          New Memory Map For Static DBUF
 *
 *          DCache Start Address : 0x221B0000
 *
 *************************************************************************************************************/
#define DBUF_STATIC_UNIT_LIST							(DBUF_STATIC_BASE_ADDR)
#define DBUF_RETRY_RR_TABLE								(DBUF_STATIC_BASE_ADDR)
#define DBUF_RETRY_LRU_TABLE							(DBUF_RETRY_RR_TABLE + DBUF_RETRY_RR_TABLE_SIZE)
#define DBUF_DRIVE_LOG									(DBUF_RETRY_LRU_TABLE + DBUF_RETRY_LRU_TABLE_SIZE)											// 16K			(0x221A0000)
#if DRIVE_LOG_HIGH_RIORITY_EVENT_EN
#define DBUF_DRIVE_LOG_HIGH							(DBUF_DRIVE_LOG + DBUF_DRIVE_LOG_SIZE)            				 			//4k
#endif /*DRIVE_LOG_HIGH_RIORITY_EVENT_EN*/

// Burner does not need drive log, use this dbuf area save burner error PCA
#define BURNER_ERROR_PCA_RECORD_BASE					(DBUF_DRIVE_LOG) // this define only use when doing preformat
#if DRIVE_LOG_HIGH_RIORITY_EVENT_EN
#define BURNER_ERROR_PCA_RECORD_SIZE	(DBUF_DRIVE_LOG_SIZE + DBUF_DRIVE_LOG_SIZE_HIGH)
#else /*DRIVE_LOG_HIGH_RIORITY_EVENT_EN*/
#define BURNER_ERROR_PCA_RECORD_SIZE					(DBUF_DRIVE_LOG_SIZE) // this define only use when doing preformat
#endif /*DRIVE_LOG_HIGH_RIORITY_EVENT_EN*/

#if DRIVE_LOG_HIGH_RIORITY_EVENT_EN
#define DBUF_VBRMP                          		(DBUF_DRIVE_LOG + DBUF_DRIVE_LOG_SIZE + DBUF_DRIVE_LOG_SIZE_HIGH)	 	 	 									// 8K       (0x221A4000)
#else /*DRIVE_LOG_HIGH_RIORITY_EVENT_EN*/
#define DBUF_VBRMP                          		(DBUF_DRIVE_LOG + DBUF_DRIVE_LOG_SIZE)	 	 	 									// 8K       (0x221A5000)
#endif /*DRIVE_LOG_HIGH_RIORITY_EVENT_EN*/
#if (RECORD_FLASH_TEMPERATURE_EN)
#define DBUF_MAG_TEMPERATURE_SIZE                   (TOTAL_BLOCK_NUM + ((SYSTEM_AREA_SYSTEM_BLK_NUM + SYSTEM_AREA_DBT_BLK_NUM + (E17_ALLIGN_E13_MAX_CH * CODE_BLK_NUM_PER_CH) + (E17_ALLIGN_E13_MAX_CH * CODE_BLK_NUM_PER_CH)) >> RECORD_FLASH_TEMPERATURE_UNIT_NUM_PER_BYTE_LOG) + 1)//unit + systemareablk + vtmother
#define DBUF_MAG_TEMPERATURE						(DBUF_VBRMP + DBUF_VBRMP_SIZE - DBUF_MAG_TEMPERATURE_SIZE)													//828byte       (With DBUF_DRIVE_LOG_SIZE_HIGH: 0x221A5CC4. Without DBUF_DRIVE_LOG_SIZE_HIGH: 0x221A6CC4.)											
#endif /* (RECORD_FLASH_TEMPERATURE_EN) */

#define DBUF_DRIVE_LOG_BEFORE_INIT_DONE_PB_OFFSET		(0xFF)
#define DBUF_HMB_DRIVE_LOG_BACKUP_PB_OFFSET				(0x100)
#define DBUF_RETRY_2ND_BUF_BEFORE_INIT_DONE_PB_OFFSET	(0x104)
#define DBUF_DRIVE_LOG_BEFORE_INIT_DONE					(DBUF_BASE_ADDR + (DBUF_DRIVE_LOG_BEFORE_INIT_DONE_PB_OFFSET * SIZE_4KB))			// 4K	PB addr 0x0FF, (0x220FF000)
#define DBUF_HMB_DRIVE_LOG_BACKUP						(DBUF_DRIVE_LOG_BEFORE_INIT_DONE + DBUF_TOTAL_DRIVE_LOG_SIZE_BEFORE_INIT_DONE)		// 16K	PB addr 0x100, (0x22100000)
#define DBUF_RETRY_2ND_BUF_BEFORE_INIT_DONE				(DBUF_HMB_DRIVE_LOG_BACKUP + DBUF_TOTAL_DRIVE_LOG_SIZE)								// 12K	PB addr 0x104, (0x22104000)
#define DBUF_RUT										(DBUF_VBRMP + DBUF_VBRMP_SIZE)														// 32K		S17: (0x221A6000) , E13: (0x221A7000)
#define DBUF_HB_RETRY									(DBUF_RUT + DBUF_RUT_SIZE)															// 4K		S17: (0x221AE000) , E13: (0x221AF000)
#define DBUF_ERASE_CNT									(DBUF_HB_RETRY + DBUF_HB_RETRY_SIZE)												// 16K		S17: (0x221AF000) , E13: (0x221B0000)
#define DBUF_MICRON_WORDLINE_BYPASS_INFO				(DBUF_ERASE_CNT + DBUF_MICRON_WORDLINE_BYPASS_INFO_OFFSET)							// 8K		S17: (0x221B1000) , E13: (0x221B2000)
#define DBUF_READ_DISTURB_PRDH_INFO						(DBUF_ERASE_CNT + DBUF_READ_DISTURB_PRDH_INFO_OFFSET)

#define DBUF_DCACHE_START_ADDR						(0x221B4000)
/************************************** DCache Start **********************************************************/
#define DBUF_PTE_BITMAP								(DBUF_ERASE_CNT + DBUF_ERASE_CNT_SIZE)												// 16K		S17: (0x221B3000) , E13: (0x221B4000)
#define DBUF_VTDBUF									(DBUF_PTE_BITMAP + DBUF_PTE_BITMAP_SIZE)											// 12K		S17: (0x221B7000) , E13: (0x221B8000)
#define DBUF_GR_XZIP_FIRST_HIT_TABLE				(DBUF_VTDBUF + DBUF_VTDBUF_SIZE)													// 12K		S17: (0x221BA000) , E13: (0x221BB000)
#define DBUF_CMD_LIFETIME                           (DBUF_GR_XZIP_FIRST_HIT_TABLE + (DBUF_GR_XZIP_FIRST_HIT_TABLE_SIZE - DBUF_CMD_LIFETIME_SIZE))
#define DBUF_PGD									(DBUF_GR_XZIP_FIRST_HIT_TABLE + DBUF_GR_XZIP_FIRST_HIT_TABLE_SIZE)					// 2K		S17: (0x221BD000) , E13: (0x221BE000)	// Before Unlock PMD(COP1) and Program PGD(COP0) must align 4KB
#define DBUF_PMD_BITMAP_BACKUP						(DBUF_PGD + DBUF_PGD_SIZE) 															// 64B		S17: (0x221BE000) , E13: (0x221BE800)	// need after PGD. shift 4KB can't over DCACHE zone
#define DBUF_PMD_BITMAP								(DBUF_PMD_BITMAP_BACKUP + DBUF_PMD_BITMAP_BACKUP_SIZE)								// 64B		S17: (0x221BE080) , E13: (0x221BE840)
#define DBUF_TABLEGC_SOURCE_PMD_BMP					(DBUF_PMD_BITMAP + DBUF_PMD_BITMAP_SIZE)											// 64B		S17: (0x221BE100) , E13: (0x221BE880)	// must align 32Byte
#define DBUF_VC_ZERO_UNIT_SEARCH_RESULT				(DBUF_TABLEGC_SOURCE_PMD_BMP + DBUF_TABLEGC_SOURCE_PMD_BMP_SIZE)					// 32B		S17: (0x221BE180) , E13: (0x221BE8C0)	// must align 32Byte
#define	DBUF_VC_GC_SOURCE_SEARCH_RESULT				(DBUF_VC_ZERO_UNIT_SEARCH_RESULT + DBUF_VC_ZERO_UNIT_SEARCH_RESULT_SIZE)			// 32B		S17: (0x221BE1A0) , E13: (0x221BE8E0)	// must align 32Byte
#define	DBUF_LPM3_BTCM_BACKUP						(DBUF_VC_GC_SOURCE_SEARCH_RESULT + DBUF_VC_GC_SOURCE_SEARCH_RESULT_SIZE)			// 512B		S17: (0x221BE1C0) , E13: (0x221BE900)
#define	DBUF_TRIM_PMD_BITMAP						(DBUF_LPM3_BTCM_BACKUP + DBUF_LPM3_BTCM_BACKUP_SIZE)								// 64B		S17: (0x221BE3C0) , E13: (0x221BEB00)
#define DBUF_RS_SPARE_DATA_BACKUP					(DBUF_TRIM_PMD_BITMAP + DBUF_TRIM_PMD_BITMAP_SIZE)									// 384B		S17: (0x221BE440) , E13: (0x221BEB40)
#define DBUF_RS_SPARE								(DBUF_RS_SPARE_DATA_BACKUP + DBUF_RS_SPARE_DATA_BACKUP_SIZE)						// 1280B	S17: (0x221BE5C0) , E13: (0x221BECC0)	// must align 32Byte
#define DBUF_NO_USE_3                               (DBUF_RS_SPARE + DBUF_RS_SPARE_SIZE)
#define DBUF_WORDLINE_FOLDING_TABLE                 (DBUF_NO_USE_3 + DBUF_NO_USE_3_SIZE)

#define DBUF_WORDLINE_FOLDING_REMAPPING_TABLE_1   (DBUF_WORDLINE_FOLDING_TABLE)
#define DBUF_WORDLINE_FOLDING_REMAPPING_TABLE_SIZE (0x2C8) // 712bytes
#define DBUF_WORDLINE_FOLDING_REMAPPING_TABLE_2   (DBUF_WORDLINE_FOLDING_REMAPPING_TABLE_1 + DBUF_WORDLINE_FOLDING_REMAPPING_TABLE_SIZE)
#define DBUF_WORDLINE_FOLDING_REMAPPING_SRC_GLOBAL1			(DBUF_WORDLINE_FOLDING_REMAPPING_TABLE_2 + DBUF_WORDLINE_FOLDING_REMAPPING_TABLE_SIZE)
#define DBUF_WORDLINE_FOLDING_REMAPPING_SRC_GLOBAL2			(DBUF_WORDLINE_FOLDING_REMAPPING_SRC_GLOBAL1 + DBUF_WORDLINE_FOLDING_REMAPPING_SRC_GLOBAL_SIZE)
#define DBUF_WORDLINE_FOLDING_REMAPPING_SRC_GLOBAL_SIZE		(0x162)
#define DBUF_WORDLINE_FOLDING_REMAPPING_TARGET_GLOBAL1		(DBUF_WORDLINE_FOLDING_REMAPPING_SRC_GLOBAL2 + DBUF_WORDLINE_FOLDING_REMAPPING_SRC_GLOBAL_SIZE)
#define DBUF_WORDLINE_FOLDING_REMAPPING_TARGET_GLOBAL2		(DBUF_WORDLINE_FOLDING_REMAPPING_TARGET_GLOBAL1 + DBUF_WORDLINE_FOLDING_REMAPPING_TARGET_GLOBAL_SIZE)
#define DBUF_WORDLINE_FOLDING_REMAPPING_TARGET_GLOBAL_SIZE	(0x04)
#define	DBUF_WORDLINE_FOLDING_TOTAL_TABLE_SIZE				((DBUF_WORDLINE_FOLDING_REMAPPING_TABLE_SIZE + DBUF_WORDLINE_FOLDING_REMAPPING_SRC_GLOBAL_SIZE + DBUF_WORDLINE_FOLDING_REMAPPING_TARGET_GLOBAL_SIZE) * WORDLINE_FOLDING_TABLE_NUM)

#define DBUF_PHYSETTING_BEFORE_INIT_DONE			(DBUF_RS_SPARE_DATA_BACKUP)
#define DBUF_SYSTEM_AREA_HOST_INIT					(DBUF_WORDLINE_FOLDING_TABLE + DBUF_WORDLINE_FOLDING_TABLE_SIZE)                                                // 608B		S17: (0x221BF460) , E13: (0x221BFB60)
#define DBUF_MIN_EC_SEARCH_RESULT					(DBUF_SYSTEM_AREA_HOST_INIT + DBUF_SYSTEM_AREA_HOST_INIT_SIZE)						// 64B		S17: (0x221BF6C0) , E13: (0x221BFDC0)
#define DBUF_MAX_READ_CNT_UNIT_SEARCH_RESULT		(DBUF_MIN_EC_SEARCH_RESULT + DBUF_MIN_EC_SEARCH_RESULT_SIZE)						// 64B		S17: (0x221BF700) , E13: (0x221BFE00)
#define DBUF_NO_USE									(DBUF_MAX_READ_CNT_UNIT_SEARCH_RESULT + DBUF_MAX_READ_CNT_UNIT_SEARCH_RESULT_SIZE)	// 448B		S17: (0x221BF740) , E13: (0x221BFE40)
#define DBUF_TEMP_INIT_FREE_UNIT_CHECK				(DBUF_MIN_EC_SEARCH_RESULT + DBUF_MIN_EC_SEARCH_RESULT_SIZE)						// 512B		S17: (0x221BF600) , E13: (0x221BFF00)	// 和DBUF_NO_USE,DBUF_MAX_READ_CNT_UNIT_SEARCH_RESULT不可同時存在, initial debug用

#define DBUF_FW_SECTION_INFO_ADDRESS                (DBUF_LPM3_BTCM_BACKUP)
#define DBUF_FW_SECTION_SIZE                        (0x28)
#define DBUF_FW_SLOT_ADDRESS                        (DBUF_FW_SECTION_INFO_ADDRESS + DBUF_FW_SECTION_SIZE)
#define DBUF_FW_SLOT_SIZE                           (0x70)
#define DBUF_RTT0_VALUE_RECORD_ADDR                 (DBUF_FW_SLOT_ADDRESS + DBUF_FW_SLOT_SIZE)
#define DBUF_RTT0_VALUE_RECORD_SIZE                 (0x08)
#define DBUF_OPERATION_TIME_RECORD_ADDR             (DBUF_RTT0_VALUE_RECORD_ADDR + DBUF_RTT0_VALUE_RECORD_SIZE)
#define DBUF_OPERATION_TIME_RECORD_SIZE             (0x08)
#define DBUF_ERROR_LOG_BACKUP_ADDR					(DBUF_OPERATION_TIME_RECORD_ADDR + DBUF_OPERATION_TIME_RECORD_SIZE)
#define DBUF_ERROR_LOG_BACKUP_SIZE					(0x40)
#define DBUF_SATA_IDENTIFY_TABLE_BACKUP_ADDR		(DBUF_ERROR_LOG_BACKUP_ADDR + DBUF_ERROR_LOG_BACKUP_SIZE)
#define DBUF_SATA_IDENTIFY_TABLE_BACKUP_SIZE		(0x20)
#define DBUF_FW_FLH_ENV_ADDRESS						(DBUF_SATA_IDENTIFY_TABLE_BACKUP_ADDR + DBUF_SATA_IDENTIFY_TABLE_BACKUP_SIZE)
#define DBUF_FW_FLH_ENV_SIZE                        (0x80) //S17 : 132B  //CY 20191021 0x80 to 0x84, ubCHNumberInCE[4]->[8] , E13: 124B
#define DBUF_FW_VUC_PROTECT_SESSION_KEY_ADDR		(DBUF_FW_FLH_ENV_ADDRESS + DBUF_FW_FLH_ENV_SIZE)
#define DBUF_FW_VUC_PROTECT_SESSION_KEY_SIZE		(0x20)
#define DBUF_FW_FLH_ENV_MP_ADDRESS					(DBUF_FW_VUC_PROTECT_SESSION_KEY_ADDR + DBUF_FW_VUC_PROTECT_SESSION_KEY_SIZE)
#define DBUF_FW_FLH_ENV_MP_SIZE						(0x20)
#define DBUF_SET_DRIVING_FAIL_CNT_ADDRESS			(DBUF_FW_FLH_ENV_MP_ADDRESS + DBUF_FW_FLH_ENV_MP_SIZE)
#define DBUF_SET_DRIVING_FAIL_CNT_SIZE				(0x01)
#define DBUF_SET_ODT_FAIL_CNT_ADDRESS				(DBUF_SET_DRIVING_FAIL_CNT_ADDRESS + DBUF_SET_DRIVING_FAIL_CNT_SIZE)
#define DBUF_SET_ODT_FAIL_CNT_SIZE					(0x01)
#define DBUF_FC_RETRY_FAIL_CNT_ADDRESS				(DBUF_SET_ODT_FAIL_CNT_ADDRESS + DBUF_SET_ODT_FAIL_CNT_SIZE)
#define DBUF_FC_RETRY_FAIL_CNT_SIZE					(0x01)
#define DBUF_FC_RETRY_CNT_ADDRESS					(DBUF_FC_RETRY_FAIL_CNT_ADDRESS + DBUF_FC_RETRY_FAIL_CNT_SIZE)
#define DBUF_FC_RETRY_CNT_SIZE						(0x01)
#define DBUF_LPM_SLC_POOL_NUM_ADDRESS				(DBUF_FC_RETRY_CNT_ADDRESS + DBUF_FC_RETRY_CNT_SIZE)
#define DBUF_LPM_SLC_POOL_NUM_SIZE					(0x04)
#define DBUF_LPM_EXTRA_BACKUP_FLAG_ADDRESS			(DBUF_LPM_SLC_POOL_NUM_ADDRESS + DBUF_LPM_SLC_POOL_NUM_SIZE)
#define DBUF_LPM_EXTRA_BACKUP_FLAG_SIZE				(0x01)
#define DBUF_LPM_FW_BUFFER_SEQUENCE_ADDRESS			(DBUF_LPM_EXTRA_BACKUP_FLAG_ADDRESS + DBUF_LPM_EXTRA_BACKUP_FLAG_SIZE)
#define DBUF_LPM_FW_BUFFER_SEQUENCE_SIZE			(0x04)
#define DBUF_LPM_SYSTEM_AREA_LPM_RECORD_ADDR		(DBUF_LPM_FW_BUFFER_SEQUENCE_ADDRESS + DBUF_LPM_FW_BUFFER_SEQUENCE_SIZE)
#define DBUF_LPM_SYSTEM_AREA_LPM_RECORD_SIZE		(0x10)
#define DBUF_LPM_PS4_ENTRY_LATENCY_ADDR		        (DBUF_LPM_SYSTEM_AREA_LPM_RECORD_ADDR + DBUF_LPM_SYSTEM_AREA_LPM_RECORD_SIZE)
#define DBUF_LPM_PS4_ENTRY_LATENCY_SIZE				(0x2)
#define DBUF_LPM_HOST_INTERRUPT_ADDR		        (DBUF_LPM_PS4_ENTRY_LATENCY_ADDR + DBUF_LPM_PS4_ENTRY_LATENCY_SIZE)
#define DBUF_LPM_HOST_INTERRUPT_SIZE				(0x4)
// 																																		// end		(0x221C0000)
/************************************** DCache End **********************************************************/

// ---------------------------------------------------------------------------
// IRAM, Unit: bytes
// ---------------------------------------------------------------------------

/***************************
 * IRAM Re-arrangement *
 ***************************/
#define MT_PARA_LEN									(40)
#define MTQ_TOTAL_CNT								(64)
#define MT_RETRY_START_INDEX						(MTQ_TOTAL_CNT)
#define MT_RETRY_NUM		 						(16)	// Reserve for read retry & patch cmd & program fail handle
#define MT_CODE_BANK_START_INDEX					(MT_RETRY_START_INDEX + MT_RETRY_NUM)
#define MT_CODE_BANK_NUM							(3)
#define MT_REQUIRE_NUM_RETRY						(2)
#define MT_REQUIRE_NUM_CODE_BANK_STALL				(1)
#define MTQ_QOS_TOTAL_CNT							(64 - MT_CODE_BANK_NUM)
#define MT_FREEPOOL_CODE_BANK_MT_START_INDEX		(MT_RETRY_NUM) // AOM MT be placed behind FW MT 
#define MT_FREEPOOL_CODE_BANK_MT_END_INDEX			((MT_RETRY_NUM + MT_CODE_BANK_NUM) - 1) // MT FreePool Total Length - 1

#define IRAM_AXI_BASE								(0x00290000)
#define IRAM_SYS_BASE								(IRAM_AXI_BASE)

#define IRAM_BASE									(IRAM_AXI_BASE)
#define IRAM_SIZE									(0x10000)

// IRAM0 layout.
// FRAM (8K), IRAM0(8K), IRAM1(48KB)!AFRAM is only for FPU!C
#define FRAM_OFFSET									(0x0000)
#define FRAM_SIZE									(0x2000)  // 8K

#define FPU_OFF										(FRAM_OFFSET)
#define FPU_LEN										(7104)  // 7K - 64B

#define FPU_NCS_OFF									(FPU_OFF + FPU_LEN)
#define FPU_NCS_LEN									(64)	//64B

#define FPU_SEC_KEY_INFO_OFFSET						(FPU_NCS_OFF + FPU_NCS_LEN)
#define FPU_SEC_KEY_INFO_LEN						(1024)  // 1K

//Support RUT Layer 1 in IRAM0
#define FRAM_BOUND									(FPU_NCS_OFF + FPU_NCS_LEN)
#if (FRAM_BOUND > (FRAM_OFFSET + FRAM_SIZE))
#error "IRAM0 overflow!"
#endif  /*FRAM_BOUND > (FRAM_OFFSET + FRAM_SIZE)*/

#define IRAM0_OFF									(0x2000)
#define IRAM0_SIZE									(0x2000)

#if (FPGA_RTL_2CH == TRUE)
#define CONFIG_NUM_BUS								(2)	//MAX Channel
#else /*(FPGA_RTL_2CH == TRUE)*/
#define CONFIG_NUM_BUS								(4)	//MAX Channel
#endif /*(FPGA_RTL_2CH == TRUE)*/

#define MT_OFF										(IRAM0_OFF) // 0x292000
#define MT_LEN										(MT_PARA_LEN * MTQ_TOTAL_CNT)       // CH0/CH1 separate.

#define MT_RSV_OFF									(MT_OFF + MT_LEN) // 0x292A00
#define MT_RSV_LEN									(MT_PARA_LEN * (MT_RETRY_NUM + MT_CODE_BANK_NUM))

#define MT_QOS_OFF									(MT_RSV_OFF + MT_RSV_LEN) // 0x292C80
#define MT_QOS_LEN									(MT_PARA_LEN * MTQ_QOS_TOTAL_CNT)

#define SPARE_RESERVE_OFFSET						(MT_QOS_OFF + MT_QOS_LEN) // 0x293680
#define SPAER_RESERVE_LENGTH                 		(128)  // 128B, for below L4K spare offset alignment

// L4K for Code Banking from Flash
#define SPR_CODE_BANK_OFF							(SPARE_RESERVE_OFFSET + SPAER_RESERVE_LENGTH) // 0x293700
#define SPR_CODE_BANK_LEN                 			(256)  // 16B * (32K / 4K) = 16B * 8 = 128B, Need align 256B

// L4K for retry spare backup, and assign to retry DMA MT
#define SPR_RETRY_OFF								(SPR_CODE_BANK_OFF + SPR_CODE_BANK_LEN) // 0x293800
#define SPR_RETRY_TOTAL_LEN							(256)  // Maximun backup length, 16B * 16 L4K = 256B, Need align 256B

// L4K for Error handle Copy Block from Flash
#define SPARE_COPY_BLK_OFFSET						(SPR_RETRY_OFF + SPR_RETRY_TOTAL_LEN) // 0x293900
#define SPARE_COPY_BLK_LENGTH                 		(256)  // 16B * 1MT * 4L4K = 64B, Need align 256B

//Support RUT Layer 1 in IRAM1
#define RUT_L1_OFF									(SPARE_COPY_BLK_OFFSET + SPARE_COPY_BLK_LENGTH) // 0x293A00
#define RUT_L1_LEN									(512)
// L2 is not belong to IRAM
#define RUT_L2_LEN									(32 * 1024)	// Byte
#define RUT_L2_4K_NUM								(RUT_L2_LEN / 4096)		// 4K number
#define RUT_L2_ENTRY_NUM							(RUT_L2_LEN/DEF_4B)		//4 byte number


// Move FPU to FRAM
#define IRAM0_BOUND									(RUT_L1_OFF + RUT_L1_LEN)

#if (IRAM0_BOUND > (IRAM0_OFF + IRAM0_SIZE))
#error "IRAM0 overflow!"
#endif /* (IRAM0_BOUND > (IRAM0_OFF + IRAM0_SIZE)) */

// IRAM1 layout.
#define IRAM1_OFF									(0x4000)
#define IRAM1_SIZE									(0xC000)  // 48KB

#if COP0_MODE_EN

#define SPR_OFF										(IRAM1_OFF) // 0x294000
#define SPR_BYTE_CNT								(16)	// unit: byte
#define SPR_CNT										(MTQ_TOTAL_CNT * 16)  // In 16K flash page size, Each MT has 16 L4Ks at most
#define SPR_TOTAL_LEN								(SPR_BYTE_CNT * SPR_CNT)  // 16KB

#define SPR_QOS_OFF									(SPR_OFF + SPR_TOTAL_LEN) // 0x298000
#define SPR_QOS_CNT									(MTQ_QOS_TOTAL_CNT * 16)  // In 16K flash page size, Each MT has 16 L4Ks at most
#define SPR_QOS_TOTAL_LEN							(SPR_BYTE_CNT * SPR_QOS_CNT) // 16KB

#define CONV_IDX_OFF								(SPR_OFF + SPR_TOTAL_LEN)		//0x298000
#define CONV_IDX_CNT								(8192)
#define CONV_IDX_LEN								(2)
#define CONV_IDX_TOTAL_LEN							(CONV_IDX_CNT * CONV_IDX_LEN)	// 16KB

#define LLR_TBL_CNT									(4)

#define GC_BACKUP_BYTE_CNT							(16)

#define TEMP_SPR_FOR_SB_RS_OFF                      (CONV_IDX_OFF + CONV_IDX_TOTAL_LEN) // 0x29C000
#define TEMP_SPR_FOR_SB_RS_LEN                      (256)  //Reseve 64 *2 for SBRAID normal page error, Need align 256B

#define GC_BACKUP_RETRY_OFF							(TEMP_SPR_FOR_SB_RS_OFF + TEMP_SPR_FOR_SB_RS_LEN) // 0x29C100
#define GC_BACKUP_RETRY_LEN							(GC_BACKUP_BYTE_CNT * 16) // 256B

#define GC_READ_P4K_OFF								(GC_BACKUP_RETRY_OFF + GC_BACKUP_RETRY_LEN) // 0x29C200
#define GC_READ_P4K_LEN								(96 * GC_BACKUP_BYTE_CNT) // 1536B, Max FWLB_GC_COPY_BUF_MAX_SIZE_IN_4K is 96

#define GC_PTEBMP_FWSET_OFFSET						(GC_READ_P4K_OFF + GC_READ_P4K_LEN) // 0x29C800
#define GC_PTEBMP_FWSET_LENGTH						(32 * GC_BACKUP_BYTE_CNT) // 512B, GC_SRC_VB_NUM * FTL_GC_PTEBMP_GROUP_NUM is 32

#define GC_BACKUP_POR_INIT_OFF						(GC_PTEBMP_FWSET_OFFSET + GC_PTEBMP_FWSET_LENGTH) // 0x29CA00
#define GC_BACKUP_POR_INIT_LEN						(2048)  // 2KB

#define GC_BACKUP_COPY_UNIT_OFF     				(GC_BACKUP_POR_INIT_OFF + GC_BACKUP_POR_INIT_LEN) // 0x29D200
#define GC_BACKUP_COPY_UNIT_LEN     				(1536)  // 1.5KB

#define GC_COPY_UNIT_BUF_ENLARGE_OFF				(GC_BACKUP_COPY_UNIT_OFF + GC_BACKUP_COPY_UNIT_LEN) // 0x29D800
#define GC_COPY_UNIT_BUF_ENLARGE_LEN 				(512)
//為了讓COPY_UNIT_BUF_ENLARGE用連續4K的IRAM GC_BACKUP_POR_INIT_LEN + GC_BACKUP_COPY_UNIT_LEN + GC_COPY_UNIT_BUF_ENLARGE_LEN

#define GC_BACKUP_COPY_BLK_OFF						(GC_COPY_UNIT_BUF_ENLARGE_OFF + GC_COPY_UNIT_BUF_ENLARGE_LEN) // 0x29DA00
#define GC_BACKUP_COPY_BLK_LEN						(GC_BACKUP_BYTE_CNT * 16) // 暫用 256B //(GC_BACKUP_SIZE * 4 * 4)	// 4 p4k * 4 (QLC)

#define GC_BACKUP_OPEN_BLOCK_RSMAP_SPARE_OFF        (GC_BACKUP_COPY_BLK_OFF + GC_BACKUP_COPY_BLK_LEN) // 0x29DB00
#if (MULTI_PLANE_PROTECTION)
#define GC_BACKUP_OPEN_BLOCK_RSMAP_SPARE_LENGTH     (64 * 6) // 384B
#else /* MULTI_PLANE_PROTECTION */
#define GC_BACKUP_OPEN_BLOCK_RSMAP_SPARE_LENGTH     (64 * 3) // 192B
#endif /* MULTI_PLANE_PROTECTION */
#define GC_BACKUP_READ_N4K_OFF                      (GC_BACKUP_OPEN_BLOCK_RSMAP_SPARE_OFF + GC_BACKUP_OPEN_BLOCK_RSMAP_SPARE_LENGTH) // 0x29DBC0
#define GC_BACKUP_READ_N4K_LEN						(64)

#define GC_BACKUP_RETRY_SKIP_DMA_OFF            	(GC_BACKUP_READ_N4K_OFF + GC_BACKUP_READ_N4K_LEN) // 0x29DC00
#define GC_BACKUP_RETRY_SKIP_DMA_LEN				(64)

#define GC_READ_GCSA_P4K_OFF						(GC_BACKUP_RETRY_SKIP_DMA_OFF + GC_BACKUP_RETRY_SKIP_DMA_LEN) // 0x29DC40
#define GC_READ_GCSA_P4K_LEN						GC_BACKUP_BYTE_CNT

#define TT_GET_TEMPERATURE_OFF						(GC_READ_GCSA_P4K_OFF+GC_READ_GCSA_P4K_LEN)	 // 0x29DC50
#define TT_GET_TEMPERATURE_OFFSET					(16)
#define TT_GET_TEMPERATURE_LEN						(TT_GET_TEMPERATURE_OFFSET*32)
#define GC_BACKUP_LOAD_PARITY_ALIGN_32_BYTE			(0x10)
#define GC_BACKUP_LOAD_PARITY_SPARE_OFFSET          (TT_GET_TEMPERATURE_OFF + TT_GET_TEMPERATURE_LEN + GC_BACKUP_LOAD_PARITY_ALIGN_32_BYTE) // 0x29DE60
#define GC_BACKUP_LOAD_PARITY_SPARE_LENGTH          (64 * 6)
#if (FW_CATEGORY_FLASH == FLASH_N18_QLC)
#define GC_2ND_PASS_XP_OFFSET						(GC_BACKUP_LOAD_PARITY_SPARE_OFFSET + GC_BACKUP_LOAD_PARITY_SPARE_LENGTH) // 0x29DFE0
#define GC_2NDPASS_XP_LENGTH						(64 * 16) //ping pong IRAM buffer with 2 CE per group //0x400

#define RETRY_TRIM_TABLE_IRAM_ALIGN					(512 - ((GC_2ND_PASS_XP_OFFSET + GC_2NDPASS_XP_LENGTH) & BIT_MASK(9))) //32
#define RETRY_TRIM_TABLE_IRAM_OFFSET  				(GC_2ND_PASS_XP_OFFSET + GC_2NDPASS_XP_LENGTH + RETRY_TRIM_TABLE_IRAM_ALIGN) //0x29E400
#define RETRY_TRIM_TABLE_IRAM_LENGTH				(0x400) // 1K

#define IRAM1_RSV_OFF                      			(RETRY_TRIM_TABLE_IRAM_OFFSET + RETRY_TRIM_TABLE_IRAM_LENGTH) // 0x29E800
#define IRAM1_RSV_LEN								(2096 - (RETRY_TRIM_TABLE_IRAM_LENGTH + RETRY_TRIM_TABLE_IRAM_ALIGN))

#elif (FW_CATEGORY_FLASH == FLASH_N28_QLC)
#define GC_2ND_PASS_XP_OFFSET						(GC_BACKUP_LOAD_PARITY_SPARE_OFFSET + GC_BACKUP_LOAD_PARITY_SPARE_LENGTH) // 0x29DFE0
#define GC_2NDPASS_XP_LENGTH						(64 * 24) //ping pong IRAM buffer with 2 CE per group //0x600

#define RETRY_TRIM_TABLE_IRAM_ALIGN					(512 - ((GC_2ND_PASS_XP_OFFSET + GC_2NDPASS_XP_LENGTH) & BIT_MASK(9)))//32
#define RETRY_TRIM_TABLE_IRAM_OFFSET  				(GC_2ND_PASS_XP_OFFSET + GC_2NDPASS_XP_LENGTH + RETRY_TRIM_TABLE_IRAM_ALIGN) //0x29E600
#define RETRY_TRIM_TABLE_IRAM_LENGTH				(0x400) // 1K

#define IRAM1_RSV_OFF                      			(RETRY_TRIM_TABLE_IRAM_OFFSET + RETRY_TRIM_TABLE_IRAM_LENGTH) // 0x29EA00
#define IRAM1_RSV_LEN								(1584- (RETRY_TRIM_TABLE_IRAM_LENGTH + RETRY_TRIM_TABLE_IRAM_ALIGN))

#else /* (FW_CATEGORY_FLASH == FLASH_N18_QLC) */
#define RETRY_TRIM_TABLE_IRAM_ALIGN					(512 - ((GC_BACKUP_LOAD_PARITY_SPARE_OFFSET + GC_BACKUP_LOAD_PARITY_SPARE_LENGTH) & BIT_MASK(9)))//32
#define RETRY_TRIM_TABLE_IRAM_OFFSET  				(GC_BACKUP_LOAD_PARITY_SPARE_OFFSET + GC_BACKUP_LOAD_PARITY_SPARE_LENGTH + RETRY_TRIM_TABLE_IRAM_ALIGN) // 0x29E000
#define RETRY_TRIM_TABLE_IRAM_LENGTH				(0x400) // 1K
#define IRAM1_RSV_OFF                      			(RETRY_TRIM_TABLE_IRAM_OFFSET + RETRY_TRIM_TABLE_IRAM_LENGTH) // 0x29E400
#define IRAM1_RSV_LEN								(3120 - RETRY_TRIM_TABLE_IRAM_ALIGN - RETRY_TRIM_TABLE_IRAM_LENGTH)
#endif /* (FW_CATEGORY_FLASH == FLASH_N18_QLC) */

#define CONV_SEED_OFF								(IRAM1_RSV_OFF + IRAM1_RSV_LEN) //0x29EC00
#define CONV_SEED_CNT								(512)
#define CONV_SEED_LEN								(4)
#define CONV_SEED_TOTAL_LEN							(CONV_SEED_CNT * CONV_SEED_LEN)	// 2K

#define IRAM1_BOUND									(CONV_SEED_OFF + CONV_SEED_TOTAL_LEN) //0x29EC00

#else /* COP0_MODE_EN */

#define CONV_CH0_IDX_OFF							(IRAM1_OFF)                 // 512B aligment
#define CONV_IDX_CNT								(8192)
#define CONV_IDX_LEN								(2)
#define CONV_IDX_TOTAL_LEN							(CONV_IDX_CNT * CONV_IDX_LEN)

#define CONV_SEED_OFF								(CONV_CH0_IDX_OFF + CONV_IDX_TOTAL_LEN)
#define CONV_SEED_CNT								(512)
#define CONV_SEED_LEN								(4)
#define CONV_SEED_TOTAL_LEN							(CONV_SEED_CNT * CONV_SEED_LEN)

#define D2I_IRAM_TEST_OFF							(CONV_SEED_OFF + CONV_SEED_TOTAL_LEN)
#define D2I_IRAM_DST_OFF							(D2I_IRAM_TEST_OFF + 0x400)
#define D2I_IRAM_TEST_LEN							(0x800)

#define SPR_OFF										(D2I_IRAM_TEST_OFF + D2I_IRAM_TEST_LEN)                 // CH0/CH1 share.
#define SPR_BYTE_CNT								(64)                                // unit: byte

#define SPR_CNT										(MTQ_TOTAL_CNT)
#define SPR_TOTAL_LEN								(SPR_BYTE_CNT * SPR_CNT)

#define SPR_EXT_PREP_W_OFF							(SPR_OFF + SPR_TOTAL_LEN)
#define SPR_EXT_PREP_W_BYTE_CNT						(256)

#define SPR_EXT_PREP_W_CNT							((BMU_W_DBUF_SIZE_IN_4K + BMU_W_DDR_SIZE_IN_4K) >> 2)
#define SPR_EXT_PREP_W_TOTAL_LEN					(SPR_EXT_PREP_W_BYTE_CNT * SPR_EXT_PREP_W_CNT)

#define SPR_EXT_W_OFF								(SPR_EXT_PREP_W_OFF + SPR_EXT_PREP_W_TOTAL_LEN)
#define SPR_EXT_BYTE_W_CNT							(64)                                // unit: byte. 16 L4K.

#define SPR_EXT_DBUF_W_OFF							(SPR_EXT_W_OFF)
#define SPR_EXT_DBUF_W_CNT							(BMU_W_DBUF_SIZE_IN_4K >> 2)

#define SPR_EXT_DDR_W_OFF							(SPR_EXT_DBUF_W_OFF + SPR_EXT_DBUF_W_CNT * SPR_EXT_BYTE_W_CNT)
#define SPR_EXT_DDR_W_CNT							(BMU_W_DDR_SIZE_IN_4K >> 2)

#define SPR_EXT_W_CNT								(SPR_EXT_DBUF_W_CNT + SPR_EXT_DDR_W_CNT)
#define SPR_EXT_W_TOTAL_LEN							(SPR_EXT_BYTE_W_CNT * SPR_EXT_W_CNT)

#define SPR_EXT_R_OFF								(SPR_EXT_DDR_W_OFF + SPR_EXT_DDR_W_CNT * SPR_EXT_BYTE_W_CNT)
#define SPR_EXT_BYTE_R_CNT							(256)

#define SPR_EXT_DBUF_OFF							(SPR_EXT_R_OFF)
#define SPR_EXT_DBUF_CNT							(BMU_R_DBUF_SIZE_IN_4K >> 4)         // one spr_ext for four L4K = 16KB data.

#define SPR_EXT_BMU_GC_BUF_OFF						(SPR_EXT_DBUF_OFF + SPR_EXT_DBUF_CNT * SPR_EXT_BYTE_R_CNT)
#define SPR_EXT_BMU_GC_BUF_CNT						(BMU_GC_DBUF_SIZE_IN_4K >> 4)

#define SPR_EXT_DDR_OFF								(SPR_EXT_BMU_GC_BUF_OFF + SPR_EXT_BMU_GC_BUF_CNT * SPR_EXT_BYTE_R_CNT)
#define SPR_EXT_DDR_CNT								(BMU_R_DDR_SIZE_IN_4K >> 4)

#define SPR_EXT_R_CNT								(SPR_EXT_DBUF_CNT + SPR_EXT_BMU_GC_BUF_CNT + SPR_EXT_DDR_CNT)
#define SPR_EXT_R_TOTAL_LEN							(SPR_EXT_R_CNT * SPR_EXT_BYTE_R_CNT)

#define DEFAULT_MT_OFF								(SPR_EXT_R_OFF + SPR_EXT_R_TOTAL_LEN)
#define DEFAULT_MT_CNT								(0x10)
#define DEFAULT_MT_LEN								(DEFAULT_MT_CNT * MT_PARA_LEN)

#define LLR_OFF										(DEFAULT_MT_OFF + DEFAULT_MT_LEN)
#define LLR_TBL_CNT									(4)
#define LLR_REG_SIZE								(32)
#define LLR_LEN										(LLR_REG_SIZE * LLR_TBL_CNT * 3)        // L/M/U => 3

#define GC_BACKUP_OFF								(LLR_OFF + LLR_LEN)
#define GC_READ_P4K_OFF								(GC_BACKUP_OFF)
#define GC_READ_P4K_LEN								(GC_COPY_MAX_TIE_IN_CNT * 16)
#define GC_PTEBMP_FWSET_OFFSET						(GC_BACKUP_OFF)
#define GC_PTEBMP_FWSET_LENGTH						(GC_SRC_VB_NUM * FTL_GC_PTEBMP_GROUP_NUM * 16)
#define GC_BACKUP_SIZE								(16)
#define GC_BACKUP_LEN								(GC_BACKUP_SIZE * 512) // the size is decided by fw 8K

#define SPR_RETRY_OFF								(GC_BACKUP_OFF + GC_BACKUP_LEN)
#define SPR_DRIVER_BYTE_CNT							(128)

#define IRAM1_BOUND									(SPR_RETRY_OFF  +  SPR_DRIVER_BYTE_CNT)//(MT_RETRY_OFF  + MT_RETRY_LEN)

#if (CONV_CH0_IDX_OFF & 0x1FF)
#error "CONV_CH0_IDX_OFF should be 0x200 alignment!"
#endif /* (CONV_CH0_IDX_OFF & 0x1FF) */
#endif /* COP0_MODE_EN */

#if (IRAM1_BOUND > IRAM_SIZE)
#error "IRAM1 overflow!"
#endif /* (IRAM1_BOUND > IRAM_SIZE) */

// ---------------------------------------------------------------------------
// COP0 hardware configuration.
// ---------------------------------------------------------------------------
#define OPT_CMDQ_DEPTH								(12) //S17:16 , E13:12
#define OPT_QOS_CMDQ_DEPTH							(4)

// ---------------------------------------------------------------------------
// COP0 RAM, Unit: bytes
// ---------------------------------------------------------------------------
// Normal VP RAM
#define VP_PAGE_SZ									(88)
#define VP_PAGE_CNT									(384)

#define VP_RAM_BASE									(0x270000)
#define VP_RAM_LEN									(VP_PAGE_SZ * VP_PAGE_CNT)
#define M_VP_ADDR(PTR)								(VP_RAM_BASE + (VP_PAGE_SZ * (PTR)))

#define VP_RAM_VECTOR_BASE                          (VP_RAM_BASE + VP_RAM_LEN)  // //E13:0x278400, S17:0x01285800 
#define VP_RAM_PER_VECTOR_SIZE                      (40)
#define VP_RAM_TOTAL_VECTOR                         (64)
#define VP_RAM_VECTOR_LEN                           (VP_RAM_TOTAL_VECTOR * VP_RAM_PER_VECTOR_SIZE)
#define M_VP_VECTOR_ADDR(MTIDX)                     (VP_RAM_VECTOR_BASE + (VP_RAM_PER_VECTOR_SIZE * (MTIDX)))

#define VP_RAM_LCA_VALID_BASE                       (VP_RAM_VECTOR_BASE + VP_RAM_VECTOR_LEN) //S17:0x01285D00
#define VP_RAM_LCA_VALID_SIZE                       (8)
#define VP_RAM_TOTAL_LCA_VALID                      (64)
#define VP_RAM_LCA_VALID_LEN                        (VP_RAM_LCA_VALID_SIZE*VP_RAM_TOTAL_LCA_VALID)
#define M_VP_LCA_VALID_TABLE_ADDR(MTIDX)            (VP_RAM_LCA_VALID_BASE + (VP_RAM_LCA_VALID_SIZE * (MTIDX)))

#define VP_RAM_2_SZ									(40)
#define VP_RAM_2_CNT								(64)

#define VP_RAM_2_BASE								(VP_RAM_BASE + VP_RAM_LEN) //S17:0x01285800
#define VP_RAM_2_LEN								(VP_RAM_2_SZ * VP_RAM_2_CNT)

#define VP_RAM_3_SZ									(8)
#define VP_RAM_3_CNT								(64)

#define VP_RAM_3_BASE								(VP_RAM_2_BASE + VP_RAM_2_LEN) //S17:0x01285D00
#define VP_RAM_3_LEN								(VP_RAM_3_SZ * VP_RAM_3_CNT)

#define NOR_VP_RAM_LEN								(VP_RAM_LEN + VP_RAM_2_LEN + VP_RAM_3_LEN)
#define VP_RAM_HW_LEN								(0x9000)    /* Total VP RAM size defined by HW. */

// QOS VP RAM
#define VP2_PAGE_CNT								(128)

#define VP2_RAM_BASE								(0x279000)
#define VP2_RAM_LEN									(VP_PAGE_SZ * VP2_PAGE_CNT)
#define M_VP2_ADDR(PTR)								(VP2_RAM_BASE + (VP_PAGE_SZ * (PTR)))

#define VP2_RAM_2_CNT								(64)

#define VP2_RAM_2_BASE								(VP2_RAM_BASE + VP2_RAM_LEN) //S17:0x01291600
#define VP2_RAM_2_LEN								(VP_RAM_2_SZ * VP2_RAM_2_CNT)

#define VP2_RAM_3_CNT								(64)

#define VP2_RAM_3_BASE								(VP2_RAM_2_BASE + VP2_RAM_2_LEN) //S17:0x01291B00
#define VP2_RAM_3_LEN								(VP_RAM_3_SZ * VP2_RAM_3_CNT)

#define QOS_VP_RAM_LEN								(VP2_RAM_LEN + VP2_RAM_2_LEN + VP2_RAM_3_LEN)
#define VP2_RAM_HW_LEN								(0x3800)            /* Total VP2 RAM size defined by HW. */

// ICQ RAM
#define ICQ_PAGE_SIZE								(8)
#define ICQ_NOR_PAGE_CNT							(64)
#define ICQ_QOS_PAGE_CNT							(64)

#define ICQ_RAM_BASE								(0x27D000)
#define ICQ_RAM_LEN									((ICQ_NOR_PAGE_CNT + ICQ_QOS_PAGE_CNT) * ICQ_PAGE_SIZE)

// OPTC RAM
#define OPTC_PAGE_SIZE								(16)
#define OPTC_NOR_PAGE_CNT							(384)
#define OPTC_QOS_PAGE_CNT							(128)

#define OPTC_RAM_BASE								(COP0_OPT_RAM_ADDRESS)
#define OPTC_RAM_LEN								((OPTC_NOR_PAGE_CNT + OPTC_QOS_PAGE_CNT) * OPTC_PAGE_SIZE)

// LINK RAM
#define LINK_PAGE_SIZE								(4)
#define LINK_NOR_PAGE_CNT							(384)
#define LINK_QOS_PAGE_CNT							(128)

#define LINK_RAM_BASE								(0x280000)
#define LINK_RAM_LEN								((LINK_NOR_PAGE_CNT + LINK_QOS_PAGE_CNT) * LINK_PAGE_SIZE)

// OPTS RAM
#define OPTS_RAM_BASE								(0x281000)
#define OPTS_RAM_LEN								(0x5000)

// OPTD RAM
#define OPTD_RAM_BASE								(0x286000)
#define OPTD_RAM_LEN								(0x7000)

#define OPTD_RECORD_FSA_ZONE_BASE					(0x0028AA00)
#define OPTD_ANDES_READ_DISTURB_PRDH_BASE			(0x0028AE80)
#define OPTD_ANDES_READ_DISTURB_THRESHOLD_BASE		(0x0028C680)
#define OPTD_ANDES_COMM_BASE						(0x0028CF50)
#define OPTD_ANDES_ASSERT_BASE						(0x0028CFCC)

#define LB_BASE_ADDR	(0x32 << 24)
#define PB_BASE_ADDR	(0x22 << 24)
#define ZIP_LB_BASE_ADDR (0x12 << 24)
#define ZIP_PB_BASE_ADDR (0x02 << 24)

#define M_LB_TO_ADDR(LB_ID, LB_OFFSET)		(LB_BASE_ADDR + (((U32)LB_ID) << 22) + (((U32)LB_OFFSET) << 12))
#define M_PB_TO_ADDR(PB_OFFSET)				(PB_BASE_ADDR + (((U32)PB_OFFSET) << 12))
#define M_ZIP_LB_TO_ADDR(LB_ID, LB_OFFSET)	(ZIP_LB_BASE_ADDR + (((U32)LB_ID) << 22) + (((U32)LB_OFFSET) << 12))
#define M_ZIP_PB_TO_ADDR(PB_OFFSET)			(ZIP_PB_BASE_ADDR + (((U32)PB_OFFSET) << 12))

//===================================================================//
// MTPool in COP0 DCCM
//===================================================================//
#define OPT_D_MTQ_BASE				(OPTD_RAM_BASE)
#define MTP_MT_SIZE					(64)
#define MTP_NOR_MT_CNT				(111)
#define MTP_QOS_MT_CNT				(1)
#define MTP_ERR_MT_CNT				(16)
#define OPT_D_MTP_NOR_BASE			(OPT_D_MTQ_BASE)
#define OPT_D_MTP_QOS_BASE			(OPT_D_MTP_NOR_BASE+MTP_NOR_MT_CNT*MTP_MT_SIZE)
#define OPT_D_MTP_ERR_BASE			(OPT_D_MTP_QOS_BASE+MTP_QOS_MT_CNT*MTP_MT_SIZE)
#define OPT_D_MTQ_LENS				(MTP_NOR_MT_CNT+MTP_QOS_MT_CNT+MTP_ERR_MT_CNT)*(MTP_MT_SIZE)

#define OPT_D_MTD_BASE				(OPT_D_MTQ_BASE + OPT_D_MTQ_LENS)
#define MTP_MTD_SIZE				(16)
#define OPT_D_MTD_LENS				(MTP_NOR_MT_CNT+MTP_QOS_MT_CNT+MTP_ERR_MT_CNT)*(MTP_MTD_SIZE)
#define OPT_D_MTD_NOR_BASE			(OPT_D_MTD_BASE)
#define OPT_D_MTD_QOS_BASE			(OPT_D_MTD_NOR_BASE+MTP_NOR_MT_CNT*MTP_MTD_SIZE)
#define OPT_D_MTD_ERR_BASE			(OPT_D_MTD_QOS_BASE+MTP_QOS_MT_CNT*MTP_MTD_SIZE)

#define OPT_D_LL_BASE				(OPT_D_MTD_BASE + OPT_D_MTD_LENS)
#define OPT_D_LL_NOR_BASE			(OPT_D_LL_BASE)
#define OPT_D_LL_NOR_LENS			(MTP_NOR_MT_CNT+MTP_ERR_MT_CNT)
#define OPT_D_LL_ERR_BASE			(OPT_D_LL_BASE+MTP_NOR_MT_CNT)
#define OPT_D_LL_QOS_BASE			((OPT_D_LL_BASE+OPT_D_LL_NOR_LENS+7)/8*8)
#define OPT_D_LL_QOS_LENS			(MTP_QOS_MT_CNT+MTP_ERR_MT_CNT)

#define OPT_D_RSC_BASE				((OPT_D_LL_QOS_BASE+OPT_D_LL_QOS_LENS+7)/8*8)
#define OPT_D_RSC_NOR_BASE			(OPT_D_RSC_BASE)
#define OPT_D_RSC_QOS_BASE			((OPT_D_RSC_BASE+MTP_NOR_MT_CNT+7)/8*8)
#define OPT_D_RSC_ERR_BASE			((OPT_D_RSC_QOS_BASE+MTP_QOS_MT_CNT+7)/8*8)

#define COP0_L4K_PAGE_SZ			(256)   // 16B * 16 entry = 256B?  (嚙課慮zip?)
#define COP0_L4K_ENTRY_SZ			(16)

#define M_COPYBLK_BACKUP_SPARE_OFF(IDX)				(GC_BACKUP_COPY_BLK_OFF + ((IDX)*(COP0_L4K_ENTRY_SZ)))
#define M_COPYBLK_BACKUP_SPARE_ADDR(IDX)			(IRAM_BASE + GC_BACKUP_COPY_BLK_OFF + ((IDX)*(COP0_L4K_ENTRY_SZ)))
#define M_L4K_OFF(MT_IDX, PG_VLD)					(SPR_OFF + ((PG_VLD) * (COP0_L4K_ENTRY_SZ)) + ((MT_IDX) * (COP0_L4K_PAGE_SZ)))

#define M_L4K_ADDR(MT_IDX,PG_VLD)					(IRAM_BASE + SPR_OFF + ((U32)(PG_VLD) * (COP0_L4K_ENTRY_SZ)) + ((U32)(MT_IDX) * (COP0_L4K_PAGE_SZ)))
#define M_MT_ADDR(MT_IDX)							(IRAM_BASE + MT_OFF + ((U32)(MT_IDX) * (MT_PARA_LEN)))
#define M_QOS_MT_ADDR(MT_IDX)						(IRAM_BASE + MT_QOS_OFF + ((U32)(MT_IDX) * (MT_PARA_LEN)))

#if VS_SIM_EN
// include the hw_sim_reg to redefine the address with windows memory space.
#include "hw_sim_reg.h"
#endif /* VS_SIM_EN */

#endif /* _MEM_5013_H_ */
