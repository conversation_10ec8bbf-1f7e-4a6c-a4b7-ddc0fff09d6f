
#ifndef _COP0_TIEOUT_H_
#define _COP0_TIEOUT_H_

#include "setup.h"
#include "typedef.h"

// TIE OUT CMD

#define M_COP0_POP_RESULT_2_CPU(result_ptr)     			M_COP0_TIEOUT_NONBLOCK((result_ptr), 0)
#define M_COP0_POP_RESULT_2_COP1(result_ptr)     		    M_COP0_TIEOUT_NONBLOCK((result_ptr), 3)
#define M_COP0_BLOCK_POP_RESULT_2_CPU(result_ptr)     	M_COP0_TIEOUT_BLOCK((result_ptr), 0)
#define M_COP0_BLOCK_POP_RESULT_2_COP1(result_ptr)     	M_COP0_TIEOUT_BLOCK((result_ptr), 3)

#define M_COP0_TIEOUT_NONBLOCK(data, cpu_index)			COP0_TieoutNonblock((data), (cpu_index))
#define M_COP0_TIEOUT_BLOCK(data, cpu_index)				COP0_TieoutBlock((data), (cpu_index))

#if TIE_OUT_EN
U16 COP0_CPU_Rec_TieOut(void);
U16 COP0_COP1_Rec_TieOut(void);

U8 COP0_TieoutNonblock(void *pData, U8 ubCPUIndex);
void COP0_TieoutBlock(void *pData, U8 ubCPUIndex);
#endif /* TIE_OUT_EN */

#endif /* _COP0_TIEOUT_H_ */
