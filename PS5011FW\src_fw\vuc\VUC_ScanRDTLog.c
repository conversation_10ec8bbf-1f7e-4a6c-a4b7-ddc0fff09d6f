#include "burner/Burner.h"
#include "spare.h"
#include "ftl/ftl_api.h"
#include "hal/pic/uart/uart_api.h"
#include "init/fw_preformat.h"
#include "nvme_api/nvme/shr_hal_nvme.h"
#include "vuc/VUC_ScanRDTLog.h"
#include "host/VUC_handler.h"
#include "vuc/VUC_command.h"

RDTLogInfo_t gRDTLog[RDT_LOG_NUM] = {{{{0}}}};
#if (BURNER_MODE_EN || RDT_MODE_EN)
U32 gulRDTLogLCA[RDT_LOG_NUM] = {
	SPARE_LCA_DBT,
	SPARE_LCA_ERROR_RECORD_LOG_BLOCK,
	SPARE_LCA_TEMPERATURE_DIVERSITY_LOG_BLOCK,
	SPARE_LCA_RDT_TESTMARK_LOG_BLOCK,
	SPARE_LCA_FLASH_TESTMARK_LOG_BLOCK,
	SPARE_LCA_POWER_RESUME_LOG_BLOCK,
	SPARE_LCA_ERROR_RECORD_LOG2_BLOCK,
#if (RDT_MODE_EN || RDT_BURNER_MODE_EN)
	SPARE_LCA_SAMPLE_BLOCK_TEST_LOG_BLOCK,
	SPARE_LCA_TP_TESTMARK_LOG_BLOCK,
	SPARE_LCA_MICRON_BEC_TABLE_LOG_BLOCK,
	SPARE_LCA_SCAN_WINDOW_LOG_BLOCK,
	SPARE_LCA_VRLC_LOG_BLOCK,
#endif /* (RDT_MODE_EN || RDT_BURNER_MODE_EN) */
#if RDT_RECORD_ECC_DISTRIBUTION
	SPARE_LCA_FW_VRLC_LOG_BLOCK,
	SPARE_LCA_ECC_DISTRIBUTION_LOG_BLOCK,
	SPARE_LCA_ECC_DISTRIBUTION_LOG2_BLOCK,
	SPARE_LCA_ECC_DISTRIBUTION_LOG3_BLOCK,
	SPARE_LCA_ECC_DISTRIBUTION_LOG4_BLOCK,
	SPARE_LCA_ECC_DISTRIBUTION_LOG5_BLOCK,
	SPARE_LCA_ECC_DISTRIBUTION_LOG6_BLOCK,
	SPARE_LCA_ECC_DISTRIBUTION_LOG7_BLOCK,
	SPARE_LCA_ECC_DISTRIBUTION_LOG8_BLOCK,
#endif
#if (RDT_RUN_ONLINE)
	SPARE_LCA_PH_MP_LOG,
#endif
};

void VUCGetRDTLogPageNum(U16 *puwPage, PCA_t ulPCA, U32 ulLCA)
{
	U32 ulBufAddr = BURNER_VENDOR_BUF_BASE;
	PCA_t ulTargetPCA;

	for (; (*puwPage) < guwFastPagePlanesPerBlock; (*puwPage)++) {

		ulTargetPCA.ulAll = ulPCA.ulAll | (GET_FPAGE(*puwPage) << gPCARule_Page.ubShift[COP0_PCA_RULE_2]);

		FTLCOP0ReadN4kEntry_AOM_INIT_2((FTLCOP0ReadN4kEntryMode_t) {
			R_GETLCA_FROM_L4KTABLE, COP0_R_SYSTEM_INITIAL_SCAN, 1, NULL
		}, ulTargetPCA,  &ulBufAddr);

		if (gulTieoutLCA[TIEOUT_SINGLE_4K_LCA_OFFSET] != ulLCA) {
			break;
		}
	}
}

void VUC_ScanRDTLog(VUC_OPT_HCMD_PTR_t pCmd)
{
	if (TRUE == gFlhEnv.ulFlashDefaultType.BitMap.btNoSupport) {
		pCmd->ubState = CMD_ERROR;
		gVUCVar.ubVucCmdErrStatus = HOST_ERROR_FEATURE;
		return;
	}

	FlashAccessInfo_t FlaInfo = {0};
	PCA_t ulPCA = {0};
	U8 ubCHExistNum, ubInvalidCHMap, ubi, ubj;
	U8 ubRDTLogNum = RDT_LOG_NUM, ubECCModeBackUp;
	U8 ubTotalLogCnt = 0, ubProgCnt[RDT_LOG_NUM] = {0};
	U32 ulBufAddr = BURNER_VENDOR_BUF_BASE;

	M_UART(VUC_, "\nVUC_SCAN_RDT_LOG");

	ubCHExistNum = gFlhEnv.ubChannelExistNum;
	ubInvalidCHMap = ~gFlhEnv.ubChannel_Exist_Bit;

	gubLeavePreformatFlag = 0;

	for (ubi = 0; ubi < RDT_LOG_NUM; ubi++) {
		gRDTLog[ubi].ulRDTLog_PCA[0].ulAll = INVALID_PCA;
		gRDTLog[ubi].ulRDTLog_PCA[1].ulAll = INVALID_PCA;
		gRDTLog[ubi].uwRDTLog_Pages[RDT_LOG_BLOCK0] = 0;
		gRDTLog[ubi].uwRDTLog_Pages[RDT_LOG_BLOCK1] = 0;
	}

	ubECCModeBackUp = M_GET_ECC_MODE();
	//-------- Change LDPC Mode to 1792 bit --------
	FlaSwitchLDPCHandler(SET_ECC_MODE_7);

	// Scan all CE0 of all CH
	for (FlaInfo.ubChannel = 0; FlaInfo.ubChannel < ubCHExistNum; FlaInfo.ubChannel++) {
		if (BIT0 != ((ubInvalidCHMap >> FlaInfo.ubChannel) & BIT0)) {
			for (FlaInfo.ubFlashCE = 0; FlaInfo.ubFlashCE < gFlhEnv.ubCENumberInCh[FlaInfo.ubChannel]; FlaInfo.ubFlashCE++) {
				for (FlaInfo.ubPlane = 0; FlaInfo.ubPlane < gubBurstsPerBank; FlaInfo.ubPlane++) {
					for (FlaInfo.ulBlock = 0; FlaInfo.ulBlock < MAX_SCAN_RDT_BLOCK_NUM; FlaInfo.ulBlock++) {
						FlaInfo.uwPage = 0;
						ulPCA.ulAll = FTLReverseCop0CieInPCA(FlaGetPCA(FlaInfo.ubPlane, FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, FlaInfo.uwPage, FlaInfo.ulBlock, COP0_PCA_RULE_2));

						FTLCOP0ReadN4kEntry_AOM_INIT_2((FTLCOP0ReadN4kEntryMode_t) {
							R_GETLCA_FROM_L4KTABLE, COP0_R_SYSTEM_INITIAL_SCAN, 1, NULL
						}, ulPCA,   &ulBufAddr);

						switch (gulTieoutLCA[TIEOUT_SINGLE_4K_LCA_OFFSET]) {
						case SPARE_LCA_DBT_HEADER:
							if (RDT_BLOCK_NUM_PER_LOG != ubProgCnt[RDT_LOG_DBT]) {
								gRDTLog[RDT_LOG_DBT].ulRDTLog_PCA[ubProgCnt[RDT_LOG_DBT]] = ulPCA;

								ubProgCnt[RDT_LOG_DBT]++;

								ubTotalLogCnt++;
							}
							break;
						case SPARE_LCA_ERROR_RECORD_LOG_BLOCK:
							if (RDT_BLOCK_NUM_PER_LOG != ubProgCnt[RDT_LOG_ERROR_RECORD]) {
								gRDTLog[RDT_LOG_ERROR_RECORD].ulRDTLog_PCA[ubProgCnt[RDT_LOG_ERROR_RECORD]] = ulPCA;

								ubProgCnt[RDT_LOG_ERROR_RECORD]++;

								ubTotalLogCnt++;
							}
							break;
						case SPARE_LCA_TEMPERATURE_DIVERSITY_LOG_BLOCK:
							if (ubProgCnt[RDT_LOG_TEMPERATURE_DIVERSITY] != RDT_BLOCK_NUM_PER_LOG) {
								gRDTLog[RDT_LOG_TEMPERATURE_DIVERSITY].ulRDTLog_PCA[ubProgCnt[RDT_LOG_TEMPERATURE_DIVERSITY]] = ulPCA;

								ubProgCnt[RDT_LOG_TEMPERATURE_DIVERSITY]++;

								ubTotalLogCnt++;
							}
							break;
						case SPARE_LCA_RDT_TESTMARK_LOG_BLOCK:
							if (ubProgCnt[RDT_LOG_RDT_TESTMARK] != RDT_BLOCK_NUM_PER_LOG) {
								gRDTLog[RDT_LOG_RDT_TESTMARK].ulRDTLog_PCA[ubProgCnt[RDT_LOG_RDT_TESTMARK]] = ulPCA;

								ubProgCnt[RDT_LOG_RDT_TESTMARK]++;

								ubTotalLogCnt++;
							}
							break;
						case SPARE_LCA_FLASH_TESTMARK_LOG_BLOCK:
							if (ubProgCnt[RDT_LOG_FLASH_TESTMARK] != RDT_BLOCK_NUM_PER_LOG) {
								gRDTLog[RDT_LOG_FLASH_TESTMARK].ulRDTLog_PCA[ubProgCnt[RDT_LOG_FLASH_TESTMARK]] = ulPCA;

								ubProgCnt[RDT_LOG_FLASH_TESTMARK]++;

								ubTotalLogCnt++;
							}
							break;
						case SPARE_LCA_POWER_RESUME_LOG_BLOCK:
							if (ubProgCnt[RDT_LOG_POWER_RESUME] != RDT_BLOCK_NUM_PER_LOG) {
								gRDTLog[RDT_LOG_POWER_RESUME].ulRDTLog_PCA[ubProgCnt[RDT_LOG_POWER_RESUME]] = ulPCA;

								ubProgCnt[RDT_LOG_POWER_RESUME]++;

								ubTotalLogCnt++;
							}
							break;
						case SPARE_LCA_ERROR_RECORD_LOG2_BLOCK:
							if (ubProgCnt[RDT_LOG_ERROR_RECORD_2] != RDT_BLOCK_NUM_PER_LOG) {
								gRDTLog[RDT_LOG_ERROR_RECORD_2].ulRDTLog_PCA[ubProgCnt[RDT_LOG_ERROR_RECORD_2]] = ulPCA;

								ubProgCnt[RDT_LOG_ERROR_RECORD_2]++;

								ubTotalLogCnt++;
							}
							break;
#if (RDT_MODE_EN || RDT_BURNER_MODE_EN)
						case SPARE_LCA_SAMPLE_BLOCK_TEST_LOG_BLOCK:
							if (ubProgCnt[RDT_LOG_SAMPLE_BLOCK_TEST_LOG] != RDT_BLOCK_NUM_PER_LOG) {
								gRDTLog[RDT_LOG_SAMPLE_BLOCK_TEST_LOG].ulRDTLog_PCA[ubProgCnt[RDT_LOG_SAMPLE_BLOCK_TEST_LOG]] = ulPCA;

								ubProgCnt[RDT_LOG_SAMPLE_BLOCK_TEST_LOG]++;

								ubTotalLogCnt++;
							}
							break;
						case SPARE_LCA_SCAN_WINDOW_LOG_BLOCK:
							if (ubProgCnt[RDT_LOG_SCAN_WIN] != RDT_BLOCK_NUM_PER_LOG) {
								gRDTLog[RDT_LOG_SCAN_WIN].ulRDTLog_PCA[ubProgCnt[RDT_LOG_SCAN_WIN]] = ulPCA;

								ubProgCnt[RDT_LOG_SCAN_WIN]++;

								ubTotalLogCnt++;
							}
							break;
#endif /* (RDT_MODE_EN || RDT_BURNER_MODE_EN) */
#if RDT_RECORD_ECC_DISTRIBUTION
						case SPARE_LCA_ECC_DISTRIBUTION_LOG_BLOCK:
							if (RDT_BLOCK_NUM_PER_LOG != ubProgCnt[RDT_LOG_ECC_DISTRIBUTION_LOG]) {
								gRDTLog[RDT_LOG_ECC_DISTRIBUTION_LOG].ulRDTLog_PCA[ubProgCnt[RDT_LOG_ECC_DISTRIBUTION_LOG]] = ulPCA;

								ubProgCnt[RDT_LOG_ECC_DISTRIBUTION_LOG]++;

								ubTotalLogCnt++;
							}
							break;
						case SPARE_LCA_ECC_DISTRIBUTION_LOG2_BLOCK:
							if (RDT_BLOCK_NUM_PER_LOG != ubProgCnt[RDT_LOG_ECC_DISTRIBUTION_LOG2]) {
								gRDTLog[RDT_LOG_ECC_DISTRIBUTION_LOG2].ulRDTLog_PCA[ubProgCnt[RDT_LOG_ECC_DISTRIBUTION_LOG2]] = ulPCA;

								ubProgCnt[RDT_LOG_ECC_DISTRIBUTION_LOG2]++;

								ubTotalLogCnt++;
							}
							break;
						case SPARE_LCA_ECC_DISTRIBUTION_LOG3_BLOCK:
							if (RDT_BLOCK_NUM_PER_LOG != ubProgCnt[RDT_LOG_ECC_DISTRIBUTION_LOG3]) {
								gRDTLog[RDT_LOG_ECC_DISTRIBUTION_LOG3].ulRDTLog_PCA[ubProgCnt[RDT_LOG_ECC_DISTRIBUTION_LOG3]] = ulPCA;

								ubProgCnt[RDT_LOG_ECC_DISTRIBUTION_LOG3]++;

								ubTotalLogCnt++;
							}
							break;
						case SPARE_LCA_ECC_DISTRIBUTION_LOG4_BLOCK:
							if (RDT_BLOCK_NUM_PER_LOG != ubProgCnt[RDT_LOG_ECC_DISTRIBUTION_LOG4]) {
								gRDTLog[RDT_LOG_ECC_DISTRIBUTION_LOG4].ulRDTLog_PCA[ubProgCnt[RDT_LOG_ECC_DISTRIBUTION_LOG4]] = ulPCA;

								ubProgCnt[RDT_LOG_ECC_DISTRIBUTION_LOG4]++;

								ubTotalLogCnt++;
							}
							break;
						case SPARE_LCA_ECC_DISTRIBUTION_LOG5_BLOCK:
							if (RDT_BLOCK_NUM_PER_LOG != ubProgCnt[RDT_LOG_ECC_DISTRIBUTION_LOG5]) {
								gRDTLog[RDT_LOG_ECC_DISTRIBUTION_LOG5].ulRDTLog_PCA[ubProgCnt[RDT_LOG_ECC_DISTRIBUTION_LOG5]] = ulPCA;

								ubProgCnt[RDT_LOG_ECC_DISTRIBUTION_LOG5]++;

								ubTotalLogCnt++;
							}
							break;
						case SPARE_LCA_ECC_DISTRIBUTION_LOG6_BLOCK:
							if (RDT_BLOCK_NUM_PER_LOG != ubProgCnt[RDT_LOG_ECC_DISTRIBUTION_LOG6]) {
								gRDTLog[RDT_LOG_ECC_DISTRIBUTION_LOG6].ulRDTLog_PCA[ubProgCnt[RDT_LOG_ECC_DISTRIBUTION_LOG6]] = ulPCA;

								ubProgCnt[RDT_LOG_ECC_DISTRIBUTION_LOG6]++;

								ubTotalLogCnt++;
							}
							break;
						case SPARE_LCA_ECC_DISTRIBUTION_LOG7_BLOCK:
							if (RDT_BLOCK_NUM_PER_LOG != ubProgCnt[RDT_LOG_ECC_DISTRIBUTION_LOG7]) {
								gRDTLog[RDT_LOG_ECC_DISTRIBUTION_LOG7].ulRDTLog_PCA[ubProgCnt[RDT_LOG_ECC_DISTRIBUTION_LOG7]] = ulPCA;

								ubProgCnt[RDT_LOG_ECC_DISTRIBUTION_LOG7]++;

								ubTotalLogCnt++;
							}
							break;
						case SPARE_LCA_ECC_DISTRIBUTION_LOG8_BLOCK:
							if (RDT_BLOCK_NUM_PER_LOG != ubProgCnt[RDT_LOG_ECC_DISTRIBUTION_LOG8]) {
								gRDTLog[RDT_LOG_ECC_DISTRIBUTION_LOG8].ulRDTLog_PCA[ubProgCnt[RDT_LOG_ECC_DISTRIBUTION_LOG8]] = ulPCA;

								ubProgCnt[RDT_LOG_ECC_DISTRIBUTION_LOG8]++;

								ubTotalLogCnt++;
							}
							break;
#endif
						default:
							break;
						}
					}
				}
			}
		}
	}
	//-------- Restore LDPC Mode --------
	FlaSwitchLDPCHandler(ubECCModeBackUp);

	for (ubi = 0; ubi < ubRDTLogNum; ubi++) {
		if (INVALID_PCA == gRDTLog[ubi].ulRDTLog_PCA[RDT_LOG_BLOCK0].ulAll) {
			continue;
		}

		for (ubj = 0; ubj < RDT_BLOCK_NUM_PER_LOG; ubj++) {
			FlaInfo.uwPage = 1;
			VUCGetRDTLogPageNum(&FlaInfo.uwPage, gRDTLog[ubi].ulRDTLog_PCA[ubj], gulRDTLogLCA[ubi]);
			gRDTLog[ubi].uwRDTLog_Pages[ubj] = FlaInfo.uwPage - 1;
		}
	}

	gRDTLog[RDT_LOG_ERROR_RECORD_2].uwRDTLog_Pages[RDT_LOG_BLOCK0] = gRDTLog[RDT_LOG_ERROR_RECORD].uwRDTLog_Pages[RDT_LOG_BLOCK0] + gRDTLog[RDT_LOG_ERROR_RECORD_2].uwRDTLog_Pages[RDT_LOG_BLOCK0];
	gRDTLog[RDT_LOG_ERROR_RECORD_2].uwRDTLog_Pages[RDT_LOG_BLOCK1] = gRDTLog[RDT_LOG_ERROR_RECORD].uwRDTLog_Pages[RDT_LOG_BLOCK1] + gRDTLog[RDT_LOG_ERROR_RECORD_2].uwRDTLog_Pages[RDT_LOG_BLOCK1];

	for (ubi = 1; ubi < 8; ubi++) {
		if (gRDTLog[RDT_LOG_ECC_DISTRIBUTION_LOG + ubi].uwRDTLog_Pages[RDT_LOG_BLOCK0] != 0) {
			gRDTLog[RDT_LOG_ECC_DISTRIBUTION_LOG + ubi].uwRDTLog_Pages[RDT_LOG_BLOCK0] = gRDTLog[RDT_LOG_ECC_DISTRIBUTION_LOG + ubi - 1].uwRDTLog_Pages[RDT_LOG_BLOCK0] + gRDTLog[RDT_LOG_ECC_DISTRIBUTION_LOG + ubi].uwRDTLog_Pages[RDT_LOG_BLOCK0];
			gRDTLog[RDT_LOG_ECC_DISTRIBUTION_LOG + ubi].uwRDTLog_Pages[RDT_LOG_BLOCK1] = gRDTLog[RDT_LOG_ECC_DISTRIBUTION_LOG + ubi - 1].uwRDTLog_Pages[RDT_LOG_BLOCK1] + gRDTLog[RDT_LOG_ECC_DISTRIBUTION_LOG + ubi].uwRDTLog_Pages[RDT_LOG_BLOCK1];
		}
	}

	gubLeavePreformatFlag = 1;

}
#endif /* (BURNER_MODE_EN || RDT_MODE_EN) */
