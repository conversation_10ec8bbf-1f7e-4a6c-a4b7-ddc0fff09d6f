#ifndef _CPU_CACHE_API_H_
#define _CPU_CACHE_API_H_

#include "typedef.h"

/*******************************************************************************
* Define and Enum
*******************************************************************************/
#define ARM_DCache_Line_SIZE (32)

//use carefully
#define ARM_DCACHE_CLEAN_INVALIDATE_ALL() do{\
                                                if(CPU_DCACHE_EN){\
                                                     ARM_DCache_Clean_Invalidate_All();\
                                                }\
                                            }while(0)


#define ARM_DCACHE_CLEAN_RANGE(START_ADDR, SIZE_IN_BYTES) do{\
                                                                if(CPU_DCACHE_EN){\
                                                                    ARM_DCache_Clean_Range(START_ADDR, SIZE_IN_BYTES);\
                                                                }\
                                                            }while(0)

#define ARM_DCACHE_INVALIDATE_RANGE(START_ADDR, SIZE_IN_BYTES) do{\
                                                                    if(CPU_DCACHE_EN){\
                                                                        ARM_DCache_Invalidate_Range(START_ADDR, SIZE_IN_BYTES);\
                                                                    }\
                                                                }while(0)

#define ARM_DCACHE_CLEAN_INVALIDATE_RANGE(START_ADDR, SIZE_IN_BYTES) do{\
                                                                    if(CPU_DCACHE_EN){\
                                                                        ARM_DCache_Clean_Invalidate_Range(START_ADDR, SIZE_IN_BYTES);\
                                                }\
                                            }while(0)

#define ARM_DCACHE_INVALIDATE_ALL() do{\
                                                                    if(CPU_DCACHE_EN){\
                                                                        ARM_DCache_Invalidate_All();\
                                                }\
                                            }while(0)
/*******************************************************************************
* Function Prototype
*******************************************************************************/
/** FUNCTION: ARM_DCache_enable, ARM_DCache_disable, arm_icache_enable, arm_icache_disable
*
* @brief Enable or disable Dcache or Icache. For Dcache, if there exist cacheable regions, one must enable Dcache.
*
*/
void ARM_DCache_Enable(void);
void ARM_DCache_Disable(void);
void ARM_ICache_Enable(void);
void ARM_ICache_Disable(void);

/** FUNCTION: ARM_DCache_XXX_all
*
* @brief Clean, Invalidate, or "Clean plus Invalidate" all.
*
*/
void ARM_DCache_Clean_All(void);
void ARM_DCache_Invalidate_All(void);
void ARM_DCache_Clean_Invalidate_All(void);

/** FUNCTION: ARM_DCache_XXX_Range
*
* @brief Clean, Invalidate, or "Clean plus Invalidate" a Range of [Addr_start, Addr_start + SizeInBytes), given both sides are 32B algin.
* If either side is not 32B aligned, these functions extend that boundary to make it 32B align and work on a larger Range, which might be dangerous for Invalidate-only operation.
*
* @param[in] StartAddr The starting Address.
* @param[in] SizeInBytes The size of Range in bytes.
*
*/
void ARM_DCache_Clean_Range(U32 StartAddr, U32 SizeInBytes);
void ARM_DCache_Invalidate_Range(U32 StartAddr, U32 SizeInBytes);
void ARM_DCache_Clean_Invalidate_Range(U32 StartAddr, U32 SizeInBytes);

/** FUNCTION: ARM_DCache_XXX_Line
*
* @brief Clean, Invalidate, or "Clean plus Invalidate" a cache Line containing the given Address.
*
* @param[in] Addr The Address pointing to a cache Line.
*
*/
void ARM_DCache_Clean_Line(U32 Addr);
void ARM_DCache_Invalidate_Line(U32 Addr);
void ARM_DCache_Clean_Invalidate_Line(U32 Addr);


#endif /* _CPU_CACHE_API_H_ */