/* ovl_perthread.c - overlay perthread function for the single-threaded case
 *
 * Copyright 2016 ARM Limited. All rights reserved.
 */

#include <stdint.h>
#include <stddef.h>

#include "ovl_internal.h"
#include "ovl_api.h"

static struct __perthread *single_perthread_structure = {0};

void *__ARM_overlay_api_perthread(void)
{
	/*
	 * Default single-threaded implementation: just return the same
	 * pointer variable always.
	 */
	return single_perthread_structure;
}

int $Super$$main(int argc, char **argv);
int $Sub$$main(int argc, char **argv)
{
	/*
	 * Wrap main() so that we set up the above pointer variable before
	 * beginning the main program.
	 */
	single_perthread_structure = __ARM_overlay_perthread_new();
	return $Super$$main(argc, argv);
}
