/** @file setup.h
 *  @brief
 *
 * 1. ???FW?????????, ??????????
 * 2. ?????????????module?header
 * 3. FW?fw_var.h??FW??
 * 4. compiler??, ??env.h
 * 5. cpu, uart, debug(test), ?m????????.
 */
#ifndef _SETUP_H_
#define _SETUP_H_
#include "symbol.h"
#include "env.h"
#if (USB == HOST_MODE)
#include "setup_usb.h"
#endif /* (USB == HOST_MODE) */
#define FLASH_400				(400)
#define FLASH_533				(533)
#define FLASH_667				(667)
#define FLASH_800				(800)


//V6 RDT porting
#define E17_PORTING_SYS0_CLK_SKIP					(TRUE)  // 20221116
#define U17_PORTING_EARLY_BAD_INT_EN				(TRUE)

//


//Micron
//??????, ??"?????"??, ????????????BGLPM, LPM3, APST, ...??
//????????define????????, ????"?????"????
#define MICRON_TEST_EN (MICRON_FSP_EN)
//================================================================================
//		Kingston??
//================================================================================
#if((FW_CATEGORY_CUSTOMER == CUSTOMER_KINGSTON) || (FW_CATEGORY_CUSTOMER == CUSTOMER_KINGSTON_HP) || (FW_CATEGORY_CUSTOMER == CUSTOMER_KINGSTON_DELL) || (FW_CATEGORY_CUSTOMER == CUSTOMER_SOEM))
#define KINGSTON_EN         (TRUE)
#else /*((FW_CATEGORY_CUSTOMER == CUSTOMER_KINGSTON) || (FW_CATEGORY_CUSTOMER == CUSTOMER_KINGSTON_HP) || (FW_CATEGORY_CUSTOMER == CUSTOMER_KINGSTON_DELL))*/
#define KINGSTON_EN         (FALSE)
#endif /*((FW_CATEGORY_CUSTOMER == CUSTOMER_KINGSTON) || (FW_CATEGORY_CUSTOMER == CUSTOMER_KINGSTON_HP) || (FW_CATEGORY_CUSTOMER == CUSTOMER_KINGSTON_DELL))*/

//================================================================================
//		Seagate??
//================================================================================
#if(FW_CATEGORY_CUSTOMER == CUSTOMER_SEAGATE)
#define SEAGATE_EN			(TRUE)
#else /*(FW_CATEGORY_CUSTOMER == CUSTOMER_SEAGATE)*/
#define SEAGATE_EN			(FALSE)
#endif /*(FW_CATEGORY_CUSTOMER == CUSTOMER_SEAGATE)*/
//================================================================================
//      Performance Control
//================================================================================
#if((FW_BUILD_VERSION == FW_VERSION_FULL_PERFORMANCE_XZIP_ON) || (FW_BUILD_VERSION == FW_VERSION_FULL_PERFORMANCE_XZIP_OFF) || RELEASED_FW)
#define PERFORMANCE_TEST_EN         (TRUE)
#else
#define PERFORMANCE_TEST_EN         (FALSE)
#endif

#define IC_98_PERCENT		 	(TRUE && PS5013_EN)
#if PERFORMANCE_TEST_EN
#define _FLASH_SPEED (FLASH_800)
#endif /* PERFORMANCE_TEST_EN */

#if PERFORMANCE_TEST_EN
#define ARM_TELL_ANDES_SEQUENTIAL_OPERATION_EN (TRUE)
#else /* PERFORMANCE_TEST_EN */
#define ARM_TELL_ANDES_SEQUENTIAL_OPERATION_EN (TRUE)
#endif /* PERFORMANCE_TEST_EN */

#define L0S_SUPPORT				(FALSE)

//==============================================================================
// FW?????, ???????
//==============================================================================
#define NTODT_IMPLEMENT_EN		(TRUE && IM_N48R && PS5021_EN)//Duson Porting BICS5 Add
#define NTODT_IMPLEMENT_ON_EVB	(FALSE && NTODT_IMPLEMENT_EN)

//Bootloader
#define BOOTLOADER_EN				(TRUE && (!PS5017_EN) && (!PS5913_EN) && (!PS5019_EN) && (!PS5021_EN))

// D1 Support:
#define D1_MIX_D1D3GCSRC     		(D1_UNIT_EN)
#define D3_GR_ALWAYS_SLC_EN 		(D1_UNIT_EN)
#if (IM_N18)
#define N18_TEMP_SKIP				(TRUE)
#define N28_TEMP_SKIP				(FALSE)
#elif (IM_N28) /*(IM_N18)*/
#define N18_TEMP_SKIP				(FALSE)
#define N28_TEMP_SKIP				(FALSE)
#elif (IM_B47R || IM_B37R)/*(IM_N28)*/
#define N18_TEMP_SKIP				(FALSE)
#define N28_TEMP_SKIP				(FALSE)
#elif (IM_N48R)/*(IM_B47R)*/
#define N18_TEMP_SKIP				(FALSE)
#define N28_TEMP_SKIP				(FALSE)
#else /*(IM_N48R)*/
#define N18_TEMP_SKIP				(FALSE)
#define N28_TEMP_SKIP				(FALSE)
#endif /*IM_N18*/
#define N28_VALLEY_CHECK_EN		(TRUE && IM_N28 && (FW_CATEGORY_CUSTOMER == CUSTOMER_MICRON_NICKS) && (!BURNER_MODE_EN && !RDT_MODE_EN))
#define N28_COPY_UNIT_VALLEY_CHECK_EN		(TRUE && N28_VALLEY_CHECK_EN)
#define D1_WL_EN        			(WL_EN && D1_UNIT_EN)
#define D1_WL_GEN_RAND				(FALSE && D1_WL_EN)
#define D1_BINDING_EN 		        (FALSE && D1_UNIT_EN)
#define D1_ISALIVE 					((D1_UNIT_EN) ? (gpVT->D1Condition.Flag.btD1IsAlive) : (FALSE))
#define D1_RESERVED_EC_OVER_THRESHOLD_QUEUE_EN		(D1_UNIT_EN && ((!BURNER_MODE_EN) && (!RDT_MODE_EN)))
#define D1_RESERVED_UART_EN		(D1_RESERVED_EC_OVER_THRESHOLD_QUEUE_EN && FALSE)
#define D1_RESERVED_GENERATE_RANDOM_EN		(D1_RESERVED_EC_OVER_THRESHOLD_QUEUE_EN && FALSE)

/*****************************
 *  OPEN_BLOCK_RSMAP
 * ****************************/
#define OPEN_BLOCK_RSMAP_PERFORMANCE_ANALYSIS_EN	    (FALSE && OPEN_BLOCK_RSMAP_EN)

#define READ_VERIFY_EN          (TRUE && (!RDT_MODE_EN))

#define WATCH_DOG_ENABLE    (FALSE)
#define ASIC				(TRUE)

/*****************************
 *  MIX_PLANE
 * ****************************/
#define RUT_MIX_PLANE_EN 	(FALSE && (BOOTLOADER_EN || PS5017_EN)) //Dylan 2022/03/11

/*****************************
 *  GC SPOR Continue V1
 * ****************************/
#define GC_SPOR_CONTINUE_EN	((TRUE)\
	&&( (CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_2D_TLC)\
		||	(CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_TLC)\
		||	(CONFIG_FLASH_TYPE == FLASH_TYPE_SANDISK_3D_TLC)\
		||	(FW_CATEGORY_FLASH == FLASH_N18_QLC)\
		||	(FW_CATEGORY_FLASH == FLASH_N28_QLC)\
		||	(FW_CATEGORY_FLASH == FLASH_N48R_QLC))\
    && ( !(FW_CATEGORY_FLASH == FLASH_BICS4TLCHDR)\
&&   !(FW_CATEGORY_FLASH == FLASH_BICS5TLC)\
	)\
)

#define GC_SPOR_CONTINUE_V2_EN (TRUE && GC_SPOR_CONTINUE_EN && (IM_N18 || IM_N28 || IM_N48R))
#define GC_SPOR_CONTINUE_V2_DEBUG_DRIVE_LOG_EN	(FALSE && GC_SPOR_CONTINUE_V2_EN) // JIRA-12588, ????drive log
#define COPY_UNIT_BUF_ENLARGE_EN  (TRUE && GC_SPOR_CONTINUE_V2_EN)
#define GC_SPOR_CONTINUE_V3_EN (TRUE && COPY_UNIT_BUF_ENLARGE_EN) // init's copyUnit meet timeOut, turn into ftlTask
#define GC_SPOR_CONTINUE_V4_EN (TRUE && GC_SPOR_CONTINUE_V2_EN) // addFree old GCGR after GC to preSync, to avoid sync point set-back by copyToRealSync

#define POR_ABANDON_GC_TARGET_EN (TRUE&&IM_N48R)
#define DISABLE_RS_FOR_GC_EN	(FALSE)

#define DZIP_EN                             (FALSE)

#if (RDT_MODE_EN)
#define XZIP_EN                             (FALSE)
#else
#if((FW_BUILD_VERSION == FW_VERSION_FULL_MAX_XZIP_ON) || (FW_BUILD_VERSION == FW_VERSION_FULL_8G_XZIP_ON) || (FW_BUILD_VERSION == FW_VERSION_FULL_PERFORMANCE_XZIP_ON))
#define XZIP_EN                             (TRUE && !IM_N18 && !IM_N28 && !IM_B47R && !IM_N48R && !IM_B37R)
#else
#define XZIP_EN                             (FALSE)
#endif
#endif

#define BOOTCODE_SPI_CODE_BANK		(FALSE)  // BOOTCODE LOAD CODE
#if VS_SIM_EN
#define ICE_MODE_EN			(FALSE)
#define HWRS_EN				(TRUE)
#else /* VS_SIM_EN */
#define ICE_MODE_EN			(FALSE)
#endif /* VS_SIM_EN */
//Enable FlaDCC_SetFeature
#define E17_DCC_EN						(TRUE && ASIC)

#define WRITE_PROTECT_EN			(TRUE)
#define WL_EN					(TRUE)
#define BACKGROUND_EN				(TRUE)
#define BG_RELEASE_D1_EN			((D1_UNIT_EN) && (BACKGROUND_EN))
#define BG_RELEASE_SLC_EN			((TRUE) && (BACKGROUND_EN))
#define BG_RELEASE_UNIT_EN			((TRUE) && (BACKGROUND_EN))
#define BG_RELEASE_UNIT_ADVANCE_EN  ((TRUE) && (BACKGROUND_EN) && (HOST_MODE == NVME))
#define BG_WL_EN					((TRUE) && (BACKGROUND_EN))
#define SCAN_VT_MULTI_CHANNEL_EN	(TRUE)
#define FORCE_TOGGLE_EN		(FALSE)  //Shall set to TRUE if Bics4

#define BARRIER_CHECK_MERGE_QUEUE_EN (TRUE && (!VS_SIM_EN))

#if(FW_BUILD_VERSION == FW_VERSION_FULL_8G_XZIP_ON)
#define AUTO_FULL_DISK_SIZE_EN       (FALSE)
#else
#define AUTO_FULL_DISK_SIZE_EN       (TRUE)
#endif

#define FW_LIMIT_DATA_OP_EN		(FALSE || (!AUTO_FULL_DISK_SIZE_EN))

#define DEF_CE_DECODER_MODE_EN	(FALSE)

#define DEBUG_GEN_RW_COP0_SQ_FULL_EN	(FALSE) // Gen Random SQ Full Case During XferDataIn / XferDataOut
#define DEBUG_GEN_GC_COP0_SQ_FULL_EN	(FALSE)

#define ICE_MODE_CONNECTION_ENHANCE_EN	(TRUE)
#define GPIO_PULL_UP_EN                 (TRUE && PS5013_EN)

#define POWERSTATE_TO_RANDOM_LPM_EN		(TRUE && (!PERFORMANCE_TEST_EN) && (!RELEASED_FW))
#if (MICRON_FSP_EN)
#if MICRON_TEST_EN
#define LPM_EN							(TRUE && (!NCS_EN) && (!RDT_MODE_EN))
#define BG_LPM_EN						((TRUE) && (!NCS_EN) && (!RDT_MODE_EN))
#else /* MICRON_TEST_EN */
#define LPM_EN							(TRUE)
#define BG_LPM_EN						(TRUE)
#endif /* MICRON_TEST_EN */
#else  /* (MICRON_FSP_EN) */
#define LPM_EN							(TRUE && (!NCS_EN) && (!RDT_MODE_EN))
#define BG_LPM_EN						(TRUE && (!NCS_EN) && (!RDT_MODE_EN))
#endif /* (MICRON_FSP_EN) */
#define LPM_USE_PD0_DBUF_RAM_PD_256KB_FOR_LPM3	(TRUE && (NVME == HOST_MODE)) //??????????DLMC???
#define LPM_WAKE_FOR_BG_EN				((TRUE) && (BACKGROUND_EN))
#define CHECK_SLC_POOL_NUM_EN			(TRUE)
#define THERMAL_THROTTLING_EN (TRUE)
#define FAE_TT_EN 						(FALSE && (DEBUG_UART_EN) && PS5017_EN) //[BC_N]for FAE measure TT
#define IDLE_AFTER_REACH_MAX_CNTS_OF_SPOR_EN         (FALSE)
#if PS5017_EN
#define BMU_BUG_WORKAROUND_EN  (FALSE && (!RDT_MODE_EN))   //@JIRA E13-679
#else /*PS5017_EN*/
#define BMU_BUG_WORKAROUND_EN  (TRUE && (!RDT_MODE_EN))   //@JIRA E13-679
#endif /*PS5017_EN*/
#define SMB_EN                  (FALSE)
#if(((IM_B47R) || (IM_N48R) || IM_B37R) && (FW_CATEGORY_CUSTOMER == CUSTOMER_MICRON_NICKS))
#define OPEN_UNIT_LIFETIME_CHECK_EN (TRUE)
#else/*(((IM_B47R) || (IM_N48R_NEED_CHECK)) && (FW_CATEGORY_CUSTOMER == CUSTOMER_MICRON_NICKS))*/
#define OPEN_UNIT_LIFETIME_CHECK_EN (FALSE)
#endif/*(((IM_B47R) || (IM_N48R_NEED_CHECK)) && (FW_CATEGORY_CUSTOMER == CUSTOMER_MICRON_NICKS))*/
#define OPEN_UNIT_LIFETIME_DEFAULT (0xFFFFFFFFFFFFFFFF)
#define OPEN_UNIT_LIFETIME_1HOUR (3600000)
#define OPEN_UNIT_LIFETIME_15MINUTES (900000)
#define LPM_ENTRY_LATENCY_TIME_EN (FALSE)
//*****************************************
// Odd CE
//*****************************************

#define ODD_CE_EN          (FALSE || TOSHIBA_QLC_FLOW)
#define ODD_CE_DBG_UART_EN (FALSE)

//*****************************************
// INIT measurement
//*****************************************
//Will print Init Time/Counters
//Will DISable ALL the other normal UART outputs
//Will DISable PRE-READ for code size issue
//Will DISable SAVE_LOG_BY_TIME for code size issue
//Will move FTLXferDataIn() from common area into Overlay for code size issue
#define INIT_MEASURE_EN	(FALSE && RELEASED_FW && (!BURNER_MODE_EN) && (!RDT_MODE_EN))

/**************************************************************************
 * 				SECURITY SETTING
 ***************************************************************************/
#define TCG_SECURITY_VERSION    (6)
#define TCG_EN  (TCG_MODE_NOT_SUPPORT != TCG_MODE)
#define D2H_AES_EN  (TCG_EN)
#define TCG_OFF_AES_EN  (FALSE && (PS5017_EN) && (TCG_MODE_NOT_SUPPORT == TCG_MODE))  // TCG off & AES enable

#define NVME_ATA_SECURITY_SUPPORT                ((NVME == HOST_MODE) && (TRUE == BOOTLOADER_EN))
#define NVME_ATA_ENHANCE_ERASE_SUPPORT           (TRUE == NVME_ATA_SECURITY_SUPPORT)
/**************************************************************************
 * 				HOST MODE SETTING
 ***************************************************************************/

#define NVME_MODE_IOT_EN	((FALSE) && (HOST_MODE == NVME))
#define IOR_EN				((HOST_MODE == SATA) || NVME_MODE_IOT_EN || (HOST_MODE == USB))
#define WORKAROUND_STANDBY (TRUE)
#define LITEONTEST_EN 		(FALSE)
#define SAVE_LOG_BY_TIME_EN 		(TRUE && (!INIT_MEASURE_EN))
#define BACKUP_PHYSETTING_HWUSED_EN (TRUE)
#define HASEE_WORKAROUND_EN						(TRUE && (!KINGSTON_EN))
#define INDUSTRY_GRADE_EN           (FALSE)

#define HP_EN                               	(FW_CATEGORY_CUSTOMER == CUSTOMER_KINGSTON_HP || FW_CATEGORY_CUSTOMER == CUSTOMER_HP)
#define HP_SPEC_QUEUE_RESET_EN              	(HP_EN &&(TRUE))
#define HP_SPEC_IDENTIFY_KEEP_ALIVE         	(HP_EN &&(TRUE))
#define HP_SPEC_IDENTIFY_FNA                	(HP_EN &&(TRUE))
#define HP_AER_EN 								(HP_EN && TRUE)
#define HP_AER_SPARE_AREA_EN 					(HP_AER_EN && TRUE)
#define HP_AER_PERCENTAGE_USED_EN 				(HP_AER_EN && TRUE)
#define HP_SMART_EN 							(HP_EN && TRUE)
#define HP_SPEC_AER_EN							(HP_EN && TRUE)
#define HP_SPEC_4_ABORT							(HP_EN && TRUE)
#define HP_ERROR_CONTROLLER_RESET_EN			(HP_EN && TRUE)
#define HP_NGUID_FROM_MP_EN						(HP_EN && TRUE)
#define HP_SMART_TEMPERATURE1_TEMPERATURE2_EN	(HP_EN && TRUE)
#define HP_ERROR_LOG_EN							(HP_EN && TRUE)
#define HP_CCEN_RDY_3S_EN						(HP_EN && FALSE)
#define HP_PURGE_ERASE_EN					(HP_EN && TRUE)
#define HP_SMB_EN                           (HP_EN && TRUE)

#define DELL_EN									(FW_CATEGORY_CUSTOMER == CUSTOMER_KINGSTON_DELL)
#define DELL_SCP_EN								(TRUE && DELL_EN)
#define DELL_PERSISTENT_EVENT_LOG_EN		    (TRUE && DELL_EN)
//==============================================================================
// FW?????, ???????, ?????????
//==============================================================================
#define USER_DISK_SIZE     (0x1000000)  // LBA_Format=512B: 0x20000000=256GB,  0x1000000=8GB

//--- XZIP Related ---
#define FTL_XZIP_HIT_FORCE_PROGRAM_RATE_EN	(TRUE && XZIP_EN)
#define FTL_XZIP_WRITE_FORCE_CLEAR_EN			(TRUE && XZIP_EN && PERFORMANCE_TEST_EN)

//--- Fill Dummy By Frame Related ---
#define FTL_FILL_DUMMY_BY_FRAME_EN			(TRUE || IOR_EN)
#define FTL_FILL_DUMMY_FOR_SEQUENTIAL_TABLE_EN		(TRUE)
#define FTL_GCSA_FILL_DUMMY_BY_FRAME_EN			(TRUE || IOR_EN)
#define FTL_FORCE_FILL_DUMMY_BY_FRAME_EN		(IOR_EN || TRUE)		//for 1T scan valid bug(DMAC Scan Zcode force scan 3-bit bug)

//--- Read Disturb ---
#define READ_DISTURB_EN         (TRUE && (!RDT_MODE_EN))
#define READ_DISTURB_SUB_BLK_RULE_EN	(IM_N28|| IM_B47R || IM_N48R || IM_B37R)
#define READ_VERIFY_DUMMY_READ_EN		(IM_N28)
#define READ_DISTURB_THROTTLE_EN		(TRUE && (IM_N28 || IM_B47R || IM_N48R || IM_B37R))
#define READ_DISTURB_CHECK_WEAK_WORDLINE_EN		(FALSE)

#define READ_DISTURB_PRDH_EN						(TRUE && (!BURNER_MODE_EN) && (!RDT_MODE_EN) && READ_DISTURB_EN && IM_N48R && (FW_CATEGORY_CUSTOMER == CUSTOMER_MICRON_NICKS) && (!E21_TODO))
#define	READ_DISTURB_PRDH_BURNER_EN					(TRUE && (BURNER_MODE_EN) && READ_DISTURB_EN && IM_N48R && (FW_CATEGORY_CUSTOMER == CUSTOMER_MICRON_NICKS) && (!E21_TODO))
#define READ_DISTURB_PRDH_RECORD_SCAN_TIME_EN		(TRUE && READ_DISTURB_PRDH_EN && UNIFIED_LOG_EN)
//--- WordLine Folding ---
#define WORDLINE_FOLDING_EN							(TRUE && IM_N48R && (FW_CATEGORY_CUSTOMER == CUSTOMER_MICRON_NICKS) && (!RDT_MODE_EN))  //Incompatible with DEBUG_CMD_LIFETIME

//--- SystemArea Feature ---
#define SYSTEM_AREA_BANKING_BLK_EN	(TRUE)

//--- Peak Power Management ---
#define PEAK_POWER_MANAGEMENT_EN    (FALSE && IM_N48R && U17_EN && (FW_CATEGORY_CUSTOMER == CUSTOMER_MICRON_NICKS))

//--- Prodution History Block ---
#define PHBLOCK_EN                (FALSE)//(!(IM_N48R && U17_EN && (FW_CATEGORY_CUSTOMER == CUSTOMER_MICRON_NICKS)))

/**************************************************************************
 * 				VRLC SETTING
 ***************************************************************************/
////N48R function unfinished yet
#define VRLC_EN ((TRUE && ((HOST_MODE == NVME) || (HOST_MODE == USB)) && MEDIA_SCAN_EN && (IM_N28 || IM_B47R || IM_B37R || (IM_N48R)) && !(ICE_MODE_EN) && !(RDT_MODE_EN) && (FW_CATEGORY_CUSTOMER == CUSTOMER_MICRON_NICKS)) )
#define VRLC_SUPPORT_IN_USTP0                           (VRLC_EN && TRUE)
#define VRLC_IN_MMO                        				(VRLC_EN && BURNER_MODE_EN) // Burner mode aka MMO, follow Nick2W2-5's depiction
#define VRLC_IN_CMO										(VRLC_EN && (!(IM_N48R && (!BURNER_MODE_EN)))) // FW mode aka CMO, follow Nick2W2-5's depiction
#define VRLC_INIT_BY_PIO	                            (VRLC_EN && FALSE)
#define VRLC_INIT_USTP1_WITH_RETRY                      (VRLC_EN && FALSE)
#define VRLC_CENTER_AND_ONE_SIDE_UECC_MOVE_ONE_DAC_EN	(VRLC_EN && TRUE)
#define VRLC_VALIDATION_UART_EN                         (VRLC_EN && FALSE)
#define VRLC_VALIDATION_UART_PRECISE_MODE               (VRLC_EN && (VRLC_VALIDATION_UART_EN))
#define VRLC_MEASURE_EN									(VRLC_EN && (IM_B47R || IM_B37R))
#define VRLC_E3D_EN										(VRLC_EN && (!U17_EN))//U17 add
#define TEMPCO_EN						                (VRLC_EN && (IM_B47R || IM_B37R))
#define TEMPCO_INIT_EN					                (TEMPCO_EN && TRUE)
#define TEMPCO_INIT_BY_PIO				                (TEMPCO_INIT_EN && VRLC_INIT_BY_PIO)
#define MICRON_CACHE_PROGRAM_PATCH						(VRLC_INIT_USTP1_WITH_RETRY)
#define MICRON_SLC_PROGRAM_PATCH						(VRLC_INIT_USTP1_WITH_RETRY)
#define VRLC_INIT_USTP1_WITH_RETRY_REDUCE_CODE_SIZE		(VRLC_INIT_USTP1_WITH_RETRY && TRUE)
#define BFEA_EN											(VRLC_MEASURE_EN && TRUE && (!IM_N48R))
#define BFEA_MANUAL_LOAD_BIN_SET_EN						(BFEA_EN && TRUE)
#define BFEA_UNIT_LINKED_LIST							(BFEA_EN && TRUE)
#define BFEA_BIN_SET									(2)
#define LOAD_RETRY_TRIM_TABLE_EN						(TRUE || VRLC_INIT_USTP1_WITH_RETRY)
#define VRLC_CHECK_RDT_INHERIT							(VRLC_EN && TRUE) // To be enable if RDT support vRLC

/**************************************************************************
 * 				RDT VRLC SETTING
 ***************************************************************************/
#define NICKS_BIN_ACRR                                    ((IM_B47R || IM_B37R || IM_N48R) && (FW_CATEGORY_CUSTOMER == CUSTOMER_MICRON_NICKS) && (RDT_MODE_EN))

#define VALLEY_TRACK_EN									(TRUE && IM_N48R && VIRTUAL_ADDRESS_EN)

//*****************************************
//   NES & NCS
//*****************************************
// For NCS HB, SB verification
#define NCS_EN      (FALSE)
#define NCS_SBRAID_EN      (FALSE && NCS_EN)
#define NCS_V2_EN			((!BURNER_MODE_EN) && (!RDT_MODE_EN) && RELEASED_FW && (!NCS_EN))
// For NES Verification
#define NES_EN      (FALSE || NCS_EN)
#define NES_RDT     (FALSE && RDT_MODE_EN)
#define NES_GEN1_EN		(FALSE)
#define NES_GEN2_EN		(NCS_EN)
//--- Media Scan ---
#define MEDIA_SCAN_EN           (TRUE && (!NCS_EN) && (!E21_TODO) && (!RDT_MODE_EN))
#define MEDIA_SCAN_RECORD_EN	(MICRON_FSP_EN && MEDIA_SCAN_EN)
#define MEDIA_SCAN_HIGH_PRIORITY (MEDIA_SCAN_EN)
#define MEDIA_SCAN_DEBUG_EN		(FALSE && MEDIA_SCAN_EN)
#define MEDIA_SCAN_DEBUG_CHECK_READ_OFFSET_PREFIX_EN		(FALSE && MEDIA_SCAN_DEBUG_EN && MEDIA_SCAN_EN)	//check read level of 2Eh in feature addr.
#define MEDIA_SCAN_DEBUG_CHECK_READ_LEVEL_BY_MT_EN		(FALSE && MEDIA_SCAN_DEBUG_EN && MEDIA_SCAN_EN) 	//check read level getting from trim cmd by MT (double check)
#define MEDIA_SCAN_DEBUG_ONLY_SCAN_QLC_DATA_UNIT_EN				(FALSE && MEDIA_SCAN_EN)
#define MEDIA_SCAN_DEBUG_ONLY_SCAN_QLC_AND_SLC_DATA_UNIT_EN		(FALSE && MEDIA_SCAN_EN)
#define MEDIA_SCAN_DEBUG_DATA_FOR_MICRON_UART_EN				(FALSE && MEDIA_SCAN_EN)
#define MEDIA_SCAN_DEBUG_UNIFIED_LOG_UART_EN					(FALSE && MEDIA_SCAN_EN)
#define RECORD_FLASH_TEMPERATURE_EN (TRUE && MICRON_MAG_EN && MEDIA_SCAN_EN )
#define RECORD_FLASH_TEMPERATURE_N48_TODO_EN (TRUE && MICRON_MAG_EN && MEDIA_SCAN_EN)
#define RECORD_FLASH_TEMPERATURE_DEBUG_EN (FALSE && RECORD_FLASH_TEMPERATURE_EN)
// S17 Media Scan collect information
#define MEDIA_SCAN_ENHANCED_DRIVELOG_EN    (TRUE && PS5017_EN)
#define EOT_ENHANCED_DRIVELOG_EN    (TRUE && PS5017_EN && MEDIA_SCAN_EN)

//--- DPS Feature ---
// only when tsb enable, and if this feature is true, mp will decision enable or not.But if this feature is disable, mp also wouldn't enable.
#if ((FW_CATEGORY_CUSTOMER == CUSTOMER_MAINSTREAM) &&(!MICRON_FSP_EN))
#define FIP_DPS_FEATURE_EN (TRUE)
#else
#define FIP_DPS_FEATURE_EN (FALSE)
#endif
//--- BiCS AIPR ---
#if (MICRON_FSP_EN || NES_GEN1_EN || NES_GEN2_EN)
#define BICS4_AIPR_FEATURE_EN (FALSE)
#else
#define BICS4_AIPR_FEATURE_EN (TRUE)
#endif

#if ((FW_CATEGORY_FLASH == FLASH_BICS3TLC) || (FW_CATEGORY_FLASH == FLASH_BICS4TLCHDR) || (FW_CATEGORY_FLASH == FLASH_BICS5TLC))
#define NAND_SUPPORT_AIPR (TRUE)
#else /* ((FW_CATEGORY_FLASH == FLASH_BICS3TLC) || (FW_CATEGORY_FLASH == FLASH_BICS4TLCHDR) || (FW_CATEGORY_FLASH == FLASH_BICS5TLC)) */
#define NAND_SUPPORT_AIPR (FALSE)
#endif /* ((FW_CATEGORY_FLASH == FLASH_BICS3TLC) || (FW_CATEGORY_FLASH == FLASH_BICS4TLCHDR) || (FW_CATEGORY_FLASH == FLASH_BICS5TLC)) */

#define ANDES_NO_WAIT_FEATURE_EN (TRUE)
//--- Retry Patch Sequential Cache Read ---
#define RETRY_PATCH_SEQUENTIAL_CACHE_READ_EN (MICRON_FSP_EN)

//==============================================================================
// ?????
//==============================================================================
#define LAST_ONE_FREE_UNIT_FORCE_SLC_MODE_IN_GR_EN		(TRUE && (IM_B27B || IM_B27A))	// ????GR??SLC mode???spor force copy GR ??
#define MICRON_MAG_EN (TRUE)
// TSB QLC
#define TOSHIBA_QLC_FLOW 			        (CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_QLC)
#define TWO_PASS_EN    				        (TOSHIBA_QLC_FLOW || IM_N48R || (FW_CATEGORY_FLASH == FLASH_YMTC_EMS_QLC))//ems mst add--karl
#define TWO_PASS_DEBUG_EN 				    (FALSE && TWO_PASS_EN )
#define QLC_DEBUG_EN                        (FALSE && TWO_PASS_EN) // should be FALSE when release
#define QLC_PRINT_BUFFER_EN                 (FALSE && QLC_DEBUG_EN)
#define QLC_RVERIFY_ALLPAGE_EN              (TRUE && QLC_DEBUG_EN)
#define FIXED_DUMMY_EN						(TWO_PASS_EN)
#define GCSA_16K_EN							(FALSE)
#define GCSA_B_FOR_2ND_PASS_ONLY			(TRUE && TWO_PASS_EN) // GCSA-A for normal 1st-program and backupGCSA, GCSA-B for 2nd-program

#define RAIDECC_DECODE_USING_OPEN_BLK_RAIDECC_IN_SPOR_SCANNING_READ_VERIFY_EN     (FALSE)
#define READ_VERIFY_KEEP_OPEN_BLK_RAIDECC_PARITY_UNIT_TILL_ERROR_HANDLE_DONE_EN   (FALSE)
#define READ_VERIFY_KEEP_PARITY_UNIT_FOR_SPOR_NON_LAST_GR_UNIT_EN                 (FALSE)
#define RETRY_HARDBIT_FOR_SDK_EN			(FALSE)
#define DRIVE_LOG_EN						(TRUE && (!ICE_MODE_EN) && (!RDT_MODE_EN))
#define DRIVE_LOG_SAVE_FLASH_EN				(TRUE && DRIVE_LOG_EN)
#define DRIVE_LOG_HOST_EN          			(TRUE && DRIVE_LOG_EN)
#define DRIVE_LOG_PREFORMAT_ERASE_UNIT_EN	(TRUE && DRIVE_LOG_EN) // to eliminate C6 erase state
#define DRIVE_LOG_CRITICAL_LOG_EN			(TRUE && DRIVE_LOG_EN)
#define DRIVE_LOG_HIGH_RIORITY_EVENT_EN     (TRUE && DRIVE_LOG_EN)
#define DRIVE_LOG_BFEA_EN          			(TRUE && DRIVE_LOG_EN && BFEA_EN && (!BURNER_MODE_EN))
#define LCA_COMPARE_EN			(TRUE)
#if (PERFORMANCE_TEST_EN)
#define FLASH_SPEED             _FLASH_SPEED
#else /* PERFORMANCE_TEST_EN */
#define FLASH_SPEED				(FLASH_533)
#endif /* PERFORMANCE_TEST_EN */
//PREREAD_IOPS_DETECT_EN  = FALSE & PREREAD_READ_CACHE_EN = TRUE????
#if (PERFORMANCE_TEST_EN)
#define PREREAD_READ_CACHE_EN		(FALSE)
#define PREREAD_IOPS_DETECT_EN		(FALSE)
#else /* PERFORMANCE_TEST_EN */
#define PREREAD_READ_CACHE_EN		(FALSE)
#define PREREAD_IOPS_DETECT_EN		(FALSE)
#endif /* PERFORMANCE_TEST_EN */
#if (MICRON_FSP_EN)
#if MICRON_TEST_EN
#define PREREAD_EN					(TRUE && (!NCS_EN) && (!RDT_MODE_EN) && (!INIT_MEASURE_EN) && (IM_N48R) && (PS5013_EN || PS5021_EN))
#define SEQ_TABLE_EN		    		(TRUE && (!RDT_MODE_EN))
#else /*MICRON_TEST_EN*/
#define PREREAD_EN					(FALSE && (!INIT_MEASURE_EN))
#define SEQ_TABLE_EN		    	(TRUE)
#endif /*MICRON_TEST_EN*/
#else /* (MICRON_FSP_EN) */
#define PREREAD_EN					(TRUE && (!NCS_EN) && (!RDT_MODE_EN) && (!INIT_MEASURE_EN))
#define SEQ_TABLE_EN		    	(TRUE)
#endif /* (MICRON_FSP_EN) */
//SLC can build SEQ TABLE,but non SLC PCA does not have non normal bit.
//This feature can only support Micron, TSB and other flash will support in the future.
#define UTILIZE_SLC_UNUSE_PCA_BIT_EN                  (FALSE && PS5017_EN)
#define READ_FIRST_EN				(TRUE)
#if (USB == HOST_MODE)
#define FUA_EN (FALSE)
#else /* (USB == HOST_MODE) */
#define FUA_EN (TRUE)
#endif /* (USB == HOST_MODE) */
#define XZIP_XCUT_EN				(XZIP_EN && FALSE)
#define WORKAROUND_NVMEQRWT_CIDERROR_EN		(TRUE && (HOST_MODE == NVME))
#define	VDT_USE_ISR_EN				(FALSE || PS5021_EN)
#define FLASH_TT_ZQC_EN                           (TRUE)
#define HMB_PTE_CACHE_EN	(TRUE && (!IOR_EN))
#define FIP_MT_80B_EN				(FALSE)

//==============================================================================
// Error Handle
//==============================================================================
#define DUMP_LOG_PARITY_ERROR_EN	(TRUE)

#define MICRON_SLC_CLAW_BACK_EN	(TRUE && (FW_CATEGORY_CUSTOMER == CUSTOMER_MICRON_NICKS) && (IM_B47R || IM_B37R || IM_N48R) && ((!BURNER_MODE_EN) && (!RDT_MODE_EN) && (!LPM3_LOADER) && (!BOOTLOADER_MODE_EN)))

//==============================================================================
// Erase Unclean Workaround
//==============================================================================
#define WORKAROUND_FOR_ERASE_UNCLEAN_EN				(FALSE)
#define WORKAROUND_FOR_ERASE_UNCLEAN_RE_ERASE_CNT	(1)

//==============================================================================
// PS5021 Workaround
//==============================================================================
#define Enable_E21AB_FW_WORKAROUND_1 (TRUE && PS5021_EN)

//==============================================================================
// HMB
//==============================================================================
#define HMB_DCACHE_ENABLE			(FALSE && (CPU_DCACHE_EN))
#define FULLY_CACHE_EN 				(TRUE)
#define HMB_AOM_EN					(FALSE)
#define HMB_NEW_RESET_FLOW_EN		(FALSE && (!RELEASED_FW))
#define HMB_CHECK_BUF_CAN_USE_EN	(TRUE)
#define HMB_READ_TABLE_MULTI_PLANE_EN	(TRUE)

//******************************************************************************
//************ 20181024 william add debug doorbell
#define USE_FW_DB		(TRUE)
// william add feature
#define DQ_PER_BIT_TRAIN_EN	(TRUE)
#define PROGRAM_FLASH_ENVIRONMENT_TO_NAND		(FALSE)
#if(PROGRAM_FLASH_ENVIRONMENT_TO_NAND)
#define SKIP_FIP_INIT_CHECK_TYPE		(!(BURNER_MODE_EN |ICE_MODE_EN))
#else /*(PROGRAM_FLASH_ENVIRONMENT_TO_NAND)*/
#define SKIP_FIP_INIT_CHECK_TYPE		(FALSE)
#endif /*(PROGRAM_FLASH_ENVIRONMENT_TO_NAND)*/
#if((FW_BUILD_VERSION == FW_VERSION_FULL_PERFORMANCE_XZIP_ON) || (FW_BUILD_VERSION == FW_VERSION_FULL_PERFORMANCE_XZIP_OFF))
#define SCAN_WINDOW_RETURN_FAIL_RESULT_EN   (FALSE)    // when scan window fail , enable=return fail to mp and mp will stop; disable = mp will bypass scan window fail and go on
#else
#define SCAN_WINDOW_RETURN_FAIL_RESULT_EN   (TRUE)     // when scan window fail , enable=return fail to mp and mp will stop; disable = mp will bypass scan window fail and go on
#endif

//************
#if !BURNER_MODE_EN
#define CPU_DCACHE_EN      	(TRUE)
#else /*!BURNER_MODE_EN*/
#define CPU_DCACHE_EN      	(FALSE) // BURNER DO NOT NEED DCACHE
#endif /*!BURNER_MODE_EN*/
#define LB_OFFSET_CHK_EN	(FALSE)
#define LPCRC_EN			(FALSE)
//******************************************************************************
#define BG_FILL_SHARE_PAGE_EN   (TRUE)

#define CHECK_TABLE_PROGRAM_EN	(FALSE)
#define CONFIG_VBRMP_EN 		(TRUE)
#define CONFIG_BBMP_EN			(FALSE)

#define DEFAULT_CLOSE_WAIT_PCA_EN (FALSE)

#if  (FW_CATEGORY_FLASH == FLASH_BICS4PSLC)
#define FW_RUN_SLC_MODE_EN (TRUE) //BicS3 A2 mode
#else
#define FW_RUN_SLC_MODE_EN (FALSE)
#endif /* (FW_CATEGORY_FLASH == FLASH_BICS4PSLC) */

#define CONFIG_DIRECT_D3_WRITE_EN	(TRUE && ((FLASH_TLC == FLASH_TYPE) || (FLASH_QLC == FLASH_TYPE)) && (!FW_RUN_SLC_MODE_EN))	//Reip Porting 3D-V7 QLC Add
#define  DIRECT_D3_LMU_FIRST_EN		(FALSE && CONFIG_DIRECT_D3_WRITE_EN) // ex : PCA arrangement Plane[4] LMU[3:2] Entry[1:0]

#if VS_SIM_EN
#define CONFIG_MULTI_PLANE_EN			(FALSE)
#define CONFIG_FLASH_CACHE_READ_EN		(FALSE)
#define TABLE_PROG_MULTIPLANE_EN 		(FALSE)
#define TABLE_PROG_MULTIPLANE_UART		(FALSE)
#else /* VS_SIM_EN */
#define CONFIG_MULTI_PLANE_EN			(TRUE && (!DIRECT_D3_LMU_FIRST_EN))
#define CONFIG_FLASH_CACHE_READ_EN		(FALSE)
#define CONFIG_FLASH_CACHE_PROG_EN	    (FALSE)
#define TABLE_PROG_MULTIPLANE_EN 		(TRUE && CONFIG_MULTI_PLANE_EN)
#define TABLE_PROG_MULTIPLANE_UART		(FALSE)
#endif /* VS_SIM_EN */

/**************************************************************************
 * 				TEST FLOW
 ***************************************************************************/
#define XZIP_TEST_FLOW_EN		(FALSE)

#define IOR_TEST_CHANGE_GSLL_EN		(FALSE && IOR_EN)

#define RETRY_FAIL_HANDLE                  (TRUE)
#define RETRY_FAIL_HANDLE_UPDATE_ERR_LOG    (TRUE && (!NCS_EN) && (!RDT_MODE_EN) && (!DISABLE_RS_FOR_GC_EN))
// PPS Tool
#define MICRO_SECONDS_OF_SETTING_GPIO0_TO_0		(10000)		// 10 milliseconds


/**********************************************************
 *  Plane Protection for RaidECCMap
 * *********************************************************/
#define MULTI_PLANE_PROTECTION    (TRUE && (!MICRON_FSP_EN) && !TWO_PASS_EN && (!RDT_MODE_EN))
/**********************************************************
 *  Two WL Protection
 * *********************************************************/
#define TWO_WORDLINE_PROTECTION_EN (TRUE)
/**********************************************************
 *  Block Protection
 **********************************************************/
#define RAIDECC_BLOCK_PROTECTION_EN (TRUE && IM_N48R)
/**********************************************************
 *  RaidECC use parity map
 * *********************************************************/
#define DEBUG_RAIDECC_USE_PARITY_MAP	(FALSE)	// debug use
/*****************************
 *  Power Cycle Simulation
 * ****************************/
#define SIM_POWER_EN									(FALSE && (!VS_SIM_EN) && (!BURNER_MODE_EN))
#define SIM_SPOR_EN                                     (FALSE && SIM_POWER_EN && (HOST_MODE == NVME))

/**************************************************************************
 * 				DEBUG SETTING
 ***************************************************************************/
#define ENABLE_RAMARR_DEBUG	TRUE

#if((FW_BUILD_VERSION == FW_VERSION_FULL_PERFORMANCE_XZIP_ON) || (FW_BUILD_VERSION == FW_VERSION_FULL_PERFORMANCE_XZIP_OFF))
#define DEBUG_CMD_LIFETIME	(FALSE && (!BURNER_MODE_EN)) //Incompatible with WORDLINE_FOLDING_EN
#else /* PERFORMANCE_TEST_EN */
#define DEBUG_CMD_LIFETIME ((TRUE && U17_EN) && (!BURNER_MODE_EN) && (!RDT_MODE_EN))
#endif /* PERFORMANCE_TEST_EN */

#define DEBUG_HOST_EVENT_SEQUENCE	(TRUE && (!BURNER_MODE_EN))
#define DEBUG_ERR_TD        ((TRUE) && (!RETRY_FAIL_HANDLE))
#define BUNRER_DEBUG_COMMAND (FALSE)
#define DEBUG_ERASEALL_UART_EN	(FALSE)

#if (PERFORMANCE_TEST_EN)
#define DEBUG_DOUBLE_PROG_EN		(FALSE)
#else /* PERFORMANCE_TEST_EN */
#define DEBUG_DOUBLE_PROG_EN		(FALSE && (!RELEASED_FW))
#endif /* PERFORMANCE_TEST_EN */

#define DEBUG_API_UART_EN   (FALSE)
#define ENABLE_INIT_UART	(FALSE)

#if (PERFORMANCE_TEST_EN)
#define DEBUG_RTT1		(TRUE && (!RDT_MODE_EN))
#else /* PERFORMANCE_TEST_EN */
#define DEBUG_RTT1		(FALSE && (!RELEASED_FW) && (!RDT_MODE_EN))
#endif /* PERFORMANCE_TEST_EN */

#define DEBUG_UART_TT_EN	(FALSE)

#define E21_TODO			(PS5021_EN) // E21 Porting Check
#define U17_TODO			(PS5017_EN) // U17 feature Check

#if (PERFORMANCE_TEST_EN)
#define DEBUG_UART_EN (FALSE || (INIT_MEASURE_EN))
#else /* PERFORMANCE_TEST_EN */
#define DEBUG_UART_EN ((TRUE && !RELEASED_FW) || (INIT_MEASURE_EN))
#endif /* PERFORMANCE_TEST_EN */

#define E21_HOST_CHECK			(PS5021_EN) // E21 Porting Check

/*(2020.10.15 @SamuelWang): For WAF cnt*/
#define DEBUG_WAF_CNT_EN	(FALSE)
#define ERROR_HANDLE_VALIDATION (FALSE && (IM_N48R) && (FW_CATEGORY_CUSTOMER == CUSTOMER_MICRON_NICKS))
/**************************************************************************
 * 				Test Gen Data by YT 20170921
 ***************************************************************************/
#if (FPGA_BOARD == FPGA_USE_V7)
#if (FPGA_RTL_2CH == TRUE)
#define	GEN_CQ_EN			(FALSE)
#define NO_HOST_BY_V7_EN	(FALSE)
#else /*(FPGA_RTL_2CH == TRUE)*/
#define	GEN_CQ_EN			(TRUE)
#define NO_HOST_BY_V7_EN	(TRUE)
#endif /*(FPGA_RTL_2CH == TRUE)*/
#define OVERLAY_EN			(TRUE)
#elif (FPGA_BOARD == FPGA_USE_ULTRA)
#define	GEN_CQ_EN			(FALSE)
#define NO_HOST_BY_V7_EN	(FALSE)
#define OVERLAY_EN			(TRUE) //(FALSE) //
#elif (FPGA_BOARD == FPGA_USE_SIM_CODE)
#define	GEN_CQ_EN			(FALSE)
#define NO_HOST_BY_V7_EN 	(FALSE)
#define OVERLAY_EN			(FALSE)
#else
#error "Unknown configuration (board setting)"
#endif /* (FPGA_BOARD == FPGA_USE_V7) */

#if (FLH_USE_IP == FLH_USE_COP0)
#define COP0_MODE_EN			(TRUE)
#define CHK_WRITE_CONSIST_EN	(FALSE)
#define CHK_JOURNAL_CONSIST_EN	(FALSE)
#define GEN_R_FAIL_ON_COP1_EN              (FALSE)
#define GEN_R_FAIL_EN			(FALSE)
#define		GEN_R_FAIL_PERCENTAGE	(1)
#define		GEN_R_FAIL_PERCENTAGE_BASE	(100000)
#if (CONFIG_FLASH_TYPE ==FLASH_TYPE_TOSHIBA_3D_TLC)
#define TOSHIBA_BISC3_FAST_READ_EN	(TRUE)
#else//(CONFIG_FLASH_TYPE ==FLASH_TYPE_TOSHIBA_3D_TLC)
#define TOSHIBA_BISC3_FAST_READ_EN	(FALSE)
#endif//(CONFIG_FLASH_TYPE ==FLASH_TYPE_TOSHIBA_3D_TLC)
#elif (FLH_USE_IP == FLH_USE_SIM_CODE)
#define COP0_MODE_EN			(FALSE)
#define CHK_WRITE_CONSIST_EN	(FALSE)
#define CHK_JOURNAL_CONSIST_EN	(FALSE)
#else
#error "Unknown configuration (FLH setting)"
#endif /* (FLH_USE_IP == FLH_USE_COP0) */

#if GEN_CQ_EN
#define GEN_RCQ_EN	(TRUE)
#else
#define GEN_RCQ_EN	(FALSE)
#endif /* GEN_CQ_EN */

#define FTL_INIT_EN	(TRUE)

#define CONFIG_BYPASS_CONVERSION_EN	(TRUE)

#if (KINGSTON_EN || RDT_MODE_EN || RDT_BURNER_MODE_EN)
#define VUC_PROTECT_EN (FALSE)
#else /*(KINGSTON_EN)*/
#if (HOST_MODE == SATA)
#define VUC_PROTECT_EN (TRUE)
#elif (HOST_MODE == USB) /* (HOST_MODE == SATA) */
#define VUC_PROTECT_EN (FALSE)
#else /* (HOST_MODE == SATA) */

#if E21_TODO
#define VUC_PROTECT_EN (FALSE)
#else /*E21_TODO*/
#define VUC_PROTECT_EN (TRUE && (!NCS_EN))
#endif/*E21_TODO*/
#endif /* (HOST_MODE == SATA) */
#endif /*(KINGSTON_EN)*/
#if (FW_CATEGORY_CUSTOMER == CUSTOMER_MICRON_NICKS)
#define VUC_MICRON_NICKS_EN (TRUE)
#else/* (FW_CATEGORY_CUSTOMER == CUSTOMER_MICRON_NICKS) */
#define VUC_MICRON_NICKS_EN (FALSE)
#endif/* (FW_CATEGORY_CUSTOMER == CUSTOMER_MICRON_NICKS) */
#define UNIFIED_LOG_DEMAND_EXTEND_LCA_EN (TRUE && (IM_N28 || IM_B47R || IM_B37R || IM_N48R) && (VUC_MICRON_NICKS_EN) && (!LPM3_LOADER) && !RDT_MODE_EN) //Unified Log demand extend LCA when burner calculate free unit.
#define UNIFIED_LOG_EN	(UNIFIED_LOG_DEMAND_EXTEND_LCA_EN && (IM_N48R)&&(!BURNER_MODE_EN) && !RDT_MODE_EN) //Unified Log switch for FW
#define VT_SWEEP_EN (TRUE && VUC_MICRON_NICKS_EN && !RDT_MODE_EN)
#define VUC_MICRON_COMMON_VS_COMMANDS_EN (FALSE && VUC_MICRON_NICKS_EN && !U17_TODO && !RDT_MODE_EN)
#define VUC_MICRON_NAND_VS_COMMANDS_EN	(TRUE && VUC_MICRON_NICKS_EN && !U17_TODO && !RDT_MODE_EN)
//============ Enable Uart VUC Mode ============
#define UART_VUC_MODE_EN			(RDT_UART_TRANSFER)

//***************************************************************************/

#define COP1_OLD_PCA_EN	 (FALSE) //jeffrey

//XZIP
#define	XZIP_DEDICATED_BUFFER_EN	(FALSE)	//peter 20161205	//jacky delete relative code @20180412

///======================For remove SoftBit code usage==========================
#define RETRY_SOFTBITT_FOR_SDK_EN                             (FALSE)       /// default FALSE

//=============================== TableUpdate ===============================


//=============================== TRIM ===============================
#if (BURNER_MODE_EN || RDT_MODE_EN)
#define TRIM_EN				(FALSE)
#else
#define TRIM_EN				(TRUE)
#endif
#define TRIM_LBA_EN			((TRIM_EN) && TRUE)//[True]:Support Trim LBAs [False]:Trim LCAs only
#define TRIM_INSERT_NODE_EN	((TRIM_EN) && TRUE)	//Set to allow Insert-Trim-Node flow
#define TRIM_COND_FG_EN		((TRIM_EN) && TRUE)	//Set to allow Foreground Trim Flow to send ST3 CMD "Trim Conditional"
#define TRIM_COND_BG_EN		((TRIM_EN) && TRUE)	//Set to allow Background Trim Flow to send ST3 CMD "Trim Conditional"
#define TRIM_HUGE_RANGE_OPTIMIZE_EN			((TRIM_EN) && TRUE) //Set to allow optimizing "Huge-Range" case (Avoid getting splitted into too many Ranges)
#define TRIM_TRY_SKIP_CMD_EN				((TRIM_EN) && TRUE) //E13-8253: Skip if Trim Ranges are already Trimmed
#define TRIM_TRY_SKIP_CMD_PREFORMAT_INIT_EN	(TRUE) //?????TRIM_EN?????FTLPreFormatVTInit?Burner Mode.
#define SPOR_TRIM_EN		((TRIM_EN) && TRUE)
#define TRIM_COUNT_TABLE	((TRIM_EN) && FALSE) //E13-8253: ?????define
//=============================== GC ===============================
#define GC_DIRECT_ADD_FREE_UNIT_EN		            (TRUE)
#if (MICRON_FSP_EN)
#if MICRON_TEST_EN
#if (FW_CATEGORY_FLASH == FLASH_N28_QLC)
#define GC_N28_DECREASE_COPYBUFFER_FOR_PLB1         (TRUE)	//Will program LUXT 4 pages(256KB) when valley check result is failed
#define GC_N28_REDUCE_SWAP_PARITY_FREQUENCY			((!XZIP_EN) && (FW_CATEGORY_CUSTOMER != CUSTOMER_MICRON_NICKS))	//Only can be on when GCXZip is OFF
#define GC_BUILD_GCSA_BY_PCA_GROUP_EN               (TRUE)
#define	GC_MAXIMIZE_READDATA_CMDCNT					(TRUE)
#elif (FW_CATEGORY_FLASH == FLASH_B47R_TLC || FW_CATEGORY_FLASH == FLASH_N48R_QLC || FW_CATEGORY_FLASH == FLASH_B37R_TLC) /* (FW_CATEGORY_FLASH == FLASH_N28_QLC) */ //zerio n48r add
#define GC_N28_DECREASE_COPYBUFFER_FOR_PLB1         (FALSE)
#define GC_N28_REDUCE_SWAP_PARITY_FREQUENCY			(FALSE)
#define GC_BUILD_GCSA_BY_PCA_GROUP_EN               (TRUE)
#define	GC_MAXIMIZE_READDATA_CMDCNT					(TRUE)
#else /* (FW_CATEGORY_FLASH == FLASH_B47R_TLC) */
#define GC_N28_DECREASE_COPYBUFFER_FOR_PLB1         (FALSE)
#define GC_N28_REDUCE_SWAP_PARITY_FREQUENCY			(FALSE)
#define GC_BUILD_GCSA_BY_PCA_GROUP_EN               (TRUE && !RDT_MODE_EN)
#define	GC_MAXIMIZE_READDATA_CMDCNT					(TRUE && !RDT_MODE_EN)
#endif /* (FW_CATEGORY_FLASH == FLASH_N28_QLC) */
#else /*MICRON_TEST_EN*/
#define GC_N28_DECREASE_COPYBUFFER_FOR_PLB1         (FALSE)
#define GC_N28_REDUCE_SWAP_PARITY_FREQUENCY			(FALSE)
#define GC_BUILD_GCSA_BY_PCA_GROUP_EN               (TRUE)
#define	GC_MAXIMIZE_READDATA_CMDCNT					(FALSE)
#endif /*MICRON_TEST_EN*/
#else /* (MICRON_FSP_EN) */
#if ((FW_CATEGORY_FLASH == FLASH_BICS4TLCHDR) || (FW_CATEGORY_FLASH == FLASH_BICS5TLC)|| (CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_QLC))//Duson Porting BICS5 Add BICS6 Add//zerio BICS8 Add//zerio bics6 qlc add
#define GC_N28_DECREASE_COPYBUFFER_FOR_PLB1         (FALSE)
#define GC_N28_REDUCE_SWAP_PARITY_FREQUENCY			(FALSE)
#define GC_BUILD_GCSA_BY_PCA_GROUP_EN               (TRUE)
#define	GC_MAXIMIZE_READDATA_CMDCNT					(TRUE)
#else /* ((FW_CATEGORY_FLASH == FLASH_BICS4TLCHDR) || (FW_CATEGORY_FLASH == FLASH_BICS5TLC)) */
#define GC_N28_DECREASE_COPYBUFFER_FOR_PLB1         (FALSE)
#define GC_N28_REDUCE_SWAP_PARITY_FREQUENCY			(FALSE)
#define GC_BUILD_GCSA_BY_PCA_GROUP_EN               (TRUE)
#define	GC_MAXIMIZE_READDATA_CMDCNT					(FALSE)
#endif /* ((FW_CATEGORY_FLASH == FLASH_BICS4TLCHDR) || (FW_CATEGORY_FLASH == FLASH_BICS5TLC)) */
#endif /* (MICRON_FSP_EN) */
#define GC_PCA_GROUP_BY_BUF_SIZE_EN				    (TRUE && (TRUE==GC_BUILD_GCSA_BY_PCA_GROUP_EN) && (TWO_PASS_EN || (FW_CATEGORY_FLASH == FLASH_N28_QLC) || (FW_CATEGORY_FLASH == FLASH_BICS4TLCHDR) || (FW_CATEGORY_FLASH == FLASH_BICS5TLC)|| (FW_CATEGORY_FLASH == FLASH_B47R_TLC)||(FW_CATEGORY_FLASH == FLASH_B37R_TLC)))
#define GC_PCA_GROUP_CONSIDER_SRC_UNIT_EN		    (TRUE && (TRUE==GC_BUILD_GCSA_BY_PCA_GROUP_EN) && (TWO_PASS_EN || (FW_CATEGORY_FLASH == FLASH_N28_QLC) || (FW_CATEGORY_FLASH == FLASH_BICS4TLCHDR) || (FW_CATEGORY_FLASH == FLASH_BICS5TLC)|| (FW_CATEGORY_FLASH == FLASH_B47R_TLC)||(FW_CATEGORY_FLASH == FLASH_B37R_TLC)))
#define GC_PCA_GROUP_SELECT_CURRENT_GROUP_EN		(TRUE && (TRUE==GC_BUILD_GCSA_BY_PCA_GROUP_EN) && (TWO_PASS_EN || (FW_CATEGORY_FLASH == FLASH_N28_QLC) || (FW_CATEGORY_FLASH == FLASH_BICS4TLCHDR) || (FW_CATEGORY_FLASH == FLASH_BICS5TLC)|| (FW_CATEGORY_FLASH == FLASH_B47R_TLC)||(FW_CATEGORY_FLASH == FLASH_B37R_TLC)))
#define GC_PCA_GROUP_CALCULATE_NEXT_GROUP_EN		(TRUE && (TRUE==GC_BUILD_GCSA_BY_PCA_GROUP_EN) && (TWO_PASS_EN || (FW_CATEGORY_FLASH == FLASH_B47R_TLC) || (FW_CATEGORY_FLASH == FLASH_B37R_TLC)))

#define GC_ZCODE_TABLE_DISTINGUISH_ZIP_EN           (TRUE)
#define GC_CHECK_READ_PLANE_CNT_EN                  (TRUE && (TRUE==GC_BUILD_GCSA_BY_PCA_GROUP_EN))
#define GC_PROCESS_COPY_DATA_BEFORE_BUILD_GCSA_EN   (TRUE && (TRUE==GC_BUILD_GCSA_BY_PCA_GROUP_EN))
#define GC_SCAN_ZCODE_NEED_WAIT_OVER_THRESHOLD_EN   (TRUE && (TRUE==GC_BUILD_GCSA_BY_PCA_GROUP_EN))
#define GC_CHECK_PROGRAM_NO_WAIT_ALL_READ_EN        (TRUE && (TRUE==GC_BUILD_GCSA_BY_PCA_GROUP_EN))
#define GC_FORCE_SWITCH_ZCODE_TABLE_EN              (TRUE && (TRUE==GC_BUILD_GCSA_BY_PCA_GROUP_EN) && (TRUE==GC_ZCODE_TABLE_DISTINGUISH_ZIP_EN))
#define GC_LEFT_BUILD_GCSA_OVER_FSP_THRESHOLD       (TRUE && (TRUE==GC_BUILD_GCSA_BY_PCA_GROUP_EN))
#define GC_PROGRAM_DATA_ALIGN_FSP_EN                (TRUE && (FALSE==GC_BUILD_GCSA_BY_PCA_GROUP_EN))

#define GC_BUILD_GCSA_BY_PCA_GROUP_DBG_UART_EN      (FALSE)
#define GC_STANDY_ABANDON_PLB_DATA_EN	(TRUE)
#define GC_COLLECT_NO_PTE_BMP_UNIT_EN (TRUE)

#define GC_N18_RS_SCRAMBLER_MOD		(TRUE&&((FW_CATEGORY_FLASH == FLASH_N18_QLC)||(FW_CATEGORY_FLASH == FLASH_N28_QLC)))

#define GC_B47R_WEAK_PAGE_FILL_DUMMY		(FALSE)
//=============================== WL ===============================
#define WL_FORCE_BY_GC_FLOW_EN (FALSE)

//=============================== UpdateTable ===============================
#define SPILT_TABLE_GC_TABLE_UPDATE		(TRUE)

//=============================== UpdateTable ===============================
//COP0
#define COP0_FEATURE_EN (TRUE) //WSHuang20161007
#if (COP0_FEATURE_EN)
#define TIE_OUT_EN  	(TRUE)
#define COP0_MULTITHREAD_EN	(TRUE)  //Ting20161108

//	Cop0 support 16K Read Feature or not, need check TagID/BufAddr/P4KBKUPInfo when disable
//	1: Enable,  Cop0TieIn Read API only push once Tie-in to Cop0 Queue.
//	0: After calling, Cop0TieIn Read API will push 4 times Tie-in to Cop0 Queue , please check your TagID Count
#if VS_SIM_EN
#define COP0_SUPPORT_16KREAD_EN     (FALSE)	// DYChen20170323
#else
#define COP0_SUPPORT_16KREAD_EN     (TRUE)
#endif

#define COP0_SERIAL_WRITE_EN	(TRUE)	//DYChen 20170329, cop0 support serial buffer mode with TypeA

#if (TIE_OUT_EN)
#define TIE_OUT_2_CPU   0
#define TIE_OUT_2_COP1  1
#endif /* TIE_OUT_EN */

#define SCHEDULE_EN (TRUE)
#define LINK_EN	(TRUE)
#if LINK_EN
#define LINK_WRR_EN	(TRUE)
#else /* LINK_EN */
#define LINK_WRR_EN	(FALSE)
#endif /* LINK_EN */
#define ANDES_EN	(TRUE)
#define COP0_QOS_EN	(FALSE)
#define P4KTABLE_EN	(TRUE)
#define P4K_BACKUP_EN (TRUE)
#else
#define TIE_OUT_EN	(FALSE)
#define SCHEDULE_EN	(FALSE)
#define LINK_EN		(FALSE)
#define LINK_WRR_EN	(FALSE)
#define ANDES_EN	(FALSE)
#define COP0_QOS_EN	(FALSE)
#endif /* COP0_FEATURE_EN */
#define DEBUG_COP0_EN	(FALSE)

#define CONNECT		(TRUE)  // Used by NandSim
#define PCA_RULE_EN	(TRUE)
#define READ_16K_EN	(TRUE)
#define FORCE_MAKE_FAIL_EN	(FALSE)
#define MULTI_PLANE_EN	(FALSE)
#define CROSS_PAGE_EN	(FALSE)
#define PRINT_RUT_EN	(FALSE)
#define LOCK_FEATURE_EN	(FALSE)
#define L4K_PROGRAM_EN	(TRUE)

#define TLC				_TLC_MODE_TSB_BICS
#define MULTIDIE_EN		(TRUE)
#define COPY_UNIT_TEST_EN	(FALSE) // CC_TEMP

#define GC_DEBUG_UART_EN	(TRUE)
#define GC_DYNAMIC_DEBUG_UART_EN	(FALSE)
#if (PERFORMANCE_TEST_EN)
#define DEBUG_FREEPOOL_EN 	(FALSE)
#else /* PERFORMANCE_TEST_EN */
#define DEBUG_FREEPOOL_EN 	(TRUE && (!RELEASED_FW) && (!RDT_MODE_EN))
#endif /* PERFORMANCE_TEST_EN */
// allen added
#define DEBUG_TABLE_UPDATE_FLOW		(FALSE)
#define DEBUG_TABLE_GC_FLOW			(FALSE)		// increase possibility of activating table GC
#define UART_TABLE_GC_FLOW			(TRUE)		// add UART for table GC flow
#define DEBUG_DATA_UNIT_VC			(FALSE)		// debug VC of data unit
#define DEBUG_TABLE_GC_CHANGE_UNIT	(FALSE)		// increase possibility of reach end of table unit when programming dirty PTE/PMD
#define USE_NEW_MEMORY_ALLOCATION	(TRUE)		// use 4K instead 16K in TableUpdate flow, do not allocate 4K for dirty PMD log in TableGC flow
#define DEBUG_GENERATE_SQ_FULL		(FALSE)		// for debug PCA manager mechanism (generate SQ full before program table)

//Update Table
#define UPDATE_TABLE_DEBUG_UART_EN  (FALSE)

//******************************************
//	ASIC Spec
//******************************************

#if VS_SIM_EN
#define MAX_CHANNEL			(2)
#else /* VS_SIM_EN */
#if (FPGA_RTL_2CH == TRUE)
#define MAX_CHANNEL			(2)
#else /*(FPGA_RTL_2CH == TRUE)*/
#define MAX_CHANNEL			(PS5017_EN ? 2 : 4) // S17=2, E13 = 4
#endif /*(FPGA_RTL_2CH == TRUE)*/
#endif /* VS_SIM_EN */
#define MAX_CE_PER_CHANNEL	(PS5017_EN ? 8 : 4) // S17=8, E13 = 4
#define MAX_DLL_CHANNEL			(2)
#define MAX_CE				(MAX_CHANNEL * MAX_CE_PER_CHANNEL)
#define MAX_LUN_NUM				(4)  // ????????4?LUN?SPEC
#define MAX_PHYSICAL_CE_PER_CH	(8)		// The Physical CE number of each channel
#define MAX_PHYSICAL_CE_NUM		(MAX_CHANNEL * MAX_PHYSICAL_CE_PER_CH)
#define FSEL_CE_NUM		(PS5017_EN ? 8 : 4)
#define FSEL_CH_NUM		(PS5017_EN ? 2 : 4)

#define E17_ALLIGN_E13_MAX_CH      			(4) //?????project(E13/E17/S13...)?align ??size?FW_SLOT_t, ???4(all project??channel?)
#define E17_ALLIGN_E13_MAX_PHYSICAL_CE_NUM	(E17_ALLIGN_E13_MAX_CH * MAX_PHYSICAL_CE_PER_CH)

//*****************************************
// Flash
//*****************************************
#if (MICRON_FSP_EN)
#define C6_ERASE_EN			(FALSE)
#define ADWLSV_EN           (TRUE && RELEASED_FW && (!IM_B47R) && (!IM_N48R) && (!IM_B37R))	//TODO B47R ,0610 turn off ADWLSV
#define ADWLSV_CHECK_SETFEATURE_EN    (TRUE)
#define ADWLSV_RANDOM_GEN_GET_FEATURE_FAIL_EN    (FALSE)
#define ADWLSV_TEST_CODE	(FALSE)
#elif (SANDISK_FSP_EN)
#define C6_ERASE_EN			(FALSE)//Duson Porting BICS5 Add
#define ADWLSV_EN 			(FALSE)
#else /* (MICRON_FSP_EN) */
#define C6_ERASE_EN			(TRUE)
#define ADWLSV_EN 			(FALSE)
#endif /* (MICRON_FSP_EN) */

#define VIRTUAL_ADDRESS_EN (IM_B47R || IM_N48R || IM_B37R) // nand support or not

#define FIP_SUPPORT_MT_VIRTUAL_ADDRESS_EN ((PS5021_EN || PS5017_EN) && VIRTUAL_ADDRESS_EN) // IC enable VA mode or not
/*
 * Note. 140S flash in S17 & E21 HW, Virtual Address value cover the high byte address(die address) of row address
 * Workaround method:By die add auto poll sequence
 */
#define FIP_VIRTUAL_ADDRESS_WORKAROUND_EN ((PS5021_EN || PS5017_EN) && FIP_SUPPORT_MT_VIRTUAL_ADDRESS_EN) // fix VA mode for multi die workaround

//*****************************************
//   SBRAID Prototype
//*****************************************
#define   RETRY_SBRAID_EN	(FALSE && (!BURNER_MODE_EN) && (!RDT_MODE_EN))
#define   RETRY_TURBO_RAIN_EN   (TRUE && IM_N48R && (!BURNER_MODE_EN) && (!RDT_MODE_EN))

//*****************************************
// coding style
//*****************************************
// REG NAME:	REG_IPname_REGname				8 bit address
// SHIFT: 		RegName_OffsetRegName_SHIFT		32 bit base
// MASK: 		RegName_OffsetRegName_MASK		32 bit base

#define R8_SHIFT(SHIFT) (SHIFT % 8)
#define R16_SHIFT(SHIFT) (SHIFT % 16)

//*****************************************
//
//*****************************************

#define CLEAF_EN (FALSE)

//N48R function unfinished yet
#define GC_SUSTAIN_EN					(TRUE && (!RDT_MODE_EN))//(FALSE)//
#define GC_SUSTAIN_MAX_DELAY_WORKAROUND (TRUE && (USB == HOST_MODE) && (IM_N48R)) //USB SPOR TEST: write 256M and power off per loop. power on and trigger WL, free pool may be empty.
#define GC_SUPPORT_PRESUSTAIN_EN		(TRUE && (PS5017_EN) && (IM_N48R) && (FW_CATEGORY_CUSTOMER == CUSTOMER_MICRON_NICKS))//(FALSE)//
#if (PERFORMANCE_TEST_EN || RDT_MODE_EN)
#define DEBUG_LOG_JOURNAL_EN    (FALSE && (!VRLC_EN))
#else /* PERFORMANCE_TEST_EN */
#define DEBUG_LOG_JOURNAL_EN    (FALSE && (!RELEASED_FW) && (!VRLC_EN))
#endif /* PERFORMANCE_TEST_EN */

#define RMA_LOG_SAVE_SYSTEM_INFO_EN		(FALSE)
//*****************************************
// SATA
//*****************************************

#define ERROR_HANDLE_COPYBLK_USE_GC_COPYBUF_EN      (IOR_EN && (CONFIG_FLASH_TYPE != FLASH_TYPE_TOSHIBA_3D_QLC))

#if (HOST_MODE == SATA)

/*=====================================================================================
                                     SATA Setting
=====================================================================================*/
#define SATA_ALLCMD_SYNC_MODE                   (FALSE) // Write & Read Cmd mill all become SYNC mode
#define SATA_AUTOFIS_EN                         (TRUE && (FALSE == SATA_ALLCMD_SYNC_MODE)) // Turn off Auto-FIS if all Cmd SYNC mode 

/* Init Setting */
#define SATA_E3D_EN                             (TRUE) // Enable data E3D compare
#define SATA_LINK_DATA_CHECK_EN                 (FALSE) // Enable to check the size of H2D in link layer
#define SATA_IGNORE_DMAT_EN                     (TRUE) // Enable to ignore DMAT
#define SATA_IDLE_TIMER_EN                      (TRUE) // Enable idle timer
#define SATA_INTERVAL_TMR_TIMER_EN              (TRUE) // Enable Interval timer timeout interrupt
#define SATA_CMD_PAUSE_GC_EN                    (TRUE) // Enable specified SATA NRW Cmd pause GC flow

/* SATA Phy Setting */
#define SATA_PHYSICAL_SET_EN                    (TRUE && ASIC && (!ICE_MODE_EN))
#define SATA_PHYSICAL_SET_SSC_MODE_EN           (TRUE && SATA_PHYSICAL_SET_EN)

/* IPM FW Feature */
#define IPM_DEFAULT_MODE                        (0) // NOT support DIPM_AUTO_SLUMBER, INIT_FORCE_AP2S, DIPM_AUTO_WAKE_P2S
#define DIPM_AUTO_SLUMBER                       (1) // DIPM auto slumber
#define INIT_FORCE_AP2S                         (2) // Always enable partial to slumber automatically when FW init
#define DIPM_AUTO_WAKE_P2S                      (3) // DIPM wake up from partial then to slumber automatically
#if(BG_LPM_EN)
#define IPM_MODE    (IPM_DEFAULT_MODE)
#else
#define IPM_MODE    (DIPM_AUTO_SLUMBER) // DIPM_AUTO_SLUMBER or INIT_FORCE_AP2S or DIPM_AUTO_WAKE_P2S, choose one only
#endif /* BG_LPM_EN */


/*=====================================================================================
                                Feature Support/Enable
=====================================================================================*/
/* Do NOT Modify */
#define GPL_SUPPORT                             (TRUE) /* General Purpose Logging (GPL) (see 4.10) */
#define DMA_SUPPORT                             (TRUE)
#define WCACHE_SUPPORT                          (TRUE)
#define PWDIS_SUPPORT                           (FALSE) /* Power Disable feature (see **********) */
#define     PWDIS_ALWAYS_EN                         (FALSE && PWDIS_SUPPORT) /* Power Disable feature always enabled (see *********.29) */
#define SSP_SUPPORT                             (TRUE) /* Software Settings Preservation (SSP) feature set (see 4.23) */
#define STREAMING_SUPPORT                       (FALSE) /* Streaming feature set (see 4.25) */
#define IDLE_UNLOAD_SUPPORT                     (FALSE) /* Unload feature (see 7.15.2.2) */

/* Special Define */
#define CFAST_SUPPORT                           (FALSE)
#define VENDOR_SET_PIO_CMD_EN                   (FALSE) /* Set Cmd to PIO type for CK tool */

/* Support or NOT */
#define FUA_SUPPORT                             (TRUE) /* Forced Unit Access (see 7.59.3.2) */
#define SMART_SUPPORT                           (TRUE) /* Self-Monitoring, Analysis, and Reporting Technology (SMART) feature set (see 4.21) */
#define     SMART_DEFAULT_ENABLE                    (TRUE && SMART_SUPPORT) /* Default smart enable for DM2008 (see S11) */
#define     SMART_ERROR_LOGGING_SUPPORT             (TRUE && SMART_SUPPORT) /* SMART error logging (see ********.26) */
#define     SMART_SELFTEST_SUPPORT                  (TRUE && SMART_SUPPORT) /* SMART SELFTEST (SEE ********.25) */
#define     SMART_OFFLINE_READ_SCANNING_SUPPORT     (TRUE && SMART_SELFTEST_SUPPORT) /* OFF-LINE READ SCANNING IMPLEMENTED (see ATA8-ACS-3 ********) */
#define     SMART_SELFTEST_SHORT_AND_EXTEND_SUPPORT (TRUE && SMART_SELFTEST_SUPPORT) /* SELF-TEST IMPLEMENTED (see ATA8-ACS-3 ********) */
#define     SMART_CONVEYANCE_SELFTEST_SUPPORT       (TRUE && SMART_SELFTEST_SUPPORT) /* CONVEYANCE SELF-TEST IMPLEMENTED (see ATA8-ACS-3 ********) */
#define     SMART_SELECTIVE_SELFTEST_SUPPORT        (TRUE && SMART_SELFTEST_SUPPORT) /* SELECTIVE SELF-TEST IMPLEMENTED (see ATA8-ACS-3 ********) */

#define APM_SUPPORT                             (TRUE) /* Advanced Power Management (APM) feature set (see 4.6) */
#if (KINGSTON_EN)
#define     APM_ENGAGE_DIPM_CONTROL_SUPPORT     (TRUE && APM_SUPPORT && DIPM_SUPPORT) /* APM engaged DIPM control support feature */
#else /* (KINGSTON_EN) */
#define     APM_ENGAGE_DIPM_CONTROL_SUPPORT     (FALSE && APM_SUPPORT && DIPM_SUPPORT) /* APM engaged DIPM control support feature */
#endif /* (KINGSTON_EN) */
#define AUTO_ACTIVATE_SUPPORT                   (TRUE) /* DMA Setup FIS Auto-Activate optimization (see *********) */
#define SATA_PHY_EVENT_CNT_LOG_SUPPORT          (TRUE) /* SATA Phy Event Counters log (see 9.15) */
#define SATA_FORCE_MAX_GENERATION_SUPPORT       (TRUE) /* Support force max SATA Gen speed by info block */
#define TRUSTCOMPUTE_SUPPORT                    (TCG_EN) /* Trusted Computing feature set (See 4.26) */

/* gSATAInfoBlk.uoFeature (That can be closed by setting info blk if FW default is Support) */
#define TRIM_SUPPORT                            (TRUE) /* General feature set (See 4.2) */
#define DLMC_SUPPORT                            (TRUE) /* General feature set (See 4.2) */
#define     DLMC_MODE_03_SUPPORT                    (TRUE && DLMC_SUPPORT)
#define     DLMC_MODE_07_SUPPORT                    (TRUE && DLMC_SUPPORT)
#define     DLMC_MODE_0E_0F_SUPPORT                 (TRUE && DLMC_SUPPORT)
#define LBA48BIT_SUPPORT                        (TRUE) /* 48-bit Address feature set (see 4.3) */
#define AMAX_SUPPORT                            (TRUE) /* Accessible Max Address Configuration feature set (see 4.4) */
#define DCO_SUPPORT                             (TRUE) /* Device Configuration Overlay (DCO) feature set (See ATA8-ACS-2 4.7) */
#define NCQ_SUPPORT                             (TRUE) /* Native Command Queuing (NCQ) feature set (See 4.11) */
#define HPA_SUPPORT                             (TRUE) /* Host Protected Area (HPA) feature set (See ATA8-ACS-2 4.11) */
#define     HPA_SECURITY_EXTENSIONS_SUPPORT         (TRUE && HPA_SUPPORT) /* HPA security extensions (See ATA8-ACS-2 4.11.2) */
#define SANITIZE_SUPPORT                        (TRUE) /* Sanitize Device feature set (See 4.18) */
#define     CRYPTO_SCRAMBLE_SUPPORT                       (FALSE && SANITIZE_SUPPORT && TCG_EN) // 0011h (AES change key TCG must be enable)
#define     BLOCK_ERASE_SUPPORT                           (FALSE && SANITIZE_SUPPORT) // 0012h
#define     OVERWRITE_SUPPORT                             (TRUE && SANITIZE_SUPPORT) // 0014h
#define         DEFINITIVE_ENDING_PATTERN_SUPPORT             (FALSE && OVERWRITE_SUPPORT) // TODO, (See ********.44)
#define     SANITIZE_ANTIFREEZE_LOCK_SUPPORT              (TRUE && SANITIZE_SUPPORT) // 0040h
#define     RESTRICTED_SANITIZE_OVERRIDES_SECURITY_ENABLE (FALSE && SANITIZE_SUPPORT)
#define SECURITY_SUPPORT                        (TRUE) /* Security feature set (see 4.20) */
#define     SECURITY_ENHANCE_ERASE_SUPPORT          (TRUE)
#define DEVSLP_SUPPORT                          (TRUE && LPM_EN) /* DEVSLP (See SATA3.3 8.5) */
#define     LPM_DEVSLP_SIGNAL_CHECK                (TRUE && DEVSLP_SUPPORT) //FW will check DEVSLP signal when set feature enable DEVSLP
#define HIPM_SUPPORT                            (TRUE && LPM_EN && (!RDT_MODE_EN)) /* Host Initiated Power Management (HIPM) (see *********.5) */
#define HAPS_SUPPORT                            (FALSE && HIPM_SUPPORT) /* Host Automatic Partial to Slumber transitions */
#define DIPM_SUPPORT                            (TRUE && LPM_EN) /* Device Initiated Power Management (DIPM) */
#define     DIPM_SSP_PRESERVE_SUPPORT               (TRUE && DIPM_SUPPORT)
#define     DIPM_WITH_IDLE_TMR_INT_SUPPORT          (TRUE && DIPM_SUPPORT && SATA_IDLE_TIMER_EN)
#define DAPS_SUPPORT                            (TRUE && DIPM_SUPPORT) /* Device Automatic Partial to Slumber transitions */
#define WWN_SUPPORT                             (TRUE) /* World Wide Name (See ******** ) */
#define WUNC_SUPPORT                            (TRUE) /* Write Uncorrectable (See ********.32) */
#define ZERO_SUPPORT                            (FALSE) /* Zero (see ********.41) */
#define DELAY_SDB_FIS_SUPPORT					(FALSE) /* Set to delay the action of sending SDB FIS */
#define DELAY_DMA_SUP_FIS_SUPPORT				(TRUE) /* Set to delay the action of sending DMA Setup FIS */
#define DSN_SUPPORT                             (FALSE) /* Device Statistics Notification (DSN) (see 4.7) */
#define SCT_SUPPORT                             (FALSE) /* SCT Command (see 8.0) */

#endif /* (HOST_MODE == SATA) */

#define B47R_IMPRINT_EN ((IM_B47R || !IM_N48R) && TRUE && (FW_CATEGORY_CUSTOMER == CUSTOMER_MICRON_NICKS) && !LPM3_LOADER && !U17_TODO)
#define B47R_IMPROVE_TR_SNAP_EN	((TRUE) && (B47R_IMPRINT_EN) && (FW_CATEGORY_CUSTOMER == CUSTOMER_MICRON_NICKS) && (!BURNER_MODE_EN))
#define MICRON_NICKS_PREPROGRAM_EN	(IM_N48R && (FW_CATEGORY_CUSTOMER == CUSTOMER_MICRON_NICKS) && BURNER_MODE_EN && (!PS5017_EN))

/*
 *  Note. In S17 E19 Backup P4K Flow, CMSG trigger MT and Send AXI to Modify P4K IRAM are parallel,
 *  Prevent FIP Execute MT Before AXI Finish,
 *  Program Flow Add FPU Delay Wait AXI Done (Over Maximum AXI Latency),
 *  Read Flow Add OPT Status notice FW wait AXI Done when Receive COP0 CQ.
 */
#define COP0_BACKUP_P4K_WORKAROUND	(PS5017_EN)

#define VT_ERASE_FIRST_EN      (FALSE)
#endif	/* _SETUP_H_ */
