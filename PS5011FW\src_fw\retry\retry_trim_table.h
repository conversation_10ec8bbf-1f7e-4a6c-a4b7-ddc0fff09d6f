#ifndef _RETRY_TRIM_TABLE_H_
#define _RETRY_TRIM_TABLE_H_

#include "typedef.h"
#include "mem.h"
#include <stddef.h>

#if (LOAD_RETRY_TRIM_TABLE_EN)
#define RETRY_TRIM_ADDRESS_NUM	(272)
#define RETRY_TRIM_TABLE_IN_IRAM_BIN_OFFSET (0x2000)
#define BFEA_BIN_OFFSET_TRIM_ADDRESS_NUM (45)
typedef struct RetryTrimTable          RETRY_TRIM_TABLE, *RETRY_TRIM_TABLE_PTR;

struct RetryTrimTable {
	U16 uwTrimAddress[RETRY_TRIM_ADDRESS_NUM];
	U8	ubTrimValue[RETRY_TRIM_ADDRESS_NUM];
	U16 uwBFEABinOffsetTrimAddress[BFEA_BIN_OFFSET_TRIM_ADDRESS_NUM];
	U8	ubBFEABinOffsetTrimValue[BFEA_BIN_OFFSET_TRIM_ADDRESS_NUM];
};

extern RETRY_TRIM_TABLE gRetryTrimTable;
extern RETRY_TRIM_TABLE_PTR gpulRetryTrimTable;
#endif /* (LOAD_RETRY_TRIM_TABLE_EN) */

#endif /* _RETRY_TRIM_TABLE_H_ */
