#ifndef _TEST_AOM_H_
#define _TEST_AOM_H_

#include "symbol.h"


#define AOM_GENERATE_READ_FAIL_EN      			(FALSE)
#define AOM_GENERATE_READ_FAIL_FULL_PATTERN_EN	(FALSE && AOM_GENERATE_READ_FAIL_EN)

/*
 * AOM Gen Fail Parameter
 */
#define AOM_GENERATE_READ_FAIL_NUMERATOR		(5)
#define AOM_GENERATE_READ_FAIL_DENOMINATOR		(1000)

/*
 * AOM Gen Fail Level
 *
 * Description :
 * Control UNC will be occurred on which types when reading them through
 * AOMCodeBankOverlayLoad() function.  "OR" all the levels you want to
 * gen UNC.
 *
 * For example :
 *     #define AOM_GENERATE_READ_FAIL_LEVEL    (AOM_GENERATE_READ_FAIL_LEVEL_BANK | AOM_GENERATE_READ_FAIL_LEVEL_LOADER | AOM_GENERATE_READ_FAIL_LEVEL_IDPAGE)
 */
#define AOM_GENERATE_READ_FAIL_LEVEL_BANK		(BIT0)
#define AOM_GENERATE_READ_FAIL_LEVEL_LOADER		(BIT1)
#define AOM_GENERATE_READ_FAIL_LEVEL_IDPAGE		(BIT2)
#define AOM_GENERATE_READ_FAIL_LEVEL			(AOM_GENERATE_READ_FAIL_LEVEL_BANK)


#endif /* _TEST_AOM_H_ */