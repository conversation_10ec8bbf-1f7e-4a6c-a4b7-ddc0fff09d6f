#ifndef _TEMPCO_API_H_
#define _TEMPCO_API_H_
#if VRLC_IN_MMO
U8 TempcoNandImprintCommand(void);
void TempcoChangeTableValue(void);
extern U8 gubTempcoProbeVersion;
#define TEMPCO_IMPRINT_DATA_SIZE	(64)
#define TEMPCO_MPPR_OFFSET (16)
#define TEMPCO_IMPRINT_DATA_SHIFT	(4)
#define TEMPCO_ES_SAMPLE_MPPR		(12)
#define TEMPCO_ES_SAMPLE_ADDRESS	(0x181)
#define TEMPCO_ES_SAMPLE_DATA	(0x03)
#define TEMPCO_OFFSET_PV_INDEX			(15)
#endif /*VRLC_IN_MMO*/
#endif /* _TEMPCO_API_H_ */
