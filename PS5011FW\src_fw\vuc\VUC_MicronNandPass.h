#ifndef _VUC_MicronNandPass_H_
#define _VUC_MicronNandPass_H_
#include "aom/aom_api.h"

#define VUC_MICRON_GNPT_HEADER_PAYLOAD_SIZE		(0x0C)
#define VUC_MICRON_GNPT_SUBCMD_GET_VARIABLE_LENGTH		(0x08)
#define VUC_MICRON_GNPT_SUBCMD_GET_VARIABLE_JSON_LENGTH	(0x0F)
#define VUC_MICRON_GNPT_SUBCMD_SET_VARIABLE_LENGTH		(0x08)

#define VUC_MICRON_GNPT_SUBCMD_NAND_MODE		(0xC0)
#define 	VUC_MICRON_EXIT_NAND_MODE				(0x00)
#define 	VUC_MICRON_ENTER_NAND_MODE				(0x01)
#define VUC_MICRON_GNPT_SUBCMD_READ				(0xC1)
#define VUC_MICRON_GNPT_SUBCMD_NAND_PRIMITIVE	(0xC2)
#define VUC_MICRON_GNPT_SUBCMD_INTERFACE		(0xC3)
#define VUC_MICRON_GNPT_SUBCMD_STATUS			(0xC4)
#define VUC_MICRON_GNPT_SUBCMD_GET_VARIABLE		(0xC6)
#define VUC_MICRON_GNPT_SUBCMD_SET_VARIABLE		(0xC7)
#define VUC_MICRON_GNPT_SUBCMD_DELAY			(0xC8)
#define VUC_MICRON_GNPT_SUBCMD_WP_CONTROL		(0xC9)
#define VUC_MICRON_GNPT_SUBCMD_TIMESTAMP		(0xCA)
#define VUC_MICRON_GNPT_SUBCMD_ACCESS_BUFFER	(0xCB)
#define VUC_MICRON_GNPT_SUBCMD_END				(0xFF)

#define VUC_MICRON_GNPT_SUBCMD_NORMAL_LENGTH	(0x10) // except 0xC6/0xC7

#define VUC_MICRON_READ_PARAMETER_PAGE_CODE		(0xEC)

#define VUC_MICRON_SET_TRIM_OP_CODE				(0xEB)
#define VUC_MICRON_GET_TRIM_OP_CODE				(0xEA)

// C2 cmd use
#define VUC_MICRON_GNPT_SUBCMD_NAND_PRIMITIVE_MAX_ADDRESS_AND_COMMAND_NUM	(8)
#define VUC_MICRON_GNPT_SUBCMD_NAND_PRIMITIVE_NO_COMMAND	(0)
#define VUC_MICRON_GNPT_SUBCMD_NAND_PRIMITIVE_ONE_COMMAND	(1)
#define VUC_MICRON_GNPT_SUBCMD_NAND_PRIMITIVE_TWO_COMMAND	(2)
#define VUC_MICRON_GNPT_SUBCMD_NAND_PRIMITIVE_TIME_BASE		(2)
#define VUC_MICRON_GNPT_SUBCMD_NAND_PRIMITIVE_SPECIAL_TIME	(32)

// C3 cmd use
#define VUC_MICRON_GNPT_SUBCMD_INTERFACE_LEGACY		(0x00)
#define VUC_MICRON_GNPT_SUBCMD_INTERFACE_NVDDR2		(0x01)
#define VUC_MICRON_GNPT_SUBCMD_INTERFACE_NVDDR3		(0x02)
#define VUC_MICRON_GNPT_SUBCMD_INTERFACE_DEFAULT	(0x00)

// C6 C7 cmd use
#define VUC_MICRON_GNPT_SUBCMD_VARIABLE_LENGTH_VERSION					(6)
#define VUC_MICRON_GNPT_SUBCMD_VARIABLE_LENGTH_wp						(2)
#define VUC_MICRON_GNPT_SUBCMD_VARIABLE_LENGTH_VERBOSITY				(9)
#define VUC_MICRON_GNPT_SUBCMD_VARIABLE_LENGTH_MAX_CH					(6)
#define VUC_MICRON_GNPT_SUBCMD_VARIABLE_LENGTH_MAX_CE					(6)
#define VUC_MICRON_GNPT_SUBCMD_VARIABLE_LENGTH_MIN_DELAY				(9)
#define VUC_MICRON_GNPT_SUBCMD_VARIABLE_LENGTH_INTERFACE_MODE			(14)
#define VUC_MICRON_GNPT_SUBCMD_VARIABLE_LENGTH_PATTERN_WRITE_SUPPORTED	(23)


// C9 cmd use write protect
#define VUC_MICRON_GNPT_SUBCMD_WRITE_PROTECT_ALL_CHANNEL	(0xFF)
#define VUC_MICRON_GNPT_SUBCMD_WRITE_PROTECT_ALL_CE			(0xFF)

// CB cmd use
#define VUC_MICRON_GNPT_SUBCMD_ACCESS_MEMORY_ONE_BYTE	(1)
#define VUC_MICRON_GNPT_SUBCMD_ACCESS_MEMORY_TWO_BYTE	(2)
#define VUC_MICRON_GNPT_SUBCMD_ACCESS_MEMORY_FOUR_BYTE	(4)

// Response use --- Data payload part
#define VUC_MICRON_GNPT_DATA_PAYLOAD_OFFSET		(12)
#define VUC_MICRON_GNPT_LAST_CMD_OFFSET_ID		(0x00000000)
#define VUC_MICRON_GNPT_LAST_CMD_OFFSET_ID_LENGTH	(4)
#define VUC_MICRON_GNPT_LAST_CMD_OFFSET_SIZE	(0x00000004)
#define VUC_MICRON_GNPT_LAST_CMD_OFFSET_SIZE_LENGTH	(4)
#define VUC_MICRON_GNPT_LAST_CMD_OFFSET_VALUE_LENGTH	(4)
#define VUC_MICRON_GNPT_SUB_CMD_STATUS_ID		(0x00000001)
#define VUC_MICRON_GNPT_SUB_CMD_STATUS_ID_LENGTH	(4)
#define VUC_MICRON_GNPT_SUB_CMD_STATUS_SIZE		(0x00000004)
#define VUC_MICRON_GNPT_SUB_CMD_STATUS_SIZE_LENGTH	(4)
#define VUC_MICRON_GNPT_SUB_CMD_STATUS_LENGTH	(1)
#define VUC_MICRON_GNPT_SUB_CMD_RESERVED_LENGTH	(3)
#define VUC_MICRON_GNPT_RESPONSE_PAYLOAD_ID		(0x00000002)
#define VUC_MICRON_GNPT_RESPONSE_PAYLOAD_ID_LENGTH		(4)
#define VUC_MICRON_GNPT_RESPONSE_PAYLOAD_SIZE_LENGTH	(4)

#define VUC_MICRON_GNPT_RESPONSE_PAYLOAD_OFFSET	(44)

// Response use --- Header part
#define VUC_MICRON_GNPT_HEADER_FORMAT_VERSION	(0x00)
#define VUC_MICRON_GNPT_HEADER_FORMAT_VERSION_LENGTH	(1)
#define VUC_MICRON_GNPT_HEADER_FORMAT_TYPE		(0x00)
#define VUC_MICRON_GNPT_HEADER_FORMAT_TYPE_LENGTH		(1)
#define VUC_MICRON_GNPT_HEADER_COMMAND_CLASS	(0x0004)
#define VUC_MICRON_GNPT_HEADER_COMMAND_CLASS_LENGTH		(2)
#define VUC_MICRON_GNPT_HEADER_COMMAND_CODE		(0x0016)
#define VUC_MICRON_GNPT_HEADER_COMMAND_CODE_LENGTH		(2)
#define VUC_MICRON_GNPT_HEADER_STATUS_LENGTH			(2)
#define VUC_MICRON_GNPT_HEADER_DATA_PAYLOAD_LENGTH		(4)

typedef enum {
	VUC_MICRON_GNPT_SUBCMD_DATA_WRITE_PATTERN_RANDOM = 0,
	VUC_MICRON_GNPT_SUBCMD_DATA_WRITE_PATTERN_FILL,
	VUC_MICRON_GNPT_SUBCMD_DATA_WRITE_PATTERN_WriteBUFFERADDRESS
} VUC_MICRON_GNPT_SUBCMD_DATA_WRITE_PATTERN;

#pragma pack(push, 1)
typedef union NANDPassThroughPayload {
	U8 ubByte[16];

	struct {
		// C0 CMD NAND Mode
		U8 ubOPCode;
		U8 ubMode;
		U8 ubReserved[14];
	} NANDMode;

	struct {
		// C1 CMD Read CMD
		U8 ubOPCode;
		U8 btResetReadBuf	: 1;
		U8 btTimeStamp		: 1;
		U8 btReturnType		: 1; // 0: raw data , 1: error bit number per frame
		U8 Reserved1		: 5;
		U8 ubReserved2[2];
		U32 ulReadCount;
		U8 ubBuf2Compare;
		U8 ubReserved[7];
	} ReadCMD;

	struct {
		// C2 CMD NANDPrimitive
		U8 ubOPCode;
		U8 ubChannel;
		U8 ubCE;
		U8 ubAddrLength		: 4;
		U8 ubCommandLength	: 4;
		U8 btDirection		: 1;
		U8 btResetReadBuf	: 1;
		U8 btPatternWrite	: 1;
		U8 btContinueOnFailure	: 1;
		U8 Reserved1		: 2;
		U8 btScrambler		: 1;
		U8 btECC			: 1;
		U16 uwDataLength;
		U8 ubDelay;
		U8 aubCommandAndAddrArray[VUC_MICRON_GNPT_SUBCMD_NAND_PRIMITIVE_MAX_ADDRESS_AND_COMMAND_NUM];
	} NANDPrimitive;

	struct {
		// C2 DATA Write Pattern
		U8 ubType;
		U8 ubReserved1;
		U8 ubLength;
		U8 ubReserved2;
		U32 ulParameter;
		union {
			U64 uoWriteBufferAddress;
			U8 ubPattern[8];
		} PATTERN;
	} WRITEPATTERN;

	struct {
		// C3 CMD Interface
		U8 ubOPCode;
		U8 ubInterfaceMode;
		U8 btDriveBaseSwitch: 1;
		U8 Reserved			: 7;
		U8 ubReserved[13];
	} Interface;

	struct {
		// C4 CMD Status
		U8 ubOPCode;
		U8 ubChannel;
		U8 ubCE;
		U8 AddrLength		: 4;
		U8 CMDLength		: 4;
		U32 ulTimeout;
		U8 aubPIOData[8];
	} Status;

	struct {
		// C6 CMD Get variable
		U8 ubOPCode;
		U8 ubReserved[3];
		U32 ulLength;
		U8 ubReserved1[8]; // JSON?
	} GetVariable;

	struct {
		// C7 CMD Set variable
		U8 ubOPCode;
		U8 ubReserved[3];
		U32 ulLength;
		U8 ubReserved1[8]; // JSON?
	} SetVariable;

	struct {
		// C8 CMD delay
		U8 ubOPCode;
		U8 ubReserved[3];
		U32 ulDelayTime;
		U8 ubReserved1[8];
	} Delay;

	struct {
		// C9 CMD WP control
		U8 ubOPCode;
		U8 ubChannel;
		U8 ubCE;
		U8 ubWPAssert;
		U8 ubReserved[12];
	} WPControl;

	struct {
		// CA CMD TimeStamp
		U8 ubOPCode;
		U8 btClearBuf	: 1;
		U8 ReservedBit	: 7;
		U8 ubReserved[14];
	} TimeStamp;

	struct {
		// CB CMD Read/Write Memory
		U8 ubOPCode;
		U8 btResetReadBuf	: 1;
		U8 btMaskEn			: 1;
		U8 btReadAfterWrite	: 1;
		U8 btReserved0		: 1;
		U8 AccessLength		: 4;
		U8 aubReserved[2];
		U32 ulAddr;
		U16 uwReadLength;
		U16 uwWriteLength;
		U8 aubData[4]; // there are more data after aubData[3]
	} MemoryAccess;

	struct {
		// FF CMD End cmd
		U8 ubOPCode;
		U8 aubReserved[15];
	} EndCMD;
} NANDPassThroughPayLoad;
#pragma pack(pop)


typedef struct {
	U8 ubHeaderFormatVersion;
	U8 ubDataFotmatVersion;
	U16 uwCMDClass;
	U16 uwCMDCode;
	U16 uwReserved;
	U32 ulDataPayloadSize;
} GNPTHEADER_t;

extern U32 gulResponseBufferAddr;
extern U32 gulGNPTResponsePayloadSize;
extern U32 gulGNPTLastCmdOffset;

#if ((FW_CATEGORY_CUSTOMER == CUSTOMER_MICRON_NICKS) && PS5017_EN)
extern U32 gulInputBufferAddr;
extern U32 gul16KDMABufferAddr;

extern U32 gulNandWriteOffset;
extern U32 gulNandReadOffset;
extern U8  gubGNPTBufferFlag;
#endif /*((FW_CATEGORY_CUSTOMER == CUSTOMER_MICRON_NICKS) && PS5017_EN)*/

AOM_VUC_3 void VUCMicronGNPTMangement(U32 ulPayloadAddr);

#endif /* _VUC_MicronNandPass_H_ */

