/*
 * rdt_init.c
 *
 *  Created on: 2020¦~2¤ë14¤é
 *      Author: user
 */


#include "rdt_api.h"
#include "ftl/ftl.h"
#include "hal/rs/rs_reg.h"
#include "burner/Burner.h"
#include "hal/pic/i2c/i2cm_api.h"

#if (HOST_MODE == SATA)
#include "hal/sata/sata_reg.h"
#elif (HOST_MODE == USB)
#include "hal/usb/usb_api.h"
#endif
#include "host_handler/hostevt_errhdl_api.h"
#include "host_handler/host_management_api.h"



#if RDT_MODE_EN

#if (RDT_RUN_ONLINE)
#include "hal/pic/uart/shr_hal_pic_uartxfer.h"
extern UART_STATUS *gpLastStatus;
#endif

#if CONFIG_RDT_OVER_ECC_EN
#if (MAX_CHANNEL == 2)//Jeffrey add
U16 guwRdtRecordRRMinEccbitInCh[MAX_CHANNEL] = {ECC_MAX_BFC_MASK, ECC_MAX_BFC_MASK};
#else
U16 guwRdtRecordRRMinEccbitInCh[MAX_CHANNEL] = {ECC_MAX_BFC_MASK, ECC_MAX_BFC_MASK, ECC_MAX_BFC_MASK, ECC_MAX_BFC_MASK};
#endif
#endif

#define IDB_SC4_THERMAL_SENSOR          (0x800 + 130)

const U16 guwRdtLEDOnOffDuration[RDT_LED_FLOW_STATE_NUM][2] = {
	//Off , On duration
	{0, 0xFFFF},//RDT_POWER_ON
	{100, 100},//RDT_INITIAL
	{0, 0},//RDT_DDR_TEST
	{500, 500},//RDT_SRAM_TEST
	{500, 500},//RDT_NAND_TEST
	{125, 125},//RDT_TEST_ERROR
	{500, 500},//RDT_WIN_TEST
	{500, 500},//RDT_IC_TEST
	{250, 250},//RDT_RR_FUN_TEST
	{0xFFFF, 0},//RDT_TEST_FINISH
};

void rdt_init_eot_setting(RDT_API_STRUCT_PTR rdt, U32 ecc_bit_threshold)
{
	//make rdt_api_cop0_express_read() turn on eot
	//rdt_api_cop0_read() without option SYS_BLK also turn on eot

	rdt->eot_threshold = ecc_bit_threshold;

	if ( ecc_bit_threshold < 250 ) {
		rdt_fip_set_ecc_threshlod(ecc_bit_threshold);
		R32_FALL[R32_FCTL_INT_CFG] &= ~(SGN_OFF_MSK_EN_BIT | NO_STOP_MTQ_OVER_ERROR_BIT);
		R32_FALL[R32_FCTL_INT_CFG] |= OVR_ERR_INT_EN_BIT;
		R32_FALL[R32_FCTL_MAP_CFG] |= OVER_ECC_ERR_EN_BIT;

		//turn on eot && trun off ultra dma
		R32_COP0[R32_COP0_ATTR2] |= (BIT17 | BIT27);//COP0 Attr_t bteot_chk_en -> Bit 27, btUltra_dma_dis -> Bit 17

		rdt->eot_detect = 1;
		M_UART(RDT_TEST_, "\nEOT enable, ECC threshold:%d", ecc_bit_threshold);

		M_ARM_SET_ULTRA_DMA_DIS();
	}
	else {
		rdt_fip_set_ecc_threshlod(0);
		R32_FALL[R32_FCTL_INT_CFG] |= SGN_OFF_MSK_EN_BIT;
		R32_COP0[R32_COP0_ATTR2] &= ~(BIT17 | BIT27);
		rdt->eot_detect = 0;
		M_ARM_CLR_ULTRA_DMA_DIS();

		M_UART(RDT_TEST_, "\nEOT disable.");
	}
}

static void rdt_fpl_init(FPL_API_STRUCT_PTR fpl)
{
	fpl->geometry.total_bank = ((gFlhEnv.ubCENumber) / (gFlhEnv.ubChannelExistNum));
	fpl->geometry.channel_per_bank = gFlhEnv.ubChannelExistNum;
#if SUPPORT_MULTI_DIE
	fpl->geometry.plane_per_die = gFlhEnv.PlanePerDie;
#else
	fpl->geometry.plane_per_die = gFlhEnv.ubPlanePerTarget;
#endif
	fpl->geometry.lmu_number = gubLMUNumber; //QLC?
	fpl->geometry.block_per_plane = gFlhEnv.ulBlockPerPlaneBank; //Need check

	fpl->geometry.page_size = gFlhEnv.uwPageByteCnt;
#if 0
	fpl->geometry.plane_per_vb =
		BIT(gPCARule_Plane.ubBit_No
			+ gPCARule_Channel.ubBit_No
			+ gPCARule_Bank.ubBit_No
			+ gPCARule_LUN.ubBit_No);  //Need check
#else
#if SUPPORT_MULTI_DIE
	fpl->geometry.plane_per_vb = gFlhEnv.ubCENumber * gFlhEnv.PlanePerDie; //Consider odd CE
#else
	fpl->geometry.plane_per_vb = gFlhEnv.ubCENumber * gFlhEnv.ubPlanePerTarget; //Consider odd CE
#endif
#endif
	fpl->geometry.d2_page_per_block = gFlhEnv.uwPagePerBlock;
	fpl->geometry.node_per_page = gub4kEntrysPerPlane;

	//Comman Case document by Hynix and TSB, and general case for micron
	//SLC = 0, MLC = 1, TLC = 2, QLC = 3
	//gFlhEnv.ulFlashDefaultType.BitMap.CellType   = (gFlhEnv.ubFlashID[2] & (BIT3 | BIT2)) >> 2;
	switch (gFlhEnv.ulFlashDefaultType.BitMap.CellType) {
	case FLH_SLC:
		fpl->geometry.d1_page_per_block = fpl->geometry.d2_page_per_block;
		break;
	case FLH_MLC:
		fpl->geometry.d1_page_per_block = fpl->geometry.d2_page_per_block >> 1;
		break;
	case FLH_TLC:
#if(FW_CATEGORY_FLASH == FLASH_SAMSUNG_V6P_TLC)
		fpl->geometry.d1_page_per_block = IM_SSV6P_CELLNUM_SLC;
#else
		fpl->geometry.d1_page_per_block = fpl->geometry.d2_page_per_block / 3;
#endif
		break;
	case FLH_QLC:
#if((FW_CATEGORY_FLASH == FLASH_YMTC_EMS_QLC))//ems mst add--karl
		fpl->geometry.d1_page_per_block = IM_EMS_CELLNUM_SLC;
#else
		fpl->geometry.d1_page_per_block = fpl->geometry.d2_page_per_block >> 2;
#endif
		break;
	default:
		fpl->flh_no_support = TRUE;
		break;
	}
	fpl->geometry.die_per_ce = gFlhEnv.ubLUNperTarget;


	M_UART(RDT_INIT_, "\nubPlanePerTarget=%b", gFlhEnv.ubPlanePerTarget);
	M_UART(RDT_INIT_, "\ntotal_bank=%b", fpl->geometry.total_bank);
	M_UART(RDT_INIT_, "\nchannel_per_bank=%b", fpl->geometry.channel_per_bank);
	M_UART(RDT_INIT_, "\nplane_per_die=%b", fpl->geometry.plane_per_die);
	M_UART(RDT_INIT_, "\nplane_per_vb=%l", fpl->geometry.plane_per_vb);
	M_UART(RDT_INIT_, "\nblock_per_plane=%l", fpl->geometry.block_per_plane);
	M_UART(RDT_INIT_, "\nd2_page_per_block=%l", fpl->geometry.d2_page_per_block);
	M_UART(RDT_INIT_, "\nd1_page_per_block=%l", fpl->geometry.d1_page_per_block);
	M_UART(RDT_INIT_, "\nCellType=%l", gFlhEnv.ulFlashDefaultType.BitMap.CellType);
	M_UART(RDT_INIT_, "\ndie_per_ce=%b", fpl->geometry.die_per_ce);
	M_UART(RDT_INIT_, "\nnode_per_page=%b", fpl->geometry.node_per_page);
	M_UART(RDT_INIT_, "\npage_size=%d", fpl->geometry.page_size);
	M_UART(RDT_INIT_, "\ngubLMUNumber=%b", gubLMUNumber);

}

static void rdt_log_blk_init(RDT_API_STRUCT_PTR rdt)
{
	rdt->current_state = RDT_STATE_RDT_LOG_INFORMATION_INITIAL;

	U8 i, j ;

	//U16 page_cnt;
	//U32 end_buff_addr;
	//FPL_GEOMETRY_STRUCT_PTR geometry;

	for ( i = RDT_LOG_DBT; i < RDT_LOG_NUM;  i++) {
		for ( j = 0; j < MAX_BLOCK_LOG; j++) { //AV8D have 1 backup
			rdt->log[i].log_block_addr[j] = INVALID_ADDR_VALUE;
		}
	}

	/* Init scan log bitmap */
	rdt->scan_log_bitmap = 0;

	//code block record init(code_block_addr no use now?)
	//rdt->sys_blk_record.code_block_cnt = 0;
	rdt->sys_blk_record.code_pointer_block_cnt = 0;
	for (i = 0; i < MAX_CODE_BLOCK_COPY; i++) { // TO DO: needs to define the number of the code blocks
		//rdt->sys_blk_record.code_block_addr[i] = INVALID_PCA_VALUE;
		rdt->sys_blk_record.code_pointer_block_addr[i] = INVALID_PCA_VALUE; //Penny
	}

	//bbm block record init
	rdt->sys_blk_record.bbm_block_cnt = 0;
	for (i = 0; i < MAX_SYSTEM_BLOCK_COPY; i++) { // TO DO: needs to define the number of the bbm blocks
		rdt->sys_blk_record.bbm_block_addr[i] = INVALID_PCA_VALUE;
	}

	//sys block record init (info block)
	rdt->sys_blk_record.sys_block_cnt = 0;
	for (i = 0; i < MAX_SYS_BLOCK_COPY; i++) { // TO DO: needs to define the number of the bbm blocks
		rdt->sys_blk_record.sys_block_addr[i] = INVALID_PCA_VALUE;
	}

	//ph block record init
	rdt->sys_blk_record.ph_block_cnt = 0;
	for (i = 0; i < MAX_PRODUCT_HISTORY_COPY; i++) { // TO DO: needs to define the number of the bbm blocks
		rdt->sys_blk_record.ph_block_addr[i] = INVALID_PCA_VALUE;
	}

	//E13 no dram
	//rdt->base.tmp_log_base = 0x40004000;
	//rdt->temp_write_buffer = DBUF_PB_RAM_ADDRESS;

	rdt->temp_write_buffer = BURNER_HOST_BIN_FILE_BASE;//RDT_FREE_BASE;
	rdt->temp_read_buffer = rdt->temp_write_buffer + PAGE_BYTE_SIZE;
	rdt->temp_info_block_buffer = rdt->temp_read_buffer + PAGE_BYTE_SIZE;
	rdt->temp_retry_buffer = rdt->temp_info_block_buffer + PAGE_BYTE_SIZE;
	rdt->base.temperature_base = rdt->temp_retry_buffer + PAGE_BYTE_SIZE;
	U32 tmp_size = MAX_TEMP_RECORD * sizeof(RDT_TEMPERATURE_STRUCT); //(2 + 64) * 4 = 264
	rdt->base.temperature_size =  M_BYTE_TO_32BYTE_ALIGN(tmp_size) * sizeof(U32) * BITS_PER_BYTE;// 9 * 32 = 288
#if 1
	rdt->base.all_log_base = rdt->base.temperature_base + PAGE_BYTE_SIZE;
#else //E13
	U32 tmp_size = MAX_TEMP_RECORD * sizeof(RDT_TEMPERATURE_STRUCT); //(2 + 64) * 4 = 264
	rdt->base.temperature_size =  M_BYTE_TO_32BYTE_ALIGN(tmp_size) * sizeof(U32) * BITS_PER_BYTE;// 9 * 32 = 288

	//rdt->base.all_log_base = DBUF_PB_RAM_ADDRESS + PAGE_BYTE_SIZE * 3 ;
	//rdt->base.all_log_base = RDT_FREE_BASE + PAGE_BYTE_SIZE * 3 ;
	//rdt->base.all_log_base = RDT_FREE_BASE + PAGE_BYTE_SIZE * 4 ;
	rdt->base.all_log_base = RDT_FREE_BASE + PAGE_BYTE_SIZE * 5;
#endif
	M_UART(RDT_INIT_, "\n temp_write_buffer : %l", rdt->temp_write_buffer);
	M_UART(RDT_INIT_, "\n rdt->temp_read_buffer : %l",  rdt->temp_read_buffer);
	M_UART(RDT_INIT_, "\n rdt->temp_buffer : %l", rdt->temp_info_block_buffer);
	M_UART(RDT_INIT_, "\n rdt->temp_retry_buffer : %l", rdt->temp_retry_buffer);
	M_UART(RDT_INIT_, "\n rdt->base.all_log_base : %l", rdt->base.all_log_base);

	/*** Clear DBUF ***/
	//rdt_dmac_setvalue(rdt, (U32)(0x0), (U32)(rdt->temp_write_buffer), (U32)(PAGE_BYTE_SIZE * 3));
	//rdt_api_dmac_setvalue(rdt, (U32)(0x0), (U32)(rdt->temp_write_buffer), (U32)(PAGE_BYTE_SIZE * 4));
	rdt_api_dmac_setvalue((U32)(0x0), (U32)(rdt->temp_write_buffer), (U32)(PAGE_BYTE_SIZE * 5));
}


static void rdt_log_struct_init(RDT_API_STRUCT_PTR rdt)
{
	rdt->current_state = RDT_STATE_RDT_LOG_INFORMATION_INITIAL;

	U32 tmp_size = 0, dbt_size = 0;
	U16 page_cnt;
	U32 end_buff_addr;

	rdt->log[RDT_LOG_DBT].buf_base = rdt->base.all_log_base;

	if (rdt->dbt.data_bbm_size == 0) {
#if SUPPORT_MULTI_DIE
		dbt_size = rdt->fpl->geometry.plane_per_vb * rdt->fpl->geometry.block_per_plane * rdt->fpl->geometry.die_per_ce;
#else
		dbt_size = rdt->fpl->geometry.plane_per_vb * rdt->fpl->geometry.block_per_plane;
#endif
	}
	else {
		dbt_size = rdt->dbt.data_bbm_size;
	}

	M_UART(RDT_DBG_, "\n header DBT size : %l; calculated DBT size: %l; dbt_size: %l", rdt->dbt.data_bbm_size, rdt->fpl->geometry.plane_per_vb * rdt->fpl->geometry.block_per_plane, dbt_size);

#if 0
	tmp_size = SHARED_DBUF_BBM_BUF_SIZE + SHARED_DBUF_BBM_BMCNT_SIZE + (rdt->geometry.plane_per_vb * rdt->vb_total) \
		+(rdt->vb_total * rdt->geometry.plane_per_vb / 8            /* bad block skip bitmap rdt no use*/) \
		+(rdt->vb_total * rdt->geometry.plane_per_vb * sizeof(U16)) + 0x8000; /*  bad block remap, rdt no use*/
	//+0x8000; //for Production experience
	tmp_size = SHARED_DBUF_BBM_BUF_SIZE + (rdt->fpl->geometry.plane_per_vb * rdt->vb_total) \
		+ 0x8000;  //for Production experience
#else
#if 0
	tmp_size = (rdt->fpl->geometry.plane_per_vb * rdt->vb_total) \
		+ 0x8000;
	//+0x8000; //for Production experience

	tmp_size = SHARED_DBUF_BBM_BUF_SIZE + (rdt->fpl->geometry.plane_per_vb * rdt->vb_total) \
		+ 0x8000;  //for Production experience
#endif
	tmp_size = SHARED_DBUF_BBM_BUF_SIZE + dbt_size;
	//tmp_size = SHARED_DBUF_BBM_BUF_SIZE + (rdt->fpl->geometry.plane_per_vb * rdt->vb_total);
	//tmp_size = PAGE_BYTE_SIZE + (rdt->fpl->geometry.plane_per_vb * rdt->vb_total); //DBT header
#endif

	//tmp_size = rdt->geometry.plane_per_vb * rdt->vb_total;
	rdt->log[RDT_LOG_DBT].buf_offset = tmp_size;
	page_cnt = rdt_api_page_align_count(tmp_size);
	rdt->log[RDT_LOG_DBT].buf_total_size = page_cnt * PAGE_BYTE_SIZE ;
	rdt->log[RDT_LOG_DBT].page_num = page_cnt;
	rdt->log[RDT_LOG_DBT].ping_pong_status[0] = FALSE;
	rdt->log[RDT_LOG_DBT].ping_pong_status[1] = FALSE;
	M_UART(RDT_DBG_, "\nDBT: vb_total=%l tmp_size=%l page_cnt:%l plane_per_vb=%l SHARED_DBUF_BBM_BUF_SIZE=%l", rdt->fpl->geometry.block_per_plane, tmp_size, page_cnt, rdt->fpl->geometry.plane_per_vb, SHARED_DBUF_BBM_BUF_SIZE);

	//ERL
	rdt->log[RDT_LOG_ERROR_RECORD].buf_base = rdt->log[RDT_LOG_DBT].buf_base +  rdt->log[RDT_LOG_DBT].buf_total_size;
	rdt->log[RDT_LOG_ERROR_RECORD].buf_offset = 0;
	rdt->log[RDT_LOG_ERROR_RECORD].buf_total_size = PAGE_BYTE_SIZE * 4; //PAGE_BYTE_SIZE * 20
	rdt->log[RDT_LOG_ERROR_RECORD].page_num = 0;
	rdt->log[RDT_LOG_ERROR_RECORD].ping_pong_status[0] = FALSE;
	rdt->log[RDT_LOG_ERROR_RECORD].ping_pong_status[1] = FALSE;

	//TDL
	rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].buf_base = rdt->log[RDT_LOG_ERROR_RECORD].buf_base +  rdt->log[RDT_LOG_ERROR_RECORD].buf_total_size;
	rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].buf_offset = 0;
	rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].buf_total_size = PAGE_BYTE_SIZE; //remove pingpong?
	rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].page_num = 0;
	rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].ping_pong_status[0] = FALSE;
	rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].ping_pong_status[1] = FALSE;

	//RML
	rdt->log[RDT_LOG_RDT_TESTMARK].buf_base = rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].buf_base +  rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].buf_total_size;
	rdt->log[RDT_LOG_RDT_TESTMARK].buf_offset = 0;
	rdt->log[RDT_LOG_RDT_TESTMARK].buf_total_size = PAGE_BYTE_SIZE; //remove pingpong?
	rdt->log[RDT_LOG_RDT_TESTMARK].page_num = 0;
	rdt->log[RDT_LOG_RDT_TESTMARK].ping_pong_status[0] = FALSE;
	rdt->log[RDT_LOG_RDT_TESTMARK].ping_pong_status[1] = FALSE;

	//FML
	rdt->log[RDT_LOG_FLASH_TESTMARK].buf_base = rdt->log[RDT_LOG_RDT_TESTMARK].buf_base +  rdt->log[RDT_LOG_RDT_TESTMARK].buf_total_size;
	rdt->log[RDT_LOG_FLASH_TESTMARK].buf_offset = 0;
	rdt->log[RDT_LOG_FLASH_TESTMARK].buf_total_size = PAGE_BYTE_SIZE; //remove pingpong?
	rdt->log[RDT_LOG_FLASH_TESTMARK].page_num = 0;
	rdt->log[RDT_LOG_FLASH_TESTMARK].ping_pong_status[0] = FALSE;
	rdt->log[RDT_LOG_FLASH_TESTMARK].ping_pong_status[1] = FALSE;

	//PRL
	rdt->log[RDT_LOG_POWER_RESUME].buf_base = rdt->log[RDT_LOG_FLASH_TESTMARK].buf_base +  rdt->log[RDT_LOG_FLASH_TESTMARK].buf_total_size;
	rdt->log[RDT_LOG_POWER_RESUME].buf_offset = 0;
	rdt->log[RDT_LOG_POWER_RESUME].buf_total_size = PAGE_BYTE_SIZE; //PAGE_BYTE_SIZE * 2
	rdt->log[RDT_LOG_POWER_RESUME].page_num = 0;
	rdt->log[RDT_LOG_POWER_RESUME].ping_pong_status[0] = FALSE;
	rdt->log[RDT_LOG_POWER_RESUME].ping_pong_status[1] = FALSE;
	end_buff_addr =  rdt->log[RDT_LOG_POWER_RESUME].buf_base + rdt->log[RDT_LOG_POWER_RESUME].buf_total_size;

	//TML
	rdt->log[RDT_LOG_TP_TESTMARK].buf_base = rdt->log[RDT_LOG_POWER_RESUME].buf_base +  rdt->log[RDT_LOG_POWER_RESUME].buf_total_size;
	rdt->log[RDT_LOG_TP_TESTMARK].buf_offset = 0;
	rdt->log[RDT_LOG_TP_TESTMARK].buf_total_size = PAGE_BYTE_SIZE; //remove pingpong?
	rdt->log[RDT_LOG_TP_TESTMARK].page_num = 0;
	rdt->log[RDT_LOG_TP_TESTMARK].ping_pong_status[0] = FALSE;
	rdt->log[RDT_LOG_TP_TESTMARK].ping_pong_status[1] = FALSE;
	end_buff_addr =  rdt->log[RDT_LOG_TP_TESTMARK].buf_base + rdt->log[RDT_LOG_TP_TESTMARK].buf_total_size;

#if RDT_RECORD_SCAN_WINDOW_LOG
	//WIN
	rdt->log[RDT_LOG_SCAN_WIN].buf_base = end_buff_addr;
	rdt->log[RDT_LOG_SCAN_WIN].buf_offset = 0;
	rdt->log[RDT_LOG_SCAN_WIN].buf_total_size = PAGE_BYTE_SIZE * 2;
	rdt->log[RDT_LOG_SCAN_WIN].page_num = 0;
	rdt->log[RDT_LOG_SCAN_WIN].ping_pong_status[0] = FALSE;
	rdt->log[RDT_LOG_SCAN_WIN].ping_pong_status[1] = FALSE;
	end_buff_addr = rdt->log[RDT_LOG_SCAN_WIN].buf_base + rdt->log[RDT_LOG_SCAN_WIN].buf_total_size;
#endif

#if RDT_RECORD_ECC_DISTRIBUTION
	//EDL
	rdt->log[RDT_LOG_ECC_DISTRIBUTION_LOG].buf_base = end_buff_addr;
	rdt->log[RDT_LOG_ECC_DISTRIBUTION_LOG].buf_offset = 0;
	rdt->log[RDT_LOG_ECC_DISTRIBUTION_LOG].buf_total_size = PAGE_BYTE_SIZE * 4;
	rdt->log[RDT_LOG_ECC_DISTRIBUTION_LOG].page_num = 0;
	rdt->log[RDT_LOG_ECC_DISTRIBUTION_LOG].ping_pong_status[0] = FALSE;
	rdt->log[RDT_LOG_ECC_DISTRIBUTION_LOG].ping_pong_status[1] = FALSE;
	end_buff_addr = rdt->log[RDT_LOG_ECC_DISTRIBUTION_LOG].buf_base + rdt->log[RDT_LOG_ECC_DISTRIBUTION_LOG].buf_total_size;
#endif


	rdt->base.all_log_size = end_buff_addr - rdt->base.all_log_base;

	/*** Clear DBUF ***/
	rdt_api_dmac_setvalue((U32)(0x0), (U32)(rdt->base.all_log_base), (U32)(rdt->base.all_log_size));
	//memset((U8 *)rdt->base.all_log_base, 0x00, rdt->base.all_log_size);
	M_UART(RDT_INIT_, "\n rdt->base.all_log_size : %l", rdt->base.all_log_size);
	//M_UART(RDT_INIT_, "\n BURNER_HOST_BIN_FILE_BASE : %l", BURNER_HOST_BIN_FILE_BASE);

	M_UART(RDT_DBG_, "\n DBT addr : %l", rdt->log[RDT_LOG_DBT].buf_base);
	M_UART(RDT_DBG_, "\n DBT size : %l", rdt->log[RDT_LOG_DBT].buf_total_size);
	M_UART(RDT_DBG_, "\n ERL addr : %l", rdt->log[RDT_LOG_ERROR_RECORD].buf_base);
	M_UART(RDT_DBG_, "\n ERL size : %l", rdt->log[RDT_LOG_ERROR_RECORD].buf_total_size);
	M_UART(RDT_DBG_, "\n TDL addr : %l", rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].buf_base);
	M_UART(RDT_DBG_, "\n TDL size : %l", rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].buf_total_size);
	M_UART(RDT_DBG_, "\n RML addr : %l", rdt->log[RDT_LOG_RDT_TESTMARK].buf_base);
	M_UART(RDT_DBG_, "\n RML size : %l", rdt->log[RDT_LOG_RDT_TESTMARK].buf_total_size);
	M_UART(RDT_DBG_, "\n FML addr : %l", rdt->log[RDT_LOG_FLASH_TESTMARK].buf_base);
	M_UART(RDT_DBG_, "\n FML size : %l", rdt->log[RDT_LOG_FLASH_TESTMARK].buf_total_size);
	M_UART(RDT_DBG_, "\n PRL addr : %l", rdt->log[RDT_LOG_POWER_RESUME].buf_base);
	M_UART(RDT_DBG_, "\n PRL size : %l", rdt->log[RDT_LOG_POWER_RESUME].buf_total_size);
	M_UART(RDT_DBG_, "\n TML addr : %l", rdt->log[RDT_LOG_TP_TESTMARK].buf_base);
	M_UART(RDT_DBG_, "\n TML size : %l", rdt->log[RDT_LOG_TP_TESTMARK].buf_total_size);
	M_UART(RDT_DBG_, "\n end_buff_addr : %l", end_buff_addr);
	M_UART(RDT_DBG_, "\n all_log_size : %l", rdt->base.all_log_size);

	M_FW_ASSERT(ASSERT_RDT_0x0847, end_buff_addr <= (BURNER_HOST_BIN_FILE_BASE + BURNER_HOST_BIN_FILE_SIZE));
}

static void rdt_rw_buf_init(RDT_API_STRUCT_PTR rdt)
{
	FPL_GEOMETRY_STRUCT geometry = rdt->fpl->geometry;
	U32 ulRWbufSize;
#if (MST_MODE_EN)
	ulRWbufSize =  PAGE_BYTE_SIZE * geometry.channel_per_bank * geometry.total_bank;
#else
	ulRWbufSize = (geometry.plane_per_die * geometry.channel_per_bank * PAGE_BYTE_SIZE) * geometry.total_bank;
#endif
	M_UART(RDT_DBG_, "\nulRWbufSize %l", ulRWbufSize);
#if 0
	//if (ulRWbufSize > (BURNER_FREE_SIZE + BURNER_BNKING_SIZE + BURNER_VENDOR_BUFFER_SIZE)) {
	if (ulRWbufSize > (BURNER_VENDOR_BUFFER_SIZE)) {  //Do not overwrite Burner_codebank
		rdt->SimplifiedFlashTest = 1; //Use single RW buf with same data
		UartPrintf("\nUse single RW buf with same data");
	}
	else {
		rdt->SimplifiedFlashTest = 0;
	}
#endif
#if 0//(MULTIPAGE_OPT_FOR_CACHE_PROG || MULTIPAGE_OPT_FOR_CACHE_READ || RDT_SIN_FULL_SPEED_PROG || RDT_SIN_FULL_SPEED_READ)
	rdt->SimplifiedFlashTest = 1; //force using single page rw buffer
#endif

	//rdt->write.data_buf_base = BURNER_FREE_BASE;
	//rdt->read.data_buf_base = BURNER_FREE_BASE;
	rdt->write.data_buf_base = rdt->base.all_log_base + rdt->base.all_log_size; //rdt->log[RDT_LOG_TP_TESTMARK].buf_base + rdt->log[RDT_LOG_TP_TESTMARK].buf_total_size;//rdt->base.rand_page_list_base;//BURNER_VENDOR_BUFFER_BASE;  //Do not overwrite Burner_codebank
	rdt->read.data_buf_base = rdt->base.all_log_base + rdt->base.all_log_size;//rdt->log[RDT_LOG_TP_TESTMARK].buf_base + rdt->log[RDT_LOG_TP_TESTMARK].buf_total_size;//rdt->base.rand_page_list_base;//BURNER_VENDOR_BUFFER_BASE;   //Do not overwrite Burner_codebank

#if 0
	if (rdt->SimplifiedFlashTest) {
		rdt->write.data_buf_size = PAGE_BYTE_SIZE;
		rdt->read.data_buf_size = PAGE_BYTE_SIZE;
	}
	else {
		rdt->write.data_buf_size = ulRWbufSize; // Add CE and no LMU
		rdt->read.data_buf_size = ulRWbufSize; // Add CE and no LMU
	}
#endif
	rdt->write.data_buf_size = ulRWbufSize; // Add CE and no LMU
	rdt->read.data_buf_size = ulRWbufSize; // Add CE and no LMU

	M_UART(RDT_INIT_, "\nwritebuf: %l size: %l", rdt->write.data_buf_base, rdt->write.data_buf_size);
	M_UART(RDT_INIT_, "\nreadbuf: %l size: %l", rdt->read.data_buf_base, rdt->read.data_buf_size);

	M_FW_ASSERT(ASSERT_RDT_0x0848, (rdt->read.data_buf_base + rdt->read.data_buf_size) < (BURNER_HOST_BIN_FILE_BASE + BURNER_HOST_BIN_FILE_SIZE));


}

static void rdt_random_list_init(RDT_API_STRUCT_PTR rdt)
{
	rdt->base.rand_block_list_base = rdt->write.data_buf_base + rdt->write.data_buf_size;//rdt->log[RDT_LOG_TP_TESTMARK].buf_base + rdt->log[RDT_LOG_TP_TESTMARK].buf_total_size;
	rdt->base.rand_block_list_size = rdt->fpl->geometry.block_per_plane * sizeof(U16);

	rdt->base.rand_page_list_base = rdt->base.rand_block_list_base + rdt->base.rand_block_list_size;
	rdt->base.rand_page_list_size = rdt->fpl->geometry.d2_page_per_block * sizeof(U16);

	M_UART(RDT_DBG_, "\n----RAND LIST ADDR-----");
	M_UART(RDT_DBG_, "\n RBL addr : %l", rdt->base.rand_block_list_base);
	M_UART(RDT_DBG_, "\n RBL size : %l", rdt->base.rand_block_list_size);
	M_UART(RDT_INIT_, "\n rand_page_list_base : %l", rdt->base.rand_page_list_base);
	M_UART(RDT_INIT_, "\n rand_page_list_size : %l", rdt->base.rand_page_list_size);

	/*** Clear DBUF ***/
	rdt_api_dmac_setvalue((U32)(0x0), (U32)(rdt->base.rand_block_list_base), (U32)(rdt->base.rand_block_list_size + rdt->base.rand_page_list_size));

	M_FW_ASSERT(ASSERT_RDT_0x0849, (rdt->base.rand_page_list_base + rdt->base.rand_page_list_size) <= (BURNER_HOST_BIN_FILE_BASE + BURNER_HOST_BIN_FILE_SIZE));
}


void rdt_zip_init(RDT_API_STRUCT_PTR rdt)
{
	//gulZIPPatternIndex = 0;
	rdt->base.zip_buffer_base = (rdt->base.rand_page_list_base + rdt->base.rand_page_list_size + PAGE_BYTE_SIZE) / PAGE_BYTE_SIZE * PAGE_BYTE_SIZE;
	rdt->base.zip_buffer_size = 2 * DEF_4K_FRAME; // source 4K + target 4K
	M_UART(RDT_INIT_, "\n zip_buffer_base : %l", rdt->base.zip_buffer_base);
	M_UART(RDT_INIT_, "\n zip_buffer_size : %l", rdt->base.zip_buffer_size);
}

/*
void rdt_api_sec_init(RDT_API_STRUCT_PTR rdt)
{
	//if (gubRDTSecInitialized) {
	//	return;
	//}
	rdt->base.sec_buffer_base = (rdt->base.zip_buffer_base + rdt->base.zip_buffer_size + PAGE_BYTE_SIZE) / PAGE_BYTE_SIZE * PAGE_BYTE_SIZE;
	rdt->base.sec_buffer_size = 4 * SIZE_4KB;

	sec_ram_init();
	//gulRDTSecTestLoop = 0;

	//gubRDTSecInitialized = 1;
}
*/

void rdt_rs_init(RDT_API_STRUCT_PTR rdt)
{
	//rdt->base.rs_parity_buffer_base = (rdt->base.sec_buffer_base + rdt->base.sec_buffer_size + PAGE_BYTE_SIZE) / PAGE_BYTE_SIZE * PAGE_BYTE_SIZE;
	rdt->base.rs_parity_buffer_base = rdt->base.sec_buffer_base + rdt->base.sec_buffer_size;
	rdt->base.rs_parity_buffer_size = RDT_RAIDECC_TEST_MAX_TAG * (PAGE_BYTE_SIZE + 64); // tag num * 16K bits

	M_FW_ASSERT(ASSERT_RDT_0x084A, (rdt->base.rs_parity_buffer_base + rdt->base.rs_parity_buffer_size) < DBUF_STATIC_BASE_ADDR);

	R32_RS[R32_RAIDECC_DBASE_0] = rdt->base.rs_parity_buffer_base;
	R32_RS[R32_RAIDECC_SBASE_0] = rdt->base.rs_parity_buffer_base + (RDT_RAIDECC_TEST_MAX_TAG * PAGE_BYTE_SIZE);

	R32_FCON[R32_FCON_FLH_FUNC] &= ~(RAIDECC_NO_DRAM_MODE_BIT); // RDT use DRAM mode
	R32_RS[R32_RAIDECC_MODE_0] &= ~(BIT(RAIDECC_BMU_EN_SHIFT)); // RDT disable BMU MODE
	R32_RS[R32_RAIDECC_MODE_0] &= ~(SET_RAIDECC_BMU_PRE_LOAD_ENABLE);

	M_UART(RDT_INIT_, " rs_parity_buffer_base : %l", rdt->base.rs_parity_buffer_base);
	M_UART(RDT_INIT_, " rs_parity_buffer_size : %l", rdt->base.rs_parity_buffer_size);
}

static void rdt_api_fill_default_param(RDT_API_STRUCT_PTR rdt)
{
	M_UART(RDT_TEST_, "\n load default rdt param \n");
	rdt->param.tlc_ecc_bit_threshold = 511;
	rdt->param.slc_ecc_bit_threshold = 200;
	rdt->param.sram_test_enable = 1;
	rdt->param.dram_test_enable = 0;
	rdt->param.flash_test_enable.flash_tlc_test_enable = 1;
	rdt->param.flash_test_enable.flash_slc_test_enable = 1;

	rdt->param.tlc_read_error_option.read_retry_enable = 1;
	rdt->param.slc_read_error_option.read_retry_enable = 1;

	rdt->param.sram_test_cycle = 1;
	rdt->param.dram_test_cycle = 1;
	rdt->param.flash_tlc_test_cycle = 2;
	rdt->param.flash_tlc_test_loop = 2; //2
	rdt->param.flash_slc_test_cycle = 2;
	rdt->param.flash_slc_test_loop = 1; //2

	rdt->param.host_link_time = 15; //5

	//rdt->param.rdt_fast_mode_all = 0x00; //ice mode dont need it , use config.
	//BIT0: flash short test
	//BIT5: fast sram test
	//BIT6: skip ic pattern

	rdt->param.hv_pattern_test_enable = 0x7F;

	//rdt->param.form_factor = 0;//M2_2280_CONF; //XLED

#if RDT_ERASEPAGE
	rdt->param.erasepage_count_zero_thrd = ERASEPAGE_COUNT_ZERO;
#endif
	rdt->voltage_setting = 0;//RDT_PS6103_CH2_VID_SETTING_0850V;

	rdt->param.ctl_fatal_temperature = 105;
}

static void rdt_param_init(RDT_API_STRUCT_PTR rdt)
{
	rdt->current_state = RDT_STATE_SET_PARAMETER_BY_IDPAGE;

	U8 got_rdt_info = FALSE;
	if (rdt->info_blk_find) {
		M_UART(RDT_TEST_, "\ninfo block (system block) found");
		memcpy((void *)&rdt->param, (void *)rdt->temp_info_block_buffer + 0xA00, SECTOR_SIZE);

		rdt->thermal_sensor = ((U8 *)rdt->temp_info_block_buffer)[IDB_SC4_THERMAL_SENSOR];
		M_UART(RDT_TEST_, "\nrdt->thermal_sensor = %x\n", rdt->thermal_sensor);

		if ((rdt->param.rdt_info_header[0] == 'R') && (rdt->param.rdt_info_header[1] == 'D') && (rdt->param.rdt_info_header[2] == 'T')) {
			got_rdt_info = TRUE;
			M_UART(RDT_TEST_, "\ngot rdt info");

#if RDT_ERASEPAGE
			if (!rdt->param.erasepage_count_zero_thrd) {
				rdt->param.erasepage_count_zero_thrd = ERASEPAGE_COUNT_ZERO;
			}
#endif
		}
	}

	if (!got_rdt_info) {
		rdt_api_fill_default_param(rdt);
	}

#if RDT_RECORD_ECC_DISTRIBUTION
	if (rdt->param.rdt_record_ecc_distribution.single_ce_test_enable) {
		gubCENumber = 1;
		rdt->fpl->geometry.total_bank = 1;
		rdt->fpl->geometry.channel_per_bank = 1;
	}
#endif

	if (RDT_FORCE_FAST_TEST) {
		M_UART(RDT_TEST_, "\n RDT_FORCE_FAST_TEST = %b", RDT_FORCE_FAST_TEST);
		rdt_api_fill_default_param(rdt);
		rdt->param.sram_test_enable = 1;
		rdt->param.dram_test_cycle = 0;
		rdt->param.flash_tlc_test_cycle = 1;
		rdt->param.flash_tlc_test_loop = 1; //2
		rdt->param.flash_slc_test_cycle = 1;
		rdt->param.flash_slc_test_loop = 1; //2

		rdt->param.host_link_time = 5; //5

		rdt->param.rdt_fast_mode_all = 0x23; //ice mode dont need it , use config.
		//BIT0: flash short test
		//BIT1: fast ctl test
		//BIT5: fast sram test
		//BIT6: skip ic pattern
	}

	//disable read retry
	//rdt->param.tlc_read_error_option.read_retry_enable = 1;
	//rdt->param.slc_read_error_option.read_retry_enable = 0;
	//rdt->param.ambient_temperature = 25;

	M_UART(RDT_TEST_, "\nrdt->param.sram_test_cycle = %d", rdt->param.sram_test_cycle);
	M_UART(RDT_TEST_, "\nrdt->param.rdt_fast_mode_all = 0x%b", rdt->param.rdt_fast_mode_all);
	M_UART(RDT_TEST_, "\nrdt->param.tlc_read_error_option.read_retry_enable = %d", rdt->param.tlc_read_error_option.read_retry_enable);
	M_UART(RDT_TEST_, "\nrdt->param.slc_read_error_option.read_retry_enable = %d", rdt->param.slc_read_error_option.read_retry_enable);
	M_UART(RDT_TEST_, "\nrdt->param.flash_test_enable.flash_slc_rdy_test_enable = %d", rdt->param.flash_test_enable.flash_slc_rdy_test_enable);
	M_UART(RDT_TEST_, "\nrdt->param.flash_test_enable.flash_tlc_rdy_test_enable = %d", rdt->param.flash_test_enable.flash_tlc_rdy_test_enable);
	M_UART(RDT_TEST_, "\nrdt->param.ScanWindowOption_All = 0x%b", rdt->param.ScanWindowOption_All);
	M_UART(RDT_TEST_, "\nrdt->param.retention_flow = %d", rdt->param.retention_flow);
	M_UART(RDT_TEST_, "\nrdt->param.flash_test_timeout_sec = %d", rdt->param.flash_test_timeout_sec);
	M_UART(RDT_TEST_, "\nrdt->param.ctl_fatal_temperature = %d", rdt->param.ctl_fatal_temperature);
	M_UART(RDT_TEST_, "\nrdt->param.ambient_temperature = %d", rdt->param.ambient_temperature);
	M_UART(RDT_TEST_, "\ngTT.ubTemperatureOffsetOnDieToFlash.ubAll = %u", gTT.ubTemperatureOffsetOnDieToFlash.ubAll);
	M_UART(RDT_TEST_, "\nrdt->param.check_temperature_timeout_sec = %d", rdt->param.check_temperature_timeout_sec);
}

static BOOL rdt_read_log_check_page_callback(TIEOUT_FORMAT_t uoResult, void *cb_obj, U32 ulPCA)
{
	RDT_API_STRUCT *rdt = (RDT_API_STRUCT *)cb_obj;

	//U8 ubResult = TRUE;
	//rdt->done_cnt ++;

	if (uoResult.HL.B32_to_B63.Info.btMSG_TYPE) {
		rdt->read_log_empty = TRUE;
		return TRUE;
	}

	return TRUE;
}

static BOOL rdt_read_log_check_page(RDT_API_STRUCT_PTR rdt, U32 log_pca, U8 log_id)
{

	FPL_GEOMETRY_STRUCT_PTR geometry = &rdt->fpl->geometry;
	//PCA_RULE_STRUCT_PTR p_rule = &rdt->fpl->pca_rule[1];
	U32 data_buf;// = SHARED_DBUF_BASE;

	U32 pca;
	U16 page;
	//U8 rank, ch;

	U8 slc_mode = LOG_BLOCK_SLC_MODE;
	U8 pca_rule = 0;
	//PCA_t ulPCA;
	U32 ulLocalPCA;

	//rdt->trig_cnt = 0;
	//rdt->done_cnt = 0;
	rdt->read_log_empty = FALSE;
	//COP0_READ_PAGE_BUF page_buf_read;

#if RDT_RETRY_CNT_ENABLE
	BOOL retry_cnt_get_back = FALSE;
#endif

	/* Choose PCA rule */
	pca_rule = PCA_RULE(slc_mode);

#if 1
	if (log_id == RDT_LOG_RDT_TESTMARK) {
		data_buf = rdt->log[RDT_LOG_RDT_TESTMARK].buf_base; //pre load rml, use to check error bit??? need to check?
	}
	else {
		data_buf = rdt->temp_read_buffer;
	}
#else
	data_buf = rdt->temp_read_buffer;
#endif

	rdt->log[log_id].page_offset_in_block = RDT_LOG_HEADER;

	M_UART(RDT_DBG_, "databuf = %x", data_buf);
	for (page = RDT_LOG_HEADER; page < geometry->d1_page_per_block; page++) {
		//UartPrintf("page = %l", page);
		//for (lc = 0; lc < PAGE_SIZE; lc++)// the number of LC in a page
		{
			//pca = log_pca | (page << rdt->pca_rule->rule_page.shift) | (lc << rdt->pca_rule->rule_node.shift);
			pca = log_pca | (GET_FPAGE(page) << gPCARule_Page.ubShift[pca_rule]);

			memset((void *)data_buf, 0x00, PAGE_BYTE_SIZE);

			//ulPCA.FormatFor512GB.ulPCA = pca;
			ulLocalPCA = pca;

			//cop0_api_read(rdt_link_handle_read_log_callback, (U32)(ulPCA.FormatFor512GB.ulPCA), rdt, slc_mode, (U32)data_buf, 1);
			rdt_api_cop0_read(rdt_read_log_check_page_callback, (U32)ulLocalPCA, rdt, slc_mode, (U32)data_buf, SYS_BLK);

			/* Recieve all erase CQ */
			rdt_api_delegate_CQ(BUF_TYPE_READ);

#if 0
			memcpy(&page_buf_read, &rdt->burnin.page_buf_read, sizeof(page_buf_read));
			page_buf_read.pca.fields.pca_l = 0;
			page_buf_read.pca.fields.pca_h = 0;
			page_buf_read.pca.u64 |= pca;

			COP0_SET_PHY_BUFFER(&page_buf_read.buf, data_buf);

			while (!cop0_api_read(rdt->cop0, rdt_link_handle_read_log_callback, pca, rdt, &page_buf_read, COP0_API_OPT_SLC_MODE | COP0_API_OPT_PRIORITY(COP0_ATTR_QLINK_H0) | COP0_API_OPT_MT_TEMPL(MT_TEMPLATE_R_NO_COMP) | COP0_API_OPT_RMP_BYPASS )) {
				C1_THREAD_YIELD();
			}

			rdt->trig_cnt ++;

			while (rdt->trig_cnt != rdt->done_cnt) {
				C1_THREAD_YIELD();
			}

			if (rdt->read_log_empty) {
				return TRUE;
			}
#endif

			if (rdt->read_log_empty) {
#if RDT_RETRY_CNT_ENABLE
				if (log_id == RDT_LOG_POWER_RESUME && (retry_cnt_get_back)) {
					M_UART(RDT_TEST_, "\n Power Resume Retry Count\n");
					U32 i;
					U32 *retry_count_per_ce = (U32 *)(rdt->log[RDT_LOG_POWER_RESUME].buf_base + SECTOR_SIZE);
					for (i = 0; i < 32; i++) {
						M_UART(RDT_TEST_, "%l ", retry_count_per_ce[i]);
					}
				}
#endif
				M_UART(RDT_DBG_, "log empty");
				return TRUE;
			}

#if RDT_POWER_RESUME
			if (log_id == RDT_LOG_POWER_RESUME) {

				//decide next state when power cycling occur
				PRL_LOG_STRUCT_PTR prl_buffer = (PRL_LOG_STRUCT_PTR)data_buf;
				rdt->rdt_test_flow_index = prl_buffer->prl_state;
				rdt->test_in_normal_power_mode = prl_buffer->test_in_normal_power_mode;//U17
				gulAddPowerOnTime = prl_buffer->ulTotalPowerOnTime;
				//UartPrintf("\n Last Power On Time = %d ", gulAddPowerOnTime);

				if (rdt->rdt_test_flow_index == RDT_TEST_FLOW_FINISH_INDEX) {
					continue;
				}

				if (rdt->rdt_test_flow[rdt->rdt_test_flow_index] == PRL_STATE_TLC_FLASH_TEST) {
					rdt->power_resume_tlc_loop++;
					rdt->inversion_flag = prl_buffer->inversion_flag;
				}

				if (rdt->rdt_test_flow[rdt->rdt_test_flow_index] == PRL_STATE_SLC_FLASH_TEST) {
					rdt->power_resume_slc_loop++;
					rdt->inversion_flag = prl_buffer->inversion_flag;
				}

				if (rdt->rdt_test_flow[rdt->rdt_test_flow_index] == PRL_STATE_START_TEST) {
					memcpy((void *)rdt->port_id, (void *)prl_buffer->port_id, sizeof(rdt->port_id));
					M_UART(RDT_TEST_, "\n get port id : ");
					for (U8 tt = 0; tt < 20; tt++) {
						M_UART(RDT_TEST_, " %b", rdt->port_id[tt]);
					}
				}

#if RDT_RETRY_CNT_ENABLE
#if 1
				if ( (rdt->rdt_test_flow[rdt->rdt_test_flow_index] == PRL_STATE_TLC_FLASH_TEST) || (rdt->rdt_test_flow[rdt->rdt_test_flow_index] == PRL_STATE_SLC_FLASH_TEST)) {
					U32 uli;
					U32 *retry_cnt_per_ce = (U32 *)(rdt->log[RDT_LOG_POWER_RESUME].buf_base + SECTOR_SIZE);
					for (uli = 0; uli < 32; uli++) {
						retry_cnt_per_ce[uli] = *(U32 *)((data_buf + SECTOR_SIZE) + uli * 4);
					}
					retry_cnt_get_back = TRUE;
				}
#else
				U32 uli;
				U32 *retry_cnt_per_ce = (U32 *)(rdt->log[RDT_LOG_POWER_RESUME].buf_base + SECTOR_SIZE);
				M_UART(RDT_TEST_, "\n Power Resume Retry Count\n");
				for (uli = 0; uli < 32; uli++) {
					retry_cnt_per_ce[uli] = *(U32 *)((data_buf + SECTOR_SIZE) + uli * 4);
					M_UART(RDT_TEST_, "%l ", retry_cnt_per_ce[uli]);
				}
#endif
#endif
			}
#endif

			if ( log_id == RDT_LOG_RDT_TESTMARK ) {
				rdt_parse_log(RDT_LOG_RDT_TESTMARK, data_buf);
			}

#if RDT_RECORD_SCAN_WINDOW_LOG
			if (log_id == RDT_LOG_SCAN_WIN) {
				rdt_parse_log(RDT_LOG_SCAN_WIN, data_buf);
			}
#endif

#if RDT_RECORD_ECC_DISTRIBUTION
			if (log_id == RDT_LOG_ECC_DISTRIBUTION_LOG) {
				rdt_parse_log(RDT_LOG_ECC_DISTRIBUTION_LOG, data_buf);
			}
#endif

#if 0 //remove RML
			if ( (log_id == RDT_LOG_RDT_TESTMARK) ) { //pre load rml, parse DDR training
				if ( (*(U32 *)data_buf == RML_IMARK_DDR_TEST_END) ) {
					if (rdt->param.dram_test_enable && rdt->param.dram_test_cycle && rdt->param.dram_scan_window_enable) {
						//memcpy((void *)&rdt->ddr_sys_info,(U32 *)(data_buf+64),sizeof(SYSINFO_DDR_RESULT_STRUCT));
#if 1
						rdt->ddr_sys_info.burner.mdll = *(U8 *)(data_buf + 64);
						rdt->ddr_sys_info.rdt_scanwindow.mdll =  *(U8 *)(data_buf + 33 + 64);


						for (rank = 0; rank < 4; rank++) {
							for (ch = 0; ch < 2; ch++) {
								rdt->ddr_sys_info.burner.read_shift_left[rank][ch] = *(U8 *)(data_buf + 1 + 64 + (rank * 8) + (ch * 4));
								rdt->ddr_sys_info.burner.read_shift_right[rank][ch] = *(U8 *)(data_buf + 2 + 64 + (rank * 8) + (ch * 4));
								rdt->ddr_sys_info.burner.write_shift_left[rank][ch] = *(U8 *)(data_buf + 3 + 64 + (rank * 8) + (ch * 4));
								rdt->ddr_sys_info.burner.write_shift_right[rank][ch] = *(U8 *)(data_buf + 4 + 64 + (rank * 8) + (ch * 4));

								rdt->ddr_sys_info.rdt_scanwindow.read_shift_left[rank][ch] = *(U8 *)(data_buf + 34 + 64 + (rank * 8) + (ch * 4) );
								rdt->ddr_sys_info.rdt_scanwindow.read_shift_right[rank][ch] = *(U8 *)(data_buf + 35 + 64 + (rank * 8) + (ch * 4) );
								rdt->ddr_sys_info.rdt_scanwindow.write_shift_left[rank][ch] = *(U8 *)(data_buf + 36 + 64 + (rank * 8) + (ch * 4) );
								rdt->ddr_sys_info.rdt_scanwindow.write_shift_right[rank][ch] = *(U8 *)(data_buf + 37 + 64 + (rank * 8) + (ch * 4) );
							}
						}
#endif


						UART_OUT_STR_DEC("\n BURNER training  mdll:", rdt->ddr_sys_info.burner.mdll, 0);

						for (rank = 0; rank < 4; rank++) {
							for (ch = 0; ch < 2; ch++) {
								UART_OUT_STR_DEC("\n rank =", rank, FALSE);
								UART_OUT_STR_DEC(" ch =", ch, FALSE);
								UART_OUT_STR_DEC(" write L =", rdt->ddr_sys_info.burner.write_shift_left[rank][ch], FALSE);
								UART_OUT_STR_DEC(" write R =", rdt->ddr_sys_info.burner.write_shift_right[rank][ch], FALSE);
								UART_OUT_STR_DEC(" read L =", rdt->ddr_sys_info.burner.read_shift_left[rank][ch], FALSE);
								UART_OUT_STR_DEC(" read R =", rdt->ddr_sys_info.burner.read_shift_right[rank][ch], FALSE);
							}
						}

						UART_OUT_STR_DEC("\n RDT scan_window  mdll:", rdt->ddr_sys_info.rdt_scanwindow.mdll, 0);
						for (rank = 0; rank < 4; rank++) {
							for (ch = 0; ch < 2; ch++) {
								UART_OUT_STR_DEC("\n rank =", rank, FALSE);
								UART_OUT_STR_DEC(" ch =", ch, FALSE);
								UART_OUT_STR_DEC(" write L =", rdt->ddr_sys_info.rdt_scanwindow.write_shift_left[rank][ch], FALSE);
								UART_OUT_STR_DEC(" write R =", rdt->ddr_sys_info.rdt_scanwindow.write_shift_right[rank][ch], FALSE);
								UART_OUT_STR_DEC(" read L =", rdt->ddr_sys_info.rdt_scanwindow.read_shift_left[rank][ch], FALSE);
								UART_OUT_STR_DEC(" read R =", rdt->ddr_sys_info.rdt_scanwindow.read_shift_right[rank][ch], FALSE);
							}
						}
					}
					else {
						UART_OUT_STR("\n didn't do scan window");
					}
				}
			}

			if (log_id == RDT_LOG_RML) { //pre load rml, use to check error bit
				DBG_OUT_STR_HEX("data_buf =", *((U32 *)data_buf), TRUE);
				data_buf += LC_BYTE_SIZE;
			}
#endif
		}


		rdt->log[log_id].page_offset_in_block++;
	}

	return TRUE;
}

static void rdt_check_each_log_page_offset(RDT_API_STRUCT_PTR rdt)
{
	rdt->current_state = RDT_STATE_CHECK_EACH_LOG_PAGE_OFFSET;
	U8 i, j;

	for (i = RDT_LOG_DBT; i < RDT_LOG_NUM; i++) {

		if (i == RDT_LOG_ERROR_RECORD_2) {
			continue;
		}
		if (i >= RDT_LOG_ECC_DISTRIBUTION_LOG2 && i <= RDT_LOG_ECC_DISTRIBUTION_LOG8) {//zerio change
			continue;
		}

		if (rdt->scan_log_bitmap & (0x01ULL << i)) { //has 0~1 log block
			for (j = 0; j < MAX_BACKUP_LOG_BLOCK_NUM; j++) {
				if (rdt->log[i].log_block_addr[j] != INVALID_PCA_VALUE) {
					M_UART(RDT_DBG_, "read log[%b", i);
					M_UART(RDT_DBG_, "].addr[%b", j);
					M_UART(RDT_DBG_, "]\n");
					if (rdt_read_log_check_page(rdt, rdt->log[i].log_block_addr[j], i)) {
						M_UART(RDT_DBG_, "\n1:rdt->log[%b].page_offset_in_block=%l", i, rdt->log[i].page_offset_in_block);
						break;
					}
				}
			}
		}
	}
}

static void check_later_bad_into_dbt(RDT_API_STRUCT_PTR rdt)
{
	U32 pca, tmp_pca, data_buf, laterbad_pca;
	U8 bank, die, ch, plane, lc;
	U16 page, block;
	U8 error_type, bbm_type, slc_mode, state;
	U16 block_index, j;
	BBM_BLOCK_MARK bbm_mark;
	//COP0_READ_PAGE_BUF page_buf_read;
	//rdt->trig_cnt = 0;
	//rdt->done_cnt = 0;

	U8 erl_slc_mode = LOG_BLOCK_SLC_MODE;
	U8 erl_pca_rule = 0;
	U8 laterbad_pca_rule = 0;
	//PCA_t ulPCA;
	U32 ulLocalPCA;

	/* Choose PCA rule */
	erl_pca_rule = PCA_RULE(erl_slc_mode);

	bbm_type = 0;//Reip

	data_buf = rdt->temp_read_buffer;
	M_UART(RDT_TEST_, "\nload later bad from ERL to DBT\n");

	for (block_index = 0; block_index <= ((rdt->log[RDT_LOG_ERROR_RECORD].page_offset_in_block - 1) / rdt->fpl->geometry.d1_page_per_block); block_index++) {
		if ( (pca = rdt->log[RDT_LOG_ERROR_RECORD].log_block_addr[block_index * MAX_BACKUP_LOG_BLOCK_NUM]) != INVALID_PCA_VALUE) {
			for (page = RDT_LOG_HEADER; page <= ((rdt->log[RDT_LOG_ERROR_RECORD].page_offset_in_block - 1) % rdt->fpl->geometry.d1_page_per_block); page++) {
				for (lc = 0; lc < rdt->fpl->geometry.node_per_page; lc++) { // the number of LC in a page
					tmp_pca = (pca | (GET_FPAGE(page) << gPCARule_Page.ubShift[erl_pca_rule]) | lc);

					memset((void *)data_buf, 0x00, PAGE_BYTE_SIZE);

					//ulPCA.FormatFor512GB.ulPCA = tmp_pca;
					ulLocalPCA = tmp_pca;

					//cop0_api_read(rdt_find_block_callback, (U32)(ulPCA.FormatFor512GB.ulPCA), rdt, erl_slc_mode, (U32)data_buf, 1);
					rdt_api_cop0_read(rdt_find_block_callback, (U32)ulLocalPCA, rdt, erl_slc_mode, (U32)data_buf, SYS_BLK);

					/* Recieve read CQ */
					rdt_api_delegate_CQ(BUF_TYPE_READ);


					if (!rdt->find_block_result) { //UNC
						break;
					}

					//skip early bad (fail type: 01), have to find first later bad page
					/*if (((U8 *)data_buf)[0] == 0x01) {
						break;
					}*/

					//each ERL log use 32 bytes in 4096 bytes
					for (j = 0; j < (rdt->fpl->geometry.page_size / 4); j += 32) {
						error_type = ((U8 *)data_buf)[j];

						//skip early bad
						if (error_type == ERL_FACTORY_BAD_BLOCK) {
							rdt->saving_info.flash_early_bad_cnt++;
							continue;
						}
						/*
						//skip critical error
						if((error_type == 0x0B) || (error_type == 0x0E) || (error_type == 0x1A))
						{
						    return;
						}
						*/

						//skip end
						if ((error_type == 0x00) || (error_type == 0xD0) || (error_type == 0xFF)) {
							//lc end
							lc = rdt->fpl->geometry.node_per_page;
							break;
						}

						//UART_OUT_STR_HEX("error type = : ", error_type, TRUE);
						//other error, record to DBT
						//bank = ((U8 *)data_buf)[j + 1];
						bank = ((U8 *)data_buf)[j + 19];	//logic_ce
						ch = ((U8 *)data_buf)[j + 2];
						die = ((U8 *)data_buf)[j + 3];
						block = ((U16 *)data_buf)[(j + 4) >> 1];
						plane = ((U8 *)data_buf)[j + 8];
						state = ((U8 *)data_buf)[j + 14];

						if (state == RDT_STATE_FLH_TEST_SLC) {
							slc_mode = 1;
						}
						else {
							slc_mode = 0;
						}

						M_UART(RDT_DBG_, "later bad, ch = %b", ch);
						M_UART(RDT_DBG_, " , die = %b", die);
						M_UART(RDT_DBG_, " , block = %l", block);
						M_UART(RDT_DBG_, " , plane = %b", plane);
						M_UART(RDT_DBG_, " , bank = %b", bank);
						M_UART(RDT_DBG_, " , slc = %b", slc_mode);

						//parse error type to decide early bad type
						if ((error_type == ERL_READ_RETRY_FAIL) || (error_type == ERL_READ_FAIL) || (error_type == ERL_READ_EMPTY) || (error_type == ERL_READ_OVER_ECC) || (error_type == ERL_OFL_BCH_COR_OK) || (error_type == ERL_READ_CMP_DATA_FAIL)/*|| (error_type == ERL_READ_RETRY_OK)*/) {//Reip
							bbm_type = DBT_FW_READ_HB_UNC;  //BAD_BLOCK_READ_FAIL	//Reip
						}
						else if (error_type == ERL_PROGRAM_FAIL) {
							bbm_type = DBT_FW_PROGRAM_FAIL;  //BAD_BLOCK_PROG_FAIL	//Reip
						}
						else if (error_type == ERL_ERASE_FAIL) {
							bbm_type = DBT_FW_ERASE_FAIL;  //BAD_BLOCK_ERASE_FAIL	//Reip
						}

						bbm_mark.fields.bad_type = SET_BLOCK_MODE(0, bbm_type);
						//bbm_mark.fields.slc_fail = slc_mode;

						//bbm_mark.u8 = SET_BLOCK_MODE(0, bbm_type);

						/* Choose PCA rule */
						laterbad_pca_rule = PCA_RULE(slc_mode);

						laterbad_pca = PCA_VALUE(laterbad_pca_rule, die, block, bank, ch, plane, 0, 0, 0);

						if (bbm_type != 0) {//Reip
							rdt_api_dbt_log_add(rdt, laterbad_pca, bbm_mark, slc_mode);
						}

						//bbm_mark.u8 = SET_BLOCK_MODE(BLOCK_MODE_EARLY_RDT_BAD, bbm_type, slc_mode , BLOCK_AREA_VB, PRE_LATER_CNT_ORPHAN, BLOCK_ATTR_NORMAL);
						//laterbad_pca = ((block << rdt->cop0->pca_rule[slc_mode].rule_block.shift) | (die << rdt->cop0->pca_rule[slc_mode].rule_die_page.shift) | (bank << rdt->cop0->pca_rule[slc_mode].rule_bank.shift) | (ch << rdt->cop0->pca_rule[slc_mode].rule_ch.shift)  | (plane << rdt->cop0->pca_rule[slc_mode].rule_plane.shift));
						//rdt_api_dbt_log_add(rdt, laterbad_pca, bbm_mark, slc_mode);
						//later_bad_count++;
					}
				}

			}
		}
		else {
			M_UART(RDT_TEST_, "\nlater_bad_into_dbt_fail");
			while (1);

		}
	}
}


static U16 rdt_dbt_check_bad_count(RDT_API_STRUCT_PTR rdt, U8 die, U8 bank, U8 ch, U8 plane)
{
	DBT_LOG_STRUCT dbt;
	U32 bm_count_offset;//, bm_count_offset_idx;
	//PCA_RULE_STRUCT *p_pca_rule = (rdt->fpl->pca_rule + 1);
	//U8 slc_mode = LOG_BLOCK_SLC_MODE;
	//U8 pca_rule = 0;
	FPL_GEOMETRY_STRUCT_PTR geometry;

	geometry = &rdt->fpl->geometry;

	/* Choose PCA rule */
	//pca_rule = PCA_RULE(slc_mode);

	dbt.bm_count = (RDT_BBM_BM_COUNT_PTR) (rdt->log[RDT_LOG_DBT].buf_base + SHARED_DBUF_BBM_BMCNT_EARLY_OFFSET);

	/*bm_count_offset = die * BIT(p_pca_rule->rule_plane.bit_no + p_pca_rule->rule_ch.bit_no + p_pca_rule->rule_bank.bit_no)
	                  + bank * BIT(p_pca_rule->rule_plane.bit_no + p_pca_rule->rule_ch.bit_no)
	                  + ch * BIT(p_pca_rule->rule_plane.bit_no) + plane;*/
	bm_count_offset = (ch * geometry->die_per_ce * geometry->total_bank * geometry->plane_per_die) +
		(((bank * geometry->die_per_ce) + die) * geometry->plane_per_die) +
		plane;
	//bm_count_offset = ((bm_count_offset_idx * BMCNT_SIZE) + BMCNT_OFFSET);

	return dbt.bm_count[bm_count_offset].bad_cnt;
}


static void rdt_load_bad_table_callback(TIEOUT_FORMAT_t uoResult, void *cb_obj, U32 ulPCA)
{
	RDT_API_STRUCT *rdt = (RDT_API_STRUCT *)cb_obj;
	//U8 ubResult = TRUE;
	//rdt->done_cnt++;

	if (uoResult.HL.B32_to_B63.Info.btMSG_TYPE) {
		M_UART(RDT_TEST_, "LOAD DBT READ FAIL");

		//check
		//rdt_api_set_erl_log(rdt, tie_data);
		rdt->rdt_err = ERR_LOAD_BAD_TABLE_FAIL;
		//rdt_api_emergency_save(rdt);
	}
	else {
		M_UART(RDT_DBG_, "\n read log LCA=%l", uoResult.HL.B0_to_B31.Read_1st.ulLCA);
	}
	return;
}

BOOL rdt_api_load_bad_table(RDT_API_STRUCT_PTR rdt)
{
	rdt->current_state = RDT_STATE_LOAD_BBM_FROM_FLASH;

	U8 block_index, lc;
	U16 read_page_cnt, page;
	U32 bbm_blk_addr = INVALID_PCA_VALUE;
	U32 pca;
	U32 data_buf = rdt->log[RDT_LOG_DBT].buf_base;

	U8 slc_mode = LOG_BLOCK_SLC_MODE;
	U8 pca_rule = 0;
	U64 DBTSerialNum;

	//U32 temp_ECC_mode = 0;
#if LDPC_MODE_MODIFY
	U8 ubECCMode = 0;
#endif
	U8 lc_num;

	/* Choose PCA rule */
	pca_rule = PCA_RULE(slc_mode);

	//COP0_READ_PAGE_BUF page_buf_read;
	//rdt->trig_cnt = 0;
	//rdt->done_cnt = 0;

#if 0//Dylan for V6 RDT rdt_api_load_bad_table check read
	//// PIO DBG  ////
	REG32 *pFlaReg1 = R32_FCTL_CH[0];
	FlaCEControl(0, 0, ENABLE);
	pFlaReg1[R32_FCTL_PIO_CMD] = 0xCF;
	FlaCEControl(0, 0, DISABLE);
	//// PIO DBG  ////
#endif

	/*** Clear DBUF ***/
	rdt_api_dmac_setvalue((U32)(0x0), (U32)(rdt->log[RDT_LOG_DBT].buf_base), (U32)(rdt->log[RDT_LOG_DBT].buf_total_size));

	for (block_index = 0; block_index < MAX_SYSTEM_BLOCK_COPY; block_index++) {	//if first DBT have problem, need to check second... Carrie
		if (rdt->sys_blk_record.bbm_block_addr[block_index] != INVALID_PCA_VALUE) {
			bbm_blk_addr = rdt->sys_blk_record.bbm_block_addr[block_index];
			M_UART(RDT_DBG_, "\nbbm_blk_addr=%l", bbm_blk_addr);

		}

		read_page_cnt = (U16)(rdt->log[RDT_LOG_DBT].buf_total_size / PAGE_BYTE_SIZE);

		M_UART(RDT_DBG_, "\nBBM total page: %l", read_page_cnt);
		for (page = 0; page < read_page_cnt; page++) {
			if (page == 0) {
#if LDPC_MODE_MODIFY
				//temp_ECC_mode = R32_FCON[R32_FCON_LDPC_CFG];
				//R32_FCON[R32_FCON_LDPC_CFG] &= ~CHK_ECC_MODE_MASK;         // clear LDPC mode [2:0]
				//R32_FCON[R32_FCON_LDPC_CFG] |= SET_ECC_MODE_7;             //set  LDPC mode[2:0]
				ubECCMode = (U8)M_GET_ECC_MODE();
				M_SET_ECC_MODE(SET_ECC_MODE_7);
#if E19_FIP_SNAP_READ_WORKAROUND
				FIP_SET_SNAP_READ_DMY(0, 0, 0, 0);
#else
				FIP_SET_SNAP_READ_DMY(gFlhEnv.uwLDPCMode7DMYByteLen[0], gFlhEnv.uwLDPCMode7DMYByteLen[1], gFlhEnv.uwLDPCMode7DMYByteLen[2], gFlhEnv.uwLDPCMode7DMYByteLen[3]);
#endif
#endif
				lc_num = 1;
			}
			else {
				lc_num = rdt->fpl->geometry.node_per_page; //4
				if (page == 1) {
					data_buf += (LC_BYTE_SIZE * 3);
				}
			}

			M_UART(RDT_DBG_, "\npage=%l lc_num=%b data_buf=%l", page, lc_num, data_buf);

			for (lc = 0; lc < lc_num; lc++) {
				pca = (bbm_blk_addr | (GET_FPAGE(page) << gPCARule_Page.ubShift[pca_rule]) | lc);

				//cop0_api_read(rdt_cb_read_log, (U32)pca, rdt, slc_mode, (U32)data_buf);
				rdt_api_cop0_read(rdt_load_bad_table_callback, (U32)pca, rdt, slc_mode, (U32)data_buf, SYS_BLK);

				data_buf += LC_BYTE_SIZE;

			}
			rdt_api_delegate_CQ(BUF_TYPE_READ);
#if 0  //Dylan for V6 RDT test
			UartPrintf("\n debug in rdt_api_load_bad_table [ Print out: ]");
			U8 *r_ptr1;
			r_ptr1 = (U8 *) data_buf; // 4 byte
			//UartPrintf("\n [ after r_ptr -> Read_buffer , Read_buffer address=%x Â ] \n", Read_buffer);
			for (int j = 0; j < 1024; j++) { //4å€?K (å› ç‚ºU32, 1024=4K )

				//dump few data to check when copare fail
				if ((j % 32 == 0)) {
					UartPrintf("\n[%x]", 32 * j / 32);
				}
				UartPrintf(" %b", *r_ptr1);
				r_ptr1++;
			}
#endif
			/* Recieve all read CQ */

			if (page == 0) {
#if LDPC_MODE_MODIFY
				//R32_FCON[R32_FCON_LDPC_CFG] &= ~CHK_ECC_MODE_MASK;         // clear LDPC mode [2:0]
				//R32_FCON[R32_FCON_LDPC_CFG] |= temp_ECC_mode;             //set  LDPC mode[2:0]
				M_SET_ECC_MODE(ubECCMode);
				FIP_SET_SNAP_READ_DMY(gFlhEnv.uwNormalDMYByteLen[0], gFlhEnv.uwNormalDMYByteLen[1], gFlhEnv.uwNormalDMYByteLen[2], gFlhEnv.uwNormalDMYByteLen[3]);
#endif
			}

			if ( rdt->rdt_err ) {
				break;
			}
		}
		if ( rdt->rdt_err == ERR_NO_FAIL ) {
			break;
		}
		else if ((block_index + 1) < MAX_SYSTEM_BLOCK_COPY ) {
			//The first read failed and the next copy needs to be read.
			rdt->rdt_err = ERR_NO_FAIL;
		}
	}

	if ( rdt->rdt_err ) {
		return FALSE;
	}

	/* Recieve all read CQ */
	rdt_api_delegate_CQ(BUF_TYPE_READ);
	//UartPrintf("\nrdt->emergency_flag: %b", rdt->emergency_flag);
	/*
	if ( rdt->emergency_flag == 1 ) {
		//rdt_api_write_log_to_flash(rdt, SPARE_LCA_RDT_TESTMARK_LOG_BLOCK);	//fail before find good block, can't write to flash.
		//rdt_api_program_erl_log(rdt, TRUE);
		rdt->emergency_flag = 0;
		rdt->err_bitmap.fields.rdt_init_flow_fail = LOAD_BAD_TABLE_FAIL;
		return FALSE;
	}
	*/
	/*** Update DBT serial num ***/
	data_buf = rdt->log[RDT_LOG_DBT].buf_base;
#if 0  //Dylan for V6 RDT test
	UartPrintf("\n debug in rdt_api_load_bad_table [ Print out: ]");
	U8 *r_ptr1;
	r_ptr1 = (U8 *) data_buf; // 4 byte
	//UartPrintf("\n [ after r_ptr -> Read_buffer , Read_buffer address=%x Â ] \n", Read_buffer);
	for (int j = 0; j < 1024; j++) { //4å€?K (å› ç‚ºU32, 1024=4K )

		//dump few data to check when copare fail
		if ((j % 32 == 0)) {
			UartPrintf("\n[%x]", 32 * j / 32);
		}
		UartPrintf(" %b", *r_ptr1);
		r_ptr1++;
	}
#endif
	DBTSerialNum = *(U64 *)(data_buf + BBM_SERIAL_NUM_OFFSET);
	DBTSerialNum = (DBTSerialNum + 1) &BBM_SERIAL_NUM_MASK;
	*(U64 *)(data_buf + BBM_SERIAL_NUM_OFFSET) = DBTSerialNum;
	//*(U64*)(data_buf + BBM_SERIAL_NUM_OFFSET) = ((U64)0xFFFFFFFFFFFFFFFF);
	M_UART(RDT_DBG_, "\nDBTSerialNum=%L(%L) %l", DBTSerialNum, *(U64 *)(data_buf + BBM_SERIAL_NUM_OFFSET), (data_buf + BBM_SERIAL_NUM_OFFSET));

	//pca = PCA_VALUE(pca_rule, 0, 1, 1, 3, 1, 0, 0, 0);
	//UartPrintf("\nDBTmark=%b",rdt_api_dbt_check_bad(rdt, pca, slc_mode));

	return TRUE;
}



#define RDT_ADD_FAKE_ERL 0
#define BMCNT_CHECK 0
static BOOL rdt_let_early_bad_into_erl(RDT_API_STRUCT_PTR rdt)
{
	U8 die, ch, bank, pln;
	U16 block;
	U32 pca;
	U32 temp_early_bad_cnt;
	ERL_LOG_STRUCT erl_log;

	FPL_GEOMETRY_STRUCT geometry = rdt->fpl->geometry;
	U8 slc_mode = LOG_BLOCK_SLC_MODE;
	U8 pca_rule = 0;
	//U8 ubi;

	/* Choose PCA rule */
	pca_rule = PCA_RULE(slc_mode);

	memset(&erl_log, 0x00, sizeof(ERL_LOG_STRUCT) );

	//PCA_RULE_STRUCT *p_pca_rule = (rdt->fpl->pca_rule + 1);
	//FPL_GEOMETRY_STRUCT geometry = rdt->fpl->geometry;

	//UartPrintf("\nADD EARLY BAD TO ERL:\n");

	rdt->current_state = RDT_STATE_EARLY_MARK_INTO_ERL;

	//If ERL log exist and powercycle happen, record later bad instead of early bad?
	if ((rdt->log[RDT_LOG_ERROR_RECORD].page_offset_in_block > RDT_LOG_HEADER) && (rdt->rdt_test_flow_index > 0)) {
		//record later bad into DBT
		check_later_bad_into_dbt(rdt);
		M_UART(RDT_TEST_, "\nPRL Early bad count = %l", rdt->saving_info.flash_early_bad_cnt);
		return TRUE;  //Do not add early bad
	}

	for (die = 0; die < geometry.die_per_ce ; die++) {
		for (bank = 0; bank < geometry.total_bank; bank++) {
			for (ch = 0; ch < geometry.channel_per_bank ; ch++) {
				for (pln = 0; pln < geometry.plane_per_die; pln++) {
					temp_early_bad_cnt = 0;

					for (block = 0; block < geometry.block_per_plane; block++) {
						pca = PCA_VALUE(pca_rule, die, block, bank, ch, pln, 0, 0, 0);
						//pca = ((block << p_pca_rule->rule_block.shift) | (die << p_pca_rule->rule_die.shift) | (bank << p_pca_rule->rule_bank.shift) | (pln << p_pca_rule->rule_plane.shift) | (ch << p_pca_rule->rule_ch.shift));

						if (rdt_api_dbt_check_bad(rdt, pca, slc_mode)) {
							erl_log.err_bbl.fail_type = ERL_FACTORY_BAD_BLOCK;
							erl_log.err_bbl.flash_ce = rdt_api_show_phy_ce(rdt, bank, ch);
							erl_log.err_bbl.flash_channel = ch;
							erl_log.err_bbl.flash_plane = pln;
							erl_log.err_bbl.flash_die = die;
							erl_log.err_bbl.physical_block = block;
							erl_log.err_bbl.rdt_state = rdt->current_state;
							erl_log.err_bbl.logic_ce  = bank;

							rdt_api_erl_log_add(rdt, &erl_log);
							temp_early_bad_cnt++;
							rdt->saving_info.flash_early_bad_cnt++;
#if 1
							//if (rdt->external_uart) //rdt->external_uart
							{
								M_UART(RDT_DBG_, "\nEarly bad: die = %b", die);
								M_UART(RDT_DBG_, " ,ch = %b", ch);
								M_UART(RDT_DBG_, " ,ce = %b", bank);
								M_UART(RDT_DBG_, " ,pln = %b", pln);
								M_UART(RDT_DBG_, " ,unit = %l(%u)", block, block);
								M_UART(RDT_DBG_, " ,pca = %l", pca);
								M_UART(RDT_DBG_, " ,err_bbl.flash_ce = %b", erl_log.err_bbl.flash_ce);
							}

#endif
#if RDT_ADD_FAKE_ERL
							for (ubi = 0; ubi < 100; ubi++) {
								UartPrintf("\nubi=%b", ubi);
								rdt_api_erl_log_add(rdt, &erl_log);
							}
							rdt_api_program_erl_log(rdt, FALSE);
							for (ubi = 0; ubi < 100; ubi++) {
								UartPrintf("\nubi=%b", ubi);
								rdt_api_erl_log_add(rdt, &erl_log);
							}
							rdt_api_program_erl_log(rdt, FALSE);
							for (ubi = 0; ubi < 100; ubi++) {
								UartPrintf("\nubi=%b", ubi);
								rdt_api_erl_log_add(rdt, &erl_log);
							}
							rdt_api_program_erl_log(rdt, FALSE);
							/*for (ubi = 0; ubi < 100; ubi++) {
								UartPrintf("\nubi=%b", ubi);
								rdt_api_erl_log_add(rdt, &erl_log);
							}
							rdt_api_program_erl_log(rdt, FALSE);
							for (ubi = 0; ubi < 100; ubi++) {
								UartPrintf("\nubi=%b", ubi);
								rdt_api_erl_log_add(rdt, &erl_log);
							}
							rdt_api_program_erl_log(rdt, FALSE);
							for (ubi = 0; ubi < 100; ubi++) {
								UartPrintf("\nubi=%b", ubi);
								rdt_api_erl_log_add(rdt, &erl_log);
							}
							rdt_api_program_erl_log(rdt, FALSE);
							for (ubi = 0; ubi < 100; ubi++) {
								UartPrintf("\nubi=%b", ubi);
								rdt_api_erl_log_add(rdt, &erl_log);
							}
							rdt_api_program_erl_log(rdt, FALSE);
							for (ubi = 0; ubi < 100; ubi++) {
								UartPrintf("\nubi=%b", ubi);
								rdt_api_erl_log_add(rdt, &erl_log);
							}
							rdt_api_program_erl_log(rdt, FALSE);*/
#endif

						}
					}

#if 1 //bad cnt
					//if (rdt->external_uart)
					{
						M_UART(RDT_DBG_, "\nbad cnt = %l", rdt_dbt_check_bad_count(rdt, die, bank, ch, pln));
						M_UART(RDT_DBG_, " ,temp_eraly_bad_cnt = %l", temp_early_bad_cnt);
						M_UART(RDT_DBG_, " ,die = %b", die);
						M_UART(RDT_DBG_, " ,ch = %b", ch);
						M_UART(RDT_DBG_, " ,ce = %b", bank);
						M_UART(RDT_DBG_, " ,pln = %b", pln);
					}

#if BMCNT_CHECK
					if (rdt_dbt_check_bad_count(rdt, die, bank, ch, pln) != temp_early_bad_cnt) { //check early bad count
						//bad count is not match the number of bad count in the bad tbl
						rdt->err_bitmap.fields.rdt_init_flow_fail = EARLY_BAD_COUNT_NOT_MATCH_FAIL;
						rdt->rdt_err = ERR_EARLY_BAD_CNT_FAIL;
						rdt->saving_info.die = die;
						rdt->saving_info.bank = bank;
						rdt->saving_info.ch = ch;
						rdt->saving_info.pln = pln;
						rdt_api_emergency_save(rdt);
						//save RML
						rdt_api_write_log_to_flash(rdt, RDT_LOG_RDT_TESTMARK);
						rdt_api_program_erl_log(rdt, TRUE);
						rdt->emergency_flag = 0;
						UartPrintf("\ncheck bad count error");
						/*UartPrintf(" bad cnt = %l", rdt_api_dbt_check_bad_count(rdt, die, bank, ch, pln));
						UartPrintf(" ,temp_eraly_bad_cnt = %l", temp_early_bad_cnt);
						UartPrintf(" ,die = %b", die);
						UartPrintf(" ,ch = %b", ch);
						UartPrintf(" ,ce = %b", bank);
						UartPrintf(" ,pln = %b", pln);*/
						return FALSE;
					}
#endif
#endif
					//Prevent too many early bad to record(ERL dbuf is 4 pages now)
					rdt_api_program_erl_log(rdt, FALSE);
				}

				//Prevent too many early bad to record(ERL dbuf is 4 pages now)
				rdt_api_program_erl_log(rdt, FALSE);
			}

			//Prevent too many early bad to record(ERL dbuf is 4 pages now)
			rdt_api_program_erl_log(rdt, FALSE);
		}

		//Prevent too many early bad to record(ERL dbuf is 4 pages now)
		rdt_api_program_erl_log(rdt, FALSE);
	}

	rdt_api_program_erl_log(rdt, TRUE);
	return TRUE;
}


static void rdt_init_test_flow(RDT_API_STRUCT_PTR rdt)
{
	U8 index = 0;
	rdt->rdt_test_flow_index = 0;
	M_UART(RDT_TEST_, "\n init test flow");
#if (RDT_RUN_ONLINE)
	rdt->rdt_test_flow[index++] = PRL_STATE_ERASE_ALL_TEST;
	rdt->rdt_test_flow[index++] = PRL_STATE_TEST_PREPARATION;
	rdt->rdt_test_flow[index++] = PRL_STATE_SCAN_PAGE_REGISTER_TEST;
	if (!rdt->param.ScanWindowOption.btScanWindow1ByPass) {
		rdt->rdt_test_flow[index++] = PRL_STATE_SCAN_WINDOW_1_TEST;
	}
#endif

	if (rdt->param.ambient_temperature > 0) {
		rdt->rdt_test_flow[index++] = PRL_STATE_CHECK_TEMPERATURE;
	}

	rdt->rdt_test_flow[index++] = PRL_STATE_START_TEST;

	rdt->test_in_normal_power_mode = 1;
	// sram test

#if RDT_FAST_TEST_MODE
	rdt->param.sram_test_enable = 0;
	rdt->param.rdt_fast_mode.skip_ic_pattern = 1;
	rdt->param.flash_slc_test_loop = 1;
	rdt->param.flash_tlc_test_loop = 1;
	rdt->param.rdt_fast_mode.fast_flash_test = 1;
#if RDT_RECORD_ECC_DISTRIBUTION
	rdt->param.rdt_record_ecc_distribution.record_edl_enable = 1;
	rdt->param.rdt_record_ecc_distribution.record_edl_by_1P_read = 0;
#endif
#endif

	if (rdt->param.sram_test_enable) {
		rdt->rdt_test_flow[index++] = PRL_STATE_SRAM_TEST;
	}

	// ic test
	if (!rdt->param.rdt_fast_mode.skip_ic_pattern) {
		rdt->rdt_test_flow[index++] = PRL_STATE_IC_TEST;
	}
	rdt->rdt_test_flow[index++] = PRL_STATE_ENTER_LOW_POWER;

#if (!RDT_RUN_ONLINE)
	if (!rdt->param.ScanWindowOption.btScanWindow1ByPass) {
		rdt->rdt_test_flow[index++] = PRL_STATE_SCAN_WINDOW_1_TEST;
	}
#endif

	// slc flash test
	if (rdt->param.flash_test_enable.flash_slc_test_enable) {
		rdt->rdt_test_flow[index++] = PRL_STATE_SLC_FLASH_TEST;
		//if (rdt->param.flash_test_enable.flash_slc_rdy_test_enable) {	//Reip
		//rdt->rdt_test_flow[index++] = PRL_STATE_SLC_RDY_TIME_TEST;
		//}
	}

	// tlc flash test
	if (rdt->param.flash_test_enable.flash_tlc_test_enable) {
		rdt->rdt_test_flow[index++] = PRL_STATE_TLC_FLASH_TEST;
		//if (rdt->param.flash_test_enable.flash_tlc_rdy_test_enable) {	//Reip
		//rdt->rdt_test_flow[index++] = PRL_STATE_TLC_RDY_TIME_TEST;
		//}
	}

	if (rdt->param.flash_test_enable.flash_retry_cmd_verify_test_enable) {
		rdt->rdt_test_flow[index++] = PRL_STATE_RETRY_VERIFY_TEST;
	}

	if (!rdt->param.ScanWindowOption.btScanWindow2ByPass) {
		rdt->rdt_test_flow[index++] = PRL_STATE_SCAN_WINDOW_2_TEST;
	}

	// sram test
	if (rdt->param.sram_test_enable) {
		rdt->rdt_test_flow[index++] = PRL_STATE_SRAM_TEST;
	}

	// ic test
	if (!rdt->param.rdt_fast_mode.skip_ic_pattern) {
		rdt->rdt_test_flow[index++] = PRL_STATE_IC_TEST;
	}
	// recover core power
	rdt->rdt_test_flow[index++] = PRL_STATE_ENTER_NORMAL_POWER;

	// save log
	rdt->rdt_test_flow[index++] = PRL_STATE_SAVE_LOG;

	//end
	rdt->rdt_test_flow[index++] = PRL_STATE_NO_ACTION;

#if (RDT_RUN_ONLINE)
	gpLastStatus->B.ubFlowCount = index;
#endif
}


BOOL rdt_api_test_preparation(RDT_API_STRUCT_PTR rdt)
{
	M_UART(RDT_TEST_, "\nload DBT");
	if (!rdt_api_load_bad_table(rdt)) {
		//rdt->testresult |= 0x1;
		//rdt->next_state = PRL_STATE_WAIT_VUC;
		return FALSE;
	}

#if !ENABLE_RDT_NO_LOG

	M_UART(RDT_TEST_, "\nfind good block");
	if (!rdt_find_good_block(rdt)) {
		//testresult |= 0x2;
		if ( rdt->rdt_err == ERR_LCA_COMPARE_FAIL) {
			//pio write to log
			rdt->err_bitmap.fields.rdt_init_flow_fail = LCA_COMPARE_FAIL;
		}
		//rdt->next_state = PRL_STATE_WAIT_VUC;
		return FALSE;
	}

	if (!rdt->err_bitmap.fields.rdt_init_flow_fail) {
		M_UART(RDT_TEST_, "\nEarly bad into the Error log");
		if (!rdt_let_early_bad_into_erl(rdt) ) {
			M_UART(RDT_DBG_, "something wrong\n");
			//testresult |= 0x4;
			//pio write to log
			//rdt->next_state = PRL_STATE_WAIT_VUC;
			return FALSE;
		}
		//UartPrintf("\n no prl early bad count = %l", rdt->saving_info.flash_early_bad_cnt);
	}
#endif

#if (RDT_POWER_RESUME)
#if (HOST_MODE == USB)
	//check enter low power mode
	if ((rdt->test_in_normal_power_mode == 0) && (rdt->rdt_test_flow_index > 0)) {
		rdt_api_voltage_setting(rdt, COREPOWER_875mV);//rdt->voltage_setting
	}
	M_UART(RDT_TEST_, "\n current core power = %b \n", rdt_api_read_voltage_setting() >> COREPOWER_SEL_SHIFT);
#elif (HOST_MODE == SATA)
	if ((rdt->test_in_normal_power_mode == 0) && (rdt->rdt_test_flow_index > 0)) {
		rdt_SATA_voltage_setting(0);
	}
#endif
#endif

	return TRUE;
}



BOOL rdt_link_wait(RDT_API_STRUCT_PTR rdt)
{
	//U32 wait_count;
	//U32 current_time = 0;
	U8 i;
	U32 bbm_blk_addr = INVALID_PCA_VALUE;

	rdt->current_state = RDT_STATE_LOG_AND_LINK_CHECK;

	if (rdt->err_bitmap.fields.rdt_init_flow_fail) {
		//UART_OUT_STR("RDT INIT FLOW fail\n");
		rdt->rdt_err = ERR_RDT_INIT_FLOW_FAIL;
		//rdt_api_emergency_save(rdt);
		rdt->emergency_flag = 0;
		//return HAVE_INITIAL_FAIL;
		return HAVE_LINK;
	}

	//check bbm table
	for (i = 0; i < 2; i++) { // TO DO: the number of bbm block is needs to define
		if (rdt->sys_blk_record.bbm_block_addr[i] != INVALID_PCA_VALUE) {
			bbm_blk_addr = rdt->sys_blk_record.bbm_block_addr[i];
		}
	}


	if (bbm_blk_addr == INVALID_PCA_VALUE) {
		M_UART(RDT_TEST_, "\nNo BBM block");
		rdt->err_bitmap.fields.rdt_init_flow_fail = NO_BBM_FAIL;
		rdt->rdt_err = ERR_RDT_INIT_FLOW_FAIL;
		//rdt_api_emergency_save(rdt);
		rdt->emergency_flag = 0;
		return HAVE_LINK;
	}


	M_UART(RDT_TEST_, "\n waiting for host link");

	//CPU_START_TICK_COUNT();
#if RDT_POWER_RESUME
	if ( rdt->rdt_test_flow_index == RDT_TEST_FLOW_FINISH_INDEX ) {
		M_UART(RDT_TEST_, "\nRDT DONE\n");
		gubLEDState = RDT_TEST_FINISH;
		return HAVE_LINK ; // don't do rdt test
	}

#endif

#if (HOST_MODE == USB)

	/* USB link detect */
	U64 ulCheckTime;
	ulCheckTime = M_GET_FW_TIMER() + (U64)(rdt->param.host_link_time * 1000);
	M_UART(RDT_TEST_, "\n** Check USB Link **\n");

	do {

		M_RTT_IDLE_MS(100);

		M_UART(RDT_TEST_, ". ");
		USBGetUSBProtocol();

		FTLSyncCmdHandler_USB(&gUSBCUrrentCMD, (U32)M_DB_GET_RD_CNT(DB_APU_CMD_CQ));

		if (FALSE == M_USB_CHK_HOST_EVENT()) {
			if (M_DB_GET_RD_CNT(DB_APU_TD_CQ)) {
				USBTDHandler();
			}
		}

		//warm reset handler (before preformat)
		if ((M_CHECK_HOST_EVENT() & ERROR_HANDLE_APU_WRITE_ERROR_BIT) || M_USB_CHK_HOST_EVENT() || M_USB_CHECK_HOST_EVENT_LPM() || M_USB_CHK_HOST_EVENT_L1()) {
			HostEventHandler();
			M_UART(USB_DEBUG_, "\nHostEventHandler()\n");
		}


		if (gHostManager.ubType && (HOST_STATE_FW_DOING != gHostManager.ubState)) {
			HostDelegateMgr();
			M_UART(USB_DEBUG_, "\bHostDelegateMgr()\n");
		}
		if (gubCheckUSBLinkForRDT == 1) {
			M_UART(RDT_TEST_, "\n** USB is detected **");
			return HAVE_LINK;
		}

	} while ((ulCheckTime > M_GET_FW_TIMER()));
#endif
	rdt->saving_info.wait_time_to_running = (rdt_api_rtt_get_timer_count() / 1000);
	M_UART(RDT_DBG_, "\nwait time %l\n", rdt->saving_info.wait_time_to_running);

	return NO_LINK_WO_LPM;
}

void rdt_api_basic_init(RDT_API_STRUCT_PTR rdt)
{
	rdt_fpl_init(rdt->fpl);		// Init fpl geometry

	rdt_log_blk_init(rdt);

	rdt_log_struct_init(rdt);

	rdt_rw_buf_init(rdt);		// Init rdt rw buf

	rdt_random_list_init(rdt);

	/* ZIP buffer init */
	rdt_zip_init(rdt);

	/* SEC buffer init */
	rdt_api_sec_init(rdt);

	/* RS reg & dbase buffer init */
	rdt_rs_init(rdt);

	if (gMainJumpManager.btFWInitOnlineFlag) {
		//Just Init buffer and struct
		return;
	}

	U8 ubCh, ubBank, ubPhy_CE;
	for (ubCh = 0; ubCh < rdt->fpl->geometry.channel_per_bank; ubCh++) {

		for (ubBank = 0; ubBank < rdt->fpl->geometry.total_bank; ubBank++) {

			ubPhy_CE = rdt_api_show_phy_ce(rdt, ubBank, ubCh);
			M_UART(RDT_TEST_, "\n gubLogicalCEMapPhysicalCE[ubCh][ubCEIndex] = %b", gubLogicalCEMapPhysicalCE[ubCh][ubBank]);
			M_UART(RDT_TEST_, " Ch:%b Bank:%b Phy_CE: %d", ubCh, ubBank, ubPhy_CE);
		}
	}

	M_UART(RDT_DBG_, "\nubCENumber=%d", gFlhEnv.ubCENumber);
	if ( gFlhEnv.ubCENumber != 0) {

		/* check special fail log */
		rdt->scan_header_mode = 1;	//scan code blk and DBT header & ERL, RML...
		rdt->ulScanSpecificLogLCA = 0;
		rdt_api_scan_log(rdt);

		rdt_param_init(rdt);		// RDT parameter settings

		rdt_init_test_flow(rdt);

		if (!rdt->param.rdt_op_option.skip_pto_detection) {
			M_UART(RDT_TEST_, "\nRDT setup PTO Detection");
			rdt_api_setup_cop0_fip_pto_detection();
		}
		else {
			M_UART(RDT_TEST_, "\nRDT skip PTO Detection");
		}

		if (rdt->scan_log_bitmap && !rdt->btRDTOnlineReRun) {
			//check each log page offset
			rdt_check_each_log_page_offset(rdt);
			M_UART(RDT_TEST_, "\n\r basic init");
			if (rdt_api_pio_force_read_fail_log(rdt)) {
				rdt->err_bitmap.fields.rdt_init_flow_fail = COP0_INT_FAIL;
			}
		}

		if (rdt->param.over_rdy_detect_enable) {	//Reip
			rdt->saving_info.ubRDYAbnormalFlag = FALSE;//Init The Flag
		}
	}
	else {
		rdt->err_bitmap.fields.rdt_init_flow_fail = NO_CE_EXIST;
	}
}
#endif
