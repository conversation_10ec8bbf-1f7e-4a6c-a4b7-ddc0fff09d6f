/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  err_hdl_fpl_softbit_retry_table.c                                     */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/
#define SRC_FW_ERR_ERR_HDL_FPL_SOFTBIT_RETRY_TABLE_C_
/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "err_hdl_fpl_softbit_retry_table.h"
#include "typedef.h"
#include "setup.h"
/*****************************************
 *  extern golbal variables
 ****************************************/
#if(TRUE == RETRY_MICRON_NICKS_ALL_PAGE_TYPE_SB_SUPPORT)
#if(RETRY_SOFTBITT_FOR_SDK_EN==FALSE)
U16 ldpc_frame_size[9] = { 2320, 2304, 2288, 2272, 2240, 2208, 2192, 2368, 2352 };
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_QLC)
U32 sb_llr_table[4][4][DEFAULT_LLR_TABLE_LENGTH] = {

	/* Default LLR table General Case */
	{
		{0x000280A5, 0x00802128, 0x81290800, 0xCE480004, 0x70000701, 0x000D837B, 0x0F83FFD8, 0x82F7F800, 0x52B8000B, 0x90000902}, // Low
		{0x000280A5, 0x00802128, 0x81290800, 0xCE480004, 0x70000701, 0x000D837B, 0x0F83FFD8, 0x82F7F800, 0x52B8000B, 0x90000902}, // Middle
		{0x40001405, 0x00040101, 0x24090040, 0x0E024000, 0x03800038, 0xC0006C1B, 0x007C1F06, 0x5C1707C0, 0x1205C000, 0x04800048}, // Upper
		{0x000280A5, 0x00802128, 0x81290800, 0xCE480004, 0x70000701, 0x000D837B, 0x0F83FFD8, 0x82F7F800, 0x52B8000B, 0x90000902}, // Top
	},

	/* Default LLR table Corner Case 1 */
	{
		{0x000280A5, 0x00802178, 0x81297800, 0xCE780004, 0x78000701, 0x000D837B, 0x0F83FF78, 0x82F77800, 0x5278000B, 0xE0000902}, // Low
		{0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Middle
		{0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Upper
		{0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Top
	},

	/* Default LLR table Corner Case 2 */
	{
		{0x000280A5, 0x00802178, 0x81297800, 0xCE780004, 0x78000701, 0x000D837B, 0x0F83FF78, 0x82F77800, 0x5278000B, 0xD0000902}, // Low
		{0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Middle
		{0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Upper
		{0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Top
	},

	/* Default LLR table Corner Case 3 */
	{
		{0x000280A5, 0x00802168, 0x81296000, 0xCE700004, 0x78000701, 0x000D837B, 0x0F83FF50, 0x82F75800, 0x52E0000B, 0xD0000902}, // Low
		{0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Middle
		{0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Upper
		{0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Top
	},
};

U32 sb_dsp2_llr_table[RETRY_SB_DSP2_PAGE_NUM][4][4] = {
	//11b for 	-1
	//01b for 	+1
	//00b for		0
	//10b not define
	/* DSP2 LLR table for Lower */
	{
		// 	0 ~ 15  |  15 ~ 31  |  32 ~ 47  |  48 ~ 63
		{0x40C740C7, 0x40C740C7, 0x40C740C7, 0x40C740C7}, // LLR0		//SB6 = 0, SB7 = 0
		{0x00000000, 0x00000000, 0x00000000, 0x00000000}, // LLR1		//SB6 = 0, SB7 = 1
		{0xC04DC04D, 0xC04DC04D, 0xC04DC04D, 0xC04DC04D}, // LLR2		//SB6 = 1, SB7 = 0
		{0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Top		//SB6 = 1, SB7 = 1
	},

	/* DSP2 LLR table for Middle */
	{
		// 	0 ~ 15  |  15 ~ 31  |  32 ~ 47  |  48 ~ 63
		{0x40C740C7, 0x40C740C7, 0x40C740C7, 0x40C740C7}, // LLR0		//SB6 = 0, SB7 = 0
		{0x00000000, 0x00000000, 0x00000000, 0x00000000}, // LLR1		//SB6 = 0, SB7 = 1
		{0xC04DC04D, 0xC04DC04D, 0xC04DC04D, 0xC04DC04D}, // LLR2		//SB6 = 1, SB7 = 0
		{0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Top		//SB6 = 1, SB7 = 1
	},

	/* DSP2 LLR table for Upper */
	{
		// 	0 ~ 15  |  15 ~ 31  |  32 ~ 47  |  48 ~ 63
		{0x10311031, 0x10311031, 0x10311031, 0x10311031}, // LLR0		//SB6 = 0, SB7 = 0
		{0x00000000, 0x00000000, 0x00000000, 0x00000000}, // LLR1		//SB6 = 0, SB7 = 1
		{0x30133013, 0x30133013, 0x30133013, 0x30133013}, // LLR2		//SB6 = 1, SB7 = 0
		{0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Top		//SB6 = 1, SB7 = 1
	},

	/* DSP2 LLR table for TOP */
	{
		//	0 ~ 15	|  15 ~ 31	|  32 ~ 47	|  48 ~ 63
		{0x40C740C7, 0x40C740C7, 0x40C740C7, 0x40C740C7}, // LLR0		//SB6 = 0, SB7 = 0
		{0x00000000, 0x00000000, 0x00000000, 0x00000000}, // LLR1		//SB6 = 0, SB7 = 1
		{0xC04DC04D, 0xC04DC04D, 0xC04DC04D, 0xC04DC04D}, // LLR2		//SB6 = 1, SB7 = 0
		{0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Top		//SB6 = 1, SB7 = 1
	},
};

U32 sb_dsp_param[4][DSP_PARAM_LENGTH] = {
	{0x08042008, 0x00080808, 0x00000000, 0x00181008, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Low
	{0x08042008, 0x00080808, 0x00000000, 0x00181008, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Middle
	{0x08031808, 0x00000808, 0x00000000, 0x00181008, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Upper
	{0x08042008, 0x00080808, 0x00000000, 0x00181008, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Top
};

U32 sb_dsp_graycode[4][DSP_GRAYCODE_LENGTH] = {
	{
		0x2F27373F, 0x1F17070F, 0x0B03131B, 0x3B33232B, 0x29213139, 0x19110109, 0x08001018, 0x38302028,  // Low
		0x06050402, 0x0E0D0C0A, 0x16151412, 0x1E1D1C1A, 0x26252422, 0x2E2D2C2A, 0x36353432, 0x3E3D3C3A
	},

	{
		0x2F27373F, 0x1F17070F, 0x0B03131B, 0x3B33232B, 0x29213139, 0x19110109, 0x08001018, 0x38302028,  // Middle
		0x06050402, 0x0E0D0C0A, 0x16151412, 0x1E1D1C1A, 0x26252422, 0x2E2D2C2A, 0x36353432, 0x3E3D3C3A
	},

	{
		0x2E26363E, 0x1E16060E, 0x0A02121A, 0x3A32222A, 0x28203038, 0x18100008, 0x05040301, 0x0C0B0907,  // Upper
		0x13110F0D, 0x19171514, 0x1F1D1C1B, 0x25242321, 0x2C2B2927, 0x33312F2D, 0x39373534, 0x3F3D3C3B
	},

	{
		0x2F27373F, 0x1F17070F, 0x0B03131B, 0x3B33232B, 0x29213139, 0x19110109, 0x08001018, 0x38302028,  // Top
		0x06050402, 0x0E0D0C0A, 0x16151412, 0x1E1D1C1A, 0x26252422, 0x2E2D2C2A, 0x36353432, 0x3E3D3C3A
	},
};

U32 sb_dsp_lut[4][DSP_LUT_LENGTH] = {
	{
		0xF2F20000, 0x0E0000FF, 0x0000010E, 0x00010E0E, 0xFFF2F200, 0xF2F20000, 0x0E0000FF, 0x0000010E,  // Low
		0x00010E0E, 0xFFF2F200, 0xF1F10000, 0x0F0000FF, 0x0000010F, 0x00010F0F, 0xFFF1F100, 0xF1F10000,
		0x0F0000FF, 0x0000010F, 0x00010F0F, 0xFFF1F100
	},

	{
		0xF2F20000, 0x0E0000FF, 0x0000010E, 0x00010E0E, 0xFFF2F200, 0xF2F20000, 0x0E0000FF, 0x0000010E,  // Middle
		0x00010E0E, 0xFFF2F200, 0xF1F10000, 0x0F0000FF, 0x0000010F, 0x00010F0F, 0xFFF1F100, 0xF1F10000,
		0x0F0000FF, 0x0000010F, 0x00010F0F, 0xFFF1F100
	},

	{
		0xF2F20000, 0x0E0000FF, 0x0000010E, 0x00010E0E, 0xFFF2F200, 0xF2F20000, 0x0E0000FF, 0x0000010E,  // Upper
		0x00010F0F, 0xFFF1F100, 0xF1F10000, 0x0F0000FF, 0x0000010F, 0x00010F0F, 0xFFF1F100, 0xF1F10000,
		0x0F0000FF, 0x0000010F, 0x00010F0F, 0xFFF1F100
	},

	{
		0xF2F20000, 0x0E0000FF, 0x0000010E, 0x00010E0E, 0xFFF2F200, 0xF2F20000, 0x0E0000FF, 0x0000010E,  // Top
		0x00010E0E, 0xFFF2F200, 0xF1F10000, 0x0F0000FF, 0x0000010F, 0x00010F0F, 0xFFF1F100, 0xF1F10000,
		0x0F0000FF, 0x0000010F, 0x00010F0F, 0xFFF1F100
	},
};

U8 gubRetrySBCnt1Result[RETRY_SB_PAGE_NUM][9] =  {
	//PARM1  PARM2   PARM3  PARM4  PARM_LENGTH    PARM_CNT   FRAME_CNT    ROUND_CNT        SKIP_BITMAP
	{ 0x00,  0x03,   0x05,   0x0A,         4,     0,          0,           0,               0x0},       /// LOW
	{ 0x02,  0x06,   0x08,   0x0C,         4,     0,          0,           0,               0x0},       /// MIDDLE
	{ 0x01,  0x07,   0x0D,   0x00,         3,     0,          0,           0,               0x0},       /// UPPER
	{ 0x04,  0x09,   0x0B,   0x0E,         4,     0,          0,           0,               0x0},       /// TOP
};

U16 guwRetrySBCoarseTable[RETRY_SB_READ_LEVEL_NUM][5] = {
	// Divisor1    Divisor2   Divisor3   Threshold1   Threshold2
	{  50, 200, 1,  500, 4000},      // t0
	{ 150, 250, 1,  500, 4000},      // t1
	{ 100, 200, 1,  500, 4000},      // t2
	{ 100, 250, 1,  500, 4000},      // t3
	{ 100, 250, 1,  500, 4000},      // t4
	{  50, 250, 1,  150, 4000},      // t5
	{ 100, 250, 1,  500, 4000},      // t6
	{ 100, 250, 1,  500, 4000},      // t7
	{ 100, 250, 1, 1000, 4000},      // t8
	{  80, 400, 1,  800, 4000},      // t9
	{  80, 400, 1,  500, 4000},      // t10
	{  80, 400, 1,  500, 4000},      // t11
	{ 100, 300, 1,  500, 4000},      // t12
	{ 100, 200, 1, 1000, 4000},      // t13
	{  50, 300, 1,  500, 4000},      // t14
};

U8 Offset_delta_table[RETRY_SB_READ_LEVEL_NUM] = {
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0
};

/// 16KB data with each LDPC protect 2KB data, so should have 8 opt level at most
/// backup shift value in Retry_SB_select_rr_table()
U8 Backup_Optimal_shift_value[8][RETRY_SB_MAX_READ_LEVEL_NUM_PER_PAGE] = {
	{ 0, 0, 0, 0},
	{ 0, 0, 0, 0},
	{ 0, 0, 0, 0},
	{ 0, 0, 0, 0},

	{ 0, 0, 0, 0},
	{ 0, 0, 0, 0},
	{ 0, 0, 0, 0},
	{ 0, 0, 0, 0},
};

U8 delta_table[RETRY_SB_DELTA_NUM][RETRY_SB_READ_LEVEL_NUM] = { // default toshiba QLC
	// P0	 P1    P2	 P3    P4	 P5    P6	 P7	   P8	 P9    P10   P11   P12   P13   P14
	{0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7,  0xF7, 0xF7, 0xF7, 0xF7},    // -3delta
	{0xFA, 0xFA, 0xFA, 0xFA, 0xFA, 0xFA, 0xFA, 0xFA, 0xFA, 0xFA, 0xFA,  0xFA, 0xFA, 0xFA, 0xFA},    // -2delta
	{0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD,  0xFD, 0xFD, 0xFD, 0xFD},    // -1delta
	{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  0x00, 0x00, 0x00, 0x00},	//  0
	{0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03,  0x03, 0x03, 0x03, 0x03},    //  1delta
	{0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06,  0x06, 0x06, 0x06, 0x06},    //  2delta
	{0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09,  0x09, 0x09, 0x09, 0x09},    //  3delta
};

U8 default_delta_table[RETRY_SB_DELTA_NUM][RETRY_SB_READ_LEVEL_NUM] = { // default toshiba QLC
	// P0	 P1    P2	 P3    P4	 P5    P6	 P7	   P8	 P9    P10   P11   P12   P13   P14
	{0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7,  0xF7, 0xF7, 0xF7, 0xF7},    // -3delta
	{0xFA, 0xFA, 0xFA, 0xFA, 0xFA, 0xFA, 0xFA, 0xFA, 0xFA, 0xFA, 0xFA,  0xFA, 0xFA, 0xFA, 0xFA},    // -2delta
	{0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD,  0xFD, 0xFD, 0xFD, 0xFD},    // -1delta
	{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  0x00, 0x00, 0x00, 0x00},	//  0
	{0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03,  0x03, 0x03, 0x03, 0x03},    //  1delta
	{0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06,  0x06, 0x06, 0x06, 0x06},    //  2delta
	{0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09,  0x09, 0x09, 0x09, 0x09},    //  3delta
};

U8 sb_delta_table[RETRY_SB_DELTA_NUM][RETRY_SB_READ_LEVEL_NUM] = {
	// P0	 P1    P2	 P3    P4	 P5    P6	 P7	   P8	 P9    P10   P11   P12   P13   P14
	{0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7,  0xF7, 0xF7, 0xF7, 0xF7},    // -3delta
	{0xFA, 0xFA, 0xFA, 0xFA, 0xFA, 0xFA, 0xFA, 0xFA, 0xFA, 0xFA, 0xFA,  0xFA, 0xFA, 0xFA, 0xFA},    // -2delta
	{0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD,  0xFD, 0xFD, 0xFD, 0xFD},    // -1delta
	{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  0x00, 0x00, 0x00, 0x00},	//  0
	{0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03,  0x03, 0x03, 0x03, 0x03},    //  1delta
	{0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06,  0x06, 0x06, 0x06, 0x06},    //  2delta
	{0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09,  0x09, 0x09, 0x09, 0x09},    //  3delta
};

#elif (TRUE == MICRON_FSP_EN)  //RETRY_MICRON		/*(CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_QLC)*/
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)
#if (IM_N18)
U8 delta_table[RETRY_SB_DELTA_NUM][RETRY_SB_READ_LEVEL_NUM] = {
	//	   R1	 R2    R3    R4    R5    R6    R7    R8    R9   R10   R11   R12   R13   R14   R15
	{0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12},	// -3delta
	{0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C},	// -2delta
	{0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06},	// -1delta
	{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},	//	0
	{0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06},	//	1delta
	{0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C},	//	2delta
	{0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12},	//	3delta
};

U8 default_delta_table[RETRY_SB_DELTA_NUM][RETRY_SB_READ_LEVEL_NUM] = {// default_delta_table_MICRON
	//	   R1	 R2    R3	 R4    R5	 R6    R7	 R8    R9	R10   R11	R12   R13	R14   R15
	{0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12}, // -3delta
	{0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C}, // -2delta
	{0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06}, // -1delta
	{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, //	0
	{0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06}, //	1delta
	{0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C}, //	2delta
	{0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12}, //	3delta
};

U8 sb_delta_table[RETRY_SB_DELTA_NUM][RETRY_SB_READ_LEVEL_NUM] = { //MICRON
	//	   R1	 R2    R3	 R4    R5	 R6    R7	 R8    R9	R10   R11	R12   R13	R14   R15
	{0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12}, // -3delta
	{0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C}, // -2delta
	{0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06}, // -1delta
	{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, //	0
	{0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06}, //	1delta
	{0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C}, //	2delta
	{0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12}, //	3delta
};
#elif (IM_N28)
U8 delta_table[RETRY_SB_DELTA_NUM][RETRY_SB_READ_LEVEL_NUM] = { // default toshiba QLC
	// P0	 P1    P2	 P3    P4	 P5    P6	 P7	   P8	 P9    P10   P11   P12   P13   P14
	{0x0E, 0x0F, 0x0E, 0x0F, 0x0E, 0x0F, 0x0E, 0x0F, 0x0E, 0x0F, 0x0E,  0x0F, 0x0E, 0x0F, 0x0E},    // -3delta
	{0x09, 0x0A, 0x09, 0x0A, 0x09, 0x0A, 0x09, 0x0A, 0x09, 0x0A, 0x09,  0x0A, 0x09, 0x0A, 0x09},    // -2delta
	{0x04, 0x05, 0x04, 0x05, 0x04, 0x05, 0x04, 0x05, 0x04, 0x05, 0x04,  0x05, 0x04, 0x05, 0x04},    // -1delta
	{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  0x00, 0x00, 0x00, 0x00},	//  0
	{0x04, 0x05, 0x04, 0x05, 0x04, 0x05, 0x04, 0x05, 0x04, 0x05, 0x04,  0x05, 0x04, 0x05, 0x04},    //  1delta
	{0x09, 0x0A, 0x09, 0x0A, 0x09, 0x0A, 0x09, 0x0A, 0x09, 0x0A, 0x09,  0x0A, 0x09, 0x0A, 0x09},    //  2delta
	{0x0E, 0x0F, 0x0E, 0x0F, 0x0E, 0x0F, 0x0E, 0x0F, 0x0E, 0x0F, 0x0E,  0x0F, 0x0E, 0x0F, 0x0E},    //  3delta
};

U8 default_delta_table[RETRY_SB_DELTA_NUM][RETRY_SB_READ_LEVEL_NUM] = { // default toshiba QLC
	// P0	 P1    P2	 P3    P4	 P5    P6	 P7	   P8	 P9    P10   P11   P12   P13   P14
	{0x0E, 0x0F, 0x0E, 0x0F, 0x0E, 0x0F, 0x0E, 0x0F, 0x0E, 0x0F, 0x0E,  0x0F, 0x0E, 0x0F, 0x0E},    // -3delta
	{0x09, 0x0A, 0x09, 0x0A, 0x09, 0x0A, 0x09, 0x0A, 0x09, 0x0A, 0x09,  0x0A, 0x09, 0x0A, 0x09},    // -2delta
	{0x04, 0x05, 0x04, 0x05, 0x04, 0x05, 0x04, 0x05, 0x04, 0x05, 0x04,  0x05, 0x04, 0x05, 0x04},    // -1delta
	{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  0x00, 0x00, 0x00, 0x00},	//  0
	{0x04, 0x05, 0x04, 0x05, 0x04, 0x05, 0x04, 0x05, 0x04, 0x05, 0x04,  0x05, 0x04, 0x05, 0x04},    //  1delta
	{0x09, 0x0A, 0x09, 0x0A, 0x09, 0x0A, 0x09, 0x0A, 0x09, 0x0A, 0x09,  0x0A, 0x09, 0x0A, 0x09},    //  2delta
	{0x0E, 0x0F, 0x0E, 0x0F, 0x0E, 0x0F, 0x0E, 0x0F, 0x0E, 0x0F, 0x0E,  0x0F, 0x0E, 0x0F, 0x0E},    //  3delta
};

U8 sb_delta_table[RETRY_SB_DELTA_NUM][RETRY_SB_READ_LEVEL_NUM] = { // N28
	// P0	 P1    P2	 P3    P4	 P5    P6	 P7	   P8	 P9    P10   P11   P12   P13   P14
	{0x0E, 0x0F, 0x0E, 0x0F, 0x0E, 0x0F, 0x0E, 0x0F, 0x0E, 0x0F, 0x0E,  0x0F, 0x0E, 0x0F, 0x0E},    // -3delta
	{0x09, 0x0A, 0x09, 0x0A, 0x09, 0x0A, 0x09, 0x0A, 0x09, 0x0A, 0x09,  0x0A, 0x09, 0x0A, 0x09},    // -2delta
	{0x04, 0x05, 0x04, 0x05, 0x04, 0x05, 0x04, 0x05, 0x04, 0x05, 0x04,  0x05, 0x04, 0x05, 0x04},    // -1delta
	{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  0x00, 0x00, 0x00, 0x00},	//  0
	{0x04, 0x05, 0x04, 0x05, 0x04, 0x05, 0x04, 0x05, 0x04, 0x05, 0x04,  0x05, 0x04, 0x05, 0x04},    //  1delta
	{0x09, 0x0A, 0x09, 0x0A, 0x09, 0x0A, 0x09, 0x0A, 0x09, 0x0A, 0x09,  0x0A, 0x09, 0x0A, 0x09},    //  2delta
	{0x0E, 0x0F, 0x0E, 0x0F, 0x0E, 0x0F, 0x0E, 0x0F, 0x0E, 0x0F, 0x0E,  0x0F, 0x0E, 0x0F, 0x0E},    //  3delta
};

U8 sb_delta_table_tlc_qlc_open[RETRY_SB_DELTA_NUM][RETRY_SB_READ_LEVEL_NUM] = {
	// P0	 P1    P2	 P3    P4	 P5    P6	 P7	   P8	 P9    P10   P11   P12   P13   P14
	// 0xE3										 SLC
	// 0xE2										 1st program TLC
	// 0xBC										 1st program QLC
	{0x1E, 0x1B, 0x1E, 0x1B, 0x1E, 0x1B, 0x1E, 0xFF, 0xFF, 0xFF, 0xFF,  0xFF, 0xFF, 0xFF, 0xFF},    // -3delta
	{0x14, 0x12, 0x14, 0x12, 0x14, 0x12, 0x14, 0xFF, 0xFF, 0xFF, 0xFF,  0xFF, 0xFF, 0xFF, 0xFF},    // -2delta
	{0x0A, 0x09, 0x0A, 0x09, 0x0A, 0x09, 0x0A, 0xFF, 0xFF, 0xFF, 0xFF,  0xFF, 0xFF, 0xFF, 0xFF},    // -1delta
	{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF,  0xFF, 0xFF, 0xFF, 0xFF},	   //  0
	{0x0A, 0x09, 0x0A, 0x09, 0x0A, 0x09, 0x0A, 0xFF, 0xFF, 0xFF, 0xFF,  0xFF, 0xFF, 0xFF, 0xFF},    //  1delta
	{0x14, 0x12, 0x14, 0x12, 0x14, 0x12, 0x14, 0xFF, 0xFF, 0xFF, 0xFF,  0xFF, 0xFF, 0xFF, 0xFF},    //  2delta
	{0x1E, 0x1B, 0x1E, 0x1B, 0x1E, 0x1B, 0x1E, 0xFF, 0xFF, 0xFF, 0xFF,  0xFF, 0xFF, 0xFF, 0xFF},    //  3delta
};

#elif (IM_N48R)
U8 delta_table[RETRY_SB_MAX_READ_OFFSET_SETP_NUM * RETRY_SB_MICRON_N48_DELTA_NUM * SB_MICRON_MAX_READ_OFFSET_FEATURE_NUM] = {
	0x00, 0x00, 0x00, 0x00,	// default all 0
	0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00,

	0x08, 0x04, 0x00, 0x00,	// BC QLC L Soft1,1H2S
	0x00, 0x00, 0x04, 0x02, // BA QLC L
	0x00, 0x00, 0x03, 0x02, // B9 QLC L
	0x00, 0x00, 0x04, 0x02, // BE QLC L
	0x00, 0x00, 0x04, 0x02, // BC QLC U
	0x00, 0x00, 0x04, 0x02, // BD QLC U
	0x04, 0x02, 0x00, 0x00, // BE QLC U
	0x03, 0x02, 0x00, 0x00, // BF QLC U
	0x04, 0x02, 0x00, 0x00, // B9 QLC X
	0x00, 0x00, 0x04, 0x02, // B8 QLC X
	0x00, 0x00, 0x03, 0x02, // BB QLC X
	0x04, 0x02, 0x00, 0x00, // BD QLC T
	0x04, 0x02, 0x00, 0x00, // BB QLC T
	0x03, 0x02, 0x00, 0x00, // BA QLC T
	0x00, 0x00, 0x03, 0x03, // BF QLC T
	0x0A, 0x0A, 0x00, 0x00, // E1 SLC
	0x07, 0x07, 0x00, 0x00, // E2 MLC L
	0x00, 0x00, 0x17, 0x12, // E1 MLC U
	0x00, 0x00, 0x0B, 0x0D, // E2 MLC U

	0x10, 0x08, 0x00, 0x00,	// BC QLC L Soft2,1H2S
	0x00, 0x00, 0x07, 0x04, // BA QLC L
	0x00, 0x00, 0x06, 0x04, // B9 QLC L
	0x00, 0x00, 0x07, 0x04, // BE QLC L
	0x00, 0x00, 0x07, 0x04, // BC QLC U
	0x00, 0x00, 0x07, 0x04, // BD QLC U
	0x06, 0x04, 0x00, 0x00, // BE QLC U
	0x06, 0x04, 0x00, 0x00, // BF QLC U
	0x07, 0x04, 0x00, 0x00, // B9 QLC X
	0x00, 0x00, 0x06, 0x04, // B8 QLC X
	0x00, 0x00, 0x06, 0x04, // BB QLC X
	0x07, 0x04, 0x00, 0x00, // BD QLC T
	0x07, 0x04, 0x00, 0x00, // BB QLC T
	0x07, 0x04, 0x00, 0x00, // BA QLC T
	0x00, 0x00, 0x06, 0x06, // BF QLC T
	0x14, 0x14, 0x00, 0x00, // E1 SLC
	0x0F, 0x0F, 0x00, 0x00, // E2 MLC L
	0x00, 0x00, 0x2F, 0x24, // E1 MLC U
	0x00, 0x00, 0x17, 0x1B, // E2 MLC U

	0x0A, 0x07, 0x00, 0x00,	// BC QLC L Soft1,1H1S
	0x00, 0x00, 0x07, 0x04, // BA QLC L
	0x00, 0x00, 0x06, 0x04, // B9 QLC L
	0x00, 0x00, 0x06, 0x04, // BE QLC L
	0x00, 0x00, 0x07, 0x04, // BC QLC U
	0x00, 0x00, 0x06, 0x04, // BD QLC U
	0x06, 0x04, 0x00, 0x00, // BE QLC U
	0x06, 0x04, 0x00, 0x00, // BF QLC U
	0x07, 0x04, 0x00, 0x00, // B9 QLC X
	0x00, 0x00, 0x06, 0x04, // B8 QLC X
	0x00, 0x00, 0x06, 0x04, // BB QLC X
	0x07, 0x04, 0x00, 0x00, // BD QLC T
	0x06, 0x04, 0x00, 0x00, // BB QLC T
	0x06, 0x04, 0x00, 0x00, // BA QLC T
	0x00, 0x00, 0x06, 0x06, // BF QLC T
	0x0A, 0x0A, 0x00, 0x00, // E1 SLC
	0x07, 0x07, 0x00, 0x00, // E2 MLC L
	0x00, 0x00, 0x17, 0x12, // E1 MLC U
	0x00, 0x00, 0x0B, 0x0D, // E2 MLC U
};

U8 sb_delta_table[RETRY_SB_MAX_READ_OFFSET_SETP_NUM * RETRY_SB_MICRON_N48_DELTA_NUM * SB_MICRON_MAX_READ_OFFSET_FEATURE_NUM] = { //MICRON
	0x00, 0x00, 0x00, 0x00,	// default all 0
	0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00,

	0x08, 0x04, 0x00, 0x00,	// BC QLC L Soft1,1H2S
	0x00, 0x00, 0x04, 0x02, // BA QLC L
	0x00, 0x00, 0x03, 0x02, // B9 QLC L
	0x00, 0x00, 0x04, 0x02, // BE QLC L
	0x00, 0x00, 0x04, 0x02, // BC QLC U
	0x00, 0x00, 0x04, 0x02, // BD QLC U
	0x04, 0x02, 0x00, 0x00, // BE QLC U
	0x03, 0x02, 0x00, 0x00, // BF QLC U
	0x04, 0x02, 0x00, 0x00, // B9 QLC X
	0x00, 0x00, 0x04, 0x02, // B8 QLC X
	0x00, 0x00, 0x03, 0x02, // BB QLC X
	0x04, 0x02, 0x00, 0x00, // BD QLC T
	0x04, 0x02, 0x00, 0x00, // BB QLC T
	0x03, 0x02, 0x00, 0x00, // BA QLC T
	0x00, 0x00, 0x03, 0x03, // BF QLC T
	0xD8, 0x28, 0x00, 0x00, // E1 SLC
	0xD8, 0x28, 0x00, 0x00, // E2 MLC L
	0x00, 0x00, 0xD8, 0x28, // E1 MLC U
	0x00, 0x00, 0xD8, 0x28, // E2 MLC U

	0x10, 0x08, 0x00, 0x00,	// BC QLC L Soft2,1H2S
	0x00, 0x00, 0x07, 0x04, // BA QLC L
	0x00, 0x00, 0x06, 0x04, // B9 QLC L
	0x00, 0x00, 0x07, 0x04, // BE QLC L
	0x00, 0x00, 0x07, 0x04, // BC QLC U
	0x00, 0x00, 0x07, 0x04, // BD QLC U
	0x06, 0x04, 0x00, 0x00, // BE QLC U
	0x06, 0x04, 0x00, 0x00, // BF QLC U
	0x07, 0x04, 0x00, 0x00, // B9 QLC X
	0x00, 0x00, 0x06, 0x04, // B8 QLC X
	0x00, 0x00, 0x06, 0x04, // BB QLC X
	0x07, 0x04, 0x00, 0x00, // BD QLC T
	0x07, 0x04, 0x00, 0x00, // BB QLC T
	0x07, 0x04, 0x00, 0x00, // BA QLC T
	0x00, 0x00, 0x06, 0x06, // BF QLC T
	0x9C, 0x64, 0x00, 0x00, // E1 SLC
	0x9C, 0x64, 0x00, 0x00, // E2 MLC L
	0x00, 0x00, 0x9C, 0x64, // E1 MLC U
	0x00, 0x00, 0x9C, 0x64, // E2 MLC U

	0x0A, 0x07, 0x00, 0x00,	// BC QLC L Soft1,1H1S
	0x00, 0x00, 0x07, 0x04, // BA QLC L
	0x00, 0x00, 0x06, 0x04, // B9 QLC L
	0x00, 0x00, 0x06, 0x04, // BE QLC L
	0x00, 0x00, 0x07, 0x04, // BC QLC U
	0x00, 0x00, 0x06, 0x04, // BD QLC U
	0x06, 0x04, 0x00, 0x00, // BE QLC U
	0x06, 0x04, 0x00, 0x00, // BF QLC U
	0x07, 0x04, 0x00, 0x00, // B9 QLC X
	0x00, 0x00, 0x06, 0x04, // B8 QLC X
	0x00, 0x00, 0x06, 0x04, // BB QLC X
	0x07, 0x04, 0x00, 0x00, // BD QLC T
	0x06, 0x04, 0x00, 0x00, // BB QLC T
	0x06, 0x04, 0x00, 0x00, // BA QLC T
	0x00, 0x00, 0x06, 0x06, // BF QLC T
	0xD8, 0x28, 0x00, 0x00, // E1 SLC
	0xD8, 0x28, 0x00, 0x00, // E2 MLC L
	0x00, 0x00, 0xD8, 0x28, // E1 MLC U
	0x00, 0x00, 0xD8, 0x28, // E2 MLC U
};

U32 sb_llr_table[RETRY_MICRON_TABLR_PARAMETER_NUM][DEFAULT_LLR_TABLE_LENGTH] = {
	/* Default LLR table */
	{
		0x00000004, 0x00000000, 0x000c0000, 0x00000000, 0x00000000, 0x0000001c, 0x00000000, 0x00140000, 0x00000000, 0x00000000 // 1H1S
		//       0          4           8           12          16          20          24          28          32          36
	},

	{
		0x00000002, 0x00000000, 0x00060000, 0x0c000000, 0x00000000, 0x0000001E, 0x00000000, 0x001a0000, 0x14000000, 0x00000000 // 1H2S
		//       0          4           8           12          16          20          24          28          32          36
	},

	{
		0xC1D00C07, 0x00000000, 0x200A0000, 0x0F020000, 0x03805038, 0x40307419, 0x00000007, 0x60160000, 0x11060000, 0x0481B048 // Turbo Rain
		//       0          4           8           12          16          20          24          28          32          36
	},

};

U32 sb_dsp_graycode[RETRY_MICRON_TABLR_PARAMETER_NUM][DSP_GRAYCODE_LENGTH] = {
	{
		0x10002030, 0x01010101, 0x01010101, 0x01010101, 0x01010101, 0x01010101, 0x01010101, 0x01010101,  // 1H1S
		0x01010101, 0x01010101, 0x01010101, 0x01010101, 0x01010101, 0x01010101, 0x01010101, 0x01010101
	},

	{
		0x00203038, 0x01011810, 0x01010101, 0x01010101, 0x01010101, 0x01010101, 0x01010101, 0x01010101,  // 1H2S
		0x01010101, 0x01010101, 0x01010101, 0x01010101, 0x01010101, 0x01010101, 0x01010101, 0x01010101
	},
};


#endif/*IM_N18*/

#if (!IM_N48R)
U32 sb_llr_table[4][4][DEFAULT_LLR_TABLE_LENGTH] = { //MICRON QLC
	//Micron SB Todo
	/* Default LLR table0 */
	{
		{0x00000002, 0x00000100, 0x00050000, 0x0C000000, 0x00000000, 0x00000018, 0x00001F00, 0x00160000, 0x14000000, 0x00000000}, // Low
		{0x00200005, 0x10000100, 0x000A0000, 0x0C000050, 0x0000C000, 0x01800018, 0xF0001F00, 0x00160001, 0x14000160, 0x00014000}, // Upper
		{0x80501402, 0x10040100, 0x28050040, 0x0C0140A0, 0x0300C030, 0x01806018, 0xF07C1F06, 0x581607C1, 0x14058160, 0x05014050}, // eXtra
		{0x4A228845, 0x10842111, 0x14AA0842, 0x8C2A9455, 0x6318C631, 0x318C6318, 0xFFFFFFC6, 0x5AD6FFFF, 0x94B5AD6B, 0xA5294A52}, // Top
	},

	/* Default LLR table1 */
	{
		{0x00000002, 0x00000100, 0x00050000, 0x0C000000, 0x00000000, 0x00000018, 0x00001F00, 0x00160000, 0x14000000, 0x00000000}, // Low
		{0x00200005, 0x10000100, 0x000A0000, 0x0C000050, 0x0000C000, 0x01800018, 0xF0001F00, 0x00160001, 0x14000160, 0x00014000}, // Upper
		{0x80501402, 0x10040100, 0x28050040, 0x0C0140A0, 0x0300C030, 0x01806018, 0xF07C1F06, 0x581607C1, 0x14058160, 0x05014050}, // eXtra
		{0x4A228845, 0x10842111, 0x14AA0842, 0x8C2A9455, 0x6318C631, 0x318C6318, 0xFFFFFFC6, 0x5AD6FFFF, 0x94B5AD6B, 0xA5294A52}, // Top
	},

	/* Default LLR table */
	{
		{0x00000002, 0x00000100, 0x00050000, 0x0C000000, 0x00000000, 0x00000018, 0x00001F00, 0x00160000, 0x14000000, 0x00000000}, // Low
		{0x00200005, 0x10000100, 0x000A0000, 0x0C000050, 0x0000C000, 0x01800018, 0xF0001F00, 0x00160001, 0x14000160, 0x00014000}, // Upper
		{0x80501402, 0x10040100, 0x28050040, 0x0C0140A0, 0x0300C030, 0x01806018, 0xF07C1F06, 0x581607C1, 0x14058160, 0x05014050}, // eXtra
		{0x4A228845, 0x10842111, 0x14AA0842, 0x8C2A9455, 0x6318C631, 0x318C6318, 0xFFFFFFC6, 0x5AD6FFFF, 0x94B5AD6B, 0xA5294A52}, // Top
	},

	/* Default LLR table */
	{
		{0x00000002, 0x00000100, 0x00050000, 0x0C000000, 0x00000000, 0x00000018, 0x00001F00, 0x00160000, 0x14000000, 0x00000000}, // Low
		{0x00200005, 0x10000100, 0x000A0000, 0x0C000050, 0x0000C000, 0x01800018, 0xF0001F00, 0x00160001, 0x14000160, 0x00014000}, // Upper
		{0x80501402, 0x10040100, 0x28050040, 0x0C0140A0, 0x0300C030, 0x01806018, 0xF07C1F06, 0x581607C1, 0x14058160, 0x05014050}, // eXtra
		{0x4A228845, 0x10842111, 0x14AA0842, 0x8C2A9455, 0x6318C631, 0x318C6318, 0xFFFFFFC6, 0x5AD6FFFF, 0x94B5AD6B, 0xA5294A52}, // Top
	},
};

U32 sb_dsp_graycode[4][DSP_GRAYCODE_LENGTH] = {	//MICRON QLC
	{
		0x28203038, 0x18100008, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,  // Low
		0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000
	},

	{
		0x2C24343C, 0x1C14040C, 0x08001018, 0x38302028, 0x00000000, 0x00000000, 0x00000000, 0x00000000,  // Upper
		0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000
	},

	{
		0x2E26363E, 0x1E16060E, 0x0C04141C, 0x3C34242C, 0x28203038, 0x18100008, 0x0A02121A, 0x3A32222A,  // eXtra
		0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000
	},

	{
		0x2F27373F, 0x1F17070F, 0x0E06161E, 0x3E36262E, 0x2C24343C, 0x1C14040C, 0x0D05151D, 0x3D35252D,  // Top
		0x29213139, 0x19110109, 0x08001018, 0x38302028, 0x2A22323A, 0x1A12020A, 0x0B03131B, 0x3B33232B
	},
};

#endif
//Micron SB Todo
#if (!IM_N48R)

U32 sb_dsp2_llr_table[RETRY_SB_DSP2_PAGE_NUM][4][RETRY_SB_DSP2_PARAM_NUM] = { //MICRON QLC
	//11b for 	-1
	//01b for 	+1
	//00b for		0
	//10b not define
	/* DSP2 LLR table for Lower */
	{
		// 	0 ~ 15               |  15 ~ 31              |  32 ~ 47              |  48 ~ 63
		{0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // LLR0		//SB6 = 0, SB7 = 0
		{0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // LLR1		//SB6 = 0, SB7 = 1
		{0x0000000F, 0x0000000F, 0x0000000E, 0x0000000E, 0x0000000E, 0x0000000F, 0x0000000E, 0x0000000F}, // LLR2		//SB6 = 1, SB7 = 0
		{0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Top		//SB6 = 1, SB7 = 1
	},

	/* DSP2 LLR table for Upper */
	{
		// 	0 ~ 15               |  15 ~ 31              |  32 ~ 47              |  48 ~ 63
		{0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // LLR0		//SB6 = 0, SB7 = 0
		{0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // LLR1		//SB6 = 0, SB7 = 1
		{0x000F0002, 0x000F0001, 0x000E0002, 0x000E0001, 0x000E0002, 0x000F0001, 0x000E0002, 0x000F0002}, // LLR2		//SB6 = 1, SB7 = 0
		{0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Top		//SB6 = 1, SB7 = 1
	},

	/* DSP2 LLR table for eXtra */
	{
		// 	0 ~ 15               |  15 ~ 31              |  32 ~ 47              |  48 ~ 63
		{0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // LLR0		//SB6 = 0, SB7 = 0
		{0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // LLR1		//SB6 = 0, SB7 = 1
		{0x0F02020F, 0x0F01010F, 0x0E02020E, 0x0E01010E, 0x0E02020E, 0x0F01010F, 0x0E02020E, 0x0F02020F}, // LLR2		//SB6 = 1, SB7 = 0
		{0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Top		//SB6 = 1, SB7 = 1
	},

	/* DSP2 LLR table for TOP (JC 1009 Temp copy from extra)*/
	{
		// 	0 ~ 15               |  15 ~ 31              |  32 ~ 47              |  48 ~ 63
		{0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // LLR0		//SB6 = 0, SB7 = 0
		{0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // LLR1		//SB6 = 0, SB7 = 1
		{0xF22F2FF2, 0xF11F1FF1, 0xE22E2EE2, 0xE11E1EE1, 0xE22E2EE2, 0xF11F1FF1, 0xE22E2EE2, 0xF22F2FF2}, // LLR2		//SB6 = 1, SB7 = 0
		{0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Top		//SB6 = 1, SB7 = 1
	},
};
#endif
U32 sb_dsp_lut[4][DSP_LUT_LENGTH] = { //MICRON QLC
	{
		0xF2F20000, 0x0E0000FF, 0x0000010E, 0x00010E0E, 0xFFF2F200, 0xF2F20000, 0x0E0000FF, 0x0000010E,  // Low
		0x00010E0E, 0xFFF2F200, 0xF2F20000, 0x0E0000FF, 0x0000010E, 0x00010E0E, 0xFFF2F200, 0xF2F20000,
		0x0E0000FF, 0x0000010E, 0x00010E0E, 0xFFF2F200
	},

	{
		0xF2F20000, 0x0E0000FF, 0x0000010E, 0x00010E0E, 0xFFF2F200, 0xF2F20000, 0x0E0000FF, 0x0000010E,  // Upper
		0x00010E0E, 0xFFF2F200, 0xF2F20000, 0x0E0000FF, 0x0000010E, 0x00010E0E, 0xFFF2F200, 0xF2F20000,
		0x0E0000FF, 0x0000010E, 0x00010E0E, 0xFFF2F200
	},

	{
		0xF2F20000, 0x0E0000FF, 0x0000010E, 0x00010E0E, 0xFFF2F200, 0xF2F20000, 0x0E0000FF, 0x0000010E,  // eXtra
		0x00010E0E, 0xFFF2F200, 0xF2F20000, 0x0E0000FF, 0x0000010E, 0x00010E0E, 0xFFF2F200, 0xF2F20000,
		0x0E0000FF, 0x0000010E, 0x00010E0E, 0xFFF2F200
	},

	{
		0xF2F20000, 0x0E0000FF, 0x0000010E, 0x00010E0E, 0xFFF2F200, 0xF2F20000, 0x0E0000FF, 0x0000010E,  // Top
		0x00010E0E, 0xFFF2F200, 0xF2F20000, 0x0E0000FF, 0x0000010E, 0x00010E0E, 0xFFF2F200, 0xF2F20000,
		0x0E0000FF, 0x0000010E, 0x00010E0E, 0xFFF2F200
	},
};

U32 sb_dsp_param[4][DSP_PARAM_LENGTH] = { //MICRON QLC

	{0x08010808, 0x00000000, 0x00000000, 0x20181008, 0x00383028, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Low
	{0x08021008, 0x00000008, 0x00000000, 0x20181008, 0x00383028, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Upper
	{0x08042008, 0x00080808, 0x00000000, 0x20181008, 0x00383028, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // eXtra
	{0x08084008, 0x08080808, 0x00080808, 0x20181008, 0x00383028, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Top
};

U32 gulGroupTable = 0x65555555; //MICRON QLC

U8 Offset_delta_table[RETRY_SB_READ_LEVEL_NUM] = {
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0
};


#if(TRUE == RETRY_MICRON_NICKS)
U8 gubSpecificRetryTable[SB_MICRON_SPECIFIC_RETRY_TABLE_MAX_CNT] = {
	5, 6, 0, 0, 0, 0, 8
};
#else	/*(TRUE == RETRY_MICRON_NICKS)*/
U8 gubSpecificRetryTable[SB_MICRON_SPECIFIC_RETRY_TABLE_MAX_CNT] = {
	0, 5, 2, 4
};
#endif	/*(TRUE == RETRY_MICRON_NICKS)*/

U8 gubSpecificReadOffset[SB_MICRON_SPECIFIC_READ_OFFSET_MAX_CNT] = {
	0xD8, 0x21
};
#if (IM_N18)
U8 gubARCResultReadOffsetTable[RETRY_SB_DELTA_NUM][RETRY_SB_READ_LEVEL_NUM] = {
	//	   R1	 R2    R3	 R4    R5	 R6    R7	 R8    R9	R10   R11	R12   R13	R14   R15
	{0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12}, // -3delta
	{0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C}, // -2delta
	{0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06}, // -1delta
	{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, //	0
	{0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06}, //	1delta
	{0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C}, //	2delta
	{0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12}, //	3delta
};
#elif (IM_N28)
U8 gubARCResultReadOffsetTable[RETRY_SB_DELTA_NUM][RETRY_SB_READ_LEVEL_NUM] = {
	// P0	 P1    P2	 P3    P4	 P5    P6	 P7	   P8	 P9    P10   P11   P12   P13   P14
	{0x0E, 0x0F, 0x0E, 0x0F, 0x0E, 0x0F, 0x0E, 0x0F, 0x0E, 0x0F, 0x0E,  0x0F, 0x0E, 0x0F, 0x0E},    // -3delta
	{0x09, 0x0A, 0x09, 0x0A, 0x09, 0x0A, 0x09, 0x0A, 0x09, 0x0A, 0x09,  0x0A, 0x09, 0x0A, 0x09},    // -2delta
	{0x04, 0x05, 0x04, 0x05, 0x04, 0x05, 0x04, 0x05, 0x04, 0x05, 0x04,  0x05, 0x04, 0x05, 0x04},    // -1delta
	{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  0x00, 0x00, 0x00, 0x00},	//  0
	{0x04, 0x05, 0x04, 0x05, 0x04, 0x05, 0x04, 0x05, 0x04, 0x05, 0x04,  0x05, 0x04, 0x05, 0x04},    //  1delta
	{0x09, 0x0A, 0x09, 0x0A, 0x09, 0x0A, 0x09, 0x0A, 0x09, 0x0A, 0x09,  0x0A, 0x09, 0x0A, 0x09},    //  2delta
	{0x0E, 0x0F, 0x0E, 0x0F, 0x0E, 0x0F, 0x0E, 0x0F, 0x0E, 0x0F, 0x0E,  0x0F, 0x0E, 0x0F, 0x0E},    //  3delta
};
#elif (IM_N48R_NEED_CHECK)
U8 gubARCResultReadOffsetTable[RETRY_SB_DELTA_NUM][RETRY_SB_READ_LEVEL_NUM] = {
	//	   R1	 R2    R3	 R4    R5	 R6    R7	 R8    R9	R10   R11	R12   R13	R14   R15
	{0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12}, // -3delta
	{0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C}, // -2delta
	{0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06}, // -1delta
	{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, //	0
	{0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06}, //	1delta
	{0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C}, //	2delta
	{0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12}, //	3delta
};
#endif/*IM_N18*/
//MICRON QLC
U8 gubMicronReadOffsetFeatureAddressNumber[RETRY_SB_PAGE_NUM] = {//PageType Number
	//Lower, Upper, eXtra Top
	1,     2,    4,    8,
};
//MICRON QLC
#if (!IM_N48R)
U8 gubRetrySBMicronReadOffsetFeatureAddress[RETRY_SB_WL_NUM][RETRY_SB_PAGE_NUM][SB_MICRON_MAX_READ_OFFSET_FEATURE_NUM] = {	//PageType Number, Feature Number
	{
		//TLC WL
		{ 0xD3, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF}, //Lower 		//0xFF for Invalid Feature Address (Reserved in spec)
		{ 0xD1, 0xD5, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF}, //Upper
		{ 0xD0, 0xD2, 0xD4, 0xD6, 0xFF, 0xFF, 0xFF, 0xFF}, //eXtra
		{ 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF}  //TOP
	},
	{
		//OPEN QLC
		{ 0xA8, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF}, //Lower		//0xFF for Invalid Feature Address (Reserved in spec)
		{ 0xA6, 0xAA, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF}, //Upper
		{ 0xA5, 0xA7, 0xA9, 0xAB, 0xFF, 0xFF, 0xFF, 0xFF}, //eXtra
		{ 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF}  //TOP
	},
	{
		//CLOSE QLC
		{ 0xC8, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF}, //Lower		//0xFF for Invalid Feature Address (Reserved in spec)
		{ 0xC4, 0xCC, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF}, //Upper
		{ 0xC2, 0xC6, 0xCA, 0xCE, 0xFF, 0xFF, 0xFF, 0xFF}, //eXtra
		{ 0xC1, 0xC3, 0xC5, 0xC7, 0xC9, 0xCB, 0xCD, 0xCF}  //TOP
	}
};
//MICRON QLC
U8 gubRetrySBMircronPageTypesReadOffsetIndexOnDeltaTable[RETRY_SB_WL_NUM][RETRY_SB_PAGE_NUM][SB_MICRON_MAX_READ_OFFSET_FEATURE_NUM] = {	//PageType Number, Feature Number
	{
		//TLC WL
		{ 3,   0xFF,   0xFF,  0xFF, 0xFF, 0xFF, 0xFF, 0xFF},  	//Lower 		//0xFF for Invalid
		{ 1,      5,   0xFF,  0xFF, 0xFF, 0xFF, 0xFF, 0xFF},  	//Upper
		{ 0,      2,      4,     6, 0xFF, 0xFF, 0xFF, 0xFF},  	//eXtra
		{ 0xFF, 0xFF,   0xFF,  0xFF, 0xFF, 0xFF, 0xFF, 0xFF}  	//TOP
	},
	{
		//OPEN QLC
		{ 3,   0xFF,   0xFF,  0xFF, 0xFF, 0xFF, 0xFF, 0xFF},  	//Lower 		//0xFF for Invalid
		{ 1,      5,   0xFF,  0xFF, 0xFF, 0xFF, 0xFF, 0xFF},  	//Upper
		{ 0,      2,      4,     6, 0xFF, 0xFF, 0xFF, 0xFF},  	//eXtra
		{ 0xFF, 0xFF,   0xFF,  0xFF, 0xFF, 0xFF, 0xFF, 0xFF}  	//TOP
	},
	{
		//CLOSE QLC
		{ 7,   0xFF,   0xFF,  0xFF, 0xFF, 0xFF, 0xFF, 0xFF},  	//Lower 		//0xFF for Invalid
		{ 3,     11,   0xFF,  0xFF, 0xFF, 0xFF, 0xFF, 0xFF},  	//Upper
		{ 1,      5,      9,    13, 0xFF, 0xFF, 0xFF, 0xFF},  	//eXtra
		{ 0,      2,      4,     6,    8,   10,   12,   14}  	//TOP
	}
};
#endif /*(!IM_N48R)*/
#else /* (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC) */

#if ((IM_B47R || IM_B37R) && (TRUE == RETRY_MICRON_NICKS_ALL_PAGE_TYPE_SB_SUPPORT))	//Phison Flow temp use the same SB read flow with Micron Nicks

U8 default_delta_table[RETRY_SB_MAX_READ_OFFSET_SETP_NUM * RETRY_SB_DELTA_NUM * SB_MICRON_MAX_READ_OFFSET_FEATURE_NUM] = {
	//P1    P2    P3    P4
	0x00, 0x00, 0x00, 0x00,	// B1 default all 0
	0x00, 0x00, 0x00, 0x00, // B2
	0x00, 0x00, 0x00, 0x00, // B3
	0x00, 0x00, 0x00, 0x00, // B4
	0x00, 0x00, 0x00, 0x00, // E1
	0x00, 0x00, 0x00, 0x00, // E2
	0x00, 0x00, 0x00, 0x00, // E3

	0x08, 0x08, 0x08, 0x08,	// B1 Soft1,1H2S
	0x08, 0x08, 0x08, 0x08, // B2
	0x08, 0x08, 0x00, 0x00, // B3
	0x08, 0x0C, 0x08, 0x08, // B4
	0x08, 0x0A, 0x0B, 0x0C, // E1
	0x0B, 0x0D, 0x00, 0x00, // E2
	0x00, 0x00, 0x08, 0x0E, // E3

	0x12, 0x16, 0x17, 0x13,	// B1 Soft2,1H1S
	0x12, 0x16, 0x12, 0x16, // B2
	0x17, 0x13, 0x00, 0x00, // B3
	0x16, 0x22, 0x12, 0x16, // B4
	0x13, 0x18, 0x1F, 0x21, // E1
	0x21, 0x20, 0x00, 0x00, // E2
	0x00, 0x00, 0x10, 0x21,  // E3

	0x0C, 0x08, 0x0F, 0x0F,	// B1 Soft1,1H1S
	0x0C, 0x08, 0x0C, 0x08, // B2
	0x0F, 0x0F, 0x00, 0x00, // B3
	0x0D, 0x16, 0x0C, 0x08, // B4
	0x15, 0x0E, 0x10, 0x10, // E1
	0x0F, 0x10, 0x00, 0x00, // E2
	0x00, 0x00, 0x10, 0x10  // E3
};

U8 sb_delta_table[RETRY_SB_MAX_READ_OFFSET_SETP_NUM * RETRY_SB_DELTA_NUM * SB_MICRON_MAX_READ_OFFSET_FEATURE_NUM] = { // B47
	//P1    P2    P3    P4
	0x00, 0x00, 0x00, 0x00,	// B1 default all 0
	0x00, 0x00, 0x00, 0x00, // B2
	0x00, 0x00, 0x00, 0x00, // B3
	0x00, 0x00, 0x00, 0x00, // B4
	0x00, 0x00, 0x00, 0x00, // E1
	0x00, 0x00, 0x00, 0x00, // E2
	0x00, 0x00, 0x00, 0x00, // E3

	0x08, 0x08, 0x08, 0x08,	// B1 Soft1,1H2S
	0x08, 0x08, 0x08, 0x08, // B2
	0x08, 0x08, 0x00, 0x00, // B3
	0x08, 0x0C, 0x08, 0x08, // B4
	0x08, 0x0A, 0x0B, 0x0C, // E1
	0x0B, 0x0D, 0x00, 0x00, // E2
	0x00, 0x00, 0x08, 0x0E, // E3

	0x12, 0x16, 0x17, 0x13,	// B1 Soft2,1H1S
	0x12, 0x16, 0x12, 0x16, // B2
	0x17, 0x13, 0x00, 0x00, // B3
	0x16, 0x22, 0x12, 0x16, // B4
	0x13, 0x18, 0x1F, 0x21, // E1
	0x21, 0x20, 0x00, 0x00, // E2
	0x00, 0x00, 0x10, 0x21, // E3

	0x0C, 0x08, 0x0F, 0x0F,	// B1 Soft1,1H1S
	0x0C, 0x08, 0x0C, 0x08, // B2
	0x0F, 0x0F, 0x00, 0x00, // B3
	0x0D, 0x16, 0x0C, 0x08, // B4
	0x15, 0x0E, 0x10, 0x10, // E1
	0x0F, 0x10, 0x00, 0x00, // E2
	0x00, 0x00, 0x10, 0x10  // E3
};

U8 delta_table[RETRY_SB_MAX_READ_OFFSET_SETP_NUM * RETRY_SB_DELTA_NUM * SB_MICRON_MAX_READ_OFFSET_FEATURE_NUM] = {
	//P1    P2    P3    P4
	0x00, 0x00, 0x00, 0x00,	// B1 default all 0
	0x00, 0x00, 0x00, 0x00, // B2
	0x00, 0x00, 0x00, 0x00, // B3
	0x00, 0x00, 0x00, 0x00, // B4
	0x00, 0x00, 0x00, 0x00, // E1
	0x00, 0x00, 0x00, 0x00, // E2
	0x00, 0x00, 0x00, 0x00, // E3

	0x08, 0x08, 0x08, 0x08,	// B1 Soft1,1H2S
	0x08, 0x08, 0x08, 0x08, // B2
	0x08, 0x08, 0x00, 0x00, // B3
	0x08, 0x0C, 0x08, 0x08, // B4
	0x08, 0x0A, 0x0B, 0x0C, // E1
	0x0B, 0x0D, 0x00, 0x00, // E2
	0x00, 0x00, 0x08, 0x0E, // E3

	0x12, 0x16, 0x17, 0x13,	// B1 Soft2,1H1S
	0x12, 0x16, 0x12, 0x16, // B2
	0x17, 0x13, 0x00, 0x00, // B3
	0x16, 0x22, 0x12, 0x16, // B4
	0x13, 0x18, 0x1F, 0x21, // E1
	0x21, 0x20, 0x00, 0x00, // E2
	0x00, 0x00, 0x10, 0x21, // E3

	0x0C, 0x08, 0x0F, 0x0F,	// B1 Soft1,1H1S
	0x0C, 0x08, 0x0C, 0x08, // B2
	0x0F, 0x0F, 0x00, 0x00, // B3
	0x0D, 0x16, 0x0C, 0x08, // B4
	0x15, 0x0E, 0x10, 0x10, // E1
	0x0F, 0x10, 0x00, 0x00, // E2
	0x00, 0x00, 0x10, 0x10  // E3
};

U32 sb_llr_table[RETRY_MICRON_TABLR_PARAMETER_NUM][DEFAULT_LLR_TABLE_LENGTH] = {
	/* Default LLR table */
	{
		0x00000004, 0x00000000, 0x000c0000, 0x00000000, 0x00000000, 0x0000001c, 0x00000000, 0x00200000, 0x00000000, 0x00000000 // 1H1S
		//       0          4           8           12          16          20          24          28          32          36
	},

	{
		0x00000002, 0x00000000, 0x00060000, 0x0c000000, 0x00000000, 0x0000001E, 0x00000000, 0x001a0000, 0x14000000, 0x00000000 // 1H2S
		//       0          4           8           12          16          20          24          28          32          36
	},

};

U32 sb_dsp_graycode[RETRY_MICRON_TABLR_PARAMETER_NUM][DSP_GRAYCODE_LENGTH] = {
	{
		0x10002030, 0x01010101, 0x01010101, 0x01010101, 0x01010101, 0x01010101, 0x01010101, 0x01010101,  // 1H1S
		0x01010101, 0x01010101, 0x01010101, 0x01010101, 0x01010101, 0x01010101, 0x01010101, 0x01010101
	},

	{
		0x00203038, 0x01011810, 0x01010101, 0x01010101, 0x01010101, 0x01010101, 0x01010101, 0x01010101,  // 1H2S
		0x01010101, 0x01010101, 0x01010101, 0x01010101, 0x01010101, 0x01010101, 0x01010101, 0x01010101
	},
};

#if(FALSE == RETRY_MICRON_NICKS)
U8 gubCnt1Result[3][9] =  {
	//PARM is the same with N_State
	//PARM1	PARM2	PARM3	PARM4		PARM_LENGTH		PARM_CNT	FRAME_CNT	ROUND_CNT	SKIP_BITMAP
	//PARM1	PARM2	PARM3	PARM4		PARM_LENGTH		PARM_CNT	SIGN_BMP	ROUND_CNT	SKIP_BITMAP
	{ 0x03,	0x00,	0,		0,			1,				0,			0,			0,			0x0},      //LOW
	{ 0x01,	0x05,	0,		0,			2,				0,			0,			0,			0x0},        //UPPER
	{ 0x00,	0x02,	0x04,	0x06,		4,				0,			0,			0,			0x0},       //EXTRA
};

U8 gubMicronReadOffsetFeatureAddress[SB_MICRON_MAX_PAGETYPE_NUM][SB_MICRON_MAX_READ_OFFSET_FEATURE_NUM] = {	//PageType Number, Feature Number
	{ 0xA8, 0xFF, 0xFF, 0xFF},  //Lower 		//0xFF for Invalid Feature Address (Reserved in spec)
	{ 0xA6, 0xAA, 0xFF, 0xFF},  //Upper
	{ 0xA5, 0xA7, 0xA9, 0xAB}  //eXtra
};

U8 gubMircronPageTypesReadOffsetIndexOnDeltaTable[SB_MICRON_MAX_PAGETYPE_NUM][SB_MICRON_MAX_READ_OFFSET_FEATURE_NUM] = {	//PageType Number, Feature Number
	{ 0,      4, 0xFF, 0xFF},  //Lower 		//0xFF for Invalid
	{ 1,      3,      5, 0xFF},  //Upper
	{ 2,      6, 0xFF, 0xFF}  //eXtra
};

U8 gubCoarseTuningStateValue[RETRY_SB_READ_LEVEL_NUM] = {
	//N_State
	// A,	B,	  C,      D,      E,      F,      G
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};

/*for B47R*/
U8 gubRetrySBMicronCntOneInfo[RETRY_SB_PAGE_NUM][9] =  {
	//PARM is the same with N_State
	//PARM1				PARM2				PARM3				PARM4				PARM_LENGTH		PARM_CNT	SIGN_BITMAP	ROUND_CNT	SKIP_BITMAP
	{ RETRY_SB_A_STATE,	RETRY_SB_E_STATE,	RETRY_SB_N_STATE,	RETRY_SB_N_STATE,	2,				0,			0x0,			0,			0x0},      //LOW
	{ RETRY_SB_B_STATE,	RETRY_SB_D_STATE,	RETRY_SB_F_STATE,	RETRY_SB_N_STATE,	3,				0,			0x0,			0,			0x0},        //UPPER
	{ RETRY_SB_C_STATE,	RETRY_SB_G_STATE,	RETRY_SB_N_STATE,	RETRY_SB_N_STATE,	2,				0,			0x0,			0,			0x0},       //EXTRA
};

U8 gubCount1DSPDistributionIdxTable[RETRY_SB_READ_LEVEL_NUM] = {
	56, 24, 8, 0, 16, 48, 32					//SB_DSITRI_56, SB_DSITRI_24, SB_DSITRI_08, SB_DSITRI_00,  SB_DSITRI_16,  SB_DSITRI_48,  SB_DSITRI_32,
	/*
	 * List of N_State and SB_DSITRI_X
	 * A_State, SB_DSITRI_56
	 * B_State, SB_DSITRI_56, SB_DSITRI_24,
	 * C_State, SB_DSITRI_56, SB_DSITRI_24, SB_DSITRI_08
	 * D_State, SB_DSITRI_56, SB_DSITRI_24, SB_DSITRI_08, SB_DSITRI_00
	 * E_State, SB_DSITRI_56, SB_DSITRI_24, SB_DSITRI_08, SB_DSITRI_00,  SB_DSITRI_16
	 * F_State, SB_DSITRI_56, SB_DSITRI_24, SB_DSITRI_08, SB_DSITRI_00,  SB_DSITRI_16,  SB_DSITRI_48
	 * G_State, SB_DSITRI_56, SB_DSITRI_24, SB_DSITRI_08, SB_DSITRI_00,  SB_DSITRI_16,  SB_DSITRI_48,  SB_DSITRI_32,
	*/
};

#endif /*(FALSE == RETRY_MICRON_NICKS)*/

#else/* (IM_B47R && (TRUE == RETRY_MICRON_NICKS_ALL_PAGE_TYPE_SB_SUPPORT)) */
U8 delta_table[RETRY_SB_DELTA_NUM][RETRY_SB_READ_LEVEL_NUM] = {
	//r011,	r001,	r101,	r100,	r000,	r010,	r110
	//B1h,	B3h,		B1h,		B4h,		B2h,		B3h,		B2h
	{0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12},    // -3delta
	{0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C},    // -2delta
	{0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06},    // -delta
	{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},    // 0
	{0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06},    // +delta
	{0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C},    // +2delta
	{0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12},    // +3delta
};

U8 default_delta_table[RETRY_SB_DELTA_NUM][RETRY_SB_READ_LEVEL_NUM] = {// default_delta_table_MICRON
	//r011,	r001,	r101,	r100,	r000,	r010,	r110
	//B1h,	B3h,		B1h,		B4h,		B2h,		B3h,		B2h
	//A5h, 	A6h, 	A7h,		A8h, 	A9h,		AAh,		ABh		(for[3][0~7] Read offset)
	{0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12},    // -3delta
	{0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C},    // -2delta
	{0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06},    // -delta
	{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},    // 0
	{0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06},    // +delta
	{0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C},    // +2delta
	{0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12},    // +3delta
};

U8 sb_delta_table[RETRY_SB_DELTA_NUM][RETRY_SB_READ_LEVEL_NUM] = { //MICRON
	//r011,	r001,	r101,	r100,	r000,	r010,	r110
	//B1h,	B3h,		B1h,		B4h,		B2h,		B3h,		B2h
	{0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12},    // -3delta
	{0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C},    // -2delta
	{0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06},    // -delta
	{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},    // 0
	{0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06},    // +delta
	{0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C},    // +2delta
	{0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12},    // +3delta
};

U8 sb_slc_mlc_delta_table[RETRY_SB_DELTA_NUM][RETRY_SB_READ_LEVEL_NUM] = { //MICRON, SLC/MLC/TLC Open
	//r011,	r001,	r101,	r100,	r000,	r010,	r110
	//XXh,	XXh,		XXh,		B4h,		XXh,		XXh,		XXh,		SLC
	//XXh,	XXh,		XXh,		E3h,		XXh,		XXh,		XXh,		TLC Open
	//XXh,	E1h,		XXh,		E2h,		XXh,		E1h,		XXh		MLC
	{0x00, 0x1E, 0x00, 0x1E, 0x00, 0x1E, 0x00},    // -3delta
	{0x00, 0x14, 0x00, 0x14, 0x00, 0x14, 0x00},    // -2delta
	{0x00, 0x0A, 0x00, 0x0A, 0x00, 0x0A, 0x00},    // -delta
	{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},    // 0
	{0x00, 0x0A, 0x00, 0x0A, 0x00, 0x0A, 0x00},    // +delta
	{0x00, 0x14, 0x00, 0x14, 0x00, 0x14, 0x00},    // +2delta
	{0x00, 0x1E, 0x00, 0x1E, 0x00, 0x1E, 0x00},    // +3delta
};

U32 sb_llr_table[4][4][DEFAULT_LLR_TABLE_LENGTH] = {	//B16A
	//Micron SB Todo
	/* Default LLR table0 */
	{
		{0x00000005, 0x00000100, 0x00080000, 0x0e000000, 0x00000000, 0x0000001c, 0x00001f00, 0x001a0000, 0x13000000, 0x00000000}, // Low
		{0x00800005, 0x10000100, 0x000a0000, 0x0c0000a0, 0x0000d000, 0x01b00018, 0xf0001f00, 0x00160001, 0x13000160, 0x00014000}, // Upper
		{0x00501408, 0x10040102, 0x280a0040, 0x0c0280a0, 0x0300c030, 0xc180601b, 0xf07c1f06, 0x581607c1, 0x14058160, 0x0501304c}, // eXtra
		{0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Top
	},

	/* Default LLR table1 */
	{
		{0x00000002, 0x00000100, 0x00050000, 0x0c000000, 0x00000000, 0x00000018, 0x00001f00, 0x00160000, 0x14000000, 0x00000000},  // Low
		{0x00200005, 0x10000100, 0x000a0000, 0x0c000050, 0x0000c000, 0x01800018, 0xf0001f00, 0x00160001, 0x14000160, 0x00014000},  // Upper
		{0x80501402, 0x10040100, 0x28050040, 0x0c0140a0, 0x0300c030, 0x01806018, 0xf07c1f06, 0x581607c1, 0x14058160, 0x05014050},  // eXtra
		{0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000},  // Top
	},

	/* Default LLR table */
	{
		{0x00000002, 0x00000100, 0x00050000, 0x0c000000, 0x00000000, 0x00000018, 0x00001f00, 0x00160000, 0x14000000, 0x00000000},  // Low
		{0x00200005, 0x10000100, 0x000a0000, 0x0c000050, 0x0000c000, 0x01800018, 0xf0001f00, 0x00160001, 0x14000160, 0x00014000},  // Upper
		{0x80501402, 0x10040100, 0x28050040, 0x0c0140a0, 0x0300c030, 0x01806018, 0xf07c1f06, 0x581607c1, 0x14058160, 0x05014050},  // eXtra
		{0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000},  // Top
	},

	/* Default LLR table */
	{
		{0x00000002, 0x00000100, 0x00050000, 0x0c000000, 0x00000000, 0x00000018, 0x00001f00, 0x00160000, 0x14000000, 0x00000000},  // Low
		{0x00200005, 0x10000100, 0x000a0000, 0x0c000050, 0x0000c000, 0x01800018, 0xf0001f00, 0x00160001, 0x14000160, 0x00014000},  // Upper
		{0x80501402, 0x10040100, 0x28050040, 0x0c0140a0, 0x0300c030, 0x01806018, 0xf07c1f06, 0x581607c1, 0x14058160, 0x05014050},  // eXtra
		{0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000},  // Top
	},
};

//Micron SB Todo
#if (IM_N48R)
//U32 sb_dsp2_llr_table[RETRY_SB_DSP2_PAGE_NUM][4][4] = { //CY Need to check this line 2021 0108
#else /*(IM_N48R_NEED_CHECK)*/
U32 sb_dsp2_llr_table[3][4][4] = {
#endif  /*(IM_N48R_NEED_CHECK)*/
//11b for 	-1
//01b for 	+1
//00b for		0
//10b not define
/* DSP2 LLR table for Lower */
{
	// 	0 ~ 15  |  15 ~ 31  |  32 ~ 47  |  48 ~ 63
	{0x00010001, 0x00010001, 0x00010001, 0x00010001}, // LLR0		//SB6 = 0, SB7 = 0
	{0x00000000, 0x00000000, 0x00000000, 0x00000000}, // LLR1		//SB6 = 0, SB7 = 1
	{0x00030003, 0x00030003, 0x00030003, 0x00030003}, // LLR2		//SB6 = 1, SB7 = 0
	{0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Top		//SB6 = 1, SB7 = 1
},

/* DSP2 LLR table for Upper */
{
	// 	0 ~ 15  |  15 ~ 31  |  32 ~ 47  |  48 ~ 63
	{0x01030103, 0x01030103, 0x01030103, 0x01030103}, // LLR0		//SB6 = 0, SB7 = 0
	{0x00000000, 0x00000000, 0x00000000, 0x00000000}, // LLR1		//SB6 = 0, SB7 = 1
	{0x03010301, 0x03010301, 0x03010301, 0x03010301}, // LLR2		//SB6 = 1, SB7 = 0
	{0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Top		//SB6 = 1, SB7 = 1
},

/* DSP2 LLR table for eXtra */
{
	// 	0 ~ 15  |  15 ~ 31  |  32 ~ 47  |  48 ~ 63
	{0x13311331, 0x13311331, 0x13311331, 0x13311331}, // LLR0		//SB6 = 0, SB7 = 0
	{0x00000000, 0x00000000, 0x00000000, 0x00000000}, // LLR1		//SB6 = 0, SB7 = 1
	{0x31133113, 0x31133113, 0x31133113, 0x31133113}, // LLR2		//SB6 = 1, SB7 = 0
	{0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Top		//SB6 = 1, SB7 = 1
},
};


U32 sb_dsp_graycode[4][DSP_GRAYCODE_LENGTH] = {	//MICRON B16A
	{
		0x28203038, 0x18100008, 0x08001018, 0x38302028, 0x05030201, 0x0A090706, 0x0F0E0D0B, 0x15131211,  // Low
		0x1A191716, 0x1F1E1D1B, 0x25232221, 0x2A292726, 0x2F2E2D2B, 0x35333231, 0x3A393736, 0x3F3E3D3B
	},

	{
		0x2C24343C, 0x1C14040C, 0x08001018, 0x38302028, 0x22323A38, 0x12020A2A, 0x03011E1A, 0x09070605,  // Upper
		0x0F0E0D0B, 0x16151311, 0x1D1B1917, 0x2523211F, 0x2B292726, 0x312F2E2D, 0x37363533, 0x3F3D3B39
	},

	{
		0x2E26363E, 0x1E16060E, 0x0C04141C, 0x3C34242C, 0x28203038, 0x18100008, 0x0A02121A, 0x3A32222A,  // eXtra
		0x1A191716, 0x1F1E1D1B, 0x25232221, 0x2A292726, 0x2F2E2D2B, 0x35333231, 0x3A393736, 0x3F3E3D3B
	},

	{
		0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,  // Top
		0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000
	},
};

U32 sb_dsp_lut[4][DSP_LUT_LENGTH] = {	//MICRON
	{
		0xF2F20000, 0x0E0000FF, 0x0000010E, 0x00010D0D, 0xFFF1F100, 0xF1F10000, 0x0F0000FF, 0x0000010F,  // Low
		0x00010F0F, 0xFFF1F100, 0xF1F10000, 0x0F0000FF, 0x0000010F, 0x00010F0F, 0xFFF1F100, 0xF1F10000,
		0x0F0000FF, 0x0000010F, 0x00010F0F, 0xFFF1F100
	},

	{
		0xF2F20000, 0x0E0000FF, 0x0000010E, 0x00010E0E, 0xFFF2F200, 0xF2F20000, 0x0E0000FF, 0x0000010E,  // Upper
		0x00010F0F, 0xFFF1F100, 0xF1F10000, 0x0F0000FF, 0x0000010F, 0x00010F0F, 0xFFF1F100, 0xF1F10000,
		0x0F0000FF, 0x0000010F, 0x00010F0F, 0xFFF1F100
	},

	{
		0xF2F20000, 0x0E0000FF, 0x0000010E, 0x00010E0E, 0xFFF2F200, 0xF2F20000, 0x0E0000FF, 0x0000010E,  // eXtra
		0x00010E0E, 0xFFF2F200, 0xF1F10000, 0x0F0000FF, 0x0000010F, 0x00010F0F, 0xFFF1F100, 0xF1F10000,
		0x0F0000FF, 0x0000010F, 0x00010F0F, 0xFFF1F100
	},

	{
		0xF1F10000, 0xFFF1F100, 0x0000010F, 0x00010F0F, 0xFFF1F100, 0xF1F10000, 0x0F0000FF, 0x0000010F,  // Top
		0x00010F0F, 0x00000000, 0xF1F10000, 0x0F0000FF, 0x0000010F, 0x00010F0F, 0xFFF1F100, 0xF1F10000,
		0x0F0000FF, 0x0000010F, 0x00010F0F, 0xFFF1F100
	},
};

U32 sb_dsp_param[4][DSP_PARAM_LENGTH] = {		//MICRON

	{0x08010808, 0x00000000, 0x00000000, 0x20181008, 0x00000028, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Low
	{0x08021008, 0x00000008, 0x00000000, 0x20181008, 0x00000028, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Upper
	{0x08042008, 0x00080808, 0x00000000, 0x20181008, 0x00000028, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // eXtra
	{0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Top
};

U32 gulGroupTable = 0x05A5A500;

U8 gubCnt1Result[3][9] =  {
	//PARM is the same with N_State
	//PARM1	PARM2	PARM3	PARM4		PARM_LENGTH		PARM_CNT	FRAME_CNT	ROUND_CNT	SKIP_BITMAP
	{ 0x03,	0x00,	0,		0,			1,				0,			0,			0,			0x0},      //LOW
	{ 0x01,	0x05,	0,		0,			2,				0,			0,			0,			0x0},        //UPPER
	{ 0x00,	0x02,	0x04,	0x06,		4,				0,			0,			0,			0x0},       //EXTRA
};

U16 guwCoarse_Table[7][5] = {	//Micorn only t0
	// Divisor1    Divisor2   Divisor3   Threshold1   Threshold2
	{ 0, 0, 0, 0, 0},      // t0
	{ 0, 0, 0, 0, 0},    // t1
	{ 0, 0, 0, 0, 0},       // t2
	{ 80, 300, 500, 1500, 3000},      // t3		//Lower page
	{ 0, 0, 0, 0, 0},        // t4
	{ 0, 0, 0, 0, 0},      // t5
	{ 0, 0, 0, 0, 0},       // t6
};

/*for UNIC_B16_FW*/
U16 guwCount1DiffDeltaNThreshold[4] = {
	4513,	//Positive,		RETRY_SB_COUNTONE_DIFF_DELTA_N_THRESHOLD_POSITIVE_IDX_0
	874,		//Positive,		RETRY_SB_COUNTONE_DIFF_DELTA_N_THRESHOLD_POSITIVE_IDX_1
	3373,	//Negative,		RETRY_SB_COUNTONE_DIFF_DELTA_N_THRESHOLD_NEGATIVE_IDX_0
	718		//Negative		RETRY_SB_COUNTONE_DIFF_DELTA_N_THRESHOLD_NEGATIVE_IDX_1
};

U32 gulCoarseTuningCoefficient[5] = {
	2206,	//-0.002206
	4031,	//-0.004031
	23890,	//-0.02389
	6408,	//-0.006408
	2634	//-0.002634
};

U32 gulCoarseTuningConstant[5] = {
	25630000,	//-25.63, Negative
	15730000,	//-15.73, Negative
	2020000,		//2.02
	11320000,	//11.32
	24030000	//24.03
};
/*Above for UNIC_B16_FW*/

U8 Offset_delta_table[7] = {
	//A5h, 	A6h, 	A7h,		A8h, 	A9h,		AAh,		ABh		(for[3][0~7] Read offset)
	0, 0, 0, 0, 0, 0, 0
};

#if(TRUE == RETRY_MICRON_NICKS)
U8 gubSpecificRetryTable[SB_MICRON_SPECIFIC_RETRY_TABLE_MAX_CNT] = {
	0, 4, 7
};
#else	/*(TRUE == RETRY_MICRON_NICKS)*/
U8 gubSpecificRetryTable[SB_MICRON_SPECIFIC_RETRY_TABLE_MAX_CNT] = {
	0, 5, 2, 4
};
#endif	/*(TRUE == RETRY_MICRON_NICKS)*/

U8 gubSpecificReadOffset[SB_MICRON_SPECIFIC_READ_OFFSET_MAX_CNT] = {
	0xD8, 0x21
};

U8 gubARCResultReadOffsetTable[7][7] = {
	//r011,	r001,	r101,	r100,	r000,	r010,	r110
	//B1h,	B3h,		B1h,		B4h,		B2h,		B3h,		B2h
	//A5h, 	A6h, 	A7h,		A8h, 	A9h,		AAh,		ABh		(for[3][0~7] Read offset)
	{0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12},    // -3delta
	{0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C},    // -2delta
	{0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06},    // -delta
	{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},    // 0
	{0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06},    // +delta
	{0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C},    // +2delta
	{0x12, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12},    // +3delta
};

U8 gubMicronReadOffsetFeatureAddressNumber[SB_MICRON_MAX_PAGETYPE_NUM] = {	//PageType Number
	//Lower, 	Upper, eXtra
	1, 		2, 		4
};

U8 gubMicronReadOffsetFeatureAddress[SB_MICRON_MAX_PAGETYPE_NUM][SB_MICRON_MAX_READ_OFFSET_FEATURE_NUM] = {	//PageType Number, Feature Number
	{ 0xA8, 0xFF, 0xFF, 0xFF},  //Lower 		//0xFF for Invalid Feature Address (Reserved in spec)
	{ 0xA6, 0xAA, 0xFF, 0xFF},  //Upper
	{ 0xA5, 0xA7, 0xA9, 0xAB}  //eXtra
};

U8 gubMircronPageTypesReadOffsetIndexOnDeltaTable[SB_MICRON_MAX_PAGETYPE_NUM][SB_MICRON_MAX_READ_OFFSET_FEATURE_NUM] = {	//PageType Number, Feature Number
	{ 3, 0xFF, 0xFF, 0xFF},  //Lower 		//0xFF for Invalid
	{ 1,      5, 0xFF, 0xFF},  //Upper
	{ 0,      2,      4,     6}  //eXtra
};

#endif /* (IM_B47R && (TRUE == RETRY_MICRON_NICKS_ALL_PAGE_TYPE_SB_SUPPORT)) */
#endif /* (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC) */
#else /*(CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_QLC)*/

// Sandisk same as toshiba
#if(IM_N48R)
U32 sb_llr_table[RETRY_MICRON_TABLR_PARAMETER_NUM][DEFAULT_LLR_TABLE_LENGTH] = {
};
#else /*(IM_N48R)*/
WEAK U32 sb_llr_table[4][4][DEFAULT_LLR_TABLE_LENGTH] = {

	/* Default LLR table General Case */
	{
		{0x00400004, 0x10000100, 0x00080000, 0x0D000080, 0x0000D000, 0x01C0001C, 0xF0001F00, 0x00180001, 0x14000180, 0x00011000}, // Low
		{0x00401004, 0x10040100, 0x20080000, 0x0F000080, 0x03C0F03C, 0x01C0701C, 0xF07C1F00, 0x60180001, 0x11000180, 0x04411044}, // Middle
		{0x00400004, 0x10000100, 0x00080000, 0x0D000080, 0x0000D000, 0x01C0001C, 0xF0001F00, 0x00180001, 0x11000180, 0x00014000}, // Upper
		{0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Top
	},

	/* Default LLR table Corner Case 1 */
	{
		{0x00F00004, 0xF0000100, 0x00080000, 0x0D0000F0, 0x0000F000, 0x00F0001C, 0xF0001F00, 0x00180000, 0x140000F0, 0x0001C000}, // Low
		{0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Middle
		{0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Upper
		{0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Top
	},

	/* Default LLR table Corner Case 2 */
	{
		{0x00F00002, 0xF0000100, 0x00060000, 0x0D0000F0, 0x0000F000, 0x00F0001A, 0xF0001F00, 0x00160000, 0x110000F0, 0x0001A000}, // Low
		{0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Middle
		{0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Upper
		{0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Top
	},

	/* Default LLR table Corner Case 3 */
	{
		{0x00D00002, 0xC0000100, 0x00060000, 0x0D0000E0, 0x0000F000, 0x00A0001A, 0xB0001F00, 0x00160000, 0x110001C0, 0x0001A000}, // Low
		{0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Middle
		{0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Upper
		{0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Top
	},
};
#endif /*(IM_N48R)*/
// Sandisk same as toshiba
WEAK U32 sb_dsp2_llr_table[RETRY_SB_DSP2_PAGE_NUM][4][4] = {
	//11b for 	-1
	//01b for 	+1
	//00b for		0
	//10b not define
	/* DSP2 LLR table for Lower */
	{
		// 	0 ~ 15  |  15 ~ 31  |  32 ~ 47  |  48 ~ 63
		{0x01030103, 0x01030103, 0x01030103, 0x00030103}, // LLR0		//SB6 = 0, SB7 = 0
		{0x00000000, 0x00000000, 0x00000000, 0x00000000}, // LLR1		//SB6 = 0, SB7 = 1
		{0x03010301, 0x03010301, 0x03010301, 0x03010301}, // LLR2		//SB6 = 1, SB7 = 0
		{0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Top		//SB6 = 1, SB7 = 1
	},

	/* DSP2 LLR table for Middle */
	{
		// 	0 ~ 15  |  15 ~ 31  |  32 ~ 47  |  48 ~ 63
		{0x01130113, 0x11130113, 0x01130113, 0x01130113}, // LLR0		//SB6 = 0, SB7 = 0
		{0x00000000, 0x00000000, 0x00000000, 0x00000000}, // LLR1		//SB6 = 0, SB7 = 1
		{0x03310331, 0x03310331, 0x03310331, 0x33310331}, // LLR2		//SB6 = 1, SB7 = 0
		{0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Top		//SB6 = 1, SB7 = 1
	},

	/* DSP2 LLR table for Upper */
	{
		// 	0 ~ 15  |  15 ~ 31  |  32 ~ 47  |  48 ~ 63
		{0x01030103, 0x01030103, 0x01030103, 0x01030103}, // LLR0		//SB6 = 0, SB7 = 0
		{0x00000000, 0x00000000, 0x00000000, 0x00000000}, // LLR1		//SB6 = 0, SB7 = 1
		{0x03010301, 0x03010301, 0x03010301, 0x03000301}, // LLR2		//SB6 = 1, SB7 = 0
		{0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Top		//SB6 = 1, SB7 = 1
	},
};

// Sandisk same as toshiba
WEAK U32 sb_dsp_param[4][DSP_PARAM_LENGTH] = {
	{0x08021008, 0x00000008, 0x00000000, 0x00181008, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Low
	{0x09031A08, 0x00000908, 0x01000000, 0x001A1109, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Middle
	{0x08021008, 0x00000008, 0x00000000, 0x00181008, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Upper
	{0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000}, // Top
};

// Sandisk same as toshiba
#if (IM_N48R)
U32 sb_dsp_graycode[RETRY_MICRON_TABLR_PARAMETER_NUM][DSP_GRAYCODE_LENGTH] = {

};
#else /*(IM_N48R)*/
WEAK U32 sb_dsp_graycode[4][DSP_GRAYCODE_LENGTH] = {
	{
		0x2C24343C, 0x1C14040C, 0x08001018, 0x38302028, 0x05030201, 0x0A090706, 0x0F0E0D0B, 0x15131211,  // Low
		0x1A191716, 0x1F1E1D1B, 0x25232221, 0x2A292726, 0x2F2E2D2B, 0x35333231, 0x3A393736, 0x3F3E3D3B
	},

	{
		0x24343C3E, 0x14040C2C, 0x0010181C, 0x30202808, 0x22323A38, 0x12020A2A, 0x03011E1A, 0x09070605,  // Middle
		0x0F0E0D0B, 0x16151311, 0x1D1B1917, 0x2523211F, 0x2B292726, 0x312F2E2D, 0x37363533, 0x3F3D3B39
	},

	{
		0x2C24343C, 0x1C14040C, 0x08001018, 0x38302028, 0x05030201, 0x0A090706, 0x0F0E0D0B, 0x15131211,  // Upper
		0x1A191716, 0x1F1E1D1B, 0x25232221, 0x2A292726, 0x2F2E2D2B, 0x35333231, 0x3A393736, 0x3F3E3D3B
	},

	{
		0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000,  // Top
		0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000, 0x00000000
	},
};
#endif /*(IM_N48R)*/
WEAK U32 sb_dsp_lut[4][DSP_LUT_LENGTH] = { // BiCS4 HDR
	{
		0xF2F20000, 0x0E0000FF, 0x0000010E, 0x00010E0E, 0xFFF2F200, 0xF1F10000, 0x0F0000FF, 0x0000010F,  // Low
		0x00010F0F, 0xFFF1F100, 0xF1F10000, 0x0F0000FF, 0x0000010F, 0x00010F0F, 0xFFF1F100, 0xF1F10000,
		0x0F0000FF, 0x0000010F, 0x00010F0F, 0xFFF1F100
	},

	{
		0xF2F20000, 0x0E0000FF, 0x0000010E, 0x00010E0E, 0xFFF2F200, 0xF2F20000, 0x0E0000FF, 0x0000010E,  // Middle
		0x00010F0F, 0xFFF1F100, 0xF1F10000, 0x0F0000FF, 0x0000010F, 0x00010F0F, 0xFFF1F100, 0xF1F10000,
		0x0F0000FF, 0x0000010F, 0x00010F0F, 0xFFF1F100
	},

	{
		0xF2F20000, 0x0E0000FF, 0x0000010E, 0x00010E0E, 0xFFF2F200, 0xF1F10000, 0x0F0000FF, 0x0000010F,  // Upper
		0x00010F0F, 0xFFF1F100, 0xF1F10000, 0x0F0000FF, 0x0000010F, 0x00010F0F, 0xFFF1F100, 0xF1F10000,
		0x0F0000FF, 0x0000010F, 0x00010F0F, 0xFFF1F100
	},

	{
		0xF1F10000, 0xFFF1F100, 0x0000010F, 0x00010F0F, 0xFFF1F100, 0xF1F10000, 0x0F0000FF, 0x0000010F,  // Top
		0x00010F0F, 0x00000000, 0xF1F10000, 0x0F0000FF, 0x0000010F, 0x00010F0F, 0xFFF1F100, 0xF1F10000,
		0x0F0000FF, 0x0000010F, 0x00010F0F, 0xFFF1F100
	},
};

/*
 * To co-exist with toshiba, minus 1 on sandisk's state parameter.
 * Paramter     P0  P1  P2  P3  P4  P5  P6
 * Sandisk     0x1 0x2 0x3 0x4 0x5 0x6 0x7  ->  0x0 0x1 0x2 0x3 0x4 0x5 0x6
 * Toshiba     0x0 0x1 0x2 0x3 0x4 0x5 0x6
 */
U8 gubRetrySBSandiskCntOneInfo[RETRY_SB_PAGE_NUM][9] = {
	// PARM1, PARM2, PARM3, PARM4, PARM_LENGTH, PARM_CNT, FRAME_CNT, ROUND_CNT, SKIP_BITMAP
	{   0x00,  0x04,  0x00,  0x00,           2,        0,         0,         0,         0x0},  // Low
	{   0x01,  0x03,  0x05,  0x00,           3,        0,         0,         0,         0x0},  // Middle
	{   0x02,  0x06,  0x00,  0x00,           2,        0,         0,         0,         0x0},  // Upper
};
U8 gubRetrySBToshibaCntOneInfo[RETRY_SB_PAGE_NUM][9] =  {
	//PARM1  PARM2   PARM3  PARM4  PARM_LENGTH    PARM_CNT   FRAME_CNT    ROUND_CNT        SKIP_BITMAP
	{ 0x00,    0x04,     0,        0,         2,     0,          0,           0,               0x0},      /// LOW
	{ 0x01,    0x03,     0x05,   0,         3,       0,          0,           0,               0x0},        /// MIDDLE
	{ 0x02,    0x06,     0,        0,         2,     0,          0,           0,               0x0},       ///   UPPER
};

/*
 * Threshold2 0xFFFF can make sure the case will never enter Divisor3.
 * And set unused Divisor3 to 1 to prevent divide zero value.
 */
U16 guwRetrySBSandiskCoarseTuningDivisionTable[RETRY_SB_READ_LEVEL_NUM][RETRY_SB_COARSE_LEVEL_ARRAY_SIZE] = {
	// Divisor1, Divisor2, Divisor3, Threshold1, Threshold2
	{        20,      100,        1,        200,     0xFFFF},  // t0
	{        70,      400,        1,       1000,     0xFFFF},  // t1
	{        80,      250,      450,        500,       4000},  // t2
	{        70,      350,        1,       1000,     0xFFFF},  // t3
	{        80,      200,      550,        500,       3000},  // t4
	{        50,      200,      450,        500,       3000},  // t5
	{        60,      200,      450,        800,       3000},  // t6
};
U16 guwRetrySBToshibaCoarseTuningDivisionTable[RETRY_SB_READ_LEVEL_NUM][RETRY_SB_COARSE_LEVEL_ARRAY_SIZE] = {
	// Divisor1    Divisor2   Divisor3   Threshold1   Threshold2
	{ 100, 200, 300, 1500, 4000},      // t0
	{ 100, 400, 400, 1000, 10000},    // t1
	{   90, 300, 450, 1000, 3000},       // t2
	{ 150, 350, 400, 1500, 6000},      // t3
	{  80, 300, 500, 1500, 3000},        // t4
	{100, 250, 450,  1500, 4000},      // t5
	{  70, 200, 350, 1000, 2000},       // t6
};

U8 Offset_delta_table[RETRY_SB_READ_LEVEL_NUM] = {
	0, 0, 0, 0, 0, 0, 0
};

/// 16KB data with each LDPC protect 2KB data, so should have 8 opt level at most
/// backup shift value in Retry_SB_select_rr_table()
U8 Backup_Optimal_shift_value[8][RETRY_SB_MAX_READ_LEVEL_NUM_PER_PAGE] = {
	{ 0, 0, 0},
	{ 0, 0, 0},
	{ 0, 0, 0},
	{ 0, 0, 0},

	{ 0, 0, 0},
	{ 0, 0, 0},
	{ 0, 0, 0},
	{ 0, 0, 0},
};
U8 delta_table[RETRY_SB_DELTA_NUM][RETRY_SB_READ_LEVEL_NUM] = {
	// P0    P1    P2    P3    P4    P5    P6
	{0xF7, 0xEE, 0xEE, 0xEE, 0xEE, 0xEE, 0xEE},    // -3delta
	{0xFA, 0xF4, 0xF4, 0xF4, 0xF4, 0xF4, 0xF4},    // -2delta
	{0xFD, 0xFA, 0xFA, 0xFA, 0xFA, 0xFA, 0xFA},    // -delta
	{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},    // 0
	{0x03, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06},    // +delta
	{0x06, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C},    // +2delta
	{0x09, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12},    // +3delta
};

U8 default_delta_table[RETRY_SB_DELTA_NUM][RETRY_SB_READ_LEVEL_NUM] = {
	// P0    P1    P2    P3    P4    P5    P6
	{0xF7, 0xEE, 0xEE, 0xEE, 0xEE, 0xEE, 0xEE},    // -3delta
	{0xFA, 0xF4, 0xF4, 0xF4, 0xF4, 0xF4, 0xF4},    // -2delta
	{0xFD, 0xFA, 0xFA, 0xFA, 0xFA, 0xFA, 0xFA},    // -delta
	{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},    // 0
	{0x03, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06},    // +delta
	{0x06, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C},    // +2delta
	{0x09, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12},    // +3delta
};

U8 sb_delta_table[RETRY_SB_DELTA_NUM][RETRY_SB_READ_LEVEL_NUM] = { //toshiba BICS3,4
	// P0    P1    P2    P3    P4    P5    P6
	{0xF7, 0xEE, 0xEE, 0xEE, 0xEE, 0xEE, 0xEE},    // -3delta
	{0xFA, 0xF4, 0xF4, 0xF4, 0xF4, 0xF4, 0xF4},    // -2delta
	{0xFD, 0xFA, 0xFA, 0xFA, 0xFA, 0xFA, 0xFA},    // -delta
	{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},    // 0
	{0x03, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06},    // +delta
	{0x06, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C},    // +2delta
	{0x09, 0x12, 0x12, 0x12, 0x12, 0x12, 0x12},    // +3delta
};
#endif /*(CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_QLC)*/
U32 gulTrappingSetParameterSB[17] = {
	0x054E170A,	// 0
	0x054E270B,	// 1
	0x0552270B,	// 2
	0x0552A70B,	// 3
	0x0572A713,	// 4
	0x0572A913,	// 5
	0x0672A953,	// 6
	0x0672B953,	// 7
	0x0676B954, // 8
	0x06773954,	// 9
	0x0697395C,	// 10
	0x06973B5C,	// 11
	0x07973BDC,	// 12
	0x07974BDC,	// 13
	0x079B4BDD,	// 14
	0x079BCBDD,	// 15
	0x00132032	// 16
};

/*
 * Co-exist of toshiba and sandisk
 *
 * CAUTION :
 * The order should be the same as RetryBiCSProcessEnum_t
 */
U8 gubRetrySBLowerPageMinimumDeltaVthTable[RETRY_BICS_FLASH_PROCESS_NUM] = {
	// Toshiba
	0x80,  // RETRY_TOSHIBA_FLASH_PROCESS_1X
	0x80,  // RETRY_TOSHIBA_FLASH_PROCESS_1Y
	0x80,  // RETRY_TOSHIBA_FLASH_PROCESS_1Z
	0x80,  // RETRY_TOSHIBA_FLASH_PROCESS_BICS2
	0x80,  // RETRY_TOSHIBA_FLASH_PROCESS_BICS3_128G
	0xF4,  // RETRY_TOSHIBA_FLASH_PROCESS_BICS3_256G, -12
	0xEC,  // RETRY_TOSHIBA_FLASH_PROCESS_BICS3_512G, -20
	0xE0,  // RETRY_TOSHIBA_FLASH_PROCESS_EARLY_BICS4_256G, -32
	0xE0,  // RETRY_TOSHIBA_FLASH_PROCESS_FINAL_BICS4_256G, -32
	0xE0,  // RETRY_TOSHIBA_FLASH_PROCESS_NON_EARLY_FINAL_BICS4_256G, -32
	0xE0,  // RETRY_TOSHIBA_FLASH_PROCESS_BICS4_HDR_256G, -32
	0xD8,  // RETRY_TOSHIBA_FLASH_PROCESS_BICS4_512G, -40
	0x80,  // RETRY_TOSHIBA_FLASH_PROCESS_BICS4_1024G
	0x80,  // RETRY_TOSHIBA_FLASH_PROCESS_BICS4_1360G
	0xEC,  //RETRY_TOSHIBA_FLASH_PROCESS_BICS4_pTLC_512G
	0xE8,  // RETRY_KIOXIA_FLASH_PROCESS_BICS5_TLC_512G
	// Sandisk
	0xF4,  // RETRY_SANDISK_FLASH_PROCESS_BICS3_256G
	0xF4,  // RETRY_SANDISK_FLASH_PROCESS_BICS3_512G
	0xE8,  // RETRY_SANDISK_FLASH_PROCESS_BICS4_256G, -24
	0xE8   // RETRY_SANDISK_FLASH_PROCESS_BICS4_512G, -24
};

#else /*(RETRY_SOFTBITT_FOR_SDK_EN==FALSE)*/
U32 sb_dsp_lut[4][DSP_LUT_LENGTH] = {
};
U8 sb_delta_table[RETRY_SB_DELTA_NUM][RETRY_SB_READ_LEVEL_NUM] = {
};
#endif /*(RETRY_SOFTBITT_FOR_SDK_EN==FALSE)*/
#endif /*(TRUE == RETRY_MICRON_NICKS_ALL_PAGE_TYPE_SB_SUPPORT)*/

