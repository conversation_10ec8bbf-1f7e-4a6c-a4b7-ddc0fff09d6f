/****************************************************************************
*
*  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
*  All rights reserved
*
*  The content of this document is confidential and shall be applied
*  subject to the terms and conditions of the license agreement and
*  other applicable laws. Any unauthorized access, use or disclosure
*  of this document is strictly prohibited and may be punishable
*  under laws.
*
*  hostevt_errhdl.h
*
*
*
****************************************************************************/

#ifndef HOSTEVT_ERRHDL_H_
#define HOSTEVT_ERRHDL_H_

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "aom/aom_api.h"

/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */
#define HOST_EVT_CHECK_LINK_NUMBER	(10)
#define HOST_EVT_LINK_DOWN_THRESHOLD	(2)
#define HOST_EVENT_PHY_ACTIVITY_THRESHOLD	(15000)
#define W_ERR_DEBUG   (FALSE)
#define HOST_EVT_CHECK_MASK (0xFFFF)
#define WCACHE_NUM_DEFAULT	(0x7)
#define INVALID_HOSTCID_VALUE (0xFFFF)
#define CKECK_SAVE_BOUND 		(4 * 1024 * 1024 * 2)// 4G, unit is 512 byte

/*
 * ---------------------------------------------------------------------------------------------------
 *   macros
 * ---------------------------------------------------------------------------------------------------
 */
#define M_HOST_EVENT_SET_WB_ERROR_BITMAP(N)         (gFTLHostEventWriteFail.WB.ubWBErrorBitmap |= (1 << N)  )
#define M_HOST_EVENT_CHECK_WB_ERROR_BITMAP(N)         (gFTLHostEventWriteFail.WB.ubWBErrorBitmap & (1 << N)   )
#define M_HOST_EVENT_CLEAR_WB_ERROR_BITMAP(N)         (gFTLHostEventWriteFail.WB.ubWBErrorBitmap  &= ~(1 << N))

/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */
AOM_HOST_ERROR static U8 FTLFreeWCachePB( U8 ubWCacheIdx);
AOM_HOST_ERROR static void ftl_W_Err_Hdl(void);

#endif /* HOSTEVT_ERRHDL_H_ */
