#ifndef _COP1_H_
#define _COP1_H_

#define ST3C_BYPASS_COP0_EN			(FALSE)

/*  ------------------------------------------------
*  Definition
*  ------------------------------------------------
*/
#define ST1_REG_IN_4K_SIZE	1

#define COP1_NAMESPACE_NUM          (1)
#define COP1_NAMESPACE0_VALUE		(0)
#define COP1_NAMESPACE1_VALUE		(0x1FF)
#define COP1_NAMESPACE2_VALUE		(0x1FF)
#define COP1_NAMESPACE3_VALUE		(0x1FF)
#define COP1_NAMESPACE4_VALUE		(0x1FF)
#define COP1_NAMESPACE5_VALUE		(0x1FF)
#define COP1_NAMESPACE6_VALUE		(0x1FF)
#define COP1_NAMESPACE7_VALUE		(0x1FF)

#define E11_REG_CACHE_BMU	(0)
#define E11_REG_CACHE_HMB	(1)
#define E11_CACHE_OPTION	(E11_REG_CACHE_BMU)

#if (E11_CACHE_OPTION == E11_REG_CACHE_HMB)
#define INVALID_CID_VALUE           (0x07FFFFFF)
#else /* (E11_CACHE_OPTION == E11_REG_CACHE_HMB) */
#define INVALID_CID_VALUE           (0x000001FF)
#endif /* (E11_CACHE_OPTION == E11_REG_CACHE_HMB) */
#if PS5017_EN
#define INVALID_IDX_VALUE			(0x001FFFFF)
#else /* PS5017_EN */
#define INVALID_IDX_VALUE			(0x000FFFFF)
#endif /* PS5017_EN */
#define ST3C_CMD_TBL_0_SIZE                 (64)
#define ST3C_CMD_TBL_1_SIZE                 (64)


#define ST3C_COP0_CMD_D1_SLC_SET_METHOD_BIT (BIT(0))
#define ST3C_COP0_CMD_FLUSH_BIT             (BIT(1))
#define ST3C_COP0_CMD_SLC_BIT               (BIT(2))
#define ST3C_COP0_CMD_D1_BIT                (BIT(3))

#define COP1_TB_TO_MB_SHIFT			(20)
#define COP1_MISS_CNT_THRESHOLD_RATIO		(3)

#define COP1_ST1_INSERT_LCA1_BLK_ID_SHIFT		(60)
#define COP1_ST1_INSERT_LCA1_BLK_OP_SHIFT		(56)
#define COP1_ST1_INSERT_LCA1_BMU_FREE_EN_SHIFT	(50)
#define COP1_ST1_INSERT_LCA1_FREE_OP_SHIFT		(48)
#define COP1_ST1_INSERT_LCA1_LB_OFFSET_SHIFT 	(32)

#define COP1_ST1_INSERT_LCA2_BLK_ID_SHIFT				  (60)
#define COP1_ST1_INSERT_LCA2_BLK_OP_SHIFT				  (56)
#define COP1_ST1_INSERT_LCA2_XZIP_IDX_SHIFT				  (48)
#define COP1_ST1_INSERT_LCA2_NEED_UPDATE_XZIP_PCA_EN_SHIFT (42)
#define COP1_ST1_INSERT_LCA2_FINISH_SHIFT				  (43)

#endif /* _COP1_H_ */
