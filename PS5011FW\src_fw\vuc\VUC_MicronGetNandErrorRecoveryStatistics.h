#ifndef _VUC_MicronGetNandErrorRecoveryStatistics_H_
#define _VUC_MicronGetNandErrorRecoveryStatistics_H_
#include "aom/aom_api.h"

typedef struct {
	U8 ubResponseHeaderFormatVersion;
	U8 ubResponseDataFotmatVersion;
	U16 uwCMDClass;
	U16 uwCMDCode;
	U16 uwStatus;
	U32 ulDataPayloadSize;
} GetNandErrorRecoveryStatisticsResponseHeader_t;

#define DECIMAL 	(10)
#define HEXADECIMAL	(16)

#define VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_HEADER_VERSION			(0x00)
#define VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_HEADER_VERSION_LENGTH	(1)
#define VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_DATA_TYPE				(0x02)
#define VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_DATA_TYPE_LENGTH		(1)
#define VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_COMMAND_CLASS			(0x0004)
#define VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_COMMAND_CLASS_LENGTH	(2)
#define VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_COMMAND_CODE			(0x000F)
#define VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_COMMAND_CODE_LENGTH	(2)
#define VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_STATUS_LENGTH			(2)
#define VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_SIZE_OF_DATA_LENGTH	(4)
#define VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_DATA_PAYLOAD_OFFSET	(12)
#if (IM_N48R)
#define VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_DATA_PAYLOAD_LENGTH	(4224)
#define VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_MAX_DIE	            (32)
#else /* (IM_N48R) */
#define VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_DATA_PAYLOAD_LENGTH	(2048)
#endif/* (IM_N48R) */
#define VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_MAX_STEP	            (32)

#define VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_ENTRY_LENGTH			(2)
#if (HOST_MODE == NVME)
AOM_VUC_3 void VUCMicronGetNANDErrorRecoveryStatistics(U32 ulPayloadAddr);
AOM_VUC_3 U8 VUCMyStrlen(const U8 *pubString);
AOM_VUC_3 void VUCMyReverse(U8 *pubString);
AOM_VUC_3 void VUCMyitoa(U32 uln, U8 *pubString, U8 ubBase);
AOM_VUC_3 U8 *VUCMyStrcat(U8 *pubString1, const char *pubString2);
AOM_VUC_3 void VUCMyClearLastByte(U8 *pubString);
#else /* (HOST_MODE == NVME) */
AOM_VUC_3 void VUCMicronGetNANDErrorRecoveryStatistics(U32 ulPayloadAddr);
AOM_VUC_3 U8 VUCMyStrlen(const U8 *pubString);
AOM_VUC_3 void VUCMyReverse(U8 *pubString);
AOM_VUC_3 void VUCMyitoa(U32 uln, U8 *pubString, U8 ubBase);
AOM_VUC_3 U8 *VUCMyStrcat(U8 *pubString1, const char *pubString2);
AOM_VUC_3 void VUCMyClearLastByte(U8 *pubString);
#endif /* (HOST_MODE == NVME) */


#endif /* _VUC_MicronGetNandErrorRecoveryStatistics_H_ */
