#ifndef _CODE_SIGN_HEADER_H_
#define _CODE_SIGN_HEADER_H_
#include "common/mem.h"


//Code Sign Header
#if BOOTLOADER_MODE_EN
#define CODESIGN_BASE								(BOOT_CODESIGN_BASE) //Temp
#else /* BOOTLOADER_MODE_EN */
#define CODESIGN_BASE								(BURNER_CODESIGN_BASE) //Temp
#endif /* BOOTLOADER_MODE_EN */
#define CODESIGNB                                   ((REG8 *) CODESIGN_BASE)
#define CODESIGNW                                   ((REG16 *) CODESIGN_BASE)
#define CODESIGNL                                   ((REG32 *) CODESIGN_BASE)

#define CODESIGNHEAD_SIZE							(512)
#define CODESIGNHEAD_SIZE_SECTOR    				(CODESIGNHEAD_SIZE >> SECTOR_SIZE_SHIFT)
#if (PS5017_EN)
#define SIGNDIGEST_SIZE								(2560)		//512 + 4 + 512 + 512 + 512 + 8 + (padding 500)
#else /* (PS5017_EN) */
#define SIGNDIGEST_SIZE								(1536)		//256 + 4 + 256 + 256 + 256 + 4 + (padding 504)
#endif /* (PS5017_EN) */
#define SIGNDIGEST_SIZE_SECTOR						(SIGNDIGEST_SIZE >> SECTOR_SIZE_SHIFT)

#define DOUBLESIGN_MAX_VALUE						(0x01)
#define CODESIGN_TYPE_IS_DOUBLE_SIGN                (1)
//==============================================================================
//  Secuity Inforamtion - Must Keep this format
//==============================================================================

#define CSB_CODEBLK_MARK							(0x00 >> 0) //  'C', 'O', 'D', 'E', ' ', 'B', 'L', 'O', 'C', 'K', 0x55, 0xAA, 0xAA, 0x55, 0x00, 0xFF
#define CODEBLK_MARK_BYTE_CNT                       (16)

#define CSB_RSA_ID									(0x10 >> 0)
#define CSL_RSA_ID									(0x10 >> 2)//RSA Key select index
#define PHISON_1_ID									(0x31303050)                  // 1 0 0  'P'
#define PHISON_2_ID									(0x32303050)				  // 2 0 0  'P'
#define PHISON_3_ID									(0x33303050)				  // 3 0 0  'P'
#define PHISON_4_ID 								(0x34303050)				  // 4 0 0  'P'
#define PHISON_5_ID 								(0x35303050)				  // 5 0 0  'P'
#define PHISON_7_ID									(0x37303050)				  // 7 0 0  'P'
#define PHISON_8_ID									(0x38303050)				  // 8 0 0  'P'
#define	CODESIGNHEADER_RSA_KEY_SIZE					(4)

#define CSB_CMP_PUBKEY_TYPE							(0x14 >> 0)
#define RAW_KEY_IN_ROM								(0x00)
#define HASH_IN_ROM									(0x01)
#define HASH_IN_EFUSE								(0x02)

#define CSB_CHKSUM_ENABLE							(0x15 >> 0)

#define CSB_SIGNATURE_FORMAT						(0x16 >> 0)
#define SIGNATURE_ONLY								(0x01)
#define KEY_AFTER_SIGNATURE 						(0x02)
#define SIGNATURE_AFTER_KEY							(0x03)


#define CSB_RSAALGO_TYPE							(0x17 >> 0)
#define RSA_ALGORITHM_TYPE_PKCS1_V1_5           	(0x01)
#define RSA_ALGORITHM_TYPE_TEXTBOOK             	(0x02)
#define RSA_ALGORITHM_TYPE_PKCS1_V2_1           	(0x03)

#define ECDSA_ALGORITHM_TYPE_SHA256_P256            (0x04)

#define RSA_ALGORITHM_TYPE_SHA512_PKCS1_V1_5        (0x05)
#define RSA_ALGORITHM_TYPE_SHA512_TEXTBOOK          (0x06)
#define RSA_ALGORITHM_TYPE_SHA512_PKCS1_V2_1        (0x07)

#define RSA_ALGORITHM_TYPE_SHA256_4096_PKCS1_V1_5   (0x08)
#define RSA_ALGORITHM_TYPE_SHA256_4096_TEXTBOOK     (0x09)
#define RSA_ALGORITHM_TYPE_SHA256_4096_PKCS1_V2_1   (0x0A)

#define RSA_ALGORITHM_TYPE_SHA512_4096_PKCS1_V1_5   (0x0B)
#define RSA_ALGORITHM_TYPE_SHA512_4096_TEXTBOOK     (0x0C)
#define RSA_ALGORITHM_TYPE_SHA512_4096_PKCS1_V2_1   (0x0D)

#define SM3_ALGORITHM_TYPE_WITH_SM2                 (0x0E)
#define ECDSA_ALGORITHM_TYPE_SHA256_P384            (0x11)

#define CSB_IC_VERSION								(0x18 >> 0) //  'P', 'S', '5', '0', '1', '1', 0x00, 0x00
#define IC_VERSION_BYTE_CNT                         (8)

#define CSB_DOUBLESIGN								(0x20 >> 0)

#define	CODESIGNHEADER_CODE_SIGN_FINISH				(0x2B)
#define	CODESIGN_FINISHED							(0x33)
#define	CODESIGNHEADER_CODE_SIGN_FINISHED_SIZE		(1)

#define CSL_RANDOMKEY_INDEX							(0x60 >> 2)

#define CSB_LRC										(0x6F >> 0)

#define CSB_TIMESTAMP								(0x70 >> 0) //Y-M-D-CMS

#define SECURITY_INFO_BYTE_CNT                      (32)

//==============================================================================
//  Boot/Burner usage
//==============================================================================

#define CSW_ATCM_SECTORCNT							(0x80 >> 1)
#define CSW_CODEBNAK_SECTORCNT						(0x82 >> 1)
#define CSW_BTCM_SECTORCNT							(0x84 >> 1)
#define CSW_ANDES_SECTORCNT							(0x86 >> 1)
#define CSW_FIPMISC_SECTORCNT						(0x88 >> 1)
#define CSW_ALL_SECTORCNT							(0x8A >> 1)  //(ATCM + CODEBANK + BTCM + ANDES + FIP)
#define CSL_BIN_CRC32								(0x8C >> 2)  //(Inner Code sign header (0x80-0x1FF) + ATCM + CODEBANK + BTCM + ANDES + FIP)
#define CSW_CODEBANK_IN_ATCM_SECTORCNT				(0x90 >> 1)
#define CODESIGN_CUSTOMER_FW_NAME_OFFSET            (0xA0)

#define ATCM_SECTORCNT_OFFSET			(CSW_ATCM_SECTORCNT << 1)
#define CODEBNAK_SECTORCNT_OFFSET		(CSW_CODEBNAK_SECTORCNT << 1)
#define BTCM_SECTORCNT_OFFSET			(CSW_BTCM_SECTORCNT << 1)
#define ANDES_SECTORCNT_OFFSET			(CSW_ANDES_SECTORCNT << 1)
#define FIP_SECTORCNT_OFFSET			(CSW_FIPMISC_SECTORCNT << 1)


typedef struct Code_Section_Info       CODESECTION_INFO, *CODESECTION_INFO_PTR;
struct Code_Section_Info {
	U32 AndesBase;	//Buffer Base of Andes
	U32 FIPBase;	//Buffer Base of FIP

	U32 AndesSize;	//Size of Andes
	U32 FIPSize;	//Size of FIP
};

typedef struct CodeSignInfo {
	U32 ulCodeSignKeyIdx;
	U8 ubCodeSignFinish;
	U8 aubReserved[3];
} CodeSignInfo_t;

#if BOOTLOADER_EN
typedef enum CodeSignHeaderMode {
	CODESIGNHEADER_MODE_BOOTLOADER = 0,
	CODESIGNHEADER_MODE_FW
} CodeSignHeaderMode_t;

#if BURNER_MODE_EN
typedef struct {
	//0x00
	U8 aubCodeBlockMark[CODEBLK_MARK_BYTE_CNT];  //C,O,D,E, ,B,L,O,C,K,0x55,0xAA,0xAA,0x55,0x00,0xFF

	//0x10
	U32 ulRSA_ID;
	U8 ubCompareKeyType;
	U8 ubEnableChecksum;
	U8 ubSignatureFileFormat;
	U8 ubRSAAlgorithm;
	U8 ubICVersion[IC_VERSION_BYTE_CNT];        //P,S,5,0,1,3,0x00,0x00

	//0x20
	U8 ubDoubleSign;
	U8 ubRsv0[10];
	U8 ubCodeSignFinish;
	U32 ulChecksum;

	//0x30
	U32 ulRsv[12];

	//0x60
	U8 ubSecurityInfo[SECURITY_INFO_BYTE_CNT];

	//0x80
	U16 uwATCMSectorCnt;
	U16 uwCodeBankSectorCnt;
	U16 uwBTCMSectorCnt;
	U16 uwAndesSectorCnt;
	U16 uwFIPSectorCnt;
	U16 uwAllSectorCnt;
	U32 ulBinCRC32;

	//0x90
	U16 uwCodeBankInATCMSectorCnt;
	U8 ubRsv1[14];
} CodeSignHeader_t; //0xA0 Bytes
TYPE_SIZE_CHECK(CodeSignHeader_t, 0xA0);
#endif /* BURNER_MODE_EN */
#endif /* BOOTLOADER_EN */

extern CodeSignInfo_t guoCodeSignInfo;

#endif //end _CODE_SIGN_HEADER_H_
