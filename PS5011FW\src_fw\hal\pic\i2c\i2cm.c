#include "i2cm.h"
#include "i2cm_api.h"
#include "i2cm_device_api.h"
#include "hal/sys/api/rtt/rtt_api.h"
#include "hal/sys/api/mux/mux_api.h"
#include "hal/pic/uart/uart_api.h"

U8 I2CScanSlaveTriggerProcess(I2C_CTRL_PARAM_PTR i2c_param)
{
	U8 ubResult = PASS;
	U8 ubGroup_offset;
	U64 uoStartTriggerTime;

	ubGroup_offset = M_I2CM_GROUP_OFFSET(i2c_param->ubGroup);

	uoStartTriggerTime = M_RTT_GET_CNT_MILLISECOND(RTT0);
	R32_I2CM[R32_I2CM_GP1_CTRL + (ubGroup_offset >> 2)] |= GP_I2C_START_BIT;
	while (R32_I2CM[R32_I2CM_GP1_CTRL + (ubGroup_offset >> 2)] & GP_I2C_START_BIT) {
		if ( (M_RTT_GET_CNT_MILLISECOND(RTT0) - uoStartTriggerTime) > I2C_TRIGGER_TIMEOUT_MILLISECOND) {
			ubResult = FAIL;
			break;
		}
	}

	IdlePC(I2C_SCAN_SLAVE_DEVICE_DELAY_LOOP_COUNT);

	if ((R32_I2CM[R32_I2CM_GP1_NACK + (ubGroup_offset >> 2)] & GP_ANACK_BIT) | (R32_I2CM[R32_I2CM_GP1_NACK + (ubGroup_offset >> 2)] & GP_DNACK_BIT)) {
		ubResult = FAIL;
	}

	return ubResult;
}

U8 I2CTriggerProcess(I2C_CTRL_PARAM_PTR i2c_param)
{
	U8 ubResult = PASS;
	U8 ubGroup_offset;
	U64 uoStartTriggerTime;

	ubGroup_offset = M_I2CM_GROUP_OFFSET(i2c_param->ubGroup);

	uoStartTriggerTime = M_RTT_GET_CNT_MILLISECOND(RTT0);
	R32_I2CM[R32_I2CM_GP1_CTRL + (ubGroup_offset >> 2)] |= GP_I2C_START_BIT;
	while (R32_I2CM[R32_I2CM_GP1_CTRL + (ubGroup_offset >> 2)] & GP_I2C_START_BIT) {
		if ( (M_RTT_GET_CNT_MILLISECOND(RTT0) - uoStartTriggerTime) > I2C_TRIGGER_TIMEOUT_MILLISECOND) {
			ubResult = FAIL;
			break;
		}
	}

	return ubResult;
}
U8 I2CIsAddrNACK(I2C_CTRL_PARAM_PTR i2c_param)
{
	U8 ubGroup_offset;

	ubGroup_offset = M_I2CM_GROUP_OFFSET(i2c_param->ubGroup);
	return R32_I2CM[R32_I2CM_GP1_NACK + (ubGroup_offset >> 2)] & GP_ANACK_BIT;
}
U8 I2CIsDataNACK(I2C_CTRL_PARAM_PTR i2c_param)
{
	U8 ubGroup_offset;

	ubGroup_offset = M_I2CM_GROUP_OFFSET(i2c_param->ubGroup);
	return R32_I2CM[R32_I2CM_GP1_NACK + (ubGroup_offset >> 2)] & GP_DNACK_BIT;
}
U8 I2CConfig(I2C_CTRL_PARAM_PTR i2c_param, U8 ubGroup, U16 uwSlave_addr, U8 ubMode, U8 ubData_cnt, U8 ubOp_cnt)
{
	U8 ubresult = PASS;

	i2c_param->ubGroup        = ubGroup;
	i2c_param->uwSlave_addr   = uwSlave_addr;
	i2c_param->ubData_cnt     = ubData_cnt;
	i2c_param->ubOp_cnt       = ubOp_cnt;
	i2c_param->ubMode         = ubMode;

	return ubresult;
}

U8 I2COperation(I2C_CTRL_PARAM_PTR i2c_param)
{
	U8 ubResult = PASS;
	U8 ubOp_idx;

	M_I2CM_GROUP_SLAVE_ADDR(i2c_param->ubGroup, i2c_param->uwSlave_addr);
	for (ubOp_idx = 0; ubOp_idx < i2c_param->ubOp_cnt; ubOp_idx++) {
		M_I2CM_GROUP_OPERATION_REG(i2c_param->ubGroup, ubOp_idx, i2c_param->ubOp[ubOp_idx]);
	}
	M_I2CM_GROUP_CTRL(i2c_param->ubGroup, i2c_param->ubMode, i2c_param->ubData_cnt, i2c_param->ubOp_cnt);
	return ubResult;
}

U8 I2CDetectPMIC()
{
	I2C_CTRL_PARAM I2CParameter;
	U8 ubGroup = I2C_GROUP_4;
	U8 ubMode, ubOperationCnt, ubDataCnt, ubAddrNACK, ubDataNACK;
	U16 uwSlaveAddr;
	U32 ulTempMux, ulReturnValue;
	U64 uoStartTriggerTime;
	U8 ubGroupOffset;

	ubGroupOffset = M_I2CM_GROUP_OFFSET(I2C_GROUP_1);
	uoStartTriggerTime = M_RTT_GET_CNT_MILLISECOND(RTT0);

	//Wait former I2C command from TT complete
	while (M_I2CM_GET_START_BIT(ubGroupOffset)) {
		if ( (M_RTT_GET_CNT_MILLISECOND(RTT0) - uoStartTriggerTime) > I2C_TRIGGER_TIMEOUT_MILLISECOND) {
			M_UART(DEBUG_, "I2C OT\n");
			return I2C_DETECT_FAIL;
		}
	}
	M_MUX_BACK_UP_SETTING(ulTempMux);
	mux_i2cm_scl_and_sda();

	ubDataCnt = 1;
	ubOperationCnt = 1;
#if (PS5021_EN)
	uwSlaveAddr = I2C_PS6121_SLAVE_ADDR;
#else /* (PS5021_EN) */
	uwSlaveAddr = I2C_PS6103_SLAVE_ADDR;
#endif /* (PS5021_EN) */
	ubMode = ( GP_DIR_BIT | GP_BUF_SEL_BIT);
	I2CConfig(&I2CParameter, ubGroup, uwSlaveAddr, ubMode, ubDataCnt, ubOperationCnt);
#if (PS5021_EN)
	I2CParameter.ubOp[0] = I2C_PS6121_CH3_SELECT_REGISTER_GPIO;
#else /* (PS5021_EN) */
	I2CParameter.ubOp[0] = I2C_PS6103_CH1_SELECT_REGISTER;
#endif /* (PS5021_EN) */
	I2COperation(&I2CParameter);
	I2CTriggerProcess(&I2CParameter);
	ubAddrNACK = I2CIsAddrNACK(&I2CParameter);
	ubDataNACK = I2CIsDataNACK(&I2CParameter);
	if ( ubAddrNACK || ubDataNACK) {    //
		M_UART(VUC_, "\nThere is no PMIC\n");
		M_MUX_RESTROE_SETTING(ulTempMux);
		return I2C_DETECT_FAIL;
	}
	else {
		ulReturnValue = M_I2CM_GET_BUF1();
		M_MUX_RESTROE_SETTING(ulTempMux);
#if (PS5021_EN)
		if (I2C_PS6121_CH3_GPIO_VID_SETTING_1200V == ulReturnValue) {
			return I2C_PMIC_VOLTAGE_1P2V;
		}
		else {
			return I2C_PMIC_VOLTAGE_ABNORMAL;
		}
#else /* (PS5021_EN) */
		if (I2C_PS6103_1P2V_VALUE == ulReturnValue) {
			return I2C_PMIC_VOLTAGE_1P2V;
		}
		else if (I2C_PS6103_1P8V_VALUE == ulReturnValue) {
			return  I2C_PMIC_VOLTAGE_1P8V;
		}
		else {
			return I2C_PMIC_VOLTAGE_ABNORMAL;
		}
#endif /* (PS5021_EN) */
	}
}

U8 I2CDetectThermalSensor()
{
	I2C_CTRL_PARAM I2CParameter;
	U8 ubGroup = I2C_GROUP_4, ubi;
	U8 ubMode, ubOperationCnt, ubDataCnt, ubAddrNACK, ubDataNACK;
	U16 uwSlaveAddr;
	U32 ulTempMux;
	U8 ubI2CThermalSlaveAddr[] = {I2C_THERMAL_TMP102, I2C_THERMAL_TMP103B, I2C_THERMAL_G754A};
	U64 uoStartTriggerTime;
	U8 ubGroupOffset;

	ubGroupOffset = M_I2CM_GROUP_OFFSET(I2C_GROUP_1);
	uoStartTriggerTime = M_RTT_GET_CNT_MILLISECOND(RTT0);

	//Wait former I2C command from TT complete
	while (M_I2CM_GET_START_BIT(ubGroupOffset)) {
		if ( (M_RTT_GET_CNT_MILLISECOND(RTT0) - uoStartTriggerTime) > I2C_TRIGGER_TIMEOUT_MILLISECOND) {
			M_UART(DEBUG_, "I2C OT\n");
			return I2C_DETECT_FAIL;
		}
	}
	M_MUX_BACK_UP_SETTING(ulTempMux);
	mux_i2cm_scl_and_sda();

	for (ubi = 0; ubi < (sizeof(ubI2CThermalSlaveAddr) / sizeof(ubI2CThermalSlaveAddr[0])); ubi++) {
		uwSlaveAddr = ubI2CThermalSlaveAddr[ubi];
		ubMode = (GP_DIR_BIT);
		ubDataCnt = 2;
		ubOperationCnt = 1;
		I2CConfig(&I2CParameter, ubGroup, uwSlaveAddr, ubMode, ubDataCnt, ubOperationCnt);
		I2CParameter.ubOp[0] = 0x00;
		I2COperation(&I2CParameter);
		I2CTriggerProcess(&I2CParameter);
		ubAddrNACK = I2CIsAddrNACK(&I2CParameter);
		ubDataNACK = I2CIsDataNACK(&I2CParameter);
		if ( ubAddrNACK || ubDataNACK) {    //
			M_UART(VUC_, "There is no Thermal Sensor\n");
		}
		else {
			M_MUX_RESTROE_SETTING(ulTempMux);
			return uwSlaveAddr;
		}
	}
	M_MUX_RESTROE_SETTING(ulTempMux);
	return I2C_DETECT_FAIL;
}



