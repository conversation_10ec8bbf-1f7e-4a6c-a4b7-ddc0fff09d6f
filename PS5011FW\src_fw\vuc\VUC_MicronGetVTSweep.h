#ifndef _VUC_MICRONGETVTSWEEP_H_
#define _VUC_MICRONGETVTSWEEP_H_
#include "aom/aom_api.h"

#define VUC_MICRON_VT_SWEEP_VERIFY_EN	(FALSE)

#if (IM_N28)
#define VUC_MICRON_REGISTER_NEED_TO_CLEAR_PRIOR_VT_SWEEP_NUM	(32)
#elif (IM_B47R || IM_B37R) /*(IM_N28)*/
#define VUC_MICRON_REGISTER_NEED_TO_CLEAR_PRIOR_VT_SWEEP_NUM	(16)
#elif (IM_N48R) /*(IM_N28)*/
#define VUC_MICRON_REGISTER_NEED_TO_CLEAR_PRIOR_VT_SWEEP_NUM	(32)
#endif /*(IM_N28)*/
#define VUC_MICRON_VT_SWEEP_HEADER_LENGTH			(12)
#define VUC_MICRON_VT_SWEEP_RESPONSE_HEADER_LENGTH	(12)
#define VUC_MICRON_VT_SWEEP_RESPONSE_DATA_PAYLOAD_HEADER_LENGTH	(14)

#define VUC_MICRON_ALL_IBF_DATA_SIZE		(5120)
#define VUC_MICRON_N28_VT_SWEEP_MAX_VALUE	(0xFF)
#define VUC_MICRON_VT_READ_SLC_MODE			(0x01)
#define VUC_MICRON_VT_READ_NON_SLC_MODE			(0xFF)

#define VUC_MICRON_RAW_READ_USE_IBUF_NUM	(4)

#define VUC_MICRON_VT_SWEEP_COMMAND_CLASS	(0x04)
#define VUC_MICRON_VT_SWEEP_COMMAND_CODE	(0x2A)

#define VUC_MICRON_VT_SWEEP_READ_BUFFER_SIZE 	DEF_KB(20)
#define VUC_MICRON_VT_SWEEP_READ_BUFFER_A		(BURNER_HOST_BUFFER_BASE)											// 0x22164000
#define VUC_MICRON_VT_SWEEP_READ_BUFFER_B		(BURNER_HOST_BUFFER_BASE) + VUC_MICRON_VT_SWEEP_READ_BUFFER_SIZE	// 0x22169000

typedef struct {
	U16 uwChannel;
	U16 uwCE;
	U16 uwLUN;
	U16 uwPhysicalBlock;
	U16 uwPage;
	U16 uwVTStopValue;
	U16 uwVTStepSize;
	U16 uwReadMode;
} VTSweepInputData_t;

typedef struct {
	U8 ubResponseHeaderFormatVersion;
	U8 ubResponseDataFotmatVersion;
	U16 uwCMDClass;
	U16 uwCMDCode;
	U16 uwStatus;
	U32 ulDataPayloadSize;
} VTSweepResponseHEADER_t;

typedef struct {
	U16 uwChannel;
	U16 uwCE;
	U16 uwLUN;
	U16 uwPhysicalBlock;
	U16 uwPage;
	U16 uwVTStopValue;
	U16 uwVTStepSize;
} VTSweepResponseDataPayloadHEADER_t;


AOM_VUC_3 void VUCMicronGetVTSweep(U32 ulPayloadAddr);
AOM_VUC_3 void VUCMicronReadPageRawData(U32 ulReadAddr, U32 ulIFSA, U8 ubALURule);
AOM_VUC_3 U32 VUCMicronReadCountOneNum(U32 ulReadAddr);
AOM_VUC_3 void VUCMicronBackupCorrect2k(U8 ubChannel, U8 ubSrcInternalBuf, U32 ulTempAddr, U32 ulTempIRAMAddr);
AOM_VUC_3 void VUCMicronRawReadDMATrigger(U16 uwFPUOffset, U32 ulFSA, U8 ubMTIndex, U8 ubECCFrameIdx, U32 ulDummyBuf, U8 ubALURule, U32 ulSpareAddr);
AOM_VUC_3 void VUCMicronBackupRestore(U8 ubChannel, U64 uoDRAMAdddr, U32 ulIRAMAddr, U8 ubInternalBufPtr, U8 ubDirection, U8 ubMode, U8 ubBCHMode, U8 ubAll, U8 ubFrameMask);
AOM_VUC_3 void VUCMicronFPUTrigger(U8 ubChannel, U16 uwFPUOffset);

#if (HOST_MODE == NVME)
AOM_VUC_3 void VUCMicronFlaSetMLBi(U8 ubChannel, U8 ubCE, U8 ubLUN, U16 uwAddr, U8 pubFeature);
AOM_VUC_3 void VUCMicronFlaGetMLBi(U8 ubChannel, U8 ubCE, U8 ubLUN, U16 uwAddr, U8 *pubFeature);
#else /* (HOST_MODE == NVME) */
AOM_RETRY_SBRAID void VUCMicronFlaSetMLBi(U8 ubChannel, U8 ubCE, U8 ubLUN, U16 uwAddr, U8 pubFeature);
AOM_RETRY_SBRAID void VUCMicronFlaGetMLBi(U8 ubChannel, U8 ubCE, U8 ubLUN, U16 uwAddr, U8 *pubFeature);
#endif /* (HOST_MODE == NVME) */

#endif /* _VUC_MICRONGETVTSWEEP_H_ */
