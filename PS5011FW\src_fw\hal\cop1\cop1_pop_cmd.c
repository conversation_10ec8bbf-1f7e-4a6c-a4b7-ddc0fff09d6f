#include "setup.h"
#include "typedef.h"
#include "debug/debug.h"
#include "db_mgr/fw_tagid.h"
#include "hal/db/db_api.h"
#include "hal/cop1/cop1_api.h"
#include "hal/cop1/cop1_reg.h"
#include "hal/sys/api/misc/misc_api.h"
#include "ftl/ftl_seq_table_api.h"
#include "ftl/ftl_table_api.h"

typedef void (*cop1_cmd_doing_func_t)(COP1CQRst_t uoResult);

void COP1DelegateCmd(void)
{
#if (!BURNER_MODE_EN)
	while (!M_DB_CHECK_EMPTY(DB_COP1_CQ)) {
		U32 ulRPTRBackUp = M_DB_GET_RPTR(DB_COP1_CQ);
		COP1CQRst_t uoResult = *((COP1CQRst_t *)(ulRPTRBackUp));
		U16 uwTagId = (U16)uoResult.uoCOP1CQRstStruct.TagId;
		cop1_cmd_doing_func_t call_back = (cop1_cmd_doing_func_t)gpuoCmdTableMgr[COP1_CMD_TABLE_ID][uwTagId].ulCmdFuncPTR;

		if ((COP1_ST3CBLK_OP_BYPASS_OR_EXP == uoResult.uoCOP1CQRstStruct.BlkOP) && (COP1_ST3CBLK_ID == uoResult.uoCOP1CQRstStruct.TargetBlk)) {
			if (LOAD_PTE_DOING == gpVT->GC.LoadPTE.State) {
				LogAdd(LOG_TAG_ID_00062);
			}
			SeqTableRebuild(&uoResult);
		}
		else if (uoResult.uoCOP1CQRstStruct.btFinish) {
			if ((COP1_ST3CBLK_ID == uoResult.uoCOP1CQRstStruct.TargetBlk)
				&& ((COP1_ST3CBLK_OP_UNLOCK_DT == uoResult.uoCOP1CQRstStruct.BlkOP) || (COP1_ST3CBLK_OP_LOCK_DT == uoResult.uoCOP1CQRstStruct.BlkOP))) {
				gubWaitLockUnlockEvent--;
			}
			else if (call_back) {
				call_back(uoResult);
			}
			if (!((COP1_ST3BLK_OP_NOTIFY == uoResult.uoCOP1CQRstStruct.BlkOP) && (COP1_ST3BLK_ID == uoResult.uoCOP1CQRstStruct.TargetBlk))) {
				//Free Table & push tag id
				FTLPushTagPool(COP1_TAG_POOL_ID, uwTagId);
			}
		}
		M_DB_TRIGGER_READ_CNT(DB_COP1_CQ, 1);
		M_FW_ASSERT(ASSERT_HAL_COP1_0x0602, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_COP1_CQ, gDBQueueCnt.QueueCnt[(DB_COP1_CQ)] ) == M_DB_GET_RPTR((DB_COP1_CQ)));
	}
#endif
}

