#ifndef _MUX_API_H_
#define _MUX_API_H_

#include "hal/sys/reg/sys_pd0_reg.h"


#define	M_CLR_MUX_GPIO(X)								(R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL0] &= ~BIT(X))
#define	M_SET_MUX_GPIO(X)								(R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL0] |= BIT(X))
#define	M_SATA_PHY_SPEED_SATA_APU()						(R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL0] &= ~CR_MUX_PHY_SPDSEL_BIT)
#define	M_SATA_PHY_SPEED_SYSTEM()						(R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL0] |= CR_MUX_PHY_SPDSEL_BIT)
#if PS5021_EN
#define M_MUX_CTRL_BY_LED_CTRL2_9()						do{ \
														R32_SYS0_MUX_CTRL[R32_SYS0_MUX_CTRL_LED] &= ~(LED_MUX_SEL_MASK << LED_MUX_SEL_SHIFT); \
														R32_SYS0_MUX_CTRL[R32_SYS0_MUX_CTRL_LED] |= (LED_OTHSERS << LED_MUX_SEL_SHIFT); \
														R32_SYS0_PAD_CTRL[R32_SYS0_PAD_LED_OPT] &= ~(CR_LED_SO_OPT_BIT); \
														R32_SYS0_PAD_CTRL[R32_SYS0_PAD_LED_OPT] |= (APU_GPIO_DSA_ASSERT_OR_PADC); \
														} while (0)
#define M_MUX_CTRL_BY_LED_CTRL2_11()					do{ \
														R32_SYS0_MUX_CTRL[R32_SYS0_MUX_CTRL_LED] &= ~(LED_MUX_SEL_MASK << LED_MUX_SEL_SHIFT); \
														R32_SYS0_MUX_CTRL[R32_SYS0_MUX_CTRL_LED] |= (LED_OTHSERS << LED_MUX_SEL_SHIFT); \
														R32_SYS0_PAD_CTRL[R32_SYS0_PAD_LED_OPT] &= ~(CR_LED_SO_OPT_BIT); \
														R32_SYS0_PAD_CTRL[R32_SYS0_PAD_LED_OPT] |= (SONOF_PADC2); \
														} while (0)
#define M_MUX_CTRL_BY_APU()								do{ \
														R32_SYS0_MUX_CTRL[R32_SYS0_MUX_CTRL_LED] &= ~(LED_MUX_SEL_MASK << LED_MUX_SEL_SHIFT); \
														R32_SYS0_MUX_CTRL[R32_SYS0_MUX_CTRL_LED] |= (LED_OE_CTRL_BY_APU_GPIO << LED_MUX_SEL_SHIFT); \
														R32_SYS0_PAD_CTRL[R32_SYS0_PAD_LED_OPT] &= ~(CR_LED_SO_OPT_BIT); \
														R32_SYS0_PAD_CTRL[R32_SYS0_PAD_LED_OPT] |= (APU_GPIO_DSA_ASSERT_OR_PADC); \
														} while (0)

#else/* PS5021_EN*/
#define M_MUX_CTRL_BY_PADC()							(R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL1] &= ~( BIT8 | BIT9 ))
#define M_MUX_CTRL_BY_LED()								do{ \
														R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL1] &= ~(CR_MUX_LED_OE_MASK << CR_MUX_LED_OE_SHIFT); \
														R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL1] |= (LED_OE_CTRL_BY_LED << CR_MUX_LED_OE_SHIFT); \
														} while (0)
#define M_MUX_CTRL_BY_APU()								do{ \
														R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL1] &= ~(CR_MUX_LED_OE_MASK << CR_MUX_LED_OE_SHIFT); \
														R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL1] |= (LED_OE_CTRL_BY_APU << CR_MUX_LED_OE_SHIFT); \
														} while (0)
#endif/* PS5021_EN*/
#define M_SET_MUX_JTAG(X)								do{ \
														(R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL1] &= ~(CR_MUX_JTAG)); \
														(R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL1] |= ((X) << CR_MUX_JTAG_SHIFT)); \
														} while (0)

#define MUX_GPIO										(0)
#define MUX_JTAG										(1)
#define MUX_I2C											(2)

#if PS5021_EN
#define	M_CLR_MUX_GPIO_BY_VALUE(VALUE)					(R32_SYS0_MUX_CTRL[R32_SYS0_MUX_CTRL_GPIO] &= ~(VALUE))
#define	M_SET_MUX_GPIO_BY_VALUE(VALUE)					(R32_SYS0_MUX_CTRL[R32_SYS0_MUX_CTRL_GPIO] |= (VALUE))
#define	M_MUX_SET_FLASH_ODT_CONTROLL_BY_FLASH_IP()		// E21 not support
#define M_MUX_RESTROE_SETTING(X)						do{ \
														R32_SYS0_MUX_CTRL[R32_SYS0_MUX_CTRL_GPIO] = (X); \
														} while(0)
#define M_MUX_BACK_UP_SETTING(X)						do{ \
														(X) = R32_SYS0_MUX_CTRL[R32_SYS0_MUX_CTRL_GPIO]; \
														} while (0)
#define M_SET_MUX_FW_OPTION_1()							(R32_SYS0_PAD_CTRL[R32_SYS0_PAD_MUX_FW_OPT1] |= MUX_FW_OPT1_BIT)
#define M_SET_MUX_FW_OPTION_2()							(R32_SYS0_PAD_CTRL[R32_SYS0_PAD_MUX_FW_OPT1] &= ~MUX_FW_OPT1_BIT)
#else /* PS5021_EN */
#define	M_CLR_MUX_GPIO_BY_VALUE(VALUE)					(R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL0] &= ~(VALUE))
#define	M_SET_MUX_GPIO_BY_VALUE(VALUE)					(R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL0] |= VALUE)
#define	M_MUX_SET_FLASH_ODT_CONTROLL_BY_FLASH_IP()		do{ \
														R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL1] &= ~CR_MUX_FLH_ODT_BIT; \
														} while (0)
#define M_MUX_RESTROE_SETTING(X)						do{ \
														R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL0] = (X); \
														} while(0)
#define M_MUX_BACK_UP_SETTING(X)						do{ \
														(X) = R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL0]; \
														} while (0)
#endif /* PS5021_EN */


void mux_debug_port_select(U8 ubPsel, U8 ubTsel);

// GPIO[0]
void mux_gpio_0(void);
void mux_gpio_0_uart_tx_0(void);

// GPIO[1]
void mux_gpio_1(void);
void mux_spi_io3(void);
void mux_uart_rx(void);
void mux_tdat(void);
void mux_etm_data2(void);

// GPIO[2]
void mux_gpio_2(void);
void mux_gpio_2_uart_tx_0(void);
void mux_spi_miso(void);
void mux_bfh_selector(void);

// GPIO[3]
void mux_gpio_3(void);
void mux_gpio_3_uart_tx_1(void);
void mux_trstn(void);
void mux_etm_data1(void);
void mux_gpio_3_uart_tx_0(void);

// GPIO[4-5]
void mux_gpio_4_5(void);
void mux_gpio_4_5_uart_tx_1(void);
void mux_smb_sda_and_scl(void);
void mux_etm_data0_and_clk(void);

// GPIO[6]
void mux_gpio_6(void);
void mux_tclk(void);
void mux_write_protect(void);
void mux_etm_data3(void);
void mux_spi_mosi(void);

// GPIO[7-8]
void mux_gpio_7_8(void);
void mux_etm_4_5(void);
void mux_gpio_7_8_uart_tx_1(void);
void mux_spi_cs_and_clk(void);

// GPIO[9]
void mux_gpio_9(void);
void mux_pwrg_pd1(void);

// GPIO[10-12]
void mux_gpio_10_11_12(void);
void mux_etm_6_7(void);
void mux_cpu_perf_chk(void);
void mux_host_perf_chk(void);
void mux_spi_io2(void);
void mux_i2cm_scl_and_sda(void);
#if PS5017_EN
void mux_gpio_10_uart_tx_0(void);
#endif /* PS5017_EN */

// GPIO all enable
void mux_gpio_all_enable(void);
#if PS5017_EN
// GPIO for QFN88
void mux_gpio_QFN88_enable(void);
#endif /* PS5017_EN */

#endif /* _MUX_API_H_ */
