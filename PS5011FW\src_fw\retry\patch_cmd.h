
#ifndef _PATCH_CMD_H_
#define _PATCH_CMD_H_
#include "debug/debug_setup.h"
#include "hal/cop0/cop0_api.h"
#include "hal/cop0/cop0.h"
#include "hal/fip/fip_reg.h"
#include "hal/fip/fip_api.h"
#include "retry/retry.h"
#include "hal/fip/fpu.h"
#include "retry/stall.h"

typedef enum {
	MTP_INSERT_MT,
	MTP_DELETE_MT
} PATCH_MTP_OPERATION_TYPE;

typedef enum {
	IWL_NONE,
	IWL_PLANE_GROUP_A,
	IWL_PLANE_GROUP_B,
#if(IM_N48R)
	IWL_PLANE_CROSS_GROUP = 15
#else
	IWL_PLANE_CROSS_GROUP
#endif //IM_N48R
} PATCH_MICRON_IWL_BMP;
#define PATCH_CMD_MICRON_IWL_PLANE_NUM_PER_GROUP (2)

#define PATCH_LOG_HEADER					(0xFAF00000)
#define PATCH_LOG_AOM_HEADER				(0xFAF10000)
#define PATCH_LOG_MTQ_INFO					(0xFBF00000)
#define PATCH_LOG_MTP_INFO					(0xFCF00000)
#define PATCH_LOG_OPT_INFO					(0xFDF00000)
#define PATCH_LOG_PATCH_INFO				(0xFEF00000)
#define PATCH_LOG_BEC_BIN_SEARCH_STATE_INFO	(0xFFF90000)
#define PATCH_LOG_RETRY_STATE_INFO			(0xFFFA0000)
#define PATCH_LOG_FIP_ERROR_CQ_STATE_INFO	(0xFFFB0000)
#define PATCH_LOG_CODE_BANK_STATE_INFO		(0xFFFC0000)
#define PATCH_LOG_PATCH_ENTRY_STATE_INFO	(0xFFFD0000)
#define PATCH_LOG_PATCH_EXIT_STATE_INFO		(0xFFFE0000)

#define PATCH_CMD_QOS_QUEUE_SHIFT(N)	(N<<5)//????cop0??
#define PATCH_CMD_CQ_WR_FORMAT (0)//????cop0??
#define PATCH_CMD_CQ_RD_FORMAT (1)//????cop0??
#define PATCH_CMD_CMSG_PCA_PLANE_INFO (BIT0)//????cop0??
#define PATCH_CMD_CMSG_PAGE_VLD_0	(BIT0)//????cop0??

#define PATCH_CMD_OPT_SEARCH_FIND_1ST_MARK		(0)
#define PATCH_CMD_OPT_SEARCH_FIND_2ND_MARK		(1)

enum {
	PATCH_CMD_MTQ_CMD_0x3F_REVISE_TO_CMD_0x30,
	PATCH_CMD_MTQ_CMD_TRIGGER_CMD_0x30,
	PATCH_CMD_MTQ_CMD_TRIGGER_CACHE_0x31,
	PATCH_CMD_MTQ_CMD_TRIGGER_PLANEA_0x30,
	PATCH_CMD_MTQ_CMD_TRIGGER_PLANEB_0x30,
	PATCH_CMD_MTQ_CMD_REPLACE_0x3F31,		            // PATCH PLANEB 30, Change 3F31 to 3031
	PATCH_CMD_MTQ_CMD_REPLACE_0x313F,			// PATCH PLANEA 30, Change 313F to 3130
};

#define PATCH_CMD_MAX_PLANE_NUM (8)
#define PATCH_CMD_CACHE_CMD_INFO_BIT_NUM (1)
#if(MICRON_FSP_EN)
#define PATCH_CMD_MICRON_WL_STATUS_BYPASS_BIT_NUM	(1)
#define PATCH_CMD_MICRON_WL_NEED_SWITH_STATUS_BIT_NUM	(1)
#define PATCH_CMD_MICTON_WL_STATUS_BYPASS_EXTRA_PAGE_OVERRIDE_BIT_NUM	(1)
#else//MICRON_FSP_EN
#define PATCH_CMD_TOSHIBA_FAST_READ_INFO_BIT_NUM	(1)
#endif//MICRON_FSP_EN
#define PATCH_CMD_USERDEFINE_CMD_MAP_INFO_SHIFT (16)
#define PATCH_CMD_USERDEFINE_CACHE_INFO_SHIFT (24)
#define PATCH_CMD_USERDEFINE_TOSHIBA_FAST_READ_INFO_SHIFT	(27)
#define M_PATCH_CMD_GET_ANDES_CMD_PLANE_INFO(USR_DEFINE) (((USR_DEFINE)>>PATCH_CMD_USERDEFINE_CMD_MAP_INFO_SHIFT)&BIT_MASK(PATCH_CMD_MAX_PLANE_NUM));
#define M_PATCH_CMD_GET_CACHE_CMD_INFO_FROM_DMA(USR_DEFINE) (((USR_DEFINE)>>PATCH_CMD_USERDEFINE_CACHE_INFO_SHIFT)&BIT_MASK(PATCH_CMD_CACHE_CMD_INFO_BIT_NUM))
#define M_PATCH_CMD_GET_TOSHIBA_FAST_READ_INFO_FROM_DMA(USR_DEFINE) (((USR_DEFINE)>>PATCH_CMD_USERDEFINE_TOSHIBA_FAST_READ_INFO_SHIFT)&BIT_MASK(PATCH_CMD_TOSHIBA_FAST_READ_INFO_BIT_NUM))

#if ((IM_B47R) || IM_B37R || (IM_N48R))
#define PATCH_CMD_BIN_LEVEL_BIT_NUM	(3)
#define PATCH_CMD_USERDEFINE_BIN_SHIFT	(12)
#define M_PATCH_CMD_GET_MICRON_BIN_LEVEL_FROM_DMA(USR_DEFINE)	 (((USR_DEFINE) >> PATCH_CMD_USERDEFINE_BIN_SHIFT) & BIT_MASK(PATCH_CMD_BIN_LEVEL_BIT_NUM))
#if (READ_DISTURB_PRDH_EN)
#define PATCH_CMD_BLK_TYPE_BIT_NUM	(3)
#define PATCH_CMD_USERDEFINE_BLK_TYPE_SHIFT	(6)
#define M_PATCH_CMD_GET_PRDH_BLK_TYPE_FROM_DMA(USR_DEFINE)	 (((USR_DEFINE) >> PATCH_CMD_USERDEFINE_BLK_TYPE_SHIFT) & BIT_MASK(PATCH_CMD_BLK_TYPE_BIT_NUM))
#define PATCH_CMD_PRDH_BYPASS_READ_CNT_HANDLE_BIT	(BIT9)
#define M_PATCH_CMD_GET_PRDH_BYPASS_READ_CNT_HANDLE_FROM_DMA(USR_DEFINE)	 ((USR_DEFINE) & PATCH_CMD_PRDH_BYPASS_READ_CNT_HANDLE_BIT)
#endif /* (READ_DISTURB_PRDH_EN) */
#if (PS5021_EN || PS5017_EN)
#define M_BIN_LEVEL_IWL_READ_FPU_OFFSET_GAP (0)
#define M_BIN_LEVEL_READ_FPU_OFFSET_GAP(PLANE) (0)
#else /* (PS5021_EN || PS5017_EN) */
#define M_BIN_LEVEL_READ_FPU_OFFSET_GAP(PLANE)	((PLANE) > 2 ? 74 : ((PLANE) * 20 - 6)) // 1 Plane read FPU gap = 14, 2 Plane read FPU gap = 34, 3/4 Plane read FPU gap = 74
#define M_BIN_LEVEL_IWL_READ_FPU_OFFSET_GAP	(sizeof(gFpuEntryList.fpu_entry_tlc_1p_20_read_bin0) + sizeof(gFpuEntryList.fpu_entry_slc_1p_20_read_bin0))
#endif /* (PS5021_EN || PS5017_EN) */
#if(IM_N48R)
#define PATCH_CMD_MICRON_IWL_READ_CROSS_GROUP_INFO_BIT_NUM	(4)
#define PATCH_CMD_USERDEFINE_MICRON_IWL_READ_CROSS_GROUP_INFO_SHIFT	(27) /* BIT 27~30*/
#define M_PATCH_CMD_GET_MICRON_IWL_READ_CROSS_GROUP_INFO_FROM_DMA(USR_DEFINE) ((USR_DEFINE>>PATCH_CMD_USERDEFINE_MICRON_IWL_READ_CROSS_GROUP_INFO_SHIFT)&BIT_MASK(PATCH_CMD_MICRON_IWL_READ_CROSS_GROUP_INFO_BIT_NUM))
#define M_PATCH_CMD_GET_MICRON_IWL_READ_INFO_FROM_DMA(USR_DEFINE)				(M_PATCH_CMD_GET_MICRON_IWL_READ_CROSS_GROUP_INFO_FROM_DMA((USR_DEFINE)) ? TRUE : FALSE)
#else //IM_N48R
#define PATCH_CMD_MICRON_IWL_READ_INFO_BIT_NUM	(1)
#define PATCH_CMD_MICRON_IWL_READ_CROSS_GROUP_INFO_BIT_NUM	(1)
#define PATCH_CMD_USERDEFINE_MICRON_IWL_READ_INFO_SHIFT	(27)/*Micron B47R*/
#define PATCH_CMD_USERDEFINE_MICRON_IWL_READ_CROSS_GROUP_INFO_SHIFT	(28)/*Micron B47R*/
#define M_PATCH_CMD_GET_MICRON_IWL_READ_INFO_FROM_DMA(USR_DEFINE) ((USR_DEFINE>>PATCH_CMD_USERDEFINE_MICRON_IWL_READ_INFO_SHIFT)&BIT_MASK(PATCH_CMD_MICRON_IWL_READ_INFO_BIT_NUM))
#define M_PATCH_CMD_GET_MICRON_IWL_READ_CROSS_GROUP_INFO_FROM_DMA(USR_DEFINE) ((USR_DEFINE>>PATCH_CMD_USERDEFINE_MICRON_IWL_READ_CROSS_GROUP_INFO_SHIFT)&BIT_MASK(PATCH_CMD_MICRON_IWL_READ_CROSS_GROUP_INFO_BIT_NUM))
#endif //IM_N48R
#else /* ((IM_B47R) || (IM_N48R_NEED_CHECK)) */
#define M_PATCH_CMD_GET_MICRON_BIN_LEVEL_FROM_DMA(USR_DEFINE) (FALSE)
#define M_PATCH_CMD_GET_MICRON_BLOCK_TYPE_FROM_DMA(USR_DEFINE) (FALSE)
#define M_PATCH_CMD_GET_MICRON_BYPASS_READ_CNT_HDL_FROM_DMA(USR_DEFINE)  (TRUE)
#define M_BIN_LEVEL_IWL_READ_FPU_OFFSET_GAP (0)
#define M_BIN_LEVEL_READ_FPU_OFFSET_GAP(PLANE) (0)
#define M_PATCH_CMD_GET_MICRON_IWL_READ_INFO_FROM_DMA(USR_DEFINE) (FALSE)
#define M_PATCH_CMD_GET_MICRON_IWL_READ_CROSS_GROUP_INFO_FROM_DMA(USR_DEFINE) (FALSE)

#endif /* ((IM_B47R) || (IM_N48R_NEED_CHECK)) */

#if(MICRON_FSP_EN)
void PatchCmdMTQPatchWLStatusSwitchCmd(U8 ubQueueIdx);
#endif//MICRON_FSP_EN
AOM_PATCH_CMD void PatchCmdMTQPatchReadCmd(U8 ubQos, U8 ubQueueIdx, FPL_MT_FIND_DMA_INFO_t *pFindDMAInfo, U8 ubRecordDMAPlaneCnt, U8 ubMode);
AOM_PATCH_CMD void PatchCmdMTQPatchModify313F(U8 ubQos, U8 ubQueueIdx);
AOM_PATCH_CMD void PatchCmdMTPoolPatchReadCmd(U8 ubQos, FPL_MT_FIND_DMA_INFO_t *pFindDMAInfo, U8 ubRecordDMAPlaneCnt, U8 ubQueueIdx, U8 InsertNodeorOrder, U8 ubNodeType);
AOM_PATCH_CMD void PatchCmdReceiveFLHCQStateTransform(U8 ubQueueIdx);
AOM_PATCH_CMD void PatchCmdInsertMTPoolNode(U8 ubQos, U8 ubQueueIdx, U8 ubInsertNode, U8 ubTarget);
AOM_PATCH_CMD void PatchCmdDeleteMTPoolNode(U8 ubQos, U8 ubQueueIdx, U8 ubDeleteNode);
U8 PatchCmdSearchOPTMark(U8 ubQueueIdx, FPL_MT_FIND_DMA_INFO_t *pFindDMAInfo, U8 ubRecordDMACnt, U8 ubMode);
U8 PatchCmdSearchCOP0DMAInfo(FPL_MT_FIND_DMA_INFO_t *pFindDMAInfo, U8 ubRecordDMAPlaneCnt, U8 ubQos, U8 QueueIdx, U8 ubSearchStartIdx);
AOM_PATCH_CMD void PatchCmdFromMTQParser(U8 ubQos, U8 ubQueueIdx);
AOM_PATCH_CMD void PatchCmdFromMTPoolParser(U8 ubQos, U8 ubQueueIdx);
AOM_PATCH_CMD U8 PatchCmdSearchMTPIWLInfo(FPL_MT_FIND_DMA_INFO_t *pFindDMAInfo, U8 ubRecordDMAPlaneCnt, U8 ubQos, U8 ubQueueIdx);
AOM_PATCH_CMD void PatchCmdIWLParser(U8 ubQos, U8 ubQueueIdx);
AOM_PATCH_CMD void PatchCmdPushResetCmd(U8 ubQos, U8 ubQueueIdx);
AOM_PATCH_CMD U8 PatchCmdCheckQINTDone(U8 ubQos, U8 ubQueueIdx);
AOM_PATCH_CMD2 void PatchCmdResetFlash(U8 ubQueueIdx);
AOM_PATCH_CMD void PatchCmdHandler(U8 ubQos, U8 ubQueueIdx);
void PatchCmdDetect(void);
void PatchCmdAOMMTQPatchReadCmd(U8 ubQueueIdx, U8 ubQos, U8 ubMode, U8 ubRecordDMAPlaneCnt, FPL_MT_FIND_DMA_INFO_t *pFindDMAInfo);
void PatchCmdAOMIORPatch(U8 ubQueueIdx, U8 ubQos);
void PatchCmdAOMOPTPatch(U8 ubQueueIdx, U8 ubQos);
void PatchCmdWaitPatchDone(U8 ubQueueIdx, U32 ulPatchStateInfo);
U8 PatchCmdSearchOPTMarkValid(U8 ubQueueIdx, U8 *pubOPTIdx);
#endif
