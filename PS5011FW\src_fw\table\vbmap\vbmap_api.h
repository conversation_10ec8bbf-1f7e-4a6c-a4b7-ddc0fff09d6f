#ifndef _VBMAP_API_H_
#define _VBMAP_API_H_

#include "setup.h"
#include "fw_vardef.h"
#include "aom/aom_api.h"

typedef union {
	U16 uwAll;
	struct {
		U16 ToRUT_Unit: 13;	//0~4095, 12 bits are enough.
		U16 btVBType: 1;	//1: Index.  0: VB Unit.
		U16 btIsD1: 1;
		U16 btIsSLCMode: 1;
	} B;
} VirtualBlockRemapTable_t;

extern VirtualBlockRemapTable_t	*gpuwVBRMP;

//==============================================================================
// Definition
//==============================================================================
#define ERROR_VBRMP_UNIT_MARK			((U16)0xBBBB)
#define VBRMP_UNIT_INVALID				(0x1FFF)

//==============================================================================
// Function API
//==============================================================================
AOM_VUC U8 VBMAPCheck(U32 ulCheckRecordAddr);
AOM_ERROR_HANDLE void VBRMPSwap(Unit_t *puwSrcUnit, Unit_t *puwTargetUnit, U8 ubNeedSplitUnit, U8 ubNeedSwapErrorLogUnit);
#if MICRON_FSP_EN
U32 VBMAPTranslatePCA(U32 ulPCA);
#else /*MICRON_FSP_EN*/
AOM_ERROR_HANDLE2 U32 VBMAPTranslatePCA(U32 ulPCA);
#endif /*MICRON_FSP_EN*/
#endif /* _VBMAP_API_H_ */
