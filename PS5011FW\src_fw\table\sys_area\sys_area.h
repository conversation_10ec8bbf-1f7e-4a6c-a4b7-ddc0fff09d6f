#ifndef _SYS_AREA_H_
#define _SYS_AREA_H_

#include "setup.h"
#include "table/sys_area/sys_area_api.h"

typedef enum SysAreaUpdateStateEnum {
	SYSAREA_UPDATE_IDLE = 0,
	SYSAREA_UPDATE_DECIDE_HANDLE_CODE_BANKING,
	SYSAREA_UPDATE_HANDLE_CODE_BANKING,
	SYSAREA_UPDATE_DECIDE_HANDLE_DBT,
	SYSAREA_UPDATE_RESET_VARIABLE,
	SYSAREA_UPDATE_HANDLE_FREEQ,
	SYSAREA_UPDATE_DBTRUT,
	SYSAREA_UPDATE_SYS,
	SYSAREA_ERASE_OLD_BLOCK,
	SYSAREA_UPDATE_VARIABLE,
	SYSAREA_UPDATE_CHECK_NEED_RELEASE_BANKING,
	SYSAREA_ERR_AFTER_UPDATE,
	SYSAREA_ERR_AFTER_ERASE_OLD_BLOCK,
	SYSARE<PERSON>_UPDATE_DONE,
	SYSAREA_ERR_UPDATE_FAIL
} SysAreaUpdateStateEnum_t;

#if IM_N28
#if ((FW_CATEGORY_CUSTOMER == CUSTOMER_MAINSTREAM) || (SEAGATE_EN))
#define DEFAULT_D1_PE_CYCLE	(40000)
#define DEFAULT_D3_PE_CYCLE	(600)
#elif (FW_CATEGORY_CUSTOMER == CUSTOMER_MICRON_NICKS)
#define DEFAULT_D1_PE_CYCLE	(40000)
#define DEFAULT_D3_PE_CYCLE	(1500)
#else /* FW_CATEGORY_CUSTOMER */
#error FW_CATEGORY_CUSTOMER Error
#endif /* FW_CATEGORY_CUSTOMER */
#else
#if ((FW_CATEGORY_FLASH == FLASH_BICS4TLCHDR) || (FW_CATEGORY_FLASH == FLASH_BICS5TLC)||(CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_QLC))//zerio BICS8 Add//zerio bics6 qlc add
#define DEFAULT_D1_PE_CYCLE	(40000)
#define DEFAULT_D3_PE_CYCLE	(3000)
#else /* ((FW_CATEGORY_FLASH == FLASH_BICS4TLCHDR) || (FW_CATEGORY_FLASH == FLASH_BICS5TLC)) */
#define DEFAULT_D1_PE_CYCLE	(30000)
#define DEFAULT_D3_PE_CYCLE	(1500)
#endif /* ((FW_CATEGORY_FLASH == FLASH_BICS4TLCHDR) || (FW_CATEGORY_FLASH == FLASH_BICS5TLC)) */
#endif

AOM_SYSTEM_AREA void SystemAreaUpdateVariable(U8 ubType, SystemAreaBlock_t *New_Block, U8 ubNewBlkCnt);
AOM_SYSTEM_AREA_INIT void SystemAreaSetVariablesFromSystemBlk(void);
#endif