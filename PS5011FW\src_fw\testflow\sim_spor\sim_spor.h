#ifndef _SIM_SPOR_H_
#define _SIM_SPOR_H_

#define SIM_SPOR_MAX_SUB_CODE_NUM                      (16)

#define SIM_SPOR_INIT_FLOW_EN                   			(TRUE)      // Enable/Disable Init flow

#define SIM_SPOR_SUB_FORCE_EN                    		    (TRUE)      // Enable/Disable force code
#define SIM_SPOR_SUB_SELECT_FORCE_EN             		    (TRUE)      // Enable/Disable select force code

#define SIM_SPOR_SUPPORT_STANDBY_EN                 		(FALSE)		// Stadnby done then POR

#define SIM_SPOR_GR_COUNTER_EN                         (FALSE)

#define SIM_SPOR_GR_ONLY_EN                			(TRUE)       // Limit GR only

#if SIM_SPOR_GR_ONLY_EN
#define SIM_SPOR_BASE_CODE_NUM                         (SIM_SPOR_BASE_GR + 1) // GR only
#else
#define SIM_SPOR_BASE_CODE_NUM                         (SIM_SPOR_BASE_NUM)
#endif  /* SIM_SPOR_GR_ONLY_EN */

#define SIM_SPOR_GR_SUB_SELECT_NUM         			(SIM_SPOR_SUB_GR_SELECT_NUM)          // Limit Sub code 0, SIM_SPOR_SUB_GR_SELECT_NUM

#define SIM_SPOR_INIT_MAX_COUNTER               			(0x08)


#define SIM_SPOR_GR_MAX_COUNTER                             (0x1F)

#define SIM_SPOR_RELEASE_POWER_ON                    	    (FALSE)
#define SIM_SPOR_RELEASE_POWER_DOWN                  	    (TRUE)

#define SIM_SPOR_EVENT_EN                                   (0)
#define SIM_SPOR_EVENT_DIS                                  (1)

#define SIM_SPOR_EVENT_DIS_SUCCESS                          (0)
#define SIM_SPOR_EVENT_DIS_POWER_DOWN_FAIL                  (1)
#define SIM_SPOR_EVENT_DIS_POWER_UP_FAIL                    (2)

#define SIM_SPOR_INVALID_CODE                          (0xFF)

#define SIM_SPOR_INVALID_PARAMETER                         (0xFFFFFFFF)
// For Security Send/Receive cmd
#define SIM_SPOR_HOST_OPCODE                                (0xE0)

#define SIM_SPOR_STATUS_READY                               (0)
#define SIM_SPOR_STATUS_CHECKING                            (1)
#define SIM_SPOR_STATUS_RECOVERY                            (2)

// Source
#define SIM_SPOR_VENDOR_CMD                            (0)
#define SIM_SPOR_SECURITY_CMD                          (1)

#define SIM_SPOR_XZIP_ADJUST_LENGTH					(1)

typedef enum {
	SIM_SPOR_HOST_EVENT_DIS,
	SIM_SPOR_HOST_EVENT_NORMAL_STANDBY,        // not POR
	SIM_SPOR_HOST_EVENT_STANDBY,
	SIM_SPOR_HOST_EVENT_SPOR,
	SIM_SPOR_HOST_EVENT_ALL_CMD_COMPLETED,
	SIM_SPOR_HOST_EVENT_NOW_SPOR,           // only support security command
	SIM_SPOR_HOST_EVENT_NUM,
} SimSPORHostEvent_t;

typedef enum {
	SIM_SPOR_BASE_GR = 0,
	SIM_SPOR_BASE_VT = 1,
	SIM_SPOR_BASE_GC = 2,
	SIM_SPOR_BASE_TABLE = 3,
	SIM_SPOR_BASE_STANDBY = 4,
	SIM_SPOR_BASE_INIT = 5,
	SIM_SPOR_BASE_INITNORMAL = 6,
	SIM_SPOR_BASE_NUM,
} SimSPORBaseCode_t;

typedef enum {
	SIM_SPOR_SUB_GR_PROGRAM,
	SIM_SPOR_SUB_GR_DSA_BEFORE_SAVE_VT,
	SIM_SPOR_SUB_GR_DSA_SAVE_VT,

	SIM_SPOR_SUB_GR_DSA_FULL,

	SIM_SPOR_SUB_GR_BEFORE_SAVE_VT,
	SIM_SPOR_SUB_GR_SAVE_VT,

	SIM_SPOR_SUB_GR_FULL,

	SIM_SPOR_SUB_GR_RS,

	SIM_SPOR_SUB_GR_GET_NEW_ERASEING,

#if !XZIP_EN
	SIM_SPOR_SUB_GR_XZIP_FIRST_HIT_TABLE,
#endif /* !XZIP_EN */

	SIM_SPOR_SUB_GR_PTE_BITMAP,

	SIM_SPOR_SUB_GR_SELECT_NUM,

	SIM_SPOR_SUB_GR_PROGRAM_JOURNAL,

	SIM_SPOR_SUB_GR_FORCE_CLEAN,

	SIM_SPOR_SUB_GR_HOST_FLUSH_DONE,

	SIM_SPOR_SUB_GR_FUA_FILL_FSP,

	SIM_SPOR_SUB_GR_GC_START,

	SIM_SPOR_SUB_GR_WL_START,

	SIM_SPOR_SUB_GR_WLING,

	SIM_SPOR_SUB_GR_WLDONE,

	SIM_SPOR_SUB_GR_FORCE_NUM,

	SIM_SPOR_SUB_GR_DSA_CONTAINER_ALL_FULL,

	SIM_SPOR_SUB_GR_NUM,
#if XZIP_EN
	SIM_SPOR_SUB_GR_XZIP_FIRST_HIT_TABLE,
#endif /* XZIP_EN */
	SIM_SPOR_SUB_GR_VENDOR_EVENT,       // for vendor Now spor
} SimSPORGRSubCode_t;

typedef enum {
	SIM_SPOR_SUB_VT_SAVE_VT_CHILD,
	SIM_SPOR_SUB_VT_SELECT_NUM,
	SIM_SPOR_SUB_VT_FULL_VT_N_FULL_VTCHILD,
	SIM_SPOR_SUB_VT_TWO_VT_N_EMPTY_VTCHILD,
	SIM_SPOR_SUB_VT_FULL_VTCHILD,
	SIM_SPOR_SUB_VT_EMPTY_VTCHILD,
	SIM_SPOR_SUB_VT_FORCE_NUM,
	SIM_SPOR_SUB_VT_NUM,
} SimSPORVTSubCode_t;

typedef enum {
	SIM_SPOR_SUB_GC_PROGRAM,
	SIM_SPOR_SUB_GC_GCGR_EMPTY,
	SIM_SPOR_SUB_GC_GCSA_BEFORE_SAVE_VT,
	SIM_SPOR_SUB_GC_GCSA_SAVE_VT,
	SIM_SPOR_SUB_GC_DONE,
	SIM_SPOR_SUB_GC_SELECT_NUM,
	SIM_SPOR_SUB_GC_FORCE_NUM,
	SIM_SPOR_SUB_GC_NUM,
	SIM_SPOR_SUB_GC_GCGR_FULL,
} SimSPORGCSubCode_t;

typedef enum {
	SIM_SPOR_SUB_TABLE_FLUSH_TABLE_DUMMY_PLANE,
	SIM_SPOR_SUB_TABLE_FLUSH_TABLE_PGD,
	SIM_SPOR_SUB_TABLE_SELECT_NUM,
	SIM_SPOR_SUB_TABLE_FORCE_NUM,
	SIM_SPOR_SUB_TABLE_NUM,
	SIM_SPOR_SUB_TABLE_FLUSH_TABLE_FULL,
	SIM_SPOR_SUB_TABLE_FLUSH_TABLE_EMPTY,
	SIM_SPOR_SUB_TABLE_FLUSH_TABLE_VC,
	SIM_SPOR_SUB_TABLE_UPDATE_FULL,
	SIM_SPOR_SUB_TABLE_UPDATE_EMPTY,
	SIM_SPOR_SUB_TABLE_UPDATE_PROGRAM_PTE,
	SIM_SPOR_SUB_TABLE_UPDATE_PROGRAM_PMD,
} SimSPORTableSubCode_t;

typedef enum {
#if SIM_SPOR_SUPPORT_STANDBY_EN
	// Set one event to force jump main
	SIM_SPOR_SUB_STANDBY_DONE,		// Stadnby done then POR
	SIM_SPOR_SUB_STANDBY_SELECT_NUM,
	SIM_SPOR_SUB_STANDBY_FORCE_NUM,
	SIM_SPOR_SUB_STANDBY_NUM,
#else
	SIM_SPOR_SUB_STANDBY_SELECT_NUM,
	SIM_SPOR_SUB_STANDBY_FORCE_NUM,
	SIM_SPOR_SUB_STANDBY_NUM,
	SIM_SPOR_SUB_STANDBY_DONE,
#endif  /* SIM_SPOR_SUPPORT_STANDBY_EN */

} SimSPORStandbySubCode_t;

typedef enum {
	SIM_SPOR_SUB_INIT_SELECT_NUM,
	SIM_SPOR_SUB_INIT_VT_MOTHER_PROGRAM_DOING,
	SIM_SPOR_SUB_INIT_ERASE_OLD_VT_MOTHER_BEFORE,
	SIM_SPOR_SUB_INIT_ERASE_VT_MOTHER_DOING,
	SIM_SPOR_SUB_INIT_ERASE_VT_MOTHER_DONE,
	SIM_SPOR_SUB_INIT_VT_CHILD_CHANGED,
	SIM_SPOR_SUB_INIT_VT_MOTHER_CHANGED,
	SIM_SPOR_SUB_INIT_VT_MOTHER_PROGRAM_DONE,
	SIM_SPOR_SUB_INIT_VT_CHILD_EMPTY,
	SIM_SPOR_SUB_INIT_INITINFO_ERASE_NEWUNIT,
	SIM_SPOR_SUB_INIT_INITINFO_CHANGED_N_VT_CHILD_PROGRAM,
	SIM_SPOR_SUB_INIT_VT_CHANGED_DONE,
	SIM_SPOR_SUB_INIT_SCAN_GR_COPY_NEW_GR_DONE,
	SIM_SPOR_SUB_INIT_MOVE_TABLE,
	SIM_SPOR_SUB_INIT_MOVE_TABLE_DONE,
	SIM_SPOR_SUB_INIT_SCAN_GR_COPY_NEW_GR,
	SIM_SPOR_SUB_INIT_SCAN_GR_RESTORE_DSA,
	SIM_SPOR_SUB_INIT_SCAN_GR_ST1_FLUSH,
	SIM_SPOR_SUB_INIT_CHECK_GR_PTEBMP,
	SIM_SPOR_SUB_INIT_CHECK_GR_CHANGED,
	SIM_SPOR_SUB_INIT_SAVE_VBRMP_BEFORE_VT,
	SIM_SPOR_SUB_INIT_SAVE_VBRMP,
	SIM_SPOR_SUB_INIT_TRIM_CROSS_GR_BEFORE_COPYDONE,
	SIM_SPOR_SUB_INIT_TRIM_CROSS_GR_COPYDONE,
	SIM_SPOR_SUB_INIT_FORCECOPY_COPYDONE,
	SIM_SPOR_SUB_INIT_HAS_LAST_JOURNAL,
	SIM_SPOR_SUB_INIT_LAST_SYNC,
	SIM_SPOR_SUB_INIT_CHANGE_TO_POR,
	SIM_SPOR_SUB_INIT_SPOR_BEFORE_COPY_SPOR,	//before copy journal
	SIM_SPOR_SUB_INIT_SPOR_BEFORE_SYNC_SPOR,	//copied journal and before sync to it
	SIM_SPOR_SUB_INIT_SPOR_AFTER_SYNC_SPOR,	//sync to the copied journal
	SIM_SPOR_SUB_INIT_FORCE_NUM,
	SIM_SPOR_SUB_INIT_NUM,
	SIM_SPOR_SUB_INIT_DONE,
} SimSPORInitSubCode_t;

typedef enum {
	SIM_SPOR_SUB_INITNORMAL_SELECT_NUM,
	SIM_SPOR_SUB_INITNORMAL_FORCE_NUM,
	SIM_SPOR_SUB_INITNORMAL_NUM,
} SimSPORInitNormalSubCode_t;

typedef struct {
	U8 ubBaseCode;
	U8 ubSubCode;
	U8 ubInitSPORCounter;
	U8 ubBaseCodeNum;

	union {
		U8 ubAll;
		struct {
			U8 btRequestSPOREvent: 1;
			U8 btNowSPOREventSet: 1;
			U8 btNowSimSPOR: 1;
			U8 btNowFWInit: 1;
			U8 btHostCmdDone: 1;
			U8 btNowNormalStandby: 1;
			U8 btVendorSetEvent: 1;
			U8 : 1;
		};
	} Event;

	union {
		U8 ubAll;
		struct {
			U8 SPORSrc: 2;
			U8 btIsAdminAERCmdToSynchronousCmd: 1;
			U8 VTBackupCnt: 2;
			U8 : 3;
		};
	} Status;

	U16 uwGRCounter;

	U32 ulTotalCnt;

	U8 ubSubCodeForce[SIM_SPOR_BASE_NUM];
	U8 ubSubCodeNum[SIM_SPOR_BASE_NUM];
	U8 ubSubCodeSelectNum[SIM_SPOR_BASE_NUM];
	U8 ubSubCodeForceNum[SIM_SPOR_BASE_NUM];
	U32 ulCounter[SIM_SPOR_BASE_NUM][SIM_SPOR_MAX_SUB_CODE_NUM];
} SimSPORInfo_t;

typedef struct {
	union {
		U32 ulDW[SIZE_4KB / sizeof(U32)];

		struct {
			U32 ulOpCode;
			U32 ulDW[11];
			U32 ulEvent;      // DWORD 12
		} Send;

		struct {
			U32 ulStatus;
		} Receive;
	};
} SimSPORSecurityVendorInfo_t;

AOM_SPOR_2 void SimSPORReleaseCmd(U8 ubMode);
AOM_SPOR_2 void SimSPORInitVariable(void);
AOM_SPOR_2 void SimSPORSelectEvent(U8 ubSrc, U8 ubEvent);
AOM_SPOR_2 void SimSPOREventChecker(U8 ubBaseCode, U8 ubSubCode, U32 ulParameter1, U32 ulParameter2);
AOM_SPOR_2 void SimSPORGRCounter(void);
AOM_SPOR_2 void SimSPORSetEvent(U8 ubBaseCode, U8 ubSubCode);
AOM_SPOR_2 U8 SimSPORGetEventStatus(U8 ubBaseCode, U8 ubSubCode);
AOM_SPOR_2 U8 SimSPORFreeWLB(void);
AOM_SPOR_2 U8 SimSPORSecurityVendorPowerHandler(U8 ubIsSecuritySend, U32 ulBufAddr);
AOM_SPOR void SimSPORSecurityCmdHandler(void);
AOM_SPOR void SimSPORGetAPUWriteFIFOCID(void);
AOM_SPOR void SimSPORFreeSyncCmd(U8 ubMode);
AOM_SPOR void SimSPORUnlockPOR(void);
AOM_SPOR U8 SimSPORSetEventPreview(U8 ubBaseCode, U8 ubSubCode, U32 ulParameter1, U32 ulParameter2);
AOM_SPOR U8 SimSPORBaseInitPreview(U8 ubBaseCode, U8 ubSubCode, U32 ulParameter1, U32 ulParameter2);
AOM_SPOR void SimSPORSelectForceSubCode(U8 ubBaseCode);
// ************************************************
// do not modify AOM_INIT
// ************************************************
AOM_INIT void SimSPORResetAPURCQDoorBellPtr(void);

#endif /* _SIM_SPOR_H_ */
