/****************************************************************************
*
*  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
*  All rights reserved
*
*  The content of this document is confidential and shall be applied
*  subject to the terms and conditions of the license agreement and
*  other applicable laws. Any unauthorized access, use or disclosure
*  of this document is strictly prohibited and may be punishable
*  under laws.
*
*  raideccmap.h
*
*
*
****************************************************************************/

#ifndef RAIDECCMAP_H_
#define RAIDECCMAP_H_
/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "typedef.h"
#include "raideccmap_api.h"
#include "fw_vardef.h"

/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */
#define RAIDECCMAP_INVALID_FW_PARITY_TAG_IDX    (0xFFFF)
#define RAIDECCMAP_INVALID_HW_PARITY_TAG_IDX    (0xFF)

#define RAIDECCMAP_SAVE_SPARE_SHIFT			(128)


/*
 * ---------------------------------------------------------------------------------------------------
 *  enum
 * ---------------------------------------------------------------------------------------------------
 */
typedef enum {
	RAIDECCMAP_GET_EMPTY_VIRTUAL_PB,
} RaidECCMapGetVirtualPBModeEnum_t;



/*
 * ---------------------------------------------------------------------------------------------------
 *   extern global variables
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */


AOM_INIT_2 RaidECCMapParityMapEnum_t RaidECCMapGetParityMapEnumByEncodeType(RaidECCMapEncodeEnum_t EncodeType);
// raideccmap tagidx table
FW_BTCM0_SECTION U16 RaidECCMapGetFWParityTagIdx(RaidECCMapEncodeEnum_t EncodeType, U32 ulPlaneIdx);

// lib
AOM_RS_2 U8 RaidECCMapGetVirtualPBByParityIdx(RaidECCMapGetVirtualPBModeEnum_t Mode, U16 uwFWParityTagIdx);
AOM_RS_1 void RaidECCMapPreLoad(U8 ubRaidECCTagIdx, U8 ubVirtualPBIdx, U32 ulBufAddrData, U32 ulBufAddrSpare);

AOM_RS_2 void RaidECCMapRemoveTag(U8 ubHwRaidECCTag);

AOM_RS_2 void RaidECCMapCopyDataToExternalBuf(U32 ulSrcDataAddr, U32 ulDstDataAddr, U32 ulSrcSpareDataAddr, U32 ulDstSpareDataAddr);
AOM_RS_2 void RaidECCMapCopySpareFromIRAMToDBuf(U32 ulSrcSpareDataAddr, U32 ulDstSpareDataAddr);
AOM_RS_2 U32 RaidECCMapAllocateExternalParityBuf(U8 ubBufIdx);
AOM_RS_2 U8 RaidECCMapFindEmptyEncodedPB(void);

AOM_WL U8 RaidECCMapGetLoadParityCnt(RaidECCMapRecoverModeEnum_t Mode);
AOM_RS_2 void RaidECCMapForceSaveFlushParity(RaidECCMapRecoverModeEnum_t Mode);
AOM_RS_2 U8 RaidECCMapGetRecoverNum(RaidECCMapRecoverModeEnum_t Mode);
AOM_RS_2 void RaidECCMapResetRecoverNum(RaidECCMapRecoverModeEnum_t Mode);
AOM_RS_2 void RaidECCMapRemoveSwapTag(U16 uwPage);

AOM_GC_SELDOM void RaidECCMapGetParityUnit();
AOM_GC_SELDOM U8 RaidECCMapEraseParityUnit();
AOM_GC_SELDOM U8 RaidECCMapIsParityUnitFull(void);
AOM_GC_SELDOM U8 RaidECCMapCheckAddFreeOldParityUnit(void);

/***************************
 *  Map Related Functions  *
 ***************************/
// Common
AOM_RS_1 U16 RaidECCMapInvertCoord(U8 ubX, U16 uwY);
U16 RaidECCMapGetParityTagIdxByCoord(RaidECCMapParityMapEnum_t ParityMapMode, U16 uwX, U16 uwY, U8 ubPlane);
AOM_RS_2 U32 RaidECCMapSwapFlowFindLastParityPlanePtr(U8 ubRSEncodeMode, RaidECCMapParityMapEnum_t ubParityMapMode, U8 ubIsSlcMode, U16 uwPageIdx, U8 ubPlaneBank);
AOM_RS_2 U8 RaidECCMapCheckNeedSwapTag(U8 ubEncodeMode, U8 ubSLCMode, U32 ulPlaneIdx, U8 IsRemoveTag);

#if (MICRON_FSP_EN)
// Micron
AOM_RS_2 U8 IsInValidCoordPage(int x, int y);
AOM_RS_2 U32 RaidECCMapXLCFindLastSameTagPageByCoord(U8 ubX, U16 uwY);
#else  /* (MICRON_FSP_EN) */
// Toshiba
AOM_RETRY_RS void RaidECCMapGetDecodePageIndexInfo(RaidECCMapParityMapEnum_t ParityMapMode, U8 ubDecodePageIndex);
AOM_RETRY_RS U16 RaidECCMapGetGRSuperpageIndex(U8 ubRoutine);
#endif  /* (MICRON_FSP_EN) */
/***************************/

#endif /* _RAIDECCMAP_API_H_ */
