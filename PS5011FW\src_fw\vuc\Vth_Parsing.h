#ifndef _VTH_PARSING_H_
#define _VTH_PARSING_H_

/* Vth tool */
#define VTH_FIRST_TOKEN_OF_ALL	(0xCA)

#define VTH_ADDR_RELATED (0xA0)
#define VTH_ADDR_GET_FROM_PARAMETER (0x0)
#define VTH_ADDR_GET_COL_ADDR (0x2)
#define VTH_ADDR_GET_ROW_ADDR (0x3)
#define VTH_ADDR_GET_FROM_PREVIOUS	(0xA)
#define VTH_ADDR_GET_FROM_PIO	(0xB)
#define VTH_ADDR_GET_FROM_PREVIOUS_AND_PAGE_ADD1 (0xC)
#define VTH_ADDR_GET_FROM_PREVIOUS_AND_BLK_ADD1 (0xD)

#define VTH_BAD_COL_RELATED (0xB0)

#define VTH_CMD_RELATED (0xC0)
#define VTH_CMD_SETUP_FIRST	(0x0)
#define VTH_CMD_SETUP_SECOND	(0x1)
#define VTH_CMD_GET_FROM_PIO	(0x2)
#define VTH_CMD_SETUP_THIRD	(0x3)
#define VTH_CMD_CHECK_BUSY	(0xB)
#define VTH_CMD_COMMIT		(0xC)
#define VTH_CMD_INSERT_DELAY (0xD)
#define VTH_CMD_END	(0xE)

#define VTH_DATA_RELATED	(0xD0)
#define VTH_DATA_READ	(0xA)
#define VTH_DATA_READ_BY_PIO	(0xB)
#define VTH_DATA_PROGRAM	(0xC)
#define VTH_DATA_PROGRAM_BY_PIO	(0xE)

#define VTH_END_ADDR	(0xAE)
#define VTH_END_TOKEN_OF_ALL	(0xAF)
#define VTH_LENGTH_FROM_HEADER_TO_DESCRIPTION	(3)
#define VENDOR_NAND_VERIFICATION_SHADOW_CMD_OFFSET		(0x12000)
#define VENDOR_NAND_VERIFICATION_SHADOW_CMD             (BURNER_VENDOR_BUF_BASE + VENDOR_NAND_VERIFICATION_SHADOW_CMD_OFFSET)
#define VENDOR_NAND_VERIFICATION_SHADOW_CMD_SIZE		(0x1000)
#define VENDOR_NAND_VERIFICATION_SHADOW_DATA            (VENDOR_NAND_VERIFICATION_SHADOW_CMD + VENDOR_NAND_VERIFICATION_SHADOW_CMD_SIZE)
#define VENDOR_NAND_VERIFICATION_SHADOW_DATA_SIZE		(0x5000)
#define VENDOR_NAND_VERIFICATION_SHADOW_ECC             (VENDOR_NAND_VERIFICATION_SHADOW_DATA + VENDOR_NAND_VERIFICATION_SHADOW_DATA_SIZE)
#define VENDOR_NAND_VERIFICATION_SHADOW_ECC_SIZE		(0x200)
#define VENDOR_NAND_VERIFICATION_SHADOW_SPARE           (VENDOR_NAND_VERIFICATION_SHADOW_ECC + VENDOR_NAND_VERIFICATION_SHADOW_ECC_SIZE)
#define VENDOR_NAND_VERIFICATION_SHADOW_SPARE_SIZE		(0x200)
#define VENDOR_NAND_VERIFICATION_SHADOW_TIMER           (VENDOR_NAND_VERIFICATION_SHADOW_SPARE + VENDOR_NAND_VERIFICATION_SHADOW_SPARE_SIZE)
#define VENDOR_NAND_VERIFICATION_SHADOW_TIMER_SIZE		(0x400)
#define VENDOR_NAND_VERIFICATION_SHADOW_FPU           	(VENDOR_NAND_VERIFICATION_SHADOW_TIMER + VENDOR_NAND_VERIFICATION_SHADOW_TIMER_SIZE)

#define CHECK_STATUS    0
#define AddrCnt 6
#define TOGGLE			(2)
#define GetHigh4bit 	(0xF0)
#define GetLow4bit		(0x0F)
#define Column_address1	(0)
#define Column_address2	(1)
#define Row_address1	(2)
#define Row_address2	(3)
#define Row_address3	(4)
#define EachAdrCycleLen	(8)

typedef struct operation_setting NAND_OPERATION_SETTING_t;
struct operation_setting {
	U8 ubAddrTokenCnt : 2;
	U8 ubIsEnableEcc : 1;
	U8 ubIsInversion : 1;
	U8 ubIsConversion : 1;
	U8 ubIsCheckSpare : 1;
	U8 ubIsShowEcc : 1;
	U8 ubIsWrite : 1;
};

// Vth Function
void Vth_ToolParserHeaderAndAddress(VUC_OPT_HCMD_PTR_t pCmd, U8 *pubPatternBuffer, U8 ubChannel);
void Vth_ToolParserDescription(VUC_OPT_HCMD_PTR_t pCmd, U8 *pubPatternBuffer, U8 ubCH);

#endif /* _VTH_PARSING_H_ */
