#include "hal/pic/uart/uart_api.h"
#include "host/VUC_handler.h"
#include "vuc/VUC_UnlockJTAG.h"
#include "hal/sys/api/misc/misc_api.h"

void VUC_UnLockJTAG(VUC_OPT_HCMD_PTR_t pCmd)
{
#if !E21_TODO
	U8 ubi;

	M_UART(VUC_, "\nVUC_UNLOCK_JTAG");

	if (JTAG_LOCK == M_GET_UNLOCK_STATUS()) {
		for (ubi = 0; ubi < VUC_UNLOCK_SEQUENCE; ++ubi) {
			M_UART(VUC_, "\n%x", *(U32 *)(gulVUCBufAddr + ubi * SIZE_4B));
			M_SET_UNLOCK_PASSWD(*(U32 *)(gulVUCBufAddr + ubi * SIZE_4B));
		}
		if (JTAG_UNLOCK == M_GET_UNLOCK_STATUS()) {
			pCmd->ubState = FW_PROCESS_DONE;
		}
		else {
			pCmd->ubState = CMD_ERROR;
		}
	}
	else {
		pCmd->ubState = FW_PROCESS_DONE;
	}
#endif /* !E21_TODO */
}
