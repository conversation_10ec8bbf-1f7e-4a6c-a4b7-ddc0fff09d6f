/*
 * rdt_fip_instruction_fetching_test.h
 *
 *  Created on: 2020¦~11¤ë6¤é
 *      Author: <PERSON><PERSON>
 */

#ifndef SRC_FW_RDT_FIP_TEST_RDT_FIP_INSTRUCTION_FETCHING_TEST_H_
#define SRC_FW_RDT_FIP_TEST_RDT_FIP_INSTRUCTION_FETCHING_TEST_H_

#include "rdt/rdt_api.h"

#if RDT_MODE_EN
#include "hal/fip/fpu.h"
#include "hal/sys/api/clk/clk.h"
#include "hal/sys/api/mux/mux_api.h"

#define FPU_TX_CNT		(5)
#define FPU_RX_CNT		(2)
#define INS_TX_CNT		(4)
#define INS_RX_CNT		(2)
#define RANDOM_DLY		(0xFFF)
#define Tx				(0)
#define Rx				(1)

#define INSTRUCTIONS_PER_ROUND		(0x80)

BOOL rdt_fip_instruction_fetching_test(RDT_API_STRUCT_PTR rdt);
BOOL rdt_fip_instruction_fetching_parallelized_test(void);

#include "hal/fip/fip_reg.h"

// Variable Definition
#define EQUAL					(0)
#define NOT_EQUAL				(1)
#define GREATER					(2)
#define SMALLER					(3)
#define GREATER_EQUAL			(4)
#define SMALLER_EQUAL			(5)
#define IGNORE					(6)


// FPU Macro
#define FPU_BUSY_TIMEOUT(ubCh)		(R32_FCTL_CH[ubCh][R32_FCTL_FPU_TRIG] & (TIMEOUT_OCC_BIT | FPU_TRIGGER_BIT))

typedef struct FLP_DBG_SET {
	U32 cmd;
	U32 mask;
	U32 expect;
	U8 	condition;
	U32 response;
} FLP_DBG_SET;

void rdt_api_fpu_timeout_diagnosis(U8 ubCh);
BOOL rdt_check_debug_sets_OR(U8 ubCh, FLP_DBG_SET debug_set[], U8 size, BOOL print);
BOOL rdt_check_debug_sets_AND(U8 ubCh, FLP_DBG_SET debug_set[], U8 size, BOOL print);
void rdt_put_command_FCTL_DBF_INF(U8 ubCh, U32 cmd);
U32 rdt_check_freq_crossover_fail(U8 ubCh);

#endif // RDT_MODE_EN

#endif /* SRC_FW_RDT_FIP_TEST_RDT_FIP_INSTRUCTION_FETCHING_TEST_H_ */
