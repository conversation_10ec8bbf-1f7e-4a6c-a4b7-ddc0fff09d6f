#ifndef _VUC_MICRONGETDEVICECONFIGDATA_H_
#define _VUC_MICRONGETDEVICECONFIGDATA_H_
#include "aom/aom_api.h"

#define VUC_MICRON_GET_DRIVE_CONFIG_COMMAND_CLASS	(0x0002)
#define VUC_MICRON_GET_DRIVE_CONFIG_COMMAND_CODE	(0x0006)

#define VUC_MICRON_UID_LENGTH	(32)

#define SECTOR_TO_GB (23)

#pragma pack(push)
#pragma pack(1)
typedef struct {
	U8 ubResponseHeaderFormatVersion;
	U8 ubResponseDataFotmatVersion;
	U16 uwCMDClass;
	U16 uwCMDCode;
	U16 uwStatus;
	U32 ulDataPayloadSize;
} GetDriveConfigResponseHEADER_t;
#pragma pack(pop)

AOM_VUC_3 void VUCMicronGetDeviceConfigData(U32 ulPayloadAddr);

#endif /* _VUC_MICRONGETDEVICECONFIGDATA_H_ */
