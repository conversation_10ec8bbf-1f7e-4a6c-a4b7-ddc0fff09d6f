#include "rdt/rdt_api.h"

#if (RDT_MODE_EN && XZIP_EN)
#include <stdlib.h>
#include <time.h>
#include "hal/bmu/bmu_api.h"
#include "hal/bmu/bmu_pop_cmd.h"
#include "hal/zip/zip_api.h"
#include "hal/xzip/xzip_api.h"
#include "hal/cop1/cop1_inline.h"
#include "hal/cop1/cop1_pop_cmd.h"
#include "hal/sys/reg/sys_pd0_reg_5017.h"
#include "lpm/lpm_api.h"

#define RDT_XZIP_TEST_LB (LB_ID_FW)
#define RDT_XZIP_TEST_LOOP (3000)

#define RDT_XZIP_BMS (BIT_SEARCH_XZIP | BIT_SEARCH_ST3 | BIT_SEARCH_ST1)

#define RDT_XZIP_PCA_RANGE (0x1FFFFFFF)
#define RDT_XZIP_MAX_ENTRY_SIZE (256)
#define RDT_XZIP_CRC_BASE_VALUE (0x200) // can be any value, just for test

#define M_SET_STATIC_EVNET_TARGET_ID_N_INDEX_BY_LB_ID(LB_ID) (LB_ID >> 1)
#define M_SET_STATIC_EVENT_TARGET_ID_N_SHIFT_BY_LB_ID(LB_ID) ((LB_ID & BIT(0)) ? STATIC_EVENT_TARGET_ID_N_SHIFT_H4B : STATIC_EVENT_TARGET_ID_N_SHIFT_L4B)

typedef struct {
	U16 uwLBOffset;
	U8 ubAllocateDone;
	U8 ubAllcateFail;
} XZIP_TEST_STATUS;

XZIP_TEST_STATUS gXZIPTestStatus;

BOOL rdt_api_check_cop1_search_xzip_result(U16 uwExpectedXZIPIndex)
{
	U32 ulRPTRBackUp = M_DB_GET_RPTR(DB_COP1_CQ);

	while (M_DB_CHECK_EMPTY(DB_COP1_CQ));

	COP1CQRst_t result = *((COP1CQRst_t *)(ulRPTRBackUp));

	U16 uwTagId = (U16)result.uoCOP1CQRstStruct.TagId;
	U8 ubLoc = result.uoSHBlkSCHLCARst.Loc;
	U8 ubBlkOP = result.uoSHBlkSCHLCARst.BlkOP;
	U8 ubTargetBlk = result.uoSHBlkSCHLCARst.TargetBlk;
	U16 uwXZIPIndex = result.uoSHBlkSCHLCARst.ulRespInfo.XZIP.ubXZIPIndex;

	// schlca cmd cq blkop & tarblk must be 0x2 & 0x2
	if ((ubBlkOP != 0x2) || (ubTargetBlk != 0x2)) {
		UartPrintf("Not schlca CQ!, blkop = %x, tarblk = %x\n", ubBlkOP, ubTargetBlk);
		return FAIL;
	}

	if (ubLoc != SEARCH_LOC_ST1_XZIP_HIT) {
		UartPrintf("Loc = %x\n\r", ubLoc);
		return FAIL;
	}

	if (uwExpectedXZIPIndex != uwXZIPIndex) {
		UartPrintf("XZIP index = %x, expected XZIP index = %x\n", uwXZIPIndex, uwExpectedXZIPIndex);
		return FAIL;
	}

	//U16 uwPBAddr = result.uoSHBlkSCHLCARst.ulRespInfo.XZIP.PBAddr;
	//UartPrintf("PBAddr = %x, XZIP index = %x\n", uwPBAddr, uwXZIPIndex);

	M_DB_TRIGGER_READ_CNT(DB_COP1_CQ, 1);

	if ((U32)M_DB_GET_QBODY_PTR(DB_COP1_CQ, gDBQueueCnt.QueueCnt[(DB_COP1_CQ)]) != M_DB_GET_RPTR((DB_COP1_CQ))) {
		return FAIL;
	}

	FTLPushTagPool(COP1_TAG_POOL_ID, uwTagId);

	return PASS;
}

void rdt_api_xzip_test_bmu_init()
{
	U8 ubRegValue;
	U32 ulRegValue;

	// reset BMU
	LPMResetControl(CR_RST_N_BMU_BIT);

	// Set BMU Pool Size (value in mem.h)
	ulRegValue = R32_BMU[R32_BMU_POOL_SIZE];
	ulRegValue &= ~(POOL_SIZE_MASK);
	ulRegValue |= DBUF_BMU_PB_4K_NUM & POOL_SIZE_MASK;
	R32_BMU[R32_BMU_POOL_SIZE] = ulRegValue;

	// ENABLE PB
	ulRegValue = R32_BMU[R32_BMU_CONTROL];
	ulRegValue &= ~(POOL_EN_MASK);
	ulRegValue |= 1 & POOL_EN_MASK;
	R32_BMU[R32_BMU_CONTROL] = ulRegValue;

	// valid full send static event
	ulRegValue = R32_BMU[R32_BMU_STATIC_EVENT_EN];
	ulRegValue &= ~(STATIC_EVENT_FULL_MASK << STATIC_EVENT_FULL_SHIFT);
	ulRegValue |= ((BIT(RDT_XZIP_TEST_LB)) & STATIC_EVENT_FULL_MASK) << STATIC_EVENT_FULL_SHIFT;

	R32_BMU[R32_BMU_STATIC_EVENT_EN] = ulRegValue;

	// Which LB need send XZIP for build and search
	R32_BMU[R32_BMU_STATIC_EVENT_XZIP] = (BIT(RDT_XZIP_TEST_LB) & XZIP_LB_MASK) << XZIP_LB_SHIFT;

	// static event send to which CQ
	ubRegValue = R8_BMU[R8_BMU_STATIC_EVENT_TARGET_ID_N + M_SET_STATIC_EVNET_TARGET_ID_N_INDEX_BY_LB_ID(RDT_XZIP_TEST_LB)];
	ubRegValue &= ~((STATIC_EVENT_TARGET_ID_N_MASK) << (M_SET_STATIC_EVENT_TARGET_ID_N_SHIFT_BY_LB_ID(RDT_XZIP_TEST_LB)));
	ubRegValue |= TARGET_ID_SW_CQ0 << (M_SET_STATIC_EVENT_TARGET_ID_N_SHIFT_BY_LB_ID(RDT_XZIP_TEST_LB));
	R8_BMU[R8_BMU_STATIC_EVENT_TARGET_ID_N + M_SET_STATIC_EVNET_TARGET_ID_N_INDEX_BY_LB_ID(RDT_XZIP_TEST_LB)] = ubRegValue;
}

void rdt_api_xzip_test_init()
{
	rdt_api_xzip_test_bmu_init();

	M_SET_FW_WAIT_PCA();
	M_SET_INT_SRAM_PARITY();
	M_SET_WAIT_PCA_TIMEOUT();

	memset(&gXZIPTestStatus, 0, sizeof(XZIP_TEST_STATUS));
}

void rdt_api_allocate_xzip_buffer_callback(BMUCmdResult_t *BmuCmdResult)
{
	if (BMU_CMD_STATUS_SUCCESS != BmuCmdResult->BMUAllocateRst.ubResult) {
		gXZIPTestStatus.ubAllcateFail = TRUE;
	}
	gXZIPTestStatus.uwLBOffset = BmuCmdResult->BMUAllocateRst.uwLBOffset;
	//UartPrintf("Allocate PB address = %x, LB offset = %x\n", BmuCmdResult->BMUAllocateRst.uwPBAddress, gXZIPTestStatus.uwLBOoffset);
	gXZIPTestStatus.ubAllocateDone = TRUE;
}

BOOL rdt_api_allocate_xzip_buffer()
{
	gXZIPTestStatus.ubAllocateDone = gXZIPTestStatus.ubAllcateFail = FALSE;
	if (BMU_CMD_STATUS_SUCCESS != BMUAPICmdAllocate(BMU_CMD_NEED_CQ, BMU_CMD_TIME_INFINITE, RDT_XZIP_TEST_LB, 1, ALLOCATE_NO_USE_LCA, 0, BMU_CMD_STREAM_ID_DEFAULT, BMU_ALLOCATE_NOT_GET_E3D512, BMU_ALLOCATE_NOT_FUA, BMU_ALLOCATE_NEED_AUTO_FREE, (U32)rdt_api_allocate_xzip_buffer_callback, 0)) {
		return FAIL;
	}
	while (!gXZIPTestStatus.ubAllocateDone) {
		BMUDelegateCmd();
	}
	return gXZIPTestStatus.ubAllcateFail;
}

void rdt_api_validate_xzip_buffer(U32 ulCRC)
{
	BMUCmdResult_t BmuCmdResult;

	//UartPrintf("validate_buffer, crc_value = %x\n\r", crc_value);

	BMUAPICmdValidate(BMU_CMD_NEED_CQ, BMU_CMD_DEFAULT_PBADDR,
		RDT_XZIP_TEST_LB, gXZIPTestStatus.uwLBOffset, 0,
		BMU_VALIDATE_NOT_UPDATE_READ_CACHE_LOCK,
		BMU_CMD_STREAM_ID_DEFAULT, BMU_VALIDATE_SIZE_NOT_ZERO_DATA,
		BMU_VALIDATE_SIZE_DEFAULT_CMD_END_FLAG,
		BMU_VALIDATE_SIZE_SET_FULL_FLAG,
		BMU_VALIDATE_OPERATOR_OR, 0xFF, ZINFO_7,
		BMU_VALIDATE_NEED_UPDATE_PEOP, BMU_VALIDATE_SET_E3D4K_FLAG,
		ulCRC, 0, &BmuCmdResult);
}

BOOL rdt_api_recieve_validate_event()
{
	while (!M_DB_CHECK_EMPTY(DB_FW_CQ0)) {
		WriteCQInfo_t *wcqInfoPtr = (WriteCQInfo_t *)M_DB_GET_RPTR(DB_FW_CQ0);
#if 0
		UartPrintf("XZIPHit = %x \n", wcqInfoPtr->DW0.B.btXZIPHit);
		UartPrintf("XZIPBuild = %x \n", wcqInfoPtr->DW0.B.btXZIPBuild);
		UartPrintf("XZIPIndex = %x \n", wcqInfoPtr->DW1.B.ubXZIPIndex);
#endif
		if (wcqInfoPtr->DW1.B.btXZIPError) {
			UartPrintf("XZIP parity error!\n");
			return FAIL;
		}

		M_DB_TRIGGER_READ_CNT(DB_FW_CQ0, 1);
	}

	return PASS;
}

BOOL rdt_api_xzip_build_entries_crc()
{
	U32 ulEntryIndex = 0;
	for (ulEntryIndex = 0; ulEntryIndex < RDT_XZIP_MAX_ENTRY_SIZE; ++ulEntryIndex) {
		if (rdt_api_allocate_xzip_buffer()) {
			return FAIL;
		}

		rdt_api_validate_xzip_buffer(ulEntryIndex + RDT_XZIP_CRC_BASE_VALUE);

		if (rdt_api_recieve_validate_event()) {
			return FAIL;
		}
	}

	return PASS;
}

void rdt_api_cop1_insert_xzip_pca(U16 uwXZIPIndex, U32 ulPCA)
{
	WriteCQInfo_t wcq_info;

	memset(&wcq_info, 0, sizeof(wcq_info));

	wcq_info.ulLCA = ulPCA; // LCA = PCA for testing convenience
	wcq_info.DW0.B.btXZIPBuild = TRUE;
	wcq_info.DW1.B.ubXZIPIndex = uwXZIPIndex;

	COP1ST1InsertEGLCA(&wcq_info, ulPCA);
}

BOOL rdt_api_cop1_search_xzip_pca(U16 uwXZIPIndex, U32 ulPCA)
{
	COP1APISHBlkSCHLCA(ulPCA, RDT_XZIP_BMS, COP1_SH_NOT_FW_LOAD_TABLE, COP1_SH_LBIDSEL_NOT_LBIDSEL, COP1_COP0_ATTR0, NULL);
	return rdt_api_check_cop1_search_xzip_result(uwXZIPIndex);
}

void rdt_api_reset_cop1_st1()
{
	while (!M_COP1_ST1_CHK_IDLE()) {
		// do nothing
	}
	// 1.a reset ST1 HW (write 0 to bit0)
	R32_COP1_ST1[R32_COP1_ST1_IP_RST_N] = 0;

	// wait RESET done (till bit0 becomes 1)
	while ((R32_COP1_ST1[R32_COP1_ST1_IP_RST_N] & IP_RST_N_V_MASK) == 0) {
		// do nothing
	}
}

BOOL rdt_api_xzip_test_main(RDT_API_STRUCT_PTR rdt)
{
	U32 ulLoopIndex = 0;
	for (ulLoopIndex = 0; ulLoopIndex < RDT_XZIP_TEST_LOOP; ++ulLoopIndex) {

		U32 ulRandomPCA = rdt_api_get_int_random_value(RDT_XZIP_PCA_RANGE), ulEntryIndex = 0;

		// Update PCA to XZIP
		for (ulEntryIndex = 0; ulEntryIndex < RDT_XZIP_MAX_ENTRY_SIZE; ++ulEntryIndex) {
			rdt_api_cop1_insert_xzip_pca(ulEntryIndex, ulRandomPCA + ulEntryIndex);
		}

		for (ulEntryIndex = 0; ulEntryIndex < RDT_XZIP_MAX_ENTRY_SIZE; ++ulEntryIndex) {
			if (rdt_api_cop1_search_xzip_pca(ulEntryIndex, ulRandomPCA + ulEntryIndex)) {
				return FAIL;
			}

			// to avoid xzip lock full
			if (XZIP_PUSH_SQ_SUCCESS != XZIPUnLock(ulEntryIndex, NULL, 0)) {
				UartPrintf("XZIPUnLock fail\n");
				return FAIL;
			}

			XZIPDelegateCmd();
		}

		rdt_api_reset_cop1_st1();
	}

	return PASS;
}

BOOL rdt_api_xzip_test(RDT_API_STRUCT_PTR rdt)
{
	M_UART(RDT_TEST_, "\n[IC pattern] XZIP test start\n");
	rdt->current_state = RDT_STATE_XZIP_TEST;

	U32 start_time = rdt_api_rtt_get_timer_count();
	rdt_api_rml_log_add(rdt, RML_IMARK_XZIP_TEST_START);
	rdt_api_write_log_to_flash(rdt, RDT_LOG_RDT_TESTMARK);

	rdt_api_xzip_test_init();

	U8 ubResult = PASS;
	if (rdt_api_xzip_build_entries_crc()) {
		ubResult = FAIL;
	}
	else {
		if (rdt_api_xzip_test_main(rdt)) {
			ubResult = FAIL;
		}
	}

	if (ubResult) {
		rdt->rdt_parity.parity_error |= BIT(RDT_XZIP_EVT);
		rdt_api_check_ecc_parity(rdt, 0, 0);
		rdt_api_program_erl_log(rdt, TRUE);
	}

	rdt_api_rml_log_add(rdt, RML_IMARK_XZIP_TEST_END);
	rdt_api_write_log_to_flash(rdt, RDT_LOG_RDT_TESTMARK);

	M_UART(RDT_TEST_, "-- test time %d ms \n", rdt_api_rtt_get_timer_count() - start_time);
	M_UART(RDT_TEST_, "XZIP test %s\n", ubResult ? "fail" : "pass");

	return ubResult;
}

#endif    //RDT_MODE_EN && XZIP_EN
