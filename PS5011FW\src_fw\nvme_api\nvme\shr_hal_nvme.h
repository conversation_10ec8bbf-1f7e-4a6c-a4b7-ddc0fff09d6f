/**************************************************************************/
/*                                                                        */
/*           Copyright (c) 2000-2015 by Phison Electronics Corp.          */
/*                                                                        */
/**************************************************************************/

/**************************************************************************/
/*                                                                        */
/*  APPLICATION INTERFACE DEFINITION                       RELEASE        */
/*                                                                        */
/*    shr_hal_nvme.h                                        GNU C         */
/*                                                           X.X          */
/*  AUTHOR                                                                */
/*                                                                        */
/*                                                                        */
/*  DESCRIPTION                                                           */
/*                                                                        */
/*    This file defines the basic Application Interface (API) to the      */
/*    NVME library.                                                       */
/*                                                                        */
/*  RELEASE HISTORY                                                       */
/*                                                                        */
/*    DATE              NAME                      DESCRIPTION             */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#ifndef _SHR_NVME_LIB_H_
#define _SHR_NVME_LIB_H_

#include "aom/aom_api.h"
#include "host/VUC_host.h"
#include "nvme_api/nvme/shr_hal_nvme_cmgr.h"
#if (PS5021_EN)
#include "nvme_api/nvme/shr_hal_nvme_reg_5021.h"
#else /* (PS5021_EN) */
#include "nvme_api/nvme/shr_hal_nvme_reg_5013.h"
#endif /* (PS5021_EN) */
#include "hal/apu/apu_reg.h"
#include <stdlib.h>
#include <string.h>

#define BC_4B           0x0004
#define BC_8B             0x0008
#define BC_16B           0x0010
#define BC_64B			0x0040
#define BC_256B         0x0100
#define BC_512B         0x0200
#define BC_1KB          0x0400
#define BC_2KB          0x0800
#define BC_4KB          0x1000
#define BC_6KB          0x1800
#define BC_8KB          0x2000
#define BC_12KB         0x3000
#define BC_16KB         0x4000
#define BC_32KB         0x8000
#define BC_256KB        0x00040000
#define BC_512KB        0x00080000
#define BC_1MB          0x00100000
#define BC_16MB         0x01000000
#define BC_32MB         0x02000000

#define LCAC_1C             0x01  //LCA count


#define APU_SRAM_LS_EN          (0)
#define NVME_SRAM_LS_EN         (0)
#define NVME_ALLCMD_SYNC_MODE   (0)
#define NVME_BYPASS_DEPCHK      (0)
#define NONUSER_DATA_BMU_EN     (1)
#define NONUSER_DATA_E3D_EN     (1)
#define APU_AUTO_SRCH_EN        (1)
#define WR_AUTO_CPL_EN          (1)
#define RD_AUTO_CPL_EN          (1)
#define SYNC_WR_AUTO_CPL_EN     (0)
#define SYNC_RD_AUTO_CPL_EN     (0)

#define WR_CMD_WITH_BCMD                        (0)
#define WR_CMD_WITH_BCMD_MT                     (1)
#define WR_CMD_WITH_BCMD_FW                     (2)
#define WR_CMD_WITH_MT                          (3)
#define WR_CMD_WITH_ONESHOT                     (4)
#define WR_CMD_MT_NLCA_THR                      (8)   //(in 4KB unit)
#define WR_CMD_MT_BC_THR                        (3)   //(in 4KB unit)

#define RD_CMD_512B_MOD                         (0)
#define RD_CMD_BCMD_MOD                         (1)

#if (PS5021_EN)
#define MAX_CTAG_NUMBER							(504)
#define MAX_HW_CTAG_NUMBER                      (512)
#else /*(PS5021_EN)*/
#define MAX_CTAG_NUMBER                         (128)
#define MAX_HW_CTAG_NUMBER                      (128)
#endif /*(PS5021_EN)*/

#define MAX_QUEUE_NUMBER                        (9)   // ADM queue included

#define NVME_WAIT_D2H_IDLE_TIMEOUT_THRESHOLD	(1000) //1ms

/*====================== END: NVME SETTING ===========================*/
#pragma pack(push)
#pragma pack(1)


#define POWERON_RST     		                (0)
#define NVME_CTRLRST    		                (1)

#define ASYNC_CMD               (0x00)
#define WRITE_CMD               (0x01)
#define READ_CMD                (0x02)
#define CMP_CMD                 (0x03)

#define WCH_WTYPE               (0x00)
#define WCH_RTYPE               (0x01)

#define NOT_BCMD (0x00)
#define BYTE_CMD (0x01)

#define PRP_ENTRY_SIZE						(8)//Byte
#define PRP_ENTRY_ALIGN						(4)//Byte
#define PRP_ENTRY_ALIGN_LOG_2				(2)

#define PRP_ENTRY_LIST_SIZE					(8)//Byte
#define PRP_ENTRY_LIST_ALIGN				(8)//Byte
#define PRP_ENTRY_LIST_ALIGN_LOG_2			(3)

#define BYTE_CMD_HW_TRIGGER_D2H_ALIGN		(16)//Byte
#define BYTE_CMD_HW_TRIGGER_D2H_ALIGN_LOG_2	(4)
#define BYTE_CMD_HW_TRIGGER_TD_ALIGN		(4)//Byte
#define BYTE_CMD_HW_TRIGGER_TD_ALIGN_LOG_2	(2)

#define VUC_FORMAT_LBAF_SHIFT               (0)
#define VUC_FORMAT_LBAF_MASK                (0x0F)
#define VUC_FORMAT_MS_SHIFT                 (4)
#define VUC_FORMAT_MS_MASK                  (0x01)
#define VUC_FORMAT_PI_SHIFT                 (5)
#define VUC_FORMAT_PI_MASK                  (0x07)
#define VUC_FORMAT_PIL_SHIFT                (0)
#define VUC_FORMAT_PIL_MASK                 (0x01)
#define VUC_FORMAT_SES_SHIFT                (1)
#define VUC_FORMAT_SES_MASK                 (0x07)


#if (PS5021_EN)   // AER Type
#define NVME_ERROR_INVALID_SQDB_VAL   (1)
#define NVME_ERROR_INVALID_CQDB_VAL   (2)
#define NVME_ERROR_INVALID_SQDB_REG   (3)
#define NVME_ERROR_INVALID_CQDB_REG   (4)
#define NVME_ERROR_CMGR_SRAM_ERROR   (5)
#endif /* (PS5021_EN)*/

typedef struct nvme_cmd          NVMEHCMD, *PNVMEHCMD;
typedef union nvme_wch          NVMEWCH, *PNVMEWCH;
typedef union nvme_td           NVMETD, *PNVMETD;
typedef union nvme_cpl          NVMECPL, *PNVMECPL;
typedef union nvme_rcq          NVMERCQ, *PNVMERCQ;

typedef volatile struct nvme_cmd         *REGHCMD_PTR;
typedef volatile union nvme_wch			 *REGWCH_PTR;
typedef volatile union nvme_td           *REGTD_PTR;
typedef volatile union nvme_cpl          *REGCPL_PTR;
typedef volatile union nvme_rcq	         *REGRCQ_PTR;

typedef union nvme_hostevt      NVMEHOSTEVT, *PNVMEHOSTEVT;

typedef volatile union nvme_cpl		*REGCPL_PTR_t;
typedef	union nvme_cpl				NVMECPL_t, *PNVMECPL_t;
typedef volatile union nvme_td		*REGTD_PTR_t;
typedef	union nvme_td				NVMETD_t, *PNVMETD_t;
typedef volatile union nvme_wch		*REGWCH_PTR_t;
typedef	union nvme_wch				NVMEWCH_t, *PNVMEWCH_t;
typedef volatile union nvme_cpl		*REGCPL_PTR_t;
typedef	union nvme_cpl				NVMECPL_t, *PNVMECPL_t;

typedef union nvme_dma_err_st             NVME_DMA_ERR_ST, *NVME_DMA_ERR_ST_PTR;
union nvme_dma_err_st {
	struct raw {
		U32 	dw[2];
	} raw;

	struct el {
		U32     read_dma_err         :   1;
		U32     write_dma_err	  	 :   1;
		U32     read_unc_err         :   1;
		U32     rsv                  :   5;

		U32 	PRP_ERR              :   1;
		U32     PCIE_AXI_PAR_ERR     :   1;
		U32     PCIE_AXI_ERR         :   1;
		U32 	rsv2  				 :	 5;

		U32     RLS_APU_PAR_ERR      :   1;
		U32     RLS_PRP_ERR          :   1;
		U32     RLS_PCIE_AXI_ERR     :   1;
		U32     RLS_SYS_AXI_ERR      :   1;
		U32     RLS_E3D_ERR          :   1;
		U32     RLS_UNC_ERR          :   1;
		U32     rsv3                 :   2;

		U32     APU_ERR_NSID         :   8;

		U32     APU_ERR_LBA;
	} el;
};

typedef struct nvme_sq_cmd             NVME_SQ_CMD, *NVME_SQ_CMD_PTR;
struct nvme_sq_cmd {
	union {
		struct raw_data {
			U32     dw[16];
		} raw_data;
		struct raw_u16_data {
			U16     wd[32];
		} raw_u16_data;
		struct raw_u8_data {
			U8     by[64];
		} raw_u8_data;
		struct adm {
			/*  dw0 */
			U32     opcode           :   8;
			U32     fuse		        :   2;
			U32     rsv                 :   4;
			U32     psdt               :   2;
			U32 	cid  			 :   16;
			/*	dw1 */
			U32     nsid;
			/*	dw2 */
			U32     rsv1;
			/*	dw3 */
			U32 	rsv2;
			/*	dw4 */
			U32 	mptr_L;
			/*	dw5 */
			U32 	mptr_H;
			/*	dw6-dw7 */
			union {
				struct prp1_64 {
					U64    addr;
				} prp1_64;
				struct prp1_32 {
					U32    addr_L;
					U32    addr_H;
				} prp1_32;
			};
			/*dw8-dw9 */
			union {
				struct prp2_64 {
					U64    addr;
				} prp2_64;
				struct prp2_32 {
					U32    addr_L;
					U32    addr_H;
				} prp2_32;
			};

			union {
				struct delSQ {
					/*	dw10 */
					U32 	qid 				:	16;
					U32 	rsv3				:	16;
					/*	dw11 */
					U32 	rsv4;
					/*	dw12 */
					U32 	rsv5;
					/*	dw13 */
					U32 	rsv6;
					/*	dw14 */
					U32 	rsv7;
					/*	dw15 */
					U32 	rsv8;
				} delSQ;

				struct createSQ {
					/*	dw10 */
					U32 	qid 				:	16;
					U32 	qsize	             :	16;
					/*	dw11 */
					U32 	pc				:	1;
					U32 	qprio			:	2;
					U32 	rsv4				:	13;
					U32 	cqid				:	16;
					/*	dw12 */
					U32 	rsv5;
					/*	dw13 */
					U32 	rsv6;
					/*	dw14 */
					U32 	rsv7;
					/*	dw15 */
					U32 	rsv8;
				} createSQ;

				struct getlog {
					/*	dw10 */
					U32  logid                 :   8;
					U32  lsp                   :   4;
					U32  rsv_1                 :   3;
					U32  rae                   :   1;
					U32  uwNumd                  :   16;
					/*	dw11 */
					U32 	rsv4;
					/*	dw12 */
					U32 	rsv5;
					/*	dw13 */
					U32 	rsv6;
					/*	dw14 */
					U32 	rsv7;
					/*	dw15 */
					U32 	rsv8;
				} getlog;

				struct delCQ {
					/*	dw10 */
					U32 	qid 				:	16;
					U32 	rsv3				:	16;
					/*	dw11 */
					U32 	rsv4;
					/*	dw12 */
					U32 	rsv5;
					/*	dw13 */
					U32 	rsv6;
					/*	dw14 */
					U32 	rsv7;
					/*	dw15 */
					U32 	rsv8;
				} delCQ;

				struct createCQ {
					/*	dw10 */
					U32 	qid 				:	16;
					U32 	qsize				:	16;
					/*	dw11 */
					U32 	pc					:	1;
					U32 	intEn				:	1;
					U32 	rsv4				:	14;
					U32 	intVec				:	16;
					/*	dw12 */
					U32 	rsv5;
					/*	dw13 */
					U32 	rsv6;
					/*	dw14 */
					U32 	rsv7;
					/*	dw15 */
					U32 	rsv8;
				} createCQ;

				struct idf {
					/*	dw10 */
					U32 	cns 				:	8;
					U32 	rsv3				:	8;
					U32 	cntid			:	16;
					/*	dw11 */
					U32 	rsv4;
					/*	dw12 */
					U32 	rsv5;
					/*	dw13 */
					U32 	rsv6;
					/*	dw14 */
					U32 	rsv7;
					/*	dw15 */
					U32 	rsv8;
				} idf;

				struct abort {
					/*	dw10 */
					U32 	sqid				:	16;
					U32 	abort_cid		:	16;
					/*	dw11 */
					U32 	rsv4;
					/*	dw12 */
					U32 	rsv5;
					/*	dw13 */
					U32 	rsv6;
					/*	dw14 */
					U32 	rsv7;
					/*	dw15 */
					U32 	rsv8;
				} abort;


				struct setfeature {
					/*	dw10 */
					U32 	fid  				:	8;
					U32     rsv3                 :   23;
					U32 	sv 	     			:	1;

					union {
						struct arb {
							/*	dw11 */
							U32 	arb_bust              :   3;
							U32 	rsv_1                 :   5;
							U32     lpw                   :   8;
							U32     mpw                   :   8;
							U32     hpw                   :   8;
							/*	dw12 */
							U32 	rsv5;
							/*	dw13 */
							U32 	rsv6;
							/*	dw14 */
							U32 	rsv7;
							/*	dw15 */
							U32 	rsv8;
						} arb;

						struct pwr_mang {
							/*	dw11 */
							U32 	pwr_st                :   5;
							U32 	workload              :   3;
							U32     rsv4                  :   24;
							/*	dw12 */
							U32 	rsv5;
							/*	dw13 */
							U32 	rsv6;
							/*	dw14 */
							U32 	rsv7;
							/*	dw15 */
							U32 	rsv8;
						} pwr_mang;

						struct lba_rang {
							/*	dw11 */
							U32 	num_lba_rng           :   6;
							U32     rsv4                  :   26;
							/*	dw12 */
							U32 	rsv5;
							/*	dw13 */
							U32 	rsv6;
							/*	dw14 */
							U32 	rsv7;
							/*	dw15 */
							U32 	rsv8;
						} lba_rang;

						struct temp_thrhold {
							/*	dw11 */
							U32 	tmpth                 :   16;
							U32     tmpsel                :   4;
							U32     thsel                 :   2;
							U32     rsv4                  :   10;
							/*	dw12 */
							U32 	rsv5;
							/*	dw13 */
							U32 	rsv6;
							/*	dw14 */
							U32 	rsv7;
							/*	dw15 */
							U32 	rsv8;
						} temp_thrhold;

						struct err_rcovy {
							/*	dw11 */
							U32 	tler                  :   16;
							U32     dulbe                 :   1;
							U32     rsv4                  :   15;
							/*	dw12 */
							U32 	rsv5;
							/*	dw13 */
							U32 	rsv6;
							/*	dw14 */
							U32 	rsv7;
							/*	dw15 */
							U32 	rsv8;
						} err_rcovy;

						struct wr_cache {
							/*	dw11 */
							U32 	wce                   :   1;
							U32     rsv4                  :   31;
							/*	dw12 */
							U32 	rsv5;
							/*	dw13 */
							U32 	rsv6;
							/*	dw14 */
							U32 	rsv7;
							/*	dw15 */
							U32 	rsv8;
						} wr_cache;

						struct num_queue {
							/*	dw11 */
							U32 	nsqr                   :   16;
							U32     ncqr                   :   16;
							/*	dw12 */
							U32 	rsv5;
							/*	dw13 */
							U32 	rsv6;
							/*	dw14 */
							U32 	rsv7;
							/*	dw15 */
							U32 	rsv8;
						} num_queue;

						struct int_coal {
							/*	dw11 */
							U32 	thr                    :   8;
							U32     time                   :   8;
							U32     rsv4                   :   16;
							/*	dw12 */
							U32 	rsv5;
							/*	dw13 */
							U32 	rsv6;
							/*	dw14 */
							U32 	rsv7;
							/*	dw15 */
							U32 	rsv8;
						} int_coal;

						struct int_vec {
							/*	dw11 */
							U32 	iv                     :   16;
							U32     cd                     :   1;
							U32     rsv4                   :   15;
							/*	dw12 */
							U32 	rsv5;
							/*	dw13 */
							U32 	rsv6;
							/*	dw14 */
							U32 	rsv7;
							/*	dw15 */
							U32 	rsv8;
						} int_vec;

						struct wr_atomic {
							/*	dw11 */
							U32 	dn                     :   1;
							U32     rsv4                   :   31;
							/*	dw12 */
							U32 	rsv5;
							/*	dw13 */
							U32 	rsv6;
							/*	dw14 */
							U32 	rsv7;
							/*	dw15 */
							U32 	rsv8;
						} wr_atomic;

						struct async_confg {
							/*	dw11 */
							U32 	smart_crit             :   8;
							U32     ns_attr_notic          :   1;
							U32     fw_activate_notic      :   1;
							U32     rsv4                   :   22;
							/*	dw12 */
							U32 	rsv5;
							/*	dw13 */
							U32 	rsv6;
							/*	dw14 */
							U32 	rsv7;
							/*	dw15 */
							U32 	rsv8;
						} async_confg;

						struct apst {
							/*	dw11 */
							U32 	apste                  :   1;
							U32     rsv4                   :   31;
							/*	dw12 */
							U32 	rsv5;
							/*	dw13 */
							U32 	rsv6;
							/*	dw14 */
							U32 	rsv7;
							/*	dw15 */
							U32 	rsv8;
						} apst;

						struct hmb {
							/*	dw11 */
							U32 	ehm                    :   1;
							U32     mr                     :   1;
							U32     rsv4                   :   30;
							/*	dw12 */
							U32 	hsize;
							/*	dw13 */
							U32 	hmdlla;
							/*	dw14 */
							U32 	hmdlua;
							/*	dw15 */
							U32 	hmdlec;
						} hmb;
					};
				} setfeature;

				struct getfeature {
					/*	dw10 */
					U32 	fid 				:	8;
					U32 	sel 				:	3;
					U32 	rsv3				:	21;
					/*	dw11 */
					U32 	rsv4;
					/*	dw12 */
					U32 	rsv5;
					/*	dw13 */
					U32 	rsv6;
					/*	dw14 */
					U32 	rsv7;
					/*	dw15 */
					U32 	rsv8;
				} getfeature;

				struct asyc_evnt {
					/*	dw10 */
					U32 	rsv3;
					/*	dw11 */
					U32 	rsv4;
					/*	dw12 */
					U32 	rsv5;
					/*	dw13 */
					U32 	rsv6;
					/*	dw14 */
					U32 	rsv7;
					/*	dw15 */
					U32 	rsv8;
				} asyc_evnt;

				struct ns_mang {
					/*	dw10 */
					U32     sel                 :   4;
					U32 	rsv3                :   28;
					/*	dw11 */
					U32 	rsv4;
					/*	dw12 */
					U32 	rsv5;
					/*	dw13 */
					U32 	rsv6;
					/*	dw14 */
					U32 	rsv7;
					/*	dw15 */
					U32 	rsv8;
				} ns_mang;

				struct ns_att {
					/*	dw10 */
					U32 	sel 				:	4;
					U32 	rsv3				:	28;
					/*	dw11 */
					U32 	rsv4;
					/*	dw12 */
					U32 	rsv5;
					/*	dw13 */
					U32 	rsv6;
					/*	dw14 */
					U32 	rsv7;
					/*	dw15 */
					U32 	rsv8;
				} ns_att;

				struct fw_cmit {
					// CDW 10
					U32 fs         : 3;     // firmware slot (FS)
					U32 ca         : 3;     // commit action (CA)
					U32 rsvd       : 25;
					U32 bpid      : 1;   //boot partiton(BPID)
					/*	dw11 */
					U32 	rsv4;
					/*	dw12 */
					U32 	rsv5;
					/*	dw13 */
					U32 	rsv6;
					/*	dw14 */
					U32 	rsv7;
					/*	dw15 */
					U32 	rsv8;
				} fw_cmit;

				struct fw_img_dl {
					// CDW 10
					U32 numd;                   // number of dwords (NUMD)

					// CDW 11
					U32 offset;                 // the number of Dword offset from the start of the firmware image
					/*	dw12 */
					U32 rsv5;
					/*	dw13 */
					U32 rsv6;
					/*	dw14 */
					U32 rsv7;
					/*	dw15 */
					U32 rsv8;
				} fw_img_dl;

				struct dev_self_test {
					// CDW 10
					U8  stc     : 4; // Self-test Code
					U8  rsv1    : 4;
					U8  rsv2[3];
					// CDW 11
					U32 rsv4;
					/*	dw12 */
					U32 rsv5;
					/*	dw13 */
					U32 rsv6;
					/*	dw14 */
					U32 rsv7;
					/*	dw15 */
					U32 rsv8;
				} dev_self_test;

				struct fw_format {
					// CDW 10
					U32 lbaf        :  4;       // LBA Format
					U32 ms          :  1;       // Metadata Setting
					U32 pi          :  3;       // Protection Information
					U32 pil         :  1;       // Protection Information Location
					U32 ses         :  3;       // Secure Erase Setting
					U32 rsvd        :  20;
					// CDW 11
					U32 rsv4;
					/*	dw12 */
					U32 rsv5;
					/*	dw13 */
					U32 rsv6;
					/*	dw14 */
					U32 rsv7;
					/*	dw15 */
					U32 rsv8;
				} fw_format;

				struct security_rw {
					// CDW 10
					U8  nssf;   // NVMe Security Specific Field (NSSF)
					U16 spsp;   // SP Specific (SPSP0 & SPSP1)
					U8  secp;   // Security Protocol (SECP)

					// CDW 11
					U32 tl; // Transfer Length (TL)
					/*	dw12 */
					U32 rsv5;
					/*	dw13 */
					U32 rsv6;
					/*	dw14 */
					U32 rsv7;
					/*	dw15 */
					U32 rsv8;
				} security_rw;



				struct sanitize {
					// CDW 10
					U32 sanact      : 3; // Sanitize Action
					U32 ause        : 1; // Allow Unrestricted Sanitize Exit
					U32 owpass      : 4; // Overwrite Pass Count
					U32 oipbp       : 1; // Overwrite Invert Pattern Between Passes
					U32 nodealloc   : 1; // No Deallocate After Sanitize
					U32 rsvd        : 22;
					// CDW 11
					U32 ovrpat; // overwrite pattern
					/*	dw12 */
					U32 rsv5;
					/*	dw13 */
					U32 rsv6;
					/*	dw14 */
					U32 rsv7;
					/*	dw15 */
					U32 rsv8;
				} sanitize;

				struct vuc_vpg {
					// CDW 10
					U32 sub_op;
					// CDW 11
					U32 cdw11;
					// CDW 12
					U32 cdw12;
					/*  dw13 */
					U32 rsv6;
					/*  dw14 */
					U32 rsv7;
					/*  dw15 */
					U32 rsv8;
				} vuc_vpg;

			};

		} adm;


		struct io {
			/*	dw0 */
			U32 	opcode			:	8;
			U32 	fuse				:	2;
			U32 	rsv 				:	4;
			U32 	psdt				:	2;
			U32 	cid 				:	16;
			/*	dw1 */
			U32 	nsid;
			/*	dw2 */
			U32 	rsv1;
			/*	dw3 */
			U32 	rsv2;
			/*	dw4 */
			U32 	mptr_L;
			/*	dw5 */
			U32 	mptr_H;
			/*	dw6 */
			//U32 	prp1_L;
			/*	dw7 */
			//U32 	prp1_H;
			/*	dw8 */
			//U32 	prp2_L;
			/*	dw9 */
			//U32 	prp2_H;

			/*	dw6-dw7 */
			union {
				struct io_prp1_64 {
					U64    addr;
				} io_prp1_64;
				struct io_prp1_32 {
					U32    addr_L;
					U32    addr_H;
				} io_prp1_32;
			};
			/*dw8-dw9 */
			union {
				struct io_prp2_64 {
					U64    addr;
				} io_prp2_64;
				struct io_prp2_32 {
					U32    addr_L;
					U32    addr_H;
				} io_prp2_32;
			};


			union {
				struct flush_cmd {
					/*	dw10 */
					U32 	rsv3;
					/*	dw11 */
					U32 	rsv4;
					/*	dw12 */
					U32 	rsv5;
					/*	dw13 */
					U32 	rsv6;
					/*	dw14 */
					U32 	rsv7;
					/*	dw15 */
					U32 	rsv8;
				} flush_cmd;

				struct wzero_cmd {
					/*	dw10 */
					U32 	ulSlbaL;
					/*	dw11 */
					U32 	ulSlbaH;
					/*	dw12 */
					U32 btNlb      : 16; // number of logical blocks
					U32 btRsv1    : 10;
					U32 btPrinfo   : 4; // protection information field
					U32 btFua      : 1; // force unit access
					U32 btLimitedRetry : 1; // limited retry
					/*	dw13 */
					U32 	rsv6;
					/*	dw14 */
					U32 	rsv7;
					/*	dw15 */
					U32 	rsv8;
				} wzero_cmd;

				struct wunc_cmd {
					/*	dw10 */
					U32 	ulSlbaL;
					/*	dw11 */
					U32 	ulSlbaH;
					/*	dw12 */
					U32 btNlb      : 16; // number of logical blocks
					U32 btRsv1    : 16;
					/*	dw13 */
					U32 	rsv6;
					/*	dw14 */
					U32 	rsv7;
					/*	dw15 */
					U32 	rsv8;
				} wunc_cmd;

				struct dsm_cmd {
					/*	dw10 */
					U32 btNumRange  : 8; // number of ranges
					U32 btRsv1               : 24;
					/*	dw11 */
					U32 btAttribute      : 3; // attribute:AD(2):IDW(1):IDR(0)
					U32 btRsv2               : 29;
					/*	dw12 */
					U32 	rsv5;
					/*	dw13 */
					U32 	rsv6;
					/*	dw14 */
					U32 	rsv7;
					/*	dw15 */
					U32 	rsv8;
				} dsm_cmd;

				struct compare_cmd {
					/*	dw10 */
					U32 	ulSlbaL;
					/*	dw11 */
					U32 	ulSlbaH;
					/*	dw12 */
					U32 btNlb      : 16; // number of logical blocks
					U32 btRsv1    : 16;
					/*	dw13 */
					U32 	rsv6;
					/*	dw14 *//*dw15 */
					U64 uoErrSlba; //record first compare error LBA
				} compare_cmd;
			} ;

		} io;

		struct vendor {
			/*	dw0 */
			U32 	opcode				:	8;
			U32 	fuse				:	2;
			U32 	rsv 				:	4;
			U32 	psdt				:	2;
			U32 	cid 				:	16;
			/*	dw1 */
			U32 	nsid;
			/*	dw2 */
			U32 	rsv1;
			/*	dw3 */
			U32 	rsv2;
			/*	dw4 */
			U32 	mptr_L;
			/*	dw5 */
			U32 	mptr_H;
			/*	dw6 */
			U32 	prp1_L;
			/*	dw7 */
			U32 	prp1_H;
			/*	dw8 */
			U32 	prp2_L;
			/*	dw9 */
			U32 	prp2_H;

			union {
				struct usrdef {
					/*	dw10 */
					U32 	num_DW;
					/*	dw11 */
					U32 	num_DW_meta;
					/*	dw12 */
					U32 	ubfeature		: 8;
					U32 	ubsub_feature	: 8;
					U32 	uwrsv7			: 16;
					/*	dw13 */
					U32 	ulRAM_L;
					/*	dw14 */
					U32 	ulRAM_H;
					/*	dw15 */
					U32		ubslotnumber	: 8;
					U32		ubrsv8			: 8;
					U32 	uwCRC			: 16;
				} usrdef;
				struct cacheW {
					/*	dw10 */
					U32 	ulLength;
					/*	dw11 */
					U32 	ulRsv1;
					/*	dw12 */
					U32 	ubFeature		: 8;
					U32 	ubCmdMode		: 2;
					U32 btRawData		: 1;
					U32 ulRsv2			: 21;
					/*	dw13 */
					U32 	ulRAM_L;
					/*	dw14 */
					U32 	ulRAM_H			: 24;
					U32 ubCE			: 8;
					/*	dw15 */
					U32	ubSlotnumber	: 8;
					U32	ubRsv3			: 8;
					U32 	uwCRC			: 16;
				} cacheW;
				struct cacheR {
					/*	dw10 */
					U32 	ulLength;
					/*	dw11 */
					U32 	ulRsv1;
					/*	dw12 */
					U32 	ubFeature		: 8;
					U32 	ubCmdMode		: 2;
					U32 btRawData		: 1;
					U32 ulRsv2			: 21;

					/*	dw13 */
					U32 	ulRAM_L;
					/*	dw14 */
					U32 	ulRAM_H			: 24;
					U32 ubCE			: 8;
					/*	dw15 */
					U32	ubSlotnumber	: 8;
					U32	ubRsv3			: 8;
					U32 	uwCRC			: 16;
				} cacheR;
				struct ReadScanFlashWindow {
					/*	dw10 */
					U32 	ulLength;
					/*	dw11 */
					U32 	ulRsv1;
					/*	dw12 */
					U32 	ubFeature		: 8;
					U32 	ubSubFeature	: 8;
					U32 	uwRsv2			: 16;
					/*	dw13 */
					U32 	ulDataOffset;
					/*	dw14 */
					U32 	ulRsv3;
					/*	dw15 */
					U32	ubSlotnumber	: 8;
					U32	ubRsv4			: 8;
					U32 	uwCRC			: 16;
				} ReadScanFlashWindow;
				struct ScanFlashWindowSet {
					/*	dw10 */
					U32 	ulLength;
					/*	dw11 */
					U32 	ulRsv1;
					/*	dw12 */
					U32 	ubFeature		: 8;
					U32 	ubSubFeature	: 8;
					U32 	uwRsv2			: 16;
					/*	dw13 */
					U32 	ulRsv3;
					/*	dw14 */
					U32 	ulRsv4;
					/*	dw15 */
					U32	ubSlotnumber	: 8;
					U32	ubRsv5			: 8;
					U32 	uwCRC			: 16;
				} ScanFlashWindowSet;
				struct ScanFlashWindowSetPara {
					/*	dw10 */
					U32 	ulLength;
					/*	dw11 */
					U32 	ulRsv1;
					/*	dw12 */
					U32 	ubFeature		: 8;
					union {
						U32 ubSubFeature 		: 8;
						struct SubFeature {
							U32 	btBitScan	: 1;
							U32 ubRsv2		: 3;
							U32 ubDuty		: 4;
						} SubFeature;
					};
					U32 	uwRsv2			: 16;
					/*	dw13 */
					union {
						U32 ulSLBA1;
						struct SLBA1 {
							U16 uwStarOffset;
							U16 uwEndOffset;
						} SLBA1;
					};
					union {
						U32 ulSLBA2;
						struct SLBA2 {
							U8	ubChannelSelect;
							U8	ubBankSelect;
							U8	ubBitScanPos;
							U8	ubRsv3;
						} SLBA2;
					};
					U32 	ulRsv3;
					/*	dw14 */
					U32 	ulRsv4;
					/*	dw15 */
					U32	ubSlotnumber	: 8;
					U32	ubRsv5			: 8;
					U32 	uwCRC			: 16;
				} ScanFlashWindowSetPara;
				struct DetectPeripheral {
					/*	dw10 */
					U32 	ulLength;
					/*	dw11 */
					U32 	ulRsv1;
					/*	dw12 */
					U32 	ubFeature		: 8;
					U32 	ubSubFeature	: 8;
					U32 	uwRsv2			: 16;

					/*	dw13 */
					U32 	ulRsv3;
					/*	dw14 */
					U32 	ulRsv4;
					/*	dw15 */
					U32	ubSlotnumber	: 8;
					U32	ubRsv4			: 8;
					U32 	uwCRC			: 16;
				} DetectPeripheral;
				struct DirectWirteFlash {
					/*	dw10 */
					U32 ulLength;
					/*	dw11 */
					U32 ulRsv1;
					/*	dw12 */
					U32 ubFeature		: 8;
					U32 CmdMode			: 2;
					U32 btRawData		: 1;
					U32 Rsv2			: 21;
					/*	dw13 */
					union {
						U32 ulPCA;
						struct {
							U16 uwNode;
							U16 uwPage;
						};
					};
					/*	dw14 */
					U16 uwBlock;
					U8 ubRsv3;
					U8 ubCE;
					/*	dw15 */
					U16	uwRsv4;
					U16 uwCRC;
				} DirectWirteFlash;
				struct DirectReadFlash {
					/*	dw10 */
					U32 ulLength;
					/*	dw11 */
					U32 ulRsv1;
					/*	dw12 */
					U32 ubFeature	: 8;
					U32	CmdMode		: 2;
					U32 btRawData	: 1;
					U32 btByPass	: 1;
					U32 LDPCMode	: 4;
					U32	uwSeed		: 16;
					/*	dw13 */
					union {
						U32 ulPCA;
						struct {
							U16	uwNode;
							U16	uwPage;
						};
					};
					/*	dw14 */
					U16 uwBlock;
					U8	ubLMU;
					U8	ubCE;
					/*	dw15 */
					U16 uwRsv2;
					U16	uwCRC;
				} DirectReadFlash;
				struct DirectEraseFlash {
					/*	dw10 */
					U32 ulLength;
					/*	dw11 */
					U32 ulRsv1;
					/*	dw12 */
					U32 ubFeature		: 8;
					U32 CmdMode			: 2;
					U32 Rsv2			: 22;
					/*	dw13 */
					U32 ulPCA;
					/*	dw14 */
					U16 uwBlock;
					U8 ubRsv3;
					U8 ubCE;
					/*	dw15 */
					U16	uwRsv4;
					U16 uwCRC;
				} DirectEraseFlash;
				struct NandVerificationTrigger {
					/*	dw10 */
					U32 ulLength;
					/*	dw11 */
					U32 ulRsv1;
					/*	dw12 */
					U32 ubFeature	: 8;
					U32 Rsv2		: 24;
					/*	dw13 */
					U32 ulTransLen;
					/*	dw14 */
					U32 ulWriteCnt	: 8;
					U32 ubRsv3		: 8;
					U32	ubCE		: 8;
					U32	ubRsv4		: 8;
					/*	dw15 */
					U16 uwRsv5;
					U16 uwCRC;
				} NandVerificationTrigger;
				struct NandVerificationWrite {
					/*	dw10 */
					U32 ulLength;
					/*	dw11 */
					U32 ulRsv1;
					/*	dw12 */
					U32	ubFeature	: 8;
					U32 ubType		: 8;
					U32 uwRsv2		: 16;
					/*	dw13 */
					U32 ulTransLen;
					/*	dw14 */
					U32 ulRsv3;
					/*	dw15 */
					U16 uwRsv4;
					U16 uwCRC;
				} NandVerificationWrite;
				struct NandVerificationRead {
					/*	dw10 */
					U32 ulLength;
					/*	dw11 */
					U32 ulRsv1;
					/*	dw12 */
					U32	ubFeature	: 8;
					U32 ubType		: 8;
					U32 uwRsv2		: 16;
					/*	dw13 */
					U32 ulTransLen;
					/*	dw14 */
					U32 ulRsv3;
					/*	dw15 */
					U16 uwRsv4;
					U16 uwCRC;
				} NandVerificationRead;
				struct FlashParameterTable {
					/*	dw10 */
					U32 ulLength;
					/*	dw11 */
					U32 ulRsv1;
					/*	dw12 */
					U32 ubFeature		: 8;
					U32 ubSubFeature	: 8;
					U32 uwRsv2			: 16;
					/*	dw13 */
					U32 ulSectorOffset;
					/*	dw14 */
					U16 uwRsv3;
					U8	ubDie;
					U8	ubCE;
					/*	dw15 */
					U16	uwRsv4;
					U16 uwCRC;
				} FlashInfo;
				struct GetRDTLog {
					/*	dw10 */
					U32 	ulLength;
					/*	dw11 */
					U32 	ulRsv1;
					/*	dw12 */
					U32 	ubFeature		: 8;
					U32 	ubSubFeature	: 8;
					U32 	uwRsv2			: 16;
					/*	dw13 */
					U32 	ulPageOffset;
					/*  dw14 */
					U32		ulSectorOffset;
					/*	dw15 */
					U32		uwRsv3	: 16;
					U32 	uwCRC	: 16;
				} GetRDTLog;
				struct ISPFlash {
					/*	dw10 */
					U32 ulLength;
					/*	dw11 */
					U32 ulRsv1;
					/*	dw12 */
					U8 ubFeature;
					union {
						U8 ubSubFeature;
						struct {
							U8 btType		: 1;
							U8 btScrambled	: 1;
							U8 btCommit		: 1;
							U8 btRsv2		: 1;
							U8 Slot			: 3;
							U8 btMode		: 1;
						};
					};
					U8 EncryptMethod	: 4;
					U8 Rsv3				: 4;
					U8 ubRsv4;
					/*	dw13 */
					U32 ulOffset;
					/*	dw14 */
					U32 ulTotalSize;
					/*	dw15 */
					U8 FWSlots			: 4;
					U8 Rsv5				: 4;
					U8 ubRsv6;
					U16 uwCRC;
				} ISPFlash;
				struct ReadWriteSram {
					/*	dw10 */
					U32 ulLength;
					/*	dw11 */
					U32 ulRsv1;
					/*	dw12 */
					U32 ubFeature		: 8;
					U32 ubSubFeature	: 8;
					U32 uwRsv2			: 16;
					/*	dw13 */
					U32 ulRamAddr;
					/*	dw14 */
					U32 ulRsv3;
					/*	dw15 */
					U16	uwRsv4;
					U16 uwCRC;
				} ReadWriteSram;
				struct FlashFeatureConfigure {
					/*	dw10 */
					U32 ulLength;
					/*	dw11 */
					U32 ulRsv1;
					/*	dw12 */
					U32 ubFeature		: 8;
					U32 ubSubFeature	: 8;
					U32 uwRsv2			: 16;
					/*	dw13 */
					U32	ubMode			: 8;
					U32	Rsv3			: 24;
					/*	dw14 */
					U32 Rsv4			: 24;
					U32	ubCE			: 8;
					/*	dw15 */
					U16	uwRsv5;
					U16 uwCRC;
				} FlashFeatureConfigure;
				struct GetVUCDataSize {
					/*	dw10 */
					U32 ulLength;
					/*	dw11 */
					U32 ulRsv1;
					/*	dw12 */
					U8	ubFeature;
					U8	ubSubFeature;
					U16 uwRsv2;
					/*	dw13 */
					U8	ubRCmdSubFeature;
					U8	ubRCmdFeature;
					U16	uwRsv3;
					/*	dw14 */
					U32 ulRsv4;
					/*	dw15 */
					U16	uwRsv5;
					U16 uwCRC;
				} GetVUCDataSize;
				struct ISPReadPRAM {
					/*	dw10 */
					U32 ulLength;
					/*	dw11 */
					U32 ulRsv1;
					/*	dw12 */
					U8	ubFeature;
					U8	ubSubFeature;
					U16 uwRsv2;
					/*	dw13 */
					U32	ulRsv3;
					/*	dw14 */
					U32 ulRsv4;
					/*	dw15 */
					U16	uwRsv5;
					U16 uwCRC;
				} ISPReadPRAM;
				struct ISPReadROM {
					/*	dw10 */
					U32 ulLength;
					/*	dw11 */
					U32 ulRsv1;
					/*	dw12 */
					U8	ubFeature;
					U8	ubSubFeature;
					U16 uwRsv2;
					/*	dw13 */
					U32	ulRsv3;
					/*	dw14 */
					U32 ulRsv4;
					/*	dw15 */
					U16	uwRsv5;
					U16 uwCRC;
				} ISPReadROM;
				struct NandVerifyTrigger {
					/*	dw10 */
					U32 ulLength;
					/*	dw11 */
					U32 ulRsv1;
					/*	dw12 */
					U8	ubFeature;
					U8	ubSubFeature;
					U16 uwRsv2;
					/*	dw13 */
					U32	ulTransferLengh;
					/*	dw14 */
					U8	ubWriteUninCnt;
					U8	ubRsv3;
					U8	ubCE;
					U8	ubRsv4;
					/*	dw15 */
					U16	uwRsv5;
					U16 uwCRC;
				} NandVerifyTrigger;
				struct ReadWrtieReg {
					/*	dw10 */
					U32 ulLength;
					/*	dw11 */
					U32 ulRsv1;
					/*	dw12 */
					U8	ubFeature;
					U8	ubSubFeature;
					U16 uwRsv2;
					/*	dw13 */
					U32	ulRegAddr;
					/*	dw14 */
					U32	ulRsv3;
					/*	dw15 */
					U16	uwRsv4;
					U16 uwCRC;
				} ReadWriteReg;
				struct VUCDownloadImage {
					/*	dw10 */
					U32 ulLength;
					/*	dw11 */
					U32 ulRsv1;
					/*	dw12 */
					U8	ubFeature;
					U8	ubSubFeature;
					U16 uwRsv2;
					/*	dw13 */
					U32	ulRsv3;
					/*	dw14 */
					U32	ulOffset;
					/*	dw15 */
					U32	ulRsv4;
				} VUCDownloadImage;
				struct VUCCommit {
					/*	dw10 */
					U32 ulLength;
					/*	dw11 */
					U32 ulRsv1;
					/*	dw12 */
					U8	ubFeature;
					U8	ubSubFeature;
					U16 uwRsv2;
					/*	dw13 */
					U32	FS: 3;
					U32 CA: 3;
					U32 : 26;
					/*	dw14 */
					U32	ulRsv3;
					/*	dw15 */
					U32	ulRsv4;
				} VUCCommit;
			};
		} vendor;
	};
};


typedef enum  {
	UNINIT,  //0
	SETUP_HCMD_DONE,  // 1
	WAIT_FW_LOAD_INFO, // 2
	WAIT_FW_LOAD_INFO_PROCESS, // 3
	FW_LOAD_INFO_DONE, // 4
	INITDONE, // 5
	INSERT_DEP_TABLE, // for sync read, write command //6
	ALLOCRESOURCE, // 7
	ALLOCRESOURCE_WAIT,  // 8
	ALLOCRESOURCE_DONE, // 9
	TRIGDMA, // 10
	WAIT_DATA, //11
	WAIT_TD, //12
	GET_TD, // 13
	CMD_ERROR, // 14 (0xE)
	WCHERR, //for write cache transport to Dbuf error and FW  retry fail case // 15
	RD_ABORT, // 16
	WAIT_FW_PROCESS, // 17
	FW_PROCESS_SYNC_CMD, // 18
	FW_PROCESS_LIBRARY_PAUSE,  // 19
	FW_PROCESS_DONE, // 20
	PROCESS_4K_DONE, // 21
	FREERESOURCE, // 22
	PROCESS_ALL_DONE, // 23
	WAIT_FW_SAVE_INFO, // 24
	WAIT_FW_SAVE_INFO_PROCESS, // 25
	FW_SAVE_INFO_DONE, // 26
	CMD_STATE_CMD_CPL, // 27
	CMD_STATE_FINISH, // 28
	FINISH, // 29 (0x1D)
} nvme_cmd_state_mathinc;


//mechanical

typedef enum {
	NSIDFY,
	IDFY,
	FEAT,
	SMART,
	ERRLOG,
	FWSLOT,
	NSLIST,
	DEVSELF,
	TELEMETRYLOG,
	SANI,
	D2H,
	FWDL,
	HOSTPRO,
} nvm_mem_addr_type;

typedef enum {
#if (BURNER_MODE_EN || RDT_MODE_EN)
	BURNERINIT,
#endif /* (BURNER_MODE_EN || RDT_MODE_EN) */
	HOSTINIT,//burrner mode -performat
	HOSTLOAD, //load host info to dbuf.
	HOSTRESET, //function level reset
	CNTLRESET, //controller level reset
	LPMRESET, //LPM
	REBOOTINIT, //reboot to clear some host parameter
	INITFAIL // Scan VT fail
} nvm_host_rmode;

typedef enum {
	HOST_DBUF_NO_STATE,
	HOST_DBUF_HAVE_STATE,
	HOST_DBUF_LOAD,
	HOST_DBUF_SAVE,
} nvm_host_dbuf_mode;

typedef enum {
	HOST_DBUF_ID0,  //identify,log
	HOST_DBUF_ID1,  //feature,hmb..
	HOST_DBUF_ID2,	 //
} nvm_host_dbuf_id;

typedef enum {
	HOST_DBUF_4K = 1,
	HOST_DBUF_8K = 2,
	HOST_DBUF_12K = 3,
} nvm_host_dbuf_size;

typedef union nvme_sts           NVME_STS, *NVME_STS_PTR;

union nvme_sts {

	U32 ulDW[1];

	struct {
		U8 ubSC;    // Status Code in Completion Status Field
		U8 ubSCT         ;    // Status Code Type in Completion Status Field
		U8 ubERRByte;
		U8 ubERRBit;
	};
};


typedef struct nvme_HCMD_op             OPT_HCMD, *OPT_HCMD_PTR, *OPT_HCMD_PTR_t;
struct nvme_HCMD_op {
	U32                		  valid               :   1;
	U32                	      btIsBufCmd            :   1;
	U32                       ubIsHostCmdInSPOR           : 4;
	U32                	      rsv                 :   2;
	U32                       state               :   8;
	U32                       cur_sct                :   3;
	U32                       cur_sc                :   8;
	U32                       trigdma_err              :   1;
	U32                       io_parState             :   4;//IO FW parser result
	U32                       cmd_err_byte          :   8;//error log command err byte
	U32                       cmd_err_bit             :   8;//error log command err bit
	U32                       cmd_err_specific   :   16;//error log command specific
	U32                       cmd_cq_specific;           //for cq dword0
	U32                       total_LCA;
	U32                       done_LCA;
	U32                       total_Byte_Cnt;
	U32                       done_Byte_Cnt;
	U32                       cur_LBID;
	U32                       cur_LBOFFSET;
	U32                       cur_phyMemAddr;
	NVMEHCMD                  hcmd;
	NVME_SQ_CMD               nvme_sqcmd;
};

typedef volatile union nvme_rcq		*REGRCQ_PTR_t;
union nvme_rcq {

	U32 ulDW[4];

	struct {

		U32 LCA            ;

		U32 ubRSV0      : 1;
		U32 ubCONT      : 1;
		U32 ubAlign4K   : 1;
		U32 ubBarrier   : 1;
		U32 ubCmdHead   : 1;
		U32 ubFUA       : 1;
		U32 ubPrdClr    : 1;
		U32 ubLR        : 1;
		U32 ubVldBit    : 8;
		U32 ubCtag      : 7;
		U32 ubCmdEnd    : 1;
		U32 ubRCQOffset : 8;

		U32 ulPCA          ;

		U32 ubShDone    : 1;
		U32 ubInvalid   : 1;
		U32 ubLOC       : 2;
		U32 ubLOC2      : 2;
		U32 ubWLBFull   : 1;
		U32 ubXZIPErr   : 1;
		U32 ubCop1Err   : 3;
		U32 ubRsv       : 4;

		U32 ubPBAddr    : 9;
		U32 ubRCQOfst   : 8;
	};
};

union nvme_hostevt {

	U32 ulDW;

	struct {
		// PCIE evt
		U32 bPerstF     : 1;
		U32 bPerstR     : 1;
		U32 bLinkF      : 1;
		U32 bLinkR      : 1;
		U32 bFLR        : 1;
		U32 bRsv0       : 3;

		// APU evt
		U32 bAPUWrFlushErr : 1;
		U32 bAPUCmdAbotrINT: 1;
		U32 bAPUErrRdCmdAbotr: 1;
		U32 bRsv2       : 5;

		// NVME evt
		U32 bInvSQDBVal : 1;
		U32 bInvCQDBVal : 1;
		U32 bWr2InvSQDB : 1;
		U32 bWr2InvCQDB : 1;
		U32 bCCEN_1to0  : 1;
		U32 bCCEN_0to1  : 1;
		U32 bNSSR       : 1;
		U32 bAbrSht     : 1;
		U32 bNormSht    : 1;
		U32 bSQDel      : 1;
		U32 bCQDel      : 1;
		U32 bRsv1       : 5;
	};
};

#define NVME_LAYER_ARBITRATION_BURST (0)
#define NVME_HOSTCMD_BUF_SIZE 	((MAX_CTAG_NUMBER < ((2 << NVME_LAYER_ARBITRATION_BURST)* 9))?MAX_CTAG_NUMBER:((2 << NVME_LAYER_ARBITRATION_BURST)* 9))   // 8 IO Queue and 1 Admin Queue
typedef struct nvme_HCMD_buf    NVME_HCMD_BUF;
struct nvme_HCMD_buf {
	U8              ubCmdCnt;
	U8              ubCmdIdx;
	U32				ulFcf[NVME_HOSTCMD_BUF_SIZE];
	NVMEHCMD		hcmd[NVME_HOSTCMD_BUF_SIZE];
};

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////




//typedef struct copy_para        CPYPARA, *PCPYPARA;
//typedef struct nvme_ns          NVMENS, *PNVMENS;
//typedef struct nvme_qpb         NVMEQPB, *PNVMEQPB;
//typedef struct nvme_feature     NVMEFEAT, *PNVMEFEAT;
//typedef struct nvme_log         NVMELOG, *PNVMELOG;
//typedef union nvme_nlsts        NVMENLSTS, *PNVMENLSTS;

//typedef union nvme_nlssr        NVMENLSSR, *PNVMENLSSR;
//typedef union nvme_nliosts      NVMENLIOSTS, *PNVMENLIOSTS;
//typedef union nvme_cmdhead      NVMECHEAD, *PNVMECHEAD;
//typedef union nvme_cmddw0       NVMECDW0, *PNVMECDW0;


#define HCPLTAG(x)      ((U32)(x) & 0xFF)
#define HCPLSC(x)       (((U32)(x) & 0xFF) << 16)
#define HCPLSCT(x)      (((U32)(x) & 0x07) << 24)


enum contr_id {
	CONTR0,
	CONTR1,
};

enum adm_cmd {
	DELETE_SQ,        //0x0
	CREATE_SQ,       //0x1
	GET_LOG,           //0x2
	DELETE_CQ = 4, //0x4
	CREATE_CQ,      //0x5
	IDENTIFY,         //0x6
	ABORT = 8,       //0x8
	SET_FEATURE,  //0x9
	GET_FEATURE,   //0xA
	ASYNC_REQ = 0xC, //0xC
	NS_MGMT,            //0xD
	FW_COMMIT = 0x10,  //0x10
	FW_DL,                  //0x11
	DEV_SELF_TEST = 0x14, //0x14
	NS_ATT = 0x15,         //0x15
	KEEP_ALIVE = 0x18,  //0x18
	DIR_SEND = 0x19,
	DIR_RECEIVE = 0x1A,
	OVER_FABRICS = 0x7F,
	FORMAT_NVME = 0x80,
	SECRUITY_SEND = 0x81,
	SECURITY_RECEIVE = 0x82,
	SANITIZE = 0x84,
	//vendor specific 0xC0
	VENDOR_MICRON_VS_INPUT = 0xC1,
	VENDOR_MICRON_VS_OUTPUT = 0xC2,
	VENDOR_VPG = 0xCF,
	VENDOR_NODATA = 0xD0,
	VENDOR_WRITEDATA = 0xD1,
	VENDOR_READDATA = 0xD2,
	VENDOR_ENCRYPT_NO_DATA = 0xE0,
	VENDOR_ENCRYPT_WRITE_DATA = 0xE1,
	VENDOR_ENCRYPT_READ_DATA = 0xE2,
	VENDOR_KINGSTON_VERIFY_ANTIFAKE_NO_DATA = 0xE4,
	VENDOR_KINGSTON_VERIFY_ANTIFAKE_WRITE_DATA = 0xE5,
	VENDOR_KINGSTON_VERIFY_ANTIFAKE_READ_DATA = 0xE6,
	VENDOR_POWER = 0xF4,
};

enum nvm_cmd {
	NVME_FLUSH,
	NVME_WRITE,
	NVME_READ,
	NVME_WRUNC = 4,
	NVME_COMP,
	NVME_WRZERO = 8,
	NVME_DATASET,
	NVME_RESV_REGISTER = 0xD,
	NVME_RESV_REPORT = 0xE,
	NVME_RESV_ACQUIRE = 0x11,
	NVME_RESV_RELEASE = 0x15,
	NVME_RESV = 0xFF,
};

enum nvme_sct {
	GENERIC_CMD,
	CMD_SPEC,
	INTEGRITY_ERR,
};

enum nvme_sc {
	SUCCESS_CPL,
	INVALID_OPC,
	INVALID_FIELD,
	CID_CONFLICT,
	TRANSFER_ERR,
	POWER_LOSS,
	INTERNAL_ERR,
	ABORT_REQ,
	SQ_DELETION,
	FAILED_FUSED,
	MISSING_FUSED,
	INVALID_NS,
	CMDSEQ_ERR,
	INVALID_SGLDES,
	INVALID_SGLNUM,
	DATASGL_INVALID,
	METASGL_INVALID,
	SGLTYPE_INVALID,
	INVALID_CMB,
	PRP_INVALID,
	AWU_EXCEEDED,
	OPERATION_DENIED,//0x15
	SGL_INVALID,
	HOST_IIF = 0x18,
	KEEP_ALIVE_EXPIRED,
	KEEP_ALIVE_INVALID,
	CMD_ABORT_PREEMPT_ABORT,
	SZNITIZE_FAILED = 0x1C,
	SZNITIZE_IN_PROGRESS,
	SGL_BLICK_INVALID,
	COM_NOT_SUPP_CMB,
};

enum nvme_nvm_sc {
	LBA_OUT_RANGE = 0x80,
	EXCEEDED_CAP,
	NS_NRDY,
	RES_CONFLICT,
	FORMATTING,
};

enum nvme_CMD_Specific_sc {
	CQ_INVALD,  //0
	INVALD_Q_ID,
	INVALD_Q_SIZE,
	ABORT_CMD_LMT_EXCEED,
	CMD_SPECIFIC_RSVD,   //4
	ASYNC_REQ_LMT_EXCEED,
	INVALD_FW_SLOT,
	INVALD_FW_IMG,
	INVALD_INT_VECTOR,
	INVLAD_LOG_PAGE,   //9
	INVALD_FMT,
	FW_ACT_NEED_CONVENTIONAL_RST,
	INVALD_Q_DEL,
	FID_NOT_SAVABLE,  //D
	FEATURE_NOT_CHANGEABLE,
	FEATURE_NOT_NS_SPECIFIC,
	FW_ACT_NEED_NSSR, //0x10
	FW_ACT_NEED_RST,
	FW_ACT_NEED_MAX_TIME_VIOLATION,
	FW_ACT_PROHIB,
	OVERLAP_RANGE,  // 0x14
	NS_NOT_ENOUGH_SPACE,
	NS_ID_NOT_AVAILABLE,
	CMD_SPECIFIC_RSVD2, //0x17
	NS_ALREADY_ATTACHED,
	NS_IS_PRIVATE,
	NS_NOT_ATTACHED,
	THIN_PROVISION_NOT_SUPPORT,
	CONTROLLER_LST_INVALD,   //0x1C
	DST_IN_PROGRESS,  //0x1D
	BOOT_PARTITION_WRITE_PROHIBITED //0x1E
};

enum nvme_CMD_Specific_NVM_sc {
	CONFLICTING_ATTR = 0x80,
	INVALD_PROTECT_INFO,
	ATTEMPTED_WR_2_RD_ONLY_RNG
};

enum nvme_Intergrity_NVM_sc {
	WRITE_FAULT = 0x80,
	UNC_READ_ERR,
	E2E_GUARD_CHK_ERR,
	E2E_APP_TAG_CHK_ERR,
	E2E_REF_TAG_CHK_ERR,
	CMP_FAILURE,
	ACCESS_DENIED,
	DEALLOCAT_UNWRITTEN_LBA
};

enum nvme_SHN_state {
	NO_NOTIFY,
	NORM_SHN,
	ABRUPT_SHN,
	RESERVE,
};


//save identify ns struce info type
enum nvme_NS_mode {
	NS_CREATE,
	NS_DELETE,
	NS_FORMAT,
};

//NS management select 0:create,1:delete
enum nvme_NM_sel {
	NM_CREATE,
	NM_DELETE,
};

enum nvme_att {
	CTR_ATTACH,
	CTR_DETACH,
};

//parser CMGR table command type
enum cmgr_cmd_type {
	CMGR_IO,
	CMGR_ADM,
};

//parser CMGR table opcode
enum cmgr_opc_type {
	CMGR_NCHK_OPC,
	CMGR_CHK_OPC,
};

//for asyn even

typedef struct nvme_host_isr_if    HOST_ISR_IF_T, * HOST_ISR_IF_PTR;

struct nvme_host_isr_if {
	U16    invld_cqdb_val; //invaild doorbell
	U16    invld_sqdb_val;
	U16    invld_cqdb_reg;//not exit doorbell
	U16    invld_sqdb_reg;
	U32    invld_internal_err;
	U32    invld_internal_cmgr_err;
	U8     invld_flag   : 1;
	U8     rsvd1         : 6;
	U8     ctrl_fatal_err: 1;
};

typedef union {

	U8 ubAll[12];

	struct {
		U32 ulErrNs;
		U16 uwErrSQId;
		U16 uwErrCmdId;
		U8  ubCtag;
		U8  btValid  : 1;
		U8  Reserved : 7;
	};

} NVMECCENErrorLog_t;

#pragma pack(pop)

#ifdef _SHR_NVME_LIB_C_
HOST_ISR_IF_T gHostIsr;
#if (HOST_MODE == NVME)
OPT_HCMD nvme_cur_HCMD;
NVME_DMA_ERR_ST ctag_DMA_err[MAX_CTAG_NUMBER];
NVME_HCMD_BUF gNvmeHcmdBuf;
#endif /* (HOST_MODE == NVME) */
#else
extern HOST_ISR_IF_T gHostIsr;
extern OPT_HCMD nvme_cur_HCMD;
extern NVME_DMA_ERR_ST ctag_DMA_err[MAX_CTAG_NUMBER];
extern NVME_HCMD_BUF gNvmeHcmdBuf;
#endif
extern NVMECCENErrorLog_t gNVMECCENErrorLog;
//~
extern void nvme_db_init(void);
//EXTERN void nvme_create_ns(U64 NS_Size, U8 Nsid, U8 LBAF, U8 WP);
//EXTERN void nvme_create_ns(U64 NS_Size, U8 Nsid, U8 LBAF, U8 dps,U8 nmic,U8 ubmode);
AOM_HOST_RESET extern void nvme_create_ns (U64 uoNsSize, U8 Nsid, U8 ubLbaf, U8 ubDps, U8 ubNmic, U8 ubmode);

AOM_NRW_2 extern void nvme_delete_ns(U8 Nsid);
AOM_HOST_RESET extern U8 nvme_get_csts_value_nssro (void);
AOM_HOST_RESET extern void nvme_set_csts_value_nssro (U8 ubNSSRO);
AOM_HOST_RESET extern void nvme_set_csts_value_shst (U8 ubStatus);
AOM_HOST_RESET extern void nvme_set_csts_value_pp (U8 ubStatus);
AOM_HOST_RESET extern void nvme_ccen_1to0(U8 ubHostInfoReady);
AOM_HOST_RESET extern void nvme_ccen_0to1(void);
AOM_HOST_RESET extern void nvme_qpb_cq_delete(U8 ubCQID);
AOM_HOST_RESET extern void nvme_qpb_sq_delete(U8 ubSQID);

AOM_COMMON extern void nvme_bar0_init(void); //could be called in interrupt
AOM_HOST_RESET extern void nvme_cmgr_ctrl_init(void);
AOM_HOST_RESET extern void nvme_ctrl_init(U8 ubHostInfoReady);
extern void nvme_get_hcmd(PNVMEHCMD pCMD);
//EXTERN void nvme_dispatch_cmd(PNVMEHCMD pCmd);
extern void NVME_CMGR_INT_HANDLER(void);

AOM_INIT extern void nvme_send_success(CTAG_t CTAG);
extern void nvme_send_err_cq (CTAG_t CTAG);
extern void nvme_send_cq_completion(CTAG_t CTAG, U8 ubSCT, U8 ubSC, U32 ulCmdSpecific, U8 ubMore, U8 ubDoNotRetry, U32 *pErrInfo);   //Callback function flow
//EXTERN void nvme_send_cq_entry(U8 ubCTAG, U8 ubSCT, U8 ubSC, U32 ulCmdSpecific, U8 ubMore, U8 ubDoNotRetry);
AOM_NRW_3 extern void nvme_qpb_cq_insert(U16 ubCqID, U8 ubIEN, U64 ullBase, U16 ubCqIV, U16 uwCqSize);

AOM_NRW_3 extern void nvme_qpb_sq_insert(U16 ubSqID, U8 ubPRI, U64 ullBase, U16 ubCqID, U16 uwSqSize);

AOM_NRW extern void issue_wch(U32 ubCtag, U32 ubCmdEnd, U32 ubBCMD, U32 ubFT, U32 ubNBMU, U32 ubACPL, U32 ubType, U32 ulCNLC, U32 ulPbAddr, U32 ulLBID, U32 ulLBOffset, U32 ulTNLC);


void PCIESendLTR(U32 ulLTR);
AOM_HOST_ERROR extern void nvme_err_handle(OPT_HCMD_PTR pCmd, U32 ulErr);
//extern void nvme_fill_smart_data(U32 ulBufAddr);
extern void nvme_check_error(void);

extern void nvme_event_handler(void);

AOM_NRW extern U32 nvme_htd_handle(void);
//extern U32 cal_CRC24(U32 src_addr, U64 LBA);

AOM_HOST_RESET void nvme_var_init(void);
AOM_NRW extern void nvme_setupHCMD(REGHCMD_PTR pCmd, OPT_HCMD_PTR curCmdSlot);
AOM_HOST_ERROR extern U8 nvme_setupHCMD_to_buf(REGHCMD_PTR pCmd);
AOM_NRW extern U8 pop_hcmd_buf_cmd(OPT_HCMD_PTR pCmd);

AOM_NRW extern void nvmeHCMD_handler(OPT_HCMD_PTR pCmd);
AOM_NRW void nvme_set_HCMD_done(OPT_HCMD_PTR pCmd);
extern void nvme_admIO_setup(VUC_OPT_HCMD_PTR pCmd, U32 ulSize);
AOM_NRW extern U8 adm_malloc(OPT_HCMD_PTR pCmd, U32 ulSize,  U32 *pMemAddr, U32 *LBID, U32 *LBOFST);
AOM_NRW extern U8 nvme_admIO_trig(OPT_HCMD_PTR pCmd, U32 pMemAddr, U32 LBID, U32 LBOFST, U8 ubDir, U8 ubBCMD);


AOM_HOST_RESET extern void nvme_attach_ns(U8 ubNSID, U8 ubAttach);
AOM_NRW extern U8 is_trig_CMGR_lock_cmd(REGHCMD_PTR pCmd);
AOM_NRW extern U8 is_clear_CMGR_lock_on_cmd_done (REGHCMD_PTR pCmd);
AOM_NRW extern U8 is_trig_dispatch_lock_cmd(REGHCMD_PTR pCmd);
extern void errTD_trans_to_nvme_SCT_SC(CTAG_t CTAG, U8 *ubSCT, U8 *ubSC); //Callback function flow
extern U32 nvme_check_ns_cnt(void);

/*
 * Function : nvme_find_empty_ns
 * Function : nvme_count_ns_size
 *
 * Originally in AOM_NRW_2 (overlay 4).  These functions are only called by
 * nvme_ns_mang_check_status() which in AOM_NRW (overlay 3).  To save 20 bytes
 * in ATCM_1, move to AOM_NRW (overlay 3).
 */
AOM_NRW extern U32 nvme_find_empty_ns(void);
AOM_NRW extern U64 nvme_count_ns_size(void);

AOM_NRW_2 extern U8 nvme_search_nsid_exist(U32 uldeleteNs);
AOM_NRW_2 void nvme_set_arb_api (U8 ubAB, U8 ubLPW, U8 ubMPW, U8 ubHPW);

/*
 * Function : nvme_get_arb_api
 *
 * Originally in AOM_NRW_2 (overlay 4).  This function is only called by
 * nvme_get_feature() which in AOM_NRW (overlay 3).  The size of this
 * function is 2 bytes.  To save 20 bytes in ATCM_1, move to AOM_NRW
 * (overlay 3).
 */
AOM_NRW void nvme_get_arb_api(void);

AOM_NRW_2 void nvme_set_int_coa_ctrl_api (U8 ubTime, U8 ubThr);
AOM_NRW_2 void nvme_get_int_coa_ctrl_api (void);
AOM_NRW_2 void nvme_set_int_coa_dis_api (U8 ubDis, U8 ubIv, U8 ubMess);
AOM_NRW_2 U32 nvme_get_mdts_max_size_byte_api (void);
AOM_NRW_2 void nvme_fill_nonalign_part(OPT_HCMD_PTR pcmd);
AOM_TCG_INIT U64 nvme_calculate_last_D2H_align_addr(OPT_HCMD_PTR pcmd);
AOM_NRW extern void nvme_mfree_hal_api(OPT_HCMD_PTR pCmd, U32 ulSize);
AOM_NRW extern U16 nvme_parser_cmgr_table_cnt(U8 ubAdminCom, U8 ubOpcode, U8 ubParMode, U32 ulNSID);
AOM_NRW_2 extern void nvme_set_cmd_sync_mode(OPT_HCMD *pulCurrentCMD);
AOM_NRW extern U8 nvme_async_trigger_cpl(U16 uwCID, U8 ubSCT, U8 ubSC, U32 ulCmdSpecific, U8 ubMore, U8 ubDoNotRetry);
AOM_NRW extern void nvme_async_cmgr_clear_ctag(OPT_HCMD_PTR pCmd);
AOM_NRW extern void nvme_malloc_dbuf_api (OPT_HCMD_PTR pCmd, U32 ulSize, U8 ubDir, U8 ubBCMD) ;
extern void nvme_change_state_api (nvme_cmd_state_mathinc ubstate);
AOM_NRW extern void nvme_change_io_parser_state_api (U8 ubstate);
AOM_LPM U8 nvme_check_outstanding_CMD_in_host_side(void);
AOM_NRW void nvme_fw_commit (OPT_HCMD_PTR pCmd);
AOM_NRW_2 void nvme_fw_img_download (OPT_HCMD_PTR pCmd);
AOM_NRW void nvme_format (OPT_HCMD_PTR pCmd);
AOM_NRW U8 NVMEAdminWriteProtectCheck(OPT_HCMD_PTR pCmd);
U8 NVMEInitinfoLockCheck(OPT_HCMD_PTR pCmd);
AOM_HOST_SPOR U8 NVMEAdminHostCmdInSPORCheck(OPT_HCMD_PTR pCmd);
#if (PS5021_EN)
U8 hal_nvme_get_fun_q_entry_number(U8 ubFID, U8 ubQid);
#endif /* (PS5021_EN) */
#endif /* _SHR_NVME_LIB_H_ */
