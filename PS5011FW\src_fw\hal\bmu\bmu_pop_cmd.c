#include "setup.h"
#include "typedef.h"
#if VS_SIM_EN
#include "ip/Bmu/Bmu.h"
#endif /* VS_SIM_EN */
#include "hal/db/db_api.h"
#include "hal/bmu/bmu_api.h"
#include "db_mgr/fw_cmd_table.h"
#include "db_mgr/fw_tagid.h"
#include "ftl/ftl.h"

#if RDT_MODE_EN
#include "rdt/rdt_api.h"
#endif

typedef void (* bmu_cmd_doing_func_t)(BMUCmdResult_t *BmuCmdStatus);
//TODO: pointer

void BMUDelegateCmd(void)
{
#if(!BURNER_MODE_EN)
	U16 uwTagId;
	U8 ubOpCode;
	BMUCmdResult_t *pBMUCmdResult = NULL;	//using new BMU Result structure to point to CQ

#if VS_SIM_EN
	//using old BMU Result structure to point to CQ
	BMUCmdResult_t BMUCmdResult;	//for transfer old/new structure
	BmuCmdStatus_t *pBmuCmdStatus;
#endif /*VS_SIM_EN*/

	bmu_cmd_doing_func_t call_back = NULL;
	while (!M_DB_CHECK_EMPTY(DB_BMU_CQ)) {
		pBMUCmdResult = (BMUCmdResult_t *)M_DB_GET_QBODY_PTR( DB_BMU_CQ, gDBQueueCnt.B.uwBMUCQCnt);
		ubOpCode = (U8)pBMUCmdResult->BMURst.ubOpcode;
#if (RDT_MODE_EN)
		if (gubRDTBMUTest) {
			guwRDTProgPBAddress = pBMUCmdResult->BMUAllocateRst.uwPBAddress;
			guwRDTProgLBOffset = pBMUCmdResult->BMUAllocateRst.uwLBOffset;
			gubRDTLBID = pBMUCmdResult->BMUAllocateRst.ubLBID;
		}
#endif
		//BMUCmdResult.All = pBMUCmdResult->All;
#if VS_SIM_EN
		pBmuCmdStatus = (BmuCmdStatus_t *)M_DB_GET_QBODY_PTR( DB_BMU_CQ, gDBQueueCnt.B.uwBMUCQCnt);

		ubOpCode = pBmuCmdStatus->CmdSts_t.ubOpCode;

		memset(&BMUCmdResult, 0, sizeof(BMUCmdResult_t));
		if (ubOpCode == BMU_ALLOCATE_CMD) {
			BMUCmdResult.BMUAllocateRst.ubOpcode = pBmuCmdStatus->Allocate_Sts_t.ubOpCode;
			BMUCmdResult.BMUAllocateRst.ubResult = pBmuCmdStatus->Allocate_Sts_t.ubStatus;
			BMUCmdResult.BMUAllocateRst.ubTargetID = pBmuCmdStatus->Allocate_Sts_t.ubSrcTagID;
			BMUCmdResult.BMUAllocateRst.btOption = pBmuCmdStatus->Allocate_Sts_t.ubRule;
			BMUCmdResult.BMUAllocateRst.btAF = pBmuCmdStatus->Allocate_Sts_t.ubAutoFreeIfFail;
			BMUCmdResult.BMUAllocateRst.ubTimeout = pBmuCmdStatus->Allocate_Sts_t.ubTimeoutValue;
			BMUCmdResult.BMUAllocateRst.btFUA = pBmuCmdStatus->Allocate_Sts_t.ubFUA;
			BMUCmdResult.BMUAllocateRst.btNoCQ = (!((pBmuCmdStatus->Allocate_Sts_t.uwTagID & 0x8000) >> 15));
			BMUCmdResult.BMUAllocateRst.ubTagID = (U8)pBmuCmdStatus->Allocate_Sts_t.uwTagID;
			BMUCmdResult.BMUAllocateRst.uwPBAddress = pBmuCmdStatus->Allocate_Sts_t.LBNA.B.uwPoolOffset;
			BMUCmdResult.BMUAllocateRst.uwLBOffset = pBmuCmdStatus->Allocate_Sts_t.uwLogicalBufferOffset;
			BMUCmdResult.BMUAllocateRst.ubLBID = (pBmuCmdStatus->Allocate_Sts_t.ubLogicalBufferID & 0x07);
			//BMUCmdResult.BMUAllocateRst.ubCTag = pBmuCmdStatus->Allocate_Sts_t.;
			BMUCmdResult.BMUAllocateRst.ulLCA = pBmuCmdStatus->Allocate_Sts_t.ulLCA;
			BMUCmdResult.BMUAllocateRst.ubDoneCount = pBmuCmdStatus->Allocate_Sts_t.ubSize;
			BMUCmdResult.BMUAllocateRst.ubStreamID = (pBmuCmdStatus->Allocate_Sts_t.uwStreamID & 0xF);
		}
		else if (ubOpCode == BMU_ALLOCATE_PB_CMD) {
			BMUCmdResult.BMUAllocatePBRst.ubOpcode = pBmuCmdStatus->AllocatePB_Sts_t.ubOpcode;
			BMUCmdResult.BMUAllocatePBRst.ubResult = pBmuCmdStatus->AllocatePB_Sts_t.ubStatus;
			BMUCmdResult.BMUAllocatePBRst.ubTargetID = pBmuCmdStatus->AllocatePB_Sts_t.ubSrcTagID;
			BMUCmdResult.BMUAllocatePBRst.btOption = (pBmuCmdStatus->AllocatePB_Sts_t.ubRule & BIT0);
			//BMUCmdResult.BMUAllocatePBRst.btQOP = pBmuCmdStatus->AllocatePB_Sts_t.;
			BMUCmdResult.BMUAllocatePBRst.ubTimeout = pBmuCmdStatus->AllocatePB_Sts_t.ubTimeoutValue;
			//BMUCmdResult.BMUAllocatePBRst.btFUA = pBmuCmdStatus->AllocatePB_Sts_t.;
			BMUCmdResult.BMUAllocatePBRst.btNoCQ = (!((pBmuCmdStatus->AllocatePB_Sts_t.uwTagID & 0x8000) >> 15));
			BMUCmdResult.BMUAllocatePBRst.ubTagID = (U8)pBmuCmdStatus->AllocatePB_Sts_t.uwTagID;
			BMUCmdResult.BMUAllocatePBRst.uwPBAddress = pBmuCmdStatus->AllocatePB_Sts_t.LBNA.B.uwPoolOffset;
			//BMUCmdResult.BMUAllocatePBRst.ubLBID = ;
			BMUCmdResult.BMUAllocatePBRst.ubCTag = pBmuCmdStatus->AllocatePB_Sts_t.ubCTAG;
			BMUCmdResult.BMUAllocatePBRst.ulLCA = pBmuCmdStatus->AllocatePB_Sts_t.ulLCA;
			//BMUCmdResult.BMUAllocatePBRst.ubStreamID = ;
		}
		else if (ubOpCode == Allocate_PB_Link) {		// odd opcode from HW
			pBmuCmdStatus->AllocatePBLink_Sts_t.ubOpcode = BMU_ALLOCATE_PB_LINK_CMD;
			BMUCmdResult.BMUAllocatePBLinkRst.ubOpcode = pBmuCmdStatus->AllocatePBLink_Sts_t.ubOpcode;
			BMUCmdResult.BMUAllocatePBLinkRst.ubResult = pBmuCmdStatus->AllocatePBLink_Sts_t.ubStatus;
			BMUCmdResult.BMUAllocatePBLinkRst.ubTargetID = pBmuCmdStatus->AllocatePBLink_Sts_t.ubSrcTagID;
			//BMUCmdResult.BMUAllocatePBLinkRst.btOption = pBmuCmdStatus->AllocatePBLink_Sts_t.ubRule;
			BMUCmdResult.BMUAllocatePBLinkRst.ubTimeout = pBmuCmdStatus->AllocatePBLink_Sts_t.ubTimeoutValue;
			BMUCmdResult.BMUAllocatePBLinkRst.btNoCQ = (!((pBmuCmdStatus->AllocatePBLink_Sts_t.uwTagID & 0x8000) >> 15));
			BMUCmdResult.BMUAllocatePBLinkRst.ubTagID = (U8)pBmuCmdStatus->AllocatePBLink_Sts_t.uwTagID;
			BMUCmdResult.BMUAllocatePBLinkRst.uwPBAddr = pBmuCmdStatus->AllocatePBLink_Sts_t.LBNA.B.uwPoolOffset;
			BMUCmdResult.BMUAllocatePBLinkRst.uwLBOffset = pBmuCmdStatus->AllocatePBLink_Sts_t.uwLogicalBufferOffset;
			//BMUCmdResult.BMUAllocatePBLinkRst.ubLBID = (pBmuCmdStatus->AllocatePBLink_Sts_t.ubLogicalBufferID & 0x07);
			BMUCmdResult.BMUAllocatePBLinkRst.ubCTag = pBmuCmdStatus->AllocatePBLink_Sts_t.ubCTAG;
			BMUCmdResult.BMUAllocatePBLinkRst.ulLCA = pBmuCmdStatus->AllocatePBLink_Sts_t.ulLCA;
			BMUCmdResult.BMUAllocatePBLinkRst.ubDoneCount = pBmuCmdStatus->AllocatePBLink_Sts_t.ubSize;
			//BMUCmdResult.BMUAllocatePBLinkRst.ubStreamID = (pBmuCmdStatus->AllocatePBLink_Sts_t.uwStreamID & 0xF);
		}
		else if (ubOpCode == BMU_EVENT_LISTENER_CMD) {
			BMUCmdResult.BMUEventListenerDynamicRst.ubOpcode = pBmuCmdStatus->EventListener_Sts_t.ubOpcode;
			BMUCmdResult.BMUEventListenerDynamicRst.btET = pBmuCmdStatus->EventListener_Sts_t.ubEventType & BIT0;
			BMUCmdResult.BMUEventListenerDynamicRst.ubTargetID = pBmuCmdStatus->EventListener_Sts_t.ubSrcTagID;
			BMUCmdResult.BMUEventListenerDynamicRst.uwPBOffset = pBmuCmdStatus->EventListener_Sts_t.LBNA.B.uwPoolOffset;
			BMUCmdResult.BMUEventListenerDynamicRst.ubValidBitmap = pBmuCmdStatus->EventListener_Sts_t.ubValidBitmap;
			BMUCmdResult.BMUEventListenerDynamicRst.ubEventTag = pBmuCmdStatus->EventListener_Sts_t.ubEventTag;
			//pBmuCmdStatus->EventListener_Sts_tubLogicalBufferID;
			//pBmuCmdStatus->EventListener_Sts_tuwLogicalBufferOffset;
		}
		else if (ubOpCode == WLB_SEARCH_CMD) {	// odd opcode from HW
			pBmuCmdStatus->WLBSearch_Sts_t.ubOpcode = BMU_WLB_SEARCH_CMD;	// 17->20
			BMUCmdResult.BMUWLBSearchRst.ubOpcode = pBmuCmdStatus->WLBSearch_Sts_t.ubOpcode;
			BMUCmdResult.BMUWLBSearchRst.ubResult = pBmuCmdStatus->WLBSearch_Sts_t.ubStatus;
			BMUCmdResult.BMUWLBSearchRst.ubTargetID = pBmuCmdStatus->WLBSearch_Sts_t.ubSrcTagID;
			BMUCmdResult.BMUWLBSearchRst.btOption = (pBmuCmdStatus->WLBSearch_Sts_t.ubRule & BIT0);
			BMUCmdResult.BMUWLBSearchRst.btZero = pBmuCmdStatus->WLBSearch_Sts_t.ubZeroFlag;
			BMUCmdResult.BMUWLBSearchRst.btNoCQ = (!((pBmuCmdStatus->WLBSearch_Sts_t.uwTagID & 0x8000) >> 15));
			BMUCmdResult.BMUWLBSearchRst.ubTagID = (U8)pBmuCmdStatus->WLBSearch_Sts_t.uwTagID;
			BMUCmdResult.BMUWLBSearchRst.uwPBAddr = pBmuCmdStatus->WLBSearch_Sts_t.LBNA.B.uwPoolOffset;
			BMUCmdResult.BMUWLBSearchRst.uwLBOffset = pBmuCmdStatus->WLBSearch_Sts_t.uwLogicalBufferOffset;
			BMUCmdResult.BMUWLBSearchRst.ubLBID = (pBmuCmdStatus->WLBSearch_Sts_t.ubLogicalBufferID & 0x07);
			BMUCmdResult.BMUWLBSearchRst.btE3D4KFlag = pBmuCmdStatus->WLBSearch_Sts_t.ubE3D4KFlag;
			BMUCmdResult.BMUWLBSearchRst.btFull = pBmuCmdStatus->WLBSearch_Sts_t.btWLB4KFull;
			BMUCmdResult.BMUWLBSearchRst.ulLCA = pBmuCmdStatus->WLBSearch_Sts_t.ulLCA;
		}
		else if (ubOpCode == BMU_ALLOCATE_LB_CMD) {
			BMUCmdResult.BMUAllocateLBRst.ubOpcode	= pBmuCmdStatus->AllocateLB_Sts_t.ubOpcode;
			BMUCmdResult.BMUAllocateLBRst.btNoCQ	= (!((pBmuCmdStatus->AllocateLB_Sts_t.uwTagID & 0x8000) >> 15));
			BMUCmdResult.BMUAllocateLBRst.ubResult	= pBmuCmdStatus->AllocateLB_Sts_t.ubStatus;
			BMUCmdResult.BMUAllocateLBRst.ubLBID	= (pBmuCmdStatus->AllocateLB_Sts_t.ubLogicalBufferID & 0x07);
			BMUCmdResult.BMUAllocateLBRst.ubTagID	= (U8)pBmuCmdStatus->AllocateLB_Sts_t.uwTagID;
			BMUCmdResult.BMUAllocateLBRst.ubTargetID = pBmuCmdStatus->AllocateLB_Sts_t.ubSrcTagID;
			BMUCmdResult.BMUAllocateLBRst.uwLBOffset = pBmuCmdStatus->AllocateLB_Sts_t.uwLogicalBufferOffset;
			BMUCmdResult.BMUAllocateLBRst.uwPBAddr	= pBmuCmdStatus->AllocateLB_Sts_t.LBNA.B.uwPoolOffset;
		}
		vQueuePop (CMD_CQ_GET_QUEUE_PTR (BMUCmdResult.BMURst.ubTargetID), pBmuCmdStatus);
		pBMUCmdResult->All = BMUCmdResult.All;
#endif /*VS_SIM_EN*/


		if (ubOpCode == BMU_EVENT_LISTENER_CMD) {
			uwTagId = (U16)pBMUCmdResult->BMUEventListenerDynamicRst.ubEventTag;
		}
		else if (ubOpCode == 0x1F) {
			//continue;
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
		}
		else {
			uwTagId	 =  (U16)pBMUCmdResult->BMURst.ubTagID;
		}

		call_back = (bmu_cmd_doing_func_t)gpuoCmdTableMgr[BMU_CMD_TABLE_ID][uwTagId].ulCmdFuncPTR;
		if (call_back != 0) {
			call_back(pBMUCmdResult);
			gpuoCmdTableMgr[BMU_CMD_TABLE_ID][uwTagId].ulCmdFuncPTR = 0;
			gpuoCmdTableMgr[BMU_CMD_TABLE_ID][uwTagId].ulData.ulAll = 0;
			FTLPushTagPool(BMU_TAG_POOL_ID, uwTagId);
		}
		else {
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
		}

		M_DB_TRIGGER_READ_CNT(DB_BMU_CQ, 1);
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_CQ, gDBQueueCnt.QueueCnt[(DB_BMU_CQ)] ) == M_DB_GET_RPTR((DB_BMU_CQ)));
	}
#endif //(!BURNER_MODE_EN)	
}

