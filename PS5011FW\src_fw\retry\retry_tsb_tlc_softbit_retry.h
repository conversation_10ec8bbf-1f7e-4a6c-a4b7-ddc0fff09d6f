/****************************************************************************/
//
//  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
//  All rights reserved
//
//  The content of this document is confidential and shall be applied
//  subject to the terms and conditions of the license agreement and
//  other applicable laws. Any unauthorized access, use or disclosure
//  of this document is strictly prohibited and may be punishable
//  under laws.
//
//  retry_tsb_tlc_softbit_retry.h
//
//
//
/****************************************************************************/

#ifndef _RETRY_TSB_TLC_SOFTBIT_RETRY_H_
#define _RETRY_TSB_TLC_SOFTBIT_RETRY_H_

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "setup.h"
#include "hal/fip/fip_api.h"
#include "common/fw_common.h"
#if ((FW_CATEGORY_FLASH != FLASH_BICS5TLC) && ((CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_2D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_SANDISK_3D_TLC)))
/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */
#define  LDPC_FRAME_NUMBER_PER_FRAME                    (2)

#if(RETRY_SOFTBITT_FOR_SDK_EN==FALSE)

#define NORMAL_MODE    (0)
#define INVERSE_MODE   (1)

#define RETRY_SB_BACKUP_RESTORE_TO_DRAMIRAM       (1)
#define RETRY_SB_BACKUP_RESTORE_TO_ONLYDRAM       (0)
#define RETRY_SB_BACKUP_RESTORE_TO_ONLYDRAM_AND_KEEP       (2)
#define RETRY_SB_BACKUP_RESTORE_FROM_ONLYDRAM       (3)
#define RETRY_SB_BACKUP_RESTORE_TO_DRAMIRAM_AND_KEEP       (4)

// MT Trigger Mode
#define RETRY_SB_MT_TRIGGER_MODE_NORMAL			(0)
#define RETRY_SB_MT_TRIGGER_MODE_LOW_CLOCK		(1)
#define RETRY_SB_MT_TRIGGER_MODE_LEGACY			(2)

#define RETRY_SB_INSERT_USED_MT_MODE                   (0)
#define RETRY_SB_INSERT_NEW_MT_MODE                    (1)
#define RETRY_SB_INSERT_USED_MT_MODE_AND_WRITE_SPARE   (2)
#define RETRY_SB_INSERT_NEW_MT_MODE_AND_WRITE_SPARE		(3)

/// for gubRetrySBCnt1Result
#define CNT1_RESULT_ARRAY_PARM_LENGTH   (4)
#define CNT1_RESULT_ARRAY_PARM_CNT        (5)
#define CNT1_RESULT_ARRAY_FRAME_CNT      (6)
#define CNT1_RESULT_ARRAY_ROUND_CNT      (7)
#define CNT1_RESULT_SKIP_BIT_CNT      (8)
#define RETRY_SB_CNT1_RESULT_ARRAY_SIZE			(9)

#define COARSE_TABLE_CHECK_ROUND_MAX_TIMES  (3)

#define  COARSE_LEVEL_DIVISOR_1_IDX  (0)
#define  COARSE_LEVEL_DIVISOR_2_IDX  (1)
#define  COARSE_LEVEL_DIVISOR_3_IDX  (2)
#define  COARSE_LEVEL_THRESHOLD_1_IDX  (3)
#define  COARSE_LEVEL_THRESHOLD_2_IDX  (4)
#define  RETRY_SB_COARSE_LEVEL_ARRAY_SIZE		(5)

#define RETRY_SB_COARSE_TABLE_MAGIC_NUMBER   (18336)
#define RETRY_SB_MT_FAIL_RETRY_CNT_MAX            (0xF)

#define SB_BICS3_BICS4_GENERAL_LLR_TABLE_START_IDX		(0)
#define SB_BICS3_BICS4_CORNER_CASE_LLR_TABLE_START_IDX	(1)

//SB Debug 0121
#define SB_FIRST_2K_DATA		BIT0
#define SB_FIRST_2K_SPARE		BIT1
#define SB_SECOND_2K_DATA		BIT2
#define SB_SECOND_2K_SPARE	BIT3
#else /* (RETRY_SOFTBITT_FOR_SDK_EN==FALSE) */

#endif /* (RETRY_SOFTBITT_FOR_SDK_EN==FALSE) */

#define SBIT_RETRY_STATE_READY                          (BIT0)
#define SBIT_RETRY_FORCE_SIG                            (BIT1)

#define SBIT_NAND_STATE_INIT                            (0)

#define SBIT_LOWER_PAGE_ID                              (0)         // page id low
#define SBIT_MIDDLE_PAGE_ID                             (1)         // page id middle
#define SBIT_UPPER_PAGE_ID                              (2)         // page id up
#define SBIT_TOP_PAGE_ID                                (3)         // page id top (QLC)
#define SBIT_MAX_SUPPORT_PAGE_NUM                       (3)         // max page number

#define SBIT_LLR_TBL_NUM                                (2)         // llr table buffer address, 4 llr table for lmut
#define SBIT_LLR_TABLE_SIZE                             (32)        // each table size is 32B (fixed by hardware)
#define SBIT_GRAY_CODE_TABLE_SIZE                       (20)        // gray code table size in byte (fixed by hardware)
#define SBIT_LUT_TABLE_SIZE                             (40)        // lookup table size in byte (fixed by hardware)
#define SBIT_DSP_PARAM_TABLE_SIZE                       (8)         // dsp param table size in byte (fixed by hardware)

#define SBIT_DSP_LLR_TABLE_SIZE                             (40)        // each DSP LLR table size is 40B (fixed by hardware)
#define SBIT_DSP_LLR_TABLE_BITS_PER_HW_ACCESS      (5)        // HW access LLR table per 5Bits
#define SBIT_DSP_LLR_TABLE_SIZE_IN_4_BYTES         ((SBIT_DSP_LLR_TABLE_SIZE << 3) / 32)        //10
#define SBIT_DSP_LLR_TABLE_SIZE_IN_40_BITS         ((SBIT_DSP_LLR_TABLE_SIZE << 3) / 40)        //8
#define SBIT_DSP_LLR_TABLE_SIZE_IN_5_BITS         ((SBIT_DSP_LLR_TABLE_SIZE << 3) / 5)			//64
#define SBIT_DSP_LLR_TABLE_5_BITS_TO_40_BITS_IDX		(8)		//	64 / 8
#define SBIT_DSP_LLR_TABLE_5_BITS_TO_40_BITS_IDX_LOG	(3)		//	Log(8)
#define SBIT_DSP_LLR_TABLE_5_BITS_TO_OPRATION_IDX		(16)		//	64 / 4
#define SBIT_DSP_LLR_TABLE_5_BITS_TO_OPRATION_IDX_LOG	(4)		//	Log(16)
#define SBIT_DSP_LLR_TABLE_BITS_PER_OPRATION	(2)		//	2Bits for +1, -1, 0

#define SBIT_DELTA_MAX_COL_NUM                          (8)         // delta table column number
#define SBIT_DELTA_MAX_ROW_NUM                          (7)         // delta table row number
#define SBIT_DELTA_N3R                                  (0)
#define SBIT_DELTA_N2R                                  (1)
#define SBIT_DELTA_N1R                                  (2)
#define SBIT_DELTA_P0R                                  (3)
#define SBIT_DELTA_P1R                                  (4)
#define SBIT_DELTA_P2R                                  (5)
#define SBIT_DELTA_P3R                                  (6)

#define SBIT_DELTA_P0                                   (0)
#define SBIT_DELTA_P1                                   (1)
#define SBIT_DELTA_P2                                   (2)
#define SBIT_DELTA_P3                                   (3)
#define SBIT_DELTA_P4                                   (4)
#define SBIT_DELTA_P5                                   (5)
#define SBIT_DELTA_P6                                   (6)
#define SBIT_DELTA_DUMMY                                (7)

#define SBIT_CENTER_MAX_COL_NUM                         (SBIT_DELTA_MAX_COL_NUM)
#define SBIT_CENTER_MAX_ROW_NUM                         (SBIT_DELTA_MAX_ROW_NUM - 1)
#define SBIT_CENTER_P0                                  (SBIT_DELTA_P0)
#define SBIT_CENTER_P1                                  (SBIT_DELTA_P1)
#define SBIT_CENTER_P2                                  (SBIT_DELTA_P2)
#define SBIT_CENTER_P3                                  (SBIT_DELTA_P3)
#define SBIT_CENTER_P4                                  (SBIT_DELTA_P4)
#define SBIT_CENTER_P5                                  (SBIT_DELTA_P5)
#define SBIT_CENTER_P6                                  (SBIT_DELTA_P6)

#define SOFTBIT_MAX_RETRY_STATE                         (10)             // softbit retry lib maximum support state number

#define SBIT_FPU_STEP_INIT                              (0)
#define SBIT_FPU_STEP_DONE                              (0xFE)
#define SBIT_FPU_STEP_INVALID                           (0xFF)

#define SBIT_CENTER_NUM                                 (4)

#define SBIT_TAG_STATUS_DONE                            (BIT0)
#define SBIT_TAG_STATUS_ALLOC_FAIL                      (BIT1)

#define SOFTBIT_RETRY_FPU_BUF_NUM                   (2)
#define SOFTBIT_RETRY_FPU_BUF_ENTRY_NUM             (64)
#define SOFTBIT_RETRY_FPU_TEMPLATE_BUF_ENTRY_NUM    (192)

#define SOFTBIT_RETRY_CASE_A				(0)
#define SOFTBIT_RETRY_CASE_B				(1)

#define SBIT_WL_NUM_PER_SHARE_WL	(4)

// Check over value boundary related
#define RETRY_SB_NO_OVER_BOUNDARY			(0)
#define RETRY_SB_OVER_POSITIVE_BOUNDARY		(1)
#define RETRY_SB_OVER_NEGATIVE_BOUNDARY		(2)

#define RETRY_SB_BICS4_HDR_COUNT_DELTA_VTH_BASE	(18336)
#define RETRY_SB_BICS4_HDR_256Gb_COUNT_DELTA_VTH_PARAMETER_A	(38)
#define RETRY_SB_BICS4_HDR_256Gb_COUNT_DELTA_VTH_PARAMETER_B	(56)
#define RETRY_SB_BICS4_HDR_512Gb_COUNT_DELTA_VTH_PARAMETER_A	(41)
#define RETRY_SB_BICS4_HDR_512Gb_COUNT_DELTA_VTH_PARAMETER_B	(72)

#define RETRY_SB_COUNT_DELTA_LEVEL(ubReadState_m)	(ubReadState_m + 1)

#define RETRY_SB_COARSE_TUNING_COFFICEINT_CONSTANT_FLOAT_SHIFT	(1000000)

/*
 * ---------------------------------------------------------------------------------------------------
 *  enum
 * ---------------------------------------------------------------------------------------------------
 */
#if(RETRY_SOFTBITT_FOR_SDK_EN==FALSE)
/*
 *  SOFTBIT SUBSTATE MACHINE
 *  =====================
 */

typedef enum SoftBitSubStateEnum {
	RETRY_SB_SUBSTATE_DET_COARSE_LVL__INIT = 1,							// 1
	RETRY_SB_SUBSTATE_DET_COARSE_LVL__DEFAULT_RD_LVL_I,					// 2
	RETRY_SB_SUBSTATE_DET_COARSE_LVL__DEFAULT_RD_LVL_II,					// 3

	RETRY_SB_SUBSTATE_DET_COARSE_LVL__RD_CNTONE_OP,						// 4
	RETRY_SB_SUBSTATE_DET_COARSE_LVL__RD_CNTONE_DMA,					// 5
	RETRY_SB_SUBSTATE_DET_COARSE_LVL__CHK_COARSE_TABLE,					// 6

	RETRY_SB_SUBSTATE_DET_COARSE_LVL__RD_IDLE_OR_WAIT,					// 7

	///--------------------------------------------------------------------
	/// DEBUG USAGE
	///---------------------------------------------------------------------
	RETRY_SB_SUBSTATE_DET_COARSE_LVL__RD_CNTONE_OP_DEBUG,				// 8
	RETRY_SB_SUBSTATE_DET_COARSE_LVL__RD_CNTONE_DMA_DEBUG,			// 9
	///---------------------------------------------------------------------

	RETRY_SB_SUBSTATE_DECODE_WITH_DEFAULT_LLR_WITH_OPT_READ_LEVEL_INIT,			// 10
	RETRY_SB_SUBSTATE_DECODE_WITH_DEFAULT_LLR_WITH_OPT_READ_LEVEL_SB_CORR,		// 11

	RETRY_SB_SUBSTATE_READ_WITH_COARSE_TUNING_LEVEL_INIT,								// 12
	RETRY_SB_SUBSTATE_READ_WITH_COARSE_TUNING_LEVEL_RD_HB,							// 13
	RETRY_SB_SUBSTATE_READ_WITH_COARSE_TUNING_LEVEL_RD_SB1,							// 14
	RETRY_SB_SUBSTATE_READ_WITH_COARSE_TUNING_LEVEL_RD_SB2,							// 15
	RETRY_SB_SUBSTATE_READ_WITH_COARSE_TUNING_LEVEL_RD_SB3,							// 16
	RETRY_SB_SUBSTATE_READ_WITH_COARSE_TUNING_LEVEL_RD_SB4,							// 17
	RETRY_SB_SUBSTATE_DECODE_WITH_CORNER_CASE_FIX_LLR_WITH_COARSE_TUNING_READ_LEVEL_INIT,		// 18
	RETRY_SB_SUBSTATE_DECODE_WITH_CORNER_CASE_FIX_LLR_WITH_COARSE_TUNING_READ_LEVEL_SB_CORR,	// 19
	RETRY_SB_SUBSTATE_READ_WITH_COARSE_TUNING_LEVEL_IDLE_OR_WAIT,									// 20

	RETRY_SB_SUBSTATE_DECODE_WITH_ADT_LLR_WITH_COARSE_TUNING_READ_LEVEL_INIT,						// 21
	RETRY_SB_SUBSTATE_DECODE_WITH_ADT_LLR_WITH_COARSE_TUNING_READ_LEVEL_SB_CORR,				// 22

	RETRY_SB_SUBSTATE_RETURE_DEFAULT_FEATURE,													// 23

	RETRY_SB_SUBSTATE_WITH_COARSE_TUNING_LVL_INIT,											// 24
	RETRY_SB_SUBSTATE_WITH_COARSE_TUNING_LVL_HB_RD_OP,										// 25
	RETRY_SB_SUBSTATE_WITH_COARSE_TUNING_LVL_HB_CORR_2K,									// 26

	RETRY_SB_SUBSTATE_WITH_OPT_LVL_INIT,														// 27
	RETRY_SB_SUBSTATE_WITH_OPT_LVL_HB_RD_OP,													// 28
	RETRY_SB_SUBSTATE_WITH_OPT_LVL_HB_CORR_2K,												// 29
	RETRY_SB_SUBSTATE_WITH_OPT_LVL_INIT_SB_CORR,												// 30
	RETRY_SB_SUBSTATE_WITH_OPT_LVL_SB_CORR,													// 31

	RETRY_SB_SUBSTATE_READ_WITH_OPT_LEVEL_INIT,										// 32
	RETRY_SB_SUBSTATE_READ_WITH_OPT_LEVEL_RD_HB,										// 33
	RETRY_SB_SUBSTATE_READ_WITH_OPT_LEVEL_RD_SB1,										// 34
	RETRY_SB_SUBSTATE_READ_WITH_OPT_LEVEL_RD_SB2,										// 35
	RETRY_SB_SUBSTATE_READ_WITH_OPT_LEVEL_RD_SB3,										// 36
	RETRY_SB_SUBSTATE_READ_WITH_OPT_LEVEL_RD_SB4,										// 37

	RETRY_SB_SUBSTATE_DECODE_WITH_CORNER_CASE_FIX_LLR_WITH_OPT_READ_LEVEL_INIT,			// 38
	RETRY_SB_SUBSTATE_DECODE_WITH_CORNER_CASE_FIX_LLR_WITH_OPT_READ_LEVEL_SB_CORR,		// 39
	RETRY_SB_SUBSTATE_READ_WITH_OPT_LEVEL_IDLE_OR_WAIT,										// 40
	RETRY_SB_SUBSTATE_DECODE_WITH_ADT_LLR_WITH_OPT_READ_LEVEL_INIT,							// 41
	RETRY_SB_SUBSTATE_DECODE_WITH_ADT_LLR_WITH_OPT_READ_LEVEL_SB_CORR,					// 42

	RETRY_SB_SUBSTATE_WITH_OPT_LEVEL_AND_APT_LLR__INIT,					// 43

	RETRY_SB_SUBSTATE_DSP2_INIT,											// 44
	RETRY_SB_SUBSTATE_DSP2_SKIP_FOUR_PAGE_RD_HB_1,						// 45
	RETRY_SB_SUBSTATE_DSP2_SKIP_FOUR_PAGE_RD_HB_2,						// 46
	RETRY_SB_SUBSTATE_DSP2_SKIP_FOUR_PAGE_RD_HB_3,						// 47
	RETRY_SB_SUBSTATE_DSP2_GEN_SB6_SB7,									// 48
	RETRY_SB_SUBSTATE_DSP2_RD_HB,											// 49
	RETRY_SB_SUBSTATE_DSP2_RD_SB1,											// 50
	RETRY_SB_SUBSTATE_DSP2_RD_SB2,											// 51
	RETRY_SB_SUBSTATE_DSP2_RD_SB3,											// 52
	RETRY_SB_SUBSTATE_DSP2_RD_SB4,											// 53
	RETRY_SB_SUBSTATE_DSP2__INT_SB_CORR,									// 54
	RETRY_SB_SUBSTATE_DSP2__SB_CORR,										// 55

	RETRY_SB_SUBSTATE_WITH_OPT_READ_LEVEL__AFTER_RAIDECC__INIT,					// 56
	RETRY_SB_SUBSTATE_WITH_OPT_READ_LEVEL__AFTER_RAIDECC__HB,					// 57
	RETRY_SB_SUBSTATE_WITH_OPT_READ_LEVEL__AFTER_RAIDECC__RD_SB1,				// 58
	RETRY_SB_SUBSTATE_WITH_OPT_READ_LEVEL__AFTER_RAIDECC__RD_SB2,				// 59
	RETRY_SB_SUBSTATE_WITH_OPT_READ_LEVEL__AFTER_RAIDECC__RD_SB3,				// 60
	RETRY_SB_SUBSTATE_WITH_OPT_READ_LEVEL__AFTER_RAIDECC__RD_SB4,				// 61
	RETRY_SB_SUBSTATE_WITH_OPT_READ_LEVEL__AFTER_RAIDECC__BKUP_RESTORE_SB6,	// 62
	RETRY_SB_SUBSTATE_WITH_OPT_READ_LEVEL__AFTER_RAIDECC__TRIG_SBC_FOR_ADT_LLR,	//63
	RETRY_SB_SUBSTATE_WITH_OPT_READ_LEVEL__AFTER_RAIDECC__INT_SB_CORR,			// 64
	RETRY_SB_SUBSTATE_WITH_OPT_READ_LEVEL__AFTER_RAIDECC__SB_CORR,				// 65
	RETRY_SB_SUBSTATE_WITH_OPT_READ_LEVEL__AFTER_RAIDECC__IDLE_OR_WAIT,		// 66

	RETRY_SB_SUBSTATE_DSP2_SKIP_FOUR_PAGE_RD_G_STATUS,						// 67

	//Gen ADT_LLR
	RETRY_SB_SUBSTATE_WITH_COARSE_TUNING_LVL_TRIG_SBC_FOR_ADT_LLR	,	//68
	RETRY_SB_SUBSTATE_WITH_OPT_LVL_TRIG_SBC_FOR_ADT_LLR,				//69
	RETRY_SB_SUBSTATE_DSP2__SB_TRIG_SBC_FOR_ADT_LLR,					//70

	RETRY_SB_SUBSTATE_STATE__INITIAL_VARIABLE_AND_THIRDSTATE,                                    //71
	RETRY_SB_SUBSTATE_STATE__SET_DEFAULT_FEATURE,		// 72
	RETRY_SB_SUBSTATE_STATE__GET_FEATURE_AND_CHECK,		// 73

	RETRY_SB_SUBSTATE_DET_COARSE_LVL__TEST_MODE_ENTRY,		// 74

	RETRY_SB_SUBSTATE_SNDK_BICS3_READ_OPT_LEVEL_RD_SB1_SB2,	//75
	RETRY_SB_SUBSTATE_SNDK_BICS3_READ_WITH_COARSE_TUNING_LEVEL_RD_SB1_SB2, //76
	RETRY_SB_SUBSTATE_SNDK_BICS3_DSP2_RD_SB1_SB2,		//77
	RETRY_SB_SUBSTATE_SNDK_BICS3_WITH_OPT_READ_LEVEL__AFTER_RAIDECC__RD_SB1_SB2,	//78

	RETRY_SB_SUBSTATE_NULL = 0xFF
} SoftBitSubStateEnum_t;

/*
 *  SOFTBIT 3RD STATE MACHINE
 *  =====================
 */
typedef enum SoftBitThirdStateEnum {
	RETRY_SB_3RD_STATE__RD_HB__INIT = 1,                                              //(1)
	RETRY_SB_3RD_STATE__RD_HB__SET_NEW_READ_LVL_I,                   //(2)
	RETRY_SB_3RD_STATE__RD_HB__SET_NEW_READ_LVL_II,                  //(3)
	RETRY_SB_3RD_STATE__RD_HB__DIRECT_RD_OP,                              //(4)
	RETRY_SB_3RD_STATE__RD_HB__DIRECT_RD_DMA,                            //(5)

	RETRY_SB_3RD_STATE__RETURN_DEFAULT_RD_LVL_I,                          //(6)
	RETRY_SB_3RD_STATE__RETURN_DEFAULT_RD_LVL_II,                          //(7)
	RETRY_SB_3RD_STATE__RETURN_DEFAULT_RD_LVL_IDLE,                      //(8)

	RETRY_SB_3RD_STATE__RD_SB1__INIT,                                             //(9)
	RETRY_SB_3RD_STATE__RD_SB1__HB_ADD2_DELTA,                          //(10)
	RETRY_SB_3RD_STATE__RD_SB1__HB_MINUS2_DELTA,                       //(11)
	RETRY_SB_3RD_STATE__RD_SB1__XOR,                                             //(12)
	RETRY_SB_3RD_STATE__RD_SB1__DMA,                                             //(13)
	RETRY_SB_3RD_STATE__RD_HB__RETRUN_DEFAULT_READ_LVL_I,       //(14)
	RETRY_SB_3RD_STATE__RD_HB__RETRUN_DEFAULT_READ_LVL_II,      //(15)

	RETRY_SB_3RD_STATE__RD_SB2__INIT,                                              //(16)
	RETRY_SB_3RD_STATE__RD_SB2__HB_ADD3_DELTA,                          //(17)
	RETRY_SB_3RD_STATE__RD_SB2__HB_MINUS3_DELTA,                       //(18)
	RETRY_SB_3RD_STATE__RD_SB2__ADDMINUS3_XOR,                          //(19)
	RETRY_SB_3RD_STATE__RD_SB2__MINUS1_DELTA,                          //(20)
	RETRY_SB_3RD_STATE__RD_SB2__ADDMINUS3_MINUS1_XOR,          //(21)
	RETRY_SB_3RD_STATE__RD_SB2__ADD1_DELTA,                                 //(22)
	RETRY_SB_3RD_STATE__RD_SB2__MINUS3_XOR,                             //(23)
	RETRY_SB_3RD_STATE__RD_SB2__DMA,                                              //(24)

	RETRY_SB_3RD_STATE__RD_SB3__INIT,                                             //(25)
	RETRY_SB_3RD_STATE__RD_SB3__OP,                                            //(26)
	RETRY_SB_3RD_STATE__RD_SB3__DMA,                                            //(27)

	RETRY_SB_3RD_STATE__RD_SB4__INIT,                                             //(28)
	RETRY_SB_3RD_STATE__RD_SB4__OP,                                            //(29)
	RETRY_SB_3RD_STATE__RD_SB4__DMA,                                            //(30)

	RETRY_SB_3RD_STATE__GET_DEFAULT_RD_LVL_I,                             //(31)
	RETRY_SB_3RD_STATE__CHK_DEFAULT_RD_LVL_I,                             //(32)
	RETRY_SB_3RD_STATE__GET_DEFAULT_RD_LVL_II,                            //(33)
	RETRY_SB_3RD_STATE__CHK_DEFAULT_RD_LVL_II,                            //(34)

	RETRY_SB_3RD_STATE__GET_NEW_RD_LVL_I,					//(35)
	RETRY_SB_3RD_STATE__GET_NEW_RD_LVL_II,					//(36)
	RETRY_SB_3RD_STATE__CHK_NEW_RD_LVL_I,					//(37)
	RETRY_SB_3RD_STATE__CHK_NEW_RD_LVL_II,					//(38)

	RETRY_SB_3RD_STATE__RD_SB2__ADDMINUS3_DELTA_DMA,		// (39)
	RETRY_SB_3RD_STATE__RD_HB__TEST_MODE_ENTRY,		// (40)

	RETRY_SB_3RD_STATE__RETURN_DEFAULT_SB_LVL_I,			// (41)
	RETRY_SB_3RD_STATE__RETURN_DEFAULT_SB_LVL_II,			// (42)
	RETRY_SB_3RD_STATE__GET_DEFAULT_SB_LVL_I,				// (43)
	RETRY_SB_3RD_STATE__CHK_DEFAULT_SB_LVL_I,				// (44)
	RETRY_SB_3RD_STATE__GET_DEFAULT_SB_LVL_II,				// (45)
	RETRY_SB_3RD_STATE__CHK_DEFAULT_SB_LVL_II,				// (46)

	RETRY_SB_3RD_STATE__SNDK_BICS3_RD_SB__INIT,				// (47)
	RETRY_SB_3RD_STATE__SNDK_BICS3_RD_SB1__SET_DELTA,		// (48)
	RETRY_SB_3RD_STATE__SNDK_BICS3_RD_SB1__DMA,				// (49)
	RETRY_SB_3RD_STATE__SNDK_BICS3_RD_SB2__SET_DELTA,		// (50)
	RETRY_SB_3RD_STATE__SNDK_BICS3_RD_SB2__DMA,				// (51)
	RETRY_SB_3RD_STATE__SNDK_BICS3_RD_SB2__SET_2ND_DELTA,	// (52)
	RETRY_SB_3RD_STATE__SNDK_BICS3_RD_SB2__2ND_DMA,			// (53)
} SoftBitThirdStateEnum_t;

typedef enum SoftBitFourthStateEnum {
	RETRY_SB_4TH_STATE_MODIFY_RR_DATA = 1,                                         //(1)
	RETRY_SB_4TH_STATE_TRIGGER_MT,                                                  //(2)
	RETRY_SB_4TH_STATE_SNDK_BICS3_DSP_SBREAD_TESTMODE							//(3)
} SoftBitFourthStateEnum_t;

typedef enum RetrySBDataBitLength {
	RETRY_SB_U8_BIT_LENGTH = 8,
	RETRY_SB_U16_BIT_LENGTH = 16,
	RETRY_SB_U32_BIT_LENGTH = 32,
	RETRY_SB_U64_BIT_LENGTH = 64,
} RetrySBDataBitLength_t;

#else /* (RETRY_SOFTBITT_FOR_SDK_EN==FALSE) */

#endif  /* (RETRY_SOFTBITT_FOR_SDK_EN==FALSE) */
/*
 * ---------------------------------------------------------------------------------------------------
 *  data types
 * ---------------------------------------------------------------------------------------------------
 */
typedef struct fpl_sbit_retry_fip_llr_struct        FPL_SBIT_RETRY_FIP_LLR_STRUCT,   *FPL_SBIT_RETRY_FIP_LLR_STRUCT_PTR;
#if(RETRY_SOFTBITT_FOR_SDK_EN==FALSE)
typedef struct fpl_sbit_retry_param_struct          FPL_SBIT_RETRY_PARAM_STRUCT,     *FPL_SBIT_RETRY_PARAM_STRUCT_PTR;
#endif /* (RETRY_SOFTBITT_FOR_SDK_EN==FALSE) */

struct fpl_sbit_retry_fip_llr_struct {
	U8 entry[SBIT_LLR_TABLE_SIZE];
};

/*
 *           DETAL TABLE             =>          CENTER TABLE
 *           ===========                         ============
 *
 *                                                 +---------------------------------------+
 *                                                 | C0 |    |    |    | C1 |    |    |    |        <= center 0 and 1 for lower page
 *                                                 |    | C0 |    | C1 |    | C2 |    |    |        <= center 0, 1, and 2 for middle page
 *                                                 | C0 |    |    |    | C1 |    |    |    |        <= center 0 and 1 for upper page
 *  +--------------------------------------------------------------------------------------+
 *  |           | P0 | P1 |...| P7 |    |          | P0 | P1 | P2 | P3 | P4 | P6 | P6 | P7 |
 *  |------------------------------+----+--------------------------------------------------|
 *  | - detal 3 | ## | ## |...| ## |    |          |    |    |    |    |    |    |    |    |
 *  |           |    |    |   |    | => | level  0 | ## | ## | ## | ## | ## | ## | ## | ## |
 *  | - detal 2 | ## | ## |...| ## |    |          |    |    |    |    |    |    |    |    |
 *  |           |    |    |   |    | => | level  1 | ## | ## | ## | ## | ## | ## | ## | ## |
 *  | - detal 1 | ## | ## |...| ## |    |          |    |    |    |    |    |    |    |    |
 *  |           |    |    |   |    | => | level  2 | ## | ## | ## | ## | ## | ## | ## | ## |
 *  |   detal 0 | ## | ## |...| ## |    |          |    |    |    |    |    |    |    |    |
 *  |           |    |    |   |    | => | level  3 | ## | ## | ## | ## | ## | ## | ## | ## |
 *  | + detal 1 | ## | ## |...| ## |    |          |    |    |    |    |    |    |    |    |
 *  |           |    |    |   |    | => | level  4 | ## | ## | ## | ## | ## | ## | ## | ## |
 *  | + detal 2 | ## | ## |...| ## |    |          |    |    |    |    |    |    |    |    |
 *  |           |    |    |   |    | => | level  5 | ## | ## | ## | ## | ## | ## | ## | ## |
 *  | + detal 3 | ## | ## |...| ## |    |          |    |    |    |    |    |    |    |    |
 *  +--------------------------------------------------------------------------------------+
 *
 */
struct fpl_sbit_retry_param_struct {
	FPL_SBIT_RETRY_FIP_LLR_STRUCT_PTR default_llr[SBIT_MAX_SUPPORT_PAGE_NUM];
	FPL_SBIT_RETRY_FIP_LLR_STRUCT_PTR adt_llr[SBIT_LLR_TBL_NUM];

	U8  delta[SBIT_DELTA_MAX_ROW_NUM][SBIT_DELTA_MAX_COL_NUM];
	U8  center[SBIT_CENTER_MAX_ROW_NUM][SBIT_CENTER_MAX_COL_NUM];
	U8  dsp_center_idx[SBIT_CENTER_NUM];
};

#if(RETRY_SOFTBITT_FOR_SDK_EN==FALSE)
struct corse_tuning_element {
	U32 x_low;
	U32 x_upp;
	U32 y_low;
	U32 y_upp;
	U32 z_low;
	U32 z_upp;
};

typedef struct SoftBitLLR40Bits   SoftBitLLR40Bits_t;
struct SoftBitLLR40Bits {      // use union type to optimize.
	U8 ubLLR8Bits[5];
};

typedef struct SoftBitLLRTable   SoftBitLLRTable_t;
struct SoftBitLLRTable {      // use union type to optimize.
	union {
		struct {
			U32 ulLLR32bits[10];
		} AccessBy32bits;
		struct {
			U8 ubLLR8bits[40];
		} AccessBy8bits;
		SoftBitLLR40Bits_t LLR40Bits[8];
	} LLRTable;
};

typedef struct LLRTable40BitsAccess   LLRTable40BitsAccess_t;
struct LLRTable40BitsAccess {      // use union type to optimize.
	union {
		U64 uoAll;
		SoftBitLLR40Bits_t LLR40Bits;
	} LLRTable;
};

typedef struct SBRetryParameterMgr {
	// Feature Address
	U8 ubHBReadLevelFeatureAddr1st;
	U8 ubHBReadLevelFeatureAddr2nd;
	// HB Retry Read FPU
	U16 uwHBRetryReadFPUOffsetp[SBIT_MAX_SUPPORT_PAGE_NUM];
	// SB Read FPU
	U16 uwSBReadFPUOffset[SBIT_MAX_SUPPORT_PAGE_NUM];
	// Single State Read
	U16 uwSingleStateReadFPUOffset;
	// Global Trigger Mode
	U8 ubMTTriggerMode;
	U8 ubReserved;  // fill compiler alignment
} RetrySBParameterManager_t;

#else /* (RETRY_SOFTBITT_FOR_SDK_EN==FALSE) */
#endif /* (RETRY_SOFTBITT_FOR_SDK_EN==FALSE) */

#endif /* ((FW_CATEGORY_FLASH != FLASH_BICS5TLC) && ((CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_2D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_SANDISK_3D_TLC))) */
#endif /* _RETRY_TSB_TLC_SOFTBIT_RETRY_H_ */

