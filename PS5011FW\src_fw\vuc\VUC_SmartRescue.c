#include "common/fw_common.h"
#include "common/mem.h"
#include "common/symbol.h"
#include "env.h"
#include "hal/cop0/cop0_api.h"
#include "hal/dmac/dmac_api.h"
#include "hal/dmac/dmac_pop_cmd.h"
#include "hal/fip/fip_api.h"
#include "host/VUC_handler.h"
#include "host/VUC_handler_api.h"
#include "host/VUC_host.h"
#include "init/NewScanFlow.h"
#include "nvme_api/nvme/shr_hal_nvme.h"
#include "typedef.h"
#include "vuc/VUC_DumpTable.h"
#include "vuc/VUC_ReadFlash.h"
#include "vuc/VUC_SmartRescue.h"
#include "vuc/VUC_SmartRescue_api.h"


#if (SMART_RESCUE_EN)
#define SMART_RESCUE_PREREAD_PAGE_NUM            (8) //Preread 8 page for speedup.
#define SMART_RESCUE_PREREAD_4K_NUM              (SMART_RESCUE_PREREAD_PAGE_NUM * FRAMES_PER_PAGE)

#define SMART_RESCUE_RESERVED_SIZE_DATA          (SMART_RESCUE_PREREAD_PAGE_NUM * PLANE_SIZE) //Reserved size for data in VUC_ReadVBDataCop0Read()
#define SMART_RESCUE_PREREAD_DATA_ADDR           (BURNER_HOST_BIN_FILE_BASE)

#define SMART_RESCUE_RESERVED_SIZE_SPARE         (64 * BC_1KB) //Reserved size for spare in VUC_ReadVBDataCop0Read()
#define SMART_RESCUE_SPARE_ECCINFO_SHIFT         (BC_1KB) //Temporarily puts ECCInfo in Spare region
#define SMART_RESCUE_PREREAD_SPARE_ADDR          (SMART_RESCUE_PREREAD_DATA_ADDR + SMART_RESCUE_RESERVED_SIZE_DATA)
#define SMART_RESCUE_PREREAD_SPARE_ECCINFO_ADDR  (SMART_RESCUE_PREREAD_SPARE_ADDR + SMART_RESCUE_SPARE_ECCINFO_SHIFT)

#define SMART_RESCUE_RESERVED_SIZE_ALIGNED_SPARE (SMART_RESCUE_OUTPUT_BUFFER_SIZE) //Reserved size for arranged spare to output
#define SMART_RESCUE_ALIGNED_SPARE_ADDR          (SMART_RESCUE_PREREAD_SPARE_ADDR + SMART_RESCUE_RESERVED_SIZE_SPARE)

#define SMART_RESCUE_OUTPUT_BUFFER_ADDR          (BURNER_VENDOR_BUF_BASE)

#define VUC_DIRECT_READ_ZIP_ADDR_MASK		     (0x2ffffff)


static U8 ubFlag = FALSE;
static U8 gubReceiveCQNum = 0;
static U32 gulECCInfo[8] = {0};
static U16 guwCOP0Tag[8] = {0};

AOM_BURNER static void VUC_ReadVBData(VUC_OPT_HCMD_PTR_t pCmd);
AOM_BURNER static void VUC_ReadVBDataCop0Read(U32 ulPCA, U8 ubFrameNum, U32 ulSeed);
static void VUCReadVBData_Callback(TIEOUT_FORMAT_t uoResult);
AOM_BURNER static void VUC_UnZIPVBData(U32 ulAddr, U32 ulSrc, U8 ubPageIdx, U32 ulTransferCnt);
AOM_VUC static void VUCDirectedReadGetL2P(RWBufferManager_t *pRWBufferManager, L4KData_t *pL4KData, U8 *ubZByte);


#if (HOST_MODE == SATA)
void VUCSmartRescueS17(VUC_OPT_HCMD_PTR_t pCmd)
{
	U32 ulLength = 0;

	ulLength = pCmd->vuc_sqcmd.adm.security_rw.ulTL; //Byte unit

	if (SMART_RESCUE_OUTPUT_BUFFER_SIZE != ulLength) {
		pCmd->ubState = CMD_ERROR;
		gVUCVar.ubVucCmdErrStatus = HOST_ERROR_LENGTH_NOT_SUPPORT;
		return;
	}

	gSATAVar.ulCmdTotalDataSize  = ulLength;
	gSATAVar.ulCmdRemainDataSize = gSATAVar.ulCmdTotalDataSize;
	pCmd->ulTotalLCA = M_BYTE_TO_4K_NUM(ulLength);
	pCmd->ulDoneLCA = 0;

	pCmd->ulCurrentPhysicalMemoryAddr = SMART_RESCUE_OUTPUT_BUFFER_ADDR;

	VUC_ReadVBData(pCmd);

	VUCTriggerDMA(pCmd, ulLength, WCH_RTYPE);

	if (CMD_ERROR == pCmd->ubState) {
		return;
	}

	gVUCVar.ubHostTrigCQNeed = TRUE;
	pCmd->ubState = CMD_STATE_FINISH;

	return;
}
#endif /*(HOST_MODE == SATA)*/

void VUC_ReadVBData(VUC_OPT_HCMD_PTR_t pCmd)
{
	U32 ulPCA, ulAddr;
	U32 ulVBNum, ulTransferCnt, ulSeed, ulTransferSpareCnt;

	DMACParam_t DMACParam;

	ulAddr = pCmd->ulCurrentPhysicalMemoryAddr;
	ulVBNum = gVUCSecuritySPSQ64BVariable.uldw[0]; //VB_ID
	ulTransferCnt = gVUCSecuritySPSQ64BVariable.uldw[1]; //Transfered 64KB data count.

	M_UART(VUC_SMART_RESCUE_, "SR:VRVD:%d,%d ", (U32) ulVBNum, (U32) ulTransferCnt);

	//Init output buffer.
	memset(&DMACParam, 0, sizeof(DMACParam));
	DMACParam.DMACSetValue.ulDestAddr = ulAddr;
	DMACParam.DMACSetValue.ulLowValue = 0;
	DMACParam.DMACSetValue.ulHighValue = 0;
	DMACParam.DMACSetValue.ul32ByteNum = SIZE_IN_32B(SMART_RESCUE_OUTPUT_BUFFER_SIZE);
	DMACParam.DMACSetValue.btValidate = FALSE;
	gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
	DMACSetValue(&DMACParam, gulDMACDirectWaitDone_Callback, 0);
	while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
		DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
	}

	M_CLR_COP0_LCA_CMP();
	M_FIP_VUC_DIRECTED_READ_SOURCE_DATA();
	M_CLR_COP0_CONV(COP0_MT_TEMP_BUF_ADDR_READ);

#if (HOST_MODE == SATA)
	ulSeed = 0;
#else /*(HOST_MODE == SATA)*/
	ulSeed = BURNER_RANDOM_SEED;
#endif /*(HOST_MODE == SATA)*/

	ulTransferSpareCnt = ulTransferCnt / (SMART_RESCUE_DATA_SPARE_RATIO + 1);
	ulTransferCnt = (ulTransferCnt - ulTransferSpareCnt);

	if ((ulTransferCnt != 0) && (ulTransferCnt % SMART_RESCUE_DATA_SPARE_RATIO == 0) && (!ubFlag)) {
		//Prepare Spare 64KB
		M_UART(VUC_SMART_RESCUE_, "S\n");
		ubFlag = TRUE;

		memset(&DMACParam, 0, sizeof(DMACParam));
		DMACParam.ulSourceAddr = SMART_RESCUE_ALIGNED_SPARE_ADDR;
		DMACParam.ulDestAddr = ulAddr;
		DMACParam.ul32ByteNum = SIZE_IN_32B(SMART_RESCUE_RESERVED_SIZE_ALIGNED_SPARE);
		gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
		DMACCopy(DMAC_MODE_NORMAL_QUEUE, &DMACParam, gulDMACDirectWaitDone_Callback, 0);
		while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
			DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
		}
	}
	else {
		//Prepare Data 64KB
		M_UART(VUC_SMART_RESCUE_, "D\n");
		ubFlag = FALSE;

		// Return Unzip data case
		ulPCA = (ulVBNum << gub4kEntrysPerUnitAlignLog) + (ulTransferCnt << gub4kEntrysPerPlaneLog); //VCA

		//Preread for speed up
		if (0 == (ulPCA % SMART_RESCUE_PREREAD_4K_NUM)) {
			memset(&DMACParam, 0, sizeof(DMACParam));
			DMACParam.DMACSetValue.ulDestAddr = SMART_RESCUE_PREREAD_DATA_ADDR;
			DMACParam.DMACSetValue.ulLowValue = 0;
			DMACParam.DMACSetValue.ulHighValue = 0;
			if (0 == (ulPCA % (SMART_RESCUE_DATA_SPARE_RATIO * FRAMES_PER_PAGE))) {
				DMACParam.DMACSetValue.ul32ByteNum = SIZE_IN_32B(SMART_RESCUE_RESERVED_SIZE_DATA + SMART_RESCUE_RESERVED_SIZE_SPARE + SMART_RESCUE_RESERVED_SIZE_ALIGNED_SPARE);
			}
			else {
				DMACParam.DMACSetValue.ul32ByteNum = SIZE_IN_32B(SMART_RESCUE_RESERVED_SIZE_DATA + SMART_RESCUE_RESERVED_SIZE_SPARE);
			}
			DMACParam.DMACSetValue.btValidate = FALSE;
			gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
			DMACSetValue(&DMACParam, gulDMACDirectWaitDone_Callback, 0);
			while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
				DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
			}

			VUC_ReadVBDataCop0Read(ulPCA, SMART_RESCUE_PREREAD_4K_NUM, ulSeed);
		}

		/* -----------------------------------  Unzip data as below ----------------------------------- */
		VUC_UnZIPVBData(ulAddr, SMART_RESCUE_PREREAD_DATA_ADDR, (ulPCA % SMART_RESCUE_PREREAD_4K_NUM) / FRAMES_PER_PAGE, ulTransferCnt);
	}

	return;
}

void VUC_ReadVBDataCop0Read(U32 ulPCA, U8 ubFrameNum, U32 ulSeed)
{
	COP0Status_t eCOP0Status;
	COP0ReadSQPara_t ReadSQPara = {{0}};
	BUF_TYPE_t ulBufPara[FRAMES_PER_PAGE];
	P4KTable16B_t *pP4KTable = NULL;
	U8 ubi, ubj, ubExpectCQNum = 0;
	U32 ubPageNum = ubFrameNum / FRAMES_PER_PAGE;
	FWSetPara_t ulFWSet[FRAMES_PER_PAGE];

	gulVUCDirectedCieOutInfo = 0;
	gpVT->FTL.btSPORDoing = 1;

	cmd_table_t uoCallbackInfo = {(U32)VUCReadVBData_Callback, {0}};
	uoCallbackInfo.ulData.ulFLHInfo.btKeepTag = FALSE;

	for (ubi = 0; ubi < ubPageNum; ubi++) {
		for (ubj = 0; ubj < FRAMES_PER_PAGE; ubj++) {
#if (PS5017_EN)
			ulFWSet[ubj].ulFWSet = DBUF_VUC_BACKUP_READ_L4K_IDX + (ubi * FRAMES_PER_PAGE + ubj);
#else /*(PS5017_EN)*/
			ulFWSet[ubj].ulFWSet = IRAM1_RSV_OFF + ((ubi * FRAMES_PER_PAGE + ubj) * sizeof (P4KTable16B_t));
#endif /*(PS5017_EN)*/
			ulFWSet[ubj].ubZInfo = MAX_ZINFO;
			ulFWSet[ubj].btDummyEn = FALSE;
			ulFWSet[ubj].ubCrossPageCopyOffset = 0;
			ulFWSet[ubj].btCrossPage = FALSE;
			ulBufPara[ubj].A.ulBUF_ADR = SMART_RESCUE_PREREAD_DATA_ADDR + ((ubi * FRAMES_PER_PAGE + ubj) * DEF_KB(4));
		}

		COP0API_FillCOP0ReadSQUserData0Para(COP0_R_INITIAL_SCAN, &ReadSQPara);
		ReadSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_VBRMP;
		ReadSQPara.UserData0.TagID = TAG_ID_ALLOCATE;
		ReadSQPara.ulPCA.ulAll = ulPCA + (ubi * FRAMES_PER_PAGE);
		ReadSQPara.UserData0.btRUTBps = FALSE;
		ReadSQPara.UserData0.btVBRMPBps = FALSE;
		ReadSQPara.UserData0.AttrMTTemplate = COP0_MT_TEMP_BUF_ADDR_READ;
		ReadSQPara.UserData0.L4KNum = FRAMES_PER_PAGE - 1;
		ReadSQPara.UserData0.DataDef |= (COP0_SEED_EN_BIT | COP0_FWSET_EN_BIT | COP0_BUFVLD_EN_BIT);
		ReadSQPara.UserData0.btVUCRead = FALSE;
		ReadSQPara.BufVld.ubBufType = BUF_TYPE_A;
		ReadSQPara.BufVld.pulBufInfoPtr = ulBufPara;
		ReadSQPara.UserData0.btReadBackUp4K = TRUE;
		ReadSQPara.UserData0.btReadTieOutMethod = TRUE;
		ReadSQPara.pulFWSetPtr = ulFWSet;
		ReadSQPara.ulSeed.ulAll = ulSeed;
#if (PS5017_EN)
		//Use 3D Seed Rule to read user data from non-SystemArea. ReadSQPara.ulSeed.ulAll = Don't_care.
		ReadSQPara.UserData0.btUseParamSeed = FALSE;
		ReadSQPara.UserData0.btSysArea = FALSE;
#endif /*(PS5017_EN)*/

		eCOP0Status = COP0API_SendReadSQ(&ReadSQPara, &uoCallbackInfo);
		M_FW_ASSERT(ASSERT_VUC_0x0ACB, eCOP0Status.btSendCmdSuccess);
		while (0 != guwDebugBurnerErrorCode) {;}
		ubExpectCQNum++;
		guwCOP0Tag[ubi] = ReadSQPara.UserData0.TagID;
		gulECCInfo[ubi] = 0;
	}

	gubReceiveCQNum = 0;

	while (gubReceiveCQNum < ubExpectCQNum) {
		FWCop0Waiting();
	}

	gpVT->FTL.btSPORDoing = 0;

#if (PS5017_EN)
	pP4KTable = (P4KTable16B_t *)(DBUF_VUC_BACKUP_READ_L4K_BASE);
#else /*(PS5017_EN)*/
	pP4KTable = (P4KTable16B_t *)(IRAM_BASE + IRAM1_RSV_OFF);
#endif /*(PS5017_EN)*/

	M_FW_ASSERT(ASSERT_VUC_0x0ACC, (SMART_RESCUE_SPARE_ECCINFO_SHIFT >= (ubFrameNum * DEF_16B)));
	while (0 != guwDebugBurnerErrorCode) {;}
	for (ubi = 0; ubi < ubFrameNum; ubi++) {
		memcpy((void *)(SMART_RESCUE_PREREAD_SPARE_ADDR + ubi * DEF_16B), (void *)& pP4KTable[ubi].ulL4K_LCA, DEF_4B);
		memcpy((void *)(SMART_RESCUE_PREREAD_SPARE_ADDR + ubi * DEF_16B + DEF_4B), (void *)& pP4KTable[ubi].Para0x04.ulAll, DEF_4B);
		memcpy((void *)(SMART_RESCUE_PREREAD_SPARE_ADDR + ubi * DEF_16B + DEF_8B), (void *)& pP4KTable[ubi].Para0x08.ulAll, DEF_4B);
		memcpy((void *)(SMART_RESCUE_PREREAD_SPARE_ADDR + ubi * DEF_16B + (DEF_4B + DEF_8B)), (void *)& pP4KTable[ubi].Para0x0C.ulAll, DEF_4B);
		memcpy((void *)(SMART_RESCUE_PREREAD_SPARE_ECCINFO_ADDR + ubi * DEF_4B), (void *)& gulECCInfo[ubi / FRAMES_PER_PAGE], sizeof(U32));
	}

	return;
}

void VUCReadVBData_Callback(TIEOUT_FORMAT_t uoResult)
{
	U8 ubi;

	for (ubi = 0; ubi < SMART_RESCUE_PREREAD_PAGE_NUM; ubi++) {
		if (uoResult.HL.B32_to_B63.Read_1st_2.uwTAG == guwCOP0Tag[ubi]) {
			if (TMSG_TYPE_ERROR == uoResult.HL.B32_to_B63.Info.btMSG_TYPE) {
				gulECCInfo[ubi] |= VUC_DIRECT_READ_CRC_BIT;
				gulECCInfo[ubi] |= VUC_DIRECT_READ_ECC_BIT;
			}
			if (TRUE == uoResult.HL.B32_to_B63.Read_1st_2.btErasePage) {
				gulECCInfo[ubi] |= VUC_DIRECT_READ_ERASE_BIT;
			}
			break;
		}
	}

	gubReceiveCQNum++;
}

void VUC_UnZIPVBData(U32 ulAddr, U32 ulSrc, U8 ubPageIdx, U32 ulTransferCnt)
{
	DMACParam_t DMACParam = {{0}};
	U8 ubi, ubZInfo, ubDataNumInOneFrame = 0, ubNewFrame = 0, ubUnc = FALSE;
	//U8 ubZByte = 0;
	static U8 ubZByte = 0;
	U32 ulTotalDealLength = 0, ulP2LIdx = 0;
	//RWBufferManager_t RWBufferManagerLocal;
	static RWBufferManager_t RWBufferManagerLocal;
	//PlaneInfoManager_t PlaneInfoManager;
	static PlaneInfoManager_t PlaneInfoManager;
	L2PEntry_t P2LTable[MAX_ZIP_DATA_IN_ONE_FW_PLANE];

	memset(&RWBufferManagerLocal, 0, sizeof(RWBufferManager_t));
	memset(&PlaneInfoManager, 0, sizeof(PlaneInfoManager_t));
	RWBufferManagerLocal.pPlaneInfoManager = &PlaneInfoManager;

	P4KTable16B_t *pulSpare = (P4KTable16B_t *)(ulSrc + SMART_RESCUE_RESERVED_SIZE_DATA);
	U32 *pulInterruptInfo = (U32 *)(ulSrc + SMART_RESCUE_RESERVED_SIZE_DATA + SMART_RESCUE_SPARE_ECCINFO_SHIFT);

	for (ubi = 0 ; ubi < FRAMES_PER_PAGE ; ubi++) {
		RWBufferManagerLocal.pPlaneInfoManager->PlaneInfo[RWBufferManagerLocal.ubCurrentPlaneIndex].ulRAMAddrOfRWBuffer[ubi] = (U32)ulSrc + (ubPageIdx * DEF_KB(16)) + (ubi * 4096);
		RWBufferManagerLocal.pPlaneInfoManager->PlaneInfo[RWBufferManagerLocal.ubCurrentPlaneIndex].pP4KTable[ubi] = &pulSpare[ubPageIdx * FRAMES_PER_PAGE + ubi];
	}


	for (ubi = 0; ubi < FRAMES_PER_PAGE; ubi++) {
		if (pulInterruptInfo[(ubPageIdx * FRAMES_PER_PAGE) + ubi] & VUC_DIRECT_READ_ECC_BIT) {
			ubUnc = TRUE;
		}
		else if (pulInterruptInfo[(ubPageIdx * FRAMES_PER_PAGE) + ubi] & VUC_DIRECT_READ_ERASE_BIT) {
			ubUnc = TRUE;
		}
		else if (pulInterruptInfo[(ubPageIdx * FRAMES_PER_PAGE) + ubi] & VUC_DIRECT_READ_CRC_BIT) {
			ubUnc = TRUE;
		}

		if (ubUnc) {
			M_P4K_PTR_SET_SPRV(RWBufferManagerLocal.pPlaneInfoManager->PlaneInfo[RWBufferManagerLocal.ubCurrentPlaneIndex].pP4KTable[ubi], 0);
			RWBufferManagerLocal.pPlaneInfoManager->PlaneInfo[RWBufferManagerLocal.ubCurrentPlaneIndex].uwP4KHeader[ubi] = 0;
			RWBufferManagerLocal.pPlaneInfoManager->PlaneInfo[RWBufferManagerLocal.ubCurrentPlaneIndex].ulLCA[ubi] = SPARE_LCA_BAD;
		}
		else {
			RWBufferManagerLocal.pPlaneInfoManager->PlaneInfo[RWBufferManagerLocal.ubCurrentPlaneIndex].pP4KTable[ubi]->ulL4K_LCA = pulSpare[ubPageIdx * FRAMES_PER_PAGE + ubi].ulL4K_LCA; //Redundant
			RWBufferManagerLocal.pPlaneInfoManager->PlaneInfo[RWBufferManagerLocal.ubCurrentPlaneIndex].pP4KTable[ubi]->Para0x04.ulAll = pulSpare[ubPageIdx * FRAMES_PER_PAGE + ubi].Para0x04.ulAll; //Redundant
			RWBufferManagerLocal.pPlaneInfoManager->PlaneInfo[RWBufferManagerLocal.ubCurrentPlaneIndex].pP4KTable[ubi]->Para0x08.ulAll = pulSpare[ubPageIdx * FRAMES_PER_PAGE + ubi].Para0x08.ulAll; //Redundant
			RWBufferManagerLocal.pPlaneInfoManager->PlaneInfo[RWBufferManagerLocal.ubCurrentPlaneIndex].pP4KTable[ubi]->Para0x0C.ulAll = pulSpare[ubPageIdx * FRAMES_PER_PAGE + ubi].Para0x0C.ulAll; //Redundant
			RWBufferManagerLocal.pPlaneInfoManager->PlaneInfo[RWBufferManagerLocal.ubCurrentPlaneIndex].uwP4KHeader[ubi] = M_P4K_PTR_GET_HEAD(RWBufferManagerLocal.pPlaneInfoManager->PlaneInfo[RWBufferManagerLocal.ubCurrentPlaneIndex].pP4KTable[ubi]);
			RWBufferManagerLocal.pPlaneInfoManager->PlaneInfo[RWBufferManagerLocal.ubCurrentPlaneIndex].ulLCA[ubi] = RWBufferManagerLocal.pPlaneInfoManager->PlaneInfo[RWBufferManagerLocal.ubCurrentPlaneIndex].pP4KTable[ubi]->ulL4K_LCA;
		}

		ubUnc = FALSE;
	}

	if (FALSE == ubUnc) {
		//L4KData_t L4KData;
		static L4KData_t L4KData;
		memset(&L4KData, 0, sizeof(L4KData));
		while (!RWBufferManagerLocal.ubCurrentPlaneIndex) {
			VUCDirectedReadGetL2P(&RWBufferManagerLocal, &L4KData, &ubZByte);
			P2LTable[ulP2LIdx].ulPCA.ulAll = L4KData.ulPCA.ulAll;
			P2LTable[ulP2LIdx].ulLCA = L4KData.ulLCA;
			P2LTable[ulP2LIdx].ubZByte = ubZByte;
			P2LTable[ulP2LIdx].ubCurrent4KIdx = L4KData.ub4KFrameIndex;
			P2LTable[ulP2LIdx].ulFwSet = L4KData.ulFWSet;
			P2LTable[ulP2LIdx].uwE3D4KFirst = L4KData.uwE3D4KFirst;
			P2LTable[ulP2LIdx].ubE3D4KSecond = L4KData.ubE3D4KSecond;
			ulP2LIdx++;
		}
	}
	RWBufferManagerLocal.ubCurrentPlaneIndex = 0;

	// Set value 0 from offset 0k ~ 80k
	memset(&DMACParam, 0, sizeof(DMACParam_t));
	DMACParam.DMACSetValue.ulDestAddr = ulAddr;
	DMACParam.DMACSetValue.ulLowValue = 0;
	DMACParam.DMACSetValue.ulHighValue = 0;
	DMACParam.DMACSetValue.ul32ByteNum = SIZE_IN_32B(SMART_RESCUE_OUTPUT_BUFFER_SIZE);
	DMACParam.DMACSetValue.btValidate = FALSE;
	gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
	DMACSetValue(&DMACParam, gulDMACDirectWaitDone_Callback, 0);
	while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
		DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
	}

	for (ubi = 0 ; ubi < ulP2LIdx ; ubi++) {
		M_ZINFO_FROM_ZBYTE_GET(ubZInfo, P2LTable[ubi].ubZByte);

		if (ubNewFrame != P2LTable[ubi].ubCurrent4KIdx) {
			ubDataNumInOneFrame = 0;
			ubNewFrame = P2LTable[ubi].ubCurrent4KIdx;
		}

		if (SPARE_LCA_DUMMY != P2LTable[ubi].ulLCA) {
			// TODO: AES case need to be considered
			memset(&DMACParam, 0, sizeof(DMACParam_t));
			DMACParam.DMACDataCopy.ul32ByteNum = M_KBYTE_TO_32BYTE_ALIGN(SIZE_4B);
			DMACParam.DMACDataCopy.ulMask = 0xFF;
			DMACParam.DMACDataCopy.ulLCA = P2LTable[ubi].ulLCA;
			DMACParam.DMACDataCopy.ubZipMode = DMAC_COPY_ZIPMODE_UNZIP;
			DMACParam.DMACDataCopy.ulSourceAddr = (ulSrc + (ubPageIdx * PLANE_SIZE) + ulTotalDealLength) & VUC_DIRECT_READ_ZIP_ADDR_MASK;
			DMACParam.DMACDataCopy.ulDestAddr =  ulAddr + ubDataNumInOneFrame * FRAME_SIZE + P2LTable[ubi].ubCurrent4KIdx * PLANE_SIZE;
			DMACParam.DMACDataCopy.ubZInfoSource = ubZInfo;

			gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
			DMACDataCopyFunction(&DMACParam, gulDMACDirectWaitDone_Callback, NULL, DMAC_NO_USE_VALIDBMP);
			while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
				DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
			}
		}

		ulTotalDealLength += ((ubZInfo + 1) * SIZE_512B);

		//Copy 20B Spare info to output buffer for each zipped data.
		//ubDataNumInOneFrame = 0 ~ at most 3. P2LTable[ubi].ubCurrent4KIdx = 0 ~ 3
		/*
			1 FWPlane = [4K Frame, 4K Frame, 4K Frame, 4K Frame]
			Each Frame consists of at most 4 zipped HostData, e.g., [1K, 1K, 1K, 1K], [2K, 1K, 1K], etc.
			Each HostData has 20B Spare info.

			Arrange the output Spare buffer as follows:
				1KB aligned for each FWPlane.
				80B alinged for each Frame.
				20B alinged for each HostData in Frame.
				E.g., Spare of Frame 0            Spare of Frame 1              Spare of Frame 2             Spare of Frame 3
				      [20B X, 20B X, 20B X, 20B X] [20B X, 20B X, 20B 0, 20B 0] [20B X, 20B 0, 20B 0, 20B 0] [20B X, 20B 0, 20B 0, 20B 0]
				(X: non-zeros, 0: zeros)
		*/
		U32 ulDestAddr = SMART_RESCUE_ALIGNED_SPARE_ADDR + (ulTransferCnt % SMART_RESCUE_DATA_SPARE_RATIO) * (SMART_RESCUE_SPARE_ALIGN_BYTE) + P2LTable[ubi].ubCurrent4KIdx * (SIZE_16B + SIZE_64B) + ubDataNumInOneFrame * (SIZE_4B + SIZE_16B);
		memcpy((void *)(ulDestAddr), &P2LTable[ubi].ulLCA, SIZE_4B);
		memcpy((void *)(ulDestAddr + SIZE_4B), (void *)&pulSpare[(ubPageIdx * FRAMES_PER_PAGE) + P2LTable[ubi].ubCurrent4KIdx].Para0x04.ulAll, SIZE_4B);
		memcpy((void *)(ulDestAddr + SIZE_8B), &(RWBufferManagerLocal.pPlaneInfoManager->PlaneInfo[0].uwP4KHeader[P2LTable[ubi].ubCurrent4KIdx]), SIZE_2B);
		memcpy((void *)(ulDestAddr + SIZE_2B + SIZE_8B), & P2LTable[ubi].uwE3D4KFirst, SIZE_2B);
		memcpy((void *)(ulDestAddr + SIZE_12B), & P2LTable[ubi].ubE3D4KSecond, SIZE_1B);

		U8 ubRANDandPCACRC = RWBufferManagerLocal.pPlaneInfoManager->PlaneInfo[0].pP4KTable[P2LTable[ubi].ubCurrent4KIdx]->Para0x0C.BitMap.RAND + ((RWBufferManagerLocal.pPlaneInfoManager->PlaneInfo[0].pP4KTable[P2LTable[ubi].ubCurrent4KIdx]->Para0x0C.BitMap.PCACRC) << 4);
		U16 uwCRC16 = RWBufferManagerLocal.pPlaneInfoManager->PlaneInfo[0].pP4KTable[P2LTable[ubi].ubCurrent4KIdx]->Para0x0C.BitMap.uwCRC16;
		memcpy((void *)(ulDestAddr + SIZE_1B + SIZE_12B), &ubRANDandPCACRC, SIZE_1B);
		memcpy((void *)(ulDestAddr + SIZE_2B + SIZE_12B), &uwCRC16, SIZE_2B);
		memcpy((void *)(ulDestAddr + SIZE_16B), &pulInterruptInfo[P2LTable[ubi].ubCurrent4KIdx], SIZE_4B);

		ubDataNumInOneFrame++;
	}
}

void VUCDirectedReadGetL2P(RWBufferManager_t *pRWBufferManager, L4KData_t *pL4KData, U8 *ubZByte)
{
	U8 ubScanSectorCnt = 0;
	U8 ubCrossFrameWithPreviousFrame = FALSE;

	U32 ulL4KDataLocalPCA;
	U8 ubL4KDataLocalZByte;
	U8 ubL4KDataLocalCrossFrame;
	U8 ubL4KDataLocalLPCRC = 0;

	U32 ulRWBufferLocalPCA;

	// scan P4K_Header, and locate a L4K data
	while ( TRUE ) {

		// update scanning variables
		PORUpdateScanningL4KDataVar( pRWBufferManager );

		// assign ubCurrentBit
		pRWBufferManager->ubCurrentBit = (pRWBufferManager->pPlaneInfoManager->PlaneInfo[ pRWBufferManager->ubCurrentPlaneIndex ].uwP4KHeader[pRWBufferManager->ubCurrent4KFrameIndex] >> pRWBufferManager->ubCurrentSectorIndex) & BIT0;

		// assign ubPreviousBit in bounary case
		if ( pRWBufferManager->ubCurrentSectorIndex == 0 ) {
			pRWBufferManager->ubPreviousBit = pRWBufferManager->ubCurrentBit;
		}
		// reach last sector in this frame and this L4K is not cross-frame with next frame
		else if ( (pRWBufferManager->ubCurrentSectorIndex == 7) &&
			(((pRWBufferManager->pPlaneInfoManager->PlaneInfo[ pRWBufferManager->ubCurrentPlaneIndex ].uwP4KHeader[pRWBufferManager->ubCurrent4KFrameIndex] >> 12) & 0xF) == 0) ) {

			if (pRWBufferManager->ubCurrentBit == pRWBufferManager->ubPreviousBit) {
				pRWBufferManager->ubLastL4K = TRUE;
				ubScanSectorCnt++;
				(pRWBufferManager->ubCurrentSectorIndex)++;
			}
		}

		// bit changed or reach the lastL4K (get a L4K)
		if ( (pRWBufferManager->ubCurrentBit != pRWBufferManager->ubPreviousBit) || pRWBufferManager->ubLastL4K ) {

			// update previous bit
			pRWBufferManager->ubPreviousBit = pRWBufferManager->ubCurrentBit;

			// check ubFirstL4K
			if ( pRWBufferManager->ubFirstL4K == TRUE ) {

				// check cross-frame with previous 4K frame or not
				if ( (pRWBufferManager->pPlaneInfoManager->PlaneInfo[ pRWBufferManager->ubCurrentPlaneIndex ].uwP4KHeader[pRWBufferManager->ubCurrent4KFrameIndex] >> 8) & 0xF ) {
					ubCrossFrameWithPreviousFrame = TRUE;
				}

				// reset ubFirstL4K
				pRWBufferManager->ubFirstL4K = FALSE;

			}

			// L4K with Zcode=7(out of compression) case
			if ( ubScanSectorCnt == SECTORS_PER_4K ) {

				// assign pL4KData->ulLCA
				// cross-frame with previous 4K frame, and the P4K_LCA of the previous 4K frame equals the LCA of current L4K data
				if ( ubCrossFrameWithPreviousFrame == TRUE ) {
					pL4KData->ulLCA = pRWBufferManager->pPlaneInfoManager->PlaneInfo[ pRWBufferManager->ubCurrentPlaneIndex ].ulLCA[pRWBufferManager->ubCurrent4KFrameIndex - 1];
					pL4KData->ulFWSet = pRWBufferManager->pPlaneInfoManager->PlaneInfo[ pRWBufferManager->ubCurrentPlaneIndex ].pP4KTable[pRWBufferManager->ubCurrent4KFrameIndex - 1]->Para0x04.ulAll;
					pL4KData->uwE3D4KFirst = pRWBufferManager->pPlaneInfoManager->PlaneInfo[ pRWBufferManager->ubCurrentPlaneIndex ].pP4KTable[pRWBufferManager->ubCurrent4KFrameIndex - 1]->Para0x08.BitMap.uwE3D4K;
					pL4KData->ubE3D4KSecond = pRWBufferManager->pPlaneInfoManager->PlaneInfo[ pRWBufferManager->ubCurrentPlaneIndex ].pP4KTable[pRWBufferManager->ubCurrent4KFrameIndex - 1]->Para0x0C.BitMap.ubE3D4K;
				}
				// L4K is out of compression, and does not cross-frame with previous 4K frame,
				// and the current P4K_LCA equals the LCA of current L4K data
				else {
					pL4KData->ulLCA = pRWBufferManager->pPlaneInfoManager->PlaneInfo[ pRWBufferManager->ubCurrentPlaneIndex ].ulLCA[pRWBufferManager->ubCurrent4KFrameIndex];
					pL4KData->ulFWSet = pRWBufferManager->pPlaneInfoManager->PlaneInfo[ pRWBufferManager->ubCurrentPlaneIndex ].pP4KTable[pRWBufferManager->ubCurrent4KFrameIndex]->Para0x04.ulAll;
					pL4KData->uwE3D4KFirst = pRWBufferManager->pPlaneInfoManager->PlaneInfo[ pRWBufferManager->ubCurrentPlaneIndex ].pP4KTable[pRWBufferManager->ubCurrent4KFrameIndex]->Para0x08.BitMap.uwE3D4K;
					pL4KData->ubE3D4KSecond = pRWBufferManager->pPlaneInfoManager->PlaneInfo[ pRWBufferManager->ubCurrentPlaneIndex ].pP4KTable[pRWBufferManager->ubCurrent4KFrameIndex]->Para0x0C.BitMap.ubE3D4K;
				}
			}
			// L4K with Zcode=1~6(with compression) case
			else {

				// assign pL4KData->ulLCA
				// last L4K data of this 4K frame, and it is a DUMMY
				if ( pRWBufferManager->ubLastL4K == TRUE &&
					pRWBufferManager->pPlaneInfoManager->PlaneInfo[ pRWBufferManager->ubCurrentPlaneIndex ].ulLCA[pRWBufferManager->ubCurrent4KFrameIndex] == SPARE_LCA_DUMMY ) {
					pL4KData->ulLCA = SPARE_LCA_DUMMY;
				}
				// valid L4K data with Zcode=1~6(with compression) case
				else {
					// Reference: dzip_RegSpec.docx, chapter: 3.5 DZIP data format
					// Format: ZData + LCA(4B) + bitmap(1B) + FW set(3B) + E3D4K(3B) + ZData(5B)
					pL4KData->ulLCA = *((volatile unsigned long *)(pRWBufferManager->pPlaneInfoManager->PlaneInfo[pRWBufferManager->ubCurrentPlaneIndex].ulRAMAddrOfRWBuffer[pRWBufferManager->ubCurrent4KFrameIndex] + (pRWBufferManager->ubCurrentSectorIndex * 512) - 16));
					pL4KData->ulFWSet = *((volatile unsigned long *)(pRWBufferManager->pPlaneInfoManager->PlaneInfo[pRWBufferManager->ubCurrentPlaneIndex].ulRAMAddrOfRWBuffer[pRWBufferManager->ubCurrent4KFrameIndex] + (pRWBufferManager->ubCurrentSectorIndex * 512) - 12));
					pL4KData->uwE3D4KFirst = *((volatile unsigned short *)(pRWBufferManager->pPlaneInfoManager->PlaneInfo[pRWBufferManager->ubCurrentPlaneIndex].ulRAMAddrOfRWBuffer[pRWBufferManager->ubCurrent4KFrameIndex] + (pRWBufferManager->ubCurrentSectorIndex * 512) - 8));
					pL4KData->ubE3D4KSecond = *((volatile unsigned char *)(pRWBufferManager->pPlaneInfoManager->PlaneInfo[pRWBufferManager->ubCurrentPlaneIndex].ulRAMAddrOfRWBuffer[pRWBufferManager->ubCurrent4KFrameIndex] + (pRWBufferManager->ubCurrentSectorIndex * 512) - 6));
				}
			}

			// terminate
			break;
		}
		// L4K data is scanning, check next sector
		else {
			// update ubScanSectorCnt
			ubScanSectorCnt++;

			// update ubCurrentSectorIndex
			(pRWBufferManager->ubCurrentSectorIndex)++;
		}
	}

	// assign pL4KData->ubType
	if (SPARE_LCA_JOURNAL == pL4KData->ulLCA) {
		pL4KData->ubType = L4K_JOURNAL_DATA;
	}
	else if ((SPARE_LCA_DUMMY == pL4KData->ulLCA) || (SPARE_LCA_BAD == pL4KData->ulLCA)) {
		pL4KData->ubType = L4K_DUMMY_DATA;
	}
	else {
		pL4KData->ubType = L4K_NORMAL_DATA;
	}

	// assign pL4KData->ubSectorCnt
	pL4KData->ubSectorCnt = ubScanSectorCnt;

	// assign pL4KData->ub4KFrameIndex & pL4KData->ubStartSectorIndex
	if ( ubCrossFrameWithPreviousFrame == TRUE ) {
		pL4KData->ub4KFrameIndex = pRWBufferManager->ubCurrent4KFrameIndex - 1;
		pL4KData->ubStartSectorIndex = SECTORS_PER_4K + pRWBufferManager->ubCurrentSectorIndex - ubScanSectorCnt;
	}
	else {
		pL4KData->ub4KFrameIndex = pRWBufferManager->ubCurrent4KFrameIndex;
		pL4KData->ubStartSectorIndex = pRWBufferManager->ubCurrentSectorIndex - ubScanSectorCnt;
	}

	// assign pL4KData->ubPlaneIndex
	pL4KData->ubPlaneIndex = pRWBufferManager->ubCurrentPlaneIndex;



	// assign pL4KData->ulPCA (i.e. Old_PCA)
	M_FWPCA_PCA_GET(ulRWBufferLocalPCA, pRWBufferManager->pPlaneInfoManager->ulReadPCA[pRWBufferManager->ubCurrentPlaneIndex].ulAll);
	M_ZBYTE_FROM_SECTOR_GET(ubL4KDataLocalZByte, ubScanSectorCnt);
	ubL4KDataLocalCrossFrame = ubCrossFrameWithPreviousFrame;
	ulL4KDataLocalPCA = ulRWBufferLocalPCA + pL4KData->ub4KFrameIndex;
	M_FWPCA_SET(pL4KData->ulPCA.ulAll, ulL4KDataLocalPCA, ubL4KDataLocalZByte, ubL4KDataLocalCrossFrame, ubL4KDataLocalLPCRC);
	*ubZByte = ubL4KDataLocalZByte;
	// update scanning variables
	PORUpdateScanningL4KDataVar( pRWBufferManager );

	return ;
}
#endif /*(SMART_RESCUE_EN)*/
