/**************************************************************************/
/*                                                                        */
/*           Copyright (c) 2000-2017 by Phison Electronics Corp.          */
/*                                                                        */
/**************************************************************************/

/**************************************************************************/
/*                                                                        */
/*  FILE : error_monitor.c                 PROJECT : PS5011               */
/*                                                                        */
/*                                                                        */
/*  DESCRIPTION                                                           */
/*                                                                        */
/*    This file implements implement error handler		                  */
/*                                                                        */
/*  RELEASE HISTORY                                                       */
/*                                                                        */
/*    DATE              NAME                DESCRIPTION                   */
/*                                                                        */
/*  2017-05-16     Eddie Chiang             Initial Version 1.0           */
/*                                                                        */
/**************************************************************************/
#include "typedef.h"
#include "hal/pic/uart/uart_api.h"
#include "debug/debug.h"
#if (RDT_MODE_EN)
#if (PS5017_EN)
#include "rdt/rdt_api.h"
#else
#include "rdt/rdt.h"
#include "rdt/rdt_pio_force_wr_log.h"
#endif
#endif /* (RDT_MODE_EN) */

void CPUErrorHandler(U32 ulErrorType, U32 ulErrorPC, U32 ulErrorDFAR, U32 ulErrorIFAR)
{
	/*--------------------------------------------------------
		gDebugInfoBTCM.ulErrorType :
			0x13 : SWI Handler
			0x17 : Abort Handler (Data abort/Pre-fetch abort)
			0x1B : Undefined Handler
	--------------------------------------------------------*/
#if (RDT_MODE_EN)
	M_UART(RDT_INIT_, "[CPU_error_handler] Type: %x, PC: %x, DFAR: %x, IFAR: %x\n", ulErrorType, ulErrorPC, ulErrorDFAR, ulErrorIFAR);
	rdt_api_pio_force_write_fail_log(&gRdtApiStruct, RDT_PIO_FORCE_WR_LOG_PIO_CTL_ISSUE | RDT_PIO_FORCE_WR_LOG_CPU_ABORT_ERROR, ulErrorDFAR, TRUE);
#else
	M_UART(ASSERT_, "[CPU_error_handler] Type: %x, PC: %x, DFAR: %x, IFAR: %x\n", ulErrorType, ulErrorPC, ulErrorDFAR, ulErrorIFAR);
#endif
	M_FW_CRITICAL_ASSERT(ASSERT_CPU_0x0237, FALSE);
}

