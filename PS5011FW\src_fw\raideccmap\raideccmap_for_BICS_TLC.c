#include "raideccmap.h"
#include "raideccmap_api.h"
#include "raideccmap_inline_api.h"
#include "typedef.h"
#include "fw_common.h"
#include "ftl/ftl_api.h"
#include "retry/err_hdl_fpl_RS.h"
#include "retry/retry_api.h"


#if (FALSE == MICRON_FSP_EN)
U16 RaidECCMapGetParityTagIdxByCoord(RaidECCMapParityMapEnum_t ParityMapMode, U16 uwX, U16 uwY, U8 ubPlane)
{
	U16 uwFWParityTagIdx = 0;
	U16 uwParityTagBase = 0;
	U16 uwShiftBase = 0;
	U16 uwShiftOffset = 0;
	U16 uwModNum = 0;
	U16 uwBaseModNum = 0;
	U16 uwBaseOffset = 0;
	U8 ubPlaneNumOfString = 4;

	switch (ParityMapMode) {
	case RAIDECCMAP_PARITYMAP_MODE_GR_SLC:
		uwBaseModNum = RAIDECCMAP_SLC_WORDLINE_PROTECT_NUM * RAIDECCMAP_PLANE_PROTECT_NUM;
		uwModNum = RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM;
		uwParityTagBase = RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM;
		uwShiftBase = (uwY / uwBaseModNum);
		uwShiftOffset = uwX;
		uwBaseOffset = (uwY % uwBaseModNum);

		if (MULTI_PLANE_PROTECTION && (ubPlane % RAIDECCMAP_PLANE_PROTECT_NUM)) {
			uwBaseOffset += RAIDECCMAP_SLC_WORDLINE_PROTECT_NUM;
		}

		break;
	case RAIDECCMAP_PARITYMAP_MODE_GR_XLC:
	case RAIDECCMAP_PARITYMAP_MODE_GC:
		uwBaseModNum = RAIDECCMAP_XLC_WORDLINE_PROTECT_NUM * RAIDECCMAP_PLANE_PROTECT_NUM;
		uwModNum = RAIDECCMAP_1WL_PROTECTION_IN_XLC_PARITY_TAG_IDX_NUM;
		uwParityTagBase = RAIDECCMAP_1WL_PROTECTION_IN_XLC_PARITY_TAG_IDX_NUM;

		uwBaseOffset = (uwY % uwBaseModNum);

		if (MULTI_PLANE_PROTECTION && (ubPlane % gubBurstsPerBank)) {
			uwBaseOffset += RAIDECCMAP_XLC_WORDLINE_PROTECT_NUM;
		}

		uwShiftOffset = uwX;
		uwShiftBase = ((uwY / uwBaseModNum) % ubPlaneNumOfString) * gubLMUNumber;
		break;
	case RAIDECCMAP_PARITYMAP_MODE_TABLE:
	default:
		break;
	}

	uwFWParityTagIdx = (uwParityTagBase * (uwBaseOffset % uwBaseModNum)) + ((uwShiftBase + uwShiftOffset) % uwModNum);

	return uwFWParityTagIdx;
}


U16 RaidECCMapGetParityTagIdxByProgramOrderTable(RaidECCMapParityMapEnum_t ParityMapMode, U32 ulPlaneIdx)
{
	U16 uwFWTagIdx = 0;
	if (RAIDECCMAP_PARITYMAP_MODE_TABLE == ParityMapMode) {
		uwFWTagIdx = (ulPlaneIdx / (gRS.ubRSTableDataNum + RAIDECC_PARITY_PAGE_NUM)) % 2;
	}
	else if (RAIDECCMAP_PARITYMAP_MODE_GR_SLC == ParityMapMode) {
		U16 uwSuperPageIdx = ulPlaneIdx / gubPlanesPerSuperPage;
		U8 ubPlaneOffset = ulPlaneIdx % gubPlanesPerSuperPage;
		U16 uwX, uwY = 0;
		uwX = uwSuperPageIdx % RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM;
		uwY = uwSuperPageIdx / RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM;

		uwFWTagIdx = RaidECCMapGetParityTagIdxByCoord(ParityMapMode, uwX, uwY, ubPlaneOffset);
	}
	else {
		U16 uwSuperPageIdx = 0;
		U16 uwX, uwY = 0;
		U8 ubPlaneOffset = 0;
		FTLPlaneInx2Physical(ulPlaneIdx, &uwSuperPageIdx, &ubPlaneOffset);

		uwX = FTLGetCoord(uwSuperPageIdx, IM_GETCOORD_X_VAL);
		uwY = FTLGetCoord(uwSuperPageIdx, IM_GETCOORD_Y_VAL);
		uwFWTagIdx = RaidECCMapGetParityTagIdxByCoord(ParityMapMode, uwX, uwY, ubPlaneOffset);
	}
	return uwFWTagIdx;
}

U16 RaidECCMapGetPreParityTagIdxPlaneIdxByCoord(RaidECCMapParityMapEnum_t ParityMapMode, U16 uwX, U16 uwY)
{
	U16 uwSuperPageIdx = 0xFFFF;
	U8  ubNoPreSuperPage = FALSE;

	switch (ParityMapMode) {
	case RAIDECCMAP_PARITYMAP_MODE_GR_XLC:
	case RAIDECCMAP_PARITYMAP_MODE_GC:
		if (0 == uwX) {
			uwX = RAIDECCMAP_1WL_PROTECTION_IN_XLC_PARITY_TAG_IDX_NUM;
		}
		else {
			uwX --;
		}

		if (uwY > 4) {
			uwY -= 2;
		}
		else if (uwY == 3) {
			if (0 == (uwX % 3)) {
				ubNoPreSuperPage = TRUE;
			}
			else {
				uwY -= 2;
			}
		}
		else if (uwY == 2) {
			if (2 != (uwX % 3)) {
				ubNoPreSuperPage = TRUE;
			}
			else {
				uwY -= 2;
			}
		}
		else {
			ubNoPreSuperPage = TRUE;
		}
		break;
	case RAIDECCMAP_PARITYMAP_MODE_GR_SLC:
	case RAIDECCMAP_PARITYMAP_MODE_TABLE:
	default:
		break;
	}
	if (FALSE == ubNoPreSuperPage) {
		uwSuperPageIdx = RaidECCMapInvertCoord(uwX, uwY);
	}

	return uwSuperPageIdx;
}

U16 RaidECCMapInvertCoord(U8 ubX, U16 uwY)
{
	return (uwY * 12 + ubX);
}

U32 RaidECCMapSwapFlowFindLastParityPlanePtr(U8 ubRSEncodeMode, RaidECCMapParityMapEnum_t ubParityMapMode, U8 ubIsSlcMode, U16 uwPageIdx, U8 ubPlane)
{
	U8 ubPageShift = 0;
	U8 ubOneWLTagNum = 0;
	U8 ubShiftNum = 0;
	U32 ulParityUnitPlanePtr = gulPlanesPerUnit; // Invalid

	if (ubIsSlcMode) {
		ubPageShift = (RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM * RAIDECCMAP_SLC_WORDLINE_PROTECT_NUM);
		ubOneWLTagNum = RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM;
		ubShiftNum = 1;
	}
	else {
		ubPageShift = (RAIDECCMAP_1WL_PROTECTION_IN_XLC_PARITY_TAG_IDX_NUM * RAIDECCMAP_XLC_WORDLINE_PROTECT_NUM);
		ubOneWLTagNum = RAIDECCMAP_1WL_PROTECTION_IN_XLC_PARITY_TAG_IDX_NUM;
		ubShiftNum = gubLMUNumber;
	}

	if (0 == ((uwPageIdx / ubPageShift) % RAIDECCMAP_PLANE_PROTECT_NUM)) {
		ulParityUnitPlanePtr = (((uwPageIdx - ubPageShift - (uwPageIdx % ubOneWLTagNum)) + ((ubShiftNum + uwPageIdx) % ubOneWLTagNum)) * RAIDECCMAP_PLANE_PROTECT_NUM) + ((ubPlane + 1) % RAIDECCMAP_PLANE_PROTECT_NUM);
	}
	else {
		ulParityUnitPlanePtr = ((uwPageIdx - ubPageShift) * RAIDECCMAP_PLANE_PROTECT_NUM) + ((ubPlane + 1) % RAIDECCMAP_PLANE_PROTECT_NUM);
	}

	return ulParityUnitPlanePtr;
}

U8 RaidECCMapCheckNeedSwapTag(U8 ubRSEncodeMode, U8 ubSLCMode, U32 ulPlaneIdx, U8 IsRemoveTag)
{
	U8 ubNeedSwap = TRUE;
	U16 uwPage = 0;
	U16 uwPrePage = 0;
	U8 ubPlaneBank = 0;
	U8 ubTagNumPerPlane = 0;
	U8 ubOneWLTagNum = 0;
	U8 ubShiftNum = 0;

	RAIDECCMAPPlaneIdx2PhysicalInline(ulPlaneIdx, &uwPage, &ubPlaneBank, ubSLCMode);
	//debug uart
	if (DEBUG_RAIDECCMAP_UART_SWAP_PARITY_TAG_EN) {
		M_UART(RAIDECCMAP_DEBUG_, "\n SLC=%d", ubSLCMode);
	}

	if (IsRemoveTag) {
		//debug uart
		if (DEBUG_RAIDECCMAP_UART_SWAP_PARITY_TAG_EN) {
			M_UART(RAIDECCMAP_DEBUG_, "\n Is rmv");
		}
		ubNeedSwap = !(RaidECCMapCheckParityInline(ubRSEncodeMode, ubSLCMode, uwPage, ubPlaneBank));
	}
	else {
		if (ubSLCMode) {
			ubTagNumPerPlane = (RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM * RAIDECCMAP_SLC_WORDLINE_PROTECT_NUM);
			ubOneWLTagNum = RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM;
			ubShiftNum = 1;
		}
		else {
			ubTagNumPerPlane = (RAIDECCMAP_1WL_PROTECTION_IN_XLC_PARITY_TAG_IDX_NUM * RAIDECCMAP_XLC_WORDLINE_PROTECT_NUM);
			ubOneWLTagNum = RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM;
			ubShiftNum = gubLMUNumber;
		}

		if ((uwPage / ubTagNumPerPlane) >= 1) {
			if (0 == ((uwPage / ubTagNumPerPlane) % RAIDECCMAP_PLANE_PROTECT_NUM)) {
				uwPrePage = ((uwPage - ubTagNumPerPlane - (uwPage % ubOneWLTagNum)) + ((ubShiftNum + uwPage) % ubOneWLTagNum));
			}
			else {
				uwPrePage = (uwPage - ubTagNumPerPlane);
			}
		}

		if (((uwPage / ubTagNumPerPlane) < 1) || RaidECCMapCheckParityInline(ubRSEncodeMode, ubSLCMode, uwPrePage, (gubPlanesPerSuperPage - 1))) {
			//this case should not occur in here since its tag doesn't need to be swapped.
			ubNeedSwap = FALSE;
		}
	}
	//debug uart
	if (DEBUG_RAIDECCMAP_UART_SWAP_PARITY_TAG_EN) {
		M_UART(RAIDECCMAP_DEBUG_, "\n ubNeedSwap= %d", ubNeedSwap);
	}

	return ubNeedSwap;
}

void RaidECCMapGetDecodePageIndexInfo(RaidECCMapParityMapEnum_t ParityMapMode, U8 ubDecodePageIdx)
{
	switch (ParityMapMode) {
	case RAIDECCMAP_PARITYMAP_MODE_GR_SLC:
		if (ubDecodePageIdx == gpRS_Task.ubRSDataNum) {
			U8 ubSuperPageInSingleTagRoutineBackwardCnt = 0;
			while ((gpRS_Task.uwBaseSuperPageIdx + (gpRS_Task.ubSuperpageNeededInSingleTagRoutine - 1 - ubSuperPageInSingleTagRoutineBackwardCnt) * (RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM * RAIDECCMAP_SLC_WORDLINE_PROTECT_NUM)) > ((guwSuperPagesPerUnit / gubLMUNumber) - 1)) {
				ubSuperPageInSingleTagRoutineBackwardCnt++;
			}

			gpRS_Task.ubRSDataNum -= (ubSuperPageInSingleTagRoutineBackwardCnt * (gubPlanesPerSuperPage / RAIDECCMAP_PLANE_PROTECT_NUM));

			U16 uwSuperPageIdx;
			U8 ubTagShiftNumByStringRotate, ubTagIDWithoutWLPlane, ubWLSwitch;
			uwSuperPageIdx = ((gpRS_Task.uwBaseSuperPageIdx / (RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM * RAIDECCMAP_SLC_WORDLINE_PROTECT_NUM)) + (gpRS_Task.ubSuperpageNeededInSingleTagRoutine - 1 - ubSuperPageInSingleTagRoutineBackwardCnt)) * (RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM * RAIDECCMAP_SLC_WORDLINE_PROTECT_NUM);
			ubTagShiftNumByStringRotate  = ((uwSuperPageIdx / (RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM * RAIDECCMAP_SLC_WORDLINE_PROTECT_NUM * RAIDECCMAP_PLANE_PROTECT_NUM)) % (RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM / RAIDECCMAP_TAG_SHIFT_NUM_FOR_SLC_STRING)) * RAIDECCMAP_TAG_SHIFT_NUM_FOR_SLC_STRING;

			ubWLSwitch = (gpRS_Task.ubErrorPageTagID % (RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM * RAIDECCMAP_SLC_WORDLINE_PROTECT_NUM)) / RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM;
			ubTagIDWithoutWLPlane = gpRS_Task.ubErrorPageTagID % RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM;

			if (ubTagIDWithoutWLPlane < ubTagShiftNumByStringRotate ) {
				uwSuperPageIdx += (RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM + ubTagIDWithoutWLPlane - ubTagShiftNumByStringRotate ) + (ubWLSwitch * RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM);
			}
			else {
				uwSuperPageIdx += (ubTagIDWithoutWLPlane - ubTagShiftNumByStringRotate ) + (ubWLSwitch * RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM);
			}
			gpRS_Task.uwSuperPageIdx = uwSuperPageIdx;
			gpRS_Task.ubSuperPageOffset = ((gpRS_Task.ubBaseSuperPageOffset + (gpRS_Task.ubSuperpageNeededInSingleTagRoutine - 1 - ubSuperPageInSingleTagRoutineBackwardCnt)) % RAIDECCMAP_PLANE_PROTECT_NUM) + (((gubPlanesPerSuperPage / RAIDECCMAP_PLANE_PROTECT_NUM) - 1) * RAIDECCMAP_PLANE_PROTECT_NUM);
		}
		else {
			U8 ubShiftX = RAIDECCMAP_PLANE_PROTECT_NUM;
			U8 ubShiftY = RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM * RAIDECCMAP_SLC_WORDLINE_PROTECT_NUM;
			U8 ubMaxX = (gubPlanesPerSuperPage / RAIDECCMAP_PLANE_PROTECT_NUM);

			U8 ubX = (ubDecodePageIdx % ubMaxX);
			U8 ubY = (ubDecodePageIdx / ubMaxX);

			U16 uwSuperPageIdx;
			U8 ubTagShiftNumByStringRotate, ubTagIDWithoutWLPlane, ubWLSwitch;
			uwSuperPageIdx = ((gpRS_Task.uwBaseSuperPageIdx / (RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM * RAIDECCMAP_SLC_WORDLINE_PROTECT_NUM)) + ubY) * (ubShiftY);
			ubTagShiftNumByStringRotate  = ((uwSuperPageIdx / (RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM * RAIDECCMAP_SLC_WORDLINE_PROTECT_NUM * RAIDECCMAP_PLANE_PROTECT_NUM)) % (RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM / RAIDECCMAP_TAG_SHIFT_NUM_FOR_SLC_STRING)) * RAIDECCMAP_TAG_SHIFT_NUM_FOR_SLC_STRING;

			ubWLSwitch = (gpRS_Task.ubErrorPageTagID % (RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM * RAIDECCMAP_SLC_WORDLINE_PROTECT_NUM)) / RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM;
			ubTagIDWithoutWLPlane = gpRS_Task.ubErrorPageTagID % RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM;

			if (ubTagIDWithoutWLPlane < ubTagShiftNumByStringRotate ) {
				uwSuperPageIdx += (RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM + ubTagIDWithoutWLPlane - ubTagShiftNumByStringRotate ) + (ubWLSwitch * RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM);
			}
			else {
				uwSuperPageIdx += (ubTagIDWithoutWLPlane - ubTagShiftNumByStringRotate ) + (ubWLSwitch * RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM);
			}
			gpRS_Task.uwSuperPageIdx = uwSuperPageIdx;
			gpRS_Task.ubSuperPageOffset = ((gpRS_Task.ubBaseSuperPageOffset + ubY) % RAIDECCMAP_PLANE_PROTECT_NUM) + (ubX * ubShiftX);
		}
		break;
	case RAIDECCMAP_PARITYMAP_MODE_GR_XLC:
	case RAIDECCMAP_PARITYMAP_MODE_GC:
		if (ubDecodePageIdx == gpRS_Task.ubRSDataNum) {
			U8 ubSuperPageInSingleTagRoutineBackwardCnt = 0;
			while (gpRS_Task.uwBaseSuperPageIdx + (gpRS_Task.ubSuperpageNeededInSingleTagRoutine - 1 - ubSuperPageInSingleTagRoutineBackwardCnt) * (RAIDECCMAP_1WL_PROTECTION_IN_XLC_PARITY_TAG_IDX_NUM * RAIDECCMAP_XLC_WORDLINE_PROTECT_NUM) > (guwSuperPagesPerUnit - 1)) {
				ubSuperPageInSingleTagRoutineBackwardCnt++;
			}

			gpRS_Task.ubRSDataNum -= (ubSuperPageInSingleTagRoutineBackwardCnt * (gubPlanesPerSuperPage / RAIDECCMAP_PLANE_PROTECT_NUM));

			U16 uwSuperPageIdx;
			U8 ubTagShiftNumByStringRotate, ubTagIDWithoutWLPlane, ubWLSwitch;
			uwSuperPageIdx = ((gpRS_Task.uwBaseSuperPageIdx / (RAIDECCMAP_1WL_PROTECTION_IN_XLC_PARITY_TAG_IDX_NUM * RAIDECCMAP_XLC_WORDLINE_PROTECT_NUM))  + (gpRS_Task.ubSuperpageNeededInSingleTagRoutine - 1 - ubSuperPageInSingleTagRoutineBackwardCnt)) * (RAIDECCMAP_1WL_PROTECTION_IN_XLC_PARITY_TAG_IDX_NUM * RAIDECCMAP_XLC_WORDLINE_PROTECT_NUM);
			ubTagShiftNumByStringRotate = ((uwSuperPageIdx / (RAIDECCMAP_1WL_PROTECTION_IN_XLC_PARITY_TAG_IDX_NUM * RAIDECCMAP_XLC_WORDLINE_PROTECT_NUM * RAIDECCMAP_PLANE_PROTECT_NUM)) % (RAIDECCMAP_1WL_PROTECTION_IN_XLC_PARITY_TAG_IDX_NUM / RAIDECCMAP_TAG_SHIFT_NUM_FOR_XLC_STRING)) * RAIDECCMAP_TAG_SHIFT_NUM_FOR_XLC_STRING;

			ubWLSwitch = (gpRS_Task.ubErrorPageTagID % (RAIDECCMAP_1WL_PROTECTION_IN_XLC_PARITY_TAG_IDX_NUM * RAIDECCMAP_XLC_WORDLINE_PROTECT_NUM)) / RAIDECCMAP_1WL_PROTECTION_IN_XLC_PARITY_TAG_IDX_NUM;
			ubTagIDWithoutWLPlane = gpRS_Task.ubErrorPageTagID % RAIDECCMAP_1WL_PROTECTION_IN_XLC_PARITY_TAG_IDX_NUM;

			if (ubTagIDWithoutWLPlane < ubTagShiftNumByStringRotate ) {
				uwSuperPageIdx += (RAIDECCMAP_1WL_PROTECTION_IN_XLC_PARITY_TAG_IDX_NUM + ubTagIDWithoutWLPlane - ubTagShiftNumByStringRotate ) + (ubWLSwitch * RAIDECCMAP_1WL_PROTECTION_IN_XLC_PARITY_TAG_IDX_NUM);
			}
			else {
				uwSuperPageIdx += (ubTagIDWithoutWLPlane - ubTagShiftNumByStringRotate ) + (ubWLSwitch * RAIDECCMAP_1WL_PROTECTION_IN_XLC_PARITY_TAG_IDX_NUM);
			}
			gpRS_Task.uwSuperPageIdx = uwSuperPageIdx;
			gpRS_Task.ubSuperPageOffset = ((gpRS_Task.ubBaseSuperPageOffset + (gpRS_Task.ubSuperpageNeededInSingleTagRoutine - 1 - ubSuperPageInSingleTagRoutineBackwardCnt)) % RAIDECCMAP_PLANE_PROTECT_NUM) + (((gubPlanesPerSuperPage / RAIDECCMAP_PLANE_PROTECT_NUM) - 1) * RAIDECCMAP_PLANE_PROTECT_NUM);
		}
		else {
			U8 ubShiftX = RAIDECCMAP_PLANE_PROTECT_NUM;
			U8 ubShiftY = RAIDECCMAP_1WL_PROTECTION_IN_XLC_PARITY_TAG_IDX_NUM * RAIDECCMAP_XLC_WORDLINE_PROTECT_NUM;
			U8 ubMaxX = (gubPlanesPerSuperPage / RAIDECCMAP_PLANE_PROTECT_NUM);

			U8 ubX = (ubDecodePageIdx % ubMaxX);
			U8 ubY = (ubDecodePageIdx / ubMaxX);

			U16 uwSuperPageIdx;
			U8 ubTagShiftNumByStringRotate, ubTagIDWithoutWLPlane, ubWLSwitch;
			uwSuperPageIdx = ((gpRS_Task.uwBaseSuperPageIdx / (RAIDECCMAP_1WL_PROTECTION_IN_XLC_PARITY_TAG_IDX_NUM * RAIDECCMAP_XLC_WORDLINE_PROTECT_NUM)) + ubY) * (ubShiftY);
			ubTagShiftNumByStringRotate  = ((uwSuperPageIdx / (RAIDECCMAP_1WL_PROTECTION_IN_XLC_PARITY_TAG_IDX_NUM * RAIDECCMAP_XLC_WORDLINE_PROTECT_NUM * RAIDECCMAP_PLANE_PROTECT_NUM)) % (RAIDECCMAP_1WL_PROTECTION_IN_XLC_PARITY_TAG_IDX_NUM / RAIDECCMAP_TAG_SHIFT_NUM_FOR_XLC_STRING)) * RAIDECCMAP_TAG_SHIFT_NUM_FOR_XLC_STRING;

			ubWLSwitch = (gpRS_Task.ubErrorPageTagID % (RAIDECCMAP_1WL_PROTECTION_IN_XLC_PARITY_TAG_IDX_NUM * RAIDECCMAP_XLC_WORDLINE_PROTECT_NUM)) / RAIDECCMAP_1WL_PROTECTION_IN_XLC_PARITY_TAG_IDX_NUM;
			ubTagIDWithoutWLPlane = gpRS_Task.ubErrorPageTagID % RAIDECCMAP_1WL_PROTECTION_IN_XLC_PARITY_TAG_IDX_NUM;

			if (ubTagIDWithoutWLPlane < ubTagShiftNumByStringRotate) {
				uwSuperPageIdx += (RAIDECCMAP_1WL_PROTECTION_IN_XLC_PARITY_TAG_IDX_NUM + ubTagIDWithoutWLPlane - ubTagShiftNumByStringRotate ) + (ubWLSwitch * RAIDECCMAP_1WL_PROTECTION_IN_XLC_PARITY_TAG_IDX_NUM);
			}
			else {
				uwSuperPageIdx += (ubTagIDWithoutWLPlane - ubTagShiftNumByStringRotate) + (ubWLSwitch * RAIDECCMAP_1WL_PROTECTION_IN_XLC_PARITY_TAG_IDX_NUM);
			}
			gpRS_Task.uwSuperPageIdx = uwSuperPageIdx;
			gpRS_Task.ubSuperPageOffset = ((gpRS_Task.ubBaseSuperPageOffset + ubY) % RAIDECCMAP_PLANE_PROTECT_NUM) + (ubX * ubShiftX);
		}
		break;
	case RAIDECCMAP_PARITYMAP_MODE_TABLE:
		if (gubPlanesPerSuperPage < RAIDECCMAP_TABLE_MIN_PLANE_NUM_PER_GROUP) {
			gpRS_Task.uwSuperPageIdx = gpRS_Task.uwBaseSuperPageIdx + (ubDecodePageIdx / gubPlanesPerSuperPage);
			gpRS_Task.ubSuperPageOffset = ubDecodePageIdx % gubPlanesPerSuperPage;
		}
		else {
			gpRS_Task.uwSuperPageIdx = gpRS_Task.uwBaseSuperPageIdx;
			gpRS_Task.ubSuperPageOffset = ubDecodePageIdx;
		}
		break;
	default:
		break;
	}
}

U16 RaidECCMapGetGRSuperpageIndex(U8 ubSuperPageIdxInSingleTagRoutine)
{
	U16 uwSuperPageIdx;
	U8 ubTagShiftNumByStringRotate, ubTagIDWithoutWLPlane, ubWLSwitch;
	uwSuperPageIdx = ((gpRS_Task.uwBaseSuperPageIdx / (RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM * RAIDECCMAP_SLC_WORDLINE_PROTECT_NUM)) + ubSuperPageIdxInSingleTagRoutine) * (RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM * RAIDECCMAP_SLC_WORDLINE_PROTECT_NUM);
	ubTagShiftNumByStringRotate = ((uwSuperPageIdx / (RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM * RAIDECCMAP_SLC_WORDLINE_PROTECT_NUM * RAIDECCMAP_PLANE_PROTECT_NUM)) % (RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM / RAIDECCMAP_TAG_SHIFT_NUM_FOR_SLC_STRING)) * RAIDECCMAP_TAG_SHIFT_NUM_FOR_SLC_STRING;

	ubWLSwitch = (gpRS_Task.ubErrorPageTagID % (RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM * RAIDECCMAP_SLC_WORDLINE_PROTECT_NUM)) / RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM;
	ubTagIDWithoutWLPlane = gpRS_Task.ubErrorPageTagID % RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM;

	if (ubTagIDWithoutWLPlane < ubTagShiftNumByStringRotate) {
		uwSuperPageIdx += (RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM + ubTagIDWithoutWLPlane - ubTagShiftNumByStringRotate) + (ubWLSwitch * RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM);
	}
	else {
		uwSuperPageIdx += (ubTagIDWithoutWLPlane - ubTagShiftNumByStringRotate) + (ubWLSwitch * RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM);
	}

	return uwSuperPageIdx;
}
#endif /*(FALSE == MICRON_FSP_EN)*/
