#ifndef _RTT_H_
#define _RTT_H_

#include "typedef.h"
#include "hal/sys/reg/sys_pd0_reg.h"
#include "hal/sys/reg/sys_pd1_reg.h"
#include "common/math_op.h"

#define RTT_PER_US									(1)
#define RTT_PER_MS									(RTT_PER_US * 1000)
#define RTT_PER_S									(RTT_PER_MS * 1000)
#define RTT_FLL_CALIBRATION_INT_INTERVAL_MS			(100) // 100ms
#define RTT_FLL_CALIBRATION_TIME_OUT_LIMIT			(700000) // 700ms
#define RTT_US_PER_100MS							(100000)
#define RTT_5PERCENT_OF_US_PER_100MS				(5000) // 100000 * 0.05
#define RTT_20PERCENT_OF_US_PER_100MS				(20000) // 100000 * 0.2

#if PS5021_EN
#define RTT_RTT0_SHIFT_HIGH_4BYTE					(32)

#define M_ENABLE_MS_DW_TIMER1_REP()					(R32_SYS1_TIMER_CTRL[R32_SYS1_DW_TIMER_CFG] |= MS1_DW_TIMER_REP_ENB_BIT)
#define M_DISABLE_MS_DW_TIMER1_REP()				(R32_SYS1_TIMER_CTRL[R32_SYS1_DW_TIMER_CFG] &= ~MS1_DW_TIMER_REP_ENB_BIT)
#define M_ENABLE_MS_DW_TIMER2_REP()					(R32_SYS1_TIMER_CTRL[R32_SYS1_DW_TIMER_CFG] |= MS2_DW_TIMER_REP_ENB_BIT)
#define M_DISABLE_MS_DW_TIMER2_REP()				(R32_SYS1_TIMER_CTRL[R32_SYS1_DW_TIMER_CFG] &= ~MS2_DW_TIMER_REP_ENB_BIT)
#define M_ENABLE_MS_DW_TIMER3_REP()					(R32_SYS1_TIMER_CTRL[R32_SYS1_DW_TIMER_CFG] |= MS3_DW_TIMER_REP_ENB_BIT)
#define M_DISABLE_MS_DW_TIMER3_REP()				(R32_SYS1_TIMER_CTRL[R32_SYS1_DW_TIMER_CFG] &= ~MS3_DW_TIMER_REP_ENB_BIT)
#define M_ENABLE_US_DW_TIMER_REP()					(R32_SYS1_TIMER_CTRL[R32_SYS1_DW_TIMER_CFG] |= US_DW_TIMER_REP_ENB_BIT)
#define M_DISABLE_US_DW_TIMER_REP()					(R32_SYS1_TIMER_CTRL[R32_SYS1_DW_TIMER_CFG] &= ~US_DW_TIMER_REP_ENB_BIT)

#define M_SET_MS_DW_TIMER1(x)						(R32_SYS1_TIMER_CTRL[R32_SYS1_MS_DW_TIMER1] = ((U32)(x)))
#define M_SET_MS_DW_TIMER2(x)						(R32_SYS1_TIMER_CTRL[R32_SYS1_MS_DW_TIMER2] = ((U32)(x)))
#define M_SET_MS_DW_TIMER3(x)						(R32_SYS1_TIMER_CTRL[R32_SYS1_MS_DW_TIMER3] = ((U32)(x)))
#define M_SET_US_DW_TIMER(x)						(R32_SYS1_TIMER_CTRL[R32_SYS1_US_DW_TIMER] = ((U32)(x)))

#define M_ENABLE_MS_UW_TIMER()						(R32_SYS1_TIMER_CTRL[R32_SYS1_UW_TIMER_CFG] |= UW_TIMER_MS_ENB_BIT)
#define M_DISABLE_MS_UW_TIMER()						(R32_SYS1_TIMER_CTRL[R32_SYS1_UW_TIMER_CFG] &= ~UW_TIMER_MS_ENB_BIT)
#define M_ENABLE_US_UW_TIMER()						(R32_SYS1_TIMER_CTRL[R32_SYS1_UW_TIMER_CFG] |= UW_TIMER_US_ENB_BIT)
#define M_DISABLE_US_UW_TIMER()						(R32_SYS1_TIMER_CTRL[R32_SYS1_UW_TIMER_CFG] &= ~UW_TIMER_US_ENB_BIT)

#define M_SET_US_UW_TIMER(x)						(R32_SYS1_TIMER_CTRL[R32_SYS1_US_UW_TIMER] = ((U32)(x)))
#define M_GET_US_UW_TIMER()							(R32_SYS1_TIMER_CTRL[R32_SYS1_US_UW_TIMER])

#define M_SET_MS_UW_TIMER(x)						(R32_SYS1_TIMER_CTRL[R32_SYS1_MS_UW_TIMER] = ((U32)(x)))
#define M_GET_MS_UW_TIMER()							(R32_SYS1_TIMER_CTRL[R32_SYS1_MS_UW_TIMER])

#else /* PS5021_EN */
#define M_LATCH_RTT0_VAL()							(R32_SYS0_RTT[R32_SYS0_RTT_TO_CTRL] |= (CR_RTT_LATCH_BIT | CR_RTT_TO_VLD_BIT))
#define M_GET_RTT0_LATCH()							(R64_SYS0_RTT[R64_SYS0_RTT_LATCH])
#define M_ENABLE_RTT0()								(R32_SYS0_RTT[R32_SYS0_RTT_TO_CTRL] |= CR_RTT_EN_BIT)
#define M_DISABLE_RTT0()							(R32_SYS0_RTT[R32_SYS0_RTT_TO_CTRL] &= ~CR_RTT_EN_BIT)

#define M_GET_PD1_RTT_CNT(X)						(R32_SYS1_RTT[R32_SYS1_RT1_CNT + (X)])
#define	M_CLR_PD1_RTT_CNT(X)						(R8_SYS1_RTT[(X)] |= CR_RT1_CLR_P_BIT)// only clear rt1/rt2 count value, can't stop counter
#define	M_PD1_RTT_REPEAT_MODE(X)					(R8_SYS1_RTT[(X)] &= ~CR_RT1_MD_BIT)
#define	M_PD1_RTT_ONE_SHOT_MODE(X)					(R8_SYS1_RTT[(X)] |= CR_RT1_MD_BIT)
#define	M_DISABLE_PD1_RTT(X)						(R8_SYS1_RTT[(X)] &= ~CR_RT1_EN_BIT)
#define	M_ENABLE_PD1_RTT(X)							(R8_SYS1_RTT[(X)] |= CR_RT1_EN_BIT)
#define	M_CLR_PD1_RTT_SCALE(X)						(R8_SYS1_RTT[(X)] &= ~(CR_RT1_SK_MASK << CR_RT1_SK_SHIFT))
#define	M_SET_PD1_RTT_SCALE(X,Y)					(R8_SYS1_RTT[(X)] |= (((Y) << SET_PD1_RTT_SCALE_SHIFT) & CR_RT1_SK))
#define	M_SET_PD1_RTT_TO_LMT(X,Y)					(R32_SYS1_RTT[R32_SYS1_RT1_TO_LMT + (X)] = (U32)(Y))
#endif /* PS5021_EN */

#endif /* _RTT_H_ */
