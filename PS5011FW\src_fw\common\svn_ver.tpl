#ifndef __SVN_VER_H__
#define __SVN_VER_H__

/*!
    @brief SVN GET INFO
        @details Below is the value description
    @note
        // Test file for SubWCRev
        char *Revision      = "$WCREV$";
        char *Revision16    = "$WCREV&0xFF$";
        char *Revisionp100  = "$WCREV+100$";
        char *Revisionm100  = "$WCREV-100$";
        char *Modified      = "$WCMODS?Modified:Not modified$";
        char *Unversioned   = "$WCUNVER?Unversioned items found:no unversioned items$";
        char *Date          = "$WCDATE$";
        char *CustDate      = "$WCDATE=%a, %d %B %Y$";
        char *DateUTC       = "$WCDATEUTC$";
        char *CustDateUTC   = "$WCDATEUTC=%a, %d %B %Y$";
        char *TimeNow       = "$WCNOW$";
        char *TimeNowUTC    = "$WCNOWUTC$";
        char *RevRange      = "$WCRANGE$";
        char *Mixed         = "$WCMIXED?Mixed revision WC:Not mixed$";
        char *ExtAllFixed   = "$WCEXTALLFIXED?All externals fixed:Not all externals fixed$";
        char *IsTagged      = "$WCISTAGGED?Tagged:Not tagged$";
        char *URL           = "$WCURL$";
        char *isInSVN       = "$WCINSVN?versioned:not versioned$";
        char *needslck      = "$WCNEEDSLOCK?TRUE:FALSE$";
        char *islocked      = "$WCISLOCKED?locked:not locked$";
        char *lockdateutc   = "$WCLOCKDATEUTC$";
        char *lockdate      = "$WCLOCKDATE$";
        char *lockcustutc   = "$WCLOCKDATEUTC=%a, %d %B %Y$";
        char *lockcust      = "$WCLOCKDATE=%a, %d %B %Y$";
        char *lockown       = "$WCLOCKOWNER$";
        char *lockcmt       = "$WCLOCKCOMMENT$";
*/

#if ICE_MODE_EN
#if (HOST_MODE == SATA)
#define RUN_VER "PS3113 FW ver.$WCREV$ ice mode\n"
#else
#define RUN_VER "PS5013 FW ver.$WCREV$ ice mode\n"
#endif	/* (HOST_MODE == SATA)*/
#elif BURNER_MODE_EN
#if (HOST_MODE == SATA)
#define RUN_VER "PS3113 BURNER ver.$WCREV$\n"
#else
#define RUN_VER "PS5013 BURNER ver.$WCREV$\n"
#endif /* (HOST_MODE == SATA) */
#else
#if (HOST_MODE == SATA)
#define RUN_VER "PS3113 FW ver.$WCREV$ normal mode\n"
#else
#define RUN_VER "PS5013 FW ver.$WCREV$ normal mode\n"
#endif /* (HOST_MODE == SATA) */
#endif

#define SVN_VER "$WCREV$"

#define SVN_VER_DIGITAL 0x$WCREV$

#define COMPILE_DATE "build:$WCNOW$"

#endif /* __SVN_VER_H__ */
