#include "VUC_MicronGetNandErrorRecoveryStatistics.h"
#include "VUC_MicronResponse.h"
#include "VUC_MicronLogicaltoPhysical.h"
#include "hal/sys/api/angc/angc_api.h"
#include "hal/dmac/dmac_pop_cmd.h"
#include "hal/cop1/cop1.h"
#include "hal/cop1/cop1_api.h"
#include "VUC_PCATranslate.h"
#include "hal/cop0/cop0_api.h"
#include "table/rut/rut_api.h"

#if (VUC_MICRON_NAND_VS_COMMANDS_EN)
static PCA_t gulPCA;
static U8 gubWaitCallBack;
void VUCMicronLogicalToPhysicalAddress_Callback(COP1CQRst_t uoResult)
{
	gulPCA.ulAll = uoResult.uoSHBlkSCHLCARst.ulRespInfo.ulAll;
	gubWaitCallBack = FALSE;
}

void VUCMicronLogicalToPhysicalAddress(GetLogicaltoPhysicalAddressInputData_t *pInputData, U32 ulPayloadAddr)
{
	GetLogicaltoPhysicalAddressResponseHEADER_t *pResponseHeader;
	U32 ulResponseAddr = ulPayloadAddr + VUC_MICRON_RESPONSE_HEADER_SIZE;
	U8 ubTmp[10] = "";
	U8 ubCOP0AttrSelect = COP1_COP0_ATTR2, ubPCARuleIdx;
	U32 ulLCA, ulFSA;
	U64 ullLBA;
	U8 ubFrame;
	cmd_table_t uoCallbackInfo = {(U32)VUCMicronLogicalToPhysicalAddress_Callback, {0}};
	BlockInfo_t pulBlockInfo;
	DMACParam_t DMACParam;

	pResponseHeader = (GetLogicaltoPhysicalAddressResponseHEADER_t *)ulPayloadAddr;

	DMACParam.DMACSetValue.ulDestAddr = ulPayloadAddr;
	DMACParam.DMACSetValue.ulLowValue = 0;
	DMACParam.DMACSetValue.ulHighValue = 0;
	DMACParam.DMACSetValue.ul32ByteNum = SIZE_IN_32B(DEF_KB(4));
	DMACParam.DMACSetValue.btValidate = FALSE;
	gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
	DMACSetValue(&DMACParam, gulDMACDirectWaitDone_Callback, 0);
	while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
		DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
	}

	pResponseHeader->ubResponseHeaderFormatVersion = VUC_MICRON_RESPONSE_HEADER_FORMAT_VERSION_0;
	pResponseHeader->ubResponseDataFotmatVersion = VUC_MICRON_RESPONSE_FORMAT_JSON;
	pResponseHeader->uwCMDClass = VUC_MICRON_COMMON_VS_COMMANDS;
	pResponseHeader->uwCMDCode = VUC_MICRON_GET_L2P;
	pResponseHeader->uwStatus = 0;
	pResponseHeader->ulDataPayloadSize = SECTOR_SIZE;

	ullLBA = pInputData ->ullLBA;
	ulLCA = ullLBA / 8;

	COP1APISHBlkSCHLCA(ulLCA, BIT_SEARCH_WLB | BIT_SEARCH_ST1 | BIT_SEARCH_ST3, COP1_SH_NOT_FW_LOAD_TABLE, COP1_SH_LBIDSEL_NOT_LBIDSEL, ubCOP0AttrSelect, &uoCallbackInfo);
	gubWaitCallBack = TRUE;
	while (TRUE == gubWaitCallBack) {
		UartPrintf("\nWaiting");
		FWCOP1Waiting();
	}

	U32 ulLocalPCA;
	U8 ubLocalZByte, ubLocalCrossFrame, ubLocalLPCRC;
	M_FWPCA_GET(ulLocalPCA, ubLocalZByte, ubLocalCrossFrame, ubLocalLPCRC, gulPCA.ulAll);


	U8 ubIsSLCMode = gpuwVBRMP[ulLocalPCA >> gub4kEntrysPerUnitAlignLog].B.btIsSLCMode;
	U8 ubIsD1 = gpuwVBRMP[ulLocalPCA >> gub4kEntrysPerUnitAlignLog].B.btIsD1;;

#if MICRON_FSP_EN
	if (FALSE == (ubIsSLCMode)) {
		ulFSA = FTLTranslatePCA(COP0_VCAToPCA(ulLocalPCA));
	}
	else {
		ulFSA = FTLTranslatePCA(FTLTransferToCop0CieInPCA(ulLocalPCA));
	}
#else /* MICRON_FSP_EN */
	ulFSA = FTLTranslatePCA(ulLocalPCA);
#endif /* MICRON_FSP_EN */

	U16 uwPage;

	M_PCA_RULE_IDX(ubPCARuleIdx, ubIsSLCMode, ubIsD1);
	M_PCA_GET_INFO(pulBlockInfo.B.Block, ulFSA, gPCARule_Block, ubPCARuleIdx);
	M_PCA_GET_INFO(pulBlockInfo.B.CH, ulFSA, gPCARule_Channel, ubPCARuleIdx);
	M_PCA_GET_INFO(pulBlockInfo.B.CE, ulFSA, gPCARule_Bank, ubPCARuleIdx);
	M_PCA_GET_INFO(pulBlockInfo.B.PhysicalPlane, ulFSA, gPCARule_Plane, ubPCARuleIdx);
	M_PCA_GET_INFO(pulBlockInfo.B.LUN, ulFSA, gPCARule_LUN, ubPCARuleIdx);
	M_PCA_GET_INFO(pulBlockInfo.B.DieIL, ulFSA, gPCARule_Die_IL, ubPCARuleIdx);
	uwPage = (((ulFSA) >> gPCARule_Page.ubShift[(ubPCARuleIdx)]) & gPCARule_Page.ulMask);
	ubFrame = (((ulFSA) >> gPCARule_Entry.ubShift[(ubPCARuleIdx)]) & gPCARule_Entry.ulMask);

#if (HOST_MODE == NVME)
	VUCMyStrcat((void *)ulResponseAddr, "{\n\t");
	VUCMyStrcat((void *)ulResponseAddr, "\"structVer\":1,\n\t");
	VUCMyStrcat((void *)ulResponseAddr, "\"Channel\":");
	VUCMyitoa(pulBlockInfo.B.CH, (void *)ubTmp, DECIMAL);
	VUCMyStrcat((void *)ulResponseAddr, (void *)ubTmp);
	VUCMyStrcat((void *)ulResponseAddr, ",\n\t");

	VUCMyStrcat((void *)ulResponseAddr, "\"CE\":");
	VUCMyitoa(pulBlockInfo.B.CE, (void *)ubTmp, DECIMAL);
	VUCMyStrcat((void *)ulResponseAddr, (void *)ubTmp);
	VUCMyStrcat((void *)ulResponseAddr, ",\n\t");

	VUCMyStrcat((void *)ulResponseAddr, "\"LUN\":");
	VUCMyitoa(pulBlockInfo.B.LUN, (void *)ubTmp, DECIMAL);
	VUCMyStrcat((void *)ulResponseAddr, (void *)ubTmp);
	VUCMyStrcat((void *)ulResponseAddr, ",\n\t");

	VUCMyStrcat((void *)ulResponseAddr, "\"Block\":");
	VUCMyitoa((pulBlockInfo.B.Block << 2) + pulBlockInfo.B.PhysicalPlane, (void *)ubTmp, DECIMAL);
	VUCMyStrcat((void *)ulResponseAddr, (void *)ubTmp);
	VUCMyStrcat((void *)ulResponseAddr, ",\n\t");

	VUCMyStrcat((void *)ulResponseAddr, "\"Page\":");
	VUCMyitoa(uwPage, (void *)ubTmp, DECIMAL);
	VUCMyStrcat((void *)ulResponseAddr, (void *)ubTmp);
	VUCMyStrcat((void *)ulResponseAddr, ",\n\t");


	VUCMyStrcat((void *)ulResponseAddr, "\"CodeWord\":");
	VUCMyitoa(ubFrame, (void *)ubTmp, DECIMAL);
	VUCMyStrcat((void *)ulResponseAddr, (void *)ubTmp);
	VUCMyStrcat((void *)ulResponseAddr, ",\n\t");

	VUCMyStrcat((void *)ulResponseAddr, "\"FPA\":");
	VUCMyitoa(ulFSA, (void *)ubTmp, DECIMAL);
	VUCMyStrcat((void *)ulResponseAddr, (void *)ubTmp);
	VUCMyStrcat((void *)ulResponseAddr, "\n}");

#else /* (HOST_MODE == NVME) */

	VUCMyStrcat((void *)ulResponseAddr, "{\n\t");
	VUCMyStrcat((void *)ulResponseAddr, "\"structVer\":1,\n\t");
	VUCMyStrcat((void *)ulResponseAddr, "\"Channel\":");
	VUCMyitoa(pulBlockInfo.B.CH, (void *)ubTmp, HEXADECIMAL);
	VUCMyStrcat((void *)ulResponseAddr, (void *)ubTmp);
	VUCMyStrcat((void *)ulResponseAddr, ",\n\t");

	VUCMyStrcat((void *)ulResponseAddr, "\"CE\":");
	VUCMyitoa(pulBlockInfo.B.CE, (void *)ubTmp, HEXADECIMAL);
	VUCMyStrcat((void *)ulResponseAddr, (void *)ubTmp);
	VUCMyStrcat((void *)ulResponseAddr, ",\n\t");

	VUCMyStrcat((void *)ulResponseAddr, "\"LUN\":");
	VUCMyitoa(pulBlockInfo.B.LUN, (void *)ubTmp, HEXADECIMAL);
	VUCMyStrcat((void *)ulResponseAddr, (void *)ubTmp);
	VUCMyStrcat((void *)ulResponseAddr, ",\n\t");

	VUCMyStrcat((void *)ulResponseAddr, "\"Block\":0x");
	VUCMyitoa((pulBlockInfo.B.Block << 2) + pulBlockInfo.B.PhysicalPlane, (void *)ubTmp, HEXADECIMAL);
	VUCMyStrcat((void *)ulResponseAddr, (void *)ubTmp);
	VUCMyStrcat((void *)ulResponseAddr, ",\n\t");

	VUCMyStrcat((void *)ulResponseAddr, "\"Page\":0x");
	VUCMyitoa(uwPage, (void *)ubTmp, HEXADECIMAL);
	VUCMyStrcat((void *)ulResponseAddr, (void *)ubTmp);
	VUCMyStrcat((void *)ulResponseAddr, ",\n\t");


	VUCMyStrcat((void *)ulResponseAddr, "\"CodeWord\":");
	VUCMyitoa(ubFrame, (void *)ubTmp, HEXADECIMAL);
	VUCMyStrcat((void *)ulResponseAddr, (void *)ubTmp);
	VUCMyStrcat((void *)ulResponseAddr, ",\n\t");

	VUCMyStrcat((void *)ulResponseAddr, "\"FPA\":0x");
	VUCMyitoa(ulFSA, (void *)ubTmp, HEXADECIMAL);
	VUCMyStrcat((void *)ulResponseAddr, (void *)ubTmp);
	VUCMyStrcat((void *)ulResponseAddr, "\n}");

#endif/* (HOST_MODE == NVME) */
}

#endif /*(VUC_MICRON_NAND_VS_COMMANDS_EN)*/
