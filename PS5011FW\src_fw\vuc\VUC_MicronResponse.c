#include "VUC_MicronResponse.h"
#include "VUC_MicronNandPass.h"
#include "VUC_MicronGetEraseCount.h"
#include "VUC_MicronGetNandErrorRecoveryStatistics.h"
#include "VUC_MicronClearEventLog.h"
#include "VUC_MicronGetBEC.h"
#include "vuc/VUC_MicronGetUnifiedEventLog.h"
#include "vuc/VUC_MicronGetVTSweep.h"
#include "host/VUC_handler_api.h"
#include "hal/pic/uart/uart_api.h"
#include "hal/dmac/dmac_api.h"
#include "hal/dmac/dmac_pop_cmd.h"

#if (VUC_MICRON_NICKS_EN)

U16 guwVUCMicronResponseStatusCode;
U32 gulVUCMicronVUCOffset = 0;

void VUCMicronResponse(OPT_HCMD_PTR pCmd)
{
	U32 ulTmp, ulResponsePayloadLength;
	U32 ulRequestLength, ulRealDataLengh;
	guwVUCMicronResponseStatusCode = VUC_MICRON_RESPONSE_STATUS_NO_ERROR;
	ulRequestLength = DEF_4B * ((U32)pCmd->nvme_sqcmd.raw_data.dw[10]);

	if ( VUC_MICRON_GNPT == gubMicronLastCmdCode) {
		ulRealDataLengh = gulGNPTResponsePayloadSize + VUC_MICRON_GNPT_RESPONSE_PAYLOAD_OFFSET - VUC_MICRON_GNPT_HEADER_PAYLOAD_SIZE;
		ulResponsePayloadLength = gulGNPTResponsePayloadSize;

		M_UART(VUC_MICRON_, "\nHost Request Length:%x", ulRequestLength);
		M_UART(VUC_MICRON_, "\nReal Response Length:%x", ulRealDataLengh);

		if ( (ulRequestLength + gulVUCMicronVUCOffset) < ulRealDataLengh ) {
			guwVUCMicronResponseStatusCode = VUC_MICRON_RESPONSE_STATUS_INCOMPLETE;
		}

		memcpy((void *)(gulResponseBufferAddr + VUC_MICRON_GNPT_HEADER_FORMAT_VERSION_LENGTH + VUC_MICRON_GNPT_HEADER_FORMAT_TYPE_LENGTH + VUC_MICRON_GNPT_HEADER_COMMAND_CLASS_LENGTH + VUC_MICRON_GNPT_HEADER_COMMAND_CODE_LENGTH), &guwVUCMicronResponseStatusCode, VUC_MICRON_GNPT_HEADER_STATUS_LENGTH);
		memcpy((void *)(gulResponseBufferAddr + VUC_MICRON_GNPT_HEADER_FORMAT_VERSION_LENGTH + VUC_MICRON_GNPT_HEADER_FORMAT_TYPE_LENGTH + VUC_MICRON_GNPT_HEADER_COMMAND_CLASS_LENGTH + VUC_MICRON_GNPT_HEADER_COMMAND_CODE_LENGTH + VUC_MICRON_GNPT_HEADER_STATUS_LENGTH), &ulRealDataLengh, VUC_MICRON_GNPT_HEADER_DATA_PAYLOAD_LENGTH);

		ulTmp = VUC_MICRON_GNPT_LAST_CMD_OFFSET_ID;
		memcpy((void *)(gulResponseBufferAddr + VUC_MICRON_GNPT_DATA_PAYLOAD_OFFSET), &ulTmp, VUC_MICRON_GNPT_LAST_CMD_OFFSET_ID_LENGTH);
		ulTmp = VUC_MICRON_GNPT_LAST_CMD_OFFSET_SIZE;
		memcpy((void *)(gulResponseBufferAddr + VUC_MICRON_GNPT_DATA_PAYLOAD_OFFSET + VUC_MICRON_GNPT_LAST_CMD_OFFSET_ID_LENGTH), &ulTmp, VUC_MICRON_GNPT_LAST_CMD_OFFSET_SIZE_LENGTH);
		ulTmp = gulGNPTLastCmdOffset;
		memcpy((void *)(gulResponseBufferAddr + VUC_MICRON_GNPT_DATA_PAYLOAD_OFFSET + VUC_MICRON_GNPT_LAST_CMD_OFFSET_ID_LENGTH + VUC_MICRON_GNPT_LAST_CMD_OFFSET_SIZE_LENGTH), &ulTmp, VUC_MICRON_GNPT_LAST_CMD_OFFSET_VALUE_LENGTH);
		ulTmp = VUC_MICRON_GNPT_SUB_CMD_STATUS_ID;
		memcpy((void *)(gulResponseBufferAddr + VUC_MICRON_GNPT_DATA_PAYLOAD_OFFSET + VUC_MICRON_GNPT_LAST_CMD_OFFSET_ID_LENGTH + VUC_MICRON_GNPT_LAST_CMD_OFFSET_SIZE_LENGTH + VUC_MICRON_GNPT_LAST_CMD_OFFSET_VALUE_LENGTH), &ulTmp, VUC_MICRON_GNPT_SUB_CMD_STATUS_ID_LENGTH);
		ulTmp = VUC_MICRON_GNPT_SUB_CMD_STATUS_SIZE;
		memcpy((void *)(gulResponseBufferAddr + VUC_MICRON_GNPT_DATA_PAYLOAD_OFFSET + VUC_MICRON_GNPT_LAST_CMD_OFFSET_ID_LENGTH + VUC_MICRON_GNPT_LAST_CMD_OFFSET_SIZE_LENGTH + VUC_MICRON_GNPT_LAST_CMD_OFFSET_VALUE_LENGTH + VUC_MICRON_GNPT_SUB_CMD_STATUS_ID_LENGTH), &ulTmp, VUC_MICRON_GNPT_SUB_CMD_STATUS_SIZE_LENGTH);
		ulTmp = 0;
		memcpy((void *)(gulResponseBufferAddr + VUC_MICRON_GNPT_DATA_PAYLOAD_OFFSET + VUC_MICRON_GNPT_LAST_CMD_OFFSET_ID_LENGTH + VUC_MICRON_GNPT_LAST_CMD_OFFSET_SIZE_LENGTH + VUC_MICRON_GNPT_LAST_CMD_OFFSET_VALUE_LENGTH + VUC_MICRON_GNPT_SUB_CMD_STATUS_ID_LENGTH + VUC_MICRON_GNPT_SUB_CMD_STATUS_SIZE_LENGTH), &ulTmp, VUC_MICRON_GNPT_SUB_CMD_STATUS_LENGTH + VUC_MICRON_GNPT_SUB_CMD_RESERVED_LENGTH);
		ulTmp = VUC_MICRON_GNPT_RESPONSE_PAYLOAD_ID;
		memcpy((void *)(gulResponseBufferAddr + VUC_MICRON_GNPT_DATA_PAYLOAD_OFFSET + VUC_MICRON_GNPT_LAST_CMD_OFFSET_ID_LENGTH + VUC_MICRON_GNPT_LAST_CMD_OFFSET_SIZE_LENGTH + VUC_MICRON_GNPT_LAST_CMD_OFFSET_VALUE_LENGTH + VUC_MICRON_GNPT_SUB_CMD_STATUS_ID_LENGTH + VUC_MICRON_GNPT_SUB_CMD_STATUS_SIZE_LENGTH + VUC_MICRON_GNPT_SUB_CMD_STATUS_LENGTH + VUC_MICRON_GNPT_SUB_CMD_RESERVED_LENGTH), &ulTmp, VUC_MICRON_GNPT_RESPONSE_PAYLOAD_ID_LENGTH);
		memcpy((void *)(gulResponseBufferAddr + VUC_MICRON_GNPT_DATA_PAYLOAD_OFFSET + VUC_MICRON_GNPT_LAST_CMD_OFFSET_ID_LENGTH + VUC_MICRON_GNPT_LAST_CMD_OFFSET_SIZE_LENGTH + VUC_MICRON_GNPT_LAST_CMD_OFFSET_VALUE_LENGTH + VUC_MICRON_GNPT_SUB_CMD_STATUS_ID_LENGTH + VUC_MICRON_GNPT_SUB_CMD_STATUS_SIZE_LENGTH + VUC_MICRON_GNPT_SUB_CMD_STATUS_LENGTH + VUC_MICRON_GNPT_SUB_CMD_RESERVED_LENGTH + VUC_MICRON_GNPT_RESPONSE_PAYLOAD_ID_LENGTH), &ulResponsePayloadLength, VUC_MICRON_GNPT_RESPONSE_PAYLOAD_SIZE_LENGTH);
	}

	else if (VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS == gubMicronLastCmdCode) {
		U16 uwTmp;
		U8 ubTmp;
		ulResponsePayloadLength  = VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_DATA_PAYLOAD_LENGTH;
		ubTmp = VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_HEADER_VERSION;
		memcpy((void *)gulResponseBufferAddr, &ubTmp, VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_HEADER_VERSION_LENGTH);
		ubTmp = VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_DATA_TYPE;
		memcpy((void *)(gulResponseBufferAddr + VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_HEADER_VERSION_LENGTH), &ubTmp, VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_DATA_TYPE_LENGTH);
		uwTmp = VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_COMMAND_CLASS;
		memcpy((void *)(gulResponseBufferAddr + VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_HEADER_VERSION_LENGTH + VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_DATA_TYPE_LENGTH), &uwTmp, VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_COMMAND_CLASS_LENGTH);
		uwTmp = VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_COMMAND_CODE;
		memcpy((void *)(gulResponseBufferAddr + VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_HEADER_VERSION_LENGTH + VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_DATA_TYPE_LENGTH + VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_COMMAND_CLASS_LENGTH), &uwTmp, VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_COMMAND_CODE_LENGTH);
		memcpy((void *)(gulResponseBufferAddr + VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_HEADER_VERSION_LENGTH + VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_DATA_TYPE_LENGTH + VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_COMMAND_CLASS_LENGTH + VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_COMMAND_CODE_LENGTH), &guwVUCMicronResponseStatusCode, VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_STATUS_LENGTH);
		memcpy((void *)(gulResponseBufferAddr + VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_HEADER_VERSION_LENGTH + VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_DATA_TYPE_LENGTH + VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_COMMAND_CLASS_LENGTH + VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_COMMAND_CODE_LENGTH + VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_STATUS_LENGTH), &ulResponsePayloadLength, VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_SIZE_OF_DATA_LENGTH);
	}
	else if (VUC_MICRON_GET_BEC == gubMicronLastCmdCode) {
		ulRealDataLengh = MICRON_VUC_BEC_PAYLOAD_SIZE;
		GETBECResponseHEADER_t *pResponseHeader;
		pResponseHeader = (GETBECResponseHEADER_t *)gulResponseBufferAddr;
		if ( (ulRequestLength + gulVUCMicronVUCOffset ) < ulRealDataLengh ) {
			guwVUCMicronResponseStatusCode = VUC_MICRON_RESPONSE_STATUS_INCOMPLETE;
		}
		else {
			guwVUCMicronResponseStatusCode = VUC_MICRON_RESPONSE_STATUS_NO_ERROR;
		}
		pResponseHeader->uwStatus = guwVUCMicronResponseStatusCode;
	}
	else if ( VUC_MICRON_GET_NAND_BLOCK_ERASE_COUNT == gubMicronLastCmdCode) {
		ulRealDataLengh = FRAME_SIZE * FRAMES_PER_PAGE;

		if ( ulRequestLength < ulRealDataLengh ) {
			guwVUCMicronResponseStatusCode = VUC_MICRON_RESPONSE_STATUS_INCOMPLETE;
			GetEraseCountResponseHEADER_t *pResponseHeader;
			pResponseHeader = (GetEraseCountResponseHEADER_t *)gulResponseBufferAddr;
			pResponseHeader->uwStatus = guwVUCMicronResponseStatusCode;
		}
	}
#if (UNIFIED_LOG_EN)
	else if (VUC_MICRON_GET_UNIFIED_EVENT_LOG == gubMicronLastCmdCode) {
		if (0 == gUnifiedLogVariable.ulPayloadNeedXferSize) {
			gUnifiedLogVariable.ulPayloadNeedXferSize = ulRequestLength;
		}

		gulVUCMicronVUCOffset = gUnifiedLogVariable.ulTotalReturntoHostSize;
		if (UNIFIED_LOG_READ_FINISH == gUnifiedLogVariable.ubReadState) {
			//Case 1 : UNEL Read state = Finish, status code sholud be 0.
			if (gUnifiedLogVariable.ulPayloadNeedXferSize == (gulVUCMicronVUCOffset + SIZE_4KB)) {
				//The last 4K for this final C2 command.
				gubMicronVUCCMDNotDone = FALSE;
			}

		}
		else if (FALSE == gUnifiedLogVariable.Flag.btVUCBufferReady) {
			//Case 2 : btVUCBufferReady = False, status code should be incomplete, FW has collected sufficient data for this C2 command
			if (gUnifiedLogVariable.ulPayloadNeedXferSize == (gulVUCMicronVUCOffset + SIZE_4KB)) {
				//The last 4K for this C2 coomand.
				gUnifiedLogVariable.Flag.btVUCBufferReady = TRUE;
			}
		}
		else {
			// FW collected size is not enough for this C2 command and not finish yet.
			return;
		}
		gUnifiedLogVariable.ulTotalReturntoHostSize += SIZE_4KB;
		if (gUnifiedLogVariable.ulTotalReturntoHostSize == gUnifiedLogVariable.ulPayloadNeedXferSize) {
			//The last 4K for this C2 command
			gUnifiedLogVariable.ulPayloadNeedXferSize = 0;
			gUnifiedLogVariable.ulTotalReturntoHostSize = 0;
		}
	}
#endif /*(UNIFIED_LOG_EN)*/
	else if (VUC_MICRON_CLEAR_EVENT_LOG == gubMicronLastCmdCode) {

		ClearEventLogResponseHEADER_t *pResponseHeader;

		memset((void *)pCmd->cur_phyMemAddr, 0, VUC_MICRON_CLEAR_EVENT_LOG_HEADER_LENGTH);

		pResponseHeader = (ClearEventLogResponseHEADER_t *)pCmd->cur_phyMemAddr;

		pResponseHeader->ubResponseHeaderFormatVersion = 0x00;
		pResponseHeader->ubResponseDataFotmatVersion = 0x00;
		pResponseHeader->uwCMDClass = VUC_MICRON_CLEAR_EVENT_LOG_CLASS;
		pResponseHeader->uwCMDCode = VUC_MICRON_CLEAR_EVENT_LOG_CODE;
		pResponseHeader->uwStatus = 0;
		pResponseHeader->ulDataPayloadSize = 0x00;

		if (VUC_MICRON_CLEAR_EVENT_LOG_UNIFIED_NAND_EVENT_LOG == gubLastClearEventLogType) {
#if (UNIFIED_LOG_EN)
			if (gUnifiedLogVariable.Flag.btTrimDoing) {
				guwVUCMicronResponseStatusCode = VUC_MICRON_RESPONSE_STATUS_EXECUTING;
				pResponseHeader->uwStatus = guwVUCMicronResponseStatusCode;
			}
			else {
				// Reset value of UnifiedLog
				gpVT->UnifiedLog.ulCurrentLCA = 0;
				gpVT->UnifiedLog.ubPOREventCountDown = UNIFIED_LOG_POWER_CYCLE_LOG_INTERVAL;
				gpVT->UnifiedLog.Flag.ubAll = 0;
				gUnifiedLogVariable.ulCurrentLCA = gUnifiedLogVariable.ulLCAHead;
				gUnifiedLogVariable.ulInsertLogIdx = gUnifiedLogVariable.ulLogBase;
				gUnifiedLogVariable.Flag.btEmpty = TRUE;
				gUnifiedLogVariable.uwCurrentByteInBuffer = 0;
				gUnifiedLogVariable.uwCurrentEventCountInBuffer = 0;
				gUnifiedLogVariable.ubNoTemperatureLogCount = 0;
			}
#endif /*(UNIFIED_LOG_EN)*/
		}
	}
	else if (VUC_MICRON_GET_VT_SWEEP == gubMicronLastCmdCode) {
		ulRealDataLengh = VUC_MICRON_VT_SWEEP_RESPONSE_HEADER_LENGTH + VUC_MICRON_VT_SWEEP_RESPONSE_DATA_PAYLOAD_HEADER_LENGTH + gFlhEnv.uwPageTotalByteCnt * BITS_PER_BYTE * 2;

		if (0 == pCmd->done_LCA) {
			VTSweepResponseHEADER_t *pResponseHeader;
			pResponseHeader = (VTSweepResponseHEADER_t *)gulResponseBufferAddr;
			if ( (gulVUCMicronVUCOffset + ulRequestLength) < ulRealDataLengh ) {
				guwVUCMicronResponseStatusCode = VUC_MICRON_RESPONSE_STATUS_INCOMPLETE;
				pResponseHeader->uwStatus = guwVUCMicronResponseStatusCode;
				gubMicronVUCCMDNotDone = TRUE;
			}
			else {
				guwVUCMicronResponseStatusCode = VUC_MICRON_RESPONSE_STATUS_NO_ERROR;
				pResponseHeader->uwStatus = guwVUCMicronResponseStatusCode;
				gubMicronVUCCMDNotDone = FALSE;
			}
		}
	}
	// Return to host
	if (!((VUC_MICRON_CLEAR_EVENT_LOG == gubMicronLastCmdCode) && ( VUC_MICRON_COMMON_VS_COMMANDS == gubMicronLastCmdClass)) ) {
		// ulResponseBufferOffset : 4k offset in one NVme Command,  gulVUCMicronVUCOffset : The length of data has been transferred
#if (BURNER_MODE_EN)
		memcpy((void *)pCmd->cur_phyMemAddr, (void *)gulResponseBufferAddr, VUC_MICRON_RESPONSE_HEADER_SIZE);
		if (0 == gulVUCMicronVUCOffset) {
			gulVUCMicronVUCOffset += VUC_MICRON_RESPONSE_HEADER_SIZE;
		}
		memcpy((void *)(pCmd->cur_phyMemAddr + VUC_MICRON_RESPONSE_HEADER_SIZE), (void *)(gulResponseBufferAddr + gulVUCMicronVUCOffset), ulRequestLength - VUC_MICRON_RESPONSE_HEADER_SIZE);
		gulVUCMicronVUCOffset += ulRequestLength - VUC_MICRON_RESPONSE_HEADER_SIZE;

#else /*(BURNER_MODE_EN)*/
		if ((0 == pCmd->done_LCA) && (VUC_MICRON_GET_UNIFIED_EVENT_LOG != gubMicronLastCmdCode)) {
			// Need transfer header to host in each NVme Command
			// UNEL has fill header into vuc buffer, not necessary to handle header before DMA
			memcpy((void *)pCmd->cur_phyMemAddr, (void *)gulResponseBufferAddr, VUC_MICRON_RESPONSE_HEADER_SIZE);
			if (0 == gulVUCMicronVUCOffset) {
				// Only First header will be counted in gulVUCMicronVUCOffset
				gulVUCMicronVUCOffset += VUC_MICRON_RESPONSE_HEADER_SIZE;
			}
			memcpy((void *)(pCmd->cur_phyMemAddr + VUC_MICRON_RESPONSE_HEADER_SIZE), (void *)(gulResponseBufferAddr + gulVUCMicronVUCOffset), BC_4KB - VUC_MICRON_RESPONSE_HEADER_SIZE);
			gulVUCMicronVUCOffset += (BC_4KB - VUC_MICRON_RESPONSE_HEADER_SIZE);
		}
		else {
			memcpy((void *)(pCmd->cur_phyMemAddr), (void *)(gulResponseBufferAddr + gulVUCMicronVUCOffset), BC_4KB);
			gulVUCMicronVUCOffset += BC_4KB;
		}
#endif /*(BURNER_MODE_EN)*/
	}

#if (!BURNER_MODE_EN)
	pCmd->state = FW_PROCESS_DONE;
#endif /*(!BURNER_MODE_EN)*/
}

#endif /*(VUC_MICRON_NICKS_EN)*/
