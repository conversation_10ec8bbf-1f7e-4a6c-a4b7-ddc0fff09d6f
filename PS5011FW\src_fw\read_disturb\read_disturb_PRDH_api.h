/****************************************************************************
 *
 *  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
 *  All rights reserved
 *
 *  The content of this document is confidential and shall be applied
 *  subject to the terms and conditions of the license agreement and
 *  other applicable laws. Any unauthorized access, use or disclosure
 *  of this document is strictly prohibited and may be punishable
 *  under laws.
 *
 *  read_disturb_PRDH_api.h
 *
 *
 *
 ****************************************************************************/

#ifndef _READ_DISTURB_PRDH_API_H_
#define _READ_DISTURB_PRDH_API_H_

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "setup.h"
#include "hal/cop0/cop0_api.h"
#include "read_disturb/read_disturb_api.h"

/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */

// Temp N48 flash parameter setting
#define SLC_REFRESH_THRESHOLD				(91)
#define QLC_REFRESH_THRESHOLD				(91)

#define VICTIM_PAGE_RULE_3_PAGE_LIST_NUM	(20)

#define SLC_WORDLINE_BOTTOM_EDGE_0			(0)
#define SLC_WORDLINE_TOP_EDGE_0				(87)
#define SLC_WORDLINE_BOTTOM_EDGE_1			(88)
#define SLC_WORDLINE_TOP_EDGE_1				(175)

#define QLC_WORDLINE_BOTTOM_EDGE_0			(0)
#define QLC_WORDLINE_TOP_EDGE_0				(88)
#define QLC_WORDLINE_BOTTOM_EDGE_1			(89)
#define QLC_WORDLINE_TOP_EDGE_1				(177)

#define N48R_SUBBLK_NUM						(4)
#define N48R_PAGE_PER_QLC_CELL				(4)
#define N48R_PAGE_PER_MLC_CELL				(2)
#define N48R_PAGE_PER_QLC_CELL_BIT_NUM		(2)
#define N48R_PAGE_PER_QLC_WORD_LINE			(N48R_SUBBLK_NUM * N48R_PAGE_PER_QLC_CELL)
#define N48R_PAGE_PER_MLC_WORD_LINE			(N48R_SUBBLK_NUM * N48R_PAGE_PER_MLC_CELL)

#define MAX_SUPPORT_CE_NUM					(16)
#define MAX_SUPPORT_DIE_NUM					(32)
#define MAX_SUPPORT_BLK_NUM_PER_VB			(64)

#define SCAN_INFO_QUEUE_DEPTH				(2)
#define PAGE_LIST_TYPE_NUM					(2) // QLC , SLC

#define INVALID_URN_VALUE					(0xFFFF)
#define INVALID_PAGE						(0xFFFF)
#define INVALID_BLK_TYPE					(0xFF)

#define INVALID_TIMER						(0xFFFFFFFF)

#define BLK_TYPE_NONE						(0)
#define BLK_TYPE_OPEN_TABLE 				(1)
#define BLK_TYPE_OPEN_GR					(2)
#define BLK_TYPE_OPEN_INIT_INFO				(3)
#define BLK_TYPE_OPEN_VT					(4)
#define BLK_TYPE_OPEN_VT_CHILD				(5)
#define BLK_TYPE_OPEN_RS_PARITY				(6)
#define BLK_TYPE_FULL 						(7)
#define BLK_TYPE_INVALID					(0xFF)
#define BLK_TYPE_OPEN_NUM					(BLK_TYPE_FULL)

#define EVENT_TYPE_FULL_NUM					(EVENT_TYPE_OPEN_BLK)

#define M_READ_DISTURB_SLC_FORCE_COPY_THRESHOLD_ADJUSTMENT(x)      ((x) * 100)
#define M_READ_DISTURB_SLC_FORCE_COPY_THRESHOLD_REDUCTION(x)      ((x) / 100)

// OPTD memory map
#define OPTD_ANDES_MICRON_RESTORE_FWPCA_BASE	(OPTD_ANDES_READ_DISTURB_PRDH_BASE)
#define OPTD_ANDES_MICRON_READ_CNT_BASE			(OPTD_ANDES_MICRON_RESTORE_FWPCA_BASE + OPTD_ANDES_MICRON_RESTORE_FWPCA_SIZE) // +0x80
#define OPTD_ANDES_MICRON_COPY_READ_CNT_BASE	(OPTD_ANDES_MICRON_READ_CNT_BASE + OPTD_ANDES_MICRON_READ_CNT_NO_USE_SIZE) // +0x180
#define OPTD_ANDES_MICRON_SCAN_INFO_BASE		(OPTD_ANDES_MICRON_READ_CNT_BASE + OPTD_ANDES_MICRON_READ_CNT_SIZE)
#define OPTD_ANDES_MICRON_SCAN_TIME_BASE		(OPTD_ANDES_MICRON_SCAN_INFO_BASE + OPTD_ANDES_MICRON_SCAN_INFO_SIZE)

#define OPTD_ANDES_MICRON_RESTORE_FWPCA_SIZE	(0x80) // 16CE * 4 byte * 2 depth = 128
#define OPTD_ANDES_MICRON_READ_CNT_NO_USE_SIZE	(0x100) // 4 byte * MAX_SUPPORT_BLK_NUM_PER_VB = 256
#define OPTD_ANDES_MICRON_READ_CNT_SIZE			(sizeof(ReadDisturbPRDHAndesReadCnt_t))
#define OPTD_ANDES_MICRON_SCAN_INFO_SIZE		(sizeof(ReadDisturbPRDHScanInfo_t))

#define OPTD_ANDES_MICRON_COPY_READ_CNT_SIZE	(0x800)

#define MICRON_READ_CNT_SAVE_UNIT_OFFSET		(0x780)

#define CHECK_BIT_ERROR_CNT_MT_NUM				(2)

/*
 * ---------------------------------------------------------------------------------------------------
 *  enum
 * ---------------------------------------------------------------------------------------------------
 */
enum ReadDisturbPRDHStateEnum {
	READ_DISTURB_GET_UNIT = 0,
	READ_DISTURB_CHECK_UNIT,
	READ_DISTURB_WAIT_VERIFY_DONE,
	READ_DISTURB_CREATE_ERROR_LOG,
	READ_DISTURB_FINISH
};

enum ReadDisturbPRDHVictimPageTypeEnum {
	VICTIM_PAGE_RULE_1 = 0,
	VICTIM_PAGE_RULE_2,
	VICTIM_PAGE_RULE_3,
	VICTIM_PAGE_RULE_NUM
};

enum ReadDisturbPRDHScanEventTypeEnum {
	EVENT_TYPE_FULL_QLC = 0,
	EVENT_TYPE_FULL_D3SLC,
	EVENT_TYPE_FULL_SLC,
	EVENT_TYPE_OPEN_BLK,
	EVENT_TYPE_NUM
};

enum ReadDisturbPRDHAddDriveLogEnum  {
	READ_DISTURB_PRDH_READVERIFY_LOG = 0,
	READ_DISTURB_PRDH_FORCECOPY_LOG,
	READ_DISTURB_PRDH_DRIVE_LOG_NUM
};

/*
 * ---------------------------------------------------------------------------------------------------
 *  data types
 * ---------------------------------------------------------------------------------------------------
 */

typedef union {
	U32 ulReadCnt;
	struct {
		U16 uwReadCnt;
		U16 uwURN; //Uniform Random Number
	} PRDH;
} MicronReadCnt_t;

typedef struct {
	MicronReadCnt_t Record;
	U32 ulFWPCA;
} MicronScanInfo_t;


typedef struct {//256 + 2048 , previous 256 byte will not save
	MicronReadCnt_t OpenSLCBlk[BLK_TYPE_OPEN_NUM][MAX_SUPPORT_BLK_NUM_PER_VB];//256 + 1536
	MicronReadCnt_t Full[EVENT_TYPE_FULL_NUM][MAX_SUPPORT_DIE_NUM];//384
	U32 ulAndesRecordOpenSLCUnit[BLK_TYPE_OPEN_NUM];//28  // to align 4 byte , use U32
	U32 ulRsv[25]; // 100
} ReadDisturbPRDHAndesReadCnt_t;

typedef struct {
	U32 ulScanBMP[EVENT_TYPE_NUM]; // 20
	MicronScanInfo_t Full[EVENT_TYPE_FULL_NUM][MAX_SUPPORT_DIE_NUM][SCAN_INFO_QUEUE_DEPTH];
	MicronScanInfo_t OpenSLCBlk[BLK_TYPE_OPEN_NUM];
} ReadDisturbPRDHScanInfo_t;

typedef struct {
	U32 ulFWPCA[MAX_SUPPORT_CE_NUM][COP0_PATCH_CMD_INFO_DEPTH];
} ReadDisturbPRDHAndesRestoreInfo_t;

typedef struct {
	U8 ubBlkType;
	U16 uwRecordScanIdx;
	U16 uwURN; //Uniform Random Number
	U32 ulReadCnt;
	U32 ulFWPCA;
	U16 uwVictimPage[VICTIM_PAGE_RULE_NUM];
	U32 ulVictimPlaneIdx[VICTIM_PAGE_RULE_NUM];
} ReadDisturbPRDH_t;

#if (READ_DISTURB_PRDH_RECORD_SCAN_TIME_EN)
typedef struct {
	U32 ulStartTime[EVENT_TYPE_FULL_NUM][MAX_SUPPORT_DIE_NUM][SCAN_INFO_QUEUE_DEPTH];
} ReadDisturbPRDHScanTime_t;
#endif /* (READ_DISTURB_PRDH_RECORD_SCAN_TIME_EN) */

/*
 * ---------------------------------------------------------------------------------------------------
 *   macros
 * ---------------------------------------------------------------------------------------------------
 */

#define M_ARM_MODIFY_OPTD_REQUEST_START() do {\
	gpComm2Andes_Info->ubARMModifyOPTDRequest = TRUE;\
	__asm("DSB");\
	while (TRUE != gpComm2Andes_Info->ubARMModifyOPTDRequest);\
	while (TRUE == gpComm2Andes_Info->ubAndesModifyOPTDRequest);\
	} while(0)

#define M_ARM_MODIFY_OPTD_REQUEST_END() do {\
	__asm("DSB");\
	gpComm2Andes_Info->ubARMModifyOPTDRequest = FALSE;\
	} while(0)

/*
 * ---------------------------------------------------------------------------------------------------
 *   extern global variables
 * ---------------------------------------------------------------------------------------------------
 */
extern volatile ReadDisturbPRDHAndesReadCnt_t *gpReadDisturbPRDHAndesReadCnt;
extern volatile ReadDisturbPRDHAndesRestoreInfo_t *gpReadDisturbPRDHAndesRestoreInfo;
extern volatile ReadDisturbPRDHScanInfo_t	*gpReadDisturbPRDHScanInfo;
extern volatile ReadDisturbPRDH_t	gReadDisturbPRDH;

#if (READ_DISTURB_PRDH_RECORD_SCAN_TIME_EN)
extern volatile ReadDisturbPRDHScanTime_t *gpReadDisturbPRDHScanTime;
#endif /* (READ_DISTURB_PRDH_RECORD_SCAN_TIME_EN) */
#if (READ_DISTURB_PRDH_EN)
extern U16 guwReadDisturbPRDHPredeterminePageList[PAGE_LIST_TYPE_NUM][VICTIM_PAGE_RULE_3_PAGE_LIST_NUM]; // SLC QLC
#endif
/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */


// COP0_Init
AOM_INIT   void ReadDisturbPRDHParameterInit(void);

// FTLInit
AOM_INIT_2 void ReadDisturbPRDHRecoverReadCnt(U32 ulBufferAddress);

// PatchCmd
#if(IOR_EN) //IOR will use PatchCmdSearchCOP0DMAInfo in AOMIORPatch
U8   ReadDisturbPRDHParseBlkType(U16 uwBypassReadCntHandle, U8 ubBlkType, U32 ulFWPCA);
#else
AOM_PATCH_CMD U8   ReadDisturbPRDHParseBlkType(U16 uwBypassReadCntHandle, U8 ubBlkType, U32 ulFWPCA);
#endif
AOM_PATCH_CMD void ReadDisturbPRDHManualFullBlkHandle(U32 ulFWPCA, U8 ubPCARule, U8 ubMultiPlaneBMP, U8 ubReadCmdCnt, U8 ubDie);
AOM_PATCH_CMD void ReadDisturbPRDHManualReadCntHandle(U8 ubBlkType, U32 ulFWPCA, U8 ubReadCmdCnt, U8 ubMultiPlaneBMP, U8 ubPCARule, U32 ulFSA);

AOM_READ_DISTURB_PRDH void ReadDisturbPRDHCopyReadCntToEC(void);
AOM_READ_DISTURB_PRDH void ReadDisturbPRDHHandleAfterVTInit(void);
AOM_READ_DISTURB_PRDH void ReadDisturbPRDHUpdateVTMotherPECG(void);
AOM_READ_DISTURB_PRDH void ReadDisturbPRDHClearOpenBlkInfo(U16 uwUnit);
AOM_READ_DISTURB_PRDH void ReadDisturbPRDHClearFullScanInfo(U16 uwUnit);
AOM_READ_DISTURB_PRDH void ReadDisturbPRDHRebuildScanInfo(void);
AOM_READ_DISTURB_PRDH void ReadDisturbPRDHUnfinishScanEventHandle(void);
AOM_READ_DISTURB_PRDH U8   ReadDisturbPRDHCheckBitErrorCnt(U16 uwPage, U8 ubRefreshThreshold, U8 ubALUSelect);
AOM_READ_DISTURB_PRDH void ReadDisturbPRDHVictimPageFurtherCheck(U8 ubPageType, U8 ubALUSelect, U16 uwUnitIdx, U8 ubPlaneBank, U16 uwPage, U8 ubTableIdx);
AOM_READ_DISTURB_PRDH void ReadDisturbPRDHFillVictimPlaneIdx(U8 ubSLC, U8 ubPlaneBankIdx);
AOM_READ_DISTURB_PRDH U16  ReadDisturbPRDHGetQLCPageFromCoord(U8 ubX, U16 uwY);
AOM_READ_DISTURB_PRDH U16  ReadDisturbPRDHGetPageFromCoord(U8 ubSLC, U8 ubX, U16 uwY);
AOM_READ_DISTURB_PRDH void ReadDisturbPRDHSelectVictimPage(U8 ubSLC, U16 uwPage, U8 ubPlaneBankIdx);
AOM_READ_DISTURB_PRDH U8   ReadDisturbPRDHDecideCheck(U16 uwOverReadCntUnit);
AOM_READ_DISTURB_PRDH void ReadDisturbPRDHAddDriveLog(U8 ubLogType, U16 uwUnitIdx, U32 ulReadCnt, U16 uwEraseCnt);
AOM_READ_DISTURB_PRDH void ReadDisturbPRDHGetScanInfo(U8 *pubBlkType, U32 *pulFWPCA);
AOM_READ_DISTURB_PRDH void ReadDisturbPRDHRecordScanTime(void);
AOM_READ_DISTURB_PRDH void ReadDisturbPRDHCreateErrorLog(void);
AOM_READ_DISTURB_PRDH void ReadDisturbPRDHGetOrCheckUnit(void);

//After ReadVerify Done
AOM_READ_VERIFY void ReadDisturbPRDHClearScanInfo(void);
AOM_READ_VERIFY void ReadDisturbPRDHFinish(void);

//Common for tie in COP0 read
FW_BTCM0_SECTION U8 ReadDisturbPRDHManualOpenBlkHandle(U8 ubBlkType, U16 uwUnit, U8 ubPlaneBank, U8 ubReadCmdCnt);
FW_BTCM0_SECTION U8 ReadDisturbPRDHCheckBlkType(U16 uwUnit);
void ReadDisturbPRDHClearOpenBlkReadCnt(U8 ubBlkType);
/*
 * ---------------------------------------------------------------------------------------------------
 *  inline
 * ---------------------------------------------------------------------------------------------------
 */
#if (READ_DISTURB_PRDH_EN)

INLINE void ReadDisturbPRDHClearOpenBlkReadCntForProgramFail(U16 uwUnit, U8 ubPlaneBankIdx)
{
	U8 ubBlkType = ReadDisturbPRDHCheckBlkType(uwUnit);
	if (ubBlkType && (BLK_TYPE_FULL != ubBlkType)) {
		// Open unit
		M_ARM_MODIFY_OPTD_REQUEST_START();
		gpReadDisturbPRDHAndesReadCnt->OpenSLCBlk[ubBlkType][ubPlaneBankIdx].ulReadCnt = 0;
		M_ARM_MODIFY_OPTD_REQUEST_END();
	}
}

INLINE void ReadDisturbPRDHClearOldVTMotherReadCnt(U8 ubOldVTBlkIndex)
{
	U8 ubBlkNum;
	M_ARM_MODIFY_OPTD_REQUEST_START();
	for (ubBlkNum = 0; ubBlkNum < VT_BACKUP_CNT; ubBlkNum ++) {
		gpReadDisturbPRDHAndesReadCnt->OpenSLCBlk[BLK_TYPE_OPEN_VT][(ubOldVTBlkIndex + ubBlkNum)].ulReadCnt = 0;
	}
	M_ARM_MODIFY_OPTD_REQUEST_END();
}

INLINE void ReadDisturbPRDHUpdateSLCPECG(void)
{
	U8 ubPECG; //ProgramEraseCntGroup
	U16 AverageEraseCnt = (gpVT->ulTotalD1EraseCount / gpVT->uwTotalD1UnitNum);
	U16 *puwEraseCntRange;

	if (gFlhEnvMP.ulD1PECycle <= AverageEraseCnt) {

		ubPECG = (READ_DISTURB_MAX_ERASE_CNT_LEVEL_NUM - 1);
	}
	else {

		puwEraseCntRange = (U16 *)&gReadDisturb.Threshold.uwSLCEraseCntRange;

		for (ubPECG = 0; ubPECG < READ_DISTURB_MAX_ERASE_CNT_LEVEL_NUM; ubPECG++) {
			if (AverageEraseCnt <= puwEraseCntRange[ubPECG]) {
				break;
			}
		}
	}

	gpComm2Andes_Info->ubPECG[COP0_PCA_RULE_3] = ubPECG;
}

INLINE void ReadDisturbPRDHUpdateD3SLCAndQLCPECG(void)
{
	U8 ubD3SLCPECG, ubQLCPECG; //ProgramEraseCntGroup
	U16 AverageEraseCnt = (gpVT->ulTotalEraseCount / (gpVT->uwTotalD3UnitNum - gpVT->VT.ubVTUnitCnt - (DRIVE_LOG_EN ? DRIVE_LOG_RESERVE_NUM : 0)));
	U16 *puwEraseCntRange;

	if (gFlhEnv.NandD3PECycle <= AverageEraseCnt) {

		ubD3SLCPECG = (READ_DISTURB_MAX_ERASE_CNT_LEVEL_NUM - 1);
		ubQLCPECG 	= (READ_DISTURB_MAX_ERASE_CNT_LEVEL_NUM - 1);
	}
	else {
		// QLC
		puwEraseCntRange = (U16 *)&gReadDisturb.Threshold.uwTLCEraseCntRange;
		for (ubQLCPECG = 0; ubQLCPECG < READ_DISTURB_MAX_ERASE_CNT_LEVEL_NUM; ubQLCPECG++) {
			if (AverageEraseCnt <= puwEraseCntRange[ubQLCPECG]) {
				break;
			}
		}

		// Dynamic SLC , equivalent to SLC PEC
		AverageEraseCnt = AverageEraseCnt * (gFlhEnvMP.ulD1PECycle / gFlhEnv.NandD3PECycle);
		puwEraseCntRange = (U16 *)&gReadDisturb.Threshold.uwSLCEraseCntRange;
		for (ubD3SLCPECG = 0; ubD3SLCPECG < READ_DISTURB_MAX_ERASE_CNT_LEVEL_NUM; ubD3SLCPECG++) {
			if (AverageEraseCnt <= puwEraseCntRange[ubD3SLCPECG]) {
				break;
			}
		}

	}

	gpComm2Andes_Info->ubPECG[COP0_PCA_RULE_0] = ubQLCPECG;
	gpComm2Andes_Info->ubPECG[COP0_PCA_RULE_2] = ubD3SLCPECG;
}

#endif /* (READ_DISTURB_PRDH_EN) */

#if (READ_DISTURB_PRDH_BURNER_EN)
INLINE void ReadDisturbPRDHEraseCntTableInit(void)
{
	U8 ubBlkType;
	ReadDisturbPRDHAndesReadCnt_t *pReadDisturbPRDHAndesReadCnt	= (ReadDisturbPRDHAndesReadCnt_t *)(DBUF_READ_DISTURB_PRDH_INFO - 0x100);

	for (ubBlkType = BLK_TYPE_OPEN_TABLE; ubBlkType < BLK_TYPE_OPEN_NUM; ubBlkType++) {
		if (BLK_TYPE_OPEN_VT == ubBlkType) {
			continue;
		}
		if (0 == pReadDisturbPRDHAndesReadCnt->ulAndesRecordOpenSLCUnit[ubBlkType]) {
			pReadDisturbPRDHAndesReadCnt->ulAndesRecordOpenSLCUnit[ubBlkType] = INVALID_UNIT;
		}
	}
}
#endif /* (READ_DISTURB_PRDH_BURNER_EN) */

#endif /* _READ_DISTURB_PRDH_API_H_ */
