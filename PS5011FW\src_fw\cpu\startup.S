//=============================================================================
// Copyright PHISON.
// ARM Cortex-R5(F) Startup Code
//=============================================================================
#include "setup.h"
#include "arm.h"

// MPU region defines

	.equ	Region_32B,		0x4
	.equ	Region_64B,		0x5
	.equ	Region_128B,	0x6
	.equ	Region_256B,	0x7
	.equ	Region_512B,	0x8
	.equ	Region_1K,		0x9
	.equ	Region_2K,		0xA
	.equ	Region_4K,		0xB
	.equ	Region_8K,		0xC
	.equ	Region_16K,		0xD
	.equ	Region_32K,		0xE
	.equ	Region_64K,		0xF
	.equ	Region_128K,	0x10
	.equ	Region_256K,	0x11
	.equ	Region_512K,	0x12
	.equ	Region_1M,		0x13
	.equ	Region_2M,		0x14
	.equ	Region_4M,		0x15
	.equ	Region_8M,		0x16
	.equ	Region_16M,		0x17
	.equ	Region_32M,		0x18
	.equ	Region_64M,		0x19
	.equ	Region_128M,	0x1A
	.equ	Region_256M,	0x1B
	.equ	Region_512M,	0x1C
	.equ	Region_1G,		0x1D
	.equ	Region_2G,		0x1E
	.equ	Region_4G,		0x1F

	.equ	Region_Enable,	BIT0

	.equ	Execute_Never,	BIT12

	.equ	Strongly_ordered,	0x00 // Non-cacheable
	.equ	Device_Shared,		0x01 // Non-cacheable
	.equ	Normal_WT_nWA,		0x02 // Write-through, no write-allocate
	.equ	Normal_WB_nWA,		0x03 // Write-back, no write-allocate
	.equ	Normal_nShared_nC,	0x08 // Non-cacheable
	.equ	Normal_WB_WA,		0x0B // Write-back, write-allocate
	.equ	Normal_Shared,		0x0C // Non-cacheable
	.equ	Device_nShared,		0x10 // Non-cacheable

	.equ	No_Access,			0x0
	.equ	Full_Access,		0x3
	.equ	Read_Only,			0x6

// ACTLR register define
	.equ	DILS,				BIT6
	.equ	BP_Bit0,			BIT15
	.equ	BP_Bit1,			BIT16
	.equ	RSDIS,				BIT17

// Control register define
	.equ	MPU_EN,					BIT0	//control register bit0
	.equ	Data_Cache_EN,			BIT2	//control register bit2
	.equ	Instruction_Cache_EN,	BIT12	//control register bit12

//=============================================================================
// Set MPU region for PS5011
//=============================================================================
// refer to DDI0460D_cortex_r5_r1p2_trm.pdf P192
// The base address must align to region-sized boundary.
// see below MPU Configuration Notes:
#if PS5017_EN
	.equ	MONITOR_STACK_OVERFLOW_BASE,	0x0088F140  // 32B
#elif PS5021_EN /* PS5017_EN */
	.equ	MONITOR_STACK_OVERFLOW_BASE,	0x0089F140  // 32B
	.equ	COP0_OPTD_1_BASE,				0x12A8000  // 32KB
	.equ	COP0_OPTD_2_BASE,				0x12B0000  // 16BB
	.equ	COP0_OPTD_3_BASE,				0x12B4000  // 8BB
#endif /* PS5017_EN */

#if (PS5017_EN || PS5021_EN)
	.equ	AXI2AHB_REG_BASE,				0xF8000000  // 2MB (system PD0, PD1, PIC, DZIP, reserved)

	// 5019 HMB region - 0x4C000000 to 0xF1FFFFFF (address and size should be align)
	.equ	HMB_1_BASE,					0x4C000000  // 64MB
	.equ	HMB_2_BASE,					0x50000000  // 256MB
	.equ	HMB_3_BASE,					0x60000000  // 512MB
	.equ	HMB_4_BASE,					0x80000000  // 1GB
	.equ	HMB_5_BASE,					0xC0000000  // 512MB
	.equ	HMB_6_BASE,					0xE0000000  // 256MB
	.equ	HMB_7_BASE,					0xF0000000  // 32MB

	.equ  	DBUF_DCACHE_0_BASE, 			DBUF_DCACHE_START_ADDR
	.equ  	DBUF_DCACHE_0_SIZE, 			0x1000	    // 4K
	.equ  	DBUF_DCACHE_1_BASE,				(DBUF_DCACHE_0_BASE+DBUF_DCACHE_0_SIZE)
	.equ  	DBUF_DCACHE_1_SIZE, 			0x4000	    // 16K
	.equ  	DBUF_DCACHE_2_BASE,				(DBUF_DCACHE_1_BASE+DBUF_DCACHE_1_SIZE)
	.equ  	DBUF_DCACHE_2_SIZE, 			0x8000	    // 32K
	.equ	FWLB_DCACHE_BASE,				(0x32800000)
	// <- E17_porting
	.equ  	RS_BPT_BASE,     				0x01270000  // 8KB (DCACHE)
	.equ  	RS_BPT_BASE1,    				0x01272000  // 1KB (DCACHE)
	// -> E17_porting
	.equ    SPI_RAM_BASE,	                0x40000000
#else /* (PS5017_EN || PS5021_EN) */
	.equ	AXI2AHB_REG_BASE,				0x00800000  // 4MB (system PD0, PD1, PIC, DZIP, reserved)
	.equ	HW_IP_REG_BASE,					0x00C00000  // 1MB (0x00C00000 ~ 0x00CFFFFF)
	.equ	HW_IP_XZIP_REG_BASE,			0x002F0000  // 4KB (0x002F0000 ~ 0x002F0FFF)
	.equ	MONITOR_STACK_OVERFLOW_BASE,	0x000CF140  // 32B

	.equ	D2H_Z1_0_BASE,					0x40000000  // 1GB
	.equ	D2H_Z1_1_BASE,					0x80000000  // 1GB
	.equ	D2H_Z1_2_BASE,					0xC0000000  // 512MB
	.equ	D2H_Z2_BASE,					0xE0000000  // 512MB

	.equ  	DBUF_DCACHE_0_BASE, 			DBUF_DCACHE_START_ADDR
	.equ  	DBUF_DCACHE_0_SIZE, 			0x4000	    // 16K
	.equ  	DBUF_DCACHE_1_BASE,				(DBUF_DCACHE_START_ADDR+DBUF_DCACHE_0_SIZE)	
	.equ	FWLB_DCACHE_BASE,				(0x32800000)
	.equ	DBUF_BASE,						DBUF_BASE_ADDR
	.equ	PCIE_PROHIBIT_ADDR_BASE,		0x00CF2000
#endif  /* (PS5017_EN || PS5021_EN) */

#if (PS5013_EN)
MPU_region_setting:
	/* Region 0 - Background */
	.word	0x0															/* base address   */
	.word	0x0 | (Region_4G << 1) | Region_Enable						/* size & enable  */
	.word	0x0 | (Full_Access << 8) | Normal_Shared					/* access control */

	/* Region 1 - Code/Execute region */
	.word	0x0															/* base address   */
	.word	0x0 | (Region_512K << 1) | Region_Enable					/* size & enable  */
	.word	0x0 | (Full_Access << 8) | Normal_Shared					/* access control */

	/* Region 2 (system PD0, PD1, PIC, DZIP register - 4MB include reserved space) */
	.word	AXI2AHB_REG_BASE											/* base address   */
	.word	0x0 | (Region_4M << 1) | Region_Enable						/* size & enable  */
	.word	0x0 | Execute_Never | (Full_Access << 8) | Device_Shared	/* access control */

	/* Region 3 - HW IP register group 0 address from (0x00C00000 ~ 0x00CFFFFF), size: 1MB */
	.word	HW_IP_REG_BASE												/* base address   */
	.word	0x0 | (Region_1M << 1) | Region_Enable						/* size & enable  */
	.word	0x0 | Execute_Never | (Full_Access << 8) | Device_Shared	/* access control */

	/* Region 4 - HW IP XZIP register address from (0x002F0000 ~ 0x002F0FFF), size: 4KB */
	.word	HW_IP_XZIP_REG_BASE											/* base address   */
	.word	0x0 | (Region_4K << 1) | Region_Enable						/* size & enable  */
	.word	0x0 | Execute_Never | (Full_Access << 8) | Device_Shared	/* access control */

	/* Region 5 (Non access region - to monitor stack overflow) */
	.word	MONITOR_STACK_OVERFLOW_BASE									/* base address   */
	.word	0x0 | (Region_32B << 1) | Region_Enable						/* size & enable  */
	.word	0x0 | (Read_Only << 8)										/* access control */

	/* Region 6 - Vector region - read only protect vector (0x00000000 ~ 0x0000003F) */
	.word	0x0															/* base address   */
	.word	0x0 | (Region_64B << 1) | Region_Enable						/* size & enable  */
	.word	0x0 | (Read_Only << 8) | Normal_Shared						/* access control */

#if (FALSE == HMB_DCACHE_ENABLE)
	/* Region 7 - D2H Z1_0 region (0x40000000 ~ 0x7FFFFFFF) */
	.word	D2H_Z1_0_BASE												/* base address   */
	.word	0x0 | (Region_1G << 1) | Region_Enable						/* size & enable  */
	.word	0x0 | (No_Access << 8)										/* access control */

	/* Region 8 - D2H Z1_1 Z1_2 Z2 region (0x80000000 ~ 0xFFFFFFFF) */
	.word	D2H_Z1_1_BASE												/* base address   */
	.word	0x0 | (Region_2G << 1) | Region_Enable						/* size & enable  */
	.word	0x0 | (No_Access << 8)										/* access control */

#else  /* (FALSE == HMB_DCACHE_ENABLE) */
	/* Region 7 - D2H Z1_0 region (0x40000000 ~ 0x7FFFFFFF) */
	.word	D2H_Z1_0_BASE												/* base address   */
	.word	0x0 | (Region_1G << 1) | Region_Enable						/* size & enable  */
	.word	0x0 | Execute_Never | (Full_Access << 8) | Normal_WB_WA		/* access control */

	/* Region 8 - D2H Z1_1 Z1_2 Z2 region (0x80000000 ~ 0xFFFFFFFF) */
	.word	D2H_Z1_1_BASE												/* base address   */
	.word	0x0 | (Region_2G << 1) | Region_Enable						/* size & enable  */
	.word	0x0 | Execute_Never | (Full_Access << 8) | Normal_WB_WA		/* access control */

#endif	/* (FALSE == HMB_DCACHE_ENABLE) */
#if (CPU_DCACHE_EN)
	/* Region 9 - DCache on DBuf #1(16K) */
	.word	DBUF_DCACHE_0_BASE											/* base address   */
	.word	0x0 | (Region_16K << 1) | Region_Enable						/* size & enable  */
	.word	0x0 | Execute_Never | (Full_Access << 8) | Normal_WB_WA		/* access control */

	/* Region 10 - DCache on DBuf #2(32K) */
	.word	DBUF_DCACHE_1_BASE											/* base address   */
	.word	0x0 | (Region_32K << 1) | Region_Enable						/* size & enable  */
	.word	0x0 | Execute_Never | (Full_Access << 8) | Normal_WB_WA		/* access control */

	/* Region 11 - DCache on FWLB */
	.word	FWLB_DCACHE_BASE											/* base address   */
	.word	0x0 | (Region_512K << 1) | Region_Enable					/* size & enable  */
	.word	0x0 | Execute_Never | (Full_Access << 8) | Normal_WB_WA		/* access control */

#endif  /* CPU_DCACHE_EN */
	/* Region 12 - PCIE prohibit address */
	.word	PCIE_PROHIBIT_ADDR_BASE										/* base address   */
	.word	0x0 | (Region_8K << 1) | Region_Enable						/* size & enable  */
	.word	0x0 | (No_Access << 8)										/* access control */

MPU_region_setting_end:

#elif (PS5017_EN || PS5021_EN) /* (PS5013_EN) */

MPU_region_setting:
	/* Region 0 - Background */
	.word	0x0															/* base address   */
	.word	0x0 | (Region_4G << 1) | Region_Enable						/* size & enable  */
	.word	0x0 | (Full_Access << 8) | Normal_Shared					/* access control */

	/* Region 1 - Code/Execute region */
	.word	0x0															/* base address   */
	.word	0x0 | (Region_512K << 1) | Region_Enable					/* size & enable  */
	.word	0x0 | (Full_Access << 8) | Normal_Shared					/* access control */

	/* Region 2 (system PD0, PD1, PIC, DZIP register - 4MB include reserved space) */
	.word	AXI2AHB_REG_BASE											/* base address   */
	.word	0x0 | (Region_128K << 1) | Region_Enable					/* size & enable  */
	.word	0x0 | Execute_Never | (Full_Access << 8) | Strongly_ordered	/* access control */

	/* Region 3 - HW IP register group 0 address from (0x00C00000 ~ 0x00CFFFFF), size: 1MB */
	//.word	HW_IP_REG_BASE												/* base address   */
	//.word	0x0 | (Region_1M << 1) | Region_Enable						/* size & enable  */
	//.word	0x0 | Execute_Never | (Full_Access << 8) | Device_Shared	/* access control */

	/* Region 4 - HW IP XZIP register address from (0x002F0000 ~ 0x002F0FFF), size: 4KB */
	//.word	HW_IP_XZIP_REG_BASE											/* base address   */
	//.word	0x0 | (Region_4K << 1) | Region_Enable						/* size & enable  */
	//.word	0x0 | Execute_Never | (Full_Access << 8) | Device_Shared	/* access control */

	/* Region 5 (Non access region - to monitor stack overflow) */
	.word	MONITOR_STACK_OVERFLOW_BASE									/* base address   */
	.word	0x0 | (Region_32B << 1) | Region_Enable						/* size & enable  */
	.word	0x0 | (No_Access << 8)										/* access control */

	/* Region 6 - Vector region - read only protect vector (0x00000000 ~ 0x0000003F) */
	.word	0x0															/* base address   */
	.word	0x0 | (Region_64B << 1) | Region_Enable						/* size & enable  */
	.word	0x0 | (Read_Only << 8) | Normal_Shared						/* access control */

#if (CPU_DCACHE_EN)
	/* Region 7-1 - DCache on DBuf#1(16K) */
	.word	DBUF_DCACHE_0_BASE											/* base address   */
	.word	0x0 | (Region_4K << 1) | Region_Enable						/* size & enable  */
	.word	0x0 | Execute_Never | (Full_Access << 8) | Normal_WB_WA		/* access control */

	/* Region 7-2 - DCache on DBuf #2(32K) */
	.word	DBUF_DCACHE_1_BASE											/* base address   */
	.word	0x0 | (Region_16K << 1) | Region_Enable						/* size & enable  */
	.word	0x0 | Execute_Never | (Full_Access << 8) | Normal_WB_WA		/* access control */

	/* Region 7-3 - DCache on DBuf #2(32K) */
	.word	DBUF_DCACHE_2_BASE											/* base address   */
	.word	0x0 | (Region_32K << 1) | Region_Enable						/* size & enable  */
	.word	0x0 | Execute_Never | (Full_Access << 8) | Normal_WB_WA		/* access control */

	/* Region 8 - DCache on FWLB */
	.word	FWLB_DCACHE_BASE											/* base address   */
	.word	0x0 | (Region_512K << 1) | Region_Enable					/* size & enable  */
	.word	0x0 | Execute_Never | (Full_Access << 8) | Normal_WB_WA		/* access control */

	/* Region 9 - DCACHE on RS_BPT_1(8KB) */
	.word	RS_BPT_BASE													/* base address   */
	.word	0x0 | (Region_8K << 1) | Region_Enable						/* size & enable  */
	.word	0x0 | Execute_Never | (Full_Access << 8) | Normal_WB_WA		/* access control */

	/* Region 10 - DCACHE on RS_BPT_2(1KB) */
	.word	RS_BPT_BASE1												/* base address   */
	.word	0x0 | (Region_1K << 1) | Region_Enable						/* size & enable  */
	.word	0x0 | Execute_Never | (Full_Access << 8) | Normal_WB_WA		/* access control */

#endif /* CPU_DCACHE_EN */

    /* Region 11 (SPI region - Strongly_ordered) */
	.word	SPI_RAM_BASE												/* base address   */
	.word	0x0 | (Region_2M << 1) | Region_Enable						/* size & enable  */
	.word	0x0 | (Full_Access << 8) | Strongly_ordered					/* access control */

    /*	Region 12 - PCIE prohibit address // E19 need ???? */
	//.word	PCIE_PROHIBIT_ADDR_BASE										/* base address   */
	//.word	0x0 | (Region_8K << 1) | Region_Enable						/* size & enable  */
	//.word	0x0 | (No_Access << 8)										/* access control */

#if PS5021_EN
	.word	COP0_OPTD_1_BASE											/* base address   */
	.word	0x0 | (Region_32K << 1) | Region_Enable						/* size & enable  */
	.word	0x0 | (Full_Access << 8) | Device_Shared					/* access control */

	.word	COP0_OPTD_2_BASE											/* base address   */
	.word	0x0 | (Region_16K << 1) | Region_Enable						/* size & enable  */
	.word	0x0 | (Full_Access << 8) | Device_Shared					/* access control */

	.word	COP0_OPTD_3_BASE											/* base address   */
	.word	0x0 | (Region_8K << 1) | Region_Enable						/* size & enable  */
	.word	0x0 | (Full_Access << 8) | Device_Shared					/* access control */
#endif /* PS5021_EN */


MPU_region_setting_end:

#endif /* (PS5013_EN) */
//=============================================================================

	.eabi_attribute 25, 1
	.section	VECTORS, "ax"		// Name this block of code

//=============================================================================
// Entry point for the Reset handler
//=============================================================================

	.global  Start
Start:

//=============================================================================
// Exception Vector Table
//=============================================================================
// Note: LDR PC instructions are used here, though branch (B) instructions
// could also be used, unless the exception handlers are >32MB away.

Vectors:
	B		Reset_Handler
	B		Exception_Handler
	B		Exception_Handler
	B		Prefetch_Handler
	B		Abort_Handler
	B		.								// Reserved vector
	B		IRQ_Handler
	// FIQ Handle
	SUB		lr, lr, #4						// construct the return address
	SRSFD	sp!, #(ARM_SVC_MODE)			// store return state onto a stack.
											// Use SRS to save LR_irq and SPSR_irq on to the SVC mode stack
	CPS		#(ARM_SVC_MODE)					// change to SVC mode
	PUSH	{r0-r3, r12}					// store other AAPCS registers
	MOV		r2, #ARM_FIQ					// record FIQ entry
	B		ISR_Handler

	.global		isr_entry
	.global		CPUErrorHandler
//=============================================================================
// Exception Handlers
//=============================================================================

Abort_Handler:
	SUB		lr, lr, #4					// construct the return address, data abort need to minus 8
Prefetch_Handler:
	SUB		lr, lr, #4					// construct the return address, prefetch abort need to minus 4
Exception_Handler:
	SRSFD	sp!, #(ARM_SVC_MODE)		// store return state onto a stack. (record lr and spsr)
	MRS		lr, cpsr					// Move cpsr to lr (use lr to record mode)
	AND		lr, lr, #(ARM_MODE_MASK)	// get the Processor Mode
	SRSFD	sp!, #(ARM_SVC_MODE)		// store the Processor Mode and dummy spsr onto a stack
	CPSID	if, #(ARM_SVC_MODE)			// disable IRQ and FIQ, and enter SVC mode
	PUSH	{r0-r4, r12, lr}
	LDR		r0, [sp, #7*4]				// load processor mode to r0
	LDR		r1, [sp, #9*4]				// load lr to r1
	MRC		p15, 0, r2, c6, c0, 0       // Read DFAR to r2
	MRC		p15, 0, r3, c6, c0, 2       // Read IFAR to r3
	MOV		r12, #(ARM_DEBUG_INFO_ERROR_TYPE_ADDRESS & 0xFFFF)	// get the lower 2Byte of gDebugInfoBTCM.ulErrorType
	MOVT	r12, #(ARM_DEBUG_INFO_ERROR_TYPE_ADDRESS >> 16) 	// get the higher 2Byte of gDebugInfoBTCM.ulErrorType
	STMIA	r12, {r0-r3}
	MOV		r12, sp						//
	AND		r4, r12, #4					//
	SUB		sp, sp, r4					// remove any misalignment (0 or 4)
	BL		CPUErrorHandler
	ADD		sp, sp, r4
	POP		{r0-r4, r12, lr}
	ADD		sp, sp, #8					// bypass Processor Mode and dummy spsr
	RFEFD	sp!

IRQ_Handler:
	SUB		lr, lr, #4					// construct the return address
	SRSFD	sp!, #(ARM_SVC_MODE)		// store return state onto a stack.
										// Use SRS to save LR_irq and SPSR_irq on to the SVC mode stack
	CPS		#(ARM_SVC_MODE)				// change to SVC mode
	PUSH	{r0-r3, r12}				// store other AAPCS registers
	MOV		r2, #ARM_IRQ				// record IRQ entry
ISR_Handler:
	MOV		r0, sp						// get sp
	LDR		r1, [sp, #0x14]				// get real lr
	AND		r3, r0, #4					//
	//AND		r1, sp, #4				// test alignment of the stack
	SUB		sp, sp, r3					// remove any misalignment (0 or 4)
	PUSH	{r3, lr}					// store the adjustment and lr_SVC
	BL		isr_entry					// use r0 to do ISR
	POP		{r3, lr}					// restore stack adjustment and lr_SVC
	ADD		sp, sp, r3
	POP		{r0-r3, r12}				// restore registers
	RFEFD	sp!							// return from exception

//=============================================================================
// Reset Handler
//=============================================================================

.type Reset_Handler, "function"
Reset_Handler:
	MOV		r0, #0
	MOV		r1, #0
	MOV		r2, #0
	MOV		r3, #0
	MOV		r4, #0
	MOV		r5, #0
	MOV		r6, #0
	MOV		r7, #0
	MOV		r8, #0
	MOV		r9, #0
	MOV		r10, #0
	MOV		r11, #0
	MOV		r12, #0
	MOV		r14, #0
	CPS		#0x1f	//switch to system mode
	MOV		r14, #0
	CPS		#0x12	//switch to IRQ mode
	MOV		r14, #0
	CPS		#0x11	//switch to FIQ mode
	MOV		r8, #0
	MOV		r9, #0
	MOV		r10, #0
	MOV		r11, #0
	MOV		r12, #0
	MOV		r14, #0
	CPS		#0x13	//back to Supervisor mode
//=============================================================================
// Disable MPU and caches
//=============================================================================

// Disable MPU and cache in case it was left enabled from an earlier run
// This does not need to be done from a cold reset

	MRC		p15, 0, r0, c1, c0, 0		// Read CP15 Control Register
	BIC		r0, r0, #(MPU_EN | Data_Cache_EN)		// Disable MPU (M bit) and data cache (C bit)
	BIC		r0, r0, #(Instruction_Cache_EN)			// Disable instruction cache (I bit)
	DSB									// Ensure all previous loads/stores have completed
	MCR		p15, 0, r0, c1, c0, 0		// Write CP15 Control Register
	ISB									// Ensure subsequent insts execute wrt new MPU settings

//=============================================================================
// Disable Branch prediction
//=============================================================================

// In the Cortex-R5, the Z-bit of the SCTLR does not control the program flow prediction.
// Some control bits in the ACTLR control the program flow and prefetch features instead.
// These are enabled by default, but are shown here for completeness.

	MRC		p15, 0, r0, c1, c0, 1		// Read ACTLR
	ORR		r0, r0, #(BP_Bit1 | RSDIS)	// Enable RSDIS bit 17 to disable the return stack and Clear BP bit 15 and set BP bit 16
	ORR		r0, r0, #(DILS)			// Enable and set DILS bit 6 (Dsiable CPU auto retry when AXI access be interrupt by CPU INT)
	BIC		r0, r0, #(BP_Bit0)		// Branch always not taken and history table updates disabled
	MCR		p15, 0, r0, c1, c0, 1		// Write ACTLR

//=============================================================================
// Initialize Supervisor Mode Stack using Linker symbol from scatter file.
// Stacks must be 8 byte aligned.
//=============================================================================

	.global   Image$$ARM_LIB_STACK$$ZI$$Limit
	LDR SP, = Image$$ARM_LIB_STACK$$ZI$$Limit

//=============================================================================
// Cache invalidation
//=============================================================================

	DSB									// Complete all outstanding explicit memory operations

	MOV		r0, #0

	MCR		p15, 0, r0, c7, c5, 0		// Invalidate entire instruction cache
	MCR		p15, 0, r0, c15, c5, 0		// Invalidate entire data cache


//=============================================================================
// TCM Configuration
//=============================================================================

// Cortex-R5 provides two Tightly-Coupled Memory (TCM) blocks (ATCM and BTCM) for fast access to code or data.
// ATCM typically holds interrupt or exception code that must be accessed at high speed,
//    without any potential delay resulting from a cache miss.
// BTCM typically holds a block of data for intensive processing, such as audio or video data.
// In the Cortex-R5 processor, both ATCM and BTCM support both instruction and data accesses.

// The following illustrates basic TCM configuration, as the basis for exploration by the user

.ifdef TCM
	.global Image$$ATCM$$Base
	.global Image$$BTCM$$Base

	MRC		p15, 0, r0, c0, c0, 2		// Read TCM Type Register
	// r0 now contains ATCM & BTCM availability

	MRC		p15, 0, r0, c9, c1, 1		// Read ATCM Region Register
	// r0 now contains ATCM size in bits [6:2]

	MRC		p15, 0, r0, c9, c1, 0		// Read BTCM Region Register
	// r0 now contains BTCM size in bits [6:2]

// The Cortex-R5 Logic Tile on Versatile Express has
// 64K ATCM from 0x40000000 to 0x4000FFFF
// 64K BTCM from 0xE0FE0000 to 0xE0FEFFFF

	LDR		r0, =Image$$ATCM$$Base		// Set ATCM base address
	ORR		r0, r0, #1					// Enable it
	MCR		p15, 0, r0, c9, c1, 1		// Write ATCM Region Register

 //==== comment by mitch
 //       LDR     r0, =||Image$$BTCM$$Base||  // Set BTCM base address
 //       ORR     r0, r0, #1                  // Enable it
 //       MCR     p15, 0, r0, c9, c1, 0       // Write BTCM Region Register

.endif


//=============================================================================
// MPU Configuration
//=============================================================================

// Notes:
// * Regions apply to both instruction and data accesses.
// * Each region base address must be a multiple of its size
// * Any address range not covered by an enabled region will abort
// * The region at 0x0 over the Vector table is needed to support semihosting

	// Import linker symbols to get region base addresses
	LDR		r3, =MPU_region_setting
	LDR		r4, =MPU_region_setting_end
	MOV		r1, #0

MPU_loop:
	MCR		p15, 0, r1, c6, c2, 0		// Set memory region number register
	LDMIA	r3!, {r5, r6, r7}
	MCR		p15, 0, r5, c6, c1, 0		// Set region base address register
	MCR		p15, 0, r6, c6, c1, 2		// Set region size & enable register
	MCR		p15, 0, r7, c6, c1, 4		// Set region access control register
	ADD		r1, r1, #1
	CMP		r4, r3						// Compare Region Number
	BHI		MPU_loop
               
	// Disable all higher priority regions (assumes unified regions, which is always true for Cortex-R5)
	MRC		p15, 0, r0, c0, c0, 4		// Read MPU Type register (MPUIR)
	LSR		r0, r0, #8
	AND		r0, r0, #0xff				// r0 = DRegion (0, 12, or 16 for Cortex-R5)
	MOV		r2, #0						// Value to write to disable region
region_loop:
	CMP		r0, r1
	BLS		regions_done
	MCR		p15, 0, r1, c6, c2, 0		// Set memory region number register (RGNR)
	MCR		p15, 0, r2, c6, c1, 2		// Set region size & enable register (DRSR)
	ADD		r1, r1, #1
	B		region_loop

regions_done:
	ISB									// Ensure subsequent insts execute wrt region 0 settings

//=============================================================================
// Enable Branch prediction
//=============================================================================

// In the Cortex-R5, the Z-bit of the SCTLR does not control the program flow prediction.
// Some control bits in the ACTLR control the program flow and prefetch features instead.
// These are enabled by default, but are shown here for completeness.

	MRC		p15, 0, r0, c1, c0, 1		// Read ACTLR
	BIC		r0, r0, #(BP_Bit0 | BP_Bit1 | RSDIS)	// Clear RSDIS bit 17 to enable return stack
													// Clear BP bit 15 and BP bit 16: Normal operation, BP is taken from global history table.
	MCR		p15, 0, r0, c1, c0, 1		// Write ACTLR

//=============================================================================
// Enable MPU and branch to C library init
// Leaving the caches disabled until after scatter loading.
//=============================================================================

	MRC		p15, 0, r0, c1, c0, 0		// Read CP15 Control Register
	ORR		r0, r0, #0x01				// Set M bit to enable MPU
	DSB									// Ensure all previous loads/stores have completed
	MCR		p15, 0, r0, c1, c0, 0		// Write CP15 Control Register
	ISB									// Ensure subsequent insts execute wrt new MPU settings

	.global __main
	B		__main						// clib init, scatter loader
Reset_Handler_End:
.size Reset_Handler, (Reset_Handler_End - Reset_Handler)

	.end
