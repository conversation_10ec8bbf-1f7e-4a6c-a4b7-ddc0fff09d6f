/****************************************************************************
*
*  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
*  All rights reserved
*
*  The content of this document is confidential and shall be applied
*  subject to the terms and conditions of the license agreement and
*  other applicable laws. Any unauthorized access, use or disclosure
*  of this document is strictly prohibited and may be punishable
*  under laws.
*
*  wl.h
*
*
*
****************************************************************************/

#ifndef _FTL_WL_H_
#define _FTL_WL_H_

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "typedef.h"
#include "aom/aom_api.h"
#include "wl/wl_api.h"

/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */
#define WL_FREE_POOL_NUM_GET_THIRD_MAX_EC			(6)
#define WL_FREE_POOL_NUM_GET_SECOND_MAX_EC			(4)
#define WL_COPY_UNIT_SRC_NUM						(1)

/*
 * ---------------------------------------------------------------------------------------------------
 *  data types
 * ---------------------------------------------------------------------------------------------------
 */
typedef struct {
	U8 ubGCGRUnitIsErased;
	U32 ulDoneBitMap;
	U8 ubTieInCnt;
	U8 ubTieInChkCnt;
	U32 ulProgramPlane;
	U32 ulReadPlane;
	U32 ulProgramDonePlaneCnt;
	CopyDataStateEnum_t State;
} CpyUnitCopyData_t;

#endif// _FTL_WL_H_
