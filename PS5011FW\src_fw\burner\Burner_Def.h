#ifndef _BURNERCONFIG_DEF_H_
#define _BURNERCONFIG_DEF_H_

#include "common/mem.h"
#include "setup.h"
#include "typedef.h"

//==========================================================
//  Burner Version
//==========================================================
#define BURNER_CODE_MAIN_VERSION          ('1')
#define BURNER_CODE_SUB_VERSION           ('0')

//==========================================================
//  BurnerCode ON/OFF Config.
//==========================================================
#define BURNER_DEBUG_CODE   (0)		// Compile the code for verification only
#define BURNER_CE_REMAP_EN	(1)		// Enable ce remap and will change the ce mapping rule
#define BURNER_UFS_ONLY		(0)		// Enable UFS mode (Only Burner use this)

//==========================================================
//  BurnerCode Verify Flow Definition
//==========================================================
#define DEFUALT_ERASE_ALL_BLK_NUM	(32)   //At verification, don't erase all block to save time

//===================================================
//	FW Code Encrypt Type
//===================================================
#define DE_SCRAMBLE_OFFSET			(0x01)
#define DE_SCRAMBLE_MASK			(0x02)
#define DE_SCRAMBLE_EN				(BIT0)

#define AES_KEY_NUM					(3)
#define DE_KEYNUM_MASK				(0x03)
#define DE_KEYNUM_OFFSET			(0x00)
#define DE_TYPE_AESKEY1				(0)
#define DE_TYPE_AESKEY2				(1)
#define DE_TYPE_AESKEY3				(2)
#define DE_TYPE_XOR					(3)


#define DLMC_SCRAMBLE_EN (BIT1)
#define DLMC_KEYNUM_MASK				(0x03)
#define DLMC_KEYNUM_OFFSET			(0x03)


//==========================================================
//  BurnerCode Parameter Config.
//==========================================================
#define CE_PERCH_OFFSETNUM		(4)
/*	CH0	| CE0 CE4 CE8 CE12
 *	CH1	| CE1 CE5 CE9 CE13
 *	CH2	| CE2 CE6 CE10 CE14
 *	CH3	| CE3 CE7 CE11 CE15
 */
#define CE_REMAP_SHIFTBIT		(3)
/*	CH0	| CE0 CE1 CE2 CE3 X X X X
 *	CH1	| CE8 CE9 CE10 CE11 X X X X
 *	CH2	| CE16 CE17 CE18 CE19 X X X X
 *	CH3	| CE24 CE25 CE26 CE27 X X X X
 */

#define DEFUALT_SCAN_BLK_NUM	(16)   //when scan block, we cover the block from 0 ~ MAX_SCAN_BLK_NUM
#define CODE_BLK_PER_CH			(2)    //for one vision, there are 2 code block per channel
#define MAX_FW_SLOT     		(15)	 //How many FW slot support for One ID Page Record

#define MARK_IDPAGE		'I'
#define MARK_CODEPTR	'P'
#define MARK_CODEBLK	'C'
#define MARK_CODESIGN	'H'
#define MARK_SIGNATURE	'S'

//==========================================================
// SIMREG Bit usage Define
//==========================================================
//SYS0B_CR_SIM_CTRL0 Default 0x00
#define SIMREG_NOT_INIT_SATAPHY		(BIT7)  //0: Need to Init SATA PHY parameter, 			     1: No need to Init SATA PHY Parameter
#define SIMREG_NOT_INIT_PCIE		(BIT6)	//0: Need to wait PRESET and init host interface(AHCI/NVMe), 1: No need to do any init of PCIe
#define SIMREG_NOT_INIT_HOST		(BIT5)	//0: Init NVMe/SATA flow, 		     			      1: Not do NVMe/SATA flow
#define SIM_REG_SEND_CPL            (BIT4)  //0: jump from uart , didint need to send CPL(NVMe) 1: jumped from other place, need to send CPL (NVMe)
#define SIMREG_SERIAL_NUM_STR		(BIT3)	//0: Gernerate new Serial Number String, 		     1: Keep current Serial Number String
#define SIMREG_VENDER_FORCE_BOOT	(BIT2)  //0: Normal Load 							     1: vendor command force boot code mode
#define SIMREG_I_BIT				(BIT1)	//0: D2H without I-Bit,  					     1: D2H with I-Bit 
#define SIMREG_NOT_SEND_D2H			(BIT0)	//0: Send D2H, 							     1: not send D2H FIS (SATA)

//SYS0B_CR_SIM_CTRL1 Default 0x00
#define SIMREG_DEVSLP_LOAD_CODE 	(BIT0)  //0: Normal Flow, 1: Device sleep Flow
#define SIMREG_DEVSLP_LOAD_FAIL		(BIT1)  //0: Nothing 	     1: use 8K data to load code directly fail 	
#define SIMREG_DLMC_PREFORMAT		(BIT2)  //0: Nothing      1: DLMC with Preformat, use SYS0B_CR_SIM_CTRL2 for ubEraseMode
//SYS0B_CR_SIM_CTRL2  Default 0xFF  Command Tag save

#if (PS5017_EN)
//SYS0B_CR_SIM_CTRL4 Default 0x00, WDT reset SYS0B_CR_SIM_CTRL4 ~ 8 but LPM3 not
#define WDT_RESET_FLAG				(BIT0)	// 1: not WDT reset, 0: WDT reset/normal poweron/VDT
#define TRIM_VALUE_FLAG				(BIT1)
#endif /* (PS5017_EN) */


//==========================================================
// GPIO Bit usage Define
//==========================================================
#define IPL_GP_LOAD_CODE_MODE			BIT0			// 1: Load FW Code (Default)			        0: Force in Boot Code								


// Fast Page Rule
#define  PAGE_4A            (0x4A)
#define  PAGE_21            (0x21)
#define  PAGE_31            (0x31)
#define  PAGE_43            (0x43)
#define  PAGE_2A            (0x2A)
#define  PAGE_34            (0x34)
#define  PAGE_45			(0x45)
#define  PAGE_70            (0x70)


//==========================================================
//  Memory Allocation
//==========================================================

//DBUF  (0x22000000 1.75MB)
#define BURNER_BUF_BASE			(DBUF_PB_RAM_ADDRESS)
#define BURNER_BUF_SIZE			(DBUF_PB_RAM_SIZE)

//512B global + CodeSign Header 512B + Reserved 1KB + ID Page 4KB +
//Program BUF 32KB + Rad BUF 32KB + (Decrypted FW BIN & XMODEM_BUF)  (512 + 2)KB




// ----------------------------------------------------------
// DBUF Memory physical layout
// ----------------------------------------------------------
#define DBUF_BASE                   (DBUF_PB_RAM_ADDRESS)//DBUF_PB_RAM_ADDRESS
#define DBUF_LEN                    (0x002D0000)//DBUF_PB_RAM_SIZE  // FPGA 1M, ASIC 2880K.


//==========================================================
//  enum Define
//==========================================================
enum {	DEF_SLC_MODE = 0,
	DEF_MLC_MODE = 1,
	DEF_TLC_MODE = 2,
	DEF_QLC_MODE = 3
};

enum {
	DEF_LEGACY_INTERFACE  = 0, //Legacy Single End
	DEF_TOGGLE_INTERFACE  = 1, //Toggle Single End
};
enum {HOST_NVME = 0, HOST_SATA = 1};

#define MAX_SCAN_CODEBLK_NUM	(16)
#define MAX_PROGRAM_CODEBLK_NUM	(MAX_SCAN_CODEBLK_NUM + 1)
#define MAXCODEBLK_PER_CH		(2)

#endif
