#ifndef _BUF_API_H_
#define _BUF_API_H_

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */

#include "aom/aom_api.h"

/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */

#define BUF_ST3C_FREE_PB_RETRY_CNT_THRESHOLD		(0xFFFF)

#define BUF_GC_LOW_LIMIT_BITMAP				(BIT(LB_ID_WRITE_0) | BIT(LB_ID_FW) | BIT(LB_ID_PROGRAM_1))
#define BUF_SPOR_LOW_LIMIT_BITMAP			(BIT(LB_ID_WRITE_0) | BIT(LB_ID_READ) | BIT(LB_ID_FW) | BIT(LB_ID_NRW) | BIT(LB_ID_PROGRAM_0))
#define BUF_NRW_COMPARE_LOW_LIMIT_BITMAP	(BIT(LB_ID_READ) | BIT(LB_ID_NRW))
#define BUF_DLMC_LIMIT_BITMAP				(BIT(LB_ID_READ) | BIT(LB_ID_FW))
#define BUF_NRW_SYNC_READ_LOW_LIMIT_BITMAP	(BIT(LB_ID_READ) | BIT(LB_ID_NRW))
#define BUF_TRIM_LOW_LIMIT_BITMAP			(BIT(LB_ID_READ) | BIT(LB_ID_NRW))
#define BUF_TCG_LOW_LIMIT_BITMAP			(BIT(LB_ID_READ) | BIT(LB_ID_FW))
#define BUF_NVME_ATA_SECUIRTY_LOW_LIMIT_BITMAP (BIT(LB_ID_READ) | BIT(LB_ID_FW))

/*
 * ---------------------------------------------------------------------------------------------------
 *  enum
 * ---------------------------------------------------------------------------------------------------
 */

typedef enum bufUsingStateEnum {
	BUF_USING_STATE_MICRON_VUC,
	BUF_USING_STATE_OTHER, // Save InitInfo, GC, WL, CopyUnit
	BUF_USING_STATE_WORDLINE_FOLDING,
	BUF_USING_STATE_TABLE_UPDATE,
	BUF_USING_STATE_TABLE_GC,
	BUF_USING_STATE_NO_ONE_USING,
} BufUsingStateEnum_t;

typedef enum bufSourceEnum {
	BUF_SOURCE_COPY_BUF,
	BUF_SOURCE_DMAC_TEMP,
	BUF_SOURCE_NUM
} BufSourceEnum_t;

typedef enum bufAllocateModeEnum {
	BUF_ALLOCATE_MODE_COPY_BUFFER,
	BUF_ALLOCATE_MODE_DMAC_TEMP,
} BufAllocateModeEnum_t;

typedef enum bufEnrollModeEnum {
	BUF_ENROLL_MODE_ACTIVE,
	BUF_ENROLL_MODE_WAIT,
	BUF_ENROLL_MODE_CLEAR,
} BufEnrollModeEnum_t;

/*
 * ---------------------------------------------------------------------------------------------------
 *  data types
 * ---------------------------------------------------------------------------------------------------
 */

typedef struct BufLBLowLimit {
	U16 uwWrite0;
	U16 uwRead;
	U16 uwFW;
	U16 uwNRW;
	U16 uwProgram1;
	U16 uwProgram0;
} BufLBLowLimit_t, *BufLBLowLimitPtr_t;

typedef struct BMUInitST3C {
	U16 uwInitST3CCacheTotal;  // The number of PB which ST3C can used. Total = In FWLB + In PB
	U16 uwInitST3CCacheInFWLB; // The number of PB which ST3C share with FW_LB from total PB.
	U16 uwInitST3CCacheInPB;   // The number of PB which ST3C only.
} BufInitST3C_t;

/*
* ---------------------------------------------------------------------------------------------------
*  macros
* ---------------------------------------------------------------------------------------------------
*/

#define M_BUF_CHECK_BUF_STATE(STATE, BUF)	((STATE) == gST3CRAMDist.BufState[BUF].BufUsingState)

/*
 * ---------------------------------------------------------------------------------------------------
 *   extern global variables
 * ---------------------------------------------------------------------------------------------------
 */

extern BufInitST3C_t gBufInitST3CCache;

/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */
AOM_INIT_2 void BufferInit(void);
void BufReturnPBToCOP1(U32 ulBitBufMode);
#if (RELEASED_FW)
AOM_TABLE_UPDATE U8 BufGetPBFromCOP1(U32 ulBitBufMode);
AOM_TABLE_UPDATE void BufReturnPBToCOP1(U32 ulBitBufMode);
#else /* (RELEASED_FW) */
AOM_BUF U8 BufGetPBFromCOP1(U32 ulBitBufMode);
AOM_BUF void BufReturnPBToCOP1(U32 ulBitBufMode);
#endif /* (RELEASED_FW) */
U8 BufCheckPrioirty(BufUsingStateEnum_t BufUsingState, BufSourceEnum_t BufSource);
U8 BufferAllocateBuf(BufAllocateModeEnum_t BufAllocateMode, BufUsingStateEnum_t BufUsingState);
AOM_TABLE_UPDATE void BufferFreeBuf_AOM_TABLE_UPDATE(BufAllocateModeEnum_t BufAllocateMode, BufUsingStateEnum_t BufUsingState);
AOM_GC void BufferFreeBuf_AOM_GC(BufAllocateModeEnum_t BufAllocateMode, BufUsingStateEnum_t BufUsingState);
AOM_WL void BufferFreeBuf_AOM_WL(BufAllocateModeEnum_t BufAllocateMode, BufUsingStateEnum_t BufUsingState);
AOM_WL void BufFreeCopyUnitBuf(void);
AOM_WORDLINE_FOLDING void BufferFreeBuf_AOM_WORDLINE_FOLDING(BufAllocateModeEnum_t BufAllocateMode, BufUsingStateEnum_t BufUsingState);
AOM_ERROR_HANDLE2 U8 BufGetCopyUnitBuf(void);
AOM_GC void BufInvalidDcacheForGCBuf(void);
AOM_BUF void BufCalculateLowLimit(BufLBLowLimitPtr_t pLBLowLimit, U8 ubNeedCalculateBitMap);
AOM_SPOR_2 U16 BufSPORGetBuf(U16 uwSize);
AOM_RS_2 void BufAllocateRaidECCFWLBPBLinkWithSize (U16 uwLBOffset, U8 ubSize);
AOM_COMMON U8 BufCalculateWLBUpperLimit();
#endif /* _BUF_API_H_ */
