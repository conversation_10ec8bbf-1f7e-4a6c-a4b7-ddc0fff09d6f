#ifndef _VRLC_H_
#define _VRLC_H_

#include "symbol.h"

#define VRLC_START_X_COORD                              ((IM_B47R || IM_B37R) ? 3 : 4)

#define VRLC_USTP1										(1)
#define VRLC_GEN_CRC_FAIL_SEED_INIT						(0xFFFF)
#define VRLC_Q24_8_INTEGER_SHIFT                        (8)
#define VRLC_TLC_ERR_GRAD_SCALAR	                	(2) // follow Nick2W2-5's depiction
#define VRLC_QLC_ERR_GRAD_SCALAR						(4) // follow Nick2W2-5's depiction
#define VRLC_ERR_GRAD_SCALAR							((IM_B47R || IM_B37R) ? VRLC_TLC_ERR_GRAD_SCALAR : VRLC_QLC_ERR_GRAD_SCALAR) // follow Nick2W2-5's depiction
#define VRLC_MAX_MOVE_LOOP_DYN_SCALAR_REFCAL            ((IM_B47R || IM_B37R) ? 3 : 2)
#define VRLC_MAX_MOVE_LOOP_DYN_SCALAR_MEASURE           10 / 17		// MaxMove /1.7 = MaxMove * 10 / 17
#define VRLC_AGC_FILTER_UPPER_LIMIT                     (((IM_B47R || IM_B37R) ? 65 : 85) << VRLC_Q24_8_INTEGER_SHIFT) // DiffEC is distance, Page Based
#define VRLC_AGC_FILTER_LOWER_LIMIT	                    (((IM_B47R || IM_B37R) ? 25 : 45) << VRLC_Q24_8_INTEGER_SHIFT) // Page Based
#define VRLC_AGC_OFFSET_SELECT_MAX	                    (((IM_B47R || IM_B37R) ? 30 : 20))
#define VRLC_AGC_OFFSET_SELECT_MIN	                    (((IM_B47R || IM_B37R) ? 10 :  3))
#define VRLC_TRIM_L1_LEFT_OFFSET_SELECT	                (10)
#define VRLC_TRIM_L1_HALF_BATHTUB_SHIFT	                (25) // Page Based
#define VRLC_TRIM_L1_K_DIVISOR_DEFAULT                  (0xFF)
#define VRLC_TWO_SIDE_UECC_REDO_TRIM_LOOP_MAX			(1)
#define VRLC_AGC_WL_PARAMETER_K_ODD                    	(4)
#define VRLC_AGC_WL_PARAMETER_K_EVEN					(4)
#define VRLC_ADD_UNIFIED_EVENT_LOG_TRIM_MOVE_THRESHOLD  (3)

#define VRLC_MM_TRIM_IDX_PRE_MT_INFO_TRIM_REG_SHIFT	    (16)
#define VRLC_MM_DIE_PRE_MT_INFO_TRIM_REG_SHIFT	    	(14)
#define VRLC_MM_TRIM_IDX_PRE_MT_INFO_MASK				(BIT_MASK(VRLC_MM_DIE_PRE_MT_INFO_TRIM_REG_SHIFT))
#define VRLC_MM_TRIM_IDX_PRE_MT_INFO_INVALID			(0xFFFFFFFF)
#define VRLC_MM_DIE_PRE_MT_INFO_MASK					(BIT_MASK(VRLC_MM_TRIM_IDX_PRE_MT_INFO_TRIM_REG_SHIFT - VRLC_MM_DIE_PRE_MT_INFO_TRIM_REG_SHIFT))

//#define VRLC_DUMMY_FSA									(0xBEEFBEEF)

typedef enum VRLCInitTrimRegModeEnum {
	VRLC_INIT,
	TEMPCO_INIT,
} VRLCInitTrimRegModeEnum_t;

typedef enum VRLCInitWaitMTModeEnum {
	RETRY_VRLC_INIT_WAIT_MT_RESOURCE,
	RETRY_VRLC_INIT_WAIT_ALL_DONE,
} VRLCInitWaitMTModeEnum_t;

typedef enum VRLCInitSetUSTPModeEnum {
	VRLC_INIT_SET_USTP,
	VRLC_INIT_CHECK_USTP
} VRLCInitSetUSTPModeEnum;
#endif /* _VRLC_H_ */
