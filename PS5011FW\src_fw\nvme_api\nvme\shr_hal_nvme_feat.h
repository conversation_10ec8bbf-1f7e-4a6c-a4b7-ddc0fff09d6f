/**************************************************************************/
/*                                                                        */
/*           Copyright (c) 2000-2015 by Phison Electronics Corp.          */
/*                                                                        */
/**************************************************************************/

/**************************************************************************/
/*                                                                        */
/*  APPLICATION INTERFACE DEFINITION                       RELEASE        */
/*                                                                        */
/*    shr_hal_nvme_feat.h                                        GNU C         */
/*                                                           X.X          */
/*  AUTHOR                                                                */
/*                                                                        */
/*                                                                        */
/*  DESCRIPTION                                                           */
/*                                                                        */
/*    This file defines the basic Application Interface (API) to the      */
/*    NVME library.                                                       */
/*                                                                        */
/*  RELEASE HISTORY                                                       */
/*                                                                        */
/*    DATE              NAME                      DESCRIPTION             */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#ifndef _SHR_NVME_FEAT_LIB_H_
#define _SHR_NVME_FEAT_LIB_H_


#include "nvme_api/nvme/shr_hal_nvme_log.h"
#include "nvme_api/d2h/shr_hal_d2h.h"
#include "hal/security/security_api.h"

//#ifdef EXTERN
//#undef EXTERN
//#endif

//#ifdef _SHR_HAL_NVME_FEAT_API_C_
//#define EXTERN
//#else
//#define EXTERN  extern
//#endif


//--------------------------------------------feature start--------------------------------
// Define parameter for oncs status


// If set to 1, all the features are savable and changeable
//#define SAVE_SEL_ALL                (0)

#define EN_ARBITRATION_SAVEABLE                  (0)
#define EN_ARBITRATION_CHANGEABLE                (1)

#define EN_POWER_MANAGEMENT_SAVEABLE             (0)
#define EN_POWER_MANAGEMENT_CHANGEABLE           (1)

#define EN_LBA_RANGE_TYPE_SAVEABLE               (0)
#define EN_LBA_RANGE_TYPE_CHANGEABLE             (0)

#define EN_TEMPERATURE_THRESHOLD_SAVEABLE        (1)
#define EN_TEMPERATURE_THRESHOLD_CHANGEABLE      (1)

#define EN_ERROR_RECOVERY_SAVEABLE               (1)
#define EN_ERROR_RECOVERY_CHANGEABLE             (1)

#define EN_VOLATILE_WRITE_CACHE_SAVEABLE         (0)
#define EN_VOLATILE_WRITE_CACHE_CHANGEABLE       (1)

#define EN_NUMBER_OF_QUEUE_SAVEABLE              (0)
#define EN_NUMBER_OF_QUEUE_CHANGEABLE            (1)

#define EN_INTERRUPT_COALESCING_SAVEABLE         (0)
#define EN_INTERRUPT_COALESCING_CHANGEABLE       (1)

#define EN_INTERRUPT_VECTOR_CONFIG_SAVEABLE      (0)
#define EN_INTERRUPT_VECTOR_CONFIG_CHANGEABLE    (1)

#define EN_WRITE_ATOMICITY_SAVEABLE              (1)
#define EN_WRITE_ATOMICITY_CHANGEABLE            (1)

#define EN_ASYNC_EVENT_CONFIG_SAVEABLE           (1)
#define EN_ASYNC_EVENT_CONFIG_CHANGEABLE         (1)

#define EN_AUTO_POWER_TRANSITION_SAVEABLE        (0)
#define EN_AUTO_POWER_TRANSITION_CHANGEABLE      (1)
#define EN_HOST_MEM_BUF_SAVEABLE        (0)
#define EN_HOST_MEM_BUF_CHANGEABLE      (1)

#define NVME_SET_POWERSTATE_DELAY 				(2000000) //2s

#if SUPPORT_TIMESTAME_EN
#define EN_TIME_STAMP_SAVEABLE                   (0)
#define EN_TIME_STAMP_CHANGEABLE                 (1)
#endif


#if SUPPORT_KEEP_ALIVE_EN
#define EN_KEEP_ALIVE_TIMER_SAVEABLE                   (0)
#define EN_KEEP_ALIVE_TIMER_CHANGEABLE                 (1)
#endif

#if SUPPORT_THERMAL_MANAGEMENT_EN
#define EN_CONTROLLED_THERMAL_MANAGEMENT_SAVEABLE    (0)
#define EN_CONTROLLED_THERMAL_MANAGEMENT_CHANGEABLE  (1)
#endif

#define EN_NOP_POWER_STATE_CONFIG_SAVEABLE                   (0)
#define EN_NOP_POWER_STATE_CONFIG_CHANGEABLE                 (1)

#define EN_SW_PROGRESS_MARKER_SAVEABLE                          (1)
#define EN_SW_PROGRESS_MARKER_CHANGEABLE                    (1)

#define EN_HOST_IDENTIFIER_SAVEABLE                                 (0)
#define EN_HOST_IDENTIFIER_CHANGEABLE                            (0)

#define EN_RESERVATION_NOTN_MASK_SAVEABLE                   (0)
#define EN_RESERVATION_NOTN_MASK_CHANGEABLE                 (0)

#define EN_RESERVATION_PERSISTANCE_SAVEABLE                   (0)
#define EN_RESERVATION_PERSISTANCE_CHANGEABLE                 (0)

#define EN_DELL_SCP_SAVEABLE                   (1)
#define EN_DELL_SCP_CHANGEABLE                 (1)


#define MAX_NUM_FOR_DATA_STRUC  (64)  // total size of SHARED_DBUF_HOST_SIZE is 4k

// E8 supports max 1 LBA range in all NS
#define FEAT_MAX_LBA_NUM          (1)
//temperature threshold (TMPSEL)
#define NVME_TMPSEL_RESERVED_MAX         (0xF)
#define NVME_TMPSEL_MIN                  (0x0)

// E8 supports max 1 error recovery in all NS
#define FEAT_ERR_NUM          (1)

// E8 supports max 8 IO queues, 0-based
#define MAX_SQCQ_NUM        (MAX_QUEUE_NUMBER-2) //(-2: admin and 0 based)

// E8 supports max Interrupt 8 Vectors (0~8)
#define MAX_IV_NUM        (8+1)

// E8 supports max power states
#define MAX_PS_NUM        (5)

//E11 non-operational PS3~PS4
#define NON_OP_START    (3)


#define MAX_IOQ_SUP             (65535 - 1)          // this value is 0's base'

//threshold type select(THSEL)
typedef enum {
	OVER,
	UNDER
} NVME_THT_SEL_ENUM;


typedef enum {
	RSVD,
	FILESYSTEM,
	RAID,
	CACHE,
	PW_FILE
} NVME_LBA_RANGE_ENUM;


#pragma pack(1)

typedef union nvme_feat_sup_cap        FEATURE_SUP_CAP_T, *FEATURE_SUP_CAP_PTR;
union nvme_feat_sup_cap {
	U32 ul32;

	struct {
		U32 btRsvd0       : 1;    // Reserved
		U32 btArb         : 1;    // Arbitration
		U32 btPM          : 1;    // Power Management
		U32 btLRT         : 1;    // LBA Range Type
		U32 btTmpTh       : 1;    // Temperature Threshold
		U32 btER          : 1;    // Error Recovery
		U32 btVWC         : 1;    // Volatile Write Cache
		U32 btNQ          : 1;    // Number of Queues
		U32 btIC          : 1;    // Interrupt Coalescing
		U32 btIVC         : 1;    // Interrupt Vector Configuration
		U32 btWAN         : 1;    // Write Automicity Normal
		U32 btAEC         : 1;    // Asynchronous Event Configuration
		U32 btAPST        : 1;    // Autonomous Power State Transition
		U32 btHMB         : 1;    // Host Memory Buffer
		U32 btTimestamp : 1;    // Timestamp
		U32 btKAT       : 1;    //keep alive timer
		U32 btHCTM       : 1;  //controlled thermal management
		U32 btNOPSC      : 1; //non-operational power state config //0x11
		U32 ubRsvd1	    : 6;
		U32 btSPM         : 1;    // Software Progress Marker //0x80
		U32 btHId         : 1;    // Host Identifier //0x81
		U32 btResNoticeMask : 1;    //reservatrion notification mask //0x82
		U32 btResPersist  : 1;    // reservation persistance//0x83
		U32 btDELLSCP     : 1;      // DELL SCP //0xD8
		U32 btRsvd2       : 3;   // Reserved
	};
} ;


/**
 * @brief NVME Arbitration Feature structure.
 *
 * Refer to NVM_Express_1_2_Gold_20141209.pdf.
 */

typedef union nvme_feat_arb    FEATURE_ARB_T, *FEATURE_ARB_PTR;

union nvme_feat_arb {
	U32 ul32;

	struct {
		U32 btAB          : 3;    // Arbitration Burst
		U32 btRsvd        : 5;    // Reserved
		U32 btLPW         : 8;    // Low Priority Weight
		U32 btMPW        : 8;    // Middle Priority Weight
		U32 btHPW         : 8;    // High Priority Weight
	};
} ;




/**
 * @brief NVME Power Management Feature structure.
 *
 * Refer to NVM_Express_1_2_Gold_20141209.pdf.
 */
typedef union nvme_feat_pm    FEATURE_PM_T, *FEATURE_PM_PTR;

union nvme_feat_pm {
	U32 ul32;

	struct {
		U32 btPS          : 5;   // Power State
		U32 btWorkloadHint          : 3;   // Workload Hint
		U32 btRsvd        : 24;
	};
} ;
/**
 * @brief NVME LBA Range Type Feature structure.
 *
 * Refer to NVM_Express_1_2_Gold_20141209.pdf.
 */
typedef struct nvme_feat_lba_range    FEATURE_LBA_RANGE_T, *FEATURE_LBA_RANGE_PTR;

struct nvme_feat_lba_range {
	U8 ubType;
	U8 ubAttributes;
	U8 ubRsvd1[14];
	U64 uoStartLba;
	U64 uoNLB;
	U32 ulUniqueId[4];
	U32 ulRsvd2[4];
} ;

/**
 * @brief NVME Temperature Threshold Feature structure.
 *
 * Refer to NVM_Express_1_2_Gold_20141209.pdf.
 */
typedef union nvme_feat_tmpth    FEATURE_TMPTH_T, *FEATURE_TMPTH_PTR;

union nvme_feat_tmpth {
	U32 ul32;

	struct {
		U32 btTmpTh       : 16;   // Temperature Threshold
		U32 btTmpSel      : 4;    // Threshold Temperature Select (0~8)
		U32 btThTypeSel       : 2;    // Threshold Type Select(0:over 1:under)
		U32 btRsvd1        : 10;
	};
};

/**
 * @brief NVME Error Recovery Feature structure.
 *
 * Refer to NVM_Express_1_2_Gold_20141209.pdf.
 */
typedef union nvme_feat_err_recovery    FEATURE_ERR_RECOVERY_T, *FEATURE_ERR_RECOVERY_PTR;

union nvme_feat_err_recovery {
	U32 ulER;

	struct {
		U32 btTimeLimitER        : 16;   // Time Limited Error Recovery
		U32 btDULBE       : 1;    // Deallocated or Unwritten Logical Block Error Enable
		U32 btRsvd        : 15;
	};
} ;

/**
 * @brief NVME Volatile Write Cache Feature structure.
 *
 * Refer to NVM_Express_1_2_Gold_20141209.pdf.
 */
typedef union nvme_feat_vwc    FEATURE_VWC_T, *FEATURE_VWC_PTR;

union  nvme_feat_vwc {
	U32 ul32;

	struct {
		U32 btVWCEn         : 1;    // Volatile Write Cache Enable
		U32 btRsvd        : 31;
	};
} ;

/**
 * @brief NVME Number of Queues Feature structure.
 *
 * Refer to NVM_Express_1_2_Gold_20141209.pdf.
 */
typedef union nvme_feat_num_queue    FEATURE_NQ_T, *FEATURE_NQ_PTR;

union nvme_feat_num_queue {
	U32 ul32;

	struct {
		U32 btNSQR        : 16;   // Number of I/O Submission Queues Requested
		U32 btNCQR        : 16;   // Number of I/O Completion Queues Requested
	};
} ;

/**
 * @brief NVME Interrupt Coalescing Feature structure.
 *
 * Refer to NVM_Express_1_2_Gold_20141209.pdf.
 */
typedef union nvme_feat_int_coalesce    FEATURE_INT_COALESCE_T, *FEATURE_COALESCE_PTR;

union nvme_feat_int_coalesce {
	U32 ul32;

	struct {
		U32 btAggrTHR         : 8;    // aggregation threshold
		U32 btAggrTimer        : 8;    // aggregation time
		U32 btRsvd        : 16;
	};
} ;

/**
 * @brief NVME Interrupt Vector Configuration Feature structure.
 *
 * Refer to NVM_Express_1_2_Gold_20141209.pdf.
 */
typedef union nvme_feat_int_vector_config    FEATURE_INT_VEC_T, *FEATURE_INT_VEC_PTR;

union nvme_feat_int_vector_config {
	U32 ul32;

	struct {
		U32 btIntVec          : 16;   // interrupt vector
		U32 btCoalesceDis    : 1;    // coalescing disable
		U32 btRsvd        : 15;
	};
} ;

/**
 * @brief NVME Write Automicity Normal Feature structure.
 *
 * Refer to NVM_Express_1_2_Gold_20141209.pdf.
 */
typedef union nvme_feat_wr_atom_nor    FEATURE_WR_ATOM_NOR_T, *FEATURE_WR_ATOM_NOR_PTR;

union nvme_feat_wr_atom_nor {
	U32 ul32;

	struct {
		U32 btDisNor          : 1;    // Disable Normal
		U32 btRsvd        : 31;
	};
};

/**
 * @brief NVME Asynchronous Event Configuration Feature structure.
 *
 * Refer to NVM_Express_1_2_Gold_20141209.pdf.
 */
typedef union nvme_feat_async_event    FEATURE_ASYNC_EVENT_T, *FEATURE_ASYNC_EVENT_PTR;

union nvme_feat_async_event {
	U32 ul32;

	struct {

		U32 btSmartCW        : 8;    // SMART / Health Critical Warnings
		U32 btNsAttrNotice         : 1;    // Namespace Attribute Notices
		U32 btFwActiveNotice         : 1;    // Firmware Activation Notices
		U32 btTelemetryLogNotice        : 1;
		U32 btRsvd        : 21;
	};
} ;

/**
 * @brief NVME Autonomous Power State Transition Feature structure.
 *
 * Refer to NVM_Express_1_2_Gold_20141209.pdf.
 */
typedef union nvme_feat_apst    FEATURE_APST_T, *FEATURE_APST_PTR;

union nvme_feat_apst {
	U32 ul32;

	struct {
		U32 btApstEn       : 1;    // Autonomous Power State Transition Enable
		U32 btRsvd        : 31;
	};
} ;

typedef union nvme_feat_apst_data    FEATURE_APST_DATA_T, *FEATURE_APST_DATA_PTR;

union nvme_feat_apst_data {
	U32 ul32;

	struct {
		U32 btRsvd        : 3;
		U32 btIdleTPS   : 5;    // Idle Transition Power State
		U32 btIdleTPT   : 24;   // Idle Time Prior to Transition
	};
} ;


/**
 * @brief NVME Host Memory Buffer Feature structure.
 *
 * Refer to NVM_Express_1_2_Gold_20141209.pdf.
 */
typedef union nvme_feat_host_mem_buf     FEATURE_HMB_T, *FEATURE_HMB_PTR;

union nvme_feat_host_mem_buf {
	U32 ul32[5];

	// DW 11
	struct {
		U32 btEnHB      : 1;    // Enable Host Memory. If set to 1, then the controller may use the host memory buffer. When clear to 0, the controller shall bot use the host memory buffer.
		U32 btMemReturn  : 1;    // Memory Return
		U32 btRsvd      : 30;
		// DW 12
		U32 ulHMBSize;                // Host Memory Buffer Size

		// DW13
		U32 ulHMDLLAddr;               // Host Memory Descriptor List Lower Address

		// DW14
		U32 ulHMDLUAddr;               // Host Memory Descriptor List Upper Address

		// DW15
		U32 ulHMDLECnt;               // Host Memory Descriptor List Entry Count
	};


};

// Host memory buffer-host memory buffer descriptor entry
typedef union {
	U32 ulHMBDE[4];

	struct {
		U64 uoBufAddr;
		U32 ulBufSize;
		U32 ulRsvd;
	};
} NVME_HMBDE_T, *pNVME_HMBDE_T;

typedef enum {
	HMB_SUCCESS = 0,
	HMB_FORMAT_ERROR,
	HMB_CMDSEQ_ERROR,
} NVME_FEAT_HMB_STATUS;


#if SUPPORT_TIMESTAME_EN
/**
 * @brief NVME Timestamp Feature structure.
 *
 * Refer to NVM_Express_1_3.pdf.
 */
typedef union nvme_feat_timestamp     FEATURE_TIMESTAMP_T, *FEATURE_TIMESTAMP_PTR;


union nvme_feat_timestamp {
	U32 ul32[2];

	struct {
		U64 btTimestamp   : 48;
		U64  btSynch           : 1;
		U64  btTisOrigin       : 3;
		U64  btRsvd1           : 4;
		U64  btRsvd2           : 8;
	};
} ;
#endif


/**
 * @brief NVME Keep Alive Timer Feature structure.
 *
 * Refer to NVM_Express_1_3.pdf.
 */
typedef union nvme_feat_keep_alive_timer    FEATURE_KATIMER_T, *FEATURE_KATIMER_PTR;

union nvme_feat_keep_alive_timer {
	U32 ul32[3];

	struct {
		U32 ulKATO;  //timeout value
		//U32 ulRsvd;     //reserve
		U64 ulCurrKAT; //current count
	};
} ;

#if SUPPORT_THERMAL_MANAGEMENT_EN
/**
 * @brief NVME Host Controlled Thermal Management Feature structure.
 *
 * Refer to NVM_Express_1_3.pdf.
 */
typedef union nvme_feat_host_ctrl_thermal_mag    FEATURE_HOST_CTRL_TM_T, *FEATURE_HOST_CTRL_TM_PTR;

union nvme_feat_host_ctrl_thermal_mag {
	U32 ul32;

	struct {
		U16 uwThermalMT2;
		U16 uwThermalMT1;
	};
} ;

#endif




/**
 * @brief NVME non-operational power state config Feature structure.
 *
 * Refer to NVM_Express_1_3.pdf.
 */
typedef union nvme_feat_nop_ps_config    FEATURE_NOPPS_CFG_T, *FEATURE_NOPPS_CFG_PTR;

union nvme_feat_nop_ps_config {
	U32 ul32;

	struct {
		U32 btNoppmEn   : 1;    // non-operational power state permissive mode enable
		U32 btRsvd        : 31;
	};
} ;

/**
 * @brief NVME Software Progress Marker Feature structure.
 *
 * Refer to NVM_Express_1_2_Gold_20141209.pdf.
 */

typedef union nvme_feat_sw_pro_marker    FEATURE_SPM_T, *FEATURE_SPM_PTR;

union nvme_feat_sw_pro_marker {
	U32 ul32;

	struct {
		U32 btPreBSLCnt       : 8;    // Pre-boot Software Load Count
		U32 btRsvd        : 24;
	};
};

typedef union nvme_feat_hid    FEATURE_HID_T, *FEATURE_HID_PTR;

union nvme_feat_hid {
	U32 ul32;

	struct {
		U32 btExhid       : 1;    // set1:extended 128bit
		U32 btRsvd        : 31;
	};
};

typedef union nvme_feat_scp    FEATURE_SCP_T, *FEATURE_SCP_PTR;

union nvme_feat_scp {
	U32 ul32;

	struct {
		U32 btSCPControll : 1;    // 1: enable, 0: disable
		U32 btRsvd        : 31;
	};
};


typedef struct nvme_feat_allcmd_buf    FEATURE_ALLCMD_BUF_T, *FEATURE_ALLCMD_BUF_PTR;

struct nvme_feat_allcmd_buf {
	FEATURE_ARB_T                  Arbitration;
	FEATURE_PM_T                   PowerManagement;
	FEATURE_LBA_RANGE_T       LbaRange[FEAT_MAX_LBA_NUM];      // Each namespace may have more than 1 lba range settings, here reseverd 8 entrys for storing. Enough for the future using?
	FEATURE_TMPTH_T              TempTh[2];
	FEATURE_ERR_RECOVERY_T  ErrRecovery[FEAT_ERR_NUM];   // all namespaces the same
	FEATURE_VWC_T                 VolWrCache;
	FEATURE_NQ_T                   NumOfQueue;
	FEATURE_INT_COALESCE_T  IntCoalescing;
	FEATURE_INT_VEC_T            IVConfig[MAX_IV_NUM];      // 1 admin queue and 8 IO queues
	FEATURE_WR_ATOM_NOR_T WrAtom;
	FEATURE_ASYNC_EVENT_T   AEConfig;
	FEATURE_APST_T                AutoPST;
	FEATURE_APST_DATA_T       ApstEntry[IDFY_CTRL_MAX_NUM_PWR_STATE];           // E8 may have 8 power states,E7 may have 5 power states
	FEATURE_HMB_T                 HostMB;
	FEATURE_TIMESTAMP_T       Timestamp;
	FEATURE_KATIMER_T           KAliveTimer;
	FEATURE_HOST_CTRL_TM_T  CtrlTmpMag;
	FEATURE_NOPPS_CFG_T       NonOPSC;
	FEATURE_SPM_T                  SwProMark;
	//FEATURE_HID_T               HID;
	U8                                     HIDBuf[16];
};

typedef struct nvme_feat_mgt_info    FEATURE_MGT_T, *FEATURE_MGT_PTR;


struct nvme_feat_mgt_info {

	FEATURE_ALLCMD_BUF_T    FeatMode[3];           // used to save current value and default value
	FEATURE_SUP_CAP_T             FeatSavedBit;
	FEATURE_SUP_CAP_T             FeatChangeableBit;
};



// Feature Identifier
typedef enum {
	FID_RSVD,
	FID_ARB,         // Arbitration
	FID_PM,          // Power Management
	FID_LRT,         // LBA Range Type
	FID_TMPTH,       // Temperature Threshold
	FID_ER,          // Error Recovery
	FID_VWC,         // Volatile Write Cache
	FID_NQ,          // Number of Queues
	FID_IC,          // Interrupt Coalescing
	FID_IVC,         // Interrupt Vector Configuration
	FID_WAN,         // Write Automicity Normal
	FID_AEC,         // Asynchronous Event Configuration
	FID_APST,        // Autonomous Power State Transition
	FID_HMB,         // Host Memory Buffer
	FID_TIS,         // Timestamp
	FID_KAT,        //keep alive timer
	FID_CTM = 0x10,  // Controlled Thermal Management
	FID_NOP_PSC = 0x11,    //non-operational power state config
	FID_RESERVED = 0x17,
	FID_SPM = 0x80,  // Software Progress Marker
	FID_HID,          // Host Identifier
	FID_RSV_NOTE_MASK, //reservation notification mask
	FID_RSV_PERSIST  //reservation persistence
#if DELL_SCP_EN
	, FID_SCP = 0xD8
#endif
} NVME_FID_ENUM;


typedef enum {
	SEL_CUR,        // current
	SEL_DEF,        // default
	SEL_SAV,        // saved
	SEL_SCAP          // supported capabilities
} NVME_FEAT_SEL_ENUM;

//---------------------------------------------------feature done---------------------

//---------------------------------------------------AER----------------------------

typedef union nvme_aer_event_cfg    AER_EVENT_CFG_T, *AER_EVENT_CFG_PTR;

union nvme_aer_event_cfg {
	U16 uw16;
	struct {
		SMART_LOG_CRITICAL_WARN_T        CriticalWarn;
		U8     btNsAttribChange              : 1;
		U8     btFwActivateNotice            : 1;
		U8     btTelemetryLogChange      : 1;
		U8     btRsvd2                             : 5;
	};
} ;

/*
 *  Data structure for aer queue entry. Whenever aer command received, its cid will be saved here
 */
typedef struct nvme_aer_cid_entry    AER_CID_ENTRY_T, *AER_CID_ENTRY_PTR;

struct nvme_aer_cid_entry {
	//U16    cid;                                          // aer's cid info
	U16    uwAerCid;                                          // aer's cid info
} ;

typedef struct nvme_host_proc_info    HOST_PROC_INFO_T, * HOST_PROC_INFO_PTR;


struct nvme_host_proc_info {

	AER_EVENT_CFG_T   AerEventEn;
	// for aer queue(CID and buffer info)
	AER_CID_ENTRY_T   AerCidEntry[IDFY_CTRL_SUP_MAX_ADM_ASYNCCMD + 1]; // entry format: CID(16 bits) + misc
	// for temp aer queue,it will be process when receive asynchronous.
	ASYNC_EVENT_DW0_T AerTempEntry[MAX_ASYNC_EVENT_TYPE]; //TODO:the notice type should be divided into three categories
	U8     ubAerTypeMask;
	U8     ubAerQueHead;
	U8     ubAerQueTail;
	U8     ubAerQueSize; //IDFY_CTRL_SUP_MAX_ADM_ASYNCCMD + 1
	U8     ubAerQueCount;

	U8     btAerTempQueProidx; //current process index
	U8     btAerTempNoTriggerCnt; //total not sent event count
	U8     transfer_4k_offset; //record receive or send data 4k byte unit's offset

	U8     ubSaveHostInfo;
	U8	   ubSaveHostInfoNeedPolling;

#if (WORKAROUND_STANDBY == TRUE)
	U8	   ubSendAERCpl;
	U8     Rsvd1[1];

#else
	U8     Rsvd1[2];

#endif
} ;

typedef struct nvme_nveme_ata_security    NVME_ATA_SECURITY_T, *NVME_ATA_SECURITY_PTR;

struct nvme_nveme_ata_security {
	// do not modify the structure arbitrarily
	union {
		U8 ubAll;
		struct {
			U8 btSupport 				: 1;
			U8 btEn 					: 1;
			U8 btLocked        			: 1;
			U8 btFrozen        			: 1;
			U8 btExpired        		: 1;
			U8 btEnhanceEraseSupport	: 1;
			U8 Reserved 				: 2;
		};
	} Status;
	union {
		U8 ubAll;
		struct {
			U8 btMasterPasswordValid 		: 1;
			U8 btMasterPasswordCapability 	: 1;
			U8 btLastCmdIsErasePrepare		: 1;
			U8 btHRST						: 1;
			U8 Reserved						: 4;
		};
	} Config;
	U8 ubPasswordAttemptCnt;
	U16 uwMasterPasswordID;
	U8 aubMasterPasswordDigest[SECURITY_SHA_256_OUTPUT_LENGTH];
	U8 aubUserPasswordDigest[SECURITY_SHA_256_OUTPUT_LENGTH];
	U64 uoRecordIOCnt;
} ;

#pragma pack()


//---------------------------------------------AER---------------------------------------------


AOM_HOST_RESET extern void nvme_host_feat_init_api ( FEATURE_MGT_PTR  pFeatStruct);
AOM_HOST_RESET extern void nvme_feat_host_restore_api (FEATURE_MGT_PTR  pFeatStruct);
AOM_NRW extern U8  nvme_feat_check_sup_fid (U8 ubFeatureID);
AOM_NRW extern U16 nvme_feat_check_nsid (OPT_HCMD_PTR pCmd);
AOM_NRW extern void nvme_feature_cmd_mesage (OPT_HCMD_PTR pCmd);
AOM_NRW extern void nvme_setfeat_arbi_api (OPT_HCMD_PTR pCmd, FEATURE_MGT_PTR  pFeatStruct, U8 ubSaveBit);
AOM_NRW extern void nvme_setfeat_pm_api (OPT_HCMD_PTR pCmd, FEATURE_MGT_PTR  pFeatStruct, U8 ubSaveBit);
AOM_NRW extern void nvme_setfeat_lba_range_api (OPT_HCMD_PTR pCmd, FEATURE_MGT_PTR  pFeatStruct, U8 ubSaveBit);
AOM_NRW extern void nvme_setfeat_tmpth_api (OPT_HCMD_PTR pCmd, FEATURE_MGT_PTR  pFeatStruct, U8 ubSaveBit);
AOM_NRW extern void nvme_setfeat_er_api (OPT_HCMD_PTR pCmd, FEATURE_MGT_PTR  pFeatStruct, U8 ubSaveBit);
AOM_NRW extern void nvme_setfeat_vwc_api (OPT_HCMD_PTR pCmd, FEATURE_MGT_PTR  pFeatStruct, U8 ubSaveBit);
AOM_NRW extern void nvme_setfeat_nq_api (OPT_HCMD_PTR pCmd, FEATURE_MGT_PTR  pFeatStruct, U8 ubSaveBit);
AOM_NRW extern void nvme_setfeat_ic_api (OPT_HCMD_PTR pCmd, FEATURE_MGT_PTR  pFeatStruct, U8 ubSaveBit);
AOM_NRW extern void nvme_setfeat_ivc_api (OPT_HCMD_PTR pCmd, FEATURE_MGT_PTR  pFeatStruct, U8 ubSaveBit);
AOM_NRW extern void nvme_setfeat_wan_api (OPT_HCMD_PTR pCmd, FEATURE_MGT_PTR  pFeatStruct, U8 ubSaveBit);
AOM_NRW extern void nvme_setfeat_aec_api (OPT_HCMD_PTR pCmd, FEATURE_MGT_PTR  pFeatStruct, U8 ubSaveBit);
AOM_NRW extern void nvme_setfeat_apst_api (OPT_HCMD_PTR pCmd, FEATURE_MGT_PTR  pFeatStruct, U8 ubSaveBit);
AOM_NRW extern void nvme_setfeat_hmb_api (OPT_HCMD_PTR pCmd, FEATURE_MGT_PTR  pFeatStruct, U8 ubSaveBit);

#if SUPPORT_TIMESTAME_EN
AOM_NRW extern void nvme_setfeat_tis_api (OPT_HCMD_PTR pCmd, FEATURE_MGT_PTR  pFeatStruct, U8 ubSaveBit);
AOM_NRW extern void nvme_setfeat_tis_timer_api (void);
#endif

#if SUPPORT_KEEP_ALIVE_EN
AOM_NRW extern void nvme_keep_alive (OPT_HCMD_PTR pCmd);
AOM_NRW extern void nvme_setfeat_kat_api (OPT_HCMD_PTR pCmd, FEATURE_MGT_PTR  pFeatStruct, U8 ubSaveBit);
AOM_NRW extern void nvme_setfeat_kat_timer_check_api (FEATURE_MGT_PTR  pFeatStruct);
AOM_NRW extern void nvme_keep_alive_timeout (void);
#endif

#if SUPPORT_THERMAL_MANAGEMENT_EN
AOM_NRW extern void nvme_setfeat_ctm_api (OPT_HCMD_PTR pCmd, FEATURE_MGT_PTR  pFeatStruct, U8 ubSaveBit);
#endif

AOM_NRW extern void nvme_setfeat_nop_psc_api (OPT_HCMD_PTR pCmd, FEATURE_MGT_PTR  pFeatStruct, U8 ubSaveBit);
AOM_NRW extern void nvme_setfeat_spm_api (OPT_HCMD_PTR pCmd, FEATURE_MGT_PTR  pFeatStruct, U8 ubSaveBit);
AOM_NRW extern void nvme_setfeat_hid_api (OPT_HCMD_PTR pCmd, FEATURE_MGT_PTR  pFeatStruct, U8 ubSaveBit);
#if (DELL_SCP_EN)
AOM_NRW void nvme_setfeat_scp_api (OPT_HCMD_PTR pCmd, U8 ubSaveBit);
#endif /* (DELL_SCP_EN) */
AOM_NRW extern void nvme_getfeat_lba_range_api (OPT_HCMD_PTR pCmd);
AOM_NRW extern void nvme_getfeat_apst_api (OPT_HCMD_PTR pCmd);
AOM_NRW extern void nvme_getfeat_tis_api (OPT_HCMD_PTR pCmd);
AOM_NRW extern void nvme_getfeat_hid_api (OPT_HCMD_PTR pCmd);
//EXTERN void nvme_get_feature_vendor_get_smart_log (OPT_HCMD_PTR pCmd);
AOM_NRW extern void nvme_feat_get_async_evt_cfg (FEATURE_MGT_PTR  pFeatStruct, U16 *puwAerConfig);
AOM_HOST_RESET extern void nvme_aer_struct_init_api (HOST_PROC_INFO_PTR   gpHostPro);
AOM_HOST_RESET void nvme_aer_clear_cmd_api (HOST_PROC_INFO_PTR pHostPro);
AOM_NRW extern U8  nvme_aer_insert_que (HOST_PROC_INFO_PTR   gpHostPro, AER_CID_ENTRY_PTR pInCID);
AOM_NRW extern U8  nvme_aer_delete_que (HOST_PROC_INFO_PTR gpHostPro, AER_CID_ENTRY_PTR pOutCID);
AOM_NRW extern void nvme_aer_delete_from_temp_que (HOST_PROC_INFO_PTR   gpHostPro, ASYNC_EVENT_DW0_T Entry);
AOM_NRW extern void nvme_aer_insert_to_temp_que (HOST_PROC_INFO_PTR   gpHostPro, ASYNC_EVENT_DW0_T Entry);
AOM_NRW extern void nvme_aer_cmd_process_api(void);
AOM_NRW extern U32 nvme_set_HMB_feature(OPT_HCMD_PTR pCmd, FEATURE_MGT_PTR  pFeatStruct);
AOM_NRW extern void nvme_getfeat_hmb_api (OPT_HCMD_PTR pCmd);

AOM_NRW_2 void nvme_asyn_event_type1_check (HOST_PROC_INFO_PTR pHostPro);

#endif /* _SHR_NVME_FEAT_LIB_H_ */
