#ifndef _RNG_H_
#define _RNG_H_

#include "hal/sys/reg/sys_pd1_reg.h"

#if PS5017_EN
#define	M_SET_RNG_MODE(MODE)					do { \
												R32_SYS1_RNG_CTRL[RANDOM_NUMBER_GENERATOR_CHANNEL_0] &= (~(CR_RNG_MD_MASK << CR_RNG_MD_SHIFT)); \
												R32_SYS1_RNG_CTRL[RANDOM_NUMBER_GENERATOR_CHANNEL_0] |= (((MODE) & CR_RNG_MD_MASK) << CR_RNG_MD_SHIFT); \
												} while(0)
#define M_SET_RNG_STB_PER(CHANNEL, VALUE)		do { \
												R32_SYS1_RNG_CTRL[(CHANNEL)] &= (~(CR_RNG_STB_PER_MASK << CR_RNG_STB_PER_SHIFT)); \
												R32_SYS1_RNG_CTRL[(CHANNEL)] |= (((VALUE) & CR_RNG_STB_PER_MASK) << CR_RNG_STB_PER_SHIFT); \
												} while(0)
#define M_SET_RNG_LOOP_SFTBIT(CHANNEL, VALUE)	do { \
												R32_SYS1_RNG_CTRL[(CHANNEL)] &= (~(CR_RNG_LOOP_SFTBIT_MASK << CR_RNG_LOOP_SFTBIT_SHIFT)); \
												R32_SYS1_RNG_CTRL[(CHANNEL)] |= (((VALUE) & CR_RNG_LOOP_SFTBIT_MASK) << CR_RNG_LOOP_SFTBIT_SHIFT); \
												} while(0)
#define M_SET_RNG_LOOP_CNT(CHANNEL, VALUE)		do { \
												R32_SYS1_RNG_CTRL[(CHANNEL)] &= (~(CR_RNG_LOOP_CNT_MASK << CR_RNG_LOOP_CNT_SHIFT)); \
												R32_SYS1_RNG_CTRL[(CHANNEL)] |= (((VALUE) & CR_RNG_LOOP_CNT_MASK) << CR_RNG_LOOP_CNT_SHIFT); \
												} while(0)
#define M_SET_RNG_LOOP_PER(CHANNEL, VALUE)		do { \
												R32_SYS1_RNG_CTRL[(CHANNEL)] &= (~(CR_RNG_LOOP_PER_MASK << CR_RNG_LOOP_PER_SHIFT)); \
												R32_SYS1_RNG_CTRL[(CHANNEL)] |= (((VALUE) & CR_RNG_LOOP_PER_MASK) << CR_RNG_LOOP_PER_SHIFT); \
												} while(0)
#define M_ENABLE_NOISE_GENERATOR(CHANNEL)		(R32_SYS1_RNG[R32_SYS1_RNG_AIP_CTRL0] |= (CR_RNG_EN_CHANNEL0_BIT << (CHANNEL)))
#define M_ENABLE_ALL_CHANNEL_NOISE_GENERATOR()	(R32_SYS1_RNG[R32_SYS1_RNG_AIP_CTRL0] |= ((CR_RNG_EN_MASK) << (CR_RNG_EN_SHIFT)))
#define M_DISABLE_NOISE_GENERATOR(CHANNEL)		(R32_SYS1_RNG[R32_SYS1_RNG_AIP_CTRL0] &= (~(CR_RNG_EN_CHANNEL0_BIT << (CHANNEL))))
#define M_DISABLE_ALL_CHANNEL_NOISE_GENERATOR() (R32_SYS1_RNG[R32_SYS1_RNG_AIP_CTRL0] &= (~((CR_RNG_EN_MASK) << (CR_RNG_EN_SHIFT))))
#define M_START_RNG(CHANNEL)					(R32_SYS1_RNG_CTRL[RANDOM_NUMBER_GENERATOR_CHANNEL_0] |= (CR_RUN_EN_CHANNEL0_BIT << (CHANNEL)))
#define M_START_ALL_CHANNEL_RNG()				(R32_SYS1_RNG_CTRL[RANDOM_NUMBER_GENERATOR_CHANNEL_0] |= ((CR_RUN_EN_MASK) << (CR_RUN_EN_SHIFT)))
#define M_STOP_RNG(CHANNEL)						(R32_SYS1_RNG_CTRL[RANDOM_NUMBER_GENERATOR_CHANNEL_0] &= (~(CR_RUN_EN_CHANNEL0_BIT << (CHANNEL))))
#define M_STOP_ALL_CHANNEL_RNG()				(R32_SYS1_RNG_CTRL[RANDOM_NUMBER_GENERATOR_CHANNEL_0] &= (~(CR_RUN_EN_MASK) << (CR_RUN_EN_SHIFT)))
#define M_GET_RNG_VALUE_L()						(R32_SYS1_RNG[R32_SYS1_RNG_FINAL_L])
#define M_GET_RNG_VALUE_H()						((R32_SYS1_RNG[R32_SYS1_RNG_FINAL_H] >> RNG_FINAL_H_SHIFT) & RNG_FINAL_H_MASK)
#define M_GET_RNG_VALUE()						((((U64)M_GET_RNG_VALUE_H()) << 32) | ((U64)M_GET_RNG_VALUE_L()))
#define M_CLEAR_RNG_DONE_STATUS(CHANNEL)		(R32_SYS1_RNG[R32_SYS1_RNG_STS] |= (RNG_DONE_STS_CHANNEL0_BIT << (CHANNEL)))
#define M_CLEAR_RNG_ALL_CHANNEL_DONE_STATUS()	(R32_SYS1_RNG[R32_SYS1_RNG_STS] |= ((RNG_DONE_STS_MASK) << (RNG_DONE_STS_SHIFT)))
#define M_GET_RNG_STATUS_ALL_DONE()				(R32_SYS1_RNG[R32_SYS1_RNG_STS] & RNG_ALL_DONE_BIT)
#define M_GET_RNG_STATUS_CHECK_DONE()			(R32_SYS1_RNG[R32_SYS1_RNG_STS] & RNG_CHK_DONE_BIT)
#define M_GET_RNG_STATUS_CHECK_ERROR()			(R32_SYS1_RNG[R32_SYS1_RNG_STS] & RNG_CHK_ERR_BIT)

#elif PS5021_EN /* PS5017_EN */
#define	M_SET_RNG_ANOLOG_MODE()					(R32_SYS1_ANALOG_CTRL[R32_SYS1_ANALOG_NOISE_GEN_CTRL7] &= ~CR_RNG_MD_G25_BIT)
#define M_ENABLE_NOISE_GENERATOR()				(R32_SYS1_ANALOG_CTRL[R32_SYS1_ANALOG_NOISE_GEN_CTRL7] |= CR_RNG_EN_G25_BIT)
#define M_DISABLE_NOISE_GENERATOR()				(R32_SYS1_ANALOG_CTRL[R32_SYS1_ANALOG_NOISE_GEN_CTRL7] &= ~CR_RNG_EN_G25_BIT)
#define M_START_RNG()							(R32_SYS1_ANALOG_CTRL[R32_SYS1_ANALOG_NOISE_GEN_CTRL2] |= CR_RNG_EN_BIT)
#define M_STOP_RNG()							(R32_SYS1_ANALOG_CTRL[R32_SYS1_ANALOG_NOISE_GEN_CTRL2] &= ~CR_RNG_EN_BIT)
#define M_GET_RNG_VALUE()						(R32_SYS1_ANALOG_CTRL[R32_SYS1_ANALOG_NOISE_GEN_CTRL4])
#define M_GET_RNG_STATUS()						(R32_SYS1_ANALOG_CTRL[R32_SYS1_ANALOG_NOISE_GEN_CTRL5] & NEW_DATA_RDY_G25_SYNC_BIT)

#else /* PS5017_EN */
#define	M_SET_RNG_MODE(MODE)					do { \
												R32_SYS1_RNG[R32_SYS1_RNG_CTRL] &= ~(CR_RNG_MD_MASK << CR_RNG_MD_SHIFT); \
												R32_SYS1_RNG[R32_SYS1_RNG_CTRL] |= ((MODE) & CR_RNG_MD_MASK) << CR_RNG_MD_SHIFT; \
												} while(0)
#define M_ENABLE_NOISE_GENERATOR()				(R32_SYS1_RNG[R32_SYS1_RNG_CTRL] |= CR_RNG_EN_BIT)
#define M_DISABLE_NOISE_GENERATOR()				(R32_SYS1_RNG[R32_SYS1_RNG_CTRL] &= ~CR_RNG_EN_BIT)
#define M_START_RNG()							(R32_SYS1_RNG[R32_SYS1_RNG_CTRL] |= CR_RUN_EN_BIT)
#define M_STOP_RNG()							(R32_SYS1_RNG[R32_SYS1_RNG_CTRL] &= ~CR_RUN_EN_BIT)
#define M_GET_RNG_VALUE()						(R32_SYS1_RNG[R32_SYS1_RNG_REG])
#define M_GET_RNG_STATUS()						(R32_SYS1_RNG[R32_SYS1_RNG_DATA_RDY] & NEW_DATA_RDY_BIT)
#endif /* PS5017_EN */

#endif /* _RNG_H_ */
