#ifndef _ITC_H_
#define _ITC_H_

#include "hal/sys/reg/sys_pd1_reg.h"

#if (!PS5017_EN)
/* INTERRUPT_GROUP_ID_SYSTEM_0 Interrupt Status 0 (group 0) */
#define M_ITC_GET_GPIO_SYS_EVT			((U32)(R32_SYS1_ITC[R32_SYS1_INT_STS0] & (GPIO_SYS_EVT_MASK << GPIO_SYS_EVT_SHIFT)))
#define M_ITC_CLR_GPIO_SYS_EVT			(R32_SYS1_ITC[R32_SYS1_INT_STS0] = (GPIO_SYS_EVT_MASK << GPIO_SYS_EVT_SHIFT))
/* INTERRUPT_GROUP_ID_SYSTEM_1 Interrupt Status 1 (group 1) */

/* INTERRUPT_GROUP_ID_TIMER Interrupt Status 2 (group 2) */
#define M_ITC_GET_RTT0_SYS_EVT			((U32)(R32_SYS1_ITC[R32_SYS1_INT_STS2] & RTT0_SYS_EVT_BIT))
#define M_ITC_CLR_RTT0_SYS_EVT			(R32_SYS1_ITC[R32_SYS1_INT_STS2] = RTT0_SYS_EVT_BIT)
#define M_ITC_GET_RTT1_SYS_EVT_0		((U32)(R32_SYS1_ITC[R32_SYS1_INT_STS2] & RTT1_SYS_EVT_0_BIT))
#define M_ITC_CLR_RTT1_SYS_EVT_0		(R32_SYS1_ITC[R32_SYS1_INT_STS2] = RTT1_SYS_EVT_0_BIT)
#define M_ITC_GET_RTT1_SYS_EVT_1		((U32)(R32_SYS1_ITC[R32_SYS1_INT_STS2] & RTT1_SYS_EVT_1_BIT))
#define M_ITC_CLR_RTT1_SYS_EVT_1		(R32_SYS1_ITC[R32_SYS1_INT_STS2] = RTT1_SYS_EVT_1_BIT)
#define M_ITC_GET_RTT1_SYS_COMB_EVT		M_ITC_GET_RTT1_SYS_EVT_0
#define M_ITC_CLR_RTT1_SYS_COMB_EVT		M_ITC_CLR_RTT1_SYS_EVT_0
#define M_ITC_GET_WDT_SYS_EVT			((U32)(R32_SYS1_ITC[R32_SYS1_INT_STS2] & WDT_SYS_EVT_BIT))
#define M_ITC_CLR_WDT_SYS_EVT			(R32_SYS1_ITC[R32_SYS1_INT_STS2] = WDT_SYS_EVT_BIT)

/* INTERRUPT_GROUP_ID_PARITY_ERROR Interrupt Status 3~7 (group 3) */

/* INTERRUPT_GROUP_ID_NVME_UFS_APU Interrupt Status 8 (group 4) */

/* INTERRUPT_GROUP_ID_PCIE Interrupt Status 8 (group 5) */

/* INTERRUPT_GROUP_ID_COP1 Interrupt Status 8 (group 6) */

/* INTERRUPT_GROUP_ID_BMU Interrupt Status 8 (group 7) */

/* INTERRUPT_GROUP_ID_MR Interrupt Status 8 (group 8) */

/* INTERRUPT_GROUP_ID_DMAC Interrupt Status 8 (group 9) */

/* INTERRUPT_GROUP_ID_COP0 Interrupt Status 8 (group 10) */

/* INTERRUPT_GROUP_ID_PIC Interrupt Status 8 (group 11) */

/* INTERRUPT_GROUP_ID_D2H Interrupt Status 8 (group 12) */

/* INTERRUPT_GROUP_ID_AHB Interrupt Status 8 (group 13) */

/* INTERRUPT_GROUP_ID_RS Interrupt Status 8 (group 14) */

/* INTERRUPT_GROUP_ID_FMSG Interrupt Status 8 (group 15) */

/* INTERRUPT_GROUP_ID_TDBG Interrupt Status 8 (group 16) */

/* INTERRUPT_GROUP_ID_ITC Interrupt Status 8 (group 17) */

#endif
#endif /* _ITC_H_ */
