#include "burner/Burner_api.h"
#include "burner/codepointer.h"
#include "burner/codesignheader_api.h"
#include "burner/IDpage.h"
#include "ftl/ftl_nrw.h"
#include "ftl/ftl_api.h"
#include "hal/dmac/dmac_api.h"
#include "hal/dmac/dmac_pop_cmd.h"
#include "hal/fip/fip.h"
#include "hal/pic/uart/uart_api.h"
#include "nvme_api/nvme/shr_hal_nvme.h"
#include "host/VUC_handler.h"
#include "vuc/VUC_command.h"
#include "vuc/VUC_ISPFlash.h"
#include "vuc/VUC_ISPReadFlash.h"
#if (PS5017_EN)
#include "vuc/VUC_ReadScanFlashWindow.h"
#endif /*(PS5017_EN)*/
#if (USB == HOST_MODE)
#include "vuc/VUC_ReadFlash.h"
#include "ftl/ftl_barrier.h"
#include "hal/cop0/cop0_api.h"
#endif /* (USB == HOST_MODE) */

#if (USB == HOST_MODE)
U8 VUCReadPadPage(FlashAccessInfo_t *pFlashInfo)
{
	U8 ubLocalLPCRC = 0;
	U8 ubPCARuleMode = (FLH_SLC == gFlhEnv.ulFlashDefaultType.BitMap.CellType) ? COP0_PCA_RULE_0 : COP0_PCA_RULE_2;
	U32 ulSystemLocalPCA;
	PCA_t ulSystemPCA;

	ulSystemLocalPCA = M_GET_PCA(0, pFlashInfo->ulBlock, pFlashInfo->uwPage, pFlashInfo->ubFlashCE, pFlashInfo->ubChannel, pFlashInfo->ubPlane, 0, ubPCARuleMode);
	M_FWPCA_SET(ulSystemPCA.ulAll, ulSystemLocalPCA, gPCAInfo.ubMaxZByte, 0, ubLocalLPCRC);


#if (!BURNER_MODE_EN)
	SystemAreaWaitAndLockCOP0();
#endif /* !BURNER_MODE_EN */

	//-------- Change LDPC Mode --------
	//	FlaSwitchLDPCHandler(gFlhEnv.ubDefaultLDPCMode);


	//check here---------------------------------------------------------------------------------
	FTLCOP0ReadN4kEntry_AOM_INIT_2((FTLCOP0ReadN4kEntryMode_t) {
		R_GETLCA_FROM_L4KTABLE, COP0_R_SYSTEM_INITIAL_SCAN, 1, NULL
	}, ulSystemPCA,  &pFlashInfo->ulBufBase);
	//check here---------------------------------------------------------------------------------


	//-------- Restore LDPC Mode --------
	//	FlaSwitchLDPCHandler(ubBackupECC);
	if (!BURNER_MODE_EN) {
		ErrHandleUnlockCOP0();
	}

	if (SPARE_LCA_CODEBLOCK != gulTieoutLCA[TIEOUT_SINGLE_4K_LCA_OFFSET]) {
		return FAIL;
	}

	return PASS;
}

void VUC_ReadSinglePage(U32 ulPhysicalAddr, U32 ulPCA, U8 COP0Par, U8 ubReadFrameNumber, U8 ubReadMode)
{
	U8 ubLdpcTmp, ubFrameNum = 0;
	U32 ulSeed;
	U32 ulFCTL_NIT_CFG_TMP, ulCOP0_TMP, ulFCTL_FSA_SEL_TMP, ulFCTL_BACK_RESTORE_TMP;
#if	(!BURNER_MODE_EN)&&(!RDT_MODE_EN)
	DMACParam_t DMACParam;
#endif
	COP0Status_t eCOP0Status;
	COP0ReadSQPara_t ReadSQPara = {{0}};
	BUF_TYPE_t ulBufPara = {0};
	P4KTable16B_t *pP4KTable = NULL;
	cmd_table_t uoCallbackInfo = {(U32)VUCRead_Callback, {0}};
	FWSetPara_t ulFWSet = {0};
	U8 ubLDPCModeBackup = (U8)M_GET_ECC_MODE();
	U16 uwDMACntOneValue[4];
	U8 ubPlaneIdx, ubCH;

	//-------Wait other command done-------
	FTLSendCOP0Barrier(COP0_BARRIER_ALL, TRUE, BIT_BARRIER_CMD_COP0_NORMAL);

	// -------Save register config-------
	M_FIP_VUC_DIRECTED_READ_BACK_UP(ulFCTL_NIT_CFG_TMP, ulCOP0_TMP, ubLdpcTmp, ulFCTL_FSA_SEL_TMP, ulFCTL_BACK_RESTORE_TMP);

	ulSeed = ((COP0Par & (BIT5 | BIT4)) >> 4);

	M_UART(VUC_, "%s: PCA:%lu\n", __func__, ulPCA);

	M_CLR_COP0_LCA_CMP();

#if	(!BURNER_MODE_EN)&&(!RDT_MODE_EN)
	DMACParam.DMACSetValue.ulDestAddr = ulPhysicalAddr;
	DMACParam.DMACSetValue.ulLowValue = 0;
	DMACParam.DMACSetValue.ulHighValue = 0;
	DMACParam.DMACSetValue.ul32ByteNum = SIZE_IN_32B(SIZE_16KB);
	DMACParam.DMACSetValue.btValidate = FALSE;
	gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
	DMACSetValue(&DMACParam, gulDMACDirectWaitDone_Callback, 0);
	while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
		DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
	}
#endif

	M_FIP_VUC_DIRECTED_READ_SOURCE_DATA();
	M_CLR_COP0_CONV(COP0_MT_TEMP_BUF_ADDR_READ);

	while (ubFrameNum < ubReadFrameNumber) {

		ulFWSet.ulFWSet = DBUF_VUC_BACKUP_READ_L4K_IDX + ubFrameNum;
		ulFWSet.ubZInfo = MAX_ZINFO;

		COP0API_FillCOP0ReadSQUserData0Para(COP0_R_INITIAL_SCAN, &ReadSQPara);

		/*
		 * ubReadMode from VUC cmd:
		 * All Zero - Read DATA
		 * bit0     - Read System
		 * bit1     - Use COP0Par bit[4:5] seed mode
		*/

		if (ubReadMode & BIT0) { //Read System Area
			ReadSQPara.UserData0.btSysArea = TRUE;
		}
		else if (ubReadMode & BIT1) { //Use COP0Par bit[4:5]
			ReadSQPara.UserData0.btUseParamSeed = TRUE;
		}


		/*
		 * COP0Par from VUC cmd:
		 * bit0 - SLC mode enable
		 * bit1 - VBRMP, 0:Enable, 1:Bypass
		 * bit2 - RUT, 0:Enable, 1:Bypass
		 * bit3 - 0: VBMP, 1:TIEIN
		 * bit[4:5] - seed mode
		 *			  00 : BURNER_RANDOM_SEED
		 *			  01 : 0
		 *			  10 : RS_PARITY_RANDOM_SEED
		 *			  11 : Reserved
		 * bit6 - ECC Mode, 0:Default, 1:SET_ECC_MODE_7
		 * bit7 - Coversion, 0:Enable, 1:Bypass
		 */

		ulBufPara.A.ulBUF_ADR = ulPhysicalAddr + ubFrameNum * DEF_KB(4);
		ReadSQPara.UserData0.btSLCMode = ((COP0Par & BIT0) == 0 ) ? FALSE : TRUE;
		ReadSQPara.UserData0.btSLCSetMethod = ((COP0Par & BIT3) == 0) ? COP0_TIE_D1SLC_MODE_FROM_VBRMP : COP0_TIE_D1SLC_MODE_FROM_TIEIN;
		ReadSQPara.UserData0.TagID = TAG_ID_ALLOCATE;
		uoCallbackInfo.ulData.ulFLHInfo.btKeepTag = TRUE;
		ReadSQPara.ulPCA.ulAll = (ulPCA + ubFrameNum);
		ReadSQPara.UserData0.btRUTBps = ((COP0Par & BIT2) == 0) ? FALSE : TRUE;		//ByPass RUT
		ReadSQPara.UserData0.btVBRMPBps = ((COP0Par & BIT1) == 0) ? FALSE : TRUE;	//ByPass VBRMP
		ReadSQPara.UserData0.AttrMTTemplate = COP0_MT_TEMP_BUF_ADDR_READ;
		ReadSQPara.UserData0.L4KNum = 0;
		ReadSQPara.UserData0.DataDef |= COP0_SEED_EN_BIT;
		switch (ulSeed) {
		case 0:
			ReadSQPara.ulSeed.Seed = BURNER_RANDOM_SEED;
			break;
		case 1:
			ReadSQPara.ulSeed.Seed = 0; //Table
			break;
		case 2:
			ReadSQPara.ulSeed.Seed = RS_PARITY_RANDOM_SEED;
			break;
		default:
			ReadSQPara.ulSeed.Seed = BURNER_RANDOM_SEED;
			break;
		}
		ReadSQPara.BufVld.ubBufType = BUF_TYPE_A;
		ReadSQPara.BufVld.pulBufInfoPtr = &ulBufPara;
		ReadSQPara.UserData0.btReadBackUp4K = TRUE;
		ReadSQPara.UserData0.DataDef |= COP0_FWSET_EN_BIT;
		ReadSQPara.pulFWSetPtr = &ulFWSet;

		M_VUC_DIRECTED_READ_SET_ALL_CH_NON_INT();
		M_VUC_DIRECTED_READ_SET_ALL_CH_FORCE_DATA();

		if ((COP0Par & BIT6) == BIT6) {
			//-------- Change LDPC Mode to 2432 bit --------
			FlaSwitchLDPCHandler(SET_ECC_MODE_7);
		}

		if ((COP0Par & BIT7) == BIT7) {
			M_SET_COP0_CONV(COP0_MT_ATTR2); //Conv Disable
		}

#if (NICKS_BIN_ACRR && IM_N48R)//Dylan for V6 RDT code,for pass compiling
		RDT_API_STRUCT_PTR rdt = &gRdtApiStruct;
		rdt->rdt_bin_value = 0x01;
#endif /*(NICKS_BIN_ACRR && IM_N48R)*/

		eCOP0Status = COP0API_SendReadSQ(&ReadSQPara, &uoCallbackInfo);
		M_FW_ASSERT(ASSERT_VUC_0x0AB9, eCOP0Status.btSendCmdSuccess);

		gulTieoutLCA[TIEOUT_SINGLE_4K_LCA_OFFSET] = LCA_BEFORE_READ;
		while (LCA_BEFORE_READ == gulTieoutLCA[TIEOUT_SINGLE_4K_LCA_OFFSET]) {
			FWCop0Waiting();
		}

		M_FIP_VUC_CLEAR_BUSY();
		pP4KTable = (P4KTable16B_t *)(DBUF_VUC_BACKUP_READ_L4K_BASE);
		memcpy((void *)(ulPhysicalAddr + (ubReadFrameNumber << 12) + ubFrameNum * (DEF_16B + DEF_4B)), (void *)&gulTieoutLCA, DEF_4B);
		memcpy((void *)(ulPhysicalAddr + (ubReadFrameNumber << 12) + 4 + ubFrameNum * (DEF_16B + DEF_4B)), (void *)&pP4KTable[ubFrameNum], DEF_16B);

		ubPlaneIdx = M_FW_GET_PLANE_INDEX(ulPCA + ubFrameNum);
		ubCH = M_FW_GET_CH(ubPlaneIdx);
		uwDMACntOneValue[ubFrameNum] = M_FIP_GET_ECC_INF(ubCH);

		ubFrameNum++;
	}
	memcpy((void *)(ulPhysicalAddr + (ubReadFrameNumber << 12) + ubReadFrameNumber * (DEF_16B + DEF_4B)), (void *)&uwDMACntOneValue, DEF_2B * ubReadFrameNumber);

	//-------Restore register config-------
	M_FIP_VUC_DIRECTED_READ_RESTORE(ulFCTL_NIT_CFG_TMP, ulCOP0_TMP, ulFCTL_FSA_SEL_TMP, ulFCTL_BACK_RESTORE_TMP);
	FlaSwitchLDPCHandler(ubLDPCModeBackup);
	if ((COP0Par & BIT7) == BIT7) {
		M_CLR_COP0_CONV(COP0_MT_ATTR2); //Conv Enable
	}
}
#endif /* (USB == HOST_MODE) */

U8 VUCReadIdpage(FlashAccessInfo_t *pFlashInfo)
{
	U8 ubBackupECC = M_GET_ECC_MODE(), ubLocalLPCRC = 0;
	U8 ubPCARuleMode = (FLH_SLC == gFlhEnv.ulFlashDefaultType.BitMap.CellType) ? COP0_PCA_RULE_0 : COP0_PCA_RULE_2;
	U32 ulCRC32Result, ulSystemLocalPCA;
	PCA_t ulSystemPCA;

	ulSystemLocalPCA = M_GET_PCA(0, pFlashInfo->ulBlock, pFlashInfo->uwPage, pFlashInfo->ubFlashCE, pFlashInfo->ubChannel, pFlashInfo->ubPlane, 0, ubPCARuleMode);
	M_FWPCA_SET(ulSystemPCA.ulAll, ulSystemLocalPCA, gPCAInfo.ubMaxZByte, 0, ubLocalLPCRC);

#if (!BURNER_MODE_EN)
	SystemAreaWaitAndLockCOP0();
#endif /* !BURNER_MODE_EN */

	//-------- Change LDPC Mode to 2432 bit --------
	FlaSwitchLDPCHandler(SET_ECC_MODE_7);

	pFlashInfo->ulBufBase = IDPG_BASE;

	FTLCOP0ReadN4kEntry_AOM_INIT_2((FTLCOP0ReadN4kEntryMode_t) {
		R_GETLCA_FROM_L4KTABLE, COP0_R_SYSTEM_INITIAL_SCAN, 1, NULL
	}, (PCA_t)FTLReverseCop0CieInPCA(ulSystemPCA.ulAll),  &pFlashInfo->ulBufBase);

	//-------- Restore LDPC Mode --------
	FlaSwitchLDPCHandler(ubBackupECC);
	if (!BURNER_MODE_EN) {
		ErrHandleUnlockCOP0();
	}

	if (SPARE_LCA_CODEBLOCK != gulTieoutLCA[TIEOUT_SINGLE_4K_LCA_OFFSET]) {
		return FAIL;
	}

	if (ID_HEADER_HEXVAL != IDPGW[IDW_HEADER]) {
		return FAIL;
	}

	if (ID_FEATURE_ENABLE == IDPGB[IDB_CRC32_CHECK_IDTABLE]) {
		ulCRC32Result = IDPGL[IDL_IDPAGE_CRC];
		IDPGL[IDL_IDPAGE_CRC] = 0;
		CalcualteCRC32(IDPG_BASE, (U32)(&IDPGL[IDL_IDPAGE_CRC]), IDPG_SIZE);
		if (ulCRC32Result != IDPGL[IDL_IDPAGE_CRC]) {
			M_UART(VUC_, "\nIDPG CRC32 error\n");
			return FAIL;
		}
	}

	return PASS;
}

#if (PS5017_EN)
U8 VUCReadPadpage(FlashAccessInfo_t *pFlashInfo)
{
	U8 ubRet = FAIL;
	U16 uwPadPageIndex;
	U8 ubBackupECC = M_GET_ECC_MODE(), ubLocalLPCRC = 0;
	U8 ubPCARuleMode = (FLH_SLC == gFlhEnv.ulFlashDefaultType.BitMap.CellType) ? COP0_PCA_RULE_0 : COP0_PCA_RULE_2;
	U32 ulSystemLocalPCA;
	PCA_t ulSystemPCA;
	ubRet = VUCReadIdpage(pFlashInfo);

	if (PASS == ubRet) {
		uwPadPageIndex = IDPGW[IDW_PADPAGE_PAGEIDX];
		pFlashInfo->uwPage = uwPadPageIndex;
		ulSystemLocalPCA = M_GET_PCA(0, pFlashInfo->ulBlock, pFlashInfo->uwPage, pFlashInfo->ubFlashCE, pFlashInfo->ubChannel, pFlashInfo->ubPlane, 0, ubPCARuleMode);
		M_FWPCA_SET(ulSystemPCA.ulAll, ulSystemLocalPCA, gPCAInfo.ubMaxZByte, 0, ubLocalLPCRC);
		M_UART(VUC_, "\nPad Page Index:%x", uwPadPageIndex);
		M_UART(VUC_, "\nPad Page Block: %d, Page: %d, CE: %d, CH: %d, Plane: %d", pFlashInfo->ulBlock, pFlashInfo->uwPage, pFlashInfo->ubFlashCE, pFlashInfo->ubChannel, pFlashInfo->ubPlane);

#if (!BURNER_MODE_EN)
		SystemAreaWaitAndLockCOP0();
#endif /* !BURNER_MODE_EN */

		//-------- Change LDPC Mode to 1792 bit --------

		FlaSwitchLDPCHandler(SET_ECC_MODE_2);

		pFlashInfo->ulBufBase = IDPG_BASE;

		FTLCOP0ReadN4kEntry_AOM_INIT_2((FTLCOP0ReadN4kEntryMode_t) {
			R_GETLCA_FROM_L4KTABLE, COP0_R_SYSTEM_INITIAL_SCAN, 1, NULL
		}, ulSystemPCA,  &pFlashInfo->ulBufBase);

		//-------- Restore LDPC Mode --------
		FlaSwitchLDPCHandler(ubBackupECC);
		if (!BURNER_MODE_EN) {
			ErrHandleUnlockCOP0();
		}

		if (SPARE_LCA_CODEBLOCK != gulTieoutLCA[TIEOUT_SINGLE_4K_LCA_OFFSET]) {
			return FAIL;
		}

		if (VUC_PAD_PAGE_MARK != ((PAGE_1_SDLL_NAND_Config_t *)IDPG_BASE)->uoPadPageMark) {
			return FAIL;
		}

		return PASS;
	}
	else {
		M_UART(VUC_, "\nRead ID page fail when reading pad page\n");
		return ubRet;
	}
}
#endif /* (PS5017_EN) */

#if (BURNER_MODE_EN)
void VUC_ISPReadFlash(VUC_OPT_HCMD_PTR_t pCmd)
{
	if (TRUE == gFlhEnv.ulFlashDefaultType.BitMap.btNoSupport) {
		pCmd->ubState = CMD_ERROR;
		gVUCVar.ubVucCmdErrStatus = HOST_ERROR_FEATURE;
		return;
	}

	U8 ubActSlotIdx = 0;
	U8 ubRet = 0;
	U16 uwFWSlotBitMap = 0x01;
	U32 ulPackByteCnt = BYTE_PER_DW * pCmd->vuc_sqcmd.vendor.ISPFlash.ulLength;
	U32 ulByteOffset = pCmd->vuc_sqcmd.vendor.ISPFlash.ulOffset;
	DMACParam_t DMACParam;

	M_UART(VUC_, "\nVUC_ISP_READ_FLASH");

	if (0 == ulByteOffset) {
		ubRet = VendorReadFlash(uwFWSlotBitMap, ubActSlotIdx, &pCmd->vuc_sqcmd.vendor.ISPFlash.ulTotalSize);
	}

	if (ubRet) {
		pCmd->ubState = CMD_ERROR;
	}

	else {
		DMACParam.ulSourceAddr = BURNER_HOST_BIN_FILE_BASE + ulByteOffset;
		DMACParam.ulDestAddr = BURNER_VENDOR_BUF_BASE;
		DMACParam.ul32ByteNum = (ulPackByteCnt >> 5);
		gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
		DMACCopy(DMAC_MODE_NORMAL_QUEUE, &DMACParam, gulDMACDirectWaitDone_Callback, 0);
		while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
			DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
		}
	}
}

U8 ReadCodePointer(FlashAccessInfo_t *pFlaInfo, P4KTableHigh8B_t *pL4KInfo)
{
	U8 ubRet;
	U32 ulCRC32Rst;

	//-------- Change LDPC Mode to 2432 bit --------
	FlaSwitchLDPCHandler(SET_ECC_MODE_7);

	// load Code PTR page
	pFlaInfo->uwPage = IDPGW[IDW_CODEPTR_PAGEIDX];
	pFlaInfo->ulBufBase = CODEPTR_BASE;

	ubRet = BurnerReadSetCop0SQ(pFlaInfo, pL4KInfo);

	if (ubRet) {
		M_UART(VUC_, "Read ERR!\n");
		return DEF_READ_FAIL;
	}

	//-------- Restore LDPC Mode --------
	FlaSwitchLDPCHandler(IDPGB[IDB_LDPC_MODE]);

	ulCRC32Rst = CODEPTRL[CPL_CODEPTR_CRC32];
	CODEPTRL[CPL_CODEPTR_CRC32] = 0;
	CalcualteCRC32(CODEPTR_BASE, (U32)(&CODEPTRL[CPL_CODEPTR_CRC32]), CP_SIZE);
	if (ulCRC32Rst != CODEPTRL[CPL_CODEPTR_CRC32]) {
		M_UART(VUC_, "\nCODE PTR CRC32 error\n");
		return FAIL;
	}
	return PASS;
}

U8 ReadCodeSign_Digest(FlashAccessInfo_t *pFlaInfo, P4KTableHigh8B_t *pL4KInfo, U8 ubMode)
{
	U8 ubRet;

	//-------- Change LDPC Mode to 2432 bit --------
	FlaSwitchLDPCHandler(SET_ECC_MODE_7);

	if (SPARE_FW_CODE_BLOCK_CODE_SIGN_HEADER == ubMode) {
#if BOOTLOADER_EN
		pFlaInfo->uwPage = CODEPTRW[(CP_FWSLOT0_OFFSET + CP_CODESIGNHEADER_PAGE_IDX) >> 1];
		pFlaInfo->ulBufBase = CODESIGN_BASE + BURNER_BOOTLOADER_CODESIGN_SIZE;
#else /* BOOTLOADER_EN */
		pFlaInfo->uwPage = CODEPTRW[(CP_FWSLOT0_OFFSET + CP_CSHPAGE_IDX) >> 1];
		pFlaInfo->ulBufBase = CODESIGN_BASE;
#endif /* BOOTLOADER_EN */
	}
	else if (SPARE_FW_CODE_BLOCK_SIGN == ubMode) {
		pFlaInfo->uwPage = CODEPTRW[(CP_FWSLOT0_OFFSET + CP_SIGNPAGE_IDX) >> 1];
		pFlaInfo->ubFrame = 0;
		pFlaInfo->ulBufBase = READ_BUF_BASE;
	}

	ubRet = BurnerReadSetCop0SQ(pFlaInfo, pL4KInfo);

	//-------- Restore LDPC Mode --------
	FlaSwitchLDPCHandler(IDPGB[IDB_LDPC_MODE]);

	if (ubRet) {
		if (SPARE_FW_CODE_BLOCK_CODE_SIGN_HEADER == ubMode) {
			M_UART(VUC_, "Read CodeSignHeader ERR!\n");
		}
		else if (ubMode == SPARE_FW_CODE_BLOCK_SIGN) {
			M_UART(VUC_, "Read Signature ERR!\n");
		}
		return DEF_READ_FAIL;
	}

	if (SPARE_FW_CODE_BLOCK_CODE_SIGN_HEADER == ubMode) {
#if BOOTLOADER_EN
		memcpy((void *)BURNER_HOST_BIN_FILE_BASE, (void *)(CODESIGN_BASE + BURNER_BOOTLOADER_CODESIGN_SIZE), (CODESIGNB[CSB_DOUBLESIGN] + 1) * CODESIGNHEAD_SIZE);
#else /* BOOTLOADER_EN */
		if (CODESIGNB[CSB_DOUBLESIGN]) {
			memcpy((void *)BURNER_HOST_BIN_FILE_BASE, (void *)CODESIGN_BASE, CODESIGNHEAD_SIZE * 2);
		}
		else {
			memcpy((void *)BURNER_HOST_BIN_FILE_BASE, (void *)CODESIGN_BASE, CODESIGNHEAD_SIZE);
		}
#endif /* BOOTLOADER_EN */
	}
	else if (SPARE_FW_CODE_BLOCK_SIGN == ubMode) {
		if (CODESIGNB[CSB_DOUBLESIGN]) {
			memcpy((void *)(BURNER_HOST_BIN_FILE_BASE + CODESIGNHEAD_SIZE * 2 + (CODESIGNW[CSW_ALL_SECTORCNT] << 9)), (void *)READ_BUF_BASE, SIGNDIGEST_SIZE * 2);
		}
		else {
			memcpy((void *)(BURNER_HOST_BIN_FILE_BASE + CODESIGNHEAD_SIZE + (CODESIGNW[CSW_ALL_SECTORCNT] << 9)), (void *)READ_BUF_BASE, SIGNDIGEST_SIZE);
		}
	}

	return PASS;
}

U8 ReadCodeBody(FlashAccessInfo_t *pFlaInfo, P4KTableHigh8B_t *pL4KInfo, U32 *ulTempAdr)
{
	U8 ubFWSectionIdx, ubRet = 0;
	U16 uwPageCnt, uwSecCnt;
	U32 ulFWSlotBase, ulSectorAdrBase, ulFWSectorBase, ulCRC32Rst;

	if (CODESIGNB[CSB_DOUBLESIGN]) {
		*ulTempAdr = (U32)(BURNER_HOST_BIN_FILE_BASE + CODESIGNHEAD_SIZE * 2);
	}
	else {
		*ulTempAdr = (U32)(BURNER_HOST_BIN_FILE_BASE + CODESIGNHEAD_SIZE);
	}

	ulFWSlotBase = CP_FWSLOT0_OFFSET;
	pFlaInfo->ulBufBase = READ_BUF_BASE;

	//-------- Change LDPC Mode to 1792 bit --------
	FlaSwitchLDPCHandler(IDPGB[IDB_LDPC_MODE]);

	for (ubFWSectionIdx = CODEPTRB[(ulFWSlotBase + CP_FWSTART_SECTION)]; ubFWSectionIdx < MAX_SECTION_NUM; ubFWSectionIdx++) {
		ulSectorAdrBase = *ulTempAdr;
		ulFWSectorBase = ulFWSlotBase + CP_ATCM_SECTION_BASE + CP_FWSECTINFO_SIZE * ubFWSectionIdx;
		pFlaInfo->uwPage = CODEPTRW[(ulFWSectorBase + CP_SECTION_STARTPG_IDX) >> 1];
		pFlaInfo->ubFrame = 0;
		uwPageCnt = CODEPTRW[(ulFWSectorBase + CP_SECTION_PAGE_CNT) >> 1];
		uwSecCnt = CODEPTRW[(ulFWSectorBase + CP_SECTION_SECTOR_CNT) >> 1];
		while (uwPageCnt > 0) {
			ubRet |= BurnerReadSetCop0SQ(pFlaInfo, pL4KInfo);
			if (ubRet) {
				M_UART(VUC_, "Read Code Body error!\n");
				break;
			}
			if (1 == uwPageCnt && uwSecCnt < 8) {
				memcpy((void *)*ulTempAdr, (void *)READ_BUF_BASE, uwSecCnt << 9);
				*ulTempAdr += uwSecCnt << 9;
				break;
			}
			memcpy((void *)*ulTempAdr, (void *)READ_BUF_BASE, 4096);
			*ulTempAdr += 4096;
			uwSecCnt -= 8;
			if (pFlaInfo->ubFrame == ((gFlhEnv.uwPageByteCnt >> 12) - 1)) {
				pFlaInfo->uwPage++;
				pFlaInfo->ubFrame = 0;
				uwPageCnt--;
			}
			else {
				pFlaInfo->ubFrame++;
			}
		}

		if (ubRet) {
			break;
		}

		CalcualteCRC32(ulSectorAdrBase, (U32)&ulCRC32Rst, *ulTempAdr - ulSectorAdrBase);
		if (ulCRC32Rst != CODEPTRL[(ulFWSectorBase + CP_SECTION_CRC32) >> 2]) {
			M_UART(VUC_, "\nSector %b CRC32 error\n", ubFWSectionIdx);
			ubRet = FAIL;
			break;
		}
	}
	return ubRet;
}

U8 VendorReadFlash(U16 uwFWSlotBitMap, U8 ubActSlotIdx, U32 *pulCodeSize)
{
	P4KTableHigh8B_t L4KInfo = {0};
	FlashAccessInfo_t FlaInfo = {0};
	U8 ubCHExistNum, ubInvalidCHMap;
	U8 ubMaxPlane, ubMaxPlaneBit;
	U8 ubRet, ubProgCount, ubIDPGNum;
	U32 ulTempAdr;

	ubCHExistNum = gFlhEnv.ubChannelExistNum + 1;
	ubInvalidCHMap = ~gFlhEnv.ubChannel_Exist_Bit;

	ubIDPGNum = 0;
	ubMaxPlane = gFlhEnv.ubPlanePerTarget / gFlhEnv.ubLUNperTarget;
	ubMaxPlaneBit = FlaCheckLogValue((U32)ubMaxPlane);
	L4KInfo.ulLCA = SPARE_LCA_CODEBLOCK;

	// Scan all CE0 of all CH
	for (FlaInfo.ubChannel = 0; FlaInfo.ubChannel < ubCHExistNum; FlaInfo.ubChannel++) {
		if (BIT0 != ((ubInvalidCHMap >> FlaInfo.ubChannel) & BIT0)) {
			ubProgCount = 0;
			ubRet = 0;
			for (FlaInfo.ulBlock = 0; FlaInfo.ulBlock < (MAX_SCAN_CODEBLK_NUM / ubMaxPlane); FlaInfo.ulBlock++) {
				for (FlaInfo.ubPlane = 0; FlaInfo.ubPlane < ubMaxPlane; FlaInfo.ubPlane++) {
					// Scan IDPG
					ubRet = VUCReadIdpage(&FlaInfo);
					if (ubRet) {
						continue;
					}

					// Read Code Pointer
					ubRet = ReadCodePointer(&FlaInfo, &L4KInfo);
					if (ubRet) {
						continue;
					}

					// Read CodeSignHeader
					ubRet = ReadCodeSign_Digest(&FlaInfo, &L4KInfo, SPARE_FW_CODE_BLOCK_CODE_SIGN_HEADER);
					if (ubRet) {
						continue;
					}

					// Read Code body
					ubRet = ReadCodeBody(&FlaInfo, &L4KInfo, &ulTempAdr);
					if (ubRet) {
						continue;
					}

					// Read Signature
					ubRet = ReadCodeSign_Digest(&FlaInfo, &L4KInfo, SPARE_FW_CODE_BLOCK_SIGN);
					if (ubRet) {
						continue;
					}

					if (CODESIGNB[CSB_DOUBLESIGN]) {
						ulTempAdr += (U32)SIGNDIGEST_SIZE * 2;
					}
					else {
						ulTempAdr += (U32)SIGNDIGEST_SIZE;
					}
					*pulCodeSize = (ulTempAdr - (U32)BURNER_HOST_BIN_FILE_BASE) >> 2;
					M_UART(VUC_, "\nCode size: %x", (*pulCodeSize) << 2);
					return DEF_PROG_SUCCESS;
				}
			}
		}
	}

	return DEF_READ_FAIL;
}
#endif /* BURNER_MODE_EN */
