#ifndef _TT_H_
#define _TT_H_

#include "setup.h"
#include "typedef.h"
#include "aom/aom_api.h"
#include "TT_api.h"

#define TT_CRITICAL_WARNING_STATE_NONE	0
#define TT_CRITICAL_WARNING_STATE_WARNING	1
#define TT_CRITICAL_WARNING_STATE_CRITICAL	2

#define TT_MAX_TMT_TRANSITION_CNT	0xFFFFFFFF

#define TT_DEIVE_LOG_MTQ_DELAY_OFFSET (8)

#define TT_READ_WRITE_CMD_DELAY_UNIT_NUM	(5) //uwDelayReadTimer/uwDelayWriteTimer unit: 500ns, CMD_DELAY_CNT unit: 100ns

#define TT_GET_FLASH_TEMPERATURE_MAX_CE	(MIN (gubCEsPerSuperPage,4))
#define TT_TSB_ZERO_DEGREE_OFFSET	42
#define TT_MICRON_B16_ZERO_DEGREE_OFFSET	37

#define TT_FORMULA_LOWER_OFFSET_DEFAULT_VALUE (5)
#define TT_FORMULA_UPPER_OFFSET_DEFAULT_VALUE (22)
#define TT_FORMULA_CONSTANT_DEFAULT_VALUE (90)

#if (NVME == HOST_MODE)
#define M_TT_DELAY_VALUE(ubMPValue, ubDefaultValue)  ((ubMPValue==0xFFFF) ? (ubDefaultValue) : (ubMPValue))
#define M_TT_CLOCK_VALUE(ubMPValue, ubDefaultValue)  ((ubMPValue==0xFF) ? (ubDefaultValue) : (ubMPValue))
#else /* (NVME == HOST_MODE) */
#define M_TT_DELAY_VALUE(ubMPValue, ubDefaultValue)  (ubDefaultValue)
#define M_TT_CLOCK_VALUE(ubMPValue, ubDefaultValue)  (ubDefaultValue)
#endif /* (NVME == HOST_MODE) */

#if (!BURNER_MODE_EN)
AOM_TT void TTCalculateTime(void);
AOM_TT U8 TTSetClock(TTDelaySettingEnum_t DelaySettingIdx, U8 ubClockIPSelect);  // consider Power state transition time, pls don't change overlay. -YK
AOM_TT U8 TTAdjustClock(TTDelaySettingEnum_t DelaySettingIdx);  // consider Power state transition time, pls don't change overlay. -YK
AOM_TT void TTAdjustArbiter(U8 ubChanged, U16 uwMTQDelayTime, U16 uwWriteDelayTime, U16 uwReadDelayTime, TTMTQHostDelayEnum_t MTQHostDelay);
AOM_TT void TTChangeTMTState(TTTMTEnum_t State);
AOM_TT void TTAdjustment(void);
#endif /* BURNER_MODE_EN */


#endif  /* _TT_H_ */
