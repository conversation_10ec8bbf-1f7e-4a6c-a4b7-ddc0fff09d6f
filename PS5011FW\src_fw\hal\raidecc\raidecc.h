
/****************************************************************************
*
*  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
*  All rights reserved
*
*  The content of this document is confidential and shall be applied
*  subject to the terms and conditions of the license agreement and
*  other applicable laws. Any unauthorized access, use or disclosure
*  of this document is strictly prohibited and may be punishable
*  under laws.
*
*  raidecc.h
*
*
*
****************************************************************************/

#ifndef RAIDECC_H_
#define RAIDECC_H_

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "aom/aom_api.h"
#include "raidecc_reg.h"
#include "aom/aom_api.h"

/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */
#define RAIDECC_INITIAL_BUF_IDX		(0xFF)
#define RAIDECC_DEFAULT_BUF_IDX		(0xFE)
#define RAIDECC_INTERNAL_BUFFER_CNT	(4)


/*
 * ---------------------------------------------------------------------------------------------------
 *  enum
 * ---------------------------------------------------------------------------------------------------
 */


/*
 * ---------------------------------------------------------------------------------------------------
 *  data types
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *   macros
 * ---------------------------------------------------------------------------------------------------
 */



/*
 * ---------------------------------------------------------------------------------------------------
 *   extern global variables
 * ---------------------------------------------------------------------------------------------------
 */


/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */

AOM_RS_2 void RaidECCPreLoad(U8 ubRaidECCTagIdx, U8 ubVirtualPBIdx, U8 ubSpareOffset, U32 ulBufAddrData, U32 ulBufAddrSpare);
#if PS5017_EN
void RaidECCInitReg(void);
#else /* PS5017_EN */
AOM_INIT_2 void RaidECCInitReg(void);
#endif /* PS5017_EN */

AOM_RS_2 U8 RaidECCFindEmptyInternalBuf(void);
AOM_RS_2 void RaidECCSetExternalBufReg(U8 ubExternalBufIdx, U8 ubParityTag, U8 ubVirtualPBIdx);
AOM_RS_2 void RaidECCReBuildParityEncodedCnt(U8 ubTagIdx, U8 ubEncodedCnt);
AOM_RS_2 void RaidECCSetBMUEncodedPB(U8 ubEncodedPBIdx, U8 ubVirtualPBIdx, U8 ubTotalEncodePBCnt);
#endif /* RAIDECC_API_H_ */
