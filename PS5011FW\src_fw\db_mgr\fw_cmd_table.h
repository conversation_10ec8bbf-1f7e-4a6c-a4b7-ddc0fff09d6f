#ifndef _FW_CMD_TABLE_H_
#define _FW_CMD_TABLE_H_

#include "typedef.h"
#include "db_mgr/fw_tagid.h"
#include "aom/aom_api.h"
#include "hal/pic/uart/uart_api.h"

#define CMD_TABLE_NUM		(HW_NUM)

#define SET_VALUE_TRUE		(1)
#define SET_VALUE_FLASE		(0)

#define NOT_THIS_JOB		(0)
#define JOB_NOT_FINISH		(1)
#define JOB_FINISH			(2)

#define CMD_TABLE_SIZE (TAG_NUM + 1)

#define CMD_RESULT_TABLE_SIZE	(32)

typedef enum {
	COP0_CMD_TABLE_ID = 0,
	COP1_CMD_TABLE_ID,
	BMU_CMD_TABLE_ID,
	DMAC_CMD_TABLE_ID,
	XZIP_CMD_TABLE_ID,
	CMD_TABLE_ID_NUM
} CMDTableID_t;

typedef enum {
	U8_COP0_WRITE_JOB_INIT,
	U8_COP0_WRITE_JOB_HOST_WRITE,
	U8_COP0_WRITE_JOB_WRITE_PTE_BIT_MAP
} cop0_write_cmd_job_t;

typedef enum {
	U8_COP0_ERASE_JOB_INIT,
	U8_COP0_ERASE_JOB_ERASE_GR_TARGET
} cop0_erase_cmd_job_t;

typedef enum {
	U8_COP0_READ_JOB_INIT,
	U8_COP0_READ_JOB_LOAD_ALIGNMENT_READ_TO_TEMP_PB
} cop0_read_cmd_job_t;

typedef enum {
	U8_BMU_JOB_INIT,
	U8_BMU_JOB_LOCK_WLB
} bmu_cmd_job_t;

typedef union {
	U32 ulAll;
	struct {
		U32 ubIndex		: 8;
		U32 PTEIndex	: 20;
		U32 : 1;
		U32 btDone		: 1;	// to be deleted
		U32 btKeepTag	: 1;
		U32 btTypeGC	: 1;
	};
} GCScanValidMeta_t;
TYPE_SIZE_CHECK(GCScanValidMeta_t, SIZE_4B);

typedef union {
	U32 ulAll;
	struct {
		U32 ubIndex		: 8;
		U32 PTEIndex	: 20;
		U32 			: 2;
		U32 btBypassScan	: 1;
		U32 btTypeGC	: 1;
	};
} GCLoadPTEMeta_t;
typedef union {
	U32 ulAll;
	struct {
		U32 PTEIndex	: 20;
		U32 : 12;
	};
} ScanPTEMeta_t;
typedef union {
	U32 ulAll;
	struct {
		U32 Index		: 12;
		U32	Zinfo		: 3;
		U32 btIsXZIP	: 1;
		U32 Cnt			: 4;
		U32 ubValidBMP  : 8;
		U32				: 4;
	};
	struct {
		U32 uwPrivate	: 16;
		U32				: 16;
	} ulCallbackData;
} DMACInfo_t;

TYPE_SIZE_CHECK(GCLoadPTEMeta_t, SIZE_4B);

typedef struct {
	U32 ulCmdFuncPTR;
	union {
		U32 ulAll;
		struct { // General
			U16 uwPrivate	: 16;
			U16 Cnt		: 12;	// to be deleted (DMAC OR bitmap)
			U16 btPending	: 1;
			U16 btDone		: 1;	// to be deleted (DMAC OR bitmap)
			U16 btKeepTag	: 1;	// to be deleted (DMAC OR bitmap)
			U16 btTypeGC	: 1;
		};
		struct { // COP0 share tag
			U16 uwPrivate	: 16;
			U16 uwCnt		: 12;	// Expected CQ count
			U16 btPending	: 1;
			U16 btDone		: 1;	// All CQ are done
			U16 btKeepTag	: 1;	// Don't push tag in delegate
			U16 btNoCQCnt	: 1;	// flag for not increasing uwCnt (CQ count)
		} ulFLHInfo;
		struct {
			U32 CTag0		: 7;
			U32 CTag1		: 7;
			U32 CTag2		: 7;
			U32 CTag3		: 7;
			U32 CTagNum		: 3;
			U32 btTypeFUA	: 1;
		} ulFUAInfo;
		GCScanValidMeta_t ulGCScanValid;
		GCLoadPTEMeta_t ulGCLoadPTE;
		ScanPTEMeta_t  ulScanPTEMeta;
		DMACInfo_t ulDMACInfo;
		struct { // COP0 share tag
			U32 ulLockCnt;
		} ulST3CLockInfo;
		struct { // COP0 share tag
			U32 btNeedWaitTrimPMD;
		} ulUpdatePMDInfo;

	} ulData;
#if (RDT_MODE_EN)
	U32 ulPCA;
	void *cb_obj;
#endif /* (RDT_MODE_EN) */
} cmd_table_t;
#if (RDT_MODE_EN)
TYPE_SIZE_CHECK(cmd_table_t, SIZE_16B);
#else /* (RDT_MODE_EN) */
TYPE_SIZE_CHECK(cmd_table_t, SIZE_8B);
#endif /* (RDT_MODE_EN) */

extern cmd_table_t *gpuoCmdTableMgr[CMD_TABLE_ID_NUM];

/**
 * @brief Check Command Done
 *
 * Check command CQ count done.
 *
 * @param ubCmdTableID Table ID as type of command table.
 * @param uwTagID Tag ID as index of command table.
 */
INLINE U8 TagCheckDone(CMDTableID_t ubCmdTableID, U16 uwTagID)
{
	return (U8)(gpuoCmdTableMgr[ubCmdTableID][uwTagID].ulData.btDone);
}

INLINE void TagCOP0SetCmdPending(U16 uwTagID, U8 ubPending)
{
	if ((uwTagID != TAG_ID_ALLOCATE) && (uwTagID != TAG_ID_DEFAULT)) {
		gpuoCmdTableMgr[COP0_CMD_TABLE_ID][uwTagID].ulData.btPending = ubPending;
	}
}


/**
 * @brief Check COP0 Command Done And Release Tag
 *
 * Check COP0 command CQ count done and release tag resource.
 *
 * @param uwTagID Tag ID as index of command table.
 */
INLINE U8 TagCOP0CheckDoneRelease(U16 uwTagID)
{
	if ((uwTagID != TAG_ID_ALLOCATE) && (uwTagID != TAG_ID_DEFAULT)) {
		if (gpuoCmdTableMgr[COP0_CMD_TABLE_ID][uwTagID].ulData.ulFLHInfo.btDone) {
			FTLPushTagPool(COP0_TAG_POOL_ID, uwTagID);
			return TRUE;
		}
		else {
			return FALSE;
		}
	}
	else {
		return TRUE;
	}
}

/**
 * @brief Get Cnt of COP0 Command Table
 *
 * Get Cnt of COP0 command table Tag ID.
 *
 * @param uwTagID Tag ID as index of command table.
 */
INLINE U16 TagCOP0GetCnt(U16 uwTagID)
{
	return gpuoCmdTableMgr[COP0_CMD_TABLE_ID][uwTagID].ulData.ulFLHInfo.uwCnt;
}

/**
 * @brief Get Private Data of Command Table
 *
 * Get private data of command table by table ID and Tag ID.
 *
 * @param ubCmdTableID Table ID as type of command table.
 * @param uwTagID Tag ID as index of command table.
 */
INLINE U16 TagGetPrivate(CMDTableID_t ubCmdTableID, U16 uwTagID)
{
	return gpuoCmdTableMgr[ubCmdTableID][uwTagID].ulData.uwPrivate;
}

/**
 * @brief Get ScanValid Meta of Command Table
 *
 * Get DMAC ScanValid Meta of command table by Tag ID.
 *
 * @param uwTagID Tag ID as index of command table.
 */
INLINE GCScanValidMeta_t TagGetScanValidMeta(U16 uwTagID)
{
	return gpuoCmdTableMgr[DMAC_CMD_TABLE_ID][uwTagID].ulData.ulGCScanValid;
}

/**
 * @brief Get LoadPTE Meta of Command Table
 *
 * Get COP1 LoadPTE Meta of command table by Tag ID.
 *
 * @param uwTagID Tag ID as index of command table.
 */
INLINE GCLoadPTEMeta_t TagGetLoadPTEMeta(U16 uwTagID)
{
	return gpuoCmdTableMgr[COP1_CMD_TABLE_ID][uwTagID].ulData.ulGCLoadPTE;
}

#endif /* _FW_CMD_TABLE_H_ */
