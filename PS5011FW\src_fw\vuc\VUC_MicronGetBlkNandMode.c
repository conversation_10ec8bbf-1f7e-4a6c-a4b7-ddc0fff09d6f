#include "VUC_MicronResponse.h"
#include "hal/dmac/dmac_pop_cmd.h"
#include "table/vbmap/vbmap_api.h"
#include "VUC_MicronGetBlkNandMode.h"
#include "VUC_MicronGetNandErrorRecoveryStatistics.h"
#include "table/rut/rut_api.h"
#include "hal/cop0/cop0_api.h"

#if (VUC_MICRON_NAND_VS_COMMANDS_EN)
void VUCMicronGetBlkNandMode(U32 ulInputPayloadAddr, U32 ulPayloadAddr)
{
#if (!BURNER_MODE_EN)
	U32 ulResponseAddr = ulPayloadAddr + VUC_MICRON_RESPONSE_HEADER_SIZE;
	U8 ubTmp[10] = "";
	GetBlkNandModeInputData_t *pInputData;
	GetBlkNandModeResponseHEADER_t *pResponseHeader;
	DMACParam_t DMACParam;
	U16 uwUnit;
	FlashAccessInfo_t FlaInfo = {0};
	U8 ubBlkNandMode = VUC_BLK_NAND_SLC_MODE;
	U8 ubStaticbt = FALSE;
	U32 *pulRUTL2Ptr = gpulRUT_L2;
	U16 uwSearchUnit;
	U8 ubSearchPBCE, ubPBCE;
	U16 uwBlk;
	U32 ulIdx = 0;

	pResponseHeader = (GetBlkNandModeResponseHEADER_t *)ulPayloadAddr;
	pInputData = (GetBlkNandModeInputData_t *)(ulInputPayloadAddr + VUC_MICRON_GET_BLOCK_NAND_MODE_HEADER_LENGTH);

	DMACParam.DMACSetValue.ulDestAddr = ulPayloadAddr;
	DMACParam.DMACSetValue.ulLowValue = 0;
	DMACParam.DMACSetValue.ulHighValue = 0;
	DMACParam.DMACSetValue.ul32ByteNum = SIZE_IN_32B(SECTOR_SIZE);
	DMACParam.DMACSetValue.btValidate = FALSE;
	gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
	DMACSetValue(&DMACParam, gulDMACDirectWaitDone_Callback, 0);
	while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
		DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
	}

	pResponseHeader->ubResponseHeaderFormatVersion = VUC_MICRON_RESPONSE_HEADER_FORMAT_VERSION_0;
	pResponseHeader->ubResponseDataFotmatVersion = VUC_MICRON_RESPONSE_FORMAT_JSON;
	pResponseHeader->uwCMDClass = VUC_MICRON_NAND_VS_COMMANDS;
	pResponseHeader->uwCMDCode = VUC_MICRON_GET_BLOCK_NAND_MODE;
	pResponseHeader->uwStatus = 0;
	pResponseHeader->ulDataPayloadSize = SECTOR_SIZE;

	FlaInfo.ubFlashCE = pInputData->uwCE;
	FlaInfo.ubChannel = pInputData->uwCH;
	FlaInfo.ubPlane = (U8)(pInputData->uwBlk & gubBurstsPerBankMask);
	FlaInfo.ubLUN = (U8)(pInputData->uwLUN);
	FlaInfo.ulBlock = (pInputData->uwBlk >> gubBurstsPerBankLog) & gPCARule_Block.ulMask;
	uwSearchUnit = (U16)FlaInfo.ulBlock;
	M_RUT_GEN_PBCE(ubSearchPBCE, 0, FlaInfo.ubLUN, FlaInfo.ubPlane, FlaInfo.ubFlashCE * gFlhEnv.ubChannelExistNum + FlaInfo.ubChannel);

	while (pulRUTL2Ptr[ulIdx] != 0xFFFFFFFF) {
		uwBlk = RUTGetValueByParsingRUTL2(pulRUTL2Ptr[ulIdx], RUT_L2_PB_NUM_RULE);
		ubPBCE = (pulRUTL2Ptr[ulIdx] & RUT_L2_RULE_PBCE_MASK) >> RUT_L2_RULE_PBCE_START_BIT;
		if ((uwBlk == uwSearchUnit) && (ubPBCE == ubSearchPBCE)) {
			uwSearchUnit = RUTGetValueByParsingRUTL2(pulRUTL2Ptr[ulIdx], RUT_L2_VB_NUM_RULE);
			break;
		}
		ulIdx ++;
	}

	for (uwUnit = 0; uwUnit < MAX_UNIT; uwUnit++) {
		if ( gpuwVBRMP[uwUnit].B.ToRUT_Unit == uwSearchUnit) {
			break;
		}
	}
	M_FW_ASSERT(ASSERT_VUC_0x0ABB, MAX_UNIT != uwUnit);

	if (gpulVC[uwUnit].B.btStatic) {
		ubStaticbt = TRUE;
	}

	U8 ubIsSLCMode = FALSE;
	if (gpuwVBRMP[uwUnit].B.btIsSLCMode | gpuwVBRMP[uwUnit].B.btIsD1) {
		ubIsSLCMode = TRUE;
	}

	if (ubIsSLCMode) {
		ubBlkNandMode = VUC_BLK_NAND_SLC_MODE;
	}
	else {
		switch (gFlhEnv.ulFlashDefaultType.BitMap.CellType) {
		case FLH_QLC:
			ubBlkNandMode = VUC_BLK_NAND_QLC_MODE;
			break;
		case FLH_TLC:
			ubBlkNandMode = VUC_BLK_NAND_TLC_MODE;
			break;
		case FLH_MLC:
			ubBlkNandMode = VUC_BLK_NAND_MLC_MODE;
			break;
		case FLH_SLC:
			ubBlkNandMode = VUC_BLK_NAND_SLC_MODE;
			break;
		default:
			M_FW_ASSERT(ASSERT_VUC_0x0ABC, FALSE);
			break;
		}
	}

	VUCMyStrcat((void *)ulResponseAddr, "{\n\t");
	VUCMyStrcat((void *)ulResponseAddr, "\"structVer\":0,\n\t");

	VUCMyStrcat((void *)ulResponseAddr, "\"Block\":");
	VUCMyitoa(ubBlkNandMode, (void *)ubTmp, DECIMAL);
	VUCMyStrcat((void *)ulResponseAddr, (void *)ubTmp);
	VUCMyStrcat((void *)ulResponseAddr, ",\n\t");

	VUCMyStrcat((void *)ulResponseAddr, "\"Static\":");
	VUCMyitoa(ubStaticbt, (void *)ubTmp, DECIMAL);
	VUCMyStrcat((void *)ulResponseAddr, (void *)ubTmp);

	VUCMyStrcat((void *)ulResponseAddr, "\n}");
#endif /*(!BURNER_MODE_EN)*/
}
#endif /* VUC_MICRON_NAND_VS_COMMANDS_EN */

