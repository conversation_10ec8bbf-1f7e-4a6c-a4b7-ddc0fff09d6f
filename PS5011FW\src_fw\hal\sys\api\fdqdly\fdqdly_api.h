#ifndef	_FDQDLY_API_H_
#define	_FDQDLY_API_H_

#include "hal/sys/reg/sys_pd1_reg.h"
#include "setup.h"

#if PS5017_EN // E17 porting : move to FIP, see M_FIP_SET_FDAT_TX_DELAY() & M_FIP_SET_FDAT_RX_DELAY()
#define FDQDLY_DQ_SIZE 						M_FW_TODO()
#define FDQDLY_FDAT_GROUP_R32_REG_OFFSET	M_FW_TODO()
#define M_FDQDLY_SET_FDAT0_3_GROUP_DELAY(group,x)			M_FW_TODO()
#define M_FDQDLY_SET_FDAT4_7_GROUP_DELAY(group,x)			M_FW_TODO()
#define M_FDQDLY_SET_FDAT_GROUP_TX_DELAY(group,x,y)			M_FW_TODO()
#define M_FDQDLY_SET_FDAT_GROUP_RX_DELAY(group,x,y)			M_FW_TODO()
#define	M_FDQDLY_GET_FDAT_GROUP_DELAY(group, low, high)		M_FW_TODO()

#if DQ_PER_BIT_TRAIN_EN
#define M_FDQDLY_FLASH_DQ_DELAY_EN()		M_FW_TODO()
#else /* DQ_PER_BIT_TRAIN_EN */
#define M_FDQDLY_FLASH_DQ_DELAY_EN()
#endif /* DQ_PER_BIT_TRAIN_EN */
#define M_FDQDLY_FLASH_DQ_DELAY_DIS()		M_FW_TODO()
#else /* PS5017_EN */
#define FDQDLY_DQ_SIZE 				(8)
#define FDQDLY_FDAT_GROUP_R32_REG_OFFSET		(2)

#define M_FDQDLY_SET_FDAT0_3_GROUP_DELAY(group,x)			(R32_SYS1_FDQDLY[R32_SYS1_PAD_FDAT0_3_GROUP0_15_TXRX_DLY0+FDQDLY_FDAT_GROUP_R32_REG_OFFSET*group] = x)

#define M_FDQDLY_SET_FDAT4_7_GROUP_DELAY(group,x)			(R32_SYS1_FDQDLY[R32_SYS1_PAD_FDAT4_7_GROUP0_15_TXRX_DLY0+FDQDLY_FDAT_GROUP_R32_REG_OFFSET*group] = x)

#define M_FDQDLY_SET_FDAT_GROUP_TX_DELAY(group,x,y) (R8_SYS1_FDQDLY[R8_SYS1_PAD_FDAT0_3_GROUP0_TXRX_DLY + FDQDLY_DQ_SIZE * group + y] = ((x &CR_FDAT_GROUP0_15_TX_DLY_3_0_MASK) << CR_FDAT_GROUP0_15_TX_DLY_3_0_SHIFT) | (R8_SYS1_FDQDLY[R8_SYS1_PAD_FDAT0_3_GROUP0_TXRX_DLY + FDQDLY_DQ_SIZE * group + y] & SYS1_FDQDLY_RX_MASK))
#define M_FDQDLY_SET_FDAT_GROUP_RX_DELAY(group,x,y) (R8_SYS1_FDQDLY[R8_SYS1_PAD_FDAT0_3_GROUP0_TXRX_DLY + FDQDLY_DQ_SIZE * group + y] = ((x &CR_FDAT_GROUP0_15_RX_DLY_3_0_MASK) << CR_FDAT_GROUP0_15_RX_DLY_3_0_SHIFT) | (R8_SYS1_FDQDLY[R8_SYS1_PAD_FDAT0_3_GROUP0_TXRX_DLY + FDQDLY_DQ_SIZE * group + y] & SYS1_FDQDLY_TX_MASK))

#define	M_FDQDLY_GET_FDAT_GROUP_DELAY(group, low, high)	do { \
															(low) = R32_SYS1_FDQDLY[R32_SYS1_PAD_FDAT0_3_GROUP0_15_TXRX_DLY0 + (((group) * 8) >> 2)]; \
															(high) = R32_SYS1_FDQDLY[R32_SYS1_PAD_FDAT4_7_GROUP0_15_TXRX_DLY0 + (((group) * 8) >> 2)]; \
														} while(0)
#if DQ_PER_BIT_TRAIN_EN
#define M_FDQDLY_FLASH_DQ_DELAY_EN()		do{\
										R8_SYS1_FDQDLY[R8_SYS1_CR_FDQDLY_EN]|=CR_FDQDLY_EN_BIT;\
										}while(0)
#else /* DQ_PER_BIT_TRAIN_EN */
#define M_FDQDLY_FLASH_DQ_DELAY_EN()
#endif /* DQ_PER_BIT_TRAIN_EN */
#define M_FDQDLY_FLASH_DQ_DELAY_DIS()		do{\
										R8_SYS1_FDQDLY[R8_SYS1_CR_FDQDLY_EN]&=(~CR_FDQDLY_EN_BIT);\
										}while(0)
#endif /* PS5017_EN */
#endif/* _FDQDLY_API_H_ */
