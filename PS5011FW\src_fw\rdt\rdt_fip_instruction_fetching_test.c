/*
 * rdt_fip_instruction_fetching_test.c
 *
 *  Created on: 2020¦~11¤ë6¤é
 *      Author: <PERSON><PERSON>
 */

#include "rdt/rdt_fip_instruction_fetching_test.h"

#if RDT_MODE_EN
#include "burner/Burner_api.h"
#include "vuc/VUC_api.h"
#include <stdlib.h>

/*
 * 	Function Name: rdt_api_fpu_timeout_diagnosis
 * 	Function Description:
 * 		Diagnose the main cause of FPU timeout
 * 	Parameter:
 * 		U8 ubCh: Target Channel
 * 	Return:
 * 		None
 */
void rdt_api_fpu_timeout_diagnosis(U8 ubCh)
{
	M_UART(RDT_TEST_, "\n[RDT] FPU Timeout");
	U32 failinfo = 0;
	// check crossover circuit fail case
	failinfo = rdt_check_freq_crossover_fail(ubCh);
	if (failinfo) {
		M_UART(RDT_TEST_, "\n[RDT] Frequency Crossover Circuit Fail !!\n");
		rdt_api_pio_force_write_fail_log(&gRdtApiStruct, RDT_PIO_FORCE_WR_LOG_FREQ_CROSSOVER_ERROR, failinfo, TRUE);
	}
	// Maybe more other fail cases...

	// Undefined cause: general fail case [FPU_TIMEOUT_ERROR]
	M_UART(RDT_TEST_, "\n[RDT] Undefined Cause !!\n");
	rdt_api_pio_force_write_fail_log(&gRdtApiStruct, RDT_PIO_FORCE_WR_LOG_FPU_TIMEOUT_ERROR, R32_FCTL_CH[ubCh][R32_FCTL_FPU_TRIG], TRUE);
}

/*
 * 	Function Name: rdt_check_debug_sets_OR
 * 	Function Description:
 * 		Put FCTL debug command to target channel, and check respond value to expecting result
 * 		Once one of the condition in debug sets is matched, return TRUE; otherwise, return FALSE
 * 	Parameter:
 * 		U8 ubCh: FCTL channel to put debug
 * 		FLP_DBG_SET debug_set: debug set to execute
 * 	Return:
 * 		TRUE(1): One of the condition matches
 * 		FALSE(0): None of the condition matches
 */
BOOL rdt_check_debug_sets_OR(U8 ubCh, FLP_DBG_SET debug_set[], U8 size, BOOL print)
{
	U32 response;
	BOOL result = FALSE;
	for (U8 i = 0; i < size; i++) {
		rdt_put_command_FCTL_DBF_INF(ubCh, debug_set[i].cmd);
		response = R32_FCTL_CH[ubCh][R32_FCTL_DBG_INF] & debug_set[i].mask;
		debug_set[i].response = response;
		if (print) {
			M_UART(RDT_TEST_, "\n[RDT] Cmd: %x, Exp: %x, Resp: %x", debug_set[i].cmd, debug_set[i].expect, R32_FCTL_CH[ubCh][R32_FCTL_DBG_INF]);
		}

		switch (debug_set[i].condition) {
		case EQUAL:
			if (debug_set[i].expect == response) {
				result = TRUE;
			}
			break;
		case NOT_EQUAL:
			if (debug_set[i].expect != response) {
				result = TRUE;
			}
			break;
		case GREATER:
			if (debug_set[i].expect > response) {
				result = TRUE;
			}
			break;
		case SMALLER:
			if (debug_set[i].expect < response) {
				result = TRUE;
			}
			break;
		case GREATER_EQUAL:
			if (debug_set[i].expect <= response) {
				result = TRUE;
			}
			break;
		case SMALLER_EQUAL:
			if (debug_set[i].expect >= response) {
				result = TRUE;
			}
			break;
		default:
			;
		}
	}
	return result;
}

/*
 * 	Function Name: rdt_check_debug_sets_AND
 * 	Function Description:
 * 		Put FCTL debug command to target channel, and check respond value to expecting result
 * 		Every condition in debug sets are matched, return TRUE; otherwise, return FALSE
 * 	Parameter:
 * 		U8 ubCh: FCTL channel to put debug
 * 		FLP_DBG_SET debug_set: debug set to execute
 * 	Return:
 * 		TRUE(1): All conditions match
 * 		FALSE(0): One of the condition doesn't match
 */
BOOL rdt_check_debug_sets_AND(U8 ubCh, FLP_DBG_SET debug_set[], U8 size, BOOL print)
{
	U32	response;
	BOOL result = TRUE;

	for (U8 i = 0; i < size; i++) {
		rdt_put_command_FCTL_DBF_INF(ubCh, debug_set[i].cmd);
		response = R32_FCTL_CH[ubCh][R32_FCTL_DBG_INF] & debug_set[i].mask;
		debug_set[i].response = response;
		if (print) {
			M_UART(RDT_TEST_, "\n[RDT] Cmd: %x, Exp: %x, Resp: %x", debug_set[i].cmd, debug_set[i].expect, R32_FCTL_CH[ubCh][R32_FCTL_DBG_INF]);
		}

		switch (debug_set[i].condition) {
		case EQUAL:
			if (!(debug_set[i].expect == response)) {
				result = FALSE;
			}
			break;
		case NOT_EQUAL:
			if (!(debug_set[i].expect != response)) {
				result = FALSE;
			}
			break;
		case GREATER:
			if (!(debug_set[i].expect > response)) {
				result = FALSE;
			}
			break;
		case SMALLER:
			if (!(debug_set[i].expect < response)) {
				result = FALSE;
			}
			break;
		case GREATER_EQUAL:
			if (!(debug_set[i].expect <= response)) {
				result = FALSE;
			}
			break;
		case SMALLER_EQUAL:
			if (!(debug_set[i].expect >= response)) {
				result = FALSE;
			}
			break;
		default:
			;
		}
	}
	return result;
}

/*
 * 	Function Name: rdt_put_command_FCTL_DBF_INF
 * 	Function Description:
 * 		Put debug command into FCTL debug port on target channel
 * 	Parameter:
 * 		U8 ubCh: target channel
 * 		U32 cmd: FCTL debug command
 * 	Return:
 * 		None
 */
void rdt_put_command_FCTL_DBF_INF(U8 ubCh, U32 cmd)
{
	R32_FCTL_CH[ubCh][R32_FCTL_DBG_INF] = cmd;
	M_RTT_IDLE_US(5);	// wait for response
}

/*
 *  Function Name: rdt_check_freq_crossover_fail
 *  Function Description:
 *  	Type in debug port to target channel on offset 0xFC, and read the return value.
 *  	If all of the following cases are TRUE, Frequency Cross Talk failure is happened.
 *  	1. Input = 0xF0004; Return[15:12] and [31:28] != 0x5
 *  	2. Input = 0xF0005; Return[15:12] and [31:28] != 0x5
 *  	3. Input = 0xF0000; Return[7:0] != 0x50
 *  Parameter:
 *  	None
 *  Return Value:
 *  	U32 failinfo: return fail info if freq_crossover fail; otherwise return 0x00000000
 *  	4Byte[0:1] => Channel
 *  	4Byte[2:3] => debug_sets_3->respond[7:0]
 *  	4Byte[4]   => debug_sets_2->respond[15:12]
 *  	4Byte[5]   => debug_sets_2->respond[31:28]
 *  	4Byte[6]   => debug_sets_1->respond[15:12]
 *  	4Byte[7]   => debug_sets_1->respond[31:28]
 */
U32 rdt_check_freq_crossover_fail(U8 ubCh)
{
	U32 failinfo = 0x00000000;
	BOOL result = FAIL;

	// These debug sets are referenced from HW team
	FLP_DBG_SET debug_sets_1[] = {
		{0x000F0004, 0x0000F000, 0x00005000, NOT_EQUAL, 0x00000000},
		{0x000F0004, 0xF0000000, 0x50000000, NOT_EQUAL, 0x00000000},
	};

	FLP_DBG_SET debug_sets_2[] = {
		{0x000F0005, 0x0000F000, 0x00005000, NOT_EQUAL, 0x00000000},
		{0x000F0005, 0xF0000000, 0x50000000, NOT_EQUAL, 0x00000000},
	};

	FLP_DBG_SET debug_sets_3[] = {
		{0x000F0000, 0x000000FF, 0x00000050, NOT_EQUAL, 0x00000000},
	};

	if (!rdt_check_debug_sets_AND(ubCh, debug_sets_1, sizeof(debug_sets_1) / sizeof(FLP_DBG_SET), TRUE)) {
		result = PASS;
	}
	if (!rdt_check_debug_sets_AND(ubCh, debug_sets_2, sizeof(debug_sets_2) / sizeof(FLP_DBG_SET), TRUE)) {
		result = PASS;
	}
	if (!rdt_check_debug_sets_AND(ubCh, debug_sets_3, sizeof(debug_sets_3) / sizeof(FLP_DBG_SET), TRUE)) {
		result = PASS;
	}

	// All condition matched, diagnosed as Frequency Crossover Circuit fail
	// customize fail info from debug set
	if (result == FAIL) {
		failinfo |= ubCh;
		failinfo |= (debug_sets_1[0].response << (3 * 4));
		failinfo |= (debug_sets_1[1].response >> (0 * 4));
		failinfo |= (debug_sets_2[0].response << (1 * 4));
		failinfo |= (debug_sets_2[1].response >> (2 * 4));
		failinfo |= (debug_sets_3[0].response << (2 * 4));
	}

	return failinfo;
}

void rdt_instruction_fetch_test_backup(U8 ubCh, U32 *_MAP, U32 *_INT, U32 *_FPU, U8 *_LDPC, U32 *_CHNL)
{
	*_LDPC = (U8)(R32_FCON[R32_FCON_LDPC_CFG] & LDPC_MODE_MASK);
	*_FPU = R32_FCTL_CH[ubCh][R32_FCTL_FPU_ENTRY];
	*_MAP = R32_FCTL_CH[ubCh][R32_FCTL_MAP_CFG];
	*_INT = R32_FCTL_CH[ubCh][R32_FCTL_INT_CFG];
	*_CHNL = R32_FCTL_CH[ubCh][R32_FCTL_CHNL_SET];

	R32_FCTL_CH[ubCh][R32_FCTL_MAP_CFG] = 0;
	R32_FCTL_CH[ubCh][R32_FCTL_INT_CFG] = 0;
	R32_FCTL_CH[ubCh][R32_FCTL_ECC_CFG] &= CLR_LDPC_COR_DIS;
	R32_FCTL_CH[ubCh][R32_FCTL_CHNL_SET] |= SET_CONV_BYPASS_EN;
	R32_FCTL_CH[ubCh][R32_FCTL_CHNL_SET] |= SET_INV_BYPASS_EN;
}

void rdt_instruction_fetch_test_restore(U8 ubCh, U32 *_MAP, U32 *_INT, U32 *_FPU, U8 *_LDPC, U32 *_CHNL)
{
	R32_FCTL_CH[ubCh][R32_FCTL_FPU_ENTRY] = *_FPU;
	R32_FCTL_CH[ubCh][R32_FCTL_MAP_CFG] = *_MAP;
	R32_FCTL_CH[ubCh][R32_FCTL_INT_CFG] = *_INT;
	R32_FCTL_CH[ubCh][R32_FCTL_ECC_CFG] |= SET_LDPC_COR_EN;
	M_SET_ECC_MODE(*_LDPC);
	R32_FCTL_CH[ubCh][R32_FCTL_CHNL_SET] = *_CHNL;
}

void rdt_clock_restore(U32 ulFLHClock)
{
	gFlhEnv.ubTargetFlashClock = ulFLHClock;
	gFlhEnv.ubCurrentFlashClock = ulFLHClock;

	U8 ubFLHClock;
	switch (gFlhEnv.ubTargetFlashClock) {
	case FIP_FLASH_CLOCK_10MHZ:
		ubFLHClock = CLOCK_FLH_IF_10p4;
		break;
	case FIP_FLASH_CLOCK_33MHZ:
		ubFLHClock = CLOCK_FLH_IF_33p3;
		break;
	case FIP_FLASH_CLOCK_41P7MHZ:
		ubFLHClock = CLOCK_FLH_IF_41p7;
		break;
	case FIP_FLASH_CLOCK_100MHZ:
		ubFLHClock = CLOCK_FLH_IF_200;
		break;
	case FIP_FLASH_CLOCK_200MHZ:
		ubFLHClock = CLOCK_FLH_IF_400;
		break;
	case FIP_FLASH_CLOCK_266MHZ:
		ubFLHClock = CLOCK_FLH_IF_533;
		break;
	case FIP_FLASH_CLOCK_333MHZ:
		ubFLHClock = CLOCK_FLH_IF_667;
		break;
	case FIP_FLASH_CLOCK_400MHZ:
		ubFLHClock = CLOCK_FLH_IF_800;
		break;
	case FIP_FLASH_CLOCK_600MHZ:
		ubFLHClock = CLOCK_FLH_IF_1200;
		break;
	case FIP_FLASH_CLOCK_700MHZ:
		ubFLHClock = CLOCK_FLH_IF_1400;
		break;
	default:
		ubFLHClock = CLOCK_FLH_IF_533;
	};

	M_UART(RDT_DBG_, "\nRestore clock to %b ", ubFLHClock);
	ClockSwitchModeAndACTiming(ubFLHClock);
	FlaMDLLTracking(ENABLE);
}

/*
 * 	Function Name: rdt_fip_instruction_fetching_test
 * 	Parameters:
 * 		None
 * 	Description:
 * 		Compare the command triggered with the actually executed command.
 * 		Make sure instruction firstly stored in IRAM will not be modified through bus transmission.
 * 	Return Value:
 * 		PASS(0): All comparison matched
 * 		FAIL(1): Command fail to match
 *
 */
BOOL rdt_fip_instruction_fetching_test(RDT_API_STRUCT_PTR rdt)
{

	M_UART(RDT_TEST_, "\n\t[FIP] Instruction fetching test ");

	U8	ubCh, rand_int;
	U8 	ulLDPCModebackUp = 0;
	U32 ulBackUpMapCfg = 0, ulBackupIntCfg = 0, ulBackupFPU = 0, ulBackupINTVCT, ulBackupADRGEN, ulBackupCHNL;
	U16 guwFPUContent[FPU_TX_CNT] = { FPU_CMD(0x00), FPU_CMD(0x30), FPU_CMD(0x80), FPU_ADR(5), FPU_DLY(0x00) };
	U16 content_tx[INS_TX_CNT] = { 0x0000, 0x0000, 0x0000, FPU_END };
	U16 content_rx[INS_RX_CNT] = { FPU_ADR_GEN, FPU_END };
	U16 *content;
	FLP_DBG_SET debug_sets[] = {
		{0x000F0004, 0xFFFFFFFF, 0x00000000, EQUAL, 0x00000000},
		{0x000F0005, 0xFFFFFFFF, 0x00000000, EQUAL, 0x00000000},
	};

	U32 failinfo = 0;
#if (HOST_MODE == USB)
	U8 ubOriginalVoltage = rdt_api_read_voltage_setting() >> COREPOWER_SEL_SHIFT;
#endif
	U32 ulOriginalFlashClock = gFlhEnv.ubCurrentFlashClock;
	M_UART(RDT_DBG_, "\nOriginalFlashClock = %b, switch to %b", ulOriginalFlashClock, FIP_FLASH_CLOCK_700MHZ);
	gFlhEnv.ubTargetFlashClock = FIP_FLASH_CLOCK_700MHZ;
	gFlhEnv.ubCurrentFlashClock = gFlhEnv.ubTargetFlashClock;

	//	{
	//		R32_SYS0_MISC_CTRL[SYS0L_HW_DBG_CTRL_PD0] |= 0x00000010;
	//		R32_SYS0_MISC_CTRL[SYS0L_HW_DBG_CTRL_PD0] |= 0x00070000;
	//		R32_SYS0_MISC_CTRL[SYS0L_HW_DBG_CTRL_PD0] |= 0xFF000000;
	//		UartPrintf("\nReady!!!!");
	//		while(0);
	//	}

	// Step 1: Increase FIP clock rate
	// increase the core voltage
#if HOST_MODE == SATA
	if (!rdt->test_in_normal_power_mode) {
		rdt_SATA_voltage_setting(1);
	}
#elif (HOST_MODE == USB)
	if (!rdt->test_in_normal_power_mode) {
		rdt_api_voltage_setting(rdt, COREPOWER_975mV);
	}
#endif

	ClockSwitchModeAndACTiming(CLOCK_FLH_IF_1400);
	FlaMDLLTracking(ENABLE);

#if HOST_MODE == SATA
	if (!rdt->test_in_normal_power_mode) {
		rdt_SATA_voltage_setting(0);
	}
#elif (HOST_MODE == USB)
	if (!rdt->test_in_normal_power_mode) {
		rdt_api_voltage_setting(rdt, ubOriginalVoltage);//COREPOWER_875mV
	}
#endif


	// Step 2: select a channel
	for (ubCh = 0; ubCh < gFlhEnv.ubChannelExistNum; ubCh++) {
		// Step 3: Force UNC & enable ADR GEN Stop
		R32_FCTL_CH[ubCh][R32_FCTL_UNC_CFG] |= AUTO_GENUNC_DMA_R;
		ulBackupADRGEN = R32_FCON[R32_FCON_ADR_GEN_STOP];
		R32_FCON[R32_FCON_ADR_GEN_STOP] |= FTA_PIP_EN;
		// Step 4: Change Error Target CPU
		ulBackupINTVCT = R32_FCTL_CH[ubCh][R32_FCTL_INT_VCT];
		R32_FCTL_CH[ubCh][R32_FCTL_INT_VCT] &= ~NORMAL_CPU_SHIFT_MASK; // Clear Normal Target
		R32_FCTL_CH[ubCh][R32_FCTL_INT_VCT] &= ~ERROR_CPU_SHIFT_MASK; // Clear Error Target
		R32_FCTL_CH[ubCh][R32_FCTL_INT_VCT] |= SET_NORMAL_CPU0; // Error Target to CPU0
		R32_FCTL_CH[ubCh][R32_FCTL_INT_VCT] |= SET_ERROR_CPU0; // Normal Target to CPU0

		for (U8 opt = 0; opt < 2; opt++) {
			M_UART(RDT_DBG_, "\n\t[FIP] Channel %b %s :", ubCh, (opt == Tx) ? "Tx" : "Rx");

			for (U8 i = 0; i < INSTRUCTIONS_PER_ROUND; i++) {
				// Step 4: select a set of FPU command
				switch (opt) {
				case Tx:
					for (U8 k = 0; k < INS_TX_CNT - 1; k++) {
						srand(M_GET_FW_TIMER() + i + k);
						rand_int = (U8) rand() % FPU_TX_CNT;
						content_tx[k] = guwFPUContent[rand_int];
						if (content_tx[k] == FPU_DLY(0x00)) {
							srand(M_GET_FW_TIMER());
							content_tx[k] |= (U16) rand() % RANDOM_DLY;
						}
					}
					content = content_tx;
					memcpy( (void *)(FLASH_IRAM_ADDRESS + FPU_NCS_OFF), (void *) content, sizeof(content_tx) );
					debug_sets[0].expect = (((U32)content[2] << 16) | content[3]);
					debug_sets[1].expect = (((U32)content[0] << 16) | content[1]);
					//UartPrintf("\n %x, %x, %x, %x", content[0], content[1], content[2], content[3]);
					break;
				case Rx:
					content = content_rx;
					memcpy( (void *)(FLASH_IRAM_ADDRESS + FPU_NCS_OFF), (void *) content, sizeof(content_rx) );
					debug_sets[0].expect = (((U32)content[0] << 16) | content[1]);
					debug_sets[1].condition = IGNORE;
					//UartPrintf("\n %x, %x", content[0], content[1]);
					break;
				}

				// Step 5: backup and reset channel register setting
				rdt_instruction_fetch_test_backup(ubCh, &ulBackUpMapCfg, &ulBackupIntCfg, &ulBackupFPU, &ulLDPCModebackUp, &ulBackupCHNL);

				// Step 6: Set FPU command
				R32_FCTL_CH[ubCh][R32_FCTL_FPU_ENTRY] = (IRAM_BASE + FPU_NCS_OFF);

				// Step 7: trigger FPU command
				R32_FCTL_CH[ubCh][R32_FCTL_FPU_TRIG]  |= FPU_TRIGGER_BIT;
				FlaCheckFPUBusy(ubCh, GERNAL_TIMEOUT_THRESHOLD);

				// Step 8: wait until channel is ready
				while ((R32_FCTL_CH[ubCh][R32_FCTL_RBY_INF] & FLH_CE_RBY_AND_SGN_BIT) == 1) {}
				while ((R32_FCTL_CH[ubCh][R32_FCTL_RBY_INF] & FLH_CE_RBY_AND_SGN_BIT) == 0) {}
				IdlePC(300);

				// Step 9: restore all register setting
				rdt_instruction_fetch_test_restore(ubCh, &ulBackUpMapCfg, &ulBackupIntCfg, &ulBackupFPU, &ulLDPCModebackUp, &ulBackupCHNL);

				// Step 10: Compare instruction
				if (!rdt_check_debug_sets_AND(ubCh, debug_sets, sizeof(debug_sets) / sizeof(FLP_DBG_SET), FALSE)) {
					R32_FCTL_CH[ubCh][R32_FCTL_UNC_CFG] &= ~AUTO_GENUNC_DMA_R; // disable UNC
					M_UART(RDT_TEST_, "Instruction Compare FAIL (%b)", i);

					debug_sets[0].response &= 0xF000F000;
					debug_sets[1].response &= 0xF000F000;
					failinfo |= (debug_sets[1].response << (3 * 4));
					failinfo |= (debug_sets[1].response >> (0 * 4));
					failinfo |= (debug_sets[0].response << (1 * 4));
					failinfo |= (debug_sets[0].response >> (2 * 4));
					failinfo &= 0xFFFFFF00;
					failinfo |= BIT(4);
					failinfo |= ubCh;

					rdt_api_pio_force_write_fail_log(&gRdtApiStruct, RDT_PIO_FORCE_WR_LOG_FREQ_CROSSOVER_ERROR, failinfo, TRUE);
					return FAIL;
				}
			}
			M_UART(RDT_DBG_, "PASS");
		}

		// Step 11: Disable UNC
		R32_FCTL_CH[ubCh][R32_FCTL_UNC_CFG] &= ~AUTO_GENUNC_DMA_R;
		// Step 12: Resume Interrupt Target & ADR GEN
		R32_FCTL_CH[ubCh][R32_FCTL_INT_VCT] = ulBackupINTVCT;
		R32_FCON[R32_FCON_ADR_GEN_STOP] = ulBackupADRGEN;
	}

	// increase the core voltage
#if HOST_MODE == SATA
	if (!rdt->test_in_normal_power_mode) {
		rdt_SATA_voltage_setting(1);
	}
#elif (HOST_MODE == USB)
	if (!rdt->test_in_normal_power_mode) {
		rdt_api_voltage_setting(rdt, COREPOWER_975mV);
	}
#endif

	// Step 13: Resume Clock Rate
	rdt_clock_restore(ulOriginalFlashClock);
#if HOST_MODE == SATA
	if (!rdt->test_in_normal_power_mode) {
		rdt_SATA_voltage_setting(0);
	}
#elif (HOST_MODE == USB)
	if (!rdt->test_in_normal_power_mode) {
		rdt_api_voltage_setting(rdt, ubOriginalVoltage);//COREPOWER_875mV
	}
#endif

	return PASS;
}

#endif // RDT_MODE_EN

