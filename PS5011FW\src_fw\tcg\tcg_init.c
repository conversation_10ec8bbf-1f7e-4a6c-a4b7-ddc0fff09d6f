#include "tcg.h"
#include "tcg_inline.h"
#include "hal/bmu/bmu_api.h"
#include "nvme_api/nvme/shr_hal_nvme_api.h"
#include "common/fw_common.h"
#include "hal/pic/uart/uart_api.h"
#include "ftl/ftl.h"
#include "table/initinfo_vt/initinfo_vt.h"
#include "table/sys_block/sys_block_api.h"
#include "vuc/VUC_api.h"
#if (TCG_EN && !BURNER_MODE_EN && !RDT_MODE_EN && !LPM3_LOADER && !BOOTLOADER_MODE_EN)
U8 TcgCheckOpalFlashTable(void)
{
	LoadTableState_t LoadTableState = gTcg.pAllFunctionStates->LoadTableState;
	LoadTableVariablesPtr_t pLoadTableVariables = gTcg.pAllFunctionStates->pLoadTableVariables;
	U8 ubContinue = TRUE;
	while (ubContinue) {
		ubContinue = FALSE;
		switch (LoadTableState) {
		case LoadTableState_Initial:
			M_TCG_INFO_LOG(0x0006);
			TcgValidVariables(sizeof(LoadTableVariables_t), &pLoadTableVariables);
			LoadTableState = LoadTableState_CheckVT;
		//no break;
		case LoadTableState_CheckVT: {
				U32 uli;
				for (uli = 0; uli < OPAL_TOTAL_TABLE_FLASH_CNT; uli++) {
					if ((U32)(gpVT->ualTcgPlaneIdx[uli]) == (U32)INVALID_PLANE_INDEX ) {
						break;
					}
				}
				if (uli != OPAL_TOTAL_TABLE_FLASH_CNT) {
					pLoadTableVariables->ubCheckOpalTableResult = FALSE;
					LoadTableState = LoadTableState_Done;
				}
				else {
					if (gpVT->OPAL.btTransaction == TRUE) {
						//SaveOpalTable will Save VT
						if (TRUE == gubEnterFTLTask) {
							gpVT->OPAL.btTransaction = FALSE;
						}
						else {
							// Because it cannot write flash table when power on at init stage, we need to change plane idx to make it correct
							// Temporarily replace the Origin Index with Spare Index,
							// Wait for the first TCG command and then recover the origin table content.
							U32 ulTableSyncBMPIdx;
							for (ulTableSyncBMPIdx = TcgFlashTableOriginAdmin1Index; ulTableSyncBMPIdx < (TcgFlashTableOriginAdmin1Index + OPAL_SP_TABLE_FLASH_CNT); ++ulTableSyncBMPIdx) {
								if (0 == (gpVT->OPAL.uwTcgTableSyncBMP & BIT(ulTableSyncBMPIdx))) {
									gpVT->ualTcgPlaneIdx[ulTableSyncBMPIdx] = gpVT->ualTcgPlaneIdx[ulTableSyncBMPIdx + OPAL_SP_TABLE_FLASH_CNT];
								}
							}
						}
						pLoadTableVariables->ubTableSyncDirection = OPAL_SYNC_SP_TABLE_RECOVER_ORIGIN;
						M_TCG_EVENT_LOG(TCG_SECURITY_LOG_SIGNIFICANT_EVENT_0x0004); // Transaction recover tables - Power cycle
						LoadTableState = LoadTableState_SyncSpareTable;
					}
					else {
						pLoadTableVariables->ubCheckOpalTableResult = TRUE;
						LoadTableState = LoadTableState_Done;
					}
				}
				ubContinue = TRUE;
			}
			break;
		case LoadTableState_SyncSpareTable:
			TcgSyncSpTable(pLoadTableVariables->ubTableSyncDirection);
			if (gTcg.pAllFunctionStates->SyncSpState == SyncSpState_Initial) {
				ubContinue = TRUE;
				pLoadTableVariables->ubCheckOpalTableResult = TRUE;
				LoadTableState = LoadTableState_Done;
			}
			break;
		case LoadTableState_Done: {
				U8 ubResult = pLoadTableVariables->ubCheckOpalTableResult;
				TcgInvalidVariables(sizeof(LoadTableVariables_t), &pLoadTableVariables);
				LoadTableState = LoadTableState_Initial;

				gTcg.pAllFunctionStates->pLoadTableVariables = pLoadTableVariables;
				gTcg.pAllFunctionStates->LoadTableState = LoadTableState;
				return ubResult;
			}
		}
	}
	gTcg.pAllFunctionStates->pLoadTableVariables = pLoadTableVariables;
	gTcg.pAllFunctionStates->LoadTableState = LoadTableState;
	return FALSE;
}

U32 TcgGetOpalTableFlashIndexByAceTableIndex(U32 ulTableIndex)
{
	if (ulTableIndex < TCG_ACE_TABLE_ELEMENT_PER_4K) {
		return (TcgFlashTableOriginLocking4Index);
	}
	else if (ulTableIndex >= TCG_ACE_TABLE_ELEMENT_PER_4K && ulTableIndex < 2 * TCG_ACE_TABLE_ELEMENT_PER_4K) { /* parasoft-suppress BD-PB-CC "confirmed by author" */
		return (TcgFlashTableOriginLocking5Index);
	}
	else if (ulTableIndex >= 2 * TCG_ACE_TABLE_ELEMENT_PER_4K && ulTableIndex < 3 * TCG_ACE_TABLE_ELEMENT_PER_4K) {
		return (TcgFlashTableOriginLocking6Index);
	}
	else if (ulTableIndex >= 3 * TCG_ACE_TABLE_ELEMENT_PER_4K && ulTableIndex < 4 * TCG_ACE_TABLE_ELEMENT_PER_4K) {
		return (TcgFlashTableOriginLocking7Index);
	}
	else if (ulTableIndex >= 4 * TCG_ACE_TABLE_ELEMENT_PER_4K && ulTableIndex < 5 * TCG_ACE_TABLE_ELEMENT_PER_4K) {
		return (TcgFlashTableOriginLocking8Index);
	}
	else if (ulTableIndex >= 5 * TCG_ACE_TABLE_ELEMENT_PER_4K && ulTableIndex < 6 * TCG_ACE_TABLE_ELEMENT_PER_4K) {
		return (TcgFlashTableOriginLocking9Index);
	}
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, 0);
	return NULL;
}

void TcgInitComIdProperty(void)
{
	M_TCG_INFO_LOG(0x0007);
	gTcg.pVT->ComIdProperty.ulRequestComId = 0;
	gTcg.pVT->ComIdProperty.ubComIdRequestCode = 0;
	gTcg.pVT->ComIdProperty.ulComIdState = TcgInactiveComIdState;
	gTcg.pVT->ComIdProperty.ulComIdResponse = TcgInvalidComIdState;
}

void TcgInitTperProperty()
{
	M_TCG_INFO_LOG(0x0008);
	gTcg.pVT->TperProperty.ulMaxMethods = 1;
	gTcg.pVT->TperProperty.ulMaxSubpackets = 1;
	gTcg.pVT->TperProperty.ulMaxCompacketSize = TCG_PAYLOAD_BUFFER_SIZE;
	gTcg.pVT->TperProperty.ulMaxPacketSize = gTcg.pVT->TperProperty.ulMaxCompacketSize - TCG_COMPACKET_HEADER_LENGTH;
	gTcg.pVT->TperProperty.ulMaxPackets = 1;
	gTcg.pVT->TperProperty.ulMaxResponseCompacketSize = gTcg.pVT->TperProperty.ulMaxCompacketSize;
	gTcg.pVT->TperProperty.ulMaxSessions = 1;
	gTcg.pVT->TperProperty.ulMaxIndexTokenSize = gTcg.pVT->TperProperty.ulMaxCompacketSize - TCG_PAYLOAD_HEADER_LENGTH;
	/* A10-3-2-15-1 */
	gTcg.pVT->TperProperty.ulMaxAuthentications = M_TCG_GET_LOCKING_SP_USER_NUM();
	gTcg.pVT->TperProperty.ulMaxTransactionLimits = 1;
	gTcg.pVT->TperProperty.ulDefSessionTimeout = TCG_SESSION_TIMEOUT_TIME;
}

void TcgInitHostProperty()
{
	M_TCG_INFO_LOG(0x0009);
	gTcg.pVT->HostProperty.ulMaxMethods = 1;
	gTcg.pVT->HostProperty.ulMaxSubpackets = 1;
	gTcg.pVT->HostProperty.ulMaxCompacketSize = 2048;
	gTcg.pVT->HostProperty.ulMaxPacketSize = gTcg.pVT->HostProperty.ulMaxCompacketSize - TCG_COMPACKET_HEADER_LENGTH;
	gTcg.pVT->HostProperty.ulMaxPackets = 1;
	gTcg.pVT->HostProperty.ulMaxResponseCompacketSize = 0;
	gTcg.pVT->HostProperty.ulMaxIndexTokenSize = gTcg.pVT->HostProperty.ulMaxCompacketSize - TCG_PAYLOAD_HEADER_LENGTH;
}

void TcgInitSessionProperty()
{
	SessionInitState_t SessionInitState = gTcg.pAllFunctionStates->SessionInitState;
	U8 ubContinue = TRUE;
	while (ubContinue) {
		ubContinue = FALSE;
		switch (SessionInitState) {
		case SessionInitState_Initial:
			M_TCG_INFO_LOG(0x000A);
			SessionInitState = SessionInitState_PropertyInitial;
		// no break;
		case SessionInitState_PropertyInitial:
			gTcg.pVT->ComIdProperty.ulComId = TCG_TPER_COMID;
			gTcg.pVT->SessionProperty.ulSpUidL = TcgSpNullUidL;
			gTcg.pVT->SessionProperty.ulHostSessionId = 0;
			gTcg.pVT->SessionProperty.ulDeviceSessionId = 0;
			gTcg.pVT->SessionProperty.ubWriteSession = FALSE;
			gTcg.pVT->SessionProperty.ubStartSession = FALSE;
			gTcg.btDisableLPM = FALSE;
			gTcg.pVT->SessionProperty.ubCloseSession = FALSE;
			/* A7-1-6-1-1, A7-1-7-1-1, A7-1-8-1-1 */
			TcgSetTransactionAndRegisterSaveVT(FALSE);
			gTcg.pVT->SessionProperty.ubEndTransaction = FALSE;
			gTcg.pVT->SessionProperty.ubEndTransactionStatus = FALSE;
			gTcg.CmdSequenceState = TcgIfSendState;
			gTcg.pVT->SessionProperty.uoCurrentSessionTimeout = TCG_SESSION_TIMEOUT_TIME;
#if(SATA == HOST_MODE)
			TcgSetSATALPMEnable();
#endif
			SessionInitState = SessionIniState_TriggerLoadAdminAuthority;
		// no break;
		case SessionIniState_TriggerLoadAdminAuthority:
			//Trigger Load
			TcgSetOpalTableEventSecuritySend(OpalTableState_Load, TcgFlashTableOriginAdmin2Index, OPAL_NULL_TARGET_ADDR);
			if (gTcg.BufManager.OpalTableState == OpalTableState_Idle) {
				SessionInitState = SessionIniState_ClearAdminAuthority;
				ubContinue = TRUE;
			}
			break;
		case SessionIniState_ClearAdminAuthority: {
				U32 uli;
				U8 ubAdminSPAuthorityTableLength = M_TCG_GET_ADMIN_SP_AUTHORITY_TABLE_LENGTH();
				TcgAdminPartial2Ptr_t pTcgApiStructAdminPartial2 = (TcgAdminPartial2Ptr_t)gTcg.BufManager.ulBufAddr;
				for (uli = 0; uli < ubAdminSPAuthorityTableLength; uli++) {
					pTcgApiStructAdminPartial2->AuthorityTable[uli].ubAuthenticate = FALSE;
				}
			}
			SessionInitState = SessionIniState_TriggerSaveAdminAuthority;
		// no break;
		case SessionIniState_TriggerSaveAdminAuthority:
			//Triggre Save
			TcgSetOpalTableEventSecuritySend(OpalTableState_Save, TcgFlashTableOriginAdmin2Index, OPAL_NULL_TARGET_ADDR);
			if (gTcg.BufManager.OpalTableState == OpalTableState_Idle) {
				SessionInitState = SessionIniState_TriggerLoadLockingAuthority;
				ubContinue = TRUE;
			}
			break;
		case SessionIniState_TriggerLoadLockingAuthority:
			//Trigger Load
			TcgSetOpalTableEventSecuritySend(OpalTableState_Load, TcgFlashTableOriginLocking3Index, OPAL_NULL_TARGET_ADDR);
			if (gTcg.BufManager.OpalTableState == OpalTableState_Idle) {
				SessionInitState = SessionIniState_ClearLockingAuthority;
				ubContinue = TRUE;
			}
			break;
		case SessionIniState_ClearLockingAuthority: {
				U32 uli;
				U8 ubLockingSPAuthorityTableLength = M_TCG_GET_LOCKING_SP_AUTHORITY_TABLE_LENGTH();
				TcgLockingPart3Ptr_t pTcgApiStructLockingPartial3 = (TcgLockingPart3Ptr_t)gTcg.BufManager.ulBufAddr;
				for (uli = 0; uli < ubLockingSPAuthorityTableLength; uli++) {
					pTcgApiStructLockingPartial3->AuthorityTable[uli].ubAuthenticate = FALSE;
				}
			}
			SessionInitState = SessionIniState_TriggerSaveLockingAuthority;
		// no break;
		case SessionIniState_TriggerSaveLockingAuthority:
			//Trigger Save
			TcgSetOpalTableEventSecuritySend(OpalTableState_Save, TcgFlashTableOriginLocking3Index, OPAL_NULL_TARGET_ADDR);
			if (gTcg.BufManager.OpalTableState == OpalTableState_Idle) {
				SessionInitState = SessionInitState_Done;
				ubContinue = TRUE;
			}
			break;
		case SessionInitState_Done:
			SessionInitState = SessionInitState_Initial;
			break;
		}
	}
	gTcg.pAllFunctionStates->SessionInitState = SessionInitState;
}
#endif/* (TCG_EN && !BURNER_MODE_EN && !RDT_MODE_EN && !LPM3_LOADER && !BOOTLOADER_MODE_EN) */

#if (TCG_EN && (PS5013_EN || PS5021_EN || (PS5017_EN && !BURNER_MODE_EN)) && !RDT_MODE_EN && !LPM3_LOADER && !BOOTLOADER_MODE_EN)
// Cannot call TcgEncryptKeyData or TcgDecryptKeyData except Psid because salt is fixed
void TcgInitAdminSp()
{
	U32 ulTableIndex;
	U8 aubMsid[TCG_PIN_MAX_LENGTH] = {0};
	U8 ubMsidLength = 0;
	AdminInitState_t AdminInitState = gTcg.pAllFunctionStates->AdminInitState;
	AdminInitVariablesPtr_t pAdminInitVariables = gTcg.pAllFunctionStates->pAdminInitVariables;
	U8 ubContinue = TRUE;
	while (ubContinue) {
		ubContinue = FALSE;
		switch (AdminInitState) {
		case AdminInitState_Initial:
			M_TCG_INFO_LOG(0x000B);
			TcgValidVariables(sizeof(AdminInitVariables_t), &pAdminInitVariables);
			AdminInitState = AdminInitState_TriggerLoadAdmin1Table;
		// no break;
		case AdminInitState_TriggerLoadAdmin1Table:
			TcgSetOpalTableEvent(OpalTableState_Load, TcgFlashTableOriginAdmin1Index, OPAL_NULL_TARGET_ADDR);
			if (gTcg.BufManager.OpalTableState == OpalTableState_Idle) {
				TcgAdminPartial1Ptr_t pTcgApiStructAdminPartial1 = (TcgAdminPartial1Ptr_t)gTcg.BufManager.ulBufAddr;
				if (RevertState_AdminInit == gTcg.pAllFunctionStates->RevertState) {
					M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, TCG_SECURITY_VERSION == pTcgApiStructAdminPartial1->ubSecurityVersion);
				}
				pTcgApiStructAdminPartial1->ubSecurityVersion = TCG_SECURITY_VERSION;
				AdminInitState = AdminInitState_TriggerSaveAdmin1Table;
				ubContinue = TRUE;
			}
			break;
		case AdminInitState_TriggerSaveAdmin1Table:
			TcgSetOpalTableEvent(OpalTableState_Save, TcgFlashTableOriginAdmin1Index, OPAL_NULL_TARGET_ADDR);
			if (gTcg.BufManager.OpalTableState == OpalTableState_Idle) {
				AdminInitState = AdminInitState_TriggerLoadAdmin2Table;
				ubContinue = TRUE;
			}
			break;
		case AdminInitState_TriggerLoadAdmin2Table:
			TcgSetOpalTableEvent(OpalTableState_Load, TcgFlashTableOriginAdmin2Index, OPAL_NULL_TARGET_ADDR);
			if (gTcg.BufManager.OpalTableState == OpalTableState_Idle) {
				AdminInitState = AdminInitState_SetAdmin2Table;
				ubContinue = TRUE;
			}
			break;
		case AdminInitState_SetAdmin2Table: {
				TcgAdminPartial2Ptr_t pTcgApiStructAdminPartial2 = (TcgAdminPartial2Ptr_t)gTcg.BufManager.ulBufAddr;
				U8 ubAdminSPAuthorityTableLength = M_TCG_GET_ADMIN_SP_AUTHORITY_TABLE_LENGTH();
				U8 ubAdminSPCPINTableLength = M_TCG_GET_ADMIN_SP_CPIN_TABLE_LENGTH();
				//==================== Table Table ====================
				ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableTableUidL, TcgTableTableUidL);
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulUidL = TcgTableTableUidL;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulColumnNum = TCG_TABLE_TABLE_COLUMN_NUM;

				if (M_TCG_CHECK_PYRITE_VERSION_2()) {
					pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulRowNum = TCG_PYRITE_2_ADMIN_SP_TABLE_TABLE_LENGTH;
					pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMinRowNum = TCG_PYRITE_2_ADMIN_SP_TABLE_TABLE_LENGTH;
					pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMaxRowNum = TCG_PYRITE_2_ADMIN_SP_TABLE_TABLE_LENGTH;
				}
				else if (M_TCG_CHECK_PYRITE_VERSION_1()) {
					pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulRowNum = TCG_PYRITE_1_ADMIN_SP_TABLE_TABLE_LENGTH;
					pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMinRowNum = TCG_PYRITE_1_ADMIN_SP_TABLE_TABLE_LENGTH;
					pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMaxRowNum = TCG_PYRITE_1_ADMIN_SP_TABLE_TABLE_LENGTH;
				}
				else {
					pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulRowNum = TCG_OPAL_ADMIN_SP_TABLE_TABLE_LENGTH;
					pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMinRowNum = TCG_OPAL_ADMIN_SP_TABLE_TABLE_LENGTH;
					pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMaxRowNum = TCG_OPAL_ADMIN_SP_TABLE_TABLE_LENGTH;
				}

				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].TableKind = TcgTableObjectKind;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMadatoryWriteGranularity = TCG_OBJECT_TABLE_WRITE_GRANULARITY;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulRecommandReadGranularity = TCG_OBJECT_TABLE_READ_GRANULARITY;
				//==================== AccessControl Table ====================
				ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableTableUidL, TcgTableAclUidL);
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulUidL = TcgTableAclUidL;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].TableKind = TcgTableObjectKind;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMadatoryWriteGranularity = TCG_OBJECT_TABLE_WRITE_GRANULARITY;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulRecommandReadGranularity = TCG_OBJECT_TABLE_READ_GRANULARITY;
				//==================== SPInfo Table ====================
				ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableTableUidL, TcgTableSpInfoUidL);
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulUidL = TcgTableSpInfoUidL;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulColumnNum = TCG_SP_INFO_TABLE_COLUMN_NUM;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulRowNum = TCG_ADMIN_SP_SP_INFO_TABLE_LENGTH;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMinRowNum = TCG_ADMIN_SP_SP_INFO_TABLE_LENGTH;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMaxRowNum = TCG_ADMIN_SP_SP_INFO_TABLE_LENGTH;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].TableKind = TcgTableObjectKind;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMadatoryWriteGranularity = TCG_OBJECT_TABLE_WRITE_GRANULARITY;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulRecommandReadGranularity = TCG_OBJECT_TABLE_READ_GRANULARITY;
				// spinfo
				ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableSpInfoUidL, TcgSpInfoUidL);
				pTcgApiStructAdminPartial2->SpInfoTable[ulTableIndex].ulUidL = TcgSpInfoUidL;
				pTcgApiStructAdminPartial2->SpInfoTable[ulTableIndex].SpUidL = TcgSpAdminUidL;
				pTcgApiStructAdminPartial2->SpInfoTable[ulTableIndex].ubEnable = TRUE;
				pTcgApiStructAdminPartial2->SpInfoTable[ulTableIndex].ulTcgLBASize = TCG_TABLE_SECTOR_CNT;
				pTcgApiStructAdminPartial2->SpInfoTable[ulTableIndex].ulAdminSpByteSize = TCG_ADMINSP_TABLE_BYTE_SIZE;
				pTcgApiStructAdminPartial2->SpInfoTable[ulTableIndex].uoSpSessionTimeout = TCG_SESSION_TIMEOUT_TIME;
				//==================== SPTemplates Table ====================
				ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableTableUidL, TcgTableSpTemplateUidL);
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulUidL = TcgTableSpTemplateUidL;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulColumnNum = TCG_SP_TEMPLATE_TABLE_COLUMN_NUM;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulRowNum = TCG_ADMIN_SP_SP_TEMPLATE_TABLE_LENGTH;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMinRowNum = TCG_ADMIN_SP_SP_TEMPLATE_TABLE_LENGTH;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMaxRowNum = TCG_ADMIN_SP_SP_TEMPLATE_TABLE_LENGTH;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].TableKind = TcgTableObjectKind;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMadatoryWriteGranularity = TCG_OBJECT_TABLE_WRITE_GRANULARITY;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulRecommandReadGranularity = TCG_OBJECT_TABLE_READ_GRANULARITY;
				// base
				ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableSpTemplateUidL, TcgSpTemplateBaseUidL);
				pTcgApiStructAdminPartial2->SpTemplateTable[ulTableIndex].ulUidL = TcgSpTemplateBaseUidL;
				pTcgApiStructAdminPartial2->SpTemplateTable[ulTableIndex].ulTemplateUidL = TcgTemplateBaseUidL;
				// admin
				ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableSpTemplateUidL, TcgSpTemplateAdminUidL);
				pTcgApiStructAdminPartial2->SpTemplateTable[ulTableIndex].ulUidL = TcgSpTemplateAdminUidL;
				pTcgApiStructAdminPartial2->SpTemplateTable[ulTableIndex].ulTemplateUidL = TcgTemplateAdminUidL;
				//==================== MethodID Table ====================
				ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableTableUidL, TcgTableMethodUidL);
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulUidL = TcgTableMethodUidL;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulColumnNum = TCG_METHOD_TABLE_COLUMN_NUM;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulRowNum = TCG_ADMIN_SP_METHOD_TABLE_LENGTH;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMinRowNum = TCG_ADMIN_SP_METHOD_TABLE_LENGTH;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMaxRowNum = TCG_ADMIN_SP_METHOD_TABLE_LENGTH;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].TableKind = TcgTableObjectKind;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMadatoryWriteGranularity = TCG_OBJECT_TABLE_WRITE_GRANULARITY;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulRecommandReadGranularity = TCG_OBJECT_TABLE_READ_GRANULARITY;
				// Next
				ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableMethodUidL, TcgMethodNextUidL);
				pTcgApiStructAdminPartial2->MethodTable[ulTableIndex].ulUidL = TcgMethodNextUidL;
				// GetACL
				ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableMethodUidL, TcgMethodGetAclUidL);
				pTcgApiStructAdminPartial2->MethodTable[ulTableIndex].ulUidL = TcgMethodGetAclUidL;
				// Get
				ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableMethodUidL, TcgMethodGetUidL);
				pTcgApiStructAdminPartial2->MethodTable[ulTableIndex].ulUidL = TcgMethodGetUidL;
				// Set
				ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableMethodUidL, TcgMethodSetUidL);
				pTcgApiStructAdminPartial2->MethodTable[ulTableIndex].ulUidL = TcgMethodSetUidL;
				// Authenticate
				ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableMethodUidL, TcgMethodAuthenticateUidL);
				pTcgApiStructAdminPartial2->MethodTable[ulTableIndex].ulUidL = TcgMethodAuthenticateUidL;
				// Revert
				ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableMethodUidL, TcgMethodRevertUidL);
				pTcgApiStructAdminPartial2->MethodTable[ulTableIndex].ulUidL = TcgMethodRevertUidL;
				// Activate
				ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableMethodUidL, TcgMethodActivateUidL);
				pTcgApiStructAdminPartial2->MethodTable[ulTableIndex].ulUidL = TcgMethodActivateUidL;
				// Random
				ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableMethodUidL, TcgMethodRandomUidL);
				pTcgApiStructAdminPartial2->MethodTable[ulTableIndex].ulUidL = TcgMethodRandomUidL;

				//==================== ACE Table ====================
				ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableTableUidL, TcgTableAceUidL);
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulUidL = TcgTableAceUidL;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulColumnNum = TCG_ACE_TABLE_COLUMN_NUM;

				if (M_TCG_CHECK_PYRITE_VERSION_2()) {
					pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulRowNum = TCG_PYRITE_2_ADMIN_SP_ACE_TABLE_INCLUDE_PSID_LENGTH;
					pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMinRowNum = TCG_PYRITE_2_ADMIN_SP_ACE_TABLE_INCLUDE_PSID_LENGTH;
					pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMaxRowNum = TCG_PYRITE_2_ADMIN_SP_ACE_TABLE_INCLUDE_PSID_LENGTH;
				}
				else if (M_TCG_CHECK_PYRITE_VERSION_1()) {
					pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulRowNum = TCG_PYRITE_1_ADMIN_SP_ACE_TABLE_INCLUDE_PSID_LENGTH;
					pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMinRowNum = TCG_PYRITE_1_ADMIN_SP_ACE_TABLE_INCLUDE_PSID_LENGTH;
					pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMaxRowNum = TCG_PYRITE_1_ADMIN_SP_ACE_TABLE_INCLUDE_PSID_LENGTH;
				}
				else {
					pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulRowNum = TCG_OPAL_ADMIN_SP_ACE_TABLE_INCLUDE_PSID_LENGTH;
					pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMinRowNum = TCG_OPAL_ADMIN_SP_ACE_TABLE_INCLUDE_PSID_LENGTH;
					pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMaxRowNum = TCG_OPAL_ADMIN_SP_ACE_TABLE_INCLUDE_PSID_LENGTH;
				}

				if (FALSE == M_TCG_SUPPORT_PSID_FEATURE()) {
					pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulRowNum -= TCG_ADMIN_SP_ACE_TABLE_PSID_LENGTH;
					pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMinRowNum -= TCG_ADMIN_SP_ACE_TABLE_PSID_LENGTH;
					pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMaxRowNum -= TCG_ADMIN_SP_ACE_TABLE_PSID_LENGTH;
				}

				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].TableKind = TcgTableObjectKind;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMadatoryWriteGranularity = TCG_OBJECT_TABLE_WRITE_GRANULARITY;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulRecommandReadGranularity = TCG_OBJECT_TABLE_READ_GRANULARITY;
				// BaseACEs - ACE_Anybody
				ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableAceUidL, TcgAceAnybodyUidL);
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ulUidL = TcgAceAnybodyUidL;
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAnybodyUidL;
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ubAuthorityCnt = 1;
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ubBooleanAceCnt = 0;
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ubAvailableColumnCnt = 0;
				if (M_TCG_CHECK_OPAL()) {
					// BaseACEs - ACE_Admin
					ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableAceUidL, TcgAceAdminUidL);
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ulUidL = TcgAceAdminUidL;
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ubAuthorityCnt = 1;
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ubBooleanAceCnt = 0;
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ubAvailableColumnCnt = 0;
					// Authority - ACE_Set_Enabled
					ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableAceUidL, TcgAceSetEnabledUidL);
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ulUidL = TcgAceSetEnabledUidL;
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthoritySidUidL;
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ubAuthorityCnt = 1;
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ubBooleanAceCnt = 0;
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ubAvailableColumn[0] = TcgAuthorityTableEnableColumn;
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ubAvailableColumnCnt = 1;
				}
				// C_PIN - ACE_C_PIN_SID_Get_NOPIN
				ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableAceUidL, TcgAceCPinSidGetNoPinUidL);
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ulUidL = TcgAceCPinSidGetNoPinUidL;
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ACElementContent[1].AuthorityUidL = TcgAuthoritySidUidL;
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ubAuthorityCnt = 2;
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ACElementContent[0].BooleanAce = TcgOrBoolean;
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ubBooleanAceCnt = 1;
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ubAvailableColumn[0] = TcgCPinUidColumn;
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ubAvailableColumn[1] = TcgCPinCharSetColumn;
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ubAvailableColumn[2] = TcgCPinTryLimitColumn;
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ubAvailableColumn[3] = TcgCPinTriesColumn;
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ubAvailableColumn[4] = TcgCPinPersistenceColumn;
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ubAvailableColumnCnt = 5;
				// C_PIN - ACE_C_PIN_SID_Set_PIN
				ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableAceUidL, TcgAceCPinSidSetPinUidL);
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ulUidL = TcgAceCPinSidSetPinUidL;
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthoritySidUidL;
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ubAuthorityCnt = 1;
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ubBooleanAceCnt = 0;
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ubAvailableColumn[0] = TcgCPinPinColumn;
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ubAvailableColumnCnt = 1;
				// C_PIN - ACE_C_PIN_MSID_Get_PIN
				ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableAceUidL, TcgAceCPinMsidGetPinUidL);
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ulUidL = TcgAceCPinMsidGetPinUidL;
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAnybodyUidL;
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ubAuthorityCnt = 1;
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ubBooleanAceCnt = 0;
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ubAvailableColumn[0] = TcgCPinUidColumn;
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ubAvailableColumn[1] = TcgCPinPinColumn;
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ubAvailableColumnCnt = 2;
				if (M_TCG_CHECK_OPAL()) {
					// C_PIN - ACE_C_PIN_Admins_Set_PIN
					ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableAceUidL, TcgAceCPinAdminsSetPinUidL);
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ulUidL = TcgAceCPinAdminsSetPinUidL;
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ACElementContent[1].AuthorityUidL = TcgAuthoritySidUidL;
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ubAuthorityCnt = 2;
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ACElementContent[0].BooleanAce = TcgOrBoolean;
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ubBooleanAceCnt = 1;
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ubAvailableColumn[0] = TcgCPinPinColumn;
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ubAvailableColumnCnt = 1;
				}
				// TPerInfo - ACE_TPerInfo_Set_ProgrammaticResetEnable
				ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableAceUidL, TcgAceTperInfoSetProgrammaticResetEnableUidL);
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ulUidL = TcgAceTperInfoSetProgrammaticResetEnableUidL;
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthoritySidUidL;
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ubAuthorityCnt = 1;
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ubBooleanAceCnt = 0;
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ubAvailableColumn[0] = TcgTperInfoProgrammaticResetEnableColumn;
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ubAvailableColumnCnt = 1;
				// SP - ACE_SP_SID
				ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableAceUidL, TcgAceSpSidUidL);
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ulUidL = TcgAceSpSidUidL;
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthoritySidUidL;
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ubAuthorityCnt = 1;
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ubBooleanAceCnt = 0;
				pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ubAvailableColumnCnt = 0;
				if (M_TCG_SUPPORT_PSID_FEATURE()) {
					// SP - ACE_SP_PSID
					ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableAceUidL, TcgAceSpPsidUidL);
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ulUidL = TcgAceSpPsidUidL;
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityPsidUidL;
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ubAuthorityCnt = 1;
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ubBooleanAceCnt = 0;
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ubAvailableColumnCnt = 0;
					// SP - ACE_SP_CPIN_GET_PSID_NO_PIN
					ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableAceUidL, TcgAceCPinGetPsidNoPinUidL);
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ulUidL = TcgAceCPinGetPsidNoPinUidL;
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAnybodyUidL;
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ubAuthorityCnt = 1;
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ubBooleanAceCnt = 0;
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ubAvailableColumn[0] = TcgCPinUidColumn;
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ubAvailableColumn[1] = TcgCPinCharSetColumn;
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ubAvailableColumn[2] = TcgCPinTryLimitColumn;
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ubAvailableColumn[3] = TcgCPinTriesColumn;
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ubAvailableColumn[4] = TcgCPinPersistenceColumn;
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ubAvailableColumnCnt = 5;
				}
				//==================== Authority Table ====================
				ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableTableUidL, TcgTableAuthorityUidL);
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulUidL = TcgTableAuthorityUidL;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulColumnNum = TCG_AUTHORITY_TABLE_COLUMN_NUM;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulRowNum = ubAdminSPAuthorityTableLength;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMinRowNum = ubAdminSPAuthorityTableLength;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMaxRowNum = ubAdminSPAuthorityTableLength;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].TableKind = TcgTableObjectKind;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMadatoryWriteGranularity = TCG_OBJECT_TABLE_WRITE_GRANULARITY;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulRecommandReadGranularity = TCG_OBJECT_TABLE_READ_GRANULARITY;
				// Anybody
				ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableAuthorityUidL, TcgAuthorityAnybodyUidL);
				pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].ulUidL = TcgAuthorityAnybodyUidL;
				pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].ubClass = FALSE;
				pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].ulAuthorityClassUidL = TcgAuthorityNullUidL;
				pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].ubEnable = TRUE;
				pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].AuthorityOperation = TcgAuthorityNoneOperation;
				pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].ulCredentialCPinUidL = TcgCPinNullUidL;
				pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].CommonName.ubLength = 0;
				// Admins
				ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableAuthorityUidL, TcgAuthorityAdminsUidL);
				pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].ulUidL = TcgAuthorityAdminsUidL;
				pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].ubClass = TRUE;
				pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].ulAuthorityClassUidL = TcgAuthorityNullUidL;
				pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].ubEnable = TRUE;
				pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].AuthorityOperation = TcgAuthorityNoneOperation;
				pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].ulCredentialCPinUidL = TcgCPinNullUidL;
				pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].CommonName.ubLength = 0;
				if (M_TCG_CHECK_OPAL()) {
					// Makers
					ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableAuthorityUidL, TcgAuthorityMakersUidL);
					pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].ulUidL = TcgAuthorityMakersUidL;
					pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].ubClass = TRUE;
					pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].ulAuthorityClassUidL = TcgAuthorityNullUidL;
					pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].ubEnable = TRUE;
					pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].AuthorityOperation = TcgAuthorityNoneOperation;
					pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].ulCredentialCPinUidL = TcgCPinNullUidL;
					pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].CommonName.ubLength = 0;
				}
				// SID
				ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableAuthorityUidL, TcgAuthoritySidUidL);
				pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].ulUidL = TcgAuthoritySidUidL;
				pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].ubClass = FALSE;
				pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].ulAuthorityClassUidL = TcgAuthorityNullUidL;
				pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].ubEnable = TRUE;
				pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].AuthorityOperation = TcgAuthorityPasswordOperation;
				pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].ulCredentialCPinUidL = TcgAdminSpCPinSidUidL;
				pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].CommonName.ubLength = 0;
				if (M_TCG_CHECK_OPAL()) {
					// Admin1
					ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableAuthorityUidL, TcgAdminSpAuthorityAdmin1UidL);
					pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].ulUidL = TcgAdminSpAuthorityAdmin1UidL;
					pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].ubClass = FALSE;
					pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].ulAuthorityClassUidL = TcgAuthorityAdminsUidL;
					pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].ubEnable = FALSE;
					pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].AuthorityOperation = TcgAuthorityPasswordOperation;
					pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].ulCredentialCPinUidL = TcgAdminSpCPinAdmin1UidL;
					pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].CommonName.ubLength = 0;
				}
				if (M_TCG_SUPPORT_PSID_FEATURE()) {
					// Psid
					ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableAuthorityUidL, TcgAuthorityPsidUidL);
					pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].ulUidL = TcgAuthorityPsidUidL;
					pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].ubClass = FALSE;
					pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].ulAuthorityClassUidL = TcgAuthorityNullUidL;
					pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].ubEnable = TRUE;
					pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].AuthorityOperation = TcgAuthorityPasswordOperation;
					pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].ulCredentialCPinUidL = TcgCPinPsidUidL;
					pTcgApiStructAdminPartial2->AuthorityTable[ulTableIndex].CommonName.ubLength = 0;
				}
				//==================== C_PIN Table ====================
				ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableTableUidL, TcgTableCPinUidL);
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulUidL = TcgTableCPinUidL;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulColumnNum = TCG_CPIN_TABLE_COLUMN_NUM;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulRowNum = ubAdminSPCPINTableLength;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMinRowNum = ubAdminSPCPINTableLength;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMaxRowNum = ubAdminSPCPINTableLength;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].TableKind = TcgTableObjectKind;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMadatoryWriteGranularity = TCG_OBJECT_TABLE_WRITE_GRANULARITY;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulRecommandReadGranularity = TCG_OBJECT_TABLE_READ_GRANULARITY;

				// C_PIN_MSID
				ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableCPinUidL, TcgAdminSpCPinMsidUidL);
				if (M_TCG_CHECK_INIT_REASON(TCG_INIT_REASON_BURNER_PREFORMAT | TCG_INIT_REASON_DLMC_PREFORMAT)) {
					// Set MSID PIN (aubMsid, ubMsidLength)
					U32 ulCheckMsidIdx;
					SystemBlock_t *pSystemBlkTable = (SystemBlock_t *)INFO_BLK_BASE_ADDR;
					// Scan MSID PIN from infoblk
					// MSID PIN length is calculated to the first 0x00 or reach 32 bytes
					for (ulCheckMsidIdx = 0; ulCheckMsidIdx < (TCG_PIN_MAX_LENGTH); ulCheckMsidIdx++) {
						U8 *pubValue = (U8 *)(((U32)pSystemBlkTable->Sector0_Info.ubIPB_TCG_MSID) + (ulCheckMsidIdx));
						if (0x00 == *pubValue) {
							// Encountered 0x00, stop scanning MSID
							break;
						}
					}
					if (0 == ulCheckMsidIdx) {
						// Default MSID
						U8 ubDefaultMsid[TCG_MSID_DEFAULT_PIN_LENGTH] = TCG_MSID_DEFAULT_PIN;
						ubMsidLength = TCG_MSID_DEFAULT_PIN_LENGTH;
						memcpy((void *)&aubMsid[0], (void *)&ubDefaultMsid[0], ubMsidLength);
					}
					else {
						// Set MSID from infoblk
						ubMsidLength = ulCheckMsidIdx;
						memcpy((void *)&aubMsid[0], (void *)pSystemBlkTable->Sector0_Info.ubIPB_TCG_MSID, ubMsidLength);
					}

					memcpy((void *)&pTcgApiStructAdminPartial2->MsidPin.aubPin[0], (void *)&aubMsid[0], ubMsidLength);
					pTcgApiStructAdminPartial2->MsidPin.ubLength = ubMsidLength;
				}
				else {
					// Get MSID PIN (aubMsid, ubMsidLength)
					ubMsidLength = pTcgApiStructAdminPartial2->MsidPin.ubLength;
					memcpy((void *)&aubMsid[0], (void *)&pTcgApiStructAdminPartial2->MsidPin.aubPin[0], ubMsidLength);
				}
				M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, ubMsidLength != 0);
				pTcgApiStructAdminPartial2->CPinTable[ulTableIndex].ulUidL = TcgAdminSpCPinMsidUidL;
				pTcgApiStructAdminPartial2->CPinTable[ulTableIndex].ubTryLimit = TCG_PASSWORD_TRY_LIMIT;
				pTcgApiStructAdminPartial2->CPinTable[ulTableIndex].ubTries = 0;
				pTcgApiStructAdminPartial2->CPinTable[ulTableIndex].ubPersistence = FALSE;

				// default msid
				// C_PIN_SID
				ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableCPinUidL, TcgAdminSpCPinSidUidL);
				pTcgApiStructAdminPartial2->CPinTable[ulTableIndex].ubPinLength = ubMsidLength;
				pTcgApiStructAdminPartial2->CPinTable[ulTableIndex].ulUidL = TcgAdminSpCPinSidUidL;
				pTcgApiStructAdminPartial2->CPinTable[ulTableIndex].ubTryLimit = TCG_PASSWORD_TRY_LIMIT;
				pTcgApiStructAdminPartial2->CPinTable[ulTableIndex].ubTries = 0;
				pTcgApiStructAdminPartial2->CPinTable[ulTableIndex].ubPersistence = FALSE;
				if (M_TCG_CHECK_OPAL()) {
					// C_PIN_Admin1
					ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableCPinUidL, TcgAdminSpCPinAdmin1UidL );
					pTcgApiStructAdminPartial2->CPinTable[ulTableIndex].ubPinLength = 0;
					pTcgApiStructAdminPartial2->CPinTable[ulTableIndex].ulUidL = TcgAdminSpCPinAdmin1UidL;
					pTcgApiStructAdminPartial2->CPinTable[ulTableIndex].ubTryLimit = TCG_PASSWORD_TRY_LIMIT;
					pTcgApiStructAdminPartial2->CPinTable[ulTableIndex].ubTries = 0;
					pTcgApiStructAdminPartial2->CPinTable[ulTableIndex].ubPersistence = FALSE;
				}
				if (M_TCG_SUPPORT_PSID_FEATURE()) {
					TcgEpassword_t Epassword;
					ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableCPinUidL, TcgCPinPsidUidL);
					if (M_TCG_CHECK_INIT_REASON(TCG_INIT_REASON_BURNER_PREFORMAT | TCG_INIT_REASON_DLMC_PREFORMAT)) {
						if (FALSE == gpVT->OPAL.btVUCSetPsid) {
							// Set Psid with preformat Psid buffer
							pTcgApiStructAdminPartial2->CPinTable[ulTableIndex].ubPinLength = TCG_VUC_SETPSID_LENGTH;
							memcpy((void *)&pTcgApiStructAdminPartial2->PsidEpasswordBackup.ubKey[0], (void *)&gaubTcgPsidEpassword[0], sizeof(TcgEpassword_t));
							memcpy((void *)&pAdminInitVariables->PsidEpassword, (void *)&gaubTcgPsidEpassword[0], sizeof(TcgEpassword_t));
						}
						else {
							// Set Psid with Default Value
							TcgEncryptKeyData(&aubMsid[0], ubMsidLength, NULL, (U8 *)&Epassword, NULL, TcgEncryptKeyDataModePSIDPassword);
							pTcgApiStructAdminPartial2->CPinTable[ulTableIndex].ubPinLength = ubMsidLength;
							memcpy((void *)&pTcgApiStructAdminPartial2->PsidEpasswordBackup.ubKey[0], (void *)&Epassword, sizeof(TcgEpassword_t));
							memcpy((void *)&pAdminInitVariables->PsidEpassword, (void *)&Epassword, sizeof(TcgEpassword_t));
						}
					}
					//---------------------------------------------------//
					// C_PIN_PSID
					pTcgApiStructAdminPartial2->CPinTable[ulTableIndex].ulUidL = TcgCPinPsidUidL;
					pTcgApiStructAdminPartial2->CPinTable[ulTableIndex].ubTryLimit = TCG_PASSWORD_TRY_LIMIT;
					pTcgApiStructAdminPartial2->CPinTable[ulTableIndex].ubTries = 0;
					pTcgApiStructAdminPartial2->CPinTable[ulTableIndex].ubPersistence = FALSE;
				}
				//==================== TPerInfo Table ====================
				ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableTableUidL, TcgTableTperInfoUidL);
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulUidL = TcgTableTperInfoUidL;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulColumnNum = TCG_TPER_INFO_TABLE_COLUMN_NUM;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulRowNum = TCG_LOCKING_SP_TPER_INFO_TABLE_LENGTH;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMinRowNum = TCG_LOCKING_SP_TPER_INFO_TABLE_LENGTH;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMaxRowNum = TCG_LOCKING_SP_TPER_INFO_TABLE_LENGTH;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].TableKind = TcgTableObjectKind;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMadatoryWriteGranularity = TCG_OBJECT_TABLE_WRITE_GRANULARITY;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulRecommandReadGranularity = TCG_OBJECT_TABLE_READ_GRANULARITY;
				// TPerInfo
				pTcgApiStructAdminPartial2->TperInfoTable[0].ulUidL = TcgTperInfoUidL;
				pTcgApiStructAdminPartial2->TperInfoTable[0].ulProtocolVersion = TCG_TPER_INFO_TABLE_PROTOCOL_VERSION;
				pTcgApiStructAdminPartial2->TperInfoTable[0].ubProgramaticResetEnable = FALSE;
				if (M_TCG_CHECK_OPAL()) {
					pTcgApiStructAdminPartial2->TperInfoTable[0].SSC.ubLength = 4;
					pTcgApiStructAdminPartial2->TperInfoTable[0].SSC.ubName[0] = 'O';
					pTcgApiStructAdminPartial2->TperInfoTable[0].SSC.ubName[1] = 'p';
					pTcgApiStructAdminPartial2->TperInfoTable[0].SSC.ubName[2] = 'a';
					pTcgApiStructAdminPartial2->TperInfoTable[0].SSC.ubName[3] = 'l';
				}
				else if (M_TCG_CHECK_PYRITE()) {
					pTcgApiStructAdminPartial2->TperInfoTable[0].SSC.ubLength = 6;
					pTcgApiStructAdminPartial2->TperInfoTable[0].SSC.ubName[0] = 'P';
					pTcgApiStructAdminPartial2->TperInfoTable[0].SSC.ubName[1] = 'y';
					pTcgApiStructAdminPartial2->TperInfoTable[0].SSC.ubName[2] = 'r';
					pTcgApiStructAdminPartial2->TperInfoTable[0].SSC.ubName[3] = 'i';
					pTcgApiStructAdminPartial2->TperInfoTable[0].SSC.ubName[4] = 't';
					pTcgApiStructAdminPartial2->TperInfoTable[0].SSC.ubName[5] = 'e';
				}
				//ref. ArchitectureCore
				pTcgApiStructAdminPartial2->TperInfoTable[0].ubGudId[0] = 0x02;
				pTcgApiStructAdminPartial2->TperInfoTable[0].ubGudId[1] = 0x23;
				pTcgApiStructAdminPartial2->TperInfoTable[0].ubGudId[2] = 0x00;
				pTcgApiStructAdminPartial2->TperInfoTable[0].ubGudId[3] = 0x08;
				pTcgApiStructAdminPartial2->TperInfoTable[0].ubGudId[4] = (0x05 << 4) ;
#if (NVME == HOST_MODE)
				pTcgApiStructAdminPartial2->TperInfoTable[0].ubGudId[4] |= (U8)(gpHostPrtInfo->pVarIdfy[0]->btIdfyIEEE >> 20);
				pTcgApiStructAdminPartial2->TperInfoTable[0].ubGudId[5] = (U8)(gpHostPrtInfo->pVarIdfy[0]->btIdfyIEEE >> 12);
				pTcgApiStructAdminPartial2->TperInfoTable[0].ubGudId[6] = (U8)(gpHostPrtInfo->pVarIdfy[0]->btIdfyIEEE >> 4);
				pTcgApiStructAdminPartial2->TperInfoTable[0].ubGudId[7] = (U8)(gpHostPrtInfo->pVarIdfy[0]->btIdfyIEEE & 0x0F) << 4;
#endif /*(NVME == HOST_MODE)*/

				//==================== Template Table ====================
				ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableTableUidL, TcgTableTemplateUidL);
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulUidL = TcgTableTemplateUidL;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulColumnNum = TCG_TEMPLATE_TABLE_COLUMN_NUM;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulRowNum = TCG_ADMIN_SP_TEMPLATE_TABLE_LENGTH;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMinRowNum = TCG_ADMIN_SP_TEMPLATE_TABLE_LENGTH;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMaxRowNum = TCG_ADMIN_SP_TEMPLATE_TABLE_LENGTH;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].TableKind = TcgTableObjectKind;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMadatoryWriteGranularity = TCG_OBJECT_TABLE_WRITE_GRANULARITY;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulRecommandReadGranularity = TCG_OBJECT_TABLE_READ_GRANULARITY;
				// Base
				ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableTemplateUidL, TcgTemplateBaseUidL);
				pTcgApiStructAdminPartial2->TemplateTable[ulTableIndex].ulUidL = TcgTemplateBaseUidL;
				// Admin
				ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableTemplateUidL, TcgTemplateAdminUidL);
				pTcgApiStructAdminPartial2->TemplateTable[ulTableIndex].ulUidL = TcgTemplateAdminUidL;
				// Locking
				ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableTemplateUidL, TcgTemplateLockingUidL);
				pTcgApiStructAdminPartial2->TemplateTable[ulTableIndex].ulUidL = TcgTemplateLockingUidL;

				//==================== SP Table ====================
				ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableTableUidL, TcgTableSpUidL);
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulUidL = TcgTableSpUidL;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulColumnNum = TCG_SP_TABLE_COLUMN_NUM;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulRowNum = TCG_ADMIN_SP_SP_TABLE_LENGTH;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMinRowNum = TCG_ADMIN_SP_SP_TABLE_LENGTH;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMaxRowNum = TCG_ADMIN_SP_SP_TABLE_LENGTH;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].TableKind = TcgTableObjectKind;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMadatoryWriteGranularity = TCG_OBJECT_TABLE_WRITE_GRANULARITY;
				pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulRecommandReadGranularity = TCG_OBJECT_TABLE_READ_GRANULARITY;
				// Admin
				ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableSpUidL, TcgSpAdminUidL);
				pTcgApiStructAdminPartial2->SpTable[ulTableIndex].ulUidL = TcgSpAdminUidL;
				pTcgApiStructAdminPartial2->SpTable[ulTableIndex].ulLifeCycle = TcgManufacturedLifeCycle;
				// Locking
				ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableSpUidL, TcgSpLockingUidL);
				pTcgApiStructAdminPartial2->SpTable[ulTableIndex].ulUidL = TcgSpLockingUidL;
				pTcgApiStructAdminPartial2->SpTable[ulTableIndex].ulLifeCycle = TcgManufacturedInactiveLifeCycle;

				if (M_TCG_CHECK_PYRITE_VERSION_2()) {
					ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableTableUidL, TcgTableDataRemovalMechanismUidL);
					pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulUidL = TcgTableDataRemovalMechanismUidL;
					pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulColumnNum = TCG_DATA_REMOVAL_MECHANISM_TABLE_COLUMN_NUM;
					pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulRowNum = TCG_ADMIN_SP_DATA_REMOVAL_MECHANISM_LENGTH;
					pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMinRowNum = TCG_ADMIN_SP_DATA_REMOVAL_MECHANISM_LENGTH;
					pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMaxRowNum = TCG_ADMIN_SP_DATA_REMOVAL_MECHANISM_LENGTH;
					pTcgApiStructAdminPartial2->TableTable[ulTableIndex].TableKind = TcgTableObjectKind;
					pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulMadatoryWriteGranularity = TCG_OBJECT_TABLE_WRITE_GRANULARITY;
					pTcgApiStructAdminPartial2->TableTable[ulTableIndex].ulRecommandReadGranularity = TCG_OBJECT_TABLE_READ_GRANULARITY;

					ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableAceUidL, TcgAceDataRemovalMechanismSetActiveDataRemovalMechanismUidL);
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ulUidL = TcgAceDataRemovalMechanismSetActiveDataRemovalMechanismUidL;
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ACElementContent[1].AuthorityUidL = TcgAuthoritySidUidL;
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ubAuthorityCnt = 2;
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ACElementContent[0].BooleanAce = TcgOrBoolean;
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].BooleanExpression.ubBooleanAceCnt = 1;
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ubAvailableColumn[0] = TcgDataRemovalMechanismActiveDataRemovalMechanismColumn;
					pTcgApiStructAdminPartial2->AceTable[ulTableIndex].ubAvailableColumnCnt = 1;

					ulTableIndex = TcgGetTableIndexAssert(TcgSpAdminUidL, TcgTableDataRemovalMechanismUidL, TcgDataRemovalMechanismUidL);
					pTcgApiStructAdminPartial2->DataRemovalMechanismTable[ulTableIndex].ulUidL = TcgDataRemovalMechanismUidL;
					pTcgApiStructAdminPartial2->DataRemovalMechanismTable[ulTableIndex].ulActiveDataRemovalMechanism = TcgDataRemovalMechanismUnmap;
				}
			}
			AdminInitState = AdminInitState_TriggerSaveAdmin2Table;
		// no break;
		case AdminInitState_TriggerSaveAdmin2Table:
			TcgSetOpalTableEvent(OpalTableState_Save, TcgFlashTableOriginAdmin2Index, OPAL_NULL_TARGET_ADDR);
			if (gTcg.BufManager.OpalTableState == OpalTableState_Idle) {
				if ((M_TCG_CHECK_INIT_REASON(TCG_INIT_REASON_BURNER_PREFORMAT | TCG_INIT_REASON_DLMC_PREFORMAT)) && (TRUE == M_TCG_SUPPORT_PSID_FEATURE())) {
					AdminInitState = AdminInitState_TriggerLoadAdmin3Table;
				}
				else {
					AdminInitState = AdminInitState_Done;
				}
				ubContinue = TRUE;
			}
			break;
		case AdminInitState_TriggerLoadAdmin3Table:
			TcgSetOpalTableEvent(OpalTableState_Load, TcgFlashTableOriginAdmin3Index, OPAL_NULL_TARGET_ADDR);
			if (OpalTableState_Idle == gTcg.BufManager.OpalTableState) {
				TcgAdminPartial3Ptr_t pTcgApiStructAdminPartial3 = (TcgAdminPartial3Ptr_t)gTcg.BufManager.ulBufAddr;
				memcpy((void *)&pTcgApiStructAdminPartial3->Epassword[M_TCG_ADMIN_SP_PSID_EPASSWORD_IDX()], (void *)&pAdminInitVariables->PsidEpassword, sizeof(TcgEpassword_t));
				AdminInitState = AdminInitState_TriggerSaveAdmin3Table;
				ubContinue = TRUE;
			}
			break;
		case AdminInitState_TriggerSaveAdmin3Table:
			TcgSetOpalTableEvent(OpalTableState_Save, TcgFlashTableOriginAdmin3Index, OPAL_NULL_TARGET_ADDR);
			if (OpalTableState_Idle == gTcg.BufManager.OpalTableState) {
				AdminInitState = AdminInitState_Done;
				ubContinue = TRUE;
			}
			break;
		case AdminInitState_Done:
			TcgInvalidVariables(sizeof(AdminInitVariables_t), &pAdminInitVariables);
			AdminInitState = AdminInitState_Initial;
			break;
		}
	}
	gTcg.pAllFunctionStates->pAdminInitVariables = pAdminInitVariables;
	gTcg.pAllFunctionStates->AdminInitState = AdminInitState;
}

void TcgInitLockingSp(U8 ubMode)
{
	LockingInitState_t LockingInitState = gTcg.pAllFunctionStates->LockingInitState;
	U8 ubLockingSPAdminNum = M_TCG_GET_LOCKING_SP_ADMIN_NUM();
	U8 ubLockingSPUserNum = M_TCG_GET_LOCKING_SP_USER_NUM();
	U8 ubContinue = TRUE;
	while (ubContinue) {
		ubContinue = FALSE;
		switch (LockingInitState) {
		case LockingInitState_Initial:
			M_TCG_INFO_LOG(0x000C);
			LockingInitState = LockingInitState_TriggerLoadLockingTable1;
		// no break;
		case LockingInitState_TriggerLoadLockingTable1:
			TcgSetOpalTableEvent(OpalTableState_Load, TcgFlashTableOriginLocking2Index, OPAL_NULL_TARGET_ADDR);
			if (gTcg.BufManager.OpalTableState == OpalTableState_Idle) {
				LockingInitState = LockingInitState_SetLockingTable1;
				ubContinue = TRUE;
			}
			break;
		case LockingInitState_SetLockingTable1: {
				U32 ulTableIndex;
				U32 uli;
				U8 ubLockingSPAuthorityTableLength 	= M_TCG_GET_LOCKING_SP_AUTHORITY_TABLE_LENGTH();
				U8 ubLockingSPTableTableLength 		= M_TCG_GET_LOCKING_SP_TABLE_TABLE_LENGTH() ;
				U8 ubLockingSPMethodTableLength 	= M_TCG_GET_LOCKING_SP_METHOD_TABLE_LENGTH();
				U8 ubLockingSPACETableLength 		= M_TCG_GET_LOCKING_SP_ACE_TABLE_LENGTH();
				U8 ubLockingSPLockingTableLength	= M_TCG_GET_LOCKING_SP_LOCKING_TABLE_LENGTH();
				U8 ubLockingSPCPINTableLength		= M_TCG_GET_LOCKING_SP_CPIN_TABLE_LENGTH();

				TcgLockingPart2Ptr_t pTcgApiStructLockingPartial2 = (TcgLockingPart2Ptr_t)gTcg.BufManager.ulBufAddr;
				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableTableUidL, TcgTableAclUidL);
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulUidL = TcgTableAclUidL;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].TableKind = TcgTableObjectKind;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMadatoryWriteGranularity = TCG_OBJECT_TABLE_WRITE_GRANULARITY;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulRecommandReadGranularity = TCG_OBJECT_TABLE_READ_GRANULARITY;

				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableTableUidL, TcgTableTableUidL);
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulUidL = TcgTableTableUidL;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulColumnNum = TCG_TABLE_TABLE_COLUMN_NUM;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulRowNum = ubLockingSPTableTableLength;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMinRowNum = ubLockingSPTableTableLength;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMaxRowNum = ubLockingSPTableTableLength;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].TableKind = TcgTableObjectKind;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMadatoryWriteGranularity = TCG_OBJECT_TABLE_WRITE_GRANULARITY;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulRecommandReadGranularity = TCG_OBJECT_TABLE_READ_GRANULARITY;

				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableTableUidL, TcgTableSpInfoUidL);
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulUidL = TcgTableSpInfoUidL;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulColumnNum = TCG_SP_INFO_TABLE_COLUMN_NUM;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulRowNum = TCG_LOCKING_SP_SP_INFO_TABLE_LENGTH;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMinRowNum = TCG_LOCKING_SP_SP_INFO_TABLE_LENGTH;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMaxRowNum = TCG_LOCKING_SP_SP_INFO_TABLE_LENGTH;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].TableKind = TcgTableObjectKind;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMadatoryWriteGranularity = TCG_OBJECT_TABLE_WRITE_GRANULARITY;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulRecommandReadGranularity = TCG_OBJECT_TABLE_READ_GRANULARITY;

				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableTableUidL, TcgTableSpTemplateUidL);
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulUidL = TcgTableSpTemplateUidL;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulColumnNum = TCG_SP_TEMPLATE_TABLE_COLUMN_NUM;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulRowNum = TCG_LOCKING_SP_SP_TEMPLATE_TABLE_LENGTH;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMinRowNum = TCG_LOCKING_SP_SP_TEMPLATE_TABLE_LENGTH;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMaxRowNum = TCG_LOCKING_SP_SP_TEMPLATE_TABLE_LENGTH;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].TableKind = TcgTableObjectKind;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMadatoryWriteGranularity = TCG_OBJECT_TABLE_WRITE_GRANULARITY;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulRecommandReadGranularity = TCG_OBJECT_TABLE_READ_GRANULARITY;

				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableTableUidL, TcgTableMethodUidL);
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulUidL = TcgTableMethodUidL;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulColumnNum = TCG_METHOD_TABLE_COLUMN_NUM;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulRowNum = ubLockingSPMethodTableLength;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMinRowNum = ubLockingSPMethodTableLength;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMaxRowNum = ubLockingSPMethodTableLength;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].TableKind = TcgTableObjectKind;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMadatoryWriteGranularity = TCG_OBJECT_TABLE_WRITE_GRANULARITY;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulRecommandReadGranularity = TCG_OBJECT_TABLE_READ_GRANULARITY;

				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableMethodUidL, TcgMethodNextUidL);
				pTcgApiStructLockingPartial2->MethodTable[ulTableIndex].ulUidL = TcgMethodNextUidL;

				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableMethodUidL, TcgMethodGetAclUidL);
				pTcgApiStructLockingPartial2->MethodTable[ulTableIndex].ulUidL = TcgMethodGetAclUidL;
				if (M_TCG_CHECK_OPAL()) {
					ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableMethodUidL, TcgMethodGenKeyUidL);
					pTcgApiStructLockingPartial2->MethodTable[ulTableIndex].ulUidL = TcgMethodGenKeyUidL;
				}
				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableMethodUidL, TcgMethodRevertSpUidL);
				pTcgApiStructLockingPartial2->MethodTable[ulTableIndex].ulUidL = TcgMethodRevertSpUidL;

				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableMethodUidL, TcgMethodGetUidL);
				pTcgApiStructLockingPartial2->MethodTable[ulTableIndex].ulUidL = TcgMethodGetUidL;

				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableMethodUidL, TcgMethodSetUidL);
				pTcgApiStructLockingPartial2->MethodTable[ulTableIndex].ulUidL = TcgMethodSetUidL;

				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableMethodUidL, TcgMethodAuthenticateUidL);
				pTcgApiStructLockingPartial2->MethodTable[ulTableIndex].ulUidL = TcgMethodAuthenticateUidL;

				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableMethodUidL, TcgMethodRandomUidL);
				pTcgApiStructLockingPartial2->MethodTable[ulTableIndex].ulUidL = TcgMethodRandomUidL;
				if (M_TCG_SUPPORT_SINGLE_USER_MODE_FEATURE()) {
					ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableMethodUidL, TcgMethodReactivateUidL);
					pTcgApiStructLockingPartial2->MethodTable[ulTableIndex].ulUidL = TcgMethodReactivateUidL;

					ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableMethodUidL, TcgMethodEraseUidL);
					pTcgApiStructLockingPartial2->MethodTable[ulTableIndex].ulUidL = TcgMethodEraseUidL;
				}
				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableTableUidL, TcgTableAceUidL);
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulUidL = TcgTableAceUidL;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulColumnNum = TCG_ACE_TABLE_COLUMN_NUM;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulRowNum = ubLockingSPACETableLength;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMinRowNum = ubLockingSPACETableLength;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMaxRowNum = ubLockingSPACETableLength;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].TableKind = TcgTableObjectKind;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMadatoryWriteGranularity = TCG_OBJECT_TABLE_WRITE_GRANULARITY;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulRecommandReadGranularity = TCG_OBJECT_TABLE_READ_GRANULARITY;

				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableTableUidL, TcgTableAuthorityUidL);
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulUidL = TcgTableAuthorityUidL;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulColumnNum = TCG_AUTHORITY_TABLE_COLUMN_NUM;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulRowNum = ubLockingSPAuthorityTableLength;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMinRowNum = ubLockingSPAuthorityTableLength;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMaxRowNum = ubLockingSPAuthorityTableLength;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].TableKind = TcgTableObjectKind;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMadatoryWriteGranularity = TCG_OBJECT_TABLE_WRITE_GRANULARITY;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulRecommandReadGranularity = TCG_OBJECT_TABLE_READ_GRANULARITY;

				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableTableUidL, TcgTableCPinUidL);
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulUidL = TcgTableCPinUidL;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulColumnNum = TCG_CPIN_TABLE_COLUMN_NUM;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulRowNum = ubLockingSPCPINTableLength;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMinRowNum = ubLockingSPCPINTableLength;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMaxRowNum = ubLockingSPCPINTableLength;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].TableKind = TcgTableObjectKind;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMadatoryWriteGranularity = TCG_OBJECT_TABLE_WRITE_GRANULARITY;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulRecommandReadGranularity = TCG_OBJECT_TABLE_READ_GRANULARITY;

				for (uli = 0; uli < ubLockingSPAdminNum; uli++) {
					ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableCPinUidL, TcgLockingSpCPinAdmin1UidL + uli);
					pTcgApiStructLockingPartial2->CPinTable[ulTableIndex].ulUidL = TcgLockingSpCPinAdmin1UidL + uli;
					pTcgApiStructLockingPartial2->CPinTable[ulTableIndex].ubTryLimit = 0;
					pTcgApiStructLockingPartial2->CPinTable[ulTableIndex].ubTries = 0;
					pTcgApiStructLockingPartial2->CPinTable[ulTableIndex].ubPersistence = FALSE;
					if ((TRUE == M_TCG_SUPPORT_SINGLE_USER_MODE_FEATURE()) && (TcgInitLockingSpModeReactivate == ubMode) && (TCG_LOCKING_SP_CPIN_ADMIN1_TABLE_IDX == uli)) {
						// depends on the Reactivate method's Admin1PIN parameter
					}
					else {
						pTcgApiStructLockingPartial2->CPinTable[ulTableIndex].ubPinLength = 0;
					}
				}

				for (uli = 0; uli < ubLockingSPUserNum; uli++) {
					ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableCPinUidL, TcgLockingSpCPinUser1UidL + uli);
					pTcgApiStructLockingPartial2->CPinTable[ulTableIndex].ulUidL = TcgLockingSpCPinUser1UidL + uli;
					pTcgApiStructLockingPartial2->CPinTable[ulTableIndex].ubTryLimit = 0;
					pTcgApiStructLockingPartial2->CPinTable[ulTableIndex].ubTries = 0;
					pTcgApiStructLockingPartial2->CPinTable[ulTableIndex].ubPersistence = FALSE;
					pTcgApiStructLockingPartial2->CPinTable[ulTableIndex].ubPinLength = 0;
				}
				if (M_TCG_CHECK_OPAL()) {
					ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableTableUidL, TcgTableSecretProtectUidL);
					pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulUidL = TcgTableSecretProtectUidL;
					pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulColumnNum = TCG_SECRET_PROTECT_TABLE_COLUMN_NUM;
					pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulRowNum = TCG_LOCKING_SP_SECRET_PROTECT_TABLE_LENGTH;
					pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMinRowNum = TCG_LOCKING_SP_SECRET_PROTECT_TABLE_LENGTH;
					pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMaxRowNum = TCG_LOCKING_SP_SECRET_PROTECT_TABLE_LENGTH;
					pTcgApiStructLockingPartial2->TableTable[ulTableIndex].TableKind = TcgTableObjectKind;
					pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMadatoryWriteGranularity = TCG_OBJECT_TABLE_WRITE_GRANULARITY;
					pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulRecommandReadGranularity = TCG_OBJECT_TABLE_READ_GRANULARITY;

					ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableSecretProtectUidL, TcgSecretProtectKeyAes256UidL);
					pTcgApiStructLockingPartial2->SecretProtectTable[ulTableIndex].ulUidL = TcgSecretProtectKeyAes256UidL;

					uli = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableTableUidL, TcgTableKeyAes256UidL);
					pTcgApiStructLockingPartial2->SecretProtectTable[ulTableIndex].ulTableTableElementAddrOffsetFromTcgLockingPart2 = (U32)&pTcgApiStructLockingPartial2->TableTable[uli] - (U32)pTcgApiStructLockingPartial2;
					pTcgApiStructLockingPartial2->SecretProtectTable[ulTableIndex].ulColumnNumber = TcgSecretProtectProtectMechanismColumn;
					pTcgApiStructLockingPartial2->SecretProtectTable[ulTableIndex].ubProtectMechanisms = TCG_SECRET_PROTECT_TABLE_PROTECT_MECHANISM;
				}
				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableTableUidL, TcgTableLockingInfoUidL);
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulUidL = TcgTableLockingInfoUidL;
				if (M_TCG_SUPPORT_SINGLE_USER_MODE_FEATURE()) {
					pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulColumnNum = TCG_LOCKING_INFO_TABLE_COLUMN_NUM_SINGLE_USER_MODE;
				}
				else {
					pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulColumnNum = TCG_LOCKING_INFO_TABLE_COLUMN_NUM;
				}
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulRowNum = TCG_LOCKING_SP_LOCKING_INFO_TABLE_LENGTH;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMinRowNum = TCG_LOCKING_SP_LOCKING_INFO_TABLE_LENGTH;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMaxRowNum = TCG_LOCKING_SP_LOCKING_INFO_TABLE_LENGTH;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].TableKind = TcgTableObjectKind;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMadatoryWriteGranularity = TCG_OBJECT_TABLE_WRITE_GRANULARITY;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulRecommandReadGranularity = TCG_OBJECT_TABLE_READ_GRANULARITY;

				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableTableUidL, TcgTableLockingUidL);
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulUidL = TcgTableLockingUidL;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulColumnNum = TCG_LOCKING_TABLE_COLUMN_NUM;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulRowNum = ubLockingSPLockingTableLength;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMinRowNum = ubLockingSPLockingTableLength;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMaxRowNum = ubLockingSPLockingTableLength;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].TableKind = TcgTableObjectKind;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMadatoryWriteGranularity = TCG_OBJECT_TABLE_WRITE_GRANULARITY;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulRecommandReadGranularity = TCG_OBJECT_TABLE_READ_GRANULARITY;

				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableTableUidL, TcgTableMBRControlUidL);
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulUidL = TcgTableMBRControlUidL;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulColumnNum = TCG_MBR_CONTROL_TABLE_COLUMN_NUM;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulRowNum = TCG_LOCKING_SP_MBR_CONTROL_TABLE_LENGTH;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMinRowNum = TCG_LOCKING_SP_MBR_CONTROL_TABLE_LENGTH;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMaxRowNum = TCG_LOCKING_SP_MBR_CONTROL_TABLE_LENGTH;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].TableKind = TcgTableObjectKind;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMadatoryWriteGranularity = TCG_OBJECT_TABLE_WRITE_GRANULARITY;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulRecommandReadGranularity = TCG_OBJECT_TABLE_READ_GRANULARITY;

				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableMBRControlUidL, TcgMBRControlUidL);
				pTcgApiStructLockingPartial2->MBRControlTable[ulTableIndex].ulUidL = TcgMBRControlUidL;
				pTcgApiStructLockingPartial2->MBRControlTable[ulTableIndex].ubEnable = FALSE;
				pTcgApiStructLockingPartial2->MBRControlTable[ulTableIndex].ubDone = FALSE;
				pTcgApiStructLockingPartial2->MBRControlTable[ulTableIndex].MBRDoneOnReset[0] = TcgPowerCycleReset;
				pTcgApiStructLockingPartial2->MBRControlTable[ulTableIndex].MBRDoneOnReset[1] = TcgProgramaticReset;
				pTcgApiStructLockingPartial2->MBRControlTable[ulTableIndex].ubMBRDoneOnResetCnt = 2;

				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableTableUidL, TcgTableMBRUidL);
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulUidL = TcgTableMBRUidL;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulColumnNum = TCG_MBR_TABLE_COLUMN_NUM;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulRowNum = TCG_MBR_TABLE_SIZE;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMinRowNum = TCG_MBR_TABLE_SIZE;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMaxRowNum = TCG_MBR_TABLE_SIZE;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].TableKind = TcgTableByteKind;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMadatoryWriteGranularity = TCG_MBR_MANDATORY_WRITE_GRANULARITY_VALUE;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulRecommandReadGranularity = TCG_MBR_RECOMMENDED_ACCESS_GRANULARITY;
				if (M_TCG_CHECK_OPAL()) {
					ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableTableUidL, TcgTableKeyAes256UidL);
					pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulUidL = TcgTableKeyAes256UidL;
					pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulColumnNum = TCG_KEY_AES_256_TABLE_COLUMN_NUM;
					pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulRowNum = TCG_OPAL_LOCKING_SP_KEY_AES_256_TABLE_LENGTH;
					pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMinRowNum = TCG_OPAL_LOCKING_SP_KEY_AES_256_TABLE_LENGTH;
					pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMaxRowNum = TCG_OPAL_LOCKING_SP_KEY_AES_256_TABLE_LENGTH;
					pTcgApiStructLockingPartial2->TableTable[ulTableIndex].TableKind = TcgTableObjectKind;
					pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMadatoryWriteGranularity = TCG_OBJECT_TABLE_WRITE_GRANULARITY;
					pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulRecommandReadGranularity = TCG_OBJECT_TABLE_READ_GRANULARITY;
				}
				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableTableUidL, TcgTableDataStoreUidL);
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulUidL = TcgTableDataStoreUidL;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulColumnNum = TCG_DATASTORE_TABLE_COLUMN_NUM;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulRowNum = TCG_DATASTORE_TABLE_SIZE_ALL;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMinRowNum = TCG_DATASTORE_TABLE_SIZE_ALL;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMaxRowNum = TCG_DATASTORE_TABLE_SIZE_ALL;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].TableKind = TcgTableByteKind;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMadatoryWriteGranularity = TCG_DATASTORE_MANDATORY_WRITE_GRANULARITY_VALUE;
				pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulRecommandReadGranularity = TCG_DATASTORE_RECOMMENDED_ACCESS_GRANULARITY;
				if (M_TCG_SUPPORT_ADDITIONAL_DATASTORE_FEATURE()) {
					for (uli = 1; uli < TCG_OPAL_LOCKING_SP_DATASTORE_NUM; uli++) {
						ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableTableUidL, TcgTableDataStore2UidL + uli - 1);
						pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulUidL = TcgTableDataStore2UidL + uli - 1;
						pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulColumnNum = TCG_DATASTORE_TABLE_COLUMN_NUM;
						pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulRowNum = 0;
						pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMinRowNum = 0;
						pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMaxRowNum = TCG_DATASTORE_TABLE_SIZE_ALL;
						pTcgApiStructLockingPartial2->TableTable[ulTableIndex].TableKind = TcgTableByteKind;
						pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulMadatoryWriteGranularity = TCG_DATASTORE_MANDATORY_WRITE_GRANULARITY_VALUE;
						pTcgApiStructLockingPartial2->TableTable[ulTableIndex].ulRecommandReadGranularity = TCG_DATASTORE_RECOMMENDED_ACCESS_GRANULARITY;
					}
				}
			}
			LockingInitState = LockingInitState_TriggerSaveLockingTable1;
		// no break;
		case LockingInitState_TriggerSaveLockingTable1:
			TcgSetOpalTableEvent(OpalTableState_Save, TcgFlashTableOriginLocking2Index, OPAL_NULL_TARGET_ADDR);
			if (gTcg.BufManager.OpalTableState == OpalTableState_Idle) {
				LockingInitState = LockingInitState_TriggerLoadLockingTable2;
				ubContinue = TRUE;
			}
			break;
		case LockingInitState_TriggerLoadLockingTable2:
			TcgSetOpalTableEvent(OpalTableState_Load, TcgFlashTableOriginLocking3Index, OPAL_NULL_TARGET_ADDR);
			if (gTcg.BufManager.OpalTableState == OpalTableState_Idle) {
				LockingInitState = LockingInitState_SetLockingTable2;
				ubContinue = TRUE;
			}
			break;
		case LockingInitState_SetLockingTable2: {
				U32 uli;
				U32 ulTableIndex;
				TcgLockingPart3Ptr_t pTcgApiStructLockingPartial3 = (TcgLockingPart3Ptr_t)gTcg.BufManager.ulBufAddr;
				U8 ubLockingSPUserNum = M_TCG_GET_LOCKING_SP_USER_NUM();

				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableSpInfoUidL, TcgSpInfoUidL);
				pTcgApiStructLockingPartial3->SpInfoTable[ulTableIndex].ulUidL = TcgSpInfoUidL;
				pTcgApiStructLockingPartial3->SpInfoTable[ulTableIndex].SpUidL = TcgSpLockingUidL;
				pTcgApiStructLockingPartial3->SpInfoTable[ulTableIndex].ubEnable = TRUE;
				pTcgApiStructLockingPartial3->SpInfoTable[ulTableIndex].uoSpSessionTimeout = TCG_SESSION_TIMEOUT_TIME;

				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableSpTemplateUidL, TcgSpTemplateBaseUidL);
				pTcgApiStructLockingPartial3->SpTemplateTable[ulTableIndex].ulUidL = TcgSpTemplateBaseUidL;
				pTcgApiStructLockingPartial3->SpTemplateTable[ulTableIndex].ulTemplateUidL = TcgTemplateBaseUidL;
				// locking
				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableSpTemplateUidL, TcgSpTemplateLockingUidL);
				pTcgApiStructLockingPartial3->SpTemplateTable[ulTableIndex].ulUidL = TcgSpTemplateLockingUidL;
				pTcgApiStructLockingPartial3->SpTemplateTable[ulTableIndex].ulTemplateUidL = TcgTemplateLockingUidL;

				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAuthorityUidL, TcgAuthorityAnybodyUidL);
				pTcgApiStructLockingPartial3->AuthorityTable[ulTableIndex].ulUidL = TcgAuthorityAnybodyUidL;
				pTcgApiStructLockingPartial3->AuthorityTable[ulTableIndex].ubClass = FALSE;
				pTcgApiStructLockingPartial3->AuthorityTable[ulTableIndex].ulAuthorityClassUidL = TcgAuthorityNullUidL;
				pTcgApiStructLockingPartial3->AuthorityTable[ulTableIndex].ubEnable = TRUE;
				pTcgApiStructLockingPartial3->AuthorityTable[ulTableIndex].AuthorityOperation = TcgAuthorityNoneOperation;
				pTcgApiStructLockingPartial3->AuthorityTable[ulTableIndex].ulCredentialCPinUidL = TcgCPinNullUidL;
				pTcgApiStructLockingPartial3->AuthorityTable[ulTableIndex].CommonName.ubLength = 0;
				// admins
				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAuthorityUidL, TcgAuthorityAdminsUidL);
				pTcgApiStructLockingPartial3->AuthorityTable[ulTableIndex].ulUidL = TcgAuthorityAdminsUidL;
				pTcgApiStructLockingPartial3->AuthorityTable[ulTableIndex].ubClass = TRUE;
				pTcgApiStructLockingPartial3->AuthorityTable[ulTableIndex].ulAuthorityClassUidL = TcgAuthorityNullUidL;
				pTcgApiStructLockingPartial3->AuthorityTable[ulTableIndex].ubEnable = TRUE;
				pTcgApiStructLockingPartial3->AuthorityTable[ulTableIndex].AuthorityOperation = TcgAuthorityNoneOperation;
				pTcgApiStructLockingPartial3->AuthorityTable[ulTableIndex].ulCredentialCPinUidL = TcgCPinNullUidL;
				pTcgApiStructLockingPartial3->AuthorityTable[ulTableIndex].CommonName.ubLength = 0;

				for (uli = 0; uli < ubLockingSPAdminNum; uli++) {
					ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAuthorityUidL, TcgLockingSpAuthorityAdmin1UidL + uli);
					pTcgApiStructLockingPartial3->AuthorityTable[ulTableIndex].ulUidL = TcgLockingSpAuthorityAdmin1UidL + uli;
					pTcgApiStructLockingPartial3->AuthorityTable[ulTableIndex].ubClass = FALSE;
					pTcgApiStructLockingPartial3->AuthorityTable[ulTableIndex].ulAuthorityClassUidL = TcgAuthorityAdminsUidL;
					if ( uli == 0 ) { /* parasoft-suppress BD-PB-CC "pyrite mode only have 1 LockingSP admin, that is uli always == 0" */
						// admin1 set to True
						pTcgApiStructLockingPartial3->AuthorityTable[ulTableIndex].ubEnable = TRUE;
					}
					else {
						// admin2 ~ adminXXXX set to False
						pTcgApiStructLockingPartial3->AuthorityTable[ulTableIndex].ubEnable = FALSE;
					}
					pTcgApiStructLockingPartial3->AuthorityTable[ulTableIndex].AuthorityOperation = TcgAuthorityPasswordOperation;
					pTcgApiStructLockingPartial3->AuthorityTable[ulTableIndex].ulCredentialCPinUidL = TcgLockingSpCPinAdmin1UidL + uli;
					pTcgApiStructLockingPartial3->AuthorityTable[ulTableIndex].CommonName.ubLength = 0;
				}
				// users
				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAuthorityUidL, TcgLockingSpAuthorityUsersUidL);
				pTcgApiStructLockingPartial3->AuthorityTable[ulTableIndex].ulUidL = TcgLockingSpAuthorityUsersUidL;
				pTcgApiStructLockingPartial3->AuthorityTable[ulTableIndex].ubClass = TRUE;
				pTcgApiStructLockingPartial3->AuthorityTable[ulTableIndex].ulAuthorityClassUidL = TcgAuthorityNullUidL;
				pTcgApiStructLockingPartial3->AuthorityTable[ulTableIndex].ubEnable = TRUE;
				pTcgApiStructLockingPartial3->AuthorityTable[ulTableIndex].AuthorityOperation = TcgAuthorityNoneOperation;
				pTcgApiStructLockingPartial3->AuthorityTable[ulTableIndex].ulCredentialCPinUidL = TcgCPinNullUidL;
				pTcgApiStructLockingPartial3->AuthorityTable[ulTableIndex].CommonName.ubLength = 0;

				for (uli = 0; uli < ubLockingSPUserNum; uli++) {
					ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAuthorityUidL, TcgLockingSpAuthorityUser1UidL + uli);
					pTcgApiStructLockingPartial3->AuthorityTable[ulTableIndex].ulUidL = TcgLockingSpAuthorityUser1UidL + uli;
					pTcgApiStructLockingPartial3->AuthorityTable[ulTableIndex].ubClass = FALSE;
					pTcgApiStructLockingPartial3->AuthorityTable[ulTableIndex].ulAuthorityClassUidL = TcgLockingSpAuthorityUsersUidL;
					pTcgApiStructLockingPartial3->AuthorityTable[ulTableIndex].ubEnable = FALSE;
					pTcgApiStructLockingPartial3->AuthorityTable[ulTableIndex].AuthorityOperation = TcgAuthorityPasswordOperation;
					pTcgApiStructLockingPartial3->AuthorityTable[ulTableIndex].ulCredentialCPinUidL = TcgLockingSpCPinUser1UidL + uli;
					pTcgApiStructLockingPartial3->AuthorityTable[ulTableIndex].CommonName.ubLength = 0;
				}

				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableLockingInfoUidL, TcgLockingInfoUidL);
				pTcgApiStructLockingPartial3->LockingInfoTable[ulTableIndex].ulUidL = TcgLockingInfoUidL;
				if (M_TCG_CHECK_OPAL()) {
					pTcgApiStructLockingPartial3->LockingInfoTable[ulTableIndex].EncryptSupport = TcgMediaEncryptSupport;
					pTcgApiStructLockingPartial3->LockingInfoTable[ulTableIndex].ulMaxLockingRangesNum = TCG_OPAL_LOCKING_SP_USER_NUM - 1;
				}
				else if (M_TCG_CHECK_PYRITE()) {
					pTcgApiStructLockingPartial3->LockingInfoTable[ulTableIndex].EncryptSupport = TcgNoneEncryptSupport;
					pTcgApiStructLockingPartial3->LockingInfoTable[ulTableIndex].ulMaxLockingRangesNum = 0;
				}
				pTcgApiStructLockingPartial3->LockingInfoTable[ulTableIndex].ubAlignmentRequired = TCG_LOCKING_RANGE_ALIGNMENT_REQUIRED;
				pTcgApiStructLockingPartial3->LockingInfoTable[ulTableIndex].uoAlignmentGranularity = TCG_OPAL_ALIGNMENT_GRANULARITY;
				pTcgApiStructLockingPartial3->LockingInfoTable[ulTableIndex].uoLowestAlignedLBA = TCG_OPAL_LOWEST_ALIGNED_LBA;
				if (M_TCG_SUPPORT_SINGLE_USER_MODE_FEATURE()) {
					pTcgApiStructLockingPartial3->LockingInfoTable[ulTableIndex].ubSingleUserModeRangesCnt = 0;
					pTcgApiStructLockingPartial3->LockingInfoTable[ulTableIndex].RangeLengthSettingPolicy = RangeLengthAdminSettingPolicy;
					pTcgApiStructLockingPartial3->LockingInfoTable[ulTableIndex].ubSingleUserModeEntireLockingTable = FALSE;
				}
				gTcg.pVT->NonVolatile.ulLockOnResetBmp = (U32)0;

				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableLockingUidL, TcgLockingGlobalRangeUidL);
				pTcgApiStructLockingPartial3->LockingTable[ulTableIndex].ulUidL = TcgLockingGlobalRangeUidL;
				pTcgApiStructLockingPartial3->LockingTable[ulTableIndex].uoStartLBA = 0;
				pTcgApiStructLockingPartial3->LockingTable[ulTableIndex].uoLBACnt = 0;
				pTcgApiStructLockingPartial3->LockingTable[ulTableIndex].ubReadLockEnable = FALSE;
				pTcgApiStructLockingPartial3->LockingTable[ulTableIndex].ubWriteLockEnable = FALSE;
				pTcgApiStructLockingPartial3->LockingTable[ulTableIndex].ubReadLock = FALSE;
				pTcgApiStructLockingPartial3->LockingTable[ulTableIndex].ubWriteLock = FALSE;
				pTcgApiStructLockingPartial3->LockingTable[ulTableIndex].LockOnReset[0] = TcgPowerCycleReset;
				pTcgApiStructLockingPartial3->LockingTable[ulTableIndex].LockOnReset[1] = TcgProgramaticReset;
				pTcgApiStructLockingPartial3->LockingTable[ulTableIndex].ubLockOnResetCnt = 2;
				if (M_TCG_CHECK_OPAL()) {
					uli = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableKeyAes256UidL, TcgKeyAes256GlobalRangeUidL);
					pTcgApiStructLockingPartial3->LockingTable[ulTableIndex].ulKeyAes256TableElementAddrOffsetFromTcgLockingPart3 = (U32)(&pTcgApiStructLockingPartial3->KeyAes256Table[uli]) - (U32)pTcgApiStructLockingPartial3;
				}
				pTcgApiStructLockingPartial3->LockingTable[ulTableIndex].CommonName.ubLength = 0;
				if (M_TCG_SUPPORT_SINGLE_USER_MODE_FEATURE()) {
					uli = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAuthorityUidL, TcgLockingSpAuthorityUser1UidL);
					pTcgApiStructLockingPartial3->LockingTable[ulTableIndex].ulAuthorityTableElementAddrOffsetFromTcgLockingPart3 = (U32)(&pTcgApiStructLockingPartial3->AuthorityTable[uli]) - (U32)pTcgApiStructLockingPartial3;
					pTcgApiStructLockingPartial3->LockingTable[ulTableIndex].ubSingleUserModeEnable = FALSE;
					pTcgApiStructLockingPartial3->LockingTable[ulTableIndex].RangeLengthSettingPolicy = TCG_LOCKING_INFO_POLICY;
				}
				if (M_TCG_CHECK_OPAL()) {
					// Locking_Range1 ~ Locking_RangeNNNN
					U32 ulj;
					for (uli = 0; uli < TCG_OPAL_LOCKING_SP_LOCKING_TABLE_LENGTH - 1; uli++) {
						ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableLockingUidL, TcgLockingRange1UidL + uli);
						pTcgApiStructLockingPartial3->LockingTable[ulTableIndex].ulUidL = TcgLockingRange1UidL + uli;
						if ((TRUE == M_TCG_SUPPORT_SINGLE_USER_MODE_FEATURE()) && (TcgInitLockingSpModeReactivate == ubMode)) {
							// remains the same
						}
						else {
							pTcgApiStructLockingPartial3->LockingTable[ulTableIndex].uoStartLBA = 0;
							pTcgApiStructLockingPartial3->LockingTable[ulTableIndex].uoLBACnt = 0;
						}
						pTcgApiStructLockingPartial3->LockingTable[ulTableIndex].ubReadLockEnable = FALSE;
						pTcgApiStructLockingPartial3->LockingTable[ulTableIndex].ubWriteLockEnable = FALSE;
						pTcgApiStructLockingPartial3->LockingTable[ulTableIndex].ubReadLock = FALSE;
						pTcgApiStructLockingPartial3->LockingTable[ulTableIndex].ubWriteLock = FALSE;
						pTcgApiStructLockingPartial3->LockingTable[ulTableIndex].LockOnReset[0] = TcgPowerCycleReset;
						pTcgApiStructLockingPartial3->LockingTable[ulTableIndex].LockOnReset[1] = TcgProgramaticReset;
						pTcgApiStructLockingPartial3->LockingTable[ulTableIndex].ubLockOnResetCnt = 2;

						ulj = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableKeyAes256UidL, TcgKeyAes256Range1UidL + uli);
						pTcgApiStructLockingPartial3->LockingTable[ulTableIndex].ulKeyAes256TableElementAddrOffsetFromTcgLockingPart3 = (U32)(&pTcgApiStructLockingPartial3->KeyAes256Table[ulj]) - (U32)pTcgApiStructLockingPartial3;
						pTcgApiStructLockingPartial3->LockingTable[ulTableIndex].CommonName.ubLength = 0;
						if (M_TCG_SUPPORT_SINGLE_USER_MODE_FEATURE()) {
							ulj = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAuthorityUidL, TcgLockingSpAuthorityUser1UidL + uli + 1);
							pTcgApiStructLockingPartial3->LockingTable[ulTableIndex].ulAuthorityTableElementAddrOffsetFromTcgLockingPart3 = (U32)(&pTcgApiStructLockingPartial3->AuthorityTable[ulj]) - (U32)pTcgApiStructLockingPartial3;
							pTcgApiStructLockingPartial3->LockingTable[ulTableIndex].ubSingleUserModeEnable = FALSE;
							pTcgApiStructLockingPartial3->LockingTable[ulTableIndex].RangeLengthSettingPolicy = TCG_LOCKING_INFO_POLICY;
						}
					}
					ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableKeyAes256UidL, TcgKeyAes256GlobalRangeUidL);
					pTcgApiStructLockingPartial3->KeyAes256Table[ulTableIndex].ulUidL = TcgKeyAes256GlobalRangeUidL;
					pTcgApiStructLockingPartial3->KeyAes256Table[ulTableIndex].SymmetricMediaEncryptMode = TcgXTSSymmetricMediaEncrypt;
					// K_AES_256_Range1_Key ~ K_AES_256_RangeNNNN_Key
					for (uli = 1; uli < TCG_OPAL_LOCKING_SP_USER_NUM; uli++) {
						ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableKeyAes256UidL, TcgKeyAes256Range1UidL + uli - 1);
						pTcgApiStructLockingPartial3->KeyAes256Table[ulTableIndex].ulUidL = TcgKeyAes256Range1UidL + uli - 1;
						pTcgApiStructLockingPartial3->KeyAes256Table[ulTableIndex].SymmetricMediaEncryptMode = TcgXTSSymmetricMediaEncrypt;
					}
				}
			}
			LockingInitState = LockingInitState_TriggerSaveLockingTable2;
		// no break;
		case LockingInitState_TriggerSaveLockingTable2:
			TcgSetOpalTableEvent(OpalTableState_Save, TcgFlashTableOriginLocking3Index, OPAL_NULL_TARGET_ADDR);
			if (gTcg.BufManager.OpalTableState == OpalTableState_Idle) {
				LockingInitState = LockingInitState_TriggerLoadLockingTable3;
				ubContinue = TRUE;
			}
			break;
		case LockingInitState_TriggerLoadLockingTable3:
			TcgSetOpalTableEvent(OpalTableState_Load, TcgFlashTableOriginLocking4Index, OPAL_NULL_TARGET_ADDR);
			if (gTcg.BufManager.OpalTableState == OpalTableState_Idle) {
				LockingInitState = LockingInitState_SetLockingTable3;
				ubContinue = TRUE;
			}
			break;
		case LockingInitState_SetLockingTable3: {
				U32 uli;
				U32 ulTableIndex;
				AceTableElementPtr_t pAceTable = NULL;
				TcgLockingPart4Ptr_t pTcgApiStructLockingPartial4 = (TcgLockingPart4Ptr_t)gTcg.BufManager.ulBufAddr;

				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceAnybodyUidL);
				pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial4);
				pAceTable->ulUidL = TcgAceAnybodyUidL; /* parasoft-suppress BD-PB-NP "confirmed by author" */
				pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAnybodyUidL;
				pAceTable->BooleanExpression.ubAuthorityCnt = 1;
				pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
				pAceTable->ubAvailableColumnCnt = 0;

				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceAdminUidL);
				pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial4);
				pAceTable->ulUidL = TcgAceAdminUidL;
				pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
				pAceTable->BooleanExpression.ubAuthorityCnt = 1;
				pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
				pAceTable->ubAvailableColumnCnt = 0;
				if (M_TCG_CHECK_OPAL()) {
					ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceAnybodyGetCommonNameUidL);
					pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial4);
					pAceTable->ulUidL = TcgAceAnybodyGetCommonNameUidL;
					pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAnybodyUidL;
					pAceTable->BooleanExpression.ubAuthorityCnt = 1;
					pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
					pAceTable->ubAvailableColumn[0] = TcgAceTableUidColumn;
					pAceTable->ubAvailableColumn[1] = TcgAceTableCommonNameColumn;
					pAceTable->ubAvailableColumnCnt = 2;
					// Base ACEs - ACE_Admin_Set_CommonName, 3
					ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceAnybodySetCommonNameUidL);
					pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial4);
					pAceTable->ulUidL = TcgAceAnybodySetCommonNameUidL;
					pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
					pAceTable->BooleanExpression.ubAuthorityCnt = 1;
					pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
					pAceTable->ubAvailableColumn[0] = TcgAceTableCommonNameColumn;
					pAceTable->ubAvailableColumnCnt = 1;
				}

				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceAceGetAllUidL);
				pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial4);
				pAceTable->ulUidL = TcgAceAceGetAllUidL;
				pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
				pAceTable->BooleanExpression.ubAuthorityCnt = 1;
				pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
				pAceTable->ubAvailableColumnCnt = 0;
				// ACE - ACE_ACE_Set_BooleanExpression, 5
				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceAceSetBooleanExpressionUidL);
				pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial4);
				pAceTable->ulUidL = TcgAceAceSetBooleanExpressionUidL;
				pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
				pAceTable->BooleanExpression.ubAuthorityCnt = 1;
				pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
				pAceTable->ubAvailableColumn[0] = TcgAceTableBooleanExpressionColumn;
				pAceTable->ubAvailableColumnCnt = 1;
				// Authority - ACE_Authority_Get_All, 6
				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceAuthorityGetAllUidL);
				pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial4);
				pAceTable->ulUidL = TcgAceAuthorityGetAllUidL;
				pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
				pAceTable->BooleanExpression.ubAuthorityCnt = 1;
				pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
				pAceTable->ubAvailableColumnCnt = 0;
				// Authority - ACE_Authority_Set_Enabled, 7
				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceAuthoritySetEnabledUidL);
				pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial4);
				pAceTable->ulUidL = TcgAceAuthoritySetEnabledUidL;
				pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
				pAceTable->BooleanExpression.ubAuthorityCnt = 1;
				pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
				pAceTable->ubAvailableColumn[0] = TcgAuthorityTableEnableColumn;
				pAceTable->ubAvailableColumnCnt = 1;
				if (M_TCG_CHECK_OPAL()) {
					// Authority - ACE_User1_Set_CommonName ~ ACE_UserMMMM_Set_CommonName
					for (uli = 0; uli < TCG_OPAL_LOCKING_SP_USER_NUM; uli++) {
						ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceUser1SetCommonNameUidL + uli);
						pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial4);
						pAceTable->ulUidL = TcgAceUser1SetCommonNameUidL + uli;
						pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
						pAceTable->BooleanExpression.ubAuthorityCnt = 1;
						pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
						pAceTable->ubAvailableColumn[0] = TcgAuthorityTableCommonNameColumn;
						pAceTable->ubAvailableColumnCnt = 1;
					}
				}
				// C_PIN - ACE_C_PIN_Admins_Get_All_NOPIN
				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceCPinAdminsGetAllNoPinUidL);
				pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial4);
				pAceTable->ulUidL = TcgAceCPinAdminsGetAllNoPinUidL;
				pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
				pAceTable->BooleanExpression.ubAuthorityCnt = 1;
				pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
				pAceTable->ubAvailableColumn[0] = TcgCPinUidColumn;
				pAceTable->ubAvailableColumn[1] = TcgCPinCharSetColumn;
				pAceTable->ubAvailableColumn[2] = TcgCPinTryLimitColumn;
				pAceTable->ubAvailableColumn[3] = TcgCPinTriesColumn;
				pAceTable->ubAvailableColumn[4] = TcgCPinPersistenceColumn;
				pAceTable->ubAvailableColumnCnt = 5;
				// C_PIN - ACE_C_PIN_Admins_Set_PIN
				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceCPinAdminsSetPinUidL);
				pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial4);
				pAceTable->ulUidL = TcgAceCPinAdminsSetPinUidL;
				pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
				pAceTable->BooleanExpression.ubAuthorityCnt = 1;
				pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
				pAceTable->ubAvailableColumn[0] = TcgCPinPinColumn;
				pAceTable->ubAvailableColumnCnt = 1;
				if (M_TCG_CHECK_OPAL()) {
					// C_PIN - ACE_C_PIN_User1_Set_PIN ~ ACE_C_PIN_UserMMMM_Set_PIN
					for (uli = 0; uli < 10; uli++) {
						ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceCPinUser1SetPinUidL + uli);
						pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial4);
						pAceTable->ulUidL = TcgAceCPinUser1SetPinUidL + uli;
						pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
						pAceTable->BooleanExpression.ACElementContent[1].AuthorityUidL = TcgLockingSpAuthorityUser1UidL + uli;
						pAceTable->BooleanExpression.ubAuthorityCnt = 2;
						pAceTable->BooleanExpression.ACElementContent[0].BooleanAce = TcgOrBoolean;
						pAceTable->BooleanExpression.ubBooleanAceCnt = 1;
						pAceTable->ubAvailableColumn[0] = TcgCPinPinColumn;
						pAceTable->ubAvailableColumnCnt = 1;
					}
				}
				else if (M_TCG_CHECK_PYRITE()) {
					//2
					// C_PIN - ACE_C_PIN_User1_Set_PIN ~ ACE_C_PIN_UserMMMM_Set_PIN
					for (uli = 0; uli < TCG_PYRITE_LOCKING_SP_USER_NUM; uli++) {
						ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceCPinUser1SetPinUidL + uli);
						pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial4);
						pAceTable->ulUidL = TcgAceCPinUser1SetPinUidL + uli;
						pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
						pAceTable->BooleanExpression.ACElementContent[1].AuthorityUidL = TcgLockingSpAuthorityUser1UidL + uli;
						pAceTable->BooleanExpression.ubAuthorityCnt = 2;
						pAceTable->BooleanExpression.ACElementContent[0].BooleanAce = TcgOrBoolean;
						pAceTable->BooleanExpression.ubBooleanAceCnt = 1;
						pAceTable->ubAvailableColumn[0] = TcgCPinPinColumn;
						pAceTable->ubAvailableColumnCnt = 1;
					}
					uli = 0;
					{
						ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceLockingGlobalRangeGetRangeStartToActiveKeyUidL + uli);
						pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial4);
						pAceTable->ulUidL = TcgAceLockingGlobalRangeGetRangeStartToActiveKeyUidL + uli;
						pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
						pAceTable->BooleanExpression.ubAuthorityCnt = 1;
						pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
						pAceTable->ubAvailableColumn[0] = TcgLockingRangeStartColumn;
						pAceTable->ubAvailableColumn[1] = TcgLockingRangeLengthColumn;
						pAceTable->ubAvailableColumn[2] = TcgLockingReadLockEnabledColumn;
						pAceTable->ubAvailableColumn[3] = TcgLockingWriteLockEnabledColumn;
						pAceTable->ubAvailableColumn[4] = TcgLockingReadLockedColumn;
						pAceTable->ubAvailableColumn[5] = TcgLockingWriteLockedColumn;
						pAceTable->ubAvailableColumn[6] = TcgLockingLockOnResetColumn;
						pAceTable->ubAvailableColumn[7] = TcgLockingActiveKeyColumn;
						pAceTable->ubAvailableColumnCnt = 8;
					}
					uli = 0;
					{
						ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceLockingGlobalRangeSetReadLockedUidL + uli);
						pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial4);
						pAceTable->ulUidL = TcgAceLockingGlobalRangeSetReadLockedUidL + uli;
						pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
						pAceTable->BooleanExpression.ubAuthorityCnt = 1;
						pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
						pAceTable->ubAvailableColumn[0] = TcgLockingReadLockedColumn;
						pAceTable->ubAvailableColumnCnt = 1;
					}
					uli = 0;
					{
						ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceLockingGlobalRangeSetWriteLockedUidL + uli);
						pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial4);
						pAceTable->ulUidL = TcgAceLockingGlobalRangeSetWriteLockedUidL + uli;
						pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
						pAceTable->BooleanExpression.ubAuthorityCnt = 1;
						pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
						pAceTable->ubAvailableColumn[0] = TcgLockingWriteLockedColumn;
						pAceTable->ubAvailableColumnCnt = 1;
					}
					ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceLockingGlobalRangeAdminsSetUidL);
					pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial4);
					pAceTable->ulUidL = TcgAceLockingGlobalRangeAdminsSetUidL;
					pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
					pAceTable->BooleanExpression.ubAuthorityCnt = 1;
					pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
					pAceTable->ubAvailableColumn[0] = TcgLockingReadLockEnabledColumn;
					pAceTable->ubAvailableColumn[1] = TcgLockingWriteLockEnabledColumn;
					pAceTable->ubAvailableColumn[2] = TcgLockingReadLockedColumn;
					pAceTable->ubAvailableColumn[3] = TcgLockingWriteLockedColumn;
					pAceTable->ubAvailableColumn[4] = TcgLockingLockOnResetColumn;
					pAceTable->ubAvailableColumnCnt = 5;

					ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceMBRControlAdminsSetUidL);
					pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial4);
					pAceTable->ulUidL = TcgAceMBRControlAdminsSetUidL;
					pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
					pAceTable->BooleanExpression.ubAuthorityCnt = 1;
					pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
					pAceTable->ubAvailableColumn[0] = TcgMBRControlEnableColumn;
					pAceTable->ubAvailableColumn[1] = TcgMBRControlDoneColumn;
					pAceTable->ubAvailableColumn[2] = TcgMBRControlDoneOnResetColumn;
					pAceTable->ubAvailableColumnCnt = 3;

					ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceMBRControlSetDoneToDoneOnResetUidL);
					pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial4);
					pAceTable->ulUidL = TcgAceMBRControlSetDoneToDoneOnResetUidL;
					pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
					pAceTable->BooleanExpression.ubAuthorityCnt = 1;
					pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
					pAceTable->ubAvailableColumn[0] = TcgMBRControlDoneColumn;
					pAceTable->ubAvailableColumn[1] = TcgMBRControlDoneOnResetColumn;
					pAceTable->ubAvailableColumnCnt = 2;

					ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceDataStoreGetAllUidL);
					pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial4);
					pAceTable->ulUidL = TcgAceDataStoreGetAllUidL;
					pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
					pAceTable->BooleanExpression.ubAuthorityCnt = 1;
					pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
					pAceTable->ubAvailableColumnCnt = 0;


					ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceDataStoreSetAllUidL);
					pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial4);
					pAceTable->ulUidL = TcgAceDataStoreSetAllUidL;
					pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
					pAceTable->BooleanExpression.ubAuthorityCnt = 1;
					pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
					pAceTable->ubAvailableColumnCnt = 0;
				}
			}
			LockingInitState = LockingInitState_TriggerSaveLockingTable3;
		// no break;
		case LockingInitState_TriggerSaveLockingTable3:
			TcgSetOpalTableEvent(OpalTableState_Save, TcgFlashTableOriginLocking4Index, OPAL_NULL_TARGET_ADDR);
			if (gTcg.BufManager.OpalTableState == OpalTableState_Idle) {
				if (M_TCG_CHECK_OPAL()) {
					LockingInitState = LockingInitState_TriggerLoadLockingTable4;
				}
				else if (M_TCG_CHECK_PYRITE()) {
					LockingInitState = LockingInitState_Done;
				}
				ubContinue = TRUE;
			}
			break;
		case LockingInitState_TriggerLoadLockingTable4:
			TcgSetOpalTableEvent(OpalTableState_Load, TcgFlashTableOriginLocking5Index, OPAL_NULL_TARGET_ADDR);
			if (gTcg.BufManager.OpalTableState == OpalTableState_Idle) {
				LockingInitState = LockingInitState_SetLockingTable4;
				ubContinue = TRUE;
			}
			break;
		case LockingInitState_SetLockingTable4: {
				U32 uli;
				U32 ulTableIndex;
				AceTableElementPtr_t pAceTable = NULL;
				TcgLockingPart5Ptr_t pTcgApiStructLockingPartial5 = (TcgLockingPart5Ptr_t)gTcg.BufManager.ulBufAddr;
				U8 ubLockingSPUserNum = M_TCG_GET_LOCKING_SP_USER_NUM();

				for (uli = 10; uli < ubLockingSPUserNum; uli++) {
					ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceCPinUser1SetPinUidL + uli);
					pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial5);
					pAceTable->ulUidL = TcgAceCPinUser1SetPinUidL + uli;
					pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
					pAceTable->BooleanExpression.ACElementContent[1].AuthorityUidL = TcgLockingSpAuthorityUser1UidL + uli;
					pAceTable->BooleanExpression.ubAuthorityCnt = 2;
					pAceTable->BooleanExpression.ACElementContent[0].BooleanAce = TcgOrBoolean;
					pAceTable->BooleanExpression.ubBooleanAceCnt = 1;
					pAceTable->ubAvailableColumn[0] = TcgCPinPinColumn;
					pAceTable->ubAvailableColumnCnt = 1;
				}
				if (M_TCG_CHECK_OPAL()) {
					ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceCPinAdminsGetAllNoPinVendorUidL);//[To be modified] Undefine in Spec
					pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial5);
					pAceTable->ulUidL = TcgAceCPinAdminsGetAllNoPinVendorUidL;
					pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAnybodyUidL;
					pAceTable->BooleanExpression.ubAuthorityCnt = 1;
					pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
					pAceTable->ubAvailableColumn[0] = TcgCPinUidColumn;
					pAceTable->ubAvailableColumn[1] = TcgCPinCharSetColumn;
					pAceTable->ubAvailableColumn[2] = TcgCPinTryLimitColumn;
					pAceTable->ubAvailableColumn[3] = TcgCPinTriesColumn;
					pAceTable->ubAvailableColumn[4] = TcgCPinPersistenceColumn;
					pAceTable->ubAvailableColumnCnt = 5;

					ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceCPinAdminsSetPinVendorUidL);//[To be modified] Undefine in Spec
					pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial5);
					pAceTable->ulUidL = TcgAceCPinAdminsSetPinVendorUidL;
					pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
					pAceTable->BooleanExpression.ubAuthorityCnt = 1;
					pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
					pAceTable->ubAvailableColumn[0] = TcgCPinPinColumn;
					pAceTable->ubAvailableColumn[1] = TcgCPinTryLimitColumn;
					pAceTable->ubAvailableColumn[2] = TcgCPinPersistenceColumn;
					pAceTable->ubAvailableColumnCnt = 3;

					for (uli = 0; uli < TCG_OPAL_LOCKING_SP_USER_NUM; uli++) {
						ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceCPinUser1SetPinVendorUidL + uli);//[To be modified] Undefine in Spec
						pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial5);
						pAceTable->ulUidL = TcgAceCPinUser1SetPinVendorUidL + uli;
						pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
						pAceTable->BooleanExpression.ACElementContent[1].AuthorityUidL = TcgLockingSpAuthorityUser1UidL + uli;
						pAceTable->BooleanExpression.ubAuthorityCnt = 2;
						pAceTable->BooleanExpression.ACElementContent[0].BooleanAce = TcgOrBoolean;
						pAceTable->BooleanExpression.ubBooleanAceCnt = 1;

						pAceTable->ubAvailableColumn[0] = TcgCPinPinColumn;
						pAceTable->ubAvailableColumn[1] = TcgCPinTryLimitColumn;
						pAceTable->ubAvailableColumn[2] = TcgCPinPersistenceColumn;
						pAceTable->ubAvailableColumnCnt = 3;
					}

					ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceKeyAesModeUidL);
					pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial5);
					pAceTable->ulUidL = TcgAceKeyAesModeUidL;
					pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAnybodyUidL;
					pAceTable->BooleanExpression.ubAuthorityCnt = 1;
					pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
					pAceTable->ubAvailableColumn[0] = TcgKeyAesModeColumn;
					pAceTable->ubAvailableColumnCnt = 1;

					for (uli = 0; uli < 11 ; uli++) {
						ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceKeyAes256GlobalRangeGenKeyUidL + uli);
						pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial5);
						pAceTable->ulUidL = TcgAceKeyAes256GlobalRangeGenKeyUidL + uli;
						pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
						pAceTable->BooleanExpression.ubAuthorityCnt = 1;
						pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
						pAceTable->ubAvailableColumnCnt = 0;
					}
				}
			}
			LockingInitState = LockingInitState_TriggerSaveLockingTable4;
		// no break;
		case LockingInitState_TriggerSaveLockingTable4:
			TcgSetOpalTableEvent(OpalTableState_Save, TcgFlashTableOriginLocking5Index, OPAL_NULL_TARGET_ADDR);
			if (gTcg.BufManager.OpalTableState == OpalTableState_Idle) {
				LockingInitState = LockingInitState_TriggerLoadLockingTable5;
				ubContinue = TRUE;
			}
			break;
		case LockingInitState_TriggerLoadLockingTable5:
			TcgSetOpalTableEvent(OpalTableState_Load, TcgFlashTableOriginLocking6Index, OPAL_NULL_TARGET_ADDR);
			if (gTcg.BufManager.OpalTableState == OpalTableState_Idle) {
				LockingInitState = LockingInitState_SetLockingTable5;
				ubContinue = TRUE;
			}
			break;
		case LockingInitState_SetLockingTable5: {
				U32 uli = 0;
				U32 ulTableIndex;
				AceTableElementPtr_t pAceTable = NULL;
				TcgLockingPart6Ptr_t pTcgApiStructLockingPartial6 = (TcgLockingPart6Ptr_t)gTcg.BufManager.ulBufAddr;
				if (M_TCG_CHECK_OPAL()) {
					//5
					for (uli = 11; uli < TCG_OPAL_LOCKING_SP_USER_NUM; uli++) {
						ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceKeyAes256GlobalRangeGenKeyUidL + uli);
						pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial6);
						pAceTable->ulUidL = TcgAceKeyAes256GlobalRangeGenKeyUidL + uli;
						pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
						pAceTable->BooleanExpression.ubAuthorityCnt = 1;
						pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
						pAceTable->ubAvailableColumnCnt = 0;
					}
				}
				//5 + 16
				for (uli = 0; uli < TCG_OPAL_LOCKING_SP_USER_NUM; uli++) {
					ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceLockingGlobalRangeGetRangeStartToActiveKeyUidL + uli);
					pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial6);
					pAceTable->ulUidL = TcgAceLockingGlobalRangeGetRangeStartToActiveKeyUidL + uli; /* parasoft-suppress BD-PB-NP "confirmed by author" */
					pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
					pAceTable->BooleanExpression.ubAuthorityCnt = 1;
					pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
					pAceTable->ubAvailableColumn[0] = TcgLockingRangeStartColumn;
					pAceTable->ubAvailableColumn[1] = TcgLockingRangeLengthColumn;
					pAceTable->ubAvailableColumn[2] = TcgLockingReadLockEnabledColumn;
					pAceTable->ubAvailableColumn[3] = TcgLockingWriteLockEnabledColumn;
					pAceTable->ubAvailableColumn[4] = TcgLockingReadLockedColumn;
					pAceTable->ubAvailableColumn[5] = TcgLockingWriteLockedColumn;
					pAceTable->ubAvailableColumn[6] = TcgLockingLockOnResetColumn;
					pAceTable->ubAvailableColumn[7] = TcgLockingActiveKeyColumn;
					pAceTable->ubAvailableColumnCnt = 8;
					if (M_TCG_CHECK_PYRITE()) { // Pyrite only has one range (global range), and number of user(2) is not equal to range(1)
						break;
					}
				}

				//5 + 16 + 15
				for (uli = 0; uli < 15; uli++) {
					ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceLockingGlobalRangeSetReadLockedUidL + uli);
					pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial6);
					pAceTable->ulUidL = TcgAceLockingGlobalRangeSetReadLockedUidL + uli;
					pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
					pAceTable->BooleanExpression.ubAuthorityCnt = 1;
					pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
					pAceTable->ubAvailableColumn[0] = TcgLockingReadLockedColumn;
					pAceTable->ubAvailableColumnCnt = 1;
					if (M_TCG_CHECK_PYRITE()) { // Pyrite only has one range (global range), and number of user(2) is not equal to range(1)
						break;
					}
				}
			}
			LockingInitState = LockingInitState_TriggerSaveLockingTable5;
		// no break;
		case LockingInitState_TriggerSaveLockingTable5:
			TcgSetOpalTableEvent(OpalTableState_Save, TcgFlashTableOriginLocking6Index, OPAL_NULL_TARGET_ADDR);
			if (gTcg.BufManager.OpalTableState == OpalTableState_Idle) {
				LockingInitState = LockingInitState_TriggerLoadLockingTable6;
				ubContinue = TRUE;
			}
			break;
		case LockingInitState_TriggerLoadLockingTable6:
			TcgSetOpalTableEvent(OpalTableState_Load, TcgFlashTableOriginLocking7Index, OPAL_NULL_TARGET_ADDR);
			if (gTcg.BufManager.OpalTableState == OpalTableState_Idle) {
				LockingInitState = LockingInitState_SetLockingTable6;
				ubContinue = TRUE;
			}
			break;
		case LockingInitState_SetLockingTable6: {
				U32 uli = 0;
				U32 ulTableIndex;
				AceTableElementPtr_t pAceTable = NULL;
				TcgLockingPart7Ptr_t pTcgApiStructLockingPartial7 = (TcgLockingPart7Ptr_t)gTcg.BufManager.ulBufAddr;

				//1
				for (uli = (M_TCG_CHECK_OPAL() ? 15 : 0); uli < TCG_OPAL_LOCKING_SP_USER_NUM; uli++) {
					ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceLockingGlobalRangeSetReadLockedUidL + uli);
					pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial7);
					pAceTable->ulUidL = TcgAceLockingGlobalRangeSetReadLockedUidL + uli; /* parasoft-suppress BD-PB-NP "confirmed by author" */
					pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
					pAceTable->BooleanExpression.ubAuthorityCnt = 1;
					pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
					pAceTable->ubAvailableColumn[0] = TcgLockingReadLockedColumn;
					pAceTable->ubAvailableColumnCnt = 1;
					if (M_TCG_CHECK_PYRITE()) { // Pyrite only has one range (global range), and number of user(2) is not equal to range(1)
						break;
					}
				}
				// 1 + 16
				for (uli = 0; uli < TCG_OPAL_LOCKING_SP_USER_NUM; uli++) {
					ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceLockingGlobalRangeSetWriteLockedUidL + uli);
					pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial7);
					pAceTable->ulUidL = TcgAceLockingGlobalRangeSetWriteLockedUidL + uli;
					pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
					pAceTable->BooleanExpression.ubAuthorityCnt = 1;
					pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
					pAceTable->ubAvailableColumn[0] = TcgLockingWriteLockedColumn;
					pAceTable->ubAvailableColumnCnt = 1;
					if (M_TCG_CHECK_PYRITE()) { // Pyrite only has one range (global range), and number of user(2) is not equal to range(1)
						break;
					}
				}
				// 1 + 16 + 1
				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceLockingGlobalRangeAdminsSetUidL);
				pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial7);
				pAceTable->ulUidL = TcgAceLockingGlobalRangeAdminsSetUidL;
				pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
				pAceTable->BooleanExpression.ubAuthorityCnt = 1;
				pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
				pAceTable->ubAvailableColumn[0] = TcgLockingReadLockEnabledColumn;
				pAceTable->ubAvailableColumn[1] = TcgLockingWriteLockEnabledColumn;
				pAceTable->ubAvailableColumn[2] = TcgLockingReadLockedColumn;
				pAceTable->ubAvailableColumn[3] = TcgLockingWriteLockedColumn;
				pAceTable->ubAvailableColumn[4] = TcgLockingLockOnResetColumn;
				pAceTable->ubAvailableColumnCnt = 5;
				if (M_TCG_CHECK_OPAL()) {
					// locking - ACE_Locking_Admins_RangeStartToLOR
					// 1 + 16 + 1 + 1
					ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceLockingAdminsRangeStartToLockOnResetUidL);
					pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial7);
					pAceTable->ulUidL = TcgAceLockingAdminsRangeStartToLockOnResetUidL;
					pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
					pAceTable->BooleanExpression.ubAuthorityCnt = 1;
					pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
					pAceTable->ubAvailableColumn[0] = TcgLockingRangeStartColumn;
					pAceTable->ubAvailableColumn[1] = TcgLockingRangeLengthColumn;
					pAceTable->ubAvailableColumn[2] = TcgLockingReadLockEnabledColumn;
					pAceTable->ubAvailableColumn[3] = TcgLockingWriteLockEnabledColumn;
					pAceTable->ubAvailableColumn[4] = TcgLockingReadLockedColumn;
					pAceTable->ubAvailableColumn[5] = TcgLockingWriteLockedColumn;
					pAceTable->ubAvailableColumn[6] = TcgLockingLockOnResetColumn;
					pAceTable->ubAvailableColumnCnt = 7;
				}
				// 1 + 16 + 1 + 1 + 1
				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceMBRControlAdminsSetUidL);
				pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial7);
				pAceTable->ulUidL = TcgAceMBRControlAdminsSetUidL;
				pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
				pAceTable->BooleanExpression.ubAuthorityCnt = 1;
				pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
				pAceTable->ubAvailableColumn[0] = TcgMBRControlEnableColumn;
				pAceTable->ubAvailableColumn[1] = TcgMBRControlDoneColumn;
				pAceTable->ubAvailableColumn[2] = TcgMBRControlDoneOnResetColumn;
				pAceTable->ubAvailableColumnCnt = 3;
				// 1 + 16 + 1 + 1 + 1 + 1
				// MBRControl - ACE_MBRControl_Set_DonToDOR
				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceMBRControlSetDoneToDoneOnResetUidL);
				pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial7);
				pAceTable->ulUidL = TcgAceMBRControlSetDoneToDoneOnResetUidL;
				pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
				pAceTable->BooleanExpression.ubAuthorityCnt = 1;
				pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
				pAceTable->ubAvailableColumn[0] = TcgMBRControlDoneColumn;
				pAceTable->ubAvailableColumn[1] = TcgMBRControlDoneOnResetColumn;
				pAceTable->ubAvailableColumnCnt = 2;
				// 1 + 16 + 1 + 1 + 1 + 1 + 1
				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceDataStoreGetAllUidL);
				pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial7);
				pAceTable->ulUidL = TcgAceDataStoreGetAllUidL;
				pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
				pAceTable->BooleanExpression.ubAuthorityCnt = 1;
				pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
				pAceTable->ubAvailableColumnCnt = 0;
				// 1 + 16 + 1 + 1 + 1 + 1 + 1 + 1
				// DataStore - ACE_DataStore_Set_All
				ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceDataStoreSetAllUidL);
				pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial7);
				pAceTable->ulUidL = TcgAceDataStoreSetAllUidL;
				pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
				pAceTable->BooleanExpression.ubAuthorityCnt = 1;
				pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
				pAceTable->ubAvailableColumnCnt = 0;
				// 1 + 16 + 1 + 1 + 1 + 1 + 1 + 1 + 12
				if (M_TCG_SUPPORT_ADDITIONAL_DATASTORE_FEATURE()) {
					for (uli = 1; uli < 7; uli++) {
						// ACE_DataStore2_Get_All ~ ACE_DataStoreXXXX_Get_All
						ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceDataStoreGetAllUidL + (uli * 2));
						pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial7);
						pAceTable->ulUidL = TcgAceDataStoreGetAllUidL + (uli * 2);
						pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
						pAceTable->BooleanExpression.ubAuthorityCnt = 1;
						pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
						pAceTable->ubAvailableColumnCnt = 0;
						// ACE_DataStore2_Set_All ~ ACE_DataStoreXXXX_Set_All
						ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceDataStoreSetAllUidL + (uli * 2));
						pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial7);
						pAceTable->ulUidL = TcgAceDataStoreSetAllUidL + (uli * 2);
						pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
						pAceTable->BooleanExpression.ubAuthorityCnt = 1;
						pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
						pAceTable->ubAvailableColumnCnt = 0;
					}
					// 1 + 16 + 1 + 1 + 1 + 1 + 1 + 1 + 12 + 1
					ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceDataStoreGetAllUidL + (7 * 2));
					pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial7);
					pAceTable->ulUidL = TcgAceDataStoreGetAllUidL + (7 * 2);
					pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
					pAceTable->BooleanExpression.ubAuthorityCnt = 1;
					pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
					pAceTable->ubAvailableColumnCnt = 0;
				}
			}
			LockingInitState = LockingInitState_TriggerSaveLockingTable6;
		// no break;
		case LockingInitState_TriggerSaveLockingTable6:
			TcgSetOpalTableEvent(OpalTableState_Save, TcgFlashTableOriginLocking7Index, OPAL_NULL_TARGET_ADDR);
			if (gTcg.BufManager.OpalTableState == OpalTableState_Idle) {
				LockingInitState = LockingInitState_TriggerLoadLockingTable7;
				ubContinue = TRUE;
			}
			break;
		case LockingInitState_TriggerLoadLockingTable7:
			TcgSetOpalTableEvent(OpalTableState_Load, TcgFlashTableOriginLocking8Index, OPAL_NULL_TARGET_ADDR);
			if (gTcg.BufManager.OpalTableState == OpalTableState_Idle) {
				LockingInitState = LockingInitState_SetLockingTable7;
				ubContinue = TRUE;
			}
			break;
		case LockingInitState_SetLockingTable7: {
				// 1
				if (M_TCG_SUPPORT_ADDITIONAL_DATASTORE_FEATURE()) {
					U32 uli;
					U32 ulTableIndex;
					AceTableElementPtr_t pAceTable = NULL;
					TcgLockingPart8Ptr_t pTcgApiStructLockingPartial8 = (TcgLockingPart8Ptr_t)gTcg.BufManager.ulBufAddr;
					ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceDataStoreSetAllUidL + (7 * 2));
					pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial8);
					pAceTable->ulUidL = TcgAceDataStoreSetAllUidL + (7 * 2);
					pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
					pAceTable->BooleanExpression.ubAuthorityCnt = 1;
					pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
					pAceTable->ubAvailableColumnCnt = 0;
					// 1 + 16
					for (uli = 8; uli < TCG_OPAL_LOCKING_SP_DATASTORE_NUM; uli++) {
						// ACE_DataStore2_Get_All ~ ACE_DataStoreXXXX_Get_All
						ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceDataStoreGetAllUidL + (uli * 2));
						pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial8);
						pAceTable->ulUidL = TcgAceDataStoreGetAllUidL + (uli * 2);
						pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
						pAceTable->BooleanExpression.ubAuthorityCnt = 1;
						pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
						pAceTable->ubAvailableColumnCnt = 0;
						// ACE_DataStore2_Set_All ~ ACE_DataStoreXXXX_Set_All
						ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceDataStoreSetAllUidL + (uli * 2));
						pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial8);
						pAceTable->ulUidL = TcgAceDataStoreSetAllUidL + (uli * 2);
						pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
						pAceTable->BooleanExpression.ubAuthorityCnt = 1;
						pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
						pAceTable->ubAvailableColumnCnt = 0;
					}
				}
				if (M_TCG_SUPPORT_SINGLE_USER_MODE_FEATURE()) {
					U32 uli;
					U32 ulTableIndex;
					AceTableElementPtr_t pAceTable = NULL;
					TcgLockingPart8Ptr_t pTcgApiStructLockingPartial8 = (TcgLockingPart8Ptr_t)gTcg.BufManager.ulBufAddr;
					// 1 + 16 + 1
					ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceSpReactivateAdminUidL);
					pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial8);
					pAceTable->ulUidL = TcgAceSpReactivateAdminUidL;
					pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
					pAceTable->BooleanExpression.ubAuthorityCnt = 1;
					pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
					pAceTable->ubAvailableColumnCnt = 0;
					// 1 + 16 + 1 + 1
					ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceLockingGlobalRangeSetReadLockEnabledToLockOnResetUidL);
					pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial8);
					pAceTable->ulUidL = TcgAceLockingGlobalRangeSetReadLockEnabledToLockOnResetUidL;
					pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgLockingSpAuthorityUser1UidL;
					pAceTable->BooleanExpression.ubAuthorityCnt = 1;
					pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
					pAceTable->ubAvailableColumn[0] = TcgLockingReadLockEnabledColumn;
					pAceTable->ubAvailableColumn[1] = TcgLockingWriteLockEnabledColumn;
					pAceTable->ubAvailableColumn[2] = TcgLockingReadLockedColumn;
					pAceTable->ubAvailableColumn[3] = TcgLockingWriteLockedColumn;
					pAceTable->ubAvailableColumn[4] = TcgLockingLockOnResetColumn;
					pAceTable->ubAvailableColumnCnt = 5;
					// 1 + 16 + 1 + 1 + 1
					ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceLockingGlobalRangeEraseUidL);
					pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial8);
					pAceTable->ulUidL = TcgAceLockingGlobalRangeEraseUidL;
					pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
					pAceTable->BooleanExpression.ACElementContent[1].AuthorityUidL = TcgLockingSpAuthorityUser1UidL;
					pAceTable->BooleanExpression.ubAuthorityCnt = 2;
					pAceTable->BooleanExpression.ACElementContent[0].BooleanAce = TcgOrBoolean;
					pAceTable->BooleanExpression.ubBooleanAceCnt = 1;
					pAceTable->ubAvailableColumnCnt = 0;
					// 1 + 16 + 1 + 1 + 1 + 15
					for (uli = 0; uli <  TCG_OPAL_LOCKING_SP_USER_NUM - 1; uli++) {
						ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceLockingRange1SetReadLockEnabledToLockOnResetUidL + uli);
						pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial8);
						pAceTable->ulUidL = TcgAceLockingRange1SetReadLockEnabledToLockOnResetUidL + uli;
						pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgLockingSpAuthorityUser1UidL + uli + 1;
						pAceTable->BooleanExpression.ubAuthorityCnt = 1;
						pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
						pAceTable->ubAvailableColumn[0] = TcgLockingReadLockEnabledColumn;
						pAceTable->ubAvailableColumn[1] = TcgLockingWriteLockEnabledColumn;
						pAceTable->ubAvailableColumn[2] = TcgLockingReadLockedColumn;
						pAceTable->ubAvailableColumn[3] = TcgLockingWriteLockedColumn;
						pAceTable->ubAvailableColumn[4] = TcgLockingLockOnResetColumn;
						pAceTable->ubAvailableColumnCnt = 5;
					}
					// 1 + 16 + 1 + 1 + 1 + 15 + 1
					ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceLockingRange1EraseUidL + 0);
					pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial8);
					pAceTable->ulUidL = TcgAceLockingRange1EraseUidL + 0;
					pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
					pAceTable->BooleanExpression.ACElementContent[1].AuthorityUidL = TcgLockingSpAuthorityUser1UidL + 0 + 1 ;
					pAceTable->BooleanExpression.ubAuthorityCnt = 2;
					pAceTable->BooleanExpression.ACElementContent[0].BooleanAce = TcgOrBoolean;
					pAceTable->BooleanExpression.ubBooleanAceCnt = 1;
					pAceTable->ubAvailableColumnCnt = 0;
				}
			}
			LockingInitState = LockingInitState_TriggerSaveLockingTable7;
		// no break;
		case LockingInitState_TriggerSaveLockingTable7:
			TcgSetOpalTableEvent(OpalTableState_Save, TcgFlashTableOriginLocking8Index, OPAL_NULL_TARGET_ADDR);
			if (gTcg.BufManager.OpalTableState == OpalTableState_Idle) {
				LockingInitState = LockingInitState_TriggerLoadLockingTable8;
				ubContinue = TRUE;
			}
			break;
		case LockingInitState_TriggerLoadLockingTable8:
			TcgSetOpalTableEvent(OpalTableState_Load, TcgFlashTableOriginLocking9Index, OPAL_NULL_TARGET_ADDR);
			if (gTcg.BufManager.OpalTableState == OpalTableState_Idle) {
				LockingInitState = LockingInitState_SetLockingTable8;
				ubContinue = TRUE;
			}
			break;
		case LockingInitState_SetLockingTable8: {
				// 1
				// ACE_Locking_RaneNNNN_Set_RangeStartToRangeLength
				if (M_TCG_SUPPORT_SINGLE_USER_MODE_FEATURE()) {
					U32 uli;
					U32 ulTableIndex;
					AceTableElementPtr_t pAceTable = NULL;
					TcgLockingPart9Ptr_t pTcgApiStructLockingPartial9 = (TcgLockingPart9Ptr_t)gTcg.BufManager.ulBufAddr;
					ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceLockingRange1SetRangeStartToRangeLengthUidL + 0);
					pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial9);
					pAceTable->ulUidL = TcgAceLockingRange1SetRangeStartToRangeLengthUidL + 0;
					if ( TCG_LOCKING_INFO_POLICY == RangeLengthUserSettingPolicy ) {
						pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgLockingSpAuthorityUser1UidL + 0 + 1;
					}
					else if ( TCG_LOCKING_INFO_POLICY == RangeLengthAdminSettingPolicy ) {
						pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
					}
					pAceTable->BooleanExpression.ubAuthorityCnt = 1;
					pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
					pAceTable->ubAvailableColumn[0] = TcgLockingRangeStartColumn;
					pAceTable->ubAvailableColumn[1] = TcgLockingRangeLengthColumn;
					pAceTable->ubAvailableColumnCnt = 2;
					// 1 + 28
					for (uli = 1; uli < TCG_OPAL_LOCKING_SP_USER_NUM - 1; uli++) {
						// ACE_Locking_GlobalRange_Erase ~ ACE_Locking_RangeNNNN_Erase
						ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceLockingRange1EraseUidL + uli);
						pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial9);
						pAceTable->ulUidL = TcgAceLockingRange1EraseUidL + uli;
						pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
						pAceTable->BooleanExpression.ACElementContent[1].AuthorityUidL = TcgLockingSpAuthorityUser1UidL + uli + 1 ;
						pAceTable->BooleanExpression.ubAuthorityCnt = 2;
						pAceTable->BooleanExpression.ACElementContent[0].BooleanAce = TcgOrBoolean;
						pAceTable->BooleanExpression.ubBooleanAceCnt = 1;
						pAceTable->ubAvailableColumnCnt = 0;
						// ACE_Locking_RaneNNNN_Set_RangeStartToRangeLength
						ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceLockingRange1SetRangeStartToRangeLengthUidL + uli);
						pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial9);
						pAceTable->ulUidL = TcgAceLockingRange1SetRangeStartToRangeLengthUidL + uli;
						if ( TCG_LOCKING_INFO_POLICY == RangeLengthUserSettingPolicy ) {
							pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgLockingSpAuthorityUser1UidL + uli + 1;
						}
						else if ( TCG_LOCKING_INFO_POLICY == RangeLengthAdminSettingPolicy ) {
							pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAdminsUidL;
						}
						pAceTable->BooleanExpression.ubAuthorityCnt = 1;
						pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
						pAceTable->ubAvailableColumn[0] = TcgLockingRangeStartColumn;
						pAceTable->ubAvailableColumn[1] = TcgLockingRangeLengthColumn;
						pAceTable->ubAvailableColumnCnt = 2;
					}

					// 1 + 28 + 1
					ulTableIndex = TcgGetTableIndexAssert(TcgSpLockingUidL, TcgTableAceUidL, TcgAceCPinAnybodyGetNoPinUidL);
					pAceTable = TcgGetLockingSpAceTableElementPtr(ulTableIndex, (U32)pTcgApiStructLockingPartial9);
					pAceTable->ulUidL = TcgAceCPinAnybodyGetNoPinUidL;
					pAceTable->BooleanExpression.ACElementContent[0].AuthorityUidL = TcgAuthorityAnybodyUidL;
					pAceTable->BooleanExpression.ubAuthorityCnt = 1;
					pAceTable->BooleanExpression.ubBooleanAceCnt = 0;
					pAceTable->ubAvailableColumn[0] = TcgCPinUidColumn;
					pAceTable->ubAvailableColumn[1] = TcgCPinCharSetColumn;
					pAceTable->ubAvailableColumn[2] = TcgCPinTryLimitColumn;
					pAceTable->ubAvailableColumn[3] = TcgCPinTriesColumn;
					pAceTable->ubAvailableColumn[4] = TcgCPinPersistenceColumn;
					pAceTable->ubAvailableColumnCnt = 5;
				}
			}
			LockingInitState = LockingInitState_TriggerSaveLockingTable8;
		// no break;
		case LockingInitState_TriggerSaveLockingTable8:
			TcgSetOpalTableEvent(OpalTableState_Save, TcgFlashTableOriginLocking9Index, OPAL_NULL_TARGET_ADDR);
			if (gTcg.BufManager.OpalTableState == OpalTableState_Idle) {
				LockingInitState = LockingInitState_Done;
				ubContinue = TRUE;
			}
			break;
		case LockingInitState_Done:
			LockingInitState = LockingInitState_Initial;
			break;
		}
	}
	gTcg.pAllFunctionStates->LockingInitState = LockingInitState;
}

AceTableElementPtr_t TcgGetLockingSpAceTableElementPtr(U32 ulTableIndex, U32 ulBufferAddr)
{
	if (ulTableIndex < TCG_ACE_TABLE_ELEMENT_PER_4K) {

		TcgLockingPart4Ptr_t pTcgApiStructLockingPartial4 = (TcgLockingPart4Ptr_t)ulBufferAddr;
		return (&pTcgApiStructLockingPartial4->AceTablePart1[ulTableIndex]);

	}
	else if (ulTableIndex >= TCG_ACE_TABLE_ELEMENT_PER_4K && ulTableIndex < 2 * TCG_ACE_TABLE_ELEMENT_PER_4K) { /* parasoft-suppress BD-PB-CC "confirmed by author" */

		TcgLockingPart5Ptr_t pTcgApiStructLockingPartial5 = (TcgLockingPart5Ptr_t)ulBufferAddr;
		return (&pTcgApiStructLockingPartial5->AceTablePart2[ulTableIndex - TCG_ACE_TABLE_ELEMENT_PER_4K]);

	}
	else if (ulTableIndex >= 2 * TCG_ACE_TABLE_ELEMENT_PER_4K && ulTableIndex < 3 * TCG_ACE_TABLE_ELEMENT_PER_4K) {

		TcgLockingPart6Ptr_t pTcgApiStructLockingPartial6 = (TcgLockingPart6Ptr_t)ulBufferAddr;
		return (&pTcgApiStructLockingPartial6->AceTablePart3[ulTableIndex - 2 * TCG_ACE_TABLE_ELEMENT_PER_4K]);

	}
	else if (ulTableIndex >= 3 * TCG_ACE_TABLE_ELEMENT_PER_4K && ulTableIndex < 4 * TCG_ACE_TABLE_ELEMENT_PER_4K) {

		TcgLockingPart7Ptr_t pTcgApiStructLockingPartial7 = (TcgLockingPart7Ptr_t)ulBufferAddr;
		return (&pTcgApiStructLockingPartial7->AceTablePart4[ulTableIndex - 3 * TCG_ACE_TABLE_ELEMENT_PER_4K]);

	}
	else if (ulTableIndex >= 4 * TCG_ACE_TABLE_ELEMENT_PER_4K && ulTableIndex < 5 * TCG_ACE_TABLE_ELEMENT_PER_4K) {

		TcgLockingPart8Ptr_t pTcgApiStructLockingPartial8 = (TcgLockingPart8Ptr_t)ulBufferAddr;
		return (&pTcgApiStructLockingPartial8->AceTablePart5[ulTableIndex -  4 * TCG_ACE_TABLE_ELEMENT_PER_4K]);

	}
	else if (ulTableIndex >= 5 * TCG_ACE_TABLE_ELEMENT_PER_4K && ulTableIndex < 6 * TCG_ACE_TABLE_ELEMENT_PER_4K) {

		TcgLockingPart9Ptr_t pTcgApiStructLockingPartial9 = (TcgLockingPart9Ptr_t)ulBufferAddr;
		return (&pTcgApiStructLockingPartial9->AceTablePart6[ulTableIndex - 5 * TCG_ACE_TABLE_ELEMENT_PER_4K]);

	}
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, 0);
	return NULL;
}
#endif /* (TCG_EN && (PS5013_EN || PS5021_EN || (PS5017_EN && !BURNER_MODE_EN)) && !RDT_MODE_EN && !LPM3_LOADER && !BOOTLOADER_MODE_EN) */
