#ifndef _BOOTLOADER_H_
#define _BOOTLOADER_H_

#include "setup.h"
#include "common/boot_api.h"    // BootloaderMain()
#include "common/fw_common.h"	// CB_INFO_t
#include "common/typedef.h"		// U8, U16, U32
#include "burner/codepointer.h" // MAX_SECTION_NUM

#define BOOTLOADER_DEBUG_EN                     (FALSE)
#define CODE_BLOCK_IS_CB0                       (0)
#define CODE_BLOCK_IS_CB1                       (1)
#define CODE_BLOCK_ALL_UNC                      (2)
#define CODE_BLOCK_NO_VALID                     (0xFFFF)

#define ALL_CH_CE0								(0xF)
#define BL_ALL_CE0_EN                           (0x01010101)
#define BL_ALL_CE_DIS                           (0x00000000)

#define BYTE32_ALIGN_SHIFT                  	(5)

#define BOOTLOADER_LOAD_CODE_BUF_PAGE_NUM       (BOOTLOADER_LOAD_CODE_BUF_SIZE / gFlhEnv.uwPageByteCnt)
#define LAST_CNT_TO_LOAD                        (1)

// -----------------------------------------------------------------
// FPU definitions.
// -----------------------------------------------------------------
// Read
#if MICRON_FSP_EN
#if ((IM_B47R) || IM_B37R || (IM_N48R))
#define BL_FPU_PTR_C3B_C00_A6_C30				(0x0000)
#else    /* ((IM_B47R) || (IM_N48R)) */
#define BL_FPU_PTR_CDA_C00_A5_C30				(0x0000)
#endif   /* ((IM_B47R) || (IM_N48R)) */
#else
#define BL_FPU_PTR_CA2_C00_A5_C30				(0x0000)
#endif
#define BL_FPU_PTR_C00_A5_C30					(0x0002)

// Read DMA
#define BL_FPU_PTR_C05_A5_CE0_DR				(0x0010)
#define BL_FPU_PTR_C05_A2_CE0_DR				(0x0020)
// Poll Status
#define BL_FPU_PTR_C70_POL_MK40_C00			    (0x0030)
// pull RE low
#define BL_FPU_PTR_CTRL_RE_LOW					(0x0040)
//Loader Use temp
#define BL_FPU_SIZE				                (0x0048) //Unit: 2Byte

typedef struct {
	CB_INFO_t sCBInfo[MAX_CHANNEL];//48						//record channel info with valid code block  from lowest channel to highest channel

	U32 ulFW_Code_Err;// 4									//Bitwise recording of the errors that has occuredduring loading code
	U8 ubCodeSignSHAMode;   // 1                            //Distinguish SHA mode, and it reveal the digest size the same time
	U8 btRSAEn: 1;
	U8 btHMACEn: 1;
	U8 btFIPS140NextEn : 1;
	U8 btRSV: 5;
	U8 ubRSV[2];            // 2


	U8 ubValidChannel;// 1									//The number of Channel with valid code blocks
	U8 ubValidChannelBitMap;// 1
	U8 ubFWRevisionID;// 1									//Revision ID of the Code that is currently in use  default value: 0xFF
	U8 ubFailRevision;// 1									//Records the Revision ID of the copy of code that has failed to be loaded, used to tell the firmware that a failed version of firmware exists and should be deleted  default value: 0xFF

	U8 ubRound;// 1											//Records this code block is found in which round in ScanIDPage
	U8 ubCHofCodePointer;									//Record IDPG found Channels to load Code pointer
	U16 uwBlockIndexOfCodePointer;							//Record IDPG found Blockindex to load Code pointer
} BOOT_BL_FWCODE_INFO_t; //64Byte
TYPE_SIZE_CHECK(BOOT_BL_FWCODE_INFO_t, (16 + MAX_CHANNEL *sizeof(CB_INFO_t)));

typedef struct {
	U16 uwPage;										//page index to read
	U16 uwBlock;									//block index to read

	U8  ubCH;										//operation channel index
	U8  ubCE;										//operation ce index for ubCH
	U8  ubFrameCnt;									//number of DMA frame to read, 0 means no operation
	U8  ubRsv;

	U32 ulBufBase;									//read buffer base

	union {
		U32 ulAll;
		struct {
			U32	btIsReadError: 1;					// 1 read operation error, skip DMA read operation
			U32	btIsErasedPage: 1;					// 1 is erase page
			U32	btIsUNC: 1;							// 1 is UNC
			U32	btIsCRC: 1;							// 1 is CRC
			U32	btIsTimeout: 1;
			U32	ubRsv: 3;
			U32 ulRsv: 24;
		} Status;
	} BitMap;
} BLParallelReadStruct; //16Byt

typedef enum CodeSignMode {
	CODESIGNHEADER = 0,
	SIGNATURE
} CodeSignModeEnum_t;

typedef struct {
	U8 ubLoadCnt[MAX_SECTION_NUM];
	U8 ubReserve[2];
	union {
		U8 ubAll;
		struct {
			U8 btCalCRCSeparately	: 1;
			U8 Reserve : 7;
		} B;
	} Flag;
} BootloaderLoadCodeInfo_t;

extern BootloaderLoadCodeInfo_t gBootloaderLoadCodeInfo;

void Bootloader_TCM_ECC_Enable(void);
void Bootloader_CEControl(U8 ubCH, U8 ubCE, U8 ubCEControlEn);
U8 BootloaderCheckFPUBusy(U8 ubCh);
void BootloaderBubbleSortU16(U16 *uwAry, U8 ubNum);
U8 BootloaderChannelParallelRead(BLParallelReadStruct *sBLParallelRead);
U8 BootloaderCalActiveSlotIdx(CODE_POINTER_HEADER_t *pCodePointerHeader);
U8 FlaBootloaderCodeSignLoader(CodeSignModeEnum_t ubIsSignature);
void BootloaderGetCodeInfo(void);
U8 BootloaderLoadCode(void);
void BootloaderInfoCollection(void);
U8 BootloaderLoadCodeFlow(void);
void BootloaderDaemon(void);
void BootloaderFlaIPCommonInit(void);
void BootloaderParseIDPage(void);
void BootloaderInitFlow(void);
void BootloaderRestoreCodeAndPC(void);

#endif /* _BOOTLOADER_H_ */

