#ifndef _VUC_SCANPH_H_
#define _VUC_SCANPH_H_

/* Product History block */
#if ENABLE_INIT_UART==TRUE
#define PH_UART_EN 	(TRUE)
#else
#define PH_UART_EN (FALSE)
#endif
#define VUC_PH_MP_LOG	(0x00)
#define VUC_PH_RDT_LOG	(0x01)
#define VUC_PH_EC		(0x02)
#if VRLC_EN
#define VUC_PH_VRLC	(0x03)
#endif /* VRLC_EN */
#define VUC_PH_NO_LOG_FOUND	        (0xFFFF)
#define VUC_PH_LOG_SIZE             (0x4000)
#define VUC_PH_MP_LOG_OFFSET        (VUC_PH_LOG_SIZE + VUC_PH_LOG_SIZE)
#define VUC_PH_EC_OFFSET            (VUC_PH_LOG_SIZE + VUC_PH_LOG_SIZE + VUC_PH_LOG_SIZE)
#if VRLC_EN
#define VUC_PH_VRLC_OFFSET          (VUC_PH_LOG_SIZE + VUC_PH_LOG_SIZE + VUC_PH_LOG_SIZE + VUC_PH_LOG_SIZE)
#endif /* VRLC_EN */

#pragma pack(push)
#pragma pack(1)
typedef struct {
	SystemAreaBlock_t PHBlk[PH_NUM];
	U8  ubState;
	U16 uwLastPage;
	U16 uwMPLogPage;
	U16 uwRDTLogPage;
	U16 uwECLogPage;
#if VRLC_EN
	U16 uwVRLCLogPage;
#endif /* VRLC_EN */
	U32 ulHostBufAddr;
	union {
		U8 ubAll;
		struct {
			U8 btActiveBlk		: 1;
			U8 btScannedDone		: 1;
			U8 btPHExist		    : 1;
			U8 ubRsv				: 5;
		} B;
	} Info;
} PH_t;
#pragma pack(pop)

extern PH_t gPH;

AOM_VUC void VUCScanPH(U32 ulDstAddr);

#endif /* _VUC_SCANPH_H_ */
