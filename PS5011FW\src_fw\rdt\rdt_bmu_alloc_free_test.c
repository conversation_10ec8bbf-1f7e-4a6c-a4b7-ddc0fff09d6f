#include "burner/Burner.h"
#include "rdt/rdt_api.h"
#include "hal/bmu/bmu_pop_cmd.h"
#include "lpm/lpm_api.h"

#if (RDT_MODE_EN)
#define TOTAL_LB_OFFSET_CNT			(1024)
#define LBID_TEST_NUM				(2)

U8 gubRDTBMUTest = FALSE;
U8 gubRDTLBID = 0;
U16 guwRDTProgPBAddress = 0;
U16 guwRDTProgLBOffset = 0;

#define		FIRST_FSA_SHIFT						(0)
#define		FIRST_FSA_MASK						BIT_MASK(7)
#define		FIRST_FSA_SHIFT_MASK				(FIRST_FSA_MASK << FIRST_FSA_SHIFT)
#define		M_SET_FSA_SEL(X)					((U32)(X) & FIRST_FSA_MASK)

BOOL rdt_compare_fpu_free_lb_cmd(RDT_API_STRUCT_PTR rdt, U8 LBID, U16 LBOffset)
{
	U8 ubIdx;
	U32 data_buf;
	U32 uwCheckData, uwGoldenData, failAddress;

	data_buf = 0x1004000; // (0x1004000 + 0x200) store FPU free/validate BMU SQ cmd

	for (ubIdx = 0; ubIdx < 4; ubIdx++) {
		uwGoldenData = ((((U32)(LBOffset + ubIdx) & BMU_CMD_FREE_LB_OFFSET_MASK) << BMU_CMD_FREE_LB_OFFSET_SHIFT) | (((U32)LBID & BMU_CMD_FREE_LB_ID_MASK) << BMU_CMD_FREE_LB_ID_SHIFT));
		uwCheckData = *(U32 *)((data_buf + 0x200) + ((sizeof(U32) * 8) * ubIdx) + sizeof(U32));

		if (uwCheckData != uwGoldenData) {
			failAddress = (data_buf + 0x200) + ((sizeof(U32) * 8) * ubIdx) + sizeof(U32);
			rdt_api_add_sram_err_into_erl(rdt, ERL_TYPE_SRAM_SHARE, ERL_FAIL_BMU, failAddress, uwCheckData, uwGoldenData, 0, 0);
			M_UART(RDT_TEST_, "\nFail FPU Free LB CMD Compare FAIL, CheckData=%l != GoldenData=%l", uwCheckData, uwGoldenData);
			return FAIL;
		}
	}
	return PASS;
}

#if (RDT_DO_IC_TEST)
BOOL rdt_api_bmu_alloc_free_test(RDT_API_STRUCT_PTR rdt)
{
	U8 ubCh, ubLBIDIdx, ubAlloc4KCnt;
	U16 ubAllocIdx;
	U32 write_data_buf;
	U32 backup_int_cfg, backup_zip_cfg_1, backup_dma_cfg;
	U32 uwProgLBOffset;
	U8 ubTimeOut = FALSE;
	U8 ubResult = PASS;
	RTTSelfTimeoutCondition_t RttTimer;

	// goal to check FPU free SQ cmd at : (LBID + LBOffset all 0) && (LBID + LBOffset all 1)
	U8 ubLBIDTest[LBID_TEST_NUM] = {LB_ID_WRITE_0, LB_ID_PROGRAM_0};
	U32 uwCheckLBOffset[LBID_TEST_NUM] = {0x0, 0x3FC};

	ubAlloc4KCnt = 4; //allocate 4's 4K LB once
	write_data_buf = (U32)(rdt->base.rs_parity_buffer_base);

	rdt->current_state = RDT_STATE_BMU_TEST;
	gubRDTBMUTest = TRUE;

	M_UART(RDT_TEST_, "\n[IC pattern] BMU alloc free test start\n");
	U32 start_time = rdt_api_rtt_get_timer_count();

	for (ubCh = 0; (ubCh < gFlhEnv.ubChannelExistNum) && !ubResult; ubCh++) {

		// Reset BMU
		LPMResetControl(CR_RST_N_BMU_BIT);
		// BMU init will set double free at all of LBID
		BMUInit();

		backup_int_cfg = R32_FCTL_CH[ubCh][R32_FCTL_INT_CFG];
		R32_FCTL_CH[ubCh][R32_FCTL_INT_CFG] = (SET_ALL_FLH_INT_EN & ~CMP_LCA_INT_EN_BIT);
		R32_FCTL_CH[ubCh][R32_FCTL_INT_CFG] &= (~AUTO_POL_STA_FAIL_INT_EN_BIT);
		backup_zip_cfg_1 = R32_FCTL_CH[ubCh][R32_FCTL_ZIP_CFG_1];
		R32_FCTL_CH[ubCh][R32_FCTL_ZIP_CFG_1] &= ~GC_EN_BIT;
		R32_FCTL_CH[ubCh][R32_FCTL_ZIP_CFG_1] |= PROG_BUF_VALID_SHIFT_MASK;
		backup_dma_cfg = R32_FCTL_CH[ubCh][R32_FCTL_DMA_CFG];
		R32_FCTL_CH[ubCh][R32_FCTL_DMA_CFG] &= CLR_FRAME_NUM;
		R32_FCTL_CH[ubCh][R32_FCTL_DMA_CFG] |= M_SET_FRAME_NUM(1);
		R32_FCTL_CH[ubCh][R32_FCTL_DMA_CFG] |= BUF_MD_EN_BIT;   //BUF Mode => auto free LB when fpu program finish

		FlaCEControl(ubCh, 0, ENABLE);
		for (ubLBIDIdx = 0; (ubLBIDIdx < LBID_TEST_NUM) && !ubResult; ubLBIDIdx++) {
			gubRDTLBID = ubLBIDTest[ubLBIDIdx];
			uwProgLBOffset = uwCheckLBOffset[ubLBIDIdx];
			for (ubAllocIdx = 0; (ubAllocIdx < TOTAL_LB_OFFSET_CNT / ubAlloc4KCnt) && !ubResult; ubAllocIdx++) {
				// Allocate LB & PB
				gubBMUCallBackDone = FALSE;
				if (BMU_CMD_STATUS_SUCCESS != BMUAPICmdAllocate(  //Alocate PB + LB
						BMU_CMD_NEED_CQ,						/* btNoCQ */
						BMU_CMD_TIME_INFINITE,					/* ubTimeout */
						gubRDTLBID,							    /* ubLBID */
						ubAlloc4KCnt,						    /* ubSize */
						BMU_ALLOCATE_DEFAULT_LCA,				/* ulLCA */
						0,										/* ubCTAG */
						0,										/* ubStreamID */
						BMU_ALLOCATE_NOT_GET_E3D512,			/* btOption */
						BMU_ALLOCATE_NOT_FUA,					/* btFUA */
						BMU_ALLOCATE_NOT_NEED_AUTO_FREE,		/* btAF */
						(U32)BMUSimpleCallBack, 				/* ulCallBackAddr */
						(U8)NULL)								/* uwCallBackData */
				) {
					ubResult = FAIL;
					gRdtApiStruct.rdt_parity.parity_error |= BIT(RDT_BMU_EVT);
					gRdtApiStruct.rdt_parity.STS[RDT_BMU_EVT] = R32_BMU[R32_BMU_ERROR_STATUS];// & 0x03000000;
					M_UART(RDT_TEST_, "BMUAPICmdAllocate Fail, CH=%b\n", ubCh);
					break;
				}
				else {
					RTTSetSelfTimeout(&RttTimer, GERNAL_TIMEOUT_THRESHOLD * 1000);
					// Allocate 1 entry LB
					while (!gubBMUCallBackDone) {
						BMUDelegateCmd();
						ubTimeOut = RTTCheckSelfTimeout(&RttTimer);
						if (TRUE == ubTimeOut) {
							break;
						}
					}
					if (TRUE == ubTimeOut) {
						ubResult = FAIL;
						M_UART(RDT_TEST_, "\nBMUDelegateCmd Time Out CH=%b", ubCh);
						break;
					}
				}

				if (guwRDTProgLBOffset == uwProgLBOffset) {
					//fpu program + free LB + PB first
					//rdt_fpu_write(0, ubCh, 0, 0, 0, FRAMES_PER_PAGE, write_data_buf);
					rdt_api_fpu_write(0, ubCh, 0, 0, FRAMES_PER_PAGE, write_data_buf);

					M_RTT_IDLE_MS(1); // wait 1 MS for LB free cmd translation at MR before free cmd compare

					if (rdt_compare_fpu_free_lb_cmd(rdt, gubRDTLBID, guwRDTProgLBOffset)) {
						ubResult = FAIL;
						M_UART(RDT_TEST_, "\nrdt_compare_fpu_free_lb_cmd Failm, CH=%b", ubCh);
					}
				}

				if (!ubResult) {
					// CPU free LB + PB directly
					BMUCmdResult_t BMUCmdResult = {{0}};
					while (BMU_CMD_STATUS_SUCCESS != BMUAPICmdFree(BMU_CMD_NEED_CQ, gubRDTLBID, (U16)guwRDTProgLBOffset, ubAlloc4KCnt,
							BMU_FREE_OPTION_NOT_NEED_CHECK_FULL_FLAG, BMU_FREE_NOT_NEED_CHECK_DF_FLAG, BMU_FREE_FAR_DIS, &BMUCmdResult));

					rdt_api_check_ecc_parity(&gRdtApiStruct, 0, 0);

					if ((BMUCmdResult.BMUFreeRst.ubResult != 0) || gRdtApiStruct.rdt_err) {
						ubResult = FAIL;
						gRdtApiStruct.rdt_parity.parity_error |= BIT(RDT_BMU_EVT);
						gRdtApiStruct.rdt_parity.STS[RDT_BMU_EVT] = R32_BMU[R32_BMU_ERROR_STATUS];// & 0x03000000;
						M_UART(RDT_TEST_, "\nBMUAPICmdFree Fail, CH=%b", ubCh);
						break;
					}
				}

				if (guwRDTProgLBOffset == uwProgLBOffset) {
					//UartPrintf("Check ubCh=%l, LBID=%b, guwProgLBOffset=%l\n", ubCh, gubLBID, guwProgLBOffset);
					break;
				}
			}
		}
		FlaCEControl(ubCh, 0, DISABLE);
		R32_FCTL_CH[ubCh][R32_FCTL_ZIP_CFG_1] = backup_zip_cfg_1;
		R32_FCTL_CH[ubCh][R32_FCTL_INT_CFG] = backup_int_cfg;
		R32_FCTL_CH[ubCh][R32_FCTL_DMA_CFG] = backup_dma_cfg;
	}

	if (ubResult) {
		rdt_api_check_ecc_parity(rdt, 0, 0);
		rdt_api_program_erl_log(rdt, TRUE);
	}

	gubRDTBMUTest = FALSE;
	LPMResetControl(CR_RST_N_BMU_BIT);
	BMUInit();

	M_UART(RDT_TEST_, "-- test time %d ms \n", rdt_api_rtt_get_timer_count() - start_time);
	M_UART(RDT_TEST_, "BMU alloc free test %s\n", ubResult ? "fail" : "pass");

	return ubResult;
}
#endif /*(RDT_DO_IC_TEST)*/

#endif /* (FT_MODE_EN) */
