#ifndef _VUC_MICRONLOGICALTOPHYSICAL_H_
#define _VUC_MICRONLOGICALTOPHYSICAL_H_
#include "aom/aom_api.h"

#define VUC_MICRON_LOGICAL_TO_PHYSICAL_ADDRESS_HEADER_LENGTH (12)

typedef struct {
	U64	ullLBA;
	U32	ulNSID;
	U32 ulCommandBitFlag;
} GetLogicaltoPhysicalAddressInputData_t;

typedef struct {
	U8 ubResponseHeaderFormatVersion;
	U8 ubResponseDataFotmatVersion;
	U16 uwCMDClass;
	U16 uwCMDCode;
	U16 uwStatus;
	U32 ulDataPayloadSize;
} GetLogicaltoPhysicalAddressResponseHEADER_t;

/*typedef struct {
	U8 ubChannel;
	U8 ubCE;
	U8 ubLUN;
	U8 ubPlane;
	U16 uwBlk;
	U16 uwPage;
	U8 ubFrame;
} GetLogicaltoPhysicalAddressResponse;*/

#if (HOST_MODE == NVME)
AOM_VUC_3 void VUCMicronLogicalToPhysicalAddress(GetLogicaltoPhysicalAddressInputData_t *pInputData, U32 ulPayloadAddr);
AOM_VUC_3 void VUCMicronLogicalToPhysicalAddress_Callback(COP1CQRst_t uoResult);
#else /* (HOST_MODE == NVME) */
AOM_VUC_3 void VUCMicronLogicalToPhysicalAddress(GetLogicaltoPhysicalAddressInputData_t *pInputData, U32 ulPayloadAddr);
AOM_VUC_3 void VUCMicronLogicalToPhysicalAddress_Callback(COP1CQRst_t uoResult);
#endif /* (HOST_MODE == NVME) */
#endif /* _VUC_MICRONLOGICALTOPHYSICAL_H_ */
