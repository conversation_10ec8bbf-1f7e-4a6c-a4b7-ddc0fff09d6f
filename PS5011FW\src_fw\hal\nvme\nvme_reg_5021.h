#ifndef _NVME_REG_E21_H_
#define _NVME_REG_E21_H_

#include "common/math_op.h"
#include "mem.h"

/*========================= START : NVMe Layer Status =========================*/

#define R32_NVME_NL_STS                     (0x00000000 >> 2)
#define     INT_DB_WP                           (BIT0)
#define     INT_NSSR                            (BIT1)
#define     NL_INT_CCEN_FLAG                    (BIT2)
#define     NL_INT_CCSHN_FLAG                   (BIT3)
#define     INT_BPR                             (BIT4)

#define R32_NVME_NL_AXI_ESTS                (0x00000004 >> 2)
#define		NL_AXI_ESTS_ALL                     (0x1FFF)

#define     NL_INT_W_ABN_ADDR_BIT                   (BIT0)
#define     NL_INT_HST_W_DERR_BIT                   (BIT1)
#define     NL_INT_CMD_R_DERR_BIT                   (BIT2)
#define     NL_INT_CMD_R_RERR_BIT                   (BIT3)
#define     NL_INT_MSIX_RERR_BIT                    (BIT4)
#define     NL_INT_CPL_RERR_BIT                     (BIT5)
#define     NL_INT_SRAM_PAR_ERR_BIT                 (BIT6)
#define     NL_INT_INVLD_QID_BIT                    (BIT7)
#define     NL_INT_SQDB_INVLD_REG_FLAG_BIT          (BIT8)
#define     NL_INT_SQDB_INVLD_VAL_FLAG_BIT          (BIT9)
#define     NL_INT_CQDB_INVLD_REG_FLAG_BIT          (BIT10)
#define     NL_INT_CQDB_INVLD_VAL_FLAG_BIT          (BIT11)
#define     NL_INT_INVLD_QOFST_BIT                  (BIT12)

#define R32_NVME_NL_INT_EN                  (0x00000008 >> 2)
#define     NL_INT_NL_DB_WP_EN                  (BIT0)
#define     NL_INT_NSSR_EN                      (BIT1)
#define     NL_INT_CCEN_FLAG_EN                 (BIT2)
#define     NL_INT_CCSHN_FLAG_EN                (BIT3)
#define     NL_INT_BPR_FLAG_EN                  (BIT4)

#define R32_NVME_NL_ERR_INT_EN              (0x0000000C >> 2)
#define     NL_INT_W_ABN_ADDR_EN                (BIT0)
#define     NL_INT_HST_W_DERR_EN                (BIT1)
#define     NL_INT_CMD_R_DERR_EN                (BIT2)
#define     NL_INT_CMD_R_RERR_EN                (BIT3)
#define     NL_INT_MSIX_RERR_EN                 (BIT4)
#define     NL_INT_CPL_RERR_EN                  (BIT5)
#define     NL_INT_SRAM_PAR_ERR_EN              (BIT6)
#define     NL_INT_INVLD_QID_EN                 (BIT7)
#define     NL_INT_SQDB_INVLD_REG_FLAG_EN       (BIT8)
#define     NL_INT_SQDB_INVLD_VAL_FLAG_EN       (BIT9)
#define     NL_INT_CQDB_INVLD_REG_FLAG_EN       (BIT10)
#define     NL_INT_CQDB_INVLD_VAL_FLAG_EN       (BIT11)
#define     NL_INT_IVD_QOFST_EN                 (BIT12)

#define R32_NVME_NL_CE_CHG                  (0x00000010 >> 2)
#define     NL_INT_CE_CHG                       (BITMSK(17,0))  // BIT [16:0]

#define R32_NVME_NL_SHN_CHG                 (0x00000014 >> 2)
#define     NL_INT_SHN_CHG                      (BITMSK(17,0))  // BIT [16:0]

#define R32_NVME_NL_BP_RD                   (0x00000018 >> 2)
#define     NL_INT_BPR                          (BITMSK(17,0))  // BIT [16:0]

#define R32_NVME_NL_INVLD_SQDB_REG          (0x00000020 >> 2)

#define R32_NVME_NL_INVLD_SQDB_VAL          (0x00000040 >> 2)

#define R32_NVME_NL_INVLD_CQDB_REG          (0x00000060 >> 2)

#define R32_NVME_NL_INVLD_CQDB_VAL          (0x00000080 >> 2)

#define R64_NVME_NL_ABN_ADDR                (0x000000A0 >> 3)

#define R32_NVME_NL_DB_INFO                 (0x000000A8 >> 2)
#define     NL_INVLD_DB_FID                     (BITMSK(5,0))   // BIT [4:0]
#define     NL_INVLD_DB_QID                     (BITMSK(8,8))   // BIT [15:8]
#define     NL_INVLD_DB_QTYPE                   (BITMSK(2,16))  // BIT [17:16]
#define         INVLD_DB_QTYPE_SQ                   (1)
#define         INVLD_DB_QTYPE_CQ                   (2)
#define     NL_INVLD_DB_ETYPE                   (BITMSK(2,20))  // BIT [21:20]
#define     NL_ABN_FID                   (BITMSK(5,24))  // BIT [24:28]

#define R32_NVME_NL_INVLD_QID               (0x000000AC >> 2)
#define     NL_INVLD_FID                        (BITMSK(5,0))   // BIT [4:0]
#define     NL_INVLD_QID                        (BITMSK(8,8))   // BIT [15:8]
#define     NL_INVLD_Q_TYPE                     (BITMSK(2,16))  // BIT [17:16]

#define R32_NVME_NL_HST_WR_ST               (0x000000B0 >> 2)
#define     NL_HST_WR_ST                        (BITMSK(17,0))  // BIT [16:0]

#define R32_NVME_NL_INVLD_QOFST             (0x000000B4 >> 2)
#define     NL_INVLD_QOFST                      (BITMSK(8,0))   // BIT [7:0]
#define     NL_INVLD_QOFST_TYPE                 (BIT8)
#define         INVLD_QOFST_TYPE_SQ                 (0)
#define         INVLD_QOFST_TYPE_CQ                 (1)

/*========================= END : NVMe Layer Status =========================*/

/*========================= START : NVMe Layer Control =========================*/

#define R32_NVME_NL_CTRL                    (0x00000100 >> 2)
#define     NL_CTRL_CMDBUF_THR                  (BITMSK(2,0))   // BIT [1:0]
#define     NL_CTRL_LAST_BP                     (BIT2)
#define     NL_CTRL_PBA_RD_EN                   (BIT3)
#define     NL_CTRL_SRAM_PAR_CHK_EN             (BIT4)
#define     NL_CTRL_SQDB_EXCD_IGND              (BIT5)
#define     NL_CTRL_SQDB_PREV_IGND              (BIT6)
#define     NL_CTRL_SQDB_FULL_IGND              (BIT7)
#define     NL_CTRL_BLK_CMD_EN                  (BIT8)
#define     NL_ACQ_FULL_CHG_EN                  (BIT9)

#define R32_NVME_NL_SRAM_CTRL               (0x00000104 >> 2)
#define     CTRL_SRAM_INIT                      (BIT0)
#define     SRAM_INIT_DONE                      (BIT1)
#define     CTRL_SRAM_ERR_INJ                   (BIT2)
#define     CTRL_SRAM_LS_ENTRY_THR              (BITMSK(8,8))   // BIT [15:8]
#define     CTRL_SRAM_LS_EXIT_THR               (BITMSK(8,16))  // BIT [23:16]
#define     CTRL_SRAM_LOCK                      (BIT24)

#define R32_NVME_NL_TIME_UNIT               (0x00000108 >> 2)
#define     NL_TIME_UNIT                        (BITMSK(16,0))  // BIT [15:0]

#define R32_NVME_NL_AUTO_IC                 (0x0000010C >> 2)
#define     CTRL_AUTO_IC_EN                     (BIT0)
#define     CTRL_AUTO_IC_THR                    (BITMSK(8,8))   // BIT [15:8]
#define     CTRL_AUTO_IC_QCNT_THR               (BITMSK(10,16))  // BIT [25:16]

#define R32_NVME_NL_INT_TO_THR              (0x00000110 >> 2)
#define     CTRL_INT_TO_THR                     (BITMSK(16,0))  // BIT [15:0]

#define R32_NVME_NL_CPL_FLUSH               (0x00000114 >> 2)
#define     CTRL_CPL_FLUSH                      (BITMSK(17,0))  // BIT [16:0]

#define R8_NVME_NL_SQ_MASK                  (0x00000120)
#define R32_NVME_NL_SQ_MASK                 (0x00000120 >> 2)
#define     ALL_SQ                              (0xFFFFFFFF)

#define R32_NVME_NL_FUNC_MASK               (0x00000140 >> 2)
#define     CTRL_FUNC_MASK                      (BITMSK(17,0))  // BIT [16:0]

#define R8_NVME_NL_FUNC_BASE                (0x00000150)
#define R32_NVME_NL_FUNC_BASE               (0x00000150 >> 2)
#define     VF_BASE0                            (BITMSK(8,0))   // BIT [7:0]
#define     VF_BASE1                            (BITMSK(8,8))   // BIT [15:8]
#define     VF_BASE2                            (BITMSK(8,16))  // BIT [23:16]
#define     VF_BASE3                            (BITMSK(8,24))  // BIT [31:24]

#define R32_NVME_NL_MSI_IV_MASK             (0x00000160 >> 2)

#define R8_NVME_NL_FW_RST                   (0x00000180)
#define R32_NVME_NL_FW_RST                  (0x00000180 >> 2)
#define     FW_RST_FID                          (BITMSK(5,0))   // BIT [4:0]
#define     FW_RST_EXE                          (BIT8)
#define     INT_FLUSH_EXE                       (BIT9)

#define R32_NVME_NL_FW_DEL                  (0x00000184 >> 2)   // F800_3184h
#define     DEL_SQID                            (BITMSK(8,0))   // BIT [7:0]
#define     DEL_FID                             (BITMSK(5,8))   // BIT [12:8]
#define     DEL_TYPE                            (BIT13)
#define     FUNCTION_DELETE                     (0)
#define     QUEUE_LEVEL_DELETE                  (1)
#define     STS_STABLE                          (BIT15)
#define     DEL_REQ                             (BIT16)


#define R32_NVME_NL_CPL_STS_L               (0x00000200 >> 2)
#define     NL_CPL_CID                          (BITMSK(16,0))  // BIT [15:0]
#define     NL_CPL_ST                           (BITMSK(15,16)) // BIT [30:16]
#define     NL_CPL_PUSH_BIT                     (BIT31)
#define R32_NVME_NL_CPL_STS_M               (0x00000204 >> 2)
#define     NL_CPL_SPEC                         (BITMSK(32,0))  // BIT [63:32]
#define R32_NVME_NL_CPL_STS_H               (0x00000208 >> 2)
#define     NL_CPL_NLB                          (BITMSK(16,0))  // BIT [79:64]
#define     NL_CPL_SQID                         (BITMSK(8,16))  // BIT [87:80]
#define     NL_CPL_FID                          (BITMSK(5,24))  // BIT [92:88]
#define     NL_CPL_IO_CMD                       (BIT29)         // BIT 93
#define     NL_CPL_LABF                         (BIT30)         // BIT 94

#define R8_NVME_NL_SQ_MASK_ST               (0x00000240)
#define R32_NVME_NL_SQ_MASK_ST              (0x00000240 >> 2)

#define R8_NVME_NL_INT_PEND                 (0x00000260)

#define R32_NVME_NL_IDLE_ST                 (0x00000280 >> 2)
#define	R8_NVME_NL_IDLE_ST					(0x00000280 )
#define     NL_CMDBUF_EMPTY                     (BIT0)
#define     NL_AXIS_IDLE                        (BIT1)
#define     NL_INTC_IDLE                        (BIT2)
#define     NL_SRAM_IDLE                        (BIT3)
#define     NL_CPU_IDLE                         (BIT4)
#define     NL_NFE_IDLE                         (BIT5)
#define     NL_NFE_NO_OUTSTD                    (BIT6)
#define     NL_NFE_NO_CMD                       (BIT7)
#define     NL_ACQ_FULL_FID                     (BITMSK(5,8))  // BIT [12:8]
#define     NL_ACQ_FULL                         (BIT16)

/*====================== END: NVMe Layer Control =============================*/

/*========================= START : NVMe Layer SQ Information =========================*/
#define R32_NVME_NL_SQ_DESP0_DW0            (0x00000000 >> 2)   // use 32 bit method to access enable bit

#define R64_NVME_NL_SQ_DESP0_L              (0x00000000 >> 3)
#define R64_NVME_NL_SQ_DESP0_H              (0x00000008 >> 3)
#define     NL_SQ_EN                            (BIT0)
#define     NL_SQ_PRI                           (BITMSK(2,1))   // BIT [2:1]
#define     NL_SQ_CQID                          (BITMSK(8,4))   // BIT [11:4]
#define     NL_SQ_BASE                          (BITMSK(52,12)) // BIT [63:12]
#define     NL_SQ_SIZE                          (BITMSK(16,0))  // BIT [79:64]
#define     NL_SQ_HEAD                          (BITMSK(16,16)) // BIT [95:80]
#define     NL_SQ_WHEAD                         (BITMSK(16,32)) // BIT [111:96]
#define     NL_SQ_TAIL                          (BITMSK(16,48)) // BIT [127:112]

#define R64_NVME_NL_SQ_DESP1_L              (0x00000010 >> 3)
#define R64_NVME_NL_SQ_DESP1_H              (0x00000018 >> 3)
#define     NL_SQ_WL                            (BITMSK(32,0))  // BIT [31:0]
#define     NL_WLL_THR                          (BITMSK(32,32)) // BIT [63:32]
#define     NL_WLR_THR                          (BITMSK(32,0))  // BIT [95:64]

/*========================= END : NVMe Layer SQ Information =========================*/

/*========================= START : NVMe Layer Function Capability & Control Information =========================*/

#define R32_NVME_NL_CAP0                    (0x00001400 >> 2)
#define     NL_CAP_MQES                         (BITMSK(16,0))  // BIT [15:0]
#define     NL_CAP_CQR                          (BIT16)
#define     NL_CAP_AMS                          (BITMSK(2,17))  // BIT [18:17]
#define     NL_CAP_TO                           (BITMSK(8,24))  // BIT [31:24]

#define R32_NVME_NL_CAP1                    (0x00001404 >> 2)
#define     NL_CAP_DSTRD                        (BITMSK(4,0))   // BIT [3:0]
#define     NL_CAP_NSSRS                        (BIT4)
#define     NL_CAP_CSS                          (BITMSK(8,5))   // BIT [12:5]
#define     NL_CAP_BPS                          (BIT13)
#define     NL_CAP_MPSMIN                       (BITMSK(4,16))  // BIT [19:16]
#define     NL_CAP_MPSMAX                       (BITMSK(4,20))  // BIT [23:20]

#define R32_NVME_NL_CC                      (0x00001408 >> 2)
#define     NL_CC_EN                            (BIT0)
#define     NL_CC_CSS                           (BITMSK(3,4))   // BIT [6:4]
#define     NL_CC_MPS                           (BITMSK(4,7))   // BIT [10:7]
#define     NL_CC_AMS                           (BITMSK(3,11))  // BIT [13:11]
#define     NL_CC_SHN                           (BITMSK(2,14))  // BIT [15:14]
#define     NL_CC_IOSQES                        (BITMSK(4,16))  // BIT [19:16]
#define     NL_CC_IOCQES                        (BITMSK(4,20))  // BIT [23:20]

#define R32_NVME_NL_CSTS                    (0x0000140C >> 2)
#define     NL_CSTS_RDY                         (BIT0)
#define     NL_CSTS_CFS                         (BIT1)
#define     NL_CSTS_SHST                        (BITMSK(2,2))   // BIT [3:2]
#define         NORMAL_OPERATION                    (0)
#define         SHUTDOWN_OCCURING                   (1)
#define         SHUTDOWN_COMPLETE                   (2)
#define     NL_CSTS_NSSRO                       (BIT4)
#define     NL_CSTS_PP                          (BIT5)

#define CHECK_NL_CSTS_RDY_BIT()					(R32_NVME_NL_RAM[R32_NVME_NL_CSTS] & NL_CSTS_RDY)

#define R32_NVME_NL_BPINFO                  (0x00001410 >> 2)
#define     NL_BPINFO_BPSZ                      (BITMSK(15,0))  // BIT [14:0]
#define     NL_BPINFO_BRS                       (BITMSK(2,24))  // BIT [25:24]
#define     NL_BPINFO_ABPID                     (BIT31)

#define R32_NVME_NL_BPRSEL                  (0x00001414 >> 2)
#define     NL_BPRSEL_BPRSZ                     (BITMSK(10,0))  // BIT [9:0]
#define     NL_BPRSEL_BPROF                     (BITMSK(20,10)) // BIT [29:10]
#define     NL_BPRSEL_BPID                      (BIT31)

#define R64_NVME_NL_BPMBL                   (0x00001418 >> 3)
#define     NL_BPMBL_BMBBA                      (BITMSK(53,12)) // BIT [64:12]

#define R32_NVME_NL_WRR_WGT                 (0x00001420 >> 2)
#define     CTRL_ARB_BURST                      (BITMSK(3,0))   // BIT [2:0]
#define     CTRL_1ST_ADM                        (BIT3)
#define     CTRL_WRR_EN                         (BIT4)
#define     CTRL_LOW_PRI_NUM                    (BITMSK(8,8))   // BIT [15:8]
#define     CTRL_MED_PRI_NUM                    (BITMSK(8,16))  // BIT [23:16]
#define     CTRL_HIGH_PRI_NUM                   (BITMSK(8,24))  // BIT [31:24]

#define R32_NVME_NL_WRR_ST                  (0x00001424 >> 2)
#define     NLL_WRR_ST_LOW                      (BITMSK(8,0))   // BIT [7:0]
#define     NLL_WRR_ST_MED                      (BITMSK(8,8))   // BIT [15:8]
#define     NLL_WRR_ST_HIGH                     (BITMSK(8,16))  // BIT [23:16]
#define     NLL_WRR_ST_URG                      (BITMSK(8,24))  // BIT [31:24]

#define R32_NVME_NL_WRR_RCNT                (0x00001428 >> 2)
#define     WRR_RC_L_CNT                        (BITMSK(8,0))   // BIT [7:0]
#define     WRR_RE_M_CNT                        (BITMSK(8,8))   // BIT [15:8]
#define     WRR_RE_H_CNT                        (BITMSK(8,16))  // BIT [23:16]

#define R32_NVME_NL_IC_CFG                  (0x0000142C >> 2)
#define     INT_COA_THR                      (BITMSK(8,0))   // BIT [7:0]
#define     INT_COA_TIME                     (BITMSK(8,8))   // BIT [15:8]
#define     NLL_WRR_HML                         (BITMSK(2,16))  // BIT [17:16]

#define R32_NVME_NL_NSSR                    (0x00001A00 >> 2)

#define R32_NVME_NL_VS                      (0x00001A04 >> 2)
#define     NL_VS_TER                           (BITMSK(8,0))   // BIT [7:0]
#define     NL_VS_MNR                           (BITMSK(8,8))   // BIT [15:8]
#define     NL_VS_MJR                           (BITMSK(16,16)) // BIT [31:16]

/*========================= END : NVMe Layer Function Capability & Control Information =========================*/

/*========================= START : NVMe Layer CQ and Interrupt Vector Information =========================*/
#define R32_NVME_NL_CQ_DESP_DW0             (0x00002000 >> 2)   // use 32 bit method to access enable bit

#define R64_NVME_NL_CQ_DESP_L               (0x00002000 >> 3)
#define R64_NVME_NL_CQ_DESP_H               (0x00002008 >> 3)
#define     NL_CQ_EN                            (BIT0)
#define     NL_CQ_IE                            (BIT1)
#define     NL_CQ_PHASE                         (BIT2)
#define     NL_CQ_IVID                          (BITMSK(8,4))   // BIT [11:4]
#define     NL_CQ_BASE                          (BITMSK(52,12)) // BIT [63:12]
#define     NL_CQ_SIZE                          (BITMSK(16,0))  // BIT [79:64]
#define     NL_CQ_WTAIL                         (BITMSK(16,16)) // BIT [95:80]
#define     NL_CQ_HEAD                          (BITMSK(16,32)) // BIT [111:96]
#define     NL_CQ_TAIL                          (BITMSK(16,48)) // BIT [127:112]

#define R64_NVME_NL_MSIX_TABLE_L            (0x00003000 >> 3)
#define R64_NVME_NL_MSIX_TABLE_H            (0x00003008 >> 3)
#define     NL_MSIX_MSG_ADDR                    (BITMSK(64,0))  // BIT [63:0]
#define     NL_MSIX_MSG_DATA                    (BITMSK(32,0))  // BIT [95:64]
#define     NL_MSIX_MASK                        (BIT32)         // BIT 96
#define     NL_INT_COA_CD                       (BIT33)         // BIT 97
#define     NL_IV_MAP                           (BITMSK(4,36))  // BIT [103:100]
#define     NL_INT_COA_CNT                      (BITMSK(8,48))  // BIT [119:112]
#define     NL_INT_COA_TMR                      (BITMSK(8,56))  // BIT [127:120]

#define R64_NVME_NL_IV_MAP_L                (0x00004000 >> 3)
#define R64_NVME_NL_IV_MAP_H                (0x00004008 >> 3)

/*========================= END : NVMe Layer CQ and Interrupt Vector Information =========================*/
/*=============================== NVME MSIX Table ===========================*/
#define	NVME_NL_MSIX_TABLE_SIZE					(0x10)
/*============================= END: NVME MSIX Table ========================*/

#endif /* _NVME_REG_E21_H */
