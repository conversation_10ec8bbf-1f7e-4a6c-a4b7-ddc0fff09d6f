#ifndef _SYS_PD0_REG_5021_H_
#define _SYS_PD0_REG_5021_H_

#include "typedef.h"
#include "mem.h"
#include "symbol.h"

/*
 *  +----------------------------------------------------------------+
 *  |					System PD0 Register                          |
 *  | 				Offset:0xF8006000 ~ 0xF8006FFF                   |
 *  |----------------------------------------------------------------|
 *  | SYSTEM Control         | 0x000 ~ 0x024 | SYS0_SYS_CTRL_BASE    |
 *  | POWER Control      	 | 0x040 ~ 0x074 | SYS0_PWR_CTRL_BASE    |
 *  | PMU Control        	 | 0x080 ~ 0x0AC | SYS0_PMU_CTRL_BASE    |
 *  | MUX Control        	 | 0x0B0 ~ 0x0BC | SYS0_MUX_CTRL_BASE    |
 *  | GPIO Control       	 | 0x0C0 ~ 0x0C0 | SYS0_GPIO_CTRL_BASE   |
 *  | SRAM Control           | 0x0DC ~ 0x108 | SYS0_SRAM_CTRL_BASE   |
 *  | PAD Control            | 0x1C0 ~ 0x218 | SYS0_PAD_CTRL_BASE    |
 *  | ANALOG Control         | 0x320 ~ 0x3B4 | SYS0_ANALOG_CTRL_BASE |
 *  | HOST Control           | 0x400 ~ 0x440 | SYS0_HOST_CTRL_BASE   |
 *  | PD0 IP Control         | 0x500 ~ 0x520 | SYS0_IP_CTRL_BASE     |
 *  | PHY Control            | 0xC10 ~ 0xC4C | SYS0_PHY_CTRL_BASE    |
 *  | FLASH PHY OSC Control  | 0xD00 ~ 0xD18 | N/A                   |
 *  | EFUSE                  | 0xDA0 ~ 0xDFC | N/A                   |
 *  | PD0 MISC               | 0xE00 ~ 0xE80 | SYS0_MISC_CTRL_BASE   |
 *  +----------------------------------------------------------------+
 */


/*
 *  +-----------------------------------------------------------------------+
 *  |					SYSTEM Control      								|
 *  +-----------------------------------------------------------------------+
 */
#define SYS0_SYS_CTRL_BASE                          (SYSTEM_PD0_REG_ADDRESS)

#define R8_SYS0_SYS_CTRL							((REG8  *) SYS0_SYS_CTRL_BASE)
#define R16_SYS0_SYS_CTRL							((REG16 *) SYS0_SYS_CTRL_BASE)
#define R32_SYS0_SYS_CTRL							((REG32 *) SYS0_SYS_CTRL_BASE)
#define R64_SYS0_SYS_CTRL							((REG64 *) SYS0_SYS_CTRL_BASE)

/* Default: 0x00000000 */
#define R32_SYS0_CPU_RESET                          (0x000 >> 2)
#define 	CPU_RESET_BIT                           (BIT3)

/* Default: 0x00000008 */
#define R32_SYS0_CLK_SEL                            (0x004 >> 2)
#define 	CPU_CLK_SEL_SHIFT                       (0)
#define 	CPU_CLK_SEL_MASK                        (BIT_MASK(4))
#define 	CPU_CLK_SEL                             (CPU_CLK_SEL_MASK << CPU_CLK_SEL_SHIFT)
#define 	CPU_CLK_SW_BIT                          (BIT31)

/* Default: 0x00000000 */
#define R32_SYS0_SYS_INFO                           (0x008 >> 2)
#define 	ICE_CTRL_SYNC_BIT                       (BIT2)        // RO
#define 	MASK_ROM_SYNC_BIT                       (BIT3)        // RO

/* Default: 0x00000005 */
#define R32_SYS0_CPU_CTRL                           (0x00C >> 2)
#define 	ROM_0000_START_BIT                      (BIT0)
#define 	EVENTI0_BIT                             (BIT1)
#define 	PMU_0000_START_BIT                      (BIT2)

/* Default: 0x00000000 */
#define R32_SYS0_CLK_SEL1                           (0x014 >> 2)
#define 	SYSTEM_HCLK_SEL_SHIFT                   (0)
#define 	SYSTEM_HCLK_SEL_MASK                    (BIT_MASK(3))
#define 	SYSTEM_HCLK_SEL                         (SYSTEM_HCLK_SEL_MASK << SYSTEM_HCLK_SEL_SHIFT)

/* Default: 0xFFF9FFFF */
#define R32_SYS0_RESET_CTRL                         (0x020 >> 2)
#define 	SRST_AXI_BIT                            (BIT0)  // AXI
#define 	SRST_BMU_BIT                            (BIT1)
#define 	SRST_COP1_BIT                           (BIT2)
#define 	SRST_DBUF_BIT                           (BIT3)
#define 	SRST_ANDES_BIT                          (BIT4)  // COP0_core_run
#define 	SRST_PIC_BIT                            (BIT5)
#define 	SRST_SEC_BIT                            (BIT6)
#define 	SRST_DMAC_BIT                           (BIT7)
#define 	SRST_APU_BIT                            (BIT8)
#define 	SRST_NVM_PD50_BIT                       (BIT9)
#define 	SRST_NVM_PD51_BIT                       (BIT10)
#define 	SRST_PCIE1_BIT                          (BIT11)
#define 	SRST_PCIE2_BIT                          (BIT12)
#define 	SRST_MRT_BIT                            (BIT14)
#define 	SRST_DZIP_BIT                           (BIT15)
#define 	SRST_FPHY_BIT                           (BIT18)
#define 	SRST_ZQ_BIT                             (BIT19)
#define 	SRST_COR_BIT                            (BIT20)
#define 	SRST_ECC_BIT                            (BIT21)
#define 	SRST_FIP_SYS_BIT                        (BIT22)
#define 	SRST_AHB_BIT                            (BIT24) // AHB

#define		CR_RST_N_FLH_SYS_BIT					(SRST_FIP_SYS_BIT)
#define		CR_RST_N_FLH_ECC_BIT					(SRST_ECC_BIT)
#define		CR_RST_N_PIC_BIT						(SRST_PIC_BIT)
#define		CR_RST_N_COP0_BIT						(SRST_ANDES_BIT)   // COP0 reset
#define		CR_RST_N_COP1_BIT						(SRST_COP1_BIT)    // COP1 reset
#define		CR_RST_N_AXI_BIT						(SRST_AXI_BIT)     // AXI reset
#define		CR_RST_N_SEC_BIT						(SRST_SEC_BIT)
#define		CR_RST_N_MR_BIT							(SRST_MRT_BIT)
#define		CR_RST_N_DMAC_BIT						(SRST_DMAC_BIT)    // DMAC reset
#define		CR_RST_N_AHB_BIT						(SRST_AHB_BIT)
#define		CR_RST_N_PCIE_BIT						(SRST_PCIE1_BIT | SRST_PCIE2_BIT)
#define		CR_RST_N_NVME_BIT						(SRST_NVM_PD50_BIT | SRST_NVM_PD51_BIT)

#define SRST_IP_FW_INIT								(SRST_COP1_BIT | SRST_BMU_BIT | SRST_DMAC_BIT | SRST_SEC_BIT | SRST_DZIP_BIT | \
													SRST_AHB_BIT | SRST_DBUF_BIT | SRST_AXI_BIT | SRST_MRT_BIT)

#define SRST_FIP_ALL                                (SRST_FPHY_BIT | SRST_ZQ_BIT | SRST_COR_BIT | SRST_ECC_BIT | SRST_FIP_SYS_BIT)

#define SRST_ALL                                    (SRST_AXI_BIT | SRST_BMU_BIT | SRST_COP1_BIT | SRST_DBUF_BIT | SRST_ANDES_BIT | \
													 SRST_PIC_BIT | SRST_SEC_BIT | SRST_DMAC_BIT | SRST_APU_BIT | SRST_NVM_PD50_BIT | \
                                                     SRST_NVM_PD51_BIT | SRST_PCIE1_BIT | SRST_PCIE2_BIT | SRST_MRT_BIT | SRST_DZIP_BIT | \
                                                     SRST_FPHY_BIT | SRST_ZQ_BIT | SRST_COR_BIT | SRST_ECC_BIT | SRST_FIP_SYS_BIT | \
                                                     SRST_AHB_BIT)

#define SRST_ALL_WITHOUT_HOST                       (SRST_AXI_BIT | SRST_BMU_BIT | SRST_COP1_BIT | SRST_DBUF_BIT | SRST_ANDES_BIT | \
													 SRST_PIC_BIT | SRST_SEC_BIT | SRST_DMAC_BIT | SRST_MRT_BIT | SRST_DZIP_BIT | \
													 SRST_FPHY_BIT | SRST_ZQ_BIT | SRST_COR_BIT | SRST_ECC_BIT | SRST_FIP_SYS_BIT | \
													 SRST_AHB_BIT)

#define SRST_ALLWITHOUT_PCIE                        (SRST_AXI_BIT | SRST_BMU_BIT | SRST_COP1_BIT | SRST_DBUF_BIT | SRST_ANDES_BIT | \
													 SRST_PIC_BIT | SRST_SEC_BIT | SRST_DMAC_BIT | SRST_APU_BIT | SRST_NVM_PD50_BIT | \
                                                     SRST_NVM_PD51_BIT | SRST_MRT_BIT | SRST_DZIP_BIT | SRST_FPHY_BIT | SRST_ZQ_BIT | \
                                                     SRST_COR_BIT | SRST_ECC_BIT | SRST_FIP_SYS_BIT | SRST_AHB_BIT)

/* Default: 0xFFFFFFFF */
#define R32_SYS0_RESET_CTRL1                        (0x024 >> 2)
#define 	SRST_SYS_BIT                            (BIT0)
#define 	SRST_RNG_BIT                            (BIT2)
#define 	SRST_AHB_HW_EN_BIT                      (BIT16)

#define SRST_ALL1                                   (SRST_SYS_BIT | SRST_RNG_BIT | SRST_AHB_HW_EN_BIT)

/*
 *  +-----------------------------------------------------------------------+
 *  |					POWER Control      									|
 *  +-----------------------------------------------------------------------+
 */
#define SYS0_PWR_CTRL_BASE                          (SYSTEM_PD0_REG_ADDRESS)

#define R8_SYS0_PWR_CTRL                            ((REG8  *) SYS0_PWR_CTRL_BASE)
#define R16_SYS0_PWR_CTRL                           ((REG16 *) SYS0_PWR_CTRL_BASE)
#define R32_SYS0_PWR_CTRL                           ((REG32 *) SYS0_PWR_CTRL_BASE)
#define R64_SYS0_PWR_CTRL							((REG64 *) SYS0_PWR_CTRL_BASE)

/* Default: 0x00000000 */
#define R32_SYS0_PWR_CTRL_PCIE_REG                  (0x040 >> 2)
#define 	PCI_MD_STD_L12_BIT                      (BIT0)
#define 	SR_ISO_PSW_PCIE1_EN_BIT                 (BIT8)
#define 	STB_PCI_PMU_PD51_PD_BIT                 (BIT24)

/* Default: 0x00000101 */
#define R32_SYS0_PWR_CTRL_PCIE1_REG                 (0x044 >> 2)
#define 	SR_HW_PCI_PMU_BIT                       (BIT0)
#define		SR_PD_PSW_PCIE1_SHIFT					(16)
#define		SR_PD_PSW_PCIE1_MASK					(BIT_MASK(12))
#define		SR_PD_PSW_PCIE1							(SR_PD_PSW_PCIE1_MASK << SR_PD_PSW_PCIE1_SHIFT)

/* Default: 0x00000000 */
#define R32_SYS0_PWR_WAKEUP_CTRL_REG                (0x050 >> 2)
#define 	WAKEUP_CTRL_PCIE_BIT                    (BIT0)
#define 	WAKEUP_CTRL_NVME_BIT                    (BIT1)
#define 	WAKEUP_CTRL_RTT_BIT                     (BIT2)
#define 	WAKEUP_CTRL_GPIO_BIT                    (BIT3)
#define 	WAKEUP_CTRL_CLKREQB_BIT                 (BIT4)
#define 	WAKEUP_CTRL_PIC_BIT                     (BIT5)
#define 	WAKEUP_CTRL_PERSTN_BIT                  (BIT6)
#define 	WAKEUP_CTRL_PHY_BIT                  	(BIT7)
#define 	WAKEUP_CTRL_PLN_BIT                  	(BIT8)
#define 	WAKEUP_CTRL_PWRDIS_BIT                  (BIT9)

/* Default: 0x00000000 */
#define R32_SYS0_PWR_WAKEUP_STS_REG                 (0x054 >> 2) // RO, clear enb(0x50) to clear status (0x54)
#define 	WAKEUP_STS_PCIE_BIT                     (BIT0)
#define 	WAKEUP_STS_NVME_BIT                     (BIT1)
#define 	WAKEUP_STS_RTT_BIT                      (BIT2)
#define 	WAKEUP_STS_GPIO_BIT                     (BIT3)
#define 	WAKEUP_STS_CLKREQB_BIT                  (BIT4)
#define 	WAKEUP_STS_PIC_BIT                      (BIT5)
#define 	WAKEUP_STS_PERSTN_BIT                   (BIT6)
#define 	WAKEUP_STS_PHY_BIT                   	(BIT7)
#define 	WAKEUP_STS_PLN_BIT                   	(BIT8)
#define 	WAKEUP_STS_PWRDIS_BIT					(BIT9)

/* Default: 0x00000000 */
#define R32_SYS0_PWR_CTRL_REG                       (0x058 >> 2)
#define 	PD_SYS_TRIG_LPM3_BIT                    (BIT0)
#define 	PD_SYS_TRIG_LPM1_BIT                    (BIT8)

/* Default: 0x00000000 */
#define R32_SYS0_PWR_GPIO_WAKEUP                    (0x060 >> 2)
#define R16_SYS0_PWR_GPIO_WAKEUP_EN                 (0x060 >> 1)
#define		GPIO_WAKEUP_EN_SHIFT					(0)
#define		GPIO_WAKEUP_EN_MASK						(BIT_MASK(16))
#define		GPIO_WAKEUP_EN							(GPIO_WAKEUP_EN_MASK << GPIO_WAKEUP_EN_SHIFT)
#define R16_SYS0_PWR_GPIO_WAKEUP_TYPE               (0x062 >> 1)
#define		GPIO_WAKEUP_TYPE_SHIFT					(0)
#define		GPIO_WAKEUP_TYPE_MASK					(BIT_MASK(2))
#define 	GPIO_WAKEUP_TYPE                        (GPIO_WAKEUP_TYPE_MASK << GPIO_WAKEUP_TYPE_SHIFT)
#define 	GPIO_WAKEUP_0                           (0x00)
#define 	GPIO_WAKEUP_1                           (0x01)
#define 	GPIO_WAKEUP_FALLING                     (0x02)
#define 	GPIO_WAKEUP_RISING                      (0x03)

/* Default: 0x00000000 */
#define R32_SYS0_PWR_CLKEQB_WAKEUP                  (0x064 >> 2)
#define		CLKREQB_WAKEUP_TYPE_SHIFT				(0)
#define		CLKREQB_WAKEUP_TYPE_MASK				(BIT_MASK(2))
#define 	CLKREQB_WAKEUP_TYPE                     (CLKREQB_WAKEUP_TYPE_MASK << CLKREQB_WAKEUP_TYPE_SHIFT)
#define 	CLKREQB_WAKEUP_0                        (0x00)
#define 	CLKREQB_WAKEUP_1                        (0x01)
#define 	CLKREQB_WAKEUP_FALLING                  (0x02)
#define 	CLKREQB_WAKEUP_RISING                   (0x03)

/* Default: 0x00000000 */
#define R32_SYS0_PWR_PERSTN_WAKEUP                  (0x068 >> 2)
#define		PERSTN_WAKEUP_TYPE_SHIFT				(0)
#define		PERSTN_WAKEUP_TYPE_MASK					(BIT_MASK(2))
#define 	PERSTN_WAKEUP_TYPE                      (PERSTN_WAKEUP_TYPE_MASK << PERSTN_WAKEUP_TYPE_SHIFT)
#define 	PERSTN_WAKEUP_0                         (0x00)
#define 	PERSTN_WAKEUP_1                         (0x01)
#define 	PERSTN_WAKEUP_FALLING                   (0x02)
#define 	PERSTN_WAKEUP_RISING                    (0x03)

/* Default: 0x00000000 */
#define R32_SYS0_PWR_PWRDIS_WAKEUP                  (0x06C >> 2)
#define		PWRDIS_WAKEUP_TYPE_SHIFT				(0)
#define		PWRDIS_WAKEUP_TYPE_MASK					(BIT_MASK(2))
#define 	PWRDIS_WAKEUP_TYPE                      (PWRDIS_WAKEUP_TYPE_MASK << PWRDIS_WAKEUP_TYPE_SHIFT)
#define 	PWRDIS_WAKEUP_0                         (0x00)
#define 	PWRDIS_WAKEUP_1                         (0x01)
#define 	PWRDIS_WAKEUP_FALLING                   (0x02)
#define 	PWRDIS_WAKEUP_RISING                    (0x03)

/* Default: 0x030F07FF */
#define R32_SYS0_PWR_CLK_GATE_CTRL                  (0x070 >> 2)
#define 	FLH_CLK_EN_FW_BIT                       (BIT0)
#define 	FLH_SYSCLK_EN_BIT                       (BIT8)
#define 	ECC_CLK_EN_FW_BIT                       (BIT9)
#define 	ZQ_CLK_EN_FW_BIT                        (BIT10)
#define 	AES_CLK_EN_FW_BIT                       (BIT17)
#define 	SHA_CLK_EN_FW_BIT                       (BIT18)
#define 	LZSS_CLK_EN_FW_BIT                      (BIT19)
#define 	TS_CLK_EN_FW_BIT                        (BIT24)
#define 	RNG_CLK_EN_FW_BIT                       (BIT25)

#define PWR_GATING_MASK                             (FLH_CLK_EN_FW_BIT | FLH_SYSCLK_EN_BIT | ECC_CLK_EN_FW_BIT | ZQ_CLK_EN_FW_BIT | \
													 AES_CLK_EN_FW_BIT | SHA_CLK_EN_FW_BIT | LZSS_CLK_EN_FW_BIT | TS_CLK_EN_FW_BIT | \
													 RNG_CLK_EN_FW_BIT)

/*
 *  +-----------------------------------------------------------------------+
 *  |					PMU Control      									|
 *  +-----------------------------------------------------------------------+
 */
#define SYS0_PMU_CTRL_BASE                          (SYSTEM_PD0_REG_ADDRESS)

#define R8_SYS0_PMU_CTRL                            ((REG8  *) SYS0_PMU_CTRL_BASE)
#define R16_SYS0_PMU_CTRL                           ((REG16 *) SYS0_PMU_CTRL_BASE)
#define R32_SYS0_PMU_CTRL                           ((REG32 *) SYS0_PMU_CTRL_BASE)
#define R64_SYS0_PMU_CTRL                           ((REG64 *) SYS0_PMU_CTRL_BASE)

/* Default: 0x00000000 */
#define R32_SYS0_PMU_CTRL_INT_SET                   (0x080 >> 2)
#define R16_SYS0_PMU_CTRL_INT_MASK                  (0x080 >> 1)
#define R16_SYS0_PMU_CTRL_INT_STS                   (0x082 >> 1) // w1c
#define		PMU_INT_ALL_MASK						(BIT_MASK(16))
#define		PMU_INT_PMU_BIT							(BIT2)

/* Default: 0x00000000 */
#define R32_SYS0_PMU_TIMER_LOG1                     (0x088 >> 2) // RO
#define		LOG_RTT_S_COUNT_SYNC_SHIFT				(0)
#define		LOG_RTT_S_COUNT_SYNC_MASK				(BIT_MASK(22))
#define 	LOG_RTT_S_COUNT_SYNC                    (LOG_RTT_S_COUNT_SYNC_MASK << LOG_RTT_S_COUNT_SYNC_SHIFT)

/* Default: 0x00000000 */
#define R32_SYS0_PMU_TIMER_LOG2                     (0x08C >> 2) // RO
#define		LOG_RTT_MS_COUNT_SYNC_SHIFT				(0)
#define		LOG_RTT_MS_COUNT_SYNC_MASK				(BIT_MASK(10))
#define 	LOG_RTT_MS_COUNT_SYNC                   (LOG_RTT_MS_COUNT_SYNC_MASK << LOG_RTT_MS_COUNT_SYNC_SHIFT)

/* Default: 0x00000000 */
#define R32_SYS0_PMU_TIMER_CFG                      (0x090 >> 2)
#define 	PMU_RTT_EN_BIT                          (BIT0)
#define 	START_TRAINING_PMU_BIT                  (BIT8)
#define 	RTT_TRAINING_DONE_SYNC_BIT              (BIT16) // RO

/* Default: 0x00000000 */
#define R32_SYS0_PMU_MS_UW_TIMER                    (0x094 >> 2)
#define		PMU_RTT_TARGET_CNT_SHIFT				(0)
#define		PMU_RTT_TARGET_CNT_MASK					(BIT_MASK(24))
#define 	PMU_RTT_TARGET_CNT                   	(PMU_RTT_TARGET_CNT_MASK << PMU_RTT_TARGET_CNT_SHIFT)

/* Default: 0x00000000 */
#define R32_SYS0_PMU_LPM3_PMIC                      (0x09C >> 2)
#define 	PMU_LPM3_PMIC_LOW_BIT                   (BIT0)
#define 	PMU_LPM3_PMIC_OE_BIT                 	(BIT8)

/* Default: 0x00000000 */
#define R32_SYS0_PMU_TMODE                          (0x0A0 >> 2)
#define 	PMU_TMODE_STOP_SYNC_BIT                 (BIT0) // RO
#define 	PMU_TMODE_INTOSTB_SYNC_BIT              (BIT8) // RO

/* Default: 0x00000400 */
#define R32_SYS0_PMU_PMIC_WAIT                      (0x0A4 >> 2)

/* Default: 0x00000000 */
#define R32_SYS0_PMU_LPM3_GATE                      (0x0A8 >> 2)
#define 	LPM3_CLOCK_GATED_EN_BIT                 (BIT0)

/* Default: 0x00000000 */
#define R32_SYS0_PMU_CLK_CTRL                       (0x0AC >> 2)
#define 	SR_PMU_CLOCK_SWITCH_EN_BIT              (BIT0)
#define 	SR_GATED_PCIE_FREE_RUN_EN_BIT           (BIT8)

/*
 *  +-----------------------------------------------------------------------+
 *  |					MUX Control      									|
 *  +-----------------------------------------------------------------------+
 */
#define SYS0_MUX_CTRL_BASE                          (SYSTEM_PD0_REG_ADDRESS)

#define R8_SYS0_MUX_CTRL                            ((REG8  *) SYS0_MUX_CTRL_BASE)
#define R16_SYS0_MUX_CTRL                           ((REG16 *) SYS0_MUX_CTRL_BASE)
#define R32_SYS0_MUX_CTRL                           ((REG32 *) SYS0_MUX_CTRL_BASE)
#define R64_SYS0_MUX_CTRL                           ((REG64 *) SYS0_MUX_CTRL_BASE)

/* Default: 0x0000FFFF */
#define R32_SYS0_MUX_CTRL_GPIO                      (0x0B0 >> 2) // 1: GPIO mode, 0: option mode
#define R16_SYS0_MUX_CTRL_GPIO                      (0x0B0 >> 1) // 1: GPIO mode, 0: option mode
#define		MUX_GPIO_SEL_SHIFT						(0)
#define		MUX_GPIO_SEL_MASK						(BIT_MASK(16))
#define 	MUX_GPIO_SEL                   			(MUX_GPIO_SEL_MASK << MUX_GPIO_SEL_SHIFT)

/* Default: 0x00000001 */
#define R32_SYS0_MUX_CTRL_CLKREQB                   (0x0B4 >> 2)
#define 	CLKREQB_MUX_SEL_BIT                     (BIT0)

/* Default: 0x00000000 */
#define R32_SYS0_MUX_CTRL_LED                       (0x0B8 >> 2)
#define		LED_MUX_SEL_SHIFT						(0)
#define		LED_MUX_SEL_MASK						(BIT_MASK(2))
#define 	LED_MUX_SEL                   			(LED_MUX_SEL_MASK << LED_MUX_SEL_SHIFT)
#define		LED_OE_CTRL_BY_APU_GPIO					(1)
#define     LED_OTHSERS								(0)

/* Default: 0x00000000 */
#define R32_SYS0_MUX_CTRL_TESSENT                   (0x0BC >> 2)
#define 	SR_TESSENT_BIST_EFUC_SEL_BIT            (BIT0)

/*
 *  +-----------------------------------------------------------------------+
 *  |					GPIO Control      									|
 *  +-----------------------------------------------------------------------+
 */
#define SYS0_GPIO_CTRL_BASE                         (SYSTEM_PD0_REG_ADDRESS)

#define R8_SYS0_GPIO_CTRL                           ((REG8  *) SYS0_GPIO_CTRL_BASE)
#define R16_SYS0_GPIO_CTRL                          ((REG16 *) SYS0_GPIO_CTRL_BASE)
#define R32_SYS0_GPIO_CTRL                          ((REG32 *) SYS0_GPIO_CTRL_BASE)
#define R64_SYS0_GPIO_CTRL                          ((REG64 *) SYS0_GPIO_CTRL_BASE)

/* Default: 0x00000000 */
#define R32_SYS0_GPIO_SETTING                       (0x0C0 >> 2)
#define R16_SYS0_GPIO_SETTING_VALUE                 (0x0C0 >> 1)
#define		GPIO_VALUE_SHIFT						(0)
#define		GPIO_VALUE_MASK							(BIT_MASK(16))
#define 	GPIO_VALUE                   			(GPIO_VALUE_MASK << GPIO_VALUE_SHIFT)
#define R16_SYS0_GPIO_SETTING_OE_N                  (0x0C2 >> 1)
#define		GPIO_OE_N_SHIFT							(0)
#define		GPIO_OE_N_MASK							(BIT_MASK(16))
#define 	GPIO_OE_N                   			(GPIO_OE_N_MASK << GPIO_OE_N_SHIFT)

/*
 *  +-----------------------------------------------------------------------+
 *  |					SRAM Control      									|
 *  +-----------------------------------------------------------------------+
 */
#define SYS0_SRAM_CTRL_BASE                         (SYSTEM_PD0_REG_ADDRESS)

#define R8_SYS0_SRAM_CTRL                           ((REG8  *) SYS0_SRAM_CTRL_BASE)
#define R16_SYS0_SRAM_CTRL                          ((REG16 *) SYS0_SRAM_CTRL_BASE)
#define R32_SYS0_SRAM_CTRL                          ((REG32 *) SYS0_SRAM_CTRL_BASE)
#define R64_SYS0_SRAM_CTRL                          ((REG64 *) SYS0_SRAM_CTRL_BASE)

/* Default: 0x00000000 */
#define R32_SYS0_SRAM_LS_EN                         (0x0DC >> 2)
#define 	SYS_IP_LS_EN_BIT                        (BIT0)

/* Default: 0x00000000 */
#define R32_SYS0_SRAM_PD0_DS                        (0x0FC >> 2)
#define		SR_DBUF0_PD0_SRAM0_DS_SHIFT				(0)
#define		SR_DBUF0_PD0_SRAM0_DS_MASK				(BIT_MASK(8))
#define 	SR_DBUF0_PD0_SRAM0_DS                   (SR_DBUF0_PD0_SRAM0_DS_MASK << SR_DBUF0_PD0_SRAM0_DS_SHIFT)
#define 	SR_SEC_RAMK_DS_BIT                      (BIT8)

/* Default: 0x00000000 */
#define R32_SYS0_SRAM_PD0_SD                        (0x100 >> 2)
#define		CR_DBUF0_PD0_SRAM0_SD_SHIFT				(0)
#define		CR_DBUF0_PD0_SRAM0_SD_MASK				(BIT_MASK(8))
#define 	CR_DBUF0_PD0_SRAM0_SD                   (CR_DBUF0_PD0_SRAM0_SD_MASK << CR_DBUF0_PD0_SRAM0_SD_SHIFT)
#define 	SR_SEC_RAMK_SD_BIT                      (BIT8)

/* Default: 0x00000000 */
#define R32_SYS0_SRAM_PD51_DS                       (0x104 >> 2)
#define 	STB_PCI_PD51_RAM_DS_BIT                 (BIT0)

/* Default: 0x00000000 */
#define R32_SYS0_SRAM_PD51_SD                       (0x108 >> 2)
#define 	STB_PCI_PD51_RAM_SD_BIT                 (BIT0)

/*
 *  +-----------------------------------------------------------------------+
 *  |					PAD Control      									|
 *  +-----------------------------------------------------------------------+
 */
#define SYS0_PAD_CTRL_BASE                          (SYSTEM_PD0_REG_ADDRESS)

#define R8_SYS0_PAD_CTRL                            ((REG8  *) SYS0_PAD_CTRL_BASE)
#define R16_SYS0_PAD_CTRL                           ((REG16 *) SYS0_PAD_CTRL_BASE)
#define R32_SYS0_PAD_CTRL                           ((REG32 *) SYS0_PAD_CTRL_BASE)
#define R64_SYS0_PAD_CTRL                           ((REG64 *) SYS0_PAD_CTRL_BASE)

/* Default: 0x00000000 */
#define R32_SYS0_PAD_PD0_CTRL                       (0x1C0 >> 2)
#define 	PLN_PAD_SYNC_BIT                        (BIT0)        // RO
#define 	CLKREQB_PAD_SYNC_BIT                    (BIT1)        // RO
#define 	PERSTN_PAD_SYNC_BIT                     (BIT2)        // RO
#define 	LED_PAD_SYNC_BIT                        (BIT3)        // RO
#define 	LVCC_PAD_SYNC_BIT                       (BIT4)        // RO
#define 	TCK_PAD_SYNC_BIT                        (BIT5)        // RO
#define 	TMS_PAD_SYNC_BIT                        (BIT6)        // RO
#define 	PWRDIS_PAD_SYNC_BIT                     (BIT7)        // RO
#define		MODE_PAD_SYNC_SHIFT						(8)
#define		MODE_PAD_SYNC_MASK						(BIT_MASK(6))
#define 	MODE_PAD_SYNC                   		(MODE_PAD_SYNC_MASK << MODE_PAD_SYNC_SHIFT)

/* Default: 0x08040818 */
#define R32_SYS0_PAD_PD0_CTRL1                      (0x1C4 >> 2)
#define 	SR_CLKREQB_PAD_CONOF_BIT                (BIT0)
#define 	SR_CLKREQB_PAD_EN_BIT                   (BIT1)
#define 	SR_CLKREQB_PAD_PD75_BIT                 (BIT2)
#define 	SR_CLKREQB_PAD_SONOF_BIT                (BIT3)
#define 	SR_CLKREQB_PAD_GB_BIT                   (BIT4)
#define		SR_CLKREQB_PAD_IODRV_SHIFT				(5)
#define		SR_CLKREQB_PAD_IODRV_MASK				(BIT_MASK(2))
#define 	SR_CLKREQB_PAD_IODRV                    (SR_CLKREQB_PAD_IODRV_MASK << SR_CLKREQB_PAD_IODRV_SHIFT)
#define 	SR_CLKREQB_PAD_PD5P5_BIT                (BIT7)
#define 	SR_PLN_PAD_CONOF_BIT                    (BIT8)
#define 	SR_PLN_PAD_EN_BIT                       (BIT9)
#define 	SR_PLN_PAD_PD75_BIT                     (BIT10)
#define 	SR_PLN_PAD_SONOF_BIT                    (BIT11)
#define 	SR_PLN_PAD_PD5P5_BIT                    (BIT12)
#define 	SR_PERSTN_PAD_CONOF_BIT                 (BIT16)
#define 	SR_PERSTN_PAD_PD75_BIT                  (BIT17)
#define 	SR_PERSTN_PAD_SONOF_BIT                 (BIT18)
#define 	SR_PERSTN_PAD_PD5P5_BIT                 (BIT19)
#define 	SR_PLA_PAD_CONOF_BIT                    (BIT24)
#define 	SR_PLA_PAD_EN_BIT                       (BIT25)
#define 	SR_PLA_PAD_PD75_BIT                     (BIT26)
#define 	SR_PLA_PAD_SONOF_BIT                    (BIT27)
#define 	SR_PLA_PAD_PD5P5_BIT                    (BIT28)

/* Default: 0x00980801 */
#define R32_SYS0_PAD_PD0_CTRL2                      (0x1C8 >> 2)
#define		SR_JTAG_IODRV_SHIFT						(0)
#define		SR_JTAG_IODRV_MASK						(BIT_MASK(2))
#define 	SR_JTAG_IODRV                    		(SR_JTAG_IODRV_MASK << SR_JTAG_IODRV_SHIFT)
#define 	SR_LED_PAD_CONOF_BIT                    (BIT8)
#define 	SR_LED_PAD_EN_BIT                       (BIT9)
#define 	SR_LED_PAD_PD75_BIT                     (BIT10)
#define 	SR_LED_PAD_SONOF_BIT                    (BIT11)
#define 	SR_LED_PAD_PD5P5_BIT                    (BIT12)
#define 	SR_LVCC_PAD_A_BIT                       (BIT16)
#define 	SR_LVCC_PAD_CONOF_BIT                   (BIT17)
#define 	SR_LVCC_PAD_EN_BIT                      (BIT18)
#define 	SR_LVCC_PAD_GB_BIT                      (BIT19)
#define 	SR_LVCC_PAD_PD75_BIT                    (BIT20)
#define 	SR_LVCC_PAD_PU5P5_BIT                   (BIT21)
#define 	SR_LVCC_PAD_PU75_BIT                    (BIT22)
#define 	SR_LVCC_PAD_SONOF_BIT                   (BIT23)
#define 	SR_LVCC_PAD_SR_BIT                      (BIT24)
#define 	SR_LVCC_PAD_PD5P5_BIT                   (BIT25)

/* Default: 0x0C3200CC */
#define R32_SYS0_PAD_PD0_CTRL3                      (0x1CC >> 2)
#define 	SR_TCK_PAD_A_BIT                        (BIT0)
#define 	SR_TCK_PAD_CONOF_BIT                    (BIT1)
#define 	SR_TCK_PAD_EN_BIT                       (BIT2)
#define 	SR_TCK_PAD_GB_BIT                       (BIT3)
#define 	SR_TCK_PAD_PD75_BIT                     (BIT4)
#define 	SR_TCK_PAD_PU5P5_BIT                    (BIT5)
#define 	SR_TCK_PAD_PU75_BIT                     (BIT6)
#define 	SR_TCK_PAD_SONOF_BIT                    (BIT7)
#define 	SR_TCK_PAD_SR_BIT                       (BIT8)
#define 	SR_TCK_PAD_PD5P5_BIT                    (BIT9)
#define 	SR_TMS_PAD_CONOF_BIT                    (BIT16)
#define 	SR_TMS_PAD_GB_BIT                       (BIT17)
#define 	SR_TMS_PAD_PD75_BIT                     (BIT18)
#define 	SR_TMS_PAD_PU5P5_BIT                    (BIT19)
#define 	SR_TMS_PAD_PU75_BIT                     (BIT20)
#define 	SR_TMS_PAD_SONOF_BIT                    (BIT21)
#define 	SR_TMS_PAD_SR_BIT                       (BIT22)
#define 	SR_TMS_PAD_PD5P5_BIT                    (BIT23)
#define 	SR_PWRDIS_PAD_CONOF_BIT                 (BIT24)
#define 	SR_PWRDIS_PAD_EN_BIT                    (BIT25)
#define 	SR_PWRDIS_PAD_PD75_BIT                  (BIT26)
#define 	SR_PWRDIS_PAD_SONOF_BIT                 (BIT27)
#define 	SR_PWRDIS_PAD_PD5P5_BIT                 (BIT28)

/* Default: 0x013200C8 */
#define R32_SYS0_PAD_PD0_CTRL4                      (0x230 >> 2)
#define 	SR_N25_TCK_PAD_A_BIT                    (BIT0)
#define 	SR_N25_TCK_PAD_CONOF_BIT                (BIT1)
#define 	SR_N25_TCK_PAD_EN_BIT                   (BIT2)
#define 	SR_N25_TCK_PAD_GB_BIT                   (BIT3)
#define 	SR_N25_TCK_PAD_PD75_BIT                 (BIT4)
#define 	SR_N25_TCK_PAD_PU5P5_BIT                (BIT5)
#define 	SR_N25_TCK_PAD_PU75_BIT                 (BIT6)
#define 	SR_N25_TCK_PAD_SONOF_BIT                (BIT7)
#define 	SR_N25_TCK_PAD_SR_BIT                   (BIT8)
#define 	SR_N25_TCK_PAD_PD5P5_BIT                (BIT9)
#define 	SR_N25_TMS_PAD_CONOF_BIT                (BIT16)
#define 	SR_N25_TMS_PAD_GB_BIT                   (BIT17)
#define 	SR_N25_TMS_PAD_PD75_BIT                 (BIT18)
#define 	SR_N25_TMS_PAD_PU5P5_BIT                (BIT19)
#define 	SR_N25_TMS_PAD_PU75_BIT                 (BIT20)
#define 	SR_N25_TMS_PAD_SONOF_BIT                (BIT21)
#define 	SR_N25_TMS_PAD_SR_BIT                   (BIT22)
#define 	SR_N25_TMS_PAD_PD5P5_BIT                (BIT23)
#define		SR_N25_JTAG_IODRV_SHIFT					(24)
#define		SR_N25_JTAG_IODRV_MASK					(BIT_MASK(2))
#define 	SR_N25_JTAG_IODRV                   	(SR_N25_JTAG_IODRV_MASK << SR_N25_JTAG_IODRV_SHIFT)

/* Default: 0xFFFF0000 */
#define R32_SYS0_GPIO_PAD_CTRL1                     (0x1D0 >> 2)
#define R16_SYS0_GPIO_PAD_CONOF						(0x1D0 >> 1)
#define		SR_GPIO_PAD_CONOF_SHIFT					(0)
#define		SR_GPIO_PAD_CONOF_MASK					(BIT_MASK(16))
#define 	SR_GPIO_PAD_CONOF                   	(SR_GPIO_PAD_CONOF_MASK << SR_GPIO_PAD_CONOF_SHIFT)
#define R16_SYS0_GPIO_PAD_GB						(0x1D2 >> 1)
#define		SR_GPIO_PAD_GB_SHIFT					(0)
#define		SR_GPIO_PAD_GB_MASK						(BIT_MASK(16))
#define 	SR_GPIO_PAD_GB                   		(SR_GPIO_PAD_GB_MASK << SR_GPIO_PAD_GB_SHIFT)

/* Default: 0xFFFF0000 */
#define R32_SYS0_GPIO_PAD_CTRL2                     (0x1D4 >> 2)
#define R16_SYS0_GPIO_PAD_PU5P5						(0x1D4 >> 1)
#define		SR_GPIO_PAD_PU5P5_SHIFT					(0)
#define		SR_GPIO_PAD_PU5P5_MASK					(BIT_MASK(16))
#define 	SR_GPIO_PAD_PU5P5                   	(SR_GPIO_PAD_PU5P5_MASK << SR_GPIO_PAD_PU5P5_SHIFT)
#define R16_SYS0_GPIO_PAD_SONOF						(0x1D6 >> 1)
#define		SR_GPIO_PAD_SONOF_SHIFT					(0)
#define		SR_GPIO_PAD_SONOF_MASK					(BIT_MASK(16))
#define 	SR_GPIO_PAD_SONOF                   	(SR_GPIO_PAD_SONOF_MASK << SR_GPIO_PAD_SONOF_SHIFT)

/* Default: 0xFFFF0000 */
#define R32_SYS0_GPIO_PAD_CTRL3                     (0x1D8 >> 2)
#define R16_SYS0_GPIO_PAD_PD						(0x1D8 >> 1)
#define		SR_GPIO_PAD_PD75_SHIFT					(0)
#define		SR_GPIO_PAD_PD75_MASK					(BIT_MASK(16))
#define 	SR_GPIO_PAD_PD75                   		(SR_GPIO_PAD_PD75_MASK << SR_GPIO_PAD_PD75_SHIFT)
#define R16_SYS0_GPIO_PAD_PU75						(0x1DA >> 1)
#define		SR_GPIO_PAD_PU75_SHIFT					(0)
#define		SR_GPIO_PAD_PU75_MASK					(BIT_MASK(16))
#define 	SR_GPIO_PAD_PU75                   		(SR_GPIO_PAD_PU75_MASK << SR_GPIO_PAD_PU75_SHIFT)

/* Default: 0x00010000 */
#define R32_SYS0_GPIO_PAD_CTRL4                     (0x1DC >> 2)
#define R16_SYS0_GPIO_PAD_SR						(0x1DC >> 1)
#define		SR_GPIO_PAD_SR_SHIFT					(0)
#define		SR_GPIO_PAD_SR_MASK						(BIT_MASK(16))
#define 	SR_GPIO_PAD_SR                   		(SR_GPIO_PAD_SR_MASK << SR_GPIO_PAD_SR_SHIFT)
#define R8_SYS0_GPIO_PAD_IODRV						(0x1DE >> 0)
#define		SR_GPIO_PAD_IODRV_SHIFT					(0)
#define		SR_GPIO_PAD_IODRV_MASK					(BIT_MASK(2))
#define 	SR_GPIO_PAD_IODRV                   	(SR_GPIO_PAD_IODRV_MASK << SR_GPIO_PAD_IODRV_SHIFT)
#define R8_SYS0_GPIO_PAD_CFG						(0x1DF >> 0)
#define		SR_GPIO_PAD_CFG_SHIFT					(0)
#define		SR_GPIO_PAD_CFG_MASK					(BIT_MASK(2))
#define 	SR_GPIO_PAD_CFG                   		(SR_GPIO_PAD_CFG_MASK << SR_GPIO_PAD_CFG_SHIFT)
#define		SR_GPIO_PAD_PMODE_BIT					(BIT2)

/* Default: 0x00000000 */
#define R32_SYS0_GPIO_PAD_CTRL5                     (0x250 >> 2)
#define R16_SYS0_GPIO_PAD_PD5P5						(0x250 >> 1)
#define		SR_GPIO_PAD_PD5P5_SHIFT					(0)
#define		SR_GPIO_PAD_PD5P5_MASK					(BIT_MASK(16))
#define 	SR_GPIO_PAD_PD5P5                   	(SR_GPIO_PAD_PD5P5_MASK << SR_GPIO_PAD_PD5P5_SHIFT)

/* Default: 0x00000000 */
#define R32_SYS0_CLAMP_CTRL                         (0x1E0 >> 2)
#define 	CR_FIP_ZQCAL_BIT                        (BIT0)

/* Default: 0x00000000 */
#define R32_SYS0_CLAMP_PAD_CTRL1                    (0x1E4 >> 2)
#define 	SR_CTRSTBYB_IN_H_BIT                    (BIT0)
#define 	SR_CTRSTBYB_IN_V_BIT                    (BIT1)
#define 	SR_CTRSTBYB_IN_C_BIT                    (BIT2)
#define 	SR_CTRSTBL2H_IN_H_BIT                   (BIT16)
#define 	SR_CTRSTBL2H_IN_V_BIT                   (BIT17)
#define 	SR_CTRSTBL2H_IN_C_BIT                   (BIT18)
#define 	SR_STBY_DEGLITCH_EN_H_BIT               (BIT24)
#define 	SR_STBY_DEGLITCH_EN_V_BIT               (BIT25)
#define 	SR_STBY_DEGLITCH_EN_C_BIT               (BIT26)

/* Default: 0x09090909 */
#define R32_SYS0_CLAMP_PAD_CTRL2                    (0x1E8 >> 2)
#define R8_SYS0_CLAMP_PAD_CTRL2                     (0x1E8 >> 0)
#define		SR_DRVN_IN_CH0_SHIFT					(0)
#define		SR_DRVN_IN_CH0_MASK						(BIT_MASK(5))
#define 	SR_DRVN_IN_CH0                   		(SR_DRVN_IN_CH0_MASK << SR_DRVN_IN_CH0_SHIFT)
#define		SR_DRVN_IN_CH1_SHIFT					(8)
#define		SR_DRVN_IN_CH1_MASK						(BIT_MASK(5))
#define 	SR_DRVN_IN_CH1                   		(SR_DRVN_IN_CH1_MASK << SR_DRVN_IN_CH1_SHIFT)
#define		SR_DRVN_IN_CH2_SHIFT					(16)
#define		SR_DRVN_IN_CH2_MASK						(BIT_MASK(5))
#define 	SR_DRVN_IN_CH2                   		(SR_DRVN_IN_CH2_MASK << SR_DRVN_IN_CH2_SHIFT)
#define		SR_DRVN_IN_CH3_SHIFT					(24)
#define		SR_DRVN_IN_CH3_MASK						(BIT_MASK(5))
#define 	SR_DRVN_IN_CH3                   		(SR_DRVN_IN_CH3_MASK << SR_DRVN_IN_CH3_SHIFT)

/* Default: 0x0A0A0A0A */
#define R32_SYS0_CLAMP_PAD_CTRL3                    (0x1EC >> 2)
#define R8_SYS0_CLAMP_PAD_CTRL3                     (0x1EC >> 0)
#define		SR_DRVP_IN_CH0_SHIFT					(0)
#define		SR_DRVP_IN_CH0_MASK						(BIT_MASK(5))
#define 	SR_DRVP_IN_CH0                   		(SR_DRVP_IN_CH0_MASK << SR_DRVP_IN_CH0_SHIFT)
#define		SR_DRVP_IN_CH1_SHIFT					(8)
#define		SR_DRVP_IN_CH1_MASK						(BIT_MASK(5))
#define 	SR_DRVP_IN_CH1                   		(SR_DRVP_IN_CH1_MASK << SR_DRVP_IN_CH1_SHIFT)
#define		SR_DRVP_IN_CH2_SHIFT					(16)
#define		SR_DRVP_IN_CH2_MASK						(BIT_MASK(5))
#define 	SR_DRVP_IN_CH2                   		(SR_DRVP_IN_CH2_MASK << SR_DRVP_IN_CH2_SHIFT)
#define		SR_DRVP_IN_CH3_SHIFT					(24)
#define		SR_DRVP_IN_CH3_MASK						(BIT_MASK(5))
#define 	SR_DRVP_IN_CH3                   		(SR_DRVP_IN_CH3_MASK << SR_DRVP_IN_CH3_SHIFT)

/* Default: 0x00000000 */
#define R32_SYS0_VFPHYA_CTRL                        (0x1F0 >> 2)
#define 	CR_FIO_SET_BY_SYS_BIT                   (BIT0)
#define 	SR_FPHYA_EN_PSW_FPHYA_BIT               (BIT8)

/* Default: 0x00000000 */
#define R32_SYS0_VFPHYA_PAD_CTRL0                   (0x1F4 >> 2)
#define 	VOUT_REGFPHYA_CH01_SYNC_BIT             (BIT0)
#define 	VOUT_REGFPHYA_CH23_SYNC_BIT             (BIT8)

/* Default: 0x00000200 */
#define R32_SYS0_VFPHYA_PAD_CTRL1                   (0x1F8 >> 2)
#define 	SR_VFPHY01_BYPASS_VREG_BIT              (BIT0)
#define 	SR_VFPHY01_DIS_SOFT_BIT                 (BIT1)
#define 	SR_VFPHY01_EN_REG_BIT                   (BIT2)
#define 	SR_VFPHY01_FG_BIT                       (BIT3)
#define 	SR_VFPHY01_IDC_PD_BIT                   (BIT4)
#define 	SR_VFPHY01_PDB_BGP_BIT                  (BIT5)
#define		SR_VFPHY01_CFG_SHIFT					(8)
#define		SR_VFPHY01_CFG_MASK						(BIT_MASK(4))
#define 	SR_VFPHY01_CFG                   		(SR_VFPHY01_CFG_MASK << SR_VFPHY01_CFG_SHIFT)
#define		SR_VFPHY01_PREG_TRIM_SHIFT				(16)
#define		SR_VFPHY01_PREG_TRIM_MASK				(BIT_MASK(5))
#define 	SR_VFPHY01_PREG_TRIM                   	(SR_VFPHY01_PREG_TRIM_MASK << SR_VFPHY01_PREG_TRIM_SHIFT)

/* Default: 0x00000200 */
#define R32_SYS0_VFPHYA_PAD_CTRL2                   (0x1FC >> 2)
#define 	SR_VFPHY23_BYPASS_VREG_BIT              (BIT0)
#define 	SR_VFPHY23_DIS_SOFT_BIT                 (BIT1)
#define 	SR_VFPHY23_EN_REG_BIT                   (BIT2)
#define 	SR_VFPHY23_FG_BIT                       (BIT3)
#define 	SR_VFPHY23_IDC_PD_BIT                   (BIT4)
#define 	SR_VFPHY23_PDB_BGP_BIT                  (BIT5)
#define		SR_VFPHY23_CFG_SHIFT					(8)
#define		SR_VFPHY23_CFG_MASK						(BIT_MASK(4))
#define 	SR_VFPHY23_CFG                   		(SR_VFPHY23_CFG_MASK << SR_VFPHY23_CFG_SHIFT)
#define		SR_VFPHY23_PREG_TRIM_SHIFT				(16)
#define		SR_VFPHY23_PREG_TRIM_MASK				(BIT_MASK(5))
#define 	SR_VFPHY23_PREG_TRIM                   	(SR_VFPHY23_PREG_TRIM_MASK << SR_VFPHY23_PREG_TRIM_SHIFT)

/* Default: 0x00000000 */
#define R32_SYS0_ZQ_PAD_TRIM                        (0x210 >> 2)
#define R8_SYS0_ZQ_PAD_TRIM                         (0x210 >> 0)
#define		SR_ZQ_RES_TRIM_IN_SHIFT					(0)
#define		SR_ZQ_RES_TRIM_IN_MASK					(BIT_MASK(8))
#define 	SR_ZQ_RES_TRIM_IN                   	(SR_ZQ_RES_TRIM_IN_MASK << SR_ZQ_RES_TRIM_IN_SHIFT)

/* Default: 0x00000000 */
#define R32_SYS0_PAD_MUX_FW_OPT1                    (0x214 >> 2)
#define 	MUX_FW_OPT1_BIT                         (BIT0)

/* Default: 0x00000000 */
#define R32_SYS0_PAD_LED_OPT                        (0x218 >> 2)
#define 	CR_LED_SO_OPT_BIT                       (BIT0)
#define     SONOF_PADC2								(1)
#define 	APU_GPIO_DSA_ASSERT_OR_PADC				(0)

enum PAD_SETTING {
	PULL_UP_75K     = R16_SYS0_GPIO_PAD_PU75,
	PULL_UP_5P5K    = R16_SYS0_GPIO_PAD_PU5P5,
	PULL_DOWN_5P5K	= R16_SYS0_GPIO_PAD_PD5P5,
	PULL_DOWN_75K   = R16_SYS0_GPIO_PAD_PD
};

/*
 *  +-----------------------------------------------------------------------+
 *  |					ANALOG Control      								|
 *  +-----------------------------------------------------------------------+
 */
#define SYS0_ANALOG_CTRL_BASE                       (SYSTEM_PD0_REG_ADDRESS)

#define R8_SYS0_ANALOG_CTRL                         ((REG8  *) SYS0_ANALOG_CTRL_BASE)
#define R16_SYS0_ANALOG_CTRL                        ((REG16 *) SYS0_ANALOG_CTRL_BASE)
#define R32_SYS0_ANALOG_CTRL                        ((REG32 *) SYS0_ANALOG_CTRL_BASE)
#define R64_SYS0_ANALOG_CTRL                        ((REG64 *) SYS0_ANALOG_CTRL_BASE)

/* Default: 0x00000000 */
#define R32_SYS0_AIP_REGVDTOSCTS                    (0x320 >> 2)
#define 	SR_FUSE_G_BIT                           (BIT0)
#define R8_SYS0_AIP_DET_TRIM_MODE                   (0x321 >> 0)
#define		SR_DET_TRIM_MODE_SHIFT					(0)
#define		SR_DET_TRIM_MODE_MASK					(BIT_MASK(4))
#define 	SR_DET_TRIM_MODE                   		(SR_DET_TRIM_MODE_MASK << SR_DET_TRIM_MODE_SHIFT)

/* Default: 0x00000000 */
#define R32_SYS0_AIP_REG_CTRL0                      (0x324 >> 2)
#define 	SR_DET_SELFT_RDY_SYNC_BIT               (BIT0)  // RO
#define R8_SYS0_AIP_DET_SELFT_CODE_SYNC             (0x325 >> 0)
#define		SR_DET_SELFT_CODE_SYNC_SHIFT			(0)
#define		SR_DET_SELFT_CODE_SYNC_MASK				(BIT_MASK(6))
#define 	SR_DET_SELFT_CODE_SYNC                  (SR_DET_SELFT_CODE_SYNC_MASK << SR_DET_SELFT_CODE_SYNC_SHIFT) // RO

/* Default: 0x00000100 */
#define R32_SYS0_AIP_REG_CTRL1                      (0x328 >> 2)
#define 	SR_EN_OCP_VPHY12_BIT                    (BIT0)
#define 	SR_EN_REG_VPHY12_BIT                    (BIT8)
#define 	SR_DIS_SOFT_VPHY12_BIT                  (BIT16)
#define 	SR_UPDATE_TRIM_REG_BIT                  (BIT24)

/* Default: 0x00000000 */
#define R32_SYS0_AIP_REG_CTRL2                      (0x32C >> 2)
#define 	SR_IREGPHY12_PD_BIT                     (BIT0)
#define 	SR_STB_REG_VPHY12_BIT                   (BIT8)
#define 	SR_IREG18_PD_BIT                        (BIT16)
#define 	SR_SOFT_LVL_VPHY12_BIT                  (BIT24)

/* Default: 0x00000000 */
#define R32_SYS0_AIP_REG_CTRL3                      (0x330 >> 2)
#define		SR_CFG_REG_SHIFT						(0)
#define		SR_CFG_REG_MASK							(BIT_MASK(12))
#define 	SR_CFG_REG                  			(SR_CFG_REG_MASK << SR_CFG_REG_SHIFT)
#define		SR_CFG_OCP_VPHY12_SHIFT					(16)
#define		SR_CFG_OCP_VPHY12_MASK					(BIT_MASK(4))
#define 	SR_CFG_OCP_VPHY12                  		(SR_CFG_OCP_VPHY12_MASK << SR_CFG_OCP_VPHY12_SHIFT)
#define		SR_HIGHPHY12_SHIFT						(24)
#define		SR_HIGHPHY12_MASK						(BIT_MASK(2))
#define 	SR_HIGHPHY12                  			(SR_HIGHPHY12_MASK << SR_HIGHPHY12_SHIFT)
#define		SR_LOWPHY12_SHIFT						(28)
#define		SR_LOWPHY12_MASK						(BIT_MASK(3))
#define 	SR_LOWPHY12                  			(SR_LOWPHY12_MASK << SR_LOWPHY12_SHIFT)

/* Default: 0x00000000 */
#define R32_SYS0_AIP_REG_CTRL4                      (0x334 >> 2)
#define		SR_PREG0_TRIM_SHIFT						(0)
#define		SR_PREG0_TRIM_MASK						(BIT_MASK(6))
#define 	SR_PREG0_TRIM                  			(SR_PREG0_TRIM_MASK << SR_PREG0_TRIM_SHIFT)
#define		SR_PREG_V18_TRIM_SHIFT					(8)
#define		SR_PREG_V18_TRIM_MASK					(BIT_MASK(6))
#define 	SR_PREG_V18_TRIM                  		(SR_PREG_V18_TRIM_MASK << SR_PREG_V18_TRIM_SHIFT)

/* Default: 0x00000000 */
#define R32_SYS0_AIP_VDT_CTRL0                      (0x338 >> 2)   // RO
#define		SR_TESTOUT_SYNC_SHIFT					(0)
#define		SR_TESTOUT_SYNC_MASK					(BIT_MASK(6))
#define 	SR_TESTOUT_SYNC                  		(SR_TESTOUT_SYNC_MASK << SR_TESTOUT_SYNC_SHIFT)

/* Default: 0x00000000 */
#define R32_SYS0_AIP_VDT_CTRL1                      (0x33C >> 2)
#define 	SR_UPDATE_TRIM_VDT_BIT                  (BIT8)
#define 	SR_DISCHARGE_VDDFIO_PD_BIT				(BIT16)

/* Default: 0x00000000 */
#define R32_SYS0_AIP_VDT_CTRL2                      (0x340 >> 2)
#define		SR_HVDT_GPIO18_SHIFT					(0)
#define		SR_HVDT_GPIO18_MASK						(BIT_MASK(2))
#define 	SR_HVDT_GPIO18                  		(SR_HVDT_GPIO18_MASK << SR_HVDT_GPIO18_SHIFT)
#define		SR_HVDT_VCCAH_SHIFT						(2)
#define		SR_HVDT_VCCAH_MASK						(BIT_MASK(2))
#define 	SR_HVDT_VCCAH                  			(SR_HVDT_VCCAH_MASK << SR_HVDT_VCCAH_SHIFT)
#define		SR_HVDT_FC_SHIFT						(4)
#define		SR_HVDT_FC_MASK							(BIT_MASK(2))
#define 	SR_HVDT_FC                  			(SR_HVDT_FC_MASK << SR_HVDT_FC_SHIFT)
#define		SR_HVDT_FIO12_SHIFT						(6)
#define		SR_HVDT_FIO12_MASK						(BIT_MASK(2))
#define 	SR_HVDT_FIO12                  			(SR_HVDT_FIO12_MASK << SR_HVDT_FIO12_SHIFT)
#define 	SR_HVDT_PSW_CORE_SYS_BIT                (BIT8)
#define 	SR_HVDT_VCCK_BIT                        (BIT9)
#define 	SR_HVDT_VPHY12_BIT                      (BIT10)
#define 	SR_HVDT_PSW_CORE_PCIE1_BIT              (BIT11)
#define 	SR_HVDT_AVDDT_BIT                       (BIT12)

/* Default: 0x00000000 */
#define R32_SYS0_AIP_VDT_CTRL3                      (0x344 >> 2)
#define 	SR_PD_PRDY_EFU_BIT                      (BIT0)
#define 	SR_PD_PRDY_FIO_BIT                      (BIT1)
#define 	SR_PD_PRDY_GPIO_BIT                     (BIT2)
#define 	SR_PD_VDT_AVDDT_BIT                     (BIT3)
#define 	SR_PD_VDT_FC_BIT                        (BIT4)
#define 	SR_PD_VDT_FIO12_BIT                     (BIT5)
#define 	SR_PD_VDT_GPIO18_BIT                    (BIT6)
#define 	SR_PD_VDT_PSW_CORE_PCIE1_BIT            (BIT7)
#define 	SR_PD_VDT_PSW_CORE_SYS_BIT              (BIT8)
#define 	SR_PD_VDT_VCCAH_BIT                     (BIT9)
#define 	SR_PD_VDT_VCCK_BIT                      (BIT10)
#define 	SR_PD_VDT_VPHY12_BIT                    (BIT11)
#define 	SR_PD_VDT_ALL                           (0xFFF)

/* Default: 0x00000000 */
#define R32_SYS0_AIP_VDT_CTRL4                      (0x348 >> 2)
#define R8_SYS0_AIP_VDT_PVDT_FC_TRIM                (0x348 >> 0)
#define		SR_PVDT_FC_TRIM_SHIFT					(0)
#define		SR_PVDT_FC_TRIM_MASK					(BIT_MASK(6))
#define 	SR_PVDT_FC_TRIM                  		(SR_PVDT_FC_TRIM_MASK << SR_PVDT_FC_TRIM_SHIFT)
#define R8_SYS0_AIP_VDT_FIO12_TRIM                  (0x349 >> 0)
#define		SR_FIO12_TRIM_SHIFT						(0)
#define		SR_FIO12_TRIM_MASK						(BIT_MASK(6))
#define 	SR_FIO12_TRIM                  			(SR_FIO12_TRIM_MASK << SR_FIO12_TRIM_SHIFT)
#define R8_SYS0_AIP_VDT_PVDT_TRIM                   (0x34A >> 0)
#define		SR_PVDT_TRIM_SHIFT						(0)
#define		SR_PVDT_TRIM_MASK						(BIT_MASK(6))
#define 	SR_PVDT_TRIM                  			(SR_PVDT_TRIM_MASK << SR_PVDT_TRIM_SHIFT)

/* Default: 0x000003FF */
#define R32_SYS0_AIP_VDT_CTRL5                      (0x34C >> 2)
#define 	VOUT_VCCK_MASK_BIT                      (BIT4)
#define 	VOUTH_MASK_BIT                          (BIT6)

/* Default: 0x00000000 */
#define R32_SYS0_AIP_VDT_VOUT                       (0x350 >> 2)   // W1C
#define 	VDT_VOUT_FC_STS_BIT                     (BIT0)
#define 	VDT_VOUT_FIO12_STS_BIT                  (BIT1)
#define 	VDT_VOUT_VPSW_PCIE1_STS_BIT             (BIT2)
#define 	VDT_VOUT_VPSW_SYS_STS_BIT               (BIT3)
#define 	VDT_VOUT_VPHY12_STS_BIT                 (BIT4)
#define 	VDT_VOUT_AVDDT_STS_BIT                  (BIT5)
#define 	VDT_VOUT_GPIO18_STS_BIT                 (BIT6)

#define VDT_VOUT_STS_MASK                           (VDT_VOUT_FC_STS_BIT | VDT_VOUT_FIO12_STS_BIT | VDT_VOUT_VPSW_PCIE1_STS_BIT |\
                                                    VDT_VOUT_VPSW_SYS_STS_BIT | VDT_VOUT_VPHY12_STS_BIT | VDT_VOUT_AVDDT_STS_BIT |\
                                                    VDT_VOUT_GPIO18_STS_BIT)

/* Default: 0x00000000 */
#define R32_SYS0_AIP_OSC_CTRL0                      (0x354 >> 2)
#define 	SR_OSC_SYS_SELFT_RDY_SYNC_BIT           (BIT0)    // RO
#define		SR_OSC_SYS_SELFT_CODE_SYNC_SHIFT		(8)
#define		SR_OSC_SYS_SELFT_CODE_SYNC_MASK			(BIT_MASK(10))
#define 	SR_OSC_SYS_SELFT_CODE_SYNC              (SR_OSC_SYS_SELFT_CODE_SYNC_MASK << SR_OSC_SYS_SELFT_CODE_SYNC_SHIFT)
#define 	SR_OSC_SYS_DIS_FLAG_SYNC_BIT            (BIT24)    // RO

/* Default: 0x00000001 */
#define R32_SYS0_AIP_OSC_CTRL1                      (0x358 >> 2)
#define 	SR_EN_OSC_SYS_BIT                       (BIT0)

/* Default: 0x00000000 */
#define R32_SYS0_AIP_OSC_CTRL2                      (0x35C >> 2)
#define 	SR_UPDATE_TRIM_OSC_SYS_BIT              (BIT0)

/* Default: 0x00000001 */
#define R32_SYS0_AIP_OSC_CTRL3                      (0x360 >> 2)
#define 	SR_EN_LFOSC_BIT                         (BIT0)

/* Default: 0x00000000 */
#define R32_SYS0_AIP_OSC_CTRL4                      (0x364 >> 2)
#define		SR_OSC_SYS_SEL_SHIFT					(0)
#define		SR_OSC_SYS_SEL_MASK						(BIT_MASK(2))
#define 	SR_OSC_SYS_SEL              			(SR_OSC_SYS_SEL_MASK << SR_OSC_SYS_SEL_SHIFT)

/* Default: 0x00000000 */
#define R32_SYS0_AIP_OSC_CTRL5                      (0x368 >> 2)
#define R16_SYS0_AIP_OSC_POSC_SYS_SEL01_SEL00_TRIM  (0x368 >> 1)
#define		SR_POSC_SYS_SEL01_SEL00_TRIM_SHIFT		(0)
#define		SR_POSC_SYS_SEL01_SEL00_TRIM_MASK		(BIT_MASK(10))
#define 	SR_POSC_SYS_SEL01_SEL00_TRIM            (SR_POSC_SYS_SEL01_SEL00_TRIM_MASK << SR_POSC_SYS_SEL01_SEL00_TRIM_SHIFT)
#define R16_SYS0_AIP_OSC_POSC_SYS_SEL10_TRIM        (0x36A >> 1)
#define		SR_POSC_SYS_SEL10_TRIM_SHIFT			(0)
#define		SR_POSC_SYS_SEL10_TRIM_MASK				(BIT_MASK(10))
#define 	SR_POSC_SYS_SEL10_TRIM            		(SR_POSC_SYS_SEL10_TRIM_MASK << SR_POSC_SYS_SEL10_TRIM_SHIFT)

/* Default: 0x00000000 */
#define R32_SYS0_AIP_OSC_CTRL6                      (0x36C >> 2)
#define R16_SYS0_AIP_OSC_POSC_SYS_SEL11_TRIM        (0x36C >> 1)
#define		SR_POSC_SYS_SEL11_TRIM_SHIFT			(0)
#define		SR_POSC_SYS_SEL11_TRIM_MASK				(BIT_MASK(10))
#define 	SR_POSC_SYS_SEL11_TRIM            		(SR_POSC_SYS_SEL11_TRIM_MASK << SR_POSC_SYS_SEL11_TRIM_SHIFT)
#define R16_SYS0_AIP_OSC_PLOSC_TRIM                	(0x36E >> 1)
#define		SR_PFOSC_TRIM_SHIFT						(0)
#define		SR_PFOSC_TRIM_MASK						(BIT_MASK(6))
#define 	SR_PFOSC_TRIM            				(SR_PFOSC_TRIM_MASK << SR_PFOSC_TRIM_SHIFT)

/* Default: 0x00000000 */
#define R32_SYS0_AIP_OSC_CTRL7                      (0x370 >> 2)
#define 	SR_OSC_SYS_TRIM_MODE_BIT				(BIT0)
#define 	SR_OSC_SYS_BYPASS_MODE_BIT				(BIT1)
#define R8_SYS0_AIP_OSC_CFG_OSC_SYS               	(0x371 >> 0)
#define		SR_CFG_OSC_SYS_SHIFT					(0)
#define		SR_CFG_OSC_SYS_MASK						(BIT_MASK(8))
#define 	SR_CFG_OSC_SYS            				(SR_PFOSC_TRIM_MASK << SR_PFOSC_TRIM_MASK)
#define R8_SYS0_AIP_OSC_POSC_SYS_TC_TRIM            (0x372 >> 0)
#define		SR_POSC_SYS_TC_TRIM_SHIFT				(0)
#define		SR_POSC_SYS_TC_TRIM_MASK				(BIT_MASK(6))
#define 	SR_POSC_SYS_TC_TRIM            			(SR_POSC_SYS_TC_TRIM_MASK << SR_POSC_SYS_TC_TRIM_SHIFT)
#define R8_SYS0_AIP_OSC_CFG_LFOSC_TC               	(0x373 >> 0)
#define		SR_CFG_LFOSC_TC_SHIFT					(0)
#define		SR_CFG_LFOSC_TC_MASK					(BIT_MASK(3))
#define 	SR_CFG_LFOSC_TC            				(SR_CFG_LFOSC_TC_MASK << SR_CFG_LFOSC_TC_SHIFT)

/* Default: 0x00000001 */
#define R32_SYS0_AIP_OSC_CTRL8                      (0x374 >> 2)
#define 	SR_OSC_SYS_SELFT_RESET_BIT				(BIT0)
#define		SR_OSC_SYS_SELFT_PERIOD_SHIFT			(8)
#define		SR_OSC_SYS_SELFT_PERIOD_MASK			(BIT_MASK(2))
#define 	SR_OSC_SYS_SELFT_PERIOD            		(SR_OSC_SYS_SELFT_PERIOD_MASK << SR_OSC_SYS_SELFT_PERIOD_SHIFT)
#define 	SR_OSC_SYS_SELFT_OSC_SEL_BIT			(BIT16)

/* Default: 0x00000000 */
#define R32_SYS0_AIP_OSC_CTRL9                      (0x378 >> 2)
#define		SR_OSC_SYS_SELFT_DEC_SEL_BIT			(BIT0)
#define R16_SYS0_AIP_OSC_SELFT_DEC                  (0x37A >> 1)
#define		SR_OSC_SYS_SELFT_DEC_SHIFT				(0)
#define		SR_OSC_SYS_SELFT_DEC_MASK				(BIT_MASK(12))
#define 	SR_OSC_SYS_SELFT_DEC            		(SR_OSC_SYS_SELFT_DEC_MASK << SR_OSC_SYS_SELFT_DEC_SHIFT)

/* Default: 0x00000000 */
#define R32_SYS0_AIP_TS_CTRL0                       (0x37C >> 2) // RO
#define		SR_TS_TCODE_SHIFT						(0)
#define		SR_TS_TCODE_MASK						(BIT_MASK(10))
#define 	SR_TS_TCODE            					(SR_TS_TCODE_MASK << SR_TS_TCODE_SHIFT)
#define 	SR_TS_TCODE_RDY_BIT                     (BIT16)
#define 	SR_TS_TEMP_HIGH1_BIT                    (BIT24)
#define 	SR_TS_TEMP_HIGH2_BIT                    (BIT25)

/* Default: 0x01010001 */
#define R32_SYS0_AIP_TS_CTRL1                       (0x380 >> 2)
#define 	SR_TS_RESET_BIT                         (BIT0)
#define 	SR_TS_ENABLE_BIT                        (BIT8)
#define 	SR_TS_CHOP_EN_BIT                       (BIT16)
#define 	SR_TS_DEC_SEL_BIT                       (BIT24)

/* Default: 0x000A8401 */
#define R32_SYS0_AIP_TS_CTRL2                       (0x384 >> 2)
#define 	SR_TS_INTCLK_SEL_BIT                    (BIT0)
#define 	SR_TS_CLK_CFG_BIT                       (BIT1)
#define		SR_TS_CLK_SEL_SHIFT						(2)
#define		SR_TS_CLK_SEL_MASK						(BIT_MASK(2))
#define 	SR_TS_CLK_SEL            				(SR_TS_CLK_SEL_MASK << SR_TS_CLK_SEL_SHIFT)
#define 	SR_TS_BYPASS_MODE_BIT                   (BIT4)
#define		SR_TS_DEC_DENOM_SHIFT					(8)
#define		SR_TS_DEC_DENOM_MASK					(BIT_MASK(12))
#define 	SR_TS_DEC_DENOM            				(SR_TS_DEC_DENOM_MASK << SR_TS_DEC_DENOM_SHIFT)

/* Default: 0x00000000 */
#define R32_SYS0_AIP_TS_CTRL3                       (0x388 >> 2)
#define		SR_TS_CURVE_TRIM_SHIFT					(0)
#define		SR_TS_CURVE_TRIM_MASK					(BIT_MASK(7))
#define 	SR_TS_CURVE_TRIM            			(SR_TS_CURVE_TRIM_MASK << SR_TS_CURVE_TRIM_SHIFT)
#define		SR_TS_DEC_TRIM_SHIFT					(8)
#define		SR_TS_DEC_TRIM_MASK						(BIT_MASK(10))
#define 	SR_TS_DEC_TRIM            				(SR_TS_DEC_TRIM_MASK << SR_TS_DEC_TRIM_SHIFT)
#define 	SR_TS_TCODE_SIGN_TARGET1_BIT            (BIT24)
#define 	SR_TS_TCODE_SIGN_TARGET2_BIT            (BIT25)

/* Default: 0x00000000 */
#define R32_SYS0_AIP_TS_CTRL4                       (0x38C >> 2)
#define R16_SYS0_AIP_TS_TCODE_TARGET1               (0x38C >> 1)
#define		SR_TS_TCODE_TARGET1_SHIFT				(0)
#define		SR_TS_TCODE_TARGET1_MASK				(BIT_MASK(10))
#define 	SR_TS_TCODE_TARGET1            			(SR_TS_TCODE_TARGET1_MASK << SR_TS_TCODE_TARGET1_SHIFT)
#define R16_SYS0_AIP_TS_TCODE_TARGET2               (0x38E >> 1)
#define		SR_TS_TCODE_TARGET2_SHIFT				(0)
#define		SR_TS_TCODE_TARGET2_MASK				(BIT_MASK(10))
#define 	SR_TS_TCODE_TARGET2            			(SR_TS_TCODE_TARGET2_MASK << SR_TS_TCODE_TARGET2_SHIFT)

/* Default: 0x00000000 */
#define R32_SYS0_AIP_TS_INT_SET                     (0x390 >> 2)
#define R16_SYS0_AIP_TS_INT_SET                     (0x390 >> 1)
#define 	SR_TS_DET_EN_BIT                        (BIT0)
#define 	SR_TS_INT_EN_BIT                        (BIT1)
#define R8_SYS0_AIP_TS_DET_MASK                     (0x391 >> 0)
#define		SR_TS_DET_MASK_SHIFT					(0)
#define		SR_TS_DET_MASK_MASK						(BIT_MASK(6))
#define 	SR_TS_DET_MASK            				(SR_TS_DET_MASK_MASK << SR_TS_DET_MASK_SHIFT)
#define R16_SYS0_AIP_TS_INT_STS                     (0x392 >> 1)   // W1C
#define		SR_TS_EVENT_SHIFT						(0)
#define		SR_TS_EVENT_MASK						(BIT_MASK(6))
#define 	SR_TS_EVENT            					(SR_TS_EVENT_MASK << SR_TS_EVENT_SHIFT)

/*
 *  +-----------------------------------------------------------------------+
 *  |					HOST Control      									|
 *  +-----------------------------------------------------------------------+
 */
#define SYS0_HOST_CTRL_BASE                         (SYSTEM_PD0_REG_ADDRESS)

#define R8_SYS0_HOST_CTRL                           ((REG8  *) SYS0_HOST_CTRL_BASE)
#define R16_SYS0_HOST_CTRL                          ((REG16 *) SYS0_HOST_CTRL_BASE)
#define R32_SYS0_HOST_CTRL                          ((REG32 *) SYS0_HOST_CTRL_BASE)
#define R64_SYS0_HOST_CTRL                          ((REG64 *) SYS0_HOST_CTRL_BASE)

/* Default: 0x00030019 */
#define R32_SYS0_PCIE_CTRL_REG                      (0x400 >> 2)
#define R16_SYS0_PCIE_CTRL_REG                      (0x400 >> 1)
#define		SYS_CLOCK_FREQ_SHIFT					(0)
#define		SYS_CLOCK_FREQ_MASK						(BIT_MASK(9))
#define 	SYS_CLOCK_FREQ            				(SYS_CLOCK_FREQ_MASK << SYS_CLOCK_FREQ_SHIFT)
#define 	PCIE_RESETN_CLR_EN_BIT                  (BIT16)
#define 	PCIE_PHY_2RSTN_CLR_EN_BIT               (BIT17)
#define 	PCIE_RESETN_BIT                         (BIT30)
#define 	PCIE_PHY_2RSTN_BIT                      (BIT31)

/* Default: 0x00010100 */
#define R32_SYS0_PCIE_CFG_REG                       (0x404 >> 2)
#define 	TOPHY_RST_OPT_BIT                    	(BIT8)
#define 	PHY_RCOSC_EN_CFG_BIT                    (BIT16)
#define 	EN_PCIE_BUS_RST_BIT                     (BIT30)
#define 	EN_PHY_BUS_RST_BIT                      (BIT31)

/* Default: 0x000000001 */
#define R32_SYS0_PCIE_DGTH_CFG                      (0x408 >> 2)
#define R8_SYS0_PCIE_DGTH_CFG_CLKREQB               (0x408 >> 0)
#define		CLKREQB_DGTH_SEL_SHIFT					(0)
#define		CLKREQB_DGTH_SEL_MASK					(BIT_MASK(2))
#define 	CLKREQB_DGTH_SEL            			(CLKREQB_DGTH_SEL_MASK << CLKREQB_DGTH_SEL_SHIFT)
#define 	CLKREQB_DEGLITCH_0_2_US                 (0x0)
#define 	CLKREQB_DEGLITCH_0_4_US                 (0x1)
#define 	CLKREQB_DEGLITCH_0_7_US                 (0x2)
#define 	CLKREQB_DEGLITCH_1_0_US                 (0x3)
#define R8_SYS0_PCIE_DGTH_CFG_PERSTN                (0x409 >> 0)
#define		PERSTN_DGTH_SEL_SHIFT					(0)
#define		PERSTN_DGTH_SEL_MASK					(BIT_MASK(2))
#define 	PERSTN_DGTH_SEL            				(PERSTN_DGTH_SEL_MASK << PERSTN_DGTH_SEL_SHIFT)
#define 	PERSTN_DEGLITCH_0_2_US                  (0x0)
#define 	PERSTN_DEGLITCH_2_5_US                  (0x1)
#define 	PERSTN_DEGLITCH_40_0_US                 (0x2)
#define 	PERSTN_DEGLITCH_102_0_US                (0x3)

/* Default: 0x00000000 */
#define R32_SYS0_NVME_PD51_RST_ECO                  (0x420 >> 2)
#define 	SRST_NVM_PD51_N_BIT                     (BIT0)

/* Default: 0x00000000 */
#define R32_SYS0_SATA_SET2                          (0x428 >> 2)
#define 	HOST_MSK_REQ_BIT                        (BIT0)

/* Default: 0x00000000 */
#define R32_SYS0_HOST_LPM_REQ_STS                   (0x440 >> 2)
#define 	HOST_STATUS_SYNC_BIT                    (BIT0)    // RO
#define 	HOST_STATUS_RDY_SYNC_BIT                (BIT8)    // RO

/*
 *  +-----------------------------------------------------------------------+
 *  |					PD0 IP Control      								|
 *  +-----------------------------------------------------------------------+
 */
#define SYS0_IP_CTRL_BASE                           (SYSTEM_PD0_REG_ADDRESS)

#define R8_SYS0_IP_CTRL                             ((REG8  *) SYS0_IP_CTRL_BASE)
#define R16_SYS0_IP_CTRL                            ((REG16 *) SYS0_IP_CTRL_BASE)
#define R32_SYS0_IP_CTRL                            ((REG32 *) SYS0_IP_CTRL_BASE)
#define R64_SYS0_IP_CTRL                            ((REG64 *) SYS0_IP_CTRL_BASE)

/* Default: 0x00000000 */
#define R32_SYS0_DBUF0_CTRL                         (0x500 >> 2)
#define R8_SYS0_DBUF0_CTRL_PD0_SRAM_INIT_START      (0x500 >> 0)
#define		SR_DBUF_PD0_SRAM_INIT_START_SHIFT		(0)
#define		SR_DBUF_PD0_SRAM_INIT_START_MASK		(BIT_MASK(8))
#define 	SR_DBUF_PD0_SRAM_INIT_START            	(SR_DBUF_PD0_SRAM_INIT_START_MASK << SR_DBUF_PD0_SRAM_INIT_START_SHIFT) // 0x23000000 ~ 0x2307FFFF (Each 64KB)
#define 	SR_DBUF_LS_EN_BIT                 		(BIT16)

/* Default: 0x00000000 */
#define R32_SYS0_AES_IP_CLK_SEL                     (0x514 >> 2)
#define 	AES_CLK_SEL_BIT                         (BIT0)

/* Default: 0x00000400 */
#define R32_SYS0_SMBUS_PWR_SAVING                   (0x520 >> 2)
#define R8_SYS0_SMBUS_CHK_ADDR_MASK                 (0x520 >> 0)
#define		SR_SMBUS_CHK_ADDR_SHIFT					(0)
#define		SR_SMBUS_CHK_ADDR_MASK					(BIT_MASK(7))
#define 	SR_SMBUS_CHK_ADDR            			(SR_SMBUS_CHK_ADDR_MASK << SR_SMBUS_CHK_ADDR_SHIFT)
#define R8_SYS0_SMBUS_SDA_HOLD                   	(0x521 >> 0)
#define		SR_SMBUS_SDA_HOLD_SHIFT					(0)
#define		SR_SMBUS_SDA_HOLD_MASK					(BIT_MASK(3))
#define 	SR_SMBUS_SDA_HOLD            			(SR_SMBUS_SDA_HOLD_MASK << SR_SMBUS_SDA_HOLD_SHIFT)
#define 	PD0_RDWR_N_SYNC_BIT                     (BIT24) // RO

/*
 *  +-----------------------------------------------------------------------+
 *  |					PHY Control      									|
 *  +-----------------------------------------------------------------------+
 */
#define SYS0_PHY_CTRL_BASE                          (SYSTEM_PD0_REG_ADDRESS)

#define R8_SYS0_PHY_CTRL                            ((REG8  *) SYS0_PHY_CTRL_BASE)
#define R16_SYS0_PHY_CTRL                           ((REG16 *) SYS0_PHY_CTRL_BASE)
#define R32_SYS0_PHY_CTRL                           ((REG32 *) SYS0_PHY_CTRL_BASE)
#define R64_SYS0_PHY_CTRL                           ((REG64 *) SYS0_PHY_CTRL_BASE)

/* Default: 0x00000000 */
#define R32_SYS0_COMBO_PHY_CFG1                     (0xC10 >> 2)
#define		COMBO_PHY_SEL_MODE_SHIFT				(0)
#define		COMBO_PHY_SEL_MODE_MASK					(BIT_MASK(3))
#define 	COMBO_PHY_SEL_MODE            			(COMBO_PHY_SEL_MODE_MASK << COMBO_PHY_SEL_MODE_SHIFT)

/* Default: 0x00000101 */
#define R32_SYS0_FLASH_PHY_CLK_SEL                  (0xC20 >> 2)
#define R8_SYS0_FLASH_PHY_RCLK_SEL                  (0xC21 >> 0)
#define		FPHY_CLK_SEL_SHIFT						(0)
#define		FPHY_CLK_SEL_MASK						(BIT_MASK(5))
#define 	FPHY_CLK_SEL            				(FPHY_CLK_SEL_MASK << FPHY_CLK_SEL_SHIFT)

/* Default: 0x00000000 */
#define R32_SYS0_PCIE_PHY_TRIM1                     (0xC30 >> 2) // 0~31

/* Default: 0x00000000 */
#define R32_SYS0_PCIE_PHY_TRIM2                     (0xC34 >> 2) // 32~63

/* Default: 0x00000000 */
#define R32_SYS0_PCIE_PHY_TRIM3                     (0xC38 >> 2) // 64~95

/* Default: 0x00000000 */
#define R32_SYS0_PCIE_PHY_TRIM4                     (0xC3C >> 2) // 96~127

/* Default: 0x00000000 */
#define R32_SYS0_PCIE_PHY_BIST0                     (0xC40 >> 2) // RO
#define R8_SYS0_PCIE_PHY_BIST0_VALID_SYNC           (0xC40 >> 0)
#define		PHY_BIST_VALID_SYNC_SHIFT				(0)
#define		PHY_BIST_VALID_SYNC_MASK				(BIT_MASK(4))
#define 	PHY_BIST_VALID_SYNC            			(PHY_BIST_VALID_SYNC_MASK << PHY_BIST_VALID_SYNC_SHIFT)
#define R8_SYS0_PCIE_PHY_BIST0_OK_SYNC              (0xC41 >> 0)
#define		PHY_BIST_OK_SYNC_SHIFT					(0)
#define		PHY_BIST_OK_SYNC_MASK					(BIT_MASK(4))
#define 	PHY_BIST_OK_SYNC            			(PHY_BIST_OK_SYNC_MASK << PHY_BIST_OK_SYNC_SHIFT)
#define R8_SYS0_PCIE_PHY_BIST0_FAIL_SYNC            (0xC42 >> 0)
#define		PHY_BIST_FAIL_SYNC_SHIFT				(0)
#define		PHY_BIST_FAIL_SYNC_MASK					(BIT_MASK(4))
#define 	PHY_BIST_FAIL_SYNC            			(PHY_BIST_FAIL_SYNC_MASK << PHY_BIST_FAIL_SYNC_SHIFT)

/* Default: 0x00000000 */
#define R32_SYS0_PCIE_PHY_BIST1                     (0xC44 >> 2)
#define 	PHY_BIST_EN0_BIT                        (BIT0)
#define 	PHY_BIST_EN1_BIT                        (BIT8)
#define 	PHY_BIST_EN2_BIT                        (BIT16)
#define 	PHY_BIST_EN3_BIT                        (BIT24)

/* Default: 0x01010101 */
#define R32_SYS0_PCIE_PHY_BIST2                     (0xC48 >> 2)
#define 	PHY_BIST_PD0_BIT                        (BIT0)
#define 	PHY_BIST_PD1_BIT                        (BIT8)
#define 	PHY_BIST_PD2_BIT                        (BIT16)
#define 	PHY_BIST_PD3_BIT                        (BIT24)

/* Default: 0x00000000 */
#define R32_SYS0_PCIE_PHY_BIST3                     (0xC4C >> 2)
#define R8_SYS0_PCIE_PHY_PHY_BIST_SEL               (0xC4C >> 0)
#define		CR_PHY_BIST_SEL_SHIFT					(0)
#define		CR_PHY_BIST_SEL_MASK					(BIT_MASK(7))
#define 	CR_PHY_BIST_SEL            				(CR_PHY_BIST_SEL_MASK << CR_PHY_BIST_SEL_SHIFT)
#define 	CR_PHY_BIST_REG_LBK_FARD0_BIT           (BIT8)
#define 	CR_PHY_BIST_REG_LBK_FARA0_BIT           (BIT9)
#define 	CR_PHY_BIST_REG_LBK_NEARA0_BIT          (BIT10)
#define 	CR_PHY_BIST_RSTN0_BIT                   (BIT16)

/*
 *  +-----------------------------------------------------------------------+
 *  |					FLASH PHY OSC Control      							|
 *  +-----------------------------------------------------------------------+
 */


/*
 *  +-----------------------------------------------------------------------+
 *  |					EFUSE Control      									|
 *  +-----------------------------------------------------------------------+
 */
#define	SYS1_EFUC_BASE								(EFUSE_REG_ADDRESS)

#define	R8_SYS1_EFUC								((REG8  *) SYS1_EFUC_BASE)
#define	R16_SYS1_EFUC								((REG16 *) SYS1_EFUC_BASE)
#define	R32_SYS1_EFUC								((REG32 *) SYS1_EFUC_BASE)

#define	R32_SYS1_EFU_MODE_SEL						(0x00 >> 2)
#define	R8_SYS1_EFU_MODE_SEL						(0x00)
#define		MODE_SEL_SHIFT							(0)
#define		MODE_SEL_MASK							(BIT_MASK(3))
#define 	PROGRAM_MODE							(0x2)
#define		READ_MODE								(0x3)
#define		BANK_SEL_SHIFT							(4)
#define		BANK_SEL_MASK							(BIT_MASK(4))
#define		MR_BIT									(BIT8)
#define		NO_DOUBLE_BIT							(BIT12)

#define	R32_SYS1_EFU_RDY							(0x04 >> 2)
#define		READ_READY_BIT							(BIT0)
#define		PROGRAM_READY_BIT						(BIT8)
#define		STANDBY_BIT								(BIT9)
#define		POWER_DOWN_BIT							(BIT10)

#define	R32_SYS1_EFU_ADDR							(0x08 >> 2)
#define		EFUSE_ADR_SHIFT							(0)
#define		EFUSE_ADR_MASK							(BIT_MASK(7))

#define	R32_SYS1_EFU_PROG_DATA						(0x0C >> 2)
#define	R32_PROGRAM_WORD							(0x0C >> 2)

#define	R32_SYS1_EFU_DATA_TRIGGER					(0x10 >> 2)
#define		TRIGGER_BIT								(BIT0)

#define	R32_SYS1_EFU_STAT_PROTECT					(0x14 >> 2)
#define		EFUSE_PROGRAM_PROTECT_BIT				(BIT8)
#define		STATE_SHIFT								(24)
#define		STATE_MASK								(BIT_MASK(4))

#define	R32_SYS1_EFU_STRB_CTRL						(0x18 >> 2)
#define		PRGM_STRB_WIDTH_SHIFT					(0)
#define		PRGM_STRB_WIDTH_MASK					(BIT_MASK(10))
#define		READ_STRB_WIDTH_SHIFT					(12)
#define		READ_STRB_WIDTH_MASK					(BIT_MASK(3))
#define		PRGM_STRB_INTVL_SHIFT					(16)
#define		PRGM_STRB_INTVL_MASK					(BIT_MASK(8))
#define		READ_STRB_INTVL_SHIFT					(24)
#define		READ_STRB_INTVL_MASK					(BIT_MASK(2))

#define	R32_SYS1_EFU_SW_CTRL						(0x1C >> 2)
#define		EFUC_A_SHIFT							(0)
#define		EFUC_A_MASK								(BIT_MASK(12))
#define		EFUC_RWL_BIT							(BIT14)
#define		EFUC_RSB_BIT							(BIT15)
#define		EFUC_ENPOR_BIT							(BIT16)
#define		EFUC_PD_BIT								(BIT17)
#define		EFUC_PS_BIT								(BIT18)
#define		EFUC_STROBE_BIT							(BIT19)
#define		EFUC_PGENB_BIT							(BIT20)
#define		EFUC_LOAD_BIT							(BIT21)
#define		EFUC_CSB_BIT							(BIT22)
#define		EFUC_MR_BIT								(BIT23)
#define		EFUC_RF_SHIFT							(24)
#define		EFUC_RF_MASK							(BIT_MASK(8))

#define	R32_SYS1_EFU_SW_DATA						(0x20 >> 2)

#define	R32_SYS1_EFU_STS							(0x24 >> 2)
#define		EFU_STS_BIT								(BIT0)

#define	R8_SYS1_EFU_REG0							(0x40 >> 0)
#define	R32_SYS1_EFU_REG0							(0x40 >> 2)
#define	R32_READ_REG0								(0x40 >> 2)

#define	R32_SYS1_EFU_REG1							(0x44 >> 2)
#define	R32_READ_REG1								(0x44 >> 2)

#define	R32_SYS1_EFU_REG2							(0x48 >> 2)
#define	R32_READ_REG2								(0x48 >> 2)

#define	R8_SYS1_EFU_REG3							(0x4C >> 0)
#define	R32_SYS1_EFU_REG3							(0x4C >> 2)
#define	R32_READ_REG3								(0x4C >> 2)

#define	R32_SYS1_EFU_REG4							(0x50 >> 2)
#define	R32_READ_REG4								(0x50 >> 2)

#define	R32_SYS1_EFU_REG5							(0x54 >> 2)
#define	R32_READ_REG5								(0x54 >> 2)

#define	R32_SYS1_EFU_REG6							(0x58 >> 2)
#define	R32_READ_REG6								(0x58 >> 2)

#define	R8_SYS1_EFU_REG7							(0x5C >> 0)
#define	R32_SYS1_EFU_REG7							(0x5C >> 2)
#define	R32_READ_REG7								(0x5C >> 2)

/*
 *  +-----------------------------------------------------------------------+
 *  |					PD0 MISC Control      								|
 *  +-----------------------------------------------------------------------+
 */
#define SYS0_MISC_CTRL_BASE                         (SYSTEM_PD0_REG_ADDRESS)

#define R8_SYS0_MISC_CTRL                           ((REG8  *) SYS0_MISC_CTRL_BASE)
#define R16_SYS0_MISC_CTRL                          ((REG16 *) SYS0_MISC_CTRL_BASE)
#define R32_SYS0_MISC_CTRL                          ((REG32 *) SYS0_MISC_CTRL_BASE)
#define R64_SYS0_MISC_CTRL                          ((REG64 *) SYS0_MISC_CTRL_BASE)

/*
   Note:
   The Value in this field depends on efuse content.
   When FW access:
   BIT0~8, and BIT10 can be overwritten to 1, but cannot be overwritten to 0
   BIT9 can be overwritten to any value.
*/

/* Default: 0x00000000 */
#define R32_SYS0_SPI_CFG                      		(0xE00 >> 2)
#define		SPI_CDIV_REG_SHIFT						(0)
#define		SPI_CDIV_REG_MASK						(BIT_MASK(2))
#define 	SPI_CDIV_REG            				(SPI_CDIV_REG_MASK << SPI_CDIV_REG_SHIFT)

/* Default: 0x00000000 */
#define R32_SYS0_HW_DISABLE                         (0xE04 >> 2)
#define 	SR_JTAG_FUNC_DIS_BIT                    (BIT0)   // disable on JTAG ports
#define 	SR_UART_FUNC_DIS_BIT                    (BIT1)   // disable on GPIO[1:0]
#define 	SR_I2CS_FUNC_DIS_BIT                    (BIT2)   // disable on JTAG ports
#define 	SR_PIC_DBG_DIS_BIT                      (BIT3)
#define 	SR_AES_KEY_DIS_BIT                      (BIT4)
#define 	SR_AES_OTF_DIS_BIT                      (BIT5)
#define 	SR_AES_OFL_DIS_BIT                      (BIT6)
#define 	SR_SHA_FUNC_DIS_BIT                     (BIT7)
#define 	SR_PKE_FUNC_DIS_BIT                     (BIT8)
#define 	SR_SPI_SCR_DIS_BIT                      (BIT9)
#define 	SR_AES_OTF_DBUF_DIS_BIT                 (BIT10)

/* Default: 0x00000000 */
#define R32_SYS0_HW_SIM_INFO1_PD0                   (0xE10 >> 2)

/* Default: 0x00000000 */
#define R32_SYS0_HW_SIM_INFO2_PD0                   (0xE14 >> 2)

/* Default: 0x00000000 */
#define R32_SYS0_FW_SIM_INFO1_PD0                   (0xE18 >> 2)
#define R8_SYS0_FW_SIM_INFO1_PD0					(0xE18 >> 0)

/* Default: 0x00000000 */
#define R32_SYS0_FW_SIM_INFO2_PD0                   (0xE1C >> 2)

/* Default: 0x00000000 */
#define R32_SYS0_HW_DBG_CTRL_PD0                    (0xE20 >> 2)
#define R8_SYS0_HW_DBG_IP_SEL                       (0xE20 >> 0)
#define		DBG_IP_SEL_SHIFT						(0)
#define		DBG_IP_SEL_MASK							(BIT_MASK(6))
#define 	DBG_IP_SEL            					(DBG_IP_SEL_MASK << DBG_IP_SEL_SHIFT)
#define R8_SYS0_HW_DBG_PORT_SEL                     (0xE21 >> 0)
#define		DBG_PORT_SEL_SHIFT						(0)
#define		DBG_PORT_SEL_MASK						(BIT_MASK(8))
#define 	DBG_PORT_SEL            				(DBG_PORT_SEL_MASK << DBG_PORT_SEL_SHIFT)
#define R8_SYS0_HW_DBG_BSEL                         (0xE22 >> 0)
#define		DBG_BSEL_SHIFT							(0)
#define		DBG_BSEL_MASK							(BIT_MASK(3))
#define 	DBG_BSEL            					(DBG_BSEL_MASK << DBG_BSEL_SHIFT)
#define 	SR_DBG_OUT_PD0_EN_BIT                   (BIT24)

/* Default: 0x00000000 */
#define R32_SYS0_HW_DBG_STS1_PD0                    (0xE24 >> 2) // RO

/* Default: 0x00000000 */
#define R32_SYS0_HW_DBG_STS2_PD0                    (0xE28 >> 2) // RO
#define     DBG_IP_OUT_PD0_SYNC_SHIFT				(0)
#define     DBG_IP_OUT_PD0_SYNC_MASK				(BIT_MASK(16))
#define     DBG_IP_OUT_PD0_SYNC						(DBG_IP_OUT_PD0_SYNC_MASK << DBG_IP_OUT_PD0_SYNC_SHIFT)

/* Default: 0x00000000 */
#define R32_SYS0_CHIP_ID_CFG1                       (0xE30 >> 2)

/* Default: 0x00000000 */
#define R32_SYS0_CHIP_ID_CFG2                       (0xE34 >> 2)

/* Default: 0x00000000 */
#define R32_SYS0_FW_SIM_INFO3_PD0                   (0xE40 >> 2)

/* Default: 0x00000000 */
#define R32_SYS0_FW_SIM_INFO4_PD0                   (0xE44 >> 2)

/* Default: 0x00000000 */
#define R32_SYS0_HW_SIM_INFO3_PD0                   (0xE48 >> 2)

/* Default: 0x00000000 */
#define R32_SYS0_HW_SIM_INFO4_PD0                   (0xE4C >> 2)

/* Default: 0x00000000 */
#define R32_SYS0_SPI_EFUSE_SELECTION                (0xE80 >> 2)
#define		SR_SPI_SELECTION_BIT					(BIT0)

#endif  /* _SYS_PD0_REG_5021_H_ */
