/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  err_hdl_fpl_SoftbitRaid.h                                          */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#ifndef SRC_FW_RETRY_ERR_HDL_FPL_SOFTBITRAID_H_
#define SRC_FW_RETRY_ERR_HDL_FPL_SOFTBITRAID_H_

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "hal/fip/fip_api.h"
#include "common/fw_common.h"
#include "table/vbmap/vbmap_api.h"
#include "aom/aom_api.h"
/*
 * ---------------------------------------------------------------------------------------------------
 *   definitions
 * ---------------------------------------------------------------------------------------------------
 */
#define SBRAID_IDLE_MODE    (0)
#define SBRAID_RAIDECC_AND_SB_MODE     (1)
#define SBRAID_ONLY_RAIDECC_MODE     (2)
#define SBRAID_NORMAL_FAIL_ALL_DONE	(3)
#define SBRAID_TARGET_CANNOT_RECOVER		(4)
#define SBRAID_TARGET_DONE				(5)

#define SOFTBITRAID_SB_CORR_NULL    (0)
#define SOFTBITRAID_SB_CORR_PASS    (1)
#define SOFTBITRAID_SB_CORR_FAIL    (2)

#define SOFTBITRAID_RETRY_TO_RETRYBUF_MODE  (0)
#define SOFTBITRAID_RETRY_FROM_RETRYBUF_MODE  (1)

#define SOFTBITRAID_BUF_START_OFFSET_BY_PAGE     (2)   /// buf0 for parity, buf 1 for err page, backup normal page starts from buf2

#define CORR_FAIL_FRAME_MODE    (0)
#define CORR_ALL_FRAME_MODE     (1)
#define BACKUP_RAIDECC_RESULT_TO_BUF0_MODE     (2)
#define BACKUP_RAIDECC_RESULT_TO_BUF1_MODE     (3)

#define RETRY_SBRAID_MAX_ITERATION_CNT   (3)

#define SOFTBITRAID_ORIGIN_ERRPAGE_NON_INIT_MARK      (0xFF)
#define SOFTBITRAID_MAX_BITMAP_LENGTH_IN_BYTE     (16)

#define SBRAID_XORALL_BUF_IDX		(0)
#define SBRAID_FAIL_PAGE_BUF_IDX		(1)

#define SBRAID_NCS_COMPARE_SBRAID_XORALL	(0)
#define SBRAID_NCS_COMPARE_SBRAID_SB6		(1)

/*
 * ---------------------------------------------------------------------------------------------------
 *  enum
 * ---------------------------------------------------------------------------------------------------
 */
enum {
	SOFTBITRAID_IDLE = 0,									// 0
	SOFTBITRAID_START,							// 1

	SOFTBITRAID_CHECK_ITERATION,						// 2
	SOFTBITRAID_CHECK_NEXT_ERRPAGE,						// 3

	SOFTBITRAID_BUSY_DOING_SB,                                                                    // 4
	SOFTBITRAID_BUSY_DOING_RAID,                                                                    // 5
	SOFTBITRAID_BUSY_DOING_SBRAID,							// 6
	SOFTBITRAID_BACK_TO_CHECK_LOOP,                                             // 7
};

/*
 * ---------------------------------------------------------------------------------------------------
 *  data types
 * ---------------------------------------------------------------------------------------------------
 */
typedef struct {

	U8 ubState;
	U8 ubIterationCnt;
	U8 ubDealingWithErrPageFlag;
	U8 ubAllocated4KBufCnt;

	U8 ubStopSBRAIDFlag;
	U8 ubRSDecodeFlowLoopBreakFlag;
	U8 ubErrPageTotalCnt;            ///This variable only cnt for normal page, parity & err page are not included.
	U8 ubErrPageTotalCntMax;       ///This variable only cnt for normal page, parity & err page are not included.
	U8 ubErrPageTotalCntIdx;

	U8 ubSBCORRResult;
	U8 ubErrPageOfAllPageIdx;
	U8 ubOriginErrPageOfAllPageIdx;
	U8 ubBackupOptimalShiftValue[5][8][3];          //5: Max ErrPage cnt.  8:Max Frame in a Page  3: Max Vth type cnt in single Page type
	U8 ubErrPageBMP[SOFTBITRAID_MAX_BITMAP_LENGTH_IN_BYTE];
	U8 ubErrPageCorrectedBMP[SOFTBITRAID_MAX_BITMAP_LENGTH_IN_BYTE];

	//RETRY_SBRAID_CONTINUE_PROCESSING_OTHER_FAIL_PAGE
	U8 ubTargetPageDone;

	U32 ulBackupOriginErrVCA;
	U32 ulBackupTargetErrVCA;
	U32 ulBackupTargetErrPCA;
	U32 ulDataBaseAddr;
	U32 ulSpareBaseAddr;

	//Debug
	U32 ulXORResultErrCnt;

} SOFTBITRAID_TASK_t;

/*
 * ---------------------------------------------------------------------------------------------------
 *   macros
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *   extern global variables
 * ---------------------------------------------------------------------------------------------------
 */
extern U32 gulSBRAIDAddr;
extern SOFTBITRAID_TASK_t gSBRaidTask;

/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */
AOM_RETRY_SBRAID void RetrySBRAIDInit(void);
AOM_RETRY_SBRAID void RetrySBRAIDAddErrPage(U8 ubErrPageIdx, U8 ubMode, U8 ubBuffAllocate);
AOM_RETRY_SBRAID void RetrySBRAIDRemoveErrPage(U8 ubPageIdx);
AOM_RETRY_SBRAID void RetrySBRAIDDMACCopy(U8 ubBufIdx,  U8 ubMode, U8 ubIsFullBitmapOrNot);
AOM_RETRY_SBRAID void RetrySBRAIDMain(void);

#endif /* SRC_FW_RETRY_ERR_HDL_FPL_SOFTBITRAID_H_ */
