#include "setup.h"
#include "typedef.h"
#if VS_SIM_EN
#include "sys_res.h"
#include "ip/cop0/hw_cop0.h"
#endif /* VS_SIM_EN */
#include "hal/cop0/cop0_tieout.h"
#include "debug/debug.h"

#if VS_SIM_EN
/* tie out */
U8 COP0_TieoutNonblock(void *pData, U8 ubCPUIndex)
{
	if (ubCPUIndex == 0) {
		if (M_DB_CHECK_EMPTY(DB_COP0_CQ)) {
			return FALSE;
		}
		else {
#if USE_FW_DB
			memcpy(pData, M_DB_GET_QBODY_PTR( DB_COP0_CQ, gDBQueueCnt.QueueCnt[(DB_COP0_CQ)] ), M_DB_GET_DATA_SIZE(DB_COP0_CQ));
#else
			memcpy(pData, (void *)M_DB_GET_RPTR(DB_COP0_CQ), M_DB_GET_DATA_SIZE(DB_COP0_CQ));
#endif
			M_DB_TRIGGER_READ_CNT(DB_COP0_CQ, 1);
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_COP0_CQ, gDBQueueCnt.QueueCnt[(DB_COP0_CQ)] ) == M_DB_GET_RPTR((DB_COP0_CQ)));
			return TRUE;
		}
	}
	else {
		return hw_que_pop(&gSysResStruct.pool_que[SYS_QUE_ID_COP0_2_COP1], pData);
	}
}

void COP0_TieoutBlock(void *pData, U8 ubCPUIndex)
{
	if (ubCPUIndex == 0) {
		while (M_DB_CHECK_EMPTY(DB_COP0_CQ));
#if USE_FW_DB
		memcpy(pData, M_DB_GET_QBODY_PTR( DB_COP0_CQ, gDBQueueCnt.QueueCnt[(DB_COP0_CQ)] ), M_DB_GET_DATA_SIZE(DB_COP0_CQ));
#else
		memcpy(pData, (void *)M_DB_GET_RPTR(DB_COP0_CQ), M_DB_GET_DATA_SIZE(DB_COP0_CQ));
#endif
		M_DB_TRIGGER_READ_CNT(DB_COP0_CQ, 1);
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_COP0_CQ, gDBQueueCnt.QueueCnt[(DB_COP0_CQ)] ) == M_DB_GET_RPTR((DB_COP0_CQ)));
	}
	else {
		while (hw_que_pop(&gSysResStruct.pool_que[SYS_QUE_ID_COP0_2_COP1], pData) == FALSE) {
			hw_que_wait_not_empty(&gSysResStruct.pool_que[SYS_QUE_ID_COP0_2_COP1]);
		}
	}
}

#if TIE_OUT_EN
U16 COP0_CPU_Rec_TieOut(void)
{
	TIEOUT_FORMAT_t tieout_data;

	M_COP0_TIEOUT_BLOCK(&tieout_data, 0);

	return (U16)tieout_data.HL.B32_to_B63.Info.uwTAG;
}

U16 COP0_COP1_Rec_TieOut(void)
{
	TIEOUT_FORMAT_t tieout_data;

	M_COP0_TIEOUT_BLOCK(&tieout_data, 1);

	return (U16)tieout_data.HL.B32_to_B63.Info.uwTAG;
}
#endif /* TIE_OUT_EN */
#else /* VS_SIM_EN */
U16 COP0_CPU_Rec_TieOut(void)
{
	M_FW_TODO();
	return 0;
}

U16 COP0_COP1_Rec_TieOut(void)
{
	M_FW_TODO();
	return 0;
}

U8 COP0_TieoutNonblock(void *pData, U8 ubCPUIndex)
{
	M_FW_TODO();
	return 0;
}

void COP0_TieoutBlock(void *pData, U8 ubCPUIndex)
{
	M_FW_TODO();
}
#endif /* VS_SIM_EN */
