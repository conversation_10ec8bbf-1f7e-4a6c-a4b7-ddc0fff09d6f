/**************************************************************************/
/*                                                                        */
/*           Copyright (c) 2000-2017 by Phison Electronics Corp.          */
/*                                                                        */
/**************************************************************************/

/**************************************************************************/
/*                                                                        */
/*  HAL UART DEFINITION                                    RELEASE        */
/*                                                                        */
/*    shr_hal_nvme_reg.h                                    GNU C         */
/*                                                           X.X          */
/*  AUTHOR                                                                */
/*                                                                        */
/*                                                                        */
/*  DESCRIPTION                                                           */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/*  RELEASE HISTORY                                                       */
/*                                                                        */
/*    DATE              NAME                      DESCRIPTION             */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#ifndef _SHR_HAL_E21_NVME_REG_H_
#define _SHR_HAL_E21_NVME_REG_H_
//This file is based on shr_hal_A13_nvme_reg.h
#include "common/typedef.h"
#include "hal/nvme/nvme_reg_5021.h"

#define NVME_LAYER_OFFSET                      (0x1000)
#define NVME_REGISTER_ADDRESS                  (NVME_REG_ADDRESS + NVME_LAYER_OFFSET)
#define NVME_RAM_ADDRESS                        (NVME_NL_RAM_ADDRESS)
#define B_OFFSET_01H    (0x01 >> 0)
#define DW_OFFSET_04H   (0x04 >> 2)
#define DW_OFFSET_10H   (0x10 >> 2)
#define DW_OFFSET_20H   (0x20 >> 2)

#define DW_OFFSET_40H   (0x40 >> 2)
#define QW_OFFSET_10H   (0x10 >> 3)
#define QW_OFFSET_20H   (0x20 >> 3)
#define QW_OFFSET_40H   (0x40 >> 3)


#define R8_NVME_NL_REG                             ((REG8 *)NVME_REGISTER_ADDRESS) //0xF8003000
#define R16_NVME_NL_REG                            ((REG16 *)NVME_REGISTER_ADDRESS)
#define R32_NVME_NL_REG                            ((REG32 *)NVME_REGISTER_ADDRESS)
#define R64_NVME_NL_REG                            ((REG64 *)NVME_REGISTER_ADDRESS)

#define R8_NVME_NL_RAM                         ((REG8 *)NVME_RAM_ADDRESS) // 0x40214000
#define R16_NVME_NL_RAM                        ((REG16 *)NVME_RAM_ADDRESS)
#define R32_NVME_NL_RAM                        ((REG32 *)NVME_RAM_ADDRESS)
#define R64_NVME_NL_RAM                        ((REG64 *)NVME_RAM_ADDRESS)

#define NL_MAX_QINFO_BLOCK_NUM				(9) // 1:Adm, 8:I/O
#define	NVME_NL_MSIX_TABLE_SIZE					(0x10)

//----------------- start : NVMe setting $$$ A13 ADDED $$$ -----------------
//#define DEVICE_TOTAL_SIZE       (DBUF_RAMDISK_SIZE / BC_512B) // default sram mode : 1MB, unit is 512B

// HW limitation
#define MAX_NUM_CTAG            (512)
#define MAX_NUM_FUN             (17) // also means the number of controller
#define MAX_NUM_NS              (32)  // max num of namespace is 32
#define MAX_NUM_ASYNC_CMD       (4)
#define MAX_NUM_PWR_STATE       (5)
#define MAX_QUEUE_SIZE          (0xFFFF) // 0's based value
#define MAX_IO_SQ_ENTRY_SIZE    (0x6) // i.e 2^6 = 64 byte submission entry size
#define MAX_IO_CQ_ENTRY_SIZE    (0x4) // i.e 2^4 = 16 byte completion entry size
#define MAX_DATA_TRANSFER_SIZE  (9) // MDTS
#define MAX_THERMAL_THRESHOLD   (0x60)
#define NL_INT_EN_ALL           (0xFFFFFFFF)
/*
#define MAX_NUM_QUEUE           (132) // ADM queue included
#define PF_NUM_QUEUE            (9)
#define VF0_NUM_QUEUE           (9)
#define VF1_NUM_QUEUE           (9)
#define VF2_NUM_QUEUE           (9)
#define VF3_NUM_QUEUE           (9)
#define VF4_NUM_QUEUE           (9)
#define VF5_NUM_QUEUE           (8)
#define VF6_NUM_QUEUE           (7)
#define VF7_NUM_QUEUE           (7)
#define VF8_NUM_QUEUE           (7)
#define VF9_NUM_QUEUE           (7)
#define VF10_NUM_QUEUE          (7)
#define VF11_NUM_QUEUE          (7)
#define VF12_NUM_QUEUE          (7)
#define VF13_NUM_QUEUE          (7)
#define VF14_NUM_QUEUE          (7)
#define VF15_NUM_QUEUE          (7)
#define TOTAL_NUM_QUEUE         (PF_NUM_QUEUE + VF0_NUM_QUEUE + VF1_NUM_QUEUE + VF2_NUM_QUEUE + VF3_NUM_QUEUE + VF4_NUM_QUEUE + VF5_NUM_QUEUE + VF6_NUM_QUEUE + VF7_NUM_QUEUE + VF8_NUM_QUEUE + VF9_NUM_QUEUE + VF10_NUM_QUEUE + VF11_NUM_QUEUE + VF12_NUM_QUEUE + VF13_NUM_QUEUE + VF14_NUM_QUEUE + VF15_NUM_QUEUE)

#if (TOTAL_NUM_QUEUE > MAX_NUM_QUEUE)
#error "Too much num of queue"
#endif
*/
#define CAP_IO_QUEUE_PC         (1)
#define CAP_SUP_AMS             (1)
#define CAP_TIME_OUT            (120)
#define CAP_SUP_NSSRS           (1)
#define CAP_SUP_NVM_CMD         (1)
#define CAP_SUP_BPS             (1)
#define CAP_MPS_MIN             (0)
#define CAP_MPS_MAX             (4)

#define CC_AMS_WRR_URGENT       (1)
//----------------- end : NVMe setting $$$ A13 ADDED $$$ end-----------------



/*======================== NVME Layer Status ================================*/
#define NL_CHK_NSSR_STS()                   (R32_NVME_NL_REG[R32_NVME_NL_STS] & INT_NSSR)
#define NL_CLR_NSSR_STS()                   (R32_NVME_NL_REG[R32_NVME_NL_STS] = INT_NSSR)
#define NL_CHK_DB_WP_STS()                  (R32_NVME_NL_REG[R32_NVME_NL_STS] & INT_DB_WP)
#define NL_CLR_DB_WP_STS()                  (R32_NVME_NL_REG[R32_NVME_NL_STS] = INT_DB_WP)
#define NL_GET_CE_STS()                     (R32_NVME_NL_REG[R32_NVME_NL_STS])
#define NL_CLR_CE_STS(N)					(R32_NVME_NL_REG[R32_NVME_NL_STS] = N)

#define NL_STS_ALL                          (0x1F)

#define NL_CLR_STS(N)						(R32_NVME_NL_REG[R32_NVME_NL_STS] = N)

#define NL_CHK_CE_CHG_STS()                 ((R32_NVME_NL_REG[R32_NVME_NL_CE_CHG] & NL_INT_CE_CHG) >> 0)
#define NL_CLR_CE_CHG_STS(x)                 (R32_NVME_NL_REG[R32_NVME_NL_CE_CHG] = (((U32)x << 0) & NL_INT_CE_CHG))

#define NL_CHK_SHN_CHG_STS()                ((R32_NVME_NL_REG[R32_NVME_NL_SHN_CHG] & NL_INT_SHN_CHG) >> 0)
#define NL_CLR_SHN_CHG_STS(x)                (R32_NVME_NL_REG[R32_NVME_NL_SHN_CHG] = (((U32)x << 0) & NL_INT_SHN_CHG))

#define NL_CHK_BPR_STS()                    ((R32_NVME_NL_REG[R32_NVME_NL_BP_RD] & NL_INT_BPR) >> 0)
#define NL_CLR_BPR_STS(x)                    (R32_NVME_NL_REG[R32_NVME_NL_BP_RD] = (((U32)x << 0) & NL_INT_BPR))

#define INT_W_ABN_ADDR                  	(NL_INT_W_ABN_ADDR_BIT)
#define INT_HST_W_DERR                      (NL_INT_HST_W_DERR_BIT)
#define INT_CMD_R_PERR                      (NL_INT_CMD_R_DERR_BIT)
#define INT_CMD_R_RERR                      (NL_INT_CMD_R_RERR_BIT)
#define INT_CPL_WERR                        (NL_INT_CPL_RERR_BIT)
#define INT_MSIX_WERR                       (NL_INT_MSIX_RERR_BIT) //HW change the name from E13
#define SRAM_PAR_ERR                        (NL_INT_SRAM_PAR_ERR_BIT)
#define INVALID_QID							(NL_INT_INVLD_QID_BIT)
#define	SQDB_INVALID_REG					(NL_INT_SQDB_INVLD_REG_FLAG_BIT)
#define	SQDB_INVALID_VAL					(NL_INT_SQDB_INVLD_VAL_FLAG_BIT)
#define	CQDB_INVALID_REG					(NL_INT_CQDB_INVLD_REG_FLAG_BIT)
#define CQDB_INVALID_VAL					(NL_INT_CQDB_INVLD_VAL_FLAG_BIT)
#define	INVALID_QOFST						(NL_INT_INVLD_QOFST_BIT)
#define GET_NL_AXI_ESTS()                   (R32_NVME_NL_REG[R32_NVME_NL_AXI_ESTS] >> 0)
#define CLR_NL_AXI_ESTS(x)                  (R32_NVME_NL_REG[R32_NVME_NL_AXI_ESTS]=(x))

#define HAL_NVME_GET_INVLD_SQDB_REG(x)      (R32_NVME_NL_REG[R32_NVME_NL_INVLD_SQDB_REG + ((U32)x * DW_OFFSET_04H)])
#define HAL_NVME_CLR_INVLD_SQDB_REG(x,y)    (R32_NVME_NL_REG[R32_NVME_NL_INVLD_SQDB_REG + ((U32)x * DW_OFFSET_04H)] = (U32)y)

#define HAL_NVME_GET_INVLD_SQDB_VAL(x)      (R32_NVME_NL_REG[R32_NVME_NL_INVLD_SQDB_VAL + ((U32)x * DW_OFFSET_04H)])
#define HAL_NVME_CLR_INVLD_SQDB_VAL(x,y)    (R32_NVME_NL_REG[R32_NVME_NL_INVLD_SQDB_VAL + ((U32)x * DW_OFFSET_04H)] = (U32)y)

// F800_3060h E19 Add
#define HAL_NVME_GET_INVLD_CQDB_REG(x)      (R32_NVME_NL_REG[R32_NVME_NL_INVLD_CQDB_REG + ((U32)x * DW_OFFSET_04H)])
#define HAL_NVME_CLR_INVLD_CQDB_REG(x,y)    (R32_NVME_NL_REG[R32_NVME_NL_INVLD_CQDB_REG + ((U32)x * DW_OFFSET_04H)] = (U32)y)

// F800_3080h E19 Add
#define HAL_NVME_GET_INVLD_CQDB_VAL(x)      (R32_NVME_NL_REG[R32_NVME_NL_INVLD_CQDB_VAL + ((U32)x * DW_OFFSET_04H)])
#define HAL_NVME_CLR_INVLD_CQDB_VAL(x,y)    (R32_NVME_NL_REG[R32_NVME_NL_INVLD_CQDB_VAL + ((U32)x * DW_OFFSET_04H)] = (U32)y)
#define HAL_NVME_CHK_HST_WR_ST()            (R32_NVME_NL_REG[R32_NVME_NL_HST_WR_ST])
#define HAL_NVME_CLR_HST_WR_ST(x)           (R32_NVME_NL_REG[R32_NVME_NL_HST_WR_ST] = ((U32)1 << x))


#define NL_SET_ALL_INT_EN()                 (R32_NVME_NL_REG[R32_NVME_NL_INT_EN] = 0x1F)
#define NL_SET_ALL_ERR_INT_EN()             (R32_NVME_NL_REG[R32_NVME_NL_ERR_INT_EN] = 0x1FFF)
#define NL_CHK_NO_CMD()      		         (R32_NVME_NL_REG[R32_NVME_NL_IDLE_ST] & NL_NFE_NO_CMD)

#define NL_GET_ABN_ADDR()              		(R64_NVME_NL_REG[R64_NVME_NL_ABN_ADDR] >> 0) // $$$ A13 $$$



#define NL_CHK_CE_STS(x)                     (R32_NVME_NL_RAM[R32_NVME_NL_CC + ((U32)x * DW_OFFSET_40H)] & NL_CC_EN) // nl ram
#define NL_GET_SHN_ST(x)				    ((R32_NVME_NL_RAM[R32_NVME_NL_CC + ((U32)x * DW_OFFSET_40H)] & NL_CC_SHN) >> 14)
#define NL_CLR_SHN_STS(x)                    (R32_NVME_NL_RAM[R32_NVME_NL_CSTS + ((U32)x * DW_OFFSET_40H)] &= (~NL_CSTS_SHST))
#define NL_SET_NSSRO(x, y)                    (R32_NVME_NL_RAM[R32_NVME_NL_CSTS + ((U32)x * DW_OFFSET_40H)] |= ((y & BITMSK(1,0)) << 4))
#define NL_GET_NSSRO(x)                    ((R32_NVME_NL_RAM[R32_NVME_NL_CSTS + ((U32)x * DW_OFFSET_40H)] & BITMSK(1,4)) >> 4 )

/*====================== END: NVME Layer Status =============================*/

/*======================== NVME Layer Control ================================*/
#define HAL_NVME_CHK_SQDB_EXCD_IGND()       (R32_NVME_NL_REG[R32_NVME_NL_CTRL] & NL_CTRL_SQDB_EXCD_IGND)
#define HAL_NVME_CHK_SQDB_PREV_IGND()       (R32_NVME_NL_REG[R32_NVME_NL_CTRL] & NL_CTRL_SQDB_PREV_IGND)
#define HAL_NVME_CHK_SQDB_FULL_IGND()       (R32_NVME_NL_REG[R32_NVME_NL_CTRL] & NL_CTRL_SQDB_FULL_IGND)

#define NL_CTRL_SET_SRAM_PAR_CHK_EN()       (R32_NVME_NL_REG[R32_NVME_NL_CTRL] |= NL_CTRL_SRAM_PAR_CHK_EN)
#define HAL_NVME_SET_SQDB_EXCD_IGND()       (R32_NVME_NL_REG[R32_NVME_NL_CTRL] |= NL_CTRL_SQDB_EXCD_IGND)
#define HAL_NVME_SET_SQDB_PREV_IGND()       (R32_NVME_NL_REG[R32_NVME_NL_CTRL] |= NL_CTRL_SQDB_PREV_IGND)
#define HAL_NVME_SET_SQDB_FULL_IGND()       (R32_NVME_NL_REG[R32_NVME_NL_CTRL] |= NL_CTRL_SQDB_FULL_IGND)
#define HAL_NVME_SET_BLK_CMD_EN()           (R32_NVME_NL_REG[R32_NVME_NL_CTRL] |= NL_CTRL_BLK_CMD_EN)
#define HAL_NVME_CLR_BLK_CMD_EN()           (R32_NVME_NL_REG[R32_NVME_NL_CTRL] &= (~NL_CTRL_BLK_CMD_EN))

#define M_SET_NL_INT_EN()                   {(R32_NVME_NL_REG[R32_NVME_NL_INT_EN] = 0x1F); \
                                                              (R32_NVME_NL_REG[R32_NVME_NL_ERR_INT_EN] = 0x1FFF);}

#define NL_CHK_NO_OUTSTD_CMD()              (R32_NVME_NL_REG[R32_NVME_NL_IDLE_ST] & NL_NFE_NO_OUTSTD) //This bit is different among E13/E21
#define NL_SET_FW_RST_FID(x)                (R8_NVME_NL_REG[R8_NVME_NL_FW_RST] = (U8)x)
#define NL_SET_FW_RST_EXE()                 (R32_NVME_NL_REG[R32_NVME_NL_FW_RST] |= FW_RST_EXE)
#define NL_CHK_FW_RST_EXE()                 (R32_NVME_NL_REG[R32_NVME_NL_FW_RST] &= FW_RST_EXE)
#define HAL_NVME_CLR_CMDH_DEL_DATA()        (R32_NVME_NL_REG[R32_NVME_NL_FW_DEL] = 0)
#define HAL_NVME_SET_CMDH_DEL_SQID(x)       (R32_NVME_NL_REG[R32_NVME_NL_FW_DEL] |= ((U32)(x)&DEL_SQID))
#define HAL_NVME_SET_CMDH_DEL_FID(x)        (R32_NVME_NL_REG[R32_NVME_NL_FW_DEL] |= ((U32)((x)<<8)&DEL_FID))
#define HAL_NVME_SET_CMDH_DEL_TYPE(x)       (R32_NVME_NL_REG[R32_NVME_NL_FW_DEL] |= ((x & BIT0) << 13))
#define HAL_NVEM_CHK_CMDH_STS_STABLE()      (R32_NVME_NL_REG[R32_NVME_NL_FW_DEL] & STS_STABLE)
#define HAL_NVME_SET_CMDH_DEL_REQ()         (R32_NVME_NL_REG[R32_NVME_NL_FW_DEL] |= DEL_REQ)
#define HAL_NVME_CHK_CMDH_DEL_REQ()         (R32_NVME_NL_REG[R32_NVME_NL_FW_DEL] & DEL_REQ)

#define NL_CHK_CMDBUF_EMPTY()               (R32_NVME_NL_REG[R32_NVME_NL_IDLE_ST] & NL_CMDBUF_EMPTY)
#define NL_CHK_SRAM_IDLE()                  (R32_NVME_NL_REG[R32_NVME_NL_IDLE_ST] & NL_SRAM_IDLE)
#define HAL_NVME_CHK_NL_ACQ_FULL()          (R32_NVME_NL_REG[R32_NVME_NL_IDLE_ST] & NL_ACQ_FULL)
#define HAL_NVME_GET_NL_ACQ_FULL_FID()      ((R32_NVME_NL_REG[R32_NVME_NL_IDLE_ST] & NL_ACQ_FULL_FID)>>8)

#define NL_GET_IDLE_ST()					(R8_NVME_NL_REG[R8_NVME_NL_IDLE_ST] & BITMSK(8,0))

#define NL_SQ_MASK_GET_ST() 				(R32_NVME_NL_REG[R32_NVME_NL_SQ_MASK_ST])
#define NL_CHK_ALLSQ_MASK()                 (R32_NVME_NL_REG[R32_NVME_NL_SQ_MASK_ST]  == ALL_SQ)
#define NL_SQx_MASK_GET_ST(x) 				((R8_NVME_NL_REG[R8_NVME_NL_SQ_MASK_ST + ((U8)x / 8)] & (1 << ((U8)x % 8))) >> ((U8)x % 8))

#define NL_SET_ALLSQ_MASK()                 (R32_NVME_NL_REG[R32_NVME_NL_SQ_MASK]  = ALL_SQ)
#define NL_CLR_ALLSQ_MASK()                 (R32_NVME_NL_REG[R32_NVME_NL_SQ_MASK]  = 0x000)

#define NL_SET_SQx_MASK(x) 		            (R32_NVME_NL_REG[R32_NVME_NL_SQ_MASK + ((U32)x / 32)] |= (1 << ((U8)x % 32)))
#define NL_CLR_SQx_MASK(x)                  (R32_NVME_NL_REG[R32_NVME_NL_SQ_MASK + ((U32)x / 32)] &= ~(1 << ((U8)x % 32)))
#define NL_GET_ALLSQ_MASK()                 (R32_NVME_NL_REG[R32_NVME_NL_SQ_MASK])
#define HAL_NVME_SET_ALL_FUNC_MASK()        (R32_NVME_NL_REG[R32_NVME_NL_FUNC_MASK] = CTRL_FUNC_MASK)
#define HAL_NVME_CLR_ALL_FUNC_MASK()        (R32_NVME_NL_REG[R32_NVME_NL_FUNC_MASK] &= ~CTRL_FUNC_MASK)

#define NL_SET_IC_DIS(x, iv)			    (R64_NVME_NL_RAM[R64_NVME_NL_MSIX_TABLE_H + ((U32)iv * QW_OFFSET_10H)] |= (((U64)x << 33) & NL_INT_COA_CD))
#define NL_CLR_IC_DIS(iv)					(R64_NVME_NL_RAM[R64_NVME_NL_MSIX_TABLE_H + ((U32)iv * QW_OFFSET_10H)] &= ~(NL_INT_COA_CD))
#define NL_GET_IC_DIS(iv)					((R64_NVME_NL_RAM[R64_NVME_NL_MSIX_TABLE_H + ((U32)iv * QW_OFFSET_10H)] & NL_INT_COA_CD) >> 33)

#define	NL_SRAM_CTRL_ADDR					(R32_NVME_NL_REG[R32_NVME_NL_SRAM_CTRL])
#define NL_SET_SRAM_INIT()                  (R32_NVME_NL_REG[R32_NVME_NL_SRAM_CTRL] |= CTRL_SRAM_INIT)
#define NL_CHK_SRAM_INIT()                  (R32_NVME_NL_REG[R32_NVME_NL_SRAM_CTRL] & SRAM_INIT_DONE)
#define HAL_NVME_SET_NL_CPL_FLUSH(fid)      (R32_NVME_NL_REG[R32_NVME_NL_CPL_FLUSH] |= (fid))
#define HAL_NVME_CLR_NL_CPL_FLUSH(fid)      (R32_NVME_NL_REG[R32_NVME_NL_CPL_FLUSH] &= ~(fid))

#define NL_ARB_SET_BURST(x, y)				(R32_NVME_NL_RAM[R32_NVME_NL_WRR_WGT + ((U32)x * DW_OFFSET_40H)] |= (((U32)y << 0) & CTRL_ARB_BURST))
#define NL_ARB_GET_BURST(x)					(R32_NVME_NL_RAM[R32_NVME_NL_WRR_WGT + ((U32)x * DW_OFFSET_40H)])
#define NL_ARB_CLR_BURST(x)					(R32_NVME_NL_RAM[R32_NVME_NL_WRR_WGT + ((U32)x * DW_OFFSET_40H)] &= ~(CTRL_ARB_BURST))
#define NL_ARB_SET_WGT_LO(x, y)				(R32_NVME_NL_RAM[R32_NVME_NL_WRR_WGT + ((U32)x * DW_OFFSET_40H)] |= (((U32)y << 8) & CTRL_LOW_PRI_NUM))
#define NL_ARB_GET_WGT_LO(x)				(R32_NVME_NL_RAM[R32_NVME_NL_WRR_WGT + ((U32)x * DW_OFFSET_40H)])
#define NL_ARB_SET_WGT_MED(x, y)		    (R32_NVME_NL_RAM[R32_NVME_NL_WRR_WGT + ((U32)x * DW_OFFSET_40H)] |= (((U32)y << 16) & CTRL_MED_PRI_NUM))
#define NL_ARB_GET_WGT_MED(x)				(R32_NVME_NL_RAM[R32_NVME_NL_WRR_WGT + ((U32)x * DW_OFFSET_40H)])
#define NL_ARB_SET_WGT_HI(x, y)				(R32_NVME_NL_RAM[R32_NVME_NL_WRR_WGT + ((U32)x * DW_OFFSET_40H)] |= (((U32)y << 24) & CTRL_HIGH_PRI_NUM))
#define NL_ARB_GET_WGT_HI(x)				(R32_NVME_NL_RAM[R32_NVME_NL_WRR_WGT + ((U32)x * DW_OFFSET_40H)])
#define NL_ARB_CLR_WGT_ALL(x)				(R32_NVME_NL_RAM[R32_NVME_NL_WRR_WGT + ((U32)x * DW_OFFSET_40H)] &= ~(BITMSK(24,8)))
#define NL_ARB_CLR_1ST_ADM(x)				(R32_NVME_NL_RAM[R32_NVME_NL_WRR_WGT + ((U32)x * DW_OFFSET_40H)] &= (~CTRL_1ST_ADM))

#define NL_INT_COA_TIME(x, y)			    (R32_NVME_NL_RAM[R32_NVME_NL_IC_CFG + ((U32)x * DW_OFFSET_40H)] |= (((U32)y << 8) & INT_COA_TIME))
#define NL_INT_COA_THR(x, y)			    (R32_NVME_NL_RAM[R32_NVME_NL_IC_CFG + ((U32)x * DW_OFFSET_40H)] |= (((U32)y << 0) & INT_COA_THR))
#define NL_CLR_TIME_THR(x)					(R32_NVME_NL_RAM[R32_NVME_NL_IC_CFG + ((U32)x * DW_OFFSET_40H)] &= ~(BITMSK(16,0)))

#define NL_SRAM_CTRL_W32                    (0x000000104 >> 2)

/*====================== END: NVME Layer Control =============================*/


/*======================== NVME Controller Cap and Config ====================*/
#define NLCAP_SET_MQES(x, y)			    (R32_NVME_NL_RAM[R32_NVME_NL_CAP0 + ((U32)x * DW_OFFSET_40H)] |= (((U32)y << 0) & NL_CAP_MQES))
#define NLCAP_GET_MQES(x)			        ((R32_NVME_NL_RAM[R32_NVME_NL_CAP0 + ((U32)x * DW_OFFSET_40H)] & NL_CAP_MQES) >> 0)
#define NLCAP_GET_CQR(x)                    ((R32_NVME_NL_RAM[R32_NVME_NL_CAP0 + ((U32)x * DW_OFFSET_40H)] & NL_CAP_CQR) >> 16)
#define NLCAP_GET_AMS(x)			        ((R32_NVME_NL_RAM[R32_NVME_NL_CAP0 + ((U32)x * DW_OFFSET_40H)] & NL_CAP_AMS) >> 17)
#define NLCAP_SET_TO(x, y)				    (R32_NVME_NL_RAM[R32_NVME_NL_CAP0 + ((U32)x * DW_OFFSET_40H)] |= (((U32)y << 24) & NL_CAP_TO))
#define NLCAP_GET_TO(x)				        ((R32_NVME_NL_RAM[R32_NVME_NL_CAP0 + ((U32)x * DW_OFFSET_40H)] & (NL_CAP_TO)) >> 24)
#define NLCAP_CLR_TO(x)                     (R32_NVME_NL_RAM[R32_NVME_NL_CAP0 + ((U32)x * DW_OFFSET_40H)] &= (~NL_CAP_TO))

#define NLCAP_GET_DSTRD(x)                  ((R32_NVME_NL_RAM[R32_NVME_NL_CAP1 + ((U32)x * DW_OFFSET_40H)] & NL_CAP_DSTRD) >> 0)
#define NLCAP_GET_NSSRS(x)                  ((R32_NVME_NL_RAM[R32_NVME_NL_CAP1 + ((U32)x * DW_OFFSET_40H)] & NL_CAP_NSSRS) >> 4)
#define NLCAP_GET_CSS(x)                    ((R32_NVME_NL_RAM[R32_NVME_NL_CAP1 + ((U32)x * DW_OFFSET_40H)] & NL_CAP_CSS) >> 5)
#define NLCAP_SET_CSS(x, y)			        (R32_NVME_NL_RAM[R32_NVME_NL_CAP1 + ((U32)x * DW_OFFSET_40H)] |= (((U32)y << 5) & NL_CAP_CSS))
#define NLCAP_GET_BPS(x)                    ((R32_NVME_NL_RAM[R32_NVME_NL_CAP1 + ((U32)x * DW_OFFSET_40H)] & NL_CAP_BPS) >> 13)
#define NLCAP_GET_MPSMIN(x)                 ((R32_NVME_NL_RAM[R32_NVME_NL_CAP1 + ((U32)x * DW_OFFSET_40H)] & NL_CAP_MPSMIN) >> 16)
#define NLCAP_GET_MPSMAX(x)                 ((R32_NVME_NL_RAM[R32_NVME_NL_CAP1 + ((U32)x * DW_OFFSET_40H)] & NL_CAP_MPSMAX) >> 20)
#define NLCAP_SET_MPSMAX(x, y)		        (R32_NVME_NL_RAM[R32_NVME_NL_CAP1 + ((U32)x * DW_OFFSET_40H)] |= (((U32)y << 20) & NL_CAP_MPSMAX))
#define NLCAP_CLR_MPSMAX(x)                 (R32_NVME_NL_RAM[R32_NVME_NL_CAP1 + ((U32)x * DW_OFFSET_40H)] &= (~NL_CAP_MPSMAX))

#define NLVS_GET_VER()                      (R32_NVME_NL_RAM[R32_NVME_NL_VS] >> 0)
#define NLVS_SET_VER(x)                     (R32_NVME_NL_RAM[R32_NVME_NL_VS] = x)

#define NLCC_SET_CCEN(x,N)                  (R32_NVME_NL_RAM[R32_NVME_NL_CC + ((U32)x * DW_OFFSET_40H)] |= N)
#define NLCC_CHK_CCEN(x)                    (R32_NVME_NL_RAM[R32_NVME_NL_CC + ((U32)x * DW_OFFSET_40H)] & NL_CC_EN)
#define NLCC_CLR_CCEN(x)                    (R32_NVME_NL_RAM[R32_NVME_NL_CC + ((U32)x * DW_OFFSET_40H)] &= (~NL_CC_EN))
#define NLCC_GET_CSS(x)						((R32_NVME_NL_RAM[R32_NVME_NL_CC + ((U32)x * DW_OFFSET_40H)] & NL_CC_CSS) >> 4)
#define NLCC_GET_MPS(x)				        ((R32_NVME_NL_RAM[R32_NVME_NL_CC + ((U32)x * DW_OFFSET_40H)] & NL_CC_MPS) >> 7)
#define NLCC_GET_AMS(x)				        ((R32_NVME_NL_RAM[R32_NVME_NL_CC + ((U32)x * DW_OFFSET_40H)] & NL_CC_AMS) >> 11)
#define	NLCC_GET_SHN(x)						((R32_NVME_NL_RAM[R32_NVME_NL_CSTS + ((U32)x * DW_OFFSET_40H)] & NL_CC_SHN) >> 14)
#define NLCC_GET_IOSQES(x)			        ((R32_NVME_NL_RAM[R32_NVME_NL_CC + ((U32)x * DW_OFFSET_40H)] & NL_CC_IOSQES) >> 16)
#define NLCC_GET_IOCQES(x)			        ((R32_NVME_NL_RAM[R32_NVME_NL_CC + ((U32)x * DW_OFFSET_40H)] & NL_CC_IOCQES) >> 20)
#define	NLCC_GET_ALL(x)						(R32_NVME_NL_RAM[R32_NVME_NL_CC + ((U32)x * DW_OFFSET_40H)])
#define	NLCC_SET_ALL(x,y)					(R32_NVME_NL_RAM[R32_NVME_NL_CC + ((U32)x * DW_OFFSET_40H)] = (y))

#define NLCSTS_CLR_RDY(x)                   (R32_NVME_NL_RAM[R32_NVME_NL_CSTS + ((U32)x * DW_OFFSET_40H)] &= (~NL_CSTS_RDY))
#define NLCSTS_SET_RDY(x)                   (R32_NVME_NL_RAM[R32_NVME_NL_CSTS + ((U32)x * DW_OFFSET_40H)] |= NL_CSTS_RDY)
#define NLCSTS_CHK_RDY(x)                   (R32_NVME_NL_RAM[R32_NVME_NL_CSTS + ((U32)x * DW_OFFSET_40H)] & NL_CSTS_RDY)
#define NLCSTS_GET_CFS(x)			        ((R32_NVME_NL_RAM[R32_NVME_NL_CSTS + ((U32)x * DW_OFFSET_40H)] & NL_CSTS_CFS) >> 1)
#define NLCSTS_SET_CFS(x)			        (R32_NVME_NL_RAM[R32_NVME_NL_CSTS + ((U32)x * DW_OFFSET_40H)] |= NL_CSTS_CFS)
#define NLCSTS_CLR_CFS(x)			        (R32_NVME_NL_RAM[R32_NVME_NL_CSTS + ((U32)x * DW_OFFSET_40H)] &= (~NL_CSTS_CFS))
#define NLCSTS_SET_SHST(x, y)			    (R32_NVME_NL_RAM[R32_NVME_NL_CSTS + ((U32)x * DW_OFFSET_40H)] |= ((y << 2) & NL_CSTS_SHST))
#define NLCSTS_CLR_SHST(x)                  (R32_NVME_NL_RAM[R32_NVME_NL_CSTS + ((U32)x * DW_OFFSET_40H)] &=  (~NL_CSTS_SHST))
#define NLCSTS_GET_NSSRO(x)                 ((R32_NVME_NL_RAM[R32_NVME_NL_CSTS + ((U32)x * DW_OFFSET_40H)] & NL_CSTS_NSSRO) >> 4)
#define NLCSTS_SET_NSSRO(x,y)			    (R32_NVME_NL_RAM[R32_NVME_NL_CSTS + ((U32)x * DW_OFFSET_40H)] |= ((y << 4) & NL_CSTS_NSSRO))
#define NLCSTS_CLR_NSSRO(x)			        (R32_NVME_NL_RAM[R32_NVME_NL_CSTS + ((U32)x * DW_OFFSET_40H)] &= (~NL_CSTS_NSSRO))
#define NLCSTS_SET_PP(x,y)			        (R32_NVME_NL_RAM[R32_NVME_NL_CSTS + ((U32)x * DW_OFFSET_40H)] |= (y<<5))
#define NLCSTS_CLR_PP(x)			        (R32_NVME_NL_RAM[R32_NVME_NL_CSTS + ((U32)x * DW_OFFSET_40H)] &= (~NL_CSTS_PP))

#define SHST_PROCESS_OCCURRING 				(BIT0)
#define SHUTDOWN_PROCESS_COMPLETE 			(BIT1)

#define HAL_NVME_SET_CAP0_MQES(x,y)         (R32_NVME_NL_RAM[R32_NVME_NL_CAP0 + ((U32)x * DW_OFFSET_40H)] |= (((U32)y << 0) & NL_CAP_MQES))
#define HAL_NVME_SET_CAP0_CQR(x,y)          (R32_NVME_NL_RAM[R32_NVME_NL_CAP0 + ((U32)x * DW_OFFSET_40H)] |= (((U32)y << 16) & NL_CAP_CQR))
#define HAL_NVME_SET_CAP0_AMS(x,y)          (R32_NVME_NL_RAM[R32_NVME_NL_CAP0 + ((U32)x * DW_OFFSET_40H)] |= (((U32)y << 17) & NL_CAP_AMS))
#define HAL_NVME_SET_CAP0_TO(x,y)           (R32_NVME_NL_RAM[R32_NVME_NL_CAP0 + ((U32)x * DW_OFFSET_40H)] |= (((U32)y << 24) & NL_CAP_TO))

#define HAL_NVME_CLR_CAP0_TO(x)             (R32_NVME_NL_RAM[R32_NVME_NL_CAP0 + ((U32)x * DW_OFFSET_40H)] &= (~NL_CAP_TO))
#define HAL_NVME_CLR_CAP0(x)                (R32_NVME_NL_RAM[R32_NVME_NL_CAP0 + ((U32)x * DW_OFFSET_40H)] = 0)

// 4020_5404h + N*40h (N = 0 ~ 16)
#define HAL_NVME_GET_CAP1_MPSMIN(x)         ((R32_NVME_NL_RAM[R32_NVME_NL_CAP1 + ((U32)x * DW_OFFSET_40H)] & NL_CAP_MPSMIN) >> 16)

#define HAL_NVME_SET_CAP1_NSSRS(x,y)        (R32_NVME_NL_RAM[R32_NVME_NL_CAP1 + ((U32)x * DW_OFFSET_40H)] |= (((U32)y << 4) & NL_CAP_NSSRS))
#define HAL_NVME_SET_CAP1_CSS(x,y)          (R32_NVME_NL_RAM[R32_NVME_NL_CAP1 + ((U32)x * DW_OFFSET_40H)] |= (((U32)y << 5) & NL_CAP_CSS))
#define HAL_NVME_SET_CAP1_BPS(x,y)          (R32_NVME_NL_RAM[R32_NVME_NL_CAP1 + ((U32)x * DW_OFFSET_40H)] |= (((U32)y << 13) & NL_CAP_BPS))
#define HAL_NVME_SET_CAP1_MPSMIN(x,y)       (R32_NVME_NL_RAM[R32_NVME_NL_CAP1 + ((U32)x * DW_OFFSET_40H)] |= (((U32)y << 16) & NL_CAP_MPSMIN))
#define HAL_NVME_SET_CAP1_MPSMAX(x,y)       (R32_NVME_NL_RAM[R32_NVME_NL_CAP1 + ((U32)x * DW_OFFSET_40H)] |= (((U32)y << 20) & NL_CAP_MPSMAX))

#define HAL_NVME_CLR_CAP1_MPSMAX(x)         (R32_NVME_NL_RAM[R32_NVME_NL_CAP1 + ((U32)x * DW_OFFSET_40H)] &= (~NL_CAP_MPSMAX))
#define HAL_NVME_CLR_CAP1(x)                (R32_NVME_NL_RAM[R32_NVME_NL_CAP1 + ((U32)x * DW_OFFSET_40H)] = 0)
#define BOOTPARTITIONSIZE_MAX        		BC_32MB


/*====================== END: NVME Controller Cap and Config =================*/


/*======================== NVME Layer Complete Information ======================*/
#define NLCPL_GET_STS()				        (R32_NVME_NL_REG[R32_NVME_NL_CPL_STS_L])
#define NLCPL_CLR_STS()				        (R32_NVME_NL_REG[R32_NVME_NL_CPL_STS_L] &= ~NL_CPL_ST)
#define NLCPL_SET_CID(x)			        (R32_NVME_NL_REG[R32_NVME_NL_CPL_STS_L] |= (x & NL_CPL_CID))
#define NLCPL_SET_ST(x)				        (R32_NVME_NL_REG[R32_NVME_NL_CPL_STS_L] |= ((x & BITMSK(15,0)) << 16))
#define NLCPL_SET_PUSH()			        (R32_NVME_NL_REG[R32_NVME_NL_CPL_STS_L] |= NL_CPL_PUSH_BIT)
#define NLCPL_GET_PUSH()                    ((R32_NVME_NL_REG[R32_NVME_NL_CPL_STS_L] & NL_CPL_PUSH_BIT) >> 31 )
#define NLCPL_CHK_PUSH()					(R32_NVME_NL_REG[R32_NVME_NL_CPL_STS_L] & NL_CPL_PUSH_BIT)
#define NLCPL_PUSH()			            (R32_NVME_NL_REG[R32_NVME_NL_CPL_STS_L] |= NL_CPL_PUSH_BIT)

#define NLCPL_CLR_SPC()				        (R32_NVME_NL_REG[R32_NVME_NL_CPL_STS_M] = 0)
#define NLCPL_SET_SPC(x)			        (R32_NVME_NL_REG[R32_NVME_NL_CPL_STS_M] = (x))

#define NLCPL_CLR_INFO()			        (R32_NVME_NL_REG[R32_NVME_NL_CPL_STS_H] = 0 )
#define NLCPL_SET_NLB(x)                    (R32_NVME_NL_REG[R32_NVME_NL_CPL_STS_H] |= (x & NL_CPL_NLB))
#define NLCPL_SET_LBA4K()           	    (R32_NVME_NL_REG[R32_NVME_NL_CPL_STS_H] |= NL_CPL_LABF)
#define NLCPL_SET_SQID(x)           	    (R32_NVME_NL_REG[R32_NVME_NL_CPL_STS_H] |= ((x & BITMSK(8,0)) << 16))
#define NLCPL_SET_IOCMD()           	    (R32_NVME_NL_REG[R32_NVME_NL_CPL_STS_H] |= NL_CPL_IO_CMD)

/*====================== END: NVME Layer Complete Information ===================*/

#define HAL_NVME_GET_FUNC_BASE(x)           (R8_NVME_NL_REG[R8_NVME_NL_FUNC_BASE + ((U8)x)])


#define HAL_NVME_GET_SQ_CQID(x)             ((R64_NVME_NL_RAM[R64_NVME_NL_SQ_DESP0_L + ((U32)x * QW_OFFSET_20H)] & NL_SQ_CQID) >> 4)
#define HAL_NVME_GET_SQ_BASE(x)             ((R64_NVME_NL_RAM[R64_NVME_NL_SQ_DESP0_L + ((U32)x * QW_OFFSET_20H)] & NL_SQ_BASE) >> 12)
#define HAL_NVME_GET_SQ_SIZE(x)             ((R64_NVME_NL_RAM[R64_NVME_NL_SQ_DESP0_H + ((U32)x * QW_OFFSET_20H)] & NL_SQ_SIZE) >> 0)

#define HAL_NVME_SET_SQ_EN(x)               (R32_NVME_NL_RAM[R32_NVME_NL_SQ_DESP0_DW0 + ((U32)x * DW_OFFSET_20H)] |= NL_SQ_EN)
#define HAL_NVME_SET_SQ_PRI(x,y)            (R64_NVME_NL_RAM[R64_NVME_NL_SQ_DESP0_L + ((U32)x * QW_OFFSET_20H)] |= (((U64)y << 1) & NL_SQ_PRI))
#define HAL_NVME_SET_SQ_CQID(x,y)           (R64_NVME_NL_RAM[R64_NVME_NL_SQ_DESP0_L + ((U32)x * QW_OFFSET_20H)] |= (((U64)y << 4) & NL_SQ_CQID))
#define HAL_NVME_SET_SQ_BASE(x,y)           (R64_NVME_NL_RAM[R64_NVME_NL_SQ_DESP0_L + ((U32)x * QW_OFFSET_20H)] |= (((U64)y << 12) & NL_SQ_BASE))
#define HAL_NVME_SET_SQ_DESP0_L(x,y)        (R64_NVME_NL_RAM[R64_NVME_NL_SQ_DESP0_L + ((U32)x * QW_OFFSET_20H)] = (U64)y)
#define HAL_NVME_SET_SQ_SIZE(x,y)           (R64_NVME_NL_RAM[R64_NVME_NL_SQ_DESP0_H + ((U32)x * QW_OFFSET_20H)] |= (((U64)y << 0) & NL_SQ_SIZE))


#define HAL_NVME_CLR_SQ_EN(x)               (R32_NVME_NL_RAM[R32_NVME_NL_SQ_DESP0_DW0 + ((U32)x * DW_OFFSET_20H)] &= (~NL_SQ_EN))
#define HAL_NVME_CLR_SQ_DESP0_L(x)          (R64_NVME_NL_RAM[R64_NVME_NL_SQ_DESP0_L + ((U32)x * QW_OFFSET_20H)] = 0)//E19_host
#define HAL_NVME_CLR_SQ_DESP0_H(x)          (R64_NVME_NL_RAM[R64_NVME_NL_SQ_DESP0_H + ((U32)x * QW_OFFSET_20H)] = 0)//E19_host
#define HAL_NVME_CLR_SQ_DESP1_L(x)          (R64_NVME_NL_RAM[R64_NVME_NL_SQ_DESP1_L + ((U32)x * QW_OFFSET_20H)] = 0)//E19_host
#define HAL_NVME_CLR_SQ_DESP1_H(x)          (R64_NVME_NL_RAM[R64_NVME_NL_SQ_DESP1_H + ((U32)x * QW_OFFSET_20H)] = 0)//E19_host


#define HAL_NVME_GET_CQ_BASE(x)             ((R64_NVME_NL_RAM[R64_NVME_NL_CQ_DESP_L + ((U32)x * QW_OFFSET_10H)] & NL_CQ_BASE) >> 12)
#define HAL_NVME_GET_CQ_SIZE(x)             ((R64_NVME_NL_RAM[R64_NVME_NL_CQ_DESP_H + ((U32)x * QW_OFFSET_10H)] & NL_CQ_SIZE) >> 0)

#define HAL_NVME_SET_CQ_EN(x)               (R32_NVME_NL_RAM[R32_NVME_NL_CQ_DESP_DW0 + ((U32)x * DW_OFFSET_10H)] |= NL_CQ_EN)
#define HAL_NVME_SET_CQ_IE(x,y)             (R64_NVME_NL_RAM[R64_NVME_NL_CQ_DESP_L + ((U32)x * QW_OFFSET_10H)] |= (((U64)y << 1) & NL_CQ_IE))
#define HAL_NVME_SET_CQ_IVID(x,y)           (R64_NVME_NL_RAM[R64_NVME_NL_CQ_DESP_L + ((U32)x * QW_OFFSET_10H)] |= (((U64)y << 4) & NL_CQ_IVID))
#define HAL_NVME_SET_CQ_BASE(x,y)           (R64_NVME_NL_RAM[R64_NVME_NL_CQ_DESP_L + ((U32)x * QW_OFFSET_10H)] |= (((U64)y << 12) & NL_CQ_BASE))
#define HAL_NVME_SET_CQ_DESP_L(x,y)         (R64_NVME_NL_RAM[R64_NVME_NL_CQ_DESP_L + ((U32)x * QW_OFFSET_10H)] = (U64)y)
#define HAL_NVME_SET_CQ_SIZE(x,y)           (R64_NVME_NL_RAM[R64_NVME_NL_CQ_DESP_H + ((U32)x * QW_OFFSET_10H)] |= (((U64)y << 0) & NL_CQ_SIZE))

#define HAL_NVME_CLR_CQ_EN(x)               (R32_NVME_NL_RAM[R32_NVME_NL_CQ_DESP_DW0 + ((U32)x * DW_OFFSET_10H)] &= (~NL_CQ_EN))


/*======================== NVME Layer Queue Information ======================*/
#define NL_QINFO_OFFSET                     (0x2000) //The cq desp offset from NL ram.
#define NL_SQ_DESP_SIZE                     (0x20)
#define NL_CQ_DESP_SIZE                     (0x10)
#define NL_QINFO_CNT                        (132)
#define NL_QINFO_SIZE                       (0x40)
#define NL_QINFO_CQ_SIZE                 (0x20)
#define NL_QINFO_SQSIZE                     (8)
#define NL_QINFO_CQSIZE                     (4)

#define NL_MAX_QINFO_BLOCK_NUM				(9) // 1:Adm, 8:I/O // Eason: ?????

#define NL_GET_SQ_EN(SQID)		    (R8_NVME_NL_RAM[ ((SQID) * NL_SQ_DESP_SIZE)] & NL_SQ_EN)
#define NL_GET_CQ_EN(CQID)		    (R8_NVME_NL_RAM[NL_QINFO_OFFSET + ((CQID) * NL_CQ_DESP_SIZE) ] & NL_CQ_EN)
//offset 8 byte to get NL_SQ_CQID
#define NL_GET_SQ_CQID(SQID)	    ((R16_NVME_NL_RAM[(((SQID) * NL_SQ_DESP_SIZE) >> 1)] & (NL_SQ_CQID)) >> 4)

#define HAL_NVME_CHK_CQ_EN(x)       (R32_NVME_NL_RAM[R32_NVME_NL_CQ_DESP_DW0 + ((U32)x * DW_OFFSET_10H)] & NL_CQ_EN)
#define HAL_NVME_CHK_SQ_EN(x)       (R32_NVME_NL_RAM[R32_NVME_NL_SQ_DESP0_DW0 + ((U32)x * DW_OFFSET_20H)] & NL_SQ_EN)

#define NL_GET_SQ_BASE(SQID)	    ((R64_NVME_NL_RAM[R64_NVME_NL_SQ_DESP0_L+((SQID) * (QW_OFFSET_20H))] & NL_SQ_BASE)>>12)
#define NL_GET_CQ_BASE(CQID)	     ((R64_NVME_NL_RAM[R64_NVME_NL_CQ_DESP_L+((CQID) * (QW_OFFSET_10H))] & NL_CQ_BASE)>>12)


#define NL_GET_SQ_HEAD(SQID)	    ((R64_NVME_NL_RAM[R64_NVME_NL_SQ_DESP0_H+((SQID) * (QW_OFFSET_20H))] & NL_SQ_HEAD)>>16)
#define NL_GET_SQ_TAIL(SQID)	    ((R64_NVME_NL_RAM[R64_NVME_NL_SQ_DESP0_H+((SQID) * (QW_OFFSET_20H))] & NL_SQ_TAIL)>>48)
#define NL_GET_CQ_HEAD(SQID)	    ((R64_NVME_NL_RAM[R64_NVME_NL_CQ_DESP_H+((SQID) * (QW_OFFSET_10H))] & NL_CQ_HEAD)>>32)
#define NL_GET_CQ_TAIL(SQID)	    ((R64_NVME_NL_RAM[R64_NVME_NL_CQ_DESP_H+((SQID) * (QW_OFFSET_10H))] & NL_CQ_TAIL)>>48)


#if VS_SIM_EN
#define NVME_PACKED
#else
#define NVME_PACKED  __attribute__((packed))
#endif

typedef union nvme_sq_info          		 SQINFO;
typedef union nvme_cq_info         		 	CQINFO;

typedef volatile union nvme_sq_info         *SQINFO_PTR;      // total 0 ~ 8 QINFO Block
typedef volatile union nvme_cq_info         *CQINFO_PTR;      // total 0 ~ 8 QINFO Block

union nvme_sq_info {

	// SQ Descriptor //
	U32 ulDW[8];

	struct NVME_PACKED {

		// [63 : 0]
		U64 ullNlSqEn    : 1;
		U64 ullNlSqPri   : 2;    // SQ priority
		U64 ullResv0     : 1;
		U64 ullNlSqCqId  : 8;
		U64 ullNlSqBase : 52;

		// [127 : 64]
		U16 uwNlSqSize;
		U16 uwNlSqHead;
		U16 uwNlSqWHead;
		U16 uwNlSqTail;

		// [63 : 0]
		U32 ulNlSqWl        ;    // SQ workload
		U32 ulNlSqWllThr    ;

		// [127 : 64]
		U32 ulNlSqWlrThr    ;
		U32 ulResv1         ;
	};
};



union nvme_cq_info {

	// CQ Descriptor //
	U32 ulDW[4];

	struct NVME_PACKED {

		// [63 : 0]
		U64 ullNlCqEn    : 1;
		U64 ullNlCqIe    : 1;
		U64 ullNlCqPhtag : 1;
		U64 ullResv0     : 1;
		U64 ullNlCqIvId  : 8;
		U64 ullNlCqBase : 52;

		// [127 : 64]
		U16 uwNlCqSize      ;
		U16 uwNlCqWtail     ;
		U16 uwNlCqHead      ;
		U16 uwNlCqTail      ;
	};

};
/*====================== END: NVME Layer Queue Information ===================*/


/*=============================== NVME MSIX Table ===========================*/
typedef volatile struct nvme_msix_entry     *MSIXEntry_PTR;
#define NL_MSIX_OFFSET                      (0x3000)
#define gpNvmeMsix                          ((MSIXEntry_PTR)(NVME_RAM_ADDRESS+NL_MSIX_OFFSET)) //0x40217000


struct nvme_msix_entry {

	// MSIX Entry //
	union {
		U32 ulDW[4];

		struct NVME_PACKED {
			U64 ullMsgAddr      ;
			U32 ulMsgData       ;
			U8  ubMask       : 1;
			U8  ubIntCoaDisable : 1;
			U32 ulResv0      : 2;
			U32 ulIVMap		: 4;
			U32 ulResv1		: 8;
			U32 ulIntCoaCnt	: 8;
			U32 ulIntCoaTimer : 8;
		} elem ;
	} MSIX_ENTRY;

};

//

/*============================= END: NVME MSIX Table ========================*/


#endif /* _SHR_REG_NVME_H_ */
