#ifndef _VUC_ISPJUMP_H_
#define _VUC_ISPJUMP_H_

/* vendor_isp_jump */
#define VUC_ISP_JUMP_BOOT_ROM_LOAD_FW_ENABLE    (0x00)
#define VUC_ISP_JUMP_BOOT_ROM_LOAD_FW_DISABLE   (0x01)
#define VUC_ISP_JUMP_ROM_MODE_LOAD_FW           (0x00)
#define VUC_ISP_JUMP_ROM_MODE                   (0x01)
#define VUC_ISP_JUMP_PRAM_MODE                  (0x02)
#define VUC_ISP_JUMP_SPI_MODE_LOAD_FW           (0x04)
#define VUC_ISP_JUMP_SPI_MODE                   (0x05)

COMMON_AREA void VUC_ISPJumpAPI(U16 uwMode);
COMMON_AREA void VUC_IspJump(VUC_OPT_HCMD_PTR pCmd);

#endif /* _VUC_ISPJUMP_H_ */
