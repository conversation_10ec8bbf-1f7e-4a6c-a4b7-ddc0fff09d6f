#ifndef _S17_FW_VERSION_H_
#define _S17_FW_VERSION_H_

#include "setup.h"

//==============================================================================
// FW REVISION
//==============================================================================


#if (HOST_MODE == SATA)
#define	FW_REVISION0		'S'
#else /* (HOST_MODE == SATA) */
#define	FW_REVISION0		'E'
#endif /* (HOST_MODE == SATA) */

#define	FW_REVISION1		'H' // 17

#if BURNER_MODE_EN
#define	FW_REVISION2		'B'	// Burner
#elif (RDT_MODE_EN)
#define FW_REVISION2        'S' // RDT
#else /* BURNER_MODE_EN */
#define	FW_REVISION2		'F'	// FW
#endif /* BURNER_MODE_EN */

#if (FW_CATEGORY_CUSTOMER == CUSTOMER_MAINSTREAM)
#define	FW_REVISION3		'M'
#elif (KINGSTON_EN)
#define	FW_REVISION3		'K'
#elif (FW_CATEGORY_CUSTOMER == CUSTOMER_MICRON_NICKS)
#define	FW_REVISION3		'N'
#else /* FW_CATEGORY_CUSTOMER */
#error FW_CATEGORY_CUSTOMER Error
#endif /* FW_CATEGORY_CUSTOMER */

#if ((FW_CATEGORY_FLASH == FLASH_BICS3TLC) || (FW_CATEGORY_FLASH == FLASH_BICS4TLCHDR))
#define FW_REVISION4		'0'
#elif (FW_CATEGORY_FLASH == FLASH_BICS4QLC)
#define FW_REVISION4		'1'
#elif (FW_CATEGORY_FLASH == FLASH_MICRONTLC)
#define FW_REVISION4		'6'
#elif (FW_CATEGORY_FLASH == FLASH_BICS4PSLC)
#define FW_REVISION4		'3'
#elif (FW_CATEGORY_FLASH == FLASH_N18_QLC)
#define FW_REVISION4		'5'
#elif (FW_CATEGORY_FLASH == FLASH_N28_QLC)
#define FW_REVISION4		'7'
#elif (FW_CATEGORY_FLASH == FLASH_B27A_TLC)
#define FW_REVISION4		'8'
#elif (FW_CATEGORY_FLASH == FLASH_B47R_TLC)
#define FW_REVISION4		'9'
#elif (FW_CATEGORY_FLASH == FLASH_N48R_QLC)
#define FW_REVISION4		'F'
#elif (FW_CATEGORY_FLASH == FLASH_BICS5TLC)
#define FW_REVISION4		'2'
#elif (FW_CATEGORY_FLASH == FLASH_NONE)
#define FW_REVISION4		'X'
#else /* FW_CATEGORY_FLASH */
#error FW_CATEGORY_FLASH Error
#endif /* FW_CATEGORY_FLASH */

#if (RDT_MODE_EN || RDT_BURNER_MODE_EN)

#define FW_REVISION5        '0'
#define FW_REVISION6        '.'
#define FW_REVISION7        '0'
#define FW_INTERNALVERSION0 '0'
#define FW_INTERNALVERSION1 '0'
#define FW_INTERNALVERSION2 '0'
#define FW_INTERNALVERSION3 '0'

#else /*(RDT_MODE_EN || RDT_BURNER_MODE_EN)*/

#if RELEASED_FW
#if (FW_CATEGORY_CUSTOMER == CUSTOMER_SOEM)
#define	FW_REVISION5		'N'		// N: Kingston NV1.
#else
#define	FW_REVISION5		'0'		// T: for test version.
#endif
#else /* RELEASED_FW */
#define	FW_REVISION5		'T'		// T: for test version.
#endif /* RELEASED_FW */

#if NCS_EN
#define	FW_REVISION6		'C'
#elif (FW_CATEGORY_FLASH == FLASH_N48R_QLC) // W: for working sample
#define FW_REVISION6        'W'
#elif RELEASED_FW
#if (FW_CATEGORY_CUSTOMER == CUSTOMER_SOEM)
#define	FW_REVISION6		'0'     // Kingston NV1.
#else
#define	FW_REVISION6		'E'
#endif
#else /* NCS_EN */
#define	FW_REVISION6		'T'
#endif /* NCS_EN */

#if ((FW_CATEGORY_FLASH == FLASH_MICRONTLC) || (FW_CATEGORY_FLASH == FLASH_N18_QLC) || (FW_CATEGORY_FLASH == FLASH_N28_QLC) || (FW_CATEGORY_FLASH == FLASH_B47R_TLC) || (FW_CATEGORY_FLASH == FLASH_N48R_QLC) )
#if PARTIAL_BLOCK_MEDIA_SCAN_EN
#define    FW_REVISION7               '0'
#elif OPEN_BLOCK_RSMAP_EN
#define	FW_REVISION7		'1'
#else /* OPEN_BLOCK_RSMAP_EN */
#define	FW_REVISION7		'0'
#endif /* OPEN_BLOCK_RSMAP_EN */
#else /* ((FW_CATEGORY_FLASH == FLASH_MICRONTLC) || (FW_CATEGORY_FLASH == FLASH_N18_QLC) || (FW_CATEGORY_FLASH == FLASH_N28_QLC)) */
#define	FW_REVISION7		'0'
#endif /* ((FW_CATEGORY_FLASH == FLASH_MICRONTLC) || (FW_CATEGORY_FLASH == FLASH_N18_QLC) || (FW_CATEGORY_FLASH == FLASH_N28_QLC)) */

#define FW_INTERNALVERSION0 '0'
#define FW_INTERNALVERSION1 '0'
#define FW_INTERNALVERSION2 '0'

#if(FW_BUILD_VERSION == FW_VERSION_FULL_MAX_XZIP_ON)
#define    FW_INTERNALVERSION3 '0'
#elif (FW_BUILD_VERSION == FW_VERSION_FULL_MAX_XZIP_OFF)
#define    FW_INTERNALVERSION3 '1'
#elif (FW_BUILD_VERSION == FW_VERSION_FULL_8G_XZIP_ON)
#define    FW_INTERNALVERSION3 '2'
#elif (FW_BUILD_VERSION == FW_VERSION_FULL_PERFORMANCE_XZIP_ON)
#define    FW_INTERNALVERSION3 '3'
#elif (FW_BUILD_VERSION == FW_VERSION_FULL_PERFORMANCE_XZIP_OFF)
#define    FW_INTERNALVERSION3 '5'
#else
#error FW_BUILD_VERSION Error
#endif

#endif /*(RDT_MODE_EN || RDT_BURNER_MODE_EN)*/

#endif /* _S17_FW_VERSION_H_ */
