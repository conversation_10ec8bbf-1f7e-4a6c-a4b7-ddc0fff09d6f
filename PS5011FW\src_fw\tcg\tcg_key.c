#include "tcg.h"
#include "hal/pic/uart/uart_api.h"
#include <string.h>
#include "hal/security/security_api.h"
#include "tcg_inline.h"

#if (TCG_EN && (PS5013_EN || PS5021_EN || (PS5017_EN && !BURNER_MODE_EN)) && !RDT_MODE_EN && !LPM3_LOADER && !BOOTLOADER_MODE_EN)
void TcgGenerateAllDek(U8 ubKeepGlobalRangeKey)
{
	M_TCG_INFO_LOG(0x000D);
	U32 uli;
	U8 ubAESKeyNum = M_TCG_GET_AES_KEY_NUM();
	if ( !ubKeepGlobalRangeKey ) {
		TcgGenerateDek(TcgLockingGlobalRangeUidL);
	}
	for (uli = 1; uli < ubAESKeyNum; uli++) {
		TcgGenerateDek(TcgLockingRange1UidL + uli - 1);
	}
}

void TcgGenerateDek(U32 ulLockingUidL)
{
	if (M_TCG_AES_BUG_WORKAROUND()) {
		U8 ubKeyIndex;
		ubKeyIndex = TcgLockingUidLToKeyIndex(ulLockingUidL);
		if (TcgLockingGlobalRangeUidL == ulLockingUidL) {
			TcgDek_t Dek;
			//Execute only Gen Global Range Key
			if (M_TCG_CHECK_INIT_REASON(TCG_INIT_REASON_BURNER_PREFORMAT | TCG_INIT_REASON_DLMC_PREFORMAT)
				|| (RevertState_GenAllDek == gTcg.pAllFunctionStates->RevertState)
				|| (RevertSpState_GenAllDek == gTcg.pAllFunctionStates->RevertSpState)
				|| (TcgCryptoEraseState_GenAllDek == gTcg.CryptoEraseState)) {
				TcgRandomNumGenerator((U8 *)&Dek, sizeof(TcgDek_t));
				memcpy((void *)&gTcg.pVT->Dek[ubKeyIndex], (void *)&Dek, sizeof(TcgDek_t));
				memset((void *)&Dek, 0x00, sizeof(TcgDek_t));
			}
		}
		else {
			memset((void *)&gTcg.pVT->Dek[ubKeyIndex], 0xFF, sizeof(TcgDek_t));
		}
	}
	else {
		U8 ubKeyIndex;
		TcgDek_t Dek;
		ubKeyIndex = TcgLockingUidLToKeyIndex(ulLockingUidL);
		TcgRandomNumGenerator((U8 *)&Dek, sizeof(TcgDek_t));
		memcpy((void *)&gTcg.pVT->Dek[ubKeyIndex], (void *)&Dek, sizeof(TcgDek_t));
		memset((void *)&Dek, 0x00, sizeof(TcgDek_t));
	}
}

U8 TcgLockingUidLToKeyIndex(U32 ulLockingUidL)
{
	U8 ubKeyIndex;
	if ( ulLockingUidL == TcgLockingGlobalRangeUidL) {
		ubKeyIndex = 0;
	}
	else {
		ubKeyIndex = (ulLockingUidL & 0xFF);
	}
	return ubKeyIndex;
}

void TcgGenerateKek(void)
{
	M_TCG_INFO_LOG(0x000E);
	TcgKek_t Kek;
	TcgRandomNumGenerator((U8 *)&Kek, sizeof(TcgKek_t));
	memcpy((void *)&gTcg.Kek, (void *)&Kek, sizeof(TcgKek_t));
	gTcg.btKekExist = TRUE;
}

void TcgGenerateAllKekRange(void)
{
	M_TCG_INFO_LOG(0x000F);
	U32 uli;
	U8 ubAESKeyNum = M_TCG_GET_AES_KEY_NUM();
	for (uli = 0; uli < ubAESKeyNum; uli++) {
		TcgGenerateKekRange(uli);
	}
}

void TcgGenerateKekRange(U32 ulKeyIndex)
{
	TcgKek_t Kek;
	TcgRandomNumGenerator((U8 *)&Kek, sizeof(TcgKek_t));
	memcpy((void *)&gTcg.pVT->KekRange[ulKeyIndex], (void *)&Kek, sizeof(TcgKek_t));
}

void TcgEncryptAllDekToEdek(U8 ubKeepGlobalRangeKey, U8 ubRange, U8 ubDontCareLockOnResetBMP)
{
	EncryptAllDekState_t EncryptAllDekState = gTcg.pAllFunctionStates->EncryptAllDekState;
	EncryptAllDekVariablesPtr_t pEncryptAllDekVariables = gTcg.pAllFunctionStates->pEncryptAllDekVariables;
	U8 ubContinue = TRUE;
	while (ubContinue) {
		ubContinue = FALSE;
		switch (EncryptAllDekState) {
		case EncryptAllDekState_Initial:
			M_TCG_INFO_LOG(0x0010);
			TcgValidVariables(sizeof(EncryptAllDekVariables_t), &pEncryptAllDekVariables);
			pEncryptAllDekVariables->ubRange = ubRange;
			if (ubKeepGlobalRangeKey) {
				pEncryptAllDekVariables->ubKeyIndex = 1;
			}
			else {
				pEncryptAllDekVariables->ubKeyIndex = 0;
			}
			EncryptAllDekState = EncryptAllDekState_CheckContinue;
		//no break;
		case EncryptAllDekState_CheckContinue:
			if (pEncryptAllDekVariables->ubKeyIndex < M_TCG_GET_AES_KEY_NUM()) {
				EncryptAllDekState = EncryptAllDekState_Encrypt;
			}
			else {
				EncryptAllDekState = EncryptAllDekState_Done;
			}
			ubContinue = TRUE;
			break;
		case EncryptAllDekState_Encrypt:
			TcgEncryptDekToEdek(TcgKeyIndexToLockingUidL(pEncryptAllDekVariables->ubKeyIndex), pEncryptAllDekVariables->ubRange, ubDontCareLockOnResetBMP);
			if (gTcg.pAllFunctionStates->EncryptDekState == EncryptDekState_Initial) {
				EncryptAllDekState = EncryptAllDekState_AddIndex;
				ubContinue = TRUE;
			}
			break;
		case EncryptAllDekState_AddIndex:
			pEncryptAllDekVariables->ubKeyIndex ++;
			EncryptAllDekState = EncryptAllDekState_CheckContinue;
			ubContinue = TRUE;
			break;
		case EncryptAllDekState_Done:
			TcgInvalidVariables(sizeof(EncryptAllDekVariables_t), &pEncryptAllDekVariables);
			EncryptAllDekState = EncryptAllDekState_Initial;
			break;
		}
	}
	gTcg.pAllFunctionStates->EncryptAllDekState = EncryptAllDekState;
	gTcg.pAllFunctionStates->pEncryptAllDekVariables = pEncryptAllDekVariables;
}

void TcgEncryptDekToEdek(U32 ulLockingUidL, U8 ubRange, U8 ubDontCareLockOnResetBMP)
{
	EncryptDekState_t EncryptDekState = gTcg.pAllFunctionStates->EncryptDekState;
	EncryptDekVariablesPtr_t pEncryptDekVars = gTcg.pAllFunctionStates->pEncryptDekVariables;
	U8 ubContinue = TRUE;
	while (ubContinue) {
		ubContinue = FALSE;
		switch (EncryptDekState) {
		case EncryptDekState_Initial:
			M_TCG_INFO_LOG(0x0011);
			TcgValidVariables(sizeof(EncryptDekVariables_t), &pEncryptDekVars);
			pEncryptDekVars->ubKeyIndex = TcgLockingUidLToKeyIndex(ulLockingUidL);
			pEncryptDekVars->ubRange = ubRange;
			if (OPAL_RANGE == pEncryptDekVars->ubRange) {
				if ((TCG_CARE_LOCK_ON_RESET_BMP == ubDontCareLockOnResetBMP) && (gTcg.pVT->NonVolatile.ulLockOnResetBmp & BIT(pEncryptDekVars->ubKeyIndex))) {
					EncryptDekState = EncryptDekState_Done;
				}
				else {
					EncryptDekState = EncryptDekState_TriggerLoadEdek;
				}
			}
			else {
				EncryptDekState = EncryptDekState_TriggerLoadEdek;
			}
			ubContinue = TRUE;
			break;
		case EncryptDekState_TriggerLoadEdek:
			//Trigger Load
			TcgSetOpalTableEvent(OpalTableState_Load, TcgFlashTableOriginAdmin1Index, OPAL_NULL_TARGET_ADDR);
			if (gTcg.BufManager.OpalTableState == OpalTableState_Idle) {
				EncryptDekState = EncryptDekState_EncryptandSet;
				ubContinue = TRUE;
			}
			break;
		case EncryptDekState_EncryptandSet: {
				TcgKek_t Kek;
				TcgDek_t Dek;
				TcgEncryptedDek_t Edek;
				U8 ubi;
				U8 ubWrapNum;

				if (OPAL_NOT_RANGE == pEncryptDekVars->ubRange) {
					memcpy((void *)&Kek, (void *)&gTcg.Kek, sizeof(TcgKek_t));
				}
				else {
					memcpy((void *)&Kek, (void *)&gTcg.pVT->KekRange[pEncryptDekVars->ubKeyIndex], sizeof(TcgKek_t));
				}
				memcpy((void *)&Dek, (void *)&gTcg.pVT->Dek[pEncryptDekVars->ubKeyIndex], sizeof(TcgDek_t));
				TcgKek_t ZeroKek = {{0}};
				TcgDek_t ZeroDek = {{0}};
				if (memcmp(&ZeroKek, &Kek, sizeof(TcgKek_t)) == 0 || memcmp(&ZeroDek, &Dek, sizeof(TcgDek_t)) == 0) {
					M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, 0);
				}

				ubWrapNum = sizeof(TcgDek_t) / TCG_KEY_WRAP_PLAINTEXT_SIZE; //HW limit
				for (ubi = 0; ubi < ubWrapNum; ubi++) {
					TcgEncryptKeyData(NULL, 0, (U8 *)&Kek, (U8 *)((U32)(&Edek) + ubi * TCG_KEY_WRAP_CIPHERTEXT_SIZE), (U8 *)((U32)(&Dek) + ubi * TCG_KEY_WRAP_PLAINTEXT_SIZE), TcgEncryptKeyDataModeDek);
				}

				TcgAdminPartial1Ptr_t	pTcgApiStructAdminPartial1 = (TcgAdminPartial1Ptr_t)gTcg.BufManager.ulBufAddr;
				if (OPAL_NOT_RANGE == pEncryptDekVars->ubRange) {
					memcpy((void *)&pTcgApiStructAdminPartial1->Edek[pEncryptDekVars->ubKeyIndex], (void *)&Edek, sizeof(TcgEncryptedDek_t));
				}
				else {
					memcpy((void *)&pTcgApiStructAdminPartial1->EdekRange[pEncryptDekVars->ubKeyIndex], (void *)&Edek, sizeof(TcgEncryptedDek_t));
				}
			}
			EncryptDekState = EncryptDekState_TriggerSaveEdek;
		// no break;
		case EncryptDekState_TriggerSaveEdek:
			//Trigger Save
			TcgSetOpalTableEvent(OpalTableState_Save, TcgFlashTableOriginAdmin1Index, OPAL_NULL_TARGET_ADDR);
			if (gTcg.BufManager.OpalTableState == OpalTableState_Idle) {
				EncryptDekState = EncryptDekState_Done;
				ubContinue = TRUE;
			}
			break;
		case EncryptDekState_Done:
			TcgInvalidVariables(sizeof(EncryptDekVariables_t), &pEncryptDekVars);
			EncryptDekState = EncryptDekState_Initial;
			break;
		}
	}
	gTcg.pAllFunctionStates->EncryptDekState = EncryptDekState;
	gTcg.pAllFunctionStates->pEncryptDekVariables = pEncryptDekVars;
}

void TcgEncryptAllKekRangeToEkekRange(U8 ubDontCareLockOnResetBMP)
{
	EncryptAllKekRangeState_t EncryptAllKekRangeState = gTcg.pAllFunctionStates->EncryptAllKekRangeState;
	EncryptAllKekRangeVariablesPtr_t pEncryptAllKekRangeVariables = gTcg.pAllFunctionStates->pEncryptAllKekRangeVariables;
	U8 ubContinue = TRUE;
	while (ubContinue) {
		ubContinue = FALSE;
		switch (EncryptAllKekRangeState) {
		case EncryptAllKekRangeState_Initial:
			M_TCG_INFO_LOG(0x0012);
			TcgValidVariables(sizeof(EncryptAllKekRangeVariables_t), &pEncryptAllKekRangeVariables);
			pEncryptAllKekRangeVariables->ubKeyIndex = 0;
			EncryptAllKekRangeState = EncryptAllKekRangeState_Encrypt;
		// no break;
		case EncryptAllKekRangeState_Encrypt:
			TcgEncryptKekRangeToEkekRange(pEncryptAllKekRangeVariables->ubKeyIndex, ubDontCareLockOnResetBMP);
			if (gTcg.pAllFunctionStates->EncryptKekRangeState == EncryptKekRangeState_Initial) {
				EncryptAllKekRangeState = EncryptAllKekRangeState_CheckContinue;
				ubContinue = TRUE;
			}
			break;
		case EncryptAllKekRangeState_CheckContinue:
			pEncryptAllKekRangeVariables->ubKeyIndex++;
			if (pEncryptAllKekRangeVariables->ubKeyIndex < M_TCG_GET_AES_KEY_NUM()) { /* parasoft-suppress BD-PB-CC "In pyrite FW, it always evaluates to false" */
				EncryptAllKekRangeState = EncryptAllKekRangeState_Encrypt;
			}
			else {
				EncryptAllKekRangeState = EncryptAllKekRangeState_Done;
			}
			ubContinue = TRUE;
			break;
		case EncryptAllKekRangeState_Done:
			TcgInvalidVariables(sizeof(EncryptAllKekRangeVariables_t), &pEncryptAllKekRangeVariables);
			EncryptAllKekRangeState = EncryptAllKekRangeState_Initial;
			break;
		}
	}
	gTcg.pAllFunctionStates->EncryptAllKekRangeState = EncryptAllKekRangeState;
	gTcg.pAllFunctionStates->pEncryptAllKekRangeVariables = pEncryptAllKekRangeVariables;
}

void TcgEncryptKekandPasswordWhenInit(U8 ubMode, U8 ubEkekIdx)
{
	EncryptKekAndPasswordWhenInitState_t EncryptKekAndPasswordWhenInitState = gTcg.pAllFunctionStates->EncryptKekAndPasswordWhenInitState;
	EncryptKekAndPasswordWhenInitPtr_t pEncryptKekAndPasswordWhenInitVariables = gTcg.pAllFunctionStates->pEncryptKekAndPasswordWhenInitVariables;
	U8 ubContinue = TRUE;
	while (ubContinue) {
		ubContinue = FALSE;
		switch (EncryptKekAndPasswordWhenInitState) {
		case EncryptKekandPasswordWhenInitState_Initial:
			M_TCG_INFO_LOG(0x0013);
			TcgValidVariables(sizeof(EncryptKekAndPasswordWhenInit_t), &pEncryptKekAndPasswordWhenInitVariables);
			EncryptKekAndPasswordWhenInitState = EncryptKekAndPasswordWhenInitState_TriggerLoadCPin;
			pEncryptKekAndPasswordWhenInitVariables->ubKeyIdx = ubEkekIdx;
		// no break;
		case EncryptKekAndPasswordWhenInitState_TriggerLoadCPin:
			//Trigger Load
			TcgSetOpalTableEvent(OpalTableState_Load, TcgFlashTableOriginAdmin2Index, OPAL_NULL_TARGET_ADDR);
			if (gTcg.BufManager.OpalTableState == OpalTableState_Idle) {
				EncryptKekAndPasswordWhenInitState = EncryptKekAndPasswordWhenInitState_GetCPin;
				ubContinue = TRUE;
			}
			break;
		case EncryptKekAndPasswordWhenInitState_GetCPin: {
				TcgAdminPartial2Ptr_t pTcgApiStructAdminPartial2 = (TcgAdminPartial2Ptr_t)gTcg.BufManager.ulBufAddr;
				memcpy((void *)&pEncryptKekAndPasswordWhenInitVariables->MsidPin, (void *)&pTcgApiStructAdminPartial2->MsidPin, sizeof(TcgPin_t));
				pEncryptKekAndPasswordWhenInitVariables->ubSidPinLength = pTcgApiStructAdminPartial2->CPinTable[TCG_ADMIN_SP_CPIN_SID_TABLE_IDX].ubPinLength;
				EncryptKekAndPasswordWhenInitState = EncryptKekAndPasswordWhenInitState_TriggerUnloadCPin;
			}
		// no break;
		case EncryptKekAndPasswordWhenInitState_TriggerUnloadCPin:
			//Trigger Unload
			TcgSetOpalTableEvent(OpalTableState_Unload, TcgFlashTableOriginAdmin2Index, OPAL_NULL_TARGET_ADDR);
			if (gTcg.BufManager.OpalTableState == OpalTableState_Idle) {
				EncryptKekAndPasswordWhenInitState = EncryptKekAndPasswordWhenInitState_TriggerLoadAdmin3;
				ubContinue = TRUE;
			}
			break;
		case EncryptKekAndPasswordWhenInitState_TriggerLoadAdmin3:
			TcgSetOpalTableEvent(OpalTableState_Load, TcgFlashTableOriginAdmin3Index, OPAL_NULL_TARGET_ADDR);
			if (gTcg.BufManager.OpalTableState == OpalTableState_Idle) {
				EncryptKekAndPasswordWhenInitState = EncryptKekAndPasswordWhenInitState_SetEkekAndEpassword;
				ubContinue = TRUE;
			}
			break;
		case EncryptKekAndPasswordWhenInitState_SetEkekAndEpassword: {
				TcgEncryptedKek_t Ekek;
				TcgEpassword_t Epassword;
				TcgAdminPartial3Ptr_t pTcgApiStructAdminPartial3 = (TcgAdminPartial3Ptr_t)gTcg.BufManager.ulBufAddr;
				U8 ubi;
				U8 aubPIN[SECURITY_SHA_512_OUTPUT_LENGTH] = {0};
				U8 ubPINLength = 0;

				if ((TcgEncryptKekandPasswordWhenInitModeInit == ubMode) || (TcgEncryptKekandPasswordWhenInitModeRevertAdminSp == ubMode)) {
					memcpy((void *)aubPIN, (void *)&pEncryptKekAndPasswordWhenInitVariables->MsidPin.aubPin[0], pEncryptKekAndPasswordWhenInitVariables->MsidPin.ubLength);
					ubPINLength = pEncryptKekAndPasswordWhenInitVariables->MsidPin.ubLength;
				}
				else if (TcgEncryptKekandPasswordWhenInitModeRevertLockingSp == ubMode) {
					memcpy((void *)aubPIN, (void *)&gTcg.aubSecurityKey[0], TCG_SECURITY_KEY_SIZE);
					ubPINLength = TCG_SECURITY_KEY_SIZE;
					if (0 != pEncryptKekAndPasswordWhenInitVariables->ubSidPinLength) {
						gTcg.pVT->NonVolatile.btRevertLockingSpSIDTempEkek = TRUE;
					}
				}

				if (TRUE == M_TCG_OPAL_PYRITE2_AES_EN()) {
					M_FW_CRITICAL_ASSERT(ASSERT_TCG_0x0C13, (TRUE == gTcg.btKekExist));
				}
				// AdminSp SID
				if ((TcgEncryptKekandPasswordWhenInitModeInit == ubMode)
					|| (TcgEncryptKekandPasswordWhenInitModeRevertAdminSp == ubMode)
					|| (TcgEncryptKekandPasswordWhenInitModeRevertLockingSp == ubMode)) {

					if ((TcgEncryptKekandPasswordWhenInitModeInit == ubMode)
						|| (TcgEncryptKekandPasswordWhenInitModeRevertAdminSp == ubMode)) { //Revert AdminSp or TcgInit
						TcgEncryptKeyData(&aubPIN[0], ubPINLength, NULL, (U8 *)&Epassword, NULL, TcgEncryptKeyDataModePassword);
						memcpy((void *)&pTcgApiStructAdminPartial3->Epassword[M_TCG_ADMIN_SP_SID_EPASSWORD_IDX()], (void *)&Epassword, sizeof(TcgEpassword_t));
					}
					if (TRUE == M_TCG_OPAL_PYRITE2_AES_EN()) {
						TcgEncryptKeyData(&aubPIN[0], ubPINLength, NULL, (U8 *)&Ekek, (U8 *)&gTcg.Kek, TcgEncryptKeyDataModeKek);
						memcpy((void *)&pTcgApiStructAdminPartial3->Ekek[M_TCG_ADMIN_SP_SID_EKEK_IDX()], (void *)&Ekek, sizeof(TcgEncryptedKek_t));
					}
				}

				// LockingSp Admins and Users
				TcgEncryptKeyData(&gTcg.aubSecurityKey[0], TCG_SECURITY_KEY_SIZE, NULL, (U8 *)&Epassword, NULL, TcgEncryptKeyDataModePassword);
				if (TRUE == M_TCG_OPAL_PYRITE2_AES_EN()) {
					TcgEncryptKeyData(&gTcg.aubSecurityKey[0], TCG_SECURITY_KEY_SIZE, NULL, (U8 *)&Ekek, (U8 *)&gTcg.Kek, TcgEncryptKeyDataModeKek);
				}
				for (ubi = 0; ubi < M_TCG_GET_LOCKING_SP_CPIN_TABLE_LENGTH(); ubi++) {
					if (TRUE == M_TCG_SUPPORT_SINGLE_USER_MODE_FEATURE()) {
						// Condition 1:
						// Admin1's PIN is dependent on the presence and value of the Reactivate method's Admin1PIN parameter
						// Admin1's Ekek doesn't need to copy in this phase
						// Condition 2:
						// Copy Ekek for authority[ubEkekIdx] for Erase method
						if (((TcgEncryptKekandPasswordWhenInitModeReactivate == ubMode) && (TCG_LOCKING_SP_CPIN_ADMIN1_TABLE_IDX == ubi))
							|| ((TcgEncryptKekandPasswordWhenInitModeErase == ubMode) && (pEncryptKekAndPasswordWhenInitVariables->ubKeyIdx != ubi))) {
							continue;
						}
					}
					memcpy((void *)&pTcgApiStructAdminPartial3->Epassword[ubi], (void *)&Epassword, sizeof(TcgEpassword_t));
					if (TRUE == M_TCG_OPAL_PYRITE2_AES_EN()) {
						memcpy((void *)&pTcgApiStructAdminPartial3->Ekek[ubi], (void *)&Ekek, sizeof(TcgEncryptedKek_t));
					}
				}

				// AdminSp Admin1 (Use Securitykey to Encrypt)
				if ((TcgEncryptKekandPasswordWhenInitModeInit == ubMode) || (TcgEncryptKekandPasswordWhenInitModeRevertAdminSp == ubMode)) {
					memcpy((void *)&pTcgApiStructAdminPartial3->Epassword[M_TCG_ADMIN_SP_ADMIN1_EPASSWORD_IDX()], (void *)&Epassword, sizeof(TcgEpassword_t));
				}
			}
			EncryptKekAndPasswordWhenInitState = EncryptKekAndPasswordWhenInitState_TriggerSaveAdmin3;
		// no break;
		case EncryptKekAndPasswordWhenInitState_TriggerSaveAdmin3:
			//Trigger Save
			TcgSetOpalTableEvent(OpalTableState_Save, TcgFlashTableOriginAdmin3Index, OPAL_NULL_TARGET_ADDR);
			if (gTcg.BufManager.OpalTableState == OpalTableState_Idle) {
				EncryptKekAndPasswordWhenInitState = EncryptKekAndPasswordWhenInitState_Done;
				ubContinue = TRUE;
			}
			break;
		case EncryptKekAndPasswordWhenInitState_Done:
			TcgInvalidVariables(sizeof(EncryptKekAndPasswordWhenInit_t), &pEncryptKekAndPasswordWhenInitVariables);
			EncryptKekAndPasswordWhenInitState = EncryptKekandPasswordWhenInitState_Initial;
			break;
		}
	}
	gTcg.pAllFunctionStates->EncryptKekAndPasswordWhenInitState = EncryptKekAndPasswordWhenInitState;
	gTcg.pAllFunctionStates->pEncryptKekAndPasswordWhenInitVariables = pEncryptKekAndPasswordWhenInitVariables;
}

void TcgEncryptKekRangeToEkekRange(U8 Ubkey_index, U8 ubDontCareLockOnResetBMP)
{
	EncryptKekRangeState_t EncryptKekRangeState = gTcg.pAllFunctionStates->EncryptKekRangeState;
	EncryptKekRangeVariablesPtr_t pEncryptKekRangeVariables = gTcg.pAllFunctionStates->pEncryptKekRangeVariables;
	U8 ubContinue = TRUE;
	while (ubContinue) {
		ubContinue = FALSE;
		switch (EncryptKekRangeState) {
		case EncryptKekRangeState_Initial:
			M_TCG_INFO_LOG(0x0014);
			TcgValidVariables(sizeof(EncryptKekRangeVariables_t), &pEncryptKekRangeVariables);
			pEncryptKekRangeVariables->ubKeyIndex = Ubkey_index;
			if ((TCG_CARE_LOCK_ON_RESET_BMP == ubDontCareLockOnResetBMP) && (gTcg.pVT->NonVolatile.ulLockOnResetBmp & BIT(pEncryptKekRangeVariables->ubKeyIndex))) {
				EncryptKekRangeState = EncryptKekRangeState_Done;
			}
			else {
				EncryptKekRangeState = EncryptKekRangeState_TriggerLoadEkek;
			}
			ubContinue = TRUE;
			break;
		case EncryptKekRangeState_TriggerLoadEkek:
			//Trigger Load
			TcgSetOpalTableEvent(OpalTableState_Load, TcgFlashTableOriginAdmin1Index, OPAL_NULL_TARGET_ADDR);
			if (gTcg.BufManager.OpalTableState == OpalTableState_Idle) {
				EncryptKekRangeState = EncryptKekRangeState_SetEkek;
				ubContinue = TRUE;
			}
			break;
		case EncryptKekRangeState_SetEkek: {
				TcgEncryptedKek_t Ekek;
				TcgKek_t ZeroKek = {{0}};
				if (memcmp(&ZeroKek, &gTcg.pVT->KekRange[pEncryptKekRangeVariables->ubKeyIndex], sizeof(TcgKek_t)) == 0) {
					M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, 0);
				}
				TcgEncryptKeyData(&gTcg.aubSecurityKey[0], TCG_SECURITY_KEY_SIZE, NULL, (U8 *)&Ekek, (U8 *)&gTcg.pVT->KekRange[pEncryptKekRangeVariables->ubKeyIndex], TcgEncryptKeyDataModeKek);
				TcgAdminPartial1Ptr_t pTcgApiStructAdminPartial1 = (TcgAdminPartial1Ptr_t)gTcg.BufManager.ulBufAddr;
				memcpy((void *)&pTcgApiStructAdminPartial1->EkekRange[pEncryptKekRangeVariables->ubKeyIndex].ubKey[0], (void *)&Ekek, sizeof(TcgEncryptedKek_t));
			}
			EncryptKekRangeState = EncryptKekRangeState_TriggerSaveEkek;
		// no break;
		case EncryptKekRangeState_TriggerSaveEkek:
			//Trigger Save
			TcgSetOpalTableEvent(OpalTableState_Save, TcgFlashTableOriginAdmin1Index, OPAL_NULL_TARGET_ADDR);
			if (gTcg.BufManager.OpalTableState == OpalTableState_Idle) {
				EncryptKekRangeState = EncryptKekRangeState_Done;
				ubContinue = TRUE;
			}
			break;
		case EncryptKekRangeState_Done:
			TcgInvalidVariables(sizeof(EncryptKekRangeVariables_t), &pEncryptKekRangeVariables);
			EncryptKekRangeState = EncryptKekRangeState_Initial;
			break;
		}
	}
	gTcg.pAllFunctionStates->EncryptKekRangeState = EncryptKekRangeState;
	gTcg.pAllFunctionStates->pEncryptKekRangeVariables = pEncryptKekRangeVariables;
}

void TcgEncryptKeyData(U8 *pubPin, U32 ulPinLength, U8 *pubKeyEncryptionKey, U8 *pubEncryptedKeyData, U8 *pubKeyData, U8 ubMode)
{
	U8 aubKeyData[SIZE_32B] = {0};
	U8 aubKeyEncryptionKey[SIZE_32B] = {0};
	//do PBKDF2
	if ((TcgEncryptKeyDataModePassword == ubMode)
		|| (TcgEncryptKeyDataModePSIDPassword == ubMode)
		|| (TcgEncryptKeyDataModeKek == ubMode)) { //Mode Is Password or Kek
		U8 ubSaltPSIDMode = (TcgEncryptKeyDataModePSIDPassword == ubMode);
		TcgPBKDF2(pubPin, ulPinLength, TCG_PBKDF2_OUTPUT_LENGTH, TCG_PBKDF2_ITERATION_NUM, &aubKeyEncryptionKey[0], ubSaltPSIDMode);
	}

	if ((TcgEncryptKeyDataModePassword == ubMode) || (TcgEncryptKeyDataModePSIDPassword == ubMode)) {
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, ((NULL != pubPin) && (NULL == pubKeyEncryptionKey) && (NULL == pubKeyData)));
		U8 aubPasswordWrapString[SIZE_32B] = {"TheStringIsUsedToWrapForPassword"};
		memcpy((void *)&aubKeyData, (void *)aubPasswordWrapString, SIZE_32B);
	}
	else if (TcgEncryptKeyDataModeKek == ubMode) {
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, ((NULL != pubPin) && (NULL == pubKeyEncryptionKey) && (NULL != pubKeyData)));
		if (TRUE == M_TCG_OPAL_PYRITE2_AES_EN()) {
			memcpy((void *)&aubKeyData, (void *)pubKeyData, SIZE_32B);
		}
		else {
			return;
		}
	}
	else if (TcgEncryptKeyDataModeDek == ubMode) {
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, ((NULL == pubPin) && (NULL != pubKeyEncryptionKey) && (NULL != pubKeyData)));
		if (TRUE == M_TCG_AES_EN()) {
			memcpy((void *)&aubKeyData, (void *)pubKeyData, SIZE_32B);
			memcpy((void *)&aubKeyEncryptionKey, (void *)pubKeyEncryptionKey, sizeof(TcgKek_t));
		}
		else {
			return;
		}
	}

	TcgKeyWrap((U32 *)&aubKeyEncryptionKey[0], pubEncryptedKeyData, aubKeyData);
}

U8 TcgDecryptKeyData(U8 *pubPin, U32 ulPinLength, U8 *pubKeyEncryptionKey, U8 *pubEncryptedKeyData, U8 *pubKeyData, U8 ubMode)
{
	U8 ubResult = TCG_CORRECT_PIN;
	U8 aubKeyEncryptionKey[TCG_PBKDF2_OUTPUT_LENGTH];
	//do PBKDF2
	if ((TcgDecryptKeyDataModeEpassword == ubMode)
		|| (TcgDecryptKeyDataModePSIDEpassword == ubMode)
		|| (TcgDecryptKeyDataModeEkek == ubMode)) { //Mode Is Epassword or Ekek
		U8 ubSaltPSIDMode = (TcgDecryptKeyDataModePSIDEpassword == ubMode);
		TcgPBKDF2(pubPin, ulPinLength, TCG_PBKDF2_OUTPUT_LENGTH, TCG_PBKDF2_ITERATION_NUM, &aubKeyEncryptionKey[0], ubSaltPSIDMode);
	}

	if ((TcgDecryptKeyDataModeEpassword == ubMode) || (TcgDecryptKeyDataModePSIDEpassword == ubMode)) {
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, ((NULL != pubPin) && (NULL == pubKeyEncryptionKey) && (NULL != pubEncryptedKeyData)));
		U8 aubPassword[sizeof(TcgKek_t)] = {0};
		pubKeyData = aubPassword;
	}
	else if (TcgDecryptKeyDataModeEkek == ubMode) {
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, ((NULL != pubPin) && (NULL == pubKeyEncryptionKey) && (NULL != pubEncryptedKeyData)));
		if (FALSE == M_TCG_OPAL_PYRITE2_AES_EN()) {
			return 0;
		}
	}
	else if (TcgDecryptKeyDataModeEdek == ubMode) {
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, ((NULL == pubPin) && (NULL != pubKeyEncryptionKey) && (NULL != pubEncryptedKeyData)));
		if (TRUE == M_TCG_AES_EN()) {
			memcpy((void *)&aubKeyEncryptionKey, (void *)pubKeyEncryptionKey, sizeof(TcgKek_t));
		}
		else {
			return 0;
		}
	}

	ubResult = TcgKeyUnwrap((U32 *)&aubKeyEncryptionKey[0], pubEncryptedKeyData, pubKeyData, ubMode);
	return ubResult;
}

U32 TcgKeyIndexToLockingUidL(U8 ubKeyIndex)
{
	U32 Locking_range_uid_l;
	if (ubKeyIndex != 0) {
		Locking_range_uid_l = TcgLockingRange1UidL + ubKeyIndex - 1;
	}
	else {
		Locking_range_uid_l = TcgLockingGlobalRangeUidL;
	}
	return Locking_range_uid_l;
}

void TcgKeyWrap(U32 *pulKeyEncryptionKey, U8 *pubEncryptedKeyData, U8 *pubKeyData)
{
	U32 aulInitialVectorForEncryption[2] = {0xA6A6A6A6, 0xA6A6A6A6};
	memset((void *)(&gTcg.pVT->ubSecuritySrcBufAddr128[0]), 0x00, SIZE_128B);
	memset((void *)(&gTcg.pVT->ubSecurityDstBufAddr128[0]), 0x00, SIZE_128B);
	memcpy((void *)(&gTcg.pVT->ubSecuritySrcBufAddr128[0]), (void *)pubKeyData, sizeof(TcgKek_t));
	SecurityAESKeyWrap(SECURITY_AES_OFFLINE_KEY_MODE_256, pulKeyEncryptionKey, SECURITY_OFFLINE_KEY_INDEX, (const U32 *)(&gTcg.pVT->ubSecuritySrcBufAddr128[0]), (U32 *)(&gTcg.pVT->ubSecurityDstBufAddr128[0]), SECURITY_AES_OFFLINE_KEY_WRAP_N4_TEXT_LEN_256, aulInitialVectorForEncryption, SECURITY_AES_OFFLINE_ENCRYPT);
	memcpy((void *)pubEncryptedKeyData, (void *)(&gTcg.pVT->ubSecurityDstBufAddr128[0]), sizeof(TcgEncryptedKek_t));
}

U8 TcgKeyUnwrap(U32 *pulKeyEncryptionKey, U8 *pubEncryptedKeyData, U8 *pubKeyData, U8 ubMode)      // 64byte key, Ekek, dst_kek
{
	U32 aulInitialVectorForEncryption[2] = {0xA6A6A6A6, 0xA6A6A6A6};
	U32 aulInitialVectorForDecryption[2] = {0};
	memset((void *)(&gTcg.pVT->ubSecuritySrcBufAddr128[0]), 0x00, SIZE_128B);
	memset((void *)(&gTcg.pVT->ubSecurityDstBufAddr128[0]), 0x00, SIZE_128B);
	memcpy((void *)aulInitialVectorForDecryption, (void *)pubEncryptedKeyData, TCG_KEY_WRAP_IV_SIZE);
	memcpy((void *)(&gTcg.pVT->ubSecuritySrcBufAddr128[0]), (void *)(pubEncryptedKeyData + TCG_KEY_WRAP_IV_SIZE), (sizeof(TcgEncryptedKek_t) - TCG_KEY_WRAP_IV_SIZE));

	SecurityAESKeyWrap(SECURITY_AES_OFFLINE_KEY_MODE_256, pulKeyEncryptionKey, SECURITY_OFFLINE_KEY_INDEX, (const U32 *)(&gTcg.pVT->ubSecuritySrcBufAddr128[0]), (U32 *)(&gTcg.pVT->ubSecurityDstBufAddr128[0]), SECURITY_AES_OFFLINE_KEY_WRAP_N4_TEXT_LEN_256, aulInitialVectorForDecryption, SECURITY_AES_OFFLINE_DECRYPT);

	if (memcmp((void *)(&gTcg.pVT->ubSecurityDstBufAddr128[0]), (void *)aulInitialVectorForEncryption, TCG_KEY_WRAP_IV_SIZE)) {
		if ((TcgDecryptKeyDataModeEpassword == ubMode) || (TcgDecryptKeyDataModePSIDEpassword == ubMode)) {
			return TCG_WRONG_PIN;
		}
		else {
			M_FW_CRITICAL_ASSERT(ASSERT_TCG_0x0C14, FALSE);
		}
	}
	memcpy((void *)pubKeyData, (void *)(&gTcg.pVT->ubSecurityDstBufAddr128[0] + TCG_KEY_WRAP_IV_SIZE), sizeof(TcgKek_t));
	return TCG_CORRECT_PIN;
}

void TcgPyrite1SecurityKeyProtectDek(U8 ubAESDirection)
{
	Pyrite1SecurityKeyProtectDekState_t Pyrite1SecurityKeyProtectDekState = gTcg.pAllFunctionStates->Pyrite1SecurityKeyProtectDekState;
	U8 ubContinue = TRUE;
	while (ubContinue) {
		ubContinue = FALSE;
		switch (Pyrite1SecurityKeyProtectDekState) {
		case Pyrite1SecurityKeyProtectDekState_Initial:
			M_TCG_INFO_LOG(0x0099);
			Pyrite1SecurityKeyProtectDekState = Pyrite1SecurityKeyProtectDekState_TriggerLoadAdmin1;
		// no break;
		case Pyrite1SecurityKeyProtectDekState_TriggerLoadAdmin1:
			TcgSetOpalTableEvent(OpalTableState_Load, TcgFlashTableOriginAdmin1Index, OPAL_NULL_TARGET_ADDR);
			if (OpalTableState_Idle == gTcg.BufManager.OpalTableState) {
				Pyrite1SecurityKeyProtectDekState = Pyrite1SecurityKeyProtectDekState_AES;
				ubContinue = TRUE;
			}
			break;
		case Pyrite1SecurityKeyProtectDekState_AES: {
				TcgKek_t Kek;
				TcgDek_t Dek;
				TcgEncryptedDek_t Edek;
				TcgAdminPartial1Ptr_t pTcgApiStructAdminPartial1 = (TcgAdminPartial1Ptr_t)gTcg.BufManager.ulBufAddr;
				TcgKek_t ZeroKek = {{0}};
				TcgDek_t ZeroDek = {{0}};
				U8 ubi;
				memcpy((void *)&Kek, (void *)&gTcg.aubSecurityKey[0], sizeof(TcgKek_t)); //Only half of security key
				if (0 == memcmp(&ZeroKek, &Kek, sizeof(TcgKek_t))) {
					M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
				}
				if (SECURITY_AES_OFFLINE_DECRYPT == ubAESDirection) {
					memcpy((void *)&Edek, (void *)&pTcgApiStructAdminPartial1->Edek[SECURITY_GLOBAL_RANGE_KEY_IDX], sizeof(TcgEncryptedDek_t));
					U8 ubWrapNum = sizeof(TcgEncryptedDek_t) / TCG_KEY_WRAP_CIPHERTEXT_SIZE; //HW limit
					for (ubi = 0; ubi < ubWrapNum; ubi++) {
						TcgDecryptKeyData(NULL, 0, (U8 *)&Kek, (U8 *)((U32)(&Edek) + ubi * TCG_KEY_WRAP_CIPHERTEXT_SIZE), (U8 *)((U32)(&Dek) + ubi * TCG_KEY_WRAP_PLAINTEXT_SIZE), TcgDecryptKeyDataModeEdek);
					}
					memcpy((void *)&gTcg.pVT->Dek[SECURITY_GLOBAL_RANGE_KEY_IDX], (void *)&Dek, sizeof(TcgDek_t));
					Pyrite1SecurityKeyProtectDekState = Pyrite1SecurityKeyProtectDekState_Done;
				}
				else {
					memcpy((void *)&Dek, (void *)&gTcg.pVT->Dek[SECURITY_GLOBAL_RANGE_KEY_IDX], sizeof(TcgDek_t));
					if (0 == memcmp(&ZeroDek, &Dek, sizeof(TcgDek_t))) {
						M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
					}
					U8 ubWrapNum = sizeof(TcgDek_t) / TCG_KEY_WRAP_PLAINTEXT_SIZE; //HW limit
					for (ubi = 0; ubi < ubWrapNum; ubi++) {
						TcgEncryptKeyData(NULL, 0, (U8 *)&Kek, (U8 *)((U32)(&Edek) + ubi * TCG_KEY_WRAP_CIPHERTEXT_SIZE), (U8 *)((U32)(&Dek) + ubi * TCG_KEY_WRAP_PLAINTEXT_SIZE), TcgEncryptKeyDataModeDek);
					}
					memcpy((void *)&pTcgApiStructAdminPartial1->Edek, (void *)&Edek, sizeof(TcgEncryptedDek_t));
					Pyrite1SecurityKeyProtectDekState = Pyrite1SecurityKeyProtectDekState_TriggerSaveAdmin1;
				}
			}
			ubContinue = TRUE;
			break;
		case Pyrite1SecurityKeyProtectDekState_TriggerSaveAdmin1:
			TcgSetOpalTableEvent(OpalTableState_Save, TcgFlashTableOriginAdmin1Index, OPAL_NULL_TARGET_ADDR);
			if (OpalTableState_Idle == gTcg.BufManager.OpalTableState) {
				Pyrite1SecurityKeyProtectDekState = Pyrite1SecurityKeyProtectDekState_Done;
				ubContinue = TRUE;
			}
			break;
		case Pyrite1SecurityKeyProtectDekState_Done:
			Pyrite1SecurityKeyProtectDekState = Pyrite1SecurityKeyProtectDekState_Initial;
			break;
		}
	}
	gTcg.pAllFunctionStates->Pyrite1SecurityKeyProtectDekState = Pyrite1SecurityKeyProtectDekState;
}
#endif /* (TCG_EN && (PS5013_EN || PS5021_EN || (PS5017_EN && !BURNER_MODE_EN)) && !RDT_MODE_EN && !LPM3_LOADER && !BOOTLOADER_MODE_EN) */

#if (TCG_EN && !BURNER_MODE_EN && !RDT_MODE_EN && !LPM3_LOADER && !BOOTLOADER_MODE_EN)
void TcgDecryptEdekToDek(U32 ulLockingUidL, U8 ubRange, U8 ubDontCareLockOnResetBMP)
{
	DecryptEdekState_t DecryptEdekState = gTcg.pAllFunctionStates->DecryptEdekState;
	DecryptEdekVariablesPtr_t pDecryptEdekVariables = gTcg.pAllFunctionStates->pDecryptEdekVariables;
	U8 ubContinue = TRUE;
	while (ubContinue) {
		ubContinue = FALSE;
		switch (DecryptEdekState) {
		case DecryptEdekState_Initial:
			M_TCG_INFO_LOG(0x0015);
			TcgValidVariables(sizeof(DecryptEdekVariables_t), &pDecryptEdekVariables);
			pDecryptEdekVariables->ubKeyIndex = TcgLockingUidLToKeyIndex(ulLockingUidL);
			pDecryptEdekVariables->ubRange = ubRange;
			if (pDecryptEdekVariables->ubRange == OPAL_RANGE) {
				if ((TCG_CARE_LOCK_ON_RESET_BMP == ubDontCareLockOnResetBMP) && (gTcg.pVT->NonVolatile.ulLockOnResetBmp & BIT(pDecryptEdekVariables->ubKeyIndex))) {
					if (M_TCG_AES_BUG_WORKAROUND() && (SECURITY_GLOBAL_RANGE_KEY_IDX == pDecryptEdekVariables->ubKeyIndex)) { /* parasoft-suppress BD-PB-CC "Check by author. This is always false condition, when M_TCG_AES_BUG_WORKAROUND() == FALSE" */
						DecryptEdekState = DecryptEdekState_TriggerLoadEdek;
					}
					else {
						DecryptEdekState = DecryptEdekState_Done;
					}
				}
				else {
					DecryptEdekState = DecryptEdekState_TriggerLoadEdek;
				}
			}
			else {
				DecryptEdekState = DecryptEdekState_TriggerLoadEdek;
			}
			ubContinue = TRUE;
			break;
		case DecryptEdekState_TriggerLoadEdek:
			//Trigger Load
			TcgSetOpalTableEvent(OpalTableState_Load, TcgFlashTableOriginAdmin1Index, OPAL_NULL_TARGET_ADDR);
			if (gTcg.BufManager.OpalTableState == OpalTableState_Idle) {
				DecryptEdekState = DecryptEdekState_DecryptEdekAndSet;
				ubContinue = TRUE;
			}
			break;
		case DecryptEdekState_DecryptEdekAndSet: {
				TcgEncryptedDek_t Edek;
				TcgKek_t Kek;
				TcgDek_t Dek;
				TcgAdminPartial1Ptr_t pTcgApiStructAdminPartial1 = (TcgAdminPartial1Ptr_t)gTcg.BufManager.ulBufAddr;
				if (pDecryptEdekVariables->ubRange) {
					memcpy((void *)&Edek, (void *)&pTcgApiStructAdminPartial1->EdekRange[pDecryptEdekVariables->ubKeyIndex], sizeof(TcgEncryptedDek_t));
				}
				else {
					memcpy((void *)&Edek, (void *)&pTcgApiStructAdminPartial1->Edek[pDecryptEdekVariables->ubKeyIndex], sizeof(TcgEncryptedDek_t));
				}
				if (pDecryptEdekVariables->ubRange) {
					memcpy((void *)&Kek, (void *)&gTcg.pVT->KekRange[pDecryptEdekVariables->ubKeyIndex], sizeof(TcgKek_t));
				}
				else {
					memcpy((void *)&Kek, (void *)&gTcg.Kek, sizeof(TcgKek_t));
				}
				TcgKek_t ZeroKek = {{0}};
				if (memcmp(&ZeroKek, &Kek, sizeof(TcgKek_t)) == 0) {
					M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, 0);
				}
				U8 ubi;
				U8 ubWrapNum = sizeof(TcgEncryptedDek_t) / TCG_KEY_WRAP_CIPHERTEXT_SIZE; //HW limit
				for (ubi = 0; ubi < ubWrapNum; ubi++) {
					TcgDecryptKeyData(NULL, 0, (U8 *)&Kek, (U8 *)((U32)(&Edek) + ubi * TCG_KEY_WRAP_CIPHERTEXT_SIZE), (U8 *)((U32)(&Dek) + ubi * TCG_KEY_WRAP_PLAINTEXT_SIZE), TcgDecryptKeyDataModeEdek);
				}
				memcpy((void *)&gTcg.pVT->Dek[pDecryptEdekVariables->ubKeyIndex], (void *)&Dek, sizeof(TcgDek_t));
			}
			DecryptEdekState = DecryptEdekState_TriggerUnloadEdek;
		// no break;
		case DecryptEdekState_TriggerUnloadEdek:
			//Trigger Unload
			TcgSetOpalTableEvent(OpalTableState_Unload, TcgFlashTableOriginAdmin1Index, OPAL_NULL_TARGET_ADDR);
			if (gTcg.BufManager.OpalTableState == OpalTableState_Idle) {
				DecryptEdekState = DecryptEdekState_Done;
				ubContinue = TRUE;
			}
			break;
		case DecryptEdekState_Done:
			TcgInvalidVariables(sizeof(DecryptEdekVariables_t), &pDecryptEdekVariables);
			DecryptEdekState = DecryptEdekState_Initial;
			break;
		}
	}
	gTcg.pAllFunctionStates->DecryptEdekState = DecryptEdekState;
	gTcg.pAllFunctionStates->pDecryptEdekVariables = pDecryptEdekVariables;
}

void TcgDecryptAllEdekToDek(U8 ubKeepGlobalRangeKey, U8 ubRange, U8 ubDontCareLockOnResetBMP)
{
	DecryptAllEdekState_t DecryptAllEdekState = gTcg.pAllFunctionStates->DecryptAllEdekState;
	DecryptAllEdekVariablesPtr_t pDecryptAllEdekVariables = gTcg.pAllFunctionStates->pDecryptAllEdekVariables;
	U8 ubContinue = TRUE;
	while (ubContinue) {
		ubContinue = FALSE;
		switch (DecryptAllEdekState) {
		case DecryptAllEdekState_Initial:
			M_TCG_INFO_LOG(0x0016);
			TcgValidVariables(sizeof(DecryptAllEdekVariables_t), &pDecryptAllEdekVariables);
			if (ubKeepGlobalRangeKey) {
				pDecryptAllEdekVariables->ubKeyIndex = 1;
			}
			else {
				pDecryptAllEdekVariables->ubKeyIndex = 0;
			}
			pDecryptAllEdekVariables->ubRange = ubRange;
			DecryptAllEdekState = DecryptAllEdekState_Decrypt;
		// no break;
		case DecryptAllEdekState_Decrypt:
			TcgDecryptEdekToDek(TcgKeyIndexToLockingUidL(pDecryptAllEdekVariables->ubKeyIndex), pDecryptAllEdekVariables->ubRange, ubDontCareLockOnResetBMP);
			if (gTcg.pAllFunctionStates->DecryptEdekState == DecryptEdekState_Initial) {
				DecryptAllEdekState = DecryptAllEdekState_CheckContinue;
				ubContinue = TRUE;
			}
			break;
		case DecryptAllEdekState_CheckContinue:
			pDecryptAllEdekVariables->ubKeyIndex ++;
			if (pDecryptAllEdekVariables->ubKeyIndex < M_TCG_GET_AES_KEY_NUM()) { /* parasoft-suppress BD-PB-CC "In pyrite FW, it always evaluates to false" */
				DecryptAllEdekState = DecryptAllEdekState_Decrypt;
			}
			else {
				DecryptAllEdekState = DecryptAllEdekState_Done;
			}
			ubContinue = TRUE;
			break;
		case DecryptAllEdekState_Done:
			TcgInvalidVariables(sizeof(DecryptAllEdekVariables_t), &pDecryptAllEdekVariables);
			DecryptAllEdekState = DecryptAllEdekState_Initial;
			break;
		}
	}
	gTcg.pAllFunctionStates->DecryptAllEdekState = DecryptAllEdekState;
	gTcg.pAllFunctionStates->pDecryptAllEdekVariables = pDecryptAllEdekVariables;
}

void TcgDecryptAllEkekRangetoKekRange(U8 ubDontCareLockOnResetBMP)
{
	DecryptAllEkekRangeVariablesPtr_t pDecryptAllEkekRangeVariables = gTcg.pAllFunctionStates->pDecryptAllEkekRangeVariables;
	DecryptAllEkekRangeState_t DecryptAllEkekRangeState = gTcg.pAllFunctionStates->DecryptAllEkekRangeState;
	U8 ubContinue = TRUE;
	while (ubContinue) {
		ubContinue = FALSE;
		switch (DecryptAllEkekRangeState) {
		case DecryptAllEkekRangeState_Initial:
			M_TCG_INFO_LOG(0x0017);
			TcgValidVariables(sizeof(DecryptAllEkekRangeVariables_t), &pDecryptAllEkekRangeVariables);
			pDecryptAllEkekRangeVariables->ubKeyIndex = 0;
			DecryptAllEkekRangeState = DecryptAllEkekRangeState_Decrypt;
		// no break;
		case DecryptAllEkekRangeState_Decrypt:
			TcgDecryptEkekRangeToKekRange(pDecryptAllEkekRangeVariables->ubKeyIndex, ubDontCareLockOnResetBMP);
			if (gTcg.pAllFunctionStates->DecryptEkekRangeState == DecryptEkekRangeState_Initial) {
				DecryptAllEkekRangeState = DecryptAllEkekRangeState_CheckContinue;
				ubContinue = TRUE;
			}
			break;
		case DecryptAllEkekRangeState_CheckContinue:
			pDecryptAllEkekRangeVariables->ubKeyIndex ++;
			if (pDecryptAllEkekRangeVariables->ubKeyIndex <  M_TCG_GET_AES_KEY_NUM()) { /* parasoft-suppress BD-PB-CC "In pyrite FW, it always evaluates to false" */
				DecryptAllEkekRangeState = DecryptAllEkekRangeState_Decrypt;
			}
			else {
				DecryptAllEkekRangeState = DecryptAllEkekRangeState_Done;
			}
			ubContinue = TRUE;
			break;
		case DecryptAllEkekRangeState_Done:
			TcgInvalidVariables(sizeof(DecryptAllEkekRangeVariables_t), &pDecryptAllEkekRangeVariables);
			DecryptAllEkekRangeState = DecryptAllEkekRangeState_Initial;
			break;
		}
	}
	gTcg.pAllFunctionStates->DecryptAllEkekRangeState = DecryptAllEkekRangeState;
	gTcg.pAllFunctionStates->pDecryptAllEkekRangeVariables = pDecryptAllEkekRangeVariables;
}

void TcgDecryptEkekRangeToKekRange(U32 ulKeyIndex, U8 ubDontCareLockOnResetBMP)
{
	DecryptEkekRangeVariablesPtr_t pDecryptEkekRangeVariables = gTcg.pAllFunctionStates->pDecryptEkekRangeVariables;
	DecryptEkekRangeState_t DecryptEkekRangeState = gTcg.pAllFunctionStates->DecryptEkekRangeState;
	U8 ubContinue = TRUE;
	while (ubContinue) {
		ubContinue = FALSE;
		switch (DecryptEkekRangeState) {
		case DecryptEkekRangeState_Initial:
			M_TCG_INFO_LOG(0x0018);
			TcgValidVariables(sizeof(DecryptEkekRangeVariables_t), &pDecryptEkekRangeVariables);
			pDecryptEkekRangeVariables->ulKeyIndex = ulKeyIndex;
			if ((TCG_CARE_LOCK_ON_RESET_BMP == ubDontCareLockOnResetBMP) && (gTcg.pVT->NonVolatile.ulLockOnResetBmp & BIT(pDecryptEkekRangeVariables->ulKeyIndex))) {
				if (M_TCG_AES_BUG_WORKAROUND() && (SECURITY_GLOBAL_RANGE_KEY_IDX == pDecryptEkekRangeVariables->ulKeyIndex)) { /* parasoft-suppress BD-PB-CC "Check by author. This is always false condition, when M_TCG_AES_BUG_WORKAROUND() == FALSE" */
					DecryptEkekRangeState = DecryptEkekRangeState_TriggerLoadEkekRange;
				}
				else {
					DecryptEkekRangeState = DecryptEkekRangeState_Done;
				}
			}
			else {
				DecryptEkekRangeState = DecryptEkekRangeState_TriggerLoadEkekRange;
			}
			ubContinue = TRUE;
			break;
		case DecryptEkekRangeState_TriggerLoadEkekRange:
			//Trigger Load
			TcgSetOpalTableEvent(OpalTableState_Load, TcgFlashTableOriginAdmin1Index, OPAL_NULL_TARGET_ADDR);
			if (gTcg.BufManager.OpalTableState == OpalTableState_Idle) {
				DecryptEkekRangeState = DecryptEkekRangeState_DecryptEkekRangeAndSet;
				ubContinue = TRUE;
			}
			break;
		case DecryptEkekRangeState_DecryptEkekRangeAndSet: {
				TcgKek_t Kek;
				TcgAdminPartial1Ptr_t pTcgApiStructAdminPartial1 = (TcgAdminPartial1Ptr_t)gTcg.BufManager.ulBufAddr;
				TcgDecryptKeyData(&gTcg.aubSecurityKey[0], TCG_SECURITY_KEY_SIZE, NULL, (U8 *)&pTcgApiStructAdminPartial1->EkekRange[pDecryptEkekRangeVariables->ulKeyIndex], (U8 *)&Kek, TcgDecryptKeyDataModeEkek);
				memcpy((void *)&gTcg.pVT->KekRange[pDecryptEkekRangeVariables->ulKeyIndex], (void *)&Kek, sizeof(TcgKek_t));
			}
			DecryptEkekRangeState = DecryptEkekRangeState_TriggerUnloadEkekRange;
		// no break;
		case DecryptEkekRangeState_TriggerUnloadEkekRange:
			//Trigger Unload
			TcgSetOpalTableEvent(OpalTableState_Unload, TcgFlashTableOriginAdmin1Index, OPAL_NULL_TARGET_ADDR);
			if (gTcg.BufManager.OpalTableState == OpalTableState_Idle) {
				DecryptEkekRangeState = DecryptEkekRangeState_Done;
				ubContinue = TRUE;
			}
			break;
		case DecryptEkekRangeState_Done:
			TcgInvalidVariables(sizeof(DecryptEkekRangeVariables_t), &pDecryptEkekRangeVariables);
			DecryptEkekRangeState = DecryptEkekRangeState_Initial;
			break;
		}
	}
	gTcg.pAllFunctionStates->pDecryptEkekRangeVariables = pDecryptEkekRangeVariables;
	gTcg.pAllFunctionStates->DecryptEkekRangeState = DecryptEkekRangeState;
}
#endif /* (TCG_EN && !BURNER_MODE_EN && !RDT_MODE_EN && !LPM3_LOADER && !BOOTLOADER_MODE_EN) */
