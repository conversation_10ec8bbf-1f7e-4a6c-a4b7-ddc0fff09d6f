/**************************************************************************/
/*                                                                        */
/*           Copyright (c) 2000-2017 by Phison Electronics Corp.          */
/*                                                                        */
/**************************************************************************/

/**************************************************************************/
/*                                                                        */
/*  FILE : shr_hal_pcie_reg.h                     PROJECT : PS5011         */
/*                                                                        */
/*                                                                        */
/*  DESCRIPTION                                                           */
/*                                                                        */
/*    This is the header file for PS5011 HW memory mapping definition     */
/*                                                                        */
/*  RELEASE HISTORY                                                       */
/*                                                                        */
/*    DATE              NAME                DESCRIPTION                   */
/*                                                                        */
/*  2017-04-26      Owen Wang              Initial Version 1.0            */
/*                                                                        */
/**************************************************************************/

#if (NVME == HOST_MODE)
#ifndef _SHR_HAL_PCIE_REG_H_
#define _SHR_HAL_PCIE_REG_H_


// goodid
#if 0 //merge@@
#include "misc/shr_datatype.h"
#include "misc/shr_def.h"

//#include "misc/shr_config.h"
#else
#include "common/typedef.h"
#include "common/symbol.h"
#include "common/math_op.h"
#include "cpu/cpu_api.h"
//#include "hal/sys/sys_config_info.h"
#endif

#if 0 //merge@@
#define PCIE_CFGREGISTER_ADDRESS       (PCIE_CFGREGISTER_ADDRESS)
#define PCIE_PLDA_REGISTER_ADDRESS    (PCIE_PLDA_REGISTER_ADDRESS)
#define  MPHY_REGISTER_ADDRESS           (MPHY_REGISTER_ADDRESS)
#else
#define PCIE_CFGREGISTER_ADDRESS       (PCIE_CFGREG_ADDRESS)
#define PCIE_PLDA_REGISTER_ADDRESS   (PCIE_PLDA_REG_ADDRESS)
#define MPHY_REGISTER_ADDRESS           (PCIE2_REG_ADDRESS)
#endif

/*===============================  PLDA CTRL Registers =============================*/

#define r8_PCIE_PLDA                   ((REG8  *)PCIE_PLDA_REGISTER_ADDRESS)
#define r16_PCIE_PLDA                  ((REG16 *)PCIE_PLDA_REGISTER_ADDRESS)
#define r32_PCIE_PLDA                  ((REG32 *)PCIE_PLDA_REGISTER_ADDRESS)
#define r64_PCIE_PLDA                  ((REG64 *)PCIE_PLDA_REGISTER_ADDRESS)

#define HOST_TOOL_OFFSET                   0xFE0
#define HOST_TOOL_ADDR                     PCIE_PLDA_REGISTER_ADDRESS + HOST_TOOL_OFFSET

#define CFGCTRL_W32                    (0x84 >> 2)
#define 	PCIE_CFG_NOT_RDY               BIT0

#define     SET_PCIE_CFG_NOT_RDY()         do {\
	CPUARMSetCriticalRegisterBit((r32_PCIE_PLDA + CFGCTRL_W32), PCIE_CFG_NOT_RDY);\
} while(0)
#define     CLR_PCIE_CFG_NOT_RDY()         do {\
	CPUARMClearCriticalRegisterBit((r32_PCIE_PLDA + CFGCTRL_W32), PCIE_CFG_NOT_RDY);\
} while(0)
#define     GET_PCIE_CFG_NOT_RDY()         (r32_PCIE_PLDA[CFGCTRL_W32] & PCIE_CFG_NOT_RDY)


#define PCI_IDS_W32                    (0x98 >> 2)
#define PCI_VID_W16                    (0x98 >> 1)
#define PCI_DID_W16                    (0x9A >> 1)
#define PCI_REVID_W8                   (0x9C)
#define PCI_SSVID_W16                  (0xA0 >> 1)
#define PCI_SSDID_W16                  (0xA2 >> 1)

#define     SET_PCIE_PCI_VID(N)         (r16_PCIE_PLDA[PCI_VID_W16] |= N)
#define     CLR_PCIE_PCI_VID()         (r16_PCIE_PLDA[PCI_VID_W16] &= (~ BITMSK(16,0)))
#define     SET_PCIE_PCI_DID(N)         (r16_PCIE_PLDA[PCI_DID_W16] |= N)
#define     CLR_PCIE_PCI_DID()         (r16_PCIE_PLDA[PCI_DID_W16] &= (~ BITMSK(16,0)))
#define     SET_PCIE_PCI_SSVID(N)         (r16_PCIE_PLDA[PCI_SSVID_W16] |= N)
#define     CLR_PCIE_PCI_SSVID()         (r16_PCIE_PLDA[PCI_SSVID_W16] &= (~ BITMSK(16,0)))
#define     SET_PCIE_PCI_SSDID(N)         (r16_PCIE_PLDA[PCI_SSDID_W16] |= N)
#define     CLR_PCIE_PCI_SSDID()         (r16_PCIE_PLDA[PCI_SSDID_W16] &= (~ BITMSK(16,0)))
#define     SET_PCIE_PCI_RID(N)         (r8_PCIE_PLDA[PCI_REVID_W8] |= N)
#define     CLR_PCIE_PCI_RID()         (r8_PCIE_PLDA[PCI_REVID_W8] &= (~ BITMSK(8,0)))


#define PCI_IDS_0x9C                   (0x9C >> 2)
#define RID(x)                         ((U32)(x) & 0xFF)
#define CC(x)                          (((U32)(x) & 0x00FFFFFF) << 8)
#define PI(x)                          (((U32)(x) & 0xFF) << 8)
#define AHCIHBA                        0x01
#define NVMHCI                         0x02
#define SCC(x)                         (((U32)(x) & 0xFF) << 16)
#define SATAD                          0x06
#define NVMC                           0x08
#define BCC(x)                         (((U32)(x) & 0xFF) << 24)
#define MSC                            0x01


#define PLDAL_PCI_LPM_0xA4             (0xA4 >> 2)
#define D1_SPPORT                      BIT25
#define D2_SPPORT                      BIT26
#define PME_SUPPORT(x)                 (((U32)(x) & 0x1F) << 27)

#define PLDAL_PCI_IRQ_0xA8             (0xA8 >> 2)
#define INTPIN(x)                      ((U32)(x) & 0x03)
#define INTA                           1
#define INTB                           2
#define INTC                           3
#define INTD                           4
#define MSGNUM(x)                      (((U32)(x) & 0x07) << 4)
#define PVM                            BIT7
#define TABLESIZE(x)                   (((U32)(x) & 0x07FF) << 16)
#define GET_TABLESIZE()                ((r32_PCIE_PLDA[PLDAL_PCI_IRQ_0xA8] & BITMSK(11,16)) >> 16 )



#define MSIX                           BIT31

#define PLDAL_PCI_IRQ_0xAC             (0xAC >> 2)
#define TABLEBIR(x)                    ((U32)(x) & 0x07)
#define TABLEOFFSET(x)                 ((U32)(x) & 0xFFFFFFF8) // Qword-aligned

#define PLDAL_PCI_IRQ_0xB0             (0xB0 >> 2)
#define PBABIR(x)                      ((U32)(x) & 0x07)
#define PBAOFFSET(x)                   ((U32)(x) & 0xFFFFFFF8) // Qword-aligned

#define PLDAL_PEX_DEV_0xC0             (0xC0 >> 2)
#define L0S_ACCEPTABLE_LAT(x)          (((U32)(x) & 0x7) << 6)
#define L1_ACCEPTABLE_LAT(x)           (((U32)(x) & 0x7) << 9)
#define CLR_MAXPAYLOAD_SIZE_256B()	   (r32_PCIE_PLDA[PLDAL_PEX_DEV_0xC0] &= (~BITMSK(3,0)))  // 0: 128
#define PCIE_MAXPAYLOADSIZE_128B        (BIT0)
#define PCIE_MAXPAYLOADSIZE_256B        (BIT1)

#define PLDAL_PEX_LINK_0xC8            (0xC8 >> 2)
#define ASPM_L0S_SUP                   BIT10
#define ASPM_L1_SUP                    BIT11
#define CLK_PM_SUP                     BIT18

#define SET_ASPM_L0S_SUP()             (r32_PCIE_PLDA[PLDAL_PEX_LINK_0xC8]|=ASPM_L0S_SUP)
#define CLR_ASPM_L0S_SUP()             (r32_PCIE_PLDA[PLDAL_PEX_LINK_0xC8]&=~ASPM_L0S_SUP)
#define SET_ASPM_L1_SUP()              (r32_PCIE_PLDA[PLDAL_PEX_LINK_0xC8]|=ASPM_L1_SUP)
#define CLR_ASPM_L1_SUP()              (r32_PCIE_PLDA[PLDAL_PEX_LINK_0xC8]&=~ASPM_L1_SUP)
#define SET_CLK_PM_SUP()               (r32_PCIE_PLDA[PLDAL_PEX_LINK_0xC8]|=CLK_PM_SUP)
#define CLR_CLK_PM_SUP()               (r32_PCIE_PLDA[PLDAL_PEX_LINK_0xC8]&=~CLK_PM_SUP)

#define GET_PORT_NUM()                 ((r32_PCIE_PLDA[PLDAL_PEX_LINK_0xC8] & BITMSK(8,24)) >> 24 )


#define PLDAL_PEX_SPC_0xD4             (0xD4 >> 2)
#define SLOT_CLK_CFG                   BIT13   // 0:independent , 1:refclk


#define PLDAL_PEX_SPC2_0xD8            (0xD8 >> 2)
#define ECRC_GEN_SUP                   BIT1
#define ECRC_CHECK_SUP                 BIT2
#define AER_MSG_NUM(x)                 (((U32)(x) & 0x1F) << 3)
#define MSI_MSG_NUM(x)                 (((U32)(x) & 0x1F) << 8)
#define ASPM_L0S_ENT_DELAY(x)          (((U32)(x) & 0x1F) << 13)
#define ASPM_L1_ENT_DELAY(x)           (((U32)(x) & 0x1F) << 18)


#define SET_ECRC_GEN_SUP()             (r32_PCIE_PLDA[PLDAL_PEX_SPC2_0xD8]|=ECRC_GEN_SUP)
#define CLR_ECRC_GEN_SUP()             (r32_PCIE_PLDA[PLDAL_PEX_SPC2_0xD8]&=~ECRC_GEN_SUP)
#define SET_ECRC_CHECK_SUP()           (r32_PCIE_PLDA[PLDAL_PEX_SPC2_0xD8]|=ECRC_CHECK_SUP)
#define CLR_ECRC_CHECK_SUP()           (r32_PCIE_PLDA[PLDAL_PEX_SPC2_0xD8]&=~ECRC_CHECK_SUP)
#define CLR_ASPM_L0S_ENT_DELAY()       (r32_PCIE_PLDA[PLDAL_PEX_SPC2_0xD8]&=(~(ASPM_L0S_ENT_DELAY(0x1F))))
#define SET_ASPM_L0S_ENT_DELAY(x)      (r32_PCIE_PLDA[PLDAL_PEX_SPC2_0xD8]|=ASPM_L0S_ENT_DELAY(x))
#define CLR_ASPM_L1_ENT_DELAY()        (r32_PCIE_PLDA[PLDAL_PEX_SPC2_0xD8]&=(~(ASPM_L1_ENT_DELAY(0x1F))))
#define SET_ASPM_L1_ENT_DELAY(x)       (r32_PCIE_PLDA[PLDAL_PEX_SPC2_0xD8]|=ASPM_L1_ENT_DELAY(x))


#define PLDAL_L1SS_CAP_0xE0            (0xE0 >> 2)
#define PCI_PM_L1_2_SUP                BIT0
#define PCI_PM_L1_1_SUP                BIT1
#define ASPM_L1_2_SUP                  BIT2
#define ASPM_L1_1_SUP                  BIT3
#define L1_PM_SUBSTATE_SUP             BIT4
#define T_POWEROFF(x)                 (((U32)(x) & 0x07) << 5)
#define PORT_COMMN_RSTO_TIME(x)       (((U32)(x) & 0xFF) << 8)
#define T_POWER_ON_SCALE(x)           (((U32)(x) & 0x03) << 16)
#define T_POWER_ON_VALUE(x)           (((U32)(x) & 0x1F) << 19)
#define CLR_PCI_PM_L1_2_SUP()         r32_PCIE_PLDA[PLDAL_L1SS_CAP_0xE0] &= (~PCI_PM_L1_2_SUP);
#define CLR_PCI_PM_L1_1_SUP()         r32_PCIE_PLDA[PLDAL_L1SS_CAP_0xE0] &= (~PCI_PM_L1_1_SUP);
#define CLR_ASPM_L1_2_SUP()           r32_PCIE_PLDA[PLDAL_L1SS_CAP_0xE0] &= (~ASPM_L1_2_SUP);
#define CLR_ASPM_L1_1_SUP()           r32_PCIE_PLDA[PLDAL_L1SS_CAP_0xE0] &= (~ASPM_L1_1_SUP);


#define BAR0_W32                      (0xE4 >> 2)
#define BAR1_W32                      (0xE8 >> 2)
#define BAR2_W32                      (0xEC >> 2)
#define BAR3_W32                      (0xF0 >> 2)
#define BAR4_W32                      (0xF4 >> 2)
#define BAR5_W32                      (0xF8 >> 2)

#define PLDAL_SRIOV_0x120             (0x120 >> 2)
#define VF_ID(x)     			      ((U32)(x) & 0xFFFF)
#define VF_SUB_ID(x)   				  (((U32)(x) & 0xFFFF) << 16)

#define PCIE_PLDA_ISTATUS_LOCAL                 (0x184 >> 2)

#define M_PCIE_GET_PLDA_ISTATUS_LOCAL()         (r32_PCIE_PLDA[PCIE_PLDA_ISTATUS_LOCAL])
// To avoid register be modified after you read, you should write what you read
#define M_PCIE_CLR_PLDA_ISTATUS_LOCAL(x)        (r32_PCIE_PLDA[PCIE_PLDA_ISTATUS_LOCAL] =x)

#define PCIE_OUTSTANDING_STATUS                 (0x1D4 >> 2)
#define OUTSTANDING_STATUS_MASK                 (0x00330033)
#define M_PCIE_GET_OUTSTANDING_STATUS()         (r32_PCIE_PLDA[PCIE_OUTSTANDING_STATUS] & OUTSTANDING_STATUS_MASK)

#define PCIE_MST0_STATUS                        (0x1D8)
#define M_PCIE_GET_MST0_STATUS()                (r8_PCIE_PLDA[PCIE_MST0_STATUS])

#define PCIE_SLV0_STATUS                        (0x1E0)
#define M_PCIE_GET_SLV0_STATUS()                (r8_PCIE_PLDA[PCIE_SLV0_STATUS])

#define PCIE_SLV1_STATUS                        (0x1E4)
#define M_PCIE_GET_SLV1_STATUS()                (r8_PCIE_PLDA[PCIE_SLV1_STATUS])

INLINE U32 PCIEGetAxiStatus(void)
{
	U32 ulPcieAxiStatusTmp = M_PCIE_GET_OUTSTANDING_STATUS();
	ulPcieAxiStatusTmp = ( (ulPcieAxiStatusTmp | (ulPcieAxiStatusTmp >> 14))&BITMSK(8, 0) );
	ulPcieAxiStatusTmp = ( ulPcieAxiStatusTmp | (((U32)M_PCIE_GET_MST0_STATUS()) << 8) | (((U32)M_PCIE_GET_SLV0_STATUS()) << 16) | (((U32)M_PCIE_GET_SLV1_STATUS()) << 24) );
	return ulPcieAxiStatusTmp;
}

#define PCIE_PLDA_LTR_VALUES          (0x1A4 >> 2)
#define SNOOP_VALUE(x)     			  ((U32)(x) & 0x03FF)
#define SNOOP_SCALE(x)     			  (((U32)(x) & 0x07) << 10)
#define SNOOP_REQUIRE                 BIT15
#define NO_SNOOP_VALUE(x)     		  (((U32)(x) & 0x03FF) << 16)
#define NO_SNOOP_SCALE(x)     		  (((U32)(x) & 0x07) << 26)
#define NO_SNOOP_REQUIRE              BIT31

#if (PS5021_EN)
#define R32_PCIE_PLDA_A2P_WRCPL_TIMEOUT     (0x340 >> 2)
#endif /*(PS5021_EN)*/

#define PCIE_FW_HOST_TOOL_0x38      (0xFF8 >> 2)
#define PCIE_FW_HOST_TOOL_0x3C      (0xFFC >> 2)

#define SET_FW_REC_RXTS(x)          (r32_PCIE_PLDA[PCIE_FW_HOST_TOOL_0x38]|=(x<<0))
#define SET_FW_REC_RXERR(x)         (r32_PCIE_PLDA[PCIE_FW_HOST_TOOL_0x38]|=(x<<8))

#define CLR_FW_REC_RXTS()          (r32_PCIE_PLDA[PCIE_FW_HOST_TOOL_0x38] &= 0xFFFFFF00)
#define CLR_FW_REC_RXERR()         (r32_PCIE_PLDA[PCIE_FW_HOST_TOOL_0x38] &= 0xFFFF00FF)


#define PCIE_CFG_HOST_TOOL_0x3C      (0x153C >> 2)
#define GET_CFG_HOST_TOOL_0x3C(x)    (x=r32_PCIE_PLDA[PCIE_CFG_HOST_TOOL_0x3C])
#define CLR_CFG_HOST_TOOL_0x3C()     (r32_PCIE_PLDA[PCIE_CFG_HOST_TOOL_0x3C]=0)



/*================= PCI Configuration Space (Normal start from 0x80)  ===================*/

#define r8_PCIE_CFG                    ((REG8  *)PCIE_CFGREGISTER_ADDRESS)
#define r16_PCIE_CFG                   ((REG16 *)PCIE_CFGREGISTER_ADDRESS)
#define r32_PCIE_CFG                   ((REG32 *)PCIE_CFGREGISTER_ADDRESS)
#define r64_PCIE_CFG                   ((REG64 *)PCIE_CFGREGISTER_ADDRESS)

#define PCI_HEADER_CMD_W32              ((0x04) >> 2)
#define PCI_HEADER_CMD_W16              ((0x04) >> 1)
#define 	IOSE                        BIT0
#define 	MSE                         BIT1
#define 	BME                         BIT2
#define M_SET_PCIE_BME_OFF()            (r16_PCIE_CFG[PCI_HEADER_CMD_W16] &= ~BME)  // Turn off BME)

#define PCIEL_MSICAP                    (0xE0 >> 2)

#define GET_PCIE_MSI_EN()               (r32_PCIE_CFG[PCIEL_MSICAP] & BIT16)
#define GET_PCIE_MSI_MME()              ((r32_PCIE_CFG[PCIEL_MSICAP] & BITMSK(3,20)) >> 20 )
#define GET_PCIE_MSI_MMC()              ((r32_PCIE_CFG[PCIEL_MSICAP] & BITMSK(3,17)) >> 17 )

#define PCIEL_MSIXCAP                   (0xD0 >> 2)

#define GET_PCIE_MSIX_EN()              (r32_PCIE_CFG[PCIEL_MSIXCAP] & BIT31)
#define GET_PCIE_MSIX_NUM()             ((r32_PCIE_CFG[PCIEL_MSIXCAP] & BITMSK(11,16)) >> 16 )

#define PCIE_PCIECAP_LINK_CONTROL	                    (0x90 >> 2)
#define M_PCIE_GET_PCIECAP_LINK_CONTROL(x)              (x=r32_PCIE_CFG[PCIE_PCIECAP_LINK_CONTROL])

#define PCIE_PCIECAP_LINK_STATUS	                    (0x92)
#define SLOT_CLOCK_CONFIG                               BIT12

#define PCI_PXDC2_W16                                   ((0xA8) >> 1)
#define CTD                                             BIT4
#define M_SET_PCIE_CTD_OFF()                            (r16_PCIE_CFG[PCI_PXDC2_W16] &= ~CTD)  // Turn off CTD)

#define PCIE_PCIECAP_LINK_CONTROL_2                     (0xB0 >> 2)
#define M_PCIE_GET_PCIECAP_LINK_CONTROL_2(x)            (x=r32_PCIE_CFG[PCIE_PCIECAP_LINK_CONTROL_2])
#define M_PCIE_SET_PCIECAP_LINK_CONTROL_2(x)            (r32_PCIE_CFG[PCIE_PCIECAP_LINK_CONTROL_2]=x)

// Link Status 2 Register (Offset 32h)
#define R16_PCIE_PCIECAP_LINK_STATUS_2                  (0xB2 >> 1) // 0x80 + 0x32
#define     GEN3_EQUALIZATION_DONE_BIT                  (BIT1) // Equalization 8.0 GT/s Complete
#define M_CHECK_GEN3_EQUALIZATION_DONE()                (GEN3_EQUALIZATION_DONE_BIT == (r16_PCIE_CFG[R16_PCIE_PCIECAP_LINK_STATUS_2] & GEN3_EQUALIZATION_DONE_BIT))

#define PCIE_AERCAP_UNCORRECTABLE_STATUS                (0x204 >> 2)
#define M_PCIE_GET_AERCAP_UNCORRECTABLE()               (r32_PCIE_CFG[PCIE_AERCAP_UNCORRECTABLE_STATUS] & BITMSK(26,0))

#define PCIE_AERCAP_UNCORRECTABLE_MASK                  (0x208 >> 2)
#define M_PCIE_GET_AERCAP_UNCORRECTABLE_MASK(x)         (x=r32_PCIE_CFG[PCIE_AERCAP_UNCORRECTABLE_MASK])
#define M_PCIE_SET_AERCAP_UNCORRECTABLE_MASK(x)         (r32_PCIE_CFG[PCIE_AERCAP_UNCORRECTABLE_MASK]=x)

#define PCIE_AERCAP_UNCORRECTABLE_SEVERITY              (0x20c >> 2)
#define M_PCIE_GET_AERCAP_UNCORRECTABLE_SEVERITY(x)     (x=r32_PCIE_CFG[PCIE_AERCAP_UNCORRECTABLE_SEVERITY])
#define M_PCIE_SET_AERCAP_UNCORRECTABLE_SEVERITY(x)     (r32_PCIE_CFG[PCIE_AERCAP_UNCORRECTABLE_SEVERITY]=x)

#define PCIE_AERCAP_CORRECTABLE_STATUS                  (0x210 >> 2)
#define M_PCIE_GET_AERCAP_CORRECTABLE()                 (((r32_PCIE_CFG[PCIE_AERCAP_CORRECTABLE_STATUS] & BITMSK(4,12)) << 16) | ((r32_PCIE_CFG[PCIE_AERCAP_CORRECTABLE_STATUS] & BIT8) << 19))
#define M_PCIE_GET_AERCAP_STATUS()                      (M_PCIE_GET_AERCAP_UNCORRECTABLE()| M_PCIE_GET_AERCAP_CORRECTABLE())

#define PCIE_AERCAP_CORRECTABLE_MASK                    (0x214 >> 2)
#define M_PCIE_GET_AERCAP_CORRECTABLE_MASK(x)           (x=r32_PCIE_CFG[PCIE_AERCAP_CORRECTABLE_MASK])
#define M_PCIE_SET_AERCAP_CORRECTABLE_MASK(x)           (r32_PCIE_CFG[PCIE_AERCAP_CORRECTABLE_MASK]=x)

#define PCIE_AERCAP_ADVANCED_ERROR                      (0x218 >> 2)
#define M_PCIE_GET_AERCAP_ADVANCED_ERROR(x)             (x=r32_PCIE_CFG[PCIE_AERCAP_ADVANCED_ERROR])
#define M_PCIE_SET_AERCAP_ADVANCED_ERROR(x)             (r32_PCIE_CFG[PCIE_AERCAP_ADVANCED_ERROR]=x)

/*================================= MISC Registers =================================*/

#define r8_PCIE_MISC                  ((REG8  *)MPHY_REGISTER_ADDRESS)
#define r16_PCIE_MISC                 ((REG16 *)MPHY_REGISTER_ADDRESS)
#define r32_PCIE_MISC                 ((REG32 *)MPHY_REGISTER_ADDRESS)
#define r64_PCIE_MISC                 ((REG64 *)MPHY_REGISTER_ADDRESS)

#if (PS5021_EN)
#define MISC_OFFSET                   0x0
#else /*(PS5021_EN)*/
#define MISC_OFFSET                   0x800
#endif /*(PS5021_EN)*/

#define RESET_CTRL_W32                ((MISC_OFFSET + 0x00) >> 2)
#define 	SW_PWROK                        BIT0
#define 	SW_RESETN                       BIT1
#define 	SW_PERSTN                       BIT2
#define 	CR_PL_SRST                      BIT4
#define 	CR_TL_SRST                      BIT5
#define 	CR_TL_CRST                      BIT6
#define 	CR_BR_SRST                      BIT7
#define 	CR_SLVL_ASRST                   BIT8
#define 	CR_MSTL_ASRST                   BIT9
#define 	CR_MST0_ASRST                   BIT10
#define 	CR_SLV0_ASRST                   BIT11
#define 	CR_SLV1_ASRST                   BIT12
#define 	CR_BR_AXI_ASRST                 BIT13
#if (PS5021_EN)
#define 	PF_FLR_ACK                   	BIT15
#define 	VF0_FLR_ACK                     BIT16 // BIT16~BIT31
#else /*(PS5021_EN)*/

#define 	CR_PERSTN_SEL                   BIT16
#define 	CR_RESETN_SEL                   BIT17
#define 	CR_D2H_RESET                    BIT18
#define 	PF_FLR_ACK                      BIT24
#define 	VF0_FLR_ACK                     BIT25
#endif /*(PS5021_EN)*/
#define     GET_PCIE_MISC_RESET_CTRL(x)     (x=r32_PCIE_MISC[RESET_CTRL_W32])
#define     PCIE_MISC_SET_CR_BR_AXI_ASRST() do {\
	CPUARMSetCriticalRegisterBit((r32_PCIE_MISC + RESET_CTRL_W32), CR_BR_AXI_ASRST);\
} while(0)
#define     PCIE_MISC_CLR_CR_BR_AXI_ASRST() do {\
	CPUARMClearCriticalRegisterBit((r32_PCIE_MISC + RESET_CTRL_W32), CR_BR_AXI_ASRST);\
} while(0)
#define     PCIE_MISC_SET_CR_PL_SRST()      do {\
	CPUARMSetCriticalRegisterBit((r32_PCIE_MISC + RESET_CTRL_W32), CR_PL_SRST);\
} while(0)
#define     PCIE_MISC_CLR_CR_PL_SRST()      do {\
	CPUARMClearCriticalRegisterBit((r32_PCIE_MISC + RESET_CTRL_W32), CR_PL_SRST);\
} while(0)
#define     PCIE_MISC_SET_CR_TL_SRST()      do {\
	CPUARMSetCriticalRegisterBit((r32_PCIE_MISC + RESET_CTRL_W32), CR_TL_SRST);\
} while(0)
#define     PCIE_MISC_CLEAR_CR_TL_SRST()      do {\
	CPUARMClearCriticalRegisterBit((r32_PCIE_MISC + RESET_CTRL_W32), CR_TL_SRST);\
} while(0)
#define     PCIE_MISC_SET_PF_FLR_ACK()      do {\
	CPUARMSetCriticalRegisterBit((r32_PCIE_MISC + RESET_CTRL_W32), PF_FLR_ACK);\
} while(0)
#define 	PCIE_MISC_CHECK_PF_FLR_ACK_DONE()         (!(r32_PCIE_MISC[RESET_CTRL_W32] & PF_FLR_ACK))
#define     PCIE_MISC_SET_D2H_RESET() 		do {\
	CPUARMSetCriticalRegisterBit((r32_PCIE_MISC + RESET_CTRL_W32), CR_D2H_RESET);\
} while(0)
#define     PCIE_MISC_CLR_D2H_RESET() 		do {\
	CPUARMClearCriticalRegisterBit((r32_PCIE_MISC + RESET_CTRL_W32), CR_D2H_RESET);\
} while(0)

#if 0
#define     SET_PCIE_PHY_RESET(x)           (r32_PCIE_MISC[RESET_CTRL_W32] = (x& (~(SW_RESETN | SW_PERSTN)) ) | (CR_PERSTN_SEL | CR_RESETN_SEL | CR_PL_SRST | CR_TL_SRST | CR_TL_CRST))
#define     CLR_PCIE_PHY_RESET(x)           \
			{\
				r32_PCIE_MISC[RESET_CTRL_W32] = ( x & (~(CR_TL_SRST | CR_TL_CRST)) ) | (SW_RESETN | SW_PERSTN);\
				r32_PCIE_MISC[RESET_CTRL_W32] &= ~(CR_PERSTN_SEL | CR_RESETN_SEL);\
			}
#else
#define     SET_PCIE_PHY_RESET(x)           r32_PCIE_MISC[RESET_CTRL_W32] = ((x) | (CR_PL_SRST | CR_TL_SRST | CR_TL_CRST))
#define     CLR_PCIE_PHY_RESET(x)           r32_PCIE_MISC[RESET_CTRL_W32] = ( x & (~(CR_TL_SRST | CR_TL_CRST)) );

#endif





#define STS_W32                       ((MISC_OFFSET + 0x04) >> 2)
#define 	XPERSTB                         BIT10
#define 	CLK_REQBI                       BIT11

#define INT_STS_W32                   ((MISC_OFFSET + 0x08) >> 2)
#define INT_EN_W32                    ((MISC_OFFSET + 0x0C) >> 2)
#define WAKEUP_EN_W32                 ((MISC_OFFSET + 0x10) >> 2)
#define 	PF_FLR                          BIT0
#define 	VF_FLR                          BIT1
#define 	PF_BME_R                        BIT2
#define 	PF_BME_F                        BIT3
#define 	VF_EN_R                         BIT4
#define 	VF_EN_F                         BIT5
#define 	VF_BME_R                        BIT6
#define 	VF_BME_F                        BIT7
#define 	PERST_RISING                    BIT8
#define 	PERST_FALLING                   BIT9
#define 	CLKREQI_RISING                  BIT10
#define 	CLKREQI_FALLING                 BIT11
#define 	BVCI2PHY_TO                     BIT12
#define 	BVCI2AXI_TO                     BIT13
#define 	PL_RSTN_SRST_OUT_R              BIT14
#define 	LINK_DOWN                       BIT15
#define 	ENTER_L0                        BIT16
#define 	LEAVE_L0                        BIT17
#define 	ENTER_L1_0                      BIT18
#define 	LEAVE_L1_0                      BIT19
#define 	ENTER_L1_2                      BIT20
#define 	LEAVE_L1_2                      BIT21
#define 	ENTER_L23                       BIT22
#define 	PME_TURN_OFF_RCV                BIT23
#define 	SPEED_CHG_TO_LOW                BIT24
#define 	D3_ENTRY                        BIT25
#define 	LINKUP_R                        BIT26
#define 	LINKUP_F                        BIT27
#define 	VF_MSE_R                        BIT28
#define 	VF_MSE_F                        BIT29
#define 	PCIE_LOCAL_INTR                 BIT30
#define 	PCIE_MISC_INTR                  BIT31
#define		ALL_INT_STS						(0xFFFFFFFF)

#define GET_PCIE_MISC_INT_STS(x)		(x=r32_PCIE_MISC[INT_STS_W32])
#define CHK_PCIE_MISC_INT_STS()			(r32_PCIE_MISC[INT_STS_W32])
#define CLR_PCIE_MISC_INT_STS(x)		(r32_PCIE_MISC[INT_STS_W32]=x)
#define CHK_PCIE_MISC_INT_LINKUP_R()	(r32_PCIE_MISC[INT_STS_W32] & LINKUP_R)
#define CLR_PCIE_MISC_INT_LINKUP()	do { \
	CPUARMSetCriticalRegisterBit((r32_PCIE_MISC + INT_STS_W32), (LINKUP_R | LINKUP_F) );\
} while(0)
#define SET_PCIE_MISC_INT_PERST()		(r32_PCIE_MISC[INT_EN_W32] = (PERST_RISING|PERST_FALLING))

#define SET_PCIE_WAKE_EN(x)				(r32_PCIE_MISC[WAKEUP_EN_W32] |= (x))


#if 0
#define SET_PCIE_MISC_INT_ALL()        (r32_PCIE_MISC[INT_EN_W32] = (PF_FLR|PF_BME_R|PF_BME_F|PERST_RISING|PERST_FALLING| \
										CLKREQI_RISING|CLKREQI_FALLING|PL_RSTN_SRST_OUT_R|LINK_DOWN| \
			                            ENTER_L0|LEAVE_L0|ENTER_L1_0|LEAVE_L1_0|ENTER_L1_2|LEAVE_L1_2| \
			                            ENTER_L23|PME_TURN_OFF_RCV|SPEED_CHG_TO_LOW|D3_ENTRY| \
			                            LINKUP_R|LINKUP_F|PCIE_LOCAL_INTR))
#else

#define SET_PCIE_MISC_INT_ALL()        (r32_PCIE_MISC[INT_EN_W32] = (PF_FLR|PF_BME_R|PF_BME_F|PERST_RISING|PERST_FALLING| \
										LINK_DOWN|BVCI2PHY_TO|BVCI2AXI_TO| \
			                            SPEED_CHG_TO_LOW|D3_ENTRY| \
			                            LINKUP_R|LINKUP_F|PCIE_LOCAL_INTR))
#define M_SET_PCIE_MISC_INT_ALL()       SET_PCIE_MISC_INT_ALL()
#endif

#define SET_PCIE_MISC_INT_PF_FLR()       (r32_PCIE_MISC[INT_EN_W32] |= PF_FLR)
#define CLR_PCIE_MISC_INT_PF_FLR()       (r32_PCIE_MISC[INT_EN_W32] &= (~PF_FLR))
#define CLR_PCIE_MISC_INT_STS_PF_FLR()   (r32_PCIE_MISC[INT_STS_W32] = PF_FLR)


#define SET_PCIE_MISC_WAKEUP_EN(x)      (r32_PCIE_MISC[INT_STS_W32] = x)
#define PCIE_MISC_PERST_WAKEUP_EN       (PERST_RISING | PERST_FALLING)




#define OPT_CTRL1_W32                 ((MISC_OFFSET + 0x14) >> 2)
#define 	AUTO_LTR_EN                     BIT0
#define 	FW_PME_ACK_OK                   BIT1
#define 	FW_ASPML0S_DIS                  BIT2
#define 	FW_RX_PAUSE                     BIT3    // block rx cfg cycle
#define 	FW_ASPML1_TIMER_DIS             BIT4
#define 	USE_XTAL_EN                     BIT5
#define 	BYPASS_SYS                      BIT6
#define 	FW_ERR_PHYRCV_DIS               BIT7
#define 	FW_LBK_CHANGE_DIS               BIT8
#define 	FW_ACK_PM_PRIORITY_DIS          BIT9
//#define FW_ACK_PM_PRIORITY_DIS          BIT10
#define 	CHK_PD2_PENDING                 BIT11
#define 	CHK_OUTSTANDING                 BIT12
#define 	DISABLE_BLK_D2H                 BIT13
#define 	REF_CLK_INV                     BIT14
#define 	DISABLE_ARI_CAP                 BIT15
#define 	DISABLE_SRIOV_CAP               BIT16
#define 	DISABLE_EXP_ROM                 BIT17
#define 	DISABLE_LAN_ERR                 BIT18
#define 	USE_LEGACY_ENDP                 BIT19
#define 	AUTO_FLR_ACK                    BIT20
#define 	ENABLE_UNUSED_LAN               BIT21   // when set to 0, hw disable unused lane power
#define 	DISCARD_EI_INFER                BIT22
#define 	SYNC_CLK_1US                    BIT23
#define 	LEGACY_L1_EN                    BIT24
#define 	FW_PHI_LTR_SOURCE               BIT25
#define 	RELEASE_CLK_REQ                 BIT26
#define 	PHY_CTRL_KEEP1US                BIT27
#define 	SKP_INTERVAL_SRIS               BIT28
#define 	PHYCLKREQ_SEL                   BIT29
#define 	PSW_WAIT_MASK                   BIT30
#define 	FPGA_DFE_EN                     BIT31

#define     SET_FW_PHI_LTR_SOURCE()         (r32_PCIE_MISC[OPT_CTRL1_W32] |= FW_PHI_LTR_SOURCE)

#define     SET_AUTO_LTR_EN()               (r32_PCIE_MISC[OPT_CTRL1_W32] |= AUTO_LTR_EN)
#define     CLR_AUTO_LTR_EN()               (r32_PCIE_MISC[OPT_CTRL1_W32] &= ~AUTO_LTR_EN)

#define     SET_FW_ASPML0S_DIS()            (r32_PCIE_MISC[OPT_CTRL1_W32] |= FW_ASPML0S_DIS)
#define     CLR_FW_ASPML0S_DIS()            (r32_PCIE_MISC[OPT_CTRL1_W32] &= ~FW_ASPML0S_DIS)

#define     SET_FW_RX_PAUSE()               (r32_PCIE_MISC[OPT_CTRL1_W32] |= FW_RX_PAUSE)
#define     CLR_FW_RX_PAUSE()               (r32_PCIE_MISC[OPT_CTRL1_W32] &= ~FW_RX_PAUSE)

#define     SET_FW_ASPML1_TIMER_DIS()       (r32_PCIE_MISC[OPT_CTRL1_W32] |= FW_ASPML1_TIMER_DIS)
#define     CLR_FW_ASPML1_TIMER_DIS()       (r32_PCIE_MISC[OPT_CTRL1_W32] &= ~FW_ASPML1_TIMER_DIS)

#define     SET_CHK_OUTSTANDING()           (r32_PCIE_MISC[OPT_CTRL1_W32] |= CHK_OUTSTANDING)
#define     CLR_CHK_OUTSTANDING()           (r32_PCIE_MISC[OPT_CTRL1_W32] &= ~CHK_OUTSTANDING)

#define     M_PCIE_SET_DISABLE_BLK_D2H()    (r32_PCIE_MISC[OPT_CTRL1_W32] |= DISABLE_BLK_D2H)
#define     M_PCIE_CLR_DISABLE_BLK_D2H()    (r32_PCIE_MISC[OPT_CTRL1_W32] &= ~DISABLE_BLK_D2H)

#define     SET_DISABLE_EXP_ROM()           (r32_PCIE_MISC[OPT_CTRL1_W32] |= DISABLE_EXP_ROM)
#define     CLR_DISABLE_EXP_ROM()           (r32_PCIE_MISC[OPT_CTRL1_W32] &= ~DISABLE_EXP_ROM)

#define     SET_AUTO_FLR_ACK()              (r32_PCIE_MISC[OPT_CTRL1_W32] |= AUTO_FLR_ACK)
#define     CLR_AUTO_FLR_ACK()              (r32_PCIE_MISC[OPT_CTRL1_W32] &= ~AUTO_FLR_ACK)

#define     SET_LEGACY_L1_EN()              (r32_PCIE_MISC[OPT_CTRL1_W32] |= LEGACY_L1_EN)
#define     CLR_LEGACY_L1_EN()              (r32_PCIE_MISC[OPT_CTRL1_W32] &= ~LEGACY_L1_EN)

#define     SET_SKP_INTERVAL_SRIS()         (r32_PCIE_MISC[OPT_CTRL1_W32] |= SKP_INTERVAL_SRIS)
#define     CLR_SKP_INTERVAL_SRIS()         (r32_PCIE_MISC[OPT_CTRL1_W32] &= ~SKP_INTERVAL_SRIS)




#define OPT_CTRL2_W32                 ((MISC_OFFSET + 0x18) >> 2)
#define 	PSM_CHK_PLL_LOCK                BIT4
#define 	PHY_PMU_SEL                     BIT5    //1b to use PHY PMU instead of POWER_SM
#define 	POWER_RST_ENN                   BIT6    //
#define 	PHY_CTRL_SEL                    BIT7
#define 	PCLK_GATING_EN                  BIT19
#define 	ISO_BREAK_EN                    BIT20
#define 	DIS_BLK_AXI_LP                  BIT21
#define 	PM_AUTO_CLR_EN                  BIT22
#define 	PM_AUTO_EN                      BIT23
#define 	LIGHT_SLEEP_EN                  BIT30
#define 	DEEP_SLEEP_EN                   BIT31

#define     SET_PM_AUTO_EN()               do {\
	CPUARMSetCriticalRegisterBit((r32_PCIE_MISC + OPT_CTRL2_W32), PM_AUTO_EN);\
} while(0)
#define     CLR_PM_AUTO_EN()               do {\
	CPUARMClearCriticalRegisterBit((r32_PCIE_MISC + OPT_CTRL2_W32), PM_AUTO_EN);\
} while(0)

#define K_GEN_W32                       ((MISC_OFFSET + 0x24) >> 2)
#define   K_GEN_LANE_2					(BIT8)
#define   K_GEN_LANE_4					(BIT9)
#define 	SUP_G2(x)                       (((U32)(x) & 0x1) << 12)
#define 	SUP_G3(x)                       (((U32)(x) & 0x1) << 13)

#define     PCIE_MISC_5G_SUP                BIT12
#define     PCIE_MISC_8G_SUP                BIT13
#define     PCIE_MISC_16G_SUP               BIT14

#define 	OP_SRIS_MODE                    BIT20

#define 	PCIE_MISC_SET_K_GEN(N)			(r32_PCIE_MISC[K_GEN_W32] = N)

#define     SET_OP_SRIS_MODE()              (r32_PCIE_MISC[K_GEN_W32] |= OP_SRIS_MODE)
#define     CLR_OP_SRIS_MODE()              (r32_PCIE_MISC[K_GEN_W32] &= ~OP_SRIS_MODE)

#define PMU_CTRL_W16					((MISC_OFFSET + 0x28) >> 1)
#define PMU_CTRL_W32                    ((MISC_OFFSET + 0x28) >> 2)
#define 	PMU_L12_ENTRY                   BIT10
#define 	L12_IDLE                        BIT11
#define 	PMU_L12_EXIT                    BIT12
#define 	FSM_POWER_ON_STATE              BIT31

#define     CHK_FSM_POWER_ON_STATE()                        (r32_PCIE_MISC[PMU_CTRL_W32] & FSM_POWER_ON_STATE)
#define 	GET_PMU_CONTROL_LINK_WIDTH()                    ((r32_PCIE_MISC[PMU_CTRL_W32] & BITMSK(3,5)) >> 5)
#define 	GET_PMU_CONTROL_LINK_SPEED()                    ((r32_PCIE_MISC[PMU_CTRL_W32] & BITMSK(2,8)) >> 8)
#define 	GET_PMU_CONTROL_LTSSM()                         (r32_PCIE_MISC[PMU_CTRL_W32] & BITMSK(5,0))
#define		GET_PMU_CONTROL_LTSSM_WIDTH_SPEED()             (r16_PCIE_MISC[PMU_CTRL_W16])
#define		M_PCIE_GET_PMU_CONTROL()                        (r32_PCIE_MISC[PMU_CTRL_W32])
// get PCI Cap offset 0x12 Link status register from ltssm
#define     M_PCIE_GET_PCIECAP_LINK_STATUS_FROM_LTSSM(x)    (x=(GET_PMU_CONTROL_LINK_SPEED()|(GET_PMU_CONTROL_LINK_WIDTH()<<4)|SLOT_CLOCK_CONFIG))
#define 	GET_PCIE_MISC_PM_STATE()                        ((r32_PCIE_MISC[PMU_CTRL_W32] & BITMSK(2,24)) >> 24)

#define		LTSSM_DETECT				(0x1)
#define     LTSSM_L0					(0x10)
#define		LTSSM_L1_IDLE				(0x13)
#define     LTSSM_L2_TRANSMITWAKE		(0x15)
#define		PCIE_DEVICE_STATE_D0		(0x0)
#define		PCIE_DEVICE_STATE_D3		(0x3)

#define PHISON_LTR_VALUE_W32  		    ((MISC_OFFSET + 0x2C) >> 2)
#define PSN_SNOOP_VALUE(x)     			((U32)(x) & 0x03FF)
#define PSN_SNOOP_SCALE(x)     			(((U32)(x) & 0x07) << 10)
#define PSN_SNOOP_REQUIRE               BIT15
#define PSN_NO_SNOOP_VALUE(x)     		(((U32)(x) & 0x03FF) << 16)
#define PSN_NO_SNOOP_SCALE(x)     		(((U32)(x) & 0x07) << 26)
#define PSN_NO_SNOOP_REQUIRE            BIT31

#define SET_PHISON_LTR_VALUE(x)			(r32_PCIE_MISC[PHISON_LTR_VALUE_W32]=x)

#define MISCL_OPT_CTRL4_0x34            ((MISC_OFFSET + 0x34) >> 2)
#define FW_ASPML1_DIS                   BIT22
#define D3_BLK_D2H0_ENB                 BIT24
#define ECO6_ENB                        BIT30
#define PCIE_MISC_CR_REF_EN             BIT31
#define PCIE_BAD0BAD0                   (0xBAD0BAD0)

#define M_PCIE_L1_DIS()					do {\
	CPUARMSetCriticalRegisterBit((r32_PCIE_MISC + MISCL_OPT_CTRL4_0x34), FW_ASPML1_DIS);\
} while(0)
#define M_PCIE_L1_EN()                  do {\
	CPUARMClearCriticalRegisterBit((r32_PCIE_MISC + MISCL_OPT_CTRL4_0x34), FW_ASPML1_DIS);\
} while(0)
#define SET_D3_BLK_D2H0_ENB()           do {\
	CPUARMSetCriticalRegisterBit((r32_PCIE_MISC + MISCL_OPT_CTRL4_0x34), D3_BLK_D2H0_ENB);\
} while(0)
#define M_PCIE_SET_ECO6_ENB()           do {\
	CPUARMSetCriticalRegisterBit((r32_PCIE_MISC + MISCL_OPT_CTRL4_0x34), ECO6_ENB);\
} while(0)
#define M_PCIE_CLR_ECO6_ENB()           do {\
	CPUARMClearCriticalRegisterBit((r32_PCIE_MISC + MISCL_OPT_CTRL4_0x34), ECO6_ENB);\
} while(0)
#define M_PCIE_SET_MISC_0x34(x)         (r32_PCIE_MISC[MISCL_OPT_CTRL4_0x34] = x)
#define M_PCIE_GET_MISC_0x34()          (r32_PCIE_MISC[MISCL_OPT_CTRL4_0x34])
#define M_PCIE_SET_CR_REF_EN()          do {\
	CPUARMSetCriticalRegisterBit((r32_PCIE_MISC + MISCL_OPT_CTRL4_0x34), PCIE_MISC_CR_REF_EN);\
} while(0)

#define MISCL_OPT_CTRL5_0x38            ((MISC_OFFSET + 0x38) >> 2) // Modified by ISR

#define GET_L0_TO_REC_RXTS()            ((r32_PCIE_MISC[MISCL_OPT_CTRL5_0x38]&0x00FF0000)>>16)
#define GET_L0_TO_REC_RXERR()           ((r32_PCIE_MISC[MISCL_OPT_CTRL5_0x38]&0xFF000000)>>24)
#define CLR_L0_TO_REC_RXTS()            (r32_PCIE_MISC[MISCL_OPT_CTRL5_0x38]|=0x00010000)
#define CLR_L0_TO_REC_RXERR()           (r32_PCIE_MISC[MISCL_OPT_CTRL5_0x38]|=0x01000000)
#define M_PCIE_GET_RXTX_RECOVRY()       ((r32_PCIE_MISC[MISCL_OPT_CTRL5_0x38]& BITMSK(16,16))>>16)
#define M_PCIE_CLR_RXTX_RECOVRY()       (r32_PCIE_MISC[MISCL_OPT_CTRL5_0x38]|= BITMSK(16,16))

#define MISCL_OPT_CTRL7_0x40            ((MISC_OFFSET + 0x40) >> 2)
#define AXI_ERR_LK_DWN_DIS              BIT0
#define FIRST_VF_256                    BIT1

#define SET_FIRST_VF_256()    	    (r32_PCIE_MISC[MISCL_OPT_CTRL7_0x40] |= FIRST_VF_256)
#define CLR_FIRST_VF_256()     	    (r32_PCIE_MISC[MISCL_OPT_CTRL7_0x40] &= ~FIRST_VF_256)

#define OPT_CTRL8_W32                   ((MISC_OFFSET + 0x44) >> 2)
#define CHK_CLK_GATING                  BIT1
#define AUTO_CHK_PLL_LOCK               BIT4
#define CHK_PCLK_READY                  BIT5
#define BLK_RXELECIDLE_CHK              BIT8
#define LEGACY_L1_CLKREQ_EN             BIT9
#define BLK_RX_DATA_BUFFER              BIT11
#define ASPML1_256NS	                BIT13
#define PHYSTATUS_W_PDPLL               BIT15   // FPGA only
#define PHYSTATUS_MASK_WA               BIT18

#define PCIE_MISC_CPL_TIMEOUT_SHIFT     (20)

#define SET_CHK_CLK_GATING()    	    do {\
	CPUARMSetCriticalRegisterBit((r32_PCIE_MISC + OPT_CTRL8_W32), CHK_CLK_GATING);\
} while(0)
#define CLR_CHK_CLK_GATING()     	    do {\
	CPUARMClearCriticalRegisterBit((r32_PCIE_MISC + OPT_CTRL8_W32), CHK_CLK_GATING);\
} while(0)
#define SET_AUTO_CHK_PLL_LOCK()    	    do {\
	CPUARMSetCriticalRegisterBit((r32_PCIE_MISC + OPT_CTRL8_W32), AUTO_CHK_PLL_LOCK);\
} while(0)
#define CLR_AUTO_CHK_PLL_LOCK()     	do {\
	CPUARMClearCriticalRegisterBit((r32_PCIE_MISC + OPT_CTRL8_W32), AUTO_CHK_PLL_LOCK);\
} while(0)
#define M_PCIE_SET_CHK_PCLK_READY()     do {\
	CPUARMSetCriticalRegisterBit((r32_PCIE_MISC + OPT_CTRL8_W32), CHK_PCLK_READY);\
} while(0)
#define M_PCIE_CLR_CHK_PCLK_READY()     do {\
	CPUARMClearCriticalRegisterBit((r32_PCIE_MISC + OPT_CTRL8_W32), CHK_PCLK_READY);\
} while(0)
#define SET_BLK_RXELECIDLE_CHK()        do {\
	CPUARMSetCriticalRegisterBit((r32_PCIE_MISC + OPT_CTRL8_W32), BLK_RXELECIDLE_CHK);\
} while(0)
#define CLR_BLK_RXELECIDLE_CHK()        do {\
	CPUARMClearCriticalRegisterBit((r32_PCIE_MISC + OPT_CTRL8_W32), BLK_RXELECIDLE_CHK);\
} while(0)
#define SET_LEGACY_L1_CLKREQ_EN()       do {\
	CPUARMSetCriticalRegisterBit((r32_PCIE_MISC + OPT_CTRL8_W32), LEGACY_L1_CLKREQ_EN);\
} while(0)
#define CLR_LEGACY_L1_CLKREQ_EN()       do {\
	CPUARMClearCriticalRegisterBit((r32_PCIE_MISC + OPT_CTRL8_W32), LEGACY_L1_CLKREQ_EN);\
} while(0)
#define SET_BLK_RX_DATA_BUFFER()        	do {\
	CPUARMSetCriticalRegisterBit((r32_PCIE_MISC + OPT_CTRL8_W32), BLK_RX_DATA_BUFFER);\
} while(0)
#define CHECK_BLK_RX_DATA_BUFFER()        ((r32_PCIE_MISC[OPT_CTRL8_W32] & BLK_RX_DATA_BUFFER) == BLK_RX_DATA_BUFFER)
#define CLR_BLK_RX_DATA_BUFFER()        	do {\
	CPUARMClearCriticalRegisterBit((r32_PCIE_MISC + OPT_CTRL8_W32), BLK_RX_DATA_BUFFER);\
} while(0)
#define SET_ASPML1_256NS()            	do {\
	CPUARMSetCriticalRegisterBit((r32_PCIE_MISC + OPT_CTRL8_W32), ASPML1_256NS);\
} while(0)
#define CLR_ASPML1_256NS()         	   	do {\
	CPUARMClearCriticalRegisterBit((r32_PCIE_MISC + OPT_CTRL8_W32), ASPML1_256NS);\
} while(0)
#define SET_PHYSTATUS_W_PDPLL()         do {\
	CPUARMSetCriticalRegisterBit((r32_PCIE_MISC + OPT_CTRL8_W32), PHYSTATUS_W_PDPLL);\
} while(0)
#define CLR_PHYSTATUS_W_PDPLL()         do {\
	CPUARMClearCriticalRegisterBit((r32_PCIE_MISC + OPT_CTRL8_W32), PHYSTATUS_W_PDPLL);\
} while(0)
#define SET_PHYSTATUS_MASK_WA()         do {\
	CPUARMSetCriticalRegisterBit((r32_PCIE_MISC + OPT_CTRL8_W32), PHYSTATUS_MASK_WA);\
} while(0)
#define CLR_PHYSTATUS_MASK_WA()         do {\
	CPUARMClearCriticalRegisterBit((r32_PCIE_MISC + OPT_CTRL8_W32), PHYSTATUS_MASK_WA);\
} while(0)
#if (PS5021_EN)
//#define M_PCIE_MISC_SET_CPL_TIMEOUT(x) {r32_PCIE_MISC[OPT_CTRL8_W32] &= ~(BITMSK(2,20)); r32_PCIE_MISC[OPT_CTRL8_W32] |= ((x & BITMSK(2,0)) << PCIE_MISC_CPL_TIMEOUT_SHIFT);}
#define M_PCIE_MISC_SET_CPL_TIMEOUT(x)         do {\
	   CPUARMClearCriticalRegisterBit((r32_PCIE_MISC + OPT_CTRL8_W32), BITMSK(2,20));\
	   CPUARMSetCriticalRegisterBit((r32_PCIE_MISC + OPT_CTRL8_W32), ((x & BITMSK(2,0)) << PCIE_MISC_CPL_TIMEOUT_SHIFT));\
} while(0)
#endif /*(PS5021_EN)*/


#define INT_STS2_W32                   ((MISC_OFFSET + 0x48) >> 2)
#define INT_EN2_W32                    ((MISC_OFFSET + 0x4C) >> 2)
#define ARI_HIER_R                      BIT0
#define ARI_HIER_F                      BIT1
#define DIR_PF_FLR_REQ_ST               BIT2
#define DIR_VF_FLR_REQ_ST               BIT3
#define NON_D0_TO_D0_ST                 BIT4

#define MAXPAYLOAD_ERR                  BIT6
#define RXRAM_ERR                       BIT7
#define TXRAM_ERR                       BIT8
#define P2ARAM_ERR                      BIT9
#define A2PRAM_ERR                      BIT10
#define HOST_TOOL_ST                    BIT13

#define GET_PCIE_MISC_INT_STS2(x)       (x=r32_PCIE_MISC[INT_STS2_W32])
#define CLR_PCIE_MISC_INT_STS2(x)       (r32_PCIE_MISC[INT_STS2_W32]=x)
#define SET_PCIE_MISC_INT2_ALL()        (r32_PCIE_MISC[INT_EN2_W32] = (NON_D0_TO_D0_ST|MAXPAYLOAD_ERR|\
																	   RXRAM_ERR|TXRAM_ERR|P2ARAM_ERR|A2PRAM_ERR))
#define M_SET_PCIE_MISC_INT2_ALL()		SET_PCIE_MISC_INT2_ALL()

// PS5013 850/854/858 Dword can not clear when 0xffff
#define ERROR_RECORD1                       ((MISC_OFFSET + 0x50) >> 2) // Modified by ISR

#define M_PCIE_GET_ERR_PHYRCV()             (r32_PCIE_MISC[ERROR_RECORD1] & BITMSK(16,0))
#define M_PCIE_GET_ERR_DLLBADTLP()          ((r32_PCIE_MISC[ERROR_RECORD1] & BITMSK(8,16))>>16)
#define M_PCIE_CLR_ERROR_RECORD1()          (r32_PCIE_MISC[ERROR_RECORD1] |= BITMSK(32,0))

#define ERROR_RECORD2                       ((MISC_OFFSET + 0x54) >> 2) // Modified by ISR

#define M_PCIE_GET_ERR_DLLBADDLLP()         (r32_PCIE_MISC[ERROR_RECORD2] & BITMSK(8,0))
#define M_PCIE_GET_ERR_DLLRCV()             ((r32_PCIE_MISC[ERROR_RECORD2] & BITMSK(8,16))>>16)
#define M_PCIE_CLR_ERROR_RECORD2()          (r32_PCIE_MISC[ERROR_RECORD2] |= BITMSK(32,0))

#define ERROR_RECORD3                       ((MISC_OFFSET + 0x58) >> 2) // Modified by ISR

#define M_PCIE_GET_ERR_TLECRC()             (r32_PCIE_MISC[ERROR_RECORD3] & BITMSK(8,0))
#define M_PCIE_GET_ERR_TLMALF()             ((r32_PCIE_MISC[ERROR_RECORD3] & BITMSK(8,16))>>16)
#define M_PCIE_CLR_ERROR_RECORD3()          (r32_PCIE_MISC[ERROR_RECORD3] |= BITMSK(32,0))

#define M_PCIE_GET_PACKET_ERR()             ((M_PCIE_GET_ERR_TLMALF()<<24)|(M_PCIE_GET_ERR_DLLRCV()<<16)|(M_PCIE_GET_ERR_DLLBADDLLP()<<8)|(M_PCIE_GET_ERR_DLLBADTLP()))

#define ERROR_RECORD4                       ((MISC_OFFSET + 0x5C) >> 2)

#define M_GET_PCIE_MISC_CNT_RECOVERY()      (r32_PCIE_MISC[ERROR_RECORD4] & BITMSK(16,0))
#define M_CLEAR_PCIE_MISC_CNT_RECOVERY()    (r32_PCIE_MISC[ERROR_RECORD4] |= BIT0)

#define EQ_BEST_FOM_DBG                     ((MISC_OFFSET + 0x60) >> 2)

#define OPT_CTRL9_W32                   ((MISC_OFFSET + 0x68) >> 2)
#define BYPASS_VSEC_CAP                 BIT16
#define PM_START_CHK_CMD                BIT17
#define D3_LATCH_DMA_DIS                BIT18
#define D3_LATCH_INT_DIS                BIT19
#define D3_LATCH_MSI_DIS                BIT20
#define BME_LATCH_MSI_DIS               BIT21
#define PCIE_MISC_FRC_CPL_TO            BIT28
#define PCIE_MISC_FRC_WCPL_TO           BIT29

#define SET_PM_START_CHK_CMD()           do {\
	CPUARMSetCriticalRegisterBit((r32_PCIE_MISC + OPT_CTRL9_W32), PM_START_CHK_CMD);\
} while(0)
#define CLR_PM_START_CHK_CMD()           do {\
	CPUARMClearCriticalRegisterBit((r32_PCIE_MISC + OPT_CTRL9_W32), PM_START_CHK_CMD);\
} while(0)
#define SET_D3_LATCH_DMA_DIS()           do {\
	CPUARMSetCriticalRegisterBit((r32_PCIE_MISC + OPT_CTRL9_W32), D3_LATCH_DMA_DIS);\
} while(0)
#define CLR_D3_LATCH_DMA_DIS()           do {\
	CPUARMClearCriticalRegisterBit((r32_PCIE_MISC + OPT_CTRL9_W32), D3_LATCH_DMA_DIS);\
} while(0)
#define CHK_D3_LATCH_DMA_DIS()           (r32_PCIE_MISC[OPT_CTRL9_W32] & D3_LATCH_DMA_DIS)

#define SET_D3_LATCH_INT_DIS()           do {\
	CPUARMSetCriticalRegisterBit((r32_PCIE_MISC + OPT_CTRL9_W32), D3_LATCH_INT_DIS);\
} while(0)
#define CLR_D3_LATCH_INT_DIS()           do {\
	CPUARMClearCriticalRegisterBit((r32_PCIE_MISC + OPT_CTRL9_W32), D3_LATCH_INT_DIS);\
} while(0)
#define CHK_D3_LATCH_INT_DIS()           (r32_PCIE_MISC[OPT_CTRL9_W32] & D3_LATCH_INT_DIS)

#define SET_D3_LATCH_MSI_DIS()           do {\
	CPUARMSetCriticalRegisterBit((r32_PCIE_MISC + OPT_CTRL9_W32), D3_LATCH_MSI_DIS);\
} while(0)
#define CLR_D3_LATCH_MSI_DIS()           do {\
	CPUARMClearCriticalRegisterBit((r32_PCIE_MISC + OPT_CTRL9_W32), D3_LATCH_MSI_DIS);\
} while(0)
#define CHK_D3_LATCH_MSI_DIS()           (r32_PCIE_MISC[OPT_CTRL9_W32] & D3_LATCH_MSI_DIS)

#define SET_BME_LATCH_MSI_DIS()          do {\
	CPUARMSetCriticalRegisterBit((r32_PCIE_MISC + OPT_CTRL9_W32), BME_LATCH_MSI_DIS);\
} while(0)
#define CLR_BME_LATCH_MSI_DIS()          do {\
	CPUARMClearCriticalRegisterBit((r32_PCIE_MISC + OPT_CTRL9_W32), BME_LATCH_MSI_DIS);\
} while(0)
#define CHK_BME_LATCH_MSI_DIS()          (r32_PCIE_MISC[OPT_CTRL9_W32] & BME_LATCH_MSI_DIS)

#define SET_BYPASS_VSEC_CAP()            do {\
	CPUARMSetCriticalRegisterBit((r32_PCIE_MISC + OPT_CTRL9_W32), BYPASS_VSEC_CAP);\
} while(0)
#define CLR_BYPASS_VSEC_CAP()            do {\
	CPUARMClearCriticalRegisterBit((r32_PCIE_MISC + OPT_CTRL9_W32), BYPASS_VSEC_CAP);\
} while(0)

#if (PS5021_EN)
#define M_PCIE_MISC_SET_FRC_CPL_TO()            do {\
	CPUARMSetCriticalRegisterBit((r32_PCIE_MISC + OPT_CTRL9_W32), PCIE_MISC_FRC_CPL_TO);\
} while(0)
#define M_PCIE_MISC_CLR_FRC_CPL_TO()            do {\
	CPUARMClearCriticalRegisterBit((r32_PCIE_MISC + OPT_CTRL9_W32), PCIE_MISC_FRC_CPL_TO);\
} while(0)
#define M_PCIE_MISC_SET_FRC_WCPL_TO()            do {\
	CPUARMSetCriticalRegisterBit((r32_PCIE_MISC + OPT_CTRL9_W32), PCIE_MISC_FRC_WCPL_TO);\
} while(0)
#define M_PCIE_MISC_CLR_FRC_WCPL_TO()            do {\
	CPUARMClearCriticalRegisterBit((r32_PCIE_MISC + OPT_CTRL9_W32), PCIE_MISC_FRC_WCPL_TO);\
} while(0)
#endif /*(PS5021_EN)*/

#define	PCIE_MISC_EQ_TXCOEFF_LAST_L0	        ((MISC_OFFSET + 0x70) >> 2)

#define	PCIE_MISC_SCRAM_PAR_ERR_ADD	            ((MISC_OFFSET + 0x80) >> 2)
#define M_PCIE_GET_SCRAM_PARITY_ERROR_ADD()     (r32_PCIE_MISC[PCIE_MISC_SCRAM_PAR_ERR_ADD])

#define OPT_CTRL10_W32                          ((MISC_OFFSET + 0x84) >> 2)

#define PL_RST_PTX_READY_DIS                    BIT18

#define SET_PL_RST_PTX_READY_DIS()              (r32_PCIE_MISC[OPT_CTRL10_W32] |= PL_RST_PTX_READY_DIS)
#define CLR_PL_RST_PTX_READY_DIS()              (r32_PCIE_MISC[OPT_CTRL10_W32] &= ~PL_RST_PTX_READY_DIS)

#define LOW_POWER_OPTION                ((MISC_OFFSET + 0xA0) >> 2)
#define R8_LOW_POWER_OPTION				(MISC_OFFSET + 0xA0)
#define ASPML1_TIMER_CTRL               BIT8
#define M_PCIE_MISC_SET_ASPML1_DELAY(x)	(r8_PCIE_MISC[R8_LOW_POWER_OPTION] = x)

#define SET_ASPML1_TIMER_CTRL()         (r32_PCIE_MISC[LOW_POWER_OPTION]|=ASPML1_TIMER_CTRL)
#define ASPML1_TIME(x)                  (((U32)(x) & 0xFF) << 0)
#define CLR_ASPML1_TIME()               (r32_PCIE_MISC[LOW_POWER_OPTION]&=(~(ASPML1_TIME(0xFF))))
#define SET_ASPML1_TIME(x)              (r32_PCIE_MISC[LOW_POWER_OPTION]|=(ASPML1_TIME(x)))

#define LOW_POWER_OPTION2               ((MISC_OFFSET + 0xA4) >> 2)
#define PHISON_HOST_TOOL                BIT31

#define SET_PHISON_HOST_TOOL()       (r32_PCIE_MISC[LOW_POWER_OPTION2] |= PHISON_HOST_TOOL)
#define CLR_PHISON_HOST_TOOL()       (r32_PCIE_MISC[LOW_POWER_OPTION2] &= ~PHISON_HOST_TOOL)

#define r8_PHY                                  ((REG8  *)COMB_PHY_REG_ADDRESS)
#define r32_PHY                                 ((REG32 *)COMB_PHY_REG_ADDRESS)

#if (PS5021_EN)
#define R32_PCIE_MISC_FORCE_OPTION      	((MISC_OFFSET + 0xC0) >> 2)
#define M_PCIE_MISC_SET_FORCE_OPTION(x)   (r32_PCIE_MISC[R32_PCIE_MISC_FORCE_OPTION] |= (((U32)(0x01) ) << x) )
#define M_PCIE_MISC_CLR_FORCE_OPTION(x)   (r32_PCIE_MISC[R32_PCIE_MISC_FORCE_OPTION] &= ~(((U32)(0x01) ) << x) )
#endif /*(PS5021_EN)*/

#define	PCIE_PHYSETTING_LANE_0		(COMB_PHY_REG_ADDRESS + 0xF0)
#define	PCIE_PHYSETTING_LANE_1		(COMB_PHY_REG_ADDRESS + 0x1F0)
#define	PCIE_PHYSETTING_LANE_2		(COMB_PHY_REG_ADDRESS + 0x2F0)
#define	PCIE_PHYSETTING_LANE_3		(COMB_PHY_REG_ADDRESS + 0x3F0)
#define	PCIE_EQINFO_OFFSET_1		(MPHY_REGISTER_ADDRESS + (ERROR_RECORD4 << 2))
#define	PCIE_EQINFO_OFFSET_2		(MPHY_REGISTER_ADDRESS + (PCIE_MISC_EQ_TXCOEFF_LAST_L0 << 2))

#if (!PS5021_EN)
#define R8_PHY_PHYSETTING_LANE_0                (0x0F0) // = R8_PHY_PHYSETTING_LANE_0 + (0 * 0x100)
#define R8_PHY_PHYSETTING_LANE_1                (0x1F0) // R8_PHY_PHYSETTING_LANE_0 + 0x100 = R8_PHY_PHYSETTING_LANE_0 + (1 * 0x100)
#define R8_PHY_PHYSETTING_LANE_2                (0x2F0) // R8_PHY_PHYSETTING_LANE_1 + 0x100 = R8_PHY_PHYSETTING_LANE_0 + (2 * 0x100)
#define R8_PHY_PHYSETTING_LANE_3                (0x3F0) // R8_PHY_PHYSETTING_LANE_2 + 0x100 = R8_PHY_PHYSETTING_LANE_0 + (3 * 0x100)
#define PICAL_OFFSET_DONE_BIT                   (BIT1)
#define M_PCIE_CHECK_LANE_PICAL_OFFSET_DONE(LANE)    (r8_PHY[(R8_PHY_PHYSETTING_LANE_0 + (LANE * 0x100))] & PICAL_OFFSET_DONE_BIT)

#define R8_PHY_EYE_LOW_LIMIT                    (0x431)
#define M_PCIE_SET_EYE_LOW_LIMIT()              (r8_PHY[R8_PHY_EYE_LOW_LIMIT] = 0x04)
#define M_PCIE_RESET_EYE_LOW_LIMIT()            (r8_PHY[R8_PHY_EYE_LOW_LIMIT] = 0x14)

#define AG3_PLL_BW                              (0x508 >> 2)
#define M_PCIE_SET_AG3_BW()                     (r32_PHY[AG3_PLL_BW] = 0x20010000)

#define AG3_PLL_BS                              (0x510 >> 2)
#define M_PCIE_CLR_AG3_PLL_BS()                 (r32_PHY[AG3_PLL_BS] &= (~(BITMSK(6, 21))))
#define M_PCIE_SET_AG3_PLL_BS(x)                (r32_PHY[AG3_PLL_BS] |= (x<<21))

#define AG2_PLL_BW                              (0x514 >> 2)
#define M_PCIE_SET_AG2_BW()                     (r32_PHY[AG2_PLL_BW] = 0x70163001)

#define AG12_PLL_BS                             (0x51C >> 2)
#define M_PCIE_CLR_AG12_PLL_BS()                (r32_PHY[AG12_PLL_BS] &= (~(BITMSK(6, 5))))
#define M_PCIE_SET_AG12_PLL_BS(x)               (r32_PHY[AG12_PLL_BS] |= (x<<5))

#define R32_TERMINATION_NOISE_THRESHOLD         (0x540 >> 2)
#define M_PCIE_TERMINATION_NOISE_THRESHOLD()    (r32_PHY[R32_TERMINATION_NOISE_THRESHOLD] |= (BIT17|BIT18|BIT19))
#define M_PCIE_TERMINATION()                    (r32_PHY[R32_TERMINATION_NOISE_THRESHOLD] |= (BIT17))

#define BAND_SEL_FORCE                          (0x550 >> 2)
#define M_PCIE_AG12_BAND_SEL_FORCE_ENABLE()     (r32_PHY[BAND_SEL_FORCE] |= 0x00002000)
#define M_PCIE_AG3_BAND_SEL_FORCE_ENABLE()      (r32_PHY[BAND_SEL_FORCE] |= 0x00000020)
#define M_PCIE_CHECK_AG12_BAND_SEL_FORCE()      (r32_PHY[BAND_SEL_FORCE] & 0x00002000)
#define M_PCIE_CHECK_AG3_BAND_SEL_FORCE()       (r32_PHY[BAND_SEL_FORCE] & 0x00000020)


#define R32_PHY_DEBUG_PORT_1                    (0x608 >> 2)
#define M_PCIE_PHY_DEBUG_PORT_BAND_1()          (r32_PHY[R32_PHY_DEBUG_PORT_1] = 0x383F661A)

#define R8_PHY_EYE_INIT_1                       (0x60A)
#define M_PCIE_GET_EYE_INIT_1()                 (r8_PHY[R8_PHY_EYE_INIT_1])
#define M_PCIE_SET_EYE_INIT_1(X)                (r8_PHY[R8_PHY_EYE_INIT_1] = (U8)(X))
#define M_PCIE_EYE_INIT_1_LANE_0()              (M_PCIE_SET_EYE_INIT_1(0x12))
#define M_PCIE_EYE_INIT_1_LANE_1()              (M_PCIE_SET_EYE_INIT_1(0x32))
#define M_PCIE_EYE_INIT_1_LANE_2()              (M_PCIE_SET_EYE_INIT_1(0x5F))
#define M_PCIE_EYE_INIT_1_LANE_3()              (M_PCIE_SET_EYE_INIT_1(0x70))

#define R8_PHY_EYE_TRIGGER                      (0x60C)
#define M_PCIE_SET_EYE_TRIGGER()                (r8_PHY[R8_PHY_EYE_TRIGGER] = 0x88)
#define M_PCIE_CLEAR_EYE_TRIGGER()              (r8_PHY[R8_PHY_EYE_TRIGGER] = 0x08)

#define R32_PHY_DEBUG_PORT_0                    (0x614 >> 2)
#define M_PCIE_PHY_DEBUG_PORT_PHY_STATE()       (r32_PHY[R32_PHY_DEBUG_PORT_0] = 0x0352CC0F)
#define M_PCIE_PHY_DEBUG_PORT_BAND_0()          (r32_PHY[R32_PHY_DEBUG_PORT_0] = 0x0342CC0F)

#define R8_PHY_EYE_INIT_2                       (0x616)
#define M_PCIE_RESTORE_EYE_INIT_2()             (r8_PHY[R8_PHY_EYE_INIT_2] = 0x42)

#define PCIE_TRIMSEL_CHANGE                     (0x65C >> 2)
#define M_PCIE_AG12_TRIMSEL_CHANGE()            (r32_PHY[PCIE_TRIMSEL_CHANGE] |= 0x00000020)
#define M_PCIE_AG3_TRIMSEL_CHANGE()             (r32_PHY[PCIE_TRIMSEL_CHANGE] |= 0x00000010)

#define R8_PHY_EYE_Y1                           (0x745)
#define R8_PHY_EYE_Y2                           (0x746)
#define R8_PHY_EYE_Y3                           (0x747)
#define M_PCIE_SET_EYE_LANE_0_Y(Y)              do { \
                                                    r8_PHY[R8_PHY_EYE_Y1] = (U8)((Y) & BITMSK(8, 0)); \
                                                    r8_PHY[R8_PHY_EYE_Y2] = (U8)(((Y) >> 8) & BITMSK(4, 0)); /* Bit0 ~ Bit3 */ \
                                                } while (0)
#define M_PCIE_SET_EYE_LANE_1_Y(Y)              do { \
                                                    r8_PHY[R8_PHY_EYE_Y2] = (U8)(((Y) & BITMSK(4, 0)) << 4); /* Bit4 ~ Bit7 */ \
                                                    r8_PHY[R8_PHY_EYE_Y3] = (U8)(((Y) >> 4) & BITMSK(8, 0)); \
                                                } while (0)

#define R8_PHY_EYE_Y4                           (0x769)
#define R8_PHY_EYE_Y5                           (0x76A)
#define R8_PHY_EYE_Y6                           (0x76B)
#define M_PCIE_SET_EYE_LANE_2_Y(Y)              do { \
                                                    r8_PHY[R8_PHY_EYE_Y4] = (U8)((Y) & BITMSK(8, 0)); \
                                                    r8_PHY[R8_PHY_EYE_Y5] = (U8)(((Y) >> 8) & BITMSK(4, 0)); /* Bit0 ~ Bit3 */ \
                                                } while (0)
#define M_PCIE_SET_EYE_LANE_3_Y(Y)              do { \
                                                    r8_PHY[R8_PHY_EYE_Y5] = (U8)(((Y) & BITMSK(4, 0)) << 4); /* Bit4 ~ Bit7 */ \
                                                    r8_PHY[R8_PHY_EYE_Y6] = (U8)(((Y) >> 4) & BITMSK(8, 0)); \
                                                } while (0)

#define M_PCIE_CLEAR_EYE_ALL_LANE_Y(Y)          do { \
                                                    M_PCIE_SET_EYE_LANE_0_Y(0); \
                                                    M_PCIE_SET_EYE_LANE_1_Y(0); \
                                                    M_PCIE_SET_EYE_LANE_2_Y(0); \
                                                    M_PCIE_SET_EYE_LANE_3_Y(0); \
                                                } while (0)

#define R32_PHY_DEBUG_PORT_VALUE                (0x780 >> 2)
#define M_PCIE_GET_AG12_PLL_BS()                ((r32_PHY[R32_PHY_DEBUG_PORT_VALUE] & BITMSK(6, 26)) >> 26)
#define M_PCIE_GET_AG3_PLL_BS()                 (r32_PHY[R32_PHY_DEBUG_PORT_VALUE] & BITMSK(6, 0))
#define M_PCIE_GET_PHY_STATE()                  (r32_PHY[R32_PHY_DEBUG_PORT_VALUE] & BITMSK(4, 0))

#define R8_PHY_EYE_X_LEFT                       (0x781)
#define     EYE_X_DONE_BIT                      (BIT6)
#define M_PCIE_GET_EYE_X_LEFT()                 (r8_PHY[R8_PHY_EYE_X_LEFT] & BITMSK(5, 0))
#define M_PCIE_CHECK_EYE_FLOW_DONE()            (r8_PHY[R8_PHY_EYE_X_LEFT] & BIT6)

#define R8_PHY_EYE_X_RIGHT                      (0x782)
#define M_PCIE_GET_EYE_X_RIGHT()                (r8_PHY[R8_PHY_EYE_X_RIGHT] & BITMSK(5, 0))

#define PHY_ACTIVITY_STATE                      (0x6)
#define M_PCIE_CHECK_PHY_STATE_ACTIVITY()       (PHY_ACTIVITY_STATE == M_PCIE_GET_PHY_STATE())
#endif /*(!PS5021_EN)*/


/*================================= END of Registers ================================*/

#endif /* _SHR_REG_PCIE_H_ */
#endif /* (NVME == HOST_MODE) */
