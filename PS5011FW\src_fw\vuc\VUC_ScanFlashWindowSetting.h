#ifndef _VUC_SCANFLASHWINDOWSETTING_H_
#define _VUC_SCANFLASHWINDOWSETTING_H_
#include "env.h"
#include "host/VUC_host.h"
#include "setup.h"
#include "typedef.h"
#define IO_TYPE_MASK  				(0xFEFFFFFF)
#define ODT_EN_MASK  				(0xFBFFFFFF)
#define PARAMETER_CNT				((PS5017_EN || PS5021_EN) ? 60 : 50)
#define ODT_ENABLE					(1)
#define ODT_DISABLE				(PS5017_EN ? 0 : 2)
#define IO_TYPE_DIFFRENTIAL			(1)
#define IO_TYPE_SINGLE				(0)
#define DRIVE_UNDERDRIVE			(2)
#define DRIVE_NORMAL			(4)
#define DRIVE_OVERDRIVE			(6)
#if((IM_B47R) || IM_B37R || (IM_N48R))
#define MICRON_NORMAL			(4)
#else /* ((IM_B47R) || (IM_N48R_NEED_CHECK)) */
#define MICRON_NORMAL			(2)
#endif /* ((IM_B47R) || (IM_N48R_NEED_CHECK)) */
#if PS5017_EN
#define MICRON_OVERDRIVE			(1)
#endif /* PS5017_EN */
#define MICRON_UNDERDRIVE			(3)


#define VUC_SCAN_FLASH_WINDOW_SETTING_FLASH_DRIVING_COMPARE_FAIL	(0x0A)
#define VUC_SCAN_FLASH_WINDOW_SETTING_FLASH_ODT_COMPARE_FAIL	(0x0B)
#define VUC_SCAN_FLASH_WINDOW_SETTING_FLAHS_VREF_MASK			(0xF)

#if (PS5021_EN)
typedef struct ScanFlhWindowSet { //17
	union {
		U8 ubValue[PARAMETER_CNT];
		//MP
		struct {
			U8 ubFlashDataRateRead;  //byte 0
			U8 ubFlashDataRateWrite; //byte 1
			U8 ubFlhOdtReadEn;		 //byte 2
			U8 ubFlhOdtRead;		 //byte 3
			U8 ubFlhOdtWriteEn;		 //byte 4
			U8 ubFlhOdtWrite;		 //byte 5
			U8 ubFlashODTMatrixTermination;//byte 6
			U8 ubFlhDriveEn;		 //byte 7

			U8 ubFlhDrive;			 //byte 8
			U8 ubPadOdtEn;			 //byte 9
			U8 ubPadOdt;			 //byte 10
			U8 ubPadDriveEn;		 //byte 11
			U8 ubPadDriveALE; 		 //byte 12
			U8 ubPadDriveCLE; 		 //byte 13
			U8 ubPadDriveWE; 		 //byte 14
			U8 ubPadDriveRE;	//byte 15

			U8 ubPadDriveDQS;	//byte 16
			U8 ubPadDriveDATA;	//byte 17
			U8 ubPadDriveFRDY;		 //byte 18
			U8 ubPadDriveWP;		 //byte 19
			U8 ubPadDriveCE;		 //byte 20
			U8 ubDutyCycleRE;		 //byte 21
			U8 ubDutyCycleDQS;		 //byte 22
			U8 ubDutyCycleDAT;		 //byte 23

			U8 ubIOType;			 //byte 24
			U8 ubWEPad_slewrate; 	 //byte 25
			U8 ubREPad_slewrate;	 //byte 26
			U8 ubDQSPad_slewrate;	 //byte 27
			U8 ubDQPad_slewrate;	 //byte 28
			U8 ubALE_slewrate;		 //byte 29
			U8 ubCLE_slewrate; 		 //byte 30
			U8 ubCE_slewrate;		 //byte 31

			U8 ubWP_slewrate;		 //byte 32
			U8 ubRDY_slewrate;		 //byte 33
			U8 ubWarmupcycleRead;	 //byte 34
			U8 ubWarmupcycleWrite; 	 //byte 35
			U8 ubNandZQCL;			 //byte 36
			U8 ubReserve[13];		//byte 37-49
		} type;
	} value;
} ScanFlhWindowSet_t;

#elif (PS5017_EN)
typedef struct ScanFlhWindowSet { //17
	union {

		U8 ubValue[PARAMETER_CNT];
		//MP
		struct {
			U8 ubFlashDataRateRead;  //byte 0
			U8 ubFlashDataRateWrite; //byte 1
			U8 ubFlhOdtReadEn;		 //byte 2
			U8 ubFlhOdtRead;		 //byte 3
			U8 ubFlhOdtWriteEn;		 //byte 4
			U8 ubFlhOdtWrite;		 //byte 5
			U8 ubFlashODTMatrixTermination;//byte 6
			U8 ubFlhDriveEn;		 //byte 7
			U8 ubFlhDrive;			 //byte 8
			U8 ubPadOdtEn;			 //byte 9
			U8 ubPadOdt;			 //byte 10
			U8 ubPadDriveEn;		 //byte 11
			U8 ubPadDriveALE; 		 //byte 12
			U8 ubPadDriveCLE; 		 //byte 13
			U8 ubPadDriveWE; 		 //byte 14
			U8 ubPadDriveRE[MAX_CHANNEL];	//byte 15-16
			U8 ubPadDriveDQS[MAX_CHANNEL];	//byte 17-18
			U8 ubPadDriveDATA[MAX_CHANNEL];	//byte 19-20
			U8 ubPadDriveFRDY;		 //byte 21
			U8 ubPadDriveWP;		 //byte 22
			U8 ubPadDriveCE;		 //byte 23
			U8 ubDutyCycleRE;		 //byte 24
			U8 ubDutyCycleDQS;		 //byte 25
			U8 ubDutyCycleDAT;		 //byte 26
			U8 ubIOType;			 //byte 27
			//17 slew rate ???HW???(no use)
			U8 ubWEPad_slewrate; 	 //byte 28
			U8 ubREPad_slewrate;	 //byte 29
			U8 ubDQSPad_slewrate;	 //byte 30
			U8 ubDQPad_slewrate;	 //byte 31
			U8 ubALE_slewrate;		 //byte 32
			U8 ubCLE_slewrate; 		 //byte 33
			U8 ubCE_slewrate;		 //byte 34
			U8 ubWP_slewrate;		 //byte 35
			U8 ubRDY_slewrate;		 //byte 36

			U8 ubWarmupcycleRead;	 //byte 37
			U8 ubWarmupcycleWrite; 	 //byte 38
			U8 ubNandZQCL;			 //byte 39
			U8 ubReserve[20];		 //byte40-59
		} type;
	} value;
} ScanFlhWindowSet_t;
#else /* (PS5021_EN) */
typedef struct ScanFlhWindowSet {
	union {
		U8 ubValue[PARAMETER_CNT];
		struct {
			U8 ubFlashDataRateRead;
			U8 ubFlashDataRateWrite;
			U8 ubFlhOdtEn;
			U8 ubFlhOdt_CH0;
			U8 ubFlhDriveEn;
			U8 ubFlhDrive;
			U8 ubPadOdtEnDQS;
			U8 ubPadOdt;
			U8 ubPadDriveEn;
			U8 ubPadDriveALE1_2V;
			U8 ubPadDriveALE1_8V;
			U8 ubPadDriveCLE1_2V;
			U8 ubPadDriveCLE1_8V;
			U8 ubPadDriveWE;
			U8 ubPadDriveRE_CH0;
			U8 ubPadDriveDQS_CH0;
			U8 ubPadDriveDATA_CH0;
			U8 ubPadDriveFRDY1_2V;
			U8 ubPadDriveWP1_2V;
			U8 ubPadDriveCE1_2V;
			U8 ubPadDriveFRDY1_8V;
			U8 ubPadDriveWP1_8V;
			U8 ubPadDriveCE1_8V;
			U8 ubDutyCycleRE;
			U8 ubDutyCycleDQS;
			U8 ubDutyCycleDAT;
			U8 ubIOType;
			U8 ubPadSRWE1_2V;
			U8 ubPadSRRE1_2V;
			U8 ubPadSRDQS1_2V;
			U8 ubPadSRDQ1_2V;
			U8 ubPadSRWE1_8V;
			U8 ubPadSRRE1_8V;
			U8 ubPadSRDQS1_8V;
			U8 ubPadSRDQ1_8V;
			U8 ubPadSRALE;
			U8 ubPadSRCLE;
			U8 ubPadSRCE;
			U8 ubPadSRWP;
			U8 ubPadSRRDY;
			U8 ubNandZQCL;
			U8 ubPadDriveDQS_CH1;
			U8 ubPadDriveDATA_CH1;
			U8 ubPadDriveDQS_CH2;
			U8 ubPadDriveDATA_CH2;
			U8 ubPadDriveDQS_CH3;
			U8 ubPadDriveDATA_CH3;
			U8 ubFlhOdt_CH1;
			U8 ubFlhOdt_CH2;
			U8 ubFlhOdt_CH3;
			U8 ubPadDriveRE_CH1;
			U8 ubPadDriveRE_CH2;
			U8 ubPadDriveRE_CH3;
		} type;
	} value;
} ScanFlhWindowSet_t;
#endif /* (PS5021_EN) */

extern volatile ScanFlhWindowSet_t gWindowSetValue;

void VUC_ScanFlashWindowSetting(VUC_OPT_HCMD_PTR_t pCmd);

#endif /* _VUC_SCANFLASHWINDOWSETTING_H_ */
