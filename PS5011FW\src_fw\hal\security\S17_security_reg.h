
/****************************************************************************
*
*  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
*  All rights reserved
*
*  The content of this document is confidential and shall be applied
*  subject to the terms and conditions of the license agreement and
*  other applicable laws. Any unauthorized access, use or disclosure
*  of this document is strictly prohibited and may be punishable
*  under laws.
*
*  S17_security_reg.h
*
*
*
****************************************************************************/

#ifndef _S17_SECURITY_REG_H_
#define _S17_SECURITY_REG_H_

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "mem.h"

/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */
#define SECURITY_REG_BASE			SECURITY_REG_ADDRESS

//Register Address
#define SECURITY_SHA_OFFSET			(0x000)
#define SECURITY_PKER_OFFSET		(0x100)
#define SECURITY_MEMR_OFFSET		(0x200)
#define SECURITY_AES_OFL_OFFSET		(0x300)
#define SECURITY_AES_OTF_OFFSET		(0x400)
#define SECURITY_MEMR_ADDR   		(SECURITY_REG_BASE + SECURITY_MEMR_OFFSET)
//SRAM Address
#define SECURITY_MEMK_ADDR			(SEC_KEY_RAM_ADDRESS)	//E17, 7KB, 0x012F3800~0x012F53FF
#define SECURITY_MEMQ_ADDR			(SEC_PKE_RAM_ADDRESS)	//E17, 2KB, 0x012F1800~0x012F1FFF
#define SECURITY_MEMA_ADDR			(SECURITY_MEMQ_ADDR + 0x800)	//E17, 2KB, 0x012F2000~0x012F27FF
#define SECURITY_MEMB_ADDR			(SECURITY_MEMA_ADDR + 0x800)	//E17, 2KB, 0x012F2800~0x012F2FFF
#define SECURITY_MEMC_ADDR			(SECURITY_MEMB_ADDR + 0x800)	//E17, 2KB, 0x012F3000~0x012F37FF

#define SECURITY_ENCRYPT_KEY_BASE	(SECURITY_MEMK_ADDR)
#define SECURITY_LBA_KEY_BASE		(SECURITY_MEMK_ADDR + 0x0020)
#define SECURITY_DECRYPT_KEY_BASE	(SECURITY_MEMK_ADDR + 0x0040)
#define SECURITY_KEY_IDX_OFFSET		(0x60) //32 Byte Key + 32 Byte LBA Key + 32 Byte Decryption Key (Generated By Hardware)

/*============= SHA ============= */
#define SECURITY_SHA_REG_BASE		(SECURITY_REG_BASE + SECURITY_SHA_OFFSET)
#define R8_SECURITY_SHA 			((REG8 *)SECURITY_SHA_REG_BASE)
#define R16_SECURITY_SHA			((REG16 *)SECURITY_SHA_REG_BASE)
#define R32_SECURITY_SHA			((REG32 *)SECURITY_SHA_REG_BASE)
#define R64_SECURITY_SHA			((REG64 *)SECURITY_SHA_REG_BASE)
#define R32_SECURITY_SHA_FUNC				(0x00 >> 2)
#define R8_SECURITY_SHA_FUNC         		(0x00 >> 0)
#define 	SECURITY_SHA_FUNC_INIT_VALUE         (0x80000000)
#define 	SHA_FIRST_ROUND_SHIFT			(0)
#define 	SHA_FIRST_ROUND_MASK 			(BIT_MASK(1))

#define 	SHA_FINAL_ROUND_SHIFT 			(1)
#define 	SHA_FINAL_ROUND_MASK 			(BIT_MASK(1))

#define 	SHA_MODE_SHIFT 					(2)
#define 	SHA_MODE_MASK					(BIT_MASK(3))
#define 	SECURITY_SHA_MODE_SHA160     		(0)
#define 	SECURITY_SHA_MODE_SHA256     		(1)
#define 	SECURITY_SHA_MODE_SHA512     		(2)
#define 	SECURITY_SHA_MODE_SM3        		(3)

#define 	SHA_BIT_NUM_SHIFT 				(8)
#define 	SHA_BIT_NUM_MASK				(BIT_MASK(11))
#define 	SECURITY_SHA_256_BIT_NUM 			(0x200)
#define 	SECURITY_SHA_512_BIT_NUM 			(0x400)
#define 	SECURITY_HMAC_BIT_NUM_256 			(0x100)
#define 	SECURITY_HMAC_BIT_NUM_512 			(0x200)
#define 	SECURITY_HMAC_BIT_NUM_1024 			(0x400)

#define 	HMAC_FIRST_SHIFT 				(24)
#define 	HMAC_FIRST_MASK 				(BIT_MASK(1))

#define 	HMAC_FINAL_SHIFT 				(25)
#define 	HMAC_FINAL_MASK 				(BIT_MASK(1))

#define 	SHA_AXI_PAC32_SHIFT				(31)
#define 	SHA_AXI_PAC32_MASK 				(BIT_MASK(1))

#define R32_SECURITY_SHA_CTRL				(0x04 >> 2)
#define R8_SECURITY_SHA_CTRL				(0x04 >> 0)
#define 	SHA_START_SHIFT					(0)
#define 	SHA_START_MASK 					(BIT_MASK(1))

#define R32_SECURITY_SHA_SOC_ADDR			(0x08 >> 2)

#define R32_SECURITY_SHA_TAG_ADDR			(0x0C >> 2)

#define R16_SECURITY_SHA_TRIG_CNT			(0x18 >> 1)
#define 	SHA_TRIG_CNT_SHIFT				(0)
#define 	SHA_TRIG_CNT_MASK				(BIT_MASK(10))
#define 	SECURITY_SHA_MAX_TRIGGER_CNT 		(BIT10 - 1)
#define 	SECURITY_SHA_TRIGGER_CNT_INIT_VALUE (1)

#define SECURITY_SHA_HMAC_KEY_BASE    	(SECURITY_SHA_REG_BASE + 0x20)

/*=============	PKER ============= */
#define SECURITY_PKE_MEMORY_Q_ADDR	(SECURITY_MEMQ_ADDR)
#define SECURITY_PKE_MEMORY_A_ADDR	(SECURITY_MEMA_ADDR)
#define SECURITY_PKE_MEMORY_B_ADDR	(SECURITY_MEMB_ADDR)
#define SECURITY_PKE_MEMORY_C_ADDR	(SECURITY_MEMC_ADDR)
#define SECURITY_PKE_MEMORY_R_ADDR	(SECURITY_MEMR_ADDR)

#define SECURITY_PKER_REG_BASE		(SECURITY_REG_BASE + SECURITY_PKER_OFFSET)
#define R32_SECURITY_PKER 				((REG32 *)(SECURITY_PKER_REG_BASE))
#define R32_SECURITY_PKE_RAM_CLEAR		(0x1C >> 2)
#define 	SECURITY_RAM_CLR_TRIGGER_BIT	BIT0
#define 	SECURITY_RAMQ_CLR_TRIGGER_BIT	BIT1

#define R8_PKER 				((REG8 *)(SECURITY_REG_BASE + SECURITY_PKER_OFFSET))
#define R16_PKER 				((REG16 *)(SECURITY_REG_BASE + SECURITY_PKER_OFFSET))
#define R32_PKER 				((REG32 *)(SECURITY_REG_BASE + SECURITY_PKER_OFFSET))

#define	R32_PKE_EN				(0x08 >> 2)
#define		PKE_EN_BIT			BIT0
#define		PKE_CMD_CLR_BIT		BIT8
#define		PKE_BLEDN_BIT		BIT16

#define	R32_PKE_TB				(0x0C >> 2)
#define		TB_WR_BIT			BIT24
#define		TB_W_VAL_BIT		BIT25

#define	R16_TB_VAL				(0x0C >> 1)

#define	R8_TB_WR_SEL			(0x0E)

#define	R32_PKE_STA				(0x10 >> 2)
#define	R8_PKE_STA				(0x10)
#define		CMDQ_OP_BUSY_BIT	BIT0
#define		WR_PTR_SET_BIT		BIT3

#define	R8_CMDQ_WR_PTR			(0x13)

#define R32_PKE_RAM_CLEAR			(0x1C >> 2)
#define 	RAM_CLR_TRIG			BIT0
#define		RAMQ_CLR_TRIG			BIT1

/*=============	AES_OFL ============= */
#define SECURITY_AES_OFL_REG_BASE		(SECURITY_REG_BASE + SECURITY_AES_OFL_OFFSET)
#define R8_SECURITY_AES_OFL				((REG8 *)(SECURITY_AES_OFL_REG_BASE))
#define R16_SECURITY_AES_OFL			((REG16 *)(SECURITY_AES_OFL_REG_BASE))
#define R32_SECURITY_AES_OFL			((REG32 *)(SECURITY_AES_OFL_REG_BASE))

#define R32_SECURITY_AES_OFL_FUNC			(0x00 >> 2)
//EN_DE(1 bit)
#define SECURITY_AES_OFFLINE_FUNCTION_DEFAULT_VALUE (0)
#define 	AES_OFL_EN_DE_SHIFT				(0)
#define 	AES_OFL_EN_DE_MASK				(BIT_MASK(1))
#define 	SECURITY_AES_OFFLINE_DECRYPT		(0)
#define 	SECURITY_AES_OFFLINE_ENCRYPT		(1)
//KEY256(1 bit)
#define 	AES_OFL_KEY_MODE_SHIFT			(1)
#define 	AES_OFL_KEY_MODE_MASK			(BIT_MASK(1))
#define 	SECURITY_AES_OFFLINE_KEY_MODE_128	(0)
#define 	SECURITY_AES_OFFLINE_KEY_MODE_256	(1)
//KEY INDEX(5 bit)
#define 	AES_OFL_KEY_IDX_SHIFT			(16)
#define 	AES_OFL_KEY_IDX_MASK			(BIT_MASK(7))
//rsv(1 bit)
//OFL_MODE(3 bit)
#define 	AES_OFL_MODE_SHIFT				(8)
#define 	AES_OFL_MODE_MASK				(BIT_MASK(3))
#define 	SECURITY_AES_OFFLINE_MODE_XTS		(0)
#define 	SECURITY_AES_OFFLINE_MODE_CTR		(1)
#define 	SECURITY_AES_OFFLINE_MODE_CBC		(2)
#define 	SECURITY_AES_OFFLINE_MODE_ECB		(3)
#define 	SECURITY_AES_OFFLINE_MODE_OFB		(4)
//rsv(1 bit)
//LSBFST(1 bit)
#define 	AES_OFL_LSB_FIRST_SHIFT			(12)
#define 	AES_OFL_LSB_FIRST_MASK			(BIT_MASK(1))
#define 	SECURITY_AES_OFFLINE_MSB_FIRST		(0)
#define 	SECURITY_AES_OFFLINE_LSB_FIRST		(1)
//rsv(3 bit)
//rsv(7 bit)
//SM4_EN(1 bit)
#define 	AES_OFL_SM4_EN_SHIFT			(13)
#define 	AES_OFL_SM4_EN_MASK				(BIT_MASK(1))
#define 	SECURITY_AES_OFFLINE_SM4_DIS		(0)
#define 	SECURITY_AES_OFFLINE_SM4_EN			(1)

#define R32_SECURITY_AES_OFL_CTRL			(0x04 >> 2)
//AES_START(1 bit)
#define 	AES_OFL_START_SHIFT				(0)
#define 	AES_OFL_START_MASK				(BIT_MASK(1))
#define 	SECURITY_AES_OFFLINE_START_BIT		BIT0
//rsv(31 bit)

#define R32_SECURITY_AES_OFL_SOC_ADDR		(0x08>>2)

#define R32_SECURITY_AES_OFL_TAG_ADDR		(0x0c>>2)

#define R16_SECURITY_AES_OFL_SEC_CNT		(0x10>>1)
#define R32_SECURITY_AES_OFL_SEC_CNT		(0x10>>2)
//SEC_CNT(16 bit)
#define 	AES_OFL_SEC_CNT_SHIFT			(0)
#define 	AES_OFL_SEC_CNT_MASK 			(BIT_MASK(16))
//BLK_NUM(6 bit)
#define 	AES_OFL_BLK_NUM_SHIFT			(16)
#define 	AES_OFL_BLK_NUM_MASK			(BIT_MASK(6))
//rsv(10 bit)

#define R32_SECURITY_AES_OFL_LBA			(0x14>>2)
#define R32_SECURITY_AES_OFL_LBA_1			(0x18>>2)
#define R32_SECURITY_AES_OFL_LBA_2			(0x1c>>2)
#define R32_SECURITY_AES_OFL_LBA_3			(0x20>>2)
#define SECURITY_AES_OFL_LBA_H_OFFSET		(0x001C)
#define SECURITY_AES_OFL_LBA_L_OFFSET		(0x0014)
#define SECURITY_AES_OFL_LBA_L_BASE			(SECURITY_AES_OFL_REG_BASE + SECURITY_AES_OFL_LBA_L_OFFSET)
#define SECURITY_AES_OFL_LBA_H_BASE			(SECURITY_AES_OFL_REG_BASE + SECURITY_AES_OFL_LBA_H_OFFSET)
#define SECURITY_AES_OFFLINE_LBA_DEFAULT_VALUE	((U64)0)

#define SECURITY_AES_OFL_INIV_BASE			(SECURITY_AES_OFL_REG_BASE + 0x0024)
#define R32_SECURITY_AES_OFL_INI_VECTOR_1	(0x24>>2)
#define R32_SECURITY_AES_OFL_INI_VECTOR_2	(0x28>>2)
#define R32_SECURITY_AES_OFL_INI_VECTOR_3	(0x2c>>2)
#define R32_SECURITY_AES_OFL_INI_VECTOR_4	(0x30>>2)

#define R32_SECURITY_AES_OFL_DEBUG			(0x38>>2)
//DEBUG_SEL(4 bit)
#define 	AES_OFL_DEBUG_PORT_SHIFT		(0)
#define 	AES_OFL_DEBUG_PORT_MASK			(BIT_MASK(4))
//DEBUG_EN(1 bit)
#define 	AES_OFL_DEBUG_EN_SHIFT			(4)
#define 	AES_OFL_DEBUG_EN_MASK			(BIT_MASK(1))
#define 	SECURITY_AES_OFFLINE_DEBUG_UN_SELECT	(0)
#define 	SECURITY_AES_OFFLINE_DEBUG_SELECT_PORT	(1)

#define R32_SECURITY_AES_OFL_AXI_MODE		(0x3c>>2)
//RR_EN(1 bit)
#define 	AES_OFL_AXI_PRIORITY_CONTROL_SHIFT		(0)
#define 	AES_OFL_AXI_PRIORITY_CONTROL_MASK		(BIT_MASK(1))
#define 	SECURITY_AES_OFFLINE_AXI_PRIORITY_CONTROL_HIGH_TO_LOW	(0)
#define 	SECURITY_AES_OFFLINE_AXI_PRIORITY_CONTROL_ROTATE		(1)
//ARB_EN(1 bit)
#define 	AES_OFL_AXI_ARB_ENHANCE_SHIFT			(1)
#define 	AES_OFL_AXI_ARB_ENHANCE_MASK			(BIT_MASK(1))
#define 	SECURITY_AES_OFFLINE_AXI_ARB_NORMAL_MODE	(0)
#define 	SECURITY_AES_OFFLINE_AXI_ARB_ENHANCE_MODE	(1)
//WR_DONE_SEL(1 bit)
#define 	AES_OFL_AXI_WR_DONE_SEL_SHIFT			(2)
#define 	AES_OFL_AXI_WR_DONE_SEL_MASK			(BIT_MASK(1))
#define 	SECURITY_AES_OFFLINE_AXI_WRITE_DONE_SELECT_BY_BVALID	(0)
#define 	SECURITY_AES_OFFLINE_AXI_WRITE_DONE_SELECT_BY_WLAST		(1)
//rsv(1 bit)
//RESP_FAIL_FRC(3 bit)
#define 	AES_OFL_AXI_RESP_FAIL_FRC_SHIFT			(4)
#define 	AES_OFL_AXI_RESP_FAIL_FRC_MASK			(BIT_MASK(3))
#define 	SECURITY_AES_OFFLINE_AXI_PKE_RESPOND_FAIL	(1)
#define 	SECURITY_AES_OFFLINE_AXI_SHA_RESPOND_FAIL	(2)
#define 	SECURITY_AES_OFFLINE_AXI_RESPOND_FAIL		(4)
//rsv(25 bit)

#define R32_SECURITY_AES_OFL_KWRAP_DAT		(0x40>>2)

#define R32_SECURITY_AES_OFL_KWRAP_FUNC		(0x44>>2)
//AES_OFL_KWRAP_N4_TEXT_LEN(1 bit)
#define 	AES_OFL_KWRAP_N4_SHIFT			(0)
#define 	AES_OFL_KWRAP_N4_MASK			(BIT_MASK(1))
#define 	SECURITY_AES_OFFLINE_KEY_WRAP_N4_TEXT_LEN_128	(0)
#define 	SECURITY_AES_OFFLINE_KEY_WRAP_N4_TEXT_LEN_256	(1)
//rsv (3 bit)
//KWRAP_EN(1bit)
#define 	AES_OFL_KWRAP_EN_SHIFT			(4)
#define 	AES_OFL_KWRAP_EN_MASK			(BIT_MASK(1))
#define 	SECURITY_AES_OFFLINE_KEY_WRAP_NORMAL	(0)
#define 	SECURITY_AES_OFFLINE_KEY_WRAP_EN		(1)
//rsv (3 bit)
//KWRAP_TRIG(1 bit)
#define 	AES_OFL_KWRAP_TRIG_SHIFT		(8)
#define 	AES_OFL_KWRAP_TRIG_MASK			(BIT_MASK(1))
#define 	SECURITY_AES_OFFLINE_KEY_WRAP_TRIGGER_BIT			BIT8

//KWRAP Result Data Register
#define R32_SECURITY_AES_OFL_KWRAP_RLT_0	(0x48>>2)
#define R32_SECURITY_AES_OFL_KWRAP_RLT_1	(0x4c>>2)
#define R32_SECURITY_AES_OFL_KWRAP_RLT_2	(0x50>>2)
#define R32_SECURITY_AES_OFL_KWRAP_RLT_3	(0x54>>2)
#define R32_SECURITY_AES_OFL_KWRAP_RLT_4	(0x58>>2)
#define R32_SECURITY_AES_OFL_KWRAP_RLT_5	(0x5c>>2)
#define R32_SECURITY_AES_OFL_KWRAP_RLT_6	(0x60>>2)
#define R32_SECURITY_AES_OFL_KWRAP_RLT_7	(0x64>>2)
#define R32_SECURITY_AES_OFL_KWRAP_RLT_8	(0x68>>2)
#define R32_SECURITY_AES_OFL_KWRAP_RLT_9	(0x6c>>2)

/*=============	AES_OTF ============= */
#define SECURITY_AES_OTF_REG_BASE			(SECURITY_REG_BASE + SECURITY_AES_OTF_OFFSET)
#define R8_SECURITY_AES_OTF					((REG8 *)(SECURITY_AES_OTF_REG_BASE))
#define R16_SECURITY_AES_OTF				((REG16 *)(SECURITY_AES_OTF_REG_BASE))
#define R32_SECURITY_AES_OTF				((REG32 *)(SECURITY_AES_OTF_REG_BASE))

#define R32_SECURITY_AES_OTF_OPERATION		(0x00 >> 2) // SEC Reg Name: AES FUNC in AES_OTF
#define SECURITY_AES_ON_THE_FLY_OPERATION_DEFAULT_VALUE (0x8000190C)
#define 	AES_OTF_BYPASS_SHIFT			(0)
#define 	AES_OTF_BYPASS_MASK				(BIT_MASK(1))
#define 	SECURITY_AES_ON_THE_FLY_BYPASS_NORMAL	(0)
#define 	SECURITY_AES_ON_THE_FLY_BYPASS			(1)

#define 	AES_OTF_ACT_ALW_ON_SHIFT		(1)
#define 	AES_OTF_ACT_ALW_ON_MASK			(BIT_MASK(1))
#define 	SECURITY_AES_ON_THE_FLY_ACT_ALW_ON_NORMAL		(0)
#define 	SECURITY_AES_ON_THE_FLY_ACT_ALW_ON_NEVER_GATED	(1)

#define 	AES_OTF_KEY_IDX_SHIFT			(16)
#define 	AES_OTF_KEY_IDX_MASK			(BIT_MASK(7))
#define		SECURITY_AES_ON_THE_FLY_DEFAULT_KEY_IDX	(0)

#define 	AES_OTF_KEY_READ_DIS_SHIFT		(7)
#define 	AES_OTF_KEY_READ_DIS_MASK		(BIT_MASK(1))
#define 	SECURITY_AES_ON_THE_FLY_KEY_READ_NORMAL	(0)
#define 	SECURITY_AES_ON_THE_FLY_KEY_READ_DIS	(1)

#define 	AES_OTF_D2H_MCORE_SHIFT			(8)
#define 	AES_OTF_D2H_MCORE_MASK			(BIT_MASK(1))
#define 	SECURITY_AES_ON_THE_FLY_DEVICE_TO_HOST_MCORE_ONE		(0)
#define 	SECURITY_AES_ON_THE_FLY_DEVICE_TO_HOST_MCORE_DYNAMICAL	(1)

#define 	AES_OTF_SM4_EN_SHIFT			(9)
#define 	AES_OTF_SM4_EN_MASK				(BIT_MASK(1))
#define 	SECURITY_AES_ON_THE_FLY_SM4_NORMAL	(0)
#define 	SECURITY_AES_ON_THE_FLY_SM4_EN		(1)

#define 	AES_OTF_D2H_WR_SEG_SHIFT		(11)
#define 	AES_OTF_D2H_WR_SEG_MASK			(BIT_MASK(1))
#define 	SECURITY_AES_ON_THE_FLY_DEVICE_TO_HOST_WRITE_DATA_WHOLE	(0)
#define 	SECURITY_AES_ON_THE_FLY_DEVICE_TO_HOST_WRITE_DATA_PART	(1)

#define 	AES_OTF_D2H_PRIORITY_CONTROL_SHIFT	(12)
#define 	AES_OTF_D2H_PRIORITY_CONTROL_MASK	(BIT_MASK(1))
#define 	SECURITY_AES_ON_THE_FLY_DEVICE_TO_HOST_PRIORITY_CONTROL_HIGH_TO_LOW	(0)
#define 	SECURITY_AES_ON_THE_FLY_DEVICE_TO_HOST_PRIORITY_CONTROL_ROTATE		(1)

#define 	AES_OTF_GEN_KEY_EN_SHIFT		(30)
#define 	AES_OTF_GEN_KEY_EN_MASK			(BIT_MASK(1))
#define 	SECURITY_AES_ON_THE_FLY_GENERATE_KEY_NORMAL	(0)
#define 	SECURITY_AES_ON_THE_FLY_GENERATE_KEY_DIS	(1)

#define 	AES_OTF_KEY_CHG_DIR_SHIFT		(31)
#define 	AES_OTF_KEY_CHG_DIR_MASK		(BIT_MASK(1))
#define 	SECURITY_AES_ON_THE_FLY_KEY_CHANGE_DIRECTION_REGISTER_TO_TABLE	(0)
#define 	SECURITY_AES_ON_THE_FLY_KEY_CHANGE_DIRECTION_TABLE_TO_REGISTER	(1)

#define R8_SECURITY_AES_OTF_KEY_CHG			(0x04>>0)
#define 	AES_OTF_KEY_CHG_SHIFT			(0)
#define 	AES_OTF_KEY_CHG_MASK			(BIT_MASK(1))
#define 	SECURITY_AES_ON_THE_FLY_KEY_CHANGE_BIT	BIT0

#define R8_SECURITY_AES_OTF_KEY_GEN			(0x08>>0)
#define 	AES_OTF_KEY_GEN_SHIFT			(0)
#define 	AES_OTF_KEY_GEN_MASK			(BIT_MASK(1))
#define 	SECURITY_AES_ON_THE_FLY_KEY_GENERATE_BIT	BIT0

#define R8_SECURITY_AES_OTF_FUNC			(0x0c>>0)
#define R32_SECURITY_AES_OTF_FUNC			(0x0c>>2)
#define 	AES_OTF_ZIP_KEY_MODE_256_SHIFT		(0)
#define 	AES_OTF_ZIP_KEY_MODE_256_MASK		(BIT_MASK(1))
#define 	SECURITY_AES_ON_THE_FLY_ZIP_KEY_MODE_128	(0)
#define 	SECURITY_AES_ON_THE_FLY_ZIP_KEY_MODE_256	(1)

#define 	AES_OTF_D2H_KEY_MODE_256_SHIFT		(1)
#define 	AES_OTF_D2H_KEY_MODE_256_MASK		(BIT_MASK(1))
#define 	SECURITY_AES_ON_THE_FLY_DEVICE_TO_HOST_KEY_MODE_128	(0)
#define 	SECURITY_AES_ON_THE_FLY_DEVICE_TO_HOST_KEY_MODE_256	(1)

#define 	AES_OTF_LBA_FIX_0_SHIFT			(2)
#define 	AES_OTF_LBA_FIX_0_MASK			(BIT_MASK(1))
#define 	SECURITY_AES_ON_THE_FLY_LBA_FROM_HOST	(0)
#define 	SECURITY_AES_ON_THE_FLY_LBA_FIX_0		(1)

#define 	AES_OTF_LSB_FIRST_SHIFT			(3)
#define 	AES_OTF_LSB_FIRST_MASK			(BIT_MASK(1))
#define 	SECURITY_AES_ON_THE_FLY_MSB_FIRST		(0)
#define 	SECURITY_AES_ON_THE_FLY_LSB_FIRST		(1)

#define 	AES_OTF_D2H_RW_FLAG_SHIFT		(4)
#define 	AES_OTF_D2H_RW_FLAG_MASK		(BIT_MASK(1))

#define 	AES_OTF_ZIP_KEY_IDX_SHIFT		(8)
#define 	AES_OTF_ZIP_KEY_IDX_MASK		(BIT_MASK(7))

#define 	AES_OTF_ZIP_KEY_IDX_INIT_SHIFT	(5)
#define 	AES_OTF_ZIP_KEY_IDX_INIT_MASK	(BIT_MASK(1))
#define 	SECURITY_AES_ON_THE_FLY_ZIP_KEY_IDX_INIT_BIT	(BIT5)

#define 	AES_OTF_HOST_TAG_SHIFT			(16)
#define 	AES_OTF_HOST_TAG_MASK			(BIT_MASK(8))

#define 	AES_OTF_DMAC_TAG_SHIFT			(24)
#define 	AES_OTF_DMAC_TAG_MASK			(BIT_MASK(8))

#define R32_SECURITY_AES_ERR_INST			(0x10>>2)
#define 	AES_ERR_INST_CRC_FAIL_FLAG		(BIT25)

#define R32_SECURITY_AES_DEF_LCA			(0x14>>2)
#define R32_SECURITY_AES_DEF_KIDX			(0x18>>2)
#define R32_SECURITY_AES_DIR_DAT			(0x1c>>2)
#define R32_SECURITY_AES_DIR_FUNC			(0x20>>2)
#define R32_SECURITY_AES_DIR_RLT_0			(0x24>>2)

#define R32_SECURITY_AES_KRAM_CLEAR			(0x34 >> 2)
#define 	AES_OTF_KRAM_CLR_SHIFT			(0)
#define 	AES_OTF_KRAM_CLR_MASK			(BIT_MASK(1))
#define 	SECURITY_AES_ON_THE_FLY_KEY_RAM_CLEAR_TRIGGER_BIT	BIT0

#define R32_SECURITY_AES_PERR_ADR			(0x38>>2)
#define R32_SECURITY_AES_DEBUG				(0x3c>>2)
#endif /* _S17_SECURITY_REG_H_ */