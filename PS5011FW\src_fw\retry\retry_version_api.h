/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  retry_version_api.h                                          */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#ifndef _RETRY_VERSION_API_H_
#define _RETRY_VERSION_API_H_
/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "typedef.h"
/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */
#define RETRY_BICS3_DECODEING_VERSION         (1)
#define RETRY_EARLY_BICS4_256G_DECODEING_VERSION         (94)
#define RETRY_FINAL_BICS4_256G_DECODEING_VERSION         (149)
#if PS5017_EN
#define RETRY_BICS4_HDR_256G_DECODEING_VERSION         (40000029)
#define RETRY_BICS5_512G_2PLANE_DECODING_VERSION		(40000047)
#else /* PS5017_EN */
#define RETRY_BICS4_HDR_256G_DECODEING_VERSION         (240)
#endif /* PS5017_EN */
#define RETRY_BICS4_HDR_512G_DECODEING_VERSION         (0)	//TODO 
#define RETRY_BICS4_512G_DECODEING_VERSION         (165)
#define RETRY_BICS4_1360G_DECODEING_VERSION        (122)
#define RETRY_BICS4_1360G_PTLC_DECODEING_VERSION        (151)
#define RETRY_SANDISK_BICS3_256G_DECODING_VERSION	(0)  // TODO:
#define RETRY_SANDISK_BICS3_512G_DECODING_VERSION	(0)  // TODO:
#define RETRY_SANDISK_BICS4_256G_DECODING_VERSION	(132)
#define RETRY_SANDISK_BICS4_512G_DECODING_VERSION	(132)
#define RETRY_HYNIX_V6_512G_DECODING_VERSION		(0) //Hynix v6 RDT porting
#define RETRY_HYNIX_V7_512G_DECODING_VERSION        (0)//Hynix v7 RDT porting
#define RETRY_HYNIX_V8_512G_DECODING_VERSION        (0)//Hynix v8 RDT porting Jeffrey
#define RETRY_HYNIX_V7_1024G_DECODING_VERSION        (0)//Hynix v7 RDT porting Reip
#define RETRY_HYNIX_V5_512G_DECODING_VERSION        (0)//Hynix v5 RDT porting Jeffrey
#define RETRY_BICS5_512G_GTLC_2PLANE_DECODING_VERSION (0)//Sandisk Bics5 RDT porting
#define RETRY_BICS6_1024G_GTLC_4PLANE_DECODING_VERSION (0)//BICS6 Add//zerio bics6 qlc add
#define RETRY_BICS8_1024G_GTLC_4PLANE_DECODING_VERSION (0)//zerio BICS8 Add
#define RETRY_MICRON_1024G_GTLC_4PLANE_DECODING_VERSION (0)//zerio n48r add
#if(CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_QLC)//ems mst add--karl
#define RETRY_EMS_1024G_GTLC_4PLANE_DECODING_VERSION (0)
#endif
typedef enum RetryNANDEnum {
	RETRY_NAND_TOSHIBA_FLASH = 1,
	RETRY_NAND_SANDISK_FLASH
} RetryNANDEnum_t;

typedef enum RetryDesignRuleEnum {
	RETRY_DESIGNRULE_1Z = 1,
	RETRY_DESIGNRULE_BICS2,
	RETRY_DESIGNRULE_BICS3,
	RETRY_DESIGNRULE_BICS4,
	RETRY_DESIGNRULE_BICS4_HDR,
	RETRY_DESIGNRULE_BICS5,
} RetryDesignRuleEnum_t;

typedef enum RetryDieEnum {
	RETRY_DIE_128G = 1,
	RETRY_DIE_256G,
	RETRY_DIE_512G,
	RETRY_DIE_1360G,
} RetryDieEnum_t;

typedef enum RetryTypeEnum {
	RETRY_TYPE_NORMAL = 1,
	RETRY_TYPE_ENTERPRISE,
	RETRY_TYPE_INDUSTRIAL
} RetryTypeEnum_t;

typedef enum RetryVersionEnum {
	RETRY_VERSION_1 = 1,
	RETRY_VERSION_2,
	RETRY_VERSION_3,
	RETRY_VERSION_4,
	RETRY_VERSION_5,
	RETRY_VERSION_6
} RetryVersionEnum_t;

typedef struct {
	union {
		U64 uoAll;
		struct {
			U8	ubNand;//TSB=1;SDK=2
			U8	ubDesignRule;//1Z=1;BiCS2=2;BiCS3=3;BiCS4=4;BiCS4HDR=5
			U8	ubMode;//MLC=1;TLC=2;QLC=3
			U8	ubMonoDie;//128Gb=1;256Gb=2;512Gb=3
			U8	ubNandType;//Normal=1;Enterprise=2;Industrial=3
			U8	ubVersion;//RRT Version
			U16	uwReserve;
		} Idx ;
	} Retry;
	U64 uoDecodingVersion;
	U8 ubRetryTableWriteByMP;
} ReadRetryVersion_t;

#endif /*_RETRY_VERSION_API_H_ */
