/****************************************************************************/
//
//  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
//  All rights reserved
//
//  The content of this document is confidential and shall be applied
//  subject to the terms and conditions of the license agreement and
//  other applicable laws. Any unauthorized access, use or disclosure
//  of this document is strictly prohibited and may be punishable
//  under laws.
//
//  err_hdl_fpl_softbit_retry.h
//
//
//
/****************************************************************************/

#ifndef _ERR_HDL_FPL_SOFTBIT_RETRY_H_
#define _ERR_HDL_FPL_SOFTBIT_RETRY_H_

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "env.h"

#if (CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_QLC)
#include "retry_tsb_qlc_softbit_retry.h"
#elif ((CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_2D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_SANDISK_3D_TLC))
#if (PS5017_EN && (FW_CATEGORY_FLASH == FLASH_BICS5TLC))
#include "retry_kioxia_bics5_tlc/retry_kioxia_bics5_tlc_S17_neutral_sb.h"
#else /* (PS5017_EN && (FW_CATEGORY_FLASH == FLASH_BICS5TLC)) */
#include "retry_tsb_tlc_softbit_retry.h"
#endif /* (PS5017_EN && (FW_CATEGORY_FLASH == FLASH_BICS5TLC)) */
#elif ((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_MLC))

#if (TRUE == RETRY_MICRON_NICKS_ALL_PAGE_TYPE_SB_SUPPORT)
#if (IM_B47R || IM_B37R)
#include "retry_micron_tlc_softbit_retry_all_page_type_B47R.h"
#else /* IM_B47R */
#include "retry_micron_tlc_softbit_retry_all_page_type.h"
#endif /* IM_B47R */
#else	/*(TRUE == RETRY_MICRON_NICKS_ALL_PAGE_TYPE_SB_SUPPORT)*/
#include "retry_micron_tlc_softbit_retry.h"
#endif	/*(TRUE == RETRY_MICRON_NICKS_ALL_PAGE_TYPE_SB_SUPPORT)*/

#elif ((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC)|| (CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_QLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC))//Duson Porting BICS5 Add //Reip Porting 3D-V7 QLC Add//zerio bics6 qlc add
#if (IM_N48R || IM_V6 || IM_V7 || IM_V8|| IM_V5 || IM_BICS5 || IM_BICS6 || IM_BICS8 || IM_BICS6_QLC)//Duson Porting BICS5 Add//Jeffrey Porting 3D V8 TLC Add//zerio BICS8 Add//zerio bics6 qlc add//zerio n48r add
#include "retry_micron_qlc_softbit_retry_all_page_type_N48R.h"
#else /*(IM_N48R)*/
#include "retry_micron_qlc_softbit_retry.h"
#endif  /*(IM_N48R)*/
#else /* (CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_QLC)*/
#include "retry_tsb_tlc_softbit_retry.h"
#endif /*(CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_QLC)*/

#endif /* _ERR_HDL_FPL_SOFTBIT_RETRY_H_ */
