#ifndef _FPU_SanDisk_BICS8_TLC_H_
#define _FPU_SanDisk_BICS8_TLC_H_

#include "typedef.h"
#include "mem.h"
#include <stddef.h>

#if (FW_CATEGORY_FLASH == FLASH_SANDISK_BICS8_TLC)
/*  move to Env.h
#define FLASH_TYPE_TOSHIBA_2D_TLC               (0)
#define FLASH_TYPE_TOSHIBA_3D_TLC               (1)
#define FLASH_TYPE_HYNIX_2D_TLC                 (2)
#define FLASH_TYPE_HYNIX_3D_TLC                 (3)
#define FLASH_TYPE_MICRON_3D_MLC                (4)
#define FLASH_TYPE_MICRON_3D_TLC                (5)
#define FLASH_TYPE_SANDISK_3D_TLC               (6)
#define FLASH_TYPE_TOSHIBA_SLC                   (7)
*/

/*
#define CONFIG_FLASH_TYPE                      (FLASH_TYPE_TOSHIBA_SLC)
*/
#define OPT_SUPPORT_INTERNAL_COPYBACK	0
#define OPT_SUPPORT_ENTRY_MASK			  (0)

#define FPU_IRAM_OFF                        (0)
#define FPU_PTR_OFFSET(fpu)                     ((U16)(offsetof(FPU_ENTRY_LIST, fpu)) + FPU_IRAM_OFF)
// -----------------------------------------------------------------
// FPU comamnds
// Polling for Ready:
//      Ready : (DAT & DAT_H) != DAT_L
//      Busy  : Others
// Polling for Error:
//      Fail  : (DAT & DAT_H) != DAT_L
//      Pass  : Others
// -----------------------------------------------------------------
#define FPU_NOP                 (0x0000)

#define FPU_DLY(x)              (0x1000 | (x))

#define FPU_CMD(x)              (0x2000 | (x))
#define FPU_CMD_COND(x)         (0x2000 | BIT10 | (x))
#define FPU_CMD_DQS(x)          (0x2300 | (x))
#define FPU_CMD_DIE_MODE        (0x2800)

#define FPU_ADR(x)              (0x3000 | ((x) << 8))
#define FPU_ADR_1B(x)           (0x3100 | (x))
#define FPU_ADR_DIE_MODE        (0x3900)      // die bit is valid when send one address

#define FPU_DAT_R_MASK(x)       (0x4000 | (x))
#define FPU_DAT_W(x)            (0x4100 | (x))
#define FPU_DAT_R_MASK_EXT(x)   (0x4200 | (x))  // ext
#define FPU_DAT_W_FIR(x)        (0x4500 | (x))
#define FPU_DAT_W_LAST(x)       (0x4900 | (x))
#define FPU_DAT_W_1B(x)         (0x4D00 | (x))
#define FPU_DAT_R_CMP(x)        (0x4E00 | (x))  // ext

#define FPU_DAT_EXT				(0x4200)
#define FPU_POL_RBY             (0x5000)
#define FPU_POL_MASK(x)         (0x5100 | (x))

#define FPU_DMA_R               (0x6000)

#define FPU_DMA_READ_SHIFT_OFFSET	(0xE) // for 00-05-E0

#define FPU_DMA_W               (0x6100)
#define FPU_DMA_W_DMY           (0x6300)
#define FPU_DMA_W_RAW(x)        (0x6500|(x))
#define    FPU_RAW_PROG_IBF     (BIT11)


#define FPU_ADR_GEN             (0x7000)

#define FPU_BCC_TRIG            (0x8000)

#define FPU_RTY_DAT(x)          (0x9000 | (x))

#define FPU_DMA_R_RAW(x)        (0xA000 | (x))
#define     FPU_RAW_1ST_FRM         (0)
#define     FPU_RAW_2ND_FRM         (BIT8)
#define     FPU_RAW_LOGIC_XOR       (0)
#define     FPU_RAW_LOGIC_XNOR      (BIT6)
#define     FPU_RAW_LOGIC_NOP       (BIT7)
#define     FPU_RAW_SRC_IBUF(X)     ((((U16)(X)) & 0x07) << 3)
#define     FPU_RAW_DST_IBUF(X)     ((((U16)(X)) & 0x07) << 0)
#define     FPU_RAW_SET_MODE(X)     ((((U16)(X)) & 0x03) << 8)

#define FPU_DMA_R_COR(x)        (0xB000 | ((x) << 1))
#define 	COR_IBF_PTR(x)          (((x) & 0x7) << 1)

#define FPU_SBC(x)              (0xC000 | (x))
#define     FPU_SBC_SB_IBUF(x)      ((x) & 0x07)
#define     FPU_SBC_HB_IBUF(x)      (((x) & 0x07) << 3)
#define     FPU_SBC_DSP_EN          (BIT6)
#define     FPU_SBC_SB_FLOW         (BIT7)
#define     FPU_SBC_2K_SEL          (BIT8)
#define     FPU_SBC_DSP_4K_EN       (BIT9)

#define FPU_DUMP_IBUF(x)        (0xD000 | (x))
#define FPU_BR(x)               (0xD000 | (x))

#define FPU_GPO(x)              (0xE000 | (x))

#define FPU_END                 (0xF000)

#if NTODT_IMPLEMENT_EN
#define	FPU_NTODT_ITEM_SHIFT		(4) // occupied 4 FPU
#define	FPU_NTODT_ITEM_SHIFT_BYTE	(FPU_NTODT_ITEM_SHIFT << 1) //each FPU size is 2 bytes.
#define	FPU_NTODT_ITEM_SHIFT_5		(5) // occupied 5 FPU
#define	FPU_NTODT_ITEM_SHIFT_5_BYTE	(FPU_NTODT_ITEM_SHIFT_5 << 1) //each FPU size is 2 bytes.
#define	FPU_NTODT_ITEM_SHIFT_8		(8) // occupied 8 FPU
#define	FPU_NTODT_ITEM_SHIFT_8_BYTE	(FPU_NTODT_ITEM_SHIFT_8 << 1) //each FPU size is 2 bytes.
#else /* NTODT_IMPLEMENT_EN */
#define	FPU_NTODT_ITEM_SHIFT		(0) // occupied 4 FPU
#define	FPU_NTODT_ITEM_SHIFT_BYTE	(FPU_NTODT_ITEM_SHIFT << 1) //each FPU size is 2 bytes.
#define	FPU_NTODT_ITEM_SHIFT_5		(0) // occupied 5 FPU
#define	FPU_NTODT_ITEM_SHIFT_5_BYTE	(FPU_NTODT_ITEM_SHIFT_5 << 1) //each FPU size is 2 bytes.
#define	FPU_NTODT_ITEM_SHIFT_8		(0) // occupied 8 FPU
#define	FPU_NTODT_ITEM_SHIFT_8_BYTE	(FPU_NTODT_ITEM_SHIFT_8 << 1) //each FPU size is 2 bytes.
#endif /* NTODT_IMPLEMENT_EN */

/*
 * auto poll seq idx
 */
// non-die-interleave
#define POL_SEQ_FPU_ENTRY_READ_STATUS_BUSY_40                   (0) // cache ready --- fpu_entry_read_status_busy_40
#define POL_SEQ_FPU_ENTRY_READ_STATUS_BUSY_20                   (1) // true ready --- fpu_entry_read_status_busy_20

// die-interleave
#define POL_SEQ_FPU_ENTRY_READ_STATUS_F1_BUSY_20                (2)
#define POL_SEQ_FPU_ENTRY_READ_STATUS_F2_BUSY_20                (3)

#define POL_SEQ_FPU_ENTRY_READ_STATUS_70_BUSY_40_PRE_TLC_MODE_02	(4)
#define POL_SEQ_FPU_ENTRY_READ_STATUS_70_BUSY_20_CUR_TLC_MODE_01	(5)
#define POL_SEQ_FPU_ENTRY_READ_STATUS_70_BUSY_40_PRE_SLC_MODE_08	(6)
#define POL_SEQ_FPU_ENTRY_READ_STATUS_70_BUSY_20_CUR_SLC_MODE_04	(7)
#define POL_SEQ_FPU_ENTRY_READ_STATUS_78_BUSY_40                    (8)
#define POL_SEQ_FPU_ENTRY_READ_STATUS_78_BUSY_20                    (9)
#define POL_SEQ_FPU_ENTRY_READ_STATUS_70_BUSY_C0_INTERMEDIATE_MULTIPLANE_PROGRAM	(13) // For multi-plane program poll true ready
#define POL_SEQ_FPU_ENTRY_READ_STATUS_70_BUSY_20_CUR_SLC_TLC_MODE_05	(14)
/*
 *  Version Control  (32 bits)
 *
 *  Andes checks "FPU_VER_H" and "FPU_VER_L"
 *
 */

//  flash type
// control from conf.h // 8 bits

//  FPU version  8 bits
#define FPU_VERSION    (0x00)

//  function definition // 16 bits
#define FPU_SUPPORT_BANKING         (BIT0)

#define FPU_SUPPORT_DIE_INTERLEAVE  (BIT1)
#define FPU_SUPPORT_CACHE_CMD       (BIT2)

#define FPU_SUPPORT_FUNCTION (FPU_SUPPORT_BANKING       | \
                              FPU_SUPPORT_DIE_INTERLEAVE| \
                              FPU_SUPPORT_CACHE_CMD |\
                              FPU_SUPPORT_4_PLANE)

#define FPU_VER_H (((CONFIG_FLASH_TYPE) << 8 ) | FPU_VERSION)
#define FPU_VER_L (FPU_SUPPORT_FUNCTION)

#define ADDRESS_READ_NUM 	(5)
#define ADDRESS_DMA_NUM 	(2)
#define ADDRESS_WRITE_NUM 	(5)
#define ADDRESS_ERASE_NUM 	(3)
#define ADDRESS_ROW_NUM 	(3)

#define FPU_GC_BACKUP_P4K_PENDING_DELAY_NUM     ((COP0_BACKUP_P4K_WORKAROUND) ? (4) : (0))
#if (COP0_BACKUP_P4K_WORKAROUND)
#define FPU_GC_BACKUP_P4K_PENDING_DELAY			FPU_NOP,FPU_NOP,FPU_NOP,FPU_NOP,
#else /*(COP0_BACKUP_P4K_WORKAROUND)*/
#define FPU_GC_BACKUP_P4K_PENDING_DELAY
#endif  /*(COP0_BACKUP_P4K_WORKAROUND)*/

/*
 * pre-fix setting
 */
#define TOSHIBA_FAST_READ_PREFIX 	(FPU_NOP)
#define RD_SLC_PREFIX   (FPU_CMD(0xA2))
#define RD_TLC_PREFIX   (FPU_NOP)
#define RD_MLC_PREFIX   (FPU_NOP)
#define WR_SLC_PREFIX   (FPU_CMD_DQS(0xA2))
#define WR_TLC_PREFIX   (FPU_NOP)
#define WR_MLC_PREFIX   (FPU_NOP)
#define ER_SLC_PREFIX   (FPU_CMD(0xA2))
#define ER_TLC_PREFIX   (FPU_NOP)
#define ER_MLC_PREFIX   (FPU_NOP)
#define WR_FW_SLC_PREFIX	(FPU_CMD_DQS(0xDA))
#define ER_FW_SLC_PREFIX	(FPU_CMD(0xDA))

typedef struct fpu_entry_list               FPU_ENTRY_LIST, *FPU_ENTRY_LIST_PTR;

struct fpu_entry_list {
	U16 fpu_version[2];
	U16 fpu_entry_addr_gen_die[2];
	U16 fpu_entry_nop[4];

	U16 fpu_entry_read_status_busy_40[8];
	U16 fpu_entry_read_status_busy_20[8];
	U16 fpu_entry_read_status_70_01_CURR_TLC_MODE[8];
	U16 fpu_entry_read_status_70_02_PREV_TLC_MODE[8];
	U16 fpu_entry_read_status_70_04_CURR_SLC_MODE[8];
	U16 fpu_entry_read_status_70_08_PREV_SLC_MODE[8];

	U16 fpu_entry_read_status_70_busy_20_CURR_TLC_MODE_01[8];
	U16 fpu_entry_read_status_70_busy_40_PREV_TLC_MODE_02[8];
	U16 fpu_entry_read_status_70_busy_20_CURR_SLC_MODE_04[8];
	U16 fpu_entry_read_status_70_busy_40_PREV_SLC_MODE_08[8];
	U16 fpu_entry_read_status_70_busy_C0_INTERMEDIATE_MULTIPLANE_PROGRAM[8];
	U16 fpu_entry_read_status_70_busy_20_CURR_SLC_TLC_MODE_05[8];

	U16 fpu_entry_read_status_f1_busy_40[8];
	U16 fpu_entry_read_status_f2_busy_40[8];
	U16 fpu_entry_read_status_f1_tlc_busy_20[8];
	U16 fpu_entry_read_status_f2_tlc_busy_20[8];
	U16 fpu_entry_read_status_f1_slc_busy_20[8];
	U16 fpu_entry_read_status_f2_slc_busy_20[8];

	U16 fpu_read_and_compare_feature_data[20];
	U16 fpu_entry_read_status_err_81[8];

	U16 fpu_entry_read_status_78_busy_40[8];
	U16 fpu_entry_read_status_78_busy_20[8];

	U16 fpu_entry_read[7]; //(12)       // Error Handling
	U16 fpu_entry_dma_r_05_e0[7];       // Error Handling
	U16 fpu_entry_dma_r_00_05_e0[9];    // Error Handling

	U16 fpu_entry_tlc_read_lower[7];    // Error Handling
	U16 fpu_entry_tlc_read_middle[7];   // Error Handling
	U16 fpu_entry_tlc_read_upper[7];    // Error Handling
	U16 fpu_entry_tlc_read_lower_relax_read_mode[8];    // Error Handling
	U16 fpu_entry_tlc_read_middle_relax_read_mode[8];   // Error Handling
	U16 fpu_entry_tlc_read_upper_relax_read_mode[8];    // Error Handling
	U16 fpu_entry_rand_in[7];           // Error Handling
	U16 fpu_reserved3[7];          // Error Handling
	U16 fpu_entry_cor[4];               // Error Handling
	U16 fpu_entry_dump_ibuf[2];         // Error Handling
	U16 fpu_entry_dma_r_raw[9];         // Error Handling
	U16 fpu_entry_tsb_tlc_rr_lower[7];  // Error Handling
	U16 fpu_entry_tsb_tlc_rr_middle[7]; // Error Handling
	U16 fpu_entry_tsb_tlc_rr_upper[7];  // Error Handling
	U16 fpu_entry_tsb_tlc_rr_lower_relax_read_mode[8];  // Error Handling
	U16 fpu_entry_tsb_tlc_rr_middle_relax_read_mode[8]; // Error Handling
	U16 fpu_entry_tsb_tlc_rr_upper_relax_read_mode[8];  // Error Handling

	U16 fpu_entry_direct_slc_read[16];      // Error Handling
	U16 fpu_entry_direct_tlc_l_read[16];    // Error Handling
	U16 fpu_entry_direct_tlc_m_read[16];    // Error Handling
	U16 fpu_entry_direct_tlc_u_read[16];    // Error Handling
	U16 fpu_entry_direct_tlc_l_read_relax_read_mode[17];    // Error Handling
	U16 fpu_entry_direct_tlc_m_read_relax_read_mode[17];    // Error Handling
	U16 fpu_entry_direct_tlc_u_read_relax_read_mode[17];    // Error Handling

	U16 fpu_entry_reset_ff[3];
	U16 fpu_entry_reset_fc[3];
	U16 fpu_entry_reset_fa_lun0[12];
	U16 fpu_entry_reset_fa_lun1[12];
	U16 fpu_entry_reset_fa_lun2[12];
	U16 fpu_entry_reset_fa_lun3[12];

	U16 fpu_entry_slc_read[7];

	U16 fpu_entry_erase[6]; //(12)
	U16 fpu_entry_slc_erase[6]; //(14+2)
	U16 fpu_entry_slc_60h_row_erase[10];
	U16 fpu_entry_tlc_60h_row_erase[10];
	U16 fpu_entry_2p_erase[9];

	/*
	 * misc
	 */

	U16 fpu_entry_read_dma[5];
	U16 fpu_entry_dly_only[2];

	U16 fpu_entry_erase_all_done[5];
	U16 fpu_entry_70[3];
	U16 fpu_entry_prog_to_flash_cache[7];
	U16 fpu_entry_read_from_flash_cache[7];
	U16 fpu_entry_C85_A5_NoDMAW[5];
	U16 fpu_entry_DMA_W_RAW[2];
	U16 fpu_entry_DMA_R_RAW[2];
	U16 fpu_entry_C11[2];
	U16 fpu_entry_C00_A5_C05_A2_CE0[7];

	/*
	 * read/cache read
	 */

	/*
	 * Read command: slc / mlc mode + plane cache mode.
	 */
	U16 fpu_entry_slc_3F_read[1];
	U16 fpu_entry_3F_read[2];

	U16 fpu_entry_slc_1p_20_read[1];	//4KB Page Read (hynix)
	U16 fpu_entry_tlc_1p_20_read[6];
	U16 fpu_entry_mlc_2p_32_30_read[15];
	U16 fpu_entry_mlc_2p_32_31_read[15];
	U16 fpu_entry_mlc_4p_32_30_read[33];
	U16 fpu_entry_mlc_4p_32_31_read[33];
	U16 fpu_entry_slc_1p_30_fast_read[1];
	U16 fpu_entry_slc_1p_30_read[6];
	U16 fpu_entry_slc_1p_31_fast_read[1];
	U16 fpu_entry_slc_1p_31_read[6];
	U16 fpu_entry_slc_2p_32_30_read[15];
	U16 fpu_entry_slc_2p_32_31_read[15];
	U16 fpu_entry_slc_2p_32_F1_30_read[15];
	U16 fpu_entry_slc_2p_32_F2_30_read[15];
	U16 fpu_entry_slc_4p_32_30_read[33];
	U16 fpu_entry_slc_4p_32_31_read[33];
	U16 fpu_entry_slc_3p_32_30_read[24];
	U16 fpu_entry_slc_3p_32_31_read[24];

	/*
	 * Read command: tlc mode + plane cache mode.
	 */
	U16 fpu_entry_tlc_1p_30_lower_pg_fast_read[1];
	U16 fpu_entry_tlc_1p_30_lower_pg_read[6];
	U16 fpu_entry_tlc_1p_30_middle_pg_fast_read[1];
	U16 fpu_entry_tlc_1p_30_middle_pg_read[6];
	U16 fpu_entry_tlc_1p_30_upper_pg_fast_read[1];
	U16 fpu_entry_tlc_1p_30_upper_pg_read[6];
	U16 fpu_entry_tlc_1p_31_lower_pg_fast_read[1];
	U16 fpu_entry_tlc_1p_31_lower_pg_read[6];
	U16 fpu_entry_tlc_1p_31_middle_pg_fast_read[1];
	U16 fpu_entry_tlc_1p_31_middle_pg_read[6];
	U16 fpu_entry_tlc_1p_31_upper_pg_fast_read[1];
	U16 fpu_entry_tlc_1p_31_upper_pg_read[6];
	U16 fpu_entry_tlc_2p_32_30_lower_pg_read[16];
	U16 fpu_entry_tlc_2p_32_30_middle_pg_read[16];
	U16 fpu_entry_tlc_2p_32_30_upper_pg_read[16];
	U16 fpu_entry_tlc_2p_32_31_lower_pg_read[16];
	U16 fpu_entry_tlc_2p_32_31_middle_pg_read[16];
	U16 fpu_entry_tlc_2p_32_31_upper_pg_read[16];
	U16 fpu_entry_tlc_2p_32_F1_30_lower_pg_read[16];
	U16 fpu_entry_tlc_2p_32_F1_30_middle_pg_read[16];
	U16 fpu_entry_tlc_2p_32_F1_30_upper_pg_read[16];
	U16 fpu_entry_tlc_2p_32_F2_30_lower_pg_read[16];
	U16 fpu_entry_tlc_2p_32_F2_30_middle_pg_read[16];
	U16 fpu_entry_tlc_2p_32_F2_30_upper_pg_read[16];
	U16 fpu_entry_tlc_3p_32_30_lower_pg_read[26];
	U16 fpu_entry_tlc_3p_32_30_middle_pg_read[26];
	U16 fpu_entry_tlc_3p_32_30_upper_pg_read[26];
	U16 fpu_entry_tlc_3p_32_31_lower_pg_read[26];
	U16 fpu_entry_tlc_3p_32_31_middle_pg_read[26];
	U16 fpu_entry_tlc_3p_32_31_upper_pg_read[26];
	U16 fpu_entry_tlc_4p_32_30_lower_pg_read[36];
	U16 fpu_entry_tlc_4p_32_30_middle_pg_read[36];
	U16 fpu_entry_tlc_4p_32_30_upper_pg_read[36];
	U16 fpu_entry_tlc_4p_32_31_lower_pg_read[36];
	U16 fpu_entry_tlc_4p_32_31_middle_pg_read[36];
	U16 fpu_entry_tlc_4p_32_31_upper_pg_read[36];

	/*
	 * end of common region (need by loader)
	 */

	U16 fpu_common_region_delimiter[2];

	/*
	 * Program command: slc / mlc mode.
	 */

	U16 fpu_entry_prog_mlc_80_11[12];
	U16 fpu_entry_prog_mlc_80_15[8];
	U16 fpu_entry_prog_bypass_WDMA[4];
	U16 fpu_entry_prog_slc_80_11[12];
#if (COP0_BACKUP_P4K_WORKAROUND)
	U16 fpu_entry_prog_slc_80_11_gc[12 + FPU_GC_BACKUP_P4K_PENDING_DELAY_NUM];
#endif /*(COP0_BACKUP_P4K_WORKAROUND)*/
	U16 fpu_entry_prog_slc_80_15[8];
#if (COP0_BACKUP_P4K_WORKAROUND)
	U16 fpu_entry_prog_slc_80_15_gc[8 + FPU_GC_BACKUP_P4K_PENDING_DELAY_NUM];
#endif /*(COP0_BACKUP_P4K_WORKAROUND)*/

	U16 fpu_entry_prog_slc_80_11_F1[12];
	U16 fpu_entry_prog_slc_80_11_F2[12];

	U16 fpu_entry_mlc_prog[8];
	U16 fpu_entry_slc_prog[8];
#if (COP0_BACKUP_P4K_WORKAROUND)
	U16 fpu_entry_slc_prog_gc[8 + FPU_GC_BACKUP_P4K_PENDING_DELAY_NUM];
#endif /*(COP0_BACKUP_P4K_WORKAROUND)*/
	U16 fpu_entry_test_CA2_C85_A5_DW[8];

	/*
	 * program command: tlc mode.
	 */
	U16 fpu_entry_first_upper_prog_10[9];
	U16 fpu_entry_foggy_upper_prog_10[9];
	U16 fpu_entry_fine_upper_prog_10[8];

	U16 fpu_entry_first_lower_prog_1A[9];
	U16 fpu_entry_first_middle_prog_1A[9];
	U16 fpu_entry_foggy_lower_prog_1A[9];
	U16 fpu_entry_foggy_middle_prog_1A[9];
	U16 fpu_entry_fine_lower_prog_1A[8];
	U16 fpu_entry_fine_middle_prog_1A[8];

	U16 fpu_entry_first_lower_prog_11[13];
	U16 fpu_entry_first_middle_prog_11[13];
	U16 fpu_entry_first_upper_prog_11[13];
	U16 fpu_entry_foggy_lower_prog_11[13];
	U16 fpu_entry_foggy_middle_prog_11[13];
	U16 fpu_entry_foggy_upper_prog_11[13];
	U16 fpu_entry_fine_lower_prog_11[12];
	U16 fpu_entry_fine_middle_prog_11[12];
	U16 fpu_entry_fine_upper_prog_11[12];

	U16 fpu_entry_first_upper_prog_15[9];
	U16 fpu_entry_foggy_upper_prog_15[9];
	U16 fpu_entry_fine_upper_prog_15[8];

	U16 fpu_entry_first_lower_prog_11_F1[13];
	U16 fpu_entry_first_middle_prog_11_F1[13];
	U16 fpu_entry_first_upper_prog_11_F1[13];
	U16 fpu_entry_foggy_lower_prog_11_F1[13];
	U16 fpu_entry_foggy_middle_prog_11_F1[13];
	U16 fpu_entry_foggy_upper_prog_11_F1[13];
	U16 fpu_entry_fine_lower_prog_11_F1[12];
	U16 fpu_entry_fine_middle_prog_11_F1[12];
	U16 fpu_entry_fine_upper_prog_11_F1[12];

	U16 fpu_entry_first_lower_prog_11_F2[13];
	U16 fpu_entry_first_middle_prog_11_F2[13];
	U16 fpu_entry_first_upper_prog_11_F2[13];
	U16 fpu_entry_foggy_lower_prog_11_F2[13];
	U16 fpu_entry_foggy_middle_prog_11_F2[13];
	U16 fpu_entry_foggy_upper_prog_11_F2[13];
	U16 fpu_entry_fine_lower_prog_11_F2[12];
	U16 fpu_entry_fine_middle_prog_11_F2[12];
	U16 fpu_entry_fine_upper_prog_11_F2[12];

	U16 fpu_entry_SanDisk_for_align[1];

	U16 fpu_entry_SanDisk_FSP_LSB_80_program_1A[8]; //TLC 1P
	U16 fpu_entry_SanDisk_FSP_CSB_80_program_1A[8];
	U16 fpu_entry_SanDisk_FSP_MSB_80_program_10[8];

	U16 fpu_entry_SanDisk_FSP_LSB_80_program_11[8]; //TLC 2P - 1st Plane
	U16 fpu_entry_SanDisk_FSP_CSB_80_program_11[8];
	U16 fpu_entry_SanDisk_FSP_MSB_80_program_11[8];

	U16 fpu_entry_SanDisk_FSP_LSB_81_program_1A[8]; //TLC 2P - last Plane
	U16 fpu_entry_SanDisk_FSP_CSB_81_program_1A[8];
	U16 fpu_entry_SanDisk_FSP_MSB_81_program_10[8];

	U16 fpu_entry_SanDisk_FSP_LSB_81_program_11[8];//TLC 2P - other Plane  //SanDisk BICS5 just 2Planes
	U16 fpu_entry_SanDisk_FSP_CSB_81_program_11[8];
	U16 fpu_entry_SanDisk_FSP_MSB_81_program_11[8];

#if (COP0_BACKUP_P4K_WORKAROUND)
	U16 fpu_entry_SanDisk_FSP_LSB_80_program_1A_gc[12]; //TLC 1P
	U16 fpu_entry_SanDisk_FSP_CSB_80_program_1A_gc[12];
	U16 fpu_entry_SanDisk_FSP_MSB_80_program_10_gc[12];

	U16 fpu_entry_SanDisk_FSP_LSB_80_program_11_gc[12]; //TLC 2P - 1st Plane
	U16 fpu_entry_SanDisk_FSP_CSB_80_program_11_gc[12];
	U16 fpu_entry_SanDisk_FSP_MSB_80_program_11_gc[12];

	U16 fpu_entry_SanDisk_FSP_LSB_81_program_1A_gc[12]; //TLC 2P - last Plane
	U16 fpu_entry_SanDisk_FSP_CSB_81_program_1A_gc[12];
	U16 fpu_entry_SanDisk_FSP_MSB_81_program_10_gc[12];

	U16 fpu_entry_SanDisk_FSP_LSB_81_program_11_gc[12];//TLC 2P - other Plane //SanDisk BICS5 just 2Planes
	U16 fpu_entry_SanDisk_FSP_CSB_81_program_11_gc[12];
	U16 fpu_entry_SanDisk_FSP_MSB_81_program_11_gc[12];
#endif

	U16 fpu_entry_SanDisk_FSP_MSB_81_program_15[8];
#if (COP0_BACKUP_P4K_WORKAROUND)
	U16 fpu_entry_SanDisk_FSP_MSB_81_program_15_gc[12];
#endif

	U16 fpu_entry_SanDisk_slc_81_program_10[8];
#if (COP0_BACKUP_P4K_WORKAROUND)
	U16 fpu_entry_SanDisk_slc_81_program_10_gc[12];
#endif

	U16 fpu_entry_SanDisk_slc_81_program_11[12];
#if (COP0_BACKUP_P4K_WORKAROUND)
	U16 fpu_entry_SanDisk_slc_81_program_11_gc[16];
#endif

	U16 fpu_entry_SanDisk_slc_81_program_15[8];
#if (COP0_BACKUP_P4K_WORKAROUND)
	U16 fpu_entry_SanDisk_slc_81_program_15_gc[12];
#endif

#if OPT_SUPPORT_INTERNAL_COPYBACK
	U16 fpu_entry_first_lower_85_11_1a_copyback[20];
	U16 fpu_entry_first_middle_85_11_1a_copyback[20];
	U16 fpu_entry_first_upper_85_11_10_copyback[20];
	U16 fpu_entry_foggy_lower_85_11_1a_copyback[20];
	U16 fpu_entry_foggy_middle_85_11_1a_copyback[20];
	U16 fpu_entry_foggy_upper_85_11_10_copyback[20];
	U16 fpu_entry_fine_lower_85_11_1a_copyback[18];
	U16 fpu_entry_fine_middle_85_11_1a_copyback[18];
	U16 fpu_entry_fine_upper_85_11_10_copyback[18];
#endif	/* OPT_SUPPORT_INTERNAL_COPYBACK*/

	U16 fpu_entry_fsp_upper_prog_10[8 + FPU_GC_BACKUP_P4K_PENDING_DELAY_NUM];

	U16 fpu_entry_fsp_lower_prog_1A[8 + FPU_GC_BACKUP_P4K_PENDING_DELAY_NUM];
	U16 fpu_entry_fsp_middle_prog_1A[8 + FPU_GC_BACKUP_P4K_PENDING_DELAY_NUM];

	U16 fpu_entry_fsp_lower_prog_1A_F1[12];
	U16 fpu_entry_fsp_middle_prog_1A_F1[12];

	U16 fpu_entry_fsp_lower_prog_1A_F2[12];
	U16 fpu_entry_fsp_middle_prog_1A_F2[12];

	U16 fpu_entry_fsp_lower_prog_11[12 + FPU_GC_BACKUP_P4K_PENDING_DELAY_NUM];
	U16 fpu_entry_fsp_middle_prog_11[12 + FPU_GC_BACKUP_P4K_PENDING_DELAY_NUM];
	U16 fpu_entry_fsp_upper_prog_11[12 + FPU_GC_BACKUP_P4K_PENDING_DELAY_NUM];

	U16 fpu_entry_fsp_upper_prog_15[8 + FPU_GC_BACKUP_P4K_PENDING_DELAY_NUM];

	U16 fpu_entry_fsp_lower_prog_11_F1[12];
	U16 fpu_entry_fsp_middle_prog_11_F1[12];
	U16 fpu_entry_fsp_upper_prog_11_F1[12];

	U16 fpu_entry_fsp_lower_prog_11_F2[12];
	U16 fpu_entry_fsp_middle_prog_11_F2[12];
	U16 fpu_entry_fsp_upper_prog_11_F2[12];

	/*
	* add for match with andes code [Hynix_V6_ToDo]
	*/
	U16 fpu_entry_m3d_tlc_prog_10[9];
#if (COP0_BACKUP_P4K_WORKAROUND)
	U16 fpu_entry_m3d_tlc_prog_10_gc[9 + FPU_GC_BACKUP_P4K_PENDING_DELAY_NUM];
#endif /*(COP0_BACKUP_P4K_WORKAROUND)*/
	U16 fpu_entry_m3d_tlc_prog_11[13];
#if (COP0_BACKUP_P4K_WORKAROUND)
	U16 fpu_entry_m3d_tlc_prog_11_gc[13 + FPU_GC_BACKUP_P4K_PENDING_DELAY_NUM];
#endif /*(COP0_BACKUP_P4K_WORKAROUND)*/
	U16 fpu_entry_m3d_tlc_prog_15[9];
#if (COP0_BACKUP_P4K_WORKAROUND)
	U16 fpu_entry_m3d_tlc_prog_15_gc[9 + FPU_GC_BACKUP_P4K_PENDING_DELAY_NUM];
#endif

	/*
	 * get feature temperature
	 */

	U16 fpu_entry_getfeature_temperature_die0[8];
	U16 fpu_entry_getfeature_dma[4];

	/*
	 * Error Handle - Softbit
	 */

	U16 fpu_sandisk_test_mode_entry[6];    // Error Handling
	U16 fpu_entry_sndk_tlc_rr_pre[3];    // Error Handling

	U16 fpu_entry_tsb_2dtlc_rr[24];       // Error Handling
	U16 fpu_entry_tsb_3dtlc_rr[24];       // Error Handling
	U16 fpu_sandisk_select_state_and_single_state_read[11];       // Error Handling
	U16 fpu_sandisk_select_state_and_5D_single_state_read[12];       // Error Handling

	U16 fpu_sandisk_tlc_sb_read_lower[8];  // Error Handling
	U16 fpu_sandisk_tlc_sb_read_middle[8]; // Error Handling
	U16 fpu_sandisk_tlc_sb_read_upper[8];  // Error Handling

	U16 fpu_sandisk_tlc_bics3_sb_read[14];  // Error Handling

	U16 fpu_entry_tsb_tlc_xnor[9];      // Error Handling
	U16 fpu_entry_sndk_tlc_xnor[5];      // Error Handling
	U16 fpu_entry_tsb_tlc_manual_read_c[4]; // Error Handling
	U16 fpu_entry_tsb_tlc_manual_read_e[4]; // Error Handling
	U16 fpu_entry_tsb_tlc_sb_read[7];   // Error Handling
	U16 fpu_entry_sndk_tlc_sb_read[6];   // Error Handling
	U16 fpu_entry_tsb_tlc_rr_exit[25];  // Error Handling
	U16 fpu_entry_sbc[2];               // Error Handling
	//For E11 verification
	U16 fpu_entry_adg_test[3];
	U16 fpu_entry_check_status_to_ready[5];

	U16 fpu_entry_test_CA2_C00_A5_C30[8];
	U16 fpu_entry_test_C05_A5_CE0_DR[8];
	U16 fpu_reserved4[8];
	U16 fpu_reserved1[8];
	U16 fpu_entry_test_C70_POL_MK40_C00[8];
	U16 fpu_entry_test_CA2_C80_A5_DW_C10[8];
	U16 fpu_entry_test_C85_A5_DW[8];
	U16 fpu_entry_test_CA2_C60_A3_CD0[8];

	U16 fpu_entry_70_e0[4];

	U16 fpu_set_feature[28];
	U16 fpu_get_feature[16];

	U16 fpu_raw_dma_read[9];

	U16 fpu_tlc_read_lower[7];
	U16 fpu_tlc_read_middle[7];
	U16 fpu_tlc_read_upper[7];

	U16 fpu_tlc_rr_lower[7];
	U16 fpu_tlc_rr_middle[7];
	U16 fpu_tlc_rr_upper[7];

	U16 fpu_sbc[2];
	U16 fpu_backup_restore_ibuf[5];

	U16 fpu_00_30_read[8];

	U16 fpu_ram_cmd[7];

	U16 fpu_correct[3];

	U16 fpu_reserved2[9];

	U16 fpu_tsb_bics3_sing_read[8];
	U16 fpu_tsb_tlc_xnor[9];
	U16 fpu_tsb_tlc_hb_rr[8];
	U16 fpu_tsb_1st_relax_read_lower[8];
	U16 fpu_tsb_1st_relax_read_middle[8];
	U16 fpu_tsb_1st_relax_read_upper[8];
	U16 fpu_tsb_other_relax_read_lower[8];
	U16 fpu_tsb_other_relax_read_middle[8];
	U16 fpu_tsb_other_relax_read_upper[8];

	U16 fpu_entry_05_E0_no_dma_read[8];

	U16 fpu_entry_TSB_read_temperature_cmd[4];
	U16 fpu_entry_TSB_read_temperature_DMA[4];

	U16 fpu_entry_slc_no_dma_program[7];

	U16 fpu_sandisk_slc_dynamic_read[8];
	U16 fpu_sandisk_tlc_dynamic_read_lower[8];
	U16 fpu_sandisk_tlc_dynamic_read_middle[8];
	U16 fpu_sandisk_tlc_dynamic_read_upper[8];

	/*  BICS4_AIPR_FEATURE_EN */

	U16 fpu_entry_align[3];
	U16 fpu_entry_addr_gen_only[4];

	U16 fpu_slc_sb_negative_read[8];
#if ICE_MODE_EN
	U16 fpu_entry_dummy[1];
#endif
	U16 fpu_entry_ctrl_re_low[4];

	U16 fpu_entry_reset_fa[12];
};

// -----------------------------------------------------------------
// FPU definitions.
// -----------------------------------------------------------------

#define FPU_PTR_NOP_CMD											(FPU_PTR_OFFSET(fpu_entry_nop))
#define FPU_PTR_SLC_READ_CMD									(FPU_PTR_OFFSET(fpu_entry_slc_read))
#define FPU_PTR_SLC_1P_30_READ_CMD								(FPU_PTR_OFFSET(fpu_entry_slc_1p_30_read))
#define FPU_PTR_SLC_2P_32_30_READ_CMD							(FPU_PTR_OFFSET(fpu_entry_slc_2p_32_30_read))
#define FPU_PTR_DMA_READ										(FPU_PTR_OFFSET(fpu_entry_dma_r_05_e0))

#define FPU_PTR_C70_MK40										(0x0000)
#define FPU_PTR_C70_MK60										(0x0010)
#define FPU_PTR_C70_MKC0_MK81_EXT								(0x0050)
#define FPU_PTR_C70_MKC0_MK00_EXT								(0x0060)	// for force fail
#define FPU_PTR_C78_MKC0_MK81_EXT								(0x00F8)
#define FPU_PTR_C78_MK40										(0x0080)
#define FPU_PTR_C70_MK80										(0x0108)	// for dummy read status. always ready


// Read
#define FPU_PTR_CA2_C00_A5_C30									(0x0000 + FPU_SIZE_TEST)
#define FPU_PTR_C00_A5_C30										(0x0002 + FPU_SIZE_TEST)

// Read DMA
#define FPU_PTR_C05_A5_CE0_DR									(0x0010 + FPU_SIZE_TEST)	// no use
#define FPU_PTR_C02_A5_CE0_DR									(0x0020 + FPU_SIZE_TEST)	// no use
#define FPU_PTR_C00_A5_C05_A2_CE0_DR							(0x0030 + FPU_SIZE_TEST)

// Poll Status
#define FPU_PTR_C70_POL_MK40_C00								(0x0040 + FPU_SIZE_TEST)
//======= Below is for testing
// Program
#define FPU_PTR_CA2_C80_A5_DW_C10								(0x0050 + FPU_SIZE_TEST)
#define FPU_PTR_C80_A5_DW_C10									(0x0052 + FPU_SIZE_TEST)
// Program Cache Only
#define FPU_PTR_C85_A5_DW										(0x0060 + FPU_SIZE_TEST)
// Erase
#define FPU_PTR_CA2_C60_A3_CD0									(0x0070 + FPU_SIZE_TEST)
#define FPU_PTR_C60_A3_CD0										(0x0072 + FPU_SIZE_TEST)

#define FPU_SIZE_FIP_SIMPLE		(0x80)
#define FPU_SIZE_TEST			(FPU_LEN - FPU_SIZE_FIP_SIMPLE)
#define FPU_SIZE					(FPU_LEN)

#define FPU_PTR_ADDR_ONLY_CMD						(NULL)

extern FPU_ENTRY_LIST gFpuEntryList;
#if NCS_EN
extern U16 guwFPUContentForNCS[0x0D];
#endif  /* NES_GEN1_EN */
extern U16 guwFPUEntryResetFAh[];
#endif /* (CONFIG_FLASH_TYPE == FLASH_SANDISK_BICS8_TLC) */
#endif /* _FPU_SanDisk_BICS8_TLC_H_ */
