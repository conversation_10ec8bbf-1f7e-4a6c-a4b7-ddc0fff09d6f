#include "rtt.h"
#include "rtt_api.h"
#include "debug/debug.h"
#include "cpu/cpu_api.h"
#include "hal/sys/api/pmu/pmu_api.h"
#include "hal/fip/fip_api.h"
#include "ftl/ftl.h"


#if PS5017_EN
RTTVariation_t gRTTVariation = {
	.uwOSCVariationCoefficient = 1000,
	.uwFLLAndRCVariationCoefficient = 1000,
	.ubFLLValue = 0,
	.ubRTTVariationFlag	= FALSE,
	.uwLFOSCMinTrimVariationCoefficient = 1000,
	.ubLFOSCMinTrimVariationFlag = FALSE
};
#elif PS5021_EN /* PS5017_EN */
RTTVariation_t gRTTVariation = {
	.ulRTT0Value[0] = 0,
	.ulRTT0Value[1] = 0,
	.uoRTT0CntRecord = 0,
	.uwOSCVariationCoefficient = 1000,
	.ubRTT0CheckOverflowLock = FALSE
};
#else /* PS5017_EN */
RTTVariation_t gRTTVariation = {
	.uoRTT0CntRecord = 0,
	.uwOSCVariationCoefficient = 1000,
	.uwFLLAndRCVariationCoefficient = 1000,
	.ubFLLValue = 0
};
#endif /* PS5017_EN */
#if BURNER_MODE_EN
RTTFLLAndRCCalibration_t gRTTFLLAndRCCalibration = {0};
#endif /*BURNER_MODE_EN*/
U64 guoRTTIdleEndTime;

U64 RTTGetCnt(RTT_TYPE_t RTT_Type)
{
#if PS5021_EN
	U32 ulRecordTime[2] = {0};
	U64 uoRealTime = 0;

	gRTTVariation.ubRTT0CheckOverflowLock = TRUE;
	ulRecordTime[1] = gRTTVariation.ulRTT0Value[1];
	ulRecordTime[0] = gRTTVariation.ulRTT0Value[0];
	uoRealTime = (U64)(M_GET_US_UW_TIMER());
	gRTTVariation.ubRTT0CheckOverflowLock = FALSE;

	if (ulRecordTime[0] > (U32)uoRealTime) {
		uoRealTime += BIT64(RTT_RTT0_SHIFT_HIGH_4BYTE);
	}
	uoRealTime += (((U64)ulRecordTime[1]) << RTT_RTT0_SHIFT_HIGH_4BYTE);
	return uoRealTime;

#else /* PS5021_EN */
	if (RTT0 == RTT_Type) {
		M_LATCH_RTT0_VAL();
		__asm("DSB");
		return M_GET_RTT0_LATCH();
	}
	else {
		return (U64)M_GET_PD1_RTT_CNT(RTT_Type);
	}
#endif /* PS5021_EN */
}

void RTTSetPD1RTTTimeout(RTT_TYPE_t RTT_Type, PD1_RTT_SCALE_t RTT_SCALE, PD1_RTT_MODE_t RTT_MODE, U32 ulTimeOutValue)
{
#if PS5021_EN
	switch (RTT_Type) {
	case US_UW_TIMER:
		M_SET_US_UW_TIMER(ulTimeOutValue);
		M_ENABLE_US_UW_TIMER();
		break;
	case US_DW_TIMER:
		M_SET_US_DW_TIMER(ulTimeOutValue);
		M_ENABLE_US_DW_TIMER_REP();
		break;
	default:
		break;
	}

#else/* PS5021_EN*/
	U32 ulRTTReg = R8_SYS1_RTT1_CTRL + RTT_Type;

	//Stop RTT and Clear RTT Counter
	M_DISABLE_PD1_RTT(ulRTTReg);
	M_CLR_PD1_RTT_CNT(ulRTTReg);

	//Scale
	M_CLR_PD1_RTT_SCALE(ulRTTReg);
	M_SET_PD1_RTT_SCALE(ulRTTReg, RTT_SCALE);

	//Mode
	if (REPEAT_MODE == RTT_MODE) {
		M_PD1_RTT_REPEAT_MODE(ulRTTReg);
	}
	else {//(ONE_SHOT_MODE == RTT_MODE)
		M_PD1_RTT_ONE_SHOT_MODE(ulRTTReg);
	}

	//Set Timeout Value
	M_SET_PD1_RTT_TO_LMT(RTT_Type, ulTimeOutValue);

	//Start RTT
	M_ENABLE_PD1_RTT(ulRTTReg);
#endif /* PS5021_EN */
}
void RTTInitOperationTimer(void)
{
#if (RDT_MODE_EN)
	RTTSetPD1RTTTimeout(RTT1, SCALE_1MS, REPEAT_MODE, 1);
#else /* (RDT_MODE_EN) */
#if PS5017_EN
	RTTSetPD1RTTTimeout(RTT2, SCALE_1US, REPEAT_MODE, ((U32)gRTTVariation.uwOSCVariationCoefficient)); // For FW Operation Timer with Calibration
#else /* PS5017_EN */
	if (0 != gRTTVariation.uoRTT0CntRecord) {
		guoOperationTime += (M_RTT_RTT0_OSC_CALIBRATION((RTTGetCnt(RTT0) - gRTTVariation.uoRTT0CntRecord) / RTT0_UNIT_PER_MILLISECOND));
	}
#if PS5021_EN
	RTTSetPD1RTTTimeout(US_DW_TIMER, SCALE_1US, REPEAT_MODE, ((U32)gRTTVariation.uwOSCVariationCoefficient)); // FW Operation Timer with Calibration
#else/* PS5021_EN*/
	RTTSetPD1RTTTimeout(RTT2, SCALE_1US, REPEAT_MODE, M_RTT_GET_RTT1_1MS_CNT()); // FW Operation Timer with Calibration
#endif /* PS5021_EN*/
	gRTTVariation.uoRTT0CntRecord = 0;
#endif /* PS5017_EN */
#endif /* (RDT_MODE_EN) */
}

void RTTStopTimer(RTT_TYPE_t RTT_Type)
{
#if E21_TODO
#else/* E21_TODO*/
	if (RTT0 == RTT_Type) {
		M_DISABLE_RTT0();
	}
	else {
		M_DISABLE_PD1_RTT(R8_SYS1_RTT1_CTRL + RTT_Type);
	}
#endif/* E21_TODO*/
}

void RTT0Idle(U64 uoIdleCnt)
{
	U64 uoEndTime;

	uoEndTime = RTTGetCnt(RTT0) + uoIdleCnt;

	while (1) {
		if (RTTGetCnt(RTT0) >= uoEndTime) {
			break;
		}
	}
}

void IdlePC(volatile U64 uoLoopCnt)
{
	while (uoLoopCnt--) {
		asm volatile("nop");
	}
}

void RTTSetSelfTimeout(pRTTSelfTimeoutCondition pTimer, U64 uoTimeoutOffset)
{
	pTimer->uoStartRTT = RTTGetCnt(RTT0);
	pTimer->uoEndRTT = pTimer->uoStartRTT + uoTimeoutOffset;
}

U8 RTTCheckSelfTimeout(pRTTSelfTimeoutCondition pTimer)
{
	U64 uoCurrentRtt = RTTGetCnt(RTT0);

	return (uoCurrentRtt >= pTimer->uoEndRTT);
}

void RTTSetPD1RTTTimeoutCnt(RTT_TYPE_t RTT_Type, U32 ulTimeOutValue)
{
#if E21_TODO
#else /* E21_TODO */
	M_SET_PD1_RTT_TO_LMT(RTT_Type, ulTimeOutValue);
#endif /* E21_TODO */
}

#if (!PS5021_EN)
#if BURNER_MODE_EN
U8 RTTCheckFLLAndRCVariation(void)
{
	U8 ubi, ubj;
	U32 ulMin;
	U32 *pRTT0Cnt = &gRTTFLLAndRCCalibration.ulRTT0CntPerHundredMillisecond[0];
	U32 ulDeviation = 0;

	if (gRTTFLLAndRCCalibration.ubCalibrationCnt != RTT_FLL_AND_RC_CALIBRATION_CNT) {
		return FAIL;
	}

	// Sorting by Bubble Sort
	for (ubi = 0; ubi < RTT_FLL_AND_RC_CALIBRATION_CNT; ++ubi) {
		ulMin = pRTT0Cnt[ubi];
		for (ubj = ubi + 1; ubj < RTT_FLL_AND_RC_CALIBRATION_CNT; ++ubj) {
			if (pRTT0Cnt[ubj] < pRTT0Cnt[ubi]) {
				ulMin = pRTT0Cnt[ubj];
				pRTT0Cnt[ubj] = pRTT0Cnt[ubi];
				pRTT0Cnt[ubi] = ulMin;
			}
		}
	}

	// Max and Min Must Below 5%
	if ((pRTT0Cnt[RTT_FLL_AND_RC_CALIBRATION_CNT - 1] - pRTT0Cnt[0]) > RTT_5PERCENT_OF_US_PER_100MS) {
		return FAIL;
	}
	ulDeviation = (pRTT0Cnt[0] < RTT_US_PER_100MS)
		? (RTT_US_PER_100MS - pRTT0Cnt[0]) : (pRTT0Cnt[0] - RTT_US_PER_100MS);
	// Deviation Must Below 20%
	if (ulDeviation > RTT_20PERCENT_OF_US_PER_100MS) {
		return FAIL;
	}

	return SUCCESS;
}

U8 RTTGetFLLAndRCVariation(void)
{
	U8 ubReEnter = 0;
	U8 ubResult = FAIL;
	RTTSelfTimeoutCondition_t RTTTimer;

	do {
#if PS5017_EN
		// 0. Switch RTT0 Clock Source To RC And Get
		//PMUSwitchClockSrc(PMU_FLL_CMD_MODE_PHY_RC_BIT); // E17 remove FLL circuit

		// 1. Disable IRQ Interrupt
		M_ARM_IRQ_DISABLE();
#else /* PS5017_EN */
		// 0. Switch RTT0 Clock Source To RC And Get
		PMUSwitchClockSrc(PMU_FLL_CMD_MODE_PHY_RC_BIT);

		// 1. Disable IRQ Interrupt
		M_ARM_IRQ_DISABLE();
#endif /* PS5017_EN */

		// 2. Initial Parameter
		gRTTFLLAndRCCalibration.ubCalibrationCnt = 0;

		// 3. Set RTT1 Interrupt
		RTTSetSelfTimeout(&RTTTimer, RTT_FLL_CALIBRATION_TIME_OUT_LIMIT);
		RTTSetPD1RTTTimeout(RTT1, SCALE_1MS, REPEAT_MODE, RTT_FLL_CALIBRATION_INT_INTERVAL_MS); //100ms
		gRTTFLLAndRCCalibration.uoPreviousRTT0Cnt = 0;

		// 4. Wait 5 Loops or Break with Timeout 600ms
		while ((gRTTFLLAndRCCalibration.ubCalibrationCnt < RTT_FLL_AND_RC_CALIBRATION_CNT) && (FALSE == RTTCheckSelfTimeout(&RTTTimer))) ;
		RTTStopTimer(RTT1);

#if PS5017_EN
		// 5. Enable IRQ Interrupt
		M_ARM_IRQ_ENABLE();

		// 6. Recover RTT0 Clock Source To OSC
		//PMUSwitchClockSrc(PMU_FLL_CMD_MODE_OSC_BIT); // E17 remove FLL circuit
#else /* PS5017_EN */
		// 5. Enable IRQ Interrupt
		M_ARM_IRQ_ENABLE();

		// 6. Recover RTT0 Clock Source To OSC
		PMUSwitchClockSrc(PMU_FLL_CMD_MODE_OSC_BIT);
#endif /* PS5017_EN */


		// 7. Check Result
		if (SUCCESS == RTTCheckFLLAndRCVariation()) {
			U32 ulTotal = 0;
			for (U8 ubCnt = 0; ubCnt < RTT_FLL_AND_RC_CALIBRATION_CNT; ++ubCnt) {
				ulTotal += gRTTFLLAndRCCalibration.ulRTT0CntPerHundredMillisecond[ubCnt];
			}
			gRTTVariation.uwFLLAndRCVariationCoefficient = (U16)(ulTotal / (RTT_FLL_AND_RC_CALIBRATION_CNT * RTT_FLL_CALIBRATION_INT_INTERVAL_MS));
			ubResult = SUCCESS;
			break;
		}
		else {
			if (0 == ubReEnter) {
				ubReEnter = 1;
			}
			else {
				gRTTVariation.uwFLLAndRCVariationCoefficient = (U16)RTT_FLL_AND_RC_CALIBRATION_FAIL_SIGN;
				ubResult = FAIL;
				break;
			}
		}
	} while (ubReEnter);

	// 8. Recover RTT1 For ZQ Delay
	RTTSetPD1RTTTimeout(RTT1, SCALE_50NS, REPEAT_MODE, FIP_ZQ_TIMEOUT);
	return ubResult;
}
#endif /*BURNER_MODE_EN*/
#endif /* (!PS5021_EN) */

