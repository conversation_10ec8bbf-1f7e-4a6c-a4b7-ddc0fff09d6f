#ifndef SEC_RSA_API_H_
#define SEC_RSA_API_H_

#define FAST_PKE 					(TRUE)
#define ECC_RSA_OP_CODE_CNT 		(22)
#define ECC_RSA_OP_CODE_CNT_EX		(20)

#if PS5017_EN
#define	RSA_DIGEST_SIZE				(512) // RSA 4096 Signature size
#define	SECURITY_RSA_KEY_LENGTH		(32)  // Key hash length in ROM
#define	SECURITY_AA_SUPOORT_RSA_KEY_NUB	(32)
#define	SECURITY_BB_SUPOORT_RSA_KEY_NUB	(32)

#define	RSA_PUBLIC_KEY_N_SIZE		(512)
#define	RSA_PUBLIC_KEY_E_SIZE		(4)
#define	RSA_PUBLIC_KEY_N0_SIZE		(8)//MMUL 64
#define	RSA_PUBLIC_KEY_R_SIZE		(512)
#define	RSA_PUBLIC_KEY_RR_SIZE		(512)
#define RSA_PUBLIC_KEY_MESSAGE_SIZE	(768) // For RSA_SHA512_4096_ALGORITHM_TYPE_PKCS1_V1_5
#define RSA_PUBLIC_KEY_SIGN_SIZE	(512) // For RSA_SHA512_4096_ALGORITHM_TYPE_PKCS1_V1_5
#else /* PS5017_EN */
#define	RSA_DIGEST_SIZE				(256)
#define	SECURITY_RSA_KEY_LENGTH		(32)
#define	SECURITY_AA_SUPOORT_RSA_KEY_NUB	(5)
#define	SECURITY_BB_SUPOORT_RSA_KEY_NUB	(32)

#define	RSA_PUBLIC_KEY_N_SIZE		(256)
#define	RSA_PUBLIC_KEY_E_SIZE		(4)
#define	RSA_PUBLIC_KEY_N0_SIZE		(4)
#define	RSA_PUBLIC_KEY_R_SIZE		(256)
#define	RSA_PUBLIC_KEY_RR_SIZE		(256)
#define RSA_PUBLIC_KEY_MESSAGE_SIZE	(128)
#define RSA_PUBLIC_KEY_SIGN_SIZE	(256)
#endif /* PS5017_EN */

#define RSA_PUBLIC_KEY_TOTAL_SIZE	(RSA_PUBLIC_KEY_N_SIZE + RSA_PUBLIC_KEY_E_SIZE + RSA_PUBLIC_KEY_N0_SIZE + RSA_PUBLIC_KEY_R_SIZE + RSA_PUBLIC_KEY_RR_SIZE)

#define PKE_USE_MEMORY_A    (0)
#define PKE_USE_MEMORY_B    (1)
#define PKE_USE_MEMORY_C    (2)
#define PKE_USE_MEMORY_R    (3)
#define PKE_USE_MEMORY_Q    (4)

#if PS5017_EN
#define	SEC_PKE_OUTPUT_ADDR_OFFSET	(0x41)
#define	SEC_PKE_BASE_ADDR_OFFSET	(0x41)
#define	SEC_PKE_POW_ADDR_OFFSET		(0x41)
#else /* PS5017_EN */
#define	SEC_PKE_OUTPUT_ADDR_OFFSET	(0x21)
#define	SEC_PKE_BASE_ADDR_OFFSET	(0x21)
#define	SEC_PKE_POW_ADDR_OFFSET		(0x21)
#endif /* PS5017_EN */

#define PKE_MEMORY_REG_N0_R11  (11)

#define	SEC_RSA_PUBLIC_EXPONENT				(65537)
#define	SEC_RSA_TRAILER_FIELD				(0xBC)

#define ASN1_TYPE_SHA256_DIGEST_INFO_LEN	(19)
#define ASN1_TYPE_SHA512_DIGEST_INFO_LEN	(19)
#define RSA_PKCS1_V2_1_SHA256_KAT_SALT_LENGTH	(10)
#define RSA_PKCS1_V2_1_SHA512_KAT_SALT_LENGTH	(64)
#define HMS_EMSA_PSS_SALT_256_LENGTH		(32)
#define HMS_EMSA_PSS_SALT_512_LENGTH		(64)
#define EMSA_PSS_MPRIME_PADDING_LENGTH  	(8)
#define HMS_EMSA_PSS_EMBITS             	(SUPPORT_RSA_BIT_NUM_2048 - 1)

#define HASH_SHA256_LENGTH		(32)
#define HASH_SHA512_LENGTH		(64)

#if PS5017_EN
#define SUPPORT_RSA_BIT_NUM				(4096)
#else /* PS5017_EN */
#define SUPPORT_RSA_BIT_NUM				(2048)
#endif /* PS5017_EN */
#define SUPPORT_RSA_BYTE_NUM			(SUPPORT_RSA_BIT_NUM / 8)
#define SUPPORT_RSA_DOUBLE_WORD_NUM		(SUPPORT_RSA_BYTE_NUM / 4)

#define SUPPORT_RSA_BIT_NUM_2048			(2048)
#define SUPPORT_RSA_BYTE_NUM_2048			(SUPPORT_RSA_BIT_NUM_2048 >> 3)
#define SUPPORT_RSA_DOUBLE_WORD_NUM_2048	(SUPPORT_RSA_BIT_NUM_2048 >> 5)

#define SUPPORT_RSA_BIT_NUM_4096			(4096)
#define SUPPORT_RSA_BYTE_NUM_4096			(SUPPORT_RSA_BIT_NUM_4096 >> 3)
#define SUPPORT_RSA_DOUBLE_WORD_NUM_4096	(SUPPORT_RSA_BIT_NUM_4096 >> 5)

#define SUPPORT_ECDSA_P256_BIT_NUM			(256)
#define SUPPORT_ECDSA_P256_BYTE_NUM			(SUPPORT_ECDSA_P256_BIT_NUM >> 3)

#define SUPPORT_ECDSA_P384_BIT_NUM			(384)
#define SUPPORT_ECDSA_P384_BYTE_NUM			(SUPPORT_ECDSA_P384_BIT_NUM >> 3)

#define SUPPORT_SM2_BIT_NUM					(256)
#define SUPPORT_SM2_BYTE_NUM				(SUPPORT_SM2_BIT_NUM >> 3)

#if PS5017_EN
#define SECURITY_ROM_AA_RSA_PUBLIC_MODE_OPCODE_4096			(0x0005B5C0) // 160B
#define SECURITY_ROM_AA_RSA4096_N							(0x0005E5D0) // 512B
#define SECURITY_ROM_AA_RSA4096_R							(0x0005E7D0) // 512B
#define SECURITY_ROM_AA_RSA4096_RR							(0x0005E9D0) // 512B
#define SECURITY_ROM_AA_RSA4096_N0							(0x0005E2C8) // 8B
#define SECURITY_ROM_AA_SHA256_RSA4096_MESSAGE				(0x0005E2D0) // 768B
#define SECURITY_ROM_AA_SHA256_RSA4096_SIGN					(0x0005EBD0) // 512B
#define SECURITY_ROM_AA_SHA512_RSA4096_MESSAGE				(0x0005EDD0) // 768B
#define SECURITY_ROM_AA_SHA512_RSA4096_SIGN					(0x0005F0D0) // 512B

#define SECURITY_ROM_AA_RSA4096_KAT1V5_N					(0x0005F5D8) // 512B
#define SECURITY_ROM_AA_RSA4096_KAT1V5_R					(0x0005F7D8) // 512B
#define SECURITY_ROM_AA_RSA4096_KAT1V5_RR					(0x0005F9D8) // 512B
#define SECURITY_ROM_AA_RSA4096_KAT1V5_N0					(0x0005F2D0) // 8B
#define SECURITY_ROM_AA_SHA256_RSA4096_KAT1V5_MESSAGE		(0x0005F2D8) // 768B
#define SECURITY_ROM_AA_SHA256_RSA4096_KAT1V5_SIGN			(0x0005FBD8) // 512B
#define SECURITY_ROM_AA_SHA512_RSA4096_KAT1V5_MESSAGE		(0x0005FDD8) // 768B
#define SECURITY_ROM_AA_SHA512_RSA4096_KAT1V5_SIGN			(0x000600D8) // 512B
#define SECURITY_ROM_AA_ASN1_HASH_SHA256					(0x0005BCD4) // 19B
#define SECURITY_ROM_AA_ASN1_HASH_SHA512					(0x0005BCE8) // 19B

#define SECURITY_ROM_AA_RSA4096_KAT2V1_N					(0x000608E0) // 512B
#define SECURITY_ROM_AA_RSA4096_KAT2V1_R					(0x00060AE0) // 512B
#define SECURITY_ROM_AA_RSA4096_KAT2V1_RR					(0x00060CE0) // 512B
#define SECURITY_ROM_AA_RSA4096_KAT2V1_N0					(0x000602D8) // 8B
#define SECURITY_ROM_AA_SHA256_RSA4096_KAT2V1_MESSAGE		(0x000602E0) // 768B
#define SECURITY_ROM_AA_SHA256_RSA4096_KAT2V1_SIGN			(0x00060EE0) // 512B
#define SECURITY_ROM_AA_SHA512_RSA4096_KAT2V1_MESSAGE		(0x000610E0) // 768B
#define SECURITY_ROM_AA_SHA512_RSA4096_KAT2V1_SIGN			(0x000613E0) // 512B
#define SECURITY_ROM_AA_RSA_SHA256_KAT2V1_SALTVAL			(0x0005BCFC) // 10B
#define SECURITY_ROM_AA_RSA_SHA512_KAT2V1_SALTVAL			(0x0005BD08) // 64B

#define SECURITY_ROM_BB_RSA_PUBLIC_MODE_OPCODE_4096			(0x0005D1A0) // 160B
#define SECURITY_ROM_BB_RSA4096_N							(0x000601F8) // 512B
#define SECURITY_ROM_BB_RSA4096_R							(0x000603F8) // 512B
#define SECURITY_ROM_BB_RSA4096_RR							(0x000605F8) // 512B
#define SECURITY_ROM_BB_RSA4096_N0							(0x0005FEF0) // 8B
#define SECURITY_ROM_BB_SHA256_RSA4096_MESSAGE				(0x0005FEF8) // 768B
#define SECURITY_ROM_BB_SHA256_RSA4096_SIGN					(0x000607F8) // 512B
#define SECURITY_ROM_BB_SHA512_RSA4096_MESSAGE				(0x000609F8) // 768B
#define SECURITY_ROM_BB_SHA512_RSA4096_SIGN					(0x00060CF8) // 512B

#define SECURITY_ROM_BB_RSA4096_KAT1V5_N					(0x00061200) // 512B
#define SECURITY_ROM_BB_RSA4096_KAT1V5_R					(0x00061400) // 512B
#define SECURITY_ROM_BB_RSA4096_KAT1V5_RR					(0x00061600) // 512B
#define SECURITY_ROM_BB_RSA4096_KAT1V5_N0					(0x00060EF8) // 8B
#define SECURITY_ROM_BB_SHA256_RSA4096_KAT1V5_MESSAGE		(0x00060F00) // 768B
#define SECURITY_ROM_BB_SHA256_RSA4096_KAT1V5_SIGN			(0x00061800) // 512B
#define SECURITY_ROM_BB_SHA512_RSA4096_KAT1V5_MESSAGE		(0x00061A00) // 768B
#define SECURITY_ROM_BB_SHA512_RSA4096_KAT1V5_SIGN			(0x00061D00) // 512B
#define SECURITY_ROM_BB_ASN1_HASH_SHA256					(0x0005D8FC) // 19B
#define SECURITY_ROM_BB_ASN1_HASH_SHA512					(0x0005D910) // 19B

#define SECURITY_ROM_BB_RSA4096_KAT2V1_N					(0x00062508) // 512B
#define SECURITY_ROM_BB_RSA4096_KAT2V1_R					(0x00062708) // 512B
#define SECURITY_ROM_BB_RSA4096_KAT2V1_RR					(0x00062908) // 512B
#define SECURITY_ROM_BB_RSA4096_KAT2V1_N0					(0x00061F00) // 8B
#define SECURITY_ROM_BB_SHA256_RSA4096_KAT2V1_MESSAGE		(0x00061F08) // 768B
#define SECURITY_ROM_BB_SHA256_RSA4096_KAT2V1_SIGN			(0x00062B08) // 512B
#define SECURITY_ROM_BB_SHA512_RSA4096_KAT2V1_MESSAGE		(0x00062D08) // 768B
#define SECURITY_ROM_BB_SHA512_RSA4096_KAT2V1_SIGN			(0x00063008) // 512B
#define SECURITY_ROM_BB_RSA_SHA256_KAT2V1_SALTVAL			(0x0005D924) // 10B
#define SECURITY_ROM_BB_RSA_SHA512_KAT2V1_SALTVAL			(0x0005D930) // 64B
#endif /* PS5017_EN */

typedef struct rsa_lib_mpi {
	U16 uwn;            /*!<  total size of limbs Unit: 4Byte */
	U32 *pulp;           /*!<  U32 pointer to limbs  */
} SecMPI_t;

typedef struct mem_sel_adr {
	U8 ubSelect;   /* memory select  */
	U8 ubAddr;   /* memory address */
} SecMSA_t;

typedef struct {
	struct {
		U32 ulID;
		U8 ubN[SUPPORT_RSA_BYTE_NUM];
		U32 ulE;
	} key;
#if PS5017_EN
	U64 ulN0;
#else /* PS5017_EN */
	U32 ulN0;
#endif /* PS5017_EN */
	U8 ubR[SUPPORT_RSA_BYTE_NUM];
	U8 ubRR[SUPPORT_RSA_BYTE_NUM];
} RSAPublicKey_t;

typedef struct {
	U16 uwRSAByteLength;
	U8 ubCodeSignSHAMode;
	U8 ubCodeSignHashLength;
} RSACodeSignInfo_t;

AOM_SECURITY U8 SecParseSignatureParameter(U32 ulBufBase, U8 ubSigFormat, U8 ubIsDoubleSign, U32 ulKeyID, U32 ulDigestAdr, U32 ulDstAddr, RSAPublicKey_t *pRSAPublicKey);
AOM_SECURITY U8 SecVerifyPKCS1(U32 ulSHA256Adr, U32 ulDecryptDesAdr, U8 ubRSAAlgoType, U32 ulTempBufAddr, RSACodeSignInfo_t *pulCodeSignInfo);
AOM_SECURITY U8 SecRSADecode(U32 ulSignatureSrcAdr, U32 ulDecryptDesAdr, RSAPublicKey_t *pRSAPublicKey);
AOM_SECURITY void SecCodeSignAlgorithmInfoInit(U8 ubRSAAlogrithmType, RSACodeSignInfo_t *pulCodeSignInfo);
AOM_SECURITY U8 SecRSA4096KnownAnswerTest(U8 ubRSAAlgoType, U32 ulSignatureAddr, U32 ulHashAddr, U32 ulDecryptDstAddr, U32 ulTempBufAddr);

#endif /* SEC_RSA_API_H_ */
