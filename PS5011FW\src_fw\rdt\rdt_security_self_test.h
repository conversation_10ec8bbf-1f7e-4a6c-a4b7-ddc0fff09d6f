#ifndef _RDT_RDT_SECURITY_SELF_TEST_H_
#define _RDT_RDT_SECURITY_SELF_TEST_H_
#include "setup.h"

#if RDT_MODE_EN
#include "rdt/rdt_api.h"
//#include "rdt/rdt_tamt.h"

enum RDT_SECURITY_ST {
	RDT_SECURITY_ST_SHA1 = 0x0,
	RDT_SECURITY_ST_SHA256,
	RDT_SECURITY_ST_SHA512,
	RDT_SECURITY_ST_SM3,
	RDT_SECURITY_ST_SHA512_256,
	RDT_SECURITY_ST_PKE,
	RDT_SECURITY_ST_SM4,
	RDT_SECURITY_ST_AES,
	RDT_SECURITY_ST_NUM
};

#define RDT_SECURITY_ST_TEST_LOOP       (1000)

BOOL rdt_api_security_self_test(RDT_API_STRUCT_PTR rdt);
#endif

#endif /* _MTFW_MTFW_SECURITY_SELF_TEST_H_ */
