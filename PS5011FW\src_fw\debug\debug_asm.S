#include "common/mem.h"

.global DebugCriticalAssertEntry

.type DebugCriticalAssertEntry, "function"
DebugCriticalAssertEntry:
	STMDB sp!,{r0}									// Push R0 into Stack
	LDR r0, =BTCM1_DEBUG_INFO_ADDRESS             // Move Debug Info Addr into R0  	
	STMIA r0!,{r1-r12}								// Store R1-R12 into Debug Info Addr and Update R0
	STR sp,[r0], #4									// Store SP into the position which R0 point at
	STR lr,[r0]										// Store LR into the position which R0 point at
	LDMIA sp!,{r0}									// Pop R0 out of Stack
	B DebugCriticalError							// Branch to DebugCriticalError and update Link Register
DebugRecordCPURegister_End:
.size DebugCriticalAssertEntry, (DebugRecordCPURegister_End - DebugCriticalAssertEntry)

.end
