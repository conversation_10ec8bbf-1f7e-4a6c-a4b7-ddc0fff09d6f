#ifndef _VUC_WRITEINFO_H_
#define _VUC_WRITEINFO_H_

/*
 * --------------------------
 * definitions
 *--------------------------
 */
#define VUC_WRITEINFO_FIRST_IN (1)

typedef enum WriteInfoStateEnum {
	WRITE_INFO_INIT,
	WRITE_INFO_PROCESS,
	WRITE_INFO_UPDATE_IDPAGE,
	WRITE_INFO_FINISH
} WriteInfoStateEnum_t;
/*
 * --------------------------
 * extern global variables
 *--------------------------
 */
extern U8 const gubInfoBlock_Title[6];
extern U8 gubNeedUpdateIDPage;

/*
 * --------------------------
 * public prototypes
 *--------------------------
 */
AOM_VUC void VUC_WriteInfo(VUC_OPT_HCMD_PTR pCmd);
AOM_VUC U8 WriteInfo(VUC_OPT_HCMD_PTR_t pCmd);

#endif /* _VUC_WRITEINFO_H_ */
