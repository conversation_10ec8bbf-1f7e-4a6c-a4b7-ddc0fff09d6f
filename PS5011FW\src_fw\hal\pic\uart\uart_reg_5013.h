/*
 * uart_reg.h
 *
 *  Created on: 2017�~8��24��
 *      Author: hyt_huang
 */

#ifndef _E13_UART_REG_H_
#define _E13_UART_REG_H_

#include "setup.h"
#include "typedef.h"
#include "symbol.h"
#include "mem.h"

#define UART_REG_OFFSET		(0x0C00)
#define UART_REG_BASE		(PIC_REG_ADDRESS + UART_REG_OFFSET)


#define R8_UART		((volatile U8 *)  UART_REG_BASE)
#define R16_UART	((volatile U16 *) UART_REG_BASE)
#define R32_UART	((volatile U32 *) UART_REG_BASE)

#define R32_UART_UART_CTRL					(0x00 >> 2)
#define		BAUD_RATE_SHIFT						(0)
#define		BAUD_RATE_MASK						(BIT_MASK(24))
#define		OOB_EN_SHIFT						(24)
#define		OOB_EN_MASK							(BIT_MASK(1))
#define		RX_BAUD_EN_SHIFT					(26)
#define		RX_BAUD_EN_MASK						(BIT_MASK(1))
#define		TX_BAUD_EN_SHIFT					(27)
#define		TX_BAUD_EN_MASK						(BIT_MASK(1))
#define		RX_LAT_POSI_SHIFT					(28)
#define		RX_LAT_POSI_MASK					(BIT_MASK(3))
#define		RX_RST_SHIFT						(31)
#define		RX_RST_MASK							(BIT_MASK(1))
#define     OOB_EN_BIT                     		(OOB_EN_MASK << OOB_EN_SHIFT)			//BIT24
#define     RX_BAUD_EN_BIT                 		(RX_BAUD_EN_MASK << RX_BAUD_EN_SHIFT)	//BIT26
#define     TX_BAUD_EN_BIT                 		(TX_BAUD_EN_MASK << TX_BAUD_EN_SHIFT)	//BIT27
#define     RX_LAT_POSI_BIT                		(RX_LAT_POSI_MASK << RX_LAT_POSI_SHIFT)	//BIT28
#define     RX_RESET_BIT                   		(RX_RST_MASK << RX_RST_SHIFT)			//BIT31


#define R32_UART_TX_TRIG					(0x04 >> 2)
#define		TX_TRIG_SHIFT						(0)
#define		TX_TRIG_MASK						(BIT_MASK(1))
#define		EMPTY_CMPT_EN_SHIFT					(4)
#define		EMPTY_CMPT_EN_MASK					(BIT_MASK(1))
#define		TX_OUT_SEL_SHIFT					(5)
#define		TX_OUT_SEL_MASK						(BIT_MASK(2))
#define		OOB_RX_BUF_NOT_EMPTY_SHIFT			(8)
#define		OOB_RX_BUF_NOT_EMPTY_MASK			(BIT_MASK(1))



#define R32_UART_FTO_PERIOD					(0x08 >> 2)
#define		FRM_TO_TMR_SHIFT					(0)
#define		FRM_TO_TMR_MASK						(BIT_MASK(24))

#define R32_UART_TX_PTK_CFG					(0x0C >> 2)
#define		TX_PKT_TYP_SHIFT					(0)
#define		TX_PKT_TYP_MASK						(BIT_MASK(8))
#define		TX_PAYLD_LEN_SHIFT					(8)
#define		TX_PAYLD_LEN_MASK					(BIT_MASK(8))

#define R8_UART_TX_DATA_X					(0x10 >> 0)

#define R32_UART_UR_INT_EN					(0x50 >> 2)
#define		PKT_CRC_ERR_INT_EN_SHIFT			(0)
#define		PKT_CRC_ERR_INT_EN_MASK				(BIT_MASK(1))
#define		PKT_LEN_ERR_INT_EN_SHIFT			(1)
#define		PKT_LEN_ERR_INT_EN_MASK				(BIT_MASK(1))
#define		PKT_FRAMING_ERR_INT_EN_SHIFT		(2)
#define		PKT_FRAMING_ERR_INT_EN_MASK			(BIT_MASK(1))
#define		PKT_TO_ERR_INT_EN_SHIFT				(3)
#define		PKT_TO_ERR_INT_EN_MASK				(BIT_MASK(1))
#define		PKT_START_ERR_INT_EN_SHIFT			(4)
#define		PKT_START_ERR_INT_EN_MASK			(BIT_MASK(1))
#define		PKT_RX_INT_EN_SHIFT					(5)
#define		PKT_RX_INT_EN_MASK					(BIT_MASK(1))
#define		PKT_TX_CMPLT_INT_EN_SHIFT			(6)
#define		PKT_TX_CMPLT_INT_EN_MASK			(BIT_MASK(1))
#define		RX_FRAME_ERR_INT_EN_SHIFT			(8)
#define		RX_FRAME_ERR_INT_EN_MASK			(BIT_MASK(1))
#define		RX_OVFLW_INT_EN_SHIFT				(9)
#define		RX_OVFLW_INT_EN_MASK				(BIT_MASK(1))
#define		RX_NOT_EMPTY_INT_EN_SHIFT			(10)
#define		RX_NOT_EMPTY_INT_EN_MASK			(BIT_MASK(1))
#define		TX_CMPLT_INT_EN_SHIFT				(11)
#define		TX_CMPLT_INT_EN_MASK				(BIT_MASK(2))
#define		TX_CMPLT_INT_EN_FIFO1_MASK			(BIT_MASK(1))
#define		READ_AXI_ERR_SHIFT					(13)
#define		READ_AXI_ERR_MASK					(BIT_MASK(1))
#define		LOAD_ROM_EN_SHIFT					(14)
#define		LOAD_ROM_EN_MASK					(BIT_MASK(1))
#define		LOAD_ROM_AXI_ERR_SHIFT				(15)
#define		LOAD_ROM_AXI_ERR_MASK				(BIT_MASK(1))

#define R32_RX_PKT_INFO						(0x54 >> 2)
#define		RX_PKT_TYPE_SHIFT					(0)
#define		RX_PKT_TYPE_MASK					(BIT_MASK(8))
#define		RX_PKT_PAYLD_SHIFT					(8)
#define		RX_PKT_PAYLD_MASK					(BIT_MASK(8))
#define		RX_CS_SHIFT							(16)
#define		RX_CS_MASK							(BIT_MASK(8))

#define R32_UART_RX_FIFO_DATA				(0x58 >> 2)
#define		RX_FIFO_DATA_SHIFT					(0)
#define		RX_FIFO_DATA_MASK					(BIT_MASK(8))
#define		RX_FIFO_EMPTY_SHIFT					(8)
#define		RX_FIFO_EMPTY_MASK					(BIT_MASK(1))
#define 	RX_FIFO_EMPTY_BIT					(RX_FIFO_EMPTY_MASK << RX_FIFO_EMPTY_SHIFT)		//BIT8

#define R8_UART_RX_FIFO_DATA				(0x58 >> 0)
#define R8_UART_FIFO_EMPTY					(0x59 >> 0)

#define R8_UART_RX_OOB_DATA_X				(0x60 >> 0)

#define R32_UART_URAT_INT_EN				(0x70 >> 2)
#define		OOB_PKT_CRC_ERR_INT_EN_SHIFT		(0)
#define		OOB_PKT_CRC_ERR_INT_EN_MASK			(BIT_MASK(1))
#define		OOB_PKT_LEN_ERR_INT_EN_SHIFT		(1)
#define		OOB_PKT_LEN_ERR_INT_EN_MASK			(BIT_MASK(1))
#define		OOB_PKT_FRAMING_ERR_INT_EN_SHIFT	(2)
#define		OOB_PKT_FRAMING_ERR_INT_EN_MASK		(BIT_MASK(1))
#define		OOB_PKT_TO_ERR_INT_EN_SHIFT			(3)
#define		OOB_PKT_TO_ERR_INT_EN_MASK			(BIT_MASK(1))
#define		OOB_PKT_START_ERR_INT_EN_SHIFT		(4)
#define		OOB_PKT_START_ERR_INT_EN_MASK		(BIT_MASK(1))
#define		OOB_PKT_RX_INT_EN_SHIFT				(5)
#define		OOB_PKT_RX_INT_EN_MASK				(BIT_MASK(1))
#define		OOB_PKT_TX_CMPLT_INT_EN_SHIFT		(6)
#define		OOB_PKT_TX_CMPLT_INT_EN_MASK		(BIT_MASK(1))
#define		UART_RX_FRAME_ERR_INT_EN_SHIFT		(8)
#define		UART_RX_FRAME_ERR_INT_EN_MASK		(BIT_MASK(1))
#define		UART_RX_OVFLW_INT_EN_SHIFT			(9)
#define		UART_RX_OVFLW_INT_EN_MASK			(BIT_MASK(1))
#define		UART_RX_NOT_EMPTY_INT_EN_SHIFT		(10)
#define		UART_RX_NOT_EMPTY_INT_EN_MASK		(BIT_MASK(1))
#define		UART_TX_FIFO1_CMPLT_INT_SHIFT		(11)
#define		UART_TX_FIFO1_CMPLT_INT_MASK		(BIT_MASK(1))
#define		UART_TX_FIFO2_CMPLT_INT_SHIFT		(12)
#define		_FIFO2_CMPLT_INT_MASK		(BIT_MASK(1))
#define		LOAD_ROM_INT_SHIFT					(14)
#define		LOAD_ROM_INT_MASK					(BIT_MASK(1))
#define     PKT_CRC_ERR_INT_EN             		(OOB_PKT_CRC_ERR_INT_EN_MASK << OOB_PKT_CRC_ERR_INT_EN_SHIFT)		//BIT0
#define     PKT_LEN_ERR_INT_EN             		(OOB_PKT_LEN_ERR_INT_EN_MASK << OOB_PKT_LEN_ERR_INT_EN_SHIFT)		//BIT1
#define     PKT_FRAMING_ERR_INT_EN         		(OOB_PKT_FRAMING_ERR_INT_EN_MASK << OOB_PKT_FRAMING_ERR_INT_EN_SHIFT)	//BIT2
#define     PKT_TO_ERR_INT_EN              		(OOB_PKT_TO_ERR_INT_EN_MASK << OOB_PKT_TO_ERR_INT_EN_SHIFT)			//BIT3
#define     PKT_START_ERR_INT_EN           		(OOB_PKT_START_ERR_INT_EN_MASK << OOB_PKT_START_ERR_INT_EN_SHIFT)	//BIT4
#define     PKT_RX_INT_EN                  		(OOB_PKT_RX_INT_EN_MASK << OOB_PKT_RX_INT_EN_SHIFT)					//BIT5
#define     PKT_TX_CMPLT_INT_EN            		(OOB_PKT_TX_CMPLT_INT_EN_MASK << OOB_PKT_TX_CMPLT_INT_EN_SHIFT)		//BIT6
#define     RX_FRAME_ERR_INT_EN            		(UART_RX_FRAME_ERR_INT_EN_MASK << UART_RX_FRAME_ERR_INT_EN_SHIFT)	//BIT8
#define     RX_OVFLW_INT_EN                		(UART_RX_OVFLW_INT_EN_MASK << UART_RX_OVFLW_INT_EN_SHIFT)			//BIT9
#define     RX_NOT_EMPTY_INT_EN            		(UART_RX_NOT_EMPTY_INT_EN_MASK << UART_RX_NOT_EMPTY_INT_EN_SHIFT)	//BIT10
#define     TX_FIFO1_CMPLT_INT					(UART_TX_FIFO1_CMPLT_INT_MASK << UART_TX_FIFO1_CMPLT_INT_SHIFT)		//BIT11
#define     TX_FIFO2_CMPLT_INT          		(UART_TX_FIFO2_CMPLT_INT_MASK << UART_TX_FIFO2_CMPLT_INT_SHIFT)		//BIT12
#define     LOAD_ROM_INT		        		(LOAD_ROM_INT_MASK << LOAD_ROM_INT_SHIFT)							//BIT14


#define R8_UART_S_FIFO1_WDATA				(0x74 >> 0)

#define R32_UART_S_FIFO1_CTRL_ST			(0x78 >> 2)
#define		S_FIFO_FULL_SHIFT					(0)
#define		S_FIFO_FULL_MASK					(BIT_MASK(1))
#define		S_FIFO_EMPTY_SHIFT					(1)
#define		S_FIFO_EMPTY_MASK					(BIT_MASK(1))
#define		TX_S_FIFO_EN_SHIFT					(8)
#define		TX_S_FIFO_EN_MASK					(BIT_MASK(1))

#define R8_UART_S_FIFO2_WDATA				(0x80 >> 0)

#define R32_UART_S_FIFO2_CTRL_ST			(0x84 >> 2)
#define		S_FIFO2_FULL_SHIFT					(0)
#define		S_FIFO2_FULL_MASK					(BIT_MASK(1))
#define		S_FIFO2_EMPTY_SHIFT					(1)
#define		S_FIFO2_EMPTY_MASK					(BIT_MASK(1))
#define		TX_S_FIFO2_EN_SHIFT					(8)
#define		TX_S_FIFO2_EN_MASK					(BIT_MASK(1))
#define		TX_S_FIFO2_EN_BIT					(BIT8)


#define R32_UART_AXI_WR_START_ADDR			(0x90 >> 2)
#define R32_UART_AXI_RD_START_ADDR			(0x94 >> 2)

#define R32_UART_AXI_RD_CONF				(0x98 >> 2)
#define		DOORBELL_MODE_SHIFT					(0)
#define		DOORBELL_MODE_MASK					(BIT_MASK(1))
#define		DOORBELL_WR_SHIFT					(8)
#define		DOORBELL_WR_MASK					(BIT_MASK(1))
#define		DOORBELL_FULL_SHIFT					(16)
#define		DOORBELL_FULL_MASK					(BIT_MASK(1))

#define R16_UART_AXI_RD_BUF_LEN				(0x9C >> 1)

#define R32_UART_DOORBELL_WR_PTR			(0xA0 >> 2)
#define R32_UART_DOORBELL_RD_PTR			(0xA4 >> 2)

#endif /* _E13_UART_REG_H_ */
