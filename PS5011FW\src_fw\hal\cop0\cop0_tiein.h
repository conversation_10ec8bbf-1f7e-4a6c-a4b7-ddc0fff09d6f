#ifndef _COP0_TIEIN_H_
#define _COP0_TIEIN_H_
#include "typedef.h"
#include "hal/cop0/cop0_cmd.h"
#include "db_mgr/fw_cmd_table.h"

#define COP0_FRAME_NUM_PER_16KPAGE	(4)
#define COP0_MAX_L4K_NUM_PER_16KPAGE	(16)

//COP0 TARGET BLOCK
#define COP0_BLOCK_FMAN                     (0xA)
#define COP0_BLOCK_XPCA                     (0xB)

//#define COP0_FMAN_CMD_WRITE                 (0x1)
//#define COP0_FMAN_CMD_READ                  (0x2)
//#define COP0_FMAN_CMD_ERASE                 (0x3)
//#define COP0_FMAN_CMD_UNLOCK                (0x4)

//TIE IN SOURCE
//#define TARGET_CPU0			0
//#define TARGET_COP1         1

//TIE IN TARGET Q
#define COP0_NORMAL_Q			0
//#define COP1_2_COP0			1
#define	COP0_MERGE_Q0			2
#define COP0_MERGE_Q1			3

//TIE IN CMD
#define COP0_TIE_IN_READ     (1)
#define COP0_TIE_IN_WRITE    (2)
#define COP0_TIE_IN_ERASE    (3)
#define COP0_TIE_IN_UNLOCK   (4)
#define COP0_TIE_IN_BARRIER  (5)
#define COP0_TIE_IN_INORDERREAD	(6)


//DEF DAT
#define COP0_USER_DATA0      (0) //WSHuang20161102
#define COP0_DEF_UDEF_BIT		BIT0
#define COP0_DEF_PCA_BIT		BIT1
#define COP0_DEF_BUF_BIT		BIT2
#define COP0_DEF_LCA_BIT		BIT3
#define COP0_DEF_FWSET_BIT		BIT4
#define COP0_DEF_SEED_BIT		BIT5
#define COP0_DEF_RS_BIT			BIT6
#if (COP0_FEATURE_EN) //WSHuang20161007
#define COP0_DEF_ZIP_BIT		BIT7
#define COP0_DEF_END         (0xFFFFFFFF)
#define COP0_DEF_BIT_NUM     (8)
#endif /* (COP0_FEATURE_EN) */

#define COP0_ATTR_CONT_BIT		BIT15
#define COP0_ATTR_BMU_EN_BIT		BIT7
#define COP0_ATTR_BUF_BIT		BIT6

#define COP0_TIEIN_PRIORITY_DEFAULT	(0)
#define COP0_TIEIN_PRIORITY_LOW		(0)
#define COP0_TIEIN_PRIORITY_MIDDLE	(1)
#define COP0_TIEIN_PRIORITY_HIGH0	(2)
#define COP0_TIEIN_PRIORITY_HIGH1	(3)

//Tie-In Read Mode : Must choose 1 Mode
#define COP0_TIE_MODE_READ_HOST		(0)
#define COP0_TIE_MODE_READ_FW		(1)
#define COP0_TIE_MODE_READ_BUF_ADDR	(2)
#define COP0_TIE_MODE_READ_PREREAD	(3)

#define COP0_TIE_MODE_WRITE_NORMAL	(0)

#define COP0_TIE_MODE_ERASE_NORMAL	(0)

//Tie-In Read/Write/Erase Feature : Can choose many feature
#define COP0_TIE_FEATURE_NONE				(0)
#define COP0_TIE_FEATURE_SLC_RWE_BIT		BIT1	//read/write/erase all can use this feature
#define COP0_TIE_FEATURE_LOCK_RWE_BIT		BIT2
#define COP0_TIE_FEATURE_P4K_BACKUP_RW_BIT	BIT3	//read/write can use this feature
#define COP0_TIE_FEATURE_ZIPBYPASS_R_BIT	BIT4	//Bypass ZIP feature (read only now)
#define COP0_TIE_FEATURE_LCA_CMP_R_BIT		BIT5
#define COP0_TIE_FEATURE_READ16K_R_BIT		BIT6
#define COP0_TIE_FEATURE_SERIAL_W_BIT		BIT7	//Write continuous buffer mode
#define COP0_TIE_FEATURE_BYPASS_VBRMP_BIT	BIT8

#define COP0_TIE_D1SLC_MODE_FROM_VBRMP	(0)
#define COP0_TIE_D1SLC_MODE_FROM_TIEIN 	(1)

#define COP0_D1_ERASE_BIT				BIT0
#define COP0_D3_ERASE_BIT				BIT1

#define COP0_NO_USE			(0xFFFFFFF0)
#define COP0_LCA_INVALID	(0xFFFFFFFF) //DYChen 20170327, with Bypass Read

#define COP0_RECEIVE_P4K     TRUE
#define COP0_DISCARD_P4K     FALSE

//User Define
//for flush D1
#define COP0_USERDEFINE_NON_DMA_BIT			BIT0
#define COP0_USERDEFINE_BYPASS_READ_BIT		BIT1

#define COP0_COPYBACK_MODE_NON_DMA_BIT		BIT0
#define COP0_COPYBACK_MODE_DMA_BIT			BIT1
#define COP0_COPYBACK_MODE_D3_TO_D3_BIT		BIT2

#define COP0_COPYBACK_DOING_BIT			BIT0
#define COP0_COPYBACK_FINISH_BIT		BIT1

//Always push cmd to cpu0
#define COP0_PUSH_CMD(cmd_ptr)          		COP0_TIEIN_NONBLOCK((cmd_ptr), 0)
#define COP0_BLOCK_PUSH_CMD(cmd_ptr)    		COP0_TIEIN_BLOCK((cmd_ptr), 0)

#define COP0_PUSH_CMD_FOR_COP1(cmd_ptr)         COP0_TIEIN_NONBLOCK((cmd_ptr), 1)
#define COP0_BLOCK_PUSH_CMD_FOR_COP1(cmd_ptr)   COP0_TIEIN_BLOCK((cmd_ptr), 1)

#define COP0_TIEIN_NONBLOCK(cmd, cpu_index)		COP0_TieinNonblock((cmd), (cpu_index))
#define COP0_TIEIN_BLOCK(cmd, cpu_index)		COP0_TieinBlock((cmd), (cpu_index))

//VBRMP
#define	COP0_VBRMP_DEBUG	(FALSE)


void COP0API_TieinCPURead(TIE_IN_PARAM_t *pTieInParamStruct);
void COP0API_TieinCOP1Read(TIE_IN_PARAM_t *pTieInParamStruct);
void COP0API_TieinProgram_LB_4K(TIE_IN_PARAM_t *pTieInParamStruct);
void COP0API_TieinProgram_LB(TIE_IN_PARAM_t *pTieInParamStruct);
void COP0API_TieinDirectlyProgram(TIE_IN_PARAM_t *pTieInParamStruct);
void COP0API_TieinErase(TIE_IN_PARAM_t *pTieInParamStruct);
void COP0API_TieinUnlock(TIE_IN_PARAM_t *pTieInParamStruct);
void COP0API_TieinBarrier(TIE_IN_PARAM_t *pTieInParamStruct);//Amour20161230

extern void COP0_VSTieInHostReadCmd(ReadCQInfo_t *ReadCQInfo);
extern void COP0_VSTieInHostWriteCmd(COP0HostWriteModeEnum_t COP0WriteMode, PCA_t ulFWPCA, WriteCQInfo_t *puoWriteCQInfo, U8 ubPlaneOffset);
extern void COP0_VSTieInReadCmd(COP0ReadSQPara_t *pCOP0ReadSQPara);
extern void COP0_VSTieInWriteCmd(COP0WriteSQPara_t *pCOP0WriteSQPara);
extern void COP0_VSTieInGCWriteCmd(COP0GCWriteSQPara_t *pCOP0GCWriteSQPara);
extern void COP0_VSTieInJournalWriteCmd(COP0JournalWriteSQPara_t *pCOP0JournalWriteSQPara);
extern void COP0_VSTieInEraseCmd(COP0EraseSQPara_t *pCOP0EraseSQPara);
extern void COP0_VSTieInBarrierCmd(COP0BarrierSQPara_t *pCOP0BarrierSQPara);

#endif /* _COP0_TIEIN_H_ */
