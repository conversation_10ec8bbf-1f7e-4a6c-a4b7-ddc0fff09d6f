#include "burner/Burner_api.h"
#include "hal/pic/uart/uart_api.h"
#include "hal/sys/api/efuc/efuse_api.h"
#include "nvme_api/nvme/shr_hal_nvme.h"
#include "vuc/VUC_CutEFuse.h"
#include "host/VUC_handler.h"

void VUC_CutEFuse(VUC_OPT_HCMD_PTR_t pCmd)
{

#if (PS5021_EN && E21_TODO)
	pCmd->ubState = FW_PROCESS_DONE;
	return;
#endif /* (PS5021_EN && E21_TODO) */
	M_UART(VUC_, "\nVUC_CUT_EFUSE");

	U8 ubBank = (pCmd->vuc_sqcmd.vendor.usrdef.ulRAM_L & VUC_CUT_EFUSE_BANK_MASK);
	U8 ubAddressOffset = (pCmd->vuc_sqcmd.vendor.usrdef.ulRAM_L & VUC_CUT_EFUSE_ADR_OFFSET_MASK) >> VUC_CUT_EFUSE_ADR_OFFSET_SHIFT;
	U8 ubBitOffset = (pCmd->vuc_sqcmd.vendor.usrdef.ulRAM_L & VUC_CUT_EFUSE_BIT_OFFSET_MASK) >> VUC_CUT_EFUSE_BIT_OFFSET_SHIFT;
	U8 ubSubFeature = pCmd->vuc_sqcmd.vendor.usrdef.ubSubFeature;
	U32 ulEfuData;
	U32 ulDataAdr = gulVUCBufAddr;

	M_UART(VUC_, "\nBank:%b, Address:%b, BitOft:%b", ubBank, ubAddressOffset, ubBitOffset);
#if PS5017_EN
	if (VUC_CUT_EFUSE_BY_SINGLE == ubSubFeature) { //Only can trim only one bit
		if ((VUC_ADM_NONDATA  != pCmd->hcmd.sata.nonuser.ubOPCode) && ((!VUC_PROTECT_EN) || (VUC_ADMIN_ENCRYPT_NO_DATA != pCmd->hcmd.sata.nonuser.ubOPCode))) {
			pCmd->ubState = CMD_ERROR;
			gVUCVar.ubVucCmdErrStatus = HOST_ERROR_FEATURE;
			return;
		}

		EFuseCutSoftwaremode(ubAddressOffset, ubBitOffset);
	}
	else if (VUC_CUT_EFUSE_BY_TABLE == ubSubFeature) { //Can trim one bank ( 8 DW = 32 Bytes)
		if ((VUC_ADM_WRITEDATA != pCmd->hcmd.sata.nonuser.ubOPCode) && ((!VUC_PROTECT_EN) || (VUC_ADMIN_ENCRYPT_WRITE_DATA != pCmd->hcmd.sata.nonuser.ubOPCode))) {
			pCmd->ubState = CMD_ERROR;
			gVUCVar.ubVucCmdErrStatus = HOST_ERROR_FEATURE;
			return;
		}

		U8 ubi, ubj;

		for (ubi = 0; ubi < EFUSE_DW_PER_BANK; ubi++) {
			ubAddressOffset = ubBank * EFUSE_DW_PER_BANK + ubi;
			ulEfuData = *(U32 *)(ulDataAdr + ubi * EFUSE_BYTE_PER_DW);

			for (ubj = 0; ubj < EFUSE_BIT_PER_DW; ubj++) {
				if (BIT(ubj) & ulEfuData) {
					EFuseCutSoftwaremode(ubAddressOffset, ubj);
				}
			}
		}
	}

	pCmd->ubState = FW_PROCESS_DONE;
#else /*PS5017_EN*/
	if (VUC_CUT_EFUSE_BY_SINGLE == ubSubFeature) {
		ulEfuData = BIT(ubBitOffset);

		EFuseCutFlow(ubAddressOffset, ulEfuData);
	}
	else if (VUC_CUT_EFUSE_BY_TABLE == ubSubFeature) {
		U8 ubi;
		for (ubi = 0; ubi < EFUSE_EACH_BANK_WIDE; ubi++) {
			ubAddressOffset = ubBank * EFUSE_EACH_BANK_WIDE + ubi;
			ulEfuData = *(U32 *)(ulDataAdr + ubi * EFUSE_EACH_BANK_WIDE);

			EFuseCutFlow(ubAddressOffset, ulEfuData);
		}
	}
#endif /*PS5017_EN*/
}
