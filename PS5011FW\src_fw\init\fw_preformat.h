#ifndef _FW_PREFORMAT_H_
#define _FW_PREFORMAT_H_

#include "setup.h"
#include "typedef.h"
#include "symbol.h"
#include "aom/aom_api.h"
#include "table/rut/rut_api.h"
#include "table/sys_block/sys_block_api.h"
#include "table/dbt/dbt_api.h"
#include "hal/dmac/dmac_api.h"

//******************************************
//	Definition
//******************************************
#define DEBUG_DBT_SCAN_UART FALSE

#define PB_NUMBER			(128)
#define INFO_BLK_SIZE		(0x1000)
#define RUT_SPARE_TABLE			(0x7800)

#define INIT_PREFORMAT_COMPARE_LCA	(FALSE)
#define INIT_PREFORMAT_COMPARE_DATA	(TRUE)

#define INIT_PREFORMAT_COMPARE_START_FRAME	(0)

#define PREFORMAT_512MB_EXTEND_LCA_UNIT_OP_NUMBER	(0x100000) // 512MB  (Sector)
#if UNIFIED_LOG_DEMAND_EXTEND_LCA_EN
#if (PS5017_EN)
#define PREFORMAT_1536MB_EXTEND_LCA_FOR_UNIFIED_LOG_UNIT_OP_NUMBER	(0x300000) //1.5GB (Sector)
#else /*(PS5017_EN)*/
#define PREFORMAT_1536MB_EXTEND_LCA_FOR_UNIFIED_LOG_UNIT_OP_NUMBER	(0) //Temp extend LCA = 12MB, could be put into 512MB.(Wait Micron VUC spec)
#endif /*(PS5017_EN)*/
#else /* UNIFIED_LOG_DEMAND_EXTEND_LCA_EN */
#define PREFORMAT_1536MB_EXTEND_LCA_FOR_UNIFIED_LOG_UNIT_OP_NUMBER      (0)
#endif /* UNIFIED_LOG_DEMAND_EXTEND_LCA_EN */

#define PREFORMAT_ERROR_INFO_BLOCK_D1_BEYOND		(0xED)
#define PREFORMAT_ERROR_GET_CODE_BLK_INVALID        (0xEE)
#define PREFORMAT_ERROR_GET_SYSTEM_AREA_INVALID     (0xEF)
#define PREFORMAT_ERROR_INFO_BLOCK_ERROR			(0xF0)
#define PREFORMAT_ERROR_NOT_ENOUGH_UNIT				(0xF1)
#define PREFORMAT_ERROR_NOT_ENOUGH_D1UNIT			(0xF2)
#if (U17_EN)
#define PREFORMAT_ERROR_NOT_ENOUGH_TOTALUNIT	    (0xF3)
#define PREFORMAT_ERROR_NOT_ENOUGH_FREEBLK		(0xF4)
#define PREFORMAT_ERROR_SPEED_TOO_HIGH_IN_U17 	(0xF5)
#define PREFORMAT_ERROR_PH_2ND_READ_DBT_HEADER_FAIL   (0xE0) //(0xF3)
#define PREFORMAT_ERROR_PH_BLK_LOST    				(0xE1) //(0xF4)
#define PREFORMAT_ERROR_PH_BLK_CHOOSE_THE_SAME    	(0xE2) //(0xF5)
#define PREFORMAT_ERROR_READ_FAIL_TOO_MUCH  		    (0xE3) //(0xF6)
#else  /* (U17_EN) */
#define PREFORMAT_ERROR_PH_2ND_READ_DBT_HEADER_FAIL (0xF3)
#define PREFORMAT_ERROR_PH_BLK_LOST    				(0xF4)
#define PREFORMAT_ERROR_PH_BLK_CHOOSE_THE_SAME    	(0xF5)
#define PREFORMAT_ERROR_READ_FAIL_TOO_MUCH  		(0xF6)
#endif /* (U17_EN) */
#define PREFORMAT_ERROR_MPPR_NOT_MATCH				(0xFD)
#define PREFORMAT_ERROR_TCG_AES_EN_XZIP_MODE_FAIL   (0xFE)
#define PREFORMAT_ERROR_XZIP_MODE_FAIL   			(0xFF)
#if VRLC_EN
#if (U17_EN)
#define PREFORMAT_ERROR_VRLC_RDT_INHERIT            (0xF6)
#define PREFORMAT_ERROR_CHECK_VRLC_VERSION          (0xF7)
#define PREFORMAT_ERROR_VRLC_FW_INHERIT             (0xF8)
#define PREFORMAT_ERROR_VRLC_TWO_SIDE_UECC          (0xF9)
#define PREFORMAT_ERROR_VRLC_TABLE_E3D_NOT_MATCH    (0xFA)
#define PREFORMAT_ERROR_VRLC_TABLE_PROGRAM_FAIL     (0xFB)
#else  /* (U17_EN) */
#define PREFORMAT_ERROR_VRLC_RDT_INHERIT            (0xF7)
#define PREFORMAT_ERROR_CHECK_VRLC_VERSION          (0xF8)
#define PREFORMAT_ERROR_VRLC_FW_INHERIT             (0xF9)
#define PREFORMAT_ERROR_VRLC_TWO_SIDE_UECC          (0xFA)
#define PREFORMAT_ERROR_VRLC_TABLE_E3D_NOT_MATCH    (0xFB)
#define PREFORMAT_ERROR_VRLC_TABLE_PROGRAM_FAIL     (0xFC)
#endif /* (U17_EN) */
#define MMO_TRIM_USE_BLK_CNT		(40)
#define MMO_TRIM_USE_UNIT_CNT		CEILING_DIV(MMO_TRIM_USE_BLK_CNT, PLANE_NUM)

//MMO State
typedef enum MMO_Stage {
	MMO_CHECK_INHERIT = 0,
	MMO_GET_UNIT,
	MMO_ERASE_UNIT,
	MMO_PROGRAM_UNIT,
#if DEBUG_VRLC_READ_MMO_UNIT_EN
	MMO_READ_UNIT,
#endif /* VRLC_DEBUG_READ_MMO_UNIT_EN */
	MMO_WAIT_TUNING,
	MMO_PROGRAM_TUNING_INFO,
	MMO_FREE_UNIT,
	MMO_ERROR_OCCUR,
	MMO_DONE
} MMO_Stage_t;

typedef enum MMOErrorEventModeEnum {
	MMO_RDT_INHERIT_FAIL = 1,
	MMO_CHECK_VRLC_VERSION_FAIL = 2,
	MMO_FW_INHERIT_FAIL = 4,
	MMO_TWO_SIDE_UECC_FAIL = 8,
	MMO_E3D_NOT_MATCH = 16,
	MMO_VRLC_TABLE_PROGRAM_FAIL = 32
} MMOErrorEventModeEnum_t;
#endif /* VRLC_EN */

typedef enum FTLInitScanSystemAreaEnum {
	SCAN_OK,
	SCAN_NONE
} FTLInitScanSystemAreaEnum_t;

typedef enum FTLInitScanDBTEnum {
	SCAN_VB_RUT,
	SCAN_DBT,
	SCAN_DBT_CODEBLK
} FTLInitScanDBTEnum_t;

typedef enum FTLPreFormatEnum {
	BURNER_PREFORMAT = 0,
	ICE_PREFORMAT,
	DLMC_PREFORMAT
} FTLPreFormatEnum_t;

typedef enum Preformat_Stage {
	PREFORMAT_INIT_STATE = 0,
	PREFORMAT_INIT_SCAN_SYS_AREA,
	PREFORMAT_BUILD_DBT_ERASE,
	PREFORMAT_BUILD_DBT_PROGRAM, //for sandisk and bics4HDR slc program check factory bad
	PREFORMAT_BUILD_DBT_READ,
	PREFORMAT_BUILD_DBT_DONE,
	PREFORMAT_MARK_SYSTEM_AREA,
	PREFORMAT_ERASE_LEVEL_WORKAROUND,
	PREFORMAT_INIT_RUT,

	PREFORMAT_DBT_RESERVE_D1_MARK,
	PREFORMAT_INIT_D3_PBSCORE,
	PREFORMAT_CALCULATE_D3_SCORE,
	PREFORMAT_SORT_D3_SCORE,
	PREFORMAT_BUILD_D3_RUT,
	PREFORMAT_BUILD_D3_RUTSpare_Table,
	PREFORMAT_BUILD_VBRMP,
	PREFORMAT_ERASE_UNIT,
	PREFORMAT_SET_VT_UNIT,
	PREFORMAT_SET_FW_UNIT,
	PREFORMAT_ADD_FREE_D3_UNIT,
	PREFORMAT_ADD_D3_OP_UNIT,
	PREFORMAT_CLEAR_DBT_RESERVE_D1_MARK,

	//Handle D1 Unit
	PREFORMAT_DBT_RESERVE_D3_MARK,
	PREFORMAT_INIT_D1_PBSCORE,
	PREFORMAT_CALCULATE_D1_SCORE,
	PREFORMAT_SORT_D1_SCORE,
	PREFORMAT_BUILD_D1_RUT,
	PREFORMAT_BUILD_D1_RUTSpare_Table,
	PREFORMAT_ADD_FREE_D1_UNIT,
	PREFORMAT_CLEAR_DBT_RESERVE_D3_MARK,
	//Handle D1 Unit Done

	PREFORMAT_SET_GR_UNIT,
	PREFORMAT_PROGRAM_PMD,
	PREFORMAT_PROGRAM_PGD,
	PREFORMAT_PROGRAM_VC,
	PREFORMAT_PROGRAM_TABLE_RS,
	PREFORMAT_PROGRAM_VBRMP,
	PREFORMAT_PROGRAM_EC,
	PREFORMAT_PROGRAM_HOST,
#if VRLC_EN
	PREFORMAT_PROGRAM_VRLC,
#endif /* VRLC_EN */
#if BFEA_EN
	PREFORMAT_PROGRAM_BFEA,
#endif
	PREFORMAT_PROGRAM_TCG,
	PREFORMAT_PROGRAM_INIT_INFO_RS,
	PREFORMAT_PROGRAM_VT,
	PREFORMAT_PROGRAM_SYSTEM_AREA,
	PREFORMAT_ERROR_STATE,
	PREFORMAT_DONE
} Preformat_Stage_t;

//******************************************
//	Structure
//******************************************
typedef union {
	U32 ulAll;
	struct {
		U32 ulVB_NUM: 12;
		///-----------------------
		U32 ulPB_NUM: 12;
		///-----------------------
		U32 ulgCE: 4;
		U32 ulPlane: 2;
		U32 ulLUN: 2;
	} elements;
} RUT_TABLE_2ND_LAYER;

typedef struct {
	/// suppose each 1st_layer element is 4 byte long.

	U32 ulStartAddr;
	U32 ul1ST_layer_Num;

} RUT_TABLE_1ST_LAYER;

typedef enum PreformatMarkSystemArea {
	PREFORMAT_MARK_SYSTEMAREA_INIT = 0,
	PREFORMAT_SYSTEMAREA_MARK_RANGE,
	PREFORMAT_SYSTEMAREA_SELECT_BLK,
	PREFORMAT_MARK_SYSTEMAREA_DONE
} PreformatMarkSystemArea_t;

typedef enum ProgramSystemArea {
	PREFORMAT_PROGRAM_SYSTEMAREA_INIT = 0,
	PREFORMAT_PROGRAM_DBT_BLK,
	PREFORMAT_PROGRAM_BANKING_BLK_CH0,
	PREFORMAT_PROGRAM_BANKING_BLK_CH1,
	PREFORMAT_PROGRAM_BANKING_BLK_CH2,
	PREFORMAT_PROGRAM_BANKING_BLK_CH3,
	PREFORMAT_PROGRAM_SYSTEM_BLK,
	PREFORMAT_PROGRAM_SYSTEMAREA_DONE
} PreformatProgramSystemArea_t;

typedef struct {
	U8 ubDoing;
	U8 ubPreformatReadVerify;
	U8 ubPreformatReadVerifyResult;
	U8 ubOldDBTNum;
	U8 ubValidPsid;
	U8 ubOldVTMomBlkIdx;
	U8 ubNewVTMomBlkIdx;
	U8 ubPreformatErrorCode;
	FencingConfig_t FencingConfig;
	U16 uwAvailableUnitNumber;
	U16 uwDoingUnit;
	U16 uwScoreStartUnit;
	U16 uwInfoBlkOP;
	U16 uwCEMinAverageScore; //MixPlane
	U32 ulPreviousEC;
	U32 ulPreviousD1EC;
	SystemAreaBlock_t DBTBlock[SYSTEM_AREA_DBT_BLK_NUM];
	SystemAreaBlock_t uwMarkSystemAreaCheckBlk;
	Preformat_Stage_t ubState;
	U8 ubSystemAreaBlkSelectNum;
	PreformatMarkSystemArea_t ubMarkSystemAreaState;
	PreformatProgramSystemArea_t ubProgramSystemAreaState;
} PreformaInfo_t;

typedef struct {
	TTTemperatureCelsius_t PreformatFlashTemperatrue;
	U16 uwFlashTemperatureTagID;
} PreformatTemperature_t;

#if VRLC_EN
typedef struct MMOSystemFlag_t {
	U8	btFromRDT : 1;
	U8	btLoadVRLCFail : 1;
	U8	btInherit	: 1;
	U8	btQLCProgramDone	: 1;
#if DEBUG_VRLC_READ_MMO_UNIT_EN
	U8	btReadVerifyDone : 1;
	U8	btReserved : 3;
#else /* DEBUG_VRLC_READ_MMO_UNIT_EN */
	U8	btReserved : 4;
#endif /* DEBUG_VRLC_READ_MMO_UNIT_EN */
} MMOSystemFlag_t;

typedef struct MMOSystemFailFlag_t {
	U8	btRDTInherit : 1;
	U8	btCheckVRLCVersion : 1;
	U8	btFWInherit : 1;
	U8	btTwoSideUECC	: 1;
	U8	btE3DNotMatch	: 1;
	U8	btProgramFail : 1;
	U8	btReserved : 2;
} MMOSystemFailFlag_t;

typedef struct {
	U8 ubState;
	union {
		U8  ubAll;
		MMOSystemFlag_t ubMMOSystemFlag;
	} MMOSystemFlag;
	union {
		U8  ubAll;
		MMOSystemFailFlag_t ubMMOSystemFailFlag;
	} MMOSystemFailFlag;

	union {
		U8 ubAll;
		struct { // this struct only used in FTLPreFormatStartMMO
			U8 UnitIdx       : 6;
			U8 Iteration     : 2;
		};
	} ubDoingUnitIdx;
	U8 ubDoneCECnt;
	U16 uwDoingTrimIdx;
	U16 uwDoneTrimCnt;
	U32 ulOldCheckE3D[VRLC_TABLE_SIZE_4K_NUM];
	U32 ulNewCheckE3D[VRLC_TABLE_SIZE_4K_NUM];
	Unit_t uwChooseUnit[MMO_TRIM_USE_UNIT_CNT];
	U8 ubUnitIterationBMP[MMO_TRIM_USE_UNIT_CNT];
	U64 uoProgramDoneTime;
} MMOTuningInfo_t;
extern MMOTuningInfo_t gMMOInfo;
#endif /* VRLC_EN */

extern volatile U8 gubLeavePreformatFlag;
extern volatile U8 gubLeavePreformatFlag_Overlay;
#if (U17_EN)
extern volatile U8 gubUSBCheckHighSpeed;
#endif /* (U17_EN) */
extern volatile PreformaInfo_t gPreformat;
AOM_VUC U32 FTLPreFormatProgramSystemArea(SystemBlock_t *pSystemBlockTable, DBTBlockHeader_t *pDBTBlockHeader, U32 *pRUTL1, U32 *pRUTL2, RUTSpareTable_t *pRUTSpareTable);
AOM_VUC void FTLPreFormatMarkSystemArea(SystemBlock_t *pSystemBlockTable);
AOM_BURNER void FTLPreformatSetVBRMP(PB_BLK_SCORE_STRUCT *pulScorePtr);
#if (PS5017_EN)
#if (MICRON_FSP_EN)
AOM_VUC U8 FTLPreFormatAssignBlock(U8 ubNowCE);
#else /* (MICRON_FSP_EN) */
AOM_BURNER U8 FTLPreFormatAssignBlock(U8 ubNowCE);
#endif /* (MICRON_FSP_EN) */
#else /* (PS5017_EN) */
AOM_VUC U8 FTLPreFormatAssignBlock(U8 ubNowCE);
#endif /* (PS5017_EN) */
AOM_BURNER void FTLPreformatInitRUTVariable(U16 *puwRUTLayer2Number);
AOM_BURNER void FTLPreformatReserveSystemAreaBlk(SystemBlock_t *pSystemBlkTable);
AOM_BURNER void FTLPreformatReserveBlk(U8 ubReserveUnitType, SystemBlock_t *pSystemBlkTable);
AOM_BURNER void FTLPreformatClearDBTReserveMark(void);
AOM_BURNER void FTLPreFormatInitPBScore(PB_SCORE_STRUCT *ulPBScorePtr);
AOM_BURNER void FTLPreFormatInitScoreBase(void);
AOM_BURNER void FTLScoreEachUnit(U16 uwStartPhysicalUnit, DBT_STRUCT *pubDBTPtr, PB_BLK_SCORE_STRUCT *pubScorePtr, BUILD_RUT_ELEMENT *pubBuildRUTElementPtr, PB_SCORE_STRUCT *pulPBScorePtr);
#if (!PS5017_EN)
AOM_VUC void FTLPreFormat(void); //S17 move to FW_init.h
#endif /* (!PS5017_EN) */
#if ((!LPM3_LOADER) && ((BURNER_MODE_EN) || (HOST_MODE == SATA || (USB == HOST_MODE))))
AOM_VUC void VUC_PreformatPrepare(VUC_OPT_HCMD_PTR_t pCmd);
#endif /* ((!LPM3_LOADER) && ((BURNER_MODE_EN) || (HOST_MODE == SATA || (USB == HOST_MODE)))) */
AOM_BURNER void VUC_FTLPreFormat(FTLPreFormatEnum_t PreformatMode);
AOM_BURNER void FTLPreFormatReadPsidDigest(void);
AOM_BURNER void FTLPreformatProgramTableRS(void);
AOM_BURNER void FTLPreFormatProgramInitInfoRS(void);
#if (RECORD_FLASH_TEMPERATURE_EN)
AOM_MEDIA_SCAN_TEMPERATURE void MediaScanSetPreformatBlkTemperature(TTTemperatureCelsius_t TTFlashTemperatrue);
AOM_MEDIA_SCAN_TEMPERATURE void MediaScanPreformatTriggerFlashTemperature(PreformatTemperature_t *PreformatTemperature);
AOM_MEDIA_SCAN_TEMPERATURE U8 MediaScanPreformatGetFlashTemperature(PreformatTemperature_t *pubTemp);
#endif /* (RECORD_FLASH_TEMPERATURE_EN) */

void PreformatSetWindow(PAGE_1_SDLL_NAND_Config_t *pSDLLSetting);
#if (PS5013_EN)
void GetPreformatInfo(void);
#endif


#if VRLC_EN
void FTLPreFormatMMOVRLC_Callback(TIEOUT_FORMAT_t uoResult);
#endif /* VRLC_EN */

#endif /* _FW_PREFORMAT_H_ */
