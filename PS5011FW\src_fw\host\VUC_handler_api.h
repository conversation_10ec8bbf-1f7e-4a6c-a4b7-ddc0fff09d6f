#ifndef _VUC_HANDLER_API_H_
#define _VUC_HANDLER_API_H_

#include "host/VUC_handler.h"
#include "vuc/VUC_Protect_api.h"

#define VUC_HANDLE_SIZE_UNIT_SATA (BC_4KB * INIT_LL_NRW_LB)

typedef union {
	U8 ubAll;
	struct {
		U8 btVUCSecurity      : 1; // 1 represent current security send is the first VUC Cmd, and must skip LPM until the third VUC Cmd is done
		U8 btVUCProtect       : 1; //
		U8 btVUCDoPCIEEyeFlow : 1; // 1 represent receive VUC_DO_PCIE_EYE_FLOW cmd, and must skip LPM until receive and finish VUC_GET_PCIE_EYE_INFO cmd
		U8 reserved           : 5;
	};
}
VUCSkipLPMBMP_t;

typedef struct {
	U8 ubApKeyValid         : 1;   // 1 accept vuc command
	U8 ubApKeyValidAdvantech: 1;   // If support this feature, 1: allow ubApKeyValid to be TRUE.
	U8 ubrsv                : 6;
	U8 ubHostTrigCQNeed;           // 1 current vendor is trigger from Host (SATA/NVMe/SCSI) need send CQ back
	U8 ubVucCmdErrStatus;          // non-zero some error occur 0: no-error occur only for show uart
	U8 ubVucNonData;               // 1 current vuc is non-data case
	U8 ubCmdInType;                // record the command source CMD_IN_UART CMD_IN_NVME CMD_IN_SECURITY CMD_IN_SATA
	U8 ubSecspKeyValid;            // IF Send SQ_64B Success , Set This Bit as '1'    and   Vuc_in Done , Must Set Bit as '0'
	U8 ubVUCDispatch;              // 1 represent current security send/receive is the Second VUC Command of VUC Cycle.
	U8 ubVucAPKeyShutdown : 1;
	U8 reserved           : 7;
	VUCProtect_t VUCProtect;
	VUCSkipLPMBMP_t VUCSkipLPMBMP; // BMP to skip FW enter LPM
}
VendorCmdVariable_t;

#if (VUC_MICRON_NICKS_EN)
typedef struct {
	U8 ubInputHeaderFormatVersion;
	U8 ubInputDataFormatVersion;
	U16 uwCmdClass;
	U16 uwCmdCode;
	U8 Reserved;
	U8 ubInputDataFormatType;
	U32 ulDataPayloadSize;
} VUCMicronInputHeader_t;

extern U8 gubMicronLastCmdCode;
extern U8 gubMicronLastCmdClass;
extern U8 gubMicronVUCCMDNotDone;
#endif /*(VUC_MICRON_NICKS_EN)*/


//==============================================================================
// Function API
//==============================================================================
AOM_BURNER void BurnerMicronVUCDispatch(VUC_OPT_HCMD_PTR_t pCmd);
#if (((BURNER_MODE_EN || RDT_MODE_EN) && (HOST_MODE == NVME)) || (USB == HOST_MODE))
AOM_BURNER void VUCDispatch(VUC_OPT_HCMD_PTR_t pCmd, U8 ubCmdInPath);
void VUCCQ(VUC_OPT_HCMD_PTR_t pCmd);
AOM_BURNER void VUCSpSecurityProtocolInfo(VUC_OPT_HCMD_PTR_t pCmd);
#endif /* (((BURNER_MODE_EN || RDT_MODE_EN) && (HOST_MODE == NVME)) || (USB == HOST_MODE)) */

#if ((USB == HOST_MODE) || (HOST_MODE == SATA) || BURNER_MODE_EN || RDT_MODE_EN)
AOM_VUC void VUCSecurityProtocolSendVendor(VUC_OPT_HCMD_PTR_t pCmd, U8 ubPathCmdIn);
AOM_VUC void VUCSecurityProtocolReceiveVendor(VUC_OPT_HCMD_PTR_t pCmd, U8 ubPathCmdIn);
AOM_VUC void VUCSecurityProtocolErrorHandler(VUC_OPT_HCMD_PTR_t pCmd, U8 ubErrorCode);
#endif /* ((USB == HOST_MODE) || (HOST_MODE == SATA) || BURNER_MODE_EN || RDT_MODE_EN) */

#if (VUC_MICRON_NICKS_EN)
AOM_VUC_3 void VUCMicronParsingInputHeader(U32 ulAddr, U8 *ubInputHeaderFormatVersion, U8 *ubInputDataFormatVersion, U16 *uwCmdClass, U16 *uwCmdCode);
#endif /*(VUC_MICRON_NICKS_EN)*/

#if (HOST_MODE == SATA)
AOM_VUC void VUCDispatchSATA(VUC_OPT_HCMD_PTR_t pCmd, U8 ubCmdInPath);
#endif /*(HOST_MODE == SATA)*/

extern VendorCmdVariable_t gVUCVar;

#endif /* _VUC_HANDLER_API_H_ */
