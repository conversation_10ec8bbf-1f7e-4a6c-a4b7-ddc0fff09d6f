#ifndef _VUC_MICRONGETERASECOUNT_H_
#define _VUC_MICRONGETERASECOUNT_H_
#include "aom/aom_api.h"

#define VUC_MICRON_GET_ERASE_COUNT_CLASS	(0x0004)
#define VUC_MICRON_GET_ERASE_COUNT_CODE		(0x0010)

typedef struct {
	U8 ubResponseHeaderFormatVersion;
	U8 ubResponseDataFotmatVersion;
	U16 uwCMDClass;
	U16 uwCMDCode;
	U16 uwStatus;
	U32 ulDataPayloadSize;
} GetEraseCountResponseHEADER_t;

AOM_VUC_3 void VUCMicronGetEraseCount(U32 ulPayloadAddr);

#endif /* _VUC_MICRONGETERASECOUNT_H_ */
