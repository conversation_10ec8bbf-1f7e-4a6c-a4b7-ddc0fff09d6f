#ifndef _MR_API_H_
#define _MR_API_H_
#include "setup.h"
#include "common/symbol.h"
#include "hal/mr/mr_reg.h"
#include "aom/aom_api.h"

#if (PS5017_EN || PS5021_EN)
//  Mail Router Slave Index
#define MR_S_DMAC_CQ_HIGH               (0)
#define MR_S_DMAC_CQ_NORMAL             (1)
#define MR_S_BMU_CQ0                    (2)
#define MR_S_BMU_WRITE_CQ0              (3)
#define MR_S_APU_TD_CQ                  (4)
#define MR_S_APU_CMD_CQ                 (5)
#define MR_S_APU_READ_CQ                (6)
#define MR_S_XZIP_CQ                    (7)
#define MR_S_COP0_CQ                    (8)
#define MR_S_COP1_CQ                    (9)
#define MR_S_FLHMSG_CQ                  (10)
#define MR_S_RS_CQ                      (11)
#define MR_S_PIC_CQ                     (12)
#define MR_S_LOG_MSG                    (13)
#define MR_S_ERR_MSG                    (14)
#define MR_S_SW_CQ0                     (15)
#define MR_S_SW_CQ1                     (16)
#define MR_S_SW_CQ2                     (17)
#define MR_S_SW_CQ3                     (18)
#define MR_S_WRITE_CQ1                  (19)
#define MR_S_BMU_CQ1                    (20)
#define MR_S_APU_CMD_WCH                (21)
#define MR_S_APU_CMD_CPL                (22)
#define MR_S_APU_RSP_COP1               (23)
#define MR_S_APU_RSP_BMU                (24)
#define MR_S_COP1_CMD                   (25)
#define MR_S_COP1_RSP_COP0              (26)
#define MR_S_COP1_RSP_XZIP              (27)
#define MR_S_COP1_RSP_BMU               (28)
#define MR_S_COP1_RSP_DMAC              (29)
#define MR_S_COP0_CMD_CPUR              (30)
#define MR_S_COP0_CMD_CPUW              (31)
#define MR_S_COP0_CMD_COP0              (32)
#define MR_S_COP0_RSP_BMU               (33)
#define MR_S_COP0_RSP_NERGE             (34)
#define MR_S_MERGE_RSP_DMAC             (35)
#define MR_S_MERGE_RSP_BMU              (36)
#define MR_S_FIP_RSP_BMU                (37)
#define MR_S_FIP_RSP_BMU_2              (38)
#define MR_S_RS_RSP_BMU                 (39)
#define MR_S_DMAC_CMD_HIGH              (40)
#define MR_S_DMAC_CMD_NORMAL            (41)
#define MR_S_DMAC_RSP_BMU               (42)
#define MR_S_DMAC_RSP_AES               (43)
#define MR_S_BMU_CMD_OTHERS             (44)
#define MR_S_BMU_CMD_CPU                (45)
#define MR_S_BMU_CMD_APU                (46)
#define MR_S_BMU_CMD_CPU_BLOCKING       (47)
#define MR_S_BMU_CMD_FIP                (48)
#define MR_S_BMU_CMD_FIP_BLOCKING       (49)
#define MR_S_BMU_CMD_MERGE_BLOCKING     (50)
#define MR_S_BMU_CMD_RS_BLOCKING        (51)
#define MR_S_BMU_CMD_COP1_BLOCKING      (52)
#define MR_S_BMU_CMD_WLB_SRH            (53)
#define MR_S_XZIP_CMD_COP1              (54)
#define MR_S_XZIP_CMD_CPU               (55)
#define MR_S_XZIP_RSP_DMAC              (56)
#define MR_S_BMU_CMD_APUR_BLOCKING      (57)
#define MR_S_AXI_APU_CMD_LOG            (58)
#define MR_S_APU_RSP_AES                (59)
#define MR_S_AES_CMD_KEY                (60)
#define MR_S_SQ_IDX_MAX                 (61)

//  Mail Router Target ID
#define MR_TID_DMAC_CQ_HIGH                 (0x00)
#define MR_TID_DMAC_CQ_NORMAL               (0x01)
#define MR_TID_BMU_CQ0                      (0x02)
#define MR_TID_BMU_WRITE_CQ0                (0x03)
#define MR_TID_APU_TD_CQ                    (0x04)
#define MR_TID_APU_CMD_CQ                   (0x05)
#define MR_TID_APU_READ_CQ                  (0x06)
#define MR_TID_XZIP_CQ                      (0x07)
#define MR_TID_COP0_CQ                      (0x08)
#define MR_TID_COP1_CQ                      (0x09)
#define MR_TID_FLHMSG_CQ                    (0x0A)
#define MR_TID_RS_CQ                        (0x0B)
#define MR_TID_PIC_CQ                       (0x0C)
#define MR_TID_LOG_MSG                      (0x0D)
#define MR_TID_ERR_MSG                      (0x0E)
#define MR_TID_SW_CQ0                       (0x10)
#define MR_TID_SW_CQ1                       (0x11)
#define MR_TID_SW_CQ2                       (0x12)
#define MR_TID_SW_CQ3                       (0x13)
#define MR_TID_WRITE_CQ1                    (0x14)
#define MR_TID_BMU_CQ1                      (0x15)
#define MR_TID_APU_CMD_WCH                  (0x20)
#define MR_TID_APU_CMD_CPL                  (0x21)
#define MR_TID_APU_RSP_COP1                 (0x22)
#define MR_TID_APU_RSP_BMU                  (0x23)
#define MR_TID_COP1_CMD                     (0x30)
#define MR_TID_COP1_RSP_COP0                (0x31)
#define MR_TID_COP1_RSP_XZIP                (0x32)
#define MR_TID_COP1_RSP_BMU                 (0x33)
#define MR_TID_COP1_RSP_DMAC                (0x34)
#define MR_TID_COP0_CMD_CPUR                (0x40)
#define MR_TID_COP0_CMD_CPUW                (0x41)
#define MR_TID_COP0_CMD_COP0                (0x42)
#define MR_TID_COP0_RSP_BMU                 (0x43)
#define MR_TID_COP0_RSP_NERGE               (0x44)
#define MR_TID_MERGE_RSP_DMAC               (0x50)
#define MR_TID_MERGE_RSP_BMU                (0x51)
#define MR_TID_FIP_RSP_BMU                  (0x60)
#define MR_TID_FIP_RSP_BMU_2                (0x61)
#define MR_TID_RS_RSP_BMU                   (0x62)
#define MR_TID_DMAC_CMD_HIGH                (0x70)
#define MR_TID_DMAC_CMD_NORMAL              (0x71)
#define MR_TID_DMAC_RSP_BMU                 (0x72)
#define MR_TID_DMAC_RSP_AES                 (0x73)
#define MR_TID_BMU_CMD_OTHERS               (0x80)
#define MR_TID_BMU_CMD_CPU                  (0x81)
#define MR_TID_BMU_CMD_APU                  (0x82)
#define MR_TID_BMU_CMD_CPU_BLOCKING         (0x83)
#define MR_TID_BMU_CMD_FIP                  (0x84)
#define MR_TID_BMU_CMD_FIP_BLOCKING         (0x85)
#define MR_TID_BMU_CMD_MERGE_BLOCKING       (0x86)
#define MR_TID_BMU_CMD_RS_BLOCKING          (0x87)
#define MR_TID_BMU_CMD_COP1_BLOCKING        (0x88)
#define MR_TID_BMU_CMD_WLB_SRH              (0x89)
#define MR_TID_XZIP_CMD_COP1                (0x8A)
#define MR_TID_XZIP_CMD_CPU                 (0x8B)
#define MR_TID_XZIP_RSP_DMAC                (0x8C)
#define MR_TID_BMU_CMD_APUR_BLOCKING        (0x8D)
#define MR_TID_AXI_APU_CMD_LOG              (0x90)
#define MR_TID_APU_RSP_AES                  (0xE0)
#define MR_TID_AES_CMD_KEY                  (0xF0)

//  Mail Router Master Index
#define MR_M_DMAC_SQ_HIGH               (0)
#define MR_M_DAMC_SQ_NORMAL             (1)
#define MR_M_BMU_SQ                     (2)
#define MR_M_BMU_SQ_BLOCKING            (3)
#define MR_M_APU_SQ_WCH                 (4)
#define MR_M_APU_SQ_CPL                 (5)
#define MR_M_XZIP_SQ                    (6)
#define MR_M_COP0_SQ_CPUR               (7)
#define MR_M_COP0_SQ_CPUW               (8)
#define MR_M_COP1_SQ                    (9)
#define MR_M_SW_SQ0                     (10)
#define MR_M_SW_SQ1                     (11)
#define MR_M_SW_SQ2                     (12)
#define MR_M_SW_SQ3                     (13)
#define MR_M_BMU_SQ_SH                  (14)
#define MR_M_COP0_SQ_W1                 (15)
#define MR_M_APU                        (16)
#define MR_M_APU_RCQ                    (17)
#define MR_M_APU_KEY                    (18)
#define MR_M_DZIP                       (19)
#define MR_M_AES                        (20)
#define MR_M_COP1                       (21)
#define MR_M_MERG_COP0_FIP_RS           (22)
#define MR_M_COP0                       (23)
#define MR_M_FMSG                       (24)
#define MR_M_DMAC                       (25)
#define MR_M_BMU_XZIP_E3D512            (26)
#define MR_M_BMU                        (27)
#define MR_M_DBUF                       (28)
#define MR_M_D2H                        (29)
#define MR_M_PIC                        (30)
#endif /* (PS5017_EN || PS5021_EN) */

#define	MR_COP1_CMD_SLAVE_QUEUE_ID					(25)	//cop1 SQ
#define MR_COP0_RESPOND_TO_COP1_SLAVE_QUEUE_ID		(26)	//cop0 rsp to cop1
#define MR_XZIP_RESPOND_TO_COP1_SLAVE_QUEUE_ID		(27)	//xzip rsp to cop1
#define MR_BMU_RESPOND_TO_COP1_SLAVE_QUEUE_ID		(28)	//bmu rsp to cop1
#define MR_DMAC_RESPOND_TO_COP1_SLAVE_QUEUE_ID		(29)	//dmac rsp to cop1
#define MR_COP0_CMD_READ_SLAVE_QUEUE_ID					(30)
#define MR_COP0_CMD_WRITE_SLAVE_QUEUE_ID					(31)
#define MR_COP1_RESPOND_TO_COP0_SLAVE_QUEUE_ID		(32)
#define MR_BMU_RESPOND_TO_COP0_SLAVE_QUEUE_ID		(33)
#define MR_COP0_CMD_WRITE_2_SLAVE_QUEUE_ID		(34)
#define MR_DMAC_RESPOND_TO_MERG_SLAVE_QUEUE_ID		(35)
#define MR_BMU_RESPOND_TO_MERG_SLAVE_QUEUE_ID		(36)
#define MR_BMU_NON_BLOCK_RESPOND_TO_FIP_SLAVE_QUEUE_ID		(37)
#define MR_BMU_BLOCK_RESPOND_TO_FIP_SLAVE_QUEUE_ID		(38)
#define MR_BMU_RESPOND_TO_RS_SLAVE_QUEUE_ID		(39)
#define MR_DMAC_HIGH_PRIORITY_SLAVE_QUEUE_ID		(40)
#define MR_DMAC_NORMAL_PRIORITY_SLAVE_QUEUE_ID		(41)
#define MR_BMU_RESPOND_TO_DMAC_SLAVE_QUEUE_ID		(42)
#define MR_AES_RESPOND_TO_DMAC_SLAVE_QUEUE_ID		(43)

#define MR_DMAC_HIGH_PRIORITY_SQ_DECODER_NUM							(0>>1)
#define MR_DMAC_NORMAL_PRIORITY_SQ_DECODER_NUM							(1>>1)
#define MR_COP0_READ_SQ_DECODER_NUM							(7>>1)
#define MR_COP0_WRITE_SQ_DECODER_NUM							(8>>1)
#define MR_COP1_SQ_DECODER_NUM							(9>>1)
#define MR_COP0_WRITE_1_SQ_DECODER_NUM							(15>>1)
#define MR_APU_SEND_TO_COP1_DECODER_NUM							(16>>1)
#define MR_AES_SEND_TO_OTHERS_DECODER_NUM							(20>>1)
#define MR_COP1_SEND_TO_OTHERS_DECODER_NUM							(21>>1)
#define MR_COP0_SEND_TO_COP1_DECODER_NUM							(22>>1)
#define MR_DMAC_SEND_TO_COP1_DECODER_NUM							(25>>1)
#define MR_BMU_SEND_TO_COP1_DECODER_NUM							(26>>1)

#if PS5017_EN
#define	MR_DECODER_ODD_STATUS_IDLE						(0x80)
#define	MR_DECODER_EVEN_STATUS_IDLE						(0x8)
#else /*PS5017_EN*/
#define	MR_DECODER_ODD_STATUS_IDLE						(0x40)
#define	MR_DECODER_EVEN_STATUS_IDLE						(0x4)
#endif /*PS5017_EN*/
#define	MR_DECODER_ODD_IDLE_BIT_MASK				(0xF0)
#define	MR_DECODER_EVEN_IDLE_BIT_MASK				(0xF)

#define MR_DECODER_STOP_COP1_SQ_BIT_NUM							(BIT9)
#define MR_DECODER_STOP_APU_SEND_TO_COP1_BIT_NUM							(BIT16)
#define MR_DECODER_STOP_COP0_SEND_TO_COP1_BIT_NUM							(BIT22)
#define MR_DECODER_STOP_DMAC_SEND_TO_COP1_BIT_NUM							(BIT25)
#define MR_DECODER_STOP_BMU_SEND_TO_COP1_BIT_NUM							(BIT26)

#define MR_LOG_MESSAGE_SLAVE_QUEUE_ID               (13)
#define MR_COP0_READ_SLAVE_QUEUE_ID					(30)	//cop0 normal SQ
#define MR_COP0_WRITE0_SLAVE_QUEUE_ID				(31)	//cop0 merge0 SQ
#define MR_COP0_WRITE1_SLAVE_QUEUE_ID				(34)	//cop0 merge1 SQ
#define MR_COP1_SQ_SLAVE_QUEUE_ID				(25)	//cop1 SQ
#define MR_SLAVE_QUEUE_INFO_SIZE					(8)
#define MR_LINK_SIZE 								(8)
#define MR_LINK_CNT_PER_NODE 						(8)
#define MR_LINK_READ_SLAVE_QUEUE_UPPER_LIMIT_CNT	(60)
#define MR_LINK_WRITE_SLAVE_QUEUE_UPPER_LIMIT_CNT	(60)
#define MR_LINK_WRITE1_SLAVE_QUEUE_UPPER_LIMIT_CNT	(60)
#define MR_LINK_COP1_SQ_SLAVE_QUEUE_UPPER_LIMIT_CNT	(24)
#define MR_LINK_LOG_MESSAGE_QUEUE_UPPER_LIMIT_CNT   (1)
#define MR_TOTAL_FREE_LINK_CNT 						(256)
#define M_MR_SET_INTERRRUPT_FULL_MASK_EXCEPT_LOG_MESSAGE()	(R64_MR[R64_MR_ITC_FUL_MSK] = 0xFFFFFFFFFFFFBFFF)

#define MR_BMU_WORKAROUND_FAKE_LOG_MESSAGE_NUM      (8)
#define M_MR_CHECK_LOG_MESSAGE_INTERRUPT_FULL_FLAG()  (R32_MR[R32_MR_ITC_FUL_FLAG_L] & ITC_FULL_FLAG_LOG_MESSAGE)
#define M_MR_CEHCK_FULL_INT_ERR()				((R32_MR[R32_MR_ITC_ERR_INFO_L]&FULL_INT_ERR)?TRUE:FALSE)

//X = 0:1BYTE 1:2BYTE 2:4BYTE 3:8BYTE 4:16BYTE    5:32BYTE
#define MR_FWSQ0_DATA_SIZE(x)          (R8_MR[R8_MR_MRIW_DAT_SIZE + (10 >> 1)] = ((((x) & 0xF) << 0) | (R8_MR[R8_MR_MRIW_DAT_SIZE + (10>> 1)] & 0xF0)))
#define MR_FWSQ1_DATA_SIZE(x)          (R8_MR[R8_MR_MRIW_DAT_SIZE + (11 >> 1)] = ((((x) & 0xF) << 4) | (R8_MR[R8_MR_MRIW_DAT_SIZE + (11 >> 1)] & 0x0F)))
#define MR_FWSQ2_DATA_SIZE(x)          (R8_MR[R8_MR_MRIW_DAT_SIZE + (12 >> 1)] = ((((x) & 0xF) << 0) | (R8_MR[R8_MR_MRIW_DAT_SIZE + (12 >> 1)] & 0xF0)))
#define MR_FWSQ3_DATA_SIZE(x)          (R8_MR[R8_MR_MRIW_DAT_SIZE + (13 >> 1)] = ((((x) & 0xF) << 4) | (R8_MR[R8_MR_MRIW_DAT_SIZE + (13 >> 1)] & 0x0F)))
#if PS5021_EN
#define MR_IR10_DATA_SIZE(x)          (R8_MR[R8_MR_MRIR_DAT_SIZE + (10 >> 1)] = ((((x) & 0xF) << 0) | (R8_MR[R8_MR_MRIR_DAT_SIZE + (10 >> 1)] & 0xF0)))
#endif /* PS5021_EN */

#define MR_FWCQ0_DATA_SIZE(x)          (R8_MR[R8_MR_MRIR_DAT_SIZE + (15 >> 1)] = ((((x) & 0xF) << 4) | (R8_MR[R8_MR_MRIR_DAT_SIZE + (15 >> 1)] & 0x0F)))
#define MR_FWCQ1_DATA_SIZE(x)          (R8_MR[R8_MR_MRIR_DAT_SIZE + (16 >> 1)] = ((((x) & 0xF) << 0) | (R8_MR[R8_MR_MRIR_DAT_SIZE + (16 >> 1)] & 0xF0)))
#define MR_FWCQ2_DATA_SIZE(x)          (R8_MR[R8_MR_MRIR_DAT_SIZE + (17 >> 1)] = ((((x) & 0xF) << 4) | (R8_MR[R8_MR_MRIR_DAT_SIZE + (17 >> 1)] & 0x0F)))
#define MR_FWCQ3_DATA_SIZE(x)          (R8_MR[R8_MR_MRIR_DAT_SIZE + (18 >> 1)] = ((((x) & 0xF) << 0) | (R8_MR[R8_MR_MRIR_DAT_SIZE + (18 >> 1)] & 0xF0)))
#define M_MR_GET_SQR_EMT(MRID)         (R32_MR_SQ[(MRID)][R32_MR_SQ_N_INFO_H] & SQR_EMT_BIT) //E17, E19 code base not fix SPEC
#define	MR_SET_FWQ_TARG_ID(id)			(R32_MR[R32_MR_OTHERS_CFG_L] |= ((id) & DB_SQ10_TID_MASK))
#define M_MR_GET_IDLE()					(R32_MR[R32_MR_EMPTY_STATUS] & ALL_EMPTY_BIT)
#if (PS5017_EN || PS5021_EN)
#define M_MR_CHK_RCQ_PATH_EMPTY() 		( ((R8_MR[R8_MR_DEC_N_INFO + (MR_M_APU_RCQ >> 1)] & 0xF0) == (1 << DEC_N_IDLE_4_SHIFT)) && ((R32_MR[R32_MR_SQ_N_INFO_H + (MR_S_APU_READ_CQ << 1)]  & (1 << SQR_EMT_SHIFT)) == (1 << SQR_EMT_SHIFT)) && ((R8_MR[R8_MR_MRIR_N_INFO + (MR_S_APU_READ_CQ >> 1)] & 0xF) == (1 << MRIR_N_IDLE_0_SHIFT)) )
// DEC 17, SQ_INFO_H 6, MRIR 6
#define M_MR_CHK_NRW_PATH_EMPTY()  		( ((R8_MR[R8_MR_DEC_N_INFO + (MR_M_APU >> 1)] & 0xF) == (1 << DEC_N_IDLE_0_SHIFT)) && ((R32_MR[R32_MR_SQ_N_INFO_H + (MR_S_APU_CMD_CQ << 1)]  & (1 << SQR_EMT_SHIFT)) == (1 << SQR_EMT_SHIFT)) && ((R8_MR[R8_MR_MRIR_N_INFO + (MR_S_APU_CMD_CQ >> 1)] & 0xF0) == (1 << MRIR_N_IDLE_4_SHIFT)) )
// DEC 16, SQ_INFO_H 5, MRIR 5
#define M_MR_CHK_TD_PATH_EMPTY() 		( ((R8_MR[R8_MR_DEC_N_INFO + (MR_M_APU >> 1)] & 0xF) == (1 << DEC_N_IDLE_0_SHIFT)) && ((R32_MR[R32_MR_SQ_N_INFO_H + (MR_S_APU_TD_CQ << 1)]  & (1 << SQR_EMT_SHIFT)) == (1 << SQR_EMT_SHIFT)) && ((R8_MR[R8_MR_MRIR_N_INFO + (MR_S_APU_TD_CQ >> 1)] & 0xF) == (1 << MRIR_N_IDLE_0_SHIFT)) )
// DEC 16, SQ_INFO_H 4, MRIR 4
#define M_MR_CHK_COP0RSQ_PATH_EMPTY()   ( ((R8_MR[R8_MR_DEC_N_INFO + (MR_M_COP0_SQ_CPUR >> 1)] & 0xF0) == (1 << DEC_N_IDLE_4_SHIFT)) && ((R32_MR[R32_MR_SQ_N_INFO_H + (MR_S_COP0_CMD_CPUR << 1)]  & (1 << SQR_EMT_SHIFT)) == (1 << SQR_EMT_SHIFT)) && ((R8_MR[R8_MR_MRIW_N_INFO + (MR_M_COP0_SQ_CPUR >> 1) ] & 0xF0) == (1 << MRIR_N_IDLE_4_SHIFT)) )
// DEC 7, SQ_INFO_H 30, MRIW 7
#else /*PS5017_EN*/
#define M_MR_CHK_RCQ_PATH_EMPTY() 		( ((R8_MR[R8_MR_DEC_N_INFO + (17 >> 1)] & 0xF0) == 0x40) && ((R32_MR[R32_MR_SQ_N_INFO_H + (6 << 1)]  & 0x20000000) == 0x20000000) && ((R8_MR[R8_MR_MRIR_N_INFO + (6 >> 1)] & 0xF) == 0x8) )
// DEC 17, SQ_INFO_H 6, MRIR 6
#define M_MR_CHK_NRW_PATH_EMPTY()  		( ((R8_MR[R8_MR_DEC_N_INFO + (16 >> 1)] & 0xF) == 0x4) && ((R32_MR[R32_MR_SQ_N_INFO_H + (5 << 1)]  & 0x20000000) == 0x20000000) && ((R8_MR[R8_MR_MRIR_N_INFO + (5 >> 1)] & 0xF0) == 0x80) )
// DEC 16, SQ_INFO_H 5, MRIR 5
#define M_MR_CHK_TD_PATH_EMPTY() 		( ((R8_MR[R8_MR_DEC_N_INFO + (16 >> 1)] & 0xF) == 0x4) && ((R32_MR[R32_MR_SQ_N_INFO_H + (4 << 1)]  & 0x20000000) == 0x20000000) && ((R8_MR[R8_MR_MRIR_N_INFO + (4 >> 1)] & 0xF) == 0x8) )
// DEC 16, SQ_INFO_H 4, MRIR 4
#define M_MR_CHK_COP0RSQ_PATH_EMPTY()   ( ((R8_MR[R8_MR_DEC_N_INFO + (7 >> 1)] & 0xF0) == 0x40) && ((R32_MR[R32_MR_SQ_N_INFO_H + (30 << 1)]  & 0x20000000) == 0x20000000) && ((R8_MR[R8_MR_MRIW_N_INFO + (7 >> 1) ] & 0xF0) == 0x80) )
// DEC 7, SQ_INFO_H 30, MRIW 7
#endif /* (PS5017_EN || PS5021_EN) */

#define M_MR_GET_SQ_LINK_STATUS(ID)		  ((R32_MR[((MR_SLAVE_QUEUE_INFO_SIZE * (ID) + R8_MR_SQ_N_INFO_H) >> 2)] >> SQW_STS_SHIFT) & SQW_STS_MASK)
#define M_MR_SET_SQ_UPPER_LIMIT(ID, CNT)  (R8_MR[(R8_MR_SQ_N_LNK_LMT + (ID))] = (CNT))
#define M_MR_GET_SQ_UPPER_LIMIT(ID)       (R8_MR[(R8_MR_SQ_N_LNK_LMT + (ID))])
#define M_MR_GET_SQ_USING_LNK_CNT(ID)     ((R32_MR[((MR_SLAVE_QUEUE_INFO_SIZE * (ID) + R8_MR_SQ_N_INFO_H) >> 2)] >> SQR_LNK_CNT_SHIFT) & SQR_LNK_CNT_MASK)
#define M_MR_GET_WRITE_PTR(ID)            ((R32_MR[((MR_SLAVE_QUEUE_INFO_SIZE * (ID) + R8_MR_SQ_N_INFO_H) >> 2)] >> SQW_PTR_SHIFT) & SQW_PTR_MASK)
#define M_MR_GET_READ_PTR(ID)             ((R32_MR[((MR_SLAVE_QUEUE_INFO_SIZE * (ID) + R8_MR_SQ_N_INFO_L) >> 2)] >> SQR_PTR_SHIFT) & SQR_PTR_MASK)

#define M_MR_TOTAL_MAX_LINK_CNT()         ((R32_MR[R32_MR_LNK_INFO] >> MAX_LNK_CNT_SHIFT) & MAX_LNK_CNT_MASK)

#define M_MR_CLEAR_INT_FULL()				(R32_MR[R32_MR_ITC_FUL_INFO] = (ITC_FUL_CLR_MASK << ITC_FUL_CLR_SHIFT))

#define M_MR_MASK_PARITY_ERROR()			(R32_MR[R32_MR_ITC_ERR_MASK_N] |= ITC_ERR_MASK_SRAM_PARITY_ERROR_BIT)

#if PS5017_EN
//isr handle
#define M_MR_CHK_INT_ERR_DEC()				 (R32_MR[R32_MR_ITC_ERR_INFO_L] & INT_ERR_DEC_BIT)
#define M_MR_CLR_INT_ERR_DEC()				 (R32_MR[R32_MR_ITC_ERR_INFO_L] |= INT_ERR_DEC_CLR_BIT)

#define M_MR_CHK_INT_ERR_MRIW() 			 (R32_MR[R32_MR_ITC_ERR_INFO_L] & INT_ERR_MRIW_BIT)
#define M_MR_CLR_INT_ERR_MRIW() 			 (R32_MR[R32_MR_ITC_ERR_INFO_L] |= INT_ERR_MRIW_CLR_BIT)

#define M_MR_CHK_INT_ERR_MRIR() 			 (R32_MR[R32_MR_ITC_ERR_INFO_L] & INT_ERR_MRIR_BIT)
#define M_MR_CLR_INT_ERR_MRIR() 			 (R32_MR[R32_MR_ITC_ERR_INFO_L] |= INT_ERR_MRIR_CLR_BIT)

#define M_MR_CHK_INT_ERR_HOST_LOG() 		 (R32_MR[R32_MR_ITC_ERR_INFO_L] & INT_ERR_HOST_LOG_BIT)
#define M_MR_CLR_INT_ERR_HOST_LOG() 		 (R32_MR[R32_MR_ITC_ERR_CLR] |= INT_ERR_LOG_CLR_BIT)

#define M_MR_CHK_INT_ERR_MONITOR()			 (R32_MR[R32_MR_ITC_ERR_INFO_L] & INT_ERR_MONITOR_BIT)
#define M_MR_CLR_INT_ERR_MONITOR()			 (R32_MR[R32_MR_ITC_ERR_CLR] |= INT_ERR_MON_CLR_BIT)

//#define M_MR_CHK_INT_ERR_FULL() 			 (R32_MR[R32_MR_ITC_ERR_INFO_L] & FULL_INT_ERR)		// E17 redefine
//#define M_MR_CLR_INT_ERR_FULL() 			 (R32_MR[R32_MR_ITC_FUL_INFO] |= INT_FUL_CLR_BIT)	// E17 redefine

#define M_MR_CHK_INT_ERR_IOP()				 (R32_MR[R32_MR_ITC_ERR_INFO_L] & INT_ERR_IOP_BIT)
#define M_MR_CLR_INT_ERR_IOP()				 (R32_MR[R32_MR_ITC_ERR_CLR] |= INT_ERR_IOP_CLR_BIT)
#endif /*PS5017_EN*/

#define MR_LOG_MSG_FAIL_CASE_CRC_ERROR			(0)
#define MR_LOG_MSG_FAIL_CASE_ZIP_KEY_FAIL		(1)
#define MR_LOG_MSG_FAIL_CASE_ZIP_CMD_SIZE_FAIL	(2)
#define MR_LOG_MSG_FAIL_CASE_D2H_CMD_SIZE_FAIL	(3)
#define MR_LOG_MSG_FAIL_CASE_KEY_RAM_PAR_ERR	(4)

#define MR_LOG_MSG_ID_AES						(0x02)
#define MR_LOG_MSG_ID_D2H						(0x05)

typedef struct MRLogMsg {
	U64 IP_Define	: 56;
	U64 ID 			: 8;
} MRLogMsg_t;

typedef struct MRD2HLogMsg {
	U64 Fail_Addr : 48;
	U64 Master_ID : 4;
	U64 Error_Flag: 3;
	U64 : 1;
	U64 ID : 8;
} MRD2HLogMsg_t;

typedef struct MRAESLogMsg {
	U64 Axi_id : 8;
	U64 Axi_Addr : 32;
	U64 : 11;
	U64 Fail_Case : 5;
	U64 ID : 8;
} MRAESLogMsg_t;

extern U8 gubMRLogMessageSlaveQueueDummyCnt;

extern void MRRegClearRCQBitmap(RCQOffset_t RCQOffset);
#if PS5017_EN
U16 MrGetLinkValidCnt(void);
#else /*PS5017_EN*/
AOM_BARRIER U16 MrGetLinkValidCnt(void);
#endif /*PS5017_EN*/
AOM_SECURITY U8 MRCheckCOP0DecoderIdle(void);
AOM_SECURITY U8 MRCheckCOP0SlaveIdle(void);
AOM_SECURITY U8 MRCheckDMACDecoderIdle(void);
AOM_SECURITY U8 MRCheckDMACSlaveIdle(void);
void MRLockCOP1Decoder(void);
void MRUnLockCOP1Decoder(void);
AOM_LPM U8 MRCheckAllSQIdle(void);
INLINE U8 MRCheckCOP1SlaveIdle(void)
{
	return ((SQR_EMT_BIT == M_MR_GET_SQR_EMT(MR_COP1_CMD_SLAVE_QUEUE_ID)) &&  //cop1 SQ & apu send to cop1
			(SQR_EMT_BIT == M_MR_GET_SQR_EMT(MR_COP0_RESPOND_TO_COP1_SLAVE_QUEUE_ID)) &&     //cop0 rsp to cop1
			(SQR_EMT_BIT == M_MR_GET_SQR_EMT(MR_XZIP_RESPOND_TO_COP1_SLAVE_QUEUE_ID)) &&     //xzip rsp to cop1
			(SQR_EMT_BIT == M_MR_GET_SQR_EMT(MR_BMU_RESPOND_TO_COP1_SLAVE_QUEUE_ID)) &&     //bmu rsp to cop1
			(SQR_EMT_BIT == M_MR_GET_SQR_EMT(MR_DMAC_RESPOND_TO_COP1_SLAVE_QUEUE_ID))) ? (TRUE) : (FALSE);  //dma rsp to cop1
}

INLINE U8 MRCheckCOP1DecoderIdle(void)
{
	return (((R8_MR[R8_MR_DEC_N_INFO + (MR_COP1_SQ_DECODER_NUM)] & MR_DECODER_ODD_IDLE_BIT_MASK) == MR_DECODER_ODD_STATUS_IDLE) &&
			((R8_MR[R8_MR_DEC_N_INFO + (MR_APU_SEND_TO_COP1_DECODER_NUM)] & MR_DECODER_EVEN_IDLE_BIT_MASK) == MR_DECODER_EVEN_STATUS_IDLE) &&
			((R8_MR[R8_MR_DEC_N_INFO + (MR_COP0_SEND_TO_COP1_DECODER_NUM)] & MR_DECODER_EVEN_IDLE_BIT_MASK) == MR_DECODER_EVEN_STATUS_IDLE) &&
			((R8_MR[R8_MR_DEC_N_INFO + (MR_DMAC_SEND_TO_COP1_DECODER_NUM)] & MR_DECODER_ODD_IDLE_BIT_MASK) == MR_DECODER_ODD_STATUS_IDLE) &&
			((R8_MR[R8_MR_DEC_N_INFO + (MR_BMU_SEND_TO_COP1_DECODER_NUM)] & MR_DECODER_EVEN_IDLE_BIT_MASK) == MR_DECODER_EVEN_STATUS_IDLE)) ? (TRUE) : (FALSE); //dma rsp to cop1
}

INLINE void MRSlaveQueueFlushAll(U8 ubEn)
{
	if (ubEn == TRUE) {
		R32_MR[R32_MR_SQ_FLUSH_EN_L] = MR_SQ_FLUSH_EN_L_ALL;
		R32_MR[R32_MR_SQ_FLUSH_EN_H] = MR_SQ_FLUSH_EN_H_ALL;
	}
	else {
		R32_MR[R32_MR_SQ_FLUSH_EN_L] = 0;
		R32_MR[R32_MR_SQ_FLUSH_EN_H] = 0;
	}
}

#endif /* _MR_API_H_ */
