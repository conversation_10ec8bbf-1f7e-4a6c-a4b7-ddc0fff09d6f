/** @file utils.h
 *  @brief
 *
 *
 *  <AUTHOR>  @bug No know bugs.
 */

#ifndef _UART_H_
#define _UART_H_

#include "setup.h"
#include "typedef.h"
#include "uart_reg.h"
#include "aom/aom_api.h"

//******************************************
//	Define
//******************************************
#if VS_SIM_EN
#define UART_LOG_FILE_NAME              "pc_debug.txt"
#define UART_BUFFER_SIZE				SIZE_128B
#endif /* VS_SIM_EN */

#define M_TX_FIFO_EMPTY(PORT)		(R32_UART[PORT] & (S_FIFO_EMPTY_MASK << S_FIFO_EMPTY_SHIFT))
#define M_TX_FIFO_DATA(PORT, DATA)	(R8_UART[PORT] = DATA)
#define M_TX_FIFO_EN(PORT)			(R32_UART[PORT] |= (1 << TX_S_FIFO_EN_SHIFT))

#define UART_RX_DATA_LENGTH			(9)		// 1bit R/W check + 8bit Data
#define UART_RX_CRC_LENGTH			(2)		// CRC8
#define UART_RX_DATA_BIT_CNT		(32)

#define UART_ASCII_ENTER_KEY		0x0D
#define UART_ASCII_0				0x30
#define UART_ASCII_9				0x39
#define UART_ASCII_A				0x41

#define UART_ASCII_D				0x44
#define UART_ASCII_R				0x52
#define UART_ASCII_W				0x57

#define UART_RX_CRC_PARAMETER		0x07
#define UART_RX_CRC_PARAMETER_SHIFT	(24)

//******************************************
//	Uart parsing State
//******************************************
enum UART_PARSING_STATE {
	UART_PARS_INIT = 0,
	UART_PARS_RECV_DEVICE
};

//******************************************
//	Struct Def
//******************************************

typedef struct RXData_Struct_t	RXData_STRUCT, *RXData_STRUCT_PTR;
struct RXData_Struct_t {
	U8 btRXWriteCmdFlag: 1;
	U8 Reserved: 7;

	U8 ubRXCount;
	U8 ubRXData[UART_RX_DATA_LENGTH + UART_RX_CRC_LENGTH];
	U32 ulRXWriteAddrBuf;
};

extern volatile RXData_STRUCT gsRXData;

//******************************************
//	Marco
//******************************************


//******************************************
//	Variable
//******************************************



//******************************************
//	Function
//******************************************
extern void Uart_ISR(void);
extern void UartEnableISR(void);
extern U8 UartCRC8(U8 *puchMsg, U8 usDataLen);
extern U16 UartCRC16(U32 ulAddr, U16 uwLen);

AOM_UART_RX_DEBUG void UARTRXDataCheck(void);
AOM_UART_RX_DEBUG U8 UARTRXDataCRC(U32 ulRXData);

#endif /* _UART_H_ */
