#include "cpu/cpu_cache_api.h"
#include "hal/cop0/cop0_api.h"
#include "read_disturb/read_disturb_api.h"
#include "read_disturb/read_disturb.h"
#include "cpu/cpu_api.h"
#include "drive_log/drive_log_api.h"
#include "table/initinfo_vt/initinfo_vt_api.h"
#include "ftl/ftl_readverify_api.h"
#if (READ_DISTURB_PRDH_EN)
#include "read_disturb/read_disturb_PRDH_api.h"
#endif /* (READ_DISTURB_PRDH_EN) */

#define READ_DISTURB_MAX_SEARCH_RESULT (8)
#define READ_DISTURB_READ_VERIFY_EN (TRUE)
#define M_READ_DISTURB_SLC_ERASE_CNT_RANGE_ADJUSTMENT(x)	((U32)x*10)

ReadDisturb_t gReadDisturb = {0};
ReadDisturbThrottle_t gReadDisturbThrottle = {0, READ_DISTURB_XFER_DATA_OUT_SQ_THRESHOLD, 0, 0};

AOM_FTL_EXT static U8 ReadDisturbDecideCheck(U16 uwMaxReadCntUnit);
AOM_FTL_EXT static void ReadDisturbDecideSentinelPlane(U16 uwUnitIdx);
AOM_FTL_EXT static void ReadDisturbGetThreshold(U16 *puwReadVerifyThreshold, U16 *puwForceCopyThreshold, U16 uwUnitIdx);
AOM_FTL_EXT static void ReadDisturbDecideHandleUnit(void);
AOM_FTL_EXT static void ReadDisturbAddDriveLog(U8 ubLogType, U16 uwUnitIdx, U16 uwReadCnt, U16 uwEraseCnt);
AOM_FTL_EXT static void ReadDisturbCleanOverThresholdBit(U16 uwUnitIdx, U8 ubCleanReadCnt);
AOM_FTL_EXT static U8 ReadDisturbHandleUnit(U16 uwHandleUnit);
void ReadDisturbSearchMaxReadCntDone_Callback(void);
FW_CALLBACK_SECTION U32 gulReadDisturbSearchMaxReadCntDone_Callback = (U32)ReadDisturbSearchMaxReadCntDone_Callback;

void ReadDisturbSearchMaxReadCntDone_Callback(void)
{
#if ((!BURNER_MODE_EN) && (!RDT_MODE_EN))
	gReadDisturb.ubState = READ_DISTURB_GET_RESULT;
#endif // ((!BURNER_MODE_EN) && (!RDT_MODE_EN))
}

#if ((!BURNER_MODE_EN) && (!RDT_MODE_EN))

U8 ReadDisturbNeedThrottleXferDataOut(void)
{
	return ((gpVT->ReadVerify.ubNowDoingEvent & BIT(READ_VERIFY_SRC_READ_DISTURB_UNIT)) && (gReadDisturbThrottle.uwXferDataOutSendSQNum >= gReadDisturbThrottle.uwXferDataOutSQUpperBound));
}

U8 ReadDisturbDecideCheck(U16 uwMaxReadCntUnit)
{
	U8 ubUnitIdx;
	U8 ubActiveGR = FALSE;

	if (gErrSummary.uwErrorLogNum) {
		if (gErrSummary.uwTail != ErrHandleCheckUnitInFailLog(uwMaxReadCntUnit, FALSE)) {
			return FALSE;
		}
	}

	if (gpVT->FTL.btNowNeedGCTable) {
		for (ubUnitIdx = 0; ubUnitIdx < gFTLState.TableGC.ubGCSourceNum; ubUnitIdx++) {
			if (uwMaxReadCntUnit == gFTLState.TableGC.uwGCSource[ubUnitIdx]) {
				return FALSE;
			}
		}
		for (ubUnitIdx = 0; ubUnitIdx < gFTLState.TableGC.ubGCVCZeroSourceNum; ubUnitIdx++) {
			if (uwMaxReadCntUnit == gFTLState.TableGC.uwGCVCZeroSource[ubUnitIdx]) {
				return FALSE;
			}
		}
	}

	if ((ErrorHandleIsVTMotherUnit(uwMaxReadCntUnit)) || (uwMaxReadCntUnit == gpVT->VTChild.uwUnit.B.uwUnit)) {
		return FALSE;
	}

	if (InitInfoCheckUnitIsInitInfoOldUnit(uwMaxReadCntUnit)) {
		return FALSE;
	}

	if ((uwMaxReadCntUnit == gpVT->DriveLog.uwUnit.B.uwUnit) || (uwMaxReadCntUnit == gpVT->OpenBlockRS.uwUnit.B.uwUnit)) {
		return FALSE;
	}

	if (TRUE == gpulVC[uwMaxReadCntUnit].B.btNoPTEBMP) {
		return FALSE;
	}

	for (ubUnitIdx = 0; ubUnitIdx <= gpVT->GR.ubUnitIndex; ubUnitIdx++ ) {
		if (gpVT->GR.uwUnit[ubUnitIdx].B.uwUnit == uwMaxReadCntUnit) {
			ubActiveGR = TRUE;
			break;
		}
	}
	if ((0 == gpulVC[uwMaxReadCntUnit].B.ValidCnt) && (FALSE == ubActiveGR)) {
		return FALSE;
	}
	return TRUE;
}

void ReadDisturbDecideSentinelPlane(U16 uwUnitIdx)
{
	U32 ulVCAInFIFO = 0;
	U32 ulMaxPlanes = 0;
	U8 ubFound = FALSE;
	U8 ubIdx = 0;
	ulMaxPlanes = M_GET_IS_SMALL_UNIT_BY_VC(uwUnitIdx) ? gulFastPagePlanesPerUnit : gulPlanesPerUnit;

	if (gpulVC[uwUnitIdx].B.btStatic) {
		gReadDisturb.ulSentinelPlaneIdx = ulMaxPlanes;
		M_UART(READ_DISTURB_, "\n [ReadDisturb] Unit Static");
	}
	else {
		for (ubIdx = 0; ubIdx <= gpVT->GR.ubUnitIndex; ubIdx++ ) {
			if (gpVT->GR.uwUnit[ubIdx].B.uwUnit == uwUnitIdx) {
				ubFound = TRUE;
				break;
			}
		}
		if (ubFound) {
			if (gpVT->GR.ubUnitIndex == ubIdx) {
#if (PS5013_EN)
				M_SEL_PLB_ADDR(PLB_PID0);
#if (MICRON_FSP_EN)
				ulVCAInFIFO = M_COP0_GET_VCA_FROM_CSCH_FIFO(); ///////////////////// PCA
				if (FIFO_LAYER_EMPTY == ulVCAInFIFO) {
					gReadDisturb.ulSentinelPlaneIdx = M_FW_GET_PLANE_INDEX(COP0_PCAToVCA(M_GET_COP0_LAST_PCA_PID0()));
				}
				else {
					gReadDisturb.ulSentinelPlaneIdx = M_FW_GET_PLANE_INDEX(COP0_PCAToVCA(ulVCAInFIFO));
				}
#else /* (MICRON_FSP_EN) */
				ulVCAInFIFO = M_COP0_GET_VCA_FROM_CSCH_FIFO();
				if (FIFO_LAYER_EMPTY == ulVCAInFIFO) {
					gReadDisturb.ulSentinelPlaneIdx = M_FW_GET_PLANE_INDEX(M_GET_COP0_LAST_PCA_PID0());
				}
				else {
					gReadDisturb.ulSentinelPlaneIdx = M_FW_GET_PLANE_INDEX(ulVCAInFIFO);
				}
#endif /* (MICRON_FSP_EN) */
#else /* (PS5013_EN) */
#if (MICRON_FSP_EN)
				gReadDisturb.ulSentinelPlaneIdx = M_FW_GET_PLANE_INDEX(COP0_PCAToVCA(M_GET_COP0_LAST_PCA_PID0()));
#else /* (MICRON_FSP_EN) */
				gReadDisturb.ulSentinelPlaneIdx = M_FW_GET_PLANE_INDEX(M_GET_COP0_LAST_PCA_PID0());
#endif /* (MICRON_FSP_EN) */
#endif /* (PS5013_EN) */
			}
			else {
				gReadDisturb.ulSentinelPlaneIdx = ulMaxPlanes;
			}
		}
		else if (gpVT->InfoUnit.uwUnit.B.uwUnit == uwUnitIdx) {
			gReadDisturb.ulSentinelPlaneIdx = gpVT->InfoUnit.ulPlaneIndex;
		}
		else if (gpVT->Table.uwUnit[gpVT->Table.uwUnitIndex].B.uwUnit == uwUnitIdx) {
			gReadDisturb.ulSentinelPlaneIdx = M_FW_GET_PLANE_INDEX(gFTLTablePCAManager.ulLastPCAOfTableProgram);
		}
		else if (gpVT->GCPTETarget.uwUnit.B.uwUnit == uwUnitIdx) {
			gReadDisturb.ulSentinelPlaneIdx = gpVT->GCPTETarget.ulPlaneIndex;;
		}
		else if ((gpVT->VTChild.uwOldUnit.B.uwUnit == uwUnitIdx) || (gpVT->InfoUnit.uwOldInitUnit.B.uwUnit == uwUnitIdx)) {
			gReadDisturb.ulSentinelPlaneIdx = ulMaxPlanes;
		}
		else {
			for (ubIdx = 0; ubIdx < gpVT->GC.ubTargetNumMax; ubIdx++ ) {
				if (gpVT->GC.uwTargetUnit[ubIdx].B.uwUnit == uwUnitIdx) {
					ubFound = TRUE;
					break;
				}
			}

			if (ubFound) {
				if (ubIdx == gpVT->GC.ubTargetCopyIdx) {
					M_SEL_PLB_ADDR(PLB_PID1);
#if (MICRON_FSP_EN)
					ulVCAInFIFO = M_COP0_GET_VCA_FROM_CSCH_FIFO();
					if (FIFO_LAYER_EMPTY == ulVCAInFIFO) {
						gReadDisturb.ulSentinelPlaneIdx = M_FW_GET_PLANE_INDEX(COP0_PCAToVCA(M_GET_COP0_LAST_PCA_PID1()));
					}
					else {
						gReadDisturb.ulSentinelPlaneIdx = M_FW_GET_PLANE_INDEX(COP0_PCAToVCA(ulVCAInFIFO));
					}
#else /* (MICRON_FSP_EN) */
					ulVCAInFIFO = M_COP0_GET_VCA_FROM_CSCH_FIFO();
					if (FIFO_LAYER_EMPTY == ulVCAInFIFO) {
						gReadDisturb.ulSentinelPlaneIdx = M_FW_GET_PLANE_INDEX(M_GET_COP0_LAST_PCA_PID1());
					}
					else {
						gReadDisturb.ulSentinelPlaneIdx = M_FW_GET_PLANE_INDEX(ulVCAInFIFO);
					}
#endif /* (MICRON_FSP_EN) */
				}
				else {
					gReadDisturb.ulSentinelPlaneIdx = ulMaxPlanes;
				}
			}
			else {
				for (ubIdx = 0; ubIdx < gpVT->GC.ubSrcNum; ubIdx++ ) {
					if (gpVT->GC.uwSrcUnit[ubIdx].B.uwUnit == uwUnitIdx) {
						ubFound = TRUE;
						break;
					}
				}
				if (ubFound) {
					gReadDisturb.ulSentinelPlaneIdx = ulMaxPlanes;
				}
				else {
					for (ubIdx = 0; ubIdx < gpVT->Table.uwUnitIndex; ubIdx++ ) {
						if (gpVT->Table.uwUnit[ubIdx].B.uwUnit == uwUnitIdx) {
							ubFound = TRUE;
							break;
						}
					}
					if (ubFound) {
						gReadDisturb.ulSentinelPlaneIdx = ulMaxPlanes;
					}
					else {
						gReadDisturb.ulSentinelPlaneIdx = 0;
					}
				}
			}
		}
		M_UART(READ_DISTURB_, "\n [ReadDisturb] PlaneIdx:%x",  gReadDisturb.ulSentinelPlaneIdx);
	}
}

void ReadDisturbGetThreshold(U16 *puwReadVerifyThreshold, U16 *puwForceCopyThreshold, U16 uwUnitIdx)
{
	U16 uwEraseCnt = gpulEC[uwUnitIdx].B.uwEraseCnt;
	U8 ubReadDisturbCheckRule = READ_DISTURB_XLC_RULE;
	U8 ubLoop;

	M_UART(READ_DISTURB_, "\n [ReadDisturb] Threshold, Unit:%x", uwUnitIdx);
#if ((IM_N28) || (IM_B47R) || (IM_B37R) || (IM_N48R_NEED_CHECK))
	if (gpuwVBRMP[uwUnitIdx].B.btIsD1) {//only static D1
		ubReadDisturbCheckRule = READ_DISTURB_SLC_RULE;
	}
	M_UART(READ_DISTURB_, " D1:%b CheckRule:%d", gpuwVBRMP[uwUnitIdx].B.btIsD1, ubReadDisturbCheckRule);
#else /* ((IM_N28) || (IM_B47R) || (IM_N48R_NEED_CHECK)) */
	if (gpulEC[uwUnitIdx].B.btTableUnit || M_GET_IS_SLC_BY_VC(uwUnitIdx)) {
		ubReadDisturbCheckRule = READ_DISTURB_SLC_RULE;
	}
	M_UART(READ_DISTURB_, " VC_D1:%b VC_SLCPool:%b VC_Table:%b EC_Table:%b CheckRule:%d", gpulVC[uwUnitIdx].B.btD1, gpulVC[uwUnitIdx].B.btSLCPool, gpulVC[uwUnitIdx].B.btTableUnit, gpulEC[uwUnitIdx].B.btTableUnit, ubReadDisturbCheckRule);
#endif /* ((IM_N28) || (IM_B47R) || (IM_N48R_NEED_CHECK)) */

	if (READ_DISTURB_SLC_RULE == ubReadDisturbCheckRule) {
		for (ubLoop = 0; ubLoop < READ_DISTURB_MAX_ERASE_CNT_LEVEL_NUM; ubLoop++) {
			if ((U32)uwEraseCnt <= M_READ_DISTURB_SLC_ERASE_CNT_RANGE_ADJUSTMENT(gReadDisturb.Threshold.uwSLCEraseCntRange[ubLoop])) {
				*puwReadVerifyThreshold = gReadDisturb.Threshold.uwSLCReadVerifyReadCntThreshold[ubLoop];
				*puwForceCopyThreshold = gReadDisturb.Threshold.uwSLCForceCopyReadCntThreshold[ubLoop];
				break;
			}
		}
	}
	else {
		for (ubLoop = 0; ubLoop < READ_DISTURB_MAX_ERASE_CNT_LEVEL_NUM; ubLoop++) {
			if (uwEraseCnt <= gReadDisturb.Threshold.uwTLCEraseCntRange[ubLoop]) {
				*puwReadVerifyThreshold = gReadDisturb.Threshold.uwTLCReadVerifyReadCntThreshold[ubLoop];
				*puwForceCopyThreshold = gReadDisturb.Threshold.uwTLCForceCopyReadCntThreshold[ubLoop];
				break;
			}
		}
	}
}

void ReadDisturbDecideHandleUnit(void)
{
	SMAXEntry_t *pulTargetAddr = (SMAXEntry_t *)DBUF_MAX_READ_CNT_UNIT_SEARCH_RESULT;
	U16 uwResultUnitIdx = INVALID_BLOCK;
	U8 ubLoop = 0;

	gReadDisturb.uwMaxReadCntUnit = INVALID_BLOCK;
	gReadDisturb.ulSentinelPlaneIdx = COP0_INVALID_PLANE;
	for (ubLoop = 0; ubLoop < READ_DISTURB_MAX_SEARCH_RESULT; ubLoop++) {
		if (pulTargetAddr[ubLoop].btValid) {
			uwResultUnitIdx = (U16)(pulTargetAddr[ubLoop].ulOffset);
			if (ReadDisturbDecideCheck(uwResultUnitIdx)) {
				gReadDisturb.uwMaxReadCntUnit = uwResultUnitIdx;
				break;
			}
			else {
				ReadDisturbCleanOverThresholdBit(uwResultUnitIdx, FALSE);
			}
		}
		else {
			break;
		}
	}
}

void ReadDisturbAddDriveLog(U8 ubLogType, U16 uwUnitIdx, U16 uwReadCnt, U16 uwEraseCnt)
{
	DriveLogPayload_t DriveLogPayload = {{0}};
	if (READ_DISTURB_READVERIFY_LOG == ubLogType) {
		DriveLogPayload.Payload_Read_Disturb.Type.btReadVerifyUnit = TRUE;
		DriveLogPayload.Payload_Read_Disturb.ulSentinelPlaneIdx = gReadDisturb.ulSentinelPlaneIdx;
	}
	else {
		DriveLogPayload.Payload_Read_Disturb.Type.btForceSwapUnit = TRUE;
	}
	DriveLogPayload.Payload_Read_Disturb.uwUnit = uwUnitIdx;
	DriveLogPayload.Payload_Read_Disturb.ulForceSwapUnitCnt = gpVTDBUF->ReadDisturb.ulForceSwapUnitCnt;
	DriveLogPayload.Payload_Read_Disturb.ulReadVerifyUnitCnt = gpVTDBUF->ReadDisturb.ulReadVerifyUnitCnt;
	DriveLogPayload.Payload_Read_Disturb.uwOverReadCntThresholdUnitNum = gpComm2Andes_Info->uwOverReadCntThresholdUnitNum;
	DriveLogPayload.Payload_Read_Disturb.uwReadCnt = uwReadCnt;
	DriveLogPayload.Payload_Read_Disturb.uwEraseCnt = uwEraseCnt;
	DriveLogAdd(READ_DISTURB_LOG, &DriveLogPayload, DRIVE_LOG_ADD_DEFAULT_MODE);
}

void ReadDisturbCleanOverThresholdBit(U16 uwUnitIdx, U8 ubCleanReadCnt)
{
	if (gpComm2Andes_Info->uwOverReadCntThresholdUnitNum) {
		--gpComm2Andes_Info->uwOverReadCntThresholdUnitNum;
	}
	M_ARM_MODIFY_DBUF_REQUEST_START();
	if (ubCleanReadCnt) {
		gpulEC[uwUnitIdx].B.ReadCnt = 0;
	}
	gpulEC[uwUnitIdx].B.btOverReadCntThreshold = FALSE;
	M_ARM_MODIFY_DBUF_REQUEST_END();
	M_UART(READ_DISTURB_, "\n [ReadDisturb] Unit:%x Clean ThresholdBit, CleanReadCnt:%b, THRNum:%d", uwUnitIdx, ubCleanReadCnt, gpComm2Andes_Info->uwOverReadCntThresholdUnitNum);
}

U8 ReadDisturbHandleUnit(U16 uwHandleUnit)
{
	U16 uwReadCnt = gpulEC[uwHandleUnit].B.ReadCnt;
	U16 uwEraseCnt = gpulEC[uwHandleUnit].B.uwEraseCnt;
	U16 uwReadVerifyReadCntThreshold = 0;
	U16 uwForceCopyReadCntThreshold = 0;
	U8 ubHandleUnitDone = TRUE;
	U8 ubCleanReadCnt = FALSE;

	ReadDisturbGetThreshold(&uwReadVerifyReadCntThreshold, &uwForceCopyReadCntThreshold, uwHandleUnit);

	M_UART(READ_DISTURB_, "\n [ReadDisturb] Unit:%x, ReadCnt:%d, EraseCnt:%d, OverTHNum:%d", uwHandleUnit, uwReadCnt, uwEraseCnt, gpComm2Andes_Info->uwOverReadCntThresholdUnitNum);
	if ((uwReadCnt >= uwForceCopyReadCntThreshold) && (uwForceCopyReadCntThreshold)) {
		++gpVTDBUF->ReadDisturb.ulForceSwapUnitCnt;
		if (DRIVE_LOG_EN) {
			ReadDisturbAddDriveLog(READ_DISTURB_FORCECOPY_LOG, uwHandleUnit, uwReadCnt, uwEraseCnt);
		}

		ErrLogInfo_t ErrorLogInfo = {0};
		ErrorLogInfo.uwUnit = uwHandleUnit;
		ErrorLogInfo.ubPlaneBank = ERROR_LOG_PLANE_BANK_FOR_READ_DISTURB;
		ErrorLogInfo.ubNeedCopy = TRUE;
		ErrorLogInfo.ubFailPhase = ERROR_LOG_PHASE_COPY;
		ErrHandleErrorLog(&ErrorLogInfo);
		M_UART(READ_DISTURB_, " =>AddErrorLog, LogNum:%d", gErrSummary.uwErrorLogNum);
	}
	else if (uwReadCnt >= uwReadVerifyReadCntThreshold) {
		if (READ_DISTURB_READ_VERIFY_EN) {
			Unit_t uwUnit = {0};
			uwUnit.B.uwUnit = uwHandleUnit;
			if (COP0_INVALID_PLANE == gReadDisturb.ulSentinelPlaneIdx) {
				ReadDisturbDecideSentinelPlane(uwHandleUnit);
			}

			if (SUCCESS == FTLSetReadVerifyEvent(uwUnit, READ_VERIFY_SRC_READ_DISTURB_UNIT, gReadDisturb.ulSentinelPlaneIdx)) {
				++gpVTDBUF->ReadDisturb.ulReadVerifyUnitCnt;
				if (DRIVE_LOG_EN) {
					ReadDisturbAddDriveLog(READ_DISTURB_READVERIFY_LOG, uwHandleUnit, uwReadCnt, uwEraseCnt);
				}
				ubCleanReadCnt = (!uwForceCopyReadCntThreshold);
				M_UART(READ_DISTURB_, " =>SetReadVerify SUCCESS");
			}
			else {
				ubHandleUnitDone = FALSE;
				M_UART(READ_DISTURB_, " =>SetReadVerify FAIL");
			}
		}
	}
	else {
		M_UART(READ_DISTURB_, " =>Andes OverThreshold BUG");
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
	}

	if (ubHandleUnitDone) {
		ReadDisturbCleanOverThresholdBit(uwHandleUnit, ubCleanReadCnt);
	}

	return ubHandleUnitDone;
}

void ReadDisturb(void)
{
#if ((!BURNER_MODE_EN) && (!RDT_MODE_EN))
#if (READ_DISTURB_PRDH_EN)
	if (gReadDisturb.ubState < READ_DISTURB_WAIT_VERIFY_DONE) {
		ReadDisturbPRDHGetOrCheckUnit();
	}
	if (gReadDisturb.ubState == READ_DISTURB_CREATE_ERROR_LOG) {
		ReadDisturbPRDHCreateErrorLog();
	}
	if (gReadDisturb.ubState == READ_DISTURB_FINISH) {
		ReadDisturbPRDHFinish();
	}
#else /* (READ_DISTURB_PRDH_EN) */
	U8 ubHandleUnitDone = TRUE;

	switch (gReadDisturb.ubState) {
	case READ_DISTURB_SEARCH_UNIT:
		M_UART(READ_DISTURB_, "\n [ReadDisturb] OverTHNum:%d ", gpComm2Andes_Info->uwOverReadCntThresholdUnitNum);
		ARM_DCACHE_INVALIDATE_RANGE(DBUF_MAX_READ_CNT_UNIT_SEARCH_RESULT, DBUF_MAX_READ_CNT_UNIT_SEARCH_RESULT_SIZE);
		DMACSearchMaxReadCnt(DBUF_MAX_READ_CNT_UNIT_SEARCH_RESULT, SIZE_IN_32B_CEILING((gpVT->uwTotalD3UnitNum + gpVT->uwTotalD1UnitNum) * sizeof(EraseCnt_t)), (U32)gulReadDisturbSearchMaxReadCntDone_Callback);
		gReadDisturb.ubState = READ_DISTURB_WAIT_SEARCH_UNIT;
		gReadDisturb.btDoing = TRUE;
		break;
	case READ_DISTURB_WAIT_SEARCH_UNIT:
		break;
	case READ_DISTURB_GET_RESULT:
		ReadDisturbDecideHandleUnit();

		if (INVALID_BLOCK == gReadDisturb.uwMaxReadCntUnit) {
			M_UART(READ_DISTURB_, "\n [ReadDisturb] No Unit found!");
			gpComm2Andes_Info->uwOverReadCntThresholdUnitNum = 0;
			gReadDisturb.btKeepSearchUnit = FALSE;
			gReadDisturb.ubState = READ_DISTURB_SEARCH_UNIT;
			gReadDisturb.btDoing = FALSE;
			break;
		}
		else {
			gReadDisturb.ubState = READ_DISTURB_HANDLE_UNIT;
		}

	case READ_DISTURB_HANDLE_UNIT:
		gReadDisturb.btKeepSearchUnit = TRUE;
		ubHandleUnitDone = ReadDisturbHandleUnit(gReadDisturb.uwMaxReadCntUnit);

		if (ubHandleUnitDone) {
			gReadDisturb.ubState = READ_DISTURB_SEARCH_UNIT;
			gReadDisturb.btDoing = FALSE;
		}
		break;
	}
	gpVTDBUF->ReadDisturb.uwOverReadCntThresholdUnitNum = gpComm2Andes_Info->uwOverReadCntThresholdUnitNum;
#endif /* (READ_DISTURB_PRDH_EN) */
#endif // ((!BURNER_MODE_EN) && (!RDT_MODE_EN))
}
#endif //#if ((!BURNER_MODE_EN) && (!RDT_MODE_EN))