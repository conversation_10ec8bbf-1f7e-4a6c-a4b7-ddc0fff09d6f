#ifndef _RETRY_KIOXIA_BICS5_TLC_S17_NEUTRAL_HB_H
#define _RETRY_KIOXIA_BICS5_TLC_S17_NEUTRAL_HB_H

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */

#include "env.h"

#if (PS5017_EN && (FLASH_BICS5TLC == FW_CATEGORY_FLASH) && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER))
/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */
#define RETRY_KIOXIA_BICS5_TLC_512G_STEP_NUM		(30 + 1)
#define RETRY_KIOXIA_BICS5_SLC_512G_STEP_NUM		( 7 + 1)

/*
 * ---------------------------------------------------------------------------------------------------
 *  enum
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */


#endif /* (PS5017_EN && (FLASH_BICS5TLC == FW_CATEGORY_FLASH) && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER)) */

#endif /* _RETRY_KIOXIA_BICS5_TLC_S17_NEUTRAL_HB_H */
