#ifndef _VUC_MICRONGETMLBI_H_
#define _VUC_MICRONGETMLBI_H_
#include "aom/aom_api.h"

#define VUC_MICRON_GET_MLBI_HEADER_LENGTH (12)
#define VUC_MICRON_GET_MLBI_PAYLOAD_LENGTH (10)

typedef struct {
	U16 uwCH;
	U16 uwCE;
	U16 uwLUN;
	U16 uwTrimRegAddr;
} GetMLBiInputData_t;

typedef struct {
	U8 ubResponseHeaderFormatVersion;
	U8 ubResponseDataFotmatVersion;
	U16 uwCMDClass;
	U16 uwCMDCode;
	U16 uwStatus;
	U32 ulDataPayloadSize;
} GetMLBiResponseHeader_t;

typedef struct {
	U16 uwCH;
	U16 uwCE;
	U16 uwLUN;
	U16 uwTrimRegAddr;
	U16 uwTrimRegData;
} GetMLBiResponsePayload_t;

AOM_VUC_3 void VUCMicronGetMLBi(U32 ulInputPayloadAddr, U32 ulPayloadAddr);

#endif /* _VUC_MICRONGETMLBI_H_ */

