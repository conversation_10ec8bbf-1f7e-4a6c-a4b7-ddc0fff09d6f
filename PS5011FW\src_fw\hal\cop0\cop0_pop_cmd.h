#ifndef _COP0_POP_CMD_H_
#define _COP0_POP_CMD_H_

#include "setup.h"
#include "typedef.h"
#include "hal/cop0/cop0_cmd.h"

#if VS_SIM_EN
#define TIE_OUT_WRITE    (2)
#define TIE_OUT_READ     (1)
#else /* VS_SIM_EN */
#define TIE_OUT_WRITE    (1)
#define TIE_OUT_READ     (2)
#endif /* VS_SIM_EN */
#define TIE_OUT_ERASE    (3)
#define TIE_OUT_BARRIER  (5)
#define TIE_OUT_IOR_NO_DMA	(6)
#define TIE_OUT_VENDOR 	(7)

//Tie-Out Msg Type
#define TMSG_TYPE_NO_ERROR	(0)
#define TMSG_TYPE_ERROR		(1)

#define CHK_URGENT_Q_FOR_PATCH(URGENT_BM, PATCH_BM,Qidx) (((0 == (URGENT_BM & BIT(Qidx))) && (PATCH_BM & BIT(Qidx))) ||\
                                                           ((0 == URGENT_BM) && (PATCH_BM & BIT(Qidx))))


#define ERR_CQ_DEBUG_UART_EN		(TRUE && (!BURNER_MODE_EN) && (!RDT_MODE_EN))
#define MTP_STALL_DEBUG_UART_EN		(FALSE)

extern U64 guoErrorFSA;

void COP0DelegateCmd(void);
void COP0EventCheckFLHOPDone_Callback(TIEOUT_FORMAT_t uoResult);
void FTLDelegateAllCmd(U16 uwTagId);
void COP0_ReleaseMTAllocatedPB(U8 ubMTIndex, U16 uwFreePBBitmap);
U8 COP0_MTPool_DeStall( U8 ubChannel, U8 ubBank, U8 ubQos, U8 ubStallRequestEvent);
U8 COP0_MTPool_Stall(U8 ubChannel, U8 ubBank, U8 ubNeedWait, U8 ubStallRequestEvent);
void COP0MTPoolDebugStallRecord(U8 ubChannel, U8 ubBank, U8 ubStallRequestEvent, U8 ubOperationStall);

#endif /* _COP0_POP_CMD_H_ */
