#include "hal/dmac/dmac_api.h"
#include "hal/dmac/dmac_pop_cmd.h"
#include "ftl/ftl_api.h"
#include "hal/pic/uart/uart_api.h"
#include "nvme_api/nvme/shr_hal_nvme.h"
#include "host/VUC_handler.h"
#include "vuc/VUC_command.h"
#include "vuc/VUC_ScanRDTLog.h"

#if (BURNER_MODE_EN || RDT_MODE_EN)
void VUC_GetRDTLog(VUC_OPT_HCMD_PTR_t pCmd)
{
	if (TRUE == gFlhEnv.ulFlashDefaultType.BitMap.btNoSupport) {
		pCmd->ubState = CMD_ERROR;
		gVUCVar.ubVucCmdErrStatus = HOST_ERROR_FEATURE;
		return;
	}

	U8 ubi;
	U8 ubLogId = pCmd->vuc_sqcmd.vendor.GetRDTLog.ubSubFeature;
	U32 ulPageOffset = pCmd->vuc_sqcmd.vendor.GetRDTLog.ulPageOffset;
	U32 ulBufAdr = BURNER_VENDOR_BUF_BASE;
	U32 ulSectorOffset = pCmd->vuc_sqcmd.vendor.GetRDTLog.ulSectorOffset;
	U32 ulLength = pCmd->vuc_sqcmd.vendor.GetRDTLog.ulLength * BYTE_PER_DW;
	PCA_t ulFWPCA;
	U32 ulRDTPCA;
	DMACParam_t DMACParam = {{0}};
	U32 ulBufAddrArray[FRAMES_PER_PAGE];
	U32 ulBlockIndex = 0;
	U32 ulPageIndex = 0;
	U32 ulLogCntPerBlock = gFlhEnv.uwPagePerBlock / 3 - 1;

	ulBlockIndex = ulPageOffset / ulLogCntPerBlock;
	ulPageIndex = ulPageOffset % ulLogCntPerBlock;

	// skip page0
	ulPageIndex++;
	M_UART(VUC_, "\nVUC_GET_RDT_LOG");

	// skip page0
	ulPageOffset++;

	if (INVALID_PCA == gRDTLog[ubLogId].ulRDTLog_PCA[RDT_LOG_BLOCK0].ulAll) {
		pCmd->ubState = CMD_ERROR;
		return;
	}

	if (ulPageOffset <= gRDTLog[ubLogId].uwRDTLog_Pages[RDT_LOG_BLOCK0]) {
		M_FWPCA_PCA_GET(ulRDTPCA, gRDTLog[ubLogId].ulRDTLog_PCA[RDT_LOG_BLOCK0].ulAll);
		ulFWPCA.ulAll = ulRDTPCA + (GET_FPAGE(ulPageOffset) << gPCARule_Page.ubShift[COP0_PCA_RULE_2]);
	}
	else {
		if ( (RDT_LOG_ERROR_RECORD == ubLogId) && (ulPageOffset <= gRDTLog[RDT_LOG_ERROR_RECORD_2].uwRDTLog_Pages[RDT_LOG_BLOCK0])) {
			M_FWPCA_PCA_GET(ulRDTPCA, gRDTLog[RDT_LOG_ERROR_RECORD_2].ulRDTLog_PCA[RDT_LOG_BLOCK0].ulAll);
			ulFWPCA.ulAll = ulRDTPCA + (GET_FPAGE(ulPageOffset - gRDTLog[RDT_LOG_ERROR_RECORD].uwRDTLog_Pages[RDT_LOG_BLOCK0]) << gPCARule_Page.ubShift[COP0_PCA_RULE_2]);
		}
		else if ( (RDT_LOG_ECC_DISTRIBUTION_LOG == ubLogId && ulBlockIndex > 0)
			&& gRDTLog[RDT_LOG_ECC_DISTRIBUTION_LOG + ulBlockIndex].ulRDTLog_PCA[RDT_LOG_BLOCK0].ulAll != INVALID_PCA) {
			M_FWPCA_PCA_GET(ulRDTPCA, gRDTLog[RDT_LOG_ECC_DISTRIBUTION_LOG + ulBlockIndex].ulRDTLog_PCA[RDT_LOG_BLOCK0].ulAll);
			ulFWPCA.ulAll = ulRDTPCA + (GET_FPAGE(ulPageIndex) << gPCARule_Page.ubShift[COP0_PCA_RULE_2]);
		}
		else {
			pCmd->ubState = CMD_ERROR;
			return;
		}
	}
	if (0 == ulSectorOffset) {
		gubLeavePreformatFlag = 0;

		for (ubi = 0; ubi < gub4kEntrysPerPlane; ubi++) {
			ulBufAddrArray[ubi] = ulBufAdr;
			ulBufAdr += FRAME_SIZE;
		}
		FTLCOP0ReadN4kEntry_AOM_INIT_2((FTLCOP0ReadN4kEntryMode_t) {
			R_DEFAULT, COP0_R_SYSTEM_INITIAL_SCAN, gub4kEntrysPerPlane, NULL
		}, ulFWPCA, ulBufAddrArray);

		gubLeavePreformatFlag = 1;
	}
	else {
		DMACParam.DMACDataCopy.ulSourceAddr = BURNER_VENDOR_BUF_BASE + ulSectorOffset * SECTOR_SIZE;
		DMACParam.DMACDataCopy.ulDestAddr = BURNER_VENDOR_BUF_BASE;
		DMACParam.DMACDataCopy.ul32ByteNum = SIZE_IN_32B(ulLength) ;
		gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
		DMACCopy(DMAC_MODE_NORMAL_QUEUE, &DMACParam, gulDMACDirectWaitDone_Callback, 0);
		while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
			DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
		}
	}
}
#endif /* (BURNER_MODE_EN || RDT_MODE_EN) */
