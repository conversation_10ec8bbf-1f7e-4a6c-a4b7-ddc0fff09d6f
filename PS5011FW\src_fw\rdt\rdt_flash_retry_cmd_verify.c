/*
 * rdt_flash_rdy_time_test.c
 *
 *  Created on: 2024¦~12¤ë27¤é
 *      Author: user
 */
#include "rdt/rdt_api.h"

#if RDT_RETRY_CMD_VERIFY

U8 gubTestStateIndex;
U8 gubRVAddress;
U8 gubRVBytesId;

#define START_TEST		   	   			(0x00)
#define SET_AND_CHECK_FEATURE			(0x01)
#define DMA_RAW_READ					(0x02)
#define RECORD_ZERO_CNT   	   			(0x05)
#define ERROR_RETRY_SET_FEATURE_TIMEOUT	(0x03)
#define ERROR_RETRY_SET_FEATURE_FAIL	(0x04)
#define ERROR_RETRY_VERIFY_FAIL     	(0x06)
#define SLC_FLASH_TEST     	   			(0x07)
#define TEST_FLOW_FINISH_INDEX			(0xFF)

void rdt_flash_test_program_1p(RDT_API_STRUCT_PTR rdt, U8 ubDie, U16 uwUnit, U8 ubBank, U8 ubCh, U8 ubPhyPlaneIndex, U8 ubPCARule, ProgramOrder *pProgramOrder)
{
	if (!pProgramOrder || !pProgramOrder->ubIsValidPO) {
		M_UART(RDT_TEST_, "Invalid program order\n");
		return;
	}

	U8 ubLmu;
	U8 ubLmuEnd;
	U8 ubCOP0OPT;
	U8 ubProcessWL 	= 0;
	U8 ubProgramMode;
	U32 ulDataBuf;
	U32 ulLocalPCA;

	U8 ubProcessWLCount = CACHE_PROG_WL_SCALE;

	FPL_GEOMETRY_STRUCT_PTR geometry = &rdt->fpl->geometry;

	if (pProgramOrder->ubIsSLC) {
		ubLmuEnd = 1;
	}
	else {
		ubLmuEnd = gubLMUNumber;
	}

	for (ubProcessWL = 0; ubProcessWL < ubProcessWLCount; ubProcessWL++) {
		if (pProgramOrder->ubIsValidPO) {
			if ((pProgramOrder->swWL >= 0) && (pProgramOrder->swWL < pProgramOrder->uwEndWL)) {	//Reip Porting 3D-V7 QLC Add
#if (IM_N48R || IM_B47R)
				ubLmuEnd = pProgramOrder->ubIsSLC ? 1 : FTLGetCoord(GetPageFromCellNumInRDT((U16)pProgramOrder->swWL, 0), IM_GETCOORD_WIN_SIZE); //LP, UP, XP, TP
#elif ((FW_CATEGORY_FLASH == FLASH_YMTC_EMS_QLC))//ems mst add--karl
				ubLmuEnd = pProgramOrder->ubIsSLC ? 1 : FTLGetCoord(GetPageFromCellNumInRDT_EMS((U16)pProgramOrder->swWL, 0), IM_GETCOORD_WIN_SIZE); //LP, UP, XP, TP
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
				ubLmuEnd = pProgramOrder->ubIsSLC ? 1 : FTLGetCoord(GetPageFromCellNumInRDT((U16)pProgramOrder->swWL, 0), IM_GETCOORD_WIN_SIZE);
#endif
				for (ubLmu = 0; ubLmu < ubLmuEnd; ubLmu++) {
					/* PCA */
#if (IM_N48R || IM_B47R)
					ulLocalPCA = PCA_VALUE(ubPCARule, ubDie, uwUnit, ubBank, ubCh, ubPhyPlaneIndex, pProgramOrder->ubIsSLC ? pProgramOrder->swWL : GetPageFromCellNumInRDT((U16)pProgramOrder->swWL, ubLmu), 0, 0);
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_QLC)//ems mst add--karl
					ulLocalPCA = PCA_VALUE(ubPCARule, ubDie, uwUnit, ubBank, ubCh, ubPhyPlaneIndex, pProgramOrder->ubIsSLC ? pProgramOrder->swWL : GetPageFromCellNumInRDT_EMS((U16)pProgramOrder->swWL, ubLmu), 0, 0);
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_TLC)
					ulLocalPCA = PCA_VALUE(ubPCARule, ubDie, uwUnit, ubBank, ubCh, ubPhyPlaneIndex, pProgramOrder->swWL * ubLmuEnd + ubLmu, 0, 0);
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
					ulLocalPCA = PCA_VALUE(ubPCARule, ubDie, uwUnit, ubBank, ubCh, ubPhyPlaneIndex, pProgramOrder->ubIsSLC ? pProgramOrder->swWL : GetPageFromCellNumInRDT((U16)pProgramOrder->swWL, ubLmu), 0, 0);
#elif (FW_CATEGORY_FLASH == FLASH_INTEL_N38A_QLC)
					ulLocalPCA = PCA_VALUE(ubPCARule, ubDie, uwUnit, ubBank, ubCh, ubPhyPlaneIndex, pProgramOrder->swWL * ubLmuEnd + ubLmu, 0, 0);
#else /*(IM_N48R)*/
					ulLocalPCA = PCA_VALUE(ubPCARule, ubDie, uwUnit, ubBank, ubCh, ubPhyPlaneIndex, pProgramOrder->swWL, ubLmu, 0);
#endif /*(IM_N48R)*/
					/* Assign write buf */
					U32 rw_buf_id = rdt_get_rwbuf_id(geometry, ubLmu, ubPhyPlaneIndex, ubCh, ubBank);
					ulDataBuf = rdt_api_get_data_buf(rdt, rw_buf_id, 0, BUF_TYPE_WRITE);
					U32 prog_data = PCA_VALUE(ubPCARule, ubDie, uwUnit, ubBank, ubCh, 0, 0, 0, 0);
					rdt_api_dmac_setvalue((prog_data), ulDataBuf, (U32)PAGE_BYTE_SIZE);
					((U16 *)(ulDataBuf))[0] = 0xFFFF;	//Reip

					/* Send program tie-in */
					if (ubLmu == (ubLmuEnd - 1)) {
						ubCOP0OPT = MULTIPLANE_END;
					}
					else {
						ubCOP0OPT = MULTIPLANE_ING;
					}
#if ENABLE_RDT_SRAM_RS_ENC_TEST
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC)	//Reip Porting 3D-V7 QLC Add
					if (!pProgramOrder->ubIsSLC) {
						if (2 == pProgramOrder->ubPass) {
							ubProgramMode = RDT_COP0_W_RAIDECC_ENC_PROGRAM;

						}
						else {
							ubProgramMode = RDT_COP0_W_RAIDECC_ENC_PROGRAM_2PASS;
						}
					}
					else {
						ubProgramMode = RDT_COP0_W_RAIDECC_ENC_PROGRAM;
					}
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_QLC)//zerio bics6 qlc add//zerio bics6 qlc add
					if (!pProgramOrder->ubIsSLC) {
						if (2 == pProgramOrder->ubPass) {
							ubProgramMode = RDT_COP0_W_RAIDECC_ENC_PROGRAM_2PASS;
						}
						else {
							ubProgramMode = RDT_COP0_W_RAIDECC_ENC_PROGRAM;
						}
					}
					else {
						ubProgramMode = RDT_COP0_W_RAIDECC_ENC_PROGRAM;
					}
#elif((FW_CATEGORY_FLASH == FLASH_YMTC_EMS_QLC))//ems mst add--karl
					ubProgramMode = RDT_COP0_W_RAIDECC_ENC_PROGRAM;
					if (!pProgramOrder->ubIsSLC) {
						if ((1 == pProgramOrder->ubPass) && (QLC_WL_PAGE_NUM == FTLGetCoord(GetPageFromCellNumInRDT_EMS((U16)pProgramOrder->swWL, ubLmu), IM_GETCOORD_WIN_SIZE))) {
							ubProgramMode = RDT_COP0_W_RAIDECC_ENC_PROGRAM_2PASS;
						}
					}
#elif(IM_N48R)//Dylan for V6 RDT porting,Reference V6 based RDT code,V6 don't do this case
					ubProgramMode = RDT_COP0_W_RAIDECC_ENC_PROGRAM;
					if (!pProgramOrder->ubIsSLC) {
						if ((1 == pProgramOrder->ubPass) && (QLC_WL_PAGE_NUM == FTLGetCoord(GetPageFromCellNumInRDT((U16)pProgramOrder->swWL, ubLmu), IM_GETCOORD_WIN_SIZE)))  {//
							ubProgramMode = RDT_COP0_W_RAIDECC_ENC_PROGRAM_2PASS; //QLC page first pass use 0Dh
						}
					}
#else //FLASH_TYPE_HYNIX_3D_QLC
					ubProgramMode = RDT_COP0_W_RAIDECC_ENC_PROGRAM;
#endif //FLASH_TYPE_HYNIX_3D_QLC

					rdt_api_cop0_prog(rdt_find_block_callback, (U32)ulLocalPCA, rdt, ubProgramMode, ubCOP0OPT, pProgramOrder->ubIsSLC, (U32)ulDataBuf, (U32)ulLocalPCA, 0);
#endif


				} //End of ubLmu
			}//pProgramOrder.swWL < pProgramOrder->uwEndWL + 8
			else {
#if(CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC)	//Reip Porting 3D-V7 QLC Add
				M_UART(RDT_DBG_, "\nInvalid program order");
#else
				M_UART(RDT_TEST_, "\nInvalid program order");
#endif
			}
		}	//pProgramOrder->ubIsValidPO
		else {
#if(CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC)	//Reip Porting 3D-V7 QLC Add
			M_UART(RDT_DBG_, "\nInvalid program order");
#else
			M_UART(RDT_TEST_, "\nInvalid program order");
#endif
		}
		*pProgramOrder = GetNextProgramOrder(*pProgramOrder);
	} //End of process_wl
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC)	//Reip Porting 3D-V7 QLC Add
	if ((pProgramOrder->ubPass == 2) && (pProgramOrder->swWL < (pProgramOrder->uwEndWL))) {
		pProgramOrder->ubIsValidPO = TRUE;
	}
#endif

	while (FTLGetTagPoolNum(COP0_TAG_POOL_ID) != TAG_NUM) {
		//Use while to prevent tag pool num would be not enough for next run
		/* Recieve all program CQ */
		rdt_api_delegate_partial_CQ_with_IC_pattern(BUF_TYPE_WRITE, 0);
	}
	/* Ping-pong ERL (to do) */
	rdt_api_program_erl_log(rdt, FALSE);
}

void rdt_flash_set_feature_and_check_result(U8 ubCh, U8 ubTestId, U8 ubRVOffset, U8 ubTestAdressCnt)
{
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC || CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)	//This Flash is Special, Need Read Offset Prefix(2Eh) Command to Change RV before Read Data;
	gubTestStateIndex = DMA_RAW_READ;
	return;
#else

#if (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC)
	U8 ubRVBytesId[7] = {1, 1, 1, 1, 1, 1, 1};
#if ((FW_CATEGORY_FLASH == FLASH_V5_TLC) || (FW_CATEGORY_FLASH == FLASH_V6_TLC))
	U8 ubRVAddress[7] = {0x29, 0x2A, 0x2B, 0x2C, 0x2D, 0x2E, 0x2F};
#elif ((FW_CATEGORY_FLASH == FLASH_V7_TLC) || (FW_CATEGORY_FLASH == FLASH_V8_TLC))
	U8 ubRVAddress[7] = {0xA9, 0xAA, 0xAB, 0xAC, 0xAD, 0xAE, 0xAF};
#endif
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC)
	U8 ubRVAddress[15] = {0x92, 0x93, 0x94, 0x95, 0x96, 0x97, 0x98, 0x99, 0x9A, 0x9B, 0x9C, 0x9D, 0x9E, 0x9F, 0xA0};
	U8 ubRVBytesId[15] = {1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1};
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_TLC)
#if (FW_CATEGORY_FLASH == FLASH_SANDISK_BICS5_TLC)
	U8 ubRVAddress[7] = {0x89, 0x8A, 0x89, 0x89, 0x8A, 0x89, 0x8A};
	U8 ubRVBytesId[7] = {1, 1, 2, 3, 2, 4, 3};//1/2/3/4 means set the first/second/third/fourth byte of the RVAddress
#else
	U8 ubRVAddress[7] = {0x89, 0x8A, 0x89, 0x8A, 0x89, 0x8A, 0x89};
	//U8 ubRVAddress[7] = {0x12, 0x13, 0x12, 0x13, 0x12, 0x13, 0x12};
	U8 ubRVBytesId[7] = {1, 1, 2, 2, 3, 3, 4};
#endif
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_QLC)
	U8 ubRVAddress[15] = {0x9D, 0x9D, 0x9D, 0x9E, 0x9E, 0x9E, 0x9E, 0x9B, 0x9B, 0x9B, 0x9B, 0x9C, 0x9C, 0x9C, 0x9C};
	U8 ubRVBytesId[15] = {1, 2, 3, 1, 2, 3, 4, 1, 2, 3, 4, 1, 2, 3, 4};
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
	U8 ubRVAddress[7] = {0x89, 0x8A, 0x89, 0x8A, 0x89, 0x8A, 0x89};
	U8 ubRVBytesId[7] = {1, 1, 2, 2, 3, 3, 4};
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_TLC)
	U8 ubRVAddress[7] = {0xA0, 0xA1, 0xA2, 0xA1, 0xA0, 0xA1, 0xA2};
	U8 ubRVBytesId[7] = {1, 1, 1, 2, 2, 3, 2};
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_QLC)
	U8 ubRVAddress[15] = {0xA3, 0xA0, 0xA1, 0xA3, 0xA2, 0xA3, 0xA1, 0xA0, 0xA1, 0xA2, 0xA3, 0xA2, 0xA1, 0xA0, 0xA2};
	U8 ubRVBytesId[15] = {1, 1, 1, 2, 1, 3, 2, 2, 3, 2, 4, 3, 4, 3, 4};
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_INTEL_3D_QLC)
	U8 ubRVAddress[4] = {0x89, 0x89, 0x89, 0x89};
	U8 ubRVBytesId[4] = {1, 1, 1, 1};
#endif

	REG32 *pFlaReg = (REG32 *)R32_FCTL_CH[ubCh];
	FlaCEControl(ubCh, 0, ENABLE);
	__asm("NOP");
	//M_UART(RDT_TEST_, "\nVerify RVAddress = %x, ubRVOffset = %x", ubRVAddress[ubTestId], ubRVOffset);
	gubTestStateIndex = DMA_RAW_READ;
	gubRVAddress = ubRVAddress[ubTestId];
	gubRVBytesId = ubRVBytesId[ubTestId];

#if ((CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC))
	//Set Feature
	for (U8 ubi = 0; ubi < ubTestAdressCnt; ubi++) {
		pFlaReg[R32_FCTL_PIO_CMD] = 0x36;
		__asm("NOP");
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC)
		pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
		__asm("NOP");
		pFlaReg[R32_FCTL_PIO_DAT] = 0x01;
		__asm("NOP");
#endif
		pFlaReg[R32_FCTL_PIO_ADR] = ubRVAddress[ubi];
		__asm("NOP");
		pFlaReg[R32_FCTL_PIO_DAT] = (ubi == ubTestId) ? ubRVOffset : 0;
		__asm("NOP");
		pFlaReg[R32_FCTL_PIO_CMD] = 0x16;
		__asm("NOP");
		if (FAIL == FlaWaitFlashReady(GERNAL_TIMEOUT_THRESHOLD)) {
			gubTestStateIndex = ERROR_RETRY_SET_FEATURE_TIMEOUT;
			gubRVAddress = ubRVAddress[ubi];
			return;
		}
	}
	//Get Feature
	U8 ubFeatureData;
	pFlaReg[R32_FCTL_PIO_CMD] = 0x37;
	__asm("NOP");
	pFlaReg[R32_FCTL_PIO_ADR] = ubRVAddress[ubTestId];
	__asm("NOP");
	ubFeatureData = (U8)pFlaReg[R32_FCTL_PIO_DAT];
	__asm("NOP");
	//Check
	if (ubFeatureData != ubRVOffset) {
		gubTestStateIndex = ERROR_RETRY_SET_FEATURE_FAIL;
		return;
	}
#elif ((CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_TLC) \
		|| (CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_QLC) \
		|| (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC) \
		|| (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_TLC) \
		|| (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_QLC) \
		|| (CONFIG_FLASH_TYPE == FLASH_TYPE_INTEL_3D_QLC))
	//Set Feature
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_QLC)
	for (U8 ubi = 0x9B; ubi <= 0x9E; ubi++) {
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_TLC)
	for (U8 ubi = 0xA0; ubi <= 0xA2; ubi++) {
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_QLC)
	for (U8 ubi = 0xA0; ubi <= 0xA3; ubi++) {
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_INTEL_3D_QLC)
	for (U8 ubi = 0x89; ubi <= 0x89; ubi++) {
#else
	for (U8 ubi = 0x89; ubi <= 0x8A; ubi++) {
#endif

#if(YMTC_FSP_EN)
		pFlaReg[R32_FCTL_HS_MODE] |= (BIT0 | BIT1);
		pFlaReg[R32_FCTL_PIO_CMD] = 0xEF;
		pFlaReg[R32_FCTL_HS_MODE] &= ~(BIT0 | BIT1);
#else
		pFlaReg[R32_FCTL_PIO_CMD] = 0xEF;
#endif
		__asm("NOP");
		pFlaReg[R32_FCTL_PIO_ADR] = ubi;
		__asm("DSB");
		IdlePC(200);
		pFlaReg[R32_FCTL_HS_MODE] &= CLR_DAT_MID;

		for (U8 ubk = 0; ubk < 4; ubk++) {
			if (ubk == 0) {
				pFlaReg[R32_FCTL_HS_MODE] |= PIO_DAT_FIRST_BIT;
			}
			else if (ubk == 3) {
				pFlaReg[R32_FCTL_HS_MODE] |= PIO_DAT_LAST_BIT;
			}
			__asm("DSB");
			pFlaReg[R32_FCTL_PIO_DAT] = ((ubi == ubRVAddress[ubTestId]) && ((ubRVBytesId[ubTestId] - 1) == ubk)) ?
				((U8)ubRVOffset << 8 | (U8)ubRVOffset) : 0;
			pFlaReg[R32_FCTL_HS_MODE] &= CLR_DAT_MID;
			__asm("DSB");
		}

		pFlaReg[R32_FCTL_HS_MODE] |= SET_DAT_DEFAULT;

		if (FAIL == FlaWaitFlashReady(GERNAL_TIMEOUT_THRESHOLD)) {
			gubTestStateIndex = ERROR_RETRY_SET_FEATURE_TIMEOUT;
			gubRVAddress = ubi;
			return;
		}
	}
	//Get Feature
	U8 ubFeatureData[4] = {0, 0, 0, 0};
#if(YMTC_FSP_EN)
	pFlaReg[R32_FCTL_HS_MODE] |= (BIT0 | BIT1);
	pFlaReg[R32_FCTL_PIO_CMD] = 0xEE;
	pFlaReg[R32_FCTL_HS_MODE] &= ~(BIT0 | BIT1);
#else
	pFlaReg[R32_FCTL_PIO_CMD] = 0xEE;
#endif
	__asm("NOP");
	pFlaReg[R32_FCTL_PIO_ADR] = ubRVAddress[ubTestId];
	__asm("DSB");
	IdlePC(200);
	pFlaReg[R32_FCTL_HS_MODE] &= CLR_DAT_MID;
	for (U8 ubj = 0; ubj < 4; ubj++) {
		if (ubj == 0) {
			pFlaReg[R32_FCTL_HS_MODE] |= PIO_DAT_FIRST_BIT;
		}
		else if (ubj == 3) {
			pFlaReg[R32_FCTL_HS_MODE] |= PIO_DAT_LAST_BIT;
		}
		__asm("DSB");
		ubFeatureData[ubj] = (U8)pFlaReg[R32_FCTL_PIO_DAT];
		pFlaReg[R32_FCTL_HS_MODE] &= CLR_DAT_MID;
	}
	pFlaReg[R32_FCTL_HS_MODE] |= SET_DAT_DEFAULT;
	//Check
	if (ubFeatureData[ubRVBytesId[ubTestId] - 1] != ubRVOffset) {
		gubTestStateIndex = ERROR_RETRY_SET_FEATURE_FAIL;
		return;
	}
	//pFlaReg[R32_FCTL_FLH_SET] = ulBackup;
#endif
#endif
}

void FIPReadNoDMA(U8 ubCh, U8 ubPageType, U8 ubTestId, U8 ubRVOffset)
{
#if ((CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC) \
		|| (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC))
	U16 gwFPU_tlc_1P_prog[4] = {
		FPU_PTR_OFFSET(fpu_entry_tlc_1p_30_lower_pg_read),
		FPU_PTR_OFFSET(fpu_entry_tlc_1p_30_middle_pg_read),
		FPU_PTR_OFFSET(fpu_entry_tlc_1p_30_upper_pg_read),
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC)
		FPU_PTR_OFFSET(fpu_entry_qlc_1p_30_top_pg_read)
#else
		FPU_PTR_OFFSET(fpu_entry_nop)
#endif
	};
#elif ((CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_TLC) \
		|| (CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_QLC))
	U16 gwFPU_tlc_1P_prog[4] = {
		FPU_PTR_OFFSET(fpu_sandisk_tlc_dynamic_read_lower),
		FPU_PTR_OFFSET(fpu_sandisk_tlc_dynamic_read_middle),
		FPU_PTR_OFFSET(fpu_sandisk_tlc_dynamic_read_upper),
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_QLC)
		FPU_PTR_OFFSET(fpu_sandisk_tlc_dynamic_read_top)
#else
		FPU_PTR_OFFSET(fpu_entry_nop)
#endif
	};
#elif ((CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC) \
		|| (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_TLC))
	U16 gwFPU_tlc_1P_prog[3] = {
		FPU_PTR_OFFSET(fpu_entry_tlc_1p_30_pg_read),
		FPU_PTR_OFFSET(fpu_entry_tlc_1p_30_pg_read),
		FPU_PTR_OFFSET(fpu_entry_tlc_1p_30_pg_read)
	};
#elif ((CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_QLC) \
		/*|| (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC)*/)
	U16 gwFPU_tlc_1P_prog[4] = {
		FPU_PTR_OFFSET(fpu_entry_tlc_1p_30_read),
		FPU_PTR_OFFSET(fpu_entry_tlc_1p_30_read),
		FPU_PTR_OFFSET(fpu_entry_tlc_1p_30_read),
		FPU_PTR_OFFSET(fpu_entry_tlc_1p_30_read)
	};
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_INTEL_3D_QLC)
	U16 gwFPU_tlc_1P_prog[4] = {
		FPU_PTR_OFFSET(fpu_entry_tlc_1p_30_pg_read),
		FPU_PTR_OFFSET(fpu_entry_tlc_1p_30_pg_read),
		FPU_PTR_OFFSET(fpu_entry_tlc_1p_30_pg_read),
		FPU_PTR_OFFSET(fpu_entry_tlc_1p_30_pg_read)
	};
#endif

#if (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC || CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)
	U8 ubFPUIdx = 0;
	U16 uwFPUPtr, *puwFPU;
	U8 ubRVCycleId[7] = {0, 1, 0, 1, 2, 0, 1};
	//U8 ubRVAddress[7] = {0xA5, 0xA9, 0xA6, 0xA8, 0xAA, 0xA7, 0xAB};
	//M_UART(RDT_TEST_, "\nVerify RVAddress = %x, ubRVOffset = %x", ubRVAddress[ubTestId], ubRVOffset);
	uwFPUPtr = FPU_PTR_OFFSET(fpu_tlc_prefix_read_retry);
	puwFPU = (U16 *)(IRAM_BASE + uwFPUPtr);
	puwFPU += 1;
	for (U8 ubi = 0; ubi < 3; ubi++) {
		puwFPU[ubFPUIdx++] = FPU_ADR_1B((ubi == ubRVCycleId[ubTestId]) ? ubRVOffset : 0);
	}
	FIPFPUTrigger(ubCh, uwFPUPtr);
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)
	U8 ubFPUIdx = 0;
	U16 uwFPUPtr, *puwFPU;
	U8 ubRVCycleId[15] = {0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 0, 1, 2, 3};
	//M_UART(RDT_TEST_, "\nVerify RVAddress = %x, ubRVOffset = %x", ubRVAddress[ubTestId], ubRVOffset);
	uwFPUPtr = FPU_PTR_OFFSET(fpu_tlc_prefix_read_retry);
	puwFPU = (U16 *)(IRAM_BASE + uwFPUPtr);
	puwFPU += 1;
	for (U8 ubi = 0; ubi < 4; ubi++) {
		puwFPU[ubFPUIdx++] = FPU_ADR_1B((ubi == ubRVCycleId[ubTestId]) ? ubRVOffset : 0);
	}
	FIPFPUTrigger(ubCh, uwFPUPtr);
#else
	FIPFPUTrigger(ubCh, gwFPU_tlc_1P_prog[ubPageType]);
#endif

	FIPFPUTrigger(ubCh, FPU_PTR_OFFSET(fpu_entry_70_e0));
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC || CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)
	REG32 *pFlaReg = (REG32 *)R32_FCTL_CH[ubCh];
	FlaCEControl(ubCh, 0, ENABLE);
	pFlaReg[R32_FCTL_PIO_CMD] = 0x00;
#endif
#if ((CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC) \
		|| (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC) \
		|| (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC))
	FIPFPUTrigger(ubCh, FPU_PTR_OFFSET(fpu_entry_00_05_E0_no_dma_read));
#elif ((CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_TLC) \
		|| (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_QLC) \
		|| (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC) \
		|| (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)\
		|| (CONFIG_FLASH_TYPE == FLASH_TYPE_INTEL_3D_QLC))
	FIPFPUTrigger(ubCh, FPU_PTR_OFFSET(fpu_entry_06_E0_no_dma_read));
#else
	FIPFPUTrigger(ubCh, FPU_PTR_OFFSET(fpu_entry_05_E0_no_dma_read));
#endif
}

void rdt_flash_dma_raw_read(U8 ubCh, U8 ubPageType, U32 ulPatternSize, U32 ulLocalPCA, U8 ubTestId, U8 ubRVOffset)
{
	memset((void *)SCAN_READ_BUF_BASE, 0x00, ulPatternSize);
	//Backup
	REG32 *pFlaReg = (REG32 *)R32_FCTL_CH[ubCh];
	U32 ul_R32_FCTL_SEED_INIT_backup = R32_FALL[R32_FCTL_SEED_INIT];
	U32 ul_R32_FCTL_ECC_CFG_backup = R32_FALL[R32_FCTL_ECC_CFG];
	U32 ul_R32_FCTL_DMA_CFG_backup = R32_FALL[R32_FCTL_DMA_CFG];
	U32 ul_R32_FCTL_CHNL_SET_backup = pFlaReg[R32_FCTL_CHNL_SET];
	//Disable Conversion, Inversion, LDPC, Snap Read
	pFlaReg[R32_FCTL_CHNL_SET] |= SET_CONV_BYPASS_EN;
	pFlaReg[R32_FCTL_CHNL_SET] |= SET_INV_BYPASS_EN;
	R32_FALL[R32_FCTL_SEED_INIT] = 0;
	R32_FALL[R32_FCTL_ECC_CFG] &= CLR_LDPC_COR_DIS;
	R32_FALL[R32_FCTL_DMA_CFG] &= ~(SNAP_READ_EN_BIT | SET_ERF_UNC_EN);
	// Set FSA
	M_FIP_VUC_ASSIGN_IFSA(ubCh, ulLocalPCA, 0);
	pFlaReg[R32_FCTL_DMA_CFG] &= CLR_ALU_GROUP_CLEAR;
	pFlaReg[R32_FCTL_DMA_CFG] |= 0;
	// Set frame = 1
	pFlaReg[R32_FCTL_OTHER_SET] &= CLR_FRAME_START_PTR_CLEAR;
	pFlaReg[R32_FCTL_OTHER_SET] |= (0 << FRAME_START_PNT_SHIFT);
	pFlaReg[R32_FCTL_DMA_CFG] &= CLR_FRAME_NUM;
	pFlaReg[R32_FCTL_DMA_CFG] |= M_SET_FRAME_NUM(1);
	// Set Raw Read frame size
	pFlaReg[R32_FCTL_PURE_RAW_DMA_CFG] &= (~(PURE_RAW_DMA_LEN_MASK << PURE_RAW_DMA_LEN_SHIFT));
	pFlaReg[R32_FCTL_PURE_RAW_DMA_CFG] |= FRAME_SIZE;
	//Read Data No DMA
	FIPReadNoDMA(ubCh, ubPageType, ubTestId, ubRVOffset);
	// Raw read data to IBF1, then Backup frame to DBUF
	U8 ubIBfCnt, ubIBfPtr;
	U16 uwDataLen;
	ubIBfCnt = ((ulPatternSize >> FIP_FOURK_PAGE_LOG) + ((ulPatternSize & 0xFFF) ? 1 : 0));
	for (ubIBfPtr = 0; ubIBfPtr < ubIBfCnt; ubIBfPtr++) {
		if (ubIBfPtr == (ubIBfCnt - 1)) {
			//uwDataLen = ulPatternSize & 0xFFF;
			uwDataLen = (ulPatternSize - (ubIBfPtr * FRAME_SIZE) + 15) / 16 * 16;//Should be a multiple of 16 bytes
			pFlaReg[R32_FCTL_PURE_RAW_DMA_CFG] &= (~(PURE_RAW_DMA_LEN_MASK << PURE_RAW_DMA_LEN_SHIFT));
			pFlaReg[R32_FCTL_PURE_RAW_DMA_CFG] |= uwDataLen;
		}
		else {
			uwDataLen = FRAME_SIZE;
		}
		U16 *FpuPtr;
		FpuPtr = (U16 *)(IRAM_BASE + FPU_PTR_OFFSET(fpu_entry_DMA_R_RAW));
		*FpuPtr = FPU_DMA_R_RAW(FPU_RAW_DST_IBUF(0)
				| FPU_RAW_SRC_IBUF(1)
				| FPU_RAW_SET_MODE(2)
				| FPU_RAW_LOGIC_NOP);
		FIPFPUTrigger(ubCh, FPU_PTR_OFFSET(fpu_entry_DMA_R_RAW));
		// BR backup unit size = 512byte ; if <= 512byte, transfer 512 byte
		FIPBackupRestore(ubCh, SCAN_READ_BUF_BASE + (ubIBfPtr * FRAME_SIZE), 0, 0, 0, 0, 0, uwDataLen);
	}
	//Recover
	R32_FALL[R32_FCTL_SEED_INIT] = ul_R32_FCTL_SEED_INIT_backup;
	R32_FALL[R32_FCTL_ECC_CFG] = ul_R32_FCTL_ECC_CFG_backup;
	R32_FALL[R32_FCTL_DMA_CFG] = ul_R32_FCTL_DMA_CFG_backup;
	pFlaReg[R32_FCTL_CHNL_SET] = ul_R32_FCTL_CHNL_SET_backup;

	gubTestStateIndex = RECORD_ZERO_CNT;
}

U32 rdt_flash_count_zero(U32 ulPatternSize)
{
	U32 ulOneCnt = 0;
	U32 ulZeroCnt = 0;
	U8 *pubBytesData;
	pubBytesData = (U8 *)(SCAN_READ_BUF_BASE);
	for (U16 uwi = 0; uwi < ulPatternSize; uwi++) {
		for (U8 uwj = 0; uwj < 8; uwj++) {
			if (pubBytesData[uwi] & (1 << uwj)) {
				ulOneCnt++;
			}
			else {
				ulZeroCnt++;
			}
		}
	}
	//M_UART(RDT_TEST_, "\nThe OneCnt-sf = %d", ulOneCnt);
	//M_UART(RDT_TEST_, "\nThe ZeroCnt-sf = %d", ulZeroCnt);
	return ulZeroCnt;
}

void rdt_flash_retry_cmd_verify(RDT_API_STRUCT_PTR rdt)
{
	U8 	ubi;
	U8  ubCh;
	U8	ubDie;
	U8  ubBank;
	U8  ubPCARule;
	U8 	ubFailOccur = FALSE;
	U8  ubPhyPlaneIndex;
	U8 	ubDefaultTestUnitCnt;
	U16 uwUnit;
	U16	uwWLEnd;
	U16 uwTestUnitCnt;
	U16 *puwTestUnitId;
	U32 ulPatternSize;
	U32 ulLocalPCA;
	U32 ulGoodUnitBufBase;
	U32 ulTestUnitBufBase;
	ERL_LOG_STRUCT 			erl_log;
	ERR_RETRY_CMD_VERIFY	erl_retry_cmd_verify;
	FPL_GEOMETRY_STRUCT_PTR geometry = &(rdt->fpl->geometry);

	U32 flash_test_start_time;
	U32 flash_test_end_time;
	flash_test_start_time = rdt_api_rtt_get_timer_count();

	ubPCARule = PCA_RULE(0);
	ulPatternSize = gFlhEnv.uwPageTotalByteCnt;
	memset(&erl_log, 0x00, sizeof(ERL_LOG_STRUCT));
	memset(&erl_retry_cmd_verify, 0x00, sizeof(ERR_RETRY_CMD_VERIFY));

#if (IM_N48R)
	uwWLEnd   = IM_N48_CELLNUM;
#elif (IM_B47R)
	uwWLEnd	 = IM_B47R_CELLNUM;
#elif ((FW_CATEGORY_FLASH == FLASH_YMTC_EMS_QLC))//ems mst add--karl
	uwWLEnd   = IM_EMS_CELLNUM;
#elif (FW_CATEGORY_FLASH == FLASH_SAMSUNG_V6_TLC)
	uwWLEnd   = IM_SSV6_CELLNUM;
#elif (FW_CATEGORY_FLASH == FLASH_SAMSUNG_V6P_TLC)
	uwWLEnd   = IM_SSV6P_CELLNUM;
#elif (FW_CATEGORY_FLASH == FLASH_SAMSUNG_V7_TLC)//Samsung v7 mst add--Reip
	uwWLEnd   = IM_SSV7_CELLNUM;
#elif (FW_CATEGORY_FLASH == FLASH_SAMSUNG_V8_TLC)//Samsung v8 mst add--Reip
	uwWLEnd   = IM_SSV8_CELLNUM;
#else
	uwWLEnd   = geometry->d1_page_per_block; //Dylan for V6 RDT porting, Reference V6-based RDT code
#endif

	ubDefaultTestUnitCnt = 2;	//Select 2 unit test per plane
	ulGoodUnitBufBase = SCAN_GEN_PATTERN_BASE;
	ulTestUnitBufBase = SCAN_PROGRAM_BUF_BASE;
	rdt_get_test_unit(rdt, 0, ubDefaultTestUnitCnt, ulGoodUnitBufBase, ulTestUnitBufBase);
	puwTestUnitId = (U16 *)(SCAN_PROGRAM_BUF_BASE);

#if !DIEIL_EN
	for (ubDie = 0; ubDie < geometry->die_per_ce; ubDie++) {
#endif
		for (ubBank = 0; ubBank < geometry->total_bank; ubBank++) {
			for (ubCh = 0; ubCh < geometry->channel_per_bank; ubCh++) {
				for (ubPhyPlaneIndex = 0; ubPhyPlaneIndex < geometry->plane_per_die; ubPhyPlaneIndex++) {

					uwTestUnitCnt = puwTestUnitId[0];
					if (uwTestUnitCnt == 0xFFFF) {
						M_UART(RDT_TEST_, "\n Skip the bad plane %d", ubPhyPlaneIndex);
						puwTestUnitId += 1;
						continue;
					}

					for (ubi = 1; ubi <= uwTestUnitCnt; ubi++) {
						if (ubFailOccur == FALSE) {	//if have error, stop test
							uwUnit = puwTestUnitId[ubi];
							M_UART(RDT_TEST_, "\n test ce = %d, channel = %d, die = %d, plane = %d, vb = %d", ubBank, ubCh, ubDie, ubPhyPlaneIndex, uwUnit);
							//xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
							//						Erase
							//xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
							rdt->find_block_result = TRUE;
#if (FW_CATEGORY_FLASH == FLASH_INTEL_N38A_QLC)
							for (U8 i = 0; i < 3; i++) { //N38A·ÖÈý¶ÎErase
								ulLocalPCA = M_GET_PCA(ubDie, uwUnit, (3072 * i), ubBank, ubCh, ubPhyPlaneIndex, 0, ubPCARule);
								rdt_api_cop0_erase(rdt_find_block_callback, (U32)ulLocalPCA, 0, MULTIPLANE_END, 0);
								rdt_api_delegate_CQ(BUF_TYPE_ERASE);
							}
#else
							ulLocalPCA = M_GET_PCA(ubDie, uwUnit, 0, ubBank, ubCh, ubPhyPlaneIndex, 0, ubPCARule);
							rdt_api_cop0_erase(rdt_find_block_callback, (U32)ulLocalPCA, 0, MULTIPLANE_END, 0);
							rdt_api_delegate_CQ(BUF_TYPE_ERASE);
#endif
							if (!rdt->find_block_result) {
								M_UART(RDT_TEST_, "\n Erase Fail, Skip!");
								continue;
							}
							//M_UART(RDT_TEST_, "\n Debug point 1");
							//xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
							//						Program
							//xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
							ProgramOrder poProgramOrder = GetInitialPorgramOrder(0, 0, uwWLEnd);
							while (poProgramOrder.ubIsValidPO) {
								rdt_flash_test_program_1p(rdt, ubDie, uwUnit, ubBank, ubCh, ubPhyPlaneIndex, ubPCARule, &poProgramOrder);
							}
							if (!rdt->find_block_result) {
								M_UART(RDT_TEST_, "\n Program Fail, Skip!");
								continue;
							}
							//M_UART(RDT_TEST_, "\n Debug point 2");
							//xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
							//						Read
							//xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
							U8 ubTestRVGroupID = 0;
							U8 ubTestId = 0;
							U8 ubRVOffset[2] = {0x81, 0x7F};	//Default, can be changed according to the actual situation
							U32 ulZeroCnt[2] = {0, 0};
							U32 ulThreshlod = 10000;	//Default, can be changed according to the actual situation
							U32 ulZeroDifference = 0;

#if (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC)
							U8 ubTestPageType[7]  = {UPPER_PAGE, MIDDLE_PAGE, LOWER_PAGE, MIDDLE_PAGE, UPPER_PAGE, MIDDLE_PAGE, LOWER_PAGE};
							U8 ubTestAdressCnt = 7;
							U8 ubWL = 0;
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC)
							U8 ubTestPageType[15]  = {LOWER_PAGE, MIDDLE_PAGE, UPPER_PAGE, TOP_PAGE, UPPER_PAGE, LOWER_PAGE, MIDDLE_PAGE,
									LOWER_PAGE, UPPER_PAGE, TOP_PAGE, LOWER_PAGE, TOP_PAGE, MIDDLE_PAGE, TOP_PAGE, UPPER_PAGE
								};
							U8 ubTestAdressCnt = 15;
							U8 ubWL = 0;
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_TLC)
#if (FW_CATEGORY_FLASH == FLASH_SANDISK_BICS5_TLC)
							U8 ubTestPageType[7]  = {MIDDLE_PAGE, UPPER_PAGE, MIDDLE_PAGE, LOWER_PAGE, UPPER_PAGE, MIDDLE_PAGE, UPPER_PAGE};
#else
							U8 ubTestPageType[7]  = {LOWER_PAGE, MIDDLE_PAGE, UPPER_PAGE, MIDDLE_PAGE, LOWER_PAGE, MIDDLE_PAGE, UPPER_PAGE};
#endif
							U8 ubTestAdressCnt = 7;
							U8 ubWL = 0;
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_QLC)
							U8 ubTestPageType[15]  = {LOWER_PAGE, LOWER_PAGE, LOWER_PAGE, MIDDLE_PAGE, MIDDLE_PAGE, MIDDLE_PAGE, MIDDLE_PAGE,
									UPPER_PAGE, UPPER_PAGE, UPPER_PAGE, UPPER_PAGE, TOP_PAGE, TOP_PAGE, TOP_PAGE, TOP_PAGE
								};
							U8 ubTestAdressCnt = 15;
							U8 ubWL = 0;
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
							U8 ubTestPageType[7]  = {LOWER_PAGE, MIDDLE_PAGE, LOWER_PAGE, MIDDLE_PAGE, UPPER_PAGE, MIDDLE_PAGE, UPPER_PAGE};
							U8 ubTestAdressCnt = 7;
							U8 ubWL = 55;
							U16 ulD3Page = 0;
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_TLC)
							U8 ubTestPageType[7]  = {LOWER_PAGE, MIDDLE_PAGE, UPPER_PAGE, MIDDLE_PAGE, LOWER_PAGE, MIDDLE_PAGE, UPPER_PAGE};
							U8 ubTestAdressCnt = 7;
							U8 ubWL = 0;
							U16 ulD3Page = 0;
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_QLC)
							U8 ubTestPageType[15]  = {TOP_PAGE, LOWER_PAGE, MIDDLE_PAGE, TOP_PAGE, UPPER_PAGE, TOP_PAGE, MIDDLE_PAGE,
									LOWER_PAGE, MIDDLE_PAGE, UPPER_PAGE, TOP_PAGE, UPPER_PAGE, MIDDLE_PAGE, LOWER_PAGE, UPPER_PAGE
								};
							U8 ubTestAdressCnt = 15;
							U8 ubWL = 55;
							U16 ulD3Page = 0;
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC)
							U8 ubTestPageType[7]  = {LOWER_PAGE, LOWER_PAGE, MIDDLE_PAGE, MIDDLE_PAGE, MIDDLE_PAGE, UPPER_PAGE, UPPER_PAGE};
							U8 ubTestAdressCnt = 7;
							U8 ubWL = 4;
							U16 ulD3Page = 0;
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)
							U8 ubTestPageType[15]  = {TOP_PAGE, LOWER_PAGE, MIDDLE_PAGE, TOP_PAGE, UPPER_PAGE, TOP_PAGE, MIDDLE_PAGE,
									LOWER_PAGE, MIDDLE_PAGE, UPPER_PAGE, TOP_PAGE, UPPER_PAGE, MIDDLE_PAGE, LOWER_PAGE, UPPER_PAGE
								};
							U8 ubTestAdressCnt = 15;
							U8 ubWL = 55;
							U16 ulD3Page = 0;
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_INTEL_3D_QLC)
							U8 ubTestPageType[4]  = {LOWER_PAGE, MIDDLE_PAGE, UPPER_PAGE, TOP_PAGE};
							U8 ubTestAdressCnt = 4;
							U8 ubWL = 10;
							U16 ulD3Page = 0;
#endif

							gubTestStateIndex = START_TEST;
							while (gubTestStateIndex < TEST_FLOW_FINISH_INDEX) {
								switch (gubTestStateIndex) {
								case START_TEST:
									gubTestStateIndex = SET_AND_CHECK_FEATURE;
									break;
								case SET_AND_CHECK_FEATURE:
#if (FW_CATEGORY_FLASH == FLASH_V5_TLC)
									if (ubTestId == 1) {	//HY3DV5-TLC, The RVAddress 0x2A is special, we need change RVOffset to test;
										ubRVOffset[0] = 0x98;
										ubRVOffset[1] = 0x99;
									}
									else {
										ubRVOffset[0] = 0x81;
										ubRVOffset[1] = 0x7F;
									}
#elif(INTEL_FSP_EN)
									ubRVOffset[0] = 0x0B;
									ubRVOffset[1] = 0x0C;
#endif
									rdt_flash_set_feature_and_check_result(ubCh, ubTestId, ubRVOffset[ubTestRVGroupID], ubTestAdressCnt);
									break;
								case DMA_RAW_READ:
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
									ulD3Page = GetPageFromCellNumInRDT((U16)ubWL, ubTestPageType[ubTestId]);
									ulLocalPCA = PCA_VALUE(ubPCARule, ubDie, uwUnit, ubBank, ubCh, ubPhyPlaneIndex, ulD3Page, 0, 0);
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_TLC)
									ulD3Page = ubWL * 3 + ubTestPageType[ubTestId];
									ulLocalPCA = PCA_VALUE(ubPCARule, ubDie, uwUnit, ubBank, ubCh, ubPhyPlaneIndex, ulD3Page, 0, 0);
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_QLC)
									ulD3Page = GetPageFromCellNumInRDT_EMS((U16)ubWL, ubTestPageType[ubTestId]);
									ulLocalPCA = PCA_VALUE(ubPCARule, ubDie, uwUnit, ubBank, ubCh, ubPhyPlaneIndex, ulD3Page, 0, 0);
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC || CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)
									ulD3Page = GetPageFromCellNumInRDT((U16)ubWL, ubTestPageType[ubTestId]);
									ulLocalPCA = PCA_VALUE(ubPCARule, ubDie, uwUnit, ubBank, ubCh, ubPhyPlaneIndex, ulD3Page, 0, 0);
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_INTEL_3D_QLC)
									ulD3Page = ubWL * 4 + ubTestPageType[ubTestId];
									ulLocalPCA = PCA_VALUE(ubPCARule, ubDie, uwUnit, ubBank, ubCh, ubPhyPlaneIndex, ulD3Page, 0, 0);
#else
									ulLocalPCA = PCA_VALUE(ubPCARule, ubDie, uwUnit, ubBank, ubCh, ubPhyPlaneIndex, ubWL, ubTestPageType[ubTestId], 0);
#endif
									//M_UART(RDT_TEST_, "\n ulLocalPCA = %x", ulLocalPCA);
									rdt_flash_dma_raw_read(ubCh, ubTestPageType[ubTestId], ulPatternSize, ulLocalPCA, ubTestId, ubRVOffset[ubTestRVGroupID]);
									break;
								case RECORD_ZERO_CNT:
									ulZeroCnt[ubTestRVGroupID] = rdt_flash_count_zero(ulPatternSize);
									if (ubTestRVGroupID == 0) {
										ubTestRVGroupID++;
										ulZeroCnt[ubTestRVGroupID] = ulZeroCnt[ubTestRVGroupID - 1];
										gubTestStateIndex = SET_AND_CHECK_FEATURE;
									}
									else {
										if (ulZeroCnt[0] > ulZeroCnt[1]) {
											ulZeroDifference = ulZeroCnt[0] - ulZeroCnt[1];
										}
										else {
											ulZeroDifference = ulZeroCnt[1] - ulZeroCnt[0];
										}
										if (ulZeroDifference < ulThreshlod) {
											M_UART(RDT_TEST_, "\nRetry Cmd Verify Fail");
											gubTestStateIndex = ERROR_RETRY_VERIFY_FAIL;
										}
										else {
											if (ubTestId == (ubTestAdressCnt - 1)) {
												M_UART(RDT_TEST_, "\nRetry Cmd Verify Pass");
												gubTestStateIndex = TEST_FLOW_FINISH_INDEX;
											}
											else {
												ubTestId++;
												ubTestRVGroupID = 0;
												ulZeroCnt[0] = 0;
												ulZeroCnt[1] = 0;
												gubTestStateIndex = SET_AND_CHECK_FEATURE;
											}
										}
									}
									break;
								case ERROR_RETRY_SET_FEATURE_TIMEOUT:
									M_UART(RDT_TEST_, "\n ERROR_RETRY_SET_FEATURE_TIMEOUT");
									ubFailOccur = TRUE;
									erl_log.erl_retry_cmd_verify.fail_type = ERL_RETRY_SET_FEATURE_TIMEOUT;
									erl_log.erl_retry_cmd_verify.RVAddress = gubRVAddress;
									erl_log.erl_retry_cmd_verify.RVBytesId = gubRVBytesId;
									erl_log.erl_retry_cmd_verify.RVOffset1 = ubRVOffset[ubTestRVGroupID];
									gubTestStateIndex = TEST_FLOW_FINISH_INDEX;
									break;
								case ERROR_RETRY_SET_FEATURE_FAIL:
									M_UART(RDT_TEST_, "\n ERROR_RETRY_SET_FEATURE_FAIL");
									ubFailOccur = TRUE;
									erl_log.erl_retry_cmd_verify.fail_type = ERL_RETRY_SET_FEATURE_FAIL;
									erl_log.erl_retry_cmd_verify.RVAddress = gubRVAddress;
									erl_log.erl_retry_cmd_verify.RVBytesId = gubRVBytesId;
									erl_log.erl_retry_cmd_verify.RVOffset1 = ubRVOffset[ubTestRVGroupID];
									gubTestStateIndex = TEST_FLOW_FINISH_INDEX;
									break;
								case ERROR_RETRY_VERIFY_FAIL:
									M_UART(RDT_TEST_, "\n ERROR_RETRY_VERIFY_FAIL");
									ubFailOccur = TRUE;
									erl_log.erl_retry_cmd_verify.fail_type = ERL_RETRY_VERIFY_FAIL;
									erl_log.erl_retry_cmd_verify.RVAddress = gubRVAddress;
									erl_log.erl_retry_cmd_verify.RVBytesId = gubRVBytesId;
									erl_log.erl_retry_cmd_verify.RVOffset1 = ubRVOffset[0];
									erl_log.erl_retry_cmd_verify.RVOffset2 = ubRVOffset[1];
									erl_log.erl_retry_cmd_verify.ZeroDifference = ulZeroDifference;
									gubTestStateIndex = TEST_FLOW_FINISH_INDEX;
									break;
								default:
									gubTestStateIndex = TEST_FLOW_FINISH_INDEX;
									break;
								}
							}

							if (ubFailOccur == TRUE) {
								M_UART(RDT_TEST_, "\nfail_type = %d, ", erl_log.erl_retry_cmd_verify.fail_type);
								M_UART(RDT_TEST_, "RVAddress = %x, ", erl_log.erl_retry_cmd_verify.RVAddress);
								M_UART(RDT_TEST_, "RVBytesId = %d, ", erl_log.erl_retry_cmd_verify.RVBytesId);
								M_UART(RDT_TEST_, "RVOffset1 = %x, ", erl_log.erl_retry_cmd_verify.RVOffset1);
								M_UART(RDT_TEST_, "RVOffset2 = %x, ", erl_log.erl_retry_cmd_verify.RVOffset2);
								M_UART(RDT_TEST_, "ulZeroDifference = %d, ", erl_log.erl_retry_cmd_verify.ZeroDifference);
								rdt_api_erl_log_add(rdt, &erl_log);
								rdt_api_program_erl_log(rdt, TRUE);
							}

							//xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
							//                      Erase End
							//xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

							if (rdt->param.rdt_op_option.skip_last_erase) {
								//M_UART(RDT_TEST_, "\n Skip Erase!"); //Reip test
							}
							else {
								ulLocalPCA = M_GET_PCA(ubDie, uwUnit, 0, ubBank, ubCh, ubPhyPlaneIndex, 0, ubPCARule);
								rdt_api_cop0_erase((void *)NULL, (U32)ulLocalPCA, 0, MULTIPLANE_END, 0);
								rdt_api_delegate_CQ(BUF_TYPE_ERASE);
							}
						}
					} //End of ubi
					puwTestUnitId += (uwTestUnitCnt + 1);
				} //End of ubPhyPlaneIndex
			} //End of ubCh
		} //End of ubBank
#if !DIEIL_EN
	} //End of ubDie
#endif
	flash_test_end_time = rdt_api_rtt_get_timer_count();
	M_UART(RDT_TEST_, "\nflash tlc test total time: %d second\n", (U16)((flash_test_end_time - flash_test_start_time) / (1000)));
}

#endif	//RDT_RETRY_CMD_VERIFY
