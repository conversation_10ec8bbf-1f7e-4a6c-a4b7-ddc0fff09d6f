#ifndef _RUT_H_
#define _RUT_H_

#include "typedef.h"
#include "symbol.h"

//==============================================================================
// Definition
//==============================================================================
// Using global variable as spare table for debug mode
#define RAND_GEN_RUT_FOR_TEST			(0)
#define RAND_FILL_BAD_TO_SPARE			(0)

#define RUT_MAX_DISTRICTS				(2)
#define RUT_MAX_SPARE_NUM				(7551) //( 30 * 1024(30k) - startoffset table( 2 * 129 * 2 ) ) / 4(per spare 4bytes)

#define RUTRULE_STARTBIT_OFFSET			(0)
#define RUTRULE_STARTBIT_LENGTH			(5)
#define RUTRULE_LENGTH_OFFSET			(5)
#define RUTRULE_LENGTH_LENGTH			(4)


#define RUT_DUMMY_PB			(0xFFF)

#define M_BUILD_RUT_BIT_MAP_CHECK_RESULT(ELEMANT, PLANEBANKIDX) ((ELEMANT)->uoBitmap[(PLANEBANKIDX) >> RUT_PLANE_BANK_BMP_SHIFT] & BIT64(((PLANEBANKIDX) & RUT_PLANE_BANK_BMP_BIT_MASK)) ? TRUE : FALSE)

#define M_BUILD_RUT_GET_SPARE(SPARE, BLKIDX, PLANEBANKIDX, D1) do { \
	(SPARE).ulAll = 0; \
	(SPARE).BlockInPlane = (BLKIDX); \
	(SPARE).Plane = (PLANEBANKIDX) % gFlhEnv.PlanePerDie; \
	(SPARE).LUN = ((PLANEBANKIDX)/ gFlhEnv.PlanePerDie) / gFlhEnv.ubCENumber;\
	(SPARE).CE = ((PLANEBANKIDX) / gFlhEnv.PlanePerDie) % gFlhEnv.ubCENumber; \
	(SPARE).btD1 = D1; \
} while(0)

//==============================================================================
// Structures
//==============================================================================

//==============================================================================
// Function
//==============================================================================
#endif /* _RUT_H_ */
