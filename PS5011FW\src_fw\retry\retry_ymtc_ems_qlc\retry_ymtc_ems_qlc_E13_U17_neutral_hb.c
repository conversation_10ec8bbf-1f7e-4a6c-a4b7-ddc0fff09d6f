#include "retry/retry.h"
#include "retry/retry_api.h"
#include "retry/retry_hb.h"
#include "retry_ymtc_ems_qlc_E13_U17_neutral_hb.h"
#include "hal/pic/uart/uart_api.h"
#include "retry/retry_version_api.h" // Retry Version
#include "table/initinfo_vt/initinfo_vt.h" // VT
#include "ftl/ftl_api.h" // FW Timer
#include "hal/fip/fpu.h"
#include "table/sys_block/sys_block_api.h" //RetryInitSystemBlockReadRetryTable

#if (((PS5013_EN) || (PS5017_EN)) && (FLASH_YMTC_EMS_QLC == FW_CATEGORY_FLASH)  && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER))
/*
 * ----------------------------------------------------------------------------
 *   Definitions
 * ----------------------------------------------------------------------------
 */

#define RETRY_HYNIX_V6_SLC_RRT_BASE		(DBUF_RETRY_RR_TABLE)
#define RETRY_HYNIX_V6_TLC_RRT_BASE		(DBUF_RETRY_RR_TABLE + DEF_KB(2))

#define OTP_RR_CNT_SITE                 (8)
#define OTP_RR_REG_CNT_SITE             (16)

#define OTP_DEFAULT_RR_CNT              (50)
#define OTP_DEFAULT_RR_REG_CNT_SLC      (HBIT_RETRY_SLC_FEA_DATA_NUM)
#define OTP_DEFAULT_RR_REG_CNT_QLC      (HBIT_RETRY_QLC_FEA_DATA_NUM)

#define OTP_DEFAULT_SET_NUM             (8)
#define OTP_NORMAL_SEQ                  (0)
#define OTP_INVERSE_SEQ                 (1)
#define OTP_SEQ_CNT_PER_SET             (2)

/*
 * ----------------------------------------------------------------------------
 *   Enum
 * ----------------------------------------------------------------------------
 */

/*
 * ----------------------------------------------------------------------------
 *   Data Types
 * ----------------------------------------------------------------------------
 */

typedef struct {
	U8 ubSeq[OTP_DEFAULT_SET_NUM][OTP_SEQ_CNT_PER_SET][OTP_DEFAULT_RR_CNT * OTP_DEFAULT_RR_REG_CNT_QLC];
} OTPSeq_t;
/*
 * ----------------------------------------------------------------------------
 *   Macros
 * ----------------------------------------------------------------------------
 */

/*
 * ----------------------------------------------------------------------------
 *   Extern Global Variables
 * ----------------------------------------------------------------------------
 */
extern U8 gubHynixtADLDelay;
extern U8 gubHynixtWHRDelay;

const U8 gubRetryReadRetryTableParameterIdxToPageType[HBIT_RETRY_SELECT_NON_SLC_FEA_DATA_NUM] = {
	HB_LRU_LOWER, HB_LRU_LOWER, HB_LRU_LOWER, HB_LRU_LOWER, HB_LRU_MIDDLE, HB_LRU_MIDDLE, HB_LRU_MIDDLE, HB_LRU_MIDDLE,
	HB_LRU_UPPER, HB_LRU_UPPER, HB_LRU_UPPER, HB_LRU_UPPER, HB_LRU_TOP, HB_LRU_TOP, HB_LRU_TOP, HB_LRU_TOP
};

const U8 gubRetryTransitionToPageType[HBIT_RETRY_SELECT_NON_SLC_FEA_DATA_NUM] = {
	TOP_PAGE_SEL, LOWER_PAGE_SEL, MIDDLE_PAGE_SEL, TOP_PAGE_SEL, UPPER_PAGE_SEL, TOP_PAGE_SEL, MIDDLE_PAGE_SEL, LOWER_PAGE_SEL,
	MIDDLE_PAGE_SEL, UPPER_PAGE_SEL, TOP_PAGE_SEL, UPPER_PAGE_SEL, MIDDLE_PAGE_SEL, LOWER_PAGE_SEL, UPPER_PAGE_SEL, 4
};

AOM_RETRY_LOAD_TABLE const U8 gubYMTCEMSQLCHBRetryData1024G[HBIT_RETRY_YMTC_EMS_QLC_1024G_STEP_NUM * HBIT_RETRY_QLC_FEA_DATA_NUM] = {
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, //00
	0x00, 0x02, 0x06, 0x00, 0x00, 0x02, 0x02, 0x06, 0x00, 0x02, 0x03, 0x09, 0xF6, 0x00, 0x01, 0x02, //01
	0x00, 0x03, 0x08, 0x00, 0x00, 0x03, 0x03, 0x08, 0x00, 0x03, 0x05, 0x0B, 0xF6, 0x00, 0x02, 0x04, //02
	0x00, 0x04, 0x0A, 0x00, 0x00, 0x04, 0x04, 0x0A, 0x00, 0x04, 0x07, 0x0D, 0xF6, 0x00, 0x03, 0x06, //03
	0x01, 0x00, 0x04, 0x00, 0x01, 0xFF, 0x00, 0x03, 0x02, 0x00, 0x00, 0x04, 0xF5, 0x00, 0xFF, 0x01, //04
	0xFF, 0x00, 0x06, 0x00, 0xFE, 0xFF, 0x01, 0x04, 0xFF, 0x02, 0x04, 0x06, 0xFF, 0xFE, 0x00, 0x02, //05
	0x03, 0x02, 0xFC, 0x00, 0x03, 0x02, 0x01, 0xFE, 0x01, 0x00, 0xFF, 0xFB, 0xF6, 0x03, 0x04, 0x02, //06
	0x05, 0x04, 0xFF, 0x00, 0x05, 0x04, 0x04, 0x00, 0x04, 0x02, 0x01, 0xFE, 0xFA, 0x05, 0x06, 0x05, //07
	0x07, 0x06, 0x01, 0x00, 0x07, 0x06, 0x06, 0x03, 0x06, 0x05, 0x04, 0x00, 0xFD, 0x07, 0x08, 0x07, //08
	0x0A, 0x09, 0x04, 0x00, 0x0A, 0x09, 0x09, 0x06, 0x09, 0x08, 0x07, 0x03, 0x00, 0x0A, 0x0B, 0x0A, //09
	0x0C, 0x0B, 0x06, 0x00, 0x0C, 0x0B, 0x0B, 0x08, 0x0B, 0x0A, 0x09, 0x05, 0x03, 0x0C, 0x0D, 0x0C, //10
	0x0E, 0x0D, 0x09, 0x00, 0x0E, 0x0D, 0x0D, 0x0B, 0x0D, 0x0D, 0x0C, 0x08, 0x06, 0x0E, 0x0F, 0x0F, //11
	0x10, 0x0F, 0x0C, 0x00, 0x10, 0x0F, 0x10, 0x0E, 0x10, 0x0F, 0x0F, 0x0B, 0x0A, 0x10, 0x11, 0x11, //12
	0xF0, 0xED, 0xEA, 0x00, 0xEF, 0xEC, 0xEC, 0xEB, 0xEE, 0xEB, 0xE9, 0xE8, 0xE4, 0xEE, 0xEE, 0xED, //13
	0xF2, 0xF0, 0xEC, 0x00, 0xF1, 0xEF, 0xEF, 0xED, 0xF1, 0xED, 0xEC, 0xEB, 0xE8, 0xF0, 0xF0, 0xF0, //14
	0xF4, 0xF2, 0xEF, 0x00, 0xF3, 0xF1, 0xF1, 0xF0, 0xF3, 0xEF, 0xEE, 0xEE, 0xEC, 0xF2, 0xF2, 0xF2, //15
	0xF6, 0xF4, 0xF1, 0x00, 0xF5, 0xF3, 0xF3, 0xF2, 0xF5, 0xF1, 0xF0, 0xF0, 0xEF, 0xF4, 0xF4, 0xF4, //16
	0xF9, 0xF7, 0xF4, 0x00, 0xF8, 0xF6, 0xF6, 0xF5, 0xF8, 0xF4, 0xF3, 0xF3, 0xF3, 0xF7, 0xF7, 0xF7, //17
	0xFB, 0xF9, 0xF7, 0x00, 0xFA, 0xF8, 0xF8, 0xF8, 0xFA, 0xF6, 0xF5, 0xF6, 0xF7, 0xF9, 0xF9, 0xF9, //18
	0xFD, 0xFC, 0xF9, 0x00, 0xFC, 0xFB, 0xFB, 0xFA, 0xFD, 0xF8, 0xF8, 0xF9, 0xFB, 0xFB, 0xFB, 0xFC, //19
	0xFD, 0xF4, 0xE8, 0x00, 0xFC, 0xF5, 0xF2, 0xEA, 0xF9, 0xF0, 0xEB, 0xE4, 0xF0, 0xFA, 0xF8, 0xF0, //20
	0xFF, 0xF6, 0xEA, 0x00, 0xFE, 0xF7, 0xF4, 0xEC, 0xFB, 0xF2, 0xED, 0xE7, 0xF4, 0xFC, 0xFA, 0xF2, //21
	0x00, 0xF7, 0xEC, 0x00, 0xFF, 0xF8, 0xF5, 0xEE, 0xFC, 0xF3, 0xEF, 0xE9, 0xF8, 0xFD, 0xFB, 0xF4, //22
	0x03, 0xF9, 0xEE, 0x00, 0x01, 0xFA, 0xF7, 0xF0, 0xFE, 0xF5, 0xF1, 0xEB, 0xFB, 0xFF, 0xFD, 0xF6, //23
	0x05, 0xFB, 0xF1, 0x00, 0x03, 0xFC, 0xF9, 0xF3, 0x00, 0xF7, 0xF4, 0xEE, 0xFF, 0x01, 0xFF, 0xF9, //24
	0x07, 0xFD, 0xF3, 0x00, 0x04, 0xFE, 0xFB, 0xF5, 0x01, 0xF9, 0xF6, 0xF0, 0x02, 0x02, 0x00, 0xFB, //25
	0x09, 0xFF, 0xF5, 0x00, 0x06, 0x00, 0xFD, 0xF7, 0x03, 0xFB, 0xF8, 0xF3, 0x06, 0x04, 0x02, 0xFD, //26
	0xFE, 0xF3, 0xE3, 0x00, 0xFC, 0xF4, 0xF1, 0xE8, 0xF9, 0xEE, 0xE8, 0xE0, 0xF1, 0xFA, 0xF7, 0xEE, //27
	0x00, 0xF5, 0xE6, 0x00, 0xFE, 0xF6, 0xF3, 0xEA, 0xFB, 0xF0, 0xEA, 0xE2, 0xF5, 0xFC, 0xF9, 0xF0, //28
	0x01, 0xF6, 0xE8, 0x00, 0xFF, 0xF7, 0xF4, 0xEC, 0xFC, 0xF1, 0xEC, 0xE5, 0xF9, 0xFD, 0xFA, 0xF2, //29
	0x04, 0xF8, 0xEA, 0x00, 0x01, 0xF9, 0xF6, 0xEE, 0xFE, 0xF3, 0xEE, 0xE7, 0xFC, 0xFF, 0xFC, 0xF4, //30
	0x06, 0xFA, 0xED, 0x00, 0x03, 0xFB, 0xF8, 0xF1, 0x00, 0xF5, 0xF1, 0xEA, 0x00, 0x01, 0xFE, 0xF7, //31
	0x08, 0xFC, 0xEF, 0x00, 0x04, 0xFD, 0xFA, 0xF3, 0x01, 0xF7, 0xF3, 0xED, 0x03, 0x02, 0x00, 0xF9, //32
	0x0A, 0xFE, 0xF2, 0x00, 0x06, 0xFF, 0xFC, 0xF5, 0x03, 0xF9, 0xF5, 0xEF, 0x07, 0x04, 0x01, 0xFB, //33
	0xFD, 0xF6, 0xED, 0x00, 0xFB, 0xF7, 0xF5, 0xF0, 0xF9, 0xF3, 0xF0, 0xE9, 0xF1, 0xFB, 0xF9, 0xF5, //34
	0xFF, 0xF8, 0xEF, 0x00, 0xFD, 0xF9, 0xF7, 0xF2, 0xFB, 0xF5, 0xF2, 0xEB, 0xF4, 0xFC, 0xFB, 0xF7, //35
	0x00, 0xF9, 0xF0, 0x00, 0xFE, 0xFA, 0xF8, 0xF3, 0xFC, 0xF6, 0xF3, 0xED, 0xF8, 0xFD, 0xFC, 0xF8, //36
	0x02, 0xFB, 0xF2, 0x00, 0x00, 0xFC, 0xFA, 0xF5, 0xFE, 0xF8, 0xF5, 0xEF, 0xFB, 0xFF, 0xFE, 0xFA, //37
	0x04, 0xFD, 0xF4, 0x00, 0x02, 0xFE, 0xFC, 0xF7, 0x00, 0xFA, 0xF7, 0xF2, 0xFF, 0x01, 0x00, 0xFC, //38
	0x05, 0xFF, 0xF6, 0x00, 0x03, 0x00, 0xFE, 0xF9, 0x01, 0xFC, 0xF9, 0xF4, 0x02, 0x02, 0x01, 0xFE, //39
	0x07, 0x00, 0xF8, 0x00, 0x05, 0x01, 0x00, 0xFB, 0x03, 0xFE, 0xFB, 0xF6, 0x05, 0x03, 0x03, 0x00, //40
	0xFD, 0xF5, 0xE9, 0x00, 0xFB, 0xF5, 0xF3, 0xEC, 0xF8, 0xF1, 0xED, 0xE5, 0xF2, 0xFA, 0xF8, 0xF2, //41
	0xFF, 0xF7, 0xEB, 0x00, 0xFD, 0xF7, 0xF5, 0xEE, 0xFA, 0xF3, 0xEF, 0xE7, 0xF5, 0xFB, 0xFA, 0xF4, //42
	0x00, 0xF8, 0xED, 0x00, 0xFE, 0xF8, 0xF6, 0xF0, 0xFB, 0xF4, 0xF1, 0xE9, 0xF9, 0xFC, 0xFB, 0xF5, //43
	0x02, 0xFA, 0xEF, 0x00, 0x00, 0xFA, 0xF8, 0xF2, 0xFD, 0xF6, 0xF3, 0xEB, 0xFC, 0xFE, 0xFD, 0xF7, //44
	0x04, 0xFC, 0xF2, 0x00, 0x02, 0xFC, 0xFA, 0xF5, 0xFF, 0xF8, 0xF6, 0xEE, 0x00, 0x00, 0xFF, 0xF9, //45
	0x05, 0xFE, 0xF4, 0x00, 0x03, 0xFE, 0xFC, 0xF7, 0x00, 0xFA, 0xF8, 0xF0, 0x03, 0x01, 0x00, 0xFB, //46
	0x07, 0x00, 0xF6, 0x00, 0x05, 0x00, 0xFE, 0xF9, 0x02, 0xFC, 0xFA, 0xF2, 0x06, 0x02, 0x02, 0xFD, //47
	0xFC, 0xFC, 0xF8, 0x00, 0xFC, 0xFB, 0xFB, 0xF9, 0xFC, 0xFA, 0xF9, 0xF6, 0xF0, 0xFD, 0xFD, 0xFC, //48
	0xFE, 0xFD, 0xFA, 0x00, 0xFE, 0xFD, 0xFD, 0xFB, 0xFE, 0xFC, 0xFB, 0xF8, 0xF4, 0xFE, 0xFE, 0xFE, //49
	0x00, 0xFE, 0xFB, 0x00, 0xFF, 0xFE, 0xFE, 0xFC, 0xFF, 0xFD, 0xFC, 0xFA, 0xF8, 0xFF, 0xFF, 0xFF, //50
	0x02, 0x00, 0xFD, 0x00, 0x01, 0x00, 0x00, 0xFE, 0x01, 0xFF, 0xFE, 0xFC, 0xFB, 0x01, 0x01, 0x01, //51
	0x04, 0x02, 0xFF, 0x00, 0x03, 0x02, 0x02, 0x00, 0x03, 0x01, 0x00, 0xFF, 0xFF, 0x03, 0x03, 0x03, //52
	0x06, 0x03, 0x00, 0x00, 0x04, 0x03, 0x03, 0x01, 0x04, 0x02, 0x01, 0x00, 0x02, 0x04, 0x04, 0x04, //53
	0x08, 0x04, 0x02, 0x00, 0x06, 0x05, 0x05, 0x03, 0x06, 0x04, 0x03, 0x02, 0x06, 0x05, 0x05, 0x06, //54
	0xFE, 0xF7, 0xEE, 0x00, 0xFB, 0xF7, 0xF5, 0xF0, 0xFA, 0xF2, 0xF0, 0xEB, 0xF6, 0xFA, 0xF8, 0xF4, //55
	0x00, 0xF8, 0xEF, 0x00, 0xFD, 0xF9, 0xF6, 0xF1, 0xFB, 0xF4, 0xF1, 0xED, 0xF9, 0xFB, 0xF9, 0xF6, //56
	0x01, 0xF8, 0xF0, 0x00, 0xFD, 0xF9, 0xF6, 0xF2, 0xFB, 0xF4, 0xF2, 0xEE, 0xFC, 0xFB, 0xF9, 0xF6, //57
	0x03, 0xFA, 0xF2, 0x00, 0xFF, 0xFB, 0xF8, 0xF4, 0xFD, 0xF6, 0xF4, 0xF0, 0xFF, 0xFD, 0xFB, 0xF8, //58
	0x05, 0xFC, 0xF4, 0x00, 0x01, 0xFD, 0xFA, 0xF6, 0xFF, 0xF8, 0xF6, 0xF2, 0x02, 0xFF, 0xFD, 0xFA, //59
	0x06, 0xFD, 0xF6, 0x00, 0x01, 0xFE, 0xFB, 0xF8, 0x00, 0xF9, 0xF8, 0xF4, 0x05, 0x00, 0xFE, 0xFB, //60
	0x08, 0xFE, 0xF7, 0x00, 0x03, 0x00, 0xFC, 0xF9, 0x00, 0xFB, 0xF9, 0xF6, 0x08, 0x00, 0xFF, 0xFD, //61
	0xFF, 0xF5, 0xE8, 0x00, 0xFC, 0xF5, 0xF2, 0xEB, 0xFA, 0xF0, 0xED, 0xE4, 0xF9, 0xFA, 0xF7, 0xF1, //62
	0x00, 0xF6, 0xEA, 0x00, 0xFD, 0xF7, 0xF3, 0xED, 0xFB, 0xF2, 0xEE, 0xE6, 0xFC, 0xFB, 0xF8, 0xF3, //63
	0x02, 0xF6, 0xEB, 0x00, 0xFE, 0xF7, 0xF3, 0xEE, 0xFB, 0xF2, 0xEF, 0xE8, 0x00, 0xFB, 0xF8, 0xF3, //64
	0x05, 0xF8, 0xED, 0x00, 0x00, 0xF9, 0xF5, 0xF0, 0xFD, 0xF4, 0xF1, 0xEA, 0x03, 0xFD, 0xFA, 0xF5, //65
	0x07, 0xFA, 0xEF, 0x00, 0x02, 0xFB, 0xF7, 0xF2, 0xFF, 0xF6, 0xF3, 0xED, 0x06, 0xFF, 0xFC, 0xF7/*, //66
	0x09, 0xFB, 0xF1, 0x00, 0x03, 0xFC, 0xF8, 0xF4, 0x00, 0xF7, 0xF5, 0xEF, 0x0A, 0x00, 0xFD, 0xF8, //67
	0x0B, 0xFC, 0xF3, 0x00, 0x04, 0xFE, 0xF9, 0xF6, 0x00, 0xF9, 0xF6, 0xF1, 0x0D, 0x00, 0xFE, 0xFA, //68
	0xF8, 0xF7, 0xFA, 0x00, 0xF7, 0xF8, 0xF8, 0xF9, 0xF7, 0xF7, 0xF9, 0xF9, 0xFD, 0xFA, 0xF9, 0xF8, //69
	0xFB, 0xFD, 0xFF, 0x00, 0xFB, 0xFB, 0xFD, 0xFE, 0xFD, 0xFE, 0xFF, 0xFF, 0x00, 0xFE, 0xFC, 0xFF, //70
	0xFE, 0x00, 0x01, 0x00, 0xFE, 0x00, 0x00, 0x01, 0xFF, 0x00, 0x01, 0x00, 0x02, 0x00, 0xFF, 0x02, //71
	0xF5, 0xF6, 0xF8, 0x00, 0xF6, 0xF7, 0xF4, 0xF5, 0xF6, 0xF5, 0xF6, 0xF9, 0xEF, 0xF4, 0xF6, 0xF6, //72
	0xF7, 0xFB, 0xFD, 0x00, 0xF9, 0xFC, 0xFB, 0xFD, 0xFA, 0xFB, 0xFD, 0xFD, 0xEF, 0xF9, 0xFC, 0xFC, //73
	0xFD, 0x01, 0x02, 0x00, 0xFF, 0x02, 0x00, 0x01, 0xFF, 0x01, 0x02, 0x02, 0xF7, 0xFF, 0x02, 0x02, //74
	0xF3, 0xF3, 0xF6, 0x00, 0xF4, 0xF3, 0xF3, 0xF5, 0xF4, 0xF2, 0xF3, 0xF6, 0xF4, 0xF4, 0xF4, 0xF3, //75
	0xF6, 0xF8, 0xF9, 0x00, 0xF8, 0xF9, 0xF8, 0xF8, 0xF9, 0xF7, 0xF7, 0xFB, 0xF4, 0xF9, 0xF9, 0xF7, //76
	0xFB, 0xFC, 0xFD, 0x00, 0xFC, 0xFD, 0xFC, 0xFC, 0xFD, 0xFC, 0xFC, 0xFE, 0xF4, 0xFD, 0xFD, 0xFC, //77
	0xF3, 0xF4, 0xF6, 0x00, 0xF3, 0xF5, 0xF5, 0xF6, 0xF4, 0xF5, 0xF6, 0xF6, 0xF4, 0xF4, 0xF5, 0xF5, //78
	0xF4, 0xF9, 0xFA, 0x00, 0xF7, 0xF9, 0xF9, 0xFA, 0xF8, 0xF9, 0xFA, 0xFB, 0xF4, 0xF8, 0xFA, 0xFA, //79
	0xF6, 0xFD, 0xFD, 0x00, 0xFC, 0xFD, 0xFD, 0xFD, 0xFC, 0xFD, 0xFD, 0xFE, 0xF4, 0xFC, 0xFD, 0xFD, //80
	0xF3, 0xF3, 0xF5, 0x00, 0xF3, 0xF4, 0xF4, 0xF5, 0xF3, 0xF3, 0xF5, 0xF6, 0xF2, 0xF3, 0xF4, 0xF4, //81
	0xF5, 0xF7, 0xF9, 0x00, 0xF6, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xF7, 0xFA, 0xF2, 0xF7, 0xF8, 0xF7, //82
	0xFA, 0xFC, 0xFC, 0x00, 0xFB, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFD, 0xF2, 0xFC, 0xFC, 0xFC, //83
	0xF3, 0xF3, 0xF5, 0x00, 0xF3, 0xF3, 0xF3, 0xF4, 0xF3, 0xF2, 0xF3, 0xF5, 0xF2, 0xF4, 0xF4, 0xF2, //84
	0xF4, 0xF8, 0xF7, 0x00, 0xF6, 0xF8, 0xF7, 0xF6, 0xF7, 0xF6, 0xF5, 0xFA, 0xF2, 0xF6, 0xF8, 0xF5, //85
	0xFA, 0xFC, 0xFC, 0x00, 0xFB, 0xFC, 0xFC, 0xFB, 0xFC, 0xFB, 0xFB, 0xFD, 0xF2, 0xFB, 0xFC, 0xFB, //86
	0xF4, 0xF3, 0xF5, 0x00, 0xF3, 0xF3, 0xF3, 0xF4, 0xF4, 0xF3, 0xF3, 0xF5, 0xF4, 0xF4, 0xF4, 0xF3, //87
	0xF6, 0xF8, 0xF9, 0x00, 0xF7, 0xF8, 0xF7, 0xF7, 0xF8, 0xF6, 0xF6, 0xFA, 0xF4, 0xF8, 0xF8, 0xF7, //88
	0xFB, 0xFC, 0xFD, 0x00, 0xFC, 0xFC, 0xFC, 0xFC, 0xFC, 0xFB, 0xFB, 0xFD, 0xF4, 0xFC, 0xFC, 0xFC, //89
	0xF3, 0xF3, 0xF5, 0x00, 0xF3, 0xF3, 0xF3, 0xF4, 0xF3, 0xF2, 0xF3, 0xF5, 0xF2, 0xF4, 0xF4, 0xF2, //90
	0xF4, 0xF8, 0xF7, 0x00, 0xF6, 0xF8, 0xF7, 0xF6, 0xF7, 0xF6, 0xF5, 0xFA, 0xF2, 0xF6, 0xF8, 0xF5, //91
	0xFA, 0xFC, 0xFC, 0x00, 0xFB, 0xFC, 0xFC, 0xFB, 0xFC, 0xFB, 0xFB, 0xFD, 0xF2, 0xFB, 0xFC, 0xFB, //92
	0xF8, 0xF7, 0xFA, 0x00, 0xF7, 0xF8, 0xF8, 0xF9, 0xF7, 0xF7, 0xF9, 0xF9, 0xFD, 0xFA, 0xF9, 0xF8, //93
	0xFB, 0xFD, 0xFF, 0x00, 0xFB, 0xFB, 0xFD, 0xFE, 0xFD, 0xFE, 0xFF, 0xFF, 0x00, 0xFE, 0xFC, 0xFF, //94
	0xFE, 0x00, 0x01, 0x00, 0xFE, 0x00, 0x00, 0x01, 0xFF, 0x00, 0x01, 0x00, 0x02, 0x00, 0xFF, 0x02, //95
	0xF5, 0xF6, 0xF8, 0x00, 0xF6, 0xF7, 0xF4, 0xF5, 0xF6, 0xF5, 0xF6, 0xF9, 0xEF, 0xF4, 0xF6, 0xF6, //96
	0xF7, 0xFB, 0xFD, 0x00, 0xF9, 0xFC, 0xFB, 0xFD, 0xFA, 0xFB, 0xFD, 0xFD, 0xEF, 0xF9, 0xFC, 0xFC, //97
	0xFD, 0x01, 0x02, 0x00, 0xFF, 0x02, 0x00, 0x01, 0xFF, 0x01, 0x02, 0x02, 0xF7, 0xFF, 0x02, 0x02, //98
	0xF6, 0xF4, 0xF7, 0x00, 0xF6, 0xF4, 0xF4, 0xF6, 0xF5, 0xF4, 0xF5, 0xF8, 0xF6, 0xF4, 0xF5, 0xF4, //99
	0xFA, 0xFA, 0xFA, 0x00, 0xFA, 0xFA, 0xFA, 0xFA, 0xFA, 0xFA, 0xFA, 0xFB, 0xFA, 0xFA, 0xFB, 0xFA, //100
	0xFD, 0xFD, 0xFD, 0x00, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFE, 0xFD, 0xFD, 0xFE, 0xFD, //101
	0xF6, 0xFA, 0xFC, 0x00, 0xF7, 0xF9, 0xFB, 0xFE, 0xFA, 0xFA, 0xFB, 0xFD, 0xF4, 0xF7, 0xF9, 0xFB, //102
	0xFB, 0xFD, 0xFD, 0x00, 0xFB, 0xFC, 0xFD, 0xFE, 0xFC, 0xFD, 0xFD, 0xFE, 0xF8, 0xFC, 0xFC, 0xFD, //103
	0xFE, 0xFF, 0xFF, 0x00, 0xFE, 0xFE, 0xFF, 0xFF, 0xFE, 0xFF, 0xFF, 0xFF, 0xFB, 0xFE, 0xFE, 0xFF, //104
	0xF6, 0xF6, 0xFA, 0x00, 0xF6, 0xF6, 0xF6, 0xF9, 0xF6, 0xF6, 0xF7, 0xFA, 0xF4, 0xF6, 0xF8, 0xF6, //105
	0xFA, 0xFA, 0xFC, 0x00, 0xFA, 0xFB, 0xFB, 0xFB, 0xFA, 0xFB, 0xFB, 0xFC, 0xF8, 0xFB, 0xFB, 0xFB, //106
	0xFD, 0xFD, 0xFE, 0x00, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFE, 0xFC, 0xFD, 0xFD, 0xFD, //107
	0xF6, 0xF8, 0xF8, 0x00, 0xF6, 0xF8, 0xF8, 0xF8, 0xF8, 0xF6, 0xF7, 0xF9, 0xF6, 0xF6, 0xF8, 0xF6, //108
	0xFA, 0xFB, 0xFB, 0x00, 0xFA, 0xFB, 0xFB, 0xFB, 0xFB, 0xFB, 0xFB, 0xFC, 0xF9, 0xFA, 0xFB, 0xFB, //109
	0xFD, 0xFD, 0xFD, 0x00, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFE, 0xFD, 0xFD, 0xFD, 0xFD, //110
	0xF4, 0xF6, 0xF8, 0x00, 0xF4, 0xF6, 0xF6, 0xF9, 0xF8, 0xF6, 0xF7, 0xFA, 0xF4, 0xF6, 0xF6, 0xF6, //111
	0xFA, 0xFB, 0xFB, 0x00, 0xFA, 0xFA, 0xFB, 0xFB, 0xFB, 0xFA, 0xFB, 0xFC, 0xFA, 0xFB, 0xFB, 0xFB, //112
	0xFD, 0xFD, 0xFD, 0x00, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFE, 0xFD, 0xFD, 0xFD, 0xFD, //113
	0xF6, 0xF8, 0xF8, 0x00, 0xF6, 0xF8, 0xF8, 0xF8, 0xF8, 0xF6, 0xF7, 0xF9, 0xF6, 0xF6, 0xF8, 0xF6, //114
	0xFA, 0xFB, 0xFB, 0x00, 0xFA, 0xFB, 0xFB, 0xFB, 0xFB, 0xFB, 0xFB, 0xFC, 0xF9, 0xFA, 0xFB, 0xFB, //115
	0xFD, 0xFD, 0xFD, 0x00, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFD, 0xFE, 0xFD, 0xFD, 0xFD, 0xFD //116*/
};

AOM_RETRY_LOAD_TABLE const U8 gubYMTCEMSSLCHBRetryData1024G[HBIT_RETRY_YMTC_EMS_SLC_1024G_STEP_NUM * HBIT_RETRY_SLC_FEA_DATA_NUM] = {
	0x00,//0 for Initial Read
	0xC4,//1
	0xD8,//2
	0xEC,//3
	0x14,//4
	0x28,//5
	0x3C,//6
	0x50,//7
	0x64,//8
	0x78,//9
};
/*
 * ----------------------------------------------------------------------------
 *   Private Prototypes
 * ----------------------------------------------------------------------------
 */
INLINE void HBRetryInitRegisterRetryTable(void);
INLINE void HBRetryInitAdjustVthFPU(void);
AOM_RETRY_LOAD_TABLE NO_INLINE void HBRetryInitTable(void);
INLINE U16 YMTCEMSHBRetrySetFeatureFPU(U8 ubStep, U8 ubPageType, U8 ubSLCMode);
INLINE U16 YMTCEMSHBRetryCheckFeatureFPU(U8 ubStep, U8 ubPageType, U8 ubSLCMode);
/*
 * ----------------------------------------------------------------------------
 *   Private
 * ----------------------------------------------------------------------------
 */

U8 HBRetryGetPageType(U64 uoiFSA, U8 ubALUSel)
{
	RetryPCARuleSet_t *pRuleSet = gpOtherInfo->pRuleSet;
	U16 uwPage;
	U8 ubPageType;
	if ((ubALUSel >> 1) & BIT0) {
		//YMTC-EMS SLC
		return 0;
	}
	else {
		//YMTC-EMS QLC page type
		uwPage = (uoiFSA >> pRuleSet->pPage->ubShift[ubALUSel]) & pRuleSet->pPage->ulMask;
		if (uwPage < IM_EMS_SECTION_1_RDT) {
			ubPageType = uwPage % 3;
		}
		else if (uwPage < IM_EMS_SECTION_2_RDT) {
			ubPageType = (uwPage - IM_EMS_SECTION_1_RDT) % 4;
		}
		else if (uwPage < IM_EMS_SECTION_3_RDT) {
			ubPageType = (uwPage - IM_EMS_SECTION_2_RDT) % 3;
		}
		else if (uwPage < IM_EMS_SECTION_4_RDT) {
			ubPageType = (uwPage - IM_EMS_SECTION_3_RDT) % 4;
		}
		else if (uwPage < IM_EMS_SECTION_5_RDT) {
			ubPageType = (uwPage - IM_EMS_SECTION_4_RDT) % 3;
		}
		else {
			ubPageType = 0;
		}
		return ubPageType;
		//return ((uoiFSA >> pRuleSet->pPage->ubShift[ubALUSel]) & pRuleSet->pPage->ulMask) % 3;
	}
}

void HBRetryInitRegisterRetryTable(void)
{
	gpHBParameterArray[FPL_ADDR_TLC_RULE].pubRetryTable = (gpIDPage->ubRetryGroupCount ? ((U8 *)(YMTC_QLC_RRT_BASE)) : ((U8 *)(&gubHbitRetryData)));			// TLC
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubTotalRetryCnt = (gpIDPage->ubRetryGroupCount ? (gpIDPage->ubRetryGroupCount + 1) : HBIT_RETRY_YMTC_EMS_QLC_1024G_STEP_NUM);
	gpHBParameterArray[FPL_ADDR_SLC_RULE].pubRetryTable = (gpIDPage->ubRetryGroupCount ? ((U8 *)(YMTC_SLC_RRT_BASE)) : ((U8 *)(&gubSlcHbitRetryData)));		// SLC
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubTotalRetryCnt = (gpIDPage->ubD1RetryGroupCount ? (gpIDPage->ubD1RetryGroupCount + 1) : HBIT_RETRY_YMTC_EMS_SLC_1024G_STEP_NUM);
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubCurrentStep = HB_READ_START_STEP;
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubLastSuccessIndex = 0;
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubRetryParameterSize = (gpIDPage->ubRetryParamCount ? gpIDPage->ubRetryParamCount : HBIT_RETRY_QLC_FEA_DATA_NUM);
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubParameterNumPerFPU = (gpIDPage->ubRetryParamCount ? gpIDPage->ubRetryParamCount : HBIT_RETRY_QLC_FEA_DATA_NUM);
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubCurrentStep = HB_READ_START_STEP;
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubLastSuccessIndex = 0;
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubRetryParameterSize = (gpIDPage->ubD1RetryParamCount ? gpIDPage->ubD1RetryParamCount : HBIT_RETRY_SLC_FEA_DATA_NUM);
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubParameterNumPerFPU = (gpIDPage->ubD1RetryParamCount ? gpIDPage->ubD1RetryParamCount : HBIT_RETRY_SLC_FEA_DATA_NUM);
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubScratchTableIdx = INVALID_RETRY_TABLE_INDEX;
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubScratchTableIdx = INVALID_RETRY_TABLE_INDEX;
}

void HBRetryInitAdjustVthFPU(void)
{
	// Register set feature fpu
	gFpuEntryList.fpu_set_feature[0] = FPU_ADR_GEN;
	gFpuEntryList.fpu_set_feature[1] = FPU_CMD(0x78);
	gFpuEntryList.fpu_set_feature[2] = FPU_ADR(4);
	gFpuEntryList.fpu_set_feature[3] = FPU_DLY(0x10);
	gFpuEntryList.fpu_set_feature[4] = FPU_CMD(0xEF);

	gFpuEntryList.fpu_set_feature[27] = FPU_END;

	// Register read and compare feature data fpu
	//	gFpuEntryList.fpu_read_and_compare_feature_data[0] = FPU_ADR_GEN;
	//	gFpuEntryList.fpu_read_and_compare_feature_data[1] = FPU_CMD(0x78);
	//	gFpuEntryList.fpu_read_and_compare_feature_data[2] = FPU_ADR(3);
	//	gFpuEntryList.fpu_read_and_compare_feature_data[3] = FPU_DLY(0x10);
	//	gFpuEntryList.fpu_read_and_compare_feature_data[4] = FPU_CMD(0xEE); // BICS5 Get feature cmd
	//	gFpuEntryList.fpu_read_and_compare_feature_data[5] = FPU_DLY(0x10);
	gFpuEntryList.fpu_read_and_compare_feature_data[0] = FPU_CMD(0xEE); // BICS5 Get feature cmd
	gFpuEntryList.fpu_read_and_compare_feature_data[1] = FPU_DLY(0x10);
}

U16 YMTCEMSHBRetrySetFeatureFPU(U8 ubStep, U8 ubPageType, U8 ubSLCMode)
{
	FPL_HBIT_RETRY_PARAM_STRUCT_PTR pParam = &(gpHBParameterArray[ubSLCMode]);
	U8 ubValidParameterSize = pParam->ubParameterNumPerFPU;
	U8 ubi;
	U16 uwFPUPtr;
	U16 *puwFPU;
	U8 ubFeatureData[HBIT_RETRY_QLC_FEA_DATA_NUM] = {0};
	U8 ubFPUIdx;
	//// PIO DBG  ////
	//	REG32 *pFlaReg0 = R32_FCTL_CH[0];
	//	FlaCEControl(0, 0, ENABLE);
	//	pFlaReg0[R32_FCTL_PIO_CMD] = 0xCD;
	//	FlaCEControl(0, 0, DISABLE);
	//// PIO DBG  ////

	if (ubStep < pParam->ubTotalRetryCnt) { //Prevent Access Array Over flow
		U16 uwTableOffset = ubStep * pParam->ubRetryParameterSize;
		// Copy set feature from HB retry table
		memcpy((void *)ubFeatureData, (void *)&pParam->pubRetryTable[uwTableOffset], ubValidParameterSize);
	}
#if (RETRY_HB_FEEDBACK_CACULATE_READ_LEVEL_TO_LRU)
	// Check if CurrentStep is "SB Pass Read Level", ubTotalRetryCnt = RR_Table + Scratch_Table
	else {
		U8 ubReadLevelTableIdx = ubStep - gpHBParameterArray[FPL_ADDR_TLC_RULE].ubTotalRetryCnt; // Get Node Idx
		RETRY_HB_LRU_FEEDBACK_READ_LEVEL_TABLE_STRUCT_PTR pFeedBackReadLevelTable = M_RETRY_GET_FEEDBACK_CACULATE_READ_LEVEL_TABLE(gHBParamMgr.ubSLCMode, gHBParamMgr.ubGlobalDie, gHBParamMgr.ubPageType);
		U8 ubValidIdx = 0;
		for (U8 ubi = 0; ubi < ubValidParameterSize; ubi++) {
			if (ubPageType == M_RETRY_GET_PAGE_TYPE_BY_READ_RETRY_TABLE_PARAMETER_IDX(ubi)) {
				ubFeatureData[ubi] = pFeedBackReadLevelTable[ubReadLevelTableIdx].ubSBPassReadLevelTable[ubValidIdx]; // Same Page Type Read Level Can't Cross Two Round(89/8A).
				++ubValidIdx;
			}
		}
	}
#endif /* (RETRY_HB_FEEDBACK_CACULATE_READ_LEVEL_TO_LRU) */


	// Get FPU PTR
	uwFPUPtr = FPU_PTR_OFFSET(fpu_set_feature);
	puwFPU = (U16 *)(IRAM_BASE + uwFPUPtr);

	puwFPU += 5;
	ubFPUIdx = 0;
	if (ubSLCMode) {
		puwFPU[ubFPUIdx++] = FPU_ADR_1B(0xB0);
	}
	else {
		puwFPU[ubFPUIdx++] = FPU_ADR_1B(0xA0 + ubPageType);
	}

	// Delay (tADL)
	puwFPU[ubFPUIdx++] = FPU_DLY(0x10);

	for (ubi = 0; ubi < ubValidParameterSize; ++ubi) {
		if (ubSLCMode || ubPageType == M_RETRY_GET_PAGE_TYPE_BY_READ_RETRY_TABLE_PARAMETER_IDX(ubi)) {
			// Set feature data
			puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(ubFeatureData[ubi]);
			M_HB_DEBUG_UART("\nData[%d]=[%x]", ubi, ubFeatureData[ubi]);
		}
	}
	// FPU end
	puwFPU[ubFPUIdx++] = FPU_END;
	return uwFPUPtr;
}

U16 YMTCEMSHBRetryCheckFeatureFPU(U8 ubStep, U8 ubPageType, U8 ubSLCMode)
{
	FPL_HBIT_RETRY_PARAM_STRUCT_PTR pParam =  &(gpHBParameterArray[ubSLCMode]);
	U8 ubValidParameterSize = pParam->ubParameterNumPerFPU;
	U8 ubi;
	U16 uwFPUPtr;
	U16 *puwFPU;
	U8 ubFeatureData[HBIT_RETRY_QLC_FEA_DATA_NUM] = {0};
	U8 ubFPUIdx;

	if (ubStep < pParam->ubTotalRetryCnt) { //Prevent Access Array Over flow
		U16 uwTableOffset = ubStep * pParam->ubRetryParameterSize;
		// Copy set feature from HB retry table
		memcpy((void *)ubFeatureData, (void *)&pParam->pubRetryTable[uwTableOffset], ubValidParameterSize);
	}
#if (RETRY_HB_FEEDBACK_CACULATE_READ_LEVEL_TO_LRU)
	// Check if CurrentStep is "SB Pass Read Level", ubTotalRetryCnt = RR_Table + Scratch_Table
	else {
		U8 ubReadLevelTableIdx = ubStep - gpHBParameterArray[FPL_ADDR_TLC_RULE].ubTotalRetryCnt; // Get Node Idx
		RETRY_HB_LRU_FEEDBACK_READ_LEVEL_TABLE_STRUCT_PTR pFeedBackReadLevelTable = M_RETRY_GET_FEEDBACK_CACULATE_READ_LEVEL_TABLE(gHBParamMgr.ubSLCMode, gHBParamMgr.ubGlobalDie, gHBParamMgr.ubPageType);
		U8 ubValidIdx = 0;
		for (U8 ubi = 0; ubi < ubValidParameterSize; ubi++) {
			if (ubPageType == M_RETRY_GET_PAGE_TYPE_BY_READ_RETRY_TABLE_PARAMETER_IDX(ubi)) {
				ubFeatureData[ubi] = pFeedBackReadLevelTable[ubReadLevelTableIdx].ubSBPassReadLevelTable[ubValidIdx]; // Same Page Type Read Level Can't Cross Two Round(89/8A).
				++ubValidIdx;
			}
		}
	}
#endif /* (RETRY_HB_FEEDBACK_CACULATE_READ_LEVEL_TO_LRU) */

	// Get FPU PTR
	uwFPUPtr = FPU_PTR_OFFSET(fpu_read_and_compare_feature_data);
	puwFPU = (U16 *)(IRAM_BASE + uwFPUPtr);

	// Get parameter cmd, already fill FPU[0] in init state
	puwFPU += 2;

	ubFPUIdx = 0;
	if (ubSLCMode) {
		puwFPU[ubFPUIdx++] = FPU_ADR_1B(0xB0);
	}
	else {
		puwFPU[ubFPUIdx++] = FPU_ADR_1B(0xA0 + ubPageType);
	}
	//M_HB_DEBUG_UART("\n Addr=%x", puwFPU[ubFPUIdx - 1] & 0xFF);
	puwFPU[ubFPUIdx++] = FPU_CMD_DQS(0x70);
	puwFPU[ubFPUIdx++] = FPU_DLY(0x40);
	puwFPU[ubFPUIdx++] = FPU_POL_MASK(0xE0);
	puwFPU[ubFPUIdx++] = FPU_CMD(0x00);
	puwFPU[ubFPUIdx++] = FPU_DLY(0x40);

	for (ubi = 0; ubi < ubValidParameterSize; ++ubi) {
		if (ubSLCMode || ubPageType == M_RETRY_GET_PAGE_TYPE_BY_READ_RETRY_TABLE_PARAMETER_IDX(ubi)) {
			// Get feature data
			puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(ubFeatureData[ubi]);
			M_HB_DEBUG_UART("\nData[%d]=[%x]", ubi, ubFeatureData[ubi]);
			puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);
		}
	}
	// FPU end
	puwFPU[ubFPUIdx++] = FPU_END;

	return uwFPUPtr;

}

/*
 * ------------------------------------
 * 		Over Wirte Weak Function
 * ------------------------------------
 */
void HBRetryInitParameter(void)
{
	//UartPrintf("\r\n[HB MakerCode %x, Process Type%x]", gpOtherInfo->ubMakerCode, gpOtherInfo->ubProcess);
	M_FW_ASSERT(ASSERT_RETRY_0x0871, (ID_YMTC == gpOtherInfo->ubMakerCode) && (RETRY_YMTC_FLASH_PROCESS_EMS_1024G == gpOtherInfo->ubProcess));
	HBRetryInitRegisterRetryTable();
	HBRetryInitAdjustVthFPU();
	if (gpIDPage->ubRetryGroupCount) {
		//Do not do HBRetryInitTable();
	}
	else {
		HBRetryInitTable();
	}
}

void HBRetryInitTable(void)
{
	U8 ubHBRetryDataStepNum = HBIT_RETRY_YMTC_EMS_QLC_1024G_STEP_NUM;
	U8 ubSLCHBRetryDataStepNum = HBIT_RETRY_YMTC_EMS_SLC_1024G_STEP_NUM;
	memcpy((&gubHbitRetryData), (&gubYMTCEMSQLCHBRetryData1024G), (HBIT_RETRY_YMTC_EMS_QLC_1024G_STEP_NUM * HBIT_RETRY_QLC_FEA_DATA_NUM));
	memcpy((&gubSlcHbitRetryData), (&gubYMTCEMSSLCHBRetryData1024G), (HBIT_RETRY_YMTC_EMS_QLC_1024G_STEP_NUM * HBIT_RETRY_SLC_FEA_DATA_NUM));

	UartPrintf("\n\r gubHbitRetryData = %l,gubYMTCEMSQLCHBRetryData1024G = %l, HBIT_RETRY_YMTC_EMS_QLC_1024G_STEP_NUM * HBIT_RETRY_QLC_FEA_DATA_NUM = %l", sizeof(gubHbitRetryData), sizeof(gubYMTCEMSQLCHBRetryData1024G), HBIT_RETRY_YMTC_EMS_QLC_1024G_STEP_NUM * HBIT_RETRY_QLC_FEA_DATA_NUM);
	UartPrintf("\n\r pubRetryTable address = %x", gubHbitRetryData);
	UartPrintf("\n\r gubYMTCEMSQLCHBRetryData1024G address = %x", gubYMTCEMSQLCHBRetryData1024G);
	gpHBParameterArray[FPL_ADDR_TLC_RULE].pubRetryTable = (U8 *)(&gubHbitRetryData);
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubTotalRetryCnt = ubHBRetryDataStepNum;
	gpHBParameterArray[FPL_ADDR_SLC_RULE].pubRetryTable = (U8 *)(&gubSlcHbitRetryData);
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubTotalRetryCnt = ubSLCHBRetryDataStepNum;
}

void HBRetryPreconditionSetFeaturePass_Callback(U8 ubMTIdx)
{
	gHBParamMgr.ubSetFeatureDoneMTCnt++;

	if (gHBParamMgr.ubSetFeatureDoneMTCnt == gHBParamMgr.ubSetFeatureToDoMTCnt) { // All set feature MT done
		gHBParamMgr.ubGetFeatureDoneMTCnt = 0;
		if (NCS_V2_EN && gSystmAreaFWSettingFromInfoBlk.Init.Flag.btNCS2En) {
			gHBTask.ubState = RETRY_HB_STATE_SEARCH_PASS_STEP_READ;
		}
		else {
			gHBTask.ubState = RETRY_HB_STATE_CHECK_PRECONDITION; // Check set feature result
		}
	}
	else {
		gHBTask.ubState = RETRY_HB_STATE_PRECONDITION; // Execute remain set feature MT
	}
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_HB_RETRY;
}

void HBRetryPostconditionSetFeaturePass_Callback(U8 ubMTIdx)
{
	gHBParamMgr.ubSetFeatureDoneMTCnt++;

	if (gHBParamMgr.ubSetFeatureDoneMTCnt == gHBParamMgr.ubSetFeatureToDoMTCnt) {
		gHBParamMgr.ubGetFeatureDoneMTCnt = 0;
		if (NCS_V2_EN && gSystmAreaFWSettingFromInfoBlk.Init.Flag.btNCS2En) {
			gHBTask.ubState = RETRY_HB_STATE_POSTCONDITION_DONE_RESET_FLASH; // Check set feature result
		}
		else {
			gHBTask.ubState = RETRY_HB_STATE_CHECK_POSTCONDITION; // Check set feature result
		}
	}
	else {
		gHBTask.ubState = RETRY_HB_STATE_POSTCONDITION;
	}
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_HB_RETRY;
}

U16 HBRetrySelectResetCMDFPU(void)
{
	return FPU_PTR_OFFSET(fpu_entry_reset_ff);
}

U16 HBRetryPreconditionSetFeatureFPU(void)
{
	return YMTCEMSHBRetrySetFeatureFPU(gHBParamMgr.pParam->ubCurrentStep, gHBParamMgr.ubPageType, gHBParamMgr.ubSLCMode);
}

U16 HBRetryPreconditionCheckFeatureFPU(void)
{
	return YMTCEMSHBRetryCheckFeatureFPU(gHBParamMgr.pParam->ubCurrentStep, gHBParamMgr.ubPageType, gHBParamMgr.ubSLCMode);
}

U16 HBRetryPostconditionSetFeatureFPU(void)
{
	HB_LRU_TABLE_STRUCT_PTR pLRUTable = M_RETRY_GET_LRU_TABLE(gHBParamMgr.ubSLCMode, gHBParamMgr.ubGlobalDie, gHBParamMgr.ubPageType);
	return YMTCEMSHBRetrySetFeatureFPU(pLRUTable[0].param.ubTableIdx, gHBParamMgr.ubPageType, gHBParamMgr.ubSLCMode);
}

U16 HBRetryPostconditionCheckFeatureFPU(void)
{
	HB_LRU_TABLE_STRUCT_PTR pLRUTable = M_RETRY_GET_LRU_TABLE(gHBParamMgr.ubSLCMode, gHBParamMgr.ubGlobalDie, gHBParamMgr.ubPageType);
	return YMTCEMSHBRetryCheckFeatureFPU(pLRUTable[0].param.ubTableIdx, gHBParamMgr.ubPageType, gHBParamMgr.ubSLCMode);
}
#endif /*((PS5013_EN) || (PS5017_EN)) && (FLASH_YMTC_EMS_QLC == FW_CATEGORY_FLASH)  && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER)*/
