#ifndef _NVME_CMGR_REG_H_
#define _NVME_CMGR_REG_H_

#include "mem.h"

/*========================= START : CMGR Interrupt =========================*/

#define R32_NVME_CMGR_INT                   (0x00000000 >> 2)
#define R32_NVME_CMGR_INT_EN                (0x00000004 >> 2)

#define     CMGR_LOCK                           (BIT0)
#define     CMGR_IDLE_INT                       (BIT1)
#define     APU_R_CMD_ERR                       (BIT2)
#define     APU_W_CMD_ERR                       (BIT3)
#define     APU_R_UNC_ERR                       (BIT4)
#define     PNSRAM_PERR                         (BIT5)
#define     CTSRAM_PERR                         (BIT6)
#define     GPSRAM_PERR                         (BIT7)
#define     NLOG_BUSY_OV                   (BIT8)
#define     NLOG_RDAT_OV                  (BIT9)
#define     NLOG_RCMD_OV                   (BIT10)
#define     NLOG_WDAT_OV                   (BIT11)
#define     NLOG_WCMD_OV                   (BIT12)
#define     RCH_LCA_ERR                         (BIT13)
#define     PRP_DW_ERR                          (BIT14)
#define     CLR_CVLD_ERR                        (BIT15)

#define R8_NVME_CMGR_SRAM_INIT_DONE         (0x00000008)
#define     CMGR_SRAM_INIT_DONE                 (BIT0)


/*========================= END : CMGR Interrupt =========================*/

/*========================= START : CMGR Control =========================*/
#define R32_NVME_CMGR_CTRL_0                (0x0000000C >> 2)
#define     CMGR_CTAG_EOFST                     (BIT_MASK(9))   // BIT [8:0]


#define R8_NVME_CMGR_CTRL                   (0x00000010)
#define R32_NVME_CMGR_CTRL                  (0x00000010 >> 2)
#define     CMGR_AUTO_LOCK_EN_BIT                   (BIT0)
#define     CMGR_AES_CHK_EN_BIT                     (BIT1)
#define     CMGR_CID_SCAN_EN_BIT                    (BIT2)
#define     CMGR_IOR_ACPL_EN_BIT                    (BIT3)
#define     CMGR_SRAM_AUTO_LS_EN_BIT                (BIT4)
#define     CMGR_DB_ACTIVE_BIT                      (BIT5)
#define     CMGR_IOR_SYNC_BIT                       (BIT6)
#define     CMGR_GPSRAM_BLK_BIT                     (BIT15)
#define     CMGR_SRAM_ENTRY_THR_SHIFT               (16)
#define     CMGR_SRAM_ENTRY_THR_MASK                (BIT_MASK(8))  // BIT [23:16]
#define     CMGR_SRAM_EXIT_THR_SHIFT                (24)
#define     CMGR_SRAM_EXIT_THR_MASK                 (BIT_MASK(8))  // BIT [31:24]

#define R32_NVME_CMGR_ERR_CTRL              (0x00000014 >> 2)
#define     APU_R_CMD_ERR_LOCK_EN_BIT                (BIT0)
#define     APU_W_CMD_ERR_LOCK_EN_BIT                (BIT1)
#define     APU_R_UNC_LOCK_EN_BIT                    (BIT2)
#define     APU_SPAR_CHK_EN_BIT                      (BIT3)
#define     APU_PAR_CHK_EN_BIT                       (BIT4)
#define     RCH_LCA_CHK_BIT                          (BIT5)
#define     PRP_DW_MASK_BIT                          (BIT6)
#define     APU_DRU_ERR_INJ_BIT                      (BIT8)
#define     APU_DWU_ERR_INJ_BIT                      (BIT9)
#define     CTSRAM_ERR_INJ_BIT                       (BIT12)

#define R16_NVME_CMGR_APU_DLY_TMR           (0x00000018 >> 1)
#define R32_NVME_CMGR_APU_DLY_TMR           (0x00000018 >> 2)
#define     APU_DLY_WR_TMR                      (BITMSK(16,0))  // BIT [15:0]
#define     APU_DLY_RD_TMR                      (BITMSK(16,16)) // BIT [31:16]
#define	R16_NVME_CMGR_APU_DLY_WR_TMR		(0x00000018 >> 1)
#define	R16_NVME_CMGR_APU_DLY_RD_TMR		(0x0000001A >> 1)

#define R16_NVME_CMGR_APU_DLY_CTRL          (0x0000001C >> 1)
#define R32_NVME_CMGR_APU_DLY_CTRL          (0x0000001C >> 2)
#define     APU_DLY_UNIT                        (BITMSK(16,0))  // BIT [15:0]

#define	R8_NVME_CMGR_APU_DLY_CTRL			(0x0000001E)
#define		APU_DLY_WR_EN_BIT				    (BIT0)
#define		APU_DLY_RD_EN_BIT					(BIT1)

#define R32_NVME_CMGR_CFG0                  (0x00000020 >> 2)
#define     NVME_MPS_0                          (BITMSK(4,0))   // BIT [3:0]
#define     NVME_MPS_1                          (BITMSK(4,4))   // BIT [7:4]
#define     NVME_MPS_2                          (BITMSK(4,8))   // BIT [11:8]
#define     NVME_MPS_3                          (BITMSK(4,12))  // BIT [15:12]
#define     NVME_MPS_4                          (BITMSK(4,16))  // BIT [19:16]
#define     NVME_MPS_5                          (BITMSK(4,20))  // BIT [23:20]
#define     NVME_MPS_6                          (BITMSK(4,24))  // BIT [27:24]
#define     NVME_MPS_7                          (BITMSK(4,28))  // BIT [31:28]

#define R32_NVME_CMGR_CFG1                  (0x00000024 >> 2)
#define     NVME_MPS_8                          (BITMSK(4,0))   // BIT [3:0]
#define     NVME_MPS_9                          (BITMSK(4,4))   // BIT [7:4]
#define     NVME_MPS_10                         (BITMSK(4,8))   // BIT [11:8]
#define     NVME_MPS_11                         (BITMSK(4,12))  // BIT [15:12]
#define     NVME_MPS_12                         (BITMSK(4,16))  // BIT [19:16]
#define     NVME_MPS_13                         (BITMSK(4,20))  // BIT [23:20]
#define     NVME_MPS_14                         (BITMSK(4,24))  // BIT [27:24]
#define     NVME_MPS_15                         (BITMSK(4,28))  // BIT [31:28]

#define R8_NVME_CMGR_CFG2                   (0x00000028)
#define R32_NVME_CMGR_CFG2                  (0x00000028 >> 2)
#define     NVME_MPS_16                         (BITMSK(4,0))   // BIT [3:0]
#define R8_NVME_CMGR_MDTS                   (0x00000029)
#define     NVME_MDTS                           (BIT_MASK(8))   // BIT [8:0]

#define R32_NVME_CMGR_NLOG_MS_UNIT          (0x0000002C >> 2)

#define R32_NVME_CMGR_IDLE_TMR              (0x00000030 >> 2)

#define R32_NVME_CMGR_IDLE_TMR_THR          (0x00000034 >> 2)

#define R32_NVME_CMGR_IDLE_TMR_CTRL         (0x00000038 >> 2)
#define     CMGR_IDLE_TMR_EN_BIT                (BIT0)
#define     CMGR_IDLE_TMR_PAUSE_BIT             (BIT1)

#define R32_NVME_CMGR_NLOG_SYS_CNT          (0x00000040 >> 2)   // F800_2040h + N * 4h, (N = 0 ~ 16)

#define R32_NVME_CMGR_FW_DEL_CMD            (0x00000084 >> 2)   // F800_2084h
#define     CMGR_FW_DEL_CMD_REQ                 (BIT0)
#define     CMGR_PROC_CMD_ACT                   (BIT1)
#define     CMGR_FCF_INFO_VLD                   (BIT2)
#define     CMGR_FW_DEL_CMD_CTAG                (BITMSK(9,8))   // BIT [16:8]
#define     CMGR_FW_DEL_CMD_CTAG_SHIFT          (8)   // BIT [16:8]

#define R16_NVME_CMGR_FW_CLR_CTAG           (0x00000088 >> 1)
#define R32_NVME_CMGR_FW_CLR_CTAG           (0x00000088 >> 2)
#define		CMGR_FW_CLR_CVLD_CTAG_SHIFT			(0)
#define		CMGR_FW_CLR_CVLD_CTAG_MASK			(BIT_MASK(9))   // BIT [8:0]
#define     CMGR_FW_CLR_CVLD_REQ_BIT            (BIT9)
#define     CMGR_FW_DROP_ACT_BIT                (BIT10)

#define R32_NVME_CMGR_RST                   (0x0000008C >> 2)
#define		CMGR_APU_RST_BIT					(BIT0)
#define		CMGR_APU_PAUSE_BIT					(BIT1)
#define		CMGR_APU_LSI_RST_BIT				(BIT2)


/*========================= END : CMGR Control =========================*/

/*========================= START : CMGR Status =========================*/

#define R16_NVME_CMGR_NVME_QCNT             (0x00000090 >> 1)
#define     CMGR_NVME_QCNT                      (BITMSK(10,0))   // BIT [9:0]

#define R16_NVME_CMGR_BUSY                  (0x00000092 >> 1)
#define     CMGR_BUSY                           (BIT0)

//#define R64_NVME_CMGR_FW_GPSRAM_QW_RDATA    (0x00000098 >> 3)

#define R32_NVME_CMGR_APU_UNC_CTAG          (0x000000A0 >> 2)
#define     APU_UNC_CTAG_SHIFT                  (16)
#define     APU_UNC_CTAG_MASK					(BIT_MASK(9))
#define     APU_UNC_CTAG                        (BITMSK(9,16))  // BIT [24:16]

#define R32_NVME_CMGR_APU_UNC_LBA           (0x000000A8 >> 2)
#define R64_NVME_CMGR_APU_UNC_LBA           (0x000000A8 >> 2)
#define     APU_UNC_LBA_SHIFT                   (0)
#define     APU_UNC_LBA_MASK                    (BIT_MASK64(33))

#define R32_NVME_CMGR_APU_UNC_NSID          (0x000000AC >> 2)
#define     APU_UNC_LBA_HIGH                    (BITMSK(1,0))   // BIT [0:0]
#define     APU_UNC_NSID_SHIFT                  (1)
#define     APU_UNC_NSID_MASK                   (BIT_MASK(4))
#define     APU_UNC_NSID                        (BITMSK(4,1))   // BIT [4:1]

#define R32_NVME_CMGR_RERR_INFO             (0x000000B0 >> 2)
#define     RLS_ERR_CTAG                        (BITMSK(9,0))   // BIT [8:0]
#define     RLS_APU_PAR_ERR_BIT                 (BIT16)
#define     RLS_PRP_ERR_BIT                     (BIT17)
#define     RLS_PCIE_AXI_ERR_BIT                (BIT18)
#define     RLS_SYS_AXI_ERR_BIT                 (BIT19)
#define     RLS_E3D_ERR_BIT                     (BIT20)
#define     RLS_UNC_ERR_BIT                     (BIT21)

#define R32_NVME_CMGR_WERR_INFO             (0x000000B4 >> 2)
#define     WLS_ERR_CTAG                        (BITMSK(9,0))   // BIT [8:0]
#define     WLS_PRP_ERR_BIT                     (BIT16)
#define     WLS_PCIE_PAR_ERR_BIT                (BIT17)
#define     WLS_PCI_ERR_BIT                     (BIT18)

#define R32_NVME_CMGR_FCF_CMD_DW0           (0x000000C0 >> 2)
#define R32_NVME_CMGR_FCF_CMD_DW1           (0x000000C4 >> 2)
#define R32_NVME_CMGR_FCF_CMD_DW2           (0x000000C8 >> 2)
#define R32_NVME_CMGR_FCF_CMD_DW3           (0x000000CC >> 2)
#define R32_NVME_CMGR_FCF_CMD_DW4           (0x000000D0 >> 2)
#define R32_NVME_CMGR_FCF_CMD_DW5           (0x000000D4 >> 2)
#define R32_NVME_CMGR_FCF_CMD_DW6           (0x000000D8 >> 2)
#define R32_NVME_CMGR_FCF_CMD_DW7           (0x000000DC >> 2)
#define R32_NVME_CMGR_FCF_CMD_DW8           (0x000000E0 >> 2)
#define R32_NVME_CMGR_FCF_CMD_DW9           (0x000000E4 >> 2)
#define R32_NVME_CMGR_FCF_CMD_DW10          (0x000000E8 >> 2)
#define R32_NVME_CMGR_FCF_CMD_DW11          (0x000000EC >> 2)
#define R32_NVME_CMGR_FCF_CMD_DW12          (0x000000F0 >> 2)
#define R32_NVME_CMGR_FCF_CMD_DW13          (0x000000F4 >> 2)
#define R32_NVME_CMGR_FCF_CMD_DW14          (0x000000F8 >> 2)
#define R32_NVME_CMGR_FCF_CMD_DW15          (0x000000FC >> 2)

#define R32_NVME_CMGR_FCF_ERR_INFO          (0x00000100 >> 2)
#define		CMGR_FCF_SQID_SHIFT					(0)
#define		CMGR_FCF_SQID_MASK					(BIT_MASK(8))
#define     CMGR_FCF_SQID                       (BITMSK(8,0))   // BIT [7:0]
#define     CMGR_FCF_FID_SHIFT                  (8)
#define     CMGR_FCF_FID                        (BITMSK(5,8))   // BIT [12:8]
#define     CMD_PRP2_ERR_BIT                    (BIT16)
#define     CMD_PRP1_ERR_BIT                    (BIT17)
#define     CMD_LOCK_ERR_BIT                    (BIT18)
#define     CMD_LBA_ERR_BIT                     (BIT19)
#define     CMD_WP_ERR_BIT                      (BIT20)
#define     CMD_DSM_ERR_BIT                     (BIT21)
#define     CMD_PSDT_ERR_BIT                    (BIT22)
#define     CMD_FUSE_ERR_BIT                    (BIT23)
#define     CMD_MDTS_ERR_BIT                    (BIT24)
#define     CMD_AES_ERR_BIT                     (BIT25)
#define     CMD_CID_ERR_BIT                     (BIT26)
#define     CMD_NS_ERR_BIT                      (BIT27)
#define     PRP_FETCH_AXI_ERR_BIT               (BIT28)
#define     PRP_INVALD_OFFSET_ERR_BIT           (BIT29)
#define     CMD_NL_ERR_BIT                      (BIT30)
#define     CMD_DTYPE_ERR_BIT                   (BIT31)

#define R32_NVME_CMGR_RPF_OFST              (0x00000104 >> 2)
#define     RPF_ERR_OFST                        (BITMSK(17,0))  // BIT [16:0]

#define R32_NVME_CMGR_SRAM_PERR_ADDR        (0x00000108 >> 2)
#define     GP_SRAM_PERR_ADDR                   (BITMSK(7,0))   // BIT [6:0]
#define     PN_SRAM_PERR_ADDR                   (BITMSK(8,8))   // BIT [15:8]
#define     CT_SRAM_PERR_ADDR                   (BITMSK(10,16)) // BIT [25:16]

#define R32_NVME_CMGR_NLOG_BUSY_OV          (0x0000010C >> 2)

#define R32_NVME_CMGR_NLOG_RDAT_OV          (0x00000110 >> 2)

#define R32_NVME_CMGR_NLOG_RCMD_OV          (0x00000114 >> 2)

#define R32_NVME_CMGR_NLOG_WDAT_OV          (0x00000118 >> 2)

#define R32_NVME_CMGR_NLOG_WCMD_OV          (0x0000011C >> 2)

#define R32_NVME_CMGR_CMD_CNT               (0x00000128 >> 2)
#define     NVME_CMGR_READ_CMD_CNT              (BITMSK(10,0))   // BIT [9:0]
#define     MVME_CMGR_WRITE_CMD_CNT             (BITMSK(10,10))  // BIT [19:10]
#define     NVME_CMGR_NON_RWCMD_CNT             (BITMSK(10,20))  // BIT [29:20]

/*========================= END : CMGR Status =========================*/

/*========================= START : CMGR Directive Table =========================*/

#define R32_NVME_CMGR_DT                    (0x00000180 >> 2)
#define     DT_STRID                            (BITMSK(16,0))  // BIT [15:0]
#define     DT_NSID                             (BITMSK(5,16))  // BIT [20:16]
#define     DT_LRU                              (BITMSK(3,21))  // BIT [23:21]
#define     DT_HSTID                            (BITMSK(5,24))  // BIT [28:24]
#define     DT_GLB                              (BIT29)
#define     DT_OPEN                             (BIT30)

/*========================= END : CMGR Directive Table =========================*/

/*========================= START : CMGR General Purpose SRAM =========================*/

#define R64_NVME_CMGR_GPSRAM_WCH_PRP_ENTRY1 (0x00000800 >> 3)
#define R64_NVME_CMGR_GPSRAM_WCH_PRP_ENTRY2 (0x00000808 >> 3)

#define R8_NVME_CMGR_GPSRAM_NS_INFO         (0x00000900 >> 0)
#define R64_NVME_CMGR_GPSRAM_NS_INFO_L      (0x00000900 >> 3)
#define     GPSRAM_NS_SIZE                      (BITMSK(40,0))  // BIT [39:0]
#define     GPSRAM_NS_AFID                      (BITMSK(17,40)) // BIT [56:40]
#define     GPSRAM_NS_AFID_MASK                     (BITMSK(17,0)) // [56:40]
#define     GPSRAM_NS_AFID_SHIFT                 (40)
#define R64_NVME_CMGR_GPSRAM_NS_INFO_H      (0x00000908 >> 3)
#define     GPSRAM_NSID                         (BITMSK(6,0))   // BIT [69:64]
#define     GPSRAM_NS_LBAF_SHIFT                (8)
#define     GPSRAM_NS_LBAF_BIT                  (BIT8)          // BIT 72
#define         NS_LBAF_512B                        (0)
#define         NS_LBAF_4K                          (1)
#define     GPSRAM_NS_WP_SHIFT                  (9)
#define     GPSRAM_NS_WP_BIT                    (BIT9)          // BIT 73
#define     GPSRAM_NS_ACT_SHIFT                 (10)
#define     GPSRAM_NS_ACT_BIT                   (BIT10)         // BIT 74
#define     GPSRAM_NS_DE                        (BITMSK(17,11)) // BIT [91:75]
#define     NS_WR_LOCK                          (BITMSK(17,28)) // BIT [108:92]
#define     NS_RD_LOCK                          (BITMSK(17,45)) // BIT [125:109]

#define R8_NVME_CMGR_GPSRAM_HSTID_INFO      (0x00000B00)

#define R64_NVME_CMGR_NLOG_ENTRY_SIZE       (0x20 >> 3)
#define R64_NVME_CMGR_NLOG_RCMD_CNT         (0x00000B20 >> 3)
#define R64_NVME_CMGR_NLOG_RDAT_CNT         (0x00000B28 >> 3)
#define R64_NVME_CMGR_NLOG_WCMD_CNT         (0x00000B30 >> 3)
#define R64_NVME_CMGR_NLOG_WDAT_CNT         (0x00000B38 >> 3)
#define R64_NVME_CMGR_NLOG_BUSY_TIME_MS     (0x00000D40 >> 3)
#define R64_NVME_CMGR_NLOG_BUSY_TIME_MIN    (0x00000D48 >> 3)

/*========================= END : CMGR General Purpose SRAM =========================*/

/*========================= START : CMGR PNRAM =========================*/

#define R64_NVME_CMGR_PNSRAM_RD_PRP_ENTRY1  (0x00001000 >> 3)
#define R64_NVME_CMGR_PNSRAM_RD_PRP_ENTRY2  (0x00001008 >> 3)

/*========================= END : CMGR PNRAM =========================*/

/*========================= START : CMGR Command Table SRAM =========================*/
#define	NVME_CMGR_CMD_TABLE_SRAM_ENTRY_SIZE		(0x40)
#define CTSRAM_CMD_OFFSET_DW                    (NVME_CMGR_CMD_TABLE_SRAM_ENTRY_SIZE >> 2)
#define CTSRAM_CMD_OFFSET_QW                    (NVME_CMGR_CMD_TABLE_SRAM_ENTRY_SIZE >> 3)

#define R32_NVME_CMGR_CTSRAM_CMD_INFO       (0x00006000 >> 2)
#define R64_NVME_CMGR_CTSRAM_CMD_INFO       (0x00006000 >> 3)
#define CTSRAM_DW0                          (0x6000 >> 2)
#define     CTSRAM_OPC_SHIFT                    (0)
#define     CTSRAM_OPC_MASK                     (BIT_MASK(8))
#define     CTSRAM_OPC                          (BITMSK(8,0))   // BIT [7:0]
#define     CTSRAM_FUSE_SHIFT                   (8)
#define     CTSRAM_FUSE_MASK                    (BIT_MASK(2))
#define     CTSRAM_FUSE                         (BITMSK(2,8))   // BIT [9:8]
#define     CTSRAM_PSDT_SHIFT                   (14)
#define     CTSRAM_PSDT_MASK                    (BIT_MASK(2))
#define     CTSRAM_PSDT                         (BITMSK(2,14))  // BIT [15:14]
#define     CTSRAM_CID_SHIFT                    (16)
#define     CTSRAM_CID_MASK                     (BIT_MASK(16))
#define     CTSRAM_CID                          (BITMSK(16,16)) // BIT [31:16]
#define CTSRAM_DW1                          (0x6004 >> 2)
#define     CTSRAM_NSID_SHIFT                   (0)
#define     CTSRAM_NSID                         (BITMSK(32,0)) // BIT [64:32]

#define R32_NVME_CMGR_CTSRAM_CMD_STS        (0x00006008 >> 2)
#define R64_NVME_CMGR_CTSRAM_CMD_STS        (0x00006008 >> 3)
#define CTSRAM_DW2                          (0x6008 >> 2)
#define     CTSRAM_VLD_BIT                      (BIT64(0))
#define     CTSRAM_CMD_TYPE_SHIFT               (1)
#define     CTSRAM_CMD_TYPE                     (BITMSK(2,1))   // BIT [2:1]
#define         CMD_TYPE_WRITE                      (1)
#define         CMD_TYPE_READ                       (2)
#define     CTSRAM_CMD_STS_SHIFT                (3)
#define     CTSRAM_CMD_STS                      (BITMSK(2,3))   // BIT [4:3]
#define         PUSH_CQ_DONE                        (3)         // push CQ with error
#define     CTSRAM_PT_SHIFT                     (5)
#define     CTSRAM_PT_BIT                       (BIT64(5))
#define		CTSRAM_ERR_SHIFT					(6)
#define     CTSRAM_ERR_BIT                      (BIT64(6))
#define     CTSRAM_ACPL_BIT                     (BIT64(7))
#define     CTSRAM_FID                          (BITMSK(5,8))   // BIT [12:8]
#define     CTSRAM_FID_SHIFT                    (8)
#define     CTSRAM_LBAF_SHIFT                   (15)
#define     CTSRAM_LBAF_BIT                     (BIT64(15))
#define         LBAF_512                        (0)
#define         LBAF_4K                         (1)
#define     CTSRAM_PNS                          (BITMSK(6,16))  // BIT [21:16]
#define     CTSRAM_LPN_BIT                      (BIT64(22))
#define     CTSRAM_PNV_BIT                      (BIT64(23))
#define     CTSRAM_SSD_NSID_SHIFT               (24)
#define     CTSRAM_SSD_NSID                     (BITMSK(5,24))  // BIT [26:24] sure!?
#define     CTSRAM_ADM_BIT                      (BIT64(31))
#define CTSRAM_DW3                          (0x600C >> 2)
#define     CTSRAM_PNIDX_SHIFT                  (0)
#define     CTSRAM_PNIDX                        (BITMSK(8,0))  // BIT [39:32]
#define     CTSRAM_SQID_SHIFT                   (8)
#define     CTSRAM_SQID                         (BITMSK(8,8))  // BIT [47:40]
#define     CTSRAM_CID_STS_SHIFT                (16)
#define     CTSRAM_CID_STS                      (BITMSK(16,16)) // BIT [63:48]  // duplicate CTSRAM_CID in NVME_CMGR_CTSRAM_CMD_INFO

#define R64_NVME_CMGR_CTSRAM_RLBN           (0x00006010 >> 3)
#define CTSRAM_DW4                          (0x6010 >> 2)
#define		CTSRAM_Z0_RLBN_SHIFT				(0)
#define     CTSRAM_Z0_RLBN                      (BITMSK(17,0))  // BIT [16:0]
#define		CTSRAM_W_RLBN_SHIFT				    (0)
#define     CTSRAM_W_RLBN                       (BITMSK(17,0))  // BIT [16:0], same as Z0_RLBN
#define CTSRAM_DW5                          (0x6014 >> 2)
#define		CTSRAM_Z1_RLBN_SHIFT				(0)
#define		CTSRAM_Z1_RLBN_MASK					(BIT_MASK(17))
#define     CTSRAM_Z1_RLBN                      (BITMSK(17,0)) // BIT [48:32]

#define R64_NVME_CMGR_CTSRAM_PRP1           (0x00006018 >> 3)
#define CTSRAM_DW6                          (0x6018 >> 2)
#define CTSRAM_DW7                          (0x601C >> 2)

#define R64_NVME_CMGR_CTSRAM_PRP2           (0x00006020 >> 3)
#define CTSRAM_DW8                          (0x6020 >> 2)
#define CTSRAM_DW9                          (0x6024 >> 2)

#define R64_NVME_CMGR_CTSRAM_SLBA           (0x00006028 >> 3)
#define CTSRAM_DW10                         (0x6028 >> 2)
#define CTSRAM_DW11                         (0x602C >> 2)

#define R64_NVME_CMGR_CTSRAM_CMD_SPEC       (0x00006030 >> 3)
#define CTSRAM_DW12                         (0x6030 >> 2)
#define		CTSRAM_NLB_SHIFT					(0)
#define		CTSRAM_NLB_MASK						(BIT_MASK(16))
#define     CTSRAM_NLB                          (BITMSK(16,0))  // BIT [15:0]
#define		CTSRAM_DTYPE_SHIFT					(20)
#define		CTSRAM_DTYPE_MASK					(BIT_MASK(4))
#define     CTSRAM_DTYPE                        (BITMSK(4,20))  // BIT [23:20]
#define		CTSRAM_PRINFO_SHIFT					(26)
#define		CTSRAM_PRINFO_MASK					(BIT_MASK(4))
#define     CTSRAM_PRINFO                       (BITMSK(4,26)) //RSV in HW spec...
#define     CTSRAM_FUA_SHIFT                    (30)
#define     CTSRAM_FUA_BIT                      (BIT64(30))
#define     CTSRAM_LR_SHIFT                     (31)
#define     CTSRAM_LR_BIT                       (BIT64(31))
#define CTSRAM_DW13                         (0x6034 >> 2)
#define		CTSRAM_DSM_SHIFT					(0)
#define		CTSRAM_DSM_MASK						(BIT_MASK(8))
#define     CTSRAM_DSM                          (BITMSK(8,0))  // BIT [39:32]
#define		CTSRAM_DSPEC_SHIFT					(16)
#define		CTSRAM_DSPEC_MASK					(BIT_MASK(16))
#define     CTSRAM_DSPEC                        (BITMSK(16,16))  // BIT [63:48]

#define R64_NVME_CMGR_CTSRAM_QW7            (0x00006038 >> 3)
#define CTSRAM_DW14                         (0x6038 >> 2)
#define CTSRAM_DW15                         (0x603C >> 2)

/*========================= END : CMGR Command Table SRAM =========================*/

/*========================= START : CMGR Debug Port =========================*/
#define R32_NVME_CMGR_LCA_MGR_DB_PORT       (0x00000148 >> 2)
#define     CMGR_WRITE_PRP_DEBUG1			    (BIT12)
#define     CMGR_WRITE_PRP_DEBUG2				(BIT21)
#define     DMA_CNT                             (BITMSK(8,0))   // [3:0]: Write LCA cnt, [7:4]: Read LCA cnt
#define     DMA_IDLE_BIT                        (BIT30)

#define R32_NVME_CMGR_DEBUG_PORT_0X120          (0x0150 >> 2)
#define     READ_DMA_UPDATE_CMD_TABLE_IDLE_BIT      (BIT28)

#define R32_NVME_CMGR_LCA_MGR_DB_PORT2      (0x0000014C >> 2)   // F800_214Ch
#define     DMAT_STATE                          (BITMSK(4,0))

#define R32_NVME_CMGR_CFMGR_DB_PORT         (0x00000158 >> 2)
#define     CFMGR_IDLE_BIT                      (BIT0)
#define     CFMGR_REMPTY_BIT                    (BIT24)

#define R32_NVME_CMGR_LCA_MGR_DB_PORT3      (0x0000015C >> 2)
#define     CMGR_READ_PRP_DEBUG1_BIT            (BIT5)

#define	R32_NVME_CMGR_CFMGR_DB_PORT2		(0x00000164 >> 2)
#define     CMGR_PARSING_CMD_IDLE_BIT           (BIT0)

#define	R32_NVME_CMGR_MGR_DB_PORT		    (0x00000174 >> 2)
#define     CMGR_PNSRAM_FULL_BIT                (BIT4)

/*========================= END : CMGR Debug Port =========================*/

#endif /* _NVME_CMGR_REG_H_ */
