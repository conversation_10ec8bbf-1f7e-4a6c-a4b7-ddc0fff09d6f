/*
 * shr_hal_nvme_persisten_event_log_api.h
 *
 *  Created on: 20191105
 *      Author: jacky_li
 */

#ifndef SRC_FW_NVME_API_NVME_SHR_HAL_NVME_PERSISTENT_LOG_API_H_
#define SRC_FW_NVME_API_NVME_SHR_HAL_NVME_PERSISTENT_LOG_API_H_

#include "common/typedef.h"
#include "aom/aom_api.h"
#include "hal/bmu/bmu_api.h"
#include "nvme_api/nvme/shr_hal_nvme.h"

#define PERSISTENT_LOG_POWER_ON_POR	(1)
#define PERSISTENT_LOG_HW_ERROR_08H	(2)

#define PERSISTENT_LOG_DEFAULT_ADD_MODE		(1)
#define PERSISTENT_LOG_FORCE_SAVE_FLASH_MODE	(2)

#define PERSISTENT_LOG_EXTEND_LCA_SIZE			(0x4000000)	//64MB
#define PERSISTENT_LOG_SUPPORT_BYTE_SIZE		(0x200000) //2MB
#define PERSISTENT_LOG_BUFFER_SIZE		(0x1000) //4K
#define PERSISTENT_LOG_RESERVE_BYTE_FOR_SPARE (0x20) //32B
#define PERSISTENT_LOG_SPARE_OFFSET	(PERSISTENT_LOG_BUFFER_SIZE - PERSISTENT_LOG_RESERVE_BYTE_FOR_SPARE)
#define PERSISTENT_LOG_SPARE_ACCUMULATE_BYTE_OFFSET		(PERSISTENT_LOG_SPARE_OFFSET)
#define PERSISTENT_LOG_SPARE_ACCUMULATE_BYTE_SIZE			(0x04) //4B
#define PERSISTENT_LOG_SPARE_ACCUMULATE_LOG_CNT_OFFSET	(PERSISTENT_LOG_SPARE_OFFSET + PERSISTENT_LOG_SPARE_ACCUMULATE_BYTE_SIZE)
#define PERSISTENT_LOG_SPARE_ACCUMULATE_LOG_CNT_SIZE		(0x04) //4B
#define PERSISTENT_LOG_SPARE_BUFFER_BYTE_OFFSET			(PERSISTENT_LOG_SPARE_ACCUMULATE_LOG_CNT_OFFSET + PERSISTENT_LOG_SPARE_ACCUMULATE_LOG_CNT_SIZE)
#define PERSISTENT_LOG_SPARE_BUFFER_BYTE_SIZE			(0x02) //2B
#define PERSISTENT_LOG_SPARE_BUFFER_LOG_CNT_OFFSET 		(PERSISTENT_LOG_SPARE_BUFFER_BYTE_OFFSET + PERSISTENT_LOG_SPARE_BUFFER_BYTE_SIZE)
#define PERSISTENT_LOG_SPARE_BUFFER_LOG_CNT_SIZE			(0x02) //2B
#define PERSISTENT_LOG_SPARE_BUFFER_LCA_OFFSET			(PERSISTENT_LOG_SPARE_BUFFER_LOG_CNT_OFFSET + PERSISTENT_LOG_SPARE_BUFFER_LOG_CNT_SIZE)
#define PERSISTENT_LOG_SPARE_BUFFER_LCA_SIZE			(0x04)

#define PERSISTENT_LOG_WRITE_BUFFER_READY			(BIT0)
#define PERSISTENT_LOG_GET_LOG_DOING				(BIT1)
#define PERSISTENT_LOG_WAIT_BUFFER_FLUSH			(BIT2)
#define PERSISTENT_LOG_SPEED_CHANGE_ADD			(BIT3)
#define PERSISTENT_LOG_LINK_F_ADD					(BIT4)
#define PERSISTENT_LOG_WAIT_ACCLOCATE_WRITE_BUFFER	(BIT5)

#define PERSISTENT_LOG_ROUTINE_EVENT			(PERSISTENT_LOG_WAIT_BUFFER_FLUSH | PERSISTENT_LOG_SPEED_CHANGE_ADD | PERSISTENT_LOG_LINK_F_ADD)

typedef struct PersistentLogVariable {
	U32 ulPersistentLogBase;
	U32 ulInsertLogIdx;
	U16 uwFlushIdx;
	U16 uwTotalByte;
	U16 uwState;
	U16 uwWritePbOffset;

	U8 ubGetLogEstablishContext;  //0 = not establish context
	U8 ubEmpty;
	U16 uwGetLogStatus;
	U16 uwCurrentByteInBuffer;
	U16 uwCurrentEventCountInBuffer;
	U32 ulCurrentLPOL;

	U8 ubLCARingBack;
	U32 ulCurrentLCA;
	U32 ulLCAHead;
	U32 ulLCATail;
	U32 ulLinkStatusReg;
	U64 uoSpeedChangeTimeStamp; //HW_ERROR_04H
	U64 uoLinkFTimeStamp;	      //HW_ERROR_05H

} PersistentLogVariable_t;

extern PersistentLogVariable_t gPersistentLogVariable;

#define M_PERSISTENT_LOG_SET_SPARE_ACCUMULATED_BYTE(ulBaseAddr , x)		(((volatile U32 *)ulBaseAddr)[PERSISTENT_LOG_SPARE_ACCUMULATE_BYTE_OFFSET>> 2] = (x))
#define M_PERSISTENT_LOG_GET_SPARE_ACCUMULTED_BYTE(ulBaseAddr)		(((volatile U32 *)ulBaseAddr)[PERSISTENT_LOG_SPARE_ACCUMULATE_BYTE_OFFSET>> 2])
#define M_PERSISTENT_LOG_SET_SPARE_ACCUMULATED_LOG_CNT(ulBaseAddr , x)	(((volatile U32 *)ulBaseAddr)[PERSISTENT_LOG_SPARE_ACCUMULATE_LOG_CNT_OFFSET>> 2] = (x))
#define M_PERSISTENT_LOG_GET_SPARE_ACCUMULATED_LOG_CNT(ulBaseAddr)		(((volatile U32 *)ulBaseAddr)[PERSISTENT_LOG_SPARE_ACCUMULATE_LOG_CNT_OFFSET>> 2])
#define M_PERSISTENT_LOG_SET_SPARE_BUFFER_BYTE(ulBaseAddr , x)			(((volatile U16 *)ulBaseAddr)[PERSISTENT_LOG_SPARE_BUFFER_BYTE_OFFSET>> 1] = (x))
#define M_PERSISTENT_LOG_GET_SPARE_BUFFER_BYTE(ulBaseAddr)				(((volatile U16 *)ulBaseAddr)[PERSISTENT_LOG_SPARE_BUFFER_BYTE_OFFSET>> 1])
#define M_PERSISTENT_LOG_SET_SPARE_BUFFER_LOG_CNT(ulBaseAddr , x)		(((volatile U16 *)ulBaseAddr)[PERSISTENT_LOG_SPARE_BUFFER_LOG_CNT_OFFSET>> 1] = (x))
#define M_PERSISTENT_LOG_GET_SPARE_BUFFER_LOG_CNT(ulBaseAddr)			(((volatile U16 *)ulBaseAddr)[PERSISTENT_LOG_SPARE_BUFFER_LOG_CNT_OFFSET>> 1])
#define M_PERSISTENT_LOG_SET_SPARE_BUFFER_LCA(ulBaseAddr , x)		(((volatile U32 *)ulBaseAddr)[PERSISTENT_LOG_SPARE_BUFFER_LCA_OFFSET>> 2] = (x))
#define M_PERSISTENT_LOG_GET_SPARE_BUFFER_LCA(ulBaseAddr)			(((volatile U32 *)ulBaseAddr)[PERSISTENT_LOG_SPARE_BUFFER_LCA_OFFSET>> 2])

INLINE void PersistentLogUpdateInfo(void)
{
	M_PERSISTENT_LOG_SET_SPARE_BUFFER_BYTE(gPersistentLogVariable.ulPersistentLogBase, gPersistentLogVariable.uwCurrentByteInBuffer);
	M_PERSISTENT_LOG_SET_SPARE_BUFFER_LOG_CNT(gPersistentLogVariable.ulPersistentLogBase, gPersistentLogVariable.uwCurrentEventCountInBuffer);
	M_PERSISTENT_LOG_SET_SPARE_BUFFER_LCA(gPersistentLogVariable.ulPersistentLogBase, gPersistentLogVariable.ulCurrentLCA);
}

AOM_NRW_2 void PersistentLogInit(U8 ubPowerOnType);
AOM_NRW_2 void PersistentLogInitVariable(void);
void PersistentLogAllocateWritePB_Callback(BMUCmdResult_t *pBmuCmdResult);
AOM_NRW_2 void PersistenLogAllocateWritePB(void);
AOM_NRW_2 void PersistentLogUpdateInUseLCA(void);
AOM_NRW_2 void PersistentLogFlushCurrentBuffer(void);
AOM_NRW_2 U8 PersistentLogCheckBufferSizeEnough(U16 uwPayloadSize);
AOM_NRW_2 void PersistentLogAdd(U16 uwPayloadSize, U8 ubAddMode);
AOM_NRW_2 void PersistentLogAddSpeedChangeLog(void);
AOM_NRW_2 void PersistentLogAddLinkFallingLog(void);
AOM_NRW_2 void PersistentLogCloseRead(void);
AOM_NRW_2 void PersistentLogRoutine(void);

AOM_NRW_3 void PersistentLogGetLogInit(U64 uoNUMDU, U64 uoLPOUL);
void PersistentLogAllocateTempPB_Callback(BMUCmdResult_t *pBmuCmdResult);
AOM_NRW_3 void PersistentLogCopyData(U32 ulSrcAddr, U16 uwSrcRemainSize, U32 ulTargetAddr);
AOM_NRW_3 void PersistentLogFillBuffer(OPT_HCMD *pulCurrentCMD);
#endif /* SRC_FW_NVME_API_NVME_SHR_HAL_NVME_PERSISTENT_LOG_API_H_ */
