#ifndef _S17_SPI_REG_H_
#define _S17_SPI_REG_H_

#include "mem.h"

#define	R8_SPI				((REG8 *) 	(SPI_REG_ADDRESS))
#define	R16_SPI				((REG16 *) 	(SPI_REG_ADDRESS))
#define	R32_SPI				((REG32 *) 	(SPI_REG_ADDRESS))
#define	R64_SPI				((REG64 *) 	(SPI_REG_ADDRESS))

//*************

#define R32_SPI_CFG                        (0x00 >> 2)
#define     DMA_READ_WRITE_DIRECTION        (BIT0) // 0: spi read   1: spi write  default(0)
#define     COMMAND_DUAL                    (BIT1)
#define     DATA_DUAL                       (BIT2)
#define     ADDRESS_DUAL                    (BIT3)
#define     SPI_CPHA                        (BIT4)
#define     SPI_CPOL                        (BIT5)
#define     SPI_CS_N_OUTPUT_VALUE           (BIT6)
#define     SPI_CS_N_SWITCH                 (BIT7) // 0: Control by IP   1: Control by FW  default(0)
#define     DUMMY_BYTE_ENABLE               (BIT8)
#define     IO_LATCH_POLARITY               (BIT9)
#define     SPI_CS_HIGH_CNT                 ((U32)0x0000F000)
#define     SCR_DISABLE                     (BIT24)
#define     SCR_RST                         (BIT25)
#define     DOUBLE_BUFFER_RESET             (BIT28)
#define R32_SPI_START_ADDR                 (0x04 >> 2)
#define     SPI_START_ADDR_SET(X)           (R32_SPI[R32_SPI_START_ADDR] = (X & 0x00FFFFFF))
#define R32_SPI_DMA_BCNT                   (0x08 >> 2)
#define     SPI_DMA_BCNT_SET(X)             (R32_SPI[R32_SPI_DMA_BCNT] = (X & 0x00FFFFFF))
#define R32_SPI_PAGE_SIZE                  (0x0C >> 2)
#define     SPI_PAGE_SIZE_SET(X)            (R32_SPI[R32_SPI_PAGE_SIZE] = (X & 0x00000FFF))
#define R32_SPI_DMA_RAM_ADDR               (0x10 >> 2)
#define     SPI_DMA_RAM_ADDR_SET(X)         (R32_SPI[R32_SPI_DMA_RAM_ADDR] = (X & 0x00000FFF))
#define R32_SPI_CMD_LEN                    (0x14 >> 2)
#define     SPI_COMMAND_1_LENGTH_SET(X)     (R32_SPI[R32_SPI_CMD_LEN] |= ((X << 0) & 0x0000000F))
#define     SPI_COMMAND_2_LENGTH_SET(X)     (R32_SPI[R32_SPI_CMD_LEN] |= ((X << 4) & 0x000000F0))
#define     SPI_COMMAND_3_LENGTH_SET(X)     (R32_SPI[R32_SPI_CMD_LEN] |= ((X << 8) & 0x00000F00))
#define     SPI_COMMAND_4_LENGTH_SET(X)     (R32_SPI[R32_SPI_CMD_LEN] |= ((X << 12) & 0x0000F000))
#define     SPI_START_ADDRESS_LENGTH_SET(X) (R32_SPI[R32_SPI_CMD_LEN] |= ((X << 16) & 0x000F0000))
#define R32_SPI_CMD_1                      (0x18 >> 2)
#define R32_SPI_CMD_2                      (0x1C >> 2)
#define R32_SPI_CMD_3                      (0x20 >> 2)
#define R32_SPI_CMD_4                      (0x24 >> 2)
#define R32_SPI_CMD_8765                   (0x28 >> 2)
#define     SPI_COMMAND_5_SET(X)            (R32_SPI[R32_SPI_CMD_8765] |= ((X << 0) & 0x000000FF))
#define     SPI_COMMAND_6_SET(X)            (R32_SPI[R32_SPI_CMD_8765] |= ((X << 8) & 0x0000FF00))
#define     SPI_COMMAND_7_SET(X)            (R32_SPI[R32_SPI_CMD_8765] |= ((X << 16) & 0x00FF0000))
#define     SPI_COMMAND_8_SET(X)            (R32_SPI[R32_SPI_CMD_8765] |= ((X << 24) & 0xFF000000))
#define R32_SPI_CMD_ORDER_L                (0x2C >> 2)
#define     SPI_CMD_ORDER_8_CLEAR()         (R32_SPI[R32_SPI_CMD_ORDER_L] &= (0x0FFFFFFF))
#define     SPI_CMD_ORDER_7_CLEAR()         (R32_SPI[R32_SPI_CMD_ORDER_L] &= (0xF0FFFFFF))
#define     SPI_CMD_ORDER_6_CLEAR()         (R32_SPI[R32_SPI_CMD_ORDER_L] &= (0xFF0FFFFF))
#define     SPI_CMD_ORDER_5_CLEAR()         (R32_SPI[R32_SPI_CMD_ORDER_L] &= (0xFFF0FFFF))
#define     SPI_CMD_ORDER_4_CLEAR()         (R32_SPI[R32_SPI_CMD_ORDER_L] &= (0xFFFF0FFF))
#define     SPI_CMD_ORDER_3_CLEAR()         (R32_SPI[R32_SPI_CMD_ORDER_L] &= (0xFFFFF0FF))
#define     SPI_CMD_ORDER_2_CLEAR()         (R32_SPI[R32_SPI_CMD_ORDER_L] &= (0xFFFFFF0F))
#define     SPI_CMD_ORDER_1_CLEAR()         (R32_SPI[R32_SPI_CMD_ORDER_L] &= (0xFFFFFFF0))

#define     SPI_CMD_ORDER_8_SET(X)          {\
                                                SPI_CMD_ORDER_8_CLEAR();\
                                                (R32_SPI[R32_SPI_CMD_ORDER_L] |= ((X << 28) & 0xF0000000));\
                                            }
#define     SPI_CMD_ORDER_7_SET(X)          {\
                                                SPI_CMD_ORDER_7_CLEAR();\
                                                (R32_SPI[R32_SPI_CMD_ORDER_L] |= ((X << 24) & 0x0F000000));\
                                            }
#define     SPI_CMD_ORDER_6_SET(X)          {\
                                                SPI_CMD_ORDER_6_CLEAR();\
                                                (R32_SPI[R32_SPI_CMD_ORDER_L] |= ((X << 20) & 0x00F00000));\
                                            }
#define     SPI_CMD_ORDER_5_SET(X)          {\
                                                SPI_CMD_ORDER_5_CLEAR();\
                                                (R32_SPI[R32_SPI_CMD_ORDER_L] |= ((X << 16) & 0x000F0000));\
                                            }
#define     SPI_CMD_ORDER_4_SET(X)          {\
                                                SPI_CMD_ORDER_4_CLEAR();\
                                                (R32_SPI[R32_SPI_CMD_ORDER_L] |= ((X << 12) & 0x0000F000));\
                                            }
#define     SPI_CMD_ORDER_3_SET(X)          {\
                                                SPI_CMD_ORDER_3_CLEAR();\
                                                (R32_SPI[R32_SPI_CMD_ORDER_L] |= ((X << 8) & 0x00000F00));\
                                            }
#define     SPI_CMD_ORDER_2_SET(X)          {\
                                                SPI_CMD_ORDER_2_CLEAR();\
                                                (R32_SPI[R32_SPI_CMD_ORDER_L] |= ((X << 4) & 0x000000F0));\
                                            }
#define     SPI_CMD_ORDER_1_SET(X)          {\
                                                SPI_CMD_ORDER_1_CLEAR();\
                                                (R32_SPI[R32_SPI_CMD_ORDER_L] |= ((X << 0) & 0x0000000F));\
                                            }

#define R32_SPI_CMD_ORDER_H            (0x30 >> 2)
#define     SPI_CMD_ORDER_16_CLEAR()        (R32_SPI[R32_SPI_CMD_ORDER_H] &= (0x0FFFFFFF))
#define     SPI_CMD_ORDER_15_CLEAR()        (R32_SPI[R32_SPI_CMD_ORDER_H] &= (0xF0FFFFFF))
#define     SPI_CMD_ORDER_14_CLEAR()        (R32_SPI[R32_SPI_CMD_ORDER_H] &= (0xFF0FFFFF))
#define     SPI_CMD_ORDER_13_CLEAR()        (R32_SPI[R32_SPI_CMD_ORDER_H] &= (0xFFF0FFFF))
#define     SPI_CMD_ORDER_12_CLEAR()        (R32_SPI[R32_SPI_CMD_ORDER_H] &= (0xFFFF0FFF))
#define     SPI_CMD_ORDER_11_CLEAR()        (R32_SPI[R32_SPI_CMD_ORDER_H] &= (0xFFFFF0FF))
#define     SPI_CMD_ORDER_10_CLEAR()        (R32_SPI[R32_SPI_CMD_ORDER_H] &= (0xFFFFFF0F))
#define     SPI_CMD_ORDER_9_CLEAR()         (R32_SPI[R32_SPI_CMD_ORDER_H] &= (0xFFFFFFF0))
#define     SPI_CMD_ORDER_16_SET(X)         {\
                                                SPI_CMD_ORDER_16_CLEAR();\
                                                (R32_SPI[R32_SPI_CMD_ORDER_H] |= ((X << 28) & 0xF0000000));\
                                            }
#define     SPI_CMD_ORDER_15_SET(X)         {\
                                                SPI_CMD_ORDER_15_CLEAR();\
                                                (R32_SPI[R32_SPI_CMD_ORDER_H] |= ((X << 24) & 0x0F000000));\
                                            }
#define     SPI_CMD_ORDER_14_SET(X)         {\
                                                SPI_CMD_ORDER_14_CLEAR();\
                                                (R32_SPI[R32_SPI_CMD_ORDER_H] |= ((X << 20) & 0x00F00000));\
                                            }
#define     SPI_CMD_ORDER_13_SET(X)         {\
                                                SPI_CMD_ORDER_13_CLEAR();\
                                                (R32_SPI[R32_SPI_CMD_ORDER_H] |= ((X << 16) & 0x000F0000));\
                                            }
#define     SPI_CMD_ORDER_12_SET(X)         {\
                                                SPI_CMD_ORDER_12_CLEAR();\
                                                (R32_SPI[R32_SPI_CMD_ORDER_H] |= ((X << 12) & 0x0000F000));\
                                            }
#define     SPI_CMD_ORDER_11_SET(X)         {\
                                                SPI_CMD_ORDER_11_CLEAR();\
                                                (R32_SPI[R32_SPI_CMD_ORDER_H] |= ((X << 8) & 0x00000F00));\
                                            }
#define     SPI_CMD_ORDER_10_SET(X)         {\
                                                SPI_CMD_ORDER_10_CLEAR();\
                                                (R32_SPI[R32_SPI_CMD_ORDER_H] |= ((X << 4) & 0x000000F0));\
                                            }
#define     SPI_CMD_ORDER_9_SET(X)          {\
                                                SPI_CMD_ORDER_9_CLEAR();\
                                                (R32_SPI[R32_SPI_CMD_ORDER_H] |= ((X << 0) & 0x0000000F));\
                                            }

#define R32_SPI_TRIG				(0x34 >> 2)
#define		SPI_DMA_TRIGGER_BIT			BIT0
#define		READ_DATA_LOCATION_BIT		BIT1
#define		BANKING_AUTO_ERASE_BIT		BIT8


#define 	SPI_SET_BANKING_AUTO_ERASE()       (R32_SPI[R32_SPI_TRIG] |= BANKING_AUTO_ERASE_BIT);
#define 	SPI_CLR_BANKING_AUTO_ERASE()       (R32_SPI[R32_SPI_TRIG] &= (~BANKING_AUTO_ERASE_BIT));
#define     SET_SPI_READ_DATA_LOCATION_TO_SRAM() (R32_SPI[R32_SPI_TRIG] &= 0xFFFFFFFD)
#define R8_SPI_RDATA0                     (0x38 >> 0)
#define R32_SPI_RDATA0                     (0x38 >> 2)
#define R64_SPI_RDATA0                    (0x38 >> 3)
#define     SPI_READ_DATA_BYTE3_SET(X)      (R32_SPI[R32_SPI_RDATA0] |= ((X << 24) & 0xFF000000))
#define     SPI_READ_DATA_BYTE2_SET(X)      (R32_SPI[R32_SPI_RDATA0] |= ((X << 16) & 0x00FF0000))
#define     SPI_READ_DATA_BYTE1_SET(X)      (R32_SPI[R32_SPI_RDATA0] |= ((X << 8) & 0x0000FF00))
#define     SPI_READ_DATA_BYTE0_SET(X)      (R32_SPI[R32_SPI_RDATA0] |= ((X << 0) & 0x000000FF))
#define R32_SPI_RDATA1                     (0x3C >> 2)
#define     SPI_READ_DATA_BYTE7_SET(X)      (R32_SPI[R32_SPI_RDATA1] |= ((X << 24) & 0xFF000000))
#define     SPI_READ_DATA_BYTE6_SET(X)      (R32_SPI[R32_SPI_RDATA1] |= ((X << 16) & 0x00FF0000))
#define     SPI_READ_DATA_BYTE5_SET(X)      (R32_SPI[R32_SPI_RDATA1] |= ((X << 8) & 0x0000FF00))
#define     SPI_READ_DATA_BYTE4_SET(X)      (R32_SPI[R32_SPI_RDATA1] |= ((X << 0) & 0x000000FF))

#define R32_SCR_SEED               (0x40 >> 2)
#define     SPI_SCR_SEED_BYTE3_SET(X)       (R32_SPI[R32_SCR_SEED] |= ((X << 24) & 0xFF000000))
#define     SPI_SCR_SEED_BYTE2_SET(X)       (R32_SPI[R32_SCR_SEED] |= ((X << 16) & 0x00FF0000))
#define     SPI_SCR_SEED_BYTE1_SET(X)       (R32_SPI[R32_SCR_SEED] |= ((X << 8) & 0x0000FF00))
#define     SPI_SCR_SEED_BYTE0_SET(X)       (R32_SPI[R32_SCR_SEED] |= ((X << 0) & 0x000000FF))

#endif /* _S17_SPI_REG_H_ */
