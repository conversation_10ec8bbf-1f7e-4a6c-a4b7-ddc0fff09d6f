/*
 * hal_cop0.c
 *
 *  Created on: 2017?9?27?
 *      Author: hyt_huang
 */
#define COP0_VAR_CC
#include "setup.h"
#include "debug/debug.h"
#include "mem.h"
#include "spare.h"
#include "cop0.h"
#include "hal/cop0/cop0_cmd.h"
#include "hal/cop0/cop0_tiein.h"
#include "hal/bmu/bmu_api.h"
#include "hal/cop1/cop1_api.h"
#include "hal/fip/fpu.h"
#include "db_mgr/fw_tagid.h"
#include "table/vbmap/vbmap_api.h"
#include "hal/db/db_reg.h"
#include "hal/db/db_api.h"

#if COP0_MODE_EN

#include "common/fw_common.h"
#include "common/math_op.h"
#include "hal/sys/reg/sys_pd0_reg.h"
#include "hal/sys/reg/sys_pd1_reg.h"
#include "hal/sys/api/mux/mux_api.h"
#include "hal/sys/api/pmu/pmu_api.h"
#include "hal/pic/uart/uart_reg.h"
#include "hal/fip/fip_reg.h"
#include "hal/fip/fip_api.h"
#include "hal/cop0/cop0_tiein.h"
#include "hal/cop0/cop0_reg.h"
#include "hal/cop0/cop0_api.h"
#include "hal/cop0/cop0.h"
#include "hal/bmu/bmu_api.h"
#include "hal/dmac/dmac_api.h"
#include "hal/dmac/dmac_pop_cmd.h"
#include "db_mgr/fw_tagid.h"
#include "db_mgr/fw_cmd_table.h"
#include "debug/debug.h"
#include "hal/cop0/cop0_pop_cmd.h"
#include "retry/retry.h"
#include "init/fw_preformat.h"
#include "init/fw_init.h"
#include "ftl/ftl_barrier.h"
#include "ftl/ftl_bfea_api.h"
#include "rs/rsmap.h"
#include "table/rut/rut_api.h"
#include "err_handle/err_handle_api.h"
#include <string.h>
#include <math.h>
#include "retry/retry_api.h"
#include "retry/patch_cmd.h"
#include "hal/zip/zip_api.h"
#include "vuc/VUC_api.h"
#if (READ_DISTURB_PRDH_EN)
#include "read_disturb/read_disturb_PRDH_api.h"
#endif /* (READ_DISTURB_PRDH_EN) */

U8 gubMerge0SectorSum = 0;
U8 gubMerge1SectorSum = 0;
U8 gubFromISPJumpFlag = 0;
#if PS5021_EN
volatile U8 gubLDPCModeSelect = 0;
#endif /* PS5021_EN */
PATCH_CMD_MGR_INFO_t gPatchCmdMgr = {0};
volatile COP0_ANDES_COMM_INFO_t *gpComm2Andes_Info = (volatile COP0_ANDES_COMM_INFO_t *)OPTD_ANDES_COMM_BASE;

U8 gubCheckPlaneEndFrameIdx[PLANE_NUM];

#if (COP0_BACKUP_P4K_WORKAROUND)
U32 gulBackupP4KCPUDelay = 0;
#endif /*(COP0_BACKUP_P4K_WORKAROUND)*/
//Dylan for V6 RDT porting, for Error: L6218E: Undefined symbol guwFPUTLCCacheRead (referred from cop0.o).
#if (MST_MODE_EN/*(CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC)\
		||(CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC)\
		||(CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)\
		||(CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_TLC)\
		||(CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_QLC)\
		||(CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_TLC)\
		||(CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_QLC)\
		||(CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC)\
		||(CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)*/)//Duson Porting BICS5 Add //Reip Porting 3D-V7 QLC Add//mst ems add--karl//zerio bics6 qlc add
const U8 gubReadCmdPlaneNumByDMABMP[16] = {
	0,// 0 0 0 0
	1,// 0 0 0 1
	1,// 0 0 1 0
	2,// 0 0 1 1
	1,// 0 1 0 0
	2,// 0 1 0 1
	2,// 0 1 1 0
	3,// 0 1 1 1
	1,// 1 0 0 0
	2,// 1 0 0 1
	2,// 1 0 1 0
	3,// 1 0 1 1
	2,// 1 1 0 0
	3,// 1 1 0 1
	3,// 1 1 1 0
	4 // 1 1 1 1
};
#if ((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC))
const U16 guwFPUMicronSnapRead[2] = {
	FPU_PTR_TLC_SNAP_READ,
	FPU_PTR_SLC_SNAP_READ
};
#endif /* ((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)) */

#else//((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC))
const U8 gubReadCmdPlaneNumByDMABMP[16] = {
	0,// 0 0 0 0
	1,// 0 0 0 1
	1,// 0 0 1 0
	2,// 0 0 1 1
	1,// 0 1 0 0
	2,// 0 1 0 1
	2,// 0 1 1 0
	4,// 0 1 1 1
	1,// 1 0 0 0
	2,// 1 0 0 1
	2,// 1 0 1 0
	4,// 1 0 1 1
	2,// 1 1 0 0
	4,// 1 1 0 1
	4,// 1 1 1 0
	4 // 1 1 1 1
};
#endif//((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC))

#if (CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_QLC)
const U16 guwFPUTLCCacheRead[4][2] = { //  low/mid/upper/top, //  single-plane ,multi-plane
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_lower_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_31_lower_pg_read)
	},
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_middle_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_31_middle_pg_read)
	},
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_upper_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_31_upper_pg_read)
	},
	{
		FPU_PTR_OFFSET( fpu_entry_qlc_1p_31_top_pg_read),
		FPU_PTR_OFFSET( fpu_entry_qlc_2p_32_31_top_pg_read)
	}
};

const U16 guwFPUTLCRead[4][2] = { //  low/mid/upper, //  single-plane ,multi-plane
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_lower_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_30_lower_pg_read)
	},
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_middle_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_30_middle_pg_read)
	},
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_upper_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_30_upper_pg_read)
	},
	{
		FPU_PTR_OFFSET( fpu_entry_qlc_1p_30_top_pg_read),
		FPU_PTR_OFFSET( fpu_entry_qlc_2p_32_30_top_pg_read)
	}
};

const U16 guwFPUTLCFastRead[4] = {
	FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_lower_pg_fast_read),
	FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_middle_pg_fast_read),
	FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_upper_pg_fast_read),
	FPU_PTR_OFFSET( fpu_entry_qlc_1p_30_top_pg_fast_read)
};

const U16 guwFPUTLCFastCacheRead[4] = {
	FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_lower_pg_fast_read),
	FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_middle_pg_fast_read),
	FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_upper_pg_fast_read),
	FPU_PTR_OFFSET( fpu_entry_qlc_1p_31_top_pg_fast_read)
};
#elif ((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC))//zerio n48r add
#if ((IM_B47R) || IM_B37R || (IM_N48R))
#if (PS5013_EN) // S17 & E21 Use HW to Send Virtual Address
const U16 guwFPUTLCCacheRead[PLANE_NUM] = { //  single-plane ,multi-plane
	FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_read_bin0),
	FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_31_read_bin0),
	FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_31_read_bin0),
	FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_31_read_bin0)
};

const U16 guwFPUTLCRead[PLANE_NUM] = {  //  single-plane ,multi-plane
	FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_read_bin0),
	FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_30_read_bin0),
	FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_30_read_bin0),
	FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_30_read_bin0)
};
#else /* (PS5013_EN) */
const U16 guwFPUTLCCacheRead[PLANE_NUM] = { //  single-plane ,multi-plane
	FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_read),
	FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_31_read),
	FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_31_read),
	FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_31_read)
};

const U16 guwFPUTLCRead[PLANE_NUM] = {  //  single-plane ,multi-plane
	FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_read),
	FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_30_read),
	FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_30_read),
	FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_30_read)
};
#endif /* (PS5013_EN) */
#endif /* (IM_B47R) || (IM_N48R) */
#elif ((CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_2D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_SANDISK_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_SLC))

const U16 guwFPUTLCCacheRead[3][2] = { //  low/mid/upper, //  single-plane ,multi-plane
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_TLC)
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_lower_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_31_lower_pg_read)
	},
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_middle_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_31_middle_pg_read)
	},
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_upper_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_31_upper_pg_read)
	}
#elif(CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_SLC)
	{
		FPU_PTR_OFFSET( fpu_entry_m3d_tlc_1p_31_read),
		FPU_PTR_OFFSET( fpu_entry_mlc_2p_32_31_read)
	},
	{
		FPU_PTR_OFFSET( fpu_entry_m3d_tlc_1p_31_read),
		FPU_PTR_OFFSET( fpu_entry_mlc_2p_32_31_read)
	},
	{
		FPU_PTR_OFFSET( fpu_entry_m3d_tlc_1p_31_read),
		FPU_PTR_OFFSET( fpu_entry_mlc_2p_32_31_read)
	}
#endif
};

const U16 guwFPUTLCRead[3][2] = { //  low/mid/upper, //  single-plane ,multi-plane
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_TLC)
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_lower_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_30_lower_pg_read)
	},
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_middle_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_30_middle_pg_read)
	},
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_upper_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_30_upper_pg_read)
	}
#elif(CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_SLC)
	{
		FPU_PTR_OFFSET(fpu_entry_m3d_tlc_1p_30_read),
		FPU_PTR_OFFSET(fpu_entry_mlc_2p_32_30_read)
	},
	{
		FPU_PTR_OFFSET(fpu_entry_m3d_tlc_1p_30_read),
		FPU_PTR_OFFSET(fpu_entry_mlc_2p_32_30_read)
	},
	{
		FPU_PTR_OFFSET(fpu_entry_m3d_tlc_1p_30_read),
		FPU_PTR_OFFSET(fpu_entry_mlc_2p_32_30_read)
	}

#endif
};

const U16 guwFPUTLCFastRead[3] = {
	FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_lower_pg_fast_read),
	FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_middle_pg_fast_read),
	FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_upper_pg_fast_read)
};

const U16 guwFPUTLCFastCacheRead[3] = {
	FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_lower_pg_fast_read),
	FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_middle_pg_fast_read),
	FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_upper_pg_fast_read)
};

/*  BICS4_AIPR_FEATURE_EN */
const U16 guwFPUAIPRSLCRead[2] = { //  single-plane ,multi-plane
	FPU_PTR_OFFSET( fpu_entry_slc_1p_30_read),
	FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_slc_2p_read)
};

const U16 guwFPUAIPRTLCRead[3][3][2] = { //  low/mid/upper,A //  low/mid/upper,B  //  single-plane ,multi-plane
	{
		{
			FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_lower_pg_read),
			FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_tlc_read_low_low)
		},
		{
			FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_lower_pg_read),
			FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_tlc_read_low_middle)
		},
		{
			FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_lower_pg_read),
			FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_tlc_read_low_upper)
		}
	},
	{
		{
			FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_middle_pg_read),
			FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_tlc_read_middle_low)
		},
		{
			FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_middle_pg_read),
			FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_tlc_read_middle_middle)
		},
		{
			FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_middle_pg_read),
			FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_tlc_read_middle_upper)
		}
	},
	{
		{
			FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_upper_pg_read),
			FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_tlc_read_upper_low)
		},
		{
			FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_upper_pg_read),
			FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_tlc_read_upper_middle)
		},
		{
			FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_upper_pg_read),
			FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_tlc_read_upper_upper)
		}
	}
};

const U16 guwFPUAIPRSLCAllCacheRead[2] = { //  single-plane ,multi-plane
	FPU_PTR_OFFSET( fpu_entry_slc_1p_31_read),
	FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_slc_2p_cache_read)
};

const U16 guwFPUAIPRTLCAllCacheRead[3][3][2] = { //  low/mid/upper,A //  low/mid/upper,B  //  single-plane ,multi-plane
	{
		{
			FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_lower_pg_read),
			FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_tlc_cache_read_low_low)
		},
		{
			FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_lower_pg_read),
			FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_tlc_cache_read_low_middle)
		},
		{
			FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_lower_pg_read),
			FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_tlc_cache_read_low_upper)
		}
	},
	{
		{
			FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_middle_pg_read),
			FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_tlc_cache_read_middle_low)
		},
		{
			FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_middle_pg_read),
			FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_tlc_cache_read_middle_middle)
		},
		{
			FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_middle_pg_read),
			FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_tlc_cache_read_middle_upper)
		}
	},
	{
		{
			FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_upper_pg_read),
			FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_tlc_cache_read_upper_low)
		},
		{
			FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_upper_pg_read),
			FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_tlc_cache_read_upper_middle)
		},
		{
			FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_upper_pg_read),
			FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_tlc_cache_read_upper_upper)
		}
	}
};

const U16 guwFPUAIPRSLC3130Read[2] = {
	FPU_PTR_OFFSET( fpu_entry_slc_1p_31_read),
	FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_slc_31_30_read )
};
const U16 guwFPUAIPRTLC3130Read[3][3][2] = { //  low/mid/upper,A //  low/mid/upper,B  //  single-plane ,multi-plane
	{
		{
			FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_lower_pg_read),
			FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_tlc_31_30_read_low_low)
		},
		{
			FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_lower_pg_read),
			FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_tlc_31_30_read_low_middle)
		},
		{
			FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_lower_pg_read),
			FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_tlc_31_30_read_low_upper)
		}
	},
	{
		{
			FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_middle_pg_read),
			FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_tlc_31_30_read_middle_low)
		},
		{
			FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_middle_pg_read),
			FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_tlc_31_30_read_middle_middle)
		},
		{
			FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_middle_pg_read),
			FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_tlc_31_30_read_middle_upper)
		}
	},
	{
		{
			FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_upper_pg_read),
			FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_tlc_31_30_read_upper_low)
		},
		{
			FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_upper_pg_read),
			FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_tlc_31_30_read_upper_middle)
		},
		{
			FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_upper_pg_read),
			FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_tlc_31_30_read_upper_upper)
		}
	}
};

const U16 guwFPUAIPRSLC3031Read[2] = {
	FPU_PTR_OFFSET( fpu_entry_slc_1p_31_read),
	FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_slc_30_31_read )
};
const U16 guwFPUAIPRTLC3031Read[3][3][2] = { //  low/mid/upper,A //  low/mid/upper,B  //  single-plane ,multi-plane
	{
		{
			FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_lower_pg_read),
			FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_tlc_30_31_read_low_low)
		},
		{
			FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_lower_pg_read),
			FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_tlc_30_31_read_low_middle)
		},
		{
			FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_lower_pg_read),
			FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_tlc_30_31_read_low_upper)
		}
	},
	{
		{
			FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_middle_pg_read),
			FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_tlc_30_31_read_middle_low)
		},
		{
			FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_middle_pg_read),
			FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_tlc_30_31_read_middle_middle)
		},
		{
			FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_middle_pg_read),
			FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_tlc_30_31_read_middle_upper)
		}
	},
	{
		{
			FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_upper_pg_read),
			FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_tlc_30_31_read_upper_low)
		},
		{
			FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_upper_pg_read),
			FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_tlc_30_31_read_upper_middle)
		},
		{
			FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_upper_pg_read),
			FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_tlc_30_31_read_upper_upper)
		}
	}
};

const U16 guwFPUAIPRSLC313FRead = {
	FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_slc_31_3f_read )
};
const U16 guwFPUAIPRTLC313FRead[3] = { //  low/mid/upper
	FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_tlc_31_3f_read_low),
	FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_tlc_31_3f_read_middle),
	FPU_PTR_OFFSET( fpu_entry_tsb_bics4_aipr_tlc_31_3f_read_upper)
};

const U16 guwFPUAIPREndCacheRead[2][2] = {//  tlc/slc ;     //  plane num
	{
		FPU_PTR_OFFSET(fpu_entry_tsb_bics4_aipr_1p_cache_end),
		FPU_PTR_OFFSET(fpu_entry_tsb_bics4_aipr_2p_cache_end)
	},
	{
		FPU_PTR_OFFSET(fpu_entry_tsb_bics4_aipr_slc_1p_cache_end),
		FPU_PTR_OFFSET(fpu_entry_tsb_bics4_aipr_slc_2p_cache_end)
	},
};
/* BICS4_AIPR_FEATURE_EN */
//Dylan for V6 RDT porting, modify by V6-based RDT code
#elif (FLASH_TYPE_HYNIX_3D_TLC == CONFIG_FLASH_TYPE)
const U16 guwFPUTLCCacheRead[3][PLANE_NUM] = { //  low/mid/upper, //  single-plane ,multi-plane
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_lower_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_31_lower_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_31_lower_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_31_lower_pg_read)
	},
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_middle_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_31_middle_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_31_middle_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_31_middle_pg_read)
	},
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_upper_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_31_upper_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_31_upper_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_31_upper_pg_read)
	}
};

const U16 guwFPUTLCRead[3][PLANE_NUM] = { //  low/mid/upper, //  single-plane ,multi-plane
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_lower_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_30_lower_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_30_lower_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_30_lower_pg_read)
	},
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_middle_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_30_middle_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_30_middle_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_30_middle_pg_read)
	},
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_upper_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_30_upper_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_30_upper_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_30_upper_pg_read)
	}
};
#elif (FLASH_TYPE_HYNIX_3D_QLC == CONFIG_FLASH_TYPE) //Reip Porting 3D-V7 QLC Add
const U16 guwFPUTLCCacheRead[4][PLANE_NUM] = { //  low/mid/upper/top, //  single-plane ,multi-plane
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_lower_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_31_lower_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_31_lower_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_31_lower_pg_read)
	},
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_middle_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_31_middle_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_31_middle_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_31_middle_pg_read)
	},
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_upper_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_31_upper_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_31_upper_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_31_upper_pg_read)
	},
	{
		FPU_PTR_OFFSET( fpu_entry_qlc_1p_31_top_pg_read),
		FPU_PTR_OFFSET( fpu_entry_qlc_2p_32_31_top_pg_read),
		FPU_PTR_OFFSET( fpu_entry_qlc_3p_32_31_top_pg_read),
		FPU_PTR_OFFSET( fpu_entry_qlc_4p_32_31_top_pg_read)
	}
};

const U16 guwFPUTLCRead[4][PLANE_NUM] = { //  low/mid/upper/top, //  single-plane ,multi-plane
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_lower_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_30_lower_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_30_lower_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_30_lower_pg_read)
	},
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_middle_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_30_middle_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_30_middle_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_30_middle_pg_read)
	},
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_upper_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_30_upper_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_30_upper_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_30_upper_pg_read)
	},
	{
		FPU_PTR_OFFSET( fpu_entry_qlc_1p_30_top_pg_read),
		FPU_PTR_OFFSET( fpu_entry_qlc_2p_32_30_top_pg_read),
		FPU_PTR_OFFSET( fpu_entry_qlc_3p_32_30_top_pg_read),
		FPU_PTR_OFFSET( fpu_entry_qlc_4p_32_30_top_pg_read)
	}
};
#elif(CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_TLC)//Duson Porting BICS5 Add//zerio BICS8 Add
const U16 guwFPUTLCCacheRead[3][PLANE_NUM] = { //  low/mid/upper, //  single-plane ,multi-plane
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_lower_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_31_lower_pg_read)
#if(IM_BICS6||IM_BICS8)
		, FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_31_lower_pg_read)
		, FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_31_lower_pg_read)
#endif
	},
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_middle_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_31_middle_pg_read)
#if(IM_BICS6||IM_BICS8)
		, FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_31_middle_pg_read)
		, FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_31_middle_pg_read)
#endif
	},
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_upper_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_31_upper_pg_read)
#if(IM_BICS6||IM_BICS8)
		, FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_31_upper_pg_read)
		, FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_31_upper_pg_read)
#endif
	}
};
const U16 guwFPUTLCRead[3][PLANE_NUM] = { //  low/mid/upper, //  single-plane ,multi-plane
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_lower_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_30_lower_pg_read)
#if(IM_BICS6||IM_BICS8)
		, FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_30_lower_pg_read)
		, FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_30_lower_pg_read)
#endif
	},
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_middle_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_30_middle_pg_read)
#if(IM_BICS6||IM_BICS8)
		, FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_30_middle_pg_read)
		, FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_30_middle_pg_read)
#endif
	},
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_upper_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_30_upper_pg_read)
#if(IM_BICS6||IM_BICS8)
		, FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_30_upper_pg_read)
		, FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_30_upper_pg_read)
#endif
	}
};
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_QLC) //zerio bics6 qlc add
const U16 guwFPUTLCCacheRead[4][PLANE_NUM] = { //  low/mid/upper/top, //  single-plane ,multi-plane
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_lower_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_31_lower_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_31_lower_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_31_lower_pg_read)
	},
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_middle_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_31_middle_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_31_middle_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_31_middle_pg_read)
	},
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_upper_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_31_upper_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_31_upper_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_31_upper_pg_read)
	},
	{
		FPU_PTR_OFFSET( fpu_entry_qlc_1p_31_top_pg_read),
		FPU_PTR_OFFSET( fpu_entry_qlc_2p_32_31_top_pg_read),
		FPU_PTR_OFFSET( fpu_entry_qlc_3p_32_31_top_pg_read),
		FPU_PTR_OFFSET( fpu_entry_qlc_4p_32_31_top_pg_read)
	}
};

const U16 guwFPUTLCRead[4][PLANE_NUM] = { //  low/mid/upper/top, //  single-plane ,multi-plane
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_lower_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_30_lower_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_30_lower_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_30_lower_pg_read)
	},
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_middle_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_30_middle_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_30_middle_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_30_middle_pg_read)
	},
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_upper_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_30_upper_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_30_upper_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_30_upper_pg_read)
	},
	{
		FPU_PTR_OFFSET( fpu_entry_qlc_1p_30_top_pg_read),
		FPU_PTR_OFFSET( fpu_entry_qlc_2p_32_30_top_pg_read),
		FPU_PTR_OFFSET( fpu_entry_qlc_3p_32_30_top_pg_read),
		FPU_PTR_OFFSET( fpu_entry_qlc_4p_32_30_top_pg_read)
	}
};

#endif  /* (CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_QLC) */
#if ((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC))//zerio n48r
//Only Support BiCS3 now.
#if ((PS5013_EN) && (IM_B47R || IM_B37R || IM_N48R)) // S17 & E21 Use HW to Send Virtual Address
const U16 guwFPUSLCCacheRead[PLANE_NUM] = {     //  single-plane ,multi-plane
	FPU_PTR_OFFSET( fpu_entry_slc_1p_31_read_bin0),
	FPU_PTR_OFFSET( fpu_entry_slc_2p_32_31_read_bin0),
	FPU_PTR_OFFSET( fpu_entry_slc_3p_32_31_read_bin0),
	FPU_PTR_OFFSET( fpu_entry_slc_4p_32_31_read_bin0)
};


const U16 guwFPUSLCRead[PLANE_NUM] = {     //  single-plane ,multi-plane
	FPU_PTR_OFFSET( fpu_entry_slc_1p_30_read_bin0),
	FPU_PTR_OFFSET( fpu_entry_slc_2p_32_30_read_bin0),
	FPU_PTR_OFFSET( fpu_entry_slc_3p_32_30_read_bin0),
	FPU_PTR_OFFSET( fpu_entry_slc_4p_32_30_read_bin0)
};
#else /* ((PS5013_EN) && IM_B47R) */
const U16 guwFPUSLCCacheRead[PLANE_NUM] = {     //  single-plane ,multi-plane
	FPU_PTR_OFFSET( fpu_entry_slc_1p_31_read),
	FPU_PTR_OFFSET( fpu_entry_slc_2p_32_31_read),
	FPU_PTR_OFFSET( fpu_entry_slc_3p_32_31_read),
	FPU_PTR_OFFSET( fpu_entry_slc_4p_32_31_read)
};


const U16 guwFPUSLCRead[PLANE_NUM] = {     //  single-plane ,multi-plane
	FPU_PTR_OFFSET( fpu_entry_slc_1p_30_read),
	FPU_PTR_OFFSET( fpu_entry_slc_2p_32_30_read),
	FPU_PTR_OFFSET( fpu_entry_slc_3p_32_30_read),
	FPU_PTR_OFFSET( fpu_entry_slc_4p_32_30_read)
};
#endif /* ((PS5013_EN) && (IM_B47R || IM_N48R))*/

const U16 guwFPUEndCacheRead[2] = {
	FPU_PTR_OFFSET(fpu_entry_3F_read),
	FPU_PTR_OFFSET(fpu_entry_slc_3F_read)
};
//Dylan for V6 RDT porting, modify by V6-based RDT code
#elif ((FLASH_TYPE_HYNIX_3D_TLC == CONFIG_FLASH_TYPE) || (FLASH_TYPE_HYNIX_3D_QLC == CONFIG_FLASH_TYPE)) //Reip Porting 3D-V7 QLC Add
const U16 guwFPUSLCCacheRead[PLANE_NUM] = { 	//	single-plane ,multi-plane
	FPU_PTR_OFFSET( fpu_entry_slc_1p_31_read),
	FPU_PTR_OFFSET( fpu_entry_slc_2p_32_31_read),
	FPU_PTR_OFFSET( fpu_entry_slc_3p_32_31_read),
	FPU_PTR_OFFSET( fpu_entry_slc_4p_32_31_read)
};


const U16 guwFPUSLCRead[PLANE_NUM] = {	   //  single-plane ,multi-plane
	FPU_PTR_OFFSET( fpu_entry_slc_1p_30_read),
	FPU_PTR_OFFSET( fpu_entry_slc_2p_32_30_read),
	FPU_PTR_OFFSET( fpu_entry_slc_3p_32_30_read),
	FPU_PTR_OFFSET( fpu_entry_slc_4p_32_30_read)
};


const U16 guwFPUEndCacheRead[2] = {
	FPU_PTR_OFFSET(fpu_entry_3F_read),
	FPU_PTR_OFFSET(fpu_entry_slc_3F_read)
};
const U16 guwFPUSLCFastRead = FPU_PTR_OFFSET( fpu_entry_slc_1p_30_fast_read);

const U16 guwFPUSLCFastCacheRead =	FPU_PTR_OFFSET( fpu_entry_slc_1p_31_fast_read);
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_TLC || CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_QLC)//Duson Porting BICS5 Add//zerio BICS8 Add//zerio bics6 qlc add
const U16 guwFPUSLCCacheRead[PLANE_NUM] = { 	//	single-plane ,multi-plane
	FPU_PTR_OFFSET( fpu_entry_slc_1p_31_read),
	FPU_PTR_OFFSET( fpu_entry_slc_2p_32_31_read)
#if(IM_BICS6||IM_BICS8||IM_BICS6_QLC)
	, FPU_PTR_OFFSET( fpu_entry_slc_3p_32_31_read)
	, FPU_PTR_OFFSET( fpu_entry_slc_4p_32_31_read)
#endif
};


const U16 guwFPUSLCRead[PLANE_NUM] = {	   //  single-plane ,multi-plane
	FPU_PTR_OFFSET( fpu_entry_slc_1p_30_read),
	FPU_PTR_OFFSET( fpu_entry_slc_2p_32_30_read)
#if(IM_BICS6||IM_BICS8||IM_BICS6_QLC)
	, FPU_PTR_OFFSET( fpu_entry_slc_3p_32_30_read)
	, FPU_PTR_OFFSET( fpu_entry_slc_4p_32_30_read)
#endif
};


const U16 guwFPUEndCacheRead[2] = {
	FPU_PTR_OFFSET(fpu_entry_3F_read),
	FPU_PTR_OFFSET(fpu_entry_slc_3F_read)
};

const U16 guwFPUSLCFastRead = FPU_PTR_OFFSET( fpu_entry_slc_1p_30_fast_read);

const U16 guwFPUSLCFastCacheRead =	FPU_PTR_OFFSET( fpu_entry_slc_1p_31_fast_read);

#elif (FLASH_TYPE_YMTC_3D_TLC == CONFIG_FLASH_TYPE)
const U16 guwFPUTLCCacheRead[3][PLANE_NUM] = { //  low/mid/upper, //  single-plane ,multi-plane
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_31_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_31_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_31_pg_read)
	},
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_31_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_31_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_31_pg_read)
	},
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_31_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_31_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_31_pg_read)
	}
};

const U16 guwFPUTLCRead[3][PLANE_NUM] = { //  low/mid/upper, //  single-plane ,multi-plane
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_30_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_30_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_30_pg_read)
	},
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_30_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_30_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_30_pg_read)
	},
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_30_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_30_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_30_pg_read)
	}
};

const U16 guwFPUSLCCacheRead[PLANE_NUM] = { 	//	single-plane ,multi-plane
	FPU_PTR_OFFSET( fpu_entry_slc_1p_31_read),
	FPU_PTR_OFFSET( fpu_entry_slc_2p_32_31_read),
	FPU_PTR_OFFSET( fpu_entry_slc_3p_32_31_read),
	FPU_PTR_OFFSET( fpu_entry_slc_4p_32_31_read)
};


const U16 guwFPUSLCRead[PLANE_NUM] = {	   //  single-plane ,multi-plane
	FPU_PTR_OFFSET( fpu_entry_slc_1p_30_read),
	FPU_PTR_OFFSET( fpu_entry_slc_2p_32_30_read),
	FPU_PTR_OFFSET( fpu_entry_slc_3p_32_30_read),
	FPU_PTR_OFFSET( fpu_entry_slc_4p_32_30_read)
};


const U16 guwFPUEndCacheRead[2] = {
	FPU_PTR_OFFSET(fpu_entry_3F_read),
	FPU_PTR_OFFSET(fpu_entry_slc_3F_read)
};

const U16 guwFPUSLCFastRead = FPU_PTR_OFFSET( fpu_common_region_delimiter);

const U16 guwFPUSLCFastCacheRead =	FPU_PTR_OFFSET( fpu_common_region_delimiter);

#elif (FLASH_TYPE_YMTC_3D_QLC == CONFIG_FLASH_TYPE)//ems mst add--karl
const U16 guwFPUTLCCacheRead[PLANE_NUM] = { //  single-plane ,multi-plane
	FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_read),
	FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_31_read),
	FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_31_read),
	FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_31_read)
};

const U16 guwFPUTLCRead[PLANE_NUM] = { //  single-plane ,multi-plane
	FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_read),
	FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_30_read),
	FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_30_read),
	FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_30_read)
};

const U16 guwFPUSLCCacheRead[PLANE_NUM] = { 	//	single-plane ,multi-plane
	FPU_PTR_OFFSET( fpu_entry_slc_1p_31_read),
	FPU_PTR_OFFSET( fpu_entry_slc_2p_32_31_read),
	FPU_PTR_OFFSET( fpu_entry_slc_3p_32_31_read),
	FPU_PTR_OFFSET( fpu_entry_slc_4p_32_31_read)
};


const U16 guwFPUSLCRead[PLANE_NUM] = {	   //  single-plane ,multi-plane
	FPU_PTR_OFFSET( fpu_entry_slc_1p_30_read),
	FPU_PTR_OFFSET( fpu_entry_slc_2p_32_30_read),
	FPU_PTR_OFFSET( fpu_entry_slc_3p_32_30_read),
	FPU_PTR_OFFSET( fpu_entry_slc_4p_32_30_read)
};


const U16 guwFPUEndCacheRead[2] = {
	FPU_PTR_OFFSET(fpu_entry_3F_read),
	FPU_PTR_OFFSET(fpu_entry_slc_3F_read)
};

#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
#if ((FW_CATEGORY_FLASH == FLASH_SAMSUNG_V6_TLC) || (FW_CATEGORY_FLASH == FLASH_SAMSUNG_V6P_TLC) || (FW_CATEGORY_FLASH == FLASH_SAMSUNG_V5_TLC))
const U16 guwFPUTLCCacheRead[3][PLANE_NUM] = { //  low/mid/upper, //  single-plane ,multi-plane
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_31_pg_read)

	},
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_31_pg_read)
	},
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_31_pg_read)
	}
};

const U16 guwFPUTLCRead[3][PLANE_NUM] = { //  low/mid/upper, //  single-plane ,multi-plane
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_30_pg_read)
	},
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_30_pg_read)
	},
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_30_pg_read)
	}
};

const U16 guwFPUSLCCacheRead[PLANE_NUM] = { 	//	single-plane ,multi-plane
	FPU_PTR_OFFSET( fpu_entry_slc_1p_31_read),
	FPU_PTR_OFFSET( fpu_entry_slc_2p_32_31_read)
};


const U16 guwFPUSLCRead[PLANE_NUM] = {	   //  single-plane ,multi-plane
	FPU_PTR_OFFSET( fpu_entry_slc_1p_30_read),
	FPU_PTR_OFFSET( fpu_entry_slc_2p_32_30_read)
};
#elif (FW_CATEGORY_FLASH == FLASH_SAMSUNG_V7_TLC || FW_CATEGORY_FLASH == FLASH_SAMSUNG_V8_TLC)//Samsung v7/v8 mst add--Reip
const U16 guwFPUTLCCacheRead[3][PLANE_NUM] = { //  low/mid/upper, //  single-plane ,multi-plane
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_31_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_31_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_31_pg_read)
	},
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_31_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_31_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_31_pg_read)
	},
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_31_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_31_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_31_pg_read)

	}
};

const U16 guwFPUTLCRead[3][PLANE_NUM] = { //  low/mid/upper, //  single-plane ,multi-plane
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_30_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_30_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_30_pg_read)
	},
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_30_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_30_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_30_pg_read)
	},
	{
		FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_30_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_30_pg_read),
		FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_30_pg_read)
	}
};

const U16 guwFPUSLCCacheRead[PLANE_NUM] = { 	//	single-plane ,multi-plane
	FPU_PTR_OFFSET( fpu_entry_slc_1p_31_read),
	FPU_PTR_OFFSET( fpu_entry_slc_2p_32_31_read),
	FPU_PTR_OFFSET( fpu_entry_slc_3p_32_31_read),
	FPU_PTR_OFFSET( fpu_entry_slc_4p_32_31_read)
};

const U16 guwFPUSLCRead[PLANE_NUM] = {	   //  single-plane ,multi-plane
	FPU_PTR_OFFSET( fpu_entry_slc_1p_30_read),
	FPU_PTR_OFFSET( fpu_entry_slc_2p_32_30_read),
	FPU_PTR_OFFSET( fpu_entry_slc_3p_32_30_read),
	FPU_PTR_OFFSET( fpu_entry_slc_4p_32_30_read)
};
#endif

const U16 guwFPUEndCacheRead[2] = {
	FPU_PTR_OFFSET(fpu_entry_3F_read),
	FPU_PTR_OFFSET(fpu_entry_slc_3F_read)
};

const U16 guwFPUSLCFastRead = FPU_PTR_OFFSET( fpu_common_region_delimiter);

const U16 guwFPUSLCFastCacheRead =	FPU_PTR_OFFSET( fpu_common_region_delimiter);

#elif (FLASH_TYPE_INTEL_3D_QLC == CONFIG_FLASH_TYPE)
const U16 guwFPUTLCCacheRead[PLANE_NUM] = { //  single-plane ,multi-plane
	FPU_PTR_OFFSET( fpu_entry_tlc_1p_31_pg_read),
	FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_31_pg_read),
	FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_31_pg_read),
	FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_31_pg_read)
};

const U16 guwFPUTLCRead[PLANE_NUM] = { //  single-plane ,multi-plane
	FPU_PTR_OFFSET( fpu_entry_tlc_1p_30_pg_read),
	FPU_PTR_OFFSET( fpu_entry_tlc_2p_32_30_pg_read),
	FPU_PTR_OFFSET( fpu_entry_tlc_3p_32_30_pg_read),
	FPU_PTR_OFFSET( fpu_entry_tlc_4p_32_30_pg_read)
};

const U16 guwFPUSLCCacheRead[PLANE_NUM] = { 	//	single-plane ,multi-plane
	FPU_PTR_OFFSET( fpu_entry_slc_1p_31_read),
	FPU_PTR_OFFSET( fpu_entry_slc_2p_32_31_read),
	FPU_PTR_OFFSET( fpu_entry_slc_3p_32_31_read),
	FPU_PTR_OFFSET( fpu_entry_slc_4p_32_31_read)
};


const U16 guwFPUSLCRead[PLANE_NUM] = {	   //  single-plane ,multi-plane
	FPU_PTR_OFFSET( fpu_entry_slc_1p_30_read),
	FPU_PTR_OFFSET( fpu_entry_slc_2p_32_30_read),
	FPU_PTR_OFFSET( fpu_entry_slc_3p_32_30_read),
	FPU_PTR_OFFSET( fpu_entry_slc_4p_32_30_read)
};


const U16 guwFPUEndCacheRead[2] = {
	FPU_PTR_OFFSET(fpu_entry_3F_read),
	FPU_PTR_OFFSET(fpu_entry_slc_3F_read)
};

#else//((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC))
//Only Support BiCS3 now.
const U16 guwFPUSLCCacheRead[2] = {     //  single-plane ,multi-plane
	FPU_PTR_OFFSET( fpu_entry_slc_1p_31_read),
	FPU_PTR_OFFSET( fpu_entry_slc_2p_32_31_read)
};


const U16 guwFPUSLCRead[2] = {     //  single-plane ,multi-plane
	FPU_PTR_OFFSET( fpu_entry_slc_1p_30_read),
	FPU_PTR_OFFSET( fpu_entry_slc_2p_32_30_read)
};


const U16 guwFPUEndCacheRead[2] = {
	FPU_PTR_OFFSET(fpu_entry_3F_read),
	FPU_PTR_OFFSET(fpu_entry_slc_3F_read)
};

const U16 guwFPUSLCFastRead = FPU_PTR_OFFSET( fpu_entry_slc_1p_30_fast_read);

const U16 guwFPUSLCFastCacheRead =  FPU_PTR_OFFSET( fpu_entry_slc_1p_31_fast_read);
#endif//((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC))
#endif// COP0_MODE_EN

void COP0_TriggerCOP0SQ(U8 ubDoorBellQueueID)
{
	if (ubDoorBellQueueID == DB_COP0_RD_SQ) {
		M_DB_TRIGGER_WRITE_CNT(DB_COP0_RD_SQ, 1);
		M_FW_ASSERT(ASSERT_HAL_COP0_0x05B2, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_COP0_RD_SQ, gDBQueueCnt.QueueCnt[(DB_COP0_RD_SQ)] ) == M_DB_GET_WPTR((DB_COP0_RD_SQ)));
	}
	else if (ubDoorBellQueueID == DB_COP0_WR_SQ) {
		M_DB_TRIGGER_WRITE_CNT(DB_COP0_WR_SQ, 1);
		M_FW_ASSERT(ASSERT_HAL_COP0_0x05B3, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_COP0_WR_SQ, gDBQueueCnt.QueueCnt[(DB_COP0_WR_SQ)] ) == M_DB_GET_WPTR((DB_COP0_WR_SQ)));
	}
	else if (ubDoorBellQueueID == DB_COP0_SQ1) {
		M_DB_TRIGGER_WRITE_CNT(DB_COP0_SQ1, 1);
		M_FW_ASSERT(ASSERT_HAL_COP0_0x05B4, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_COP0_SQ1, gDBQueueCnt.QueueCnt[(DB_COP0_SQ1)] ) == M_DB_GET_WPTR((DB_COP0_SQ1)));
	}

}

U32 COP0_GetCOP0SQPtr(U8 ubDoorBellQueueID)
{
	U32 ulPTR = 0;
	if (ubDoorBellQueueID == DB_COP0_RD_SQ) {
		ulPTR = (U32) (M_DB_GET_QBODY_PTR(DB_COP0_RD_SQ, gDBQueueCnt.B.uwCOP0ReadSQCnt));
	}
	else if (ubDoorBellQueueID == DB_COP0_WR_SQ) {
		ulPTR = (U32) (M_DB_GET_QBODY_PTR(DB_COP0_WR_SQ, gDBQueueCnt.B.uwCOP0WriteSQCnt));
	}
	else {
		ulPTR = (U32) (M_DB_GET_QBODY_PTR(DB_COP0_SQ1, gDBQueueCnt.B.uwCOP0SQ1Cnt));
	}
	return ulPTR;
}

U8 COP0_CheckCOP0SQIsEnoughSpaceByWriteCnt(U8 ubDoorBellQueueID, U8 ubCmdNum)
{
	if (M_DB_GET_WR_CNT(ubDoorBellQueueID) < ubCmdNum) {
		return FALSE;
	}
	else {
		return TRUE;
	}
}

void COP0_WaitCOP0SQIsEnoughSpaceByWriteCnt(U8 ubDoorBellQueueID, U8 ubCmdNum)
{
	if (ubDoorBellQueueID == DB_COP0_RD_SQ) {
		while (M_DB_GET_WR_CNT(DB_COP0_RD_SQ) < ubCmdNum);
	}
	else if (ubDoorBellQueueID == DB_COP0_WR_SQ) {
		while (M_DB_GET_WR_CNT(DB_COP0_WR_SQ) < ubCmdNum);
	}
	else if (ubDoorBellQueueID == DB_COP0_SQ1) {
		while (M_DB_GET_WR_CNT(DB_COP0_SQ1) < ubCmdNum);
	}
}

void COP0_WaitCOP0SQIsEnoughSpace(U8 ubDoorBellQueueID)
{
	COP0_WaitCOP0SQIsEnoughSpaceByWriteCnt( ubDoorBellQueueID, 1);
}

#if COP0_MODE_EN
//void fpl_cop0_set_pack_addr_rule(PCA_Rule_t *pRule, PCA_Rule_t *pLSB_Rule, U32 ulValue)
void COP0_FplSetPack_AddrRule(PCA_Rule_t *pRule, PCA_Rule_t *pLSB_Rule,	U32 ulValue)
{
	U8 ubRuleIdx;
	for (ubRuleIdx = 0; ubRuleIdx < COP0_PCA_RULE_NUM; ubRuleIdx ++) {
		// shift
		if (pLSB_Rule) {
			pRule->ubShift[ubRuleIdx] = pLSB_Rule->ubShift[ubRuleIdx] + pLSB_Rule->ubBit_No;
		}
		else {
			pRule->ubShift[ubRuleIdx] = 0;
		}
	}

	// bit_no
	pRule->ubBit_No = (U8)(log2(ulValue));
	if (((U32)1 << pRule->ubBit_No) < ulValue) {
		pRule->ubBit_No++;
	}

	// mask
	pRule->ulMask = (1 << pRule->ubBit_No) - 1;
}

//void fpl_cop0_set_slc_addr_rule (void)
void COP0_FplSetSlc_AddrRule(void)
{
	/*
	U8 ubRuleIdx;

	gubTotalPCALen = gPCARule_Block.ubShift[COP0_PCA_RULE_0] + gPCARule_Block.ubBit_No;
	//------------------------------------------------------------------------------------------//
	//---------------------------------------- vca rule ----------------------------------------//
	//------------------------------------------------------------------------------------------//
	for (ubRuleIdx = 0; ubRuleIdx < COP0_PCA_RULE_NUM; ubRuleIdx ++) {
		gVCARule_Entry.ubShift[ubRuleIdx]   = gPCARule_Entry.ubShift[ubRuleIdx];
	}
	gVCARule_Entry.ulMask    = gPCARule_Entry.ulMask;
	gVCARule_Entry.ubBit_No  = gPCARule_Entry.ubBit_No;

	//2.2.2 CREG_VB_RULE(Offset: 87h~84h) -> block & die & ext die
	gVCARule_VB.ubBit_No     = gPCARule_Block.ubBit_No + gPCARule_LUN.ubBit_No + gPCARule_ExtLUN.ubBit_No;

	//2.2.66    CREG_RS_CONMON_RULE -> PPAGE_RULE_START & PPAGE_RULE_LENS
	for (ubRuleIdx = 0; ubRuleIdx < COP0_PCA_RULE_NUM; ubRuleIdx ++) {
		gVCARule_PPG.ubShift[ubRuleIdx]     = gVCARule_Entry.ubShift[ubRuleIdx] + gVCARule_Entry.ubBit_No;
	}
	gVCARule_PPG.ubBit_No    = gubTotalPCALen - gVCARule_VB.ubBit_No - gVCARule_Entry.ubBit_No;
	gVCARule_PPG.ulMask      = (0x01UL << gVCARule_PPG.ubBit_No) - 1;

	//2.2.2 CREG_VB_RULE(Offset: 87h~84h) -> block & die & ext die
	for (ubRuleIdx = 0; ubRuleIdx < COP0_PCA_RULE_NUM; ubRuleIdx ++) {
		gVCARule_VB.ubShift[ubRuleIdx]      = gVCARule_PPG.ubShift[ubRuleIdx] + gVCARule_PPG.ubBit_No;
	}
	gVCARule_VB.ulMask       = (0x01UL << gVCARule_VB.ubBit_No) - 1;

	M_UART(COP0_, "\n****************** VCA Rule ******************\n");

	for (ubRuleIdx = 0; ubRuleIdx < COP0_PCA_RULE_NUM; ubRuleIdx ++) {
		M_UART(COP0_, "VEntry shift[%b]:%x, bit num:%d\n", ubRuleIdx, gVCARule_Entry.ubShift[ubRuleIdx], gVCARule_Entry.ubBit_No);
		M_UART(COP0_, "VPPG   shift[%b]:%x, bit num:%d\n", ubRuleIdx, gVCARule_PPG.ubShift[ubRuleIdx]  , gVCARule_PPG.ubBit_No  );
		M_UART(COP0_, "VB     shift[%b]:%x, bit num:%d\n", ubRuleIdx, gVCARule_VB.ubShift[ubRuleIdx]   , gVCARule_PPG.ubBit_No  );
		M_UART(COP0_, "==================================\n");
	}

	return;
	*/
}

//void hal_cop0_slc_pca_reg_setup(void)
void COP0_SlcPCA_RegSetup(void)
{
	COP0_Channel_Rule_t ch_tmp[COP0_PCA_RULE_NUM];
	COP0_Plane_Rule_t pln_tmp[COP0_PCA_RULE_NUM];
	COP0_Lmu_Rule_t lmu_tmp[COP0_PCA_RULE_NUM];
	COP0_Bank_Rule_t bank_tmp[COP0_PCA_RULE_NUM];
	COP0_Page_Rule_t page_tmp[COP0_PCA_RULE_NUM];
	COP0_Block_Rule_t block_tmp[COP0_PCA_RULE_NUM];
	COP0_Die_Rule_t die_tmp[COP0_PCA_RULE_NUM];
	COP0_Exdie_Rule_t exdie_tmp[COP0_PCA_RULE_NUM];
	COP0_DieIL_Rule_t dieIL_tmp[COP0_PCA_RULE_NUM];
	COP0_Trim_Rule_t trim_tmp;
	U64 uoVB_rule;
	U64 uoSB_rule[COP0_PCA_RULE_NUM];
	U8 ubRuleIdx;

	for (ubRuleIdx = 0; ubRuleIdx < COP0_PCA_RULE_NUM; ubRuleIdx ++) {
		ch_tmp[ubRuleIdx].ubAll = 0;
		ch_tmp[ubRuleIdx].A.ubStartPoint = gPCARule_Channel.ubShift[ubRuleIdx];
		ch_tmp[ubRuleIdx].A.ubLength = gPCARule_Channel.ubBit_No;

		pln_tmp[ubRuleIdx].ubAll = 0;
		pln_tmp[ubRuleIdx].A.ubStartPoint = gPCARule_Plane.ubShift[ubRuleIdx];
		pln_tmp[ubRuleIdx].A.ubLength = gPCARule_Plane.ubBit_No;

		lmu_tmp[ubRuleIdx].ubAll = 0;
		lmu_tmp[ubRuleIdx].A.ubStartPoint = (ubRuleIdx <= COP0_PCA_RULE_1) ? gPCARule_LMU.ubShift[ubRuleIdx] : gPCARule_SLCIdx.ubShift[ubRuleIdx];
		lmu_tmp[ubRuleIdx].A.ubLength = (ubRuleIdx <= COP0_PCA_RULE_1) ? gPCARule_LMU.ubBit_No : gPCARule_SLCIdx.ubBit_No;

		bank_tmp[ubRuleIdx].ubAll = 0;
		bank_tmp[ubRuleIdx].A.ubStartPoint = gPCARule_Bank.ubShift[ubRuleIdx];
		bank_tmp[ubRuleIdx].A.ubLength = gPCARule_Bank.ubBit_No;

#if(MICRON_FSP_EN)
		page_tmp[ubRuleIdx].uwAll = 0;
		page_tmp[ubRuleIdx].A.ubStartPoint = gPCARule_Page.ubShift[ubRuleIdx];
		page_tmp[ubRuleIdx].A.ubLength = (ubRuleIdx <= COP0_PCA_RULE_1) ? gPCARule_Page.ubBit_No : gPCARule_FastPage.ubBit_No;
#else//MICRON_FSP_EN
		page_tmp[ubRuleIdx].uwAll = 0;
		page_tmp[ubRuleIdx].A.ubStartPoint = gPCARule_Page.ubShift[ubRuleIdx];
		page_tmp[ubRuleIdx].A.ubLength = gPCARule_Page.ubBit_No;
#endif//MICRON_FSP_EN

		block_tmp[ubRuleIdx].uwAll = 0;
		block_tmp[ubRuleIdx].A.ubStartPoint = gPCARule_Block.ubShift[ubRuleIdx];
		block_tmp[ubRuleIdx].A.ubLength = gPCARule_Block.ubBit_No;

		die_tmp[ubRuleIdx].ubAll = 0;
		die_tmp[ubRuleIdx].A.ubStartPoint = gPCARule_LUN.ubShift[ubRuleIdx];
		die_tmp[ubRuleIdx].A.ubLength = gPCARule_LUN.ubBit_No;

		exdie_tmp[ubRuleIdx].ubAll = 0;
		exdie_tmp[ubRuleIdx].A.ubStartPoint = gPCARule_ExtLUN.ubShift[ubRuleIdx];
		exdie_tmp[ubRuleIdx].A.ubLength = gPCARule_ExtLUN.ubBit_No;

		trim_tmp.ubAll = 0;
		trim_tmp.A.ubStartPoint = gubTotalPCALen;
		trim_tmp.A.ubLength = 1;

		// DIE_IL???OPT queue selection, ???DIE interleave???????queue, ?????????len = 0, ???????OPT queue?MTP?CE value
		dieIL_tmp[ubRuleIdx].ubAll = 0;
		dieIL_tmp[ubRuleIdx].A.ubStartPoint = gPCARule_Die_IL.ubShift[ubRuleIdx];
		dieIL_tmp[ubRuleIdx].A.ubLength = gPCARule_Die_IL.ubBit_No;

		uoSB_rule[ubRuleIdx] = (((U64) gPCARule_Bank.ulMask) << gPCARule_Bank.ubShift[ubRuleIdx]) |
			(((U64)gPCARule_LUN.ulMask) << gPCARule_LUN.ubShift[ubRuleIdx]) |
			(((U64) gPCARule_Plane.ulMask) << gPCARule_Plane.ubShift[ubRuleIdx]) |
			(((U64) gPCARule_Channel.ulMask) << gPCARule_Channel.ubShift[ubRuleIdx]);
	}

	R8_COP0[R8_COP0_CHANNEL_RULE] = ch_tmp[COP0_PCA_RULE_0].ubAll;
	R8_COP0[R8_COP0_PLANE_RULE] = pln_tmp[COP0_PCA_RULE_0].ubAll;
	R8_COP0[R8_COP0_LMU_RULE] = lmu_tmp[COP0_PCA_RULE_0].ubAll;
	R8_COP0[R8_COP0_BANK_RULE] = bank_tmp[COP0_PCA_RULE_0].ubAll;
	R16_COP0[R16_COP0_PAGE_RULE] = page_tmp[COP0_PCA_RULE_0].uwAll;
	R16_COP0[R16_COP0_BLOCK_RULE] = block_tmp[COP0_PCA_RULE_0].uwAll;
	R8_COP0[R8_COP0_DIE_RULE] = die_tmp[COP0_PCA_RULE_0].ubAll;
	R8_COP0[R8_COP0_EXDIE_RULE] = exdie_tmp[COP0_PCA_RULE_0].ubAll;
	R8_COP0[R8_COP0_TRIM_RULE] = trim_tmp.ubAll;
	R8_COP0[R8_COP0_DIE_IL_RULE] = dieIL_tmp[COP0_PCA_RULE_0].ubAll;
	R32_COP0[R32_COP0_SB_RULE] = uoSB_rule[COP0_PCA_RULE_0];

	R8_COP0[R8_COP0_CHANNEL_RULE_1] = ch_tmp[COP0_PCA_RULE_1].ubAll;
	R8_COP0[R8_COP0_PLANE_RULE_1] = pln_tmp[COP0_PCA_RULE_1].ubAll;
	R8_COP0[R8_COP0_LMU_RULE_1] = lmu_tmp[COP0_PCA_RULE_1].ubAll;
	R8_COP0[R8_COP0_BANK_RULE_1] = bank_tmp[COP0_PCA_RULE_1].ubAll;
	R16_COP0[R16_COP0_PAGE_RULE_1] = page_tmp[COP0_PCA_RULE_1].uwAll;
	R16_COP0[R16_COP0_BLOCK_RULE_1] = block_tmp[COP0_PCA_RULE_1].uwAll;
	R8_COP0[R8_COP0_DIE_RULE_1] = die_tmp[COP0_PCA_RULE_1].ubAll;
	R8_COP0[R8_COP0_EXDIE_RULE_1] = exdie_tmp[COP0_PCA_RULE_1].ubAll;
	R8_COP0[R8_COP0_DIE_IL_RULE_1] = dieIL_tmp[COP0_PCA_RULE_1].ubAll;
	//R8_COP0[R8_COP0_TRIM_RULE_1]    = trim_tmp.all;
	R32_COP0[R32_COP0_SB_RULE_1] = uoSB_rule[COP0_PCA_RULE_1];

	R8_COP0[R8_COP0_CHANNEL_RULE_2] = ch_tmp[COP0_PCA_RULE_2].ubAll;
	R8_COP0[R8_COP0_PLANE_RULE_2] = pln_tmp[COP0_PCA_RULE_2].ubAll;
	R8_COP0[R8_COP0_LMU_RULE_2] = lmu_tmp[COP0_PCA_RULE_2].ubAll;
	R8_COP0[R8_COP0_BANK_RULE_2] = bank_tmp[COP0_PCA_RULE_2].ubAll;
	R16_COP0[R16_COP0_PAGE_RULE_2] = page_tmp[COP0_PCA_RULE_2].uwAll;
	R16_COP0[R16_COP0_BLOCK_RULE_2] = block_tmp[COP0_PCA_RULE_2].uwAll;
	R8_COP0[R8_COP0_DIE_RULE_2] = die_tmp[COP0_PCA_RULE_2].ubAll;
	R8_COP0[R8_COP0_EXDIE_RULE_2] = exdie_tmp[COP0_PCA_RULE_2].ubAll;
	R8_COP0[R8_COP0_DIE_IL_RULE_2] = dieIL_tmp[COP0_PCA_RULE_2].ubAll;
	//R8_COP0[R8_COP0_TRIM_RULE_2]    = trim_tmp.all;
	R32_COP0[R32_COP0_SB_RULE_2] = uoSB_rule[COP0_PCA_RULE_2];

	R8_COP0[R8_COP0_CHANNEL_RULE_3] = ch_tmp[COP0_PCA_RULE_3].ubAll;
	R8_COP0[R8_COP0_PLANE_RULE_3] = pln_tmp[COP0_PCA_RULE_3].ubAll;
	R8_COP0[R8_COP0_LMU_RULE_3] = lmu_tmp[COP0_PCA_RULE_3].ubAll;
	R8_COP0[R8_COP0_BANK_RULE_3] = bank_tmp[COP0_PCA_RULE_3].ubAll;
	R16_COP0[R16_COP0_PAGE_RULE_3] = page_tmp[COP0_PCA_RULE_3].uwAll;
	R16_COP0[R16_COP0_BLOCK_RULE_3] = block_tmp[COP0_PCA_RULE_3].uwAll;
	R8_COP0[R8_COP0_DIE_RULE_3] = die_tmp[COP0_PCA_RULE_3].ubAll;
	R8_COP0[R8_COP0_EXDIE_RULE_3] = exdie_tmp[COP0_PCA_RULE_3].ubAll;
	R8_COP0[R8_COP0_DIE_IL_RULE_3] = dieIL_tmp[COP0_PCA_RULE_3].ubAll;
	//R8_COP0[R8_COP0_TRIM_RULE_3]    = trim_tmp.all;
	R32_COP0[R32_COP0_SB_RULE_3] = uoSB_rule[COP0_PCA_RULE_3];

	/****************** VB, SB rule ******************/

	/*****************************************************************/
	/* PCA = TRIM/BLK/DIE/EXDIE/PAGE/BANK/LMU/PLANE/CHANNEL/ENTRIES  */
	/*****************************************************************/
	if (!MULTIDIE_EN) {
		M_FW_ASSERT(ASSERT_HAL_COP0_0x05B5, gPCARule_LUN.ulMask == 0);
	}
	uoVB_rule = (((U64) gPCARule_Unit.ulMask) << gPCARule_Unit.ubShift[COP0_PCA_RULE_0]);
	R32_COP0[R32_COP0_VB_RULE_L] = uoVB_rule;
	R32_COP0[R32_COP0_VB_RULE_H] = uoVB_rule >> 32;
}

void COP0_RUTSetup(U16 uwLast_SB, U32 ulLast_SB_Offset)
{
	//LAYER1 BASE
	R16_COP0[R16_COP0_BBMP_L1_BASE_ADDR] = RUT_L1_OFF;

	//LAYER2 BASE
	R32_COP0[R32_COP0_BBMP_BASE_ADDR] = (U32)gpulRUT_L2;

	//LAYER2 RULE
	R16_COP0[R16_COP0_BBRMP_L2_VB_NUM_RULE] = (gPCARule_Unit.ubBit_No << COP0_L2_RULE_LENGTH_SHIFT) + RUT_L2_RULE_VB_START_BIT;
	R16_COP0[R16_COP0_BBRMP_L2_PB_NUM_RULE] = (gPCARule_Block.ubBit_No << COP0_L2_RULE_LENGTH_SHIFT) + RUT_L2_RULE_PB_START_BIT;
	//Note: Must check FW SuperBlock sequece - Plane, CH, CE
	R16_COP0[R16_COP0_BBRMP_L2_CH_NUM_RULE] = (gPCARule_Channel.ubBit_No << COP0_L2_RULE_LENGTH_SHIFT) + (RUT_L2_RULE_PBCE_START_BIT);
	R16_COP0[R16_COP0_BBRMP_L2_CE_NUM_RULE] = (gPCARule_Bank.ubBit_No << COP0_L2_RULE_LENGTH_SHIFT)
		+ (RUT_L2_RULE_PBCE_START_BIT + gPCARule_Channel.ubBit_No);
	R16_COP0[R16_COP0_BBRMP_L2_PLN_NUM_RULE] = (gPCARule_Plane.ubBit_No << COP0_L2_RULE_LENGTH_SHIFT)
		+ (RUT_L2_RULE_PBCE_START_BIT + gPCARule_Channel.ubBit_No + gPCARule_Bank.ubBit_No);
	R16_COP0[R16_COP0_BBRMP_L2_LUN_NUM_RULE] = (gPCARule_LUN.ubBit_No << COP0_L2_RULE_LENGTH_SHIFT)
		+ (RUT_L2_RULE_PBCE_START_BIT + gPCARule_Channel.ubBit_No + gPCARule_Bank.ubBit_No + gPCARule_Plane.ubBit_No);

	R16_COP0[R16_COP0_BBRMP_L2_DIL_NUM_RULE] = 0x0000;
	R16_COP0[R16_COP0_LAST_SB] = uwLast_SB;
	R16_COP0[R16_COP0_LAST_SB_OFST] = ulLast_SB_Offset;

	///RUT Enable
	R32_COP0[R32_COP0_CR_BBMP_EN] |= COP0_CR_BBMP_EN_BIT;
}

//void hal_cop0_reg_setup(void)
void COP0_RegSetup(void)
{
	COP0_Link_t link_tmp;
	COP0_Attr_t attr_def_0;
	COP0_Clksw_t clksw_def_0;

	// slc entry rule and tlc entry rule are smae.
	if (gPCARule_Entry.ubBit_No == 2) {
		R8_COP0[R8_COP0_ENTRY_RULE] = COP0_ENTRY_16K_PAGE;
	}
	else if (gPCARule_Entry.ubBit_No == 1) {
		R8_COP0[R8_COP0_ENTRY_RULE] = COP0_ENTRY_8K_PAGE;
	}
	else {
		R8_COP0[R8_COP0_ENTRY_RULE] = COP0_ENTRY_4K_PAGE;
	}

	/****************** CREG_LINK rule ******************/

	link_tmp.ulAll = 0x00;
	link_tmp.A.btWrr_en = COP0_LINK_WRR_EN;
	link_tmp.A.ubHighWrrWeight = COP0_LINK_HIGH_WRR_WEIGHT;
	link_tmp.A.ubMidWrrWeight = COP0_LINK_MID_WRR_WEIGHT;
	R32_COP0[R32_COP0_LINK] = link_tmp.ulAll; // set 8 pages limit for 4 templates,weight fake input 2, and last bits not to enable rr algo.
	R32_COP0[R32_COP0_HIGH01_WEIGHT] = COP0_LINK_HIGH1_WEIGHT_SET(COP0_LINK_HIGH1_WEIGHT) | COP0_LINK_HIGH0_WEIGHT_SET(COP0_LINK_HIGH0_WEIGHT);

	/****************** BBMP Configuration ******************/
	if (CONFIG_BBMP_EN) {
		// delete code...that move to COP0_RUTSetup of init flow
	}
	else {
		R32_COP0[R32_COP0_CR_BBMP_EN] &= (~COP0_CR_BBMP_EN_BIT);
	}

	if (CONFIG_VBRMP_EN) {
		//hal_cop0_set_vbrmp_base_addr(DBUF_VBRMP);
		COP0_SetVBRMP_BaseAddr((U64) gpuwVBRMP);
		M_COP0_SET_VBRMP_EN_BIT();
	}
	else {
		R8_COP0[R8_COP0_VBRMP_EN] &= (~COP0_VBRMP_EN_BIT);
	}
#if (PS5021_EN || PS5017_EN)
	COP0_SetP4K_GC_BaseAddr(DBUF_P4K_GC);
#endif /* (PS5021_EN || PS5017_EN) */

	/****************** RAM Initial ******************/
	// MT_ZONE
	R32_COP0[R32_COP0_CMSG_1] &= (~(COP0_MT_ZONE_LEN_MASK << COP0_MT_ZONE_LEN_SHIFT));
	R32_COP0[R32_COP0_CMSG_1] |= ((MT_LEN / MT_PARA_LEN) << COP0_MT_ZONE_LEN_SHIFT);
	R32_COP0[R32_COP0_CMSG_0] &= (~(COP0_QOS_MT_ZONE_LEN_MASK << COP0_QOS_MT_ZONE_LEN_SHIFT));
	R32_COP0[R32_COP0_CMSG_0] |= ((MT_QOS_LEN / MT_PARA_LEN) << COP0_QOS_MT_ZONE_LEN_SHIFT);

	// L4k_ZONE
	R32_COP0[R32_COP0_CMSG_0] &= (~(COP0_L4K_ZONE_OFS_MASK << COP0_L4K_ZONE_SHIFT));
	R32_COP0[R32_COP0_CMSG_0] |= SPR_OFF << COP0_L4K_ZONE_SHIFT;
	R32_COP0[R32_COP0_CMSG_1] &= (~(COP0_QOS_L4K_ZONE_MASK << COP0_QOS_L4K_ZONE_SHIFT));
	R32_COP0[R32_COP0_CMSG_1] |= SPR_QOS_OFF << COP0_QOS_L4K_ZONE_SHIFT;

	// Reset MT BASE ADDR
#if PS5021_EN
	R32_FCON[R32_FCON_MT_ADR_BASE] = MT_GLB_TRIG_EN_BIT | MT_OFF | ((MT_FORMAT_40BYTE & MT_TABLE_SIZE_MASK) << MT_TABLE_SIZE_SHIFT);    /* If target cpu is cop0, don't enable index mode*/
#else /* PS5021_EN */
	R32_FCON[R32_FCON_MT_ADR_BASE] = MT_GLB_TRIG_EN_BIT | MT_OFF;    /* If target cpu is cop0, don't enable index mode*/
#endif /* PS5021_EN */

	// Reset QOS BASE ADDR
	R32_FCON[R32_FCON_QOS_MT_ADR_BASE] = MT_QOS_OFF;

	// ****************** Tie Out Response ***************************
	R8_COP0[R8_COP0_CR_IRAM_NOT_RLS_CFG] = (COP0_CQ_MSG_PFA_BIT | COP0_CQ_MSG_MT_STOP_BIT | COP0_CQ_MSG_STA_BIT | COP0_CQ_MSG_EOT_BIT);
	R8_COP0[R8_COP0_CR_ERR_MSG_CFG] = (COP0_CQ_MSG_PFA_BIT | COP0_CQ_MSG_MT_STOP_BIT | COP0_CQ_MSG_STA_BIT | COP0_CQ_MSG_EOT_BIT);
	R8_COP0[R8_COP0_CR_ERR_INT_FORMAT_CFG] = (COP0_CQ_MSG_PFA_BIT | COP0_CQ_MSG_MT_STOP_BIT | COP0_CQ_MSG_STA_BIT | COP0_CQ_MSG_EOT_BIT);
	R8_COP0[R8_COP0_CR_TIEOUT_ENABLE] = (COP0_CQ_MSG_PFA_BIT | COP0_CQ_MSG_MT_STOP_BIT | COP0_CQ_MSG_STA_BIT | COP0_CQ_MSG_EOT_BIT | COP0_CQ_MSG_DMA_DONE_BIT | COP0_CQ_MSG_ALL_DONE_BIT);

	R8_COP0[R8_COP0_CR_ERR_CPU] = COP0_ERR_CQ_MSG_TO_CPU0_BIT; /* The ERR_CPU which receive the ERR_CPU_INT. */

	R32_COP0[R32_COP0_CMSG_3] |= COP0_CR_CQ_BLOCK_EN_0_BIT;
	R8_COP0[R8_COP0_CR_CQ_BLOCK_INT_PTN_0] = COP0_CQ_MSG_DMA_DONE_BIT;

	R32_COP0[R32_COP0_CMSG_3] |= COP0_CR_CQ_BLOCK_EN_2_BIT;
	R8_COP0[R8_COP0_CR_CQ_BLOCK_INT_PTN_2] = COP0_CQ_MSG_PARTIAL_DONE_BIT;

	R32_FALL[R32_FCTL_INT_CFG] &= (~OVR_ERR_INT_EN_BIT); /* ECC Over Bits */

	// *************************************************************

	/****************** Tie Out Response ******************/
	R32_FCON[R32_FCON_SYS_INTQ_BASE2] = (U32) &R32_COP0[R32_COP0_CQ_MSG0]; /* This Register is used to control IntMsg queue base address of system COP0. */

	//****************** FIP ADG TLC Rule Setting, shift 8 bit for FIP iFSA hidden bit ***************************
	if (gPCARule_LMU.ubBit_No) {
		R32_FCON[R32_FCON_ADR_GEN] |= (TLC_PTR_EN_0_BIT | ((((U32)gPCARule_LMU.ubShift[COP0_PCA_RULE_0] + IFSA_HIDDEN_BIT_NUM) & TLC_PTR_LOC_MASK) << TLC_PTR_LOC_SHIFT));
	}

	/****************** Attribute for optimizer form MT template - 0 ******************/
	/*** For RS Decode Bug, Grp 0 (having 4 tag) is empty, data unit use grp1 , gc use grp2, and so on **/
	attr_def_0.ulAll = 0UL;
	attr_def_0.A.ubRs_grp_num = 1; //data??group1
	attr_def_0.A.btUltra_dma_dis = FALSE; //ws: 1->0
#if (!PS5021_EN)
	attr_def_0.A.btUltra_w_en = TRUE;
#endif /* (!PS5021_EN) */
	if (NCS_EN) {
		attr_def_0.A.btCrcChk_dis = TRUE;
		attr_def_0.A.btUltra_dma_dis = TRUE;
		attr_def_0.A.btUltra_w_en = FALSE;
	}
	attr_def_0.A.btCmp_en = FALSE;
	attr_def_0.A.btBuf_mode = COP0_BUF_MODE_LBPB;
	R32_COP0[R32_COP0_ATTR0] = attr_def_0.ulAll;

	/*read*/
	attr_def_0.A.btUltra_w_en = FALSE;
	/* // use trunk code gXZIP.ubGCXZIPEn and gXZIP.ubXZIPEn so remove setting at here
	if (XZIP_EN) {
		attr_def_0.A.btCmp_en = FALSE;
	}
	else {
		attr_def_0.A.btCmp_en = LCA_COMPARE_EN;
	}
	*/
	R32_COP0[R32_COP0_ATTR1] = attr_def_0.ulAll;

	// for Gen R fail
	R32_COP0[R32_COP0_ATTR6] = attr_def_0.ulAll | BIT0;

	attr_def_0.A.btCmp_en = FALSE;
	attr_def_0.A.ubRs_grp_num = 2; //GC??group2
#if (!PS5021_EN)
	attr_def_0.A.btUltra_w_en = TRUE;   //ws: open
#endif /* (!PS5021_EN) */
	if (NCS_EN) {
		attr_def_0.A.btUltra_w_en = FALSE;
	}
	R32_COP0[R32_COP0_ATTR4] = attr_def_0.ulAll; //RS GC encode

	attr_def_0.A.ubRs_grp_num = 1; //data??group1

	attr_def_0.A.btBuf_mode = COP0_BUF_MODE_ADDR;
	R32_COP0[R32_COP0_ATTR10] = attr_def_0.ulAll;

	attr_def_0.A.btCmp_en = LCA_COMPARE_EN;
	attr_def_0.A.btUltra_w_en = FALSE;

	R32_COP0[R32_COP0_ATTR2] = attr_def_0.ulAll; //For Read

	// for Gen R fail
	R32_COP0[R32_COP0_ATTR3] = attr_def_0.ulAll | BIT0;

	attr_def_0.A.btCmp_en = FALSE;
	R32_COP0[R32_COP0_ATTR11] = attr_def_0.ulAll;
	attr_def_0.A.btCmp_en = LCA_COMPARE_EN;

	//write for table and gc
	attr_def_0.A.btCmp_en = FALSE;
	attr_def_0.A.ubRs_grp_num = 2; //GC??group2
#if (!PS5021_EN)
	attr_def_0.A.btUltra_w_en = TRUE;   //ws: open
#endif /* (!PS5021_EN) */
	if (NCS_EN) {
		attr_def_0.A.btUltra_w_en = FALSE;
	}
	R32_COP0[R32_COP0_ATTR5] = attr_def_0.ulAll; //RS GC encode(PTEbitmap)

	attr_def_0.A.ubRs_grp_num = 3; //table??group3
#if (!PS5021_EN)
	attr_def_0.A.btUltra_w_en = TRUE;   //ws: open
#endif /* (PS5021_EN) */
	R32_COP0[R32_COP0_ATTR7] = attr_def_0.ulAll; //RS table encode

	attr_def_0.A.ubRs_grp_num = 4;//initinfo??group4
#if (!PS5021_EN)
	attr_def_0.A.btUltra_w_en = TRUE;   //ws: open
#endif /* (!PS5021_EN) */
	R32_COP0[R32_COP0_ATTR13] = attr_def_0.ulAll;//RS initinfo encode

	attr_def_0.ulAll = 0UL;
	if (NCS_EN) {
		attr_def_0.A.btCrcChk_dis = TRUE;
		attr_def_0.A.btUltra_dma_dis = TRUE;
		attr_def_0.A.btUltra_w_en = FALSE;
	}
	attr_def_0.A.ubRs_grp_num = 1; //data??group0
	attr_def_0.A.btBuf_mode = COP0_BUF_MODE_ADDR;
	attr_def_0.A.btCmp_en = LCA_COMPARE_EN;
	attr_def_0.A.btUltra_w_en = FALSE;
	R32_COP0[R32_COP0_ATTR12] = attr_def_0.ulAll; //For Read SystemArea with LCA Cmp

	attr_def_0.ulAll = 0UL;
	attr_def_0.A.btUltra_dma_dis = FALSE;
#if (!PS5021_EN)
	attr_def_0.A.btUltra_w_en = TRUE;
#endif /* (!PS5021_EN) */
	if (NCS_EN) {
		attr_def_0.A.btCrcChk_dis = TRUE;
		attr_def_0.A.btUltra_dma_dis = TRUE;
		attr_def_0.A.btUltra_w_en = FALSE;
	}
	attr_def_0.A.btCmp_en = FALSE;
	attr_def_0.A.btBuf_mode = COP0_BUF_MODE_LBPB;
	attr_def_0.A.ubRs_grp_num = 2; //copy unit use GC tag
	R32_COP0[R32_COP0_ATTR8] = attr_def_0.ulAll; //For copy unit

	attr_def_0.A.ubRs_grp_num = 5; //copy table use GC tag????group 5
	R32_COP0[R32_COP0_ATTR14] = attr_def_0.ulAll; //For copy tabe

	attr_def_0.A.ubRs_grp_num = 2; //copy unit use GC tag
	attr_def_0.A.btCrcChk_dis = TRUE;
#if (PS5017_EN)
	attr_def_0.A.bteot_chk_en = (READ_DISTURB_EN || MEDIA_SCAN_EN); // for ReadVerify trigger OverECC INT to errhandle copy unit
#endif /* (PS5017_EN) */
	R32_COP0[R32_COP0_ATTR9] = attr_def_0.ulAll; //For copy unit program + copy unit with block mode (read RS page)

	attr_def_0.ulAll             = 0UL;
	if (NCS_EN) {
		attr_def_0.A.btCrcChk_dis = TRUE;
		attr_def_0.A.btUltra_dma_dis = TRUE;
		attr_def_0.A.btUltra_w_en = FALSE;
	}
	attr_def_0.A.btCmp_en = LCA_COMPARE_EN;
	attr_def_0.A.btBuf_mode = COP0_BUF_MODE_ADDR;
	attr_def_0.A.ubRs_grp_num = 1; //copy unit use GC tag
	R32_COP0[R32_COP0_ATTR15] = attr_def_0.ulAll; //For copy unit

	/****************** Clock switch attribute for optimizer form MT template - 0 ******************/
	clksw_def_0.ulAll = 0UL;
	// skip SET_IF_DEFAULT case. Because default interfce of nand are only legacy & toggle for TSB and legacy for MICRON / HYNIX (except ONFI 4 nand). These interface use sigle end.
	if (!gFlhEnv.ulFlashDefaultType.BitMap.btDifferential) {
		clksw_def_0.A.btIo_typ = 1; //single end.
	}

	//TODO : Check

	if (gFlhEnv.ulFlashDefaultType.BitMap.btToggleNand) {
		/* Bit[9:8]: flh_typ 0:legacy, 2:toggle, 3:onfi */
#if(MST_MODE_EN)
		clksw_def_0.A.ubFlh_typ = 2;
#else
		if (gFlhEnv.ulFlashDefaultType.BitMap.VendorType == TOSHIBASANDISK) {
			clksw_def_0.A.ubFlh_typ = 2;
		}
		else if (gFlhEnv.ulFlashDefaultType.BitMap.VendorType == INTELMICRON) {
			clksw_def_0.A.ubFlh_typ = 2;
		}
		else if (gFlhEnv.ulFlashDefaultType.BitMap.VendorType == SKHYNIX) {//Dylan for V6 RDT porting,for COP0 error
			clksw_def_0.A.ubFlh_typ = 2;
		}
		else if (gFlhEnv.ulFlashDefaultType.BitMap.VendorType == YMTC) {
			clksw_def_0.A.ubFlh_typ = 2;
		}
#endif
	}
#if PS5017_EN
	switch (gFlhEnv.ubTargetFlashClock) {
	case FIP_FLASH_CLOCK_700MHZ:
	case FIP_FLASH_CLOCK_600MHZ:
	case FIP_FLASH_CLOCK_533MHZ:
	case FIP_FLASH_CLOCK_400MHZ:
	case FIP_FLASH_CLOCK_333MHZ:
	case FIP_FLASH_CLOCK_266MHZ:
	case FIP_FLASH_CLOCK_225MHZ:
	case FIP_FLASH_CLOCK_200MHZ:
		if ((IM_B47R || IM_N48R || IM_B37R) && (gFlhEnv.ubTargetFlashClock > FIP_FLASH_CLOCK_200MHZ)) {
			clksw_def_0.A.ubFclkDiv = 0;
		}
		else {
			clksw_def_0.A.ubFclkDiv = 0;
		}
		break;
	case FIP_FLASH_CLOCK_100MHZ:
		clksw_def_0.A.ubFclkDiv = 2;
		break;
	case FIP_FLASH_CLOCK_41P7MHZ:
	case FIP_FLASH_CLOCK_33MHZ:
		clksw_def_0.A.ubFclkDiv = 3;
		break;
	case FIP_FLASH_CLOCK_10MHZ:
		clksw_def_0.A.ubFclkDiv = 5;
		break;
	default:
		M_FW_ASSERT(ASSERT_HAL_COP0_0x05B6, FALSE);
	}
#endif /* PS5017_EN */

	R32_COP0[R32_COP0_CLKSW_ATTR0] = clksw_def_0.ulAll;

	/****************** CREG_MT_OPTION ******************/
	R32_COP0[R32_COP0_MT_OPTION] |= COP0_CE_SEL_MODE_BIT;

	/****************** Decoder Mode ******************/
	//Set Normal mode
	R32_COP0[R32_COP0_CE_DECODER_MODE] &= (~COP0_CE_DECODER_MODE_EN_BIT);

	/****************** Set PLB ID  ******************/
	//R32_COP0[R32_COP0_DBG_PORT] |= LB_ID_PROGRAM_0;
	//Merge0 PLB
	R32_COP0[R32_COP0_MERGE_CFG] |= (LB_ID_PROGRAM_0 & COP0_MERGE0_ALCT_ID_MASK) << COP0_MERGE0_ALCT_ID_SHIFT;
	//Merge1 PLB
	R32_COP0[R32_COP0_MERGE_CFG] |= (LB_ID_PROGRAM_1 & COP0_MERGE1_ALCT_ID_MASK) << COP0_MERGE1_ALCT_ID_SHIFT;

	/****************** Set UD_MISS_MODE 0/CLEAN_UD_EN	******************/
	//Clear Userdefine bit
	R32_COP0[R32_COP0_CSCH_0] &= ~COP0_UD_MISS_MODE_MASK;
	R32_COP0[R32_COP0_CSCH_0] |= COP0_UD_MISS_MODE_0;
	R32_COP0[R32_COP0_CSCH_0] |= COP0_CLEAR_UD_EN;

	R32_COP0[R32_COP0_DUMMY_L4K_0] = SPARE_LCA_DUMMY;
	R32_COP0[R32_COP0_DUMMY_L4K_1] = 0;
	R32_COP0[R32_COP0_DUMMY_L4K_2] = 0;
	R32_COP0[R32_COP0_MERGE_CFG] |= COP0_DUMMY_SPRVLD_BIT;



	return;
}

//void hal_cop0_reg_check(void)
void COP0_RegCheck(void)
{
	U8 ubRuleIdx, ubRegOffset;

	M_UART(COP0_, "COP0_CHANNEL_RULE: 0x%x\n", R8_COP0[R8_COP0_CHANNEL_RULE]);
	M_UART(COP0_, "COP0_PLANE_RULE  : 0x%x\n", R8_COP0[R8_COP0_PLANE_RULE]);
	M_UART(COP0_, "COP0_LMU_RULE    : 0x%x\n", R8_COP0[R8_COP0_LMU_RULE]);
	M_UART(COP0_, "COP0_BANK_RULE   : 0x%x\n", R8_COP0[R8_COP0_BANK_RULE]);
	M_UART(COP0_, "COP0_PAGE_RULE   : 0x%x\n", R16_COP0[R16_COP0_PAGE_RULE]);
	M_UART(COP0_, "COP0_BLOCK_RULE  : 0x%x\n", R16_COP0[R16_COP0_BLOCK_RULE]);
	M_UART(COP0_, "COP0_DIE_RULE    : 0x%x\n", R8_COP0[R8_COP0_DIE_RULE]);
	M_UART(COP0_, "COP0_DIE_IL_RULE : 0x%x\n", R8_COP0[R8_COP0_DIE_IL_RULE]);
	M_UART(COP0_, "COP0_EXDIE_RULE  : 0x%x\n", R8_COP0[R8_COP0_EXDIE_RULE]);

	for (ubRuleIdx = COP0_PCA_RULE_1; ubRuleIdx < COP0_PCA_RULE_NUM; ubRuleIdx++) {
		ubRegOffset = (ubRuleIdx - 1) * 0x10; // PCA Rule 1~3 offset gap is 0x10
		M_UART(COP0_, "COP0_CHANNEL_RULE_%b: 0x%x\n", ubRuleIdx, R8_COP0[R8_COP0_CHANNEL_RULE_1 + ubRegOffset]);
		M_UART(COP0_, "COP0_PLANE_RULE_%b  : 0x%x\n", ubRuleIdx, R8_COP0[R8_COP0_PLANE_RULE_1 + ubRegOffset]);
		M_UART(COP0_, "COP0_LMU_RULE_%b    : 0x%x\n", ubRuleIdx, R8_COP0[R8_COP0_LMU_RULE_1 + ubRegOffset]);
		M_UART(COP0_, "COP0_BANK_RULE_%b   : 0x%x\n", ubRuleIdx, R8_COP0[R8_COP0_BANK_RULE_1 + ubRegOffset]);
		M_UART(COP0_, "COP0_PAGE_RULE_%b   : 0x%x\n", ubRuleIdx, R16_COP0[R16_COP0_PAGE_RULE_1 + (ubRegOffset >> 1)]);
		M_UART(COP0_, "COP0_BLOCK_RULE_%b  : 0x%x\n", ubRuleIdx, R16_COP0[R16_COP0_BLOCK_RULE_1 + (ubRegOffset >> 1)]);
		M_UART(COP0_, "COP0_DIE_RULE_%b    : 0x%x\n", ubRuleIdx,	R8_COP0[R8_COP0_DIE_RULE_1 + ubRegOffset]);
		M_UART(COP0_, "COP0_EXDIE_RULE_%b  : 0x%x\n", ubRuleIdx, R8_COP0[R8_COP0_EXDIE_RULE_1 + ubRegOffset]);
	}

	M_UART(COP0_, "COP0_TRIM_RULE   : 0x%x\n", R8_COP0[R8_COP0_TRIM_RULE]);
	M_UART(COP0_, "COP0_LINK        : 0x%x\n", R32_COP0[R32_COP0_LINK]);
	M_UART(COP0_, "COP0_VB_RULE_L   : 0x%x\n", R32_COP0[R32_COP0_VB_RULE_L]);
	M_UART(COP0_, "COP0_VB_RULE_H   : 0x%x\n", R32_COP0[R32_COP0_VB_RULE_H]);
	M_UART(COP0_, "COP0_SB_RULE     : 0x%x\n", R32_COP0[R32_COP0_SB_RULE]);
	M_UART(COP0_, "COP0_SB_RULE_1   : 0x%x\n", R32_COP0[R32_COP0_SB_RULE_1]);
	M_UART(COP0_, "COP0_SB_RULE_2   : 0x%x\n", R32_COP0[R32_COP0_SB_RULE_2]);
	M_UART(COP0_, "COP0_SB_RULE_3   : 0x%x\n", R32_COP0[R32_COP0_SB_RULE_3]);

	M_UART(COP0_, "ATTR0: io_typ=%d\n", R32_COP0[R32_COP0_CLKSW_ATTR0] & BIT0);
	M_UART(COP0_, "ATTR0: fclk_div_en=%d\n", R32_COP0[R32_COP0_CLKSW_ATTR0] & BIT1);
	M_UART(COP0_, "ATTR0: fclk_div=%d\n", (R32_COP0[R32_COP0_CLKSW_ATTR0] >> 2) & 0x3F);
	M_UART(COP0_, "ATTR0: flh_typ=%d\n", (R32_COP0[R32_COP0_CLKSW_ATTR0] >> 8) & 0x03);
	M_UART(COP0_, "ATTR0: tim_cfg_sel=%d\n", R32_COP0[R32_COP0_CLKSW_ATTR0] & BIT10);

	M_UART(COP0_, "R16_COP0_CR_L4K_ZONE_OFS=0x%x\n", R32_COP0[R32_COP0_CMSG_1] & (COP0_QOS_L4K_ZONE_MASK << COP0_QOS_L4K_ZONE_SHIFT));

	M_UART(COP0_, "FCON_SYS_INTQ_BASE_2=0x%x\n", R32_FCON[R32_FCON_SYS_INTQ_BASE2]);

	if (R32_FCON[R32_FCON_SYS_INTQ_BASE2] != 0x00C00330) {
		while (1);  // error.
	}

}

/*!
 @brief   Set VBRMP base addr
 @param[in] addr The VBRMP base addr
 @return None
 @note
 - NONE
*/
//void hal_cop0_set_vbrmp_base_addr(U64 addr)
void COP0_SetVBRMP_BaseAddr(U64 uoAddr)
{
	R32_COP0[R32_COP0_VBRMP_BASE] = uoAddr;
}
#if (PS5021_EN || PS5017_EN)
void COP0_SetP4K_GC_BaseAddr(U32 ulAddr)
{
	R32_COP0[R32_COP0_DBUF_GC_BASE] = ulAddr;
}
#endif /* (PS5021_EN || PS5017_EN) */

//void hal_cop0_init_cpu(void)
void COP0_Init_CPU(void)
{
	/*
	#if (CONFIG_TARGET_PRID == PRID_CORE0)
	    g8_sys_cpu_num = 0;
	#else
	    g8_sys_cpu_num = 1;
	#endif

	    if (g8_sys_cpu_num == 0) {
	        g8_cpu_ret_sel = NORMAL_RESPONSE_CPU0;

	        srand(1);  //diff. seed.

	    } else if (g8_sys_cpu_num == 1) {
	        g8_cpu_ret_sel = NORMAL_RESPONSE_CPU1;

	        srand(2);  //diff. seed.

	    }
	    return;
	 */
}

//-------------------------------------------------------------------
// Function : fpl_cop0_set_div_rule
// Description : DIV Rule converts FW VCA entry to True PCA entry

// Input  : N/A
// return : N/A
//-------------------------------------------------------------------
//void hal_cop0_set_div_rule()
void COP0_SetDivRule()
{
	DIV_RULE_STRUCT_t CE, Channel, Page, LMU;
	U8 ubRuleIdx;
	U16 uwDivRuleNum, uwDivRuleStartOffset;
	U8 ubEndShift;
	// Set Div Rules
	CE.ulAll = 0;
	Channel.ulAll = 0;
	Page.ulAll = 0;
	LMU.ulAll = 0;
	uwDivRuleNum = 0;
	uwDivRuleStartOffset = 0;

	for (ubRuleIdx = 0; ubRuleIdx < COP0_PCA_RULE_NUM; ubRuleIdx++) {
		uwDivRuleStartOffset += uwDivRuleNum; // shift div rule start offset
		uwDivRuleNum = 0; // reset div rule number
		ubEndShift   = (UTILIZE_SLC_UNUSE_PCA_BIT_EN) ? (gPCARule_Page.ubShift[ubRuleIdx] + gPCARule_Page.ubBit_No) : gPCARule_Unit.ubShift[ubRuleIdx];
		if (ODD_CE_DBG_UART_EN) {
			M_UART(COP0_, "\n-----------------------------------\n");
		}
		if (ubRuleIdx == COP0_PCA_RULE_1) {
			continue; // no need to set D1 TLC
		}

#if(MICRON_FSP_EN)
		// for Micron TLC/QLC (do not execute div rules)
		if (COP0_PCA_RULE_0 == ubRuleIdx) {
			// assign 0 to register (clear default value of the register)
			R8_COP0[R8_COP0_SLC_D1_RULE_0 + ubRuleIdx] = (uwDivRuleNum << COP0_NUM_RULE_SHIFT) + uwDivRuleStartOffset;
			continue;
		}
#endif//MICRON_FSP_EN

		if (ubRuleIdx < COP0_PCA_RULE_1) {
			if (gPCARule_LMU.ubBit_No) {
				// Parse LMU info
				LMU.A.ubRemainder = gPCARule_LMU.ubShift[ubRuleIdx];
				LMU.A.ubDividend = ((UTILIZE_SLC_UNUSE_PCA_BIT_EN) ? gPCARule_Page.ubShift[ubRuleIdx] : ubEndShift) - gPCARule_LMU.ubShift[ubRuleIdx];
				LMU.A.ubQuotinet = LMU.A.ubRemainder + gPCARule_LMU.ubBit_No;
				LMU.A.uwDivisor = gubLMUNumber;
				if (ODD_CE_DBG_UART_EN) {
					M_UART(COP0_, " odd lmu r: %x ,d: %x ,q: %x ,d: %x\n", LMU.A.ubRemainder, LMU.A.ubDividend, LMU.A.ubQuotinet, LMU.A.uwDivisor);
				}
				R32_COP0[R32_COP0_VBMP_DIV_RULE_0 + uwDivRuleStartOffset + uwDivRuleNum] = LMU.ulAll;
				uwDivRuleNum++;
			}
		}



		// Odd CE, Parse CE info
		CE.A.ubRemainder = gPCARule_Channel.ubShift[ubRuleIdx];
		CE.A.ubDividend = ubEndShift - gPCARule_Channel.ubShift[ubRuleIdx];
		CE.A.ubQuotinet = gPCARule_Bank.ubShift[ubRuleIdx] + gPCARule_Bank.ubBit_No;
		CE.A.uwDivisor = gubCENumber;
		if (ODD_CE_DBG_UART_EN) {
			M_UART(COP0_, " odd ce r: %x ,d: %x ,q: %x ,d: %x\n", CE.A.ubRemainder, CE.A.ubDividend, CE.A.ubQuotinet, CE.A.uwDivisor);
		}
		R32_COP0[R32_COP0_VBMP_DIV_RULE_0 + uwDivRuleStartOffset + uwDivRuleNum] = CE.ulAll;
		uwDivRuleNum++;
		if (((U32)1 << gPCARule_Bank.ubBit_No) != (gFlhEnv.ubCENumber / gFlhEnv.ubChannelExistNum)) {
			//Support Odd Parallel Mode Later???
			Channel.A.ubRemainder = gPCARule_Channel.ubShift[ubRuleIdx];
			Channel.A.ubDividend = gPCARule_Page.ubShift[ubRuleIdx] - gPCARule_Channel.ubShift[ubRuleIdx];
			Channel.A.ubQuotinet = Channel.A.ubRemainder + gPCARule_Channel.ubBit_No;
			Channel.A.uwDivisor = gubPlanesPerBurst;
			if (ODD_CE_DBG_UART_EN) {
				M_UART(COP0_, " odd ch r: %x ,d: %x ,q: %x ,d: %x\n", Channel.A.ubRemainder, Channel.A.ubDividend, Channel.A.ubQuotinet, Channel.A.uwDivisor);
			}
			R32_COP0[R32_COP0_VBMP_DIV_RULE_0 + uwDivRuleStartOffset + uwDivRuleNum] = Channel.ulAll;
			uwDivRuleNum++;
		}

		if (((U32)1 << gPCARule_Page.ubBit_No) != guwSuperPagesPerUnit) {
			// guwPagePerUnit is NOT power of 2, Parse Page info
			Page.A.ubRemainder = gPCARule_Page.ubShift[ubRuleIdx];
			Page.A.ubDividend = ubEndShift - gPCARule_Page.ubShift[ubRuleIdx];
#if(MICRON_FSP_EN)
			Page.A.ubQuotinet = Page.A.ubRemainder + ((ubRuleIdx > COP0_PCA_RULE_1) ? gPCARule_FastPage.ubBit_No : gPCARule_Page.ubBit_No);
			Page.A.uwDivisor = (ubRuleIdx > COP0_PCA_RULE_1) ?  guwFastPagePlanesPerBlock : guwSuperPagesPerUnit;
#else//MICRON_FSP_EN
			Page.A.ubQuotinet = Page.A.ubRemainder + gPCARule_Page.ubBit_No;
			Page.A.uwDivisor = (ubRuleIdx > COP0_PCA_RULE_1) ? (guwSuperPagesPerUnit / gubLMUNumber) : guwSuperPagesPerUnit;
#endif//MICRON_FSP_EN
			if (ODD_CE_DBG_UART_EN) {
				M_UART(COP0_, " odd unit  r: %x ,d: %x ,q: %x ,d: %x\n", Page.A.ubRemainder, Page.A.ubDividend, Page.A.ubQuotinet, Page.A.uwDivisor);
			}
			R32_COP0[R32_COP0_VBMP_DIV_RULE_0 + uwDivRuleStartOffset + uwDivRuleNum] = Page.ulAll;
			uwDivRuleNum++;
		}

		R8_COP0[R8_COP0_SLC_D1_RULE_0 + ubRuleIdx] = (uwDivRuleNum << COP0_NUM_RULE_SHIFT) + uwDivRuleStartOffset;
	}

}

#if (RDT_MODE_EN && FW_CATEGORY_CUSTOMER == CUSTOMER_MAINSTREAM)//rdt_express_read
void COP0_Init_Param_RDT()
{
	guoHostReadUserData0.uoAll = 0; //Clear Qbody
	guoHostReadUserData0.A.btD1 			= FALSE;
	//guoHostReadUserData0.A.btSLCMode		= ubIsSLCMode;
	guoHostReadUserData0.A.ubCmd       		= COP0_CMD_READ;
	guoHostReadUserData0.A.btRUTBps  		= TRUE;
	guoHostReadUserData0.A.btLock			= FALSE;	//Default: FALSE
	guoHostReadUserData0.A.btFlush   		= FALSE;	//Default: FALSE
	guoHostReadUserData0.A.btRemapBps		= TRUE;
	guoHostReadUserData0.A.ubTargetID  		= COP0_TARGET_CPU;
	//guoHostReadUserData0.A.uwTagID			= uwSendTagID;
	guoHostReadUserData0.A.ubL4KNum    		= 0;
	guoHostReadUserData0.A.btSLCSetMethod	= COP0_TIE_D1SLC_MODE_FROM_TIEIN;
	guoHostReadUserData0.A.btAttrGetPBNA		= 0;
	guoHostReadUserData0.A.btAttrReadTieOutMethod = FALSE; //Default: FALSE
	guoHostReadUserData0.A.btAttrReadBackUp4K = FALSE;
	guoHostReadUserData0.A.btAttrCLKTemplate = COP0_CLKSW_ATTR0;
	//guoHostReadUserData0.A.btAttrNorCQResp	= pCOP0ReadSQPara->UserData0.btNeedCQ;
	guoHostReadUserData0.A.ubAttrPriority	= COP0_LINK_PIORITY_LOW;
	guoHostReadUserData0.A.btAttrSerial		= 0;
	guoHostReadUserData0.A.btAttrZIPEn		= 0;//ZIP_EN;
	guoHostReadUserData0.A.btAttrQOS		= 0;		//Default: FALSE

	//test skip
	guoHostReadUserData0.A.ubAttrMTTemplate = COP0_MT_TEMP_BUF_ADDR_READ;
	guoHostReadUserData0.A.ubDataDef   		= (COP0_USERDEF_EN_BIT | COP0_PCA_EN_BIT | COP0_BUFVLD_EN_BIT | COP0_LCA_EN_BIT | COP0_SEED_EN_BIT);
}
#endif

void COP0_Init_Param(void)
{
	U8 ubi = 0;
	gubHostReadDataDefNum = 1; // UserData0
	// Read Param
	guoHostReadUserData0.uoAll = 0; //Clear Qbody
	guoHostReadUserData0.A.btD1				= FALSE; // TODO:
	guoHostReadUserData0.A.btSLCMode			= FALSE; // TODO:
	guoHostReadUserData0.A.ubCmd				= COP0_CMD_READ;
	guoHostReadUserData0.A.btRUTBps			= FALSE;	//[Change] By pass RUT
	guoHostReadUserData0.A.btLock			= FALSE;
	guoHostReadUserData0.A.btFlush			= FALSE;
	guoHostReadUserData0.A.btInOrderReadEn	= IOR_EN;
	//guoHostReadUserData0.A.btLastProgramPage	= TRUE; // TODO: check//FALSE
	guoHostReadUserData0.A.btRemapBps 		= FALSE;	//[Change] By pass VBRMP
	guoHostReadUserData0.A.ubTargetID			= COP0_TARGET_CPU;
	guoHostReadUserData0.A.uwTagID				= TAG_ID_DEFAULT;
	guoHostReadUserData0.A.ubL4KNum			= 0;
	guoHostReadUserData0.A.ubDataDef			= (COP0_USERDEF_EN_BIT | COP0_PCA_EN_BIT | COP0_BUFVLD_EN_BIT | COP0_LCA_EN_BIT | COP0_SEED_EN_BIT);
	guoHostReadUserData0.A.btSLCSetMethod	= COP0_TIE_D1SLC_MODE_FROM_VBRMP; // TODO:
	guoHostReadUserData0.A.btAttrGetPBNA		= FALSE;
	guoHostReadUserData0.A.btAttrWrite		= FALSE;
	guoHostReadUserData0.A.btAttrCLKTemplate = COP0_CLKSW_ATTR0;
	guoHostReadUserData0.A.btAttrNorCQResp	= FALSE;
	guoHostReadUserData0.A.ubAttrPriority		= COP0_LINK_PIORITY_LOW;
	guoHostReadUserData0.A.btAttrBMUAlctEN	= TRUE;
	guoHostReadUserData0.A.ubAttrMTTemplate	= COP0_MT_TEMP_BUF_LBPB_READ;	//??

	if (SCAN_SYSTEMAREA_BYPASS) {
		if (VS_SIM_EN) {
			guoHostReadUserData0.A.btAttrZIPEn = TRUE;
		}
		else {
			guoHostReadUserData0.A.btAttrZIPEn = (TRUE == FPGA_RTL_2CH) ? FALSE : TRUE;
		}
	}
	else {
		guoHostReadUserData0.A.btAttrZIPEn = TRUE;//(ZIP_EN == TRUE); //consider journal
	}

	for (ubi = 0; ubi < 7; ubi++) {
		if (guoHostReadUserData0.A.ubDataDef & BIT(ubi)) {
			gubHostReadDataDefNum++;
		}
	}

	guoHostReadUserDefine.uoAll = 0; // Clear All
	if (CONFIG_FLASH_CACHE_READ_EN == FALSE) {
		guoHostReadUserDefine.uoAll |= COP0_USERDEF_DIS_CACHE; // disable cache read
	}

	if (TOSHIBA_BISC3_FAST_READ_EN && (CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_TLC)) {
		/*
		 * JIRA_E13-855
		 *
		 * Fast read prefix may make sandisk read UNC.  Disable for now.
		 */
		if (ID_SANDISK != gFlhEnv.ubFlashID[0]) {
			guoHostReadUserDefine.A.uwUserDefine |= COP0_USERDEF_TOSHIBA_FAST_READ;
		}
	}

	guoHostReadBufAdr.uoAll = 0;
	guoHostReadBufAdr.TypeC.ubLBID		= LB_ID_READ;
	guoHostReadBufAdr.TypeC.ubBufVld	= 0xFF;

	gubHostWriteFirstDataDefNum = 1; // UserData0
	gubHostWriteLoopDataDefNum = 0;
	// Write Param
	guoHostWriteUserData0.uoAll = 0; //Clear Qbody
	guoHostWriteUserData0.A.btD1					= FALSE; // TODO:
	guoHostWriteUserData0.A.btSLCMode 			= FALSE; // TODO:
	guoHostWriteUserData0.A.ubCmd					= COP0_CMD_WRITE;
	guoHostWriteUserData0.A.btRUTBps				= FALSE;	//[Change] By pass RUT
	guoHostWriteUserData0.A.btLock				= FALSE;	//(ubLMU == 0xFF) ? FALSE : TRUE
	guoHostWriteUserData0.A.btFlush				= FALSE;
	guoHostWriteUserData0.A.btLastProgramPage	= FALSE;	//(ubLMU == 0xFF) ? TRUE : (ubLMU == (gubLMUNumber - 1))
	guoHostWriteUserData0.A.btRemapBps			= FALSE;
	guoHostWriteUserData0.A.ubTargetID				= COP0_TARGET_CPU;
	guoHostWriteUserData0.A.uwTagID				= TAG_ID_DEFAULT;
	guoHostWriteUserData0.A.ubL4KNum				= gub4kEntrysPerPlane - 1;
	// Temp solution for data
	if (RAIDECC_ENCODE_EN) {
		guoHostWriteUserData0.A.ubDataDef = (COP0_USERDEF_EN_BIT | COP0_PCA_EN_BIT | COP0_BUFVLD_EN_BIT | COP0_FWSET_EN_BIT | COP0_CONVPAGE_EN_BIT | COP0_SEED_EN_BIT);
	}
	else {
		guoHostWriteUserData0.A.ubDataDef = (COP0_USERDEF_EN_BIT | COP0_PCA_EN_BIT | COP0_BUFVLD_EN_BIT | COP0_FWSET_EN_BIT | COP0_SEED_EN_BIT);
	}
	guoHostWriteUserData0.A.btSLCSetMethod		= COP0_TIE_D1SLC_MODE_FROM_VBRMP;
	guoHostWriteUserData0.A.btAttrGetPBNA		= TRUE;
	guoHostWriteUserData0.A.btAttrWrite			= TRUE;
	guoHostWriteUserData0.A.btAttrCLKTemplate	= COP0_CLKSW_ATTR0;
	guoHostWriteUserData0.A.btAttrNorCQResp		= FALSE;
	guoHostWriteUserData0.A.ubAttrPriority			= COP0_LINK_PIORITY_LOW;
	guoHostWriteUserData0.A.btAttrBMUAlctEN		= FALSE;
	guoHostWriteUserData0.A.btAttrZIPEn			= TRUE;//(ZIP_EN == TRUE);//consider journal
	guoHostWriteUserData0.A.ubAttrMTTemplate		= COP0_MT_TEMP_BUF_LBPB_WRITE;
	for (ubi = 0; ubi < 7; ubi++) {
		if (guoHostWriteUserData0.A.ubDataDef & BIT(ubi)) {
			if (ubi < 2) { // COP0_USERDEF_EN_BIT, COP0_PCA_EN_BIT
				gubHostWriteFirstDataDefNum++;
			}
			else if (ubi < 5) { // COP0_BUFVLD_EN_BIT, COP0_LCA_EN_BIT, COP0_FWSET_EN_BIT
				gubHostWriteLoopDataDefNum++;
			}
			else { // COP0_SEED_EN_BIT, COP0_CONVPAGE_EN_BIT
				gubHostWriteLastDataDefNum++;
			}
		}
	}
}

void COP0_Init(void)
{
	COP0_Plane_Rule_t pln_tmp;
	COP0_Lmu_Rule_t lmu_tmp;
	COP0_Page_Rule_t page_tmp;
	COP0_Block_Rule_t block_tmp;
	COP0_Die_Rule_t die_tmp;
	COP0_Exdie_Rule_t exdie_tmp;

	U8 ubAdd8BitZeroForiFSAEn = ((PS5017_EN || PS5021_EN) ? 0 : 8); // E19, 8->0, E13 workaround
	U8 ubALUSubRuleCnt = 0;
	U8 ubRuleIdx;
	U8 ubRegOffset;
	U16 uwALURule_TargetBit;
#if (PS5013_EN)
	// switch gpio mux to andes function
	if ( GPIO_FOR_ANDES_ENABLE ) {
		mux_tdat(); // GPIO1 Andes_TDAT
		mux_trstn(); // GPIO3 Andes_RSTN
		mux_tclk(); // GPIO6 Andes_TCLK
	}
	mux_gpio_4_5_uart_tx_1(); // GPIO4 AndexUartTX1
#endif /* (PS5013_EN) */
#if PS5021_EN
	R32_SYS0_PAD_CTRL[R32_SYS0_PAD_MUX_FW_OPT1] &= (~MUX_FW_OPT1_BIT);
	R16_SYS0_MUX_CTRL[R16_SYS0_MUX_CTRL_GPIO] &= ~((U16)(BIT4));

	while (!(R32_COP0[R32_CREG_COP0_N25_CFG] & CHK_CORE_WFI_MODE));//N25
#endif
#if (!RDT_RUN_ONLINE)
	// Online???????ifo2,?????uart????
	// enable fifo2 for and setting fifo1 outputed by tx0 and fifo2 outputed by tx1
	R32_UART[R32_UART_S_FIFO2_CTRL_ST] |= TX_S_FIFO2_EN_BIT;
	R32_UART[R32_UART_TX_TRIG] &= ~(EMPTY_CMPT_EN_MASK << EMPTY_CMPT_EN_SHIFT);
	R32_UART[R32_UART_TX_TRIG] &= ~(TX_OUT_SEL_MASK << TX_OUT_SEL_SHIFT);
	R32_UART[R32_UART_TX_TRIG] |= (3 & TX_OUT_SEL_MASK) << TX_OUT_SEL_SHIFT;

	R32_UART[R32_UART_UART_CTRL] |= (1 & TX_BAUD_EN_MASK) << TX_BAUD_EN_SHIFT;
#endif
	//================ START: initial flow for COP0, ref COP0 spec 2.1.1 ================

#if PS5021_EN
	// wakeup andes. Andes CPU start running here.
	R32_COP0[R32_COP0_ANDES_STANDBY] &= (~COP0_STANDBY_REQ_BIT);
	// wait until andes andes wakeup.
	while (R32_COP0[R32_COP0_ANDES_STANDBY] & COP0_ANDES_STANDBY_BIT);	//N25

	// check COP0 parity error
	if (R32_COP0[R32_COP0_PAR_ERR_STAL] & COP0_PAR_ERR_STAL_BIT) {
		while (1);
	}
#else
#if (!PS5017_EN) // E17 : COP0_CLK tie with SYS_CLK (== SYS_CLK * 2)
	// 1. Enable COP0 clock (configured @ system)
	R32_SYS0_CLK[R32_SYS0_CLK_EN_CTRL] |= CR_COP0_CKEN_BIT;

	// 2. Set COP0_ANDES_2X_CLK_EN with respect to Andes clock (configured @ system)
	R32_SYS0_CLK[R32_SYS0_CLK_RXP_CLKSW0_CTRL] &= ~CR_COP0_SEL_BIT;
#endif /* (!PS5017_EN) */

	// 3. Enable Andes clock (set andes to 2x clock after svn 11868)
	R32_COP0[R32_COP0_ANDES_STANDBY] |= COP0_ANDES_2X_CLK_EN_BIT;

	// 4. Synchronize COP0 and ANDES
	R32_COP0[R32_COP0_ANDES_STANDBY] |= COP0_SYNC_CLR_BIT;
#endif
	// 5. Initial  Cop0 ram
#if 0 // enhance initial flow
	//memset((void *)VP_RAM_BASE, 0x00, NOR_VP_RAM_LEN);	// 0x00270000, COP0S_RAM for normal queue
	memset((void *)(VP_RAM_BASE + VP_RAM_LEN + VP_RAM_2_LEN), 0x00, VP_RAM_3_LEN); //init 278E00 ~ 278FFF
	//memset((void *)VP2_RAM_BASE, 0x00, QOS_VP_RAM_LEN);	// 0x00279000, COP0S_RAM for QOS queue
	memset((void *)(VP2_RAM_BASE + VP2_RAM_LEN + VP2_RAM_2_LEN), 0x00, VP2_RAM_3_LEN);	// init 27C600 ~ 27C7FF
	memset((void *)ICQ_RAM_BASE, 0x00, ICQ_RAM_LEN);	// 0x0027D000
	//memset((void *)OPTC_RAM_BASE, 0x00, OPTC_RAM_LEN);	// 0x0027E000  no need initial
	memset((void *)LINK_RAM_BASE, 0x00, LINK_RAM_LEN);	// 0x00280000
	//memset((void *)OPTS_RAM_BASE, 0x00, OPTS_RAM_LEN);	// 0x00281000
	//memset((void *)OPTD_RAM_BASE, 0x00, OPTD_RAM_LEN);  // 0x00286000 no need initial
#else
	memset((void *)VP_RAM_BASE, 0x00, NOR_VP_RAM_LEN);	// S17: 0x01280000 E13: 0x00270000, COP0S_RAM for normal queue
	memset((void *)VP2_RAM_BASE, 0x00, QOS_VP_RAM_LEN);	// S17: 0x01290000 E13: 0x00279000, COP0S_RAM for QOS queue
	memset((void *)ICQ_RAM_BASE, 0x00, ICQ_RAM_LEN);	// S17: 0x01293800 E13: 0x0027D000
	memset((void *)OPTC_RAM_BASE, 0x00, OPTC_RAM_LEN);	// S17: 0x01294000 E13: 0x0027E000
	memset((void *)LINK_RAM_BASE, 0x00, LINK_RAM_LEN);	// S17: 0x01297000 E13: 0x00280000
	//memset((void *)OPTS_RAM_BASE, 0x00, OPTS_RAM_LEN);	// S17: 0x01298000 E13: 0x00281000
	memset((void *)OPTD_RAM_BASE, 0x00, OPTD_RAM_LEN);  // S17: 0x012A0000 E13: 0x00286000
#endif
	// 6. Set CREG configuration properly
	COP0_SlcPCA_RegSetup();
	COP0_SetDivRule();
	COP0_RegSetup();
	//COP0_RegCheck();
	COP0_Init_CPU();
	COP0_Init_Param();
#if (RDT_MODE_EN && FW_CATEGORY_CUSTOMER == CUSTOMER_MAINSTREAM)
	COP0_Init_Param_RDT();//rdt_express_read
#endif

	// 7.a  Initial LRSC RAM, That need be turn on when COP0 be reset
	R32_COP0[R32_COP0_CR_LRSC_INIT] |= COP0_LRSC_INIT_BIT;

	// 7.b  After FW set the base and length, FW set COP0_CR_IRAM_INIT =1'b1.
	R32_COP0[R32_COP0_CMSG_3] |= COP0_CR_IRAM_INIT_BIT;

	// Wait LRSC Ram & IRAM initial done
	while ((R32_COP0[R32_COP0_CMSG_3] & COP0_CR_IRAM_INIT_BIT) || (R32_COP0[R32_COP0_CR_LRSC_INIT] & COP0_LRSC_INIT_BIT));
	//IdlePC(1000);

#if (MICRON_FSP_EN)
	gpComm2Andes_Info->micron_info.ubLUNForMoreUnitEn = LUN_FOR_MORE_UNIT_EN;
#else /* (MICRON_FSP_EN) */
	gpComm2Andes_Info->ubBiCS4AIPROnFlag = gubAIPRFlagOn;
	gpComm2Andes_Info->ubLUNForMoreUnitEn = LUN_FOR_MORE_UNIT_EN;
#endif /* (MICRON_FSP_EN) */

#if (!PS5013_EN)
	memcpy((void *)OPTD_ANDES_SEEDINIT_TABLE_BASE, gulSeedInitTable, sizeof(gulSeedInitTable));
#endif /* (!PS5013_EN) */

#if (FIP_SUPPORT_MT_VIRTUAL_ADDRESS_EN)
	memcpy((void *)OPTD_ANDES_BIN_VALUE_TABLE_BASE, gubBinValue, sizeof(gubBinValue));
#endif /* (FIP_SUPPORT_MT_VIRTUAL_ADDRESS_EN) */
	/* 8. ICE_MODE load bin file here! */
#if PS5021_EN
#if ICE_MODE_EN
	memset((void *)OPTS_RAM_BASE, 0x00, OPTS_RAM_LEN);	// S17: 0x01280000 E13: 0x00270000, COP0S_RAM for normal queue
	UartPrintf("Please Load Andes Bin File to %x", OPTS_RAM_BASE); // 2CH :0x1280000
#endif /* ICE_MODE_EN */
	while ((*(volatile unsigned int *) OPTS_RAM_BASE) == 0) {
		asm volatile("nop");
	};

	//Send MEIP interrupt to make N25 leave WFI mode
	//R32_COP0[R32_CREG_COP0_N25_CFG] |= SET_N25_MEIP_TRIG;

	R32_COP0[R32_COP0_ANDES_STANDBY] |= (COP0_STANDBY_REQ_BIT);
	M_PMU_SET_IP_RESET(SRST_ANDES_BIT);
	M_PMU_CLR_IP_RESET(SRST_ANDES_BIT);
	R32_COP0[R32_COP0_ANDES_STANDBY] &= (~COP0_STANDBY_REQ_BIT);
	R32_COP0[R32_CREG_COP0_N25_CFG] |= SET_N25_MEIP_LEVEL_TRIG;
	R32_COP0[R32_CREG_COP0_N25_CFG] &= (~SET_N25_MEIP_LEVEL_TRIG);

	// wait ANDES leave WFI mode
	while (R32_COP0[R32_CREG_COP0_N25_CFG] & CHK_CORE_WFI_MODE);
#else
	M_UART(INIT_, "Please Load Andes Bin file before Core Run\n");	// ARM ICE  (ARM ICE load *.bin)

	// Andes has been Copied
	// Only wait DMAC done (MH modify)
	while (FALSE == gDMACWaitCQInfo.ubANDESDMACDoneFlag) {
		DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
	}

	// 9. Enable COP0 core run
	M_COP0_CORE_RUN();
#endif
	/* ICE_MODE load Andes FW by Andes ICE here! */
	M_UART(INIT_, "Please Load Andes FW(.axf) after Core Run\n"); // ARM ICE + Andes ICE (Andes ICE exectute *.axf)

	// 10.a Release COP0_Andes_STANDBY (wakeup andes)
	R32_COP0[R32_COP0_ANDES_STANDBY] &= (~COP0_STANDBY_REQ_BIT);

	// 10.b Wait until andes wakeup.
	while (R32_COP0[R32_COP0_ANDES_STANDBY] & COP0_ANDES_STANDBY_BIT)
		;

	//Check Andes init done
	do {
		gpComm2Andes_Info->uoARM_Stall_Req_BM = 0x1;
		__asm("DSB");
		while (gpComm2Andes_Info->uoARM_Stall_Req_BM) {}
	} while (gpComm2Andes_Info->uoAndes_Stop_FormMT_BM == 0);

	gpComm2Andes_Info->uoAndes_Stop_FormMT_BM = 0;
	__asm("DSB");
	gpComm2Andes_Info->uoAndes_Req_ARM_Directly_Stall_BM = 0;
	__asm("DSB");

	// check COP0 parity error
	if (R32_COP0[R32_COP0_PAR_ERR_STAL] & COP0_PAR_ERR_STAL_BIT) {
		while (1);
	}
#if(MICRON_FSP_EN)
	//Micron: Send info to Andes DCCM
	gpComm2Andes_Info->micron_info.uwD3PageSize = gFlhEnv.uwPagePerBlock;
#endif//MICRON_FSP_EN
#if (READ_DISTURB_PRDH_EN)
	// Init after Andes init done
	ReadDisturbPRDHParameterInit();
#endif /* (READ_DISTURB_PRDH_EN) */

	M_UART(INIT_, "\nInitCOP0Done");
	//================ END: initial flow for COP0, ref COP0 spec 2.1.1 ================

	//================ Set FIP ALU Group ================
	// Set FIP ALU Group 0 with PCA rule 0
	pln_tmp.ubAll = R8_COP0[R8_COP0_PLANE_RULE];
	lmu_tmp.ubAll = R8_COP0[R8_COP0_LMU_RULE];

	page_tmp.uwAll = R16_COP0[R16_COP0_PAGE_RULE];
	block_tmp.uwAll = R16_COP0[R16_COP0_BLOCK_RULE];

	die_tmp.ubAll = R8_COP0[R8_COP0_DIE_RULE];
	exdie_tmp.ubAll = R8_COP0[R8_COP0_EXDIE_RULE];

	uwALURule_TargetBit = 16; // start from ROW address (Byte 2)

	// ALU_REG 0~3 for PCA Rule 0
	R16_FCON[R16_FCON_ALU_REG_0] = ((page_tmp.A.ubStartPoint + ubAdd8BitZeroForiFSAEn) << 10) | (uwALURule_TargetBit << 4) | (page_tmp.A.ubLength);	// ALU_G0_0
	ubALUSubRuleCnt++;

	uwALURule_TargetBit += page_tmp.A.ubLength;
	R16_FCON[R16_FCON_ALU_REG_0 + 1] |= ((pln_tmp.A.ubStartPoint + ubAdd8BitZeroForiFSAEn) << 10) | (uwALURule_TargetBit << 4) | (pln_tmp.A.ubLength);	// ALU_G0_1
	ubALUSubRuleCnt++;

	uwALURule_TargetBit += pln_tmp.A.ubLength;
	R16_FCON[R16_FCON_ALU_REG_1] = ((block_tmp.A.ubStartPoint + ubAdd8BitZeroForiFSAEn) << 10) | (uwALURule_TargetBit << 4) | (block_tmp.A.ubLength);	// ALU_G0_2
	ubALUSubRuleCnt++;

	uwALURule_TargetBit += block_tmp.A.ubLength;
	R16_FCON[R16_FCON_ALU_REG_1 + 1] |= ((die_tmp.A.ubStartPoint + ubAdd8BitZeroForiFSAEn) << 10) | (uwALURule_TargetBit << 4) | (die_tmp.A.ubLength);	// ALU_G0_3
	ubALUSubRuleCnt++;

	// Set FIP ALU Group 1~3 with PCA rule 1~3
	for (ubRuleIdx = COP0_PCA_RULE_1; ubRuleIdx < COP0_PCA_RULE_NUM; ubRuleIdx++) {
		ubRegOffset = (ubRuleIdx - 1) * 0x10; // PCA Rule 1~3 offset gap is 0x10
		pln_tmp.ubAll = R8_COP0[R8_COP0_PLANE_RULE_1 + ubRegOffset];
		lmu_tmp.ubAll = R8_COP0[R8_COP0_LMU_RULE_1 + ubRegOffset];

		page_tmp.uwAll = R16_COP0[R16_COP0_PAGE_RULE_1 + (ubRegOffset >> 1)];
		block_tmp.uwAll = R16_COP0[R16_COP0_BLOCK_RULE_1 + (ubRegOffset >> 1)];

		die_tmp.ubAll = R8_COP0[R8_COP0_DIE_RULE_1 + ubRegOffset];
		exdie_tmp.ubAll = R8_COP0[R8_COP0_EXDIE_RULE_1 + ubRegOffset];

		uwALURule_TargetBit = 16; // start from ROW address (Byte 2)

		// ALU_REG 4~7 for PCA Rule 1, ALU_REG 8~11 for PCA Rule 2, ALU_REG 12~15 for PCA Rule 3
		R16_FCON[R16_FCON_ALU_REG_0 + ubRuleIdx * MAX_ALU_SUBRULE_NUM] = ((page_tmp.A.ubStartPoint + ubAdd8BitZeroForiFSAEn) << 10) | (uwALURule_TargetBit << 4) | (page_tmp.A.ubLength);// ALU_G0_0

		uwALURule_TargetBit += page_tmp.A.ubLength;
#if (MICRON_FSP_EN)
		if (COP0_PCA_RULE_1 != ubRuleIdx) {
			uwALURule_TargetBit += lmu_tmp.A.ubLength;
		}
#endif//MICRON_FSP_EN
		R16_FCON[(R16_FCON_ALU_REG_0 + 1) + ubRuleIdx * MAX_ALU_SUBRULE_NUM] |= ((pln_tmp.A.ubStartPoint + ubAdd8BitZeroForiFSAEn) << 10) | (uwALURule_TargetBit << 4) | (pln_tmp.A.ubLength);	// ALU_G0_1

		uwALURule_TargetBit += pln_tmp.A.ubLength;
		R16_FCON[R16_FCON_ALU_REG_1 + ubRuleIdx * MAX_ALU_SUBRULE_NUM] = ((block_tmp.A.ubStartPoint + ubAdd8BitZeroForiFSAEn) << 10) | (uwALURule_TargetBit << 4) | (block_tmp.A.ubLength);// ALU_G0_2

		uwALURule_TargetBit += block_tmp.A.ubLength;
		R16_FCON[(R16_FCON_ALU_REG_1 + 1) + ubRuleIdx * MAX_ALU_SUBRULE_NUM] |= ((die_tmp.A.ubStartPoint + ubAdd8BitZeroForiFSAEn) << 10) | (uwALURule_TargetBit << 4) | (die_tmp.A.ubLength) ;	// ALU_G0_3
	}
	//STOP ALU_#.
	R32_FCON[R32_FCON_ADR_GEN_STOP] &= ~ALU_STOP_PTR_SHIFT_MASK;
	R32_FCON[R32_FCON_ADR_GEN_STOP] |= (ubALUSubRuleCnt - 1);
	gubALUSubRuleCnt = ubALUSubRuleCnt;

	if (gubCENumber < 4) {
		gubCop0TimeOutResolution = COP0_TIMEOUT_RESOLUTION_5; // timeout: 125us, (resolution 5, value: 0x38F)
	}
	else if (MICRON_FSP_EN && ANDES_NO_WAIT_FEATURE_EN && (gFlhEnv.PlanePerDie > 2)) {
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)
		gubCop0TimeOutResolution = COP0_TIMEOUT_RESOLUTION_3;
#endif/*(CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)*/
		if (gubCENumber >= 16) {
			gubCop0TimeOutResolution = COP0_TIMEOUT_RESOLUTION_3;  // timeout: 500us, (resolution 3, value: 0x38F)
		}
		else {
			gubCop0TimeOutResolution = COP0_TIMEOUT_RESOLUTION_4;  // timeout: 250us, (resolution 4, value: 0x38F)
		}
	}
	else if (MICRON_FSP_EN && ANDES_NO_WAIT_FEATURE_EN && (gubCENumber >= 16)) {
		gubCop0TimeOutResolution = COP0_TIMEOUT_RESOLUTION_4;  // timeout: 250us, (resolution 4, value: 0x38F)
	}
	else {
		gubCop0TimeOutResolution = COP0_TIMEOUT_RESOLUTION_6; // timeout: 62us, (resolution 6, value: 0x38F)
	}
	//COP0 Time out
	M_COP0_SET_TIMEOUT(COP0_TIMEOUT_VALUE_FOR_GC, COP0_TIMEOUT_RESOLUTION_7);

	M_SET_ANDES_SEQUENTIAL_READ(FALSE);
	M_SET_ANDES_SEQUENTIAL_WRITE(FALSE);
#if PS5021_EN
	R8_COP0[R8_COP0_LDPC_MODE_SEL] = (0x7 << 4) | gFlhEnv.ubDefaultLDPCMode;
	gubLDPCModeSelect = 0;
#endif /*PS5021_EN*/

#if  (MST_MODE_EN/*(CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC)\
		||(CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_TLC)\
		||(CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_TLC)\
		||(CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_QLC)\
		||(CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_QLC)\
		||(CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC)\
		||(CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC)\
		||(CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)*/)//Duson Porting BICS5 Add //Reip Porting 3D-V7 QLC Add//ems mst add--karl//zerio bics6 qlc add
	gpComm2Andes_Info->ubDisable_Cache_Prog = TRUE; // for Hynix recommendation #3
	gpComm2Andes_Info->ubDisable_Cache_Read = TRUE;
	gpComm2Andes_Info->btPolling_waitrdy = gpIDPage->ubExtra_Option.btPolling_waitrdy;//get from idpage//zerio BICS8 Add
#endif /*  (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC)*/

}

void COP0_DebugRead(U32 ulPCA, U32 ulLCA, U32 ulAddr, U32 ulCompareDataAddr)
{
	//return;
	COP0Status_t eCOP0Status;

	U16 uwTagID = TAG_ID_ALLOCATE;
	COP0ReadSQPara_t ReadSQPara = {{0}};
	BUF_TYPE_t ulBufPara = {0};

	ulBufPara.A.ulBUF_ADR = ulAddr;
	COP0API_FillCOP0ReadSQUserData0Para(COP0_R_DEBUG_UNC, &ReadSQPara);
	ReadSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_VBRMP;
	ReadSQPara.UserData0.btRUTBps = FALSE;
	ReadSQPara.UserData0.btVBRMPBps = FALSE;
	ReadSQPara.pulLCAPtr = (U32 *)&ulLCA;
	M_FWPCA_PCA_SET(ReadSQPara.ulPCA.ulAll, ulPCA);
	ReadSQPara.BufVld.ubBufType = BUF_TYPE_A;
	ReadSQPara.BufVld.pulBufInfoPtr = &ulBufPara;
	eCOP0Status = COP0API_SendReadSQ(&ReadSQPara, NULL);
	M_FW_ASSERT(ASSERT_HAL_COP0_0x05B7, eCOP0Status.btSendCmdSuccess);
	uwTagID = ReadSQPara.UserData0.TagID;

	gpuoCmdTableMgr[COP0_CMD_TABLE_ID][uwTagID].ulCmdFuncPTR = (U32)NULL;
	gpuoCmdTableMgr[COP0_CMD_TABLE_ID][uwTagID].ulData.ulAll = 0;
	gpuoCmdTableMgr[COP0_CMD_TABLE_ID][uwTagID].ulData.ulFLHInfo.uwCnt = 1;
	gpuoCmdTableMgr[COP0_CMD_TABLE_ID][uwTagID].ulData.ulFLHInfo.btKeepTag = FALSE;
	while (!gpuoCmdTableMgr[COP0_CMD_TABLE_ID][uwTagID].ulData.ulFLHInfo.btDone) {
		COP0DelegateCmd();
	}
	M_UART(COP0_, "[Debug Read]\n");
}
#else /* COP0_MODE_EN */

#endif /* COP0_MODE_EN */

void COP0_P4kBackupInit(void)
{
#if VS_SIM_EN
	P4KBackupRAMInit();
#else
	M_FW_TODO();
#endif /* VS_SIM_EN */
}

void COP0MTAttrSettingEnable(U8 ubTemplateIndex, U32 ulAttr)
{
	if (ubTemplateIndex < COP0_MT_ATTR_CNT) {
		R32_COP0[R32_COP0_ATTR0 + ubTemplateIndex] |= (ulAttr);
	}
}

void COP0MTAttrSettingDisable(U8 ubTemplateIndex, U32 ulAttr)
{
	if (ubTemplateIndex < COP0_MT_ATTR_CNT) {
		R32_COP0[R32_COP0_ATTR0 + ubTemplateIndex] &= ~(ulAttr);
	}
}

U8 CheckCOP0SQIsEnoughSpaceForTableProgram(U8 ubCmdNum)
{
	if (M_DB_GET_WR_CNT(DB_COP0_RD_SQ) >= ubCmdNum) {
		return TRUE;
	}
	else {
		return FALSE;
	}
}

void COP0_STALL_SINGLE_MTP_SET(U8 ubChannel, U8 ubBank, U8 ubqos, U8 ubNeedWait)
{
	///STALL normal Q
	R8_COP0[R8_COP0_MTP_STALL_SET] = (COP0_SET_STALL_BIT | COP0_SET_QOS_BIT(ubqos) | (  ubChannel + (ubBank * BIT(gPCARule_Channel.ubBit_No))  ) );

	if (ubNeedWait) {
		// wait till MTP is really stalled
		while (R8_COP0[R8_COP0_MTP_STALL_BSY] & COP0_MTP_STALL_BSY);
	}
}

void COP0_STALL_SINGLE_MTP_CLR(U8 ubChannel, U8 ubBank, U8 ubqos)
{
	///STALL normal Q
	R8_COP0[R8_COP0_MTP_STALL_CLR] = (COP0_SET_STALL_BIT | COP0_SET_QOS_BIT(ubqos) | (  ubChannel + (ubBank * BIT(gPCARule_Channel.ubBit_No))  ) );
}

void COP0_IRAM_Release(U16 uwMTIdx, U8 ubHWInt, U8 ubErrFrm, U8 ubQOS, U16 uwErrorL4KBMP)
{
	COP0_CQ_MSG_Release_t CQMsgRls;
	CQMsgRls.ulAll = 0UL;
	CQMsgRls.ulAll |=  ((1 << COP0_CQ_MSG_ERR_CPU_SEND_OFST) | (ubQOS << COP0_CQ_MSG_QOS_OFST) | (ubErrFrm << COP0_CQ_MSG_ERR_FRM_OFST) | (ubHWInt << COP0_CQ_MSG_HW_INT_OFST) | (uwMTIdx << COP0_CQ_MSG_MT_IDX_OFST));

	do {
		R16_COP0[R16_COP0_CQ_MSG1] = uwErrorL4KBMP;
	} while (R16_COP0[R16_COP0_CQ_MSG1] != uwErrorL4KBMP);
	R32_COP0[R32_COP0_CQ_MSG0] = CQMsgRls.ulAll;
	__asm ("DSB");
	return;
}

U8 COP0CheckMTPoolHeadVaild(U8 ubQos, U8 ubQueue)
{
	//Set current queue
	R8_COP0[R8_COP0_MTP_SEL_QUEUE] = COP0_MTP_SEL(ubQos, ubQueue);
	while (M_COP0_WAIT_MTP_QUEUE_LINKLIST_INFO_DONE);
	return R8_COP0[R8_COP0_MTP_SEL_HEAD_VALID];
}

U8 COP0GetMTPoolHeadIndex(U8 ubQos, U8 ubQueue)
{
	//Set current queue
	R8_COP0[R8_COP0_MTP_SEL_QUEUE] = COP0_MTP_SEL(ubQos, ubQueue);
	while (M_COP0_WAIT_MTP_QUEUE_LINKLIST_INFO_DONE);
	return R8_COP0[R8_COP0_MTP_SEL_HEAD_INDEX];
}

U16 COP0API_get_vecTable_tag(U8 ubMTidx)
{
	VECTRO_TABLE_STRUCT_PTR vecPTR = (VECTRO_TABLE_STRUCT_PTR)M_COP0_GET_VECTOR_TABLE_ADDR(ubMTidx);

	U8 l4kidx = 0;

	U16 uwTag = 0;

	FlhMT_t *tempMT = (FlhMT_t *)M_MT_ADDR(ubMTidx);

	L4KTable16B_t *tempL4K = (L4KTable16B_t *)(IRAM_BASE + tempMT->dma.L4KSparePtr);

	if (vecPTR->uldat.btZipEn) {
		uwTag = vecPTR->uldat.uwTagId0_3[0];
	}
	else {
		l4kidx = tempL4K->BitMap.Read_Before_WithBMU.PCA;
		uwTag = vecPTR->uldat.uwTagId0_3[l4kidx];
	}

	M_UART(COP0_, " vec=%x TAGID %d MTIDX %x\n", M_COP0_GET_VECTOR_TABLE_ADDR(ubMTidx), uwTag, ubMTidx);

	return uwTag;
}

#if GEN_FIP_PROG_FAIL_EN
U32 COP0GetOPTCacheProgramPCA(U8 ubQueueIdx)
{
	U8 ubOPTWaitCheckCnt, ubOPTElementIdx;
	U16 uwCmdPtr;
	volatile U8 *pubHeadOPTQueue = (volatile U8 *)COP0_OPT_Q_HEAD_BASE;
	volatile LINK_RAM_t *pOPTLinkRam = (volatile LINK_RAM_t *)(LINK_RAM_BASE);
	volatile COP0_CMD_ELEMENTS_t *pOPTCQueueElement = (volatile COP0_CMD_ELEMENTS_t *)(OPTC_RAM_BASE);
	ubOPTWaitCheckCnt = M_COP0_GET_OPT_Q_ELEMENT_CNT(ubQueueIdx);
	ubOPTElementIdx = pubHeadOPTQueue[ubQueueIdx];
	U32 ulFirstOPTPCA = INVALID_PCA;
	U8 ubRecordFirstFlag = TRUE;

	while (ubOPTWaitCheckCnt) {
		uwCmdPtr = pOPTLinkRam[ubQueueIdx * COP0_OPT_ELEMENT_NUM_PER_QUEUE + ubOPTElementIdx].bits.link0_ram;

		//Debug
		if (ubRecordFirstFlag) {
			if (OPT_PROGRAM_CMD != pOPTCQueueElement[uwCmdPtr].dw0_dat.attr_bits.cmd) {

				M_UART(COP0_, "OPT EleIdx %d, dw0_dat %x, PCA %x\n", ubOPTElementIdx, pOPTCQueueElement[uwCmdPtr].dw0_dat.all, pOPTCQueueElement[uwCmdPtr].pca);
				M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);	//head should be Cache Program CMD
			}
		}

		//Check the last write cmd in head program group
		if (pOPTCQueueElement[uwCmdPtr].user_define.bits.user_define & COP0_USERDEF_END_PROG) {
			if (ubRecordFirstFlag) {
				ubRecordFirstFlag = FALSE;
				ulFirstOPTPCA = pOPTCQueueElement[uwCmdPtr].pca;
			}
		}

		ubOPTElementIdx++;
		if (ubOPTElementIdx >= COP0_OPT_ELEMENT_NUM_PER_QUEUE) {
			ubOPTElementIdx -= COP0_OPT_ELEMENT_NUM_PER_QUEUE;
		}
		ubOPTWaitCheckCnt--;
	}


	return ulFirstOPTPCA;
}


U8 COP0GetVectorTableElementByPCA(U32 ulPCA)
{
	VECTRO_TABLE_STRUCT_PTR pVecTable;
	U8 ubMTIdx = VP_RAM_TOTAL_VECTOR;
	U8 ubTargetMTIdx = VP_RAM_TOTAL_VECTOR;
	for (ubMTIdx = 0 ; ubMTIdx < VP_RAM_TOTAL_VECTOR ; ubMTIdx++ ) {
		pVecTable = (VECTRO_TABLE_STRUCT_PTR)M_COP0_GET_VECTOR_TABLE_ADDR(ubMTIdx);

		if (pVecTable->uldat.ulVCA == ulPCA ) {


			if (OPT_PROGRAM_CMD == pVecTable->uldat.CMD) {
				ubTargetMTIdx = ubMTIdx;
			}

		}
	}
	if (ubTargetMTIdx == VP_RAM_TOTAL_VECTOR) {

	}

	return ubTargetMTIdx;
}

U32 COP0GetOPTCMD(U8 ubQueueIdx)
{
	U8 ubOPTWaitCheckCnt, ubOPTElementIdx;
	U16 uwCmdPtr;
	volatile U8 *pubHeadOPTQueue = (volatile U8 *)COP0_OPT_Q_HEAD_BASE;
	volatile LINK_RAM_t *pOPTLinkRam = (volatile LINK_RAM_t *)(LINK_RAM_BASE);
	volatile COP0_CMD_ELEMENTS_t *pOPTCQueueElement = (volatile COP0_CMD_ELEMENTS_t *)(OPTC_RAM_BASE);

	U8 ubCMD = OPT_INVALID_CMD;

	ubOPTWaitCheckCnt = M_COP0_GET_OPT_Q_ELEMENT_CNT(ubQueueIdx);
	ubOPTElementIdx = pubHeadOPTQueue[ubQueueIdx];

	if (ubOPTWaitCheckCnt) {
		uwCmdPtr = pOPTLinkRam[ubQueueIdx * COP0_OPT_ELEMENT_NUM_PER_QUEUE + ubOPTElementIdx].bits.link0_ram;
		if (RETRY_STALL_CACHE_PROGRAM_UART) {
			M_UART(COP0_, "First Search OPT Queue %d, Pointer %d,  RemapPCA is: %x, PCA is :%x\n", ubQueueIdx, ubOPTElementIdx, pOPTCQueueElement[uwCmdPtr].remap_pca, pOPTCQueueElement[uwCmdPtr].pca);
			M_UART(COP0_, "Search OPT Queue %d, Pointer %d,  RemapPCA is:  %x\n", ubQueueIdx, ubOPTElementIdx, pOPTCQueueElement[uwCmdPtr].remap_pca);
		}

		ubCMD = pOPTCQueueElement[uwCmdPtr].dw0_dat.attr_bits.cmd;

	}
	return ubCMD;
	//return pOPTCQueueElement[uwCmdPtr].dw0_dat.attr_bits.cmd;
}


#endif 	//GEN_FIP_PROG_FAIL_EN

void COP0GetErrMTInfoFromOPT(U8 ubQueueIdx)
{
	U8 ubOPTWaitCheckCnt, ubOPTElementIdx;
	U16 uwCmdPtr;
	volatile U8 *pubHeadOPTQueue = (volatile U8 *)COP0_OPT_Q_HEAD_BASE;
	volatile LINK_RAM_t *pOPTLinkRam = (volatile LINK_RAM_t *)(LINK_RAM_BASE);
	volatile COP0_CMD_ELEMENTS_t *pOPTCQueueElement = (volatile COP0_CMD_ELEMENTS_t *)(OPTC_RAM_BASE);
	ubOPTWaitCheckCnt = M_COP0_GET_OPT_Q_ELEMENT_CNT(ubQueueIdx);
	ubOPTElementIdx = pubHeadOPTQueue[ubQueueIdx];
	U16 uwOPT_STS = 0;
	U8 ubD1 = 0;
	U8 ubSLC = 0;

	gFailMTInfo.ulVCA = INVALID_PCA;
	M_FW_ASSERT(ASSERT_HAL_COP0_0x05B8, 0 != ubOPTWaitCheckCnt);
	if (ubOPTWaitCheckCnt) {
		uwCmdPtr = pOPTLinkRam[ubQueueIdx * COP0_OPT_ELEMENT_NUM_PER_QUEUE + ubOPTElementIdx].bits.link0_ram;
		ubD1 = pOPTCQueueElement[uwCmdPtr].user_define.bits.d1;
		ubSLC = pOPTCQueueElement[uwCmdPtr].dw0_dat.attr_bits.slc_mode;
		if (GEN_FIP_PROG_FAIL_UART) {
			M_UART(COP0_, "Search OPT Queue %d, Pointer %d,  RemapPCA is: %x, PCA is :%x, SLC: %d, MK: %d\n", ubQueueIdx, ubOPTElementIdx, pOPTCQueueElement[uwCmdPtr].remap_pca, pOPTCQueueElement[uwCmdPtr].pca, pOPTCQueueElement[uwCmdPtr].dw0_dat.attr_bits.slc_mode, pOPTCQueueElement[uwCmdPtr].user_define.bits.mark);
		}

		//Debug
		if (OPT_PROGRAM_CMD != pOPTCQueueElement[uwCmdPtr].dw0_dat.attr_bits.cmd) {
			if (GEN_FIP_PROG_FAIL_UART) {
				M_UART(COP0_, "OPT EleIdx %d, dw0_dat %x, PCA %x\n", ubOPTElementIdx, pOPTCQueueElement[uwCmdPtr].dw0_dat.all, pOPTCQueueElement[uwCmdPtr].pca);
			}
			M_FW_ASSERT(ASSERT_HAL_COP0_0x05B9, FALSE);	//head should be Cache Program CMD
		}

		if (pOPTCQueueElement[uwCmdPtr].user_define.bits.mark == TRUE) {
			//Check the last write cmd in head program group
			if (pOPTCQueueElement[uwCmdPtr].user_define.bits.user_define & COP0_USERDEF_END_PROG) {
				gFailMTInfo.param.bits.btSLC = ubSLC;
				gFailMTInfo.param.bits.btD1 = ubD1;
				gFailMTInfo.ulVCA = pOPTCQueueElement[uwCmdPtr].pca;
				gFailMTInfo.ulFSA = pOPTCQueueElement[uwCmdPtr].remap_pca;
				//gFailMTInfo.uwTagId0 = (pOPTCQueueElement[uwCmdPtr].user_define.user_define & PROG_SYS_AREA);
				gFailMTInfo.uwTagId0 = 0;	//Todo, should get program system area info from user define
				gFailMTInfo.param.bits.RandomSeedMode = pOPTCQueueElement[uwCmdPtr].dw0_dat.attr_bits.attr_rand_seed_mode;
				uwOPT_STS = (U16)((gpComm2Andes_Info->uoPreviousProgramQueueMultiPlaneBMP >> (ubQueueIdx * MAX_PLANE_BANK_NUM)) & BIT_MASK(MAX_PLANE_BANK_NUM));
				gFailMTInfo.uwOPT_STS = uwOPT_STS;	//No BIT of CACHE_PROG
				// because current Andes won't cache Multi-Mix-Plane,
				// so give it default value: Logical A -> Physical A, Logical B -> Physical B
				gFailMTInfo.ubPlaneP2LMapping = (((RUT_MIX_PLANE_EN && gubRUTMixPlaneEn)) ? PLANE_P2L_MAPPING_DEFAULT : PLANE_P2L_MAPPING_INVALID);
			}
		}
	}
	if (GEN_FIP_PROG_FAIL_UART) {
		M_UART(COP0_, "Get OPT_STS %x \n", uwOPT_STS);
	}
	return;
}


void COP0GetErrMTInfoFromVecTable(U8 ubMTIndex)
{
	VECTRO_TABLE_STRUCT_PTR pVecTable = (VECTRO_TABLE_STRUCT_PTR)M_COP0_GET_VECTOR_TABLE_ADDR(ubMTIndex);
	U8 ubPCARule = 0;
	U32 ulPage = 0;
	FlhMT_t *pMT = NULL;
	U32 ulFSA_Temp = 0;

	pMT = (FlhMT_t *)M_MT_ADDR(ubMTIndex);

	gFailMTInfo.param.bits.btSLC = pVecTable->uldat.btSLC;
#if PS5021_EN
	VectorLCAVLD_t *puoLCAVldTablePTR = (VectorLCAVLD_t *)(M_COP0_GET_LCA_VALID_TABLE(ubMTIndex));
	gFailMTInfo.uwOPT_STS = pVecTable->uldat.ubOPT_STS + (puoLCAVldTablePTR->btOPTStatusB8 << 8); //for previous MT index, exactly what we want
#else /*PS5021_EN*/
	gFailMTInfo.uwOPT_STS = pVecTable->uldat.ubOPT_STS; //for previous MT index, exactly what we want
#endif /*PS5021_EN*/

	M_FW_ASSERT(ASSERT_HAL_COP0_0x05BA, gFailMTInfo.uwOPT_STS & CACHE_PROG);
	gFailMTInfo.param.bits.btD1 = pVecTable->uldat.btD1;
	gFailMTInfo.uwTagId0 = pVecTable->uldat.uwTagId0_3[0];
	gFailMTInfo.ubPlaneP2LMapping = (((RUT_MIX_PLANE_EN && gubRUTMixPlaneEn)) ? PLANE_P2L_MAPPING_DEFAULT : PLANE_P2L_MAPPING_INVALID);

	M_PCA_RULE_IDX(ubPCARule, gFailMTInfo.param.bits.btSLC, gFailMTInfo.param.bits.btD1);

	////Get VCA/////////
	gFailMTInfo.ulVCA = pVecTable->uldat.ulVCA; // VCA of this MT, need to shift on page to get Program Fail MT`s VCA  in later flow

	////Get FSA/////////
	//Get FSA from MT
	// FSA of this MT, need to shift on page to get Program Fail MT`s VCA
	ulFSA_Temp = pMT->dma.uliFSA0_1;

	//Should be lower page when TLC program
	if (ubPCARule == COP0_PCA_RULE_0) {
		M_FW_ASSERT(ASSERT_HAL_COP0_0x05BB,  FLH_LOWER_PAGE == ((ulFSA_Temp >> gPCARule_LMU.ubShift[ubPCARule]) & gPCARule_LMU.ulMask));
	}

	// Calculate Page
	ulPage = ((ulFSA_Temp >> gPCARule_Page.ubShift[ubPCARule]) & gPCARule_Page.ulMask);
	M_FW_ASSERT(ASSERT_HAL_COP0_0x05BC, ulPage != 0);
	ulPage--;

	// Update FSA by calculated error page
	ulFSA_Temp &= ~(gPCARule_Page.ulMask << gPCARule_Page.ubShift[ubPCARule]);
	gFailMTInfo.ulFSA = ulFSA_Temp | (ulPage << gPCARule_Page.ubShift[ubPCARule]);
	if (GEN_FIP_PROG_FAIL_UART) {
		M_UART(COP0_, "\nErrFSA:%x\n", gFailMTInfo.ulFSA);
	}

	//Get RandomSeedMode
	gFailMTInfo.param.bits.RandomSeedMode = pMT->dma.RandomSeedMode;
	return;
}

void COP0DebugPrintAllMTPoolNode(U8 QoS, U8 ubQueue)
{
	U8 ubHeadValid, ubHeadIdx, ubTailIdx;
	U8 ubCurrentIdx;
	U8 ubi;
	U8 ubSLCMode = FALSE;
	U8 ubIsMark = FALSE;
	U8 ubCmd;
	U8 ubQueueIdxFromPCA;
	U32 ulElementRemapPCA;
	volatile U8 *pubMTPLinkList = (volatile U8 *)(OPT_D_LL_NOR_BASE);
	volatile FlhCmdMT_t *pMTQ = NULL;
	volatile MtpMT_t *puMTP = NULL;

	U8 ubOPTWaitCheckCnt, ubOPTElementIdx, ubOPTLogValid = FALSE;
	U16 uwCmdPtr;
	volatile U8 *pubHeadOPTQueue = (volatile U8 *)COP0_OPT_Q_HEAD_BASE;
	volatile LINK_RAM_t *pOPTLinkRam = (volatile LINK_RAM_t *)(LINK_RAM_BASE);
	volatile COP0_CMD_ELEMENTS_t *pOPTCQueueElement = (volatile COP0_CMD_ELEMENTS_t *)(OPTC_RAM_BASE);
	volatile COP0_ANDES_RD_CMD_SEQ_INFO_t *pAndesReadSequenceInfo = (volatile COP0_ANDES_RD_CMD_SEQ_INFO_t *)(OPTD_RECORD_FSA_ZONE_BASE);

	//Set current queue
	R8_COP0[R8_COP0_MTP_SEL_QUEUE] = COP0_MTP_SEL(QoS, ubQueue);
	while (M_COP0_WAIT_MTP_QUEUE_LINKLIST_INFO_DONE);

	//Get Info
	ubHeadValid = R8_COP0[R8_COP0_MTP_SEL_HEAD_VALID];
	ubHeadIdx = R8_COP0[R8_COP0_MTP_SEL_HEAD_INDEX];
	ubTailIdx = R8_COP0[R8_COP0_MTP_SEL_TAIL_INDEX];

	gpRetry->ulPatchLog[gpRetry->ubPatchLogIdx++]  = PATCH_LOG_HEADER | ubQueue;
	//=================MTQ=================
	for (ubi = 0; ubi < gPatchCmdMgr.QueueRecord[ubQueue].ubNonExecutedMTQNum; ubi++) {
		pMTQ = (volatile FlhCmdMT_t *)((QoS) ? M_QOS_MT_ADDR(gPatchCmdMgr.QueueRecord[ubQueue].uoBackupNonExecutedMTQConfig[ubi].bits_recc.ubMT_IDX) : M_MT_ADDR(gPatchCmdMgr.QueueRecord[ubQueue].uoBackupNonExecutedMTQConfig[ubi].bits_recc.ubMT_IDX)); //check MT0 if relate with read cmd or not
		gpRetry->ulPatchLog[gpRetry->ubPatchLogIdx++]  = PATCH_LOG_MTQ_INFO | pMTQ->uwFPUPtr;
		gpRetry->ulPatchLog[gpRetry->ubPatchLogIdx++] = pMTQ->uliFSA0_1;
		gpRetry->ulPatchLog[gpRetry->ubPatchLogIdx++] = pMTQ->uliFSA1_1;
	}
	//=================MTP=================
	if (ubHeadValid) {
		ubCurrentIdx = ubHeadIdx;
		ubi = 0;
		while (1) {
			M_COP0_GET_MTP_ADR(puMTP, ubCurrentIdx, QoS);
			gpRetry->ulPatchLog[gpRetry->ubPatchLogIdx++]  = PATCH_LOG_MTP_INFO | (ubi << 16) | puMTP->dw5_misc_cfg_2.bits.Fpu_ptr;
			gpRetry->ulPatchLog[gpRetry->ubPatchLogIdx++] = puMTP->dw10_iFSA0;
			gpRetry->ulPatchLog[gpRetry->ubPatchLogIdx++] = puMTP->dw12_iFSA1;
			if (ubCurrentIdx == ubTailIdx) {
				break;
			}
			else {
				ubCurrentIdx = *(pubMTPLinkList + ubCurrentIdx);
				ubi++;
			}
		}
	}
	//=================OPT=================
	ubOPTWaitCheckCnt = M_COP0_GET_OPT_Q_ELEMENT_CNT(ubQueue);
	ubOPTElementIdx = pubHeadOPTQueue[ubQueue];
	while (ubOPTWaitCheckCnt) {
		uwCmdPtr = pOPTLinkRam[ubQueue * COP0_OPT_ELEMENT_NUM_PER_QUEUE + ubOPTElementIdx].bits.link0_ram;
		ubIsMark = pOPTCQueueElement[uwCmdPtr].user_define.bits.mark;
		ubSLCMode = pOPTCQueueElement[uwCmdPtr].dw0_dat.attr_bits.slc_mode;
		ubCmd = pOPTCQueueElement[uwCmdPtr].dw0_dat.attr_bits.cmd;
		ulElementRemapPCA = pOPTCQueueElement[uwCmdPtr].remap_pca;
		ubQueueIdxFromPCA = gubPlanesPerBurst * ((ulElementRemapPCA >> gPCARule_Bank.ubShift[ubSLCMode * COP0_PCA_RULE_2])&gPCARule_Bank.ulMask) + \
			((ulElementRemapPCA >> gPCARule_Channel.ubShift[ubSLCMode * COP0_PCA_RULE_2])&gPCARule_Channel.ulMask);

		if ((ubQueue == ubQueueIdxFromPCA) && ubIsMark && (OPT_READ_CMD == ubCmd)) {
			ubOPTLogValid = TRUE;
			break;
		}

		ubOPTElementIdx++;
		if (ubOPTElementIdx >= COP0_OPT_ELEMENT_NUM_PER_QUEUE) {
			ubOPTElementIdx -= COP0_OPT_ELEMENT_NUM_PER_QUEUE;
		}
		ubOPTWaitCheckCnt--;
	}
	if (ubOPTLogValid) {
		for (ubi = 0; ubi < OPT_LOG_LENGTH; ubi++) {
			if (pAndesReadSequenceInfo[ubQueue].ubPlaneVld_BM[ubi]) {
				gpRetry->ulPatchLog[gpRetry->ubPatchLogIdx++]  = PATCH_LOG_OPT_INFO | pAndesReadSequenceInfo[ubQueue].ubPlaneVld_BM[ubi];
				gpRetry->ulPatchLog[gpRetry->ubPatchLogIdx++] = pAndesReadSequenceInfo[ubQueue].ulFSA[ubi][0];
				gpRetry->ulPatchLog[gpRetry->ubPatchLogIdx++] = pAndesReadSequenceInfo[ubQueue].ulFSA[ubi][1];
			}
		}
	}
}

MTPOOL_NODE_INFO_t COP0SearchMTPoolNode(U8 ubQosOrNot, U8 ubQueueIdx, U8 ubSearchMode, U8 ubNeedSetStall, U32 ulModeParam, U8 ubSearchStartIdx)
{
	U8 ubHeadValid, ubHeadIdx, ubTailIdx;
	U8 ubCurrentIdx, ubNodeCnt = 0;
	U8 ubSLC, ubLMU, ub2ndLMU, ubPCARule;
	U8 ubReadCmdMaxPlaneNum = BIT(gPCARule_Plane.ubBit_No);
	U8 ubReadMultiPlaneNum;
	volatile U8 *pubMTPLinkList = (volatile U8 *)(OPT_D_LL_NOR_BASE);
	volatile MtpMT_t *pMTP = NULL;
	volatile MtpMTData_t *pMTD = NULL;
	MTPOOL_NODE_INFO_t MTPoolNodeInfo;
#if((CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC)) //Reip Porting 3D-V7 QLC Add

	U16 uwDMAFPUOffset = FPU_PTR_OFFSET(fpu_entry_dma_r_00_05_e0);
#else
	U16 uwDMAFPUOffset = FPU_PTR_OFFSET(fpu_entry_dma_r_05_e0);
#endif
	//Set current queue
	M_COP0_SET_SEL_MTP_QUEUE_LINKLIST_INFO(ubQosOrNot, ubQueueIdx);
	while (M_COP0_WAIT_MTP_QUEUE_LINKLIST_INFO_DONE);

	//Get Info
	ubHeadValid = M_COP0_CHK_SEL_QUEUE_MTP_HEAD_VALID;
	ubHeadIdx = M_COP0_GET_SEL_QUEUE_MTP_HEAD_IDX;
	ubTailIdx = M_COP0_GET_SEL_QUEUE_MTP_TAIL_IDX;
	//Init Node Info
	MTPoolNodeInfo.ubNodeIdx = INVALID_INFO_BYTE;
	MTPoolNodeInfo.ubOrder = INVALID_INFO_BYTE;
	MTPoolNodeInfo.btResult = FALSE;
	MTPoolNodeInfo.ubPlaneNum = INVALID_INFO_BYTE;
	//Patch CMD target: read dma sequence
	//FW use :search end of program seqeunce
	if (ubHeadValid) {
		if (FROM_MTP_HEAD_NODE == ubSearchStartIdx) {
			ubCurrentIdx = ubHeadIdx ;
		}
		else {
			ubCurrentIdx = ubSearchStartIdx;
		}
		while (1) {
			M_COP0_GET_MTP_ADR(pMTP, ubCurrentIdx, ubQosOrNot);
			ubPCARule = pMTP->dw1_rs_cfg.bits.ALU_Sel;
			ubLMU = ((pMTP->dw10_iFSA0 >> gPCARule_LMU.ubShift[ubPCARule]) & gPCARule_LMU.ulMask);
			ub2ndLMU = ((pMTP->dw12_iFSA1 >> gPCARule_LMU.ubShift[ubPCARule]) & gPCARule_LMU.ulMask);
			ubSLC = M_CHECK_COP0_PCA_IS_SLC(ubPCARule);
			if (SEARCH_MODE_CACHE_READ == ubSearchMode) {
				for (ubReadMultiPlaneNum = FLASH_ONE_PLANE_OPERATION; ubReadMultiPlaneNum <= ubReadCmdMaxPlaneNum; ubReadMultiPlaneNum++) {
					if ((NAND_SUPPORT_AIPR) && gubAIPRFlagOn && (M_COP0_AIPR_END_CACHE_READ_CMD(ubSLC, ubReadMultiPlaneNum) == pMTP->dw5_misc_cfg_2.bits.Fpu_ptr)) {
						MTPoolNodeInfo.ubNodeIdx = ubCurrentIdx;
						MTPoolNodeInfo.ubOrder = ubNodeCnt;
						MTPoolNodeInfo.btResult = TRUE;
						MTPoolNodeInfo.ubPlaneNum = ubReadMultiPlaneNum;
						MTPoolNodeInfo.ReadType = NODE_TYPE_0x3F;
						return MTPoolNodeInfo;
					}
					if ((NAND_SUPPORT_AIPR) && gubAIPRFlagOn && (FLASH_TWO_PLANE_OPERATION == ubReadMultiPlaneNum)) {
						if ( M_COP0_AIPR_READ_CMD(ubSLC, ubLMU, ub2ndLMU, ubReadMultiPlaneNum) == pMTP->dw5_misc_cfg_2.bits.Fpu_ptr ) { //Hit AIPR 0030 0030
							MTPoolNodeInfo.ubNodeIdx = ubCurrentIdx;
							MTPoolNodeInfo.ubOrder = ubNodeCnt;
							MTPoolNodeInfo.btResult = TRUE;
							MTPoolNodeInfo.ubPlaneNum = ubReadMultiPlaneNum;
							MTPoolNodeInfo.ReadType = NODE_TYPE_0x30;
							return MTPoolNodeInfo;
						}
						else if ( M_COP0_AIPR_CACHE_READ_CMD(ubSLC, ubLMU, ub2ndLMU, ubReadMultiPlaneNum) == pMTP->dw5_misc_cfg_2.bits.Fpu_ptr ) { //Hit AIPR 0031 0031
							MTPoolNodeInfo.ubNodeIdx = ubCurrentIdx;
							MTPoolNodeInfo.ubOrder = ubNodeCnt;
							MTPoolNodeInfo.btResult = TRUE;
							MTPoolNodeInfo.ubPlaneNum = ubReadMultiPlaneNum;
							MTPoolNodeInfo.ReadType = NODE_TYPE_0x3131;
							return MTPoolNodeInfo;
						}
						else if ((M_COP0_AIPR_PATCH_START_CMD() <= pMTP->dw5_misc_cfg_2.bits.Fpu_ptr) && ( M_COP0_AIPR_PATCH_END_CMD() >= pMTP->dw5_misc_cfg_2.bits.Fpu_ptr)) { //Hit AIPR single 31 range
							if (M_COP0_AIPR_3130_READ_CMD(ubSLC, ubLMU, ub2ndLMU, ubReadMultiPlaneNum) == pMTP->dw5_misc_cfg_2.bits.Fpu_ptr ) {
								MTPoolNodeInfo.ubNodeIdx = ubCurrentIdx;
								MTPoolNodeInfo.ubOrder = ubNodeCnt;
								MTPoolNodeInfo.btResult = TRUE;
								MTPoolNodeInfo.ubPlaneNum = ubReadMultiPlaneNum;
								MTPoolNodeInfo.ReadType = NODE_TYPE_0x3130;
								return MTPoolNodeInfo;
							}
							if (M_COP0_AIPR_3031_READ_CMD(ubSLC, ubLMU, ub2ndLMU, ubReadMultiPlaneNum) == pMTP->dw5_misc_cfg_2.bits.Fpu_ptr ) {
								MTPoolNodeInfo.ubNodeIdx = ubCurrentIdx;
								MTPoolNodeInfo.ubOrder = ubNodeCnt;
								MTPoolNodeInfo.btResult = TRUE;
								MTPoolNodeInfo.ubPlaneNum = ubReadMultiPlaneNum;
								MTPoolNodeInfo.ReadType = NODE_TYPE_0x3031;
								return MTPoolNodeInfo;
							}
							if (M_COP0_AIPR_313F_READ_CMD(ubSLC, ubLMU) == pMTP->dw5_misc_cfg_2.bits.Fpu_ptr ) {
								MTPoolNodeInfo.ubNodeIdx = ubCurrentIdx;
								MTPoolNodeInfo.ubOrder = ubNodeCnt;
								MTPoolNodeInfo.btResult = TRUE;
								MTPoolNodeInfo.ubPlaneNum = ubReadMultiPlaneNum;
								MTPoolNodeInfo.ReadType = NODE_TYPE_0x313F;
								return MTPoolNodeInfo;
							}
						}
					}
					else {
						if ((IM_B47R || IM_B37R || IM_N48R) && (PS5013_EN)) {
							//Search 00 30, Check Bin0 ~ Bin7 Read CMD range
							if ((pMTP->dw5_misc_cfg_2.bits.Fpu_ptr >= M_MULTIPLANE_READ_CMD(ubSLC, ubLMU, ubReadMultiPlaneNum)) &&
								((pMTP->dw5_misc_cfg_2.bits.Fpu_ptr - M_MULTIPLANE_READ_CMD(ubSLC, ubLMU, ubReadMultiPlaneNum)) <= (M_BIN_LEVEL_READ_FPU_OFFSET_GAP(ubReadMultiPlaneNum) * BFEA_BIN7))) {
								MTPoolNodeInfo.btResult = TRUE;
								MTPoolNodeInfo.ReadType = NODE_TYPE_0x30;
							}
							//Search 00 31, Check Bin0 ~ Bin7 Read CMD range
							else if ((pMTP->dw5_misc_cfg_2.bits.Fpu_ptr >= M_MULTIPLANE_CACHE_READ_CMD(ubSLC, ubLMU, ubReadMultiPlaneNum)) &&
								((pMTP->dw5_misc_cfg_2.bits.Fpu_ptr - M_MULTIPLANE_CACHE_READ_CMD(ubSLC, ubLMU, ubReadMultiPlaneNum)) <= (M_BIN_LEVEL_READ_FPU_OFFSET_GAP(ubReadMultiPlaneNum) * BFEA_BIN7))) {
								MTPoolNodeInfo.btResult = TRUE;
								MTPoolNodeInfo.ReadType = NODE_TYPE_0x31;
							}

							if (MTPoolNodeInfo.btResult) {
								MTPoolNodeInfo.ubNodeIdx = ubCurrentIdx;
								MTPoolNodeInfo.ubOrder = ubNodeCnt;
								MTPoolNodeInfo.ubPlaneNum = ubReadMultiPlaneNum;
								return MTPoolNodeInfo;
							}
						}
						else {
							if (M_MULTIPLANE_READ_CMD(ubSLC, ubLMU, ubReadMultiPlaneNum) == pMTP->dw5_misc_cfg_2.bits.Fpu_ptr ) {
								MTPoolNodeInfo.ubNodeIdx = ubCurrentIdx;
								MTPoolNodeInfo.ubOrder = ubNodeCnt;
								MTPoolNodeInfo.btResult = TRUE;
								MTPoolNodeInfo.ubPlaneNum = ubReadMultiPlaneNum;
								MTPoolNodeInfo.ReadType = NODE_TYPE_0x30;
								return MTPoolNodeInfo;
							}
							//Search 00 31
							else if (M_MULTIPLANE_CACHE_READ_CMD(ubSLC, ubLMU, ubReadMultiPlaneNum) == pMTP->dw5_misc_cfg_2.bits.Fpu_ptr) {
								MTPoolNodeInfo.ubNodeIdx = ubCurrentIdx;
								MTPoolNodeInfo.ubOrder = ubNodeCnt;
								MTPoolNodeInfo.btResult = TRUE;
								MTPoolNodeInfo.ubPlaneNum = ubReadMultiPlaneNum;
								MTPoolNodeInfo.ReadType = NODE_TYPE_0x31;
								return MTPoolNodeInfo;
							}
						}
#if (!MICRON_FSP_EN)
						if (TOSHIBA_BISC3_FAST_READ_EN && (FLASH_ONE_PLANE_OPERATION == ubReadMultiPlaneNum)) {
							if (M_SINGLE_PLANE_FAST_READ_CMD(ubSLC, ubLMU) == pMTP->dw5_misc_cfg_2.bits.Fpu_ptr ) {
								MTPoolNodeInfo.ubNodeIdx = ubCurrentIdx;
								MTPoolNodeInfo.ubOrder = ubNodeCnt;
								MTPoolNodeInfo.btResult = TRUE;
								MTPoolNodeInfo.ubPlaneNum = ubReadMultiPlaneNum;
								MTPoolNodeInfo.ReadType = NODE_TYPE_0x30;
								return MTPoolNodeInfo;
							}
							else if (M_SINGLE_PLANE_FAST_CACHE_READ_CMD(ubSLC, ubLMU) == pMTP->dw5_misc_cfg_2.bits.Fpu_ptr) {
								MTPoolNodeInfo.ubNodeIdx = ubCurrentIdx;
								MTPoolNodeInfo.ubOrder = ubNodeCnt;
								MTPoolNodeInfo.btResult = TRUE;
								MTPoolNodeInfo.ubPlaneNum = ubReadMultiPlaneNum;
								MTPoolNodeInfo.ReadType = NODE_TYPE_0x31;
								return MTPoolNodeInfo;
							}
						}
#endif//(!MICRON_FSP_EN)								
					}
				}
				//Search 3F
				if (M_COP0_NORMAL_END_CACHE_READ_CMD(ubSLC) == pMTP->dw5_misc_cfg_2.bits.Fpu_ptr) {
					MTPoolNodeInfo.ubNodeIdx = ubCurrentIdx;
					MTPoolNodeInfo.ubOrder = ubNodeCnt;
					MTPoolNodeInfo.btResult = TRUE;
					MTPoolNodeInfo.ReadType = NODE_TYPE_0x3F;
					return MTPoolNodeInfo;
				}
#if (MICRON_FSP_EN)
				//Search DMA with IWL Flag
#if (!E21_TODO)
				else if (((uwDMAFPUOffset == pMTP->dw5_misc_cfg_2.bits.Fpu_ptr) && M_PATCH_CMD_GET_MICRON_IWL_READ_INFO_FROM_DMA(pMTP->dw8_UserDefine.ulAll)) ||
					((M_SINGLE_PLANE_MICRON_SNAP_READ_CMD(ubSLC) <= pMTP->dw5_misc_cfg_2.bits.Fpu_ptr) &&
						((M_SINGLE_PLANE_MICRON_SNAP_READ_CMD(ubSLC) + M_BIN_LEVEL_IWL_READ_FPU_OFFSET_GAP * BFEA_BIN7) >= pMTP->dw5_misc_cfg_2.bits.Fpu_ptr)) ||
					(FPU_PTR_ADDR_ONLY_CMD == pMTP->dw5_misc_cfg_2.bits.Fpu_ptr)) {
					return MTPoolNodeInfo;
				}
#endif /*(!E21_TODO)*/
#endif /* (MICRON_FSP_EN) */
				else if ((uwDMAFPUOffset != pMTP->dw5_misc_cfg_2.bits.Fpu_ptr) &&
					(M_FIP_CHECK_FPU_IS_NOT_DMA_WITH_POLL(pMTP->dw5_misc_cfg_2.bits.Fpu_ptr)) &&
					(M_COP0_AIPR_ADDR_GEN_ONLY() != pMTP->dw5_misc_cfg_2.bits.Fpu_ptr)) {
					return MTPoolNodeInfo;
				}
			}
			else if (SEARCH_MODE_NEED_PATCH_READ_NODE == ubSearchMode) {
				for (ubReadMultiPlaneNum = FLASH_ONE_PLANE_OPERATION; ubReadMultiPlaneNum <= ubReadCmdMaxPlaneNum; ubReadMultiPlaneNum++) {
					if ((NAND_SUPPORT_AIPR) && gubAIPRFlagOn && (M_COP0_AIPR_END_CACHE_READ_CMD(ubSLC, ubReadMultiPlaneNum) == pMTP->dw5_misc_cfg_2.bits.Fpu_ptr)) {
						MTPoolNodeInfo.btResult = TRUE;
						return MTPoolNodeInfo;
					}
					if ((NAND_SUPPORT_AIPR) && gubAIPRFlagOn && (FLASH_TWO_PLANE_OPERATION == ubReadMultiPlaneNum )) {
						if (M_COP0_AIPR_CACHE_READ_CMD(ubSLC, ubLMU, ub2ndLMU, ubReadMultiPlaneNum) == pMTP->dw5_misc_cfg_2.bits.Fpu_ptr) { //Hit AIPR 0031 0031
							MTPoolNodeInfo.btResult = TRUE;
							return MTPoolNodeInfo;
						}
						else if ((M_COP0_AIPR_PATCH_START_CMD() <= pMTP->dw5_misc_cfg_2.bits.Fpu_ptr) &&
							(M_COP0_AIPR_PATCH_END_CMD() >= pMTP->dw5_misc_cfg_2.bits.Fpu_ptr)) { //Hit AIPR single 31 range
							MTPoolNodeInfo.btResult = TRUE;
							return MTPoolNodeInfo;
						}
					}
					else {
						if ((IM_B47R || IM_B37R || IM_N48R)  && (PS5013_EN)) {
							//Search 00 31, Check Bin0 ~ Bin7 Read CMD range
							if ((pMTP->dw5_misc_cfg_2.bits.Fpu_ptr >= M_MULTIPLANE_CACHE_READ_CMD(ubSLC, ubLMU, ubReadMultiPlaneNum)) &&
								((pMTP->dw5_misc_cfg_2.bits.Fpu_ptr - M_MULTIPLANE_CACHE_READ_CMD(ubSLC, ubLMU, ubReadMultiPlaneNum)) <= (M_BIN_LEVEL_READ_FPU_OFFSET_GAP(ubReadMultiPlaneNum) * BFEA_BIN7))) {
								MTPoolNodeInfo.btResult = TRUE;
								return MTPoolNodeInfo;
							}
						}
						else {
							//Search 00 31
							if (M_MULTIPLANE_CACHE_READ_CMD(ubSLC, ubLMU, ubReadMultiPlaneNum) == pMTP->dw5_misc_cfg_2.bits.Fpu_ptr) {
								MTPoolNodeInfo.btResult = TRUE;
								return MTPoolNodeInfo;
							}
						}
#if (!MICRON_FSP_EN)
						if (TOSHIBA_BISC3_FAST_READ_EN && (FLASH_ONE_PLANE_OPERATION == ubReadMultiPlaneNum)) {
							if (M_SINGLE_PLANE_FAST_CACHE_READ_CMD(ubSLC, ubLMU) == pMTP->dw5_misc_cfg_2.bits.Fpu_ptr) {
								MTPoolNodeInfo.btResult = TRUE;
								return MTPoolNodeInfo;
							}
						}
#endif /* (!MICRON_FSP_EN) */
					}
				}
				//Search 3F
				if (M_COP0_NORMAL_END_CACHE_READ_CMD(ubSLC) == pMTP->dw5_misc_cfg_2.bits.Fpu_ptr) {
					MTPoolNodeInfo.btResult = TRUE;
					return MTPoolNodeInfo;
				}
#if (MICRON_FSP_EN)
				//Search DMA with IWL Flag
				else if (((uwDMAFPUOffset == pMTP->dw5_misc_cfg_2.bits.Fpu_ptr) && M_PATCH_CMD_GET_MICRON_IWL_READ_INFO_FROM_DMA(pMTP->dw8_UserDefine.ulAll)) ||
					((M_SINGLE_PLANE_MICRON_SNAP_READ_CMD(ubSLC) <= pMTP->dw5_misc_cfg_2.bits.Fpu_ptr) &&
						((M_SINGLE_PLANE_MICRON_SNAP_READ_CMD(ubSLC) + M_BIN_LEVEL_IWL_READ_FPU_OFFSET_GAP * BFEA_BIN7) >= pMTP->dw5_misc_cfg_2.bits.Fpu_ptr)) ||
					(FPU_PTR_ADDR_ONLY_CMD == pMTP->dw5_misc_cfg_2.bits.Fpu_ptr)) {
					MTPoolNodeInfo.btResult = TRUE;
					gPatchCmdMgr.QueueRecord[ubQueueIdx].ubIWLPatch = TRUE;
					return MTPoolNodeInfo;
				}
#endif/*(MICRON_FSP_EN)*/
				//Search DMA
				else if ((uwDMAFPUOffset == pMTP->dw5_misc_cfg_2.bits.Fpu_ptr) ||
					(M_FIP_CHECK_FPU_IS_DMA_WITH_POLL(pMTP->dw5_misc_cfg_2.bits.Fpu_ptr)) ||
					(M_COP0_AIPR_ADDR_GEN_ONLY() == pMTP->dw5_misc_cfg_2.bits.Fpu_ptr)) {
					MTPoolNodeInfo.btResult = TRUE;
					return MTPoolNodeInfo;
				}
				break;
			}
			else if (SEARCH_MODE_TAIL_NODE == ubSearchMode) {
				if (ubCurrentIdx == ubTailIdx) {
					if (ubNeedSetStall) {
						M_COP0_GET_MTD_ADR(pMTD, ubCurrentIdx, ubQosOrNot);
						pMTD->dw0_data.bits.mtp_stall = 1;
					}
					MTPoolNodeInfo.ubNodeIdx = ubCurrentIdx;
					MTPoolNodeInfo.ubOrder = ubNodeCnt;
					MTPoolNodeInfo.btResult = TRUE;
					return MTPoolNodeInfo;
				}
			}

			ubNodeCnt++;

			if (ubCurrentIdx == ubTailIdx) {
				break;    //no found
			}
			ubCurrentIdx = *(pubMTPLinkList + ubCurrentIdx); //search next node
		}
	}
	else {
		if (SEARCH_MODE_NEED_PATCH_READ_NODE == ubSearchMode) {
			U8 ubOPTIdx;
			if (PatchCmdSearchOPTMarkValid(ubQueueIdx, &ubOPTIdx)) {
				//Mark bit in R
				MTPoolNodeInfo.btResult = TRUE;
				volatile U8 *pubHeadOPTQueue = (volatile U8 *)COP0_OPT_Q_HEAD_BASE;
				if (ubOPTIdx != pubHeadOPTQueue[ubQueueIdx]) {
					gPatchCmdMgr.RepairCntInfo.uwFindOPTLookupNotZeroCnt++;
				}
#if (MICRON_FSP_EN)
				volatile COP0_ANDES_RD_CMD_SEQ_INFO_t *pAndesReadSequenceInfo = (volatile COP0_ANDES_RD_CMD_SEQ_INFO_t *)(OPTD_RECORD_FSA_ZONE_BASE);
				if (pAndesReadSequenceInfo[ubQueueIdx].cmd_info.bits.btIsIWLRead) {
					M_FW_ASSERT(ASSERT_HAL_COP0_0x05BD, pAndesReadSequenceInfo[ubQueueIdx].ubPlaneVld_BM[0]);
					gPatchCmdMgr.QueueRecord[ubQueueIdx].ubIWLPatch = TRUE;
				}
#endif /* (MICRON_FSP_EN) */
			}
		}
	}
	MTPoolNodeInfo.ReadType = NODE_TYPE_CHK_OPT_2nd_SET;
	return MTPoolNodeInfo;
}
#if TWO_PASS_EN
void COP0_CheckProgramCRC_Callback(DMACCQRst_t *pDMACCQRst)
{
	M_UART(GC2P_, "%x", pDMACCQRst->CRC32Rst.ulResult);
	gGC.ubWaitFlag = 0;
}
void COP0_CheckProgramE2E_Callback(DMACCQRst_t *pDMACCQRst)
{
	M_UART(GC2P_, "%x", pDMACCQRst->E3DRst.ulResult);
	gGC.ubWaitFlag = 0;
	M_FW_ASSERT(ASSERT_HAL_COP0_0x05BE, (TagGetPrivate(DMAC_CMD_TABLE_ID, pDMACCQRst->uoCompareRst.ubTagID) || (FALSE == pDMACCQRst->E3DRst.btStatus)));
}
#if QLC_DEBUG_EN
void COP0_TwoPassCheckProgram(BUF_TYPE_t ulBufferInfo, U32 *ulLCAAddr, U32 ulPCA, U8 ubMode)
{
	// check both two-pass were programed, and uart the first 4k buffer
	U8 ubLocalZByte, ubLocalSector;
	U8 ubIsFirst4k = ubMode & BIT0;
	U8 ubIsDummy = ubMode & BIT1;
	U8 ubIsJournal = ubMode & BIT3;
	U32 ulLocalPCA;
	U32 ulPlanesPerUnit = M_GET_PLANES_PER_UNIT_FROM_UNIT(gpVT->GC.uwTargetUnit[gpVT->GC.ubTargetCopyIdx]) - ((IM_N48R && !gpVT->GC.CopyData.TwoPass.Flag.btIs1stDoing) ? (gubPlanesPerSuperPage * 8) : 0); //1WL * cellNum per WL * MLC wordline
	U32 ulBuffer = (U32)M_LB_TO_ADDR(ulBufferInfo.B.ubPBorLB_ID, ulBufferInfo.B.uwPBorLB_OFFSET);
	U32 *pulCheckPlaneIdx = (gpVT->GC.CopyData.TwoPass.Flag.btIs1stDoing ? &gGC.ul1stCheckPlaneIdx : &gGC.ul2ndCheckPlaneIdx);
	U32 ulGCGRPlaneIdx = (gpVT->GC.CopyData.TwoPass.Flag.btIs1stDoing ? gpVT->GC.ulTargetPlaneIdx : gpVT->GC.CopyData.TwoPass.ul2ndTargetPlaneIdx);
	Unit_t uwGCGRTarget = gpVT->GC.uwTargetUnit[gpVT->GC.ubTargetCopyIdx];
	U8 ubRSParityShift = ((gubLMUNumber > 1) && (uwGCGRTarget.B.btSingleSLC == 0)) ? 1 : 0; // workaround for DirectD3 XZIP 1st hit table address calculation
	U32 ulLastPlaneIndex = M_GET_PTE_BMP_BASE_PLANE_FROM_UNIT(uwGCGRTarget) - 1 - GetXZIPFirstHitTablePlaneNum() - ubRSParityShift;

	M_FWPCA_PCA_GET(ulLocalPCA, ulPCA);

	if (QLC_PRINT_BUFFER_EN && ((*pulCheckPlaneIdx) <= ulLastPlaneIndex)) {
		M_FWPCA_ZBYTE_GET(ubLocalZByte, ulPCA);
		M_SECTOR_FROM_ZBYTE_GET(ubLocalSector, ubLocalZByte);
		if (ubIsFirst4k) {
			M_UART(GC2P_, "\nChk%d:%d ", gpVT->GC.CopyData.TwoPass.Flag.btIs1stDoing, (*pulCheckPlaneIdx));
			M_UART(N48_ET_, "%d=%x:%x ", gpVT->GCTimeStamp_TwoPass.ul2ndUseLastTimeStampTillPlaneIdx, gpVT->GCTimeStamp.ulActiveGCTimeStamp, gpVT->GCTimeStamp_TwoPass.ulLastGCTimeStamp);
		}
		if (RaidECCMAPIsParity(RS_MODE_GC, ulGCGRPlaneIdx, (U8)gpVT->GC.uwTargetUnit[gpVT->GC.ubTargetCopyIdx].B.btSingleSLC)) {
			UartPrintf("R");
		}
		else {
			if (ubIsDummy || ubIsJournal) {
				if (ubIsDummy) {
					M_UART(GC2P_, "D");
				}
				else if (ubIsJournal) {
					M_UART(GC2P_, "J");
				}
				DMAC_CRC32(DMAC_MODE_NORMAL_QUEUE, ulBuffer, BURNER_RANDOM_SEED, SIZE_4KB, FALSE, (U32)COP0_CheckProgramCRC_Callback, 0);
			}
			else {
				M_UART(GC2P_, "-");
				U32 ulSourceAddr = M_ZIP_LB_TO_ADDR(ulBufferInfo.B.ubPBorLB_ID, ulBufferInfo.B.uwPBorLB_OFFSET);
				BMUCmdResult_t BMUCmdResult;
				while (BMUAPICmdGetPBNA(BMU_CMD_NEED_CQ, BMU_CMD_DEFAULT_PBADDR, ulBufferInfo.B.ubPBorLB_ID, ulBufferInfo.B.uwPBorLB_OFFSET, &BMUCmdResult));
				DMAC_E3D4K(ulSourceAddr, (U32)COP0_CheckProgramE2E_Callback, (UNIFIED_LOG_DEMAND_EXTEND_LCA_EN && BMUCmdResult.BMUGetPBNARst.ulLCA >= gUnifiedLogVariable.ulLCAHead), BMUCmdResult.BMUGetPBNARst.ulLCA, BMUCmdResult.BMUGetPBNARst.ubZinfo, BMUCmdResult.BMUGetPBNARst.ulE3D4K, FALSE);
			}
			gGC.ubWaitFlag = TRUE;
			while (gGC.ubWaitFlag) {
				DMACDelegateCmd(DB_DMAC_NORM_CQ);
			}
		}
	}
	if (ubIsFirst4k) {
		if ((*pulCheckPlaneIdx) != M_FW_GET_PLANE_INDEX(ulPCA)) {
			M_UART(GC2P_, "ChkPln:%d Tiein:%d", (*pulCheckPlaneIdx), M_FW_GET_PLANE_INDEX(ulPCA));
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
		}
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, ulGCGRPlaneIdx == (*pulCheckPlaneIdx));
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (gGC.ul1stCheckPlaneIdx > gGC.ul2ndCheckPlaneIdx) || (gGC.ul1stCheckPlaneIdx == 0));
		(*pulCheckPlaneIdx)++;
		if ((*pulCheckPlaneIdx) == ulPlanesPerUnit) {
			(*pulCheckPlaneIdx) = 0;
		}
	}
}
#endif /*QLC_DEBUG_EN*/
#endif /*TWO_PASS_EN*/
