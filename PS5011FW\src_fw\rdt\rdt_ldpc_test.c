
#include "rdt/rdt_api.h"

#if RDT_MODE_EN
#include "rdt/rdt_ldpc_test.h"

U8 ldpc_test_state;

const byte_table _0xFF_UECC_DATA[] = {
	{0xef, 6, 4},
	{0x3f, 18, 12},
	{0x9f, 50, 11},
	{0x1, 50, 12},
	{0xea, 50, 13},
	{0xf2, 57, 4},
	{0xf7, 57, 14},
	{0xfe, 58, 5},
	{0x1f, 129, 7},
	{0xfc, 129, 8},
	{0xef, 129, 9},
};

const U32 _0xFF_CORR_PARITY[] = {
	0xffffffff, 0xffffffff, 0x00000000, 0x00000000,
	0x23be9800, 0x55a112e3, 0x00000000, 0x00000000,
	0x06d61afb, 0x70e600aa, 0x09a05634, 0x630b60ce,
	0xdcaba41f, 0x8d0d0bd2, 0xa5f74b71, 0xbb8ea46c,
	0xe72fe6c6, 0xade2cddf, 0x0866fdb3, 0xf623c888,
	0xd1ae04b6, 0x1e762ba2, 0xe267394e, 0x9c07e3a6,
	0xd764dd02, 0x5f97a825, 0x618988f8, 0x023a5bd2,
	0x47e2d66a, 0x60e69f27, 0xc929ac5c, 0xfb5b1e89,
	0xa271171b, 0x2608341d, 0x9f043c70, 0x6245b29a,
	0x6a02e43e, 0x8b477b45, 0x610da5b1, 0xc1d81d3c,
	0xb5371880, 0x43b72c39, 0x5f551b5c, 0xb4624525,
	0x4b755764, 0xd11f1167, 0xeeda065d, 0xd093f66f,
	0x6f693723, 0xb6ce9b7c, 0xdb457d42, 0x9a4a807c,
	0x1f3aef09, 0x968ebc13, 0x60cfe926, 0x67f71591,
	0xff615443, 0x5f95dc94, 0x48d13976, 0xa88b54aa,
	0x24e05b06, 0xd2f94f28, 0xe1a509bd, 0x704d021a,
	0x85881005, 0x5bee226b, 0x19de015f, 0xcd2a9903,
};

const U32 _M2_0x5A_RAND_PARITY[] = {
	0x55aa55aa, 0x55aa55aa, 0x00000000, 0x00000000,
	0x41a1c729, 0x620c0a53, 0x00000000, 0x00000000,
	0x25f2756d, 0x6b91f395, 0x0412a098, 0x8e49fc91,
	0xa748a4f4, 0xa06edc33, 0x20ccce6d, 0x75c1314f,
	0xafc285d0, 0xab1396b7, 0x2b212f23, 0x7e9696cd,
	0x632e6efe, 0x6dc24908, 0x0fe8a5d9, 0xabe61b86,
	0xd3eee247, 0x01a84a84, 0xa761909f, 0x39cd1dc4,
	0x315854ca, 0x61a43756, 0xfdaa2f9b, 0xefb75b20,
	0xcd944742, 0x9b6e440d, 0x3711b566, 0x891b8afd,
	0x9d6a9c98, 0xa760bab2, 0x3f24baf6, 0xd956ccf5,
	0x42e7c983, 0x458dcc6e, 0xe033a080, 0xcc2536d1,
	0x350f782e, 0xac49121a, 0xe5716f15, 0xef388d0c,
	0xee79d1b8, 0xaaca62e4, 0x1f1d97fd, 0xbf400b60,
	0xf44d6823, 0x0876285d, 0xbaa89346, 0x098b42c7,
	0x4ae79746, 0x57fee020, 0x0f56b6fa, 0x3bf1c9ca,
	0x336f0c54, 0x56de3bd3, 0xe41ae3b3, 0x3ffc7001,
};

/*
 * 	Function Name: fill_0xFF_UECC_ANSWER
 * 	Parameter:
 * 		U32 data_buf: address to store data
 * 	Description:
 * 		This data is a UECC case for LDPC.
 * 		This data is compatible to E13, E17, E19
 */
void fill_0xFF_UECC_DATA(U32 data_buf, BOOL Answer)
{
	if (Answer) {
		// Answer
		rdt_api_dmac_setvalue(0xFFFFFFFF, data_buf, _FORMAT_SIZE_LDPC);
		for (U8 i = 0; i < sizeof(_0xFF_UECC_DATA) / sizeof(byte_table); i++) {
			(*(U8 *)(data_buf + _1_data_LDPC + _0xFF_UECC_DATA[i].frame * BYTE_PER_FRAME + _0xFF_UECC_DATA[i].byte)) = _0xFF_UECC_DATA[i].data;
			(*(U8 *)(data_buf + _2_data_LDPC + _0xFF_UECC_DATA[i].frame * BYTE_PER_FRAME + _0xFF_UECC_DATA[i].byte)) = _0xFF_UECC_DATA[i].data;
		}

	}
	else {
		// Pattern
		rdt_api_dmac_setvalue(0xFFFFFFFF, data_buf, _FORMAT_SIZE_LDPC);
	}
}

void fill_0xFF_CORR_DATA(U32 data_buf, BOOL Answer)
{
	rdt_api_dmac_setvalue(0xFFFFFFFF, data_buf, _FORMAT_SIZE_LDPC);
	memcpy((void *)(data_buf + _1_spare_LDPC), (void *)_0xFF_CORR_PARITY, sizeof(_0xFF_CORR_PARITY));
	memcpy((void *)(data_buf + _2_spare_LDPC), (void *)_0xFF_CORR_PARITY, sizeof(_0xFF_CORR_PARITY));
}

void fill_0x5A_RAND_DATA(U32 data_buf, BOOL Answer)
{
	U16 ubFrame, ubByte;
	U32 idx;

	// Step 1: fill base data
	rdt_api_dmac_setvalue(0x55555555, data_buf + _1_data_LDPC, (U32)(_FORMAT_SIZE_LDPC / 2));
	for (ubFrame = 0; ubFrame < (DATA_2K_LEN / BYTE_PER_FRAME); ubFrame++) {
		for (ubByte = 0; ubByte < BYTE_PER_FRAME; ubByte++) {
			if ((ubFrame % 2 == 0 && ubByte % 2 == 0) || (ubFrame % 2 != 0 && ubByte % 2 != 0)) {
				(*(U8 *)(data_buf + _1_data_LDPC + ubFrame * BYTE_PER_FRAME + ubByte)) = 0xAA;
			}
		}
	}

	// Step 2: Fill Parity + spare
	memcpy((void *)(data_buf + _1_spare_LDPC), (void *)_M2_0x5A_RAND_PARITY, sizeof(_M2_0x5A_RAND_PARITY));

	if (!Answer) {
		// Step 3: flip random bit
		U32 error_bits[HIGH_RAND_ERR_BITS] = {0};
		generate_random_number(error_bits, HIGH_RAND_ERR_BITS, 0, ((_FORMAT_SIZE_LDPC / 2) * 8) - 1, TRUE, IN_LDPC_DUMMY_AREA, (U32)guoOperationTime);
		for (idx = 0; idx < HIGH_RAND_ERR_BITS; idx++) {
			(*(U8 *)(data_buf + (error_bits[idx] / 8))) ^= BIT((error_bits[idx] % 8));
		}
	}

	// Step 4: copy first 2K to second 2K
	memcpy((void *)(data_buf + _2_data_LDPC), (void *) data_buf, _FORMAT_SIZE_LDPC / 2);
}

U16 get_parity_length()
{
	switch (M_GET_ECC_MODE()) {
	case 0:
		return 256;
	case 1:
		return 240;
	case 2:
		return 224;
	case 3:
		return 208;
	case 4:
		return 176;
	case 5:
		return 144;
	case 6:
		return 128;
	case 7:
		return 304;
	case 8:
		return 288;
	}
	return 0;
}

BOOL IN_LDPC_DUMMY_AREA(U32 bit)
{

	U32 dummy_1_s = (_1_spare_LDPC + SPARE_LEN) * 8;
	U32 dummy_1_e = dummy_1_s + DUMMY_LEN * 8;
	U32 dummy_2_s = (_1_CRC_LDPC + CRC_LEN) * 8;
	U32 dummy_2_e = dummy_2_s + DUMMY_LEN * 8;
	U32 dummy_3_s = (_2_spare_LDPC + SPARE_LEN) * 8;
	U32 dummy_3_e = dummy_3_s + DUMMY_LEN * 8;
	U32 dummy_4_s = (_2_CRC_LDPC + CRC_LEN) * 8;
	U32 dummy_4_e = dummy_4_s + DUMMY_LEN * 8;

	if ((bit >= dummy_1_s && bit < dummy_1_e) ||
		(bit >= dummy_2_s && bit < dummy_2_e) ||
		(bit >= dummy_3_s && bit < dummy_3_e) ||
		(bit >= dummy_4_s && bit < dummy_4_e)) {
		return TRUE;
	}
	return FALSE;
}

void generate_random_number(U32 *numbers, U32 length, U32 min, U32 max, BOOL unique, BOOL (*exception)(U32), U32 seed)
{
	// exception is probably a problem to cause endless loop
	if ((max - min + 1) < length && unique) {
		return;
	}

	srand(seed);

	U32 n, m, rn, rm;
	m = 0;
	for (n = 0; n < max && m < length; ++n) {
		rn = max - n;
		rm = length - m;
		if (rand() % rn < rm)
			if (!exception(n + min)) {
				numbers[m++] = n + min;
			}
	}
}

/*
 * 	Function Name: fill_golden_data
 * 	Parameters:
 * 		U32 buffer: data buffer to store final data
 * 		U32 temp_buf: a temporarily used data buffer
 * 		BOOL MODE: for filling golden answer or golden pattern
 * 			GOLDEN_PATTERN(0): To fill golden pattern (Error bit containing data)
 *		 	GOLDEN_ANSWER(1): To fill golden answer (Error bit free data)
 *	Return:
 *		None
 *	Note:
 *		This function fill in golden data into target buffer.
 *		Golden data is generated from different randomizer seed which is differ by project
 *		The following data is "SPECIFIC FOR E13". Please remember to replace golden data generated by project seed.
 */
void fill_fixed_data(U32 data_buf, U32 temp_buf, BOOL Pattern, BOOL Answer)
{

	switch (Pattern) {
	case ALL_ZERO_BYPASS_LDPC_3:
		rdt_api_dmac_setvalue(0x00000000, data_buf, (U32)(_FORMAT_SIZE_LDPC));
		break;
	case ALL_0xFF_BYPASS_LDPC_3:
		rdt_api_dmac_setvalue(0xFFFFFFFF, data_buf, (U32)(_FORMAT_SIZE_LDPC));
		break;
	case ALL_ZERO_ENABLE_LDPC_3:
		rdt_api_dmac_setvalue(0x00000000, data_buf, (U32)(_FORMAT_SIZE_LDPC));
		break;
	case ALL_0xFF_ENABLE_LDPC_3_UECC:
		fill_0xFF_UECC_DATA(data_buf, Answer);
		break;
	case ALL_0xFF_ENABLE_LDPC_3_CORR:
		fill_0xFF_CORR_DATA(data_buf, Answer);
		break;
	case RAND_0x5A_ENABLE_LDPC_3_CORR:
		fill_0x5A_RAND_DATA(data_buf, Answer);
		break;
	}

	LDPC_format_2_IBuf_format(data_buf, temp_buf);

}

BOOL ldpc_test_check_pattern(U8 MODE, U32 data_buffer, U32 answer_buffer, U32 length)
{
	return memcmp((void *)data_buffer, (void *)answer_buffer, length);
}

/*
 * 	Function Name: ldpc_test_check_bit_flipped_count
 * 	Function Description
 * 		Check whether expected bit flipped count is equal to actual bit flipped count or not
 * 	Parameter:
 * 		U8 MODE: selected test mode
 * 		U8 ubCh: selected channel
 * 	Return:
 * 		1: Equal
 * 		0: Not equal
 */
BOOL ldpc_test_check_bit_flipped_count(U8 MODE, U8 ubCh)
{
	U16 expected_bit_count;

	switch (MODE) {
	case ALL_ZERO_BYPASS_LDPC_3:
		expected_bit_count = 0;
		break;
	case ALL_0xFF_BYPASS_LDPC_3:
		expected_bit_count = 0;
		break;
	case ALL_ZERO_ENABLE_LDPC_3:
		expected_bit_count = 0;
		break;
	case ALL_0xFF_ENABLE_LDPC_3_UECC:
		expected_bit_count = 0x7FFF;
		break;
	case ALL_0xFF_ENABLE_LDPC_3_CORR:
		expected_bit_count = 0;
		break;
	case RAND_0x5A_ENABLE_LDPC_3_CORR:
		expected_bit_count = HIGH_RAND_ERR_BITS;
		break;
	default:
		expected_bit_count = 0;
	}

	return !(R32_FCTL_CH[ubCh][R32_FCTL_ECC_INF] == expected_bit_count);
}

/*
 * 	Function Name: ldpc_test_setting_backup_restore
 * 	Function Description
 * 		A backup & restore function used in LDPC test cases
 * 	Parameter:
 * 		U8 ubCh: selected channel
 * 		U8 MODE: RESTORE(1): to restore setting
 * 				 BACKUP(0): to backup setting
 * 	Return:
 * 		None
 */
void ldpc_test_setting_backup_restore(U8 MODE, U32 *_CHNL, U32 *_ECC, U32 *_INT, U32 *_CMP, U32 *_ERR, U32 *_BR)
{
	U8 ubCh;
	switch (MODE) {
	case RESTORE:
		for (ubCh = 0; ubCh < gFlhEnv.ubChannelExistNum; ubCh++) {
			R32_FCTL_CH[ubCh][R32_FCTL_CHNL_SET] = _CHNL[ubCh];
			R32_FCTL_CH[ubCh][R32_FCTL_ECC_CFG] = _ECC[ubCh];
			R32_FCTL_CH[ubCh][R32_FCTL_INT_CFG] = _INT[ubCh];
			R32_FCTL_CH[ubCh][R32_FCTL_CMP_CFG] = _CMP[ubCh];
			R32_FCTL_CH[ubCh][R32_FCTL_ERR_HANDLE_SET] = _ERR[ubCh];
			R32_FCTL_CH[ubCh][R32_FCTL_BACK_RESTORE] = _BR[ubCh];
		}
		break;
	case BACKUP:
		for (ubCh = 0; ubCh < gFlhEnv.ubChannelExistNum; ubCh++) {
			_CHNL[ubCh] = R32_FCTL_CH[ubCh][R32_FCTL_CHNL_SET];
			_ECC[ubCh] = R32_FCTL_CH[ubCh][R32_FCTL_ECC_CFG];
			_INT[ubCh] = R32_FCTL_CH[ubCh][R32_FCTL_INT_CFG];
			_CMP[ubCh] = R32_FCTL_CH[ubCh][R32_FCTL_CMP_CFG];
			_ERR[ubCh] = R32_FCTL_CH[ubCh][R32_FCTL_ERR_HANDLE_SET];
			_BR[ubCh] = R32_FCTL_CH[ubCh][R32_FCTL_BACK_RESTORE];
		}
		break;
	}
}

/*
 * 	Function Name: ldpc_test_setting
 * 	Function Description
 * 		(1) Choose to bypass Randomizer Conversion or not
 * 		(2) Choose to bypass LDPC correction or not
 * 		(3) Do not Compare LCA
 * 		(4) Close interrupt setting
 * 		(5) Enable ibuffer backup restore setting
 * 		(6) Set backup restore length
 * 		(7) Disable FSA referencing
 * 	Parameter:
 * 		BOOL bypass_conv: TRUE: bypass, FALSE enable
 * 		BOOL bypass_LDPC: TRUE: bypass, FALSE enable
 * 	Return:
 * 		None
 */
void ldpc_test_setting(BOOL bypass_conv, BOOL bypass_LDPC)
{
	if (bypass_conv) { // (1)
		R32_FALL[R32_FCTL_CHNL_SET] |= SET_CONV_BYPASS_EN;
	}
	else {
		R32_FALL[R32_FCTL_CHNL_SET] &= CLR_CONV_BYPASS_DIS;
	}

	if (bypass_LDPC) { // (2)
		R32_FALL[R32_FCTL_ECC_CFG] &= CLR_LDPC_COR_DIS;
	}
	else {
		R32_FALL[R32_FCTL_ECC_CFG] |= SET_LDPC_COR_EN;
	}

	R32_FALL[R32_FCTL_CMP_CFG] &= ~CMP_EN_BIT; // (3)
	R32_FALL[R32_FCTL_INT_CFG] = 0; // (4)
	R32_FALL[R32_FCTL_ERR_HANDLE_SET] &= ~BIT(0); // (5)
	R32_FALL[R32_FCTL_BACK_RESTORE] |= M_SET_BR_W_LEN(303); // (6)
	R32_FALL[R32_FCTL_BACK_RESTORE] &= ~DRAM_MULTI_EN; // (7)
}

/*
 * 	Function Name: LDPC_format_2_IBuf_format
 * 	Function Description:
 * 		Revise input data in LDPC format into IBuffer format
 * 	Parameter:
 * 		U32 data_buf: buffer in which LDPC data is stored
 * 		U32 temp_buf: buffer for temporarily used
 * 	Return:
 * 		None
 * 	Note:
 * 		Data in temp_buf will be revised through process for 0x1210 bytes.
 * 		Remember to backup that data if it's important
 */
void LDPC_format_2_IBuf_format(U32 data_buf, U32 temp_buf)
{
	rdt_api_dmac_setvalue(0x00000000, temp_buf, _FORMAT_SIZE_LDPC);

	memcpy((void *)(temp_buf + _1_data_IBUF),   (void *)(data_buf + _1_data_LDPC),   DATA_2K_LEN);
	memcpy((void *)(temp_buf + _1_spare_IBUF),  (void *)(data_buf + _1_spare_LDPC),  SPARE_LEN);
	memcpy((void *)(temp_buf + _1_CRC_IBUF),    (void *)(data_buf + _1_CRC_LDPC),    CRC_LEN);
	memcpy((void *)(temp_buf + _1_parity_IBUF), (void *)(data_buf + _1_parity_LDPC), PARITY_LEN);
	memcpy((void *)(temp_buf + _2_data_IBUF),   (void *)(data_buf + _2_data_LDPC),   DATA_2K_LEN);
	memcpy((void *)(temp_buf + _2_spare_IBUF),  (void *)(data_buf + _2_spare_LDPC),  SPARE_LEN);
	memcpy((void *)(temp_buf + _2_CRC_IBUF),    (void *)(data_buf + _2_CRC_LDPC),    CRC_LEN);
	memcpy((void *)(temp_buf + _2_parity_IBUF), (void *)(data_buf + _2_parity_LDPC), PARITY_LEN);

	memcpy((void *)(data_buf), (void *)(temp_buf), _FORMAT_SIZE_IBUF);
}

/*
 * 	Function Name: rdt_ldpc_trigger_correct
 * 	Function Description:
 * 		A full process to trigger LDPC correction.
 * 	Parameter:
 * 		U8 ubCh: selected channel
 * 		U8 ibuf: selected ibuffer
 * 		U8 wait_ready: WAIT_READY(1) to wait FPU ready
 * 					   DONT_WAIT(0) do not wait FPU ready
 */
void rdt_ldpc_trigger_correct(U8 ubCh, U8 ibuf, U8 wait_ready)
{
	U16 uwFPUPtr;
	U16 *puwFPU = NULL;

	M_FIP_VUC_ASSIGN_IFSA(ubCh, 0, 0);
	uwFPUPtr = FPU_PTR_OFFSET(fpu_correct);
	puwFPU = (U16 *)(IRAM_BASE + uwFPUPtr);
	*(puwFPU + 1) = FPU_DMA_R_COR(ibuf);

	rdt_api_fpu_trigger(uwFPUPtr, ubCh, wait_ready);

}

/*
 * 	Function Name: rdt_ldpc_shift_pattern_test
 * 	Function Description:
 * 		This function sequentially mark the error pattern then perform LDPC correction.
 *      Both bypass and enable of LDPC setting are used for testing.
 * 		Bit flipped count and answer are checked to verify correction.
 * 	Parameters:
 * 	Return:
 * 		PASS(0)
 * 		FAIL(1)
 */
BOOL rdt_ldpc_shift_pattern_test(RDT_API_STRUCT_PTR rdt)
{
	if (CHECK_FAIL(ldpc_test_state)) {
		return FAIL;
	}

	U8	ubCh, ubIbuf, ubPart;
	U32	write_buf, read_buf, shift;
	U8 	interval = 8; // bytes
	BOOL failed = FALSE;
	U32 _CHNL[MAX_CHANNEL] = {0}, _ECC[MAX_CHANNEL] = {0}, _INT[MAX_CHANNEL] = {0}, _CMP[MAX_CHANNEL] = {0}, _ERR[MAX_CHANNEL] = {0}, _BR[MAX_CHANNEL] = {0};

	write_buf = rdt->base.rs_parity_buffer_base;
	read_buf = write_buf + PAGE_BYTE_SIZE;

	U16 first[] =  { _1_data_IBUF, _1_spare_IBUF, _1_CRC_IBUF, _1_parity_IBUF };
	U16 second[] = { _2_data_IBUF, _2_spare_IBUF, _2_CRC_IBUF, _2_parity_IBUF };
	U16 length[] = { DATA_2K_LEN, SPARE_LEN, CRC_LEN, PARITY_LEN };
	U32 first_part_base, second_part_base;
	U32 ulTempIram_addr = (U32)(IRAM_AXI_BASE + TEMP_SPR_FOR_SB_RS_OFF + 64);

	M_UART(RDT_TEST_, "\n\t[FIP] LDPC Shift Pattern Test : ");
	ldpc_test_state = SHIFT_PATTERN;

	// Step 1: Backup Randomizer & ECC & interrupt setting
	ldpc_test_setting_backup_restore(BACKUP, _CHNL, _ECC, _INT, _CMP, _ERR, _BR);

	// Step 2: Test Setting
	ldpc_test_setting(TRUE, FALSE);

	// Step 3: Fill write buffer with 0x00000000
	rdt_api_dmac_setvalue(0x00000000, write_buf, (U32)(_FORMAT_SIZE_IBUF));

	for (ubIbuf = 0; ubIbuf < IBUF_PER_CHN && !failed; ubIbuf++) {
		for (ubPart = 0; ubPart < 4  && !failed; ubPart++) {
			first_part_base = first[ubPart];
			second_part_base = second[ubPart];
			for (shift = 0; shift < length[ubPart] && !failed; shift += interval) {
				// Step 4: mark shifted pattern on write buffer
				memset((void *)(write_buf + first_part_base + shift), 0xFF, interval);
				memset((void *)(write_buf + second_part_base + shift), 0xFF, interval);

				// Step 5: bypass LDPC correction
				R32_FALL[R32_FCTL_ECC_CFG] &= CLR_LDPC_COR_DIS;

				for (ubCh = 0; ubCh < gFlhEnv.ubChannelExistNum; ubCh++) {
					// M_UART(RDT_TEST_, "\n Ibuf %b, Part %b, shift %d Ch %b", ubIbuf, ubPart, shift, ubCh);
					// Step 6: restore data from write buffer to Ibuffer
					rdt_api_fpu_check_read(ubCh);
					rdt_fip_BackupRestore(ubCh, write_buf, ulTempIram_addr, ubIbuf, DIRECTION_TO_IBF, MODE_DBUF_ONLY, BCH_MODE_NO_ECC, ALL_RESTORE_BY_PTR, 0);
					// M_UART(RDT_TEST_, ", write = %x, read = %x", *(U32 *)(write_buf + first_part_base + shift), *(U32 *)(read_buf + first_part_base + shift));
					FlaDMAConfig(ubCh, 0, 1, PROGRAM_SPARE_IRAM_OFFSET + SPARE_SIZE * ENTRY_PER_PLANE * ubCh, read_buf);

					// Step 7: trigger LDPC correct
					rdt_ldpc_trigger_correct(ubCh, ubIbuf, DONT_WAIT);
				}

				for (ubCh = 0; ubCh < gFlhEnv.ubChannelExistNum; ubCh++) {
					// Step 8: check channel ready
					rdt_api_fpu_check_read(ubCh);
					rdt_fip_BackupRestore(ubCh, read_buf, ulTempIram_addr, ubIbuf, DIRECTION_TO_EX_MEM, MODE_DBUF_ONLY, BCH_MODE_NO_ECC, ALL_RESTORE_BY_PTR, 0);
					// M_UART(RDT_TEST_, " --> %x", *(U32 *)(read_buf + first_part_base + shift));
					// Step 9: compare data with written pattern in write buffer
					if (memcmp((void *)write_buf, (void *)read_buf, FRAME_SIZE + SPARE_SIZE)) {
						ldpc_test_state |= BIT(0); // state record: bypass LDPC
						ldpc_test_state |= TEST_FAIL;
						M_UART(RDT_TEST_, "Compare Fail (bypass LDPC)");
						failed = TRUE;
						break;
					}
				}

				if (failed) {
					break;
				}

				// Step 10: enable LDPC correction
				R32_FALL[R32_FCTL_ECC_CFG] |= SET_LDPC_COR_EN;

				for (ubCh = 0; ubCh < gFlhEnv.ubChannelExistNum; ubCh++) {
					// Step 11: restore data from write buffer to Ibuffer
					rdt_api_fpu_check_read(ubCh);
					rdt_fip_BackupRestore(ubCh, read_buf, ulTempIram_addr, ubIbuf, DIRECTION_TO_EX_MEM, MODE_DBUF_ONLY, BCH_MODE_NO_ECC, ALL_RESTORE_BY_PTR, 0);
					FlaDMAConfig(ubCh, 0, 1, PROGRAM_SPARE_IRAM_OFFSET + SPARE_SIZE * ENTRY_PER_PLANE * ubCh, read_buf);

					// Step 12: trigger LDPC correct
					rdt_ldpc_trigger_correct(ubCh, ubIbuf, DONT_WAIT);
				}

				// Step 13: erase pattern in write buffer
				memset((void *)(write_buf + first_part_base + shift), 0x00, interval);
				memset((void *)(write_buf + second_part_base + shift), 0x00, interval);

				for (ubCh = 0; ubCh < gFlhEnv.ubChannelExistNum; ubCh++) {
					// Step 14: wait channel ready
					rdt_api_fpu_check_read(ubCh);
					rdt_fip_BackupRestore(ubCh, read_buf, ulTempIram_addr, ubIbuf, DIRECTION_TO_EX_MEM, MODE_DBUF_ONLY, BCH_MODE_NO_ECC, ALL_RESTORE_BY_PTR, 0);

					// Step 15: check LDPC bit flipped count
					if (R32_FCTL_CH[ubCh][R32_FCTL_ECC_INF] != interval * BYTE_PER_PATTERN * 2) {
						M_UART(RDT_TEST_, "Flipped Count Error : %d (enable LDPC)", R32_FCTL_CH[ubCh][R32_FCTL_ECC_INF]);
						ldpc_test_state |= FLIP_ERROR;
						ldpc_test_state |= TEST_FAIL;
						failed = TRUE;
						break;
					}

					// Step 16: compare data with written pattern in write buffer
					if (memcmp((void *)write_buf, (void *)read_buf, FRAME_SIZE + SPARE_SIZE)) {
						M_UART(RDT_TEST_, "Compare Fail (enable LDPC)");
						ldpc_test_state |= TEST_FAIL;
						failed = TRUE;
						break;
					}
				}
			}
		}
	}

	// Step 17: restore register setting
	ldpc_test_setting_backup_restore(RESTORE, _CHNL, _ECC, _INT, _CMP, _ERR, _BR);

	if (failed) {
		// Step 18: record error log
		rdt_ldpc_test_record_erl_log(ubCh, 0);
		rdt_api_program_erl_log(rdt, TRUE);
		M_UART(RDT_TEST_, " FAIL");
	}
	else {
		M_UART(RDT_TEST_, " PASS");
	}

	return (failed) ? FAIL : PASS;
}

/*
 * 	Function Name: rdt_ldpc_hb_fixed_pattern_test
 * 	Function Description:
 * 		This function is given a selected golden pattern then perform LDPC correction.
 * 		Bit flipped count and answer are checked to verify correction.
 * 	Parameters:
 * 		U32 MODE: Selected golden pattern
 * 		U32 pattern: pattern print out
 * 	Return:
 * 		PASS(0)
 * 		FAIL(1)
 * 	Note:
 * 		Different project and selected LDPC mode may have different Bit flipped count and answer.
 * 		Please replace those data before using this function
 *
 */
BOOL rdt_ldpc_hb_correction_pattern_test(RDT_API_STRUCT_PTR rdt, U32 MODE, U32 pattern, U32 round)
{
	if (CHECK_FAIL(ldpc_test_state)) {
		return FAIL;
	}

	U8   ubCh, ubIbuf;
	U32	 ulRound, fail_cnt = 0;
	BOOL result = PASS;
	BOOL BypassLDPC = MODE & BIT(0);
	U32	from_buf, answer_buf, to_buf;
	U32 _CHNL[MAX_CHANNEL] = {0}, _ECC[MAX_CHANNEL] = {0}, _INT[MAX_CHANNEL] = {0}, _CMP[MAX_CHANNEL] = {0}, _ERR[MAX_CHANNEL] = {0}, _BR[MAX_CHANNEL] = {0};
	U32 ulTempIram_addr = (U32)(IRAM_AXI_BASE + TEMP_SPR_FOR_SB_RS_OFF + 64);

	from_buf = rdt->base.rs_parity_buffer_base;
	answer_buf = from_buf + PAGE_BYTE_SIZE;
	to_buf = answer_buf + PAGE_BYTE_SIZE;

	M_UART(RDT_DBG_, "\n\t[FIP] LDPC Hardbit Pattern test");
	M_UART(RDT_TEST_, "\n\t[FIP] LDPC All %x Test, Bypass LDPC = %d : ", pattern, BypassLDPC);
	ldpc_test_state = MODE;

	// Step 1: Backup Randomizer & ECC & interrupt setting
	ldpc_test_setting_backup_restore(BACKUP, _CHNL, _ECC, _INT, _CMP, _ERR, _BR);

	//  Step2: Fill data
	fill_fixed_data(from_buf, to_buf, MODE, GOLDEN_PATTERN);
	fill_fixed_data(answer_buf, to_buf, MODE, GOLDEN_ANSWER);

	ldpc_test_setting(TRUE, BypassLDPC);

	for (ulRound = 0; ulRound < round && !result; ulRound++) {
		// Step 2.1: Random a new set of test pattern
		if (RANDOM_PATTERN(MODE)) {
			fill_fixed_data(from_buf, to_buf, MODE, GOLDEN_PATTERN);
		}

		for (ubCh = 0; ubCh < gFlhEnv.ubChannelExistNum; ubCh++) {
			for (ubIbuf = 0; ubIbuf < IBUF_PER_CHN; ubIbuf++) {
				//fip_backup_restore_ibuf(ubCh, from_buf, (IRAM_BASE + GC_BACKUP_COPY_UNIT_OFF), ubIbuf, DIRECTION_TO_IBF, MODE_DBUF_ONLY, BCH_MODE_NO_ECC, ALL_RESTORE_BY_PTR, 0);
				rdt_fip_BackupRestore(ubCh, from_buf, ulTempIram_addr, ubIbuf, DIRECTION_TO_IBF, MODE_DBUF_ONLY, BCH_MODE_NO_ECC, ALL_RESTORE_BY_PTR, 0);
				FlaDMAConfig(ubCh, 0, 1, READ_SPARE_IRAM_OFFSET + SPARE_SIZE * ENTRY_PER_PLANE * ubCh, to_buf);

				// Step 3: trigger LDPC correction
				rdt_ldpc_trigger_correct(ubCh, ubIbuf, WAIT_READY);
				// fip_backup_restore_ibuf(ubCh, to_buf, (IRAM_BASE + GC_BACKUP_COPY_UNIT_OFF), ubIbuf, DIRECTION_TO_EX_MEM, MODE_DBUF_ONLY, BCH_MODE_NO_ECC, ALL_RESTORE_BY_PTR, 0);
				rdt_fip_BackupRestore(ubCh, to_buf, ulTempIram_addr, ubIbuf, DIRECTION_TO_EX_MEM, MODE_DBUF_ONLY, BCH_MODE_NO_ECC, ALL_RESTORE_BY_PTR, 0);

				// Step 4: Compare data & flipped bit
				if (ldpc_test_check_pattern(MODE, to_buf, answer_buf, (DATA_2K_LEN + SPARE_LEN) * 2)) {
					//M_UART(RDT_TEST_, "\nround %d Compare Fail", ulRound);
					result |= 1;
				}
				if (ldpc_test_check_bit_flipped_count(MODE, ubCh)) {
					//M_UART(RDT_TEST_, "\nround %d Flipped Count Error : %x", ulRound, R32_FCTL_CH[ubCh][R32_FCTL_ECC_INF]);
					result |= 1;
				}
				//result |= ldpc_test_check_pattern(MODE, to_buf, answer_buf, (DATA_2K_LEN + SPARE_LEN) * 2);
				//result |= ldpc_test_check_bit_flipped_count(MODE, ubCh);
				if (result) {
					// Step 5: if executing Random Pattern, fail_cnt+=1
					if (RANDOM_PATTERN(MODE)) {
						fail_cnt += 1;
						result = 0;
					}
					else {
						ldpc_test_state |= TEST_FAIL;
						// rdt_ldpc_test_record_erl_log(ubCh, 0);
					}
				}
			}
		}
	}

	// Step 6: if executing Random Pattern
	if (RANDOM_PATTERN(MODE)) {
		fail_cnt /= (gFlhEnv.ubChannelExistNum * IBUF_PER_CHN);
		M_UART(RDT_TEST_, " (%d/%d) ", fail_cnt, round);
		if (fail_cnt > RAND_FAIL_RATE * round / 100) {
			result = FAIL;
			ldpc_test_state |= TEST_FAIL;
			rdt_ldpc_test_record_erl_log(0, fail_cnt);
		}
	}

	if (result) {
		M_UART(RDT_TEST_, "FAIL");
		rdt_api_program_erl_log(rdt, TRUE);
	}
	else {
		M_UART(RDT_TEST_, "PASS");
	}

	// Step 7: Restore register setting
	ldpc_test_setting_backup_restore(RESTORE, _CHNL, _ECC, _INT, _CMP, _ERR, _BR);

	return result;
}

void rdt_ldpc_test_record_erl_log(U8 ubCh, U32 fail_cnt)
{
	ERL_LOG_STRUCT erl_log;
	RDT_API_STRUCT_PTR rdt = &gRdtApiStruct;
	memset(&erl_log, 0, sizeof(ERL_LOG_STRUCT));
	erl_log.err_bbl.fail_type         = ERL_LDPC_TEST_FAIL;
	erl_log.err_bbl.flash_channel     = ubCh;
	erl_log.err_bbl.loop_number       = ldpc_test_state & ~BIT(7);
	erl_log.err_bbl.cycle_number	  = (U32)fail_cnt;
	erl_log.err_bbl.rdt_state         = rdt->current_state;
	erl_log.err_bbl.current_time      = (U16)(rdt_api_rtt_get_timer_count() / (1000 * 60));
	erl_log.err_bbl.flash_temperature = rdt_api_get_flash_temperature(ubCh, 0, 0, 0);
	erl_log.err_bbl.ctl_temperature   = rdt_read_CTRL_temperature();
	erl_log.err_bbl.over_err_bit      = R32_FCTL_CH[ubCh][R32_FCTL_ECC_INF];
	rdt_api_erl_log_add (rdt, &erl_log);
}

BOOL rdt_api_ldpc_test(RDT_API_STRUCT_PTR rdt)
{
	// Check whether LDPC Mode & Parity is set correspondingly
	if (get_parity_length() != PARITY_LEN) {
		// ldpc_test_state |= TEST_FAIL;
		M_UART(RDT_TEST_, "\n\t[LDPC] Parity length (%d) is not compatible with LDPC Mode %d (%d)!", PARITY_LEN, M_GET_ECC_MODE(), get_parity_length());
	}
	U8 backup_ECCMode = (U8)M_GET_ECC_MODE();
	rdt_api_switch_ldpc(SET_ECC_MODE_2);
	M_UART(RDT_TEST_, "\n\t[LDPC] Switch LDPC mode (%d) to %d , Parity length = %d", backup_ECCMode, M_GET_ECC_MODE(), get_parity_length());

	// Shift Pattern
	rdt_ldpc_shift_pattern_test(rdt);

	// Random 0x5A Pattern
	rdt_ldpc_hb_correction_pattern_test(rdt, RAND_0x5A_ENABLE_LDPC_3_CORR, 0x55AA55AA, 10000);

#if 0 // x11.B Not yet released
	// Bypass LDPC correction
	rdt_ldpc_hb_correction_pattern_test(ALL_ZERO_BYPASS_LDPC, 0x00000000);
	rdt_ldpc_hb_correction_pattern_test(ALL_0xFF_BYPASS_LDPC, 0xFFFFFFFF);

	// Enable LDPC correction
	rdt_ldpc_hb_correction_pattern_test(ALL_ZERO_ENABLE_LDPC, 0x00000000);
	rdt_ldpc_hb_correction_pattern_test(ALL_0xFF_ENABLE_LDPC_UECC, 0xFFFFFFFF);
	rdt_ldpc_hb_correction_pattern_test(ALL_0xFF_ENABLE_LDPC_CORR, 0xFFFFFFFF);

	/* Following Patterns are not yet released */
	// ibuffer to dbuffer path test
	rdt_ibuf_dbuf_path_test();
#endif
	rdt_api_switch_ldpc(backup_ECCMode);
	return CHECK_FAIL(ldpc_test_state);
}

#endif // RDT_MODE_EN
