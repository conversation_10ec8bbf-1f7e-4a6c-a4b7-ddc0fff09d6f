#ifndef _VUC_MICRONPHYSICALTOLOGICAL_H_
#define _VUC_MICRONPHYSICALTOLOGICAL_H_

#include "aom/aom_api.h"

#define VUC_MICRON_GET_BLOCK_NAND_MODE_HEADER_LENGTH	(12)


typedef struct {
	U16 uwCH;
	U16 uwCE;
	U16 uwLUN;
	U16 uwBlk;
	U16 uwPage;
	U16 uwFrameIdx;
} PhysicaltoLogicalAddressInputData_t;

typedef struct {
	U8 ubResponseHeaderFormatVersion;
	U8 ubResponseDataFotmatVersion;
	U16 uwCMDClass;
	U16 uwCMDCode;
	U16 uwStatus;
	U32 ulDataPayloadSize;
} PhysicaltoLogicalAddressHEADER_t;
#if (HOST_MODE == NVME)
AOM_VUC_3 void VUCMicronPhysicaltoLogicalAddress(U32 ulInputPayloadAddr, U32 ulPayloadAddr);
#else /* (HOST_MODE == NVME) */
AOM_VUC_3 void VUCMicronPhysicaltoLogicalAddress(U32 ulInputPayloadAddr, U32 ulPayloadAddr);
#endif /* (HOST_MODE == NVME) */
#endif /* _VUC_MICRONPHYSICALTOLOGICAL_H_ */
