// s
#include "setup.h"
#include "hal/sata/sata.h"
#include "hal/sata/sata_vuc.h"
#include "hal/sata/sata_cmd.h"
#include "hal/sata/sata_cmd_tbl.h"
#include "hal/sata/sata_vendor_cmd.h"

#if (HOST_MODE == SATA)

const pSATAFuncPtr SATACmdTable[256] = {

	//3 OP Code: 00 -0Fh

	/* 0x00: NOP (Non-Data) */
	SATACmdNOP,

	/* 0x01: (*Not Implemented) */
	CmdNotImplement,

	/* 0x02: (*Not Implemented) */
	CmdNotImplement,

	/* 0x03: (*Not Implemented) (Reserved for CFA) */
	CmdNotImplement,

	/* 0x04: (*Not Implemented) */
	CmdNotImplement,

	/* 0x05: (*Not Implemented) */
	CmdNotImplement,

	/* 0x06: DATA SET MANAGEMENT (DMA-Out) */
	SATACmdDataSetManagement, // Trim

	/* 0x07: (*Not Implemented) DATA SET MANAGEMENT XL */
	CmdNotImplement,

	/* 0x08: (*Not Implemented) ATAPI Soft Reset / DEVICE RESET */
	CmdNotImplement,

	/* 0x09: (*Not Implemented) */
	CmdNotImplement,

	/* 0x0A: (*Not Implemented) */
	CmdNotImplement,

	/* 0x0B: (*Not Implemented) REQUEST SENSE DATA EXT */
	CmdNotImplement,

	/* 0x0C: (*Not Implemented) */
	CmdNotImplement,

	/* 0x0D: (*Not Implemented) */
	CmdNotImplement,

	/* 0x0E: (*Not Implemented) */
	CmdNotImplement,

	/* 0x0F: (*Not Implemented) */
	CmdNotImplement,

	//3 ---------------------------------------------------------------------------------
	//3 OP Code: 10 -1Fh

	/* 0x10: RECALIBRATE (Non-Data) */
	SATACmdRecalibrate,

	/* 0x11: RECALIBRATE (Non-Data) */
	SATACmdRecalibrate,

	/* 0x12: RECALIBRATE (Non-Data) / GET PHYSICAL ELEMENT STATUS */
	SATACmdRecalibrate,

	/* 0x13: RECALIBRATE (Non-Data) */
	SATACmdRecalibrate,

	/* 0x14: RECALIBRATE (Non-Data) */
	SATACmdRecalibrate,

	/* 0x15: RECALIBRATE (Non-Data) */
	SATACmdRecalibrate,

	/* 0x16: RECALIBRATE (Non-Data) */
	SATACmdRecalibrate,

	/* 0x17: RECALIBRATE (Non-Data) */
	SATACmdRecalibrate,

	/* 0x18: RECALIBRATE (Non-Data) */
	SATACmdRecalibrate,

	/* 0x19: RECALIBRATE (Non-Data) */
	SATACmdRecalibrate,

	/* 0x1A: RECALIBRATE (Non-Data) */
	SATACmdRecalibrate,

	/* 0x1B: RECALIBRATE (Non-Data) */
	SATACmdRecalibrate,

	/* 0x1C: RECALIBRATE (Non-Data) */
	SATACmdRecalibrate,

	/* 0x1D: RECALIBRATE (Non-Data) */
	SATACmdRecalibrate,

	/* 0x1E: RECALIBRATE (Non-Data) */
	SATACmdRecalibrate,

	/* 0x1F: RECALIBRATE (Non-Data) */
	SATACmdRecalibrate,

	//3 ---------------------------------------------------------------------------------
	//3 OP Code: 20 -2Fh

	/* 0x20: READ SECTORS (PIO Data-In) */
	SATACmdSyncRead,

	/* 0x21: READ SECTORS WITHOUT RETRY (PIO Data-In) */
	SATACmdSyncReadSectorWithoutRetry,

	/* 0x22: (*Not Implemented) READ LONG */
	CmdNotImplement,

	/* 0x23: (*Not Implemented) READ LONG WITHOUT RETRY */
	CmdNotImplement,

	/* 0x24: READ SECTORS EXT (PIO Data-In) */
	SATACmdSyncRead,

	/* 0x25: READ DMA EXT (DMA-In) */
	SATACmdSyncRead,

	/* 0x26: (*Not Implemented) READ DMA QUEUED EXT */
	CmdNotImplement,

	/* 0x27: READ NATIVE MAX ADDRESS EXT (Non-Data) */
	SATACmdReadNativeMaxAddr,

	/* 0x28: (*Not Implemented) */
	CmdNotImplement,

	/* 0x29: READ MULTIPLE EXT (PIO Data-In) */
	SATACmdSyncRead,

	/* 0x2A: (*Not Implemented) READ STREAM DMA EXT */
	CmdNotImplement,

	/* 0x2B: (*Not Implemented) READ STREAM EXT */
	CmdNotImplement,

	/* 0x2C: (*Not Implemented) */
	CmdNotImplement,

	/* 0x2D: (*Not Implemented) */
	CmdNotImplement,

	/* 0x2E: (*Not Implemented) */
	CmdNotImplement,

	/* 0x2F: READ LOG EXT (PIO Data-In) */
	SATACmdReadLogExt,

	//3 ---------------------------------------------------------------------------------
	//3 OP Code: 30 -3Fh

	/* 0x30: WRITE SECTORS (PIO Data-Out) */
	CmdShouldNotBeSync,

	/* 0x31: WRITE SECTORS WITHOUT RETRY (PIO Data-Out) */
	SATACmdSyncWriteSectorWithoutRetry,

	/* 0x32: (*Not Implemented) WRITE LONG */
	CmdNotImplement,

	/* 0x33: (*Not Implemented) WRITE LONG WITHOUT RETRY */
	CmdNotImplement,

	/* 0x34: WRITE SECTORS EXT (PIO Data-Out) */
	CmdShouldNotBeSync,

	/* 0x35: WRITE DMA EXT (DMA-Out) */
	CmdShouldNotBeSync,

	/* 0x36: (*Not Implemented) WRITE DMA QUEUED EXT */
	CmdNotImplement,

	/* 0x37: SET NATIVE MAX ADDRESS EXT (Non-Data) */
	SATACmdSetMaxAddr,

	/* 0x38: CFA WRITE SECTORS WITHOUT ERASE (PIO Data-Out)*/
	SATACmdCFAWriteSector,

	/* 0x39: WRITE MULTIPLE EXT (PIO Data-Out) */
	CmdShouldNotBeSync,

	/* 0x3A: (*Not Implemented) WRITE STREAM DMA EXT */
	CmdNotImplement,

	/* 0x3B: (*Not Implemented) WRITE STREAM EXT */
	CmdNotImplement,

	/* 0x3C: (*Not Implemented) WRITE VERIFY */
	CmdNotImplement,

	/* 0x3D: WRITE DMA FUA EXT (DMA-Out) */
	CmdShouldNotBeSync,

	/* 0x3E: (*Not Implemented) WRITE DMA QUEUED FUA EXT */
	CmdNotImplement,

	/* 0x3F: WRITE LOG EXT (PIO Data-Out) */
	SATACmdWriteLogExt,

	//3 ---------------------------------------------------------------------------------
	//3 OP Code: 40 -4Fh

	/* 0x40: READ VERIFY SECTORS (Non-Data) */
	SATACmdReadVerify,

	/* 0x41: READ VERIFY SECTORS WITHOUT RETRY (Non-Data) */
	SATACmdReadVerify,

	/* 0x42: READ VERIFY SECTORS EXT (Non-Data) */
	SATACmdReadVerify,

	/* 0x43: (*Not Implemented) */
	CmdNotImplement,

	/* 0x44: ZERO EXT (Non-Data) */
	SATACmdZeroExt,

	/* 0x45: WRITE UNCORRECTABLE EXT (Non-Data) */
	SATACmdWriteUNC,

	/* 0x46: (*Not Implemented) */
	CmdNotImplement,

	/* 0x47: READ LOG DMA EXT (DMA-In) */
	SATACmdReadLogExt,

	/* 0x48: (*Not Implemented) */
	CmdNotImplement,

	/* 0x49: (*Not Implemented) */
	CmdNotImplement,

	/* 0x4A: (*Not Implemented) ZAC Management In */
	CmdNotImplement,

	/* 0x4B: (*Not Implemented) */
	CmdNotImplement,

	/* 0x4C: (*Not Implemented) */
	CmdNotImplement,

	/* 0x4D: (*Not Implemented) */
	CmdNotImplement,

	/* 0x4E: (*Not Implemented) */
	CmdNotImplement,

	/* 0x4F: (*Not Implemented) */
	CmdNotImplement,

	//3 ---------------------------------------------------------------------------------
	//3 OP Code: 50 -5Fh

	/* 0x50: (*Not Implemented) FORMAT TRACK */
	CmdNotImplement,

	/* 0x51: (*Not Implemented) CONFIGURE STREAM */
	CmdNotImplement,

	/* 0x52: (*Not Implemented) */
	CmdNotImplement,

	/* 0x53: (*Not Implemented) */
	CmdNotImplement,

	/* 0x54: (*Not Implemented) */
	CmdNotImplement,

	/* 0x55: (*Not Implemented) */
	CmdNotImplement,

	/* 0x56: (*Not Implemented) */
	CmdNotImplement,

	/* 0x57: WRITE LOG DMA EXT (DMA-Out) */
	SATACmdWriteLogExt,

	/* 0x58: (*Not Implemented) */
	CmdNotImplement,

	/* 0x59: (*Not Implemented) */
	CmdNotImplement,

	/* 0x5A: (*Not Implemented) */
	CmdNotImplement,

	/* 0x5B: TRUSTED NON-DATA */
	SATACmdTrustedNonData,

	/* 0x5C: TRUSTED RECEIVE (PIO-In) */
	SATACmdTrustedReceive,

	/* 0x5D: TRUSTED RECEIVE DMA (DMA-In) */
	SATACmdTrustedReceive,

	/* 0x5E: TRUSTED SEND (PIO-Out) */
	SATACmdTrustedSend,

	/* 0x5F: TRUSTED SEND DMA (DMA-Out) */
	SATACmdTrustedSend,

	//3 ---------------------------------------------------------------------------------
	//3 OP Code: 60 -6Fh

	/* 0x60: READ FPDMA QUEUED (DMA-In Queued) */
	SATACmdSyncRead,

	/* 0x61: WRITE FPDMA QUEUED (DMA-Out Queued) */
	CmdShouldNotBeSync,

	/* 0x62: (*Not Implemented) (SATA Reserved) */
	CmdNotImplement,

	/* 0x63: (*Not Implemented) NCQ NON-DATA */
	CmdNotImplement,

	/* 0x64: (*Not Implemented) SEND FPDMA QUEUED */
	CmdNotImplement,

	/* 0x65: (*Not Implemented) RECEIVE FPDMA QUEUED */
	CmdNotImplement,

	/* 0x66: (*Not Implemented) (SATA Reserved) */
	CmdNotImplement,

	/* 0x67: (*Not Implemented) (SATA Reserved) */
	CmdNotImplement,

	/* 0x68: (*Not Implemented) (SATA Reserved) */
	CmdNotImplement,

	/* 0x69: (*Not Implemented) (SATA Reserved) */
	CmdNotImplement,

	/* 0x6A: (*Not Implemented) (SATA Reserved) */
	CmdNotImplement,

	/* 0x6B: (*Not Implemented) (SATA Reserved) */
	CmdNotImplement,

	/* 0x6C: (*Not Implemented) (SATA Reserved) */
	CmdNotImplement,

	/* 0x6D: (*Not Implemented) (SATA Reserved) */
	CmdNotImplement,

	/* 0x6E: (*Not Implemented) (SATA Reserved) */
	CmdNotImplement,

	/* 0x6F: (*Not Implemented) (SATA Reserved) */
	CmdNotImplement,

	//3 ---------------------------------------------------------------------------------
	//3 OP Code: 70 -7Fh

	/* 0x70: SEEK (Non-Data) */
	SATACmdSeek,

	/* 0x71: SEEK (Non-Data) */
	SATACmdSeek,

	/* 0x72: SEEK (Non-Data) */
	SATACmdSeek,

	/* 0x73: SEEK (Non-Data) */
	SATACmdSeek,

	/* 0x74: SEEK (Non-Data) */
	SATACmdSeek,

	/* 0x75: SEEK (Non-Data) */
	SATACmdSeek,

	/* 0x76: SEEK (Non-Data) */
	SATACmdSeek,

	/* 0x77: (*Not Implemented) SET DATE & TIME EXT */
	CmdNotImplement,

	/* 0x78: ACCESSIBLE MAX ADDRESS CONFIGURATION (Non-Data) */
	SATACmdAMaxAddrConfig,

	/* 0x79: SEEK (Non-Data) */
	SATACmdSeek,

	/* 0x7A: SEEK (Non-Data) */
	SATACmdSeek,

	/* 0x7B: SEEK (Non-Data) */
	SATACmdSeek,

	/* 0x7C: SEEK (Non-Data) / REMOVE ELEMENT AND TRUNCATE */
	SATACmdSeek,

	/* 0x7D: SEEK (Non-Data) */
	SATACmdSeek,

	/* 0x7E: SEEK (Non-Data) */
	SATACmdSeek,

	/* 0x7F: SEEK (Non-Data) */
	SATACmdSeek,

	//3 ---------------------------------------------------------------------------------
	//3 OP Code: 80 -8Fh

	/* 0x80: (*Not Implemented) (Vendor Specific) */
	CmdNotImplement,

	/* 0x81: (*Not Implemented) (Vendor Specific) */
	CmdNotImplement,

	/* 0x82: (*Not Implemented) (Vendor Specific) */
	CmdNotImplement,

	/* 0x83: (*Not Implemented) (Vendor Specific) */
	CmdNotImplement,

	/* 0x84: (*Not Implemented) (Vendor Specific) */
	CmdNotImplement,

	/* 0x85: (*Not Implemented) (Vendor Specific) */
	CmdNotImplement,

	/* 0x86: (*Not Implemented) (Vendor Specific) */
	CmdNotImplement,

	/* 0x87: (*Not Implemented) (Vendor Specific) / (Reserved for CFA) */
	CmdNotImplement,

	/* 0x88: (*Not Implemented) (Vendor Specific) */
	CmdNotImplement,

	/* 0x89: (*Not Implemented) (Vendor Specific) */
	CmdNotImplement,

	/* 0x8A: (*Not Implemented) (Vendor Specific) */
	CmdNotImplement,

	/* 0x8B: (*Not Implemented) (Vendor Specific) */
	CmdNotImplement,

	/* 0x8C: (*Not Implemented) (Vendor Specific) */
	CmdNotImplement,

	/* 0x8D: (*Not Implemented) (Vendor Specific) */
	CmdNotImplement,

	/* 0x8E: (*Not Implemented) (Vendor Specific) -> Vendor Kingston Write Buffer */
	SATAVendorKingstonWriteBuf,

	/* 0x8F: (*Not Implemented) (Vendor Specific) -> Vendor Kingston Read Buffer */
	SATAVendorKingstonReadBuf,

	//3 ---------------------------------------------------------------------------------
	//3 OP Code: 90 -9Fh

	/* 0x90: EXECUTE DEVICE DIAGNOSTICS (Execute Device Diagnostic) */
	SATACmdDeviceDiagnostic,

	/* 0x91: INITIALIZE DEVICE PARAMETERS (Non-Data) */
	SATACmdInitDeviceParameter,

	/* 0x92: DOWNLOAD MICROCODE (PIO Data-Out / Non-Data) */
	SATACmdDLMC,

	/* 0x93: DOWNLOAD MICROCODE DMA (DMA-Out / Non-Data) */
	SATACmdDLMC,

	/* 0x94: (*Not Implemented) STANDBY IMMEDIATE(Retired) */
	CmdNotImplement,

	/* 0x95: (*Not Implemented) IDLE IMMEDIATE(Retired) */
	CmdNotImplement,

	/* 0x96: (*Not Implemented) STANDBY(Retired) */
	CmdNotImplement,

	/* 0x97: (*Not Implemented) IDLE(Retired) */
	CmdNotImplement,

	/* 0x98: (*Not Implemented) CHECK POWER MODE(Retired) */
	CmdNotImplement,

	/* 0x99: (*Not Implemented) SLEEP(Retired) */
	CmdNotImplement,

	/* 0x9A: (*Not Implemented) (Vendor Specific) */
	CmdNotImplement,

	/* 0x9B: (*Not Implemented) */
	CmdNotImplement,

	/* 0x9C: (*Not Implemented) */
	CmdNotImplement,

	/* 0x9D: (*Not Implemented) */
	CmdNotImplement,

	/* 0x9E: (*Not Implemented) */
	CmdNotImplement,

	/* 0x9F: (*Not Implemented) ZAC Management Out */
	CmdNotImplement,

	//3 ---------------------------------------------------------------------------------
	//3 OP Code: A0 -AFh

	/* 0xA0: (*Not Implemented) PACKET */
	CmdNotImplement,

	/* 0xA1: (*Not Implemented) IDENTIFY PACKET DEVICE */
	CmdNotImplement,

	/* 0xA2: (*Not Implemented) SERVICE */
	CmdNotImplement,

	/* 0xA3: (*Not Implemented) */
	CmdNotImplement,

	/* 0xA4: (*Not Implemented) */
	CmdNotImplement,

	/* 0xA5: (*Not Implemented) */
	CmdNotImplement,

	/* 0xA6: (*Not Implemented) */
	CmdNotImplement,

	/* 0xA7: (*Not Implemented) */
	CmdNotImplement,

	/* 0xA8: (*Not Implemented) */
	CmdNotImplement,

	/* 0xA9: (*Not Implemented) */
	CmdNotImplement,

	/* 0xAA: (*Not Implemented) */
	CmdNotImplement,

	/* 0xAB: (*Not Implemented) */
	CmdNotImplement,

	/* 0xAC: (*Not Implemented) */
	CmdNotImplement,

	/* 0xAD: (*Not Implemented) */
	CmdNotImplement,

	/* 0xAE: (*Not Implemented) */
	CmdNotImplement,

	/* 0xAF: (*Not Implemented) */
	CmdNotImplement,

	//3 ---------------------------------------------------------------------------------
	//3 OP Code: B0 -BFh

	/* 0xB0: SMART (PIO Data-In) */
	SATACmdSMART,

	/* 0xB1: DEVICE CONFIGURATION (PIO / DMA / Non-Data) */
	SATACmdDCO,

	/* 0xB2: (*Not Implemented) SET SECTOR CONFIGURATON EXT */
	CmdNotImplement,

	/* 0xB3: (*Not Implemented) */
	CmdNotImplement,

	/* 0xB4: Sanitize Device (Non-Data) */
	SATACmdSanitize,

	/* 0xB5: (*Not Implemented) */
	CmdNotImplement,

	/* 0xB6: (*Not Implemented) Non-Volatile(NV) CACHE */
	CmdNotImplement,

	/* 0xB7: (*Not Implemented) (Reserved for CFA) */
	CmdNotImplement,

	/* 0xB8: (*Not Implemented) (Reserved for CFA) */
	CmdNotImplement,

	/* 0xB9: (*Not Implemented) (Reserved for CFA) */
	CmdNotImplement,

	/* 0xBA: (*Not Implemented) (Reserved for CFA) */
	CmdNotImplement,

	/* 0xBB: (*Not Implemented) (Reserved for CFA) */
	CmdNotImplement,

	/* 0xBC: (*Not Implemented) (Reserved) */
	CmdNotImplement,

	/* 0xBD: (*Not Implemented) (Reserved) */
	CmdNotImplement,

	/* 0xBE: (*Not Implemented) (Reserved) */
	CmdNotImplement,

	/* 0xBF: (*Not Implemented) (Reserved) */
	CmdNotImplement,

	//3 ---------------------------------------------------------------------------------
	//3 OP Code: C0 -CFh

	/* 0xC0: (*Not Implemented) (Vendor Specific) / CFA ERASE SECTORS */
	CmdNotImplement,

	/* 0xC1: (*Not Implemented) (Vendor Specific) */
	CmdNotImplement,

	/* 0xC2: (*Not Implemented) (Vendor Specific) */
	CmdNotImplement,

	/* 0xC3: (*Not Implemented) (Vendor Specific) */
	CmdNotImplement,

	/* 0xC4: READ MULTIPLE (PIO Data-In) */
	SATACmdSyncRead,

	/* 0xC5: WRITE MULTIPLE (PIO Data-Out) */
	CmdShouldNotBeSync,

	/* 0xC6: SET MULTIPLE MODE (Non-Data) */
	SATACmdSetMultipleMode,

	/* 0xC7: (*Not Implemented) READ DMA QUEUED */
	CmdNotImplement,

	/* 0xC8: READ DMA (DMA-In) */
	SATACmdSyncRead,

	/* 0xC9: READ DMA WITHOUT RETRIES (DMA-In) */
	SATACmdSyncRead,

	/* 0xCA: WRITE DMA (DMA-Out) */
	CmdShouldNotBeSync,

	/* 0xCB: WRITE DMA WITHOUT RETRIES (DMA-Out) */
	CmdShouldNotBeSync,

	/* 0xCC: (*Not Implemented) WRITE DMA QUEUED */
	CmdNotImplement,

	/* 0xCD: (*Not Implemented) (Reserved for CFA) */
	CmdNotImplement,

	/* 0xCE: WRITE MULTIPLE FUA EXT (PIO Data-Out) */
	CmdShouldNotBeSync,

	/* 0xCF: (*Not Implemented) */
	CmdNotImplement,

	//3 ---------------------------------------------------------------------------------
	//3 OP Code: D0 -DFh

	/* 0xD0: (*Not Implemented) */
	CmdNotImplement,

	/* 0xD1: (*Not Implemented) CHECK MEDIA CARD TYPE */
	CmdNotImplement,

	/* 0xD2: (*Not Implemented) (Reserved for the Media Card Pass Through Command feature set) */
	CmdNotImplement,

	/* 0xD3: (*Not Implemented) (Reserved for the Media Card Pass Through Command feature set) */
	CmdNotImplement,

	/* 0xD4: (*Not Implemented) (Reserved for the Media Card Pass Through Command feature set) */
	CmdNotImplement,

	/* 0xD5: (*Not Implemented) */
	CmdNotImplement,

	/* 0xD6: (*Not Implemented) */
	CmdNotImplement,

	/* 0xD7: (*Not Implemented) */
	CmdNotImplement,

	/* 0xD8: (*Not Implemented) */
	CmdNotImplement,

	/* 0xD9: (*Not Implemented) */
	CmdNotImplement,

	/* 0xDA: (*Not Implemented) GET MEDIA STATUS */
	CmdNotImplement,

	/* 0xDB: (*Not Implemented) ACKNOWLEDGE MEDIA CHANGE */
	CmdNotImplement,

	/* 0xDC: (*Not Implemented) BOOT POST BOOT */
	CmdNotImplement,

	/* 0xDD: (*Not Implemented) BOOT PRE BOOT */
	CmdNotImplement,

	/* 0xDE: (*Not Implemented) MEDIA LOCK */
	CmdNotImplement,

	/* 0xDF: (*Not Implemented) MEDIA UNLOCK */
	CmdNotImplement,

	//3 ---------------------------------------------------------------------------------
	//3 OP Code: E0 -EFh

	/* 0xE0: STANDBY IMMEDIATE (Non-Data) */
	SATACmdStandby,

	/* 0xE1: IDLE IMMEDIATE (Non-Data) */
	SATACmdIdle,

	/* 0xE2: STANDBY (Non-Data) */
	SATACmdStandby,

	/* 0xE3: IDLE (Non-Data) */
	SATACmdIdle,

	/* 0xE4: READ BUFFER (PIO Data-In) */
	SATACmdReadBuf,

	/* 0xE5: CHECK POWER MODE (Non-Data) */
	SATACmdCheckPower,

	/* 0xE6: SLEEP (Non-Data) */
	SATACmdSleep,

	/* 0xE7: FLUSH CACHE (Non-Data) */
	SATACmdFlushCache,

	/* 0xE8: WRITE BUFFER (PIO Data-Out) */
	SATACmdWriteBuf,

	/* 0xE9: READ BUFFER DMA (DMA-In) */
	SATACmdReadBuf,

	/* 0xEA: FLUSH CACHE EXT (Non-Data) */
	SATACmdFlushCache,

	/* 0xEB: WRITE BUFFER DMA (DMA-Out) */
	SATACmdWriteBuf,

	/* 0xEC: IDENTIFY DEVICE (PIO Data-In) */
	SATACmdIdentifyDev,

	/* 0xED: (*Not Implemented) MEDIA EJECT */
	CmdNotImplement,

	/* 0xEE: (*Not Implemented) IDENTIFY DEVICE DMA */
	CmdNotImplement,

	/* 0xEF: SET FEATURES (Non-Data) */
	SATACmdSetFeature,

	//3 ---------------------------------------------------------------------------------
	//3 OP Code: F0 -FFh

	/* 0xF0: (*Not Implemented) (Vendor Specific) */
	CmdNotImplement,

	/* 0xF1: SECURITY SET PASSWORD (PIO Data-Out) */
	SATACmdSecurity,

	/* 0xF2: SECURITY UNLOCK (PIO Data-Out) */
	SATACmdSecurity,

	/* 0xF3: SECURITY ERASE PREPARE (Non-Data) */
	SATACmdSecurity,

	/* 0xF4: SECURITY ERASE UNIT (PIO Data-Out) */
	SATACmdSecurity,

	/* 0xF5: SECURITY FREEZE LOCK (Non-Data) */
	SATACmdSecurity,

	/* 0xF6: SECURITY DISABLE PASSWORD (PIO Data-Out) */
	SATACmdSecurity,

	/* 0xF7: (*Not Implemented) (Vendor Specific) */
	CmdNotImplement,

	/* 0xF8: READ NATIVE MAX ADDRESS (Non-Data) */
	SATACmdReadNativeMaxAddr,

	/* 0xF9: SET MAX ADDRESS (Non-Data) */
	SATACmdSetMaxAddr,

	/* 0xFA: (*Not Implemented) (Vendor Specific) -> Vendor set Cmd to PIO */
	SATAVendorSetPIOCmd,

	/* 0xFB: (*Not Implemented) (Vendor Specific) */
	CmdNotImplement,

	/* 0xFC: (*Not Implemented) (Vendor Specific) */
	CmdNotImplement,

	/* 0xFD: (*Not Implemented) (Vendor Specific) */
	CmdNotImplement,

	/* 0xFE: (*Not Implemented) (Vendor Specific) */
	CmdNotImplement,

	/* 0xFF: (*Not Implemented) (Vendor Specific) */
	CmdNotImplement

};

#endif /* (HOST_MODE == SATA) */

