#ifndef _DMAC_API_H_
#define _DMAC_API_H_

#include "setup.h"
#include "typedef.h"
#include "fw_vardef.h"
#include "dmac_cmd_type.h"
#include "aom/aom_api.h"

typedef enum SortRuleEnum {
	SORT_RULE_S_TO_L,
	SORT_RULE_L_TO_S
} SortRuleEnum_t;

typedef enum DMACCntPTEEntryEnum {
	DMAC_TABLE_1024_ENTRY,
	DMAC_TABLE_2048_ENTRY,
	DMAC_TABLE_4096_ENTRY,
	DMAC_TABLE_RESERVE_ENTRY
} DMACCntPTEEntryEnum_t;

#define DMAC_PUSH_SQ_SUCCESS			(1)
#define DMAC_PUSH_SQ_FAIL				(0)

#define DAMC_DEFAULT_TAG_ID	(0xFF)
#define DMAC_INVALID_SEARCH_RESULT (0x7FFF)
#define DMAC_SEARCH_VC_ZERO_MAX_VALID_RESULT	(8)
#define DMAC_NO_USE_VALIDBMP  (0)
#define DMAC_QUEUEID_SHIFT  (8)
#define DMAC_QUEUEID_IDX_MASK  (BIT_MASK(8))

/********************************************************************
			DMAC	OP
*********************************************************************/
#define DMAC_OP_COPY		(0x01)
#define DMAC_OP_SV			(0x02)
#define DMAC_OP_E2E			(0x03)
#define DMAC_OP_XOR			(0x04)
#define DMAC_OP_CMP			(0x05)
#define DMAC_OP_CRC32		(0x06)
#define DMAC_OP_SORT		(0x07)
#define	DMAC_OP_DMY			(0x08)
#define DMAC_OP_SMAX		(0x0C)
#define DMAC_OP_SBIT		(0x0D)
#define DMAC_OP_TRIM_MERGE	(0x0E)
#define DMAC_OP_SRH			(0x10)
#define DMAC_OP_CHKSUM		(0x12)
#define DMAC_OP_SCAN_ZCODE	(0x13)
#define DMAC_OP_SCAN_VLD	(0x14)
#define DMAC_OP_SEQ_PTE		(0x15)
#define DMAC_OP_CNT_PTE		(0x16)
#define DMAC_OP_SVM			(0x17)


/********************************************************************
			DMAC	Msk En
*********************************************************************/
#define DMAC_MASK_EN		(0x1)
#define DMAC_MASK_DIS		(0x0)
/********************************************************************
			DMAC	CQ	En
*********************************************************************/
#define DMAC_SET_CQ			(0x1)
#define DMAC_NOT_SET_CQ		(0x0)

#define LB_BASE				(0x19) //0x32000000 
#define PB_BASE				(0x11) //0x22000000

#define ZIP_LB_BASE			(0x09) //0x12000000
#define ZIP_PB_BASE			(0x01) //0x02000000

/********************************************************************
			DMAC	E2E	En
*********************************************************************/
#define E2E512 (0)
#define E2E4K (1)

// 0: no check no gen, 1: gen E3d512 check e3d4k, 2:check E3d512
#define E2EMODE_CHECKE3D4K_GENE3D512 (1)
#define E2EMODE_CHECKE3D512_GENE3D4K (2)


/********************************************************************
			DMAC	Entry	Size
*********************************************************************/
#define DMAC_ENTRY_SIZE_1B	(0x0)
#define DMAC_ENTRY_SIZE_2B	(0x1)
#define DMAC_ENTRY_SIZE_4B	(0x2)
#define DMAC_ENTRY_SIZE_8B	(0x3)


#define DMAC_OPERATOR_OR             (0)
#define DMAC_OPERATOR_AND            (1)
#define DMAC_OPERATOR_ASSIGN         (2)
#define DMAC_OPERATOR_NOT            (3)
/********************************************************************
			DMAC	Entry	Size
*********************************************************************/
#define DMAC_ENTRY_NUMBER_4K		(128)
/*******************************************************************
			DMAC	XOR	Oper	(XOR)
********************************************************************/
#define DMAC_XOR_OPER_XOR			(0)
#define DMAC_XOR_OPER_XNOR			(1)
#define DMAC_XOR_OPER_OR			(2)

/*******************************************************************
			DMAC	Search	Mode	(Search)
********************************************************************/
#define DMAC_SRH_MODE_E		0	// ==
#define DMAC_SRH_MODE_GE	1	// >=
#define DMAC_SRH_MODE_LE	2	// <=
#define DMAC_SRH_MODE_NE	3	// !=
#define DMAC_SRH_MAX_NUM	8
/*******************************************************************
			DMAC	SMAX	Mode	(SMAX)
********************************************************************/
#define DMAC_SMAX_MODE_MAX	(0)
#define DMAC_SMAX_MODE_MIN	(1)

/*******************************************************************
			DMAC	Sort	OP	(Sort)
********************************************************************/
#define DMAC_SORT_SCAN			(0)
#define DMAC_SORT_OFS_CPY_SCAN	(1)
#define DMAC_SORT_OFS_CPY		(2)
#define DMAC_SORT_OFS			(3)
#define DMAC_SORT_CPY			(4)
/*******************************************************************
			DMAC	Sort	Last	(Sort)
********************************************************************/
#define DMAC_SORT_NOT_SORT_LAST_CMD		(0)
#define DMAC_SORT_SORT_LAST_CMD			(1)
/*******************************************************************
			DMAC	Sort	Order	(Sort)
********************************************************************/
#define DMAC_SORT_ORDER_ASCEND			(0)
#define DMAC_SORT_ORDER_DECEND			(1)
/*******************************************************************
			DMAC	Sort	Entry Size	(Sort)
********************************************************************/
#define DMAC_SORT_ENTRY_SIZE_4B			(0)
#define DMAC_SORT_ENTRY_SIZE_8B			(1)
#define DMAC_SORT_ENTRY_SIZE_16B		(2)
/*******************************************************************
			DMAC	Sort	Bits (for Sort DT log)
********************************************************************/
#define PTE_PMD_INDEX_START_BIT			(32)
#define PTE_PMD_INDEX_BITS				(PS5017_EN ? 20 : 19) // S17, Support 4TB
#define INVALID_BIT_START_BIT           (PS5017_EN ? 61 : 60) // S17, Support 4TB
#define INVALID_BIT_BITS                (PS5017_EN ? 3 : 4) // S17, Support 4TB

/*******************************************************************
			DMAC	Scan Zcode	Type	(Scan Zcode)
********************************************************************/
#define DMAC_SCAN_ZCODE_TYPE_FOR_ZINFO	(0)
#define DMAC_SCAN_ZCODE_TYPE_FOR_VB		(1)
/*******************************************************************
			DMAC	Scan Zcode	ZInfo Mask	(Scan Zcode)
********************************************************************/
#define DMAC_SCAN_ZCODE_TYPE_FOR_ZINFO	(0)
#define DMAC_SCAN_ZCODE_TYPE_FOR_VB		(1)

/*******************************************************************
			DMAC	Scan Vld	(Scan Vld)
********************************************************************/

#define DMAC_SCAN_VLD_SCAN_TYPE_PTE_TABLE	(0)
#define DMAC_SCAN_VLD_SCAN_TYPE_VLD_TABLE	(1)

/*******************************************************************
			DMAC	Copy Vld	(Copy Vld)
********************************************************************/
#define DMAC_COPY_VLD_DEFAULT				(0)
#define DMAC_COPY_VLD_SOURCE				(1)
#define	DMAC_COPY_VLD_TARGET				(2)
#define DMAC_COPY_VLD_AllOCATE_LB_SOURCE	(3)
#define DMAC_COPY_VLD_AllOCATE_LB_TARGET	(4)

#define DMAC_COPY_UNLOCK_DEFAULT			(0)
#define DMAC_COPY_UNLOCK_SOURCE				(1)
#define DMAC_COPY_UNLOCK_TARGET				(2)

/*******************************************************************
			DMAC	CRC32	(CRC32)
********************************************************************/
#define DMAC_CRC32_ROM_EN		(ENABLE)
#define DMAC_CRC32_ROM_DIS		(DISABLE)

/********************************************************************
			DMAC	MARCO
********************************************************************/
#define M_BYTE_TO_32BYTE_ALIGN(N)	((N + 31) >> 5)
#define M_KBYTE_TO_32BYTE_ALIGN(N)	(((N << 10) + 31) >> 5)


#define M_CLEAR_DMAC_QBODY(PTR) do {\
	PTR->CommonCmd.ulDW0 = 0;\
	PTR->CommonCmd.ulDW1 = 0;\
	PTR->CommonCmd.ulDW2 = 0;\
	PTR->CommonCmd.ulDW3 = 0;\
	PTR->CommonCmd.ulDW4 = 0;\
	PTR->CommonCmd.ulDW5 = 0;\
	PTR->CommonCmd.ulDW6 = 0;\
	PTR->CommonCmd.ulDW7 = 0;\
} while(0)

#define DMAC_COPY_ZIPMODE_NORMAL	(0)
#define DMAC_COPY_ZIPMODE_ZIP	(1)
#define DMAC_COPY_ZIPMODE_UNZIP	(2)

#define DMAC_COPY_AESMODE_NORMAL	(0)
#define DMAC_COPY_AESMODE_AES	(1)
#define DMAC_COPY_AESMODE_UNAES	(2)

#define DMAC_SEARCH_MIN_DATA_D1     	(0)
#define DMAC_SEARCH_MIN_DATA_D3     	(1)
#define DMAC_SEARCH_MIN_DATA_D3_SLCPOOL	(2)
#define DMAC_SEARCH_MIN_DATA_D3_ALL		(3)
#define DMAC_SEARCH_MIN_DATA_D1D3		(4)
#define DMAC_SEARCH_MIN_DATA_ALL		(5) //D1 and D3
#define DMAC_SEARCH_MIN_TABLE          	(6)
#define DMAC_SEARCH_MIN_NO_PTE_BMP_DATA (7)
#define DMAC_SEARCH_MAX_DATA 			(8)

typedef struct {
	U32 ulValue;
	U32 ulOffset	: 31;
	U32 btValid		: 1;
} SMAXEntry_t;

typedef union DMACSQCmd {
	DMACCommonCmd_t		CommonCmd;
	DMACCopyCmd_t		CopyCmd;
	DMACXORCmd_t		XORCmd;
	DMACCRC32Cmd_t		CRC32Cmd;
	DMACSetValueCmd_t	SetValueCmd;
	DMACSortCmd_t		SortCmd;
	DMACSMaxCmd_t		SMaxCmd;
#if PS5017_EN
	DMACSBitCmd_t       SBitCmd;
#endif /* PS5017_EN */
	DMACSearchCmd_t		SearchCmd;
	DMACScanZcodeCmd_t	ScanZcodeCmd;
	DMACScanVldCmd_t	ScanVldCmd;
	DMACCompareCmd_t	CompareCmd;
	DMACTrimMergeCmd_t	TrimMergeCmd;
	DMACDmyCmd_t		DmyCmd;
	DMACSeqPTECmd_t		SeqPTECmd;
	DMACCntPTECmd_t		CntPTECmd;
	DMACE3DCmd_t		E3DCmd;
} DMACSQCmd_t;
TYPE_SIZE_CHECK(DMACSQCmd_t, SIZE_32B);

typedef union DMACCQRst {
	DMACCommonRst_t		uoCommonRst;
	DMACCopyRst_t		uoCopyRst;
	DMACXORRst_t		uoXORRst;
	DMACCRC32Rst_t		CRC32Rst;
	DMACSetValueRst_t	uoSetValueRst;
	DMACSortRst_t		uoSortRst;
	DMACSMaxRst_t		uoSMaxRst;
	DMACSearchRst_t		uoSearchRst;
	DMACScanZcodeRst_t	uoScanZcodeRst;
	DMACScanVldRst_t	uoScanVldRst;
	DMACCompareRst_t	uoCompareRst;
	DMACTrimMergeRst_t	uoTrimMergeRst;
	DMACSeqPTERst_t		uoSeqPTERst;
	DMACCntPTERst_t		uoCntPTERst;
	DMACE3DRst_t		E3DRst;
} DMACCQRst_t;

TYPE_SIZE_CHECK(DMACCQRst_t, SIZE_8B);

typedef struct {
	U32 ulMaskedValue1;
	U32 ulRefValue1;
} DMACSearchMask_t;

typedef union {
	struct {
		U32 ulSourceAddr;
		U32 ulDestAddr;
		U32 ul32ByteNum;
		U32 ulLCA;
		U8 ubEntrySize;
		U8 btGenE3D512: 1;
		U8 btValidateTarget: 1 ;
		U8 Reserved: 6;
		U8 ubReserved;
	};

	struct {
		U32 ulAddr0;
		U32 ulAddr1;
		U32 ul32ByteNum;
	} DMACCompare;

	struct {
		U32 ulDestAddr;
		U32 ulLowValue;
		U32 ulHighValue;
		U32 ul32ByteNum;
		U8 btValidate : 1;
		U8 : 7;
	} DMACSetValue;

	struct {
		U32 ulSourceAddr;
		U32 ulDestAddr;
		U32 ul32ByteNum;
		U32 ulStartLCA;
		U32 ulStartIdx;
	} DMACScanValid;

	struct {
		U32 ulSourceAddr;		// source address
		U32 ulDestAddr;			// destination address
		U32 ul32ByteNum;		// copy size in 32B
		U32 ulMask;				// bit mask, 1 bit for 512B data
		U32 ulLCA;				// for E3D encode
		U8 ubZipMode : 2;		// zip / unzip / nothing
		U8 ubValidateMode : 3;
		U8 ubUnLockMode : 2;
		U8 btQOP		: 1;
		U8 ubSourceLBID	: 3;
		U8 btZero       : 1;
		U8 AESMode	: 2;
		U8 E2EMode	: 2; // 0: no check no gen, 1: gen E3d512 check e3d4k, 2:check E3d512
		U8 ubZInfoSource;
	} DMACDataCopy;

	struct {
		U32 ulSourceAddr;
		U32 ulDestAddr;
		U32 ul32ByteNum;
		U8 ubEntrySize;
		U8 ubOrder : 1;
	} DMACSort;

	struct {
		U32 ulStartPCA;
		U32 ulDestAddr;
		U32 ulLCA;
		U32 ulDest32ByteNum;
		U16 uwTagId;
		U8 ubSkipPCASize;
		U8 Zinfo	  	: 3;
		U8 NSID	  		: 3;
		U8 Reserved 	: 2;
	} DMACSeqPTE;

	struct {
		U32 ulSrcAddr;
		U32 ulPTEMask;
		U32 Src32ByteNum		: 24;
		U32 DSAHeadSkipNum	: 2;
		U32 Reserve0			: 6;
	} DMACCntPTE;

	struct {
		U32 ulSourceAddr;
		U32 ulDestAddr;
		U32 ul32ByteNum;
		U8 NSID						: 3;
		U8 btWriteLockErrorCheckEn	: 1;
		U8 btIsNVMEFormat			: 1;
		U8 btMode					: 1;
		U8							: 2;
	} DMACTrimMerge;
#if PS5017_EN
	struct {
		U32 ulSourceAddr;
		U32 ulDest32ByteNum;
		U8  ubFindOne;
	} DMACSBit;
#endif /* PS5017_EN */
} DMACParam_t;

typedef union {
	U16 uwAll;
	struct {
		U16 uwSkipPCAOfs	: 13;	// [12:0]
		U16	btSkipPCAVld	: 1;	// [13]
		U16					: 2;	// [15:14]
	};
} DMACSeqPTESkipTableEntry_t;

typedef enum {
	DMAC_MODE_HIGH_QUEUE,
	DMAC_MODE_NORMAL_QUEUE
} DMACQueueMode_t;

typedef struct {
	U8 ubDoneFlag;
	U8 ubReserved[3];
	volatile U32 ulCRC32Result;
} DMACDirectWaitCQInfo_t;

typedef struct {
	U8 ubIRAMDMACDoneFlag;
	U8 ubANDESDMACDoneFlag;
	U8 ubReserved[2];
} DMACWaitCQInfo_t;

//*****************
//DMAC variable
//*****************
extern DMACDirectWaitCQInfo_t gDMACDirectWaitCQInfo;
extern DMACWaitCQInfo_t gDMACWaitCQInfo;
extern FW_CALLBACK_SECTION U32 gulDMACDirectWaitDone_Callback;
extern FW_CALLBACK_SECTION U32 gulDMACWaitIRAMDone_Callback;
extern FW_CALLBACK_SECTION U32 gulDMACWaitANDESDone_Callback;
extern FW_CALLBACK_SECTION U32 gulDMACDirectWaitCRCResult_Callback;

//************************************************************************
// DMAC Functions
//************************************************************************
void DMACDumpErrorInfo(void);
void DMACCopy(DMACQueueMode_t DMACSQMode, DMACParam_t *pParam, U32 ulCallbackAddr, U16 uwCallbackData);
void DMACSetZeroMask(DMACParam_t *pParam, U32 ulMask, U32 ulCallbackAddr, U16 uwCallbackData, U8 ubSetZeroFlag);
void DMACSetValue(DMACParam_t *pParam, U32 ulCallbackAddr, U16 CallbackData);
AOM_TABLE_UPDATE void DMACAPISortBit(DMACParam_t *pParam, U32 ulCallbackAddr, U8 ubStartBit, U8 ubSortBit);
void DMACScanValid(DMACParam_t *pParam, Unit_t *puwSourceUnit, U32 ulCallbackAddr, U32 ulCallbackData); //Callback function flow
AOM_GC void DMACScanZcode(DMACParam_t *pParam, U32 ulDest32ByteNum, U32 ulSrc32ByteNum, U32 ulCallbackAddr);
U8 DMACSearchData(DMACSearchMask_t uoSearchMask, U32 ulDestAddr, U32 ul32ByteNum, U32 ulCallbackAddr, U32 ulSrcAddr, U8 ubEntrySize, U16 uwValue);
AOM_GC void DMACSort(U32 ulSourceAddr, U32 ulDestAddr, U32 ul32ByteNum, U8 ubKeyIdx, U8 ubKeyNum, U8 ubOrder, U32 ulCallbackAddr);
U8 DMACSearchZeroUnit(DMACSearchMask_t uoSearchMask, U32 ulDestAddr, U32 ul32ByteNum, U32 ulCallbackAddr);
AOM_SPOR_3 U8 DMACSearchECSrcBit(U32 ulCallbackAddr);
void DMACCompare(DMACParam_t *pParam, U32 ulCallbackAddr, U16 uwCallbackData);  //Callback function flow
void DMACAPISRHVCDONE(DMACCQRst_t *pDMACCQRst);
AOM_GC U8 DMACSearchMinVC(U8 ubMode, U32 ulDestAddr, U32 ul32ByteNum, U32 ulCallbackAddr);
#if(PERFORMANCE_TEST_EN)
AOM_TABLE_UPDATE U8 DMACSearchMinEC(U8 ubIsD1Unit, U32 ulDestAddr, U32 ul32ByteNum, U32 ulCallbackAddr);
#else /*(PERFORMANCE_TEST_EN)*/
AOM_WL U8 DMACSearchMinEC(U8 ubIsD1Unit, U32 ulDestAddr, U32 ul32ByteNum, U32 ulCallbackAddr);
#endif /*(PERFORMANCE_TEST_EN)*/
AOM_FTL_EXT U8 DMACSearchMaxReadCnt(U32 ulDestAddr, U32 ul32ByteNum, U32 ulCallbackAddr);
AOM_TABLE_UPDATE void DMACBitmapOR(U32 ulDestAddr, U32 ulSourceAddr, U32 ul32ByteNum, U32 ulCallbackAddr);  //Callback function flow
AOM_INIT void InitDMACReg(void);
U8 DMACCopyHighPriority(U32 ulSourceAddr, U32 ulDestAddr, U32 ulCopy32ByteNum);
void DMAC_CRC32(DMACQueueMode_t DMACSQMode, U32 ulSourceAddr, U32 ulSeed, U32 ulTranByteCnt, U8 ubROMEn, U32 ulCallbackAddr, U16 uwCallbackData);
AOM_GC void DMAC_E3D4K(U32 ulSourceAddr, U32 ulCallbackAddr, U16 uwCallbackData, U32 ulLCA, U8 ubZInfo, U32 ulE3D4K, U8 ubUnAESEn);
AOM_HMB void DMAC_E3D512(U32 ulSourceAddr, U32 ulCallbackAddr, U16 uwCallbackData);
void DMAC_GenRandom(U32 ulSourceAddr, U32 ulLCA, U32 ulNSID, U32 ulTranByteCnt, U32 ulCallbackAddr, U16 uwCallbackData);
void DMAC_BitmapXOR(U32 ulDestAddr, U32 ulSourceAddr, U32 ul32ByteNum, U32 ulCallbackAddr);
AOM_TRIM void DMACTrimMerge(DMACParam_t *pParam, U32 ulCallbackAddr);
AOM_TABLE_UPDATE void DMACSeqPTE(DMACParam_t *pParam, DMACSeqPTESkipTableEntry_t *uwSipTable, U32 ulCallBackAddr, U16 uwCallBackData);
AOM_TABLE_UPDATE void DMACCntPTE(DMACParam_t *pParam, U32 ulCallBackAddr);
#if PS5017_EN
void DMACSBit(DMACParam_t *pParam, U32 ulCallbackAddr, U16 uwCallbackData);	//E19
#endif /* PS5017_EN */
void DMACDirectWaitDone_Callback(DMACCQRst_t *pDMACRst);
void DMACWaitIRAMDone_Callback(DMACCQRst_t *pDMACRst);
void DMACWaitANDESDone_Callback(DMACCQRst_t *pDMACRst);
void DMACDirectWaitCRCResult_Callback(DMACCQRst_t *pDMACRst);
void DMACDataCopyFunction(DMACParam_t *pParam, U32 ulCallbackAddr, U16 uwCallbackData, U8 ubValidBMP);

AOM_INIT_2 void DMACSetZeroValueWaitUntilDone_AOM_INIT_2(U32 ulDestAddr, U32 ulSize);
// DMAC inline function is included as API.
#include "dmac_inline.h"

#endif /* _DMAC_API_H_ */
