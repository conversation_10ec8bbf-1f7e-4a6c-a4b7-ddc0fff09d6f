#include "vuc/VUC_SetFlashFeature.h"
#include "host/VUC_handler.h"
#include "vuc/VUC_command.h"
#include "vuc/VUC_Utilities.h"
#include "hal/sys/api/efuc/efuse_api.h"
#include "hal/sys/api/clk/clk_api.h"
#include "hal/fip/fip_api.h"
#include "hal/pic/uart/uart_api.h"
#include "hal/dmac/dmac_api.h"
#include "hal/dmac/dmac_pop_cmd.h"

AOM_VUC void VUC_FlashFeatureConfigure(VUC_OPT_HCMD_PTR_t pCmd)
{
	if (TRUE == gFlhEnv.ulFlashDefaultType.BitMap.btNoSupport) {
		pCmd->ubState = CMD_ERROR;
		gVUCVar.ubVucCmdErrStatus = HOST_ERROR_FEATURE;
		return;
	}

	U32 ulFlashClockTemp = 0 ;
	U32 ulLength = pCmd->vuc_sqcmd.vendor.FlashFeatureConfigure.ulLength * BYTE_PER_DW ;
	U8 ubSubFeature = pCmd->vuc_sqcmd.vendor.FlashFeatureConfigure.ubSubFeature;
	U8 ubCETarget	= pCmd->vuc_sqcmd.vendor.FlashFeatureConfigure.ubCE;
	U8 ubMode = pCmd->vuc_sqcmd.vendor.FlashFeatureConfigure.ubMode;
	U8 ubData[TOSHIBA_GET_FEATURE_MIN_REQUIRE_BYTE] = {0}, ubClockWrite, ubClockRead;
	U8 ubFlashCE = 0;
	U8 ubChannel = 0;
	U8 ubBackupData[TOSHIBA_GET_FEATURE_MIN_REQUIRE_BYTE];


	DMACParam_t DMACParam = {{0}};

	DMACParam.DMACSetValue.ulDestAddr = pCmd->ulCurrentPhysicalMemoryAddr;
	DMACParam.DMACSetValue.ulLowValue = 0;
	DMACParam.DMACSetValue.ulHighValue = 0;
	DMACParam.DMACSetValue.ul32ByteNum = SIZE_IN_32B(ulLength);
	gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
	DMACSetValue(&DMACParam, gulDMACDirectWaitDone_Callback, 0);
	while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
		DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
	}

	if (FALSE == PhysicalCE2LogicalCHCE(((EFU_BANK3 *)gEFuseBank2347Info.ubEFuseBank3)->dw0.btCeDecoder, ubCETarget, &ubChannel, &ubFlashCE)) {
		pCmd->ubState = CMD_ERROR;
		return;
	}

	M_FIP_DIV_BACK_UP(ubChannel, ulFlashClockTemp);

	// Low frequency for set/get feature
	FlaSetClock(ubChannel, FLH_CLOCK_DIV_8);

	switch (ubSubFeature) {

	case GET_FLASH_FEATURE:
		//byte[3:0]		flash feature
		//byte[7:4]		driving
		//byte[11:8]		odt value
		//byte[15:12]	read clk rate
		//byte[16:20]	write clk rate

		FlaGetFeature(ubChannel, ubFlashCE, TOSHIBA_FEATURE_ADDR_MODE, ubData, FIP_NO_USE_PARAMETER, FIP_NO_USE_PARAMETER, FALSE, FIP_NO_USE_PARAMETER, FIP_NO_USE_PARAMETER);
		memcpy((U8 *)(pCmd->ulCurrentPhysicalMemoryAddr + INTERFACE_SHIFT), (U8 *)ubData, sizeof(ubData));

		FlaGetFeature(ubChannel, ubFlashCE, TOSHIBA_FEATURE_DRIVE, ubData, FIP_NO_USE_PARAMETER, FIP_NO_USE_PARAMETER, FALSE, FIP_NO_USE_PARAMETER, FIP_NO_USE_PARAMETER);
		memcpy((U8 *)(pCmd->ulCurrentPhysicalMemoryAddr + DRIVE_SHIFT), (U8 *)ubData, sizeof(ubData));

		FlaGetFeature(ubChannel, ubFlashCE, TOSHIBA_FEATURE_ADDR_TOGGLE2, ubData, FIP_NO_USE_PARAMETER, FIP_NO_USE_PARAMETER, FALSE, FIP_NO_USE_PARAMETER, FIP_NO_USE_PARAMETER);
		memcpy((U8 *)(pCmd->ulCurrentPhysicalMemoryAddr + ODT_SHIFT), (U8 *)ubData, sizeof(ubData));

		ClockGetReadWriteFrequency(&ubClockRead, &ubClockWrite);

		ubData[0] = ubClockRead;
		memcpy((U8 *)(pCmd->ulCurrentPhysicalMemoryAddr + CLOCK_READ_SHIFT), (U8 *)ubData, sizeof(ubData));
		ubData[0] = ubClockWrite;
		memcpy((U8 *)(pCmd->ulCurrentPhysicalMemoryAddr + CLOCK_WRITE_SHIFT), (U8 *)ubData, sizeof(ubData));

		break;
	case RETRIEVE_FLASH_FEATURE:
		break;
	case SET_FLASH_FEATURE_INTERFACE:
		if (BURNER_MODE_EN) {
			if (TOSHIBASANDISK == gFlhEnv.ulFlashDefaultType.BitMap.VendorType) {
				ubData[0] = ubMode ;
				FlaSetFeature(ubChannel, ubFlashCE, TOSHIBA_FEATURE_ADDR_MODE, ubData);

				if (TOSHIBA_FEATURE_TOOGLE == ubMode) {
					M_FIP_SET_LEGACY_MODE(ubChannel);
					M_FIP_SET_TOGGLE_MODE(ubChannel);
					gFlhEnv.ubCE_Interface[ubCETarget] = TOGGLE_INTERFACE;

					ClockGetReadWriteFrequency(&ubClockRead, &ubClockWrite);

					if ((ubClockRead >= CLOCK_SELECT_200M) || (ubClockWrite >= CLOCK_SELECT_200M)) {

						FlaGetFeature(ubChannel, ubFlashCE, TOSHIBA_FEATURE_ADDR_TOGGLE2, ubBackupData, FIP_NO_USE_PARAMETER, FIP_NO_USE_PARAMETER, FALSE, FIP_NO_USE_PARAMETER, FIP_NO_USE_PARAMETER);
						memcpy((void *)ubData, (void *)ubBackupData, TOSHIBA_GET_FEATURE_MIN_REQUIRE_BYTE);
						ubData[0] |= (SET_FEATURE_DIFFERENTIAL_MASK << SET_FEATURE_DIFFERENTIAL_SHIFT);
						FlaSetFeature(ubChannel, ubFlashCE, TOSHIBA_FEATURE_ADDR_TOGGLE2, ubData);

						FlaIOTypeSetting(ubChannel, FIP_IO_DIFFERENTIAL);
						gFlhEnv.ulFlashDefaultType.BitMap.btDifferential = TRUE;
					}
				}
				else if (TOSHIBA_FEATURE_SDR == ubMode) {

					M_FIP_SET_LEGACY_MODE(ubChannel);
					gFlhEnv.ubCE_Interface[ubCETarget] = LEGACY_INTERFACE;

					FlaGetFeature(ubChannel, ubFlashCE, TOSHIBA_FEATURE_ADDR_TOGGLE2, ubBackupData, FIP_NO_USE_PARAMETER, FIP_NO_USE_PARAMETER, FALSE, FIP_NO_USE_PARAMETER, FIP_NO_USE_PARAMETER);
					memcpy((void *)ubData, (void *)ubBackupData, TOSHIBA_GET_FEATURE_MIN_REQUIRE_BYTE);
					ubData[0] &= (~(SET_FEATURE_DIFFERENTIAL_MASK << SET_FEATURE_DIFFERENTIAL_SHIFT));
					FlaSetFeature(ubChannel, ubFlashCE, TOSHIBA_FEATURE_ADDR_TOGGLE2, ubData);

					FlaIOTypeSetting(ubChannel, FIP_IO_SINGLE_END);
					gFlhEnv.ulFlashDefaultType.BitMap.btDifferential = FALSE;
				}
			}
		}
		break;
	case SET_FLASH_DRIVING:
#if (BURNER_MODE_EN)
		{
			ubData[0] = ubMode;
			FlaSetFeature(ubChannel, ubFlashCE, TOSHIBA_FEATURE_DRIVE, ubData);
		}
#endif
		break;
	case SET_FLASH_ODT:
#if (BURNER_MODE_EN)
		{
			FlaGetFeature(ubChannel, ubFlashCE, TOSHIBA_FEATURE_ADDR_TOGGLE2, ubBackupData, FIP_NO_USE_PARAMETER, FIP_NO_USE_PARAMETER, FALSE, FIP_NO_USE_PARAMETER, FIP_NO_USE_PARAMETER);
			memcpy((void *)ubData, (void *)ubBackupData, TOSHIBA_GET_FEATURE_MIN_REQUIRE_BYTE);

			ubData[0] &= (~(SET_FEATURE_ODT_MASK << SET_FEATURE_ODT_SHIFT));
			ubData[0] |= (ubMode << SET_FEATURE_ODT_SHIFT);
			FlaSetFeature(ubChannel, ubFlashCE, TOSHIBA_FEATURE_ADDR_TOGGLE2, ubData);
		}
#endif
		break;
	case SET_FLASH_FREQUENCY:
#if (BURNER_MODE_EN)
		{
#if (PS5017_EN)
			// ubMode should be VUC_MP_FLASH_CLOCK_xxM
			ClockSetIPFlhRW(CLOCK_IP_FPHY_X2_R, ubMode);
			ClockSetIPFlhRW(CLOCK_IP_FPHY_X2_R, ubMode);
#elif !E21_TODO /* (PS5017_EN) */
			U8 ubClockSrc, ubClockDiv, ubBypass = FALSE;

			switch (ubMode) {
			case CLOCK_SELECT_10M:
				ubClockSrc = OSC1_KOUT3_100;
				ubClockDiv = CLOCK_DIV_10;
				break;
			case CLOCK_SELECT_40M:
				ubClockSrc = OSC2_KOUT6_41p7;
				ubClockDiv = CLOCK_DIV_10;
				break;
			case CLOCK_SELECT_50M:
				ubClockSrc = OSC1_KOUT3_100;
				ubClockDiv = CLOCK_DIV_2;
				break;
			case CLOCK_SELECT_166M:
				ubClockSrc = OSC2_KOUT1_667;
				ubClockDiv = CLOCK_DIV_4;
				break;
			case CLOCK_SELECT_200M:
				ubClockSrc = OSC1_KOUT1_800;
				ubClockDiv = CLOCK_DIV_4;
				break;
			case CLOCK_SELECT_333M:
				ubClockSrc = OSC2_KOUT1_667;
				ubClockDiv = CLOCK_DIV_2;
				break;
			case CLOCK_SELECT_400M:
				ubClockSrc = OSC1_KOUT1_800;
				ubClockDiv = CLOCK_DIV_2;
				break;
			case CLOCK_SELECT_533M:
				ubClockSrc = OSC1_KOUT2_533;
				ubClockDiv = CLOCK_DIV_1;
				break;
			case CLOCK_SELECT_667M:
				ubClockSrc = OSC2_KOUT1_667;
				ubClockDiv = CLOCK_DIV_1;
				break;
			case CLOCK_SELECT_800M:
				ubClockSrc = OSC1_KOUT1_800;
				ubClockDiv = CLOCK_DIV_1;
				break;
			default:
				ubBypass = TRUE;
				break;

			}
			if (TRUE == ubBypass) {
				ClockSetIPClock(CLOCK_CENTER_1, CLOCK_IP_0, ubClockSrc, ubClockDiv);
				ClockSetIPClock(CLOCK_CENTER_1, CLOCK_IP_1, ubClockSrc, ubClockDiv);
				ClockSetIPClock(CLOCK_CENTER_1, CLOCK_IP_2, ubClockSrc, ubClockDiv);
				ClockSetIPClock(CLOCK_CENTER_1, CLOCK_IP_3, ubClockSrc, ubClockDiv);
			}
#endif /* (PS5017_EN) */
		}
#endif
		break;
#if(MST_MODE_EN) //Dylan for V6 RDT porting,refer to the V6 based RDT code
		M_FW_ASSERT(ASSERT_VUC_0x0ACA, FALSE);
#elif (!MICRON_FSP_EN)
	case VUC_SET_FLASH_FEATURE_VERIFY_EARLY_LATER_BICS4:
		FIPIdentifyEarlyFinalBICS4(ubChannel, ubFlashCE, (U8 *)pCmd->ulCurrentPhysicalMemoryAddr);
		break;
#endif /*!MICRON_FSP_EN*/
	default:
		break;
	}

	M_FIP_DIV_RESTORE(ubChannel, ulFlashClockTemp);
	if (TRUE == gFlhEnv.ulFlashDefaultType.BitMap.btNoSupport) {
		gVUCVar.ubVucCmdErrStatus = HOST_ERROR_FLH_STATUS_TIMEOUT;
		pCmd->ubState = CMD_ERROR;
	}
}

