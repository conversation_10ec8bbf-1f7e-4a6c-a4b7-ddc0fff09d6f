#include "burner/Burner_api.h"
#include "spare.h"
#include "ftl/ftl_api.h"
#include "hal/dmac/dmac_api.h"
#include "hal/dmac/dmac_pop_cmd.h"
#include "hal/pic/uart/uart_api.h"
#include "init/fw_init.h"
#include "nvme_api/nvme/shr_hal_nvme.h"
#include "vuc/VUC_ReadPH.h"
#include "vuc/VUC_ScanPH.h"
#include "vuc/VUC_WritePH.h"
#include "host/VUC_handler_api.h"

#if (RDT_MODE_EN && !U17_EN)
void VUCWritePH(VUC_OPT_HCMD_PTR_t pCmd)
{
	U8 ubSubfeature = pCmd->vuc_sqcmd.vendor.WritePH.ubSubFeature;
	U8 ubRet = FAIL;
	U8 ubNeedErase = 0;
	U8 ubNeedEraseBlk;
	FlashAccessInfo_t FlashInfo = {0};
	U8 ubBlkPtr;

	//Scan DBT header to assign gProductHistory.PHBlock[].uwAll first
	if ((INVALID_BLOCK == gPH.PHBlk[0].uwAll) || (INVALID_BLOCK == gPH.PHBlk[1].uwAll)) {
		pCmd->ubState = CMD_ERROR;
		M_UART(VUC_, "\n Product History blocks are not assigned!");
		return;
	}

	M_UART(VUC_, "\n Product History blocks are %l %l!", gPH.PHBlk[0].uwAll, gPH.PHBlk[1].uwAll);

	if (FALSE == gPH.Info.B.btScannedDone) {
		VUCScanPH(READ_BUF_BASE);
	}

	if (gPH.uwLastPage == (gulFastPagePlanesPerUnit / gubPlanesPerSuperPage)) {
		if (PH_UART_EN) {
			UartPrintf("\n [PHInfo] ChangeBlk");
		}

		ubNeedErase = 1; // erase old block later
		ubNeedEraseBlk = gPH.Info.B.btActiveBlk;
		if (VUC_PH_NO_LOG_FOUND != gPH.uwRDTLogPage) {
			ubRet = VUCReadPHLog(VUC_PH_RDT_LOG, PROGRAM_BUF_BASE);
			if (ubRet == FAIL) {
				pCmd->ubState = CMD_ERROR;
				return;
			}
		}

		if (VUC_PH_NO_LOG_FOUND != gPH.uwMPLogPage) {
			ubRet = VUCReadPHLog(VUC_PH_MP_LOG, PROGRAM_BUF_BASE + VUC_PH_LOG_SIZE);
			if (ubRet == FAIL) {
				pCmd->ubState = CMD_ERROR;
				return;
			}
		}
		gPH.Info.B.btActiveBlk ^= 1;
		gPH.uwLastPage = 0;

		if (VUC_PH_NO_LOG_FOUND != gPH.uwRDTLogPage) {
			ubRet = VUCWritePHLog(VUC_PH_RDT_LOG, PROGRAM_BUF_BASE);
			if (ubRet == FAIL) {
				pCmd->ubState = CMD_ERROR;
				return;
			}
		}

		if (VUC_PH_NO_LOG_FOUND != gPH.uwMPLogPage) {
			ubRet = VUCWritePHLog(VUC_PH_MP_LOG, PROGRAM_BUF_BASE + VUC_PH_LOG_SIZE);
			if (ubRet == FAIL) {
				pCmd->ubState = CMD_ERROR;
				return;
			}
		}

	}

	ubRet = VUCWritePHLog(ubSubfeature, pCmd->ulCurrentPhysicalMemoryAddr);
	UartPrintf("\n pCmd->ulCurrentPhysicalMemoryAddr=%l BURNER_VENDOR_BUF_BASE=%l", pCmd->ulCurrentPhysicalMemoryAddr, BURNER_VENDOR_BUF_BASE);
	if (FAIL == ubRet) {
		pCmd->ubState = CMD_ERROR;
		return;
	}

	if (ubNeedErase) {
		ubBlkPtr = gPH.PHBlk[ubNeedEraseBlk].Info.ubBlockOrder;
		FlashInfo.ulBlock = ubBlkPtr / (PLANE_BANK_NUM);
		FlashInfo.ubChannel = gPH.PHBlk[ubNeedEraseBlk].Info.Channel;
		FlashInfo.ubFlashCE = gPH.PHBlk[ubNeedEraseBlk].Info.CEperCH;
		FlashInfo.ubLUN = 0;
		FlashInfo.ubPlane = ubBlkPtr % PLANE_BANK_NUM;
		FlashInfo.uwPage = 0;
		if (PH_UART_EN) {
			UartPrintf("\n [PHInfo] Erase old Block %d", ubNeedEraseBlk);
		}

		// Erase Block
		BurnerEraseBLK(gFlhEnv.ubSLCMethod, MODE_D3, (FlashAccessInfo_t *)&FlashInfo, 0);
		BurnerWaitCQDone(guwBurnerBGTag);
	}

	//-------------------------------
	//		Read Data Check
	//-------------------------------

	ubRet = VUCReadPHLog(ubSubfeature, READ_BUF_BASE);
	if (FAIL == ubRet) {
		pCmd->ubState = CMD_ERROR;
		return;
	}

	// Compare Data
	ubRet = memcmp((void *)pCmd->ulCurrentPhysicalMemoryAddr, (void *)READ_BUF_BASE, VUC_PH_LOG_SIZE);

	if (ubRet) {
		pCmd->ubState = CMD_ERROR;
		M_UART(VUC_, "\n Product History CMP ERR!");
		return;
	}

	pCmd->ubState = FW_PROCESS_DONE;
}
#else /* (RDT_MODE_EN) */
#if (PHBLOCK_EN)
void VUCWritePH(VUC_OPT_HCMD_PTR_t pCmd)
{
	U8 ubSubfeature = pCmd->vuc_sqcmd.vendor.WritePH.ubSubFeature;
	U8 ubRet;
	U8 ubNeedErase = 0;
	U8 ubNeedEraseBlk;
	FlashAccessInfo_t FlashInfo = {0};
	U8 ubBlkPtr;
	U32 ulCmdTransferDataSize = (U32)(DEF_4B * pCmd->vuc_sqcmd.vendor.WritePH.ulLength);
	DMACParam_t DMACParameter;

	if (VUC_PH_LOG_SIZE != ulCmdTransferDataSize) {
		M_UART(VUC_, "Transfer size is wrong: %x\n", ulCmdTransferDataSize);
		pCmd->ubState = CMD_ERROR;
		gVUCVar.ubVucCmdErrStatus = HOST_ERROR_LENGTH_NOT_SUPPORT;
		return;
	}

	if (PH_UART_EN) {
		M_UART(VUC_, "\n [PHInfo] WriteProductHistory %d-%d", pCmd->ulTotalLCA, pCmd->ulDoneLCA);
	}

#if (HOST_MODE == NVME)
	if (1 == pCmd->ulDoneLCA) {
		COP1APIST3CReleaseCache(FWLB_VENDER_SIZE_IN_4K);
		BufferAllocateFWLBPBLink(FWLB_VENDER_USAGE_BIT, ALLOCATE_NO_USE_CTAG, ALLOCATE_NO_USE_LCA);
		gPH.ulHostBufAddr = M_LB_TO_ADDR(LB_ID_FW, gFWLBMgr.Type[FWLB_VENDER_USAGE].uwLBOffset);
		M_SIM_LB_ADDR_TO_VADDR(gPH.ulHostBufAddr);
		if (PH_UART_EN) {
			M_UART(VUC_, "\n [PHInfo] FTLAllocateFWLBPBLink Done");
		}
	}

	DMACParameter.ulSourceAddr =  pCmd->ulCurrentPhysicalMemoryAddr;
	DMACParameter.ulDestAddr = gPH.ulHostBufAddr + (U32)BC_4KB * (pCmd->ulDoneLCA - 1);
	DMACParameter.ul32ByteNum = SIZE_IN_32B(BC_4KB) ;
	gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
	DMACCopy(DMAC_MODE_NORMAL_QUEUE, &DMACParameter, gulDMACDirectWaitDone_Callback, 0);
	while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
		DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
	}
	if (PH_UART_EN) {
		M_UART(VUC_, "\n [PHInfo] Copy S:%x to D:%x", DMACParameter.ulSourceAddr, DMACParameter.ulDestAddr);
		M_UART(VUC_, " PTN %b", *(U8 *)(DMACParameter.ulSourceAddr));
	}


	if (pCmd->ulDoneLCA == pCmd->ulTotalLCA) {

		if (FALSE == gPH.Info.B.btScannedDone) {
			VUCScanPH(gPH.ulHostBufAddr);
		}

		if (gPH.uwLastPage == (gulFastPagePlanesPerUnit / gubPlanesPerSuperPage)) {
			if (PH_UART_EN) {
				M_UART(VUC_, "\n [PHInfo] ChangeBlk");
			}

			ubNeedErase = 1; // erase old block later
			ubNeedEraseBlk = gPH.Info.B.btActiveBlk;
			VUCReadPHLog(VUC_PH_RDT_LOG, gPH.ulHostBufAddr + VUC_PH_LOG_SIZE);
			VUCReadPHLog(VUC_PH_MP_LOG, gPH.ulHostBufAddr + VUC_PH_MP_LOG_OFFSET);
			VUCReadPHLog(VUC_PH_EC, gPH.ulHostBufAddr + VUC_PH_EC_OFFSET);
#if VRLC_EN
			VUCReadPHLog(VUC_PH_VRLC, gPH.ulHostBufAddr + VUC_PH_VRLC_OFFSET);
#endif /* VRLC_EN */
			gPH.Info.B.btActiveBlk ^= 1;
			gPH.uwLastPage = 0;

			ubRet = VUCWritePHLog(VUC_PH_RDT_LOG, gPH.ulHostBufAddr + VUC_PH_LOG_SIZE);
			if (FAIL == ubRet) {
				pCmd->ubState = CMD_ERROR;
				return;
			}
			ubRet = VUCWritePHLog(VUC_PH_MP_LOG, gPH.ulHostBufAddr + VUC_PH_MP_LOG_OFFSET);
			if (FAIL == ubRet) {
				pCmd->ubState = CMD_ERROR;
				return;
			}
			ubRet = VUCWritePHLog(VUC_PH_EC, gPH.ulHostBufAddr + VUC_PH_EC_OFFSET);
			if (FAIL == ubRet) {
				pCmd->ubState = CMD_ERROR;
				return;
			}
#if VRLC_EN
			ubRet = VUCWritePHLog(VUC_PH_VRLC, gPH.ulHostBufAddr + VUC_PH_VRLC_OFFSET);
			if (FAIL == ubRet) {
				pCmd->ubState = CMD_ERROR;
				return;
			}
#endif /* VRLC_EN */

		}

		ubRet = VUCWritePHLog(ubSubfeature, gPH.ulHostBufAddr);
		if (FAIL == ubRet) {
			pCmd->ubState = CMD_ERROR;
			return;
		}

		if (ubNeedErase) { /* parasoft-suppress BD-PB-CC "Jenkins cannot know the variable's value which is read from nand"*/
			ubBlkPtr = gPH.PHBlk[ubNeedEraseBlk].Info.ubBlockOrder;
			FlashInfo.ulBlock = ubBlkPtr / (PLANE_BANK_NUM);
			FlashInfo.ubChannel = gPH.PHBlk[ubNeedEraseBlk].Info.Channel;
			FlashInfo.ubFlashCE = gPH.PHBlk[ubNeedEraseBlk].Info.CEperCH;
			FlashInfo.ubLUN = 0;
			FlashInfo.ubPlane = ubBlkPtr % PLANE_BANK_NUM;
			FlashInfo.uwPage = 0;
			if (PH_UART_EN) {
				M_UART(VUC_, "\n [PHInfo] Erase old Block %d", ubNeedEraseBlk);
			}

			// Erase Block
			BurnerEraseBLK(gFlhEnv.ubSLCMethod, MODE_D3, (FlashAccessInfo_t *)&FlashInfo, 0);
			BurnerWaitCQDone(guwBurnerBGTag);
		}

		BufferFreeFWLBPBLink(FWLB_VENDER_USAGE_BIT);
		COP1APIST3CCollectCache(FWLB_VENDER_SIZE_IN_4K);
	}
#else /* (HOST_MODE == NVME) */

	COP1APIST3CReleaseCache(FWLB_VENDER_SIZE_IN_4K);
	BufferAllocateFWLBPBLink(FWLB_VENDER_USAGE_BIT, ALLOCATE_NO_USE_CTAG, ALLOCATE_NO_USE_LCA);
	gPH.ulHostBufAddr = M_LB_TO_ADDR(LB_ID_FW, gFWLBMgr.Type[FWLB_VENDER_USAGE].uwLBOffset);
	M_SIM_LB_ADDR_TO_VADDR(gPH.ulHostBufAddr);
	if (PH_UART_EN) {
		M_UART(VUC_, "\n [PHInfo] FTLAllocateFWLBPBLink Done");
	}

	//Copy Product History from VUC buffer to write to flash buffer
	DMACParameter.ulSourceAddr =  pCmd->ulCurrentPhysicalMemoryAddr;
	DMACParameter.ulDestAddr = gPH.ulHostBufAddr;
	DMACParameter.ul32ByteNum = SIZE_IN_32B(BC_16KB) ;
	gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
	DMACCopy(DMAC_MODE_NORMAL_QUEUE, &DMACParameter, gulDMACDirectWaitDone_Callback, 0);
	while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
		DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
	}
	if (PH_UART_EN) {
		M_UART(VUC_, "\n [PHInfo] Copy S:%x to D:%x", DMACParameter.ulSourceAddr, DMACParameter.ulDestAddr);
		M_UART(VUC_, " PTN %b", *(U8 *)(DMACParameter.ulSourceAddr));
	}

	if (FALSE == gPH.Info.B.btScannedDone) {
		VUCScanPH(gPH.ulHostBufAddr);
	}

	if (gPH.uwLastPage == (gulFastPagePlanesPerUnit / gubPlanesPerSuperPage)) {
		if (PH_UART_EN) {
			M_UART(VUC_, "\n [PHInfo] ChangeBlk");
		}

		ubNeedErase = 1; // erase old block later
		ubNeedEraseBlk = gPH.Info.B.btActiveBlk;
		VUCReadPHLog(VUC_PH_RDT_LOG, gPH.ulHostBufAddr + VUC_PH_LOG_SIZE);
		VUCReadPHLog(VUC_PH_MP_LOG, gPH.ulHostBufAddr + VUC_PH_MP_LOG_OFFSET);
		VUCReadPHLog(VUC_PH_EC, gPH.ulHostBufAddr + VUC_PH_EC_OFFSET);
#if VRLC_EN
		VUCReadPHLog(VUC_PH_VRLC, gPH.ulHostBufAddr + VUC_PH_VRLC_OFFSET);
#endif /* VRLC_EN */
		gPH.Info.B.btActiveBlk ^= 1;
		gPH.uwLastPage = 0;

		ubRet = VUCWritePHLog(VUC_PH_RDT_LOG, gPH.ulHostBufAddr + VUC_PH_LOG_SIZE);
		if (FAIL == ubRet) {
			pCmd->ubState = CMD_ERROR;
			return;
		}
		ubRet = VUCWritePHLog(VUC_PH_MP_LOG, gPH.ulHostBufAddr + VUC_PH_MP_LOG_OFFSET);
		if (FAIL == ubRet) {
			pCmd->ubState = CMD_ERROR;
			return;
		}
		ubRet = VUCWritePHLog(VUC_PH_EC, gPH.ulHostBufAddr + VUC_PH_EC_OFFSET);
		if (FAIL == ubRet) {
			pCmd->ubState = CMD_ERROR;
			return;
		}
#if VRLC_EN
		ubRet = VUCWritePHLog(VUC_PH_VRLC, gPH.ulHostBufAddr + VUC_PH_VRLC_OFFSET);
		if (FAIL == ubRet) {
			pCmd->ubState = CMD_ERROR;
			return;
		}
#endif /* VRLC_EN */

	}

	ubRet = VUCWritePHLog(ubSubfeature, gPH.ulHostBufAddr);
	if (FAIL == ubRet) {
		pCmd->ubState = CMD_ERROR;
		return;
	}

	if (ubNeedErase) { /* parasoft-suppress BD-PB-CC "Jenkins cannot know the variable's value which is read from nand"*/
		ubBlkPtr = gPH.PHBlk[ubNeedEraseBlk].Info.ubBlockOrder;
		FlashInfo.ulBlock = ubBlkPtr / (PLANE_BANK_NUM);
		FlashInfo.ubChannel = gPH.PHBlk[ubNeedEraseBlk].Info.Channel;
		FlashInfo.ubFlashCE = gPH.PHBlk[ubNeedEraseBlk].Info.CEperCH;
		FlashInfo.ubLUN = 0;
		FlashInfo.ubPlane = ubBlkPtr % PLANE_BANK_NUM;
		FlashInfo.uwPage = 0;
		if (PH_UART_EN) {
			M_UART(VUC_, "\n [PHInfo] Erase old Block %d", ubNeedEraseBlk);
		}

		// Erase Block
		BurnerEraseBLK(gFlhEnv.ubSLCMethod, MODE_D3, (FlashAccessInfo_t *)&FlashInfo, 0);
		BurnerWaitCQDone(guwBurnerBGTag);
	}
	BufferFreeFWLBPBLink(FWLB_VENDER_USAGE_BIT);
	COP1APIST3CCollectCache(FWLB_VENDER_SIZE_IN_4K);
#endif /* (HOST_MODE == NVME) */

	pCmd->ubState = FW_PROCESS_DONE;
}
#endif /* (PHBLOCK_EN) */
#endif /* (RDT_MODE_EN) */
#if (PHBLOCK_EN)
U8 VUCWritePHLog(U8 ubSubfeature, U32 ulDstAddr)
{
	FlashAccessInfo_t FlashInfo = {0};
	P4KTableHigh8B_t L4KInfo = {0};
	U8 ubRet;
	U8 ubL4KNum = gub4kEntrysPerPlane;
	U8 ubBlkPtr, ubProgramCnt, ubProgramNum;
	ubBlkPtr = gPH.PHBlk[gPH.Info.B.btActiveBlk].Info.ubBlockOrder;
	FlashInfo.ulBlock = ubBlkPtr / PLANE_BANK_NUM;
	FlashInfo.ubChannel = gPH.PHBlk[gPH.Info.B.btActiveBlk].Info.Channel;
	FlashInfo.ubFlashCE = gPH.PHBlk[gPH.Info.B.btActiveBlk].Info.CEperCH;
	FlashInfo.ubLUN = 0;
	FlashInfo.ubPlane = ubBlkPtr % PLANE_BANK_NUM;
	FlashInfo.uwPage = gPH.uwLastPage;
	ubProgramNum = VUC_PH_LOG_SIZE / gFlhEnv.uwPageByteCnt;
	gubLeavePreformatFlag = 0;


	M_FW_ASSERT(ASSERT_VUC_0x0AD1, gPH.uwLastPage < (gulFastPagePlanesPerUnit / gubPlanesPerSuperPage));

	if (VUC_PH_MP_LOG == ubSubfeature) {
		gPH.uwMPLogPage  = FlashInfo.uwPage;
		L4KInfo.ulLCA = SPARE_LCA_PH_MP_LOG;
	}
	else if (VUC_PH_EC == ubSubfeature) {
		gPH.uwECLogPage  = FlashInfo.uwPage;
		L4KInfo.ulLCA = SPARE_LCA_PH_EC_LOG;
	}
#if VRLC_EN
	else if (VUC_PH_VRLC == ubSubfeature) {
		gPH.uwVRLCLogPage  = FlashInfo.uwPage;
		L4KInfo.ulLCA = SPARE_LCA_PH_VRLC_LOG;
	}
#endif
	else {
		gPH.uwRDTLogPage  = FlashInfo.uwPage;
		L4KInfo.ulLCA = SPARE_LCA_PH_RDT_LOG;
	}

	for (ubProgramCnt = 0; ubProgramCnt < ubProgramNum; ubProgramCnt++) {
		// Set Buffer Address
		FlashInfo.ulBufBase = ulDstAddr + (U32)ubProgramCnt * gFlhEnv.uwPageByteCnt;
		ubRet = BurnerProgramSetCop0SQ(&FlashInfo, &L4KInfo, ubL4KNum);
		if (ubRet) {
			gubLeavePreformatFlag = 1;
			return FAIL;
		}
		FlashInfo.uwPage++;
		gPH.uwLastPage++;
	}

	gubLeavePreformatFlag = 1;
	return PASS;
}
#endif /* (PHBLOCK_EN) */
