#include <string.h>
#include "hal/security/security_api.h"
#include "tcg.h"
#include "tcg_pbkdf2.h"

#if (TCG_EN && (PS5013_EN || PS5021_EN || (PS5017_EN && !BURNER_MODE_EN)) && !RDT_MODE_EN && !LPM3_LOADER && !BOOTLOADER_MODE_EN)
void PBKDF2(U8 *pubPassword, U32 ulPasswordLength, U8 *pubSalt, U32 ulSaltLength, U32 ulDkLength, U32 ulIteration, U8 *pubOutput)
{
	// PBKDF2 use HMAC-256, cannot be changed, password length max is 64-byte (security key), so HMAC-256 is enough
	U32 ulLength = 0;
	U32 uli = 0;
	U32 ulj = 0;

	U8 aubI[PBKDF2_I_SIZE] = {0};

	U8 *pubT = gTcg.pVT->ubSecuritySrcBufAddr128;
	U8 *pubU = gTcg.pVT->ubSecuritySrcBufAddr128 + SIZE_64B;

	ulLength = (ulDkLength % SECURITY_SHA_256_OUTPUT_LENGTH > 0) ? (ulDkLength / SECURITY_SHA_256_OUTPUT_LENGTH + 1) : (ulDkLength / SECURITY_SHA_256_OUTPUT_LENGTH);

	memset((void *)SECURITY_SHA_HMAC_KEY_BASE, 0x00, SIZE_64B);
	memcpy((void *)SECURITY_SHA_HMAC_KEY_BASE, (void *)(pubPassword), ulPasswordLength);
	R32_SECURITY_SHA[R32_SECURITY_SHA_SOC_ADDR] = (U32)pubU;
	R32_SECURITY_SHA[R32_SECURITY_SHA_TAG_ADDR] = (U32)pubU;

	for (uli = 1; uli <= ulLength; uli++) {
		memset((void *)pubT, 0x00, PBKDF2_T_SIZE);
		aubI[0] = (U8)(uli >> 24);
		aubI[1] = (U8)(uli >> 16);
		aubI[2] = (U8)(uli >> 8);
		aubI[3] = (U8)uli;

		memcpy((void *)pubU, (void *)pubSalt, ulSaltLength);
		memcpy((void *)(pubU + ulSaltLength), (void *)aubI, 4);

		SecurityPBKDF2HMAC256WithXOR(pubU, (ulSaltLength + 4), FALSE, pubT);
		// Do XOR from previous round output
		for (ulj = 1; ulj < ulIteration; ulj++) {
			SecurityPBKDF2HMAC256WithXOR(pubU, SECURITY_SHA_256_OUTPUT_LENGTH, TRUE, pubT);
		}
		// Final round XOR
		for (ulj = 0; ulj < (SECURITY_SHA_256_OUTPUT_LENGTH / SIZE_8B); ulj++) {
			*(((U64 *)pubT) + ulj) = *(((U64 *)pubT) + ulj) ^ *(((U64 *)pubU) + ulj);
		}
		memcpy((void *)(pubOutput + (SECURITY_SHA_256_OUTPUT_LENGTH * (uli - 1))), pubT, SECURITY_SHA_256_OUTPUT_LENGTH);
	}
	memset((void *)SECURITY_SHA_HMAC_KEY_BASE, 0x00, ulPasswordLength);
}
#endif /* (TCG_EN && (PS5013_EN || PS5021_EN || (PS5017_EN && !BURNER_MODE_EN)) && !RDT_MODE_EN && !LPM3_LOADER && !BOOTLOADER_MODE_EN) */
