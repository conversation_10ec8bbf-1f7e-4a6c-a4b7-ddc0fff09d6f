#ifndef _VRLC_API_H_
#define _VRLC_API_H_

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "aom/aom_api.h"
#include "setup.h"
#include "typedef.h"
#include "hal/fip/fip_api.h"
#include "ftl/ftl_api.h"
/*
 * ---------------------------------------------------------------------------------------------------
 *   definitions
 * ---------------------------------------------------------------------------------------------------
 */

#define VRLC_FW_VARIABLE_RESERVE                        (1)
#if IM_B47R
#define VRLC_TRIM_LOOP_CNT_MAX			                (20)
#define VRLC_TOTAL_TRIM_CNT_PER_CE		                (56)
#define VRLC_NEED_SCAN_MAX_LV_SIZE				        (1)
#define VRLC_NEED_SCAN_MAX_LV_SHIFT				        (0)
#define VRLC_AGC_OFFSET_SELECT_RESERVE                  (17)
#define VRLC_TRIM_CENTER_VALUE_RESERVE                  (294)
#elif IM_B37R
#define VRLC_TRIM_LOOP_CNT_MAX			                (20)
#define VRLC_TOTAL_TRIM_CNT_PER_CE		                (56)
#define VRLC_NEED_SCAN_MAX_LV_SIZE				        (1)
#define VRLC_NEED_SCAN_MAX_LV_SHIFT				        (0)
#define VRLC_AGC_OFFSET_SELECT_RESERVE                  (17)
#define VRLC_TRIM_CENTER_VALUE_RESERVE                  (294)
#elif IM_N48R /* IM_B47R */
#define VRLC_TRIM_LOOP_CNT_MAX			                (30)
#define VRLC_NEED_SCAN_MAX_LV_SIZE				        (4)
#define VRLC_NEED_SCAN_MAX_LV_SHIFT				        (2)
#define VRLC_TOTAL_TRIM_CNT_PER_CE		                (120)
#define VRLC_AGC_OFFSET_SELECT_RESERVE                  (7)
#define VRLC_TRIM_CENTER_VALUE_RESERVE                  (182)
#else /* IM_B47R */ // below define for build code only
#define VRLC_TRIM_LOOP_CNT_MAX			                (30)
#define VRLC_TOTAL_TRIM_CNT_PER_CE		                (120)
#define VRLC_NEED_SCAN_MAX_LV_SIZE				        (1)
#define VRLC_NEED_SCAN_MAX_LV_SHIFT				        (0)
#define VRLC_AGC_OFFSET_SELECT_RESERVE                  (24)
#define VRLC_TRIM_CENTER_VALUE_RESERVE                  (230)
#endif /* IM_B47R */
// sizeof(TempCo) => 1286
// (8K - sizeof(TempCo) / MAX_CE) - ((8K - sizeof(TempCo) / MAX_CE) % 32) => to align 4 bytes and 32 bytes
#define VRLC_NEED_SCAN_PAGE_Y_COORD_SIZE                (8)		//WLG Num
#define VRLC_NEED_SCAN_PAGE_Y_COORD_SHIFT	            (3)
#define VRLC_META_SIZE                                  (416)
#define VRLC_VERSION_LENGTH                             (4)
#define VRLC_VERSION_OFFSET                             ((VRLC_TABLE_SIZE_4K_NUM * SIZE_4KB) - VRLC_VERSION_LENGTH)
#define VRLC_REVISION0                                  'F' // FW/RDT => F/S => Represent VRLC Table MMO Creater
#define VRLC_REVISION1                                  'N' // NICKS
#define VRLC_REVISION2                                  '0' // Ver.HighByte
#define VRLC_REVISION3                                  '1' // Ver.LowByte

#define VRLC_DEFAULT_CE                                 (0xFF)
#define VRLC_MAX_ITERATION	                            (3)
#define VRLC_MAX_REDO_ITERATION							(0)
#define VRLC_TOTAL_ITERATION							(VRLC_MAX_ITERATION + VRLC_MAX_REDO_ITERATION) // Original 3 + 17 = 20
#define VRLC_TABLE_BURNER_PBOFFSET                      (PREFORMAT_VRLC_TABLE_BUF_ADDR)
#define VRLC_READ_VERIFY_PRIVATE_INFO_SHIFT	            (8)
#define VRLC_READ_VERIFY_MODE_INFO_MASK                 (BIT_MASK(1))

#define VRLC_TEMPCO_EN_BY_VRLC_FLOW_BUT_VRLC_NOT_ACTIVE	(TEMPCO_EN && TRUE)
#if VRLC_TEMPCO_EN_BY_VRLC_FLOW_BUT_VRLC_NOT_ACTIVE
#define VRLC_STARTUP_PE_CYCLE_THRESHOLD             	(500)	//TBD
#else	/*VRLC_TEMPCO_EN_BY_VRLC_FLOW_BUT_VRLC_NOT_ACTIVE*/
#define VRLC_STARTUP_PE_CYCLE_THRESHOLD             	(0xFFFF)	//TBD
#endif	/*VRLC_TEMPCO_EN_BY_VRLC_FLOW_BUT_VRLC_NOT_ACTIVE*/
#define VRLC_STARTUP_PER_PE_CYCLE_THRESHOLD             (100)
#define VRLC_STARTUP_TIMER_DEFAULT                      (0xFFFFFFFFFFFFFFFF)
#define VRLC_MAX_SET_FEATRUE_RETRY_CNT					(10)
#define TEMPCO_TRIM_NUM									(16)
#define TEMPCO_EC_LEVEL_NUM								(5)
#define TEMPCO_EC_LEVEL1								(500)
#define TEMPCO_EC_LEVEL2								(1000)
#define TEMPCO_EC_LEVEL3								(2000)
#define TEMPCO_EC_LEVEL4								(3000)
#define TEMPCO_EC_LEVEL0_TABLE                          (0)
#define TEMPCO_EC_LEVEL1_TABLE                          (1)
#define TEMPCO_EC_LEVEL2_TABLE                          (2)
#define TEMPCO_EC_LEVEL3_TABLE                          (3)
#define TEMPCO_EC_LEVEL4_TABLE                          (4)
#define TEMPCO_META_LASTMARK                            (0xAA)

#define MMO_WRITE_READ_DATA_BUF                         (1)
#define MMO_RDT_LOG_BUF                                 (2)

#define E3D_CALCULATE                                   (BIT0)
#define E3D_COMPARE                                     (BIT1)

#define VRLC_RDT_STRUCTURE_SIZE_RESERVE                 (12)
#define VRLC_RDT_QLC_AGC_RESERVE_ALIGN_16B              (16 - QLC_WL_TRIM_REG_NUM)
#define VRLC_RDT_TLC_AGC_RESERVE_ALIGN_16B              (16 - TLC_WL_TRIM_REG_NUM)
#define VRLC_RDT_OFFSET_OF_TRIM_CENTER_VALUE            (16 * 5)

#define VRLC_AFTER_PROGRAM_WAIT_DELAY_TIME				(2*60*1000*1000) //us
#define VRLC_DUMMY_READ_WAIT_DELAY_TIME					(5)//ms

#define VRLC_CODEWORD_PER_PAGE_SHIFT					(3)
#define VRLC_UECC_LIMIT_BIT_NUM							(150)
#define VRLC_AGC_FILTER_LDPC_MODE0_UNC_MAX_BIT	        (VRLC_UECC_LIMIT_BIT_NUM << VRLC_CODEWORD_PER_PAGE_SHIFT)
#define VRLC_AGC_FILTER_LDPC_MODE0_CORRECT_MAX_BIT_NUM	((VRLC_UECC_LIMIT_BIT_NUM - 1) << VRLC_CODEWORD_PER_PAGE_SHIFT)

#define VRLC_DUMMY_FSA									(0xBEEFBEEF)

typedef enum VRLCReadStepEnum {
	VRLC_LEFT = 0,
	VRLC_RIGHT,
	VRLC_CENTER,
	VRLC_NUM
} VRLCReadStepEnum_t;

typedef enum VRLCModeEnum {
	VRLC_REFCAL_MODE = 0,
	VRLC_MEASURE_MODE,
	VRLC_MODE_NUM
} VRLCModeEnum_t;

typedef enum VRLCTableInitEventModeEnum {
#if ((BURNER_MODE_EN && (!PS5017_EN)) || (PS5017_EN))
	VRLC_EVENT_BURNER_INIT,
	VRLC_EVENT_BURNER_PREFORMAT,
#endif /* ((BURNER_MODE_EN && (!PS5017_EN)) || (PS5017_EN)) */
	VRLC_EVENT_DLMC_PREFORMAT,
	VRLC_EVENT_FW_INIT_LOAD,
} VRLCTableInitEventModeEnum_t;

typedef enum VRLCTableInfoInitModeEnum {
	VRLC_BURNER_PREFORMAT,
	VRLC_FW_LOAD,
} VRLCTableInfoInitModeEnum_t;

typedef enum VRLCDBUFStateEnum {
	VRLC_DBUF_NO_STATE = 0,
	VRLC_DBUF_HAVE_STATE
} VRLCDBUFStateEnum_t;

typedef enum {
	VRLC_SAVE_REASON_NORMAL = 1,
	VRLC_SAVE_REASON_INIT,
	VRLC_SAVE_REASON_STANDBY,
} VRLCTableSaveReason;

typedef enum VRLCMeasureStateEnum {
	VRLC_MEASURE_STATE_IDLE = 0,
	VRLC_MEASURE_STATE_WAIT_RSO_DONE,
	VRLC_MEASURE_STATE_WAIT_FIRST_READ_DONE,
	VRLC_MEASURE_STATE_MEASURE_DONE,
	//----Break Loop if above State----//
	VRLC_MEASURE_BREAKLOOP_STATE,
	//-----------------------------//
	VRLC_MEASURE_STATE_INIT,
	VRLC_MEASURE_STATE_FIRST_READ,
	VRLC_MEASURE_STATE_DO_ONE_RSO,

} VRLCMeasureStateEnum_t;

typedef enum VRLCStateEnum {
	VRLC_STATE_IDLE = 0,
	VRLC_STATE_START,
	VRLC_STATE_SELECT_READ_LEVEL,
	VRLC_STATE_PRECONDITION,
	VRLC_STATE_CHECK_PRECONDITION,
	VRLC_STATE_READ,
	VRLC_STATE_POSTCONDITION,
	VRLC_STATE_CHECK_POSTCONDITION,
	TEMPCO_STATE_PRECONDITION,
	TEMPCO_STATE_CHECK_PRECONDITION,
	VRLC_STATE_DONE,

	VRLC_STATE_WAIT_PRECONDITION,
	VRLC_STATE_WAIT_CHECK_PRECONDITION,
	VRLC_STATE_WAIT_READ,
	VRLC_STATE_WAIT_POSTCONDITION,
	VRLC_STATE_WAIT_CHECK_POSTCONDITION,
	TEMPCO_STATE_WAIT_PRECONDITION,
	TEMPCO_STATE_WAIT_CHECK_PRECONDITION
} VRLCStateEnum_t;
/*
 * ---------------------------------------------------------------------------------------------------
 *  Macro
 * ---------------------------------------------------------------------------------------------------
 */
#if(IM_B47R)
#define M_VRLC_GET_TRIMREG_FROM_WLG(YCoordIdx, ubWordLineLTrimIdx) 	(0x0038 - (0x0008 * (YCoordIdx)) + (ubWordLineLTrimIdx))
#define M_VRLC_GET_WORDLINE_L_TRIM_IDX_FROM_TRIMREG(uwTrimReg) 		((uwTrimReg) & BIT_MASK(3))
#define M_VRLC_GET_TRIMREG_IDX_FROM_TRIMREG(uwTrimReg) 				(M_VRLC_GET_TRIM_NUM_FROM_TRIMREG(uwTrimReg)-1)//((7 - ((uwTrimReg) >> 3)) * 7 + ((uwTrimReg) & BIT_MASK(3)))
#define M_VRLC_GET_TRIM_NUM_FROM_TRIMREG(uwTrimReg)					((7 * ((uwTrimReg) >> 3)) + (((uwTrimReg) & BIT0) ? (3 + (((uwTrimReg) >> 1)& BIT_MASK(2))) : ((((uwTrimReg) & BIT1) ? 6 : 1) + (((uwTrimReg) & BIT2) ? 1 : 0))))
#define M_VRLC_GET_WLG_FROM_Y(ubY)									(((ubY)>3)+((ubY)>44)+((ubY)>84)+((ubY)>88)+((ubY)>95)+((ubY)>133)+((ubY)>171))
#define M_VRLC_GET_L7_TRIMREG_FROM_Y(ubY)							(M_VRLC_GET_TRIMREG_FROM_WLG(M_VRLC_GET_WLG_FROM_Y(ubY),6))
#elif(IM_B37R)
#define M_VRLC_GET_TRIMREG_FROM_WLG(YCoordIdx, ubWordLineLTrimIdx) 	(0x0038 - (0x0008 * (YCoordIdx)) + (ubWordLineLTrimIdx))
#define M_VRLC_GET_WORDLINE_L_TRIM_IDX_FROM_TRIMREG(uwTrimReg) 		((uwTrimReg) & BIT_MASK(3))
#define M_VRLC_GET_TRIMREG_IDX_FROM_TRIMREG(uwTrimReg) 				(M_VRLC_GET_TRIM_NUM_FROM_TRIMREG(uwTrimReg)-1)//((7 - ((uwTrimReg) >> 3)) * 7 + ((uwTrimReg) & BIT_MASK(3)))
#define M_VRLC_GET_TRIM_NUM_FROM_TRIMREG(uwTrimReg)					((7 * ((uwTrimReg) >> 3)) + (((uwTrimReg) & BIT0) ? (3 + (((uwTrimReg) >> 1)& BIT_MASK(2))) : ((((uwTrimReg) & BIT1) ? 6 : 1) + (((uwTrimReg) & BIT2) ? 1 : 0))))
#define M_VRLC_GET_WLG_FROM_Y(ubY)									(((ubY)>3)+((ubY)>44)+((ubY)>84)+((ubY)>88)+((ubY)>95)+((ubY)>133)+((ubY)>171))
#define M_VRLC_GET_L7_TRIMREG_FROM_Y(ubY)							(M_VRLC_GET_TRIMREG_FROM_WLG(M_VRLC_GET_WLG_FROM_Y(ubY),6))
#elif(IM_N48R)/* (IM_B47R) */
#define M_VRLC_GET_TRIMREG_FROM_WLG(YCoordIdx, ubWordLineLTrimIdx) 	(0x0040 + (0x0010 * (YCoordIdx)) + (ubWordLineLTrimIdx))
#define M_VRLC_GET_WORDLINE_L_TRIM_IDX_FROM_TRIMREG(uwTrimReg) 		((uwTrimReg) & BIT_MASK(4))
#define M_VRLC_GET_TRIMREG_IDX_FROM_TRIMREG(uwTrimReg) 				(M_VRLC_GET_TRIM_NUM_FROM_TRIMREG(uwTrimReg)-1)//((7 - ((uwTrimReg) >> 3)) * 7 + ((uwTrimReg) & BIT_MASK(3)))
#define M_VRLC_GET_TRIM_NUM_FROM_TRIMREG(uwTrimReg)					(gubMicronVRLCTrimRegIdxToNum[(uwTrimReg) & BIT_MASK(4)] + (((uwTrimReg) >> 4) - 4) * 15)
#define M_VRLC_GET_WLG_FROM_Y(ubY)									(((ubY)>3)+((ubY)>24)+((ubY)>62)+((ubY)>85)+((ubY)>91)+((ubY)>102)+((ubY)>138))
#define M_VRLC_GET_L7_TRIMREG_FROM_Y(ubY)							(M_VRLC_GET_TRIMREG_FROM_WLG(M_VRLC_GET_WLG_FROM_Y(ubY),6))
#endif/* (IM_B47R) */

/*
 * ---------------------------------------------------------------------------------------------------
 *  data types
 * ---------------------------------------------------------------------------------------------------
 */
typedef struct VRLCTask_t                                   VRLC_TASK_STRUCT, *VRLC_TASK_STRUCT_PTR;
typedef struct VRLCParameter_t                              VRLC_PARAMETER_STRUCT, *VRLC_PARAMETER_STRUCT_PTR;
typedef struct TempcoInfo_t                              	TEMPCOINFO_STRUCT, *TEMPCOINFO_STRUCT_PTR;
typedef struct TempcoMeta_t          						TEMPCO_META, *TEMPCO_META_PTR;
typedef struct VRLCMeasure_t								VRLC_MEASURE_STRUCT, *VRLC_MEASURE_STRUCT_PTR;

struct TempcoMeta_t {
	U16 uwTrimAddr[TEMPCO_TRIM_NUM]; // 32 Bytes
	U8 ubTrimValue[TEMPCO_EC_LEVEL_NUM][TEMPCO_TRIM_NUM]; // 80 Bytes
	// total 1285Byte and cause it will align most large data type in this struct so will ADD 1 byte = 1286Byte
	U8  ubTempcoECLevel; // 1 Byte
	U8  ubReserve; // 1 Byte
};

struct TempcoInfo_t {
	U32	ulTempcoDoneCEBMP;
	U16 uwTempcoTableIdx;
	U16 uwTempcoTrimReg;
	U16 uwTempcoTrimValue;
	U8 ubTempcoECLevel;
	U8 ubTempcoDone;
};

typedef struct VRLCMeasure_t {
	U64 uoFirstReadStartTime;
	U16 uwTagID;
	U8 ubMeasureState;
	U8 ubIterationCnt;
	U8 ubTrimLoopCnt;
	U8 ubDoingCE;
} VRLCMeasure_t;

typedef struct VRLCMagnitude_t {
	U8	ubValue : 7;
	U8	ubDirection : 1;
} VRLCMagnitude_t;

typedef struct VRLCSystemFlag_t {
	U8	btTrimCenterValueUpdate : 1;
	U8	btTrimCenterDithering : 1;
	U8	btTrimLoopDoing	: 1;
	U8	btTrimL1	: 1;
	U8	btAGCSystemUpdateEn	: 1;
	U8	btAGCSystemUpdate : 1;
	U8	btReserved : 2;
} VRLCSystemFlag_t;

struct VRLCTask_t {
	FlhMT_t MTTemplate; // Set Retry PCA & Frame number & Retry read setting in this template
	//	U32 ulSprOffset[FRAMES_PER_PAGE];
	VRLC_PARAMETER_STRUCT_PTR pVRLCParameter;
	U16 uwTrimReg;
	U8	ubState;
	U8	ubReadLevel;
	U8	ubDie; // VRLC Rand Die
	U8	ubGlobalCE;
	U8	ubSetFeatureRetryCnt;
	U8	ubStepInIteration; // Left Right Center
	U8	ubMLBiEnable;
	U8  ubVRLCMode;
};

struct VRLCParameter_t {
	U8  ubMaxMove;
	union {
		U8  ubAll;
		VRLCMagnitude_t ubVRLCMagnitude;
	} VRLCPresentMagnitude;
	union {
		U8  ubAll;
		VRLCMagnitude_t ubVRLCMagnitude;
	} VRLCLastMagnitude;
	union {
		U8  ubAll;
		VRLCSystemFlag_t ubVRLCSystemFlag;
	} VRLCSystemFlag;

	U8  ubTrimCenterValue;
	U8  ubIterationCnt;
	U8	ubRedoInterationCnt;
	U8  ubTwoSideUECCRedoTrimLoopCnt;
	U16 uwTrimReg;
	U8	ubBestTrimCenterValue;
	U8  ubAGCParameterK;
	U8  ubAGCOffsetSelect;
	U8  ubTrimLoopCnt;
	U32 ulDiffEC;
	U32 ulFilterOut; // Q24.8
	U16 uwErrorBitsPerTrimLoop[VRLC_MAX_ITERATION][VRLC_NUM];
	U16 uwBestTrimCenterEC;
	U8	ubOriginalTrimCenterValue;
	U8	ubWordLineType;
	U32 ulNandBlk[VRLC_TOTAL_ITERATION];

};

typedef struct {
	U16 uwTotalInterationCnt[VRLC_NEED_SCAN_PAGE_Y_COORD_SIZE << VRLC_NEED_SCAN_MAX_LV_SHIFT];

	U8 ubDoingYCoordIdx;  // 0 ~ 27 get Coor-Y from array
	U8 ubDoingWordLineType; // QLC_WL, TLC_WL, SLC_WL
	U8 ubDoingInteration; // 0 ~ 2, total 3 times
	U8 ubDoingLoopCnt; // 0 ~ 14, MAX 15 times, if reach MAX need SET FAILED.

	U16 uwDoingTrimReg;
	U16 uwTrimCenterDoneCnt; // MAX:56, 0 ~ 55

	U32 ulPreviousActivateAverageEC;

	U8 ubDoingWordLineLTrimIdx; // QLC:0~14, TLC:0~6, SLC:0 => Reach MAX then ++ubDoingYCoordIdx
	U8 ubMeasureDoingLoopCnt;
	U16 uwMeasureTrimReg;

	U8 ubMeasureDoingInteration;
	S8 sbMeasureMeasOffset;
	U8 ubMeasureCenterEC;
	U8 ubMeasureDiffEC;

	U8 ubTrimDoneYCoordBMP[(VRLC_NEED_SCAN_PAGE_Y_COORD_SIZE << VRLC_NEED_SCAN_MAX_LV_SHIFT) >> 3];

	U8 ubDoingPageTypeIdx;
	U8 ubDoingLVIdx;
#if (IM_B47R || IM_B37R)
	U8 ubReserveFWVariable[VRLC_FW_VARIABLE_RESERVE];
	U8 ubTLCWLAGCOffsetSelect[TLC_WL_TRIM_REG_NUM];  // 0 ~ 6 , 0 is HalfBTShift so don't care, 1~6 map with ubDoingWordLineLTrimIdx
#elif (IM_N48R)/* (IM_B47R) */
	U8 ubQLCWLAGCOffsetSelect[QLC_WL_TRIM_REG_NUM];  // 0 ~ 14 , 0 is HalfBTShift so don't care, 1~14 map with ubDoingWordLineLTrimIdx
#endif /* (IM_B47R) */
	U8 ubReserveAGCOffsetSelect[VRLC_AGC_OFFSET_SELECT_RESERVE]; // Reserve for MLC SLC WLG
	// Alert: be aware to align 4 byte to avoid overflow ubTrimCenterValue
	// till here use 60 bytes

	U8 ubTrimCenterValue[VRLC_TOTAL_TRIM_CNT_PER_CE]; //  map with ubDoingWordLineLTrimIdx(L1 ~ L7), ubDoingYCoordIdx(0~7)
	// till here use 118 bytes

	U8 ubReserveTrimCenterValue[VRLC_TRIM_CENTER_VALUE_RESERVE]; //
	// till here use 416 bytes, align 4 byte and align 32 byte

} VRLC_META;
TYPE_SIZE_CHECK(VRLC_META, VRLC_META_SIZE);

typedef struct {
	U8 ubVersion[VRLC_VERSION_LENGTH];
} VRLC_VERSION;
TYPE_SIZE_CHECK(VRLC_VERSION, SIZE_4B);

//RDT VRLC Meta Type
typedef enum RDT_VRLC_Meta_Type {
	QLC_WL_AGC_OFFSET_SELECTIVE = 0,
	TLC_WL_AGC_OFFSET_SELECTIVE,
	TRIM_CENTER_DONE_CNT,
	TRIM_CENTER_VALUE,
	VRLC_VERSIONS
} RDT_VRLC_Meta_Type_t;

typedef struct {
	U32 ulQLCAGCOffsetSelectSize;
	U8 ubReserve1[VRLC_RDT_STRUCTURE_SIZE_RESERVE];   // For 16 Byte-align (0xFF)
	// till here use 16 bytes

	U8 ubQLCWLAGCOffsetSelect[QLC_WL_TRIM_REG_NUM]; // 15 Bytes
	U8 ubReserveQLCAGCOffsetSelect[VRLC_RDT_QLC_AGC_RESERVE_ALIGN_16B]; // Byte for 16 Byte-align (0xFF)
	// till here use 32 bytes

	U32 ulTLCAGCOffsetSelectSize;
	U8 ubReserve2[VRLC_RDT_STRUCTURE_SIZE_RESERVE];   // For 16 Byte-align (0xFF)
	// till here use 48 bytes

	U8 ubTLCWLAGCOffsetSelect[TLC_WL_TRIM_REG_NUM]; // 7 Bytes
	U8 ubReserveTLCAGCOffsetSelect[VRLC_RDT_TLC_AGC_RESERVE_ALIGN_16B]; // Byte for 16 Byte-align (0xFF)
	// till here use 64 bytes

	U32 ulTrimCenterValueSize;
	U8 ubReserve3[VRLC_RDT_STRUCTURE_SIZE_RESERVE];   // For 16 Byte-align  (0xFF)
	// till here use 80 bytes

	U8 ubTrimCenterValue[VRLC_TOTAL_TRIM_CNT_PER_CE]; // 344 Bytes
	U8 ubReserve4[SECTOR_SIZE - VRLC_TOTAL_TRIM_CNT_PER_CE - VRLC_RDT_OFFSET_OF_TRIM_CENTER_VALUE - VRLC_VERSION_LENGTH];	   // For 16 Byte-align (0xFF)

	U8 ubVersion[VRLC_VERSION_LENGTH];
} RDT_MICRON_VRLC_LOG;
TYPE_SIZE_CHECK(RDT_MICRON_VRLC_LOG, SECTOR_SIZE);

typedef struct {
	U8 ubDoingCE; // 8CE:0~7, 16CE:0~15
	union {
		U8 ubAll;
		struct {
			U8 btVRLCTableSaveNeedPolling       : 1; // Handle Save Initinfo
			U8 btVRLCTableNeedSaved             : 1; // Need Save Initinfo
			U8 btVRLCTableIterationNeedRedo     : 1; // Error Occur iteration need redo
			U8 btVRLCTableTrimCenterDithering   : 1; // determine Dithering
			U8 btVRLCTableTwoSideUECC           : 1; // leftAvgEC and rightAvgEC both UECC, need stop FW_MMO
			U8 btVRLCTableLoadError             : 1; // Load VRLC Table Error
#if VRLC_IN_MMO
			U8 btVRLCTableUpdateAGCSystem       : 1; // need update AGCOffsetSel and AGCFiltDiffEC
			U8 btReserved : 1;
#else /* VRLC_IN_MMO */
			U8 btReserved : 2;
#endif /* VRLC_IN_MMO */
		};
	} VRLCStatusFlag;
	U8  ubDiffECErr;            // coding style follow Nick2W2-5's depiction
	U8  ubPECThreshold;
	U16 uwStartPECThreshold;
	U32 ulTrimCenterAllDoneBMP;
	U32 ulPreviousAverageEC;
	U64 uoVRLCCheckStartUpTimer;
} VRLC_INFO;

/*
 * ---------------------------------------------------------------------------------------------------
 *   extern global variables
 * ---------------------------------------------------------------------------------------------------
 */
extern const VRLC_VERSION gVRLCVersion;
extern VRLC_INFO gVRLCInfoManager;
extern VRLC_META *gpVRLCMeta;
extern VRLC_VERSION *gpVRLCVersion;
extern VRLC_TASK_STRUCT gVRLCTask;
#if (VRLC_IN_CMO)
extern VRLC_PARAMETER_STRUCT gVRLCParameter[VRLC_NEED_SCAN_PAGE_Y_COORD_SIZE << VRLC_NEED_SCAN_MAX_LV_SHIFT];
#endif /* (VRLC_IN_CMO) */
extern VRLC_PARAMETER_STRUCT gVRLCMeasureParameter;
extern VRLC_MEASURE_STRUCT gVRLCMeasure;
#if VRLC_IN_MMO
#if(IM_B47R || IM_B37R)
extern U32 gulTLCWLAGCFiltDiffEC[MAX_CE][TLC_WL_TRIM_REG_NUM];
#elif(IM_N48R)/* (IM_B47R) */
extern U32 gulQLCWLAGCFiltDiffEC[MAX_CE][QLC_WL_TRIM_REG_NUM];
#endif/* (IM_B47R) */
#endif /* VRLC_IN_MMO */
extern const U8 gubMicronVRLCTrimRegIdxToNum[QLC_WL_TRIM_REG_NUM];
extern TEMPCO_META gTempcoMeta;
extern TEMPCO_META_PTR gpTempcoMeta;
extern TEMPCOINFO_STRUCT gTempcoInfo;
extern volatile U32 gulTempcoCE;
/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */
AOM_VRLC_INIT_2 void MediaScanVRLCUpdateInfo(void);
AOM_MEDIA_SCAN_VRLC U32 MediaScanVRLCGetStartPlaneIdx(void);
AOM_MEDIA_SCAN_VRLC U8 MediaScanVRLCCheckNeedStartUp(void);
AOM_VRLC_INIT_2 U8 VRLCSaveVRLCTable(U8 ubVRLCDBUFState, U8 SaveReason);
#if (RELEASED_FW)
AOM_INITINFO U8 VRLCLoadVRLCTable(U8 ubPolling);
#else /* RELEASED_FW */
AOM_INITINFO U8 VRLCLoadVRLCTable(U8 ubPolling);
#endif /* RELEASED_FW */
AOM_INIT void VRLCTableInitEventHandle(VRLCTableInitEventModeEnum_t Event);
#if (RELEASED_FW)
AOM_INITINFO void VRLCInitVRLCTableInfo(VRLCTableInfoInitModeEnum_t ubMode);
#else /* RELEASED_FW */
AOM_INITINFO void VRLCInitVRLCTableInfo(VRLCTableInfoInitModeEnum_t ubMode);
#endif /* RELEASED_FW */
AOM_RETRY void VRLCMain(void);
AOM_VRLC_INIT_2 void VRLCCreateVRLCTask(RetryJob_t *pCurrentRetryJob);
AOM_VRLC_INIT void VRLCInit(void);
AOM_INITINFO void VRLCReleaseVRLCTablePB(void);
#if VRLC_MEASURE_EN
AOM_BFEA void VRLCMeasure(U16 uwUnit, U16 uwDie, U16 uwPage, U16 uwDoingTrimReg, U8 ubNeedFirstRead, U8 ubSingleRSOOnly);
void VRLCMeasure_Callback(TIEOUT_FORMAT_t uoResult);
AOM_BFEA void VRLCMeasureForceStop(void);
AOM_BFEA void VRLCFirstReadEvokeBlock(U16 *uwUnitList, U16 uwUnitNum, U64 *puoFirstReadStartTime);
#endif /* VRLC_MEASURE_EN */

#if (BURNER_MODE_EN)
AOM_BURNER void InitVRLCBuf(U8 ubRAMIdx);
AOM_BURNER void VRLCCalculateTempcoECLevel(U8 ubLoadECFail);
AOM_BURNER void VRLCScanRDTLog(void);
AOM_BURNER U8 VRLCBurnerLoadVRLCTable(void);
AOM_BURNER U8 VRLCE3D(U8 ubFunction, U32 ulBufAddr);
#endif /* (BURNER_MODE_EN) */
#endif /* _VRLC_API_H_ */
