#ifndef _VUC_CUTEFUSE_H_
#define _VUC_CUTEFUSE_H_
#include "aom/aom_api.h"

/* vendor_cut_efuse */
#define VUC_CUT_EFUSE_BANK_MASK    		(0x0000FF)
#define VUC_CUT_EFUSE_ADR_OFFSET_MASK   (0x00FF00)
#define VUC_CUT_EFUSE_ADR_OFFSET_SHIFT  (8)
#define VUC_CUT_EFUSE_BIT_OFFSET_MASK   (0xFF0000)
#define VUC_CUT_EFUSE_BIT_OFFSET_SHIFT	(16)
#define VUC_CUT_EFUSE_BY_SINGLE			(0x00)
#define VUC_CUT_EFUSE_BY_TABLE			(0x01)
#if (PS5017_EN)
#define EFUSE_DW_PER_BANK				(8)
#define EFUSE_BYTE_PER_DW				(4)
#define EFUSE_BIT_PER_DW				(32)
#endif /* (PS5017_EN) */
#define EFUSE_EACH_BANK_WIDE			(8)

AOM_VUC void VUC_CutEFuse(VUC_OPT_HCMD_PTR_t pCmd);

#endif /* _VUC_CUTEFUSE_H_ */
