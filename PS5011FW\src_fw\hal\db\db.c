#include "setup.h"
#include "typedef.h"
#include "hal/db/db_api.h"
#include "hal/mr/mr_api.h"
#include "hal/sys/api/misc/misc_api.h"
#include "hal/mr/mr_api.h"

DBQueueCnt_t gDBQueueCnt;
U64 guoAPUCmdLog;

//---for initial
static void DBQInfoReset(U8 ubDBID)
{
	//Need modify
	// set VQ = 1
	R8_DB[ubDBID][R8_DB_CTRL] = (1 << VALID_QUEUE_SHIFT); //BIT0

	// reset queue (RQ)
	R8_DB[ubDBID][R8_DB_CTRL] = (1 << RESET_QUEUE_SHIFT); //BIT1

	// set VQ = 1 & RQ = 0 (HW will clear RQ bit)
	R8_DB[ubDBID][R8_DB_CTRL] = (1 << VALID_QUEUE_SHIFT); //BIT0

#if (!(PS5017_EN || PS5021_EN)) // E17 & E21, read only 
	// set EMPTY
	R8_DB[ubDBID][R8_DB_CTRL] |= (1 << QUEUE_EMPTY_SHIFT); //BIT3
#endif  /* (!(PS5017_EN || PS5021_EN)) */

	// wait reset queue complete
	while (R8_DB[ubDBID][R8_DB_CTRL] & (1 << RESET_QUEUE_SHIFT)); //check BIT1
}

NO_INLINE void DBQInfoInit(U8 ubDBID, U32 ulQBase, U8 ubDataSize, U16 uwBufLength)
{
	if (uwBufLength == 0) {
		return;
	}

	//M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, ((ubDBID < DB_QUEUE_CNT));
	//M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!(ulQBase & 0x1F));   // QBase must be 32B align

#if !VS_SIM_EN
	R8_DB[ubDBID][R8_DB_CTRL] = 0;
#endif /* !VS_SIM_EN */

	// set queue base address + WPTR +RPTR
	R32_DB[ubDBID][R32_DB_QBASE_ADR] = ulQBase;
#if (!(PS5017_EN || PS5021_EN)) // E17 & E21, read only
	R32_DB[ubDBID][R32_DB_WPTR] = ulQBase;
	R32_DB[ubDBID][R32_DB_RPTR] = ulQBase;
#endif /* (!(PS5017_EN || PS5021_EN)) */

	// set buffer length
	R16_DB[ubDBID][R16_DB_BUF_LEN] = uwBufLength;

	// set data size
	R8_DB[ubDBID][R8_DB_DATA_SIZE] = ubDataSize;

#if (!(PS5017_EN || PS5021_EN)) // E17 & E21, read only
	//initial=    tail + WR_CNT + RD_CNT
	// set queue tail address
	R32_DB[ubDBID][R32_DB_QTAIL_ADR] = ulQBase + (uwBufLength * (1 << ubDataSize));
	R16_DB[ubDBID][R16_DB_WR_CNT] = uwBufLength;
	R16_DB[ubDBID][R16_DB_RD_CNT] = 0;
#endif /* (!(PS5017_EN || PS5021_EN)) */

	// reset queue and set VQ = 1
#if !VS_SIM_EN
	DBQInfoReset(ubDBID);
#endif /* !VS_SIM_EN */
}

void DBInit(void)
{
	U8 ubQueueIdx;

	for (ubQueueIdx = 0; ubQueueIdx < DB_QUEUE_CNT; ubQueueIdx++) {
		gDBQueueCnt.QueueCnt[ubQueueIdx] = 0;
	}

	// DMAC High SQ
	DBQInfoInit(DB_DMAC_HIGH_SQ, (U32)DB_DMAC_HIGH_SQ_BASE, DB_DMAC_HIGH_SQ_DATA_SIZE_LOG, DB_DMAC_HIGH_SQ_DEPTH);

	// DMAC High CQ
	DBQInfoInit(DB_DMAC_HIGH_CQ, (U32)DB_DMAC_HIGH_CQ_BASE, DB_DMAC_HIGH_CQ_DATA_SIZE_LOG, DB_DMAC_HIGH_CQ_DEPTH);

	// DMAC Normal SQ
	DBQInfoInit(DB_DMAC_NORM_SQ, (U32)DB_DMAC_NORM_SQ_BASE, DB_DMAC_NORM_SQ_DATA_SIZE_LOG, DB_DMAC_NORM_SQ_DEPTH);

	// DMAC Normal CQ
	DBQInfoInit(DB_DMAC_NORM_CQ, (U32)DB_DMAC_NORM_CQ_BASE, DB_DMAC_NORM_CQ_DATA_SIZE_LOG, DB_DMAC_NORM_CQ_DEPTH);

	// BMU NON-BLOCKING SQ
	DBQInfoInit(DB_BMU_NOBLK_SQ, (U32)DB_BMU_NOBLK_SQ_BASE, DB_BMU_NOBLK_SQ_DATA_SIZE_LOG, DB_BMU_NOBLK_SQ_DEPTH);

	// BMU BLOCKING SQ
	DBQInfoInit(DB_BMU_BLK_SQ, (U32)DB_BMU_BLK_SQ_BASE, DB_BMU_BLK_SQ_DATA_SIZE_LOG, DB_BMU_BLK_SQ_DEPTH);

	// BMU CQ
	DBQInfoInit(DB_BMU_CQ, (U32)DB_BMU_CQ_BASE, DB_BMU_CQ_DATA_SIZE_LOG, DB_BMU_CQ_DEPTH);

	// BMU WRITE CQ
	DBQInfoInit(DB_BMU_WR_CQ, (U32)DB_BMU_WR_CQ_BASE, DB_BMU_WR_CQ_DATA_SIZE_LOG, DB_BMU_WR_CQ_DEPTH);

	// BMU WRITE CQ 2
	DBQInfoInit(DB_BMU_WR_CQ2, (U32)DB_BMU_WR_CQ_2_BASE, DB_BMU_WR_CQ_2_DATA_SIZE_LOG, DB_BMU_WR_CQ_2_DEPTH);

	// BMU Search SQ
	DBQInfoInit(DB_BMU_SEARCH_SQ, (U32)DB_BMU_SEARCH_SQ_BASE, DB_BMU_SEARCH_SQ_DATA_SIZE_LOG, DB_BMU_SEARCH_SQ_DEPTH);

	// APU Write Channel SQ
	DBQInfoInit(DB_APU_WR_SQ, (U32)DB_APU_WR_SQ_BASE, DB_APU_WR_SQ_DATA_SIZE_LOG, DB_APU_WR_SQ_DEPTH);

	// APU Completion SQ
	DBQInfoInit(DB_APU_CMPL_SQ, (U32)DB_APU_CMPL_SQ_BASE, DB_APU_CMPL_SQ_DATA_SIZE_LOG, DB_APU_CMPL_SQ_DEPTH);

	// APU Transfer Done CQ
	DBQInfoInit(DB_APU_TD_CQ, (U32)DB_APU_TD_CQ_BASE, DB_APU_TD_CQ_DATA_SIZE_LOG, DB_APU_TD_CQ_DEPTH);

	// APU Command CQ
	DBQInfoInit(DB_APU_CMD_CQ, (U32)DB_APU_CMD_CQ_BASE, DB_APU_CMD_CQ_DATA_SIZE_LOG, DB_APU_CMD_CQ_DEPTH);

	// APU Read CQ
	DBQInfoInit(DB_APU_RD_CQ, (U32)DB_APU_RD_CQ_BASE, DB_APU_RD_CQ_DATA_SIZE_LOG, DB_APU_RD_CQ_DEPTH);

	// XZIP SQ
	DBQInfoInit(DB_XZIP_SQ, (U32)DB_XZIP_SQ_BASE, DB_XZIP_SQ_DATA_SIZE_LOG, DB_XZIP_SQ_DEPTH);

	// XZIP CQ
	DBQInfoInit(DB_XZIP_CQ, (U32)DB_XZIP_CQ_BASE, DB_XZIP_CQ_DATA_SIZE_LOG, DB_XZIP_CQ_DEPTH);

	// COP0 READ SQ
	DBQInfoInit(DB_COP0_RD_SQ, (U32)DB_COP0_RD_SQ_BASE, DB_COP0_RD_SQ_DATA_SIZE_LOG, DB_COP0_RD_SQ_DEPTH);

	// COP0 WRITE SQ
	DBQInfoInit(DB_COP0_WR_SQ, (U32)DB_COP0_WR_SQ_BASE, DB_COP0_WR_SQ_DATA_SIZE_LOG, DB_COP0_WR_SQ_DEPTH);

	// COP0 CQ
	DBQInfoInit(DB_COP0_CQ, (U32)DB_COP0_CQ_BASE, DB_COP0_CQ_DATA_SIZE_LOG, DB_COP0_CQ_DEPTH);

	// COP1 SQ
	DBQInfoInit(DB_COP1_SQ, (U32)DB_COP1_SQ_BASE, DB_COP1_SQ_DATA_SIZE_LOG, DB_COP1_SQ_DEPTH);

	// COP1 CQ
	DBQInfoInit(DB_COP1_CQ, (U32)DB_COP1_CQ_BASE, DB_COP1_CQ_DATA_SIZE_LOG, DB_COP1_CQ_DEPTH);

	// FLH MSG CQ
	DBQInfoInit(DB_FLH_MSG_CQ, (U32)DB_FLH_MSG_CQ_BASE, DB_FLH_MSG_CQ_DATA_SIZE_LOG, DB_FLH_MSG_CQ_DEPTH);

	// RS MSG CQ
	DBQInfoInit(DB_RS_MSG_CQ, (U32)DB_RS_MSG_CQ_BASE, DB_RS_MSG_CQ_DATA_SIZE_LOG, DB_RS_MSG_CQ_DEPTH);

	// PIC CQ
	DBQInfoInit(DB_PIC_CQ, (U32)DB_PIC_CQ_BASE, DB_PIC_CQ_DATA_SIZE_LOG, DB_PIC_CQ_DEPTH);

	// LOG MSG CQ
	DBQInfoInit(DB_LOG_MSG_CQ, (U32)DB_LOG_MSG_CQ_BASE, DB_LOG_MSG_CQ_DATA_SIZE_LOG, DB_LOG_MSG_CQ_DEPTH);

	// ERR MSG CQ
	DBQInfoInit(DB_ERR_MSG_CQ, (U32)DB_ERR_MSG_CQ_BASE, DB_ERR_MSG_CQ_DATA_SIZE_LOG, DB_ERR_MSG_CQ_DEPTH);

	// Software SQ 0
	MR_FWSQ0_DATA_SIZE(DB_FW_SQ0_DATA_SIZE_LOG);
	DBQInfoInit(DB_FW_SQ0, (U32)DB_FW_SQ0_BASE, DB_FW_SQ0_DATA_SIZE_LOG, DB_FW_SQ0_DEPTH);

	// Software CQ 0
	MR_FWCQ0_DATA_SIZE(DB_FW_CQ0_DATA_SIZE_LOG);
	DBQInfoInit(DB_FW_CQ0, (U32)DB_FW_CQ0_BASE, DB_FW_CQ0_DATA_SIZE_LOG, DB_FW_CQ0_DEPTH);

	// Software SQ 1
	MR_FWSQ1_DATA_SIZE(DB_FW_SQ1_DATA_SIZE_LOG);
	DBQInfoInit(DB_FW_SQ1, (U32)DB_FW_SQ1_BASE, DB_FW_SQ1_DATA_SIZE_LOG, DB_FW_SQ1_DEPTH);

	// Software CQ 1
	MR_FWCQ1_DATA_SIZE(DB_FW_CQ1_DATA_SIZE_LOG);
	DBQInfoInit(DB_FW_CQ1, (U32)DB_FW_CQ1_BASE, DB_FW_CQ1_DATA_SIZE_LOG, DB_FW_CQ1_DEPTH);

	// Software SQ 2
	MR_FWSQ2_DATA_SIZE(DB_FW_SQ2_DATA_SIZE_LOG);
	DBQInfoInit(DB_FW_SQ2, (U32)DB_FW_SQ2_BASE, DB_FW_SQ2_DATA_SIZE_LOG, DB_FW_SQ2_DEPTH);

	// Software CQ 2
	MR_FWCQ2_DATA_SIZE(DB_FW_CQ2_DATA_SIZE_LOG);
	DBQInfoInit(DB_FW_CQ2, (U32)DB_FW_CQ2_BASE, DB_FW_CQ2_DATA_SIZE_LOG, DB_FW_CQ2_DEPTH);

	// Software SQ 3
	MR_FWSQ3_DATA_SIZE(DB_FW_SQ3_DATA_SIZE_LOG);
	DBQInfoInit(DB_FW_SQ3, (U32)DB_FW_SQ3_BASE, DB_FW_SQ3_DATA_SIZE_LOG, DB_FW_SQ3_DEPTH);

	// Software CQ 3
	MR_FWCQ3_DATA_SIZE(DB_FW_CQ3_DATA_SIZE_LOG);
	DBQInfoInit(DB_FW_CQ3, (U32)DB_FW_CQ3_BASE, DB_FW_CQ3_DATA_SIZE_LOG, DB_FW_CQ3_DEPTH);

	// BMU NonBlocking CQ
	DBQInfoInit(DB_BMU_NOBLK_CQ, (U32)DB_BMU_NOBLK_CQ_BASE, DB_BMU_NOBLK_CQ_DATA_SIZE_LOG, DB_BMU_NOBLK_CQ_DEPTH);//non blocking CQ

	// COP0 WRITE SQ
	DBQInfoInit(DB_COP0_SQ1, (U32)DB_COP0_SQ1_BASE, DB_COP0_SQ1_DATA_SIZE_LOG, DB_COP0_SQ1_DEPTH);
#if VS_SIM_EN
#if (!(PS5017_EN || PS5021_EN)) // E17 & E21, remove R32_DB_QTAIL_ADR
	if ((R32_DB[DB_QUEUE_CNT - 1][R32_DB_QTAIL_ADR] - R32_DB[0][R32_DB_QBASE_ADR]) > 16384) {
		while (1);
	}
#endif /* (!(PS5017_EN || PS5021_EN)) */
#endif
#if (PS5021_EN)
	MR_IR10_DATA_SIZE(DB_FLH_MSG_CQ_DATA_SIZE_LOG); //E21 FIP CQ 16B, need to modified MRIW to 16B
#endif /* PS5021_EN */
	M_MR_SET_INTERRRUPT_FULL_MASK_EXCEPT_LOG_MESSAGE();
	M_MR_SET_SQ_UPPER_LIMIT(MR_COP0_READ_SLAVE_QUEUE_ID, MR_LINK_READ_SLAVE_QUEUE_UPPER_LIMIT_CNT);			//normal link limit = 60
	M_MR_SET_SQ_UPPER_LIMIT(MR_COP0_WRITE0_SLAVE_QUEUE_ID, MR_LINK_WRITE_SLAVE_QUEUE_UPPER_LIMIT_CNT);		//merge0 link limit = 60
	M_MR_SET_SQ_UPPER_LIMIT(MR_COP0_WRITE1_SLAVE_QUEUE_ID, MR_LINK_WRITE1_SLAVE_QUEUE_UPPER_LIMIT_CNT);		//merge1 link limit = 60
	M_MR_SET_SQ_UPPER_LIMIT(MR_COP1_SQ_SLAVE_QUEUE_ID, MR_LINK_COP1_SQ_SLAVE_QUEUE_UPPER_LIMIT_CNT);		//cop1 SQ link limit = 24
#if (PS5013_EN)
	if (BMU_BUG_WORKAROUND_EN && (!BOOTLOADER_MODE_EN)) {
		// Set Log Message link
		M_MR_SET_SQ_UPPER_LIMIT(MR_LOG_MESSAGE_SLAVE_QUEUE_ID, MR_LINK_LOG_MESSAGE_QUEUE_UPPER_LIMIT_CNT);
		DBFillDummyInLogMessageQueue();
	}
#endif /* (PS5013_EN) */
	R32_MR[R32_MR_HOST_LOG_HEAD_ADR] = (U32)&guoAPUCmdLog;
}

#if (!PS5021_EN)
void DBFillDummyInLogMessageQueue(void)
{
	U8 ubi;
	U32 ulMax = 0;
	U32 ulSys1PD1RegBackup = 0;
	U32 ulSys1DbufConfigRegBackup = 0;

	// Backup Register
	ulSys1PD1RegBackup = M_SYSTEM1_GET_MISCH_CONTROL();
	ulSys1DbufConfigRegBackup = M_SYSTEM1_GET_DBUF_CONFIG();

	// Set Dbuf Parity Error
	M_MISC_SET_DBUF_PARITY_ERROR();

	// Creat MR_BMU_WORKAROUND_FAKE_LOG_MESSAGE_NUM Error Log to stuff MR Log Message Link
	gubMRLogMessageSlaveQueueDummyCnt = MR_BMU_WORKAROUND_FAKE_LOG_MESSAGE_NUM;
	for (ubi = 0; ubi < MR_BMU_WORKAROUND_FAKE_LOG_MESSAGE_NUM; ++ubi) {
		U32 ulValue = 0;
		ulValue = *((volatile U32 *)DBUF_PB_RAM_ADDRESS + (0x20 * ubi));
		//Simple Algorithm to avoid compiler optimize this for loop
		if (ulValue > ulMax) {
			ulMax++;
		}
	}
	//Check Error Log Cnt
	//Check MR Log Message linklist Write Pointer = 0
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (0 == M_MR_GET_WRITE_PTR(MR_LOG_MESSAGE_SLAVE_QUEUE_ID)));

	// Restore Reg to reset Dbuf parity error setting
	M_SYSTEM1_SET_MISCH_CONTROL_BY_REG(ulSys1PD1RegBackup);
	M_SYSTEM1_SET_DBUF_CONFIG_BY_REG(ulSys1DbufConfigRegBackup);
}
#endif /* (!PS5021_EN) */

