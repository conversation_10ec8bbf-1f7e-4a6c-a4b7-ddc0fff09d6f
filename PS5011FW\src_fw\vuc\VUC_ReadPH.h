#ifndef _VUC_READPH_H_
#define _VUC_READPH_H_

#define PH_2ND_READ_DBT_HEADER_FAIL   (BIT0)
#define PH_CHOOSE_THE_SAME_ONE        (BIT1)
#define PH_NOT_EXIST                  (BIT2)
#define PH_LOST                       (BIT3)

AOM_VUC U8 VUCReadPHGetPHFromDBTHeader(U32 ulBufAddr);
AOM_VUC void VUCReadPH(VUC_OPT_HCMD_PTR_t pCmd);
AOM_VUC U8 VUCReadPHLog(U8 ubSubfeature, U32 ulDstAddr);

#endif /* _VUC_READPH_H_ */
