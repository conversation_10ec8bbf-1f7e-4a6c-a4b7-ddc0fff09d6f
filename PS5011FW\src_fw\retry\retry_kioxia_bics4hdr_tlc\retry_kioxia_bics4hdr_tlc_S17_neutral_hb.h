#ifndef _RETRY_KIOXIA_BICS4HDR_TLC_S17_NEUTRAL_HB_H
#define _RETRY_KIOXIA_BICS4HDR_TLC_S17_NEUTRAL_HB_H

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */

#include "env.h"

#if (PS5017_EN && (FLASH_BICS4TLCHDR == FW_CATEGORY_FLASH) && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER))
/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */
#define HBIT_RETRY_BICS4_HDR_TLC_256G_STEP_NUM	(40 + 1)
#define HBIT_RETRY_BICS4_HDR_TLC_512G_STEP_NUM	(33 + 1)
#define HBIT_RETRY_BICS4_HDR_SLC_256G_STEP_NUM	(7 + 1)
#define HBIT_RETRY_BICS4_HDR_SLC_512G_STEP_NUM	(7 + 1)
#define HBIT_RETRY_BICS4_HDR_256G_SCRATCH_TABLE_NUM		(3)
#define HBIT_RETRY_BICS4_HDR_512G_SCRATCH_TABLE_NUM		(2)

/*
 * ---------------------------------------------------------------------------------------------------
 *  enum
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */


#endif /* (PS5017_EN && (FLASH_BICS4TLCHDR == FW_CATEGORY_FLASH) && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER)) */

#endif /* _RETRY_KIOXIA_BICS4HDR_TLC_S17_NEUTRAL_HB_H */
