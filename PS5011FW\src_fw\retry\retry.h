#ifndef _READ_RETRY_H_
#define _READ_RETRY_H_

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "setup.h"
#include "retry_api.h"
/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */

#define USE_ERR_Q					(1)
#define USE_NORMAL_Q				(0) 	               //****TBD*****
#define RETRY_USE_Q_MODE		USE_ERR_Q
#define RETRY_1ST_FILL_BUF  (1)
#define RETRY_2ND_FILL_BUF  (2)
#define NORMAL_RS_MODE    (0)
#define SOFITBITRAID_RS_MODE   (1)
#define HB_TRAPPING_SET_PARAMETER_NUM		(17)
#define RETRY_DRIVELOG_TASK_FAIL_MODE		(0)
#define RETRY_DRIVELOG_TASK_PASS_MODE	(1)
#define RETRY_DRIVELOG_INTERFACE_ERR_DEFAULT		(0xFF)

#define RETRY_INVALID_GLOBAL_PLANE_IDX		(0xFF)	//for NCS verify using
#define RETRY_INVALID_PAGE_IDX				(0xFFFF)
#define RETRY_GLOBAL_PLANE_NON_SLC_SHIFT	(1)
#define HB_TRAPPING_SET_PARAMETER_EN_OFFSET	(16)
#if(PS5017_EN)
#define RETRY_HB_LRU_MAX_DIE_NUM						(((MAX_CHANNEL * MAX_CE_PER_CHANNEL * 2) > RETRY_MAX_GLOBAL_DIE_NUM) ? RETRY_MAX_GLOBAL_DIE_NUM : (MAX_CHANNEL * MAX_CE_PER_CHANNEL * 2)) // Support Max 32 Die
#else /*(PS5017_EN)*/
#define RETRY_HB_LRU_MAX_DIE_NUM						(((MAX_CHANNEL * MAX_CE_PER_CHANNEL) > RETRY_MAX_GLOBAL_DIE_NUM) ? RETRY_MAX_GLOBAL_DIE_NUM : (MAX_CHANNEL * MAX_CE_PER_CHANNEL)) // Support Max 16 Die 
#endif /*(PS5017_EN)*/

#if(CALLBACK_FUNC_IN_COMMON_CODEBANK)
#define AOM_FLH_ERR_SB_CALLBACK  AOM_RETRY
#define AOM_FLH_ERR_RS_CALLBACK  AOM_RETRY
#else
#define AOM_FLH_ERR_SB_CALLBACK  AOM_RETRY_SB
#define AOM_FLH_ERR_RS_CALLBACK  AOM_RETRY_RS
#endif

#define PARAMETER_NUM_PER_FPU	(4)

#define CMP_MODE_0		(0)
#define CMP_MODE_1		(1)
#define CMP_MODE_2		(2)

#define RS_SPARE_BASE	(DBUF_HB_RETRY) // for align 4K Need
#define RS_SPARE_SIZE	(SPR_SIZE_PER_CH)
#define HB_BASE			(RS_SPARE_BASE + SPR_SIZE_PER_CH)
#define HB_SIZE_PER_CH	(0x80)
#define HB_SIZE			(HB_SIZE_PER_CH * MAX_CHANNEL)

#define ERR_HDL_MAX_L4K_PER_PAGE			(16)

#define LOWER_PAGE_SEL              (0)
#define MIDDLE_PAGE_SEL             (1)
#define UPPER_PAGE_SEL              (2)
#define TOP_PAGE_SEL                (3)

#define LDPC_FRAME_MAX_CNT          (2)

#define RAW_MODE_OFFSET     (8)
#define RAW_LOGIC_OFFSET    (6)
#define RAW_SRC_PTR_OFFSET  (3)
#define RAW_DST_PTR_OFFSET  (0)

#define IM_B47R_AXIS_INVALID_PAGE_IDX							(0xFFFF)
#define RETRY_MICRON_WORDLINEBYPASS_CLOSEPAGE		(FALSE)
#define RETRY_MICRON_WORDLINEBYPASS_OPENPAGE		(TRUE)

#define RETRY_MICRON_RECORD_WORDLINEBYPASS_SHIFT		(0)
#define RETRY_MICRON_RECORD_WORDLINEBYPASS_EXTRAPAGE_OVERRIDE_SHIFT		(1)

/*
 * ---------------------------------------------------------------------------------------------------
 *  enum
 * ---------------------------------------------------------------------------------------------------
 */

typedef enum RetryAddErrorLogReasonEnum {
	RETRY_HB_PASS = 0,
	RETRY_HB_FAIL,
	RETRY_SB_PASS,
	RETRY_SB_FAIL,
	RETRY_RAIDECC_PASS,
	RETRY_RAIDECC_FAIL,
	RETRY_TURBORAIN_PASS,
	RETRY_TURBORAIN_FAIL
} RetryAddErrorLogReasonEnum_t;

typedef enum RetryStateEnum {
	FLH_RETRY__RETRY_JOB_INIT = 0,				// 0
	FLH_RETRY__ALLOCATE_RETRY_BUFFER,			// 1

	FLH_RETRY__CREATE_HB_RETRY_TASK,            // 2
	FLH_RETRY__HANDLING_HB_RETRY,				// 3

	FLH_RETRY__BEFORE_SB_RETRY_WAIT_PATCH,		// 4
	FLH_RETRY__BEFORE_SB_RETRY_STALL,			// 5
	FLH_RETRY__CREATE_SB_TASK,		// 6
	FLH_RETRY__SB_RETRY_INIT_BETWEEN_RS,        // 7
	FLH_RETRY__HANDLING_SB_RETRY,				// 8
	FLH_RETRY__BEFORE_RS_RETRY_WAIT_PATCH,		// 9
	FLH_RETRY__BEFORE_RS_RETRY_STALL,			// 10
	FLH_RETRY__BEFORE_RS_RETRY_CHK_STALL,		// 11
	FLH_RETRY__HANDLING_RS_RETRY,				// 12
	FLH_RETRY__BEFORE_SB_AFTER_RS_RETRY,		// 13
	FLH_RETRY__HANDLING_SB_AFTER_RS_RETRY,		// 14
	FLH_RETRY__CREATE_HB_RETRY_TASK_BETWEEN_RS,	// 15
	FLH_RETRY__HANDLE_VRLC,						// 16

	FLH_RETRY__RETRY_DONE_HB,					// 17
	FLH_RETRY__RETRY_DONE_SB,					// 18
	FLH_RETRY__RETRY_DONE_RS,					// 19

	FLH_RETRY__TRIGGER_BACKUP_RESTORE,			// 20
	FLH_RETRY__TRIGGER_CORR,					// 21

	FLH_RETRY__WAITING_HB_MT_BUSY,				// 22
	FLH_RETRY__WAITING_SB_MT_BUSY,				// 23
	FLH_RETRY__WAITING_RS_MT_BUSY,				// 24
	FLH_RETRY__WAITING_NODMA_MT_BUSY,			// 25
	FLH_RETRY__WAITING_COR_MT_BUSY,				// 26
	FLH_RETRY__WATTING_VRLC_MT_BUSY,			// 27

	FLH_RETRY__RELEASE_RETRY_BUFFER,			// 28

	FLH_RETRY__SET_PATCH_CMD_PARAMETER,			// 39
	FLH_RETRY__DELETE_RETRY_JOB,				// 30

	FLH_RETRY__NODMA_HANDLE,					// 31
	FLH_RETRY__IRAM_RELEASE,                    // 32
	FLH_RETRY__FAIL,                            // 33

	FLH_RETRY__SBRAID_RETRY_INIT,               // 34
	FLH_RETRY__HANDLING_SBRAID_RETRY,			// 35
	FLH_RETRY__SBRAID_CREATE_RS_TASK,			// 36
	FLH_RETRY__SBRAID_CREATE_SB_TASK,			// 37

	FLH_RETRY__CREATE_TURBORAIN_TASK,			// 38
	FLH_RETRY__HANDLING_TURBORAIN_RETRY,		// 39
	FLH_RETRY__RETRY_DONE_TURBORAIN,			// 40
	FLH_RETRY__WAITING_TURBORAIN_MT_BUSY,		// 41
	FLH_RETRY__CREATE_SB_RETRY_TASK_BETWEEN_TURBORAIN, // 42
} RetryStateEnum_t;

typedef enum RetryModeEnum {
	NOT_DEFINE_OR_IDLE = 0,
	HB_STATE,
	SINGLE_QUEUE,
	SB_STATE,
	ZQC_CHANNEL_STATE,
	CODE_BANK,
	RS_STATE,
	BFEA_CHANNEL_STATE
} RetryModeEnum_t;

typedef enum RetryMicronWordLineTypeEnum {
	RETRY_MICRON_SLC_WORDLINE = 0,
	RETRY_MICRON_MLC_WORDLINE,
	RETRY_MICRON_TLC_WORDLINE,
	RETRY_MICRON_QLC_WORDLINE,
} RetryMicronWordLineTypeEnum_t;

typedef enum RetryHBTrigMTModeEnum {
	RETRY_HB_MODE = 0,
#if VRLC_EN
	RETRY_VRLC_MODE
#endif /* VRLC_EN */
} RetryHBTrigMTModeEnum_t;

/*
 * Co-exist of toshiba and sandisk
 *
 * CAUTION :
 * The order should be the same as gubRetrySBLowerPageMinimumDeltaVthTable
 */
typedef enum RetryBiCSProcessEnum {
	// Toshiba
	RETRY_TOSHIBA_FLASH_PROCESS_1X,
	RETRY_TOSHIBA_FLASH_PROCESS_1Y,
	RETRY_TOSHIBA_FLASH_PROCESS_1Z,
	RETRY_TOSHIBA_FLASH_PROCESS_BICS2,
	RETRY_TOSHIBA_FLASH_PROCESS_BICS3_128G,
	RETRY_TOSHIBA_FLASH_PROCESS_BICS3_256G,
	RETRY_TOSHIBA_FLASH_PROCESS_BICS3_512G,
	RETRY_TOSHIBA_FLASH_PROCESS_EARLY_BICS4_256G,
	RETRY_TOSHIBA_FLASH_PROCESS_FINAL_BICS4_256G,
	RETRY_TOSHIBA_FLASH_PROCESS_NON_EARLY_FINAL_BICS4_256G,
	RETRY_TOSHIBA_FLASH_PROCESS_BICS4_HDR_256G,
	RETRY_TOSHIBA_FLASH_PROCESS_BICS4_512G,
	RETRY_TOSHIBA_FLASH_PROCESS_BICS4_1024G,
	RETRY_TOSHIBA_FLASH_PROCESS_BICS4_1360G,
	RETRY_TOSHIBA_FLASH_PROCESS_BICS4_pTLC_512G,
	RETRY_KIOXIA_FLASH_PROCESS_BICS5_TLC_512G,
	// Sandisk
	RETRY_SANDISK_FLASH_PROCESS_BICS3_256G,
	RETRY_SANDISK_FLASH_PROCESS_BICS3_512G,
	RETRY_SANDISK_FLASH_PROCESS_BICS4_256G,
	RETRY_SANDISK_FLASH_PROCESS_BICS4_512G,

	// Total BiCS process num
	RETRY_BICS_FLASH_PROCESS_NUM
} RetryBiCSProcessEnum_t;

#if ((CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC)) //Reip Porting 3D-V7 QLC Add
// HYNIX
typedef enum RetryHynixProcessEnum {
	RETRY_HYNIX_FLASH_PROCESS_V6_512G,
	RETRY_HYNIX_FLASH_PROCESS_V6_1T,
	RETRY_HYNIX_FLASH_PROCESS_V7_512G,
	RETRY_HYNIX_FLASH_PROCESS_V8_512G,//Jeffrey Porting 3D-V8 TLC Add
	RETRY_HYNIX_FLASH_PROCESS_V7_1024G,
	RETRY_HYNIX_FLASH_PROCESS_V5_512G,//Jeffrey Porting 3D-V5 TLC Add
} RetryHynixProcessEnum_t;
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_TLC)//BICS5
typedef enum RetryHynixProcessEnum {
	RETRY_SANDISK_FLASH_PROCESS_BICS5_512G,
	RETRY_SANDISK_FLASH_PROCESS_BICS5_1024G,
} RetryHynixProcessEnum_t;
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_QLC)//zerio bics6 qlc add
typedef enum RetryHynixProcessEnum {
	RETRY_SANDISK_FLASH_PROCESS_BICS5_512G,
	RETRY_SANDISK_FLASH_PROCESS_BICS5_1024G,
} RetryHynixProcessEnum_t;
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_TLC)//
typedef enum RetryHynixProcessEnum {
	RETRY_YMTC_FLASH_PROCESS_TAS_512G,
	RETRY_YMTC_FLASH_PROCESS_WTS_512G,
} RetryHynixProcessEnum_t;
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_QLC)//
typedef enum RetryHynixProcessEnum {
	RETRY_YMTC_FLASH_PROCESS_EMS_1024G,
} RetryHynixProcessEnum_t;
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)//
typedef enum RetryHynixProcessEnum {
	RETRY_SAMSUNG_FLASH_PROCESS_V6_512G,
} RetryHynixProcessEnum_t;
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_INTEL_3D_QLC)//
typedef enum RetryHynixProcessEnum {
	RETRY_INTEL_FLASH_PROCESS_N38A_1024G,
} RetryHynixProcessEnum_t;
#endif /* (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC) */

#if (MICRON_FSP_EN)
//MICRON
typedef enum RetryMicronProcessEnum {
	RETRY_MICRON_FLASH_PROCESS_B16A,
	RETRY_MICRON_FLASH_PROCESS_B17A,
	RETRY_MICRON_FLASH_PROCESS_B27A,
	RETRY_MICRON_FLASH_PROCESS_B27B,
	RETRY_MICRON_FLASH_PROCESS_N18A,
	RETRY_MICRON_FLASH_PROCESS_N28A,
	RETRY_MICRON_FLASH_PROCESS_B47R,
	RETRY_MICRON_FLASH_PROCESS_N48R,
	RETRY_MICRON_FLASH_PROCESS_B37R,
} RetryMicronProcessEnum_t;

typedef enum HBPAGETYPEEnum {
	HB_PAGE_LOWER = 0,
	HB_PAGE_UPPER,
	HB_PAGE_EXTRA,
	HB_PAGE_TOP
} HBPAGETYPEEnum_t;

#endif /* (MICRON_FSP_EN) */

/*
 * ---------------------------------------------------------------------------------------------------
 *  data types
 * ---------------------------------------------------------------------------------------------------
 */

#pragma	pack(push)
#pragma	pack(1)
//Variable names refer to decoding flow and FIP spec.
typedef struct {
	U8 ubLDPC_MODE;  //Byte 0
	U8 ubET_ENABLE;  //Byte 1
	U16 uwSYNSUM_THRS1;   //Byte 2-3
	U16 uwSYNSUM_THRS2;   //Byte 4-5
	U8 ubABS_APP_THRS;  //Byte 6
	U8 ubMSA_ITER_THRS;  //Byte 7
	U16 uwCONVERGE_THRS;   //Byte 8-9
	U8 ubREMAP_THRS1;  //Byte 10
	U8 ubREMAP_THRS2;  //Byte 11
	U8 ubREMAP_OFFSET0;  //Byte 12
	U8 ubREMAP_OFFSET1;  //Byte 13
	U8 ubREMAP_OFFSET2;  //Byte 14
	U8 ubFLIP_MODE;  //Byte 15
	U8 ubMSA_MAX_ITE;  //Byte 16
	U8 ubFHB_MAX_ITE;  //Byte 17
	U8 ubNHB_MAX_ITE;  //Byte 18
	U8 ubGDBF_MAX_ITE;  //Byte 19
	U8 ubHB_TRAPPING_SET_LLR;   //Byte 20
	U32 ulBF_RAPRAM[17];   //Byte 21-88
	U16 uwINI_PTYCHYSUM_THRS;   //Byte 89-90
	U32 ulDspParam[10];//Byte 91-130
	U32 ulDSPGraycode[16];//Byte 131-194
	U32 ulDSP_LUT[20];//Byte 195-274
	U32 ulLLR_Table0[10];//Byte 275-314
	U32 ulLLR_Table1[10];//Byte 315-354
	U32 ulLLR_Table2[10];//Byte 355-394
	U32 ulLLR_Table3[10];//Byte 395-434
	U32 ulDSP2_GroupTable;//Byte 435-438
	U8 ubDECODE_MODE; //Byte 439
	U8 ubFLOW_MODE; //Byte 440
	U8 ubHWRegSel;
} RetryHWSetting_t;
#pragma	pack(pop)

/*
 * ---------------------------------------------------------------------------------------------------
 *	 macros
 * ---------------------------------------------------------------------------------------------------
 */

#if (IM_B47R || IM_B37R)
#define M_RETRY_GET_MICRON_BIN_LEVEL_FROM_DMA(USR_DEFINE)	 (((USR_DEFINE) >> PATCH_CMD_USERDEFINE_BIN_SHIFT) & BIT_MASK(PATCH_CMD_BIN_LEVEL_BIT_NUM))
#else /* (IM_B47R) */
#define M_RETRY_GET_MICRON_BIN_LEVEL_FROM_DMA(USR_DEFINE)	 (FALSE)
#endif /* (IM_B47R) */

/*
 * ---------------------------------------------------------------------------------------------------
 *   extern global variables
 * ---------------------------------------------------------------------------------------------------
 */

extern U32 gulVUCAddr;

extern U32 gulTrappingSetParameter[HB_TRAPPING_SET_PARAMETER_NUM];
extern U32 gulTrappingSetParameterSB[HB_TRAPPING_SET_PARAMETER_NUM];

/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */

AOM_RETRY void Retry_MountPBtoLB(void);
AOM_RETRY void Retry_CORRead_Pass_Callback(U8 ubMTIndex);
AOM_RETRY void Retry_CORRead_Fail_Callback(U8 ubMTIndex);

AOM_RETRY_2 void Retry_init(volatile FLH_ENV_STRUCT_t *pFlhEnv);

AOM_RETRY_2 U8 RetryCheckAddErrorLogCondition(RetryJob_t *pCurrentRetryJob, U8 *pubSLCMode, U8 ubRetryPhase);
AOM_RETRY_2 void RetryAddErrorLog(RetryJob_t *pCurrentRetryJob, U8 ubRetryPhase);
AOM_RETRY void Retry_GetRetryBufferFromErrMT(void);
RetryPageCoordinateInfo_t RetryGetSharedPageType(U32 ulFSA, U8 ubALUSelect);	// For RetryGetLRUACRR, Need to move from AOM_RETRY to COMMON
AOM_RETRY void RetryGetWordLineType (RetryPageCoordinateInfo_t PageCoordinateInfo);
AOM_RETRY void Retry_Create_HB_Task(void);
AOM_RETRY void Retry_Clear_HB_Task(void);
AOM_RETRY void Retry_Create_HB_Task_RS(void);
AOM_RETRY void Retry_Create_SB_Task_RS(void);
AOM_RETRY void Retry_Clear_SB_Task_RS(void);
AOM_RETRY void Retry_Create_SB_Task(void);
AOM_RETRY void Retry_Clear_SB_Task(void);
AOM_RETRY void Retry_Create_RS_Task(U8 ubMode);
AOM_RETRY void Retry_Create_TurboRain_Task(void);
AOM_RETRY void Retry_Clear_TurboRain_Task(void);
AOM_RETRY void Retry_Create_SB_Task_TurboRain(void);
AOM_RETRY void Retry_Clear_SB_Task_TurboRain(void);
AOM_RETRY U32 ReadRetryVBRMPDivRuleAlter(U32 ulPCA, U8 ubSLC_D1);
AOM_RETRY void Retry_Clear_RS_Task(void);
AOM_RETRY void Retry_Create_SoftbitRaid_Task(void);
AOM_RETRY void Retry_FillJobOtherInfo(void);
AOM_RETRY void Retry_NODMA_HANDLE(void);
AOM_RETRY void Retry_NODMA_Fail_Callback(U8 ubMTIndex);
AOM_RETRY void Retry_NODMA_Pass_Callback(U8 ubMTIndex);

extern void Retry_Manually_AllowSwitch(U8 ubChannel,  U8 ubBank, U8 ubPresentQ);

AOM_RETRY U8 Retry_PrepareFreeMT(MT_Callback_Func_t PassCallback, MT_Callback_Func_t FailCallback);
AOM_RETRY void Retry_ReleaseFinishMT(void);
AOM_RETRY void Retry_PatchMTdone_SetInitState(U8 ubMode, U8 ubChannel, U8 ubBank);
AOM_RETRY void Retry_backup_restore_IBF(U8 ubIBFNum, U8 ubDirection, U8 ubMode, U8 ubBCHMode, U8 ubAll, U8 ubFrameMask, U8 ubMTHardLock, U8 ubMTIndex);
AOM_RETRY void Retry_FIP_COR_Read(U8 ubMTIndex);
AOM_RETRY void RetryAllocateBuf2nd(void);
void ReadRetryDMACDone_CallBack(void);
AOM_RETRY void RetryHBTriggerCmdMT(U16 uwFPUPtr, U8 ubPolTrueReady, U8 ubMTLock, U8 ubMTIdx, RetryHBTrigMTModeEnum_t ubMode);
#if PS5017_EN
void ReadRetryDumpHWSettingUart(U8 ubCh, U8 ubPage, U8 ubHWRegSel, U8 ubDecodeMode, U8 ubScaleMode, U8 ubFlowMode, U8 ubHardBitFlow);
void RetryUartNCSPrintBuffer(U32 ulBufAddr, U32 ulSize);
void RetryUartNCSPrintValue(U32 ulValue, U32 ulSize);
#else /* PS5017_EN */
AOM_NCS void ReadRetryDumpHWSettingUart(U8 ubCh, U8 ubPage, U8 ubHWRegSel, U8 ubDecodeMode, U8 ubScaleMode, U8 ubFlowMode, U8 ubHardBitFlow);
AOM_NCS void RetryUartNCSPrintBuffer(U32 ulBufAddr, U32 ulSize);
AOM_NCS void RetryUartNCSPrintValue(U32 ulValue, U32 ulSize);
#endif /* PS5017_EN */
AOM_NCS void RetryUartSendNCSHBInfo(U32 ulCheckSumOffset, U32 ulBufAddr, U32 ulSpareOffset, U8 ubLDPCResult);

#if N28_VALLEY_CHECK_EN
AOM_RETRY_2 void RetryValleyCheckInit(void);
#endif/*(N28_VALLEY_CHECK_EN)*/

#endif /* _READ_RETRY_H_ */

