/**************************************************************************/
/*                                                                        */
/*           Copyright (c) 2000-2015 by Phison Electronics Corp.          */
/*                                                                        */
/**************************************************************************/

/**************************************************************************/
/*                                                                        */
/*  APPLICATION INTERFACE DEFINITION                       RELEASE        */
/*                                                                        */
/*    shr_hal_nvme_log.h                                        GNU C         */
/*                                                           X.X          */
/*  AUTHOR                                                                */
/*                                                                        */
/*                                                                        */
/*  DESCRIPTION                                                           */
/*                                                                        */
/*    This file defines the basic Application Interface (API) to the      */
/*    NVME library.                                                       */
/*                                                                        */
/*  RELEASE HISTORY                                                       */
/*                                                                        */
/*    DATE              NAME                      DESCRIPTION             */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#ifndef _SHR_NVME_LOG_LIB_H_
#define _SHR_NVME_LOG_LIB_H_
#include "common/typedef.h"
#include "nvme_api/nvme/shr_hal_nvme.h"
#include "nvme_api/nvme/shr_hal_nvme_identify.h"

//#ifdef EXTERN
//#undef EXTERN
//#endif

//#ifdef _SHR_HAL_NVME_LOG_API_C_
//#define EXTERN
//#else
//#define EXTERN  extern
//#endif

#define SAVE_ERRLOG_EN  FALSE        //Enable to save host info when error log have be insert  (Wasting flash space )
#define ERR_LOG_DOWN_SIZE TRUE   //Enable:only save no reserved field (0~39 byte)

#define	ERROR_LOG_ENTRY_NUM			(16)
#define	EXTEND_ERROR_LOG_ENTRY_NUM	(48)

#define SECURE_LOG_ENTRY_NUM	(63)

// defines for sup log page id
#define    LID_SUP                                        (0)
#define    LID_NON_SUP                                    (1)
#define    LID_RSVD                                       (2)


#define AVAILABLE_SPARE_THRESHOLD                         (5)
#define PERCENTAGE_USED_THRESHOLD     (95)  //E13 Percentage Used is 95%
#define DELL_SMART_PROGRAM_FAIL_THRESHOLD         (90)
#define DELL_SMART_ERASE_FAIL_THRESHOLD         (90)
#define DELL_SMART_WEAR_LEVELING_THRESHOLD         (90)
#define DELL_SMART_USED_RESERVED_BLOCK_THRESHOLD         (50)
#define PERCENTAGE_MAX                               (100)

#define SENSOR_KELVIN_ZERO                      (273)
#define HP_DATA_UECC_MASK                 (0xFFFFFFFF)
#define HP_DATA_UECC_SHIFT                (32)
#define HP_DATA_CRC_CHECKSUM_MASK         (0xFFFFFFFF)
#define HP_DATA_CRC_CHECKSUM_SHIFT        (0)
#define HP_DATA_LBA_TAG_MASK              (0xFFFF)
#define HP_DATA_LBA_TAG_SHIFT             (48)
#define HP_END2END_DATA_MASK              (0xFFFFFF)
#define HP_END2END_DATA_SHIFT             (24)
#define HP_MEDIA_ERROR_MASK               (0xFFFFFF)
#define HP_MEDIA_ERROR_SHIFT              (0)

//Reference E8

// Get Log Page command variables (NVM-Express Rev1.4)
#define    ERR_LOG_ENTRY_SIZE                             (BC_64B)
#define    CHG_NS_LIST_ENTRY_SIZE                         (BC_4B)
#define    MAX_CHG_NS_LIST_ENTRY_NUM                      (1024)

// for transfer size of each LID
#define    MAX_XFER_SIZE_ERR_LOG                          (ERR_LOG_ENTRY_SIZE * IDFY_CTRL_SUP_MAX_ERR_LOG_ETR)
#define    MAX_XFER_SIZE_SMART                            (512)
#define    MAX_XFER_SIZE_FW_SLOT                          (512)
#define    MAX_XFER_SIZE_CHG_NS_LIST                      (CHG_NS_LIST_ENTRY_SIZE * MAX_CHG_NS_LIST_ENTRY_NUM) // 4096
#define    MAX_XFER_SIZE_CMD_EFFECT                       (4096)
#define    MAX_XFER_SIZE_CMD_DEV_SELF_TEST                (564)
#define    MAX_XFER_SIZE_VENDOR_SMART                     (4096)
#define    MAX_XFER_SIZE_DRIVE_LOG                       (4 * 1024 * 1024) /* 4MB, TBD */
#define    CONFIG_DRIVE_LOG_SIZE_IN_BYTE                  (4 * 1024 * 1024) /* 4MB, TBD */
#define    MAX_XFER_SIZE_RSV_NOTICE                       (64)
#define    MAX_XFER_SIZE_SANI_STS    (512)
#define    MAX_XFER_SIZE_DELL_SMART_LOG    (512)

/*
 *  Asynchronous event type, defined in Figure 45. returned within AER's completion DW0
 */
#define    ASYNC_EVENT_TYPE_ERROR_STATUS                  (0)
#define    ASYNC_EVENT_TYPE_SMART_HEALTH_STATUS           (1)
#define    ASYNC_EVENT_TYPE_NOTICE                        (2)
#define    ASYNC_EVENT_TYPE_RESERVED                      (3)
#define    ASYNC_EVENT_TYPE_RESERVED1                     (4)
#define    ASYNC_EVENT_TYPE_RESERVED2                     (5)
#define    ASYNC_EVENT_TYPE_IO_CMD_SPEC_STATUS            (6)
#define    ASYNC_EVENT_TYPE_VDR_SPEC                      (7)

/*
 *  Async event information's error status, defined in Figure 47(sub types for ASYNC_EVENT_TYPE_ERROR_STATUS)
 */
#define    ASYNC_EVENT_INFO_ERR_WRT_INVALID_DBL           (0)
#define    ASYNC_EVENT_INFO_ERR_INVALID_DBL_WRITE         (1)
#define    ASYNC_EVENT_INFO_ERR_DIAG_FAIL                 (2)
#define    ASYNC_EVENT_INFO_ERR_PERSISTENT_INTERNAL       (3)
#define    ASYNC_EVENT_INFO_ERR_TRANSIENT_INTERNAL        (4)
#define    ASYNC_EVENT_INFO_ERR_FW_IMG_LOAD               (5)

/*
 *  Async event information's SMART/Health status, defined in Figure 48(sub types for ASYNC_EVENT_TYPE_SMART_HEALTH_STATUS)
 */
#define    ASYNC_EVENT_INFO_SMARTH_NVM_RELIABILITY        (0)    // maybe due to media error, internal error, or media placed in read only mode
#define    ASYNC_EVENT_INFO_SMARTH_TEMP_THRESHOLD         (1)    // over/under threshold
#define    ASYNC_EVENT_INFO_SMARTH_SPARE_BELOW_TH         (2)

/*
 *  Async event information's NOTICE , defined in Figure 49(sub types for ASYNC_EVENT_TYPE_NOTICE)
 */
#define    ASYNC_EVENT_INFO_NOTICE_NS_ATTR_CHGED          (0)
#define    ASYNC_EVENT_INFO_NOTICE_FW_ACT_STARTING        (1)
#define    ASYNC_EVENT_INFO_NOTICE_TELEMETRY_LOG_CHANGE   (2)


/*
 *  Async event information's NVM Command Set Specific Status , defined in Figure 50(sub types for I/O Command Set specific status)
 */
#define    ASYNC_EVENT_INFO_NOTICE_RSV_LOG_AVAILABLE      (0)
#define    ASYNC_EVENT_INFO_NOTICE_SANITIZE_OPERATION_COMPLETE (1)

//
#define    C_ERR_LOG_CSI_NO_INFO                          (0)
#define    C_ERR_LOG_CSI_CMD_ERR_PF                       (1)
#define    C_ERR_LOG_CSI_CMD_ERR_VF                       (2)
#define    C_ERR_LOG_CSI_FATAL_ERR                        (3)
#define    C_ERR_LOG_CSI_SQ_INV_W                         (4)
#define    C_ERR_LOG_CSI_CQ_INV_W                         (5)
#define    C_ERR_LOG_CSI_SQ_INV_R                         (6)
#define    C_ERR_LOG_CSI_CQ_INV_R                         (7)

#define    C_NOT_CMDERR                                   (0) //doorbell erro....fatal error
#define    C_CMDERR                                       (1)     //command error 
#define    C_DONTCARE_ERRSTS                              (0XFFFE)
#define    C_DONCARE_CTAG                                 (0xFF)



// LID Error Info Byte Location
#define NVME_ERR_LOC_OPCODE              (0x00)
#define NVME_ERR_LOC_DW0                 (0x00)
#define NVME_ERR_LOC_NAMESPACE           (0x04)
#define NVME_ERR_LOC_DW2                 (0x08)
#define NVME_ERR_LOC_PRP1                (0x18)
#define NVME_ERR_LOC_PRP2                (0x20)
#define NVME_ERR_LOC_DW10                (0x28)
#define NVME_ERR_LOC_DW11                (0x2C)
#define NVME_ERR_LOC_DW12                (0x30)
#define NVME_ERR_LOC_DW13                (0x34)
#define NVME_ERR_LOC_DW14                (0x38)
#define NVME_ERR_LOC_DW15                (0x3C)

#define NVME_DW_BYTE_OFFSET_0            (0x00)
#define NVME_DW_BYTE_OFFSET_1            (0x01)
#define NVME_DW_BYTE_OFFSET_2            (0x02)
#define NVME_DW_BYTE_OFFSET_3            (0x03)
// LID Error Info The File Is Not Specific Error
#define NVME_ERR_NOT_SPECIFIC              (0xFF)
#define NVME_ERROR_CONTROLLER_RESET			(0xFF)
/*
 *  Get Log Page-SMART/Health Information Log, defiined in Figure 79
 */
#define    SMARTH_CRITICAL_WARN_AVAIL_SPACE_BELOW_TH     (1)
#define    SMARTH_CRITICAL_WARN_TEMP_OVER_UNDER_TH       (2)
#define    SMARTH_CRITICAL_WARN_RELIABILITY_DEGRADE      (3)
#define    SMARTH_CRITICAL_WARN_MEDIA_READ_ONLY          (4)
#define    SMARTH_CRITICAL_WARN_VM_BACKUP_FAIL           (5)

/*
 *  Get Log Page-self-test result data structure, defiined in Figure 99
 */
/*
 * DW10-  Device Self test:note FW to abort device self test
 */
#define DST_SHORT                        (0x1)
#define DST_EXTENDED                     (0x2)
#define DST_CMD_ABORT_CNTLRESET          (0x3)
#define DST_CMD_ABORT_REMOVE_NS          (0x4)
#define DST_CMD_ABORT_FORMAT             (0x5)
#define DST_CMD_ABORT_SANITIZE           (0x6)
#define SATA_DST_OFFLINE                 (0x7)
#define SATA_DST_CONVEYANCE              (0x8)
#define SATA_DST_SELECTIVE               (0x9)
#define DST_VENDOR                       (0xE)
#define DST_CMD_ABORT                    (0xF)
//---------------------------------------------------
//device self-test status: result of the device self-test operation figure99 bit3:0
#define DST_RST_SUCCESS                 (0x0)
#define DST_RST_COMMAND_ABORT           (0x1)
#define DST_RST_CONTROLLER_RESET_ABORT  (0x2)
#define DST_RST_NAMESPACE_REMOVE_ABORT  (0x3)
#define DST_RST_FORMAT_NVM_ABORT        (0x4)
#define DST_RST_FATAL_ERROR_ABORT       (0x5)
#define DST_RST_UNKNOWN_SEGMENT_FAIL    (0x6)
#define DST_RST_SEGMENT_FAIL            (0x7)
#define DST_RST_UNKNOWN_REASON_ABORT    (0x8)
#define DST_RST_SANTIZE_ABORT           (0x9)
#define DST_RST_ENTRY_NO_USED           (0xF)
//valid diagnostic information:byte2
#define DST_RST_NSID_VALID              (BIT0)
#define DST_RST_FLAB_VALID              (BIT1)
#define DST_RST_SCT_VALID               (BIT2)
#define DST_RST_SC_VALID                (BIT3)

#define DST_CPL_NEVER_START              (0)
#define DST_CPL_SETUP_DONE			(5)
#define DST_CPL_CHECK_DATA_DONE		(90)
#define DST_CPL_COMPLETE			(100)
#define AER_CLEAR_RSVD2 0xFFFFFF
#define AER_NEED_1ST_SENT 0x80
#define AER_NEED_2ND_SENT 0x40
#define AER_ALREADY_SENT 0x00
#define AER_CLEAR_RSVD2 0xFFFFFF

#define PERSISTENT_EVENT_BITMAP0_SMART					(BIT1)
#define PERSISTENT_EVENT_BITMAP0_FW_COMMIT				(BIT2)
#define PERSISTENT_EVENT_BITMAP0_TIMESTAMP_CHANGE 		(BIT3)
#define PERSISTENT_EVENT_BITMAP0_POWER_ON_OR_RESET		(BIT4)
#define PERSISTENT_EVENT_BITMAP0_NVM_SUBSYSTEM_HW_ERROR	(BIT5)
#define PERSISTENT_EVENT_BITMAP0_CHANGE_NAMESPACE 		(BIT6)
#define PERSISTENT_EVENT_BITMAP0_FORMAT_NVM_START 		(BIT7)
#define PERSISTENT_EVENT_BITMAP0_FORMAT_NVM_COMPLETE		(BIT8)
#define PERSISTENT_EVENT_BITMAP0_SANITIZE_START			(BIT9)
#define PERSISTENT_EVENT_BITMAP0_SANITIZE_COMPLETE		(BIT10)
#define PERSISTENT_EVENT_BITMAP0_SET_FEATURE				(BIT11)
#define PERSISTENT_EVENT_BITMAP0_TELEMETRY_LOG_CREATE 		(BIT12)
#define PERSISTENT_EVENT_BITMAP0_THERMAL_EXCURSION		(BIT13)

#define PERSISTENT_EVENT_BITMAP3_VENDOR_SPECIFIC			(BIT30)
#define PERSISTENT_EVENT_BITMAP3_TCG_DEFINED				(BIT31)

#define SUPPORT_PERSISTENT_EVENT_BITMAP0 (PERSISTENT_EVENT_BITMAP0_SMART | \
                                          PERSISTENT_EVENT_BITMAP0_FW_COMMIT | \
                                          PERSISTENT_EVENT_BITMAP0_TIMESTAMP_CHANGE | \
                                          PERSISTENT_EVENT_BITMAP0_POWER_ON_OR_RESET | \
                                          PERSISTENT_EVENT_BITMAP0_NVM_SUBSYSTEM_HW_ERROR | \
										  PERSISTENT_EVENT_BITMAP0_CHANGE_NAMESPACE | \
                                          PERSISTENT_EVENT_BITMAP0_FORMAT_NVM_START | \
                                          PERSISTENT_EVENT_BITMAP0_FORMAT_NVM_COMPLETE | \
                                          PERSISTENT_EVENT_BITMAP0_SANITIZE_START | \
                                          PERSISTENT_EVENT_BITMAP0_SANITIZE_COMPLETE)

typedef enum {
	DLMC_SECURITY_LOG = 1,
	TCG_SECURITY_LOG,
	AUTHORIZED_ACCESS_OF_VUC,
	UNAUTHORIZED_ACCESS_OF_VUC
} SECURE_LOG_HEADER_t;

#pragma pack(1)

/**
 * @brief NVME.
 *
 * Refer to NVM_Express_1_2_Gold_20141209.pdf.
 */
typedef enum {

	LID_RSV,        // Reserved (0x00)
	LID_ERR_INFO,         // Error information  (0x01)
	LID_SMART_INFO,         // Smart/Health information  (0x02)
	LID_FW_SLOT_INFO,        // Firmware slot information  (0x03)
	LID_CHG_NS_LIST,        // Changed Namespace List  (0x04)
	LID_CMD_EFF_LOG,        // command effects log       (0x05)
	LID_DEV_SELF_TEST,        // devive selftest                 (0x06)
	LID_TELEMETRY_HOST_INIT,        // Telemetry Host-Initiated   (0x07)
	LID_TELEMETRY_CNT_INIT,        // Telemetry Controller-Initiated (0x08)
	LID_PERSISTENT_EVENT = 0x0D,		// Persistent Event Log
	LID_RSV_NOTICE = 0x80,  // Reservation Notification
	LID_SANI_LOG = 0x81, //Sanitize status
	// Vendor Unique Command
	LID_VUC_GET_SMART = 0xC0,
	LID_UNSECURE_LOG = 0xC7,
	LID_DELL_SMART = 0xCA,
} NVME_LOG_ID_ENUM ;

typedef enum {
	EVENT_TYPE_SMART 					= 0x1,
	EVENT_TYPE_FW_COMMIT 				= 0x2,
	EVENT_TYPE_TIMESTAMP_CHANGE 		= 0x3,
	EVENT_TYPE_POWER_ON_OR_RESET		= 0x4,
	EVENT_TYPE_NVM_SUBSYSTEM_HW_ERROR 	= 0x5,
	EVENT_TYPE_CHANGE_NAMESPACE 		= 0x6,
	EVENT_TYPE_FORMAT_NVM_START 		= 0x7,
	EVENT_TYPE_FORMAT_NVM_COMPLETE 		= 0x8,
	EVENT_TYPE_SANITIZE_START 			= 0x9,
	EVENT_TYPE_SANITIZE_COMPLETE 		= 0xA,
	EVENT_TYPE_SET_FEATURE 				= 0xB,
	EVENT_TYPE_TELEMETRY_LOG_CREATE 	= 0xC,
	EVENT_TYPE_THERMAL_EXCURSION 		= 0xD,
	EVENT_TYPE_VENDOR_SPECIFIC 			= 0xDE,
	EVENT_TYPE_TCG_DEFINED 				= 0xDF,
} PERSISTENT_EVENT_TYPE_ENUM ;
//From E19
typedef enum {
	/* High Temp Transitions */
	THERMAL_EXCURSION_GREATER_WCTEMP	= 0x1,
	THERMAL_EXCURSION_GREATER_CCTEMP	= 0x2,
	THERMAL_EXCURSION_GREATER_TMT1 		= 0x3,
	THERMAL_EXCURSION_GREATER_TMT2		= 0x4,
	THERMAL_EXCURSION_GREATER_VENDOR 	= 0x5,
	/* Normal Temp Transitions */
	THERMAL_EXCURSION_RETURN_NORMAL		= 0x88,
	THERMAL_EXCURSION_TT_RESUME			= 0x89,
	/* Low Temp Transitions */
	THERMAL_EXCURSION_LESS_UNDER 		= 0xB0,
} THERMAL_EXCURSION_THRESHOLD_ENUM;

/*
 *  Data structure for 3 event queues:
 *  - error event queue, smart event queue, and notice event queue
 */
typedef struct nvme_async_event_dw0    ASYNC_EVENT_DW0_T, * ASYNC_EVENT_DW0_PTR;

struct nvme_async_event_dw0 {
	U8    btAsyncEvtType     : 3;     // [error,smart,notice]
	U8    btRsvd1       : 5;     // [error,smart,notice] vendor defined and used if FW wants to locate the high priority event and process in the future !
	U8    ubAsyncEvtInfo;             // [error,smart,notice]
	U8    ubAsyncLogPageId;                // [error,smart,notice]
	U8    ubRsvd2;                        // [0x80:AER need to sent ,0x40:HP AER need to second sent , 0: already sent]
};


/*
 *  Data structure for Get Log Page LID = 0x1(Error Information Log), ref Figure 78
 */

typedef struct nvme_err_log_info    ERR_LOG_INFO_T, * ERR_LOG_INFO_PTR;

struct   nvme_err_log_info {          // 32B
	U16    uwErrSQId;           // should be 0xFFFF if not specific to a command
	U16    uwErrCmdId;                  // should be 0xFFFF if not specific to a command
	union {
		struct errraw {
			U16    uwErrStsField;//bit[15:1] status and bit0 for P tag.

		} errraw;

		struct uwerr {
			U16    btPT: 1;
			U16    btSC: 8;
			U16    btSCT: 3;
			U16    btRsvd: 2;
			U16    btMore: 1;
			U16    btDnr: 1;
		} uwerr;
	};

	U16    btParaErrByte  : 8;   // valid values 0~63
	U16    btParaErrBit   : 8;   // [2:0]valid values 0~7 [3:7] reserved
	U64    uoErrLba;
	U32    ulErrNs;                   // namespace that the error is associated with
	U8      ubErrVdrSpecLid;      // 0 indicates no additional info. Valid range 80h~FFh
	U8      ubRsvd2[3];
	U64    uoErrCSI;                  // command specific information
} ;

typedef struct nvme_err_log_entry    ERR_LOG_ENTRY_T, * ERR_LOG_ENTRY_PTR;

struct nvme_err_log_entry {
	U64    uoErrorCount;              // 8B. unique identifier. reset once MP and accumulate whenever error log inserted. 0 indicate invalid entry

	// for "specific to a command" mainly
	ERR_LOG_INFO_T ErrInfo;    // 32B
#if (ERR_LOG_DOWN_SIZE != TRUE)
	U32    ulRsvd[6];              // 24B
#endif
} ;

#if ERR_LOG_DOWN_SIZE
typedef struct nvme_err_log_down_size_entry    ERR_LOG_DOWN_SIZE_ENTRY_T, * ERR_LOG_DOWN_SIZE_ENTRY_PTR;

struct nvme_err_log_down_size_entry {
	ERR_LOG_ENTRY_T            ErrEnt;

	U32    ulRsvd[6];              // 24B
} ;

#endif


typedef struct nvme_err_log_save_var    ERR_LOG_SAVE_VAR_T, * ERR_LOG_SAVE_VAR_PTR;

struct nvme_err_log_save_var {

	// for error info log. If not so FW need to manually scan whole error info log then locate these 2 info !
	U64       uoCurrErrLogCnt;         // init to 1 by MP. When insert error, inc this then assign to Error Count field in entry
	U8         ubCurrErrLogWidx;      // init to 0 by MP. After insert error entry done, inc this pointer
	U8 		   ubPhySettingIdx;
	U8         ubRsvd[2];


} ;

typedef struct nvme_err_log_save_info    ERR_LOG_SAVED_T, *  ERR_LOG_SAVED_PTR;


struct nvme_err_log_save_info {
	ERR_LOG_ENTRY_T ErrLogEntry[ERROR_LOG_ENTRY_NUM];
	ERR_LOG_SAVE_VAR_T    ErrMisc;
};


typedef struct nvme_smart_log_cri_warn    SMART_LOG_CRITICAL_WARN_T, * SMART_LOG_CRITICAL_WARN_PTR;


//smart/health information log critical warning
struct nvme_smart_log_cri_warn {
	union {
		U8 ubAll;
		struct {
			U8     btSmartAvailSpareBelowTh   : 1;
			U8     btSmartTmpThUnderTh     : 1;
			U8     btSmartReliabilityDegraded    : 1;
			U8     btSmartMediaReadOnly            : 1;
			U8     btSmartVolMemBackupFail    : 1;
			U8     btRsvd1                  : 3;
		};
	};
};


//need to sync with FTL_SMART_SAVE_STRUCT
typedef struct nvme_smart_log_save_info    SMART_LOG_SAVED_T, *  SMART_LOG_SAVED_PTR;

struct nvme_smart_log_save_info {
	//hardware only use U64 fields
	U64    uoDataUnitRead[1];
	U64    uoDataUnitWritten[1];
	U64    uoHostReadCmdCnt[1];
	U64    uoHostWriteCmdCnt[1];
	U64    uoCtrlBusyTimeMin[1];
	U64    uoCtrlBusyTimeMS[1];
	U32    ulCtrlSysClockCnt[1];
	SMART_LOG_CRITICAL_WARN_T         SmartCriWarn;
	U8      ubRsvd0[3];
	U64    uoPowerOnSec[1];// hours= x/3600 (60 minutes * 60 sec)
} ;



/*
 * Data structure for Get Log Page LID = 0x2(Smart/health information Log), ref Figure 79
 */
typedef struct nvme_smart_log    SMART_LOG_T, *  SMART_LOG_PTR;


struct nvme_smart_log {
	//-------------------------------
	//  union {//B0
	SMART_LOG_CRITICAL_WARN_T     CriWarn;

	U16    uwCompositeTemperature;//B2-B1
	U8     ubAvailSpare;//B3
	U8     ubAvailSpareThreshold;//B4
	U8     ubPercentageUsed;//B5
	U8     ubRsvd1[26];//B31-B6
	U64    uoDataUnitsRead[2];//B47-B32
	U64    uoDataUnitsWritten[2];//B63-B48
	U64    uoHostRdCmds[2];//B79-B64
	U64    uoHostWtCmds[2];//B95-B80
	U64    uoControllerBusyTime[2];//B111-B96
	U64    uoPowerCycles[2];//B127-B112
	U64    uoPowerOnHours[2];//B143-B128
	U64    UnsafeShutdowns[2];//B159-B144
	U64    uoMediaErr[2];//B175-B160
	U64    uoNumOfErrInfoLogEntries[2];//B191-B176
	U32   ulWarningCompositeTemperatureTimeMins;//B195-B192
	U32   ulCriticalCompositeTemperatureTimeMins;//B199-B196
	U16   uwTemperatureSensor1;//B201-B200
	U16   uwTemperatureSensor2;//B203-B202
	U16   uwTemperatureSensor3;//B205-B204
	U16   uwTemperatureSensor4;//B207-B206
	U16   uwTemperatureSensor5;//B209-B208
	U16   uwTemperatureSensor6;//B211-B210
	U16   uwTemperatureSensor7;//B213-B212
	U16   uwTemperatureSensor8;//B215-B214
	U32   ulThermalMT1TransitionCnt;//B219-B216
	U32   ulThermalMT2TransitionCnt;//B223-B220
	U32   ulTotalTimeForThermalMT1;//B227-B224
	U32   ulTotalTimeForThermalMT2;//B231-B228
	U32	  ulPhyErrorCnt;//B232-B235
	U8     ubRsvd2[276];//B511-B236
};
LIB_SIZE_CHECK(SMART_LOG_T, SIZE_512B);

/*
 * persistent log region (based on NVMe 1.4 2019/03/14)
 */
typedef struct persistent_event_log	 PERSISTENT_EVENT_LOG_T, *  PERSISTENT_EVENT_LOG_PTR;

struct persistent_event_log {
	U8 ubLogIdentifier;
	U8 ubReserved0[3];
	U32 ulTNEV;	// Total Number of Events
	U64 uoTLL; // Total Log Length
	U8 ubLogRevision;
	U8 ubReserved1;
	U16 uwLogHeaderLength;
	U64 uoTimestamp;
	U64 uoPowerOnHours0;
	U64 uoPowerOnHours1;
	U64	uoPowerCycleCount;
	U16 uwVID; // PCI Vendor ID
	U16 uwSSVID; // PCI Subsystem Vendor ID
	U8 ubSN[20]; // Serial Number
	U8 ubMN[40]; // Model Number
	U8 ubSUBNQN[256]; // NVM Subsystem NVMe Qualified Name
	U8 ubReserved2[108];
	U64 uoSupportedEventsBitmap0;
	U64 uoSupportedEventsBitmap1;
	U64 uoSupportedEventsBitmap2;
	U64 uoSupportedEventsBitmap3;
};
LIB_SIZE_CHECK(PERSISTENT_EVENT_LOG_T, SIZE_512B);

typedef struct persistent_event_log_event_header   PERSISTENT_EVENT_LOG_EVENT_HEADER_T, *  PERSISTENT_EVENT_LOG_EVENT_HEADER_PTR;
struct persistent_event_log_event_header {
	U8 ubEventType;
	U8 ubEventTypeRevision;
	U8 ubEventHeaderLength;
	U8 ubReserved0;
	U16 uwControllerIdentifier;
	U64 uoEventTimestamp;
	U8 ubReserved1[6];
	U16 uwVSIL; // Vendor Specific Information Length
	U16 uwEL; // Event Length
};

typedef union PersistentLogPayload PERSISTENT_LOG_PAYLOAD_T, *PERSISTENT_LOG_PAYLOAD_PTR;

union PersistentLogPayload {
	struct {
		PERSISTENT_EVENT_LOG_EVENT_HEADER_T Header;
	} GeneralHeader; // total 24Byte

	struct {
		PERSISTENT_EVENT_LOG_EVENT_HEADER_T Header;
		SMART_LOG_T SmartLog;
	} SmartHealthLogSnapshot; // total 536 Byte

	struct {
		PERSISTENT_EVENT_LOG_EVENT_HEADER_T Header;
		U64 uoOldFirmwareRevision;
		U64 uoNewFirmwareRevision;
		U8 ubFirmwareCommitAction;
		U8 ubFirmwareSlot;
		U8 ubStatusCodeType;
		U8 ubStatusReturned;
		U16 uwVendorAssignedFirmwareCommitResult;
	} FirmwareCommit; // total 46 Byte

	struct {
		PERSISTENT_EVENT_LOG_EVENT_HEADER_T Header;
		U64 uoPreviousTimestamp;
		U64 uoMillisecondsSinceReset;
	} TimestampChange; // total 40 Byte

	struct {
		PERSISTENT_EVENT_LOG_EVENT_HEADER_T Header;
		U64 uoFirmwareRevision;
		U16 uwControllerID;
		U8 ubFirmwareActivation;
		U8 ubOperationInProgress;
		U8 ubReserved[12];
		U32 ulControllerPowerCycle;
		U64 uoPowerOnMilliseconds;
		U64 uoControllerTimestamp;
	} PowerOnOrReset; // total 68 Byte, it's for normal use. Its size maybe different. If differ from this structure, self-define it.

	struct {
		PERSISTENT_EVENT_LOG_EVENT_HEADER_T Header;
		U16 uwNVMSubsystemHardWareErrorEventCode;
		U16 uwReserved;
	} NVMSubsystemHardwareError; // total 28 Byte

	struct {
		PERSISTENT_EVENT_LOG_EVENT_HEADER_T Header;
		U16 uwNVMSubsystemHardWareErrorEventCode;
		U16 uwReserved;
		U16 uwLinkStatusReg;
	} NVMSubsystemHardwareError04H; // total 30 Byte, PCIe Link Status Change

	struct {
		PERSISTENT_EVENT_LOG_EVENT_HEADER_T Header;
		U16 uwNVMSubsystemHardWareErrorEventCode;
		U16 uwReserved;
		U8 ubCriticalWarning;
	} NVMSubsystemHardwareError06H; // total 29 Byte, Critical  Warning  Condition

	struct {
		PERSISTENT_EVENT_LOG_EVENT_HEADER_T Header;
		U16 uwNVMSubsystemHardWareErrorEventCode;
		U16 uwReserved;
		U32 ulAdditionalHardwareError;
	} NVMSubsystemHardwareError07H; // total 32 Byte, Endurance  Group  Critical  Warning  Condition

	struct {
		PERSISTENT_EVENT_LOG_EVENT_HEADER_T Header;
		U16 uwNVMSubsystemHardWareErrorEventCode;
		U16 uwReserved;
		U64 uoUnsafeShutdownL;
		U64 uoUnsafeShutdownH;
	} NVMSubsystemHardwareError08H; // total 44 Byte, Unsafe  Shutdown

	struct {
		PERSISTENT_EVENT_LOG_EVENT_HEADER_T Header;
		U16 uwNVMSubsystemHardWareErrorEventCode;
		U16 uwReserved;
		U32 ulCQContent[4];
	} NVMSubsystemHardwareError0AH; // total 44 Byte, Media and Data Integrity Status

	struct {
		PERSISTENT_EVENT_LOG_EVENT_HEADER_T Header;
		U16 uwNVMSubsystemHardWareErrorEventCode;
		U16 uwReserved;
		U16 uwPCIeDeviceStatusRegister;
		U8 btPCIeAERSupported : 1;
		U8 btReserved : 7;
		U8 ubReserved[13];
		U32 ulPCIeAERErrorStatus0;
		U32 ulPCIeAERErrorStatus1;
		U32 ulPCIeAERErrorStatus2;
		U32 ulPCIeAERErrorStatus3;
		U32 ulPCIeAERErrorMask0;
		U32 ulPCIeAERErrorMask1;
		U32 ulPCIeAERErrorMask2;
		U32 ulPCIeAERErrorMask3;
		U32 ulPCIeAERHeaderLogRegister0;
		U32 ulPCIeAERHeaderLogRegister1;
		U32 ulPCIeAERHeaderLogRegister2;
		U32 ulPCIeAERHeaderLogRegister3;
		U32 ulPCIeAERTLPPrefixLogRegister0;
		U32 ulPCIeAERTLPPrefixLogRegister1;
		U32 ulPCIeAERTLPPrefixLogRegister2;
		U32 ulPCIeAERTLPPrefixLogRegister3;
	} NVMSubsystemHardwareErrorAddition; // total 108 Byte

	struct {
		PERSISTENT_EVENT_LOG_EVENT_HEADER_T Header;
		U32 ulNamespaceManagementCDW10;
		U32 ulReserved;
		U64 uoNSZE;
		U64 uoNCAP[2];
		U8 ubFLBAS;
		U8 ubDPS;
		U8 ubNMIC;
		U8 ubReserved;
		U32 ulANAGRPID;
		U16 uwNVMSETID;
		U16 uwReserved;
		U32 ulNSID;
	} ChangeNamespace; // total 72 Byte

	struct {
		PERSISTENT_EVENT_LOG_EVENT_HEADER_T Header;
		U32 ulNamespaceIdentifier;
		U8 ubFNAFormatApplyAllns : 1;
		U8 ubFNAEraseApplyAllns : 1;
		U8 ubFNACryptoEraseSup : 1;
		U8 ubFNAReserve : 5;
		U8 ubReserved[3];
		U32 ulFormatNVMCDW10;
	} FormatNVMStart; // total 36 Byte

	struct {
		PERSISTENT_EVENT_LOG_EVENT_HEADER_T Header;
		U32 ulNamespaceIdentifier;
		U8 ubSmallestFormatProgressIdicator;
		U8 ubFormatNVMStatus;
		U16 uwCompletionInformation;
		U32 ulStatusField;
	} FormatNVMCompletion; // total 36 Byte

	struct {
		PERSISTENT_EVENT_LOG_EVENT_HEADER_T Header;
		U32 ulSANICAP;
		U32 ulSanitizeCDW10;
		U32 ulSanitizeCDW11;
	} SanitizeStart; // total 36 Byte

	struct {
		PERSISTENT_EVENT_LOG_EVENT_HEADER_T Header;
		U16 uwSanitizeProgress;
		U16 uwSanitizeStatus;
		U16 uwCompletionInformation;
		U16 uwReserved;
	} SanitizeCompletion; // total 32 Byte


	// following are optional..
	struct {
		PERSISTENT_EVENT_LOG_EVENT_HEADER_T Header;
		U32 btDwordCount : 3;
		U32 btLoggedCommandCompletionDword0 : 1;
		U32 btReserved : 12;
		U32 btMemoryBufferCount : 16;
		U32 ulCommandDword[6];
	} SetFeature; // total 52 Byte, it's for normal use WITHOUT memory buffer and CommandCompletionDW0

	struct {
		PERSISTENT_EVENT_LOG_EVENT_HEADER_T Header;
		U32 btDwordCount : 3;
		U32 btLoggedCommandCompletionDword0 : 1;
		U32 btReserved : 12;
		U32 btMemoryBufferCount : 16;
		U32 ulCommandDword[6];
		U32 ulCommandCompletionDword0;
	} SetFeatureWithDW0; // total 56 Byte, it's for normal use WITHOUT memory buffer

	struct {
		PERSISTENT_EVENT_LOG_EVENT_HEADER_T Header;
		U8 ubTelemetryInitiatedLog[512];
	} TelemetryLogCreated; // total 536 Byte
	struct {
		PERSISTENT_EVENT_LOG_EVENT_HEADER_T Header;
		U8 ubOverTemperature;
		U8 ubThreshold;
	} ThermalExcursion; // total 26 Byte

	struct {
	} VendorSpecificEvent;
	struct {
	} TCGDefined;
};

/*
 * Data structure for Get Log Page LID = 0x3(Firmware slot information Log), ref Figure 81
 */
#define 	FW_REVISION_SLOT_OFFSET   (8)  // byte
typedef struct nvme_fw_slot_log    FW_SLOT_LOG_T, *  FW_SLOT_LOG_PTR;

struct nvme_fw_slot_log {
	// byte 0~7
	union {
		U32 all;
		struct {
			U32    btAfiActiveFwSlot     : 3;
			U32    btRsvd1                  : 1;
			U32    btActFwSlotNextRst : 3;
			U32    btRsvd2                  : 25;
		} bits;
	} afi;  // active firmware information
	U32     ulRsvd3;

	// byte 8~63
	U8      ubFwRevisionSlot1[8];
	U8      ubFwRevisionSlot2[8];
	U8      ubFwRevisionSlot3[8];
	U8      ubFwRevisionSlot4[8];
	U8      ubFwRevisionSlot5[8];
	U8      ubFwRevisionSlot6[8];
	U8      ubFwRevisionSlot7[8];


	// offset 64
	//U8     rsvd4[448];
};
LIB_SIZE_CHECK(FW_SLOT_LOG_T, SIZE_64B);
//TYPE_SIZE_CHECK(FW_SLOT_LOG_T, SIZE_32B);
/*
 * Data structure for Get Log Page LID = 0x4(Command changed namespace list Log), ref Figure 82,83
 */
//typedef struct nvme_ns_list_log    NS_LIST_LOG_SAVED_T, *  NS_LIST_LOG_SAVED_PTR;

//struct nvme_ns_list_log
//{
//U32    ulNsListLogEntry[1024];
//    U32    ulNsListLogEntry[MAX_NS_NUMBER];
//} ;

typedef struct nvme_ns_list_log_pro    NS_LIST_LOG_PRO_T, *  NS_LIST_LOG_PRO_PTR;

struct nvme_ns_list_log_pro {
	U32    ulNsListLogCnt;
} ;

typedef struct nvme_ns_list_log    NS_LIST_LOG_SAVED_T, *  NS_LIST_LOG_SAVED_PTR;

struct nvme_ns_list_log {
	NS_LIST_LOG_PRO_T  ulNsListLogMisc;
	U32    ulNsListLogEntry[1];//NS1~NS32
};

/*
 * Data structure for Get Log Page LID = 0x5(Command Effects Log), ref Figure 37
 */

typedef struct nvme_effect_log_enter    EFFECT_LOG_ENTER_T, *  EFFECT_LOG_ENTER_PTR;

struct nvme_effect_log_enter {
	U32     btCmdSupp                     : 1;
	U32     btLogicalBlkContentChg   : 1;
	U32     btNsCapabilityChg           : 1;
	U32     btNsInventoryChg           : 1;    // adding or removing namespace
	U32     btCtrlCapabilityChg          : 1;    // capability change include a firmware update that change the CAP settings
	U32     btRsvd1                          : 11;
	U32     btCmdSubmitExec           : 3;
	U32     btRsvd2                         : 13;
} ;

typedef struct nvme_effect_log    EFFECT_LOG_T, *  EFFECT_LOG_PTR;

struct  nvme_effect_log { //  4KB
	EFFECT_LOG_ENTER_T    AdminCmdSup[256];
	EFFECT_LOG_ENTER_T    IOCmdSup[256];
	U8 ubRsvd[2048];
};
LIB_SIZE_CHECK(EFFECT_LOG_T, SIZE_4KB);
/*
 * Data structure for Get Log Page LID = 0x7(Telemetry Host-initiated), ref Figure 101
 */
typedef struct nvme_telemetry_log_handler    TELEMETRY_LOG_HANDLER_T, * TELEMETRY_LOG_HANDLER_PTR;

struct nvme_telemetry_log_handler {
	U64     uoTCIStartBufOffset;         //record data offset for fw reference
	U32     ulTCITransferByteCnt;
	U32     btTCIDataAvailable        : 1; //set to 1 the controller shall have log
	U32     btTCICreateData           : 1; //set to 1 the controller shall capature
	U32     ubDataGenNum             : 8;
	U32     Rsvd1                        : 22;

};

typedef struct nvme_telemetry_host_init_log    TELEMETRY_HOSTI_LOG_T, * TELEMETRY_HOSTI_LOG_PTR;

struct nvme_telemetry_host_init_log {
	U8   ubTHILid;
	U8   ubRsvd1[4];
	U8   ubTHIIEEEOuiId[3];
	U16 uwTHIDataAreaLastBlk1;
	U16 uwTHIDataAreaLastBlk2;
	U16 uwTHIDataAreaLastBlk3;
	U8   ubRsvd2[368];

	// offset 382
	U8 ubTCIDataAvailable;
	U8 ubTCIDataGenNum;
	U8 ubReasonId[128];
} ;
LIB_SIZE_CHECK(TELEMETRY_HOSTI_LOG_T, SIZE_512B);
/*
 * Data structure for Get Log Page LID = 0x8(Telemetry Controller-initiated), ref Figure 102
 */
typedef struct nvme_telemetry_log_saved_info    TELEMETRY_LOG_SAVED_T, * TELEMETRY_LOG_SAVED_PTR;

struct nvme_telemetry_log_saved_info {
	TELEMETRY_LOG_HANDLER_T  TelemetryLogMisc;
};


/*
 * Data structure for Get Log Page LID = 0x6(Device Self_test 06), ref Figure 99
 */
typedef struct nvme_dev_self_test_result    DEV_SELF_TEST_RESULT_T, *  DEV_SELF_TEST_RESULT_PTR;

struct nvme_dev_self_test_result {
	U8  ubDSTStatusResult  : 4;       //test result
	U8  ubDSTStatusOperation   : 4;//first start operation
	U8  ubSegmentNum;
	U8  ubValidDiagnosticInfo;
	U8  ubRsv1;
	U64 woDSTPowerOnHours;
	U32 ulNSID;
	U64 uoFailingLBA;
	U16 uwStatusCode;
	U8  btRsv2[2];
} ;

/*
 * Data structure for Get Log Page LID = 0x6(Device Self_test 06), ref Figure 98
 */
typedef struct nvme_dev_self_test_svaed    DEV_SELF_TEST_SAVED_T, *  DEV_SELF_TEST_SAVED_PTR;

struct nvme_dev_self_test_svaed {
	U8 ubCurrDSTOper;
	U8 ubCurrDSTCpl;
	U8 ubRsv1[1];
	U8 ubDSTResultDataTail;
	DEV_SELF_TEST_RESULT_T result_data[LOG_DST_RSLT_ENTRY];
} ;

typedef struct NGUID    NGUID_T, *  NGUID_PTR;

struct NGUID {
	U64 uoNGUIDL;
	U64 uoNGUIDH;
} ;

/*
 * Data structure for Get Log Page LID = 0xCx, Secure Log
 */
typedef union nvme_secure_log	SECURE_LOG_T, *  SECURE_LOG_PTR;

union nvme_secure_log {
	U32 ulAll;
	struct {
		U8 ubHeader;
		U8 ubLogType;
		U16 uwTimeStamp;
	} DLMCLog;
	struct {
		U8 ubHeader;
		U8 ubFeature;
		U16 uwTimeStamp;
	} VUCLog;
} ;

typedef struct nvme_secure_log_saved    SECURE_LOG_SAVED_T, *  SECURE_LOG_SAVED_PTR;

struct nvme_secure_log_saved {
	U32 ulIdx;
	SECURE_LOG_T SecureLog[SECURE_LOG_ENTRY_NUM];
} ;

#pragma pack()

AOM_NRW extern U16 nvme_log_check_nsid (OPT_HCMD_PTR pCmd);
AOM_NRW extern U8   nvme_log_check_sup_fid (U8 ubLogID);
AOM_NRW extern void nvme_log_cmd_message (OPT_HCMD_PTR pCmd);
AOM_NRW_2 void nvme_aer_proc(void);

AOM_NRW_2 extern void nvme_asyn_event_type2_check ( U8 ubErrType);

extern void nvme_lid_err_log_fill_info_api (U8 ubErrByte, U8 ubErrBit, U64 uoErrLba, U64 uoCmdSpecInfo, ERR_LOG_INFO_PTR pErrInfoData); //Callback function flow
extern void nvme_lid_err_log_insert_api (U8 ubCmderr, ERR_LOG_INFO_PTR pErrInfoData);  //Callback function flow
AOM_TCG_COMMON void nvme_unsecure_log_insert_api(U32 ulSecureLog);
AOM_NRW void nvme_get_unsecure_log_api(U32 ulPhyAddr);
AOM_NRW_2 extern void nvme_lid_adjust_offset (U64 uoLPOUL, U32 ulPhyAddr, U64 uoDataSize);
AOM_NRW extern void nvme_lid_error_info_fill (U32 ulPhyAddr);

AOM_HOST_RESET extern void nvme_smart_info_restore_hal_api (SMART_LOG_SAVED_PTR pSmartLog);
#if (LPM_USE_PD0_DBUF_RAM_PD_256KB_FOR_LPM3)
AOM_LPM extern void nvme_smart_info_refresh_hal_api (SMART_LOG_SAVED_PTR pSmartLog);
#else /*(LPM_USE_PD0_DBUF_RAM_PD_256KB_FOR_LPM3)*/
AOM_INIT_2 extern void nvme_smart_info_refresh_hal_api (SMART_LOG_SAVED_PTR pSmartLog);
#endif /*(LPM_USE_PD0_DBUF_RAM_PD_256KB_FOR_LPM3)*/
AOM_NRW_2 extern void nvme_temperature_check_api(U16  uwCurrTemp);
AOM_NRW_2 extern void nvme_media_read_only_check_api(void);
AOM_NRW_2 extern void nvme_lid_smart_info_fill (U32 ulPhyAddr);
AOM_NRW_2 extern void nvme_media_read_only_set_api(U32 ulNs);
AOM_NRW_2 extern void nvme_lid_fw_slot_info_fill (U32 ulPhyAddr);
AOM_NRW_2 extern void nvme_lid_changed_ns_list_insert_api (U32 ulNS);
AOM_NRW_2 extern void nvme_lid_changed_ns_list_info_fill (U32 ulPhyAddr);
AOM_NRW_2 extern void nvme_lid_cmd_effect_info_fill (U32 ulPhyAddr);
AOM_HOST_RESET extern void nvme_lid_dev_self_test_init (DEV_SELF_TEST_SAVED_PTR pSrc);
AOM_NRW_2 extern void nvme_lid_dev_self_test_info_fill (U32 ulPhyAddr);
AOM_HOST_RESET extern void nvme_thi_init(void);
AOM_NRW_2 extern void nvme_thi_set_create_data (U8 ubEnable );
AOM_NRW_2 extern void nvme_lid_telemetry_Initiated_info_fill (U8  ubLID, U32 ulPhyAddr);
AOM_NRW_2 extern void nvme_lid_dev_self_test_stop_process_api (U8 ubStc, U8 ubNSIDbitmap);
AOM_NRW_2 extern void nvme_lid_dev_self_test_update_process_api (U8 ubStc);
AOM_NRW_2 extern void nvme_lid_dev_self_test_update_result_api (U8 ubDstCpl, U8 ubDstResult, U8 ubSegNum, U8 ubValidInfo, U32 ulNsid, U64 uoFailLba, U16 uwSC);
AOM_NRW_2 void nvme_lid_err_clear_lba_field_api (void);
AOM_NRW_2 extern void nvme_lid_sanitize_init_api (void);
AOM_NRW_2 extern void nvme_lid_sanitize_set_gde_api (U8 ubGde);
AOM_HOST_RESET extern void nvme_lid_sanitize_set_fw_active_pend_api (U8 ubFwActivePend);
AOM_NRW_2 extern void nvme_lid_sanitize_update_result_api (U16 uwSprog, U8 ubSaniState, U8 ubOverWCntDone);
AOM_NRW_2 extern void nvme_lid_sanitize_info_fill (U32 ulPhyAddr);
AOM_NRW_2 extern void nvme_async_trigger_check (ASYNC_EVENT_DW0_PTR pEntry);
AOM_NRW_2 extern void nvme_async_insert_and_trigger_check (ASYNC_EVENT_DW0_PTR entry);
AOM_NRW_3 U8 nvme_smart_chk_cri_warn_api (void);
AOM_NRW_2 extern void nvme_smart_set_cri_warn_api (SMART_LOG_SAVED_PTR pSmartLog, U8 ubfield, U8 ubVal);
AOM_NRW_2 extern void nvme_async_evt_mask_type (ASYNC_EVENT_DW0_PTR entry);
AOM_NRW_2 extern void nvme_async_evt_unmask_type (U8  ubLogID);
AOM_NRW_2 extern void  nvme_lid_smart_mess_api (void);
AOM_NRW_2 extern void nvme_set_sensor_threshold_hal_api(U16 uwTempOver, U16 uwTempUnder, U16 uwThermalTMT1, U16 uwThermalTMT2);

#if SUPPORT_PERSISTENT_EVENT_EN
AOM_NRW_2 void nvme_lid_form_persistent_event_header (U32 ulPhyAddr, U8 ubEventType, U16 uwEventStructLength);
#endif

#endif /* _SHR_NVME_LOG_LIB_H_ */
