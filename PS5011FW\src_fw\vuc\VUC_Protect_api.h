#ifndef _VUC_PROTECT_API_H_
#define _VUC_PROTECT_API_H_

#include "hal/security/security_rsa_api.h"

#if PS5017_EN
#define VUC_PROTECT_ENCRYPT_RANDOM_SIZE		(512)
#define VUC_PROTECT_SECUREKEY_DUMMY_RANDOM_LENGTH	(432)
#define VUC_PROTECT_SECURE_KEY_INFO_SIZE	(2048)
#else /* PS5017_EN */
#define VUC_PROTECT_ENCRYPT_RANDOM_SIZE		(256)
#define VUC_PROTECT_SECUREKEY_DUMMY_RANDOM_LENGTH	(180)
#define VUC_PROTECT_SECURE_KEY_INFO_SIZE	(1024)
#endif /* PS5017_EN */
#define VUC_PROTECT_ENCRYPT_CRC_SZIE	(4)
#define VUC_PROTECT_SESSION_KEY_SIZE    (32)
#define VUC_PROTECT_SECUREKEY_KEY_HEADER_LENGTH	(10)
#define VUC_PROTECT_AES_CIPHERBLOCKCHAINING_INITIALIZATIONVECTOR_LENGTH	(4)
#if (HOST_MODE == NVME)
#define VUC_PROTECT_DLMC_KEY_DATASTRUCT_VERSION_FIRST_BYTE	(0x62)
#define VUC_PROTECT_DLMC_KEY_DATASTRUCT_VERSION_SECOND_BYTE	(0x9F)
#else /* (HOST_MODE == NVME) */
#define VUC_PROTECT_DLMC_KEY_DATASTRUCT_VERSION_FIRST_BYTE	(0xC3)
#define VUC_PROTECT_DLMC_KEY_DATASTRUCT_VERSION_SECOND_BYTE	(0x09)
#endif /* (HOST_MODE == NVME) */

#define VUC_PROTECT_CHECK_HEADER_PASS   (2)	//For NRWSATADLMCCheckMPHeader use

typedef enum {
	VUC_PROTECT_VERIFY_STATE_LOCK_DOWN = 0,

	VUC_PROTECT_VERIFY_STATE_PROTECT,
	VUC_PROTECT_VERIFY_STATE_ENGINEERING,

	VUC_PROTECT_VERIFY_STATE_HANDSHAKE_START,
	VUC_PROTECT_VERIFY_STATE_ENCRYPTION_DATA_SENT,
	VUC_PROTECT_VERIFY_STATE_PASS
}
VUCProtectVerifyStateEnum_t;

typedef struct {
	U8 aubHeader[VUC_PROTECT_SECUREKEY_KEY_HEADER_LENGTH]; //PhIsOnNo.1
	U16 uwInfoVersion;

	//0x0C
	U16 uwIdx; // e ~ 1F
	U8 aubRsvd[18];  // e ~ 1F

	//0x20
	RSAPublicKey_t PublicKey;

	//PS5013: 0x32C, PS5017: 0x630
	U8 aubSessionKeyParameter[VUC_PROTECT_SESSION_KEY_SIZE];

	//PS5013: 0x34C, PS5017: 0x650
	U8 aubDummyRandom[VUC_PROTECT_SECUREKEY_DUMMY_RANDOM_LENGTH];
}
SecureKeyInfo_t;
TYPE_SIZE_CHECK(SecureKeyInfo_t, VUC_PROTECT_SECURE_KEY_INFO_SIZE);

typedef struct {
	U8 ubProtectStateDefault;
	U8 ubProtectState;

	U8 ubVerifyFailCount;

	SecureKeyInfo_t *pSecureKeyInfo;
	U8 aubAEncryptRandom[VUC_PROTECT_ENCRYPT_RANDOM_SIZE]; //Points to a data of VUC_PROTECT_ENCRYPT_RANDOM_SIZE

	U32 ulAESCipherBlockChainingInitializayionVector[VUC_PROTECT_AES_CIPHERBLOCKCHAINING_INITIALIZATIONVECTOR_LENGTH];
	U8 aubASessionKey[VUC_PROTECT_SESSION_KEY_SIZE];
}
VUCProtect_t;

#if E21_TODO
typedef struct {
	const U8 *pn0;
	const U8 *pN;
	const U8 *pR;
	const U8 *pRR;
	const U8 *pMsgSHA256;
	const U8 *pMsgSHA512;
	const U8 *pSigSHA256;
	const U8 *pSigSHA512;
	U8 ubEdn0;
	U8 ubEdN;
	U8 ubEdR;
	U8 ubEdRR;
	U8 ubEdMsg;
	U8 ubEdSig;
} RSA_PLUS_KAT_t;

typedef struct {
	RSA_PLUS_KAT_t tData;
	U8 an0[8];
	U8 aN[512];
	U8 aR[512];
	U8 aRR[512];
	U8 aMsgSHA256[128];
	U8 aMsgSHA512[128];
	U8 aSigSHA256[512];
	U8 aSigSHA512[512];
} RSA_4096_KAT_t;
#endif /* E21_TODO */
AOM_NRW_2 U8 VUCProtectCheckMPHeader(U32 ulAddr);
AOM_SECURITY void VUCProtectCheckTimeOut(void);

#endif /*_VUC_PROTECT_API_H_*/
