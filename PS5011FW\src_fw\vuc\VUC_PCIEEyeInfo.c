#include "setup.h"

#if (NVME == HOST_MODE)

#include "nvme_api/nvme/shr_hal_nvme_api.h"
#include "nvme_api/pcie/shr_hal_pcie.h"
#include "host/VUC_handler_api.h"
#include "vuc/VUC_PCIEEyeInfo.h"

#if (!E21_TODO)
void VUC_DoPCIEEyeFlow(VUC_OPT_HCMD_PTR_t pCmd)
{
	/* Record PCIe eye parameters for VUC_GET_PCIE_EYE_INFO cmd */

	if (M_PCIE_CHECK_LANE_PICAL_OFFSET_DONE(pCmd->vuc_sqcmd.vendor.DoPCIEEyeFlow.ubLane)) {
		// Just store target lane not to do eye open here
		gPCIeEyeOpenInfo.ubEyeTargetLane = pCmd->vuc_sqcmd.vendor.DoPCIEEyeFlow.ubLane;
		// Set skip LPM bit to protect gPCIeEyeOpenInfo preservation crossing cmd
		gVUCVar.VUCSkipLPMBMP.btVUCDoPCIEEyeFlow = TRUE;
		// Disable Active State Power Management (ASPM) L1
		M_PCIE_L1_DIS();

		pCmd->ubState = PROCESS_ALL_DONE;
	}
	else {
		// Set lane to default value
		gPCIeEyeOpenInfo.ubEyeTargetLane = VUC_TARGET_LANE_NOT_SET;

		gVUCVar.ubVucCmdErrStatus = HOST_ERROR_PARAMETER;
		pCmd->ubState = CMD_ERROR;
	}

}

void VUC_GetPCIEEyeInfo(VUC_OPT_HCMD_PTR_t pCmd)
{
	/* Do PCIe eye data and return to Host */

	U8 ubGetEyeStatus = PCIE_GET_EYE_FAIL;

	// Need to do VUC_DO_PCIE_EYE_FLOW cmd to setup target lane first
	if (VUC_TARGET_LANE_NOT_SET != gPCIeEyeOpenInfo.ubEyeTargetLane) {
		// Real do eye open
		ubGetEyeStatus = PCIEGetEyeByLane(PCIE_GET_EYE_VUC_MODE, gPCIeEyeOpenInfo.ubEyeTargetLane, pCmd->ulCurrentPhysicalMemoryAddr);
		// Clear target lane
		gPCIeEyeOpenInfo.ubEyeTargetLane = VUC_TARGET_LANE_NOT_SET;
	}

	// Check status
	if (PCIE_GET_EYE_SUCCESS == ubGetEyeStatus) {
		pCmd->ubState = FW_PROCESS_DONE;
	}
	else {
		gVUCVar.ubVucCmdErrStatus = HOST_ERROR_CMD_FAIL;
		pCmd->ubState = CMD_ERROR;
	}

	// Clear skip LPM bit after cmd finish
	gVUCVar.VUCSkipLPMBMP.btVUCDoPCIEEyeFlow = FALSE;

}
#endif /*(!E21_TODO)*/
#endif /* (NVME == HOST_MODE) */

