/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include <string.h>
#include "setup.h"
#include "typedef.h"
#include "log/log_api.h"
#include "table/initinfo_vt/initinfo_vt.h"
/*
 * ---------------------------------------------------------------------------------------------------
 *   extern global variables
 * ---------------------------------------------------------------------------------------------------
 */
LogVariable_t gubLogConditionMap = {0};

/*
 * ---------------------------------------------------------------------------------------------------
 *   public
 * ---------------------------------------------------------------------------------------------------
 */

void LogInit(void)
{
#if(LOG_EN)
	U32 ulIdx;
	for (ulIdx = 0; ulIdx < LOG_CASE_CNT; ulIdx++) {
		gpVTDBUF->ubCorner[ulIdx] = 0;
	}
#endif /*LOG_EN*/
}

void LogAdd(U32 ulTag)
{
#if(LOG_EN)
	if (BIT_MASK(8) > gpVTDBUF->ubCorner[ulTag]) {
		++gpVTDBUF->ubCorner[ulTag];
	}
#endif /*LOG_EN*/
}
