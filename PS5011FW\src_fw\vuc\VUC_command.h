#ifndef _HOST_VUC_COMMAND_H_
#define _HOST_VUC_COMMAND_H_

#include "symbol.h"
#include "typedef.h"
#include "init/fw_preformat.h"
#include "hal/pic/uart/uart_api.h"
#include "hal/pic/uart/uart.h"
#include "hal/db/db_reg.h"
#include "common/fw_common.h"
#include "burner/Burner_Def.h"
#include "burner/Burner.h"
#include "hal/nvme/nvme_api.h"

/* System Area Information */
#define CODE_BLOCK_NUMBER		(8)			//1 TODO, Code block is varay, it is channel * 2
#define DBT_BLOCK_NUMBER		(2)
#define	SYSTEM_TABLE_NUMBER		(2)
#define CODE_BLOCK_PER_CH		(2)

#define ERASEALL_RESERVED_GCE	(BIT0)
#define ERASEALL_RESERVED_BLOCK	(BIT1)

#define	DEF_DBT_CHECKED	(BIT0)
#define DEF_DBT_FOUNDED	(BIT1)

#define BYTE_PER_DW			(4)

#define VUC_SYS_INFO_MP_MARK	(0x5A)

/* Vendor Unique Command -- Feature (@Byte 48) */
/// No-Data VUC
#define VUC_AP_KEY                  (0x00)
#define VUC_AP_RESET                (0x01)
#define VUC_ISP_JUMP                (0x02)
#define VUC_SET_SPECIFIC_GLOBAL_VALUE	(0x07)
#define VUC_ERASE_ALL               (0x08)
#define VUC_ERASE_BLOCK             (0x09)
#define VUC_ERASE_PCA               (0x0A)
#define VUC_CLEAR_SMART             (0x0B)
#define VUC_MAKE_ERROR	            (0x0C)
#define VUC_DDR_TRAINING            (0x0D)

#define VUC_MODULE_TEST             (0x0F)        // for module test
#define VUC_DLMC_PREFORMAT			(0x45)
#define VUC_SET_SCAN_WINDOW_PARAMETER       (0x60)
#define VUC_FIND_DDR_WINDOW         (0x61)
#define VUC_SCAN_RDT_LOG            (0xF3)

#define VUC_SEND_HANDSHAKE_REQUEST  (0xC4)
#define VUC_DISABLE_VUC    			(0xC7)
#define VUC_DO_PCIE_EYE_FLOW        (0xEE)

/// Data-In VUC
#define VUC_DISABLE_SEC_FROZEN_LOCK (0x05)
#define VUC_PREFORMAT               (0x10)
#define VUC_WRITE_VRLC              (0x11)
#define VUC_ISP_PRAM                (0x12)
#define VUC_WRITE_SRAM              (0x20)
#define VUC_WRITE_REG               (0x22)
#define VUC_ISP_FLASH               (0x30)
#define VUC_ISP_ROM                 (0x31)
#define VUC_WRITE_INFO              (0x32)
#define VUC_WRITE_PH   				(0x33)
#define VUC_KINGSTON_ANTI_FAKE_DATA (0x35)
#define VUC_WRITE_AP_DBT            (0x36)
#define VUC_PROG_PAGE               (0x40)
#define VUC_PROG_PCA                (0x41)
#define VUC_CACHE_PROG              (0x42)
#define VUC_NAND_VERIFY_SET         (0x50)
#define VUC_NAND_VERIFY_WRITE       (0x51)
#define VUC_NAND_VERIFY_TRIGGER     (0x52)
#define VUC_SCAN_FLASH_SETTING      (0x53)
#define VUC_DOWNLOAD_RETRY_TABLE    (0x55)
#define VUC_SET_SMART_VALUE			(0x66)
#define VUC_SET_SMART_UPDATE_RATIO	(0x68)
#define VUC_SEND_ENCRYPTION_DATA    (0xC6)

/// Data-Out VUC
#define VUC_SEARCH_SYS_BLOCK        (0x70)
#define VUC_READ_SYS_INFO           (0x80)
#define VUC_DUMP_TABLE              (0x81)
#define VUC_PCA_TRANSLATE           (0x82)
#define VUC_GET_BLOCK_STATUS        (0x88)
#define VUC_GET_SMART               (0x89)
#define VUC_GET_RMA_INFO			(0x8D)
#define VUC_GET_FCE_CONFIG          (0x8E)
#define VUC_GET_FLASH_INFO        	(0x90)
#define VUC_GET_TRIM_TABLE          (0x91)
#define VUC_DUMP_PRAM               (0x92)
#define VUC_READ_SRAM               (0xA0)
#define VUC_READ_REG                (0xA2)
#define VUC_DUMP_CODE               (0xB0)
#define VUC_READ_INFO               (0xB1)
#define VUC_READ_PH    				(0xB2)
#define VUC_READ_PAGE               (0xC0)
#define VUC_READ_PCA                (0xC1)
#define VUC_CACHE_READ              (0xC2)
#define VUC_RECEIEVE_ENCRYPTION_DATA	(0xC5)
#define VUC_NAND_VERIFY_GET         (0xD0)
#define VUC_NAND_VERIFY_READ        (0xD1)
#define VUC_NAND_SCAN_WINDOW_READ   (0xD2)
#define VUC_READ_DDR_WINDOW         (0xD3)
#define VUC_GET_STATUS              (0xE0)
#define VUC_GET_DATA_SIZE			(0XE1)
#define VUC_GET_PCIE_EYE_INFO       (0xEF)
#define VUC_GET_RDT_LOG             (0xF1)

///	Data-Mix VUC
#define VUC_CUT_EFUSE               (0x03)
#define VUC_FLASH_FEATURE_CONFIGURE	(0x16)
#define VUC_FW_FEATURE_CTRL			(0x85)
#define VUC_TCG_PSID				(0xA6)
#define VUC_DETECT_PERIPHERAL       (0xF2)
#define VUC_READ_AP_DBT             (0xF6)
#if (HOST_MODE == NVME)
#define VUC_SECURITY_PASS_THROUGH	(0xFF)
#endif

/* VUC SubFeature */
#define VUC_SP_SUBFEA_GET_LOG_PAGE	(0x02)
#define VUC_SP_SUBFEA_IDENTIFY		(0x06)
#define VUC_DETECT_TXOPEN_RXSHORT	(0xF8)

///	Data-Mix VUC
#define VUC_SIDEBAND_IO_TEST	(0xF7)
#if (MICRON_FSP_EN)

#define VUC_MICRON_COMMON_VS_COMMANDS	(0x02)
#define 	VUC_MICRON_CLEAR_EVENT_LOG			(0x04)
#define 	VUC_MICRON_GET_DRIVE_CONFIGURE		(0x06)
#define 	VUC_MICRON_GET_L2P					(0x0D)
#define 	VUC_MICRON_GET_P2L					(0x0E)
#define 	VUC_MICRON_GET_ALL_TEMPERATURE		(0x11)

#define VUC_MICRON_NAND_VS_COMMANDS		(0x04)

#define 	VUC_MICRON_GET_RAW_BIT_ERROR_COUNT			(0x02)
#define 	VUC_MICRON_GET_BLOCK_NAND_MODE			    (0x03)
#define 	VUC_MICRON_GET_DEFECT_LIST					(0x0C)
#define 	VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS	(0x0F)
#define 	VUC_MICRON_GET_NAND_BLOCK_ERASE_COUNT		(0x10)
#define 	VUC_MICRON_GNPT								(0x16)
#define 	VUC_MICRON_SET_MLBI			                (0x18)
#define 	VUC_MICRON_GET_MLBI			                (0x19)
#define 	VUC_MICRON_GET_VT_SWEEP						(0x2A)
#define 	VUC_MICRON_GET_BEC							(0x38)
#define 	VUC_MICRON_GET_UNIFIED_EVENT_LOG			(0x3B)

#endif /*(MICRON_FSP_EN)*/

#if (RDT_RUN_ONLINE)
#define	VUC_RDT_ONLINE_PASS_THROUGH                (0x77)
#endif

#if (USB == HOST_MODE)
/* VUC Command */
#define USB_VUC_SUB_OP_RESERVED             (0x00U)
#define READ_INFO                           (0x05U)
#define UPDATE_INFO							(0x06U)
#define GET_FLASH_ID						(0x56U)
#define USB_FORCE_WRTIE_PROTECT				(0x31U)
#define ReMP_ISP_JUMP						(0xB3U)

/* USB_VUC_SUB_OP_RESERVED, 0x00 */
#define CHANGE_SPEED                        (0xCDU)
#define USB_VUC_DUMP_TABLE                  (0xFDU)

/* USB_VUC_SUB_OP_RESERVED, 0x00 */
#define GET_SMART_INFO                      (0x89U)
#define GET_FLASH_INFO                      (0x90U)
#define DIRECT_READ_INFO_BLOCK              (0xB1U)
#define CHANGE_SPEED                        (0xCDU)
#define USB_VUC_DUMP_TABLE                  (0xFDU)

/*GET_SMART_INFO, 0x89*/
#define VUC_GET_NVME_SMART					(0x01U)
#define VUC_GET_SMART_ATTRIBUTE				(0x02U)

/*Dump Table, USB_VUC_DUMP_TABLE, 0xFD*/
#define VUC_DUMP_ECTABLE					(0x00U)
#define VUC_DUMP_VCTABLE					(0x01U)
#define VUC_DUMP_SYSINFO					(0x02U)
#define VUC_DUMP_VBRMP						(0x03U)
#define VUC_GET_PCARULE						(0x04U)
#define VUC_DUMP_POR_PARAMETER              (0x06U)
#define VUC_DUMP_BADBLOCKTABLE              (0x08U)
#define VUC_DUMP_DRIVELOG					(0x09U)
#define VUC_DUMP_VT_RAM						(0x10U)
#define VUC_DUMP_VT_VTDBUF_RAM				(0x11U)
#define VUC_READ_VT_PAGE					(0x12U)
#define VUC_DUMP_ID_PAGE					(0x13U)
#define VUC_DUMP_PAD_PAGE					(0x14U)
#define VUC_DUMP_RAM		       	        (0x15U)
#define VUC_DUMP_Efuse		       	        (0x16U)
#define VUC_Get_VRLC_TABLE	       	        (0x17U)
#define VUC_DUMP_RUT_RAM	       	        (0x18U)
#endif /* (USB == HOST_MODE) */

#endif //_HOST_VUC_COMMAND_H_
