#ifndef _RW_MGR_H_
#define _RW_MGR_H_
#include "typedef.h"

//***************Lock Write Fifo***************
#define LOCK_FIFO_FAIL					(0)
#define LOCK_FIFO_PASS					(1)
#define SATA_RW_TIMER_THRESHOLD			(500000) //micro sec
#define SATA_RW_TIMER_THRESHOLD_10s		(10000000) //micro sec
#define SATA_RW_TIMER_THRESHOLD_10mins	(600000000) //micro sec
#define SATA_RW_TIMER_THRESHOLD_1hr		(3600000000) //micro sec

//***************Read Write Mode***************
#define RW_MODE_READ					(0)
#define RW_MODE_WRITE					(1)

typedef enum LockWFIFOStateEnum {
	U8_LOCK_WFIFO_INIT,
	U8_LOCK_WFIFO,
	U8_WAIT_LOCK_WFIFO_DONE,
	U8_WAIT_WR_REMAIN4K_EMPTY,
	U8_FLUSH_WCACHE,
	U8_WAIT_FLUSH_WCACHE,
	U8_WRITE_STOP,
} LockWFIFOStateEnum_t;

typedef enum LockRFIFOStateEnum {
	HOST_HANDLER_LOCK_READ_FIFO_INIT,
	HOST_HANDLER_LOCK_READ_FIFO,
	HOST_HANDLER_WAIT_LOCK_READ_FIFO_DONE,
	HOST_HANDLER_WAIT_READ_REMAIN4K_EMPTY,
	HOST_HANDLER_WAIT_MR_RCQ_PATH_EMPTY,
	HOST_HANDLER_WAIT_RCQ_EMPTY,
	HOST_HANDLER_CHECK_IOT_CTAG,
	HOST_HANDLER_WAIT_READ_CACHE_EMPTY,
	HOST_HANDLER_READ_STOP,
} LockRFIFOStateEnum_t;

typedef struct {
	U64 uoLockFIFOTimer;	//start time @ms
	U8 btLockWFIFO	: 1;
	U8 btLockRFIFO	: 1;
	U8 btRWMode		: 1; //0: Read, 1: Write
	U8 btModeActive	: 1; //btRWMode is valid when btModeActive is True
	U8 btChangeMode	: 1;
	U8 btCheckChange: 1;
	U8 ubRes		: 2;
	LockWFIFOStateEnum_t LockWriteFIFO;
	LockRFIFOStateEnum_t LockReadFIFO;
} ReadWriteMgr;

void FTLCheckChangeRWMode(void);

#endif
