#ifndef _SYS_PD1_REG_5021_H_
#define _SYS_PD1_REG_5021_H_

#include "typedef.h"
#include "mem.h"
#include "symbol.h"

/*
 *  +--------------------------------------------------------------------+
 *  |					System PD1 Register                              |
 *  | 				Offset:0xF8007000 ~ 0xF8007FFF                       |
 *  |--------------------------------------------------------------------|
 *  | GPIO Control       	 | 0x000 ~ 0x01C | SYS1_GPIO_CTRL_BASE       |
 *  | PWR Control      	     | 0x020 ~ 0x024 | SYS1_PWR_CTRL_BASE        |
 *  | SRAM Control           | 0x040 ~ 0x040 | SYS1_SRAM_CTRL_BASE       |
 *  | PCIE PERSTN Control    | 0x090 ~ 0x09C | SYS1_PCIE_PERSTN_BASE     |
 *  | GPIO DEGLITCH Control  | 0x0C0 ~ 0x0CC | SYS1_GPIO_DEGLITCH_BASE   |
 *  | FLASH PAD Control      | 0x100 ~ 0x170 | SYS1_FLH_PAD_CTRL_BASE    |
 *  | FLASH PHY PAD Control  | 0x180 ~ 0x1CC | SYS1_FLH_PHY_PAD_CTRL_BASE|
 *  | FLASH PAD Control      | 0x200 ~ 0x240 | SYS1_FLH_PAD_CTRL_BASE    |
 *  | FLASH PHY OSC Control  | 0x284 ~ 0x2B8 | SYS1_FLH_PHY_OSC_CTRL_BASE|
 *  | INTERRUPT Control      | 0x300 ~ 0x30C | SYS1_INTERRUPT_CTRL_BASE  |
 *  | TIMER Control          | 0x340 ~ 0x364 | SYS1_TIMER_CTR_BASE       |
 *  | ANALOG Control         | 0x398 ~ 0x3AC | SYS1_ANALOG_CTRL_BASE     |
 *  | PAD1 IP Control        | 0x400 ~ 0x5E0 | SYS1_IP_CTRL_BASE         |
 *  | PD1 MISC               | 0x600 ~ 0x63C | SYS1_MISC_CTRL_BASE       |
 *  +--------------------------------------------------------------------+
 */


/*
 *  +-----------------------------------------------------------------------+
 *  |					GPIO Control      								    |
 *  +-----------------------------------------------------------------------+
 */
#define SYS1_GPIO_CTRL_BASE                         	(SYSTEM_PD1_REG_ADDRESS)

#define R8_SYS1_GPIO                                	((REG8  *) SYS1_GPIO_CTRL_BASE)
#define R16_SYS1_GPIO                               	((REG16 *) SYS1_GPIO_CTRL_BASE)
#define R32_SYS1_GPIO                               	((REG32 *) SYS1_GPIO_CTRL_BASE)
#define R64_SYS1_GPIO                               	((REG64 *) SYS1_GPIO_CTRL_BASE)

#define R32_SYS1_SYS_INT_TYPE							(0x004 >> 2)
#define 	CR_GPIO_PLN_TRIG_TYPE_SHIFT					(0)
#define 	CR_GPIO_PLN_TRIG_TYPE_MASK					(BIT_MASK(4))
#define 	CR_GPIO_PLN_TRIG_TYPE						(CR_GPIO_PLN_TRIG_TYPE_MASK << CR_GPIO_PLN_TRIG_TYPE_SHIFT)
#define 	CR_GPIO_PWRDIS_TRIG_TYPE_SHIFT				(4)
#define 	CR_GPIO_PWRDIS_TRIG_TYPE_MASK				(BIT_MASK(4))
#define     CR_GPIO_PWRDIS_TRIG_TYPE					(CR_GPIO_PWRDIS_TRIG_TYPE_MASK << CR_GPIO_PWRDIS_TRIG_TYPE_SHIFT)

#define R32_SYS1_SYS_INT_STS							(0x008 >> 2)
#define 	CR_GPIO_PLN_STS_SHIFT						(0)
#define 	CR_GPIO_PLN_STS_MASK						(BIT_MASK(4))
#define 	CR_GPIO_PLN_STS								(CR_GPIO_PLN_STS_MASK << CR_GPIO_PLN_STS_SHIFT)
#define 	CR_GPIO_PWRDIS_STS_SHIFT					(4)
#define 	CR_GPIO_PWRDIS_STS_MASK						(BIT_MASK(4))
#define 	CR_GPIO_PWRDIS_STS							(CR_GPIO_PWRDIS_STS_MASK << CR_GPIO_PWRDIS_STS_SHIFT)

#define R32_SYS1_GPIO_INT_SET                          	(0x00C >> 2)
#define 	GPIO_TRIG_EVENT_SHIFT						(0)
#define 	GPIO_TRIG_EVENT_MASK						(BIT_MASK(18))
#define 	GPIO_TRIG_EVENT                             (GPIO_TRIG_EVENT_MASK << GPIO_TRIG_EVENT_SHIFT)    // W1C

#define R64_SYS1_GPIO_INT_TYPE 	                        (0x010 >> 3)
#define R32_SYS1_GPIO_INT_TYPE1    	                    (0x010 >> 2)
#define 	GPIO00_TRIG_TYPE_SHIFT						(0)
#define 	GPIO00_TRIG_TYPE_MASK						(BIT_MASK(4))
#define 	GPIO00_TRIG_TYPE                            (GPIO00_TRIG_TYPE_MASK << GPIO00_TRIG_TYPE_SHIFT)
#define 	GPIO01_TRIG_TYPE_SHIFT						(4)
#define 	GPIO01_TRIG_TYPE_MASK						(BIT_MASK(4))
#define 	GPIO01_TRIG_TYPE                            (GPIO01_TRIG_TYPE_MASK << GPIO01_TRIG_TYPE_SHIFT)
#define 	GPIO02_TRIG_TYPE_SHIFT						(8)
#define 	GPIO02_TRIG_TYPE_MASK						(BIT_MASK(4))
#define 	GPIO02_TRIG_TYPE                            (GPIO02_TRIG_TYPE_MASK << GPIO02_TRIG_TYPE_SHIFT)
#define 	GPIO03_TRIG_TYPE_SHIFT						(12)
#define 	GPIO03_TRIG_TYPE_MASK						(BIT_MASK(4))
#define 	GPIO03_TRIG_TYPE                            (GPIO03_TRIG_TYPE_MASK << GPIO03_TRIG_TYPE_SHIFT)
#define 	GPIO04_TRIG_TYPE_SHIFT						(16)
#define 	GPIO04_TRIG_TYPE_MASK						(BIT_MASK(4))
#define 	GPIO04_TRIG_TYPE                            (GPIO04_TRIG_TYPE_MASK << GPIO04_TRIG_TYPE_SHIFT)
#define 	GPIO05_TRIG_TYPE_SHIFT						(20)
#define 	GPIO05_TRIG_TYPE_MASK						(BIT_MASK(4))
#define 	GPIO05_TRIG_TYPE                            (GPIO05_TRIG_TYPE_MASK << GPIO05_TRIG_TYPE_SHIFT)
#define 	GPIO06_TRIG_TYPE_SHIFT						(24)
#define 	GPIO06_TRIG_TYPE_MASK						(BIT_MASK(4))
#define 	GPIO06_TRIG_TYPE                            (GPIO06_TRIG_TYPE_MASK << GPIO06_TRIG_TYPE_SHIFT)
#define 	GPIO07_TRIG_TYPE_SHIFT						(28)
#define 	GPIO07_TRIG_TYPE_MASK						(BIT_MASK(4))
#define 	GPIO07_TRIG_TYPE                            (GPIO07_TRIG_TYPE_MASK << GPIO07_TRIG_TYPE_SHIFT)

#define R32_SYS1_GPIO_INT_TYPE2                        	(0x014 >> 2)
#define 	GPIO08_TRIG_TYPE_SHIFT						(0)
#define 	GPIO08_TRIG_TYPE_MASK						(BIT_MASK(4))
#define 	GPIO08_TRIG_TYPE                            (GPIO08_TRIG_TYPE_MASK << GPIO08_TRIG_TYPE_SHIFT)
#define 	GPIO09_TRIG_TYPE_SHIFT						(4)
#define 	GPIO09_TRIG_TYPE_MASK						(BIT_MASK(4))
#define 	GPIO09_TRIG_TYPE                            (GPIO09_TRIG_TYPE_MASK << GPIO09_TRIG_TYPE_SHIFT)
#define 	GPIO10_TRIG_TYPE_SHIFT						(8)
#define 	GPIO10_TRIG_TYPE_MASK						(BIT_MASK(4))
#define 	GPIO10_TRIG_TYPE                            (GPIO10_TRIG_TYPE_MASK << GPIO10_TRIG_TYPE_SHIFT)
#define 	GPIO11_TRIG_TYPE_SHIFT						(12)
#define 	GPIO11_TRIG_TYPE_MASK						(BIT_MASK(4))
#define 	GPIO11_TRIG_TYPE                            (GPIO11_TRIG_TYPE_MASK << GPIO11_TRIG_TYPE_SHIFT)
#define 	GPIO12_TRIG_TYPE_SHIFT						(16)
#define 	GPIO12_TRIG_TYPE_MASK						(BIT_MASK(4))
#define 	GPIO12_TRIG_TYPE                            (GPIO12_TRIG_TYPE_MASK << GPIO12_TRIG_TYPE_SHIFT)
#define 	GPIO13_TRIG_TYPE_SHIFT						(20)
#define 	GPIO13_TRIG_TYPE_MASK						(BIT_MASK(4))
#define 	GPIO13_TRIG_TYPE                            (GPIO13_TRIG_TYPE_MASK << GPIO13_TRIG_TYPE_SHIFT)
#define 	GPIO14_TRIG_TYPE_SHIFT						(24)
#define 	GPIO14_TRIG_TYPE_MASK						(BIT_MASK(4))
#define 	GPIO14_TRIG_TYPE                            (GPIO14_TRIG_TYPE_MASK << GPIO14_TRIG_TYPE_SHIFT)
#define 	GPIO15_TRIG_TYPE_SHIFT						(28)
#define 	GPIO15_TRIG_TYPE_MASK						(BIT_MASK(4))
#define 	GPIO15_TRIG_TYPE                            (GPIO15_TRIG_TYPE_MASK << GPIO15_TRIG_TYPE_SHIFT)

#define R64_SYS1_GPIO_INT_STS                         	(0x018 >> 3)
#define R32_SYS1_GPIO_INT_STS1                         	(0x018 >> 2)
#define 	GPIO00_TRIG_STS_SHIFT						(0)
#define 	GPIO00_TRIG_STS_MASK						(BIT_MASK(4))
#define 	GPIO00_TRIG_STS                             (GPIO00_TRIG_STS_MASK << GPIO00_TRIG_STS_SHIFT)
#define 	GPIO01_TRIG_STS_SHIFT						(4)
#define 	GPIO01_TRIG_STS_MASK						(BIT_MASK(4))
#define 	GPIO01_TRIG_STS                             (GPIO01_TRIG_STS_MASK << GPIO01_TRIG_STS_SHIFT)
#define 	GPIO02_TRIG_STS_SHIFT						(8)
#define		GPIO02_TRIG_STS_MASK						(BIT_MASK(4))
#define 	GPIO02_TRIG_STS                             (GPIO02_TRIG_STS_MASK << GPIO02_TRIG_STS_SHIFT)
#define 	GPIO03_TRIG_STS_SHIFT						(12)
#define 	GPIO03_TRIG_STS_MASK						(BIT_MASK(4))
#define 	GPIO03_TRIG_STS                             (GPIO03_TRIG_STS_MASK << GPIO03_TRIG_STS_SHIFT)
#define 	GPIO04_TRIG_STS_SHIFT						(16)
#define 	GPIO04_TRIG_STS_MASK						(BIT_MASK(4))
#define 	GPIO04_TRIG_STS                             (GPIO04_TRIG_STS_MASK << GPIO04_TRIG_STS_SHIFT)
#define 	GPIO05_TRIG_STS_SHIFT						(20)
#define 	GPIO05_TRIG_STS_MASK						(BIT_MASK(4))
#define 	GPIO05_TRIG_STS                             (GPIO05_TRIG_STS_MASK << GPIO05_TRIG_STS_SHIFT)
#define 	GPIO06_TRIG_STS_SHIFT						(24)
#define 	GPIO06_TRIG_STS_MASK						(BIT_MASK(4))
#define 	GPIO06_TRIG_STS                             (GPIO06_TRIG_STS_MASK << GPIO06_TRIG_STS_SHIFT)
#define 	GPIO07_TRIG_STS_SHIFT						(28)
#define 	GPIO07_TRIG_STS_MASK						(BIT_MASK(4))
#define 	GPIO07_TRIG_STS                             (GPIO07_TRIG_STS_MASK << GPIO07_TRIG_STS_SHIFT)

#define R32_SYS1_GPIO_INT_STS2                         	(0x01C >> 2)
#define 	GPIO08_TRIG_STS_SHIFT                   	(0)
#define 	GPIO08_TRIG_STS_MASK                    	(BIT_MASK(4))
#define 	GPIO08_TRIG_STS								(GPIO08_TRIG_STS_MASK << GPIO08_TRIG_STS_SHIFT)
#define 	GPIO09_TRIG_STS_SHIFT               	    (4)
#define 	GPIO09_TRIG_STS_MASK                 	    (BIT_MASK(4))
#define 	GPIO09_TRIG_STS								(GPIO09_TRIG_STS_MASK << GPIO09_TRIG_STS_SHIFT)
#define 	GPIO10_TRIG_STS_SHIFT               	    (8)
#define 	GPIO10_TRIG_STS_MASK                   		(BIT_MASK(4))
#define 	GPIO10_TRIG_STS								(GPIO10_TRIG_STS_MASK << GPIO10_TRIG_STS_SHIFT)
#define 	GPIO11_TRIG_STS_SHIFT                  		(12)
#define 	GPIO11_TRIG_STS_MASK                    	(BIT_MASK(4))
#define 	GPIO11_TRIG_STS								(GPIO11_TRIG_STS_MASK << GPIO11_TRIG_STS_SHIFT)
#define 	GPIO12_TRIG_STS_SHIFT                   	(16)
#define 	GPIO12_TRIG_STS_MASK                    	(BIT_MASK(4))
#define 	GPIO12_TRIG_STS								(GPIO12_TRIG_STS_MASK << GPIO12_TRIG_STS_SHIFT)
#define 	GPIO13_TRIG_STS_SHIFT                   	(20)
#define 	GPIO13_TRIG_STS_MASK                    	(BIT_MASK(4))
#define 	GPIO13_TRIG_STS								(GPIO13_TRIG_STS_MASK << GPIO13_TRIG_STS_SHIFT)
#define 	GPIO14_TRIG_STS_SHIFT                   	(24)
#define 	GPIO14_TRIG_STS_MASK                    	(BIT_MASK(4))
#define 	GPIO14_TRIG_STS								(GPIO14_TRIG_STS_MASK << GPIO14_TRIG_STS_SHIFT)
#define 	GPIO15_TRIG_STS_SHIFT                   	(28)
#define 	GPIO15_TRIG_STS_MASK                    	(BIT_MASK(4))
#define 	GPIO15_TRIG_STS								(GPIO15_TRIG_STS_MASK << GPIO15_TRIG_STS_SHIFT)

/*
 *  +-----------------------------------------------------------------------+
 *  |					PWR Control      								    |
 *  +-----------------------------------------------------------------------+
 */
#define SYS1_PWR_CTRL_BASE                          	(SYSTEM_PD1_REG_ADDRESS)

#define R8_SYS1_PWR_CTRL                            	((REG8  *) SYS1_PWR_CTRL_BASE)
#define R16_SYS1_PWR_CTRL                           	((REG16 *) SYS1_PWR_CTRL_BASE)
#define R32_SYS1_PWR_CTRL                           	((REG32 *) SYS1_PWR_CTRL_BASE)
#define R64_SYS1_PWR_CTRL                           	((REG64 *) SYS1_PWR_CTRL_BASE)

#define R32_SYS1_PWR_FIP_DCC_CTRL1                     	(0x020 >> 2)
#define R16_SYS1_PWR_DCTL_FW_FLH_CTRL_EN               	(0x020 >> 1)
#define 	CR_DCTL_FW_FLH_CTRL_EN_SHIFT                (0)
#define 	CR_DCTL_FW_FLH_CTRL_EN_MASK                 (BIT_MASK(4))
#define 	CR_DCTL_FW_FLH_CTRL_EN                      (CR_DCTL_FW_FLH_CTRL_EN_MASK << CR_DCTL_FW_FLH_CTRL_EN_SHIFT)
#define 	FIP_FLH_DCC_CFG_SHIFT                       (16)
#define 	FIP_FLH_DCC_CFG_MASK                        (BIT_MASK(12))
#define 	FIP_FLH_DCC_CFG                        		(FIP_FLH_DCC_CFG_MASK << FIP_FLH_DCC_CFG_SHIFT)

#define R32_SYS1_PWR_FIP_DCC_CTRL2                     	(0x024 >> 2)
#define 	CR_DCTL_FW_ECC_CTRL_EN_BIT                  (BIT0)
#define 	FIP_ECC_DCC_CFG_SHIFT						(8)
#define 	FIP_ECC_DCC_CFG_MASK						(BIT_MASK(3))
#define 	FIP_ECC_DCC_CFG                             (FIP_ECC_DCC_CFG_MASK << FIP_ECC_DCC_CFG_SHIFT)
#define 	CR_DCTL_FW_SYS_CTRL_EN_BIT                  (BIT16)
#define 	FIP_SYS_DCC_CFG_SHIFT                       (24)
#define 	FIP_SYS_DCC_CFG_MASK                        (BIT_MASK(3))
#define 	FIP_SYS_DCC_CFG                             (FIP_SYS_DCC_CFG_MASK << FIP_SYS_DCC_CFG_SHIFT)

/*
 *  +-----------------------------------------------------------------------+
 *  |					SRAM Control      								    |
 *  +-----------------------------------------------------------------------+
 */
#define SYS1_SRAM_CTRL_BASE                         	(SYSTEM_PD1_REG_ADDRESS)

#define R8_SYS1_SRAM_CTRL                           	((REG8  *) SYS1_SRAM_CTRL_BASE)
#define R16_SYS1_SRAM_CTRL                          	((REG16 *) SYS1_SRAM_CTRL_BASE)
#define R32_SYS1_SRAM_CTRL                          	((REG32 *) SYS1_SRAM_CTRL_BASE)
#define R64_SYS1_SRAM_CTRL                          	((REG64 *) SYS1_SRAM_CTRL_BASE)

#define R32_SYS1_ATCM_CTRL                             	(0x40 >> 2)
#define		ATCM_WDET_EN_BIT                            (BIT0)

/*
 *  +-----------------------------------------------------------------------+
 *  |					PCIE PERSTN Control      							|
 *  +-----------------------------------------------------------------------+
 */
#define SYS1_PCIE_PERSTN_BASE	                        (SYSTEM_PD1_REG_ADDRESS)

#define R8_SYS1_PCIE_PERSTN_CTRL    	                ((REG8  *) SYS1_PCIE_PERSTN_BASE)
#define R16_SYS1_PCIE_PERSTN_CTRL      	                ((REG16 *) SYS1_PCIE_PERSTN_BASE)
#define R32_SYS1_PCIE_PERSTN_CTRL          	            ((REG32 *) SYS1_PCIE_PERSTN_BASE)
#define R64_SYS1_PCIE_PERSTN_CTRL              	        ((REG64 *) SYS1_PCIE_PERSTN_BASE)

#define R32_SYS1_SYS_PERSTN_INT_SET					  	(0x090 >> 2)
#define 	CR_PERSTN_INT_MSK_SHIFT			  			(0)
#define 	CR_PERSTN_INT_MSK_MASK						(BIT_MASK(2))
#define 	CR_PERSTN_INT_MSK							(CR_PERSTN_INT_MSK_MASK << CR_PERSTN_INT_MSK_SHIFT)
#define 	CR_PERSTN_INT_STS_SHIFT				  		(16)
#define 	CR_PERSTN_INT_STS_MASK				  		(BIT_MASK(2))
#define 	CR_PERSTN_INT_STS							(CR_PERSTN_INT_STS_MASK << CR_PERSTN_INT_STS_SHIFT)

#define R32_SYS1_SYS_PERSTN_DGTH_STS				  	(0x094 >> 2) // RO
#define     CR_PERSTN_R_DGTH_DONE_BIT					(BIT0)
#define     CR_PERSTN_F_DGTH_DONE_BIT					(BIT1)

#define SYS0B_PCIE_DGTH_CFG_PERSTN_R                    (0x098 >> 0)
#define 	PERSTN_R_DGTH_SEL_SHIFT				  		(0)
#define 	PERSTN_R_DGTH_SEL_MASK				  		(BIT_MASK(2))
#define 	PERSTN_R_DGTH_SEL							(PERSTN_R_DGTH_SEL_MASK << PERSTN_R_DGTH_SEL_SHIFT)
#define 	PERSTN_R_DEGLITCH_0_2_US                    (0x0)
#define 	PERSTN_R_DEGLITCH_2_5_US                    (0x1)
#define 	PERSTN_R_DEGLITCH_40_0_US                   (0x2)
#define 	PERSTN_R_DEGLITCH_102_0_US                  (0x3)
#define SYS0B_PCIE_DGTH_CFG_PERSTN_F                    (0x099 >> 0)
#define 	PERSTN_F_DGTH_SEL_SHIFT				  		(0)
#define 	PERSTN_F_DGTH_SEL_MASK				  		(BIT_MASK(2))
#define 	PERSTN_F_DGTH_SEL							(PERSTN_F_DGTH_SEL_MASK << PERSTN_F_DGTH_SEL_SHIFT)
#define 	PERSTN_F_DEGLITCH_0_2_US                    (0x0)
#define 	PERSTN_F_DEGLITCH_2_5_US                    (0x1)
#define 	PERSTN_F_DEGLITCH_40_0_US                   (0x2)
#define 	PERSTN_F_DEGLITCH_102_0_US                  (0x3)

#define R32_SYS1_SYS_PERSTN_DGTH_CTRL					(0x09C >> 2)
#define 	CR_PERSTN_R_DGTH_EN_BIT						(BIT0)
#define 	CR_PERSTN_F_DGTH_EN_BIT						(BIT8)
#define R16_SYS1_SYS_PERSTN_DGTH_CTRL					(0x09E >> 1)
#define 	CR_PERSTN_R_DGTH_TIMER_BASE_UNIT_SHIFT		(0)
#define 	CR_PERSTN_R_DGTH_TIMER_BASE_UNIT_MASK		(BIT_MASK(6))
#define 	CR_PERSTN_R_DGTH_TIMER_BASE_UNIT			(CR_PERSTN_R_DGTH_TIMER_BASE_UNIT_MASK << CR_PERSTN_R_DGTH_TIMER_BASE_UNIT_SHIFT)

/*
 *  +-----------------------------------------------------------------------+
 *  |					PCIE PERSTN Control      							|
 *  +-----------------------------------------------------------------------+
 */
#define SYS1_GPIO_DEGLITCH_BASE	                        (SYSTEM_PD1_REG_ADDRESS)

#define R8_SYS1_GPIO_DEGLITCH_CTRL    	                ((REG8  *) SYS1_GPIO_DEGLITCH_BASE)
#define R16_SYS1_GPIO_DEGLITCH_CTRL      	            ((REG16 *) SYS1_GPIO_DEGLITCH_BASE)
#define R32_SYS1_GPIO_DEGLITCH_CTRL          	        ((REG32 *) SYS1_GPIO_DEGLITCH_BASE)
#define R64_SYS1_GPIO_DEGLITCH_CTRL              	    ((REG64 *) SYS1_GPIO_DEGLITCH_BASE)

#define R32_SYS1_SYS_GPIO_DEGLITCH						(0x0C0 >> 2)
#define 	CR_GPIO_S0_DGTH_DONE_BIT					(BIT0)
#define 	CR_GPIO_S1_DGTH_DONE_BIT					(BIT1)
#define 	CR_GPIO_S2_DGTH_DONE_BIT					(BIT2)

#define R32_SYS1_SYS_GPIO_DEGLITCH1						(0x0C4 >> 2)
#define 	CR_GPIO_S0_DGTH_EN_BIT						(BIT0)
#define 	CR_GPIO_S1_DGTH_EN_BIT						(BIT8)
#define 	CR_GPIO_S2_DGTH_EN_BIT						(BIT16)

#define R32_SYS1_SYS_GPIO_DEGLITCH2						(0x0C8 >> 2)
#define 	CR_GPIO_S0_DGTH_SEL_SHIFT					(0)
#define 	CR_GPIO_S0_DGTH_SEL_MASK					(BIT_MASK(3))
#define		CR_GPIO_S0_DGTH_SEL							(CR_GPIO_S0_DGTH_SEL_MASK << CR_GPIO_S0_DGTH_SEL_SHIFT)
#define 	CR_GPIO_S1_DGTH_SEL_SHIFT					(4)
#define 	CR_GPIO_S1_DGTH_SEL_MASK					(BIT_MASK(3))
#define 	CR_GPIO_S1_DGTH_SEL							(CR_GPIO_S1_DGTH_SEL_MASK << CR_GPIO_S1_DGTH_SEL_SHIFT)
#define 	CR_GPIO_S2_DGTH_SEL_SHIFT					(8)
#define 	CR_GPIO_S2_DGTH_SEL_MASK					(BIT_MASK(3))
#define 	CR_GPIO_S2_DGTH_SEL							(CR_GPIO_S2_DGTH_SEL_MASK << CR_GPIO_S2_DGTH_SEL_SHIFT)
#define 	CR_GPIO_S0_DGTH_TYPE_SHIFT					(16)
#define 	CR_GPIO_S0_DGTH_TYPE_MASK					(BIT_MASK(2))
#define 	CR_GPIO_S0_DGTH_TYPE						(CR_GPIO_S0_DGTH_TYPE_MASK << CR_GPIO_S0_DGTH_TYPE_SHIFT)
#define 	CR_GPIO_S1_DGTH_TYPE_SHIFT					(20)
#define 	CR_GPIO_S1_DGTH_TYPE_MASK					(BIT_MASK(2))
#define 	CR_GPIO_S1_DGTH_TYPE						(CR_GPIO_S1_DGTH_TYPE_MASK << CR_GPIO_S1_DGTH_TYPE_SHIFT)
#define 	CR_GPIO_S2_DGTH_TYPE_SHIFT					(24)
#define 	CR_GPIO_S2_DGTH_TYPE_MASK					(BIT_MASK(2))
#define 	CR_GPIO_S2_DGTH_TYPE						(CR_GPIO_S2_DGTH_TYPE_MASK << CR_GPIO_S2_DGTH_TYPE_SHIFT)

#define R32_SYS1_SYS_GPIO_DEGLITCH3						(0x0CC >> 2)
#define 	CR_GPIO_DGTH_TIMER_BASE_UNIT_SHIFT			(0)
#define 	CR_GPIO_DGTH_TIMER_BASE_UNIT_MASK			(BIT_MASK(9))
#define 	CR_GPIO_DGTH_TIMER_BASE_UNIT				(CR_GPIO_DGTH_TIMER_BASE_UNIT_MASK << CR_GPIO_DGTH_TIMER_BASE_UNIT_SHIFT)

/*
 *  +-----------------------------------------------------------------------+
 *  |					FLASH PAD Control      							    |
 *  +-----------------------------------------------------------------------+
 */
#define SYS1_FLH_PAD_CTRL_BASE                  		(SYSTEM_PD1_REG_ADDRESS)

#define R8_SYS1_FLH_PAD                         		((REG8  *) SYS1_FLH_PAD_CTRL_BASE)
#define R16_SYS1_FLH_PAD                        		((REG16 *) SYS1_FLH_PAD_CTRL_BASE)
#define R32_SYS1_FLH_PAD                        		((REG32 *) SYS1_FLH_PAD_CTRL_BASE)
#define R64_SYS1_FLH_PAD                        		((REG64 *) SYS1_FLH_PAD_CTRL_BASE)

#define R32_SYS1_SYS_FLH_PAD_CH0_FCLE	    	        (0x100 >> 2)
#define R32_SYS1_SYS_FLH_PAD_CH1_FCLE  	                (0x104 >> 2)
#define R32_SYS1_SYS_FLH_PAD_CH2_FCLE                  	(0x108 >> 2)
#define R32_SYS1_SYS_FLH_PAD_CH3_FCLE                  	(0x10C >> 2)
#define 	CR_FCLE_PAD_CONOF_BIT                       (BIT0)
#define 	CR_FCLE_PAD_EN_BIT                          (BIT1)
#define 	CR_FCLE_PAD_ERV_CFG_BIT                     (BIT2)
#define 	CR_FCLE_PAD_GB_BIT                          (BIT3)
#define 	CR_FCLE_PAD_PD_BIT                          (BIT4)
#define 	CR_FCLE_PAD_PU_BIT                          (BIT5)
#define 	CR_FCLE_PAD_PU5P5_BIT                       (BIT6)
#define 	CR_FCLE_PAD_SONOF_BIT                       (BIT7)
#define 	CR_FXLE_PAD_IOLH_SHIFT						(10)
#define 	CR_FXLE_PAD_IOLH_MASK						(BIT_MASK(2))
#define 	CR_FXLE_PAD_IOLH                            (CR_FXLE_PAD_IOLH_MASK << CR_FXLE_PAD_IOLH_SHIFT)	// ALE and CLE

#define R32_SYS1_SYS_FLH_PAD_CH0_FALE	                (0x110 >> 2)
#define R32_SYS1_SYS_FLH_PAD_CH1_FALE   	            (0x114 >> 2)
#define R32_SYS1_SYS_FLH_PAD_CH2_FALE     	            (0x118 >> 2)
#define R32_SYS1_SYS_FLH_PAD_CH3_FALE     		        (0x11C >> 2)
#define		CR_FALE_PAD_CONOF_BIT                       (BIT0)
#define		CR_FALE_PAD_EN_BIT                          (BIT1)
#define		CR_FALE_PAD_ERV_CFG_BIT                     (BIT2)
#define		CR_FALE_PAD_GB_BIT                          (BIT3)
#define		CR_FALE_PAD_PD_BIT                          (BIT4)
#define		CR_FALE_PAD_PU_BIT                          (BIT5)
#define		CR_FALE_PAD_PU5P5_BIT                       (BIT6)
#define		CR_FALE_PAD_SONOF_BIT                       (BIT7)

#define R32_SYS1_SYS_FLH_PAD_CH0_FCEB                  	(0x120 >> 2)
#define R32_SYS1_SYS_FLH_PAD_CH1_FCEB                  	(0x124 >> 2)
#define R32_SYS1_SYS_FLH_PAD_CH2_FCEB                  	(0x128 >> 2)
#define R32_SYS1_SYS_FLH_PAD_CH3_FCEB                  	(0x12C >> 2)
#define 	CR_FCEB_PAD_CONOF_SHIFT						(0)
#define 	CR_FCEB_PAD_CONOF_MASK                      (BIT_MASK(4))
#define 	CR_FCEB_PAD_CONOF                           (CR_FCEB_PAD_CONOF_MASK << CR_FCEB_PAD_CONOF_SHIFT)
#define 	CR_FCEB_PAD_EN_SHIFT						(4)
#define 	CR_FCEB_PAD_EN_MASK							(BIT_MASK(4))
#define 	CR_FCEB_PAD_EN                              (CR_FCEB_PAD_EN_MASK << CR_FCEB_PAD_EN_SHIFT)
#define 	FCEB_OE_ALL_MASK							(BIT_MASK(4))
#define 	CR_FCEB_PAD_ERV_CFG_SHIFT					(8)
#define 	CR_FCEB_PAD_ERV_CFG_MASK					(BIT_MASK(4))
#define 	CR_FCEB_PAD_ERV_CFG                         (CR_FCEB_PAD_ERV_CFG_MASK << CR_FCEB_PAD_ERV_CFG_SHIFT)
#define 	CR_FCEB_PAD_GB_SHIFT						(12)
#define 	CR_FCEB_PAD_GB_MASK							(BIT_MASK(4))
#define 	CR_FCEB_PAD_GB                              (CR_FCEB_PAD_GB_MASK << CR_FCEB_PAD_GB_SHIFT)
#define 	CR_FCEB_PAD_PD_SHIFT						(16)
#define 	CR_FCEB_PAD_PD_MASK							(BIT_MASK(4))
#define 	CR_FCEB_PAD_PD                              (CR_FCEB_PAD_PD_MASK << CR_FCEB_PAD_PD_SHIFT)
#define 	CR_FCEB_PAD_PU_SHIFT						(20)
#define 	CR_FCEB_PAD_PU_MASK							(BIT_MASK(4))
#define 	CR_FCEB_PAD_PU                              (CR_FCEB_PAD_PU_MASK << CR_FCEB_PAD_PU_SHIFT)
#define 	CR_FCEB_PAD_PU5P5_SHIFT						(24)
#define 	CR_FCEB_PAD_PU5P5_MASK						(BIT_MASK(4))
#define 	CR_FCEB_PAD_PU5P5                           (CR_FCEB_PAD_PU5P5_MASK << CR_FCEB_PAD_PU5P5_SHIFT)
#define 	CR_FCEB_PAD_SONOF_SHIFT						(28)
#define 	CR_FCEB_PAD_SONOF_MASK						(BIT_MASK(4))
#define 	CR_FCEB_PAD_SONOF                           (CR_FCEB_PAD_SONOF_MASK << CR_FCEB_PAD_SONOF_SHIFT)

#define R32_SYS1_SYS_FLH_PAD_CH0_FCEB1                 	(0x130 >> 2)
#define R32_SYS1_SYS_FLH_PAD_CH1_FCEB1                 	(0x134 >> 2)
#define R32_SYS1_SYS_FLH_PAD_CH2_FCEB1                 	(0x138 >> 2)
#define R32_SYS1_SYS_FLH_PAD_CH3_FCEB1                 	(0x13C >> 2)
#define 	CR_FCEB_PAD_IOLH_SHIFT						(8)
#define 	CR_FCEB_PAD_IOLH_MASK						(BIT_MASK(2))
#define 	CR_FCEB_PAD_IOLH                            (CR_FCEB_PAD_IOLH_MASK << CR_FCEB_PAD_IOLH_SHIFT)

#define R32_SYS1_SYS_FLH_PAD_CH0_FWEB                  	(0x140 >> 2)
#define R32_SYS1_SYS_FLH_PAD_CH1_FWEB                  	(0x144 >> 2)
#define R32_SYS1_SYS_FLH_PAD_CH2_FWEB                  	(0x148 >> 2)
#define R32_SYS1_SYS_FLH_PAD_CH3_FWEB                  	(0x14C >> 2)
#define 	CR_FWEB_PAD_CONOF_BIT                       (BIT0)
#define 	CR_FWEB_PAD_EN_BIT                          (BIT1)
#define 	CR_FWEB_PAD_ERV_CFG_BIT                     (BIT2)
#define 	CR_FWEB_PAD_GB_BIT                          (BIT3)
#define 	CR_FWEB_PAD_PD_BIT                          (BIT4)
#define 	CR_FWEB_PAD_PU_BIT                          (BIT5)
#define 	CR_FWEB_PAD_PU5P5_BIT                       (BIT6)
#define 	CR_FWEB_PAD_SONOF_BIT                       (BIT7)
#define 	CR_FWEB_PAD_IOLH_SHIFT						(10)
#define 	CR_FWEB_PAD_IOLH_MASK						(BIT_MASK(2))
#define 	CR_FWEB_PAD_IOLH                            (CR_FWEB_PAD_IOLH_MASK << CR_FWEB_PAD_IOLH_SHIFT)

#define R32_SYS1_SYS_FLH_PAD_FRDY                      	(0x154 >> 2)
#define 	CR_FRDY_PAD_CONOF_BIT                       (BIT0)
#define 	CR_FRDY_PAD_EN_BIT                          (BIT1)
#define 	CR_FRDY_PAD_ERV_CFG_BIT                     (BIT2)
#define 	CR_FRDY_PAD_GB_BIT                          (BIT3)
#define 	CR_FRDY_PAD_PD_BIT                          (BIT4)
#define 	CR_FRDY_PAD_PU_BIT                          (BIT5)
#define 	CR_FRDY_PAD_PU5P5_BIT                       (BIT6)
#define 	CR_FRDY_PAD_SONOF_BIT                       (BIT7)
#define 	CR_FRDY_PAD_A_BIT                           (BIT8)

#define R32_SYS1_SYS_FLH_PAD_ZQ_PAD_CTRL0              	(0x160 >> 2)
#define 	ZQCAL_ZQ_RES_TRIM_RDY_SYNC_BIT              (BIT0)
#define 	ZQCAL_ZQ_RES_TRIM_OUT_SYNC_SHIFT			(8)
#define 	ZQCAL_ZQ_RES_TRIM_OUT_SYNC_MASK				(BIT_MASK(8))
#define 	ZQCAL_ZQ_RES_TRIM_OUT_SYNC                  (ZQCAL_ZQ_RES_TRIM_OUT_SYNC_MASK << ZQCAL_ZQ_RES_TRIM_OUT_SYNC_SHIFT)

#define R32_SYS1_SYS_FLH_PAD_ZQ_PAD_CTRL1              	(0x164 >> 2)
#define 	ZQCAL_ZQ_CAL_RDY_SYNC_BIT                   (BIT0)
#define 	ZQCAL_ZQ_CAL_N_SYNC_SHIFT					(8)
#define 	ZQCAL_ZQ_CAL_N_SYNC_MASK					(BIT_MASK(5))
#define 	ZQCAL_ZQ_CAL_N_SYNC                         (ZQCAL_ZQ_CAL_N_SYNC_MASK << ZQCAL_ZQ_CAL_N_SYNC_SHIFT)
#define 	ZQCAL_ZQ_CAL_P_SYNC_SHIFT					(16)
#define 	ZQCAL_ZQ_CAL_P_SYNC_MASK					(BIT_MASK(5))
#define 	ZQCAL_ZQ_CAL_P_SYNC                         (ZQCAL_ZQ_CAL_P_SYNC_MASK << ZQCAL_ZQ_CAL_P_SYNC_SHIFT)

#define R32_SYS1_SYS_FLH_PAD_ZQ_PAD_CTRL2              	(0x168 >> 2)
#define 	SR_ZQ_VREF_UP_SHIFT							(0)
#define 	SR_ZQ_VREF_UP_MASK							(BIT_MASK(2))
#define 	SR_ZQ_VREF_UP                               (SR_ZQ_VREF_UP_MASK << SR_ZQ_VREF_UP_SHIFT)
#define 	SR_ZQ_VREF_DN_SHIFT							(8)
#define 	SR_ZQ_VREF_DN_MASK							(BIT_MASK(2))
#define 	SR_ZQ_VREF_DN                               (SR_ZQ_VREF_DN_MASK << SR_ZQ_VREF_DN_SHIFT)
#define 	SR_ZQ_CONFIG_SHIFT							(16)
#define 	SR_ZQ_CONFIG_MASK							(BIT_MASK(4))
#define 	SR_ZQ_CONFIG                                (SR_ZQ_CONFIG_MASK << SR_ZQ_CONFIG_SHIFT)

#define R32_SYS1_SYS_FLH_PAD_ZQ_PAD_CTRL3              	(0x16C >> 2)
#define 	SR_ZQ_RSTB_RES_TRIM_BIT                     (BIT0)
#define 	SR_ZQ_EN_RES_TRIM_BIT                       (BIT8)

#define R32_SYS1_SYS_FLH_PAD_ZQ_PAD_CTRL4              	(0x170 >> 2)
#define 	SR_ZQ_BYPASS_RES_TRIM_BIT                   (BIT0)
#define 	SR_ZQ_RES_TRIM_CHK_BIT                      (BIT8)
#define 	SR_ZQ_RSTB_CAL_BIT                          (BIT16)
#define 	SR_ZQ_EN_CAL_BIT                            (BIT24)

#define R32_SYS1_SYS_FLH_PAD_CH0_FWPB                   (0x200 >> 2)
#define R32_SYS1_SYS_FLH_PAD_CH1_FWPB                   (0x204 >> 2)
#define R32_SYS1_SYS_FLH_PAD_CH2_FWPB                   (0x208 >> 2)
#define R32_SYS1_SYS_FLH_PAD_CH3_FWPB                   (0x20C >> 2)
#define 	CR_SR_WPB_BIT							    (BIT0)
#define 	CR_EN_WPB_BIT							    (BIT1)
#define 	CR_GB_WPB_BIT							    (BIT2)
#define 	CR_PMODE_WPB_BIT					        (BIT3)
#define 	CR_PD75_WPB_BIT							    (BIT4)
#define 	CR_PD5P5_WPB_BIT					        (BIT5)
#define 	CR_PU75_WPB_BIT							    (BIT6)
#define 	CR_PU5P5_WPB_BIT					   	    (BIT7)
#define 	CR_CONOF_WPB_BIT					        (BIT8)
#define 	CR_SONOF_WPB_BIT				    	    (BIT9)
#define     CR_IODRV_WPB_SHIFT						    (10)
#define     CR_IODRV_WPB_MASK						    (BIT_MASK(2))
#define     CR_IODRV_WPB						   	    (CR_IODRV_WPB_MASK << CR_IODRV_WPB_SHIFT)
#define     CR_CFG_WPB_SHIFT					   	    (12)
#define     CR_CFG_WPB_MASK							    (BIT_MASK(2))
#define     CR_CFG_WPB								    (CR_CFG_WPB_MASK << CR_CFG_WPB_SHIFT)
#define 	CR_A_WPB_BIT						  	    (BIT16)

#define R32_SYS1_SYS_FLH_PAD_COMMON                    	(0x240 >> 2)
#define 	CR_FLH_PAD_PMODE_BIT						(BIT0)
#define 	CR_FLH_PAD_REF_EN_BIT						(BIT8)

/*
 *  +-----------------------------------------------------------------------+
 *  |					FLASH PHY PAD Control      							|
 *  +-----------------------------------------------------------------------+
 */
#define SYS1_FLH_PHY_PAD_CTRL_BASE                  	(SYSTEM_PD1_REG_ADDRESS)

#define R8_SYS1_FLH_PHY_PAD                         	((REG8  *) SYS1_FLH_PHY_PAD_CTRL_BASE)
#define R16_SYS1_FLH_PHY_PAD                        	((REG16 *) SYS1_FLH_PHY_PAD_CTRL_BASE)
#define R32_SYS1_FLH_PHY_PAD                        	((REG32 *) SYS1_FLH_PHY_PAD_CTRL_BASE)
#define R64_SYS1_FLH_PHY_PAD                        	((REG64 *) SYS1_FLH_PHY_PAD_CTRL_BASE)

#define R32_SYS1_SYS_FPHY_CH0_CTRL1                    	(0x180 >> 2)
#define 	CR_FPHY_CH0_CONFIG_SHIFT					(0)
#define 	CR_FPHY_CH0_CONFIG_MASK						(BIT_MASK(8))
#define 	CR_FPHY_CH0_CONFIG                     		(CR_FPHY_CH0_CONFIG_MASK << CR_FPHY_CH0_CONFIG_SHIFT)
#define 	CR_FPHY_CH0_CONOF_DQ_BIT                    (BIT24)
#define 	CR_FPHY_CH0_CONOF_DQS_BIT                   (BIT25)
#define 	CR_FPHY_CH0_CONOF_REB_BIT                   (BIT26)
#define 	CR_FPHY_CH0_SONOF_DQ_BIT                    (BIT27)
#define 	CR_FPHY_CH0_SONOF_DQS_BIT                   (BIT28)
#define 	CR_FPHY_CH0_SONOF_REB_BIT                   (BIT29)

#define R32_SYS1_SYS_FPHY_CH0_CTRL2                    	(0x184 >> 2)
#define 	CR_FPHY_CH0_ERV_CFG_DQ_SHIFT                (0)
#define 	CR_FPHY_CH0_ERV_CFG_DQ_MASK                 (BIT_MASK(4))
#define 	CR_FPHY_CH0_ERV_CFG_DQ                     	(CR_FPHY_CH0_ERV_CFG_DQ_MASK << CR_FPHY_CH0_ERV_CFG_DQ_SHIFT)
#define 	CR_FPHY_CH0_ERV_CFG_DQS_SHIFT               (4)
#define 	CR_FPHY_CH0_ERV_CFG_DQS_MASK                (BIT_MASK(4))
#define 	CR_FPHY_CH0_ERV_CFG_DQS                     (CR_FPHY_CH0_ERV_CFG_DQS_MASK << CR_FPHY_CH0_ERV_CFG_DQS_SHIFT)
#define 	CR_FPHY_CH0_ERV_CFG_DQSB_SHIFT              (8)
#define 	CR_FPHY_CH0_ERV_CFG_DQSB_MASK               (BIT_MASK(4))
#define 	CR_FPHY_CH0_ERV_CFG_DQSB                    (CR_FPHY_CH0_ERV_CFG_DQSB_MASK << CR_FPHY_CH0_ERV_CFG_DQSB_SHIFT)
#define 	CR_FPHY_CH0_ERV_CFG_RE_SHIFT                (12)
#define 	CR_FPHY_CH0_ERV_CFG_RE_MASK	                (BIT_MASK(4))
#define 	CR_FPHY_CH0_ERV_CFG_RE                      (CR_FPHY_CH0_ERV_CFG_RE_MASK << CR_FPHY_CH0_ERV_CFG_RE_SHIFT)
#define 	CR_FPHY_CH0_ERV_CFG_REB_SHIFT               (16)
#define 	CR_FPHY_CH0_ERV_CFG_REB_MASK	            (BIT_MASK(4))
#define 	CR_FPHY_CH0_ERV_CFG_REB                     (CR_FPHY_CH0_ERV_CFG_REB_MASK << CR_FPHY_CH0_ERV_CFG_REB_SHIFT)
#define 	CR_FPHY_CH0_ODT_DQ_SHIFT                    (24)
#define 	CR_FPHY_CH0_ODT_DQ_MASK	                    (BIT_MASK(2))
#define 	CR_FPHY_CH0_ODT_DQ                          (CR_FPHY_CH0_ODT_DQ_MASK << CR_FPHY_CH0_ODT_DQ_SHIFT)
#define 	CR_FPHY_CH0_ODT_DQS_SHIFT                   (26)
#define 	CR_FPHY_CH0_ODT_DQS_MASK	                (BIT_MASK(2))
#define 	CR_FPHY_CH0_ODT_DQS                         (CR_FPHY_CH0_ODT_DQS_MASK << CR_FPHY_CH0_ODT_DQS_SHIFT)
#define 	ODT_SET_150_OHM								(0)
#define 	ODT_SET_100_OHM								(1)
#define 	ODT_SET_75_OHM								(2)
#define 	ODT_SET_50_OHM								(3)
#define 	CR_FPHY_CH0_RSEL_DQS_BIT                    (BIT28)
#define 	CR_FPHY_CH0_RSEL_REB_BIT                    (BIT29)

#define R32_SYS1_SYS_FPHY_CH0_CTRL3                    	(0x188 >> 2)
#define 	CR_FPHY_CH0_DTY_DQ_SHIFT                    (0)
#define 	CR_FPHY_CH0_DTY_DQ_MASK                     (BIT_MASK(4))
#define 	CR_FPHY_CH0_DTY_DQ                          (CR_FPHY_CH0_DTY_DQ_MASK << CR_FPHY_CH0_DTY_DQ_SHIFT)
#define 	CR_FPHY_CH0_DTY_DQS_SHIFT                   (4)
#define 	CR_FPHY_CH0_DTY_DQS_MASK                    (BIT_MASK(4))
#define 	CR_FPHY_CH0_DTY_DQS                         (CR_FPHY_CH0_DTY_DQS_MASK << CR_FPHY_CH0_DTY_DQS_SHIFT)
#define 	CR_FPHY_CH0_DTY_REB_SHIFT                   (8)
#define 	CR_FPHY_CH0_DTY_REB_MASK                    (BIT_MASK(4))
#define 	CR_FPHY_CH0_DTY_REB                         (CR_FPHY_CH0_DTY_REB_MASK << CR_FPHY_CH0_DTY_REB_SHIFT)
#define 	CR_FPHY_CH0_DIS_DQSB_BIT                    (BIT16)
#define 	CR_FPHY_CH0_DIS_RE_BIT                      (BIT17)
#define 	CR_FPHY_CH0_EN_LBM_BIT                      (BIT18)
#define 	CR_FPHY_CH0_GB_DQS_BIT                      (BIT19)
#define 	CR_FPHY_CH0_GB_PHYA_BIT                     (BIT20)
#define 	CR_FPHY_CH0_GB_REB_BIT                      (BIT21)

#define R32_SYS1_SYS_FPHY_CH0_CTRL4                    	(0x18C >> 2)
#define 	CR_FPHY_CH0_IOLH_DQ_SHIFT                   (0)
#define 	CR_FPHY_CH0_IOLH_DQ_MASK					(BIT_MASK(3))
#define 	CR_FPHY_CH0_IOLH_DQ							(CR_FPHY_CH0_IOLH_DQ_MASK << CR_FPHY_CH0_IOLH_DQ_SHIFT)
#define 	CR_FPHY_CH0_IOLH_DQS_SHIFT                  (3)
#define 	CR_FPHY_CH0_IOLH_DQS_MASK					(BIT_MASK(3))
#define 	CR_FPHY_CH0_IOLH_DQS						(CR_FPHY_CH0_IOLH_DQS_MASK << CR_FPHY_CH0_IOLH_DQS_SHIFT)
#define 	CR_FPHY_CH0_IOLH_REB_SHIFT                  (8)
#define 	CR_FPHY_CH0_IOLH_REB_MASK					(BIT_MASK(3))
#define 	CR_FPHY_CH0_IOLH_REB						(CR_FPHY_CH0_IOLH_REB_MASK << CR_FPHY_CH0_IOLH_REB_SHIFT)
#define 	FPHY_IOLH_SET_50_OHM						(2)
#define 	FPHY_IOLH_SET_37_5_OHM						(3)
#define 	FPHY_IOLH_SET_30_OHM						(4)
#define 	FPHY_IOLH_SET_25_OHM						(5)
#define 	FPHY_IOLH_SET_21_43_OHM						(6)
#define     CR_FPHY_CH0_PD_DQ_SHIFT						(16)
#define     CR_FPHY_CH0_PD_DQ_MASK						(BIT_MASK(8))
#define     CR_FPHY_CH0_PD_DQ							(CR_FPHY_CH0_PD_DQ_MASK << CR_FPHY_CH0_PD_DQ_SHIFT)
#define 	CR_FPHY_CH0_PD_DQS_BIT                      (BIT24)
#define 	CR_FPHY_CH0_PD_REB_BIT                      (BIT25)

#define R32_SYS1_SYS_FPHY_CH0_CTRL5                    	(0x190 >> 2)
#define 	CR_FPHY_CH0_PU5P5_DQ_SHIFT                  (0)
#define 	CR_FPHY_CH0_PU5P5_DQ_MASK					(BIT_MASK(8))
#define 	CR_FPHY_CH0_PU5P5_DQ						(CR_FPHY_CH0_PU5P5_DQ_MASK << CR_FPHY_CH0_PU5P5_DQ_SHIFT)
#define 	CR_FPHY_CH0_PU_DQ_SHIFT                  	(8)
#define 	CR_FPHY_CH0_PU_DQ_MASK						(BIT_MASK(8))
#define 	CR_FPHY_CH0_PU_DQ							(CR_FPHY_CH0_PU_DQ_MASK << CR_FPHY_CH0_PU_DQ_SHIFT)
#define 	CR_FPHY_CH0_PU_DQS_BIT                      (BIT16)
#define 	CR_FPHY_CH0_PU_REB_BIT                      (BIT17)
#define 	CR_FPHY_CH0_GB_DQ_SHIFT                     (24)
#define 	CR_FPHY_CH0_GB_DQ_MASK                      (BIT_MASK(8))
#define 	CR_FPHY_CH0_GB_DQ                           (CR_FPHY_CH0_GB_DQ_MASK << CR_FPHY_CH0_GB_DQ_SHIFT)

#define R32_SYS1_SYS_FPHY_CH1_CTRL1                    	(0x194 >> 2)
#define 	CR_FPHY_CH1_CONFIG_SHIFT                    (0)
#define 	CR_FPHY_CH1_CONFIG_MASK                     (BIT_MASK(8))
#define 	CR_FPHY_CH1_CONFIG                          (CR_FPHY_CH1_CONFIG_MASK << CR_FPHY_CH1_CONFIG_SHIFT)
#define 	CR_FPHY_CH1_CONOF_DQ_BIT                    (BIT24)
#define 	CR_FPHY_CH1_CONOF_DQS_BIT                   (BIT25)
#define 	CR_FPHY_CH1_CONOF_REB_BIT                   (BIT26)
#define 	CR_FPHY_CH1_SONOF_DQ_BIT                    (BIT27)
#define 	CR_FPHY_CH1_SONOF_DQS_BIT                   (BIT28)
#define 	CR_FPHY_CH1_SONOF_REB_BIT                   (BIT29)

#define R32_SYS1_SYS_FPHY_CH1_CTRL2                    	(0x198 >> 2)
#define 	CR_FPHY_CH1_ERV_CFG_DQ_SHIFT                (0)
#define 	CR_FPHY_CH1_ERV_CFG_DQ_MASK                 (BIT_MASK(4))
#define 	CR_FPHY_CH1_ERV_CFG_DQ                      (CR_FPHY_CH1_ERV_CFG_DQ_MASK << CR_FPHY_CH1_ERV_CFG_DQ_SHIFT)
#define 	CR_FPHY_CH1_ERV_CFG_DQS_SHIFT               (4)
#define 	CR_FPHY_CH1_ERV_CFG_DQS_MASK                (BIT_MASK(4))
#define 	CR_FPHY_CH1_ERV_CFG_DQS                     (CR_FPHY_CH1_ERV_CFG_DQS_MASK << CR_FPHY_CH1_ERV_CFG_DQS_SHIFT)
#define 	CR_FPHY_CH1_ERV_CFG_DQSB_SHIFT              (8)
#define 	CR_FPHY_CH1_ERV_CFG_DQSB_MASK               (BIT_MASK(4))
#define 	CR_FPHY_CH1_ERV_CFG_DQSB                    (CR_FPHY_CH1_ERV_CFG_DQSB_MASK << CR_FPHY_CH1_ERV_CFG_DQSB_SHIFT)
#define 	CR_FPHY_CH1_ERV_CFG_RE_SHIFT                (12)
#define 	CR_FPHY_CH1_ERV_CFG_RE_MASK                 (BIT_MASK(4))
#define 	CR_FPHY_CH1_ERV_CFG_RE                      (CR_FPHY_CH1_ERV_CFG_RE_MASK << CR_FPHY_CH1_ERV_CFG_RE_SHIFT)
#define 	CR_FPHY_CH1_ERV_CFG_REB_SHIFT               (16)
#define 	CR_FPHY_CH1_ERV_CFG_REB_MASK                (BIT_MASK(4))
#define 	CR_FPHY_CH1_ERV_CFG_REB                     (CR_FPHY_CH1_ERV_CFG_REB_MASK << CR_FPHY_CH1_ERV_CFG_REB_SHIFT)
#define 	CR_FPHY_CH1_ERV_ODT_DQ_SHIFT                (24)
#define 	CR_FPHY_CH1_ERV_ODT_DQ_MASK                 (BIT_MASK(2))
#define 	CR_FPHY_CH1_ERV_ODT_DQ                      (CR_FPHY_CH1_ERV_ODT_DQ_MASK << CR_FPHY_CH1_ERV_ODT_DQ_SHIFT)
#define 	CR_FPHY_CH1_ERV_ODT_DQS_SHIFT               (26)
#define 	CR_FPHY_CH1_ERV_ODT_DQS_MASK                (BIT_MASK(2))
#define 	CR_FPHY_CH1_ERV_ODT_DQS                     (CR_FPHY_CH1_ERV_ODT_DQS_MASK << CR_FPHY_CH1_ERV_ODT_DQS_SHIFT)
#define 	CR_FPHY_CH1_ERV_RSEL_DQS_BIT                (BIT28)
#define 	CR_FPHY_CH1_ERV_RSEL_REB_BIT                (BIT29)

#define R32_SYS1_SYS_FPHY_CH1_CTRL3                    	(0x19C >> 2)
#define 	CR_FPHY_CH1_DTY_DQ_SHIFT                    (0)
#define 	CR_FPHY_CH1_DTY_DQ_MASK                     (BIT_MASK(4))
#define 	CR_FPHY_CH1_DTY_DQ                          (CR_FPHY_CH1_DTY_DQ_MASK << CR_FPHY_CH1_DTY_DQ_SHIFT)
#define 	CR_FPHY_CH1_DTY_DQS_SHIFT                   (4)
#define 	CR_FPHY_CH1_DTY_DQS_MASK                    (BIT_MASK(4))
#define 	CR_FPHY_CH1_DTY_DQS                         (CR_FPHY_CH1_DTY_DQS_MASK << CR_FPHY_CH1_DTY_DQS_SHIFT)
#define 	CR_FPHY_CH1_DTY_REB_SHIFT                   (8)
#define 	CR_FPHY_CH1_DTY_REB_MASK                    (BIT_MASK(4))
#define 	CR_FPHY_CH1_DTY_REB                         (CR_FPHY_CH1_DTY_REB_MASK << CR_FPHY_CH1_DTY_REB_SHIFT)
#define 	CR_FPHY_CH1_DIS_DQSB_BIT                    (BIT16)
#define 	CR_FPHY_CH1_DIS_RE_BIT                      (BIT17)
#define 	CR_FPHY_CH1_EN_LBM_BIT                      (BIT18)
#define 	CR_FPHY_CH1_GB_DQS_BIT                      (BIT19)
#define 	CR_FPHY_CH1_GB_PHYA_BIT                     (BIT20)
#define 	CR_FPHY_CH1_GB_REB_BIT                      (BIT21)

#define R32_SYS1_SYS_FPHY_CH1_CTRL4                    	(0x1A0 >> 2)
#define 	CR_FPHY_CH1_IOLH_DQ_SHIFT                   (0)
#define 	CR_FPHY_CH1_IOLH_DQ_MASK                    (BIT_MASK(3))
#define 	CR_FPHY_CH1_IOLH_DQ                         (CR_FPHY_CH1_IOLH_DQ_MASK << CR_FPHY_CH1_IOLH_DQ_SHIFT)
#define 	CR_FPHY_CH1_IOLH_DQS_SHIFT                  (3)
#define 	CR_FPHY_CH1_IOLH_DQS_MASK                   (BIT_MASK(3))
#define 	CR_FPHY_CH1_IOLH_DQS                        (CR_FPHY_CH1_IOLH_DQS_MASK << CR_FPHY_CH1_IOLH_DQS_SHIFT)
#define 	CR_FPHY_CH1_IOLH_REB_SHIFT                  (8)
#define 	CR_FPHY_CH1_IOLH_REB_MASK                   (BIT_MASK(3))
#define 	CR_FPHY_CH1_IOLH_REB                        (CR_FPHY_CH1_IOLH_REB_MASK << CR_FPHY_CH1_IOLH_REB_SHIFT)
#define 	CR_FPHY_CH1_PD_DQ_SHIFT                     (16)
#define 	CR_FPHY_CH1_PD_DQ_MASK                      (BIT_MASK(8))
#define 	CR_FPHY_CH1_PD_DQ                           (CR_FPHY_CH1_PD_DQ_MASK << CR_FPHY_CH1_PD_DQ_SHIFT)
#define 	CR_FPHY_CH1_PD_DQS_BIT                      (BIT24)
#define 	CR_FPHY_CH1_PD_REB_BIT                      (BIT25)

#define R32_SYS1_SYS_FPHY_CH1_CTRL5                    	(0x1A4 >> 2)
#define 	CR_FPHY_CH1_PU5P5_DQ_SHIFT                  (0)
#define 	CR_FPHY_CH1_PU5P5_DQ_MASK                   (BIT_MASK(8))
#define 	CR_FPHY_CH1_PU5P5_DQ                        (CR_FPHY_CH1_PU5P5_DQ_MASK << CR_FPHY_CH1_PU5P5_DQ_SHIFT)
#define 	CR_FPHY_CH1_PU_DQ_SHIFT                  	(8)
#define 	CR_FPHY_CH1_PU_DQ_MASK                   	(BIT_MASK(8))
#define 	CR_FPHY_CH1_PU_DQ                        	(CR_FPHY_CH1_PU_DQ_MASK << CR_FPHY_CH1_PU_DQ_SHIFT)
#define 	CR_FPHY_CH1_PU_DQS_BIT                      (BIT16)
#define 	CR_FPHY_CH1_PU_REB_BIT                      (BIT17)
#define 	CR_FPHY_CH1_GB_DQ_SHIFT                     (24)
#define 	CR_FPHY_CH1_GB_DQ_MASK                      (BIT_MASK(8))
#define 	CR_FPHY_CH1_GB_DQ                           (CR_FPHY_CH1_GB_DQ_MASK << CR_FPHY_CH1_GB_DQ_SHIFT)

#define R32_SYS1_SYS_FPHY_CH2_CTRL1                    	(0x1A8 >> 2)
#define 	CR_FPHY_CH2_CONFIG_SHIFT                    (0)
#define 	CR_FPHY_CH2_CONFIG_MASK                     (BIT_MASK(8))
#define 	CR_FPHY_CH2_CONFIG                          (CR_FPHY_CH2_CONFIG_MASK << CR_FPHY_CH2_CONFIG_SHIFT)
#define 	CR_FPHY_CH2_CONOF_DQ_BIT                    (BIT24)
#define 	CR_FPHY_CH2_CONOF_DQS_BIT                   (BIT25)
#define 	CR_FPHY_CH2_CONOF_REB_BIT                   (BIT26)
#define 	CR_FPHY_CH2_SONOF_DQ_BIT                    (BIT27)
#define 	CR_FPHY_CH2_SONOF_DQS_BIT                   (BIT28)
#define 	CR_FPHY_CH2_SONOF_REB_BIT                   (BIT29)

#define R32_SYS1_SYS_FPHY_CH2_CTRL2                    	(0x1AC >> 2)
#define 	CR_FPHY_CH2_ERV_CFG_DQ_SHIFT                (0)
#define 	CR_FPHY_CH2_ERV_CFG_DQ_MASK                 (BIT_MASK(4))
#define 	CR_FPHY_CH2_ERV_CFG_DQ                      (CR_FPHY_CH2_ERV_CFG_DQ_MASK << CR_FPHY_CH2_ERV_CFG_DQ_SHIFT)
#define 	CR_FPHY_CH2_ERV_CFG_DQS_SHIFT               (4)
#define 	CR_FPHY_CH2_ERV_CFG_DQS_MASK                (BIT_MASK(4))
#define 	CR_FPHY_CH2_ERV_CFG_DQS                     (CR_FPHY_CH2_ERV_CFG_DQS_MASK << CR_FPHY_CH2_ERV_CFG_DQS_SHIFT)
#define 	CR_FPHY_CH2_ERV_CFG_DQSB_SHIFT              (8)
#define 	CR_FPHY_CH2_ERV_CFG_DQSB_MASK               (BIT_MASK(4))
#define 	CR_FPHY_CH2_ERV_CFG_DQSB                    (CR_FPHY_CH2_ERV_CFG_DQSB_MASK << CR_FPHY_CH2_ERV_CFG_DQSB_SHIFT)
#define 	CR_FPHY_CH2_ERV_CFG_RE_SHIFT                (12)
#define 	CR_FPHY_CH2_ERV_CFG_RE_MASK                 (BIT_MASK(4))
#define 	CR_FPHY_CH2_ERV_CFG_RE                      (CR_FPHY_CH2_ERV_CFG_RE_MASK << CR_FPHY_CH2_ERV_CFG_RE_SHIFT)
#define 	CR_FPHY_CH2_ERV_CFG_REB_SHIFT               (16)
#define 	CR_FPHY_CH2_ERV_CFG_REB_MASK                (BIT_MASK(4))
#define 	CR_FPHY_CH2_ERV_CFG_REB                     (CR_FPHY_CH2_ERV_CFG_REB_MASK << CR_FPHY_CH2_ERV_CFG_REB_SHIFT)
#define 	CR_FPHY_CH2_ERV_ODT_DQ_SHIFT                (24)
#define 	CR_FPHY_CH2_ERV_ODT_DQ_MASK                 (BIT_MASK(2))
#define 	CR_FPHY_CH2_ERV_ODT_DQ                      (CR_FPHY_CH2_ERV_ODT_DQ_MASK << CR_FPHY_CH2_ERV_ODT_DQ_SHIFT)
#define 	CR_FPHY_CH2_ERV_ODT_DQS_SHIFT               (26)
#define 	CR_FPHY_CH2_ERV_ODT_DQS_MASK                (BIT_MASK(2))
#define 	CR_FPHY_CH2_ERV_ODT_DQS                     (CR_FPHY_CH2_ERV_ODT_DQS_MASK << CR_FPHY_CH2_ERV_ODT_DQS_SHIFT)
#define 	CR_FPHY_CH2_ERV_RSEL_DQS_BIT                (BIT28)
#define 	CR_FPHY_CH2_ERV_RSEL_REB_BIT                (BIT29)

#define R32_SYS1_SYS_FPHY_CH2_CTRL3                    	(0x1B0 >> 2)
#define 	CR_FPHY_CH2_DTY_DQ_SHIFT                    (0)
#define 	CR_FPHY_CH2_DTY_DQ_MASK                     (BIT_MASK(4))
#define 	CR_FPHY_CH2_DTY_DQ                          (CR_FPHY_CH2_DTY_DQ_MASK << CR_FPHY_CH2_DTY_DQ_SHIFT)
#define 	CR_FPHY_CH2_DTY_DQS_SHIFT                   (4)
#define 	CR_FPHY_CH2_DTY_DQS_MASK                    (BIT_MASK(4))
#define 	CR_FPHY_CH2_DTY_DQS                         (CR_FPHY_CH2_DTY_DQS_MASK << CR_FPHY_CH2_DTY_DQS_SHIFT)
#define 	CR_FPHY_CH2_DTY_REB_SHIFT                   (8)
#define 	CR_FPHY_CH2_DTY_REB_MASK                    (BIT_MASK(4))
#define 	CR_FPHY_CH2_DTY_REB                         (CR_FPHY_CH2_DTY_REB_MASK << CR_FPHY_CH2_DTY_REB_SHIFT)
#define 	CR_FPHY_CH2_DIS_DQSB_BIT                    (BIT16)
#define 	CR_FPHY_CH2_DIS_RE_BIT                      (BIT17)
#define 	CR_FPHY_CH2_EN_LBM_BIT                      (BIT18)
#define 	CR_FPHY_CH2_GB_DQS_BIT                      (BIT19)
#define 	CR_FPHY_CH2_GB_PHYA_BIT                     (BIT20)
#define 	CR_FPHY_CH2_GB_REB_BIT                      (BIT21)

#define R32_SYS1_SYS_FPHY_CH2_CTRL4                    	(0x1B4 >> 2)
#define 	CR_FPHY_CH2_IOLH_DQ_SHIFT                   (0)
#define 	CR_FPHY_CH2_IOLH_DQ_MASK                    (BIT_MASK(3))
#define 	CR_FPHY_CH2_IOLH_DQ                         (CR_FPHY_CH2_IOLH_DQ_MASK << CR_FPHY_CH2_IOLH_DQ_SHIFT)
#define 	CR_FPHY_CH2_IOLH_DQS_SHIFT                  (3)
#define 	CR_FPHY_CH2_IOLH_DQS_MASK                   (BIT_MASK(3))
#define 	CR_FPHY_CH2_IOLH_DQS                        (CR_FPHY_CH2_IOLH_DQS_MASK << CR_FPHY_CH2_IOLH_DQS_SHIFT
#define 	CR_FPHY_CH2_IOLH_REB_SHIFT                  (8)
#define 	CR_FPHY_CH2_IOLH_REB_MASK                   (BIT_MASK(3))
#define 	CR_FPHY_CH2_IOLH_REB                        (CR_FPHY_CH2_IOLH_REB_MASK << CR_FPHY_CH2_IOLH_REB_SHIFT
#define 	CR_FPHY_CH2_PD_DQ_SHIFT                     (16)
#define 	CR_FPHY_CH2_PD_DQ_MASK                      (BIT_MASK(8))
#define 	CR_FPHY_CH2_PD_DQ                           (CR_FPHY_CH2_PD_DQ_MASK << CR_FPHY_CH2_PD_DQ_SHIFT)
#define 	CR_FPHY_CH2_PD_DQS_BIT                      (BIT24)
#define 	CR_FPHY_CH2_PD_REB_BIT                      (BIT25)

#define R32_SYS1_SYS_FPHY_CH2_CTRL5                    	(0x1B8 >> 2)
#define 	CR_FPHY_CH2_PU5P5_DQ_SHIFT                  (0)
#define 	CR_FPHY_CH2_PU5P5_DQ_MASK                   (BIT_MASK(8))
#define 	CR_FPHY_CH2_PU5P5_DQ                        (CR_FPHY_CH2_PU5P5_DQ_MASK << CR_FPHY_CH2_PU5P5_DQ_SHIFT)
#define 	CR_FPHY_CH2_PU_DQ_SHIFT                  	(8)
#define 	CR_FPHY_CH2_PU_DQ_MASK                   	(BIT_MASK(8))
#define 	CR_FPHY_CH2_PU_DQ                        	(CR_FPHY_CH2_PU_DQ_MASK << CR_FPHY_CH2_PU_DQ_SHIFT
#define 	CR_FPHY_CH2_PU_DQS_BIT                      (BIT16)
#define 	CR_FPHY_CH2_PU_REB_BIT                      (BIT17)
#define 	CR_FPHY_CH2_GB_DQ_SHIFT                     (24)
#define 	CR_FPHY_CH2_GB_DQ_MASK                      (BIT_MASK(8))
#define 	CR_FPHY_CH2_GB_DQ                           (CR_FPHY_CH2_GB_DQ_MASK << CR_FPHY_CH2_GB_DQ_SHIFT)

#define R32_SYS1_SYS_FPHY_CH3_CTRL1                    	(0x1BC >> 2)
#define 	CR_FPHY_CH3_CONFIG_SHIFT                    (0)
#define 	CR_FPHY_CH3_CONFIG_MASK                     (BIT_MASK(8))
#define 	CR_FPHY_CH3_CONFIG                          (CR_FPHY_CH3_CONFIG_MASK << CR_FPHY_CH3_CONFIG_SHIFT)
#define 	CR_FPHY_CH3_CONOF_DQ_BIT                    (BIT24)
#define 	CR_FPHY_CH3_CONOF_DQS_BIT                   (BIT25)
#define 	CR_FPHY_CH3_CONOF_REB_BIT                   (BIT26)
#define 	CR_FPHY_CH3_SONOF_DQ_BIT                    (BIT27)
#define 	CR_FPHY_CH3_SONOF_DQS_BIT                   (BIT28)
#define 	CR_FPHY_CH3_SONOF_REB_BIT                   (BIT29)

#define R32_SYS1_SYS_FPHY_CH3_CTRL2                    	(0x1C0 >> 2)
#define 	CR_FPHY_CH3_ERV_CFG_DQ_SHIFT                (0)
#define 	CR_FPHY_CH3_ERV_CFG_DQ_MASK                 (BIT_MASK(4))
#define 	CR_FPHY_CH3_ERV_CFG_DQ                      (CR_FPHY_CH3_ERV_CFG_DQ_MASK << CR_FPHY_CH3_ERV_CFG_DQ_SHIFT)
#define 	CR_FPHY_CH3_ERV_CFG_DQS_SHIFT               (4)
#define 	CR_FPHY_CH3_ERV_CFG_DQS_MASK                (BIT_MASK(4))
#define 	CR_FPHY_CH3_ERV_CFG_DQS                     (CR_FPHY_CH3_ERV_CFG_DQS_MASK << CR_FPHY_CH3_ERV_CFG_DQS_SHIFT)
#define 	CR_FPHY_CH3_ERV_CFG_DQSB_SHIFT              (8)
#define 	CR_FPHY_CH3_ERV_CFG_DQSB_MASK               (BIT_MASK(4))
#define 	CR_FPHY_CH3_ERV_CFG_DQSB                    (CR_FPHY_CH3_ERV_CFG_DQSB_MASK << CR_FPHY_CH3_ERV_CFG_DQSB_SHIFT)
#define 	CR_FPHY_CH3_ERV_CFG_RE_SHIFT                (12)
#define 	CR_FPHY_CH3_ERV_CFG_RE_MASK                 (BIT_MASK(4))
#define 	CR_FPHY_CH3_ERV_CFG_RE                      (CR_FPHY_CH3_ERV_CFG_RE_MASK << CR_FPHY_CH3_ERV_CFG_RE_SHIFT)
#define 	CR_FPHY_CH3_ERV_CFG_REB_SHIFT               (16)
#define 	CR_FPHY_CH3_ERV_CFG_REB_MASK                (BIT_MASK(4))
#define 	CR_FPHY_CH3_ERV_CFG_REB                     (CR_FPHY_CH3_ERV_CFG_REB_MASK << CR_FPHY_CH3_ERV_CFG_REB_SHIFT)
#define 	CR_FPHY_CH3_ERV_ODT_DQ_SHIFT                (24)
#define 	CR_FPHY_CH3_ERV_ODT_DQ_MASK                 (BIT_MASK(2))
#define 	CR_FPHY_CH3_ERV_ODT_DQ                      (CR_FPHY_CH3_ERV_ODT_DQ_MASK << CR_FPHY_CH3_ERV_ODT_DQ_SHIFT)
#define 	CR_FPHY_CH3_ERV_ODT_DQS_SHIFT               (26)
#define 	CR_FPHY_CH3_ERV_ODT_DQS_MASK                (BIT_MASK(2))
#define 	CR_FPHY_CH3_ERV_ODT_DQS                     (CR_FPHY_CH3_ERV_ODT_DQS_MASK << CR_FPHY_CH3_ERV_ODT_DQS_SHIFT)
#define 	CR_FPHY_CH3_ERV_RSEL_DQS_BIT                (BIT28)
#define 	CR_FPHY_CH3_ERV_RSEL_REB_BIT                (BIT29)

#define R32_SYS1_SYS_FPHY_CH3_CTRL3                    	(0x1C4 >> 2)
#define 	CR_FPHY_CH3_DTY_DQ_SHIFT                    (0)
#define 	CR_FPHY_CH3_DTY_DQ_MASK                     (BIT_MASK(4))
#define 	CR_FPHY_CH3_DTY_DQ                          (CR_FPHY_CH3_DTY_DQ_MASK << CR_FPHY_CH3_DTY_DQ_SHIFT)
#define 	CR_FPHY_CH3_DTY_DQS_SHIFT                   (4)
#define 	CR_FPHY_CH3_DTY_DQS_MASK                    (BIT_MASK(4))
#define 	CR_FPHY_CH3_DTY_DQS                         (CR_FPHY_CH3_DTY_DQS_MASK << CR_FPHY_CH3_DTY_DQS_SHIFT
#define 	CR_FPHY_CH3_DTY_REB_SHIFT                   (8)
#define 	CR_FPHY_CH3_DTY_REB_MASK                    (BIT_MASK(4))
#define 	CR_FPHY_CH3_DTY_REB                         (CR_FPHY_CH3_DTY_REB_MASK << CR_FPHY_CH3_DTY_REB_SHIFT
#define 	CR_FPHY_CH3_DIS_DQSB_BIT                    (BIT16)
#define 	CR_FPHY_CH3_DIS_RE_BIT                      (BIT17)
#define 	CR_FPHY_CH3_EN_LBM_BIT                      (BIT18)
#define 	CR_FPHY_CH3_GB_DQS_BIT                      (BIT19)
#define 	CR_FPHY_CH3_GB_PHYA_BIT                     (BIT20)
#define 	CR_FPHY_CH3_GB_REB_BIT                      (BIT21)
#define 	CR_FPHY_CH3_REF_EN_BIT                      (BIT22)

#define R32_SYS1_SYS_FPHY_CH3_CTRL4                    	(0x1C8 >> 2)
#define 	CR_FPHY_CH3_IOLH_DQ_SHIFT                   (0)
#define 	CR_FPHY_CH3_IOLH_DQ_MASK                    (BIT_MASK(3))
#define 	CR_FPHY_CH3_IOLH_DQ                         (CR_FPHY_CH3_IOLH_DQ_MASK << CR_FPHY_CH3_IOLH_DQ_SHIFT)
#define 	CR_FPHY_CH3_IOLH_DQS_SHIFT                  (3)
#define 	CR_FPHY_CH3_IOLH_DQS_MASK                   (BIT_MASK(3))
#define 	CR_FPHY_CH3_IOLH_DQS                        (CR_FPHY_CH3_IOLH_DQS_MASK << CR_FPHY_CH3_IOLH_DQS_SHIFT)
#define 	CR_FPHY_CH3_IOLH_REB_SHIFT                  (8)
#define 	CR_FPHY_CH3_IOLH_REB_MASK                   (BIT_MASK(3))
#define 	CR_FPHY_CH3_IOLH_REB                        (CR_FPHY_CH3_IOLH_REB_MASK << CR_FPHY_CH3_IOLH_REB_SHIFT)
#define 	CR_FPHY_CH3_PD_DQ_SHIFT                     (16)
#define 	CR_FPHY_CH3_PD_DQ_MASK                      (BIT_MASK(8))
#define 	CR_FPHY_CH3_PD_DQ                           (CR_FPHY_CH3_PD_DQ_MASK << CR_FPHY_CH3_PD_DQ_SHIFT)
#define 	CR_FPHY_CH3_PD_DQS_BIT                      (BIT24)
#define 	CR_FPHY_CH3_PD_REB_BIT                      (BIT25)

#define R32_SYS1_SYS_FPHY_CH3_CTRL5                    	(0x1CC >> 2)
#define 	CR_FPHY_CH3_PU5P5_DQ_SHIFT                  (0)
#define 	CR_FPHY_CH3_PU5P5_DQ_MASK                   (BIT_MASK(8))
#define 	CR_FPHY_CH3_PU5P5_DQ                        (CR_FPHY_CH3_PU5P5_DQ_MASK << CR_FPHY_CH3_PU5P5_DQ_SHIFT)
#define 	CR_FPHY_CH3_PU_DQ_SHIFT                  	(8)
#define 	CR_FPHY_CH3_PU_DQ_MASK                   	(BIT_MASK(8))
#define 	CR_FPHY_CH3_PU_DQ                        	(CR_FPHY_CH3_PU_DQ_MASK << CR_FPHY_CH3_PU_DQ_SHIFT)
#define 	CR_FPHY_CH3_PU_DQS_BIT                      (BIT16)
#define 	CR_FPHY_CH3_PU_REB_BIT                      (BIT17)
#define 	CR_FPHY_CH3_GB_DQ_SHIFT                     (24)
#define 	CR_FPHY_CH3_GB_DQ_MASK                      (BIT_MASK(8))
#define 	CR_FPHY_CH3_GB_DQ                           (CR_FPHY_CH3_GB_DQ_MASK << CR_FPHY_CH3_GB_DQ_SHIFT)

/*
 *  +-----------------------------------------------------------------------+
 *  |					FLASH PHY OSC Control      							|
 *  +-----------------------------------------------------------------------+
 */
#define SYS1_FLH_PHY_OSC_CTRL_BASE                  	(SYSTEM_PD1_REG_ADDRESS)

#define R8_SYS1_FLH_PHY_OSC                         	((REG8  *) SYS1_FLH_PHY_OSC_CTRL_BASE)
#define R16_SYS1_FLH_PHY_OSC                        	((REG16 *) SYS1_FLH_PHY_OSC_CTRL_BASE)
#define R32_SYS1_FLH_PHY_OSC                        	((REG32 *) SYS1_FLH_PHY_OSC_CTRL_BASE)
#define R64_SYS1_FLH_PHY_OSC                        	((REG64 *) SYS1_FLH_PHY_OSC_CTRL_BASE)

#define R32_SYS1_SYS_OSC_HCTRL1	                    	(0x284 >> 2)
#define 	CR_SR_FG_OSC_FPHY_BIT						(BIT0)
#define 	CR_SR_EN_OSC_FPHY_H_BIT						(BIT8)
#define 	CR_SR_POSC_UPD_FPHY_H_BIT					(BIT16)
#define     CR_SR_OSC_SEL_FPHY_H_SHIFT					(24)
#define     CR_SR_OSC_SEL_FPHY_H_MASK					(BIT_MASK(3))
#define     CR_SR_OSC_SEL_FPHY_H						(CR_SR_OSC_SEL_FPHY_H_MASK << CR_SR_OSC_SEL_FPHY_H_SHIFT)
#define 	RCOSC_FLH_MHZ_1600                      	(0x7)
#define 	RCOSC_FLH_MHZ_1200                      	(0x6)
#define 	RCOSC_FLH_MHZ_1066                      	(0x5)
#define 	RCOSC_FLH_MHZ_800                       	(0x4)
#define 	RCOSC_FLH_MHZ_667                       	(0x3)
#define 	RCOSC_FLH_MHZ_533                       	(0x2)
#define 	RCOSC_FLH_MHZ_400                       	(0x1)
#define 	RCOSC_FLH_MHZ_200                       	(0x0)


#define R32_SYS1_SYS_OSC_HCTRL4	                    	(0x290 >> 2)
#define 	CR_SR_OSC_TRIM_MODE_FPHY_H_BIT				(BIT0)
#define 	CR_SR_OSC_BYPASS_MODE_FPHY_H_BIT			(BIT1)
#define		CR_SR_CFG_OSC_FPHY_H_SHIFT					(8)
#define		CR_SR_CFG_OSC_FPHY_H_MASK					(BIT_MASK(6))
#define		CR_SR_CFG_OSC_FPHY_H						(CR_SR_CFG_OSC_FPHY_H_MASK << CR_SR_CFG_OSC_FPHY_H_MASK)
#define     CR_SR_OSCREG_FPHY_H_SHIFT					(16)
#define     CR_SR_OSCREG_FPHY_H_MASK					(BIT_MASK(4))
#define     CR_SR_OSCREG_FPHY_H							(CR_SR_OSCREG_FPHY_H_MASK << CR_SR_OSCREG_FPHY_H_SHIFT)

#define R32_SYS1_SYS_OSC_HCTRL5	                    	(0x294 >> 2)
#define 	CR_SR_OSC_SELFT_RESET_FPHY_H_BIT			(BIT0)
#define 	CR_SR_OSC_SELFT_DEC_SEL_FPHY_H_BIT			(BIT8)
#define 	CR_SR_OSC_SELFT_PERIOD_FPHY_H_SHIFT			(9)
#define 	CR_SR_OSC_SELFT_PERIOD_FPHY_H_MASK			(BIT_MASK(2))
#define 	CR_SR_OSC_SELFT_PERIOD_FPHY_H				(CR_SR_OSC_SELFT_PERIOD_FPHY_H_MASK << CR_SR_OSC_SELFT_PERIOD_FPHY_H_SHIFT)
#define		CR_SR_OSC_SELFT_DEC_FPHY_H_SHIFT			(16)
#define		CR_SR_OSC_SELFT_DEC_FPHY_H_MASK				(BIT_MASK(12))
#define		CR_SR_OSC_SELFT_DEC_FPHY_H					(CR_SR_OSC_SELFT_DEC_FPHY_H_MASK << CR_SR_OSC_SELFT_DEC_FPHY_H_SHIFT)

#define R32_SYS1_SYS_OSC_HCTRL6	                    	(0x298 >> 2)
#define 	CR_SR_EN_SSC_OSC_FPHY_H_BIT					(BIT0)
#define 	CR_SR_CFG_SSC_FPHY_H_SHIFT					(8)
#define 	CR_SR_CFG_SSC_FPHY_H_MASK					(BIT_MASK(8))
#define 	CR_SR_CFG_SSC_FPHY_H						(CR_SR_CFG_SSC_FPHY_H_MASK << CR_SR_CFG_SSC_FPHY_H_SHIFT)

#define R32_SYS1_SYS_OSC_VCTRL1							(0x2A4 >> 2)
#define 	CR_SR_FG_OSC_FPHY_V_BIT						(BIT0)
#define 	CR_SR_EN_OSC_FPHY_V_BIT						(BIT8)
#define 	CR_SR_POSC_UPD_FPHY_V_BIT					(BIT16)
#define 	CR_SR_OSC_SEL_FPHY_V_SHIFT					(24)
#define 	CR_SR_OSC_SEL_FPHY_V_MASK					(BIT_MASK(3))
#define 	CR_SR_OSC_SEL_FPHY_V						(CR_SR_OSC_SEL_FPHY_V_MASK << CR_SR_OSC_SEL_FPHY_V_SHIFT)

#define R32_SYS1_SYS_OSC_VCTRL4							(0x2B0 >> 2)
#define 	CR_SR_OSC_TRIM_MODE_FPHY_V_BIT				(BIT0)
#define 	CR_SR_OSC_BYPASS_MODE_FPHY_V_BIT			(BIT1)
#define 	CR_SR_CFG_OSC_FPHY_V_SHIFT					(8)
#define 	CR_SR_CFG_OSC_FPHY_V_MASK					(BIT_MASK(6))
#define 	CR_SR_CFG_OSC_FPHY_V						(CR_SR_CFG_OSC_FPHY_V_MASK << CR_SR_CFG_OSC_FPHY_V_SHIFT)
#define 	CR_SR_CFG_OSCREG_FPHY_V_SHIFT				(16)
#define 	CR_SR_CFG_OSCREG_FPHY_V_MASK				(BIT_MASK(4))
#define 	CR_SR_CFG_OSCREG_FPHY_V						(CR_SR_CFG_OSCREG_FPHY_V_MASK << CR_SR_CFG_OSCREG_FPHY_V_SHIFT)

#define R32_SYS1_SYS_OSC_VCTRL5							(0x2B4 >> 2)
#define		CR_OSC_SELFT_RESET_FPHY_V_BIT				(BIT0)
#define 	CR_OSC_SELFT_DEC_SEL_FPHY_V_BIT				(BIT8)
#define 	CR_SR_OSC_SELFT_PERIOD_FPHY_V_SHIFT			(9)
#define 	CR_SR_OSC_SELFT_PERIOD_FPHY_V_MASK			(BIT_MASK(2))
#define 	CR_SR_OSC_SELFT_PERIOD_FPHY_V				(CR_SR_OSC_SELFT_PERIOD_FPHY_V_MASK << CR_SR_OSC_SELFT_PERIOD_FPHY_V_SHIFT)
#define 	CR_SR_OSC_SELFT_DEC_FPHY_V_SHIFT			(16)
#define 	CR_SR_OSC_SELFT_DEC_FPHY_V_MASK				(BIT_MASK(12))
#define 	CR_SR_OSC_SELFT_DEC_FPHY_V					(CR_SR_OSC_SELFT_DEC_FPHY_V_MASK << CR_SR_OSC_SELFT_DEC_FPHY_V_SHIFT)

#define R32_SYS1_SYS_OSC_VCTRL6							(0x2B8 >> 2)
#define 	CR_SR_EN_SSC_OSC_FPHY_V_BIT					(BIT0)
#define		CR_SR_CFG_SSC_FPHY_V_SHIFT					(8)
#define		CR_SR_CFG_SSC_FPHY_V_MASK					(BIT_MASK(8))
#define		CR_SR_CFG_SSC_FPHY_V						(CR_SR_CFG_SSC_FPHY_V_MASK << CR_SR_CFG_SSC_FPHY_V_SHIFT)

/*
 *  +-----------------------------------------------------------------------+
 *  |					INTERRUPT Control      							    |
 *  +-----------------------------------------------------------------------+
 */
#define SYS1_INTERRUPT_CTRL_BASE                  		(SYSTEM_PD1_REG_ADDRESS)

#define R8_SYS1_INTERRUPT_CTRL                         	((REG8  *) SYS1_INTERRUPT_CTRL_BASE)
#define R16_SYS1_INTERRUPT_CTRL                        	((REG16 *) SYS1_INTERRUPT_CTRL_BASE)
#define R32_SYS1_INTERRUPT_CTRL                        	((REG32 *) SYS1_INTERRUPT_CTRL_BASE)
#define R64_SYS1_INTERRUPT_CTRL                        	((REG64 *) SYS1_INTERRUPT_CTRL_BASE)

#define R32_SYS1_FIQ_EVENT                             	(0x300 >> 2)
#define 	NFIQ0_EVENT_SHIFT                       	(0)
#define 	NFIQ0_EVENT_MASK                       		(0xFFFFFFFF)
#define 	NFIQ0_EVENT                       			(NFIQ0_EVENT_MASK << NFIQ0_EVENT_SHIFT)

#define R32_SYS1_FIQ_MASK                              	(0x304 >> 2)
#define 	NFIQ0_EN_SHIFT								(0)
#define 	NFIQ0_EN_MASK                               (0xFFFFFFFF)
#define 	NFIQ0_EN									(NFIQ0_EN_MASK << NFIQ0_EN_SHIFT)

#define R32_SYS1_IRQ_EVENT                             	(0x308 >> 2)
#define 	NIRQ0_EVENT_SHIFT                           (0)
#define 	NIRQ0_EVENT_MASK                            (0xFFFFFFFF)
#define 	NIRQ0_EVENT                                 (NIRQ0_EVENT_MASK << NIRQ0_EVENT_SHIFT)

#define R32_SYS1_IRQ_MASK                              	(0x30C >> 2)
#define 	NIRQ0_EN_SHIFT                              (0)
#define 	NIRQ0_EN_MASK                               (0xFFFFFFFF)
#define 	NIRQ0_EN                               		(NIRQ0_EN_MASK << NIRQ0_EN_SHIFT)

#define		INTERRUPT_EVENT_APU							(BIT0)
#define		INTERRUPT_EVENT_PCIE						(BIT1)
#define		INTERRUPT_EVENT_DBUF						(BIT2)
#define		INTERRUPT_EVENT_D2H							(BIT3)
#define		INTERRUPT_EVENT_COP1_ST3C					(BIT4)
#define		INTERRUPT_EVENT_COP1						(BIT5)
#define		INTERRUPT_EVENT_BMU							(BIT6)
#define		INTERRUPT_EVENT_MR							(BIT7)
#define		INTERRUPT_EVENT_SEC_PERR					(BIT8)
#define		INTERRUPT_EVENT_XZIP_PERR					(BIT9)
#define		INTERRUPT_EVENT_DMAC						(BIT10)
#define		INTERRUPT_EVENT_PMU							(BIT11)
#define		INTERRUPT_EVENT_TS							(BIT12)
#define		INTERRUPT_EVENT_VDT							(BIT13)
#define		INTERRUPT_EVENT_GPIO						(BIT14)
#define		INTERRUPT_EVENT_PERSTN						(BIT15)
#define		INTERRUPT_EVENT_FLH_INT_WRAP				(BIT16)
#define		INTERRUPT_EVENT_ATCM						(BIT17)
#define		INTERRUPT_EVENT_COP1_PARITY_ERR				(BIT18)
#define		INTERRUPT_EVENT_AHBX						(BIT19)
#define		INTERRUPT_EVENT_MS_BASED					(BIT20)
#define		INTERRUPT_EVENT_US_BASED					(BIT21)
#define		INTERRUPT_EVENT_AXI							(BIT22)
#define		INTERRUPT_EVENT_PIC_UART					(BIT23)
#define		INTERRUPT_EVENT_PIC_SMBUS					(BIT24)
#define		INTERRUPT_EVENT_PIC_I2C						(BIT25)
#define		INTERRUPT_EVENT_COP0_INT0					(BIT26)
#define		INTERRUPT_EVENT_COP0_INT1					(BIT27)
#define		INTERRUPT_EVENT_COP0_INT2					(BIT28)
#define		INTERRUPT_EVENT_FIP_ERR						(BIT29)
#define		INTERRUPT_EVENT_RS_ERR						(BIT30)
#define		INTERRUPT_EVENT_RS							(BIT31)

/*
 *  +-----------------------------------------------------------------------+
 *  |					TIMER Control      								    |
 *  +-----------------------------------------------------------------------+
 */
#define SYS1_TIMER_CTR_BASE                         	(SYSTEM_PD1_REG_ADDRESS)

#define R8_SYS1_TIMER_CTRL                          	((REG8  *) SYS1_TIMER_CTR_BASE)
#define R16_SYS1_TIMER_CTRL                         	((REG16 *) SYS1_TIMER_CTR_BASE)
#define R32_SYS1_TIMER_CTRL                         	((REG32 *) SYS1_TIMER_CTR_BASE)
#define R64_SYS1_TIMER_CTRL                         	((REG64 *) SYS1_TIMER_CTR_BASE)

#define R32_SYS1_UW_TIMER_CFG                          	(0x340 >> 2)
#define 	UW_TIMER_MS_ENB_BIT                         (BIT0)
#define 	UW_TIMER_US_ENB_BIT                         (BIT8)

#define R32_SYS1_DW_TIMER_CFG                          	(0x344 >> 2)
#define 	MS1_DW_TIMER_REP_ENB_BIT                    (BIT0)
#define 	MS2_DW_TIMER_REP_ENB_BIT                    (BIT8)
#define 	MS3_DW_TIMER_REP_ENB_BIT                    (BIT16)
#define 	US_DW_TIMER_REP_ENB_BIT                     (BIT24)

#define R32_SYS1_MS_UW_TIMER                           	(0x348 >> 2)
#define 	UP_TIMER_MS_SHIFT							(0)
#define 	UP_TIMER_MS_MASK							(BIT_MASK(32))
#define 	UP_TIMER_MS									(UP_TIMER_MS_MASK << UP_TIMER_MS_SHIFT)

#define R32_SYS1_US_UW_TIMER                           	(0x34C >> 2)
#define 	UP_TIMER_US_SHIFT							(0)
#define 	UP_TIMER_US_MASK							(BIT_MASK(32))
#define 	UP_TIMER_US									(UP_TIMER_US_MASK << UP_TIMER_US_SHIFT)

#define R32_SYS1_MS_DW_TIMER1                          	(0x350 >> 2)
#define 	DOWN_TIMER_MS1_SHIFT						(0)
#define 	DOWN_TIMER_MS1_MASK							(BIT_MASK(32))
#define 	DOWN_TIMER_MS1								(DOWN_TIMER_MS1_MASK << DOWN_TIMER_MS1_SHIFT)

#define R32_SYS1_MS_DW_TIMER2                          	(0x354 >> 2)
#define 	DOWN_TIMER_MS2_SHIFT						(0)
#define 	DOWN_TIMER_MS2_MASK							(BIT_MASK(32))
#define 	DOWN_TIMER_MS2								(DOWN_TIMER_MS2_MASK << DOWN_TIMER_MS2_SHIFT)

#define R32_SYS1_MS_DW_TIMER3                          	(0x358 >> 2)
#define 	DOWN_TIMER_MS3_SHIFT						(0)
#define 	DOWN_TIMER_MS3_MASK							(BIT_MASK(32))
#define 	DOWN_TIMER_MS3								(DOWN_TIMER_MS3_MASK << DOWN_TIMER_MS3_SHIFT)

#define R32_SYS1_US_DW_TIMER                           	(0x35C >> 2)
#define 	DOWN_TIMER_US_SHIFT							(0)
#define 	DOWN_TIMER_US_MAKS							(BIT_MASK(32))
#define 	DOWN_TIMER_US								(DOWN_TIMER_US_MAKS << DOWN_TIMER_US_SHIFT)

#define R32_SYS1_MS_DW_TIMER_SRC  		                (0x360 >> 2)
#define 	DOWN_TIMER_MS1_INT_BIT                      (BIT0)
#define 	DOWN_TIMER_MS2_INT_BIT                      (BIT8)
#define 	DOWN_TIMER_MS3_INT_BIT                      (BIT16)

#define R32_SYS1_TIMER_BASE                            	(0x364 >> 2)
#define 	TIMER_BASE_UNIT_SHIFT                       (0)
#define 	TIMER_BASE_UNIT_MASK						(BIT_MASK(9))
#define 	TIMER_BASE_UNIT								(TIMER_BASE_UNIT_MASK << TIMER_BASE_UNIT_SHIFT)

/*
 *  +-----------------------------------------------------------------------+
 *  |					ANALOG Control      								|
 *  +-----------------------------------------------------------------------+
 */
#define SYS1_ANALOG_CTRL_BASE                         	(SYSTEM_PD1_REG_ADDRESS)

#define R8_SYS1_ANALOG_CTRL                           	((REG8  *) SYS1_ANALOG_CTRL_BASE)
#define R16_SYS1_ANALOG_CTRL                          	((REG16 *) SYS1_ANALOG_CTRL_BASE)
#define R32_SYS1_ANALOG_CTRL                          	((REG32 *) SYS1_ANALOG_CTRL_BASE)
#define R64_SYS1_ANALOG_CTRL                          	((REG64 *) SYS1_ANALOG_CTRL_BASE)

#define R32_SYS1_ANALOG_NOISE_GEN_CTRL2                 (0x398 >> 2)
#define 	CR_RNG_EN_BIT		                        (BIT0)
#define R16_SYS1_ANALOG_NOISE_GEN_CFG                 	(0x39A >> 1)
#define 	CR_RNG_CFG_SHIFT                            (0)
#define 	CR_RNG_CFG_MASK								(BIT_MASK(9))
#define 	CR_RNG_CFG									(CR_RNG_CFG_MASK << CR_RNG_CFG_SHIFT)

#define R32_SYS1_ANALOG_NOISE_GEN_CTRL4                	(0x3A0 >> 2)
#define     RNG_REG_G25_SYNC_SHIFT                      (0)
#define     RNG_REG_G25_SYNC_MASK						(BIT_MASK(32))
#define     RNG_REG_G25_SYNC							(RNG_REG_G25_SYNC_MASK << RNG_REG_G25_SYNC_SHIFT)

#define R32_SYS1_ANALOG_NOISE_GEN_CTRL5                 (0x3A4 >> 2)
#define 	NEW_DATA_RDY_G25_SYNC_BIT                   (BIT0)

#define R32_SYS1_ANALOG_NOISE_GEN_CTRL6                 (0x3A8 >> 2) // useful when enable LFSR(CR_RNG_EN_G25 = 1)
#define 	CR_RNG_REG_IN_G25_SHIFT                     (0)
#define 	CR_RNG_REG_IN_G25_MASK						(BIT_MASK(32))
#define 	CR_RNG_REG_IN_G25							(CR_RNG_REG_IN_G25_MASK << CR_RNG_REG_IN_G25_SHIFT)

#define R32_SYS1_ANALOG_NOISE_GEN_CTRL7                 (0x3AC >> 2)
#define 	CR_RNG_EN_G25_BIT                           (BIT0)
#define 	CR_RNG_MD_G25_BIT                           (BIT8)
#define 	CR_RNG_BYTE_CLR_G25_BIT                     (BIT16)
#define 	CR_RNG_STB_PER_G25_SHIFT                    (24)
#define 	CR_RNG_STB_PER_G25_MASK						(BIT_MASK(6))
#define 	CR_RNG_STB_PER_G25							(CR_RNG_STB_PER_G25_MASK << CR_RNG_STB_PER_G25_SHIFT)

/*
 *  +-----------------------------------------------------------------------+
 *  |					PAD1 IP Control      								|
 *  +-----------------------------------------------------------------------+
 */
#define SYS1_IP_CTRL_BASE                           	(SYSTEM_PD1_REG_ADDRESS)

#define R8_SYS1_IP                                  	((REG8  *) SYS1_IP_CTRL_BASE)
#define R16_SYS1_IP                                 	((REG16 *) SYS1_IP_CTRL_BASE)
#define R32_SYS1_IP                                 	((REG32 *) SYS1_IP_CTRL_BASE)
#define R64_SYS1_IP                                 	((REG64 *) SYS1_IP_CTRL_BASE)

#define R32_SYS1_IP_DMAC_PCA_VB_POS                    	(0x400 >> 2)
#define 	CR_DMAC_PCA_VB_POS_SHIFT					(0)
#define 	CR_DMAC_PCA_VB_POS_MASK						(BIT_MASK(32))
#define 	CR_DMAC_PCA_VB_POS							(CR_DMAC_PCA_VB_POS_MASK << CR_DMAC_PCA_VB_POS_SHIFT)
#define 	PCA_VB_POS_START_SHIFT                      (0)
#define 	PCA_VB_POS_START_MASK                       (BIT_MASK(5))
#define 	PCA_VB_POS_START                       		(PCA_VB_POS_START_MASK << PCA_VB_POS_START_SHIFT)
#define     PCA_VB_POS_LENGTH_SHIFT						(8)
#define 	PCA_VB_POS_LENGTH_MASK                      (BIT_MASK(4))
#define 	PCA_VB_POS_LENGTH                      		(PCA_VB_POS_LENGTH_MASK << PCA_VB_POS_LGNGTH_SHIFT)

#define R32_SYS1_IP_DMAC_MR_TRG_RMP0                   	(0x404 >> 2)
#define 	DMAC_HIGH_MR_ID_SHIFT						(0)
#define 	DMAC_HIGH_MR_ID_MASK						(BIT_MASK(8))
#define 	DMAC_HIGH_MR_ID								(DMAC_HIGH_MR_ID_MASK << DMAC_HIGH_MR_ID_SHIFT)
#define 	DMAC_NOR_MR_ID_SHIFT						(8)
#define 	DMAC_NOR_MR_ID_MASK							(BIT_MASK(8))
#define 	DMAC_NOR_MR_ID								(DMAC_NOR_MR_ID_MASK << DMAC_NOR_MR_ID_SHIFT)
#define 	COP1_MR_ID_SHIFT							(16)
#define 	COP1_MR_ID_MASK								(BIT_MASK(8))
#define 	COP1_MR_ID									(COP1_MR_ID_MASK << COP1_MR_ID_SHIFT)
#define 	MERGE_MR_ID_SHIFT							(24)
#define 	MERGE_MR_ID_MASK							(BIT_MASK(8))
#define 	MERGE_MR_ID									(MERGE_MR_ID_MASK << MERGE_MR_ID_SHIFT)

#define R32_SYS1_IP_DMAC_MR_TRG_RMP1                   	(0x408 >> 2)
#define 	BMU_MR_ID_SHIFT								(0)
#define 	BMU_MR_ID_MASK								(BIT_MASK(8))
#define 	BMU_MR_ID									(BMU_MR_ID_MASK << BMU_MR_ID_SHIFT)
#define 	XZIP_MR_ID_SHIFT							(8)
#define 	XZIP_MR_ID_MASK								(BIT_MASK(8))
#define 	XZIP_MR_ID									(XZIP_MR_ID_MASK << XZIP_MR_ID_SHIFT)
#define 	AES_MR_ID_SHIFT								(16)
#define 	AES_MR_ID_MASK								(BIT_MASK(8))
#define 	AES_MR_ID									(AES_MR_ID_MASK << AES_MR_ID_SHIFT)

#define R32_SYS1_IP_DMAC_REG                           	(0x40C >> 2)  //ps5013: 0080_3050 PD1/MISCH
#define 	SR_DMAC_IDLE_SHIFT							(0)
#define 	SR_DMAC_IDLE_MASK							(BIT_MASK(8))
#define 	SR_DMAC_IDLE								(SR_DMAC_IDLE_MASK << SR_DMAC_IDLE_SHIFT)
#define 	SR_DMA_IDLE									(SR_DMAC_IDLE)
#define 	DMAC_PCA_ZINFO_POS_SHIFT					(8)
#define 	DMAC_PCA_ZINFO_POS_MASK						(BIT_MASK(5))
#define 	DMAC_PCA_ZINFO_POS							(DMAC_PCA_ZINFO_POS_MASK << DMAC_PCA_ZINFO_POS_SHIFT)
#define 	CR_DMAC_PCA_ZINFO_LEN_SHIFT					(13)
#define 	CR_DMAC_PCA_ZINFO_LEN_MASK					(BIT_MASK(2))
#define 	CR_DMAC_PCA_ZINFO_LEN						(CR_DMAC_PCA_ZINFO_LEN_MASK << CR_DMAC_PCA_ZINFO_LEN_SHIFT)
#define 	CR_SYS_DMAC_RESUME_SHIFT					(16)
#define 	CR_SYS_DMAC_RESUME_MASK						(BIT_MASK(2))
#define 	CR_SYS_DMAC_RESUME							(CR_SYS_DMAC_RESUME_MASK << CR_SYS_DMAC_RESUME_SHIFT)
#define 	CR_SYS_DMAC_ABORT_SHIFT						(16)
#define 	CR_SYS_DMAC_ABORT_MASK						(BIT_MASK(2))
#define 	CR_SYS_DMAC_ABORT							(CR_SYS_DMAC_ABORT_MASK << CR_SYS_DMAC_ABORT_SHIFT)
#define 	CR_DMAC_RRC_PASS_INJ_BIT					(BIT24)
#define 	CR_DMAC_RRC_LS_EN_BIT						(BIT25)
#define 	CR_DMAC_RRC_CFG_SHIFT						(26)
#define 	CR_DMAC_RRC_CFG_MASK						(BIT_MASK(3))
#define 	CR_DMAC_RRC_CFG								(CR_DMAC_RRC_CFG_MASK << CR_DMAC_RRC_CFG_SHIFT)
#define 	CR_DMAC_ERR_STALL_EN_BIT					(BIT31)

#define R32_SYS1_IP_DMAC_ERR_STS                       	(0x410 >> 2)  //ps5013: 0080_3090 PD1/MISCH
#define 	CR_DMAC_ERR_STS_SHIFT						(0)
#define 	CR_DMAC_ERR_STS_MASK						(BIT_MASK(32))
#define 	CR_DMAC_ERR_STS								(CR_DMAC_ERR_STS_MASK << CR_DMAC_ERR_STS_SHIFT)

#define R32_SYS1_IP_DMAC_ERR_SEL                       	(0x414 >> 2)  //ps5013: 0080_3094 PD1/MISCH
#define 	CR_DMAC_ERR_SEL_SHIFT                       (0)
#define 	CR_DMAC_ERR_SEL_MASK						(BIT_MASK(8))
#define 	CR_DMAC_ERR_SEL								(CR_DMAC_ERR_SEL_MASK << CR_DMAC_ERR_SEL_SHIFT)

#define R32_SYS1_IP_DMAC_PERR_STS                      	(0x418 >> 2)  //ps5013: 0080_3098 PD1/MISCH
#define 	SR_DMAC0_PERR_ADDR_SHIFT					(0)
#define 	SR_DMAC0_PERR_ADDR_MASK						(BIT_MASK(7))
#define 	SR_DMAC0_PERR_ADDR							(SR_DMAC0_PERR_ADDR_MASK << SR_DMAC0_PERR_ADDR_SHIFT)
#define 	SR_DMAC0_PERR_VLD_BIT                       (BIT7)
#define 	SR_DMAC1_PERR_ADDR_SHIFT					(8)
#define 	SR_DMAC1_PERR_ADDR_MASK						(BIT_MASK(7))
#define 	SR_DMAC1_PERR_ADDR							(SR_DMAC1_PERR_ADDR_MASK << SR_DMAC1_PERR_ADDR_SHIFT)
#define 	SR_DMAC1_PERR_VLD_BIT                       (BIT15)
#define 	SR_DMAC2_PERR_ADDR_SHIFT					(16)
#define 	SR_DMAC2_PERR_ADDR_MASK						(BIT_MASK(7))
#define 	SR_DMAC2_PERR_ADDR							(SR_DMAC2_PERR_ADDR_MASK << SR_DMAC2_PERR_ADDR_SHIFT)
#define 	SR_DMAC2_PERR_VLD_BIT                       (BIT23)
#define 	SR_DMAC0_PERR_CLR_BIT                       (BIT24)
#define 	SR_DMAC1_PERR_CLR_BIT                       (BIT25)
#define 	SR_DMAC2_PERR_CLR_BIT                       (BIT26)

#define R32_SYS1_IP_PCIE_PMU_STS                       	(0x420 >> 2)
#define 	PCI_PMU_L0_BIT                              (BIT0)
#define 	PCI_PMU_L11_BIT                             (BIT1)
#define 	PCI_PMU_L12_IDLE_BIT                        (BIT2)
#define 	PCI_PMU_L12_ENTRY_BIT                       (BIT3)
#define 	PCI_PMU_L12_EXIT_BIT                        (BIT4)

#define R32_SYS1_IP_AHB_INT_SET                        	(0x440 >> 2)
#define 	AHBX_INT_MASK_SHIFT                         (0)
#define 	AHBX_INT_MASK_MASK							(BIT_MASK(4))
#define 	AHBX_INT_MASK								(AHBX_INT_MASK_MASK << AHBX_INT_MASK_SHIFT)
#define 	AXBX_INT_STS_SHIFT                          (8)
#define 	AXBX_INT_STS_MASK							(BIT_MASK(4))
#define 	AXBX_INT_STS								(AXBX_INT_STS_MASK << AXBX_INT_STS_SHIFT)

#define R32_SYS1_IP_AHB_TOUT_SET                       	(0x444 >> 2)
#define 	AHBX_TOUT_CHK_BIT                           (BIT0)
#define 	AHBX_TOUT_CLR_BIT                           (BIT8)
#define 	AHBX_TIMR_TOUT_SHIFT						(16)
#define 	AHBX_TIMR_TOUT_MASK							(BIT_MASK(2))
#define 	AHBX_TIMR_TOUT								(AHBX_TIMR_TOUT_MASK << AHBX_TIMR_TOUT_SHIFT)

#define R32_SYS1_IP_AHB_TOUT_ADR                       	(0x448 >> 2)
#define 	AHBX_TOUT_ADR_SHIFT							(0)
#define 	AHBX_TOUT_ADR_MASK							(BIT_MASK(32))
#define 	AHBX_TOUT_ADR								(AHBX_TOUT_ADR_MASK << AHBX_TOUT_ADR_SHIFT)

#define R32_SYS1_IP_AXI_INT_MASK                       	(0x450 >> 2)
#define 	AXIS_TOUT_EVT_MASK_BIT                      (BIT0)
#define 	AXIM_TOUT_EVT_MASK_BIT                      (BIT1)
#define 	AXIM_RNGE_EVT_MASK_BIT                      (BIT2)
#define 	AXIW_PRCT_EVT_MASK_BIT                      (BIT3)
#define 	AXIMON_CPU_ACCESS_EVT_MASK_BIT              (BIT4)
#define 	AXIMON_AESH_ACCESS_EVT_MASK_BIT             (BIT5)
#define 	AXIMON_DBUF2_ACCESS_EVT_MASK_BIT            (BIT6)
#define 	AXIMON_DBUF3_ACCESS_EVT_MASK_BIT            (BIT7)
#define 	DBUF0_WTOUT_EVT_MASK_BIT            		(BIT8)
#define 	DBUF1_WTOUT_EVT_MASK_BIT            		(BIT9)
#define 	DBUF2_WTOUT_EVT_MASK_BIT            		(BIT10)
#define 	DBUF3_WTOUT_EVT_MASK_BIT            		(BIT11)
#define 	DBUF0_RTOUT_EVT_MASK_BIT            		(BIT12)
#define 	DBUF1_RTOUT_EVT_MASK_BIT            		(BIT13)
#define 	DBUF2_RTOUT_EVT_MASK_BIT            		(BIT14)
#define 	DBUF3_RTOUT_EVT_MASK_BIT            		(BIT15)
#define 	ZIP_AXIS_WCH_TOUT_MASK_BIT					(BIT16)
#define 	ZIP_AXIS_RCH_TOUT_MASK_BIT					(BIT17)
#define 	D2H_AXI_SLV_TIMEOUT_MASK_BIT				(BIT18)
#define 	SEC_AESB_AXI_SLV_TIMEOUT_MASK_BIT			(BIT19)
#define 	SEC_AESH_AXI_SLV_TIMEOUT_MASK_BIT			(BIT20)
#define 	COP1_AXI_SLV_TIMEOUT_MASK_BIT				(BIT21)
#define 	PIC_AXI_SLV_TIMEOUT_MASK_BIT				(BIT22)
#define 	HOST_AXIS_HANG_INTERRUPT_MASK_BIT			(BIT23)
#define 	AXIM_WTOUT_EVT_MASK_BIT						(BIT24)
#define 	AXIM_RTOUT_EVT_MASK_BIT						(BIT25)
#define 	COP0_WRITE_DETECT_MASK_BIT					(BIT28)
#define 	COP1_WRITE_DETECT_MASK_BIT					(BIT29)
#define 	BMU_WRITE_DETECT_MASK_BIT					(BIT30)

#define 	AXI_INT_MASK_ALL                            (AXIS_TOUT_EVT_MASK_BIT | AXIM_TOUT_EVT_MASK_BIT | AXIM_RNGE_EVT_MASK_BIT | AXIW_PRCT_EVT_MASK_BIT | \
														AXIMON_CPU_ACCESS_EVT_MASK_BIT | AXIMON_AESH_ACCESS_EVT_MASK_BIT | AXIMON_DBUF2_ACCESS_EVT_MASK_BIT | \
														AXIMON_DBUF3_ACCESS_EVT_MASK_BIT | DBUF0_WTOUT_EVT_MASK_BIT | DBUF1_WTOUT_EVT_MASK_BIT | \
														DBUF2_WTOUT_EVT_MASK_BIT | DBUF3_WTOUT_EVT_MASK_BIT | DBUF0_RTOUT_EVT_MASK_BIT | \
														DBUF1_RTOUT_EVT_MASK_BIT | DBUF2_RTOUT_EVT_MASK_BIT | DBUF3_RTOUT_EVT_MASK_BIT | \
														ZIP_AXIS_WCH_TOUT_MASK_BIT | ZIP_AXIS_RCH_TOUT_MASK_BIT | D2H_AXI_SLV_TIMEOUT_MASK_BIT | \
														SEC_AESB_AXI_SLV_TIMEOUT_MASK_BIT | SEC_AESH_AXI_SLV_TIMEOUT_MASK_BIT | COP1_AXI_SLV_TIMEOUT_MASK_BIT | \
														PIC_AXI_SLV_TIMEOUT_MASK_BIT | HOST_AXIS_HANG_INTERRUPT_MASK_BIT | AXIM_WTOUT_EVT_MASK_BIT | \
														AXIM_RTOUT_EVT_MASK_BIT | COP0_WRITE_DETECT_MASK_BIT | COP1_WRITE_DETECT_MASK_BIT | \
														BMU_WRITE_DETECT_MASK_BIT)


#define R32_SYS1_IP_DBUF1_CTRL                         	(0x460 >> 2)  //ps5013: 0080_3004 PD1/MISCH
#define		CR_DBUF_PERR_INJ_SHIFT						(0)
#define		CR_DBUF_PERR_INJ_MASK						(BIT_MASK(4))
#define		CR_DBUF_PERR_INJ							(CR_DBUF_PERR_INJ_MASK << CR_DBUF_PERR_INJ_SHIFT)
#define		CR_DBUF_RRC_CFG_SHIFT						(10)
#define		CR_DBUF_RRC_CFG_MASK						(BIT_MASK(3))
#define		CR_DBUF_RRC_CFG								(CR_DBUF_RRC_CFG_MASK << CR_DBUF_RRC_CFG_SHIFT)
#define 	CR_DBUF_PERR_CHK_EN_BIT						(BIT14)
#define		CR_DBUF_CFG_SHIFT							(16)
#define		CR_DBUF_CFG_MASK							(BIT_MASK(9))
#define		CR_DBUF_CFG									(CR_DBUF_CFG_MASK << CR_DBUF_CFG_SHIFT)

#define R32_SYS1_IP_DBUF1_CTRL1                        	(0x464 >> 2)
#define 	CR_DBUF_PD1_INIT_START_BIT            		(BIT0)

#define R32_SYS1_IP_DBUF1_CTRL2                        	(0x468 >> 2)
#define 	CR_DBUF_TOUT_CHK_BIT                        (BIT0)
#define 	CR_DBUF_TIMER_TOUT_SHIFT					(8)
#define 	CR_DBUF_TIMER_TOUT_MASK						(BIT_MASK(2))
#define 	CR_DBUF_TIMER_TOUT							(CR_DBUF_TIMER_TOUT_MASK << CR_DBUF_TIMER_TOUT_SHIFT)

#define R32_SYS1_IP_APU_CFG	                            (0x470 >> 2)  //ps5013: 0080_3018 PD1/MISCH
#define 	CR_APU_RRC_CFG_SHIFT						(0)
#define 	CR_APU_RRC_CFG_MASK							(BIT_MASK(21))
#define 	CR_APU_RRC_CFG                              (CR_APU_RRC_CFG_MASK << CR_APU_RRC_CFG_SHIFT)

#define R32_SYS1_IP_APU_TSA	                            (0x474 >> 2)
#define 	CR_IP_TSA_EN_BIT							(BIT0)

#define R32_SYS1_DZIP_CFG                              	(0x480 >> 2)  //ps5013: 0080_6000 DZIP_REG/ZIP_CFG
#define 	CFG_BIG_ENDIAN_BIT                          (BIT0)
#define 	ZIP_RESOLUTION_MODE_SHIFT					(8)
#define 	ZIP_RESOLUTION_MODE_MASK					(BIT_MASK(2))
#define 	ZIP_RESOLUTION_MODE							(ZIP_RESOLUTION_MODE_MASK << ZIP_RESOLUTION_MODE_SHIFT)

#define R32_SYS1_DZIP_ERR_HND                          	(0x484 >> 2)  //ps5013: 0080_6004 DZIP_REG/ZIP_ERR_HND
#define 	FW_HDL_END_BIT                              (BIT8)
#define 	ZIP_AXIS_TOUT_ENB_BIT                       (BIT16)
#define 	ZIP_AXIS_TOUT_FRC_BIT                       (BIT17)
#define 	ZIP_AXIS_TOUT_CNT_SHIFT						(24)
#define 	ZIP_AXIS_TOUT_CNT_MASK						(BIT_MASK(4))
#define 	ZIP_AXIS_TOUT_CNT                           (ZIP_AXIS_TOUT_CNT_MASK << ZIP_AXIS_TOUT_CNT_SHIFT)

#define R32_SYS1_DZIP_RN_ZINFO                         	(0x488 >> 2)  //ps5013: 0080_6008 DZIP_REG/ZIP_RN_ZINFO
#define 	FW_RAND_ZINFO_EN_BIT                        (BIT0)

#define R32_SYS1_DZIP_RAM_CFG        	                (0x490 >> 2)  //ps5013: 0080_6010 DZIP_REG/ZIP_RAM_CFG
#define 	RAM_PARITY_ERR_INJ_BIT                      (BIT0)
#define 	LZSSRAM_RRC_CFG_SHIFT						(19)
#define 	LZSSRAM_RRC_CFG_MASK						(BIT_MASK(3))
#define 	LZSSRAM_RRC_CFG                             (LZSSRAM_RRC_CFG_MASK << LZSSRAM_RRC_CFG_SHIFT)

#define 	ROWDRAM_RRC_CFG_SHIFT						(27)
#define 	ROWDRAM_RRC_CFG_MASK						(BIT_MASK(3))
#define 	ROWDRAM_RRC_CFG                             (ROWDRAM_RRC_CFG_MASK << ROWDRAM_RRC_CFG_SHIFT)

#define R32_SYS1_DZIP_DBG1                             	(0x498 >> 2)  //ps5013: 0080_6018 DZIP_REG/ZIP_DBG1
#define 	DZIP_DBG_SEL_SHIFT							(0)
#define 	DZIP_DBG_SEL_MASK							(BIT_MASK(8))
#define 	DZIP_DBG_SEL	                            (DZIP_DBG_SEL_MASK >> DZIP_DBG_SEL_SHIFT)
#define 	DZIP_DEBUG_SEL_REG_BIT                      (BIT8)
#define 	DZIP_DBG_PORT_SHIFT							(16)
#define 	DZIP_DBG_PORT_MASK							(BIT_MASK(16))
#define 	DZIP_DBG_PORT                         		(DZIP_DBG_PORT_MASK << DZIP_DBG_PORT_SHIFT)

#define R32_SYS1_DZIP_DBG2                             	(0x49C >> 2)  //ps5013: 0080_601c DZIP_REG/ZIP_DBG2
#define 	ZIP_DBUG_PORT_SHIFT							(0)
#define 	ZIP_DBUG_PORT_MASK							(BIT_MASK(32))
#define 	ZIP_DBUG_PORT								(ZIP_DBUG_PORT_MASK << ZIP_DBUG_PORT_SHIFT)

#define R32_SYS1_AXI_INT_SET                           	(0x4A8 >> 2)
#define 	AXIS_TOUT_EVT_BIT                           (BIT0)
#define 	AXIM_TOUT_EVT_BIT                           (BIT1)
#define 	AXIM_RNGE_EVT_BIT                           (BIT2)
#define 	AXIW_PRCT_EVT_BIT                           (BIT3)

#define R32_SYS1_AXI_STOUT_SRC                         	(0x4AC >> 2)
#define 	READY_HANG_ID_SHIFT							(0)
#define 	READY_HANG_ID_MASK							(BIT_MASK(8))
#define 	READY_HANG_ID                               (READY_HANG_ID_MASK << READY_HANG_ID_SHIFT)
#define 	READY_HANG_FLG_SHIFT						(8)
#define 	READY_HANG_FLG_MASK							(BIT_MASK(8))
#define 	READY_HANG_FLG                              (READY_HANG_FLG_MASK << READY_HANG_FLG_SHIFT)
#define 	ZIP_AXIS_WCH_TOUT_BIT                       (BIT16)
#define 	ZIP_AXIS_RCH_TOUT_BIT                       (BIT17)
#define 	D2H_AXI_SLV_TOUT_BIT                        (BIT18)
#define 	SEC_AESH_D2H_AXIS_TOUT_BIT                  (BIT19)
#define 	SEC_AESB_ZIP_AXIS_TOUT_BIT                  (BIT20)
#define 	COP1_AXIS_TOUT_BIT                          (BIT21)
#define 	PIC_AXI_SLV_TOUT_BIT                        (BIT22)
#define 	HOST_AXIS_HANG_INTERRUPT_BIt                (BIT23)

#define R32_SYS1_AXI_STOUT_ADR1                        	(0x4B0 >> 2)
#define 	READY_HANG_ADDR_SHIFT						(0)
#define 	READY_HANG_ADDR_MASK						(BIT_MASK(32))
#define 	READY_HANG_ADDR                        		(READY_HANG_ADDR_MASK << READY_HANG_ADDR_SHIFT)

#define R32_SYS1_AXI_STOUT_ADR2                        	(0x4B4 >> 2)
#define 	ZIP_AXIS_WCH_TADR_SHIFT						(0)
#define 	ZIP_AXIS_WCH_TADR_MASK						(BIT_MASK(32))
#define 	ZIP_AXIS_WCH_TADR                           (ZIP_AXIS_WCH_TADR_MASK << ZIP_AXIS_WCH_TADR_SHIFT)

#define R32_SYS1_AXI_STOUT_ADR3                        	(0x4B8 >> 2)
#define 	ZIP_AXIS_RCH_TADR_SHIFT						(0)
#define 	ZIP_AXIS_RCH_TADR_MASK						(BIT_MASK(32))
#define 	ZIP_AXIS_RCH_TADR                           (ZIP_AXIS_RCH_TADR_MASK << ZIP_AXIS_RCH_TADR_SHIFT)

#define R32_SYS1_AXI_STOUT_ADR4                        	(0x4BC >> 2)
#define 	D2H_AXI_SLV_ERR_ADDR_SHIFT					(0)
#define 	D2H_AXI_SLV_ERR_ADDR_MASK					(BIT_MASK(32))
#define 	D2H_AXI_SLV_ERR_ADDR                        (D2H_AXI_SLV_ERR_ADDR_MASK << D2H_AXI_SLV_ERR_ADDR_SHIFT)

#define R32_SYS1_AXI_STOUT_ADR5                        	(0x4C0 >> 2)
#define 	SEC_AESH_AXI_SLV_ERR_ADDR_SHIFT				(0)
#define 	SEC_AESH_AXI_SLV_ERR_ADDR_MASK				(BIT_MASK(32))
#define 	SEC_AESH_AXI_SLV_ERR_ADDR                   (SEC_AESH_AXI_SLV_ERR_ADDR_MASK << SEC_AESH_AXI_SLV_ERR_ADDR_SHIFT)

#define R32_SYS1_AXI_STOUT_ADR6                        	(0x4C4 >> 2)
#define 	SEC_AESH_AXI_SLV_ERR_ADDR_SHIFT				(0)
#define 	SEC_AESH_AXI_SLV_ERR_ADDR_MASK				(BIT_MASK(32))
#define 	SEC_AESH_AXI_SLV_ERR_ADDR                   (SEC_AESH_AXI_SLV_ERR_ADDR_MASK << SEC_AESH_AXI_SLV_ERR_ADDR_SHIFT)
//VIC99 4C0 = 4C4?

#define R32_SYS1_AXI_STOUT_ADR7                        	(0x4C8 >> 2)
#define 	COP1_AXI_SLV_ERR_ADDR_SHIFT					(0)
#define 	COP1_AXI_SLV_ERR_ADDR_MASK					(BIT_MASK(32))
#define 	COP1_AXI_SLV_ERR_ADDR                       (COP1_AXI_SLV_ERR_ADDR_MASK << COP1_AXI_SLV_ERR_ADDR_SHIFT)

#define R32_SYS1_AXI_HSTOUT_CFG                        	(0x4CC >> 2)
#define 	CR_HOST_AXIS_INTERRUPT_EN_BIT               (BIT0)
#define 	CR_HOST_AXIS_HANG_INTERRUPT_CLR_BIT         (BIT8)
#define 	CR_HOST_AXIS_UNLOCK_EN_BIT                  (BIT16)

#define R32_SYS1_AXI_MTOUT_SRC                         	(0x4D0 >> 2)
#define 	AXI_TIMEOUT_WID_SHIFT						(0)
#define 	AXI_TIMEOUT_WID_MASK						(BIT_MASK(4))
#define 	AXI_TIMEOUT_WID                             (AXI_TIMEOUT_WID_MASK << AXI_TIMEOUT_WID_SHIFT)
#define 	AXI_TIMEOUT_RID_SHIFT						(8)
#define 	AXI_TIMEOUT_RID_MASK						(4)
#define 	AXI_TIMEOUT_RID                             (AXI_TIMEOUT_RID_MASK << AXI_TIMEOUT_RID_SHIFT)
#define 	AXIM_WTOUT_EVT_BIT                          (BIT16)
#define 	AXIM_RTOUT_EVT_BIT                          (BIT17)

#define R32_SYS1_AXI_MTOUT_WCFG                        	(0x4D4 >> 2)
#define     AXI_TIMEOUT_WCNT_SHIFT						(0)
#define     AXI_TIMEOUT_WCNT_MASK						(BIT_MASK(20))
#define     AXI_TIMEOUT_WCNT							(AXI_TIMEOUT_WCNT_MASK << AXI_TIMEOUT_WCNT_SHIFT)

#define R32_SYS1_AXI_MTOUT_RCFG                        	(0x4D8 >> 2)
#define     AXI_TIMEOUT_RCNT_SHIFT						(0)
#define     AXI_TIMEOUT_RCNT_MASK						(BIT_MASK(20))
#define     AXI_TIMEOUT_RCNT							(AXI_TIMEOUT_RCNT_MASK << AXI_TIMEOUT_RCNT_SHIFT)

#define R32_SYS1_AXI_RANGE_SRC                         	((0x4DC) >> 2)
#define     AXIMON_CPU_ACCESS_ID_SHIFT					(0)
#define     AXIMON_CPU_ACCESS_ID_MASK					(BIT_MASK(4))
#define     AXIMON_CPU_ACCESS_ID						(AXIMON_CPU_ACCESS_ID_MASK << AXIMON_CPU_ACCESS_ID_SHIFT)
#define     AXIMON_AESH_ACCESS_ID_SHIFT					(4)
#define     AXIMON_AESH_ACCESS_ID_MASK					(BIT_MASK(4))
#define     AXIMON_AESH_ACCESS_ID						(AXIMON_AESH_ACCESS_ID_MASK << AXIMON_AESH_ACCESS_ID_SHIFT)
#define     AXIMON_DBUF2_ACCESS_ID_SHIFT				(8)
#define     AXIMON_DBUF2_ACCESS_ID_MASK					(BIT_MASK(4))
#define     AXIMON_DBUF2_ACCESS_ID						(AXIMON_DBUF2_ACCESS_ID_MASK << AXIMON_DBUF2_ACCESS_ID_SHIFT)
#define     AXIMON_DBUF3_ACCESS_ID_SHIFT				(12)
#define     AXIMON_DBUF3_ACCESS_ID_MASK					(BIT_MASK(4))
#define     AXIMON_DBUF3_ACCESS_ID						(AXIMON_DBUF3_ACCESS_ID_MASK << AXIMON_DBUF3_ACCESS_ID_SHIFT)
#define 	AXIMON_CPU_ACCESS_EVT_BIT                   (BIT16)
#define 	AXIMON_AESH_ACCESS_EVT_BIT                  (BIT17)
#define 	AXIMON_DBUF2_ACCESS_EVT_BIT                 (BIT18)
#define 	AXIMON_DBUF3_ACCESS_EVT_BIT                 (BIT19)

#define R32_SYS1_AXI_RANGE_CFG1                        	(0x4E0 >> 2)
#define     AXIMON_CPU_START_ADDR_SHIFT					(0)
#define     AXIMON_CPU_START_ADDR_MASK					(BIT_MASK(32))
#define     AXIMON_CPU_START_ADDR						(AXIMON_CPU_START_ADDR_MASK << AXIMON_CPU_START_ADDR_SHIFT)

#define R32_SYS1_AXI_RANGE_CFG2                        	(0x4E4 >> 2)
#define     AXIMON_AESH_START_ADDR_SHIFT				(0)
#define     AXIMON_AESH_START_ADDR_MASK					(BIT_MASK(32))
#define     AXIMON_AESH_START_ADDR						(AXIMON_AESH_START_ADDR_MASK << AXIMON_AESH_START_ADDR_SHIFT)

#define R32_SYS1_AXI_RANGE_CFG3                        	(0x4E8 >> 2)
#define     AXIMON_DBUF2_START_ADDR_SHIFT				(0)
#define     AXIMON_DBUF2_START_ADDR_MASK				(BIT_MASK(32))
#define     AXIMON_DBUF2_START_ADDR						(AXIMON_DBUF2_START_ADDR_MASK << AXIMON_DBUF2_START_ADDR_SHIFT)

#define R32_SYS1_AXI_RANGE_CFG4                        	(0x4EC >> 2)
#define     AXIMON_DBUF3_START_ADDR_SHIFT				(0)
#define     AXIMON_DBUF3_START_ADDR_MASK				(BIT_MASK(32))
#define     AXIMON_DBUF3_START_ADDR						(AXIMON_DBUF3_START_ADDR_MASK << AXIMON_DBUF3_START_ADDR_SHIFT)

#define R32_SYS1_AXI_RANGE_CFG5                        	(0x500 >> 2)
#define     AXIMON_CPU_START_LEN_SHIFT					(0)
#define     AXIMON_CPU_START_LEN_MASK					(BIT_MASK(16))
#define     AXIMON_CPU_START_LEN						(AXIMON_CPU_START_LEN_MASK << AXIMON_CPU_START_LEN_SHIFT)
#define     AXIMON_AESH_START_LEN_SHIFT					(16)
#define     AXIMON_AESH_START_LEN_MASK					(BIT_MASK(16))
#define     AXIMON_AESH_START_LEN						(AXIMON_AESH_START_LEN_MASK << AXIMON_AESH_START_LEN_SHIFT)

#define R32_SYS1_AXI_RANGE_CFG6                        	(0x504 >> 2)
#define     AXIMON_DBUF2_START_LEN_SHIFT				(0)
#define     AXIMON_DBUF2_START_LEN_MASK					(BIT_MASK(16))
#define     AXIMON_DBUF2_START_LEN						(AXIMON_DBUF2_START_LEN_MASK << AXIMON_DBUF2_START_LEN_SHIFT)
#define     AXIMON_DBUF3_START_LEN_SHIFT				(16)
#define     AXIMON_DBUF3_START_LEN_MASK					(BIT_MASK(16))
#define     AXIMON_DBUF3_START_LEN						(AXIMON_DBUF3_START_LEN_MASK << AXIMON_DBUF3_START_LEN_SHIFT)

#define R32_SYS1_AXI_RANGE_ADR1                        	(0x508 >> 2)
#define     AXIMON_CPU_ACCESS_ADDR_SHIFT				(0)
#define     AXIMON_CPU_ACCESS_ADDR_MASK					(BIT_MASK(32))
#define     AXIMON_CPU_ACCESS_ADDR						(AXIMON_CPU_ACCESS_ADDR_MASK << AXIMON_CPU_ACCESS_ADDR_SHIFT)

#define R32_SYS1_AXI_RANGE_ADR2                        	(0x50C >> 2)
#define     AXIMON_AESH_ACCESS_ADDR_SHIFT				(0)
#define     AXIMON_AESH_ACCESS_ADDR_MASK				(BIT_MASK(32))
#define     AXIMON_AESH_ACCESS_ADDR						(AXIMON_AESH_ACCESS_ADDR_MASK << AXIMON_AESH_ACCESS_ADDR_SHIFT)

#define R32_SYS1_AXI_RANGE_ADR3                        	(0x510 >> 2)
#define     AXIMON_DBUF2_ACCESS_ADDR_SHIFT				(0)
#define     AXIMON_DBUF2_ACCESS_ADDR_MASK				(BIT_MASK(32))
#define     AXIMON_DBUF2_ACCESS_ADDR					(AXIMON_DBUF2_ACCESS_ADDR_MASK << AXIMON_DBUF2_ACCESS_ADDR_SHIFT)

#define R32_SYS1_AXI_RANGE_ADR4                        	(0x514 >> 2)
#define     AXIMON_DBUF3_ACCESS_ADDR_SHIFT				(0)
#define     AXIMON_DBUF3_ACCESS_ADDR_MASK				(BIT_MASK(32))
#define     AXIMON_DBUF3_ACCESS_ADDR					(AXIMON_DBUF3_ACCESS_ADDR_MASK << AXIMON_DBUF3_ACCESS_ADDR_SHIFT)

#define R32_SYS1_AXI_OUTOFORDER                        	(0x518 >> 2)
#define     AXI_OUT_OF_ORDER_EN_SHIFT					(0)
#define     AXI_OUT_OF_ORDER_EN_MASK					(BIT_MASK(10))
#define     AXI_OUT_OF_ORDER_EN							(AXI_OUT_OF_ORDER_EN_MASK << AXI_OUT_OF_ORDER_EN_SHIFT)

#define R32_SYS1_AXI_WPRTC_SRC                         	(0x51C >> 2)
#define 	COP0_WRITE_DETECT_BIT                       (BIT0)  // 0x01280000 to 0x012a9000
#define 	COP1_WRITE_DETECT_BIT                       (BIT1)  // 0x00CA0000 to 0x00CC0000
#define 	BMU_WRITE_DETECT_BIT                        (BIT2)  // 0x01000000 to 0x0100BC00

#define R32_SYS1_AXI_WPRTC_CFG                         	(0x520 >> 2)
#define     AXI_W_PROT_SHIFT							(0)
#define     AXI_W_PROT_MASK								(BIT_MASK(4))
#define     AXI_W_PROT									(AXI_W_PROT_MASK << AXI_W_PROT_SHIFT)
#define     AXI_W_PROT_COP0_BIT							(BIT0)
#define     AXI_W_PROT_COP1_BIT							(BIT1)
#define     AXI_W_PROT_BMU_BIT							(BIT2)

#define R32_SYS1_AXI_FAVRIC_MMON1                      	(0x524 >> 2)
#define     RS_AXIM_MONITOR_SHIFT						(0)
#define     RS_AXIM_MONITOR_MASK						(BIT_MASK(4))
#define     RS_AXIM_MONITOR								(RS_AXIM_MONITOR_MASK << RS_AXIM_MONITOR_SHIFT)
#define     FIP_AXIM_MONITOR_SHIFT						(4)
#define     FIP_AXIM_MONITOR_MASK						(BIT_MASK(4))
#define     FIP_AXIM_MONITOR							(FIP_AXIM_MONITOR_MASK << FIP_AXIM_MONITOR_SHIFT)
#define     AESH_AXIM_MONITOR_SHIFT						(8)
#define     AESH_AXIM_MONITOR_MASK						(BIT_MASK(4))
#define     AESH_AXIM_MONITOR							(AESH_AXIM_MONITOR_MASK << AESH_AXIM_MONITOR_SHIFT)
#define     AESB_AXIM_MONITOR_SHIFT						(12)
#define     AESB_AXIM_MONITOR_MASK						(BIT_MASK(4))
#define     AESB_AXIM_MONITOR							(AESB_AXIM_MONITOR_MASK << AESB_AXIM_MONITOR_SHIFT)
#define     DMAC_AXIM2_MONITOR_SHIFT					(16)
#define     DMAC_AXIM2_MONITOR_MASK						(BIT_MASK(4))
#define     DMAC_AXIM2_MONITOR							(DMAC_AXIM2_MONITOR_MASK << DMAC_AXIM2_MONITOR_SHIFT)
#define     DMAC_AXIM_MONITOR_SHIFT						(20)
#define     DMAC_AXIM_MONITOR_MASK						(BIT_MASK(4))
#define     DMAC_AXIM_MONITOR							(DMAC_AXIM_MONITOR_MASK << DMAC_AXIM_MONITOR_SHIFT)
#define     COP1_AXIM_MONITOR_SHIFT						(24)
#define     COP1_AXIM_MONITOR_MASK						(BIT_MASK(4))
#define     COP1_AXIM_MONITOR							(COP1_AXIM_MONITOR_MASK << COP1_AXIM_MONITOR_SHIFT)
#define     HOST_AXIM_MONITOR_SHIFT						(28)
#define     HOST_AXIM_MONITOR_MASK						(BIT_MASK(4))
#define     HOST_AXIM_MONITOR							(HOST_AXIM_MONITOR_MASK << HOST_AXIM_MONITOR_SHIFT)

#define R32_SYS1_AXI_FAVRIC_MMON2                      	(0x528 >> 2)
#define     ZIP_AXIM_MONITOR_SHIFT						(0)
#define     ZIP_AXIM_MONITOR_MASK						(BIT_MASK(4))
#define     ZIP_AXIM_MONITOR							(ZIP_AXIM_MONITOR_MASK << ZIP_AXIM_MONITOR_SHIFT)
#define     CPU_AXIM_MONITOR_SHIFT						(4)
#define     CPU_AXIM_MONITOR_MASK						(BIT_MASK(4))
#define     CPU_AXIM_MONITOR							(CPU_AXIM_MONITOR_MASK << CPU_AXIM_MONITOR_SHIFT)
#define     COP0_AXIM_MONITOR_SHIFT						(8)
#define     COP0_AXIM_MONITOR_MASK						(BIT_MASK(4))
#define     COP0_AXIM_MONITOR							(COP0_AXIM_MONITOR_MASK << COP0_AXIM_MONITOR_SHIFT)
#define     HLLR_AXIM_MONITOR_SHIFT						(12)
#define     HLLR_AXIM_MONITOR_MASK						(BIT_MASK(4))
#define     HLLR_AXIM_MONITOR							(HLLR_AXIM_MONITOR_MASK << HLLR_AXIM_MONITOR_SHIFT)
#define     MR_AXIM2_MONITOR_SHIFT						(16)
#define     MR_AXIM2_MONITOR_MASK						(BIT_MASK(4))
#define     MR_AXIM2_MONITOR							(MR_AXIM2_MONITOR_MASK << MR_AXIM2_MONITOR_SHIFT)

#define R32_SYS1_AXI_FAVRIC_SMON1                      	(0x52C >> 2)
#define     AXI_DBUF1_AXIS_MONITOR_SHIFT				(0)
#define     AXI_DBUF1_AXIS_MONITOR_MASK					(BIT_MASK(5))
#define     AXI_DBUF1_AXIS_MONITOR						(AXI_DBUF1_AXIS_MONITOR_MASK << AXI_DBUF1_AXIS_MONITOR_SHIFT)
#define     AXI_DBUF2_AXIS_MONITOR_SHIFT				(8)
#define     AXI_DBUF2_AXIS_MONITOR_MASK					(BIT_MASK(5))
#define     AXI_DBUF2_AXIS_MONITOR						(AXI_DBUF2_AXIS_MONITOR_MASK << AXI_DBUF2_AXIS_MONITOR_SHIFT)
#define     AXI_DBUF3_AXIS_MONITOR_SHIFT				(16)
#define     AXI_DBUF3_AXIS_MONITOR_MASK					(BIT_MASK(5))
#define     AXI_DBUF3_AXIS_MONITOR						(AXI_DBUF3_AXIS_MONITOR_MASK << AXI_DBUF3_AXIS_MONITOR_SHIFT)
#define     D2H_AXIS_MONITOR_SHIFT						(24)
#define     D2H_AXIS_MONITOR_MASK						(BIT_MASK(5))
#define     D2H_AXIS_MONITOR							(D2H_AXIS_MONITOR_MASK << D2H_AXIS_MONITOR_SHIFT)

#define R32_SYS1_AXI_FAVRIC_SMON2                      	(0x530 >> 2)
#define     CPU_AXIS_MONITOR_SHIFT						(0)
#define     CPU_AXIS_MONITOR_MASK						(BIT_MASK(5))
#define     CPU_AXIS_MONITOR							(CPU_AXIS_MONITOR_MASK << CPU_AXIS_MONITOR_SHIFT)
#define     FIP_AXIS_MONITOR_SHIFT						(8)
#define     FIP_AXIS_MONITOR_MASK						(BIT_MASK(5))
#define     FIP_AXIS_MONITOR							(FIP_AXIS_MONITOR_MASK << FIP_AXIS_MONITOR_SHIFT)
#define     COP1_AXIS_MONITOR_SHIFT						(16)
#define     COP1_AXIS_MONITOR_MASK						(BIT_MASK(5))
#define     COP1_AXIS_MONITOR							(COP1_AXIS_MONITOR_MASK << COP1_AXIS_MONITOR_SHIFT)
#define     ZIP_AXIS_MONITOR_SHIFT						(24)
#define     ZIP_AXIS_MONITOR_MASK						(BIT_MASK(5))
#define     ZIP_AXIS_MONITOR							(ZIP_AXIS_MONITOR_MASK << ZIP_AXIS_MONITOR_SHIFT)

#define R32_SYS1_AXI_FAVRIC_SMON3                      	(0x534 >> 2)
#define     AESH_AXIS_MONITOR_SHIFT						(0)
#define     AESH_AXIS_MONITOR_MASK						(BIT_MASK(5))
#define     AESH_AXIS_MONITOR							(AESH_AXIS_MONITOR_MASK << AESH_AXIS_MONITOR_SHIFT)
#define     HOST_AXIS_MONITOR_SHIFT						(8)
#define     HOST_AXIS_MONITOR_MASK						(BIT_MASK(5))
#define     HOST_AXIS_MONITOR							(HOST_AXIS_MONITOR_MASK << HOST_AXIS_MONITOR_SHIFT)
#define     SPI_AXIS_MONITOR_SHIFT						(16)
#define     SPI_AXIS_MONITOR_MASK						(BIT_MASK(5))
#define     SPI_AXIS_MONITOR							(SPI_AXIS_MONITOR_MASK << SPI_AXIS_MONITOR_SHIFT)
#define     BMU_AXIS_MONITOR_SHIFT						(24)
#define     BMU_AXIS_MONITOR_MASK						(BIT_MASK(5))
#define     BMU_AXIS_MONITOR							(BMU_AXIS_MONITOR_MASK << BMU_AXIS_MONITOR_SHIFT)

#define R32_SYS1_AXI_MR_BLOCK_CTRL                     	(0x53C >> 2)
#define 	CPU_SYS_BLKN_BIT                            (BIT0)
#define 	BMU_SYS_BLKN_BIT                            (BIT1)
#define 	SEC_SYS_BLKN_BIT                            (BIT2)
#define 	ZIP_SYS_BLKN_BIT                            (BIT3)
#define 	DMAC_SYS_BLKN_BIT                           (BIT4)
#define 	COP1_SYS_BLKN_BIT                           (BIT5)
#define 	FLH_SYS_BLKN_BIT                            (BIT6)
#define 	HLLR_SYS_BLKN_BIT                           (BIT7)

#define R64_SYS1_FLH_SYS_CFG                          	(0x540 >> 3)
#define     SYS_ICCM_DSA_SHIFT							(0)
#define     SYS_ICCM_DSA_MASK							(BIT_MASK64(34))
#define     SYS_ICCM_DSA								(SYS_ICCM_DSA_MASK << SYS_ICCM_DSA_SHIFT)

#define R32_SYS1_FLH_SYS_CFG3                          	(0x548 >> 2)
#define     SYS_POOL0_SA_SHIFT							(0)
#define     SYS_POOL0_SA_MASK							(BIT_MASK(20))
#define     SYS_POOL0_SA								(SYS_POOL0_SA_MASK << SYS_POOL0_SA_SHIFT)

#define R32_SYS1_FLH_MRT_TID1                         	(0x54C >> 2)
#define     TARGET_ID31_SHIFT							(0)
#define     TARGET_ID31_MASK							(BIT_MASK(8))
#define     TARGET_ID31									(TARGET_ID31_MASK << TARGET_ID31_SHIFT)
#define     TARGET_ID70_SHIFT							(8)
#define     TARGET_ID70_MASK							(BIT_MASK(8))
#define     TARGET_ID70									(TARGET_ID70_MASK << TARGET_ID70_SHIFT)
#define     TARGET_ID84_SHIFT							(16)
#define     TARGET_ID84_MASK							(BIT_MASK(8))
#define     TARGET_ID84									(TARGET_ID84_MASK << TARGET_ID84_SHIFT)
#define     TARGET_ID85_SHIFT							(24)
#define     TARGET_ID85_MASK							(BIT_MASK(8))
#define     TARGET_ID85									(TARGET_ID85_MASK << TARGET_ID85_SHIFT)

#define R32_SYS1_FLH_MRT_TID2                          	(0x550 >> 2)
#define     TARGET_ID86_SHIFT							(0)
#define     TARGET_ID86_MASK							(BIT_MASK(8))
#define     TARGET_ID86									(TARGET_ID86_MASK << TARGET_ID86_SHIFT)
#define     TARGET_ID87_SHIFT							(8)
#define     TARGET_ID87_MASK							(BIT_MASK(8))
#define     TARGET_ID87									(TARGET_ID87_MASK << TARGET_ID87_SHIFT)

#define R32_SYS1_FLH_AXIS_CFG                          	(0x554 >> 2)
#define 	CR_FIP_AXIS_ACT_EN_BIT                   	(BIT0)

#define R32_SYS1_TESSENT_FLH_CTRL1                      (0x560 >> 2)
#define		SR_TESSENT_FLH_TEST_START_BIT				(BIT0)
#define		SR_TESSENT_FLH_TEST_INIT_BIT				(BIT8)
#define		SR_TESSENT_FLH_TMEM_EN_BIT					(BIT16)

#define R32_SYS1_TESSENT_FLH_CTRL2                      (0x564 >> 2)
#define     SR_TESSENT_FLH_CTRL_SELECT_SHIFT			(0)
#define     SR_TESSENT_FLH_CTRL_SELECT_MASK				(BIT_MASK(8))
#define     SR_TESSENT_FLH_CTRL_SELECT					(SR_TESSENT_FLH_CTRL_SELECT_MASK << SR_TESSENT_FLH_CTRL_SELECT_SHIFT)

#define R32_SYS1_TESSENT_FLH_CTRL3                      (0x568 >> 2)
#define     SR_TESSENT_FLH_TMEM_SELECT_SHIFT			(0)
#define     SR_TESSENT_FLH_TMEM_SELECT_MASK				(BIT_MASK(32))
#define     SR_TESSENT_FLH_TMEM_SELECT					(SR_TESSENT_FLH_TMEM_SELECT_MASK << SR_TESSENT_FLH_TMEM_SELECT_SHIFT)

#define R32_SYS1_TESSENT_FLH_CTRL4                      (0x56c >> 2)
#define     SR_TESSENT_FLH_ALGORITHM_SELECT_SHIFT		(0)
#define     SR_TESSENT_FLH_ALGORITHM_SELECT_MASK		(BIT_MASK(3))
#define     SR_TESSENT_FLH_ALGORITHM_SELECT				(SR_TESSENT_FLH_ALGORITHM_SELECT_MASK << SR_TESSENT_FLH_ALGORITHM_SELECT_SHIFT)
#define     SR_TESSENT_FLH_ALGORITHM_SELECT_EN_BIT		(BIT8)
#define     SR_TESSENT_FLH_ALGORITHM_OP_SET_SELECT_BIT	(BIT16)
#define     SR_TESSENT_FLH_ALGORITHM_OP_SET_SELECT_EN_BIT	(BIT24)

#define R32_SYS1_TESSENT_FLH_STS1                       (0x570 >> 2)
#define 	SR_TESSENT_FLH_TEST_PASS_SYNC_BIT			(BIT0)
#define 	SR_TESSENT_FLH_TEST_DONE_SYNC_BIT			(BIT8)

#define R32_SYS1_TESSENT_FLH_STS2                       (0x574 >> 2)
#define     SR_TESSENT_FLH_CTRL_DONE_SYNC_SHIFT			(0)
#define     SR_TESSENT_FLH_CTRL_DONE_SYNC_MASK			(BIT_MASK(8))
#define     SR_TESSENT_FLH_CTRL_DONE_SYNC				(SR_TESSENT_FLH_CTRL_DONE_SYNC_MASK << SR_TESSENT_FLH_CTRL_DONE_SYNC_SHIFT)

#define R32_SYS1_TESSENT_FLH_STS3                       (0x578 >> 2)
#define     SR_TESSENT_FLH_CTRL_PASS_SYNC_SHIFT			(0)
#define     SR_TESSENT_FLH_CTRL_PASS_SYNC_MASK			(BIT_MASK(8))
#define     SR_TESSENT_FLH_CTRL_PASS_SYNC				(SR_TESSENT_FLH_CTRL_PASS_SYNC_MASK << SR_TESSENT_FLH_CTRL_PASS_SYNC_SHIFT)

#define R32_SYS1_FLH_IO_OPTION                         	(0x580 >> 2)
#define 	SR_FIO_COSO_BY_SYS_BIT                      (BIT0)
#define 	SR_FIO_AUTO_TG2_OPT_BIT                     (BIT8)
#define     SR_FIO_WPT_OPT_CTRL_BY_FIP                  (BIT16)
#define     SR_FIO_WPT_OPT_CTRL_BY_SYS                  (~BIT16)

#define R32_SYS1_DBUF1_ECC_INT							(0x590 >> 2)
#define		DBUF0_ECC_ERR_STS_BIT						(BIT0)
#define		DBUF1_ECC_ERR_STS_BIT						(BIT1)
#define		DBUF2_ECC_ERR_STS_BIT						(BIT2)
#define		DBUF3_ECC_ERR_STS_BIT						(BIT3)
#define		DBUF0_ECC_DET_STS_BIT						(BIT4)
#define		DBUF1_ECC_DET_STS_BIT						(BIT5)
#define		DBUF2_ECC_DET_STS_BIT						(BIT6)
#define		DBUF3_ECC_DET_STS_BIT						(BIT7)
#define		DBUF0_ECC_ERR_MASK_BIT						(BIT8)
#define		DBUF1_ECC_ERR_MASK_BIT						(BIT9)
#define		DBUF2_ECC_ERR_MASK_BIT						(BIT10)
#define		DBUF3_ECC_ERR_MASK_BIT						(BIT11)
#define		DBUF0_ECC_DET_MASK_BIT						(BIT12)
#define		DBUF1_ECC_DET_MASK_BIT						(BIT13)
#define		DBUF2_ECC_DET_MASK_BIT						(BIT14)
#define		DBUF3_ECC_DET_MASK_BIT						(BIT15)
#define     DBUF0_ECC_INFO_SHIFT						(16)
#define     DBUF0_ECC_INFO_MASK							(BIT_MASK(2))
#define     DBUF0_ECC_INFO								(DBUF0_ECC_INFO_MASK << DBUF0_ECC_INFO_SHIFT)
#define     DUBF1_ECC_INFO_SHIFT						(18)
#define     DUBF1_ECC_INFO_MASK							(BIT_MASK(2))
#define     DUBF1_ECC_INFO								(DUBF1_ECC_INFO_MASK << DUBF1_ECC_INFO_SHIFT)
#define     DBUF2_ECC_INFO_SHIFT						(20)
#define     DBUF2_ECC_INFO_MASK							(BIT_MASK(2))
#define     DBUF2_ECC_INFO								(DBUF2_ECC_INFO_MASK << DBUF2_ECC_INFO_SHIFT)
#define     DBUF3_ECC_INFO_SHIFT						(22)
#define     DBUF3_ECC_INFO_MASK							(BIT_MASK(2))
#define     DBUF3_ECC_INFO								(DBUF3_ECC_INFO_MASK << DBUF3_ECC_INFO_SHIFT)

#define R32_SYS1_DBUF1_ECC_ADDR1						(0x594 >> 2)
#define     DBUF0_ECC_ERR_ADDR_SHIFT					(0)
#define     DBUF0_ECC_ERR_ADDR_MASK						(BIT_MASK(15))
#define     DBUF0_ECC_ERR_ADDR							(DBUF0_ECC_ERR_ADDR_MASK << DBUF0_ECC_ERR_ADDR_SHIFT)
#define     DBUF1_ECC_ERR_ADDR_SHIFT					(16)
#define     DBUF1_ECC_ERR_ADDR_MASK						(BIT_MASK(15))
#define     DBUF1_ECC_ERR_ADDR							(DBUF1_ECC_ERR_ADDR_MASK << DBUF1_ECC_ERR_ADDR_SHIFT)

#define R32_SYS1_DBUF1_ECC_ADDR2						(0x598 >> 2)
#define     DBUF2_ECC_ERR_ADDR_SHIFT					(0)
#define     DBUF2_ECC_ERR_ADDR_MASK						(BIT_MASK(15))
#define     DBUF2_ECC_ERR_ADDR							(DBUF2_ECC_ERR_ADDR_MASK << DBUF2_ECC_ERR_ADDR_SHIFT)
#define     DBUF3_ECC_ERR_ADDR_SHIFT					(16)
#define     DBUF3_ECC_ERR_ADDR_MASK						(BIT_MASK(15))
#define     DBUF3_ECC_ERR_ADDR							(DBUF3_ECC_ERR_ADDR_MASK << DBUF3_ECC_ERR_ADDR_SHIFT)

#define R32_SYS1_DBUF1_ECC_CNT1							(0x59C >> 2)
#define     DBUF0_ECC_DET_CNT_SHIFT						(0)
#define     DBUF0_ECC_DET_CNT_MASK						(BIT_MASK(16))
#define     DBUF0_ECC_DET_CNT							(DBUF0_ECC_DET_CNT_MASK << DBUF0_ECC_DET_CNT_SHIFT)
#define     DBUF1_ECC_DET_CNT_SHIFT						(16)
#define     DBUF1_ECC_DET_CNT_MASK						(BIT_MASK(16))
#define     DBUF1_ECC_DET_CNT							(DBUF1_ECC_DET_CNT_MASK << DBUF1_ECC_DET_CNT_SHIFT)

#define R32_SYS1_DBUF1_ECC_CNT2							(0x5A0 >> 2)
#define     DBUF2_ECC_DET_CNT_SHIFT						(0)
#define     DBUF2_ECC_DET_CNT_MASK						(BIT_MASK(16))
#define     DBUF2_ECC_DET_CNT							(DBUF2_ECC_DET_CNT_MASK << DBUF2_ECC_DET_CNT_SHIFT)
#define     DBUF3_ECC_DET_CNT_SHIFT						(16)
#define     DBUF3_ECC_DET_CNT_MASK						(BIT_MASK(16))
#define     DBUF3_ECC_DET_CNT							(DBUF3_ECC_DET_CNT_MASK << DBUF3_ECC_DET_CNT_SHIFT)

#define R32_SYS1_SEC_SLFCHK_STATUS						(0x5B0 >> 2)
#define 	AES_PO_SLFCHK_OVER_BIT						(BIT0)
#define 	SM4_PO_SLFCHK_OVER_BIT						(BIT1)
#define 	SM3_PO_SLFCHK_OVER_BIT						(BIT2)
#define 	SHA1_PO_SLFCHK_OVER_BIT						(BIT3)
#define 	SHA256_PO_SLFCHK_OVER_BIT					(BIT4)
#define 	SHA512_PO_SLFCHK_OVER_BIT					(BIT5)
#define 	SHA_512_256_PO_SLFCHK_OVER_BIT				(BIT6)
#define 	PKE_PO_SLFCHK_OVER_BIT						(BIT7)
#define 	AES_PO_SLFCHK_PASS_BIT						(BIT16)
#define 	SM4_PO_SLFCHK_PASS_BIT						(BIT17)
#define 	SM3_PO_SLFCHK_PASS_BIT						(BIT18)
#define 	SHA1_PO_SLFCHK_PASS_BIT						(BIT19)
#define 	SHA256_PO_SLFCHK_PASS_BIT					(BIT20)
#define 	SHA512_PO_SLFCHK_PASS_BIT					(BIT21)
#define 	SHA512_256_PO_SLFCHK_PASS_BIT				(BIT22)
#define 	PKE_PO_SLFCHK_PASS_BIT						(BIT23)

#define R32_SYS1_TESSENT_TOP_CTRL1						(0x5C0 >> 2)
#define 	SR_TESSENT_TOP_TEST_START_BIT				(BIT0)
#define 	SR_TESSENT_TOP_TEST_INIT_BIT				(BIT8)
#define 	SR_TESSENT_TOP_TMEM_EN_BIT					(BIT16)

#define R32_SYS1_TESSENT_TOP_CTRL2						(0x5C4 >> 2)
#define     SR_TESSENT_TOP_CTRL_SELECT_SHIFT			(0)
#define     SR_TESSENT_TOP_CTRL_SELECT_MASK				(BIT_MASK(24))
#define     SR_TESSENT_TOP_CTRL_SELECT					(SR_TESSENT_TOP_CTRL_SELECT_MASK << SR_TESSENT_TOP_CTRL_SELECT_SHIFT)

#define R32_SYS1_TESSENT_TOP_CTRL3						(0x5C8 >> 2)
#define     SR_TESSENT_TOP_TMEM_SELECT_SHIFT			(0)
#define     SR_TESSENT_TOP_TMEM_SELECT_MASK				(BIT_MASK(32))
#define     SR_TESSENT_TOP_TMEM_SELECT					(SR_TESSENT_TOP_TMEM_SELECT_MASK << SR_TESSENT_TOP_TMEM_SELECT_SHIFT)
//VIC99 SPEC BIT0~7 OR 0~31?

#define R32_SYS1_TESSENT_TOP_CTRL4						(0x5CC >> 2)
#define     SR_TESSENT_TOP_ALGORITHM_SELECT_SHIFT		(0)
#define     SR_TESSENT_TOP_ALGORITHM_SELECT_MASK		(BIT_MASK(3))
#define     SR_TESSENT_TOP_ALGORITHM_SELECT				(SR_TESSENT_TOP_ALGORITHM_SELECT_MASK << SR_TESSENT_TOP_ALGORITHM_SELECT_SHIFT)
#define     SR_TESSENT_TOP_ALGORITHM_SELECT_EN_BIT		(BIT8)
#define     SR_TESSENT_TOP_ALGORITHM_OP_SET_SELECT_BIT	(BIT16)
#define     SR_TESSENT_TOP_ALGORITHM_OP_SET_SELECT_EN_BIT	(BIT24)

#define R32_SYS1_TESSENT_TOP_STS1						(0x5D0 >> 2)
#define 	SR_TESSENT_TOPTEST_PASS_SYNC_BIT			(BIT0)
#define 	SR_TESSENT_TOPTEST_DONE_SYNC_BIT			(BIT8)

#define R32_SYS1_TESSENT_TOP_STS2						(0x5D4 >> 2)
#define     SR_TESSENT_TOP_CTRL_DONE_SYNC_SHIFT			(0)
#define     SR_TESSENT_TOP_CTRL_DONE_SYNC_MASK			(BIT_MASK(24))
#define     SR_TESSENT_TOP_CTRL_DONE_SYNC				(SR_TESSENT_TOP_CTRL_DONE_SYNC_MASK << SR_TESSENT_TOP_CTRL_DONE_SYNC_SHIFT)

#define R32_SYS1_TESSENT_TOP_STS3						(0x5D8 >> 2)
#define     SR_TESSENT_TOP_CTRL_PASS_SYNC_SHIFT			(0)
#define     SR_TESSENT_TOP_CTRL_PASS_SYNC_MASK			(BIT_MASK(24))
#define     SR_TESSENT_TOP_CTRL_PASS_SYNC				(SR_TESSENT_TOP_CTRL_PASS_SYNC_MASK << SR_TESSENT_TOP_CTRL_PASS_SYNC_SHIFT)

#define R32_SYS1_TESSENT_MEM_REPAIR						(0x5E0 >> 2)
#define 	SR_TESSENT_BISR_RSTN_BIT					(BIT0)
#define 	SR_TESSENT_BISR_EFUC_EN_BIT					(BIT8)
#define 	SR_TESSENT_BISR_GO_SYNC_BIT					(BIT16)
#define 	SR_TESSENT_BISR_DONE_SYNC_BIT				(BIT24)

/*
 *  +-----------------------------------------------------------------------+
 *  |					PD1 MISC Control      								|
 *  +-----------------------------------------------------------------------+
 */
#define SYS1_MISC_CTRL_BASE								(SYSTEM_PD1_REG_ADDRESS)

#define R8_SYS1_MISC_CTRL								((REG8  *) SYS1_MISC_CTRL_BASE)
#define R16_SYS1_MISC_CTRL								((REG16 *) SYS1_MISC_CTRL_BASE)
#define R32_SYS1_MISC_CTRL								((REG32 *) SYS1_MISC_CTRL_BASE)
#define R64_SYS1_MISC_CTRL								((REG64 *) SYS1_MISC_CTRL_BASE)

#define R32_SYS1_DISPLAY_MSG							(0x600 >> 2)
#define     DISPLAY_MSG_REG_SHIFT						(0)
#define     DISPLAY_MSG_REG_MASK						(BIT_MASK(8))
#define     DISPLAY_MSG_REG								(DISPLAY_MSG_REG_MASK << DISPLAY_MSG_REG_SHIFT)

#define R32_SYS1_STATE_MSG								(0x604 >> 2)
#define     STATE_MSG_REG_SHIFT							(0)
#define     STATE_MSG_REG_MASK							(BIT_MASK(8))
#define     STATE_MSG_REG								(STATE_MSG_REG_MASK << STATE_MSG_REG_SHIFT)

#define R32_SYS1_DATA_STUS_REG							(0x608 >> 2)
#define     DATA_STUSU_REG_SHIFT						(0)
#define     DATA_STUSU_REG_MASK							(BIT_MASK(16))
#define     DATA_STUSU_REG								(DATA_STUSU_REG_MASK << DATA_STUSU_REG_SHIFT)

#define R32_SYS1_DATA_CREG_DLY_CTRL						(0x60C >> 2)
#define 	CREG_BUS_WIDTH_BIT                          (BIT16)
#define 	CREG_CPHY_TYPE_BIT                          (BIT17)
#define 	CREG_FLUSH_BIT								(BIT24)
#define 	CREG_SM_RST_BIT								(BIT25)

#define R32_SYS1_HW_DBG_CTRL_PD1                    	(0x610 >> 2)
#define 	SR_DBG_OUT_PD1_MSB_EN_BIT                   (BIT0)

#define R32_SYS1_HW_DBG_SYS1_PD1                    	(0x614 >> 2)
#define     DBG_IP_OUT_SYNC_SHIFT						(0)
#define     DBG_IP_OUT_SYNC_MASK						(BIT_MASK(32))
#define     DBG_IP_OUT_SYNC								(DBG_IP_OUT_SYNC_MASK << DBG_IP_OUT_SYNC_SHIFT)

#define R32_SYS1_HW_DBG_SYS2_PD1                    	(0x618 >> 2)
#define     DBG_IP_OUT_SYNC2_SHIFT						(0)
#define     DBG_IP_OUT_SYNC2_MASK						(BIT_MASK(16))
#define     DBG_IP_OUT_SYNC2							(DBG_IP_OUT_SYNC2_MASK << DBG_IP_OUT_SYNC2_SHIFT)

#define R32_SYS1_MISC_PERR_INJ                         	(0x61C >> 2)  //ps5013: 0080_3180 PD1/ITC
#define     CR_DMAC_PERR_INJ_SHIFT						(8)
#define     CR_DMAC_PERR_INJ_MASK						(BIT_MASK(2))
#define     CR_DMAC_PERR_INJ							(CR_DMAC_PERR_INJ_MASK << CR_DMAC_PERR_INJ_SHIFT)
#define 	CR_COP0_PERR_INJ_BIT                        (BIT16)

#define R32_SYS1_MISC_SYS_PD1_MISC                     	(0x620 >> 2) //ps5013: 0080_3080 PD1/MISCH
#define 	CR_SYS_XZIP_EN_BIT                          (BIT0)
#define 	CR_SYS_CHK_RANGE_EN_BIT                     (BIT1)
#define 	CR_SYS_CHK_LBNA_EN_BIT                      (BIT2)
#define     CR_TCM_ECC_EN_SHIFT							(3)
#define     CR_TCM_ECC_EN_MASK							(BIT_MASK(3))
#define     CR_TCM_ECC_EN								(CR_TCM_ECC_EN_MASK << CR_TCM_ECC_EN_SHIFT)
#define     CR_DBUF_RRC_SET_SHIFT						(6)
#define     CR_DBUF_RRC_SET_MASK						(BIT_MASK(2))
#define     CR_DBUF_RRC_SET								(CR_DBUF_RRC_SET_MASK << CR_DBUF_RRC_SET_SHIFT)

#define R32_SYS1_MISC_PCAE2E_CTRL                      	(0x624 >> 2)  // ps5013: 0080_1004 PD0/MISCH
#define 	CR_SYS_PCAE2E_EN_BIT                        (BIT0)
#define     CR_SYS_PCAE2E_CFG_SHIFT						(1)
#define     CR_SYS_PCAE2E_CFG_MASK						(BIT_MASK(2))
#define     CR_SYS_PCAE2E_CFG							(CR_SYS_PCAE2E_CFG_MASK << CR_SYS_PCAE2E_CFG_SHIFT)

#define R32_SYS1_MISC_EFUSE_DLY_CTRL                   	(0x628 >> 2)
#define     EFUSE_WEXT_CNT_SHIFT						(16)
#define     EFUSE_WEXT_CNT_MASK							(BIT_MASK(2))
#define     EFUSE_WEXT_CNT								(EFUSE_WEXT_CNT_MASK << EFUSE_WEXT_CNT_SHIFT)
#define     EFUSE_REXT_CNT_SHIFT						(18)
#define     EFUSE_REXT_CNT_MASK							(BIT_MASK(2))
#define     EFUSE_REXT_CNT								(EFUSE_REXT_CNT_MASK << EFUSE_REXT_CNT_SHIFT)
#define 	EFUSE_FLUSH_BIT                             (BIT24)
#define 	EFUSE_SM_RST_BIT                            (BIT25)

#define R32_SYS1_MISC_FPGA_7_SEG_DISPLAY                (0x62C >> 2)
#define     GPIO_OUT2_SHIFT								(0)
#define     GPIO_OUT2_MASK								(BIT_MASK(16))
#define     GPIO_OUT2									(GPIO_OUT2_MASK << GPIO_OUT2_SHIFT)

#define R32_SYS1_MISC_HW_SIM_INFO1_PD1                  (0x630 >> 2)
#define     HW_SIM_INFO1_SHIFT							(0)
#define     HW_SIM_INFO1_MASK							(BIT_MASK(32))
#define     HW_SIM_INFO1								(HW_SIM_INFO1_MASK << HW_SIM_INFO1_SHIFT)

#define R32_SYS1_MISC_HW_SIM_INFO2_PD1                  (0x634 >> 2)
#define     HW_SIM_INFO2_SHIFT			    		   	(0)
#define     HW_SIM_INFO2_MASK						   	(BIT_MASK(32))
#define     HW_SIM_INFO2							   	(HW_SIM_INFO2_MASK << HW_SIM_INFO2_SHIFT)

#define R32_SYS1_MISC_HW_SIM_INFO3_PD1                  (0x638 >> 2)
#define     HW_SIM_INFO3_SHIFT						   	(0)
#define     HW_SIM_INFO3_MASK						   	(BIT_MASK(32))
#define     HW_SIM_INFO3							   	(HW_SIM_INFO3_MASK << HW_SIM_INFO3_SHIFT)

#define R32_SYS1_MISC_HW_SIM_INFO4_PD1                  (0x63C >> 2)
#define     HW_SIM_INFO4_SHIFT						   	(0)
#define     HW_SIM_INFO4_MASK						   	(BIT_MASK(32))
#define     HW_SIM_INFO4							   	(HW_SIM_INFO4_MASK << HW_SIM_INFO4_SHIFT)

#endif  /* _SYS_PD1_REG_5021_H_ */
