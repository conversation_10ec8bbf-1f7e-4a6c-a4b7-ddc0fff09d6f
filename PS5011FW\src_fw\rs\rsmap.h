#ifndef _RS_H_
#define _RS_H_

#include "typedef.h"
#include "common/fw_common.h"
#include "common/math_op.h"
#include "fw_vardef.h"
#include "aom/aom_api.h"
#include "hal/dmac/dmac_api.h"
#include "hal/cop1/cop1_api.h"
#include "math_op.h"
#include "hal/rs/rs_api.h"
#include "raideccmap/raideccmap_api.h"

extern U32 gulFirstHitTableBasePlane;
extern Unit_t guwPORNewGR;

#if (MICRON_FSP_EN)
extern U32 FTLGetFirstPlaneIdx(U32 ulPlaneInx);
extern U8 FTLCheckPageType(U32 ulPlaneInx);
extern void FTLPlaneInx2Physical(U32 ulPlaneInx, U16 *uwPage, U8 *ubPlane);
extern U8 FTLGetPlaneFlushNum(U8 ubIsSLCMode, U32 ulPlaneIndex);
#endif

AOM_INIT_2 void FTLCalculateFirstHitTableBasePlane(void);
extern void FTLPlaneInx2Physical(U32 ulPlaneInx, U16 *uwPage, U8 *ubPlane);

#endif /* _RSMAP_H_ */
