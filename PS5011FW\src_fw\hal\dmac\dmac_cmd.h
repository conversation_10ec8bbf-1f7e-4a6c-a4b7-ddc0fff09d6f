#ifndef _DMAC_CMD_H_
#define _DMAC_CMD_H_

#include "typedef.h"
/**********************/
//	for each command
/**********************/
#define NEED_MSK			(1)
#define NOT_NEED_MSK		(0)

#define NEED_CQ				(1)
#define NOT_NEED_CQ			(0)
/**********************/
//	for CPY
/**********************/
#define NEED_UN_ZIP			(1)
#define NOT_NEED_UN_ZIP		(0)

/**********************/
//	for SV	(1, 2, 4, 8, random)
//	for SMAX(1, 2, 4)
//	for SRH	(1, 2, 4, 8)
//	for SVM	(1, 2, 4, 8)
/**********************/
#define ENTRY_1B			(0x00)
#define ENTRY_2B			(0x01)
#define ENTRY_4B			(0x02)
#define ENTRY_8B			(0x03)
#define ENTRY_RND			(0x01)

#define ZIPMODE_NORMAL		(0x00)
#define ZIPMODE_ZIP			(0x01)
#define ZIPMODE_UNZIP		(0x02)

/**********************/
//	for XOR
/**********************/
#define XOR_MODE_XOR		(0x00)
#define XOR_MODE_XNOR		(0x01)
#define XOR_MODE_OR			(0x02)

/**********************/
//	for SORT
/**********************/
#define SORT_ENTRY_4B		(0x00)
#define SORT_ENTRY_8B		(0x01)
#define SORT_ENTRY_16B		(0x02)

#define ASCEND				(0)
#define DESCEND				(1)

#define IS_SORT_LAST_CMD	(1)
#define NOT_SORT_LAST_CMD	(0)

/**********************/
//	for SMAX
/**********************/
#define SMAX_MODE_MAX	(0)
#define SMAX_MODE_MIN	(1)

/**********************/
//	for SBIT
/**********************/
#define SBIT_MODE_FIND_ZERO	(0)
#define SBIT_MODE_FIND_ONE	(1)

/**********************/
//	for TRIM_MERGE
/**********************/
#define TRIM_MERGE_FORMAT_SATA		(0)
#define TRIM_MERGE_FORMAT_NVME		(1)

#define TRIM_MERGE_MODE_512B		(0)
#define TRIM_MERGE_MODE_4KB			(1)

#define NEED_WL_ERR_CHK				(1)
#define NOT_NEED_WL_ERR_CHK			(0)

#define NEED_LBA2LCA				(1)
#define NOT_NEED_LBA2LCA			(0)

/**********************/
//	for SRH
/**********************/
#define SHEARCH_MODE_EQ		(0x00)	// value == source
#define SHEARCH_MODE_GEQ	(0x01)	// value >= source
#define SHEARCH_MODE_LEQ	(0x02)	// value <= source
#define SHEARCH_MODE_NEQ	(0x03)	// value != source

#define IS_FIND_ONE_ONLY	(1)
#define NOT_FIND_ONE_ONLY	(0)

#define IS_FIND_LAST_ONE	(1)
#define NOT_FIND_LAST_ONE	(0)

/**********************/
//	for SCAN_ZCODE
/**********************/
#define SCAN_ZINFO			(0)
#define SCAN_VB				(1)

#define EACH_CASE			(1)
#define SINGLE_CASE			(0)

/**********************/
//	for SCAN_VALID0
/**********************/
#define PTE_SIZE_1K		(0x00)
#define PTE_SIZE_2K		(0x01)
#define PTE_SIZE_4K		(0x02)

/**********************/
//	for dump error
/**********************/
#define	DMAC_GET_ERROR_BIT				(0x00)
#define DMAC_GET_ERROR_INFO				(0x10)
#define DMAC_GET_HIGH_ERROR_SQ0_BASE	(0x20)
#define DMAC_GET_HIGH_ERROR_SQ1_BASE	(0x28)
#define DMAC_GET_NORMAL_ERROR_SQ0_BASE	(0x30)
#define DMAC_GET_NORMAL_ERROR_SQ1_BASE	(0x38)
#define DMAC_GET_HICH_ERROR_CQ0_BASE	(0x40)
#define DMAC_GET_HICH_ERROR_CQ1_BASE	(0x42)
#define DMAC_GET_NORMAL_ERROR_CQ0_BASE	(0x50)
#define DMAC_GET_NORMAL_ERROR_CQ1_BASE	(0x52)
#define DMAC_GET_BUF_PERR_CNT				(0xFF)

enum SortOP {
	SORT_SCAN,
	SORT_OFS_CPY_SCAN,
	SORT_OFS_CPY,
	SORT_OFS,
	SORT_CPY
};

enum Sort_states {
	WAIT_SCAN,
	WAIT_OFS,
	WAIT_CPY
};

typedef struct {
	U8 ubHead;
	U8 ubTail;
	U8 ubLen;
} DMACQueueMgr_t;

/*
typedef union {
	U32 uladdr;
	U16 uwPhysicalBuffer;
	struct {
		U32	ubID		: 4;
		U32	uwOffset	: 16;
		U32	reserved	: 12;
	} LogicalBuffer;
} DMAC_ADDR_t;
*/

typedef union {
	U32 uladdr;
	struct {
		U32	RSV0		: 12;
		U32	uwOffset	: 9;
		U32 RSV1		: 4;
		U32	BaseAddr	: 7;
	} PhysicalBuffer;
	struct {
		U32	RSV0		: 12;
		U32	uwOffset	: 10;
		U32 ubID		: 3;
		U32	BaseAddr	: 7;
	} LogicalBuffer;
} DMAC_ADDR_t;

typedef struct {
	U64	uoLBA			: 48;
	U64	uwSectorCount	: 16;
} ATATrimRangeIn_t;

typedef struct {
	U32	ulContextAttributes;
	U32	ulSectorCount;
	U64	uoLBA;
} NVMETrimRangeIn_t;

typedef struct {
	U64	uoSectorCount;
	U64	uoLBA;
} TrimRangeOut_t;

typedef struct {
	PCA_t ulPCA;
	U32 ulLCA;
} ValidTable_t;

/*typedef struct {
	U32 ulvalue;
	U32 uloffset	: 31;
	U32 btIsValid	: 1;
} SMAXEntry_t;*/

typedef struct {
	U32	ulRegZinfoPos;
	struct {
		U32 ubVBPos	: 8;
		U32 VBLen	: 4;
		U32 RSV		: 20;
	} RegVBPos;
} DMACReg_t;

typedef union {
	struct {
		struct {
			U32	OP			: 5;
			U32 RSV1		: 10;
			U32 btCQ_EN		: 1;
			U32 ubSQ_TAG	: 8;
			U32	SRC_ADR_TYPE: 2;
			U32	DST_ADR_TYPE: 2;
			U32 RSV2		: 4;
		} DW0;
		U32 ulDW1;
		U32 ulDW2;
		U32 ulDW3;
		U32 ulDW4;
		U32 ulDW5;
		U32 ulDW6;
		U32 ulDW7;
	} COMMON;

	struct {
		struct {
			U32 OP				: 5;
			U32 btMERGE_IP_EN	: 1;
			U32 btCOP1_IP_EN	: 1;
			U32 btRSV1			: 1;
			U32 btE2E_CHK_EN	: 1;
			U32 btE2E_CHK_TYPE	: 1;
			U32 btE2E_GEN_EN	: 1;
			U32 btE2E_GEN_TYPE	: 1;
			U32	btMSK_EN		: 1;
			U32	btDEPEND		: 1;
			U32	btERR_INS		: 1;
			U32 btCQ_EN			: 1;
			U32 ubSQ_TAG		: 8;
			U32	SRC_ADR_TYPE	: 2;
			U32	DST_ADR_TYPE	: 2;
			U32 RSV2			: 4;
		} DW0;
		DMAC_ADDR_t ulSADD;
		DMAC_ADDR_t ulTADD;
		struct {
			U32 MERGE_FW_SET	: 24;
			U32 btMERGE_FW_VLD	: 1;
			U32 RSV				: 7;
		} DW3;
		U32	ulMSK;
		struct {
			U32 LEN			: 24;
			U32 ubPCACRC	: 8;
		} DW5;
		struct {
			U32 LCA			: 29;
			U32 NSID		: 3;
		} DW6;
		struct {
			U32 btVlid_Src			: 1;
			U32 btVlid_Dst			: 1;
			U32 btVlid_alloc_lb_Src	: 1;
			U32 btVlid_alloc_lb_Dst	: 1;
			U32 btUnlock_Src		: 1;
			U32 btUnlock_Dst		: 1;
			U32 btFree_Src			: 1;
			U32 btFree_Dst			: 1;
			U32 btGet_pbna_Src		: 1;
			U32 btGet_pbna_Dst		: 1;
			U32 LBID_S				: 3;
			U32 btDst_full_flag		: 1;
			U32 btDst_zero_flag		: 1;
			U32 btDst_cmd_end		: 1;
			U32 btSource_far		: 1;
			U32 btSource_qop		: 1;
			U32 target_oper			: 2;
			U32 btUN_AES_EN			: 1;
			U32 btUN_ZIP_EN			: 1;
			U32 btAES_EN			: 1;
			U32 btZIP_EN			: 1;
			U32 btCTS_EN			: 1;
			U32 ZINFO				: 3;
			U32 LBID_T				: 3;
			U32 btRSV				: 1;
		} DW7;
	} CPY;

	struct {
		struct {
			U32	OP				: 5;
			U32 RSV1			: 2;
			U32 btRSV2			: 1;
			U32 UNIT_SIZE		: 2;
			U32 btRND_EN		: 1;
			U32 btE2E_GEN_EN	: 1;
			U32 btMSK_EN		: 1;
			U32 btDEPEND		: 1;
			U32 btRSV3			: 1;
			U32 btCQ_EN			: 1;
			U32 ubSQ_TAG		: 8;
			U32 RSV4			: 2;
			U32	DST_ADR_TYPE	: 2;
			U32 RSV5			: 4;
		} DW0;
		U32 ulHighValue;
		DMAC_ADDR_t ulTADD;
		U32	ulLowValue;
		U32	ulMSK;
		struct {
			U32 LEN			: 24;
			U32 ubRSV		: 8;
		} DW5;
		struct {
			U32 LCA			: 29;
			U32 NSID		: 3;
		} DW6;
		struct {
			U32 btVlid_Src			: 1;
			U32 btVlid_Dst			: 1;
			U32 btVlid_alloc_lb_Src	: 1;
			U32 btVlid_alloc_lb_Dst	: 1;
			U32 btUnlock_Src		: 1;
			U32 btUnlock_Dst		: 1;
			U32 btFree_Src			: 1;
			U32 btFree_Dst			: 1;
			U32 btGet_pbna_Src		: 1;
			U32 btGet_pbna_Dst		: 1;
			U32 LBID_S				: 3;
			U32 btDst_full_flag		: 1;
			U32 btDst_zero_flag		: 1;
			U32 btDst_cmd_end		: 1;
			U32 btSource_far		: 1;
			U32 btSource_qop		: 1;
			U32 target_oper			: 2;
			U32 ubRSV1				: 8;
			U32 LBID_T				: 3;
			U32 btRSV2				: 1;
		} DW7;
	} SV;

	struct {
		struct {
			U32	OP			: 5;
			U32 RSV1		: 2;
			U32 btRSV2		: 1;
			U32 OP_ATTR		: 2;
			U32 btXOR_FAST	: 1;
			U32 btRSV3		: 1;
			U32 btMSK_EN	: 1;
			U32 btDEPEND	: 1;
			U32 btERR_INS	: 1;
			U32 btCQ_EN		: 1;
			U32 ubSQ_TAG	: 8;
			U32 SRC_ADR_TYPE: 2;
			U32	DST_ADR_TYPE: 2;
			U32 RSV4		: 4;
		} DW0;
		DMAC_ADDR_t ulSADD0;
		DMAC_ADDR_t ulTADD;
		DMAC_ADDR_t ulSADD1;
		U32	ulMSK;
		struct {
			U32 LEN			: 24;
			U32 ubRSV		: 8;
		} DW5;
		U32 ulLCA;
		U32 ulRSV;
	} XOR;

	struct {
		struct {
			U32	OP				: 5;
			U32 btXZIP_IP_EN	: 1;
			U32 RSV1			: 2;
			U32 btCMP_FAST		: 1;
			U32 RSV2			: 3;
			U32 btMSK_EN		: 1;
			U32 btDEPEND		: 1;
			U32 btERR_INS		: 1;
			U32 btCQ_EN			: 1;
			U32 ubSQ_TAG		: 8;
			U32 SRC_ADR_TYPE	: 2;
			U32	DST_ADR_TYPE	: 2;
			U32 RSV3			: 4;
		} DW0;
		DMAC_ADDR_t ulSADD0;
		U32			ulRSV1;
		DMAC_ADDR_t ulSADD1;
		U32			ulMSK;
		struct {
			U32	LEN				: 24;
			U32 ubRSV			: 8;
		} DW5;
		U32		ulLCA;
		U32		ulRSV2;
	} CMP;

	struct {
		struct {
			U32	OP			: 5;
			U32 RSV1		: 2;
			U32 btRSV2		: 1;
			U32 SORT_OP		: 3;
			U32 btLAST		: 1;
			U32 btSORT_ORDER: 1;
			U32 btDEPEND	: 1;
			U32 btERR_INS	: 1;
			U32 btCQ_EN		: 1;
			U32 ubSQ_TAG	: 8;
			U32 SRC_ADR_TYPE: 2;
			U32	DST_ADR_TYPE: 2;
			U32 RSV3		: 4;
		} DW0;
		DMAC_ADDR_t ulSADD;
		DMAC_ADDR_t ulTADD;
		struct {
			U32	entry_size	: 2;
			U32 RSV1		: 6;
			U32 key_idx		: 5;
			U32 RSV2		: 3;
			U32 key_msk		: 4;
			U32 RSV3		: 12;
		} sort_attr;
		U32			RSV1;
		struct {
			U32 LEN			: 24;
			U32 ubRSV		: 8;
		} DW5;
		U32			ulRSV2;
		U32			ulRSV3;
	} SORT;

	struct {
		struct {
			U32	OP			: 5;
			U32 RSV1		: 10;
			U32 btCQ_EN		: 1;
			U32 ubSQ_TAG	: 8;
			U32 ubRSV2		: 8;
		} DW0;
		U32			ulRSV1;
		U32			ulRSV2;
		U32			ulRSV3;
		U32			ulRSV4;
		struct {
			U32 LEN			: 24;
			U32 ubRSV		: 8;
		} DW5;
		U32			ulRSV5;
		U32			ulRSV6;
	} DMY;


	struct {
		struct {
			U32 OP				: 5;
			U32 RSV1			: 2;
			U32 btRSV2			: 1;
			U32 UNIT_SIZE		: 2;
			U32 btSMODE			: 1;
			U32 btIGNORE_EN		: 1;
			U32 btRSV3			: 1;
			U32 btDEPEND		: 1;
			U32 btRSV4			: 1;
			U32 btCQ_EN			: 1;
			U32 ubSQ_TAG		: 8;
			U32 SRC_ADR_TYPE	: 2;
			U32 DST_ADR_TYPE	: 2;
			U32 RSV5			: 4;
		} DW0;
		DMAC_ADDR_t ulSADD;
		DMAC_ADDR_t ulTADD;
		U32	ulSearch_mask;
		U32	ulMatch_mask;
		struct {
			U32 LEN				: 24;
			U32 ubRSV			: 8;
		} DW5;
		U32	ulIgnore_value;
		U32	ulMatch_value;
	} SMAX;

	struct {
		struct {
			U32	OP			: 5;
			U32 RSV1		: 2;
			U32 btRSV2		: 1;
			U32 btSMODE		: 1;
			U32 RSV3		: 2;
			U32 btRSV4		: 1;
			U32 btRSV5		: 1;
			U32 btDEPEND	: 1;
			U32 btERR_INS	: 1;
			U32 btCQ_EN		: 1;
			U32 ubSQ_TAG	: 8;
			U32 SRC_ADR_TYPE: 2;
			U32	RSV6		: 2;
			U32 RSV7		: 4;
		} DW0;
		DMAC_ADDR_t ulSADD;
		U32			ulRSV1;
		U32			ulRSV2;
		U32			ulRSV3;
		struct {
			U32 LEN			: 24;
			U32 ubRSV		: 8;
		} DW5;
		U32			ulRSV4;
		U32			ulRSV5;
	} SBIT;

	struct {
		struct {
			U32	OP				: 5;
			U32 RSV1			: 2;
			U32 btRSV2			: 1;
			U32 btFormat		: 1;
			U32 btMode			: 1;
			U32 btWL_ERR_CHK_EN	: 1;
			U32 btLBA2LCA_EN	: 1;
			U32 btRSV3			: 1;
			U32 btDEPEND		: 1;
			U32 btERR_INS		: 1;
			U32 btCQ_EN			: 1;
			U32 ubSQ_TAG		: 8;
			U32 SRC_ADR_TYPE	: 2;
			U32	DST_ADR_TYPE	: 2;
			U32 RSV4			: 4;
		} DW0;
		DMAC_ADDR_t ulSADD;
		DMAC_ADDR_t ulTADD;
		U32			ulRSV1;
		U32			ulRSV2;
		struct {
			U32 LEN			: 24;
			U32 ubRSV		: 8;
		} DW5;
		struct {
			U32 RSV			: 29;
			U32 NSID		: 3;
		} DW6;
		U32			ulRSV3;
	} TRIM_MERGE;

	struct {
		struct {
			U32	OP				: 5;
			U32 RSV1			: 2;
			U32 btFIND_ONE_ONLY	: 1;
			U32 UNIT_SIZE		: 2;
			U32 SMODE			: 2;
			U32 btFIND_LAST_ONE	: 1;
			U32 btDEPEND		: 1;
			U32 btERR_INS		: 1;
			U32 btCQ_EN			: 1;
			U32 ubSQ_TAG		: 8;
			U32 SRC_ADR_TYPE	: 2;
			U32	DST_ADR_TYPE	: 2;
			U32 RSV2			: 4;
		} DW0;
		DMAC_ADDR_t ulSADD;
		DMAC_ADDR_t ulTADD;
		U32			ulLowMSK;
		U32			ulHighMSK;
		struct {
			U32 LEN			: 24;
			U32 ubRSV			: 8;
		} DW5;
		U32			ulLowValue;
		U32			ulHighValue;
	} SRH;

	struct {
		struct {
			U32	OP				: 5;
			U32 RSV1			: 2;
			U32 btRSV2			: 1;
			U32 btSCAN_TYPE		: 1;
			U32 ZINFO_MSK		: 3;
			U32 btZINFO_MSK_EN	: 1;
			U32 btDEPEND		: 1;
			U32 btERR_INS		: 1;
			U32 btCQ_EN			: 1;
			U32 ubSQ_TAG		: 8;
			U32 SRC_ADR_TYPE	: 2;
			U32	DST_ADR_TYPE	: 2;
			U32 RSV3			: 4;
		} DW0;
		DMAC_ADDR_t ulSADD;
		DMAC_ADDR_t ulTADD;
		struct {
			U32	SRH_VB0		: 13;
			U32 TRG_TAB_LEN0: 3;
			U32	SRH_VB1		: 13;
			U32 TRG_TAB_LEN1: 3;
		} DW3;
		struct {
			U32	SRH_VB2		: 13;
			U32 TRG_TAB_LEN2: 3;
			U32	SRH_VB3		: 13;
			U32 TRG_TAB_LEN3: 3;
		} DW4;
		struct {
			U32 LEN			: 24;
			U32 ubRSV		: 8;
		} DW5;
		struct {
			U32	SRH_VB4		: 13;
			U32 TRG_TAB_LEN4: 3;
			U32	SRH_VB5		: 13;
			U32 TRG_TAB_LEN5: 3;
		} DW6;
		struct {
			U32	SRH_VB6		: 13;
			U32 TRG_TAB_LEN6: 3;
			U32	SRH_VB7		: 13;
			U32 TRG_TAB_LEN7: 3;
		} DW7;
	} SCAN_ZCODE;

	struct {
		struct {
			U32	OP			: 5;
			U32 RSV1		: 2;
			U32 btRSV2		: 1;
			U32 PTESIZE		: 2;
			U32 RSV3		: 3;
			U32 btDEPEND	: 1;
			U32 btERR_INS	: 1;
			U32 btCQ_EN		: 1;
			U32 ubSQ_TAG	: 8;
			U32 SRC_ADR_TYPE: 2;
			U32	DST_ADR_TYPE: 2;
			U32 RSV4		: 4;
		} DW0;
		DMAC_ADDR_t ulSADD;
		DMAC_ADDR_t ulTADD;
		struct {
			U32	START_IDX	: 12;
			U32 RSV			: 20;
		} DW3;
		U32 PTE_IDX;
		struct {
			U32 LEN			: 24;
			U32 ubRSV			: 8;
		} DW5;
		U32 RSV0;
		U32 RSV1;
	} SCAN_VALID0;

	struct {
		struct {
			U32	OP			: 5;
			U32 RSV1		: 2;
			U32 btRSV2		: 1;
			U32 RSV3		: 5;
			U32 btDEPEND	: 1;
			U32 btERR_INS	: 1;
			U32 btCQ_EN		: 1;
			U32 ubSQ_TAG	: 8;
			U32 SRC_ADR_TYPE: 2;
			U32	DST_ADR_TYPE: 2;
			U32 RSV4		: 4;
		} DW0;
		DMAC_ADDR_t ulSADD;
		DMAC_ADDR_t ulTADD;
		struct {
			U32	SRH_VB0		: 13;
			U32 RSV0		: 3;
			U32	SRH_VB1		: 13;
			U32 RSV1		: 3;
		} DW3;
		struct {
			U32	SRH_VB2		: 13;
			U32 RSV0		: 3;
			U32	SRH_VB3		: 13;
			U32 RSV1		: 3;
		} DW4;
		struct {
			U32 LEN			: 24;
			U32	ubRSV		: 8;
		} DW5;
		struct {
			U32	SRH_VB4		: 13;
			U32 RSV0		: 3;
			U32	SRH_VB5		: 13;
			U32 RSV1		: 3;
		} DW6;
		struct {
			U32	SRH_VB6		: 13;
			U32 RSV0		: 3;
			U32	SRH_VB7		: 13;
			U32 RSV1		: 3;
		} DW7;
	} SCAN_VALID1;

	struct {
		struct {
			U32	OP			: 5;
			U32 RSV1		: 2;
			U32 btRSV2		: 1;
			U32 ZINFO		: 3;
			U32 btRSV3		: 1;
			U32 btRSV4		: 1;
			U32 btDEPEND	: 1;
			U32 btERR_INS	: 1;
			U32 btCQ_EN		: 1;
			U32 ubSQ_TAG	: 8;
			U32 SRC_ADR_TYPE: 2;
			U32	DST_ADR_TYPE: 2;
			U32 RSV5		: 4;
		} DW0;
		U32			ulSTART_PCA;
		DMAC_ADDR_t ulTADD;
		U32			ulSKIP_PCA0;
		U32			ulSKIP_PCA1;
		struct {
			U32 LEN			: 24;
			U32 ubRSV		: 8;
		} DW5;
		U32			ulSKIP_PCA2;
		U32			ulSKIP_PCA3;
	} SEQ_PTE;

	struct {
		struct {
			U32	OP			: 5;
			U32 RSV1		: 2;
			U32 btRSV2		: 1;
			U32 PTESIZE		: 2;
			U32 PMDSIZE		: 2;
			U32 btRSV3		: 1;
			U32 btDEPEND	: 1;
			U32 btERR_INS	: 1;
			U32 btCQ_EN		: 1;
			U32 ubSQ_TAG	: 8;
			U32 SRC_ADR_TYPE: 2;
			U32	RSV4		: 2;
			U32 RSV5		: 4;
		} DW0;
		DMAC_ADDR_t ulSADD;
		U32			ulRSV1;
		U32			ulRSV2;
		U32			ulPTE_MSK;
		struct {
			U32 LEN			: 24;
			U32 ubRSV		: 8;
		} DW5;
		U32			ulRSV3;
		U32			ulRSV4;
	} CNT_PTE;

	struct {
		struct {
			U32	OP			: 5;
			U32 RSV1		: 2;
			U32 btRSV2		: 1;
			U32 UNIT_SIZE	: 2;
			U32 RSV3		: 3;
			U32 btDEPEND	: 1;
			U32 btRSV4		: 1;
			U32 btCQ_EN		: 1;
			U32 ubSQ_TAG	: 8;
			U32 SRC_ADR_TYPE: 2;
			U32	DST_ADR_TYPE: 2;
			U32 RSV6		: 4;
		} DW0;
		DMAC_ADDR_t ulSADD;
		DMAC_ADDR_t ulTADD;
		U32			ulLowMSK;
		U32			ulHighMSK;
		struct {
			U32 LEN			: 24;
			U32 ubRSV		: 8;
		} DW5;
		U32 ulLowValue;
		U32 ulHighValue;
	} SVM;

} DMACCmd_t;

typedef struct {
	union {
		U64	uoSTA;
		struct {
			U64	btSTA		: 1;
			U64	btAXI_R_ERR	: 1;
			U64	AXI_R_SID	: 2;
			U64	btAXI_W_ERR	: 1;
			U64	AXI_W_SID	: 2;
			U64 btPTY_ERR	: 1;
			U64 ubCQ_TAG	: 8;
			U64	RSV			: 48;
			/*
			U64 ubRSV0		: 8;
			U64 ubRSV1		: 8;
			U64 ulRSV2		: 32;
			*/
		} COMM;

		struct {
			U64	btSTA		: 1;
			U64	btAXI_R_ERR	: 1;
			U64	AXI_R_SID	: 2;
			U64	btAXI_W_ERR	: 1;
			U64	AXI_W_SID	: 2;
			U64 btPTY_ERR	: 1;
			U64 ubCQ_TAG	: 8;
			U64 ubBMU_STS1	: 8;
			U64 ubBMU_STS2	: 8;
			U64 ulRSLT		: 32;
		} CPY_RLT;

		struct {
			U64	btSTA		: 1;
			U64	btAXI_R_ERR	: 1;
			U64	AXI_R_SID	: 2;
			U64	btAXI_W_ERR	: 1;
			U64	AXI_W_SID	: 2;
			U64 btPTY_ERR	: 1;
			U64 ubCQ_TAG	: 8;
			U64 ubBMU_STS1	: 8;
			U64 ubBMU_STS2	: 8;
			U64 ulRSLT		: 32;
		} SV_RLT;

		struct {
			U64	btSTA		: 1;
			U64	btAXI_R_ERR	: 1;
			U64	AXI_R_SID	: 2;
			U64	btAXI_W_ERR	: 1;
			U64	AXI_W_SID	: 2;
			U64 btPTY_ERR	: 1;
			U64 ubCQ_TAG	: 8;
			U64 ubRSV1		: 8;
			U64 ubRSV2		: 8;
			U64 ulRSV3		: 32;
		} XOR_RLT;

		struct {
			U64	btCMP_ERR		: 1;
			U64	btAXI_R_ERR		: 1;
			U64	AXI_R_SID		: 2;
			U64	btAXI_W_ERR		: 1;
			U64	AXI_W_SID		: 2;
			U64 btPTY_ERR		: 1;
			U64 ubCQ_TAG		: 8;
			U64 btDATA_CMP_ERR	: 1;
			U64	RSV				: 15;
			U64 ulOFFSET		: 32;
		} CMP_RLT;

		struct {
			U64	btSTA		: 1;
			U64	btAXI_R_ERR	: 1;
			U64	AXI_R_SID	: 2;
			U64	btAXI_W_ERR	: 1;
			U64	AXI_W_SID	: 2;
			U64 btPTY_ERR	: 1;
			U64 ubCQ_TAG	: 8;
			U64	CMP_ERR_BMU_STS_INFO1	: 2;
			U64	CMP_ERR_BMU_STS_INFO2	: 2;
			U64	CMP_ERR_RSV				: 4;
			U64 ubR0		: 8;
			U64 ulRSLT		: 32;
		} SORT_RLT;

		struct {
			U64	btSTA		: 1;
			U64	btAXI_R_ERR	: 1;
			U64	AXI_R_SID	: 2;
			U64	btAXI_W_ERR	: 1;
			U64	AXI_W_SID	: 2;
			U64 btPTY_ERR	: 1;
			U64 ubCQ_TAG	: 8;
			U64 ubRSV1		: 8;
			U64 ubRSV2		: 8;
			U64 ulRSV3		: 32;
		} DMY_RLT;

		struct {
			U64	btSTA		: 1;
			U64	btAXI_R_ERR	: 1;
			U64	AXI_R_SID	: 2;
			U64	btAXI_W_ERR	: 1;
			U64	AXI_W_SID	: 2;
			U64 btPTY_ERR	: 1;
			U64 ubCQ_TAG	: 8;
			U64 uwRSV1		: 16;
			U64 ulRSV2		: 32;
		} SMAX_RLT;

		struct {
			U64 btFOUND		: 1;
			U64	btAXI_R_ERR	: 1;
			U64	AXI_R_SID	: 2;
			U64	btAXI_W_ERR	: 1;
			U64	AXI_W_SID	: 2;
			U64 btPTY_ERR	: 1;
			U64 ubCQ_TAG	: 8;
			U64 uwRSV		: 16;
			U64 ulRSLT		: 32;
		} SBIT_RLT;

		struct {
			U64 btFOUND		: 1;
			U64	btAXI_R_ERR	: 1;
			U64	AXI_R_SID	: 2;
			U64	btAXI_W_ERR	: 1;
			U64	AXI_W_SID	: 2;
			U64 btPTY_ERR	: 1;
			U64 ubCQ_TAG	: 8;
			U64 uwRSV		: 16;
			U64 ulRSLT		: 32;
		} TRIM_MERGE_RLT;

		struct {
			U64 btHIT			: 1;
			U64	AXI_R_SID	: 2;
			U64	AXI_W_SID	: 2;
			U64 btPTY_ERR0	: 1;
			U64 btPTY_ERR1	: 1;
			U64 btSrh_type	: 1;
			U64 ubCQ_TAG	: 8;
			U64 uwOFS		: 16;
			U64 ulNUM		: 32;
		} SHR_RLT;

		struct {
			U64	btSTA		: 1;
			U64	btAXI_R_ERR	: 1;
			U64	AXI_R_SID	: 2;
			U64	btAXI_W_ERR	: 1;
			U64	AXI_W_SID	: 2;
			U64 btPTY_ERR	: 1;
			U64 ubCQ_TAG	: 8;
			U64 uwRSV		: 16;
			U64 ulRSLT		: 32;
		} SCAN_ZCODE_RLT;

		struct {
			U64	btSTA		: 1;
			U64	btAXI_R_ERR	: 1;
			U64	AXI_R_SID	: 2;
			U64	btAXI_W_ERR	: 1;
			U64	AXI_W_SID	: 2;
			U64 btPTY_ERR	: 1;
			U64 ubCQ_TAG	: 8;
			U64 uwRSV0		: 16;
			U64 ulRSV1		: 32;
		} SCAN_VALID0_RLT;

		struct {
			U64	btSTA		: 1;
			U64	btAXI_R_ERR	: 1;
			U64	AXI_R_SID	: 2;
			U64	btAXI_W_ERR	: 1;
			U64	AXI_W_SID	: 2;
			U64 btPTY_ERR	: 1;
			U64 ubCQ_TAG	: 8;
			U64 uwRSV		: 16;
			U64 ulSTOP_IDX	: 32;
		} SCAN_VALID1_RLT;

		struct {
			U64 btSKIP_ERR	: 1;
			U64	btAXI_R_ERR	: 1;
			U64	AXI_R_SID	: 2;
			U64	btAXI_W_ERR	: 1;
			U64	AXI_W_SID	: 2;
			U64 btPTY_ERR	: 1;
			U64 ubCQ_TAG	: 8;
			U64 ubRSV1		: 8;
			U64 ubRSV2		: 8;
			U64 ulRSLT		: 32;
		} SEQ_PTE_RLT;

		struct {
			U64	btSTA		: 1;
			U64	btAXI_R_ERR	: 1;
			U64	AXI_R_SID	: 2;
			U64	btAXI_W_ERR	: 1;
			U64	AXI_W_SID	: 2;
			U64 btPTY_ERR	: 1;
			U64 ubCQ_TAG	: 8;
			U64 ubRSV1		: 8;
			U64 ubRSV2		: 8;
			U64 uwPTE_CNT	: 16;
			U64 uwPMD_CNT	: 16;
		} CNT_PTE_RLT;
	} STA;
} DMACCmdSts_t;

typedef struct {
	U32 ulErrorQueueBitmap;
	U32 ulErrorInfo[4];
	U32 ulSQ[8];
	U32 ulCQ[2];
	U32 ulPerrCnt;
} DMACErrorInfo_t;
#endif /* _DMAC_CMD_H_ */
