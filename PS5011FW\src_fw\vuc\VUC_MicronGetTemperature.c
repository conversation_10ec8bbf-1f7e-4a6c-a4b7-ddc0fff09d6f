#include "VUC_MicronGetTemperature.h"
#include "VUC_MicronGetNandErrorRecoveryStatistics.h"
#include "VUC_MicronResponse.h"
#include "hal/sys/api/angc/angc_api.h"
#include "hal/dmac/dmac_pop_cmd.h"

#if (VUC_MICRON_COMMON_VS_COMMANDS_EN)

void VUCMicronGetTemperatureSensor(U32 ulPayloadAddr)
{
	U8 ubChannel, ubCE, ubLUN, ubTmp[50] = "", ubTmp2[10];
	U32 ulTmp, ulResponsePayload = ulPayloadAddr + VUC_MICRON_RESPONSE_HEADER_SIZE;
	GetTemperatureResponseHEADER_t *pResponseHeader;
	pResponseHeader = (GetTemperatureResponseHEADER_t *)ulPayloadAddr;

	DMACParam_t DMACParam;
	DMACParam.DMACSetValue.ulDestAddr = ulPayloadAddr;
	DMACParam.DMACSetValue.ulLowValue = 0;
	DMACParam.DMACSetValue.ulHighValue = 0;
	DMACParam.DMACSetValue.ul32ByteNum = SIZE_IN_32B(DEF_KB(FRAMES_PER_PAGE));
	DMACParam.DMACSetValue.btValidate = FALSE;
	gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
	DMACSetValue(&DMACParam, gulDMACDirectWaitDone_Callback, 0);
	while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
		DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
	}

	pResponseHeader->ubResponseHeaderFormatVersion = 0x00;
	pResponseHeader->ubResponseDataFotmatVersion = VUC_MICRON_RESPONSE_FORMAT_JSON;
	pResponseHeader->uwCMDClass = VUC_MICRON_GET_TEMPERATURE_COMMAND_CLASS;
	pResponseHeader->uwCMDCode = VUC_MICRON_GET_TEMPERATURE_COMMAND_CODE;
	pResponseHeader->uwStatus = 0;
	pResponseHeader->ulDataPayloadSize = FRAME_SIZE;

	VUCMyStrcat((void *)ulResponsePayload, "{\n\t");
	VUCMyStrcat((void *)ulResponsePayload, "\"structVer\":1,\n\t");
	VUCMyStrcat((void *)ulResponsePayload, "\"ASIC1\":");

	ulTmp = M_ANGC_GET_TEMPERATURE_SENSOR_CELSIUS();
	VUCMyitoa(ulTmp, (void *)ubTmp, DECIMAL);

	VUCMyStrcat((void *)ulResponsePayload, (void *)ubTmp);
	VUCMyStrcat((void *)ulResponsePayload, ",\n\t");
	VUCMyStrcat((void *)ulResponsePayload, "\"NAND\":[\n\t\t");

	for (ubLUN = 0; ubLUN < gFlhEnv.ubLUNperTarget; ubLUN++) {
		for (ubChannel = 0; ubChannel < gFlhEnv.ubChannelExistNum; ubChannel++) {
			for (ubCE = 0; ubCE < (gFlhEnv.ubCENumber / gFlhEnv.ubChannelExistNum); ubCE++) {

				VUCMyStrcat((void *)ulResponsePayload, "{\n\t\t\t\"Channel\":");
				VUCMyitoa(ubChannel, (void *)ubTmp, DECIMAL);
				VUCMyStrcat((void *)ulResponsePayload, (void *)ubTmp);
				VUCMyStrcat((void *)ulResponsePayload, ",\n\t\t\t\"CE\":");
				VUCMyitoa(ubCE, (void *)ubTmp, DECIMAL);
				VUCMyStrcat((void *)ulResponsePayload, (void *)ubTmp);
				VUCMyStrcat((void *)ulResponsePayload, ",\n\t\t\t\"LUN\":");
				VUCMyitoa(ubLUN, (void *)ubTmp, DECIMAL);
				VUCMyStrcat((void *)ulResponsePayload, (void *)ubTmp);
				VUCMyStrcat((void *)ulResponsePayload, ",\n\t\t\t\"Temp\":");


				FIPScanTemperature(ubChannel, ubCE, ubLUN, (U8 *)ubTmp);

				ubTmp[0] = ubTmp[0] -  VUC_MICRON_TEMPERSTURE_BASE;
				ubTmp[1] = '\0';

				VUCMyitoa(ubTmp[0], &ubTmp2[0], DECIMAL);

				VUCMyStrcat((void *)ulResponsePayload, (void *)ubTmp2);

				if (((gFlhEnv.ubLUNperTarget - 1) == ubLUN) && ( (gFlhEnv.ubChannelExistNum - 1) == ubChannel) && (((gFlhEnv.ubCENumber / gFlhEnv.ubChannelExistNum) - 1) == ubCE)) {
					VUCMyStrcat((void *)ulResponsePayload, "}]\n}");
				}
				else {
					VUCMyStrcat((void *)ulResponsePayload, "},\n\t\t");
				}
			}
		}
	}
}

#endif /*(VUC_MICRON_COMMON_VS_COMMANDS_EN)*/
