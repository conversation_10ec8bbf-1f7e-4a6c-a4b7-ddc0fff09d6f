/****************************************************************************
*
*  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
*  All rights reserved
*
*  The content of this document is confidential and shall be applied
*  subject to the terms and conditions of the license agreement and
*  other applicable laws. Any unauthorized access, use or disclosure
*  of this document is strictly prohibited and may be punishable
*  under laws.
*
*  FTL_inline.h
*
*
*
****************************************************************************/

#ifndef FTL_INLINE_H_
#define FTL_INLINE_H_

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "table/vbmap/vbmap_api.h"
#include "common/fw_common.h"
#include "ftl/ftl.h"

/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */
INLINE U8 FTLCheckPCAIsSLCMode(U32 ulPCA)
{
	return (U8)(gpuwVBRMP[ulPCA >> gub4kEntrysPerUnitAlignLog].B.btIsSLCMode);
}

INLINE void FTLClearProgDummyOrJournalBitmap(U32 ulClearBit)
{
	gXferDataInParam.ulProgDummyOrJournalBitmap &= ~(ulClearBit);
}

INLINE U32 WLGetD3ECAverageInline(void)
{
	/*exclude reduce unit*/
	return ((gpVT->ulTotalEraseCount > gpVT->ulD3ReduceEraseCnt) ? ((gpVT->ulTotalEraseCount - gpVT->ulD3ReduceEraseCnt) / (gpVT->uwTotalD3UnitNum - gpVTDBUF->ErrorHandle.uwTotalReduceUnits - gpVT->uwNonFreepoolUnitNum)) : 0);
}

INLINE U32 WLGetD1ECAverageInline(void)
{
	/*exclude reduce unit*/
	return ((gpVT->ulTotalD1EraseCount > gpVT->ulD1ReduceEraseCnt) ? ((gpVT->ulTotalD1EraseCount - gpVT->ulD1ReduceEraseCnt) / (gpVT->uwTotalD1UnitNum - gpVTDBUF->ErrorHandle.uwTotalD1ReduceUnits)) : 0);
}

INLINE U32 FTLGetD3ECAverageInline(void)
{
	return ((gpVT->ulTotalEraseCount) / (gpVT->uwTotalD3UnitNum - gpVT->uwNonFreepoolUnitNum));
}

INLINE U32 FTLGetD1ECAverageInline(void)
{
	return ((gpVT->ulTotalD1EraseCount) / (gpVT->uwTotalD1UnitNum));
}

#if ((!BURNER_MODE_EN) && (!RDT_MODE_EN))
INLINE U8 PCANeedCheckLastPCAReg(U32 ulPCA) //need to check PCA if UTILIZE_SLC_UNUSE_PCA_BIT_EN is on
{
	U32 ulEntry = ulPCA & gul4kEntrysPerUnitAlignMask;
	U32 ulCheckEntry = (gpVT->GR.ulPlaneIndex < (U32)gubPlanesPerSuperPage * 4) ? 0 : (gpVT->GR.ulPlaneIndex - (U32)gubPlanesPerSuperPage * 4);
	return ((M_GET_UNIT_VALUE_FROM_PCA(ulPCA) == (gpVT->GR.uwUnit[gpVT->GR.ubUnitIndex].B.uwUnit)) && (ulEntry >= ulCheckEntry));
}
#endif /* ((!BURNER_MODE_EN) && (!RDT_MODE_EN)) */

#if TWO_PASS_EN
INLINE U8 FTLIs1stThen1st(U32 ulPlaneIdx)
{
	U8 ubResult = FALSE;
#if IM_N48R
	U16 uwPageIdx = 0;
	U16 uwCellNum = 0;
	U8 ubPlaneBankIdx = 0;

	FTLPlaneInx2Physical(ulPlaneIdx, &uwPageIdx, &ubPlaneBankIdx);
	uwCellNum = FTLGetCoord(uwPageIdx, IM_GETCOORD_CELLNUM_VAL);

	if ((uwCellNum <= (IM_N48_CELLNUM_SECTION_1 + IM_N48_2PASS_DIFF))
		|| ((uwCellNum > (IM_N48_CELLNUM_SECTION_2 + IM_N48_2PASS_DIFF)) && (uwCellNum <= (IM_N48_CELLNUM_SECTION_3 + IM_N48_2PASS_DIFF)))) {
		ubResult = TRUE;
	}
#else /* IM_N48R */
	// TSB QLC
	ubResult = (ulPlaneIdx <= guwKeepSuperLayerPlaneNum);
#endif /* IM_N48R */
	return ubResult;
}

INLINE U8 FTLIs2ndThen2nd(U32 ulPlaneIdx)
{
#if IM_N48R
	return FALSE;
#else /* IM_N48R */
	// TSB QLC
	return (ulPlaneIdx >= (gulPlanesPerUnit - guwKeepSuperLayerPlaneNum));
#endif /* IM_N48R */
}

INLINE U32 FTLGetTwoPassAddPlane(U8 ubIs1stDoing, U32 ulPlaneIdx)
{
	U32 ulAddPlaneCnt = 0;
#if IM_N48R
	if (ubIs1stDoing) {
		U16 uwPageIdx = 0;
		U16 uwCellNum = 0;
		U8 ubPlaneBankIdx = 0;
		FTLPlaneInx2Physical(ulPlaneIdx, &uwPageIdx, &ubPlaneBankIdx);
		uwCellNum = FTLGetCoord(uwPageIdx, IM_GETCOORD_CELLNUM_VAL);
		// add uwAddPlaneCnt to fill the 2nd-pass hole
		if ((IM_N48_CELLNUM_SECTION_1 + IM_N48_2PASS_DIFF) == uwCellNum) {
			ulAddPlaneCnt = gubPlanesPerSuperPage * IM_N48_SECTION_1;
		}
		else if ((IM_N48_CELLNUM_SECTION_3 + IM_N48_2PASS_DIFF) == uwCellNum) {
			ulAddPlaneCnt = gubPlanesPerSuperPage * (IM_N48_SECTION_3 - IM_N48_SECTION_2);
		}
	}
#endif /* IM_N48R */
	return ulAddPlaneCnt;
}

INLINE U8 FTLIsPlaneHitMinorLayer(U32 ulPlaneIdx)
{
	U8 ubResult = FALSE;
#if IM_N48R
	U16 uwPageIdx = 0;
	U8 ubPlaneBankIdx = 0;

	if (ulPlaneIdx == gulPlanesPerUnit) { // need?
		ubResult = TRUE;
	}
	else {
		FTLPlaneInx2Physical(ulPlaneIdx, &uwPageIdx, &ubPlaneBankIdx);
		if ((0 == ubPlaneBankIdx) && (IM_LP_PAGE == FTLCheckPageType(ulPlaneIdx))) {
			ubResult = TRUE;
		}
	}
#else /* IM_N48R */
	// TSB QLC
	ubResult = (0 == ulPlaneIdx % guwKeepMinorLayerPlaneNum);
#endif /* IM_N48R */
	return ubResult;
}
#endif /* TWO_PASS_EN */
#endif // FTL_INLINE_H_
