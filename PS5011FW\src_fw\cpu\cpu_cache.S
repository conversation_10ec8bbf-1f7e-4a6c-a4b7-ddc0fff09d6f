
.section .text

.globl ARM_DCache_Enable
.globl ARM_DCache_Disable
.globl ARM_DCache_Clean_All
.globl ARM_DCache_Invalidate_All
.globl ARM_DCache_Clean_Invalidate_All
.globl ARM_DCache_Clean_Range
.globl ARM_DCache_Invalidate_Range
.globl ARM_DCache_Clean_Invalidate_Range

	.type ARM_DCache_Enable, "function"
	.type ARM_DCache_Disable, "function"

	.type ARM_ICache_Enable, "function"
	.type ARM_ICache_Disable, "function"

	.type ARM_ICache_Invalidate_All, "function"


	.type ARM_DCache_Clean_All, "function"
	.type ARM_DCache_Invalidate_All, "function"
	.type ARM_DCache_Clean_Invalidate_All, "function"
	// By Range
	.type ARM_DCache_Clean_Range, "function"
	.type ARM_DCache_Invalidate_Range, "function"
	.type ARM_DCache_Clean_Invalidate_Range, "function"
	// By Line
	.type ARM_DCache_Clean_Line, "function"
	.type ARM_DCache_Invalidate_Line, "function"
	.type ARM_DCache_Clean_Invalidate_Line, "function"



ARM_DCache_Enable:
 	MRC     p15, 0, r0, c1, c0, 0   // Read System Control Register configuration data
 	ORR     r0, r0, #(1 << 2)       // Set C bit
 	MCR     p15, 0, r0, c1, c0, 0   // Write System Control Register configuration data
 	BX      lr

ARM_DCache_Disable:
	MRC     p15, 0, r0, c1, c0, 0   // Read System Control Register configuration data
	BIC     r0, r0, #(1 << 2)       // Clear C bit
	MCR     p15, 0, r0, c1, c0, 0   // Write System Control Register configuration data
	BX      lr

ARM_ICache_Enable:
 	MRC     p15, 0, r0, c1, c0, 0   // Read System Control Register configuration data
 	ORR     r0, r0, #(1 << 12)      // Set I bit
 	MCR     p15, 0, r0, c1, c0, 0   // Write System Control Register configuration data
 	BX      lr

ARM_ICache_Disable:
	MRC     p15, 0, r0, c1, c0, 0   // Read System Control Register configuration data
	BIC     r0, r0, #(1 << 12)      // Clear I bit
	MCR     p15, 0, r0, c1, c0, 0   // Write System Control Register configuration data
	BX      lr

// ------------------------------------------------------------
// ICache invalidate all
// ------------------------------------------------------------

ARM_ICache_Invalidate_All:
	DSB     						// Complete all outstanding explicit memory operations
	MOV     r0, #0
	MCR     p15, 0, r0, c7, c5, 0   // ICIALLU - Invalidate entire I Cache, and flushes branch target cache
	ISB
	BX      lr

// ------------------------------------------------------------
// DCache invalidate all
// ------------------------------------------------------------
ARM_DCache_Invalidate_All:
	PUSH    {r4-r12}
	DSB     // Complete all outstanding explicit memory operations

  //
  // Based on code example given in section B2.2.4/11.2.4 of ARM DDI 0406B
  //

	MRC     p15, 1, r0, c0, c0, 1     // Read CLIDR
	ANDS    r3, r0, #0x7000000
	MOV     r3, r3, LSR #23           // Cache level value (naturally aligned)
	BEQ     Invalidate_Caches_Finished
	MOV     r10, #0

Invalidate_Caches_Loop_1:
	ADD     r2, r10, r10, LSR #1      // Work out 3xcachelevel
	MOV     r1, r0, LSR r2            // bottom 3 bits are the Cache type for this level
	AND     r1, r1, #7                // get those 3 bits alone
	CMP     r1, #2
	BLT     Invalidate_Caches_Skip    // no cache or only instruction cache at this level
	MCR     p15, 2, r10, c0, c0, 0    // write the Cache Size selection register
	ISB                               // ISB to sync the change to the CacheSizeID reg
	MRC     p15, 1, r1, c0, c0, 0     // reads current Cache Size ID register
	AND     r2, r1, #7                // extract the line length field
	ADD     r2, r2, #4                // add 4 for the line length offset (log2 16 bytes)
	LDR     r4, =0x3FF
	ANDS    r4, r4, r1, LSR #3        // R4 is the max number on the way size (right aligned)
	CLZ     r5, r4                    // R5 is the bit position of the way size increment
	LDR     r7, =0x00007FFF
	ANDS    r7, r7, r1, LSR #13       // R7 is the max number of the index size (right aligned)

Invalidate_Caches_Loop_2:
	MOV     r9, R4                    // R9 working copy of the max way size (right aligned)

Invalidate_Caches_Loop_3:
	ORR     r11, r10, r9, LSL r5      // factor in the way number and cache number into R11
	ORR     r11, r11, r7, LSL r2      // factor in the index number
	MCR     p15, 0, r11, c7, c6, 2    // DCISW - invalidate by set/way
	SUBS    r9, r9, #1                // decrement the way number
	BGE     Invalidate_Caches_Loop_3
	SUBS    r7, r7, #1                // decrement the index
	BGE     Invalidate_Caches_Loop_2

Invalidate_Caches_Skip:
	ADD     r10, r10, #2              // increment the cache number
	CMP     r3, r10
	BGT     Invalidate_Caches_Loop_1

Invalidate_Caches_Finished:
	POP     {r4-r12}
	DSB 	// Ensure completion
	BX      lr

// ------------------------------------------------------------
// DCache clean and invalidate all
// ------------------------------------------------------------
ARM_DCache_Clean_Invalidate_All:
  PUSH    {r4-r12}
  DSB     // Complete all outstanding explicit memory operations

  //
  // Based on code example given in section 11.2.4 of ARM DDI 0406B
  //

  MRC     p15, 1, r0, c0, c0, 1     // Read CLIDR
  ANDS    r3, r0, #0x7000000
  MOV     r3, r3, LSR #23           // Cache level value (naturally aligned)
  BEQ     Clean_Invalidate_DCache_Finished
  MOV     r10, #0

Clean_Invalidate_DCache_Loop_1:
  ADD     r2, r10, r10, LSR #1      // Work out 3xcachelevel
  MOV     r1, r0, LSR r2            // bottom 3 bits are the Cache type for this level
  AND     r1, r1, #7                // get those 3 bits alone
  CMP     r1, #2
  BLT     Clean_Invalidate_DCache_Skip // no cache or only instruction cache at this level
  MCR     p15, 2, r10, c0, c0, 0    // write the Cache Size selection register
  ISB                               // ISB to sync the change to the CacheSizeID reg
  MRC     p15, 1, r1, c0, c0, 0     // reads current Cache Size ID register
  AND     r2, r1, #7                // extract the line length field
  ADD     r2, r2, #4                // add 4 for the line length offset (log2 16 bytes)
  LDR     r4, =0x3FF
  ANDS    r4, r4, r1, LSR #3        // R4 is the max number on the way size (right aligned)
  CLZ     r5, r4                    // R5 is the bit position of the way size increment
  LDR     r7, =0x00007FFF
  ANDS    r7, r7, r1, LSR #13       // R7 is the max number of the index size (right aligned)

Clean_Invalidate_DCache_Loop_2:
  MOV     r9, R4                    // R9 working copy of the max way size (right aligned)

Clean_Invalidate_DCache_Loop_3:
  ORR     r11, r10, r9, LSL r5      // factor in the way number and cache number into R11
  ORR     r11, r11, r7, LSL r2      // factor in the index number
  MCR     p15, 0, r11, c7, c14, 2   // DCCISW - clean and invalidate by set/way
  SUBS    r9, r9, #1                // decrement the way number
  BGE     Clean_Invalidate_DCache_Loop_3
  SUBS    r7, r7, #1                // decrement the index
  BGE     Clean_Invalidate_DCache_Loop_2

Clean_Invalidate_DCache_Skip:
  ADD     r10, r10, #2              // increment the cache number
  CMP     r3, r10
  BGT     Clean_Invalidate_DCache_Loop_1

Clean_Invalidate_DCache_Finished:
  POP     {r4-r12}
  DSB 		// Ensure completion
  BX      lr


// ------------------------------------------------------------
// DCache clean all
// ------------------------------------------------------------

ARM_DCache_Clean_All:
	PUSH    {r4-r12}
	DSB     						// Complete all outstanding explicit memory operations
	//
	//	Based on code example given in section 11.2.4 of ARM DDI 0406B
	//
	MRC     p15, 1, r0, c0, c0, 1     // Read CLIDR
	ANDS    r3, r0, #0x7000000
	MOV     r3, r3, LSR #23           // Cache level value (naturally aligned)
	BEQ     Clean_DCache_Finished
	MOV     r10, #0

Clean_DCache_Loop_1:
	ADD     r2, r10, r10, LSR #1      // Work out 3xcachelevel
	MOV     r1, r0, LSR r2            // bottom 3 bits are the Cache type for this level
	AND     r1, r1, #7                // get those 3 bits alone
	CMP     r1, #2
	BLT     Clean_DCache_Skip         // no cache or only instruction cache at this level
	MCR     p15, 2, r10, c0, c0, 0    // write the Cache Size selection register
	ISB                               // ISB to sync the change to the CacheSizeID reg
	MRC     p15, 1, r1, c0, c0, 0     // reads current Cache Size ID register
	AND     r2, r1, #7                // extract the line length field
	ADD     r2, r2, #4                // add 4 for the line length offset (log2 16 bytes)
	LDR     r4, =0x3FF
	ANDS    r4, r4, r1, LSR #3        // R4 is the max number on the way size (right aligned)
	CLZ     r5, r4                    // R5 is the bit position of the way size increment
	LDR     r7, =0x00007FFF
	ANDS    r7, r7, r1, LSR #13       // R7 is the max number of the index size (right aligned)

Clean_DCache_Loop_2:
	MOV     r9, R4                    // R9 working copy of the max way size (right aligned)

Clean_DCache_Loop_3:
	ORR     r11, r10, r9, LSL r5      // factor in the way number and cache number into R11
	ORR     r11, r11, r7, LSL r2      // factor in the index number
	MCR     p15, 0, r11, c7, c10, 2   // DCCSW - clean by set/way
	SUBS    r9, r9, #1                // decrement the way number
	BGE     Clean_DCache_Loop_3
	SUBS    r7, r7, #1                // decrement the index
	BGE     Clean_DCache_Loop_2

Clean_DCache_Skip:
	ADD     r10, r10, #2              // increment the cache number
	CMP     r3, r10
	BGT     Clean_DCache_Loop_1

Clean_DCache_Finished:
  POP     {r4-r12}
  DSB 		// Ensure completion
  BX      lr

// ------------------------------------------------------------
// DCache clean by range
// ------------------------------------------------------------
ARM_DCache_Clean_Range:
    DSB     // Complete all outstanding explicit memory operations

    MRC     p15, 0, r2, c0, c0, 1 // Read CTR (cache type register)
    UBFX    r2, r2, #16, #4 // Extract DMinLine, gives log2 of the number of words
    MOV     r3, #1
    MOV     r2, r3, LSL r2 // Convert to words
    LSL     r2, r2, #2 // Convert from words to bytes

  // Calculate end address
    ADD     r1, r0, r1 // End (r1) = start (r0) + size (r1)
    BIC     r0, r0, #0x1F

DCache_Clean_Loop:
    MCR     p15, 0, r0, c7, c10, 1 // DCCMVAC, clean data cache line by MVA to PoC
    ADD     r0, r0, r2 // Increment address by cache line length
    CMP     r0, r1 // Check for end of range
    BLT     DCache_Clean_Loop
    DSB // Ensure completion
    BX      lr




// ------------------------------------------------------------
// DCache invalidate by range
// ------------------------------------------------------------
ARM_DCache_Invalidate_Range:
    DSB     // Complete all outstanding explicit memory operations

    MRC     p15, 0, r2, c0, c0, 1 // Read CTR (cache type register)
    UBFX    r2, r2, #16, #4 // Extract DMinLine, gives log2 of the number of words
    MOV     r3, #1
    MOV     r2, r3, LSL r2 // Convert to words
    LSL     r2, r2, #2 // Convert from words to bytes

    // Calculate end address
    ADD     r1, r0, r1 // End (r1) = start (r0) + size (r1)
    BIC     r0, r0, #0x1F

DCache_Invalidate_Loop:
    MCR     p15, 0, r0, c7, c6, 1 // DCIMVAC, invalidate data cache line by MVA to PoC
    ADD     r0, r0, r2 // Increment address by cache line length
    CMP     r0, r1 // Check for end of range
    BLT     DCache_Invalidate_Loop
    DSB // Ensure completion
    BX      lr

// ------------------------------------------------------------
// DCache clean and invalidate by range
// ------------------------------------------------------------
ARM_DCache_Clean_Invalidate_Range:
    DSB                             // Complete all outstanding explicit memory operations

    MRC     p15, 0, r2, c0, c0, 1   // Read CTR (cache type register)
    UBFX    r2, r2, #16, #4         // Extract DMinLine, gives log2 of the number of words
    MOV     r3, #1
    MOV     r2, r3, LSL r2          // Convert to words
    LSL     r2, r2, #2              // Convert from words to bytes
    // Calculate end address
    ADD     r1, r0, r1              // End (r1) = start (r0) + size (r1)
    BIC     r0, r0, #0x1F

DCache_Clean_Invalidate_Loop:
    MCR     p15, 0, r0, c7, c14, 1  // DCCIMVAC, clean and invalidate data cache line by MVA to PoC
    ADD     r0, r0, r2              // Increment address by cache line length
    CMP     r0, r1                  // Check for end of range
    BLT     DCache_Clean_Invalidate_Loop

    DSB // Ensure completion
    BX      lr




// ------------------------------------------------------------
// DCache clean by line
// ------------------------------------------------------------
ARM_DCache_Clean_Line:
    DSB                               // Complete all outstanding explicit memory operations
    MCR     p15, 0, r0, c7, c10, 1    // DCCMVAC, clean data cache line by MVA to PoC
    DSB                               // Ensure completion
    BX      lr

// ------------------------------------------------------------
// DCache invalidate by line
// ------------------------------------------------------------
ARM_DCache_Invalidate_Line:
    DSB                           //Complete all outstanding explicit memory operations
    MCR     p15, 0, r0, c7, c6, 1 // DCIMVAC, invalidate data cache line by MVA to PoC
    DSB // Ensure completion
    BX      lr

// ------------------------------------------------------------
// DCache clean and invalidate by line
// ------------------------------------------------------------
ARM_DCache_Clean_Invalidate_Line:
    DSB      						//Complete all outstanding explicit memory operations
    MCR     p15, 0, r0, c7, c14, 1 	// DCCIMVAC, clean and invalidate data cache line by MVA to PoC
    DSB 							// Ensure completion
    BX      lr
