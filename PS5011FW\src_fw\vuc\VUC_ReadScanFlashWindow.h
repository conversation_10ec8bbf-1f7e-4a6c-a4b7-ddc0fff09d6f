#ifndef _VUC_READSCANFLASHWINDOW_H_
#define _VUC_READSCANFLASHWINDOW_H_

#define FAE_LOG_MAX_CE				(8)
#define TWOS_COMLEMENT_BIT	(BIT16)
#define DLL_OFFSET_TWOS_COMLEMENT_BIT	(BIT9)
#define VUC_GET_FEATURE_FOUR_BYTE		(4)
#define DQ_MAX_GROUP_SIZE			(16)
#if (PS5021_EN)
#define VUC_PAD_PAGE_MARK				(0xAA5021AA)
#elif (PS5017_EN)
#define VUC_PAD_PAGE_MARK				(0xAA3117AA)
#endif /* PS5021_EN */
#define VUC_SCAN_PAGE_REG_MARK				(0xAA5245AA)

typedef struct ChannelWindowInfo {
	U16 uwSdllCombiedMin;
	U16 uwSdllCombiedMax;
	U16 uwMdllWindow;
	U16 uwDeltaWindow;
	U16 uwOverlapSDllWindowTime;
	U16 uwReserved1;
	U16 uwReserved2;
	U16 uwReserved3;
} ChannelWindowInfo_t;
typedef struct ReturnWindowInfo {
	ChannelWindowInfo_t ReadSdll;
	ChannelWindowInfo_t WriteSdll;
} ReturnWindowInfo_t;
#define M_VUC_READ_SCAN_FALSH_WINDOW_CALCULATE_PERIOD_TIME(RETURNVALUE,CLOCKRATE,MDLLVALUE,MAXVALUE,MINVALUE)		do{\
		(RETURNVALUE) = (1000000000 / ((CLOCKRATE) * 2) / (26 + (MDLLVALUE)) * ((MAXVALUE) - (MINVALUE))) / 1000;\
}while(0)

void VUC_ReadScanFlashWindow(VUC_OPT_HCMD_PTR_t pCmd);

#endif /* _VUC_READSCANFLASHWINDOW_H_ */
