#ifndef _NVME_API_H_
#define _NVME_API_H_

#if (PS5021_EN)
#include "nvme_reg_5021.h"
#else /* (PS5021_EN) */
#include "nvme_reg_5013.h"
#endif /* (PS5021_EN) */

#include "hal/nvme/nvme.h"


/*====================== END: NVME SETTING ===========================*/
#pragma	pack(push)
#pragma	pack(1)

typedef struct nvme_feature			NVMEFEAT_t, *PNVMEFEAT_t;
typedef struct nvme_log				NVMELOG_t, *PNVMELOG_t;

struct nvme_feature {
	U32 ulTMPTH;
	U32 ulTLER;
	U32 ulNQA;
	U32 ulDN;
	U32 ulAEC;
	U32 ulAPSTE;
	U32 ulPROGMARK;
};

typedef struct {
	U64 uoBadd;
	U32 ulBsize;
	U32 ulRev;
} nvme_HMB_Dec_t;

typedef struct {
	U32 ulHMDLEC;                         //number of entry count
	U32 ulHSize;                          //total host memory size
	nvme_HMB_Dec_t sHMB_Dec_arry[16];
} nvme_HMB_fmt_t;

struct nvme_log {
	U8 ubErrInfoCnt;
	U8 ubSmartCnt;
	U8 ubFWslotInfoCnt;
};


typedef struct {
	U16    uwMaximumPower;
	U8	   ubReserved0;
	U8	   btmps			:	1;
	U8	   btnops			:	1;
	U8	   reserved			:	6;
	U32    ulentryLatency;
	U32    ulexitLatency;
	U8     rrt				:	5;
	U8     reserved1		:	3;
	U8     rrl				:	5;
	U8     reserved2		:	3;
	U8     rwt				:	5;
	U8     reserved3		:	3;
	U8     rwl				:	5;
	U8     reserved4		:	3;
	U32    ulreserved5[4];
} NVME_POWER_STATE_DESCRIPTOR_T;

typedef struct {
	U16    uwvid;
	U16    uwssvid;
	U8     ubserialNumber[20];
	U8     ubmodelNumber[40];
	U8     ubfirmwareRevison[8];
	U8     ubrecommendArbitrationBurst;
	U8     ubieeeIdentifier[3];
	U8     ubmic;//multi interface capabilities
	U8     ubmdts;//maximum data transfer size
	U16	   uwcntlid;//Controller ID
	U32    ulVER;
	U32    ulRTD3R;
	U32    ulRTD3E;
	U32    ulOAES;
	U32    ulCTRATT;
	U8     ubreserved0[156];
	U16    uwoacs;//optional admin command support;
	U8     ubacl;//abort command limit
	U8     ubaerl;//asycnchronous event request
	U8     ubfrmw;//firmware updates
	U8     ublpa;//log page attribute
	U8     ubelpe;//error log page
	U8     ubnpss;//number of power state support
	U8	   ubavscc;//Admin Vendor Specific Command Configuration
	U8	   ubapsta;//Autonomous Power State Transition Attributes
	U16    uwWCTEMP;
	U16    uwCCTEMP;
	U16    uwMTFA;
	U32    ulHMPRE;
	U32    ulHMMIN;
	U8     ubTNVMCAP[16];
	U8     ubUNVMCAP[16];
	U32    ulRPMBS;
	U32    ulreserved1;
	U16    uwKAS;
	U8     ubreserved2[190];
	U8     ubsqes;//submission queue entry size
	U8     ubcqes;//completion queue entry size
	U16    uwMAXCMD;
	U32    ulnamespacesNumber;//name
	U16    uwoncs;//optional NVM command support
	U16    uwfuses;//fuse support
	U8     ubfna;//format nvm attributes
	U8     ubvwc;//volatile write cache
	U16    uwawun;//atomic write unit normal
	U16    uwawupf;//atomic write unit power fail
	U8     ubnvscc;
	U8     ubreserved3;
	U16	   uwacwu;//Atomic Compare & Write Unit
	U16	   uwreserved5;
	U32	   ulsgls;//SGL Support
	U8     ubreserved6[228];
	U8     ubSUBNQN[256];
	U8 	   ubreserved7[1024];
	NVME_POWER_STATE_DESCRIPTOR_T   psd[32];
	U8     ubvendorspecfic[1024];
} NVME_IDENTIFY_CONTROLLER_DATA_STRUCT_T;

typedef struct {
	U32	uwmetadataSize			:	16;
	U32	ublbaDataSize			:	8;
	U32	relativePerformance		:	2;
	U32	reserved				:	6;
} NVME_LBA_FORMAT_T;

typedef struct {
	U64    uonamespaceSize;//nsze								//bytes   0 - 7
	U64    uonamespaceCapacity;//ncap							//bytes   8 - 15
	U64    uonamespaceUtil;//nuse 								//bytes  16 - 23
	U8     ubnamespaceFeatures;//nsfeat							//bytes	 24
	U8     ubnlbaf;//number of LBA Formats						//bytes  25
	U8     ubformattedLbaSize;//flbas							//bytes  26
	U8     ubmetadataCapabilities;//mc							//bytes  27
	U8     ubdpc;//end to end data protection capabilities		//bytes	 28
	U8     ubdps;//end to end data protection settings			//bytes  29
	U8	   ubnmic;//ns multi I/O and ns sharing capabilities	//bytes  30
	U8	   ubrescap;//reservation capabilities					//bytes  31
	U8	   ubreserved0[88];										//bytes 32 - 119
	U64	   uoeui64;//ieee extended unique identifier			//bytes 120 - 127
	NVME_LBA_FORMAT_T    lbaFormatSupport[16];					//bytes 128 - 191
	U8 	   ubreserved1[192];									//bytes 192 - 383
	U8	   ubvendorspecific[3712];								//bytes 384 - 4095
} NVME_IDENTIFY_NAMESPACE_STRUCT_T;


typedef struct {
	//-------------------------------
	//	union {//B0
	U8		ubCriticalWarning;
	/*		struct {
				uint8_t 	AvailableSpareSpace : 1;
				uint8_t		Temperature : 1;
				uint8_t		Reliability : 1;
				uint8_t		ReadOnly : 1;
				uint8_t		VolatileMemoryFail : 1;
				uint8_t		reserved : 3;
			};
		};*/
	//-------------------------------

	U16		uwCompositeTemperature;//B2-B1
	U8		ubAvailSpare;//B3
	U8		ubAvailSpareThreshold;//B4
	U8		ubPercentageUsed;//B5
	U8		ubreserved1[26];//B31-B6
	U64		uoDataUnitRead[2];//B47-B32
	U64		uoDataUnitWrite[2];//B63-B48
	U64		uoHostRdCmds[2];//B79-B64
	U64		uoHostWTCmds[2];//B95-B80
	U64		uoCntlrBusyTime[2];//B111-B96
	U64		uoPowerCycle[2];//B127-B112
	U64		uoPowerOnHours[2];//B143-B128
	U64		uoUnsafeShutdowns[2];//B159-B144
	U64		uoMediaErr[2];//B175-B160
	U64		uoNumOfErrInfoLogEntries[2];//B191-B176
	U32		ulWarningCompositeTemperatureTimeMins;//B195-B192
	U32		ulCriticalCompositeTemperatureTimeMins;//B199-B196
	U16		uwTemperatureSensor1;//B201-B200
	U16		uwTemperatureSensor2;//B203-B202
	U16		uwTemperatureSensor3;//B205-B204
	U16		uwTemperatureSensor4;//B207-B206
	U16		uwTemperatureSensor5;//B209-B208
	U16		uwTemperatureSensor6;//B211-B210
	U16		uwTemperatureSensor7;//B213-B212
	U16		uwTemperatureSensor8;//B215-B214
	U8		ubreserved2[296];//B511-B216
} NVME_GETLOGPAGE_SMART_INFO_STRUCT_T;

enum nvme_fid {
	RESERVED,
	ARBITRATION,
	POWER_MGMT,
	LBA_RANGE,
	TMP_THR,
	ERR_RECOVERY,
	VOL_WRCACHE,
	NUM_QUEUE,
	INT_COALESCING,
	IV_CONFIG,
	WRITE_ATOM,
	AE_CONFIG,
	APST,
	HMB,
	TIMESTAMP,
	PROGRESS_MARK = 0x80,
	HOST_ID,
	RSV_NOTE_MASK,
	RSV_PERSIST,
	VENDOR_BCMD_TEST = 0xC0,
};

#pragma pack(pop)

void nvme_vuc_handler(void);

#endif /* _NVME_API_H_ */
