/*

*/
#ifndef _FTL_BACKGROUND_H_
#define _FTL_BACKGROUND_H_

#include "typedef.h"
#include "symbol.h"
#include "fw_vardef.h"


//==============================================================================
// Defined Values and Capabilities
//==============================================================================

//==============================================================================
// Types and Structures
//==============================================================================


//==============================================================================
// Macro Functions
//==============================================================================

//==============================================================================
// Global Variables
//==============================================================================

//==============================================================================
//	Functions
//==============================================================================
AOM_LPM void FTLBackGroundStop(void);
AOM_LPM void FTLBackGround(void);

#endif // _FTL_BACKGROUND_H_
