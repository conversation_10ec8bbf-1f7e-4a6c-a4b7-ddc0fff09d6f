#include "hal/dmac/dmac_api.h"
#include "hal/dmac/dmac_pop_cmd.h"
#include "hal/pic/uart/uart_api.h"
#include "nvme_api/nvme/shr_hal_nvme.h"
#include "vuc/VUC_command.h"

#if (BURNER_MODE_EN)
void VUC_ISPReadPRAM(VUC_OPT_HCMD_PTR_t pCmd)
{
	U32 ulPackByteCnt = pCmd->vuc_sqcmd.vendor.ISPReadPRAM.ulLength * BYTE_PER_DW;
	DMACParam_t DMACParam;
	M_UART(VUC_, "\nVUC_ISP_Read_PRAM");

	M_UART(VUC_, "\nByteCnt\t%d", ulPackByteCnt);

	DMACParam.ulSourceAddr = ATCM_RAM_ADDRESS;
	DMACParam.ulDestAddr = BURNER_VENDOR_BUF_BASE;
	DMACParam.ul32ByteNum = M_BYTE_TO_32BYTE_ALIGN(ulPackByteCnt);
	gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
	DMACCopy(DMAC_MODE_NORMAL_QUEUE, &DMACParam, gulDMACDirectWaitDone_Callback, 0);
	while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
		DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
	}
	//memcpy((void *)BURNER_VENDOR_BUF_BASE, (void *)ATCM_RAM_ADDRESS, ulPackByteCnt);

}
#endif /* BURNER_MODE_EN */
