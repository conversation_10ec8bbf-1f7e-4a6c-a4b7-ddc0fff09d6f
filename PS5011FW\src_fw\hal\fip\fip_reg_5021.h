#ifndef _E21_FIP_REG_H_
#define _E21_FIP_REG_H_

#include "mem.h"

typedef union {
	U16 uwAll;
	struct {
		U16 Length: 4;
		U16 TargetAddrStartBit : 6;
		U16 SrcAddrStartBit	: 6;
	} Rule;
} ALU_Rule_t;

//temporary place here
#define IRAM_3D_NAND_IDX_TBL_BASE              (CONV_IDX_OFF)
#define IRAM_3D_NAND_IDX_TBL_SIZE              (CONV_IDX_TOTAL_LEN)
#define IRAM_3D_NAND_SEED_TBL_BASE             (CONV_SEED_OFF)
#define IRAM_3D_NAND_SEED_TBS_SIZE             (CONV_SEED_TOTAL_LEN)

#if (IRAM_3D_NAND_IDX_TBL_BASE % DEF_512B)
#error "IRAM_3D_NAND_IDX_TBL_BASE need align 512B"
#endif /*(IRAM_3D_NAND_IDX_TBL_BASE % 0x200)*/

//=============================================================================
/* Register base setting and common define */
//=============================================================================
#define FTOP			(0x00000000) //offset Top Register
#define FCH0			(0x00000800) //offset CH0                                       // SVN.4544
#define FCH1			(0x00000A00) //offset CH1                                       // SVN.4544
#define FCH2			(0x00000C00) //offset CH2                                       // SVN.4544
#define FCH3			(0x00000E00) //offset CH3                                       // SVN.4544
#define FALL			(0x00001000) //offset All IP                                    // SVN.4544
#define FDL0			(0x00001200) //offset Digital PLL for MDLL of CH0 and CH1       // SVN.4544
#define FDL1			(0x00001400) //offset Digital PLL for MDLL of CH2 and CH3       // SVN.4544

#define FLH_CH_GAP			(0x200)    //CH Reg.  <-> CH Reg. DLL Reg. <-> DLL Reg.
#define FLH_REG_GAP			(0x400)    //TOP Reg. <-> CH Reg. CH Reg.  <-> DLL Reg.

#define R32_FLH_CH_GAP			(FLH_CH_GAP >> 2)

#define FLH_TOP_REG_BASE			(FLASH_REG_ADDRESS)         //0xF800D000
#define FLH_CTL_REG_BASE			(FLASH_REG_ADDRESS + FCH0)  //0xF800D800
#define FLH_ALL_REG_BASE			(FLASH_REG_ADDRESS + FALL)  //0xF800E000
#define FLH_DLL_REG_BASE			(FLASH_REG_ADDRESS + FDL0)	 //0xF800E200

#define FLH_DLL_GROUP_BASE(x)       (FLH_DLL_REG_BASE + (FLH_CH_GAP * (x)))

#define FLH_CH_BASE(x)                (FLH_CTL_REG_BASE + (FLH_CH_GAP * (x)))

#define R8_FCON				((REG8 *) 	(FLH_TOP_REG_BASE))
#define R16_FCON			((REG16 *)	(FLH_TOP_REG_BASE))
#define R32_FCON			((REG32 *)	(FLH_TOP_REG_BASE))
#define R64_FCON			((REG64 *)	(FLH_TOP_REG_BASE))

#define R8_FCTL_CH			((volatile U8   (*)[FLH_CH_GAP >> 0])FLH_CTL_REG_BASE)
#define R16_FCTL_CH			((volatile U16  (*)[FLH_CH_GAP >> 1])FLH_CTL_REG_BASE)
#define R32_FCTL_CH			((volatile U32  (*)[FLH_CH_GAP >> 2])FLH_CTL_REG_BASE)

#define R8_FALL				((REG8 *)	(FLH_ALL_REG_BASE))
#define R16_FALL			((REG16 *)	(FLH_ALL_REG_BASE))
#define R32_FALL			((REG32 *)	(FLH_ALL_REG_BASE))
#define R64_FALL			((REG64 *)	(FLH_ALL_REG_BASE))

#define M_FLH_DLL_BASE(x)			(FLH_DLL_REG_BASE + (FLH_CH_GAP * (x)))

#define R8_FDLL(x)			((REG8 *)	(FLH_DLL_GROUP_BASE(x)))
#define R16_FDLL(x)			((REG16 *)	(FLH_DLL_GROUP_BASE(x)))
#define R32_FDLL(x)			((REG32 *)	(FLH_DLL_GROUP_BASE(x)))
#define R64_FDLL(x)			((REG64 *)	(FLH_DLL_GROUP_BASE(x)))

#define DLL_SEL_VAL_0               (0x20 >> 2)    // CH0
#define DLL_SEL_VAL_1               (0x24 >> 2)    // CH1
#define DLL_SDLL_VAL_0              (0x40 >> 2)    // CH0
#define DLL_SDLL_VAL_1              (0x44 >> 2)    // CH1

enum {DDL1 = 0, DDL2};
enum {CH0 = 0, CH1 = 1,  CH2 = 2,  CH3 = 3, CH_ALL = 4};
enum {CE0 = 0, CE1,  CE2,  CE3,  CE4,  CE5,  CE6,  CE7,
	CE8,     CE9,  CE10, CE11, CE12, CE13, CE14, CE15,
	CE16,    CE17, CE18, CE19, CE20, CE21, CE22, CE23,
	CE24,    CE25, CE26, CE27, CE28, CE29, CE30, CE31
};

//=============================================================================
/* Register Offset Definitions of Top register */
//=============================================================================

#define R32_FCON_INFO					(0x00 >> 2)
#define		FLH_CH_SEL_SHIFT				(16)
#define		FLH_CH_SEL_MASK					BIT_MASK(8)
#define		FLH_CH_SEL_SHIFT_MASK			(FLH_CH_SEL_MASK << FLH_CH_SEL_SHIFT)
#define		SET_SKIP_4_CH					(0xF0 << FLH_CH_SEL_SHIFT)
#define		SET_SKIP_6_CH					(0xFC << FLH_CH_SEL_SHIFT)
#define		SET_SKIP_7_CH					(0xFE << FLH_CH_SEL_SHIFT)
#define		FALL_CH_SEL_SHIFT				(27)
#define		FALL_CH_SEL_MASK				BIT_MASK(2)

#define R32_FCON_MODE_SET					(0x04 >> 2)
#define		CPU_WAIT_MODE_BIT					BIT31
#define     COR_CTRL_PIN_TERMI_COEF_SHIFT		(24)
#define     COR_CTRL_PIN_TERMI_COEF_MASK		BIT_MASK(7)
#define		COR_CTRL_PIN_TERMI_EN_BIT			BIT23
#define		COR_CTRL_PIN_LENGTH_EN_BIT			BIT22
#define		COR_CTRL_PIN_CS_ACC_EN_BIT			BIT21
#define		COR_CTRL_PIN_ACC_EN_BIT				BIT20
#define		CTRL_COR_CLK_BIT					BIT18
#define		CTRL_ECC_CLK_BIT					BIT17
#define		CTRL_KEY_SAFE_MODE_BIT				BIT16
#define		DQS_OE_DRV_EN_BIT					BIT15
#define		MT_CE_VALUE_MASK_SHIFT				(12)
#define		MT_CE_VALUE_MASK_MASK				BIT_MASK(3)
#define		MT_CE_VALUE_MASK_SHIFT_MASK			(MT_CE_VALUE_MASK_MASK << MT_CE_VALUE_MASK_SHIFT)
#define		SET_MT_CE_VALUE_MASK(n)				(((n) << MT_CE_VALUE_MASK_SHIFT) & MT_CE_VALUE_MASK_SHIFT_MASK)
#define		MT_CE_VALUE_SHIFT_SHIFT				(9)
#define		MT_CE_VALUE_SHIFT_MASK				BIT_MASK(3)
#define		MT_CE_VALUE_SHIFT_SHIFT_MASK		(MT_CE_VALUE_SHIFT_MASK << MT_CE_VALUE_SHIFT_SHIFT)
#define		SET_MT_CE_VALUE_SHIFT(n)			(((n) << MT_CE_VALUE_SHIFT_SHIFT) & MT_CE_VALUE_SHIFT_SHIFT_MASK)
#define     PDLY_OPTION_0                       BIT8
#define		DQS_OE_NDSRT_BIT					BIT7
#define		COP0_MT_TRIG_ACK_CNT_SHIFT			(3)
#define		COP0_MT_TRIG_ACK_CNT_MASK			BIT_MASK(3)
#define		AP_CMP_MODE_BIT						BIT2
#define		CE_DECODE_EN_BIT					BIT1
#define		FCE_ENB_INV_BIT						BIT0

#define R8_FCON_FCE_ENB			(0x08 >> 0)
#define R32_FCON_FCE_ENB			(0x08 >> 2)
#define		FNCE_CTRL_SHIFT				(0)
#define		FNCE_CTRL_MASK				BIT_MASK(32)

#define R32_FCON_RGD_ADR			(0x10 >> 2)
#define		FCON_RGD_ADR_SHIFT			(0)
#define		FCON_RGD_ADR_MASK			BIT_MASK(32)

#define R32_FCON_RGD_CTL			(0x14 >> 2)
#define		FCON_RGD_SEL_SHIFT			(4)
#define		FCON_RGD_SEL_MASK			BIT_MASK(4)
#define		FCON_RGD_SEL_SHIFT_MASK	(FCON_RGD_SEL_MASK << FCON_RGD_SEL_SHIFT)
#define		CHANNEL_DUMP			(0x00)
#define		TOP_DUMP				(0x08)
#define		FCON_RGD_RD_WRJ_BIT			BIT1
#define		FCON_RGD_TRIG_BIT			BIT0

#define R32_FCON_SMP_CFG			(0x18 >> 2)
#define		MNT_ABT_SEL_BIT				BIT31
#define		CUR_SEMA_NUM_SHIFT			(8)
#define		CUR_SEMA_NUM_MASK			BIT_MASK(5)     // E17
#define		CUR_SEMA_NUM_SHIFT_MASK		(CUR_SEMA_NUM_MASK << CUR_SEMA_NUM_SHIFT)
#define		TOT_SEMA_NUM_SHIFT			(1)
#define		TOT_SEMA_NUM_MASK			BIT_MASK(5)     // E17
#define		TOT_SEMA_NUM_SHIFT_MASK		(TOT_SEMA_NUM_MASK << TOT_SEMA_NUM_SHIFT)
#define		TOT_SEMA_NUM_EN_BIT			BIT0

#define R32_FCON_FPU_DIE_CFG_1		(0x20 >> 2)
#define		FPU_D7_CFG_SHIFT			(24)
#define		FPU_D7_CFG_MASK				BIT_MASK(8)
#define		FPU_D6_CFG_SHIFT			(16)
#define		FPU_D6_CFG_MASK				BIT_MASK(8)
#define		FPU_D5_CFG_SHIFT			(8)
#define		FPU_D5_CFG_MASK				BIT_MASK(8)
#define		FPU_D4_CFG_SHIFT			(0)
#define		FPU_D4_CFG_MASK				BIT_MASK(8)

#define R32_FCON_FPU_DIE_CFG_0		(0x24 >> 2)
#define		FPU_D3_CFG_SHIFT			(24)
#define		FPU_D3_CFG_MASK				BIT_MASK(8)
#define		FPU_D2_CFG_SHIFT			(16)
#define		FPU_D2_CFG_MASK				BIT_MASK(8)
#define		FPU_D1_CFG_SHIFT			(8)
#define		FPU_D1_CFG_MASK				BIT_MASK(8)
#define		FPU_D0_CFG_SHIFT			(0)
#define		FPU_D0_CFG_MASK				BIT_MASK(8)

#define R32_FCON_IRC_CTL			(0x28 >> 2)
#define     IRAM_INI_ENHANCE_EN         BIT9
#define		IRAM_INIT_TRIG_BIT			BIT8
#define		SET_IRAM_INIT_TRIG			SET_BIT8
#define		CHK_IRAM_INIT_BUSY			SET_BIT8
#define		IRAM_CP_TRIG_BIT			BIT7
#define		IRAM_CP_LEN_SHIFT			(0)
#define		IRAM_CP_LEN_MASK			BIT_MASK(7)
#define		IRAM_CP_LEN_SHIFT_MASK		(IRAM_CP_LEN_MASK << IRAM_CP_LEN_SHIFT)
#define		M_SET_IRAM_CP_LEN(n)		(((n) << IRAM_CP_LEN_SHIFT) & IRAM_CP_LEN_SHIFT_MASK)

#define R32_FCON_IRC_ADR_0					(0x2C >> 2)
#define		IRAM_CP_DSTN_START_ADDR_SHIFT		(16)
#define		IRAM_CP_DSTN_START_ADDR_MASK		BIT_MASK(16)
#define		IRAM_CP_SRC_START_ADDR_SHIFT		(0)
#define		IRAM_CP_SRC_START_ADDR_MASK			BIT_MASK(16)

#define R32_FCON_CNT_ERR_SET		(0x30 >> 2)
#define		SET_ERR_CNT_SHIFT			(0)
#define		SET_ERR_CNT_MASK			BIT_MASK(4)

#define R32_FCON_CNT_ERR			(0x34 >> 2)
#define		ERR_CNT_STAT_SHIFT			(0)
#define		ERR_CNT_STAT_MASK			BIT_MASK(32)

#define R32_FCON_FLH_FUNC				(0x38 >> 2)
#define		MT_FLH_CHA_CLK_BIT				BIT31
#define		CLR_IN_FLAG_BIT					BIT30
#define		WAIT_CLK_STATIC_BIT				BIT27
#define		DQS_INPUT_EVT_MASK_PRE_STA_BIT	BIT21
#define		AXI_R_ENH_EN_BIT				BIT20
#define		AXI_RUN_ROBIN_EN_BIT			BIT19
#define		CE_DELAY_CNT_SHIFT				(13)
#define		CE_DELAY_CNT_MASK				BIT_MASK(4)
#define		CE_DELAY_CNT_SHIFT_MASK			(CE_DELAY_CNT_MASK << CE_DELAY_CNT_SHIFT)
#define		INSERT_IRAM_ERR_PARITY_BIT		BIT11
#define		FORCE_ERR_MAP_EN_BIT			BIT10
#define		SEED_TBL_SRC_IRAM_BIT			BIT9
#define		RAND_IP_SEL_0_CLRBIT			CLR_BIT8
#define		RAND_IP_SEL_1_BIT				BIT8
#define		IOR_EN_BIT						BIT7
#define		RAIDECC_NO_DRAM_MODE_BIT		BIT6
#define		LCA_SRC_SEL_BIT					BIT5
#define		CE_MT_TABLE_BIT					BIT4
#define		SET_CE_MT_TABLE                 SET_BIT4
#define		CE_BANK_REMAP_EN_BIT			BIT3
#define		CLR_CE_BANK_REMAP_DIS           CLR_BIT3

#define R32_FCON_MODE_SET_1             (0x3C >> 2)
#define     READ_END_KEEP_DQS_H_EN          BIT31
#define     WARMUP_MODE_SHIFT               (29)
#define     WARMUP_MODE_MASK                BIT_MASK(1)
#define     WARMUP_PAUSE_MODE               CLR_BIT29
#define     WARMUP_EXIT_MODE                BIT29
#define     IO_OE_DRV_EN                    BIT17
#define     WARMUP_CYCLE_READ_SHIFT         (13)
#define     WARMUP_CYCLE_READ_MASK          BIT_MASK(3)
#define     WARMUP_CYCLE_WRITE_SHIFT        (10)
#define     WARMUP_CYCLE_WRITE_MASK         BIT_MASK(3)
#define     PDLY_OPTION_1                   BIT9

#define R32_FCON_MNT_R_CFG				(0x40 >> 2)
#define		MDM_RT_VAL_SHIFT				(16)
#define		MDM_RT_VAL_MASK					BIT_MASK(16)
#define		MDM_RT_VAL_SHIFT_MASK			(MDM_RT_VAL_MASK << MDM_RT_VAL_SHIFT)
#define		MDM_READ_STEP_VAL_SHIFT			(1)
#define		MDM_READ_STEP_VAL_MASK			BIT_MASK(15)
#define		MDM_READ_STEP_VAL_SHIFT_MASK	(MDM_READ_STEP_VAL_MASK << MDM_READ_STEP_VAL_SHIFT)
#define		MT_R_DMA_MNT_EN_BIT				BIT0

#define R32_FCON_SGN_MSK				(0x44 >> 2)
#define		SIGNOFF_MASK_EN_BIT				BIT0

#define R32_FCON_MNT_R_CNT				(0x48 >> 2)

#define R32_FCON_RTY_CFG				(0x4C >> 2)
#define		TGL_ONF_DATA_PST_CNT_1_BIT			BIT30
#define		TGL_ONF_DATA_PRE_CNT_1_BIT			BIT29
#define		TGL_CMD_ADR_PST_CNT_1_SHIFT			(27)
#define		TGL_CMD_ADR_PST_CNT_1_MASK			BIT_MASK(2)
#define		TGL_CMD_ADR_PST_CNT_1_SHIFT_MASK	(TGL_CMD_ADR_PST_CNT_1_MASK << TGL_CMD_ADR_PST_CNT_1_SHIFT)
#define		TGL_CMD_ADR_WEN_CNT_1_SHIFT			(25)
#define		TGL_CMD_ADR_WEN_CNT_1_MASK			BIT_MASK(2)
#define		TGL_CMD_ADR_WEN_CNT_1_SHIFT_MASK	(TGL_CMD_ADR_WEN_CNT_1_MASK << TGL_CMD_ADR_WEN_CNT_1_SHIFT)
#define		TGL_CMD_ADR_PRE_CNT_1_BIT			BIT24
#define		TGL_ONF_DATA_PST_CNT_BIT			BIT22
#define		TGL_ONF_DATA_PRE_CNT_BIT			BIT21
#define		TGL_CMD_ADR_PST_CNT_SHIFT			(19)
#define		TGL_CMD_ADR_PST_CNT_MASK			BIT_MASK(2)
#define		TGL_CMD_ADR_PST_CNT_SHIFT_MASK		(TGL_CMD_ADR_PST_CNT_MASK << TGL_CMD_ADR_PST_CNT_SHIFT)
#define		TGL_CMD_ADR_WEN_CNT_SHIFT			(17)
#define		TGL_CMD_ADR_WEN_CNT_MASK			BIT_MASK(2)
#define		TGL_CMD_ADR_WEN_CNT_SHIFT_MASK		(TGL_CMD_ADR_WEN_CNT_MASK << TGL_CMD_ADR_WEN_CNT_SHIFT)
#define		TGL_CMD_ADR_PRE_CNT_BIT				BIT16
#define     TGL_WARMUP_POSTAMBLE_CNT_R_TIMEING_CFG_1    BIT14
#define     TGL_WARMUP_POSTAMBLE_CNT_W_TIMEING_CFG_1    BIT13
#define     TGL_WARMUP_POSTAMBLE_CNT_R_TIMEING_CFG_0    BIT6
#define     TGL_WARMUP_POSTAMBLE_CNT_W_TIMEING_CFG_0    BIT5

#define R32_FCON_SPECIAL				(0x50 >> 2)
#define		RRESP_CHK_IRAM_BIT				BIT31
#define		MT_QRY_FLAG_BIT					BIT29
#define		QRY_FLAG_CNT_SHIFT				(26)
#define		QRY_FLAG_CNT_MASK				BIT_MASK(3)
#define		QRY_FLAG_CNT_SHIFT_MASK			(QRY_FLAG_CNT_MASK << QRY_FLAG_CNT_SHIFT)
#define		ECC_THRESHOLD_SHIFT				(16)
#define		ECC_THRESHOLD_MASK				BIT_MASK(10)
#define		ECC_THRESHOLD_SHIFT_MASK		(ECC_THRESHOLD_MASK << ECC_THRESHOLD_SHIFT)
#define		M_SET_ECC_THRESHOLD_NUM(X)		(((X) & ECC_THRESHOLD_MASK) << ECC_THRESHOLD_SHIFT)
#define		PFA_FUNC_EN_BIT					BIT15
#define		SET_PFA_EN						SET_BIT15
#define		INT_INDEX_SEL_BIT				BIT14
#define		IBF_TOP_ERR_FORCE_BIT			BIT13
#define		IBF_TOP_ERR_EN_BIT				BIT12
#define		PAR_CHK_XRAM_BIT				BIT11
#define		PAR_CHK_IRAM_BIT				BIT10
#define		PAD_EN_BIT						BIT9
#define		MT_AUTO_POLL_RDY_BIT			BIT8
#define		CLR_USE_RDY_PIN                 CLR_BIT8
#define		MCU_WT_CNT_VAL_SHIFT			(0)
#define		MCU_WT_CNT_VAL_MASK				BIT_MASK(8)
#define		MCU_WT_CNT_VAL_SHIFT_MASK		(MCU_WT_CNT_VAL_MASK << MCU_WT_CNT_VAL_SHIFT)

#define R32_FCON_MNT_W_CFG				(0x58 >> 2)
#define		MDM_WRITE_TIME_VAL_SHIFT		(16)
#define		MDM_WRITE_TIME_VAL_MASK			BIT_MASK(16)
#define		MDM_WRITE_STEP_VAL_SHIFT		(1)
#define		MDM_WRITE_STEP_VAL_MASK			BIT_MASK(15)
#define		MT_W_DMA_MNT_EN_BIT				BIT0

#define R32_FCON_MNT_W_CNT				(0x5C >> 2)

#define R32_FCON_MNT_UPP_BOUND			(0X60 >> 2)

#define R32_FCON_LGC_TIME_CFG			(0x64 >> 2)
#define		H_LGC_ADDR_WEN_PHS_CNT_BIT		BIT31
#define		H_LGC_CMD_WEN_PHS_CNT_BIT		BIT30
#define		LGC_DATA_PST_CNT_SHIFT			(20)
#define		LGC_DATA_PST_CNT_MASK			BIT_MASK(4)
#define		LGC_DATA_PST_CNT_SHIFT_MASK		(LGC_DATA_PST_CNT_MASK << LGC_DATA_PST_CNT_SHIFT)
#define		LGC_DATA_PRE_CNT_SHIFT			(16)
#define		LGC_DATA_PRE_CNT_MASK			BIT_MASK(4)
#define		LGC_DATA_PRE_CNT_SHIFT_MASK		(LGC_DATA_PRE_CNT_MASK << LGC_DATA_PRE_CNT_SHIFT)
#define		LGC_ADR_PST_CNT_SHIFT			(13)
#define		LGC_ADR_PST_CNT_MASK			BIT_MASK(3)
#define		LGC_ADR_PST_CNT_SHIFT_MASK		(LGC_ADR_PST_CNT_MASK << LGC_ADR_PST_CNT_SHIFT)
#define		LGC_ADR_WEN_CNT_SHIFT			(11)
#define		LGC_ADR_WEN_CNT_MASK			BIT_MASK(2)
#define		LGC_ADR_WEN_CNT_SHIFT_MASK		(LGC_ADR_WEN_CNT_MASK << LGC_ADR_WEN_CNT_SHIFT)
#define		LGC_ADR_PRE_CNT_SHIFT			(8)
#define		LGC_ADR_PRE_CNT_MASK			BIT_MASK(3)
#define		LGC_ADR_PRE_CNT_SHIFT_MASK		(LGC_ADR_PRE_CNT_MASK << LGC_ADR_PRE_CNT_SHIFT)
#define		LGC_CMD_PST_CNT_SHIFT			(5)
#define		LGC_CMD_PST_CNT_MASK			BIT_MASK(3)
#define		LGC_CMD_PST_CNT_SHIFT_MASK		(LGC_CMD_PST_CNT_MASK << LGC_CMD_PST_CNT_SHIFT)
#define		LGC_CMD_WEN_CNT_SHIFT			(3)
#define		LGC_CMD_WEN_CNT_MASK			BIT_MASK(2)
#define		LGC_CMD_WEN_CNT_SHIFT_MASK		(LGC_CMD_WEN_CNT_MASK << LGC_CMD_WEN_CNT_SHIFT)
#define		LGC_CMD_PRE_CNT_SHIFT			(0)
#define		LGC_CMD_PRE_CNT_MASK			BIT_MASK(3)
#define		LGC_CMD_PRE_CNT_SHIFT_MASK		(LGC_CMD_PRE_CNT_MASK << LGC_CMD_PRE_CNT_SHIFT)

#define R32_FCON_TGL_TIME_CFG			(0x68 >> 2)
#define		TGL_DATA_CLH_CNT_SHIFT			(28)
#define		TGL_DATA_CLH_CNT_MASK			BIT_MASK(4)
#define		TGL_DATA_CLH_CNT_SHIFT_MASK		(TGL_DATA_CLH_CNT_MASK << TGL_DATA_CLH_CNT_SHIFT)
#define		TGL_DATA_PSTH_CNT_SHIFT			(24)
#define		TGL_DATA_PSTH_CNT_MASK			BIT_MASK(4)
#define		TGL_DATA_PSTH_CNT_SHIFT_MASK	(TGL_DATA_PSTH_CNT_MASK << TGL_DATA_PSTH_CNT_SHIFT)
#define		TGL_DATA_PST_CNT_SHIFT			(20)
#define		TGL_DATA_PST_CNT_MASK			BIT_MASK(4)
#define		TGL_DATA_PST_CNT_SHIFT_MASK		(TGL_DATA_PST_CNT_MASK << TGL_DATA_PST_CNT_SHIFT)
#define		TGL_DATA_PRE_CNT_SHIFT			(16)
#define		TGL_DATA_PRE_CNT_MASK			BIT_MASK(4)
#define		TGL_DATA_PRE_CNT_SHIFT_MASK		(TGL_DATA_PRE_CNT_MASK << TGL_DATA_PRE_CNT_SHIFT)
#define		TGL_ADR_PST_CNT_SHIFT			(12)
#define		TGL_ADR_PST_CNT_MASK			BIT_MASK(3)
#define		TGL_ADR_PST_CNT_SHIFT_MASK		(TGL_ADR_PST_CNT_MASK << TGL_ADR_PST_CNT_SHIFT)
#define		TGL_ADR_PRE_CNT_SHIFT			(8)
#define		TGL_ADR_PRE_CNT_MASK			BIT_MASK(4)
#define		TGL_ADR_PRE_CNT_SHIFT_MASK		(TGL_ADR_PRE_CNT_MASK << TGL_ADR_PRE_CNT_SHIFT)
#define		TGL_CMD_PST_CNT_SHIFT			(4)
#define		TGL_CMD_PST_CNT_MASK			BIT_MASK(3)
#define		TGL_CMD_PST_CNT_SHIFT_MASK		(TGL_CMD_PST_CNT_MASK << TGL_CMD_PST_CNT_SHIFT)
#define		TGL_CMD_PRE_CNT_SHIFT			(0)
#define		TGL_CMD_PRE_CNT_MASK			BIT_MASK(4)
#define		TGL_CMD_PRE_CNT_SHIFT_MASK		(TGL_CMD_PRE_CNT_MASK << TGL_CMD_PRE_CNT_SHIFT)

#define R32_FCON_TGL_TIME_CFG_5			(0x6C >> 2)
#define		TGL_WARM_UP_PST_HOLD_COUNT_READ_TIMING_CFG1_SHIFT 	     (21)
#define		TGL_WARM_UP_PST_HOLD_COUNT_READ_TIMING_CFG1_MASK	     BIT_MASK(5)
#define		TGL_WARM_UP_PST_HOLD_COUNT_READ_TIMING_CFG1_SHIFT_MASK  (TGL_WARM_UP_PST_HOLD_COUNT_READ_TIMING_CFG1_MASK << TGL_WARM_UP_PST_HOLD_COUNT_READ_TIMING_CFG1_SHIFT)
#define		TGL_WARM_UP_PST_HOLD_COUNT_READ_TIMING_CFG0_SHIFT 	     (16)
#define		TGL_WARM_UP_PST_HOLD_COUNT_READ_TIMING_CFG0_MASK	     BIT_MASK(5)
#define		TGL_WARM_UP_PST_HOLD_COUNT_READ_TIMING_CFG0_SHIFT_MASK  (TGL_WARM_UP_PST_HOLD_COUNT_READ_TIMING_CFG0_MASK << TGL_WARM_UP_PST_HOLD_COUNT_READ_TIMING_CFG0_SHIFT)
#define		TGL_WARM_UP_PST_HOLD_COUNT_WRITE_TIMING_CFG1_SHIFT 	     (5)
#define		TGL_WARM_UP_PST_HOLD_COUNT_WRITE_TIMING_CFG1_MASK	     BIT_MASK(5)
#define		TGL_WARM_UP_PST_HOLD_COUNT_WRITE_TIMING_CFG1_SHIFT_MASK  (TGL_WARM_UP_PST_HOLD_COUNT_WRITE_TIMING_CFG1_MASK << TGL_WARM_UP_PST_HOLD_COUNT_WRITE_TIMING_CFG1_SHIFT)
#define		TGL_WARM_UP_PST_HOLD_COUNT_WRITE_TIMING_CFG0_SHIFT 	     (0)
#define		TGL_WARM_UP_PST_HOLD_COUNT_WRITE_TIMING_CFG0_MASK	     BIT_MASK(5)
#define		TGL_WARM_UP_PST_HOLD_COUNT_WRITE_TIMING_CFG0_SHIFT_MASK  (TGL_WARM_UP_PST_HOLD_COUNT_WRITE_TIMING_CFG0_MASK << TGL_WARM_UP_PST_HOLD_COUNT_WRITE_TIMING_CFG0_SHIFT)

#define R32_FCON_LGC_TIME_CFG_1			(0x70 >> 2)
#define		H_LGC_ADDR_WEN_PHS_CNT_BIT		BIT31
#define		H_LGC_CMD_WEN_PHS_CNT_BIT		BIT30
#define		LGC_DATA_PST_CNT_SHIFT			(20)
#define		LGC_DATA_PST_CNT_MASK			BIT_MASK(4)
#define		LGC_DATA_PRE_CNT_SHIFT			(16)
#define		LGC_DATA_PRE_CNT_MASK			BIT_MASK(4)
#define		LGC_ADR_PST_CNT_SHIFT			(13)
#define		LGC_ADR_PST_CNT_MASK			BIT_MASK(3)
#define		LGC_ADR_WEN_PAHSE_CNT_SHIFT		(11)
#define		LGC_ADR_WEN_PAHSE_CNT_MASK		BIT_MASK(2)
#define		LGC_ADR_PRE_CNT_SHIFT			(8)
#define		LGC_ADR_PRE_CNT_MASK			BIT_MASK(3)
#define		LGC_CMD_PST_CNT_SHIFT			(5)
#define		LGC_CMD_PST_CNT_MASK			BIT_MASK(3)
#define		LGC_CMD_WEN_PHASE_CNT_SHIFT		(3)
#define		LGC_CMD_WEN_PHASE_CNT_MASK		BIT_MASK(2)
#define		LGC_CMD_PRE_CNT_SHIFT			(0)
#define		LGC_CMD_PRE_CNT_MASK			BIT_MASK(3)

#define R32_FCON_TGL_TIME_CFG_1			(0x74 >> 2)
#define		TGL_DATA_CLH_PHASE_CNT_SHIFT	(28)
#define		TGL_DATA_CLH_PHASE_CNT_MASK		BIT_MASK(4)
#define		TGL_DATA_PSTH_PHASE_CNT_SHIFT	(24)
#define		TGL_DATA_PSTH_PHASE_CNT_MASK	BIT_MASK(4)
#define		TGL_DATA_PST_PHASE_CNT_SHIFT	(20)
#define		TGL_DATA_PST_PHASE_CNT_MASK		BIT_MASK(4)
#define		TGL_DATA_PRE_PHASE_CNT_SHIFT	(16)
#define		TGL_DATA_PRE_PHASE_CNT_MASK		BIT_MASK(4)
#define		TGL_ADR_PST_PHASE_CNT_SHIFT		(12)
#define		TGL_ADR_PST_PHASE_CNT_MASK		BIT_MASK(3)
#define		TGL_ADR_PRE_PHASE_CNT_SHIFT		(8)
#define		TGL_ADR_PRE_PHASE_CNT_MASK		BIT_MASK(4)
#define		TGL_CMD_PST_PHASE_CNT_SHIFT		(4)
#define		TGL_CMD_PST_PHASE_CNT_MASK		BIT_MASK(3)
#define		TGL_CMD_PRE_PHASE_CNT_SHIFT		(0)
#define		TGL_CMD_PRE_PHASE_CNT_MASK		BIT_MASK(4)

#define R32_FCON_TGL_TIME_CFG_6			(0x78 >> 2)
#define		TGL_ADR_WEN_PHASE_CNT_SHIFT		(28)
#define		TGL_ADR_WEN_PHASE_CNT_MASK	    BIT_MASK(3)
#define		TGL_CMD_WEN_PHASE_CNT_SHIFT		(24)
#define		TGL_CMD_WEN_PHASE_CNT_MASK		BIT_MASK(3)
#define		TGL_ADR_WEN_PHASE_CNT1_SHIFT	(20)
#define		TGL_ADR_WEN_PHASE_CNT1_MASK	    BIT_MASK(3)
#define		TGL_CMD_WEN_PHASE_CNT1_SHIFT	(16)
#define		TGL_CMD_WEN_PHASE_CNT1_MASK		BIT_MASK(3)
#define		TGL_CMD_WPSTH_PHASE_CNT_SHIFT	(8)
#define		TGL_CMD_WPSTH_PHASE_CNT_MASK	BIT_MASK(5)
#define		TGL_CMD_WPSTH_PHASE_CNT1_SHIFT	(0)
#define		TGL_CMD_WPSTH_PHASE_CNT1_MASK	BIT_MASK(5)

#define R8_FCON_FCE_SEL_0				(0x84 >> 0)
#define R32_FCON_FCE_SEL_0				(0x84 >> 2)
#define		REMAP_CE_MASK					BIT_MASK(8)
#define		REMAP_CE_SHIFT(PhysicalCE)               ((PhysicalCE % 4) * 8)
#define		M_SET_CE_REMAP(PhysicalCE, RemapCE)      (((RemapCE) & REMAP_CE_MASK) << REMAP_CE_SHIFT(PhysicalCE))
#define		REMAP_CE3_SHIFT					(24)
#define		M_SET_CE3_REMAP(n)				(((n) & REMAP_CE_MASK) << REMAP_CE3_SHIFT)
#define		REMAP_CE2_SHIFT					(16)
#define		M_SET_CE2_REMAP(n)				(((n) & REMAP_CE_MASK) << REMAP_CE2_SHIFT)
#define		REMAP_CE1_SHIFT					(8)
#define		M_SET_CE1_REMAP(n)				(((n) & REMAP_CE_MASK) << REMAP_CE1_SHIFT)
#define		REMAP_CE0_SHIFT					(0)
#define		M_SET_CE0_REMAP(n)              (((n) & REMAP_CE_MASK) << REMAP_CE0_SHIFT)

#define R32_FCON_FCE_SEL_1				(0x88 >> 2)
#define		REMAP_CE7_SHIFT					(24)
#define		M_SET_CE7_REMAP(n)				(((n) & REMAP_CE_MASK) << REMAP_CE7_SHIFT)
#define		REMAP_CE6_SHIFT					(16)
#define		M_SET_CE6_REMAP(n)				(((n) & REMAP_CE_MASK) << REMAP_CE6_SHIFT)
#define		REMAP_CE5_SHIFT					(8)
#define		M_SET_CE5_REMAP(n)				(((n) & REMAP_CE_MASK) << REMAP_CE5_SHIFT)
#define		REMAP_CE4_SHIFT					(0)
#define		M_SET_CE4_REMAP(n)				(((n) & REMAP_CE_MASK) << REMAP_CE4_SHIFT)

#define R32_FCON_FCE_SEL_2				(0x8C >> 2)
#define		REMAP_CE11_SHIFT				(24)
#define		M_SET_CE11_REMAP(n)				(((n) & REMAP_CE_MASK) << REMAP_CE11_SHIFT)
#define		REMAP_CE10_SHIFT				(16)
#define		M_SET_CE10_REMAP(n)				(((n) & REMAP_CE_MASK) << REMAP_CE10_SHIFT)
#define		REMAP_CE9_SHIFT					(8)
#define		M_SET_CE9_REMAP(n)				(((n) & REMAP_CE_MASK) << REMAP_CE9_SHIFT)
#define		REMAP_CE8_SHIFT					(0)
#define		M_SET_CE8_REMAP(n)				(((n) & REMAP_CE_MASK) << REMAP_CE8_SHIFT)

#define R32_FCON_FCE_SEL_3				(0x90 >> 2)
#define		REMAP_CE15_SHIFT				(24)
#define		M_SET_CE15_REMAP(n)				(((n) & REMAP_CE_MASK) << REMAP_CE15_SHIFT)
#define		REMAP_CE14_SHIFT				(16)
#define		M_SET_CE14_REMAP(n)				(((n) & REMAP_CE_MASK) << REMAP_CE14_SHIFT)
#define		REMAP_CE13_SHIFT				(8)
#define		M_SET_CE13_REMAP(n)				(((n) & REMAP_CE_MASK) << REMAP_CE13_SHIFT)
#define		REMAP_CE12_SHIFT				(0)
#define		M_SET_CE12_REMAP(n)				(((n) & REMAP_CE_MASK) << REMAP_CE12_SHIFT)

#define R32_FCON_FCE_SEL_4				(0x94 >> 2)
#define		REMAP_CE19_SHIFT				(24)
#define		M_SET_CE19_REMAP(n)				(((n) & REMAP_CE_MASK) << REMAP_CE19_SHIFT)
#define		REMAP_CE18_SHIFT				(16)
#define		M_SET_CE18_REMAP(n)				(((n) & REMAP_CE_MASK) << REMAP_CE18_SHIFT)
#define		REMAP_CE17_SHIFT				(8)
#define		M_SET_CE17_REMAP(n)				(((n) & REMAP_CE_MASK) << REMAP_CE17_SHIFT)
#define		REMAP_CE16_SHIFT				(0)
#define		M_SET_CE16_REMAP(n)				(((n) & REMAP_CE_MASK) << REMAP_CE16_SHIFT)

#define R32_FCON_FCE_SEL_5				(0x98 >> 2)
#define		REMAP_CE23_SHIFT				(24)
#define		M_SET_CE23_REMAP(n)				(((n) & REMAP_CE_MASK) << REMAP_CE23_SHIFT)
#define		REMAP_CE22_SHIFT				(16)
#define		M_SET_CE22_REMAP(n)				(((n) & REMAP_CE_MASK) << REMAP_CE22_SHIFT)
#define		REMAP_CE21_SHIFT				(8)
#define		M_SET_CE21_REMAP(n)				(((n) & REMAP_CE_MASK) << REMAP_CE21_SHIFT)
#define		REMAP_CE20_SHIFT				(0)
#define		M_SET_CE20_REMAP(n)				(((n) & REMAP_CE_MASK) << REMAP_CE20_SHIFT)

#define R32_FCON_FCE_SEL_6				(0x9C >> 2)
#define		REMAP_CE27_SHIFT				(24)
#define		M_SET_CE27_REMAP(n)				(((n) & REMAP_CE_MASK) << REMAP_CE27_SHIFT)
#define		REMAP_CE26_SHIFT				(16)
#define		M_SET_CE26_REMAP(n)				(((n) & REMAP_CE_MASK) << REMAP_CE26_SHIFT)
#define		REMAP_CE25_SHIFT				(8)
#define		M_SET_CE25_REMAP(n)				(((n) & REMAP_CE_MASK) << REMAP_CE25_SHIFT)
#define		REMAP_CE24_SHIFT				(0)
#define		M_SET_CE24_REMAP(n)				(((n) & REMAP_CE_MASK) << REMAP_CE24_SHIFT)

#define R32_FCON_FCE_SEL_7				(0xA0 >> 2)
#define		REMAP_CE31_SHIFT				(24)
#define		M_SET_CE31_REMAP(n)				(((n) & REMAP_CE_MASK) << REMAP_CE31_SHIFT)
#define		REMAP_CE30_SHIFT				(16)
#define		M_SET_CE30_REMAP(n)				(((n) & REMAP_CE_MASK) << REMAP_CE30_SHIFT)
#define		REMAP_CE29_SHIFT				(8)
#define		M_SET_CE29_REMAP(n)				(((n) & REMAP_CE_MASK) << REMAP_CE29_SHIFT)
#define		REMAP_CE28_SHIFT				(0)
#define		M_SET_CE28_REMAP(n)				(((n) & REMAP_CE_MASK) << REMAP_CE28_SHIFT)

#define	R8_FCON_GEN_CONV_IDX1			(0xC4)
#define R32_FCON_GEN_CONV_IDX1			(0xC4 >> 2)
#define		CONV_REMAP_OFST_INIT_SHIFT		(20)
#define		CONV_REMAP_OFST_INIT_MASK		BIT_MASK(12)
#define		CONV_REMAP_OFST_INIT_SHIFT_MASK	(CONV_REMAP_OFST_INIT_MASK << CONV_REMAP_OFST_INIT_SHIFT)
#define		CONV_GENIDX_TBL_BAS_SHIFT		(12)
#define		CONV_GENIDX_TBL_BAS_MASK		BIT_MASK(8)
#define		CONV_GENIDX_TBL_BAS_SHIFT_MASK	(CONV_GENIDX_TBL_BAS_MASK << CONV_GENIDX_TBL_BAS_SHIFT)

#define	R8_FCON_GEN_CONV_IDX2					(0xC8)
#define R32_FCON_GEN_CONV_IDX2					(0xC8 >> 2)
#define		CONV_CYCLE_DIFF_INIT_SHIFT				(12)
#define		CONV_CYCLE_DIFF_INIT_MASK				BIT_MASK(12)
#define		CONV_CYCLE_DIFF_INIT_SHIFT_MASK			(CONV_CYCLE_DIFF_INIT_MASK << CONV_CYCLE_DIFF_INIT_SHIFT)
#define		CONV_REMAP_OFST_DIFF_INIT_SHIFT			(0)
#define		CONV_REMAP_OFST_DIFF_INIT_MASK			BIT_MASK(12)
#define		CONV_REMAP_OFST_DIFF_INIT_SHIFT_MASK	(CONV_REMAP_OFST_DIFF_INIT_MASK << CONV_REMAP_OFST_DIFF_INIT_SHIFT)

#define	R8_FCON_GEN_CONV_IDX3			(0xCC)
#define R32_FCON_GEN_CONV_IDX3			(0xCC >> 2)
#define		CONV_GENIDX_TRIG_BIT			BIT31
#define		CONV_GENIDX_BUSY_BIT			BIT30
#define		CONV_PG_FRM_NUM_SHIFT			(24)
#define		CONV_PG_FRM_NUM_MASK			BIT_MASK(6)
#define		CONV_PG_FRM_NUM_SHIFT_MASK		(CONV_PG_FRM_NUM_MASK << CONV_PG_FRM_NUM_SHIFT)
#define		CONV_D2_SHIFT					(12)
#define		CONV_D2_MASK					BIT_MASK(12)
#define		CONV_D2_SHIFT_MASK				(CONV_D2_MASK << CONV_D2_SHIFT)
#define		CONV_CYCLE_INIT_SHIFT			(0)
#define		CONV_CYCLE_INIT_MASK			BIT_MASK(12)
#define		CONV_CYCLE_INIT_SHIFT_MASK		(CONV_CYCLE_INIT_MASK << CONV_CYCLE_INIT_SHIFT)

#define R32_FCON_CRC_EN				(0xD0 >> 2)
#define		CRC16_EN_BIT				BIT1
#define		CRC32_EN_BIT				BIT0
#define		SET_CRC32_EN				SET_BIT0
#define		CLR_CRC32_DIS				CLR_BIT0

#define R32_FCON_ROW_PLANE_MASK		(0XD4 >> 2)

#define R32_FCON_TMR_CTL			(0xD8 >> 2)
#define		TIMER_CPU_INT_EN_BIT		BIT25
#define		TIMER_INT_EN_BIT			BIT24
#define		TIMER_CNT_LEN_SHIFT			(0)
#define		TIMER_CNT_LEN_MASK			BIT_MASK(24)

#define R32_FCON_ALU_BAS			(0xE0 >> 2)

#define R32_FCON_SEED_TABLE_BASE		(0xE4 >> 2)
#define		CHK_SEED_TABLE_BASE_SHIFT		(0)
#define		CHK_SEED_TABLE_BASE_MASK		BIT_MASK(16)
#define		CHK_SEED_TABLE_BASE_SHIFT_MASK	(CHK_SEED_TABLE_BASE_MASK << CHK_SEED_TABLE_BASE_SHIFT)

#define R32_FCON_MT_STS                     (0xE8 >> 2)
#define		M_FCON_MTQ_IS_FULL(CH, BANK)			(R32_FCON[R32_FCON_MT_STS] &  BIT(((BANK) * 4) + (CH)))

#define R32_FCON_MT_CFG				(0xEC >> 2)
#define		MTP_GRO_PRI_DEF_BIT			BIT31
#define		RAIDECC_PROG_PARITY_BIT		BIT30
#define		MT_HRD_LCK_BIT				BIT29
#define		QUE_IDX_SHIFT				(24)
#define		QUE_IDX_MASK				BIT_MASK(5)
#define		QOS_BIT						BIT23
#define		RAIDECC_OTFENC_EN_BIT		BIT22
#define		MT_SFT_LCK_BIT				BIT21
#define		FRC_EMP_BIT					BIT20
#define		FCON_RAIDECC_GRP_NUM_SHIFT	(17)
#define		FCON_RAIDECC_GRP_NUM_MASK	BIT_MASK(3)
#define		RAIDECC_TAG_LB_OFS_SHIFT	(9)
#define		RAIDECC_TAG_LB_OFS_MASK		BIT_MASK(8)
#define		LB_OFS_BIT					BIT8
#define		MTP_SMP_REQ_BIT				BIT7
#define		MT_IDX_SHIFT				(0)
#define		MT_IDX_MASK					BIT_MASK(7)
#define     HAL_FLH_MTP_IDX(IDX)                    ((U32) (IDX))
#define     HAL_FLH_MTQ_IDX(Q)                      (((U32)(Q)) << 24)

#define R32_FCON_ADR_GEN			(0xF0 >> 2)
#define     PL_ADR_LOC_SHIFT                (26)
#define     PL_ADR_LOC_MASK                 BIT_MASK(6)
#define		NON_SLC_SEL_BIT					BIT25
#define		IALU_EN_BIT						BIT24
#define		SET_IALU_EN                     SET_BIT24   /* ALU is from register */
#define		CLR_IALU_EN                     CLR_BIT24   /* ALU is from IRAM */
#define		TLC_PTR_EN_0_BIT				BIT23
#define		TLC_PTR_LOC_SHIFT				(17)
#define		TLC_PTR_LOC_MASK				BIT_MASK(6)
#define		TLC_PTR_LOC_SHIFT_MASK			(TLC_PTR_LOC_MASK << TLC_PTR_LOC_SHIFT)
#define		FRA_PTR_CATCH_MSK_SHIFT			(12)
#define		FRA_PTR_CATCH_MSK_MASK			BIT_MASK(5)
#define		SET_FRM_PTR_CATCH_MASK			BIT_MASK(4)
#define		SET_FRM_PTR_CATCH_SHIFT_MASK	(SET_FRM_PTR_CATCH_MASK << FRA_PTR_CATCH_MSK_SHIFT)
#define		SET_FRM_MASK_16K				BIT_MASK(2)
#define		SET_FRM_MASK_SHIFT_16K			(SET_FRM_MASK_16K << FRA_PTR_CATCH_MSK_SHIFT)
#define		SET_FRM_MASK_8K					BIT_MASK(1)
#define		SET_FRM_MASK_SHIFT_8K			(SET_FRM_MASK_8K << FRA_PTR_CATCH_MSK_SHIFT)
#define		SET_FRM_MASK_4K					BIT_MASK(0)
#define		SET_FRM_MASK_SHIFT_4K			(SET_FRM_MASK_4K << FRA_PTR_CATCH_MSK_SHIFT)
#define		FSA_CATCH_MD_BIT				BIT11
#define		FTA_ADDR_GEN_BIT				BIT10
#define		CLM_ADDR_AUTO_GEN_BIT			BIT9
#define		SET_CLM_ADDR_AUTO_GEN           SET_BIT9
#define		FRA_PTR_UPD_EN_BIT				BIT8
#define		SET_FRA_PTR_UPD_EN				SET_BIT8
#define		NEXT_FRA_PTR_UPD_EN_BIT			BIT7
#define		SET_NEXT_FRA_PTR_UPD_EN			SET_BIT7
#define		STORE_FTA_EN_BIT				BIT6
#define		FRM_PTR_CLM_ADDR_GEN_SHIFT		(0)
#define		FRM_PTR_CLM_ADDR_GEN_MASK		BIT_MASK(6)
#define		FRM_PTR_CLM_ADDR_GEN_SHIFT_MASK	(FRM_PTR_CLM_ADDR_GEN_MASK << FRM_PTR_CLM_ADDR_GEN_SHIFT)
#define		FLH_FSA_SHIFT_BITS				(0)

#define R32_FCON_ADR_GEN_STOP			(0xF4 >> 2)
#define		FTA_1T_EN	                    BIT31
#define		TLC_PTR_EN_2_BIT				BIT30
#define		TLC_PTR_LOC_2_SHIFT				(24)
#define		TLC_PTR_LOC_2_MASK				BIT_MASK(6)
#define		FTA_PIP_EN      				BIT23
#define		TLC_PTR_EN_1_BIT				BIT22
#define		TLC_PTR_LOC_1_SHIFT				(16)
#define		TLC_PTR_LOC_1_MASK				BIT_MASK(6)
#define		FLH_ADDR_ALU_STOP_CHK_EN_BIT	BIT15
#define		FLH_ADDR_ALU_STOP_CHK_DATA		BIT14
#define		ALU_STOP_CMP_ADDR_SHIFT			(8)
#define		ALU_STOP_CMP_ADDR_MASK			BIT_MASK(6)
#define		ALU_STOP_CMP_ADDR_SHIFT_MASK	(ALU_STOP_CMP_ADDR_MASK << ALU_STOP_CMP_ADDR_SHIFT)
#define		M_ALU_STOP_CMP_ADDR(x)			(x << ALU_STOP_CMP_ADDR_SHIFT)
#define     PL_BIT_LEN_SHIFT                (3)
#define     PL_BIT_LEN_MASK                 BIT_MASK(2)
#define		ALU_STOP_PTR_SHIFT				(0)
#define		ALU_STOP_PTR_MASK				BIT_MASK(3)
#define		ALU_STOP_PTR_SHIFT_MASK			(ALU_STOP_PTR_MASK << ALU_STOP_PTR_SHIFT)

#define R32_FCON_FCE_SET				(0xF8 >> 2)
#define		W_NUM_SET_CE_SHIFT				(0)
#define		W_NUM_SET_CE_MASK				BIT_MASK(6)

#define R32_FCON_FCE_CLR				(0xFC >> 2)
#define		W_NUM_CLR_CE_SHIFT				(0)
#define		W_NUM_CLR_CE_MASK				BIT_MASK(6)

#define R8_FCON_GEN_CONV_IDX4			(0x100)
#define R32_FCON_GEN_CONV_IDX4			(0x100 >> 2)
#define		CONV_RANDTBL_DEPTH_SHIFT		(16)
#define		CONV_RANDTBL_DEPTH_MASK			BIT_MASK(10)
#define		CONV_RANDTBL_DEPTH_SHIFT_MASK	(CONV_RANDTBL_DEPTH_MASK << CONV_RANDTBL_DEPTH_SHIFT)
#define		RND_PG_ADR_REGION_SHIFT			(12)
#define		RND_PG_ADR_REGION_MASK			BIT_MASK(3)
#define		RND_PG_ADR_REGION_SHIFT_MASK	(RND_PG_ADR_REGION_MASK << RND_PG_ADR_REGION_SHIFT)
#define		RND_PG_ADR_9_BITS				(0x00)
#define		RND_PG_ADR_8_BITS				(0x01)
#define		RND_PG_ADR_7_BITS				(0x02)
#define		RND_PG_ADR_6_BITS				(0x03)
#define		RND_PG_ADR_10_BITS				(0x04)
#define		RND_PG_ADR_11_BITS				(0x05)
#define		RND_PG_ADR_12_BITS				(0x06)
#define		RND_PG_ADR_13_BITS				(0x07)
#define		SET_RND_PG_ADR_9_BITS           (RND_PG_ADR_9_BITS << RND_PG_ADR_REGION_SHIFT)
#define		SET_RND_PG_ADR_8_BITS           (RND_PG_ADR_8_BITS << RND_PG_ADR_REGION_SHIFT)
#define		SET_RND_PG_ADR_7_BITS           (RND_PG_ADR_7_BITS << RND_PG_ADR_REGION_SHIFT)
#define		SET_RND_PG_ADR_6_BITS           (RND_PG_ADR_6_BITS << RND_PG_ADR_REGION_SHIFT)
#define		SET_RND_PG_ADR_10_BITS          (RND_PG_ADR_10_BITS << RND_PG_ADR_REGION_SHIFT)
#define		SET_RND_PG_ADR_11_BITS          (RND_PG_ADR_11_BITS << RND_PG_ADR_REGION_SHIFT)
#define		SET_RND_PG_ADR_12_BITS          (RND_PG_ADR_12_BITS << RND_PG_ADR_REGION_SHIFT)
#define		SET_RND_PG_ADR_13_BITS          (RND_PG_ADR_13_BITS << RND_PG_ADR_REGION_SHIFT)
#define		CONV_D1_SHIFT					(0)
#define		CONV_D1_MASK					BIT_MASK(12)
#define		CONV_D1_SHIFT_MASK				(CONV_D1_MASK << CONV_D1_SHIFT)

#define R32_FCON_IRAM_PERR				(0x104 >> 2)
#define		CH7_AUTO_POL_IPERR_BIT			BIT23
#define		CH6_AUTO_POL_IPERR_BIT			BIT22
#define		CH5_AUTO_POL_IPERR_BIT			BIT21
#define		CH4_AUTO_POL_IPERR_BIT			BIT20
#define		CH3_AUTO_POL_IPERR_BIT			BIT19
#define		CH2_AUTO_POL_IPERR_BIT			BIT18
#define		CH1_AUTO_POL_IPERR_BIT			BIT17
#define		CH0_AUTO_POL_IPERR_BIT			BIT16
#define		CH7_MT_IPERR_BIT				BIT15
#define		CH6_MT_IPERR_BIT				BIT14
#define		CH5_MT_IPERR_BIT				BIT13
#define		CH4_MT_IPERR_BIT				BIT12
#define		CH3_MT_IPERR_BIT				BIT11
#define		CH2_MT_IPERR_BIT				BIT10
#define		CH1_MT_IPERR_BIT				BIT9
#define		CH0_MT_IPERR_BIT				BIT8
#define		IPERR_MAP_EN_BIT				BIT7
#define		IPERR_CPU_INT_EN_BIT			BIT6
#define		AXIS_IPERR_BIT					BIT4
#define		BVCI_IPERR_BIT					BIT3
#define		IRC_IPERR_BIT					BIT2
#define		IRAM_PARITY_CHECK_EN_BIT		BIT0

#define R32_FCON_FW_RESET				(0x10C >> 2)
#define R8_FCON_FW_RESET_FLH_CLK		(0x10C)
#define R8_FCON_FW_RESET_ECC_CLK		(0x10D)
#define R8_FCON_FW_RESET_SYS_CLK		(0x10E)
#define R8_FCON_FW_RESET_SYS_DMA		(0x10F)
#define		SYS_DMA_RESET_BIT				BIT31
#define		RAIDECC_RESET					BIT30
#define		SYS_CLK_DM_RST_SHIFT			(16)
#define		SYS_CLK_DM_RST_MASK				BIT_MASK(8)
#define		ECC_CLK_DM_RST_SHIFT			(8)
#define		ECC_CLK_DM_RST_MASK				BIT_MASK(8)
#define		FLH_CLK_DM_RST_SHIFT			(0)
#define		FLH_CLK_DM_RST_MASK				BIT_MASK(8)
#define		IP_FUNC_ALL_DIS				    (0x00)
#define		IP_FUNC_RESET					(0xFF)
#define		IP_FUNC_ALL_RESET				(0xFFFFFFFF)


#define R32_FCON_LDPC_CFG				(0x110 >> 2)    // use generic definition for SECC/LDPC
#define R8_FCON_LDPC_CFG				(0x110)
#define		LDPC_COR_CLR_BIT				BIT31
#define		LDPC_MEM_LS_EN_BIT				BIT30
#define		LDPC_PARITY_ERR_DIS_BIT			BIT29
#define		DSP_LLR_UPD_BIT					BIT28
#define		DSP_LUT_SEL_SHIFT				(26)
#define		DSP_LUT_SEL_MASK				BIT_MASK(2)
#define		DSP_LUT_SEL_SHIFT_MASK			(DSP_LUT_SEL_MASK << DSP_LUT_SEL_SHIFT)
#define		LUT_GROUP0_EN					(0x00)
#define		LUT_GROUP1_EN					(0x01)
#define		LUT_GROUP2_EN					(0x02)
#define 	LUT_GROUP3_EN					(0x03)
#define		WAIT_NHB_FAST_MODE_BIT			BIT25
#define		WAIT_NHB_BIT					BIT24
#define		FLIP_BPS_BIT					BIT23
#define		FLIP_MOD_SHIFT					(21)
#define		FLIP_MOD_MASK					BIT_MASK(2)
#define		FLIP_MOD_SHIFT_MASK				(FLIP_MOD_MASK << FLIP_MOD_SHIFT)
#define		LDPC_A_ERR_BIT					BIT15
#define		LDPC_F_ERR_BIT					BIT14
#define		LDPC_O_ERR_BIT					BIT13
#define		LDPC_I_ERR_BIT					BIT12
#define		FLOW_MODE_SHIFT					(8)
#define		FLOW_MODE_MASK					BIT_MASK(2)
#define		PAGE_SELECT_SHIFT				(6)
#define		PAGE_SELECT_MASK				BIT_MASK(2)
#define		PAGE_SELECT_SHIFT_MASK			(PAGE_SELECT_MASK << PAGE_SELECT_SHIFT)
#define		DSP_EN_BIT						BIT5
#define		ET_ENABLE_BIT					BIT4
#define		LDPC_MODE_SHIFT					(0)
#define		LDPC_MODE_MASK					BIT_MASK(4)
#define		CHK_ECC_MODE_MASK				BIT_MASK(3)
#define		SET_ECC_MODE_0					(0)				//2048 bits = 256 Bytes
#define		SET_ECC_MODE_1					(1)				//1920 bits = 240 Bytes
#define		SET_ECC_MODE_2					(2)				//1792 bits = 224 Bytes
#define		SET_ECC_MODE_3					(3)				//1664 bits = 208 Bytes
#define		SET_ECC_MODE_4					(4)				//1408 bits = 176 Bytes
#define		SET_ECC_MODE_5					(5)				//1152 bits = 144 Bytes
#define		SET_ECC_MODE_6					(6)				//1024 bits = 128 Bytes
#define		SET_ECC_MODE_7					(7)				//2432 bits = 304 Bytes
#define		SET_ECC_MODE_8					(8)				//2304 bits = 288 Bytes

#define R32_FCON_ECC_BFC_SET		(0x114 >> 2)
#define     M_GET_LDPC_MS_BSY(X)        (X & BIT31)
#define     M_GET_LDPC_BF_BSY_1(X)      (X & BIT30)
#define     M_GET_LDPC_BF_BSY_0(X)      (X & BIT29)
#define		OVER_BFC_CNT_THR_SHIFT		(4)
#define		OVER_BFC_CNT_THR_MASK		BIT_MASK(15)
#define		OVER_BFC_CNT_THR_SHIFT_MASK	(OVER_BFC_CNT_THR_MASK << OVER_BFC_CNT_THR_SHIFT)
#define		CNT_BFC_SEL_SHIFT			(0)
#define		CNT_BFC_SEL_MASK			BIT_MASK(4)
#define		CNT_BFC_SEL_SHIFT_MASK		(CNT_BFC_SEL_MASK << CNT_BFC_SEL_SHIFT)
#define		CNT_BFC_0					(0x00)
#define		CNT_BFC_1_15				(0x01)
#define		CNT_BFC_16_31				(0x02)
#define		CNT_BFC_32_47				(0x03)
#define		CNT_BFC_48_63				(0x04)
#define		CNT_BFC_64_79				(0x05)
#define		CNT_BFC_80_95				(0x06)
#define		CNT_BFC_96_111				(0x07)
#define		CNT_BFC_112_120				(0x08)
#define		CNT_BFC_UNC					(0x09)

#define R32_FCON_ECC_BFC			(0x118 >> 2)

#define R8_FCON_PAGE_CFG			(0x11C)
#define R32_FCON_PAGE_CFG			(0x11C >> 2)
#define		FRM_1_DMY_LEN_SHIFT			 (20)
#define		FRM_1_DMY_LEN_MASK			 BIT_MASK(11)
#define		FRM_1_DMY_LEN_SHIFT_MASK	 (FRM_1_DMY_LEN_MASK << FRM_1_DMY_LEN_SHIFT)
#define		FRM_0_DMY_LEN_SHIFT			 (8)
#define		FRM_0_DMY_LEN_MASK			 BIT_MASK(11)
#define		FRM_0_DMY_LEN_SHIFT_MASK	 (FRM_0_DMY_LEN_MASK << FRM_0_DMY_LEN_SHIFT)
#define		DMY_TO_IBF_BIT				 BIT_MASK(6)
#define     DATA_LEN_AND_PAGE_SIZE_MASK	 BIT_MASK(5)
#define		DATA_LEN_SHIFT				 (2)
#define		DATA_LEN_MASK				 BIT_MASK(3)
#define		M_SET_DATA_LEN_KB(X)		 ((X) << DATA_LEN_SHIFT)
#define		PAGE_SIZE_SHIFT				 (0)
#define		PAGE_SIZE_MASK				 BIT_MASK(2)
#define		PAGE_SIZE_SHIFT_MASK		 (PAGE_SIZE_MASK << PAGE_SIZE_SHIFT)
#define		DATA_LEN_AND_PAGE_SIZE_SHIFT_MASK	(DATA_LEN_AND_PAGE_SIZE_MASK<<PAGE_SIZE_SHIFT)
#define		SET_4KB_PAGE				 (0x00 << PAGE_SIZE_SHIFT)
#define		SET_8KB_PAGE				 (0x01 << PAGE_SIZE_SHIFT)
#define		SET_12KB_PAGE				 (0x02 << PAGE_SIZE_SHIFT)
#define		SET_16KB_PAGE				 (0x03 << PAGE_SIZE_SHIFT)

#define R32_FCON_MT_ADR_BASE		(0x120 >> 2)
#define		MT_GLB_TRIG_EN_BIT				BIT22
#define 	INT_IDX_MODE_BIT				BIT17
#define 	SET_MT_IDX_MODE_EN              SET_BIT22
#define 	CLR_MT_IDX_MODE_EN              CLR_BIT22
#define     MT_TABLE_SIZE_SHIFT             (18)
#define     MT_TABLE_SIZE_MASK              BIT_MASK(4)
#define 	SET_INT_IDX_MODE                SET_BIT17
#define 	CLR_INT_IDX_MODE                CLR_BIT17
#define 	MT_BAS_ADDR_IRAM_SHIFT			(0)
#define 	MT_BAS_ADDR_IRAM_MASK			BIT_MASK(16)
#define 	MT_BAS_ADDR_IRAM_SHIFT_MASK		(MT_BAS_ADDR_IRAM_MASK << MT_BAS_ADDR_IRAM_SHIFT)

#define R32_FCON_SYS_INTQ_BASE		(0x124 >> 2)

#define R32_FCON_FLHCLK_SPLITE_EN	(0x128 >> 2)
#define     MT_ABORT_NOT_PRE_LOAD_LD        BIT31
#define     MT_ABORT_NOT_PRE_LOAD_WP        BIT30
#define 	SPLIT_RW_FLH_CLK_EN_BIT			BIT0

#define R32_FCON_MTQ_INF_0			(0x12C >> 2)
#define		GLB_MTQ3_NEXE_EVT_NUM_SHIFT		(24)
#define		GLB_MTQ3_NEXE_EVT_NUM_MASK		BIT_MASK(8)
#define		GLB_MTQ2_NEXE_EVT_NUM_SHIFT		(16)
#define		GLB_MTQ2_NEXE_EVT_NUM_MASK		BIT_MASK(8)
#define		GLB_MTQ1_NEXE_EVT_NUM_SHIFT		(8)
#define		GLB_MTQ1_NEXE_EVT_NUM_MASK		BIT_MASK(8)
#define		GLB_MTQ0_NEXE_EVT_NUM_SHIFT		(0)
#define		GLB_MTQ0_NEXE_EVT_NUM_MASK		BIT_MASK(8)

#define R32_FCON_MTQ_INF_1			(0x130 >> 2)
#define		GLB_MTQ7_NEXE_EVT_NUM_SHIFT		(24)
#define		GLB_MTQ7_NEXE_EVT_NUM_MASK		BIT_MASK(8)
#define		GLB_MTQ6_NEXE_EVT_NUM_SHIFT		(16)
#define		GLB_MTQ6_NEXE_EVT_NUM_MASK		BIT_MASK(8)
#define		GLB_MTQ5_NEXE_EVT_NUM_SHIFT		(8)
#define		GLB_MTQ5_NEXE_EVT_NUM_MASK		BIT_MASK(8)
#define		GLB_MTQ4_NEXE_EVT_NUM_SHIFT		(0)
#define		GLB_MTQ4_NEXE_EVT_NUM_MASK		BIT_MASK(8)

#define R32_FCON_MTQ_INF_2			(0x134 >> 2)
#define		GLB_MTQ11_NEXE_EVT_NUM_SHIFT	(24)
#define		GLB_MTQ11_NEXE_EVT_NUM_MASK		BIT_MASK(8)
#define		GLB_MTQ10_NEXE_EVT_NUM_SHIFT	(16)
#define		GLB_MTQ10_NEXE_EVT_NUM_MASK		BIT_MASK(8)
#define		GLB_MTQ9_NEXE_EVT_NUM_SHIFT		(8)
#define		GLB_MTQ9_NEXE_EVT_NUM_MASK		BIT_MASK(8)
#define		GLB_MTQ8_NEXE_EVT_NUM_SHIFT		(0)
#define		GLB_MTQ8_NEXE_EVT_NUM_MASK		BIT_MASK(8)

#define R32_FCON_MTQ_INF_3			(0x138 >> 2)
#define		GLB_MTQ15_NEXE_EVT_NUM_SHIFT	(24)
#define		GLB_MTQ15_NEXE_EVT_NUM_MASK		BIT_MASK(8)
#define		GLB_MTQ14_NEXE_EVT_NUM_SHIFT	(16)
#define		GLB_MTQ14_NEXE_EVT_NUM_MASK		BIT_MASK(8)
#define		GLB_MTQ13_NEXE_EVT_NUM_SHIFT	(8)
#define		GLB_MTQ13_NEXE_EVT_NUM_MASK		BIT_MASK(8)
#define		GLB_MTQ12_NEXE_EVT_NUM_SHIFT	(0)
#define		GLB_MTQ12_NEXE_EVT_NUM_MASK		BIT_MASK(8)

#define R32_FCON_ERASE_PAG			(0x13C >> 2)
#define     SET_EP_COR_DIS			    SET_BIT17
#define		EP_COR_DIS_BIT				BIT17
#define		EP_INT_TYPE_BIT				BIT16
#define		SET_EP_INT_WITHOUT_UNC		SET_BIT16
#define		CLR_EP_INT_WITH_UNC			CLR_BIT16
#define		ERS_PG_THR_LOW_SHIFT		(0)
#define		ERS_PG_THR_LOW_MASK			BIT_MASK(16)
#define		ERS_PG_THR_LOW_SHIFT_MASK	(ERS_PG_THR_LOW_MASK << ERS_PG_THR_LOW_SHIFT)

#define R32_FCON_ZIP_CFG			(0x144 >> 2)
#define     BLCA_NOZIP_EN               BIT29
#define		VLD_LB_EN				    BIT28
#define		RS_TAG_EMP_ABT_EN_BIT		BIT25
#define		RS_TAG_ABT_EN_BIT			BIT24
#define		UP_ERR_ADR_EN_BIT			BIT23
#define		XZIP_LCA_CHK_EN				BIT22
#define		ULTRA_W_WAIT_IBF_PTR_SHIFT	(20)
#define		ULTRA_W_WAIT_IBF_PTR_MASK	BIT_MASK(2)
#define		BMU_TIM_SHIFT				(16)
#define		BMU_TIM_MASK				BIT_MASK(4)
#define		ERR_NO_FREE_BUF_BIT			BIT15
#define		GC_CRC_BR_EN_BIT			BIT14
#define		SLV_SIGN_MSK_DIS			BIT13
#define		AES_IDX_SHIFT				(0)
#define		AES_IDX_MASK				BIT_MASK(5)

#define R32_FCON_SYS_INTQ_BASE1		(0x14C >> 2)

#define R32_FCON_SYS_INTQ_BASE2		(0x150 >> 2)

#define R32_FCON_FTA_MSK			(0x154 >> 2)

#define R32_FCON_DSP_TRIG			(0x158 >> 2)
#define		SB_LEVEL_SHIFT				(24)
#define		SB_LEVEL_MASK				BIT_MASK(5)
#define		SB_LEVEL_SHIFT_MASK			(SB_LEVEL_MASK << SB_LEVEL_SHIFT)
#define		TSB_SB_LEVEL				(0x08)
#define		MICRON_SB_LEVEL				(0x06)
#define		M_SET_SB_LEVEL(n)			(((n) << SB_LEVEL_SHIFT) & SB_LEVEL_SHIFT_MASK)
#define		SHIFT_DIRECTION_SHIFT		(16)
#define		SHIFT_DIRECTION_MASK		BIT_MASK(4)
#define		DSP_LLR_TABLE_BIT			BIT15
#define		DSP_SM_DIS_BIT				BIT14
#define		DSP_IBF_PTR_SHIFT			(11)
#define		DSP_IBF_PTR_MASK			BIT_MASK(3)
#define		DSP_IBF_PTR_SHIFT_MASK		(DSP_IBF_PTR_MASK << DSP_IBF_PTR_SHIFT)
#define		DSP_4K_EN_BIT				BIT10
#define		DSP_2K_SEL_BIT				BIT9
#define		DSP_FW_CLEAR_BIT			BIT8
#define		DSP2_TRIG_SHIFT				(4)
#define		DSP2_TRIG_MASK				BIT_MASK(4)
#define		DSP2_TRIG_SHIFT_MASK			(DSP2_TRIG_MASK << DSP2_TRIG_SHIFT)
#define		DSP_TRIG_SHIFT				(0)
#define		DSP_TRIG_MASK				BIT_MASK(4)
#define		DSP_TRIG_SHIFT_MASK			(DSP_TRIG_MASK << DSP_TRIG_SHIFT)

#define R32_FCON_ECC_PARAM_CFG		(0x15C >> 2)
#define		DSP_LLR_TABLE_SEL_SHIFT		(26)
#define		DSP_LLR_TABLE_SEL_MASK		BIT_MASK(4)
#define		DSP_CENTER_SEL_SHIFT		(24)
#define		DSP_CENTER_SEL_MASK			BIT_MASK(2)
#define		DSP_DISTRIBUTION_SEL_SHIFT	(16)
#define		DSP_DISTRIBUTION_SEL_MASK	BIT_MASK(5)
#define		ECC_PARAM_SEL_SHIFT			(8)
#define		ECC_PARAM_SEL_MASK			BIT_MASK(5)
#define		DSP_LUT_LOAD_BIT			BIT7
#define		DSP_GRAY_CODE_LOAD_BIT		BIT6
#define		DSP_PARAM_LOAD_BIT			BIT5
#define		BF_PARAM_LOAD_BIT			BIT4
#define		LLR_TABLE3_LOAD_BIT			BIT3
#define		LLR_TABLE2_LOAD_BIT			BIT2
#define		LLR_TABLE1_LOAD_BIT			BIT1
#define		LLR_TABLE0_LOAD_BIT			BIT0

#define R32_FCON_ECC_PARAM			(0x160 >> 2)

#define R32_FCON_DSP_CENTER_0		(0x164 >> 2)

#define R32_FCON_DSP_CENTER_1		(0x168 >> 2)

#define R32_FCON_DSP_DISTRIBUTION   (0x16C >> 2)

#define R32_FCON_DSP_LLR_TABLE		(0x170 >> 2)

#define R32_FCON_ECC_REG_CFG		(0x174 >> 2)
#define		LDPC_REG_SEL_SHIFT		(24)
#define		LDPC_REG_SEL_MASK		BIT_MASK(5)
#define		LDPC_REG_SHIFT			(16)
#define		LDPC_REG_MASK			BIT_MASK(3)
#define		DSP_REG_SEL_SHIFT		(8)
#define		DSP_REG_SEL_MASK		BIT_MASK(5)
#define		DSP_REG_SHIFT			(0)
#define		DSP_REG_MASK			BIT_MASK(2)
#define 	DSP_PARAM				(0)
#define 	DSP_GRAYCODE			(1)
#define 	DSP_LLR					(2)

#define R32_FCON_DSP_REG			(0x174 >> 2)		//DSP Information Register

#define R32_FCON_LDPC_REG			(0x17C >> 2)		//LDPC Information Register

#define R32_FCON_ERASE_PAG_1		(0x180 >> 2)
#define     EARLY_BAD_THR_LOW_SHIFT     (16)
#define     EARLY_BAD_THR_LOW_MASK      BIT_MASK(16)
#define		ERS_PG_THR_HIGH_SHIFT		(0)
#define		ERS_PG_THR_HIGH_MASK		BIT_MASK(16)

#define R32_FCON_CNV_CFG			(0x184 >> 2)
#define		TLC_PTR_EN_3_BIT			BIT22
#define		TLC_PTR_LOC_3_SHIFT			(16)
#define		TLC_PTR_LOC_3_MASK			BIT_MASK(6)
#define		CONV_INIT_SEL_DAT_LEN_SHIFT	(9)
#define		CONV_INIT_SEL_DAT_LEN_MASK	BIT_MASK(2)
#define		CONV_INIT_SEL_EN_BIT		BIT8
#define		CONV_INIT_SEL_IDX_SHIFT		(0)
#define		CONV_INIT_SEL_IDX_MASK		BIT_MASK(7)


#define R32_FCON_TGL_TIME_CFG_2			(0x1A4 >> 2)
#define		TGL_READ_DAT_PRE_PHASE_CNT_CFG1_SHIFT	(24)
#define		TGL_READ_DAT_PRE_PHASE_CNT_CFG1_MASK	BIT_MASK(5)
#define		TGL_READ_DAT_PRE_PHASE_CNT_SHIFT		(16)
#define		TGL_READ_DAT_PRE_PHASE_CNT_MASK			BIT_MASK(5)
#define		TGL_READ_DAT_PST_PHASE_CNT_CFG1_SHIFT	(8)
#define		TGL_READ_DAT_PST_PHASE_CNT_CFG1_MASK	BIT_MASK(5)
#define		TGL_READ_DAT_PST_PHASE_CNT_SHIFT		(0)
#define		TGL_READ_DAT_PST_PHASE_CNT_MASK			BIT_MASK(5)

#define R32_FCON_TGL_TIME_CFG_3			(0x1C0 >> 2)
#define		TGL_WARM_UP_PST_COUNT_WRITE_TIMING_CFG1_SHIFT 	    	 (27)
#define		TGL_WARM_UP_PST_COUNT_WRITE_TIMING_CFG1_MASK	     	 BIT_MASK(5)
#define		TGL_WARM_UP_PST_COUNT_WRITE_TIMING_CFG1_SHIFT_MASK  	 (TGL_WARM_UP_PST_COUNT_WRITE_TIMING_CFG1_MASK << TGL_WARM_UP_PST_COUNT_WRITE_TIMING_CFG1_SHIFT)
#define		TGL_WARM_UP_TCLR_COUNT_WRITE_TIMING_CFG1_SHIFT 	     	 (21)
#define		TGL_WARM_UP_TCLR_COUNT_WRITE_TIMING_CFG1_MASK	    	 BIT_MASK(6)
#define		TGL_WARM_UP_TCLR_COUNT_WRITE_TIMING_CFG1_SHIFT_MASK  	 (TGL_WARM_UP_TCLR_COUNT_WRITE_TIMING_CFG1_MASK << TGL_WARM_UP_TCLR_COUNT_WRITE_TIMING_CFG1_SHIFT)
#define		TGL_WARM_UP_PST_COUNT_WRITE_TIMING_CFG0_SHIFT 	    	 (16)
#define		TGL_WARM_UP_PST_COUNT_WRITE_TIMING_CFG0_MASK	     	 BIT_MASK(5)
#define		TGL_WARM_UP_PST_COUNT_WRITE_TIMING_CFG0_SHIFT_MASK  	 (TGL_WARM_UP_PST_COUNT_WRITE_TIMING_CFG0_MASK << TGL_WARM_UP_PST_COUNT_WRITE_TIMING_CFG0_SHIFT)
#define		TGL_WARM_UP_SUSP_COUNT_WRITE_TIMING_CFG1_SHIFT 	  		 (11)
#define		TGL_WARM_UP_SUSP_COUNT_WRITE_TIMING_CFG1_MASK	     	 BIT_MASK(5)
#define		TGL_WARM_UP_SUSP_COUNT_WRITE_TIMING_CFG1_SHIFT_MASK  	 (TGL_WARM_UP_SUSP_COUNT_WRITE_TIMING_CFG1_MASK << TGL_WARM_UP_SUSP_COUNT_WRITE_TIMING_CFG1_SHIFT)
#define		TGL_WARM_UP_TCLR_COUNT_WRITE_TIMING_CFG0_SHIFT 	     	 (5)
#define		TGL_WARM_UP_TCLR_COUNT_WRITE_TIMING_CFG0_MASK	    	 BIT_MASK(6)
#define		TGL_WARM_UP_TCLR_COUNT_WRITE_TIMING_CFG0_SHIFT_MASK  	 (TGL_WARM_UP_TCLR_COUNT_WRITE_TIMING_CFG0_MASK << TGL_WARM_UP_TCLR_COUNT_WRITE_TIMING_CFG0_SHIFT)
#define		TGL_WARM_UP_SUSP_COUNT_WRITE_TIMING_CFG0_SHIFT 	  		 (0)
#define		TGL_WARM_UP_SUSP_COUNT_WRITE_TIMING_CFG0_MASK	     	 BIT_MASK(5)
#define		TGL_WARM_UP_SUSP_COUNT_WRITE_TIMING_CFG0_SHIFT_MASK  	 (TGL_WARM_UP_SUSP_COUNT_WRITE_TIMING_CFG0_MASK << TGL_WARM_UP_SUSP_COUNT_WRITE_TIMING_CFG0_SHIFT)

#define R32_FCON_TGL_TIME_CFG_4			(0x1C4 >> 2)
#define		TGL_WARM_UP_PST_COUNT_READ_TIMING_CFG1_SHIFT 	     (27)
#define		TGL_WARM_UP_PST_COUNT_READ_TIMING_CFG1_MASK	     	 BIT_MASK(5)
#define		TGL_WARM_UP_PST_COUNT_READ_TIMING_CFG1_SHIFT_MASK  	 (TGL_WARM_UP_PST_COUNT_WRITE_TIMING_CFG1_MASK << TGL_WARM_UP_PST_COUNT_WRITE_TIMING_CFG1_SHIFT)
#define		TGL_WARM_UP_TCLR_COUNT_READ_TIMING_CFG1_SHIFT 	     (21)
#define		TGL_WARM_UP_TCLR_COUNT_READ_TIMING_CFG1_MASK	     BIT_MASK(6)
#define		TGL_WARM_UP_TCLR_COUNT_READ_TIMING_CFG1_SHIFT_MASK   (TGL_WARM_UP_TCLR_COUNT_WRITE_TIMING_CFG1_MASK << TGL_WARM_UP_TCLR_COUNT_WRITE_TIMING_CFG1_SHIFT)
#define		TGL_WARM_UP_PST_COUNT_READ_TIMING_CFG0_SHIFT 	     (16)
#define		TGL_WARM_UP_PST_COUNT_READ_TIMING_CFG0_MASK	     	 BIT_MASK(5)
#define		TGL_WARM_UP_PST_COUNT_READ_TIMING_CFG0_SHIFT_MASK  	 (TGL_WARM_UP_PST_COUNT_WRITE_TIMING_CFG0_MASK << TGL_WARM_UP_PST_COUNT_WRITE_TIMING_CFG0_SHIFT)
#define		TGL_WARM_UP_SUSP_COUNT_READ_TIMING_CFG1_SHIFT 	  	 (11)
#define		TGL_WARM_UP_SUSP_COUNT_READ_TIMING_CFG1_MASK	      BIT_MASK(5)
#define		TGL_WARM_UP_SUSP_COUNT_READ_TIMING_CFG1_SHIFT_MASK   (TGL_WARM_UP_SUSP_COUNT_WRITE_TIMING_CFG1_MASK << TGL_WARM_UP_SUSP_COUNT_WRITE_TIMING_CFG1_SHIFT)
#define		TGL_WARM_UP_TCLR_COUNT_READ_TIMING_CFG0_SHIFT 	      (5)
#define		TGL_WARM_UP_TCLR_COUNT_READ_TIMING_CFG0_MASK	     BIT_MASK(6)
#define		TGL_WARM_UP_TCLR_COUNT_READ_TIMING_CFG0_SHIFT_MASK   (TGL_WARM_UP_TCLR_COUNT_WRITE_TIMING_CFG0_MASK << TGL_WARM_UP_TCLR_COUNT_WRITE_TIMING_CFG0_SHIFT)
#define		TGL_WARM_UP_SUSP_COUNT_READ_TIMING_CFG0_SHIFT 	  	 (0)
#define		TGL_WARM_UP_SUSP_COUNT_READ_TIMING_CFG0_MASK	      BIT_MASK(5)
#define		TGL_WARM_UP_SUSP_COUNT_READ_TIMING_CFG0_SHIFT_MASK   (TGL_WARM_UP_SUSP_COUNT_WRITE_TIMING_CFG0_MASK << TGL_WARM_UP_SUSP_COUNT_WRITE_TIMING_CFG0_SHIFT)

#define R32_FCON_LDPC_ITE_1             (0x1E8 >> 2)
#define     MSA_MAX_ITE_HB_SHIFT            (0)
#define     MSA_MAX_ITE_HB_MASK             BIT_MASK(8)

#define R32_FCON_RAIDECC_PB_SEL			(0x1F0 >> 2)
#define R8_FCON_RAIDECC_PB_SEL			(0x1F0 >> 0)
#define		RAIDECC_PB_SEL_SHIFT            (0)
#define		RAIDECC_PB_SEL_MASK				BIT_MASK(6)
#define		RAIDECC_PB_SEL_SHIFT_MASK		(RAIDECC_PB_SEL_MASK << RAIDECC_PB_SEL_SHIFT)

#define R32_FCON_RAIDECC_PB_STS			(0x1F4 >> 2)
#define 	RAIDECC_PB_STS_SHIFT			(16)
#define		RAIDECC_PB_STS_BIT				BIT16
#define		RAIDECC_PB_TAG_NUM_SHIFT		(0)
#define		RAIDECC_PB_TAG_NUM_MASK			BIT_MASK(9)

#define R32_FCON_LDPC_ITE_CFG				(0x1F8 >> 2)
#define		FHB_MAX_ITE_SHIFT		            (0)
#define		FHB_MAX_ITE_MASK			        BIT_MASK(8)
#define		NHB_MAX_ITE_SHIFT		            (8)
#define		NHB_MAX_ITE_MASK			        BIT_MASK(8)
#define		GDBF_MAX_ITE_SHIFT		            (16)
#define		GDBF_MAX_ITE_MASK			        BIT_MASK(8)
#define 	MSA_MAX_ITE_SHIFT					(24)
#define 	MSA_MAX_ITE_MASK					BIT_MASK(8)
#define     M_CLR_LDPC_FHB_MAX_ITE()	            (R32_FCON[R32_FCON_LDPC_ITE_CFG] &= ~(FHB_MAX_ITE_MASK << FHB_MAX_ITE_SHIFT))
#define     M_SEL_LDPC_FHB_MAX_ITE(FHB_MAX_ITE)		(R32_FCON[R32_FCON_LDPC_ITE_CFG] |= ((FHB_MAX_ITE & FHB_MAX_ITE_MASK) << FHB_MAX_ITE_SHIFT))
#define     M_CLR_LDPC_NHB_MAX_ITE()	            (R32_FCON[R32_FCON_LDPC_ITE_CFG] &= ~(NHB_MAX_ITE_MASK << NHB_MAX_ITE_SHIFT))
#define     M_SEL_LDPC_NHB_MAX_ITE(NHB_MAX_ITE)		(R32_FCON[R32_FCON_LDPC_ITE_CFG] |= ((NHB_MAX_ITE & NHB_MAX_ITE_MASK) << NHB_MAX_ITE_SHIFT))
#define     M_CLR_LDPC_GDBF_MAX_ITE()	            (R32_FCON[R32_FCON_LDPC_ITE_CFG] &= ~(GDBF_MAX_ITE_MASK << GDBF_MAX_ITE_SHIFT))
#define     M_SEL_LDPC_GDBF_MAX_ITE(GDBF_MAX_ITE)	(R32_FCON[R32_FCON_LDPC_ITE_CFG] |= ((GDBF_MAX_ITE & GDBF_MAX_ITE_MASK) << GDBF_MAX_ITE_SHIFT))
#define     M_CLR_LDPC_MSA_MAX_ITE()	            (R32_FCON[R32_FCON_LDPC_ITE_CFG] &= ~(MSA_MAX_ITE_MASK << MSA_MAX_ITE_SHIFT))
#define     M_SEL_LDPC_MSA_MAX_ITE(MSA_MAX_ITE)		(R32_FCON[R32_FCON_LDPC_ITE_CFG] |= ((MSA_MAX_ITE & MSA_MAX_ITE_MASK) << MSA_MAX_ITE_SHIFT))

#define R8_FCON_PAGE_CFG_1				(0x1FC)
#define R32_FCON_PAGE_CFG_1				(0x1FC >> 2)
#define		RAIDECC_PARITY_CNT_SHIFT		(27)
#define		RAIDECC_PARITY_CNT_MASK			BIT_MASK(4)
#define		FRM_3_DMY_LEN_SHIFT				(16)
#define		FRM_3_DMY_LEN_MASK				BIT_MASK(11)
#define		FRM_3_DMY_LEN_SHIFT_MASK		(FRM_3_DMY_LEN_MASK << FRM_3_DMY_LEN_SHIFT)
#define		FRM_2_DMY_LEN_SHIFT				(0)
#define		FRM_2_DMY_LEN_MASK				BIT_MASK(11)
#define		FRM_2_DMY_LEN_SHIFT_MASK		(FRM_2_DMY_LEN_MASK << FRM_2_DMY_LEN_SHIFT)

#define R32_FCON_ALU_REG_0				(0x200 >> 2)
#define R16_FCON_ALU_REG_0				(0x200 >> 1)
#define		ALU_G0_0_SHIFT					(0)
#define		ALU_G0_0_MASK					BIT_MASK(16)
#define		ALU_G0_1_SHIFT					(16)
#define		ALU_G0_1_MASK					BIT_MASK(16)

#define R32_FCON_ALU_REG_1              (0x204 >> 2)
#define R16_FCON_ALU_REG_1				(0x204 >> 1)
#define		ALU_G0_2_SHIFT					(0)
#define		ALU_G0_2_MASK					BIT_MASK(16)
#define		ALU_G0_3_SHIFT					(16)
#define		ALU_G0_3_MASK					BIT_MASK(16)

#define R32_FCON_ALU_REG_2				(0x208 >> 2)
#define		ALU_G0_4_SHIFT					(0)
#define		ALU_G0_4_MASK					BIT_MASK(16)
#define		ALU_G0_5_SHIFT					(16)
#define		ALU_G0_5_MASK					BIT_MASK(16)

#define R32_FCON_ALU_REG_3				(0x20C >> 2)
#define		ALU_G0_6_SHIFT					(0)
#define		ALU_G0_6_MASK					BIT_MASK(16)
#define		ALU_G0_7_SHIFT					(16)
#define		ALU_G0_7_MASK					BIT_MASK(16)

#define R32_FCON_ALU_REG_4				(0x210 >> 2)
#define		ALU_G1_0_SHIFT					(0)
#define		ALU_G1_0_MASK					BIT_MASK(16)
#define		ALU_G1_1_SHIFT					(16)
#define		ALU_G1_1_MASK					BIT_MASK(16)

#define R32_FCON_ALU_REG_5				(0x214 >> 2)
#define		ALU_G1_2_SHIFT					(0)
#define		ALU_G1_2_MASK					BIT_MASK(16)
#define		ALU_G1_3_SHIFT					(16)
#define		ALU_G1_3_MASK					BIT_MASK(16)

#define R32_FCON_ALU_REG_6				(0x218 >> 2)
#define		ALU_G1_4_SHIFT					(0)
#define		ALU_G1_4_MASK					BIT_MASK(16)
#define		ALU_G1_5_SHIFT					(16)
#define		ALU_G1_5_MASK					BIT_MASK(16)

#define R32_FCON_ALU_REG_7				(0x21C >> 2)
#define		ALU_G1_6_SHIFT					(0)
#define		ALU_G1_6_MASK					BIT_MASK(16)
#define		ALU_G1_7_SHIFT					(16)
#define		ALU_G1_7_MASK					BIT_MASK(16)

#define R32_FCON_ALU_REG_8				(0x220 >> 2)
#define		ALU_G2_0_SHIFT					(0)
#define		ALU_G2_0_MASK					BIT_MASK(16)
#define		ALU_G2_1_SHIFT					(16)
#define		ALU_G2_1_MASK					BIT_MASK(16)

#define R32_FCON_ALU_REG_9				(0x224 >> 2)
#define		ALU_G2_2_SHIFT					(0)
#define		ALU_G2_2_MASK					BIT_MASK(16)
#define		ALU_G2_3_SHIFT					(16)
#define		ALU_G2_3_MASK					BIT_MASK(16)

#define R32_FCON_ALU_REG_10				(0x228 >> 2)
#define		ALU_G2_4_SHIFT					(0)
#define		ALU_G2_4_MASK					BIT_MASK(16)
#define		ALU_G2_5_SHIFT					(16)
#define		ALU_G2_5_MASK					BIT_MASK(16)

#define R32_FCON_ALU_REG_11				(0x22C >> 2)
#define		ALU_G2_6_SHIFT					(0)
#define		ALU_G2_6_MASK					BIT_MASK(16)
#define		ALU_G2_7_SHIFT					(16)
#define		ALU_G2_7_MASK					BIT_MASK(16)

#define R32_FCON_ALU_REG_12				(0x230 >> 2)
#define		ALU_G3_0_SHIFT					(0)
#define		ALU_G3_0_MASK					BIT_MASK(16)
#define		ALU_G3_1_SHIFT					(16)
#define		ALU_G3_1_MASK					BIT_MASK(16)

#define R32_FCON_ALU_REG_13				(0x234 >> 2)
#define		ALU_G3_2_SHIFT					(0)
#define		ALU_G3_2_MASK					BIT_MASK(16)
#define		ALU_G3_3_SHIFT					(16)
#define		ALU_G3_3_MASK					BIT_MASK(16)

#define R32_FCON_ALU_REG_14				(0x238 >> 2)
#define		ALU_G3_4_SHIFT					(0)
#define		ALU_G3_4_MASK					BIT_MASK(16)
#define		ALU_G3_5_SHIFT					(16)
#define		ALU_G3_5_MASK					BIT_MASK(16)

#define R32_FCON_ALU_REG_15				(0x23C >> 2)
#define		ALU_G3_6_SHIFT					(0)
#define		ALU_G3_6_MASK					BIT_MASK(16)
#define		ALU_G3_7_SHIFT					(16)
#define		ALU_G3_7_MASK					BIT_MASK(16)

#define R32_FCON_PROGRAM_SPD_SEQ_01		(0x240 >> 2)
#define		PROGRAM_SPD_SEQ_1_SHIFT			(16)
#define		PROGRAM_SPD_SEQ_1_MASK			BIT_MASK(16)
#define		PROGRAM_SPD_SEQ_0_SHIFT			(0)
#define		PROGRAM_SPD_SEQ_0_MASK			BIT_MASK(16)
#define	R8_FCONB_PROGRAM_SPD_SEQ		(0x240 >> 0)
#define		M_FCON_PROGRAM_SPD_SEQ_BASE(x)	(FLH_TOP_REG_BASE + R8_FCONB_PROGRAM_SPD_SEQ + (2 * (x)))
#define		M_FCON_PROGRAM_SPD_SEQ(x)		*((volatile U16 *) M_FCON_PROGRAM_SPD_SEQ_BASE(x))

#define R32_FCON_PROGRAM_SPD_SEQ_23		(0x244 >> 2)
#define		PROGRAM_SPD_SEQ_3_SHIFT			(16)
#define		PROGRAM_SPD_SEQ_3_MASK			BIT_MASK(16)
#define		PROGRAM_SPD_SEQ_2_SHIFT			(0)
#define		PROGRAM_SPD_SEQ_2_MASK			BIT_MASK(16)

#define R32_FCON_PROGRAM_RSM_SEQ_01		(0x248 >> 2)
#define		PROGRAM_RSM_SEQ_1_SHIFT			(16)
#define		PROGRAM_RSM_SEQ_1_MASK			BIT_MASK(16)
#define		PROGRAM_RSM_SEQ_0_SHIFT			(0)
#define		PROGRAM_RSM_SEQ_0_MASK			BIT_MASK(16)
#define	R8_FCONB_PROGRAM_RSM_SEQ		(0x248 >> 0)
#define		M_FCON_PROGRAM_RSM_SEQ_BASE(x)	(FLH_TOP_REG_BASE + R8_FCONB_PROGRAM_RSM_SEQ + (2 * (x)))
#define		M_FCON_PROGRAM_RSM_SEQ(x)         *((volatile U16 *) M_FCON_PROGRAM_RSM_SEQ_BASE(x))

#define R32_FCON_PROGRAM_RSM_SEQ_23		(0x24C >> 2)
#define		PROGRAM_RSM_SEQ_3_SHIFT			(16)
#define		PROGRAM_RSM_SEQ_3_MASK			BIT_MASK(16)
#define		PROGRAM_RSM_SEQ_2_SHIFT			(0)
#define		PROGRAM_RSM_SEQ_2_MASK			BIT_MASK(16)

#define R32_FCON_PROGRAM_SPD_SEQ_45		(0x250 >> 2)
#define		PROGRAM_SPD_SEQ_5_SHIFT			(16)
#define		PROGRAM_SPD_SEQ_5_MASK			BIT_MASK(16)
#define		PROGRAM_SPD_SEQ_4_SHIFT			(0)
#define		PROGRAM_SPD_SEQ_4_MASK			BIT_MASK(16)
#define	R8_FCONB_ERASE_SPD_SEQ			(0x250 >>0)
#define		M_FCON_ERASE_SPD_SEQ_BASE(x)	(FLH_TOP_REG_BASE + R8_FCONB_ERASE_SPD_SEQ + (2 * (x)))
#define		M_FCON_ERASE_SPD_SEQ(x)			*((volatile U16 *) M_FCON_ERASE_SPD_SEQ_BASE(x))

#define R32_FCON_PROGRAM_SPD_SEQ_67		(0x254 >> 2)
#define		PROGRAM_SPD_SEQ_7_SHIFT			(16)
#define		PROGRAM_SPD_SEQ_7_MASK			BIT_MASK(16)
#define		PROGRAM_SPD_SEQ_6_SHIFT			(0)
#define		PROGRAM_SPD_SEQ_6_MASK			BIT_MASK(16)

#define R32_FCON_PROGRAM_RSM_SEQ_45		(0x258 >> 2)        // E17

#define		PROGRAM_RSM_SEQ_5_SHIFT			(16)
#define		PROGRAM_RSM_SEQ_5_MASK			BIT_MASK(16)
#define		PROGRAM_RSM_SEQ_4_SHIFT			(0)
#define		PROGRAM_RSM_SEQ_4_MASK			BIT_MASK(16)
#define	R8_FCONB_RSM_SEQ_01				(0x258 >> 0)
#define		M_FCON_RSM_SEQ_BASE(x)			(FLH_TOP_REG_BASE + R8_FCONB_RSM_SEQ_01 + (2 * (x)))
#define		M_FCON_RSM_SEQ(x)				*((volatile U16 *) M_FCON_RSM_SEQ_BASE(x))

#define R32_FCON_PROGRAM_RSM_SEQ_67		(0x25C >> 2)
#define		PROGRAM_RSM_SEQ_7_SHIFT			(16)
#define		PROGRAM_RSM_SEQ_7_MASK			BIT_MASK(16)
#define		PROGRAM_RSM_SEQ_6_SHIFT			(0)
#define		PROGRAM_RSM_SEQ_6_MASK			BIT_MASK(16)

#define R32_FCON_PROG_PCA_BASE			(0x274 >> 2)
#define		PROG_PCA_UPD_EN_BIT				BIT31
#define		SPDPFA_ERR_CPU_SHIFT			(28)
#define		SPDPFA_ERR_CPU_MASK				BIT_MASK(3)
#define		SPDPFA_ERR_CPU_SHIFT_MASK		(SPDPFA_ERR_CPU_MASK << SPDPFA_ERR_CPU_SHIFT)
#define		SPDPFA_ERR_CPU0             	(0x01)
#define		SPDPFA_ERR_CPU1					(0x02)
#define		SPDPFA_ERR_COP0					(0x04)
#define		SPDPFA_IDX_BASE_SHIFT			(16)
#define		SPDPFA_IDX_BASE_MASK			BIT_MASK(12)
#define		SPDPFA_IDX_BASE_SHIFT_MASK		(SPDPFA_IDX_BASE_MASK << SPDPFA_IDX_BASE_SHIFT)
#define		PROG_PCA_BASE_SHIFT				(0)
#define		PROG_PCA_BASE_MASK				BIT_MASK(16)
#define		PROG_PCA_BASE_SHIFT_MASK		(PROG_PCA_BASE_MASK << PROG_PCA_BASE_SHIFT)

#define R32_FCON_POWER_FAIL				(0x278 >> 2)
#define		ALL_PWRFALL_STOP_BIT			BIT8
#define		POWER_FAIL_BIT					BIT0

#define R32_FCON_QOS_MT_ADR_BASE		(0x27C >> 2)
#define		QOS_MT_BAS_ADDR_IRAM_SHIFT		(0)
#define		QOS_MT_BAS_ADDR_IRAM_MASK		BIT_MASK(16)

#define R64_FCONLL_MTQ_BODY_0			(0x280 >> 3)

#define R32_FCON_MTQ_BODY_0_0			(0x280 >> 2)

#define R32_FCON_MTQ_BODY_0_1			(0x284 >> 2)
#define		FCON_MTQ_BODY_0_SHIFT			(0)
#define		FCON_MTQ_BODY_0_MASK			BIT_MASK(17)

#define R64_FCONLL_MTQ_BODY_1			(0x288 >> 3)

#define R32_FCON_MTQ_BODY_1_0			(0x288 >> 2)

#define R32_FCON_MTQ_BODY_1_1			(0x28C >> 2)
#define		FCON_MTQ_BODY_1_SHIFT			(0)
#define		FCON_MTQ_BODY_1_MASK			BIT_MASK(17)

#define R32_FCON_QOS_MTQ_BODY_0_0		(0x2A0 >> 2)

#define R64_FCON_QOS_MTQ_BODY_0			(0x2A0 >> 3)

#define R32_FCON_QOS_MTQ_BODY_0_1		(0x2A4 >> 2)
#define		FCON_QOS_MTQ_BODY_0_SHIFT			(0)
#define		FCON_QOS_MTQ_BODY_0_MASK			BIT_MASK(17)

#define R32_FCON_QOS_MTQ_BODY_1_0		(0x2A8 >> 2)

#define R64_FCON_QOS_MTQ_BODY_1			(0x2A8 >> 3)

#define R32_FCON_QOS_MTQ_BODY_1_1		(0x2AC >> 2)
#define		FCON_QOS_MTQ_BODY_1_SHIFT			(0)
#define		FCON_QOS_MTQ_BODY_1_MASK			BIT_MASK(17)

#define R32_FCON_MTQ_NON_EXE_SEL		(0x2C0 >> 2)
#define		FCON_QOS_MTQ_RD_PTR_SHIFT			(24)
#define		FCON_QOS_MTQ_RD_PTR_MASK			BIT_MASK(8)
#define		FCON_MTQ_RD_PTR_SHIFT				(16)
#define		FCON_MTQ_RD_PTR_MASK				BIT_MASK(8)
#define		FCON_MTQ_RD_PTR_SHIFT_MASK			(FCON_MTQ_RD_PTR_MASK << FCON_MTQ_RD_PTR_SHIFT)
#define		FCON_MTQ_NON_EXE_CE_SEL_SHIFT		(8)
#define		FCON_MTQ_NON_EXE_CE_SEL_MASK		BIT_MASK(8)
#define		FCON_MTQ_NON_EXE_CE_SEL_SHIFT_MASK	(FCON_MTQ_NON_EXE_CE_SEL_MASK << FCON_MTQ_NON_EXE_CE_SEL_SHIFT)
#define		M_SET_MTQ_BODY_CE_SEL(n)			(((n) << FCON_MTQ_NON_EXE_CE_SEL_SHIFT) & FCON_MTQ_NON_EXE_CE_SEL_SHIFT_MASK)
#define		FCON_MTQ_NON_EXE_CH_SEL_SHIFT		(0)
#define		FCON_MTQ_NON_EXE_CH_SEL_MASK  		BIT_MASK(8)
#define		FCON_MTQ_NON_EXE_CH_SEL_SHIFT_MASK	(FCON_MTQ_NON_EXE_CH_SEL_MASK << FCON_MTQ_NON_EXE_CH_SEL_SHIFT)
#define		M_SET_MTQ_BODY_CH_SEL(n)			(((n) << FCON_MTQ_NON_EXE_CH_SEL_SHIFT) & FCON_MTQ_NON_EXE_CH_SEL_SHIFT_MASK)

#define R32_FCON_AES_BYP_RANGE0             (0x2C4 >> 2)

#define R32_FCON_AES_BYP_RANGE1             (0x2C8 >> 2)

#define R32_FCON_AES_BYP_RANGE2             (0x2CC >> 2)
#define		FCON_AES_BYP_DEND_SHIFT				(8)
#define		FCON_AES_BYP_DEND_MASK				BIT_MASK(8)
#define		FCON_AES_BYP_DEND_SHIFT_MASK		(FCON_AES_BYP_DEND_MASK << FCON_AES_BYP_DEND_SHIFT)
#define		M_SET_FCON_AES_BYP_DEND(n)			(((n) << FCON_AES_BYP_DEND_SHIFT) & FCON_AES_BYP_DEND_SHIFT_MASK)
#define		FCON_AES_BYP_DSA_SHIFT				(0)
#define		FCON_AES_BYP_DSA_MASK				BIT_MASK(8)
#define		FCON_AES_BYP_DSA_SHIFT_MASK			(FCON_AES_BYP_DSA_MASK << FCON_AES_BYP_DSA_SHIFT)
#define		M_SET_FCON_AES_BYP_DSA(n)			(((n) << FCON_AES_BYP_DSA_SHIFT) & FCON_AES_BYP_DSA_SHIFT_MASK)

#define R32_FCON_MT_STS_1                   (0x2D0 >> 2)
#define M_FCON_MTQ_IS_BUSY(CH, BANK)			(R32_FCON[R32_FCON_MT_STS_1] & BIT(((BANK) * 4) + (CH)))


#define R32_FCON_MTQ_INF_4                  (0x2D4 >> 2)
#define		GLB_MTQ19_NEXE_EVT_NUM_SHIFT		(24)
#define		GLB_MTQ19_NEXE_EVT_NUM_MASK			BIT_MASK(8)
#define		GLB_MTQ18_NEXE_EVT_NUM_SHIFT		(16)
#define		GLB_MTQ18_NEXE_EVT_NUM_MASK			BIT_MASK(8)
#define		GLB_MTQ17_NEXE_EVT_NUM_SHIFT		(8)
#define		GLB_MTQ17_NEXE_EVT_NUM_MASK			BIT_MASK(8)
#define		GLB_MTQ16_NEXE_EVT_NUM_SHIFT		(0)
#define		GLB_MTQ16_NEXE_EVT_NUM_MASK			BIT_MASK(8)

#define R32_FCON_MTQ_INF_5                  (0x2D8 >> 2)
#define		GLB_MTQ23_NEXE_EVT_NUM_SHIFT		(24)
#define		GLB_MTQ23_NEXE_EVT_NUM_MASK			BIT_MASK(8)
#define		GLB_MTQ22_NEXE_EVT_NUM_SHIFT		(16)
#define		GLB_MTQ22_NEXE_EVT_NUM_MASK			BIT_MASK(8)
#define		GLB_MTQ21_NEXE_EVT_NUM_SHIFT		(8)
#define		GLB_MTQ21_NEXE_EVT_NUM_MASK			BIT_MASK(8)
#define		GLB_MTQ20_NEXE_EVT_NUM_SHIFT		(0)
#define		GLB_MTQ20_NEXE_EVT_NUM_MASK			BIT_MASK(8)

#define R32_FCON_MTQ_INF_6                  (0x2DC >> 2)
#define		GLB_MTQ27_NEXE_EVT_NUM_SHIFT		(24)
#define		GLB_MTQ27_NEXE_EVT_NUM_MASK			BIT_MASK(8)
#define		GLB_MTQ26_NEXE_EVT_NUM_SHIFT		(16)
#define		GLB_MTQ26_NEXE_EVT_NUM_MASK			BIT_MASK(8)
#define		GLB_MTQ25_NEXE_EVT_NUM_SHIFT		(8)
#define		GLB_MTQ25_NEXE_EVT_NUM_MASK			BIT_MASK(8)
#define		GLB_MTQ24_NEXE_EVT_NUM_SHIFT		(0)
#define		GLB_MTQ24_NEXE_EVT_NUM_MASK			BIT_MASK(8)

#define R32_FCON_MTQ_INF_7                  (0x2E0 >> 2)
#define		GLB_MTQ31_NEXE_EVT_NUM_SHIFT		(24)
#define		GLB_MTQ31_NEXE_EVT_NUM_MASK			BIT_MASK(8)
#define		GLB_MTQ30_NEXE_EVT_NUM_SHIFT		(16)
#define		GLB_MTQ30_NEXE_EVT_NUM_MASK			BIT_MASK(8)
#define		GLB_MTQ29_NEXE_EVT_NUM_SHIFT		(8)
#define		GLB_MTQ29_NEXE_EVT_NUM_MASK			BIT_MASK(8)
#define		GLB_MTQ28_NEXE_EVT_NUM_SHIFT		(0)
#define		GLB_MTQ28_NEXE_EVT_NUM_MASK			BIT_MASK(8)

#define R32_FCON_MTQ_QOS_INF_0              (0x304 >> 2)
#define		GLB_MTQ3_QOS_NEXE_EVT_NUM_SHIFT		(24)
#define		GLB_MTQ3_QOS_NEXE_EVT_NUM_MASK		BIT_MASK(8)
#define		GLB_MTQ2_QOS_NEXE_EVT_NUM_SHIFT		(16)
#define		GLB_MTQ2_QOS_NEXE_EVT_NUM_MASK		BIT_MASK(8)
#define		GLB_MTQ1_QOS_NEXE_EVT_NUM_SHIFT		(8)
#define		GLB_MTQ1_QOS_NEXE_EVT_NUM_MASK		BIT_MASK(8)
#define		GLB_MTQ0_QOS_NEXE_EVT_NUM_SHIFT		(0)
#define		GLB_MTQ0_QOS_NEXE_EVT_NUM_MASK		BIT_MASK(8)
#define	R8_FCONB_MTQ_QOS_INF_0				(0x304 >>0 )
#define		M_FCON_MTQ_QOS_INF_BASE(x)			(FLH_TOP_REG_BASE + R8_FCONB_MTQ_QOS_INF_0 + (x))
#define		M_FCON_MTQ_QOS_INF(x)				*((volatile U8 *) M_FCON_MTQ_QOS_INF_BASE(x))

#define R32_FCON_MTQ_QOS_INF_1              (0x308 >> 2)
#define		GLB_MTQ7_QOS_NEXE_EVT_NUM_SHIFT		(24)
#define		GLB_MTQ7_QOS_NEXE_EVT_NUM_MASK		BIT_MASK(8)
#define		GLB_MTQ6_QOS_NEXE_EVT_NUM_SHIFT		(16)
#define		GLB_MTQ6_QOS_NEXE_EVT_NUM_MASK		BIT_MASK(8)
#define		GLB_MTQ5_QOS_NEXE_EVT_NUM_SHIFT		(8)
#define		GLB_MTQ5_QOS_NEXE_EVT_NUM_MASK		BIT_MASK(8)
#define		GLB_MTQ4_QOS_NEXE_EVT_NUM_SHIFT		(0)
#define		GLB_MTQ4_QOS_NEXE_EVT_NUM_MASK		BIT_MASK(8)

#define R32_FCON_MTQ_QOS_INF_2              (0x30C >> 2)
#define		GLB_MTQ11_QOS_NEXE_EVT_NUM_SHIFT	(24)
#define		GLB_MTQ11_QOS_NEXE_EVT_NUM_MASK		BIT_MASK(8)
#define		GLB_MTQ10_QOS_NEXE_EVT_NUM_SHIFT	(16)
#define		GLB_MTQ10_QOS_NEXE_EVT_NUM_MASK		BIT_MASK(8)
#define		GLB_MTQ9_QOS_NEXE_EVT_NUM_SHIFT		(8)
#define		GLB_MTQ9_QOS_NEXE_EVT_NUM_MASK		BIT_MASK(8)
#define		GLB_MTQ8_QOS_NEXE_EVT_NUM_SHIFT		(0)
#define		GLB_MTQ8_QOS_NEXE_EVT_NUM_MASK		BIT_MASK(8)

#define R32_FCON_MTQ_QOS_INF_3              (0x310 >> 2)
#define		GLB_MTQ15_QOS_NEXE_EVT_NUM_SHIFT	(24)
#define		GLB_MTQ15_QOS_NEXE_EVT_NUM_MASK		BIT_MASK(8)
#define		GLB_MTQ14_QOS_NEXE_EVT_NUM_SHIFT	(16)
#define		GLB_MTQ14_QOS_NEXE_EVT_NUM_MASK		BIT_MASK(8)
#define		GLB_MTQ13_QOS_NEXE_EVT_NUM_SHIFT	(8)
#define		GLB_MTQ13_QOS_NEXE_EVT_NUM_MASK		BIT_MASK(8)
#define		GLB_MTQ12_QOS_NEXE_EVT_NUM_SHIFT	(0)
#define		GLB_MTQ12_QOS_NEXE_EVT_NUM_MASK		BIT_MASK(8)

#define R32_FCON_MTQ_QOS_INF_4              (0x314 >> 2)
#define		GLB_MTQ19_QOS_NEXE_EVT_NUM_SHIFT	(24)
#define		GLB_MTQ19_QOS_NEXE_EVT_NUM_MASK		BIT_MASK(8)
#define		GLB_MTQ18_QOS_NEXE_EVT_NUM_SHIFT	(16)
#define		GLB_MTQ18_QOS_NEXE_EVT_NUM_MASK		BIT_MASK(8)
#define		GLB_MTQ17_QOS_NEXE_EVT_NUM_SHIFT	(8)
#define		GLB_MTQ17_QOS_NEXE_EVT_NUM_MASK		BIT_MASK(8)
#define		GLB_MTQ16_QOS_NEXE_EVT_NUM_SHIFT	(0)
#define		GLB_MTQ16_QOS_NEXE_EVT_NUM_MASK		BIT_MASK(8)

#define R32_FCON_MTQ_QOS_INF_5              (0x318 >> 2)
#define		GLB_MTQ23_QOS_NEXE_EVT_NUM_SHIFT	(24)
#define		GLB_MTQ23_QOS_NEXE_EVT_NUM_MASK		BIT_MASK(8)
#define		GLB_MTQ22_QOS_NEXE_EVT_NUM_SHIFT	(16)
#define		GLB_MTQ22_QOS_NEXE_EVT_NUM_MASK		BIT_MASK(8)
#define		GLB_MTQ21_QOS_NEXE_EVT_NUM_SHIFT	(8)
#define		GLB_MTQ21_QOS_NEXE_EVT_NUM_MASK		BIT_MASK(8)
#define		GLB_MTQ20_QOS_NEXE_EVT_NUM_SHIFT	(0)
#define		GLB_MTQ20_QOS_NEXE_EVT_NUM_MASK		BIT_MASK(8)

#define R32_FCON_MTQ_QOS_INF_6              (0x31C >> 2)
#define		GLB_MTQ27_QOS_NEXE_EVT_NUM_SHIFT	(24)
#define		GLB_MTQ27_QOS_NEXE_EVT_NUM_MASK		BIT_MASK(8)
#define		GLB_MTQ26_QOS_NEXE_EVT_NUM_SHIFT	(16)
#define		GLB_MTQ26_QOS_NEXE_EVT_NUM_MASK		BIT_MASK(8)
#define		GLB_MTQ25_QOS_NEXE_EVT_NUM_SHIFT	(8)
#define		GLB_MTQ25_QOS_NEXE_EVT_NUM_MASK		BIT_MASK(8)
#define		GLB_MTQ24_QOS_NEXE_EVT_NUM_SHIFT	(0)
#define		GLB_MTQ24_QOS_NEXE_EVT_NUM_MASK		BIT_MASK(8)

#define R32_FCON_MTQ_QOS_INF_7              (0x320 >> 2)
#define		GLB_MTQ31_QOS_NEXE_EVT_NUM_SHIFT	(24)
#define		GLB_MTQ31_QOS_NEXE_EVT_NUM_MASK		BIT_MASK(8)
#define		GLB_MTQ30_QOS_NEXE_EVT_NUM_SHIFT	(16)
#define		GLB_MTQ30_QOS_NEXE_EVT_NUM_MASK		BIT_MASK(8)
#define		GLB_MTQ29_QOS_NEXE_EVT_NUM_SHIFT	(8)
#define		GLB_MTQ29_QOS_NEXE_EVT_NUM_MASK		BIT_MASK(8)
#define		GLB_MTQ28_QOS_NEXE_EVT_NUM_SHIFT	(0)
#define		GLB_MTQ28_QOS_NEXE_EVT_NUM_MASK		BIT_MASK(8)

#define R32_FCON_MT_QOS_STS                 (0x34C >> 2)
#define R32_FCON_MT_QOS_STS_1               (0x350 >> 2)
#define R32_FCON_MT_QOS_STS_2               (0x354 >> 2)
#define R32_FCON_MT_QOS_STS_3               (0x358 >> 2)

#define R32_FCON_FPU_ADR_BASE               (0x35C >> 2)
#define		MT_FPU_ADDR_IRAM_SHIFT				(0)
#define		MT_FPU_ADDR_IRAM_MASK				BIT_MASK(17)

#define R32_FCON_GC_FW_BASE                 (0x360 >> 2)
#define		GC_FW_BACKUP_EN_BIT					BIT17
#define		GC_FW_BASE_ADDR_SHIFT				(0)
#define		GC_FW_BASE_ADDR_MASK				BIT_MASK(17)
#define		GC_FW_BASE_ADDR_SHIFT_MASK			(GC_FW_BASE_ADDR_MASK << GC_FW_BASE_ADDR_SHIFT)
#define		M_SET_GC_FW_BASE_ADDR(n)			(((n) << GC_FW_BASE_ADDR_SHIFT) & GC_FW_BASE_ADDR_SHIFT_MASK)

#define R32_FCON_QOS_TIMEOUT_CFG            (0x364 >> 2)
#define		QOS_SPD_LMT_SHIFT					(26)
#define		QOS_SPD_LMT_MASK					BIT_MASK(6)
#define		QOS_SPD_LMT_EN_BIT					BIT25
#define		QOS_TO_EN_BIT						BIT24
#define		QOS_TO_CNT_SHIFT					(0)
#define		QOS_TO_CNT_MASK						BIT_MASK(23)

#define R32_FCON_SPD_STATUS_CFG             (0x368 >> 2)
#define		NAND_SUPPORT_SPD_BIT				BIT31
#define		QOS_ITLV_EN_BIT						BIT30
#define		RSM_AP_MODE_BIT						BIT29
#define		ROW_FF_EN_BIT						BIT28
#define		SPDCHK_APOL_SEL_SHIFT				(24)
#define		SPDCHK_APOL_SEL_MASK				BIT_MASK(4)
#define		SPDCHK_APOL_SEL_SHIFT_MASK			(SPDCHK_APOL_SEL_MASK << SPDCHK_APOL_SEL_SHIFT)    // DO NOT change H/W seting.
#define		RSMCHK_APOL_SEL_SHIFT				(20)
#define		RSMCHK_APOL_SEL_MASK				BIT_MASK(4)
#define		SPD_SUCCESS_BIT_SEL_SHIFT			(17)
#define		SPD_SUCCESS_BIT_SEL_MASK			BIT_MASK(3)
#define		SPD_SUCCESS_BIT_SEL_SHIFT_MASK		(SPD_SUCCESS_BIT_SEL_MASK << SPD_SUCCESS_BIT_SEL_SHIFT)
#define		SPD_SUCCESS_BIT_VAL_BIT				BIT16
#define		SPD_FAIL_MSK_SHIFT					(8)
#define		SPD_FAIL_MSK_MASK					BIT_MASK(8)
#define		SPD_FAIL_MSK_SHIFT_MASK				(SPD_FAIL_MSK_MASK << SPD_FAIL_MSK_SHIFT)
#define		SPD_FAIL_STS_SHIFT					(0)
#define		SPD_FAIL_STS_MASK					BIT_MASK(8)
#define		SPD_FAIL_STS_SHIFT_MASK				(SPD_FAIL_STS_MASK << SPD_FAIL_STS_SHIFT)

#define R32_FCON_ZQCAL_CFG					(0x36C >> 2)
#define     M_GET_ZQCAL_BSY(x)                  (x & BIT31)
#define     ZQCAL_TMR_TH_H_SHIFT                (24)
#define     ZQCAL_TMR_TH_H_MASK                 BIT_MASK(7)
#define		ZQCAL_CLK_EN_BIT					BIT21
#define		LOAD_ZQ_AUTO_BIT					BIT20
#define		ZQCAL_EN_BIT						BIT17
#define		ZQCAL_FW_RSTJ_BIT					BIT16
#define		LOAD_ZQ_PHS_CNT_SHIFT				(12)
#define		LOAD_ZQ_PHS_CNT_MASK				BIT_MASK(4)
#define		ZQCAL_TMR_TH_SHIFT					(0)
#define		ZQCAL_TMR_TH_MASK					BIT_MASK(12)

#define R32_FCON_POL_SEQ_0                  (0x380 >> 2)
#define		FCON_POL_SEQ_1_SHIFT				(16)
#define		FCON_POL_SEQ_1_MASK					BIT_MASK(16)
#define		FCON_POL_SEQ_0_SHIFT				(0)
#define		FCON_POL_SEQ_0_MASK					BIT_MASK(16)
#define	R8_FCONB_POL_SEQ_0                  (0x380 >> 0)
#define		M_FCON_POL_SEQ_BASE(x)				(FLH_TOP_REG_BASE + R8_FCONB_POL_SEQ_0 + (2 * (x)))
#define		M_FCON_POL_SEQ(x)					*((volatile U16 *) M_FCON_POL_SEQ_BASE(x))

#define R32_FCON_POL_SEQ_2                  (0x384 >> 2)
#define		FCON_POL_SEQ_3_SHIFT				(16)
#define		FCON_POL_SEQ_3_MASK					BIT_MASK(16)
#define		FCON_POL_SEQ_2_SHIFT				(0)
#define		FCON_POL_SEQ_2_MASK					BIT_MASK(16)

#define R32_FCON_POL_SEQ_4                  (0x388 >> 2)
#define		FCON_POL_SEQ_5_SHIFT				(16)
#define		FCON_POL_SEQ_5_MASK					BIT_MASK(16)
#define		FCON_POL_SEQ_4_SHIFT				(0)
#define		FCON_POL_SEQ_4_MASK					BIT_MASK(16)

#define R32_FCON_POL_SEQ_6                  (0x38C >> 2)
#define		FCON_POL_SEQ_7_SHIFT				(16)
#define		FCON_POL_SEQ_7_MASK					BIT_MASK(16)
#define		FCON_POL_SEQ_6_SHIFT				(0)
#define		FCON_POL_SEQ_6_MASK					BIT_MASK(16)

#define R32_FCON_POL_SEQ_8                  (0x390 >> 2)
#define		FCON_POL_SEQ_9_SHIFT				(16)
#define		FCON_POL_SEQ_9_MASK					BIT_MASK(16)
#define		FCON_POL_SEQ_8_SHIFT				(0)
#define		FCON_POL_SEQ_8_MASK					BIT_MASK(16)

#define R32_FCON_POL_SEQ_A                  (0x394 >> 2)
#define		FCON_POL_SEQ_11_SHIFT				(16)
#define		FCON_POL_SEQ_11_MASK				BIT_MASK(16)
#define		FCON_POL_SEQ_10_SHIFT				(0)
#define		FCON_POL_SEQ_10_MASK				BIT_MASK(16)

#define R32_FCON_POL_SEQ_C                  (0x398 >> 2)
#define		FCON_POL_SEQ_13_SHIFT				(16)
#define		FCON_POL_SEQ_13_MASK				BIT_MASK(16)
#define		FCON_POL_SEQ_12_SHIFT				(0)
#define		FCON_POL_SEQ_12_MASK				BIT_MASK(16)

#define R32_FCON_POL_SEQ_E                  (0x39C >> 2)
#define		FCON_POL_SEQ_15_SHIFT				(16)
#define		FCON_POL_SEQ_15_MASK				BIT_MASK(16)
#define		FCON_POL_SEQ_14_SHIFT				(0)
#define		FCON_POL_SEQ_14_MASK				BIT_MASK(16)

#define R32_FCON_LDPC_SBC                   (0x3A0 >> 2)
#define		SBC_FINISH_SHIFT					(24)
#define		SBC_FINISH_MASK						BIT_MASK(8)
#define		SBC_SUCCEED_BIT						BIT23
#define		SBC_BFC_CNT_SHIFT					(8)
#define		SBC_BFC_CNT_MASK					BIT_MASK(15)
#define		SBC_BFC_CNT_SHIFT_MASK				(SBC_BFC_CNT_MASK << SBC_BFC_CNT_SHIFT)
#define		SBC_BSY_SHIFT						(0)
#define		SBC_BSY_MASK						BIT_MASK(2) //E21 8 to 2
#define		SBC_BSY_SHIFT_MASK				    SBC_BSY_MASK << SBC_BSY_SHIFT)

#define R32_FCON_LDPC_SBC2					(0x3A4 >> 2)
#define		SBC_PTY_INI_SHIFT					(8)
#define		SBC_PTY_INI_MASK					BIT_MASK(12)
#define		SBC_PTY_INI_SHIFT_MASK				(SBC_PTY_INI_MASK << SBC_PTY_INI_SHIFT)
#define		SBC_ITER_STOP_SHIFT					(0)
#define		SBC_ITER_STOP_MASK					BIT_MASK(8)
#define		SBC_ITER_STOP_SHIFT_MASK			(SBC_ITER_STOP_MASK << SBC_ITER_STOP_SHIFT)

#define R32_FCON_LDPC_THRS4                 (0x3A8 >> 2)
#define     LDPC_HB_LLR_TS_SHIFT                (16)
#define     LDPC_HB_LLR_TS_MASK                 BIT_MASK(4)
#define     LDPC_TSRCV_ENABLE                   BIT12
#define     LDPC_TS_THRS_SHIFT                  (0)
#define     LDPC_TS_THRS_MASK                   BIT_MASK(12)

#define R32_FCON_IPERR_INF0                 (0x3AC >> 2)
#define		IPERR_CH_SEL_SHIFT					(29)
#define		IPERR_CH_SEL_MASK					BIT_MASK(2) //E21 3 to 2
#define		IPERR_CH_SEL_SHIFT_MASK				(IPERR_CH_SEL_MASK << IPERR_CH_SEL_SHIFT)
#define		IPERR_PFA_MT_IDX_SHIFT				(0)
#define		IPERR_PFA_MT_IDX_MASK				BIT_MASK(7) //E21 9 to 7
#define		IPERR_PFA_MT_IDX_SHIFT_MASK			(IPERR_PFA_MT_IDX_MASK << IPERR_PFA_MT_IDX_SHIFT)

#define R32_FCON_IPERR_INF1                 (0x3B0 >> 2)
#define		MT_IPERR_ADDR_SHIFT					(9)
#define		MT_IPERR_ADDR_MASK					BIT_MASK(16) //E21 17 to 16
#define		MT_IDX_SHIFT						(0)
#define		MT_IDX_MASK							BIT_MASK(7)
#define		MT_IDX_SHIFT_MASK					(MT_IDX_MASK << MT_IDX_SHIFT)

#define R32_FCON_LCH2PCH_TABLE              (0x3B4 >> 2)
#define		LCH2PCH_7_SHIFT						(28)
#define		LCH2PCH_7_MASK						BIT_MASK(4)
#define		LCH2PCH_6_SHIFT						(24)
#define		LCH2PCH_6_MASK						BIT_MASK(4)
#define		LCH2PCH_5_SHIFT						(20)
#define		LCH2PCH_5_MASK						BIT_MASK(4)
#define		LCH2PCH_4_SHIFT						(16)
#define		LCH2PCH_4_MASK						BIT_MASK(4)
#define		LCH2PCH_3_SHIFT						(12)
#define		LCH2PCH_3_MASK						BIT_MASK(4)
#define		LCH2PCH_2_SHIFT						(8)
#define		LCH2PCH_2_MASK						BIT_MASK(4)
#define		LCH2PCH_1_SHIFT						(4)
#define		LCH2PCH_1_MASK						BIT_MASK(4)
#define		LCH2PCH_0_SHIFT						(0)
#define		LCH2PCH_0_MASK						BIT_MASK(4)

#define R32_FCON_RAIDECC_GROUP_PB_CFG_0		(0x3B8 >> 2)
#define		RAIDECC_GRP_3_PB_NUM_SHIFT			(24)
#define		RAIDECC_GRP_3_PB_NUM_MASK			BIT_MASK(5)
#define		RAIDECC_GRP_2_PB_NUM_SHIFT			(16)
#define		RAIDECC_GRP_2_PB_NUM_MASK			BIT_MASK(5)
#define		RAIDECC_GRP_1_PB_NUM_SHIFT			(8)
#define		RAIDECC_GRP_1_PB_NUM_MASK			BIT_MASK(5)
#define		RAIDECC_GRP_0_PB_NUM_SHIFT			(0)
#define		RAIDECC_GRP_0_PB_NUM_MASK			BIT_MASK(5)

#define R32_FCON_RAIDECC_GROUP_PB_CFG_1		(0x3BC >> 2)
#define		RAIDECC_GRP_7_PB_NUM_SHIFT			(24)
#define		RAIDECC_GRP_7_PB_NUM_MASK			BIT_MASK(5)
#define		RAIDECC_GRP_6_PB_NUM_SHIFT			(16)
#define		RAIDECC_GRP_6_PB_NUM_MASK			BIT_MASK(5)
#define		RAIDECC_GRP_5_PB_NUM_SHIFT			(8)
#define		RAIDECC_GRP_5_PB_NUM_MASK			BIT_MASK(5)
#define		RAIDECC_GRP_4_PB_NUM_SHIFT			(0)
#define		RAIDECC_GRP_4_PB_NUM_MASK			BIT_MASK(5)

#define R32_FCON_MT_CFG_1                  (0x3C0 >> 2)
#define     POL_SEQ_SEL_EXT_1                   (31)
#define     POL_SEQ_SEL_EXT_0                   (16)
#define     POL_SEQ_SEL_EXT_MASK                BIT_MASK(1)
#define     POL_SEQ_SEL_EXT_MASK_ALL            ((POL_SEQ_SEL_EXT_MASK << POL_SEQ_SEL_EXT_0) | (POL_SEQ_SEL_EXT_MASK << POL_SEQ_SEL_EXT_1))
#define     M_SET_POL_SEQ_SEL_EXT(X)            (((X & POL_SEQ_SEL_EXT_MASK) << (POL_SEQ_SEL_EXT_0)) + (((X >> 1) & POL_SEQ_SEL_EXT_MASK) << (POL_SEQ_SEL_EXT_1)))
#define     EOT_SEL_SHIFT                       (29)
#define     EOT_SEL_MASK                        BIT_MASK(2)
#define     MT_LDPC_MODE_SHIFT                     (25)
#define     MT_LDPC_MODE_MASK                      BIT_MASK(4)
#define     DIE_IDX_SHIFT                       (22)
#define     DIE_IDX_MASK                        BIT_MASK(2)
#define     MT_DLY_CNT_SEL_SHIFT                (18)
#define     MT_DLY_CNT_SEL_MASK                 BIT_MASK(4)
#define     BOOKING_CHK_BPS                     BIT17
#define		GRP1_SHIFT							(13)
#define		GRP1_MASK							BIT_MASK(3)
#define		L4K_NUM1_SHIFT						(8)
#define		L4K_NUM1_MASK						BIT_MASK(5)
#define		GRP0_SHIFT							(5)
#define		GRP0_MASK							BIT_MASK(3)
#define		L4K_NUM0_SHIFT						(0)
#define		L4K_NUM0_MASK						BIT_MASK(5)

#define R32_FCON_LDPC_THRS                  (0x3C4 >> 2)
#define     LDPC_SYNSUM_THRS_TRMT_SHITF         (12)
#define     LDPC_SYNSUM_THRS_TRMT_MASK          BIT_MASK(12)
#define     LDPC_SYNSUM_THRS_MS_SHITF           (0)
#define     LDPC_SYNSUM_THRS_MS_MASK            BIT_MASK(12)

#define R32_FCON_IOR_CFG					(0x3C8 >> 2)
#define		IDX_VAL_SHIFT						(0)
#define		IDX_VAL_MASK						BIT_MASK(3)

#define	R32_FCON_IOR_CFG_1					(0x3CC >> 2)
#define 	CHK_GRP_SIZE_FIFO_ALL_FULL_BIT		BIT24
#define		GRP_SIZE_FIFO_FULL_SHIFT			(24)
#define		GRP_SIZE_FIFO_FULL_MASK				BIT_MASK(2)
#define		FW_GRP_SIZE_SHIFT					(8)
#define		FW_GRP_SIZE_MASK					BIT_MASK(9)
#define		FW_GRP_IDX_SHIFT					(0)
#define		FW_GRP_IDX_MASK						BIT_MASK(3)

#define	R8_FCON_IOR_CFG_1_FIFO_EMPTY		(0x3CF)

#define	R32_FCON_DSP2_TBL					(0x3D0 >> 2)

#define R32_FCON_IOR_CFG_2					(0x3D4 >> 2)
#define		BK_STATE_SHIFT						(25)
#define		BK_STATE_MASK						BIT_MASK(5)
#define		N_POLARITY_BIT						BIT24
#define		TB_REQ_MODE_SHIFT					(18)
#define		TB_REQ_MODE_MASK					BIT_MASK(2)
#define		IDX_SEL_SHIFT						(16)
#define		IDX_SEL_MASK						BIT_MASK(2)
#define		IOR_CUR_GRP_IDX						(0)
#define		IOR_CUR_TABLE_IDX					(1)
#define		IOR_CUR_TAIL_IDX					(2)
#define		BOOKING_TABLE_IDX_SHIFT				(13)
#define		BOOKING_TABLE_IDX_MASK				BIT_MASK(3)
#define		BOOKING_TABLE_SEL_SHIFT				(10)
#define		BOOKING_TABLE_SEL_MASK				BIT_MASK(3)
#define		LB_OFS_RING_THR_SHIFT				(0)
#define		LB_OFS_RING_THR_MASK				BIT_MASK(10)

#define R32_FCON_LDPC_THRS1				    (0x3D8 >> 2)
#define     LDPC_MSA_ITER_SHIFT                 (24)
#define     LDPC_MSA_ITER_MASK                  BIT_MASK(8)
#define     LDPC_SYNSUM_THRS_GDBF_SHIFT         (12)
#define     LDPC_SYNSUM_THRS_GDBF_MASK          BIT_MASK(12)
#define     LDPC_SYNSUM_THRS_FHB_SHIFT          (0)
#define     LDPC_SYNSUM_THRS_FHB_MASK           BIT_MASK(12)
#define     M_SET_LDPC_THRS1(FHB_THRS, GDBF_THRS)      (R32_FCON[R32_FCON_LDPC_THRS1] |= ((GDBF_THRS << LDPC_SYNSUM_THRS_GDBF_SHIFT) | (FHB_THRS << LDPC_SYNSUM_THRS_FHB_SHIFT)))
#define     M_CLR_LDPC_THRS()                          (R32_FCON[R32_FCON_LDPC_THRS1] &= ~((LDPC_SYNSUM_THRS_GDBF_MASK << LDPC_SYNSUM_THRS_GDBF_SHIFT) | (LDPC_SYNSUM_THRS_FHB_MASK << LDPC_SYNSUM_THRS_FHB_SHIFT))) //set  LDPC mode[2:0]
#define     M_CLR_LDPC_MSA_ITER_THRS()                 (R32_FCON[R32_FCON_LDPC_THRS1] &= ~(LDPC_MSA_ITER_MASK << LDPC_MSA_ITER_SHIFT))
#define     M_SET_LDPC_MSA_ITER_THRS(MSA_ITER_THRS)    (R32_FCON[R32_FCON_LDPC_THRS1] |= (MSA_ITER_THRS & LDPC_MSA_ITER_MASK) << LDPC_MSA_ITER_SHIFT)

#define R32_FCON_LDPC_THRS2				    (0x3DC >> 2)
#define     LDPC_REMAP_THRS2_OFFSET             (24)
#define     LDPC_REMAP_THRS2_MASK               BIT_MASK(5)
#define     LDPC_REMAP_THRS1_OFFSET             (16)
#define     LDPC_REMAP_THRS1_MASK               BIT_MASK(5)
#define     LDPC_ABS_APP_THRS_OFFSET            (11)
#define     LDPC_ABS_APP_THRS_MASK              BIT_MASK(5)
#define     LDPC_CONV_THRS_OFFSET               (0)
#define     LDPC_CONV_THRS_MASK                 BIT_MASK(11)
#define     M_CLR_LDPC_CONV_THRS()                    (R32_FCON[R32_FCON_LDPC_THRS2] &= ~(LDPC_CONV_THRS_MASK << LDPC_CONV_THRS_OFFSET))
#define     M_SET_LDPC_CONV_THRS(CONV_THRS)           (R32_FCON[R32_FCON_LDPC_THRS2] |= ((CONV_THRS & LDPC_CONV_THRS_MASK) << LDPC_CONV_THRS_OFFSET ))
#define     M_CLR_LDPC_ABS_APP_THRS()                 (R32_FCON[R32_FCON_LDPC_THRS2] &= ~(LDPC_ABS_APP_THRS_MASK << LDPC_ABS_APP_THRS_OFFSET))
#define     M_SET_LDPC_ABS_APP_THRS(ABS_APP_THRS)     (R32_FCON[R32_FCON_LDPC_THRS2] |= ((ABS_APP_THRS & LDPC_ABS_APP_THRS_MASK) << LDPC_ABS_APP_THRS_OFFSET))


#define R32_FCON_LDPC_THRS3                 (0x3E0 >> 2)
#define     LDPC_REMAP_OFFSET3_OFFSET           (16)
#define     LDPC_REMAP_OFFSET3_MASK             BIT_MASK(5)
#define     LDPC_REMAP_OFFSET2_OFFSET           (8)
#define     LDPC_REMAP_OFFSET2_MASK             BIT_MASK(5)
#define     LDPC_REMAP_OFFSET1_OFFSET           (0)
#define     LDPC_REMAP_OFFSET1_MASK             BIT_MASK(5)

#define R32_FCON_IOR_CFG_3					(0x3E4 >> 2)
#define		BOOKING_TABLE_VAL_SHIFT				(0)
#define		BOOKING_TABLE_VAL_MASK				BIT_MASK(9)

#define R32_FCON_IOR_CFG_4					(0x3E8 >> 2)
#define		ALCPBL_PHS_BIT						BIT30
#define		NODMA_TO_CH_SHIFT					(24)
#define		NODMA_TO_CH_MASK					BIT_MASK(4)
#define		GRP_STS_SHIFT						(16)
#define		GRP_STS_MASK						BIT_MASK(8)
#define		NODMA_TO_CNT_SHIFT					(12)
#define		NODMA_TO_CNT_MASK					BIT_MASK(4)
#define		NODMA_TERMINATE_BIT					BIT9
#define		RESET_IOR_STATE_BIT					BIT8
#define		RETURN_BOOKING_BIT					BIT0

#define R32_FCON_IOR_CFG_5					(0x3EC >> 2)
#define		ALCPBL_TARGET_ID_SHIFT				(4)
#define		ALCPBL_TARGET_ID_MASK				BIT_MASK(4)
#define		BOOKING_TARGET_ID_SHIFT				(0)
#define		BOOKING_TARGET_ID_MASK				BIT_MASK(4)

#define R32_FCON_MT_PRIO_INFO				(0x3F0 >> 2)
#define		PRIO_VLD_OPTION_BIT					BIT14
#define		RS_RDY_OPTION_BIT					BIT13
#define		LBOFS_MODE_BIT						BIT12
#define		SOFT_LOCK_OR_BIT					BIT11
#define		CHS_SMQ_BIT							BIT10
#define		ATP_UPG_BIT							BIT9
#define		FW_PRIO_RULE_EN_BIT					BIT8
#define		PRIO_WAIT_CNT_SHIFT					(0)
#define		PRIO_WAIT_CNT_MASK					BIT_MASK(8)

#define R32_FCON_SMP_FLAG					(0x3F4 >> 2)

#define	R32_FCON_PARITY_ERR_FLAG_0			(0x3F8 >> 2)
#define		PITO_FLAG_SHIFT		                (28)
#define		PITO_FLAG_MASK		                BIT_MASK(4)
#define		IRAM_PARITY_ERR_FLAG_SHIFT			(0)
#define		IRAM_PARITY_ERR_FLAG_MASK			BIT_MASK(3)
#define		IBF_PARITY_ERR_FLAG_SHIFT			(0)
#define		IBF_PARITY_ERR_FLAG_MASK			BIT_MASK(16)

#define R32_FCON_PARITY_ERR_FLAG_1			(0x3FC >> 2)
#define		PARITY_ERR_SRAM_SEL_SHIFT			(19)
#define		PARITY_ERR_SRAM_SEL_MASK			BIT_MASK(5)
#define		AXI_RRESP_ERR_FLAG_SHIFT			(12)
#define		AXI_RRESP_ERR_FLAG_MASK				BIT_MASK(4)
#define		AXI_BPESP_ERR_FLAG_SHIFT			(8)
#define		AXI_BPESP_ERR_FLAG_MASK				BIT_MASK(4)
#define		LDPC_A_MEM_PARITY_ERR_FLAG_SHIFT	(6)
#define		LDPC_A_MEM_PARITY_ERR_FLAG_MASK		BIT_MASK(2)
#define		LDPC_F_MEM_PARITY_ERR_FLAG_SHIFT	(4)
#define		LDPC_F_MEM_PARITY_ERR_FLAG_MASK		BIT_MASK(2)
#define		LDPC_O_MEM_PARITY_ERR_FLAG_SHIFT	(2)
#define		LDPC_O_MEM_PARITY_ERR_FLAG_MASK		BIT_MASK(2)
#define		LDPC_I_MEM_PARITY_ERR_FLAG_SHIFT	(0)
#define		LDPC_I_MEM_PARITY_ERR_FLAG_MASK		BIT_MASK(2)

#define R32_FCON_PARITY_ERR_ADR             (0x400 >> 2)
#define		PARITY_ERR_ADR_SHIFT			    (0)
#define		PARITY_ERR_ADR_MASK			        BIT_MASK(17)

#define R32_FCON_MTQ_DLY_0                  (0x404 >> 2)
#define R32_FCON_MTQ_DLY_1                  (0x408 >> 2)
#define R32_FCON_MTQ_DLY_2                  (0x40C >> 2)
#define R32_FCON_MTQ_DLY_3                  (0x410 >> 2)
#define R32_FCON_MTQ_DLY_4                  (0x414 >> 2)
#define R32_FCON_MTQ_DLY_5                  (0x418 >> 2)
#define R32_FCON_MTQ_DLY_6                  (0x41C >> 2)
#define R32_FCON_MTQ_DLY_7                  (0x420 >> 2)
#define R32_FCON_MTQ_DLY_8                  (0x424 >> 2)
#define R32_FCON_MTQ_DLY_9                  (0x428 >> 2)
#define R32_FCON_MTQ_DLY_10                 (0x42C >> 2)
#define R32_FCON_MTQ_DLY_11                 (0x430 >> 2)
#define R32_FCON_MTQ_DLY_12                 (0x434 >> 2)
#define		MTQ_DELAY_SHIFT			            (0)
#define		MTQ_DELAY_MASK			            BIT_MASK(24)

#define R32_FCON_MTQ_DLY_13                 (0x438 >> 2)
#define R32_FCON_MTQ_DLY_14                 (0x43C >> 2)
#define R32_FCON_MTQ_DLY_15                 (0x440 >> 2)
#define		CE_OFF_DELAY_SHIFT			        (0)
#define		CE_OFF_DELAY_MASK			        BIT_MASK(24)

#define R32_FCON_SDLL_OFS_CFG_0             (0x444 >> 2)
#define     SOFS_BASE_SHIFT                     (0)
#define     SOFS_BASE_MASK                      BIT_MASK(17)
#define     SOFS_IRAM_EN                        BIT17

#define R32_FCON_PAD_DLY_CFG_0              (0x448 >> 2)
#define     PDLY_BASE_SHIFT                     (0)
#define     PDLY_BASE_MASK                      BIT_MASK(17)
#define     PDLY_IRAM_EN                        BIT17
#define		PDLY_TBL_SEL						BIT18
#define     PSSEL_MODE_SHIFT                    (20)
#define     PSSEL_MODE_MASK                     BIT_MASK(2)
#define     DIE_MODE_SHIFT                      (22)
#define     DIE_MODE_MASK                       BIT_MASK(2)
#define     TOTAL_CH_DIE_NUM_SHIFT              (24)
#define     TOTAL_CH_DIE_NUM_MASK               BIT_MASK(6)
#define		CE_GRANT_ERR						BIT30

#define R32_FCON_SDLL_OFS_CFG_1             (0x450 >> 2)
#define     SOFS_BASE_SHIFT                     (0)
#define     SOFS_BASE_MASK                      BIT_MASK(17)
#define     SOFS_IRAM_EN                        BIT17

#define R32_FCON_PAD_DLY_CFG_1              (0x454 >> 2)
#define     PDLY_BASE_SHIFT                     (0)
#define     PDLY_BASE_MASK                      BIT_MASK(17)
#define     PDLY_IRAM_EN                        BIT17

#define R32_FCON_LDPC_CFG_2                 (0x460 >> 2)
#define     SETUP_VALID                         (BIT6)

#define R32_FCON_CE_DECEN_SET               (0x464 >> 2)
#define R32_FCON_EOT_CFG                    (0x468 >> 2)
#define R32_FCON_NON_SLC_PFX_CFG            (0x46C >> 2)
#define R32_FCON_LUN_ADR_CFG                (0x470 >> 2)
#define R32_FCON_PL_0_RR_FA_CFG             (0x474 >> 2)
#define R32_FCON_PL_1_RR_FA_CFG             (0x478 >> 2)
#define R32_FCON_PL_2_RR_FA_CFG             (0x47C >> 2)
#define R32_FCON_PL_3_RR_FA_CFG             (0x480 >> 2)
#define R32_FCON_PL_4_RR_FA_CFG             (0x484 >> 2)
#define R32_FCON_PL_5_RR_FA_CFG             (0x488 >> 2)
#define R32_FCON_RR_SLC_PFX_FA_CFG          (0x48C >> 2)
#define R32_FCON_RR_SLC_FA_CFG              (0x490 >> 2)
#define R32_FCON_POTO_CFG                   (0x494 >> 2)
#define R32_FCON_LDPC_DBG_CFG               (0x498 >> 2)
#define R32_FCON_LDPC_DBG_DAT               (0x49C >> 2)
#define R32_FCON_LDPC_DBG_TRG               (0x4A0 >> 2)
#define R32_FCON_ODT_WP_CFG                 (0x4A4 >> 2)
#define R32_FCON_POL_SEQ_10                 (0x4A8 >> 2)
#define R32_FCON_POL_SEQ_12                 (0x4AC >> 2)
#define R32_FCON_POL_SEQ_14                 (0x4B0 >> 2)
#define R32_FCON_POL_SEQ_16                 (0x4B4 >> 2)
#define R32_FCON_POL_SEQ_18                 (0x4B8 >> 2)
#define R32_FCON_POL_SEQ_1A                 (0x4BC >> 2)
#define R32_FCON_POL_SEQ_1C                 (0x4C0 >> 2)
#define R32_FCON_POL_SEQ_1E                 (0x4C4 >> 2)
#define R32_FCON_POL_SEQ_20                 (0x4C8 >> 2)
#define R32_FCON_POL_SEQ_22                 (0x4CC >> 2)
#define R32_FCON_POL_SEQ_24                 (0x4D0 >> 2)
#define R32_FCON_POL_SEQ_26                 (0x4D4 >> 2)
#define R32_FCON_POL_SEQ_28                 (0x4D8 >> 2)
#define R32_FCON_POL_SEQ_2A                 (0x4DC >> 2)
#define R32_FCON_POL_SEQ_2C                 (0x4E0 >> 2)
#define R32_FCON_POL_SEQ_2E                 (0x4E4 >> 2)
#define R32_FCON_POL_SEQ_30                 (0x4E8 >> 2)
#define R32_FCON_POL_SEQ_32                 (0x4EC >> 2)
#define R32_FCON_POL_SEQ_34                 (0x4F0 >> 2)
#define R32_FCON_POL_SEQ_36                 (0x4F4 >> 2)
#define R32_FCON_POL_SEQ_38                 (0x4F8 >> 2)
#define R32_FCON_POL_SEQ_3A                 (0x4FC >> 2)
#define R32_FCON_POL_SEQ_3C                 (0x500 >> 2)
#define R32_FCON_POL_SEQ_3E                 (0x504 >> 2)
#define     POL_SEQ_B_SHIFT                     (16)
#define     POL_SEQ_A_SHIFT                     (0)
#define     POL_SEQ_MASK                        BIT_MASK(16)

//=============================================================================
/* Register Offset Definitions of Control register of each Channels */
//=============================================================================
#define R32_FCTL_PIO_CMD                    (0x00 >> 2)
#define		DIRECT_PIO_CMD_FLH_INF_SHIFT		(0)
#define		DIRECT_PIO_CMD_FLH_INF_MASK			BIT_MASK(8)

#define R32_FCTL_PIO_ADR                    (0x04 >> 2)
#define R8_FCTL_PIO_ADR			            (0x04)
#define		DIRECT_PIO_ADR_FLH_INF_SHIFT		(0)
#define		DIRECT_PIO_ADR_FLH_INF_MASK			BIT_MASK(8)

#define R32_FCTL_PIO_DAT                    (0x08 >> 2)
#define		DIRECT_PIO_DATA_FLH_INF_SHIFT		(0)
#define		DIRECT_PIO_DATA_FLH_INF_MASK		BIT_MASK(16)

#define R32_FCTL_SPD_INF_CFG                (0x0C >> 2)
#define R32_FCTL_PROG_SPD_BLK_ADR           (0x10 >> 2)
#define R32_FCTL_ERASE_SPD_BLK_ADR          (0x14 >> 2)
#define R32_FCTL_SPD_CNT                    (0x18 >> 2)

#define R8_FCTL_IO_SET                     (0x1C)
#define R32_FCTL_IO_SET                     (0x1C >> 2)
#define		FLH_WP_BIT							BIT31
#define		CLR_FLH_WP_L						CLR_BIT31
#define		FLH_PHY_RST_BIT						BIT30
#define     LBK_BIST_FAIL                       BIT29
#define     LBK_BIST_START                      BIT28
#define     LBK_BIST_EN                         BIT27
#define		RB_NEG_EDGE_MONITOR_BIT				BIT26
#define		IO_PAD_TYPE_SEL_BIT					BIT25
#define		FORCE_FLH_ALE_SIG_HIGH_BIT			BIT24
#define		FORCE_FLH_CLE_SIG_HIGH_BIT			BIT23
#define		FORCE_FLH_WEN_SIG_LOW_BIT			BIT22
#define		FORCE_FLH_REN_SIG_LOW_BIT			BIT21
#define		FLH_HIGH_BUS_OUTPUT_MODE_EN			BIT20
#define		FLH_LOW_BUS_OUTPUT_MODE_EN			BIT19
#define     LBK_BIST_DBG_BYT_SEL_SHIFT          (17)
#define     LBK_BIST_DBG_BYT_SEL_MASK           BIT_MASK(2)
#define     LBK_BIST_DONE                       BIT16
#define		FLH_IO_H_BUS_GPO_SHIFT				(8)
#define		FLH_IO_H_BUS_GPO_MASK				BIT_MASK(8)
#define		FLH_IO_L_BUS_GPO_SHIFT				(0)
#define		FLH_IO_H_BUS_GPO_MASK				BIT_MASK(8)

#define R32_FCTL_ZIP_CFG                    (0x20 >> 2)
#define     WDMA_MSK_FLH_IO                     BIT31
#define     BMU_FAR_SHIFT                       (24)
#define     BMU_FAR_MASK                        BIT(4)
#define		STOP_QRY_BIT16						BIT16
#define		FIP_QRY_BSY_BIT						BIT12
#define		UP_CRC16_BIT						BIT11
#define		UP_CRC8_BIT							BIT10
#define		UP_SPRV_BIT							BIT9
#define		UP_E2E4K_BIT						BIT8
#define		SGN_DIS_SHIFT						(0)
#define		SGN_DIS_MASK						BIT_MASK(4)

#define R8_FCTL_ZIP_CFG_STOP_QRY			(0x22)
#define		STOP_QRY_BIT0						BIT0

#define R32_FCTL_MT_TRIG                    (0x24 >> 2)
#define		MTP_RLD_SGN_BIT31					BIT31
#define		QUEUE_7_TO_0_ST_SHIFT				(16)
#define		QUEUE_7_TO_0_ST_MASK				BIT_MASK(8)
#define 	CHK_QUEUE_7_TO_0_MT_BUSY_MASK		(QUEUE_7_TO_0_ST_MASK << QUEUE_7_TO_0_ST_SHIFT)
#define		CLEAR_QUEUE_7_TO_0_SHIFT      		(8)
#define		CLEAR_QUEUE_7_TO_0_MASK				BIT_MASK(8)
#define		CLEAR_QUEUE_7_TO_0_SHIFT_MASK		(CLEAR_QUEUE_7_TO_0_MASK << CLEAR_QUEUE_7_TO_0_SHIFT)
#define		MTQ_7_TO_0_PUSH_SHIFT				(0)
#define		MTQ_7_TO_0_PUSH_MASK				BIT_MASK(8)

#define R8_FCTL_MT_TRIG						(0x27)
#define		MTP_RLD_SGN_BIT7				    BIT7

#define R32_FCTL_MT_ADR_0                   (0x28 >> 2)
#define		MTP_SMP_REQ_0_BIT					BIT21
#define		MTP_FRC_EMP_RQS_0_EN_BIT			BIT20
#define		MTP_DLY_0_EN_BIT					BIT19
#define		MTP_LOCK_STATUS_0_EN_BIT			BIT18
#define		MTP_GRP_PRIO_DFI_0_BIT				BIT17
#define		MT_PAR_ADR_0_SHIFT					(0)
#define		MT_PAR_ADR_0_MASK					BIT_MASK(17)

#define R32_FCTL_MT_ADR_1                   (0x2C >> 2)
#define		MTP_SMP_REQ_1_BIT					BIT21
#define		MTP_FRC_EMP_RQS_1_EN_BIT			BIT20
#define		MTP_DLY_1_EN_BIT					BIT19
#define		MTP_LOCK_STATUS_1_EN_BIT			BIT18
#define		MTP_GRP_PRIO_DFI_1_BIT				BIT17
#define		MT_PAR_ADR_1_SHIFT					(0)
#define		MT_PAR_ADR_1_MASK					BIT_MASK(17)

#define R32_FCTL_MT_ADR_2                   (0x30 >> 2)
#define		MTP_SMP_REQ_3_BIT					BIT21
#define		MTP_FRC_EMP_RQS_3_EN_BIT			BIT20
#define		MTP_DLY_3_EN_BIT					BIT19
#define		MTP_LOCK_STATUS_3_EN_BIT			BIT18
#define		MTP_GRP_PRIO_DFI_3_BIT				BIT17
#define		MT_PAR_ADR_3_SHIFT					(0)
#define		MT_PAR_ADR_3_MASK					BIT_MASK(17)

#define R32_FCTL_MT_ADR_3                   (0x34 >> 2)
#define		MTP_SMP_REQ_2_BIT					BIT21
#define		MTP_FRC_EMP_RQS_2_EN_BIT			BIT20
#define		MTP_DLY_2_EN_BIT					BIT19
#define		MTP_LOCK_STATUS_2_EN_BIT			BIT18
#define		MTP_GRP_PRIO_DFI_2_BIT				BIT17
#define		MT_PAR_ADR_2_SHIFT					(0)
#define		MT_PAR_ADR_2_MASK					BIT_MASK(17)

#define	R32_FCTL_FTA_ROW					(0x38 >> 2)
#define		ADR_PHA_6TH_ADR_BYTE_VAL_SHIFT		(24)
#define		ADR_PHA_6TH_ADR_BYTE_VAL_MASK		BIT_MASK(8)
#define		ADR_PHA_5TH_ADR_BYTE_VAL_SHIFT		(16)
#define		ADR_PHA_5TH_ADR_BYTE_VAL_MASK		BIT_MASK(8)
#define		ADR_PHA_4TH_ADR_BYTE_VAL_SHIFT		(8)
#define		ADR_PHA_4TH_ADR_BYTE_VAL_MASK		BIT_MASK(8)
#define		ADR_PHA_3TH_ADR_BYTE_VAL_SHIFT		(0)
#define		ADR_PHA_3TH_ADR_BYTE_VAL_MASK		BIT_MASK(8)

#define R32_FCTL_FTA_COL					(0x3C >> 2)
#define		ADR_PHA_2TH_ADR_BYTE_VAL_SHIFT		(8)
#define		ADR_PHA_2TH_ADR_BYTE_VAL_MASK		BIT_MASK(8)
#define		ADR_PHA_1TH_ADR_BYTE_VAL_SHIFT		(0)
#define		ADR_PHA_1TH_ADR_BYTE_VAL_MASK		BIT_MASK(8)

#define R32_FCTL_DMA_CFG                    (0x40 >> 2)
#define		BUF_MD_EN_BIT						BIT31
#define		ERF_UNC_EN_BIT						BIT29
#define 	SET_ERF_UNC_EN                     			SET_BIT29
#define 	ERF_UNC_DIS_BIT                        			CLR_BIT29
#define 	CLR_ERF_UNC_EN                      CLR_BIT29
#define		RND_SEED_MODE_SHIFT					(27)
#define		RND_SEED_MODE_MASK					BIT_MASK(2)
#define		RND_SEED_MODE_SHIFT_MASK			(RND_SEED_MODE_MASK << RND_SEED_MODE_SHIFT)
#define		BMU_ALCT_EN_BIT						BIT26
#define		SNAP_READ_EN_BIT					BIT25
#define		IGNORE_PHI_RVALID_BIT				BIT23
#define		DQS_GPO_MD_SWITCH_BIT				BIT22
#define		DQS_VALID_BIT						BIT21
#define		DQS_DQSB_CHK_EN						BIT20
#define		DQSB_VALID_BIT						BIT19
#define		DQS_SIN_DUM_SEND_DQS_DATA_BIT		BIT18
#define		DQS_DUM_BUSY_STATUS_BIT				BIT17
#define		DQS_SEND_DATA_BIT					BIT16
#define		DQS_CHK_STATUS_BIT					BIT14
#define		DQS_CHK_STATUS_CLR_BIT				BIT13
#define		LDPC_COR_BPS_BIT					BIT12
#define		BCH_DEC_BPS_BIT						BIT11
#define		FRAME_NUM_SHIFT        				(8)
#define		FRAME_NUM_MASK						BIT_MASK(3)
#define		FRAME_NUM_SHIFT_MASK          		(FRAME_NUM_MASK << FRAME_NUM_SHIFT)
#define		M_SET_FRAME_NUM(X)					((X) << FRAME_NUM_SHIFT)
#define		CLR_FRAME_NUM						(CLR_BIT8&CLR_BIT9&CLR_BIT10)
#define		ALU_GROUP_SHIFT						(4)
#define		ALU_GROUP_MASK						BIT_MASK(2)
#define		ALU_GROUP_SHIFT_MASK				(ALU_GROUP_MASK << ALU_GROUP_SHIFT)
#define		SET_ALU3_GROUP_SEL					(SET_BIT4|SET_BIT5)
#define		SET_ALU2_GROUP_SEL					SET_BIT5
#define		SET_ALU1_GROUP_SEL					SET_BIT4
#define		CLR_ALU0_GROUP_SEL					(CLR_BIT4&CLR_BIT5)
#define		CLR_ALU_GROUP_CLEAR					(CLR_BIT4&CLR_BIT5)
#define		FSA_SEL_BIT							BIT3
#define		CLR_FCTL_FSA_SEL					CLR_BIT3
#define		ULTRA_DMA_BIT						BIT1
#define		TM_CFG_SEL_BIT						BIT0

#define R32_FCTL_OTHER_SET                  (0x44 >> 2)
#define		PAGE_KEY_SEL_BIT					BIT31
#define		REF_SEED_VAL_12_BIT					BIT30
#define		NO_READ_DMA_BIT						BIT29
#define		BCH_COR_EN_BIT						BIT28
#define		SET_BCH_COR_EN						SET_BIT28
#define		CLR_BCH_COR_DIS						CLR_BIT28
#define		FRAME_START_PNT_SHIFT				(26)
#define		FRAME_START_PNT_MASK				BIT_MASK(2)
#define		FRAME_START_PTR_SHIFT_MASK			(FRAME_START_PNT_MASK << FRAME_START_PNT_SHIFT)
#define		SET_FRAME_START_PTR_3				(SET_BIT26|SET_BIT27)
#define		SET_FRAME_START_PTR_2				SET_BIT27
#define		SET_FRAME_START_PTR_1				SET_BIT26
#define		CLR_FRAME_START_PTR_0				(CLR_BIT26&CLR_BIT27)
#define		CLR_FRAME_START_PTR_CLEAR			(CLR_BIT26&CLR_BIT27)
#define		REF_SEED_SEL_BIT					BIT25
#define		CLR_REF_SEED_ROW_ADR        		CLR_BIT25
#define		REF_SEED_VAL_SHIFT					(13)
#define		REF_SEED_VAL_MASK					BIT_MASK(12)
#define		REF_SEED_VAL_SHIFT_MASK				(REF_SEED_VAL_MASK << REF_SEED_VAL_SHIFT)
#define		CLR_REF_SEED_CLEAR					(~REF_SEED_VAL_SHIFT_MASK)
#define		M_SET_REF_SEED_VALUE(X)				(((U32)(X) & REF_SEED_VAL_MASK) << REF_SEED_VAL_SHIFT)
#define		CONV_POLY_ROT_BIT					BIT12
#define		ERR_CFG_PAR_CHK_IBUF_BIT			BIT11
#define		MTQ_GRP_PRIO_EN_BIT					BIT9
#define		FIX_DATA_EN_BIT						BIT8
#define		FIX_DATA_VAL_SHIFT					(0)
#define		FIX_DATA_VAL_MASK					BIT_MASK(8)

#define R32_FCTL_FSA_SEL					(0x48 >> 2)
#define		PIO_WMUP_BPS						BIT31
#define		AP_WMUP_BPS							BIT30
#define		RV_OFS_GRP_SEL_SHIFT			    (26)
#define		RV_OFS_GRP_SEL_MASK	    			BIT_MASK(2)
#define		RV_OFS_FSA_SEL_SHIFT			    (24)
#define		RV_OFS_FSA_SEL_MASK					BIT_MASK(2)
#define		VA_MODE_SHIFT					    (22)
#define		VA_MODE_MASK						BIT_MASK(2)
#define		ADG_SL_PTR_SHIFT					(20)
#define		ADG_SL_PTR_MASK						BIT_MASK(2)
#define		ADG_SL_PTR_SHIFT_MASK				(ADG_SL_PTR_MASK << ADG_SL_PTR_SHIFT)
#define     FPU_RD_DAT_EN                       BIT19
#define		MT_WMUP_BPS							BIT18
#define		SECOND_FSA_COUNTER_SHIFT			(16)
#define		SECOND_FSA_COUNTER_MASK				BIT_MASK(2)
#define		SECOND_FSA_COUNTER_SHIFT_MASK		(SECOND_FSA_COUNTER_MASK << SECOND_FSA_COUNTER_SHIFT)
#define		M_SET_FSA2_COUNTER(X)				(((U32)(X) & SECOND_FSA_COUNTER_MASK) << SECOND_FSA_COUNTER_SHIFT)
#define		SECOND_FSA_SHIFT					(8)
#define		SECOND_FSA_MASK						BIT_MASK(7)
#define		SECOND_FSA_SHIFT_MASK				(SECOND_FSA_MASK << SECOND_FSA_SHIFT)
#define		M_SET_FSA2_SEL(X)					(((U32)(X) & SECOND_FSA_MASK) << SECOND_FSA_SHIFT)
#define		FIRST_FSA_SHIFT						(0)
#define		FIRST_FSA_MASK						BIT_MASK(7)
#define		FIRST_FSA_SHIFT_MASK				(FIRST_FSA_MASK << FIRST_FSA_SHIFT)
#define		M_SET_FSA_SEL(X)					((U32)(X) & FIRST_FSA_MASK)

#define R32_FCTL_UNC_CFG                    (0x4C >> 2)
#define		FORCE_4K_FRAME_UNC_SHIFT			(24)
#define		FORCE_4K_FRAME_UNC_MASK				BIT_MASK(4)
#define		FORCE_4K_FRAME_UNC_SHIFT_MASK		(FORCE_4K_FRAME_UNC_MASK << FORCE_4K_FRAME_UNC_SHIFT )
#define		SET_FORCE_ALL_4K_FRAME_UNC			(FORCE_4K_FRAME_UNC_MASK << FORCE_4K_FRAME_UNC_SHIFT )
#define		M_SET_FORCE_4K_FRAME_UNC(X)			(((U32)(X) & FORCE_4K_FRAME_UNC_MASK) << FORCE_4K_FRAME_UNC_SHIFT )
#define		AUTO_GENPRO_DMA_W					BIT17
#define		AUTO_GENUNC_DMA_R					BIT16

#define R32_FCTL_CNT_ONE                    (0x50 >> 2)
#define		CNT_VAL_BIT							BIT31
#define		SET_COUNT_ONE						SET_BIT31
#define		CLR_COUNT_ZERO						CLR_BIT31
#define		CNT_MODE_EN_BIT						BIT30
#define		SET_COUNTER_MODE_EN					SET_BIT30
#define		CNT_ONE_FUNC_RES_SHIFT				(0)
#define		CNT_ONE_FUNC_RES_MASK				BIT_MASK(18)
#define		CNT_ONE_FUNC_RES_SHIFT_MASK         (CNT_ONE_FUNC_RES_MASK << CNT_ONE_FUNC_RES_SHIFT)


#define R32_FCTL_ECC_INF                    (0x54 >> 2)
#define		ECC_MAX_BFC_SHIFT					(0)
#define		ECC_MAX_BFC_MASK					BIT_MASK(15)
#define		ECC_MAX_BFC_SHIFT_MASK				(ECC_MAX_BFC_MASK << ECC_MAX_BFC_SHIFT)

#define R32_FCTL_INT_VCT                    (0x58 >> 2)
#define R8_FCTL_INT_VCT                    (0x58)
#define		INT_VCT_SHIFT						(8)
#define		INT_VCT_MASK						BIT_MASK(24)
#define		INT_VCT_SHIFT_MASK					(INT_VCT_MASK << INT_VCT_SHIFT) //INT_INDEX_MODE = 0
#define		INDEX_MASK							BIT_MASK(8)
#define		INDEX_SHIFT_MASK					(INDEX_MASK << INT_VCT_SHIFT) 	//INT_INDEX_MODE = 1
#define		ERROR_CPU_SHIFT						(3)
#define		ERROR_CPU_MASK						BIT_MASK(3)
#define		ERROR_CPU_SHIFT_MASK				(((U32) ERROR_CPU_MASK) << ERROR_CPU_SHIFT)
#define		CLR_ERROR_CPU						(~ERROR_CPU_SHIFT_MASK)
#define		SET_ERROR_COP0						SET_BIT5
#define		SET_ERROR_CPU1						SET_BIT4
#define		SET_ERROR_CPU0						SET_BIT3
#define		NORMAL_CPU_SHIFT					(0)
#define		NORMAL_CPU_MASK						BIT_MASK(3)
#define		NORMAL_CPU_SHIFT_MASK				(((U32) NORMAL_CPU_MASK) << NORMAL_CPU_SHIFT)
#define		CLR_NORMAL_CPU						(~NORMAL_CPU_SHIFT_MASK)
#define		SET_NORMAL_COP0						SET_BIT2
#define		SET_NORMAL_CPU1						SET_BIT1
#define		SET_NORMAL_CPU0						SET_BIT0

#define R32_FCTL_CMP_CFG                    (0x60 >> 2)
#define		CMP_EN_BIT							BIT0

#define R32_FCTL_INT_INF                    (0x64 >> 2)
#define     TOTAL_ERR_ALL_BIT                   (0xFFEFFEF0)
#define		CRC16_ERR_UPD_BIT					BIT31
#define		CNT01_LESS_UPD_BIT       			BIT30
#define		P4K_VAL_ERR_UPD_BIT					BIT29
#define		IBF_PTY_ERR_UPD_BIT					BIT28
#define		STA_ERR_UPD_BIT						BIT27
#define		PFA_ERR_UPD_BIT						BIT26
#define		LCA_ERR_UPD_BIT						BIT25
#define		ZINFO_ERR_UPD_BIT					BIT24
#define		OVER_ECC_ERR_UPD_BIT				BIT23
#define		SIGNOFF_UPD_BIT      				BIT22
#define		UNCORR_UPD_BIT						BIT21
#define		INT_VCT_UPD_BIT						BIT20
#define		ERASE_PAGE_UPD_BIT					BIT19
#define		PTO_ERR_UPD_BIT						BIT18
#define		LCA_SER_ERR_UPD_BIT					BIT17
#define		RS_ENC_PTY_ERR_UPD_BIT				BIT16
#define		FLASH_PROG_RD_SLAVE_ERR_UPD_BIT		BIT15
#define		FPU_IRAM_PTY_ERR_UPD_BIT			BIT14
#define		ZIP_IRAM_PTY_ERR_UPD_BIT			BIT13
#define		BR_IRAM_PTY_ERR_UPD_BIT				BIT12
#define		ADG_IRAM_PTY_ERR_UPD_BIT			BIT11
#define		CONV_3D_IRAM_PTY_ERR_UPD_BIT		BIT10
#define		ERASE_FRAME_UPD_BIT					BIT9
#define		MAP_FIFO_PNT_RST_BIT				BIT8
#define		BLCA_ERR_UPD_BIT					BIT7
#define		LPCRC_ERR_UPD_BIT					BIT6
#define		BUFFER_ERR_UPD_BIT					BIT5
#define     NODMA_ERR_UPD_BIT                   BIT4
#define     EARLY_BAD_FRAME_UPD_BIT             BIT3

#define R8_FCTL_INT_INF                    		(0x65)
#define		CLR_MAP_FIFO_PNT_RESET				CLR_BIT0    // Write 0 to clear. should be byte write
//#define		CLR_MAP_FIFO_PNT_RESET				CLR_BIT8    // Write 0 to clear. should be byte write
#define		INT_BUSY_BIT						BIT0
#define		CHK_INT_BUSY						SET_BIT0
#define		CLR_INT_BUSY						CLR_BIT0

#define R32_FCTL_MAP_CFG				(0x68 >> 2)
#define		SET_ALL_UPD_EN						(0x3FFFFF00)
#define		CURRENT_MAP_FIFO_PTR_BIT			BIT31
#define     CNT1_MAP_SEL                        BIT30
#define		NO_DMA_TIMEOUT_EN_BIT				BIT29
#define		ERASE_FRAME_EN_BIT					BIT28
#define		BUF_ERR_EN_BIT						BIT27
#define		BLCA_ERR_EN_BIT						BIT26
#define		LPCRC_ERR_EN_BIT					BIT25
#define		CRC16_ERR_EN_BIT					BIT24
#define		FLASH_PROG_RD_SLAVE_ERR_EN_BIT		BIT23
#define		RS_ENC_ERR_MAP_EN_BIT				BIT22   // not enabled now.
#define		LCA_SEARCH_ERR_EN_BIT				BIT21
#define		PTO_ERR_MAP_EN_BIT					BIT20
#define		ERASE_PAGE_EN_BIT					BIT19
#define		CNT01_LESS_THRESHOLD_EN_BIT 		BIT18
#define		P4K_VALID_ERR_EN_BIT				BIT17
#define		IBF_PTY_ERR_EN_BIT					BIT16
#define		STA_MAP_EN_BIT						BIT15
#define		PFA_ERR_MAP_EN_BIT					BIT14
#define		LCA_ERR_EN_BIT						BIT13
#define		ZINFO_ERR_EN_BIT					BIT12
#define		OVER_ECC_ERR_EN_BIT					BIT11
#define		SIGN_OFF_MASK_EN_BIT				BIT10
#define		UNCORR_MAP_EN_BIT					BIT9
#define		INT_VCT_MAP_EN_BIT					BIT8
#define		PING_PONG_FIFO_INF_SHIFT			(6)
#define		PING_PONG_FIFO_INF_MASK				BIT_MASK(2)
#define		CHK_CURRENT_FIFO					(0x01 << PING_PONG_FIFO_INF_SHIFT)
#define		CHK_NEXT_FIFO						(0x02 << PING_PONG_FIFO_INF_SHIFT)
#define		PING_PONG_FIFO_INF_SHIFT_MASK		(PING_PONG_FIFO_INF_MASK << PING_PONG_FIFO_INF_SHIFT)
#define		PING_PONG_FIFO_SEL_SHIFT			(1)
#define		PING_PONG_FIFO_SEL_MASK				BIT_MASK(5)
#define		PING_PONG_FIFO_SEL_SHIFT_MASK		(PING_PONG_FIFO_SEL_MASK << PING_PONG_FIFO_SEL_SHIFT)
#define		INT_VCT_MAP							(0x00)
#define		ZIP_UNC_MAP							(0x01)
#define		SIGN_OFF_MASK_MAP					(0x02)
#define		OVER_ECC_ERR_THRESHOLD_MAP			(0x03)
#define		ZINFO_ERR_MAP						(0x04)
#define		COMPARE_LCA_FW_ERR_MAP				(0x05)
#define		PFA_ERR_MAP							(0x06)
#define		STA_ERR_MAP							(0x07)
#define		FPU_IRAM_PARITY_ERR					(0x09)
#define		LDPC_UNC_MAP						(0x0A)
#define		ZIP_IRAM_PARITY_ERR					(0x0C)
#define		LCA_SEARCH_ERR_MAP					(0x0D)
#define		CNT01_LESS_THRESHOLD_MAP			(0x0E)
#define		RS_ENCODE_PARITY_ERR_MAP			(0x0F)
#define		CRC16_ERR_MAP	          			(0x10)
#define		MTQID_ERR_MAP	          			(0x11)
#define		P4K_VALID_ERR_MAP					(0x13)
#define		LPCRC_ERR_MAP						(0x14)
#define		BLCA_ERR_MAP						(0x15)
#define		ERR_LCA_MAP							(0x16)
#define		ALLOCATE_4K_MAP						(0x17)
#define		ERASE_FRAME_MAP						(0x18)
#define		BUFFER_ERR_MAP						(0x19)
#define		FCON_MT_CFG_MAP						(0x1A)
#define		FCON_MT_CFG1_MAP					(0x1B)
#define		INI_PTYCHSUM_0_1					(0x1C)
#define		INI_PTYCHSUM_2_3					(0x1D)
#define		INI_PTYCHSUM_4_5					(0x1E)
#define		INI_PTYCHSUM_6_7					(0x1F)

#define		INI_PTYCHSUM_MASK				BIT_MASK(12)
#define		INI_PTYCHSUM_L_SHIFT				(0)
#define		INI_PTYCHSUM_H_SHIFT				(16)

#define SET_SEL_P4K_VALID_ERR          	(0x13 << PING_PONG_FIFO_SEL_SHIFT)
#define SET_SEL_LPCRC_ERR          	(0x14 << PING_PONG_FIFO_SEL_SHIFT)
#define SET_SEL_BLCA_ERR          	(0x15 << PING_PONG_FIFO_SEL_SHIFT)
#define SET_SEL_ERR_LCA_MAP          	(0x16 << PING_PONG_FIFO_SEL_SHIFT)
#define SET_SEL_ALLOCATE_4K_ERR          	(0x17 << PING_PONG_FIFO_SEL_SHIFT)
#define SET_SEL_ERASE_FRM_ERR          	(0x18 << PING_PONG_FIFO_SEL_SHIFT)
#define SET_SEL_BURRER_ERR          	(0x19 << PING_PONG_FIFO_SEL_SHIFT)
#define		PING_PONG_FIFO_SWITCH_BIT			BIT0

#define R32_FCTL_MAP_INF                    (0x6C >> 2)

#define R32_FCTL_RBY_INF                    (0x74 >> 2)
#define		FLH_CE_RBY_AND_SGN_BIT				BIT8
#define		FLH_CE_RBY_SHIFT					(0)
#define		FLH_CE_RBY_MASK						BIT_MASK(8)

#define R32_FCTL_FLH_SET                    (0x7C >> 2)
#define     FCTL_ENH                            BIT28
#define     WDMA_WAT_PHS_EN                     BIT27
#define		TADL_PHS_CNT_SHIFT					(16)
#define		TADL_PHS_CNT_MASK			    	BIT_MASK(11)
#define     DCC_WR_CHK                          BIT15
#define     CDSH_BYPASS                         BIT13
#define		CNV_CLR								BIT12
#define		CNV_WECNT_SHIFT						(2)
#define		CNV_WECNT_MASK						BIT_MASK(10)
#define		CNV_WECNT_SHIFT_MASK				(CNV_WECNT_MASK << CNV_WECNT_SHIFT)
#define		FLH_IF_TYPE_SHIFT					(0)
#define		FLH_IF_TYPE_MASK					BIT_MASK(2)
#define     TADL_PHS_CNT_SHIFT					(16)
#define     TADL_PHS_CNT_MASK                   BIT_MASK(11)
#define		FLH_IF_TYPE_SHIFT_MASK              (FLH_IF_TYPE_MASK << FLH_IF_TYPE_SHIFT)
#define		SET_ONFI_MODE						(0x03 << FLH_IF_TYPE_SHIFT)
#define		SET_TOGGLE_MODE						(0x02 << FLH_IF_TYPE_SHIFT)
#define		CLR_LEGACY_MODE						((CLR_BIT0&CLR_BIT1))

#define R32_FCTL_HS_MODE                    (0x80 >> 2)
#define     PITO_FLAG	                        (31)
#define 	WPSTH_LEN_SHIFT						(27)
#define 	WPSTH_LEN_MASK						BIT_MASK(4)
#define 	WPSTH_LEN_SHIFT_MASK				(WPSTH_LEN_MASK << WPSTH_LEN_SHIFT)
#define 	DQSRH_LEN_SHIFT						(24)
#define 	DQSRH_LEN_MASK						BIT_MASK(3)
#define     PITO_FLAG_CLR_MASK                  (23)
#define 	DQSRH_LEN_SHIFT_MASK				(DQSRH_LEN_MASK << DQSRH_LEN_SHIFT)
#define 	DQSRH_HALF_LEN_EN_BIT				BIT22
#define 	CMD_OEH_LEN_SHIFT					(19)
#define 	CMD_OEH_LEN_MASK					BIT_MASK(3)
#define 	CMD_OEH_LEN_SHIFT_MASK				(CMD_OEH_LEN_MASK << CMD_OEH_LEN_SHIFT)
#define 	CMD_OEL_LEN_SHIFT					(16)
#define 	CMD_OEL_LEN_MASK					BIT_MASK(3)
#define 	CMD_OEL_LEN_SHIFT_MASK				(CMD_OEL_LEN_MASK << CMD_OEL_LEN_SHIFT)
#define     EXT_CDQSH_LE_SHIFT                  (14)
#define     EXT_CDQSH_LE_MASK                   BIT_MASK(2)
#define		PIO_DAT_LAST_BIT					BIT13
#define 	PIO_DAT_FIRST_BIT					BIT12
#define 	CLR_DAT_MID							(~(PIO_DAT_FIRST_BIT | PIO_DAT_LAST_BIT))
#define 	SET_DAT_DEFAULT						(PIO_DAT_FIRST_BIT | PIO_DAT_LAST_BIT)
#define 	DQS_GPO_VAL_BIT						BIT8
#define 	DQS_GPO_MD_BIT						BIT7
#define     DQS_GPO_OE                          BIT6
#define     DQS_GPO_OE_SEL                      BIT5
#define 	PIO_DQS_VAL_BIT						BIT1
#define 	PIO_DQS_OUTP_EN_BIT					BIT0

#define R32_FCTL_INT_CFG					(0x84 >> 2)
#define		SET_ALL_FLH_INT_EN					(0x31BEAFFE)                    // SET_EN_INT_VEC(bit0) use mt
#define		NO_DMA_TIM_INT_EN_BIT				BIT30
#define		NO_STOP_MTQ_INT_OCCUR_BIT			BIT29
#define		BUF_ERR_INT_EN_BIT					BIT28
#define		NO_STOP_MTQ_POL_CHK_INT_OCCUR_BIT	BIT27
#define		NO_STOP_MTQ_ERROR_INT_OCCUR_BIT		BIT26
#define		NO_STOP_MTQ_OVER_ERROR_BIT          BIT25
#define		ERASE_FRAME_INT_EN_BIT				BIT24
#define		BLCA_FAIL_INT_EN_BIT				BIT23
#define		LPCRC_INT_EN_BIT					BIT22
#define		CRC16_FAIL_INT_EN_BIT				BIT21
#define		PTO_INT_EN_BIT						BIT20
#define		SLAVE_ERROR_INT_EN_BIT				BIT19
#define		RS_ENC_PTY_ERR_INT_EN_BIT			BIT18
#define		LCA_SEARCH_ERR_INT_EN_BIT			BIT17
#define		ALL_DONE_INT_EN_BIT					BIT15
#define		DMA_DONE_INT_EN_BIT					BIT13
#define		ABORT_DONE_INT_EN_BIT				BIT12                    // This bit is not allowed to enable
#define		AUTO_POL_STA_FAIL_INT_EN_BIT		BIT11
#define		CMP_LCA_INT_EN_BIT					BIT10
#define		ERASE_PG_INT_EN_BIT					BIT9
#define		P4K_VALID_ERR_INT_EN_BIT			BIT8
#define		IBF_PAR_INT_EN_BIT					BIT7
#define		COUNT01_THRESHOLD_EN_BIT 			BIT6
#define		OVR_ERR_INT_EN_BIT					BIT5
#define		ZINFO_ERR_INT_EN_BIT				BIT4
#define		ERR_STA_INT_EN_BIT                  BIT3
#define		SGN_OFF_MSK_EN_BIT             		BIT2
#define		UNCORR_ECC_INT_EN_BIT				BIT1
#define		INT_VEC_EN_BIT						BIT0

#define R32_FCTL_PCA_CFG					(0x88 >> 2)
#define R32_FCTL_PCA_CFG_1					(0x8C >> 2)
#define		FPU_PCA_EN_BIT						BIT31
#define		ZQ_LOAD_QUE_BIT						BIT17
#define		PAD_ZQ_MAN_LOAD_BIT					BIT16
#define		PAD_ZQ_P_SHIFT						(8)
#define		PAD_ZQ_P_MASK						BIT_MASK(5)
#define		PAD_ZQ_N_SHIFT						(0)
#define		PAD_ZQ_N_MASK						BIT_MASK(5)

#define R32_FCTL_SEED_INIT                  (0x90 >> 2)

#define R32_FCTL_TRN_CFG                    (0x98 >> 2)
#define     INQ_ENHAN                           BIT31
#define     PCA_ENHAN                           BIT29
#define		HB_LLR_SHIFT					    (18)
#define		HB_LLR_MASK						    BIT_MASK(5)
#define		HB_LLR_SHIFT_MASK                   (HB_LLR_MASK << HB_LLR_SHIFT)
#define     PDLY_DIE_SEL_SHIFT                  (16)
#define     PDLY_DIE_SEL_MASK                   BIT_MASK(2)
#define     TRN_MODE                            BIT13
#define     TRN_EN                              BIT12
#define     TRN_WRP_SIZE_SHIFT                  (8)
#define     TRN_WRP_SIZE_MASK                   BIT_MASK(4)
#define     TRN_WRP_SIZE_SHIFT_MASK             (TRN_WRP_SIZE_MASK << TRN_WRP_SIZE_SHIFT)
#define		TRN_WRP_SIZE_8B     				(0x0 << TRN_WRP_SIZE_SHIFT)
#define		TRN_WRP_SIZE_16B     				(0x1 << TRN_WRP_SIZE_SHIFT)
#define		TRN_WRP_SIZE_24B     				(0x2 << TRN_WRP_SIZE_SHIFT)
#define		TRN_WRP_SIZE_32B     				(0x3 << TRN_WRP_SIZE_SHIFT)
#define     TRN_RSUT_SHIFT                      (0)
#define     TRN_RSUT_MASK                       BIT_MASK(8)


#define R32_FCTL_PURE_RAW_DMA_CFG           (0xA0 >> 2)
#define		PURE_RAW_DMA_LEN_SHIFT				(0)
#define		PURE_RAW_DMA_LEN_MASK				BIT_MASK(13)

#define R32_FCTL_SEED_IDX_BASE              (0xA4 >> 2)
#define		SEED_IDX_BASE_SHIFT					(0)
#define		SEED_IDX_BASE_MASK					BIT_MASK(8)
#define		SEED_IDX_BASE_SHIFT_MASK			(SEED_IDX_BASE_MASK << SEED_IDX_BASE_SHIFT)

#define R32_FCTL_PAD_DLY_0                  (0xA8 >> 2)
#define     FLH_DQ7_TX_DLY_SHIFT                (28)
#define     FLH_DQ7_TX_DLY_MASK                 BIT_MASK(4)
#define     FLH_DQ7_TX_DLY_SHIFT_MASK           (FLH_DQ7_TX_DLY_MASK << FLH_DQ7_TX_DLY_SHIFT)
#define     FLH_DQ6_TX_DLY_SHIFT                (24)
#define     FLH_DQ6_TX_DLY_MASK                 BIT_MASK(4)
#define     FLH_DQ6_TX_DLY_SHIFT_MASK           (FLH_DQ6_TX_DLY_MASK << FLH_DQ6_TX_DLY_SHIFT)
#define     FLH_DQ5_TX_DLY_SHIFT                (20)
#define     FLH_DQ5_TX_DLY_MASK                 BIT_MASK(4)
#define     FLH_DQ5_TX_DLY_SHIFT_MASK           (FLH_DQ5_TX_DLY_MASK << FLH_DQ5_TX_DLY_SHIFT)
#define     FLH_DQ4_TX_DLY_SHIFT                (16)
#define     FLH_DQ4_TX_DLY_MASK                 BIT_MASK(4)
#define     FLH_DQ4_TX_DLY_SHIFT_MASK           (FLH_DQ4_TX_DLY_MASK << FLH_DQ4_TX_DLY_SHIFT)
#define     FLH_DQ3_TX_DLY_SHIFT                (12)
#define     FLH_DQ3_TX_DLY_MASK                 BIT_MASK(4)
#define     FLH_DQ3_TX_DLY_SHIFT_MASK           (FLH_DQ3_TX_DLY_MASK << FLH_DQ3_TX_DLY_SHIFT)
#define     FLH_DQ2_TX_DLY_SHIFT                (8)
#define     FLH_DQ2_TX_DLY_MASK                 BIT_MASK(4)
#define     FLH_DQ2_TX_DLY_SHIFT_MASK           (FLH_DQ2_TX_DLY_MASK << FLH_DQ2_TX_DLY_SHIFT)
#define     FLH_DQ1_TX_DLY_SHIFT                (4)
#define     FLH_DQ1_TX_DLY_MASK                 BIT_MASK(4)
#define     FLH_DQ1_TX_DLY_SHIFT_MASK           (FLH_DQ1_TX_DLY_MASK << FLH_DQ1_TX_DLY_SHIFT)
#define     FLH_DQ0_TX_DLY_SHIFT                (0)
#define     FLH_DQ0_TX_DLY_MASK                 BIT_MASK(4)
#define     FLH_DQ0_TX_DLY_SHIFT_MASK           (FLH_DQ0_TX_DLY_MASK << FLH_DQ0_TX_DLY_SHIFT)

#define R32_FCTL_PAD_DLY_1                  (0xAC >> 2)
#define     FLH_WRB_RX_DLY_SHIFT                (20)
#define     FLH_WRB_RX_DLY_MASK                 BIT_MASK(4)
#define     FLH_WRB_RX_DLY_SHIFT_MASK           (FLH_WRB_RX_DLY_MASK << FLH_WRB_RX_DLY_SHIFT)
#define     FLH_RDB_RX_DLY_SHIFT                (16)
#define     FLH_RDB_RX_DLY_MASK                 BIT_MASK(4)
#define     FLH_RDB_RX_DLY_SHIFT_MASK           (FLH_RDB_RX_DLY_MASK << FLH_RDB_RX_DLY_SHIFT)
#define     FLH_DQS_RX_DLY_SHIFT                (12)
#define     FLH_DQS_RX_DLY_MASK                 BIT_MASK(4)
#define     FLH_DQS_RX_DLY_SHIFT_MASK           (FLH_DQS_RX_DLY_MASK << FLH_DQS_RX_DLY_SHIFT)
#define     FLH_WRB_TX_DLY_SHIFT                (8)
#define     FLH_WRB_TX_DLY_MASK                 BIT_MASK(4)
#define     FLH_WRB_TX_DLY_SHIFT_MASK           (FLH_WRB_TX_DLY_MASK << FLH_WRB_TX_DLY_SHIFT)
#define     FLH_RDB_TX_DLY_SHIFT                (4)
#define     FLH_RDB_TX_DLY_MASK                 BIT_MASK(4)
#define     FLH_RDB_TX_DLY_SHIFT_MASK           (FLH_RDB_TX_DLY_MASK << FLH_RDB_TX_DLY_SHIFT)
#define     FLH_DQS_TX_DLY_SHIFT                (0)
#define     FLH_DQS_TX_DLY_MASK                 BIT_MASK(4)
#define     FLH_DQS_TX_DLY_SHIFT_MASK           (FLH_DQS_TX_DLY_MASK << FLH_DQS_TX_DLY_SHIFT)

#define R32_FCTL_PAD_DLY_2                  (0xB0 >> 2)
#define     FLH_DQ7_RX_DLY_SHIFT                (0)
#define     FLH_DQ7_RX_DLY_MASK                 BIT_MASK(4)
#define     FLH_DQ7_RX_DLY_SHIFT_MASK           (FLH_DQ7_RX_DLY_MASK << FLH_DQ7_RX_DLY_SHIFT)
#define     FLH_DQ6_RX_DLY_SHIFT                (0)
#define     FLH_DQ6_RX_DLY_MASK                 BIT_MASK(4)
#define     FLH_DQ6_RX_DLY_SHIFT_MASK           (FLH_DQ6_RX_DLY_MASK << FLH_DQ6_RX_DLY_SHIFT)
#define     FLH_DQ5_RX_DLY_SHIFT                (0)
#define     FLH_DQ5_RX_DLY_MASK                 BIT_MASK(4)
#define     FLH_DQ5_RX_DLY_SHIFT_MASK           (FLH_DQ5_RX_DLY_MASK << FLH_DQ5_RX_DLY_SHIFT)
#define     FLH_DQ4_RX_DLY_SHIFT                (0)
#define     FLH_DQ4_RX_DLY_MASK                 BIT_MASK(4)
#define     FLH_DQ4_RX_DLY_SHIFT_MASK           (FLH_DQ4_RX_DLY_MASK << FLH_DQ4_RX_DLY_SHIFT)
#define     FLH_DQ3_RX_DLY_SHIFT                (0)
#define     FLH_DQ3_RX_DLY_MASK                 BIT_MASK(4)
#define     FLH_DQ3_RX_DLY_SHIFT_MASK           (FLH_DQ3_RX_DLY_MASK << FLH_DQ3_RX_DLY_SHIFT)
#define     FLH_DQ2_RX_DLY_SHIFT                (0)
#define     FLH_DQ2_RX_DLY_MASK                 BIT_MASK(4)
#define     FLH_DQ2_RX_DLY_SHIFT_MASK           (FLH_DQ2_RX_DLY_MASK << FLH_DQ2_RX_DLY_SHIFT)
#define     FLH_DQ1_RX_DLY_SHIFT                (0)
#define     FLH_DQ1_RX_DLY_MASK                 BIT_MASK(4)
#define     FLH_DQ1_RX_DLY_SHIFT_MASK           (FLH_DQ1_RX_DLY_MASK << FLH_DQ1_RX_DLY_SHIFT)
#define     FLH_DQ0_RX_DLY_SHIFT                (0)
#define     FLH_DQ0_RX_DLY_MASK                 BIT_MASK(4)
#define     FLH_DQ0_RX_DLY_SHIFT_MASK           (FLH_DQ0_RX_DLY_MASK << FLH_DQ0_RX_DLY_SHIFT)

#define R32_FCTL_FARG_ROW                   (0xB8 >> 2)
#define		ADR_PHA_6TH_VAL_SHIFT				(24)
#define		ADR_PHA_6TH_VAL_MASK				BIT_MASK(8)
#define		ADR_PHA_5TH_VAL_SHIFT				(16)
#define		ADR_PHA_5TH_VAL_MASK				BIT_MASK(8)
#define		ADR_PHA_4TH_VAL_SHIFT				(8)
#define		ADR_PHA_4TH_VAL_MASK				BIT_MASK(8)
#define		ADR_PHA_3RD_VAL_SHIFT				(0)
#define		ADR_PHA_3RD_VAL_MASK				BIT_MASK(8)

#define R32_FCTL_FARG_COL                   (0xBC >> 2)
#define		ADR_PHA_2ND_VAL_SHIFT				(8)
#define		ADR_PHA_2ND_VAL_MASK				BIT_MASK(8)
#define		ADR_PHA_1ST_VAL_SHIFT				(0)
#define		ADR_PHA_1ST_VAL_MASK				BIT_MASK(8)

#define R32_FCTL_FSA_ADR                    (0xC0 >> 2)
#define		FSA_BASE_SHIFT						(0)
#define		FSA_BASE_MASK						BIT_MASK(17)
#define		FSA_BASE_SHIFT_MASK					(FSA_BASE_MASK << FSA_BASE_SHIFT)

#define R32_FCTL_CHNL_SET                   (0xC4 >> 2)
#define     DUM_LEN_2_SHIFT                     (29)
#define     DUM_LEN_2_MASK                      BIT_MASK(2)
#define		MT_AUTO_CE_CLR_BIT					BIT28
#define		SET_MT_AUTO_CE_CLR					SET_BIT28
#define		DUM_LEN_SHIFT						(16)
#define		DUM_LEN_MASK						BIT_MASK(11)
#define		DUM_LEN_SHIFT_MASK					(DUM_LEN_MASK << DUM_LEN_SHIFT)
#define     M_CLR_DUM_LEN(X)                    (x & (~(DUM_LEN_SHIFT_MASK + DUM_LEN_2_MASK)))
#define		FLH_EDO_MD_BIT						BIT10
#define		SET_EDO_EN							SET_BIT10
#define		CNVS_BYPASS_BIT						BIT9
#define		SET_CONV_BYPASS_EN					SET_BIT9
#define		CLR_CONV_BYPASS_DIS					CLR_BIT9
#define		INVS_BYPASS_BIT						BIT8
#define		SET_INV_BYPASS_EN					SET_BIT8
#define		CLR_INV_BYPASS_DIS					CLR_BIT8
#define		IOR_Q_SEL_BIT						BIT5
#define		SCALE_MODE_SHIFT					(3)
#define		SCALE_MODE_MASK						BIT_MASK(2)
#define		SCALE_MODE_SHIFT_MASK				(SCALE_MODE_MASK << SCALE_MODE_SHIFT)
#define		SET_SCALE_MODE_SB					(0x1 << SCALE_MODE_SHIFT)
#define		DECODE_MODE_SHIFT					(0)
#define		DECODE_MODE_MASK					BIT_MASK(3)
#define		DECODE_MODE_SHIFT_MASK				(DECODE_MODE_MASK << DECODE_MODE_SHIFT)
#define		SET_DECODE_MODE_HB					(0x0 << DECODE_MODE_SHIFT)
#define		SET_DECODE_MODE_HBSB1				(0x1 << DECODE_MODE_SHIFT)
#define		SET_DECODE_MODE_HBSB12				(0x2 << DECODE_MODE_SHIFT)
#define		SET_DECODE_MODE_HBSB123				(0x3 << DECODE_MODE_SHIFT)
#define		SET_DECODE_MODE_HBSB1234			(0x4 << DECODE_MODE_SHIFT)


#define R32_FCTL_INT_CFG_1                  (0xC8 >> 2)
#define		ERR_INT_MATCH_VALUE_SHIFT			(24)
#define		ERR_INT_MATCH_VALUE_MASK			BIT_MASK(5)
#define     ERB_FRM_CPU_INT_EN_BIT              BIT21
#define		NODMA_TO_CPU_INT_EN_BIT				BIT20
#define		ERA_FRM_CPU_INT_EN_BIT				BIT19
#define		BUF_ERR_CPU_INT_EN_BIT				BIT18
#define		BLCA_CPU_INT_EN_BIT					BIT17
#define		LPCRC_CPU_INT_EN_BIT				BIT16
#define		PTO_CPU_INT_EN_BIT					BIT15
#define		SLV_ERR_INT_EN_BIT					BIT14
#define		RSENC_ERR_CPU_INT_EN_BIT			BIT13
#define		E2E_ERR_CPU_INT_EN_BIT				BIT12
#define		CRC16_FAIL_CPU_INT_EN_BIT			BIT11
#define		STS_CMP_FAIL_CPU_NIT_EN_BIT			BIT10
#define		AUTO_POLL_CPU_INT_EN_BIT			BIT9
#define		ERA_PAGE_CPU_INT_EN_BIT				BIT8
#define		AES_PAR_ERR_CPU_INT_EN_BIT			BIT7
#define		IBF_PAR_ERR_CPU_INT_EN_BIT			BIT6
#define		CNT_01_LESS_THR_CPU_INT_EN_BIT		BIT5
#define		UNC_ECC_CPU_INT_EN_BIT				BIT4
#define		SIGNOFF_MASK_CPU_INT_EN_BIT			BIT3
#define		OVER_ERR_CPU_INT_EN_BIT				BIT2
#define		CRC32_FAIL_CPU_INT_EN_BIT			BIT1
#define		CMP_LCA_FW_CPU_INT_EN_BIT			BIT0

#define R32_FCTL_INT_RDY                    (0xCC >> 2)
#define		MTQ_RSM_INT_RDY_SHIFT				(24)
#define		MTQ_RSM_INT_RDY_MASK				BIT_MASK(8)
#define		MTQ_RSM_INT_RDY_SHIFT_MASK			(MTQ_RSM_INT_RDY_MASK << MTQ_RSM_INT_RDY_SHIFT)
#define		MTQ_SPD_INT_RDY_SHIFT				(16)
#define		MTQ_SPD_INT_RDY_MASK				BIT_MASK(8)
#define		MTQ_SPD_INT_RDY_SHIFT_MASK			(MTQ_SPD_INT_RDY_MASK << MTQ_SPD_INT_RDY_SHIFT)
#define		MTQ_QOS_INT_BUSY_SHIFT     			(8)
#define		MTQ_QOS_INT_BUSY_MASK				BIT_MASK(8)
#define		MTQ_QOS_BUSY_SHIFT_MASK            	(MTQ_QOS_INT_BUSY_MASK << MTQ_QOS_INT_BUSY_SHIFT)
#define		M_SET_MTQ_QOS_BUSY(x)            	(((x) & MTQ_QOS_INT_BUSY_MASK) << MTQ_QOS_INT_BUSY_SHIFT)
#define     SET_ALL_QOS_INT_RDY            		MTQ_QOS_BUSY_SHIFT_MASK
#define		MTQ_INT_RDY_SHIFT     				(0)
#define		MTQ_INT_RDY_MASK					BIT_MASK(8)
#define		MTQ_INT_RDY_SHIFT_MASK            	(MTQ_INT_RDY_MASK << MTQ_INT_RDY_SHIFT)
#define		M_SET_MTQ_INT_RDY(x)            	(((x)&MTQ_INT_RDY_MASK) << MTQ_INT_RDY_SHIFT)
#define 	SET_ALL_MTQ_INT_RDY					MTQ_INT_RDY_SHIFT_MASK
#define		MTQ_INT_RDY_AUTOPOLL_ALLCLEAR		(0xFFFFFFFF)

#define R32_FCTL_FPU_TRIG                   (0xD0 >> 2)
#define R8_FCTL_FPU_TRIG                    (0xD0)
#define		TIMEOUT_OCC_BIT						BIT7
#define     SMP_BSY_BIT                         BIT6
#define		MT_BSY_BIT							BIT4
#define		ANY_BSY_BIT							BIT3
#define		SRQ_SVL_BIT							BIT2
#define		SIGN_OFF_BUSY_BIT					BIT1
#define		FPU_TRIGGER_BIT						BIT0
#define		CHK_FPU_BUSY						(FPU_TRIGGER_BIT | SIGN_OFF_BUSY_BIT)

#define R32_FCTL_FPU_ENTRY                  (0xD4 >> 2)
#define		FPU_ADDR_BASE_SHIFT					(0)
#define		FPU_ADDR_BASE_MASK					BIT_MASK(16)
#define		FPU_ADDR_BASE_SHIFT_MASK			(FPU_ADDR_BASE_MASK << FPU_ADDR_BASE_SHIFT)

#define R32_FCTL_MT_ADR_4					(0xD8 >> 2)
#define		MTP_SMP_REQ_4_BIT					BIT21
#define		MTP_FRC_EMP_RQS_4_EN_BIT			BIT20
#define		MTP_DLY_4_EN_BIT					BIT19
#define		MTP_LOCK_STATUS_4_EN_BIT			BIT18
#define		MTP_GRP_PRIO_DFI_4_BIT				BIT17
#define		MTP_ADR_4_SHIFT						(0)
#define		MTP_ADR_4_MASK						BIT_MASK(17)

#define R32_FCTL_MT_ADR_5					(0xDC >> 2)
#define		MTP_SMP_REQ_5_BIT					BIT21
#define		MTP_FRC_EMP_RQS_5_EN_BIT			BIT20
#define		MTP_DLY_5_EN_BIT					BIT19
#define		MTP_LOCK_STATUS_5_EN_BIT			BIT18
#define		MTP_GRP_PRIO_DFI_5_BIT				BIT17
#define		MTP_ADR_5_SHIFT						(0)
#define		MTP_ADR_5_MASK						BIT_MASK(17)

#define R32_FCTL_MT_ADR_6					(0xE0 >> 2)
#define		MTP_SMP_REQ_6_BIT					BIT21
#define		MTP_FRC_EMP_RQS_6_EN_BIT			BIT20
#define		MTP_DLY_6_EN_BIT					BIT19
#define		MTP_LOCK_STATUS_6_EN_BIT			BIT18
#define		MTP_GRP_PRIO_DFI_6_BIT				BIT17
#define		MTP_ADR_6_SHIFT						(0)
#define		MTP_ADR_6_MASK						BIT_MASK(17)

#define R32_FCTL_MT_ADR_7					(0xE4 >> 2)
#define		MTP_SMP_REQ_7_BIT					BIT21
#define		MTP_FRC_EMP_RQS_7_EN_BIT			BIT20
#define		MTP_DLY_7_EN_BIT					BIT19
#define		MTP_LOCK_STATUS_7_EN_BIT			BIT18
#define		MTP_GRP_PRIO_DFI_7_BIT				BIT17
#define		MTP_ADR_7_SHIFT						(0)
#define		MTP_ADR_7_MASK						BIT_MASK(17)

#define R32_FCTL_FAIL_CNT                   (0xE8 >> 2)
#define INT_FAIL_CNT_SHIFT						(0)
#define INT_FAIL_CNT_MASK						BIT_MASK(8)
#define INT_FAIL_CNT_SHIFT_MASK					(INT_FAIL_CNT_MASK << INT_FAIL_CNT_SHIFT)

#define R32_FCTL_MTQ_CFG                    (0xEC >> 2)
#define		FRC_EMP_QOS_SHIFT					(16)
#define		FRC_EMP_QOS_MASK					BIT_MASK(8)
#define		FRC_EMP_QOS_SHIFT_MASK				(FRC_EMP_QOS_MASK << FRC_EMP_QOS_SHIFT)
#define 	SET_ALL_QOS_MT_EMPTY            	(FRC_EMP_QOS_SHIFT_MASK)
#define		M_SET_QOS_MT_EMPTY(n)				(BIT((n)) << FRC_EMP_QOS_SHIFT)
#define		CLR_ALL_QOS_MT_EMPTY				(~FRC_EMP_QOS_SHIFT_MASK)
#define		MASK_REQ_SHIFT						(8)
#define		MASK_REQ_MASK						BIT_MASK(8)
#define		MASK_REQ_SHIFT_MASK					(MASK_REQ_MASK << MASK_REQ_SHIFT)
#define		FRC_EMP_SHIFT						(0)
#define		FRC_EMP_MASK						BIT_MASK(8)
#define		FRC_EMP_SHIFT_MASK					(FRC_EMP_MASK << FRC_EMP_SHIFT)
#define 	SET_ALL_MT_EMPTY					(FRC_EMP_SHIFT_MASK)
#define		M_SET_MT_EMPTY(n)	                (BIT((n)) << FRC_EMP_SHIFT)
#define		CLR_ALL_MT_EMPTY					(~FRC_EMP_SHIFT_MASK)

#define R32_FCTL_BF_CFG                     (0xF0 >> 2)

#define R32_FCTL_MTQ_INF_0                  (0xF8 >> 2)
#define		MTQ3_NON_EXE_NUM_SHIFT				(24)
#define		MTQ3_NON_EXE_NUM_MASK				BIT_MASK(8)
#define		MTQ2_NON_EXE_NUM_SHIFT				(16)
#define		MTQ2_NON_EXE_NUM_MASK				BIT_MASK(8)
#define		MTQ1_NON_EXE_NUM_SHIFT				(8)
#define		MTQ1_NON_EXE_NUM_MASK				BIT_MASK(8)
#define		MTQ0_NON_EXE_NUM_SHIFT				(0)
#define		MTQ0_NON_EXE_NUM_MASK				BIT_MASK(8)
#define		MTQ_NON_EXE_NUM_SHIFT				(8)
#define		MTQ_NON_EXE_NUM_MASK				BIT_MASK(8)
#define 	QUEUE_NUM_PER_MTQ_INFO_REG				(4)

#define R32_FCTL_DBG_INF                    (0xFC >> 2)
#define		SET_DGB_INF_BCF						(0x00)
#define		SET_DGB_INF_RWG						(0x01)
#define		SET_DGB_INF_FDMA					(0x02)
#define		SET_DGB_INF_EDMA					(0x03)
#define		SET_DGB_INF_RDMA					(0x04)
#define		SET_DGB_INF_FAG						(0x05)
#define		SET_DGB_INF_FPU						(0x06)
#define		SET_DGB_INF_MT						(0x07)
#define		SET_DGB_INF_SRQ_0					(0x08)
#define		SET_DGB_INF_SRQ_1					(0x09)
#define		SET_DGB_INF_SRQ_2					(0x0A)

#define R32_FCTL_ERASE_CFG                  (0x104 >> 2)
#define		TIMEOUT_FLG_STA_BIT					BIT31
#define		MTQ_TIMEOUT_STOP_EN_BIT				BIT29
#define		TIMEOUT_FLG_STA_CLR_BIT				BIT28
#define		EXP_TIME_OUT_VAL_SHIFT				(0)
#define		EXP_TIME_OUT_VAL_MASK				BIT_MASK(28)
#define		EXP_TIME_OUT_VAL_SHIFT_MASK			(EXP_TIME_OUT_VAL_MASK << EXP_TIME_OUT_VAL_SHIFT)

#define R32_FCTL_ERASE_SEQ                  (0x108 >> 2)

#define R32_FCTL_BF_CFG_1                   (0x10C >> 2)

#define R32_FCTL_MTQ_INF_1                  (0x110 >> 2)
#define		MTQ7_NON_EXE_NUM_SHIFT				(24)
#define		MTQ7_NON_EXE_NUM_MASK				BIT_MASK(8)
#define		MTQ6_NON_EXE_NUM_SHIFT				(16)
#define		MTQ6_NON_EXE_NUM_MASK				BIT_MASK(8)
#define		MTQ5_NON_EXE_NUM_SHIFT				(8)
#define		MTQ5_NON_EXE_NUM_MASK				BIT_MASK(8)
#define		MTQ4_NON_EXE_NUM_SHIFT				(0)
#define		MTQ4_NON_EXE_NUM_MASK				BIT_MASK(8)

#define R32_FCTL_MTQ_DLY                    (0x114 >> 2)
#define		MTQ_DELAY_CANCEL_SHIFT				(24)
#define		MTQ_DELAY_CANCEL_MASK				BIT_MASK(8)
#define		MTQ_DELAY_CANCEL_SHIFT_MASK			(MTQ_DELAY_CANCEL_MASK << MTQ_DELAY_CANCEL_SHIFT)
#define		MTQ_DELAY_SHIFT						(0)
#define		MTQ_DELAY_MASK						BIT_MASK(24)

#define R32_FCTL_FDIV_CFG                   (0x118 >> 2)
#define		DIV_BS_BIT							BIT30
#define		PREV_FLH_CLK_DIV_VAL_SHIFT			(27)
#define		PREV_FLH_CLK_DIV_VAL_MASK			BIT_MASK(3)
#define		PREV_FLH_CLK_DIV_VAL_SHIFT_MASK		(PREV_FLH_CLK_DIV_VAL_MASK << PREV_FLH_CLK_DIV_VAL_SHIFT)
#define		PREV_FLH_CLK_DIV_EN_BIT				BIT26
#define		FLH_CLK_DIV_DIS_TRIG_BIT			BIT7
#define		FLH_CLK_DIV_EN_BIT					BIT6
#define		FLH_CLK_DIV_VAL_SHIFT				(0)
#define		FLH_CLK_DIV_VAL_MASK				BIT_MASK(3)
#define		FLH_CLK_DIV_VAL_SHIFT_MASK			(FLH_CLK_DIV_VAL_MASK << FLH_CLK_DIV_VAL_SHIFT)
#define 		FLH_CLK_DIV2_VAL					(0)
#define     	FLH_CLK_DIV4_VAL					(1)
#define     	FLH_CLK_DIV8_VAL					(2)
#define     	FLH_CLK_DIV16_VAL					(3)
#define     	FLH_CLK_DIV32_VAL					(4)
#define     	FLH_CLK_DIV64_VAL					(5)
#define     	FLH_CLK_DIV128_VAL					(6)
#define     	FLH_CLK_DIV128_VAL_1				(7)
#define         FLH_CLK_DIV_VAL_MIN                 (FLH_CLK_DIV2_VAL)

#define R32_FCTL_BF_CFG_2                   (0x11C >> 2)
#define     RBER_BPS                            BIT8
#define		FLH_FW_FLIP_CNT_SHIFT				(0)
#define		FLH_FW_FLIP_CNT_MASK				BIT_MASK(8)

#define R32_FCTL_RV_OFS_CFG                 (0x120 >> 2)

#define R32_FCTL_FRM_MASK                   (0x124 >> 2)
#define		FRM_MASK_SHIFT						(0)
#define		FRM_MASK_MASK						BIT_MASK(16)
#define		FRM_MASK_SHIFT_MASK					(FRM_MASK_MASK << FRM_MASK_SHIFT)

#define R32_FCTL_L4K_SPR_ADR                (0x128 >> 2)

#define R32_FCTL_ZIP_CFG_1                  (0x12C >> 2)
#define		PCA_CRC_CHK_EN_BIT					BIT31
#define		LP_CRC_CHK_EN_BIT					BIT30
#define		P4K_VAL_CHK_EN_BIT					BIT29
#define		ZINFO_CHK_EN_BIT					BIT28
#define		LP_CRC_FAIL_BIT						BIT24
#define		ZIP_EN_BIT							BIT23
#define		CRC_CHK_DIS_BIT						BIT22
#define		RAIDECC_FPU_ENC_MODE_BIT			BIT21
#define		L4K_NUM_SHIFT       				(16)
#define		L4K_NUM_MASK						BIT_MASK(5)
#define		L4K_NUM_SHIFT_MASK         			(L4K_NUM_MASK << L4K_NUM_SHIFT)
#define		BLCA_CHK_EN_BIT						BIT15
#define		UP_BADR_EN_BIT						BIT14
#define		ULTRA_W_ADG_BIT						BIT13
#define		GC_EN_BIT							BIT12
#define		PROG_BUF_VALID_SHIFT				(8)
#define		PROG_BUF_VALID_MASK					BIT_MASK(4)
#define		PROG_BUF_VALID_SHIFT_MASK			(PROG_BUF_VALID_MASK << PROG_BUF_VALID_SHIFT)
#define		RAIDECC_SKIP_CNT_SHIFT       		0
#define		RAIDECC_SKIP_CNT_MASK				BIT_MASK(8)
#define		RAIDECC_SKIP_CNT_SHIFT_MASK         (RAIDECC_SKIP_CNT_MASK << RAIDECC_SKIP_CNT_SHIFT)

#define R32_FCTL_RAW_DMA_ADR                (0x130 >> 2)

#define R32_FCTL_BACK_RESTORE               (0x134 >> 2)
#define     DRAM_MULTI_EN						BIT19
#define		M_SET_DRAM_ADDR(x)					(((x) & DRAM_ADDR_SEL_MASK) << DRAM_ADDR_SEL_SHIFT)
#define		BR_W_LEN_SHIFT						(20)
#define		BR_W_LEN_MASK						BIT_MASK(10)
#define		BR_W_LEN_SHIFT_MASK					(BR_W_LEN_MASK << BR_W_LEN_SHIFT)
#define		M_SET_BR_W_LEN(x)					(((x) & BR_W_LEN_MASK) << BR_W_LEN_SHIFT)
#define		DIRECTION_TO_EX_MEM					(0) // From IBF to external buffer
#define		DIRECTION_TO_IBF					(1)	// From external buffer to IBF
#define		MODE_DBUF_ONLY						(0)	// Transfer all data from/to DBUF
#define		MODE_DBUF_AND_IRAM					(1)	// Transfer 4KB user data from/to DBUF, 16B spare data from/to IRAM
#define		BCH_MODE_NO_ECC						(0) // Transfer 4KB user data + 16B spare data
#define		BCH_MODE_WITH_ECC					(1) // Trnsfer all data, include LDPC parity
#define		ALL_RESTORE_BY_PTR					(0)
#define		ALL_RESTORE_ALL						(1)

#define R32_FCTL_RAIDECC_CFG				(0x138 >> 2)
#define		FCTL_RAIDECC_GRP_NUM_SHIFT			(29)
#define		FCTL_RAIDECC_GRP_NUM_MASK			BIT_MASK(3)
#define		FCTL_RAIDECC_GRP_NUM_SHIFT_MASK		(FCTL_RAIDECC_GRP_NUM_MASK << FCTL_RAIDECC_GRP_NUM_SHIFT)
#define		RAIDECC_PAR_MD_EN_BIT				BIT28
#define		RAIDECC_PG_NUM_SHIFT				(20)
#define		RAIDECC_PG_NUM_MASK					BIT_MASK(8)
#define		RAIDECC_PG_NUM_SHIFT_MASK           (RS_PG_NUM_MASK << RS_PG_NUM_SHIFT)
#define		RAIDECC_PRT_TLC_PROG_BIT			BIT17
#define		RAIDECC_TAG_SHIFT					(8)
#define		RAIDECC_TAG_MASK					BIT_MASK(8)
#define		RAIDECC_TAG_SHIFT_MASK				(RAIDECC_TAG_MASK << RAIDECC_TAG_SHIFT)
#define		RAIDECC_DEC_PEC_CNT_BIT				BIT5
#define		RAIDECC_PROGRAM_EC_PAR_BIT			BIT4
#define		LAST_PG_VAL_BIT						BIT3
#define		PROGRAM_PAR_EN_BIT					BIT2
#define		OTF_EN_BIT							BIT1
#define		ENC_MD_BIT							BIT0

#define R32_FCTL_ECC_CFG                    (0x13C >> 2)
#define     CH_LDPC_MODE_SHIFT                  (16)
#define     CH_LDPC_MODE_MASK                   BIT_MASK(4)
#define		BCH_RESULT_COR_BIT					BIT15
#define		LDPC_RESULT_COR_BIT					BIT14
#define		LDPC_COR_EN_BIT						BIT13
#define		SET_LDPC_COR_EN              		SET_BIT13
#define		CLR_LDPC_COR_DIS             		CLR_BIT13
#define     CH_EOT_SEL_SHIFT                    (11)
#define     CH_EOT_SEL_MASK                     BIT_MASK(2)

#define R32_FCTL_RD_CNT                     (0x140 >> 2)
#define		RE_CNT_SHIFT						(0)
#define		RE_CNT_MASK							BIT_MASK(16)
#define		RE_CNT_SHIFT_MASK					(RE_CNT_MASK << RE_CNT_SHIFT)

#define R32_FCTL_DQS_CNT                    (0x144 >> 2)
#define		DQS_REDGE_CNT_SHIFT					(16)
#define		DQS_REDGE_CNT_MASK					BIT_MASK(16)
#define		DQS_REDGE_CNT_SHIFT_MASK			(DQS_REDGE_CNT_MASK << DQS_REDGE_CNT_SHIFT)
#define		DQS_FEDGE_CNT_SHIFT					(0)
#define		DQS_FEDGE_CNT_MASK					BIT_MASK(16)
#define		DQS_FEDGE_CNT_SHIFT_MASK			(DQS_FEDGE_CNT_MASK << DQS_FEDGE_CNT_SHIFT)

#define R32_FCTL_DYNAMIC_ODT                (0x148 >> 2)
#define		ODT_EN_SET_LEN_SHIFT				(8)
#define		ODT_EN_SET_LEN_MASK					BIT_MASK(5)
#define		ODT_EN_SET_LEN_SHIFT_MASK			(ODT_EN_SET_LEN_MASK << ODT_EN_SET_LEN_SHIFT)
#define		ODT_SET_H_COND_BIT					BIT3
#define		ODT_FORCE_VALUE_BIT					BIT2
#define		ODT_FORCE_EN_BIT					BIT1
#define		DYNAMIC_ODT_EN_BIT					BIT0

#define R32_FCTL_IBF_CTL                    (0x14C >> 2)
#define		FORCE_DATA_COR_BIT					BIT9
#define		SET_FORCE_DATA_COR_EN				SET_BIT9
#define		CLR_FORCE_DATA_COR_EN				CLR_BIT9

#define R32_FCTL_ECC_DBG                    (0x150 >> 2)
#define		ECC_CUR_COR_CNT_SHIFT				(4)
#define		ECC_CUR_COR_CNT_MASK				BIT_MASK(4)
#define		ECC_CUR_COR_CNT_SHIFT_MASK			(ECC_CUR_COR_CNT_MASK << ECC_CUR_COR_CNT_SHIFT)
#define		ECC_CUR_EN_DE_CNT_SHIFT				(0)
#define		ECC_CUR_EN_DE_CNT_MASK				BIT_MASK(4)
#define		ECC_CUR_EN_DE_CNT_SHIFT_MASK		(ECC_CUR_EN_DE_CNT_MASK << ECC_CUR_EN_DE_CNT_SHIFT)

#define R32_FCTL_IFSA0                      (0x154 >> 2)

#define R32_FCTL_IFSA1                      (0x158 >> 2)

#define R32_FCTL_IFSA2                      (0x15C >> 2)

#define R32_FCTL_IFSA3                      (0x160 >> 2)

#define R32_FCTL_FPU_INT_VCT                (0x164 >> 2)
#define		CUR_FPU_INT_VCT_IND_SHIFT			(0)
#define		CUR_FPU_INT_VCT_IND_MASK			BIT_MASK(24)

#define R32_FCTL_DMA_DONE_VCT               (0x168 >> 2)
#define		DMA_DONE_INT_VCT_IND_SHIFT			(0)
#define		DMA_DONE_INT_VCT_IND_MASK			BIT_MASK(24)

#define R32_FCTL_FRONT_VCT                  (0x16C >> 2)
#define		FRONT_DMA_INT_VCT_IND_SHIFT			(0)
#define		FRONT_DMA_INT_VCT_IND_MASK			BIT_MASK(24)

#define R32_FCTL_BACK_VCT                   (0x170 >> 2)
#define		BACK_DMA_INT_VCT_IND_SHIFT			(0)
#define		BACK_DMA_INT_VCT_IND_MASK			BIT_MASK(24)

#define R32_FTCL_RAW_CNT_ERR                (0x174 >> 2)

#define R32_FCTL_ZIP_DMA_ST                 (0x17C >> 2)
#define		AXI_FINISH_L4K_SHIFT				(0)
#define		AXI_FINISH_L4K_MASK					BIT_MASK(16)

#define R32_FCTL_E2E_ERR_CNT                (0x180 >> 2)
#define		E2E_FAIL_CLR_BIT					BIT31
#define		CNT_E2E_FAIL_SHIFT					(0)
#define		CNT_E2E_FAIL_MASK					BIT_MASK(31)
#define		CNT_E2E_FAIL_SHIFT_MASK				(CNT_E2E_FAIL_MASK << CNT_E2E_FAIL_SHIFT)

#define R32_FCTL_DMA_STS_0                  (0x184 >> 2)
#define		INS_QEMPTY_BIT						BIT28
#define		FLH_AG_QENPTY_BIT					BIT27
#define		IBF_PTR_SHIFT						(24)
#define		IBF_PTR_MASK						BIT_MASK(3)
#define		IBF_PTR_SHIFT_MASK					(IBF_PTR_MASK << IBF_PTR_SHIFT)
#define		IBF_AXI_STS_SHIFT					(20)
#define		IBF_AXI_STS_MASK					BIT_MASK(4)
#define		IBF_AXI_STS_SHIFT_MASK				(IBF_AXI_STS_MASK << IBF_AXI_STS_SHIFT)
#define		FLH_IBF_STS_SHIFT					(16)
#define		FLH_IBF_STS_MASK					BIT_MASK(4)
#define		FLH_IBF_STS_SHIFT_MASK				(FLH_IBF_STS_MASK << FLH_IBF_STS_SHIFT)
#define		IBF_FLH_STS_SHIFT					(12)
#define		IBF_FLH_STS_MASK					BIT_MASK(4)
#define		IBF_FLH_STS_SHIFT_MASK				(IBF_FLH_STS_MASK << IBF_FLH_STS_SHIFT)
#define		AXI_IBF_STS_SHIFT					(8)
#define		AXI_IBF_STS_MASK					BIT_MASK(4)
#define		AXI_IBF_STS_SHIFT_MASK				(AXI_IBF_STS_MASK << AXI_IBF_STS_SHIFT)
#define		SCAN_PHS_BIT						BIT7
#define		DMA_INT_BSY_BIT						BIT6
#define		NON_DMA_INT_BSY_BIT					BIT5
#define		INT_PHS_BIT							BIT4
#define		FLH_DMA_CUR_FRM_NUM_SHIFT			(2)
#define		FLH_DMA_CUR_FRM_NUM_MASK			BIT_MASK(2)
#define		FLH_DMA_CUR_FRM_NUM_SHIFT_MASK		(FLH_DMA_CUR_FRM_NUM_MASK << FLH_DMA_CUR_FRM_NUM_SHIFT)
#define		FLH_DMA_PHS_BIT						BIT1
#define		ULTRA_DMA_PHS_BIT					BIT0

#define R32_FCTL_MT_STATE                   (0x188 >> 2)
#define		WP_STATE_SHIFT						(11)
#define		WP_STATE_MASK						BIT_MASK(5)
#define		WP_STATE_SHIFT_MASK					(WP_STATE_MASK << WP_STATE_SHIFT)
#define		WP_STATE_IDLE						(0x00 << WP_STATE_SHIFT)
#define		WP_STATE_WAIT_FPU_CAN_TRIG			(0x13 << WP_STATE_SHIFT)

#define R32_FCTL_ERR_HANDLE_SET             (0x18C >> 2)
#define R8_FCTL_FORCE_DMA_ABORT             (0x18D) // should be byte write
#define		FORCE_DMA_ABORT_BIT					BIT0
#define		SET_DMA_ABORT_EN					SET_BIT0
#define		CLR_DMA_ABORT_DIS					CLR_BIT0

#define		IPERR_INT_CLR_BIT					BIT24
#define		FAQ_CLR_BIT							BIT20
#define		F2E_CLR_BIT							BIT19
#define		E2F_CLR_BIT							BIT18
#define		R2E_CLR_BIT							BIT17
#define		E2R_CLR_BIT							BIT16
#define		SET_FIFO_CLR						(FAQ_CLR_BIT | F2E_CLR_BIT | E2F_CLR_BIT | R2E_CLR_BIT | E2R_CLR_BIT)
//#define		FORCE_DMA_ABORT_BIT					BIT8
//#define		SET_DMA_ABORT_EN					SET_BIT8
//#define		CLR_DMA_ABORT_DIS					CLR_BIT8
#define     NODMA_TERMINATE                     BIT7
#define		RAW_LEN_DSP_EN_BIT					BIT6
#define		FORCE_IBF_PTR_EN_BIT				BIT4
#define		FORCE_IBF_PTR_SHIFT					(1)
#define		FORCE_IBF_PTR_MASK					BIT_MASK(3)
#define		FORCE_IBF_PTR_SHIFT_MASK			(FORCE_IBF_PTR_MASK << FORCE_IBF_PTR_SHIFT)
#define		IBF_RING_EN_BIT						BIT0

#define R32_FCTL_POLLTIMEOUT_LEN_0          (0x190 >> 2)
#define		PTO_LEN_SEL_SHIFT					(29)
#define		PTO_LEN_SEL_MASK					BIT_MASK(2)
#define		PTO_LEN_SEL_SHIFT_MASK				(PTO_LEN_SEL_MASK << PTO_LEN_SEL_SHIFT)
#define		SET_PTO_LEN_SEL_0					(0x00 << PTO_LEN_SEL_SHIFT)
#define		SET_PTO_LEN_SEL_1					(0x01 << PTO_LEN_SEL_SHIFT)
#define		SET_PTO_LEN_SEL_2					(0x02 << PTO_LEN_SEL_SHIFT)
#define		PTO_EN_BIT							BIT28
#define		QOS_PTO_EN_BIT						BIT27
#define		SPDRSM_PTO_EN_BIT					BIT26

#define R32_FCTL_FPU_CE_SET                 (0x194 >> 2)

#define R32_FCTL_FPU_CE_SET_1               (0x194 >> 2)

#define R32_FCTL_POLLCNT_LEN_0              (0x19C >> 2)
#define		POOLCNT_LEN_0_SHIFT					(0)
#define		POOLCNT_LEN_0_MASK					BIT_MASK(25)
#define		POOLCNT_LEN_0_SHIFT_MASK			(POOLCNT_LEN_0_MASK << POOLCNT_LEN_0_SHIFT)
#define		M_SET_POOLCNT_LEN_0(n)				((n) & POOLCNT_LEN_0_SHIFT_MASK) // [24:0]

#define R32_FCTL_POLLCNT_LEN_1              (0x1A0 >> 2)
#define		POOLCNT_LEN_1_SHIFT					(0)
#define		POOLCNT_LEN_1_MASK					BIT_MASK(25)
#define		POOLCNT_LEN_1_SHIFT_MASK			(POOLCNT_LEN_1_MASK << POOLCNT_LEN_1_SHIFT)
#define		M_SET_POOLCNT_LEN_1(n)				((n) & POOLCNT_LEN_1_SHIFT_MASK) // [24:0]

#define R32_FCTL_POLLCNT_LEN_2              (0x1A4 >> 2)
#define		POOLCNT_LEN_2_SHIFT					(0)
#define		POOLCNT_LEN_2_MASK					BIT_MASK(25)
#define		POOLCNT_LEN_2_SHIFT_MASK			(POOLCNT_LEN_2_MASK << POOLCNT_LEN_2_SHIFT)
#define		M_SET_POOLCNT_LEN_2(n)				((n) & POOLCNT_LEN_2_SHIFT_MASK) // [24:0]

#define R32_FCTL_POLTIMEOUT_FLG             (0x1A8 >> 2)
#define		PTO_FLG_SHIFT						(0)
#define		PTO_FLG_MASK						BIT_MASK(8)
#define		PTO_FLG_SHIFT_MASK					(PTO_FLG_MASK << PTO_FLG_SHIFT)

#define R32_FCTL_IFSA_HIGH                  (0x1AC >> 2)
#define R8_FCTL_IN_FSA3                     (0x1AC)
#define R8_FCTL_IN_FSA2                     (0x1AD)
#define R8_FCTL_IN_FSA1                     (0x1AE)
#define R8_FCTL_IN_FSA0                     (0x1AF)
#define		IN_FSA0_SHIFT						(24)
#define		IN_FSA0_MASK						BIT_MASK(8)
#define		IN_FSA1_SHIFT						(16)
#define		IN_FSA1_MASK						BIT_MASK(8)
#define		IN_FSA2_SHIFT						(8)
#define		IN_FSA2_MASK						BIT_MASK(8)
#define		IN_FSA3_SHIFT						(0)
#define		IN_FSA3_MASK						BIT_MASK(8)

#define R32_FCTL_DQ_BIT_REV                 (0x1B0 >> 2)

#define R32_FCTL_CNT1_THR                   (0x1B4 >> 2)
#define		CNT1_THR_SHIFT						(0)
#define		CNT1_THR_MASK						BIT_MASK(18)
#define		CNT1_THR_SHIFT_MASK					(CNT1_THR_MASK << CNT1_THR_SHIFT)
#define		M_SET_CNT_THR(n)					((n) & CNT1_THR_SHIFT_MASK) // [17:0]

#define R32_FCTL_MTQ_QOS_TRIG               (0x1B8 >> 2)
#define		QOS_QUEUE_STA_SHIFT					(16)
#define		QOS_QUEUE_STA_MASK					BIT_MASK(8)
#define		QOS_QUEUE_STA_SHIFT_MASK			(QOS_QUEUE_STA_MASK << QOS_QUEUE_STA_SHIFT)
#define		CLEAR_QOS_QUEUE_SHIFT				(8)
#define		CLEAR_QOS_QUEUE_MASK				BIT_MASK(8)
#define		CLEAR_QOS_QUEUE_SHIFT_MASK			(CLEAR_QOS_QUEUE_MASK << CLEAR_QOS_QUEUE_SHIFT)

#define R32_FCTL_MTQ_QOS_INF_0              (0x1BC >> 2)
#define		MT_QOS_QUEUE3_NON_EXE_NUM_SHIFT		(24)
#define		MT_QOS_QUEUE3_NON_EXE_NUM_MASK		BIT_MASK(8)
#define		MT_QOS_QUEUE2_NON_EXE_NUM_SHIFT		(16)
#define		MT_QOS_QUEUE2_NON_EXE_NUM_MASK		BIT_MASK(8)
#define		MT_QOS_QUEUE1_NON_EXE_NUM_SHIFT		(8)
#define		MT_QOS_QUEUE1_NON_EXE_NUM_MASK		BIT_MASK(8)
#define		MT_QOS_QUEUE0_NON_EXE_NUM_SHIFT		(0)
#define		MT_QOS_QUEUE0_NON_EXE_NUM_MASK		BIT_MASK(8)

#define R32_FCTL_MTQ_QOS_INF_1              (0x1C0 >> 2)
#define		MT_QOS_QUEUE7_NON_EXE_NUM_SHIFT		(24)
#define		MT_QOS_QUEUE7_NON_EXE_NUM_MASK		BIT_MASK(8)
#define		MT_QOS_QUEUE6_NON_EXE_NUM_SHIFT		(16)
#define		MT_QOS_QUEUE6_NON_EXE_NUM_MASK		BIT_MASK(8)
#define		MT_QOS_QUEUE5_NON_EXE_NUM_SHIFT		(8)
#define		MT_QOS_QUEUE5_NON_EXE_NUM_MASK		BIT_MASK(8)
#define		MT_QOS_QUEUE4_NON_EXE_NUM_SHIFT		(0)
#define		MT_QOS_QUEUE4_NON_EXE_NUM_MASK		BIT_MASK(8)

#define R32_FCTL_QOS_DBG_INF_0				 (0x1C4 >> 2)
#define		MT_NOR_ALLOW_SWITCH_SHIFT			(24)
#define		MT_NOR_ALLOW_SWITCH_MASK			BIT_MASK(8)
#define		MT_QOS_ALLOW_SWITCH_SHIFT			(16)
#define		MT_QOS_ALLOW_SWITCH_MASK			BIT_MASK(8)
#define		MT_NOR0_QOS1_SEL_SHIFT				(8)
#define		MT_NOR0_QOS1_SEL_MASK				BIT_MASK(8)
#define		MT_IN_SPD_STATE_SHIFT				(0)
#define		MT_IN_SPD_STATE_MASK				BIT_MASK(8)

#define R32_FCTL_QOS_DBG_INF_1				 (0x1C8 >> 2)
#define		QOS_DBG_INF_SEL_SHIFT				(24)
#define		QOS_DBG_INF_SEL_MASK				BIT_MASK(3)
#define		QOS_DBG_INF_SET_SHIFT				(16)
#define		QOS_DBG_INF_SET_MASK				BIT_MASK(8)
#define		QOS_DBG_INF_CLR_SHIFT				(8)
#define		QOS_DBG_INF_CLR_MASK				BIT_MASK(8)
#define		MT_ONCE_ENTER_SPD_SHIFT				(0)
#define		MT_ONCE_ENTER_SPD_MASK				BIT_MASK(8)

#define R32_FCTL_QOS_DBG_INF_2				 (0x1CC >> 2)
#define		SPD_CNT_CLR_SHIFT					(16)
#define		SPD_CNT_CLR_MASK					BIT_MASK(8)
#define		ROW_FF_EMPTY_SHIFT					(8)
#define		ROW_FF_EMPTY_MASK					BIT_MASK(8)
#define		SPD_STS_VLD_SHIFT					(0)
#define		SPD_STS_VLD_MASK					BIT_MASK(8)

#define R32_FCTL_CNT1_VAL_W				 (0x1D0 >> 2)
#define		CNT1_VAL_W_SHIFT					(0)
#define		CNT1_VAL_W_MASK						BIT_MASK(18)

#define R32_FCTL_OPERATION				 (0x1D4 >> 2)
#define     W_DMA                               BIT16
#define		W_DMA_RO_SHIFT						(8)
#define		W_DMA_RO_MASK						BIT_MASK(8)
#define		W_DMA_RO_SHIFT_MASK					(W_DMA_RO_MASK << W_DMA_RO_SHIFT)
#define		M_CHK_W_DMA_RO_QUEUE(n)				((0x1 << (n)) << W_DMA_RO_SHIFT)
#define		ERASE_RO_SHIFT						(0)
#define		ERASE_RO_MASK						BIT_MASK(8)
#define		ERASE_RO_SHIFT_MASK					(ERASE_RO_MASK << ERASE_RO_SHIFT)
#define		M_CHK_ERASE_RO_QUEUE(n)				((0x1 << (n)) << ERASE_RO_SHIFT)

#define R32_FCTL_PWRFAIL_STOP			(0x1D8 >> 2)
#define		MT_PWRFAIL_STOP_SHIFT				(0)
#define		MT_PWRFAIL_STOP_MASK				BIT_MASK(8)

#define R32_FCTL_MTQ_LOCK				(0x1DC >> 2)
#define		MTQ_LOCK_SOFT_SEL_BIT				BIT26
#define		LBOFS_CHK_EN_BIT					BIT25
#define		MTQ_LOCK_QOS_SEL_BIT				BIT24
#define		MTQ_LOCK_INF_SHIFT					(16)
#define		MTQ_LOCK_INF_MASK					BIT_MASK(8)
#define		MTQ_LOCK_INF_SHIFT_MASK				(MTQ_LOCK_INF_MASK << MTQ_LOCK_INF_SHIFT)
#define		MTQ_LOCK_CLR_SHIFT					(8)
#define		MTQ_LOCK_CLR_MASK					BIT_MASK(8)
#define		MTQ_LOCK_CLR_SHIFT_MASK				(MTQ_LOCK_CLR_MASK << MTQ_LOCK_CLR_SHIFT)
#define		MTQ_SOFT_LOCK_CLR_SHIFT				(0)       // info from H/W: not usable.
#define		MTQ_SOFT_LOCK_CLR_MASK				BIT_MASK(8)
#define		MTQ_LOCK_SET_SHIFT_MASK				(MTQ_SOFT_LOCK_CLR_MASK << MTQ_SOFT_LOCK_CLR_SHIFT)

#define R32_FCTL_FPU_INF				(0x1E4 >> 2)
#define		FPU_ENTRY_STAGE_SEL_SHIFT			(28)
#define		FPU_ENTRY_STAGE_SEL_MASK			BIT_MASK(2)
#define		CLR_LAST_FPU_ENTRY_BIT				BIT27
#define		FPU_ENTRY_Q_SEL_SHIFT				(24)
#define		FPU_ENTRY_Q_SEL_MASK				BIT_MASK(3)
#define		RSM_FPU_BIT							BIT23
#define		SPD_FPU_BIT							BIT22
#define		QOS_FPU_BIT							BIT21
#define		NOR_FUP_BIT							BIT20
#define		LAST_FPU_ENTRY_SHIFT				(0)
#define		LAST_FPU_ENTRY_MASK					BIT_MASK(16)

#define R32_FCTL_CHKSUM				    (0x1E8 >> 2)

#define R32_FCTL_FPU_RD_DAT             (0x1F0 >> 2)

#define R32_FCTL_NTCE                   (0x1F4 >> 2)

#define R32_FCTL_ERROR_USAGE			(0x1FC >> 2)
#define		ERROR_USAGE_SHIFT					(0)
#define		ERROR_USAGE_MASK					BIT_MASK(6)

//=============================================================================
/* Register Offset Definitions of Digital PLL */
//=============================================================================

#define R32_DLL_DLL_CTL						(0x00 >> 2)
#define		MDLL_SRCH_MD_SHIFT						(30)
#define		MDLL_SRCH_MD_MASK						BIT_MASK(2)
#define		MDLL_TRACK_FLT_SHIFT					(28)
#define		MDLL_TRACK_FTL_MASK						BIT_MASK(2)
#define		CH1_MDLL_DL_LINE_CFG_LGC_EN_EXTRA_BIT	BIT18
#define		CH0_MDLL_DL_LINE_CFG_LGC_EN_EXTRA_BIT	BIT18
#define		RDLL180_MD_SHIFT						(19)
#define		RDLL180_MD_MASK							BIT_MASK(2)
#define     AUTO_EXTRA_DLY_VAL_SHIFT				(17)
#define     AUTO_EXTRA_DLY_VAL_MASK					BIT_MASK(2)
#define     AUTO_EXTRA_DLY_VAL_SHIFT_MASK			(AUTO_EXTRA_DLY_VAL_MASK << AUTO_EXTRA_DLY_VAL_SHIFT)
#define		MDLL_RSLT_OFFS_SHIFT					(0)
#define		MDLL_RSLT_OFFS_MASK						BIT_MASK(9)
#define		MDLL_DL_LINE_CFG_EN_EXTRA_BIT			BIT7
#define		MDLL_DL_LINE_CFG_R_BIT					BIT6
#define		SDLLW_AUTO_SWITCH_BIT					BIT5
#define		MDLL_EN_BIT								BIT4
#define		MDLL_RSTJ_BIT							BIT2
#define     RDLL_MD_FW_CTL_SDLL_MODE                BIT1
#define		RDLL_MD_SHIFT							(0)
#define		RDLL_MD_MASK							BIT_MASK(2)
#define		RDLL_MD_STROBE_MODE_BIT					(BIT0)
#define		MDLL_DL_LINE_CFG_EN_EXTRA_SHIFT			(6)
#define		MDLL_DL_LINE_CFG_EN_EXTRA_MASK			BIT_MASK(2)
#define		MDLL_DL_LINE_CFG_EN_EXTRA_SHIFT_MASK	(MDLL_DL_LINE_CFG_EN_EXTRA_MASK << MDLL_DL_LINE_CFG_EN_EXTRA_SHIFT)
#define		TURN_OFF_LARGE_DELAY_BASEMENT			(0x0 << MDLL_DL_LINE_CFG_EN_EXTRA_SHIFT)
#define		TURN_ON_FIRST_LEVEL						(0x1 << MDLL_DL_LINE_CFG_EN_EXTRA_SHIFT)
#define		TURN_ON_SECOND_LEVEL					(0x2 << MDLL_DL_LINE_CFG_EN_EXTRA_SHIFT)
#define		TURN_ON_THIRD_LEVEL						(0x3 << MDLL_DL_LINE_CFG_EN_EXTRA_SHIFT)

#define 	DLL_GROUP_TOTAL_NUM						(2)

#define R32_DLL_MDLL_STA                (0x04 >> 2)
#define		MDLL_OVERFLOW_WR_CH1_BIT		BIT21
#define		MDLL_OVERFLOW_WR_CH0_BIT		BIT20
#define		MDLL_TRACK_RSLT_SHIFT			(8)
#define		MDLL_TRACK_RSLT_MASK			BIT_MASK(9)
#define		MDLL_UNDERFLOW_BIT				BIT3
#define		MDLL_OVERFLOW_RD_BIT			BIT2
#define		MDLL_FAIL_BIT					BIT1
#define		MDLL_LOCK_BIT					BIT0

#define R32_DLL_LOAD_CTL                (0x08 >> 2)
#define		TRIG_AUTO_LOAD_BIT				BIT31
#define		MDLL_AVRG_BIT					BIT30
#define		DATA_LOAD_EN_BIT				BIT28
#define		AUTO_MELL_EN_BIT				BIT26
#define		AUTO_RST_MDLL_EN_BIT			BIT25
#define		AUTO_LOAD_EN_BIT				BIT24
#define		MDLL_RESULT_LOAD_CH1_3			BIT17
#define		MDLL_RESULT_LOAD_CH0_2			BIT16
#define		MDLL_RESULT_LOAD_SHIFT			(16)
#define		MDLL_RESULT_LOAD_MASK			BIT_MASK(8)
#define		AUTO_LOAD_PERIOD_SHIFT			(0)
#define		AUTO_LOAD_PERIOD_MASK			BIT_MASK(16)

#define R32_DLL_PHASE_R_CTL             (0x10 >> 2)
#define		WELL_MD_CH1_SHIFT				(26)
#define		WDLL_MD_CH1_MASK				BIT_MASK(2)
#define		WDLL_MD_CH1_SHIFT_MASK          (WDLL_MD_CH1_MASK << WELL_MD_CH1_SHIFT)
#define		SET_WDLL_MD_1_FLH_STROBE		(0x00 << WELL_MD_CH1_SHIFT)
#define		SET_WDLL_MD_1_FW_DIR_CTL		(0x02 << WELL_MD_CH1_SHIFT)
#define		WDLL_MD_CH0_SHIFT				(24)
#define		WDLL_MD_CH0_MASK				BIT_MASK(2)
#define		WDLL_MD_CH0_SHIFT_MASK			(WDLL_MD_CH0_MASK << WDLL_MD_CH0_SHIFT)
#define		SET_WDLL_MD_0_FLH_STROBE		(0x00 << WDLL_MD_CH0_SHIFT)
#define		SET_WDLL_MD_0_FW_DIR_CTL		(0x02 << WDLL_MD_CH0_SHIFT)
#define		DGRE_CH1_READ_MD_SHIFT			(3)
#define		DGRE_CH1_READ_MD_MASK			BIT_MASK(3)
#define		DGRE_CH0_READ_MD_SHIFT			(0)
#define		DGRE_CH0_READ_MD_MASK			BIT_MASK(3)

#define R32_DLL_PHASE_W_CTL             (0x14 >> 2)
#define		DGRE_CH1_WT_MD_SHIFT			(3)
#define		DGRE_CH1_WT_MD_MASK				BIT_MASK(3)
#define		DGRE_CH0_WT_MD_SHIFT			(0)
#define		DGRE_CH0_WT_MD_MASK				BIT_MASK(3)

#define R32_DLL_GCK_DLY                 (0x1C >> 2)
#define		LGCY_R_DLY_SHIFT				(12)
#define		LGCY_R_DLY_MASK					BIT_MASK(2)
#define		LEG_READ_DELAY_VAL_SHIFT		(0)
#define		LEG_READ_DELAY_VAL_MASK			BIT_MASK(9)

#define R32_DLL_SEL_VAL_0               (0x20 >> 2)    // CH0 CH2
#define		CH0_SDLL_DIR_VAL_WP_SHIFT		(16)
#define		CH0_SDLL_DIR_VAL_WP_MASK		BIT_MASK(9)
#define		CH0_SDLL_DIR_VAL_RP_SHIFT		(0)
#define		CH0_SDLL_DIR_VAL_RP_MASK		BIT_MASK(9)

#define R32_DLL_SEL_VAL_1               (0x24 >> 2)    // CH1 CH3
#define		CH1_SDLL_DIR_VAL_WP_SHIFT		(16)
#define		CH1_SDLL_DIR_VAL_WP_MASK		BIT_MASK(9)
#define		CH1_SDLL_DIR_VAL_RP_SHIFT		(0)
#define		CH1_SDLL_DIR_VAL_RP_MASK		BIT_MASK(9)

#define R32_DLL_SDLL_VAL_0              (0x40 >> 2)    // CH0 CH2
#define		CH0_SDLL_DLY_CELL_VAL_WP_SHIFT	(16)
#define		CH0_SDLL_DLY_CELL_VAL_WP_MASK	BIT_MASK(9)
#define		CH0_SDLL_DLY_CELL_VAL_RP_SHIFT	(0)
#define		CH0_SDLL_DLY_CELL_VAL_RP_MASK	BIT_MASK(9)

#define R32_DLL_SDLL_VAL_1              (0x44 >> 2)    // CH1 CH3
#define		CH1_SDLL_DLY_CELL_VAL_WP_SHIFT	(16)
#define		CH1_SDLL_DLY_CELL_VAL_WP_MASK	BIT_MASK(9)
#define		CH1_SDLL_DLY_CELL_VAL_RP_SHIFT	(0)
#define		CH1_SDLL_DLY_CELL_VAL_RP_MASK	BIT_MASK(9)


#define	R32_DLL_SDLL_OFFSET_CH0		(0x60 >> 2)	// e13 : R32_DLL_SDLL_OFFSET_CE0_0
#define	R32_DLL_SDLL_OFFSET_CH1		(0x64 >> 2)
#define		SDLL_DLY_OFFS_VAL_WP_SHIFT	(16)
#define		SDLL_DLY_OFFS_VAL_WP_MASK	BIT_MASK(9)
#define		SDLL_DLY_OFFS_VAL_RP_SHIFT	(0)
#define		SDLL_DLY_OFFS_VAL_RP_MASK	BIT_MASK(9)

#define	R32_DLL_LGC_SEL_VAL_CH0	    	(0x98 >> 2)
#define	R32_DLL_LGC_SEL_VAL_CH1	    	(0x9C >> 2)
#define		DLL_LGC_SEL_VAL_SHIFT	    (0)
#define		DLL_LGC_SEL_VAL_MASK	    BIT_MASK(9)

#define R32_DLL_DLY_LINE_COMPENSATION_CFG		(0xAC >> 2)
#define     DLL_SFS_INI_SEL_SHIFT       (0)
#define     DLL_SFS_INI_SEL_MASK        BIT_MASK(9)
#define     DLL_PDLY_INI_SEL_SHIFT      (12)
#define     DLL_PDLY_INI_SEL_MASK       BIT_MASK(9)
#define     DLL_MBASE_SHIFT             (24)
#define     DLL_MBASE_MASK              BIT_MASK(8)

#define R32_DLL_SEL_D180_VAL_0          (0xB0 >> 2)
#define     CH0_F180_SDLL_DIR_VAL_RP_SHIFT      (0)
#define     CH0_F180_SDLL_DIR_VAL_RP_MASK       BIT_MASK(9)

#define R32_DLL_SEL_D180_VAL_1          (0xB4 >> 2)
#define     CH1_F180_SDLL_DIR_VAL_RP_SHIFT      (0)
#define     CH1_F180_SDLL_DIR_VAL_RP_MASK       BIT_MASK(9)


#define	R32_DLL_D180_PHASE_R_CTL	(0xB8 >> 2)
#define		D180_READ_MD_CH0_SHIFT	    (0)
#define		D180_READ_MD_CH0_MASK	    BIT_MASK(3)
#define		D180_READ_MD_CH1_SHIFT	    (3)
#define		D180_READ_MD_CH1_MASK	    BIT_MASK(3)

#define	R32_DLL_D180_SDLL_OFFSET_CH0	(0xBC >> 2)
#define	R32_DLL_D180_SDLL_OFFSET_CH1 	(0xC0 >> 2)
#define		DLL_D180_SDLL_OFFSET_SHIFT	(0)
#define		DLL_D180_SDLL_OFFSET_MASK	BIT_MASK(9)

#define R32_DLL_DLY_HOLD_CNT            (0xC4)

//===========================================================================================================================

#endif // _FIP_REG_H_

