#ifndef _RETRY_INTEL_QLC_E13_NEUTRAL_HB_H_
#define _RETRY_INTEL_QLC_E13_NEUTRAL_HB_H_
/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */

#include "env.h"

#if (((PS5013_EN) || (PS5017_EN)) && (CONFIG_FLASH_TYPE == FLASH_TYPE_INTEL_3D_QLC) && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER))
/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */

#define HBIT_RETRY_INTEL_N38A_QLC_1024G_STEP_NUM			(15 + 1)
#define HBIT_RETRY_INTEL_N38A_SLC_1024G_STEP_NUM			(15 + 1)

/*
 * ---------------------------------------------------------------------------------------------------
 *  enum
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */

#endif /* ((PS5013_EN) && ((FLASH_SANDISK_BICS5_TLC == FW_CATEGORY_FLASH)||(FW_CATEGORY_FLASH == FLASH_SANDISK_BICS6_TLC)) && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER)) */
#endif /* _RETRY_INTEL_QLC_E13_NEUTRAL_HB_H_ */
