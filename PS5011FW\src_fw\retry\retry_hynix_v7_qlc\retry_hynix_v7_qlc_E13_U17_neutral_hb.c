#include <retry/retry_hynix_v7_qlc/retry_hynix_v7_qlc_E13_U17_neutral_hb.h>
#include "retry/retry.h"
#include "retry/retry_api.h"
#include "retry/retry_hb.h"
#include "hal/pic/uart/uart_api.h"
#include "retry/retry_version_api.h" // Retry Version
#include "table/initinfo_vt/initinfo_vt.h" // VT
#include "ftl/ftl_api.h" // FW Timer
#include "hal/fip/fpu.h"
#include "table/sys_block/sys_block_api.h" //RetryInitSystemBlockReadRetryTable
//V7 USE V6 RDT Setting
#if (((PS5013_EN) || (PS5017_EN)) && (FLASH_V7_QLC == FW_CATEGORY_FLASH) && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER)) //Reip
/*
 * ----------------------------------------------------------------------------
 *   Definitions
 * ----------------------------------------------------------------------------
 */

#define RETRY_HYNIX_V6_SLC_RRT_BASE		(DBUF_RETRY_RR_TABLE)
#define RETRY_HYNIX_V6_TLC_RRT_BASE		(DBUF_RETRY_RR_TABLE + DEF_KB(2))

#define OTP_RR_CNT_SITE                 (8)
#define OTP_RR_REG_CNT_SITE             (16)

#define OTP_DEFAULT_RR_CNT              (50)
#define OTP_DEFAULT_RR_REG_CNT_SLC      (HBIT_RETRY_SLC_FEA_DATA_NUM)
#define OTP_DEFAULT_RR_REG_CNT_TLC      (HBIT_RETRY_TLC_FEA_DATA_NUM)
#define OTP_DEFAULT_RR_REG_CNT_QLC      (HBIT_RETRY_QLC_FEA_DATA_NUM) //Reip

#define OTP_DEFAULT_SET_NUM             (8)
#define OTP_NORMAL_SEQ                  (0)
#define OTP_INVERSE_SEQ                 (1)
#define OTP_SEQ_CNT_PER_SET             (2)

/*
 * ----------------------------------------------------------------------------
 *   Enum
 * ----------------------------------------------------------------------------
 */

/*
 * ----------------------------------------------------------------------------
 *   Data Types
 * ----------------------------------------------------------------------------
 */

typedef struct {
	U8 ubSeq[OTP_DEFAULT_SET_NUM][OTP_SEQ_CNT_PER_SET][OTP_DEFAULT_RR_CNT * OTP_DEFAULT_RR_REG_CNT_QLC]; //Reip
} OTPSeq_t;
/*
 * ----------------------------------------------------------------------------
 *   Macros
 * ----------------------------------------------------------------------------
 */

/*
 * ----------------------------------------------------------------------------
 *   Extern Global Variables
 * ----------------------------------------------------------------------------
 */
extern U8 gubHynixtADLDelay;
extern U8 gubHynixtWHRDelay;
/*
 *	Hynix V7 QLC //Reip L/M/U/T
 *  92h: L, 93h: M, 94h: U, 95h: T, 96h: U, 97h: L, 98h: M, 99h: L, 9Ah: U, 9Bh: T, 9Ch: L, 9Dh: T, 9Eh: M, 9Fh: T, A0h: U
 */
const U8 gubRetryReadRetryTableParameterIdxToPageType[HBIT_RETRY_SELECT_NON_SLC_FEA_DATA_NUM] = {
	HB_LRU_LOWER, HB_LRU_MIDDLE, HB_LRU_UPPER, HB_LRU_TOP, HB_LRU_UPPER, HB_LRU_LOWER, HB_LRU_MIDDLE, HB_LRU_LOWER, HB_LRU_UPPER, HB_LRU_TOP, HB_LRU_LOWER, HB_LRU_TOP, HB_LRU_MIDDLE, HB_LRU_TOP, HB_LRU_UPPER
};
/*
 *	Hynix V7 QLC //Reip
 *  Transition 0 to 15
 *  P0 | P1 | P2 | P3 | P4 | P5 | P6 | P7 | P8 | P9 | P10 | P11 | P12 | P13 | P14
 *  LOW| MID| UP | TOP| UP | LOW| MID| LOW| UP | TOP| LOW | TOP | MID | TOP | UP
 */
const U8 gubRetryTransitionToPageType[HBIT_RETRY_SELECT_NON_SLC_FEA_DATA_NUM] = {
	LOWER_PAGE_SEL, MIDDLE_PAGE_SEL, UPPER_PAGE_SEL, TOP_PAGE_SEL, UPPER_PAGE_SEL, LOWER_PAGE_SEL, MIDDLE_PAGE_SEL, LOWER_PAGE_SEL, UPPER_PAGE_SEL, TOP_PAGE_SEL, LOWER_PAGE_SEL, TOP_PAGE_SEL, MIDDLE_PAGE_SEL, TOP_PAGE_SEL, UPPER_PAGE_SEL
};

/*
 * ----------------------------------------------------------------------------
 *   Static Global Variables
 * ----------------------------------------------------------------------------
 */

/*
 * ----------------------------------------------------------------------------
 *   Private Prototypes
 * ----------------------------------------------------------------------------
 */

/*
 * ----------------------------------------------------------------------------
 *   Private Prototypes
 * ----------------------------------------------------------------------------
 */
INLINE void HBRetryInitRegisterRetryTable(void);
INLINE void HBRetryInitAdjustVthFPU(void);
INLINE U8 HynixGetRRTFromOTP(U8 ubCh, U8 ubBank, U8 ubLun, U8 ubSLCMode, U8 *pubReadRetryTable);//Dylan build V6 RDT (Add HB flow)
INLINE U16 HynixV6HBRetrySetFeatureFPU(U8 ubStep, U8 ubPageType, U8 ubSLCMode);
INLINE U16 HynixV6HBRetryCheckFeatureFPU(U8 ubStep, U8 ubPageType, U8 ubSLCMode);
/*
 * ----------------------------------------------------------------------------
 *   Private
 * ----------------------------------------------------------------------------
 */

void HBRetryInitRegisterRetryTable(void)
{
	gpHBParameterArray[FPL_ADDR_TLC_RULE].pubRetryTable = (U8 *)(HYNIX_TLC_RRT_BASE);			// QLC
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubTotalRetryCnt = (gpIDPage->ubRetryGroupCount ? (gpIDPage->ubRetryGroupCount + 1) : HBIT_RETRY_V6_TLC_512G_STEP_NUM);
	gpHBParameterArray[FPL_ADDR_SLC_RULE].pubRetryTable = (U8 *)(HYNIX_SLC_RRT_BASE);			// SLC
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubTotalRetryCnt = (gpIDPage->ubD1RetryGroupCount ? (gpIDPage->ubD1RetryGroupCount + 1) : HBIT_RETRY_V6_SLC_512G_STEP_NUM);
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubCurrentStep = HB_READ_START_STEP;
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubLastSuccessIndex = 0;
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubRetryParameterSize = (gpIDPage->ubRetryParamCount ? gpIDPage->ubRetryParamCount : HBIT_RETRY_QLC_FEA_DATA_NUM);	//Reip
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubParameterNumPerFPU = (gpIDPage->ubRetryParamCount ? gpIDPage->ubRetryParamCount : HBIT_RETRY_QLC_FEA_DATA_NUM); // Hynix only need 1 SetFeature CMD to mod FA //Reip
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubCurrentStep = HB_READ_START_STEP;
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubLastSuccessIndex = 0;
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubRetryParameterSize = (gpIDPage->ubD1RetryParamCount ? gpIDPage->ubD1RetryParamCount : HBIT_RETRY_SLC_FEA_DATA_NUM);
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubParameterNumPerFPU = (gpIDPage->ubD1RetryParamCount ? gpIDPage->ubD1RetryParamCount : HBIT_RETRY_SLC_FEA_DATA_NUM);;
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubScratchTableIdx = INVALID_RETRY_TABLE_INDEX;
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubScratchTableIdx = INVALID_RETRY_TABLE_INDEX;
	/*
		M_UART(RDT_TEST_, " \r\nubD3RetryGroupCnt=%d, ubD3RetryParamCnt=%d",
			gpHBParameterArray[FPL_ADDR_TLC_RULE].ubTotalRetryCnt,
			gpHBParameterArray[FPL_ADDR_TLC_RULE].ubRetryParameterSize);
		M_UART(RDT_TEST_, " \r\nubD1RetryGroupCnt=%d, ubD1RetryParamCnt=%d",
			gpHBParameterArray[FPL_ADDR_SLC_RULE].ubTotalRetryCnt,
			gpHBParameterArray[FPL_ADDR_SLC_RULE].ubRetryParameterSize);
	*/
}

void HBRetryInitAdjustVthFPU(void)
{
	// Register set feature fpu
	gFpuEntryList.fpu_set_feature[0] = FPU_ADR_GEN;
	gFpuEntryList.fpu_set_feature[1] = FPU_CMD(0x78);
	gFpuEntryList.fpu_set_feature[2] = FPU_ADR(3);
	gFpuEntryList.fpu_set_feature[3] = FPU_DLY(0x10);
	gFpuEntryList.fpu_set_feature[4] = FPU_CMD(0x36); // Set feature cmd
	gFpuEntryList.fpu_set_feature[5] = FPU_ADR_1B(0x00);
	gFpuEntryList.fpu_set_feature[6] = FPU_DAT_W_1B(0x01);
	gFpuEntryList.fpu_set_feature[48] = FPU_ADR_1B(0x00);
	gFpuEntryList.fpu_set_feature[49] = FPU_DAT_W_1B(0x00);
	gFpuEntryList.fpu_set_feature[50] = FPU_CMD(0x16); //Reip
	gFpuEntryList.fpu_set_feature[51] = FPU_END;


	// Register read and compare feature data fpu
	gFpuEntryList.fpu_read_and_compare_feature_data[0] = FPU_ADR_GEN;
	gFpuEntryList.fpu_read_and_compare_feature_data[1] = FPU_CMD(0x78);
	gFpuEntryList.fpu_read_and_compare_feature_data[2] = FPU_ADR(3);
	gFpuEntryList.fpu_read_and_compare_feature_data[3] = FPU_DLY(0x10);
	gFpuEntryList.fpu_read_and_compare_feature_data[4] = FPU_CMD(0x37); // Get feature cmd
	gFpuEntryList.fpu_read_and_compare_feature_data[5] = FPU_DLY(0x10);
}

U8 RetryInitGenHynixRRT(U32 ulTLCReadRetryTableBufAddr, U32 ulSLCReadRetryTableBufAddr)
{
	//UartPrintf("\r\n[HB debug TLC %x, SLC %x]", ulTLCReadRetryTableBufAddr, ulSLCReadRetryTableBufAddr);
	U8 ubCh, ubBank, ubLun, ubSLCMode, ubGlobalDie;
	SystemBlkReadRetry_t *pSystemBlkReadRetryTable = (SystemBlkReadRetry_t *)ulTLCReadRetryTableBufAddr;
	SystemBlkSLCReadRetry_t *pSystemBlkSLCReadRetryTable = (SystemBlkSLCReadRetry_t *) ulSLCReadRetryTableBufAddr;

	for (ubLun = 0; ubLun < gFlhEnv.ubLUNperTarget; ubLun++) {
		for (ubCh = 0; ubCh < gFlhEnv.ubChannelExistNum; ubCh++) {
			for (ubBank = 0; ubBank < gFlhEnv.ubCENumberInCh[ubCh]; ubBank++) {
				FlaCEControl(ubCh, ubBank, ENABLE);
				ubGlobalDie = M_GLOBAL_DIE_IDX(ubLun, ubCh, ubBank,  BIT(Log2Cal((U32)gFlhEnv.ubLUNperTarget)), BIT(Log2Cal((U32)gFlhEnv.ubChannelExistNum)));
				M_FW_ASSERT(ASSERT_RETRY_0x0872, ubGlobalDie < RETRY_MAX_GLOBAL_DIE_NUM);
				M_UART(RETRY_, "\nubGlobalDie:%d", ubGlobalDie);
				for (ubSLCMode = 0; ubSLCMode < 2; ubSLCMode++) {
					U8 *pubReadRetryTable = (U8 *)(ubSLCMode ? pSystemBlkSLCReadRetryTable->ubSLCReadRetryTable[ubGlobalDie][1] : pSystemBlkReadRetryTable->ubReadRetryTable[ubGlobalDie][1]);
					M_UART(RETRY_, ubSLCMode ? "\nSLC\n" : "\nTLC\n");
					if (!HynixGetRRTFromOTP(ubCh, ubBank, ubLun, ubSLCMode, pubReadRetryTable)) {
						FlaCEControl(ubCh, ubBank, DISABLE);
						return FALSE;
					}
				}
				FlaCEControl(ubCh, ubBank, DISABLE);
			}
		}
	}
	return TRUE;
}

U8 HynixGetRRTFromOTP(U8 ubCh, U8 ubBank, U8 ubLun, U8 ubSLCMode, U8 *pubReadRetryTable) //Reip
{
	U32 uli, ulj, ulEnd, ulDat, ulSet, ulIsInverse, ulSite, ulSeqCnt;
	U8 ubTmp, ubOneCounter, ubTmp1[16];
	OTPSeq_t *OTPSeq = (OTPSeq_t *) OTP_MAN_BASE;
	M_FIP_SET_LEGACY_MODE(ubCh);

	M_FIP_ASSIGN_PIO_CMD(ubCh, 0xFF);
	IdlePC(300);
	while (BUSY == M_FIP_CHK_RBY_AND_CHANNEL(ubCh));

	M_FIP_ASSIGN_PIO_CMD(ubCh, 0x78);
	M_FIP_ASSIGN_PIO_ADDR(ubCh, 0x00);
	M_FIP_ASSIGN_PIO_ADDR(ubCh, 0x00);

	M_FIP_ASSIGN_PIO_ADDR(ubCh, ubLun << 6);

	M_FIP_ASSIGN_PIO_CMD(ubCh, 0x17);
	M_FIP_ASSIGN_PIO_CMD(ubCh, 0x04);
	M_FIP_ASSIGN_PIO_CMD(ubCh, 0x19);

	M_FIP_ASSIGN_PIO_CMD(ubCh, 0xDA);
	M_FIP_ASSIGN_PIO_CMD(ubCh, 0x00);
	M_FIP_ASSIGN_PIO_ADDR(ubCh, 0x00);
	M_FIP_ASSIGN_PIO_ADDR(ubCh, 0x00);

	if (ubSLCMode) {
		M_FIP_ASSIGN_PIO_ADDR(ubCh, 0x4C);		//QLC
	}
	else {
		M_FIP_ASSIGN_PIO_ADDR(ubCh, 0x4A);		//QLC
	}

	M_FIP_ASSIGN_PIO_ADDR(ubCh, 0x13);			//QLC
	M_FIP_ASSIGN_PIO_ADDR(ubCh, ubLun << 6);

	M_FIP_ASSIGN_PIO_CMD(ubCh, 0x30);
	IdlePC(1000);
	while (BUSY == M_FIP_CHK_RBY_AND_CHANNEL(ubCh));

	for (uli = 0; uli < OTP_RR_REG_CNT_SITE; uli ++) {
		ubTmp1[uli] = M_FIP_GET_PIO_DATA(ubCh); // just pop, don't care
	}

	if ((ubTmp1[0] & 0xff) != 50) {
		M_UART(RETRY_, "[OTP] First byte [%d] != 50\r\n", (U32)ubTmp1[0]);
#if (BURNER_MODE_EN)
		guwDebugBurnerErrorCode = 0xE191;
#endif
	}

	if ((ubTmp1[8] & 0xff) != (ubSLCMode ? 1 : 15)) {		//QLC Reip
		M_UART(RETRY_, "[OTP] RR Reg. [%b] not match\r\n", (ubTmp1[8] & 0xff));
#if (BURNER_MODE_EN)
		guwDebugBurnerErrorCode = 0xE192;
#endif
	}
	ulSet = 0;
	ulIsInverse = 0;
	ulSite = 0;
	ulSeqCnt = (ubSLCMode) ? (OTP_DEFAULT_RR_CNT * OTP_DEFAULT_RR_REG_CNT_SLC) : (OTP_DEFAULT_RR_CNT * OTP_DEFAULT_RR_REG_CNT_QLC); //Reip
	ulEnd = ((ulSeqCnt << 1 /* normal and inverse */) << 3/* 8 set */);

	/* read data from OTP */
	for (uli = 0; uli < ulEnd;) {
		ulDat = M_FIP_GET_PIO_DATA(ubCh);
		OTPSeq->ubSeq[ulSet][ulIsInverse][ulSite] =  ulDat & 0xff;
		ulSite++;
		uli++;
		if (!(uli % ulSeqCnt)) {
			ulIsInverse ^= BIT0;
			ulSite = 0;
			if (!ulIsInverse) {
				ulSet += 1;
			}
		}
	}

	/* RR in OTP end */
	M_FIP_ASSIGN_PIO_CMD(ubCh, 0xFF);
	IdlePC(300);
	while (BUSY == M_FIP_CHK_RBY_AND_CHANNEL(ubCh));
	/*
	 * majority check
	 */
	for (uli = 0; uli < ulSeqCnt; uli++) {
		ubTmp = 0;
		for (ulj = 0; ulj < 8; ulj++) { // 8 bits
			ubOneCounter = 0;
			for (ulSet = 0; ulSet < OTP_DEFAULT_SET_NUM; ulSet ++) {
				if (OTPSeq->ubSeq[ulSet][OTP_NORMAL_SEQ][uli] & BIT(ulj)) {
					ubOneCounter ++;
				}
			}

			if (ubOneCounter == 4) {
				// go check inverse
				ubOneCounter = 0;
				for (ulSet = 0; ulSet < OTP_DEFAULT_SET_NUM; ulSet ++) {
					if ((OTPSeq->ubSeq[ulSet][OTP_INVERSE_SEQ][uli]) & BIT(ulj)) {
						ubOneCounter ++;
					}
				}

				if (ubOneCounter == 4) {
#if (BURNER_MODE_EN)
					guwDebugBurnerErrorCode = 0xE193;
#endif
					return FALSE;
				}
				else if (ubOneCounter < 4) {   // inverse case
					ubTmp |= BIT(ulj);
				}
			}
			else if (ubOneCounter > 4) {	// choose most repeat bit (normal)
				ubTmp |= BIT(ulj);
			}
		}
		if (ubSLCMode) {

			//UartPrintf("uli = %d, value = %d", uli, ubTmp);
		}
		pubReadRetryTable[uli] = ubTmp;
		M_UART(RETRY_, "%b ", ubTmp);
	}
	return TRUE;
}

U16 HynixV6HBRetrySetFeatureFPU(U8 ubStep, U8 ubPageType, U8 ubSLCMode)
{
	FPL_HBIT_RETRY_PARAM_STRUCT_PTR pParam = &(gpHBParameterArray[ubSLCMode]);
	U8 ubValidParameterSize = pParam->ubParameterNumPerFPU;
	U8 ubi;
	U16 uwFPUPtr;
	U16 *puwFPU;
	U8 ubFeatureData[HBIT_RETRY_QLC_FEA_DATA_NUM] = {0};	//Reip
	U8 ubFPUIdx;

	if (ubStep < pParam->ubTotalRetryCnt) { //Prevent Access Array Over flow
		U16 uwTableOffset = ubStep * pParam->ubRetryParameterSize;
		// Copy set feature from HB retry table
		memcpy((void *)ubFeatureData, (void *)&pParam->pubRetryTable[uwTableOffset], ubValidParameterSize);
	}
#if (RETRY_HB_FEEDBACK_CACULATE_READ_LEVEL_TO_LRU)
	// Check if CurrentStep is "SB Pass Read Level", ubTotalRetryCnt = RR_Table + Scratch_Table
	else {
		U8 ubReadLevelTableIdx = ubStep - gpHBParameterArray[FPL_ADDR_TLC_RULE].ubTotalRetryCnt; // Get Node Idx
		RETRY_HB_LRU_FEEDBACK_READ_LEVEL_TABLE_STRUCT_PTR pFeedBackReadLevelTable = M_RETRY_GET_FEEDBACK_CACULATE_READ_LEVEL_TABLE(gHBParamMgr.ubSLCMode, gHBParamMgr.ubGlobalDie, gHBParamMgr.ubPageType);
		U8 ubValidIdx = 0;
		for (ubi = 0; ubi < ubValidParameterSize; ubi++) {
			if (ubPageType == M_RETRY_GET_PAGE_TYPE_BY_READ_RETRY_TABLE_PARAMETER_IDX(ubi)) {
				ubFeatureData[ubi] = pFeedBackReadLevelTable[ubReadLevelTableIdx].ubSBPassReadLevelTable[ubValidIdx]; // Same Page Type Read Level Can't Cross Two Round(89/8A).
				++ubValidIdx;
			}
		}
	}
#endif /* (RETRY_HB_FEEDBACK_CACULATE_READ_LEVEL_TO_LRU) */
	M_HB_DEBUG_UART(" [HB] set feature:");
	// Get FPU PTR
	uwFPUPtr = FPU_PTR_OFFSET(fpu_set_feature);
	puwFPU = (U16 *)(IRAM_BASE + uwFPUPtr);

	// Set parameter cmd, already fill FPU[0] in init state
	//puwFPU[0] = FPU_CMD(0x36);
	//puwFPU += 5;
	puwFPU += 7;

	ubFPUIdx = 0;
	for (ubi = 0; ubi < ubValidParameterSize; ++ubi) {// Reip
		if (ubSLCMode || ubPageType == M_RETRY_GET_PAGE_TYPE_BY_READ_RETRY_TABLE_PARAMETER_IDX(ubi)) {
			// Feature address
			// SLC A1h
			// QLC 92h, 93h, 94h, 95h, 96h, 97h, 98h, 99h, 9Ah, 9Bh, 9Ch, 9Dh, 9Eh, 9Fh, A0h
			puwFPU[ubFPUIdx++] = FPU_ADR_1B(0x92 + ubi + ((ubSLCMode << 4) - ubSLCMode));

			// Delay (tADL)
			puwFPU[ubFPUIdx++] = FPU_DLY(gubHynixtADLDelay);
			// Set feature data
			puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(ubFeatureData[ubi]);
			M_HB_DEBUG_UART("ubFeatureData[%d]=[%x]", ubi, ubFeatureData[ubi]);
		}
	}
	// FPU end
	puwFPU[ubFPUIdx++] = FPU_CMD(0x16);
	puwFPU[ubFPUIdx++] = FPU_END;

	return uwFPUPtr;
}

U16 HynixV6HBRetryCheckFeatureFPU(U8 ubStep, U8 ubPageType, U8 ubSLCMode)
{
	FPL_HBIT_RETRY_PARAM_STRUCT_PTR pParam =  &(gpHBParameterArray[ubSLCMode]);
	U8 ubValidParameterSize = pParam->ubParameterNumPerFPU;
	U8 ubi;
	U16 uwFPUPtr;
	U16 *puwFPU;
	U8 ubFeatureData[HBIT_RETRY_QLC_FEA_DATA_NUM] = {0};	//Reip
	U8 ubFPUIdx;

	if (ubStep < pParam->ubTotalRetryCnt) { //Prevent Access Array Over flow
		U16 uwTableOffset = ubStep * pParam->ubRetryParameterSize;
		// Copy set feature from HB retry table
		memcpy((void *)ubFeatureData, (void *)&pParam->pubRetryTable[uwTableOffset], ubValidParameterSize);
	}
#if (RETRY_HB_FEEDBACK_CACULATE_READ_LEVEL_TO_LRU)
	// Check if CurrentStep is "SB Pass Read Level", ubTotalRetryCnt = RR_Table + Scratch_Table
	else {
		U8 ubReadLevelTableIdx = ubStep - gpHBParameterArray[FPL_ADDR_TLC_RULE].ubTotalRetryCnt; // Get Node Idx
		RETRY_HB_LRU_FEEDBACK_READ_LEVEL_TABLE_STRUCT_PTR pFeedBackReadLevelTable = M_RETRY_GET_FEEDBACK_CACULATE_READ_LEVEL_TABLE(gHBParamMgr.ubSLCMode, gHBParamMgr.ubGlobalDie, gHBParamMgr.ubPageType);
		U8 ubValidIdx = 0;
		for (ubi = 0; ubi < ubValidParameterSize; ubi++) {
			if (ubPageType == M_RETRY_GET_PAGE_TYPE_BY_READ_RETRY_TABLE_PARAMETER_IDX(ubi)) {
				ubFeatureData[ubi] = pFeedBackReadLevelTable[ubReadLevelTableIdx].ubSBPassReadLevelTable[ubValidIdx]; // Same Page Type Read Level Can't Cross Two Round(89/8A).
				++ubValidIdx;
			}
		}
	}
#endif /* (RETRY_HB_FEEDBACK_CACULATE_READ_LEVEL_TO_LRU) */
	M_HB_DEBUG_UART(" [HB] get feature:");

	// Get FPU PTR
	uwFPUPtr = FPU_PTR_OFFSET(fpu_read_and_compare_feature_data);
	puwFPU = (U16 *)(IRAM_BASE + uwFPUPtr);

	// Get parameter cmd, already fill FPU[0] in init state
	//puwFPU[0] = FPU_CMD(0x37);
	puwFPU += 6;

	if ((uwFPUPtr & 0x3) == 0x2) {
		// For FPU_DAT_R_CMP, make the FPU sequence align 4B
		puwFPU++;
	}

	ubFPUIdx = 0;
	for (ubi = 0; ubi < ubValidParameterSize; ++ubi) {	//Reip
		if (ubSLCMode || ubPageType == M_RETRY_GET_PAGE_TYPE_BY_READ_RETRY_TABLE_PARAMETER_IDX(ubi)) {
			// Feature address
			// SLC A1h
			// QLC 92h, 93h, 94h, 95h, 96h, 97h, 98h, 99h, 9Ah, 9Bh, 9Ch, 9Dh, 9Eh, 9Fh, A0h
			puwFPU[ubFPUIdx++] = FPU_ADR_1B(0x92 + ubi + ((ubSLCMode << 4) - ubSLCMode));
			// Delay (tWHR)
			puwFPU[ubFPUIdx++] = FPU_DLY(gubHynixtWHRDelay);
			// Get feature data
			puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(ubFeatureData[ubi]);
			//UartPrintf("ubFeatureData[%d]=[%x]", ubi, ubFeatureData[ubi]);
			puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);
		}
	}
	// FPU end
	puwFPU[ubFPUIdx++] = FPU_END;

	return uwFPUPtr;
}

/*
 * ------------------------------------
 * 		Over Wirte Weak Function
 * ------------------------------------
 */
void HBRetryInitParameter(void)
{
	switch (gpOtherInfo->ubMakerCode) {
	case ID_HYNIX:
		switch (gpOtherInfo->ubProcess) {
		case RETRY_HYNIX_FLASH_PROCESS_V7_1024G:	//Reip
		case RETRY_HYNIX_FLASH_PROCESS_V7_512G://Dylan add V7 RDT HB Retry
		case RETRY_HYNIX_FLASH_PROCESS_V6_512G:
		case RETRY_HYNIX_FLASH_PROCESS_V6_1T:
			HBRetryInitRegisterRetryTable();
			HBRetryInitAdjustVthFPU();
			break;

		default:
			M_FW_ASSERT(ASSERT_RETRY_0x0870, FALSE);
			break;
		}

		break;

	default:
		M_FW_ASSERT(ASSERT_RETRY_0x0871, FALSE);
		break;
	}
}

void HBRetryPreconditionSetFeaturePass_Callback(U8 ubMTIdx)
{
	gHBParamMgr.ubSetFeatureDoneMTCnt++;

	if (gHBParamMgr.ubSetFeatureDoneMTCnt == gHBParamMgr.ubSetFeatureToDoMTCnt) { // All set feature MT done
		gHBParamMgr.ubGetFeatureDoneMTCnt = 0;
		if (NCS_V2_EN && gSystmAreaFWSettingFromInfoBlk.Init.Flag.btNCS2En) {
			gHBTask.ubState = RETRY_HB_STATE_SEARCH_PASS_STEP_READ;
		}
		else {
			gHBTask.ubState = RETRY_HB_STATE_CHECK_PRECONDITION; // Check set feature result
		}
	}
	else {
		gHBTask.ubState = RETRY_HB_STATE_PRECONDITION; // Execute remain set feature MT
	}
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_HB_RETRY;
}

void HBRetryPostconditionSetFeaturePass_Callback(U8 ubMTIdx)
{
	gHBParamMgr.ubSetFeatureDoneMTCnt++;

	if (gHBParamMgr.ubSetFeatureDoneMTCnt == gHBParamMgr.ubSetFeatureToDoMTCnt) {
		gHBParamMgr.ubGetFeatureDoneMTCnt = 0;
		if (NCS_V2_EN && gSystmAreaFWSettingFromInfoBlk.Init.Flag.btNCS2En) {
			gHBTask.ubState = RETRY_HB_STATE_POSTCONDITION_DONE_RESET_FLASH; // Check set feature result
		}
		else {
			gHBTask.ubState = RETRY_HB_STATE_CHECK_POSTCONDITION; // Check set feature result
		}
	}
	else {
		gHBTask.ubState = RETRY_HB_STATE_POSTCONDITION;
	}
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_HB_RETRY;
}

U16 HBRetrySelectResetCMDFPU(void)
{
	return FPU_PTR_OFFSET(fpu_entry_reset_fc);
}

U16 HBRetryPreconditionSetFeatureFPU(void)
{
	return HynixV6HBRetrySetFeatureFPU(gHBParamMgr.pParam->ubCurrentStep, gHBParamMgr.ubPageType, gHBParamMgr.ubSLCMode);
}

U16 HBRetryPreconditionCheckFeatureFPU(void)
{
	return HynixV6HBRetryCheckFeatureFPU(gHBParamMgr.pParam->ubCurrentStep, gHBParamMgr.ubPageType, gHBParamMgr.ubSLCMode);
}

U16 HBRetryPostconditionSetFeatureFPU(void)
{
	HB_LRU_TABLE_STRUCT_PTR pLRUTable = M_RETRY_GET_LRU_TABLE(gHBParamMgr.ubSLCMode, gHBParamMgr.ubGlobalDie, gHBParamMgr.ubPageType);
	return HynixV6HBRetrySetFeatureFPU(pLRUTable[0].param.ubTableIdx, gHBParamMgr.ubPageType, gHBParamMgr.ubSLCMode);
}

U16 HBRetryPostconditionCheckFeatureFPU(void)
{
	HB_LRU_TABLE_STRUCT_PTR pLRUTable = M_RETRY_GET_LRU_TABLE(gHBParamMgr.ubSLCMode, gHBParamMgr.ubGlobalDie, gHBParamMgr.ubPageType);
	return HynixV6HBRetryCheckFeatureFPU(pLRUTable[0].param.ubTableIdx, gHBParamMgr.ubPageType, gHBParamMgr.ubSLCMode);
}

#endif /* ((PS5013_EN) && (FLASH_HYNIXV6_TLC == FW_CATEGORY_FLASH) && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER)) */
