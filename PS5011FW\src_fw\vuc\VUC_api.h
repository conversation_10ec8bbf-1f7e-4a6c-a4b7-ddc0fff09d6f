#ifndef _VUC_API_H_
#define _VUC_API_H_

/*
 * ----------------------------------------------------------------------------------------------------------
 * header files
 * ----------------------------------------------------------------------------------------------------------
 */
#include "vuc/VUC_ApKey.h"
#include "vuc/VUC_CacheRead.h"
#include "vuc/VUC_CacheWrite.h"
#include "vuc/VUC_command.h"
#include "vuc/VUC_CutEFuse.h"
#include "vuc/VUC_DetectPeripheral.h"
#include "vuc/VUC_DirectReadInfo.h"
#include "vuc/VUC_DumpTable.h"
#include "vuc/VUC_DisableFrozenLock.h"
#include "vuc/VUC_EraseAll.h"
#include "vuc/VUC_EraseFlash.h"
#include "vuc/VUC_GetEraseCount.h"
#include "vuc/VUC_GetFlashInfo.h"
#include "vuc/VUC_GetRDTLog.h"
#include "vuc/VUC_GetStatus.h"
#include "vuc/VUC_GetVucDataSize.h"
#include "vuc/VUC_ISPFlash.h"
#include "vuc/VUC_ISPJump.h"
#include "vuc/VUC_ISPReadFlash.h"
#include "vuc/VUC_ISPReadPRAM.h"
#include "vuc/VUC_ISPReadROM.h"
#include "vuc/VUC_ISPRom.h"
#include "vuc/VUC_KingstonAntiFakeData.h"
#include "vuc/VUC_KingstonVerifyAntiFake.h"
#include "vuc/VUC_ListBadBlock.h"
#include "vuc/VUC_NandVerifyRead.h"
#include "vuc/VUC_NandVerifyTrigger.h"
#include "vuc/VUC_NandVerifyWrite.h"
#include "vuc/VUC_Write_VRLC.h"
#include "vuc/VUC_ProgramFlash.h"
#include "vuc/VUC_ReadFlash.h"
#include "vuc/VUC_ReadPH.h"
#include "vuc/VUC_ReadReg.h"
#include "vuc/VUC_ReadScanFlashWindow.h"
#include "vuc/VUC_ReadSram.h"
#include "vuc/VUC_ReadSysInfo.h"
#include "vuc/VUC_ScanFlashWindowParameter.h"
#include "vuc/VUC_ScanFlashWindowSetting.h"
#include "vuc/VUC_ScanRDTLog.h"
#include "vuc/VUC_ScanPH.h"
#include "vuc/VUC_SearchSysBlock.h"
#include "vuc/VUC_SecurityPassThrough.h"
#include "vuc/VUC_SetFlashFeature.h"
#include "vuc/VUC_SmartRescue.h"
#include "vuc/VUC_SMART.h"
#include "vuc/VUC_WriteInfo.h"
#include "vuc/VUC_WritePH.h"
#include "vuc/VUC_WriteReg.h"
#include "vuc/VUC_WriteSram.h"
#include "vuc/VUC_Protect.h"
#include "vuc/VUC_SetPSID.h"
#include "vuc/VUC_GetRMAInfo.h"
#include "vuc/VUC_PCATranslate.h"
#include "vuc/VUC_PCIEEyeInfo.h"
#include "vuc/VUC_SetSpecificGlobalValue.h"
#include "vuc/VUC_DetectTxRx.h"
#include "vuc/VUC_UnlockJTAG.h"
#include "vuc/VUC_FwFeatureControl.h"
#include "vuc/VUC_MicronGetUnifiedEventLog.h"
#if (VUC_MICRON_NICKS_EN)
#include "vuc/VUC_MicronGetRawBitErrCnt.h"
#include "vuc/VUC_MicronGetNandErrorRecoveryStatistics.h"
#include "vuc/VUC_MicronNandPass.h"
#include "vuc/VUC_MicronResponse.h"
#include "vuc/VUC_MicronGetDriveConfigData.h"
#include "VUC_MicronGetBEC.h"
#include "vuc/VUC_MicronGetTemperature.h"
#include "vuc/VUC_MicronGetDefectList.h"
#include "vuc/VUC_MicronClearEventLog.h"
#include "vuc/VUC_MicronGetEraseCount.h"
#include "vuc/VUC_MicronGetVTSweep.h"
#include "vuc/VUC_MicronGetBlkNandMode.h"
#include "vuc/VUC_MicronGetMLBi.h"
#include "vuc/VUC_MicronSetMLBi.h"
#include "vuc/VUC_MicronLogicaltoPhysical.h"
#include "vuc/VUC_MicronPhysicaltoLogicalAddress.h"
#endif /*(VUC_MICRON_NICKS_EN)*/

#if RDT_MODE_EN
#include "vuc/VUC_WriteReadAPDBT.h"
#endif

/*
 * ---------------------------------------------------------------------------------------------------
 *   definitions
 * ---------------------------------------------------------------------------------------------------
 */
#define VUC_WRITE_PROTECT_MICRON_NAND_MODE_BIT	(BIT6) //gpVTDBUF->WriteProtect.btMicronNandMode

/*
 * ----------------------------------------------------------------------------------------------------------
 * enum
 * ----------------------------------------------------------------------------------------------------------
 */
typedef enum VUC_MP_FLASH_CLOCK {
	VUC_MP_FLASH_CLOCK_10M		= 0,
	VUC_MP_FLASH_CLOCK_33M		= 1,
	VUC_MP_FLASH_CLOCK_41P7M	= 2,
	VUC_MP_FLASH_CLOCK_100M		= 3,
	VUC_MP_FLASH_CLOCK_200M		= 4,
	VUC_MP_FLASH_CLOCK_333M		= 5,
	VUC_MP_FLASH_CLOCK_400M		= 6,
	VUC_MP_FLASH_CLOCK_533M		= 7,
	VUC_MP_FLASH_CLOCK_667M		= 8,
	VUC_MP_FLASH_CLOCK_800M		= 9,
	VUC_MP_FLASH_CLOCK_1200M 	= 10,
	VUC_MP_FLASH_CLOCK_1066M 	= 11,
	VUC_MP_FLASH_CLOCK_1400M 	= 13,
	VUC_MP_FLASH_CLOCK_1600M 	= 14,
	VUC_MP_FLASH_CLOCK_450M		= 15,
	VUC_MP_FLASH_CLOCK_DEFAULT	= 0xFF
} VUC_MP_FLASH_CLOCK_t;

/*
 * ----------------------------------------------------------------------------------------------------------
 * public prototypes
 * ----------------------------------------------------------------------------------------------------------
 */
#if (HOST_MODE == NVME)
AOM_VUC void VUC_MakeError(VUC_OPT_HCMD_PTR_t pCmd);
#endif

#endif /* _VUC_API_H_ */

