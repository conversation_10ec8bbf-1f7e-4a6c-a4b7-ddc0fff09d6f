/*
 * rdt_flash_test.c
 *
 *  Created on: 2020?~2る14ら
 *      Author: user
 */
#include "rdt/rdt_api.h"
#include "cpu/cpu_api.h"

#if RDT_MODE_EN
#if (RDT_RUN_ONLINE)
#include "hal/pic/uart/shr_hal_pic_uartxfer.h"
extern UART_STATUS *gpLastStatus;
#endif

U8 gubSkipTestPlaneMap[64] = {0};// max plane num = 64, die_per_ce * total_bank * channel_per_bank * plane_per_die
extern U8 ERL_Bypass_Record_HB_RETRY_INFO[16][16][4];
extern U8 global_RDT_no_seed;
extern U32 gFlashTestStartTime;

#if ENABLE_RDT_DELEGATE_CQ_IC_PATTERN
BUSY_TASK gbz_task;
#endif
U8 rdt_get_rwbuf_id(FPL_GEOMETRY_STRUCT_PTR geometry, U8 lmu, U8 plane, U8 ch, U8 bank)
{
	U8 buf_id = 0;
	buf_id = ch * geometry->total_bank + bank;
	return buf_id;
}

void  rdt_dbt_check_total_bad_count(RDT_API_STRUCT_PTR rdt, U8 die)
{
	DBT_LOG_STRUCT dbt;
	U32 bm_count_offset;//, bm_count_offset_idx;
	U8 ubCh = 0;
	U8 ubBank = 0;
	U8 ubPhyPlaneIndex = 0;
	U8 ubdie;
	//PCA_RULE_STRUCT *p_pca_rule = (rdt->fpl->pca_rule + 1);
	//U8 slc_mode = LOG_BLOCK_SLC_MODE;
	//U8 pca_rule = 0;
	FPL_GEOMETRY_STRUCT_PTR geometry;

	geometry = &rdt->fpl->geometry;
	U8 Total_Bad_Count_Max_per_plane = 25;
	ubdie = die;
	/* Choose PCA rule */
	//pca_rule = PCA_RULE(slc_mode);
	dbt.bm_count = (RDT_BBM_BM_COUNT_PTR) (rdt->log[RDT_LOG_DBT].buf_base + SHARED_DBUF_BBM_BMCNT_EARLY_OFFSET);

#if DIEIL_EN
	for (ubdie = 0; ubdie < geometry->die_per_ce; ubdie++) {
#endif
		for (ubBank = 0; ubBank < geometry->total_bank; ubBank++) {

			for (ubCh = 0; ubCh < geometry->channel_per_bank; ubCh++) {

				for (ubPhyPlaneIndex = 0; ubPhyPlaneIndex < geometry->plane_per_die; ubPhyPlaneIndex++) {

					bm_count_offset = (ubCh * geometry->die_per_ce * geometry->total_bank * geometry->plane_per_die) +
						(((ubBank * geometry->die_per_ce) + die) * geometry->plane_per_die) +
						ubPhyPlaneIndex;

					if (dbt.bm_count[bm_count_offset].bad_cnt > Total_Bad_Count_Max_per_plane) {
						rdt->rdt_err = ERR_BadBlockCount_PerPlane_ToMuch;
						gubLEDState = RDT_TEST_ERROR;
						M_UART(RDT_TEST_, "\n\r Bad block TOO MUCH");
						ubdie = geometry->die_per_ce + 1;
						ubBank = geometry->total_bank + 1;
						ubCh = geometry->channel_per_bank + 1;
						ubPhyPlaneIndex = geometry->plane_per_die + 1;
						break;
					}

				}

			}
		}
#if DIEIL_EN
	}	//for Die loop when DIEIL_EN = 1
#endif


}

U32 rdt_api_get_data_buf(RDT_API_STRUCT_PTR rdt, U8 rw_buf_id, U8 lc, U8 buf_type)
{
	U32 buf = 0;
	if (buf_type == BUF_TYPE_READ) {
		buf = rdt->read.data_buf_base + (PAGE_BYTE_SIZE * rw_buf_id) + (LC_BYTE_SIZE * lc); //lc is frame
		//M_FW_ASSERT((buf + LC_BYTE_SIZE) <= (rdt->read.data_buf_base + rdt->read.data_buf_size));
	}
	else { //BUF_TYPE_WRITE
		buf = rdt->write.data_buf_base + (PAGE_BYTE_SIZE * rw_buf_id);
		//M_FW_ASSERT((buf + PAGE_BYTE_SIZE) <= (rdt->write.data_buf_base + rdt->write.data_buf_size));
	}

	return buf;
}

void rdt_flash_test_erase(RDT_API_STRUCT_PTR rdt, U8 ubDieNumber, U16 uwUnit, U8 slc_mode)
{
	//U8 ubCEIndex = 0;
	U8 ubCh = 0;
	U8 ubBank = 0;
	U8 ubPhyPlaneIndex = 0;
	U8 skip_plane[4] = {0}; // if 4 plane, needs to define 4 element array
	U8 pca_rule = 0;
	//PCA_t ulPCA;
	U32 ulLocalPCA;
	//U16 uwTagId;
	U32 ulFindPCA = 0;
	U32 d1_find_pca = 0; //for log block
	U8 last_nonskip_plane;
	U8 cop0_opt = 0;
	U8 ubdie;
	//U8 Total_Bad_Count_Max_per_plane = 30;
	//U16 Temp_Bad_count = 0;
	FPL_GEOMETRY_STRUCT_PTR geometry = &rdt->fpl->geometry;
	/* Choose PCA rule */
	pca_rule = PCA_RULE(slc_mode);

	ubdie = ubDieNumber;
#if (FW_CATEGORY_FLASH == FLASH_INTEL_N38A_QLC)
	U32 ulSectionIdx;
	U32 ulEraseSectionCnt = 3;
	U32 ulSectionOffset = 0;

	if (slc_mode) {
		ulSectionOffset = 768;
	}
	else {
		ulSectionOffset = 3072;
	}
#endif

#if DIEIL_EN
	for (ubdie = 0; ubdie < geometry->die_per_ce; ubdie++) {
#endif
		for (ubBank = 0; ubBank < geometry->total_bank; ubBank++) {

			for (ubCh = 0; ubCh < geometry->channel_per_bank; ubCh++) {

				//Init skip_plane
				for (ubPhyPlaneIndex = 0; ubPhyPlaneIndex < geometry->plane_per_die; ubPhyPlaneIndex++) {

					ulFindPCA = PCA_VALUE(pca_rule, ubdie, uwUnit, ubBank, ubCh, ubPhyPlaneIndex, 0, 0, 0);
					d1_find_pca = PCA_VALUE((PCA_RULE(LOG_BLOCK_SLC_MODE)), ubdie, uwUnit, ubBank, ubCh, ubPhyPlaneIndex, 0, 0, 0);

					if ( (rdt_api_dbt_check_bad(rdt, (ulFindPCA), slc_mode)) || (rdt_api_check_sys_block(rdt, d1_find_pca, 0)) ) {
#if RDT_CONTINUE_TEST_PROGRAM_FAIL
						if (rdt_api_dbt_check_bad(rdt, (ulFindPCA), slc_mode) == DBT_FW_PROGRAM_FAIL) {
							// continue test program fail
						}
						else {
							skip_plane[ubPhyPlaneIndex] = TRUE;
						}
#else
						skip_plane[ubPhyPlaneIndex] = TRUE;
#endif
					}
					else {
						skip_plane[ubPhyPlaneIndex] = FALSE;
					}
				}

				//check last non-skip plane
				for (ubPhyPlaneIndex = geometry->plane_per_die; ubPhyPlaneIndex > 0 ; ubPhyPlaneIndex--) {
					if (skip_plane[ubPhyPlaneIndex - 1] == FALSE) {
						last_nonskip_plane = ubPhyPlaneIndex - 1;
						break;
					}
				}

#if (FW_CATEGORY_FLASH == FLASH_INTEL_N38A_QLC)
				for (ulSectionIdx = 0; ulSectionIdx < ulEraseSectionCnt; ulSectionIdx++)
#endif
				{
					for (ubPhyPlaneIndex = 0; ubPhyPlaneIndex < geometry->plane_per_die; ubPhyPlaneIndex++) {

						/* Check bbm */
						if (skip_plane[ubPhyPlaneIndex]) {
							continue;
						}

						/* PCA */
#if (FW_CATEGORY_FLASH == FLASH_INTEL_N38A_QLC)
						ulLocalPCA = PCA_VALUE(pca_rule, ubdie, uwUnit, ubBank, ubCh, ubPhyPlaneIndex, (ulSectionIdx * ulSectionOffset), 0, 0);
#else
						ulLocalPCA = PCA_VALUE(pca_rule, ubdie, uwUnit, ubBank, ubCh, ubPhyPlaneIndex, 0, 0, 0);
#endif

						if ( ubPhyPlaneIndex == last_nonskip_plane) {
							cop0_opt = MULTIPLANE_END;
						}
						else {
							cop0_opt = MULTIPLANE_ING;
						}

						rdt_api_cop0_erase((void *)NULL, (U32)ulLocalPCA, 0, cop0_opt, slc_mode);

					}
				}
			}
		}
#if DIEIL_EN
	}	//for Die loop when DIEIL_EN = 1
#endif
	/*------------ End of erase -------------*/

	/* Recieve all erase CQ */
	rdt_api_delegate_CQ_with_IC_pattern(BUF_TYPE_ERASE);
	/* Ping-pong ERL (to do) */
	rdt_api_program_erl_log(rdt, FALSE);
}

BOOL rdt_flash_test_read(RDT_API_STRUCT_PTR rdt, U8 ubDieNumber, U16 uwUnit, U16 wl_end, U8 lmu, BOOL slc_mode, BOOL check_erasepage)
{
	U8 ubCh = 0;
	U8 ubBank = 0;
	U8 ubPhyPlaneIndex;
	U32 ulLocalPCA;
	U8 rw_buf_id;
	U8 pca_rule;
	U8 process_page;
	U8 page_end;
	U8 ubFrameIndex;
	U8 pca_lmu;
	U32 data_buf;
	U16 test_wl, test_wl_end;
	//U16 test_wl_end;
	//S16 test_wl;
	U32 ulFindPCA;
	U32 d1_find_pca; //for log block
	U16 pca_wl = 0;
	global_RDT_no_seed = 0; //disable RDT  W/R test seed
	U8 ubdie = ubDieNumber;
	FPL_GEOMETRY_STRUCT_PTR geometry = &rdt->fpl->geometry;
	//gpComm2Andes_Info->ubRead_TimeOut_Cnt = 0;

	/* Choose PCA rule and lmu_end */
	pca_rule = PCA_RULE(slc_mode);
	//UartPrintf("gPCARule_Page.ulMask = %d",gPCARule_Page.ulMask);

	if (slc_mode) {
#if MULTIPAGE_OPT_FOR_CACHE_READ
		page_end = CACHE_READ_PAGE_SCALE;
		test_wl_end = wl_end / CACHE_READ_PAGE_SCALE;
#else
		page_end = 1;
		test_wl_end = wl_end;
#endif
	}
	else { //tlc
		test_wl_end = wl_end;
		page_end = gubLMUNumber;
	}

	//reset skip test plane bitmap
	U8 skip_test_plane_offset = 0;
	memset(gubSkipTestPlaneMap, 0, sizeof(gubSkipTestPlaneMap));
	//ERL_Record_HB_RETRY_INFO SET 0
	memset((void *)(&ERL_Bypass_Record_HB_RETRY_INFO), 0x00, sizeof(ERL_Bypass_Record_HB_RETRY_INFO));
#if DIEIL_EN
	for (ubdie = 0; ubdie < geometry->die_per_ce; ubdie++) {
#endif

		for (ubBank = 0; ubBank < geometry->total_bank; ubBank++) {
			for (ubCh = 0; ubCh < geometry->channel_per_bank; ubCh++) {
				for (ubPhyPlaneIndex = 0; ubPhyPlaneIndex < geometry->plane_per_die; ubPhyPlaneIndex++) {
					skip_test_plane_offset++;
					//Check bbm
					ulFindPCA = PCA_VALUE(pca_rule, ubdie, uwUnit, ubBank, ubCh, ubPhyPlaneIndex, 0, 0, 0);
					d1_find_pca = PCA_VALUE((PCA_RULE(LOG_BLOCK_SLC_MODE)), ubdie, uwUnit, ubBank, ubCh, ubPhyPlaneIndex, 0, 0, 0);

					if ( (rdt_api_dbt_check_bad(rdt, (ulFindPCA), slc_mode)) || (rdt_api_check_sys_block(rdt, d1_find_pca, 0)) ) {
#if RDT_CONTINUE_TEST_PROGRAM_FAIL
						if (rdt_api_dbt_check_bad(rdt, (ulFindPCA), slc_mode) == DBT_FW_PROGRAM_FAIL) {
							// continue test program fail
						}
						else {
							gubSkipTestPlaneMap[skip_test_plane_offset - 1] = 1;
						}
#elif RDT_RECORD_ALL_BLOCK_EDL	//Reip
						U8 ubBadType;
						U32 ulEDLPCA;

						if (rdt_api_check_sys_block(rdt, d1_find_pca, 0)) {
							ubBadType = 0xF;
							ulEDLPCA  = d1_find_pca;
						}
						else {
							ubBadType = rdt_api_dbt_check_bad(rdt, (ulFindPCA), slc_mode);
							ulEDLPCA  = ulFindPCA;
						}
						rdt_api_add_edl_block(rdt, ulEDLPCA, slc_mode, ubBadType);
						gubSkipTestPlaneMap[skip_test_plane_offset - 1] = 1;

#else
						gubSkipTestPlaneMap[skip_test_plane_offset - 1] = 1;
#endif
						continue;
					}
				}
			}
		}

#if DIEIL_EN
	}
#endif


	for (test_wl = 0; test_wl < test_wl_end ; test_wl++) {
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_QLC)//ems mst add--karl
		page_end = slc_mode ? page_end : FTLGetCoord(GetPageFromCellNumInRDT_EMS((U16)test_wl, 0), IM_GETCOORD_WIN_SIZE);
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
		page_end = slc_mode ? page_end : FTLGetCoord(GetPageFromCellNumInRDT((U16)test_wl, 0), IM_GETCOORD_WIN_SIZE);
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC)
		page_end = slc_mode ? page_end : FTLGetCoord(GetPageFromCellNumInRDT((U16)test_wl, 0), IM_GETCOORD_WIN_SIZE);
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)//zerio n48r add
		page_end = slc_mode ? page_end : FTLGetCoord(GetPageFromCellNumInRDT((U16)test_wl, 0), IM_GETCOORD_WIN_SIZE);
#endif
		for (process_page = 0; process_page < page_end; process_page++) {

			if (slc_mode) {
#if MULTIPAGE_OPT_FOR_CACHE_READ
				pca_wl = test_wl * CACHE_READ_PAGE_SCALE + process_page;
				pca_lmu = 0;
#else
				pca_wl = test_wl;
				pca_lmu = 0;
#endif
			}
			else { //tlc
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_TLC)
				pca_wl = test_wl * page_end + process_page;
				pca_lmu = 0;
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_QLC)//ems mst add--karl
				pca_wl = GetPageFromCellNumInRDT_EMS((U16)test_wl, process_page);
				pca_lmu = 0;
#elif (IM_B47R || IM_B37R)
				pca_wl = GetPageFromCellNumInRDT((U16)test_wl, process_page);
				pca_lmu = 0;
#elif (IM_N48R)
				pca_wl = GetPageFromCellNumInRDT((U16)test_wl, process_page);
				pca_lmu = 0;
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
				pca_wl = GetPageFromCellNumInRDT((U16)test_wl, process_page);
				pca_lmu = 0;
#elif (FW_CATEGORY_FLASH == FLASH_INTEL_N38A_QLC)
				pca_wl = test_wl * page_end + process_page;
				pca_lmu = 0;
#elif (!((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)))
				pca_wl = test_wl;
				pca_lmu = process_page;
#else
				pca_wl = test_wl * page_end + process_page;
				pca_lmu = 0;
#endif
			}

			memset((void *)rdt->read.data_buf_base, 0, rdt->read.data_buf_size);
			skip_test_plane_offset = 0;
#if DIEIL_EN
			for (ubdie = 0; ubdie < geometry->die_per_ce; ubdie++) {
#endif
				for (ubBank = 0; ubBank < geometry->total_bank; ubBank++) {
					//UartPrintf("\tce = %d",ubBank);
					for (ubCh = 0; ubCh < geometry->channel_per_bank; ubCh++) {
						//UartPrintf("\tubCh = %d",ubCh);
						for (ubPhyPlaneIndex = 0; ubPhyPlaneIndex < geometry->plane_per_die; ubPhyPlaneIndex++) {
							skip_test_plane_offset++;
							//UartPrintf("\tubPhyPlaneIndex = %d",ubPhyPlaneIndex);
							if (gubSkipTestPlaneMap[skip_test_plane_offset - 1] == 1) {
								continue;
							}



							///2. READ 16K page	(read combine)
							for (ubFrameIndex = 0; ubFrameIndex < geometry->node_per_page; ++ubFrameIndex) {

								/* PCA */
								ulLocalPCA = PCA_VALUE(pca_rule, ubdie, uwUnit, ubBank, ubCh, ubPhyPlaneIndex, pca_wl, pca_lmu, ubFrameIndex);

								rw_buf_id = rdt_get_rwbuf_id(geometry, pca_lmu, ubPhyPlaneIndex, ubCh, ubBank);
								data_buf = rdt_api_get_data_buf(rdt, rw_buf_id, ubFrameIndex, BUF_TYPE_READ);
#if	RDT_RECORD_ECC_DISTRIBUTION
								rdt_api_cop0_express_read(rdt_flash_test_read_callback, (U32)ulLocalPCA, rdt, slc_mode, (U32)data_buf, 0);
#else
								rdt_api_cop0_express_read((void *)NULL, (U32)ulLocalPCA, NULL, slc_mode, (U32)data_buf, 0);
#endif


								//rdt_api_cop0_read((void *)NULL, (U32)ulLocalPCA, rdt, slc_mode, (U32)data_buf, 0);
							} //End of ubFrameIndex
#if RDT_RECORD_ECC_DISTRIBUTION
							if (rdt->param.rdt_record_ecc_distribution.record_edl_enable) {
								if (rdt->param.rdt_record_ecc_distribution.record_edl_by_1P_read) {
									rdt_api_delegate_CQ(BUF_TYPE_READ);
								}
							}
#endif
						} //End of ubPhyPlaneIndex
#if !RDT_RECORD_ECC_DISTRIBUTION
						while (FTLGetTagPoolNum(COP0_TAG_POOL_ID) <= 32) {
							rdt_api_delegate_partial_CQ_with_IC_pattern(BUF_TYPE_READ, 1); //Only process CQ in queue but not wait all CQ
						}
#endif
					} //End of ubCh

#if RDT_RECORD_ECC_DISTRIBUTION
					if (rdt->param.rdt_record_ecc_distribution.record_edl_enable) {
						if (!rdt->param.rdt_record_ecc_distribution.record_edl_by_1P_read) {
							rdt_api_delegate_CQ(BUF_TYPE_READ);
						}
					}
#endif
				} //End of ubBank (ubCEIndex)
#if DIEIL_EN
			}	//for Die
#endif

		}//End of process_page

#if (!MULTIPAGE_OPT_FOR_CACHE_READ)
		if (!rdt->param.rdt_record_ecc_distribution.record_edl_enable) {
			rdt_api_delegate_CQ(BUF_TYPE_READ);
		}
#endif
	} //End of wl
	global_RDT_no_seed = 0; //enable W/R seed
	/* Ping-pong ERL (to do) */
	rdt_api_program_erl_log(rdt, FALSE);
#if RDT_RECORD_ECC_DISTRIBUTION
	rdt_api_program_edl_log(rdt, FALSE);
#endif
	return TRUE;
}

void rdt_flash_test_program(RDT_API_STRUCT_PTR rdt, U8 ubDieNumber, U16 uwUnit, ProgramOrder *pProgramOrder)
{
	if (!pProgramOrder || !pProgramOrder->ubIsValidPO) {
		M_UART(RDT_TEST_, "Invalid program order\n");
		return;
	}
	//U8 ubCEIndex = 0;
	U8 ubCh = 0;
	U8 ubBank = 0;
	U8 ubPhyPlaneIndex = 0;
	BOOL skip_plane[4] = {0}; // if 4 plane, needs to define 4 element array
	U8 pca_rule = 0;
	//PCA_t ulPCA;
	U32 ulLocalPCA;
	//U16 uwTagId;
	U8 lmu_end, lmu;
	//U8 ubi;
	//U16 rw_buf_id;
	U32 data_buf;
	//U32 ulBufAddrMatrix[4], ulLCA[4];
	U32 ulFindPCA = 0;
	U8 cop0_opt = 0;
	U32 d1_find_pca = 0; //for log block
	U8 last_nonskip_plane, first_nonskip_plane;
	//U32 ulData = 0;
	U8 PlaneLoop;
	U8 PlaneEndLoop;
	U8 PlaneStartIndex;
	U8 PlaneEndIndex;
	U8 last_nonskip_plane_second, first_nonskip_plane_second;
	U8 ubdie;
	U8 process_wl = 0;
	U8 ubProgramMode;
	U8 process_wl_count = CACHE_PROG_WL_SCALE;

	FPL_GEOMETRY_STRUCT_PTR geometry = &rdt->fpl->geometry;

	/* Choose PCA rule and lmu_end */
	pca_rule = PCA_RULE(pProgramOrder->ubIsSLC);
	global_RDT_no_seed = 0; //disable RDT  W/R test seed
	ubdie = ubDieNumber;

	if (pProgramOrder->ubIsSLC) {
		lmu_end = 1;
	}
	else {
		lmu_end = gubLMUNumber;
	}

	ProgramOrder poOriginalProgramOrder = *pProgramOrder;

#if DIEIL_EN
	for (ubdie = 0; ubdie < geometry->die_per_ce; ubdie++) {
#endif
		for (ubBank = 0; ubBank < geometry->total_bank; ubBank++) {

			for (ubCh = 0; ubCh < geometry->channel_per_bank; ubCh++) {

				*pProgramOrder = poOriginalProgramOrder; // Modified by other CH / Bank

				for (process_wl = 0; process_wl < process_wl_count; process_wl++) {

					if (pProgramOrder->ubIsValidPO) {
						last_nonskip_plane = 0xff;
						last_nonskip_plane_second = 0xff;

						//Init skip_plane
						for (ubPhyPlaneIndex = 0; ubPhyPlaneIndex < geometry->plane_per_die; ubPhyPlaneIndex++) {
							ulFindPCA = PCA_VALUE(pca_rule, ubdie, uwUnit, ubBank, ubCh, ubPhyPlaneIndex, 0, 0, 0);
							d1_find_pca = PCA_VALUE((PCA_RULE(LOG_BLOCK_SLC_MODE)), ubdie, uwUnit, ubBank, ubCh, ubPhyPlaneIndex, 0, 0, 0);

							if ( (rdt_api_dbt_check_bad(rdt, (ulFindPCA), pProgramOrder->ubIsSLC)) || (rdt_api_check_sys_block(rdt, d1_find_pca, 0)) ) {
#if RDT_CONTINUE_TEST_PROGRAM_FAIL
								if (rdt_api_dbt_check_bad(rdt, (ulFindPCA), pProgramOrder->ubIsSLC) == DBT_FW_PROGRAM_FAIL) {
									// continue test program fail
								}
								else {
									skip_plane[ubPhyPlaneIndex] = TRUE;
								}
#else
								skip_plane[ubPhyPlaneIndex] = TRUE;
#endif
							}
							else {
								skip_plane[ubPhyPlaneIndex] = FALSE;
							}
						}

						PlaneEndLoop = 1;//original = 1 for 4plane program ,2 is for two plane program
						if (SKHYNIX == gFlhEnv.ulFlashDefaultType.BitMap.VendorType
							|| TOSHIBASANDISK == gFlhEnv.ulFlashDefaultType.BitMap.VendorType
							|| YMTC == gFlhEnv.ulFlashDefaultType.BitMap.VendorType
							|| INTELMICRON == gFlhEnv.ulFlashDefaultType.BitMap.VendorType
							|| SAMSUNG == gFlhEnv.ulFlashDefaultType.BitMap.VendorType) {
							//check last non-skip plane
							first_nonskip_plane = 0xff;
							first_nonskip_plane_second = 0xff;
							for (ubPhyPlaneIndex = 0; ubPhyPlaneIndex < geometry->plane_per_die ; ubPhyPlaneIndex++) {

								//M_UART(RDT_DBG_, "\n skip plane[%b]=%b", (ubPhyPlaneIndex), skip_plane[ubPhyPlaneIndex]);
								if (skip_plane[ubPhyPlaneIndex] ) {
									if (PlaneEndLoop == 1) {
										PlaneEndLoop = 2;
										//M_UART(RDT_DBG_, "\n PlaneEndLoop=%b", PlaneEndLoop);
									}
								}

								else {
									if ( (ubPhyPlaneIndex) < 2 ) {
										last_nonskip_plane = ubPhyPlaneIndex;
										if (0xff == first_nonskip_plane) {
											first_nonskip_plane = ubPhyPlaneIndex;
										}
									}
									else {
										last_nonskip_plane_second = ubPhyPlaneIndex;
										if (0xff == first_nonskip_plane_second) {
											first_nonskip_plane_second = ubPhyPlaneIndex;
										}
									}
								}
							}

							//	M_UART(RDT_DBG_, "\n last_nonskip_plane=%b,   last_nonskip_plane_second=%b", last_nonskip_plane, last_nonskip_plane_second);
						}
						else {
							//check last non-skip plane
							first_nonskip_plane = 0xff;
							for (ubPhyPlaneIndex = 0; ubPhyPlaneIndex < geometry->plane_per_die; ++ubPhyPlaneIndex) {
								if (skip_plane[ubPhyPlaneIndex] == FALSE) {
									last_nonskip_plane = ubPhyPlaneIndex;
									if (0xff == first_nonskip_plane) {
										first_nonskip_plane = ubPhyPlaneIndex;
									}
								}
							}
						}
						for ( PlaneLoop = 0; PlaneLoop < PlaneEndLoop; PlaneLoop++ ) {
#if (IM_N48R || IM_B47R || IM_B37R)
							lmu_end = pProgramOrder->ubIsSLC ? 1 : FTLGetCoord(GetPageFromCellNumInRDT((U16)pProgramOrder->swWL, 0), IM_GETCOORD_WIN_SIZE); //LP, UP, XP, TP
#elif ((FW_CATEGORY_FLASH == FLASH_YMTC_EMS_QLC))//ems mst add--karl
							lmu_end = pProgramOrder->ubIsSLC ? 1 : FTLGetCoord(GetPageFromCellNumInRDT_EMS((U16)pProgramOrder->swWL, 0), IM_GETCOORD_WIN_SIZE); //LP, UP, XP, TP
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
							lmu_end = pProgramOrder->ubIsSLC ? 1 : FTLGetCoord(GetPageFromCellNumInRDT((U16)pProgramOrder->swWL, 0), IM_GETCOORD_WIN_SIZE);
#endif
							for (lmu = 0; lmu < lmu_end; lmu++) {

								if ( PlaneEndLoop == 1 ) {
									PlaneStartIndex = 0;
									PlaneEndIndex = geometry->plane_per_die;
								}
								else {
									PlaneStartIndex = (PlaneLoop * (geometry->plane_per_die / 2));
									PlaneEndIndex = PlaneStartIndex + (geometry->plane_per_die / 2);
								}
								for (ubPhyPlaneIndex = PlaneStartIndex; ubPhyPlaneIndex < PlaneEndIndex; ubPhyPlaneIndex++) {

									/* Check bbm */
									if (skip_plane[ubPhyPlaneIndex]) {
										continue;
									}

									/* PCA */
#if (IM_N48R || IM_B47R || IM_B37R)
									ulLocalPCA = PCA_VALUE(pca_rule, ubdie, uwUnit, ubBank, ubCh, ubPhyPlaneIndex, pProgramOrder->ubIsSLC ? pProgramOrder->swWL : GetPageFromCellNumInRDT((U16)pProgramOrder->swWL, lmu), 0, 0);
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_QLC)//ems mst add--karl
									ulLocalPCA = PCA_VALUE(pca_rule, ubdie, uwUnit, ubBank, ubCh, ubPhyPlaneIndex, pProgramOrder->ubIsSLC ? pProgramOrder->swWL : GetPageFromCellNumInRDT_EMS((U16)pProgramOrder->swWL, lmu), 0, 0);
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_TLC)
									ulLocalPCA = PCA_VALUE(pca_rule, ubdie, uwUnit, ubBank, ubCh, ubPhyPlaneIndex, pProgramOrder->swWL * lmu_end + lmu, 0, 0);
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
									ulLocalPCA = PCA_VALUE(pca_rule, ubdie, uwUnit, ubBank, ubCh, ubPhyPlaneIndex, pProgramOrder->ubIsSLC ? pProgramOrder->swWL : GetPageFromCellNumInRDT((U16)pProgramOrder->swWL, lmu), 0, 0);
#elif (FW_CATEGORY_FLASH == FLASH_INTEL_N38A_QLC)
									ulLocalPCA = PCA_VALUE(pca_rule, ubdie, uwUnit, ubBank, ubCh, ubPhyPlaneIndex, pProgramOrder->swWL * lmu_end + lmu, 0, 0);
#else /*(IM_N48R)*/
									ulLocalPCA = PCA_VALUE(pca_rule, ubdie, uwUnit, ubBank, ubCh, ubPhyPlaneIndex, pProgramOrder->swWL, lmu, 0);
#endif /*(IM_N48R)*/

									/* Assign write buf */
									U32 rw_buf_id = rdt_get_rwbuf_id(geometry, lmu, ubPhyPlaneIndex, ubCh, ubBank);
									data_buf = rdt_api_get_data_buf(rdt, rw_buf_id, 0, BUF_TYPE_WRITE);

									U32 prog_data = PCA_VALUE(pca_rule, ubdie, uwUnit, ubBank, ubCh, 0, 0, 0, 0);//
									//U32 prog_data = PCA_VALUE(pca_rule, ubdie, uwUnit, ubBank, ubCh, ubPhyPlaneIndex, pProgramOrder->swWL, lmu, 0);//
									rdt_api_dmac_setvalue((prog_data), data_buf, (U32)PAGE_BYTE_SIZE);
									((U16 *)(data_buf))[0] = 0xFFFF;	//Reip

									/* Send program tie-in */
									if (lmu == (lmu_end - 1)) {
										if (SKHYNIX == gFlhEnv.ulFlashDefaultType.BitMap.VendorType
											|| TOSHIBASANDISK == gFlhEnv.ulFlashDefaultType.BitMap.VendorType
											|| YMTC == gFlhEnv.ulFlashDefaultType.BitMap.VendorType
											|| INTELMICRON == gFlhEnv.ulFlashDefaultType.BitMap.VendorType
											|| SAMSUNG == gFlhEnv.ulFlashDefaultType.BitMap.VendorType) {
											if ( (( ubPhyPlaneIndex == last_nonskip_plane) && (PlaneEndLoop == 2)) || (( ubPhyPlaneIndex == last_nonskip_plane_second) && (PlaneEndLoop == 2)) || (ubPhyPlaneIndex == (geometry->plane_per_die - 1) && (PlaneEndLoop == 1)) ) {
												cop0_opt = MULTIPLANE_END;

												//M_UART(RDT_DBG_, "\n end prog");
											}
											else {
												cop0_opt = MULTIPLANE_ING;
											}
										}
										else {
											if ( ubPhyPlaneIndex == last_nonskip_plane) {
												cop0_opt = MULTIPLANE_END;
											}
											else {
												cop0_opt = MULTIPLANE_ING;
											}
										}
									}
									else {
										cop0_opt = MULTIPLANE_ING;
									}

#if ENABLE_RDT_SRAM_RS_ENC_TEST
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC)	//Reip Porting 3D-V7 QLC Add
									ubProgramMode = RDT_COP0_W_RAIDECC_ENC_PROGRAM;
									if (!pProgramOrder->ubIsSLC) {
										if ((1 == pProgramOrder->ubPass) && ((ubPhyPlaneIndex == first_nonskip_plane) || (ubPhyPlaneIndex == first_nonskip_plane_second))) {//&& (QLC_WL_PAGE_NUM == lmu_end)
											ubProgramMode = RDT_COP0_W_RAIDECC_ENC_PROGRAM_2PASS;
										}
									}
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_QLC)//zerio bics6 qlc add
									ubProgramMode = RDT_COP0_W_RAIDECC_ENC_PROGRAM;
									if (!pProgramOrder->ubIsSLC) {
										if ((1 == pProgramOrder->ubPass) && ((ubPhyPlaneIndex == first_nonskip_plane) || (ubPhyPlaneIndex == first_nonskip_plane_second))) {//&& (QLC_WL_PAGE_NUM == lmu_end)
											ubProgramMode = RDT_COP0_W_RAIDECC_ENC_PROGRAM_2PASS;
										}
									}
#elif((FW_CATEGORY_FLASH == FLASH_YMTC_EMS_QLC))//ems mst add--karl
									ubProgramMode = RDT_COP0_W_RAIDECC_ENC_PROGRAM;
									if (!pProgramOrder->ubIsSLC) {
										if ((1 == pProgramOrder->ubPass) && (QLC_WL_PAGE_NUM == FTLGetCoord(GetPageFromCellNumInRDT_EMS((U16)pProgramOrder->swWL, lmu), IM_GETCOORD_WIN_SIZE)) && ((ubPhyPlaneIndex == first_nonskip_plane) || (ubPhyPlaneIndex == first_nonskip_plane_second))) {
											ubProgramMode = RDT_COP0_W_RAIDECC_ENC_PROGRAM_2PASS;
										}
									}
#elif(IM_N48R)//Dylan for V6 RDT porting,Reference V6 based RDT code,V6 don't do this case
									ubProgramMode = RDT_COP0_W_RAIDECC_ENC_PROGRAM;
									if (!pProgramOrder->ubIsSLC) {
										if ((1 == pProgramOrder->ubPass) && (QLC_WL_PAGE_NUM == FTLGetCoord(GetPageFromCellNumInRDT((U16)pProgramOrder->swWL, lmu), IM_GETCOORD_WIN_SIZE)) && ((ubPhyPlaneIndex == first_nonskip_plane) || (ubPhyPlaneIndex == first_nonskip_plane_second))) {//
											ubProgramMode = RDT_COP0_W_RAIDECC_ENC_PROGRAM_2PASS; //QLC page first pass use 0Dh
										}
									}
#else //FLASH_TYPE_HYNIX_3D_QLC
									ubProgramMode = RDT_COP0_W_RAIDECC_ENC_PROGRAM;
#endif //FLASH_TYPE_HYNIX_3D_QLC

									rdt_api_cop0_prog((void *)NULL, (U32)ulLocalPCA, rdt, ubProgramMode, cop0_opt, pProgramOrder->ubIsSLC, (U32)data_buf, (U32)ulLocalPCA, 0);
#endif
								}  //End of ubPhyPlaneIndex
							}//End of lmu
						}
					}	//pProgramOrder->ubIsValidPO
					else {
						M_UART(RDT_TEST_, "\nInvalid program order");
					}
					*pProgramOrder = GetNextProgramOrder(*pProgramOrder);
				} //End of process_wl
			} //End of ubCh

			if (FTLGetTagPoolNum(COP0_TAG_POOL_ID) <= (MAX_PLANE_BANK_NUM * lmu_end * process_wl_count * MAX_CHANNEL)) {
				while (FTLGetTagPoolNum(COP0_TAG_POOL_ID) != TAG_NUM) {
					//Use while to prevent tag pool num would be not enough for next run
					/* Recieve all program CQ */
					rdt_api_delegate_partial_CQ_with_IC_pattern(BUF_TYPE_WRITE, 0);
				}
			}
		}  //End of ubBank (ubCEIndex)
#if DIEIL_EN
	}	//for Die loop when DIEIL_EN = 1
#endif

	while (FTLGetTagPoolNum(COP0_TAG_POOL_ID) != TAG_NUM) {
		//Use while to prevent tag pool num would be not enough for next run
		/* Recieve all program CQ */
		rdt_api_delegate_partial_CQ_with_IC_pattern(BUF_TYPE_WRITE, 0);
	}

	global_RDT_no_seed = 0; //enable W/R seed
	/* Ping-pong ERL (to do) */
	rdt_api_program_erl_log(rdt, FALSE);
}

void rdt_flash_test_process(RDT_API_STRUCT_PTR rdt, U8 slc_mode, U8 loop, U8 test_cycle_end, FLASH_TEST_OPTION flashTestOption)
{
	U8 test_cycle;
	U8 modcnt = 50;
	U8 total_test_loop;
	U8 ubSkipLastErase = 0;
	U16 end_page;
	U16 wl_end;//wl
	U16 uwUnit, start_unit, uwTestUnit;
	U16 end_unit;
	U16 ubDie = 0;

	FML_RECORD_BAD_CNT fml_bad_cnt = {0};
	FML_RECORD_RETRY_PASS_CNT fml_retry_pass_cnt = {0};
	FPL_GEOMETRY_STRUCT_PTR geometry = &(rdt->fpl->geometry);

	FLASH_TEST_OPTION flash_Test_Option = flashTestOption;

#if RDT_FLASH_TEST_SEQUENCE_FIX
	if (rdt->rdt_err) {
		return;
	}
#endif

#if RDT_RECORD_TEMPERATURE
	TDL_LOG_STRUCT tdl_log;
	RDT_TEMPERATURE_STRUCT_PTR temperature = (RDT_TEMPERATURE_STRUCT_PTR)rdt->base.temperature_base;
	TEMPERATURE_CELSIUS_SUM_t total_temp[MAX_TEMP_RECORD] = {{0}};
	U8 ubi;
	U8 ubCh, ubBank, ubPhy_CE;
	U16 uwTestPercent = 0;
	TTTemperatureCelsius_t ubTemperature;
	U8 ubTempIndex;

	U32 total_vb;
	// Initial Temperature Struct
	/*      Max_Temp    Avg_Temp    Min_Temp
		INT     0           0         0xFF
		EXT     0           0         0xFF
		CE0     0           0         0xFF
		CE1     0           0         0xFF
		 .      .           .          .
		 .      .           .          .
		CE32    0           0         0xFF
	*/
	for ( ubi = 0; ubi < MAX_TEMP_RECORD; ubi++) {
		temperature[ubi].max_temp.ubAll = 0xFF; //Negative min value
		temperature[ubi].avg_temp.ubAll = 0;
		temperature[ubi].min_temp.ubAll = 0x7F; //Positive max value
	}
#endif

#if RDT_TT_LOG
	memset(rdt->tt_log, 0, RDT_TT_LOG_LENGTH * 4 );
	rdt->tt_log[0] = RDT_TT_LOG_LENGTH - 1;
#endif

	if (slc_mode) {
		end_page = geometry->d1_page_per_block;  //(gFlhEnv.uwPagePerBlock / 3)
		wl_end   = geometry->d1_page_per_block;  //(gFlhEnv.uwPagePerBlock / 3)
		total_test_loop = rdt->param.flash_slc_test_loop;
	}
	else {
		end_page = geometry->d2_page_per_block;
#if(IM_N48R)
		wl_end   = IM_N48_CELLNUM;  //geometry->d2_page_per_block / 3
#elif(IM_B47R)
		wl_end	 = IM_B47R_CELLNUM;
#elif(IM_B37R)
		wl_end	 = IM_B37R_CELLNUM;
#elif ((FW_CATEGORY_FLASH == FLASH_YMTC_EMS_QLC))//ems mst add--karl
		wl_end   = IM_EMS_CELLNUM;
#elif (SAMSUNG_FSP_EN)
		wl_end   = IM_SAMSUNG_CELLNUM;
#else
		wl_end   = geometry->d1_page_per_block; //Dylan for V6 RDT porting, Reference V6-based RDT code
#endif
		total_test_loop = rdt->param.flash_tlc_test_loop;
	}
	if (( test_cycle_end > 2) || (test_cycle_end == 0)) {
		test_cycle_end = 2;
	}

	if (!rdt->param.rdt_fast_mode.fast_flash_test) {
		start_unit = 0;
		end_unit = geometry->block_per_plane; //gFlhEnv.ulBlockPerPlaneBank
		modcnt = 50;
	}
	else {
		start_unit = PARTIAL_START_UNIT;
		end_unit = PARTIAL_END_UNIT;
		modcnt = (((end_unit - start_unit)  / 10) > 1) ? ((end_unit - start_unit)  / 10) : 1;
	}

	M_UART(INIT_, "\nend_page=%d", end_page);
	M_UART(INIT_, "\ngeometry->die_per_ce=%b", geometry->die_per_ce);
	M_UART(INIT_, "\nend_unit=%d", end_unit);

	rdt->saving_info.flash_mode = slc_mode;
	rdt->saving_info.test_loop = loop;

	test_cycle = 0;
#if RDT_POWER_RESUME
	if (!(flashTestOption & FLASH_READ_ONLY)) {
		if (slc_mode) {
			//test_cycle = rdt->power_resume_slc_loop % rdt->param.flash_slc_test_cycle;
			rdt->power_resume_slc_loop = 0;
		}
		else {
			//test_cycle = rdt->power_resume_tlc_loop % rdt->param.flash_tlc_test_cycle;
			rdt->power_resume_tlc_loop = 0;
		}
	}
#endif

	rdt->saving_info.ulTotalTestUnits = (end_unit - start_unit) * ( slc_mode ? rdt->param.flash_slc_test_loop : rdt->param.flash_tlc_test_loop) * test_cycle_end * geometry->die_per_ce;
	rdt->saving_info.ulCountTestUnits = (end_unit - start_unit) * loop * test_cycle_end * geometry->die_per_ce;
	if (0 == loop) {
		rdt->saving_info.uwPercentMask = 0;
	}

	if ((loop == (total_test_loop - 1)) && (rdt->param.rdt_op_option.skip_last_erase)) {
		if ((slc_mode) && (rdt->param.flash_tlc_test_loop != 0)) {
			ubSkipLastErase = 0;
		}
		else {
			ubSkipLastErase = 1;
		}
	}

	for (; test_cycle < test_cycle_end; test_cycle++) {
		rdt->saving_info.test_cycle = test_cycle;
		U32 start_time = rdt_api_rtt_get_timer_count();
		M_UART(RDT_TEST_, "\n Test mode=%b, loop=%b, test_cycle=%b", slc_mode, loop, test_cycle);

#if RDT_FLASH_TEST_SEQUENCE_FIX
		if (loop % 2) { //loop1 cycle0 反向擦写读，cycle1 SLC正向读，XLC反向读
			if (test_cycle % 2) {
				flash_Test_Option = (FLASH_READ_ONLY \
						| ((ubSkipLastErase) ? 0 : FINAL_ERASE_UNIT) \
						| (slc_mode ? 0 : TEST_UNIT_REVERSE));
			}
			else {
				flash_Test_Option = NORMAL_FLASH_TEST | TEST_UNIT_REVERSE;
			}
		}
		else {//loop0 cycle0 正向向擦写读，cycle1 SLC反向读，XLC正向读
			if (test_cycle % 2) {
				flash_Test_Option = (FLASH_READ_ONLY \
						| ((ubSkipLastErase) ? 0 : FINAL_ERASE_UNIT) \
						| (slc_mode ? TEST_UNIT_REVERSE : 0));
			}
			else {
				flash_Test_Option = NORMAL_FLASH_TEST;
			}
		}
#endif

		if (rdt->read_retry_en) {
			//Clear HBRetryPassCount, Record HBRetryPassCount to FML by Cycle
			memset(gpVTDBUF->Retry.aulHBRetryPassPerCECnt, 0x00, sizeof(gpVTDBUF->Retry.aulHBRetryPassPerCECnt));
		}

#if !DIEIL_EN
		for (ubDie = 0; ubDie < geometry->die_per_ce; ubDie++) {
			M_UART(RDT_TEST_, "\nubdie = %b", ubDie);
#endif


			for (uwUnit = start_unit; uwUnit < end_unit; uwUnit++) {
				//			for (uwUnit = start_unit; uwUnit < 300; uwUnit++) {//Dylan test
				//U32 start_time;
				/** //Dylan debug Check RDT continued fail
				if (!RDT_Only_read) {

					flashTestOption = NORMAL_FLASH_TEST;
				}
				flashTestOption = NORMAL_FLASH_TEST;

				if (RDT_Only_read) {
					uwTestUnit = Test_UNIT;
					flashTestOption = FLASH_READ_ONLY;
				}
				*/

				if (uwUnit % modcnt == 0) {
					{
						M_UART(RDT_TEST_, "\n[Flash] ");
					}
					M_UART(RDT_TEST_, "total vb = %d", end_unit);
					M_UART(RDT_TEST_, ", current vb = %d", uwUnit);
				}
				//U8 Test_percentage =  (uwUnit * 100 ) / end_unit;
				//UartPrintf("\n\r[vb = %d], [slcmode = %d ]  %d|100%c  ", uwUnit, slc_mode, Test_percentage, 37);

				//Record Test process percent
				rdt->saving_info.ulCountTestUnits++;
				uwTestPercent = ((double)rdt->saving_info.ulCountTestUnits * 1.0 / rdt->saving_info.ulTotalTestUnits * 10);
				if ( (!(rdt->saving_info.uwPercentMask & (BIT0 << uwTestPercent))) && (0 <= uwTestPercent) && (uwTestPercent < 10) ) {
					rdt_api_rml_log_add(rdt, slc_mode ? (RML_IMARK_SLC_TEST_XX_PERCENT | (uwTestPercent << 4)) : (RML_IMARK_TLC_TEST_XX_PERCENT | (uwTestPercent << 4)));
					rdt_api_write_log_to_flash(rdt, RDT_LOG_RDT_TESTMARK);
					rdt->saving_info.uwPercentMask |= (BIT0 << uwTestPercent);
#if (RDT_RUN_ONLINE)
					gpLastStatus->B.ubPRLProgress = uwTestPercent * 10;
#endif
				}

#if RDT_FLASH_TEST_SEQUENCE_FIX
				if (!(flash_Test_Option & TEST_UNIT_REVERSE)) {
					uwTestUnit = uwUnit;
				}
				else {
					uwTestUnit = end_unit - uwUnit - 1 + start_unit;
				}
#else
				uwTestUnit = uwUnit; //sequential block idx test
#endif

				//				//// PIO DBG  ////
				//				if (uwTestUnit == 10) {
				//					REG32 *pFlaReg0 = R32_FCTL_CH[0];
				//					FlaCEControl(0, 0, ENABLE);
				//					pFlaReg0[R32_FCTL_PIO_CMD] = 0xCD;
				//					FlaCEControl(0, 0, DISABLE);
				//				}
				//				//// PIO DBG  ////
				//xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
				//                      Erase
				//xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

				if (!(flash_Test_Option & FLASH_READ_ONLY)) {
					rdt_flash_test_erase(rdt, ubDie, uwTestUnit, slc_mode);
					rdt_api_delegate_CQ(BUF_TYPE_ERASE);
				}

#if RDT_RECORD_TEMPERATURE
				for (ubCh = 0; ubCh < geometry->channel_per_bank; ubCh++) {

					for (ubBank = 0; ubBank < geometry->total_bank; ubBank++) {

						ubPhy_CE = rdt_api_show_phy_ce(rdt, ubBank, ubCh);

						//Get FLASH Temperature
						ubTemperature.ubAll = rdt_api_get_flash_temperature(ubCh, ubBank, ubDie, 0);

						rdt_temperature_record_update(&ubTemperature, &temperature[(ubPhy_CE + RDT_CE_START_INDEX)], &total_temp[(ubPhy_CE + RDT_CE_START_INDEX)]);
					}
				}
#endif

				//				//// PIO DBG  ////
				//
				//				if (uwTestUnit == 10) {
				//					REG32 *pFlaReg1 = R32_FCTL_CH[0];
				//					FlaCEControl(0, 0, ENABLE);
				//					pFlaReg1[R32_FCTL_PIO_CMD] = 0xCE;
				//					FlaCEControl(0, 0, DISABLE);
				//				}
				//				//// PIO DBG  ////
				//xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
				//                      Program
				//xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

				if (!(flash_Test_Option & FLASH_READ_ONLY)) {
#if RDT_FLASH_TEST_SEQUENCE_FIX
					if (loop % 2) {
						rdt->inversion_flag = 1;
						rdt_api_setup_inv(ENABLE);
					}
					else {
						rdt->inversion_flag = 0;
						rdt_api_setup_inv(DISABLE);
					}
#else
					if (loop != 0) {
						//need to inverse the state of read
						if (rdt->inversion_flag == 1) {
							rdt_api_setup_inv(DISABLE);
							M_UART(RDT_DBG_, "\n $$$$$ con dis $$$$$ ");
						}
						else {
							rdt_api_setup_inv(ENABLE);
							M_UART(RDT_DBG_, "\n $$$$$ con en $$$$$ ");
						}
#if DIEIL_EN
						if ( (uwTestUnit + 1) == end_unit) { //change inversion at last unit
#else
						if ( ((uwTestUnit + 1) == end_unit) && (ubDie == (geometry->die_per_ce - 1))) { //change inversion at last unit
#endif
							rdt->inversion_flag ^= 0x1;
						}
					}

					else if (test_cycle == 1 && uwTestUnit == start_unit) { //loop 0 && cycle 1, change inversion at first unit

						rdt->inversion_flag = 1;
						rdt_api_setup_inv(ENABLE);
					}
#endif

					ProgramOrder poProgramOrder = GetInitialPorgramOrder(slc_mode, 0, wl_end);

					while (poProgramOrder.ubIsValidPO) {
						rdt_flash_test_program(rdt, ubDie, uwTestUnit, &poProgramOrder);
					}

					/* Recieve all program CQ */
					rdt_api_delegate_CQ_with_IC_pattern(BUF_TYPE_WRITE);

					/* Ping-pong ERL (to do) */
					rdt_api_program_erl_log(rdt, FALSE);

				}
				//// PIO DBG  ////
				//				if (uwTestUnit == 10) {
				//					REG32 *pFlaReg2 = R32_FCTL_CH[0];
				//					FlaCEControl(0, 0, ENABLE);
				//					pFlaReg2[R32_FCTL_PIO_CMD] = 0xCF;
				//					FlaCEControl(0, 0, DISABLE);
				//				}
				//// PIO DBG  ////
				//xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
				//				   Read
				//xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

				if (!(flash_Test_Option & SKIP_READ_VERIFY)) { // normal cycle flow
					//M_UART(RDT_DBG_, " IR: ");
					if (!rdt_flash_test_read(rdt, ubDie, uwTestUnit, wl_end, 0, slc_mode, 0)) {
						rdt_api_delegate_CQ(BUF_TYPE_READ);
						return; //read data compare fail
					}
					/* Recieve all read CQ */
					rdt_api_delegate_CQ_with_IC_pattern(BUF_TYPE_READ);

					/* Ping-pong ERL (to do) */
					rdt_api_program_erl_log(rdt, FALSE);
				} //End of loop

				//xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
				//                      Erase
				//xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

#if RDT_FLASH_TEST_SEQUENCE_FIX
				if (flash_Test_Option & FINAL_ERASE_UNIT) {
					rdt_flash_test_erase(rdt, ubDie, uwTestUnit, slc_mode);
					rdt_api_delegate_CQ(BUF_TYPE_ERASE);
				}
#else
				if (!(flash_Test_Option & FLASH_READ_ONLY)) {
					rdt_flash_test_erase(rdt, ubDie, uwTestUnit, slc_mode);
					rdt_api_delegate_CQ(BUF_TYPE_ERASE);
				}
#endif


#if RDT_RECORD_TEMPERATURE
				//to save the Temperature Log when each flash test cycle finish

				tdl_log.loop_num = loop;
				tdl_log.cycle_number = test_cycle;
				tdl_log.step = (slc_mode) ? (0x2) : (0x1);

				//tdl_log.temperature = rdt_api_read_temperature(rdt); //TODO
				//Get CH0 CE0 Flash Temperature
				tdl_log.temperature = rdt_api_get_flash_temperature(0, 0, 0, 0);

				tdl_log.time_stamp = rdt_api_rtt_get_timer_count(); // ms

				for (ubTempIndex = RDT_INT_INDEX; ubTempIndex <= RDT_EXT_INDEX; ubTempIndex++) {
					if (ubTempIndex == RDT_INT_INDEX) {
						// Internal(Controller) Temperature
						ubTemperature.ubAll = rdt_read_CTRL_temperature();
					}
					else {
						//External Temperature
						ubTemperature.ubAll = rdt_api_read_temperature(rdt);
					}

					rdt_temperature_record_update(&ubTemperature, &temperature[ubTempIndex], &total_temp[ubTempIndex]);
				}

				ubTemperature.ubAll = rdt_read_CTRL_temperature();

				if (uwUnit == ( (end_unit - start_unit) / 8 ) + start_unit ) {
					tdl_log.log_count = 0;
					rdt_api_tdl_log_add(rdt, &tdl_log);
				}
				else if ( uwUnit == ( (end_unit - start_unit) / 8 ) * 2 + start_unit) {
					tdl_log.log_count = 1;

					// 2 to 7 dont need time so record internal thermal
					tdl_log.time_stamp = (U32)ubTemperature.ubAll;
					rdt_api_tdl_log_add(rdt, &tdl_log);
				}
				else if ( uwUnit == ( (end_unit - start_unit) / 8 ) * 3 + start_unit) {
					tdl_log.log_count = 2;

					tdl_log.time_stamp = (U32)ubTemperature.ubAll;
					rdt_api_tdl_log_add(rdt, &tdl_log);
				}
				else if ( uwUnit == ( (end_unit - start_unit) / 8 ) * 4 + start_unit) {
					tdl_log.log_count = 3;

					tdl_log.time_stamp = (U32)ubTemperature.ubAll;
					rdt_api_tdl_log_add(rdt, &tdl_log);
				}
				else if ( uwUnit == ( (end_unit - start_unit) / 8 ) * 5 + start_unit) {
					tdl_log.log_count = 4;

					tdl_log.time_stamp = (U32)ubTemperature.ubAll;
					rdt_api_tdl_log_add(rdt, &tdl_log);
				}
				else if ( uwUnit == ( (end_unit - start_unit) / 8 ) * 6 + start_unit) {
					tdl_log.log_count = 5;

					tdl_log.time_stamp = (U32)ubTemperature.ubAll;
					rdt_api_tdl_log_add(rdt, &tdl_log);
				}
				else if ( uwUnit == ( (end_unit - start_unit) / 8 ) * 7 + start_unit) {
					tdl_log.log_count = 6;

					tdl_log.time_stamp = (U32)ubTemperature.ubAll;
					rdt_api_tdl_log_add(rdt, &tdl_log);
				}
				else if ( uwUnit == ( end_unit - 1 ) ) {
					tdl_log.log_count = 0x0F;
					rdt_api_tdl_log_add(rdt, &tdl_log);
				}
#endif

				U32 now = rdt_api_rtt_get_timer_count();
				if (rdt->param.flash_test_timeout_sec > 0 && (now - gFlashTestStartTime) / 1000 > rdt->param.flash_test_timeout_sec) {
					M_UART(RDT_TEST_, "\nflash_test timeout, threshold is %ds\n", rdt->param.flash_test_timeout_sec);
					rdt->rdt_err = ERR_UNKNOWN_TIMEOUT;
					rdt_api_rml_log_add(rdt, slc_mode ? (RML_IMARK_SLC_TEST_XX_PERCENT | (uwTestPercent << 4)) : (RML_IMARK_TLC_TEST_XX_PERCENT | (uwTestPercent << 4)));
					rdt_api_write_log_to_flash(rdt, RDT_LOG_RDT_TESTMARK);
					break;
				}
			} //End of uwUnit
			rdt_api_program_erl_log(rdt, TRUE);
#if RDT_RECORD_ECC_DISTRIBUTION
			rdt_api_program_edl_log(rdt, TRUE);
#endif
			if (rdt->rdt_err) {
				break;
			}
#if !DIEIL_EN
		} //End of ubdie
#endif

		if ( test_cycle != ( test_cycle_end - 1 ) ) {
			rdt_api_write_log_to_flash(rdt, RDT_LOG_TEMPERATURE_DIVERSITY);
		}

		// to save Flash Mark Log when each flash test cycle finish
		fml_bad_cnt.flash_test_cycle = test_cycle;
		fml_bad_cnt.early_bad_cnt    = rdt->saving_info.flash_early_bad_cnt;
		fml_bad_cnt.later_bad_cnt    = rdt->saving_info.flash_later_bad_cnt;
		fml_bad_cnt.total_bad_cnt    = fml_bad_cnt.early_bad_cnt + fml_bad_cnt.later_bad_cnt;
		fml_bad_cnt.curr_time_ms     = rdt_api_rtt_get_timer_count();

		if (test_cycle == test_cycle_end - 1) {
			fml_bad_cnt.identify_mark = 0xcccccccc;
		}
		else {
			fml_bad_cnt.identify_mark = 0xaaaaaaaa;
		}
		rdt_api_fml_log_add(rdt, (FML_LOG_STRUCT_PTR)&fml_bad_cnt);

		if (rdt->read_retry_en) {
			for (ubCh = 0; ubCh < geometry->channel_per_bank; ubCh++) {
				for (ubBank = 0; ubBank < geometry->total_bank; ubBank++) {
					fml_retry_pass_cnt.ubLoop = loop;
					fml_retry_pass_cnt.ubCycle = test_cycle;
					fml_retry_pass_cnt.ubCH = ubCh;
					fml_retry_pass_cnt.ubBank = ubBank;
					fml_retry_pass_cnt.ubSLCMode = slc_mode;
					fml_retry_pass_cnt.ulRetryPassCount = gpVTDBUF->Retry.aulHBRetryPassPerCECnt[M_GLOBAL_Q_IDX(ubCh, ubBank, BIT(gPCARule_Channel.ubBit_No))];
					fml_retry_pass_cnt.identify_mark = 0x99999999;
					rdt_api_fml_log_add(rdt, (FML_LOG_STRUCT_PTR)&fml_retry_pass_cnt);
				}
			}
		}

		//dummy loop do not save prl
		if (!(flashTestOption & DUMMY_FLASH_TEST)) {
			rdt_api_prl_program(rdt, rdt->rdt_test_flow_index);//store flow index for powercycle
		}

		M_UART(RDT_TEST_, "\n-- test time %d s \n", (rdt_api_rtt_get_timer_count() - start_time) / 1000);

		if (rdt->rdt_err) {
			break;
		}
	} //End of test_cycle

#if RDT_RECORD_TEMPERATURE
#if !DIEIL_EN
	total_vb = geometry->die_per_ce * (end_unit - start_unit) * test_cycle_end;
#else
	total_vb = (end_unit - start_unit) * test_cycle_end;
#endif
	M_UART(RDT_DBG_, "\n total_vb: %d, die = %d", total_vb, geometry->die_per_ce);

	temperature[RDT_INT_INDEX].avg_temp.B.Degree = (U8)(total_temp[RDT_INT_INDEX].L.Degree / total_vb);
	temperature[RDT_INT_INDEX].avg_temp.B.btSign = total_temp[RDT_INT_INDEX].L.btSign;
	temperature[RDT_EXT_INDEX].avg_temp.B.Degree = (U8)(total_temp[RDT_EXT_INDEX].L.Degree / total_vb);
	temperature[RDT_EXT_INDEX].avg_temp.B.btSign = total_temp[RDT_EXT_INDEX].L.btSign;
	for (ubCh = 0; ubCh < geometry->channel_per_bank; ubCh++) {
		for (ubBank = 0; ubBank < geometry->total_bank; ubBank++) {

			ubPhy_CE = rdt_api_show_phy_ce(rdt, ubBank, ubCh);
			temperature[(ubPhy_CE + RDT_CE_START_INDEX)].avg_temp.B.Degree = (U8)(total_temp[(ubPhy_CE + RDT_CE_START_INDEX)].L.Degree / total_vb);
			temperature[(ubPhy_CE + RDT_CE_START_INDEX)].avg_temp.B.btSign = total_temp[(ubPhy_CE + RDT_CE_START_INDEX)].L.btSign;
			M_UART(RDT_DBG_, "\n min_temp: %d", temperature[ubPhy_CE + RDT_CE_START_INDEX].min_temp);
		}
	}

	rdt_add_temperature_info_to_tdl(rdt, FALSE);

#if RDT_TT_LOG
	rdt_add_tt_log_to_tdl(rdt);
#endif

	rdt_api_write_log_to_flash(rdt, RDT_LOG_FLASH_TESTMARK);
	rdt_api_write_log_to_flash(rdt, RDT_LOG_TEMPERATURE_DIVERSITY);
#endif
}

BOOL rdt_api_general_flash_test(RDT_API_STRUCT_PTR rdt, BOOL slc_mode)
{
	U8 loop, total_test_loop, test_cycle_end;
	U8 total_dummy_loop = 0;
	U8 current_state;
	U8 startloop = 0;
	U32 flash_test_start_time, flash_test_end_time;
	U32 rml_start_mark, rml_end_mark;
	FLASH_TEST_OPTION flashTestOption = NORMAL_FLASH_TEST;

	if (slc_mode) {
#if RDT_POWER_RESUME
		if ( rdt->param.flash_slc_test_loop != 0 ) {
			//rdt->param.flash_slc_test_loop -= (rdt->power_resume_slc_loop / rdt->param.flash_slc_test_cycle);	// 2 cycle in one loop
			startloop = (rdt->power_resume_slc_loop / rdt->param.flash_slc_test_cycle);
		}
#endif
		if (rdt->param.flash_slc_test_loop == 0) {
			return TRUE;
		}

		M_UART(RDT_TEST_, "\n[Flash] SLC Flash test\n");
		total_test_loop = rdt->param.flash_slc_test_loop;
		test_cycle_end = rdt->param.flash_slc_test_cycle;
		rml_start_mark = RML_IMARK_SLC_TEST_START;
		rml_end_mark = RML_IMARK_SLC_TEST_END;
		current_state = RDT_STATE_FLH_TEST_SLC;
		//Setting RDT RR
		rdt->read_retry_en = rdt->param.slc_read_error_option.read_retry_enable;
	}
	else { //TLC mode
#if RDT_POWER_RESUME
		if ( rdt->param.flash_tlc_test_loop != 0 ) {
			//rdt->param.flash_tlc_test_loop -= (rdt->power_resume_tlc_loop / rdt->param.flash_tlc_test_cycle);	// 2 cycle in one loop
			startloop = (rdt->power_resume_tlc_loop / rdt->param.flash_tlc_test_cycle);	// 2 cycle in one loop
		}
#endif
		if (rdt->param.flash_tlc_test_loop == 0) {
			return TRUE;
		}

		M_UART(RDT_TEST_, "\n[Flash] TLC Flash test\n");
		total_test_loop = rdt->param.flash_tlc_test_loop;
		test_cycle_end = rdt->param.flash_tlc_test_cycle;
		rml_start_mark = RML_IMARK_TLC_TEST_START;
		rml_end_mark = RML_IMARK_TLC_TEST_END;
		current_state = RDT_STATE_FLH_TEST_TLC;
		//Setting RDT RR
		rdt->read_retry_en = rdt->param.tlc_read_error_option.read_retry_enable;
	}

	//Record RDT ERL
	rdt->record_rdt_erl = 1;

#if ENABLE_RDT_DELEGATE_CQ_IC_PATTERN
	BZ_TASK_INIT(1); 				//enable busy sram test
#if ENABLE_RDT_BMU_TEST_PATTERN
	rdt_api_bmu_test_init();
#endif	//ENABLE_RDT_BMU_TEST_PATTERN
#if ENABLE_RDT_COP1_TEST
	rdt_api_cop1_search_ST1_test_init();
#endif
#endif	//ENABLE_RDT_IC_PATTERN

	//TLC mode test
	//M_UART(RDT_DBG_, "SLC First: %x\n", ((U32 *)DBUF_RETRY_TABLE)[0]);
	//M_UART(RDT_TEST_, "TLC First: %x\n", ((U32 *)DBUF_RETRY_TABLE + DEF_KB(2))[0]);
	//M_UART(RDT_TEST_, "TLC First: %x\n", *(U32 *)(DBUF_RETRY_TABLE + DEF_KB(2) + 8));
	//M_UART(RDT_TEST_, "TLC First: %x\n", *(U32 *)(DBUF_RETRY_TABLE + DEF_KB(2) + 12));

	/*** SLC/TLC test start mark, add to RML ***/
	rdt_api_rml_log_add(rdt, rml_start_mark);
	rdt_api_write_log_to_flash(rdt, RDT_LOG_RDT_TESTMARK);

	rdt->current_state = current_state;
	flash_test_start_time = rdt_api_rtt_get_timer_count();


	M_UART(RDT_TEST_, "\n\r\nrdt->read_retry=%b", rdt->read_retry_en);
	/*** disable inversion ***/
	rdt_api_setup_inv(DISABLE);
	rdt->inversion_flag = 0;

	//dummy loop
	for (loop = startloop; loop < total_dummy_loop; loop++) {
		M_UART(RDT_TEST_, "\ndummy loop: %d\n", loop);
		flashTestOption = DUMMY_FLASH_TEST | SKIP_READ_VERIFY;
		rdt_flash_test_process(rdt, slc_mode, loop, test_cycle_end, flashTestOption);

#if !ENABLE_RDT_NO_LOG
		if (rdt->rdt_err) {
			rdt->read_retry_en = 0;
			rdt->record_rdt_erl = 0;
			rdt->inversion_flag = 0;
			rdt_api_setup_inv(DISABLE);
			return FALSE;
		}
#endif
	}

	/*** disable inversion ***/
	rdt_api_setup_inv(DISABLE);
	rdt->inversion_flag = 0;

	//normal loop
	for (loop = startloop; loop < total_test_loop; loop++) {

		if (rdt->param.over_rdy_detect_enable) {	//Reip
			if (rdt->saving_info.ubRDYAbnormalFlag == TRUE) {
				M_UART(RDT_TEST_, "\n RDY Abnormal, Skip loop: %d", loop);
				break;	//Skip test
			}
		}

		flashTestOption = NORMAL_FLASH_TEST;
#if 0//RDT_THERMAL_DETECT
		rdt_thermal_detect(rdt, 0, loop); //No save cycle
#endif
		if (!rdt->rdt_err) {
			rdt_flash_test_process(rdt, slc_mode, loop, test_cycle_end, flashTestOption);
		}

#if !ENABLE_RDT_NO_LOG
		if (rdt->rdt_err) {
			rdt->read_retry_en = 0;
			rdt->record_rdt_erl = 0;
			rdt->inversion_flag = 0;
			rdt_api_setup_inv(DISABLE);
			return FALSE;
		}
#endif

		if (loop == 0) {	//Reip
#if (RDT_RDY_TIME_TEST)	//For TPROG, Reip
			if (slc_mode == 1) {
				if (rdt->param.flash_test_enable.flash_slc_rdy_test_enable) {
					M_UART(RDT_TEST_, "\n RDT Check time: flow [SLC_RDY_TIME_TEST] Start ");
					gubLEDState = RDT_NAND_TEST;
					rdt_api_rml_log_add(rdt, RML_IMARK_RDY_TEST_SLC_START);
					rdt_api_write_log_to_flash(rdt, RDT_LOG_RDT_TESTMARK);
					rdt_flash_rdy_time_test_process(rdt, TRUE);
					rdt_api_rml_log_add(rdt, RML_IMARK_RDY_TEST_SLC_END);
					rdt_api_write_log_to_flash(rdt, RDT_LOG_RDT_TESTMARK);
					M_UART(RDT_TEST_, "\n RDT Check time: flow [SLC_RDY_TIME_TEST] Finish ");
				}
			}
			else {
				if (rdt->param.flash_test_enable.flash_tlc_rdy_test_enable) {
					M_UART(RDT_TEST_, "\n RDT Check time: flow [TLC_RDY_TIME_TEST] Start ");
					gubLEDState = RDT_NAND_TEST;
					rdt_api_rml_log_add(rdt, RML_IMARK_RDY_TEST_TLC_START);
					rdt_api_write_log_to_flash(rdt, RDT_LOG_RDT_TESTMARK);
					rdt_flash_rdy_time_test_process(rdt, FALSE);
					rdt_api_rml_log_add(rdt, RML_IMARK_RDY_TEST_TLC_END);
					rdt_api_write_log_to_flash(rdt, RDT_LOG_RDT_TESTMARK);
					M_UART(RDT_TEST_, "\n RDT Check time: flow [TLC_RDY_TIME_TEST] Finish ");
				}
			}
#endif
		}

	}

	flash_test_end_time = rdt_api_rtt_get_timer_count();
	if (slc_mode) {
		rdt->saving_info.slc_test_time = (U16)((flash_test_end_time - flash_test_start_time) / (60 * 1000)); //minutes
		M_UART(RDT_TEST_, "\nflash slc test total time: %d min\n", rdt->saving_info.slc_test_time);
	}
	else {
		rdt->saving_info.tlc_test_time = (U16)((flash_test_end_time - flash_test_start_time) / (60 * 1000)); //minutes
		M_UART(RDT_TEST_, "\nflash tlc test total time: %d min\n", rdt->saving_info.tlc_test_time);
	}

	rdt_api_setup_inv(DISABLE);
	rdt->inversion_flag = 0;

	//Do not record RDT ERL
	rdt->record_rdt_erl = 0;

	//Disable RDT RR
	rdt->read_retry_en = 0;

	//TLC test end mark, add to RML
	//rdt->current_state = RDT_STATE_POST_TLC_TEST_WRITE_RML_LOG;
	rdt_api_rml_log_add(rdt, rml_end_mark);
	rdt_api_write_log_to_flash(rdt, RDT_LOG_RDT_TESTMARK);


	return TRUE;
}

#endif
