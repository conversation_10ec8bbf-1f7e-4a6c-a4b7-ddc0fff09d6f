#ifndef _XZIP_API_H_
#define _XZIP_API_H_
#include "setup.h"
#include "typedef.h"
#include "xzip_cmd_type.h"
#include "xzip_reg.h"
#include "aom/aom_api.h"
#include "common/fw_vardef.h"

#define XZIP_CMD_OPCODE_CLEAR_REGISTER	(0x1)
#define XZIP_CMD_OPCODE_ADJUST_LENGTH	(0x2)
#define XZIP_CMD_OPCODE_LOCK			(0x3)
#define XZIP_CMD_OPCODE_UNLOCK			(0x4)
#define XZIP_CMD_OPCODE_SEARCH_XZIP		(0x5)
#define XZIP_CMD_OPCODE_SEARCH_PCA		(0x6)
#define XZIP_CMD_OPCODE_INSERT_PCA		(0x7)
#define XZIP_CMD_OPCODE_ENTRY_PCA_DIFF	(0x8)

#define XZIP_REGS_NEED_SET				(1)
#define XZIP_REGS_NOT_SET				(0)

#define XZIP_PUSH_SQ_SUCCESS			(1)
#define XZIP_PUSH_SQ_FAIL				(0)

#define XZIP_MAX_ENTRY_NUM				(256)
#define XZIP_ADJUST_LENGTH				(32)

#define XZIP_EN_INFO_BLOCK_BIT		(BIT2)
typedef union {
	XZIPCQResultType_t			XZIPCQResultType;
	XZIPClearRegResult_t		ClearRegResult;
	XZIPAdjustLengthResult_t	AdjustLengthResult;
	XZIPLockResult_t			XZIPLockResult;
	XZIPUnLockResult_t			XZIPUnLockResult;
	XZIPSearchXZIPResult_t		SearchXZIPResult;
	XZIPSearchPCAResult_t		SearchPCAResult;
	XZIPInsertPCAResult_t		InsertPCAResult;
	XZIPEntryPCADiffResult_t	EntryPCADiffResult;
} XZIPCQRst_t;

#define M_CLEAR_XZIP_QBODY(PTR) do{\
	PTR-> XZIPSQCmdType.ubOpcode= 0;\
	PTR-> XZIPSQCmdType.ubTagID = 0;\
	PTR-> XZIPSQCmdType.ulInfo0 = 0;\
	PTR-> XZIPSQCmdType.ulInfo1 = 0;\
}while(0)

#define M_SET_FW_WAIT_PCA()							 (R16_XZIP[R16_XZIP_FW_CTRL_0] |= (BIT0 & FW_WAIT_PCA_MASK) << FW_WAIT_PCA_SHIFT)
#define M_CLEAR_FW_WAIT_PCA()						 (R16_XZIP[R16_XZIP_FW_CTRL_0] &= ~((BIT0 & FW_WAIT_PCA_MASK) << FW_WAIT_PCA_SHIFT))
#define M_XZIP_CHECK_FW_WAIT_PCA()					 (R16_XZIP[R16_XZIP_FW_CTRL_0] & ((BIT0 & FW_WAIT_PCA_MASK) << FW_WAIT_PCA_SHIFT))
#define M_SET_XZIP_PROGRAM_RSLT_MISS()               (R16_XZIP[R16_XZIP_FW_CTRL_0] |= ((BIT0 & FW_SET_PGRM_RSLT_MISS_MASK) << FW_SET_PGRM_RSLT_MISS_SHIFT))
#define M_CLEAR_XZIP_PROGRAM_RSLT_MISS()             (R16_XZIP[R16_XZIP_FW_CTRL_0] &= ~((BIT0 & FW_SET_PGRM_RSLT_MISS_MASK) << FW_SET_PGRM_RSLT_MISS_SHIFT))
#define M_SET_XZIP_READ_RSLT_MISS()                  (R16_XZIP[R16_XZIP_FW_CTRL_0] |= ((BIT0 & FW_SET_READ_RSLT_MISS_MASK) << FW_SET_READ_RSLT_MISS_SHIFT))
#define M_CLEAR_XZIP_READ_RSLT_MISS()                (R16_XZIP[R16_XZIP_FW_CTRL_0] &= ~((BIT0 & FW_SET_READ_RSLT_MISS_MASK) << FW_SET_READ_RSLT_MISS_SHIFT))
#define M_SET_XZIP_GEN_ENTRY_MISS()                  (R16_XZIP[R16_XZIP_FW_CTRL_0] |= ((BIT0 & FW_SET_GEN_ENTRY_MISS_MASK) << FW_SET_GEN_ENTRY_MISS_SHIFT))
#define M_CLEAR_XZIP_GEN_ENTRY_MISS()                (R16_XZIP[R16_XZIP_FW_CTRL_0] &= ~((BIT0 & FW_SET_GEN_ENTRY_MISS_MASK) << FW_SET_GEN_ENTRY_MISS_SHIFT))
#define M_SET_INT_SRAM_PARITY()                      (R16_XZIP[R16_XZIP_FW_CTRL_0] |= ((BIT0 & FW_INT_SRAM_PARITY_MASK) << FW_INT_SRAM_PARITY_SHIFT))
#define M_CLEAR_INT_SRAM_PARITY()                    (R16_XZIP[R16_XZIP_FW_CTRL_0] &= ~((BIT0 & FW_INT_SRAM_PARITY_MASK) << FW_INT_SRAM_PARITY_SHIFT))
#define M_SET_WAIT_PCA_TIMEOUT()                     (R16_XZIP[R16_XZIP_FW_CTRL_0] |= ((BIT0 & FW_INT_WAIT_PCA_TIMEOUT_MASK) << FW_INT_WAIT_PCA_TIMEOUT_SHIFT))
#define M_CLEAR_WAIT_PCA_TIMEOUT()                   (R16_XZIP[R16_XZIP_FW_CTRL_0] &= ~((BIT0 & FW_INT_WAIT_PCA_TIMEOUT_MASK) << FW_INT_WAIT_PCA_TIMEOUT_SHIFT))

#define	M_XZIP_GET_BUSY()							 (R16_XZIP[R16_XZIP_FW_RESULT_0] & XZIP_BUSY_ING_BIT)

#define M_CHECK_SRCH_XZIP_CMD_EMPTY()				 ((R32_XZIP[R32_XZIP_SRAM_FAIL_STATE] >> SRCH_XZIP_CMD_EMPTY_SHIFT) & SRCH_XZIP_CMD_EMPTY_MASK)

#define M_SET_XZIP_FPGA_SEL(VAL)					 (R8_XZIP[R8_XZIP_FPGA_SEL] = VAL)
#define M_SET_DB_LED(VAL)						 	 (R8_XZIP[R8_XZIP_DB_LED] = VAL)
#define M_SET_EXP_HIT_LED(VAL)						 (R8_XZIP[R8_XZIP_DB_EXP_HIT_LED] = VAL)

#define M_XZIP_GET_PB_ADDR(IDX)						((R16_XZIP[R16_XZIP_PB_ADDR + IDX] >> XZIP_PB_ADDR_SHIFT) & XZIP_PB_ADDR_MASK)

void XZIPDelegateCmd(void);
AOM_INIT_2 void XZIPInterruptEnable(void);
void XZIPRegSETPgrmRsltMiss(U8 ubSet);
void XZIPRegSETReadRsltMiss(U8 ubSet);
void XZIPClearRegister(U32 ulCallBackAddr);
U8 XZIPLock(U8 ubXZIPIndex, U32 ulCallBackAddr, U16 uwCallBackData);
U8 XZIPUnLock(U8 ubXZIPIndex, U32 ulCallBackAddr, U16 uwCallBackData);
U8 XZIPSearchPCA(U32 ulPCA, U32 ulCallBackAddr, U16 uwCallBackData);
AOM_INIT_2 U8 XZIPAdjustLen(U16 uwEntryNum, U32 ulCallBackAddr, U16 uwCallBackData);
U8 XZIPRegCheckGenInstEqualZero(void);
void XZIPEntryPCADiff(U32 ulCallBackAddr, U8 ubClrPCA, U8 ubClrFirstHit);

//Temp function
AOM_INIT_2 void XZIPForceAdjustLen(U16 uwEntryNum);

#endif /* _XZIP_API_H_ */
