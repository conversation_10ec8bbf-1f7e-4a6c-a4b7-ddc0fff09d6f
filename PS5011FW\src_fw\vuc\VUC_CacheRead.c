#include "flash_spec.h"
#include "hal/fip/fip.h"
#include "hal/fip/fpu.h"
#include "hal/pic/uart/uart_api.h"
#include "hal/sys/api/efuc/efuse_api.h"
#include "nvme_api/nvme/shr_hal_nvme.h"
#include "vuc/VUC_CacheRead.h"
#include "host/VUC_handler.h"
#include "vuc/VUC_command.h"

void VUC_CacheRead(VUC_OPT_HCMD_PTR_t pCmd)
{
	if (TRUE == gFlhEnv.ulFlashDefaultType.BitMap.btNoSupport) {
		pCmd->ubState = CMD_ERROR;
		gVUCVar.ubVucCmdErrStatus = HOST_ERROR_FEATURE;
		return;
	}

	U32 ulLength = pCmd->vuc_sqcmd.vendor.cacheR.ulLength * BYTE_PER_DW;
	U8 ubCE = pCmd->vuc_sqcmd.vendor.cacheR.ubCE;
	U8 ubIsRawData = pCmd->vuc_sqcmd.vendor.cacheR.btRawData;
	U8 ubMode = pCmd->vuc_sqcmd.vendor.cacheR.ubCmdMode;
	U8 ubCh, ubFlashCE, ubChannel;
	REG32 *pFlaReg = NULL;

	if (0 != (ulLength % FRAME_SIZE)) {
		ulLength = (ulLength / FRAME_SIZE) + FRAME_SIZE;
	}

	ubChannel = gubFipCEmapping[ubCE] / MAX_CE_PER_CHANNEL;
	ubFlashCE = gubFipCEmapping[ubCE] % MAX_CE_PER_CHANNEL;
	for (ubCh = 0; ubCh < gFlhEnv.ubChannelExistNum; ubCh++) {
		pFlaReg = R32_FCTL_CH[ubCh];
		if (ubCh == ubChannel) {
			if (0 == ubIsRawData) {
				pFlaReg[R32_FCTL_ECC_CFG] |= SET_LDPC_COR_EN;

				FlaSetRandomizeConfig(M_GET_3DRANDOMIZER_ENABLE(), ENABLE, ENABLE, gFlhEnv.uwPageByteCnt >> FIP_FOURK_PAGE_LOG);
			}
			else {
				pFlaReg[R32_FCTL_ECC_CFG] &= CLR_LDPC_COR_DIS;

				FlaSetRandomizeConfig(M_GET_3DRANDOMIZER_ENABLE(), DISABLE, DISABLE, gFlhEnv.uwPageByteCnt >> FIP_FOURK_PAGE_LOG);
			}

			pFlaReg[R32_FCTL_FTA_ROW]  = 0;

			FlaCEControl(ubCh, ubFlashCE, ENABLE);
			// Program Data
			FlaDMAConfig(ubCh, 0, ulLength >> FIP_FOURK_PAGE_LOG, READ_SPARE_IRAM_OFFSET_SCAN_WINDOW + SPARE_SIZE * ENTRY_PER_PLANE * ubCh, (gulVUCBufAddr + ubCh * gFlhEnv.uwPageByteCnt));
#if(CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_QLC)//ems mst add--karl
			if (A2_MODE == ubMode) {
				pFlaReg[R32_FCTL_FPU_ENTRY] = FPU_PTR_OFFSET(fpu_entry_test_C06_A6_CE0_DR);
			}
			else {
				pFlaReg[R32_FCTL_FPU_ENTRY] = FPU_PTR_OFFSET(fpu_entry_test_C06_A6_CE0_DR);
			}

#else
			if (A2_MODE == ubMode) {
				pFlaReg[R32_FCTL_FPU_ENTRY] = FPU_PTR_OFFSET(fpu_entry_test_C05_A5_CE0_DR);
			}
			else {
				pFlaReg[R32_FCTL_FPU_ENTRY] = FPU_PTR_OFFSET(fpu_entry_test_C05_A5_CE0_DR);
			}
#endif
		}
		else {
			pFlaReg[R32_FCTL_FTA_ROW]  = 0;

			FlaCEControl(ubCh, ubFlashCE, ENABLE);
			// Program Data
			//memset((void *)(SCAN_PROGRAM_BUF_BASE + ubCh * gFlhEnv.uwPageByteCnt), 0xA, ulLength);
			FlaDMAConfig(ubCh, 0, ulLength >> FIP_FOURK_PAGE_LOG, PROGRAM_SPARE_IRAM_OFFSET_SCAN_WINDOW + SPARE_SIZE * ENTRY_PER_PLANE * ubCh, ((SCAN_PROGRAM_BUF_BASE + ubCh * gFlhEnv.uwPageByteCnt) ));
#if(CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_QLC)//ems mst add--karl
			if (A2_MODE == ubMode) {
				pFlaReg[R32_FCTL_FPU_ENTRY] = FPU_PTR_OFFSET(fpu_entry_test_C85_A6_DW);
			}
			else {
				pFlaReg[R32_FCTL_FPU_ENTRY] = FPU_PTR_OFFSET(fpu_entry_test_C85_A6_DW);
			}

#else
			if ((CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_QLC)) {
				if (A2_MODE == ubMode) {
					pFlaReg[R32_FCTL_FPU_ENTRY] = FPU_PTR_OFFSET(fpu_entry_test_CA2_C85_A5_DW);
				}
				else if (DA_MODE == ubMode) { //micron
					pFlaReg[R32_FCTL_FPU_ENTRY] = FPU_PTR_OFFSET(fpu_entry_test_CA2_C85_A5_DW);
				}
				else {
					pFlaReg[R32_FCTL_FPU_ENTRY] = FPU_PTR_OFFSET(fpu_entry_test_CA2_C85_A5_DW);
				}
			}
			else {
#if (FW_CATEGORY_FLASH == FLASH_YMTC_TAS_TLC)
				pFlaReg[R32_FCTL_FPU_ENTRY] = FPU_PTR_OFFSET(fpu_entry_test_C85_A5_DW_TAS);
#elif (FW_CATEGORY_FLASH == FLASH_YMTC_WTS_TLC)//zerio wts add
				pFlaReg[R32_FCTL_FPU_ENTRY] = FPU_PTR_OFFSET(fpu_entry_test_C85_A5_DW_WTS);
#else
				pFlaReg[R32_FCTL_FPU_ENTRY] = FPU_PTR_OFFSET(fpu_entry_test_C85_A5_DW);
#endif
			}
#endif

		}
	}
	for (ubCh = 0; ubCh < gFlhEnv.ubChannelExistNum; ubCh++) {
		M_FIP_TRIG_FPU(ubCh);
	}
	for (ubCh = 0; ubCh < gFlhEnv.ubChannelExistNum; ubCh++) {
		//check write done
		pFlaReg = R32_FCTL_CH[ubCh];
		FlaCheckFPUBusy(ubCh, GERNAL_TIMEOUT_THRESHOLD);
		M_UART(VUC_, "Channel:%l Write Done\t", ubChannel);
		FlaCEControl(ubCh, ubFlashCE, DISABLE);
	}
}
