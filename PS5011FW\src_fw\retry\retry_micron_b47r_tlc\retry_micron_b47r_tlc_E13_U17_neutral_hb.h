#ifndef _RETRY_MICRON_B47R_TLC_E13_NEUTRAL_HB_H_
#define _RETRY_MICRON_B47R_TLC_E13_NEUTRAL_HB_H_
/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */

#include "env.h"
//V7 USE V6 RDT Setting
#if (((PS5013_EN) || (PS5017_EN)) && ((FLASH_B47R_TLC == FW_CATEGORY_FLASH)) && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER))
/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */
/*
#define HBIT_RETRY_MICRON_ORI_STEP_NUM				(6)
#define HBIT_RETRY_MICRON_ARC_STEP_NUM				(4)
#define HBIT_RETRY_MICRON_ACRR_STEP_NUM				(4)
#define HBIT_RETRY_MICRON_READ_STEP_NUM				(1) // normal
#define HBIT_RETRY_MICRON_ACRR_START_STEP			(HBIT_RETRY_MICRON_READ_STEP_NUM)
#define HBIT_RETRY_MICRON_ARC_START_STEP			(HBIT_RETRY_MICRON_ACRR_START_STEP + HBIT_RETRY_MICRON_ACRR_STEP_NUM)
#define HB_RETRY_MICRON_ALL_NUM						(HBIT_RETRY_MICRON_ORI_STEP_NUM + HBIT_RETRY_MICRON_ACRR_STEP_NUM) // 6 + 4
#define M_CHECK_IF_HB_STEP_IN_ACRR_RANGE(MACRO_HB_STEP)	(((HBIT_RETRY_MICRON_ACRR_START_STEP <= (MACRO_HB_STEP)) && ((MACRO_HB_STEP) < HBIT_RETRY_MICRON_ARC_START_STEP)) ? TRUE : FALSE)
#define M_GET_ACRR_STEP_FROM_HB_STEP(MACRO_HB_STEP)		(MACRO_HB_STEP - HBIT_RETRY_MICRON_ACRR_START_STEP)

#define HB_RETRY_MICRON_READ_OFFSET_STEP_NUM		(2)

#define HBIT_RETRY_MICRON_TLC_FEA_CNT				(HB_RETRY_MICRON_ALL_NUM)
#define HBIT_RETRY_MICRON_SLC_FEA_CNT				(HB_RETRY_MICRON_ALL_NUM)

#define HB__RETRY_MICRON_ORI_STEP					(0)
#define HB__RETRY_MICRON_READ_OFFSET_STEP			(HB__RETRY_MICRON_ORI_STEP + HBIT_RETRY_MICRON_ORI_STEP_NUM)
#define HBIT_RETRY_MICRON_LPI_STEP					(HB__RETRY_MICRON_READ_OFFSET_STEP + HB_RETRY_MICRON_READ_OFFSET_STEP_NUM)

#define HBIT_RETRY_MICRON_ARC_STEP					(HBIT_RETRY_MICRON_ORI_STEP_NUM + HBIT_RETRY_MICRON_ACRR_STEP_NUM)
*/
#define HBIT_RETRY_B47R_TLC_512G_STEP_NUM			(81 + 1)
#define HBIT_RETRY_B47R_SLC_512G_STEP_NUM			(8 + 1)

/*
 * ---------------------------------------------------------------------------------------------------
 *  enum
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */

#endif /* ((PS5013_EN) && (FLASH_HYNIXV6_TLC == FW_CATEGORY_FLASH) && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER)) */
#endif /* _RETRY_HYNIX_V6_TLC_E13_NEUTRAL_HB_H_ */
