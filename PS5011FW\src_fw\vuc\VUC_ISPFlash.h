#ifndef _VUC_ISPFLASH_H_
#define _VUC_ISPFLASH_H_

#if BOOTLOADER_EN
#include "host/VUC_host.h"
#include "table/sys_area/sys_area_api.h"
#include "burner/codepointer.h" // MAX_SECTION_NUM

/*
 * ---------------------------------------------------------------------------------------------------
 *   static global variables
 * ---------------------------------------------------------------------------------------------------
 */
#if (BURNER_MODE_EN || BOOTLOADER_MODE_EN)
//Code sign Blk maker
extern U8 const gCode_Sign_Blk_Mark[16];
#endif /* BURNER_MODE_EN || BOOTLOADER_MODE_EN */
#endif /* BOOTLOADER_EN */

//==============================================================================
// Code Block Configure Define
//==============================================================================
#if BOOTLOADER_EN
#define CODE_SECTION_NUM		(10) // FW:ATCM,BANK,BTCM,Andes,FIP; BootLoader:ATCM,BANK,BTCM,Andes,FIP
#define CODESINGHEAD_MAX_CNT    (2)
#define VUC_ISP_FLASH_PACKAGE_SIZE  DEF_KB(64)
#define VUC_ISP_FLASH_PROGRAM_SIZE_THRESHOLD     (BURNER_HOST_BIN_FILE_SIZE - VUC_ISP_FLASH_PACKAGE_SIZE)
#else /* BOOTLOADER_EN*/
#define CODE_SECTION_NUM		(5)
#endif /* BOOTLOADE_EN */
#define MAX_FW_SLOT_NUM			(15)
#define VUC_HMAC_KEY_LENGTH 	(64) // Bytes
#define	VUC_BURNER_SHA_DEAL_SECTION	DEF_KB(48)

#define VUC_ISP_FLASH_NAND_ZQCL_ENHANCE			(BIT0)

#if PS5021_EN
#define PAGE_NUM_PER_SEED               (16)
#endif /*PS5021_EN*/

#if PS5017_EN
#define PAD_PAGE_ENABLE							(TRUE)
#endif /* PS5017_EN */

#define	VUC_MOVE_SLOT_FORWARD	(1)
#define	VUC_MOVE_SLOT_BACKWARD	(2)

typedef struct {
	U32 ulsize;
	U32 ulbase;
} CodeSectionTemplate_t;

typedef struct VUCIDPageClockConfig {
	U8 ubSystemClockSrc;
	U8 ubSystemClockDiv;
	U8 ubCPUClockSrc;
	U8 ubCPUClockDiv;
	U8 ubECCClockSrc;
	U8 ubECCClockDiv;
	U8 ubAESClockSrc;
	U8 ubAESClockDiv;
	U8 ubSHAClockSrc;
	U8 ubSHAClockDiv;
} VUCIDPageClockConfig_t;

#if BOOTLOADER_EN
typedef struct {
	U8 ubProgramFlashCnt;   // count of program flash during ISP_Flash
	U8 ubStartSectionIdx;   // the start section to process GetCodeSectionInfo(), ProgramFlashCalculateCRC(), ProgramCodeBlock(), etc...
	U8 ubEndSectionIdx;     // the end section to process GetCodeSectionInfo(), ProgramFlashCalculateCRC(), ProgramCodeBlock(), etc...
	U8 ubSectionGetInfoCnt[BOOTLOADER_MAX_SECTION_NUM]; // get separatly section count

	U8 ubSectionPageCnt[BOOTLOADER_MAX_SECTION_NUM];  // record each section's page cnt for setting code pointer
	U16 uwBinFileTotalPageCnt;
	U16 uwSectionSectorCnt[BOOTLOADER_MAX_SECTION_NUM]; // record each section's sector cnt for setting code pointer

	U16 uwFlashInfoPage[MAX_CHANNEL]; // record the page index in each CH during program code block
	U16 uwFlashInfoBankingBlkPage[MAX_CHANNEL]; // record the page index of Code_Bank_Section in code bank block
	U32 ulLBA; // transient LBA, be used for AES descreamble

	U32 ulBootLoaderTotalSectionSize;   // BootLoader executed sections size
	U32 ulFWTotalSectionSize;        // FW executed sections size, be used for calculate CodeSign
	U32 ulSectionSizeLeftOnBuf; // record the size on buf of separated section
	U32 ulSignatureSizeLeftOnBuf; // record the size on buf of separated signature
	U32 ulHostBinFileBufferDestAddr;    // start address for memcpy, from VENDOR_BUFFER to HOST_BIN_FILE_BUFFER
	U32 ulSectionTotalSize[BOOTLOADER_MAX_SECTION_NUM]; // get section's total size from CSH
	U32	ulSeparatedSectionProgramedSize[BOOTLOADER_MAX_SECTION_NUM]; // record the size being programed

	U32 ulCodeSignHeaderBase[CODESINGHEAD_MAX_CNT];    // record the CSH base address on HOST_BIN_FILE_BUFFER

	union {
		U8 ubAll;
		struct {
			U8 btISPFlashDoing	: 1; // indicate that ISP flash is doing and not the first dispatch of ISP flash VUC
			U8 btISPFlashCommit : 1;
			U8 btISPFlashVerifyDigest : 1;
			U8 btBootLoaderDoubleSign : 1;
			U8 btGetBankSectionComplete : 1;
			U8 btGetFWFIPSectionComplete : 1;
			U8 Reserve : 2;
		} B;
	} Flag;
} ISPFlashTransientInfo_t;

typedef enum CalculateCRCMode {
	CAL_MODE_IDPG = 0,
	CAL_MODE_CODEBODY
} CalculateCRCMode_t;

#if BURNER_MODE_EN
extern ISPFlashTransientInfo_t gISPFlashTransientInfo;
#endif /* BURNER_MODE_EN */
#endif /* BOOTLOADER_EN */

extern VUCIDPageClockConfig_t gVUCIDPageClockConfig;

void VUC_IspFlash(VUC_OPT_HCMD_PTR_t pCmd);
U8 VendorProgramFlash(U8 ubScreamble, U8 ubEncrypt, U16 uwFWSlotBitMap, U8 ubActSlotIdx, U32 ulTotalSize);

AOM_DLMC U8 ProgramDummy(U16 uwPage, SystemAreaBlock_t CodeBlock, U32 ulAddr);
#if BOOTLOADER_EN
AOM_BOOTLOADER_EXTEND1 void FWCodeBlk_SetCodePointer(U8 ubActSlotIdx, U16 uwSlotBitMap);
AOM_BOOTLOADER_EXTEND1 U8 FWCodeBlk_IDPGInit(void);
AOM_BOOTLOADER_EXTEND1 U8 FWCodeBlk_InitFWSlotInfo(void);
#else /* BOOTLOADER_EN */
AOM_DLMC void FWCodeBlk_SetCodePointer(U8 ubActSlotIdx, U16 uwSlotBitMap);
AOM_DLMC U8 FWCodeBlk_IDPGInit(void);
AOM_DLMC U8 FWCodeBlk_InitFWSlotInfo(void);
#endif /* BOOTLOADER_EN */

#endif /* _VUC_ISPFLASH_H_ */
