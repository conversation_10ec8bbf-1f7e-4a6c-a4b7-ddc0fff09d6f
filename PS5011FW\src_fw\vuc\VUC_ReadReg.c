#include "hal/pic/uart/uart_api.h"
#include "host/VUC_handler.h"
#include "nvme_api/nvme/shr_hal_nvme.h"
#include "VUC_ReadReg.h"

void VUC_ReadReg(VUC_OPT_HCMD_PTR pCmd)
{
	M_UART(VUC_, "\nVUC_READ_REG");

	U8 *ubSrcBuf = (U8 *)pCmd->vuc_sqcmd.vendor.ReadWriteReg.ulRegAddr;
	U8 *ubDesBuf = (U8 *)(pCmd->ulCurrentPhysicalMemoryAddr);
	U8  ubByteCnt = ((pCmd->vuc_sqcmd.raw_data.dw[12] & 0x0000FF00) >> 8);
	U8  ubi;

	if ((1 == ubByteCnt) || (2 == ubByteCnt) || (4 == ubByteCnt) || (8 == ubByteCnt)) {
		for (ubi = 0; ubi < ubByteCnt; ubi++) {
			ubDesBuf[ubi] = ubSrcBuf[ubi];
		}
	}
	else {
		pCmd->ubState = CMD_ERROR;
		gVUCVar.ubVucCmdErrStatus = HOST_ERROR_PARAMETER;
	}

	M_UART(VUC_, "\nSrc[%l] to Buf[%l] Len %l", pCmd->vuc_sqcmd.vendor.ReadWriteReg.ulRegAddr, pCmd->ulCurrentPhysicalMemoryAddr, ubByteCnt);
}
