/*
 * rs.h
 *
 *  Created on: 2018�~7��30��
 *      Author: lin_chen
 */

#ifndef SRC_FW_HAL_RS_RS_H_
#define SRC_FW_HAL_RS_RS_H_
#include "typedef.h"
#include "common/fw_common.h"
#include "fw_vardef.h"
#include "aom/aom_api.h"

#define RAIDECC_EXTERNAL_BUF_IDX_DEFAULT        (0x1F)

#define SPARE_SIZE_DCACHE               (0x40)    // 64B

#define  MOVE_DATA_TO_TEMP_BUF            (0)
#define  MOVE_DATA_FROM_TEMP_BUF          (1)

#define  RS_PBUF_NUMBER                   (4)
#define RS_BPT_TABLE_PARITY_OFFSET        (253)


void RSDelegateCmd(void);
//AOM_RS_2 U8 FTLRSCheckEncodeCnt(U8 ubTargetRSTag, U8 ubSourceRSTag);
#endif /* SRC_FW_HAL_RS_RS_H_ */
