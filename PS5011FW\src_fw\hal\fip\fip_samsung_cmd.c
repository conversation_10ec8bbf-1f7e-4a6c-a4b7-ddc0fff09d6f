/****************************************************************************
*
*  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
*  All rights reserved
*
*  The content of this document is confidential and shall be applied
*  subject to the terms and conditions of the license agreement and
*  other applicable laws. Any unauthorized access, use or disclosure
*  of this document is strictly prohibited and may be punishable
*  under laws.
*
*  fip_toshiba_cmd.c
*
*
*
****************************************************************************/
/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include <string.h>
#include "hal/fip/fip_api.h"
#include "hal/fip/fip.h"
#include "retry/retry_api.h"
#include "debug/debug.h"
/*
 * ---------------------------------------------------------------------------------------------------
 *   public
 * ---------------------------------------------------------------------------------------------------
 */
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)

void FipGetUniqueID(U8 ubChannel, U8 ubFlashCE, U8 ubDie, U8 *pubBufAddr)
{
	U16 uwUniqueIDSize = 256;
	U16 uwDatatmp;
	U8 *pubUniqueID = (U8 *)(pubBufAddr);
	REG32 *pFlaReg = (REG32 *)R32_FCTL_CH[ubChannel];
	U16 uwi;

	memset((void *)(pubBufAddr), 0x00, DEF_512B);
	FlaResetAllCE(0xFF);
	FlaCEControl(ubChannel, ubFlashCE, ENABLE);

	pFlaReg[R32_FCTL_PIO_CMD] = 0xDA;
	pFlaReg[R32_FCTL_PIO_CMD] = 0x30;
	pFlaReg[R32_FCTL_PIO_CMD] = 0x65;
	pFlaReg[R32_FCTL_PIO_CMD] = 0x00;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
#if (FW_CATEGORY_FLASH == FLASH_SAMSUNG_V7_TLC || FW_CATEGORY_FLASH == FLASH_SAMSUNG_V8_TLC)
	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
#endif
	pFlaReg[R32_FCTL_PIO_CMD] = 0x30;
	if ( FAIL == FlaWaitFlashReady(GERNAL_TIMEOUT_THRESHOLD)) {
		gFlhEnvMP.ulFailedCEBMP |= BIT(gubLogicalCEMapPhysicalCE[ubChannel][ubFlashCE]);
#if (!BURNER_MODE_EN)
		M_FW_CRITICAL_ASSERT(ASSERT_HAL_FIP_0x06BB, FALSE);//This means check status timeout , something wrong
#endif /* (!BURNER_MODE_EN) */
	}
	//	pFlaReg[R32_FCTL_PIO_CMD] = 0x00;
	//	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
	//	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
	//	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
	//	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
	//	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
	//	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
	//	pFlaReg[R32_FCTL_PIO_CMD] = 0x05;
	//	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
	//	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
	//	pFlaReg[R32_FCTL_PIO_CMD] = 0xE0;
	//wait 1ms
	M_RTT_IDLE_MS(1);

	pFlaReg[R32_FCTL_HS_MODE] |= SET_DAT_DEFAULT;
	if (gFlhEnv.ubCurrentInterface == LEGACY_INTERFACE) {
		for (uwi = 0; uwi < uwUniqueIDSize; uwi++) {
			pubUniqueID[uwi] = pFlaReg[R32_FCTL_PIO_DAT];
		}
	}
	else if ((gFlhEnv.ubCurrentInterface == TOGGLE_INTERFACE) || (gFlhEnv.ubCurrentInterface == TOGGLE2_INTERFACE)) {
		for (uwi = 0; uwi < uwUniqueIDSize; uwi += 2) {
			pFlaReg[R32_FCTL_HS_MODE] &= CLR_DAT_MID;
			if (uwi == 0) {
				pFlaReg[R32_FCTL_HS_MODE] |= PIO_DAT_FIRST_BIT;
			}
			else if (uwi == (uwUniqueIDSize - 2)) {
				pFlaReg[R32_FCTL_HS_MODE] |= PIO_DAT_LAST_BIT;
			}
			uwDatatmp = (U16)pFlaReg[R32_FCTL_PIO_DAT];
			pubUniqueID[uwi] = (U8)(uwDatatmp & 0xFF);
			pubUniqueID[uwi + 1] = (U8)((uwDatatmp >> 8) & 0xFF);
		}
	}

	//	for (uwi = 0; uwi < 256; uwi++) {
	//		M_UART(RDT_TEST_, "\n pubUniqueID[%d] = %x", uwi, pubUniqueID[uwi]);
	//	}

	pFlaReg[R32_FCTL_HS_MODE] |= SET_DAT_DEFAULT;
	FlaCEControl(ubChannel, ubFlashCE, DISABLE);
	FlaResetAllCE(0xFF);
}

#endif
