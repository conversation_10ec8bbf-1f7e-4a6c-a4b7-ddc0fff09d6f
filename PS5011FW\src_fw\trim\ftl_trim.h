#ifndef _FTL_TRIM_H_
#define _FTL_TRIM_H_

#include "common/fw_common.h"
#include "debug/debug_setup.h"
#include "trim/ftl_trim_api.h"

//==============================================================================
// Definitions
//==============================================================================
#define TRIM_JOURNAL_ENTRY_NUM_MAX	((TRIM_CMD_RANGE_NUM_MAX) + 1) //Possible max Entry # in a Journal(+1:Special Ending Entry)
#define TRIM_LBA_DATA_NUM_MAX		((TRIM_CMD_RANGE_NUM_MAX) * 2) //Max 2 Trim-LBA Tasks for a Trim Range
#define TRIM_RANGE_ST3_TASK_MASK	(0x07) //Mask for ST3 Trim Tasks
#define TRIM_LBA_HANDLE_NUM_MAX		(10) //# of Ranges being handled at the same time in Trim-LBA flow
#define TRIM_INVALID_PTE_IDX		(0x1FFFFF) //21-Bits invalid PTE Index

#if PS5021_EN
#define TRIM_DEP_TAG_DEFAULT_BASE		(0xA0A0)
#else /* PS5021_EN */
#define TRIM_DEP_TAG_DEFAULT_BASE		(0xA0)
#endif /* PS5021_EN */
#define TRIM_SPOR_INPUT_JOURNAL_NUM_MAX	(2) //The max # of Trim Journals that can be handled at the same time in SPOR Trim Flow (Limited Buffers)
#define TRIM_RANGE_NUM_FOR_WRITE_ZERO	(1) //Fixed Trim-Range number for mode Write-Zero
#define TRIM_RANGE_NUM_FOR_FORMAT_NVM	(1) //Fixed Trim-Range number for mode Format-NVM
#define TRIM_RANGE_NUM_FOR_VUC_MICRON	(1) //Fixed Trim-Range number for mode VUC-Micron
#define TRIM_PRP_SIZE_IN_32B			(SIZE_IN_32B_CEILING(4096))

//Trim thresholds
#if (DEBUG_CORNER_TEST_43_EN)
#define TRIM_CMD_TIME_OUT_VALUE			(0)
#else /*(DEBUG_CORNER_TEST_43_EN)*/
#define TRIM_CMD_TIME_OUT_VALUE			(2000)
#endif /*(DEBUG_CORNER_TEST_43_EN)*/
#define TRIM_TRIGGER_BG_TIME_THRESHOLD	(50)	//Trigger BG Trim when idle(RCQ/WCQ empty) time >= this value
#define TRIM_INSERT_NODE_THRESHOLD		(512)	//# of LCAs threshold to trigger "Trim Insert Node" flow. (Must < 1024)
#define TRIM_RANGE_THRESHOLD_TO_SEND_HOST_EARLIER	(48)	//To send host earlier if remaining Ranges < this value

//Trim Range (Raw Format from APU) info definitions for DMAC sort & merge
#if (NVME == HOST_MODE)
#define TRIM_RANGE_START_LBA_OFFSET		(TRIM_RANGE_NVME_START_LBA_OFFSET)
#define TRIM_RANGE_START_LBA_BIT_NUM	(TRIM_RANGE_NVME_START_LBA_BIT_NUM)
#define TRIM_RANGE_ENTRY_SIZE_FOR_DMAC	(TRIM_RANGE_NVME_ENTRY_SIZE_FOR_DMAC)
#elif (USB == HOST_MODE)
#define TRIM_RANGE_START_LBA_OFFSET		(TRIM_RANGE_USB_START_LBA_OFFSET)
#define TRIM_RANGE_START_LBA_BIT_NUM	(TRIM_RANGE_USB_START_LBA_BIT_NUM)
#define TRIM_RANGE_ENTRY_SIZE_FOR_DMAC	(TRIM_RANGE_USB_ENTRY_SIZE_FOR_DMAC)
#else
#define TRIM_RANGE_START_LBA_OFFSET		(TRIM_RANGE_SATA_START_LBA_OFFSET)
#define TRIM_RANGE_START_LBA_BIT_NUM	(TRIM_RANGE_SATA_START_LBA_BIT_NUM)
#define TRIM_RANGE_ENTRY_SIZE_FOR_DMAC	(TRIM_RANGE_SATA_ENTRY_SIZE_FOR_DMAC)
#endif
//NVME
#define TRIM_RANGE_NVME_START_LBA_OFFSET	(64)
#define TRIM_RANGE_NVME_START_LBA_BIT_NUM 	(32)	//Start-LBA takes 64bits.  But only use up to 32 bits (Max 2TB)
#define TRIM_RANGE_NVME_ENTRY_SIZE_FOR_DMAC	(DMAC_SORT_ENTRY_SIZE_16B)
//SATA
#define TRIM_RANGE_SATA_START_LBA_OFFSET	(0)
#define TRIM_RANGE_SATA_START_LBA_BIT_NUM 	(48)	//Start-LBA takes 48bits.  But only use up to 32 bits (Max 2TB)
#define TRIM_RANGE_SATA_ENTRY_SIZE_FOR_DMAC	(DMAC_SORT_ENTRY_SIZE_8B)
//USB
#define TRIM_RANGE_USB_START_LBA_OFFSET		(64)
#define TRIM_RANGE_USB_START_LBA_BIT_NUM 	(40)	//Start-LBA has 64bits.  But only use up to 40 bits (For DMAC sort, it will affect the sorting times and result address.) 
#define TRIM_RANGE_USB_ENTRY_SIZE_FOR_DMAC	(DMAC_SORT_ENTRY_SIZE_16B)


//==============================================================================
// Macro Functions
//==============================================================================
#define M_TRIM_IS_TRIM_Q_FULL()									((TRIM_QUEUE_DEPTH) - 1 == gTrim.Size)
#define M_TRIM_IS_TRIM_ADD_RANGE_DONE() 						(gTrim.InsertRange.ConsumeRangeCnt == gTrim.InsertRange.InsertRangeTotalNum)
#define M_TRIM_IS_TRIM_ADD_RANGE_DONE_FROM_JOURNAL()			(gTrim.SPOR.ubDoingJournalIdx == gTrim.SPOR.TotalJournalNum)
#define M_TRIM_IS_TRIM_ADD_RANGE_FINAL_RANGE()					(gTrim.InsertRange.ConsumeRangeCnt + 1 == gTrim.InsertRange.InsertRangeTotalNum)
#define M_TRIM_IS_TRIM_INSERT_JOURNAL_DONE()					((TRUE) == gTrim.Flags.btIsJournalEndingEntryDone)
#define M_TRIM_IS_TRIM_ALLOW_INSERT_NODE() 						((gTrim.InsertRange.CheckInsertNodeL4KCnt < (TRIM_INSERT_NODE_THRESHOLD)) && (0 != gTrim.InsertRange.CheckInsertNodeL4KCnt))
#define M_TRIM_IS_TRIM_CURRENT_RANGE_PARTIALLY_DONE() 			(0 != gTrim.InsertRange.uoPartialInsertRangeLBAStart)
#define M_TRIM_IS_TRIM_JOURNAL_ENTRY_CMD_END_VALUE(JournalEntry)(((JOURNAL_TRIM_CMD_ENDING_VALUE1) == (JournalEntry).uoLBAStart) && ((JOURNAL_TRIM_CMD_ENDING_VALUE2) == (JournalEntry).uoLBAEnd))
#define M_TRIM_GET_TRIM_DEFAULT_DEP_TAG()						((TRIM_DEP_TAG_DEFAULT_BASE) + (U8)gTrim.Mode) //Default DepTag. Different default value for each Mode.
#define M_TRIM_IS_TRIM_LBA_AND_INSERT_NODE_DONE()				((!gTrim.Flags.btDoingInsertRange) && (gTrim.ubTrimLBAHead == gTrim.ubTail) && (gTrim.ubInsertNodeHead == gTrim.ubTail))
#define M_TRIM_IS_HIT_SYNC_RANGE()								(gTrim.ubHead == gTrim.Sync.SyncQueue.uoEntries[gTrim.Sync.SyncQueue.Head].ubRangeIdx)
#define M_TRIM_GET_TRIM_RAW_RANGE_SIZE()						((gTrim.Flags.btIsForceNVMEFormat) ? (sizeof(TrimRangeNVMERaw_t)) : (sizeof(TrimRangeRaw_t)))

#define M_TRIM_GET_START_LBA_FROM_TRIM_RANGE(pTrimRangeEntry)	((((U64)pTrimRangeEntry->uwStartLBA_bt32_35) << 32) | ((U64)(pTrimRangeEntry->ulStartLBA_bt0_31)))
#define M_TRIM_GET_END_LBA_FROM_TRIM_RANGE(pTrimRangeEntry)		((((U64)pTrimRangeEntry->uwEndLBA_bt32_35) << 32) | ((U64)(pTrimRangeEntry->ulEndLBA_bt0_31)))

#define M_TRIM_SET_START_LBA_TO_TRIM_RANGE(pTrimRangeEntry, uoSLBA)	do{\
		(pTrimRangeEntry->uwStartLBA_bt32_35 = (U8)((uoSLBA>>32) & BIT_MASK64(4)));\
		(pTrimRangeEntry->ulStartLBA_bt0_31  = (U32)((uoSLBA) & BIT_MASK64(32)));\
		}while(0)


#define M_TRIM_SET_END_LBA_TO_TRIM_RANGE(pTrimRangeEntry, uoELBA)	do{\
		(pTrimRangeEntry->uwEndLBA_bt32_35 = (U8)((uoELBA>>32) & BIT_MASK64(4)));\
		(pTrimRangeEntry->ulEndLBA_bt0_31  = (U32)((uoELBA) & BIT_MASK64(32)));\
		}while(0)

#if(NVME == HOST_MODE)
#define M_TRIM_IS_NEED_DELAY_SEND_HOST_FOR_GC_SUSTAIN()	((gTrim.Flags.btIsNeedCheckGCSustain) && (gpVT->Sustain.GCSustain.uwDelay || gCopyUnit.Sustain.uwDelay) && (0 != M_NVME_CMD_MANAGER_GET_DELAY_WRITE_TIMER()))
#else /*(NVME == HOST_MODE)*/
#define M_TRIM_IS_NEED_DELAY_SEND_HOST_FOR_GC_SUSTAIN()	((gTrim.Flags.btIsNeedCheckGCSustain) && (gpVT->Sustain.GCSustain.uwDelay || gCopyUnit.Sustain.uwDelay) && (0 != M_APU_GET_WR_MANAGER_CMD_DELAY_CNT()))
#endif /*(NVME == HOST_MODE)*/

#endif /* _FTL_TRIM_H_ */
