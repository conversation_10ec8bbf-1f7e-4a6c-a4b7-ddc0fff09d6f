#ifndef _FPU_H_
#define _FPU_H_

#include "env.h"

#if (CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_QLC)
#include "fpu_tsb_qlc.h"
#elif ((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC) && (MICRON_S17_E21_140S_EN) && (PS5017_EN))
#include "fpu_micron_tlc.h"
#elif ((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC) && (MICRON_S17_E21_140S_EN) && (PS5017_EN))//zerio n48r add
#include "fpu_micron_qlc.h"
#elif ((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC) && (MICRON_S17_E21_140S_EN) && (PS5021_EN))
#include "fpu_micron_b47r_5021.h"
#elif(((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC) && (!MICRON_S17_E21_140S_EN)) || (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC))
#include "fpu_micron_tlc_no140s.h"
#elif ((CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_2D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_SANDISK_3D_TLC))
#include "fpu_tsb_tlc.h"
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC)
#include "fpu_hynix_tlc.h"
#elif (FW_CATEGORY_FLASH == FLASH_SANDISK_BICS5_TLC || FW_CATEGORY_FLASH == FLASH_SANDISK_BICS6_TLC)
#include "fpu_sandisk_tlc.h"
#elif (FW_CATEGORY_FLASH == FLASH_SANDISK_BICS6_QLC)
#include "fpu_sandisk_qlc.h"
#elif (FW_CATEGORY_FLASH == FLASH_SANDISK_BICS8_TLC)//zerio BICS8 Add
#include "fpu_sandisk_BICS8_tlc.h"
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_TLC)
#include "fpu_ymtc_tlc.h"
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_QLC)//ems mst add--karl
#include "fpu_ymtc_qlc.h"
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC)	//Reip Porting 3D-V7 QLC Add
#include "fpu_hynix_qlc.h"
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
#include "fpu_samsung_tlc.h"
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_INTEL_3D_QLC)
#include "fpu_intel_qlc.h"
#endif /*(CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_QLC)*/

//=============================================================================
/* Common FPU define for all CONFIG_FLASH_TYPE */
//=============================================================================

#define FPU_SRC_PTR_SHIFT		(3)
#define FPU_SRC_PTR_MASK		(BIT_MASK(3))
#define FPU_SRC_PTR_SHIFT_MASK		(FPU_SRC_PTR_MASK<<FPU_SRC_PTR_SHIFT)
#define FPU_READ_RAW_DES_PTR_SHIFT		(0)
#define FPU_READ_RAW_DES_PTR_MASK		(BIT_MASK(3))
#define FPU_READ_RAW_DES_PTR_SHIFT_MASK		(FPU_READ_RAW_DES_PTR_MASK<<FPU_READ_RAW_DES_PTR_SHIFT)
#define FPU_READ_RAW_LOGIC_SHIFT			(6)
#define FPU_READ_RAW_LOGIC_MASK			(BIT_MASK(2))
#define FPU_READ_RAW_LOGIC_SHIFT_MASK	(FPU_READ_RAW_LOGIC_MASK << FPU_READ_RAW_LOGIC_SHIFT)
#define FPU_READ_RAW_MODE_SHIFT			(8)
#define FPU_READ_RAW_MODE_MASK			(BIT_MASK(2))
#define FPU_READ_RAW_MODE_SHIFT_MASK	(FPU_READ_RAW_MODE_MASK	<<FPU_READ_RAW_MODE_SHIFT)
#define M_SET_FPU_SRC_PTR(x)			((x)<<FPU_SRC_PTR_SHIFT)
#define FPU_IBF_DIRECTION_BACKUP		BIT1
#define FPU_IBF_DIRECTION_RESTORE		CLR_BIT1
#define FPU_IBF_PTR_SHIFT		(2)
#define FPU_IBF_PTR_MASK		(BIT_MASK(3))
#define FPU_IBF_PTR_SHIFT_MASK		(FPU_IBF_PTR_MASK<<FPU_IBF_PTR_SHIFT)
#define M_SET_FPU_IBF_PTR(x)			((x)<<FPU_IBF_PTR_SHIFT)
#define BYPASS_ADR_GEN_OFFSET			2

// FPU Shift for Backup P4K Workaound
// Micron
#define FPU_MICRON_SLC_3B_SHIFT_1		(1)
#define FPU_MICRON_TLC_QLC_SHIFT_1		(1) // QLC: 0x0D, TLC: NOP
// KIC
#define FPU_TOSHIBA_1P0V_B6_SHIFT_1		(1)
#define FPU_TOSHIBA_SLC_A2_SHIFT_1		(1)
#define FPU_TOSHIBA_QLC_0D_SHIFT_1		(1)
// YMTC
#define FPU_YMTC_SLC_DA_SHIFT_1			(1)
#define FPU_YMTC_TLC_DF_SHIFT_1			(1)
//Hynix V6
#define FPU_HYNIX_SLC_A2_SHIFT_1        (1)
//SanDisk BICS5
#define FPU_SanDisk_SLC_A2_SHIFT_1        (1)


// FPU shift for NTODT
#if NTODT_IMPLEMENT_EN
#define	FPU_NTODT_ITEM_SHIFT		(4) // occupied 4 FPU
#define	FPU_NTODT_ITEM_SHIFT_BYTE	(FPU_NTODT_ITEM_SHIFT << 1) //each FPU size is 2 bytes.
#define	FPU_NTODT_ITEM_SHIFT_5		(5) // occupied 5 FPU
#define	FPU_NTODT_ITEM_SHIFT_5_BYTE	(FPU_NTODT_ITEM_SHIFT_5 << 1) //each FPU size is 2 bytes.
#define	FPU_NTODT_ITEM_SHIFT_8		(8) // occupied 8 FPU
#define	FPU_NTODT_ITEM_SHIFT_8_BYTE	(FPU_NTODT_ITEM_SHIFT_8 << 1) //each FPU size is 2 bytes.
#else /* NTODT_IMPLEMENT_EN */
#define	FPU_NTODT_ITEM_SHIFT		(0) // occupied 4 FPU
#define	FPU_NTODT_ITEM_SHIFT_BYTE	(FPU_NTODT_ITEM_SHIFT << 1) //each FPU size is 2 bytes.
#define	FPU_NTODT_ITEM_SHIFT_5		(0) // occupied 5 FPU
#define	FPU_NTODT_ITEM_SHIFT_5_BYTE	(FPU_NTODT_ITEM_SHIFT_5 << 1) //each FPU size is 2 bytes.
#define	FPU_NTODT_ITEM_SHIFT_8		(0) // occupied 8 FPU
#define	FPU_NTODT_ITEM_SHIFT_8_BYTE	(FPU_NTODT_ITEM_SHIFT_8 << 1) //each FPU size is 2 bytes.
#endif /* NTODT_IMPLEMENT_EN */

#endif /* _FPU_H_ */

