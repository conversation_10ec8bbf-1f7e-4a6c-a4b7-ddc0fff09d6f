#include "hal/pic/uart/uart_api.h"
#include "nvme_api/nvme/shr_hal_nvme.h"
#include "vuc/VUC_command.h"
#include "vuc/VUC_SecurityPassThrough.h"

#if (BURNER_MODE_EN && (HOST_MODE == NVME))
void VUC_SecurityPassThrough(VUC_OPT_HCMD_PTR_t pCmd)
{
	U8 ubSubFea = pCmd->vuc_sqcmd.vendor.usrdef.ubSubFeature;

	M_UART(VUC_, "\nVUC_SP");
	M_UART(VUC_, "\nSubFea:%x", ubSubFea);

	switch (ubSubFea) {
	case VUC_SP_SUBFEA_GET_LOG_PAGE:
		Burner_nvme_get_log(pCmd);
		break;
	case VUC_SP_SUBFEA_IDENTIFY:
		pCmd->vuc_sqcmd.adm.idf.ubCns = 0x01;
		BurnerNVMEIdentify(pCmd);
		break;

	default:
		M_UART(VUC_, "\nVUC_SP Error Case!!!\n");
		break;
	}
}
#endif /* BURNER_MODE_EN */
