#ifndef _READ_DISTURB_H_
#define _READ_DISTURB_H_

void ReadDisturbSearchMaxRCDone_Callback(void);
enum ReadDisturbStateEnum  {
	READ_DISTURB_SEARCH_UNIT = 0,
	READ_DISTURB_WAIT_SEARCH_UNIT,
	READ_DISTURB_GET_RESULT,
	READ_DISTURB_HANDLE_UNIT
};

enum ReadDisturbCheckRuleEnum  {
	READ_DISTURB_SLC_RULE = 0,
	READ_DISTURB_XLC_RULE,
	READ_DISTURB_RULE_NUM
};

enum ReadDisturbAddDriveLogEnum  {
	READ_DISTURB_READVERIFY_LOG = 0,
	READ_DISTURB_FORCECOPY_LOG,
	READ_DISTURB_DRIVE_LOG_NUM
};

#endif /* _READ_DISTURB_H_ */
