/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  err_hdl_fpl_TurboRain.c                                               */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "debug/debug_setup.h"

#include <string.h>
#include "err_hdl_fpl_TurboRain.h"
#include "hal/fip/fpu.h"
#include "retry_setup.h"
#include "hal/dmac/dmac_pop_cmd.h"
#include "hal/dmac/dmac_api.h"
#include "common/mem.h"
#include "err_hdl_fpl_RS.h"
#include "buffer/buffer.h"
#include "ftl/ftl_api.h"
#include "setup.h"
#include "typedef.h"
#include "fw_vardef.h"
#include "hal/dmac/dmac_cmd_type.h"
#include "err_hdl_fpl_softbit_retry_table.h"

#include "aom/aom_api.h"


//NCS SBRAID
#include "err_hdl_fpl_sb_retry_api.h"
//#include "hal/sys/rng/rng_api.h"
/*
 * ---------------------------------------------------------------------------------------------------
 *   definitions
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *  enum
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *  data types
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *   macros
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *   extern global variables
 * ---------------------------------------------------------------------------------------------------
 */
TURBORAIN_TASK_t gTurboRainTask = {0};

/*
 * ---------------------------------------------------------------------------------------------------
 *   static global variables
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *   private prototypes
 * ---------------------------------------------------------------------------------------------------
 */

AOM_RETRY_SBRAID static U8 RetryTurboRainInit(void);
AOM_RETRY_SBRAID static void RetryTurboRainFlashLoadECCParam(U32 load_target);
AOM_RETRY_SBRAID static void RetryTurboRainFPUTrigger(U16 uwFpu_offset, U8 int_en, U16 vct);
AOM_RETRY_SBRAID static U32 RetryTurboRainFlashGetCurrentLDPCFrameSize(void);
AOM_RETRY_SBRAID static void RetryTurboRainCorrectMTTrigger( U8 dsp2_en, U8 sb_ibuf_ptr, U8 sel_2k, U8 dsp_en, U8 dsp_4k_en, U8 ubdecode_mode, U8 scale_mode, U8 flow_mode, U8 hb_sb, U8 ubMTIndex);
AOM_RETRY_SBRAID static void RetryTurboRainBackupCorrect2k(U8 src_ibf, U8 ldpc_frame_idx, U32 ulbackup_addr, U32 ultmp_addr, U32 Iram_addr, U32 TempIram_addr, U8 ubMode);
AOM_RETRY_SBRAID static U8 RetryTurboRainPrepareFreeMT(MT_Callback_Func_t PassCallback, MT_Callback_Func_t FailCallback);
AOM_RETRY_SBRAID static void RetryTurboRainTriggerCorrectFail_Callback(U8 ubMTIndex);
AOM_RETRY_SBRAID static void RetryTurboRainTriggerCorrectPass_Callback(U8 ubMTIndex);
AOM_RETRY_SBRAID static void RetryTurboRainDecodeFlow(void);
AOM_RETRY_SBRAID static void RetryTurboRainReleaseFinishMT(void);
AOM_RETRY_SBRAID static void RetryTurboRainFlashRetoreLLR(U8 lmu_sel, U8 llr_idx);
AOM_RETRY_SBRAID static void RetryTurboRainBackupRestore(U64 dram_addr, U32 iram_addr, U8 ibf_ptr, U8 direction, U8 mode, U8 bch_mode, U8 all, U8 frm_msk);




/*
 * ---------------------------------------------------------------------------------------------------
 *   private
 * ---------------------------------------------------------------------------------------------------
 */
#if (RETRY_TURBO_RAIN_EN)


/*
 * ---------------------------------------------------------------------------------------------------
 *   public
 * ---------------------------------------------------------------------------------------------------
 */

void RetryTurboRainFlashLoadECCParam(U32 load_target)
{
	/* Enable ECC clock for set configuration into ECC engine */
	M_FIP_ECC_CLOCK_GATING_DIS();

	/* Trigger load and check HW ready */
	R32_FCON[R32_FCON_ECC_PARAM_CFG] |= load_target;
	while (R32_FCON[R32_FCON_ECC_PARAM_CFG] & load_target);

	/* Disable ECC clock */
	M_FIP_ECC_CLOCK_GATING_EN();
}

void RetryTurboRainFPUTrigger(U16 uwFpu_offset, U8 int_en, U16 vct)
{
	R32_FCTL_CH[gTurboRainTask.ubChannel][R32_FCTL_INT_VCT] &= CLR_NORMAL_CPU;
	R32_FCTL_CH[gTurboRainTask.ubChannel][R32_FCTL_INT_VCT] |= (1 << NORMAL_CPU_SHIFT);

	R32_FCTL_CH[gTurboRainTask.ubChannel][R32_FCTL_INT_VCT] &= CLR_ERROR_CPU;
	R32_FCTL_CH[gTurboRainTask.ubChannel][R32_FCTL_INT_VCT] |= (1 << ERROR_CPU_SHIFT);

	R32_FCTL_CH[gTurboRainTask.ubChannel][R32_FCTL_INT_CFG] &= (~INT_VEC_EN_BIT);

	if (uwFpu_offset != 0xFFFF) {
		R32_FCTL_CH[gTurboRainTask.ubChannel][R32_FCTL_FPU_ENTRY] &= ~FPU_ADDR_BASE_SHIFT_MASK;
		R32_FCTL_CH[gTurboRainTask.ubChannel][R32_FCTL_FPU_ENTRY] |= uwFpu_offset;
	}

	M_FIP_TRIG_FPU(gTurboRainTask.ubChannel);
	while (M_FIP_CHK_FPU_BUSY(gTurboRainTask.ubChannel));
}


U32 RetryTurboRainFlashGetCurrentLDPCFrameSize(void)
{
	return ldpc_frame_size[M_GET_ECC_MODE()];
}

U8 RetryTurboRainInit(void)
{
	U8 ubch, ublun, ubIdx, ubBank;
	U8 ubalu = 0;

	/*
	 * should given outside :
	 *
	 * pre-function : Retry_Create_SB_Task()
	 * post-function : Retry_Clear_SB_Task()
	 *
	 */

	///copy delta table :




	//Micron SB Todo
	//FSA translation
	ubalu = gTurboRainTask.MTTemplate.dma.ALUSelect;
	ubch = (gTurboRainTask.MTTemplate.dma.uliFSA0_1 >> gpOtherInfo->pRuleSet->pChannel->ubShift[ubalu]) & gpOtherInfo->pRuleSet->pChannel->ulMask;
	ublun = ((gTurboRainTask.MTTemplate.dma.uliFSA0_1 >> gpOtherInfo->pRuleSet->pLun->ubShift[ubalu]) & gpOtherInfo->pRuleSet->pLun->ulMask) << gpOtherInfo->pRuleSet->pDie_IL->ubBit_No;
	ublun |= (gTurboRainTask.MTTemplate.dma.uliFSA0_1 >> gpOtherInfo->pRuleSet->pDie_IL->ubShift[ubalu]) & gpOtherInfo->pRuleSet->pDie_IL->ulMask;
	ubBank = (gTurboRainTask.MTTemplate.dma.uliFSA0_1 >> gpOtherInfo->pRuleSet->pBank->ubShift[ubalu]) & gpOtherInfo->pRuleSet->pBank->ulMask;

	gTurboRainTask.ubChannel = ubch;
	gTurboRainTask.ublun = ublun;
	gTurboRainTask.ubBank = ubBank;


	switch (gTurboRainTask.ubPageType) {
	case RETRY_SB_MICRON_SLC_PAGE:
	case RETRY_SB_MICRON_MLC_LOWER_PAGE:
	case RETRY_SB_MICRON_QLC_LOWER_PAGE:
		gTurboRainTask.ubDSPEnginePageType = RETRY_SB_DSP_ENGINE_LOWER_PAGE;
		break;
	case RETRY_SB_MICRON_MLC_UPPER_PAGE:
	case RETRY_SB_MICRON_QLC_UPPER_PAGE:
		gTurboRainTask.ubDSPEnginePageType = RETRY_SB_DSP_ENGINE_UPPER_PAGE;
		break;
	case RETRY_SB_MICRON_QLC_EXTRA_PAGE:
		gTurboRainTask.ubDSPEnginePageType = RETRY_SB_DSP_ENGINE_EXTRA_PAGE;
		break;
	case RETRY_SB_MICRON_QLC_TOP_PAGE:
		gTurboRainTask.ubDSPEnginePageType = RETRY_SB_DSP_ENGINE_TOP_PAGE;
		break;
	default:
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
	}


	/// now only use ErrFram, so LDPC frm set 0xFFFF to bypass
	if (RETRY_READ_SB__DIRECTLY_GET_CORRECT_DATA_FROM_ERROR_PAGE) {
		gTurboRainTask.uwLDPCCurrentErrMap = 0;
		for (ubIdx = 0 ; ubIdx < gub4kEntrysPerPlane ; ubIdx++) {
			if (gTurboRainTask.ubErr_frm & (BIT0 << ubIdx)) {
				gTurboRainTask.uwLDPCCurrentErrMap |=  ((BIT0 | BIT1) << (ubIdx << 1) );
			}
		}
	}
	else {
		gTurboRainTask.uwLDPCCurrentErrMap = 0xFFFF;
	}
#if(TRUE == RETRY_MICRON_NICKS)
	gTurboRainTask.ubBlkRefresh = 0;
#endif /*TRUE == RETRY_MICRON_NICKS*/
	M_FIP_CLEAR_LDPC_DSP_EN();

	//RetrySBFlashSetECCParam(&sb_llr_table[RETRY_MICRON_TABLR_PARAMETER_1H2S][0], DEFAULT_LLR_TABLE_LENGTH, LLR_TABLE0_LOAD_BIT, NORMAL_MODE);

	return TRUE;
}


void RetryTurboRainCorrectMTTrigger( U8 dsp2_en, U8 sb_ibuf_ptr, U8 sel_2k, U8 dsp_en, U8 dsp_4k_en, U8 ubdecode_mode, U8 scale_mode, U8 flow_mode, U8 hb_sb, U8 ubMTIndex)
{
	U16 *fpu_ptr = NULL;
	FlhMT_t *mtp = NULL;
	MTCfg_t uoMTCfg;
	U16 uwFpu_offset;

	// hb_sb = 1 => SB correct
	// hb_sb = 0 => HB correct

	// set flow mode
	R32_FCON[R32_FCON_LDPC_CFG] &= ~(FLOW_MODE_MASK << FLOW_MODE_SHIFT);
	R32_FCON[R32_FCON_LDPC_CFG] |= (flow_mode << FLOW_MODE_SHIFT);

	///+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

	mtp = (FlhMT_t *)M_MT_ADDR(ubMTIndex);

	/// copy mtp tempelete here
	uwFpu_offset = FPU_PTR_OFFSET(fpu_sbc);
	fpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);
	fpu_ptr[0] = FPU_SBC((dsp_4k_en << 9) | (sel_2k << 8) | (dsp_en << 6) | (dsp2_en << 3) | (sb_ibuf_ptr << 0)) | (hb_sb << 7);

	memcpy((void *) & (gTurboRainTask.MT), (void *) & (gTurboRainTask.MTTemplate), sizeof(FlhMT_t));

	/// given ce_value, flh_type, io_type
	gTurboRainTask.MT.dma.uwFPUPtr  = uwFpu_offset;

	gTurboRainTask.MT.dma.btLDPCCorrectEn = 1;
	gTurboRainTask.MT.dma.btInterruptVectorEn = 1;
	gTurboRainTask.MT.dma.btScaleMode = scale_mode;
	gTurboRainTask.MT.dma.DecodeMode = ubdecode_mode;
	gTurboRainTask.MT.dma.btBCHBps = 1;
	gTurboRainTask.MT.cmd.btMTPFormat = 1;

	gTurboRainTask.MT.dma.btiFSAEn = 1;
	gTurboRainTask.MT.dma.ADGSelectPointer = 0;

	gTurboRainTask.MT.cmd.uliFSA0_1    = (U32)( (gTurboRainTask.ulFSA_Align) + (gTurboRainTask.ubCurr_frm_idx) );
#if (PS5017_EN)
	gTurboRainTask.MT.dma.btFpuPCAEn = 1;
#endif /* (S17_EN) */
	///// do global trigger
	//set MTQ_ID
	gTurboRainTask.MT.cmd.btCESelectMode = 1;
	gTurboRainTask.MT.cmd.ubCEValue = gTurboRainTask.MTTemplate.cmd.ubCEValue;
	gTurboRainTask.MT.cmd.NormalTargetCPU = MT_TARGET_CPU0;
	gTurboRainTask.MT.cmd.ErrorTargetCPU = MT_TARGET_CPU0;

	memcpy((void *)mtp, (void *) & (gTurboRainTask.MT), sizeof(FlhMT_t));

	// Global Trigger MT
	uoMTCfg.uoAll = 0;
	uoMTCfg.bits_recc.ubQUE_IDX = gTurboRainTask.MTTemplate.cmd.ubCEValue;
	uoMTCfg.bits_recc.ubMT_IDX = ubMTIndex;
	uoMTCfg.bits_recc.btQOS = gTurboRainTask.ubUseQorOrNot;
	uoMTCfg.u32.ulMT_CFG1 = 0;
	FlaGlobalTrigger(&uoMTCfg);

}


void RetryTurboRainBackupCorrect2k(U8 src_ibf, U8 ldpc_frame_idx, U32 ulbackup_addr, U32 ultmp_addr, U32 Iram_addr, U32 TempIram_addr, U8 ubMode)
{

	DMACParam_t DMACParam;

	// setup ldpc frame size
	M_FIP_CLEAR_DRAM_MULTI(gTurboRainTask.ubChannel);
	// Clean BR_W_LEN then set backup_restore length
	M_FIP_SET_BACK_RESTORE_LENGTH(gTurboRainTask.ubChannel, ((((RetryTurboRainFlashGetCurrentLDPCFrameSize() * 2) + 16) / 16) - 1));
	// backup data from channel ibf to DBUF_BASE
	if (RETRY_TURBORAIN_BACKUP_RESTORE_TO_DRAMIRAM == ubMode) {
		//Retry_SB_backup_restore(ultmp_addr, TempIram_addr, src_ibf, 0, ubMode, 0, 0, 0);
		RetryTurboRainBackupRestore(ultmp_addr, TempIram_addr, src_ibf, 0, 1, 0, 0, 0);
	}

	/* For PS5011, data size in ibf format is following :

	      byte 15    byte 0
	      ------------------
	     |     2K data 0   |
	      -----------------
	     |     2K data 1   |
	      -----------------
	     | spr 1  |  spr 0 |
	      -----------------
	     |    x   |  CRC 0 |
	      -----------------
	     |      LDPC 0     |
	      -----------------
	     |    x   |  CRC 1 |
	      -----------------
	     |      LDPC 1     |
	      -----------------    */

	if (ubMode == RETRY_TURBORAIN_BACKUP_RESTORE_TO_DRAMIRAM) {
		if (ldpc_frame_idx == 0) {
			// first 2k
			/* copy first 2k data */
			DMACParam.ulSourceAddr = ultmp_addr;
			DMACParam.ulDestAddr = ulbackup_addr;
			DMACParam.ul32ByteNum = SIZE_IN_32B(BC_2KB);
			gpRetry->ubWaitDMACDoneFlag = TRUE;

			DMACCopy(DMAC_MODE_NORMAL_QUEUE, &DMACParam, gulReadRetryDMACDone_CallBack, 0);
			while (gpRetry->ubWaitDMACDoneFlag == TRUE) {
				DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
			}

			/* copy spr 0 */
			memcpy((U32 *)(Iram_addr), (U32 *)(TempIram_addr), BC_8B );

		}
		else {      // second 2k

			//        LOG_PRINTF("[Backup] 2nd correctable 2k to DBUF addr 0x%x\n", ulbackup_addr);
			/* copy second 2k data */
			///memcpy((U32 *)(ulbackup_addr + BC_2KB), (U32 *)(ultmp_addr + BC_2KB), BC_2KB);
			DMACParam.ulSourceAddr = (ultmp_addr + BC_2KB);
			DMACParam.ulDestAddr = (ulbackup_addr + BC_2KB);
			DMACParam.ul32ByteNum = SIZE_IN_32B(BC_2KB);
			gpRetry->ubWaitDMACDoneFlag = TRUE;

			DMACCopy(DMAC_MODE_NORMAL_QUEUE, &DMACParam, gulReadRetryDMACDone_CallBack, 0);
			while (gpRetry->ubWaitDMACDoneFlag == TRUE) {
				DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
			}
			/* copy spr 1 */
			memcpy((U32 *)(Iram_addr + BC_8B ), (U32 *)(TempIram_addr + BC_8B), BC_8B);
		}
	}

}


U8 RetryTurboRainPrepareFreeMT(MT_Callback_Func_t PassCallback, MT_Callback_Func_t FailCallback)
{
	U8 ubMTIndex = FlaGetFreeMTIndex();
	gpRetry->TrigMTList.MTCb[gpRetry->TrigMTList.ubTail].ubMTIndex = ubMTIndex;
	gpRetry->TrigMTList.MTCb[gpRetry->TrigMTList.ubTail].PassCallback = PassCallback;
	gpRetry->TrigMTList.MTCb[gpRetry->TrigMTList.ubTail].FailCallback = FailCallback;
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, gpRetry->TrigMTList.ubTrigCnt != RETRY_MAX_TRIG_MT_NUM);
	gpRetry->TrigMTList.ubTrigCnt++;
	gpRetry->TrigMTList.ubTail = (gpRetry->TrigMTList.ubTail + 1) & RETRY_MAX_TRIG_MT_NUM_MASK;
	return ubMTIndex;
}

void RetryTurboRainTriggerCorrectFail_Callback(U8 ubMTIndex)
{
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_TURBORAIN_RETRY;
	gTurboRainTask.ubState = RETRY_TURBORAIN_TRIGGER_LDPC_DECODE;
}

void RetryTurboRainTriggerCorrectPass_Callback(U8 ubMTIndex)
{
	U16 uwFailCnt;
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_TURBORAIN_RETRY;
	while ((R32_FCON[R32_FCON_LDPC_SBC] & ((SBC_BSY_MASK) << SBC_BSY_SHIFT))  );
	uwFailCnt = ((R32_FCON[R32_FCON_LDPC_SBC] & SBC_BFC_CNT_MASK) >> SBC_BFC_CNT_SHIFT);
	if (R32_FCON[R32_FCON_LDPC_SBC] & SBC_SUCCEED_BIT) {
		R32_FCON[R32_FCON_LDPC_SBC] = 0;

		// backup correctable 2k
		RetryTurboRainBackupCorrect2k(3, gTurboRainTask.ubCurrentLDPCFrameIdx,            \
			gTurboRainTask.ulbackup_addr, gTurboRainTask.ultmp_addr, 				\
			gTurboRainTask.ulSpareAddr, gTurboRainTask.ultmp_SpareAddr, RETRY_TURBORAIN_BACKUP_RESTORE_TO_DRAMIRAM);

		gTurboRainTask.ubState = RETRY_TURBORAIN_CHECK_PASS;
	}
	else {
		gTurboRainTask.ubState = RETRY_TURBORAIN_CHECK_FAIL;
	}

}

void RetryTurboRainDecodeFlow(void)
{
	U8 ubMTIndex;
	ubMTIndex = RetryTurboRainPrepareFreeMT(RetryTurboRainTriggerCorrectPass_Callback, RetryTurboRainTriggerCorrectFail_Callback);
	RetryTurboRainCorrectMTTrigger( 0, 0, gTurboRainTask.ubCurrentLDPCFrameIdx, 0, 0, gTurboRainTask.ubdecode_mode, 1, 0, 1, ubMTIndex);
	if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
		gpRetry->ubRetryState = FLH_RETRY__WAITING_TURBORAIN_MT_BUSY;
	}
	else {
		gpRetry->ubRetryState = FLH_RETRY__HANDLING_TURBORAIN_RETRY;
		gTurboRainTask.ubState = RETRY_TURBORAIN_STATE_RECEIVE_CQ;
	}
}

void RetryTurboRainReleaseFinishMT(void)
{
	FlaAddFreeMTIndex(gpRetry->TrigMTList.MTCb[gpRetry->TrigMTList.ubHead].ubMTIndex);
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, gpRetry->TrigMTList.ubTrigCnt);
	gpRetry->TrigMTList.ubTrigCnt--;
	gpRetry->TrigMTList.ubHead = (gpRetry->TrigMTList.ubHead + 1) & RETRY_MAX_TRIG_MT_NUM_MASK;
}

void RetryTurboRainFlashRetoreLLR(U8 lmu_sel, U8 llr_idx)
{
	U32 idx;
	if (llr_idx > LLR_TBL_CNT) {
		return;
	}
	R32_FCON[R32_FCON_LDPC_CFG] &=  ~( (PAGE_SELECT_MASK) << PAGE_SELECT_SHIFT );

	switch (lmu_sel) {
	case 0:
		R32_FCON[R32_FCON_LDPC_CFG] |=  (( 0 & PAGE_SELECT_MASK) << PAGE_SELECT_SHIFT);
		break;
	case 1:
		R32_FCON[R32_FCON_LDPC_CFG] |=  (( 1 & PAGE_SELECT_MASK) << PAGE_SELECT_SHIFT);
		break;
	case 2:
		R32_FCON[R32_FCON_LDPC_CFG] |=  (( 2 & PAGE_SELECT_MASK) << PAGE_SELECT_SHIFT);
		break;
	case 3:
		R32_FCON[R32_FCON_LDPC_CFG] |=  (( 3 & PAGE_SELECT_MASK) << PAGE_SELECT_SHIFT);
		break;
	}

	for (idx = 0; idx < 10; idx++) {

		/* Select LLR table */
		R32_FCON[R32_FCON_ECC_PARAM_CFG] &= (~(ECC_PARAM_SEL_MASK << ECC_PARAM_SEL_SHIFT ));
		R32_FCON[R32_FCON_ECC_PARAM_CFG] |= (idx << ECC_PARAM_SEL_SHIFT);

		/* Fill LLR data */
		R32_FCON[R32_FCON_ECC_PARAM] = sb_llr_table[llr_idx][idx];

		//SB MICRON Check Point 0730
		M_UART(ERROR_SOFTBIT_, "\n Def_LLR Idx %d ,%x", llr_idx, sb_llr_table[llr_idx][idx]);

	}

	RetryTurboRainFlashLoadECCParam(LLR_TABLE0_LOAD_BIT);
}

void RetryTurboRainBackupRestore( U64 dram_addr, U32 iram_addr, U8 ibf_ptr, U8 direction, U8 mode, U8 bch_mode, U8 all, U8 frm_msk)
{
	/*
	    direction --> 1: restore frame to IBF
	                  0: backup frame to DRAM/IRAM
	         mode --> 1: transfer DATA/Spare to/from DRAM/IRAM
	                  0: transfer ECC frame to/from DRAM
	*/
	// setup ldpc frame size
	M_FIP_CLEAR_DRAM_MULTI(gTurboRainTask.ubChannel);
	// Clean BR_W_LEN then set backup_restore length
	M_FIP_SET_BACK_RESTORE_LENGTH(gTurboRainTask.ubChannel, ((((RetryTurboRainFlashGetCurrentLDPCFrameSize() * 2) + 16) / 16) - 1));
	U16 *fpu_ptr = NULL;
	U16 uwFpu_offset;
	//Wait FIP_CH_FPU_TRIGGER to 0x04, only in SB (other Queue empty)
	while (SRQ_SVL_BIT != (M_FIP_GET_FPU_TRIG(gTurboRainTask.ubChannel) & (MT_BSY_BIT | ANY_BSY_BIT | SRQ_SVL_BIT | SIGN_OFF_BUSY_BIT | FPU_TRIGGER_BIT)));
#if (!PS5017_EN)
	M_FIP_SET_RAW_DMA_ADR(gTurboRainTask.ubChannel, dram_addr);
	M_FIP_SET_RAW_DMA_IRAM_ADR(gTurboRainTask.ubChannel, iram_addr);
#endif /* (!PS5017_EN) */
	/// fpu_ptr = (U16*) (IRAM_BASE + FPU_ENTRY_DUMP_IBUF_TEST);
#if (PS5017_EN)
	U32 iram_off;  //E19
	iram_off = (iram_addr - IRAM_BASE);
	R32_FCTL_CH[gTurboRainTask.ubChannel][R32_FCTL_IFSA0] = dram_addr;
	R32_FCTL_CH[gTurboRainTask.ubChannel][R32_FCTL_IFSA_HIGH] = 0;
	R8_FCTL_CH[gTurboRainTask.ubChannel][R8_FCTL_IN_FSA0] = iram_off & IN_FSA0_MASK;
	R8_FCTL_CH[gTurboRainTask.ubChannel][R8_FCTL_IN_FSA1] = (iram_off >> 8) & IN_FSA1_MASK;
#endif /*(PS5017_EN)*/

	uwFpu_offset = FPU_PTR_OFFSET(fpu_backup_restore_ibuf);
	fpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);
	*fpu_ptr = FPU_BR((frm_msk << 8) | (all << 6) | (bch_mode << 5) | (ibf_ptr << 2) | (direction << 1) | mode);
	fpu_ptr++;

	*fpu_ptr = FPU_END;
	__asm("DSB");

	RetryTurboRainFPUTrigger( uwFpu_offset, FALSE, 0xFFFF);
	while (SRQ_SVL_BIT != (M_FIP_GET_FPU_TRIG(gTurboRainTask.ubChannel) & (MT_BSY_BIT | ANY_BSY_BIT | SRQ_SVL_BIT | SIGN_OFF_BUSY_BIT | FPU_TRIGGER_BIT)));
}




void RetryTurboRainMain()
{
	U8 ubMTIndex;
	FlhIntInfo8Bit_t ubFlhMsg;
	MT_Callback_Func_t callback;

	do {
		switch (gTurboRainTask.ubState) {
		case RETRY_TURBORAIN_START:
			/// init variable
			RetryTurboRainInit();
			gTurboRainTask.ubState = RETRY_TURBORAIN_STATE_SCAN_ERR_FRAME;
			break;

		case RETRY_TURBORAIN_STATE_SCAN_ERR_FRAME:
			gTurboRainTask.ubCurrentLDPCFrameIdx = 0;
			for ( ; gTurboRainTask.ubCurr_frm_idx < gub4kEntrysPerPlane; gTurboRainTask.ubCurr_frm_idx++) {
				if ( (BIT0 << gTurboRainTask.ubCurr_frm_idx) & gTurboRainTask.ubErr_frm ) {
					/// refresh addr
					gTurboRainTask.ulbackup_addr = gTurboRainTask.ulbackup_addr_base[gTurboRainTask.ubCurr_frm_idx];
					gTurboRainTask.ulSpareAddr = gTurboRainTask.ulSpareAddr_base [gTurboRainTask.ubCurr_frm_idx];

					/// change state
					gTurboRainTask.ubState = RETRY_TURBORAIN_STATE_CHECK_ERR_LDPC_FRM;
					break;
				}
			}
			if ( gTurboRainTask.ubCurr_frm_idx == gub4kEntrysPerPlane ) {	//All error frame been processed, leave Turbo Rain flow
				gTurboRainTask.ubState = RETRY_TURBORAIN_STATE_DONE;
			}
			break;

		case RETRY_TURBORAIN_STATE_CHECK_ERR_LDPC_FRM:
			if ( gTurboRainTask.ubCurrentLDPCFrameIdx == LDPC_FRAME_MAX_CNT) {
				/// this physical frame is done, change state
				if ( (gTurboRainTask.uwLDPCCurrentErrMap & (     (BIT0 | BIT1) << ((gTurboRainTask.ubCurr_frm_idx) << 1) )  ) == 0) {
					gTurboRainTask.ubErr_frm &= ~(BIT0 << (gTurboRainTask.ubCurr_frm_idx));
				}
				gTurboRainTask.ubCurr_frm_idx++;
				gTurboRainTask.ubState = RETRY_TURBORAIN_STATE_SCAN_ERR_FRAME;
				break;
			}
			gTurboRainTask.ubState = RETRY_TURBORAIN_GENERATE_XORALL_AND_STAGE6_DATA;
			break;

		case RETRY_TURBORAIN_GENERATE_XORALL_AND_STAGE6_DATA:
			gTurboRainTask.ubState = RETRY_TURBORAIN_HANDLING_SB_RETRY;
			gpRetry->ubRetryState = FLH_RETRY__CREATE_SB_RETRY_TASK_BETWEEN_TURBORAIN;
			gTurboRainTask.ubRSDecodeFlowLoopBreakFlag = 1;
			break;

		case RETRY_TURBORAIN_INPUT_SB0_TO_SB4:
			RetryTurboRainBackupRestore(gTurboRainTask.ultmp_addr, gTurboRainTask.ultmp_SpareAddr, 0, 1, 0, 0, 0, 0); // Restore into ibuf0
			RetryTurboRainBackupRestore(gTurboRainTask.ultmp_addr + BC_4KB + BC_1KB, gTurboRainTask.ultmp_SpareAddr, 1, 1, 0, 0, 0, 0); // Restore into ibuf1

			memset((void *)gTurboRainTask.ultmp_addr, 0, BC_4KB + BC_16B); // data + spare
			memset((void *)gTurboRainTask.ultmp_addr + BC_4KB + BC_16B, 0xFF, BC_1KB - BC_16B); // CRC + PTY
			RetryTurboRainBackupRestore(gTurboRainTask.ultmp_addr, gTurboRainTask.ultmp_SpareAddr, 2, 1, 0, 0, 0, 0); // Restore into SB4
#if (RETRY_TURBORAIN_DEBUG)
			/*memset((void *)gTurboRainTask.ultmp_addr, 1, BC_4KB);
			memset((void *)gTurboRainTask.ultmp_SpareAddr, 1, BC_16B); // spare
			UartPrintf("\nOri[SB4]Data : %L", *(U64 *)(gTurboRainTask.ultmp_addr));
			UartPrintf("\nOri[SB4] : %L", *(U64 *)( gTurboRainTask.ultmp_SpareAddr));

			RetryTurboRainBackupRestore(gTurboRainTask.ultmp_addr, gTurboRainTask.ultmp_SpareAddr, 2, 0, 1, 0, 0, 0); // Backup ibuf2
			UartPrintf("\nTurboRain[SB4]Data : %L", *(U64 *)(gTurboRainTask.ultmp_addr));
			UartPrintf("\nTurboRain[SB4] : %L", *(U64 *)( gTurboRainTask.ultmp_SpareAddr));*/
#endif
			gTurboRainTask.ubState = RETRY_TURBORAIN_STATE_LOAD_TURBORAIN_LLR_TABLE;
			break;

		case RETRY_TURBORAIN_STATE_LOAD_TURBORAIN_LLR_TABLE:
			RetryTurboRainFlashRetoreLLR(gTurboRainTask.ubDSPEnginePageType, RETRY_MICRON_TABLR_PARAMETER_TURBORAIN); // set default llr table
			gTurboRainTask.ubState = RETRY_TURBORAIN_TRIGGER_LDPC_DECODE;
			break;

		case RETRY_TURBORAIN_TRIGGER_LDPC_DECODE:
			RetryTurboRainDecodeFlow();
			break;

		case RETRY_TURBORAIN_CHECK_PASS:
			gTurboRainTask.uwLDPCCurrentErrMap &= ~(     (BIT(gTurboRainTask.ubCurrentLDPCFrameIdx)) << ((gTurboRainTask.ubCurr_frm_idx) << 1)       );
			gTurboRainTask.ubCurrentLDPCFrameIdx++;
			gTurboRainTask.ubState = RETRY_TURBORAIN_STATE_CHECK_ERR_LDPC_FRM;
#if (RETRY_TURBORAIN_DEBUG)
			UartPrintf("\nRETRY_TURBORAIN_CHECK_PASS");
#endif
			break;

		case RETRY_TURBORAIN_CHECK_FAIL:
#if (RETRY_TURBORAIN_DEBUG)
			UartPrintf("\nRETRY_TURBORAIN_CHECK_FAIL");
#endif
			gTurboRainTask.ubCurrentLDPCFrameIdx++;
			gTurboRainTask.ubState = RETRY_TURBORAIN_STATE_CHECK_ERR_LDPC_FRM;
			break;

		case RETRY_TURBORAIN_HANDLING_SB_RETRY:
			break;

		case RETRY_TURBORAIN_STATE_RECEIVE_CQ:
			if (FALSE == FIPIsStopAllocateEventSet(FIP_STOP_ALLOCATE_EVENT_TURBORAIN_WAIT_MT_DONE)) {
				FIPSetStopAllocateBuf(FIP_STOP_ALLOCATE_EVENT_TURBORAIN_WAIT_MT_DONE);
			}
			FIPDelegateCmd();
			while (gpRetry->TrigMTList.ubTrigCnt) {
				ubMTIndex = gpRetry->TrigMTList.MTCb[gpRetry->TrigMTList.ubHead].ubMTIndex;

				ubFlhMsg = gMTMgr.ubMTDoneMsg[ubMTIndex - MT_RETRY_START_INDEX];

				if (ubFlhMsg.ubAll == 0) {
					break; // MT not finish, break
				}
				gpRetry->RttTimer.uoStartRTT = 0;
				gpRetry->RttTimer.uoEndRTT   = 0;

				if ((ubFlhMsg.btMTStop) || (ubFlhMsg.btErasePage)) {
					//SB MICRON Check Point 0724
					M_UART(ERROR_SOFTBIT_, "\n FailCallBack!!");
					callback = (MT_Callback_Func_t)gpRetry->TrigMTList.MTCb[gpRetry->TrigMTList.ubHead].FailCallback;
				}
				else {
					callback = (MT_Callback_Func_t)gpRetry->TrigMTList.MTCb[gpRetry->TrigMTList.ubHead].PassCallback;
				}
				if (callback != NULL) {
					callback(ubMTIndex);
				}
				RetryTurboRainReleaseFinishMT();
				if (0 == gpRetry->TrigMTList.ubTrigCnt) {
					FIPClearStopAllocateBuf(FIP_STOP_ALLOCATE_EVENT_TURBORAIN_WAIT_MT_DONE);
				}
			}
			break;

		case RETRY_TURBORAIN_STATE_DONE:
			gTurboRainTask.ubRSDecodeFlowLoopBreakFlag = TRUE;
			break;

		default:
			break;
		}
		if ((CALLBACK_FUNC_IN_COMMON_CODEBANK == FALSE) && (gTurboRainTask.ubRSDecodeFlowLoopBreakFlag)) {
			break;
		}
	} while ((CALLBACK_FUNC_IN_COMMON_CODEBANK == FALSE));

}


#endif /*(RETRY_TURBO_RAIN_EN)*/
