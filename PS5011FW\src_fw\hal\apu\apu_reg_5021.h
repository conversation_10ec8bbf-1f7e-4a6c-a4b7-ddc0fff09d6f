#if (PS5021_EN)

#include "mem.h"
#include "setup.h"
#include "typedef.h"

// Only for E21

//--------------------APU wrapper IP Register Define--------------------
#define	APU_REG_BASE										(APU_REG_ADDRESS)

#define	R8_APU												((volatile U8 *) APU_REG_BASE)
#define	R16_APU												((volatile U16 *) APU_REG_BASE)
#define	R32_APU												((volatile U32 *) APU_REG_BASE)
#define	R64_APU												((volatile U64 *) APU_REG_BASE)

/* not use
#define	RD_CACHE_SHB_SIZE									(0x30)
#define	RD_CACHE_SHW_SIZE									(0x18)
#define	RD_CACHE_SHL_SIZE									(0x0C)

#define	WR_CACHE_SHB_SIZE									(0x10)
#define	WR_CACHE_SHB_BIT									(4)
#define	WR_CACHE_SHW_BIT									(3)
#define	WR_CACHE_SHL_BIT									(2)
*/

#define	APU_WR_CACHE_NUM								(7) //E21 should be 7
#define	APU_READ_CACHE_NUM								(3)

/* not use
#define	RD_CACHE_FREE_SHB_SIZE								(0x10)
#define	RD_CACHE_FREE_SHB_BIT								(4)
#define	RD_CACHE_FREE_SHW_BIT								(3)
#define	RD_CACHE_FREE_SHL_BIT								(2)
#define	CFG_RESULT_LPCRC_RESULT								BIT8
//#define LPCRC_CRC_PCAE2E_CFG								BITMSK(2,0)
#define	CFG_RESULT_PCACRC_RESULT							BITMSK(4,16)
//#define LPCRC_WR_LCA(N)										(R32_CPU[0>>2] = N)
//#define LPCRC_WR_PCA(N)										(R32_CPU[0x4>>2] = N)
//#define LPCRC_WR_PCAE2E_CFG(N)							(r32_CPU[0x8>>2] |= ( N & LPCRC_CRC_PCAE2E_CFG )) // removed
//#define PCACRC_RD_PCACRC_RESULT()							((R32_CPU[0x8>>2] & CFG_RESULT_PCACRC_RESULT) >> 16)
//#define LPCRC_RD_LPCRC_RESULT()								((R32_CPU[0x8>>2] & CFG_RESULT_LPCRC_RESULT) >> 8)
//#define LPCRC_RD_PCAE2E_CFG()								(r32_CPU[0x8>>2] & LPCRC_CRC_PCAE2E_CFG) // removed
//#define PCACRC_WR_PCA(N)									(R32_CPU[0x20>>2] = N)
//#define LPCRC_RD_PCA_New()
#define   CMD_FIFO_LENGTH       (0x80)
*/

/*================================= System Control Register ================================*/

#define	R32_APU_INT_FLAG									(0x00 >> 2)
#define		APU_INT_ALL  									(0x1FFFFF) // 20 bit
#define		APU_NL_ERR_INT_BIT								(BIT0)//- Indicate the NVMe Error interrupt, write 1 to clear Interrupt.
#define		APU_NL_EVENT_INT_BIT							(BIT1)//- Indicate the NVMe Normal event interrupt, write 1 to clear Interrupt.
#define		APU_CMGR_INT_BIT								(BIT2)//- Indicate the CMGR interrupt, write 1 to clear Interrupt.
#define		APU_DEL_FAIL_INT_BIT							(BIT3)//- Indicate the Dependency Checker delete is fail, write 1 to clear Interrupt.
#define		APU_RD_RRC_PAR_ERR_INT_BIT						(BIT4)//- Indicate the RRC read SRAM occcur parity error, write 1 to clear Interrupt.
#define		APU_CMD_FIFO_INFO_SRAM_ERR_INT_BIT				(BIT5)//- Indicate the CMD_FIFO SRAM parity error occur when SRAM pop, write 1 to clear Interrupt.
#define		APU_WR_CACHE_FLUSH_ERR_INT_BIT					(BIT6)//- Indicate the RRC_PAR_ERR/HOST_WB_PAR_ERR/AXI_BRESP_ERR occur during cache flush, write 1 to clear Interrupt.
//#define		APU_SUB_RD_CMD_ERR_INT_BIT						(BIT7)//- Indicate the sub-read command error, write 1 to clear Interrupt & sub-read lock state. (E21 remove this interrupt)
#define		APU_RD_CMD_PB_NOT_FULL_INT_BIT					(BIT8)//- Indicate the miss-read get_pbna return not full, write 1 to clear Interrupt.
#define		APU_WR_BLK_FUA_LOCK_INT_BIT						(BIT9)//- Indicate the write command is locked due to FUA bit, write 1 to clear Interrupt(and auto clear write FUA lock).
#define		APU_CMD_FIFO_IDX_SRAM_ERR_INT_BIT				(BIT10)//- Indicate the CMD_FIFO index SRAM parity error occur when SRAM pop, write 1 to clear Interrupt.
#define		APU_SATA_INT_BIT								(BIT11)//- Indicate the SATA Interrupt, write 1 to clear Interrupt.
#define		APU_UFS_INT_BIT									(BIT12)//- Indicate the UFS Interrupt, write 1 to clear Interrupt.
#define		APU_DEP_SRAM_ERR_INT_BIT						(BIT13)//- Indicate the DEP SRAM parity error Interrupt, write 1 to clear this interrupt.
#define		APU_CMD_CNT_FULL_INT_BIT						(BIT14)//- Indicate the one or more command counters in command log are full and users can write 1 to clear this interrupt.
#define		APU_RD_MGR_PCA_SRAM_PAR_ERR_BIT					(BIT15)//- Indicate the rd_mgr lp crc error when search cop1 done.
#define		APU_RD_MGR_LP_CRC_ERR_RCQ_BIT					(BIT16)//- Indicate the lp crc err when search cop1 done.
#define		APU_RD_MGR_LP_CRC_ERR_FW_BIT					(BIT17)//- Indicate the lp crc err when FW insert.
#define		APU_RD_MGR_RD_ERR_KEEP_BUF_ASR_BIT				(BIT18)//- Indicate he RD DMA is locked, due to error occurs and rd_err_keep_buff_en is enabled.
#define		APU_CMD_ABORT_INT_BIT							(BIT19)//- Indicate there are cmds which is aborted successfully.
#define     APU_WR_MGR_AUTO_LOCK_BIT                        (BIT20)//- Indicate wr dma is locked due to all buffers are consumed.
//E13 doesn't have the following interrupt bit.
/* not use
#define     APU_CTM_INT_BIT                                 (BIT21)//- Indicate RW command timeout occurs.(Command's CTM_TIMER exceed CTM_THR)
#define     APU_CTM_PERR_BIT                                (BIT22)//- Indicate Command Timer SRAM parity error.
#define     APU_LR_PERR_BIT                                 (BIT23)//- Indicate LCA Remap related SRAM parity error occurs. When this bit is high, HW will lock LCA Remap SRAM except FW path.
#define     APU_SD_INT_BIT                                  (BIT24)//- Indicate the SD interrupt occurs. (RW1C)
#define     AOU_TSA_AXI_RRESP_ERR_INT_BIT                   (BIT25)//- Indicate the TSA function AXI read response interrupt.
#define     APU_LCTM_INT_BIT                                (BIT26)//- Indicate last four RW command timeout occurs.
#define     APU_TSA_AXI_BRESP_ERR_INT_BIT                   (BIT27)//- Indicate the TSA function AXI write response interrupt.
*/

#define	R32_APU_INT_EN										(0x04 >> 2)
#define		APU_NVME_ERR_INT_EN_BIT							(BIT0)//- The bit is set to enable the NL Error interrupt.
#define		APU_NVME_EVENT_INT_EN_BIT						(BIT1)//- The bit is set to enable the NL Normal event interrupt.
#define		APU_CMGR_INT_EN_BIT								(BIT2)//- The bit is set to enable the CMGR interrupt.
#define		APU_DEL_FAIL_INT_EN_BIT							(BIT3)//- The bit is set to enable the Dependency Checker delete fail Interrupt.
#define		APU_RD_RRC_PAR_ERR_INT_EN_BIT					(BIT4)//- The bit is set to enable the RRC read SRAM occurs parity err interrupt.
#define		APU_CMD_FIFO_SRAM_ERR_INT_EN_BIT				(BIT5)//- The bit is set to enable the CMD_FIFO info SRAM error interrupt.
#define		APU_WR_CACHE_FLUSH_ERR_INT_EN_BIT				(BIT6)//- Indicate HW error occurs during write cache flush error interrupt.
#define		APU_SUB_RD_CMD_ERR_INT_EN_BIT					(BIT7)//- The bit is set to enable the sub-read command error interrupt.
#define		APU_RD_CMD_PB_NOT_FULL_INT_EN_BIT				(BIT8)//- The bit is set to enable the miss-read PBNA not full interrupt.
#define		APU_WR_BLK_FUA_LOCK_INT_EN_BIT					(BIT9)//- The bit is set to enable the write block FUA lock interrupt.
#define		APU_CMD_FIFO_IDX_SRAM_ERR_INT_EN_BIT			(BIT10)//- The bit is set to enable the CMD_FIFO index SRAM error interrupt.
#define		APU_SATA_INT_EN_BIT								(BIT11)//- The bit is set to enable the SATA Interrupt Enable.
#define		APU_UFS_INT_EN_BIT								(BIT12)//- The bit is set to enable the UFS Interrupt.
#define		APU_DEP_SRAM_ERR_INT_EN_BIT						(BIT13)//- The bit is set to enable the DEP SRAM parity error.
#define		APU_CMD_CNT_FULL_INT_EN_BIT						(BIT14)//- The bit is set to enable the command log counter full interrupt.
#define		APU_RD_MGR_PCA_SRAM_PAR_ERR_EN_BIT				(BIT15)//- The bit is set to enable the rd_mgr PCA SRAM Parity Error Interrupt.
#define		APU_RD_MGR_LP_CRC_ERR_RCQ_EN_BIT				(BIT16)//- The bit is set to enable the rd_mgr LP CRC RCQ Error interrupt.
#define		APU_RD_MGR_LP_CRC_ERR_FW_EN_BIT					(BIT17)//- The bit is set to enable the rd_mgr LP CRC Error FW Interrupt.
#define		APU_RD_MGR_RD_ERR_KEEP_BUF_ASR_EN_BIT			(BIT18)//- The bit is set to enable the rd_mgr keep buffer Interrupt.
#define		APU_CMD_ABORT_INT_EN_BIT						(BIT19)//- The bit is set to enable the cmd abort Interrupt.

/* not use
#define     APU_WR_MGR_AUTO_LOCK_EN_BIT                     (BIT20)//- The bit is set to enable the wr_mgr auto lock interrupt.
#define     APU_CTM_INT_EN_BIT                              (BIT21)//- The bit is set to enable the CTM interrupt.
#define     APU_CTM_PERR_EN_BIT                             (BIT22)//- The bit is set to enable the CTM perr interrupt.
#define     APU_LR_PERR_EN_BIT                              (BIT23)//- The bit is set to enable the LR perr interrupt.
#define     APU_SD_INT_EN_BIT                               (BIT24)//- The bit is set to enable the SD interrupt.
#define     APU_TSA_AXI_RRESP_ERR_INT_EN                    (BIT25)//- The bit is set to enable the TSA axi rresp err interrupt.
#define     APU_LCTM_INT_EN                                 (BIT26)//- The bit is set to enable the LCTM interrupt.
#define     APU_TSA_AXI_BRESP_ERR_INT_EN                    (BIT27)//- The bit is set to enable the TSA axi bresp err interrupt.
*/
#define	R32_APU_TMR_MS_CNT_MAX								(0x08 >> 2)
#define		APU_TMR_MS_CNT_MAX_SHIFT						(0)
#define		APU_TMR_MS_CNT_MAX_MASK							(0xFFFFFFFF)


#define	R8_APU_SYS_CTRL										(0x0C)
#define		APU_WRAP_FW_RST_BIT								(BIT0)//- The bit is used to force the APU wrapper FW to reset.
#define		APU_SRAM_LS_IP_CTRL_EN_BIT						(BIT1)//- SRAM light sleep IP control enable. When enabled, SRAM will enter the light sleep mode to reduce leakage when the SRAM is idle.
#define		APU_SRAM_CLKGAT_EN_BIT							(BIT2)//- This bit is used to enable SRAM Clock Gating Funciton.

/* not use
#define	R8_APU_ERR_INSERT									(0x0D)
#define		APU_CMD_FIFO_INFO_SRAM_ERR_INSERT_BIT			(BIT0)//- Insert parity error to CMD_FIFO info sram when assert, FW clear this bit after error handle done.
#define		APU_CMD_FIFO_IDX_SRAM_ERR_INSERT_BIT			(BIT1)//- Insert parity error to CMD_FIFO idx sram when assert, FW clear this bit after error handle done.
*/

#define	R16_APU_LOCK_STATUS									(0x10 >> 1)
#define		APU_CMD_DISP_LOCK_RO_BIT						(BIT0)//- Stop receive command when receive sync command.
#define		APU_CMD_DISP_FW_LOCK_RO_BIT						(BIT1)//- Stop receive command, lock by FW
//#define		APU_CMD_DISP_SUB_RD_LOCK_RO_BIT					(BIT2)//- Stop receive command when sub-read command error.(E21 reomve this bit)
#define		APU_DEL_LOCK_RO_BIT								(BIT3)//- Indicate the deleted request is stopped with error.
#define		APU_F_DB_SYN_LOCK_RO_BIT						(BIT5)//- Indicate the (a)sync command is stopped to be sent to the doorbell.
#define		APU_F_WR_BLK_LOCK_RO_BIT						(BIT7)//- Indicate the write command is stopped to be sent to the write block.
#define		APU_WR_BLK_FUA_LOCK_RO_BIT						(BIT8)//- Indicate the write command is locked due to FUA bit.
#define		APU_USER_RD_BMU_LOCK_RO_BIT						(BIT9)//- Indicate lock RDMGR request user RD buffer from the BMU.
#define		APU_F_DMA_RD_LOCK_RO_BIT						(BIT10)//- Indicate the read command is stopped to be sent to the DMA.
#define		APU_F_DMA_WR_LOCK_RO_BIT						(BIT11)//- Indicate the write command is stopped to be sent to the DMA.


#define	R8_APU_TMR_100NS_CNT_MAX							(0x14)
#define		APU_TMR_100NS_CNT_MAX_SHIFT						(0)
#define		APU_TMR_100NS_CNT_MAX_MASK						(BIT_MASK(8))

#define R16_APU_TMR_US_CNT_MAX                              (0x16 >> 1)
#define     APU_TMR_US_CNT_MAX_SHIFT                        (0)
#define     AOU_TMR_US_CNT_MAX_MASK                         (BIT_MASK(16))

#define	R32_APU_CMD_ABORT_BITMAP							(0x200 >> 2) //E21 extend to 512 bits.
//- This field is an one-hot signal for FW set to Abort the cmd which is not in process, e.g., cmd_abort_bitmap[i] = i means cmd with ctag=I is not in process and is going to be aborted.
//- When FW W1C cmd_aborted_done, the corresponding bit in this field would be clear to 0 as well.

#define	R32_APU_DEP_CLR_BITMAP								(0x240 >> 2) //E21 extend to 512 bits.

/*================================= Command Dispatcher Register ================================*/
#define	R8_APU_CMD_DISP_CTRL								(0x80)
// not use  #define		APU_CMD_DISP_CTRL_LOCK_BIT						(BIT0)//- Stop receive command when receive sync command. <RO>
// not use  #define		APU_CMD_DISP_WR_PROTECT_EN_BIT					(BIT1)//- Change write command attribute to sync command.
#define		APU_CMD_DISP_FW_LOCK_BIT						(BIT2)//- Stop receive command, lock by FW.
#define   	CMD_DISP_RST                                    (BIT3)//- Reset Command dispatcher. HW will auto clear this bit.(E21 & A13)

// not use #define		APU_CMD_DISP_SUB_RD_LOCK_BIT					(BIT4)//- Stop receive command when sub-read command error. <RO> (E21 remove this)
#define		APU_CMD_DISP_INS_REQ_BIT						(BIT5)//- Assert if host request to insert (sub)cmd to cmd disp. <RO>

#define	R8_APU_CMD_DISP_FW_ONCE_LOCK						(0x81)
#define		APU_CMD_DISP_FW_ONCE_UNLOCK_BIT					(BIT0)//- Unlock one command, only valid when APU_CMD_DISP_FW_LOCK =1. Does not affect HW lock. FW write 1 to unlock, HW auto relock.

#define	R8_APU_CMD_DISP_FUA_CTRL							(0x82)
#define		APU_FUA_WR_EN_BIT								(BIT0)//- Write command FUA function enable.
/* // not use
#define		APU_FUA_RD_EN_BIT								(BIT1)//- Read command FUA function enable.
#define		APU_FUA_WR2SYNC_BIT								(BIT2)//- Change the FUA write command to sync command.
#define		APU_FUA_RD2SYNC_BIT								(BIT3)//- Change the FUA read command to sync command.
*/
#define		APU_FORCE_WR_FUA_BIT							(BIT4)//- Fource the write command FUA bit to 1.
/* // not use
#define		APU_FORCE_RD_FUA_BIT							(BIT5)//- Fource the read command FUA bit to 1.
#define		APU_WRCMD_FUA_LOCK_EN_BIT						(BIT6)//- Lock FIFO while FUA cmd in
*/

#define	R8_APU_CMD_DISP_STATE								(0x83)
// not use  #define		APU_CMD_DISP_STATE_SHIFT						(0)//pop command state
#define		APU_CMD_DISP_STATE_MASK							(BIT_MASK(2))//pop command stage

#define		    APU_CMD_DISP_STATE_POP_CMD						(0)//- Pop command state
#define		    APU_CMD_DISP_STATE_DEP_CHK						(1)//- Dependency check state
#define		    APU_CMD_DISP_STATE_FIFO_INSERT					(2)//- FIFO insert state
#define		    APU_CMD_DISP_STATE_LOCK_PAUSE					(3)//- Lock(pause) state
/* // not use
#define		APU_CMD_DISP_SUB_RD_STATE_SHIFT					(2)//dependency check state
#define		APU_CMD_DISP_SUB_RD_STATE_MASK					(BIT_MASK(2))//FIFO insert state
#define		    APU_CMD_DISP_SUB_RD_STATE_POP_CMD				(0)//- Pop command state
#define		    APU_CMD_DISP_SUB_RD_STATE_FIFO_INSERT			(2)//- FIFO insert state
#define		    APU_CMD_DISP_SUB_RD_STATE_LOCK_PAUSE			(3)//- Lock(pause) state (sub-read command error)
*/

#define	R32_APU_CMD_DISP_ATTR2SYNC							(0x84 >> 2)
#define		APU_NVME_ADM_02_ATTR2SYNC_BIT					(BIT0)//- Change the NVME admin command attribute for op code 02h to sync.
#define		APU_NVME_ADM_06_ATTR2SYNC_BIT					(BIT1)//- Change the NVME admin command attribute for op code 06h to sync.
#define		APU_NVME_ADM_0A_ATTR2SYNC_BIT					(BIT2)//- Change the NVME admin command attribute for op code 0Ah to sync.
#define		APU_NVME_ADM_0C_ATTR2SYNC_BIT					(BIT3)//- Change the NVME admin command attribute for op code 0Ch to sync.
#define		APU_NVME_IO_01_ATTR2SYNC_BIT					(BIT4)//- Change the NVME io command attribute for op code 01h to sync.
#define		APU_NVME_IO_02_ATTR2SYNC_BIT					(BIT5)//- Change the NVME io command attribute for op code 02h to sync.
#define 	APU_NVME_IO_02_ATTR2SYNC_SHIFT					(5)//- # Bit shift of the NVME io command attribute for op code 02h to sync.
#define		APU_SATA_20_ATTR2SYNC_BIT						(BIT8)
#define		APU_SATA_21_ATTR2SYNC_BIT						(BIT9)
#define		APU_SATA_24_ATTR2SYNC_BIT						(BIT10)
#define		APU_SATA_25_ATTR2SYNC_BIT						(BIT11)
#define		APU_SATA_29_ATTR2SYNC_BIT						(BIT12)
#define		APU_SATA_30_ATTR2SYNC_BIT						(BIT13)
#define		APU_SATA_31_ATTR2SYNC_BIT						(BIT14)
#define		APU_SATA_34_ATTR2SYNC_BIT						(BIT15)
#define		APU_SATA_35_ATTR2SYNC_BIT						(BIT16)
#define		APU_SATA_39_ATTR2SYNC_BIT						(BIT17)
#define		APU_SATA_3D_ATTR2SYNC_BIT						(BIT18)
#define		APU_SATA_60_ATTR2SYNC_BIT						(BIT19)
#define		APU_SATA_61_ATTR2SYNC_BIT						(BIT20)
#define		APU_SATA_C4_ATTR2SYNC_BIT						(BIT21)
#define		APU_SATA_C5_ATTR2SYNC_BIT						(BIT22)
#define		APU_SATA_C8_ATTR2SYNC_BIT						(BIT23)
#define		APU_SATA_C9_ATTR2SYNC_BIT						(BIT24)
#define		APU_SATA_CA_ATTR2SYNC_BIT						(BIT25)
#define		APU_SATA_CB_ATTR2SYNC_BIT						(BIT26)
#define		APU_SATA_CE_ATTR2SYNC_BIT						(BIT27)

#define	R16_APU_CMD_DISP_CTAG								(0x88 >> 1)//-  command dispatch operating ctag.
#define		APU_CMD_DISP_CTAG_SHIFT							(0)
#define		APU_CMD_DISP_CTAG_MASK							(BIT_MASK(9))

#define	R8_APU_CMD_DISP_LOCK								(0x8A)//-	stop receive command ,when receive sync command, HW auto lock, FW write 1 to clear lock.
#define		APU_CMD_DISP_LOCK_BIT							(BIT0)//- Stop receive command when receive sync command, HW auto lock, FW write 1 to clear lock.

/*================================= Dependency Checker Register ================================*/

#define	R64_APU_DEP_REQ_INSERT_INFO							(0xC0 >> 3)
#define	R32_APU_INSERT_REQ_INFO_LCA							(0xC0 >> 2)
/* // not use
#define     APU_INSERT_REQ_INFO_LCA_SHIFT                   (0)
#define     APU_INSERT_REQ_INFO_LCA_MASK                    (BIT_MASK(30))
*/
#define R8_APU_INSERT_REQ_INFO_ATTR                         (0xC3)
#define     APU_INSERT_REQ_INFO_ATTR_SHIFT                  (6)
#define     APU_INSERT_REQ_INFO_ATTR_MASK                   (BIT_MASK(2))
#define		    ATTRI_READ										(0)
#define		    ATTRI_WRITE										(1)
#define		    ATTRI_TRIM										(3)


#define	R16_APU_INSERT_REQ_INFO_NLC							(0xC4 >> 1)//- NLCA : 1-based; 17'h1 = one of LCA
#define	R8_APU_DEP_REQ_INSERT_INFO							(0xC6)
#define		APU_INSERT_REQ_INFO_NLC_1_BIT					(BIT0)
#define		APU_INSERT_REQ_INFO_NSID_SHIFT					(1)
#define		APU_INSERT_REQ_INFO_NSID_MASK					(BIT_MASK(5))

#define	R16_APU_INSERT_REQ_INFO_CTAG						(0xC6 >> 1)//- Ctag[7:0] : command tag
#define     APU_INSERT_REQ_INFO_CTAG_SHIFT                  (7)
#define     APU_INSERT_REQ_INFO_CTAG_MASK                   (BIT_MASK(9))

#define	R32_APU_DEP_REQ_CTRL								(0xC8 >> 2)
#define	R8_APU_DEP_REQ										(0xC8)
#define		APU_BYPASS_HW_DEP_CHK_BIT						(BIT0)//- Bypass HW dependency check if FW write 1.
#define		APU_INSERT_REQ_BIT								(BIT1)//- FW write 1 assert; de-assert when insert_ack assert.
#define		APU_DEP_ATTR_CHK_EN_BIT							(BIT2)//- Dependency delete attr check enable.
#define		APU_DEL_REQ_FW_BIT								(BIT3)//- FW write 1 assert; de-assert when delete request done.
#define		APU_DEL_REQ_INFO_MODE_BIT						(BIT4)
#define		APU_DEL_REQ_DEP_TAG_MODE						(0)
#define		APU_DEL_REQ_CTAG_MODE							(1)
#define		APU_DEL_REQ_INFO_ATTR_SHIFT						(6)
#define		APU_DEL_REQ_INFO_ATTR_MASK						(BIT_MASK(2))
#define		ApuDepDelete_REQ_READ_ATTR						(0x00)
#define		ApuDepDelete_REQ_WRITE_ATTR						(0x01)
#define		ApuDepDelete_REQ_TRIM_ATTR						(0x03)
#define	  APU_DEL_REQ_INFO_MODE_SHIFT						(4)
#define     APU_DEL_REQ_INFO_TAG_SHIFT                      (8)
#define     APU_DEL_REQ_INFO_TAG_MASK                       (BIT_MASK(9))
#define     APU_INSERT_ACK_INFO_SHIFT                       (17)
#define     APU_INSERT_ACK_INFO_MASK                        (BIT_MASK(9))

/* not use
#define	R16_APU_DEP_SRAM_CTRL								(0xCC >> 1)
#define	R8_APU_DEP_SRAM_ENTRY_THR							(0xCC)//- Control the dependency checker SRAM entry threshold.
#define		APU_DEP_SRAM_ENTRY_THR_SHIFT					(0)
#define		APU_DEP_SRAM_ENTRY_THR_MASK						(BIT_MASK(8))
#define		APU_DEP_SRAM_EXIT_THR_SHIFT						(8)
#define		APU_DEP_SRAM_EXIT_THR_MASK						(BIT_MASK(8))
#define	R8_APU_DEP_SRAM_EXIT_THR							(0xCD)//- Control the dependency checker SRAM exit threshold.
*/

#define	R16_APU_DEP_RD_CMD_CNT								(0xD0 >> 1)
#define		APU_DEP_RD_CMD_CNT_SHIFT						(0)
#define		APU_DEP_RD_CMD_CNT_MASK							(BIT_MASK(10))

#define	R16_APU_DEP_WR_CMD_CNT								(0xD2 >> 1)
#define		APU_DEP_WR_CMD_CNT_SHIFT						(0)
#define		APU_DEP_WR_CMD_CNT_MASK							(BIT_MASK(10))

#define	R16_APU_DEP_TRIM_CMD_CNT							(0xD4 >> 1)
#define		APU_DEP_TRIM_CMD_CNT_SHIFT						(0)
#define		APU_DEP_TRIM_CMD_CNT_MASK						(BIT_MASK(10))

#define	R8_APU_DEP_TABLE_STATE								(0xD6)
#define		APU_DEP_TABLE_FULL_BIT							(BIT0)//- Dependency table is full.
#define		APU_DEP_TABLE_FULL_BIT_SHIFT					(0)
#define		APU_DEP_TABLE_FULL_BIT_MASK						(BIT_MASK(1))
#define		APU_DEP_TABLE_EMPTY_BIT							(BIT1)//- Dependency table is empty.
#define		APU_DEP_TABLE_EMPTY_BIT_SHIFT					(1)
#define		APU_DEP_TABLE_EMPTY_BIT_MASK					(BIT_MASK(1))

#define	R8_APU_DEP_FAIL_LOCK_EN								(0xD8)
#define		APU_DEP_FAIL_LOCK_EN_BIT						(BIT0)//- This bit is set to enable the dependency lock due to error delete.

#define	R8_APU_DEP_LOCK										(0xDA)
#define		APU_DEP_LOCK_BIT								(BIT0)//- Delete request is ending with error.
#define		APU_DEL_FAIL_BIT								(BIT1)//- Dependency check lock due to error delete. 0: Due to FW delete fail, only valid when dep_lock assert. 1: Due to HW delete fail, only valid when dep_lock assert.

//E21 has 512 bit
#define	R8_APU_DEP_TABLE_VLD_BIT							(0x110)//- Dependency table valid bit. N=0~127
#define	R32_APU_DEP_TABLE_VLD_BIT_0							(0x110 >> 2)//- Dependency table valid bit.BIT
#define	R32_APU_DEP_TABLE_VLD_BIT_32						(0x114 >> 2)//- Dependency table valid bit.
#define	R32_APU_DEP_TABLE_VLD_BIT_64						(0x118 >> 2)//- Dependency table valid bit.
#define	R32_APU_DEP_TABLE_VLD_BIT_96						(0x11C >> 2)//- Dependency table valid bit.

/* not use
#define R32_APU_CTM_CTRL                                    (0xDC >> 2)
#define R16_APU_CTM_CTRL                                    (0xDC >> 1) // [15:0] is APU_CTM_THR, unit is 10ms. Reasonable value:0x10 < APU_CTM_THR < 0xFFF0
#define     APU_CTM_RST                                     (BIT16)

#define	R8_APU_DEP_SRAM_PAR_ERR								(0xE0)//- This field is used to record the DEP_SRAM address when SRAM parity error occurred.
#define	APU_DEP_SRAM_PAR_ERR_SHIFT							(0)
#define	APU_DEP_SRAM_PAR_ERR_MASK							(BIT_MASK(7))

#define	R16_APU_LR_PERR_ADDR								(0xE2 >> 1)//- This field is used to record the LCA Remap related SRAM address when parity error occurred.
#define	    APU_LR_PERR_ADDR_SHIFT						    (0)
#define	    APU_LR_PERR_ADDR_MASK						    (BIT_MASK(10))

#define R16_APU_CTM_PERR_ADDR                               (0xE4 >> 1)//- This field is used to record the Command Timer SRAM address when parity error occurred.
#define     APU_CTM_PERR_ADDR_SHIFT                         (0)
#define     APU_CTM_PERR_ADDR_MASK                          (BIT_MASK(9))

#define R16_APU_DEL_FAIL_INFO                               (0xE6 >> 1)//- Move from R32_APU_DEP_REQ_CTRL to here.
#define     APU_DEL_FAIL_INFO_SHIFT                         (0)
#define     APU_DEL_FAIL_INFO_MASK                          (BIT_MASK(9))

#define R16_APU_LCTM_CTRL                                   (0xE8 >> 1)//- [15:0] is APU_LCTM_THR, unit is 1us.

#define R32_APU_LCTM_INFO                                   (0xF0 >> 2)//- 0xF0 + N * 4 (N = 0 ~ 3)
#define     APU_LCTM_TIMER_SHIFT                            (0)
#define     APU_LCTM_TIMER_MASK                             (BIT_MASK(16))
#define     APU_LCTM_CTAG_SHIFT                             (16)
#define     APU_LCTM_CTAG_MASK                              (BIT_MASK(9))
#define     APU_LCTM_VLD                                    (BIIT31)



#define R32_APU_CTM_SRAM                                    (0x800 >> 2)//- 0x800 + N * 4 (N = 0 ~ 511)
#define     APU_CTM_TIMER_SHIFT                             (0)
#define     APU_CTM_TIMER_MASK                              (BIT_MASK(16))
#define     APU_CTM_CTAG_SHIFT                              (16)
*/

//Different with both E13 & A13
#define	R64_APU_DEP_RD_RSLT_SRAM_OFFSET						(0x2000 >> 3)//- 0x2000 + N * 8 (N = 0 ~ 511), when dependency index "X", N(X) = ((X % 128) * 4 + (X >> 7))
#define	R8_APU_DEP_RD_RSLT_SRAM_OFFSET						(0x2000)//- The result of read dependency checker SRAM register.
#define	R64_APU_DEP_RD_RSLT_SRAM							((volatile U64 *)(APU_REG_ADDRESS + R8_APU_DEP_RD_RSLT_SRAM_OFFSET))
#define		APU_DEP_RD_RSLT_SRAM_LCA_SHIFT					(0)
#define     APU_DEP_RD_RSLT_SRAM_LCA_MASK                   (BIT_MASK(30))
#define     APU_DEP_RD_RSLT_SRAM_ATTR_SHIFT                 (30)
#define     APU_DEP_RD_RSLT_SRAM_ATTR_MASK                  (BIT_MASK(2))
/* not use
#define     APU_DEP_RD_RSLT_SRAM_NLC_SHIFT                  (32)
#define     APU_DEP_RD_RSLT_SRAM_NLC_MASK                   (BIT_MASK(17))
#define     APU_DEP_RD_RSLT_SRAM_NSID_SHIFT                 (49)
#define     APU_DEP_RD_RSLT_SRAM_NSID_MASK                  (BIT_MASK(5))
*/
#define     APU_DEP_RD_RSLT_SRAM_VLD_BIT                    (BIT54)
#define     APU_DEP_RD_RSLT_VLD_SHIFT                       (54)
#define     APU_DEP_RD_RSLT_VLD_MASK                        (BIT_MASK(1))
#define     APU_DEP_RD_RSLT_SRAM_CTAG_SHIFT                 (55)
#define     APU_DEP_RD_RSLT_SRAM_CTAG_MASK                  (BIT_MASK(9))

/*================================= CMD_LOG Register ================================*/

#define	R8_APU_CMD_LOG_CTRL									(0x280)
#define		APU_CMD_INFO_LOG_EN_BIT							(BIT0)//- Set 1 to enable command info log.

#define	R16_APU_CMD_LOG_CNT_CLRL							(0x282 >> 1)
#define		APU_CMD_LOG_WR_CMD_CNT_EN_BIT					(BIT0)//- Set 1 to enable the counter of write command.
#define		APU_CMD_LOG_RD_CMD_CNT_EN_BIT					(BIT1)//- Set 1 to enable the counter of read command.
#define		APU_CMD_LOG_RAND_WR_CMD_CNT_EN_BIT				(BIT2)//- Set 1 to enable the counter of random write command.
#define		APU_CMD_LOG_RAND_RD_CMD_CNT_EN_BIT				(BIT3)//- Set 1 to enable the counter of random read command.
#define		APU_CMD_LOG_SEQ_WR_CMD_CNT_EN_BIT				(BIT4)//- Set 1 to enable the counter of sequential write command sectors.
#define		APU_CMD_LOG_SEQ_RD_CMD_CNT_EN_BIT				(BIT5)//- Set 1 to enable the counter of sequential read command sectors.
#define		APU_CMD_LOG_NONALIGN_WR_CMD_CNT_EN_BIT			(BIT6)//- Set 1 to enable the counter of no-align write command.
#define		APU_CMD_LOG_NONALIGN_RD_CMD_CNT_EN_BIT			(BIT7)//- Set 1 to enable the counter of no-align read command.
#define		APU_CMD_LOG_WR_CMD_SECTOR_CNT_EN_BIT			(BIT8)//- Set 1 to enable the counter of write command sectors.
#define		APU_CMD_LOG_RD_CMD_SECTOR_CNT_EN_BIT			(BIT9)//- Set 1 to enable the counter of read command sectors.

#define	R16_APU_CMD_LOG_CMD_CNT_CLR							(0x284 >> 1)
#define		APU_CMD_LOG_WR_CMD_CNT_CLR_BIT					(BIT0)//- Clear the write command counter. 1T pulse, FW write 1 to clear, HW de-assert when clear done.
#define		APU_CMD_LOG_RD_CMD_CNT_CLR_BIT					(BIT1)//- Clear the read command counter. 1T pulse, FW write 1 to clear, HW de-assert when clear done.
#define		APU_CMD_LOG_RAND_WR_CMD_CNT_CLR_BIT				(BIT2)//- Clear the random write command counter. 1T pulse, FW write 1 to clear, HW de-assert when clear done.
#define		APU_CMD_LOG_RAND_RD_CMD_CNT_CLR_BIT				(BIT3)//- Clear the random read command counter. 1T pulse, FW write 1 to clear, HW de-assert when clear done.
#define		APU_CMD_LOG_SEQ_WR_CMD_CNT_CLR_BIT				(BIT4)//- Clear the sequential write command counter. 1T pulse, FW write 1 to clear, HW de-assert when clear done.
#define		APU_CMD_LOG_SEQ_RD_CMD_CNT_CLR_BIT				(BIT5)//- Clear the sequential read command counter. 1T pulse, FW write 1 to clear, HW de-assert when clear done.
#define		APU_CMD_LOG_NONALIGN_WR_CMD_CNT_CLR_BIT			(BIT6)//- Clear the non-align write command counter. 1T pulse, FW write 1 to clear, HW de-assert when clear done.
#define		APU_CMD_LOG_NONALIGN_RD_CMD_CNT_CLR_BIT			(BIT7)//- Clear the non-align read command counter. 1T pulse, FW write 1 to clear, HW de-assert when clear done.
#define		APU_CMD_LOG_WR_CMD_SECTOR_CNT_CLR_BIT			(BIT8)//- Clear the write command sectors counter. 1T pulse, FW write 1 to clear, HW de-assert when clear done.
#define		APU_CMD_LOG_RD_CMD_SECTOR_CNT_CLR_BIT			(BIT9)//- Clear the read command sectors counter. 1T pulse, FW write 1 to clear, HW de-assert when clear done.

/* not use
#define	R8_APU_CMD_LOG_FW_REQ								(0x288)
#define		APU_CMD_LOG_FW_REQ_BIT							(BIT0)//- FW command log request to mail router, FW set 1 to trigger and HW clear after transmit done.

#define	R32_APU_CMD_LOG_FW_WDATA							(0x290 >> 2)//- [127:0] The data of FW write command log to mail router.
#define	R32_APU_CMD_LOG_FW_WDATA_0							(0x290 >> 2)
#define	R32_APU_CMD_LOG_FW_WDATA_32							(0x294 >> 2)
#define	R32_APU_CMD_LOG_FW_WDATA_64							(0x298 >> 2)
#define	R32_APU_CMD_LOG_FW_WDATA_96							(0x29C >> 2)

#define	R64_APU_CMD_LOG_WR_CMD_CNT							(0x2A0 >> 3)
#define		APU_CMD_LOG_WR_CMD_CNT_SHIFT					(0)
#define		APU_CMD_LOG_WR_CMD_CNT_MASK						(BIT_MASK(64))
#define	R64_APU_CMD_LOG_RD_CMD_CNT							(0x2A8 >> 3)
#define		APU_CMD_LOG_RD_CMD_CNT_SHIFT					(0)
#define		APU_CMD_LOG_RD_CMD_CNT_MASK						(BIT_MASK(64))
#define	R64_APU_CMD_LOG_RAND_WR_CMD_CNT						(0x2B0 >> 3)
#define		APU_CMD_LOG_RAND_WR_CMD_CNT_SHIFT				(0)
#define		APU_CMD_LOG_RAND_WR_CMD_CNT_MASK				(BIT_MASK(64))
#define	R64_APU_CMD_LOG_RAND_RD_CMD_CNT						(0x2B8 >> 3)
#define		APU_CMD_LOG_RAND_RD_CMD_CNT_SHIFT				(0)
#define		APU_CMD_LOG_RAND_RD_CMD_CNT_MASK				(BIT_MASK(64))
#define	R64_APU_CMD_LOG_SEQ_WR_CMD_CNT						(0x2C0 >> 3)
#define		APU_CMD_LOG_SEQ_WR_CMD_CNT_SHIFT				(0)
#define		APU_CMD_LOG_SEQ_WR_CMD_CNT_MASK					(BIT_MASK(64))
#define	R64_APU_CMD_LOG_SEQ_RD_CMD_CNT						(0x2C8 >> 3)
#define		APU_CMD_LOG_SEQ_RD_CMD_CNT_SHIFT				(0)
#define		APU_CMD_LOG_SEQ_RD_CMD_CNT_MASK					(BIT_MASK(64))
*/
#define	R64_APU_CMD_LOG_NONALIGN_WR_CMD_CNT					(0x2D0 >> 3)
#define		APU_CMD_LOG_NONALIGN_WR_CMD_CNT_SHIFT			(0)
#define		APU_CMD_LOG_NONALIGN_WR_CMD_CNT_MASK			(BIT_MASK(64))
/* not use
#define	R64_APU_CMD_LOG_NONALIGN_RD_CMD_CNT					(0x2D8 >> 3)
#define		APU_CMD_LOG_NONALIGN_RD_CMD_CNT_SHIFT			(0)
#define		APU_CMD_LOG_NONALIGN_RD_CMD_CNT_MASK			(BIT_MASK(64))
*/
#define	R64_APU_CMD_LOG_WR_CMD_SECTOR_CNT					(0x2E0 >> 3)
#define		APU_CMD_LOG_WR_CMD_SECTOR_CNT_SHIFT				(0)
#define		APU_CMD_LOG_WR_CMD_SECTOR_CNT_MASK				(BIT_MASK(64))
#define	R64_APU_CMD_LOG_RD_CMD_SECTOR_CNT					(0x2E8 >> 3)
#define		APU_CMD_LOG_RD_CMD_SECTOR_CNT_SHIFT				(0)
#define		APU_CMD_LOG_RD_CMD_SECTOR_CNT_MASK				(BIT_MASK(64))
/* not use
#define	R8_APU_CMD_LOG_STATE								(0x2F0)
#define		APU_CMD_LOG_STATE_SHIFT							(0)
#define		APU_CMD_LOG_STATE_MASK							(BIT_MASK(2))
*/
/*========================================== IO Meter Register ==========================================*/

#define	R8_APU_IO_METER_CTRL								(0x300)
#define		APU_IO_METER_CNT_RST_BIT						(BIT0)//- FW write 1 to assert, HW de-assert to indicate count reset done.
#define		APU_IO_METER_PAUSE_BIT							(BIT1)//- Set 1 to PAUSE IO Meter to count all LCA & NSID 4K number.
#define		APU_IO_METER_PART_PAUSE_BIT						(BIT2)//- Set 1 to PAUSE IO Meter to count partial LCA & NSID 4K number.
#define		APU_IO_METER_DEP_FAIL_CNT_RST_BIT				(BIT3)//- FW write 1 to assert, HW de-assert to indicate dependence check fail counter reset done.
#define		APU_IO_METER_DEP_FAIL_CNT_PAUSE_BIT				(BIT4)//- Set 1 to PAUSE IO Meter to count dependence check fail number.
#define		APU_IO_METER_PEND_CMD_CNT_RST_BIT				(BIT5)//- FW write 1 to assert, HW de-assert to indicate pending command counter reset done.
#define		APU_IO_METER_PEND_CMD_CNT_PAUSE_BIT				(BIT6)//- Set 1 to PAUSE IO Meter to count pending command number.

#define	R16_APU_IO_METER_PEND_CMD_CNT_TRG_CLK_NUM			(0x302 >> 1)//- Set the clock number for pending command count interval.
#define		APU_IO_METER_PEND_CMD_CNT_TRG_CLK_NUM_SHIFT		(0)
#define		APU_IO_METER_PEND_CMD_CNT_TRG_CLK_NUM_MASK		(BIT_MASK(16))

#define	R16_APU_IO_METER_INTVL								(0x304 >> 1)//-    1ms-unit,  Set the timing interval for all command counter.
#define		APU_IO_METER_INTVL_SHIFT						(0)
#define		APU_IO_METER_INTVL_MASK							(BIT_MASK(10))
/* not use
#define	R16_APU_IO_METER_PART_INTVL							(0x306 >> 1)//-    1ms-unit,  Set the timing interval for partial command counter.
#define		APU_IO_METER_PART_INTVL_SHIFT					(0)
#define		APU_IO_METER_PART_INTVL_MASK					(BIT_MASK(10))

#define	R32_APU_IO_METER_PART_SLCA							(0x308 >> 2)//-    Set the Start LCA of want to count range.
#define		APU_IO_METER_PART_SLCA_SHIFT					(0)
#define		APU_IO_METER_PART_SLCA_MASK						(BIT_MASK(32))
#define	R32_APU_IO_METER_PART_ELCA							(0x30C >> 2)//Set the End LCA of want to count range.
#define		APU_IO_METER_PART_ELCA_SHIFT					(0)
#define		APU_IO_METER_PART_ELCA_MASK						(BIT_MASK(32))
*/
#define	R8_APU_WR_DLY_THRH_EN								(0x310)
#define		APU_WR_DLY_THRH_EN_BIT							(BIT0)//- When APU_WR_DLY_THRESHOLD_EN = 1, if APU_IO_METER_WR_4K_CNT_0 >= APU_WR_DLY_THRESHOLD, then switch on APU_DLY_WR_EN or SATA_TQ_TRG_DLY_EN.
#define		APU_DLY_WR_STATE_BIT							(BIT1)//- Indicate the APU_DLY_WR_EN or SATA_TQ_TRG_DLY_EN switch on

#define	R32_APU_WR_DLY_THRH									(0x314 >> 2)//-    if APU_IO_METER_WR_4K_CNT_0 >= APU_WR_DLY_THRESHOLD, then switch on APU_DLY_WR_EN or SATA_TQ_TRG_DLY_EN.
#define		APU_WR_DLY_THRH_SHIFT							(0)
#define		APU_WR_DLY_THRH_MASK							(BIT_MASK(32))
/* not use
#define	R8_APU_IO_METER_PART_NS_BMAP						(0x318)//- Set the NSID of want to count range, bit 0 = NSID 0; bit 1 = NSID 1; bit 31 =NSID 31.

#define	R32_APU_IO_METER_DEP_WR_FAIL_CNT					(0x330 >> 2)//- Indicate the dependence check write command fail number.
#define		APU_IO_METER_DEP_WR_FAIL_CNT_SHIFT				(0)
#define		APU_IO_METER_DEP_WR_FAIL_CNT_MASK				(BIT_MASK(32))

#define	R32_APU_IO_METER_DEP_RD_FAIL_CNT					(0x338 >> 2)//- Indicate the dependence check read command fail number.
#define		APU_IO_METER_DEP_RD_FAIL_CNT_SHIFT				(0)
#define		APU_IO_METER_DEP_RD_FAIL_CNT_MASK				(BIT_MASK(32))
*/
//- Indicate the write/read 4K number for all/partial command counter.
//- The timing for update number is according to APU_IO_METER_INTVL
//- new number -> CNT_0 -> CNT_1 -> CNT_2 ->CNT_3

#define	R32_APU_IO_METER_WR_4K_CNT_N						(0x340 >> 2)
#define	R32_APU_IO_METER_WR_4K_CNT_0						(0x340 >> 2)
#define	R32_APU_IO_METER_WR_4K_CNT_1						(0x344 >> 2)
#define	R32_APU_IO_METER_WR_4K_CNT_2						(0x348 >> 2)
#define	R32_APU_IO_METER_WR_4K_CNT_3						(0x34C >> 2)

#define	R32_APU_IO_METER_RD_4K_CNT_N						(0x360 >> 2)
#define	R32_APU_IO_METER_RD_4K_CNT_0						(0x360 >> 2)
#define	R32_APU_IO_METER_RD_4K_CNT_1						(0x364 >> 2)
#define	R32_APU_IO_METER_RD_4K_CNT_2						(0x368 >> 2)
#define	R32_APU_IO_METER_RD_4K_CNT_3						(0x36C >> 2)
/* not use
#define	R32_APU_IO_METER_PART_WR_4K_CNT_N					(0x380 >> 2)
#define	R32_APU_IO_METER_PART_WR_4K_CNT_0					(0x380 >> 2)
#define	R32_APU_IO_METER_PART_WR_4K_CNT_1					(0x384 >> 2)
#define	R32_APU_IO_METER_PART_WR_4K_CNT_2					(0x388 >> 2)
#define	R32_APU_IO_METER_PART_WR_4K_CNT_3					(0x38C >> 2)

#define	R32_APU_IO_METER_PART_RD_4K_CNT_N					(0x3A0 >> 2)
#define	R32_APU_IO_METER_PART_RD_4K_CNT_0					(0x3A0 >> 2)
#define	R32_APU_IO_METER_PART_RD_4K_CNT_1					(0x3A4 >> 2)
#define	R32_APU_IO_METER_PART_RD_4K_CNT_2					(0x3A8 >> 2)
#define	R32_APU_IO_METER_PART_RD_4K_CNT_3					(0x3AC >> 2)
#define		APU_IO_METER_PEND_RD_CMD_CNT_MASK				(BIT_MASK(32))
*/
#define	R32_APU_IO_METER_PEND_CMD_CNT_TRG_NUM				(0x3C0 >> 2)
#define		APU_IO_METER_PEND_CMD_CNT_TRG_NUM_SHIFT			(0)
#define		APU_IO_METER_PEND_CMD_CNT_TRG_NUM_MASK			(BIT_MASK(32))

#define	R32_APU_IO_METER_PEND_WR_CMD_CNT					(0x3C8 >> 2)//- Indicate the pending write command number.
#define		APU_IO_METER_PEND_WR_CMD_CNT_SHIFT				(0)
#define		APU_IO_METER_PEND_WR_CMD_CNT_MASK				(BIT_MASK(32))

#define	R32_APU_IO_METER_PEND_RD_CMD_CNT					(0x3D0 >> 2)//- Indicate the pending read command number.
#define		APU_IO_METER_PEND_RD_CMD_CNT_SHIFT				(0)
#define		APU_IO_METER_PEND_RD_CMD_CNT_MASK				(BIT_MASK(32))


/*========================================== IN_ORDER_RD Register ==========================================*/
#define	R32_APU_IN_ORDER_RD_TAB_INFO						(0x400 >> 2)
/* not use
#define	R16_APU_IN_ORDER_RD_TAB_INFO_WORD0					(0x400 >> 1)//- [11:0] This field is used to define the data size of this tag of the In Order Read Table.
#define		APU_IN_ORDER_RD_TAB_INFO_SIZE_SHIFT				(0)
#define		APU_IN_ORDER_RD_TAB_INFO_SIZE_MASK				(BIT_MASK(11))
#define		APU_IOT_MUL_TRG_BIT								(BIT11)
#define		APU_IN_ORDER_RD_TAB_INFO_GRP_SHIFT				(12)
#define		APU_IN_ORDER_RD_TAB_INFO_GRP_MASK				(BIT_MASK(3))
#define		APU_IN_ORDER_RD_TAB_INFO_HOLD_BIT				(BIT15)
#define		APU_IN_ORDER_RD_TAB_INFO_OFST_SHIFT				(16)
#define		APU_IN_ORDER_RD_TAB_INFO_OFST_MASK				(BIT_MASK(10))
#define		APU_IN_ORDER_RD_TAB_INFO_CTAG_SHIFT				(26)
#define		APU_IN_ORDER_RD_TAB_INFO_CTAG_MASK				(BIT_MASK(5))
#define		APU_IN_ORDER_RD_TAB_INFO_CMD_BIT				(BIT31)
*/
#define	R16_APU_IN_ORDER_RD_TAB_INFO_WORD1					(0x402 >> 1)
#define		READ_IOT										(0)
#define		CONFIGURE_IOT									(1)

#define	R32_APU_IN_ORDER_RD_VLD_CLR							(0x404 >> 2)
#define		APU_IN_ORDER_RD_VLD_CLR_SHIFT					(0)
#define		APU_IN_ORDER_RD_VLD_CLR_MASK					(BIT_MASK(32))

#define	R8_APU_IN_ORDER_RD_TAB_CTRL							(0x408)
#define		APU_IN_ORDER_RD_TAB_REQ_BIT						(BIT0)//- FW writes 1 to assert and HW de-asserts to indicate the read/write In Order Read Table process is done.

/* not use
#define	R16_APU_IN_ORDER_RD_TAB_DELAY						(0x40C >> 1)
#define		APU_IN_ORDER_RD_TAB_DELAY_SHIFT					(0)
#define		APU_IN_ORDER_RD_TAB_DELAY_MASK					(BIT_MASK(16))
*/

#define	R16_APU_IN_ORDER_RD_CUR_STAT						(0x40E >> 1)
#define		APU_IN_ORDER_RD_CUR_TAG_SHIFT					(0)
#define		APU_IN_ORDER_RD_CUR_TAG_MASK					(BIT_MASK(5))
#define		APU_IN_ORDER_RD_CUR_IDX_SHIFT					(8)
#define		APU_IN_ORDER_RD_CUR_IDX_MASK					(BIT_MASK(5))

/* not use
#define	R32_APU_IN_ORDER_RD_TAB_RSLT						(0x420 >> 2)
#define		APU_IN_ORDER_RD_TAB_RSLT_SIZE_SHIFT				(0)
#define		APU_IN_ORDER_RD_TAB_RSLT_SIZE_MASK				(BIT_MASK(12))
#define		APU_IN_ORDER_RD_TAB_RSLT_GRP_SHIFT				(12)
#define		APU_IN_ORDER_RD_TAB_RSLT_GRP_MASK				(BIT_MASK(3))
#define		APU_IN_ORDER_RD_TAB_RSLT_HOLD_BIT				(BIT15)
#define		APU_IN_ORDER_RD_TAB_RSLT_OFST_SHIFT				(16)
#define		APU_IN_ORDER_RD_TAB_RSLT_OFST_MASK				(BIT_MASK(10))
#define		APU_IN_ORDER_RD_TAB_RSLT_CTAG_SHIFT				(26)
#define		APU_IN_ORDER_RD_TAB_RSLT_CTAG_MASK				(BIT_MASK(5))
*/

#define	R32_APU_IN_ORDER_RD_TAB_VLD							(0x428 >> 2)
#define		APU_IN_ORDER_RD_TAB_VLD_SHIFT					(0)
#define		APU_IN_ORDER_RD_TAB_VLD_MASK					(BIT_MASK64(32))

/*========================================== MR Register ==========================================*/

#define	R64_APU_MR_Q										(0x480 >> 3)
#define	R8_APU_MR_Q_ID										(0x480)
#define	R8_APU_MR_Q_ID_COP1									(0x480)
/* not use
#define	R8_APU_MR_Q_ID_BMU_0								(0x481)
#define	R8_APU_MR_Q_ID_BMU_1								(0x482)
#define	R8_APU_MR_Q_ID_DMAC									(0x483) //In E13, this is named as R8_APU_MR_Q_ID_AES
#define	R8_APU_MR_Q_ID_CMD_LOG								(0x484)
#define R8_APU_MR_Q_ID_BMU_2                                (0x485)
#define		APU_MR_Q_ID_COP1_SHIFT							(0)
#define		APU_MR_Q_ID_COP1_MASK							(BIT_MASK(8))
#define		APU_MR_Q_ID_BMU_0_SHIFT							(8)
#define		APU_MR_Q_ID_BMU_0_MASK							(BIT_MASK(8))
#define		APU_MR_Q_ID_BMU_1_SHIFT							(16)
#define		APU_MR_Q_ID_BMU_1_MASK							(BIT_MASK(8))
#define		APU_MR_Q_ID_DMAC_SHIFT							(24)
#define		APU_MR_Q_ID_DMAC_MASK							(BIT_MASK(8))
#define		APU_MR_Q_ID_CMD_LOG_SHIFT						(32)
#define		APU_MR_Q_ID_CMD_LOG_MASK						(BIT_MASK(8))
#define     APU_MR_Q_ID_BMU_2_SHIFT                         (40)
#define     APU_MR_Q_ID_BMU_2_MASK                          (BIT_MASK(8))
*/
/*========================================== CMD_FIFO Register ==========================================*/

#define	R8_APU_CMD_FIFO_VLD_CTRL							(0x500)
#define		APU_CMD_FIFO_VLD_CLR_REQ_BIT					(BIT0)//- The request of clear the command fifo valid of the index(APU_CMD_FIFO_VLD_IDX)

#define	R8_APU_CMD_FIFO_LOCK								(0x501)
#define		APU_F_DB_SYNC_LOCK_BIT							(BIT0)//- (A)sync command stop to send to doorbell.
#define		APU_F_PRE_RD_LOCK_BIT							(BIT1)//- Miss read command stop to send to doorbell.
#define		APU_F_WR_BLK_LOCK_BIT							(BIT2)//- Write command stop to send to write block.

#define	R8_APU_CMD_FIFO_SRAM_CTRL							(0x502)
#define		APU_CMD_FIFO_SRAM_INIT_BIT						(BIT0)//- Cmd_fifo sram init, FW shall init sram only when no cmd will come. FW write 1 to init, HW clear when init done.
#define		APU_CMD_FIFO_SRAM_PARITY_CHK_EN_BIT				(BIT1)//- Cmd_fifo sram parity check enable.

#define	R8_APU_WR_BLK_FUA_LOCK								(0x503)
#define		APU_WR_BLK_FUA_LOCK_BIT							(BIT0)//- Write command is locked due to FUA bit, FW write 1 to clear APU_WR_BLK_FUA_LOCK.

#define	R8_APU_CMD_FIFO_ONCE_UNLOCK							(0x504)
#define		APU_F_DB_SYN_ONCE_UNLOCK_BIT					(BIT0)//- Only valid when APU_F_DB_SYN_LOCK=1. Write 1 to unlock one (a)sync cmd to be sent to doorbell. Cleared by HW when one (a)sync cmd is sent to doorbell.
#define		APU_F_PRE_RD_ONCE_UNLOCK_BIT					(BIT1)//- Only valid when APU_F_PRE_RD_LOCK=1. Write 1 to unlock one pre-read cmd to be sent to read block. Cleared by HW when one pre-read cmd is sent to read block.
#define		APU_F_WR_BLK_ONCE_UNLOCK_BIT					(BIT2)//- Only valid when APU_F_WR_BLK_LOCK=1. Write 1 to unlock one write cmd to be sent to write block. Cleared by HW when one write cmd is sent to write block.

#define	R8_APU_CMD_FIFO_ABORT_LOCK							(0x505)
#define		APU_CMD_FIFO_ABORT_LOCK_BIT						(BIT0)//- Only valid when APU_CMD_FIFO_ABORT_LOCK_EN = 1. Indicate HW auto lock cmd fifo when cmd aborted and FW writes 1 to clear this bit.

#define	R16_APU_CMD_FIFO_VLD_IDX								(0x508 >> 1)
#define		APU_CMD_FIFO_VLD_IDX_SHIFT						(0)
#define		APU_CMD_FIFO_VLD_IDX_MASK						(BIT_MASK(9))

#define	R8_APU_CMD_FIFO_CTRL								(0x510)
#define		APU_CMD_FIFO_REORDER_EN_BIT						(BIT0)//- This bit is used to enable reorder sorting.
#define		APU_CMD_FIFO_SCAN_TIMER_PAUSE_BIT				(BIT1)//- Scan timer pause.
#define		APU_CMD_FIFO_INORDER_WR_EN_BIT					(BIT2)//- This register is to enable Write Cmd In-Order mode.
#define		APU_CMD_FIFO_INORDER_RD_EN_BIT					(BIT3)//- This register is to enable Read Cmd In-Order mode. Cannot set this field and APU_CMD_FIFO_REORDER_EN at the same time.
#define		APU_CMD_FIFO_ABORT_LOCK_EN_BIT					(BIT4)//- This register is to enable HW auto lock when cmd aborted.

/* not use
#define	R16_APU_CMD_FIFO_TIMER_SEL_THR						(0x514 >> 1)
#define		APU_CMD_FIFO_SCAN_TIMER_SEL_THR_SHIFT			(0)
#define		APU_CMD_FIFO_SCAN_TIMER_SEL_THR_MASK			(BIT_MASK(10))

#define	R16_APU_CMD_FIFO_TIMER_THR_F						(0x518 >> 1)
#define		APU_CMD_FIFO_SCAN_TIMER_THR_F_SHIFT				(0)
#define		APU_CMD_FIFO_SCAN_TIMER_THR_F_MASK				(BIT_MASK(16))

#define	R16_APU_CMD_FIFO_TIMER_THR_S						(0x51C >> 1)
#define		APU_CMD_FIFO_SCAN_TIMER_THR_S_SHIFT				(0)
#define		APU_CMD_FIFO_SCAN_TIMER_THR_S_MASK				(BIT_MASK(16))

#define	R16_APU_CMD_FIFO_TIMER_UNIT							(0x520 >> 1)
#define		APU_CMD_FIFO_SCAN_TIMER_UNIT_SHIFT				(0)
#define		APU_CMD_FIFO_SCAN_TIMER_UNIT_MASK				(BIT_MASK(16))

#define	R16_APU_CMD_FIFO_TIMEOUT_THR						(0x524 >> 1)//- Timer threshold for timeout.
#define		APU_CMD_FIFO_TIMEOUT_THR_SHIFT					(0)
#define		APU_CMD_FIFO_TIMEOUT_THR_MASK					(BIT_MASK(16))

#define	R16_APU_CMD_FIFO_CMD_SIZE_THR						(0x528 >> 1)//- RCmd size threshold.
#define		APU_CMD_FIFO_CMD_SIZE_THR_SHIFT					(0)
#define		APU_CMD_FIFO_CMD_SIZE_THR_MASK					(BIT_MASK(16))

#define	R16_APU_CMD_FIFO_CMD_NUM_THR						(0x52C >> 1)//- Control the outstanding RCmd number threshold for the scan quit condition.
#define		APU_CMD_FIFO_CMD_CMD_THR_SHIFT					(0)
#define		APU_CMD_FIFO_CMD_CMD_THR_MASK					(BIT_MASK(10))
*/

#define	R8_APU_CMD_FIFO_SRAM_ERR_CLR						(0x530)
#define		APU_CMD_FIFO_IDX_SRAM_ERR_ADDR_CLR_BIT			(BIT0)//- Clear APU_CMD_FIFO_IDX_SRAM_ERR_INT. FW write 1 to clear, HW de-assert when clear done.
#define		APU_CMD_FIFO_INFO_SRAM_ERR_ADDR_CLR_BIT			(BIT1)//- Clear APU_CMD_FIFO_SRAM_ERR_INT. FW write 1 to clear, HW de-assert when clear done.

/* not use
#define	R8_APU_CMD_FIFO_SRAM_CLKGAT_THR						(0x538)
#define		APU_CMD_FIFO_SRAM_CLKGAT_THR_SHIFT				(0)
#define		APU_CMD_FIFO_SRAM_CLKGAT_THR_MASK				(BIT_MASK(8))
*/

#define	R8_APU_CMD_FIFO_STATE								(0x580)
#define		APU_CMD_FIFO_SRAM_FW_ARB_REQ_BIT	        	(BIT0)
#define		APU_CMD_FIFO_SRAM_RDMGR_ARB_REQ_BIT	       		(BIT1)
#define		APU_CMD_FIFO_SRAM_WRMGR_ARB_REQ_BIT	       		(BIT2)
#define		APU_CMD_FIFO_SRAM_ASYNC_DB_ARB_REQ_BIT	       	(BIT3)
/* not use
#define		APU_CMD_FIFO_SRAM_DISP_ARB_REQ_BIT	       		(BIT4)
#define		APU_CMD_FIFO_SRAM_RD_REORDER_ARB_REQ_BIT        (BIT5)

#define		APU_CMD_FIFO_SRAM_FW_ARB_ACK_SHIFT        		(8)
#define		APU_CMD_FIFO_SRAM_FW_ARB_ACK_BIT	        	(BIT0)
#define		APU_CMD_FIFO_SRAM_RDMGR_ARB_ACK_BIT	       		(BIT1)
#define		APU_CMD_FIFO_SRAM_WRMGR_ARB_ACK_BIT	       		(BIT2)
#define		APU_CMD_FIFO_SRAM_ASYNC_DB_ARB_ACK_BIT	       	(BIT3)
#define		APU_CMD_FIFO_SRAM_DISP_ARB_ACK_BIT	       		(BIT4)
#define		APU_CMD_FIFO_SRAM_RD_REORDER_ARB_ACK_BIT        (BIT5)

#define	R8_APU_CMD_FIFO_REORDER_STATE						(0x582)
#define		APU_IDLE_STATE_BIT								(BIT0)
#define		APU_SCAN_STATE_BIT								(BIT1)
#define		APU_REORDER_FIFO_PUSH_STATE_BIT					(BIT2)
#define		APU_SYNC_Q_PUSH_STATE_BIT						(BIT3)
#define		APU_SCAN_TIMEOUT_STATE_BIT						(BIT4)
#define		APU_CMD_FIFO_REORDER_STATE_SHIFT				(0)
#define		APU_CMD_FIFO_REORDER_STATE_MASK					(BIT_MASK(5))
*/

#define	R16_APU_CMD_FIFO_ASYNC_Q_CNT						(0x584 >> 1)
#define		APU_CMD_FIFO_ASYNC_Q_CNT_SHIFT					(0)
#define		APU_CMD_FIFO_ASYNC_Q_CNT_MASK					(BIT_MASK(10))

#define	R16_APU_CMD_FIFO_WR_CMD_Q_CNT						(0x588 >> 1)
#define		APU_CMD_FIFO_WR_CMD_Q_CNT_SHIFT					(0)
#define		APU_CMD_FIFO_WR_CMD_Q_CNT_MASK					(BIT_MASK(10))

#define	R16_APU_CMD_FIFO_RD_Q_CNT							(0x58C >> 1)//- Unknown read command count in CMD_FIFO.
#define		APU_CMD_FIFO_RD_Q_CNT_SHIFT						(0)
#define		APU_CMD_FIFO_RD_Q_CNT_MASK						(BIT_MASK(10))

#define	R8_APU_CMD_FIFO_VLD									(0x590)//- Indicate the command fifo valid. N=0~511
/* not use
#define	R32_APU_CMD_FIFO_VLD_0								(0x590 >> 2)
#define	R32_APU_CMD_FIFO_VLD_32								(0x594 >> 2)
#define	R32_APU_CMD_FIFO_VLD_64								(0x598 >> 2)
#define	R32_APU_CMD_FIFO_VLD_96								(0x59C >> 2)
*/

//// SYP

//Maybe gives each register a particular address is better? Like R16_APU_REGx_ADDR = (0x5D0 + 0x10 * x)
#define	R64_APU_CMD_FIFO_INDX_ADDR							(0x5D0 >> 3)
#define	R64_APU_CMD_FIFO_VLD								(0x5D0 >> 3)
/* not use
#define		APU_CMD_FIFO_VALID_Q_RD_ADDR_SHIFT				(0)
#define		APU_CMD_FIFO_VALID_Q_RD_ADDR_MASK				(BIT_MASK(9))
#define		APU_CMD_FIFO_VALID_Q_WR_ADDR_SHIFT				(16)
#define		APU_CMD_FIFO_VALID_Q_WR_ADDR_MASK				(BIT_MASK(9))
*/
#define		APU_CMD_FIFO_RD_CMD_Q_RD_ADDR_SHIFT				(32)
#define		APU_CMD_FIFO_RD_CMD_Q_RD_ADDR_MASK				(BIT_MASK(9))
/* not use
#define		APU_CMD_FIFO_RD_CMD_Q_WR_ADDR_SHIFT				(48)
#define		APU_CMD_FIFO_RD_CMD_Q_WR_ADDR_MASK				(BIT_MASK(9))
*/

#define R64_APU_CMD_FIFO_INDX_ADDR_H                        (0x5D8 >> 3)
#define		APU_CMD_FIFO_WR_CMD_Q_RD_ADDR_SHIFT				(0)
#define		APU_CMD_FIFO_WR_CMD_Q_RD_ADDR_MASK				(BIT_MASK(9))
/* not use
#define		APU_CMD_FIFO_WR_CMD_Q_WR_ADDR_SHIFT				(16)
#define		APU_CMD_FIFO_WR_CMD_Q_WR_ADDR_MASK				(BIT_MASK(9))
#define		APU_CMD_FIFO_ASYNS_CMD_Q_RD_ADDR_SHIFT			(32)
#define		APU_CMD_FIFO_ASYNS_CMD_Q_RD_ADDR_MASK			(BIT_MASK(9))
#define		APU_CMD_FIFO_ASYNS_CMD_Q_WR_ADDR_SHIFT			(48)
#define		APU_CMD_FIFO_ASYNS_CMD_Q_WR_ADDR_MASK			(BIT_MASK(9))

#define	R16_APU_CMD_FIFO_SRAM_ERR_ADDR						(0x5E0 >> 1)//- Indicate cmd fifo Idx SRAM read error address. Only valid when APU_CMD_FIFO_IDX_SRAM_ERR_INT asserted.
#define		APU_CMD_FIFO_IDX_SRAM_ERR_ADDR_SHIFT			(0)
#define		APU_CMD_FIFO_IDX_SRAM_ERR_ADDR_MASK				(BIT_MASK(9))

#define	R16_APU_CMD_FIFO_INFO_SRAM_ERR_ADDR					(0x5E2 >> 1)//- Indicate Cmd fifo info SRAM read error address. Only valid when APU_CMD_FIFO_SRAM_ERR_INT asserted.
#define		APU_CMD_FIFO_INFO_SRAM_ERR_ADDR_SHIFT			(0)
#define		APU_CMD_FIFO_INFO_SRAM_ERR_ADDR_MASK			(BIT_MASK(9))

#define	R16_APU_CMD_FIFO_SCAN_VLD_CNT						(0x5E4 >> 1)
#define		APU_CMD_FIFO_SCAN_VLD_CNT_SHIFT					(0)
#define		APU_CMD_FIFO_SCAN_VLD_CNT_MASK					(BIT_MASK(10))

#define	R16_APU_CMD_FIFO_CMP_BASE_IDX						(0x5E6 >> 1)
#define		APU_CMD_FIFO_CMP_BASE_IDX_SHIFT					(0)
#define		APU_CMD_FIFO_CMP_BASE_IDX_MASK					(BIT_MASK(9))

#define	R32_APU_CMD_FIFO_REORDER_FIFO_TIMER					(0x5E8)
#define		APU_CMD_FIFO_REORDER_FIFO_TIMEOUT_TIMER_SHIFT	(0)
#define		APU_CMD_FIFO_REORDER_FIFO_TIMEOUT_TIMER_MASK	(BIT_MASK(16))
#define		APU_CMD_FIFO_REORDER_FIFO_SCAM_TIMER_SHIFT		(16)
#define		APU_CMD_FIFO_REORDER_FIFO_SCAM_TIMER_MASK		(BIT_MASK(16))
*/
/*========================================== CMD_FIFO SRAM ==========================================*/

#define	R8_APU_CMD_FIFO											(0x5000)
#define	R16_APU_CMD_FIFO										(0x5000 >> 1)//- APU command fifo. Offset: 5000h+N*10h (N=0~511).
#define APU_NVME_CMD_SIZE_IN_B                                                   (0x4) // x << 0x4 = 0x10
/* not use
#define	R32_APU_CMD_FIFO_N_0								(0x5000 >> 2)
#define	R32_APU_CMD_FIFO_N_32								(0x5004 >> 2)
#define	R32_APU_CMD_FIFO_N_64								(0x5008 >> 2)
#define	R32_APU_CMD_FIFO_N_96								(0x500C >> 2)
*/

#define	R8_APU_CMD_FIFO_IDX									(0x7000)//- APU command fifo index. Offset: 7000h+N*8h (N=0~511).
#define	R16_APU_CMD_FIFO_IDX									(0x7000 >> 1)//- APU command fifo index. Offset: 7000h+N*8h (N=0~511).
#define	R64_APU_CMD_FIFO_IDX_N								(0x7000 >> 3)//- Due to format change, each entry is 8 Byte.

/*========================================== RD_MGR Register ==========================================*/

#define	R16_APU_RD_MGR_CTRL									(0x600 >> 1)
#define		APU_RD_MGR_PB_FULL_CHK_EN_BIT					(BIT0)//- This bit is set to enable to check GET_PBNA return not full.
#define		APU_RD_MGR_SRCH_DISABLE_BIT						(BIT1)//- This bit is set to disable the read auto search function.
#define		APU_RD_MGR_RRC_ERR_INS_HOST_PAR_BIT				(BIT2)//- FW set to one, apu will poison parity to Host whenever rrc retry fail.
#define		APU_RD_MGR_IOT_INORDER_EN_BIT					(BIT3)//- This bit is set to force the IOT to be executed in-order.
#define		APU_RD_MGR_UNZIP_EN_BIT							(BIT4)//- This bit is set to enable the AES IP to be decrypted in read data path.
#define		APU_RD_MGR_UNAES_EN_BIT							(BIT5)//- This bit is set to enable the DZIP IP to be unzipped in the read data path.
#define		APU_RD_MGR_IOT_INTLEV_EN_BIT					(BIT6)//- This bit is set to enable the command content interleave for IOT and FW shall set one in UFS to speed up and clear it in SATA.
#define		APU_RD_MGR_IOT_GRPCHK_EN_BIT					(BIT7)//- This bit is set to enable the group check for IOT in SATA.
#define		APU_RD_MGR_UFS_IN_ORDER_EN_BIT					(BIT8)//- This bit is set to enable the in-order in UFS.

// FTLAdd
#define		APU_RD_MGR_NVME_IN_ORDER_EN_BIT					(BIT9)//- This bit is set to enable the in-order in NVME
#define		APU_RD_MGR_RCQ_DISABLE_BIT						(BIT10)//- Disable RCQ, apu will not auto search nor sending RCQ to FW.
#define		APU_RD_MGR_RCQ_INFO_VLD_MASK_BIT				(BIT11)//- This bit is used to set ignore any pca info from cop1 (include pca crc insert and auto repl zero). Only for debug.
#define		APU_RD_MGR_DB_CQ6_RST_BIT						(BIT12)//- Reset RCQ doorbell pointer. FW write 1 to reset and HW will return to 0.
#define		APU_RD_MGR_HW_AUTO_ZERO_EN_BIT					(BIT13)
#define		APU_RD_MGR_RD_ERR_KEEP_BUF_EN_BIT				(BIT14)
#define		APU_RD_MGR_E3D_CHK_EN_BIT						(BIT15)

#define	R16_APU_RD_MGR_SRCH_CTRL							(0x602 >> 1)
#define	R8_APU_RD_MGR_SRCH_CTRL								(0x602)//- 602h~603h
#define		APU_RD_MGR_SRCH_COP0_SHIFT						(0)
#define		APU_RD_MGR_SRCH_COP0_MASK						(BIT_MASK(2))
#define		APU_RD_MGR_SRCH_BMS_SHIFT						(8)
#define		APU_RD_MGR_SRCH_BMS_MASK						(BIT_MASK(4))

#define	R8_APU_RD_MGR_CLR									(0x604)
#define		APU_RD_MGR_USER_RD_SET_INIT_BIT					(BIT0)//- 1T pulse, set APU_RD_MGR_MIS_RD_CUR_TAIL (local pointer) same to MISSR_LB_TAIL(from bmu).
#define		APU_RD_MGR_PB_FULL_CHK_CLR_BIT					(BIT1)//- 1T pulse, get_pbna return not full clear.
#define		APU_RD_MGR_CACHE_CLR_BIT						(BIT2)//- 1T pulse, FW write 1 to clear read cache lock for pre-read hit same LCA flow.

#define	R16_APU_RD_MGR_COP1_SRCH_CNT_THR					(0x606 >> 1)
#define		APU_RD_MGR_COP1_SRCH_CNT_THR_SHIFT				(0)
#define		APU_RD_MGR_COP1_SRCH_CNT_THR_MASK				(BIT_MASK(9))

#define	R8_APU_RD_MGR_LOCK									(0x608)
#define		APU_USER_RD_BMU_LOCK_BIT						(BIT0)//- Lock rdmgr request miss read buffer from bmu.
#define		APU_F_DMA_RD_LOCK_BIT							(BIT1)//- Read command stop to send to DMA.
#define		APU_RD_SEARCH_LOCK_BIT							(BIT2)//- Read command search lock.
#define		APU_F_DMA_RD_LOCK_F_BIT							(BIT3)//- Read command stop to send to DMA.

#define R8_APU_RD_MGR_CTRL									(0x609)
#define 	APU_RD_IOT_QD1_SKIP_SWCH_BIT					(BIT0)// - skip Switch state in iot state machine operation if only one cmd valid in iot.

#define	R8_APU_RD_MGR_ERR_STATUS							(0x60A)
#define		APU_RD_MGR_PCA_CRC_ERR_BIT						(BIT0)//- Indicate the e3d4k mismatch bmu info and FW writes 1 to clear this bit.
#define		APU_RD_MGR_E3D4K_CRC_ERR_BIT					(BIT1)//- Indicate the pca crc mismatch bmu info and FW writes 1 to clear this bit.
#define		APU_RD_MGR_AXI_RSP_ERR_BIT						(BIT2)

#define	R8_APU_RD_MGR_ZINFO_SV_NOT_MATCH					(0x60B)
#define		APU_RD_MGR_ZINFO_SV_NOT_MATCH_BIT				(BIT0)//- FW write 1 to clear. no isr, only record and let sv=0 to host.

#define	R8_APU_RD_MGR_USRRD_LB_ID							(0x60C)
#define		APU_RD_MGR_USRRD_LB_ID_SHIFT					(0)
#define		APU_RD_MGR_USRRD_LB_ID_MASK						(BIT_MASK(3))//-  lock rdmgr request miss rd buffer from bmu.

#define	R16_APU_RD_MGR_USRRD_LB_TAIL						(0x60E >> 1)
#define		APU_RD_MGR_USRRD_LB_TAIL_SHIFT					(0)
#define		APU_RD_MGR_USRRD_LB_TAIL_MASK					(BIT_MASK(10))//-  lock rdmgr request miss rd buffer from bmu.

#define	R8_APU_RD_MGR_BARRIER_REQ							(0x610)
#define		APU_RD_MGR_BARRIER_REQ_BIT						(BIT0)//- FW request apu send barrier cmd to cop1, FW set 1 to trigger apu and HW clear when barrier cmd sent.

#define	R16_APU_RD_MGR_FWRCQ_OFFSET_END						(0x612 >> 1)  // HW Init value: 0x1FE (511)
#define		APU_RD_MGR_FWRCQ_OFSET_END_SHIFT				(0)
#define		APU_RD_MGR_FWRCQ_OFSET_END_MASK					(BIT_MASK(9))

/* not use
#define	R8_APU_RD_MGR_E3D_ERR_INJ							(0x614)
#define		APU_RD_MGR_E3D_ERR_INJ_BIT						(BIT0)//- FW set one to inject e3d err, HW clear when inject done.

#define	R8_APU_RD_MGR_RRC_PAR_ERR_INJ						(0x616)
#define		APU_RD_MGR_RRC_PAR_ERR_INJ_BIT					(BIT0)//- FW set one to inject parity err to rrc module, HW clear when inject done.

#define	R8_APU_RD_MGR_HOST_PAR_ERR_INJ						(0x618)
#define		APU_RD_MGR_HOST_PAR_ERR_INJ_BIT					(BIT0)//- FW inject parity err to Host, HW clear when inject done.

#define	R8_APU_RD_MGR_PCA_SRAM_PAR_ERR_INJ					(0x61A)
#define		APU_RD_MGR_PCA_SRAM_PAR_ERR_INJ_BIT				(BIT0)//- This bit is used to command FW to inject the parity error to the PCA SRAM and HW clears this bit when the inject process is done.

#define	R16_APU_RD_MGR_USRRD_LB_SIZE						(0x61C >> 1)
#define		APU_RD_MGR_USRRD_LB_SIZE_SHIFT					(0)
#define		APU_RD_MGR_USRRD_LB_SIZE_MASK					(BIT_MASK(11))

#define	R8_APU_RD_MGR_RRC_PAR_ERR_CNUM						(0x620)
#define		APU_RD_MGR_RRC_PAR_ERR_CNUM_SHIFT				(0)
#define		APU_RD_MGR_RRC_PAR_ERR_CNUM_MASK				(BIT_MASK(4))
*/
#define	R16_APU_RD_MGR_USRRD_REMAIN_4K_CNT					(0x628 >> 1)
#define	R32_APU_RD_MGR_REMAIN_4K_CNT					(0x628 >> 2)
#define		APU_RD_MGR_USRRD_REMAIN_4K_CNT_SHIFT			(0)
#define		APU_RD_MGR_USRRD_REMAIN_4K_CNT_MASK				(BIT_MASK(16))
#define		APU_RD_MGR_FWRD_REMAIN_4K_CNT_SHIFT			(16)
#define		APU_RD_MGR_FWRD_REMAIN_4K_CNT_MASK				(BIT_MASK(16))

/* not use
#define	R16_APU_RD_MGR_USRRD_TOTAL_4K_CNT					(0x62C >> 1)
#define		APU_RD_MGR_USRRD_TOTAL_4K_CNT_SHIFT				(0)
#define		APU_RD_MGR_USRRD_TOTAL_4K_CNT_MASK				(BIT_MASK(16))

#define	R16_APU_RD_MGR_FWRD_TOTAL_4K_CNT					(0x62E >> 1)
#define		APU_RD_MGR_FWRD_TOTAL_4K_CNT_SHIFT				(0)
#define		APU_RD_MGR_FWRD_TOTAL_4K_CNT_MASK				(BIT_MASK(16))
*/

#define	R32_APU_RD_MGR_STATE								(0x630 >> 2)
#define		APU_RD_MGR_RD_CMD_STATE_SHIFT					(0)
#define		APU_RD_MGR_RD_CMD_STATE_MASK					(BIT_MASK(3))
#define		FWRD_REMAIN_4K_CNT_16_BIT						(BIT4)
#define		APU_RD_MGR_RD_CMD_IOT_STATE_SHIFT				(8)
#define		APU_RD_MGR_RD_CMD_IOT_STATE_MASK				(BIT_MASK(3))
/* not use
#define		FWRD_TOTAL_4K_CNT_16_BIT						(BIT12)
#define		APU_RD_MGR_BMU_PEND_CNT_SHIFT					(16)
#define		APU_RD_MGR_BMU_PEND_CNT_MASK					(BIT_MASK(2))
#define		APU_RD_MGR_BMU_VLD_CNT_SHIFT                    (18)
#define		APU_RD_MGR_BMU_VLD_CNT_MASK                     (BIT_MASK(2))
*/
#define		APU_RD_MGR_FREE_CNT_EMP_BIT						(BIT23)
#define		APU_RD_MGR_ARB_REQ_SHIFT						(24)
#define		APU_RD_MGR_ARB_REQ_MASK							(BIT_MASK(3))
#define		APU_RD_MGR_ARB_ACK_SHIFT						(28)
#define		APU_RD_MGR_ARB_ACK_MASK							(BIT_MASK(3))
/* not use
#define		RD_MGR_MIS_STATE_IDLE							(0)//- Idle.
#define		RD_MGR_MIS_STATE_SEND							(1)//- Send get pbna request to bmu due to bmu ptr update.

#define	R32_APU_RD_MGR_USER_RD_LCA							(0x640 >> 2) //- LCA of cur miss read cmd.

#define	R16_APU_RD_MGR_USER_RD_CTAG							(0x648 >> 1)      //- CTAG of cur miss read cmd.
#define	R16_APU_RD_MGR_FW_RD_CTAG							(0x64A >> 1)      //- CTAG of cur miss read cmd.

#define	R8_APU_RD_MGR_USER_RD_NSID							(0x64E)      //- NSID of cur miss read cmd.
#define		APU_RD_MGR_USER_RD_NSID_SHIFT					(0)
#define		APU_RD_MGR_USER_RD_NSID_MASK					(BIT_MASK(3))

#define	R16_APU_RD_MGR_USER_RD_CUR_TAIL						(0x64C >> 1)      //- next tail address(LB offset) query to bmu(after query success, cur tail increases)
#define		APU_RD_MGR_USER_RD_CUR_TAIL_SHIFT				(0)
#define		APU_RD_MGR_USER_RD_CUR_TAIL_MASK				(BIT_MASK(10))
*/
#define	R8_APU_RD_MGR_SRCH_STATE							(0x650)      //-	Read mgr search state.
#define		APU_RD_MGR_SRCH_STATE_SHIFT						(0)
#define		APU_RD_MGR_SRCH_STATE_MASK						(BIT_MASK(2))
/* not use
#define	R8_APU_RD_MGR_DMA_STATE								(0x654)      //-	Read dma search state.
#define		APU_RD_MGR_DDMA_STATE_SHIFT						(0)
#define		APU_RD_MGR_DDMA_STATE_MASK						(BIT_MASK(3))
#define		APU_RD_MGR_ADMA_STATE_SHIFT						(4)
#define		APU_RD_MGR_ADMA_STATE_MASK						(BIT_MASK(2))
*/
#define	R16_APU_RD_MGR_COP1_SRCH_PEND_CNT					(0x658 >> 1)//- Indicate the pending count of search cmd, which have been send to COP1 but not get the response yet.
/* not use
#define	R8_APU_RD_MGR_PCA_SRAM_PAR_ERR_ADDR					(0x65C)
#define		APU_RD_MGR_PCA_SRAM_PAR_ERR_ADDR_SHIFT			(0)
#define		APU_RD_MGR_PCA_SRAM_PAR_ERR_ADDR_MASK			(BIT_MASK(9))

#define	R16_APU_RD_MGR_CMD_DELAY_CNT						(0x660 >> 1)

#define	R64_APU_RD_MGR_LP_CRC_ERR							(0x668 >> 3)

#define	R32_APU_RD_MGR_LP_CRC_ERR_LCA						(0x668 >> 2)
//- This field is used to indicate the lp crc err and the error lca of FW insert. (When the lp crc error is occurred only by COP1 search result, this field also records COP1 result).
#define	R32_APU_RD_MGR_LP_CRC_ERR_PCA						(0x66C >> 2)

#define R16_APU_RD_MGR_LP_CPC_ERR_CTAG                      (0x670 >> 1)
#define     APU_RD_MGR_LP_CRC_ERR_CTAG_SHIFT                (0)
#define     APU_RD_MGR_LP_CRC_ERR_CTAG_MASK                 (BIT_MASK(9))
*/
/*========================================== RD_MGR PCA_CRC SRAM Register ==========================================*/
/* not use
#define	APU_RD_MGR_PCA_CRC_MGR_SRAM_DATA_OFFSET				(0x8000)
#define	APU_RD_MGR_PCA_CRC_MGR_SRAM_DATA_SIZE				(0x20)
#define	R64_APU_RD_MGR_PCA_CRC_MGR_SRAM_DATA				((volatile U64 (*)[APU_RD_MGR_PCA_CRC_MGR_SRAM_DATA_SIZE >> 3])(APU_REG_ADDRESS + APU_RD_MGR_PCA_CRC_MGR_SRAM_DATA_OFFSET))//- Indicate the rd_mgr pca crc sram data. N = 0 ~ 79.
#define	R64_APU_RD_MGR_PCA_CRC_MGR_SRAM_DATA_0_OFFSET		(0x0000 >> 3)
#define		APU_RD_MGR_PCA_CRC_MGR_SRAM_DATA_0_LCA_SHIFT	(0)
#define		APU_RD_MGR_PCA_CRC_MGR_SRAM_DATA_0_LCA_MASK		(BIT_MASK(32))
#define		APU_RD_MGR_PCA_CRC_MGR_SRAM_DATA_0_CTAG_SHIFT	(32)
#define		APU_RD_MGR_PCA_CRC_MGR_SRAM_DATA_0_CTAG_MASK	(BIT_MASK(9))
#define		APU_RD_MGR_PCA_CRC_MGR_SRAM_DATA_0_CRC_SHIFT	(41)
#define		APU_RD_MGR_PCA_CRC_MGR_SRAM_DATA_0_CRC_MASK		(BIT_MASK(4))
#define	R64_APU_RD_MGR_PCA_CRC_MGR_SRAM_DATA_1_OFFSET		(0x0008 >> 3)
#define		APU_RD_MGR_PCA_CRC_MGR_SRAM_DATA_1_LCA_SHIFT	(0)
#define		APU_RD_MGR_PCA_CRC_MGR_SRAM_DATA_1_LCA_MASK		(BIT_MASK(32))
#define		APU_RD_MGR_PCA_CRC_MGR_SRAM_DATA_1_CTAG_SHIFT	(32)
#define		APU_RD_MGR_PCA_CRC_MGR_SRAM_DATA_1_CTAG_MASK	(BIT_MASK(9))
#define		APU_RD_MGR_PCA_CRC_MGR_SRAM_DATA_1_CRC_SHIFT	(41)
#define		APU_RD_MGR_PCA_CRC_MGR_SRAM_DATA_1_CRC_MASK		(BIT_MASK(4))
#define	R64_APU_RD_MGR_PCA_CRC_MGR_SRAM_DATA_2_OFFSET		(0x0010 >> 3)
#define		APU_RD_MGR_PCA_CRC_MGR_SRAM_DATA_2_LCA_SHIFT	(0)
#define		APU_RD_MGR_PCA_CRC_MGR_SRAM_DATA_2_LCA_MASK		(BIT_MASK(32))
#define		APU_RD_MGR_PCA_CRC_MGR_SRAM_DATA_2_CTAG_SHIFT	(32)
#define		APU_RD_MGR_PCA_CRC_MGR_SRAM_DATA_2_CTAG_MASK	(BIT_MASK(9))
#define		APU_RD_MGR_PCA_CRC_MGR_SRAM_DATA_2_CRC_SHIFT	(41)
#define		APU_RD_MGR_PCA_CRC_MGR_SRAM_DATA_2_CRC_MASK		(BIT_MASK(4))
#define	R64_APU_RD_MGR_PCA_CRC_MGR_SRAM_DATA_3_OFFSET		(0x0018 >> 3)
#define		APU_RD_MGR_PCA_CRC_MGR_SRAM_DATA_3_LCA_SHIFT	(0)
#define		APU_RD_MGR_PCA_CRC_MGR_SRAM_DATA_3_LCA_MASK		(BIT_MASK(32))
#define		APU_RD_MGR_PCA_CRC_MGR_SRAM_DATA_3_CTAG_SHIFT	(32)
#define		APU_RD_MGR_PCA_CRC_MGR_SRAM_DATA_3_CTAG_MASK	(BIT_MASK(9))
#define		APU_RD_MGR_PCA_CRC_MGR_SRAM_DATA_3_CRC_SHIFT	(41)
#define		APU_RD_MGR_PCA_CRC_MGR_SRAM_DATA_3_CRC_MASK		(BIT_MASK(4))

#define	R16_APU_RD_MGR_PCA_CRC_MGR_SRAM_WR_CTRL				(0x1C00 >> 1)
#define		APU_RD_MGR_PCA_CRC_SRAM_WR_REQ_BIT				(BIT0)//- This bit is used to request a write to rd_mgr pca crc sram; FW sets 1 to trigger this bit and HW will clear this bit after transmission done.
#define		APU_RD_MGR_PCA_CRC_SRAM_WR_ADDR_SHIFT			(8)
#define		APU_RD_MGR_PCA_CRC_SRAM_WR_ADDR_MASK			(BIT_MASK(7))

#define	APU_RD_MGR_PCA_CRC_SRAM_WR_DATA_OFFSET				(0x1C10)
#define	R8_APU_RD_MGR_PCA_CRC_SRAM_WR_DATA					((volatile U8 *)(APU_REG_ADDRESS + APU_RD_MGR_PCA_CRC_SRAM_WR_DATA_OFFSET))//- Control the write data to rd_mgr pca crc sram. Bit[159:0].

#define	APU_RD_MGR_PCA_CRC_SRAM_RD_DATA_OFFSET				(0x1C30)
#define	R8_APU_RD_MGR_PCA_CRC_SRAM_RD_DATA					((volatile U8 *)(APU_REG_ADDRESS + APU_RD_MGR_PCA_CRC_SRAM_RD_DATA_OFFSET))//- This register is the read data of rd_mgr pca crc sram in address. BIT[159:0].

#define	APU_RD_MGR_PCA_CRC_SRAM_TAB_VLD_OFFSET				(0x1C50)
#define	R8_APU_RD_MGR_PCA_CRC_SRAM_TAB_VLD					((volatile U8 *)(APU_REG_ADDRESS + APU_RD_MGR_PCA_CRC_SRAM_TAB_VLD_OFFSET))//- This register is the valid of rd_mgr pca crc sram table

#define	R16_APU_RD_MGR_PCA_CRC_INSERT_DELETE_REQ			(0x1C80 >> 1)
#define		APU_RD_MGR_PCA_CRC_INSERT_REQ_BIT				(BIT0)//- This register is used to send insert pca crc SRAM request, and HW clear this bit when insert done.
#define		APU_RD_MGR_PCA_CRC_DELETE_REQ_BIT				(BIT8)//- This register is used to send delete pca crc SRAM request, and HW clear this bit when insert done.

#define	R32_APU_RD_MGR_PCA_CRC_INSERT_DELETE_INFO			(0x1C90 >> 2)//- 0x1C90 ~ 1C98: This field is used to control the insert or delete data to pca crc SRAM.
//- APU_RD_MGR_PCA_CRC_INSERT_REQ assert: APU_RD_MGR_PCA_CRC_INSERT_DELETE_INFO for insert pca crc SRAM request : ctag[8:0], PCA[31:0], LCA[31:0]
//- APU_RD_MGR_PCA_CRC_DELETE_REQ assert: APU_RD_MGR_PCA_CRC_INSERT_DELETE_INFO for delete pca crc SRAM request : ctag[8:0], 32'h0, LCA[31:0]
*/
/*========================================== WR_MGR Register ==========================================*/

#define	R32_APU_WR_MGR_CTRL									(0x680 >> 2)
#define		APU_WR_MGR_ACPL_EN_BIT							(BIT0)//- Auto completion enable.
#define		APU_WR_MGR_ACPL_FUA_BIT							(BIT1)//- This bit is to set auto completion enable of FUA wr.
#define		APU_F_DMA_WR_LOCK_BIT							(BIT2)//- Write command stop to send to DMA.
#define		APU_WR_MGR_LBID0_ZIP_EN_BIT						(BIT3)//- Enable DZIP IP to zip in wrtie data path. 
#define		APU_WR_MGR_CTS_EN_BIT							(BIT4)//- Enable CTS in wrtie data path.
#define		APU_WR_MGR_LBID0_AES_EN_BIT						(BIT5)//- Enable AES IP to encrypt in wrtie data path.
#define		APU_WR_MGR_PAR_ERR_INS_E3D_EN_BIT				(BIT6)//- FW set one to make apu poison e3d whenever SRAM parity err occurs. 
#define		APU_WR_MGR_ALLC_QOP_BIT							(BIT7)//- This bit is set to enable the QOP value when send allocate command to BMU.
#define		APU_WR_MGR_WR_LBID0_SHIFT						(8)
#define		APU_WR_MGR_WR_LBID0_MASK						(BIT_MASK(3))
#define		APU_WR_MGR_LBID1_ZIP_EN_BIT						(BIT11)//- 
#define		APU_WR_MGR_WR_LBID1_SHIFT						(12)
#define		APU_WR_MGR_WR_LBID1_MASK						(BIT_MASK(3))
#define		APU_WR_MGR_LBID1_AES_EN_BIT						(BIT15)
#define 	APU_WR_MGR_WR_LBID1_AES_EN_SHIFT						(15)// -	This bit is set to enable the AES IP to encrypt in the write data path in LBID1.
#define 	APU_WR_MGR_WR_LBID1_AES_EN_MASK					             (BIT_MASK(1)) // -	This bit is set to enable the AES IP to encrypt in the write data path in LBID1.
#define		APU_WR_MGR_PRERD_LCA_CHK_SET_BIT				(BIT16)//- FW set this field to enable prerd LCA chk function, and HW clears it when wr cache hit the range once. (And HW will assert prerd clr bit in next RCQ)
#define 	APU_WR_MGR_PRERD_LCA_CHK_SET_SHIFT						(16)//-	FW set this field to enable prerd LCA chk function, and HW clears it when wr cache hit the range once. (And HW will assert prerd clr bit in next RCQ)
#define 	APU_WR_MGR_PRERD_LCA_CHK_SET_MASK					             (BIT_MASK(1))//-	FW set this field to enable prerd LCA chk function, and HW clears it when wr cache hit the range once. (And HW will assert prerd clr bit in next RCQ)
#define		APU_WR_MGR_PRERD_SKIP_SRCH_EN_BIT				(BIT17)
#define		APU_WR_MGR_PRERD_SKIP_SRCH_EN_SHIFT				(17)//- FW set this field to enable the function that when rd 4K entry hits pre rd range, skipping auto search. The pre rd range is valid only when APU_WR_MGR_PRERD_LCA_CHK_SET asserts.
#define		APU_WR_MGR_PRERD_SKIP_SRCH_EN_MASK				(BIT_MASK(1))
#define		APU_WR_MGR_WR_CMD_VLDT_OPT_SHIFT				(20)
#define		APU_WR_MGR_WR_CMD_VLDT_OPT_MASK					(BIT_MASK(2))
#define		APU_WR_LBID_SEL_EN_BIT							(BIT24)//- FW set this field to enable multi-WLB, LBID0 and LBID1
#define		APU_WR_LBID_BY_NSID_EN_BIT						(BIT25)//- FW set this field to enable separating LBID0/LBID1 by NSID, only available in NVMe mode
#define		APU_WR_LBID_BY_LCA_EN_BIT						(BIT26)//- FW set this field to enable separating LBID0/LBID1 by LCA, only available in NVMe mode
#define     APU_SET_WR_MGR_WR_LBID(x)                    	(R32_APU[R32_APU_WR_MGR_CTRL] |= (((x) & BITMSK(3,0)) << 8))//- LBID for user write data.

#define	R16_APU_WR_MGR_CACHE_VLD_CLR						(0x684 >> 1)//-	clear wr_cache_valid. FW write 1 to clear wr_cache_valid, HW clear when clear wr_cache_valid done.
#define		APU_WR_MGR_CACHE_VLD_CLR_SHIFT					(0)
#define		APU_WR_MGR_CACHE_VLD_CLR_MASK					(BIT_MASK(9))

#define R16_APU_WR_MGR_CACHE_DMACNT_CLR						(0x686 >> 1)
#define		APU_WR_MGR_CACHE_DMACNT_CLR_MASK				(BIT_MASK(9))

/* not use
#define	R8_APU_WR_MGR_E3D_ERR_INJ							(0x688)//-	FW set one to insert e3d err, HW clear when insert done.
#define		APU_WR_MGR_E3D_ERR_INJ_BIT						(BIT0)//- FW set one to inject e3d err, HW clear when insert done.

#define	R8_APU_WR_MGR_RCC_PAR_ERR_INJ						(0x68A)//-	FW set one to insert parity err, HW clear when insert done.
#define		APU_WR_MGR_RCC_PAR_ERR_INJ_BIT					(BIT0)//- FW set one to inject parity err, HW clear when insert done.
*/

#define	R8_APU_WR_MGR_FW_FLUSH								(0x68C)//-	FW set one to flush, and HW clear when flush done.
#define		APU_WR_MGR_FW_FLUSH_BIT							(BIT0)//- FW set one to flush, and HW clear when flush done.

#define	R32_APU_WR_MGR_STATE								(0x690 >> 2)//- State machine of cur wr cmd.
#define		APU_WR_MGR_STATE_SHIFT							(0)
#define		APU_WR_MGR_STATE_MASK							(BIT_MASK(3))
#define			APU_WR_MGR_STATE_WAIT_CACHE					(2)
#define			APU_WR_MGR_STATE_SEND_PBADDR_TO_HOST		(3)
#define		APU_WR_MGR_FUA_DONE_BIT							(BIT4)//- fua cmd push to write cache done.
#define		APU_WR_MGR_GETPB_STATE_SHIFT					(8)
#define		APU_WR_MGR_GETPB_STATE_MASK						(BIT_MASK(2))
#define		APU_WR_MGR_GETPB_Q_CNT_SHIFT					(12)
#define		APU_WR_MGR_GETPB_Q_CNT_MASK						(BIT_MASK(3))
#define		APU_WR_MGR_FLUSH_STATE_SHIFT					(16)
#define		APU_WR_MGR_FLUSH_STATE_MASK						(BIT_MASK(2))
#define		APU_WR_MGR_FLUSH_Q_CNT_SHIFT					(20)
#define		APU_WR_MGR_FLUSH_Q_CNT_MASK						(BIT_MASK(3))
#define		APU_WR_MGR_VLDT_STATE_SHIFT						(24)
#define		APU_WR_MGR_VLDT_STATE_MASK						(BIT_MASK(2))
#define		APU_WR_MGR_VLDT_Q_CNT_SHIFT						(28)
#define		APU_WR_MGR_VLDT_Q_CNT_MASK						(BIT_MASK(3))

#define	R8_APU_WR_MGR_WR_CMD_ARB							(0x694)
#define		APU_WR_MGR_WR_CMD_ARB_REQ_SHIFT					(0)
#define		APU_WR_MGR_WR_CMD_ARB_REQ_MASK					(BIT_MASK(2))
#define		APU_WR_CMD_ARB_NO_PENDING						(0)
#define		APU_WR_CMD_ARB_USER_CMD_PENDING					(1)//- User data cmd (from cmd_fifo) pending exist now.
#define		APU_WR_CMD_ARB_NON_USER_CMD_PENDING				(2)//- Non-user data cmd (from cmd_fifo) pending exist now.
#define		APU_WR_MGR_WR_CMD_ARB_ACK_SHIFT					(4)
#define		APU_WR_MGR_WR_CMD_ARB_ACK_MASK					(BIT_MASK(2))
#define		APU_WR_CMD_ARB_NO_DOING							(0)
#define		APU_WR_CMD_ARB_USER_CMD_DOING					(1)//- User data cmd (from cmd_fifo) doing now.
#define		APU_WR_CMD_ARB_NON_USER_CMD_DOING				(2)//- Non-user data cmd (from cmd_fifo) doing now.

#define	R8_APU_WR_MGR_ERR_STATUS							(0x698)
#define		APU_WR_MGR_RRC_PAR_ERR_BIT						(BIT0)//- Indicate rrc retry fail occurs, FW write 1 to clear.
#define		APU_WR_MGR_HOST_WB_PAR_ERR_BIT					(BIT1)//- Indicate host write data parity error occurs, FW write 1 to clear.
#define		APU_WR_MGR_AXI_BRESP_ERR_BIT					(BIT2)//- Indicate axi bresp error occurs, FW write 1 to clear.
#define		APU_WR_MGR_HOST_SIGN_ERR_BIT					(BIT3)
#define		APU_WR_MGR_GETPB_ERR_BIT						(BIT4)//- Indicate the getpb cmd or allc PB cmd error occurs and FW writes 1 to clear this bit.

#define	R32_APU_WR_MGR_CUR_LCA								(0x6A0 >> 2)//-    LCA of cur wr cmd.

/* not use
#define R8_APU_WR_MGR_CUR_NSID                              (0x6AA)
#define     APU_WR_MGR_CUR_NSID_SHIFT                       (0)
#define     APU_WR_MGR_CUR_NSID_MASK                        (BIT_MASK(5))
*/

#define	R32_APU_WR_MGR_REMAIN_4K_CNT						(0x6B0 >> 2)//-    remain 4K cnt of cur wr cmd.
#define		APU_WR_MGR_REMAIN_4K_CNT_SHIFT					(0)
#define		APU_WR_MGR_REMAIN_4K_CNT_MASK					(BIT_MASK(17))
#define	R32_APU_WR_MGR_FW_CMD_REMAIN_4K_CNT					(0x6B4 >> 2)
#define		APU_WR_MGR_FW_CMD_REMAIN_4K_CNT_SHIFT			(0)
#define		APU_WR_MGR_FW_CMD_REMAIN_4K_CNT_MASK			(BIT_MASK(17))

/* not use
#define	R32_APU_WR_MGR_TOTAL_4K_CNT							(0x6B8 >> 2)//-    Total 4K cnt of cur wr cmd.
#define		APU_WR_MGR_TOTAL_4K_CNT_SHIFT					(0)
#define		APU_WR_MGR_TOTAL_4K_CNT_MASK					(BIT_MASK(17))
#define	R32_APU_WR_MGR_FW_CMD_TOTAL_4K_CNT					(0x6BC >> 2)
#define		APU_GET_WR_MGR_FW_CMD_TOTAL_4K_CNT_SHIFT		(0)
#define		APU_GET_WR_MGR_FW_CMD_TOTAL_4K_CNT_MASK			(BIT_MASK(17))

#define	R16_APU_WR_MGR_WR_CMD_CTAG							(0x6C0 >> 1)//- CTAG of cur wr cmd.
#define	R16_APU_WR_MGR_FW_CMD_CTAG							(0x6C2 >> 1)
*/

#define	R8_APU_WR_MGR_CACHE_ERR_CNUM						(0x6C8)//-	Indicate the cache number which occurs error.
#define		APU_WR_MGR_CACHE_ERR_CNUM_SHIFT					(0)
#define		APU_WR_MGR_CACHE_ERR_CNUM_MASK					(BIT_MASK(4))

#define	R32_APU_WR_MGR_PRERD_LCA_START						(0x6E0 >> 2)  //- Indicate the Pre rd start LCA range (include) to assert pre rd clr bit in RCQ when wr cache flush.
#define	R32_APU_WR_MGR_PRERD_LCA_TAIL						(0x6E4 >> 2)

#define	R16_APU_WR_MGR_CMD_DELAY_CNT						(0x6E8 >> 1)

#define R8_APU_WR_MGR_CMD_DELAY_SEL							(0x6EA)
#define 	APU_WR_MGR_CMD_DELAY_SEL_BIT					(BIT0)

#define	R32_APU_WR_MGR_CACHE_RETRY							(0x6F0 >> 2)
#define		APU_WR_MGR_WR_CACHE_RETRY_REQ_BIT				(BIT0)//- This bit is the request of write cache flush retry, and HW clear it when retry request done. NOT flush done.
#define		APU_WR_MGR_WR_CACHE_RETRY_CNUM_SHIFT			(1)
#define		APU_WR_MGR_WR_CACHE_RETRY_CNUM_MASK				(BIT_MASK(4))
/* not use
#define		APU_WR_MGR_WR_CACHE_RETRY_VLD_BIT				(BIT5)//- This bit is indicates the cache number configured by FW, is available to retry.
#define		APU_WR_MGR_WR_CACHE_GETPB_CNUM_SHIFT			(8)
#define		APU_WR_MGR_WR_CACHE_GETPB_CNUM_MASK				(BIT_MASK(4))
#define		APU_WR_MGR_WR_CACHE_FLUSH_CNUM_SHIFT			(12)
#define		APU_WR_MGR_WR_CACHE_FLUSH_CNUM_MASK				(BIT_MASK(4))
#define		APU_WR_MGR_WR_CACHE_VLDT_CNUM_SHIFT				(16)
#define		APU_WR_MGR_WR_CACHE_VLDT_CNUM_MASK				(BIT_MASK(4))
*/
/*========================================== Write & Read Manager Cache Register ==========================================*/

#define	APU_CACHE_INFO_WR_OFFSET							(0x1000)
#define	APU_CACHE_INFO_WR_SIZE								(0x10)
#define	R32_APU_CACHE_INFO_WR								((volatile U32 (*)[APU_CACHE_INFO_WR_SIZE >> 2])(APU_REG_ADDRESS + APU_CACHE_INFO_WR_OFFSET)) //- Offset : 1000h + N*10h (N=0 to 8)
#define	R32_APU_CACHE_INFO_WR_ENTRY0_OFFSET					(0x0000 >> 2)
#define		APU_CACHE_INFO_WR_LCA_SHIFT						(0)
#define		APU_CACHE_INFO_WR_LCA_MASK						(BIT_MASK(32))
#define	R32_APU_CACHE_INFO_WR_ENTRY1_OFFSET					(0x0004 >> 2)
#define		APU_CACHE_INFO_WR_SIGNSV_SHIFT					(0)
#define		APU_CACHE_INFO_WR_SIGNSV_MASK					(BIT_MASK(8))
#define		APU_CACHE_INFO_WR_E3D4K_SHIFT					(8)
#define		APU_CACHE_INFO_WR_E3D4K_MASK					(BIT_MASK(24))
#define	R32_APU_CACHE_INFO_WR_ENTRY2_OFFSET					(0x0008 >> 2)
#define		APU_CACHE_INFO_WR_PB_SHIFT						(0)
#define		APU_CACHE_INFO_WR_PB_MASK						(BIT_MASK(10))
#define		APU_CACHE_INFO_WR_DISBMU_BIT					(BIT10)
#define		APU_CACHE_INFO_WR_FWCMD_BIT						(BIT11)
#define		APU_CACHE_INFO_WR_LOCK_BIT						(BIT12)
#define		APU_CACHE_INFO_WR_ZINFO_SHIFT					(13)
#define		APU_CACHE_INFO_WR_ZINFO_MASK					(BIT_MASK(3))
#define		APU_CHCHE_INFO_WR_LB_SHIFT						(16)
#define		APU_CHCHE_INFO_WR_LB_MASK						(BIT_MASK(10))
#define		APU_CACHE_INFO_WR_LBID_SHIFT					(26)
#define		APU_CACHE_INFO_WR_LBID_MASK						(BIT_MASK(3))
#define		APU_CACHE_INFO_WR_DMACNT_SHIFT					(29)
#define		APU_CACHE_INFO_WR_DMACNT_MASK					(BIT_MASK(3))
#define	R32_APU_CACHE_INFO_WR_ENTRY3_OFFSET					(0x000C >> 2)
#define		APU_CACHE_INFO_WR_CTAG_SHIFT					(0)
#define		APU_CACHE_INFO_WR_CTAG_MASK						(BIT_MASK(9))
#define		APU_CACHE_INFO_WR_CHKSV_SHIFT					(9)
#define		APU_CACHE_INFO_WR_CHKSV_MASK					(BIT_MASK(8))
#define		APU_CACHE_INFO_WR_BCNT_SHIFT					(17)
#define		APU_CACHE_INFO_WR_BCNT_MASK						(BIT_MASK(3))
#define		APU_CACHE_INFO_WR_AXI_DONE_BIT					(BIT20)
#define		APU_CACHE_INFO_WR_VLDT_BIT						(BIT21)
#define		APU_CACHE_INFO_WR_KICK_BIT						(BIT22)
#define		APU_CACHE_INFO_WR_CMDEND_BIT					(BIT23)
#define		APU_CACHE_INFO_WR_CMDEND_SHIFT					(23)
#define     APU_CACHE_INFO_WR_DSM_SHIFT                     (24)
#define     APU_CACHE_INFO_WR_DSM_MASK                      (BIT_MASK(3))
#define		APU_CACHE_INFO_WR_FUA_BIT						(BIT27)
#define		APU_CACHE_INFO_WR_ERR_BIT						(BIT28)
#define		APU_CACHE_INFO_WR_PBVLD_BIT						(BIT29)
#define		APU_CACHE_INFO_WR_ZERO_BIT						(BIT30)
#define		APU_CACHE_INFO_WR_ZERO_SHIFT					(30)
#define		APU_CACHE_INFO_WR_VALID_BIT						(BIT31)

#define	APU_CACHE_INFO_RD_OFFSET							(0x1100)
#define	APU_CACHE_INFO_RD_SIZE								(0x10)
#define	R32_APU_CACHE_INFO_RD								((volatile U32 (*)[APU_CACHE_INFO_RD_SIZE >> 2])(APU_REG_ADDRESS + APU_CACHE_INFO_RD_OFFSET)) //- Offset : 1100h + N*10h (N=0 to 8)
#define	R32_APU_CACHE_INFO_RD_ENTRY0_OFFSET					(0x0000 >> 2)
#define		APU_CACHE_INFO_RD_LCA_SHIFT						(0)
#define		APU_CACHE_INFO_RD_LCA_MASK						(BIT_MASK(32))
#define	R32_APU_CACHE_INFO_RD_ENTRY1_OFFSET					(0x0004 >> 2)
#define		APU_CACHE_INFO_RD_SV_SHIFT						(0)
#define		APU_CACHE_INFO_RD_SV_MASK						(BIT_MASK(8))
#define		APU_CACHE_INFO_RD_E3D4K_SHIFT					(8)
#define		APU_CACHE_INFO_RD_E3D4K_MASK					(BIT_MASK(24))
#define	R32_APU_CACHE_INFO_RD_ENTRY2_OFFSET					(0x0008 >> 2)
#define		APU_CACHE_INFO_RD_PB_SHIFT						(0)
#define		APU_CACHE_INFO_RD_PB_MASK						(BIT_MASK(10))
#define		APU_CACHE_INFO_RD_DISBMU_BIT					(BIT10)
#define		APU_CACHE_INFO_RD_FWCMD_BIT						(BIT11)
#define		APU_CACHE_INFO_RD_LOCK_BIT						(BIT12)
#define		APU_CACHE_INFO_RD_ZINFO_SHIFT					(13)
#define		APU_CACHE_INFO_RD_ZINFO_MASK					(BIT_MASK(3))
#define		APU_CACHE_INFO_RD_LB_SHIFT						(16)
#define		APU_CACHE_INFO_RD_LB_MASK						(BIT_MASK(10))
#define		APU_CACHE_INFO_RD_LBID_SHIFT					(26)
#define		APU_CACHE_INFO_RD_LBID_MASK						(BIT_MASK(3))
#define		APU_CACHE_INFO_RD_DMACNT_SHIFT					(29)
#define		APU_CACHE_INFO_RD_DMACNT_MASK					(BIT_MASK(3))
#define	R32_APU_CACHE_INFO_RD_ENTRY3_OFFSET					(0x000C >> 2)
#define		APU_CACHE_INFO_RD_CTAG_SHIFT					(0)
#define		APU_CACHE_INFO_RD_CTAG_MASK						(BIT_MASK(9))
#define     APU_CACHE_INFO_RD_E3DCHK_BIT                    (BIT27)
#define		APU_CACHE_INFO_RD_ERR_SHIFT						(28)
#define		APU_CACHE_INFO_RD_ERR_MASK						(BIT_MASK(2))
#define		APU_CACHE_INFO_RD_ZERO_BIT						(BIT30)
#define		APU_CACHE_INFO_RD_VALID_BIT						(BIT31)

#define	R8_APU_CACHE_INFO_WR_STMID							(0x11F0)

/*========================================== SRAM Manager Register ==========================================*/
/* not use
#define	R32_APU_SRAM_MGR_WR_SRAM_STATE						(0x700 >> 2)
#define		APU_SRAM_MGR_WR_SRAM_REQ_BIT					(BIT0)//- wrcmd_mgr req to use sram
#define		APU_SRAM_MGR_WR_SRAM_ACK_BIT					(BIT1)//- Indicate the SRAM usage grant to wrcmd_mgr
#define		APU_SRAM_MSG_WR_SRAM_ACK_SHIFT					(8)
#define		APU_SRAM_MSG_WR_SRAM_ACK_MASK					(BIT_MASK(9))

#define	R32_APU_SRAM_MGR_RD_SRAM_STATE						(0x704 >> 2)
#define		APU_SRAM_MGR_RD_SRAM_REQ_BIT					(BIT0)//- rdcmd_mgr req to use sram
#define		APU_SRAM_MGR_RD_SRAM_ACK_BIT					(BIT1)//- Indicate the SRAM usage grant to rdcmd_mgr
#define		APU_SRAM_MSG_RD_SRAM_ACK_SHIFT					(8)
#define		APU_SRAM_MSG_RD_SRAM_ACK_MASK					(BIT_MASK(9))

#define	R32_APU_SRAM_MGR_PAR_RRC_FAIL						(0x708 >> 2)
#define		APU_SRAM_MGR_PAR_RRC_FAIL_BIT					(BIT0)//- Indicate the rrc retry fail occurs and FW writes 1 to clear this bit.
#define		APU_SRAM_MGR_PAR_RRC_FAIL_NUM_SHIFT				(8)
#define		APU_SRAM_MGR_PAR_RRC_FAIL_NUM_MASK				(BIT_MASK(4))
#define		APU_SRAM_MGR_PAR_RRC_FAIL_ADDR_SHIFT			(16)
#define		APU_SRAM_MGR_PAR_RRC_FAIL_ADDR_MASK				(BIT_MASK(8))

#define	R32_APU_SRAM_MGR_PAR_RRC_PASS						(0x70C >> 2)
#define		APU_SRAM_MGR_PAR_RRC_PASS_BIT					(BIT0)//- Indicate the rrc retry pass occurs and FW writes 1 to clear this bit.
#define		APU_SRAM_MGR_RRC_RETRY_INJ_BIT					(BIT12)//- This bit is used to make rrc retry pass, and HW clears it when rrc retry passed once.
#define		APU_SRAM_MGR_PAR_RRC_PASS_CNT_SHIFT				(8)
#define		APU_SRAM_MGR_PAR_RRC_PASS_CNT_MASK				(BIT_MASK(2))
#define		APU_SRAM_MGR_PAR_RRC_PASS_NUM_SHIFT				(16)
#define		APU_SRAM_MGR_PAR_RRC_PASS_NUM_MASK				(BIT_MASK(4))
*/
/*========================================== LCA Remap SRAM Register ==========================================*/
//Maybe need more detail define?
#define R8_APU_LR_SRAM                                      (0x3000)//- LCA Remap SRAM Register

#define R16_APU_LR_SRAM                                     (0x3000 >> 1)//- LCA Remap SRAM Register
#define     APU_LR_SRAM_SIZE                                (2*1024)//2048Byte 
#define     APU_LR_SRAM_SHIFT                               (0)
#define     APU_LR_SRAM_MASK                                (BIT_MASK(10))

#define R16_APU_ILR_SRAM                                    (0x3800 >> 1)//- Inverse LCA Remap SRAM Register
#define     APU_ILR_SRAM_SIZE                               (2*1024)//2048Byte 
#define     APU_ILR_SRAM_SHIFT                              (0)
#define     APU_ILR_SRAM_MASK                               (BIT_MASK(15))

#define		APU_AES_RN_OFFSET                               (0x4000)
#define 	APU_AES_RANGE_NUM                               (64)
#define		APU_AES_RN_UNIT_SIZE                            (0x10)
#define     APU_AES_RN_SIZE                                 (APU_AES_RN_UNIT_SIZE*APU_AES_RANGE_MAX_NUM)//1024Byte
#define	R32_APU_AES_RN                                      ((volatile U32 (*)[APU_AES_RN_UNIT_SIZE >> 2])(APU_REG_ADDRESS + APU_AES_RN_OFFSET)) //- Offset : 4000h + N*10h (N=0 to 63)
#define	R32_APU_RN_ENTRY0_OFFSET                            (0x0000 >> 2)
#define     APU_AES_RN_SLCA_SHIFT                           (0)
#define     APU_AES_RN_SLCA_MASK                            (BIT_MASK(30))
#define R32_APU_RN_ENTRY1_OFFSET                            (0x0004 >> 2)
#define     APU_AES_RN_NSID_SHIFT                           (0)
#define     APU_AES_RN_NSID_MASK                            (5)
#define R32_APU_RN_ENTRY2_OFFSET                            (0x0008 >> 2)
#define     APU_AES_RN_ELCA_SHIFT                           (0)
#define     APU_AES_RN_ELCA_MASK                            (BIT_MASK(30))
#define R32_APU_RN_ENTRY3_OFFSET                            (0x000C >> 2)
#define     APU_AES_RN_KI_SHIFT                             (0)
#define     APU_AES_RN_KI_MASK                              (BIT_MASK(7))
#define     APU_AES_RN_RL_BIT                               (BIT8)
#define     APU_AES_RN_WL_BIT                               (BIT9)
#define     APU_AES_RN_GAP_BIT                              (BIT10)//- When this AES Range is not adjacent to next AES Range, set this bit to 1.



#define R64_APU_AES_GLB                                     (0x4400 >> 3)//- AES Global Info Register
#define R64_APU_AES_GLB_OFFSET                              (8 >> 3)
#define     APU_AES_GLB_SIZE                                (8*32)//256Byte
#define     APU_AES_GLB_MBR_ELCA_SHIFT                      (0)
#define     APU_AES_GLB_MBR_ELCA_MASK                       (BIT_MASK64(30))
#define     APU_AES_GLB_KI_SHIFT                            (32)
#define     APU_AES_GLB_KI_MASK                             (BIT_MASK64(7))
#define     APU_AES_GLB_RL_BIT                              (BIT40)
#define     APU_AES_GLB_WL_BIT                              (BIT41)
#define     APU_AES_GLB_MBR_EN_BIT                          (BIT42)//- When this AES Global Range is MBR enable.

#define R16_APU_LR_BASE                                     (0x4500 >> 1)//- LCA Remap Base Register
#define     APU_LR_BASE_SIZE                                (2*32)//64Byte
#define     APU_LR_BASE_SHIFT                               (0)
#define     APU_LR_BASE_MASK                                (BIT_MASK(10))

#define R16_APU_AES_BASE                                    (0x4540 >> 1)//- AES Base Register
#define     APU_AES_BASE_SIZE                               (2*32)//64Byte
#define     APU_AES_BASE_SHIFT                              (0)
#define     APU_AES_BASE_MASK                               (BIT_MASK(6))

#define APU_LCA_REMAP_TOTAL_SZIE                            (APU_LR_SRAM_SIZE + APU_ILR_SRAM_SIZE + APU_AES_RN_SIZE + APU_AES_GLB_SIZE + APU_LR_BASE_SIZE + APU_AES_BASE_SIZE) // E19 reset porting// 2048+2048+1024+256+64+64 = 5504 Byte



/*========================================== WRCMD Manager LBID1 Condition Register ==========================================*/

/* not use
#define	R8_APU_WR_LBID_BY_NSID								(0x730)
#define	R8_APU_WR_LBID_BY_LCA_NSID							(0x738)

#define	R32_APU_WR_LBID_BY_LCA_S							(0x740 >> 2)
#define	R32_APU_WR_LBID_BY_LCA_T							(0x760 >> 2)
*/
/*========================================== END: WRCMD Manager LBID1 Condition Register =====================================*/

/*========================================== TSA LOG Register ======================================*/
/* not use
#define R32_APU_TSA_CTRL                                    (0x18 >> 2)
#define     APU_TSA_EN_FW                                   (BIT0)//- TSA is enabling through eFuse and this bit, both eFuse and this bit are enabling, TSA would be active.
#define     APU_TSA_UNIT_SHIFT                              (8) // (0x19)
#define     APU_TSA_UNIT_MASK                               (BIT_MASK(4))//- TSA log monitor unit, 2^(2 + APU_TSA_UNIT) MB
#define     APU_TSA_FLUSH_REQ                               (BIT16)

#define R32_APU_TSA_TIME_STAMP                              (0x1C >> 2)//- This field indicates current timestamp cout for TSA function.(unit: 1 sec)
#define R32_APU_TSA_NS_HMB_SADDR                            (0x1D00 >> 2)//- TSA Log base HMB start address for NSID N.(This address is 32B unit.)
*/
/*==========================================END: TSA LOG Register ===================================*/

/*========================================== DBG Register ==========================================*/

#define	R32_APU_DBG_PORT_CTRL								(0x780 >> 2)
#define	R8_APU_DBG_PORT_CTRL								(0x780)
#define		APU_DBG_PORT_CTRL_SHIFT							(0)
#define		APU_DBG_PORT_CTRL_MASK							(BIT_MASK(3))

#define	R8_APU_DBG_PORT_SEL									(0x782)
#define		APU_DBG_PORT_SEL_SHIFT							(0)
#define		APU_DBG_PORT_SEL_MASK							(BIT_MASK(8))

#define	R64_APU_DBG_PORT									(0x784 >> 3)
#define	R32_APU_DBG_PORT									(0x784 >> 2)
#define	R32_APU_DBG_PORT_2									(0x788 >> 2)
#define		APU_DBG_PORT_SHIFT								(0)
#define		APU_DBG_PORT_MASK								(BIT_MASK(48))

/*======================================== END: Registers ===========================================*/

struct nvme_cmd {

	union {
		struct rawdata {
			U32 ulDW[4];

		} rawdata;

		struct info {
			U32 CTAG		: 9;
			U32 ubRESV		: 1;
			U32 ubPNV		: 1;
			U32 ubFUA		: 1;
			U32 ubFUSE		: 2;
			U32 ubADM		: 1;
			U32 ubERR		: 1;
			U32 ubOPCode	: 8;
			U32 ubLR		: 1;
			U32 ubATM		: 1;
			U32 ubLPN		: 1;
			U32 ubPNZ		: 1;
			U32 ubNC		: 1;
			U32 ubNSID		: 3;

			union {
				struct user {
					U32 ulSLCA;

					U32 uwCNLC		: 16;
					U32 ubSSV		: 8;
					U32 ubESV		: 8;

					U32 ulTNLC		: 17;
					U32 ubRSV		: 7;
					U32 ubStrId 	: 8;
				} user;

				struct nonuser {
					U32 ulDW10;

					U32 uwRSV		: 16;
					U32 uwDW11_H	: 16;
					U32 uwDW11_L	: 16;
					U32 ulRESV1 	: 14;
					U32 ubTYPE		: 2;  // 2'b01 : IO write command, 2'b10 : IO read command, 2'b11 : IO compare command, 2'b00 : others

				} nonuser;
			};
		} info;
	};
};

union nvme_td {

	U16 uwMSG;

	struct {
		U16 CTAG      : 9;
		U16 ubACPLEN    : 1;
		U16 ubRSVD2     : 1;
		U16 ubERR       : 1;
		U16 ubRSVD3     : 4;
	};
};

//APU command SQ ( FW trigger to HW )
union nvme_wch {

	U32 ulDW[4];

	struct {

		U32 ubCTAG      : 9;
		U32 ubBCMD      : 1;
		U32 ubFT        : 1;
		U32 ubRSV0      : 1;
		U32 ubNBMU      : 1;
		U32 ubCMDEND    : 1;
		U32 ubACPL      : 1;
		U32 ubTYPE      : 1;

		U32 ubRSV2      : 16;

		U32 ulCNLC      : 29;
		U32 ubRSV3      : 3;

		U32 ulADDR      : 16;   //PB_ADDR[9:0](for DIS_BMU = 1)   ,  LB_ADDR[15:0](for DIS_BMU = 0)
		//PB_ADDR : PB addr offset of dbuf, unit : 4K, PB_ADDR[9] shall be 0
		//LB_ADDR: [15:13] = 0, [12:10] = LB ID, [9:0] = LB offset
		U32 uwRSV4      : 16;

		U32 ulTNLC      : 29;
		U32 ubRSV5      : 3;
	};
};

union nvme_cpl {

	U32 ulDW[2];

	struct {
		U32 CTAG      : 9;
		U32 ubRSVD1     : 7;
		U32 ubSC        : 8;    // Status Code in Completion Status Field
		U32 ubSCT       : 3;    // Status Code Type in Completion Status Field
		U32 ubRSVD2     : 2;
		U32 ubM         : 1;    // More
		U32 ubDNR       : 1;    // Do Not Retry
		U32 ubRSVD3     : 1;

		U32 ulCMDSPEC;
	};
};

#endif /*(PS5021_EN)*/
