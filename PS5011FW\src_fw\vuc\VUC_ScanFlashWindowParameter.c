#include "hal/fip/fip.h"
#include "hal/fip/fip_api.h"
#include "hal/pic/uart/uart_api.h"
#include "host/VUC_handler.h"
#include "nvme_api/nvme/shr_hal_nvme.h"
#include "vuc/VUC_command.h"
#include "vuc/VUC_ScanFlashWindowParameter.h"
#include "vuc/VUC_ScanFlashWindowSetting.h"
#include "hal/sys/api/efuc/efuse_api.h"

SdllWindowParameter_t gScanWindow;
ScanWindowParam_t gFipCurWindowParam;

#if (BURNER_MODE_EN)
void VUC_ScanFlashWindowParameter(VUC_OPT_HCMD_PTR_t pCmd)
{
	U8 ubi, ubj, ubk;
	gScanWindow.SubFeature.ubAll = pCmd->vuc_sqcmd.vendor.ScanFlashWindowSetPara.ubSubFeature;
	gScanWindow.uwECCCorrectBitNum = pCmd->vuc_sqcmd.vendor.ScanFlashWindowSetPara.uwECCCorrectBitNum;
	gScanWindow.SLBA1.ulAll = pCmd->vuc_sqcmd.vendor.ScanFlashWindowSetPara.ulSLBA1;
	gScanWindow.SLBA2.ulAll = pCmd->vuc_sqcmd.vendor.ScanFlashWindowSetPara.ulSLBA2;
	/*for EVB flexibility in development not really cut efuse map
		when MP set "Ecc_Correct_Bit_Num = 0xFE" and "Ecc_Enable=0" in ini
		present NES test and 3d randomize ENABLE*/
	if (VUC_SCAN_FLASH_WINDOW_BACKDOOR_FOR_3D_RANDOMIZER == gScanWindow.uwECCCorrectBitNum && FALSE == gScanWindow.SubFeature.A.btScanWindowECCEn) {
		M_SET_3DRANDOMIZER_ENABLE(TRUE); //for EVB/NES
		FlaSetRandomizeConfig(M_GET_3DRANDOMIZER_ENABLE(), ENABLE, DISABLE, gFlhEnv.uwPageByteCnt >> 12);
	}
	M_UART(VUC_, "\nVUC_SCAN_FLASH_WINDOW PARAMETER");
	M_UART(VUC_, "\nBank:%l,Channel:%l", gScanWindow.SLBA2.A.ubNandBankSelect, gScanWindow.SLBA2.A.ubNandChannelSelect);
	M_UART(VUC_, "\nStartOffset:%l,EndOffset:%l", gScanWindow.SLBA1.A.uwStartOffset, gScanWindow.SLBA1.A.uwEndOffset);
	if (TRUE == gWindowSetValue.value.type.ubNandZQCL) {
		if (FALSE == gpVTDBUF->Nand.NandZQ.ubNandZQCLEn) {
			gLastCmd.ubProgress = SCAN_WINDOW_ONE_HUNDRED_PERCENT;
			gLastCmd.ubStatus = FAIL;
			gLastCmd.ubError_number = VUC_SCAN_FLASH_WINDOW_PARAMETER_ZQ_FAIL_ERROR_CODE;
			guwDebugBurnerErrorCode = ASSERT_VUC_0x0DE5;
			gScanWindow.btScanWindowSetted = SCAN_WINDOW_ERROR;
			return ;
		}
	}
	if (gFlhEnvMP.ulFailedCEBMP) {
		gScanWindow.btScanWindowSetted = SCAN_WINDOW_ERROR;
		return ;
	}
	if (1 == gScanWindow.SubFeature.A.btBitScan) {
		M_UART(VUC_, "\nBit Scan:TRUE BitPos:%l", gScanWindow.SLBA2.A.ubBitScanPos);
	}
	if (((gScanWindow.SLBA2.A.ubNandChannelSelect >= gFlhEnv.ubChannelExistNum) && (gScanWindow.SLBA2.A.ubNandChannelSelect != FIP_SCAN_ALL_CHANNEL)) ||
		((gScanWindow.SLBA2.A.ubNandBankSelect != FIP_SCAN_ALL_CE) && (((gFlhEnv.ulCEBMP >> gScanWindow.SLBA2.A.ubNandBankSelect)&BIT0) != BIT0))) {
		gLastCmd.ubProgress = SCAN_WINDOW_ONE_HUNDRED_PERCENT;
		gLastCmd.ubStatus = 2;
		gScanWindow.btScanWindowSetted = SCAN_WINDOW_ERROR;

	}
	else {
		gLastCmd.ubProgress = 0;
		gScanWindow.btScanWindowSetted = SCAN_WINDOW_WRITE_PATH;
		gFipPerBitTrain.ubFipTrainDQSFindPass = FIP_LEFT_ALIGN;
		memset(&gFipCurWindowParam, 0, sizeof(gFipCurWindowParam));
		gFipCurWindowParam.uwReadWindowOffset = gScanWindow.SLBA1.A.uwStartOffset;
		gFipCurWindowParam.uwWriteWindowOffset = gScanWindow.SLBA1.A.uwStartOffset;
		for (ubi = 0; ubi < MAX_CHANNEL; ubi++) {			// Set Minimum & Maxmum
			for (ubj = 0; ubj < MAX_CE_PER_CHANNEL; ubj++) {
				for (ubk = 0; ubk < MAX_LUN_NUM; ubk++) {
					gFipDllWindowValue[FIP_LEFT_ALIGN][ubi].Write.BankSdllValue[ubj].SingleBankSdllValue[ubk].B.A.uwSdllMin = U16_MAX;
					gFipDllWindowValue[FIP_LEFT_ALIGN][ubi].Write.BankSdllValue[ubj].SingleBankSdllValue[ubk].B.A.uwSdllMax = 0x0;
					gFipDllWindowValue[FIP_LEFT_ALIGN][ubi].Read.BankSdllValue[ubj].SingleBankSdllValue[ubk].B.A.uwSdllMin = U16_MAX;
					gFipDllWindowValue[FIP_LEFT_ALIGN][ubi].Read.BankSdllValue[ubj].SingleBankSdllValue[ubk].B.A.uwSdllMax = 0x0;
				}
			}
		}
	}
	//Scan will do later at get status
}
#endif
