#include "VUC_MicronGetVTSweep.h"
#include "VUC_MicronNandPass.h"
#include "hal/fip/fip_api.h"
#include "hal/fip/fip.h"
#include "hal/pic/uart/uart_api.h"
#include "burner/Burner_api.h"
#include "hal/dmac/dmac_pop_cmd.h"
#include "ftl/ftl_api.h"
#include "ftl/ftl_readverify_api.h"
#include "table/sys_block/sys_block_api.h"
#include "hal/cop0/cop0_api.h"

#if (VT_SWEEP_EN && PS5013_EN)
#include "VRLC/VRLC_api.h"
#include "retry/err_hdl_fpl_softbit_retry_table.h"

static U16 gaMicronVTSweepRegisterTmp[VUC_MICRON_REGISTER_NEED_TO_CLEAR_PRIOR_VT_SWEEP_NUM];

// For N28A

#if (IM_N28)
static U16 gaMicronVTSweepClearRegister[VUC_MICRON_REGISTER_NEED_TO_CLEAR_PRIOR_VT_SWEEP_NUM] = {
	0x0787, 0x07D5, 0x0792, 0x07AC, 0x0861, 0x08A7, 0x08ED, 0x0933,
	0x0979, 0x09BF, 0x0A05, 0x0A4B, 0x0A91, 0x0AD7, 0x0B1D, 0x0B63,
	0x0BA9, 0x0BEF, 0x0C35, 0x0C7B, 0x0CC1, 0x0D07, 0x0D4D, 0x0D93,
	0x0771, 0x078B, 0x07A5, 0x07BF, 0X07D9, 0x07F3, 0x080D, 0x0827,
};
#elif (IM_B47R)
static U16 gaMicronVTSweepClearRegister[VUC_MICRON_REGISTER_NEED_TO_CLEAR_PRIOR_VT_SWEEP_NUM] = {
	0x000, 0x004, 0x008, 0x00C, 0x010, 0x014, 0x018, 0x01C,
	0x020, 0x024, 0x028, 0x02C, 0x030, 0x034, 0x038, 0x03C,
};
#elif (IM_N48R)
static U16 gaMicronVTSweepClearRegister[VUC_MICRON_REGISTER_NEED_TO_CLEAR_PRIOR_VT_SWEEP_NUM] = {
	0x040, 0x043, 0x045, 0x04A, 0x050, 0x053, 0x055, 0x05A,
	0x060, 0x063, 0x065, 0x06A, 0x070, 0x073, 0x075, 0x07A,
	0x080, 0x083, 0x085, 0x08A, 0x090, 0x093, 0x095, 0x09A,
	0x0A0, 0x0A3, 0x0A5, 0x0AA, 0x0B0, 0x0B3, 0x0B5, 0x0BA,
};
#endif

AOM_VUC_3 static void VUCMicronClearAllVTRrgister(U8 ubChannel, U8 ubCE, U8 ubLUN)
{

	U8 ubi, ubValue[1];
	for (ubi = 0; ubi < VUC_MICRON_REGISTER_NEED_TO_CLEAR_PRIOR_VT_SWEEP_NUM; ubi++) {

		VUCMicronFlaGetMLBi(ubChannel, ubCE, ubLUN, gaMicronVTSweepClearRegister[ubi], &ubValue[0]);
		gaMicronVTSweepRegisterTmp[ubi] = ubValue[0];
		ubValue[0] = 0;
		VUCMicronFlaSetMLBi(ubChannel, ubCE, ubLUN, gaMicronVTSweepClearRegister[ubi], ubValue[0]);
		VUCMicronFlaGetMLBi(ubChannel, ubCE, ubLUN, gaMicronVTSweepClearRegister[ubi], &ubValue[0]);
	}

}

AOM_VUC_3 static void VUCMicronRestoreAllVTRrgister(U8 ubChannel, U8 ubCE, U8 ubLUN)
{
	U8 ubi;
	for (ubi = 0; ubi < VUC_MICRON_REGISTER_NEED_TO_CLEAR_PRIOR_VT_SWEEP_NUM; ubi++) {
		VUCMicronFlaSetMLBi(ubChannel, ubCE, ubLUN, gaMicronVTSweepClearRegister[ubi], gaMicronVTSweepRegisterTmp[ubi]);
	}
}
#endif

void VUCMicronFlaSetMLBi(U8 ubChannel, U8 ubCE, U8 ubLUN, U16 uwAddr, U8 pubFeature)
{
	REG32 *pFlaReg = R32_FCTL_CH[ubChannel];

	FIPWaitFIPInternalReady( ubChannel, GERNAL_TIMEOUT_THRESHOLD);

	FlaCEControl(ubChannel, ubCE, ENABLE);

	pFlaReg[R32_FCTL_PIO_CMD] = 0xEB;
	pFlaReg[R32_FCTL_PIO_ADR] = (uwAddr & BIT_MASK(8));
	pFlaReg[R32_FCTL_PIO_ADR] = ((uwAddr >> 8) & BIT_MASK(8));
	pFlaReg[R32_FCTL_PIO_ADR] = ubLUN;

	__asm("DSB");
	IdlePC(200);
	pFlaReg[R32_FCTL_HS_MODE] |= SET_DAT_DEFAULT;
	__asm("DSB");
	pFlaReg[R32_FCTL_PIO_DAT] = ((pubFeature << 8) | pubFeature);
	__asm("DSB");

	FlaWaitFlashReady(GERNAL_TIMEOUT_THRESHOLD);

	FlaCEControl(ubChannel, ubCE, DISABLE);

}

void VUCMicronFlaGetMLBi(U8 ubChannel, U8 ubCE, U8 ubLUN, U16 uwAddr, U8 *pubFeature)
{
	REG32 *pFlaReg = R32_FCTL_CH[ubChannel];

	FIPWaitFIPInternalReady( ubChannel, GERNAL_TIMEOUT_THRESHOLD);

	FlaCEControl(ubChannel, ubCE, ENABLE);

	pFlaReg[R32_FCTL_PIO_CMD] = 0xEA;
	pFlaReg[R32_FCTL_PIO_ADR] = (uwAddr & BIT_MASK(8));
	pFlaReg[R32_FCTL_PIO_ADR] = ((uwAddr >> 8) & BIT_MASK(8));
	pFlaReg[R32_FCTL_PIO_ADR] = ubLUN;
	__asm("DSB");

	FlaWaitFlashReady(GERNAL_TIMEOUT_THRESHOLD);

	pFlaReg[R32_FCTL_HS_MODE] |= SET_DAT_DEFAULT;
	__asm("DSB");

	*pubFeature = (U8)pFlaReg[R32_FCTL_PIO_DAT];
	FlaCEControl(ubChannel, ubCE, DISABLE);
}

#if (VT_SWEEP_EN && PS5013_EN)
void VUCMicronBackupCorrect2k(U8 ubChannel, U8 ubSrcInternalBuf, U32 ulTempAddr, U32 ulTempIRAMAddr)
{

	M_FIP_CLEAR_DRAM_MULTI(ubChannel);
	M_FIP_SET_BACK_RESTORE_LENGTH(ubChannel, ((((ldpc_frame_size[M_GET_ECC_MODE()] * 2) + 16) / 16) - 1));

	VUCMicronBackupRestore(ubChannel, ulTempAddr, ulTempIRAMAddr, ubSrcInternalBuf, 0, 0, 0, 0, 0);
}

void VUCMicronBackupRestore(U8 ubChannel, U64 uoDRAMAdddr, U32 ulIRAMAddr, U8 ubInternalBufPtr, U8 ubDirection, U8 ubMode, U8 ubBCHMode, U8 ubAll, U8 ubFrameMask)
{
	U16 *puwFPU = NULL;
	U16 uwFPUOffset;

	while (SRQ_SVL_BIT != (M_FIP_GET_FPU_TRIG(ubChannel) & (MT_BSY_BIT | ANY_BSY_BIT | SRQ_SVL_BIT | SIGN_OFF_BUSY_BIT | FPU_TRIGGER_BIT)));
	M_FIP_SET_RAW_DMA_ADR(ubChannel, uoDRAMAdddr);
	M_FIP_SET_RAW_DMA_IRAM_ADR(ubChannel, ulIRAMAddr);

	uwFPUOffset = FPU_PTR_OFFSET(fpu_backup_restore_ibuf);
	puwFPU = (U16 *)(IRAM_BASE + uwFPUOffset);

	*puwFPU = FPU_BR((ubFrameMask << 8) | (ubAll << 6) | (ubBCHMode << 5) | (ubInternalBufPtr << 2) | (ubDirection << 1) | ubMode);
	puwFPU++;

	*puwFPU = FPU_END;

	VUCMicronFPUTrigger(ubChannel, uwFPUOffset);
}

void VUCMicronFPUTrigger(U8 ubChannel, U16 uwFPUOffset)
{
	R32_FCTL_CH[ubChannel][R32_FCTL_INT_VCT] &= CLR_NORMAL_CPU;
	R32_FCTL_CH[ubChannel][R32_FCTL_INT_VCT] |= (1 << NORMAL_CPU_SHIFT);

	R32_FCTL_CH[ubChannel][R32_FCTL_INT_VCT] &= CLR_ERROR_CPU;
	R32_FCTL_CH[ubChannel][R32_FCTL_INT_VCT] |= (1 << ERROR_CPU_SHIFT);

	R32_FCTL_CH[ubChannel][R32_FCTL_INT_CFG] &= (~INT_VEC_EN_BIT);

	if (uwFPUOffset != 0xFFFF) {
		R32_FCTL_CH[ubChannel][R32_FCTL_FPU_ENTRY] &= ~FPU_ADDR_BASE_SHIFT_MASK;
		R32_FCTL_CH[ubChannel][R32_FCTL_FPU_ENTRY] |= uwFPUOffset;
	}

	M_FIP_TRIG_FPU(ubChannel);
	while (M_FIP_CHK_FPU_BUSY(ubChannel));
}

U32 VUCMicronReadCountOneNum(U32 ulReadAddr)
{
	U32 *pulNum = (U32 *) ulReadAddr;
	U32 ulTmp;
	U32 ulCountOneNum = 0;
	U32 ulNowLoop = 0, ulMaxLoop = (gFlhEnv.uwPageTotalByteCnt >> 4);

	while (ulNowLoop < ulMaxLoop) {
		ulTmp = *pulNum;
		while (ulTmp) {
			ulCountOneNum++;
			ulTmp = (ulTmp - 1) & ulTmp;
		}
		ulNowLoop++;
		pulNum ++;
	}

	return ulCountOneNum;
}

void VUCMicronRawReadDMATrigger(U16 uwFPUOffset, U32 ulFSA, U8 ubMTIndex, U8 ubECCFrameIdx, U32 ulDummyBuf, U8 ubALURule, U32 ulSpareAddr)
{
	FlhMT_t *pulMTAddr;
	FlhMT_t ulLocalMT = {{0}};
	MTCfg_t uoMTConfig;
	COP0_Clksw_t ulReadCmdClkswAttribute  = {0};
	U8 ubP4KFrameIdx = (U8)(ulFSA & gPCARule_Entry.ulMask);
	U8 ubGlobalCE = (U8)((ulFSA >> gPCARule_Channel.ubShift[ubALURule]) & ((gPCARule_Bank.ulMask << gPCARule_Channel.ubBit_No) | gPCARule_Channel.ulMask));
	L4KTable16B_t *pL4KSparePtr;

	ulReadCmdClkswAttribute.ulAll  = R32_COP0[R32_COP0_CLKSW_ATTR0];
	////------------------------------------------------------------
	pulMTAddr = (FlhMT_t *)M_MT_ADDR(ubMTIndex);

	////------------------------------------------------------------
	pL4KSparePtr = (L4KTable16B_t *)(ulSpareAddr);

	ulLocalMT.dma.btForce_R_Fail = 0;

	ulLocalMT.dma.uliFSA0_1 = ulFSA;
	ulLocalMT.dma.btBusy = TRUE;
	ulLocalMT.dma.btMTPFormat = 1;
	ulLocalMT.dma.ProgramBufferValid = 0xF;

	ulLocalMT.dma.btInterruptVectorEn = TRUE;
#if (!E21_TODO)
	ulLocalMT.dma.btiFSAEn = TRUE;
#endif /* (!E21_TODO) */
	ulLocalMT.dma.btLDPCCorrectEn = FALSE;
	ulLocalMT.dma.NorTarCPU = MT_TARGET_CPU0;
	ulLocalMT.dma.ErrTarCPU = MT_TARGET_CPU0;
	ulLocalMT.dma.btAllowSwitch = FALSE;
	ulLocalMT.dma.ADGSelectPointer = ubECCFrameIdx;
	ulLocalMT.dma.ALUSelect = ubALURule;
	ulLocalMT.dma.btIoType           = ulReadCmdClkswAttribute.A.btIo_typ;
#if (!E21_TODO && !U17_TODO)
	ulLocalMT.dma.btFCLK_DIV_EN      = ulReadCmdClkswAttribute.A.btFclkDiv_en;
#endif /* (!E21_TODO) */
	ulLocalMT.dma.FCLK_DIV           = ulReadCmdClkswAttribute.A.ubFclkDiv;
	ulLocalMT.dma.FlashType          = ulReadCmdClkswAttribute.A.ubFlh_typ;
	ulLocalMT.dma.btTimeConfigSelect = ulReadCmdClkswAttribute.A.btTimCfgSel;

	ulLocalMT.cmd.btUpdPollingSequence = TRUE;
#if (FIP_VIRTUAL_ADDRESS_WORKAROUND_EN)
	M_FIP_SET_POL_SEQUENCE_SELECT(ulLocalMT.cmd.POL_SEQ_SEL, (M_FIP_GET_FSA_DIE_NUMBER(ulLocalMT.cmd.uliFSA0_1, ulLocalMT.cmd.ALUSelect) ? POL_SEQ_FPU_ENTRY_READ_STATUS_BUSY_20_DIE1 : POL_SEQ_FPU_ENTRY_READ_STATUS_BUSY_20));
#else /* (FIP_VIRTUAL_ADDRESS_WORKAROUND_EN) */
	M_FIP_SET_POL_SEQUENCE_SELECT(ulLocalMT.cmd.POL_SEQ_SEL, POL_SEQ_FPU_ENTRY_READ_STATUS_BUSY_20);
#endif /* (FIP_VIRTUAL_ADDRESS_WORKAROUND_EN) */
	ulLocalMT.dma.btBMUAllocateEn = FALSE;

	ulLocalMT.dma.uwFPUPtr  = uwFPUOffset;
	//set MTQ_ID
	ulLocalMT.dma.btCESelectMode = 1;
	ulLocalMT.dma.ubCEValue = ubGlobalCE;

	ulLocalMT.dma.btConversionBypass = TRUE;
	ulLocalMT.dma.btGC = FALSE;

	ulLocalMT.dma.btCRCCheckDis = TRUE;
	ulLocalMT.dma.btDisableUDMA = TRUE;

	ulLocalMT.dma.L4KNum = 1;
	ulLocalMT.dma.FrameNum = 1;
	ulLocalMT.dma.btZipEn = 0;
	ulLocalMT.dma.btCompareEn = FALSE;
	ulLocalMT.dma.btBufferMode = 0;
	ulLocalMT.dma.L4KSparePtr = (ulSpareAddr) & 0xFFFF;

	memset((void *)pL4KSparePtr, 0, sizeof(L4KTable16B_t));
	pL4KSparePtr = (L4KTable16B_t *)((U32)(ulSpareAddr));
	pL4KSparePtr->BitMap.Read.FW = 0;
	pL4KSparePtr->BitMap.Read.Zinfo = MAX_ZINFO;
	pL4KSparePtr->BitMap.Read.ubBufferValid = 0;
#if (!E21_TODO)
	pL4KSparePtr->BitMap.Read.uwL4kNextPtr = 0xFFFF;
#endif /* (!E21_TODO) */
	pL4KSparePtr->BitMap.Read.PCA = ubP4KFrameIdx;
	pL4KSparePtr->BitMap.Read.BADR = ((U32)ulDummyBuf) >> SECTOR_SIZE_SHIFT;

	memcpy((void *)pulMTAddr, (void *) & (ulLocalMT), sizeof(FlhMT_t));
	uoMTConfig.uoAll = 0;
	uoMTConfig.bits_recc.ubQUE_IDX = ubGlobalCE;
	uoMTConfig.bits_recc.ubMT_IDX = ubMTIndex;
	uoMTConfig.bits_recc.btQOS =  FALSE;
#if E21_TODO
#else /* E21_TODO */
	uoMTConfig.u32.ulMT_CFG1 = 0;
#endif /* E21_TODO */
	FlaGlobalTrigger(&uoMTConfig);
}

void VUCMicronReadPageRawData(U32 ulReadAddr, U32 ulIFSA, U8 ubALURule)
{
	U32 ulBackupBuffer = ulReadAddr;
	U32 ulBackupBufferTempAddr;
	U32 ulSpareAddr = 0;
	U8 ubMTIdx1, ubMTIdx2;
	U8 ubGlobalCE = (U8)((ulIFSA >> gPCARule_Channel.ubShift[ubALURule]) & ((gPCARule_Bank.ulMask << gPCARule_Channel.ubBit_No) | gPCARule_Channel.ulMask));

	U8 ubIBuffSelect = 0, ubUseMaxIBufNum = 4;

	U32 ulFSA = ulIFSA;
	U8 ubChannel = ((ulFSA >> gPCARule_Channel.ubShift[ubALURule]) & gPCARule_Channel.ulMask);

	U16 uwFPUOffset = 0;
	U16 *puwFPUPtr = 0;

	ubMTIdx1 = FlaGetFreeMTIndex();
#if (IM_N28)
	uwFPUOffset = (ubALURule == COP0_PCA_RULE_2) ? FPU_PTR_OFFSET(fpu_entry_slc_1p_30_read) : FPU_PTR_OFFSET(fpu_entry_m3d_tlc_1p_30_read);
#elif ( (IM_B47R) || IM_B37R || (IM_N48R_NEED_CHECK))
#if E21_TODO
#elif U17_TODO
	uwFPUOffset = (ubALURule == COP0_PCA_RULE_2) ? FPU_PTR_OFFSET(fpu_entry_slc_1p_30_read) : FPU_PTR_OFFSET(fpu_entry_tlc_1p_30_read);
#else /* E21_TODO */
	uwFPUOffset = (ubALURule == COP0_PCA_RULE_2) ? FPU_PTR_OFFSET(fpu_entry_slc_1p_30_read_bin0) : FPU_PTR_OFFSET(fpu_entry_tlc_1p_30_read_bin0);
#endif /* E21_TODO */
#endif
#if (FIP_VIRTUAL_ADDRESS_WORKAROUND_EN)
	FIPDirectPushMTCmd(ubGlobalCE, ubMTIdx1, uwFPUOffset, ulFSA, VRLC_DUMMY_FSA, ubALURule, (M_FIP_GET_FSA_DIE_NUMBER(ulFSA, ubALURule) ? POL_SEQ_FPU_ENTRY_READ_STATUS_BUSY_20_DIE1 : POL_SEQ_FPU_ENTRY_READ_STATUS_BUSY_20), 0); // Dummy Read CMD
#else /* (FIP_VIRTUAL_ADDRESS_WORKAROUND_EN) */
	FIPDirectPushMTCmd(ubGlobalCE, ubMTIdx1, uwFPUOffset, ulFSA, VRLC_DUMMY_FSA, ubALURule, POL_SEQ_FPU_ENTRY_READ_STATUS_BUSY_20, 0); // Dummy Read CMD
#endif /* (FIP_VIRTUAL_ADDRESS_WORKAROUND_EN) */

	ubMTIdx2 = FlaGetFreeMTIndex();
#if (FIP_VIRTUAL_ADDRESS_WORKAROUND_EN)
	FIPDirectPushMTCmd(ubGlobalCE, ubMTIdx2, uwFPUOffset, ulFSA, VRLC_DUMMY_FSA, ubALURule, (M_FIP_GET_FSA_DIE_NUMBER(ulFSA, ubALURule) ? POL_SEQ_FPU_ENTRY_READ_STATUS_BUSY_20_DIE1 : POL_SEQ_FPU_ENTRY_READ_STATUS_BUSY_20), 0); // Real Read CMD
#else /* (FIP_VIRTUAL_ADDRESS_WORKAROUND_EN) */
	FIPDirectPushMTCmd(ubGlobalCE, ubMTIdx2, uwFPUOffset, ulFSA, VRLC_DUMMY_FSA, ubALURule, POL_SEQ_FPU_ENTRY_READ_STATUS_BUSY_20, 0); // Real Read CMD
#endif /* (FIP_VIRTUAL_ADDRESS_WORKAROUND_EN) */

	while (1) {
		FIPDelegateCmd();
		if (gMTMgr.ubMTDoneMsg[ubMTIdx1 - MT_RETRY_START_INDEX].btAllDone && gMTMgr.ubMTDoneMsg[ubMTIdx2 - MT_RETRY_START_INDEX].btAllDone) {
			FlaAddFreeMTIndex(ubMTIdx1);
			FlaAddFreeMTIndex(ubMTIdx2);
			break;
		}
	}

	R32_FCTL_CH[ubChannel][R32_FCTL_PURE_RAW_DMA_CFG] = (gFlhEnv.uwPageTotalByteCnt / ubUseMaxIBufNum);

	for (ubIBuffSelect = 0; ubIBuffSelect < ubUseMaxIBufNum; ubIBuffSelect++) {
		ubMTIdx1 = FlaGetFreeMTIndex();
		ulFSA &= ~(gPCARule_Entry.ulMask);
		ulFSA |= ubIBuffSelect;
		uwFPUOffset = FPU_PTR_OFFSET(fpu_raw_dma_read);
		puwFPUPtr = (U16 *)(IRAM_BASE + uwFPUOffset);
		puwFPUPtr[7] =  FPU_DMA_R_RAW((2 << RAW_MODE_OFFSET) | (2 << RAW_LOGIC_OFFSET) | (((ubIBuffSelect + 1) & BIT_MASK(2)) << RAW_SRC_PTR_OFFSET)
				| ((ubIBuffSelect) << RAW_DST_PTR_OFFSET));

#if (E21_TODO || U17_TODO)
		ulSpareAddr = IRAM_BASE + GC_BACKUP_OFF;
#else /* E21_TODO */
		ulSpareAddr = IRAM_BASE + GC_READ_P4K_OFF;
#endif /* E21_TODO */

		VUCMicronRawReadDMATrigger( uwFPUOffset, ulFSA, ubMTIdx1, 0, 0, ubALURule, ulSpareAddr);
		while (1) {
			FIPDelegateCmd();
			if (gMTMgr.ubMTDoneMsg[ubMTIdx1 - MT_RETRY_START_INDEX].btAllDone) {
				FlaAddFreeMTIndex(ubMTIdx1);
				break;
			}
		}
	}

	for (ubIBuffSelect = 0; ubIBuffSelect < ubUseMaxIBufNum; ubIBuffSelect++) {
		ulBackupBufferTempAddr = ulBackupBuffer + (ubIBuffSelect * VUC_MICRON_ALL_IBF_DATA_SIZE);
		ulSpareAddr = IRAM_BASE + GC_BACKUP_RETRY_OFF;
		VUCMicronBackupCorrect2k(ubChannel, ubIBuffSelect, ulBackupBufferTempAddr, ulSpareAddr);
	}
}

void VUCMicronGetVTSweep(U32 ulPayloadAddr)
{
#if BURNER_MODE_EN
	return;
#else
	U8 ubInternalBuf = 0, ubBitIdx, ubRuleIdx;
	U8 ubBackUpData[4] = {0};
	U8 ubData[4] = {0};
	U8 ubTmp1, ubTmp2, ubTmp3, ubTmp4, ubTmp5, ubTmp6, ubTmp7, ubTmp8;

	U32 ulBufAAddr, ulBufBAddr, ulResultAddr;
	U8 *pubBufA, *pubBufB;
	U16 *puwResult;

#if (BURNER_MODE_EN)
	pubBufA = (U8 *)VUC_MICRON_VT_SWEEP_READ_BUFFER_A;
	pubBufB = (U8 *)VUC_MICRON_VT_SWEEP_READ_BUFFER_B;
	puwResult = (U16 *)(BURNER_VENDOR_BUF_BASE + VUC_MICRON_VT_SWEEP_RESPONSE_HEADER_LENGTH + VUC_MICRON_VT_SWEEP_RESPONSE_DATA_PAYLOAD_HEADER_LENGTH);
	ulBufAAddr = VUC_MICRON_VT_SWEEP_READ_BUFFER_A;
	ulBufBAddr = VUC_MICRON_VT_SWEEP_READ_BUFFER_B;
	ulResultAddr = (BURNER_VENDOR_BUF_BASE + VUC_MICRON_VT_SWEEP_RESPONSE_HEADER_LENGTH + VUC_MICRON_VT_SWEEP_RESPONSE_DATA_PAYLOAD_HEADER_LENGTH);
#else /*(BURNER_MODE_EN)*/
	pubBufA = (U8 *)(gulResponseBufferAddr + gFlhEnv.uwPageTotalByteCnt * BITS_PER_BYTE * 2 + VUC_MICRON_VT_SWEEP_READ_BUFFER_SIZE);
	pubBufB = (U8 *)(gulResponseBufferAddr + gFlhEnv.uwPageTotalByteCnt * BITS_PER_BYTE * 2 + VUC_MICRON_VT_SWEEP_READ_BUFFER_SIZE + VUC_MICRON_VT_SWEEP_READ_BUFFER_SIZE);
	puwResult = (U16 *)(gulResponseBufferAddr + VUC_MICRON_VT_SWEEP_RESPONSE_HEADER_LENGTH + VUC_MICRON_VT_SWEEP_RESPONSE_DATA_PAYLOAD_HEADER_LENGTH);
	ulBufAAddr = (gulResponseBufferAddr + gFlhEnv.uwPageTotalByteCnt * BITS_PER_BYTE * 2 + VUC_MICRON_VT_SWEEP_READ_BUFFER_SIZE);
	ulBufBAddr = (gulResponseBufferAddr + gFlhEnv.uwPageTotalByteCnt * BITS_PER_BYTE * 2 + VUC_MICRON_VT_SWEEP_READ_BUFFER_SIZE + VUC_MICRON_VT_SWEEP_READ_BUFFER_SIZE);
	ulResultAddr = (gulResponseBufferAddr + VUC_MICRON_VT_SWEEP_RESPONSE_HEADER_LENGTH + VUC_MICRON_VT_SWEEP_RESPONSE_DATA_PAYLOAD_HEADER_LENGTH);
#endif /*(BURNER_MODE_EN)*/

	U16 uwVTValue = 0, uwInputChannel, uwInputCE, uwInputLUN, uwInputBlock, uwInputPage, uwInputVTStopValue, uwInputVTStepSize, uwInputReadMode, uwByteIdx;
	U16 uwTrimAddr;
	U32 ulCMDStartAddr, ulCountOneNum, ulIFSA = 0;
	VTSweepInputData_t *pInputData;
	DMACParam_t DMACParam;
	FlashAccessInfo_t FlaInfo = {0};

	VTSweepResponseHEADER_t *pResponseHeader;
	VTSweepResponseDataPayloadHEADER_t *pResponseDataPayloadHeader;
#if (BURNER_MODE_EN)
	pResponseHeader = (VTSweepResponseHEADER_t *)BURNER_VENDOR_BUF_BASE;
	pResponseDataPayloadHeader = (VTSweepResponseDataPayloadHEADER_t *)(BURNER_VENDOR_BUF_BASE + VUC_MICRON_VT_SWEEP_RESPONSE_HEADER_LENGTH);
#else /*(BURNER_MODE_EN)*/
	pResponseHeader = (VTSweepResponseHEADER_t *)gulResponseBufferAddr;
	pResponseDataPayloadHeader = (VTSweepResponseDataPayloadHEADER_t *)(gulResponseBufferAddr + VUC_MICRON_VT_SWEEP_RESPONSE_HEADER_LENGTH);
#endif /*(BURNER_MODE_EN)*/

	pResponseHeader->ubResponseDataFotmatVersion = 0x00;
	pResponseHeader->ubResponseHeaderFormatVersion = 0x03;
	pResponseHeader->uwCMDClass = VUC_MICRON_VT_SWEEP_COMMAND_CLASS;
	pResponseHeader->uwCMDCode = VUC_MICRON_VT_SWEEP_COMMAND_CODE;
	pResponseHeader->uwStatus = 0;
	pResponseHeader->ulDataPayloadSize = VUC_MICRON_VT_SWEEP_RESPONSE_HEADER_LENGTH + VUC_MICRON_VT_SWEEP_RESPONSE_DATA_PAYLOAD_HEADER_LENGTH + gFlhEnv.uwPageTotalByteCnt * BITS_PER_BYTE * 2;

	ulCMDStartAddr = ulPayloadAddr + VUC_MICRON_VT_SWEEP_HEADER_LENGTH;
	pInputData = (VTSweepInputData_t * )ulCMDStartAddr;

	uwInputChannel = pInputData->uwChannel;
	uwInputCE = pInputData->uwCE;
	uwInputLUN = pInputData->uwLUN;
	uwInputBlock = pInputData->uwPhysicalBlock;
	uwInputPage = pInputData->uwPage;
	uwInputVTStopValue = pInputData->uwVTStopValue;
	uwInputVTStepSize = pInputData->uwVTStepSize;
	uwInputReadMode = pInputData->uwReadMode;

	//Test mode
	/*
	uwInputChannel = 0;
	uwInputCE = 0;
	uwInputLUN = 0;
	uwInputBlock = 256;
	uwInputPage = 2196;
	uwInputVTStopValue = VUC_MICRON_N28_VT_SWEEP_MAX_VALUE;
	uwInputVTStepSize = 1;
	uwInputReadMode = 1;
	*/

	pResponseDataPayloadHeader->uwChannel = uwInputChannel;
	pResponseDataPayloadHeader->uwCE = uwInputCE;
	pResponseDataPayloadHeader->uwLUN = uwInputLUN;
	pResponseDataPayloadHeader->uwPhysicalBlock = uwInputBlock;
	pResponseDataPayloadHeader->uwPage = uwInputPage;
	pResponseDataPayloadHeader->uwVTStopValue = uwInputVTStopValue;
	pResponseDataPayloadHeader->uwVTStepSize = uwInputVTStepSize;
	ubRuleIdx = (VUC_MICRON_VT_READ_SLC_MODE == uwInputReadMode) ? COP0_PCA_RULE_2 : COP0_PCA_RULE_0;

	FlaInfo.ubChannel = uwInputChannel;
	FlaInfo.ubFlashCE = uwInputCE;
	FlaInfo.ubFrame = 0;
	FlaInfo.ubPlane = (U8)(uwInputBlock & gubBurstsPerBankMask);
	FlaInfo.ubLUN = (uwInputBlock >> gubBurstsPerBankLog) >> gPCARule_Block.ubBit_No;
	FlaInfo.ulBlock = (uwInputBlock >> gubBurstsPerBankLog) & gPCARule_Block.ulMask;
	FlaInfo.uwPage = uwInputPage;

	ulIFSA = FlaGetPCA(FlaInfo.ubPlane, FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, FlaInfo.uwPage, FlaInfo.ulBlock, ubRuleIdx);
	ulIFSA |= (FlaInfo.ubFrame & gPCARule_Entry.ulMask) << gPCARule_Entry.ubShift[ubRuleIdx];

	// Registers need to be modified prior Vt Sweep,  and the following MAGIC address number is provided by Micron.
	if (IM_N28) {
		FlaGetFeature(FlaInfo.ubChannel, FlaInfo.ubFlashCE, MICRON_FEATURE_FLAG_CHECK_FUNCTIONALITY, &ubBackUpData[0], FIP_NO_USE_PARAMETER, FIP_NO_USE_PARAMETER, FALSE, FIP_NO_USE_PARAMETER, FIP_NO_USE_PARAMETER);
		FlaGetFeature(FlaInfo.ubChannel, FlaInfo.ubFlashCE, MICRON_FEATURE_FLAG_CHECK_FUNCTIONALITY, &ubData[0], FIP_NO_USE_PARAMETER, FIP_NO_USE_PARAMETER, FALSE, FIP_NO_USE_PARAMETER, FIP_NO_USE_PARAMETER);

		ubData[0] = ubData[0] & MICRON_FLAG_CHECK_FUNCTIONALITY_DISABLED_BIT;
		FlaSetFeature(FlaInfo.ubChannel, FlaInfo.ubFlashCE, MICRON_FEATURE_FLAG_CHECK_FUNCTIONALITY, &ubData[0]);
		FlaGetFeature(FlaInfo.ubChannel, FlaInfo.ubFlashCE, MICRON_FEATURE_FLAG_CHECK_FUNCTIONALITY, &ubData[0], FIP_NO_USE_PARAMETER, FIP_NO_USE_PARAMETER, FALSE, FIP_NO_USE_PARAMETER, FIP_NO_USE_PARAMETER);

		VUCMicronFlaGetMLBi(FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, 0x80DF, &ubData[0]);

		ubData[0] = (ubData[0] | BIT(0) | BIT(1));

		VUCMicronFlaSetMLBi(FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, 0x80DF, ubData[0]);
		VUCMicronFlaGetMLBi(FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, 0x80DF, &ubData[0]);
		VUCMicronFlaGetMLBi(FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, 0x0158, &ubData[0]);
		VUCMicronFlaGetMLBi(FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, 0x80E0, &ubData[0]);
	}
	else if (IM_N48R) {
		if (VALLEY_TRACK_EN) {
			ubData[0] = 0x04;
			FlaSetFeature(FlaInfo.ubChannel, FlaInfo.ubFlashCE, 0x96, &ubData[0]);
		}
	}

	VUCMicronClearAllVTRrgister(FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN);

	if (VUC_MICRON_VT_READ_SLC_MODE == uwInputReadMode) {

		if (IM_N28) {
			// N28A SLC Block WLG 3 and WLG 102
			if (((0 <= uwInputPage) && (11 >= uwInputPage)) || ((1140 <= uwInputPage) && (1151 >= uwInputPage))) {
				uwTrimAddr = 0x1CB;
			}
			// N28A SLC Block WLG from 4 to 101
			else if (((12 <= uwInputPage) && (1139 >= uwInputPage))) {
				uwTrimAddr = 0x1CC;
			}
			else {
			}
		}
		else if (IM_B47R) {
			// B47R SLC Block WLG 182, 95, 90, and 3
			if (((0 <= uwInputPage) && (3 >= uwInputPage)) || ((348 <= uwInputPage) && (351 >= uwInputPage)) ||
				((352 <= uwInputPage) && (355 >= uwInputPage)) || ((700 <= uwInputPage) && (703 >= uwInputPage))) {
				uwTrimAddr = 0xBE;
			}
			// B47R SLC Block WLG from 181 to 96 or from 89 to 4
			else if (((4 <= uwInputPage) && (347 >= uwInputPage)) || ((356 <= uwInputPage) && (699 >= uwInputPage))) {
				uwTrimAddr = 0xB4;
			}
		}
		else if (IM_N48R) {
			// N48R SLC Block WLG 4, 91, 94, and 181
			if (((0 <= uwInputPage) && (3 >= uwInputPage)) || ((348 <= uwInputPage) && (351 >= uwInputPage)) ||
				((352 <= uwInputPage) && (355 >= uwInputPage)) || ((700 <= uwInputPage) && (703 >= uwInputPage))) {
				uwTrimAddr = 0x1D4;
			}
			else {
				uwTrimAddr = 0x1DE;
			}

		}
	}
	else if (VUC_MICRON_VT_READ_NON_SLC_MODE == uwInputReadMode) {

		if (IM_N28) {
			U16 uwWordlineIdx = FTLGetCoord(uwInputPage, IM_GETCOORD_Y_VAL);
			FTLWordLineTypeEnum_t ubWordLineType = FTLCommonLibraryWordLineInfo(uwWordlineIdx, WL_TYPE);

			if (SLC_WL == ubWordLineType) {
				uwTrimAddr = 0x01AB;
			}
			else if (TLC_WL == ubWordLineType) {
				uwTrimAddr = 0x01B0;
			}
			else if (QLC_WL == ubWordLineType) {
				uwTrimAddr = 0x01C3;
			}
		}
		else if (IM_B47R) {
			if (((2108 <= uwInputPage) && (2111 >= uwInputPage)) || ((0 <= uwInputPage) && (3 >= uwInputPage))) {
				uwTrimAddr = 0xC6;
			}
			else if (((1048 <= uwInputPage) && (1063 >= uwInputPage))) {
				uwTrimAddr = 0xA0;
			}
			else {
				uwTrimAddr = 0x50;

				VUCMicronFlaGetMLBi(FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, 0x078, &ubTmp1);
				VUCMicronFlaGetMLBi(FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, 0x079, &ubTmp2);
				VUCMicronFlaSetMLBi(FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, 0x078, 0x52);
				VUCMicronFlaSetMLBi(FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, 0x079, 0x3);

			}
		}
		else if (IM_N48R) {
			if (((0 <= uwInputPage) && (7 >= uwInputPage)) || ((1400 <= uwInputPage) && (1415 >= uwInputPage)) ||
				((2808 <= uwInputPage) && (2815 >= uwInputPage))) {
				uwTrimAddr = 0x1B6;
			}
			else {
				uwTrimAddr = 0xD0;

				VUCMicronFlaGetMLBi(FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, 0x0EE, &ubTmp1);
				VUCMicronFlaGetMLBi(FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, 0x0EF, &ubTmp2);
				VUCMicronFlaSetMLBi(FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, 0x0EE, 0x52);
				VUCMicronFlaSetMLBi(FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, 0x0EF, 0x3);
				VUCMicronFlaGetMLBi(FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, 0x102, &ubTmp5);
				VUCMicronFlaGetMLBi(FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, 0x103, &ubTmp6);
				VUCMicronFlaSetMLBi(FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, 0x102, 0x52);
				VUCMicronFlaSetMLBi(FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, 0x103, 0x3);
				VUCMicronFlaGetMLBi(FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, 0x134, &ubTmp7);
				VUCMicronFlaGetMLBi(FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, 0x135, &ubTmp8);
				VUCMicronFlaSetMLBi(FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, 0x134, 0x52);
				VUCMicronFlaSetMLBi(FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, 0x135, 0x3);
			}

		}
	}
	else {
	}

	// Init BufA to 0x00
	DMACParam.DMACSetValue.ulDestAddr = ulBufAAddr;
	DMACParam.DMACSetValue.ulLowValue = 0;
	DMACParam.DMACSetValue.ulHighValue = 0;
	DMACParam.DMACSetValue.ul32ByteNum = SIZE_IN_32B(VUC_MICRON_VT_SWEEP_READ_BUFFER_SIZE);
	DMACParam.DMACSetValue.btValidate = FALSE;
	DMACSetValue(&DMACParam, (U32)NULL, 0);

	// Init BufB to 0x00
	DMACParam.DMACSetValue.ulDestAddr = ulBufBAddr;
	DMACParam.DMACSetValue.ulLowValue = 0;
	DMACParam.DMACSetValue.ulHighValue = 0;
	DMACParam.DMACSetValue.ul32ByteNum = SIZE_IN_32B(VUC_MICRON_VT_SWEEP_READ_BUFFER_SIZE);
	DMACParam.DMACSetValue.btValidate = FALSE;
	DMACSetValue(&DMACParam, (U32)NULL, 0);

	// Init Result BUFF to 0xFF
	DMACParam.DMACSetValue.ulDestAddr = ulResultAddr;
	DMACParam.DMACSetValue.ulLowValue = 0xFFFFFFFF;
	DMACParam.DMACSetValue.ulHighValue = 0xFFFFFFFF;
	DMACParam.DMACSetValue.ul32ByteNum = SIZE_IN_32B(gFlhEnv.uwPageTotalByteCnt * BITS_PER_BYTE * 2 + SIZE_32B + SIZE_512B);
	DMACParam.DMACSetValue.btValidate = FALSE;

	gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
	DMACSetValue(&DMACParam, gulDMACDirectWaitDone_Callback, 0);
	while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
		DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
	}
	if (IM_B47R || IM_N48R) {
		VUCMicronFlaGetMLBi(FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, uwTrimAddr, &ubTmp3);
		VUCMicronFlaGetMLBi(FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, uwTrimAddr + 1, &ubTmp4);
	}

	for (uwVTValue = 0; uwVTValue < uwInputVTStopValue; uwVTValue = uwVTValue + uwInputVTStepSize) {

		ulCountOneNum = 0;

		if (IM_N28) {
			VUCMicronFlaSetMLBi(FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, uwTrimAddr, uwVTValue);
		}
		else if (IM_B47R || IM_N48R) {
			VUCMicronFlaSetMLBi(FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, uwTrimAddr, (uwVTValue & 0xff));
			VUCMicronFlaSetMLBi(FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, uwTrimAddr + 1, ((uwVTValue >> 8) & 0x3));
		}

		VUCMicronReadPageRawData(ulBufBAddr, ulIFSA, ubRuleIdx);

		for (ubInternalBuf = 0; ubInternalBuf < VUC_MICRON_RAW_READ_USE_IBUF_NUM; ubInternalBuf++) {
			for (uwByteIdx = 0; uwByteIdx < (gFlhEnv.uwPageTotalByteCnt / VUC_MICRON_RAW_READ_USE_IBUF_NUM); uwByteIdx++) {
				pubBufA[ubInternalBuf * VUC_MICRON_ALL_IBF_DATA_SIZE + uwByteIdx] = pubBufA[ubInternalBuf * VUC_MICRON_ALL_IBF_DATA_SIZE + uwByteIdx] ^  pubBufB[ubInternalBuf * VUC_MICRON_ALL_IBF_DATA_SIZE + uwByteIdx];

				for ( ubBitIdx = 0; ubBitIdx < BITS_PER_BYTE; ubBitIdx++) {
					if ((((pubBufA[ubInternalBuf * VUC_MICRON_ALL_IBF_DATA_SIZE + uwByteIdx] >> ubBitIdx)& BIT(0)) == BIT(0)) && (puwResult[ubBitIdx + (ubInternalBuf * (gFlhEnv.uwPageTotalByteCnt / VUC_MICRON_RAW_READ_USE_IBUF_NUM) + uwByteIdx) * BITS_PER_BYTE] == 0xFFFF)) {
						puwResult[ubBitIdx + (ubInternalBuf * (gFlhEnv.uwPageTotalByteCnt / VUC_MICRON_RAW_READ_USE_IBUF_NUM) + uwByteIdx) * BITS_PER_BYTE] = (U16)uwVTValue;
					}
				}
			}
		}


		ulCountOneNum = ulCountOneNum + VUCMicronReadCountOneNum(ulBufBAddr);
		ulCountOneNum = ulCountOneNum + VUCMicronReadCountOneNum(ulBufBAddr + VUC_MICRON_ALL_IBF_DATA_SIZE);
		ulCountOneNum = ulCountOneNum + VUCMicronReadCountOneNum(ulBufBAddr + VUC_MICRON_ALL_IBF_DATA_SIZE + VUC_MICRON_ALL_IBF_DATA_SIZE);
		ulCountOneNum = ulCountOneNum + VUCMicronReadCountOneNum(ulBufBAddr + VUC_MICRON_ALL_IBF_DATA_SIZE + VUC_MICRON_ALL_IBF_DATA_SIZE + VUC_MICRON_ALL_IBF_DATA_SIZE);

		if (VUC_MICRON_VT_SWEEP_VERIFY_EN) {
			UartPrintf("\nCount one: %x", ulCountOneNum);
		}

		if (VUC_MICRON_VT_SWEEP_VERIFY_EN) {
			REG32 *pFlaReg = R32_FCTL_CH[FlaInfo.ubChannel];

			U32 uli;
			U16 *puwPIOBuffer;
			if (BURNER_MODE_EN) {
				puwPIOBuffer = (U16 *)0x2216E000;
			}
			else {
				puwPIOBuffer = (U16 *) (ulBufBAddr + VUC_MICRON_VT_SWEEP_READ_BUFFER_SIZE);
			}
			U8 ubIBuffSelect;

			pFlaReg[R32_FCTL_HS_MODE] |= SET_DAT_DEFAULT;

			if ((gFlhEnv.ubCE_Interface[FlaInfo.ubChannel * MAX_CE_PER_CHANNEL + FlaInfo.ubFlashCE] >= TOGGLE_INTERFACE) && (gFlhEnv.ubCE_Interface[FlaInfo.ubChannel * MAX_CE_PER_CHANNEL + FlaInfo.ubFlashCE] <= NVDDR3_INTERFACE)) {
				pFlaReg[R32_FCTL_FLH_SET] |= SET_TOGGLE_MODE;
			}
			else {
				pFlaReg[R32_FCTL_FLH_SET] &= CLR_LEGACY_MODE;
			}

			FlaCEControl(FlaInfo.ubChannel, FlaInfo.ubFlashCE, ENABLE);

			pFlaReg[R32_FCTL_PIO_CMD] = 0x06;
			IdlePC(100);
			pFlaReg[R32_FCTL_PIO_ADR] = 0;
			pFlaReg[R32_FCTL_PIO_ADR] = 0;
			pFlaReg[R32_FCTL_PIO_ADR] = 0;
			pFlaReg[R32_FCTL_PIO_ADR] = 0;
			pFlaReg[R32_FCTL_PIO_ADR] = 0;
			IdlePC(100);
			pFlaReg[R32_FCTL_PIO_CMD] = 0xE0;
			IdlePC(100);
			pFlaReg[R32_FCTL_HS_MODE] &= CLR_DAT_MID;

			for (uli = 0; uli < (gFlhEnv.uwPageTotalByteCnt / 2);
				uli++) {
				if (uli == 0) {
					pFlaReg[R32_FCTL_HS_MODE] |= PIO_DAT_FIRST_BIT;
				}
				else if (uli == ((gFlhEnv.uwPageTotalByteCnt / 2) - 1)) {
					pFlaReg[R32_FCTL_HS_MODE] |= PIO_DAT_LAST_BIT;
				}


				__asm("DSB");
				puwPIOBuffer[uli] = (U16)pFlaReg[R32_FCTL_PIO_DAT];
				pFlaReg[R32_FCTL_HS_MODE] &= CLR_DAT_MID;
				__asm("DSB");
			}
			pFlaReg[R32_FCTL_HS_MODE] |= SET_DAT_DEFAULT;

			FlaCEControl(FlaInfo.ubChannel, FlaInfo.ubFlashCE, DISABLE);

			for (ubIBuffSelect = 0; ubIBuffSelect < VUC_MICRON_RAW_READ_USE_IBUF_NUM; ubIBuffSelect++) {
				for (uli = 0; uli < ((gFlhEnv.uwPageTotalByteCnt / VUC_MICRON_RAW_READ_USE_IBUF_NUM)); uli++) {
					if ((*(U8 *)(pubBufB + uli + (VUC_MICRON_ALL_IBF_DATA_SIZE * ubIBuffSelect))) != (*(((U8 *)puwPIOBuffer) + uli + (ubIBuffSelect * (gFlhEnv.uwPageTotalByteCnt / VUC_MICRON_RAW_READ_USE_IBUF_NUM))))) {
						UartPrintf("Fail %x %x\n", (*(U8 *)(pubBufB + uli + (VUC_MICRON_ALL_IBF_DATA_SIZE * ubIBuffSelect))), (*(((U8 *)puwPIOBuffer) + uli + (ubIBuffSelect * (gFlhEnv.uwPageTotalByteCnt / VUC_MICRON_RAW_READ_USE_IBUF_NUM)))));
					}
				}
			}
		}
		if ((gFlhEnv.uwPageTotalByteCnt * BITS_PER_BYTE) == ulCountOneNum) {
			break;
		}
	}

	// Restore related address
	VUCMicronRestoreAllVTRrgister(FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN);

	if (IM_B47R) {
		if (VUC_MICRON_VT_READ_NON_SLC_MODE == uwInputReadMode) {
			if (0x50 == uwTrimAddr) {
				VUCMicronFlaSetMLBi(FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, 0x078, ubTmp1);
				VUCMicronFlaSetMLBi(FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, 0x079, ubTmp2);
			}
		}
		VUCMicronFlaSetMLBi(FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, uwTrimAddr, ubTmp3);
		VUCMicronFlaSetMLBi(FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, uwTrimAddr + 1, ubTmp4);
	}
	else if (IM_N48R) {
		if (VUC_MICRON_VT_READ_NON_SLC_MODE == uwInputReadMode) {
			if (0xD0 == uwTrimAddr) {
				VUCMicronFlaSetMLBi(FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, 0x0EE, ubTmp1);
				VUCMicronFlaSetMLBi(FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, 0x0EF, ubTmp2);
				VUCMicronFlaSetMLBi(FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, 0x102, ubTmp5);
				VUCMicronFlaSetMLBi(FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, 0x103, ubTmp6);
				VUCMicronFlaSetMLBi(FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, 0x134, ubTmp7);
				VUCMicronFlaSetMLBi(FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, 0x135, ubTmp8);
			}
		}
		VUCMicronFlaSetMLBi(FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, uwTrimAddr, ubTmp3);
		VUCMicronFlaSetMLBi(FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, uwTrimAddr + 1, ubTmp4);

		if (VALLEY_TRACK_EN) {
			ubData[0] = 0x0C;
			FlaSetFeature(FlaInfo.ubChannel, FlaInfo.ubFlashCE, 0x96, &ubData[0]);
		}

	}
#endif
}

#endif /*(VT_SWEEP_EN)*/
