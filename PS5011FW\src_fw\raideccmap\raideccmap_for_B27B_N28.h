/****************************************************************************
 *
 *  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
 *  All rights reserved
 *
 *  The content of this document is confidential and shall be applied
 *  subject to the terms and conditions of the license agreement and
 *  other applicable laws. Any unauthorized access, use or disclosure
 *  of this document is strictly prohibited and may be punishable
 *  under laws.
 *
 *  raideccmap_for_B27B_N28.h
 *
 *
 *
 ****************************************************************************/

#ifndef RAIDECCMAP_FOR_B27B_N28_H_
#define RAIDECCMAP_FOR_B27B_N28_H_

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "common/math_op.h"
#include "env.h"
#include "fw_common.h"
#include "setup.h"
#include "typedef.h"

#if (MICRON_FSP_EN && (IM_B17 || IM_B27A || IM_B27B || IM_N18 || IM_N28 || IM_B47R || IM_B37R))
/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */
// BELOW DEFINES ARE MAINLY FOR B47R, NEED CHECK OTHER MICRON SERIES.

//**********************
// Data Parity Ratio
//**********************
// Not a formal ratio definition
// "% gubPlanesPerSuperPage" only works under odd CE
#define RAIDECCMAP_DATA_PARITY_RATIO		((IM_B47R || IM_B37R) ? (64 - (64 % gubPlanesPerSuperPage)) : (128 - (128 % gubPlanesPerSuperPage)))

#define RAIDECCMAP_TABLE_MIN_PLANE_NUM_PER_GROUP	(8)
#define RAIDECCMAP_TALBE_DATA_PARITY_RATIO			((1 == gubCENumber) ? RAIDECCMAP_TABLE_MIN_PLANE_NUM_PER_GROUP : gubPlanesPerSuperPage)


//**********************
// Plane Protect
//**********************
#define RAIDECCMAP_PLANE_PROTECT_NUM_BY_FLASH	(MULTI_PLANE_PROTECTION ? (4) : (1))


//**********************
// Flash Related
//**********************
#define RAIDECCMAP_MAX_WINDOW_SIZE		(3)


//**********************
// Basic Tag Num
//**********************
#define RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM       ((IM_B47R || IM_B37R) ? 4 : 12)
#define RAIDECCMAP_1WL_PROTECTION_IN_XLC_PARITY_TAG_IDX_NUM       ((IM_B47R || IM_B37R) ? 12: (IM_B27B ? 36 : 48))

#define RAIDECCMAP_2WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM       (RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM * 2)
#define RAIDECCMAP_2WL_PROTECTION_IN_XLC_PARITY_TAG_IDX_NUM       (RAIDECCMAP_1WL_PROTECTION_IN_XLC_PARITY_TAG_IDX_NUM * 2)

// Required tag num without switch num
#define RAIDECCMAP_SLC_DATA_BASIC_TAG_NUM_BY_FLASH      ((IM_B47R || IM_B37R) ? 8 : 12)
#define RAIDECCMAP_XLC_DATA_BASIC_TAG_NUM_BY_FLASH      ((IM_B47R || IM_B37R) ? 24: ((IM_B27A) ? 108 :(IM_B27B ? 72 : 96)))
#define RAIDECCMAP_TABLE_DATA_BASIC_TAG_NUM_BY_FLASH	(1)


//**********************
// Switch Num
//**********************
#define RAIDECCMAP_SLC_DATA_TAG_SWITCH_NUM_BY_FLASH		(1)
#define RAIDECCMAP_XLC_DATA_TAG_SWITCH_NUM_BY_FLASH		(1)
#define RAIDECCMAP_TABLE_DATA_TAG_SWITCH_NUM_BY_FLASH	(2)


//**********************
// FW Tag Related
//**********************
// Directly defined in raideccmap_api.h


//**********************
// In RAM Tag Num Related (imply buffer usage)
//**********************
// BUFFER Keep
#define RAIDECCMAP_DECODE_GROUP_SIZE_BY_FLASH					(4)
// SLC and XLC GR share the same group, so separately define them
#define RAIDECCMAP_SLC_GR_IN_RAM_TAG_NUM_BY_FLASH					((IM_B47R || IM_B37R) ? (8) : (12))
#define RAIDECCMAP_XLC_GR_IN_RAM_TAG_NUM_BY_FLASH					(6)
#define RAIDECCMAP_GR_GROUP_SIZE_BY_FLASH						(MAX(RAIDECCMAP_SLC_GR_IN_RAM_TAG_NUM_BY_FLASH, RAIDECCMAP_XLC_GR_IN_RAM_TAG_NUM_BY_FLASH))
// RAIDECCMAP_GC_AND_COPY_UNIT_GROUP_SIZE_BY_FLASH should be the same as "RAM_GCGR_RAIDECC / FRAMES_PER_PAGE" in bmu_api.h
#if (FW_CATEGORY_FLASH == FLASH_B47R_TLC)  // B47R
#define RAIDECCMAP_GC_AND_COPY_UNIT_GROUP_SIZE_BY_FLASH	(3)
#elif (CONFIG_FLASH_TYPE == FLASH_B37R_TLC)
#define RAIDECCMAP_GC_AND_COPY_UNIT_GROUP_SIZE_BY_FLASH	(2)
#elif (FW_CATEGORY_FLASH == FLASH_N28_QLC)  // N28
#define RAIDECCMAP_GC_AND_COPY_UNIT_GROUP_SIZE_BY_FLASH	((GC_N28_REDUCE_SWAP_PARITY_FREQUENCY ? (6):(2))
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC)  // B27B
#define RAIDECCMAP_GC_AND_COPY_UNIT_GROUP_SIZE_BY_FLASH	(2)
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)  // N18
#define RAIDECCMAP_GC_AND_COPY_UNIT_GROUP_SIZE_BY_FLASH	(3)
#endif  /* FW_CATEGORY_FLASH == FLASH_XXX_XXX */
#define RAIDECCMAP_TABLE_GROUP_SIZE_BY_FLASH					(1)
#define RAIDECCMAP_INITINFO_GROUP_SIZE_BY_FLASH				(1)
// No need to concern decode part
#define RAIDECCMAP_TOTAL_IN_RAM_GROUP_SIZE_BY_FLASH				(RAIDECCMAP_GR_GROUP_SIZE_BY_FLASH + RAIDECCMAP_GC_AND_COPY_UNIT_GROUP_SIZE_BY_FLASH + RAIDECCMAP_TABLE_GROUP_SIZE_BY_FLASH + RAIDECCMAP_INITINFO_GROUP_SIZE_BY_FLASH)


//**********************
// Struct Size Related
//**********************
#define RAIDECCMAP_PARITYMAP_NUM	((IM_B47R || IM_B37R) ? 350 : 256)

// B47R -> 21 (include XLC GR)
#define RAIDECCMAP_TAG_MGR_ENTRY_NUM	(\
	RAIDECCMAP_SLC_GR_IN_RAM_TAG_NUM_BY_FLASH * RAIDECCMAP_SLC_DATA_TAG_SWITCH_NUM_BY_FLASH +\
	RAIDECCMAP_XLC_GR_IN_RAM_TAG_NUM_BY_FLASH * RAIDECCMAP_XLC_DATA_TAG_SWITCH_NUM_BY_FLASH +\
	RAIDECCMAP_GC_AND_COPY_UNIT_GROUP_SIZE_BY_FLASH * RAIDECCMAP_XLC_DATA_TAG_SWITCH_NUM_BY_FLASH +\
	RAIDECCMAP_TABLE_GROUP_SIZE_BY_FLASH * RAIDECCMAP_TABLE_DATA_TAG_SWITCH_NUM_BY_FLASH +\
	RAIDECCMAP_INITINFO_GROUP_SIZE_BY_FLASH * RAIDECCMAP_TABLE_DATA_TAG_SWITCH_NUM_BY_FLASH\
)

#define RAIDECCMAP_OPEN_BLOCK_DESCRIPTION_NUM	(4)

/*
 * TODO:
 * Using RS define is not correct solution to evaluate number of parity.
 */
#if (D1_UNIT_EN)
#define RAIDECCMAP_SPOR_SCAN_GR_MAX_PARITY_NUM		(RAIDECCMAP_SLC_DATA_BASIC_TAG_NUM_BY_FLASH * RAIDECCMAP_SLC_DATA_TAG_SWITCH_NUM_BY_FLASH)
#else  /* (D1_UNIT_EN) */
#define RAIDECCMAP_SPOR_SCAN_GR_MAX_PARITY_NUM		(RAIDECCMAP_XLC_DATA_BASIC_TAG_NUM_BY_FLASH * RAIDECCMAP_XLC_DATA_TAG_SWITCH_NUM_BY_FLASH)
#endif  /* (D1_UNIT_EN) */

#define RAIDECCMAP_GR_VT_ARRAY_SIZE			(RAIDECCMAP_GR_GROUP_SIZE_BY_FLASH)
#define RAIDECCMAP_GC_VT_ARRAY_SIZE			(4)
#define RAIDECCMAP_TABLE_VT_ARRAY_SIZE		(RAIDECCMAP_TABLE_GROUP_SIZE_BY_FLASH)
#define RAIDECC_RESERVE_BYTE				((IM_B47R || IM_B37R) ? 220 : 212)  // B47R -> Reserve 224 Bytes

/*
 * ---------------------------------------------------------------------------------------------------
 *  enum
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *  data types
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *   macros
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *   extern global variables
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */

#endif  /* (MICRON_FSP_EN && (IM_B17 || IM_B27A || IM_B27B || IM_N18 || IM_N28 || IM_B47R)) */

#endif  /* RAIDECCMAP_FOR_B27B_N28_H_ */
