/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2017, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  VUC_Protect.h                                                      */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#ifndef _VUC_PROTECT_H_
#define _VUC_PROTECT_H_

#include "VUC_Protect_api.h"

#define VUC_PROTECT_PHISON_DEFUALT_KEY	(0x0001)
#define VUC_PROTECT_CODESIGN_IDX	(0x31303050) // P001

#define VUC_PROTECT_CHECK_COMMAND_STATUS_PASS       (0)
#define VUC_PROTECT_CHECK_COMMAND_STATUS_CRC_FAIL   (1)
#define VUC_PROTECT_CHECK_COMMAND_STATUS_HW_FAIL    (2)

#define VUC_PROTECT_ASN1_TYPE_SHA256_DIGEST_INFO_LEN	(19)
#define VUC_PROTECT_HASH_SHA256_LENGTH   			(32)

#if PS5017_EN
#define VUC_PROTECT_SIGNATURE_BASE		(0x22024000) // Signature 512B
#define VUC_PROTECT_SECURITY_BUF_BASE	(0x22024200) // Temp used 1024B (Message 768B + Other used 256B)
#define VUC_PROTECT_DECRYPT_BUF_BASE	(0x22024600) // Decrypt Signature 512B
#define VUC_PROTECT_SHA512_BUF_BASE		(0x22024800) // SHA512 64B
#else /* PS5017_EN */
#define VUC_PROTECT_SIGNATURE_BASE		(0x22024000)
#define VUC_PROTECT_SECURITY_BUF_BASE	(0x22024100)
#define VUC_PROTECT_SHA256_BUF_BASE		(0x22024200)
#define VUC_PROTECT_DECRYPT_BUF_BASE	(0x22024300)
#endif /* PS5017_EN */

#if PS5017_EN
#define VUC_PROTECT_GEN_RANDOM_SIZE			(1024)
#define VUC_PROTECT_TEMP_REVERSE_BUF_OFFSET	(0x400) // Size: 1024 B
#define VUC_PROTECT_TEMP_KEY_OFFSET			(0x800) // Size: 1552 B
#define VUC_PROTECT_AES_ENCRYPT_RANDOM_SIZE	(256)
#define VUC_PROTECT_PLAINTEXT_SIZE			(256)
#define VUC_PROTECT_PLAINTEXT_OFFSET_IN_32BYTE	(8)
#else /* PS5017_EN */
#define VUC_PROTECT_GEN_RANDOM_SIZE			(512)
#define VUC_PROTECT_TEMP_REVERSE_BUF_OFFSET	(0x400) // Size: 512 B
#define VUC_PROTECT_TEMP_KEY_OFFSET			(0x600) // Size: 780 B
#define VUC_PROTECT_AES_ENCRYPT_RANDOM_SIZE	(256)
#define VUC_PROTECT_PLAINTEXT_SIZE			(128)
#define VUC_PROTECT_PLAINTEXT_OFFSET_IN_32BYTE	(4)
#endif /* PS5017_EN */

#define VUC_PROTECT_DEBUG_EACH_16BYTE_PRINT_ADDRESS	(16)
#define VUC_PROTECT_MAX_VERIFY_FAIL_COUNT	(8)
#define VUC_PROTECT_DECODE_COMMAND_SIZE		(16) //DWORD 12~15 (Starting from Feature)
#define VUC_PROTECT_TIMEOUT_LIMIT_IN_SECOND	(10)

typedef struct {
	U8 ubEncryptRandom[VUC_PROTECT_ENCRYPT_RANDOM_SIZE];
	U8 ubDummyRandom[VUC_PROTECT_GEN_RANDOM_SIZE - VUC_PROTECT_ENCRYPT_RANDOM_SIZE - VUC_PROTECT_ENCRYPT_CRC_SZIE];

	U32 ulCRC;
}
VucProtectReceiveEncryptionData_t;
TYPE_SIZE_CHECK(VucProtectReceiveEncryptionData_t, VUC_PROTECT_GEN_RANDOM_SIZE);

#if PS5017_EN
typedef struct {
	U8 ubEncryptRandom[VUC_PROTECT_ENCRYPT_RANDOM_SIZE - VUC_PROTECT_ENCRYPT_CRC_SZIE]; // 508B
	U32 ulCRC; // 4B
}
VucProtectSendEncryptionData_t;
TYPE_SIZE_CHECK(VucProtectSendEncryptionData_t, VUC_PROTECT_ENCRYPT_RANDOM_SIZE);
#else /* PS5017_EN */
typedef struct {
	U8 ubEncryptRandom[VUC_PROTECT_ENCRYPT_RANDOM_SIZE];  // 256B
	U8 ubDummyRandom[VUC_PROTECT_GEN_RANDOM_SIZE - VUC_PROTECT_ENCRYPT_RANDOM_SIZE - VUC_PROTECT_ENCRYPT_CRC_SZIE]; // 252B

	U32 ulCRC; // 4B
}
VucProtectSendEncryptionData_t;
TYPE_SIZE_CHECK(VucProtectSendEncryptionData_t, VUC_PROTECT_GEN_RANDOM_SIZE);
#endif /* PS5017_EN */

AOM_VUC void VUCProtectInit(VUCProtect_t *pVucProtect, U8 ubProtectLevelDefault, const SecureKeyInfo_t *pSecureKeyInfo);
AOM_SECURITY U32 VUCProtectEngineeringModeKeyHandle(VUCProtect_t *pVucProtect, U32 ulKeyStatus);
AOM_VUC U8 VUCProtectPassList(U8 ubAlwaysPassOP);
AOM_VUC void VUCProtectFWDecryptCommand(U32 ulBufferAdr, VUC_OPT_HCMD_PTR pCmd);
AOM_SECURITY void VUCProtectSendHandshakeRequest(VUC_OPT_HCMD_PTR_t pCmd);
AOM_SECURITY void VUCProtectReceiveEncryptionData(VUC_OPT_HCMD_PTR_t pCmd);
AOM_SECURITY void VUCProtectSendEncryptionData(VUC_OPT_HCMD_PTR_t pCmd);
AOM_SECURITY void VUCProtectDisable(VUC_OPT_HCMD_PTR_t pCmd);
#if (PS5017_EN || BURNER_MODE_EN || RDT_MODE_EN)
AOM_SECURITY void VUCProtectDecryptCommand(VUC_OPT_HCMD_PTR_t pCmd);
#endif /* (PS5017_EN || BURNER_MODE_EN || RDT_MODE_EN) */

#endif /*_VUC_PROTECT_H_*/
