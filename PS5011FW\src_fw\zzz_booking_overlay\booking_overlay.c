#include "aom/aom_api.h"

ARM_OVERLAY(0) void  BookingOverlay0(void)
{

}

ARM_OVERLAY(1) void  BookingOverlay1(void)
{

}

ARM_OVERLAY(2) void  BookingOverlay2(void)
{

}

ARM_OVERLAY(3) void BookingOverlay3(void)
{

}

ARM_OVERLAY(4) void BookingOverlay4(void)
{

}

ARM_OVERLAY(5) void BookingOverlay5(void)
{

}

ARM_OVERLAY(6) void BookingOverlay6(void)
{

}

ARM_OVERLAY(7) void BookingOverlay7(void)
{

}

ARM_OVERLAY(8) void BookingOverlay8(void)
{

}

ARM_OVERLAY(9) void BookingOverlay9(void)
{

}

ARM_OVERLAY(10) void BookingOverlay10(void)
{

}

ARM_OVERLAY(11) void BookingOverlay11(void)
{

}

ARM_OVERLAY(12) void BookingOverlay12(void)
{

}

ARM_OVERLAY(13) void BookingOverlay13(void)
{

}

ARM_OVERLAY(14) void BookingOverlay14(void)
{

}

ARM_OVERLAY(15) void BookingOverlay15(void)
{

}

ARM_OVERLAY(16) void BookingOverlay16(void)
{

}

ARM_OVERLAY(17) void BookingOverlay17(void)
{

}
ARM_OVERLAY(18) void BookingOverlay18(void)
{

}
ARM_OVERLAY(19) void BookingOverlay19(void)
{

}
ARM_OVERLAY(20) void BookingOverlay20(void)
{

}
ARM_OVERLAY(21) void BookingOverlay21(void)
{

}
ARM_OVERLAY(22) void BookingOverlay22(void)
{

}