#ifndef _FTL_API_H_
#define _FTL_API_H_

#include "setup.h"
#include "typedef.h"
#include "fw_vardef.h"
#include "common/fw_common.h"
#include "table/initinfo_vt/initinfo_vt.h"
#include "aom/aom_api.h"
#include "flash_spec.h"
#include "hal/cop0/cop0_cmd.h"
#include "hal/cpu/cpu_api.h"
#include "ftl/ftl_readverify_api.h"

#define FREE_POOL_PSEUDO_NODE_NUM	(2)

#define SAVE_VT_MODE_OPTION		(1)   //0:No record Old VT Child 1: record Old VT Child

#define GC_FREEUNIT_NUM_THRESHOLD (1 + FREEPOOL_MINI_QUEUE_SIZE)

#define CHECK_AVOID_MIXPLANE_THRESHOLD	(10)

#define STATUS_HOST_LOAD_DATA_SUCCESS (0)
#define STATUS_HOST_LOAD_DATA_FAIL (1)

#if (NVME == HOST_MODE)
#define IDX_MAX_LIFETIME (128)
#elif (USB == HOST_MODE) /* (NVME == HOST_MODE) */
#define IDX_MAX_LIFETIME (32)
#else
#define IDX_MAX_LIFETIME (33)
#endif
#define FTL_RECORD_CMD_TIMEOUT_THRESHOLD_1	(5 * 1000) // 5s
#define FTL_RECORD_CMD_TIMEOUT_THRESHOLD_2	(10 * 1000) // 10s
#define FTL_RECORD_CMD_TIMEOUT_THRESHOLD_3	(19 * 1000) // 19s
#define FTL_RECORD_CMD_TIMEOUT_THRESHOLD_4	(28 * 1000) // 28s
#define FTL_RECORD_CMD_TIMEOUT_THRESHOLD_5	(32 * 1000) // 32s
#define FTL_RECORD_CMD_TIMEOUT_THRESHOLD_6	(58 * 1000) // 58s
#define FTL_RECORD_CMD_TIMEOUT_THRESHOLD_7	(62 * 1000) // 62s
#define FREEPOOL_MINI_QUEUE_SIZE	(3)
#define VT_CHILD_NUM 			  (1)
#define VT_OLD_CHILD_NUM 			  (1)
#define INIT_INFO_NUM 		  		(1)
#define OPEN_BLOCK_RAIDECCMAP_UNIT_NUM	(1)
#define META_RESERVE_NUM 	  	(1)
#define GCGR_RESERVE_NUM			(4)
#define ERROR_RESERVE_NUM	(2)
#define DRIVE_LOG_RESERVE_NUM	(1)
#define HOST_INFO_NUM				(2)
#define OPEN_BLOCK_RAIDECCMAP_UNIT_RESERVE_NUM		(1)
#define WL_OP_RESERVE_NUM (1)	//為了Table GC在Worst case可以拿到 EC 第三大 Unit(WL在目前case只能挑第三大), 須滿足WL_OP_RESERVE_NUM + ERROR_RESERVE_NUM + OPEN_BLOCK_RAIDECCMAP_UNIT_NUM + META_RESERVE_NUM >= FREEPOOL_MINI_QUEUE_SIZE + 2
#define PARITY_FOR_GCGR_UNIT_RESERVE_NUM (1)
#define D1_ERROR_RESERVE_NUM	(2)	// 考慮改用百分比,先暫用.
#define D1_META_RESERVE_NUM 	  	(1)
#define PH_RESERVE_NUM				(1)
#define WORDLINE_FOLDING_RESERVE_NUM				(2)

#define FTL_FREEPOOL_HEAP_ZONE_HAVE_SIX_UNIT	(6)
#define FTL_FREEPOOL_HEAP_ZONE_HAVE_FIVE_UNIT	(5)
#define FTL_FREEPOOL_HEAP_ZONE_HAVE_FOUR_UNIT	(4)
#define FTL_FREEPOOL_HEAP_ZONE_HAVE_THREE_UNIT	(3)
#define FTL_FREEPOOL_HEAP_ZONE_HAVE_TWO_UNIT	(2)

#define PTE_BMP_PLANE_NUM	  (1)
#define PMD_BMP_PLANE_NUM	  (1)
#if (RAIDECC_ENCODE_EN)
#define RS_LAST_PARITY_PLANE	  (1)
#else /* (RAIDECC_ENCODE_EN) */
#define RS_LAST_PARITY_PLANE	  (0)
#endif /* (RAIDECC_ENCODE_EN) */
#define FIRST_HIT_TABLE_4K_NUM	  (4)

#define INVALID_PLANE_READ	(0xFFFF)
#define INVALID_DSA_COUNT	(0xFFFF)

#define READ_VERIFY_SQ_CNT		(8)
#define MAX_TAG_CQ (256)

#define MAX_WORDLINE_GROUP_NUM (8)

#define FTL_MAX_GC_SRC_UNIT_NUM	(8)

#define FTL_READ_MIN_SEQ_4K_CNT	(8)
#define FTL_READ_MAX_IDLE_TIME_DEFAULT (8)
#define FTL_WRITE_MAX_IDLE_TIME_DEFAULT (64)
#define FTL_READ_HIGH_QUEUE_CNT	(8)
#define FTL_READ_MIN_QUEUE_CNT  (2)
#define FTL_READ_COP0_HIGH_TIME_OUT_MODE	(1)
#define FTL_READ_COP0_MIDDLE_TIME_OUT_MODE	(0)

/*
 * In N18 FW, D3_GR_ALWAYS_SLC_EN is enable, SLCPoolNum is discarding
 * N18 Disk Size Config: 500GB,1000GB,2000GB
 * For N18 3%OP config:500GB*3%OP=15GB, 1000GB*3%OP=30GB, 2000GB*3%OP=60GB
 * BGGCStopThreshold must be smaller than 15GB
 * and 13GB for PCMARK8 performance test
 */
#define FTL_BG_SLC_START_THRESHOLD_DEFAULT      (SIZE_1GB / SIZE_4KB)
#define FTL_SLC_POOL_DEFAULT       				(SIZE_8GB / SIZE_4KB)

#define FTL_LARGE_LTR					(0x90039003)
#define FTL_SMALL_LTR					(0x88468846)
#define FTL_BG_FLUSH_THRESHOLD_IN_APST       (1000)
#define FTL_BG_LTR_THRESHOLD		(60)
#define FTL_IO_UPDATE_INTERVAL			(15) //ms

#define FTL_C6_ERASE_MAX_UNIT_NUM               (10)
#define FTL_BICS3_TLC_ERASE_WORKAROUND_C6_ERASE_EN_INFO_BLOCK_BIT (BIT0)
#define FTL_BICS3_TLC_ERASE_WORKAROUND_WRITE_DUMMY_EN_INFO_BLOCK_BIT (BIT1)

#define PROGRAM_DONE_READ_VERIFY_EN (TRUE)

#define FTL_SHIFT_TIME_STAMP					(2)
#define FTL_MASK_TIME_STAMP_FOR_FWSET			(0xFFFF)
#define FTL_SHIFT_TIME_STAMP_FOR_SECOND_FWSET	(16)
#define FTL_TIME_STAMP_ENTRY_CNT				(2)

#define FTL_DATA_IS_4K_READ                   (RCQ_CMD_HEAD | RCQ_CMD_END)  // ReadCQInfo_t (btCmdEnd | BtCmdHead)
#define FTL_CONTINUE_4K_READ_TO_FLUSH_THRESHOLD ((USB == HOST_MODE) ? 30 : 10)

#if PS5021_EN
#define RCQ_STATE_2_SHIFT (4)
#define M_FTL_XFER_DATA_OUT_SET_RCQ_STATE(RCQPTR, STATE) do{\
			(RCQPTR)->DW1.RCQState = ((STATE) & BIT_MASK(RCQ_STATE_2_SHIFT)); \
			(RCQPTR)->DW3.RCQState2 = ((STATE) >> RCQ_STATE_2_SHIFT); \
			}while(0)
#define M_FTL_XFER_DATA_OUT_GET_RCQ_STATE(RCQPTR) (RCQOffset_t)((RCQPTR)->DW1.RCQState | ((RCQPTR)->DW3.RCQState2 << RCQ_STATE_2_SHIFT))
#define M_FTL_XFER_DATA_IN_GET_CTAG_FROM_WCQ(WCQPTR) (CTAG_t)((WCQPTR)->DW1.B.ubCTag | ((WCQPTR)->DW1.B.btCTagBit8 << BMU_CTAG_BIT_EXTEND_START))
#else /* PS5021_EN */
#define M_FTL_XFER_DATA_OUT_SET_RCQ_STATE(RCQPTR, STATE) ((RCQPTR)->DW1.RCQState = (STATE))
#define M_FTL_XFER_DATA_OUT_GET_RCQ_STATE(RCQPTR) (RCQOffset_t)((RCQPTR)->DW1.RCQState)
#define M_FTL_XFER_DATA_IN_GET_CTAG_FROM_WCQ(WCQPTR) (CTAG_t)((WCQPTR)->DW1.B.ubCTag)
#endif /* PS5021_EN */

#define FTL_BG_RELEASE_UNIT_ADVANCE_MIX_RATIO_MININUM	(0x100) // VC ratio must > 1%
#define FTL_BG_RELEASE_UNIT_ADVANCE_FREE_UNIT_RATIO		(64) //Base = 256  64 means  25%
#define FTL_BG_RELEASE_UNIT_ADVANCE_VALID_COUNT_RATIO	(204) //Base = 256 204 means 80%
#define FTL_BG_RELEASE_UNIT_ADVANCE_VALID_CNT_FULL_RATIO	(256) //256 means 100%

#define FTL_SLC_POOL_DEFAULT_WRITE_RANGE_THRESHOLD_FOR_EXTRA_CONDITION	(0x200000)	// 8GB
#define FTL_SLC_POOL_FREE_UNIT_NUM_THRESHOLD							(25)		// Max Unit Num could be freed in one GC = 24. Set 25 to make sure we will not use SLC Pool when random write is doing

typedef enum {
	GROUP_CE_DEAFULT_MODE = 0,
	GROUP_CE_COPY_UNIT_MODE
} GroupCEModeEnum_t;

#define M_FTL_GET_TIME_STAMP_FOR_FIRST_FWSET(T)		((T) & FTL_MASK_TIME_STAMP_FOR_FWSET)
#define M_FTL_GET_TIME_STAMP_FOR_SECOND_FWSET(T)	(((T) >> FTL_SHIFT_TIME_STAMP_FOR_SECOND_FWSET) & FTL_MASK_TIME_STAMP_FOR_FWSET)

#define M_FTL_IS_LBA_ALIGN_4K(LBA)					(0 == ((LBA) & (SECTORS_PER_4K_MASK)))
#define M_FTL_IS_LCA_ALIGN_PTE(LCA)					(0 == ((LCA) & (PTE_ENTRY_NUM_MASK)))
#define M_FTL_PMD_TO_LBA(PMD)		((PMD) << (SECTORS_PER_PMD_LOG))
#define M_FTL_LBA_TO_NSID(LBA)				(((LBA) >> (NSID_SHIFT_IN_LBA)) & (NSID_MASK))
#define M_FTL_LBA_TO_LCA_FLOOR(LBA)					((LBA) >> (SECTORS_PER_4K_LOG))
#define M_FTL_LBA_TO_PTE_FLOOR(LBA)		(((LBA) >> (SECTORS_PER_PTE_LOG)))
#define M_FTL_LBA_TO_PMD_FLOOR(LBA)		(((LBA) >> (SECTORS_PER_PMD_LOG)))
#define M_FTL_LBA_TO_LCA_CEILING(LBA)	((((LBA) + (SECTORS_PER_4K) - 1) >> (SECTORS_PER_4K_LOG)))
#define M_FTL_LBA_TO_PTE_CEILING(LBA)	((((LBA) + (SECTORS_PER_PTE) - 1) >> (SECTORS_PER_PTE_LOG)))
#define M_FTL_LBA_TO_PMD_CEILING(LBA)	((((LBA) + (SECTORS_PER_PMD) - 1) >> (SECTORS_PER_PMD_LOG)))
#define M_FTL_LCA_TO_NSID(LCA)				(((LCA) >> (NSID_SHIFT_IN_LCA)) & (NSID_MASK))
#define M_FTL_LCA_TO_PTE_FLOOR(LCA)					((LCA) >> (PTE_ENTRY_NUM_LOG))
#define M_FTL_LCA_TO_PTE_CEILING(LCA)				(((LCA) + (PTE_ENTRY_NUM) - 1) >> (PTE_ENTRY_NUM_LOG))
#define M_FTL_LCA_TO_PMD_FLOOR(LCA)					((LCA) >> (LCA_PER_PMD_LOG))
#define M_FTL_LCA_TO_PMD_CEILING(LCA)				(((LCA) + (LCA_PER_PMD) - 1) >> (LCA_PER_PMD_LOG))
#define M_FTL_PTE_TO_PMD_FLOOR(PTE)					((PTE) >> (PMD_ENTRY_NUM_LOG))
#define M_FTL_PTE_TO_PMD_CEILING(PTE)				(((PTE) + (PTE_ENTRY_NUM) - 1) >> (PTE_ENTRY_NUM_LOG))
#define M_FTL_IS_IN_SAME_LCA(LBA1, LBA2)			(M_FTL_LBA_TO_LCA_FLOOR(LBA1) == M_FTL_LBA_TO_LCA_FLOOR(LBA2))
#define M_FTL_IS_IN_SAME_PTE(LCA1, LCA2)			(M_FTL_LCA_TO_PTE_FLOOR(LCA1) == M_FTL_LCA_TO_PTE_FLOOR(LCA2))

#define M_SET_READVERIFY_EVENT(Mode) 	do { \
											gpVT->ReadVerify.ubNowDoingEvent |= (BIT(Mode));\
										} while(0)

#define M_CLEAR_READVERIFY_EVENT(Mode) 	do { \
											gpVT->ReadVerify.ubNowDoingEvent &= ~(BIT(Mode));\
										} while(0)

#define M_SET_BITMAP(pubBitmap, Index) 	do { \
											pubBitmap[(Index) >> BITS_PER_BYTE_LOG] |= BIT((Index) & BITS_PER_BYTE_MASK);\
										} while(0)

#define M_CLEAR_BITMAP(pubBitmap, Index) 	do { \
											pubBitmap[(Index) >> BITS_PER_BYTE_LOG] &= ~(BIT((Index) & BITS_PER_BYTE_MASK));\
										} while(0)

#define M_CHK_BITMAP(pubBitmap, Index) ((pubBitmap[(Index) >> BITS_PER_BYTE_LOG] & BIT((Index) & BITS_PER_BYTE_MASK)) ? TRUE : FALSE)

#define M_GET_IS_SLC_BY_UNIT(UNIT) (UNIT.B.btSingleSLC || UNIT.B.btIsD1Unit)
#define M_GET_IS_SLC_BY_VC(UNIT_INDEX) (gpulVC[(UNIT_INDEX)].B.btD1 || gpulVC[(UNIT_INDEX)].B.btSLCPool || gpulVC[(UNIT_INDEX)].B.btTableUnit) //VT VTC InitInfo Unit 嚙踝蕭嚙踝蕭嚙?
#define M_GET_IS_SLC_TABLE_BY_VC(UNIT_INDEX) (gpulVC[(UNIT_INDEX)].B.btTableUnit)
#define M_GET_IS_SLC_WITHOUT_TABLE_BY_VC(UNIT_INDEX) ((gpulVC[(UNIT_INDEX)].B.btD1 || gpulVC[(UNIT_INDEX)].B.btSLCPool) && (!gpulVC[(UNIT_INDEX)].B.btTableUnit))
#define M_GET_IS_SINGLESLC_BY_UNIT(UNIT) (UNIT.B.btSingleSLC)
#define M_GET_IS_SMALL_UNIT_BY_VC(UNIT_INDEX) ((gpulVC[(UNIT_INDEX)].B.btD1 && !D1_BINDING_EN) || gpulVC[(UNIT_INDEX)].B.btSLCPool || gpulVC[(UNIT_INDEX)].B.btTableUnit)

#define M_GET_SHARE_WORDLINE_PLANE_NUM() (gubKeepPLBPlaneNum * gubCENumber * 4)

#if (MICRON_FSP_EN)
#define M_GET_VCA_START_FROM_PLANEBANK(PLANEBANK, SLC)	(((PLANEBANK) >> gPCARule_Plane.ubBit_No) * (1 << gPCARule_Plane.ubBit_No)) + ((PLANEBANK & gPCARule_Plane.ulMask))
#define M_GET_PLANE_BANK_FROM_VCA(VCA, SLC) (FTLGetPlaneBankFromVCA(VCA, SLC))
#else /* (MICRON_FSP_EN) */
#define M_GET_PCA_START_FROM_PLANEBANK(PLANEBANK, IS_SINGLE_SLC)	(((PLANEBANK) >> gPCARule_Plane.ubBit_No) * (((IS_SINGLE_SLC) ? 1 : gubLMUNumber) << gPCARule_Plane.ubBit_No)) + ((PLANEBANK & gPCARule_Plane.ulMask))
#define M_GET_PLANE_BANK_FROM_PCA(PCA, IS_SINGLE_SLC) ((((((PCA) & gul4kEntrysPerUnitAlignMask) >> gub4kEntrysPerPlaneLog) % (gubPlanesPerSuperPage * ((IS_SINGLE_SLC) ? 1 : gubLMUNumber))) / (gubBurstsPerBank * ((IS_SINGLE_SLC) ? 1 : gubLMUNumber))) * gubBurstsPerBank + ((((PCA) & gul4kEntrysPerUnitAlignMask) >> gub4kEntrysPerPlaneLog) & gubBurstsPerBankMask))
#endif /* (MICRON_FSP_EN) */
#define M_GET_UNIT_VALUE_FROM_PCA(PCA)   ((PCA) >> gub4kEntrysPerUnitAlignLog)
#define M_GET_ENTRY_VALUE_FROM_PCA(PCA)  ((PCA) & gul4kEntrysPerUnitAlignMask)
#define M_GET_PLANES_PER_UNIT(IS_SINGLE_SLC)   ((IS_SINGLE_SLC) ? gulFastPagePlanesPerUnit : gulPlanesPerUnit)
#define M_GET_PLANES_PER_UNIT_FROM_UNIT(UNIT)   (M_GET_IS_SINGLESLC_BY_UNIT(UNIT) ? gulFastPagePlanesPerUnit : gulPlanesPerUnit)
#define M_GET_GR_FSP_PLANE_NUM()			(M_GET_IS_SLC_BY_UNIT(gpVT->GR.uwUnit[gpVT->GR.ubUnitIndex]) ? gubFastPageKeepPLBPlaneNum : gubKeepPLBPlaneNum)
#if ODD_CE_EN
#define M_GET_GLOBAL_CE(PCA) (((PCA & gul4kEntrysPerUnitAlignMask) >> (gubBurstsPerBankLog + gub4kEntrysPerPlaneLog)) % gubCEsPerSuperPage)
#else /* ODD_CE_EN */
#define M_GET_GLOBAL_CE(PCA) ((PCA >> (gubBurstsPerBankLog + gub4kEntrysPerPlaneLog)) & gubCEsPerSuperPageMask)
#endif /* ODD_CE_EN */

#if(FW_CATEGORY_FLASH == FLASH_SANDISK_BICS6_TLC || FW_CATEGORY_FLASH == FLASH_SANDISK_BICS8_TLC || FW_CATEGORY_FLASH == FLASH_SANDISK_BICS6_QLC) //zerio BICS8 Add//zerio bics6 qlc add
#define GET_FPAGE(page) ((page / 5) * 8 + (page % 5))
#else
#define GET_FPAGE(page) (page)
#endif

#define M_GET_PCA(LUN, BLOCK, PAGE, CE, CH, PLANE, ENTRY, PCA_RULE) (((LUN) << gPCARule_LUN.ubShift[PCA_RULE]) | \
						((BLOCK) << gPCARule_Block.ubShift[PCA_RULE]) | \
						((GET_FPAGE(PAGE)) << gPCARule_Page.ubShift[PCA_RULE]) | \
						((PLANE) << gPCARule_Plane.ubShift[PCA_RULE]) | \
						((CE) << gPCARule_Bank.ubShift[PCA_RULE]) | \
						((CH) << gPCARule_Channel.ubShift[PCA_RULE]) + (ENTRY))

#define M_GET_VCA(UNIT, PAGE, ENTRY) (((UNIT) << gub4kEntrysPerUnitAlignLog) + ((PAGE)<<gub4kEntrysPerPlaneLog) + (ENTRY))
#define M_GET_VCA_ENTRY(UNIT, ENTRY) (((UNIT) << gub4kEntrysPerUnitAlignLog) + (ENTRY))
#define M_GET_VCA_PLANE(UNIT, PAGE) (((UNIT) << gub4kEntrysPerUnitAlignLog) + ((PAGE)<<gub4kEntrysPerPlaneLog))

#if VS_SIM_EN
#define M_GET_DSA_ADDR() (gSt1blkStruct.DSA->l2p)
#else
#define M_GET_DSA_ADDR() (COP1_SRAM_ST1_DSA_BASE_ADDR)
#endif
#if (MULTI_PLANE_PROTECTION && RAIDECC_ENCODE_EN)
#define M_GET_PTE_BMP_BASE_PLANE(IS_SINGLE_SLC)   (M_GET_PLANES_PER_UNIT(IS_SINGLE_SLC) - PTE_BMP_PLANE_NUM - ((IS_SINGLE_SLC) ? (PLANE_BANK_NUM) : (gubLMUNumber * PLANE_BANK_NUM)))
#else /* MULTI_PLANE_PROTECTION && RAIDECC_ENCODE_EN */
#define M_GET_PTE_BMP_BASE_PLANE(IS_SINGLE_SLC)   (M_GET_PLANES_PER_UNIT(IS_SINGLE_SLC) - PTE_BMP_PLANE_NUM - RS_LAST_PARITY_PLANE)//因為有RS
#endif /* MULTI_PLANE_PROTECTION && RAIDECC_ENCODE_EN */
#if (RAIDECC_ENCODE_EN)
#define M_GET_PTE_BMP_BASE_PLANE_FOR_TABLE_UNIT()	(M_GET_PLANES_PER_UNIT(TRUE) - PTE_BMP_PLANE_NUM - RS_LAST_PARITY_PLANE)
#define M_GET_PTE_BMP_BASE_PLANE_FROM_UNIT(UNIT)   (M_GET_PLANES_PER_UNIT_FROM_UNIT(UNIT) - PTE_BMP_PLANE_NUM - RS_LAST_PARITY_PLANE)//嚙稽嚙踝蕭嚙踝蕭RS
#else /* (RAIDECC_ENCODE_EN) */
// Temp solution for data
#define M_GET_PTE_BMP_BASE_PLANE_FOR_TABLE_UNIT()	(M_GET_PLANES_PER_UNIT(TRUE) - PTE_BMP_PLANE_NUM - 1)  // Table still has parity
#define M_GET_PTE_BMP_BASE_PLANE_FROM_UNIT(UNIT)   (M_GET_PLANES_PER_UNIT_FROM_UNIT(UNIT) - PTE_BMP_PLANE_NUM - RS_LAST_PARITY_PLANE)//嚙稽嚙踝蕭嚙踝蕭RS
#endif /* (RAIDECC_ENCODE_EN) */
#if XZIP_EN
#define M_GET_DATA_4KENTRY_PER_UNIT(IS_SINGLE_SLC)	(((M_GET_PLANES_PER_UNIT(IS_SINGLE_SLC)) - PTE_BMP_PLANE_NUM - (FIRST_HIT_TABLE_4K_NUM >> gub4kEntrysPerPlaneLog) - (((IS_SINGLE_SLC) == 0) ? gRaidECCMap.ParityMap.uwParityPagesPerUnit : gRaidECCMap.ParityMap.uwSLCParityPagesPerUnit) - ((GC_B47R_WEAK_PAGE_FILL_DUMMY) ? (gRaidECCMap.uwWeakDataPageCnt) : 0)) << gub4kEntrysPerPlaneLog)//因為有RS
#if (MICRON_FSP_EN)
#define M_GET_LAST_DATA_PLANE_INDEX(SLC)    ( M_GET_PTE_BMP_BASE_PLANE(SLC) -(FIRST_HIT_TABLE_4K_NUM >> gub4kEntrysPerPlaneLog)-(((gubLMUNumber > 1) && ((SLC) == 0)) ? (gubRSParityShiftForXZIPFirstHitTable + gubRSParityShiftForLastDataPlane) : 0))
#else /* (MICRON_FSP_EN) */
#define M_GET_LAST_DATA_PLANE_INDEX(IS_SINGLE_SLC)    ( M_GET_PTE_BMP_BASE_PLANE(IS_SINGLE_SLC) -(FIRST_HIT_TABLE_4K_NUM >> gub4kEntrysPerPlaneLog)-(((gubLMUNumber > 1) && ((IS_SINGLE_SLC) == 0)) ? 2 : 0))
#endif /* (MICRON_FSP_EN) */
#else
#define M_GET_DATA_4KENTRY_PER_UNIT(IS_SINGLE_SLC)	(((M_GET_PLANES_PER_UNIT(IS_SINGLE_SLC)) - PTE_BMP_PLANE_NUM - (((IS_SINGLE_SLC) == 0) ? gRaidECCMap.ParityMap.uwParityPagesPerUnit : gRaidECCMap.ParityMap.uwSLCParityPagesPerUnit) - ((GC_B47R_WEAK_PAGE_FILL_DUMMY) ? (gRaidECCMap.uwWeakDataPageCnt) : 0)) << gub4kEntrysPerPlaneLog)//嚙稽嚙踝蕭嚙踝蕭RS
#define M_GET_LAST_DATA_PLANE_INDEX(IS_SINGLE_SLC)     (M_GET_PTE_BMP_BASE_PLANE(IS_SINGLE_SLC)-(((gubLMUNumber > 1) && ((IS_SINGLE_SLC) == 0)) ? 1 : 0))
#endif
#define M_GET_TABLE_4KENTRY_PER_UNIT()	(((M_GET_PLANES_PER_UNIT(1)) - PMD_BMP_PLANE_NUM - gRaidECCMap.uwTableParityPagesPerUnit) << gub4kEntrysPerPlaneLog)//因為有RS
#define M_GET_COARSE_NEED_TABLE_UNIT()	(CEILING_DIV((MAX_PTE_4K_NUM_IN_DSA_ABOVE_32GB_SAMPLE + guwTotalPMDNum), (M_GET_TABLE_4KENTRY_PER_UNIT())))

#define M_GET_GRCNT_IN_GRLIST() (gpVT->GR.ubUnitIndex + 1)

#define M_GET_BLK_VALUE_FROM_FSA(PCA, PCA_RULE_MODE)   (((PCA) >> gPCARule_Block.ubShift[PCA_RULE_MODE]) & gPCARule_Block.ulMask)

#define M_SET_ANDES_SEQUENTIAL_READ(IS_SEQUENTIAL) do {\
		gpComm2Andes_Info->ubTellAndesSeqR = IS_SEQUENTIAL;\
		gubTellAndesSeqReadEn = IS_SEQUENTIAL;\
	} while(0)

#define M_SET_ANDES_SEQUENTIAL_WRITE(IS_SEQUENTIAL) do {\
		gpComm2Andes_Info->ubTellAndesSeqW = IS_SEQUENTIAL;\
		gubTellAndesSeqWriteEn = IS_SEQUENTIAL;\
	} while(0)

#if (MULTI_PLANE_PROTECTION)
#define M_GET_FIRST_HIT_TABLE_BASE_PLANE(IS_SINGLE_SLC)  (M_GET_PTE_BMP_BASE_PLANE(IS_SINGLE_SLC) - 1)
#else /* MULTI_PLANE_PROTECTION */
#define M_GET_FIRST_HIT_TABLE_BASE_PLANE(IS_SINGLE_SLC)  ((IS_SINGLE_SLC) ? (M_GET_PTE_BMP_BASE_PLANE(IS_SINGLE_SLC) - ((gub4kEntrysPerPlane ==2) ? 2 : 1)) : gulFirstHitTableBasePlane);
#endif /* MULTI_PLANE_PROTECTION */
#define M_GET_PLANE_INDEX_FROM_PCA(PCA)	 (((PCA) & gul4kEntrysPerUnitAlignMask) >> gub4kEntrysPerPlaneLog)
#define M_GET_PAGE_FROM_SUPERPAGE(SUPERPAGE) M_MUL((SUPERPAGE) , gubPlanesPerSuperPage)
#define M_GET_SUPERPAGE_FROM_PAGE(PAGE) M_DIV((PAGE) , gubPlanesPerSuperPage)
#if VS_SIM_EN
#define M_GET_E3D4K_FROM_PLB(PB_ADDR, LAST_SECTOR_OFFSET)	(0x556677)
#else /* VS_SIM_EN */
#define M_GET_E3D4K_FROM_PLB(PB_ADDR, LAST_SECTOR_OFFSET)	((*((volatile U32 *)(M_PB_TO_ADDR(PB_ADDR) + (((U32)LAST_SECTOR_OFFSET) << 9) - 8))) & BIT_MASK(24))
#endif /* VS_SIM_EN */
#define BITMAP_64_LOG		(6)
#define BITMAP_64_MASK		(64 - 1)

#define FTL_PTEBMP_RANGE_MASK			(BIT_MASK(17))	//對應16K Bytes PTEBMP (容納512G Data的LCA)
#define FTL_PTEBMP_GROUP_MASK			(BIT_MASK(15))	//對應4K Bytes PTEBMP (會帶一組FW_SET)
#define FTL_PTEBMP_SUBGROUP_MASK		(BIT_MASK(13))	//對應1K Bytes PTEBMP (16bit中的4bit), 4個subgroup組成一個group(16bits)
#define FTL_PTEBMP_RANGE_MASK_SHIFT		(17)
#define FTL_PTEBMP_GROUP_MASK_SHIFT		(15)
#define FTL_PTEBMP_SUBGROUP_MASK_SHIFT	(13)
#define FTL_PTEBMP_SUBGROUP_NUM			(4)
#define FTL_PTEBMP_4TB_RANGE_MASK			(BIT_MASK(18))	//對應16K Bytes PTEBMP (容納1T Data的LCA)
#define FTL_PTEBMP_4TB_GROUP_MASK			(BIT_MASK(16))	//對應4K Bytes PTEBMP (會帶一組FW_SET)
#define FTL_PTEBMP_4TB_SUBGROUP_MASK		(BIT_MASK(14))	//對應1K Bytes PTEBMP (16bit中的4bit), 4個subgroup組成一個group(16bits)
#define FTL_PTEBMP_4TB_RANGE_MASK_SHIFT 	(18)
#define FTL_PTEBMP_4TB_GROUP_MASK_SHIFT 	(16)
#define FTL_PTEBMP_4TB_SUBGROUP_MASK_SHIFT	(14)
#define	FTL_GB_TO_MB					(1024)
#define	FTL_128GB_TO_MB					(128 * FTL_GB_TO_MB)
#define	FTL_256GB_TO_MB					(256 * FTL_GB_TO_MB)
#define	FTL_512GB_TO_MB					(512 * FTL_GB_TO_MB)
#define	FTL_1TB_TO_MB					(1024 * FTL_GB_TO_MB)
#define	FTL_2TB_TO_MB					(2048 * FTL_GB_TO_MB)
#define	FTL_4TB_TO_MB					(4096 * FTL_GB_TO_MB)

#define DISKSIZE_25GB_IN4K  (25 * 1024 * 1024 /4) //25GB * 1024 * 1024 / 4 
#define DISKSIZE_50GB_IN4K  (50 * 1024 * 1024 /4) //50GB * 1024 * 1024 / 4 
#define DISKSIZE_100GB_IN4K  (100 * 1024 * 1024 /4) //100GB * 1024 * 1024 / 4 
#define DISKSIZE_200GB_IN4K  (200 * 1024 * 1024 /4) //200GB * 1024 * 1024 / 4 
#define DISKSIZE_400GB_IN4K  (400 * 1024 * 1024 /4) //400GB * 1024 * 1024 / 4 
#define DISKSIZE_800GB_IN4K  (800 * 1024 * 1024 /4) //800GB * 1024 * 1024 / 4 


#define	M_FTL_SET_PTEBMP_FWSET_VALUE(puwFWSet, ulScanPTEIdx) do { \
	(guoDiskInfo.ulDiskSizeInMB <= FTL_2TB_TO_MB) ? \
	(puwFWSet[(((ulScanPTEIdx) & FTL_PTEBMP_RANGE_MASK) >> FTL_PTEBMP_GROUP_MASK_SHIFT)] |= (1 << ((ulScanPTEIdx) >> FTL_PTEBMP_RANGE_MASK_SHIFT) << ((((ulScanPTEIdx) & FTL_PTEBMP_GROUP_MASK) >> FTL_PTEBMP_SUBGROUP_MASK_SHIFT) * FTL_PTEBMP_SUBGROUP_NUM))) : \
	(puwFWSet[(((ulScanPTEIdx) & FTL_PTEBMP_4TB_RANGE_MASK) >> FTL_PTEBMP_4TB_GROUP_MASK_SHIFT)] |= (1 << ((ulScanPTEIdx) >> FTL_PTEBMP_4TB_RANGE_MASK_SHIFT) << ((((ulScanPTEIdx) & FTL_PTEBMP_4TB_GROUP_MASK) >> FTL_PTEBMP_4TB_SUBGROUP_MASK_SHIFT) * FTL_PTEBMP_SUBGROUP_NUM))); \
} while(0)

#define M_GET_FW_TIMER()					(guoOperationTime)

#if (MICRON_FSP_EN)
#if(IM_B17)
#define IM_B17_SECTION_1	12
#define IM_B17_SECTION_2	36
#define IM_B17_SECTION_3	60
#define IM_B17_SECTION_4	2220
#define IM_B17_SECTION_5	2268
#define IM_B17_SECTION_6	2292

#define M_IM_B17_BOTTOM_MLC_WORDLINES_UP_PAGE_START (2222)
#define M_IM_B17_BOTTOM_MLC_WORDLINES_UP_PAGE_DIVISOR (4)
#define M_IM_B17_BOTTOM_MLC_WORDLINES_UP_PAGE_REMAINDER (3)
#define M_IM_B17_CHECK_BOTTOM_MLC_WORDLINES(PAGE) ((M_IM_B17_BOTTOM_MLC_WORDLINES_UP_PAGE_START<PAGE)&&(M_IM_B17_BOTTOM_MLC_WORDLINES_UP_PAGE_REMAINDER==(PAGE%M_IM_B17_BOTTOM_MLC_WORDLINES_UP_PAGE_DIVISOR)))

#elif(IM_B27A)
#define IM_B27A_SECTION_1	18
#define IM_B27A_SECTION_2	54
#define IM_B27A_SECTION_3	90
#define IM_B27A_SECTION_4	2412
#define IM_B27A_SECTION_5	2466
#define IM_B27A_SECTION_6	2502
#define IM_B27A_SECTION_7	2538
#define IM_B27A_SECTION_8	2574
#define IM_B27A_SECTION_9	2592
#define IM_B27A_SECTION_10	2628
#define IM_B27A_SECTION_11	2646
#define IM_B27A_SECTION_12	2682
#define IM_B27A_SECTION_13	2736
#define IM_B27A_SECTION_14	5058
#define IM_B27A_SECTION_15	5112
#define IM_B27A_SECTION_16	5148

#elif(IM_B27B)
#define IM_B27B_SECTION_1	12
#define IM_B27B_SECTION_2	36
#define IM_B27B_SECTION_3	60
#define IM_B27B_SECTION_4	1608
#define IM_B27B_SECTION_5	1656
#define IM_B27B_SECTION_6	1692
#define IM_B27B_SECTION_7	1740
#define IM_B27B_SECTION_8	1752
#define IM_B27B_SECTION_9	1776
#define IM_B27B_SECTION_10		1788
#define IM_B27B_SECTION_11		1824
#define IM_B27B_SECTION_12		3372
#define IM_B27B_SECTION_13		3420
#define IM_B27B_SECTION_14		3456

#elif(IM_N18)
#define IM_N18_SECTION_1	12
#define IM_N18_SECTION_2	120
#define IM_N18_SECTION_3	3048

#define IM_TP_PAGE     ((IM_N28||IM_N48R)?3:0)
#define IM_XP_PAGE     ((IM_N28||IM_N48R)?2:1)
#define IM_UP_PAGE     ((IM_N28||IM_N48R)?1:2)
#define IM_LP_PAGE     ((IM_N28||IM_N48R)?0:3)

#define IM_N18_SECTION_1_RS_SHIFT	(0)
#define IM_N18_SECTION_2_RS_SHIFT	(2)
#define IM_N18_SECTION_3_RS_SHIFT	(0)
#define IM_N18_SECTION_4_RS_SHIFT	(2)

#define IM_B17_B27_LP_PAGE	0
#define IM_B17_B27_UP_PAGE	1
#define IM_B17_B27_XP_PAGE	2

#define IM_N48R_XP_RESIDUAL_PLANE_NUM 	(5)	//Find End Program of LUX (12 Planes)

#elif(IM_N28)
#define IM_N28_SECTION_1  (12)                            //     0-11 SLC
#define IM_N28_SECTION_2  (24)                            //     12-23       SLC Write L
#define IM_N28_SECTION_3  (132)                          //     24-131     TLC Write L U X
#define IM_N28_SECTION_4  (2292)                        //     132-2291 QLC Write L U X T[diff2]
#define IM_N28_SECTION_5  (2340)                        //     2292-2339       QLC Write L U X T[diff1]
#define IM_N28_SECTION_6  (2412)                        //     2340-2411       TLC Write L U X                      (same S_3)
#define IM_N28_SECTION_7  (2460)                        //     2412-2459       QLC Write L U X T[diff2]         (Same S_4)
#define IM_N28_SECTION_8  (2496)                        //     2460-2495       TLC Write L U X                               (same S_3)
#define IM_N28_SECTION_9  (2544)                        //     2496-2543       QLC Write L U X T[diff2]         (Same S_4)
#define IM_N28_SECTION_A (2580)                        //     2544-2579       TLC Write L U X                               (same S_3)
#define IM_N28_SECTION_B (4548)                        //     2580-4547       QLC Write L U X T[diff2]         (Same S_4)
//     4548-
#define IM_N28_SUB_BLK_NUM		(12)
#define IM_N28_SUB_BLK_IDX_0	(0)
#define IM_N28_SUB_BLK_IDX_4	(4)


#elif(IM_N48R)//zerio n48r add
#define IM_TP_PAGE     (3)
#define IM_XP_PAGE     (2)
#define IM_UP_PAGE     (1)
#define IM_LP_PAGE     (0)
#define IM_N48R_XP_RESIDUAL_PLANE_NUM 	(5)	//Find End Program of LUX (12 Planes)
#define IM_N48_SECTION_1	(8)
#define IM_N48_SECTION_2	(1400)
#define IM_N48_SECTION_3	(1416)
#define IM_N48_SECTION_4	(2808)
#define IM_N48_SECTION_5	(2816)
#define IM_N48_CELLNUM_SECTION_1	(4)
#define IM_N48_CELLNUM_SECTION_2	(352)
#define IM_N48_CELLNUM_SECTION_3	(360)
#define IM_N48_CELLNUM_SECTION_4	(708)
#define IM_N48_WORDLINE_SECTION_1	(1)
#define IM_N48_WORDLINE_SECTION_2	(88)
#define IM_N48_WORDLINE_SECTION_3	(90)
#define IM_N48_WORDLINE_SECTION_4	(177)
#define IM_N48_PROGRAM_ORDER_SECTION_1	(10)
#define IM_N48_PROGRAM_ORDER_SECTION_2	(706)
#define IM_N48_PROGRAM_ORDER_SECTION_3	(714)
#define IM_N48_PROGRAM_ORDER_SECTION_4	(1409)
#define IM_N48_SLC_WORDLINE_SECTION_1	(1)
#define IM_N48_SLC_WORDLINE_SECTION_2	(87)
#define IM_N48_SLC_WORDLINE_SECTION_3	(89)
#define IM_N48_SLC_WORDLINE_SECTION_4	(175)

#define IM_N48_2PASS_DIFF	(4)

#elif (IM_B37R)
#define IM_B37R_SECTION_1	(4)
#define IM_B37R_SECTION_2	(760)
#define IM_B37R_SECTION_3	(776)
#define IM_B37R_SECTION_4	(1532)
#define IM_B37R_SECTION_5	(1536)
#define IM_B37R_SUB_BLK_NUM		(4)
#define IM_B37R_SUB_BLK_IDX_0	(0)
#define IM_B37R_SUB_BLK_IDX_2	(2)
#define IM_B37R_LP_PAGE	0
#define IM_B37R_UP_PAGE	1
#define IM_B37R_XP_PAGE	2

#elif(IM_B47R)
#define IM_B47R_SECTION_1	(4)
#define IM_B47R_SECTION_2	(1048)
#define IM_B47R_SECTION_3	(1064)
#define IM_B47R_SECTION_4	(2108)
#define IM_B47R_SECTION_5	(2112)
#define IM_B47R_SUB_BLK_NUM		(4)
#define IM_B47R_SUB_BLK_IDX_0	(0)
#define IM_B47R_SUB_BLK_IDX_2	(2)
#define IM_B47R_LP_PAGE	0
#define IM_B47R_UP_PAGE	1
#define IM_B47R_XP_PAGE	2
#endif

#define UNIT_TYPE_GCGR   0
#define UNIT_TYPE_GR     1
#if IM_N28
#define FTL_MICRON_TLC_2ND_PASS_WINDOW_SIZE (8)
#else /*IM_N28*/
#define FTL_MICRON_TLC_2ND_PASS_WINDOW_SIZE (2)
#endif /* IM_N28*/

#define FTL_MICRON_B27A_LOWER_XPAGE		(0)
#define FTL_MICRON_B27B_LOWER_XPAGE		(2)  // B27B is X-U-L
#define FTL_MICRON_MAX_UNIT_NUM			(2000)
#define M_FTL_CLEAR_WORDLINE_BYPASS_INFO(UNIT)		do { \
	U32 *pulWordLineBypassInfo = (U32 *) DBUF_MICRON_WORDLINE_BYPASS_INFO; \
	pulWordLineBypassInfo[(UNIT)] = 0; \
} while (0)

#elif ((FW_CATEGORY_FLASH == FLASH_YMTC_EMS_QLC))//ems mst add--karl
#define IM_EMS_SECTION_1_RDT	        (18)
#define IM_EMS_SECTION_2_RDT	        (2706)
#define IM_EMS_SECTION_3_RDT	        (2778)
#define IM_EMS_SECTION_4_RDT	        (5466)
#define IM_EMS_SECTION_5_RDT	        (5520)
#define IM_EMS_CELLNUM_SECTION_1_RDT	(6)
#define IM_EMS_CELLNUM_SECTION_2_RDT	(678)
#define IM_EMS_CELLNUM_SECTION_3_RDT	(702)
#define IM_EMS_CELLNUM_SECTION_4_RDT	(1374)
#define IM_EMS_CELLNUM_SECTION_5_RDT	(1392)

#define IM_EMS_CELLNUM                  (1416)
#define IM_EMS_CELLNUM_SLC				(1392)

#elif (SAMSUNG_FSP_EN)
#if(FW_CATEGORY_FLASH == FLASH_SAMSUNG_V6_TLC)
#define IM_SSV6_SECTION_1_RDT			(16)
#define IM_SSV6_SECTION_2_RDT			(3064)
#define IM_SSV6_CELLNUM_SECTION_1_RDT	(8)
#define IM_SSV6_CELLNUM_SECTION_2_RDT	(1024)
#define IM_SSV6_CELLNUM					(1040)
#define IM_SAMSUNG_CELLNUM			    (1040)

#elif(FW_CATEGORY_FLASH == FLASH_SAMSUNG_V6P_TLC)
#define IM_SSV6P_SECTION_1_RDT			(32)
#define IM_SSV6P_SECTION_2_RDT			(3176)
#define IM_SSV6P_CELLNUM_SECTION_1_RDT	(16)
#define IM_SSV6P_CELLNUM_SECTION_2_RDT	(1064)
#define IM_SSV6P_CELLNUM					(1080)
#define IM_SAMSUNG_CELLNUM					(1080)
#define IM_SSV6P_CELLNUM_SLC				(1072)

#elif (FW_CATEGORY_FLASH == FLASH_SAMSUNG_V7_TLC)//Samsung v7 mst add--Reip
#define IM_SSV7_SECTION_1_RDT			(12)
#define IM_SSV7_SECTION_2_RDT			(1434)
#define IM_SSV7_SECTION_3_RDT			(1446)
#define IM_SSV7_SECTION_4_RDT			(1458)
#define IM_SSV7_SECTION_5_RDT			(1470)
#define IM_SSV7_SECTION_6_RDT			(3144)
#define IM_SSV7_SECTION_7_RDT			(3156)
#define IM_SSV7_CELLNUM_SECTION_1_RDT	(6)
#define IM_SSV7_CELLNUM_SECTION_2_RDT	(480)
#define IM_SSV7_CELLNUM_SECTION_3_RDT	(486)
#define IM_SSV7_CELLNUM_SECTION_4_RDT	(498)
#define IM_SSV7_CELLNUM_SECTION_5_RDT	(504)
#define IM_SSV7_CELLNUM_SECTION_6_RDT	(1062)
#define IM_SSV7_CELLNUM_SECTION_7_RDT	(1068)
#define IM_SSV7_CELLNUM					(1080)
#define IM_SAMSUNG_CELLNUM				(1080)

#elif (FW_CATEGORY_FLASH == FLASH_SAMSUNG_V8_TLC)//Samsung v8 mst add--Reip
#define IM_SSV8_SECTION_1_RDT			(12)
#define IM_SSV8_SECTION_2_RDT			(4224)
#define IM_SSV8_SECTION_3_RDT			(4236)
#define IM_SSV8_CELLNUM_SECTION_1_RDT	(6)
#define IM_SSV8_CELLNUM_SECTION_2_RDT	(1410)
#define IM_SSV8_CELLNUM_SECTION_3_RDT	(1416)
#define IM_SSV8_CELLNUM					(1428)
#define IM_SAMSUNG_CELLNUM				(1428)

#elif(FW_CATEGORY_FLASH == FLASH_SAMSUNG_V5_TLC)//Samsung v5 mst add--Jeffrey
#define IM_SSV5_SECTION_1_RDT			(8)
#define IM_SSV5_SECTION_2_RDT			(1088)
#define IM_SSV5_SECTION_3_RDT			(1096)
#define IM_SSV5_CELLNUM_SECTION_1_RDT	(4)
#define IM_SSV5_CELLNUM_SECTION_2_RDT	(364)
#define IM_SSV5_CELLNUM_SECTION_3_RDT	(368)
#define IM_SSV5_CELLNUM					(376)
#define IM_SAMSUNG_CELLNUM				(376)
#endif

#elif(INTEL_FSP_EN)
#if(FW_CATEGORY_FLASH == FLASH_INTEL_N38A_QLC)
#define IM_N38A_SECTION_1_RDT			(32)
#define IM_N38A_SECTION_2_RDT			(160)
#define IM_N38A_SECTION_3_RDT			(2912)
#define IM_N38A_SECTION_4_RDT			(2992)
#define IM_N38A_SECTION_5_RDT			(3024)
#define IM_N38A_SECTION_6_RDT			(3072)

#define IM_N38A_CELLNUM_SECTION_1_RDT	(32)
#define IM_N38A_CELLNUM_SECTION_2_RDT	(720)
#define IM_N38A_CELLNUM_SECTION_3_RDT	(736)
#define IM_N38A_CELLNUM_SECTION_4_RDT	(752)
#define IM_N38A_CELLNUM_SECTION_5_RDT	(768)
#define IM_N38A_CELLNUM_SECTION_6_RDT	(784)

#define IM_N38A_CELLNUM					(2352)
#define IM_INTEL_CELLNUM				(2352)
#endif

#endif /* (MICRON_FSP_EN) */

#define IM_GETCOORD_WIN_SIZE	(0)
#define IM_GETCOORD_RES_PLANE	(1)
#define IM_GETCOORD_CHK_ALIGN	(2)
#define IM_GETCOORD_X_VAL	(3)
#define IM_GETCOORD_Y_VAL	(4)
#define IM_GETCOORD_CELLNUM_VAL	(5)
#define IM_GETCOORD_RS_TAG_OFFSET	(6)

#define IM_FW_WORDLINE_GETCOORD_PHYSICAL_WORDLINE		(0)
#define IM_FW_WORDLINE_GETCOORD_WORDLINE_GROUP			(1)
#define IM_FW_WORDLINE_GETCOORD_IS_EDGE_WORDLINE		(2)
#define IM_FW_WORDLINE_GETCOORD_WORDLINE_TYPE			(3)
#define IM_FW_WORDLINE_GETCOORD_PHYSICAL_PAGE			(4)

#define IM_PHYSICAL_WORDLINE_GETCOORD_WORDLINE_GROUP	(0)
#define IM_PHYSICAL_WORDLINE_GETCOORD_IS_EDGE_WORDLINE	(1)
#define IM_PHYSICAL_WORDLINE_GETCOORD_WORDLINE_TYPE		(2)
#define IM_PHYSICAL_WORDLINE_GETCOORD_PHYSICAL_PAGE		(3)

#define GROUP_CE_DEAFULT_MODE			(0)
#define GROUP_CE_COPY_UNIT_MODE		(1)

#define M_ACTIVE_USING_GCTARGET_NUM() (gpVT->GC.ubTargetCopyIdx + ((gpVT->GC.CopyData.ubTargetUnitIsGet) ? 1 : 0))

#define M_GC_GET_PLANES_PER_GROUP(SLC)         ((SLC) ? gGC.ubSLCPlanesPerGroup : gGC.ubTLCPlanesPerGroup)
#define M_GC_GET_ALL_GROUPS_TOTAL_PLANES(SLC)  ((SLC) ? gGC.ubSLCAllGroupsTotalPlanes : gGC.ubTLCAllGroupsTotalPlanes)

#if(D3_GR_ALWAYS_SLC_EN)
#define M_SLCPOOL_CONDITION()	(FALSE)
#define M_D1UNIT_REMAIN_CONDITION(uwThreshold)  ((gpVT->D1Condition.Flag.btD1IsAlive) ? (gpVT->FreePool.uwD1FreeCnt >= (gpVT->FreePool.ubTotalD1ReservedCnt + uwThreshold)) : (FALSE))
#define M_D3GRSLC_REMAIN_CONDITION(uwThreshold) ((gpVT->FreePool.uwD3FreeCnt) >= (gpVT->FreePool.ubTotalReservedCnt + (uwThreshold)))
#define M_D3GRSLC_NONMIXPLANE_CONDITION(ubSeqTableCnt)	(gubRUTMixPlaneEn && (ubSeqTableCnt >= INIT_SEQ_TABLE_PER_FASTPAGEUNIT_THRESHOLD))
#else/*(D3_GR_ALWAYS_SLC_EN)*/
#define M_SLCPOOL_CONDITION()  ((gSystmAreaFWSettingFromInfoBlk.BG.ubSLCPoolEn) \
		&& (M_GET_GCBLK_SLC_POOL_NUM() > (SLC_POOL_NUM_BASE + gulFastPage4kEntrysPerUnit)) \
		&& ((gpVT->SLCPoolCondition.Flag.btIsNeedGC) \
			|| ((gpVT->FreePool.uwD3FreeCnt > (gpVT->FreePool.ubTotalReservedCnt + D1_UNIT_EN * GCGR_RESERVE_NUM)) \
				&& (gpVT->FreePool.uwD3FreeCnt - (gpVT->FreePool.ubTotalReservedCnt + D1_UNIT_EN * GCGR_RESERVE_NUM) >= gpVT->SLCPoolCondition.uwFreePoolThreshold))))
#define M_D1UNIT_REMAIN_CONDITION(uwThreshold) 	(FALSE)
#define M_D3GRSLC_REMAIN_CONDITION(uwThreshold) 	(FALSE)
#define M_D3GRSLC_NONMIXPLANE_CONDITION(ubSeqTableCnt) (FALSE)
#endif/*(D3_GR_ALWAYS_SLC_EN)*/
#define FTL_HOST_READ_BLOCK_GC_TIME_IN_MS_DEFAULT (20)
#define FTL_STOP_BLOCK_GC_TIME_IN_MS_DEFAULT (10)

#define M_FTL_GC_ARBITER(MODE, LOC)    do { \
	FTLGCArbiter((MODE)); \
	if (DEBUG_UART_GC_ARBITER_EN) { \
		M_UART(GC_, "[Loc:%x]FTL: %x\n", (LOC), gpVT->FTL.ulAll); \
	} \
} while (0)
#define M_FTL_IS_GC_WL_DONE() ((gLPM.Control.B.btDLMCForcePS4) || ((FALSE == gpVT->FTL.btNowWearLeveling) && (FALSE == gpVT->FTL.btNowGC)))

#if (UTILIZE_SLC_UNUSE_PCA_BIT_EN)
#define M_FTL_GET_PLANE_FROM_PCA(PCA) 											((((PCA) >> gPCARule_Entry.ubBit_No) & gubPlanesPerSuperPageMask) | \
																				((((PCA) >> gPCARule_Page.ubShift[COP0_PCA_RULE_0]) & gPCARule_Page.ulMask) << gubPlanesPerSuperPageLog))

#define M_FTL_GET_UNIT_FROM_PCA(PCA)											(((PCA) >> gPCARule_Unit.ubShift[COP0_PCA_RULE_0]) & gPCARule_Unit.ulMask)


INLINE U32 FTLTransferToCop0CieInPCA(U32 VCA)
{
	/*
		swap Unit and Page
		PCA format : PAGE | BLK | LUN | CE | CH | Plane | Entry
		1.VCA to PCA
		2.FSA follow Unit insert between plane and entry
	*/
	U8 ubBitNum;
	U32 ulPage, ulUnit, PCA;

	ubBitNum = (gub4kEntrysPerPlaneLog + gubPlanesPerSuperPageLog);
	ulPage  = ((VCA >> ubBitNum) & gPCARule_Page.ulMask);
	ulUnit  = ((VCA >> gub4kEntrysPerUnitAlignLog) & gPCARule_Unit.ulMask);
	PCA = VCA;
	PCA &= ~(BIT_MASK(gPCARule_Page.ubBit_No + gPCARule_Unit.ubBit_No) << ubBitNum);
	PCA |= ((ulUnit << gPCARule_Unit.ubShift[COP0_PCA_RULE_0]) | (ulPage << gPCARule_Page.ubShift[COP0_PCA_RULE_0]));
	return PCA;
}

INLINE U32 FTLReverseCop0CieInPCA(U32 PCA)
{
	/*
		swap Unit and Page
		VCA format : Unit | Plane | Entry
		1.PCA to VCA
	*/
	U8 ubBitNum;
	U32 ulPage, ulUnit, VCA;

	ubBitNum = (gub4kEntrysPerPlaneLog + gubPlanesPerSuperPageLog);
	ulPage = (PCA >> gPCARule_Page.ubShift[COP0_PCA_RULE_0]) & gPCARule_Page.ulMask;
	ulUnit  = (PCA >> gPCARule_Unit.ubShift[COP0_PCA_RULE_0]) & gPCARule_Unit.ulMask;
	VCA = PCA;
	VCA &= ~(BIT_MASK(gPCARule_Page.ubBit_No + gPCARule_Unit.ubBit_No) << ubBitNum);
	VCA |= ((ulUnit << gub4kEntrysPerUnitAlignLog) | (ulPage << ubBitNum));
	return VCA;
}
#else /* UTILIZE_SLC_UNUSE_PCA_BIT_EN */
#define M_FTL_GET_PLANE_FROM_PCA(PCA)			 								(((PCA) & gul4kEntrysPerUnitAlignMask) >> gub4kEntrysPerPlaneLog)

#define M_FTL_GET_UNIT_FROM_PCA(PCA)											((PCA) >> gub4kEntrysPerUnitAlignLog)

INLINE U32 FTLTransferToCop0CieInPCA(U32 PCA)
{
	return PCA;
}

INLINE U32 FTLReverseCop0CieInPCA(U32 PCA)
{
	return PCA;
}
#endif /* UTILIZE_SLC_UNUSE_PCA_BIT_EN */

typedef enum {
	ADD_FREE_ERROR_HANDLE,								// 0x00
	ADD_FREE_ERROR_HANDLE_RECOVERADD_SPARE_UNIT,				// 0x01
	ADD_FREE_ERROR_HANDLE_VTCHILD,							// 0x02
	ADD_FREE_FTL_UPDATE_FREE_UNIT_BARRIER,					// 0x03
	ADD_FREE_FTL_TABLE_UPDATE_STATE6_SYNC,					// 0x04
	ADD_FREE_FTL_TABLE_UPDATE_STATE6_SYNC_TABLE_GC_VC_ZERO_DEBUG,	// 0x05
	ADD_FREE_FTL_TABLE_UPDATE_STATE6_SYNC_TABLE_GC_SRC_DEBUG,		// 0x06
	ADD_FREE_FTL_TABLE_UPDATE_STATE6_SYNC_TABLE_GC_VC_ZERO,		// 0x07
	ADD_FREE_FTL_TABLE_UPDATE_STATE6_SYNC_TABLE_GC_SRC,			// 0x08
	ADD_FREE_FTL_TABLE_UPDATE_STATE6_SYNC_GC_SRC,				// 0x09
	ADD_FREE_FTL_TABLE_UPDATE_STATE6_SYNC_GC_TARGET,			// 0x0A
	ADD_FREE_ICE_PREFORMAT_TABLE,							// 0x0B
	ADD_FREE_ICE_PREFORMAT_INIT_INFO,						// 0x0C
	ADD_FREE_ICE_PREFORMAT_GC_TARGET,						// 0x0D
	ADD_FREE_ICE_PREFORMAT_ERROR_HANDLE,						// 0x0E
	ADD_FREE_ICE_PREFORMAT_DATA,							// 0x0F
	ADD_FREE_POWER_CYCLE_REMOVE_TABLE,						// 0x10
	ADD_FREE_POWER_CYCLE_MOVE_TABLE,						// 0x11
	ADD_FREE_POWER_CYCLE_HANDLE_GCGR,						// 0x12
	ADD_FREE_POWER_CYCLE_HANDLE_GC_SRC,						// 0x13
	ADD_FREE_FW_PREFORMAT_TABLE,							// 0x14
	ADD_FREE_FW_PREFORMAT_INIT_INFO,						// 0x15
	ADD_FREE_FW_PREFORMAT_GC_TARGET,						// 0x16
	ADD_FREE_FW_PREFORMAT_ERROR_HANDLE,						// 0x17
	ADD_FREE_FW_PREFORMAT_DATA,							// 0x18
	ADD_FREE_FW_PREFORMAT_DATA_FULL_SIZE,					// 0x19
	ADD_FREE_VT_VTCHILD_FULL,								// 0x1A
	ADD_FREE_COPY_INIT_INFO,								// 0x1B
	ADD_FREE_POWER_CYCLE_FORCE_COPY_GR,						// 0x1C
	ADD_FREE_SIM_SPOR_VT_CHILD,							// 0x1D
	ADD_FREE_WL,										// 0x1E
	ADD_FREE_OPEN_BLOCK_RS,								// 0x1F
	ADD_FREE_WL_OP,								// 0x20
	ADD_FREE_SPOR_COPY_GCTARGET,						// 0x21
	ADD_FREE_PARITY_FOR_GCGR,				        	// 0x22
	ADD_FREE_MMO,							            // 0x23
	ADD_FREE_GC_SPOR_CONTINUE,                           // 0x24
	ADD_FREE_GC_LOAD_GCSA_FAIL_GC_STOP,			// 0x25
	ADD_FREE_POWER_CYCLE_RETURN_NEW_ACTIVE_UNIT,			// 0x26
	ADD_FREE_SPOR_RETURN_NEW_OPEN_BLK_RAIDECC_UNIT,	//	0x27
	ADD_FREE_GC_SPOR_CONTINUE_V4,							// 0x28
	ADD_FREE_WORDLINE_FOLDING_UNIT,						//0x29
	ADD_FREE_FW_PREFORMAT_WORDLINE_FOLDING_UNIT			//0x30
} AddFreeLocationEnum_t;

#define TIEOUT_SINGLE_4K_LCA_OFFSET (0)
#define CMD_OUT_LCA_SIZE (FRAMES_PER_PAGE)
typedef enum {
	/* Need Wait CQ*/
	R_DEFAULT = 0,
	R_GETLCA_FROM_L4KTABLE,
	R_GETLCA_FROM_L4KTABLE_SKIP_ERROR_HANDLE,
	R_GETLCA_FROM_L4KTABLE_AND_RETURN_MULTIPLE_CQ,
	R_CHECK_ERASE_PAGE,
	/* No Wait CQ*/
	R_DEFAULT_NO_WAITCQ,
	R_OPAL,
	R_COP0READN4K_SET_MODE_TOTAL_NUM
} FTLCOP0ReadN4kEntrySetModeEnum_t;

typedef struct {
	U8 ubSetMode;
	U8 ubParaMode;
	U8 ub4KNum;
	U8 ubPrivate;
} FTLCOP0ReadN4kEntryMode_t;

typedef struct {
	U8 btFinalVTProgramDone			: 1;
	U8 btFinalDriveLogProgramDone	: 1;
	U8 btSATAReturnWriteProtectFISDone	: 1;
	U8 btSATASwitchToFUADone		: 1;
	U8 : 4;

	U8 ubNVMEWriteProtectStandbyDone;
} FTLWriteProtectHandle_t;

typedef enum GCArbiterMode {
	GC_ARBITER_MODE_GC = 0,
	GC_ARBITER_MODE_WL,
	GC_ARBITER_MODE_ERROR_HANDLE,
	GC_ARBITER_MODE_HMB_MODE_CHANGE,
} GCArbiterModeEnum_t;

typedef enum GCArbiterLocation {
	GC_ARBITER_LOCATION_0 = 0,
	GC_ARBITER_LOCATION_1,
	GC_ARBITER_LOCATION_2,
	GC_ARBITER_LOCATION_3,
	GC_ARBITER_LOCATION_4,
	GC_ARBITER_LOCATION_5,
	GC_ARBITER_LOCATION_6,
	GC_ARBITER_LOCATION_7,
	GC_ARBITER_LOCATION_8,
	GC_ARBITER_LOCATION_9,
	GC_ARBITER_LOCATION_10,
	GC_ARBITER_LOCATION_11,
	GC_ARBITER_LOCATION_12,
	GC_ARBITER_LOCATION_13,
	GC_ARBITER_LOCATION_14,
	GC_ARBITER_LOCATION_15,
	GC_ARBITER_LOCATION_16,
	GC_ARBITER_LOCATION_17,
	GC_ARBITER_LOCATION_18,
	GC_ARBITER_LOCATION_19,
	GC_ARBITER_LOCATION_20,
	GC_ARBITER_LOCATION_21,
	GC_ARBITER_LOCATION_22,
	GC_ARBITER_LOCATION_23,
	GC_ARBITER_LOCATION_24,
	GC_ARBITER_LOCATION_25,
	GC_ARBITER_LOCATION_26,
	GC_ARBITER_LOCATION_27,
	GC_ARBITER_LOCATION_28,
	GC_ARBITER_LOCATION_29,
	GC_ARBITER_LOCATION_30,
	GC_ARBITER_LOCATION_31,
	GC_ARBITER_LOCATION_32,
	GC_ARBITER_LOCATION_33,
	GC_ARBITER_LOCATION_34,
	GC_ARBITER_LOCATION_35,
	GC_ARBITER_LOCATION_36,
	GC_ARBITER_LOCATION_37,
	GC_ARBITER_LOCATION_60 = 60, //for D1
	GC_ARBITER_LOCATION_61, //for D1
} GCArbiterLocationEnum_t;

typedef enum ADWLSVMode {
	ADWLSV_MODE_DEFAULT = 0,
	ADWLSV_MODE_CLEAR_SELECTIVE_BLKS,
	ADWLSV_MODE_CLEAR_ALL_BLKS
} ADWLSVModeEnum_t;

typedef enum ADWLSVBlkAddrType {
	ADWLSV_BLK_ADDR_TYPE_IN_PHYSICAL = 0,
	ADWLSV_BLK_ADDR_TYPE_IN_LOGICAL
} ADWLSVBlkAddrTypeEnum_t;

typedef enum TrimRegNumEnum {
	SLC_WL_TRIM_REG_NUM = 1,
	MLC_WL_TRIM_REG_NUM = 3,
	TLC_WL_TRIM_REG_NUM = 7,
	QLC_WL_TRIM_REG_NUM = 15,
} FTLTrimRegNumEnum_t;

typedef enum WordLineInfoGetModeEnum {
	WL_PAGE_NUM,
	WL_TYPE,
	WL_TRIM_REG_NUM
} FTLWordLineInfoGetModeEnum_t;

typedef enum WordLinePageNumberEnum {
	SLC_WL_PAGE_NUM = 1, // LOWER:0
	MLC_WL_PAGE_NUM = 2, // LOWER:0, UPPER:1
	TLC_WL_PAGE_NUM = 3, // LOWER:0, UPPER:1, EXTRA:2
	QLC_WL_PAGE_NUM = 4, // LOWER:0, UPPER:1, EXTRA:2, TOP:3
} FTLWordLinePageNumberEnum_t;

typedef enum PageTypeEnum {
	LOWER, // 0
	UPPER, // 1
	EXTRA, // 2
	TOP,   // 3
	PAGE_NUM
} FTLPageTypeEnum_t;

typedef enum WordLineTypeEnum {
	SLC_WL = 0,
	MLC_WL = 1,
	TLC_WL = 2,
	QLC_WL = 3,
	WL_TYPE_NUM
} FTLWordLineTypeEnum_t;

typedef struct {
	U16 uwWriteStopBlockGCTime;
	U16 uwBlockGCTime;
	U8 ubHostReadBlockGCDis;
} GCInfoModule_t;

typedef struct {
	U64 uoFlushTime;
	union {
		U8 ubAll;
		struct {
			U8 btInitialReady				: 1;
			U8 btLPMMeasure					: 1;
			U8 Reserved						: 6;
		};
	} InfoBlockInfo;
} FTLPPSMeasure_t;
#if (ERROR_HANDLE_VALIDATION)
typedef enum {
	QLC_WL_LESS_THAN_PR16 = 0,
	QLC_WL_MORE_THAN_PR16,
	QLC_WL_TYPE_NUM
} FTLQLCWordLineTypeEnum_t;

typedef enum {
	SLC_WL_NEW = 0,
	SLC_WL_TYPE_NUM
} FTLSLCWordLineTypeEnum_t;
#endif /* (ERROR_HANDLE_VALIDATION) */

extern FTLPPSMeasure_t gFTLPPSMeasure;
extern U32 gulTieoutLCA[CMD_OUT_LCA_SIZE];
extern U8 gubTieoutLCA_P4KNum;
extern U64 guoStandbyStartTime;
extern U8 gubEnterFTLTask;
extern FTLWriteProtectHandle_t gubFTLWriteProtectHandle;
extern U64 guoWCQStartTimer;
extern U64 guoRCQStartTimer;
extern U16 guwFTLReadMaxIdleTime;
extern U16 guwFTLWriteMaxIdleTime;
extern U16 guwFTLBGThreshold;
extern U8 gubFTLLargeLTREn;
extern U8 gubFTLL1Dis;
extern U32 gulFTLLastTimeStamp;
extern U8 gubFTLNeedReClearSLC;
extern GCInfoModule_t gGCInfo;
extern FW_CALLBACK_SECTION U32 gulFTLReadN4kEntryGetLCA_Callback;

U8 FTLCalCmdLifetime(void);
void FTLUartCmdLifetime(void);

#if (MICRON_FSP_EN || PS5017_EN)
void FTLAddFreeUnit(U8 ubSelectUnit, U8 ubNeedWaitBarrier, Unit_t *puwUnit, U8 ubAddFreeSerialNumber);
#else /*(MICRON_FSP_EN)*/
AOM_INIT_2 void FTLAddFreeUnit(U8 ubSelectUnit, U8 ubNeedWaitBarrier, Unit_t *puwUnit, U8 ubAddFreeSerialNumber);
#endif /*(MICRON_FSP_EN)*/

#if(PERFORMANCE_TEST_EN)
AOM_INIT_2 void FTLGetFreeUnit(U8 ubSelectD1D3, U8 ubSelectUnit, U8 ubSelectStatic, U8 ubSelectType, Unit_t *puwUnit);
AOM_TABLE_UPDATE U8 FTLUpdateFreeUnitBarrier(U8 ubUpdateInOneTime);
#else /*(PERFORMANCE_TEST_EN)*/
AOM_INIT_2 void FTLGetFreeUnit(U8 ubSelectD1D3, U8 ubSelectUnit, U8 ubSelectStatic, U8 ubSelectType, Unit_t *puwUnit);
AOM_INIT_2 U8 FTLUpdateFreeUnitBarrier(U8 ubUpdateInOneTime);
#endif /*(PERFORMANCE_TEST_EN)*/

AOM_INIT_2 void FTLLoadPGDPMDBM(void);
AOM_INIT_2 void FTLLoadVC(void);
void FTLRead4KGetLCA_Callback(TIEOUT_FORMAT_t uoResult);
AOM_INIT_2 U16 FTLCOP0ReadN4kEntry_AOM_INIT_2(FTLCOP0ReadN4kEntryMode_t ulMode, PCA_t ulDataPCA, U32 *ulBufAddrPtr);
AOM_INIT_7 U16 FTLCOP0ReadN4kEntry_AOM_INIT_7(FTLCOP0ReadN4kEntryMode_t ulMode, PCA_t ulDataPCA, U32 *ulBufAddrPtr);
AOM_RS_1 U16 FTLCOP0ReadN4kEntry_AOM_RS_1(FTLCOP0ReadN4kEntryMode_t ulMode, PCA_t ulDataPCA, U32 *ulBufAddrPtr);
AOM_TCG_COMMON U16 FTLCOP0ReadN4kEntry_AOM_TCG_COMMON(FTLCOP0ReadN4kEntryMode_t ulMode, PCA_t ulDataPCA, U32 *ulBufAddrPtr);
void FTLReadN4kEntryGetLCA_Callback(TIEOUT_FORMAT_t uoResult);
#if (RDT_MODE_EN)
U16 FTLCOP0ReadN4kEntry(FTLCOP0ReadN4kEntryMode_t ulMode, PCA_t ulDataPCA, U32 *ulBufAddrPtr);
#endif
#if ((!BURNER_MODE_EN) && (!RDT_MODE_EN))
U8 FTLGetHitPLBIndex(PCA_t ulFWPCA, U32 ulLCA);
U16 FTLGetHitPLBParameterRamIndex(PCA_t ulFWPCA);
#endif /* ((!BURNER_MODE_EN) && (!RDT_MODE_EN)) */

U32 GetGRUnitActivePCA();
U8 IsPCAInGRUnit(PCA_t PCA);
AOM_NRW U8 FTLGetDriveLifeTime(void);
void FTLFUAFillFSP(void);
AOM_ERROR_HANDLE void FTLSwapEC(Unit_t *uwSourceUnit, Unit_t *uwTargetUnit);
AOM_DEBUG void FTLDebugReadTable(U16 uwIndex, U32 ulAddr, U32 ulPCA, U16 uwLBOffse);
void FTLGCArbiter(U8 ubMode);
FW_BTCM0_SECTION void FTLStopAndesSequentialReadWrite(void);
AOM_SPOR_2 void FTLInitC6EraseOrFillDummy(Unit_t uwUnit, U8 ubSLCMode, U32 ulStartPlaneIdx);
AOM_NRW_2 void FTLProgramDummy_AOM_NRW_2(Unit_t uwUnit, U32 ulStartPlaneIdx, U8 ubSLCMode);
AOM_LPM U8 FTLBGCheckGCInAdvanced(void);
AOM_LPM U8 FTLBGCheckNeedClearD1OrD3Unit(U8 ubBGClearMode);
AOM_LPM U8 FTLBackGroundCheck(void);

#if (MICRON_FSP_EN)
AOM_ERROR_HANDLE2 U8 FTLGetWindowSizeViaPage(U16 uwPageIdx);
AOM_ERROR_HANDLE2 U16 FTLGetLastSharedPageIdx(U16 uwPage, U8 ubXpage);
U8 FTLGetPlaneBankFromVCA(U32 ulVCA, U8 ubSLC);
FW_BTCM0_SECTION U8 FTLPlaneIsAlignedWindow(U8 ubIsSLCMode, U32 ulPlaneIndex, U8 ubMode);
void FTLCleanAllPlaneBankADWLSV();
void FTLCleanADWLSV(Unit_t *puwUnit, ADWLSVBlkAddrTypeEnum_t ubBlkAddrType, U16 uwPhysicalBlkAddr, U8 ubPlaneBank, ADWLSVModeEnum_t ubMode, U8 ubCheckMode);
AOM_INIT void FTLADWLSVSetFeature(U8 ubChannel, U8 ubCE, U8 *ubSetFeatureMode, U8 ubCheckMode);
FW_BTCM0_SECTION U16 FTLGetPageFromCoord(U8 ubX, U16 uwY);
#endif /* (MICRON_FSP_EN) */

#if ((FW_CATEGORY_FLASH == FLASH_YMTC_EMS_QLC))//ems mst add--karl
FW_BTCM0_SECTION U8 FTLPlaneIsAlignedWindow(U8 ubIsSLCMode, U32 ulPlaneIndex, U8 ubMode);
#endif
U16 FTLGetCoord(U16 uwPageIdx, U8 ubMode);
AOM_MEDIA_SCAN_FW_GET_COORD U16 FTLFWWordLineGetCoord(U8 ubX, U16 uwY, U8 ubMode, U8 ubSLC);
AOM_MEDIA_SCAN_FW_GET_COORD U16 FTLPhysicalWordLineGetCoordForSLC(U8 ubX, U16 uwPhysicalWL, U8 ubMode);
U16 FTLPhysicalWordLineGetCoordForNonSLC(U8 ubX, U16 uwPhysicalWL, U8 ubMode);
U32 FTLPhysical2PlaneInx(U16 uwPage, U8 ubPlane);
void FTLPlaneInx2Physical(U32 ulPlaneIdx, U16 *uwPage, U8 *ubPlane);
U8 FTLGetWindowSize(U32 ulPlaneIdx);
U8 FTLGetPlaneFlushNum(U8 ubIsSLCMode, U32 ulPlaneIdx);
U8 FTLResidualPlaneNumInWindow(U8 ubIsSLCMode, U32 ulPlaneIdx);

#if TWO_PASS_EN
U16 GetPageFromCellNum(U16 uwCellNum, U8 ubLMU); //LUXTP
U8 FTLIsQLCPage(U32 ulPlaneIdx);
#endif /* TWO_PASS_EN */

U8 FTLCommonLibraryWordLineInfo(U8 ubY, FTLWordLineInfoGetModeEnum_t ubMode);
FW_BTCM0_SECTION U16 FTLCommonLibraryGetSuperPageIdxFromFSA(U32 ulFSA, U8 ubALUSelect);
U16 FTLCommonLibraryGetSuperPageIdxFromVCA(U32 ulVCA, U8 ubIsSLCMode);
U32 FTLGetFirstPlaneIdx(U32 ulPlaneInx);
AOM_WL2 void InitPORSendOpenGRInfoToAndes(U8 ubSyncMode);
AOM_WORDLINE_FOLDING _FTL_DECL COP0Status_t FTLEraseBlock_AOM_WORDLINE_FOLDING(U8 ubSelectMode, U8 ubSLCMode, U16 *puwTagId, U8 ubWait, U8 ubMultiPlaneMode, PCA_t ulDataPCA, U32 ulCallBackAddr, U8 ubC6Erase);
#if (DEBUG_UTILIZE_SLC_UNUSE_PCA_BIT_EN)
void FTLCheckInsertST1(PCA_t ulVCA);
#endif
#include "ftl_inline.h"
#endif /* _FTL_API_H_ */
