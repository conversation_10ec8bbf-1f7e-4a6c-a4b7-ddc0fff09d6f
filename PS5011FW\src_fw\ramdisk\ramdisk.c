#include "typedef.h"
#include "mem.h"
#include "debug/debug.h"
#include "hal/dmac/dmac_api.h"
#include "hal/dmac/dmac_pop_cmd.h"
#include "hal/bmu/bmu_api.h"
#include "hal/dmac/dmac_inline.h"
#include "hal/fip/fip_api.h"
#include "hal/bmu/bmu_pop_cmd.h"
#include "nvme_api/nvme/shr_hal_nvme_api.h"
#include "nvme_api/d2h/shr_hal_d2h.h"
#include "hal/zip/zip_api.h"

#if (0)
#define BytePerNVMEPage			(4096)
#define BytePerNVMEPageLog		(12)

typedef struct {
	U32 ulRamDiskAddr;
	U32 ulRamDiskSize;
	//DebugForFW
	U32 ulRamDiskMetaDataAddr;
	U32 ulRamDiskMetaDataSize;
} RamDiskInfo_t;

RamDiskInfo_t gRamDiskInfo;

typedef struct {
	U8 btDMACWriteWLBToHMB	:	1;
	U8 btDMACWriteHMBToRLB	:	1;
	U8 ubRev	: 6;
} RamDiskCBFlag_t;

typedef struct {
	U8 btRLBAllocateFlag : 1;
	U8 ubRev	: 7;
} HostReadFlag_t;

typedef struct {
	U16 uwRLBOffset;
	HostReadFlag_t ubHostReadFlag;
} HostReadPara_t;

RamDiskCBFlag_t gubRamDiskCBFlag;
HostReadPara_t gHostReadPara;

U8 IsRamdiskError();

/*
 * 	Call Back
 */
void RamDiskDMACWriteCopyWLBToHMB_Callback(DMACCQRst_t *pDMACCQRst);
void RamDiskDMACWriteCopyHMBToRLB_Callback(DMACCQRst_t *pDMACCQRst);

/*
 *
 */
#if 0
void RamDiskInit(nvme_HMB_fmt_t *pHMBData, U32 ulMPS)
{
	U32 *pulRamDisk;

	U32 ulHMBDecCnt = 0;
	U32 ulLocalZ1Addr = D2H_Z1_RAM_ADDRESS;
	U32 ulZ1Len = 0;

	gRamDiskInfo.ulRamDiskSize = 0;
	for (ulHMBDecCnt = 0; ulHMBDecCnt < (pHMBData->ulHMDLEC); ulHMBDecCnt++) {
		ulZ1Len = ((pHMBData->sHMB_Dec_arry[ulHMBDecCnt].ulBsize * ulMPS) / (0x220)) * 0x200;
		D2HAESPathInit(ulHMBDecCnt, ((ulZ1Len / 0x200) * 0x220), ulLocalZ1Addr, pHMBData->sHMB_Dec_arry[ulHMBDecCnt].uoBadd);
		ulLocalZ1Addr += ((ulZ1Len / 0x200) * 0x220);//ulLocalZ1Addr += ulZ1Len;
		gRamDiskInfo.ulRamDiskSize += ulZ1Len;
	}
	gRamDiskInfo.ulRamDiskAddr = D2H_Z1_RAM_ADDRESS;

	gRamDiskInfo.ulRamDiskMetaDataAddr	=	DUMMY_REMAP_ADDRESS;
	gRamDiskInfo.ulRamDiskMetaDataSize	=	BC_16MB;

}
#endif

void RamdiskWrite(U32 ulLCA, U8 ubLBID, U16 uwLBOffset)
{
	BMUCmdResult_t BMUCmdResult;
	DMACParam_t DMACParam;
	if (IsRamdiskError()) {
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
	}

	while (BMU_CMD_STATUS_SUCCESS != BMUAPICmdGetLBNA(BMU_CMD_NEED_CQ, ubLBID, uwLBOffset, &BMUCmdResult));

	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, BMUCmdResult.BMUGetLBNARst.ubResult == 0);

	DMACParam.ulSourceAddr = M_PB_TO_ADDR(M_BMU_GET_PB_FROM_GET_LBNA_CQ(&BMUCmdResult));
	DMACParam.ulDestAddr = (U32)(gRamDiskInfo.ulRamDiskAddr + (ulLCA * BytePerNVMEPage));
	DMACParam.ul32ByteNum = M_BYTE_TO_32BYTE_ALIGN(SIZE_4KB);
	DMACCopy(DMAC_MODE_NORMAL_QUEUE, &DMACParam, (U32)RamDiskDMACWriteCopyWLBToHMB_Callback, 0);

	gubRamDiskCBFlag.btDMACWriteWLBToHMB = TRUE;
	while (gubRamDiskCBFlag.btDMACWriteWLBToHMB) {
		DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
	}

	DMACParam.ulSourceAddr = (U32)(gRamDiskInfo.ulRamDiskAddr + (ulLCA * BytePerNVMEPage));
	DMACParam.ulDestAddr = M_PB_TO_ADDR(M_BMU_GET_PB_FROM_GET_LBNA_CQ(&BMUCmdResult));
	DMACParam.ul32ByteNum = M_BYTE_TO_32BYTE_ALIGN(SIZE_4KB);
	DMACCopy(DMAC_MODE_NORMAL_QUEUE, &DMACParam, (U32)RamDiskDMACWriteCopyHMBToRLB_Callback, 0);

	gubRamDiskCBFlag.btDMACWriteHMBToRLB = TRUE;
	while (gubRamDiskCBFlag.btDMACWriteHMBToRLB) {
		DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
	}

}

void RamdiskRead(U32 ulLCA, U8 ubLBID, U16 uwLBOffset)
{
	DMACParam_t DMACParam;
	if (IsRamdiskError()) {
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
	}

	DMACParam.ulSourceAddr = (U32)(gRamDiskInfo.ulRamDiskAddr + (ulLCA * BytePerNVMEPage));
	DMACParam.ulDestAddr = M_LB_TO_ADDR(ubLBID, uwLBOffset);
	DMACParam.ul32ByteNum = M_BYTE_TO_32BYTE_ALIGN(SIZE_4KB);
	DMACCopy(DMAC_MODE_NORMAL_QUEUE, &DMACParam, (U32)RamDiskDMACWriteCopyHMBToRLB_Callback, 0);

	gubRamDiskCBFlag.btDMACWriteHMBToRLB = TRUE;
	while (gubRamDiskCBFlag.btDMACWriteHMBToRLB) {
		DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
	}
}

U8 IsRamdiskError()
{
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, gRamDiskInfo.ulRamDiskAddr != 0);
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, gRamDiskInfo.ulRamDiskMetaDataAddr != 0);
	return 0;
}

void RamDiskDMACWriteCopyWLBToHMB_Callback(DMACCQRst_t *pDMACCQRst)
{
	gubRamDiskCBFlag.btDMACWriteWLBToHMB = FALSE;
}

void RamDiskDMACWriteCopyHMBToRLB_Callback(DMACCQRst_t *pDMACCQRst)
{
	gubRamDiskCBFlag.btDMACWriteHMBToRLB = FALSE;
}

void RamDiskAllocateRLBForTempBuf_Callback(BMUCmdResult_t BmuCmdResult)
{
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, BMU_CMD_STATUS_SUCCESS == BmuCmdResult.BMUAllocateRst.ubResult);
	gHostReadPara.uwRLBOffset = BmuCmdResult.BMUAllocateRst.uwLBOffset;
	M_UART(RAMDISK_, "[Hit COP1] PB Addr : %x\n", M_BMU_GET_PB_FROM_ALLOCATE_CQ(&BmuCmdResult));
	gHostReadPara.ubHostReadFlag.btRLBAllocateFlag = FALSE;
}

void RamdiskHostRead(ReadCQInfo_t *pReadCQInfo)
{
	U8 ubSize;
	U32 ulE3D4kForCPUGen;
	BMUCmdResult_t BmuCmdResult;
	//Allocate RLB
	ubSize = 1;
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, BMU_CMD_STATUS_SUCCESS == BMUAPICmdAllocate(BMU_CMD_NEED_CQ, BMU_CMD_TIME_INFINITE, LB_ID_READ, ubSize, pReadCQInfo->ulLCA, (U8)pReadCQInfo->DW1.CTag, BMU_CMD_STREAM_ID_DEFAULT, BMU_ALLOCATE_NOT_GET_E3D512, BMU_ALLOCATE_NOT_FUA, BMU_ALLOCATE_NOT_NEED_AUTO_FREE, (U32)RamDiskAllocateRLBForTempBuf_Callback, 0));
	gHostReadPara.ubHostReadFlag.btRLBAllocateFlag = TRUE;
	while (gHostReadPara.ubHostReadFlag.btRLBAllocateFlag) {
		BMUDelegateCmd();
	}
	M_UART(RAMDISK_, "[CTag]:%x, [Offset]:%x\n", pReadCQInfo->DW1.CTag, pReadCQInfo->DW3.RCQOffset);
	//RamDisk Read
	RamdiskRead(pReadCQInfo->ulLCA, LB_ID_READ, gHostReadPara.uwRLBOffset);


	ulE3D4kForCPUGen = CalculateCRC24(M_LB_TO_ADDR(LB_ID_READ, gHostReadPara.uwRLBOffset), (pReadCQInfo->ulLCA << 3));
	M_UART(RAMDISK_, "[E3D 4k Read From COP1]:%x", ulE3D4kForCPUGen);

	BMUAPICmdValidate(BMU_CMD_NEED_CQ, BMU_CMD_DEFAULT_PBADDR,
		LB_ID_READ, gHostReadPara.uwRLBOffset, pReadCQInfo->ulLCA,
		BMU_VALIDATE_NOT_UPDATE_READ_CACHE_LOCK,
		BMU_CMD_STREAM_ID_DEFAULT, BMU_VALIDATE_SIZE_NOT_ZERO_DATA,
		BMU_VALIDATE_SIZE_DEFAULT_CMD_END_FLAG,
		BMU_VALIDATE_SIZE_SET_FULL_FLAG,
		BMU_VALIDATE_OPERATOR_OR, 0xFF, ZINFO_7,
		BMU_VALIDATE_NEED_UPDATE_PEOP, BMU_VALIDATE_SET_E3D4K_FLAG,
		(ulE3D4kForCPUGen), 0, &BmuCmdResult);
}
#endif /* #if (0)*/