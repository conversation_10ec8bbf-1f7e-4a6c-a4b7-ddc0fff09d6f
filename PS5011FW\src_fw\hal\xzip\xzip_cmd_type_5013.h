#ifndef _E13_XZIP_CMD_TYPE_H_
#define _E13_XZIP_CMD_TYPE_H_
#include "setup.h"
#include "typedef.h"

typedef struct {
	U32 ubOpcode : 5;	//bit 0  ~ 4
	U32 ubTagID	 : 6;	//bit 5  ~ 10
	U32 ulInfo0	 : 21;	//bit 11 ~ 31
	U32 ulInfo1;		//bit 32 ~ 63
} XZIPSQCmdType_t;

//Clear Reg Command
typedef	struct {
	U32 ubOpcode	: 5;	//bit 0  ~ 4
	U32 ubTagID		: 6;	//bit 5  ~ 10
	U32 ulReserved0 : 21;	//bit 11 ~ 31
	U32 uwReserved1;		//bit 32 ~ 63
} XZIPClearRegCmd_t;

//Adjust Length Command
typedef struct {
	U32 ubOpcode	: 5;	//bit 0 ~ 4
	U32 ubTagID		: 6;	//bit 5 ~ 10
	U32 btReserved0	: 1;	//bit 11
	U32 ubAdjustLen	: 8;	//bit 12 ~ 19
	U32 uwReserved1 : 12;	//bit 20 ~ 31
	U32 ulReserved2;		//bit 32 ~ 63
} XZIPAdjustLenCmd_t;

//Lock Command
typedef	struct {
	U32 ubOpcode	: 5;	//bit 0 ~ 4
	U32 ubTagID		: 6;	//bit 5 ~ 10
	U32 btReserved0	: 1;	//	bit 11
	U32 ubXZIPIndex : 8;	//bit 12 ~ 19
	U32 uwReserved1 : 12;	//	bit 20 ~ 31
	U32 ulReserved2;		//	bit 32 ~ 63
} XZIPLockCmd_t;

//Unlock Command
typedef struct {
	U32 ubOpcode	: 5;	//bit 0 ~ 4
	U32 ubTagID		: 6;	//bit 5 ~ 10
	U32 btReserved0 : 1;	//bit 11
	U32 ubXZIPIndex : 8;	//bit 12 ~ 19
	U32 uwReserved1	: 12;	//bit 20 ~ 31
	U32 ulReserved2;			//bit 32 ~ 63
} XZIPUnLockCmd_t;

//Search XZIP Command
typedef struct {
	U32 ubOpcode	: 5;	//bit 0 ~ 4
	U32 ubTagID		: 6;	//bit 5 ~ 10
	U32 ubReserved0 : 2;	//bit 11 ~ 12
	U32 ubLBID		: 3;	//bit 12 ~ 15
	U32 uwPBAddr	: 9;	//bit 16 ~ 24
	U32 ubReserved1 : 7;	//bit 25 ~ 31
	U32 ulCRC24		: 24;	//bit 32 ~ 55
	U32 ubReserved2	: 8;	//bit 56 ~ 63
} XZIPSearchXZIPCmd_t;

//Search PCA Command
typedef struct {
	U32 ubOpcode	: 5;	//bit 0 ~ 4
	U32 ubTagID		: 6;	//bit 5 ~ 10
	U32 uwReserved	: 21;	//bit 11 ~ 31
	U32 ulPCA;				//bit 32 ~ 63
} XZIPSearchPCACmd_t;

//Insert PCA Command
typedef struct {
	U32 ubOpcode	: 5;	//bit 0 ~ 4
	U32 ubTagID		: 6;	//bit 5 ~ 10
	U32 btReserved0 : 1;	//	bit 11
	U32 ubXZIPIndex : 8;	//bit 12 ~ 19
	U32 uwReserved1	: 12;	//	bit 20 ~ 31
	U32 ulPCA;				//bit 32 ~ 63
} XZIPInsertPCACmd_t;



//Entry Diff PCA Command
typedef struct {
	U32 ubOpcode	: 5;	//bit 0 ~ 4
	U32 ubTagID		: 6;	//bit 5 ~ 10
	U32 Reserved0	: 5;	//bit 11 ~ 15
	U32 btClrPCA	: 1;	//bit 16
	U32 btClrFistHit: 1;	//bit 17
	U32 btFstNumEn	: 1;	//bit 18
	U32 uwReserved1 : 13;	//bit 19 ~ 31
	U32 ulReserved2;		//bit 32 ~ 63
} XZIPEntryPCADiffCmd_t;


typedef union {
	XZIPSQCmdType_t			XZIPSQCmdType;
	XZIPClearRegCmd_t		XZIPClearRegCmd;
	XZIPAdjustLenCmd_t		XZIPAdjustLenCmd;
	XZIPLockCmd_t			XZIPLockCmd;
	XZIPUnLockCmd_t			XZIPUnLockCmd;
	XZIPSearchXZIPCmd_t		XZIPSearchXZIPCmd;
	XZIPSearchPCACmd_t		XZIPSearchPCACmd;
	XZIPInsertPCACmd_t		XZIPInsertPCACmd;
	XZIPEntryPCADiffCmd_t	XZIPEntryPCADiffCmd;
} XZIPSQCmd_t;


typedef struct {
	U32 ubOpcode : 5;	//bit 0  ~ 4
	U32 ubTagID	 : 6;	//bit 5  ~ 10
	U32 ulInfo0	 : 21;	//bit 11 ~ 31
	U32 ulInfo1;		//bit 32 ~ 63
} XZIPCQResultType_t;

//Clear Reg Command
typedef	struct {
	U32 ubOpcode	: 5;	//bit 0  ~ 4
	U32 ubTagID		: 6;	//bit 5  ~ 10
	U32 uwReserved0 : 9;
	U32 btHasLock   : 1;
	U32 btXZIPUnLockFail : 1;
	U32 btAbort		: 1;
	U32 btSRAMFail	: 1;
	U32 ubReserved1 : 8;
	U32 uwReserved2;		//bit 32 ~ 63
} XZIPClearRegResult_t;

//Adjust Length Command
typedef struct {
	U32 ubOpcode	: 5;	//bit 0  ~ 4
	U32 ubTagID		: 6;	//bit 5  ~ 10
	U32 uwReserved0 : 9;
	U32 btHasLock   : 1;
	U32 btXZIPUnLockFail : 1;
	U32 btAbort		: 1;
	U32 btSRAMFail	: 1;
	U32 ubReserved1 : 8;
	U32 ulReserved2;		//bit 32 ~ 63
} XZIPAdjustLengthResult_t;

//Lock Command
typedef	struct {
	U32 ubOpcode	: 5;	//bit 0  ~ 4
	U32 ubTagID		: 6;	//bit 5  ~ 10
	U32 uwReserved0 : 10;
	U32 btXZIPLockFail	: 1;
	U32 btXZIPLockFull	: 1;
	U32 btSRAMFail	: 1;
	U32 ubXZIPIndex : 8;
	U32 ubLockCnt	: 8;
	U32 ulReserved2 : 24;
} XZIPLockResult_t;

//Unlock Command
typedef struct {
	U32 ubOpcode	: 5;	//bit 0  ~ 4
	U32 ubTagID		: 6;	//bit 5  ~ 10
	U32 uwReserved0 : 10;
	U32 btXZIPUnLockFail : 1;
	U32 btXZIPLockEmpty	: 1;
	U32 btSRAMFail	: 1;
	U32 ubXZIPIndex : 8;
	U32 ubLockCnt	: 8;
	U32 ulReserved2 : 24;
} XZIPUnLockResult_t;

//Search XZIP Command
typedef struct {
	U32 ubOpcode	: 5;	//bit 0  ~ 4
	U32 ubTagID		: 6;	//bit 5  ~ 10
	U32 ubReserved0 : 7;
	U32 btBmuFail   : 1;
	U32 btBulid		: 1;
	U32 btHit		: 1;
	U32 btFirstHit  : 1;
	U32 btAbort		: 1;
	U32 btSRAMFail	: 1;
	U32 ubXZIPIndex : 8;
	U32 ulPCA;		//bit 32 ~ 63
} XZIPSearchXZIPResult_t;

//Search PCA Command
typedef struct {
	U32 ubOpcode	: 5;	//bit 0  ~ 4
	U32 ubTagID		: 6;	//bit 5  ~ 10
	U32 uwReserved0 : 9;
	U32 btHit		: 1;
	U32 btXZIPLockFail  : 1;
	U32 btAbort		: 1;
	U32 btSRAMFail	: 1;
	U32 ubXZIPIndex : 8;
	U32 uwPBAddr	: 9;
	U32 ulReserved1 : 23;
} XZIPSearchPCAResult_t;

//Insert PCA Command
typedef struct {
	U32 ubOpcode	: 5;	//bit 0  ~ 4
	U32 ubTagID		: 6;	//bit 5  ~ 10
	U32 uwReserved0 : 10;
	U32 btInsertFail	: 1;
	U32 btAbort		: 1;
	U32 btSRAMFail	: 1;
	U32 ubXZIPIndex : 8;
	U32 ulReserved1;
} XZIPInsertPCAResult_t;



//Entry Diff PCA Command
typedef struct {
	U32 ubOpcode	: 5;
	U32 ubTagID		: 6;
	U32 btReserved0	: 1;
	U32 uwEntryPCADiffCnt : 9;
	U32 btReserved1	: 2;
	U32 btSRAMFail	: 1;
	U32 ubReserved2 : 8;
	U32 ulHitEntryNum : 9;
	U32 ulReserved3   : 23;
} XZIPEntryPCADiffResult_t;

#endif /* _E13_XZIP_CMD_TYPE_H_ */
