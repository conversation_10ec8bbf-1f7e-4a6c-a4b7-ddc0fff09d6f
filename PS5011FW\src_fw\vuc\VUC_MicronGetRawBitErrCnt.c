#include "vuc/VUC_MicronGetRawBitErrCnt.h"
#include "VUC_MicronGetNandErrorRecoveryStatistics.h"
#include "vuc/VUC_command.h"
#include "vuc/VUC_ReadFlash.h"
#include "VUC_MicronResponse.h"
#include "hal/fip/fip_api.h"
#include "hal/cop0/cop0_api.h"
#include "hal/dmac/dmac_pop_cmd.h"

#if (VUC_MICRON_NAND_VS_COMMANDS_EN && IM_N28)

U32 gMicronVUCReadCallBackAddr = (U32)VUCMicronRead_Callback;
U32 gMicronVUCDirectedCieOutInfo;
U32 gMicronReadChannel;
U32 gMicronReadECC;

void VUCMicronRead_Callback(TIEOUT_FORMAT_t uoResult)
{
	gMicronReadECC = M_FIP_GET_ECC_INF(gMicronReadChannel);
	gulTieoutLCA[TIEOUT_SINGLE_4K_LCA_OFFSET] = uoResult.HL.B0_to_B31.Read_1st.ulLCA;
}

void VUCMicronGetRawBitRead(U32 ulPhyAdr, U32 ulIFSA, U8 ubIsSLC)
{
	COP0Status_t eCOP0Status;
	COP0ReadSQPara_t ReadSQPara = {{0}};
	BUF_TYPE_t ulBufPara = {0};
	gMicronVUCDirectedCieOutInfo = 0;
	FWSetPara_t ulFWSet = {0};
	ulFWSet.ulFWSet = IRAM1_RSV_OFF ;
	ulFWSet.ubZInfo = MAX_ZINFO;

	cmd_table_t uoCallbackInfo = {(U32)VUCMicronRead_Callback, {0}};

	COP0API_FillCOP0ReadSQUserData0Para(COP0_R_INITIAL_SCAN, &ReadSQPara);

	ulBufPara.A.ulBUF_ADR = ulPhyAdr;

	ReadSQPara.UserData0.btSLCMode = (ubIsSLC == FALSE ) ? FALSE : TRUE;
	ReadSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;
	ReadSQPara.UserData0.TagID = TAG_ID_ALLOCATE;
	uoCallbackInfo.ulData.ulFLHInfo.btKeepTag = TRUE;
	ReadSQPara.ulPCA.ulAll = ulIFSA;
	ReadSQPara.UserData0.btRUTBps = TRUE;
	ReadSQPara.UserData0.btVBRMPBps = TRUE;
	ReadSQPara.UserData0.btSkipError = TRUE;
	ReadSQPara.UserData0.AttrMTTemplate = COP0_MT_TEMP_BUF_ADDR_READ;
	ReadSQPara.UserData0.L4KNum = 0;

	ReadSQPara.UserData0.DataDef |= COP0_SEED_EN_BIT;

	ReadSQPara.BufVld.ubBufType = BUF_TYPE_A;
	ReadSQPara.BufVld.pulBufInfoPtr = &ulBufPara;

	ReadSQPara.UserData0.DataDef |= COP0_FWSET_EN_BIT;
	ReadSQPara.pulFWSetPtr = &ulFWSet;
	eCOP0Status = COP0API_SendReadSQ(&ReadSQPara, &uoCallbackInfo);
	M_FW_ASSERT(ASSERT_VUC_0x0ABD, eCOP0Status.btSendCmdSuccess);

	gulTieoutLCA[TIEOUT_SINGLE_4K_LCA_OFFSET] = LCA_BEFORE_READ;
	while (LCA_BEFORE_READ == gulTieoutLCA[TIEOUT_SINGLE_4K_LCA_OFFSET]) {
		FWCop0Waiting();
	}
}

void VUCMicronGetRawBitErrorCnt(U32 ulInputPayloadAddr, U32 ulPayloadAddr)
{
	U8 ubLDPCMode[VUC_MICRON_LDPC_MODE_RETRY_TIMES] = {SET_ECC_MODE_0, SET_ECC_MODE_7}; // N28 use 0 & 7
	U8 ubReadMode[VUC_MICRON_READ_MODE_RETRY_TIMES] = {COP0_PCA_RULE_0, COP0_PCA_RULE_2};
	U8 ubLDPCIdx, ubReadIdx, ubPlane, ubChannel, ubCE, ubLUN, ulCodeWord, ubIsUNC = TRUE, ubLdpcTmp;
	U8 ubTmp[10] = "";
	U16 uwPage;
	U32 ulCMDStartAddr, ulIFSA, ulBlock;
	U32 ulFCON_CRC_EN_TMP, ulCOP0_TMP;
	U32 ulResponseAddr = ulPayloadAddr + VUC_MICRON_RESPONSE_HEADER_SIZE;
	GetRawBitErrInputData_t *pInputData;
	GetRawBitErrResponseHEADER_t *pResponseHeader;
	pResponseHeader = (GetRawBitErrResponseHEADER_t *)ulPayloadAddr;

	DMACParam_t DMACParam;

	ulCMDStartAddr = ulInputPayloadAddr + VUC_MICRON_GET_ERR_BIT_HEADER_LENGTH;
	pInputData = (GetRawBitErrInputData_t *)ulCMDStartAddr;
	ubChannel = (U8)pInputData->uwChannel;
	ubCE = (U8)pInputData->uwCE;
	ubLUN = (U8)pInputData->uwLUN;
	ulBlock = ((pInputData->uwBlock) >> gubBurstsPerBankLog)& gPCARule_Block.ulMask;
	ubPlane = (U8)(pInputData->uwBlock)& gubBurstsPerBankMask;
	uwPage = pInputData->uwPage;
	ulCodeWord = (U8)pInputData->uwCodeWord;

	gMicronReadChannel = ubChannel;

	DMACParam.DMACSetValue.ulDestAddr = ulPayloadAddr;
	DMACParam.DMACSetValue.ulLowValue = 0;
	DMACParam.DMACSetValue.ulHighValue = 0;
	DMACParam.DMACSetValue.ul32ByteNum = SIZE_IN_32B(DEF_KB(4));
	DMACParam.DMACSetValue.btValidate = FALSE;
	gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
	DMACSetValue(&DMACParam, gulDMACDirectWaitDone_Callback, 0);
	while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
		DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
	}

	pResponseHeader->ubResponseHeaderFormatVersion = 0x00;
	pResponseHeader->ubResponseDataFotmatVersion = VUC_MICRON_RESPONSE_FORMAT_JSON;
	pResponseHeader->uwCMDClass = VUC_MICRON_NAND_VS_COMMANDS;
	pResponseHeader->uwCMDCode = VUC_MICRON_GET_RAW_BIT_ERROR_COUNT;
	pResponseHeader->uwStatus = 0;
	pResponseHeader->ulDataPayloadSize = 0x30;

	M_FIP_VUC_MICRON_READ_RAW_ERROR_BIT_BACK_UP(ubLdpcTmp, ulCOP0_TMP, ulFCON_CRC_EN_TMP);

	// Only care ECC bit number
	M_CLR_COP0_LCA_CMP();
	M_FIP_VUC_DIRECTED_READ_CLOSE_CRC();
	M_SET_COP0_CONV(COP0_MT_TEMP_BUF_ADDR_READ);

	//UartPrintf("\nInput CH:%x, CE:%x, LUN:%x, Blk:%x, Plane:%x, Page:%x, CW:%x", ubChannel, ubCE, ubLUN, ulBlock, ubPlane, uwPage, ulCodeWord);

	// Default use TLC mode & LDPC mode 0
	for (ubLDPCIdx = 0; ubLDPCIdx < VUC_MICRON_LDPC_MODE_RETRY_TIMES; ubLDPCIdx++) {
		if (FALSE == ubIsUNC) {
			break;
		}
		FlaSwitchLDPCHandler(ubLDPCMode[ubLDPCIdx]);
		for (ubReadIdx = 0; ubReadIdx < VUC_MICRON_READ_MODE_RETRY_TIMES; ubReadIdx++) {
			// TLC cannot be Mode7
			if ((COP0_PCA_RULE_0 == ubReadMode[ubReadIdx]) && ( SET_ECC_MODE_7 == ubLDPCMode[ubLDPCIdx])) {
				continue;
			}
			// Invalid Page in SLC
			if ((COP0_PCA_RULE_2 == ubReadMode[ubReadIdx]) && (uwPage >= (gFlhEnv.uwPagePerBlock / 3))) {
				continue;
			}

			ulIFSA = FlaGetPCA(ubPlane, ubChannel, ubCE, ubLUN, uwPage, ulBlock, ubReadMode[ubReadIdx]);
			ulIFSA |= (ulCodeWord & gPCARule_Entry.ulMask) << gPCARule_Entry.ubShift[ubReadMode[ubReadIdx]];

			VUCMicronGetRawBitRead(ulInputPayloadAddr, ulIFSA, ubReadMode[ubReadIdx]);

			if ( TMSG_TYPE_NO_ERROR == gMicronVUCDirectedCieOutInfo) {
				//UartPrintf("\nSuccess ALU Mode:%x, LDPC Mode:%x", ubReadMode[ubReadIdx], ubLDPCMode[ubLDPCIdx]);
				ubIsUNC = FALSE;
				break;
			}
			//UartPrintf("\nFail ALU Mode:%x, LDPC Mode:%x", ubReadMode[ubReadIdx], ubLDPCMode[ubLDPCIdx]);
		}
	}

	if (TRUE == ubIsUNC) {
		//UartPrintf("\nTry all fail.");
	}

	VUCMyStrcat((void *)ulResponseAddr, "{\n\t");
	VUCMyStrcat((void *)ulResponseAddr, "\"structVer\":0,\n\t");
	VUCMyStrcat((void *)ulResponseAddr, "\"Count\":");
	VUCMyitoa(gMicronReadECC, (void *)ubTmp, DECIMAL);
	VUCMyStrcat((void *)ulResponseAddr, (void *)ubTmp);
	VUCMyStrcat((void *)ulResponseAddr, "\n}");

	M_FIP_VUC_MICRON_READ_RAW_ERROR_BIT_RESTORE(ulCOP0_TMP, ulFCON_CRC_EN_TMP);
	FlaSwitchLDPCHandler(ubLdpcTmp);
}
#endif /*(VUC_MICRON_NAND_VS_COMMANDS_EN)*/
