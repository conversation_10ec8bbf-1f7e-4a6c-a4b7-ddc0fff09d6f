#include "retry/retry.h"
#include "retry/retry_api.h"
#include "retry/retry_hb.h"
#include "retry_micron_b47r_tlc_E13_neutral_hb.h"
#include "hal/pic/uart/uart_api.h"
#include "retry/retry_version_api.h" // Retry Version
#include "table/initinfo_vt/initinfo_vt.h" // VT
#include "ftl/ftl_api.h" // FW Timer
#include "hal/fip/fpu.h"

#if (PS5013_EN && (FLASH_B47R_TLC == FW_CATEGORY_FLASH) && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER))
/*
 * ----------------------------------------------------------------------------
 *   Definitions
 * ----------------------------------------------------------------------------
 */
#define RETRY_HB_MICRON_RECORD_FEATURE_NUM		(6)

/*
 * ----------------------------------------------------------------------------
 *   Enum
 * ----------------------------------------------------------------------------
 */

/*
 * ----------------------------------------------------------------------------
 *   Data Types
 * ----------------------------------------------------------------------------
 */

/*
 * ----------------------------------------------------------------------------
 *   Macros
 * ----------------------------------------------------------------------------
 */

/*
 * ----------------------------------------------------------------------------
 *   Extern Global Variables
 * ----------------------------------------------------------------------------
 */

/*
 * ----------------------------------------------------------------------------
 *   Static Global Variables
 * ----------------------------------------------------------------------------
 */

#if (VIRTUAL_ADDRESS_EN)
U8 gubRetryHBMicronTLCRetryTable[HBIT_RETRY_MICRON_TLC_FEA_CNT * HBIT_RETRY_MICRON_FEA_DATA_NUM] = {
	0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07
};

U8 gubRetryHBMicronSLCRetryTable[HBIT_RETRY_MICRON_SLC_FEA_CNT * HBIT_RETRY_MICRON_FEA_DATA_NUM] = {
	0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07
};

#else/* VIRTUAL_ADDRESS_EN */
U8 gubRetryHBMicronTLCRetryTable[HBIT_RETRY_MICRON_TLC_FEA_CNT * HBIT_RETRY_MICRON_FEA_DATA_NUM] = {
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};

U8 gubRetryHBMicronSLCRetryTable[HBIT_RETRY_MICRON_SLC_FEA_CNT * HBIT_RETRY_MICRON_FEA_DATA_NUM] = {
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
};
#endif /* VIRTUAL_ADDRESS_EN */

U8 gubRetrySetFeatureAddr[RETRY_HB_MICRON_RECORD_FEATURE_NUM] = {0};
U8 gubRetrySetFeatureData[RETRY_HB_MICRON_RECORD_FEATURE_NUM] = {0};
U8 gubRetryGetFeatureAddr[RETRY_HB_MICRON_RECORD_FEATURE_NUM] = {0};
U8 gubRetryGetFeatureData[RETRY_HB_MICRON_RECORD_FEATURE_NUM] = {0};

/*
 * ----------------------------------------------------------------------------
 *   Private Prototypes
 * ----------------------------------------------------------------------------
 */

/*
 * ----------------------------------------------------------------------------
 *   Private
 * ----------------------------------------------------------------------------
 */

INLINE void HBRetryInitRegisterRetryTable(void)
{
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubCurrentStep = HB_READ_START_STEP;
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubLastSuccessIndex = 0;
	gpHBParameterArray[FPL_ADDR_TLC_RULE].pubRetryTable = (U8 *)(&gubRetryHBMicronTLCRetryTable);
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubRetryParameterSize = HBIT_RETRY_MICRON_FEA_DATA_NUM;
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubParameterNumPerFPU = PARAMETER_NUM_PER_FPU;
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubTotalRetryCnt = sizeof(gubRetryHBMicronTLCRetryTable) / HBIT_RETRY_MICRON_FEA_DATA_NUM;

	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubCurrentStep = HB_READ_START_STEP;
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubLastSuccessIndex = 0;
	gpHBParameterArray[FPL_ADDR_SLC_RULE].pubRetryTable = (U8 *)(&gubRetryHBMicronSLCRetryTable);
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubRetryParameterSize = HBIT_RETRY_SLC_FEA_DATA_NUM;
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubParameterNumPerFPU = PARAMETER_NUM_PER_FPU;
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubTotalRetryCnt = sizeof(gubRetryHBMicronSLCRetryTable) / HBIT_RETRY_MICRON_FEA_DATA_NUM;
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubScratchTableIdx = INVALID_RETRY_TABLE_INDEX;
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubScratchTableIdx = INVALID_RETRY_TABLE_INDEX;
}

INLINE void HBRetryInitAdjustVthFPU(void)
{
	// Register set feature fpu
	gFpuEntryList.fpu_set_feature[0] = FPU_CMD(0xD5); // Set feature cmd
	gFpuEntryList.fpu_set_feature[7] = FPU_END;

	// Register get feature fpu
	gFpuEntryList.fpu_get_feature[0] = FPU_CMD(0xD4); // Get feature cmd
	gFpuEntryList.fpu_get_feature[3] = FPU_END;

	// Register read and compare feature data fpu
	gFpuEntryList.fpu_read_and_compare_feature_data[0] = FPU_CMD(0x00); // Switch to read mode
	gFpuEntryList.fpu_read_and_compare_feature_data[1] = FPU_NOP;
	gFpuEntryList.fpu_read_and_compare_feature_data[10] = FPU_END;
}

/*
 * ------------------------------------
 * 		Over Wirte Weak Function
 * ------------------------------------
 */

U8 HBRetryGetPageType(U64 uoiFSA, U8 ubALUSel)
{
	U8 ubPageType;
	RetryPageCoordinateInfo_t PageCoordinateInfo = RetryGetSharedPageType((U32)uoiFSA, ubALUSel);
	if ((0 == PageCoordinateInfo.uwY) || (177 == PageCoordinateInfo.uwY)) {
		ubPageType = HB_LRU_MICRON_SLC_WL;
	}
	else if ((88 == PageCoordinateInfo.uwY) || (89 == PageCoordinateInfo.uwY)) {
		if (HB_PAGE_LOWER == PageCoordinateInfo.ubLUX) {
			ubPageType = HB_LRU_MICRON_MLC_WL_LOWER;
		}
		else {
			ubPageType = HB_LRU_MICRON_MLC_WL_UPPER;
		}
	}
	else {
		ubPageType = HB_LRU_MICRON_TLC_WL_LOWER + PageCoordinateInfo.ubLUX;
	}
	M_HB_DEBUG_UART("\n [HB] P:%d X:%d Y:%d LUX:%d T:%d \n", PageCoordinateInfo.uwPage, PageCoordinateInfo.ubX, PageCoordinateInfo.uwY, PageCoordinateInfo.ubLUX, gHBParamMgr.ubPageType);
	return ubPageType;
}

void HBRetrySelectParameterTable(void)
{
	U8 ubALUSel = gHBTask.MTTemplate.dma.ALUSelect;
	U64 uoiFSA = ((U64)gHBTask.MTTemplate.dma.ubiFSA0_h << 32) | gHBTask.MTTemplate.dma.uliFSA0_1;
	RetryPCARuleSet_t *pRuleSet = gpOtherInfo->pRuleSet;

	gHBParamMgr.ubSLCMode = (ubALUSel >> 1) & BIT0;
	gHBParamMgr.ubChannel = (uoiFSA >> pRuleSet->pChannel->ubShift[ubALUSel]) & pRuleSet->pChannel->ulMask;
	gHBParamMgr.ubBank = (uoiFSA >> pRuleSet->pBank->ubShift[ubALUSel]) & pRuleSet->pBank->ulMask;
	gHBParamMgr.ubDie = ((uoiFSA >> pRuleSet->pLun->ubShift[ubALUSel]) & pRuleSet->pLun->ulMask) << pRuleSet->pDie_IL->ubBit_No;
	gHBParamMgr.ubDie |= (uoiFSA >> pRuleSet->pDie_IL->ubShift[ubALUSel]) & pRuleSet->pDie_IL->ulMask;
	gHBParamMgr.ubPlane = (uoiFSA >> pRuleSet->pPlane->ubShift[ubALUSel]) & pRuleSet->pPlane->ulMask; // QLC
	if (RETRY_HB_LRU_BY_DIE) {
		gHBParamMgr.ubGlobalDie = M_GLOBAL_DIE_IDX(gHBParamMgr.ubDie, gHBParamMgr.ubChannel, gHBParamMgr.ubBank, BIT(gPCARule_LUN.ubBit_No), BIT(gPCARule_Channel.ubBit_No));
		M_FW_CRITICAL_ASSERT(ASSERT_RETRY_0x0D65, (gHBParamMgr.ubGlobalDie < RETRY_HB_LRU_MAX_DIE_NUM));
	}
	gHBParamMgr.ubSetFeatureDoneMTCnt = 0;
	gHBParamMgr.ubSetFeatureRetryCnt = 0;
	gHBParamMgr.ubGetFeatureDoneMTCnt = 0;

	gHBParamMgr.bt1stRetryReadCMD = TRUE;
	gHBParamMgr.ubPageType = HBRetryGetPageType(uoiFSA, ubALUSel);

	gHBParamMgr.ubResetDie = 0; // For Multi Die Reset and Polling every Die. Toshiba Not Use, but still assign 0, toshiba use ff to reset.
	// Select Retry Parameter Table
	gHBParamMgr.pParam = &gpHBParameterArray[gHBParamMgr.ubSLCMode];
	gHBParamMgr.pParam->ubCurrentStep = HB_READ_START_STEP;
	gHBParamMgr.pParam->ubLastStep = INVALID_RETRY_TABLE_INDEX;
	gHBParamMgr.ubSetFeatureToDoMTCnt = HB_RETRY_MICRON_SET_GET_MT_STEP_NUM;
}

void HBRetryResetFlashPass_Callback(U8 ubMTIdx)
{
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_HB_RETRY;
	if ((gubDieNumber - 1) == gHBParamMgr.ubResetDie) {
		gHBTask.ubState = RETRY_HB_STATE_PRECONDITION_WITHOUT_SETFEATURE;
		gHBParamMgr.ubResetDie = 0;
	}
	else {
		gHBParamMgr.ubResetDie++;
		gHBTask.ubState = RETRY_HB_STATE_RESET_FLASH;
	}
}

void HBRetryPostconditionSetFeaturePass_Callback(U8 ubMTIdx)
{
	gHBParamMgr.ubSetFeatureDoneMTCnt++;

	if (gHBParamMgr.ubSetFeatureDoneMTCnt == gHBParamMgr.ubSetFeatureToDoMTCnt) {
		gHBParamMgr.ubGetFeatureDoneMTCnt = 0;
		gHBParamMgr.ubSetFeatureToDoMTCnt = 0x0b; //for B47R Get Check Feature, 0xA0~ 0xAB should be 0
		gHBTask.ubState = RETRY_HB_STATE_CHECK_POSTCONDITION; // Check set feature result
	}
	else {
		gHBTask.ubState = RETRY_HB_STATE_POSTCONDITION;
	}
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_HB_RETRY;
}

void HBRetryClearReadOffsetPassCallback(U8 ubMTIdx)
{
	M_HB_DEBUG_UART("\n HBRetryClearReadOffsetPassCallback ! HBRetryFrameMap:%x ", gHBTask.ubHBRetryFrameMap);
	M_HB_DEBUG_UART("\n FTL.btSPORDoing:%x, btSPORReadJournal:%x ,PartialBlkMediaScan:%x", gpVT->FTL.btSPORDoing, gpVT->FTL.btSPORReadJournal, gHBTask.btPartialBlkMediaScan);
	//Reset Tables Idx
	HBRetryLRUIdxInit();
	//Clean Used ReadRretryTableIdx BM
	HBRetryTableUsedBMPInit();
	if (gHBTask.ubHBRetryFrameMap && (TRUE == RetryHBCheckSPORContinueCondition())) {
		U8 ubIdx = HBRetryGetRetryTableIdx();
		gHBParamMgr.pParam->ubLastStep = INVALID_RETRY_TABLE_INDEX;
		if (INVALID_RETRY_TABLE_INDEX != ubIdx) {
			gHBParamMgr.pParam->ubCurrentStep = ubIdx;
			gHBTask.ubState = RETRY_HB_STATE_PRECONDITION_WITHOUT_SETFEATURE; // Restart retry flow for next error frame
		}
		else {
			//Reset Tables Idx
			HBRetryLRUIdxInit();
			//Clean Used ReadRretryTableIdx BM
			HBRetryTableUsedBMPInit();
			gHBTask.ubHBFailFrameMap |= gHBTask.ubHBRetryFrameMap; // Update remain frame map
			gHBTask.ubState = RETRY_HB_STATE_POSTCONDITION_DONE_RESET_FLASH;
		}
	}
	else {
		gHBTask.ubState = RETRY_HB_STATE_POSTCONDITION_DONE_RESET_FLASH;
	}
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_HB_RETRY;
}

void HBRetryPostconditionGetFeatureCheckPass_Callback(U8 ubMTIdx)
{
	gHBParamMgr.ubGetFeatureDoneMTCnt++;
	if (gHBParamMgr.ubGetFeatureDoneMTCnt == gHBParamMgr.ubSetFeatureToDoMTCnt) { // All feature data check done
		//Reset Tables Idx
		HBRetryLRUIdxInit();
		//Clean Used ReadRretryTableIdx BM
		HBRetryTableUsedBMPInit();
		if (gHBTask.ubHBRetryFrameMap && (TRUE == RetryHBCheckSPORContinueCondition())) {
			U8 ubIdx = HBRetryGetRetryTableIdx();
			gHBParamMgr.ubSetFeatureDoneMTCnt = 0;
			gHBParamMgr.pParam->ubLastStep = INVALID_RETRY_TABLE_INDEX;
			if (INVALID_RETRY_TABLE_INDEX != ubIdx) {
				gHBParamMgr.pParam->ubCurrentStep = ubIdx;
				gHBTask.ubState = RETRY_HB_STATE_PRECONDITION_WITHOUT_SETFEATURE; // Restart retry flow for next error frame
			}
			else {
				//Reset Tables Idx
				HBRetryLRUIdxInit();
				//Clean Used ReadRretryTableIdx BM
				HBRetryTableUsedBMPInit();
				gHBTask.ubHBFailFrameMap |= gHBTask.ubHBRetryFrameMap; // Update remain frame map
				gHBTask.ubState = RETRY_HB_STATE_CLEAR_READ_OFFSET;
			}
		}
		else {
			gHBTask.ubState = RETRY_HB_STATE_CLEAR_READ_OFFSET;
		}
	}
	else {
		gHBTask.ubState = RETRY_HB_STATE_CHECK_POSTCONDITION; // Check remain set feature MT
	}
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_HB_RETRY;
}

void HBRetryPostconditionGetFeatureCheckFail_Callback(U8 ubMTIdx)
{
	gHBParamMgr.ubSetFeatureDoneMTCnt = 0;
	//CheckFeature();
	if (HB_MAX_SET_FEATURE_RETRY_CNT == gHBParamMgr.ubSetFeatureRetryCnt) {
		//Reset Tables Idx
		HBRetryLRUIdxInit();
		//Clean Used ReadRretryTableIdx BM
		HBRetryTableUsedBMPInit();
		if (gHBTask.ubHBRetryFrameMap && (TRUE == RetryHBCheckSPORContinueCondition())) {
			U8 ubIdx = HBRetryGetRetryTableIdx();
			gHBParamMgr.ubSetFeatureDoneMTCnt = 0;
			gHBParamMgr.pParam->ubLastStep = INVALID_RETRY_TABLE_INDEX;
			if (INVALID_RETRY_TABLE_INDEX != ubIdx) {
				gHBParamMgr.pParam->ubCurrentStep = ubIdx;
				gHBTask.ubState = RETRY_HB_STATE_PRECONDITION_WITHOUT_SETFEATURE; // Restart retry flow for next error frame
			}
			else {
				//Reset Tables Idx
				HBRetryLRUIdxInit();
				//Clean Used ReadRretryTableIdx BM
				HBRetryTableUsedBMPInit();
				gHBTask.ubHBFailFrameMap |= gHBTask.ubHBRetryFrameMap; // Update remain frame map
				gHBTask.ubState = RETRY_HB_STATE_CLEAR_READ_OFFSET;
			}
		}
		else {
			//Clean Passed ReadRretryTableIdx BM
			HBRetryTableUsedPassedBMPInit();
			gHBTask.ubState = RETRY_HB_STATE_CLEAR_READ_OFFSET;
		}
		gpRetry->RetryStateCnt.ulSetFeatureFail++;
		gpRetry->RetryJobList[gpRetry->ubHead].btPostconditionSetFeatureFail = TRUE;
#if (!RELEASED_FW)
		DebugError();
#endif
	}
	else {
		gHBTask.ubState = RETRY_HB_STATE_POSTCONDITION;
		gHBParamMgr.ubSetFeatureRetryCnt++;
	}
	gpVTDBUF->Retry.ulSetFeatureFailCnt++;
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_HB_RETRY;
}

void HBRetryReadDMAPass_Callback(U8 ubMTIdx)
{
	FlhMT_t *pMT = (FlhMT_t *)M_MT_ADDR(ubMTIdx);
	U8 ubDMAFrameMap = (U8)(BIT_MASK(pMT->dma.FrameNum) << (pMT->dma.uliFSA0_1 & gpOtherInfo->pRuleSet->pEntry->ulMask));
#if (PS5017_EN)
	U32 ulBackupBase;
	U32 ulTargetBase;
	ulBackupBase = IRAM_BASE + GC_BACKUP_OFF + (ERR_HDL_MAX_L4K_PER_PAGE * SPARE_SIZE * ubMTIdx);
	ulTargetBase = IRAM_BASE + gHBTask.ulSprOffset[0] + ((pMT->dma.uliFSA0_1 & gpOtherInfo->pRuleSet->pEntry->ulMask) * SPARE_SIZE);
	memcpy((void *)ulTargetBase, (void *)ulBackupBase, SPARE_SIZE * pMT->dma.FrameNum);
#endif /* (PS5017_EN) */

	if (NCS_EN) {
		if (gubNeedSendNCSFlag) {
			// Get a new MT to avoid covering ECC info during delegating VUC flash CQ
			U8 ubNCSMTIdx = FlaGetFreeMTIndex();
			FIPVUCSendNCSInfo(ubNCSMTIdx, &gHBTask.MTTemplate, NCS_WRITE_FRAME_INFO, gHBTask.ulBufAddr[0], gHBTask.ulSprOffset[0], 0);
			FIPVUCSendNCSInfo(ubNCSMTIdx, &gHBTask.MTTemplate, NCS_WRITE_ERROR_BIT_INFO, 0, 0, gHBParamMgr.pParam->ubCurrentStep);
			FIPVUCSendNCSInfo(ubNCSMTIdx, &gHBTask.MTTemplate, NCS_WRITE_HW_SETTING_INFO, 0, 0, 0);
			FlaAddFreeMTIndex(ubNCSMTIdx);
		}
	}

#if (NCS_V2_EN)
	if (gSystmAreaFWSettingFromInfoBlk.Init.Flag.btNCS2En) {
		RetryUartSendNCSHBInfo(gHBTask.MTTemplate.dma.uliFSA0_1 & gub4kEntrysPerPlaneMask, gHBTask.ulBufAddr[0], gHBTask.ulSprOffset[0], TRUE);
	}
#endif /* (NCS_V2_EN) */

	M_HB_DEBUG_UART("\nHB Step:%x, DMAMap:%x, ", gHBParamMgr.pParam->ubCurrentStep, ubDMAFrameMap);

	// Update frame map
	gHBTask.ubHBRetryFrameMap &= (~ubDMAFrameMap);

	M_HB_DEBUG_UART("Remain:%x\n", gHBTask.ubHBRetryFrameMap);

	gHBParamMgr.pParam->ubLastSuccessIndex = gHBParamMgr.pParam->ubCurrentStep;
	gHBParamMgr.ubSetFeatureDoneMTCnt = 0;

	if (gHBParamMgr.pParam->ubCurrentStep == HB_NORMAL_READ_STEP) {
		if (gHBParamMgr.ubSLCMode) {
			gpVTDBUF->Retry.ulSLCNormalReadPassCnt++;
		}
		else {
			gpVTDBUF->Retry.ulNormalReadPassCnt++;
		}
	}
	else {
		if (gHBParamMgr.ubSLCMode) {
			gpVTDBUF->Retry.ulSLCHBStepPassFrameCnt[gHBParamMgr.pParam->ubCurrentStep]++;
		}
		else {
		}
		if (DEBUG_CMD_LIFETIME && M_GET_FW_TIMER() > 0) {
			++gCmdLifeTimeCount.ubHBRetrycnt;
		}
	}


	if (gHBParamMgr.pParam->ubCurrentStep != HB_NORMAL_READ_STEP) {
		//If Frame pass record this RetryTableIdx in LRU
		HBRetryLRUAddNode (gHBParamMgr.pParam->ubCurrentStep);	//"Step" to "Index"
	}
	else {
		if (HB_LRU_TABLE_DEBUG_UART) {
			M_UART(ERROR_HARDBIT_, "\n Step == HB_NORMAL_READ_STEP, in Pass");
		}
	}

	if (HB_LRU_TABLE_DEBUG_UART) {
		M_UART(ERROR_HARDBIT_, "\n Pass Reset to LRU");
	}
	//Reset Tables Idx
	HBRetryLRUIdxInit();
	//Clean Used ReadRretryTableIdx BM
	HBRetryTableUsedBMPInit();

	if (gHBTask.ubHBRetryFrameMap) {
		if (gHBParamMgr.pParam->ubCurrentStep != HB_NORMAL_READ_STEP) {
			gHBLRUTask.ubHBLastMTPass = TRUE;	//Record for later, if Fail
		}

		gHBParamMgr.pParam->ubLastStep = gHBParamMgr.pParam->ubCurrentStep;	//For Later Precondition Clear RR or Readoffset FA when switch Between RR and ReadOffset
		gHBParamMgr.pParam->ubCurrentStep = HB_READ_START_STEP;
		gHBTask.ubState = RETRY_HB_STATE_PRECONDITION_WITHOUT_SETFEATURE;
	}
	else {
		gHBLRUTask.ubHBLastMTPass = FALSE;
		gHBTask.ubState = RETRY_HB_STATE_CLEAR_READ_OFFSET;
	}

	gMTMgr.ubRecordExtInfoMT = INVALID_MT_INDEX;
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_HB_RETRY;
}


void HBRetryReadDMAFail_Callback(U8 ubMTIdx)
{
	FlhMT_t *pMT = (FlhMT_t *)M_MT_ADDR(ubMTIdx);
	U8 ubErrorP4KBMP;
	U8 ub1stErrFrameIndex;
	U8 ubDMAFrameMap = (U8)(BIT_MASK(pMT->dma.FrameNum) << (pMT->dma.uliFSA0_1 & gpOtherInfo->pRuleSet->pEntry->ulMask));

#if (PS5017_EN)
	U32 ulBackupBase;
	U32 ulTargetBase;

	ulBackupBase = IRAM_BASE + GC_BACKUP_OFF + (ERR_HDL_MAX_L4K_PER_PAGE * SPARE_SIZE * ubMTIdx);
	ulTargetBase = IRAM_BASE + gHBTask.ulSprOffset[0] + ((pMT->dma.uliFSA0_1 & gpOtherInfo->pRuleSet->pEntry->ulMask) * SPARE_SIZE);
	memcpy((void *)ulTargetBase, (void *)ulBackupBase, SPARE_SIZE * pMT->dma.FrameNum);
#endif /* (PS5017_EN) */

	ubErrorP4KBMP = FIPCalculateP4KErrorFrameMap(ubMTIdx);

	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, ubErrorP4KBMP);
	M_HB_DEBUG_UART("OldMap:%x PErrMap:%x DMAMap:%x", gHBTask.ubHBRetryFrameMap, ubErrorP4KBMP, ubDMAFrameMap);

	// Mask with original error frame map (Prevent pass frame become UNC during read retry)
	gHBTask.ubHBRetryFrameMap &= ((~ubDMAFrameMap) | ubErrorP4KBMP);

	gHBParamMgr.ubSetFeatureDoneMTCnt = 0;
	gHBParamMgr.ubSetFeatureRetryCnt = 0;

	for (ub1stErrFrameIndex = 0 ; ub1stErrFrameIndex < HB_MAX_4K_FRAME_NUM; ub1stErrFrameIndex++) {
		if (ubErrorP4KBMP & BIT(ub1stErrFrameIndex)) {
			break;
		}
	}
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, ub1stErrFrameIndex != HB_MAX_4K_FRAME_NUM);

	/*
	 * When DMA fail, the cases that needs to re-write info back to NCS :
	 * 1. PTYCHSUM more than threshold -> no need to do trapping set -> re-write
	 * 2. Already in trapping set decoding -> need to re-write decoded result -> re-write
	 */
	if (NCS_EN) {
		if (((gMTMgr.uwPTYCHSUM[ub1stErrFrameIndex] >= TRAPPING_SET_THRESHOLD) || (TRUE == gHBTask.btTrappingSetFlowEn)) && gubNeedSendNCSFlag) {  /* parasoft-suppress BD-PB-CC "Register value from R32_FCTL_CHKSUM"*/
			// Get a new MT to avoid covering ECC info during delegating VUC flash CQ
			U8 ubNCSMTIdx = FlaGetFreeMTIndex();
			FIPVUCSendNCSInfo(ubNCSMTIdx, &gHBTask.MTTemplate, NCS_WRITE_FRAME_INFO, gHBTask.ulBufAddr[0], gHBTask.ulSprOffset[0], 0);
			FIPVUCSendNCSInfo(ubNCSMTIdx, &gHBTask.MTTemplate, NCS_WRITE_ERROR_BIT_INFO, 0, 0, gHBParamMgr.pParam->ubCurrentStep);
			FIPVUCSendNCSInfo(ubNCSMTIdx, &gHBTask.MTTemplate, NCS_WRITE_HW_SETTING_INFO, 0, 0, 0);
			FlaAddFreeMTIndex(ubNCSMTIdx);
		}
	}

#if (NCS_V2_EN)
	if (gSystmAreaFWSettingFromInfoBlk.Init.Flag.btNCS2En && ((gMTMgr.uwPTYCHSUM[ub1stErrFrameIndex] >= TRAPPING_SET_THRESHOLD) || (TRUE == gHBTask.btTrappingSetFlowEn))) {
		RetryUartSendNCSHBInfo(gHBTask.MTTemplate.dma.uliFSA0_1 & gub4kEntrysPerPlaneMask, gHBTask.ulBufAddr[0], gHBTask.ulSprOffset[0], FALSE);
	}
#endif /* (NCS_V2_EN) */

	if (RETRY_HB_STATE_WAIT_READ_AGAIN == gHBTask.ubState) {
		gHBTask.ubState = RETRY_HB_STATE_RESET_FLASH;
	}
	else if ((FALSE == RetryHBCheckSPORContinueCondition()) && (gpRetry->RetryJobList[gpRetry->ubHead].ulInterruptInfo & ERASE_PAGE_UPD_BIT)) {
		gHBTask.ubHBFailFrameMap |= (ubDMAFrameMap | gHBTask.ubHBRetryFrameMap); // Update fail frame map
		gHBTask.ubState = RETRY_HB_STATE_CLEAR_READ_OFFSET;
	}
	else {
		U8 ubIdx;
		if (gHBParamMgr.bt1stRetryReadCMD) {
			gHBParamMgr.bt1stRetryReadCMD = FALSE;
		}

		// Check array bound for td3
		if (gHBParamMgr.pParam->ubCurrentStep < HB_RETRY_TABLE_BM_BYTE_NUM * BITS_PER_BYTE) {
			//Mark this ReadRetryTableIdx on BM
			M_SET_BITMAP(gubRetryTableUsed_BM, gHBParamMgr.pParam->ubCurrentStep);
		}

		ubIdx = HBRetryGetRetryTableIdx();
		gHBParamMgr.pParam->ubLastStep = gHBParamMgr.pParam->ubCurrentStep;

		if (INVALID_RETRY_TABLE_INDEX == ubIdx) {

			if (gHBTask.ubState == RETRY_HB_STATE_WAIT_SEARCH_PASS_STEP_READ) {
				gHBTask.ubHBFailFrameMap |= ubDMAFrameMap; // Update fail frame map
				gHBTask.ubHBRetryFrameMap &= (~ubDMAFrameMap); // Clear DMA frame map
			}

			if (gHBTask.ubHBRetryFrameMap && (TRUE == RetryHBCheckSPORContinueCondition())) {
				gHBTask.ubState = RETRY_HB_STATE_CLEAR_READ_OFFSET;
			}
			else {
				gHBTask.ubHBFailFrameMap |= gHBTask.ubHBRetryFrameMap; // Update remain frame map
				gHBTask.ubState = RETRY_HB_STATE_CLEAR_READ_OFFSET;
			}
		}
		else {
			gHBParamMgr.pParam->ubCurrentStep = ubIdx;
			gHBTask.ubState = RETRY_HB_STATE_PRECONDITION_WITHOUT_SETFEATURE; // Try next retry table
		}
	}
	M_HB_DEBUG_UART(", NewRetryFrameMap:%x, FailMap:%x\n", gHBTask.ubHBRetryFrameMap, gHBTask.ubHBFailFrameMap);

	gMTMgr.ubRecordExtInfoMT = INVALID_MT_INDEX;
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_HB_RETRY;
	if ((RETRY_HB_STATE_CLEAR_READ_OFFSET == gHBTask.ubState) && (FALSE == gHBTask.btCreateByRaidECC) &&
		((gMTMgr.ulINTInfo[ubMTIdx - MT_RETRY_START_INDEX] & ERASE_PAGE_UPD_BIT) ||
			(gMTMgr.ubMTDoneMsg[ubMTIdx - MT_RETRY_START_INDEX].btErasePage))) {
		// Retry result is erased page
		gpRetry->RetryJobList[gpRetry->ubHead].btResultIsErasePage = TRUE;
	}
}


void HBRetryInitParameter(void)
{
	M_FW_ASSERT(ASSERT_HARDBIT_RETRY_0x0D81, (ID_MICRON == gpOtherInfo->ubMakerCode) && (RETRY_MICRON_FLASH_PROCESS_B47R == gpOtherInfo->ubProcess));
	HBRetryInitRegisterRetryTable();
	HBRetryInitAdjustVthFPU();
}

U16 HBRetrySelectResetCMDFPU(void)
{
	//Reset CMD 0xFA is By LUN(Die)
	return guwFPUEntryResetFAh[gHBParamMgr.ubDie];
}

U16 HBRetrySelectReadCMDFPU(void)
{
	U16 uwRetFPUPtr = 0;
	if (gHBTask.MTTemplate.dma.ALUSelect & ALU_RULE_SLC_BIT) { // BIT1 = SLC
		uwRetFPUPtr = FPU_PTR_SLC_RETRY_READ_ACRR_CMD(gHBParamMgr.ubCurACRR);
	}
	else {
		uwRetFPUPtr = FPU_PTR_TLC_RETRY_READ_ACRR_CMD(gHBParamMgr.ubCurACRR);
	}
	return uwRetFPUPtr;
}

U16 HBRetryPreconditionSetFeatureFPU(void)
{
	FPL_HBIT_RETRY_PARAM_STRUCT_PTR pParam = gHBParamMgr.pParam;
	U8 ubReadOffsetMTToDoCnt = 0;
	U16 uwFPUPtr;
	U16 *puwFPU;

	/*
	*	HBIT_RETRY_MICRON_ORI_STEP_NUM	 = 1
	*   HBIT_RETRY_MICRON_ACRR_STEP_NUM  = 7
	*/
	gHBParamMgr.ubSetFeatureToDoMTCnt = 0;
	// 2nd feature address
	if ((HB_RETRY_MICRON_ALL_NUM) > pParam->ubCurrentStep) {
		//For  Micron Phison Flow, ACRR is base on gHBParamMgr.pParam->ubCurrentStep
		//ex. ACRR_0 == HB_Step_0, ACRR_1 == HB_Step_1, ACRR_2 == HB_Step_2 ...
		gHBParamMgr.ubCurACRR = pParam->ubCurrentStep;
	}

	// Get FPU PTR
	uwFPUPtr = FPU_PTR_OFFSET(fpu_set_feature);
	puwFPU = (U16 *)(IRAM_BASE + uwFPUPtr);

	// Set feature cmd, already fill FPU[0] in init state
	//puwFPU[0] = FPU_CMD(0xD5);

	// 1st feature address (LUN address)
	puwFPU[1] = FPU_ADR_1B(gHBParamMgr.ubDie);
	/*
	*	HBIT_RETRY_MICRON_ORI_STEP_NUM	 = 8
	*   HBIT_RETRY_MICRON_ACRR_STEP_NUM  = 4
	*	HBIT_RETRY_MICRON_ARC_STEP_NUM	 = 10
	*/

	// 2nd feature address
	if ((HB_RETRY_MICRON_ALL_NUM) > pParam->ubCurrentStep) {
		gHBParamMgr.ubSetFeatureToDoMTCnt = ubReadOffsetMTToDoCnt + 1;

		U16 uwFPUPtr1;
		U16 *puwFPU1;
		U8 ubPrefixOffset;

		if (gHBTask.MTTemplate.dma.ALUSelect & ALU_RULE_SLC_BIT) { // BIT1 = SLC
			uwFPUPtr1 = FPU_PTR_SLC_RETRY_READ_ACRR_CMD(gHBParamMgr.ubCurACRR);
			ubPrefixOffset = 1;
		}
		else {
			uwFPUPtr1 = FPU_PTR_TLC_RETRY_READ_ACRR_CMD(gHBParamMgr.ubCurACRR);
			ubPrefixOffset = 0;
		}
		puwFPU1 = (U16 *)(IRAM_BASE + uwFPUPtr1);

		// Phison Flow Prefix All 0
		puwFPU1[1 + ubPrefixOffset] = FPU_ADR_1B(0);
		puwFPU1[2 + ubPrefixOffset] = FPU_ADR_1B(0);
		puwFPU1[3 + ubPrefixOffset] = FPU_ADR_1B(0);

		puwFPU1[8 + ubPrefixOffset] = FPU_CMD(0x30);
		M_HB_DEBUG_UART("\n[HB] Step%d, CruACRR:%d T:%d FPU1:%x FPU2:%x FPU3:%x, FPU7: %x\n", pParam->ubCurrentStep, gHBParamMgr.ubCurACRR, gHBParamMgr.ubPageType, puwFPU1[1 + ubPrefixOffset], puwFPU1[2 + ubPrefixOffset], puwFPU1[3 + ubPrefixOffset], puwFPU1[7 + ubPrefixOffset]);
		M_UART(ERS_INFO_, "HB Step %x, Bin After %x \n", pParam->ubCurrentStep, gHBParamMgr.ubCurACRR);
	}
	return uwFPUPtr;
}


U16 HBRetryPreconditionGetFeatureFPU(void)
{
	U16 uwFPUPtr = 0;
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
	return uwFPUPtr;
}

U16 HBRetryPreconditionCheckFeatureFPU(void)
{
	FPL_HBIT_RETRY_PARAM_STRUCT_PTR pParam = gHBParamMgr.pParam;
	U8 ubCmpFeatureData[PARAMETER_NUM_PER_FPU] = {0};
	U8 ubi;
	U16 uwFPUPtr;
	U16 *puwFPU;

	// Get FPU PTR
	uwFPUPtr = FPU_PTR_OFFSET(fpu_read_and_compare_feature_data);
	puwFPU = (U16 *)(IRAM_BASE + uwFPUPtr);
	if (HB_RETRY_MICRON_ALL_NUM > pParam->ubCurrentStep) {
		ubCmpFeatureData[0] = pParam->pubRetryTable[pParam->ubCurrentStep];
	}
	else {
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
	}
	for (ubi = 0; ubi < pParam->ubParameterNumPerFPU; ubi++) {
		puwFPU[2 + (ubi << 1)] = FPU_DAT_R_CMP(ubCmpFeatureData[ubi]);
		puwFPU[3 + (ubi << 1)] = (0 == ubi) ? FPU_DAT_R_MASK(0xFF) : FPU_DAT_R_MASK(0); //Just compare P1 value
	}
	// FPU end, already fill FPU[3] in init state
	//puwFPU[3] = FPU_END;

	return uwFPUPtr;
}

U16 HBRetryPostconditionSetFeatureFPU(void)
{
	U8 ubi;
	U8 ubSetFeatureData[PARAMETER_NUM_PER_FPU] = {0};
	U16 uwFPUPtr;
	U16 *puwFPU;
	U8 ubReadOffsetFeatureAddr = 0;
	U8 ubReadOffsetMTToDoCnt = 0;
	U8 ubNeedSetWLStatusBypass = 0;
	FPL_HBIT_RETRY_PARAM_STRUCT_PTR pParam = gHBParamMgr.pParam;
	// Get FPU PTR
	uwFPUPtr = FPU_PTR_OFFSET(fpu_set_feature);
	puwFPU = (U16 *)(IRAM_BASE + uwFPUPtr);

	// Set feature cmd, already fill FPU[0] in init state
	//puwFPU[0] = FPU_CMD(0xD5);

	// 1st feature address (LUN address)
	puwFPU[1] = FPU_ADR_1B(gHBParamMgr.ubDie);

	// 2nd feature address (1st MT use 0x89, 2nd MT use 0x96)

	if (pParam->ubCurrentStep >= pParam->ubTotalRetryCnt) {
		pParam->ubCurrentStep = pParam->ubTotalRetryCnt - 1;
	}
	if (gHBTask.MTTemplate.dma.ulUserDefineReadDma.btWLStatusBypass) {
		ubNeedSetWLStatusBypass = 1;
	}
	gHBParamMgr.ubSetFeatureToDoMTCnt = ubReadOffsetMTToDoCnt + ubNeedSetWLStatusBypass; //read offset & retry
	if ((ubReadOffsetMTToDoCnt) > gHBParamMgr.ubSetFeatureDoneMTCnt) {
		//read offset set to deafult
		puwFPU[2] = FPU_ADR_1B(ubReadOffsetFeatureAddr);
	}
	else {
		if ( (gHBTask.MTTemplate.dma.ulUserDefineReadDma.btWLStatusBypass) && (gHBParamMgr.ubSetFeatureDoneMTCnt == (gHBParamMgr.ubSetFeatureToDoMTCnt - 1))) {
			//WLSB
			puwFPU[2] = FPU_ADR_1B(0xDF);
		}
		else {
			if (HBIT_RETRY_MICRON_ARC_STEP > pParam->ubCurrentStep) {
				puwFPU[2] = FPU_ADR_1B(0xA8);//LPI
			}
			else {
				//Auto Read Calibration
				puwFPU[2] = FPU_ADR_1B(0x96);//ARC or ARC+persistence
			}
		}
	}
	M_FW_ASSERT(ASSERT_RETRY_0x0D65, RETRY_HB_MICRON_RECORD_FEATURE_NUM >= gHBParamMgr.ubSetFeatureToDoMTCnt);
	gubRetrySetFeatureAddr[gHBParamMgr.ubSetFeatureDoneMTCnt] = puwFPU[2] & 0xFF;
	gubRetrySetFeatureData[gHBParamMgr.ubSetFeatureDoneMTCnt] = ubSetFeatureData[0];
	// Delay
	if (FIP_FLASH_CLOCK_400MHZ == gFlhEnv.ubTargetFlashClock) {
		// flh clk 400
		puwFPU[3] = FPU_DLY(0x15);		// Decimal 21
	}
	else if (FIP_FLASH_CLOCK_333MHZ == gFlhEnv.ubTargetFlashClock) {
		// flh clk 333
		puwFPU[3] = FPU_DLY(0xF);		// Decimal 15
	}
	else {
		puwFPU[3] = FPU_DLY(0x10);		// Decimal 16
	}

	// Set feature data
	for (ubi = 0; ubi < PARAMETER_NUM_PER_FPU; ubi++) {
		if (0 == ubi) { //first
			puwFPU[4 + (ubi << 1)] = FPU_DAT_W_FIR_D(ubSetFeatureData[ubi]);
		}
		else if ((PARAMETER_NUM_PER_FPU - 1) == ubi) { //last
			puwFPU[4 + (ubi << 1)] = FPU_DAT_W_LAST_D(ubSetFeatureData[ubi]);
		}
		else {
			puwFPU[4 + (ubi << 1)] = FPU_DAT_W_MID_D(ubSetFeatureData[ubi]);
		}
		puwFPU[4 + (ubi << 1) + 1] = FPU_DAT_W(ubSetFeatureData[ubi]);
	}
	// FPU end, already fill FPU[7] in init state
	//puwFPU[7] = FPU_END;

	return uwFPUPtr;
}

U16 HBRetryPostconditionCheckFeatureFPU(void)
{
	FPL_HBIT_RETRY_PARAM_STRUCT_PTR pParam = gHBParamMgr.pParam;
	U8 ubCmpFeatureData[PARAMETER_NUM_PER_FPU] = {0};
	U8 ubi;
	U16 uwFPUPtr;
	U16 *puwFPU;

	// Get FPU PTR
	uwFPUPtr = FPU_PTR_OFFSET(fpu_read_and_compare_feature_data);
	puwFPU = (U16 *)(IRAM_BASE + uwFPUPtr);

	// CMD 0x00, already fill FPU[0] in init state
	//puwFPU[0] = FPU_CMD(0x00);

	for (ubi = 0; ubi < pParam->ubParameterNumPerFPU; ubi++) {
		puwFPU[2 + (ubi << 1)] = FPU_DAT_R_CMP(ubCmpFeatureData[ubi]);
		puwFPU[3 + (ubi << 1)] = ((0 == ubi) || (1 == ubi)) ? FPU_DAT_R_MASK(0xFF) : FPU_DAT_R_MASK(0); // compare P1 & P2 value
	}

	// FPU end, already fill FPU[3] in init state
	//puwFPU[3] = FPU_END;
	return uwFPUPtr;
}

U16 HBRetryClearReadOffsetFPU(void)
{
	U16 uwFPUPtr;
	U16 *puwFPU;
	U8 ubPrefixOffset;

	if (gHBTask.MTTemplate.dma.ALUSelect & ALU_RULE_SLC_BIT) { // BIT1 = SLC
		uwFPUPtr = FPU_PTR_SLC_RETRY_READ_ACRR_CMD(0);
		ubPrefixOffset = 1;
	}
	else {
		uwFPUPtr = FPU_PTR_TLC_RETRY_READ_ACRR_CMD(gHBParamMgr.ubCurACRR);
		ubPrefixOffset = 0;
	}
	puwFPU = (U16 *)(IRAM_BASE + uwFPUPtr);
	puwFPU[1 + ubPrefixOffset] = FPU_ADR_1B(0);
	puwFPU[2 + ubPrefixOffset] = FPU_ADR_1B(0);
	puwFPU[3 + ubPrefixOffset] = FPU_ADR_1B(0);
#if (!PS5017_EN)
	puwFPU[8 + ubPrefixOffset] = FPU_CMD(0x30);
#endif /* (!PS5017_EN) */
	return uwFPUPtr;
}

#endif /* (PS5013_EN && (FLASH_B47R_TLC == FW_CATEGORY_FLASH) && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER)) */

