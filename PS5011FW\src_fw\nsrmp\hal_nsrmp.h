#ifndef _HAL_NSRMP_H_
#define _HAL_NSRMP_H_

#include "hal/apu/apu_reg.h"
#include "common/math_op.h"

#ifdef EXTERN
#undef EXTERN
#endif

#ifdef _HAL_NSRMP_C_
#define EXTERN
#else
#define EXTERN  extern
#endif

#define NS_NUM    (32)
#if (PS5017_EN)
#define PMD_NUM   (1024)
#else /* PS5017_EN */
#define PMD_NUM   (512)
#endif /* PS5017_EN */

// LCA Remap SRAM Register Operation//E17_porting_4TB
#define HAL_APU_SET_NS_OFS_NODE(NSID, BASE)                  (R16_APU[R16_APU_LR_BASE + NSID] = BASE & BITMSK(10,0))
#define HAL_APU_SET_NS_POOL_NODE(ORI_PMD, RMP_PMD)           (R16_APU[R16_APU_LR_SRAM + ORI_PMD] = RMP_PMD & BITMSK(10,0))
#define HAL_APU_GET_NS_POOL_NODE(ORI_PMD)                    (R16_APU[R16_APU_LR_SRAM + ORI_PMD] & BITMSK(10,0))
#define HAL_APU_SET_NS_INVERSE_NODE(RMP_PMD, NSID, ORI_PMD)  (R16_APU[R16_APU_ILR_SRAM + RMP_PMD] = (NSID << 10 | ORI_PMD) & BITMSK(15,0))
#define HAL_APU_GET_NS_POOL_BASE()                           (APU_REG_ADDRESS + R8_APU_LR_SRAM)

EXTERN void ApuLRSramInit(void);
void hal_nsrmp_table_init(void);


#undef EXTERN

#endif /* _HAL_NSRMP_H_ */
