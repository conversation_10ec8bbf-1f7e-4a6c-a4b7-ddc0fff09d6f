#include "host/VUC_handler.h"
#include "vuc/VUC_command.h"
#include "vuc/VUC_api.h"
#include "hal/pic/uart/uart_api.h"
#include "vuc/VUC_FwFeatureControl.h"

#if (HOST_MODE == NVME)
void VUC_FwFeatureControl(VUC_OPT_HCMD_PTR_t pCmd)
{
	U8 ubSubFeature = pCmd->vuc_sqcmd.vendor.FwFeatureControl.ubSubFeature;

	M_UART(VUC_, "\nVUC_FW_FEATURE_CONTROL");

	switch (ubSubFeature) {
	case VUC_UNLOCK_JTAG:
		VUC_UnLockJTAG(pCmd);
		break;
	default:
		pCmd->ubState = CMD_ERROR;
		break;
	}
}
#endif
