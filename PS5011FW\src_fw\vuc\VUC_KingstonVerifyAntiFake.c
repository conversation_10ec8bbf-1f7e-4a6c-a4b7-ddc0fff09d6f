// d
#include "hal/dmac/dmac_pop_cmd.h"
// s
#include "table/sys_block/sys_block_api.h"
// t
#include "common/typedef.h"
// u
#include "hal/pic/uart/uart_api.h"
// v
#include "host/VUC_handler.h"
#include "vuc/VUC_KingstonVerifyAntiFake.h"

U8 VUCKingstonVerifyAntiFakeChecksum(VUC_OPT_HCMD_PTR_t pCmd, U8 *ubDW12, U8 *ubDW13)
{
	U8 ubi;
	U8 ubCheckSum = 0;

	M_UART(VUC_, "\nDW[12] : ");
	for (ubi = 1; ubi < 4; ubi++) {
		ubCheckSum ^= ubDW12[ubi];
		M_UART(VUC_, "%b", ubDW12[ubi]);
	}
	M_UART(VUC_, "\nDW[13] : ");
	for (ubi = 0; ubi < 3; ubi++) {
		ubCheckSum ^= ubDW13[ubi];
		M_UART(VUC_, "%b", ubDW13[ubi]);
	}
	M_UART(VUC_, "\nChecksum : %b\n", ubCheckSum);
	if (ubDW13[3] != ubCheckSum) {
		M_UART(VUC_, "\nKingston VUC check sum fail.\n");

		return FAIL;
	}

	return PASS;
}

void VUCKingstonVerifyAntiFakeWrite(U32 ulVUCWriteBufAddr)
{
	U32 ulKingstonVUCBufAddr = 0;

	ulKingstonVUCBufAddr = M_LB_TO_ADDR(LB_ID_FW, gFWLBMgr.Type[FWLB_VUC_KINGSTON].uwLBOffset);
	memcpy((void *)(ulKingstonVUCBufAddr), (void *)(ulVUCWriteBufAddr), DEF_512B);
}

U8 VUCKingstonVerifyAntiFakeRead(U32 ulVUCReturnBufAddr)
{
	U32 ulRandomDataAddr, ulKingstonInfoAddr, ulSerialNumberAddr, ulKeyAddr;
	U32 ulTempBufAddr, ulVUCAllocateBufAddr, ulLastSLCPageIdx = 0;
	U32 ulKingstonVUCBufAddr = 0;
	SystemAreaBlockStatus_t ubKingstonInfoKeyData;
	Sector_0_Frontend_Common_Config_t *pSector0_Info = NULL;
	U8 ubi;
	U8 ubSerialNumber[20];
	U8 ubSerialNumberCnt = 0;
	U8 ub4kEntryPtr = 0;
	DMACParam_t DMACParameter;

	if (0 == gSystemArea.ubSystemBlockNum) {
		return FAIL;;
	}
	// Release ST3C cache
	BufferAdjustST3CCache_AOM_TABLE(BIT_BUFFER_FLOW_VUC_READ_WRITE, GET_BUFFER);
	// Allocate buffer
	BufferAllocateFWLBPBLink(FWLB_VUC_READ_WRITE_BIT, ALLOCATE_NO_USE_CTAG, ALLOCATE_NO_USE_LCA);
	ulVUCAllocateBufAddr = (U32)M_LB_TO_ADDR(LB_ID_FW, gFWLBMgr.Type[FWLB_VUC_READ_WRITE].uwLBOffset);
	ulRandomDataAddr = ulVUCAllocateBufAddr;
	ulKingstonInfoAddr = ulRandomDataAddr + SIZE_2KB;
	ulSerialNumberAddr = ulKingstonInfoAddr + SIZE_2KB;
	ulKeyAddr = ulSerialNumberAddr + SIZE_2KB;
	ulTempBufAddr = ulKeyAddr + SIZE_2KB;
	M_UART(VUC_, "\nK-VUC Buffer size : %d.\n", gFWLBMgr.Type[FWLB_VUC_READ_WRITE].uwAllocateSize);

	ulKingstonVUCBufAddr = M_LB_TO_ADDR(LB_ID_FW, gFWLBMgr.Type[FWLB_VUC_KINGSTON].uwLBOffset);
	memcpy((void *)(ulRandomDataAddr), (void *)(ulKingstonVUCBufAddr), DEF_512B); // get 512Byte data from VUC_Buf_Address

	ubKingstonInfoKeyData = SysBlockRead4KEntry(ulTempBufAddr, SYS_BLK_SYSTEM_AREA_ERASE_COUNT_PAGE, 1, SYSTEM_AREA_WAIT_COP0_IDLE_AND_LOCK);
	//if read Kingston manufactoring info and Key data fail
	if (FAIL == ubKingstonInfoKeyData.Info.btStatus) {
		gSystemAreaHandle.Info.btReadSystemBlkAllUnc = TRUE;
		//Release Buffer
		BufferFreeFWLBPBLink(FWLB_VUC_READ_WRITE_BIT);
		BufferAdjustST3CCache_AOM_TABLE(BIT_BUFFER_FLOW_VUC_READ_WRITE, RETURN_BUFFER);
		return FAIL;
	}
	memcpy((void *)(ulKingstonInfoAddr), (void *)(ulTempBufAddr), DEF_512B);
	memcpy((void *)(ulKeyAddr), (void *)(ulTempBufAddr + DEF_512B), DEF_KB(1));

	ubKingstonInfoKeyData = SysBlockRead4KEntry(ulTempBufAddr, ulLastSLCPageIdx, ub4kEntryPtr, SYSTEM_AREA_WAIT_COP0_IDLE_AND_LOCK);
	if (FAIL == ubKingstonInfoKeyData.Info.btStatus) {
		gSystemAreaHandle.Info.btReadSystemBlkAllUnc = TRUE;
		//Release Buffer
		BufferFreeFWLBPBLink(FWLB_VUC_READ_WRITE_BIT);
		BufferAdjustST3CCache_AOM_TABLE(BIT_BUFFER_FLOW_VUC_READ_WRITE, RETURN_BUFFER);
		return FAIL;
	}
	else {
		pSector0_Info = (Sector_0_Frontend_Common_Config_t *)ulTempBufAddr;
	}

	for (ubi = 0; ubi < SERIAL_NUM_LENGTH; ubi++) {
		if (pSector0_Info->ubIPB_Snumber[ubi]) {
			ubSerialNumber[ubi] = pSector0_Info->ubIPB_Snumber[ubi];
			ubSerialNumberCnt++;
		}
	}

	if (ubSerialNumberCnt) {
		for (ubi = 0; ubi <= (DEF_512B / ubSerialNumberCnt); ubi++) {
			memcpy((void *)(ulSerialNumberAddr + ubi * ubSerialNumberCnt), (void *)ubSerialNumber, ubSerialNumberCnt);
		}
	}

	DMAC_BitmapXOR(ulKingstonInfoAddr, ulRandomDataAddr, SIZE_IN_32B(DEF_512B), (U32)NULL);
	memcpy((void *)(ulTempBufAddr), (void *)(ulKingstonInfoAddr), DEF_512B);
	DMAC_BitmapXOR(ulSerialNumberAddr, ulRandomDataAddr, SIZE_IN_32B(DEF_512B), (U32)NULL);
	memcpy((void *)(ulTempBufAddr + DEF_512B), (void *)(ulSerialNumberAddr), DEF_512B);
	DMAC_BitmapXOR(ulTempBufAddr, ulKeyAddr, SIZE_IN_32B(DEF_KB(1)), gulDMACDirectWaitDone_Callback);
	gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
	while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
		DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
	}

	DMACParameter.ulSourceAddr = ulTempBufAddr;
	DMACParameter.ulDestAddr = ulVUCReturnBufAddr;
	DMACParameter.ul32ByteNum = SIZE_IN_32B(BC_1KB);
	gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
	DMACCopy(DMAC_MODE_NORMAL_QUEUE, &DMACParameter, gulDMACDirectWaitDone_Callback, 0);
	while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
		DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
	}

	//Release Buffer
	BufferFreeFWLBPBLink(FWLB_VUC_READ_WRITE_BIT);
	BufferAdjustST3CCache_AOM_TABLE(BIT_BUFFER_FLOW_VUC_READ_WRITE, RETURN_BUFFER);

	return PASS;
}

void VUCReleaseKingston4KBuf(void)
{
	BufferFreeFWLBPBLink(FWLB_VUC_KINGSTON_BIT);
	BufferAdjustST3CCache_AOM_TABLE(BIT_BUFFER_FLOW_VUC_KINGSTON, RETURN_BUFFER);
	M_UART(NVME_API_, "\nRelease Kingston Verify Anti-Fake 4KBuf\n");
}
