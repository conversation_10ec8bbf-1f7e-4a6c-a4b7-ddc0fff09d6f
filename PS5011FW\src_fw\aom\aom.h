/****************************************************************************
 *
 *  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
 *  All rights reserved
 *
 *  The content of this document is confidential and shall be applied
 *  subject to the terms and conditions of the license agreement and
 *  other applicable laws. Any unauthorized access, use or disclosure
 *  of this document is strictly prohibited and may be punishable
 *  under laws.
 *
 *  aom.h
 *
 *
 *
 ****************************************************************************/

#ifndef _AOM_H_
#define _AOM_H_

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "setup.h"
#include "symbol.h"
#include "mem.h"

/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */
#define SPI_MODE					(0)
#define ATCM_EXTEND_MODE            (1)
#define CODE_BLOCK_SIM_MODE         (2)
#define REAL_FW_MODE		        (3)
#if ICE_MODE_EN
#define OVERLAY_MODE	            (CODE_BLOCK_SIM_MODE) //CODE_BLOCK_SIM_MODE
#else /* ICE_MODE_EN */
#define OVERLAY_MODE	            (REAL_FW_MODE) //CODE_BLOCK_SIM_MODE
#endif /* ICE_MODE_EN */
#define DBUF_CODE_BANK_ADDRESS	DBUF_PB_RAM_ADDRESS
#define SPI_CODE_BANK_ADDRESS	SPI_RAM_ADDRESS
#define ATCM_EXTEND_BANK_ADDRESS    (ATCM_RAM_ADDRESS + ATCM_RAM_SIZE)

#if OVERLAY_EN
#if PS5017_EN
#if TCG_EN
#define CODE_BANK_COUNT			(23)    // to be modified when extend overlay
#else /* TCG_EN */
#define CODE_BANK_COUNT			(21)
#endif /* TCG_EN */
#else /* PS5017_EN */
#if TCG_EN
#if (BOOTLOADER_EN)
#define CODE_BANK_COUNT			(24)    // to be modified when extend overlay
#else /* (BOOTLOADER_EN) */
#define CODE_BANK_COUNT			(21)
#endif /* (BOOTLOADER_EN) */
#else /* TCG_EN */
#if (BOOTLOADER_EN)
#define CODE_BANK_COUNT			(19)
#else /* (BOOTLOADER_EN) */
#define CODE_BANK_COUNT			(18)
#endif /* (BOOTLOADER_EN) */
#endif /* TCG_EN */
#endif /* PS5017_EN */
#else /* OVERLAY_EN */
#define CODE_BANK_COUNT			(1)
#endif /* OVERLAY_EN */
#define CODE_BANK_SIZE			ATCM_CODE_BANK_SIZE

/*****************
 *  Multi Plane  *
 *****************/
#define AOM_MULTI_PLANE_READ_EN					(TRUE)
// AOM can only do 2 planes multi-plane read despite flash has 4 planes
#define AOM_MULTI_PLANE_SUPPORT_PLANE_NUM		(2)
// +1 for flash cmd
#define AOM_MULTI_PLANE_MT_NUM					(AOM_MULTI_PLANE_SUPPORT_PLANE_NUM + 1)
#define AOM_MULTI_PLANE_MIN_PAGE_IDX			(0)

// Code block index per CH related
#define AOM_CODE_BLK_NUM_PER_CHANNEL			(2)
#define AOM_START_CODE_BLK_IDX					(0)

#if ((FPGA_BOARD == FPGA_USE_V7) || ASIC)
#if PS5021_EN
#define CODE_BANK_SIM_WRITE_BUFFER_ADDRESS      (DBUF_STATIC_BASE_ADDR - (CODE_BANK_COUNT * CODE_BANK_SIZE))
#else /* PS5021_EN */
#define CODE_BANK_SIM_WRITE_BUFFER_ADDRESS      0x34000000 // spi address
#endif /* PS5021_EN */
#else /* ((FPGA_BOARD == FPGA_USE_V7) || ASIC) */
#if PS5017_EN
#define CODE_BANK_SIM_WRITE_BUFFER_ADDRESS      (DBUF_STATIC_BASE_ADDR - (CODE_BANK_COUNT * CODE_BANK_SIZE))
#else /* PS5017_EN */
#define CODE_BANK_SIM_WRITE_BUFFER_ADDRESS      0x28000 //0x22180000  //(0x28000)
#endif /* PS5017_EN */
#endif /* ((FPGA_BOARD == FPGA_USE_V7) || ASIC) */
#define ATCM_CODE_BANK_ADDRESS_FOR_ASIC		(0x00020000)

#define M_GET_DBUF_CODE_BANK_ADDRESS(N)	(DBUF_PB_RAM_ADDRESS + ((N) * CODE_BANK_SIZE))
#define M_GET_SPI_CODE_BANK_ADDRESS(N)	(SPI_RAM_ADDRESS + ((N) * CODE_BANK_SIZE))

// MT related
#define AOM_MT_ALU_SETTING				(2) // SLC = 1, D1 = 0
#define AOM_INVALID_FSA					(0xFFFFFFFF)
#define AOM_MT0					(0)
#define AOM_MT1					(1)
#define AOM_MT2					(2)
#define AOM_WAIT_MT0			(BIT(AOM_MT0))
#define AOM_WAIT_MT1			(BIT(AOM_MT1))
#define AOM_WAIT_MT2			(BIT(AOM_MT2))
#define AOM_WAIT_ALL_MT			(AOM_WAIT_MT0 | AOM_WAIT_MT1 | AOM_WAIT_MT2)
#define AOM_WAIT_FIRST_N_MT(n)	(BIT_MASK((n)))

// Count down loop termination condition
#define AOM_U8_CNT_DOWN_LOOP_TERMINATION	(0xFF)

// CRC related
#define AOM_CRC_SEED		(0xCBCBCBCB)

// Debug info
#define AOM_READ_FAIL_INFO_ERASEPAGE_SHIFT		(1)

// Pick MTQ related
#define CODE_BANKING_INVALID_QUEUE		(0xFF)

/*
 * ---------------------------------------------------------------------------------------------------
 *  enum
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *  data types
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *   macros
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *   extern global variables
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */

#endif /* _AOM_H_ */
