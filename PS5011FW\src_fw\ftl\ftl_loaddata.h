/****************************************************************************
*
*  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
*  All rights reserved
*
*  The content of this document is confidential and shall be applied
*  subject to the terms and conditions of the license agreement and
*  other applicable laws. Any unauthorized access, use or disclosure
*  of this document is strictly prohibited and may be punishable
*  under laws.
*
*  ftl_loaddata.h
*
*
*
****************************************************************************/
#ifndef _FTL_LOADDATA_H_
#define _FTL_LOADDATA_H_

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "typedef.h"
#include "setup.h"
#include "hal/bmu/bmu_api.h"
#include "hal/dmac/dmac_api.h"
#include "hal/cop1/cop1_api.h"

/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */
#define LOAD_DATA_DEBUG_UART_EN	(0)
#define E3D512_WORKAROUND ((PS5017_EN || PS5021_EN) ? FALSE : TRUE)
/*
 * ---------------------------------------------------------------------------------------------------
 *  union
 * ---------------------------------------------------------------------------------------------------
 */
typedef union {
	U8 ubAll;
	struct {
		U8 ZIPDoingCnt : 7;
		U8 btAdjustBMUWLBUpperLimit : 1;
	};
} FTLLoadDataZIPCnt_t;
/*
 * ---------------------------------------------------------------------------------------------------
 *  enum
 * ---------------------------------------------------------------------------------------------------
 */
enum LoadDataState {
	STATE_LD_INIT = 0,
	STATE_LD_SEARCH_WLB,
	STATE_LD_SEARCH_COP1,
	STATE_LD_FOUND_INVALID_PCA,
	STATE_LD_ALLOCATE_PB,
	STATE_LD_ALLOCATE_PB_LINK,
	STATE_LD_SET_INVALID,
	STATE_LD_SEARCH_WLB_HIT,
	STATE_LD_UNZIP_WLB_SOURCE_AND_COPY_DATA,
	STATE_LD_UNZIP_TEMP_PB_AND_COPY_DATA,
	STATE_LD_COPY_XZIP_DATA,
	STATE_LD_DMAC_COPY,
	STATE_LD_CHECK_HIT_PLB,
	STATE_LD_CHECK_LAST_PCA,
	STATE_LD_READ_FROM_FLA,
	STATE_LD_ZIP_WLB,
	STATE_LD_XZIP_UNLOCK,
	STATE_LD_FREE_PB,
	STATE_LD_MODIFY_WCQ,
	STATE_LD_FINISH,

	//wait ip CQ state
	STATE_LD_WAIT_IP_CQ,
	STATE_LD_WAIT_SEARCH_WLB,
	STATE_LD_WAIT_SEARCH_COP1,
	STATE_LD_WAIT_DMAC_SET_ZERO,
	STATE_LD_WAIT_ALLOCATE_PB,
	STATE_LD_WAIT_ALLOCATE_PB_LINK,
	STATE_LD_WAIT_UNZIP_WLB_SOURCE_AND_COPY_DATA,
	STATE_LD_WAIT_UNZIP_TEMP_PB_AND_COPY_DATA,
	STATE_LD_WAIT_COPY_XZIP_DATA,
	STATE_LD_WAIT_DMAC_COPY,
	STATE_LD_WAIT_PLB_COPY_DATA,
	STATE_LD_WAIT_READ_FROM_FLA,
	STATE_LD_WAIT_ZIP_WLB,
	STATE_LD_WAIT_BMU_EVENT,
} ;

enum LD_RST {
	RST_LD_DOING = 0,
	RST_LD_FINISH,
};
/*
 * ---------------------------------------------------------------------------------------------------
 *  extern global variables
 * ---------------------------------------------------------------------------------------------------
 */
extern FTLLoadDataZIPCnt_t gFTLZipDoingCnt;
/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */
void LoadDataCop0LoadDataDone_Callback(TIEOUT_FORMAT_t uoResult);
#if (PS5017_EN)
U8 FTLRQLoadDataHandler(U8 ubQueueId, U8 ubQueueIndex);
#else /* (PS5017_EN) */
AOM_EXTEND_LCA U8 FTLRQLoadDataHandler(U8 ubQueueId, U8 ubQueueIndex);
#endif /* (PS5017_EN) */
AOM_PRE_READ_2 U8 FTLRQCheckStop(U8 ubQueueId, U8 ubQueueIndex);
#endif /* _FTL_LOADDATA_H_ */
