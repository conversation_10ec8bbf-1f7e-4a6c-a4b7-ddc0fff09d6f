#include <retry/retry_sandisk_bics6_qlc/retry_sandisk_bics6_qlc_E13_U17_neutral_hb.h>
#include "retry/retry.h"
#include "retry/retry_api.h"
#include "retry/retry_hb.h"
#include "hal/pic/uart/uart_api.h"
#include "retry/retry_version_api.h" // Retry Version
#include "table/initinfo_vt/initinfo_vt.h" // VT
#include "ftl/ftl_api.h" // FW Timer
#include "hal/fip/fpu.h"
#include "table/sys_block/sys_block_api.h" //RetryInitSystemBlockReadRetryTable

#if (((PS5013_EN) || (PS5017_EN)) && (FW_CATEGORY_FLASH == FLASH_SANDISK_BICS6_QLC)  && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER))
/*
 * ----------------------------------------------------------------------------
 *   Definitions
 * ----------------------------------------------------------------------------
 */

#define RETRY_HYNIX_V6_SLC_RRT_BASE		(DBUF_RETRY_RR_TABLE)
#define RETRY_HYNIX_V6_TLC_RRT_BASE		(DBUF_RETRY_RR_TABLE + DEF_KB(2))

#define OTP_RR_CNT_SITE                 (8)
#define OTP_RR_REG_CNT_SITE             (16)

#define OTP_DEFAULT_RR_CNT              (50)
#define OTP_DEFAULT_RR_REG_CNT_SLC      (HBIT_RETRY_SLC_FEA_DATA_NUM)
#define OTP_DEFAULT_RR_REG_CNT_TLC      (HBIT_RETRY_TLC_FEA_DATA_NUM)

#define OTP_DEFAULT_SET_NUM             (8)
#define OTP_NORMAL_SEQ                  (0)
#define OTP_INVERSE_SEQ                 (1)
#define OTP_SEQ_CNT_PER_SET             (2)

/*
 * ----------------------------------------------------------------------------
 *   Enum
 * ----------------------------------------------------------------------------
 */

/*
 * ----------------------------------------------------------------------------
 *   Data Types
 * ----------------------------------------------------------------------------
 */

typedef struct {
	U8 ubSeq[OTP_DEFAULT_SET_NUM][OTP_SEQ_CNT_PER_SET][OTP_DEFAULT_RR_CNT * OTP_DEFAULT_RR_REG_CNT_TLC];
} OTPSeq_t;
/*
 * ----------------------------------------------------------------------------
 *   Macros
 * ----------------------------------------------------------------------------
 */

/*
 * ----------------------------------------------------------------------------
 *   Extern Global Variables
 * ----------------------------------------------------------------------------
 */
extern U8 gubHynixtADLDelay;
extern U8 gubHynixtWHRDelay;
/*
 *	Sandisk Bics6 QLC
 *  9Bh: U, 9Ch: T, 9Dh: L, 9Eh: M
 *  89h: L, 89h: M, 89h: M, 89h: M, 8Ah: U, 8Ah: U, 8Ah: U
 */
const U8 gubRetryReadRetryTableParameterIdxToPageType[HBIT_RETRY_SELECT_NON_SLC_FEA_DATA_NUM] = {
	HB_LRU_LOWER, HB_LRU_LOWER, HB_LRU_LOWER, 00,
	HB_LRU_MIDDLE, HB_LRU_MIDDLE, HB_LRU_MIDDLE, HB_LRU_MIDDLE,
	HB_LRU_UPPER, HB_LRU_UPPER, HB_LRU_UPPER, HB_LRU_UPPER,
	HB_LRU_TOP, HB_LRU_TOP, HB_LRU_TOP, HB_LRU_TOP
};
/*
 *	Sandisk Bics6 QLC
 *  Transition 0 to 14
 *  P0 | P1 | P2 | P3 | P4 | P5 | P6 | P7 | P8 | P9 | P10 | P11 | P12 | P13 | P14| P15
 *  LOW| LOW| LOW| 00 |MID | MID| MID| MID| UP | UP | UP  | UP  | TOP | TOP | TOP| TOP
 */
const U8 gubRetryTransitionToPageType[HBIT_RETRY_SELECT_NON_SLC_FEA_DATA_NUM] = {
	LOWER_PAGE_SEL, LOWER_PAGE_SEL, LOWER_PAGE_SEL, 00,
	MIDDLE_PAGE_SEL, MIDDLE_PAGE_SEL, MIDDLE_PAGE_SEL, MIDDLE_PAGE_SEL,
	UPPER_PAGE_SEL, UPPER_PAGE_SEL, UPPER_PAGE_SEL, UPPER_PAGE_SEL,
	TOP_PAGE_SEL, TOP_PAGE_SEL, TOP_PAGE_SEL, TOP_PAGE_SEL
};

/*
 * ----------------------------------------------------------------------------
 *   Static Global Variables
 * ----------------------------------------------------------------------------
 */

/*
 * ----------------------------------------------------------------------------
 *   Private Prototypes
 * ----------------------------------------------------------------------------
 */
INLINE void HBRetryInitRegisterRetryTable(void);
INLINE void HBRetryInitAdjustVthFPU(void);
//AOM_RETRY_LOAD_TABLE NO_INLINE void HBRetryInitTable(void);
INLINE U16 SandiskBics6QLCHBRetrySetFeatureFPU(U8 ubStep, U8 ubPageType, U8 ubSLCMode);
INLINE U16 SandiskBics6QLCHBRetryCheckFeatureFPU(U8 ubStep, U8 ubPageType, U8 ubSLCMode);
/*
 * ----------------------------------------------------------------------------
 *   Private
 * ----------------------------------------------------------------------------
 */

void HBRetryInitRegisterRetryTable(void)
{
	gpHBParameterArray[FPL_ADDR_TLC_RULE].pubRetryTable = (gpIDPage->ubRetryGroupCount ? ((U8 *)(SANDISK_TLC_RRT_BASE)) : ((U8 *)(&gubHbitRetryData)));			// QLC
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubTotalRetryCnt = (gpIDPage->ubRetryGroupCount ? (gpIDPage->ubRetryGroupCount + 1) : HBIT_RETRY_SANDISK_BICS6_QLC_1024G_STEP_NUM);
	gpHBParameterArray[FPL_ADDR_SLC_RULE].pubRetryTable = (gpIDPage->ubRetryGroupCount ? ((U8 *)(SANDISK_SLC_RRT_BASE)) : ((U8 *)(&gubSlcHbitRetryData)));		// SLC
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubTotalRetryCnt = (gpIDPage->ubD1RetryGroupCount ? (gpIDPage->ubD1RetryGroupCount + 1) : HBIT_RETRY_SANDISK_BICS6_SLC_1024G_STEP_NUM);
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubCurrentStep = HB_READ_START_STEP;
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubLastSuccessIndex = 0;
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubRetryParameterSize = (gpIDPage->ubRetryParamCount ? gpIDPage->ubRetryParamCount : HBIT_RETRY_QLC_FEA_DATA_NUM);
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubParameterNumPerFPU = (gpIDPage->ubRetryParamCount ? gpIDPage->ubRetryParamCount : HBIT_RETRY_QLC_FEA_DATA_NUM);
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubCurrentStep = HB_READ_START_STEP;
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubLastSuccessIndex = 0;
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubRetryParameterSize = (gpIDPage->ubD1RetryParamCount ? gpIDPage->ubD1RetryParamCount : HBIT_RETRY_SLC_FEA_DATA_NUM);
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubParameterNumPerFPU = (gpIDPage->ubD1RetryParamCount ? gpIDPage->ubD1RetryParamCount : HBIT_RETRY_SLC_FEA_DATA_NUM);
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubScratchTableIdx = INVALID_RETRY_TABLE_INDEX;
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubScratchTableIdx = INVALID_RETRY_TABLE_INDEX;
}

void HBRetryInitAdjustVthFPU(void)
{
	// Register set feature fpu
	gFpuEntryList.fpu_set_feature[0] = FPU_ADR_GEN;
	gFpuEntryList.fpu_set_feature[1] = FPU_CMD(0x78);
	gFpuEntryList.fpu_set_feature[2] = FPU_ADR(3);
	gFpuEntryList.fpu_set_feature[3] = FPU_DLY(0x10);
	gFpuEntryList.fpu_set_feature[4] = FPU_CMD(0xEF);//BICS6 qlc Set Feature CMD

	gFpuEntryList.fpu_set_feature[27] = FPU_END;

	// Register read and compare feature data fpu
	//	gFpuEntryList.fpu_read_and_compare_feature_data[0] = FPU_ADR_GEN;
	//	gFpuEntryList.fpu_read_and_compare_feature_data[1] = FPU_CMD(0x78);
	//	gFpuEntryList.fpu_read_and_compare_feature_data[2] = FPU_ADR(3);
	//	gFpuEntryList.fpu_read_and_compare_feature_data[3] = FPU_DLY(0x10);
	//	gFpuEntryList.fpu_read_and_compare_feature_data[4] = FPU_CMD(0xEE); //BICS6 qlc  Get feature cmd
	//	gFpuEntryList.fpu_read_and_compare_feature_data[5] = FPU_DLY(0x10);
	gFpuEntryList.fpu_read_and_compare_feature_data[0] = FPU_CMD(0xEE); //BICS6 qlc  Get feature cmd
	gFpuEntryList.fpu_read_and_compare_feature_data[1] = FPU_DLY(0x10);
}

U16 SandiskBics6QLCHBRetrySetFeatureFPU(U8 ubStep, U8 ubPageType, U8 ubSLCMode)
{
	FPL_HBIT_RETRY_PARAM_STRUCT_PTR pParam = &(gpHBParameterArray[ubSLCMode]);
	U8 ubValidParameterSize = pParam->ubParameterNumPerFPU;
	U16 uwFPUPtr;
	U16 *puwFPU;
	U8 ubFeatureData[HBIT_RETRY_QLC_FEA_DATA_NUM] = {0};
	U8 ubFPUIdx;
	//// PIO DBG  ////
	//	REG32 *pFlaReg0 = R32_FCTL_CH[0];
	//	FlaCEControl(0, 0, ENABLE);
	//	pFlaReg0[R32_FCTL_PIO_CMD] = 0xCD;
	//	FlaCEControl(0, 0, DISABLE);
	//// PIO DBG  ////

	if (ubStep < pParam->ubTotalRetryCnt) { //Prevent Access Array Over flow
		U16 uwTableOffset = ubStep * pParam->ubRetryParameterSize;
		// Copy set feature from HB retry table
		memcpy((void *)ubFeatureData, (void *)&pParam->pubRetryTable[uwTableOffset], ubValidParameterSize);
#if 0
		UartPrintf("\n");
		if (ubSLCMode) {
			UartPrintf("\n Step:%u, TableOffset:%u, ubSLCMode:%u", ubStep, uwTableOffset, ubSLCMode);
		}
		else {
			UartPrintf("\n Step:%u, TableOffset:%u, PageType:%u", ubStep, uwTableOffset, ubPageType);

		}
		UartPrintf("\n Group %u:", ubStep);
		for (U8 i = 0; i < ubValidParameterSize; i++) {
			/* code */
			UartPrintf("%x ", ubFeatureData[i]);

		}
#endif
	}
#if (RETRY_HB_FEEDBACK_CACULATE_READ_LEVEL_TO_LRU)
	// Check if CurrentStep is "SB Pass Read Level", ubTotalRetryCnt = RR_Table + Scratch_Table
	else {
		U8 ubReadLevelTableIdx = ubStep - gpHBParameterArray[FPL_ADDR_TLC_RULE].ubTotalRetryCnt; // Get Node Idx
		RETRY_HB_LRU_FEEDBACK_READ_LEVEL_TABLE_STRUCT_PTR pFeedBackReadLevelTable = M_RETRY_GET_FEEDBACK_CACULATE_READ_LEVEL_TABLE(gHBParamMgr.ubSLCMode, gHBParamMgr.ubGlobalDie, gHBParamMgr.ubPageType);
		U8 ubValidIdx = 0;
		for (U8 ubi = 0; ubi < ubValidParameterSize; ubi++) {
			if (ubPageType == M_RETRY_GET_PAGE_TYPE_BY_READ_RETRY_TABLE_PARAMETER_IDX(ubi)) {
				ubFeatureData[ubi] = pFeedBackReadLevelTable[ubReadLevelTableIdx].ubSBPassReadLevelTable[ubValidIdx]; // Same Page Type Read Level Can't Cross Two Round(89/8A).
				++ubValidIdx;
			}
		}
	}
#endif /* (RETRY_HB_FEEDBACK_CACULATE_READ_LEVEL_TO_LRU) */


	// Get FPU PTR
	uwFPUPtr = FPU_PTR_OFFSET(fpu_set_feature);
	puwFPU = (U16 *)(IRAM_BASE + uwFPUPtr);

	// Set parameter cmd, already fill FPU[0] in init state
	//puwFPU[0] = FPU_CMD(0x36);
	// reset fpu entry
	//	puwFPU[0] = FPU_ADR_GEN;
	//	puwFPU[1] = FPU_CMD(0x78);
	//	puwFPU[2] = FPU_ADR(3);
	//	puwFPU[3] = FPU_DLY(0x10);
	//	puwFPU[4] = FPU_CMD(0xEF); // Sandisk set Vth by setting feature

	puwFPU += 5;
	//UartPrintf("\n\r set ubFeatureData[0] = %x", ubFeatureData[0]);
	ubFPUIdx = 0;
	if (ubSLCMode) {
		//BICS6 SLC: 14h or 8Bh
		puwFPU[ubFPUIdx++] = FPU_ADR_1B(0x8B);
		// Delay (tADL)
		puwFPU[ubFPUIdx++] = FPU_DLY(gubHynixtADLDelay);
		// Set feature data
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(ubFeatureData[0]);
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(0x00);
	}
	else if (ubPageType == HB_LRU_LOWER) {
		//BICS6 QLC: 9Bh for upper page, 9Ch for top page, 9Dh for lower page, 9Eh for middle page
		puwFPU[ubFPUIdx++] = FPU_ADR_1B(0x9D);
		// Delay (tADL)
		puwFPU[ubFPUIdx++] = FPU_DLY(gubHynixtADLDelay);
		// Set feature data
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(ubFeatureData[0]);

		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(ubFeatureData[1]);
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(ubFeatureData[2]);
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(ubFeatureData[3]);
	}
	else if (ubPageType == HB_LRU_MIDDLE) {
		//BICS6 QLC: 9Bh for upper page, 9Ch for top page, 9Dh for lower page, 9Eh for middle page
		puwFPU[ubFPUIdx++] = FPU_ADR_1B(0x9E);
		// Delay (tADL)
		puwFPU[ubFPUIdx++] = FPU_DLY(gubHynixtADLDelay);
		// Set feature data
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(ubFeatureData[4]);
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(ubFeatureData[5]);
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(ubFeatureData[6]);
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(ubFeatureData[7]);
	}
	else if (ubPageType == HB_LRU_UPPER) {
		//BICS6 QLC: 9Bh for upper page, 9Ch for top page, 9Dh for lower page, 9Eh for middle page
		puwFPU[ubFPUIdx++] = FPU_ADR_1B(0x9B);
		// Delay (tADL)
		puwFPU[ubFPUIdx++] = FPU_DLY(gubHynixtADLDelay);
		// Set feature data
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(ubFeatureData[8]);
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(ubFeatureData[9]);
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(ubFeatureData[10]);
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(ubFeatureData[11]);
	}
	else if (ubPageType == HB_LRU_TOP) {
		//BICS6 QLC: 9Bh for upper page, 9Ch for top page, 9Dh for lower page, 9Eh for middle page
		puwFPU[ubFPUIdx++] = FPU_ADR_1B(0x9C);
		// Delay (tADL)
		puwFPU[ubFPUIdx++] = FPU_DLY(gubHynixtADLDelay);
		// Set feature data
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(ubFeatureData[12]);
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(ubFeatureData[13]);
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(ubFeatureData[14]);
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(ubFeatureData[15]);
	}

	// FPU end
	puwFPU[ubFPUIdx++] = FPU_END;
	return uwFPUPtr;
}


U16 SandiskBics6QLCHBRetryCheckFeatureFPU(U8 ubStep, U8 ubPageType, U8 ubSLCMode)
{
	FPL_HBIT_RETRY_PARAM_STRUCT_PTR pParam =  &(gpHBParameterArray[ubSLCMode]);
	U8 ubValidParameterSize = pParam->ubParameterNumPerFPU;
	U16 uwFPUPtr;
	U16 *puwFPU;
	U8 ubFeatureData[HBIT_RETRY_QLC_FEA_DATA_NUM] = {0};
	U8 ubFPUIdx;
	//// PIO DBG  ////
	//	REG32 *pFlaReg1 = R32_FCTL_CH[0];
	//	FlaCEControl(0, 0, ENABLE);
	//	pFlaReg1[R32_FCTL_PIO_CMD] = 0xCE;
	//	FlaCEControl(0, 0, DISABLE);
	//// PIO DBG  ////

	if (ubStep < pParam->ubTotalRetryCnt) { //Prevent Access Array Over flow
		U16 uwTableOffset = ubStep * pParam->ubRetryParameterSize;
		// Copy set feature from HB retry table
		memcpy((void *)ubFeatureData, (void *)&pParam->pubRetryTable[uwTableOffset], ubValidParameterSize);
	}
#if (RETRY_HB_FEEDBACK_CACULATE_READ_LEVEL_TO_LRU)
	// Check if CurrentStep is "SB Pass Read Level", ubTotalRetryCnt = RR_Table + Scratch_Table
	else {
		U8 ubReadLevelTableIdx = ubStep - gpHBParameterArray[FPL_ADDR_TLC_RULE].ubTotalRetryCnt; // Get Node Idx
		RETRY_HB_LRU_FEEDBACK_READ_LEVEL_TABLE_STRUCT_PTR pFeedBackReadLevelTable = M_RETRY_GET_FEEDBACK_CACULATE_READ_LEVEL_TABLE(gHBParamMgr.ubSLCMode, gHBParamMgr.ubGlobalDie, gHBParamMgr.ubPageType);
		U8 ubValidIdx = 0;
		for (U8 ubi = 0; ubi < ubValidParameterSize; ubi++) {
			if (ubPageType == M_RETRY_GET_PAGE_TYPE_BY_READ_RETRY_TABLE_PARAMETER_IDX(ubi)) {
				ubFeatureData[ubi] = pFeedBackReadLevelTable[ubReadLevelTableIdx].ubSBPassReadLevelTable[ubValidIdx]; // Same Page Type Read Level Can't Cross Two Round(89/8A).
				++ubValidIdx;
			}
		}
	}
#endif /* (RETRY_HB_FEEDBACK_CACULATE_READ_LEVEL_TO_LRU) */

	// Get FPU PTR
	uwFPUPtr = FPU_PTR_OFFSET(fpu_read_and_compare_feature_data);
	puwFPU = (U16 *)(IRAM_BASE + uwFPUPtr);

	// Get parameter cmd, already fill FPU[0] in init state
	//puwFPU[0] = FPU_CMD(0x37);
	// reset fpu entry
	//	puwFPU[0] = FPU_ADR_GEN;
	//	puwFPU[1] = FPU_CMD(0x78);
	//	puwFPU[2] = FPU_ADR(3);
	//	puwFPU[3] = FPU_DLY(0x10);
	//	puwFPU[4] = FPU_CMD(0xEE);
	//	puwFPU[5] = FPU_DLY(0x10);
	puwFPU += 2;

	if ((uwFPUPtr & 0x3) == 0x2) {
		// For FPU_DAT_R_CMP, make the FPU sequence align 4B
		puwFPU++;
	}
	//UartPrintf("\n\r get ubFeatureData[0] = %x", ubFeatureData[0]);

	ubFPUIdx = 0;
	if (ubSLCMode) {
		//BICS6 SLC: 14h or 8Bh
		puwFPU[ubFPUIdx++] = FPU_ADR_1B(0x8B);
		// Delay
		puwFPU[ubFPUIdx++] = FPU_CMD_DQS(0X70);
		puwFPU[ubFPUIdx++] = FPU_DLY(0X40);
		puwFPU[ubFPUIdx++] = FPU_POL_MASK(0xE0);
		puwFPU[ubFPUIdx++] = FPU_CMD(0x00);
		puwFPU[ubFPUIdx++] = FPU_DLY(gubHynixtADLDelay);
		//puwFPU[ubFPUIdx++] = FPU_CMD(0x70);
		//puwFPU[ubFPUIdx++] = FPU_POL_MASK(0xE0);
		//puwFPU[ubFPUIdx++] = FPU_DLY(0xFF);
		// Get feature data
		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(ubFeatureData[0]);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);
	}
	else if (ubPageType == HB_LRU_LOWER) {
		//BICS6 QLC: 9Bh for upper page, 9Ch for top page, 9Dh for lower page, 9Eh for middle page
		puwFPU[ubFPUIdx++] = FPU_ADR_1B(0x9D);
		// Delay
		puwFPU[ubFPUIdx++] = FPU_CMD_DQS(0X70);
		puwFPU[ubFPUIdx++] = FPU_DLY(0X40);
		puwFPU[ubFPUIdx++] = FPU_POL_MASK(0xE0);
		puwFPU[ubFPUIdx++] = FPU_CMD(0x00);
		puwFPU[ubFPUIdx++] = FPU_DLY(gubHynixtADLDelay);
		//puwFPU[ubFPUIdx++] = FPU_DLY(0xFF);
		// Get feature data
		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(ubFeatureData[0]);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(ubFeatureData[1]);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(ubFeatureData[2]);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(ubFeatureData[3]);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);
	}
	else if (ubPageType == HB_LRU_MIDDLE) {
		//BICS6 QLC: 9Bh for upper page, 9Ch for top page, 9Dh for lower page, 9Eh for middle page
		puwFPU[ubFPUIdx++] = FPU_ADR_1B(0x9E);
		// FPU Delay
		puwFPU[ubFPUIdx++] = FPU_CMD_DQS(0X70);
		puwFPU[ubFPUIdx++] = FPU_DLY(0X40);
		puwFPU[ubFPUIdx++] = FPU_POL_MASK(0xE0);
		puwFPU[ubFPUIdx++] = FPU_CMD(0x00);
		puwFPU[ubFPUIdx++] = FPU_DLY(gubHynixtADLDelay);
		// Get feature data
		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(ubFeatureData[4]);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(ubFeatureData[5]);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(ubFeatureData[6]);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(ubFeatureData[7]);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);
	}
	else if (ubPageType == HB_LRU_UPPER) {
		//BICS6 QLC: 9Bh for upper page, 9Ch for top page, 9Dh for lower page, 9Eh for middle page
		puwFPU[ubFPUIdx++] = FPU_ADR_1B(0x9B);
		// Delay
		puwFPU[ubFPUIdx++] = FPU_CMD_DQS(0X70);
		puwFPU[ubFPUIdx++] = FPU_DLY(0X40);
		puwFPU[ubFPUIdx++] = FPU_POL_MASK(0xE0);
		puwFPU[ubFPUIdx++] = FPU_CMD(0x00);
		puwFPU[ubFPUIdx++] = FPU_DLY(gubHynixtADLDelay);
		// Get feature data
		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(ubFeatureData[8]);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(ubFeatureData[9]);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(ubFeatureData[10]);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(ubFeatureData[11]);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);
	}
	else if (ubPageType == HB_LRU_TOP) {
		//BICS6 QLC: 9Bh for upper page, 9Ch for top page, 9Dh for lower page, 9Eh for middle page
		puwFPU[ubFPUIdx++] = FPU_ADR_1B(0x9C);
		// Delay
		puwFPU[ubFPUIdx++] = FPU_CMD_DQS(0X70);
		puwFPU[ubFPUIdx++] = FPU_DLY(0X40);
		puwFPU[ubFPUIdx++] = FPU_POL_MASK(0xE0);
		puwFPU[ubFPUIdx++] = FPU_CMD(0x00);
		puwFPU[ubFPUIdx++] = FPU_DLY(gubHynixtADLDelay);
		// Get feature data
		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(ubFeatureData[12]);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(ubFeatureData[13]);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(ubFeatureData[14]);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(ubFeatureData[15]);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);
	}
	// FPU end
	puwFPU[ubFPUIdx++] = FPU_END;
#if(0)
	ubFPUIdx = 0;
	//UartPrintf("\n Set feature FPU Sequence: ");
	while (puwFPU[ubFPUIdx] != NULL) {
		/* code */
		//UartPrintf("%x ", puwFPU[ubFPUIdx++]);

	}
#endif

	return uwFPUPtr;
}

/*
 * ------------------------------------
 * 		Over Wirte Weak Function
 * ------------------------------------
 */
void HBRetryInitParameter(void)
{
	//UartPrintf("\r\n[HB MakerCode %x, Process Type%x]", gpOtherInfo->ubMakerCode, gpOtherInfo->ubProcess);
	M_FW_ASSERT(ASSERT_RETRY_0x0871, (ID_SANDISK == gpOtherInfo->ubMakerCode) && ((RETRY_SANDISK_FLASH_PROCESS_BICS5_512G == gpOtherInfo->ubProcess) || (RETRY_SANDISK_FLASH_PROCESS_BICS5_1024G == gpOtherInfo->ubProcess)));
	HBRetryInitRegisterRetryTable();
	HBRetryInitAdjustVthFPU();
	if (gpIDPage->ubRetryGroupCount) {
		//Do not do HBRetryInitTable();
	}
	else {
		//HBRetryInitTable();
		M_FW_ASSERT(ASSERT_RETRY_0x0870, FALSE);
	}
}


void HBRetryPreconditionSetFeaturePass_Callback(U8 ubMTIdx)
{
	gHBParamMgr.ubSetFeatureDoneMTCnt++;

	if (gHBParamMgr.ubSetFeatureDoneMTCnt == gHBParamMgr.ubSetFeatureToDoMTCnt) { // All set feature MT done
		gHBParamMgr.ubGetFeatureDoneMTCnt = 0;
		if (NCS_V2_EN && gSystmAreaFWSettingFromInfoBlk.Init.Flag.btNCS2En) {
			gHBTask.ubState = RETRY_HB_STATE_SEARCH_PASS_STEP_READ;
		}
		else {
			gHBTask.ubState = RETRY_HB_STATE_CHECK_PRECONDITION; // Check set feature result
		}
	}
	else {
		gHBTask.ubState = RETRY_HB_STATE_PRECONDITION; // Execute remain set feature MT
	}
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_HB_RETRY;
}

void HBRetryPostconditionSetFeaturePass_Callback(U8 ubMTIdx)
{
	gHBParamMgr.ubSetFeatureDoneMTCnt++;

	if (gHBParamMgr.ubSetFeatureDoneMTCnt == gHBParamMgr.ubSetFeatureToDoMTCnt) {
		gHBParamMgr.ubGetFeatureDoneMTCnt = 0;
		if (NCS_V2_EN && gSystmAreaFWSettingFromInfoBlk.Init.Flag.btNCS2En) {
			gHBTask.ubState = RETRY_HB_STATE_POSTCONDITION_DONE_RESET_FLASH; // Check set feature result
		}
		else {
			gHBTask.ubState = RETRY_HB_STATE_CHECK_POSTCONDITION; // Check set feature result
		}
	}
	else {
		gHBTask.ubState = RETRY_HB_STATE_POSTCONDITION;
	}
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_HB_RETRY;
}

U16 HBRetrySelectResetCMDFPU(void)
{
	return FPU_PTR_OFFSET(fpu_entry_reset_ff);
}

U16 HBRetryPreconditionSetFeatureFPU(void)
{
	return SandiskBics6QLCHBRetrySetFeatureFPU(gHBParamMgr.pParam->ubCurrentStep, gHBParamMgr.ubPageType, gHBParamMgr.ubSLCMode);
}

U16 HBRetryPreconditionCheckFeatureFPU(void)
{
	return SandiskBics6QLCHBRetryCheckFeatureFPU(gHBParamMgr.pParam->ubCurrentStep, gHBParamMgr.ubPageType, gHBParamMgr.ubSLCMode);
}

U16 HBRetryPostconditionSetFeatureFPU(void)
{
	HB_LRU_TABLE_STRUCT_PTR pLRUTable = M_RETRY_GET_LRU_TABLE(gHBParamMgr.ubSLCMode, gHBParamMgr.ubGlobalDie, gHBParamMgr.ubPageType);
	return SandiskBics6QLCHBRetrySetFeatureFPU(pLRUTable[0].param.ubTableIdx, gHBParamMgr.ubPageType, gHBParamMgr.ubSLCMode);
}

U16 HBRetryPostconditionCheckFeatureFPU(void)
{
	HB_LRU_TABLE_STRUCT_PTR pLRUTable = M_RETRY_GET_LRU_TABLE(gHBParamMgr.ubSLCMode, gHBParamMgr.ubGlobalDie, gHBParamMgr.ubPageType);
	return SandiskBics6QLCHBRetryCheckFeatureFPU(pLRUTable[0].param.ubTableIdx, gHBParamMgr.ubPageType, gHBParamMgr.ubSLCMode);
}

#endif /* ((PS5013_EN) && (FW_CATEGORY_FLASH == FLASH_SANDISK_BICS6_QLC) && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER)) */
