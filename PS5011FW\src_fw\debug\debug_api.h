/** @file debug_api.h
 *  @brief Debug_Api Header
 *
 *	Define debug_api symbols
 *
 */
#ifndef _DEBUG_API_H_
#define _DEBUG_API_H_
#include "debug/debug_setup.h"
#include "typedef.h"
#include "aom/aom_api.h"
//=============================================================================
// Defined Values and Capabilities
//=============================================================================
//IOR Fip FIFO Entry
#define DEBUG_IOR_FIPFIFO_GROUP_IDX_SHIFT			(16)

//Standby idle time
#define DEBUG_STANDBY_ALL_FLUSH_TIME_THRESHOLD		(1500000) // us
//RAIDECC debug
#define RAIDECC_DEBUG_DECODE_READ_ERROR_PAGE		(FALSE)

//corner case parameter
#define DEBUG_CORNER_TEST_05_PROBABILITY 			(10)
#define DEBUG_CORNER_TEST_14_PROBABILITY 			(10)
#define DEBUG_CORNER_TEST_24_PROBABILITY 			(50)
#define DEBUG_CORNER_TEST_25_PROBABILITY 			(50)
#define DEBUG_CORNER_TEST_26_PROBABILITY 			(10)
#define DEBUG_CORNER_TEST_27_PROBABILITY 			(10)
#define DEBUG_CORNER_TEST_28_PROBABILITY 			(10)
#define DEBUG_CORNER_TEST_29_PROBABILITY 			(10)
#define DEBUG_CORNER_TEST_35_PROBABILITY 			(10)
#define DEBUG_CORNER_TEST_59_PROBABILITY 			(10)
#define DEBUG_CORNER_TEST_61_BMP_ALL_PROBABILITY	(100)
#define DEBUG_CORNER_TEST_69_PROBABILITY 			(10)

#define DEBUG_CORNER_TEST_18_TIME_THRESHOLD	(600000)
#define DEBUG_CORNER_TEST_62_TABLE_BIG 		(FALSE)

//=============================================================================
// Types and Structures
//=============================================================================

//=============================================================================
// Global Variables
//=============================================================================
//IOR IOT & Fip FIFO Entry
#if (DEBUG_IOR_FIP_FIFO_ENTRY_EN)
extern U32 gaulFipFIFO[8];
extern U8 gubLastFipFIFOEntryIdx;
#endif
extern U64 guoDebugLastAutoReturnSwitchTime;

//=============================================================================
// Functions
//=============================================================================
AOM_DEBUG_INIT void DebugInitFreePoolCheck(void);
AOM_DEBUG void DebugAutoReturnSwitch(void);

#endif /* _DEBUG_API_H_ */
