#include "err_handle/err_handle_api.h"
#include "hal/pic/uart/uart_api.h"
#include "hal/sys/api/efuc/efuse_api.h"
#include "nvme_api/nvme/shr_hal_nvme.h"
#include "burner/IDpage.h"
#include "vuc/VUC_command.h"
#include "vuc/VUC_DumpTable.h"
#include "vuc/VUC_SearchSysBlock.h"
#include "host/VUC_handler.h"
#include "VUC_GetVucDataSize.h"

void VUC_GetVucDataSize(VUC_OPT_HCMD_PTR_t pCmd)
{
	U8 ubReadCmdSubFeature = pCmd->vuc_sqcmd.vendor.GetVUCDataSize.ubRCmdSubFeature;
	U8 ubReadCmdFeature = pCmd->vuc_sqcmd.vendor.GetVUCDataSize.ubRCmdFeature;
	U32 ulDataSize = 0;

	memset((void *)(gulVUCBufAddr), 0x00, DEF_512B);

	switch (ubReadCmdFeature) {
	case VUC_DUMP_TABLE:
		switch (ubReadCmdSubFeature) {
		case VUC_DUMP_IDPage:
			ulDataSize = IDPG_SIZE;
			break;
		case VUC_DUMP_InfoBlock:
			ulDataSize = INFO_BLOCK_SIZE ;
			break;
		case VUC_DUMP_BadBlockTable:
			ulDataSize = gubCENumber * gubDieNumber * (gFlhEnv.ulBlockPerPlaneBank << gubBurstsPerBankLog);
			break;
		case VUC_DUMP_RUT:
			ulDataSize = RUT_L1_LEN + RUT_L2_LEN;
			break;
		case VUC_DUMP_DrivelogAddr:
			ulDataSize = DBUF_DRIVE_LOG_SIZE;
			break;
		case VUC_DUMP_VT:
			ulDataSize = VT_SIZE_TOTAL;
			break;
		case VUC_DUMP_EraseCountTable:
			ulDataSize = DBUF_ERASE_CNT_SIZE;
			break;
		case VUC_DUMP_PGD:
			ulDataSize = PGD_SIZE;
			break;
		case VUC_DUMP_PMD:
			ulDataSize = PMD_SIZE;
			break;
		case VUC_DUMP_PTE:
			ulDataSize = PTE_SIZE;
			break;
		case VUC_DUMP_EfuseTable:
			ulDataSize = EFUSE_BANK_NUM * EFUSE_BANK_SIZE;
			break;
		}
	// no break
	case VUC_SEARCH_SYS_BLOCK :
		break;
	default:
		break;
	}

	ulDataSize = CEILING_DIV(ulDataSize, SECTOR_SIZE);
	memcpy((void *)gulVUCBufAddr, (void *)&ulDataSize, sizeof(ulDataSize));

	M_UART(VUC_, "\nDataSize:%l", ulDataSize);
}
