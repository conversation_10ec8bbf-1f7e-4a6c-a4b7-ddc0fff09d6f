#ifndef _WDT_API_H_
#define _WDT_API_H_

#include "hal/sys/reg/sys_pd1_reg.h"

#if PS5021_EN
#define M_WDT_DIS()								// E21 not support
#define M_WDT_EN()								// E21 not support
#else /* PS5021_EN */
#define M_WDT_DIS()                             (R32_SYS1_WDT[R32_SYS1_WDT_CTRL] &= (~CR_WDT_EN_BIT))
#define M_WDT_EN()                              (R32_SYS1_WDT[R32_SYS1_WDT_CTRL] |= CR_WDT_EN_BIT)
#endif /* PS5021_EN */

void WDTResetWholeChip(void);

#endif /* _WDT_API_H_ */
