#include "VUC_MicronPhysicaltoLogicalAddress.h"
#include "VUC_MicronGetNandErrorRecoveryStatistics.h"
#include "VUC_MicronResponse.h"
#include "hal/sys/api/angc/angc_api.h"
#include "hal/dmac/dmac_pop_cmd.h"
#include "hal/cop0/cop0_reg.h"
#include "hal/cop0/cop0_api.h"
#include "hal/bmu/bmu_api.h"
#include "vuc/VUC_ReadFlash.h"
#include "common/fw_common.h"
#include "ftl/ftl_api.h"

#if (VUC_MICRON_NAND_VS_COMMANDS_EN)

void VUCMicronPhysicaltoLogicalAddress(U32 ulInputPayloadAddr, U32 ulPayloadAddr)
{
	PhysicaltoLogicalAddressInputData_t *pInputData;
	PhysicaltoLogicalAddressHEADER_t *pResponseHeader;
	pInputData = (PhysicaltoLogicalAddressInputData_t *)(ulInputPayloadAddr + VUC_MICRON_GET_BLOCK_NAND_MODE_HEADER_LENGTH);
	U32 ulResponseAddr = ulPayloadAddr + VUC_MICRON_RESPONSE_HEADER_SIZE;
	U32 ulPCA = 0;
	U64 ullLBA = 0;
	COP0Status_t COP0Status;
	COP0_Attr_t ulCOP0AttributeBackup; // backup cop0 attr
	cmd_table_t uoCallbackInfo = {(U32)VUCRead_Callback, {0}};
	COP0ReadSQPara_t ReadSQParameter = {{0}};
	BUF_TYPE_t ulBufParameter = {0};
	FlashAccessInfo_t FlaInfo = {0};
	FWSetPara_t ulFWSet[FRAMES_PER_PAGE] = {{0}};
	DMACParam_t DMACParam;
	U8 ubTmp[10] = "";
	U8 ubi = 0;

	FlaInfo.ubFlashCE = (U8)pInputData->uwCE;
	FlaInfo.ubChannel = (U8)pInputData->uwCH;
	FlaInfo.ubPlane = BIT_MASK(2) & (pInputData->uwBlk);
	FlaInfo.uwPage = pInputData->uwPage;
	FlaInfo.ubLUN = pInputData->uwLUN;
	FlaInfo.ulBlock = (pInputData->uwBlk) >> 2;
	FlaInfo.ubFrame = pInputData->uwFrameIdx;

	ulPCA = FlaGetPCA(FlaInfo.ubPlane, FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, FlaInfo.uwPage, FlaInfo.ulBlock, COP0_PCA_RULE_0);
	ulPCA |= (FlaInfo.ubFrame & gPCARule_Entry.ulMask) << gPCARule_Entry.ubShift[COP0_PCA_RULE_0];	// Frame(Entry)

	ulCOP0AttributeBackup.ulAll = M_COP0_GET_ATTR(COP0_MT_ATTR2);
	M_CLR_COP0_LCA_CMP();

	DMACParam.DMACSetValue.ulDestAddr = ulPayloadAddr;
	DMACParam.DMACSetValue.ulLowValue = 0;
	DMACParam.DMACSetValue.ulHighValue = 0;
	DMACParam.DMACSetValue.ul32ByteNum = SIZE_IN_32B(DEF_KB(4));
	DMACParam.DMACSetValue.btValidate = FALSE;
	gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
	DMACSetValue(&DMACParam, gulDMACDirectWaitDone_Callback, 0);
	while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
		DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
	}

	ulBufParameter.A.ulBUF_ADR = ulPayloadAddr;

	COP0API_FillCOP0ReadSQUserData0Para(COP0_R_COPYUNIT_READ, &ReadSQParameter);
	ReadSQParameter.UserData0.btSLCMode = FALSE;
	ReadSQParameter.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;
	ReadSQParameter.UserData0.TagID = TAG_ID_ALLOCATE;
	ReadSQParameter.UserData0.btReadTieOutMethod = TRUE;
	ReadSQParameter.ulPCA.ulAll = ulPCA;
	ReadSQParameter.UserData0.btRUTBps = TRUE;
	ReadSQParameter.UserData0.btVBRMPBps = TRUE;
	ReadSQParameter.UserData0.AttrMTTemplate = COP0_MT_TEMP_BUF_ADDR_READ;
	ReadSQParameter.UserData0.L4KNum = 0;
	ReadSQParameter.UserData0.btSerial = FALSE;
	ReadSQParameter.BufVld.ubBufType = BUF_TYPE_A;
	ReadSQParameter.BufVld.pulBufInfoPtr = &ulBufParameter;
	ReadSQParameter.pulFWSetPtr = ulFWSet;
	ReadSQParameter.UserData0.btUseParamSeed = TRUE;
	ReadSQParameter.ulSeed.Seed = FWCommonGetSeedInit(FlaInfo.uwPage);

	for (ubi = 0; ubi < gub4kEntrysPerPlane; ubi++) {
#if (PS5017_EN || PS5021_EN)
		ulFWSet[ubi].ulFWSet = ((DBUF_GC_BACKUP_COPY_UNIT_IDX + ubi));
#else /* (PS5017_EN || PS5021_EN) */
		ulFWSet[ubi].ulFWSet = ((GC_BACKUP_COPY_UNIT_OFF + ubi));
#endif /* (PS5017_EN || PS5021_EN) */

		ulFWSet[ubi].ubZInfo = MAX_ZINFO;
	}

	COP0Status = COP0API_SendReadSQ(&ReadSQParameter, &uoCallbackInfo);
	UartPrintf("\nStatus:%x", COP0Status.btSendCmdSuccess);

	while (COP0Status.btSendCmdSuccess != TRUE) {
		FWErrRecorder();
		COP0DelegateCmd();
		ErrHandleRetryDone();
		FIPDelegateCmd();
		COP0Status = COP0API_SendReadSQ(&ReadSQParameter, &uoCallbackInfo);
	}

	M_FW_ASSERT(ASSERT_VUC_0x0AC5, TRUE == COP0Status.btSendCmdSuccess);

	gulTieoutLCA[TIEOUT_SINGLE_4K_LCA_OFFSET] = LCA_BEFORE_READ;
	while (LCA_BEFORE_READ == gulTieoutLCA[TIEOUT_SINGLE_4K_LCA_OFFSET]) {
		UartPrintf("\nWaiting");
		FWCop0Waiting();
	}
	// read error or LCA larger than user area, avoid to read extend LCA
	if ((gulVUCDirectedCieOutInfo != VUC_DIRECT_READ_NO_ERROR) || (gulTieoutLCA[TIEOUT_SINGLE_4K_LCA_OFFSET] >= (guoDiskInfo.uoNativeSize / 8))) {
		gulTieoutLCA[TIEOUT_SINGLE_4K_LCA_OFFSET] = COP0_LCA_INVALID;
	}

	M_COP0_GET_ATTR(COP0_MT_ATTR2) = ulCOP0AttributeBackup.ulAll;

	ullLBA = gulTieoutLCA[TIEOUT_SINGLE_4K_LCA_OFFSET] * 8;

	memset((void *)ulPayloadAddr, 0x0, 512);
	pResponseHeader = (PhysicaltoLogicalAddressHEADER_t *)ulPayloadAddr;

	pResponseHeader->ubResponseHeaderFormatVersion = VUC_MICRON_RESPONSE_HEADER_FORMAT_VERSION_0;
	pResponseHeader->ubResponseDataFotmatVersion = VUC_MICRON_RESPONSE_FORMAT_JSON;
	pResponseHeader->uwCMDClass = VUC_MICRON_COMMON_VS_COMMANDS;
	pResponseHeader->uwCMDCode = VUC_MICRON_GET_P2L;
	pResponseHeader->uwStatus = 0;
	pResponseHeader->ulDataPayloadSize = SECTOR_SIZE;


	VUCMyStrcat((void *)ulResponseAddr, "{\n\t");
	VUCMyStrcat((void *)ulResponseAddr, "\"structVer\":1,\n\t");
#if (HOST_MODE == NVME)
	VUCMyStrcat((void *)ulResponseAddr, "\"LBA\":");
	VUCMyitoa(ullLBA >> 32, (void *)ubTmp, DECIMAL);
	VUCMyStrcat((void *)ulResponseAddr, (void *)ubTmp);
	VUCMyitoa((BIT_MASK64(32) & ullLBA), (void *)ubTmp, DECIMAL);
#else /* (HOST_MODE == NVME) */
	VUCMyStrcat((void *)ulResponseAddr, "\"LBA\":0x");
	VUCMyitoa(ullLBA >> 32, (void *)ubTmp, HEXADECIMAL);
	VUCMyStrcat((void *)ulResponseAddr, (void *)ubTmp);
	VUCMyitoa((BIT_MASK64(32) & ullLBA), (void *)ubTmp, HEXADECIMAL);
#endif /* (HOST_MODE == NVME) */
	VUCMyStrcat((void *)ulResponseAddr, (void *)ubTmp);
	VUCMyStrcat((void *)ulResponseAddr, ",\n\t");
	VUCMyStrcat((void *)ulResponseAddr, "\"NSID\":1");
	VUCMyStrcat((void *)ulResponseAddr, "\n}");
}

#endif /*(VUC_MICRON_NAND_VS_COMMANDS_EN)*/
