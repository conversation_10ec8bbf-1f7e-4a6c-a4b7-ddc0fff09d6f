#ifndef _PMU_API_H_
#define _PMU_API_H_

#include "hal/sys/reg/sys_pd0_reg.h"
#include "aom/aom_api.h"


#define	M_CLR_PMU_WU_EVENT_STS(WU_SOURCE)                       (R32_SYS_PMU[R32_SYS0_PMU_WU_EVENT_STS] &= ~WU_SOURCE)
#define	M_PMU_GET_WU_EVENT_STS()                                (R32_SYS_PMU[R32_SYS0_PMU_WU_EVENT_STS])

#if PS5021_EN
#define	M_PMU_EXTERNAL_POWER_IC_EN()							// E21 not support
#define	M_PMU_EXTERNAL_POWER_IC_DIS()							// E21 not support
#else /* PS5021_EN */
#define	M_PMU_EXTERNAL_POWER_IC_EN()							(R32_SYS_PMU[R32_SYS0_PMU_PD_CTRL] |= CR_PMU_EXPWR_EN_BIT) //enable wake up
#define	M_PMU_EXTERNAL_POWER_IC_DIS()							(R32_SYS_PMU[R32_SYS0_PMU_PD_CTRL] &= ~(CR_PMU_EXPWR_EN_BIT))
#endif /* PS5021_EN */
#define	M_SET_PMU_WAKE_UP_ENABLE()								(R32_SYS_PMU[R32_SYS0_PMU_PD_CTRL] |= CR_PMU_WU_F_BIT) //enable wake up
#define	M_CLR_PMU_MD_EN()										(R16_SYS_PMU[R16_SYS0_PMU_MODE_EN] = 0)
#define	M_SET_PMU_MD_EN(MODE)									(R16_SYS_PMU[R16_SYS0_PMU_MODE_EN] = (MODE))
#define	M_CLR_MSK_PMU_MD_EN()									(R16_SYS_PMU[R16_SYS0_PMU_MODE_EN] &= ~MSK_PMU_MD)
#define	M_PMU_GET_MD()											(R16_SYS_PMU[R16_SYS0_PMU_MODE_EN] & MSK_PMU_MD)
#define	M_PMU_WU_SEL_CR_WAKE_EN(WU_SOURCE)						(R32_SYS_PMU[R32_SYS0_PMU_WU_SEL] = (WU_SOURCE))
#define	M_PMU_WU_SEL_CR_WAKE_DIS(WU_SOURCE)						(R32_SYS_PMU[R32_SYS0_PMU_WU_SEL] &= ~(WU_SOURCE))
#define	M_PMU_GET_WAKEUP_SELECT()			        			(R32_SYS_PMU[R32_SYS0_PMU_WU_SEL])
#define	M_PMU_CR_OP_PRAM_EN()									(R32_SYS_PMU[R32_SYS0_PMU_MEMPD_CTRL] |= CR_OP_PRAM_EN_BIT)
#define	M_PMU_FORCE_REFCLK_NOGATE()								(R32_SYS_PMU[R32_SYS0_PMU_SATA_ROSC_CAL] |= CR_FORCE_REFCLK_NOGATE_BIT)

#if PS5017_EN
#define	M_SYS0_RLS_PCIERST()									do { \
																M_ARM_IRQ_DISABLE(); \
																R32_SYS_PMU[R32_SYS0_PMU_RSTN0_CTL] |= (CR_PCIE_PERSTN_BIT | CR_PCIE_RESETN_BIT); \
																M_ARM_IRQ_ENABLE(); \
																} while (0)
#elif PS5021_EN /* PS5017_EN */
#define	M_SYS0_RLS_PCIERST()									do { \
																CPUARMSetCriticalRegisterBit((R32_SYS0_HOST_CTRL + R32_SYS0_PCIE_CTRL_REG), (PCIE_RESETN_BIT | PCIE_PHY_2RSTN_BIT)); \
																} while(0)
#else /* PS5017_EN */
#define	M_SYS0_RLS_PCIERST()									do { \
																CPUARMSetCriticalRegisterBit((R32_SYS_PMU + R32_SYS0_PMU_RSTN0_CTL), (CR_PCIE_PERSTN_BIT | CR_PCIE_RESETN_BIT)); \
																} while(0)
#endif /* PS5017_EN */

#define	M_SYS0_GET_PCIERST()									(R32_SYS_PMU[R32_SYS0_PMU_RSTN0_CTL] & (CR_PCIE_PERSTN_BIT | CR_PCIE_RESETN_BIT))
#define	PRESET_RISING											(CR_PCIE_PERSTN_BIT | CR_PCIE_RESETN_BIT)

#if PS5017_EN
#define	M_SYS0_ASSERT_RESETN()									do { \
																M_ARM_IRQ_DISABLE(); \
																R32_SYS_PMU[R32_SYS0_PMU_RSTN0_CTL] &= ~CR_PCIE_RESETN_BIT; \
																M_ARM_IRQ_ENABLE(); \
																} while (0)
#define	M_SYS0_ASSERT_PERSTN()									do { \
																M_ARM_IRQ_DISABLE(); \
																R32_SYS_PMU[R32_SYS0_PMU_RSTN0_CTL] &= ~CR_PCIE_PERSTN_BIT; \
																M_ARM_IRQ_ENABLE(); \
																} while (0)
#elif PS5021_EN /* PS5017_EN */
#define	M_SYS0_ASSERT_RESETN()									do { \
																CPUARMClearCriticalRegisterBit((R32_SYS0_HOST_CTRL + R32_SYS0_PCIE_CTRL_REG), PCIE_RESETN_BIT); \
																} while (0)
#define	M_SYS0_ASSERT_PERSTN()									do { \
																CPUARMClearCriticalRegisterBit((R32_SYS0_HOST_CTRL + R32_SYS0_PCIE_CTRL_REG), PCIE_PHY_2RSTN_BIT); \
																} while (0)
#else /* PS5017_EN */
#define	M_SYS0_ASSERT_RESETN()									do { \
																CPUARMClearCriticalRegisterBit((R32_SYS_PMU + R32_SYS0_PMU_RSTN0_CTL), CR_PCIE_RESETN_BIT); \
																} while (0)
#define	M_SYS0_ASSERT_PERSTN()									do { \
																CPUARMClearCriticalRegisterBit((R32_SYS_PMU + R32_SYS0_PMU_RSTN0_CTL), CR_PCIE_PERSTN_BIT); \
																} while (0)
#endif /* PS5017_EN */

#if E21_TODO
#define	M_PMU_CHECK_PCIE_RESET()								((R32_SYS0_HOST_CTRL[R32_SYS0_PCIE_CTRL_REG] & (PCIE_RESETN_BIT | PCIE_PHY_2RSTN_BIT)) == (PCIE_RESETN_BIT | PCIE_PHY_2RSTN_BIT))
#else /* E21_TODO */
#define	M_PMU_CHECK_PCIE_RESET()								((R32_SYS_PMU[R32_SYS0_PMU_RSTN0_CTL] & (CR_PCIE_PERSTN_BIT | CR_PCIE_RESETN_BIT)) == (CR_PCIE_PERSTN_BIT | CR_PCIE_RESETN_BIT))
#endif /* E21_TODO */

#if PS5017_EN
#define	M_SYS0_SET_RST_FW_MODE()								do { \
																M_ARM_IRQ_DISABLE();\
																R32_SYS_PMU[R32_SYS0_PMU_RSTN0_CTL] &= ~CR_PCIE_RST_MODE; \
																M_ARM_IRQ_ENABLE(); \
																M_ARM_IRQ_DISABLE(); \
																R32_SYS_PMU[R32_SYS0_PMU_RSTN0_CTL] |= (CR_PCIE_RST_MODE_FW_CONTROL << CR_PCIE_RST_MODE_SHIFT); \
																M_ARM_IRQ_ENABLE(); \
																} while (0)
#define	M_SYS0_SET_RST_HW_MODE()								do { \
																M_ARM_IRQ_DISABLE(); \
																R32_SYS_PMU[R32_SYS0_PMU_RSTN0_CTL] &= ~CR_PCIE_RST_MODE; \
																M_ARM_IRQ_ENABLE(); \
																} while (0)
#define	M_PMU_CLKREQB_DET_EN()									do { \
																M_ARM_IRQ_DISABLE(); \
																R32_SYS_PMU[R32_SYS0_PMU_RSTN0_CTL] |= CR_CLKREQB_DET_EN_BIT; \
																M_ARM_IRQ_ENABLE(); \
																} while (0)
#define M_PMU_CR_PEREST_DET_EN()								do { \
																M_ARM_IRQ_DISABLE(); \
																R32_SYS_PMU[R32_SYS0_PMU_RSTN0_CTL] |= CR_PEREST_DET_EN_BIT; \
																M_ARM_IRQ_ENABLE(); \
																} while (0)
#else /* PS5017_EN */
#define	M_SYS0_SET_RST_FW_MODE()								do { \
																CPUARMSetCriticalRegisterBit((R32_SYS_PMU + R32_SYS0_PMU_RSTN0_CTL), CR_PCIE_RST_MODE_BIT); \
																} while(0)
#if PS5021_EN
#define	M_SYS0_SET_RST_HW_MODE()								do { \
																CPUARMClearCriticalRegisterBit((R32_SYS0_HOST_CTRL + R32_SYS0_PCIE_CTRL_REG), PCIE_RESETN_CLR_EN_BIT);\
																CPUARMClearCriticalRegisterBit((R32_SYS0_HOST_CTRL + R32_SYS0_PCIE_CTRL_REG), PCIE_PHY_2RSTN_CLR_EN_BIT);\
																} while (0)
#else /*PS5021_EN*/
#define	M_SYS0_SET_RST_HW_MODE()								do { \
																CPUARMClearCriticalRegisterBit((R32_SYS_PMU + R32_SYS0_PMU_RSTN0_CTL), CR_PCIE_RST_MODE_BIT); \
																} while (0)
#endif /*PS5021_EN*/
#define	M_PMU_CLKREQB_DET_EN()									do { \
																CPUARMSetCriticalRegisterBit((R32_SYS_PMU + R32_SYS0_PMU_RSTN0_CTL), CR_CLKREQB_DET_EN_BIT); \
																} while(0)
#define M_PMU_CR_PEREST_DET_EN()								do { \
																CPUARMSetCriticalRegisterBit((R32_SYS_PMU + R32_SYS0_PMU_RSTN0_CTL), CR_PEREST_DET_EN_BIT); \
																} while(0)
#endif /* PS5017_EN */

#if PS5021_EN
#define	M_PMU_GET_DEBOUNCE()									(R32_SYS0_PAD_CTRL[R32_SYS0_PAD_PD0_CTRL] & PERSTN_PAD_SYNC_BIT)
#define	M_PMU_CHECK_PERSTN_DEBOUNCE()                           (R32_SYS0_PAD_CTRL[R32_SYS0_PAD_PD0_CTRL] & PERSTN_PAD_SYNC_BIT)
#else /*PS5021_EN*/
#define	M_PMU_GET_DEBOUNCE()									(R32_SYS_PMU[R32_SYS0_PMU_RSTN0_CTL] & PERSTN_DBC_BIT)
#endif /*PS5021_EN*/

#define	M_PMU_GET_RESET_CONTROL()								(R32_SYS_PMU[R32_SYS0_PMU_RSTN0_CTL])
#define M_PMU_LEAVE_LPM_DISABLE()                               (R32_SYS_PMU[R32_SYS0_PMU_CPU_STATUS_CTRL] &= ~CR_LEAVE_LPM_BIT)
#define M_PMU_LEAVE_LPM_ENABLE()                                (R32_SYS_PMU[R32_SYS0_PMU_CPU_STATUS_CTRL] |= CR_LEAVE_LPM_BIT)
#define M_PMU_GET_GATING_CLOCK()                                (R32_SYS_PMU[R32_SYS0_PMU_CPU_STATUS_CTRL] & SYSTEM_CLOCK_GATING_BIT)
#define M_PMU_GET_WAKEUP_FLAG()                                 (R32_SYS_PMU[R32_SYS0_PMU_WAKEUP_FLAG] & WAKEUP_F_BIT)
#define M_PMU_GET_HOST_STS()                                    (R32_SYS_PMU[R32_SYS0_PMU_PG_CTRL] & SR_HOST_STS_BIT)
#define M_PMU_GET_LPM3_PARITY_ERROR()                           (R32_SYS_PMU[R32_SYS0_PMU_ITC_WU_EVENT_STS] & PMU_ITC_STS_SRAM_PARITY_ERROR_BIT)
#define M_PMU_CLR_LPM3_PARITY_ERROR()                           (R32_SYS_PMU[R32_SYS0_PMU_ITC_WU_EVENT_STS] |= PMU_ITC_STS_SRAM_PARITY_ERROR_BIT)
#if E21_TODO
#define M_PMU_GET_PMU_PARITY_ERROR()                            (0)
#else /* E21_TODO */
#define M_PMU_GET_PMU_PARITY_ERROR()                            (R32_SYS_PMU[R32_SYS0_PMU_ITC_WU_EVENT_STS] & PMU_ITC_STS_PMU_ROM_PARITY_ERROR_BIT)
#endif /* E21_TODO */
#if PS5017_EN
#define M_PMU_DEVSLP_DET_ENABLE()								do { \
																M_ARM_IRQ_DISABLE(); \
																R32_SYS_PMU[R32_SYS0_PMU_RSTN0_CTL] |= PMU_CR_DEVSLP_DET_EN_BIT; \
																M_ARM_IRQ_ENABLE(); \
																} while (0)
#define	M_PMU_DEVSLP_DET_DISABLE()								do { \
																M_ARM_IRQ_DISABLE(); \
																R32_SYS_PMU[R32_SYS0_PMU_RSTN0_CTL] &= ~PMU_CR_DEVSLP_DET_EN_BIT; \
																M_ARM_IRQ_ENABLE(); \
																} while (0)
#else /* PS5017_EN */
#define M_PMU_DEVSLP_DET_ENABLE()								do { \
																CPUARMSetCriticalRegisterBit((R32_SYS_PMU + R32_SYS0_PMU_RSTN0_CTL), PMU_CR_DEVSLP_DET_EN_BIT); \
																} while(0)
#define	M_PMU_DEVSLP_DET_DISABLE()								do { \
																CPUARMClearCriticalRegisterBit((R32_SYS_PMU + R32_SYS0_PMU_RSTN0_CTL), PMU_CR_DEVSLP_DET_EN_BIT); \
																} while (0)
#endif /* PS5017_EN */

#if PS5017_EN
#define M_PMU_CPHY_RESTN_ENABLE()								(R32_SYS_PMU[R32_SYS0_PMU_RSTN0_CTL] |= CR_CPHY_RESETN)
#define	M_PMU_CPHY_RESTN_DISABLE()								(R32_SYS_PMU[R32_SYS0_PMU_RSTN0_CTL] &= ~CR_CPHY_RESETN)
#define	M_PMU_USB_RSTN_MODE_ENABLE()							(R32_SYS_PMU[R32_SYS0_PMU_RSTN0_CTL] |= CR_USB_RSTN_MODE)
#endif /* PS5017_EN */

#define	M_PMU_LOW_VOLTAGE_ENABLE()								(R32_SYS_PMU[R32_SYS0_PMU_CTRL0] &= ~PMU_LV_DISABLE_BIT) // low volt enable
#define	M_PMU_LOW_VOLTAGE_DISABLE()								(R32_SYS_PMU[R32_SYS0_PMU_CTRL0] |= PMU_LV_DISABLE_BIT) // low volt disable
#define	M_PMU_CORE_WARMUP_PD_DISABLE()							(R32_SYS_PMU[R32_SYS0_PMU_CTRL0] &= ~CR_OP_CORE_WARMUP_PD_BIT)
#if PS5017_EN
#define	M_PMU_CHECK_FLASH_CORE_POWER()							(R32_SYS_PMU[R32_SYS0_PMU_PHY_POWER_CTRL] & CR_LVCC)
#elif PS5021_EN /* PS5017_EN */
#define M_PMU_CHECK_FLASH_CORE_POWER()							(TRUE) // only 2.5V
#endif /* PS5017_EN */
#define	M_PMU_VDT_ENABLE(x)										(R32_SYS_PMU[R32_SYS0_PMU_PG_CTRL] &= ~(x))
#define M_PMU_VDT_DISABLE(x)									(R32_SYS_PMU[R32_SYS0_PMU_PG_CTRL] |= (x)) //set 1 to disable
#define	M_PMU_U8_VDT_ENABLE(x)									(R8_SYS_PMU[R8_SYS0_PMU_PG_CTRL] &= ~(x))
#define	M_PMU_U8_VDT_DISABLE(x)									(R8_SYS_PMU[R8_SYS0_PMU_PG_CTRL] |= (x)) //set 1 to disable

#if PS5017_EN
#define	M_PMU_FORCE_CPHY_RC_ON_ENABLE()							(R32_SYS_PMU[R32_SYS0_PMU_SATA_ROSC_CAL] |= CR_FORCE_CPHY_RC_ON_BIT)
#define	M_PMU_FORCE_CPHY_RC_ON_DISABLE()						(R32_SYS_PMU[R32_SYS0_PMU_SATA_ROSC_CAL] &= (~CR_FORCE_CPHY_RC_ON_BIT))
#define	M_PMU_FORCE_SATA_RC_ON_ENABLE()							(R32_SYS_PMU[R32_SYS0_PMU_SATA_ROSC_CAL] |= CR_FORCE_LFOSC_ON_BIT)
#define	M_PMU_FORCE_SATA_RC_ON_DISABLE()						(R32_SYS_PMU[R32_SYS0_PMU_SATA_ROSC_CAL] &= (~CR_FORCE_LFOSC_ON_BIT))
#else /* PS5017_EN */
#define	M_PMU_FORCE_SATA_RC_ON_ENABLE()							(R32_SYS_PMU[R32_SYS0_PMU_SATA_ROSC_CAL] |= CR_FORCE_SATA_RC_NO_PD_BIT)
#define	M_PMU_FORCE_SATA_RC_ON_DISABLE()						(R32_SYS_PMU[R32_SYS0_PMU_SATA_ROSC_CAL] &= (~CR_FORCE_SATA_RC_NO_PD_BIT))
#endif /* PS5017_EN */

#define	M_CLR_PMU_SD_WU_CMD()									(R32_SYS_PMU[R32_SYS0_PMU_COP_CMD_ADR1] = 0)
#define	M_SET_PMU_WAKE_UP_MODE(WU_MODE)							(R32_SYS_PMU[R32_SYS0_PMU_COP_CMD_ADR1] |= ((WU_MODE) << 16))
#define	M_SET_PMU_SHUT_DOWN_MODE(SD_MODE)						(R32_SYS_PMU[R32_SYS0_PMU_COP_CMD_ADR1] |= (SD_MODE))

#define	M_CLR_PMU_WU_FLAG()										(R32_SYS_PMU[R32_SYS0_PMU_WAKEUP_FLAG] |= WAKEUP_F_BIT)
#define	M_SET_PMU_POWER_DOWN_ENABLE()							(R32_SYS_PMU[R32_SYS0_PMU_PD_CTRL] |= CR_PMU_PD_F_BIT) //enable power down
#define	M_PMU_GET_POWER_DOWN_FLAG()						    	(R32_SYS_PMU[R32_SYS0_PMU_PD_CTRL] & CR_PMU_PD_F_BIT)

#define M_GET_PMU_ITC_WAKEUP_EVENT_STATUS()						(R32_SYS_PMU[R32_SYS0_PMU_ITC_WU_EVENT_STS])
#define M_CLR_PMU_ITC_WU_EVENT_STS()							(R32_SYS_PMU[R32_SYS0_PMU_ITC_WU_EVENT_STS] = PMU_ITC_STS)

#if PS5017_EN
#define M_PMU_DBUF_PD0_PD_DISABLE(X)                            (R32_SYS_PMU[R32_SYS0_DBUF_SD_CONTROL] &= (~(X))) // E17
#else /* PS5017_EN */
#define M_PMU_DBUF_PD0_PD_DISABLE(X)							do { \
																R32_SYS_PMU[R32_SYS0_PMU_MEMPD_CTRL] |= CR_OP_PD0_DBUF_RAM_PD; \
                                                                R32_SYS_PMU[R32_SYS0_PMU_MEMPD_CTRL] &= (~(X)); \
                                                                } while(0)
#endif /* PS5017_EN */

#define	M_PMU_PRAM_SWITCH_EN()									(R16_SYS_PMU[R16_SYS0_PMU_MODE_EN] |= (MD_PRAM_EN_BIT))
#define	M_PMU_SET_ATCM_BACKUP_SRC_ADDRESS(ADDR)					(R32_SYS_PMU[R32_SYS0_PMU_ATCM_BACKUP_SADDR] = ADDR)
#define M_PMU_SET_ATCM_BACKUP_DEST_ADDRESS(ADDR)				(R32_SYS_PMU[R32_SYS0_PMU_ATCM_BACKUP_DADDR] = ADDR)
#define M_PMU_SET_ATCM_BACKUP_LENGTH(LENGTH)					do { \
																R32_SYS_PMU[R32_SYS0_PMU_BACKUP_LENGTH0] &= BTCM_BACKUP_LENGTH; \
																R32_SYS_PMU[R32_SYS0_PMU_BACKUP_LENGTH0] |= ((LENGTH >> 5) & ATCM_BACKUP_LENGTH); \
																} while(0)
#define	M_PMU_SET_BTCM_BACKUP_SRC_ADDRESS(ADDR)					(R32_SYS_PMU[R32_SYS0_PMU_BTCM_BACKUP_SADDR] = ADDR)
#define	M_PMU_SET_BTCM_BACKUP_DEST_ADDRESS(ADDR)				(R32_SYS_PMU[R32_SYS0_PMU_BTCM_BACKUP_DADDR] = ADDR)
#define	M_PMU_SET_BTCM_BACKUP_LENGTH(LENGTH)					do { \
																R32_SYS_PMU[R32_SYS0_PMU_BACKUP_LENGTH0] &= ATCM_BACKUP_LENGTH; \
																R32_SYS_PMU[R32_SYS0_PMU_BACKUP_LENGTH0] |= (((LENGTH >> 5) & ATCM_BACKUP_LENGTH) << 16); \
																} while(0)

#define	M_PMU_SET_SEC_BACKUP_SRC_ADDRESS(ADDR)					(R32_SYS_PMU[R32_SYS0_PMU_SEC_BACKUP_SADDR] = ADDR)
#define	M_PMU_SET_SEC_BACKUP_DEST_ADDRESS(ADDR)					(R32_SYS_PMU[R32_SYS0_PMU_SEC_BACKUP_DADDR] = ADDR)
#define	M_PMU_SET_SEC_BACKUP_LENGTH(LENGTH)						do { \
																R32_SYS_PMU[R32_SYS0_PMU_BACKUP_LENGTH1] &= 0xFFFF0000; \
																R32_SYS_PMU[R32_SYS0_PMU_BACKUP_LENGTH1] |= ((LENGTH >> 5) & SEC_BACKUP_LENGTH); \
																} while(0)
#define	M_PMU_ROM_WAKE_UP_ENABLE()								(R32_SYS_PMU[R32_SYS0_PMU_BACKUP_LENGTH1] |= CR_OP_ROM_WU_BIT)
#define	M_PMU_ROM_WAKE_UP_DISABLE()								(R32_SYS_PMU[R32_SYS0_PMU_BACKUP_LENGTH1] &= (~CR_OP_ROM_WU_BIT))
#define	M_PMU_BACKUP_TABLE_EN(ENABLE)							do { \
																R32_SYS_PMU[R32_SYS0_PMU_BACKUP_LENGTH1] &= (~(ATCM_BK_EN_BIT | BTCM_BK_EN_BIT | AES_BK_EN_BIT)); \
																R32_SYS_PMU[R32_SYS0_PMU_BACKUP_LENGTH1] |= (ENABLE); \
																} while(0)

#define M_GET_PMU_GPIO_WU_SEL()                                 (R32_SYS_PMU[R32_SYS0_PMU_GPIO_WU_SEL])
#define M_PMU_WU_GPIO_SEL_CLEAR()								(R32_SYS_PMU[R32_SYS0_PMU_GPIO_WU_SEL] = 0)
#define	M_PMU_WU_GPIO_SEL_0(X)									(R32_SYS_PMU[R32_SYS0_PMU_GPIO_WU_SEL] |= (((U32)(X) & CR_GPIO_DET_SEL_MASK) << CR_GPIO_DET_SEL_SHIFT))
#define	M_PMU_WU_GPIO_SEL_1(X)									(R32_SYS_PMU[R32_SYS0_PMU_GPIO_WU_SEL] |= (((U32)(X) & CR_GPIO_DET_SEL1_MASK) << CR_GPIO_DET_SEL1_SHIFT))
#define	M_PMU_WU_GPIO_SEL_2(X)									(R32_SYS_PMU[R32_SYS0_PMU_GPIO_WU_SEL] |= (((U32)(X) & CR_GPIO_DET_SEL2_MASK) << CR_GPIO_DET_SEL2_SHIFT))
#define	M_PMU_WU_GPIO_SEL_3(X)									(R32_SYS_PMU[R32_SYS0_PMU_GPIO_WU_SEL] |= (((U32)(X) & CR_GPIO_DET_SEL3_MASK) << CR_GPIO_DET_SEL3_SHIFT))
#define	M_PMU_GPIO_DET_EN_0()									(R32_SYS_PMU[R32_SYS0_PMU_GPIO_WU_SEL] |= CR_GPIO_DET_EN_BIT)
#define	M_PMU_GPIO_DET_DIS_0()									(R32_SYS_PMU[R32_SYS0_PMU_GPIO_WU_SEL] &= (~CR_GPIO_DET_EN_BIT))
#define	M_PMU_GPIO_DET_EN_1()									(R32_SYS_PMU[R32_SYS0_PMU_GPIO_WU_SEL] |= CR_GPIO_DET_EN1_BIT)
#define	M_PMU_GPIO_DET_DIS_1()									(R32_SYS_PMU[R32_SYS0_PMU_GPIO_WU_SEL] &= (~CR_GPIO_DET_EN1_BIT))
#define	M_PMU_GPIO_DET_EN_2()									(R32_SYS_PMU[R32_SYS0_PMU_GPIO_WU_SEL] |= CR_GPIO_DET_EN2_BIT)
#define	M_PMU_GPIO_DET_DIS_2()									(R32_SYS_PMU[R32_SYS0_PMU_GPIO_WU_SEL] &= (~CR_GPIO_DET_EN2_BIT))
#define	M_PMU_GPIO_DET_EN_3()									(R32_SYS_PMU[R32_SYS0_PMU_GPIO_WU_SEL] |= CR_GPIO_DET_EN3_BIT)
#define	M_PMU_GPIO_DET_DIS_3()									(R32_SYS_PMU[R32_SYS0_PMU_GPIO_WU_SEL] &= (~CR_GPIO_DET_EN3_BIT))
#if PS5017_EN
// E17 remove FLL  circuit, need to check OSC clock source usage
#define	M_PMU_TRIGGER_CHANGE_MODE()
#define	M_PMU_GET_CLK_SWITCH_MODE_STATUS()
#define	M_PMU_SET_CLK_SRC_OSC_MODE(X)
#else /* PS5017_EN */
#define	M_PMU_TRIGGER_CHANGE_MODE()								(R32_SYS_PMU[R32_SYS0_PMU_SATA_ROSC_CAL] |= (PMU_FLL_S_P_BIT))
#define	M_PMU_GET_CLK_SWITCH_MODE_STATUS()						(R32_SYS_PMU[R32_SYS0_PMU_SATA_ROSC_CAL] & SATA_FLL_CLK_STS_BIT)
#define	M_PMU_SET_CLK_SRC_OSC_MODE(X)							do { \
																R32_SYS_PMU[R32_SYS0_PMU_SATA_ROSC_CAL] &= ~(PMU_FLL_CMD_MASK << PMU_FLL_CMD_SHIFT); \
																R32_SYS_PMU[R32_SYS0_PMU_SATA_ROSC_CAL] |= (X); \
																} while(0)
#endif /* PS5017_EN */

#define M_PMU_SET_ATCM_BACKUP_TAR_ADDRESS(ADDR)					(R32_SYS_PMU[R32_SYS0_PMU_BACKUP_TADDR] = ADDR)

#define	SYSTEM_ENTER_LPM_1										(0)
#define	SYSTEM_ENTER_LPM_2										(1)
#define	SYSTEM_ENTER_LPM_3										(2)

#define SYSTEM_RESET_CPU_SIGN_BIT                   			(BIT0)

#define	LPM_LOW_VOLTAGE_ENABLE									(1)
#define	LPM_LOW_VOLTAGE_DISABLE									(0)

#define	LPM_RC_ON_ENABLE										(1)
#define	LPM_RC_ON_DISABLE										(0)

#define NON_ZERO_DATA                               			(1)

#define PASS                									(0)

#if PS5017_EN
#define	M_PMU_SET_IPWIC_LOWK_VPHY(V)							do { \
																R32_SYS_PMU[R32_SYS0_LOWK_CTRL] &= (~(LOWK_VPHY)); \
																R32_SYS_PMU[R32_SYS0_LOWK_CTRL] |= (V << LOWK_VPHY_SHIFT); \
																} while(0)
#define	M_PMU_SET_IPWIC_LOWK_CORE(C)							do { \
																R32_SYS_PMU[R32_SYS0_LOWK_CTRL] &= (~(LOWK_CORE)); \
																R32_SYS_PMU[R32_SYS0_LOWK_CTRL] |= (C << LOWK_CORE_SHIFT); \
																} while(0)
#else /* PS5017_EN */
#define	M_PMU_SET_IPWIC_LOWK_VPHY(V)							do { \
																R8_SYS_PMU[R8_SYS0_PMU_CTRL0] &= (~(LOWK_VPHY)); \
																R8_SYS_PMU[R8_SYS0_PMU_CTRL0] |= (V << 2); \
																} while(0)
#define	M_PMU_SET_IPWIC_LOWK_CORE(C)							do { \
																R8_SYS_PMU[R8_SYS0_PMU_CTRL0] &= (~(LOWK_CORE)); \
																R8_SYS_PMU[R8_SYS0_PMU_CTRL0] |= (C << 5); \
																} while(0)
#endif /* PS5017_EN */

#if PS5021_EN
#define	M_HOST_IS_SATA_MODE()									(FALSE)
#define	M_HOST_IS_PCIE_MODE()									(TRUE)
#else /* PS5021_EN */
#define	M_HOST_IS_SATA_MODE()									(R32_SYS_PMU[R32_SYS0_PMU_HOST_MODE_INFO] & SR_HMODE_SATA_BIT)
#define	M_HOST_IS_PCIE_MODE()									(R32_SYS_PMU[R32_SYS0_PMU_HOST_MODE_INFO] & SR_HMODE_PCIE_BIT)
#endif /* PS5021_EN */
#define	M_PMU_SET_AS_WAKE_EVT_UNMASK(MASK)						(R32_SYS_PMU[R32_SYS0_PMU_ASYNC_RST_CTRL] &= (~(MASK)))
#define	M_PMU_SET_RCOSC_AS_WAKE_EVT_UNMASK(MASK)				(R8_SYS_PMU[R8_SYS0_RCOSC_AS_WAKE_EVT_MASK] &= (~(MASK)))
#if PS5017_EN
#define	M_SATA_IS_REF_LOCK()									(R32_SYS_PMU[R32_SYS0_PMU_HOST_MODE_INFO] & SATA_REF_LOCK_BIT)
#endif /* PS5017_EN */

#if E21_TODO
#define	M_GET_CORE_VDT_CNT()									(0)
#define	M_GET_FLASH_VDT_CNT()									(0)
#define	M_CLEAR_CORE_VDT_CNT()									(NULL)
#define	M_CLEAR_FLASH_VDT_CNT()									(NULL)
#else /* E21_TODO */
#define	M_GET_CORE_VDT_CNT()									((R32_SYS_PMU[R32_SYS0_PMU_POWER_DROP_INFO] >> CORE_VDT_CNT_SHIFT) & CORE_VDT_CNT_MASK)
#define	M_GET_FLASH_VDT_CNT()									((R32_SYS_PMU[R32_SYS0_PMU_POWER_DROP_INFO] >> FLH_VDT_CNT_SHIFT) & FLH_VDT_CNT_MASK)
#define	M_CLEAR_CORE_VDT_CNT()									(R32_SYS_PMU[R32_SYS0_PMU_POWER_DROP_INFO] |= CORE_VDT_CNT_CLEAR_BIT)
#define	M_CLEAR_FLASH_VDT_CNT()									(R32_SYS_PMU[R32_SYS0_PMU_POWER_DROP_INFO] |= FLASH_VDT_CNT_CLEAR_BIT)
#endif /* E21_TODO */

#define	M_SET_CTRSTBYB_IN()										(R32_SYS_PMU[R32_SYS0_PMU_ISO_CTRL] |= CTRSTBYB_IN)
#if PS5017_EN
#define	M_PMU_GET_HOST_MODE(X)									do{ \
																(X) = (U8)((R16_SYS_PMU[R16_SYS0_PMU_MODE_EN] >> SR_1CH_MD_SHIFT) & SR_1CH_MD_MASK); \
																} while(0)
#else /* PS5017_EN */
#define	M_PMU_GET_HOST_MODE(X)									do{\
																(X) = (U8)((R16_SYS_PMU[R16_SYS0_PMU_MODE_EN] >> SR_XHMD_SHIFT) & SR_XHMD_MASK); \
																} while(0)
#endif /* PS5017_EN */
#define	PMU_SHUTDOWN_FLASH_POWER_DELAY_MS						(20) // for SATA, the power-off need 8.x ms.
#define	PMU_POWER_ON_FLASH_POWER_DELAY_MS						(3)
#define	PMU_SHUTDOWN_FLASH_POWER_DELAY_50NS						(PMU_SHUTDOWN_FLASH_POWER_DELAY_MS * 1000 * 1000 / 50) // 7ms / 50ns
#define	PMU_POWER_ON_FLASH_POWER_DELAY_50NS						(PMU_POWER_ON_FLASH_POWER_DELAY_MS * 1000 * 1000 / 50) // 7ms / 50ns
#define	PMU_IP_RESET_DELAY										(65) // 65/20.85Mhz = 3.11us

#if PS5017_EN
#define	PMU_TRIM_VLD_RESET_DELAY_US								(100)
#define	M_PMU_CLR_TRIM_VLD()									(R32_SYS_PMU[R32_SYS0_PMU_MODE_CTL] &= ~(CR_REG_TRIM_VLD_BIT))
#define	M_PMU_SET_TRIM_VLD()									(R32_SYS_PMU[R32_SYS0_PMU_MODE_CTL] |= (CR_REG_TRIM_VLD_BIT))
#endif /* PS5017_EN */

#if PS5021_EN
#define	M_PMU_SET_IP_RESET(X)									(R32_SYS0_SYS_CTRL[R32_SYS0_RESET_CTRL] &= ~(X))
#define	M_PMU_CLR_IP_RESET(X)									(R32_SYS0_SYS_CTRL[R32_SYS0_RESET_CTRL] |= (X))
#define	M_PMU_SET_SYS_RESET_CTRL1(X)							(R32_SYS0_SYS_CTRL[R32_SYS0_RESET_CTRL1] |= (X))
#define	M_PMU_CLR_INT_STS(X)									do { \
																R16_SYS0_PMU_CTRL[R16_SYS0_PMU_CTRL_INT_STS] = (U16)(X); \
																IdlePC(30); \
																} while(0)
#define	M_PMU_CLR_PMU_INT_MASK()								do { \
																R16_SYS0_PMU_CTRL[R16_SYS0_PMU_CTRL_INT_MASK] &= ~(PMU_INT_ALL_MASK); \
																IdlePC(30); \
																} while(0)
#else /* PS5021_EN*/
#define	M_PMU_SET_IP_RESET(X)									(R32_SYS_PMU[R32_SYS0_PMU_RST_CTRL] &= ~(X))
#define	M_PMU_CLR_IP_RESET(X)									(R32_SYS_PMU[R32_SYS0_PMU_RST_CTRL] |= (X))
#endif /* PS5021_EN */

enum SYSTEM_PMU_LOWER_POWER_MODE {
	LPM_1 = 0,
	LPM_2,
	LPM_3,
	LPM_ITEM_CNT,
};

enum LPM_WAKE_UP_EVENT_SELECT {
	WAKE_UP_EVENT_RTT = 0,
	WAKE_UP_EVENT_GPIO_LOW,
	WAKE_UP_EVENT_GPIO_HIGH,
	WAKE_UP_EVENT_GPIO_FALLING_EDGE,
	WAKE_UP_EVENT_GPIO_RISING_EDGE,
	WAKE_UP_EVENT_CNT
};

typedef union {
	U8 ubAll;
	struct {
		U8 btFCFallingCheck		: 1;
		U8 btFCRisingCheck		: 1;
		U8 btFCCheckResult		: 1;
		U8 btFIOFallingCheck	: 1;
		U8 btFIORisingCheck 	: 1;
		U8 btFIOCheckResult		: 1;
		U8 Reserved : 2;
	} A;
} PMUPMICCheck_t;

extern volatile PMUPMICCheck_t gubPMUPMICCheckResult;

AOM_COMMON void PMUResetWholeChip(void);
AOM_LPM void PMUSetGPIOMux(U8 ubGPIO);
AOM_LPM void PMUEnterLPM(U32 ulLPM, U32 ulLowVoltageEn, U32 ulRCOn);
AOM_LPM void PMUEnterLPM3(U32 ulLowVoltageEn, U32 ulRCOn);
#if (!PS5017_EN)
AOM_LPM void DMACCopyForLPM3(U8 ubMode);
#endif /* (!PS5017_EN) */
AOM_LPM U8 CheckCopyResult(void);
AOM_LPM void PMUSwitchClockSrc(U8 ubModeSel);
void halPMU_Async_Wake_Mask_Init(void);
AOM_LPM void SystemResetCPUForDLMC(void);
AOM_LPM void SystemGatingClock(void);
#if (BURNER_MODE_EN)
AOM_INIT void PMULoadSwitchCheckTest(void);
AOM_INIT void PMUPMICCheckTest(void);
AOM_INIT void PMUSetIOPowerVoltage(U8 ulCH1Enable);
#endif /*(BURNER_MODE_EN)*/
AOM_COMMON void PMUShutDownFlashCorePower(void);
AOM_COMMON void PMUPowerOffFlashCorePower(void);
AOM_COMMON void PMUPowerOnFlashCorePower(void);
AOM_INIT_2 void PMUInit(void);
AOM_INIT void PMUInitVDT(void);

#endif /* _PMU_API_H_ */
