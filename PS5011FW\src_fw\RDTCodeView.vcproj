<?xml version="1.0" encoding="gb2312"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="9.00"
	Name="RDTCodeView"
	ProjectGUID="{C828518D-7FF3-4C38-8242-86448F3B2EAC}"
	RootNamespace="RDTCodeView"
	TargetFrameworkVersion="196613"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="Hynix_V7|Win32"
			OutputDirectory="$(SolutionDir)$(ConfigurationName)"
			IntermediateDirectory="$(ConfigurationName)"
			ConfigurationType="1"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories=".\\common;.\\"
				PreprocessorDefinitions="TRUE=1;FALSE=0;CATEGORY_CUSTOMER=CUSTOMER_MAINSTREAM;CATEGORY_FLASH=FLASH_V7_TLC;_HOST_MODE_=USB;_FW_BUILD_VERSION_=FW_VERSION_FULL_MAX_XZIP_OFF;_TCG_MODE_=TCG_MODE_NOT_SUPPORT;_RELEASED_FW_=0;_OPEN_BLOCK_RSMAP_EN_=0;_PARTIAL_BLOCK_MEDIA_SCAN_EN_=0;_RETRY_MICRON_NICKS_ALL_PAGE_TYPE_SB_SUPPORT_=0;_RETRY_MICRON_NICKS_=0;_RANDOMIZER_3D_AGITATION_RULE_=TRUE;_D1_UNIT_EN_=TRUE;CATEGORY_CONTROLLER=CONTROLLER_PS5017;_LUN_FOR_MORE_UNIT_EN_=TRUE;_BUILD_MODE_=BUILD_FW;_RDT_=1;_RDT_BURNER_=0"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				WarningLevel="3"
				DebugInformationFormat="4"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				GenerateDebugInformation="true"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="YMTC_TAS|Win32"
			OutputDirectory="$(SolutionDir)$(ConfigurationName)"
			IntermediateDirectory="$(ConfigurationName)"
			ConfigurationType="1"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories=".\\common;.\\"
				PreprocessorDefinitions="TRUE=1;FALSE=0;CATEGORY_CUSTOMER=CUSTOMER_MAINSTREAM;CATEGORY_FLASH=FLASH_YMTC_TAS_TLC;_HOST_MODE_=USB;_FW_BUILD_VERSION_=FW_VERSION_FULL_MAX_XZIP_OFF;_TCG_MODE_=TCG_MODE_NOT_SUPPORT;_RELEASED_FW_=0;_OPEN_BLOCK_RSMAP_EN_=0;_PARTIAL_BLOCK_MEDIA_SCAN_EN_=0;_RETRY_MICRON_NICKS_ALL_PAGE_TYPE_SB_SUPPORT_=0;_RETRY_MICRON_NICKS_=0;_RANDOMIZER_3D_AGITATION_RULE_=TRUE;_D1_UNIT_EN_=TRUE;CATEGORY_CONTROLLER=CONTROLLER_PS5017;_LUN_FOR_MORE_UNIT_EN_=TRUE;_BUILD_MODE_=BUILD_FW;_RDT_=1;_RDT_BURNER_=0"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				WarningLevel="3"
				DebugInformationFormat="4"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				GenerateDebugInformation="true"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Hynix_V7_QLC|Win32"
			OutputDirectory="$(SolutionDir)$(ConfigurationName)"
			IntermediateDirectory="$(ConfigurationName)"
			ConfigurationType="1"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories=".\\common;.\\"
				PreprocessorDefinitions="TRUE=1;FALSE=0;CATEGORY_CUSTOMER=CUSTOMER_MAINSTREAM;CATEGORY_FLASH=FLASH_V7_QLC;_HOST_MODE_=USB;_FW_BUILD_VERSION_=FW_VERSION_FULL_MAX_XZIP_OFF;_TCG_MODE_=TCG_MODE_NOT_SUPPORT;_RELEASED_FW_=0;_OPEN_BLOCK_RSMAP_EN_=0;_PARTIAL_BLOCK_MEDIA_SCAN_EN_=0;_RETRY_MICRON_NICKS_ALL_PAGE_TYPE_SB_SUPPORT_=0;_RETRY_MICRON_NICKS_=0;_RANDOMIZER_3D_AGITATION_RULE_=TRUE;_D1_UNIT_EN_=TRUE;CATEGORY_CONTROLLER=CONTROLLER_PS5017;_LUN_FOR_MORE_UNIT_EN_=TRUE;_BUILD_MODE_=BUILD_FW;_RDT_=1;_RDT_BURNER_=0"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				WarningLevel="3"
				DebugInformationFormat="4"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				GenerateDebugInformation="true"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="YMTC_EMS|Win32"
			OutputDirectory="$(SolutionDir)$(ConfigurationName)"
			IntermediateDirectory="$(ConfigurationName)"
			ConfigurationType="1"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories=".\\common;.\\"
				PreprocessorDefinitions="TRUE=1;FALSE=0;CATEGORY_CUSTOMER=CUSTOMER_MAINSTREAM;CATEGORY_FLASH=FLASH_YMTC_EMS_QLC;_HOST_MODE_=USB;_FW_BUILD_VERSION_=FW_VERSION_FULL_MAX_XZIP_OFF;_TCG_MODE_=TCG_MODE_NOT_SUPPORT;_RELEASED_FW_=0;_OPEN_BLOCK_RSMAP_EN_=0;_PARTIAL_BLOCK_MEDIA_SCAN_EN_=0;_RETRY_MICRON_NICKS_ALL_PAGE_TYPE_SB_SUPPORT_=0;_RETRY_MICRON_NICKS_=0;_RANDOMIZER_3D_AGITATION_RULE_=TRUE;_D1_UNIT_EN_=TRUE;CATEGORY_CONTROLLER=CONTROLLER_PS5017;_LUN_FOR_MORE_UNIT_EN_=TRUE;_BUILD_MODE_=BUILD_FW;_RDT_=1;_RDT_BURNER_=0"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				WarningLevel="3"
				DebugInformationFormat="4"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				GenerateDebugInformation="true"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="BICS5_TLC|Win32"
			OutputDirectory="$(SolutionDir)$(ConfigurationName)"
			IntermediateDirectory="$(ConfigurationName)"
			ConfigurationType="1"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories=".\\common;.\\"
				PreprocessorDefinitions="TRUE=1;FALSE=0;CATEGORY_CUSTOMER=CUSTOMER_MAINSTREAM;CATEGORY_FLASH=FLASH_SANDISK_BICS5_TLC;_HOST_MODE_=USB;_FW_BUILD_VERSION_=FW_VERSION_FULL_MAX_XZIP_OFF;_TCG_MODE_=TCG_MODE_NOT_SUPPORT;_RELEASED_FW_=0;_OPEN_BLOCK_RSMAP_EN_=0;_PARTIAL_BLOCK_MEDIA_SCAN_EN_=0;_RETRY_MICRON_NICKS_ALL_PAGE_TYPE_SB_SUPPORT_=0;_RETRY_MICRON_NICKS_=0;_RANDOMIZER_3D_AGITATION_RULE_=TRUE;_D1_UNIT_EN_=TRUE;CATEGORY_CONTROLLER=CONTROLLER_PS5017;_LUN_FOR_MORE_UNIT_EN_=TRUE;_BUILD_MODE_=BUILD_FW;_RDT_=1;_RDT_BURNER_=0"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				WarningLevel="3"
				DebugInformationFormat="4"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				GenerateDebugInformation="true"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="BICS6_TLC|Win32"
			OutputDirectory="$(SolutionDir)$(ConfigurationName)"
			IntermediateDirectory="$(ConfigurationName)"
			ConfigurationType="1"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories=".\\common;.\\"
				PreprocessorDefinitions="TRUE=1;FALSE=0;CATEGORY_CUSTOMER=CUSTOMER_MAINSTREAM;CATEGORY_FLASH=FLASH_SANDISK_BICS6_TLC;_HOST_MODE_=USB;_FW_BUILD_VERSION_=FW_VERSION_FULL_MAX_XZIP_OFF;_TCG_MODE_=TCG_MODE_NOT_SUPPORT;_RELEASED_FW_=0;_OPEN_BLOCK_RSMAP_EN_=0;_PARTIAL_BLOCK_MEDIA_SCAN_EN_=0;_RETRY_MICRON_NICKS_ALL_PAGE_TYPE_SB_SUPPORT_=0;_RETRY_MICRON_NICKS_=0;_RANDOMIZER_3D_AGITATION_RULE_=TRUE;_D1_UNIT_EN_=TRUE;CATEGORY_CONTROLLER=CONTROLLER_PS5017;_LUN_FOR_MORE_UNIT_EN_=TRUE;_BUILD_MODE_=BUILD_FW;_RDT_=1;_RDT_BURNER_=0"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				WarningLevel="3"
				DebugInformationFormat="4"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				GenerateDebugInformation="true"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Micron_N48R_QLC|Win32"
			OutputDirectory="$(SolutionDir)$(ConfigurationName)"
			IntermediateDirectory="$(ConfigurationName)"
			ConfigurationType="1"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories=".\\common;.\\"
				PreprocessorDefinitions="TRUE=1;FALSE=0;CATEGORY_CUSTOMER=CUSTOMER_MAINSTREAM;CATEGORY_FLASH=FLASH_N48R_QLC;_HOST_MODE_=USB;_FW_BUILD_VERSION_=FW_VERSION_FULL_MAX_XZIP_OFF;_TCG_MODE_=TCG_MODE_NOT_SUPPORT;_RELEASED_FW_=0;_OPEN_BLOCK_RSMAP_EN_=0;_PARTIAL_BLOCK_MEDIA_SCAN_EN_=0;_RETRY_MICRON_NICKS_ALL_PAGE_TYPE_SB_SUPPORT_=0;_RETRY_MICRON_NICKS_=0;_RANDOMIZER_3D_AGITATION_RULE_=TRUE;_D1_UNIT_EN_=TRUE;CATEGORY_CONTROLLER=CONTROLLER_PS5017;_LUN_FOR_MORE_UNIT_EN_=TRUE;_BUILD_MODE_=BUILD_FW;_RDT_=1;_RDT_BURNER_=0"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				WarningLevel="3"
				DebugInformationFormat="4"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				GenerateDebugInformation="true"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="YMTC_WTS|Win32"
			OutputDirectory="$(SolutionDir)$(ConfigurationName)"
			IntermediateDirectory="$(ConfigurationName)"
			ConfigurationType="1"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories=".\\common;.\\"
				PreprocessorDefinitions="TRUE=1;FALSE=0;CATEGORY_CUSTOMER=CUSTOMER_MAINSTREAM;CATEGORY_FLASH=FLASH_YMTC_WTS_TLC;_HOST_MODE_=USB;_FW_BUILD_VERSION_=FW_VERSION_FULL_MAX_XZIP_OFF;_TCG_MODE_=TCG_MODE_NOT_SUPPORT;_RELEASED_FW_=0;_OPEN_BLOCK_RSMAP_EN_=0;_PARTIAL_BLOCK_MEDIA_SCAN_EN_=0;_RETRY_MICRON_NICKS_ALL_PAGE_TYPE_SB_SUPPORT_=0;_RETRY_MICRON_NICKS_=0;_RANDOMIZER_3D_AGITATION_RULE_=TRUE;_D1_UNIT_EN_=TRUE;CATEGORY_CONTROLLER=CONTROLLER_PS5017;_LUN_FOR_MORE_UNIT_EN_=TRUE;_BUILD_MODE_=BUILD_FW;_RDT_=1;_RDT_BURNER_=0"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				WarningLevel="3"
				DebugInformationFormat="4"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				GenerateDebugInformation="true"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="BICS6_QLC|Win32"
			OutputDirectory="$(SolutionDir)$(ConfigurationName)"
			IntermediateDirectory="$(ConfigurationName)"
			ConfigurationType="1"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories=".\\common;.\\"
				PreprocessorDefinitions="TRUE=1;FALSE=0;CATEGORY_CUSTOMER=CUSTOMER_MAINSTREAM;CATEGORY_FLASH=FLASH_SANDISK_BICS6_QLC;_HOST_MODE_=USB;_FW_BUILD_VERSION_=FW_VERSION_FULL_MAX_XZIP_OFF;_TCG_MODE_=TCG_MODE_NOT_SUPPORT;_RELEASED_FW_=0;_OPEN_BLOCK_RSMAP_EN_=0;_PARTIAL_BLOCK_MEDIA_SCAN_EN_=0;_RETRY_MICRON_NICKS_ALL_PAGE_TYPE_SB_SUPPORT_=0;_RETRY_MICRON_NICKS_=0;_RANDOMIZER_3D_AGITATION_RULE_=TRUE;_D1_UNIT_EN_=TRUE;CATEGORY_CONTROLLER=CONTROLLER_PS5017;_LUN_FOR_MORE_UNIT_EN_=TRUE;_BUILD_MODE_=BUILD_FW;_RDT_=1;_RDT_BURNER_=0"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				WarningLevel="3"
				DebugInformationFormat="4"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				GenerateDebugInformation="true"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="BICS8_TLC|Win32"
			OutputDirectory="$(SolutionDir)$(ConfigurationName)"
			IntermediateDirectory="$(ConfigurationName)"
			ConfigurationType="1"
			CharacterSet="2"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories=".\\common;.\\"
				PreprocessorDefinitions="TRUE=1;FALSE=0;CATEGORY_CUSTOMER=CUSTOMER_MAINSTREAM;CATEGORY_FLASH=FLASH_SANDISK_BICS8_TLC;_HOST_MODE_=USB;_FW_BUILD_VERSION_=FW_VERSION_FULL_MAX_XZIP_OFF;_TCG_MODE_=TCG_MODE_NOT_SUPPORT;_RELEASED_FW_=0;_OPEN_BLOCK_RSMAP_EN_=0;_PARTIAL_BLOCK_MEDIA_SCAN_EN_=0;_RETRY_MICRON_NICKS_ALL_PAGE_TYPE_SB_SUPPORT_=0;_RETRY_MICRON_NICKS_=0;_RANDOMIZER_3D_AGITATION_RULE_=TRUE;_D1_UNIT_EN_=TRUE;CATEGORY_CONTROLLER=CONTROLLER_PS5017;_LUN_FOR_MORE_UNIT_EN_=TRUE;_BUILD_MODE_=BUILD_FW;_RDT_=1;_RDT_BURNER_=0"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				WarningLevel="3"
				DebugInformationFormat="4"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				GenerateDebugInformation="true"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx"
			UniqueIdentifier="{4FC737F1-C7A5-4376-A066-2A32D752A2FF}"
			>
			<File
				RelativePath=".\hal\sys\api\angc\angc_api.c"
				>
			</File>
			<File
				RelativePath=".\aom\aom.c"
				>
			</File>
			<File
				RelativePath=".\hal\apu\apu.c"
				>
			</File>
			<File
				RelativePath=".\hal\bmu\bmu_api.c"
				>
			</File>
			<File
				RelativePath=".\hal\bmu\bmu_init.c"
				>
			</File>
			<File
				RelativePath=".\hal\bmu\bmu_pop_cmd.c"
				>
			</File>
			<File
				RelativePath=".\zzz_booking_overlay\booking_overlay.c"
				>
			</File>
			<File
				RelativePath=".\bootloader\bootloader.c"
				>
			</File>
			<File
				RelativePath=".\buffer\buffer.c"
				>
			</File>
			<File
				RelativePath=".\queue\buffer_queue.c"
				>
			</File>
			<File
				RelativePath=".\burner\Burner.c"
				>
			</File>
			<File
				RelativePath=".\burner\Burner_Test_Code.c"
				>
			</File>
			<File
				RelativePath=".\hal\sys\api\clk\clk.c"
				>
			</File>
			<File
				RelativePath=".\hal\cop0\cop0.c"
				>
			</File>
			<File
				RelativePath=".\hal\cop0\cop0_api.c"
				>
			</File>
			<File
				RelativePath=".\hal\cop0\cop0_pop_cmd.c"
				>
			</File>
			<File
				RelativePath=".\hal\cop0\cop0_tiein.c"
				>
			</File>
			<File
				RelativePath=".\hal\cop0\cop0_tieout.c"
				>
			</File>
			<File
				RelativePath=".\hal\cop1\cop1.c"
				>
			</File>
			<File
				RelativePath=".\hal\cop1\cop1_pop_cmd.c"
				>
			</File>
			<File
				RelativePath=".\host\cop1_st1.c"
				>
			</File>
			<File
				RelativePath=".\copy_unit\copy_unit.c"
				>
			</File>
			<File
				RelativePath=".\hal\db\db.c"
				>
			</File>
			<File
				RelativePath=".\table\dbt\dbt.c"
				>
			</File>
			<File
				RelativePath=".\debug\debug.c"
				>
			</File>
			<File
				RelativePath=".\hal\dmac\dmac.c"
				>
			</File>
			<File
				RelativePath=".\hal\dmac\dmac_init.c"
				>
			</File>
			<File
				RelativePath=".\hal\dmac\dmac_pop_cmd.c"
				>
			</File>
			<File
				RelativePath=".\drive_log\drive_log.c"
				>
			</File>
			<File
				RelativePath=".\hal\sys\api\efuc\efuse.c"
				>
			</File>
			<File
				RelativePath=".\err_handle\err_handle.c"
				>
			</File>
			<File
				RelativePath=".\err_handle\err_handle_copyblock.c"
				>
			</File>
			<File
				RelativePath=".\err_handle\err_handle_program.c"
				>
			</File>
			<File
				RelativePath=".\err_handle\err_handle_vt_child.c"
				>
			</File>
			<File
				RelativePath=".\retry\err_hdl_fpl_RS.c"
				>
			</File>
			<File
				RelativePath=".\retry\err_hdl_fpl_softbit_retry.c"
				>
			</File>
			<File
				RelativePath=".\retry\err_hdl_fpl_softbit_retry_table.c"
				>
			</File>
			<File
				RelativePath=".\retry\err_hdl_fpl_SoftbitRaid.c"
				>
			</File>
			<File
				RelativePath=".\retry\err_hdl_fpl_TurboRain.c"
				>
			</File>
			<File
				RelativePath=".\err_handle\err_log.c"
				>
			</File>
			<File
				RelativePath=".\err_handle\error_handle_code.c"
				>
			</File>
			<File
				RelativePath=".\cpu\error_monitor.c"
				>
			</File>
			<File
				RelativePath=".\hal\fip\fip.c"
				>
			</File>
			<File
				RelativePath=".\hal\fip\fip_hynix_cmd.c"
				>
			</File>
			<File
				RelativePath=".\hal\fip\fip_samsung_cmd.c"
				>
			</File>
			<File
				RelativePath=".\hal\fip\fip_toshiba_cmd.c"
				>
			</File>
			<File
				RelativePath=".\hal\fip\fpu.c"
				>
			</File>
			<File
				RelativePath=".\hal\fip\fpu_hynix_qlc.c"
				>
			</File>
			<File
				RelativePath=".\hal\fip\fpu_hynix_tlc.c"
				>
			</File>
			<File
				RelativePath=".\hal\fip\fpu_micron_b47r_5017.c"
				>
			</File>
			<File
				RelativePath=".\hal\fip\fpu_micron_b47r_5021.c"
				>
			</File>
			<File
				RelativePath=".\hal\fip\fpu_micron_qlc.c"
				>
			</File>
			<File
				RelativePath=".\hal\fip\fpu_micron_tlc.c"
				>
			</File>
			<File
				RelativePath=".\hal\fip\fpu_samsung_tlc.c"
				>
			</File>
			<File
				RelativePath=".\hal\fip\fpu_sandisk_BICS8_tlc.c"
				>
			</File>
			<File
				RelativePath=".\hal\fip\fpu_sandisk_qlc.c"
				>
			</File>
			<File
				RelativePath=".\hal\fip\fpu_sandisk_tlc.c"
				>
			</File>
			<File
				RelativePath=".\hal\fip\fpu_tsb_qlc.c"
				>
			</File>
			<File
				RelativePath=".\hal\fip\fpu_tsb_tlc.c"
				>
			</File>
			<File
				RelativePath=".\hal\fip\fpu_ymtc_qlc.c"
				>
			</File>
			<File
				RelativePath=".\hal\fip\fpu_ymtc_tlc.c"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl.c"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_api.c"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_background.c"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_barrier.c"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_bfea.c"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_common_lib.c"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_erase.c"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_gc.c"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_gc_xzip.c"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_ior.c"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_journal.c"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_load_alignment.c"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_loaddata.c"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_nrw.c"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_preread.c"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_readverify.c"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_seq_table.c"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_sustain.c"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_sync_cmd_handler.c"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_table.c"
				>
			</File>
			<File
				RelativePath=".\trim\ftl_trim.c"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_xcut.c"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_xfer_data_in.c"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_xfer_data_out.c"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_xzip.c"
				>
			</File>
			<File
				RelativePath=".\db_mgr\fw_cmd_table.c"
				>
			</File>
			<File
				RelativePath=".\common\fw_common.c"
				>
			</File>
			<File
				RelativePath=".\init\fw_init.c"
				>
			</File>
			<File
				RelativePath=".\fw_mgr\fw_mgr.c"
				>
			</File>
			<File
				RelativePath=".\init\fw_preformat.c"
				>
			</File>
			<File
				RelativePath=".\db_mgr\fw_tagid.c"
				>
			</File>
			<File
				RelativePath=".\nsrmp\hal_nsrmp.c"
				>
			</File>
			<File
				RelativePath=".\hmb\hmb.c"
				>
			</File>
			<File
				RelativePath=".\host\host.c"
				>
			</File>
			<File
				RelativePath=".\host_handler\host_management.c"
				>
			</File>
			<File
				RelativePath=".\host_handler\hostevt_errhdl.c"
				>
			</File>
			<File
				RelativePath=".\hal\pic\i2c\i2cm.c"
				>
			</File>
			<File
				RelativePath=".\hal\pic\i2c\i2cm_device.c"
				>
			</File>
			<File
				RelativePath=".\table\initinfo_vt\initinfo_vt.c"
				>
			</File>
			<File
				RelativePath=".\cpu\isr_handler.c"
				>
			</File>
			<File
				RelativePath=".\cpu\isr_handler_5013.c"
				>
			</File>
			<File
				RelativePath=".\hal\sys\api\itc\itc.c"
				>
			</File>
			<File
				RelativePath=".\hal\sys\api\led\led.c"
				>
			</File>
			<File
				RelativePath=".\log\log.c"
				>
			</File>
			<File
				RelativePath=".\lpm\lpm.c"
				>
			</File>
			<File
				RelativePath=".\lpm\lpm3loader.c"
				>
			</File>
			<File
				RelativePath=".\main.c"
				>
			</File>
			<File
				RelativePath=".\media_scan\media_scan.c"
				>
			</File>
			<File
				RelativePath=".\hal\mr\mr.c"
				>
			</File>
			<File
				RelativePath=".\hal\sys\api\mux\mux.c"
				>
			</File>
			<File
				RelativePath=".\init\NewScanFlow.c"
				>
			</File>
			<File
				RelativePath=".\nsrmp\nsrmp_api.c"
				>
			</File>
			<File
				RelativePath=".\hal\nvme\nvme.c"
				>
			</File>
			<File
				RelativePath=".\nvme_api\nvme_ata_security\nvme_ata_security.c"
				>
			</File>
			<File
				RelativePath=".\rs\open_block_rsmap.c"
				>
			</File>
			<File
				RelativePath=".\overlay\overlay\ovl_alloc.c"
				>
			</File>
			<File
				RelativePath=".\overlay\overlay\ovl_manager.c"
				>
			</File>
			<File
				RelativePath=".\overlay\overlay\ovl_notify.c"
				>
			</File>
			<File
				RelativePath=".\overlay\overlay\ovl_perthread.c"
				>
			</File>
			<File
				RelativePath=".\retry\patch_cmd.c"
				>
			</File>
			<File
				RelativePath=".\hal\sys\api\pmu\pmu.c"
				>
			</File>
			<File
				RelativePath=".\hal\raidecc\raidecc.c"
				>
			</File>
			<File
				RelativePath=".\raideccmap\raideccmap.c"
				>
			</File>
			<File
				RelativePath=".\raideccmap\raideccmap_for_B27B_N28.c"
				>
			</File>
			<File
				RelativePath=".\raideccmap\raideccmap_for_BICS_TLC.c"
				>
			</File>
			<File
				RelativePath=".\raideccmap\raideccmap_for_N48R.c"
				>
			</File>
			<File
				RelativePath=".\raideccmap\raideccmap_for_seq_table.c"
				>
			</File>
			<File
				RelativePath=".\raideccmap\raideccmap_lib.c"
				>
			</File>
			<File
				RelativePath=".\raideccmap\raideccmap_paritymap.c"
				>
			</File>
			<File
				RelativePath=".\raideccmap\raideccmap_swap.c"
				>
			</File>
			<File
				RelativePath=".\raideccmap\raideccmap_tagidx_table.c"
				>
			</File>
			<File
				RelativePath=".\ramdisk\ramdisk.c"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_api.c"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_atcm_sram_test.c"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_bmu_alloc_free_test.c"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_bmu_test.c"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_cop0_api.c"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_cop1_test.c"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_csch_test.c"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_dmac.c"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_dmac_sort_test.c"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_fip_instruction_fetching_test.c"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_fip_test.c"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_flash_program_order.c"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_flash_rdy_time_test.c"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_flash_retry_cmd_verify.c"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_fphy_loopback_test.c"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_general_flash_test.c"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_ic_pattern_test.c"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_init.c"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_ldpc_test.c"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_log.c"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_lpm_test.c"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_main.c"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_main_Online.c"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_pio_force_wr_log.c"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_sec_test.c"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_security_self_test.c"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_sram_test.c"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_sram_zip_test.c"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_temperature.c"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_xzip_test.c"
				>
			</File>
			<File
				RelativePath=".\read_disturb\read_disturb.c"
				>
			</File>
			<File
				RelativePath=".\read_disturb\read_disturb_PRDH.c"
				>
			</File>
			<File
				RelativePath=".\queue\read_queue.c"
				>
			</File>
			<File
				RelativePath=".\retry\read_retry.c"
				>
			</File>
			<File
				RelativePath=".\retry\retry_hb.c"
				>
			</File>
			<File
				RelativePath=".\retry\retry_hynix_v6_tlc\retry_hynix_v6_tlc_E13_U17_neutral_hb.c"
				>
			</File>
			<File
				RelativePath=".\retry\retry_hynix_v7_qlc\retry_hynix_v7_qlc_E13_U17_neutral_hb.c"
				>
			</File>
			<File
				RelativePath=".\retry\retry_kioxia_bics4hdr_tlc\retry_kioxia_bics4hdr_tlc_S17_neutral_hb.c"
				>
			</File>
			<File
				RelativePath=".\retry\retry_kioxia_bics5_tlc\retry_kioxia_bics5_tlc_S17_neutral_hb.c"
				>
			</File>
			<File
				RelativePath=".\retry\retry_kioxia_bics5_tlc\retry_kioxia_bics5_tlc_S17_neutral_sb.c"
				>
			</File>
			<File
				RelativePath=".\retry\retry_micron_b47r_tlc\retry_micron_b47r_tlc_E13_neutral_hb.c"
				>
			</File>
			<File
				RelativePath=".\retry\retry_micron_b47r_tlc\retry_micron_b47r_tlc_E13_S17_nicks_hb.c"
				>
			</File>
			<File
				RelativePath=".\retry\retry_micron_b47r_tlc\retry_micron_b47r_tlc_E13_U17_neutral_hb.c"
				>
			</File>
			<File
				RelativePath=".\retry\retry_micron_n48r_qlc\retry_micron_n48r_qlc_E13_nicks_U17_frey2_hb.c"
				>
			</File>
			<File
				RelativePath=".\retry\retry_micron_n48r_qlc\retry_micron_n48r_qlc_E13_U17_neutral_hb.c"
				>
			</File>
			<File
				RelativePath=".\retry\retry_micron_qlc_softbit_retry.c"
				>
			</File>
			<File
				RelativePath=".\retry\retry_micron_qlc_softbit_retry_all_page_type_N48R.c"
				>
			</File>
			<File
				RelativePath=".\retry\retry_micron_tlc_softbit_retry.c"
				>
			</File>
			<File
				RelativePath=".\retry\retry_micron_tlc_softbit_retry_all_page_type.c"
				>
			</File>
			<File
				RelativePath=".\retry\retry_micron_tlc_softbit_retry_all_page_type_B47R.c"
				>
			</File>
			<File
				RelativePath=".\retry\retry_Samsung_v6_tlc\retry_samsung_v6_tlc_E13_U17_neutral_hb.c"
				>
			</File>
			<File
				RelativePath=".\retry\retry_sandisk_bics5_tlc\retry_sandisk_bics5_tlc_E13_U17_neutral_hb.c"
				>
			</File>
			<File
				RelativePath=".\retry\retry_sandisk_bics6_qlc\retry_sandisk_bics6_qlc_E13_U17_neutral_hb.c"
				>
			</File>
			<File
				RelativePath=".\retry\retry_sandisk_bics8_tlc\retry_sandisk_bics8_tlc_E13_U17_neutral_hb.c"
				>
			</File>
			<File
				RelativePath=".\retry\retry_trim_table.c"
				>
			</File>
			<File
				RelativePath=".\retry\retry_tsb_qlc_softbit_retry.c"
				>
			</File>
			<File
				RelativePath=".\retry\retry_tsb_tlc_softbit_retry.c"
				>
			</File>
			<File
				RelativePath=".\retry\retry_ymtc_ems_qlc\retry_ymtc_ems_qlc_E13_U17_neutral_hb.c"
				>
			</File>
			<File
				RelativePath=".\retry\retry_ymtc_tas_tlc\retry_ymtc_tas_tlc_E13_U17_neutral_hb.c"
				>
			</File>
			<File
				RelativePath=".\retry\retry_ymtc_wts_tlc\retry_ymtc_wts_tlc_E13_U17_neutral_hb.c"
				>
			</File>
			<File
				RelativePath=".\err_handle\rma_log.c"
				>
			</File>
			<File
				RelativePath=".\hal\sys\api\rng\rng.c"
				>
			</File>
			<File
				RelativePath=".\hal\sys\api\rram\rram.c"
				>
			</File>
			<File
				RelativePath=".\hal\rs\rs.c"
				>
			</File>
			<File
				RelativePath=".\rs\rsmap.c"
				>
			</File>
			<File
				RelativePath=".\hal\sys\api\rtt\rtt.c"
				>
			</File>
			<File
				RelativePath=".\table\rut\rut.c"
				>
			</File>
			<File
				RelativePath=".\host_handler\rw_mgr.c"
				>
			</File>
			<File
				RelativePath=".\hal\sata\sata.c"
				>
			</File>
			<File
				RelativePath=".\hal\sata\sata_cmd.c"
				>
			</File>
			<File
				RelativePath=".\hal\sata\sata_cmd_tbl.c"
				>
			</File>
			<File
				RelativePath=".\hal\sata\sata_dco.c"
				>
			</File>
			<File
				RelativePath=".\hal\sata\sata_dlmc.c"
				>
			</File>
			<File
				RelativePath=".\hal\sata\sata_hpa.c"
				>
			</File>
			<File
				RelativePath=".\hal\sata\sata_sanitize.c"
				>
			</File>
			<File
				RelativePath=".\hal\sata\sata_security.c"
				>
			</File>
			<File
				RelativePath=".\hal\sata\sata_smart.c"
				>
			</File>
			<File
				RelativePath=".\hal\sata\sata_vendor_cmd.c"
				>
			</File>
			<File
				RelativePath=".\hal\sata\sata_vuc.c"
				>
			</File>
			<File
				RelativePath=".\hal\security\security.c"
				>
			</File>
			<File
				RelativePath=".\hal\security\security_rsa.c"
				>
			</File>
			<File
				RelativePath=".\nvme_api\d2h\Shr_hal_d2h.c"
				>
			</File>
			<File
				RelativePath=".\nvme_api\nvme\shr_hal_nvme.c"
				>
			</File>
			<File
				RelativePath=".\nvme_api\nvme\shr_hal_nvme_api.c"
				>
			</File>
			<File
				RelativePath=".\nvme_api\nvme\shr_hal_nvme_feat_api.c"
				>
			</File>
			<File
				RelativePath=".\nvme_api\nvme\shr_hal_nvme_io_api.c"
				>
			</File>
			<File
				RelativePath=".\nvme_api\nvme\shr_hal_nvme_log_api.c"
				>
			</File>
			<File
				RelativePath=".\nvme_api\nvme\shr_hal_nvme_persistent_log_api.c"
				>
			</File>
			<File
				RelativePath=".\nvme_api\nvme\shr_hal_nvme_vuc_api.c"
				>
			</File>
			<File
				RelativePath=".\nvme_api\pcie\shr_hal_pcie.c"
				>
			</File>
			<File
				RelativePath=".\hal\pic\uart\shr_hal_pic_uartxfer.c"
				>
			</File>
			<File
				RelativePath=".\testflow\sim_spor\sim_spor.c"
				>
			</File>
			<File
				RelativePath=".\hal\spi\spi.c"
				>
			</File>
			<File
				RelativePath=".\hal\cop1\st3c\st3c_pop_cmd.c"
				>
			</File>
			<File
				RelativePath=".\retry\stall.c"
				>
			</File>
			<File
				RelativePath=".\table\sys_area\sys_area.c"
				>
			</File>
			<File
				RelativePath=".\table\sys_block\sys_block.c"
				>
			</File>
			<File
				RelativePath=".\init\system_init.c"
				>
			</File>
			<File
				RelativePath=".\tcg\tcg.c"
				>
			</File>
			<File
				RelativePath=".\tcg\tcg_api.c"
				>
			</File>
			<File
				RelativePath=".\tcg\tcg_drbg.c"
				>
			</File>
			<File
				RelativePath=".\tcg\tcg_init.c"
				>
			</File>
			<File
				RelativePath=".\tcg\tcg_key.c"
				>
			</File>
			<File
				RelativePath=".\tcg\tcg_pbkdf2.c"
				>
			</File>
			<File
				RelativePath=".\VRLC\tempco.c"
				>
			</File>
			<File
				RelativePath=".\testflow\test_xzip.c"
				>
			</File>
			<File
				RelativePath=".\tt\TT.c"
				>
			</File>
			<File
				RelativePath=".\hal\pic\uart\uart.c"
				>
			</File>
			<File
				RelativePath=".\hal\usb\usb.c"
				>
			</File>
			<File
				RelativePath=".\hal\usb\usb_cmd.c"
				>
			</File>
			<File
				RelativePath=".\hal\usb\usb_cmd_para.c"
				>
			</File>
			<File
				RelativePath=".\media_scan\valley_health_check.c"
				>
			</File>
			<File
				RelativePath=".\table\vbmap\vbmap.c"
				>
			</File>
			<File
				RelativePath=".\VRLC\VRLC.c"
				>
			</File>
			<File
				RelativePath=".\vt_sweep\vt_sweep.c"
				>
			</File>
			<File
				RelativePath=".\vuc\Vth_Parsing.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_ApKey.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_CacheRead.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_CacheWrite.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_CutEFuse.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_DetectPeripheral.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_DetectTxRx.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_DirectReadInfo.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_DisableFrozenLock.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_DumpSysTable.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_DumpTable.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_EraseAll.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_EraseFlash.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_FwFeatureControl.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_GetEraseCount.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_GetFlashInfo.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_GetRDTLog.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_GetRMAInfo.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_GetStatus.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_GetVucDataSize.c"
				>
			</File>
			<File
				RelativePath=".\host\VUC_handler.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_ISPFlash.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_ISPJump.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_ISPReadFlash.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_ISPReadPRAM.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_ISPReadROM.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_ISPRom.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_KingstonAntiFakeData.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_KingstonVerifyAntiFake.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_ListBadBlock.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_MakeError.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_MicronClearEventLog.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_MicronGetBEC.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_MicronGetBlkNandMode.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_MicronGetDefectList.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_MicronGetDriveConfigData.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_MicronGetEraseCount.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_MicronGetMLBi.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_MicronGetNandErrorRecoveryStatistics.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_MicronGetRawBitErrCnt.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_MicronGetTemperature.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_MicronGetUnifiedEventLog.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_MicronGetVTSweep.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_MicronLogicaltoPhysical.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_MicronNandPass.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_MicronPhysicaltoLogicalAddress.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_MicronResponse.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_MicronSetMLBi.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_NandVerifyRead.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_NandVerifyTrigger.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_NandVerifyWrite.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_PCATranslate.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_PCIEEyeInfo.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_ProgramFlash.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_Protect.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_ReadFlash.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_ReadPH.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_ReadReg.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_ReadScanFlashWindow.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_ReadSram.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_ReadSysInfo.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_ScanFlashWindowParameter.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_ScanFlashWindowSetting.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_ScanPH.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_ScanRDTLog.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_SearchSysBlock.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_SecurityPassThrough.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_SetFlashFeature.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_SetPSID.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_SetSpecificGlobalValue.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_SideBandIOTest.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_SMART.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_SmartRescue.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_UnlockJTAG.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_Utilities.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_Write_VRLC.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_WriteInfo.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_WritePH.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_WriteReadAPDBT.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_WriteReg.c"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_WriteSram.c"
				>
			</File>
			<File
				RelativePath=".\hal\sys\api\wdt\wdt.c"
				>
			</File>
			<File
				RelativePath=".\wl\wl.c"
				>
			</File>
			<File
				RelativePath=".\WordLineFolding\WordLineFolding.c"
				>
			</File>
			<File
				RelativePath=".\host\Xmodem.c"
				>
			</File>
			<File
				RelativePath=".\hal\xzip\xzip.c"
				>
			</File>
			<File
				RelativePath=".\hal\zip\zip.c"
				>
			</File>
		</Filter>
		<Filter
			Name="Header Files"
			Filter="h;hpp;hxx;hm;inl;inc;xsd"
			UniqueIdentifier="{93995380-89BD-4b04-88EB-625FBE52EBFB}"
			>
			<File
				RelativePath=".\hal\sys\api\angc\angc_api.h"
				>
			</File>
			<File
				RelativePath=".\aom\aom.h"
				>
			</File>
			<File
				RelativePath=".\aom\aom_api.h"
				>
			</File>
			<File
				RelativePath=".\hal\apu\apu.h"
				>
			</File>
			<File
				RelativePath=".\hal\apu\apu_api.h"
				>
			</File>
			<File
				RelativePath=".\hal\apu\apu_reg.h"
				>
			</File>
			<File
				RelativePath=".\hal\apu\apu_reg_5013.h"
				>
			</File>
			<File
				RelativePath=".\hal\apu\apu_reg_5017.h"
				>
			</File>
			<File
				RelativePath=".\hal\apu\apu_reg_5021.h"
				>
			</File>
			<File
				RelativePath=".\cpu\arm.h"
				>
			</File>
			<File
				RelativePath=".\hal\bmu\bmu_api.h"
				>
			</File>
			<File
				RelativePath=".\hal\bmu\bmu_cmd_type.h"
				>
			</File>
			<File
				RelativePath=".\hal\bmu\bmu_init.h"
				>
			</File>
			<File
				RelativePath=".\hal\bmu\bmu_pop_cmd.h"
				>
			</File>
			<File
				RelativePath=".\hal\bmu\bmu_reg.h"
				>
			</File>
			<File
				RelativePath=".\hal\bmu\bmu_reg_5013.h"
				>
			</File>
			<File
				RelativePath=".\hal\bmu\bmu_reg_5017.h"
				>
			</File>
			<File
				RelativePath=".\hal\bmu\bmu_reg_5021.h"
				>
			</File>
			<File
				RelativePath=".\common\boot_api.h"
				>
			</File>
			<File
				RelativePath=".\bootloader\bootloader.h"
				>
			</File>
			<File
				RelativePath=".\bootloader\bootloader_api.h"
				>
			</File>
			<File
				RelativePath=".\buffer\buf_api.h"
				>
			</File>
			<File
				RelativePath=".\buffer\buffer.h"
				>
			</File>
			<File
				RelativePath=".\queue\buffer_queue.h"
				>
			</File>
			<File
				RelativePath=".\burner\Burner.h"
				>
			</File>
			<File
				RelativePath=".\burner\Burner_api.h"
				>
			</File>
			<File
				RelativePath=".\burner\Burner_Def.h"
				>
			</File>
			<File
				RelativePath=".\burner\Burner_Test_Code.h"
				>
			</File>
			<File
				RelativePath=".\hal\sys\api\clk\clk.h"
				>
			</File>
			<File
				RelativePath=".\hal\sys\api\clk\clk_api.h"
				>
			</File>
			<File
				RelativePath=".\burner\codepointer.h"
				>
			</File>
			<File
				RelativePath=".\burner\codesignheader_api.h"
				>
			</File>
			<File
				RelativePath=".\hal\cop0\cop0.h"
				>
			</File>
			<File
				RelativePath=".\hal\cop0\cop0_api.h"
				>
			</File>
			<File
				RelativePath=".\hal\cop0\cop0_cmd.h"
				>
			</File>
			<File
				RelativePath=".\hal\cop0\cop0_pop_cmd.h"
				>
			</File>
			<File
				RelativePath=".\hal\cop0\cop0_reg.h"
				>
			</File>
			<File
				RelativePath=".\hal\cop0\cop0_reg_5013.h"
				>
			</File>
			<File
				RelativePath=".\hal\cop0\cop0_reg_5017.h"
				>
			</File>
			<File
				RelativePath=".\hal\cop0\cop0_reg_5021.h"
				>
			</File>
			<File
				RelativePath=".\hal\cop0\cop0_tiein.h"
				>
			</File>
			<File
				RelativePath=".\hal\cop0\cop0_tieout.h"
				>
			</File>
			<File
				RelativePath=".\hal\cop1\cop1.h"
				>
			</File>
			<File
				RelativePath=".\hal\cop1\cop1_api.h"
				>
			</File>
			<File
				RelativePath=".\hal\cop1\cop1_cmd_type.h"
				>
			</File>
			<File
				RelativePath=".\hal\cop1\cop1_cmd_type_5013.h"
				>
			</File>
			<File
				RelativePath=".\hal\cop1\cop1_cmd_type_5017.h"
				>
			</File>
			<File
				RelativePath=".\hal\cop1\cop1_cmd_type_5021.h"
				>
			</File>
			<File
				RelativePath=".\hal\cop1\cop1_inline.h"
				>
			</File>
			<File
				RelativePath=".\hal\cop1\cop1_pop_cmd.h"
				>
			</File>
			<File
				RelativePath=".\hal\cop1\cop1_reg.h"
				>
			</File>
			<File
				RelativePath=".\hal\cop1\cop1_reg_5013.h"
				>
			</File>
			<File
				RelativePath=".\hal\cop1\cop1_reg_5017.h"
				>
			</File>
			<File
				RelativePath=".\hal\cop1\cop1_reg_5021.h"
				>
			</File>
			<File
				RelativePath=".\host\cop1_st1.h"
				>
			</File>
			<File
				RelativePath=".\copy_unit\copy_unit.h"
				>
			</File>
			<File
				RelativePath=".\copy_unit\copy_unit_api.h"
				>
			</File>
			<File
				RelativePath=".\hal\sys\api\cphy\cphy_api.h"
				>
			</File>
			<File
				RelativePath=".\hal\cpu\cpu_api.h"
				>
			</File>
			<File
				RelativePath=".\cpu\cpu_api.h"
				>
			</File>
			<File
				RelativePath=".\cpu\cpu_cache_api.h"
				>
			</File>
			<File
				RelativePath=".\hal\cpu\cpu_reg.h"
				>
			</File>
			<File
				RelativePath=".\hal\db\db_api.h"
				>
			</File>
			<File
				RelativePath=".\hal\db\db_reg.h"
				>
			</File>
			<File
				RelativePath=".\hal\db\db_reg_5013.h"
				>
			</File>
			<File
				RelativePath=".\hal\db\db_reg_5017.h"
				>
			</File>
			<File
				RelativePath=".\hal\db\db_reg_5021.h"
				>
			</File>
			<File
				RelativePath=".\table\dbt\dbt.h"
				>
			</File>
			<File
				RelativePath=".\table\dbt\dbt_api.h"
				>
			</File>
			<File
				RelativePath=".\debug\debug.h"
				>
			</File>
			<File
				RelativePath=".\debug\debug_api.h"
				>
			</File>
			<File
				RelativePath=".\debug\debug_setup.h"
				>
			</File>
			<File
				RelativePath=".\hal\dmac\dmac_api.h"
				>
			</File>
			<File
				RelativePath=".\hal\dmac\dmac_cmd.h"
				>
			</File>
			<File
				RelativePath=".\hal\dmac\dmac_cmd_type.h"
				>
			</File>
			<File
				RelativePath=".\hal\dmac\dmac_cmd_type_5013.h"
				>
			</File>
			<File
				RelativePath=".\hal\dmac\dmac_cmd_type_5017.h"
				>
			</File>
			<File
				RelativePath=".\hal\dmac\dmac_cmd_type_5021.h"
				>
			</File>
			<File
				RelativePath=".\hal\dmac\dmac_inline.h"
				>
			</File>
			<File
				RelativePath=".\hal\dmac\dmac_pop_cmd.h"
				>
			</File>
			<File
				RelativePath=".\drive_log\drive_log.h"
				>
			</File>
			<File
				RelativePath=".\drive_log\drive_log_api.h"
				>
			</File>
			<File
				RelativePath=".\hal\cpu\E13_cpu_reg.h"
				>
			</File>
			<File
				RelativePath=".\common\E13_fw_version.h"
				>
			</File>
			<File
				RelativePath=".\hal\sata\E13_sata_reg.h"
				>
			</File>
			<File
				RelativePath=".\hal\security\E13_security_reg.h"
				>
			</File>
			<File
				RelativePath=".\common\E21_fw_version.h"
				>
			</File>
			<File
				RelativePath=".\hal\security\E21_security_reg.h"
				>
			</File>
			<File
				RelativePath=".\hal\sys\api\efuc\efuse.h"
				>
			</File>
			<File
				RelativePath=".\hal\sys\api\efuc\efuse_api.h"
				>
			</File>
			<File
				RelativePath=".\env.h"
				>
			</File>
			<File
				RelativePath=".\err_handle\err_handle.h"
				>
			</File>
			<File
				RelativePath=".\err_handle\err_handle_api.h"
				>
			</File>
			<File
				RelativePath=".\err_handle\err_handle_copyblock_api.h"
				>
			</File>
			<File
				RelativePath=".\err_handle\err_handle_program_api.h"
				>
			</File>
			<File
				RelativePath=".\err_handle\err_handle_vt_child.h"
				>
			</File>
			<File
				RelativePath=".\retry\err_hdl_fpl_RS.h"
				>
			</File>
			<File
				RelativePath=".\retry\err_hdl_fpl_sb_retry_api.h"
				>
			</File>
			<File
				RelativePath=".\retry\err_hdl_fpl_softbit_retry.h"
				>
			</File>
			<File
				RelativePath=".\retry\err_hdl_fpl_softbit_retry_table.h"
				>
			</File>
			<File
				RelativePath=".\retry\err_hdl_fpl_SoftbitRaid.h"
				>
			</File>
			<File
				RelativePath=".\retry\err_hdl_fpl_TurboRain.h"
				>
			</File>
			<File
				RelativePath=".\err_handle\err_log_api.h"
				>
			</File>
			<File
				RelativePath=".\err_handle\error_handle_code_api.h"
				>
			</File>
			<File
				RelativePath=".\cpu\error_monitor.h"
				>
			</File>
			<File
				RelativePath=".\hal\sys\api\fdqdly\fdqdly_api.h"
				>
			</File>
			<File
				RelativePath=".\hal\fip\fip.h"
				>
			</File>
			<File
				RelativePath=".\hal\fip\fip_api.h"
				>
			</File>
			<File
				RelativePath=".\hal\fip\fip_reg.h"
				>
			</File>
			<File
				RelativePath=".\hal\fip\fip_reg_5013.h"
				>
			</File>
			<File
				RelativePath=".\hal\fip\fip_reg_5017.h"
				>
			</File>
			<File
				RelativePath=".\hal\fip\fip_reg_5021.h"
				>
			</File>
			<File
				RelativePath=".\flash_spec.h"
				>
			</File>
			<File
				RelativePath=".\hal\sys\api\fphyc\fphyc_api.h"
				>
			</File>
			<File
				RelativePath=".\hal\fip\fpu.h"
				>
			</File>
			<File
				RelativePath=".\hal\fip\fpu_hynix_qlc.h"
				>
			</File>
			<File
				RelativePath=".\hal\fip\fpu_hynix_tlc.h"
				>
			</File>
			<File
				RelativePath=".\hal\fip\fpu_micron_b47r_5017.h"
				>
			</File>
			<File
				RelativePath=".\hal\fip\fpu_micron_b47r_5021.h"
				>
			</File>
			<File
				RelativePath=".\hal\fip\fpu_micron_qlc.h"
				>
			</File>
			<File
				RelativePath=".\hal\fip\fpu_micron_tlc.h"
				>
			</File>
			<File
				RelativePath=".\hal\fip\fpu_samsung_tlc.h"
				>
			</File>
			<File
				RelativePath=".\hal\fip\fpu_sandisk_BICS8_tlc.h"
				>
			</File>
			<File
				RelativePath=".\hal\fip\fpu_sandisk_qlc.h"
				>
			</File>
			<File
				RelativePath=".\hal\fip\fpu_sandisk_tlc.h"
				>
			</File>
			<File
				RelativePath=".\hal\fip\fpu_tsb_qlc.h"
				>
			</File>
			<File
				RelativePath=".\hal\fip\fpu_tsb_tlc.h"
				>
			</File>
			<File
				RelativePath=".\hal\fip\fpu_ymtc_qlc.h"
				>
			</File>
			<File
				RelativePath=".\hal\fip\fpu_ymtc_tlc.h"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl.h"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_api.h"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_background.h"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_barrier.h"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_bfea.h"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_bfea_api.h"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_gc.h"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_gc_xzip.h"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_inline.h"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_ior.h"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_journal.h"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_journal_api.h"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_load_alignment.h"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_loaddata.h"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_nrw.h"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_nrw_api.h"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_preread.h"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_preread_api.h"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_readverify.h"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_readverify_api.h"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_seq_table.h"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_seq_table_api.h"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_sustain.h"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_sustain_api.h"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_sync_cmd_handler.h"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_table.h"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_table_api.h"
				>
			</File>
			<File
				RelativePath=".\trim\ftl_trim.h"
				>
			</File>
			<File
				RelativePath=".\trim\ftl_trim_api.h"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_xcut.h"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_xcut_api.h"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_xfer_data_in.h"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_xfer_data_in_api.h"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_xfer_data_out.h"
				>
			</File>
			<File
				RelativePath=".\ftl\ftl_xzip_api.h"
				>
			</File>
			<File
				RelativePath=".\db_mgr\fw_cmd_table.h"
				>
			</File>
			<File
				RelativePath=".\common\fw_common.h"
				>
			</File>
			<File
				RelativePath=".\init\fw_init.h"
				>
			</File>
			<File
				RelativePath=".\fw_mgr\fw_mgr.h"
				>
			</File>
			<File
				RelativePath=".\init\fw_preformat.h"
				>
			</File>
			<File
				RelativePath=".\db_mgr\fw_tagid.h"
				>
			</File>
			<File
				RelativePath=".\common\fw_vardef.h"
				>
			</File>
			<File
				RelativePath=".\common\fw_version.h"
				>
			</File>
			<File
				RelativePath=".\hal\sys\api\gpio\gpio_api.h"
				>
			</File>
			<File
				RelativePath=".\nsrmp\hal_nsrmp.h"
				>
			</File>
			<File
				RelativePath=".\hmb\hmb.h"
				>
			</File>
			<File
				RelativePath=".\hmb\hmb_api.h"
				>
			</File>
			<File
				RelativePath=".\host\host.h"
				>
			</File>
			<File
				RelativePath=".\host_handler\host_management_api.h"
				>
			</File>
			<File
				RelativePath=".\host_handler\hostevt_errhdl.h"
				>
			</File>
			<File
				RelativePath=".\host_handler\hostevt_errhdl_api.h"
				>
			</File>
			<File
				RelativePath=".\hal\pic\i2c\i2cm.h"
				>
			</File>
			<File
				RelativePath=".\hal\pic\i2c\i2cm_api.h"
				>
			</File>
			<File
				RelativePath=".\hal\pic\i2c\i2cm_device_api.h"
				>
			</File>
			<File
				RelativePath=".\hal\pic\i2c\i2cm_reg.h"
				>
			</File>
			<File
				RelativePath=".\hal\pic\i2c\i2cm_reg_5013.h"
				>
			</File>
			<File
				RelativePath=".\hal\pic\i2c\i2cm_reg_5017.h"
				>
			</File>
			<File
				RelativePath=".\hal\pic\i2c\i2cm_reg_5021.h"
				>
			</File>
			<File
				RelativePath=".\burner\IDpage.h"
				>
			</File>
			<File
				RelativePath=".\table\initinfo_vt\initinfo_vt.h"
				>
			</File>
			<File
				RelativePath=".\table\initinfo_vt\initinfo_vt_api.h"
				>
			</File>
			<File
				RelativePath=".\cpu\isr_handler.h"
				>
			</File>
			<File
				RelativePath=".\hal\sys\api\itc\itc.h"
				>
			</File>
			<File
				RelativePath=".\hal\sys\api\itc\itc_api.h"
				>
			</File>
			<File
				RelativePath=".\hal\sys\api\led\led_api.h"
				>
			</File>
			<File
				RelativePath=".\log\log_api.h"
				>
			</File>
			<File
				RelativePath=".\lpm\lpm.h"
				>
			</File>
			<File
				RelativePath=".\lpm\lpm3loader.h"
				>
			</File>
			<File
				RelativePath=".\lpm\lpm_api.h"
				>
			</File>
			<File
				RelativePath=".\common\math_op.h"
				>
			</File>
			<File
				RelativePath=".\media_scan\media_scan_api.h"
				>
			</File>
			<File
				RelativePath=".\common\mem.h"
				>
			</File>
			<File
				RelativePath=".\common\mem_5013.h"
				>
			</File>
			<File
				RelativePath=".\common\mem_5017.h"
				>
			</File>
			<File
				RelativePath=".\common\mem_5021.h"
				>
			</File>
			<File
				RelativePath=".\hal\sys\api\misc\misc.h"
				>
			</File>
			<File
				RelativePath=".\hal\sys\api\misc\misc_api.h"
				>
			</File>
			<File
				RelativePath=".\hal\mr\mr.h"
				>
			</File>
			<File
				RelativePath=".\hal\mr\mr_api.h"
				>
			</File>
			<File
				RelativePath=".\hal\mr\mr_reg.h"
				>
			</File>
			<File
				RelativePath=".\hal\mr\mr_reg_5013.h"
				>
			</File>
			<File
				RelativePath=".\hal\mr\mr_reg_5017.h"
				>
			</File>
			<File
				RelativePath=".\hal\mr\mr_reg_5021.h"
				>
			</File>
			<File
				RelativePath=".\hal\sys\api\mux\mux.h"
				>
			</File>
			<File
				RelativePath=".\hal\sys\api\mux\mux_api.h"
				>
			</File>
			<File
				RelativePath=".\init\NewScanFlow.h"
				>
			</File>
			<File
				RelativePath=".\nsrmp\nsrmp_api.h"
				>
			</File>
			<File
				RelativePath=".\hal\nvme\nvme.h"
				>
			</File>
			<File
				RelativePath=".\hal\nvme\nvme_api.h"
				>
			</File>
			<File
				RelativePath=".\nvme_api\nvme_ata_security\nvme_ata_security.h"
				>
			</File>
			<File
				RelativePath=".\nvme_api\nvme_ata_security\nvme_ata_security_api.h"
				>
			</File>
			<File
				RelativePath=".\hal\nvme\nvme_cmgr_reg_5013.h"
				>
			</File>
			<File
				RelativePath=".\hal\nvme\nvme_cmgr_reg_5021.h"
				>
			</File>
			<File
				RelativePath=".\hal\nvme\nvme_reg_5013.h"
				>
			</File>
			<File
				RelativePath=".\hal\nvme\nvme_reg_5021.h"
				>
			</File>
			<File
				RelativePath=".\hal\nvme\nvme_spec.h"
				>
			</File>
			<File
				RelativePath=".\rs\open_block_rsmap_api.h"
				>
			</File>
			<File
				RelativePath=".\overlay\overlay\ovl_api.h"
				>
			</File>
			<File
				RelativePath=".\overlay\overlay\ovl_internal.h"
				>
			</File>
			<File
				RelativePath=".\hal\sys\api\padc\padc_api.h"
				>
			</File>
			<File
				RelativePath=".\common\parasoft.h"
				>
			</File>
			<File
				RelativePath=".\retry\patch_cmd.h"
				>
			</File>
			<File
				RelativePath=".\hal\sys\api\pmu\pmu_api.h"
				>
			</File>
			<File
				RelativePath=".\hal\sys\api\pwr_ctrl\pwr_ctrl_api.h"
				>
			</File>
			<File
				RelativePath=".\hal\raidecc\raidecc.h"
				>
			</File>
			<File
				RelativePath=".\hal\raidecc\raidecc_api.h"
				>
			</File>
			<File
				RelativePath=".\hal\raidecc\raidecc_reg.h"
				>
			</File>
			<File
				RelativePath=".\hal\raidecc\raidecc_reg_5013.h"
				>
			</File>
			<File
				RelativePath=".\hal\raidecc\raidecc_reg_5017.h"
				>
			</File>
			<File
				RelativePath=".\hal\raidecc\raidecc_reg_5021.h"
				>
			</File>
			<File
				RelativePath=".\raideccmap\raideccmap.h"
				>
			</File>
			<File
				RelativePath=".\raideccmap\raideccmap_api.h"
				>
			</File>
			<File
				RelativePath=".\raideccmap\raideccmap_for_B27B_N28.h"
				>
			</File>
			<File
				RelativePath=".\raideccmap\raideccmap_for_BICS_TLC.h"
				>
			</File>
			<File
				RelativePath=".\raideccmap\raideccmap_for_N48R.h"
				>
			</File>
			<File
				RelativePath=".\raideccmap\raideccmap_for_seq_table_api.h"
				>
			</File>
			<File
				RelativePath=".\raideccmap\raideccmap_inline_api.h"
				>
			</File>
			<File
				RelativePath=".\raideccmap\raideccmap_paritymap.h"
				>
			</File>
			<File
				RelativePath=".\ramdisk\ramdisk.h"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_api.h"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_fip_instruction_fetching_test.h"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_fip_test.h"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_flash_program_order.h"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_fpl.h"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_init.h"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_ldpc_test.h"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_log.h"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_pio_force_wr_log.h"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_security_self_test.h"
				>
			</File>
			<File
				RelativePath=".\rdt\rdt_sram_test.h"
				>
			</File>
			<File
				RelativePath=".\read_disturb\read_disturb.h"
				>
			</File>
			<File
				RelativePath=".\read_disturb\read_disturb_api.h"
				>
			</File>
			<File
				RelativePath=".\read_disturb\read_disturb_PRDH_api.h"
				>
			</File>
			<File
				RelativePath=".\queue\read_queue.h"
				>
			</File>
			<File
				RelativePath=".\retry\retry.h"
				>
			</File>
			<File
				RelativePath=".\retry\retry_api.h"
				>
			</File>
			<File
				RelativePath=".\retry\retry_hb.h"
				>
			</File>
			<File
				RelativePath=".\retry\retry_hynix_v6_tlc\retry_hynix_v6_tlc_E13_U17_neutral_hb.h"
				>
			</File>
			<File
				RelativePath=".\retry\retry_hynix_v7_qlc\retry_hynix_v7_qlc_E13_U17_neutral_hb.h"
				>
			</File>
			<File
				RelativePath=".\retry\retry_kioxia_bics4hdr_tlc\retry_kioxia_bics4hdr_tlc_S17_neutral_hb.h"
				>
			</File>
			<File
				RelativePath=".\retry\retry_kioxia_bics5_tlc\retry_kioxia_bics5_tlc_S17_neutral_hb.h"
				>
			</File>
			<File
				RelativePath=".\retry\retry_kioxia_bics5_tlc\retry_kioxia_bics5_tlc_S17_neutral_sb.h"
				>
			</File>
			<File
				RelativePath=".\retry\retry_micron_b47r_tlc\retry_micron_b47r_tlc_E13_neutral_hb.h"
				>
			</File>
			<File
				RelativePath=".\retry\retry_micron_b47r_tlc\retry_micron_b47r_tlc_E13_S17_nicks_hb.h"
				>
			</File>
			<File
				RelativePath=".\retry\retry_micron_b47r_tlc\retry_micron_b47r_tlc_E13_U17_neutral_hb.h"
				>
			</File>
			<File
				RelativePath=".\retry\retry_micron_n48r_qlc\retry_micron_n48r_qlc_E13_nicks_U17_frey2_hb.h"
				>
			</File>
			<File
				RelativePath=".\retry\retry_micron_n48r_qlc\retry_micron_n48r_qlc_E13_U17_neutral_hb.h"
				>
			</File>
			<File
				RelativePath=".\retry\retry_micron_qlc_softbit_retry.h"
				>
			</File>
			<File
				RelativePath=".\retry\retry_micron_qlc_softbit_retry_all_page_type_N48R.h"
				>
			</File>
			<File
				RelativePath=".\retry\retry_micron_tlc_softbit_retry.h"
				>
			</File>
			<File
				RelativePath=".\retry\retry_micron_tlc_softbit_retry_all_page_type.h"
				>
			</File>
			<File
				RelativePath=".\retry\retry_micron_tlc_softbit_retry_all_page_type_B47R.h"
				>
			</File>
			<File
				RelativePath=".\retry\retry_Samsung_v6_tlc\retry_samsung_v6_tlc_E13_U17_neutral_hb.h"
				>
			</File>
			<File
				RelativePath=".\retry\retry_sandisk_bics5_tlc\retry_sandisk_bics5_tlc_E13_U17_neutral_hb.h"
				>
			</File>
			<File
				RelativePath=".\retry\retry_sandisk_bics6_qlc\retry_sandisk_bics6_qlc_E13_U17_neutral_hb.h"
				>
			</File>
			<File
				RelativePath=".\retry\retry_sandisk_bics8_tlc\retry_sandisk_bics8_tlc_E13_U17_neutral_hb.h"
				>
			</File>
			<File
				RelativePath=".\retry\retry_setup.h"
				>
			</File>
			<File
				RelativePath=".\retry\retry_trim_table.h"
				>
			</File>
			<File
				RelativePath=".\retry\retry_tsb_qlc_softbit_retry.h"
				>
			</File>
			<File
				RelativePath=".\retry\retry_tsb_tlc_softbit_retry.h"
				>
			</File>
			<File
				RelativePath=".\retry\retry_version_api.h"
				>
			</File>
			<File
				RelativePath=".\retry\retry_ymtc_ems_qlc\retry_ymtc_ems_qlc_E13_U17_neutral_hb.h"
				>
			</File>
			<File
				RelativePath=".\retry\retry_ymtc_tas_tlc\retry_ymtc_tas_tlc_E13_U17_neutral_hb.h"
				>
			</File>
			<File
				RelativePath=".\retry\retry_ymtc_wts_tlc\retry_ymtc_wts_tlc_E13_U17_neutral_hb.h"
				>
			</File>
			<File
				RelativePath=".\err_handle\rma_log_api.h"
				>
			</File>
			<File
				RelativePath=".\hal\sys\api\rng\rng.h"
				>
			</File>
			<File
				RelativePath=".\hal\sys\api\rng\rng_api.h"
				>
			</File>
			<File
				RelativePath=".\hal\sys\api\rram\rram_api.h"
				>
			</File>
			<File
				RelativePath=".\hal\rs\rs.h"
				>
			</File>
			<File
				RelativePath=".\hal\rs\rs_api.h"
				>
			</File>
			<File
				RelativePath=".\hal\rs\rs_reg.h"
				>
			</File>
			<File
				RelativePath=".\hal\rs\rs_reg_5013.h"
				>
			</File>
			<File
				RelativePath=".\hal\rs\rs_reg_5017.h"
				>
			</File>
			<File
				RelativePath=".\hal\rs\rs_reg_5021.h"
				>
			</File>
			<File
				RelativePath=".\rs\rsmap.h"
				>
			</File>
			<File
				RelativePath=".\hal\sys\api\rtt\rtt.h"
				>
			</File>
			<File
				RelativePath=".\hal\sys\api\rtt\rtt_api.h"
				>
			</File>
			<File
				RelativePath=".\table\rut\rut.h"
				>
			</File>
			<File
				RelativePath=".\table\rut\rut_api.h"
				>
			</File>
			<File
				RelativePath=".\host_handler\rw_mgr.h"
				>
			</File>
			<File
				RelativePath=".\hal\cpu\S17_cpu_reg.h"
				>
			</File>
			<File
				RelativePath=".\common\S17_fw_version.h"
				>
			</File>
			<File
				RelativePath=".\hal\sata\S17_sata_reg.h"
				>
			</File>
			<File
				RelativePath=".\hal\security\S17_security_reg.h"
				>
			</File>
			<File
				RelativePath=".\hal\sata\sata.h"
				>
			</File>
			<File
				RelativePath=".\hal\sata\sata_api.h"
				>
			</File>
			<File
				RelativePath=".\hal\sata\sata_cmd.h"
				>
			</File>
			<File
				RelativePath=".\hal\sata\sata_cmd_tbl.h"
				>
			</File>
			<File
				RelativePath=".\hal\sata\sata_reg.h"
				>
			</File>
			<File
				RelativePath=".\hal\sata\sata_vendor_cmd.h"
				>
			</File>
			<File
				RelativePath=".\hal\sata\sata_vuc.h"
				>
			</File>
			<File
				RelativePath=".\hal\security\security.h"
				>
			</File>
			<File
				RelativePath=".\hal\security\security_api.h"
				>
			</File>
			<File
				RelativePath=".\hal\security\security_reg.h"
				>
			</File>
			<File
				RelativePath=".\hal\security\security_rsa_api.h"
				>
			</File>
			<File
				RelativePath=".\setup.h"
				>
			</File>
			<File
				RelativePath=".\setup_usb.h"
				>
			</File>
			<File
				RelativePath=".\nvme_api\bmu\shr_hal_bmu_reg.h"
				>
			</File>
			<File
				RelativePath=".\nvme_api\d2h\shr_hal_d2h.h"
				>
			</File>
			<File
				RelativePath=".\nvme_api\d2h\shr_hal_d2h_reg.h"
				>
			</File>
			<File
				RelativePath=".\nvme_api\db\shr_hal_db.h"
				>
			</File>
			<File
				RelativePath=".\nvme_api\misc\shr_hal_debug.h"
				>
			</File>
			<File
				RelativePath=".\nvme_api\nvme\shr_hal_nvme.h"
				>
			</File>
			<File
				RelativePath=".\nvme_api\nvme\shr_hal_nvme_api.h"
				>
			</File>
			<File
				RelativePath=".\nvme_api\nvme\shr_hal_nvme_cmgr.h"
				>
			</File>
			<File
				RelativePath=".\nvme_api\nvme\shr_hal_nvme_feat.h"
				>
			</File>
			<File
				RelativePath=".\nvme_api\nvme\shr_hal_nvme_identify.h"
				>
			</File>
			<File
				RelativePath=".\nvme_api\nvme\shr_hal_nvme_io_api.h"
				>
			</File>
			<File
				RelativePath=".\nvme_api\nvme\shr_hal_nvme_log.h"
				>
			</File>
			<File
				RelativePath=".\nvme_api\nvme\shr_hal_nvme_other.h"
				>
			</File>
			<File
				RelativePath=".\nvme_api\nvme\shr_hal_nvme_persistent_log_api.h"
				>
			</File>
			<File
				RelativePath=".\nvme_api\nvme\shr_hal_nvme_reg_5013.h"
				>
			</File>
			<File
				RelativePath=".\nvme_api\nvme\shr_hal_nvme_reg_5021.h"
				>
			</File>
			<File
				RelativePath=".\nvme_api\nvme\shr_hal_nvme_vuc_api.h"
				>
			</File>
			<File
				RelativePath=".\nvme_api\pcie\shr_hal_pcie.h"
				>
			</File>
			<File
				RelativePath=".\nvme_api\pcie\shr_hal_pcie_reg.h"
				>
			</File>
			<File
				RelativePath=".\nvme_api\pic\shr_hal_pic_uartt.h"
				>
			</File>
			<File
				RelativePath=".\hal\pic\uart\shr_hal_pic_uartxfer.h"
				>
			</File>
			<File
				RelativePath=".\nvme_api\system\shr_hal_sys_pmu_reg.h"
				>
			</File>
			<File
				RelativePath=".\testflow\sim_spor\sim_spor.h"
				>
			</File>
			<File
				RelativePath=".\testflow\sim_spor\sim_spor_api.h"
				>
			</File>
			<File
				RelativePath=".\hal\pic\smbus\smb_reg.h"
				>
			</File>
			<File
				RelativePath=".\hal\pic\smbus\smb_reg_5013.h"
				>
			</File>
			<File
				RelativePath=".\hal\pic\smbus\smb_reg_5017.h"
				>
			</File>
			<File
				RelativePath=".\hal\pic\smbus\smb_reg_5021.h"
				>
			</File>
			<File
				RelativePath=".\common\spare.h"
				>
			</File>
			<File
				RelativePath=".\hal\sys\api\sphy\sphy_api.h"
				>
			</File>
			<File
				RelativePath=".\hal\spi\spi_api.h"
				>
			</File>
			<File
				RelativePath=".\hal\spi\spi_reg.h"
				>
			</File>
			<File
				RelativePath=".\hal\spi\spi_reg_5013.h"
				>
			</File>
			<File
				RelativePath=".\hal\spi\spi_reg_5017.h"
				>
			</File>
			<File
				RelativePath=".\hal\spi\spi_reg_5021.h"
				>
			</File>
			<File
				RelativePath=".\hal\cop1\st3c\st3c_cmd_api.h"
				>
			</File>
			<File
				RelativePath=".\hal\cop1\st3c\st3c_cmd_type.h"
				>
			</File>
			<File
				RelativePath=".\retry\stall.h"
				>
			</File>
			<File
				RelativePath=".\common\svn_ver.h"
				>
			</File>
			<File
				RelativePath=".\common\symbol.h"
				>
			</File>
			<File
				RelativePath=".\symbol.h"
				>
			</File>
			<File
				RelativePath=".\table\sys_area\sys_area.h"
				>
			</File>
			<File
				RelativePath=".\table\sys_area\sys_area_api.h"
				>
			</File>
			<File
				RelativePath=".\table\sys_block\sys_block_api.h"
				>
			</File>
			<File
				RelativePath=".\hal\sys\reg\sys_pd0_reg.h"
				>
			</File>
			<File
				RelativePath=".\hal\sys\reg\sys_pd0_reg_5013.h"
				>
			</File>
			<File
				RelativePath=".\hal\sys\reg\sys_pd0_reg_5017.h"
				>
			</File>
			<File
				RelativePath=".\hal\sys\reg\sys_pd0_reg_5021.h"
				>
			</File>
			<File
				RelativePath=".\hal\sys\reg\sys_pd1_reg.h"
				>
			</File>
			<File
				RelativePath=".\hal\sys\reg\sys_pd1_reg_5013.h"
				>
			</File>
			<File
				RelativePath=".\hal\sys\reg\sys_pd1_reg_5017.h"
				>
			</File>
			<File
				RelativePath=".\hal\sys\reg\sys_pd1_reg_5021.h"
				>
			</File>
			<File
				RelativePath=".\init\system_init.h"
				>
			</File>
			<File
				RelativePath=".\tcg\tcg.h"
				>
			</File>
			<File
				RelativePath=".\tcg\tcg_api.h"
				>
			</File>
			<File
				RelativePath=".\tcg\tcg_conf.h"
				>
			</File>
			<File
				RelativePath=".\tcg\tcg_def.h"
				>
			</File>
			<File
				RelativePath=".\tcg\tcg_drbg.h"
				>
			</File>
			<File
				RelativePath=".\tcg\tcg_inline.h"
				>
			</File>
			<File
				RelativePath=".\tcg\tcg_pbkdf2.h"
				>
			</File>
			<File
				RelativePath=".\tcg\tcg_security_log.h"
				>
			</File>
			<File
				RelativePath=".\tcg\tcg_state.h"
				>
			</File>
			<File
				RelativePath=".\tcg\tcg_struct.h"
				>
			</File>
			<File
				RelativePath=".\VRLC\tempco_api.h"
				>
			</File>
			<File
				RelativePath=".\testflow\test_aom.h"
				>
			</File>
			<File
				RelativePath=".\testflow\test_xzip.h"
				>
			</File>
			<File
				RelativePath=".\tt\TT.h"
				>
			</File>
			<File
				RelativePath=".\tt\TT_api.h"
				>
			</File>
			<File
				RelativePath=".\common\typedef.h"
				>
			</File>
			<File
				RelativePath=".\common\U17_fw_version.h"
				>
			</File>
			<File
				RelativePath=".\hal\pic\uart\uart.h"
				>
			</File>
			<File
				RelativePath=".\hal\pic\uart\uart_api.h"
				>
			</File>
			<File
				RelativePath=".\hal\pic\uart\uart_reg.h"
				>
			</File>
			<File
				RelativePath=".\hal\pic\uart\uart_reg_5013.h"
				>
			</File>
			<File
				RelativePath=".\hal\pic\uart\uart_reg_5017.h"
				>
			</File>
			<File
				RelativePath=".\hal\pic\uart\uart_reg_5021.h"
				>
			</File>
			<File
				RelativePath=".\hal\usb\usb.h"
				>
			</File>
			<File
				RelativePath=".\hal\usb\usb_api.h"
				>
			</File>
			<File
				RelativePath=".\hal\usb\usb_cmd.h"
				>
			</File>
			<File
				RelativePath=".\hal\usb\usb_reg.h"
				>
			</File>
			<File
				RelativePath=".\table\vbmap\vbmap_api.h"
				>
			</File>
			<File
				RelativePath=".\table\vbmap\vbmap_inline.h"
				>
			</File>
			<File
				RelativePath=".\VRLC\VRLC.h"
				>
			</File>
			<File
				RelativePath=".\VRLC\VRLC_api.h"
				>
			</File>
			<File
				RelativePath=".\vt_sweep\vt_sweep.h"
				>
			</File>
			<File
				RelativePath=".\vuc\Vth_Parsing.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_api.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_ApKey.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_CacheRead.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_CacheWrite.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_command.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_CutEFuse.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_DetectPeripheral.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_DetectTxRx.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_DirectReadInfo.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_DisableFrozenLock.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_DumpTable.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_EraseAll.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_EraseFlash.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_FwFeatureControl.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_GetEraseCount.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_GetFlashInfo.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_GetRDTLog.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_GetRMAInfo.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_GetStatus.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_GetVucDataSize.h"
				>
			</File>
			<File
				RelativePath=".\host\VUC_handler.h"
				>
			</File>
			<File
				RelativePath=".\host\VUC_handler_api.h"
				>
			</File>
			<File
				RelativePath=".\host\VUC_host.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_ISPFlash.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_ISPJump.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_ISPReadFlash.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_ISPReadPRAM.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_ISPReadROM.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_ISPRom.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_KingstonAntiFakeData.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_KingstonVerifyAntiFake.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_ListBadBlock.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_MakeError.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_MicronClearEventLog.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_MicronGetBEC.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_MicronGetBlkNandMode.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_MicronGetDefectList.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_MicronGetDriveConfigData.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_MicronGetEraseCount.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_MicronGetMLBi.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_MicronGetNandErrorRecoveryStatistics.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_MicronGetRawBitErrCnt.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_MicronGetTemperature.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_MicronGetUnifiedEventLog.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_MicronGetVTSweep.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_MicronLogicaltoPhysical.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_MicronNandPass.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_MicronPhysicaltoLogicalAddress.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_MicronResponse.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_MicronSetMLBi.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_NandVerifyRead.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_NandVerifyTrigger.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_NandVerifyWrite.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_PCATranslate.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_PCIEEyeInfo.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_ProgramFlash.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_Protect.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_Protect_api.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_ReadFlash.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_ReadPH.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_ReadReg.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_ReadScanFlashWindow.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_ReadSram.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_ReadSysInfo.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_ScanFlashWindowParameter.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_ScanFlashWindowSetting.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_ScanPH.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_ScanRDTLog.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_SearchSysBlock.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_SecurityPassThrough.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_SetFlashFeature.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_SetPSID.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_SetSpecificGlobalValue.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_SideBandIOTest.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_SMART.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_SmartRescue.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_SmartRescue_api.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_UnlockJTAG.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_Utilities.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_Write_VRLC.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_WriteInfo.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_WritePH.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_WriteReadAPDBT.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_WriteReg.h"
				>
			</File>
			<File
				RelativePath=".\vuc\VUC_WriteSram.h"
				>
			</File>
			<File
				RelativePath=".\hal\sys\api\wdt\wdt.h"
				>
			</File>
			<File
				RelativePath=".\hal\sys\api\wdt\wdt_api.h"
				>
			</File>
			<File
				RelativePath=".\wl\wl.h"
				>
			</File>
			<File
				RelativePath=".\wl\wl_api.h"
				>
			</File>
			<File
				RelativePath=".\WordLineFolding\WordLineFolding.h"
				>
			</File>
			<File
				RelativePath=".\WordLineFolding\WordLineFolding_api.h"
				>
			</File>
			<File
				RelativePath=".\host\Xmodem.h"
				>
			</File>
			<File
				RelativePath=".\hal\xzip\xzip_api.h"
				>
			</File>
			<File
				RelativePath=".\hal\xzip\xzip_cmd_type.h"
				>
			</File>
			<File
				RelativePath=".\hal\xzip\xzip_cmd_type_5013.h"
				>
			</File>
			<File
				RelativePath=".\hal\xzip\xzip_cmd_type_5017.h"
				>
			</File>
			<File
				RelativePath=".\hal\xzip\xzip_cmd_type_5021.h"
				>
			</File>
			<File
				RelativePath=".\hal\xzip\xzip_reg.h"
				>
			</File>
			<File
				RelativePath=".\hal\xzip\xzip_reg_5013.h"
				>
			</File>
			<File
				RelativePath=".\hal\xzip\xzip_reg_5017.h"
				>
			</File>
			<File
				RelativePath=".\hal\xzip\xzip_reg_5021.h"
				>
			</File>
			<File
				RelativePath=".\hal\zip\zip_api.h"
				>
			</File>
		</Filter>
		<Filter
			Name="Resource Files"
			Filter="rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav"
			UniqueIdentifier="{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}"
			>
		</Filter>
		<File
			RelativePath=".\debug\debug_asm.S"
			>
		</File>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
