#include "hal/pic/uart/uart_api.h"
#include "host/VUC_handler.h"
#include "nvme_api/nvme/shr_hal_nvme.h"
#include "vuc/VUC_command.h"
#include "vuc/VUC_EraseAll.h"
#include "vuc/VUC_ScanFlashWindowParameter.h"
#include "vuc/VUC_SearchSysBlock.h"
#include "vuc/VUC_Protect.h"
#include "vuc/VUC_GetStatus.h"
#include "init/fw_init.h"

void VUC_GetStatus(VUC_OPT_HCMD_PTR_t pCmd)
{
	if (!UART_VUC_MODE_EN) {
		M_UART(VUC_, "\nVUC_GET_STATUS LastCmd:%l", gLastCmd.ubFeature);
	} /*(!UART_VUC_MODE_EN)*/

#if BURNER_MODE_EN
	gLastCmd.ubIdentify = 2;     //Burner identify
#else
	gLastCmd.ubIdentify = 4;     // FW identify
#endif

	switch (gLastCmd.ubFeature) {
#if (BURNER_MODE_EN)
	case VUC_ERASE_ALL:
		//BurnerEraseAllCodeBLK(COP0_TIE_FEATURE_SLC_RWE_BIT, MODE_D3, 0);
		if (gEraseAll.ubEraseAllDoing) {
			U8 ubProgressRate = (((TRUE == IM_B47R) || (TRUE == IM_B37R)) && (VUC_ERASE_ALL_FILL_DUMMY_ALL_BLK & gEraseAll.uwEraseMode)) ? ERASE_ALL_PROGRESS_RATE_WITH_FILL_DUMMY : ERASE_ALL_PROGRESS_RATE_DEFAULT;
			if (((((TRUE == IM_B47R) || (TRUE == IM_B37R)) && (VUC_ERASE_ALL_FILL_DUMMY_ALL_BLK & gEraseAll.uwEraseMode)) &&
			(ERASEALL_FILL_DUMMY_ALL_BLK >= gEraseAll.State)) {
			gLastCmd.ubProgress = ((((U32)gEraseAll.uwFillDummyUnitIdx) * ubProgressRate) / BLOCK_NUM);
			}
			else {
				if (gEraseAll.uwEraseBlockIndex == gFlhEnv.ulBlockPerPlaneBank) {
					if (ERASEALL_FINISH == gEraseAll.State) {
						gLastCmd.ubProgress = 100;
						gEraseAll.ubEraseAllDoing = FALSE;
					}
					else {
						gLastCmd.ubProgress = 99;
					}
				}
				else {
					if (((TRUE == IM_B47R) || (TRUE == IM_B37R)) && (VUC_ERASE_ALL_FILL_DUMMY_ALL_BLK & gEraseAll.uwEraseMode)) {
						gLastCmd.ubProgress = ((((U32)gEraseAll.uwEraseBlockIndex) * ubProgressRate) / gFlhEnv.ulBlockPerPlaneBank) + ((((U32)gEraseAll.uwFillDummyUnitIdx) * ubProgressRate) / BLOCK_NUM);
					}
					else {
						gLastCmd.ubProgress = ((((U32)gEraseAll.uwEraseBlockIndex) * ubProgressRate) / gFlhEnv.ulBlockPerPlaneBank);
					}
				}
			}
			if (FAIL == gLastCmd.ubStatus) {
			gLastCmd.ubProgress = 100;
			gEraseAll.ubEraseAllDoing = FALSE;
		}
		M_UART(VUC_, "\nErase : %d, %d, %d ", gLastCmd.ubProgress, gEraseAll.uwEraseBlockIndex, gFlhEnv.ulBlockPerPlaneBank);
	}
	break;
case VUC_SET_SCAN_WINDOW_PARAMETER:
	//VUC_ScanFlashWindowParameter(pCmd);
	if (gLastCmd.ubProgress == 100 && gScanWindow.btScanWindowSetted == SCAN_WINDOW_FINISH) {

			M_UART(VUC_, "\nScan Window done");
		}
		else if (gLastCmd.ubProgress == 100 && gScanWindow.btScanWindowSetted == SCAN_WINDOW_ERROR) {

			M_UART(VUC_, "\nScan Window ERROR");
		}
		else {

			M_UART(VUC_, "\nScan window not finish,now progress:%D \%", gLastCmd.ubProgress);
		}
		break;
#endif
#if (PS5017_EN || BURNER_MODE_EN)
	case VUC_PREFORMAT:
		if (gPreformat.ubDoing) {
			gLastCmd.ubProgress = ((((U16)gPreformat.ubState * 100) / PREFORMAT_DONE) );
			if (PREFORMAT_DONE == gPreformat.ubState) {
				gLastCmd.ubProgress = 100;
				gPreformat.ubDoing = 0;
			}
			if (PREFORMAT_ERROR_STATE == gPreformat.ubState) {
				gLastCmd.ubProgress = 100;
				gPreformat.ubDoing = 0;
			}
		}
		else {
			gLastCmd.ubProgress = 0;
		}
		break;
#endif /* (PS5017_EN || BURNER_MODE_EN) */
	case VUC_SEARCH_SYS_BLOCK:
		gLastCmd.ubProgress = 100;
		break;
	case VUC_SEND_HANDSHAKE_REQUEST:
		if (VUC_PROTECT_VERIFY_STATE_HANDSHAKE_START == gVUCVar.VUCProtect.ubProtectState) {
			gLastCmd.ubProgress = 100;
		}
		else {
			gLastCmd.ubProgress = 0;
		}
		break;
	case VUC_SEND_ENCRYPTION_DATA:
		if (VUC_PROTECT_VERIFY_STATE_PASS == gVUCVar.VUCProtect.ubProtectState) {
			gLastCmd.ubProgress = 100;
		}
		else {
			gLastCmd.ubProgress = 0;
		}
		break;
	default:
		gLastCmd.ubProgress = 100;
		break;
	}

	if (BURNER_MODE_EN || gMainJumpManager.btCheckToBurnerFlag) {
		memset((void *)BURNER_VENDOR_BUF_BASE, 0x00, 512);
		memcpy((void *)BURNER_VENDOR_BUF_BASE, &gLastCmd, sizeof(gLastCmd));
	}
	else {
		memset((void *) pCmd->ulCurrentPhysicalMemoryAddr, 0x00, 512);
		memcpy((void *) pCmd->ulCurrentPhysicalMemoryAddr, &gLastCmd, sizeof(gLastCmd));
	}

#if BURNER_DEBUG_CODE
	M_UART(VUC_, "\nop: 0x%x",			gLastCmd.ubOP);
	M_UART(VUC_, "\nfeature: 0x%x",   	gLastCmd.ubFeature);
	M_UART(VUC_, "\nprogress: %d",    	gLastCmd.ubProgress);
	M_UART(VUC_, "\nstatus: 0x%x",		gLastCmd.ubStatus);
	M_UART(VUC_, "\nidentify: %d",		gLastCmd.ubIdentify);
	M_UART(VUC_, "\nsub_feature: 0x%x",	gLastCmd.ubSubFeature);
#endif
}
