#ifndef _VUC_MicronResponse_H_
#define _VUC_MicronResponse_H_
#include "aom/aom_api.h"
#include "VUC_command.h"

#define VUC_MICRON_RESPONSE_STATUS_NO_ERROR	(0x0000)
#define VUC_MICRON_RESPONSE_STATUS_INCOMPLETE	(0xFFFB)
#define VUC_MICRON_RESPONSE_STATUS_EXECUTING	(0xFFFE)

#define VUC_MICRON_RESPONSE_HEADER_SIZE	(12)
#define VUC_MICRON_RESPONSE_FORMAT_JSON	(1)
#define VUC_MICRON_RESPONSE_FORMAT_BIN	(2)
#define VUC_MICRON_RESPONSE_HEADER_FORMAT_VERSION_0	(0)
#define VUC_MICRON_PHYSICAL_TO_LOGICAL_ADDRESS	(0x0E)
#define VUC_MICRON_LOGICAL_TO_PHYSICAL_ADDRESS	(0x0D)

extern U16 guwVUCMicronResponseStatusCode;
extern U32 gulVUCMicronVUCOffset;
AOM_VUC_3 void VUCMicronResponse(OPT_HCMD_PTR pCmd);

#endif /* _VUC_MicronResponse_H_ */
