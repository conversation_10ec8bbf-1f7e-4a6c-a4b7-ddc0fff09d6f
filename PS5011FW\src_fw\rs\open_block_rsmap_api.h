// open_block_rsmap_api.h
#ifndef _OPEN_BLOCK_RSMAP_API_H_
#define _OPEN_BLOCK_RSMAP_API_H_

#include "setup.h"
#include "typedef.h"
#include "fw_vardef.h"
#include "aom/aom_api.h"

#define OPEN_BLK_RAIDECCMAP_GET_UNIT_IN_PARITY_UNIT_MODE  			(0)
#define OPEN_BLK_RAIDECCMAP_GET_UNIT_IN_TABLE_RESERVED_UNIT_MODE  	(1)

#define OPEN_BLOCK_RSMAP_BYPASS_SLC_EN 			(TRUE && OPEN_BLOCK_RSMAP_EN)

#define PARITY_BACKUP_P4K_WORKAROUND_CQ_CNT	(3)
typedef enum {
	OPEN_BLOCK_RSMAP_INIT_VAR_FOR_FW_INIT,
	OPEN_BLOCK_RSMAP_INIT_VAR_FOR_FLOW_INIT
} OpenBlockRSInitVarEnum_t;

typedef enum {
	OPEN_BLOCK_RSMAP_BACKUP_RS_FORCE_DONE,
	OPEN_BLOCK_RSMAP_BACKUP_RS_NOT_FORCE_DONE
} OpenBlockRSBackupModeEnum_t;
typedef enum {
	OPEN_BLOCK_RSMAP_INIT_MODE,
	OPEN_BLOCK_RSMAP_NORMAL_MODE,
	OPEN_BLOCK_RSMAP_SPOR_OLD_GR_MODE,
	OPEN_BLOCK_RSMAP_SPOR_GET_RETRUN_RESERVED_TABLE_UNIT_MODE,
	OPEN_BLOCK_RSMAP_SPOR_OLD_GR_NO_SAVE_VT_MODE,
} OpenBlockRSInitMode_t;

FW_BTCM0_SECTION void OpenBlockRSMapInit(OpenBlockRSInitVarEnum_t InitMode);
FW_BTCM0_SECTION void OpenBlockRSMapRecordRSDescription(U16 uwFWParityTagIdx, U8 ubHWParityTagIdx, U8 ubIsProgramParityInGR, U8 ubLastPageEncodeDone);
#if (RELEASED_FW)
FW_BTCM0_SECTION void OpenBlockRSMapTriggerFlow(void);
#else /* (RELEASED_FW) */
void OpenBlockRSMapTriggerFlow(void);
#endif /* (RELEASED_FW) */
FW_BTCM0_SECTION U8 OpenBlockRSMapIsFlowDoing(void);
FW_BTCM0_SECTION U8 OpenBlockRSMapCheckProgramParityFlag(void);
FW_BTCM0_SECTION U8 OpenBlockRSMapGetCurrentParityTag(void);
FW_BTCM0_SECTION void OpenBlockRSMapForceProgramParity(void);
FW_BTCM0_SECTION void OpenBlockRSMapBackupRSForce(void);
void OpenBlockRSMapFlow(OpenBlockRSBackupModeEnum_t ubMode);

U8 OpenBlockRSMapNeedToGetNewUnit(OpenBlockRSInitMode_t ubInitMode);
FW_BTCM0_SECTION U8 OpenBlockRSMapIsOldUnitCheckDone(void);
AOM_FTL_EXT U8 OpenBlockRSMapCheckAddOldUnitToFreePool(Unit_t uwUnit, OpenBlockRSInitMode_t ubInitMode);
AOM_FTL_EXT U8 OpenBlockRSMapEraseOpenBlockRSUnit(OpenBlockRSInitMode_t ubInitMode);
FW_BTCM0_SECTION U8 OpenBlockRaidECCMapCheckCurrentStateIdle(void);
FW_BTCM0_SECTION U8 OpenBlockRaidECCMapReturnIsCopyBusy(void);
AOM_RETRY_RS U8 RaidECCReturnIsOpenBlockRaidECCProgramBusy(void);
FW_BTCM0_SECTION void OpenBlockRaidECCClearProgramParityDoneBMP(U8 ubParityTagIdx);
FW_BTCM0_SECTION void OpenBlkRaidECCMapClearOpenBlkDoing(void);
AOM_RETRY_RS U8 OpenBlockRaidECCMapGetInformationForceSaveDoing(void);
void OpenBlockRaidECCMapCheckFlag(U8 ubMode, U32 ulUnitPTR, U8 ubIsSLCMode);
#endif /* _OPEN_BLOCK_RSMAP_API_H_ */

