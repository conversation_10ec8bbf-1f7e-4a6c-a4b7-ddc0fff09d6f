/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  err_hdl_fpl_TurboRain.h                                          	  */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/


/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "hal/fip/fip_api.h"
#include "common/fw_common.h"
#include "table/vbmap/vbmap_api.h"
#include "aom/aom_api.h"
/*
 * ---------------------------------------------------------------------------------------------------
 *   definitions
 * ---------------------------------------------------------------------------------------------------
 */
#define RETRY_TURBORAIN_BACKUP_RESTORE_TO_DRAMIRAM       	(0)


/*
 * ---------------------------------------------------------------------------------------------------
 *  enum
 * ---------------------------------------------------------------------------------------------------
 */
enum {
	RETRY_TURBORAIN_IDLE = 0,
	RETRY_TURBORAIN_START,
	RETRY_TURBORAIN_STATE_SCAN_ERR_FRAME,
	RETRY_TURBORAIN_STATE_CHECK_ERR_LDPC_FRM,
	RETRY_TURBORAIN_GENERATE_XORALL_AND_STAGE6_DATA,
	RETRY_TURBORAIN_INPUT_SB0_TO_SB4,
	RETRY_TURBORAIN_STATE_LOAD_TURBORAIN_LLR_TABLE,
	RETRY_TURBORAIN_TRIGGER_LDPC_DECODE,
	RETRY_TURBORAIN_CHECK_PASS,
	RETRY_TURBORAIN_CHECK_FAIL,
	RETRY_TURBORAIN_HANDLING_SB_RETRY,
	RETRY_TURBORAIN_STATE_RECEIVE_CQ,
	RETRY_TURBORAIN_STATE_DONE,

};

/*
 * ---------------------------------------------------------------------------------------------------
 *  data types
 * ---------------------------------------------------------------------------------------------------
 */
typedef struct {

	U8 ubState;
	U8 ubRSDecodeFlowLoopBreakFlag;
	U8 ubllr_index;
	U8 ubdecode_mode;
	///-----------------------simple information from PCA----------------------------
	U8  ubChannel;
	U8 	ubBank;
	U8  ublun;
	U8	ubPageType;
	U8	ubWLType;
	U8  ubDSPEnginePageType;
	///------------------------- PCA   --------------------------
	U32 ulFSA_ori;
	U32 ulFSA_Align;
	U32 ulVCA;
	///----------------------- Address  ----------------------------
	U32 ulbackup_addr_base[FRAMES_PER_PAGE];
	U32 ulbackup_addr;
	U32 ulSpareAddr_base[FRAMES_PER_PAGE];
	U32 ulSpareAddr;

	U32 ultmp_addr;
	U32 ultmp_SpareAddr;

	FlhMT_t  MTTemplate;
	FlhMT_t  MT;

	U8 ubErr_frm;

	U8 ubCurr_frm_idx;
	U8 ubCurrentLDPCFrameIdx;
	U16 uwLDPCCurrentErrMap;
	U8 ubUseQorOrNot;
	U8 ubBlkRefresh;
} TURBORAIN_TASK_t;

/*
 * ---------------------------------------------------------------------------------------------------
 *   macros
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *   extern global variables
 * ---------------------------------------------------------------------------------------------------
 */
extern TURBORAIN_TASK_t gTurboRainTask;

/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */

AOM_RETRY_SBRAID void RetryTurboRainMain(void);


