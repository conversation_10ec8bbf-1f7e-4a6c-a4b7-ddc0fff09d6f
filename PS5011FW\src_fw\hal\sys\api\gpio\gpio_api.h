#ifndef _GPIO_API_H_
#define _GPIO_API_H_

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "hal/sys/reg/sys_pd1_reg.h"

/*
 * ---------------------------------------------------------------------------------------------------
 *   macros
 * ---------------------------------------------------------------------------------------------------
 */
#if PS5017_EN
//[E13] 0x00 BIT[19:8] moved into [E17] 0x14h
#define	M_GPIO_SET_VDT_DETECTOR(x)		(R32_SYS1_GPIO[R32_SYS1_VDT_DET_EN] |= (x))
#define	M_GPIO_CLEAR_VDT_DETECTOR(x)	(R32_SYS1_GPIO[R32_SYS1_VDT_DET_EN] &= ~(x))
#elif PS5021_EN /* PS5017_EN */
#define	M_GPIO_SET_VDT_DETECTOR(x) // E21 RD: E21 doesn't have VDT isr. pmu will reset cpu directly. when VDT is enable.
#define	M_GPIO_CLEAR_VDT_DETECTOR(x) // E21 RD: E21 doesn't have VDT isr. pmu will reset cpu directly. when VDT is enable.
#else /* PS5017_EN */
#define	M_GPIO_SET_VDT_DETECTOR(x)		(R32_SYS1_GPIO[R32_SYS1_SYS_EDGE_DET_EN] |= (x))
#define	M_GPIO_CLEAR_VDT_DETECTOR(x)	(R32_SYS1_GPIO[R32_SYS1_SYS_EDGE_DET_EN] &= ~(x))
#endif /* PS5017_EN */

#if PS5021_EN
#define M_GPIO_CHECK_INT_EVENT()		(R32_SYS1_GPIO[R32_SYS1_GPIO_INT_SET] & GPIO_TRIG_EVENT_MASK)
#define M_GPIO_CLEAR_INT_EVENT(X)		(R32_SYS1_GPIO[R32_SYS1_GPIO_INT_SET] = (X))
#endif /* PS5021_EN */

#endif /* _GPIO_API_H_ */

