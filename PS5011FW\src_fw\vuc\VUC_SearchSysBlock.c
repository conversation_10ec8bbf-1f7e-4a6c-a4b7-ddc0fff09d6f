#include "hal/pic/uart/uart_api.h"
#include "nvme_api/nvme/shr_hal_nvme.h"
#include "host/VUC_handler.h"
#include "vuc/VUC_command.h"
#include "vuc/VUC_SearchSysBlock.h"

#if (BURNER_MODE_EN)
void VUC_SearchSysBlock(VUC_OPT_HCMD_PTR_t pCmd)
{
	if (TRUE == gFlhEnv.ulFlashDefaultType.BitMap.btNoSupport) {
		pCmd->ubState = CMD_ERROR;
		gVUCVar.ubVucCmdErrStatus = HOST_ERROR_FEATURE;
		return;
	}

	U8 ubSubfeature;
	ubSubfeature = pCmd->vuc_sqcmd.vendor.usrdef.ubSubFeature;

	if ( (ubSubfeature & VUC_SEARCH_CODE_BLOCK) ) {

	}
	if ((ubSubfeature & VUC_SEARCH_RUT)) {

	}
	if ((ubSubfeature & VUC_SEARCH_PGD)) {

	}

}
#endif /* BURNER_MODE_EN */
