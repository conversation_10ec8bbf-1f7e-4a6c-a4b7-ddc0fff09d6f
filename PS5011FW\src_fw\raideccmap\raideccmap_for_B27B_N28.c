
#include "env.h"
#include "ftl/ftl_api.h"
#include "raideccmap.h"
#include "raideccmap_api.h"
#include "raideccmap_for_B27B_N28.h"
#include "raideccmap_inline_api.h"
#include "typedef.h"

#if ((IM_B17) || (IM_B27A) || (IM_B27B) || (IM_N18) || (IM_N28) || (IM_B47R) || (IM_B37R))

U16 RaidECCMapGetParityTagIdxByCoord(RaidECCMapParityMapEnum_t ParityMapMode, U16 uwX, U16 uwY, U8 ubPlane)
{
	U16 uwFWParityTagIdx = 0;
	U16 uwParityTagBase = 0;
	U16 uwShiftBase = 0;
	U16 uwShiftOffset = 0;
	U16 uwModNum = 0;

	switch (ParityMapMode) {
	case RAIDECCMAP_PARITYMAP_MODE_GR_SLC:
		uwModNum = RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM;
#if (IM_B47R || IM_B37R)
		if (uwY % 2) {
			uwParityTagBase = RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM;
		}
		uwShiftBase   = ((uwY / 2) % uwModNum);
#elif (IM_B27A)
		uwShiftBase   = ( uwY % 2) ? 6 : 0;
#else /* (IM_B47R) */ /* (IM_B27A) */
		uwShiftBase   = ( uwY % uwModNum); //TODO: Change Magic Number
#endif /* (IM_B47R) */ /* (IM_B27A) */
		uwShiftOffset = uwX; //TODO: Change Magic Number
		break;
	case RAIDECCMAP_PARITYMAP_MODE_GR_XLC:
	case RAIDECCMAP_PARITYMAP_MODE_GC:
		uwModNum = RAIDECCMAP_1WL_PROTECTION_IN_XLC_PARITY_TAG_IDX_NUM;
		if (uwY % 2) {
			uwParityTagBase = RAIDECCMAP_1WL_PROTECTION_IN_XLC_PARITY_TAG_IDX_NUM;
		}

		uwShiftBase = ((uwY / 2) % uwModNum); //TODO: Change Magic Number
#if (IM_B27A)
		uwShiftOffset = ((uwX / 3) * 3) + (uwX % 3);
#elif (IM_B27B)
		uwShiftOffset = ((uwX / 3) * 3) + (3 - 1 - (uwX % 3));
#elif (IM_N28)
		uwShiftOffset = ((uwX / 4) * 4) + (uwX % 4);
#elif (IM_B47R || IM_B37R)
		uwShiftOffset = uwX;
#endif
		break;
	case RAIDECCMAP_PARITYMAP_MODE_TABLE:
	default:
		break;
	}

	uwFWParityTagIdx = uwParityTagBase + ((uwShiftBase + uwShiftOffset) % uwModNum);

	return uwFWParityTagIdx;
}


U16 RaidECCMapGetParityTagIdxByProgramOrderTable(RaidECCMapParityMapEnum_t ParityMapMode, U32 ulPlaneIdx)
{
	U16 uwFWTagIdx = 0;
	U8 ubPlaneOffset = 0;
	if (RAIDECCMAP_PARITYMAP_MODE_TABLE == ParityMapMode) {
		uwFWTagIdx = (ulPlaneIdx / (gRS.ubRSTableDataNum + RAIDECC_PARITY_PAGE_NUM)) % 2;
	}
	else if (RAIDECCMAP_PARITYMAP_MODE_GR_SLC == ParityMapMode) {
		U16 uwSuperPageIdx = ulPlaneIdx / gubPlanesPerSuperPage;
		U8 uwX, uwY = 0;
#if IM_B27A
		uwX = uwSuperPageIdx % 18;
		uwY = uwSuperPageIdx / 18;
#else/*IM_B27A*/
		uwX = uwSuperPageIdx % RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM;
		uwY = uwSuperPageIdx / RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM;
#endif/*IM_B27A*/
		uwFWTagIdx = RaidECCMapGetParityTagIdxByCoord(ParityMapMode, uwX, uwY, ubPlaneOffset);
	}
	else {
		U16 uwSuperPageIdx = 0;
		U8 uwX, uwY = 0;
		FTLPlaneInx2Physical(ulPlaneIdx, &uwSuperPageIdx, &ubPlaneOffset);

		uwX = FTLGetCoord(uwSuperPageIdx, IM_GETCOORD_X_VAL);
		uwY = FTLGetCoord(uwSuperPageIdx, IM_GETCOORD_Y_VAL);
		uwFWTagIdx = RaidECCMapGetParityTagIdxByCoord(ParityMapMode, uwX, uwY, ubPlaneOffset);
	}
	return uwFWTagIdx;
}

U16 RaidECCMapInvertCoord( U8 ubX, U16 uwY )
{
	U16 uwSuperpageIdx = 0;
	U16 uwPageIdx = 0xFFFF;
	U8 ubCase = 0xFF;
#if (IM_B47R || IM_B37R)
	U8 ubCheckFlashID = 5;
#else /* (IM_B47R) */
	U8 ubCheckFlashID = 4;
#endif /* (IM_B47R) */
#if (IM_B27B || IM_B27A)
	U16 uwWordLine = uwY;
	U16 ubWordLineBase = 0xFF;
#endif

#if(MICRON_FSP_EN)
	switch (gFlhEnv.ubFlashID[ubCheckFlashID]) {
#if 0
	case 0xA1:	//B16A
	case 0xA6:  //B17A
		if ((uwY) < IM_B17_AXIS_X_SECTION_1) {
			if ((ubX) % 3) {
				uwSuperpageIdx = IM_B17_AXIS_X_NULL_VALUE;
			}
			else {
				uwSuperpageIdx = ((uwY * 4) + (ubX / 3));
			}
		}
		else if ((uwY) < IM_B17_AXIS_X_SECTION_2) {
			if (2 == ((ubX) % 3)) {
				uwSuperpageIdx = IM_B17_AXIS_X_NULL_VALUE;
			}
			else {
				uwSuperpageIdx = (((uwY - IM_B17_AXIS_X_SECTION_1) * 8) + ((ubX / 3) + (ubX % 3)) + IM_B17_SECTION_1);
			}
		}
		else if ((uwY) < IM_B17_AXIS_X_SECTION_4) {
			if ((ubX) % 3) {
				uwSuperpageIdx = ( ((uwY - IM_B17_AXIS_X_SECTION_2) * 12) + ( ((ubX / 3) * 3) + ( 2 - (ubX % 3)) ) + IM_B17_SECTION_3 );
			}
			else {
				if ((uwY) < IM_B17_AXIS_X_SECTION_3) {
					uwSuperpageIdx = (((uwY - IM_B17_AXIS_X_SECTION_2) * 4) + (ubX / 3) + IM_B17_SECTION_2) ;
				}
				else {
					uwSuperpageIdx = (((uwY - IM_B17_AXIS_X_SECTION_3) * 12) + ((ubX / 3) * 3) + 62) ;
				}
			}

		}
		else if ((uwY) < IM_B17_AXIS_X_SECTION_5) {  //189
			if ((ubX) % 3) {
				uwSuperpageIdx = ( ((uwY - IM_B17_AXIS_X_SECTION_4) * 16) + ( ((ubX / 3) * 3) + ( 2 - (ubX % 3)) ) + IM_B17_SECTION_4 );
			}
			else {
				uwSuperpageIdx = (((uwY - IM_B17_AXIS_X_SECTION_3) * 12) + ((ubX / 3) * 3) + 62) ;
			}
		}
		else if ((uwY) < IM_B17_AXIS_X_SECTION_6) {  //192
			if ((ubX) % 3) {
				uwSuperpageIdx = ( ((uwY - IM_B17_AXIS_X_SECTION_5) * 8) + ( ((ubX / 3) * 2) + ( 2 - (ubX % 3)) ) + 2268 );
			}
			else {
				uwSuperpageIdx = (((uwY - IM_B17_AXIS_X_SECTION_3) * 12) + ((ubX / 3) * 3) + 62) ;
			}
		}
		else if ((uwY) < IM_B17_AXIS_X_SECTION_7) {  //195
			if ( 2 == ((ubX) % 3)) {
				uwSuperpageIdx = IM_B17_AXIS_X_NULL_VALUE;
			}
			else {
				uwSuperpageIdx = ( ((uwY - IM_B17_AXIS_X_SECTION_6) * 16) + ( ((ubX / 3) * 4) + (ubX % 3) ) + 2268 );
			}
		}
		else {
			if ((ubX) % 3) {
				uwSuperpageIdx = IM_B17_AXIS_X_NULL_VALUE;
			}
			else {
				uwSuperpageIdx = ((uwY * 4) + (ubX / 3) + 2292);
			}
		}
		break; // break Case B17A
#endif
#if IM_B27A
	case 0xA2:	//B27A
		switch (ubX % 3) {
		case 2: //X
			if (uwWordLine == 1 || uwWordLine == 47 || uwWordLine == 49 || uwWordLine == 51 || uwWordLine == 97) {
				break;
			}
		case 1: //U
			if (uwWordLine == 0 || uwWordLine == 98) {
				break;
			}
			else if (uwWordLine == 45) {
				ubWordLineBase = 2412;
				uwWordLine = 0;
				ubCase = 2;
				break;
			}
			else if (uwWordLine == 46 || uwWordLine == 48) {
				ubWordLineBase = 2502;
				uwWordLine -= 46;
				ubCase = 3;
				break;
			}
			else if (uwWordLine == 50) {
				ubWordLineBase = 2682;
				uwWordLine = 0;
				ubCase = 2;
				break;
			}
			else if (uwWordLine == 95) {
				ubWordLineBase = 5058;
				uwWordLine = 0;
				ubCase = 2;
				break;
			}
			else if (uwWordLine == 96) {
				ubWordLineBase = 5148;
				uwWordLine = 0;
				ubCase = 3;
				break;
			}
			else if (!(uwWordLine == 1 || uwWordLine == 47 || uwWordLine == 49 || uwWordLine == 51 || uwWordLine == 97)) {
				uwWordLine += 2;
			}
		case 0: //L
			if ((uwWordLine >= 4) && (uwWordLine <= 46)) { // 4 ~ 46
				ubWordLineBase = 90;
				uwWordLine -= 4;
				ubCase = 2;
			}
			else if ((uwWordLine >= 53) && (uwWordLine <= 96)) { // 53 ~ 96
				ubWordLineBase = 2682;
				uwWordLine -= 53;
				ubCase = 2;
			}
			else if (uwWordLine == 0) { // 0
				ubWordLineBase = 0;
				ubCase = 0;
			}
			else if (uwWordLine == 1) { // 1
				ubWordLineBase = 18;
				ubCase = 1;
			}
			else if (uwWordLine == 2) { // 2
				ubWordLineBase = 54;
				ubCase = 0;
			}
			else if (uwWordLine == 3) { // 3
				ubWordLineBase = 72;
				ubCase = 0;
			}
			else if (uwWordLine == 47) { // 47
				ubWordLineBase = 2466;
				ubCase = 1;
			}
			else if (uwWordLine == 48) { // 48
				ubWordLineBase = 2414;
				uwWordLine = 0;
				ubCase = 4;
			}
			else if (uwWordLine == 49) { // 49
				ubWordLineBase = 2592;
				ubCase = 1;
			}
			else if (uwWordLine == 50) { // 50
				ubWordLineBase = 2574;
				ubCase = 0;
			}
			else if (uwWordLine == 51) { // 51
				ubWordLineBase = 2646;
				ubCase = 1;
			}
			else if (uwWordLine == 52) { // 52
				ubWordLineBase = 2628;
				ubCase = 0;
			}
			else if (uwWordLine == 97) { // 97
				ubWordLineBase = 5112;
				ubCase = 1;
			}
			else if (uwWordLine == 98) { // 98
				ubWordLineBase = 5060;
				uwWordLine = 0;
				ubCase = 4;
			}
			break;
		}
		if (ubCase == 0) { // R0
			uwSuperpageIdx = ubWordLineBase + (ubX / 3);
		}
		else if (ubCase == 1) {
			uwSuperpageIdx = ubWordLineBase + ((ubX / 3) * 2 + (ubX % 3));
		}
		else if (ubCase == 2) {
			uwSuperpageIdx = ubWordLineBase + ((ubX / 3) * 3 + (2 - (ubX % 3))) + uwWordLine * 54;
		}
		else if (ubCase == 3) {
			uwSuperpageIdx = ubWordLineBase + uwWordLine * 18 + ((ubX / 3) * 2 + (2 - (ubX % 3)));
		}
		else if (ubCase == 4) {
			uwSuperpageIdx = ubWordLineBase + (ubX / 3) * 3;
		}
		break;
#elif IM_B27B

	case 0xE6:  //B27B
		if (0 == uwWordLine) {
			uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_0_PAGE_BASE;
			ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_0;
			ubWordLineBase = uwWordLine;
			if (2 > (ubX % 3)) {	//Upper, Xtra Page
				uwSuperpageIdx = IM_B27B_AXIS_INVALID_PAGE_IDX;
			}
		}
		else if (1 == uwWordLine) {
			uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_1_PAGE_BASE;
			ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_1;
			if (0 == (ubX % 3)) {	//Xtra Page
				uwSuperpageIdx = IM_B27B_AXIS_INVALID_PAGE_IDX;
			}
		}
		else if ((2 == uwWordLine) || (3 == uwWordLine)) {
			if (2 > (ubX % 3)) {	//Upper, Xtra Page
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_2_3_UPPER_EXTRA_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_2;
				ubWordLineBase = IM_B27B_AXIS_WORDLINE_2_3_WORDLINE_BASE;
			}
			else {	//Lower Page
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_2_3_LOWER_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_0;
				ubWordLineBase = IM_B27B_AXIS_WORDLINE_2_3_WORDLINE_BASE;
			}
		}
		else if ((4 <= uwWordLine) && (44 >= uwWordLine)) {
			uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_4_44_PAGE_BASE;
			ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_2;
			ubWordLineBase = IM_B27B_AXIS_WORDLINE_4_46_WORDLINE_BASE;
		}
		else if (45 == uwWordLine) {
			if (2 > (ubX % 3)) {	//Upper, Xtra Page
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_45_UPPER_EXTRA_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_3;
			}
			else {	//Lower Page
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_45_46_LOWER_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_2;
				ubWordLineBase = IM_B27B_AXIS_WORDLINE_4_46_WORDLINE_BASE;
			}
		}
		else if (46 == uwWordLine) {
			if (2 > (ubX % 3)) {	//Upper, Xtra Page
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_46_UPPER_EXTRA_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_4;
			}
			else {	//Lower Page
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_45_46_LOWER_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_2;
				ubWordLineBase = IM_B27B_AXIS_WORDLINE_4_46_WORDLINE_BASE;
			}
		}
		else if (47 == uwWordLine) {
			uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_47_PAGE_BASE;
			ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_5;
			if (0 == (ubX % 3)) {	//Xtra Page
				uwSuperpageIdx = IM_B27B_AXIS_INVALID_PAGE_IDX;
			}
		}
		else if (48 == uwWordLine) {
			if (2 > (ubX % 3)) {	//Upper, Xtra Page
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_48_UPPER_EXTRA_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_3;
			}
			else {	//Lower Page
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_48_LOWER_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_4;
			}
		}
		else if (49 == uwWordLine) {
			uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_49_PAGE_BASE;
			ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_5;
			if (0 == (ubX % 3)) {	//Xtra Page
				uwSuperpageIdx = IM_B27B_AXIS_INVALID_PAGE_IDX;
			}
		}
		else if (50 == uwWordLine) {
			if (2 > (ubX % 3)) {	//Upper, Xtra Page
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_50_UPPER_EXTRA_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_2;
				ubWordLineBase = uwWordLine;
			}
			else {	//Lower Page
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_50_LOWER_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_0;
				ubWordLineBase = uwWordLine;
			}
		}
		else if (51 == uwWordLine) {
			uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_51_PAGE_BASE;
			ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_1;
			if (0 == (ubX % 3)) {	//Xtra Page
				uwSuperpageIdx = IM_B27B_AXIS_INVALID_PAGE_IDX;
			}
		}
		else if (52 == uwWordLine) {
			if (2 > (ubX % 3)) {	//Upper, Xtra Page
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_52_UPPER_EXTRA_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_2;
				ubWordLineBase = IM_B27B_AXIS_WORDLINE_52_96_WORDLINE_BASE;
			}
			else {	//Lower Page
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_52_LOWER_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_0;
				ubWordLineBase = uwWordLine;
			}
		}
		else if (53 == uwWordLine) {
			if (2 > (ubX % 3)) {	//Upper, Xtra Page
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_53_UPPER_EXTRA_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_2;
				ubWordLineBase = IM_B27B_AXIS_WORDLINE_52_96_WORDLINE_BASE;
			}
			else {	//Lower Page
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_53_LOWER_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_4;
			}
		}
		else if ((54 <= uwWordLine) && (94 >= uwWordLine)) {
			uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_54_94_PAGE_BASE;
			ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_2;
			ubWordLineBase = IM_B27B_AXIS_WORDLINE_52_96_WORDLINE_BASE;
		}
		else if (95 == uwWordLine) {
			if (2 > (ubX % 3)) {	//Upper, Xtra Page
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_95_UPPER_EXTRA_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_3;
			}
			else {	//Lower Page
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_95_96_LOWER_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_2;
				ubWordLineBase = IM_B27B_AXIS_WORDLINE_52_96_WORDLINE_BASE;
			}
		}
		else if (96 == uwWordLine) {
			if (2 > (ubX % 3)) {	//Upper, Xtra Page
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_96_UPPER_EXTRA_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_4;
			}
			else {	//Lower Page
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_95_96_LOWER_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_2;
				ubWordLineBase = IM_B27B_AXIS_WORDLINE_52_96_WORDLINE_BASE;
			}
		}
		else if (97 == uwWordLine) {
			uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_97_PAGE_BASE;
			ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_5;
			if (0 == (ubX % 3)) {	//Xtra Page
				uwSuperpageIdx = IM_B27B_AXIS_INVALID_PAGE_IDX;
			}
		}
		else if (98 == uwWordLine) {
			uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_98_PAGE_BASE;
			ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_4;
			if (2 > (ubX % 3)) {	//Upper, Xtra Page
				uwSuperpageIdx = IM_B27B_AXIS_INVALID_PAGE_IDX;
			}
		}
		else {
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
		}
		if (IM_B27B_AXIS_INVALID_PAGE_IDX == uwSuperpageIdx) {
			break;
		}
		switch (ubCase) {
		case IM_B27B_AXIS_X_Y_TO_PAGE_CASE_0:
			uwSuperpageIdx += ((uwWordLine - ubWordLineBase) * 12  + (ubX / 3));
			break;
		case IM_B27B_AXIS_X_Y_TO_PAGE_CASE_1:
			uwSuperpageIdx += ((ubX / 3) * 2 + (2 - (ubX % 3)));
			break;
		case IM_B27B_AXIS_X_Y_TO_PAGE_CASE_2:
			uwSuperpageIdx += (((uwWordLine - ubWordLineBase) - ((2 == (ubX % 3)) ? 2 : 0)) * 36  + ubX);
			break;
		case IM_B27B_AXIS_X_Y_TO_PAGE_CASE_3:
			uwSuperpageIdx += ((ubX / 3) * 4 + (ubX % 3));
			break;
		case IM_B27B_AXIS_X_Y_TO_PAGE_CASE_4:
			uwSuperpageIdx += ubX;
			break;
		case IM_B27B_AXIS_X_Y_TO_PAGE_CASE_5:
			uwSuperpageIdx += ((ubX / 3) * 4 + (2 - (ubX % 3)));
			break;
		default:
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
			break;
		}
		break; // break Case B27B

#elif (IM_N28)
	case 0xC6: // N28
		ubCase = 0xFF;
		U8 ubOffset = 0;
		U8 ubLXUNumber = ubX % 4; // LUXT
		U16 uwPageIdx = 0xFFFF, uwBase;

		switch (ubLXUNumber) {
		case 2: //X
		case 1: //U
			if (uwY == 0 || uwY == 98) {
				break;
			}
			uwY += 1;
		case 0: //L
			if ((uwY > 4) && (uwY < 51)) { // 5 ~ 50
				uwBase = 132;
				uwY -= 5;
				ubCase = 2;
			}
			else if ((uwY > 56) && (uwY < 98)) { // 57 ~ 97
				uwBase = 2580;
				uwY -= 57;
				ubCase = 2;
			}
			else if (uwY == 0 || uwY == 1) { // 0 1
				uwBase = 0;
				ubCase = 0;
			}
			else if ((uwY > 1) && (uwY < 5)) { // 2 3 4
				uwBase = 24;
				uwY -= 2;
				ubCase = 1;
			}
			else if (uwY == 51 || uwY == 52) { // 51 52
				uwBase = 2340;
				uwY -= 51;
				ubCase = 1;
			}
			else if (uwY == 53) { // 53
				uwBase = 2412;
				uwY = 0;
				ubCase = 2;
			}
			else if (uwY == 54) { // 54
				uwBase = 2460;
				uwY = 0;
				ubCase = 1;
			}
			else if (uwY == 55) { // 55
				uwBase = 2496;
				uwY = 0;
				ubCase = 2;
			}
			else if (uwY == 56) { //56
				uwBase = 2544;
				uwY = 0;
				ubCase = 1;
			}
			else if (uwY == 98) { // 98
				uwBase = 4548;
				uwY = 0;
				ubCase = 3;
			}
			break;
		case 3: //T
			ubCase = 2;
			if ((uwY > 1) && (uwY < 47)) { // 2 ~ 46
				uwBase = 132;
				uwY -= 2;
			}
			else if ((uwY > 53) && (uwY < 95)) { // 54 ~ 94
				uwBase = 2580;
				uwY -= 54;
			}
			else if (uwY == 48) { // 48
				uwBase = 2292;
				uwY = 0;
			}
			else if (uwY == 50) { // 50
				uwBase = 2412;
				uwY = 0;
			}
			else if (uwY == 52) { // 52
				uwBase = 2496;
				uwY = 0;
			}
			else if (uwY == 95 || uwY == 96) { // 95 96
				uwBase = 4548;
				if (uwY == 96) {
					ubOffset = 3;
				}
				uwY = 0;
				ubCase = 3;
			}
			else {
				ubCase = 0xFF;
			}
			break;
		}
		if (ubCase == 0) { // R0
			uwPageIdx = uwY * 12 + (ubX >> 2);
		}
		else if (ubCase == 1) {
			uwPageIdx = uwBase + (ubX >> 2) * 3 + ((3 - (((ubX & 03) % 3))) % 3) + uwY * 36; // R1
		}
		else if (ubCase == 2) {
			uwPageIdx = uwBase + (ubX & ~(0x3)) + ((4 - (ubX & 0x3)) & 0x3) + uwY * 48; // R2
		}
		else if (ubCase == 3) {
			uwPageIdx = uwBase + ((ubX >> 2) * 5) + ((4 - (ubX & 0x3)) & 0x3) + ubOffset; // R3 missing Y
		}

		uwSuperpageIdx = uwPageIdx;
		break;
#endif

#if (IM_B47R)
	case 0x30:	//B47R
		if (0 == uwY) {
			if (0 == (ubX % 3)) {
				ubCase = 0;
				uwPageIdx = 0;
			}
		}
		else if (uwY < 88) {
			ubCase = 1;
			uwPageIdx = IM_B47R_SECTION_1;
			uwY -= 1;
		}
		else if (uwY < 90) {
			if ((ubX % 3) < 2) {
				ubCase = 2;
				uwPageIdx = IM_B47R_SECTION_2;
				uwY -= 88;
			}
		}
		else if (uwY < 177) {
			ubCase = 1;
			uwPageIdx = IM_B47R_SECTION_3;
			uwY -= 90;
		}
		else if (177 == uwY) {
			if (0 == (ubX % 3)) {
				ubCase = 0;
				uwPageIdx = IM_B47R_SECTION_4;
			}
		}

		switch (ubCase) {
		case 0:
			uwPageIdx += (ubX / 3);
			break;
		case 1:
			uwPageIdx += (uwY * 12 + ubX);
			break;
		case 2:
			uwPageIdx += (uwY * 8 + (ubX / 3 * 2) + (ubX % 3));
			break;
		default:
			break;
		}
		uwSuperpageIdx = uwPageIdx;
		break;
#elif (IM_B37R)
	case 0x10:	//B37R
		if (0 == uwY) {
			if (0 == (ubX % 3)) {
				ubCase = 0;
				uwPageIdx = 0;
			}
		}
		else if (uwY < 64) {
			ubCase = 1;
			uwPageIdx = IM_B37R_SECTION_1;
			uwY -= 1;
		}
		else if (uwY < 66) {
			if ((ubX % 3) < 2) {
				ubCase = 2;
				uwPageIdx = IM_B37R_SECTION_2;
				uwY -= 66;
			}
		}
		else if (uwY < 129) {
			ubCase = 1;
			uwPageIdx = IM_B37R_SECTION_3;
			uwY -= 129;
		}
		else if (129 == uwY) {
			if (0 == (ubX % 3)) {
				ubCase = 0;
				uwPageIdx = IM_B37R_SECTION_4;
			}
		}

		switch (ubCase) {
		case 0:
			uwPageIdx += (ubX / 3);
			break;
		case 1:
			uwPageIdx += (uwY * 12 + ubX);
			break;
		case 2:
			uwPageIdx += (uwY * 8 + (ubX / 3 * 2) + (ubX % 3));
			break;
		default:
			break;
		}
		uwSuperpageIdx = uwPageIdx;
		break;
#endif	/* (IM_B37R) */
	}
#endif /*(MICRON_FSP_EN)*/
	return uwSuperpageIdx;
}

U8 IsInValidCoordPage(int x, int y)
{
#if IM_B27A
	switch (y) {
	case 0:
	case 98:
		if ((x % 3) != 0) {
			return TRUE;
		}
		break;
	case 1:
	case 47:
	case 49:
	case 51:
	case 97:
		if ((x % 3) == 2) {
			return TRUE;
		}
		break;
	}
	return FALSE;
#elif IM_B27B

	switch (y) {
	case 0:
	case 98:
		if (((x + 1) % 3) != 0 ) {
			return TRUE;
		}
		break;
	case 1:
	case 47:
	case 49:
	case 51:
	case 97:
		if (((x + 1) % 3) == 1 ) {
			return TRUE;
		}
		break;
	}

	return FALSE;
#elif IM_N28	// N28
	switch (y) {
	case 0:
	case 98:
		if ((x % 4) != 0 ) {
			return TRUE;
		}
		break;
	case 1:
	case 47:
	case 49:
	case 51:
	case 53:
	case 97:
		if (((x + 1) % 4) == 0 ) {
			return TRUE;
		}
		break;
	default:
		break;
	}
	return FALSE;
#else	// B47R
	switch (y) {
	case 0:
	case 177:
		if (0 != (x % 3)) {
			return TRUE;
		}
		break;
	case 88:
	case 89:
		if ((x % 3) > 1) {
			return TRUE;
		}
		break;
	default:
		break;
	}
	return FALSE;
#endif
}

U32 RaidECCMapXLCFindLastSameTagPageByCoord(U8 ubX, U16 uwY)
{

	U8 SharedPageOrderReverse = (IM_B27B) ? TRUE : FALSE; // B27: XUL,  N28:LUXT
	U8 PagePerWL = (IM_B47R || IM_B37R) ? 12 : ((IM_B27A) ? 54 : ((IM_B27B) ? 36 : 48));
	U8 PagePer2WL = PagePerWL * 2;
	U16 PageIdxInProgramMapMatrix = uwY * PagePerWL + ubX; // counting order:  (0,0), (1,0), (2,0)...
	U16 CurrentPageIdx;
	U16 TargetX = 0, TargetY = 0; // For Backward / Foreward tacking respectively
#if (IM_B27A || IM_B27B || IM_B47R || IM_B37R)
	U16 ForeTargetX = 0, ForeTargetY = 0; // For Backward / Foreward tacking respectively
#endif

	if (PageIdxInProgramMapMatrix < PagePer2WL) { // 2WL protection
		return 0xFFFF;
	}

	CurrentPageIdx = RaidECCMapInvertCoord(ubX, uwY);

	if (SharedPageOrderReverse) {
#if (IM_B27B || IM_B47R || IM_B37R)
		U8 XLCPageNum = (IM_N28) ? 4 : 3; // LXU = 3 ,  LXUT = 4
		U8 TagReverseShiftInXLCPages = 0;
		U8 looptime = 0;
		//=============== Backward tracking ================
		TargetY = uwY;
		TargetX = ubX;
		U16 LastTargetX = 0xFFFF, LastTargetY = 0xFFFF;
		U16 backward_search_page = 0xFFFF;
		do {
			//Calculate tag shift in an XLC page interval (due to reverse order)
			if ((TargetX + 1) % XLCPageNum) {
				TagReverseShiftInXLCPages = XLCPageNum - ((TargetX + 1) % XLCPageNum);
			}
			else {
				TagReverseShiftInXLCPages = 0;
			}

			if (TargetY < 2) { // WL0 WL1 are pioneers
				break;
			}
			// Handle x : consider TagReverseShiftInXLCPages, there have 3 cases:
			// case1: If this tag is not the last one of its XLC interval, just do x-1
			// case2: Otherwise, do x+= (2*XLCPageNum)-1, since it was the first tag of its XLC interval last time (which interval's location is next to the current interval).
			// case3: In case2, it is neccessary to further check whether x exceed WL page num, if yes, it means that this tag has been rotated, do x = (XLCPageNum - 1).
			if (TagReverseShiftInXLCPages != (XLCPageNum - 1)) {
				TargetX --;
			}
			else {
				TargetX = TargetX + ((2 * XLCPageNum) - 1);
				if (TargetX > PagePerWL) {
					TargetX = (XLCPageNum - 1);
				}
			}
			// Handle y: backtracking 2 WLs before current WL
			TargetY -= 2;

			// Find out 2 valid pre pages.
			// Calculate twice, pick the larger one.
			// For B27 special case, for example:  source = 1896 (0,54), target1 = 1777 (5,52), target2 = 1792 (4,50)
			if ((!IsInValidCoordPage(TargetX, TargetY)) && (RaidECCMapInvertCoord(TargetX, TargetY) <  CurrentPageIdx)) {
				looptime++;
				if (looptime == 1) {
					// Store 1st  valid result
					LastTargetX = TargetX;
					LastTargetY = TargetY;
					backward_search_page = RaidECCMapInvertCoord(LastTargetX, LastTargetY);
				}
				else if (looptime == 2) {
					// Compare results:
					// basically treat LastTarget as the final result, if (TargetX,TargetY) is bigger then LastTarget, then update LastTarget
					if (RaidECCMapInvertCoord(TargetX, TargetY) > RaidECCMapInvertCoord(LastTargetX, LastTargetY)) {
						LastTargetX = TargetX;
						LastTargetY = TargetY;
					}
					backward_search_page = RaidECCMapInvertCoord(TargetX, TargetY);
				}
			}
		} while (looptime < 2);

		if (backward_search_page == 0xFFFF) {
			return 0xFFFF;
		}
		TargetX = LastTargetX;
		TargetY = LastTargetY;

		//=============== Forward tracking ================ (B27 special case: page 1692 1789 1776)
		ForeTargetY = uwY;
		ForeTargetX = ubX;
		looptime = 0;
		LastTargetX = 0xFFFF, LastTargetY = 0xFFFF;
		U16 forward_search_page = 0xFFFF;

		// Forward tracking finding one result while backward tracking trying to find two.
		// Once this result page of forward tracking bigger then current page, end search.
		do {
			//Calculate tag shift in an XLC page interval (due to reverse order)
			if ((ForeTargetX + 1) % XLCPageNum) {
				TagReverseShiftInXLCPages = XLCPageNum - ((ForeTargetX + 1) % XLCPageNum);
			}
			else {
				TagReverseShiftInXLCPages = 0;
			}
			if (ForeTargetY > 96 ) { // WL97 WL98 are final WLs
				break;
			}

			// Handle x : consider TagReverseShiftInXLCPages, there have 3 cases:
			// case1: If this tag is not the first one of its XLC interval, just do x+1
			// case2: Otherwise, do x-= ((2*XLCPageNum)-1)
			// case3: In case2, it is neccessary to further check whether x will be underflow (<0), if yes, it means that this tag has been rotated, do x = PagePerWL-(XLCPageNum - 1).
			if (TagReverseShiftInXLCPages != 0) {
				ForeTargetX ++;
			}
			else {
				if (((2 * XLCPageNum) - 1) <= ForeTargetX) {
					//case2
					ForeTargetX = ForeTargetX - ((2 * XLCPageNum) - 1);
				}
				else {
					//case3: under flow, tag rotated
					ForeTargetX = PagePerWL - XLCPageNum;
				}
			}
			// Handle y: forward tracking 2 WLs after current WL
			ForeTargetY += 2;

			if (RaidECCMapInvertCoord(ForeTargetX, ForeTargetY) <  CurrentPageIdx) {
				forward_search_page = RaidECCMapInvertCoord(ForeTargetX, ForeTargetY);
			}
		} while (IsInValidCoordPage(ForeTargetX, ForeTargetY));

		//========= compare Backward and Forward results ===============
		if ((forward_search_page != 0xFFFF) && (forward_search_page > backward_search_page)) {
			TargetY = ForeTargetY;
			TargetX = ForeTargetX;
		}

#endif
	}
	else {
#if IM_B27A
		U8 ublooptime = 0;
		//=============== Backward tracking ================
		TargetY = uwY;
		TargetX = ubX;
		U16 uwLastTargetX = 0xFF, uwLastTargetY = 0xFF;
		U16 uwBackwardSearchPage = IM_B27A_AXIS_INVALID_PAGE_IDX;
		do {
			if (TargetY < 2) { // WL0 WL1 are pioneers
				break;
			}
			// Handle x : consider TagReverseShiftInXLCPages, there have 3 cases:
			// case1: If this tag is not the last one of its XLC interval, just do x-1
			// case2: Otherwise, do x+= (2*XLCPageNum)-1, since it was the first tag of its XLC interval last time (which interval's location is next to the current interval).
			// case3: In case2, it is neccessary to further check whether x exceed WL page num, if yes, it means that this tag has been rotated, do x = (XLCPageNum - 1).
			TargetX++;
			if (TargetX == PagePerWL) {
				TargetX = 0;
			}

			// Handle y: backtracking 2 WLs before current WL
			TargetY -= 2;

			// Find out 2 valid pre pages.
			// Calculate twice, pick the larger one.
			// For B27 special case, for example:  source = 1896 (0,54), target1 = 1777 (5,52), target2 = 1792 (4,50)
			if ((!IsInValidCoordPage(TargetX, TargetY)) && (RaidECCMapInvertCoord(TargetX, TargetY) < CurrentPageIdx)) {
				ublooptime++;
				if (ublooptime == 1) {
					// Store 1st  valid result
					uwLastTargetX = TargetX;
					uwLastTargetY = TargetY;
					uwBackwardSearchPage = RaidECCMapInvertCoord(uwLastTargetX, uwLastTargetY);
				}
				else if (ublooptime == 2) {
					// compare results: °?Ľť¤W??¸?ĽHlast time Ź°?uĽý, ?p?GTargetX TargetY ¤?last time ¤j, ¨?ť?­n§??¨TargetX TargetY
					if (RaidECCMapInvertCoord(TargetX, TargetY) > RaidECCMapInvertCoord(uwLastTargetX, uwLastTargetY)) {
						uwLastTargetX = TargetX;
						uwLastTargetY = TargetY;
					}
					uwBackwardSearchPage = RaidECCMapInvertCoord(TargetX, TargetY);
				}
			}
		} while (ublooptime < 2);

		if (uwBackwardSearchPage == IM_B27A_AXIS_INVALID_PAGE_IDX) {
			return IM_B27A_AXIS_INVALID_PAGE_IDX;
		}
		TargetX = uwLastTargetX;
		TargetY = uwLastTargetY;

		//=============== Forward tracking ================ (B27 special case: page 1656 1789 1776)
		ForeTargetY = uwY;
		ForeTargetX = ubX;
		ublooptime = 0;
		uwLastTargetX = 0xFF, uwLastTargetY = 0xFF;
		U16 uwForwardSearchPage = IM_B27A_AXIS_INVALID_PAGE_IDX;

		//?š¤U§ä??rule¤?¸ű????¤@?I, °?Ľť¤W§ä¤@?¸´NĽiĽH¤F. ?p?G?o­?¤H§ä°_¨?´NŹO¤j?ócurrent,´N???ľ¨Ť

		do {
			if (ForeTargetY > 96) { // WL97 WL98 are final WLs
				break;
			}

			// Handle x : consider TagReverseShiftInXLCPages, there have 3 cases:
			// case1: If this tag is not the first one of its XLC interval, just do x+1
			// case2: Otherwise, do x-= ((2*XLCPageNum)-1)
			// case3: In case2, it is neccessary to further check whether x will be underflow (<0), if yes, it means that this tag has been rotated, do x = PagePerWL-(XLCPageNum - 1).

			if (ForeTargetX == 0) {
				ForeTargetX = PagePerWL - 1;
			}
			else {
				ForeTargetX--;
			}
			// Handle y: forward tracking 2 WLs after current WL
			ForeTargetY += 2;

			if (RaidECCMapInvertCoord(ForeTargetX, ForeTargetY) < CurrentPageIdx) {
				uwForwardSearchPage = RaidECCMapInvertCoord(ForeTargetX, ForeTargetY);
			}
		} while (IsInValidCoordPage(ForeTargetX, ForeTargetY));

		//========= compare Backward and Forward results ===============
		if ((uwForwardSearchPage != IM_B27A_AXIS_INVALID_PAGE_IDX) && (uwForwardSearchPage > uwBackwardSearchPage)) {
			TargetY = ForeTargetY;
			TargetX = ForeTargetX;
		}
#else //IM_B27A
		// N28: according to its program order arrangement, it's not nessessary to consider forward tracking case.
		TargetY = uwY;
		TargetX = ubX;
		//=============== Backward tracking ================
		do {
			if (TargetY < 2) { // WL0 WL1 are pioneers
				return 0xFFFF;
			}
			// Handle x : x+1
			TargetX++;
			if (TargetX == PagePerWL) {
				TargetX = 0;
			}
			// Handle y: backtracking 2 WLs before current WL
			TargetY -= 2;
			// IsInValidCoordPage indicates whether (x,y) is an empty page (non-existent), if yes, do the calculation again: backtracking 2 WLs before current WL.
		} while (IsInValidCoordPage(TargetX, TargetY) || (RaidECCMapInvertCoord(TargetX, TargetY) >  CurrentPageIdx));
#endif //IM_B27A

	}

#if DEBUG_RAIDECCMAP_UART_SWAP_PARITY_TAG_EN //Debug code: compare tag of current page and parity page (could be removed after burn-in stable)
	U8 TagOfCurrentPage = RaidECCMapCalculateTagByCoord(ubX, uwY);
	M_UART(RAIDECCMAP_DEBUG_, "\n Cur_page %d, Tag %d", CurrentPageIdx, TagOfCurrentPage);
	M_UART(RAIDECCMAP_DEBUG_, "\n Pre_page %d, Tag %d", RaidECCMapInvertCoord(TargetX, TargetY), RaidECCMapCalculateTagByCoord(TargetX, TargetY));

	// Tag must same
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (TagOfCurrentPage == RaidECCMapCalculateTagByCoord(TargetX, TargetY)));
	// Pre page must < Cur page
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (RaidECCMapInvertCoord(TargetX, TargetY) < CurrentPageIdx));
#endif

	// Return page index (i.e., ParityUnitPlanePtr)
	return RaidECCMapInvertCoord(TargetX, TargetY);
}

U32 RaidECCMapSwapFlowFindLastParityPlanePtr(U8 ubRSEncodeMode, RaidECCMapParityMapEnum_t ubParityMapMode, U8 ubIsSlcMode, U16 uwPageIdx, U8 ubPlane)
{
	U8 ubPageShift = 0;
	U32 ulParityUnitPlanePtr = gulPlanesPerUnit; // Invalid
	U16 uwPhysicalPageCnt = uwPageIdx + 1; //real page number, cnt start from 1.
	if (ubIsSlcMode) {
#if (IM_B47R || IM_B37R)
		if (0 == (uwPhysicalPageCnt % RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM)) { //because of 2 WL protection
			ubPageShift = RAIDECCMAP_2WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM + RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM - 1;
		}
		else {
			ubPageShift = RAIDECCMAP_2WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM - 1;
		}

		if (((uwPageIdx / RAIDECCMAP_2WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM) < 1) || RaidECCMapCheckParityInline(ubRSEncodeMode, ubIsSlcMode, (uwPageIdx - ubPageShift), (gubPlanesPerSuperPage - 1))) {
			//this case should not occur in here since its tag doesn't need to be swapped, must blocked by RaidECCMapCheckNeedSwapTag.
			M_UART(RAIDECCMAP_DEBUG_, "\n ERROR!!");
			M_FW_ASSERT(ASSERT_RAIDEECCMAP_0x0810, 0);
		}
		else {
			ulParityUnitPlanePtr = (uwPageIdx - ubPageShift);
		}
#else /* (IM_B47R) */
#if IM_B27A
		ubPageShift = RAIDECCMAP_GR_SLC_TAG_NUM_IN_RAM_V2;
#else
		U16 uwPhysicalPageCnt = uwPageIdx + 1; //real page number, cnt start from 1.
		if (((uwPhysicalPageCnt) % RAIDECCMAP_GR_SLC_TAG_NUM_IN_RAM_V2 ) == 0) {
			// Each tag was arranged to the bottom every 12 pages.
			ubPageShift = (2 * RAIDECCMAP_GR_SLC_TAG_NUM_IN_RAM_V2) - 1;
		}
		else {
			ubPageShift = RAIDECCMAP_GR_SLC_TAG_NUM_IN_RAM_V2 - 1;
		}
#endif
		if (((uwPageIdx / RAIDECCMAP_GR_SLC_TAG_NUM_IN_RAM_V2) < 1) || RaidECCMapCheckParityInline(ubRSEncodeMode, ubIsSlcMode, (uwPageIdx - ubPageShift), (gubPlanesPerSuperPage - 1))) {
			//this case should not occur in here since its tag doesn't need to be swapped, must blocked by RaidECCMapCheckNeedSwapTag.
			M_UART(RAIDECCMAP_DEBUG_, "\n ERROR!!");
			M_FW_ASSERT(ASSERT_RAIDEECCMAP_0x0811, 0);
		}
		else {
			ulParityUnitPlanePtr = (uwPageIdx - ubPageShift);
		}
#endif /* (IM_B47R) */
	}
	else {
		U8 ubX = FTLGetCoord(uwPageIdx, IM_GETCOORD_X_VAL);
		U16 uwY = FTLGetCoord(uwPageIdx, IM_GETCOORD_Y_VAL);
		ulParityUnitPlanePtr = RaidECCMapXLCFindLastSameTagPageByCoord(ubX, uwY);
		//TODO: according to encode mode, add planeptr shift?
	}

	return ulParityUnitPlanePtr;
}

U8 RaidECCMapCheckNeedSwapTag(U8 ubRSEncodeMode, U8 ubSLCMode, U32 ulPlaneIdx, U8 IsRemoveTag)
{
	U8 ubNeedSwap = TRUE;
	U16 uwPage = 0;
	U8 ubPlaneBank = 0;

	RAIDECCMAPPlaneIdx2PhysicalInline(ulPlaneIdx, &uwPage, &ubPlaneBank, ubSLCMode);
	//debug uart
	if (DEBUG_RAIDECCMAP_UART_SWAP_PARITY_TAG_EN) {
		M_UART(RAIDECCMAP_DEBUG_, "\n SLC=%d", ubSLCMode);
	}

	if (IsRemoveTag) {
		//debug uart
		if (DEBUG_RAIDECCMAP_UART_SWAP_PARITY_TAG_EN) {
			M_UART(RAIDECCMAP_DEBUG_, "\n Is rmv");
		}
		ubNeedSwap = !(RaidECCMapCheckParityInline(ubRSEncodeMode, ubSLCMode, uwPage, ubPlaneBank));
	}
	else {
		if (ubSLCMode) {
			U8 ubPageShift = 0;
#if IM_B27A
			ubPageShift = RAIDECCMAP_GR_SLC_TAG_NUM_IN_RAM_V2;
#else
			U16 uwPhysicalPageCnt = uwPage + 1; //real page number, cnt start from 1.

#if (IM_B47R || IM_B37R)
			if (0 == (uwPhysicalPageCnt % RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM)) {
				ubPageShift = RAIDECCMAP_2WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM + RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM - 1;
			}
			else {
				ubPageShift = RAIDECCMAP_2WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM - 1;
			}

			if (((uwPage / RAIDECCMAP_2WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM) < 1) || RaidECCMapCheckParityInline(ubRSEncodeMode, ubSLCMode, (uwPage - ubPageShift), (gubPlanesPerSuperPage - 1))) {
				//this case should not occur in here since its tag doesn't need to be swapped.
				ubNeedSwap = FALSE;
			}
#else /* (IM_B47R) */
			if (((uwPhysicalPageCnt) % RAIDECCMAP_GR_SLC_TAG_NUM_IN_RAM_V2 ) == 0) {
				// Each tag was arranged to the bottom every 12 pages.
				ubPageShift = (2 * RAIDECCMAP_GR_SLC_TAG_NUM_IN_RAM_V2) - 1;
			}
			else {
				ubPageShift = RAIDECCMAP_GR_SLC_TAG_NUM_IN_RAM_V2 - 1;
			}
#endif /* (IM_B47R) */
			if (((uwPage / RAIDECCMAP_GR_SLC_TAG_NUM_IN_RAM_V2) < 1) || RaidECCMapCheckParityInline(ubRSEncodeMode, ubSLCMode, (uwPage - ubPageShift), (gubPlanesPerSuperPage - 1))) {
				//this case should not occur in here since its tag doesn't need to be swapped.
				ubNeedSwap = FALSE;
			}
#endif
		}
		else {
			U8 ubX = FTLGetCoord(uwPage, IM_GETCOORD_X_VAL);
			U16 uwY = FTLGetCoord(uwPage, IM_GETCOORD_Y_VAL);
			U16 uwResultPage = RaidECCMapXLCFindLastSameTagPageByCoord(ubX, uwY);
			if ((uwResultPage == 0xFFFF) || RaidECCMapCheckParityInline(ubRSEncodeMode, ubSLCMode, uwResultPage, (gubPlanesPerSuperPage - 1))) {
				//debug uart
				if (uwResultPage == 0xFFFF) {
					//M_UART(RAIDECCMAP_DEBUG_, "\nTLC mode: pioneer WL, page %d, plane %d\n", uwPage, ubPlaneBank);
				}
				else {
					//M_UART(RAIDECCMAP_DEBUG_, "\nTLC mode: par page %d \n", uwResultPage);
				}
				ubNeedSwap = FALSE;
			}
		}
	}
	//debug uart
	if (DEBUG_RAIDECCMAP_UART_SWAP_PARITY_TAG_EN) {
		M_UART(RAIDECCMAP_DEBUG_, "\n ubNeedSwap= %d", ubNeedSwap);
	}
#if 0
	if (ubNeedSwap) {
		M_UART(RAIDECCMAP_DEBUG_, "\nNeedSwap M: %d, Ptr: %d", ubMapMode, ulPlaneIdx);
		M_UART(RAIDECCMAP_DEBUG_, "\nPPg: %d, Pl: %d", uwPage, ubPlaneBank);
	}
	else {
		M_UART(RAIDECCMAP_DEBUG_, "\nNo NeedSwap M: %d, Ptr: %d", ubMapMode, ulPlaneIdx);
		M_UART(RAIDECCMAP_DEBUG_, "\nPPg: %d, Pl: %d", uwPage, ubPlaneBank);
	}
#endif
	return ubNeedSwap;
}

#endif  /* ((IM_B17) || (IM_B27A) || (IM_B27B) || (IM_N18) || (IM_N28) || (IM_B47R)) */
