#include "VUC_MicronGetDriveConfigData.h"
#include "VUC_MicronGetNandErrorRecoveryStatistics.h"
#include "VUC_MicronResponse.h"
#include "hal/sys/api/efuc/efuse_api.h"
#include "nvme_api/nvme/shr_hal_nvme_api.h"
#include "hal/dmac/dmac_pop_cmd.h"

#if (VUC_MICRON_COMMON_VS_COMMANDS_EN)

void VUCMicronGetDeviceConfigData(U32 ulPayloadAddr)
{
	U8 ubi, ubChannel, ubCE, ubLUN, ubTmp[50] = "", ubTmp2[50];
	U16 uwBlockPerCE = gFlhEnv.ulBlockPerPlaneBank * gFlhEnv.ubPlanePerTarget;
	U32 ulResponsePayload = ulPayloadAddr + VUC_MICRON_RESPONSE_HEADER_SIZE;
	U64 uoNsSize = 0;
#if (!PS5017_EN)
	EFU_BANK2 *pEFuseInfo = (EFU_BANK2 *)gEFuseBank2347Info.ubEFuseBank2;
#endif /* (!PS5017_EN) */
	GetDriveConfigResponseHEADER_t *pResponseHeader;

	DMACParam_t DMACParam;
	DMACParam.DMACSetValue.ulDestAddr = ulPayloadAddr;
	DMACParam.DMACSetValue.ulLowValue = 0;
	DMACParam.DMACSetValue.ulHighValue = 0;
	DMACParam.DMACSetValue.ul32ByteNum = SIZE_IN_32B(DEF_KB(FRAMES_PER_PAGE));
	DMACParam.DMACSetValue.btValidate = FALSE;
	gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
	DMACSetValue(&DMACParam, gulDMACDirectWaitDone_Callback, 0);
	while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
		DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
	}

	pResponseHeader = (GetDriveConfigResponseHEADER_t *)ulPayloadAddr;

	memset((void *)ulPayloadAddr, 0x00, VUC_MICRON_RESPONSE_HEADER_SIZE);
	pResponseHeader->ubResponseHeaderFormatVersion = 0x00;
	pResponseHeader->ubResponseDataFotmatVersion = VUC_MICRON_RESPONSE_FORMAT_JSON;
	pResponseHeader->uwCMDClass = VUC_MICRON_GET_DRIVE_CONFIG_COMMAND_CLASS;
	pResponseHeader->uwCMDCode = VUC_MICRON_GET_DRIVE_CONFIG_COMMAND_CODE;
	pResponseHeader->uwStatus = 0;
	pResponseHeader->ulDataPayloadSize = FRAME_SIZE;

	VUCMyStrcat((void *)ulResponsePayload, "{\n\t");
	VUCMyStrcat((void *)ulResponsePayload, "\"structVer\":1,\n\t");

	// SSD
	VUCMyStrcat((void *)ulResponsePayload, "\"SSD\":{\n\t\t");
	VUCMyStrcat((void *)ulResponsePayload, "\"TotalChannels\":");
	VUCMyitoa(gFlhEnv.ubChannelExistNum, (void *)ubTmp, DECIMAL);
	VUCMyStrcat((void *)ulResponsePayload, (void *)ubTmp);
	VUCMyStrcat((void *)ulResponsePayload, ",\n\t\t");
	VUCMyStrcat((void *)ulResponsePayload, "\"TotalCEs\":");
	VUCMyitoa(gFlhEnv.ubCENumber, (void *)ubTmp, DECIMAL);
	VUCMyStrcat((void *)ulResponsePayload, (void *)ubTmp);
	VUCMyStrcat((void *)ulResponsePayload, ",\n\t\t");
	VUCMyStrcat((void *)ulResponsePayload, "\"TotalLUNs\":");
	VUCMyitoa(gFlhEnv.ubLUNperTarget, (void *)ubTmp, DECIMAL);
	VUCMyStrcat((void *)ulResponsePayload, (void *)ubTmp);
	VUCMyStrcat((void *)ulResponsePayload, "},\n\t");

	// MFG

	// ASIC
	VUCMyStrcat((void *)ulResponsePayload, "\"ASIC\":{\n\t\t");
	VUCMyStrcat((void *)ulResponsePayload, "\"STEP_REV1\":\"");
#if PS5017_EN
	if (ICVersionBB <= gubHwFwVersion)
#else /* PS5017_EN */
	if (ICVersionBB == pEFuseInfo->dw4.ubHwFwVersion)
#endif /* PS5017_EN */
	{
		VUCMyStrcat((void *)ulResponsePayload, "BB\",\n\t\t");
	}
	else {
		VUCMyStrcat((void *)ulResponsePayload, "AA\",\n\t\t");
	}
	VUCMyStrcat((void *)ulResponsePayload, "\"REF_CLK_FREQ\": 40},\n\t");

	// NAND
	VUCMyStrcat((void *)ulResponsePayload, "\"NAND\":{\n\t\t");
	VUCMyStrcat((void *)ulResponsePayload, "\"NAND_RAW_GIB\":");
	HAL_NS_GET_SIZE_API (1, uoNsSize);
	uoNsSize = uoNsSize >> SECTOR_TO_GB ;
	VUCMyitoa(uoNsSize, (void *)ubTmp, DECIMAL);
	VUCMyStrcat((void *)ulResponsePayload, (void *)ubTmp);
	VUCMyStrcat((void *)ulResponsePayload, ",\n\t\t");
	VUCMyStrcat((void *)ulResponsePayload, "\"NAND_PAGE_B\":");
	VUCMyitoa(gFlhEnv.uwPageTotalByteCnt, (void *)ubTmp, DECIMAL);
	VUCMyStrcat((void *)ulResponsePayload, (void *)ubTmp);
	VUCMyStrcat((void *)ulResponsePayload, ",\n\t\t");
	VUCMyStrcat((void *)ulResponsePayload, "\"NAND_BLK_CNT\":");
	VUCMyitoa(uwBlockPerCE, (void *)ubTmp, DECIMAL);
	VUCMyStrcat((void *)ulResponsePayload, (void *)ubTmp);
	VUCMyStrcat((void *)ulResponsePayload, ",\n\t\t");
	VUCMyStrcat((void *)ulResponsePayload, "\"NAND_PLN_CNT\":");
	VUCMyitoa(gFlhEnv.ubPlanePerTarget, (void *)ubTmp, DECIMAL);
	VUCMyStrcat((void *)ulResponsePayload, (void *)ubTmp);
	VUCMyStrcat((void *)ulResponsePayload, ",\n\t\t");
	VUCMyStrcat((void *)ulResponsePayload, "\"NAND_PGS_CNT\":");
	VUCMyitoa(gFlhEnv.uwPagePerBlock, (void *)ubTmp, DECIMAL);
	VUCMyStrcat((void *)ulResponsePayload, (void *)ubTmp);
	VUCMyStrcat((void *)ulResponsePayload, ",\n\t\t");
	VUCMyStrcat((void *)ulResponsePayload, "\"NAND_MULT_PLANE\":true},\n\t");

	//NAND_UID
	VUCMyStrcat((void *)ulResponsePayload, "\"NAND_UID\":[\n\t\t");

	for (ubLUN = 0; ubLUN < gFlhEnv.ubLUNperTarget; ubLUN++) {
		for (ubChannel = 0; ubChannel < gFlhEnv.ubChannelExistNum; ubChannel++) {
			for (ubCE = 0; ubCE < (gFlhEnv.ubCENumber / gFlhEnv.ubChannelExistNum); ubCE++) {
				FIPScanFlashUniqueID(ubChannel, ubCE, ubLUN, (U8 *)ubTmp);

				VUCMyStrcat((void *)ulResponsePayload, "{\"UID\":\"");

				for (ubi = 0; ubi < VUC_MICRON_UID_LENGTH; ubi++) {
					VUCMyitoa(ubTmp[ubi], &ubTmp2[ubi], HEXADECIMAL);
				}
				ubTmp2[VUC_MICRON_UID_LENGTH] = '\0';

				VUCMyStrcat((void *)ulResponsePayload, (void *)ubTmp2);
				VUCMyStrcat((void *)ulResponsePayload, "\",\"Channel\":");
				VUCMyitoa(ubChannel, (void *)ubTmp, DECIMAL);
				VUCMyStrcat((void *)ulResponsePayload, (void *)ubTmp);
				VUCMyStrcat((void *)ulResponsePayload, ",\"CE\":");
				VUCMyitoa(ubCE, (void *)ubTmp, DECIMAL);
				VUCMyStrcat((void *)ulResponsePayload, (void *)ubTmp);
				VUCMyStrcat((void *)ulResponsePayload, ",\"LUN\":");
				VUCMyitoa(ubLUN, (void *)ubTmp, DECIMAL);
				VUCMyStrcat((void *)ulResponsePayload, (void *)ubTmp);

				if (((gFlhEnv.ubLUNperTarget - 1) == ubLUN) && ( (gFlhEnv.ubChannelExistNum - 1) == ubChannel) && (((gFlhEnv.ubCENumber / gFlhEnv.ubChannelExistNum) - 1) == ubCE)) {
					VUCMyStrcat((void *)ulResponsePayload, "}]\n}");
				}
				else {
					VUCMyStrcat((void *)ulResponsePayload, "},\n\t\t");
				}
			}
		}
	}
}

#endif /*(VUC_MICRON_COMMON_VS_COMMANDS_EN)*/
