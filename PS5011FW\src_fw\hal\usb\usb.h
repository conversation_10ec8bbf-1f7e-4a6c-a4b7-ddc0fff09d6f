#ifndef _USB_H_
#define _USB_H_

//	C
#include "common\symbol.h"
#include "cpu/cpu_api.h"
#include "hal/sys/api/cphy/cphy_api.h"
//	S
#include "setup.h"
//	T
#include "typedef.h"
//	U
#include "hal/pic/uart/uart_api.h"

#if (USB == HOST_MODE)

#ifdef EXTERN
#undef EXTERN
#endif /* EXTERN */

#ifdef _USB_C_
#define EXTERN
#else /* _USB_C_ */
#define EXTERN extern
#endif /* _USB_C_ */

/*=====================================================================================
                                    Basic Definition
=====================================================================================*/
/* Endpoint define */
#define EP0					(0)
#define EP1					(1)
#define EP2					(2)
#define EP3					(3)
#define EP4					(4)
#define EP5					(5)
#define EP6					(6)
#define EP7					(7)
#define EP8					(8)
/* Normal BOT define */
#define CBW_SIZE			(31)
#define CSW_SIZE			(13)
#define CBW_SIGNATURE		(0x43425355)
#define CSW_SIGNATURE		(0x53425355)
/* Normal UASP define */
#define CIU_ID				(0x01)	// Command IU ID
#define SIU_ID				(0x03)	// Sense IU ID
#define RIU_ID				(0x04)	// Response IU ID
#define TMIU_ID				(0x05)	// Task Management IU ID
#define RRIU_ID				(0x06)	// Read Ready IU ID
#define WRIU_ID				(0x07)	// Write Ready IU ID
#define CIU_SIZE			(32)
#define SIU_SIZE			(16)
#define RIU_SIZE			(8)
/* APU CMD CQ type define */
#define TYPE_CMD			(0x00)
#define TYPE_TMIU			(0x01)
#define TYPE_SETUP_PACKET	(0x02)
/* APU CMD CQ attribute define */
#define ATTR_ASYNC			(0x00)
#define ATTR_WRITE			(0x01)
#define ATTR_READ			(0x02)
#define ATTR_SYNC			(0x03)
/* USB Current CMD state define */
#define CMD_CHECK			(0x00)
#define CMD_DOING			(0x01)
#define CMD_FINISH			(0x02)
/* USB card Media Define */
#define MAX_CARD			(5)
#define MEDIA_FLASH			(0) /* MAX */
#define MEDIA_FLOPPY		(1)
#define MEDIA_SECURE		(2) /* mode 7/8, MEDIA_FLASH_1 -> LUN 0 */
#define MEDIA_PUBLIC		(3) /* mode 7/8, MEDIA_FLASH_2 -> LUN 1 */
#define MEDIA_CDROM			(4) /* mode 21, MEDIA_FLASH_1 -> LUN 0 */

#define MODE7_SECURE_LUN	(0)
#define MODE7_PUBLIC_LUN	(1)
#define MODE21_CDROM_LUN	(0)
#define MODE21_SECURE_LUN	(1)

/* NRW SQ Setting */
#define NRW_SET_ACPL	(1)
#define NRW_NOT_ACPL	(0)

#define NRW_NOT_CTAG	(0)

#define NRW_SET_BCMD	(1)
#define NRW_NOT_BCMD	(0)

#define USB_CMD_REMAIN_SIZE	(SIZE_8KB)

//----------------- start : define type value -----------------
#define SIU_TYPE		(0x00)
#define RSPIU_TYPE		(0x01)
//----------------- end : define type value -------------------

//----------------- start : define STALL value -----------------
#define NON_STALL		(FALSE)
#define STALL			(TRUE)
//----------------- end : define STALL value -------------------

//----------------- start : define btSenseDat value ---------------
#define WITH_SENSE_DAT	(TRUE)
#define WO_SENSE_DAT	(FALSE)
#define NON_INPUT		(FALSE)
//----------------- end : define btSenseDat value -----------------

//----------------- start : define status code for generic command -----------------
enum USB_UASP_SC_SIU {
	SIU_GOOD			= 0x00,
	SIU_CHK_COND		= 0x02,
	SIU_COND_MET		= 0x04,
	SIU_BUSY			= 0x08,
	//OBSOLETE		=(0x10)
	//OBSOLETE		=(0x14)
	SIU_RESV_CONFLICT	= 0x18,
	//OBSOLETE		(0x22)
	SIU_TASK_SET_FULL	= 0x28,
	SIU_ACA_ACTIVE		= 0x30,
	SIU_TASK_ABORTED	= 0x40
};

enum USB_UASP_SC_RPIU {
	TMIU_COMPLETE			= 0x00,
	// Reserved				= 0x01,
	TMIU_IVLD_INFO_UNIT		= 0x02,
	// Reserved				= 0x03,
	TMIU_FUNC_NOT_SUP		= 0x04,
	TMIU_FUNC_FAILED		= 0x05,
	// Reserved				= 0x06,
	// Reserved				= 0x07,
	TMIU_FUNC_SUCCESS		= 0x08,
	TMIU_INCOR_LOG_UNIT_NUM	= 0x09,
	TMIU_OVERLAPPED_TAG		= 0x0A
};

enum USB_BOT_SC_CSW {
	CSW_GOOD		= 0x00,
	CSW_CMD_FAILED	= 0x01,
	CSW_PHASE_ERR	= 0x02
};

enum USB_SENSE_DATA_RESPONSE_CODE {
	SD_CUR_FIX_RSP_CODE = 0x70,
	SD_DEF_FIX_RSP_CODE = 0x71,
	SD_CUR_DEF_RSP_CODE = 0x72,
	SD_DEF_DEF_RSP_CODE = 0x73
};

#define	SIU_STATUS_SFT	(16)
#define RIU_STATUS_SFT	(24)

#define HOSTEVT_HIGHPRI_RESET_EVENT				(0)
#define HOSTEVT_EPHALT_EVENT					(1)
#define HOSTEVT_LOWPRI_RESET_EVENT 				(2)
#define HOSTEVT_GENERAL_ERROR					(4)

/*=====================================================================================
                                  Variable Definition
=====================================================================================*/
// HW Debug Port
#define DEBUG_PORT_PARA					(0x129AC)
// CMD packet size & CMD Q
/* R32_USB_GLB_CMDQ_CFG_0 (0x50) BIT[1:0] UASP mode cmd pkt size */
#define	CMD_PKT_SIZE					(SIZE_64B)
/* R32_USB_GLB_CMDQ_CFG_0 (0x50) BIT[3:2] maximum ctag on cmdq */
#define	CTAG_NUM_16_3					(0x10U) // 16+3
#define	CTAG_NUM_32_3					(0x20U) // 32+3
#define	CTAG_NUM_64_3					(0x40U) // 64+3
#define	CTAG_NUM_128_3					(0x80U) // 128+3
#if (USB_UASP_EN)
#define	MAX_CTAG_ON_CMDQ				(CTAG_NUM_128_3) // 16+3, 32+3, 64+3, 128+3
#else /* (USB_UASP_EN) */ //BOT
#define	MAX_CTAG_ON_CMDQ				(1)
#endif /* (USB_UASP_EN) */
#define SETUP_CMD_CTAG					(128)
/* Configuration Descriptor max power */
#define CONFIGURATION_DESCRIPTOR_SS_B8	(0x70) /* max power, unit = 8mA, Self-Powered, total = 18*8 = 144mA */
#define CONFIGURATION_DESCRIPTOR_HS_B8	(0x96)
#define CONFIGURATION_DESCRIPTOR_FS_B8	(0x31)
/* Configuration Descriptor max packet size */
#define FS_PACKET_SIZE_LB				(0x40) /* Low Byte max packet size = 64 */
#define FS_PACKET_SIZE_HB				(0x00) /* High Byte max packet size = 64 */
#define HS_PACKET_SIZE_LB				(0x00) /* Low Byte max packet size = 512 */
#define HS_PACKET_SIZE_HB				(0x02) /* High Byte max packet size = 512 */
#define SS_PACKET_SIZE_LB				(0x00) /* Low Byte max packet size = 1024 */
#define SS_PACKET_SIZE_HB				(0x04) /* High Byte max packet size = 1024 */
/* Configuration Descriptor maximum stream */
#define	MAX_STREAMLOG					(0x05U)
/* Configuration Descriptor maximum burst */
#define	MAX_BURST						(0x07U)
/* Configuration Descriptor endpoint companion */
#define	SUPERSPEED_0X31					(0x31U)
#define	SUPERSPEED_0X30					(0x30U)
#define	HIGHSPEED_0X20					(0x20U)
// [CT 20200113] Add for avoid some Host may not tell device to wake up
#define	L1_TIMEOUT_MS					(3000)
#if (USB_WORKAROUND_EOB_TWICE_EN || USB_WORKAROUND_ACK_RETRY_EN)
// unit is 1ms, must update if RTT2 scale is changed
#define WAIT_USBTX_TIMEOUT_TO_POLL_LUN_RESET	(MILLISECOND_PER_SECOND)
#endif /* (USB_WORKAROUND_EOB_TWICE_EN || USB_WORKAROUND_ACK_RETRY_EN) */
#if (USB_WORKAROUND_DETECT_USBLUN_RESET_EN)
// unit is 1ms, must update if RTT2 scale is changed
// host would wait LUN reset response in 10 sec
#define POLLING_LUN_RESET_TIMEOUT		(10 * MILLISECOND_PER_SECOND)
#endif /* (USB_WORKAROUND_DETECT_USBLUN_RESET_EN) */
/*=====================================================================================
                                  Global Var Definition
=====================================================================================*/
// Get Descriptor parameter array
extern U8 const gaubCbString0Descriptor[4U];
extern U8 const gaubCbString1Descriptor[34U];
extern U8 const gaubCbString2Descriptor[26U];
extern U8 const gaubCbString3Descriptor[34U];

extern U8 const gaubSsConfigDescriptor_UASP[];
extern U8 const gaubHSConfigDescriptor_UASP[];
extern U8 const gaubSsConfigDescriptor_BOT[];
extern U8 const gaubHSConfigDescriptor_BOT[];
extern U8 const gaubFSConfigDescriptor_BOT[];

extern U8 const gaubBinaryObjectStore[];
extern U8 const gaubSsDeviceDescriptor[];
extern U8 const gaub20DeviceDescriptor[];

extern U8 const gaubDeviceQualifier[];

extern U8 const gaubOtherSpeedConfigurationFS[0x20U];
extern U8 const gaubOtherSpeedConfigurationHS[0x20U];

extern U8 gaubResponseBuffer[128];

extern U8 gDoingInit;

/*=====================================================================================
                                     Enum Definition
=====================================================================================*/
/* ubDispatcherState */
enum {
	USBDISP_NORMAL_FLOW = 0,
	USBDISP_CHECK_STATUS,
	USBDISP_GET_CMD_HANDLER,
	USBDISP_PROCESS_CMD,
	USBDISP_WAIT_SAVE_INITINFO,
	USBDISP_CMD_COMPLETED,
	USBDISP_LEAVE_DISPATCHER,
};

/* gUSBVar.ubUSBCmdStep */
enum {
	USBCMD_INIT					= 0x00,
	USBCMD_ALLOCATE_BUF			= 0x01,
	USBCMD_LOAD_DATA			= 0x02,
	USBCMD_WAIT_LOAD_DATA		= 0x03,
	USBCMD_LOAD_DATA_DONE		= 0x04,
	USBCMD_PROCESS				= 0x05,
	USBCMD_NEED_PROCESS_AGAIN	= 0x06,
	USBCMD_WAIT_PROCESS_DONE	= 0x07,
	USBCMD_VALIDATE_BUF			= 0x08,
	USBCMD_TRANSFER_DATA		= 0x09,
	USBCMD_WAIT_TRANSFER_DONE	= 0x0A,
	USBCMD_TRANSFER_DATA_DONE	= 0x0B,
	USBCMD_HANDLE_ERROR			= 0x0C,
	USBCMD_FREE_BUF				= 0x0D,
	USBCMD_WAIT_APU_TD_DONE		= 0x0E,
	USBCMD_RESPOND_HOST			= 0x0F,
	USBCMD_LOCK_APU_W_FIFO		= 0x10,
	USBCMD_WAIT_APU_W_DATA_DONE	= 0x11,
	USBCMD_WAIT_SETUP_CMD_DONE	= 0x12,

	USBCMD_FINISH				= 0xFF
};
/* USBNRWBufManager job */
enum {
	USB_NRW_BUF_ALLOCATE = 0,
	USB_NRW_BUF_VALIDATE,
	USB_NRW_BUF_FREE
};
/* gUSBVar.ubErrorCase */
enum {
	USBCMD_NO_ERROR = 0x00,
	USBCMD_ERROR_CASE_01h = 0x01,
	USBCMD_ERROR_CASE_02h = 0x02,
	USBCMD_ERROR_CASE_03h = 0x03,
	USBCMD_ERROR_CASE_04h = 0x04,
	USBCMD_ERROR_CASE_05h = 0x05,
	USBCMD_ERROR_CASE_06h = 0x06,
	USBCMD_ERROR_CASE_07h = 0x07,
	USBCMD_ERROR_CASE_08h = 0x08,
	USBCMD_ERROR_CASE_09h = 0x09,
	USBCMD_ERROR_CASE_0Ah = 0x0A,

	USBCMD_ERROR_CASE_TRANSFER_ERROR		= 0xFD,
	USBCMD_ERROR_CASE_WCACHE_FLUSH_ERROR	= 0xFE,
	USBCMD_ERROR_CASE_PROCESS_ERROR			= 0xFF
};

/* gUSBErrCmdState */
enum {
	USB_ERR_STATE_INIT = 0,
	USB_ERR_STATE_CHECK_ERR,						// 1
	USB_ERR_STATE_ISSUE_STALL,						// 2
	USB_ERR_STATE_WAIT_HOST_ISSUE_CLEAR_EP_HALT,	// 3
	USB_ERR_STATE_WAIT_CLEAR_EP_HALT_DONE,			// 4
	USB_ERR_STATE_RETURN_STATUS,					// 5
	USB_ERR_STATE_WAIT_HOST_RECEIVE_STATUS,			// 6
	USB_ERR_STATE_RECOVERY,							// 7
	USB_ERR_STATE_FINISH,							// 8
	USB_ERR_STATE_POP_ALL_WRITE_DATA				// 9
};

/* SATAHostDBUFStateEnum */
typedef enum USBHostDBUFStateEnum {
	USB_HOST_DBUF_NO_STATE = 0,
	USB_HOST_DBUF_HAVE_STATE
} USBHostDBUFStateEnum_t;

/* USBHostTableLoadModeEnum */
typedef enum USBHostTableLoadModeEnum {
#if (BURNER_MODE_EN || RDT_MODE_EN)
	USBBURNERINIT,
#endif /* (BURNER_MODE_EN || RDT_MODE_EN) */
	USBHOSTINIT,
	USBHOSTLOAD,
} USBHostTableLoadModeEnum_t;

typedef enum {
	NO_SENSE 		= 0x00,
	SOFT_ERROR 		= 0x01,
	NOT_READY 		= 0x02,
	MEDIUM_ERROR	= 0x03,
	HARDWARE_ERROR	= 0x04,
	ILLEGAL_REQUEST	= 0x05,
	UNIT_ATTENTION	= 0x06,
	DATA_PROTECT	= 0x07,
	ABORTED_COMMAND	= 0x08,
	OTHER			= 0x09
} enumUSBSenseKey;

//==============================================================================
//  Structure Definition
//==============================================================================
// gUSBVar
typedef struct {
	U64	ulLBA;

	U32	ulSectorCnt;

	U16	uwXferByteCount;
	U8	ubUSBSpeed;
	U8	ubStall;
	/* For USB Cmd */
	U8	ubCmdCode;				// Store USB CMD OPCode
	U8	ubLastCmd;				// Store Last USB CMD OP Code
	U8	ubUSBCmdStep;			// USB NRW Cmd current process step
	U8	ubDMACSVDoing;			// Checking DMAC SetValue done yet
	U8	ubStandbyDoing;			// Checking Standby Cmd done yet
	U8	ubIsNeedSendD2H;		// Check dose Cmd need to send D2H when Cmd Complete (PIO-In is no need)
	U8	ubIsNeedSendSDB;		// Check dose Cmd need to send SDB when NCQ Cmd Error
	U8	ubNeedProcessAgain;		// Sata Cmd temp skip for Waiting FW process next loop
	U8	ubWaitProcessDone;		// Sata Cmd temp skip for Waiting FW process done
	U8	ubWCacheFlushState;		// Store W$ flush state in FTLSyncCmdHandler_USB()
	U16	uwCmdDataXferDoneCnt;	// Store how many 512 byte that FW had transfer to host for multi-sector data cmd (unit:512Byte)
	U32	ulNRWBufAddr;			// Store NRW Buffer Address of SATA Cmd
	U32	ulCmdTotalDataSize;		// Store Cmd total data size (unit:Byte)
	U32	ulCmdRemainDataSize;	// Store Cmd remain data size that hasn't transfer done yet (unit:512Byte)
	U32	ulXferDataSize;			// Store Cmd have been xfer data size

	/* For Buf Control */
	U8	ubNeedFreeNRWBuf;		// W_type Cmd FW should free NRW buffer NeedFreeNRWBuf initiative.
	/* For error case */
	U8	ubErrorCase;
	U8	ubReserved0;
	U8	ubChangeUSBSpeed		: 1;
	U8	ubSetUSBSpeed			: 2;
	U8	ubAvoidLPM					: 1;
	U8	btWaitUntilDataReady	: 1;
	U8	ubReserved1				: 3;
	U64	uoMediaSize[MAX_CARD];
	U16	uwCMDPacketSize;
	U8	ubMaxCtag;
	U8	ubUASPMode;
	//For SMART
	U32	ulSuperSpeedPlusLane1_Cnt;
	U32	ulSuperSpeedPlusLane2_Cnt;
	U32	ulSuperSpeedLane1_Cnt;
	U32	ulSuperSpeedLane2_Cnt;
	U32	ulHighSpeed_Cnt;
	U32	ulLowSpeed_Cnt;
	U32	ulFullspeed_Cnt;
	U8	ubPreviousSpeed;
	U32	ulSRAMParityErrorCount;
#if (USB_WORKAROUND_DETECT_USBLUN_RESET_EN)
	U8	ubDMAHangStartPollingLunRST;
	volatile U64 uoBusyPollingLunRSTTimeBase;
#endif /* (USB_WORKAROUND_DETECT_USBLUN_RESET_EN) */
#if (USB_WORKAROUND_EOB_TWICE_EN)
	U8	ubQDNCaseDisACPL;
#endif /* (USB_WORKAROUND_EOB_TWICE_EN) */
#if (USB_WORKAROUND_DETECT_USBLUN_RESET_EN || USB_WORKAROUND_EOB_TWICE_EN || USB_WORKAROUND_ACK_RETRY_EN)
	U8	ubPrevAPUCmdOfst;
#endif /* (USB_WORKAROUND_DETECT_USBLUN_RESET_EN || USB_WORKAROUND_EOB_TWICE_EN || USB_WORKAROUND_ACK_RETRY_EN) */
#if (USB_WORKAROUND_ACK_RETRY_EN)
	U64	uoAckRetryWAChkCmdSectorsUpperLimit;
	U8	ubAckRetryWAEnable;
#endif /* (USB_WORKAROUND_ACK_RETRY_EN) */
	U8 btStandbyDoing: 1; // if standby flow is doing
	U8 ubReserved: 7;
} USBVAR_t;

// gUSBHostEvt
typedef union {
	U64	uoStatus;
	U32	ulStatus[2];
	U16	uwStatus[4];
	U8	ubStatus[8];
	struct {
		/* Byte 0, HOSTEVT_HIGHPRI_RESET_EVENT */
		U64 btHRST				: 1; // hot reset
		U64 btWRST				: 1; // warm reset
		U64 Rsv0				: 6;
		/* Byte 1, HOSTEVT_EPHALT_EVENT */
		U64 btEP1Halt			: 1; // clear feature EP1 halt
		U64 btEP2Halt			: 1; // clear feature EP2 halt
		U64 btEP3Halt			: 1; // clear feature EP3 halt
		U64 btEP4Halt			: 1; // clear feature EP4 halt
		U64 btEP5Halt			: 1; // clear feature EP5 halt
		U64 btEP6Halt			: 1; // clear feature EP6 halt
		U64 btEP7Halt			: 1; // clear feature EP7 halt
		U64 btEP8Halt			: 1; // clear feature EP8 halt
		/* Byte 2, HOSTEVT_LOWPRI_RESET_EVENT */
		U64 btLUNRST			: 1;
		U64 btBULKRST			: 1;
		U64	Rsv1				: 6;

		/* Byte 3, LUN reset RPIU ctag */
		U64 ubRstCtag			: 8;

		/* Byte 4, HOSTEVT_GENERAL_ERROR */
		U64 btFatalCmd			: 1; // CPSR fail
		U64 btRxdmaFail			: 1; // Rxdma transfer fail, size error
		U64 btTxdmaFail			: 1; // Txdma transfer fail, size error
		U64 Rsv2				: 5;

		/* Byte 5 */
		U64 ubErrCtag			: 8;

		/* Byte 6 */
		U64 btLUNNum			: 5;
		U64 ubRsv3				: 3;

		/* Byte 7, LPM */
		U64 btLPM				: 1;
		U64 Rsv4				: 7;
	};
} USBHOSTEVT_t, *PUSBHOSTEVT_t;
// APU to FW structure
typedef union {
	U32 all[4];

	struct {
		/* dw0 */
		U32 ubCTag				: 8;
		U32 type				: 2; // 2'b00 : CIU 2'b01 : TMIU, 2'b10 : Setup packet,
		U32 IO					: 1; // IO or non-IO
		U32 fua					: 1;
		U32 rsv0				: 2;
		U32 err					: 1; // CMD error
		U32	AES_err				: 1; // AES error
		U32 op_code				: 8; // SCSI CMD OP code
		U32 zl					: 1; // Zero Length
		U32 az					: 1; // all zero : only valid for read command with MBR. FW reply all zero when this bit is set
		U32 lpn					: 1; // indicate the last PN Zone is in use(Set 1 in USB read)
		U32 rsv2				: 1;
		U32 nc					: 1; // new cmd, assert only if first sub-command of read
		U32 rsv3				: 3; // 0's based

		union {
			struct {
				/* dw1 */
				U32 slca		: 32;
				/* dw2 */
				U32 cnlc		: 16;	// current number of LCA of this sub read command
				U32 ssv			: 8;	// start sector valid
				U32 esv			: 8;	// end sector valid
				/* dw3 */
				U32 tnlc		: 17;	// total number of LCA, this field is always valid for those command that defined SLBA/NLB but for read this field is only valid when NC = 1
				U32 lun			: 5;
				U32 attr		: 2;	// 2'b00 : async, 2'b01 : write, 2'b10 : read, 2'b11 : sync (async/sync share the same doorbell)
				U32 BOT13cases	: 4;
				U32 ivld_lun	: 1;
				U32 ivld_rsv	: 1;
				U32 ivld_len	: 1;
				U32 ivld_lba	: 1;
			} io_cmd; //usb

			struct {
				/* dw1 */
				U32 Requset_Type	: 8;
				U32 Request			: 8;
				U32 Value_L			: 8;
				U32 Value_H			: 8;
				/* dw2 */
				U32 Index_L			: 8;
				U32 Index_H			: 8;
				U32 Length_L		: 8;
				U32 Length_H		: 8;
				/* dw3 */
				U32 rsv4			: 17;
				U32 lun				: 5;
				U32 attr			: 2; // 2'b00 : async, 2'b01 : write, 2'b10 : read, 2'b11 : sync (async/sync share the same doorbell)
				U32 BOTcase			: 8;
			} setup_cmd; //usb

			struct {
				/* dw1 */
				U32 scsi_B1		: 8;
				U32 scsi_B2		: 8;
				U32 scsi_B3		: 8;
				U32 scsi_B4		: 8;
				/* dw2 */
				U32 scsi_B5		: 8;
				U32 scsi_B6		: 8;
				U32 scsi_B7		: 8;
				U32 scsi_B8		: 8;
				/* dw3 */
				U32 scsi_B9		: 8;
				U32 scsi_B10	: 8;
				U32 rsv4		: 1;
				U32 lun			: 5;
				U32 attr		: 2; // 2'b00 : async, 2'b01 : write, 2'b10 : read, 2'b11 : sync (async/sync share the same doorbell)
				U32 BOT13cases	: 4;
				U32 ivld_lun	: 1;
				U32 ivld_rsv	: 1;
				U32 ivld_len	: 1;
				U32 ivld_lba	: 1;
			} scsi_cmd; //usb

			struct {
				/* dw1 */
				U32 task_mgt_ftn	: 8; // Task Management Function
				U32 rsv4			: 8;
				U32 tag_task_mgd_H	: 8; // Tag of Task to be managed [15:8]
				U32 tag_task_mgd_L	: 8; // Tag of Task to be managed [7:0]
				/* dw2 */
				U32 rsv5;
				/* dw3 */
				U32 rsv6			: 17;
				U32 lun				: 5;
				U32 attr			: 2; // 2'b00 : async, 2'b01 : write, 2'b10 : read, 2'b11 : sync (async/sync share the same doorbell)
				U32 rsv7			: 4;
				U32 ivld_lun		: 1;
				U32 ivld_rsv		: 1;
				U32 rsv8			: 1;
				U32 ivld_lba		: 1;
			} task_iu; //usb
		};
	} info;
} APUCQ_t, *PAPUCQ_t;
// USB Current handler CMD
typedef struct stUSBCURRCMD USBCURRCMD_t, *PUSBCURRCMD_t;
typedef void (*pUSBCmdHandleFun)(U32, USBCURRCMD_t *);

typedef struct {
	U32	*pulSourceAddr;
} CopyData_t;

typedef struct {
	U32	*pulSourceAddr;
} USBSenseData_t;

typedef struct {
	enumUSBSenseKey	enumSenseKey;
	U8	ubASC;
	U8	ubASQ;
	U8	btRespCode	: 7;
	U8	btRespHOST	: 1;
	U8	ubCmdStatus;
} CmdStatus_t;

// btTDStatus
#define TD_WAIT_DONE	(0)
#define TD_XFER_PASS	(1)
#define TD_XFER_FAIL	(2)

typedef struct {
	U8	btWCHType			: 1; // WCH_RTYPE, WCH_WTYPE
	U8	btIsEPC				: 1;
	U8	btIsNoNeedBufCmd	: 1;
	U8	btTDStatus			: 2;
	U8	btRsv				: 3;
	U8  bRes[3];
	U32	ulCmdTotalDataSize;
	CopyData_t	CopyData;
	CmdStatus_t	Status;
	pUSBCmdHandleFun 	pUSBCmdHandleFun;
} CmdInfo_t;

typedef struct stUSBCURRCMD {
	U8					valid			: 1;
	U8					rsv0			: 7;
	U8					ubState;
	U8					ubCDBByte11;
	U8					ubCDBByte12;
	U8					ubCDBByte13;
	U8					ubCDBByte14;
	U8					ubCDBByte15;
	U8					ubrsv; //4byte alignment
	APUCQ_t				APUCQ;
	CmdInfo_t			CmdInfo;
} USBCURRCMD_t, *PUSBCURRCMD_t;

typedef union {
	struct {
		U32 ulDW[4];
	} rawdata;

	struct {
		// DW0
		U32 ubCTAG		: 8;
		U32 btCMDEND	: 1;	// Last trigger of this command
		U32 btBCMD		: 1;	// Byte command
		U32 btFT		: 1;	// First time trigger of this cmd
		U32 btE3DPCACHK	: 1;	// Enable E3D and PCA CRC check
		U32 btDISBMU	: 1;	// Disable BMU buffer request, APU direct to start DMA
		U32 btRSV		: 1;
		U32 btACPL		: 1;	// Auto completion enable. Shall be 0 for byte cmd(BCMD = 1)
		U32 btTYPE		: 1;	// CMD type, 0 for write cmd, 1 for read cmd
		U32 RSV1		: 1;
		U32 btEPCDATRES	: 2;	// EPC data response
		U32 btEPCSTSRES	: 2;	// EPC status response
		U32 btUPDCQ		: 1;	// auto update bc/cnlc in cmdq with winc_info
		U32 RSV2		: 10;
		// DW1
		U32 BCSBC_CNLC	: 29;	// BCSBC[28:0] (for BCMD=1), CNLC[16:0] (for BCMD=0)
		U32 RSV3		: 3;
		// DW2
		U32 uwADDR		: 16;	// PB_ADDR[9:0] (for DISBMU=1), LB_ADDR[15:0] (for DISBMU=0)
		U32 uwRSV0		: 16;
		// DW3
		U32 BCTBC_TNLC	: 29;	// BCTBC[28:0] (for BCMD=1), TNLC[16:0] (for BCMD=0)
		U32 RSV4		: 3;
	} info;
} USBWCH_t, *PUSBWCH_t;

typedef union {
	struct {
		U32 ulDW[2];
	} All;

	struct {
		//DW0
		U8 ubCTag;
		U8 ubStatus;			// Status
		U8 btType		: 1;	// Type: 0:SIU / 1:RSPIU
		U8 btStall		: 1;	// ccpl raise stall flag when sending wen to ep
		U8 btSenseDat	: 1;	// SIU with Sense Data (fix 18B)
		U8 btRes0		: 1;
		U8 ubSenseKey	: 4;	// Sense Key of Sense Data
		U8 ubRespCode	: 7;	// Response Code of Sense Data
		U8 btRes1		: 1;
		//DW1
		U8 ubASC;				// ASC of Sense Data
		U8 ubASQ;				// ASQ of Sense Data
		U16 uwStatusQlfr;		// Status Qualifier of Sense Data
	} SIU;

	struct {
		//DW0
		U8 ubCTag;
		U8 ubRespCode;				// Response code
		U16 btType			: 1;	// Type: 0:SIU / 1:RSPIU
		U16 btStall			: 1;	// ccpl raise stall flag when sending wen to ep
		U16 uwRes1			: 14;
		//DW1
		U32 ulAddRespInfo	: 24;	// Additional Response Info
		U32 ubRsv			: 8;
	} RSPIU;

	struct {
		//DW0
		U8 ubCTag;
		U8 ubCSWStatus;			// CSW status
		U16 btRsv0		: 1;
		U16 btStall		: 1;	// ccpl raise stall flag when sending wen to ep
		U16 btRsv1		: 14;
		//DW1
		U32	ulCSWDataRes;
	} CSW;
} USBCPL_t, *PUSBCPL_t;

// USB Command Queue Ram
typedef union {
	U32 ulDW[8];

	struct {
		// DW0
		U32 btType				: 1;	// Type: only used for UASP mode, 0 CIU /1 TMIU
		U32 Attr				: 2;	// Attribute: 00 async / 01 write / 10 read / 11 sync
		U32 btStall				: 1;	// stall bit, issued by FW
		U32	LUN					: 4;	// Logical Unit Number
		U32 Rsv0				: 4;
		U32 btFUA				: 1;	// Force Unit Access: true when SCSI cmd FUA = 1 or cr_io_fua = 1
		U32	btDum				: 1;	// DMA dummy read/write
		U32 btErr				: 1;	// Error flag
		U32 btAESErr			: 1;	// Hit AES region
		U32 uwRemainingDataLBN	: 16;	// Remaining 4kB data unit waiting to be transfered

		// DW1
		U32 ulCBWTag;					// CBW tag

		// DW2
		U32 CSWData				: 30;	// CSW data residue
		U32 CSWSts				: 2;	// CSW status

		// DW3
		U32 btPARErr			: 1;	// Data Parity Error
		U32 btSZErr				: 1;	// Data transfer size error (ex: unexpected short pkt, tx wrong sv from apu rbuf info)
		U32	btCLRErr			: 1;	// Data transfer error with clear feature
		U32	btRSTErr			: 1;	// Data transfer error with reset
		U32	btInvldLUN			: 1;	// raised when LUN > cr_max_lun_num
		U32 btInvldRsv			: 1;	// raised when reserved bit in CIU is not zero (enabled when cr_cmd_rsv_en = 1)
		U32 btInvldLen			: 1;	// raised when BOT CBW CBD length > 16
		U32 btInvldLBA			: 1;	// raised when SCSI cmd startLBA + TransferLen > cr_lunx_max_lba
		U32	BOT13Case			: 4;	// BOT 13 cases
		U32	btAbn				: 1;	// Abnormal command received
		U32	btFirst				: 1;	// First: cmd received but data/status phase not yet started(cpsr always fill 1, cleared by apu_dat)
		U32	btIns				: 1;	// Ins: True if Cmd was already sent to APU
		U32	Rsv1				: 1;
		U32	uwTotalDataLBN		: 16;	// Total 4kB data unit

		union {
			// UASP/BOT IO Cmd
			struct {
				// DW4
				U32	ubOPCode			: 8;	// SCSI Operation Code
				U32	ubCDBByte1			: 8;	// SCSI CDB Byte1
				U32 ubCDBByte14			: 8;	// SCSI CDB Byte14
				U32	ubCDBByte15			: 8;	// SCSI CDB Byte15

				// DW5
				U32	ulStartLBAL;				// Start LBA Low DW

				// DW6
				U32	ulStartLBAH;				// Start LBA High DW

				// DW7
				U32 ulDataXferByteCnt;			// Data Transfer Byte Count
			} IO;
			// UASP/BOT non IO Cmd
			struct {
				// DW4
				U32 ubCDBByte0			: 8;
				U32 ubCDBByte1			: 8;
				U32 ubCDBByte2			: 8;
				U32 ubCDBByte3			: 8;

				// DW5
				U32 ubCDBByte4			: 8;
				U32 ubCDBByte5			: 8;
				U32 ubCDBByte6			: 8;
				U32 ubCDBByte7			: 8;

				// DW6
				U32 ubCDBByte8			: 8;
				U32 ubCDBByte9			: 8;
				U32 ubCDBByte10			: 8;
				U32 ubCDBByte11			: 8;

				// DW7
				U32 ubCDBByte12			: 8;
				U32 ubCDBByte13			: 8;
				U32 ubCDBByte14			: 8;
				U32 ubCDBByte15			: 8;
			} NonIO;
			// Task Management
			struct {
				// DW4
				U32 ubTMIUByte0			: 8;
				U32 ubTMIUByte1			: 8;
				U32 ubTMIUByte2			: 8;
				U32 ubTMIUByte3			: 8;

				// DW5
				U32 ubTMIUByte4			: 8;
				U32 ubTMIUByte5			: 8;
				U32 ubTMIUByte6			: 8;
				U32 ubTMIUByte7			: 8;

				// DW6
				U32 ubTMIUByte8			: 8;
				U32 ubTMIUByte9			: 8;
				U32 ubTMIUByte10		: 8;
				U32 ubTMIUByte11		: 8;

				// DW7
				U32 ubTMIUByte12		: 8;
				U32 ubTMIUByte13		: 8;
				U32 ubTMIUByte14		: 8;
				U32 ubTMIUByte15		: 8;
			} TMIU;
			// Setup Packet
			struct {
				// DW0
				U32	ubSPByte0			: 8;
				U32 ubSPByte1			: 8;
				U32	ubSPByte2			: 8;
				U32 ubSPByte3			: 8;

				// DW1
				U32	ubSPByte4			: 8;
				U32 ubSPByte5			: 8;
				U32	ubSPByte6			: 8;
				U32 ubSPByte7			: 8;

				// DW2
				U32 Rsv3;

				// DW3
				U32 Rsv4;
			} SetupPkt;
		};
	} Info;
} USBCMDQ_t, *PUSBCMDQ_t;

typedef union {
	U16 uwall;
	struct {
		U8 ubCtag;
		U8 btdum		: 1;
		U8 btParErr		: 1;
		U8 btSzErr		: 1;
		U8 btErr		: 1;
		U8 btRstClrErr	: 1;
		U8 Rsv0			: 3;
	};
} APUTD_t, *PAPUTD_t;

// [CT 20200113] Add for avoid some Host may not tell device to wake up
typedef struct {
	U8 usbL1Event;
	U64 usbL1StartTime;
} USBL1WA_t;
typedef struct {
	U16	uwVID;
	U16	uwPID;
	U8	ubLengthforManufacture;
	U8	ubLengthforProduct;
	U8	ubLengthforSerial;
	U8	ubInquiryProductName[16];
	U8	ubInquiryManufactureName[8];
	U8	ubInquiryVersion[4];
	U8	ubSNum[20];
	struct {
		U8 btU1U2On			: 1; // USB LGO_U1/LGO_U2 enable
		U8 btForceBOT		: 1; // Force BOT interface
		U8 btRemovableDisk	: 1;
		U8 btReserve3_7		: 5; // Reserve
	} Disk_Setting;
	U16 uwPowerOnReadyTimeForGC; // GC Continue CopyUnit threshold
} USBINFOBLK_t;

typedef struct {
	volatile U32 *ulAddr;
	U32 ulValue;
} USBPHYSETTING_t;

//==============================================================================
// Extern Variable
//==============================================================================
extern USBVAR_t gUSBVar;
extern USBCURRCMD_t gUSBCUrrentCMD;
extern volatile USBL1WA_t gUSB20_L1_Workaround;	// [CT 20200113] Add for avoid some Host may not tell device to wake up
extern volatile USBHOSTEVT_t gUSBHostEvt;		// Set by ISR need volatile
extern volatile U8 gUSBErrCmdState;
extern USBINFOBLK_t gUSBInfoBlk;

#if (USB_WORKAROUND_RX_PALORITY_EN)
extern U8 gubRxPolarity;
#endif /* (USB_WORKAROUND_RX_PALORITY_EN) */

/*=====================================================================================
                                  Function Declaretion
=====================================================================================*/
/* Common */
void USBISRHandler(void);
void USBSendDeviceNotification(U16 uwContent);

/* Code bank */
void USBGetUSBSpeed(void);
void USBGetUSBProtocol(void);
AOM_USB void USBPHYInit(void);
AOM_USB void USBLPMRegSetting(void);
AOM_USB void USBARSPRegSetting(void);
AOM_USB void USBRegInit(void);
AOM_USB void USBInit(void);
AOM_USB void USBVarInit(void);
AOM_USB void USBLPMVarInit(void);
AOM_USB void USBLPMInit(void);
AOM_USB void USBTDHandler(void);
AOM_ERROR_HANDLE void USBInfoBlkSetting(void);
#if (!BURNER_MODE_EN)
AOM_HOST_ERROR void USBEventDMASizeError_Callback(void);
AOM_HOST_RESET void USBEventEPHALT_Callback(void);
AOM_HOST_ERROR void USBEventFatalCmdError_Callback(void);
AOM_HOST_RESET void USBEventHighPriRST_Callback(void);
AOM_HOST_RESET void USBEventLowPriRST_Callback(void);
#endif /* (!BURNER_MODE_EN) */
#undef EXTERN

#endif /* (USB == HOST_MODE) */

#endif /* _USB_H_ */
