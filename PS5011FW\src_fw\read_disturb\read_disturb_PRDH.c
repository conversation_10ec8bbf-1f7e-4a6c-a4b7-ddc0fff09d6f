/****************************************************************************
 *
 *  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
 *  All rights reserved
 *
 *  The content of this document is confidential and shall be applied
 *  subject to the terms and conditions of the license agreement and
 *  other applicable laws. Any unauthorized access, use or disclosure
 *  of this document is strictly prohibited and may be punishable
 *  under laws.
 *
 *  read_disturb_PRDH.c
 *
 *
 *
 ****************************************************************************/

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "read_disturb/read_disturb_PRDH_api.h"
#include "hal/dmac/dmac_pop_cmd.h"
#include "retry/patch_cmd.h"
#include "hal/cop1/cop1_api.h"
#include "ftl/ftl_api.h"
#include "ftl/ftl_readverify_api.h"
#include "drive_log/drive_log_api.h"
#include "err_handle/err_log_api.h"
#include "media_scan/media_scan_api.h"
#include "hal/dmac/dmac_pop_cmd.h"
#include "lpm/lpm_api.h"
#if(UNIFIED_LOG_EN)
#include "vuc/VUC_MicronGetUnifiedEventLog.h"
#endif

/*
 * ---------------------------------------------------------------------------------------------------
 *   extern global variables
 * ---------------------------------------------------------------------------------------------------
 */
volatile ReadDisturbPRDHAndesReadCnt_t *gpReadDisturbPRDHAndesReadCnt	= (volatile ReadDisturbPRDHAndesReadCnt_t *)(OPTD_ANDES_MICRON_READ_CNT_BASE);
volatile ReadDisturbPRDHAndesRestoreInfo_t *gpReadDisturbPRDHAndesRestoreInfo = (volatile ReadDisturbPRDHAndesRestoreInfo_t *)(OPTD_ANDES_MICRON_RESTORE_FWPCA_BASE);
volatile ReadDisturbPRDHScanInfo_t *gpReadDisturbPRDHScanInfo = (volatile ReadDisturbPRDHScanInfo_t *)(OPTD_ANDES_MICRON_SCAN_INFO_BASE);
volatile ReadDisturbPRDH_t gReadDisturbPRDH = {0};
#if (READ_DISTURB_PRDH_RECORD_SCAN_TIME_EN)
volatile ReadDisturbPRDHScanTime_t *gpReadDisturbPRDHScanTime = (volatile ReadDisturbPRDHScanTime_t *)(OPTD_ANDES_MICRON_SCAN_TIME_BASE);
#endif /* (READ_DISTURB_PRDH_RECORD_SCAN_TIME_EN) */
#if (READ_DISTURB_PRDH_EN)
// A3 temp setting
U16 guwReadDisturbPRDHPredeterminePageList[PAGE_LIST_TYPE_NUM][VICTIM_PAGE_RULE_3_PAGE_LIST_NUM] = {
	//QLC
	{1408, 1416, 1432, 1448, 1464, 1480, 1512, 1528, 1544, 1560, 0, 1576, 8, 1592, 24, 40, INVALID_PAGE, INVALID_PAGE, INVALID_PAGE, INVALID_PAGE},
	//SLC
	{352, 356, 360, 364, 368, 376, 380, 384, 388, 0, 392, 4, 396, 8, 12, INVALID_PAGE, INVALID_PAGE, INVALID_PAGE, INVALID_PAGE, INVALID_PAGE}
};
#endif
/*
 * ---------------------------------------------------------------------------------------------------
 *   private
 * ---------------------------------------------------------------------------------------------------
 */
#if (READ_DISTURB_PRDH_EN)

void ReadDisturbPRDHRebuildScanInfo(void)
{
	U8 ubEventType = (gpVT->ReadDisturbPRDH.uwScanIdx / MAX_SUPPORT_DIE_NUM);
	U8 ubGlobalDie = (gpVT->ReadDisturbPRDH.uwScanIdx % MAX_SUPPORT_DIE_NUM);
	gpReadDisturbPRDHScanInfo->Full[ubEventType][ubGlobalDie][0].ulFWPCA = gpVT->ReadDisturbPRDH.ulScanFWPCA;
	gpReadDisturbPRDHScanInfo->Full[ubEventType][ubGlobalDie][0].Record.PRDH.uwURN = gpVT->ReadDisturbPRDH.uwScanURN;
	gpReadDisturbPRDHScanInfo->ulScanBMP[ubEventType] |= BIT(ubGlobalDie);
	++gpComm2Andes_Info->uwOverReadCntThresholdUnitNum;
}

void ReadDisturbPRDHUnfinishScanEventHandle(void)
{
	//Rebuild Scan Event
	if (FALSE == gpVT->ReadVerify.Info[READ_VERIFY_SRC_READ_DISTURB_UNIT].btReadVerifyAbort) {
		ReadDisturbPRDHRebuildScanInfo();
	}
	// Clear ReadVerify event
	gpVT->ReadVerify.ubWaitReadEvent &= ~(BIT(READ_VERIFY_SRC_READ_DISTURB_UNIT));
	gpVT->ReadVerify.Info[READ_VERIFY_SRC_READ_DISTURB_UNIT].btReadVerifyMeetsReadError = FALSE;
	gpVT->ReadVerify.Info[READ_VERIFY_SRC_READ_DISTURB_UNIT].btReadVerifyAbort = FALSE;
	gpVT->ReadVerify.Info[READ_VERIFY_SRC_READ_DISTURB_UNIT].ubState = READ_VERIFY_INIT;
	gpVT->ReadVerify.ubNowDoingEvent &= ~(BIT(READ_VERIFY_SRC_READ_DISTURB_UNIT));
}

void ReadDisturbPRDHRecordScanTime(void)
{
#if (READ_DISTURB_PRDH_RECORD_SCAN_TIME_EN)
	U8 ubDie, ubScanQueueIdx;
	U8 ubEventType = 0xFF;
	U16 uwScanIdx;
	U16 uwTotalEventNum = (EVENT_TYPE_FULL_NUM * MAX_SUPPORT_DIE_NUM + BLK_TYPE_OPEN_NUM);
	MicronScanInfo_t *pScanInfo = NULL;
	U32 *pStartTime = NULL;

	for (uwScanIdx = 0 ; uwScanIdx < (uwTotalEventNum) ; uwScanIdx++) {
		ubDie = (uwScanIdx % MAX_SUPPORT_DIE_NUM);
		ubEventType = (uwScanIdx / MAX_SUPPORT_DIE_NUM);
		if (gpReadDisturbPRDHScanInfo->ulScanBMP[ubEventType] & BIT(ubDie)) {
			pScanInfo = (MicronScanInfo_t *)&gpReadDisturbPRDHScanInfo->Full[ubEventType][ubDie];
			pStartTime = (U32 *)&gpReadDisturbPRDHScanTime->ulStartTime[ubEventType][ubDie];
			for (ubScanQueueIdx = 0 ; ubScanQueueIdx < SCAN_INFO_QUEUE_DEPTH ; ubScanQueueIdx++) {
				if ((INVALID_PCA_VALUE != pScanInfo[ubScanQueueIdx].ulFWPCA) && (INVALID_TIMER == pStartTime[ubScanQueueIdx])) {
					pStartTime[ubScanQueueIdx] = ((M_GET_FW_TIMER() / 1000) & INVALID_TIMER); //second
				}
			}
		}
	}
#endif /* (READ_DISTURB_PRDH_RECORD_SCAN_TIME_EN) */
}

U16 ReadDisturbPRDHGetQLCPageFromCoord(U8 ubX, U16 uwY)
{
	U8 ubCase = MLC_WL;
	U8 ubLXUNumber = (ubX % N48R_PAGE_PER_QLC_CELL); // LUXT
	U16 uwBase;
	U16 uwPageIdx = INVALID_PLANE_READ;
	if (IM_N48_WORDLINE_SECTION_1 > uwY) {
		uwBase = 0;
	}
	else if (IM_N48_WORDLINE_SECTION_2 > uwY) {
		uwY -= IM_N48_WORDLINE_SECTION_1;
		uwBase = IM_N48_SECTION_1;
		ubCase = QLC_WL;
	}
	else if (IM_N48_WORDLINE_SECTION_3 > uwY) {
		uwY -= IM_N48_WORDLINE_SECTION_2;
		uwBase = IM_N48_SECTION_2;
	}
	else if (IM_N48_WORDLINE_SECTION_4 > uwY) {
		uwY -= IM_N48_WORDLINE_SECTION_3;
		uwBase = IM_N48_SECTION_3;
		ubCase = QLC_WL;
	}
	else if (IM_N48_WORDLINE_SECTION_4 == uwY) {
		uwY -= IM_N48_WORDLINE_SECTION_4;
		uwBase = IM_N48_SECTION_4;
	}
	else {
		//No such y
		ubCase = 0xFF;
	}

	if (MLC_WL == ubCase) {
		if (ubLXUNumber < N48R_PAGE_PER_MLC_CELL) {
			uwPageIdx = uwBase + (uwY * N48R_PAGE_PER_MLC_WORD_LINE)
				+ (((ubX / N48R_PAGE_PER_QLC_CELL) * N48R_PAGE_PER_MLC_CELL) + ubLXUNumber);
		}
	}
	else if (QLC_WL == ubCase) {
		uwPageIdx = uwBase + (uwY * N48R_PAGE_PER_QLC_WORD_LINE) + ubX;
	}

	return uwPageIdx;
}

void ReadDisturbPRDHUpdateVTMotherPECG(void)
{
	U8 ubPECG; //ProgramEraseCntGroup
	U16 AverageEraseCnt = gpVT->VT.uwVTUnitEraseCnt;
	U16 *puwEraseCntRange;

	if (gFlhEnvMP.ulD1PECycle <= AverageEraseCnt) {

		ubPECG = (READ_DISTURB_MAX_ERASE_CNT_LEVEL_NUM - 1);
	}
	else {
		puwEraseCntRange = (U16 *)&gReadDisturb.Threshold.uwSLCEraseCntRange;

		for (ubPECG = 0; ubPECG < READ_DISTURB_MAX_ERASE_CNT_LEVEL_NUM; ubPECG++) {
			if (AverageEraseCnt <= puwEraseCntRange[ubPECG]) {
				break;
			}
		}
	}

	gpComm2Andes_Info->ubPECG[COP0_PCA_RULE_1] = ubPECG;
}

U8 ReadDisturbPRDHCheckBlkType(U16 uwUnit)
{
	U8 ubBlkType = COP0_USERDEF_BLK_TYPE_NONE;

	// VT first! To avoid invalid gpVT before Scan VT
	if (uwUnit == gpVT->VT.uwUnit[gpVT->VT.ubUnitIndex].B.uwUnit) {
		ubBlkType = COP0_USERDEF_BLK_TYPE_OPEN_VT;
	}
	else if (uwUnit == gpVT->VTChild.uwUnit.B.uwUnit) {
		ubBlkType = COP0_USERDEF_BLK_TYPE_OPEN_VT_CHILD;
	}
	else if (uwUnit == gpVT->Table.uwUnit[gpVT->Table.uwUnitIndex].B.uwUnit) {
		ubBlkType = COP0_USERDEF_BLK_TYPE_OPEN_TABLE;
	}
	else if (uwUnit == gpVT->GR.uwUnit[gpVT->GR.ubUnitIndex].B.uwUnit) {
		ubBlkType = COP0_USERDEF_BLK_TYPE_OPEN_GR;
	}
	else if (uwUnit == gpVT->InfoUnit.uwUnit.B.uwUnit) {
		ubBlkType = COP0_USERDEF_BLK_TYPE_OPEN_INIT_INFO;
	}
	else if (uwUnit == gpVT->ParityUnit.uwUnit.B.uwUnit) {
		ubBlkType = COP0_USERDEF_BLK_TYPE_OPEN_RS_PARITY;
	}
	else if (gpulVC[uwUnit].B.btStatic) {
		ubBlkType = COP0_USERDEF_BLK_TYPE_FULL;
	}

	return ubBlkType;
}

void ReadDisturbPRDHRecoverReadCnt(U32 ulBufferAddress)
{
	U8 ubBlkType, ubPlaneBankIdx;
	U16 uwShift, uwAndesCurrentUnit, uwFlashRecordUnit;
	MicronReadCnt_t *pAndesCurrentReadCnt = NULL;
	U32 *pulFlashRecordReadCnt = (U32 *)(ulBufferAddress);
	U32 *pulFlashRecordUnit = (U32 *)(ulBufferAddress + MICRON_READ_CNT_SAVE_UNIT_OFFSET);

	M_ARM_MODIFY_OPTD_REQUEST_START();

	for (ubBlkType = BLK_TYPE_OPEN_TABLE; ubBlkType < BLK_TYPE_OPEN_NUM; ubBlkType ++) {
		pAndesCurrentReadCnt = (MicronReadCnt_t *)&gpReadDisturbPRDHAndesReadCnt->OpenSLCBlk[ubBlkType];
		uwAndesCurrentUnit = gpReadDisturbPRDHAndesReadCnt->ulAndesRecordOpenSLCUnit[ubBlkType];
		uwFlashRecordUnit = pulFlashRecordUnit[ubBlkType];
		uwShift = ((ubBlkType - BLK_TYPE_OPEN_TABLE) * MAX_SUPPORT_BLK_NUM_PER_VB);

		if (INVALID_UNIT == uwAndesCurrentUnit) {
			// Andes never handle this type until now , except VT allways 0
			if (INVALID_UNIT == uwFlashRecordUnit) {
				// Both invalid , do nothing , should all zero in flash
			}
			else if (ubBlkType == ReadDisturbPRDHCheckBlkType(uwFlashRecordUnit)) {
				// Unit in flash is the same as it in VT , use flash
				gpReadDisturbPRDHAndesReadCnt->ulAndesRecordOpenSLCUnit[ubBlkType] = uwFlashRecordUnit;
			}
			else {
				// discard
				for (ubPlaneBankIdx = 0; ubPlaneBankIdx < MAX_SUPPORT_BLK_NUM_PER_VB; ubPlaneBankIdx ++) {
					pulFlashRecordReadCnt[uwShift + ubPlaneBankIdx] = 0;
				}
			}
		}
		else if (uwAndesCurrentUnit == uwFlashRecordUnit) {
			// Unit in flash is the same as it in Andes , ++
			for (ubPlaneBankIdx = 0; ubPlaneBankIdx < MAX_SUPPORT_BLK_NUM_PER_VB; ubPlaneBankIdx ++) {
				pulFlashRecordReadCnt[uwShift + ubPlaneBankIdx] += pAndesCurrentReadCnt[ubPlaneBankIdx].ulReadCnt;
			}
		}
		else {
			// Unit in flash is diff with it in Andes , use Andes
			for (ubPlaneBankIdx = 0; ubPlaneBankIdx < MAX_SUPPORT_BLK_NUM_PER_VB; ubPlaneBankIdx ++) {
				pulFlashRecordReadCnt[uwShift + ubPlaneBankIdx] = pAndesCurrentReadCnt[ubPlaneBankIdx].ulReadCnt;
			}
		}
	}

	DMACParam_t DMACParam = {{0}};
	DMACParam.ulSourceAddr 	= ulBufferAddress;
	DMACParam.ulDestAddr 	= OPTD_ANDES_MICRON_COPY_READ_CNT_BASE;
	DMACParam.ul32ByteNum 	= M_BYTE_TO_32BYTE_ALIGN(OPTD_ANDES_MICRON_COPY_READ_CNT_SIZE);
	DMACCopy(DMAC_MODE_NORMAL_QUEUE, &DMACParam, gulDMACDirectWaitDone_Callback, 0);

	gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
	while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
		DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
	}

	for (ubBlkType = BLK_TYPE_OPEN_TABLE; ubBlkType < BLK_TYPE_OPEN_NUM; ubBlkType++) {
		M_UART(READ_DISTURB_PRDH_, "\n [PRDH] Load BlockType:%b, Unit:%d\t%d\t%d\t%d\t%d", ubBlkType, gpReadDisturbPRDHAndesReadCnt->ulAndesRecordOpenSLCUnit[ubBlkType]
			, gpReadDisturbPRDHAndesReadCnt->OpenSLCBlk[ubBlkType][0].ulReadCnt
			, gpReadDisturbPRDHAndesReadCnt->OpenSLCBlk[ubBlkType][4].ulReadCnt
			, gpReadDisturbPRDHAndesReadCnt->OpenSLCBlk[ubBlkType][8].ulReadCnt
			, gpReadDisturbPRDHAndesReadCnt->OpenSLCBlk[ubBlkType][12].ulReadCnt);
	}

	gpComm2Andes_Info->uwOpenTableUnit = gpVT->Table.uwUnit[gpVT->Table.uwUnitIndex].B.uwUnit;

	// Check if over threshold
	for (ubBlkType = BLK_TYPE_OPEN_TABLE; ubBlkType < BLK_TYPE_OPEN_NUM; ubBlkType ++) {
		for (ubPlaneBankIdx = 0; ubPlaneBankIdx < MAX_SUPPORT_BLK_NUM_PER_VB; ubPlaneBankIdx ++) {
			ReadDisturbPRDHManualOpenBlkHandle(ubBlkType, gpReadDisturbPRDHAndesReadCnt->ulAndesRecordOpenSLCUnit[ubBlkType], ubPlaneBankIdx, 0);
		}
	}

	M_ARM_MODIFY_OPTD_REQUEST_END();
}

void ReadDisturbPRDHHandleAfterVTInit(void)
{
	U8 ubDieIdx, ubBlkType, ubPlaneBankIdx, ubTypeIdx;
	U16 uwWindowSize[EVENT_TYPE_FULL_NUM] = {0};
	MicronReadCnt_t *pReadCnt = NULL;

	// init parameter after D1 D3 erase cnt is handle done
	M_ARM_MODIFY_OPTD_REQUEST_START();

	ReadDisturbPRDHUpdateSLCPECG();
	ReadDisturbPRDHUpdateD3SLCAndQLCPECG();
	ReadDisturbPRDHUpdateVTMotherPECG();
	gpComm2Andes_Info->uwOpenTableUnit = gpVT->Table.uwUnit[gpVT->Table.uwUnitIndex].B.uwUnit;

	uwWindowSize[EVENT_TYPE_FULL_QLC]		= gReadDisturb.Threshold.uwTLCReadVerifyReadCntThreshold[gpComm2Andes_Info->ubPECG[COP0_PCA_RULE_0]];
	uwWindowSize[EVENT_TYPE_FULL_D3SLC] 	= gReadDisturb.Threshold.uwSLCReadVerifyReadCntThreshold[gpComm2Andes_Info->ubPECG[COP0_PCA_RULE_2]];
	uwWindowSize[EVENT_TYPE_FULL_SLC] 		= gReadDisturb.Threshold.uwSLCReadVerifyReadCntThreshold[gpComm2Andes_Info->ubPECG[COP0_PCA_RULE_3]];

	for (ubTypeIdx = 0; ubTypeIdx < EVENT_TYPE_FULL_NUM; ubTypeIdx++) {
		pReadCnt = (MicronReadCnt_t *)&gpReadDisturbPRDHAndesReadCnt->Full[ubTypeIdx];
		for (ubDieIdx = 0; ubDieIdx < (gubCENumber * gubDieNumber); ubDieIdx++) {
			if (0 == pReadCnt[ubDieIdx].PRDH.uwURN) {
				pReadCnt[ubDieIdx].PRDH.uwURN = (RngGetRandomValue() % uwWindowSize[ubTypeIdx]) + 1;
			}
		}
	}

	if ((FALSE == gpVT->NormalPowerOff.btSaveState) && (FALSE == gLPM.Control.B.btWakeupFromLPM3)) {
		//SPOR open block should change , clean open read cnt
		for (ubBlkType = BLK_TYPE_OPEN_TABLE; ubBlkType < BLK_TYPE_OPEN_NUM; ubBlkType++) {
			ReadDisturbPRDHClearOpenBlkReadCnt(ubBlkType);
		}
		M_UART(READ_DISTURB_PRDH_, "\n [PRDH] SPOR Clean OpenUnit ReadCnt");
	}

	//clean invalid VT ReadCnt
	for (ubPlaneBankIdx = 0; ubPlaneBankIdx < MAX_SUPPORT_BLK_NUM_PER_VB; ubPlaneBankIdx++) {
		if ((ubPlaneBankIdx == gpVT->VT.ubBlockIndex) || (ubPlaneBankIdx == (gpVT->VT.ubBlockIndex + 1))) {
			continue;
		}
		gpReadDisturbPRDHAndesReadCnt->OpenSLCBlk[BLK_TYPE_OPEN_VT][ubPlaneBankIdx].ulReadCnt = 0;
	}

	M_ARM_MODIFY_OPTD_REQUEST_END();
}

U8 ReadDisturbPRDHParseBlkType(U16 uwBypassReadCntHandle, U8 ubBlkType, U32 ulFWPCA)
{
	U8 ubResultBlkType;

	if (uwBypassReadCntHandle) {
		// Bypass folding read and block type which dont need to handle
		ubResultBlkType = COP0_USERDEF_BLK_TYPE_NONE;
	}
	else {
		if (COP0_USERDEF_BLK_TYPE_OPEN_TABLE >= ubBlkType) {
			// 1. Check for tie in without userdefine
			// 2. For COP1 always tie in COP0_USERDEF_BLK_TYPE_OPEN_TABLE , check if is Full table unit
			ubResultBlkType = ReadDisturbPRDHCheckBlkType(M_GET_UNIT_VALUE_FROM_PCA(ulFWPCA));
		}
		else {
			ubResultBlkType = ubBlkType;
		}
	}

	return ubResultBlkType;
}

U8 ReadDisturbPRDHManualOpenBlkHandle(U8 ubBlkType, U16 uwUnit, U8 ubPlaneBank, U8 ubReadCmdCnt)
{
	U8 ubAddLog = FALSE;
	U8 ubWaitScanEvent = FALSE;
	U8 ubEraseCntGroup, ubPECG; //ProgramEraseCntGroup
	U32 ulReadCntThreshold;
	MicronReadCnt_t *pReadCnt = NULL;

	ubEraseCntGroup = ((BLK_TYPE_OPEN_VT == ubBlkType) ? COP0_PCA_RULE_1 : COP0_PCA_RULE_2);
	ubPECG = gpComm2Andes_Info->ubPECG[ubEraseCntGroup];

	pReadCnt = (MicronReadCnt_t *)&gpReadDisturbPRDHAndesReadCnt->OpenSLCBlk[ubBlkType][ubPlaneBank];

	if (gpReadDisturbPRDHScanInfo->ulScanBMP[EVENT_TYPE_OPEN_BLK] & BIT(ubBlkType)) {
		ubWaitScanEvent = TRUE;
		if (uwUnit == M_GET_UNIT_VALUE_FROM_PCA(gpReadDisturbPRDHScanInfo->OpenSLCBlk[ubBlkType].ulFWPCA)) {
			// unit already in scan event
			return TRUE;
		}
	}

	if (uwUnit != gpReadDisturbPRDHAndesReadCnt->ulAndesRecordOpenSLCUnit[ubBlkType]) {
		ReadDisturbPRDHClearOpenBlkReadCnt(ubBlkType);
	}

	gpReadDisturbPRDHAndesReadCnt->ulAndesRecordOpenSLCUnit[ubBlkType] = uwUnit;
	pReadCnt->ulReadCnt += ubReadCmdCnt;
	ulReadCntThreshold = M_READ_DISTURB_SLC_FORCE_COPY_THRESHOLD_ADJUSTMENT(gReadDisturb.Threshold.uwSLCForceCopyReadCntThreshold[ubPECG]);

	if (ulReadCntThreshold) { // avoid threshold was not initialized
		if (pReadCnt->ulReadCnt >= ulReadCntThreshold) {
			if (FALSE == ubWaitScanEvent) {
				gpReadDisturbPRDHScanInfo->OpenSLCBlk[ubBlkType].ulFWPCA = ((uwUnit << gub4kEntrysPerUnitAlignLog) | (ubPlaneBank << gub4kEntrysPerPlaneLog));
				gpReadDisturbPRDHScanInfo->OpenSLCBlk[ubBlkType].Record.ulReadCnt = pReadCnt->ulReadCnt;
				gpReadDisturbPRDHScanInfo->ulScanBMP[EVENT_TYPE_OPEN_BLK] |= BIT(ubBlkType);
				++gpComm2Andes_Info->uwOverReadCntThresholdUnitNum;
				ReadDisturbPRDHClearOpenBlkReadCnt(ubBlkType);
				ubAddLog = TRUE;
			}
		}
	}

	return ubAddLog;
}

void ReadDisturbPRDHManualFullBlkHandle(U32 ulFWPCA, U8 ubPCARule, U8 ubMultiPlaneBMP, U8 ubReadCmdCnt, U8 ubDie)
{
	U8 ubGlobalDie = ((ulFWPCA >> gPCARule_G_CE.ubShift[ubPCARule]) & gPCARule_G_CE.ulMask) * gubDieNumber + ubDie;
	U8 ubIsMultiPlaneCmd = ((gubReadCmdPlaneNumByDMABMP[ubMultiPlaneBMP] > 1) ? TRUE : FALSE);
	U16 uwRandomValue = RngGetRandomValue();
	U8 ubRandomPlane, ubScanQueueIdx, ubPlaneIdx;
	U16 uwURN; //Uniform Random Number
	U32 ulWindowSize, ulReadCnt;

	if (COP0_PCA_RULE_2 <= ubPCARule) {
		ulWindowSize = gReadDisturb.Threshold.uwSLCReadVerifyReadCntThreshold[gpComm2Andes_Info->ubPECG[ubPCARule]];
		--ubPCARule; //QLC:0 , D3SLC:1 , SLC:2
	}
	else {
		ulWindowSize = gReadDisturb.Threshold.uwTLCReadVerifyReadCntThreshold[gpComm2Andes_Info->ubPECG[ubPCARule]];
	}

	MicronReadCnt_t *pReadCnt = (MicronReadCnt_t *)&gpReadDisturbPRDHAndesReadCnt->Full[ubPCARule][ubGlobalDie];
	MicronScanInfo_t *pScanInfo = (MicronScanInfo_t *)&gpReadDisturbPRDHScanInfo->Full[ubPCARule][ubGlobalDie];

	ulReadCnt 	= pReadCnt->PRDH.uwReadCnt;
	uwURN 		= pReadCnt->PRDH.uwURN;

	if (ulWindowSize && uwURN) { // Should not be zero

		if (ubIsMultiPlaneCmd) {
			// For Patch , handle read cmd one by one  (may be multi plane read
			// For Retry , handle read cmd all at once (expect to be single plane read
			ubReadCmdCnt = gubReadCmdPlaneNumByDMABMP[ubMultiPlaneBMP];
		}

		ulReadCnt += ubReadCmdCnt;

		if (ulReadCnt >= uwURN) {
			// Random select plane
			if (ubIsMultiPlaneCmd) {
				ubRandomPlane = (uwRandomValue % ubReadCmdCnt);
				for (ubPlaneIdx = 0; ubPlaneIdx < gubBurstsPerBank; ubPlaneIdx++) {
					if (ubMultiPlaneBMP & BIT(ubPlaneIdx)) {
						if (0 == ubRandomPlane) { //Change FWPCA to this plane
							ulFWPCA = ((ulFWPCA & ~(gubBurstsPerBankMask << gub4kEntrysPerPlaneLog)) | (ubPlaneIdx << gub4kEntrysPerPlaneLog));
							break;
						}
						--ubRandomPlane;
					}
				}
			}
			//Fill Scan Info
			for (ubScanQueueIdx = 0; ubScanQueueIdx < SCAN_INFO_QUEUE_DEPTH; ubScanQueueIdx++) {
				if (INVALID_PCA_VALUE == pScanInfo[ubScanQueueIdx].ulFWPCA) {
					pScanInfo[ubScanQueueIdx].ulFWPCA = ulFWPCA;
					pScanInfo[ubScanQueueIdx].Record.PRDH.uwReadCnt = ulReadCnt;
					pScanInfo[ubScanQueueIdx].Record.PRDH.uwURN = uwURN;
					++gpComm2Andes_Info->uwOverReadCntThresholdUnitNum;
					break;
				}
			}

			if (SCAN_INFO_QUEUE_DEPTH == ubScanQueueIdx) {
				//Without handling RC
				return;
			}

			M_UART(READ_DISTURB_PRDH_, "\n [PRDH] Manual Trigger PRDH, EventType:%b, Unit:%x, ReadCnt:%x, URN:%x", ubPCARule, M_GET_UNIT_VALUE_FROM_PCA(ulFWPCA), ulReadCnt, uwURN);

			gpReadDisturbPRDHScanInfo->ulScanBMP[ubPCARule] |= BIT(ubGlobalDie);
			pReadCnt->PRDH.uwURN = INVALID_URN_VALUE;
		}

		if (ulReadCnt >= ulWindowSize) {
			// Avoid ReadCnt >> WindowSize
			ulReadCnt = ((ulReadCnt - ulWindowSize) % ubReadCmdCnt);
			// Make URN > ReadCnt , URN will result in [ReadCnt+1 , WindowSize]
			uwURN = (uwRandomValue % ulWindowSize) + 1; // [1 , WindowSize]
			pReadCnt->PRDH.uwURN = ((ulReadCnt >= uwURN) ? (ulReadCnt + 1) : uwURN);
		}

		pReadCnt->PRDH.uwReadCnt = ulReadCnt;
	}
}

void ReadDisturbPRDHManualReadCntHandle(U8 ubBlkType, U32 ulFWPCA, U8 ubReadCmdCnt, U8 ubMultiPlaneBMP, U8 ubPCARule, U32 ulFSA)
{
	U8 ubHeadPlaneBank, ubPlaneIdx, ubDie;
	U8 ubMaxPlane = gubBurstsPerBank;
	U8 ubGlobalCE = ((ulFWPCA >> gPCARule_G_CE.ubShift[ubPCARule]) & gPCARule_G_CE.ulMask);
	U16 uwUnit = (U16)(ulFWPCA >> gub4kEntrysPerUnitAlignLog);

	if (COP0_USERDEF_BLK_TYPE_NONE != ubBlkType) {

		M_ARM_MODIFY_OPTD_REQUEST_START();

		if (BLK_TYPE_OPEN_NUM > ubBlkType) {

			if (uwUnit != gpReadDisturbPRDHAndesReadCnt->ulAndesRecordOpenSLCUnit[ubBlkType]) {
				// Due to this api will handle after Andes , believe Andes open unit
				// do nothing
			}
			else {
				ubHeadPlaneBank = ubGlobalCE * ubMaxPlane;
				for (ubPlaneIdx = 0; ubPlaneIdx < ubMaxPlane; ubPlaneIdx++) {
					// By PlaneBank handle ReadCnt
					if (ubMultiPlaneBMP & BIT(ubPlaneIdx)) {
						if (TRUE == ReadDisturbPRDHManualOpenBlkHandle(ubBlkType, uwUnit, (ubHeadPlaneBank + ubPlaneIdx), ubReadCmdCnt)) {
							// Already create error log
							break;
						}
					}
				}
			}
		}
		else if (BLK_TYPE_FULL == ubBlkType) {
			ubDie = ((ulFSA >> gPCARule_LUN.ubShift[ubPCARule]) & gPCARule_LUN.ulMask);
			ReadDisturbPRDHManualFullBlkHandle(ulFWPCA, ubPCARule, ubMultiPlaneBMP, ubReadCmdCnt, ubDie);
		}
		else {
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
		}

		M_ARM_MODIFY_OPTD_REQUEST_END();
	}
}

void ReadDisturbPRDHClearOpenBlkInfo(U16 uwUnit)
{
	U8 ubBlkType;
	M_ARM_MODIFY_OPTD_REQUEST_START();
	for (ubBlkType = BLK_TYPE_OPEN_TABLE; ubBlkType < BLK_TYPE_OPEN_NUM; ubBlkType++) {
		if (uwUnit == gpReadDisturbPRDHAndesReadCnt->ulAndesRecordOpenSLCUnit[ubBlkType]) {
			// Clear ReadCnt to avoid trigger
			ReadDisturbPRDHClearOpenBlkReadCnt(ubBlkType);
		}
		if (uwUnit == M_GET_UNIT_VALUE_FROM_PCA(gpReadDisturbPRDHScanInfo->OpenSLCBlk[ubBlkType].ulFWPCA)) {
			// Unit been add free , clear scan info
			gpReadDisturbPRDHScanInfo->OpenSLCBlk[ubBlkType].ulFWPCA = INVALID_PCA_VALUE;
			gpReadDisturbPRDHScanInfo->ulScanBMP[EVENT_TYPE_OPEN_BLK] &= ~(BIT(ubBlkType));
			--gpComm2Andes_Info->uwOverReadCntThresholdUnitNum;
			break;
		}
	}
	M_ARM_MODIFY_OPTD_REQUEST_END();
}

void ReadDisturbPRDHClearFullScanInfo(U16 uwUnit)
{
	U8 ubDieIdx;
	U8 ubScanQueueIdx;
	U8 ubScanIdx;
	U8 ubClearSituation;
	U8 ubEventType;
#if (READ_DISTURB_PRDH_RECORD_SCAN_TIME_EN)
	U32 *pStartTime = NULL;
#endif /* (READ_DISTURB_PRDH_RECORD_SCAN_TIME_EN) */

	M_ARM_MODIFY_OPTD_REQUEST_START();
	for (ubScanIdx = 0; ubScanIdx < (MAX_SUPPORT_DIE_NUM * EVENT_TYPE_FULL_NUM); ubScanIdx++) {
		ubEventType = (ubScanIdx / MAX_SUPPORT_DIE_NUM);
		ubDieIdx = (ubScanIdx % MAX_SUPPORT_DIE_NUM);
		if (gpReadDisturbPRDHScanInfo->ulScanBMP[ubEventType] & BIT(ubDieIdx)) {
			if ((gReadDisturb.btDoing) && (ubScanIdx == gReadDisturbPRDH.uwRecordScanIdx)) {
				//Skip ReadDisturb doing queue , start from queue 1
				ubScanQueueIdx = 1;
				ubClearSituation |= BIT(0);
			}
			else {
				ubScanQueueIdx = 0;
				ubClearSituation = 0;
			}
			MicronScanInfo_t *pScanInfo = (MicronScanInfo_t *)&gpReadDisturbPRDHScanInfo->Full[ubEventType][ubDieIdx];
#if (READ_DISTURB_PRDH_RECORD_SCAN_TIME_EN)
			pStartTime = (U32 *)&gpReadDisturbPRDHScanTime->ulStartTime[ubEventType][ubDieIdx];
#endif /* (READ_DISTURB_PRDH_RECORD_SCAN_TIME_EN) */
			for (; ubScanQueueIdx < SCAN_INFO_QUEUE_DEPTH; ubScanQueueIdx++) {
				if (INVALID_PCA_VALUE != pScanInfo[ubScanQueueIdx].ulFWPCA) {
					ubClearSituation |= BIT(ubScanQueueIdx);
					if (uwUnit == M_GET_UNIT_VALUE_FROM_PCA(pScanInfo[ubScanQueueIdx].ulFWPCA)) {
						pScanInfo[ubScanQueueIdx].ulFWPCA = INVALID_PCA_VALUE;
						--gpComm2Andes_Info->uwOverReadCntThresholdUnitNum;
						ubClearSituation &= (~ BIT(ubScanQueueIdx));
						M_UART(READ_DISTURB_PRDH_, "\n [PRDH] AddFree Clear Unit:%x", uwUnit);
#if (READ_DISTURB_PRDH_RECORD_SCAN_TIME_EN)
						pStartTime[ubScanQueueIdx] = INVALID_TIMER;
#endif /* (READ_DISTURB_PRDH_RECORD_SCAN_TIME_EN) */
					}
				}
			}
			if (0 == ubClearSituation) { // no info
				gpReadDisturbPRDHScanInfo->ulScanBMP[ubEventType] &= (~ BIT(ubDieIdx));
#if (READ_DISTURB_PRDH_RECORD_SCAN_TIME_EN)
				pStartTime[0] = INVALID_TIMER;
				pStartTime[1] = INVALID_TIMER;
#endif /* (READ_DISTURB_PRDH_RECORD_SCAN_TIME_EN) */
			}
			else if (BIT(1) == ubClearSituation) { // Q1 has info , Q0 been cleared
				pScanInfo[0].ulFWPCA = pScanInfo[1].ulFWPCA;
				pScanInfo[0].Record.ulReadCnt = pScanInfo[1].Record.ulReadCnt;
				pScanInfo[1].ulFWPCA = INVALID_PCA_VALUE;
#if (READ_DISTURB_PRDH_RECORD_SCAN_TIME_EN)
				pStartTime[0] = pStartTime[1];
				pStartTime[1] = INVALID_TIMER;
#endif /* (READ_DISTURB_PRDH_RECORD_SCAN_TIME_EN) */
			}
		}
	}
	M_ARM_MODIFY_OPTD_REQUEST_END();
}

void ReadDisturbPRDHGetScanInfo(U8 *pubBlkType, U32 *pulFWPCA)
{
	U8 ubDie, ubi;
	U8 ubEventType = 0xFF;
	U8 ubHit = FALSE;
	U16 uwScanIdx;
	U16 uwRecordScanIdx = gReadDisturbPRDH.uwRecordScanIdx;
	U16 uwTotalEventNum = (EVENT_TYPE_FULL_NUM * MAX_SUPPORT_DIE_NUM + BLK_TYPE_OPEN_NUM);
	U16 uwURN = INVALID_URN_VALUE; //Uniform Random Number
	U32 ulReadCnt = INVALID_PCA_VALUE;
	MicronScanInfo_t *pScanInfo = NULL;

	ReadDisturbPRDHRecordScanTime();

	M_ARM_MODIFY_OPTD_REQUEST_START();

	// Ring method to avoid always handling same event type
	for (uwScanIdx = uwRecordScanIdx ; uwScanIdx < (uwTotalEventNum) ; uwScanIdx++) {
		ubDie = (uwScanIdx % MAX_SUPPORT_DIE_NUM);
		ubEventType = (uwScanIdx / MAX_SUPPORT_DIE_NUM);
		if (gpReadDisturbPRDHScanInfo->ulScanBMP[ubEventType] & BIT(ubDie)) {
			ubHit = TRUE;
			break;
		}
	}

	if (FALSE == ubHit) {
		for (uwScanIdx = 0 ; uwScanIdx < uwRecordScanIdx ; uwScanIdx++) {
			ubDie = (uwScanIdx % MAX_SUPPORT_DIE_NUM);
			ubEventType = (uwScanIdx / MAX_SUPPORT_DIE_NUM);
			if (gpReadDisturbPRDHScanInfo->ulScanBMP[ubEventType] & BIT(ubDie)) {
				ubHit = TRUE;
				break;
			}
		}
	}

	if (TRUE == ubHit) {

		if (EVENT_TYPE_OPEN_BLK > ubEventType) {
			*pubBlkType = BLK_TYPE_FULL;
			pScanInfo = (MicronScanInfo_t *)&gpReadDisturbPRDHScanInfo->Full[ubEventType][ubDie];
			for (ubi = 0 ; ubi < SCAN_INFO_QUEUE_DEPTH ; ubi++) {
				if (INVALID_PCA_VALUE != pScanInfo[ubi].ulFWPCA) {
					*pulFWPCA 	= pScanInfo[ubi].ulFWPCA;
					ulReadCnt 	= pScanInfo[ubi].Record.PRDH.uwReadCnt;
					uwURN 		= pScanInfo[ubi].Record.PRDH.uwURN;
					break;
				}
			}
		}
		else if (EVENT_TYPE_OPEN_BLK == ubEventType) {
			*pubBlkType = ubDie; // die in open blk event is block type
			pScanInfo = (MicronScanInfo_t *)&gpReadDisturbPRDHScanInfo->OpenSLCBlk[ubDie];
			if (INVALID_PCA_VALUE != pScanInfo->ulFWPCA) {
				*pulFWPCA 	= pScanInfo->ulFWPCA;
				ulReadCnt 	= pScanInfo->Record.ulReadCnt;
			}
		}
		else {
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
		}

		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (INVALID_PCA_VALUE != *pulFWPCA));

		gReadDisturbPRDH.ubBlkType = *pubBlkType;
		gReadDisturbPRDH.ulFWPCA = *pulFWPCA;
		gReadDisturbPRDH.uwRecordScanIdx = uwScanIdx;
		gReadDisturbPRDH.ulReadCnt = ulReadCnt;
		gReadDisturbPRDH.uwURN = uwURN;
		M_UART(READ_DISTURB_PRDH_, "\n [PRDH] GetScanInfo BlkType:%b, Unit:%x, RC:%x, URN:%x", *pubBlkType, M_GET_UNIT_VALUE_FROM_PCA(*pulFWPCA), ulReadCnt, uwURN);
	}
	else {
		gpComm2Andes_Info->uwOverReadCntThresholdUnitNum = 0;
		M_UART(READ_DISTURB_PRDH_, "\n [PRDH] No EventBMP");
	}

	M_ARM_MODIFY_OPTD_REQUEST_END();
}

void ReadDisturbPRDHClearScanInfo(void)
{
	U8 ubClearBMP = TRUE;
	U16 uwRecordScanIdx = gReadDisturbPRDH.uwRecordScanIdx;
	U8 ubEventType = (uwRecordScanIdx / MAX_SUPPORT_DIE_NUM);
	U8 ubDie = (uwRecordScanIdx % MAX_SUPPORT_DIE_NUM);
	MicronScanInfo_t *pScanInfo = NULL;
#if (READ_DISTURB_PRDH_RECORD_SCAN_TIME_EN)
	U32 *pStartTime = (U32 *)&gpReadDisturbPRDHScanTime->ulStartTime[ubEventType][ubDie];
#endif /* (READ_DISTURB_PRDH_RECORD_SCAN_TIME_EN) */

	// Next round start index
	++gReadDisturbPRDH.uwRecordScanIdx;

	M_ARM_MODIFY_OPTD_REQUEST_START();

	if (EVENT_TYPE_OPEN_BLK > ubEventType) {
		pScanInfo = (MicronScanInfo_t *)&gpReadDisturbPRDHScanInfo->Full[ubEventType][ubDie];
	}
	else if (EVENT_TYPE_OPEN_BLK == ubEventType) {
		pScanInfo = (MicronScanInfo_t *)&gpReadDisturbPRDHScanInfo->OpenSLCBlk[ubDie];
	}
	else {
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
	}

	// Clear Scan Info
	if (gpReadDisturbPRDHScanInfo->ulScanBMP[ubEventType] & BIT(ubDie)) {

		if (EVENT_TYPE_OPEN_BLK > ubEventType) {
			pScanInfo = (MicronScanInfo_t *)&gpReadDisturbPRDHScanInfo->Full[ubEventType][ubDie];
			if (INVALID_PCA_VALUE != pScanInfo[1].ulFWPCA) {
				pScanInfo[0].ulFWPCA = pScanInfo[1].ulFWPCA;
				pScanInfo[0].Record.ulReadCnt = pScanInfo[1].Record.ulReadCnt; //union
				pScanInfo[1].ulFWPCA = INVALID_PCA_VALUE;
#if (READ_DISTURB_PRDH_RECORD_SCAN_TIME_EN)
				pStartTime[0] = pStartTime[1];
				pStartTime[1] = INVALID_TIMER;
#endif /* (READ_DISTURB_PRDH_RECORD_SCAN_TIME_EN) */
				ubClearBMP = FALSE;
			}
			else {
				pScanInfo[0].ulFWPCA = INVALID_PCA_VALUE;
#if (READ_DISTURB_PRDH_RECORD_SCAN_TIME_EN)
				pStartTime[0] = INVALID_TIMER;
#endif /* (READ_DISTURB_PRDH_RECORD_SCAN_TIME_EN) */
			}
		}
		else if (EVENT_TYPE_OPEN_BLK == ubEventType) {
			gpReadDisturbPRDHScanInfo->OpenSLCBlk[ubDie].ulFWPCA = INVALID_PCA_VALUE;
		}
		else {
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
		}

		if (ubClearBMP) {
			//empty
			gpReadDisturbPRDHScanInfo->ulScanBMP[ubEventType] &= ~(BIT(ubDie));
#if (READ_DISTURB_PRDH_RECORD_SCAN_TIME_EN)
			pStartTime[0] = INVALID_TIMER;
			pStartTime[1] = INVALID_TIMER;
#endif /* (READ_DISTURB_PRDH_RECORD_SCAN_TIME_EN) */
		}
	}
	else {
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
	}

	M_ARM_MODIFY_OPTD_REQUEST_END();
}

void ReadDisturbPRDHCreateErrorLog(void)
{
	ErrLogInfo_t ErrorLogInfo = {0};
	U16 uwForceCopyUnit = gReadDisturb.uwMaxReadCntUnit;
	U8 ubBlkType = gReadDisturbPRDH.ubBlkType;
	U8 ubPECG = ((BLK_TYPE_OPEN_VT == ubBlkType) ? gpComm2Andes_Info->ubPECG[COP0_PCA_RULE_1] : gpComm2Andes_Info->ubPECG[COP0_PCA_RULE_2]); //ProgramEraseCntGroup

	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (INVALID_BLOCK != uwForceCopyUnit));

	M_UART(READ_DISTURB_PRDH_, "\n [PRDH] Create Error Log, BlkType:%b, Unit:%x", ubBlkType, uwForceCopyUnit);

	ErrorLogInfo.uwUnit = uwForceCopyUnit;
	ErrorLogInfo.ubPlaneBank = ((BLK_TYPE_OPEN_VT == ubBlkType) ? gpVT->VT.ubBlockIndex : ERROR_LOG_PLANE_BANK_FOR_READ_DISTURB);
	ErrorLogInfo.ubNeedCopy = TRUE;
	ErrorLogInfo.ubDirectHandle = ((BLK_TYPE_OPEN_VT == ubBlkType) || (BLK_TYPE_OPEN_VT_CHILD == ubBlkType));
	ErrorLogInfo.ubFailPhase = ERROR_LOG_PHASE_COPY;
	ErrHandleErrorLog(&ErrorLogInfo);

	++gpVTDBUF->ReadDisturb.ulForceSwapUnitCnt;

#if(UNIFIED_LOG_EN && IM_N48R)
	U8 ubCurrentTempC, ubCodeword = 0;
	UNIFIED_LOG_INFO_T NewUnifiedLogInfo = {0};
	NewUnifiedLogInfo.uwUnit = gReadDisturb.uwMaxReadCntUnit;
	NewUnifiedLogInfo.ubCH = (((gReadDisturb.ulSentinelPlaneIdx << gub4kEntrysPerPlaneLog) >> gPCARule_Channel.ubShift[COP0_PCA_RULE_2]) & gPCARule_Channel.ulMask);
	NewUnifiedLogInfo.ubCE = (((gReadDisturb.ulSentinelPlaneIdx << gub4kEntrysPerPlaneLog) >> gPCARule_Bank.ubShift[COP0_PCA_RULE_2]) & gPCARule_Bank.ulMask);
	NewUnifiedLogInfo.ubLUN = (gubDieNumber > 1) ? (gpuwVBRMP[gReadDisturb.uwMaxReadCntUnit].B.ToRUT_Unit & 1) : 0;
	NewUnifiedLogInfo.uwBlock = gpuwVBRMP[gReadDisturb.uwMaxReadCntUnit].B.ToRUT_Unit * 4 + (((gReadDisturb.ulSentinelPlaneIdx << gub4kEntrysPerPlaneLog) >> gPCARule_Plane.ubShift[COP0_PCA_RULE_2]) & gPCARule_Plane.ulMask);
	NewUnifiedLogInfo.uwPage = (((gReadDisturb.ulSentinelPlaneIdx << gub4kEntrysPerPlaneLog) >> gPCARule_Page.ubShift[COP0_PCA_RULE_2]) & gPCARule_Page.ulMask);
	NewUnifiedLogInfo.ubBlkType = 0x05; //This event is always open SLC

	ubCurrentTempC = (gTT.ubCurrentTemperatureExternal.B.btSign) ? ((~gTT.ubCurrentTemperatureExternal.B.Degree) + 1) : gTT.ubCurrentTemperatureExternal.B.Degree;

	M_UART(RDT_TEST_, "\n\n\t[RDL]\tEvent\tCH\tCE\tLUN\tPgTp\tBlk\tPg\tCW\tBlkTp\tRefreshTp\tPEC\ttempC");
	M_UART(RDT_TEST_, "\n\t[RDL]\t%l\t%b\t%b\t%b\t%b\t%l", UNIFIED_LOG_ID_RDL, NewUnifiedLogInfo.ubCH, NewUnifiedLogInfo.ubCE, NewUnifiedLogInfo.ubLUN, NewUnifiedLogInfo.ubPageType, NewUnifiedLogInfo.uwBlock);
	M_UART(RDT_TEST_, "\t%l\t%b\t%b\t%b\t%l\t%b\n", NewUnifiedLogInfo.uwPage, ubCodeword, ubBlkType, UNIFIED_LOG_RDL_UNDEFINED, gpulEC[NewUnifiedLogInfo.uwUnit].B.uwEraseCnt, ubCurrentTempC);
	UnifiedLogRDLOverReadCntEventAdd(NewUnifiedLogInfo, UNIFIED_LOG_RDL_UNDEFINED);
#endif

	if (DRIVE_LOG_EN) {
		ReadDisturbPRDHAddDriveLog(READ_DISTURB_PRDH_FORCECOPY_LOG, uwForceCopyUnit, gReadDisturbPRDH.ulReadCnt, ubPECG);
	}

	gReadDisturb.ubState = READ_DISTURB_FINISH;

}

void ReadDisturbPRDHFinish(void)
{
	if (gpComm2Andes_Info->uwOverReadCntThresholdUnitNum) {
		--gpComm2Andes_Info->uwOverReadCntThresholdUnitNum;
		ReadDisturbPRDHClearScanInfo();
	}
	gpVTDBUF->ReadDisturb.uwOverReadCntThresholdUnitNum = gpComm2Andes_Info->uwOverReadCntThresholdUnitNum;
	gReadDisturb.ubState = READ_DISTURB_GET_UNIT;
	gReadDisturb.btDoing = FALSE;
	M_UART(READ_DISTURB_PRDH_, "\n [PRDH] finish\n\n");
}

void ReadDisturbPRDHGetOrCheckUnit(void)
{
	U8 ubBlkType 	= BLK_TYPE_INVALID;
	U32 ulFWPCA 	= INVALID_PCA_VALUE;
	U16 uwScanUnit 	= INVALID_BLOCK;
	U8 ubPlaneBankIdx;
	U16 uwPage;
	Unit_t uwUnit = {0};

	if (READ_DISTURB_GET_UNIT == gReadDisturb.ubState) {
		//Get scan info from optd
		ReadDisturbPRDHGetScanInfo(&ubBlkType, &ulFWPCA);

		uwScanUnit = (U16)(ulFWPCA >> gub4kEntrysPerUnitAlignLog);
		gReadDisturb.uwMaxReadCntUnit = uwScanUnit;

		if (BLK_TYPE_FULL > ubBlkType) {
			// Open Blk Blindly Folding
			gReadDisturb.ubState = READ_DISTURB_CREATE_ERROR_LOG;
		}
		else if (BLK_TYPE_FULL == ubBlkType) {
			gReadDisturb.ulSentinelPlaneIdx = ((ulFWPCA & gul4kEntrysPerUnitAlignMask) >> gub4kEntrysPerPlaneLog);
			gReadDisturb.ubState = READ_DISTURB_CHECK_UNIT;
			gReadDisturb.btDoing = TRUE;
		}
	}

	if (READ_DISTURB_CHECK_UNIT == gReadDisturb.ubState) {

		uwScanUnit = gReadDisturb.uwMaxReadCntUnit;
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (INVALID_BLOCK != uwScanUnit));

		if (ReadDisturbPRDHDecideCheck(uwScanUnit)) {

			uwUnit.B.uwUnit = uwScanUnit;

			if (SUCCESS == FTLSetReadVerifyEvent(uwUnit, READ_VERIFY_SRC_READ_DISTURB_UNIT, VICTIM_PAGE_RULE_NUM)) {

				gpVT->ReadDisturbPRDH.ulScanFWPCA 	= gReadDisturbPRDH.ulFWPCA;
				gpVT->ReadDisturbPRDH.uwScanURN 	= gReadDisturbPRDH.uwURN;
				gpVT->ReadDisturbPRDH.uwScanIdx		= gReadDisturbPRDH.uwRecordScanIdx;

				++gpVTDBUF->ReadDisturb.ulReadVerifyUnitCnt;
				if (DRIVE_LOG_EN) {
					ReadDisturbPRDHAddDriveLog(READ_DISTURB_PRDH_READVERIFY_LOG, uwScanUnit, gReadDisturbPRDH.ulReadCnt, gReadDisturbPRDH.uwURN);
				}

				// Calculate Victim page for ReadVerify
				uwPage = gReadDisturb.ulSentinelPlaneIdx >> gubPlanesPerSuperPageLog;
				ubPlaneBankIdx = gReadDisturb.ulSentinelPlaneIdx & gubPlanesPerSuperPageMask;

				ReadDisturbPRDHSelectVictimPage(gpuwVBRMP[uwScanUnit].B.btIsSLCMode, uwPage, ubPlaneBankIdx);

				gReadDisturb.ubState = READ_DISTURB_WAIT_VERIFY_DONE;
			}
		}
		else {
			gReadDisturb.ubState = READ_DISTURB_FINISH;
			M_UART(READ_DISTURB_PRDH_, "\n [PRDH] Skip handle");
		}
	}
}

void ReadDisturbPRDHParameterInit(void)
{
	U8 ubi, ubDieIdx, ubPCARuleIdx;

	for (ubi = 0; ubi < SCAN_INFO_QUEUE_DEPTH; ubi++) {
		for (ubDieIdx = 0; ubDieIdx < (gubCENumber * gubDieNumber); ubDieIdx++) {
			for (ubPCARuleIdx = 0; ubPCARuleIdx < EVENT_TYPE_FULL_NUM; ubPCARuleIdx++) {
				gpReadDisturbPRDHScanInfo->Full[ubPCARuleIdx][ubDieIdx][ubi].ulFWPCA = INVALID_PCA_VALUE;
			}
		}
	}

	for (ubi = 0; ubi < BLK_TYPE_OPEN_NUM; ubi++) {
		gpReadDisturbPRDHScanInfo->OpenSLCBlk[ubi].ulFWPCA = INVALID_PCA_VALUE;
		if (BLK_TYPE_OPEN_VT == ubi) {
			continue;
		}
		if (0 == gpReadDisturbPRDHAndesReadCnt->ulAndesRecordOpenSLCUnit[ubi]) {
			gpReadDisturbPRDHAndesReadCnt->ulAndesRecordOpenSLCUnit[ubi] = INVALID_UNIT;
		}
	}

#if (READ_DISTURB_PRDH_RECORD_SCAN_TIME_EN)
	U8 ubDie, ubScanQueueIdx;
	U8 ubEventType = 0xFF;
	U16 uwScanIdx;
	U16 uwTotalEventNum = (EVENT_TYPE_FULL_NUM * MAX_SUPPORT_DIE_NUM + BLK_TYPE_OPEN_NUM);
	U32 *pStartTime = NULL;

	for (uwScanIdx = 0 ; uwScanIdx < (uwTotalEventNum) ; uwScanIdx++) {
		ubDie = (uwScanIdx % MAX_SUPPORT_DIE_NUM);
		ubEventType = (uwScanIdx / MAX_SUPPORT_DIE_NUM);
		pStartTime = (U32 *)&gpReadDisturbPRDHScanTime->ulStartTime[ubEventType][ubDie];
		for (ubScanQueueIdx = 0 ; ubScanQueueIdx < SCAN_INFO_QUEUE_DEPTH ; ubScanQueueIdx++) {
			pStartTime[ubScanQueueIdx] = INVALID_TIMER;
		}
	}
#endif /* (READ_DISTURB_PRDH_RECORD_SCAN_TIME_EN) */

	if (TRUE == gLPM.Control.B.btWakeupFromLPM3) {
		M_UART(READ_DISTURB_PRDH_, "\n [PRDH] LPM3 Recover ReadCnt");
		// Recover ReadCnt from EC
		DMACParam_t DMACParam = {{0}};
		DMACParam.ulSourceAddr 	= DBUF_READ_DISTURB_PRDH_INFO;
		DMACParam.ulDestAddr 	= OPTD_ANDES_MICRON_COPY_READ_CNT_BASE;
		DMACParam.ul32ByteNum 	= M_BYTE_TO_32BYTE_ALIGN(OPTD_ANDES_MICRON_COPY_READ_CNT_SIZE);
		DMACCopy(DMAC_MODE_NORMAL_QUEUE, &DMACParam, gulDMACDirectWaitDone_Callback, 0);

		gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
		while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
			DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
		}
	}
}

void ReadDisturbPRDHClearOpenBlkReadCnt(U8 ubBlkType)
{
	U8 ubPlaneBankIdx;
	MicronReadCnt_t *pReadCnt = (MicronReadCnt_t *)&gpReadDisturbPRDHAndesReadCnt->OpenSLCBlk[ubBlkType];
	for (ubPlaneBankIdx = 0; ubPlaneBankIdx < MAX_SUPPORT_BLK_NUM_PER_VB; ubPlaneBankIdx++) {
		pReadCnt[ubPlaneBankIdx].ulReadCnt = 0;
	}
}

U8 ReadDisturbPRDHCheckBitErrorCnt(U16 uwPage, U8 ubRefreshThreshold, U8 ubALUSelect)
{
	FlhMT_t MT;
	L4KTable16B_t *L4kPtr = NULL;
	MTCfg_t uoMTCfg;
	U8 ubMTIdx[CHECK_BIT_ERROR_CNT_MT_NUM], ubIdx;
	U8 ubFrameIdx;
	U8 ubResult = PASS;
	U16 uwErrorBitNum = 0;
	RetryJob_t *pCurrentRetryJob = &gpRetry->RetryJobList[gpRetry->ubHead];
	// change to target page
	U32 ulFSA0 = ((pCurrentRetryJob->pErrorMT->dma.uliFSA0_1 & (~(gPCARule_Page.ulMask << gPCARule_Page.ubShift[ubALUSelect]))) | (uwPage << gPCARule_Page.ubShift[ubALUSelect]));
	ulFSA0 &= ~gPCARule_Entry.ulMask;

	for (ubIdx = 0; ubIdx < CHECK_BIT_ERROR_CNT_MT_NUM; ubIdx++) {

		memcpy((void *)&MT, (void *)(pCurrentRetryJob->pErrorMT), MT_SIZE);
		if (0 == ubIdx) { //cmd MT

			ubMTIdx[ubIdx] = FlaGetFreeMTIndex();

			MT.cmd.btUpdPollingSequence = TRUE;
			MT.cmd.btBusy = TRUE; // Using autopol
			MT.cmd.btiFSAEn = TRUE;
			MT.cmd.uliFSA0_1 = ulFSA0;
#if (FIP_VIRTUAL_ADDRESS_WORKAROUND_EN)
			M_FIP_SET_POL_SEQUENCE_SELECT(MT.cmd.POL_SEQ_SEL, (M_FIP_GET_FSA_DIE_NUMBER(MT.cmd.uliFSA0_1, MT.cmd.ALUSelect) ? POL_SEQ_FPU_ENTRY_READ_STATUS_BUSY_20_DIE1 : POL_SEQ_FPU_ENTRY_READ_STATUS_BUSY_20));
#else /* (FIP_VIRTUAL_ADDRESS_WORKAROUND_EN) */
			M_FIP_SET_POL_SEQUENCE_SELECT(MT.cmd.POL_SEQ_SEL, POL_SEQ_FPU_ENTRY_READ_STATUS_BUSY_20); // Pol true ready
#endif /* (FIP_VIRTUAL_ADDRESS_WORKAROUND_EN) */

			MT.cmd.btMTPFormat = 0;
			MT.cmd.NormalTargetCPU = MT_TARGET_CPU0;
			MT.cmd.ErrorTargetCPU = MT_TARGET_CPU0;
			MT.cmd.btDisableUDMA = TRUE;
#if (PS5017_EN || PS5021_EN)
			MT.cmd.uwFPUPtr = ((ubALUSelect >= COP0_PCA_RULE_2) ? FPU_PTR_OFFSET(fpu_entry_slc_1p_30_read) : FPU_PTR_OFFSET(fpu_entry_tlc_1p_30_read));
#else /* (PS5017_EN || PS5021_EN) */
			MT.cmd.uwFPUPtr = ((ubALUSelect >= COP0_PCA_RULE_2) ? FPU_PTR_OFFSET(fpu_entry_slc_1p_30_read_bin0) : FPU_PTR_OFFSET(fpu_entry_tlc_1p_30_read_bin0));
#endif /* (PS5017_EN || PS5021_EN) */

#if (VIRTUAL_ADDRESS_EN)
			// Virtual Address Setting
			U8 ubBinValue = M_PATCH_CMD_GET_MICRON_BIN_LEVEL_FROM_DMA(MT.dma.ulUserDefine);
#if (PS5017_EN || PS5021_EN)
			MT.cmd.VA0 = ((1 == ubBinValue) ? 0x10 : 0); //To Do , Temp solution for valley track
			MT.cmd.VAMode = 1;
#else /* (PS5017_EN || PS5021_EN) */
			MT.cmd.uwFPUPtr += (M_BIN_LEVEL_READ_FPU_OFFSET_GAP(FLASH_ONE_PLANE_OPERATION) * ubBinValue);
#endif /* (PS5017_EN || PS5021_EN) */
#endif /* (VIRTUAL_ADDRESS_EN) */

		}
		else { // DMA MT

			ubMTIdx[ubIdx] = FlaGetFreeMTIndex();
			gMTMgr.ubRecordExtInfoMT = ubMTIdx[ubIdx]; // for FIPDelegate record BEC when EOT

			MT.dma.btBufferMode = 0;
			MT.dma.btZipEn = FALSE;
			MT.dma.btConversionBypass = TRUE;
			MT.dma.btCRCCheckDis = TRUE;
			MT.dma.btLDPCCorrectEn = TRUE;
			MT.dma.btCompareEn = FALSE;
			MT.dma.btGC = FALSE;
#if (PS5017_EN)
			MT.dma.btFpuPCAEn = TRUE;
			MT.dma.EOTChkEn = TRUE;
#endif /* (PS5017_EN) */
			MT.dma.btForce_R_Fail = FALSE;
			MT.dma.btMTPFormat = 1;
			MT.dma.NorTarCPU = MT_TARGET_CPU0;
			MT.dma.ErrTarCPU = MT_TARGET_CPU0;
			MT.dma.btBMUAllocateEn = FALSE;
			MT.dma.btDisableUDMA = TRUE;

			MT.dma.uliFSA0_1 = ulFSA0;
			MT.dma.FrameNum = FRAMES_PER_PAGE;
			MT.dma.L4KSparePtr = SPR_RETRY_OFF;
			for (ubFrameIdx = 0; ubFrameIdx < FRAMES_PER_PAGE; ubFrameIdx++) {
				L4kPtr = (L4KTable16B_t *)(IRAM_BASE + SPR_RETRY_OFF + ubFrameIdx * L4K_SIZE);
				L4kPtr->BitMap.Read.ubBufferValid = 0;
				L4kPtr->BitMap.Read.Zinfo = MAX_ZINFO;
			}
		}
		memcpy((void *)M_MT_ADDR(ubMTIdx[ubIdx]), &MT, MT_SIZE);

		uoMTCfg.uoAll = 0;
		uoMTCfg.bits_recc.ubQUE_IDX = MT.dma.ubCEValue;
		uoMTCfg.bits_recc.ubMT_IDX = ubMTIdx[ubIdx];
		FlaGlobalTrigger(&uoMTCfg);
	}

	ubIdx = 0;

	while (CHECK_BIT_ERROR_CNT_MT_NUM > ubIdx) {
		if (gMTMgr.ubMTDoneMsg[ubMTIdx[ubIdx] - MT_RETRY_START_INDEX].btAllDone) {

			if (1 == ubIdx) { //DMA

				if (gMTMgr.ulINTInfo[ubMTIdx[ubIdx] - MT_RETRY_START_INDEX] & UNCORR_UPD_BIT) {
					uwErrorBitNum = READ_VERIFY_EOT_UNC;
					ubResult = FAIL;
				}
				else if (gMTMgr.ulINTInfo[ubMTIdx[ubIdx] - MT_RETRY_START_INDEX] & OVER_ECC_ERR_UPD_BIT) {
					uwErrorBitNum = gMTMgr.uwECCErrorBitNum;
					if (gMTMgr.uwECCErrorBitNum > ubRefreshThreshold) {
						ubResult = FAIL;
					}
				}
#if (MEDIA_SCAN_EN)
				if (uwErrorBitNum) {
					U8 ubChannel, ubBank, ubDie, ubGlobalDie;
					ubChannel = (ulFSA0 >> gPCARule_Channel.ubShift[ubALUSelect]) & gPCARule_Channel.ulMask;
					ubBank = (ulFSA0 >> gPCARule_Bank.ubShift[ubALUSelect]) & gPCARule_Bank.ulMask;
					ubDie = (ulFSA0 >> gPCARule_LUN.ubShift[ubALUSelect]) & gPCARule_LUN.ulMask;	//Rule 0/2 is same, Micron no LMU slot.
					ubGlobalDie = M_GLOBAL_DIE_IDX(ubDie, ubChannel, ubBank, BIT(gPCARule_LUN.ubBit_No), BIT(gPCARule_Channel.ubBit_No));
					MediaScanCheckErrorBitNum(uwErrorBitNum, ubGlobalDie);
				}
#endif /*(MEDIA_SCAN_EN)*/
				M_UART(READ_DISTURB_PRDH_, "\n [PRDH] VicPage:%d %s BEC:%d ", uwPage, ((FAIL == ubResult) ? ("FAIL") : ("SUCCESS")), gMTMgr.uwECCErrorBitNum);
			}
			FlaAddFreeMTIndex(ubMTIdx[ubIdx]);
			++ubIdx;
		}
		FIPDelegateCmd();
		FWErrRecorder();
	}

	return ubResult;
}


void ReadDisturbPRDHVictimPageFurtherCheck(U8 ubPageType, U8 ubALUSelect, U16 uwUnitIdx, U8 ubPlaneBank, U16 uwPage, U8 ubTableIdx)
{
	U8 ubSLC = ((COP0_PCA_RULE_2 <= ubALUSelect) ? TRUE : FALSE);
	U8 ubRefreshThreshold = (ubSLC ? SLC_REFRESH_THRESHOLD : QLC_REFRESH_THRESHOLD);
	U8 ubWordLineBottomEdge0, ubWordLineBottomEdge1, ubWordLineTopEdge0, ubWordLineTopEdge1;
	U8 ubX, ubcheckIdx;
	U16 uwY, uwChkWL, uwCheckPage;
	U8 ubStartIdx = 0;
	U8 ubEndIdx = 2;

	// VICTIM_PAGE_RULE_1 : Add   +- 1 WL to mapping table
	// VICTIM_PAGE_RULE_2 : Check +- 2 WL , add to mapping table if check bit error cnt fail

	if (ubSLC) {
		ubWordLineBottomEdge0 	= SLC_WORDLINE_BOTTOM_EDGE_0;
		ubWordLineBottomEdge1 	= SLC_WORDLINE_BOTTOM_EDGE_1;
		ubWordLineTopEdge0		= SLC_WORDLINE_TOP_EDGE_0;
		ubWordLineTopEdge1		= SLC_WORDLINE_TOP_EDGE_1;
		ubX = (uwPage % N48R_SUBBLK_NUM);
		uwY = (uwPage / N48R_SUBBLK_NUM);
	}
	else {
		ubWordLineBottomEdge0 	= QLC_WORDLINE_BOTTOM_EDGE_0;
		ubWordLineBottomEdge1 	= QLC_WORDLINE_BOTTOM_EDGE_1;
		ubWordLineTopEdge0		= QLC_WORDLINE_TOP_EDGE_0;
		ubWordLineTopEdge1		= QLC_WORDLINE_TOP_EDGE_1;
		ubX = FTLGetCoord(uwPage, IM_GETCOORD_X_VAL);
		uwY = FTLGetCoord(uwPage, IM_GETCOORD_Y_VAL);
	}

	if ((uwY == ubWordLineBottomEdge0) || (uwY == (ubWordLineBottomEdge0 + ubPageType))) { // without - 1/2 WL
		ubStartIdx = 1;
	}
	else if ((uwY == ubWordLineTopEdge0) || (uwY == (ubWordLineTopEdge0 - ubPageType))) { // without + 1/2 WL
		ubEndIdx = 1;
	}
	else if ((uwY == ubWordLineBottomEdge1) || (uwY == (ubWordLineBottomEdge1 + ubPageType))) { // without - 1/2 WL
		ubStartIdx = 1;
	}
	else if ((uwY == ubWordLineTopEdge1) || (uwY == (ubWordLineTopEdge1 - ubPageType))) { // without + 1/2 WL
		ubEndIdx = 1;
	}

	for (ubcheckIdx = ubStartIdx; ubcheckIdx < ubEndIdx; ubcheckIdx++) {
		if (0 == ubcheckIdx) {
			uwChkWL = uwY - (ubPageType + 1); //- 1/2 WL
		}
		else {
			uwChkWL = uwY + (ubPageType + 1); //+ 1/2 WL
		}

		uwCheckPage = ReadDisturbPRDHGetPageFromCoord(ubSLC, ubX, uwChkWL);

		if (VICTIM_PAGE_RULE_2 == ubPageType) {
			if (PASS == ReadDisturbPRDHCheckBitErrorCnt(uwCheckPage, ubRefreshThreshold, ubALUSelect)) {
				continue; // skip add mapping table
			}
		}
		//add mapping table
		WordLineFoldingUpdateTableSrcPhysical(uwUnitIdx, ubPlaneBank, uwCheckPage, ubTableIdx);
	}
}

void ReadDisturbPRDHFillVictimPlaneIdx(U8 ubSLC, U8 ubPlaneBankIdx)
{
	U8 ubVictimPageType;
	U32 ulPlaneIdx;

	for (ubVictimPageType = 0; ubVictimPageType < VICTIM_PAGE_RULE_NUM; ubVictimPageType++) {
		if (ubSLC) {
			ulPlaneIdx = (gReadDisturbPRDH.uwVictimPage[ubVictimPageType] << gubPlanesPerSuperPageLog) + ubPlaneBankIdx;
		}
		else {
			ulPlaneIdx = FTLPhysical2PlaneInx(gReadDisturbPRDH.uwVictimPage[ubVictimPageType], ubPlaneBankIdx);
		}
		gReadDisturbPRDH.ulVictimPlaneIdx[ubVictimPageType] = ulPlaneIdx;
		M_UART(READ_DISTURB_PRDH_, "\n [PRDH] %s, VicPage:%d", (ubSLC ? ("S") : ("Q")), gReadDisturbPRDH.uwVictimPage[ubVictimPageType]);
	}
}


U16 ReadDisturbPRDHGetPageFromCoord(U8 ubSLC, U8 ubX, U16 uwY)
{
	U16 uwPage;
	if (ubSLC) {
		uwPage = (uwY * N48R_SUBBLK_NUM) + ubX;
	}
	else {
		uwPage = ReadDisturbPRDHGetQLCPageFromCoord(ubX, uwY);
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (INVALID_PLANE_READ != uwPage));
	}
	return uwPage;
}

void ReadDisturbPRDHSelectVictimPage(U8 ubSLC, U16 uwPage, U8 ubPlaneBankIdx)
{
	U8 ubCellType, ubCandidateX, ubLowerPageShift, ubX;
	U16 uwCandidatePage, uwY;
	U8 ubWordLineBottomEdge0, ubWordLineBottomEdge1, ubWordLineTopEdge0, ubWordLineTopEdge1;

	if (ubSLC) {
		ubWordLineBottomEdge0 	= SLC_WORDLINE_BOTTOM_EDGE_0;
		ubWordLineBottomEdge1 	= SLC_WORDLINE_BOTTOM_EDGE_1;
		ubWordLineTopEdge0		= SLC_WORDLINE_TOP_EDGE_0;
		ubWordLineTopEdge1		= SLC_WORDLINE_TOP_EDGE_1;
		ubCellType = SLC_WL;
		ubX = (uwPage % N48R_SUBBLK_NUM);
		uwY = (uwPage / N48R_SUBBLK_NUM);
		ubLowerPageShift = 0;
	}
	else {
		ubWordLineBottomEdge0 	= QLC_WORDLINE_BOTTOM_EDGE_0;
		ubWordLineBottomEdge1 	= QLC_WORDLINE_BOTTOM_EDGE_1;
		ubWordLineTopEdge0		= QLC_WORDLINE_TOP_EDGE_0;
		ubWordLineTopEdge1		= QLC_WORDLINE_TOP_EDGE_1;
		ubCellType = QLC_WL;
		ubX = FTLGetCoord(uwPage, IM_GETCOORD_X_VAL);
		uwY = FTLGetCoord(uwPage, IM_GETCOORD_Y_VAL);
		ubX &= ~(BIT_MASK(N48R_PAGE_PER_QLC_CELL_BIT_NUM)); //LP for QLC is 0,4,8,12
		ubLowerPageShift = N48R_PAGE_PER_QLC_CELL_BIT_NUM; //LP for QLC is 0,4,8,12
	}

	//***********************************************************
	// VICTIM_PAGE_RULE_1: randomly select page with L0 in neighbor WL
	// (in the same physical block and subblock)
	//***********************************************************
	if ((uwY == ubWordLineBottomEdge0) || (uwY == ubWordLineBottomEdge1)) {
		uwY += 1; // only +1 WL
	}
	else if ((uwY == ubWordLineTopEdge0) || (uwY == ubWordLineTopEdge1)) {
		uwY -= 1; // only -1 WL
	}
	else {
		// random select neighbor WL (+ or -1)
		if (RngGetRandomValue() & BIT0) {
			++uwY;
		}
		else {
			--uwY;
		}
		// Check QLC Victim WordLine type
		if (!ubSLC) {
			if (IM_N48_WORDLINE_SECTION_1 > uwY) {
				ubCellType = MLC_WL;
			}
			else if ((IM_N48_WORDLINE_SECTION_2 <= uwY) && (IM_N48_WORDLINE_SECTION_3 > uwY)) {
				ubCellType = MLC_WL;
			}
			else if (IM_N48_WORDLINE_SECTION_4 == uwY) {
				ubCellType = MLC_WL;
			}
		}
	}

	uwCandidatePage = ReadDisturbPRDHGetPageFromCoord(ubSLC, ubX, uwY);

	if (MLC_WL == ubCellType) { // MLC UP has L0
		++uwCandidatePage;
	}

	gReadDisturbPRDH.uwVictimPage[VICTIM_PAGE_RULE_1] = uwCandidatePage;

	//***********************************************************
	// VICTIM_PAGE_RULE_2: randomly select LP in other subblock
	// (in the same physical block)
	//***********************************************************
	do {
		ubCandidateX = ((RngGetRandomValue() % N48R_SUBBLK_NUM) << ubLowerPageShift);//LP for QLC is 0,4,8,12
	} while (ubCandidateX == ubX); //other subblock

	uwY = (RngGetRandomValue() % (ubWordLineTopEdge1 + 1)); // [0:WordLineTopEdge1]

	gReadDisturbPRDH.uwVictimPage[VICTIM_PAGE_RULE_2] = ReadDisturbPRDHGetPageFromCoord(ubSLC, ubCandidateX, uwY);

	//***********************************************************
	// VICTIM_PAGE_RULE_3: head page from predetermined list(MAG provide)
	//***********************************************************
	do {
		uwCandidatePage = guwReadDisturbPRDHPredeterminePageList[ubSLC][(RngGetRandomValue() % VICTIM_PAGE_RULE_3_PAGE_LIST_NUM)];
	} while (uwCandidatePage == INVALID_PAGE);
	gReadDisturbPRDH.uwVictimPage[VICTIM_PAGE_RULE_3] = uwCandidatePage;
	// Gen PlaneIdx for ReadVerify
	ReadDisturbPRDHFillVictimPlaneIdx(ubSLC, ubPlaneBankIdx);

}


U8 ReadDisturbPRDHDecideCheck(U16 uwOverReadCntUnit)
{
	U8 ubUnitIdx;
	U8 ubActiveGR = FALSE;

	if (gErrSummary.uwErrorLogNum) {
		if (gErrSummary.uwTail != ErrHandleCheckUnitInFailLog(uwOverReadCntUnit, FALSE)) {
			return FALSE;
		}
	}

	if (gpVT->FTL.btNowNeedGCTable) {
		for (ubUnitIdx = 0; ubUnitIdx < gFTLState.TableGC.ubGCSourceNum; ubUnitIdx++) {
			if (uwOverReadCntUnit == gFTLState.TableGC.uwGCSource[ubUnitIdx]) {
				return FALSE;
			}
		}
		for (ubUnitIdx = 0; ubUnitIdx < gFTLState.TableGC.ubGCVCZeroSourceNum; ubUnitIdx++) {
			if (uwOverReadCntUnit == gFTLState.TableGC.uwGCVCZeroSource[ubUnitIdx]) {
				return FALSE;
			}
		}
	}

	if (TRUE == gpulVC[uwOverReadCntUnit].B.btNoPTEBMP) {
		return FALSE;
	}

	for (ubUnitIdx = 0; ubUnitIdx <= gpVT->GR.ubUnitIndex; ubUnitIdx++ ) {
		if (gpVT->GR.uwUnit[ubUnitIdx].B.uwUnit == uwOverReadCntUnit) {
			ubActiveGR = TRUE;
			break;
		}
	}

	if ((0 == gpulVC[uwOverReadCntUnit].B.ValidCnt) && (FALSE == ubActiveGR)) {
		return FALSE;
	}

	return TRUE;
}

void ReadDisturbPRDHAddDriveLog(U8 ubLogType, U16 uwUnitIdx, U32 ulReadCnt, U16 uwEraseCnt)
{
	U8 ubEventType = (gReadDisturbPRDH.uwRecordScanIdx / MAX_SUPPORT_DIE_NUM);
	U8 ubGlobalDie = (gReadDisturbPRDH.uwRecordScanIdx % MAX_SUPPORT_DIE_NUM);
	DriveLogPayload_t DriveLogPayload = {{0}};
	if (READ_DISTURB_PRDH_READVERIFY_LOG == ubLogType) {
		DriveLogPayload.Payload_Read_Disturb.Type.btReadVerifyUnit = TRUE;
		DriveLogPayload.Payload_Read_Disturb.ulSentinelPlaneIdx = gReadDisturb.ulSentinelPlaneIdx;
		DriveLogPayload.Payload_Read_Disturb.ubPlane	 = gReadDisturb.ulSentinelPlaneIdx & gubBurstsPerBankMask;
		DriveLogPayload.Payload_Read_Disturb.uwReadCnt 	= gpReadDisturbPRDHAndesReadCnt->Full[ubEventType][ubGlobalDie].PRDH.uwReadCnt;
	}
	else {
		DriveLogPayload.Payload_Read_Disturb.Type.btForceSwapUnit = TRUE;
		DriveLogPayload.Payload_Read_Disturb.uwReadCnt = M_READ_DISTURB_SLC_FORCE_COPY_THRESHOLD_REDUCTION(ulReadCnt);
	}
	DriveLogPayload.Payload_Read_Disturb.uwUnit = uwUnitIdx;
	DriveLogPayload.Payload_Read_Disturb.ulForceSwapUnitCnt = gpVTDBUF->ReadDisturb.ulForceSwapUnitCnt;
	DriveLogPayload.Payload_Read_Disturb.ulReadVerifyUnitCnt = gpVTDBUF->ReadDisturb.ulReadVerifyUnitCnt;
	DriveLogPayload.Payload_Read_Disturb.uwOverReadCntThresholdUnitNum = gpComm2Andes_Info->uwOverReadCntThresholdUnitNum;
	DriveLogPayload.Payload_Read_Disturb.ubEventType = ubEventType;
	DriveLogPayload.Payload_Read_Disturb.ubGlobalDie = ubGlobalDie;

	DriveLogPayload.Payload_Read_Disturb.uwEraseCnt = uwEraseCnt; // Is URN for ReadVerify Log
	DriveLogAdd(READ_DISTURB_LOG, &DriveLogPayload, DRIVE_LOG_ADD_DEFAULT_MODE);
}

void ReadDisturbPRDHCopyReadCntToEC(void)
{
	U8 ubBlkType;
	DMACParam_t DMACParam = {{0}};

	M_ARM_MODIFY_OPTD_REQUEST_START();

	DMACParam.ulSourceAddr 	= OPTD_ANDES_MICRON_COPY_READ_CNT_BASE;
	DMACParam.ulDestAddr 	= DBUF_READ_DISTURB_PRDH_INFO;
	DMACParam.ul32ByteNum 	= M_BYTE_TO_32BYTE_ALIGN(OPTD_ANDES_MICRON_COPY_READ_CNT_SIZE);
	DMACCopy(DMAC_MODE_NORMAL_QUEUE, &DMACParam, gulDMACDirectWaitDone_Callback, 0);

	gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;

	for (ubBlkType = BLK_TYPE_OPEN_TABLE; ubBlkType < BLK_TYPE_OPEN_NUM; ubBlkType++) {
		M_UART(READ_DISTURB_PRDH_, "\n [PRDH] Save BlockType:%b, Unit:%d\t%d\t%d\t%d\t%d", ubBlkType, gpReadDisturbPRDHAndesReadCnt->ulAndesRecordOpenSLCUnit[ubBlkType]
			, gpReadDisturbPRDHAndesReadCnt->OpenSLCBlk[ubBlkType][0].ulReadCnt
			, gpReadDisturbPRDHAndesReadCnt->OpenSLCBlk[ubBlkType][4].ulReadCnt
			, gpReadDisturbPRDHAndesReadCnt->OpenSLCBlk[ubBlkType][8].ulReadCnt
			, gpReadDisturbPRDHAndesReadCnt->OpenSLCBlk[ubBlkType][12].ulReadCnt);
	}
	M_UART(READ_DISTURB_PRDH_, "\n");

	while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
		DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
	}
	M_ARM_MODIFY_OPTD_REQUEST_END();
}

#endif /* (READ_DISTURB_PRDH_EN) */
