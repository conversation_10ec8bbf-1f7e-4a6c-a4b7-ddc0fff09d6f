#include "retry_trim_table.h"

#if (LOAD_RETRY_TRIM_TABLE_EN)
#define RETRY_TRIM_TABLE_SECTION  __attribute__ ((section("IRAM_RETRY_TRIM_TABLE")))

RETRY_TRIM_TABLE_PTR gpulRetryTrimTable;

RETRY_TRIM_TABLE gRetryTrimTable RETRY_TRIM_TABLE_SECTION = {
	// Trim Address
	{
		0x0F65, 0x0F66, 0x0F67, 0x0F68, 0x0F69, 0x0F6A, 0x0F6B, 0x0F6C,
		0x0F7F, 0x0F80, 0x0F81, 0x0F82, 0x0F83, 0x0F84, 0x0F85, 0x0F86,
		0x0F99, 0x0F9A, 0x0F9B, 0x0F9C, 0x0F9D, 0x0F9E, 0x0F9F, 0x0FA0,
		0x0FB3, 0x0FB4, 0x0FB5, 0x0FB6, 0x0FB7, 0x0FB8, 0x0FB9, 0x0FBA,
		0x0FCD, 0x0FCE, 0x0FCF, 0x0FD0, 0x0FD1, 0x0FD2, 0x0FD3, 0x0FD4,
		0x0FE7, 0x0FE8, 0x0FE9, 0x0FEA, 0x0FEB, 0x0FEC, 0x0FED, 0x0FEE,
		0x1001, 0x1002, 0x1003, 0x1004, 0x1005, 0x1006, 0x1007, 0x1008,
		0x101B, 0x101C, 0x101D, 0x101E, 0x101F, 0x1020, 0x1021, 0x1022,
		0x1035, 0x1036, 0x1037, 0x1038, 0x1039, 0x103A, 0x103B, 0x103C,
		0x104F, 0x1050, 0x1051, 0x1052, 0x1053, 0x1054, 0x1055, 0x1056,
		0x1069, 0x106A, 0x106B, 0x106C, 0x106D, 0x106E, 0x106F, 0x1070,
		0x1083, 0x1084, 0x1085, 0x1086, 0x1087, 0x1088, 0x1089, 0x108A,
		0x109D, 0x109E, 0x109F, 0x10A0, 0x10A1, 0x10A2, 0x10A3, 0x10A4,
		0x10B7, 0x10B8, 0x10B9, 0x10BA, 0x10BB, 0x10BC, 0x10BD, 0x10BE,
		0x10D1, 0x10D2, 0x10D3, 0x10D4, 0x10D5, 0x10D6, 0x10D7, 0x10D8,
		0x0DE1, 0x0DE2, 0x0DE3, 0x0DE4, 0x0DE5, 0x0DE6, 0x0DE7, 0x0DE8,
		0x0DF3, 0x0DF4, 0x0DF5, 0x0DF6, 0x0DF7, 0x0DF8, 0x0DF9, 0x0DFA,
		0x0E05, 0x0E06, 0x0E07, 0x0E08, 0x0E09, 0x0E0A, 0x0E0B, 0x0E0C,
		0x0E17, 0x0E18, 0x0E19, 0x0E1A, 0x0E1B, 0x0E1C, 0x0E1D, 0x0E1E,
		0x0E29, 0x0E2A, 0x0E2B, 0x0E2C, 0x0E2D, 0x0E2E, 0x0E2F, 0x0E30,
		0x0E3B, 0x0E3C, 0x0E3D, 0x0E3E, 0x0E3F, 0x0E40, 0x0E41, 0x0E42,
		0x0E4D, 0x0E4E, 0x0E4F, 0x0E50, 0x0E51, 0x0E52, 0x0E53, 0x0E54,
		0x0E95, 0x0E96, 0x0E97, 0x0E98, 0x0E99, 0x0E9A, 0x0E9B, 0x0E9C,
		0x0EAF, 0x0EB0, 0x0EB1, 0x0EB2, 0x0EB3, 0x0EB4, 0x0EB5, 0x0EB6,
		0x0EC9, 0x0ECA, 0x0ECB, 0x0ECC, 0x0ECD, 0x0ECE, 0x0ECF, 0x0ED0,
		0x0EE3, 0x0EE4, 0x0EE5, 0x0EE6, 0x0EE7, 0x0EE8, 0x0EE9, 0x0EEA,
		0x0EFD, 0x0EFE, 0x0EFF, 0x0F00, 0x0F01, 0x0F02, 0x0F03, 0x0F04,
		0x0F17, 0x0F18, 0x0F19, 0x0F1A, 0x0F1B, 0x0F1C, 0x0F1D, 0x0F1E,
		0x0F31, 0x0F32, 0x0F33, 0x0F34, 0x0F35, 0x0F36, 0x0F37, 0x0F38,
		0x0E83, 0x0E84, 0x0E85, 0x0E86, 0x0E87, 0x0E88, 0x0E89, 0x0E8A,
		0x0E71, 0x0E72, 0x0E73, 0x0E74, 0x0E75, 0x0E76, 0x0E77, 0x0E78,
		0x0DCF, 0x0DD0, 0x0DD1, 0x0DD2, 0x0DD3, 0x0DD4, 0x0DD5, 0x0DD6,
		0x0E5F, 0x0E60, 0x0E61, 0x0E62, 0x0E63, 0x0E64, 0x0E65, 0x0E66,
		0x0F4B, 0x0F4C, 0x0F4D, 0x0F4E, 0x0F4F, 0x0F50, 0x0F51, 0x0F52,
	},
	// Trim Value
	{
		0x02, 0xFE, 0x04, 0xFC, 0x08, 0xF8, 0x10, 0xF0,
		0x02, 0xFE, 0x04, 0xFC, 0x08, 0xF8, 0x10, 0xF0,
		0x02, 0xFE, 0x04, 0xFC, 0x08, 0xF8, 0x10, 0xF0,
		0x02, 0xFE, 0x04, 0xFC, 0x08, 0xF8, 0x10, 0xF0,
		0x02, 0xFE, 0x04, 0xFC, 0x08, 0xF8, 0x10, 0xF0,
		0x02, 0xFE, 0x04, 0xFC, 0x08, 0xF8, 0x10, 0xF0,
		0x02, 0xFE, 0x04, 0xFC, 0x08, 0xF8, 0x10, 0xF0,
		0x02, 0xFE, 0x04, 0xFC, 0x08, 0xF8, 0x10, 0xF0,
		0x02, 0xFE, 0x04, 0xFC, 0x08, 0xF8, 0x10, 0xF0,
		0x02, 0xFE, 0x04, 0xFC, 0x08, 0xF8, 0x10, 0xF0,
		0x02, 0xFE, 0x04, 0xFC, 0x08, 0xF8, 0x10, 0xF0,
		0x02, 0xFE, 0x04, 0xFC, 0x08, 0xF8, 0x10, 0xF0,
		0x02, 0xFE, 0x04, 0xFC, 0x08, 0xF8, 0x10, 0xF0,
		0x02, 0xFE, 0x04, 0xFC, 0x08, 0xF8, 0x10, 0xF0,
		0x02, 0xFE, 0x04, 0xFC, 0x08, 0xF8, 0x10, 0xF0,
		0x04, 0xFC, 0x08, 0xF8, 0x10, 0xF0, 0x20, 0xE0,
		0x04, 0xFC, 0x08, 0xF8, 0x10, 0xF0, 0x20, 0xE0,
		0x04, 0xFC, 0x08, 0xF8, 0x10, 0xF0, 0x20, 0xE0,
		0x04, 0xFC, 0x08, 0xF8, 0x10, 0xF0, 0x20, 0xE0,
		0x04, 0xFC, 0x08, 0xF8, 0x10, 0xF0, 0x20, 0xE0,
		0x04, 0xFC, 0x08, 0xF8, 0x10, 0xF0, 0x20, 0xE0,
		0x04, 0xFC, 0x08, 0xF8, 0x10, 0xF0, 0x20, 0xE0,
		0x04, 0xFC, 0x08, 0xF8, 0x10, 0xF0, 0x20, 0xE0,
		0x04, 0xFC, 0x08, 0xF8, 0x10, 0xF0, 0x20, 0xE0,
		0x04, 0xFC, 0x08, 0xF8, 0x10, 0xF0, 0x20, 0xE0,
		0x04, 0xFC, 0x08, 0xF8, 0x10, 0xF0, 0x20, 0xE0,
		0x04, 0xFC, 0x08, 0xF8, 0x10, 0xF0, 0x20, 0xE0,
		0x04, 0xFC, 0x08, 0xF8, 0x10, 0xF0, 0x20, 0xE0,
		0x04, 0xFC, 0x08, 0xF8, 0x10, 0xF0, 0x20, 0xE0,
		0x0C, 0xF4, 0x18, 0xE8, 0x30, 0xD0, 0x60, 0xA0,
		0x0C, 0xF4, 0x18, 0xE8, 0x30, 0xD0, 0x60, 0xA0,
		0x0C, 0xF4, 0x18, 0xE8, 0x30, 0xD0, 0x60, 0xA0,
		0x0C, 0xF4, 0x18, 0xE8, 0x30, 0xD0, 0x60, 0xA0,
		0x0C, 0xF4, 0x18, 0xE8, 0x30, 0xD0, 0x60, 0xA0,
	},
	//uwBFEABinOffsetTrimAddress
	{
		0x0098, 0x00A2, 0x00AC, 0x00C8, 0x0052,
		0x005C, 0x0066, 0x0070,	0x007A, 0x0084,
		0x008E, 0x0099, 0x00A3, 0x00AD, 0x00C9,
		0x0053,	0x005D, 0x0067, 0x0071, 0x007B,
		0x0085, 0x008F, 0x009A, 0x00A4,	0x00AE,
		0x00CA, 0x0054, 0x005E, 0x0068, 0x0072,
		0x007C, 0x0086,	0x0090, 0x009B, 0x00A5,
		0x00AF, 0x00CB, 0x0055, 0x005F, 0x0069,
		0x0073, 0x007D, 0x0087, 0x0091, 0x004E,
	},
	//ubBFEABinOffsetTrimValue
	{
#if(BFEA_BIN_SET == 0)
		0xFD, 0xFA, 0xF6, 0xFA, 0x00,
		0xFD, 0xFC, 0xFA, 0xF9, 0xF6,
		0xF0, 0xFB, 0xF5, 0xED, 0xF5,
		0x00, 0xFB, 0xF9, 0xF5, 0xF3,
		0xED, 0xE2, 0xFB, 0xF4, 0xEB,
		0xF4, 0x01, 0xFB, 0xF8, 0xF4,
		0xF1, 0xEB, 0xDE, 0x01, 0x03,
		0x04, 0x03, 0x00, 0x01, 0x02,
		0x03, 0x03, 0x04, 0x06, 0x00,
#elif(BFEA_BIN_SET == 1)
		0xFE, 0xFA, 0xF6, 0xFA, 0x02,
		0xFE, 0xFC, 0xFA, 0xF9, 0xF6,
		0xF1, 0xFC, 0xF5, 0xEC, 0xF5,
		0x04, 0xFC, 0xF8, 0xF5, 0xF2,
		0xEC, 0xE2, 0xFB, 0xF3, 0xE9,
		0xF3, 0x05, 0xFB, 0xF7, 0xF3,
		0xEF, 0xE9, 0xDC, 0x01, 0x02,
		0x04, 0x02, 0x00, 0x01, 0x02,
		0x02, 0x03, 0x04, 0x06, 0x00,
#elif(BFEA_BIN_SET == 2)
		0xFA, 0xF4, 0xE9, 0xF4, 0xFC,
		0xFA, 0xF7, 0xF4, 0xF0, 0xE9,
		0xDD, 0xF9, 0xF2, 0xE5, 0xF2,
		0xFC, 0xF9, 0xF5, 0xF2, 0xED,
		0xE5, 0xD6, 0xF7, 0xEF, 0xE0,
		0xEF, 0xFC, 0xF7, 0xF3, 0xEF,
		0xEA, 0xE0, 0xCF, 0x01, 0x02,
		0x04, 0x02, 0x01, 0x01, 0x02,
		0x02, 0x03, 0x04, 0x07, 0x00,
#elif(BFEA_BIN_SET == 3)
		0xFB, 0xF6, 0xEC, 0xF6, 0xFC,
		0xFB, 0xFA, 0xF6, 0xF2, 0xEC,
		0xE1, 0xFA, 0xF4, 0xE8, 0xF4,
		0xFC, 0xFA, 0xF8, 0xF4, 0xEF,
		0xE8, 0xDB, 0xF9, 0xF2, 0xE5,
		0xF2, 0xFB, 0xF9, 0xF7, 0xF2,
		0xED, 0xE5, 0xD5, 0x01, 0x02,
		0x04, 0x02, 0x01, 0x01, 0x01,
		0x02, 0x03, 0x04, 0x06, 0x00,
#endif
	}
};
#endif /* (LOAD_RETRY_TRIM_TABLE_EN) */

