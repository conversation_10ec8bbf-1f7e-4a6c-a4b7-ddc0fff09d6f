#ifndef _BOOT_API_H_
#define _BOOT_API_H_

#include "symbol.h"

//1 load code error status
#define E_FLH_NOT_READY						    (BIT0)
#define E_FLH_TIMEOUT							(BIT1)
#define E_FLH_NO_CHANNEL						(BIT2)
#define E_PRAMEN_FAIL							(BIT3)
#define E_FLH_IDPG_SCAN_FAIL					(BIT4) // use Scan method to scan ID page but no ID page found
#define E_FLH_SET_FEATURE_FAIL			    	(BIT5)
#define E_1ST_IDPG_FAIL						    (BIT6)
#define E_2ND_IDPG_FAIL						    (BIT7)
#define E_FWSLOT_FAIL				            (BIT8)
#define E_CODESECTION_CHECK_CRC_FAIL      	    (BIT9)
#define E_CODE_SIGN_HEADER_LOAD_FAIL	        (BIT10)
#define	E_CODE_SIGN_SIGNATURE_LOAD_FAIL         (BIT11)
#define E_CODE_SIGN_COMPARE_FAIL				(BIT12)
#define E_SEC_PARITY_ERROR					    (BIT13)
#define E_DMAC_PARITY_ERROR					    (BIT14)
#define E_IRAM_PARITY_ERROR					    (BIT15)
#define E_CODE_SIGN_PARSING_SIGNARUTE_FAIL      (BIT16)
#define E_CODE_SIGN_RSA_DECODE_FAIL             (BIT17)

#endif /* _BOOT_API_H_ */
