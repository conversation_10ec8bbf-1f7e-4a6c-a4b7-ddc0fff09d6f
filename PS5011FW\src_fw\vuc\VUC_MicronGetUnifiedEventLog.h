#ifndef SRC_FW_VUC_VUC_MICRONGETUNIFIEDEVENTLOG_H_
#define SRC_FW_VUC_VUC_MICRONGETUNIFIEDEVENTLOG_H_

#include "common/typedef.h"
#include "aom/aom_api.h"
#include "hal/bmu/bmu_api.h"
#include "retry/retry.h"

//Event ID defination:
#define UNIFIED_LOG_ID_POR								(0x0001)
#define UNIFIED_LOG_ID_GBB								(0x0002)
#define UNIFIED_LOG_ID_BGS								(0x0003)
#define UNIFIED_LOG_ID_RDL								(0x0004)
#define UNIFIED_LOG_ID_DEEPERR							(0x0005)
#define UNIFIED_LOG_ID_VRLC								(0x0010)
#define UNIFIED_LOG_ID_TEMPCOBYPEC						(0x0016)
#define UNIFIED_LOG_ID_BFEA_FORCE_COMBINE				(0x0017)
#define UNIFIED_LOG_ID_BFEA_FREEZE_APL_FAMILY_CREATION	(0x0018)
#define UNIFIED_LOG_ID_MS								(0x0020)
#define UNIFIED_LOG_ID_EBC_FAIL							(0x0022)
#define UNIFIED_LOG_ID_MS_FOLD							(0x0023)
#define UNIFIED_LOG_ID_MS_FOLD_STATS					(0x0024)
#define UNIFIED_LOG_ID_REFCAL_FAILED					(0x0025)
#define UNIFIED_LOG_ID_SELECT_GATE						(0x0026)
#define UNIFIED_LOG_ID_RD_SCAN							(0x00A0)
#define UNIFIED_LOG_ID_TURBO_RAIN						(0x00A1)




//defect type define:
#define UNIFIED_LOG_GBB_TYPE_UNDEFINED		(0x00)
#define UNIFIED_LOG_GBB_TYPE_ERASE_FAIL		(0x02)
#define UNIFIED_LOG_GBB_TYPE_PROGRAM_FAIL	(0x03)
#define UNIFIED_LOG_GBB_TYPE_ENTER_RAID	(0x04)
#define UNIFIED_LOG_GBB_TYPE_ENTER_TURBORAIN	(0x05)

#define UNIFIED_LOG_BGS_EOT				(0x01)
#define UNIFIED_LOG_BGS_UNC				(0x7F)

#define UNIFIED_LOG_DEEP_ERROR_RECOVERY	(0x02)

#define UNIFIED_LOG_RDL_UNDEFINED			(0X00)
#define UNIFIED_LOG_RDL_EOT				(0x01)
#define UNIFIED_LOG_RDL_UNC				(0x7F)

//MS Error Code
#if IM_N48R
#define UNIFIED_LOG_MS_FULL_SCAN_INSTANCE_START		(0x01)
#define UNIFIED_LOG_MS_FULL_SCAN_INSTANCE_END		(0x02)
#define UNIFIED_LOG_MS_INSTANCE_START				(0x03)
#define UNIFIED_LOG_MS_INSTANCE_END					(0x04)
#define UNIFIED_LOG_MS_INSTANCE_PROGRESS			(0x05)
#define UNIFIED_LOG_BLOCK_CHECK_FAIL				(0x06)
#define UNIFIED_LOG_FOLD_VHC						(0x07)
#define UNIFIED_LOG_FOLD_NDEP						(0x08)
#define UNIFIED_LOG_FOLD_FORGIVEN					(0x09)
#define UNIFIED_LOG_FOLD_RBER						(0x10)
#endif/* IM_N48R */

//FailCode
#if IM_N48R
#define UNIFIED_LOG_REFCAL_FAILED 								(0x01)
#define UNIFIED_LOG_MEASURE_FAILED								(0X02)
#endif/* IM_N48R */


//check size return result
#define UNIFIED_LOG_SIZE_ENOUGH		(0x00)
#define UNIFIED_LOG_BUFFER_NOT_READY	(0x01)
#define UNIFIED_LOG_BUFFER_IN_READING	(0x02)
#define UNIFIED_LOG_FULL_WHEN_INIT		(0x03)
#define UNIFIED_LOG_LIMIT_FLUSH		(0x04)
#define UNIFIED_LOG_FULL_BEFORE_MERGE	(0x05)
#define UNIFIED_LOG_BMU_POOL_EXHAUSTED	(0x06)
#define UNIFIED_LOG_DURING_LPM		(0x07)

#define UNIFIED_LOG_EXHAUSTED_TYPE_OK	(0x00)
#define UNIFIED_LOG_EXHAUSTED_TYPE_1	(BIT0)
#define UNIFIED_LOG_EXHAUSTED_TYPE_2	(BIT1)
#define UNIFIED_LOG_EXHAUSTED_TYPE_3	(BIT2)

#define UNIFIED_LOG_EXHAUSTED_ADD_DLOG_EN	(TRUE)
#if UNIFIED_LOG_EN
#define UNIFIED_LOG_EXTEND_LCA_SIZE	(0x1021000)//More than 16 MB a little for N48R (4129 * 4k)
#else
#define UNIFIED_LOG_EXTEND_LCA_SIZE	(0)
#endif

#define UNIFIED_LOG_RETURN_HEADER		(TRUE) //true or false depend on customer!  

#define UNIFIED_LOG_PER_LOG_SIZE		(0x20)
#define UNIFIED_LOG_HEADER_SIZE		(0x0C)
#define UNIFIED_LOG_BUFFER_SIZE		(0x1000) //4K
#if ((HOST_MODE == USB) && (FW_CATEGORY_CUSTOMER == CUSTOMER_MICRON_NICKS))
#define UNIFIED_LOG_PAYLOAD_MAX_SIZE	(0x080000) //USB
#else /*((HOST_MODE == USB) && (FW_CATEGORY_CUSTOMER == CUSTOMER_MICRON_NICKS))*/
#define UNIFIED_LOG_PAYLOAD_MAX_SIZE	(UNIFIED_LOG_EXTEND_LCA_SIZE)
#endif /*((HOST_MODE == USB) && (FW_CATEGORY_CUSTOMER == CUSTOMER_MICRON_NICKS))*/
#define UNIFIED_LOG_RESERVE_BYTE_FOR_SPARE  (0x20) //32B
#define UNIFIED_LOG_SPARE_OFFSET				(UNIFIED_LOG_BUFFER_SIZE - UNIFIED_LOG_RESERVE_BYTE_FOR_SPARE)
#define UNIFIED_LOG_SPARE_BUFFER_BYTE_OFFSET		(UNIFIED_LOG_SPARE_OFFSET)
#define UNIFIED_LOG_SPARE_BUFFER_BYTE_SIZE		(0x02) //2B
#define UNIFIED_LOG_SPARE_BUFFER_LOG_CNT_OFFSET	(UNIFIED_LOG_SPARE_BUFFER_BYTE_OFFSET + UNIFIED_LOG_SPARE_BUFFER_BYTE_SIZE)
#define UNIFIED_LOG_SPARE_BUFFER_LOG_CNT_SIZE	(0x02) //2B
#define UNIFIED_LOG_SPARE_BUFFER_LCA_OFFSET		(UNIFIED_LOG_SPARE_BUFFER_LOG_CNT_OFFSET + UNIFIED_LOG_SPARE_BUFFER_LOG_CNT_SIZE)
#define UNIFIED_LOG_SPARE_BUFFER_LCA_SIZE		(0x04) //4B
#define UNIFIED_LOG_SPARE_BUFFER_SIGNATURE_OFFSET	(UNIFIED_LOG_SPARE_BUFFER_LCA_OFFSET + UNIFIED_LOG_SPARE_BUFFER_LCA_SIZE)
#define UNIFIED_LOG_SPARE_BUFFER_SIGNATURE_SIZE	(0x01) //1B

#define UNIFIED_LOG_WRITE_BUFFER_READY			(BIT0)
#define UNIFIED_LOG_WAIT_BUFFER_FLUSH			(BIT1)
#define UNIFIED_LOG_WAIT_ACCLOCATE_WRITE_BUFFER	(BIT2)
#define UNIFIED_LOG_READ_FROM_WRITE_BUFFER		(BIT3)
#define UNIFIED_LOG_WAIT_ENTER_FTLTASK			(BIT4)
#define UNIFIED_LOG_GET_LOG_FILL_BUFFER			(BIT5)
#define UNIFIED_LOG_WAIT_MERGE				(BIT6)
#define UNIFIED_LOG_TIME_TO_FLUSH				(BIT7)


#define UNIFIED_LOG_ROUTINE_SATE	(UNIFIED_LOG_WAIT_ENTER_FTLTASK | UNIFIED_LOG_GET_LOG_FILL_BUFFER | UNIFIED_LOG_WAIT_MERGE | UNIFIED_LOG_TIME_TO_FLUSH)

#define UNIFIED_LOG_POWER_CYCLE_LOG_INTERVAL	(100)
#define UNIFIED_LOG_FLUSH_INTERVAL				(60000) //60*1000 Flush to flash every 60 sec
#define UNIFIED_LOG_DRIVE_LOG_INTERVAL				(60000)


#define UNIFIED_LOG_ADD_MODE_NORMAL		(0x00)
#define UNIFIED_LOG_ADD_MODE_FORCE_FLASH	(0x01)
#define UNIFIED_LOG_FLUSH_MODE_NORMAL		(0x00)
#define UNIFIED_LOG_FLUSH_MODE_FORCE_FLASH	(0x01) //use when buffer full / LPM / standby

#if ((HOST_MODE == USB) && (FW_CATEGORY_CUSTOMER == CUSTOMER_MICRON_NICKS))
#define UNIFIED_LOG_FILL_BUFFER_SIZE_PER_ROUND	(SIZE_8KB)
#else //((HOST_MODE == USB) && (FW_CATEGORY_CUSTOMER == CUSTOMER_MICRON_FREY2))
#define UNIFIED_LOG_FILL_BUFFER_SIZE_PER_ROUND	(FWLB_COPYBUFFER_SIZE_IN_4K * SIZE_4KB)
#if UNIFIED_LOG_RETURN_HEADER
#define UNIFIED_LOG_TOTAL_NEED_TRANSFER_SIZE	(UNIFIED_LOG_PAYLOAD_MAX_SIZE + UNIFIED_LOG_HEADER_SIZE) //header = 12 Byte
#else
#define UNIFIED_LOG_TOTAL_NEED_TRANSFER_SIZE	(UNIFIED_LOG_PAYLOAD_MAX_SIZE)
#endif
#endif //((HOST_MODE == USB) && (FW_CATEGORY_CUSTOMER == CUSTOMER_MICRON_FREY2))

#define UNIFIED_LOG_PROGRAM_FAIL_PAGE_LOG_NUM	(8)

#define UNIFIED_LOG_BUFFER_THRESHOLD	(6)
#define UNIFIED_LOG_INVALID_OFFSET		(0xFFFF)

typedef enum UnifiedLogReadStatusEnum {
	UNIFIED_LOG_READ_INIT,
	UNIFIED_LOG_ALLOCATE_READ_BUFFER,
	UNIFIED_LOG_WAIT_ALLOCATE_READ_BUFFER,
	UNIFIED_LOG_FILL_HEADER,
	UNIFIED_LOG_DECIDE_DATA_SOURCE,
	UNIFIED_LOG_READ_DATA,
	UNIFIED_LOG_WAIT_READ_DATA,
	UNIFIED_LOG_COPY_DATA,
	UNIFIED_LOG_WAIT_VUC_BUFFER,
	UNIFIED_LOG_COPY_RAMAIN_DATA,
	UNIFIED_LOG_READ_FINISH,
#if ((HOST_MODE == USB) && (FW_CATEGORY_CUSTOMER == CUSTOMER_MICRON_NICKS))
	UNIFIED_LOG_BUFFER_REACH_8K,
	UNIFIED_LOG_LCA_REACH_END
#endif /*((HOST_MODE == USB) && (FW_CATEGORY_CUSTOMER == CUSTOMER_MICRON_NICKS))*/
} UnifiedLogReadStatusEnum_t;

typedef enum UnifiedLogMergeInUseLCAStatusEnum {
	UNIFIED_LOG_LOAD_ALIGN_INIT,
	UNIFIED_LOG_ALLOCATE_MERGE_BUFFER,
	UNIFIED_LOG_WAIT_ALLOCATE_MERGE_BUFFER,
	UNIFIED_LOG_LOAD_PREVIOUS_LOG,
	UNIFIED_LOG_WAIT_LOAD_PREVIOUS_LOG,
	UNIFIED_LOG_MANAGE_LOG,
	UNIFIED_LOG_MERGE_FINISH
} UnifiedLogMergeInUseLCAStatusEnum_t;

//For couting event in VTDBUF
typedef enum UnifiedLogEventIdxEnum {
	EVENT_POR = 0,
	EVENT_GBB,
	EVENT_BGS,
	EVENT_RDL,
	EVENT_COUT_NUM
} UnifiedLogEventIdxEnum_t;

typedef enum UnifiedLogEventBlockTypeEnum {
	SLC_FULL_BLOCK = 1,
	MLC_FULL_BLOCK,
	TLC_FULL_BLOCK,
	QLC_FULL_BLOCK,
	SLC_PARTIAL_BLOCK,
	MLC_PARTIAL_BLOCK,
	TLC_PARTIAL_BLOCK,
	QLC_PARTIAL_BLOCK
} UnifiedLogEventBlockTypeEnum_t;


typedef struct UnifiedLogInfo UNIFIED_LOG_INFO_T;
struct UnifiedLogInfo {
	U8 ubCH;
	U8 ubCE;
	U8 ubLUN;
	U16 uwBlock;
	U16 uwPage;
	U16 uwUnit;
	U8 ubBlkType;
	U8 ubPageType;
	U8 ubCodeword;
};

#pragma pack(push)
#pragma pack(1)
typedef struct UnifiedLogHeader UNIFIED_LOG_HEADER_T, *UNIFIED_LOG_HEADER_PTR;
struct UnifiedLogHeader {
	U8 ubHeaderFormatVersion;
	U8 ubDataFormatVersion;
	U16 uwCommandClass;
	U16 uwCommondCode;
	U16 uwStatus;
	U32 ulSizeOfPayload;
};
//lack of temperature and POH
typedef union UnifiedLogPayload UNIFIED_LOG_PAYLOAD_T, *UNIFIED_LOG_PAYLOAD_PTR;
union UnifiedLogPayload {
	struct {
		U16 uwEventID;
		U8 ubEventDefine[25];
		U8 ubTemperature;
		U32 ulPOH;
	} Common;
	struct {
		U16 uwEventID;
		U16 uwAvgPECCount;
		U8 ubReserved[23];
		U8 ubTemperature;
		U32 ulPOH;
	} EventPOR;
	struct {
		U16 uwEventID;
		U8 ubCH;
		U8 ubCE;
		U8 ubLUN;
		U8 ubPageType;
		U16 uwBlock;
		U16 uwPage;
		U8 ubCodeword;
		U8 ubBlockType;
		U8 ubReserved1[2];
		U8 ubDefectType;
		U8 ubReserved2;
		U8 ubEraseProgramPulseCnt;
		/////Only Valid for GBB entering RAIN/////
		U8 ubMin1SWSubStep;
		U8 ubMin2SWSubStep;
		U8 ubMinSWARCSubStep;
		U8 ubReserved3[5];
		U16 uwBlockPEC;
		U8 ubTemperature;
		U32 ulPOH;
	} EventGBB;
	struct {
		U16 uwEventID;
		U8 ubCH;
		U8 ubCE;
		U8 ubLUN;
		U8 ubPageType;
		U16 uwBlock;
		U16 uwPage;
		U8 ubCodeword;
		U8 ubBlockType;
		U16 uwLTU;
		U8 ubRefreshType;
		U8 ubReserved1;
		U16 uwPBLWPage;
		U32 ulNotRequired;
		U8 ubReserved2[3];
		U16 uwBlockPEC;
		U8 ubTemperature;
		U32 ulPOH;
	} EventBGS;
	struct {
		U16 uwEventID;
		U8 ubCH;
		U8 ubCE;
		U8 ubLUN;
		U8 ubPageType;
		U16 uwBlock;
		U16 uwPage;
		U8 ubCodeword;
		U8 ubBlockType;
		U8 ubNotRequired;
		U8 ubReserved1;
		U8 ubRefreshType;
		U8 ubReserved2[10];
		U16 uwBlockPEC;
		U8 ubTemperature;
		U32 ulPOH;
	} EventRDL;
	struct {
		U16 uwEventID;
		U8 ubCH;
		U8 ubCE;
		U8 ubLUN;
		U8 ubPageType;
		U16 uwBlock;
		U16 uwPage;
		U8 ubCodeword;
		U8 ubBlockType;
		U16 uwLTU;
		U8 ubRefreshType;
		U8 ubReserved1;
		U16 uwPBLWPage;
#if (IM_N48R)
		U32 ulBlkVersion;
#else/*(IM_N48R)*/
		U32 ulNotRequired;
#endif /*(IM_N48R)*/
		U8 ubReserved2[3];
		U16 uwBlockPEC;
		U8 ubTemperature;
		U32 ulPOH;
	} EventDeepERR;
	struct {
		U16 uwEventID;
		U8 ubCH;
		U8 ubCE;
		U8 ubLUN;
		U8 ubReserved1;
		U16 uwAvgPECCount;
		U16 uwVRLCAddress;
		U8 ubReserved2;
		U8 ubTrimValue;
		U8 ubReserved3[2];
		U8 ubOffsetSelect;
		U8 ubReserved4[12];
		U8 ubTemperature;
		U32 ulPOH;
	} EventVRLC;
	struct {
		U16 uwEventID;
		U16 uwAvgPECCount;
		U8 ubReserved[23];
		U8 ubTemperature;
		U32 ulPOH;
	} EventTempcoByPEC;
	struct {
		U16 uwEventID;
		U16 uwAvgPECCount;
		U8 ubReserved[23];
		U8 ubTemperature;
		U32 ulPOH;
	} EventBFEAForceCombine;
	struct {
		U16 uwEventID;
		U16 uwAvgPECCount;
		U8 ubReserved[23];
		U8 ubTemperature;
		U32 ulPOH;
	} EventBFEAFreezeAPLFamilyCreation;
	struct {
		U16 uwEventID;
		U16 uwMSErrorCode;
		U16 uwSuperBlk;
		U32 ulBlkVersion;
		U16 uwScanPercent;
		U16 uwPage;
		U8 ubScanGroup;
		U8 ubReserved[3];
		U16 uwBlkPEC;
		U8 ubReserved2[7];
		U8 ubTemperature;
		U32 ulPOH;
	} EventMS;
	struct {
		U16 uwEventID;
		U16 uwMSErrorCode;
		U8 ubCH;
		U8 ubCE;
		U8 ubLUN;
		U8 ubCW;
		U16 uwBlock;
		U16 uwFailingPageNum;
		U16 uwSuperBlk;
		U8 ubReserved[4];
		U16 uwBlkPEC;
		U8 ubReserved2[7];
		U8 ubTemperature;
		U32 ulPOH;
	} EventEBCFail;
	struct {
		U16 uwEventID;
		U16 uwMSErrorCode;
		U8 ubCH;
		U8 ubCE;
		U8 ubLUN;
		U8 ubCodeword;
		U16 uwBlk;
		U16 uwFailingPageNum;
		U16 uwSuperBlk;
		U8 ubReserved;
		U8 ubReadOutOffset;
		U8 ubPageType;
		U8 ubBlkType;
		U16 uwBlkPEC;
		U32 ulBlkVersion;
		U8 ubReserved2[2];
		U8 ubReadWriteTempDelta;
		U8 ubTemperature;
		U32 ulPOH;
	} EventMSFold;
	struct {
		U16 uwEventID;
		U16 uwFirstCenterErrorCnt;
		U16 uwFinalCenterErrorCnt;
		U16 uwFinalLeftErrorCnt;
		U16 uwFinalRightErrorCnt;
		U16 uwDACCenter;
		U16 uwSuperBlk;
		U8 ubOffsetSelect;
		U8 ubReserved2;
		U8 ubNandDetectEmptyPageFailBitCnt;
		U8 ubReserved3[10];
		U8 ubTemperature;
		U32 ulPOH;
	} EventMSFoldStats;
	struct {
		U16 uwEventID;
		U8 ubCH;
		U8 ubCE;
		U8 ubLUN;
		U8 ubFailCode;
		U16 uwNumTrimsFailingToDither;
		U8 ubReserved[19];
		U8 ubTemperature;
		U32 ulPOH;
	} EventRefCalfailed;
	struct {
		U16 uwEventID;
		U8 ubCH;
		U8 ubCE;
		U8 ubLUN;
		U16 uwSuperBlkNum;
		U8 ubBlkType;
		U16 uwPage;
		U16 uwScanOverTimes;
		U16 uwUniformRandomNum;
		U8 ubUniformRandomNum;
		U32 ulReadCnt;
		U8 ubReserved[8];
		U8 ubTemperature;
		U32 ulPOH;
	} EventRDScan;
	struct {
		U16 uwEventID;
		U8 ubCH;
		U8 ubCE;
		U8 ubLUN;
		U8 ubPageType;
		U16 uwBlock;
		U16 uwPage;
		U8 ubCodeword;
		U8 ubBlockType;
		U8 ubReserved1[2];
		U8 ubDefectType;
		U8 ubReserved2[2];
		U8 ubMin1SWSubStep;
		U8 ubMin2SWSubStep;
		U8 ubMinSWARCSubStep;
		U8 ubReserved3[5];
		U16 uwBlockPEC;
		U8 ubTemperature;
		U32 ulPOH;
	} EventTurboRain;
};
#pragma pack(pop)

typedef struct UnifiedLogVariable {
	U32 ulLogBase;
	U32 ulInsertLogIdx;
	U64 uoFlushTime;
	U64 uoReallocateWLBDriveLogTime;
	U16 uwFlushIdx;
	U16 uwTotalByte;
	U16 uwState;
	U16 uwWritePBOffset;
	U16 uwNewPBOffset;
	U16 uwCurrentByteInBuffer;
	U16 uwCurrentEventCountInBuffer;
	U32 ulCurrentLCA;
	U32 ulLCAHead;
	U32 ulLCATail;		//This LCA should not use!
	U32 ulReadLCA;
	U16 uwReadPBAddress;
	U16 uwRead4KCollectSize; //How much data has been collected from this LCA.
	U32 ulVUCBufferCollectSize; //How much data has been collected to VUC temp buffer.
	U32 ulTotalReturntoHostSize; //How much data has been transfered to Host in this VUC get Unified Log command.
	U32 ulTotalCollectSize; //How much data has been collected in this VUC get Unified Log command.
	U32 ulPayloadAddr;
	U32 ulPayloadNeedXferSize;	//How much data need to transfer for this C2 command
	U16 uwTemperature;
	U16 uwMergePBAddress;
	UnifiedLogReadStatusEnum_t ubReadState;
	UnifiedLogMergeInUseLCAStatusEnum_t ubUELMergeInUseLCAState;
	U8 ubNoTemperatureLogCount; ////since Unified Log start logging from 0 regardless power cycle or LPM, so just recored counter.
	U16 uwEventCounter[EVENT_COUT_NUM];
	U8 ubUnifiedEventLogThresholdForVRLC;
	union {
		U8 ubAll;
		struct {
			U8 btVUCRead	: 1;
			U8 btLPM		: 1;
			U8 btStandby	: 1;
			U8 btHMB2SRAM: 1;
			U8	: 4;
		};
	} LimitGenWCQ;
	union {
		U16 uwAll;
		struct {
			U16 btEmpty 			: 1;
			U16 btLCARingBack 		: 1;
			U16 btTemperatureReady 	: 1;
			U16 btAllowForceFlashMode 	: 1;
			U16 btVUCBufferReady	: 1;
			U16 btCopyFromWritePB	: 1;
			U16 btFull			: 1;
			U16 btMergeInUseLCADone	: 1;
			U16 btSignature		: 1;
			U16 btLogNotFlush		: 1;
			U16 btFirstReadCheckDone 	: 1;
			U16 btLimitAddRQ		: 1;
			U16 btTrimDoing 		: 1;
			U16 btWaitAllocatePB		: 1;
			U16 btRDScanEventDebugMode : 1;
			U16 noUse: 1;
		};
	} Flag;
#if ((HOST_MODE == USB) && (FW_CATEGORY_CUSTOMER == CUSTOMER_MICRON_NICKS))
	U8 ubVUCCMDIdx; //USB
#endif /* ((HOST_MODE == USB) && (FW_CATEGORY_CUSTOMER == CUSTOMER_MICRON_NICKS))*/
} UnifiedLogVariable_t;

typedef struct ProgramFailPageLog {
	U16 uwBlockorUnit;
	U16 uwPageLog;
	U8 ubPlaneBank;
	U8 CE			: 3;
	U8 CH			: 2;
	U8 btIsUnit		: 1;
	U8 btValid		: 1;
	U8 : 1;
} UnfiedLogProgramFailPageLog_t;

extern UnifiedLogVariable_t gUnifiedLogVariable;
extern U8 gubEraseProgramPulseCnt[MAX_CE];
extern UnfiedLogProgramFailPageLog_t gProgramFailPageLog[UNIFIED_LOG_PROGRAM_FAIL_PAGE_LOG_NUM];

#define M_UNIFIED_LOG_SET_SPARE_BUFFER_BYTE(ulBaseAddr , x)			(((volatile U16 *)ulBaseAddr)[UNIFIED_LOG_SPARE_BUFFER_BYTE_OFFSET>> 1] = (x))
#define M_UNIFIED_LOG_GET_SPARE_BUFFER_BYTE(ulBaseAddr)			(((volatile U16 *)ulBaseAddr)[UNIFIED_LOG_SPARE_BUFFER_BYTE_OFFSET>> 1])
#define M_UNIFIED_LOG_SET_SPARE_BUFFER_LOG_CNT(ulBaseAddr , x)		(((volatile U16 *)ulBaseAddr)[UNIFIED_LOG_SPARE_BUFFER_LOG_CNT_OFFSET>> 1] = (x))
#define M_UNIFIED_LOG_GET_SPARE_BUFFER_LOG_CNT(ulBaseAddr)		(((volatile U16 *)ulBaseAddr)[UNIFIED_LOG_SPARE_BUFFER_LOG_CNT_OFFSET>> 1])
#define M_UNIFIED_LOG_SET_SPARE_BUFFER_LCA(ulBaseAddr , x)			(((volatile U32 *)ulBaseAddr)[UNIFIED_LOG_SPARE_BUFFER_LCA_OFFSET>> 2] = (x))
#define M_UNIFIED_LOG_GET_SPARE_BUFFER_LCA(ulBaseAddr)			(((volatile U32 *)ulBaseAddr)[UNIFIED_LOG_SPARE_BUFFER_LCA_OFFSET>> 2])
#define M_UNIFIED_LOG_SET_SPARE_BUFFER_SIGNATURE(ulBaseAddr , x)		(((volatile U8 *)ulBaseAddr)[UNIFIED_LOG_SPARE_BUFFER_SIGNATURE_OFFSET] = (x))
#define M_UNIFIED_LOG_GET_SPARE_BUFFER_SIGNATURE(ulBaseAddr)		(((volatile U8 *)ulBaseAddr)[UNIFIED_LOG_SPARE_BUFFER_SIGNATURE_OFFSET])
#define M_UNIFIED_LOG_CHECK_WAIT_RQ_DONE()					((UNIFIED_LOG_WAIT_READ_DATA == gUnifiedLogVariable.ubReadState) || (UNIFIED_LOG_WAIT_LOAD_PREVIOUS_LOG == gUnifiedLogVariable.ubUELMergeInUseLCAState))

#if (UNIFIED_LOG_EN)
INLINE void UnfiedLogUpdateInfo(void)
{
	M_UNIFIED_LOG_SET_SPARE_BUFFER_BYTE(gUnifiedLogVariable.ulLogBase, gUnifiedLogVariable.uwCurrentByteInBuffer);
	M_UNIFIED_LOG_SET_SPARE_BUFFER_LOG_CNT(gUnifiedLogVariable.ulLogBase, gUnifiedLogVariable.uwCurrentEventCountInBuffer);
	M_UNIFIED_LOG_SET_SPARE_BUFFER_LCA(gUnifiedLogVariable.ulLogBase, gUnifiedLogVariable.ulCurrentLCA);
	M_UNIFIED_LOG_SET_SPARE_BUFFER_SIGNATURE(gUnifiedLogVariable.ulLogBase, gUnifiedLogVariable.Flag.btSignature);
}

AOM_VUC_4 void UnifiedLogInit(void);
AOM_VUC_4 void UnifiedLogInitVariable(void);
void UnifiedLogAllocateWritePB_Callback(BMUCmdResult_t *pBmuCmdResult);
AOM_VUC_4 void UnifiedLogCleanLogPB(void);
AOM_VUC_4 void UnifiedLogAllocateWritePB(void);
AOM_VUC_4 void UnifiedLogUpdateInUseLCA(void);
AOM_VUC_4 U8 UnifiedLogCheckReallocateToWLB(void);
AOM_VUC_4 void UnifiedLogFlushCurrentBuffer(U8 ubMode);
AOM_VUC_4  U8 UnifiedLogCheckBufferSizeEnough(void); //Each type of Unified Log has same size(32Byte).
AOM_VUC_4 void UnifiedLogAdd(U8 ubMode); //Each type of Unified Log has same size(32Byte).
AOM_VUC_4 void UnifiedLogRoutine(void);
AOM_VUC_4 void UnifiedLogPOREventAdd(void);
AOM_VUC_4 U16 UnifiedLogGBBGetErrorPageLog(UNIFIED_LOG_INFO_T LogInfo, U8 ubPlaneBank);
void UnifiedLogGBBCollectErrorPageLog(U32 ulPCAorFSA, U8 ubIsPCA, U8 ubPCARule);
#if RELEASED_FW
FW_BTCM0_SECTION U8 UnifiedLogGBBGetEraseProgramPulseCnt(U8 ubChannel, U8 ubCE, U8 ubDefectType);
#else
U8 UnifiedLogGBBGetEraseProgramPulseCnt(U8 ubChannel, U8 ubCE, U8 ubDefectType);
#endif
AOM_VUC_4 void UnifiedLogGBBEventAdd(UNIFIED_LOG_INFO_T newLogInfo, U8 ubSLC_D1, U8 ubDefectType, U8 ubMin1SWSubStep, U8 ubMin2SWSubStep, U8 ubMinSWArcSubStep);
AOM_VUC_4 void UnifiedLogGetInfoFromRetryJob(RetryJob_t *pCurrentRetryJob, UNIFIED_LOG_INFO_T *pNewLogInfo, U8 *pubD1);
AOM_VUC_3 void UnifiedLogBGSEventAdd(RetryJob_t *pCurrentRetryJob, U8 ubDefectType);
AOM_VUC_4 void UnifiedLogRDLEventAdd(RetryJob_t *pCurrentRetryJob, U8 ubDefectType);
AOM_VUC_4 void UnifiedLogRDLOverReadCntEventAdd(UNIFIED_LOG_INFO_T NewLogInfo, U8 ubRefreshType);
AOM_VUC_4 void UnifiedLogDeepErrorEventAdd(RetryJob_t *pCurrentRetryJob, U8 ubDefectType);
AOM_VUC_4 void UnifiedLogVRLCEventAdd(UNIFIED_LOG_INFO_T NewLogInfo, U16 uwVRLCAddress, U8 ubTrimValue, U8 ubOffsetSelect);
AOM_VUC_4 void UnifiedLogTempcoEventAdd(void);
AOM_VUC_3 void UnifiedLogBFEAFreezeAPLFamilyCreationEventAdd(void);
AOM_VUC_3 void UnifiedLogBFEAForceCombineEventAdd(void);
AOM_VUC_4 void UnifiedLogMSEventAdd(UNIFIED_LOG_INFO_T NewLogInfo, U16 uwMSErrorCode, U16 uwScanPercent, U8 ubScanGroup, U16 uwBlkProgramEC);
AOM_VUC_4 void UnifiedLogMSFoldEventAdd(UNIFIED_LOG_INFO_T NewLogInfo, U16 uwMSErrorCode, U8 ubReadOutOffset, U8 ubReadWriteTempDelta, U16 uwBlkPEC, U8 ubCodeword);
AOM_VUC_4 void UnifiedLogMSFoldStatsEventAdd(UNIFIED_LOG_INFO_T NewLogInfo, U16 uwFirstCenterEC, U16 uwFinalCenterEC, U16 uwFinalLeftEC, U16 uwFinalRightEC, U16 uwDACCenter, U8 ubOffSel, U8 ubNDEPFailBitCnt);
AOM_VUC_4 void UnifiedLogRefCalFailedEventAdd(UNIFIED_LOG_INFO_T NewLogInfo, U8 ubFailCode, U16 uwNumTrimsFailingToDither);
AOM_READ_VERIFY void UnifiedLogRDScanEventAdd(UNIFIED_LOG_INFO_T NewLogInfo, U16 uwScanOverTimes, U16 uwURN, U32 ulReadCount);
AOM_VUC_4 void UnifiedLogTurboRainEventAdd(UNIFIED_LOG_INFO_T NewLogInfo, U8 ubSLC, U8 ubDefectType, U8 ubMin1SWSubStep, U8 ubMin2SWSubStep, U8 ubMinSWArcSubStep);
AOM_VUC_4 void UnifiedLogAddDriveLog(U16 uwEventID, UNIFIED_LOG_INFO_T NewLogInfo, U8 ubBlkType, U8 ubDefectType, U8 ubDropReason);
void UnifiedLogAllocateReadPB_Callback(BMUCmdResult_t *pBmuCmdResult);
AOM_VUC_4 void UnifiedLogCopyData(U32 ulSrcAddr, U16 uwSrcRemainSize, U32 ulTargetAddr);
AOM_VUC_4 void UnifiedLogFillHeader(U32 ulAddr, U16 uwStatusCode, U32 ulCollectSize );

#if ((HOST_MODE == USB) && (FW_CATEGORY_CUSTOMER == CUSTOMER_MICRON_NICKS))
AOM_VUC_5 void UnifiedLogFillBuffer(void); //overlay(18)
AOM_VUC_5 void VUCMicronGetUnifiedLog(U32 ulPayloadAddr);  //overlay(18)
AOM_VUC_5 void VUCMicronSetUnifiedLogThreshold(U32 ulPayloadAddr);
AOM_VUC_5 void VUCMicronGetUnifiedLogThreshold(U32 ulPayloadAddr);
#else /*((HOST_MODE == USB) && (FW_CATEGORY_CUSTOMER == CUSTOMER_MICRON_NICKS))*/
AOM_VUC_4 void UnifiedLogFillBuffer(void); //overlay(17)
AOM_VUC_4 void VUCMicronGetUnifiedLog(U32 ulPayloadAddr);  //overlay(17)
#endif /*((HOST_MODE == USB) && (FW_CATEGORY_CUSTOMER == CUSTOMER_MICRON_NICKS))*/

void UnifiedLogAllocateMergePB_Callback(BMUCmdResult_t *pBmuCmdResult);
AOM_VUC_4 void UnifiedLogMergingLogs(U32 ulSrc1Addr, U32 ulSrc2Addr, U32 ulTargetAddr, U16 uwMergeSize, U16 uwMergeOffset);
AOM_VUC_4 void UnifiedLogMergeInUseLCA(void);
#endif /* (UNIFIED_LOG_EN) */
#endif /* SRC_FW_VUC_VUC_MICRONGETUNIFIEDEVENTLOG_H_ */
