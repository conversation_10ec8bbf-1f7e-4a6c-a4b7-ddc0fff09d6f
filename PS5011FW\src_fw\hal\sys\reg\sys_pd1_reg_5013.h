#ifndef _SYS_PD1_REG_5013_H_
#define _SYS_PD1_REG_5013_H_

#include "symbol.h"
#include "typedef.h"
#include "mem.h"

/*
 *  +----------------------------------------------------------------+
 *  |					System PD1 Register                          |
 *  | 				Offset:0x00802000 ~ 0x008031FF                   |
 *  |----------------------------------------------------------------|
 *  | MISCL              | 0x2000 ~ 0x20FF | SYS1_MISCL_CTRL_BASE    |
 *  | PADC      	     | 0x2100 ~ 0x21FF | SYS1_PADC_CTRL_BASE     |
 *  | FPHYC        	     | 0x2200 ~ 0x22FF | SYS1_FPHYC_CTRL_BASE    |
 *  | RNG        	     | 0x2300 ~ 0x23FF | SYS1_RNG_BASE           |
 *  | EFUC       	     | 0x2400 ~ 0x24FF | SYS1_EFUC_BASE          |
 *  | RTT                | 0x2500 ~ 0x25FF | SYS1_RTT_BASE           |
 *  | WDT                | 0x2600 ~ 0x26FF | SYS1_WDT_BASE           |
 *  | GPIO               | 0x2700 ~ 0x27FF | SYS1_GPIO_CTRL_BASE     |
 *  | LED              	 | 0x2800 ~ 0x28FF | SYS1_LED_CTRL_BASE      |
 *  | FDQDLY           	 | 0x2A00 ~ 0x2AFF | SYS1_FDQDLY_CTRL_BASE   |
 *  | MISCH            	 | 0x3000 ~ 0x30FF | SYS1_MISCH_CTRL_BASE    |
 *  | ITC              	 | 0x3100 ~ 0x31FF | SYS1_ITC_CTRL_BASE      |
 *  +----------------------------------------------------------------+
 */


/*
 *  +-----------------------------------------------------------------------+
 *  |					MISCL      								            |
 *  +-----------------------------------------------------------------------+
 */
#define	SYS1_MISCL_OFFSET							(0x00002000) //0x2000~0x20FF
#define	SYS1_MISCL_CTRL_BASE						(SYSTEM_PD0_REG_ADDRESS + SYS1_MISCL_OFFSET)

#define	R8_SYS1_MISCL								((REG8  *) SYS1_MISCL_CTRL_BASE)
#define	R16_SYS1_MISCL								((REG16 *) SYS1_MISCL_CTRL_BASE)
#define	R32_SYS1_MISCL								((REG32 *) SYS1_MISCL_CTRL_BASE)
#define	R64_SYS1_MISCL								((REG64 *) SYS1_MISCL_CTRL_BASE)

#define	R32_SYS1_SYS_JG_UNLOCK						((0x08) >> 2)                                       //Default: 0x00000000
#define		JG_PWOK_CNT_SHIFT						(0)
#define		JG_PWOK_CNT_MASK						(BIT_MASK(4))
#define		JG_PWOK_CNT 							(JG_PWOK_CNT_MASK << JG_PWOK_CNT_SHIFT)
#define		JG_UNLOCK_BIT							(BIT7)
#define		JG_UNLOCK_SHIFT							(7)

#define R16_SYS1_SYS_SIM_STS0						((0x10) >> 1)

#define	R32_SYS1_SYS_JG_UNLOCK_PASSWD				((0x0C) >> 2)                                       //Default: 0x55AA789B

#define	R32_SYS1_SYS_SIM_STS0						((0x10) >> 2)                                       //Default: 0xFFFF0000
#define	R8_SYS1_SYS_SIM_STS0						((0x10) >> 0)
#define	R8_SYS1_CR_SIM_STS0							((R8_SYS1_SYS_SIM_STS0 + 0)
#define	R8_SYS1_CR_SIM_STS1							((R8_SYS1_SYS_SIM_STS0 + 1)
#define	R8_SYS1_CR_SIM_STS2							((R8_SYS1_SYS_SIM_STS0 + 2)
#define	R8_SYS1_CR_SIM_STS3							((R8_SYS1_SYS_SIM_STS0 + 3)

#define	R32_SYS1_SYS_SIM_STS1						((0x14) >> 2)                                       //Default: 0xFFFF0000
#define	R8_SYS1_SYS_SIM_STS1						((0x14) >> 0)
#define	R8_SYS1_CR_SIM_STS4							((R8_SYS1_SYS_SIM_STS1 + 0)
#define	R8_SYS1_CR_SIM_STS5							((R8_SYS1_SYS_SIM_STS1 + 1)
#define	R8_SYS1_CR_SIM_STS6							((R8_SYS1_SYS_SIM_STS1 + 2)
#define	R8_SYS1_CR_SIM_STS7							((R8_SYS1_SYS_SIM_STS1 + 3)

#define	R32_SYS1_SYS_SIM_STS2						((0x18) >> 2)                                       //Default: 0x0000FFFF
#define	R8_SYS1_SYS_SIM_STS2						((0x18) >> 0)
#define	R8_SYS1_CR_SIM_STS7_RO						((R8_SYS1_SYS_SIM_STS2 + 0)
#define	R8_SYS1_CR_SIM_STS6_RO						((R8_SYS1_SYS_SIM_STS2 + 1)
#define	R8_SYS1_CR_SIM_STS5_RO						((R8_SYS1_SYS_SIM_STS2 + 2)
#define	R8_SYS1_CR_SIM_STS4_RO						((R8_SYS1_SYS_SIM_STS2 + 3)

#define	R32_SYS1_SYS_SIM_STS3						((0x1C) >> 2)                                       //Default: 0x0000FFFF
#define	R8_SYS1_SYS_SIM_STS3						((0x1C) >> 0)
#define	R8_SYS1_CR_SIM_STS3_RO						((R8_SYS1_SYS_SIM_STS3 + 0)
#define	R8_SYS1_CR_SIM_STS2_RO						((R8_SYS1_SYS_SIM_STS3 + 1)
#define	R8_SYS1_CR_SIM_STS1_RO						((R8_SYS1_SYS_SIM_STS3 + 2)
#define	R8_SYS1_CR_SIM_STS0_RO						((R8_SYS1_SYS_SIM_STS3 + 3)

#define	R32_SYS1_SYS_DEBUG_STS0						((0x20) >> 2)                                       //Default: 0x00000000

#define	R32_SYS1_SYS_DEBUG_STS1						((0x24) >> 2)                                       //Default: 0x00000000

#define	R32_SYS1_SYS_PCIE_PMU_STATUS				((0x30) >> 2)                                       //Default: 0x00000000
#define		SR_PCIE_PMU_L0_BIT						(BIT0)
#define		SR_PCIE_PMU_L11_BIT						(BIT1)
#define		SR_PCIE_PMU_L12_IDLE_BIT				(BIT2)
#define		SR_PCIE_PMU_L12_ENTRY_BIT				(BIT3)
#define		SR_PCIE_PMU_L12_EXIT_BIT				(BIT4)

#define	R32_SYS1_SYS_PIC_MISC						((0x40) >> 2)
#define		CR_UART_LOAD_MODE_BIT					(BIT0)

#define	R32_SYS1_SYS_PTM_CFG						((0x44) >> 2)
#define		CR_PTM_H_SYNC_CFG_SHIFT					(0)
#define		CR_PTM_H_SYNC_CFG_MASK					(BIT_MASK(3))
#define		CR_PTM_H_SYNC_CFG						(CR_PTM_H_SYNC_CFG_MASK << CR_PTM_H_SYNC_CFG_SHIFT)
#define		CR_PTM_H_SYNC_DEC_CFG_SHIFT				(3)
#define		CR_PTM_H_SYNC_DEC_CFG_MASK				(BIT_MASK(3))
#define		CR_PTM_H_SYNC_DEC_CFG					(CR_PTM_H_SYNC_DEC_CFG_MASK << CR_PTM_H_SYNC_DEC_CFG_SHIFT)

/*
 *  +-----------------------------------------------------------------------+
 *  |					PADC      								            |
 *  +-----------------------------------------------------------------------+
 */
#define SYS1_PADC_OFFSET							(0x00002100) //0x2100~0x21FF
#define SYS1_PADC_CTRL_BASE							(SYSTEM_PD0_REG_ADDRESS + SYS1_PADC_OFFSET)

#define R8_SYS1_PADC								((REG8  *) SYS1_PADC_CTRL_BASE)
#define R16_SYS1_PADC								((REG16 *) SYS1_PADC_CTRL_BASE)
#define R32_SYS1_PADC								((REG32 *) SYS1_PADC_CTRL_BASE)
#define R64_SYS1_PADC								((REG64 *) SYS1_PADC_CTRL_BASE)

#define R32_SYS1_PAD_FLH_CTRL						((0x00) >> 2)                                        //Default: 0x000F0080
#define		CR_FLH_RSEL_BIT							(BIT0)
#define		CR_FLH_EV_BIT							(BIT5)
#define		CR_VREF_REN_BIT							(BIT7)
#define		CR_FLH_GB_SHIFT							(16)
#define		CR_FLH_GB_MASK 							(BIT_MASK(4))
#define		CR_FLH_GB 								(CR_FLH_GB_MASK << CR_FLH_GB_SHIFT)

#define R32_SYS1_PAD_FRDY_CFG						((0x04) >> 2)                                        //Default: 0x00804020
#define		CR_FRDY_PU_SHIFT						(4)
#define		CR_FRDY_PU_MASK							BIT_MASK(2)
#define		CR_FRDY_PU								(CR_FRDY_PU_MASK << CR_FRDY_PU_SHIFT)
#define		CR_FRDY_PD_BIT							(BIT6)
#define		CR_FRDY_DV_SHIFT						(14)
#define		CR_FRDY_DV_MASK							BIT_MASK(2)
#define		CR_FRDY_DV								(CR_FRDY_DV_MASK << CR_FRDY_DV_SHIFT)
#define		CR_FRDY_SR_BIT							(BIT23)
#define 	CR_FRDY_SR_SHIFT						(23)
#define		XFRDY_I_BIT								(BIT25)

#define R32_SYS1_PAD_FWPB_CFG						((0x08) >> 2)                                        //Default: 0x00804041
#define		CR_FWPB_SO_BIT							(BIT0)
#define		CR_FWPB_CO_BIT							(BIT1)
#define		CR_FWPB_PU_SHIFT						(4)
#define		CR_FWPB_PU_MASK							BIT_MASK(2)
#define		CR_FWPB_PU								(CR_FWPB_PU_MASK << CR_FWPB_PU_SHIFT)
#define		CR_FWPB_PD_BIT							(BIT6)
#define		CR_FWPB_DV_SHIFT						(14)
#define		CR_FWPB_DV_MASK							BIT_MASK(2)
#define		CR_FWPB_DV								(CR_FWPB_DV_MASK << CR_FWPB_DV_SHIFT)
#define		CR_FWPB_SR_BIT							(BIT23)
#define 	CR_FWPB_SR_SHIFT						(23)
#define		CR_FWPB_O_BIT							(BIT24)
#define		XFWPB_I_BIT								(BIT25)

#define R32_SYS1_PAD_FCEB_CFG						((0x0C) >> 2)                                        //Default: 0x00804011
#define		CR_FCEB_SO_BIT							(BIT0)
#define		CR_FCEB_CO_BIT							(BIT1)
#define		CR_FCEB_PU_SHIFT						(4)
#define		CR_FCEB_PU_MASK							BIT_MASK(2)
#define		CR_FCEB_PU								(CR_FCEB_PU_MASK << CR_FCEB_PU_SHIFT)
#define		CR_FCEB_PD_BIT							(BIT6)
#define		CR_FCEB_DV_SHIFT						(14)
#define		CR_FCEB_DV_MASK							BIT_MASK(2)
#define		CR_FCEB_DV								(CR_FCEB_DV_MASK << CR_FCEB_DV_SHIFT)
#define		CR_FCEB_SR_BIT							(BIT23)
#define 	CR_FCEB_SR_SHIFT						(23)

#define R32_SYS1_PAD_FCEB_OE						((0x10) >> 2)                                        //Default: 0x00000000
#define		CR_FCEB_OE_SHIFT						(0)
#define		CR_FCEB_OE_MASK							BIT_MASK(16)
#define		CR_FCEB_OE								(CR_FCEB_OE_MASK << CR_FCEB_OE_SHIFT)

#define R32_SYS1_PAD_XFCEB_I						((0x18) >> 2)                                        //Default: 0x00000000
#define		XFCEB_I_SHIFT							(0)
#define		XFCEB_I_MASK							BIT_MASK(16)
#define		XFCEB_I									(XFCEB_I_MASK << XFCEB_I_SHIFT)

#define R32_SYS1_PAD_XFDAT_I_0						((0x20) >> 2)

/*
 *  +-----------------------------------------------------------------------+
 *  |					FPHYC      								            |
 *  +-----------------------------------------------------------------------+
 */
#define	SYS1_FPHYC_OFFSET							(0x00002200) //0x2200~0x22FF
#define	SYS1_FPHYC_BASE								(SYSTEM_PD0_REG_ADDRESS + SYS1_FPHYC_OFFSET)

#define	R8_SYS1_FPHYC								((REG8   *) SYS1_FPHYC_BASE)
#define	R16_SYS1_FPHYC								((REG16  *) SYS1_FPHYC_BASE)
#define	R32_SYS1_FPHYC								((REG32  *) SYS1_FPHYC_BASE)

#define	R32_SYS1_PAD_FCLE_CFG0						(0x00 >> 2)
#define	R32_SYS1_PAD_FCLE_CFG1						(0x04 >> 2)
#define	R32_SYS1_PAD_FCLE_CFG2						(0x08 >> 2)
#define	R32_SYS1_PAD_FCLE_CFG3						(0x0C >> 2)
#define		CR_FCLE_SO_BIT							(BIT0)
#define		CR_FCLE_CO_BIT							(BIT1)
#define		CR_FCLE_PU_SHIFT						(4)
#define		CR_FCLE_PU_MASK							(BIT_MASK(2))
#define		CR_FCLE_PD_BIT							(BIT6)
#define		CR_FCLE_DV_SHIFT						(12)
#define		CR_FCLE_DV_MASK							(BIT_MASK(2))
#define		CR_FCLE_SR_BIT							(BIT20)
#define 	CR_FCLE_SR_SHIFT						(20)
#define		FCLE_I_BIT								(BIT25)

#define	R32_SYS1_PAD_FALE_CFG0						(0x10 >> 2)
#define	R32_SYS1_PAD_FALE_CFG1						(0x14 >> 2)
#define	R32_SYS1_PAD_FALE_CFG2						(0x18 >> 2)
#define	R32_SYS1_PAD_FALE_CFG3						(0x1C >> 2)
#define		CR_FALE_SO_BIT							(BIT0)
#define		CR_FALE_CO_BIT							(BIT1)
#define		CR_FALE_PU_SHIFT						(4)
#define		CR_FALE_PU_MASK							(BIT_MASK(2))
#define		CR_FALE_PD_BIT							(BIT6)
#define		CR_FALE_DV_SHIFT						(12)
#define		CR_FALE_DV_MASK							(BIT_MASK(2))
#define		CR_FALE_SR_BIT							(BIT20)
#define 	CR_FALE_SR_SHIFT						(20)
#define		FALE_I_BIT								(BIT25)

#define	R32_SYS1_PAD_FWEB_CFG0						(0x20 >> 2)
#define	R32_SYS1_PAD_FWEB_CFG1						(0x24 >> 2)
#define	R32_SYS1_PAD_FWEB_CFG2						(0x28 >> 2)
#define	R32_SYS1_PAD_FWEB_CFG3						(0x2C >> 2)
#define		CR_FWEB_SO_BIT							(BIT0)
#define		CR_FWEB_CO_BIT							(BIT1)
#define		CR_FWEB_PU_SHIFT						(4)
#define		CR_FWEB_PU_MASK							(BIT_MASK(2))
#define		CR_FWEB_PD_BIT							(BIT6)
#define		CR_FWEB_DT_SHIFT						(8)
#define		CR_FWEB_DT_MASK							(BIT_MASK(4))
#define		CR_FWEB_DV_SHIFT						(12)
#define		CR_FWEB_DV_MASK							(BIT_MASK(3))
#define		CR_FWEB_SR_SHIFT						(20)
#define		CR_FWEB_SR_MASK							(BIT_MASK(4))
#define		FWEB_I_BIT								(BIT25)
#define		CR_FWEB_ODT_EN_BIT						(BIT27)
#define		CR_FWEB_ODT_SHIFT						(28)
#define		CR_FWEB_ODT_MASK						(BIT_MASK(2))

#define	R32_SYS1_PAD_FREB_CFG0						(0x30 >> 2)
#define	R32_SYS1_PAD_FREB_CFG1						(0x34 >> 2)
#define	R32_SYS1_PAD_FREB_CFG2						(0x38 >> 2)
#define	R32_SYS1_PAD_FREB_CFG3						(0x3C >> 2)
#define		CR_FREB_SO_BIT							(BIT0)
#define		CR_FREB_CO_BIT							(BIT1)
#define		CR_FREB_PU_BIT							(BIT4)
#define		CR_FREB_PD_BIT							(BIT6)
#define		CR_FREB_DT_SHIFT						(8)
#define		CR_FREB_DT_MASK							(BIT_MASK(4))
#define		CR_FREB_DV_SHIFT						(12)
#define		CR_FREB_DV_MASK							(BIT_MASK(3))
#define		CR_FREB_SRB_SHIFT						(16)
#define		CR_FREB_SRB_MASK						(BIT_MASK(4))
#define		CR_FREB_SR_SHIFT						(20)
#define		CR_FREB_SR_MASK							(BIT_MASK(4))
#define		FREB_I_BIT								(BIT25)
#define		CR_FREB_ODT_EN_BIT						(BIT27)
#define		CR_FREB_ODT_SHIFT						(28)
#define		CR_FREB_ODT_MASK						(BIT_MASK(2))

#define	R32_SYS1_PAD_FDQS_CFG0						(0x40>> 2)
#define	R32_SYS1_PAD_FDQS_CFG1						(0x44>> 2)
#define	R32_SYS1_PAD_FDQS_CFG2						(0x48>> 2)
#define	R32_SYS1_PAD_FDQS_CFG3						(0x4C>> 2)
#define		CR_FDQS_SO_BIT							(BIT0)
#define		CR_FDQS_CO_BIT							(BIT1)
#define		CR_FDQS_PU_BIT							(BIT4)
#define		CR_FDQS_PD_BIT							(BIT6)
#define		CR_FDQS_DT_SHIFT						(8)
#define		CR_FDQS_DT_MASK							(BIT_MASK(4))
#define		CR_FDQS_DV_SHIFT						(12)
#define		CR_FDQS_DV_MASK							(BIT_MASK(3))
#define		CR_FDQS_SRB_SHIFT						(16)
#define		CR_FDQS_SRB_MASK						(BIT_MASK(4))
#define		CR_FDQS_SR_SHIFT						(20)
#define		CR_FDQS_SR_MASK							(BIT_MASK(4))
#define		FDQSB_I_BIT								(BIT24)
#define		FDQS_I_BIT								(BIT25)
#define		CR_FDQS_ODT_EN_BIT						(BIT27)
#define		CR_FDQS_ODT_SHIFT						(28)
#define		CR_FDQS_ODT_MASK						(BIT_MASK(2))

#define	R32_SYS1_PAD_FDAT_CFG0						(0x50 >> 2)
#define	R32_SYS1_PAD_FDAT_CFG1						(0x54 >> 2)
#define	R32_SYS1_PAD_FDAT_CFG2						(0x58 >> 2)
#define	R32_SYS1_PAD_FDAT_CFG3						(0x5C >> 2)
#define		CR_FDAT_SO_BIT							(BIT0)
#define		CR_FDAT_CO_BIT							(BIT1)
#define		CR_FDAT_PU_SHIFT						(4)
#define		CR_FDAT_PU_MASK							(BIT_MASK(2))
#define		CR_FDAT_PD_BIT							(BIT6)
#define		CR_FDAT_DT_SHIFT						(8)
#define		CR_FDAT_DT_MASK							(BIT_MASK(4))
#define		CR_FDAT_ODT_EN_BIT						(BIT27)
#define		CR_FDAT_ODT_SHIFT						(28)
#define		CR_FDAT_ODT_MASK						(BIT_MASK(2))

#define	R32_SYS1_PAD_FDATA_DVSR_L_0					(0x60 >> 2)
#define	R32_SYS1_PAD_FDATA_DVSR_L_1					(0x68 >> 2)
#define	R32_SYS1_PAD_FDATA_DVSR_L_2					(0x70 >> 2)
#define	R32_SYS1_PAD_FDATA_DVSR_L_3					(0x78 >> 2)
#define	R8_SYS1_PAD_FDATA_DVSR_L_0					(0x60)
#define	R8_SYS1_PAD_FDATA_DVSR_L_1					(0x68)
#define	R8_SYS1_PAD_FDATA_DVSR_L_2					(0x70)
#define	R8_SYS1_PAD_FDATA_DVSR_L_3					(0x78)
#define		CR_FDAT_SR_3_0_SHIFT					(0)
#define		CR_FDAT_SR_3_0_MASK						(BIT_MASK(4))
#define		CR_FDAT_DV_2_0_SHIFT					(4)
#define		CR_FDAT_DV_2_0_MASK						(BIT_MASK(3))
#define		CR_FDAT_SR_7_4_SHIFT					(8)
#define		CR_FDAT_SR_7_4_MASK						(BIT_MASK(4))
#define		CR_FDAT_DV_5_3_SHIFT					(12)
#define		CR_FDAT_DV_5_3_MASK						(BIT_MASK(3))
#define		CR_FDAT_SR_11_8_SHIFT					(16)
#define		CR_FDAT_SR_11_8_MASK					(BIT_MASK(4))
#define		CR_FDAT_DV_8_6_SHIFT					(20)
#define		CR_FDAT_DV_8_6_MASK						(BIT_MASK(3))
#define		CR_FDAT_SR_15_12_SHIFT					(24)
#define		CR_FDAT_SR_15_12_MASK					(BIT_MASK(4))
#define		CR_FDAT_DV_11_9_SHIFT					(28)
#define		CR_FDAT_DV_11_9_MASK					(BIT_MASK(3))

#define	R32_SYS1_PAD_FDATA_DVSR_H_0					(0x64 >> 2)
#define	R32_SYS1_PAD_FDATA_DVSR_H_1					(0x6C >> 2)
#define	R32_SYS1_PAD_FDATA_DVSR_H_2					(0x74 >> 2)
#define	R32_SYS1_PAD_FDATA_DVSR_H_3					(0x7C >> 2)
#define		CR_FDAT_SR_19_16_SHIFT					(0)
#define		CR_FDAT_SR_19_16_MASK					(BIT_MASK(4))
#define		CR_FDAT_DV_14_12_SHIFT					(4)
#define		CR_FDAT_DV_14_12_MASK					(BIT_MASK(3))
#define		CR_FDAT_SR_23_20_SHIFT					(8)
#define		CR_FDAT_SR_23_20_MASK					(BIT_MASK(4))
#define		CR_FDAT_DV_17_15_SHIFT					(12)
#define		CR_FDAT_DV_17_15_MASK					(BIT_MASK(3))
#define		CR_FDAT_SR_27_24_SHIFT					(16)
#define		CR_FDAT_SR_27_24_MASK					(BIT_MASK(4))
#define		CR_FDAT_DV_20_18_SHIFT					(20)
#define		CR_FDAT_DV_20_18_MASK					(BIT_MASK(3))
#define		CR_FDAT_SR_31_28_SHIFT					(24)
#define		CR_FDAT_SR_31_28_MASK					(BIT_MASK(4))
#define		CR_FDAT_DV_23_21_SHIFT					(28)
#define		CR_FDAT_DV_23_21_MASK					(BIT_MASK(3))

#define	R8_SYS1_PAD_FWEB							(0x80)
#define	R8_SYS1_PAD_FREB							(0x81)
#define	R8_SYS1_PAD_FDQS_TXRX_DLY					(0x82)
#define	R8_SYS1_PAD_RX_DLY							(0x83)
#define		CR_FWEB_RX_DLY_SHIFT					(0)
#define		CR_FWEB_RX_DLY_MASK						(BIT_MASK(4))
#define		CR_FWEB_TX_DLY_SHIFT					(4)
#define		CR_FWEB_TX_DLY_MASK						(BIT_MASK(4))
#define		CR_FREB_RX_DLY_SHIFT					(0)
#define		CR_FREB_RX_DLY_MASK						(BIT_MASK(4))
#define		CR_FREB_TX_DLY_SHIFT					(4)
#define		CR_FREB_TX_DLY_MASK						(BIT_MASK(4))
#define		CR_FDQS_RX_DLY_SHIFT					(0)
#define		CR_FDQS_RX_DLY_MASK						(BIT_MASK(4))
#define		CR_FDQS_TX_DLY_SHIFT					(4)
#define		CR_FDQS_TX_DLY_MASK						(BIT_MASK(4))

/*
 *  +-----------------------------------------------------------------------+
 *  |					RNG      								            |
 *  +-----------------------------------------------------------------------+
 */
#define	SYS1_RNG_OFFSET								(0x00002300) //0x2300~0x23FF
#define	SYS1_RNG_BASE								(SYSTEM_PD0_REG_ADDRESS + SYS1_RNG_OFFSET)

#define	R8_SYS1_RNG									((REG8  *) SYS1_RNG_BASE)
#define	R16_SYS1_RNG								((REG16 *) SYS1_RNG_BASE)
#define	R32_SYS1_RNG								((REG32 *) SYS1_RNG_BASE)

#define	R32_SYS1_RNG_CTRL							(0x00 >> 2)
#define		CR_RNG_MD_SHIFT							(0)
#define		CR_RNG_MD_MASK							(BIT_MASK(2))
#define		CR_RNG_EN_BIT							(BIT6)
#define		CR_RUN_EN_BIT							(BIT7)
#define		CR_RNG_STB_PER_SHIFT					(8)
#define		CR_RNG_STB_PER_MASK						(BIT_MASK(6))
#define		CR_RNG_CFG_SHIFT						(16)
#define		CR_RNG_CFG_MASK							(BIT_MASK(4))

#define	R32_SYS1_RNG_REG							(0x08 >> 2)

#define	R32_SYS1_RNG_DATA_RDY						(0x10 >> 2)
#define		NEW_DATA_RDY_BIT						(BIT0)

/*
 *  +-----------------------------------------------------------------------+
 *  |					EFUC      								            |
 *  +-----------------------------------------------------------------------+
 */
#define	SYS1_EFUC_OFFSET							(0x00002400) //0x2400~0x24FF
#define	SYS1_EFUC_BASE								(SYSTEM_PD0_REG_ADDRESS + SYS1_EFUC_OFFSET)

#define	R8_SYS1_EFUC								((REG8  *) SYS1_EFUC_BASE)
#define	R16_SYS1_EFUC								((REG16 *) SYS1_EFUC_BASE)
#define	R32_SYS1_EFUC								((REG32 *) SYS1_EFUC_BASE)

#define	R32_SYS1_EFU_MODE_SEL						(0x00 >> 2)
#define	R8_SYS1_EFU_MODE_SEL						(0x00)
#define		MODE_SEL_SHIFT							(0)
#define		MODE_SEL_MASK							(BIT_MASK(3))
#define 	PROGRAM_MODE							(0x2)
#define		READ_MODE								(0x3)
#define		BANK_SEL_SHIFT							(4)
#define		BANK_SEL_MASK							(BIT_MASK(4))
#define		MR_BIT									(BIT8)
#define		NO_DOUBLE_BIT							(BIT12)

#define	R32_SYS1_EFU_RDY							(0x04 >> 2)
#define		READ_READY_BIT							(BIT0)
#define		PROGRAM_READY_BIT						(BIT8)
#define		STANDBY_BIT								(BIT9)
#define		POWER_DOWN_BIT							(BIT10)

#define	R32_SYS1_EFU_ADDR							(0x08 >> 2)
#define		EFUSE_ADR_SHIFT							(0)
#define		EFUSE_ADR_MASK							(BIT_MASK(7))

#define	R32_SYS1_EFU_PROG_DATA						(0x0C >> 2)
#define	R32_PROGRAM_WORD							(0x0C >> 2)

#define	R32_SYS1_EFU_DATA_TRIGGER					(0x10 >> 2)
#define		TRIGGER_BIT								(BIT0)

#define	R32_SYS1_EFU_STAT_PROTECT					(0x14 >> 2)
#define		EFUSE_PROGRAM_PROTECT_BIT				(BIT8)
#define		STATE_SHIFT								(24)
#define		STATE_MASK								(BIT_MASK(4))

#define	R32_SYS1_EFU_STRB_CTRL						(0x18 >> 2)
#define		PRGM_STRB_WIDTH_SHIFT					(0)
#define		PRGM_STRB_WIDTH_MASK					(BIT_MASK(10))
#define		READ_STRB_WIDTH_SHIFT					(12)
#define		READ_STRB_WIDTH_MASK					(BIT_MASK(3))
#define		PRGM_STRB_INTVL_SHIFT					(16)
#define		PRGM_STRB_INTVL_MASK					(BIT_MASK(8))
#define		READ_STRB_INTVL_SHIFT					(24)
#define		READ_STRB_INTVL_MASK					(BIT_MASK(2))

#define	R32_SYS1_EFU_SW_CTRL						(0x1C >> 2)
#define		EFUC_A_SHIFT							(0)
#define		EFUC_A_MASK								(BIT_MASK(12))
#define		EFUC_RWL_BIT							(BIT14)
#define		EFUC_RSB_BIT							(BIT15)
#define		EFUC_ENPOR_BIT							(BIT16)
#define		EFUC_PD_BIT								(BIT17)
#define		EFUC_PS_BIT								(BIT18)
#define		EFUC_STROBE_BIT							(BIT19)
#define		EFUC_PGENB_BIT							(BIT20)
#define		EFUC_LOAD_BIT							(BIT21)
#define		EFUC_CSB_BIT							(BIT22)
#define		EFUC_MR_BIT								(BIT23)
#define		EFUC_RF_SHIFT							(24)
#define		EFUC_RF_MASK							(BIT_MASK(8))

#define	R32_SYS1_EFU_SW_DATA						(0x20 >> 2)

#define	R32_SYS1_EFU_STS							(0x24 >> 2)
#define		EFU_STS_BIT								(BIT0)

#define	R32_SYS1_EFU_REG0							(0x40 >> 2)
#define	R32_READ_REG0								(0x40 >> 2)

#define	R32_SYS1_EFU_REG1							(0x44 >> 2)
#define	R32_READ_REG1								(0x44 >> 2)

#define	R32_SYS1_EFU_REG2							(0x48 >> 2)
#define	R32_READ_REG2								(0x48 >> 2)

#define	R32_SYS1_EFU_REG3							(0x4C >> 2)
#define	R32_READ_REG3								(0x4C >> 2)

#define	R32_SYS1_EFU_REG4							(0x50 >> 2)
#define	R32_READ_REG4								(0x50 >> 2)

#define	R32_SYS1_EFU_REG5							(0x54 >> 2)
#define	R32_READ_REG5								(0x54 >> 2)

#define	R32_SYS1_EFU_REG6							(0x58 >> 2)
#define	R32_READ_REG6								(0x58 >> 2)

#define	R32_SYS1_EFU_REG7							(0x5C >> 2)
#define	R32_READ_REG7								(0x5C >> 2)

/*
 *  +-----------------------------------------------------------------------+
 *  |					RTT      								            |
 *  +-----------------------------------------------------------------------+
 */
#define	SYS1_RTT_OFFSET								(0x00002500)  //0x2500~0x25FF
#define	SYS1_RTT_CTRL_BASE							(SYSTEM_PD0_REG_ADDRESS + SYS1_RTT_OFFSET)

#define	R8_SYS1_RTT									((REG8  *) SYS1_RTT_CTRL_BASE)
#define	R16_SYS1_RTT								((REG16 *) SYS1_RTT_CTRL_BASE)
#define	R32_SYS1_RTT								((REG32 *) SYS1_RTT_CTRL_BASE)
#define	R64_SYS1_RTT								((REG64 *) SYS1_RTT_CTRL_BASE)

#define	R32_SYS1_RTT_CTRL							((0x00) >> 2)                                         //Default: 0x00000000
#define	R8_SYS1_RTT1_CTRL							((0x00) >> 0)
#define R8_SYS1_RTT2_CTRL							((0x01) >> 0)
#define		CR_RT1_SK_SHIFT							(0)
#define		CR_RT1_SK_MASK							BIT_MASK(2)
#define		CR_RT1_SK								(CR_RT1_SK_MASK << CR_RT1_SK_SHIFT)
#define		CR_RT1_CLR_P_BIT						(BIT4)
#define		CR_RT1_MD_BIT							(BIT6)
#define		CR_RT1_EN_BIT							(BIT7)
#define			SET_PD1_RTT_SCALE_SHIFT				(0)
#define		CR_RT2_SK_SHIFT							(8)
#define		CR_RT2_SK_MASK							(BIT_MASK(2))
#define		CR_RT2_SK								(CR_RT2_SK_MASK << CR_RT2_SK_SHIFT)
#define		CR_RT2_CLR_P							(BIT12)
#define		CR_RTT2_COMB_BIT						(BIT13)
#define		CR_RT2_MD								(BIT14)
#define		CR_RT2_EN								(BIT15)

#define	R32_SYS1_COMB_CTRL							((0x04) >> 2)                                        //Default: 0x00000000
#define	R8_SYS1_COMB_CTRL							((0x04) >> 0)
#define		CR_COMB_SK_SHIFT						(0)
#define		CR_COMB_SK_MASK							BIT_MASK(2)
#define		CR_COMB_SK								(CR_COMB_SK_MASK << CR_COMB_SK_SHIFT)
#define		CR_RTT_COMB_BIT							(BIT5)												//Read Only Reg.
#define		CR_COMB_MD_BIT							(BIT6)
#define		CR_COMB_EN_BIT							(BIT7)

#define	R32_SYS1_RT1_TO_LMT							((0x10) >> 2)                                         //Default: 0x00000000

#define	R32_SYS1_RT2_TO_LMT							((0x14) >> 2)                                         //Default: 0x00000000

#define	R32_SYS1_RT1_CNT							((0x20) >> 2)                                         //Default: 0x00000000

#define	R32_SYS1_RT2_CNT							((0x24) >> 2)                                         //Default: 0x00000000

/*
 *  +-----------------------------------------------------------------------+
 *  |					WDT      								            |
 *  +-----------------------------------------------------------------------+
 */
#define	SYS1_WDT_OFFSET								(0x00002600) //0x2600~0x26FF
#define	SYS1_WDT_BASE								(SYSTEM_PD0_REG_ADDRESS + SYS1_WDT_OFFSET)

#define	R8_SYS1_WDT									((REG8  *) SYS1_WDT_BASE)
#define	R16_SYS1_WDT								((REG16 *) SYS1_WDT_BASE)
#define	R32_SYS1_WDT								((REG32 *) SYS1_WDT_BASE)

#define	R32_SYS1_WDT_CTRL							(0x00 >> 2)
#define		CR_WDT_TO_RST_EN_SHIFT					(0)
#define		CR_WDT_TO_RST_EN_MASK					(BIT_MASK(5))
#define		EN_WHOLE_CHIP_RESET_BIT					(BIT0)
#define		EN_HOST_RESET_BIT						(BIT1)
#define		EN_CPU_RESET_BIT						(BIT2)
#define		EN_MISC_RESET_BIT						(BIT3)	//I2CM, UART, SMBUS, SPI, SEC, FLH, DBUF, BMU, AXI, COP0, COP1, DMAC
#define		CR_WDT_EN_BIT							(BIT7)
#define		WDT_TO_F_BIT							(BIT14)
#define		CR_WDT_STS_SHIFT						(16)
#define		CR_WDT_STS_MASK							(BIT_MASK(8))

#define	R32_SYS1_WDT_TO_LMT							(0x10 >> 2)

#define	R32_SYS1_WDT_CNT							(0x20 >> 2)

/*
 *  +-----------------------------------------------------------------------+
 *  |					GPIO      								            |
 *  +-----------------------------------------------------------------------+
 */
#define	SYS1_GPIO_OFFSET							(0x00002700) //0x2700~0x27FF
#define	SYS1_GPIO_BASE								(SYSTEM_PD0_REG_ADDRESS + SYS1_GPIO_OFFSET)

#define	R8_SYS1_GPIO								((REG8  *) SYS1_GPIO_BASE)
#define	R16_SYS1_GPIO								((REG16 *) SYS1_GPIO_BASE)
#define	R32_SYS1_GPIO								((REG32 *) SYS1_GPIO_BASE)

#define	R32_SYS1_SYS_EDGE_DET_EN					(0x00 >> 2)
#define		CR_GPIO_DET_EN_SHIFT					(0)
#define		CR_GPIO_DET_EN_MASK						(BIT_MASK(7))
#define		CR_VDT_DET_EN_SHIFT						(8)
#define		CR_VDT_DET_EN_MASK						(BIT_MASK(12))
#define		CR_VDT_DET_XVDT_FIO12_PG_BIT			(BIT8)
#define		CR_VDT_DET_XVDT_FIO18_PG_BIT			(BIT9)
#define		CR_VDT_DET_XVDT_CORE_PG_BIT				(BIT10)
#define		CR_VDT_DET_XVDT_PD1_PG_BIT				(BIT11)
#define		CR_VDT_DET_XVDT_PD51_PG_BIT				(BIT12)
#define		CR_VDT_DET_XVDT_FLH_PG_BIT				(BIT13)
#define		CR_VDT_DET_XVDT_CPHY_PG_BIT				(BIT14)
#define		CR_VDT_DET_XVDT_SATA_PG_BIT				(BIT15)
#define		CR_VDT_DET_XVDT_V12_PG_BIT				(BIT16)
#define		CR_VDT_DET_XVDT_GPIO12_PG_BIT			(BIT17)
#define		CR_VDT_DET_XVDT_GPIO18_PG_BIT			(BIT18)
#define		CR_VDT_DET_XVDT_PD0_PG_BIT				(BIT19)
#define		CR_TS_DET_MASK_SHIFT					(20)
#define		CR_TS_DET_MASK_MASK						(BIT_MASK(4))
#define		CR_TS_DET_EN_SHIFT						(24)
#define		CR_TS_DET_EN_MASK						(BIT_MASK(2))
#define		CR_MPHYRSTN_DET_EN_BIT					(BIT26)
#define		CR_PERSTN_DET_EN_BIT					(BIT27)
#define		CR_CLKREGB_DET_EN_BIT					(BIT28)
#define		CR_DEVSLP_DET_EN_BIT					(BIT29)
#define		CR_PLL_LOCK_DET_EN_SHIFT				(30)
#define		CR_PLL_LOCK_DET_EN_MASK					(BIT_MASK(2))
#define M_EN_GPIO_DETECT_SELECTx(x)					(R32_SYS1_GPIO[R32_SYS1_SYS_EDGE_DET_EN] |= (BIT0 << x))

#define	R32_SYS1_SYS_GPIO_DET_SEL0					(0x04 >> 2)
#define		CR_GPIO_DET_SEL_0_SHIFT					(0)
#define		CR_GPIO_DET_SEL_0_MASK					(BIT_MASK(5))
#define		CR_GPIO_DET_SEL_1_SHIFT					(8)
#define		CR_GPIO_DET_SEL_1_MASK					(BIT_MASK(5))
#define		CR_GPIO_DET_SEL_2_SHIFT					(16)
#define		CR_GPIO_DET_SEL_2_MASK					(BIT_MASK(5))
#define		CR_GPIO_DET_SEL_3_SHIFT					(24)
#define		CR_GPIO_DET_SEL_3_MASK					(BIT_MASK(5))

#define	R32_SYS1_SYS_GPIO_DET_SEL1					(0x08 >> 2)
#define		CR_GPIO_DET_SEL_4_SHIFT					(0)
#define		CR_GPIO_DET_SEL_4_MASK					(BIT_MASK(5))
#define		CR_GPIO_DET_SEL_5_SHIFT					(8)
#define		CR_GPIO_DET_SEL_5_MASK					(BIT_MASK(5))
#define		CR_GPIO_DET_SEL_6_SHIFT					(16)
#define		CR_GPIO_DET_SEL_6_MASK					(BIT_MASK(5))

#define	R32_SYS1_SYS_CNT_VAL0						(0x0C >> 2)
#define	R8_SYS1_CNT_VAL_0							(0x0C >> 0)
#define	R8_SYS1_CNT_VAL_1							(0x0D >> 0)
#define	R8_SYS1_CNT_VAL_2							(0x0E >> 0)
#define	R8_SYS1_CNT_VAL_3							(0x0F >> 0)

#define	R32_SYS1_SYS_CNT_VAL1						(0x10 >> 2)
#define	R8_SYS1_CNT_VAL_4							(0x10 >> 0)
#define	R8_SYS1_CNT_VAL_5							(0x11 >> 0)
#define	R8_SYS1_CNT_VAL_6							(0x12 >> 0)
#define	R8_SYS1_CNT_VAL_7							(0x13 >> 0)

/*
 *  +-----------------------------------------------------------------------+
 *  |					LED      								            |
 *  +-----------------------------------------------------------------------+
 */
#define	SYS1_LED_OFFSET								(0x00002800) //0x2800~0x28FF
#define	SYS1_LED_BASE								(SYSTEM_PD0_REG_ADDRESS + SYS1_LED_OFFSET)

#define	R8_SYS1_LED									((REG8  *) SYS1_LED_BASE)
#define	R16_SYS1_LED								((REG16 *) SYS1_LED_BASE)
#define	R32_SYS1_LED								((REG32 *) SYS1_LED_BASE)

#define	R32_SYS1_SYS_LED_CFG						(0x00 >> 2)
#define		MODE_SEL_SHIFT							(0)
#define		MODE_SEL_MASK							(BIT_MASK(3))
#define		ST_16_32_SEL_BIT						(BIT3)
#define		LED_WAVE_MODE_SHIFT						(8)
#define		LED_WAVE_MODE_MASK						(BIT_MASK(4))
#define		LED_TRIG_BIT							(BIT16)
#define		LED_TRIG_SHIFT							(16)
#define		FAST_SIM_BIT							(BIT20)
#define		OFF2ON_DEFINE_SHIFT						(22)
#define		OFF2ON_DEFINE_MASK						(BIT_MASK(2))
#define		LOW_LT_DEFINE_SHIFT						(24)
#define		LOW_LT_DEFINE_MASK						(BIT_MASK(4))
#define		HIGH_LT_DEFINE_SHIFT					(28)
#define		HIGH_LT_DEFINE_MASK						(BIT_MASK(4))

#define	R32_SYS1_SYS_LED_ACC_DEF					(0x04 >> 2)
#define		ACC_DEFINE_SHIFT						(0)
#define		ACC_DEFINE_MASK							(BIT_MASK(4))

/*
 *  +-----------------------------------------------------------------------+
 *  |					FDQDLY      								        |
 *  +-----------------------------------------------------------------------+
 */
#define	SYS1_FDQDLY_OFFSET							(0x00002A00) //0x2A00~0x2AFF
#define	SYS1_FDQDLY_BASE							(SYSTEM_PD0_REG_ADDRESS + SYS1_FDQDLY_OFFSET)

#define 	SYS1_FDQDLY_RX_MASK						(0x0F)
#define 	SYS1_FDQDLY_TX_MASK						(0xF0)
#define	R8_SYS1_FDQDLY								((REG8  *) SYS1_FDQDLY_BASE)
#define	R16_SYS1_FDQDLY								((REG16 *) SYS1_FDQDLY_BASE)
#define	R32_SYS1_FDQDLY								((REG32 *) SYS1_FDQDLY_BASE)

#define	R32_SYS1_PAD_FDAT0_3_GROUP0_15_TXRX_DLY0	(0x00 >> 2)
#define	R32_SYS1_PAD_FDAT0_3_GROUP0_15_TXRX_DLY1	(0x08 >> 2)
#define	R32_SYS1_PAD_FDAT0_3_GROUP0_15_TXRX_DLY2	(0x10 >> 2)
#define	R32_SYS1_PAD_FDAT0_3_GROUP0_15_TXRX_DLY3	(0x18 >> 2)
#define	R32_SYS1_PAD_FDAT0_3_GROUP0_15_TXRX_DLY4	(0x20 >> 2)
#define	R32_SYS1_PAD_FDAT0_3_GROUP0_15_TXRX_DLY5	(0x28 >> 2)
#define	R32_SYS1_PAD_FDAT0_3_GROUP0_15_TXRX_DLY6	(0x30 >> 2)
#define	R32_SYS1_PAD_FDAT0_3_GROUP0_15_TXRX_DLY7	(0x38 >> 2)
#define	R32_SYS1_PAD_FDAT0_3_GROUP0_15_TXRX_DLY8	(0x40 >> 2)
#define	R32_SYS1_PAD_FDAT0_3_GROUP0_15_TXRX_DLY9	(0x48 >> 2)
#define	R32_SYS1_PAD_FDAT0_3_GROUP0_15_TXRX_DLY10	(0x50 >> 2)
#define	R32_SYS1_PAD_FDAT0_3_GROUP0_15_TXRX_DLY11	(0x58 >> 2)
#define	R32_SYS1_PAD_FDAT0_3_GROUP0_15_TXRX_DLY12	(0x60 >> 2)
#define	R32_SYS1_PAD_FDAT0_3_GROUP0_15_TXRX_DLY13	(0x68 >> 2)
#define	R32_SYS1_PAD_FDAT0_3_GROUP0_15_TXRX_DLY14	(0x70 >> 2)
#define	R32_SYS1_PAD_FDAT0_3_GROUP0_15_TXRX_DLY15	(0x78 >> 2)

#define	R8_SYS1_PAD_FDAT0_3_GROUP0_TXRX_DLY			(0x00)
#define	R8_SYS1_PAD_FDAT0_3_GROUP1_TXRX_DLY			(0x08)
#define	R8_SYS1_PAD_FDAT0_3_GROUP2_TXRX_DLY			(0x10)
#define	R8_SYS1_PAD_FDAT0_3_GROUP3_TXRX_DLY			(0x18)
#define	R8_SYS1_PAD_FDAT0_3_GROUP4_TXRX_DLY			(0x20)
#define	R8_SYS1_PAD_FDAT0_3_GROUP5_TXRX_DLY			(0x28)
#define	R8_SYS1_PAD_FDAT0_3_GROUP6_TXRX_DLY			(0x30)
#define	R8_SYS1_PAD_FDAT0_3_GROUP7_TXRX_DLY			(0x38)
#define	R8_SYS1_PAD_FDAT0_3_GROUP8_TXRX_DLY			(0x40)
#define	R8_SYS1_PAD_FDAT0_3_GROUP9_TXRX_DLY			(0x48)
#define	R8_SYS1_PAD_FDAT0_3_GROUP10_TXRX_DLY		(0x50)
#define	R8_SYS1_PAD_FDAT0_3_GROUP11_TXRX_DLY		(0x58)
#define	R8_SYS1_PAD_FDAT0_3_GROUP12_TXRX_DLY		(0x60)
#define	R8_SYS1_PAD_FDAT0_3_GROUP13_TXRX_DLY		(0x68)
#define	R8_SYS1_PAD_FDAT0_3_GROUP14_TXRX_DLY		(0x70)
#define	R8_SYS1_PAD_FDAT0_3_GROUP15_TXRX_DLY		(0x78)
#define	R8_SYS1_PAD_FDAT4_7_GROUP0_TXRX_DLY			(0x04)
#define	R8_SYS1_PAD_FDAT4_7_GROUP1_TXRX_DLY			(0x0C)
#define	R8_SYS1_PAD_FDAT4_7_GROUP2_TXRX_DLY			(0x14)
#define	R8_SYS1_PAD_FDAT4_7_GROUP3_TXRX_DLY			(0x1C)
#define	R8_SYS1_PAD_FDAT4_7_GROUP4_TXRX_DLY			(0x24)
#define	R8_SYS1_PAD_FDAT4_7_GROUP5_TXRX_DLY			(0x2C)
#define	R8_SYS1_PAD_FDAT4_7_GROUP6_TXRX_DLY			(0x34)
#define	R8_SYS1_PAD_FDAT4_7_GROUP7_TXRX_DLY			(0x3C)
#define	R8_SYS1_PAD_FDAT4_7_GROUP8_TXRX_DLY			(0x44)
#define	R8_SYS1_PAD_FDAT4_7_GROUP9_TXRX_DLY			(0x4C)
#define	R8_SYS1_PAD_FDAT4_7_GROUP10_TXRX_DLY		(0x54)
#define	R8_SYS1_PAD_FDAT4_7_GROUP11_TXRX_DLY		(0x5C)
#define	R8_SYS1_PAD_FDAT4_7_GROUP12_TXRX_DLY		(0x64)
#define	R8_SYS1_PAD_FDAT4_7_GROUP13_TXRX_DLY		(0x6C)
#define	R8_SYS1_PAD_FDAT4_7_GROUP14_TXRX_DLY		(0x74)
#define	R8_SYS1_PAD_FDAT4_7_GROUP15_TXRX_DLY		(0x7C)
#define		CR_FDAT_GROUP0_15_RX_DLY_3_0_SHIFT		(0)
#define		CR_FDAT_GROUP0_15_RX_DLY_3_0_MASK		(BIT_MASK(4))
#define		CR_FDAT_GROUP0_15_TX_DLY_3_0_SHIFT		(4)
#define		CR_FDAT_GROUP0_15_TX_DLY_3_0_MASK		(BIT_MASK(4))
#define		CR_FDAT_GROUP0_15_RX_DLY_7_4_SHIFT		(8)
#define		CR_FDAT_GROUP0_15_RX_DLY_7_4_MASK		(BIT_MASK(4))
#define		CR_FDAT_GROUP0_15_TX_DLY_7_4_SHIFT		(12)
#define		CR_FDAT_GROUP0_15_TX_DLY_7_4_MASK		(BIT_MASK(4))
#define		CR_FDAT_GROUP0_15_RX_DLY_11_8_SHIFT		(16)
#define		CR_FDAT_GROUP0_15_RX_DLY_11_8_MASK		(BIT_MASK(4))
#define		CR_FDAT_GROUP0_15_TX_DLY_11_8_SHIFT		(20)
#define		CR_FDAT_GROUP0_15_TX_DLY_11_8_MASK		(BIT_MASK(4))
#define		CR_FDAT_GROUP0_15_RX_DLY_15_12_SHIFT	(24)
#define		CR_FDAT_GROUP0_15_RX_DLY_15_12_MASK		(BIT_MASK(4))
#define		CR_FDAT_GROUP0_15_TX_DLY_15_12_SHIFT	(28)
#define		CR_FDAT_GROUP0_15_TX_DLY_15_12_MASK		(BIT_MASK(4))

#define	R32_SYS1_PAD_FDAT4_7_GROUP0_15_TXRX_DLY0	(0x04 >> 2)
#define	R32_SYS1_PAD_FDAT4_7_GROUP0_15_TXRX_DLY1	(0x0C >> 2)
#define	R32_SYS1_PAD_FDAT4_7_GROUP0_15_TXRX_DLY2	(0x14 >> 2)
#define	R32_SYS1_PAD_FDAT4_7_GROUP0_15_TXRX_DLY3	(0x1C >> 2)
#define	R32_SYS1_PAD_FDAT4_7_GROUP0_15_TXRX_DLY4	(0x24 >> 2)
#define	R32_SYS1_PAD_FDAT4_7_GROUP0_15_TXRX_DLY5	(0x2C >> 2)
#define	R32_SYS1_PAD_FDAT4_7_GROUP0_15_TXRX_DLY6	(0x34 >> 2)
#define	R32_SYS1_PAD_FDAT4_7_GROUP0_15_TXRX_DLY7	(0x3C >> 2)
#define	R32_SYS1_PAD_FDAT4_7_GROUP0_15_TXRX_DLY8	(0x44 >> 2)
#define	R32_SYS1_PAD_FDAT4_7_GROUP0_15_TXRX_DLY9	(0x4C >> 2)
#define	R32_SYS1_PAD_FDAT4_7_GROUP0_15_TXRX_DLY10	(0x54 >> 2)
#define	R32_SYS1_PAD_FDAT4_7_GROUP0_15_TXRX_DLY11	(0x5C >> 2)
#define	R32_SYS1_PAD_FDAT4_7_GROUP0_15_TXRX_DLY12	(0x64 >> 2)
#define	R32_SYS1_PAD_FDAT4_7_GROUP0_15_TXRX_DLY13	(0x6C >> 2)
#define	R32_SYS1_PAD_FDAT4_7_GROUP0_15_TXRX_DLY14	(0x74 >> 2)
#define	R32_SYS1_PAD_FDAT4_7_GROUP0_15_TXRX_DLY15	(0x7C >> 2)

#define		CR_FDAT_GROUP0_15_RX_DLY_19_16_SHIFT	(0)
#define		CR_FDAT_GROUP0_15_RX_DLY_19_16_MASK		(BIT_MASK(4))
#define		CR_FDAT_GROUP0_15_TX_DLY_19_16_SHIFT	(4)
#define		CR_FDAT_GROUP0_15_TX_DLY_19_16_MASK		(BIT_MASK(4))
#define		CR_FDAT_GROUP0_15_RX_DLY_23_20_SHIFT	(8)
#define		CR_FDAT_GROUP0_15_RX_DLY_23_20_MASK		(BIT_MASK(4))
#define		CR_FDAT_GROUP0_15_TX_DLY_23_20_SHIFT	(12)
#define		CR_FDAT_GROUP0_15_TX_DLY_23_20_MASK		(BIT_MASK(4))
#define		CR_FDAT_GROUP0_15_RX_DLY_27_24_SHIFT	(16)
#define		CR_FDAT_GROUP0_15_RX_DLY_27_24_MASK		(BIT_MASK(4))
#define		CR_FDAT_GROUP0_15_TX_DLY_27_24_SHIFT	(20)
#define		CR_FDAT_GROUP0_15_TX_DLY_27_24_MASK		(BIT_MASK(4))
#define		CR_FDAT_GROUP0_15_RX_DLY_31_28_SHIFT	(24)
#define		CR_FDAT_GROUP0_15_RX_DLY_31_28_MASK		(BIT_MASK(4))
#define		CR_FDAT_GROUP0_15_TX_DLY_31_28_SHIFT	(28)
#define		CR_FDAT_GROUP0_15_TX_DLY_31_28_MASK		(BIT_MASK(4))

#define	R32_SYS1_CR_FDQDLY_EN						(0x80 >> 2)
#define	R8_SYS1_CR_FDQDLY_EN						(0x80)
#define		CR_FDQDLY_EN_BIT						(BIT0)
/*
 *  +-----------------------------------------------------------------------+
 *  |					MISCH      								        	|
 *  +-----------------------------------------------------------------------+
 */
#define	SYS1_MISCH_OFFSET							(0x00003000) //0x3000~0x30FF
#define	SYS1_MISCH_CTRL_BASE						(SYSTEM_PD0_REG_ADDRESS + SYS1_MISCH_OFFSET)

#define	R8_SYS1_MISCH								((REG8  *) SYS1_MISCH_CTRL_BASE)
#define	R16_SYS1_MISCH								((REG16 *) SYS1_MISCH_CTRL_BASE)
#define	R32_SYS1_MISCH								((REG32 *) SYS1_MISCH_CTRL_BASE)
#define	R64_SYS1_MISCH								((REG64 *) SYS1_MISCH_CTRL_BASE)

#define R32_SYS1_SYS_CPU_CFG						((0x00) >> 2)
#define		CR_CPU_DCACHE_EN_BIT					(BIT0)
#define		CR_AXIM_NOWAIT_EN_BIT					(BIT8)
#define 	CR_SYS_AXIS_4K_BDY_EN_BIT				(BIT16)

#define	R32_SYS1_SYS_DBUF_CFG						((0x04) >> 2)    // new
#define		CR_DBUF_PD1_192K_INIT_START_BIT			(BIT0)
#define		CR_DBUF_PD1_256K_INIT_START_BIT			(BIT1)
#define		CR_DBUF_PD1_192K_INIT_END_BIT			(BIT6)
#define		CR_DBUF_PD1_256K_INIT_END_BIT			(BIT7)
#define		CR_DBUF_RRC_PASS_INJ_BIT				(BIT8)
#define		CR_DBUF_PERR_INJ_BIT					(BIT9)
#define		CR_DBUF_RRC_CFG_SHIFT					(10)
#define		CR_DBUF_RRC_CFG_MASK					(BIT_MASK(3))
#define		CR_DBUF_RRC_CFG 						(CR_DBUF_RRC_CFG_MASK << CR_DBUF_RRC_CFG_SHIFT)
#define		CR_DBUF_PERR_CHK_EN_BIT					(BIT14)
#define		CR_DBUF_LS_EN_BIT						(BIT15)
#define		CR_DBUF_CFG_SHIFT 						(16)
#define		CR_DBUF_CFG_MASK 						(BIT_MASK(9))
#define		CR_DBUF_CFG 							(CR_DBUF_CFG_MASK << CR_DBUF_CFG_SHIFT)

#define	R32_SYS1_DCC_CTRL_0							((0x08) >> 2)  //  new
#define		CR_COP1_FW_DCCEN_BIT					(BIT0)
#define		CR_DMAC_FW_DCCEN_BIT					(BIT1)
#define		CR_UFS_FW_DCCEN_BIT						(BIT2)
#define		CR_DZIP_FW_DCCEN_BIT					(BIT4)
#define		CR_DBUF_FW_DCCEN_BIT 					(BIT6)
#define		CR_D2H_FW_DCCEN_BIT 					(BIT7)
#define		CR_BMU_FW_DCCEN_BIT 					(BIT8)
#define		CR_XZIP_FW_DCCEN_BIT 					(BIT9)
#define		CR_FLH_SYS_FW_DCCEN_BIT 				(BIT10)
#define		CR_FLH_ECC_FW_DCCEN_BIT 				(BIT11)
#define		CR_FLH_CTL_FW_DCCEN_SHIFT 				(12)
#define		CR_FLH_CTL_FW_DCCEN_MASK				(BIT_MASK(4))
#define		CR_FLH_CTL_FW_DCCEN						(CR_FLH_CTL_FW_DCCEN_MASK << CR_FLH_CTL_FW_DCCEN_SHIFT)
#define		CR_COP1_DCC_CFG_SHIFT					(16)
#define		CR_COP1_DCC_CFG_MASK 					(BIT_MASK(3))
#define		CR_COP1_DCC_CFG 						(CR_COP1_DCC_CFG_MASK << CR_COP1_DCC_CFG_SHIFT)
#define		CR_DMAC_DCC_CFG_SHIFT 					(20)
#define		CR_DMAC_DCC_CFG_MASK 					(BIT_MASK(3))
#define		CR_DMAC_DCC_CFG							(CR_DMAC_DCC_CFG_MASK << CR_DMAC_DCC_CFG_SHIFT)
#define		CR_UFS_DCC_CFG_SHIFT 					(24)
#define		CR_UFS_DCC_CFG_MASK 					(BIT_MASK(3))
#define		CR_UFS_DCC_CFG 						    (CR_UFS_DCC_CFG_MASK << CR_UFS_DCC_CFG_SHIFT)

#define R32_SYS1_SYS_DCC_CTRL_1 					((0x0C) >> 2)
#define 	CR_DZIP_DCC_CFG_SHIFT 					(0)
#define		CR_DZIP_DCC_CFG_MASK 					(BIT_MASK(3))
#define		CR_DZIP_DCC_CFG 						(CR_DZIP_DCC_CFG_MASK << CR_DZIP_DCC_CFG_SHIFT)
#define		CR_DBUF_DCC_CFG_SHIFT					(8)
#define		CR_DBUF_DCC_CFG_MASK 					(BIT_MASK(3))
#define		CR_DBUF_DCC_CFG 						(CR_DBUF_DCC_CFG_MASK << CR_DBUF_DCC_CFG_SHIFT)
#define		CR_D2H_DCC_CFG_SHIFT 					(12)
#define		CR_D2H_DCC_CFG_MASK						(BIT_MASK(3))
#define		CR_D2H_DCC_CFG 						 	(CR_D2H_DCC_CFG_MASK << CR_D2H_DCC_CFG_SHIFT)
#define		CR_BMU_DCC_CFG_SHIFT					(16)
#define		CR_BMU_DCC_CFG_MASK						(BIT_MASK(3))
#define		CR_BMU_DCC_CFG 							(CR_BMU_DCC_CFG_MASK << CR_BMU_DCC_CFG_SHIFT)
#define		CR_XZIP_DCC_CFG_SHIFT					(20)
#define		CR_XZIP_DCC_CFG_MASK					(BIT_MASK(3))
#define		CR_XZIP_DCC_CFG 						(CR_XZIP_DCC_CFG_MASK << CR_XZIP_DCC_CFG_SHIFT)
#define 	CR_FLH_SYS_DCC_CFG_SHIFT 				(24)
#define		CR_FLH_SYS_DCC_CFG_MASK 				(BIT_MASK(3))
#define		CR_FLH_SYS_DCC_CFG 						(CR_FLH_SYS_DCC_CFG_MASK << CR_FLH_SYS_DCC_CFG_SHIFT)
#define 	CR_FLH_ECC_DCC_CFG_SHIFT 				(28)
#define		CR_FLH_ECC_DCC_CFG_MASK 				(BIT_MASK(3))
#define		CR_FLH_ECC_DCC_CFG 						(CR_FLH_ECC_DCC_CFG_MASK << CR_FLH_ECC_DCC_CFG_SHIFT)

#define R32_SYS1_SYS_DCC_CTRL_2 					((0x10) >> 2)
#define		CR_FLH_CTL_DCC_CFG_SHIFT 				(0)
#define		CR_FLH_CTL_DCC_CFG_MASK 				(BIT_MASK(12))
#define		CR_FLH_CTL_DCC_CFG 						(CR_FLH_CTL_DCC_CFG_MASK << CR_FLH_CTL_DCC_CFG_SHIFT)

#define	R32_SYS1_SYS_UNI_CFG 						((0x14) >> 2)
#define 	CR_UNI_BYPASS_LINKSTARUP_BIT 			(BIT0)
#define		CR_UNI_RX_HIBERN8EXIT				    (BIT8)

#define	R32_SYS1_SYS_APU_CFG 						((0x18) >> 2)
#define 	CR_APU_RRC_CFG_SHIFT 				 	(0)
#define		CR_APU_RRC_CFG_MASK 				    (BIT_MASK(21))
#define		CR_APU_RRC_CFG 							(CR_APU_RRC_CFG_MASK << CR_APU_RRC_CFG_SHIFT)

#define R32_SYS1_SYS_MODE_EN_0						((0x1C) >> 2)
#define		MD_LBIST_SCAN_BIT 						(BIT0)
#define		MD_TS2_TEST_BIT 						(BIT1)
#define		SR_HWMD_NORMAL_SHIFT 				    (16)
#define		SR_HWMD_NORMAL_MASK						(BIT_MASK(6))
#define		SR_HWMD_NORMAL 							(SR_HWMD_MASK << SR_HWMD_SHIFT)
#define		SR_HWMD_SHIFT 						    (24)
#define		SR_HWMD_MASK						    (BIT_MASK(6))
#define		SR_HWMD 								(SR_HWMD_MASK << SR_HWMD_SHIFT)
#define			IDX_MD_VDT_TEST						(0x11110)
#define			IDX_MD_REGU_TEST					(0x11101)
#define			IDX_MD_OC1_TEST						(0x11100)
#define			IDX_MD_OC2_TEST						(0x11011)
#define			IDX_MD_TS0_TEST						(0x11010)
#define			IDX_MD_TS1_TEST						(0x11001)
#define			IDX_MD_MEM_TEST						(0x111110)
#define			IDX_MD_PPHY_TEST 					(0x111101)
#define			IDX_MD_SPHY_TEST					(0x111100)
#define			IDX_MD_MPHY_TEST					(0x111011)
#define			IDX_MD_FPHY_TEST					(0x111010)
#define			IDX_MD_SPD0_TEST					(0x111001)
#define			IDX_MD_SPD1_TEST					(0x111000)
#define			IDX_MD_DLL_TEST						(0x110111)
#define			IDX_MD_FPHY_SCAN					(0x110110)
#define			IDX_MD_CORE_SCAN					(0x110101)
#define			IDX_MD_IP_SCAN						(0x110100)
#define			IDX_MD_FIO_TEST						(0x110011)
#define			IDX_MD_ZQ_TEST						(0x110010)
#define			IDX_MD_EFU_TEST						(0x110001)
#define			IDX_MD_DEV_TRI 						(0x110000)
#define			IDX_MD_FTL_TEST 					(0x101111)
#define			IDX_MD_PLL1_TEST					(0x101110)
#define			IDX_MD_PLL2_TEST					(0x101101)
#define			IDX_MD_GPIO_TEST					(0x101100)
#define			IDX_MD_TS2_TEST						(0x101011)

#define	R32_SYS1_SYS_MODE_EN_1						((0x20) >> 2)
#define		MD_NOR_EN_BIT                           (BIT0)
#define		MD_PTM_TEST_BIT							(BIT1)
#define		MD_ICE_MODE_BIT							(BIT2)
#define		MD_DBG_MODE_BIT							(BIT3)
#define		MD_SPI_MODE_BIT							(BIT4)
#define		MD_MON_MODE_BIT							(BIT5)
#define		MD_UART_LOAD_TEST_BIT					(BIT6)
#define		MD_VDT_TEST_EN_BIT						(BIT7)
#define		MD_REGU_TEST_BIT						(BIT8)
#define		MD_OC2_TEST_BIT 						(BIT9)
#define		MD_OC1_TEST_BIT 						(BIT10)
#define		MD_TS0_TEST_BIT							(BIT11)
#define		MD_TS1_TEST_BIT 						(BIT12)
#define		MD_MEM_TEST_BIT 						(BIT13)
#define		MD_PPHY_TEST_BIT 						(BIT14)
#define		MD_SPHY_TEST_BIT 						(BIT15)
#define		MD_MPHY_TEST_BIT 						(BIT16)
#define 	MD_FPHY_TEST_BIT 						(BIT17)
#define 	MD_SPD0_TEST_BIT 						(BIT18)
#define		MD_SPD1_TEST_BIT						(BIT19)
#define		MD_DLL_TEST_BIT 						(BIT20)
#define		MD_FPHY_SCAN_BIT 						(BIT21)
#define		MD_CORE_SCAN_BIT 						(BIT22)
#define		MD_IP_SCAN_BIT 							(BIT23)
#define		MD_FIO_SCAN_BIT 						(BIT24)
#define 	MD_ZQ_SCAN_BIT 							(BIT25)
#define 	MD_EFU_TEST_BIT 						(BIT26)
#define		MD_DEV_TEST_BIT 						(BIT27)
#define		MD_FTL_TEST_BIT 						(BIT28)
#define 	MD_PLL1_TEST_BIT 						(BIT29)
#define		MD_PLL2_TEST_BIT 						(BIT30)
#define 	MD_GPIO_TEST_BIT 						(BIT31)

#define	R32_SYS1_SYS_DMA_PCA_VB_POS					((0x24) >> 2)                                       //Default: 0x00000000
#define R8_SYS1_CR_DMA_PCA_VB_POS					(0x24 >> 0)
#define R8_SYS1_CR_DMA_PCA_VB_LEN					(0x25 >> 0)
#define		CR_DMA_PCA_VB_POS_SHIFT					(0)
#define		CR_DMA_PCA_VB_POS_MASK					BIT_MASK(32)
#define		CR_DMA_PCA_VB_POS						((U32)(CR_DMA_PCA_VB_POS_MASK << CR_DMA_PCA_VB_POS_SHIFT))

#define	R32_SYS1_SYS_DMA_MR_TRG_RMP0				((0x28) >> 2)                                       //Default: 0x50340100
#define		CR_DMA_MR_TRG_RMP0_SHIFT				(0)
#define		CR_DMA_MR_TRG_RMP0_MASK					BIT_MASK(32)
#define		CR_DMA_MR_TRG_RMP0						((U32)(CR_DMA_MR_TRG_RMP0_MASK << CR_DMA_MR_TRG_RMP0_SHIFT))

#define	R32_SYS1_SYS_DMA_MR_TRG_RMP1				((0x2C) >> 2)                                       //Default: 0x00F08C80
#define		CR_DMA_MR_TRG_RMP1_SHIFT				(0)
#define		CR_DMA_MR_TRG_RMP1_MASK					BIT_MASK(32)
#define		CR_DMA_MR_TRG_RMP1						((U32)(CR_DMA_MR_TRG_RMP1_MASK << CR_DMA_MR_TRG_RMP1_SHIFT))

#define	R32_SYS1_SYS_DMA_CPY_SRC0					((0x30) >> 2)                                       //Default: 0x00000000
#define		CR_SYS_DMA_CPY_SRC0_SHIFT				(0)
#define		CR_SYS_DMA_CPY_SRC0_MASK				BIT_MASK(32)
#define		CR_SYS_DMA_CPY_SRC0						((U32)(CR_SYS_DMA_CPY_SRC0_MASK << CR_SYS_DMA_CPY_SRC0_SHIFT))

#define	R32_SYS1_SYS_DMA_CPY_TRG0					((0x34) >> 2)                                       //Default: 0x00000000
#define		CR_SYS_DMA_CPY_TRG0_SHIFT				(0)
#define		CR_SYS_DMA_CPY_TRG0_MASK				BIT_MASK(32)
#define		CR_SYS_DMA_CPY_TRG0						((U32)(CR_SYS_DMA_CPY_TRG0_MASK << CR_SYS_DMA_CPY_TRG0_SHIFT))

#define	R32_SYS1_SYS_DMA_CPY_LEN0					((0x38) >> 2)                                       //Default: 0x00000000
#define		CR_SYS_DMA_CPY_LEN0_SHIFT				(0)
#define		CR_SYS_DMA_CPY_LEN0_MASK				BIT_MASK(24)
#define		CR_SYS_DMA_CPY_LEN0						((U32)(CR_SYS_DMA_CPY_LEN0_MASK << CR_SYS_DMA_CPY_LEN0_SHIFT))

#define	R32_SYS1_SYS_DMA_CPY_GOLD0					((0x3C) >> 2)                                       //Default: 0x00000000
#define		CR_SYS_DMA_CPY_GOLD0_SHIFT				(0)
#define		CR_SYS_DMA_CPY_GOLD0_MASK				BIT_MASK(32)

#define	R32_SYS1_SYS_DMA_CPY_SRC1					((0x40) >> 2)                                       //Default: 0x00000000
#define		CR_SYS_DMA_CPY_SRC1_SHIFT				(0)
#define		CR_SYS_DMA_CPY_SRC1_MASK				BIT_MASK(32)
#define		CR_SYS_DMA_CPY_SRC1						((U32)(CR_SYS_DMA_CPY_SRC1_MASK << CR_SYS_DMA_CPY_SRC1_SHIFT))

#define	R32_SYS1_SYS_DMA_CPY_TRG1					((0x44) >> 2)                                       //Default: 0x00000000
#define		CR_SYS_DMA_CPY_TRG1_SHIFT				(0)
#define		CR_SYS_DMA_CPY_TRG1_MASK				BIT_MASK(32)
#define		CR_SYS_DMA_CPY_TRG1						((U32)(CR_SYS_DMA_CPY_TRG1_MASK << CR_SYS_DMA_CPY_TRG1_SHIFT))

#define	R32_SYS1_SYS_DMA_CPY_LEN1					((0x48) >> 2)                                       //Default: 0x00000000
#define		CR_SYS_DMA_CPY_LEN1_SHIFT				(0)
#define		CR_SYS_DMA_CPY_LEN1_MASK				BIT_MASK(24)
#define		CR_SYS_DMA_CPY_LEN1						((U32)(CR_SYS_DMA_CPY_LEN1_MASK << CR_SYS_DMA_CPY_LEN1_SHIFT))

#define	R32_SYS1_SYS_DMA_CPY_GOLD1					((0x4C) >> 2)                                       //Default: 0x00000000
#define		CR_SYS_DMA_CPY_GOLD1_SHIFT				(0)
#define		CR_SYS_DMA_CPY_GOLD1_MASK				BIT_MASK(32)
#define		CR_SYS_DMA_CPY_GOLD1					((U32)(CR_SYS_DMA_CPY_GOLD1_MASK << CR_SYS_DMA_CPY_GOLD1_SHIFT))

#define	R32_SYS1_SYS_DMA_REG						((0x50) >> 2)                                       //Default: 0x00000001
#define		SR_DMA_IDLE_SHIFT						(0)
#define		SR_DMA_IDLE_MASK						BIT_MASK(8)
#define		SR_DMA_IDLE 							(SR_DMA_IDLE_MASK << SR_DMA_IDLE_SHIFT)
#define			MRIR_IDLE_BIT 						(BIT0)
#define			SQ_IDLE_BIT 						(BIT1)
#define			SCH_IDLE_BIT 						(BIT2)
#define			CORE_S_IDLE_BIT						(BIT3)
#define			CORE_T_IDLE_BIT 					(BIT4)
#define			SORT_IDLE_BIT 						(BIT5)
#define			CQ_IDLE_BIT 						(BIT6)
#define  		MRIW_IDLE_BIT 						(BIT7)
#define		CR_DMA_PCA_ZINFO_POS_SHIFT				(8)
#define		CR_DMA_PCA_ZINFO_POS_MASK				BIT_MASK(5)
#define		CR_DMA_PCA_ZINFO_POS					((U32)(CR_DMA_PCA_ZINFO_POS_MASK << CR_DMA_PCA_ZINFO_POS_SHIFT))
#define		CR_DMA_RESUME_SHIFT						(16)
#define		CR_DMA_RESUME_MASK 						(BIT_MASK(2))
#define		CR_DMA_RESUME 							(CR_DMA_RESUME_MASK << CR_DMA_RESUME_SHIFT)
#define		CR_DMA_ABORT_SHIFT 						(18)
#define		CR_DMA_ABORT_MASK						(BIT_MASK(2))
#define		CR_DMA_ABORT 							(CR_DMA_ABORT_MASK << CR_DMA_ABORT_SHIFT)
#define		CR_DMAC_RRC_PASS_INJ_BIT 				(BIT24)
#define		CR_DMAC_RRC_LS_EN_BIT 					(BIT25)
#define		CR_DMAC_RRC_RRC_CFG_SHIFT 				(26)
#define		CR_DMAC_RRC_RRC_CFG_MASK 				(BIT_MASK(3))
#define		CR_DMAC_RRC_RRC_CFG 					(CR_DMAC_RRC_RRC_CFG_MASK << CR_DMAC_RRC_RRC_CFG_SHIFT)
#define		CR_DMAC_ERR_STALL_EN_BIT				(BIT31)

#define	R32_SYS1_SYS_COP_CONFIG						((0x54) >> 2)                                       //Default: 0x00000000
#define		CR_COP0_CORE_RUN_BIT					(BIT0)

#define	R32_SYS1_SYS_FIP_REG						((0x58) >> 2)                                       //Default: 0x00000000
#define		SR_FIP_FLH_BSY_SHIFT					(0)
#define		SR_FIP_FLH_BSY_MASK						BIT_MASK(4)
#define		SR_FIP_FLH_BSY							((U32)(SR_FIP_FLH_BSY_MASK << SR_FIP_FLH_BSY_SHIFT))
#define		SR_FIP_ECC_BSY_BIT						(BIT4)
#define		SR_FIP_SYS_BSY_BIT						(BIT5)

#define	R32_AXI_CFG_SHIFT 							((0x5C) >> 2)
#define		CR_AXI_OUT_OF_ORDER_EN_SHIFT			(0)
#define		CR_AXI_OUT_OF_ORDER_EN_MASK				(BIT_MASK(10))
#define		CR_AXI_OUT_OF_ORDER_EN 					(CR_AXI_OUT_OF_ORDER_EN_MASK << CR_AXI_OUT_OF_ORDER_EN_SHIFT)

#define R32_SYS1_SYS_PD1_RAM_RME0                   ((0x60) >> 2)   //  64->60                                       //Default: 0x00000000
#define		CR_PD1_RAM_RME_LL_SHIFT					(0)
#define		CR_PD1_RAM_RME_LL_MASK					(BIT_MASK(32))
#define		CR_PD1_RAM_RME_LL 						(CR_PD1_RAM_RME_L_MASK << CR_PD1_RAM_RME_LL_SHIFT)

#define R32_SYS1_SYS_PD1_RAM_RME1                   ((0x64) >> 2)                                    //Default: 0x00000000
#define		CR_PD1_RAM_RME_LH_SHIFT					(0)
#define		CR_PD1_RAM_RME_LH_MASK					(BIT_MASK(32))
#define		CR_PD1_RAM_RME_LH 						(CR_PD1_RAM_RME_LH_MASK << CR_PD1_RAM_RME_LH_SHIFT)

#define	R32_SYS1_SYS_PD1_RAM_RME2 					((0x68) >> 2)
#define		CR_PD1_RAM_RME_HL_SHIFT					(0)
#define		CR_PD1_RAM_RME_HL_MASK					(BIT_MASK(32))
#define		CR_PD1_RAM_RME_HL 						(CR_PD1_RAM_RME_HL_MASK << CR_PD1_RAM_RME_HL_SHIFT)

#define	R32_SYS1_SYS_PD1_RAM_RME3 					((0x6C) >> 2)
#define		CR_PD1_RAM_RME_HH_SHIFT					(0)
#define		CR_PD1_RAM_RME_HH_MASK					(BIT_MASK(25))
#define		CR_PD1_RAM_RME_HH 						(CR_PD1_RAM_RME_HH_MASK << CR_PD1_RAM_RME_HH_SHIFT)

#define R32_SYS1_SYS_PD1_RAM_RM0                    ((0x70) >> 2)                                   //Default: 0x00000000
#define		CR_PD1_RAM_RM_SHIFT						(0)
#define		CR_PD1_RAM_RM_MASK						(BIT_MASK(2))
#define		CR_PD1_RAM_RM							(CR_PD1_RAM_RM_MASK << CR_PD1_RAM_RM_MASK)

#define	R32_SYS1_SYS_PD51_RAM_RME 					((0x78) >> 2)
#define		CR_PD51_RAM_RME_SHIFT 					(0)
#define		CR_PD51_RAM_RME_MASK 					(BIT_MASK(24))
#define		CR_PD51_RAM_RME 						(CR_PD51_RAM_RME_MASK << CR_PD51_RAM_RME_SHIFT)

#define	R32_SYS1_SYS_PD51_RAM_RM 					((0x7C) >> 2)
#define		CR_PD51_RAM_RM_SHIFT   					(0)
#define		CR_PD51_RAM_RM_MASK   					(BIT_MASK(2))
#define		CR_PD51_RAM_RM   						(CR_PD51_RAM_RM_MASK << CR_PD51_RAM_RM_SHIFT)

#define	R32_SYS1_SYS_PD1_MISCH_CTRL					((0x80) >> 2)                                       //Default: 0x00000000
#define		CR_SYS_XZIP_EN_BIT						(BIT0)
#define		CR_SYS_CHK_RANGE_EN_BIT					(BIT1)
#define		CR_SYS_CHK_LBNA_EN_BIT					(BIT2)
#define		CR_TCM_ECC_EN_SHIFT						(3)
#define		CR_TCM_ECC_EN_MASK						BIT_MASK(3)
#define		CR_TCM_ECC_EN							(CR_TCM_ECC_EN_MASK << CR_TCM_ECC_EN_SHIFT)
#define		CR_DBUF_RRC_SET_SHIFT					(6)
#define		CR_DBUF_RRC_SET_MASK					BIT_MASK(2)
#define		CR_DBUF_RRC_SET							(CR_DBUF_RRC_SET_MASK << CR_DBUF_RRC_SET_SHIFT)
#define		CR_COP1_SRAM_GAT_EN_SHIFT				(8)
#define		CR_COP1_SRAM_GAT_EN_MASK				(BIT_MASK(8))
#define		CR_COP1_SRAM_GAT_EN 					(CR_COP1_SRAM_GAT_EN_MASK << CR_COP1_SRAM_GAT_EN_SHIFT)
#define		CR_AXIM_RSP_ERR_EN_BIT					(BIT16)
#define		CR_ATCM_GAT_EN_BIT						(BIT17)
#define		CR_B0TCM_GAT_EN_BIT						(BIT18)
#define		CR_B1TCM_GAT_EN_BIT						(BIT19)
#define 	CR_DCACHE_GAT_EN_BIT					(BIT20)
#define		CR_FLH_RAM0_GAT_EN_BIT 					(BIT21)
#define		CR_FLH_RAM1_GAT_EN_BIT					(BIT22)
#define		CR_FLH_RAM2_GAT_EN_BIT					(BIT23)
#define		CR_DBUF0_PD1_GAT_EN_BIT 				(BIT24)
#define		CR_DBUF1_PD1_GAT_EN_BIT					(BTI25)
#define		CR_DBUF2_PD1_GAT_EN_BIT					(BTI26)
#define		CR_DMAC_SRAM_GAT_EN_BIT 				(BTI27)
#define		CR_HOST_SRAM_GAT_EN_BIT 				(BTI28)
#define		CR_MR_SRAM_GAT_EN_BIT 					(BIT29)
#define		CR_BMU_SRAM_GAT_EN_BIT 					(BIT30)
#define		CR_XZIP_SRAM_GAT_EN_BIT 				(BIT31)

#define	R32_SYS1_SYS_ROM_CRC_GOLD0					((0x84) >> 2)                                       //Default: 0x00000000
#define		CR_SYS_ROM_CRC_GOLD0_SHIFT				(0)
#define		CR_SYS_ROM_CRC_GOLD0_MASK				BIT_MASK(32)
#define		CR_SYS_ROM_CRC_GOLD0					(CR_SYS_ROM_CRC_GOLD0_MASK << CR_SYS_ROM_CRC_GOLD0_SHIFT)

#define	R32_SYS1_SYS_ROM_CRC_GOLD1					((0x88) >> 2)                                       //Default: 0x00000000
#define		CR_SYS_ROM_CRC_GOLD1_SHIFT				(0)
#define		CR_SYS_ROM_CRC_GOLD1_MASK				BIT_MASK(32)
#define		CR_SYS_ROM_CRC_GOLD1					(CR_SYS_ROM_CRC_GOLD1_MASK << CR_SYS_ROM_CRC_GOLD1_SHIFT)
#define     CR_ITC_VLD                              (BIT31)

#define	R32_SYS1_SYS_DMA_ERR_STS					((0x90) >> 2)
#define		CR_DMA_ERR_STS_SHIFT 					(0)
#define		CR_DMA_ERR_STS_MASK 					(BIT_MASK(32))
#define		CR_DMA_ERR_STS 							(CR_DMA_ERR_STS_MASK << CR_DMA_ERR_STS_SHIFT)

#define	R32_SYS1_SYS_DMA_ERR_SEL					((0x94) >> 2)
#define R8_SYS1_SYS_DMA_ERR_SEL						(0x94)
#define		CR_DMA_ERR_SEL_SHIFT 					(0)
#define		CR_DMA_ERR_SEL_MASK 					(BIT_MASK(8))
#define		CR_DMA_ERR_SEL 							(CR_DMA_ERR_SEL_MASK << CR_DMA_ERR_SEL_SHIFT)

#define R32_SYS1_SYS_PERR_STS						((0x98) >> 2)
#define		SR_DMAC0_PERR_ADDR_SHIFT 				(0)
#define		SR_DMAC0_PERR_ADDR_MASk 				(BIT_MASK(7))
#define		SR_DMAC0_PERR_ADDR 						(SR_DMAC0_PERR_ADDR_MASk << SR_DMAC0_PERR_ADDR_SHIFT)
#define 	SR_DMAC0_PERR_VLD_BIT					(BIT7)
#define		SR_DMAC1_PERR_ADDR_SHIFT				(8)
#define		SR_DMAC1_PERR_ADDR_MASK 				(BIT_MASK(7))
#define		SR_DMAC1_PERR_ADDR 						(SR_DMAC1_PERR_ADDR_MASK << SR_DMAC1_PERR_ADDR_SHIFT)
#define		SR_DMAC1_PERR_VLD_BIT 					(BIT15)
#define		SR_DMAC2_PERR_ADDR_SHIFT				(16)
#define		SR_DMAC2_PERR_ADDR_MASK 				(BIT_MASK(7))
#define		SR_DMAC2_PERR_ADDR 						(SR_DMAC2_PERR_ADDR_MASK << SR_DMAC2_PERR_ADDR_SHIFT)
#define		SR_DMAC2_PERR_VLD_BIT 					(BIT23)
#define		CR_DMAC0_PERR_CLR_BIT 					(BIT24)
#define		CR_DMAC1_PERR_CLR_BIT 					(BIT25)
#define		CR_DMAC2_PERR_CLR_BIT 					(BIT26)

#define	R32_SYS1_SYS_FLH_PAD_I_0					((0xA0) >> 2) // Depends on padc_reg.h
#define		SYS_FLH_PAD_I_0_XFRDY_I_BIT 			(BIT0)
#define		SYS_FLH_PAD_I_0_XFWPB_I_BIT				(BIT1)
#define		SYS_FLH_PAD_I_0_XFCEB_I_SHIFT 			(8)
#define		SYS_FLH_PAD_I_0_XFCEB_I_MASK 			(BIT_MASK(16))
#define		SYS_FLH_PAD_I_0_XFCEB_I 				(SYS_FLH_PAD_I_0_XFCEB_I_MASK << SYS_FLH_PAD_I_0_XFCEB_I_SHIFT)

#define	R32_SYS1_SYS_FLH_PAD_I_1					((0xA4) >> 2)
#define		XFCLE0_I_BIT 							(BIT0)
#define		XFALE0_I_BIT							(BIT1)
#define		XFWEB0_I_BIT 							(BIT2)
#define		XFREB0_I_BIT 							(BIT3)
#define		XFDQS0_I_BIT 							(BIT4)
#define		XFDQSB0_I_BIT 							(BIT5)
#define		XFCLE1_I_BIT 							(BIT8)
#define		XFALE1_I_BIT							(BIT9)
#define		XFWEB1_I_BIT 							(BIT10)
#define		XFREB1_I_BIT 							(BIT11)
#define		XFDQS1_I_BIT 							(BIT12)
#define		XFDQSB1_I_BIT 							(BIT13)
#define		XFCLE2_I_BIT 							(BIT16)
#define		XFALE2_I_BIT							(BIT17)
#define		XFWEB2_I_BIT 							(BIT18)
#define		XFREB2_I_BIT 							(BIT19)
#define		XFDQS2_I_BIT 							(BIT20)
#define		XFDQSB2_I_BIT 							(BIT21)
#define		XFCLE3_I_BIT 							(BIT24)
#define		XFALE3_I_BIT							(BIT25)
#define		XFWEB3_I_BIT 							(BIT26)
#define		XFREB3_I_BIT 							(BIT27)
#define		XFDQS3_I_BIT 							(BIT28)
#define		XFDQSB3_I_BIT 							(BIT29)

#define	R32_SYS1_SYS_FLH_PAD_I_2					((0xA8) >> 2)
#define		XFDAT_I_SHIFT 							(0)
#define		XFDAT_I_MASK 							(BIT_MASK(32))
#define		XFDAT_I 								(XFDAT_I_MASK << XFDAT_I_SHIFT)

#define	R32_SYS1_SYS_FLH_TARGET_ID0 				((0xB0) >> 2)
#define		CR_FLH_TARGET_ID0_SHIFT 				(0)
#define		CR_FLH_TARGET_ID0_MASK					(BIT_MASK(8))
#define		CR_FLH_TARGET_ID0 						(CR_FLH_TARGET_ID0_MASK << CR_FLH_TARGET_ID0_SHIFT)
#define		CR_FLH_TARGET_ID1_SHIFT 				(8)
#define		CR_FLH_TARGET_ID1_MASK					(BIT_MASK(8))
#define		CR_FLH_TARGET_ID1 						(CR_FLH_TARGET_ID1_MASK << CR_FLH_TARGET_ID1_SHIFT)
#define		CR_FLH_TARGET_ID2_SHIFT 				(16)
#define		CR_FLH_TARGET_ID2_MASK 					(BIT_MASK(8))
#define		CR_FLH_TARGET_ID2 						(CR_FLH_TARGET_ID2_MASK << CR_FLH_TARGET_ID2_SHIFT)
#define		CR_FLH_TARGET_ID3_SHIFT 				(16)
#define		CR_FLH_TARGET_ID3_MASK 					(BIT_MASK(8))
#define		CR_FLH_TARGET_ID3 						(CR_FLH_TARGET_ID3_MASK << CR_FLH_TARGET_ID3_SHIFT)

#define	R32_SYS1_SYS_FLH_TARGET_ID1 				((0xB4) >> 2)
#define		CR_FLH_TARGET_ID4_SHIFT 				(0)
#define		CR_FLH_TARGET_ID4_MASK 					(BIT_MASK(8))
#define		CR_FLH_TARGET_ID4 						(CR_FLH_TARGET_ID4_MASK << CR_FLH_TARGET_ID4_SHIFT)
#define		CR_FLH_TARGET_ID5_SHIFT 				(8)
#define		CR_FLH_TARGET_ID5_MASK 					(BIT_MASK(8))
#define		CR_FLH_TARGET_ID5 						(CR_FLH_TARGET_ID5_MASK << CR_FLH_TARGET_ID5_SHIFT)

#define	R32_SYS1_SYS_PPHY_BIST_REG					((0xBC) >> 2)
#define		CR_PPHY_BIST_SEL_SHIFT 					(0)
#define		CR_PPHY_BIST_SEL_MASK 					(BIT_MASK(7))
#define		CR_PPHY_BIST_SEL 						(CR_PPHY_BIST_SEL_MASK << CR_PPHY_BIST_SEL_SHIFT)
#define		CR_PPHY_BIST0_EN_BIT 					(BIT8)
#define		CR_PPHY_BIST0_PD_BIT					(BIT9)
#define		CR_PPHY_BIST1_EN_BIT 					(BIT10)
#define		CR_PPHY_BIST1_PD_BIT 					(BIT11)
#define		CR_PPHY_BIST2_EN_BIT 					(BIT12)
#define		CR_PPHY_BIST2_PD_BIT 					(BIT13)
#define		CR_PPHY_BIST3_EN_BIT 					(BIT14)
#define		CR_PPHY_BIST3_PD_BIT 					(BIT15)
#define		CR_PPHY_BIST_RSTN_BIT 					(BIT31)

#define	R32_SYS1_SYS_AXI_WTO_CFG 					((0xC0) >> 2)
#define		CR_AXI_TIMEOUT_WCNT_SHIFT 				(0)
#define		CR_AXI_TIMEOUT_WCNT_MASK 				(BIT_MASK(20))
#define		CR_AXI_TIMEOUT_WCNT 					(CR_AXI_TIMEOUT_WCNT_MASK << CR_AXI_TIMEOUT_WCNT_SHIFT)
#define		SR_AXI_TIMEOUT_WID_SHIFT 				(24)
#define		SR_AXI_TIMEOUT_WID_MASK 				(BIT_MASK(4))
#define		SR_AXI_TIMEOUT_WID 						(SR_AXI_TIMEOUT_WID_MASK << SR_AXI_TIMEOUT_WID_SHIFT)
#define		CR_AXI_TIMEOUT_W_CLR_BIT 				(BIT31)

#define R32_SYS1_SYS_AXI_RTO_CFG					((0xC4) >> 2)
#define		CR_AXI_TIMEOUT_RCNT_SHIFT 				(0)
#define		CR_AXI_TIMEOUT_RCNT_MASK 				(BIT_MASK(20))
#define		CR_AXI_TIMEOUT_RCNT 					(CR_AXI_TIMEOUT_RCNT_MASK << CR_AXI_TIMEOUT_RCNT_SHIFT)
#define		SR_AXI_TIMEOUT_RID_SHIFT 				(24)
#define		SR_AXI_TIMEOUT_RID_MASK 				(BIT_MASK(4))
#define		SR_AXI_TIMEOUT_RID 						(SR_AXI_TIMEOUT_RID_MASK << SR_AXI_TIMEOUT_RID_SHIFT)
#define		CR_AXI_TIMEOUT_R_CLR_BIT 				(BIT31)

#define R32_SYS1_SYS_AXIMON_DBUF2_START_ADDR		((0xC8) >> 2)
#define		CR_AXIMON_DBUF2_START_ADDR_SHIFT 		(0)
#define		CR_AXIMON_DBUF2_START_ADDR_MASK 		(BIT_MASK(32))
#define		CR_AXIMON_DBUF2_START_ADDR 				(CR_AXIMON_DBUF2_START_ADDR_MASK << CR_AXIMON_DBUF2_START_ADDR_SHIFT)

#define R32_SYS1_SYS_AXIMON_DBUF3_START_ADDR		((0xCC) >> 2)
#define		CR_AXIMON_DBUF3_START_ADDR_SHIFT 		(0)
#define		CR_AXIMON_DBUF3_START_ADDR_MASK 		(BIT_MASK(32))
#define		CR_AXIMON_DBUF3_START_ADDR 				(CR_AXIMON_DBUF3_START_ADDR_MASK << CR_AXIMON_DBUF3_START_ADDR_SHIFT)

#define R32_SYS1_SYS_AXIMON_CPU_START_ADDR			((0xD0) >> 2)
#define		CR_AXIMON_CPU_START_ADDR_SHIFT 			(0)
#define		CR_AXIMON_CPU_START_ADDR_MASK 			(BIT_MASK(32))
#define		CR_AXIMON_CPU_START_ADDR 				(CR_AXIMON_CPU_START_ADDR_MASK << CR_AXIMON_CPU_START_ADDR_SHIFT)

#define R32_SYS1_SYS_AXIMON_AESH_START_ADR			((0xD4) >> 2)
#define		CR_AXIMON_AESH_START_ADDR_SHIFT 		(0)
#define		CR_AXIMON_AESH_START_ADDR_MASK 			(BIT_MASK(32))
#define		CR_AXIMON_AESH_START_ADDR 				(CR_AXIMON_AESH_START_ADDR_MASK << CR_AXIMON_AESH_START_ADDR_SHIFT)

#define	R16_SYS1_SYS_AXIMON_DBUF2_START_LEN			((0xD8) >> 1)
#define	R16_SYS1_SYS_AXIMON_DBUF3_START_LEN			((0xDA) >> 1)
#define R32_SYS1_SYS_AXIMON_DBUF2_3_START_LEN		((0xD8) >> 2)
#define		CR_AXIMON_DBUF2_START_LEN_SHIFT 		(0)
#define		CR_AXIMON_DBUF2_START_LEN_MASK 			(BIT_MASK(16))
#define		CR_AXIMON_DBUF2_START_LEN 				(CR_AXIMON_DBUF2_START_LEN_MASK << CR_AXIMON_DBUF2_START_LEN_SHIFT)
#define		CR_AXIMON_DBUF3_START_LEN_SHIFT 		(16)
#define		CR_AXIMON_DBUF3_START_LEN_MASK 			(BIT_MASK(16))
#define		CR_AXIMON_DBUF3_START_LEN 				(CR_AXIMON_DBUF3_START_LEN_MASK << CR_AXIMON_DBUF3_START_LEN_SHIFT)

#define R32_SYS1_SYS_AXIMON_CPU_AESH_START_LEN		((0xDC) >> 2)
#define		CR_AXIMON_CPU_START_LEN_SHIFT 			(0)
#define		CR_AXIMON_CPU_START_LEN_MASK 			(BIT_MASK(16))
#define		CR_AXIMON_CPU_START_LEN 				(CR_AXIMON_CPU_START_LEN_MASK << CR_AXIMON_CPU_START_LEN_SHIFT)
#define		CR_AXIMON_AESH_START_LEN_SHIFT 			(16)
#define		CR_AXIMON_AESH_START_LEN_MASK 			(BIT_MASK(16))
#define		CR_AXIMON_AESH_START_LEN 				(CR_AXIMON_AESH_START_LEN_MASK << CR_AXIMON_AESH_START_LEN_SHIFT)

#define R32_SYS1_SYS_AXIMON_DBUF2_ACCESS_ADR		((0xE0) >> 2)
#define		CR_AXIMON_DBUF2_ACCESS_ADDR_SHIFT 		(0)
#define		CR_AXIMON_DBUF2_ACCESS_ADDR_MASK 		(BIT_MASK(32))
#define		CR_AXIMON_DBUF2_ACCESS_ADDR 			(CR_AXIMON_DBUF2_ACCESS_ADDR_MASK << CR_AXIMON_DBUF2_ACCESS_ADDR_SHIFT)

#define R32_SYS1_SYS_AXIMON_DBUF3_ACCESS_ADR		((0xE4) >> 2)
#define		CR_AXIMON_DBUF3_ACCESS_ADDR_SHIFT 		(0)
#define		CR_AXIMON_DBUF3_ACCESS_ADDR_MASK 		(BIT_MASK(32))
#define		CR_AXIMON_DBUF3_ACCESS_ADDR 			(CR_AXIMON_DBUF3_ACCESS_ADDR_MASK << CR_AXIMON_DBUF3_ACCESS_ADDR_SHIFT)

#define R32_SYS1_SYS_AXIMON_CPU_ACCESS_ADR			((0xE8) >> 2)
#define		CR_AXIMON_CPU_ACCESS_ADDR_SHIFT 		(0)
#define		CR_AXIMON_CPU_ACCESS_ADDR_MASK 			(BIT_MASK(32))
#define		CR_AXIMON_CPU_ACCESS_ADDR 				(CR_AXIMON_CPU_ACCESS_ADDR_MASK << CR_AXIMON_CPU_ACCESS_ADDR_SHIFT)

#define R32_SYS1_SYS_AXIMON_AESH_ACCESS_ADR			((0xEC) >> 2)
#define		CR_AXIMON_AESH_ACCESS_ADDR_SHIFT 		(0)
#define		CR_AXIMON_AESH_ACCESS_ADDR_MASK 		(BIT_MASK(32))
#define		CR_AXIMON_AESH_ACCESS_ADDR 				(CR_AXIMON_AESH_ACCESS_ADDR_MASK << CR_AXIMON_AESH_ACCESS_ADDR_SHIFT)

#define R32_SYS1_SYS_AXIMON_ACCESS_ID				((0xF0) >> 2)
#define		CR_AXIMON_DBUF2_ACCESS_ID_SHIFT 		(0)
#define		CR_AXIMON_DBUF2_ACCESS_ID_MASK 			(BIT_MASK(4))
#define		CR_AXIMON_DBUF2_ACCESS_ID 				(CR_AXIMON_DBUF2_ACCESS_ID_MASK << CR_AXIMON_DBUF2_ACCESS_ID_SHIFT)
#define		CR_AXIMON_DBUF3_ACCESS_ID_SHIFT 		(8)
#define		CR_AXIMON_DBUF3_ACCESS_ID_MASK 			(BIT_MASK(4))
#define		CR_AXIMON_DBUF3_ACCESS_ID 				(CR_AXIMON_DBUF3_ACCESS_ID_MASK << CR_AXIMON_DBUF3_ACCESS_ID_SHIFT)
#define		CR_AXIMON_CPU_ACCESS_ID_SHIFT 			(16)
#define		CR_AXIMON_CPU_ACCESS_ID_MASK 			(BIT_MASK(4))
#define		CR_AXIMON_CPU_ACCESS_ID 				(CR_AXIMON_CPU_ACCESS_ID_MASK << CR_AXIMON_CPU_ACCESS_ID_SHIFT)
#define		CR_AXIMON_AESH_ACCESS_ID_SHIFT 			(24)
#define		CR_AXIMON_AESH_ACCESS_ID_MASK 			(BIT_MASK(4))
#define		CR_AXIMON_AESH_ACCESS_ID 				(CR_AXIMON_AESH_ACCESS_ID_MASK << CR_AXIMON_AESH_ACCESS_ID_SHIFT)

/*
 *  +-----------------------------------------------------------------------+
 *  |					ITC      								        	|
 *  +-----------------------------------------------------------------------+
 */
#define	SYS1_ITC_OFFSET								(0x00003100) //0x3100~0x31FF
#define	SYS1_ITC_BASE								(SYSTEM_PD0_REG_ADDRESS + SYS1_ITC_OFFSET)

#define	R8_SYS1_ITC									((REG8  *) SYS1_ITC_BASE)
#define	R32_SYS1_ITC								((REG32 *) SYS1_ITC_BASE)

#define	R32_SYS1_INT_STS0							((0x00) >> 2)                                         //Default: 0x00000000
#define		GPIO_SYS_EVT_LEN						(14)
#define		GPIO_SYS_EVT_SHIFT						(0)
#define		GPIO_SYS_EVT_MASK						BIT_MASK(GPIO_SYS_EVT_LEN)
#define		GPIO_SYS_EVT							((U32)(GPIO_SYS_EVT_MASK << GPIO_SYS_EVT_SHIFT))
#define		VDT1_SYS_EVT_LEN						(13)
#define		VDT1_SYS_EVT_SHIFT						(16)
#define		VDT1_SYS_EVT_MASK						BIT_MASK(VDT1_SYS_EVT_LEN)
#define		VDT1_SYS_EVT							((U32)(VDT1_SYS_EVT_MASK << VDT1_SYS_EVT_SHIFT))

#define	R32_SYS1_INT_STS1							((0x04) >> 2)                                         //Default: 0x00000000
#define		PMU_SYS_EVT_LEN							(14)
#define		PMU_SYS_EVT_SHIFT						(0)
#define		PMU_SYS_EVT_MASK						BIT_MASK(PMU_SYS_EVT_LEN)
#define		PMU_SYS_EVT								((U32)(PMU_SYS_EVT_MASK << PMU_SYS_EVT_SHIFT))
#define		PERSTN_SYS_EVT_SHIFT					(14)
#define		PERSTN_SYS_EVT_MASK						BIT_MASK(2)
#define		PERSTN_SYS_EVT							(PERSTN_SYS_EVT_MASK << PERSTN_SYS_EVT_SHIFT)
#define     DEVSLP_F_SYS_EVT_BIT				    (BIT11)
#define     DEVSLP_R_SYS_EVT_BIT				    (BIT12)
#define		PERSTN_F_SYS_EVT_BIT					(BIT14)
#define		PERSTN_R_SYS_EVT_BIT					(BIT15)
#define		VDT2_SYS_EVT_LEN						(13)
#define		VDT2_SYS_EVT_SHIFT						(16)
#define		VDT2_SYS_EVT_MASK						BIT_MASK(VDT2_SYS_EVT_LEN)
#define		VDT2_SYS_EVT							((U32)(VDT2_SYS_EVT_MASK << VDT2_SYS_EVT_SHIFT))

#define	R32_SYS1_INT_STS2							((0x08) >> 2)                                         //Default: 0x00000000
#define		TS_SYS_EVT_LEN							(6)
#define		TS_SYS_EVT_SHIFT						(0)
#define		TS_SYS_EVT_MASK							BIT_MASK(TS_SYS_EVT_LEN)
#define		TS_SYS_EVT								(TS_SYS_EVT_MASK << TS_SYS_EVT_SHIFT)
#define		PLL_SYS_EVT_LEN							(2)
#define		PLL_SYS_EVT_SHIFT						(6)
#define		PLL_SYS_EVT_MASK						BIT_MASK(PLL_SYS_EVT_LEN)
#define		PLL_SYS_EVT								(PLL_SYS_EVT_MASK << PLL_SYS_EVT_SHIFT)
#define		RTT0_SYS_EVT_BIT						(BIT8)
#define		RTT1_SYS_EVT_SHIFT						(16)
#define		RTT1_SYS_EVT_MASK						BIT_MASK(2)
#define		RTT1_SYS_EVT							(RTT1_SYS_EVT_MASK << RTT1_SYS_EVT_SHIFT)
#define		RTT1_SYS_EVT_0_BIT						(BIT16)
#define		RTT1_SYS_EVT_1_BIT						(BIT17)
#define		WDT_SYS_EVT_BIT							(BIT24)

#define	R32_SYS1_INT_STS3							((0x0C) >> 2)                                         //Default: 0x00000000
#define		COP1_PERR_EVT_LEN						(8)
#define		COP1_PERR_EVT_SHIFT						(0)
#define		COP1_PERR_EVT_MASK						BIT_MASK(COP1_PERR_EVT_LEN)
#define		COP1_PERR_EVT							((U32)(COP1_PERR_EVT_MASK << COP1_PERR_EVT_SHIFT))
#define		COP0_PERR_EVT_BIT						(BIT8)
#define		DMAC_ROM_EVT_BIT						(BIT16)
#define		XZIP_PER_EVT_BIT						(BIT24)

#define	R32_SYS1_INT_STS4							((0x10) >> 2)                                         //Default: 0x00000000
#define		IDPCRAM_PARITY_ERR_LEN					(4)
#define		IDPCRAM_PARITY_ERR_SHIFT				(0)
#define		IDPCRAM_PARITY_ERR_MASK					BIT_MASK(IDPCRAM_PARITY_ERR_LEN)
#define		IDPCRAM_PARITY_ERR						((U32)(IDPCRAM_PARITY_ERR_MASK << IDPCRAM_PARITY_ERR_SHIFT))
#define		FIPRAM_PARITY_ERR_LEN					(7)
#define		FIPRAM_PARITY_ERR_SHIFT					(8)
#define		FIPRAM_PARITY_ERR_MASK					BIT_MASK(FIPRAM_PARITY_ERR_LEN)
#define		FIPRAM_PARITY_ERR						((U32)(FIPRAM_PARITY_ERR_MASK << FIPRAM_PARITY_ERR_SHIFT))
#define		RSRAM_PARITY_ERR_LEN					(7)
#define		RSRAM_PARITY_ERR_SHIFT					(16)
#define		RSRAM_PARITY_ERR_MASK					BIT_MASK(RSRAM_PARITY_ERR_LEN)
#define		RSRAM_PARITY_ERR						((U32)(RSRAM_PARITY_ERR_MASK << RSRAM_PARITY_ERR_SHIFT))
#define		FLH_INT_WRAP_BIT						(BIT24)

#define	R32_SYS1_INT_STS5							((0x14) >> 2)                                         //Default: 0x00000000
#define		FIP_AXI_RSPERR_LEN						(10)
#define		FIP_AXI_RSPERR_SHIFT					(0)
#define		FIP_AXI_RSPERR_MASK						BIT_MASK(FIP_AXI_RSPERR_LEN)
#define		FIP_AXI_RSPERR							((U32)(FIP_AXI_RSPERR_MASK << FIP_AXI_RSPERR_SHIFT))
#define		RRAM_PERR_BIT							(BIT16)

#define	R32_SYS1_INT_STS6							((0x18) >> 2)                                         //Default: 0x00000000
#define		SEC_PERR_EVT_LEN						(19)
#define		SEC_PERR_EVT_SHIFT						(0)
#define		SEC_PERR_EVT_MASK						BIT_MASK(SEC_PERR_EVT_LEN)
#define		SEC_PERR_EVT							((U32)(SEC_PERR_EVT_MASK << SEC_PERR_EVT_SHIFT))

#define	R32_SYS1_INT_STS7							((0x1C) >> 2)                                         //Default: 0x00000000
#define		HOST_INT_BIT							(BIT0)
#define		PCIE_INT_BIT							(BIT1)
#define		COP1_EVT_BIT							(BIT2)
#define		ST3C_INT_BIT							(BIT3)
#define		BMU_EVT_BIT								(BIT4)
#define		MR_EVT_BIT								(BIT5)
#define		DMAC_EVT_LEN							(2)
#define		DMAC_EVT_SHIFT							(6)
#define		DMAC_EVT_MASK							BIT_MASK(DMAC_EVT_LEN)
#define		DMAC_EVT								(DMAC_EVT_MASK << DMAC_EVT_SHIFT)
#define		COP0_CMD_EVT_BIT						(BIT8)
#define		PIC_EVT_LEN								(6)
#define		PIC_EVT_SHIFT							(9)
#define		PIC_EVT_MASK							BIT_MASK(PIC_EVT_LEN)
#define		PIC_UART_EVT_BIT						(BIT9)
#define     SMBS_EVT_LEN                            (2)
#define     SMBS_EVT_SHIFT                          (10)
#define     SMBS_EVT_MASK                           BIT_MASK(SMBS_EVT_LEN)
#define     SMBS_EVT                                (SMBS_EVT_MASK << SMBS_EVT_SHIFT)
#define		PIC_SMBS_PD1_EVT_BIT					(BIT10)
#define		PIC_SMBM_PD1_EVT_BIT					(BIT11)
#define		PIC_I2C_PD1_EVT_BIT						(BIT12)
#define		PIC_PD1_SMB_START_EVT_BIT				(BIT13)
#define		SPI_CMD_INT_BIT							(BIT14)
#define		D2H_EVT_BIT								(BIT16)
#define		AHB_DERR_EVT_BIT						(BIT17)
#define		RS_EVT_BIT								(BIT18)
#define		TDBG_EVT_RP_BIT							(BIT19)
#define		CPU_EVT_BIT								(BIT20)
#define		ICC_EVT_BIT								(BIT21)
#define		AXI_TIMEOUT_EVT_BIT						(BIT24)
#define		AXIMON_DBUF2_ACCESS_EVT_BIT				(BIT25)
#define		AXIMON_DBUF3_ACCESS_EVT_BIT				(BIT26)
#define		AXIMON_CPU_ACCESS_EVT_BIT				(BIT27)
#define		AXIMON_AESH_ACCESS_EVT_BIT				(BIT28)
#define		COP0_MR_EVT_BIT							(BIT29)

#define	R32_SYS1_INT_STS8							((0x28) >> 2)
#define		MPHYRSTN_DET_SYS_EVT_SHIFT				(0)
#define		MPHYRSTN_DET_SYS_EVT_MASK				BIT_MASK(2)
#define		PERSTN_DET_SYS_EVT_SHIFT				(2)
#define		PERSTN_DET_SYS_EVT_MASK					BIT_MASK(2)
#define		CLKREQB_DET_SYS_EVT_SHIFT				(4)
#define		CLKREQB_DET_SYS_EVT_MASK				BIT_MASK(2)
#define		DEVSLP_DET_SYS_EVT_SHIFT				(6)
#define		DEVSLP_DET_SYS_EVT_MASK					BIT_MASK(2)

#define	R32_SYS1_ICC_EVT							((0x2C) >> 2)
#define		SET_ICC_EVT_BIT							(BIT0)

#define	R32_SYS1_INT_GROUP_STS						((0x30) >> 2)                                         //Default: 0x00000000
#define		ITC_COLLECT_SHIFT						(0)
#define		ITC_COLLECT_MASK						BIT_MASK(21)

#define	R32_SYS1_FIQ_MASK							((0x38) >> 2)                                         //Default: 0x00000000
#define		ITC_FIQ_MASK_SHIFT						(0)
#define		ITC_FIQ_MASK_MASK						(BIT_MASK(21))

#define	R32_SYS1_INT_IRQ_MASK						((0x40) >> 2)                                         //Default: 0x00000000
#define		ITC_IRQ_MASK_SHIFT						(0)
#define		ITC_IRQ_MASK_MASK						(BIT_MASK(21))

#define	R32_SYS1_HIGHEST_ID							((0x50) >> 2)                                         //Default: 0x00000000
#define		FIQ_HIGHEST_ID_SHIFT					(0)
#define		FIQ_FLAG_SHIFT							(5)
#define		FIQ_HIGHEST_ID_MASK						BIT_MASK(5)
#define		FIQ_FLAG_BIT							(BIT5)
#define		IRQ_HIGHEST_ID_SHIFT					(8)
#define		IRQ_FLAG_SHIFT							(13)
#define		IRQ_HIGHEST_ID_MASK						BIT_MASK(5)
#define		IRQ_FLAG_BIT							(BIT13)

#define	R32_SYS1_INT_PRIORITY_CFG0					((0x60) >> 2)                                         //Default: 0x03020100
#define	R8_SYS1_INT_PRIORITY_CFG0					((0x60) >> 0)
#define		ITC_PRI_CFG_SHIFT						(0)
#define		ITC_PRI_CFG_MASK						BIT_MASK(5)

#define	R32_SYS1_INT_PRIORITY_CFG1					((0x64) >> 2)                                         //Default: 0x07060504
#define	R8_SYS1_INT_PRIORITY_CFG1					((0x64) >> 0)

#define	R32_SYS1_INT_PRIORITY_CFG2					((0x68) >> 2)                                         //Default: 0x0B0A0908
#define	R8_SYS1_INT_PRIORITY_CFG2					((0x68) >> 0)

#define	R32_SYS1_INT_PRIORITY_CFG3					((0x6C) >> 2)                                         //Default: 0x0F0E0D0C
#define	R8_SYS1_INT_PRIORITY_CFG3					((0x6C) >> 0)

#define	R32_SYS1_INT_PRIORITY_CFG4					((0x70) >> 2)                                         //Default: 0x00001110
#define	R8_SYS1_INT_PRIORITY_CFG4					((0x70) >> 0)

#define	R32_SYS1_PARITY_ERROR_INJECTION				((0x80) >> 2)										  //Default: 0x00000000
#define		CR_DMAC_PERR_INJ_SHIFT					(8)
#define		CR_DMAC_PERR_INJ_MASK					BIT_MASK(2)
#define		CR_COP0_PERR_INJ_BIT					(BIT16)
#define		CR_RRAM_PERR_INJ_BIT					(BIT24)

#endif /* _SYS_PD1_REG_5013_H_ */

