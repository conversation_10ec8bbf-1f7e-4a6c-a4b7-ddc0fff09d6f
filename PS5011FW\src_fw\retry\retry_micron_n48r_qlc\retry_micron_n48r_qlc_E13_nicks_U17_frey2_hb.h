#ifndef _RETRY_MICRON_N48R_QLC_E13_NICKS_HB_H
#define _RETRY_MICRON_N48R_QLC_E13_NICKS_HB_H

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */

#include "env.h"

#if ((PS5013_EN || PS5017_EN) && (FLASH_N48R_QLC == FW_CATEGORY_FLASH) && (CUSTOMER_MICRON_NICKS == FW_CATEGORY_CUSTOMER))
#include "retry/retry_api.h"	//for HBIT_RETRY_MICRON_SET_GET_STEP_NUM & HBIT_RETRY_MICRON_FEA_DATA_NUM

/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */

#define HBIT_RETRY_MICRON_ORI_STEP_NUM				(9)
#define HBIT_RETRY_MICRON_ARC_STEP_NUM				(4)
#define HBIT_RETRY_MICRON_ACRR_STEP_NUM				(3)
#define HBIT_RETRY_MICRON_READ_STEP_NUM				(1) // normal
#define HBIT_RETRY_MICRON_ACRR_START_STEP			(HBIT_RETRY_MICRON_READ_STEP_NUM)
#define HBIT_RETRY_MICRON_ARC_START_STEP			(HBIT_RETRY_MICRON_ACRR_START_STEP + HBIT_RETRY_MICRON_ACRR_STEP_NUM)
#define HB_RETRY_MICRON_ALL_NUM						(HBIT_RETRY_MICRON_ORI_STEP_NUM)
#define M_CHECK_IF_HB_STEP_IN_ACRR_RANGE(MACRO_HB_STEP)	(((HBIT_RETRY_MICRON_ACRR_START_STEP <= (MACRO_HB_STEP)) && ((MACRO_HB_STEP) < HBIT_RETRY_MICRON_ARC_START_STEP)) ? TRUE : FALSE)
#define M_GET_ACRR_STEP_FROM_HB_STEP(MACRO_HB_STEP)		(MACRO_HB_STEP - HBIT_RETRY_MICRON_ACRR_START_STEP)

#define HB_RETRY_MICRON_READ_OFFSET_STEP_NUM		(2)

#define HBIT_RETRY_MICRON_TLC_FEA_CNT				(HB_RETRY_MICRON_ALL_NUM)
#define HBIT_RETRY_MICRON_SLC_FEA_CNT				(HB_RETRY_MICRON_ALL_NUM)

#define HB__RETRY_MICRON_ORI_STEP					(0)
#define HB__RETRY_MICRON_READ_OFFSET_STEP			(HB__RETRY_MICRON_ORI_STEP + HBIT_RETRY_MICRON_ORI_STEP_NUM)
#define HBIT_RETRY_MICRON_LPI_STEP					(HB__RETRY_MICRON_READ_OFFSET_STEP + HB_RETRY_MICRON_READ_OFFSET_STEP_NUM)

#define HBIT_RETRY_MICRON_ARC_STEP					(HBIT_RETRY_MICRON_ORI_STEP_NUM + HBIT_RETRY_MICRON_ACRR_STEP_NUM)

/*
 * ---------------------------------------------------------------------------------------------------
 *  enum
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */


#endif /*((PS5013_EN) && (FLASH_N48R_QLC == FW_CATEGORY_FLASH) && (CUSTOMER_MICRON_NICKS == FW_CATEGORY_CUSTOMER)) */

#endif /* _RETRY_MICRON_N48R_QLC_E13_NICKS_HB_H */
