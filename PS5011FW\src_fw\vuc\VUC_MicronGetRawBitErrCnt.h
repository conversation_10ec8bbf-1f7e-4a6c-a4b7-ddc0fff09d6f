#ifndef _VUC_MICRONGETRAWBITERRCNT_H_
#define _VUC_MICRONGETRAWBITERRCNT_H_
#include "aom/aom_api.h"

#define VUC_MICRON_GET_ERR_BIT_HEADER_LENGTH (12)

#define VUC_MICRON_LDPC_MODE_RETRY_TIMES	(2)
#define VUC_MICRON_READ_MODE_RETRY_TIMES	(2)
typedef struct {
	U16 uwChannel;
	U16 uwCE;
	U16 uwLUN;
	U16 uwBlock;
	U16 uwPage;
	U16 uwCodeWord;
} GetRawBitErrInputData_t;

typedef struct {
	U8 ubResponseHeaderFormatVersion;
	U8 ubResponseDataFotmatVersion;
	U16 uwCMDClass;
	U16 uwCMDCode;
	U16 uwStatus;
	U32 ulDataPayloadSize;
} GetRawBitErrResponseHEADER_t;

#if (VUC_MICRON_NAND_VS_COMMANDS_EN && IM_N28)
extern U32 gMicronVUCReadCallBackAddr;
extern U32 gMicronVUCDirectedCieOutInfo;
void VUCMicronRead_Callback();
AOM_VUC_3 void VUCMicronGetRawBitRead(U32 ulPhyAdr, U32 ulIFSA, U8 ubIsSLC);
AOM_VUC_3 void VUCMicronGetRawBitErrorCnt(U32 ulInputPayloadAddr, U32 ulResponsePayloadAddr);
#endif /* (VUC_MICRON_NAND_VS_COMMANDS_EN) */

#endif /* _VUC_MICRONGETRAWBITERRCNT_H_ */
