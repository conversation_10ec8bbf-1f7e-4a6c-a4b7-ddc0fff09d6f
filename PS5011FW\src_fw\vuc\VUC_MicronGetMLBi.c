#include "VUC_MicronResponse.h"
#include "hal/dmac/dmac_pop_cmd.h"
#include "vuc/VUC_api.h"

#if (VUC_MICRON_NAND_VS_COMMANDS_EN)
void VUCMicronGetMLBi(U32 ulInputPayloadAddr, U32 ulPayloadAddr)
{
	GetMLBiInputData_t *pInputData;
	GetMLBiResponseHeader_t *pResponseHeader;
	GetMLBiResponsePayload_t *pResponsePayload;
	DMACParam_t DMACParam;
	pResponseHeader = (GetMLBiResponseHeader_t *)ulPayloadAddr;
	pResponsePayload = (GetMLBiResponsePayload_t *)(ulPayloadAddr + VUC_MICRON_RESPONSE_HEADER_SIZE);
	pInputData = (GetMLBiInputData_t *)(ulInputPayloadAddr + VUC_MICRON_GET_MLBI_HEADER_LENGTH);

	DMACParam.DMACSetValue.ulDestAddr = ulPayloadAddr;
	DMACParam.DMACSetValue.ulLowValue = 0;
	DMACParam.DMACSetValue.ulHighValue = 0;
	DMACParam.DMACSetValue.ul32ByteNum = SIZE_IN_32B(DEF_KB(4));
	DMACParam.DMACSetValue.btValidate = FALSE;
	gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
	DMACSetValue(&DMACParam, gulDMACDirectWaitDone_Callback, 0);
	while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
		DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
	}
	pResponseHeader->ubResponseHeaderFormatVersion = VUC_MICRON_RESPONSE_HEADER_FORMAT_VERSION_0;
	pResponseHeader->ubResponseDataFotmatVersion = VUC_MICRON_RESPONSE_FORMAT_BIN;
	pResponseHeader->uwCMDClass = VUC_MICRON_NAND_VS_COMMANDS;
	pResponseHeader->uwCMDCode = VUC_MICRON_GET_MLBI;
	pResponseHeader->uwStatus = 0;
	pResponseHeader->ulDataPayloadSize = VUC_MICRON_GET_MLBI_PAYLOAD_LENGTH;
	VUCMicronFlaGetMLBi((U8)pInputData->uwCH, (U8)pInputData->uwCE, (U8)pInputData->uwLUN, pInputData->uwTrimRegAddr, (U8 *)&pResponsePayload->uwTrimRegData);
	pResponsePayload->uwCH = pInputData->uwCH;
	pResponsePayload->uwCE = pInputData->uwCE;
	pResponsePayload->uwLUN = pInputData->uwLUN;
	pResponsePayload->uwTrimRegAddr = pInputData->uwTrimRegAddr;
	UartPrintf("\n GetMLBi %x CH:%x CE:%x LUN:%x data:%x ", pResponsePayload->uwTrimRegAddr, pResponsePayload->uwCH, pResponsePayload->uwCE, pResponsePayload->uwLUN, pResponsePayload->uwTrimRegData);
}
#endif /* VUC_MICRON_NAND_VS_COMMANDS_EN */
