#include "setup.h"
#include "typedef.h"
#include "queue/read_queue.h"
#include "debug/debug.h"

#define RQ_DEBUG_UART_EN (FALSE)

#if (RDT_MODE_EN) && (!PS5017_EN)
#include "rdt/rdt.h"
DBUF_CODEBANK_SECTION ReadQueueInfo_t gLAQI, gPRQI, gSyncCmdQI, gReadVerifyRQInfo;
DBUF_CODEBANK_SECTION ReadQueueInfo_t *gpRQIMgr[READ_QUEUE_NUM] = {&gLAQI, &gPRQI, &gReadVerifyRQInfo, &gSyncCmdQI};
#else /* (RDT_MODE_EN) && (!PS5017_EN) */
ReadQueueInfo_t gLAQI, gPRQI, gSyncCmdQI, gReadVerifyRQInfo;
ReadQueueInfo_t *gpRQIMgr[READ_QUEUE_NUM] = {&gLAQI, &gPRQI, &gReadVerifyRQInfo, &gSyncCmdQI};
#endif /* (RDT_MODE_EN) && (!PS5017_EN) */

void InitRQ(void)
{
	U8 ubRQIIndex;
	U8 ubRQIndex;
	ReadQueue_t *pReadQueue = NULL;

	for (ubRQIIndex = 0; ubRQIIndex < READ_QUEUE_NUM; ubRQIIndex++) {
		for (ubRQIndex = 0; ubRQIndex < RQ_NUM; ubRQIndex++) {
			pReadQueue = &(gpRQIMgr[ubRQIIndex]->Queue[ubRQIndex]);
			if (SYNC_CMD_QUEUE_ID == ubRQIIndex) {
				if (ubRQIndex >= RQ_SYNC_READ_RQ_NUM) {
					pReadQueue->ubNext = ubRQIndex;
					pReadQueue->ubPrevious 	= ubRQIndex;
				}
				else {
					pReadQueue->ubNext = ((ubRQIndex + 1) == RQ_SYNC_READ_RQ_NUM) ? 0 : (ubRQIndex + 1);
					pReadQueue->ubPrevious 	= (ubRQIndex == 0) ? (RQ_SYNC_READ_RQ_NUM - 1) : (ubRQIndex - 1);
				}
			}
			else {
				pReadQueue->ubNext = ((ubRQIndex + 1) == RQ_NUM) ? 0 : (ubRQIndex + 1);
				pReadQueue->ubPrevious 	= (ubRQIndex == 0) ? (RQ_NUM - 1) : (ubRQIndex - 1);
			}
			pReadQueue->ubStatePhase = INIT_STATE;
			pReadQueue->ulLCA = INIT_LCA;
			pReadQueue->ulSHResult.ulPCA = INIT_PCA;
			pReadQueue->uwTagID = INIT_TAG_ID;
			pReadQueue->uwWCQOffset = INIT_WCQ_OFFSET;
			pReadQueue->RCQOffset = INIT_RCQ_OFFSET;
			pReadQueue->uwSrcPBOffset = 0xFFFF;
			pReadQueue->btHitPLB = FALSE;
			pReadQueue->btZeroData = FALSE;
			pReadQueue->btNeedFreeQuota	= FALSE;
		}
		gpRQIMgr[ubRQIIndex]->ubNum = 0;
		gpRQIMgr[ubRQIIndex]->ubHeader = 0;
		gpRQIMgr[ubRQIIndex]->ubTail	 = 0;
	}
}

void AddRQ(U8 ubQueueID, U8 ubLBID, U16 uwLBOffset, U32 ulLCA, U16 uwWCQOffset)
{
	U8 ubAddQueueIdx = gpRQIMgr[ubQueueID]->ubTail;
	ReadQueue_t *pRQ = NULL;
	//assert
	M_FW_ASSERT(ASSERT_QUEUE_0x0800, gpRQIMgr[ubQueueID]->ubNum < RQ_NUM);

	if (RQ_DEBUG_UART_EN) {
		M_UART(UNDEFINE_, "[LA] AddRQ Index %x, LCA %x, LBOFST %x \n", ubAddQueueIdx, ulLCA, uwLBOffset);
	}

	pRQ = &gpRQIMgr[ubQueueID]->Queue[ubAddQueueIdx];
	pRQ->ubLogicalBufferID = ubLBID;
	pRQ->uwLogicalBufferOffset = uwLBOffset;
	pRQ->ubStatePhase = INIT_STATE;
	pRQ->ulLCA = ulLCA;
	pRQ->uwTagID = INIT_TAG_ID;
	pRQ->uwWCQOffset = uwWCQOffset;
	pRQ->RCQOffset = INIT_RCQ_OFFSET;
	pRQ->ubWCQBarrier = FALSE;
	pRQ->uwSrcPBOffset = RQ_INVALID_PB_OFFSET;
	pRQ->btHitPLB = FALSE;
	pRQ->btZeroData = FALSE;
	pRQ->btNeedFreeQuota	= FALSE;
	pRQ->ulSHResult.ulPCA = INIT_PCA;
	pRQ->ubLoc = LOC_DEFAULT;
	pRQ->ulE3D4K	= RQ_DEFAULT_E3D4K;

	gpRQIMgr[ubQueueID]->ubTail = pRQ->ubNext;
	++gpRQIMgr[ubQueueID]->ubNum;

	if (RQ_DEBUG_UART_EN) {
		M_UART(UNDEFINE_, "[LA] Num %x, Tail %x \n", gpRQIMgr[ubQueueID]->ubNum, gpRQIMgr[ubQueueID]->ubTail);
	}
}

void DeleteRQ(U8 ubQueueID, U8 ubIdx)
{
	U8 ubCurrentIndex = ubIdx;
	//assert
	M_FW_ASSERT(ASSERT_QUEUE_0x0801, gpRQIMgr[ubQueueID]->ubNum != 0);

	//assert
	//while(ubCurrentIndex == gRQI.ubTail);
	if (RQ_DEBUG_UART_EN) {
		M_UART(UNDEFINE_, "[LA] DeleteRQ Index %x, LCA %x, LBOFST %x, WCQBarrier %x \n"
			, ubIdx
			, gpRQIMgr[ubQueueID]->Queue[ubCurrentIndex].ulLCA
			, gpRQIMgr[ubQueueID]->Queue[ubCurrentIndex].uwLogicalBufferOffset
			, gpRQIMgr[ubQueueID]->Queue[ubCurrentIndex].ubWCQBarrier);
	}

	if (gpRQIMgr[ubQueueID]->Queue[ubCurrentIndex].ubWCQBarrier == FALSE) {
		M_FW_ASSERT(ASSERT_QUEUE_0x0802, gpRQIMgr[ubQueueID]->Queue[ubCurrentIndex].uwLogicalBufferOffset != 0xFFFF);
	}

	/*gpRQIMgr[ubQueueID]->Queue[ubCurrentIndex].ubLogicalBufferID = INIT_LB_ID;
	gpRQIMgr[ubQueueID]->Queue[ubCurrentIndex].uwLogicalBufferOffset = INIT_LB_OFFSET;
	gpRQIMgr[ubQueueID]->Queue[ubCurrentIndex].ubStatePhase = INIT_STATE;
	gpRQIMgr[ubQueueID]->Queue[ubCurrentIndex].uwSrcPBOffset = 0xFFFF;
	gpRQIMgr[ubQueueID]->Queue[ubCurrentIndex].btHitPLB = FALSE;
	gpRQIMgr[ubQueueID]->Queue[ubCurrentIndex].btZeroData = FALSE;
	gpRQIMgr[ubQueueID]->Queue[ubCurrentIndex].btNeedFreeQuota	= FALSE;
	*/
	if (ubCurrentIndex == gpRQIMgr[ubQueueID]->ubHeader) {	//header, remove header and shift header ptr to next
		gpRQIMgr[ubQueueID]->ubHeader = gpRQIMgr[ubQueueID]->Queue[ubCurrentIndex].ubNext;
	}
	else if (ubCurrentIndex == gpRQIMgr[ubQueueID]->Queue[gpRQIMgr[ubQueueID]->ubTail].ubPrevious) { //tail-1, remove tail-1 and shift tail ptr to previous
		gpRQIMgr[ubQueueID]->ubTail = ubCurrentIndex;
	}
	else {
		gpRQIMgr[ubQueueID]->Queue[gpRQIMgr[ubQueueID]->Queue[ubCurrentIndex].ubPrevious].ubNext = gpRQIMgr[ubQueueID]->Queue[ubCurrentIndex].ubNext;
		gpRQIMgr[ubQueueID]->Queue[gpRQIMgr[ubQueueID]->Queue[ubCurrentIndex].ubNext].ubPrevious = gpRQIMgr[ubQueueID]->Queue[ubCurrentIndex].ubPrevious;
		gpRQIMgr[ubQueueID]->Queue[ubCurrentIndex].ubPrevious = gpRQIMgr[ubQueueID]->Queue[gpRQIMgr[ubQueueID]->ubTail].ubPrevious;
		gpRQIMgr[ubQueueID]->Queue[gpRQIMgr[ubQueueID]->Queue[gpRQIMgr[ubQueueID]->ubTail].ubPrevious].ubNext = ubCurrentIndex;
		gpRQIMgr[ubQueueID]->Queue[gpRQIMgr[ubQueueID]->ubTail].ubPrevious = ubCurrentIndex;
		gpRQIMgr[ubQueueID]->Queue[ubCurrentIndex].ubNext = gpRQIMgr[ubQueueID]->ubTail;
		gpRQIMgr[ubQueueID]->ubTail = ubCurrentIndex;
	}

	gpRQIMgr[ubQueueID]->ubNum --;

	if (RQ_DEBUG_UART_EN) {
		M_UART(UNDEFINE_, "[LA] Num %x\n", gpRQIMgr[ubQueueID]->ubNum);
	}

}

void AddSpecialRQ(U8 ubIdx, U32 ulLCA)
{
	ReadQueue_t *pReadQueue = &(gpRQIMgr[SYNC_CMD_QUEUE_ID]->Queue[ubIdx]);
	pReadQueue->ubLogicalBufferID = INIT_LB_ID;
	pReadQueue->uwLogicalBufferOffset = INIT_LB_OFFSET;
	pReadQueue->ubStatePhase = INIT_STATE;
	pReadQueue->ulLCA = ulLCA;
	pReadQueue->uwTagID = INIT_TAG_ID;
	pReadQueue->uwWCQOffset = INVALID_WCQ_OFFSET;
	pReadQueue->RCQOffset = INIT_RCQ_OFFSET;
	pReadQueue->ubWCQBarrier = 0;
	pReadQueue->uwSrcPBOffset = RQ_INVALID_PB_OFFSET;
	pReadQueue->btHitPLB = FALSE;
	pReadQueue->btZeroData = FALSE;
	pReadQueue->btNeedFreeQuota	= FALSE;
	pReadQueue->ulSHResult.ulPCA = INIT_PCA;
	pReadQueue->ubLoc = LOC_DEFAULT;
	pReadQueue->ulE3D4K	= RQ_DEFAULT_E3D4K;
}

U8 SearchRQ(U8 ubQueueID, U8 ubLogicalBufferID, U16 uwLogicalBufferOffset)
{
	U8 ubResult = FALSE;
	U8 ubCurrentIndex = gpRQIMgr[ubQueueID]->ubHeader;
	U8 ubNum = gpRQIMgr[ubQueueID]->ubNum;
	while (ubNum) {
		if ((gpRQIMgr[ubQueueID]->Queue[ubCurrentIndex].uwLogicalBufferOffset == uwLogicalBufferOffset)
			&& (gpRQIMgr[ubQueueID]->Queue[ubCurrentIndex].ubLogicalBufferID == ubLogicalBufferID)) {
			ubResult = TRUE;
			break;
		}
		ubCurrentIndex = gpRQIMgr[ubQueueID]->Queue[ubCurrentIndex].ubNext;
		ubNum--;
	}

	// Return Not Only Search Result But Also Index, If Search Not Found Return 0xFF Index
	if (ubResult == FALSE) {
		ubCurrentIndex = INVALID_RQ_INDEX;
	}

	return ubCurrentIndex;
}

void AddRQBarrier(U8 ubQueueID)
{
	U8 ubAddQueueIdx = gpRQIMgr[ubQueueID]->ubTail;

	M_FW_ASSERT(ASSERT_QUEUE_0x0803, gpRQIMgr[ubQueueID]->ubNum < RQ_NUM);
	ReadQueue_t *pRQ = NULL;
	if (RQ_DEBUG_UART_EN) {
		M_UART(UNDEFINE_, "[LA] AddRQBarrier Index %x \n", ubAddQueueIdx);
	}
	pRQ = &gpRQIMgr[ubQueueID]->Queue[ubAddQueueIdx];
	pRQ->ubLogicalBufferID = RQ_INVALID_LB_ID;
	pRQ->uwLogicalBufferOffset = RQ_INVALID_LB_OFFSET;
	pRQ->ubStatePhase = INIT_STATE;
	pRQ->ulLCA = 0;
	pRQ->uwTagID = INIT_TAG_ID;
	pRQ->uwWCQOffset = INVALID_WCQ_OFFSET;
	pRQ->RCQOffset = INIT_RCQ_OFFSET;
	pRQ->ubWCQBarrier = TRUE;
	pRQ->ubLoc = LOC_DEFAULT;
	gpRQIMgr[ubQueueID]->ubTail = pRQ->ubNext;
	++gpRQIMgr[ubQueueID]->ubNum;

}
U8 GetRQNum(U8 ubQueueId)
{
	return gpRQIMgr[ubQueueId]->ubNum;
}
