#ifndef _VUC_MicronGetBEC_H_
#define _VUC_MicronGetBEC_H_
#include "aom/aom_api.h"

#define MICRON_VUC_GET_BEC_COMMAND_CLASS	(0x04)
#define MICRON_VUC_GET_BEC_COMMAND_CODE		(0x38)
#if IM_N48R
#define MICRON_VUC_BEC_PAYLOAD_SIZE_PER_DIE	(16 * 4)//16 value * 4 bytes
#define MICRON_VUC_BEC_PAYLOAD_SIZE			(32 * MICRON_VUC_BEC_PAYLOAD_SIZE_PER_DIE)//32 die * 16 value * 4 bytes 
#else /* IM_N48R */
#define MICRON_VUC_BEC_PAYLOAD_SIZE_PER_DIE	(87 * 4)//87 value * 4 bytes
#define MICRON_VUC_BEC_PAYLOAD_SIZE			(16 * MICRON_VUC_BEC_PAYLOAD_SIZE_PER_DIE)//16 die * 87 value * 4 bytes 
#endif/* IM_N48R */

typedef struct {
	U8 ubHeaderFormatVersion;
	U8 ubDataFotmatVersion;
	U16 uwCMDClass;
	U16 uwCMDCode;
	U16 uwReserved;
	U32 ulDataPayloadSize;
} GETBECHEADER_t;

typedef struct {
	U8 ubResponseHeaderFormatVersion;
	U8 ubResponseDataFotmatVersion;
	U16 uwCMDClass;
	U16 uwCMDCode;
	U16 uwStatus;
	U32 ulDataPayloadSize;
} GETBECResponseHEADER_t;

AOM_VUC_3 void VUCMicronGetBEC (U32 ulPayloadAddr);

#endif /* _VUC_MicronGetBEC_H_ */
