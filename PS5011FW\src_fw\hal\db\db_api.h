#ifndef _DB_API_H_
#define _DB_API_H_

#include "setup.h"
#include "typedef.h"
#include "db_reg.h"
#if USE_FW_DB
#include "debug/debug.h"
#endif
#include "aom/aom_api.h"
#if VS_SIM_EN
#include "db/DbQueueInterface.h"
#include "ip/APU/APU.h"
#endif /* VS_SIM_EN */
//Terry 0529
#if (GEN_CQ_EN)
#include "host/host.h"
#endif /* FPGA_BOARD == FPGA_USE_V7 */
/***************** DB Define ********************/

/****************** Doorbell Queue Arrangment ******************/

// Doorbell Queue Data Length
#define DB_DMAC_HIGH_SQ_DATA_SIZE_LOG       (DATA_SIZE_32B_LOG)
#define DB_DMAC_HIGH_CQ_DATA_SIZE_LOG       (DATA_SIZE_8B_LOG)
#define DB_DMAC_NORM_SQ_DATA_SIZE_LOG       (DATA_SIZE_32B_LOG)
#define DB_DMAC_NORM_CQ_DATA_SIZE_LOG       (DATA_SIZE_8B_LOG)

#define DB_BMU_NOBLK_SQ_DATA_SIZE_LOG       (DATA_SIZE_16B_LOG)
#define DB_BMU_BLK_SQ_DATA_SIZE_LOG         (DATA_SIZE_16B_LOG)
#define DB_BMU_CQ_DATA_SIZE_LOG             (DATA_SIZE_16B_LOG)
#define DB_BMU_WR_CQ_DATA_SIZE_LOG          (DATA_SIZE_16B_LOG)
#define DB_BMU_WR_CQ_2_DATA_SIZE_LOG        (DATA_SIZE_16B_LOG)
#define DB_BMU_SEARCH_SQ_DATA_SIZE_LOG      (DATA_SIZE_16B_LOG)

#define DB_APU_WR_SQ_DATA_SIZE_LOG          (DATA_SIZE_16B_LOG)
#define DB_APU_CMPL_SQ_DATA_SIZE_LOG        (DATA_SIZE_8B_LOG)
#define DB_APU_TD_CQ_DATA_SIZE_LOG          (DATA_SIZE_2B_LOG)
#define DB_APU_CMD_CQ_DATA_SIZE_LOG         (DATA_SIZE_16B_LOG)
#define DB_APU_RD_CQ_DATA_SIZE_LOG          (DATA_SIZE_16B_LOG)

#define DB_XZIP_SQ_DATA_SIZE_LOG            (DATA_SIZE_8B_LOG)
#define DB_XZIP_CQ_DATA_SIZE_LOG            (DATA_SIZE_8B_LOG)

#define DB_COP0_WR_SQ_DATA_SIZE_LOG         (DATA_SIZE_8B_LOG)
#define DB_COP0_RD_SQ_DATA_SIZE_LOG         (DATA_SIZE_8B_LOG)
#define DB_COP0_CQ_DATA_SIZE_LOG            (DATA_SIZE_8B_LOG)

#define DB_COP1_SQ_DATA_SIZE_LOG            (DATA_SIZE_8B_LOG)
#define DB_COP1_CQ_DATA_SIZE_LOG            (DATA_SIZE_8B_LOG)

#if PS5021_EN
#define DB_FLH_MSG_CQ_DATA_SIZE_LOG         (DATA_SIZE_16B_LOG)
#else /* PS5021_EN */
#define DB_FLH_MSG_CQ_DATA_SIZE_LOG         (DATA_SIZE_8B_LOG)
#endif /* PS5021_EN */

#define DB_RS_MSG_CQ_DATA_SIZE_LOG          (DATA_SIZE_8B_LOG)

#define DB_PIC_CQ_DATA_SIZE_LOG             (DATA_SIZE_8B_LOG) // 8B x 5 = 40B
#define DB_LOG_MSG_CQ_DATA_SIZE_LOG         (DATA_SIZE_8B_LOG)
#define DB_ERR_MSG_CQ_DATA_SIZE_LOG         (DATA_SIZE_8B_LOG)

#define DB_FW_SQ0_DATA_SIZE_LOG             (DATA_SIZE_16B_LOG)
#define DB_FW_CQ0_DATA_SIZE_LOG             (DATA_SIZE_16B_LOG)
#define DB_FW_SQ1_DATA_SIZE_LOG             (DATA_SIZE_16B_LOG)
#define DB_FW_CQ1_DATA_SIZE_LOG             (DATA_SIZE_16B_LOG)
#define DB_FW_SQ2_DATA_SIZE_LOG             (DATA_SIZE_16B_LOG)
#define DB_FW_CQ2_DATA_SIZE_LOG             (DATA_SIZE_16B_LOG)
#define DB_FW_SQ3_DATA_SIZE_LOG             (DATA_SIZE_16B_LOG)
#define DB_FW_CQ3_DATA_SIZE_LOG             (DATA_SIZE_16B_LOG)

#define DB_BMU_NOBLK_CQ_DATA_SIZE_LOG       (DATA_SIZE_16B_LOG)//non blocking CQ
#define DB_COP0_SQ1_DATA_SIZE_LOG           (DATA_SIZE_8B_LOG)

// Doorbell Queue Depth
#define DB_DMAC_HIGH_SQ_DEPTH           (32)
#define DB_DMAC_HIGH_CQ_DEPTH           (32)
#define DB_DMAC_NORM_SQ_DEPTH           (32)
#define DB_DMAC_NORM_CQ_DEPTH           (32)

#define DB_BMU_NOBLK_SQ_DEPTH           (4)
#define DB_BMU_BLK_SQ_DEPTH             (4)
#define DB_BMU_CQ_DEPTH                 (64)
#define DB_BMU_WR_CQ_DEPTH              (64)
#define DB_BMU_WR_CQ_2_DEPTH            (4) // UFS use?
#define DB_BMU_SEARCH_SQ_DEPTH          (4)

#define DB_APU_WR_SQ_DEPTH              (2)
#define DB_APU_CMPL_SQ_DEPTH            (32)
#define DB_APU_TD_CQ_DEPTH              (16)
#define DB_APU_CMD_CQ_DEPTH             (32)
#if PS5021_EN
#define DB_APU_RD_CQ_DEPTH              (32) // max 511
#else /* PS5021_EN */
#define DB_APU_RD_CQ_DEPTH              (32)
#endif /* PS5021_EN */

#define DB_XZIP_SQ_DEPTH                (4)
#define DB_XZIP_CQ_DEPTH                (4)

#if PS5021_EN
#define DB_COP0_RD_SQ_DEPTH             (128)
#else /* PS5021_EN */
#define DB_COP0_RD_SQ_DEPTH             (128)
#endif /* PS5021_EN */
#define DB_COP0_WR_SQ_DEPTH             (128)//(250)//(4)
#define DB_COP0_CQ_DEPTH                (32)//(250)//(2)

#define DB_COP1_SQ_DEPTH                (64)//(250)//(4)
#define DB_COP1_CQ_DEPTH                (64)//(250)//(4)

#define DB_FLH_MSG_CQ_DEPTH             (4)
#define DB_RS_MSG_CQ_DEPTH              (4)

#define DB_PIC_CQ_DEPTH                 (4)
#define DB_LOG_MSG_CQ_DEPTH             (4)
#define DB_ERR_MSG_CQ_DEPTH             (4)

#define DB_FW_SQ0_DEPTH                 (4)
#define DB_FW_CQ0_DEPTH                 (4)
#define DB_FW_SQ1_DEPTH                 (4)
#define DB_FW_CQ1_DEPTH                 (4)//for Gen RCQ used (2)
#define DB_FW_SQ2_DEPTH                 (4)
#define DB_FW_CQ2_DEPTH                 (4)
#define DB_FW_SQ3_DEPTH                 (4)
#define DB_FW_CQ3_DEPTH                 (4)

#define DB_BMU_NOBLK_CQ_DEPTH           (4)//non blocking CQ
#define DB_COP0_SQ1_DEPTH             	(32)//(250)//(4)

// Doorbell Queue Depth Mask
#define DB_DMAC_HIGH_SQ_MASK			(DB_DMAC_HIGH_SQ_DEPTH - 1)
#define DB_DMAC_HIGH_CQ_MASK			(DB_DMAC_HIGH_CQ_DEPTH - 1)
#define DB_DMAC_NORM_SQ_MASK			(DB_DMAC_NORM_SQ_DEPTH - 1)
#define DB_DMAC_NORM_CQ_MASK			(DB_DMAC_NORM_CQ_DEPTH - 1)

#define DB_BMU_NOBLK_SQ_MASK			(DB_BMU_NOBLK_SQ_DEPTH - 1)
#define DB_BMU_BLK_SQ_MASK				(DB_BMU_BLK_SQ_DEPTH - 1)
#define DB_BMU_CQ_MASK					(DB_BMU_CQ_DEPTH - 1)
#define DB_BMU_WR_CQ_MASK				(DB_BMU_WR_CQ_DEPTH - 1)
#define DB_BMU_WR_CQ_2_MASK				(DB_BMU_WR_CQ_2_DEPTH - 1)
#define DB_BMU_SEARCH_SQ_MASK			(DB_BMU_SEARCH_SQ_DEPTH - 1)

#define DB_APU_WR_SQ_MASK				(DB_APU_WR_SQ_DEPTH - 1)
#define DB_APU_CMPL_SQ_MASK				(DB_APU_CMPL_SQ_DEPTH - 1)
#define DB_APU_TD_CQ_MASK				(DB_APU_TD_CQ_DEPTH - 1)
#define DB_APU_CMD_CQ_MASK				(DB_APU_CMD_CQ_DEPTH - 1)
#define DB_APU_RD_CQ_MASK				(DB_APU_RD_CQ_DEPTH - 1)

#define DB_XZIP_SQ_MASK					(DB_XZIP_SQ_DEPTH - 1)
#define DB_XZIP_CQ_MASK					(DB_XZIP_CQ_DEPTH - 1)


#define DB_COP0_RD_SQ_MASK				(DB_COP0_RD_SQ_DEPTH - 1)
#define DB_COP0_WR_SQ_MASK				(DB_COP0_WR_SQ_DEPTH - 1)
#define DB_COP0_CQ_MASK					(DB_COP0_CQ_DEPTH - 1)

#define DB_COP1_SQ_MASK					(DB_COP1_SQ_DEPTH - 1)
#define DB_COP1_CQ_MASK					(DB_COP1_CQ_DEPTH - 1)

#define DB_FLH_MSG_CQ_MASK				(DB_FLH_MSG_CQ_DEPTH - 1)
#define DB_RS_MSG_CQ_MASK				(DB_RS_MSG_CQ_DEPTH - 1)

#define DB_PIC_CQ_MASK					(DB_PIC_CQ_DEPTH - 1)
#define DB_LOG_MSG_CQ_MASK				(DB_LOG_MSG_CQ_DEPTH - 1)
#define DB_ERR_MSG_CQ_MASK				(DB_ERR_MSG_CQ_DEPTH - 1)

#define DB_FW_SQ0_MASK					(DB_FW_SQ0_DEPTH - 1)
#define DB_FW_CQ0_MASK					(DB_FW_CQ0_DEPTH - 1)
#define DB_FW_SQ1_MASK					(DB_FW_SQ1_DEPTH - 1)
#define DB_FW_CQ1_MASK					(DB_FW_CQ1_DEPTH - 1)
#define DB_FW_SQ2_MASK					(DB_FW_SQ2_DEPTH - 1)
#define DB_FW_CQ2_MASK					(DB_FW_CQ2_DEPTH - 1)
#define DB_FW_SQ3_MASK					(DB_FW_SQ3_DEPTH - 1)
#define DB_FW_CQ3_MASK					(DB_FW_CQ3_DEPTH - 1)

#define DB_BMU_NOBLK_CQ_MASK			(DB_BMU_NOBLK_CQ_DEPTH - 1)
#define DB_COP0_SQ1_MASK				(DB_COP0_SQ1_DEPTH - 1)

// Doorbell Queue Space (unit:BYTE)
#define DB_DMAC_HIGH_SQ_SIZE          (DB_DMAC_HIGH_SQ_DEPTH	<<	DB_DMAC_HIGH_SQ_DATA_SIZE_LOG)
#define DB_DMAC_HIGH_CQ_SIZE          (DB_DMAC_HIGH_CQ_DEPTH	<<	DB_DMAC_HIGH_CQ_DATA_SIZE_LOG)
#define DB_DMAC_NORM_SQ_SIZE          (DB_DMAC_NORM_SQ_DEPTH	<<	DB_DMAC_NORM_SQ_DATA_SIZE_LOG)
#define DB_DMAC_NORM_CQ_SIZE          (DB_DMAC_NORM_CQ_DEPTH	<<	DB_DMAC_NORM_CQ_DATA_SIZE_LOG)

#define DB_BMU_NOBLK_SQ_SIZE          (DB_BMU_NOBLK_SQ_DEPTH	<<	DB_BMU_NOBLK_SQ_DATA_SIZE_LOG)
#define DB_BMU_BLK_SQ_SIZE            (DB_BMU_BLK_SQ_DEPTH		<<	DB_BMU_BLK_SQ_DATA_SIZE_LOG)
#define DB_BMU_CQ_SIZE                (DB_BMU_CQ_DEPTH			<<	DB_BMU_CQ_DATA_SIZE_LOG)
#define DB_BMU_WR_CQ_SIZE             (DB_BMU_WR_CQ_DEPTH		<<	DB_BMU_WR_CQ_DATA_SIZE_LOG)
#define DB_BMU_WR_CQ_2_SIZE           (DB_BMU_WR_CQ_2_DEPTH		<<	DB_BMU_WR_CQ_2_DATA_SIZE_LOG)
#define DB_BMU_SEARCH_SQ_SIZE         (DB_BMU_SEARCH_SQ_DEPTH	<<	DB_BMU_SEARCH_SQ_DATA_SIZE_LOG)

#define DB_APU_WR_SQ_SIZE             (DB_APU_WR_SQ_DEPTH		<<	DB_APU_WR_SQ_DATA_SIZE_LOG)
#define DB_APU_CMPL_SQ_SIZE           (DB_APU_CMPL_SQ_DEPTH		<<	DB_APU_CMPL_SQ_DATA_SIZE_LOG)
#define DB_APU_TD_CQ_SIZE             (DB_APU_TD_CQ_DEPTH		<<	DB_APU_TD_CQ_DATA_SIZE_LOG)
#define DB_APU_CMD_CQ_SIZE            (DB_APU_CMD_CQ_DEPTH		<<	DB_APU_CMD_CQ_DATA_SIZE_LOG)
#define DB_APU_RD_CQ_SIZE             (DB_APU_RD_CQ_DEPTH		<<	DB_APU_RD_CQ_DATA_SIZE_LOG)

#define DB_XZIP_SQ_SIZE               (DB_XZIP_SQ_DEPTH			<<	DB_XZIP_SQ_DATA_SIZE_LOG)
#define DB_XZIP_CQ_SIZE               (DB_XZIP_CQ_DEPTH			<<	DB_XZIP_CQ_DATA_SIZE_LOG)

#define DB_COP0_WR_SQ_SIZE            (DB_COP0_WR_SQ_DEPTH		<<	DB_COP0_WR_SQ_DATA_SIZE_LOG)
#define DB_COP0_RD_SQ_SIZE            (DB_COP0_RD_SQ_DEPTH		<<	DB_COP0_RD_SQ_DATA_SIZE_LOG)
#define DB_COP0_CQ_SIZE               (DB_COP0_CQ_DEPTH			<<	DB_COP0_CQ_DATA_SIZE_LOG)

#define DB_COP1_SQ_SIZE               (DB_COP1_SQ_DEPTH			<<	DB_COP1_SQ_DATA_SIZE_LOG)
#define DB_COP1_CQ_SIZE               (DB_COP1_CQ_DEPTH			<<	DB_COP1_CQ_DATA_SIZE_LOG)

#define DB_FLH_MSG_CQ_SIZE            (DB_FLH_MSG_CQ_DEPTH		<<	DB_FLH_MSG_CQ_DATA_SIZE_LOG)
#define DB_RS_MSG_CQ_SIZE             (DB_RS_MSG_CQ_DEPTH		<<	DB_RS_MSG_CQ_DATA_SIZE_LOG)

#define DB_PIC_CQ_SIZE                (DB_PIC_CQ_DEPTH			<<	DB_PIC_CQ_DATA_SIZE_LOG)
#define DB_LOG_MSG_CQ_SIZE            (DB_LOG_MSG_CQ_DEPTH		<<	DB_LOG_MSG_CQ_DATA_SIZE_LOG)
#define DB_ERR_MSG_CQ_SIZE            (DB_ERR_MSG_CQ_DEPTH		<<	DB_ERR_MSG_CQ_DATA_SIZE_LOG)

#define DB_FW_SQ0_SIZE                (DB_FW_SQ0_DEPTH			<<	DB_FW_SQ0_DATA_SIZE_LOG)
#define DB_FW_CQ0_SIZE                (DB_FW_CQ0_DEPTH			<<	DB_FW_CQ0_DATA_SIZE_LOG)
#define DB_FW_SQ1_SIZE                (DB_FW_SQ1_DEPTH			<<	DB_FW_SQ1_DATA_SIZE_LOG)
#define DB_FW_CQ1_SIZE                (DB_FW_CQ1_DEPTH			<<	DB_FW_CQ1_DATA_SIZE_LOG)
#define DB_FW_SQ2_SIZE                (DB_FW_SQ2_DEPTH			<<	DB_FW_SQ2_DATA_SIZE_LOG)
#define DB_FW_CQ2_SIZE                (DB_FW_CQ2_DEPTH			<<	DB_FW_CQ2_DATA_SIZE_LOG)
#define DB_FW_SQ3_SIZE                (DB_FW_SQ3_DEPTH			<<	DB_FW_SQ3_DATA_SIZE_LOG)
#define DB_FW_CQ3_SIZE                (DB_FW_CQ3_DEPTH			<<	DB_FW_CQ3_DATA_SIZE_LOG)
#define DB_BMU_NOBLK_CQ_SIZE          (DB_BMU_NOBLK_CQ_DEPTH	<<	DB_BMU_NOBLK_CQ_DATA_SIZE_LOG)//non blocking CQ
#define DB_COP0_SQ1_SIZE              (DB_COP0_SQ1_DEPTH		<<	DB_COP0_SQ1_DATA_SIZE_LOG)

// define Queue Base address
#define DB_DMAC_HIGH_SQ_BASE          (DB_QBODY_ADDRESS)
#define DB_DMAC_HIGH_CQ_BASE          (DB_DMAC_HIGH_SQ_BASE + DB_DMAC_HIGH_SQ_SIZE)
#define DB_DMAC_NORM_SQ_BASE          (DB_DMAC_HIGH_CQ_BASE + DB_DMAC_HIGH_CQ_SIZE)
#define DB_DMAC_NORM_CQ_BASE          (DB_DMAC_NORM_SQ_BASE + DB_DMAC_NORM_SQ_SIZE)

#define DB_BMU_NOBLK_SQ_BASE          (DB_DMAC_NORM_CQ_BASE + DB_DMAC_NORM_CQ_SIZE)
#define DB_BMU_BLK_SQ_BASE            (DB_BMU_NOBLK_SQ_BASE + DB_BMU_NOBLK_SQ_SIZE)
#define DB_BMU_CQ_BASE                (DB_BMU_BLK_SQ_BASE   + DB_BMU_BLK_SQ_SIZE)
#define DB_BMU_WR_CQ_BASE             (DB_BMU_CQ_BASE       + DB_BMU_CQ_SIZE)
//#define DB_BMU_WR_CQ_2_BASE           (DB_BMU_WR_CQ_BASE    + DB_BMU_WR_CQ_SIZE)
//#define DB_BMU_SEARCH_SQ_BASE         (DB_BMU_WR_CQ_2_BASE  + DB_BMU_WR_CQ_2_SIZE)

#define DB_APU_WR_SQ_BASE             (DB_BMU_WR_CQ_BASE + DB_BMU_WR_CQ_SIZE)
#define DB_APU_CMPL_SQ_BASE           (DB_APU_WR_SQ_BASE     + DB_APU_WR_SQ_SIZE)
#define DB_APU_TD_CQ_BASE             (DB_APU_CMPL_SQ_BASE   + DB_APU_CMPL_SQ_SIZE)
#define DB_APU_CMD_CQ_BASE            (DB_APU_TD_CQ_BASE     + DB_APU_TD_CQ_SIZE)
#define DB_APU_RD_CQ_BASE             (DB_APU_CMD_CQ_BASE    + DB_APU_CMD_CQ_SIZE)

#define DB_XZIP_SQ_BASE               (DB_APU_RD_CQ_BASE    + DB_APU_RD_CQ_SIZE)
#define DB_XZIP_CQ_BASE               (DB_XZIP_SQ_BASE      + DB_XZIP_SQ_SIZE)

#define DB_COP0_RD_SQ_BASE            (DB_XZIP_CQ_BASE      + DB_XZIP_CQ_SIZE)
#define DB_COP0_WR_SQ_BASE            (DB_COP0_RD_SQ_BASE   + DB_COP0_RD_SQ_SIZE)
#define DB_COP0_CQ_BASE               (DB_COP0_WR_SQ_BASE   + DB_COP0_WR_SQ_SIZE)

#define DB_COP1_SQ_BASE               (DB_COP0_CQ_BASE      + DB_COP0_CQ_SIZE)
#define DB_COP1_CQ_BASE               (DB_COP1_SQ_BASE      + DB_COP1_SQ_SIZE)

#define DB_FLH_MSG_CQ_BASE            (DB_COP1_CQ_BASE      + DB_COP1_CQ_SIZE)
#define DB_RS_MSG_CQ_BASE             (DB_FLH_MSG_CQ_BASE   + DB_FLH_MSG_CQ_SIZE)

#define DB_PIC_CQ_BASE                (DB_RS_MSG_CQ_BASE    + DB_RS_MSG_CQ_SIZE)
#define DB_LOG_MSG_CQ_BASE            (DB_PIC_CQ_BASE       + DB_PIC_CQ_SIZE)
#define DB_ERR_MSG_CQ_BASE            (DB_LOG_MSG_CQ_BASE   + DB_LOG_MSG_CQ_SIZE)

#define DB_FW_SQ0_BASE                (DB_ERR_MSG_CQ_BASE   + DB_ERR_MSG_CQ_SIZE)
#define DB_FW_CQ0_BASE                (DB_FW_SQ0_BASE       + DB_FW_SQ0_SIZE)
#define DB_FW_SQ1_BASE                (DB_FW_CQ0_BASE       + DB_FW_CQ0_SIZE)
#define DB_FW_CQ1_BASE                (DB_FW_SQ1_BASE       + DB_FW_SQ1_SIZE)
#define DB_FW_SQ2_BASE                (DB_FW_CQ1_BASE       + DB_FW_CQ1_SIZE)
#define DB_FW_CQ2_BASE                (DB_FW_SQ2_BASE       + DB_FW_SQ2_SIZE)
#define DB_FW_SQ3_BASE                (DB_FW_CQ2_BASE       + DB_FW_CQ2_SIZE)
#define DB_FW_CQ3_BASE                (DB_FW_SQ3_BASE       + DB_FW_SQ3_SIZE)

#define DB_BMU_WR_CQ_2_BASE           (DB_FW_CQ3_BASE    + DB_FW_CQ3_SIZE)
#define DB_BMU_SEARCH_SQ_BASE         (DB_BMU_WR_CQ_2_BASE  + DB_BMU_WR_CQ_2_SIZE)
#define DB_BMU_NOBLK_CQ_BASE                (DB_BMU_SEARCH_SQ_BASE   + DB_BMU_BLK_SQ_SIZE)//non blocking CQ
#define DB_COP0_SQ1_BASE              (DB_BMU_NOBLK_CQ_BASE + DB_BMU_NOBLK_CQ_SIZE)

// width: 8, 16, 32 bits
#define M_DB_W8(DBID, OFFSET)		( R8_DB[DBID][OFFSET])
#define M_DB_W16(DBID, OFFSET)		(R16_DB[DBID][OFFSET])
#define M_DB_W32(DBID, OFFSET)		(R32_DB[DBID][OFFSET])

#define M_DB_GET_QBASE(DBID)			(R32_DB[DBID][R32_DB_QBASE_ADR])
#define M_DB_GET_WPTR(DBID)				(R32_DB[(DBID)][R32_DB_WPTR])
#define M_DB_GET_WR_CNT(DBID)			(R16_DB[DBID][R16_DB_WR_CNT])
#define M_DB_GET_WPTR_OFFSET(DBID)		((R32_DB[DBID][R32_DB_WPTR] - (U32)(DBID ## _BASE)) >> (DBID ## _DATA_SIZE_LOG))
#define M_DB_GET_RPTR(DBID)				(R32_DB[DBID][R32_DB_RPTR])
#define M_DB_GET_RD_CNT(DBID)			(R16_DB[DBID][R16_DB_RD_CNT])
#define M_DB_GET_RPTR_OFFSET(DBID)		((R32_DB[DBID][R32_DB_RPTR] - (U32)(DBID ## _BASE)) >> (DBID ## _DATA_SIZE_LOG))
#define M_DB_GET_QBODY_PTR(DBID, OFFSET)	(void *)((DBID ## _BASE) + ((U32)(OFFSET & (DBID ## _MASK)) << (DBID ## _DATA_SIZE_LOG)))
//Terry 0529
#define M_DB_GET_DEPTH(DBID)			(DBID ## _DEPTH)
#define M_DB_GET_DATA_SIZE(DBID)		(1 << (DBID ## _DATA_SIZE_LOG))
#define M_DB_CHECK_EMPTY(DBID)			((R8_DB[DBID][R8_DB_CTRL] >> QUEUE_EMPTY_SHIFT) & QUEUE_EMPTY_MASK)
#define M_DB_CHECK_FULL(DBID)			((R8_DB[DBID][R8_DB_CTRL] >> QUEUE_FULL_SHIFT) & QUEUE_FULL_MASK)

#define M_DB_GET_NEXT_OFFSET(DBID, OFFSET)	((((U32)OFFSET + (U32)1) & 0xFFFF) & (DBID ## _MASK))

#if VS_SIM_EN
#define M_DB_TRIGGER_WRITE_CNT(DBID, CNT) do { \
	R16_DB[(DBID)][R16_DB_WPIU] = (CNT); \
	SimDBTransfer((DBID), R16_DB_WPIU); \
	gDBQueueCnt.QueueCnt[(DBID)] = (gDBQueueCnt.QueueCnt[(DBID)] + (CNT)) & (DBID ## _MASK); \
} while (0)

#define M_DB_TRIGGER_READ_CNT(DBID, CNT) do { \
	R16_DB[(DBID)][R16_DB_RPIU] = (CNT); \
	SimDBTransfer((DBID), R16_DB_RPIU); \
	gDBQueueCnt.QueueCnt[(DBID)] = (gDBQueueCnt.QueueCnt[(DBID)] + (CNT)) & (DBID ## _MASK); \
} while (0)

#else /* VS_SIM_EN */
#define M_DB_TRIGGER_WRITE_CNT(DBID, CNT) do { \
	__asm ("DSB");\
	R16_DB[(DBID)][R16_DB_WPIU] = (CNT); \
	gDBQueueCnt.QueueCnt[(DBID)] = (gDBQueueCnt.QueueCnt[(DBID)] + (CNT)) & (DBID ## _MASK); \
} while (0)

#define M_DB_TRIGGER_READ_CNT(DBID, CNT) do { \
	R16_DB[(DBID)][R16_DB_RPIU] = (CNT); \
	gDBQueueCnt.QueueCnt[(DBID)] = (gDBQueueCnt.QueueCnt[(DBID)] + (CNT)) & (DBID ## _MASK); \
} while (0)
#endif /* VS_SIM_EN */
//Terry 0529
#if (FPGA_BOARD == FPGA_USE_SIM_CODE)

#define M_CHECK_RCQ_TABLE_VALID()			(R8_RCQ_INFO[R8_DB_RCQ_TABLE_INFO] & R8_RCQ_TABLE_VLD_BIT)
#define M_GET_RCQ_TABLE_READ_OFFSET()		(R8_RCQ_INFO[R8_DB_RCQ_TABLE_POS])
#define M_GET_RCQ_TABLE(INFO) 	do{\
									INFO = R8_RCQ_INFO[R8_DB_RCQ_TABLE_INFO];\
								} while (0)

#define M_FREE_RCQ_TABLE(OFFSET) do { \
	R8_RCQ_INFO[R8_DB_RCQ_TABLE_WPTR] = (OFFSET); \
	R8_RCQ_INFO[R8_DB_RCQ_TABLE_INFO] |= (R8_RCQ_TABLE_FRE_BIT); \
	ClearRCQBitmap((OFFSET)); \
} while(0)



#elif (!GEN_CQ_EN)

#if PS5021_EN
#define M_CHECK_RCQ_TABLE_VALID()			(R32_RCQ_INFO[R32_DB_RCQ_TABLE_INFO] & R32_RCQ_TABLE_VLD_BIT)
#define M_GET_RCQ_TABLE_READ_OFFSET()		((R32_RCQ_INFO[R32_DB_RCQ_TABLE_INFO] & RCQ_TABLE_POS_MASK) >> RCQ_TABLE_POS_OFFSET)
#define M_GET_RCQ_TABLE(INFO) 			do{\
												INFO = R32_RCQ_INFO[R32_DB_RCQ_TABLE_INFO];\
											} while (0)

#define M_FREE_RCQ_TABLE(OFFSET) 		do{\
	R32_RCQ_INFO[R32_DB_RCQ_TABLE_INFO] = (R32_RCQ_INFO[R32_DB_RCQ_TABLE_INFO] & ~RCQ_TABLE_WPTR_MASK) | ((OFFSET & RCQ_TABLE_WPTR_MASK) << RCQ_TABLE_WPTR_OFFSET); \
												R32_RCQ_INFO[R32_DB_RCQ_TABLE_INFO] |= (R32_RCQ_TABLE_FRE_BIT);\
											} while (0)
#else /* PS5021_EN */
#define M_CHECK_RCQ_TABLE_VALID()			(R8_RCQ_INFO[R8_DB_RCQ_TABLE_INFO] & R8_RCQ_TABLE_VLD_BIT)
#define M_GET_RCQ_TABLE_READ_OFFSET()		(R8_RCQ_INFO[R8_DB_RCQ_TABLE_POS])
#define M_GET_RCQ_TABLE(INFO) 			do{\
												INFO = R32_RCQ_INFO[R32_DB_RCQ_TABLE_INFO];\
											} while (0)

#define M_FREE_RCQ_TABLE(OFFSET) 		do{\
	R8_RCQ_INFO[R8_DB_RCQ_TABLE_WPTR] = (OFFSET); \
												R8_RCQ_INFO[R8_DB_RCQ_TABLE_INFO] |= (R8_RCQ_TABLE_FRE_BIT);\
											} while (0)
#endif /* PS5021_EN */

#elif (GEN_CQ_EN)

#define M_CHECK_RCQ_TABLE_VALID()			HostCheckRCQTableValid()
#define M_GET_RCQ_TABLE_READ_OFFSET()		HostGetRCQTableReadOffset()

#define M_FREE_RCQ_TABLE(OFFSET)			HostFreeRCQTable(OFFSET)

#else /* (FPGA_BOARD == FPGA_USE_SIM_CODE) */
#error "Unknown FPGA_BOARD"
#endif /* (FPGA_BOARD == FPGA_USE_SIM_CODE) */

INLINE void  RCQTableClearRCQBitmap(RCQOffset_t RCQOffset)
{
	M_FREE_RCQ_TABLE(RCQOffset);
#if VS_SIM_EN
	//M_SIM_RCQ_TABLE_WR (R8_DB_RCQ_TABLE_INFO);
	//U32 ulRegMRTemp;
	if (R8_DB_RCQ_TABLE_INFO & R8_RCQ_TABLE_FRE_BIT) {
		ClearRCQBitmap(R8_RCQ_INFO[R8_DB_RCQ_TABLE_WPTR]);
	}
#endif /* VS_SIM_EN */
}

typedef union DBQueueCnt {
	U16 QueueCnt[DB_QUEUE_CNT];
	struct {
		U16 uwDMACHighSQCnt;			// 0
		U16 uwDMACHighCQCnt;			// 1
		U16 uwDMACNormalSQCnt;		// 2
		U16 uwDMACNormalCQCnt;		// 3
		U16 uwBMUNonBlockingSQCnt;		// 4
		U16 uwBMUBlockingSQCnt;		// 5
		U16 uwBMUCQCnt;				// 6
		U16 uwBMUWriteCQCnt;			// 7
		U16 uwAPUWriteChannelSQCnt;	// 8
		U16 uwAPUCompletionSQCnt;		// 9
		U16 uwAPUTransferDoneCQCnt;	// 10
		U16 uwAPUCommandCQCnt;		// 11
		U16 uwAPUReadCQCnt;			// 12
		U16 uwXZIPSQCnt;				// 13
		U16	uwXZIPCQCnt;				// 14
		U16 uwCOP0ReadSQCnt;			// 15
		U16 uwCOP0WriteSQCnt;			// 16
		U16 uwCOP0CQCnt;				// 17
		U16 uwCOP1SQCnt;				// 18
		U16 uwCOP1CQCnt;				// 19
		U16 uwFLHMsgCQCnt;				// 20
		U16 uwRSMsgCQCnt;				// 21
		U16 uwPICCQCnt;				// 22
		U16 uwLogMsgCQCnt;				// 23
		U16 uwErrMsgCQCnt;				// 24
		U16 uwSoftwareSQ0Cnt;			// 25
		U16 uwSoftwareCQ0Cnt;			// 26
		U16 uwSoftwareSQ1Cnt;			// 27
		U16 uwSoftwareCQ1Cnt;			// 28
		U16 uwSoftwareSQ2Cnt;			// 29
		U16 uwSoftwareCQ2Cnt;			// 30
		U16 uwSoftwareSQ3Cnt;			// 31
		U16 uwSoftwareCQ3Cnt;			// 32
		U16 uwBMUWriteCQ2Cnt;			// 33
		U16 uwBMUSearchSQCnt;			// 34
		U16 uwBMUCQ2Cnt;				// 35
		U16 uwCOP0SQ1Cnt;				// 36
	} B;
} DBQueueCnt_t;


typedef union DBTableInfo {
	U32 ulAll;
	struct {
#if PS5021_EN
		U32 ubRCQTableWPTR	: 9;
		U32 ubRCQTableRPTR	: 9;
		U32 ubRCQTablePOS	: 9;
		U32 btRCQTableValid : 1;
		U32 btRsv			: 1;
		U32 btRCQTableFree	: 1;
		U32 btRCQTableCLR	: 1;
		U32 btRCQTableRST	: 1;
#else /* PS5021_EN */
		U8 ubRCQTableWPTR;
		U8 ubRCQTableRPTR;
		U8 ubRCQTablePOS;
		U8 btRCQTableValid:	1;
		U8 btRCQTableFree:	1;
		U8 btRCQTableCLR:	1;
		U8 btRCQTableRST:	1;
		U8 ubReserved:		4;
#endif /* PS5021_EN */
	};
} RCQTableInfo_t;


//=============================================================================
// Macro Functions
//=============================================================================
#define M_CALCULATE_RCQ_DIFF(RCQ_OFFSET) ((RCQ_OFFSET + DB_APU_RD_CQ_DEPTH - ((R32_DB[DB_APU_RD_CQ][R32_DB_RPTR] - R32_DB[DB_APU_RD_CQ][R32_DB_QBASE_ADR]) / (1 << DB_APU_RD_CQ_DATA_SIZE_LOG))) % DB_APU_RD_CQ_DEPTH)

extern DBQueueCnt_t gDBQueueCnt;

AOM_INIT void DBInit(void);
AOM_INIT NO_INLINE void DBQInfoInit(U8 ubDBID, U32 ulQBase, U8 ubDataSize, U16 uwBufLength);
AOM_INIT void DBFillDummyInLogMessageQueue(void);

#endif /* _DB_API_H_ */
