#ifndef _RS_API_H_
#define _RS_API_H_

#include "hal/rs/rs_reg.h"
#include "hal/raidecc/raidecc_reg.h"
#include "aom/aom_api.h"
#include "env.h"

#define RS_MAX_ENCODE_PB_NUM		        (34)
#define RAIDECC_MAX_VIRTUAL_PB_NUM          (34)
#define RAIDECC_INTERNAL_BUF_NUM            (4)

#define M_RS_GET_TAG_CONFIG()		(R32_RS[R32_RAIDECC_TAG_CONFIG_0])


#define M_RS_PARSE_TAG_CONFIG_FOR_RSTAG(ulRSTagConfig)	((ulRSTagConfig >> RAIDECC_TAG_SEL_SHIFT) & RAIDECC_TAG_SEL_MASK)
#define M_RS_PARSE_TAG_CONFIG_FOR_PARITY_ENCODE_CNT(ulRSTagConfig)		((ulRSTagConfig >> RAIDECC_TAG_PEC_SHIFT) & RAIDECC_TAG_PEC_MASK)
#define M_RS_PARSE_TAG_CONFIG_FOR_PARITY_READY_CNT(ulRSTagConfig)		((ulRSTagConfig >> RAIDECC_TAG_PRC_SHIFT) & RAIDECC_TAG_PRC_MASK)
#define M_RS_PARSE_TAG_CONFIG_FOR_VALID(ulRSTagConfig)			((ulRSTagConfig >> RAIDECC_TAG_VALID_SHIFT) & RAIDECC_TAG_VALID_MASK)


#define M_RS_IS_RS_IDLE()                 ((R32_RS[R32_RAIDECC_TRIG] >> RAIDECC_RS_IDLE_SHIFT) & RAIDECC_RS_IDLE_MASK)
#define M_RAIDECC_WAIT_RAIDECC_IDLE()					do{ \
												while (!M_RS_IS_RS_IDLE()); \
												while (!M_RS_IS_RS_IDLE()); \
											}while(0)

#define M_RS_GET_PARITY_BUF_SELECT_CONFIG_INFO()						(R32_RS[R32_PBUF_SEL_CFG])


#define M_RS_GET_TAG_VALID_BIT()           ((R32_RS[R32_RAIDECC_TAG_CONFIG_0] >> RAIDECC_TAG_VALID_SHIFT) & RAIDECC_TAG_VALID_MASK)
#define M_RS_GET_PARITY_ENCODED_CNT()     ((R32_RS[R32_RAIDECC_TAG_CONFIG_0] >> RAIDECC_TAG_PEC_SHIFT) & RAIDECC_TAG_PEC_MASK)

#define M_RS_SET_PARITY_ENCODED_CNT(PARITY_ENCODED_CNT) do{ \
                                                    U32 ulValue; \
                                                    ulValue = R32_RS[R32_RAIDECC_TAG_CONFIG_0]; \
                                                    ulValue &= ~(RAIDECC_TAG_PEC_MASK << RAIDECC_TAG_PEC_SHIFT);\
                                                    ulValue |= (PARITY_ENCODED_CNT & RAIDECC_TAG_PEC_MASK) << RAIDECC_TAG_PEC_SHIFT; \
                                                    R32_RS[R32_RAIDECC_TAG_CONFIG_0] = ulValue;\
                                                }while(0)

#define M_RS_SET_TAG_VALID_BIT()           (R32_RS[R32_RAIDECC_TAG_CONFIG_0] |= BIT(RAIDECC_TAG_VALID_SHIFT))
#define M_RS_CHECK_TAG_CONFIG_0_VALID_BUSY()        ((R32_RS[R32_RAIDECC_TAG_CONFIG_0] >> SET_RAIDECC_TAG_VALID_BUSY_SHIFT_60) & SET_RAIDECC_TAG_VALID_BUSY_MASK_60)
#define M_RS_SET_READ_STATUS()         do{ \
                                                R32_RS[R32_RAIDECC_TAG_VLD_CTL] |= (1 & RAIDECC_TAG_RD_MASK);       \
                                                while(R32_RS[R32_RAIDECC_TAG_VLD_CTL] & BIT(RAIDECC_TAG_RD_SHIFT)); \
                                            }while(0)
#define M_RS_TAG_SELECT(RS_TAG)              (R16_RS[R16_RAIDECC_TAG_SEL] = (RS_TAG) & RAIDECC_TAG_SEL_MASK)   //0x78


#define M_RS_SET_PBUF_TAG_NUM(PBUF_TAG_NUM)   do{ \
                                                        U32 ulValue; \
                                                        ulValue = R32_RS[R32_RAIDECC_PBUF_CTL_1]; \
                                                        ulValue &= ~(RAIDECC_PBUF_RAIDECC_TAG_NUM_MASK << RAIDECC_PBUF_RAIDECC_TAG_NUM_SHIFT); \
                                                        ulValue |= ((PBUF_TAG_NUM) & RAIDECC_PBUF_RAIDECC_TAG_NUM_MASK) << RAIDECC_PBUF_RAIDECC_TAG_NUM_SHIFT;\
                                                        R32_RS[R32_RAIDECC_PBUF_CTL_1] = ulValue; \
                                                    }while(0)

#define M_RS_SET_PB_NUM(PB_NUM)   do{ \
                                                        U32 ulValue; \
                                                        ulValue = R32_RS[R32_RAIDECC_PBUF_CTL_1]; \
                                                        ulValue &= ~(RAIDECC_RAIDECC_PB_NUM_MASK << RAIDECC_RAIDECC_PB_NUM_SHIFT); \
                                                        ulValue |= ((PB_NUM) & RAIDECC_RAIDECC_PB_NUM_MASK) << RAIDECC_RAIDECC_PB_NUM_SHIFT;\
                                                        R32_RS[R32_RAIDECC_PBUF_CTL_1] = ulValue; \
                                                    }while(0)

#define M_RS_SET_PBUF_STATUS()                 (R32_RS[R32_RAIDECC_PBUF_CTL_1] |= BIT(RAIDECC_PBUF_STATUS_SHIFT))
#define M_RS_GET_PBUF_STATUS()					((R32_RS[R32_RAIDECC_PBUF_CTL_1] >> RAIDECC_PBUF_STATUS_SHIFT) & RAIDECC_PBUF_STATUS_MASK)

#define M_RS_SET_PB_NUM_VLD()                  (R32_RS[R32_RAIDECC_PBUF_CTL_1] |= BIT(RAIDECC_PB_NUM_VLD_SHIFT))

#define M_RS_IS_ENCODED_NUM_VALID()     ((R32_RS[R32_RAIDECC_BMU_ENC_PB_CTL] >> RAIDECC_ENCODE_PB_NUM_VLD_SHIFT) & RAIDECC_ENCODE_PB_NUM_VLD_MASK)
#define M_RS_SET_ENCODED_NUM(VIRTUAL_PB_IDX)   do{ \
                                                        U32 ulValue; \
                                                        ulValue = R32_RS[R32_RAIDECC_BMU_ENC_PB_CTL]; \
                                                        ulValue &= ~(RAIDECC_ENCODE_PB_NUM_MASK << RAIDECC_ENCODE_PB_NUM_SHIFT); \
                                                        ulValue |= ((VIRTUAL_PB_IDX) & RAIDECC_ENCODE_PB_NUM_MASK) << RAIDECC_ENCODE_PB_NUM_SHIFT;\
                                                        R32_RS[R32_RAIDECC_BMU_ENC_PB_CTL] = ulValue; \
                                                    }while(0)

#define M_RS_SET_ENCODED_NUM_VALID()           (R32_RS[R32_RAIDECC_BMU_ENC_PB_CTL] |= BIT(RAIDECC_ENCODE_PB_NUM_VLD_SHIFT))

#define RS_INVALID_EXTERNAL_PB_NUM                   (0x1F)

#if PS5017_EN
#define M_RS_CHK_RS_INTR_ENABLE()      ((R32_RS[R32_RAIDECC_IP_CTL]>>RAIDECC_INTR_EN_SHIFT)&(RAIDECC_INTR_EN_MASK))//E19 add
#define M_RS_GET_ERR_INT_INFO(INFO)    (INFO = R32_RS[R32_RAIDECC_ERR_INT_INFO])//E19 add
#endif /* PS5017_EN */

#define M_RAIDECC_DISABLE_PRESAVE()			(R32_RS[R32_RAIDECC_PBUF_CTL] &= (CLR_PRE_LOAD_EN & CLR_RS_PRE_SL_DIS & CLR_PRE_SAVE_EN))

#define M_RAIDECC_GET_RAIDECC_BUF_OFFSET() 		(R32_RS[R32_RAIDECC_ALLOCATE_PB_LINK] & RAIDECC_LB_OFFSET_MASK)

#if PS5017_EN
#define M_RAIDECC_CLEAR_BUF_PTR_TABLE(BUF_IDX) 	            do{ \
	ARM_DCACHE_INVALIDATE_RANGE(RS_BPT1_OFFSET + 8 * BUF_IDX, 0x20);\
	R64_RS_BPT1[(BUF_IDX)] = 0; \
	ARM_DCACHE_CLEAN_RANGE(RS_BPT1_OFFSET + BUF_IDX * 8, 0x20); \
	}while(0)
#define M_RAIDECC_SET_BUF_PTR_TABLE(BUF_IDX, VALUE, OFFSET) do{ \
	ARM_DCACHE_INVALIDATE_RANGE(RS_BPT1_OFFSET + 8 * BUF_IDX, 0x20);\
	R64_RS_BPT1[(BUF_IDX)] |= ((U64)(VALUE)) << (OFFSET); \
	ARM_DCACHE_CLEAN_RANGE(RS_BPT1_OFFSET + BUF_IDX * 8, 0x20); \
	}while(0)
#else  /* PS5017_EN */
#define M_RAIDECC_CLEAR_BUF_PTR_TABLE(BUF_IDX) 	            do{ R64_RS_BPT1[(BUF_IDX)] = 0; }while(0)
#define M_RAIDECC_SET_BUF_PTR_TABLE(BUF_IDX, VALUE, OFFSET) do{ R64_RS_BPT1[(BUF_IDX)] |= ((U64)(VALUE)) << (OFFSET); }while(0)
#endif /* PS5017_EN */





typedef enum {
	RAIDECC_PARITY_INTERNAL_BUF,
	RAIDECC_PARITY_EXTERNAL_BUF,
	RAIDECC_PARITY_NOT_EXIST
} RaidECCParityLocation_t;
typedef struct {
	RaidECCParityLocation_t Location;
	U8 ubParityTagIdx;
	U8 ubBufIdx;
	U8 ubVirtualParityBufIdx;
} RaidECCTagIdxDescription_t;

typedef struct {
	RaidECCParityLocation_t Location;
	U8 ubBufIdx;
} RaidECCBufInformation_t;


U8 RaidECCmapCheckRaidECCEncodeDone(U8 ubRSTag, U8 ubFWParityEncodeCnt);
AOM_RS_1 U32 RaidECCMapGetExternalParityBufOffset(U8 ubExternalBufIdx, U8 ubFrame);
#if ((!BURNER_MODE_EN) && (!RDT_MODE_EN))
AOM_RETRY_RS extern void RaidECCMapTriggerDecode(U8 ubPageIdx);
AOM_RETRY_RS extern void RSTriggerCorrect(U8 ubRSColIdx);
AOM_RETRY_RS extern U8  RaidECCRetryForceSaveInternalBufferAndLock(U8 ubRSTagIndex, U32 ulBufAddrData, U32 ulBufAddrSpare);
AOM_RETRY_RS extern U8  RaidECCRetryLookupEncodeCountFromRaidECCTag(U8 ubTag);
AOM_RETRY_RS extern void RaidECCRetrySetDecodeParameter(U8 ubErrorPageCnt);
AOM_RETRY_RS extern void RaidECCRetrySelectPBuf(void);
AOM_RETRY_RS extern void RaidECCRetryUnlockAndLockToNewPBuf(U8 *pubRSPBufIdx, U8 ubRSTag);
AOM_RETRY_RS extern U8 RaidECCRetryGetExternalBufIdxAndBuf(U32 *ulDataBuffer);
AOM_RETRY_RS extern U8 RaidECCRetryCheckIfDataIsOnInternalBuf(U8 ubDecodeRSCnt, U8 ubRSTag);
#endif
AOM_RS_2 RaidECCBufInformation_t RaidECCFindEmptyTagBuf();
#endif /*_RS_API_H_ */
