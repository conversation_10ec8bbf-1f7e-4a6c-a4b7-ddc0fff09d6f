#include "hal/pic/uart/uart_api.h"
#include "hal/dmac/dmac_api.h"
#include "hal/dmac/dmac_pop_cmd.h"
#include "nvme_api/nvme/shr_hal_nvme.h"
#include "vuc/Vth_Parsing.h"
#include "host/VUC_handler.h"
#include "vuc/VUC_command.h"

#if (BURNER_MODE_EN)
void VUC_NandVerifyWrite(VUC_OPT_HCMD_PTR_t pCmd)
{
	if (TRUE == gFlhEnv.ulFlashDefaultType.BitMap.btNoSupport) {
		pCmd->ubState = CMD_ERROR;
		gVUCVar.ubVucCmdErrStatus = HOST_ERROR_FEATURE;
		return;
	}

	U32 buffer_size = pCmd->vuc_sqcmd.vendor.NandVerificationWrite.ulTransLen;

	DMACParam_t DMACParam;
	DMACParam.DMACSetValue.ulDestAddr = VENDOR_NAND_VERIFICATION_SHADOW_DATA;
	DMACParam.DMACSetValue.ulLowValue = 0;
	DMACParam.DMACSetValue.ulHighValue = 0;
	DMACParam.DMACSetValue.ul32ByteNum = SIZE_IN_32B(VENDOR_NAND_VERIFICATION_SHADOW_DATA_SIZE);
	DMACParam.DMACSetValue.btValidate = FALSE;
	gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
	DMACSetValue(&DMACParam, gulDMACDirectWaitDone_Callback, 0);
	while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
		DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
	}


	memcpy((void *)VENDOR_NAND_VERIFICATION_SHADOW_DATA, (void *)BURNER_VENDOR_BUF_BASE, buffer_size);

}
#endif /* BURNER_MODE_EN */
