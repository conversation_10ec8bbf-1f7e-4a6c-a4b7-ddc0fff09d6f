#define _USB_CMD_C_


#include "hal\usb\usb.h"
#include "hal\usb\usb_cmd.h"

#if (USB == HOST_MODE)

#define VID_L    0xfeU
#define VID_H    0x13U
#define PID_L    0x00U
#define PID_H    0x51U

AOM_USBC U8 const gaubCbString0Descriptor[4U] = {
	0x04U,
	0x03U,
	0x09U, 0x04U	/* language ID, English(US)*/
};

AOM_USBC U8 const gaubCbString1Descriptor[34U] = {
	0x22,
	0x03,
	'P',
	0x00,
	'h',
	0x00,
	'I',
	0x00,
	's',
	0x00,
	'O',
	0x00,
	'n',
	0x00,
	0x20,
	0x00,
	0x20,
	0x00,
	0x20,
	0x00,
	0x20,
	0x00,
	0x20,
	0x00,
	0x20,
	0x00,
	0x20,
	0x00,
	0x20,
	0x00,
	0x20,
	0x00,
	0x20,
	0x00
};

AOM_USBC U8 const gaubCbString2Descriptor[26U] = {
	0x1A,
	0x03,
	0x55,
	0x00,
	0x53,
	0x00,
	0x42,
	0x00,
	0x20,
	0x00,
	0x44,
	0x00,
	0x49,
	0x00,
	0x53,
	0x00,
	0x4B,
	0x00,
	0x20,
	0x00,
	0x33,
	0x00,
	0x2E,
	0x00,
	0x30,
	0x00
};

AOM_USBC U8 const gaubCbString3Descriptor[34U] = {
	0x22,
	0x03,
	0x30,
	0x00,
	0x37,
	0x00,
	0x30,
	0x00,
	0x30,
	0x00,
	0x35,
	0x00,
	0x32,
	0x00,
	0x43,
	0x00,
	0x46,
	0x00,
	0x33,
	0x00,
	0x37,
	0x00,
	0x37,
	0x00,
	0x38,
	0x00,
	0x46,
	0x00,
	0x34,
	0x00,
	0x33,
	0x00,
	0x37,
	0x00
};

AOM_USBC U8 const gaubSsDeviceDescriptor[] = {
	/*[DEVICE_LENGTH]={*/
	0x12U, /* length*/
	0x01U, /* Descriptor type Device = 1*/
	0x20U, /* USB version: 0x0320, little endian*/
	0x03U,
	0x00U, /* device class*/
	0x00U, /* device sub-class*/
	0x00U, /* device protocol*/
	0x09U, /* max packet size 2**9=512*/
	VID_L,
	VID_H,
	PID_L,
	PID_H,
	0x10U, /* low byte, device release number = 1.10*/
	0x01U, /* high byte*/
	0x01U, /* index of string descriptor for manufactory*/
	0x02U, /* index of string descriptor for product*/
	0x03U, /* 0x03, index of string descriptor for serial number. 0x00:no serial*/
	0x01U  /* number of configuration*/
};
AOM_USBC U8 const gaub20DeviceDescriptor[] = {
	/*[DEVICE_LENGTH]={*/
	0x12U, /* length*/
	0x01U, /* Descriptor type Device = 1*/
	0x10U, /* USB version: 0x0210, little endian*/
	0x02U,
	0x00U, /* device class*/
	0x00U, /* device sub-class*/
	0x00U, /* device protocol*/
	0x40U, /* max packet size*/
	VID_L,
	VID_H,
	PID_L,
	PID_H,
	0x10U, /* device release number = 1.10*/
	0x01U,
	0x01U, /* index of string descriptor for manufactory*/
	0x02U, /* index of string descriptor for product*/
	0x03U, /* 0x03, index of string descriptor for serial number. 0x00:no serial*/
	0x01U, /* number of configuration*/
};

#if (USB_HID_EN)
AOM_USBC U8 const gaubHidReport[] = {
	0x06, 0x27, 0xFF,// USAGE_PAGE(two bytes) = 0xff27 = undefined page
	0x09, 0x01,      // USAGE = 0x01 = consumer
	0xA1, 0x01,      // COLLECTION = 0x01 = application

	0x05, 0x06,      //   USAGE_PAGE(one byte) = 0x06 = Generic Device Controls
	0x09, 0x00,      //   USAGE = 0x00 = Unidentified
	0x15, 0x00,      //   LOGICAL_MINIMUM = 0x00
	0x25, 0xff,      //   LOGICAL_MAXIMUJM = 0xff
	0x96, 0x00, 0x02,//   REPORT_COUNT = 0x0400 = 1024
	0x75, 0x08,      //   REPORT_SIZE = 0x08 = 8 bits = 1 byte
	0x81, 0x02,      //   INPUT = 0x02 = Data, Variable, Absolute, No Wrap, Linear, Preferred State, No Null Position, Bit Field

	0x05, 0x06,      //   USAGE_PAGE(two bytes) = 0x06 = Generic Device Controls
	0x09, 0x00,      //   USAGE = 0x00 = Unidentified
	0x15, 0x00,      //   LOGICAL_MINIMUM = 0x00
	0x25, 0xff,      //   LOGICAL_MAXIMUM = 0xff
	0x96, 0x00, 0x02,//   REPORT_COUNT(two bytes) = 0x0400 = 1024 bytes
	0x75, 0x08,      //   REPORT_SIZE = 0x08 = 8 bits = 1 byte
	0x91, 0x02,      //   OUTPUT = 0x02 = Data, Variable, Absolute, No Wrap, Linear, Preferred State, No Null Position, Bit Field

	0xC0             // END_COLLECTION
};
#endif /* (USB_HID_EN) */

AOM_USBC U8 const gaubSsConfigDescriptor_UASP[] = {
	/* [CONFIG_LENGTH] // Super Speed Configuration */
	0x09, /* length */
	0x02, /* descriptor type configuration = 2 */
	0x79, /* total length of CONFIG_LENGTH */
	0x00, /* CONFIG_LENGTH>>8, */
	0x01, /* number of interface */
	0x01, /* configuration value??? */
	0x00, /* index of string descriptor for configuration description */
	0x80, /* Configuration attributes D7: Reserved, must 1, D6: Self powered,  D5: Remote wakeup */
	CONFIGURATION_DESCRIPTOR_SS_B8, /* max power, unit = 8mA, Self-Powered */

	/* interface 0 */
	0x09, /* length */
	0x04, /* descriptor type, interface = 4 */
	0x00, /* interface value */
	0x00, /* alternative setting value */
	0x02, /* endpoints number */
	0x08, /* interface class, MASS SOTRAGE CLASS */
	0x06, /* interface sub-class, SFF8020i */
	0x50, /* interface protocol, BOT PROTOCAL */
	0x00, /* index of string descriptor for describing interface */

	/* endpoint 1, DATA IN pipe */
	0x07, /* length */
	0x05, /* descriptor type endpoint = 5 */
	0x81, /* Endpoint Address. D7: direction (0:out, 1:in), D6..4: reserved, */
	// D3..0: endpoint number
	0x02, /* attributes: 0:control, 1:isochronous, 2:bulk, 3:interrupt */
	SS_PACKET_SIZE_LB, /* Low Byte max packet size = 1024 */
	SS_PACKET_SIZE_HB, /* High Byte max packet size = 1024 */
	0x00, /* polling interval, 1 for isochronous, 1~255 for interrupt */

	/* endpoint 1 companion. */
	0x06, /* length */
	SUPERSPEED_0X30, /* descriptor type = endpoint companion */
	MAX_BURST, /* maximum burst. 0 = one packet per burst, 15 = 16 packets per burst */
	0x00, /* maximum stream. 0 = no stream */
	0x00, /* reserved for periodic endpoints */
	0x00, /* reserved for periodic endpoints */

	/* endpoint 2, DATA OUT pipe */
	0x07, /* length */
	0x05, /* Descriptor type endpoint = 5 */
	0x02, /* endpoint address: D7 for direction(0:out, 1:in), */
	// D3..0 endpoint number
	0x02, /* attribute: 0 control, 1:isochronous, 2:bulk, 3:interrupt */
	SS_PACKET_SIZE_LB, /* Low Byte max packet size = 1024 */
	SS_PACKET_SIZE_HB, /* High Byte max packet size = 1024 */
	0x00, /* polling interval */

	/* endpoint 2 companion. */
	0x06, /* length */
	SUPERSPEED_0X30, /* descriptor type = endpoint companion */
	MAX_BURST, /* maximum burst. 0 = one packet per burst, 15 = 16 packets per burst */
	0x00, /* maximum stream. 0 = no stream */
	0x00, /* reserved for periodic endpoints */
	0x00, /* reserved for periodic endpoints */

	/* interface 1 */
	0x09, /* length */
	0x04, /* descriptor type, interface = 4 */
	0x00, /* interface value */
	0x01, /* alternative setting value */
	0x04, /* endpoints number */
	0x08, /* interface class, MASS SOTRAGE CLASS */
	0x06, /* interface sub-class, SFF8020i */
	0x62, /* interface protocol, UAS PROTOCAL */
	0x00, /* index of string descriptor for describing interface */

	/* endpoint 1, DATA IN pipe */
	0x07, /* length */
	0x05, /* descriptor type endpoint = 5 */
	0x81, /* Endpoint Address. D7: direction (0:out, 1:in), D6..4: reserved, */
	// D3..0: endpoint number
	0x02, /* attributes: 0:control, 1:isochronous, 2:bulk, 3:interrupt */
	SS_PACKET_SIZE_LB, /* Low Byte max packet size = 1024 */
	SS_PACKET_SIZE_HB, /* High Byte max packet size = 1024 */
	0x00, /* polling interval, 1 for isochronous, 1~255 for interrupt */

	/* endpoint 1 companion. */
	0x06, /* length */
	SUPERSPEED_0X30, /* descriptor type = endpoint companion */
	MAX_BURST, /* maximum burst. 0 = one packet per burst, 15 = 16 packets per burst */
	MAX_STREAMLOG, /* maximum stream. 0 = no stream */
	0x00, /* reserved for periodic endpoints */
	0x00, /* reserved for periodic endpoints */

	/* endpoint 1 pipe usage class specific descriptor */
	0x04, /* length */
	0x24, /* descriptor type = Usage class specific descriptor = 0x24 */
	0x03, /* pipe ID. data in pipe */
	0x00, /* reserved */

	/* endpoint 2, DATA-OUT pipe */
	0x07, /* length */
	0x05, /* Descriptor type endpoint = 5 */
	0x02, /* endpoint address: D7 for direction(0:out, 1:in), */
	// D3..0 endpoint number
	0x02, /* attribute: 0 control, 1:isochronous, 2:bulk, 3:interrupt */
	SS_PACKET_SIZE_LB, /* Low Byte max packet size = 1024 */
	SS_PACKET_SIZE_HB, /* High Byte max packet size = 1024 */
	0x00, /* polling interval */

	/* endpoint 2 companion. */
	0x06, /* length */
	SUPERSPEED_0X30, /* descriptor type = endpoint companion */
	MAX_BURST, /* maximum burst. 0 = one packet per burst, 15 = 16 packets per burst */
	MAX_STREAMLOG, /* maximum stream. 0 = no stream */
	0x00, /* reserved for periodic endpoints */
	0x00, /* reserved for periodic endpoints */

	/* endpoint 2 pipe usage class specific descriptor */
	0x04, /* length */
	0x24, /* descriptor type = Usage class specific descriptor = 0x24 */
	0x04, /* pipe ID. data out pipe */
	0x00, /* reserved */

	/* endpoint 3, STATUS pipe */
	0x07, /* length */
	0x05, /* descriptor type endpoint = 5 */
	0x83, /* Endpoint Address. D7: direction (0:out, 1:in), D6..4: reserved, */
	// D3..0: endpoint number
	0x02, /* attributes: 0:control, 1:isochronous, 2:bulk, 3:interrupt */
	SS_PACKET_SIZE_LB, /* Low Byte max packet size = 1024 */
	SS_PACKET_SIZE_HB, /* High Byte max packet size = 1024 */
	0x00, /* polling interval, 1 for isochronous, 1~255 for interrupt */

	/* endpoint 3 companion. */
	0x06, /* length */
	SUPERSPEED_0X30, /* descriptor type = endpoint companion */
	0x00, /* maximum burst. 0 = one packet per burst, 15 = 16 packets per burst */
	MAX_STREAMLOG, /* maximum stream. 0 = no stream */
	0x00, /* reserved for periodic endpoints */
	0x00, /* reserved for periodic endpoints */

	/* endpoint 3 pipe usage class specific descriptor */
	0x04, /* length */
	0x24, /* descriptor type = Usage class specific descriptor = 0x24 */
	0x02, /* pipe ID. status pipe */
	0x00, /* reserved */

	/* endpoint 4, COMMOND pipe */
	0x07, /* length */
	0x05, /* Descriptor type endpoint = 5 */
	0x04, /* endpoint address: D7 for direction(0:out, 1:in), */
	// D3..0 endpoint number
	0x02, /* attribute: 0 control, 1:isochronous, 2:bulk, 3:interrupt */
	SS_PACKET_SIZE_LB, /* Low Byte max packet size = 1024 */
	SS_PACKET_SIZE_HB, /* High Byte max packet size = 1024 */
	0x00, /* polling interval */

	/* endpoint 4 companion. */
	0x06, /* length */
	SUPERSPEED_0X30, /* descriptor type = endpoint companion */
	0x0F, // maximum burst. 0 = one packet per burst, 15 = 16 packets per burst */
	0x00, /* maximum stream. 0 = no stream */
	0x00, /* reserved for periodic endpoints */
	0x00, /* reserved for periodic endpoints */

	/* endpoint 4 pipe usage class specific descriptor */
	0x04, /* length */
	0x24, /* descriptor type = Usage class specific descriptor = 0x24 */
	0x01, /* pipe ID. command pipe */
	0x00, /* reserved */
};
U8 const gaubHSConfigDescriptor_UASP[] = {
	/*[CONFIG_LENGTH]={ 		// High Speed Configuration*/
	0x09, /* length */
	0x02, /* descriptor type configuration = 2 */
	CONFIG_LENGTH20_UASP, /* total length of CONFIG_LENGTH */
	0x00, /* CONFIG_LENGTH>>8, */
	0x01, /* number of interface */
	0x01, /* configuration value??? */
	0x00, /* index of string descriptor for configuration description */
	0x80, /* Configuration attributes D7: Reserved, must 1, D6: Self powered, D5: Remote wakeup */
	CONFIGURATION_DESCRIPTOR_HS_B8, /* max power, unit = 8mA, Self-Powered */

	/* interface 0 */
	0x09, /* length */
	0x04, /* descriptor type, interface = 4 */
	0x00, /* interface value */
	0x00, /* alternative setting value */
	0x02, /* endpoints number */
	0x08, /* interface class, MASS SOTRAGE CLASS */
	0x06, /* interface sub-class, SFF8020i */
	0x50, /* interface protocol, BOT PROTOCAL */
	0x00, /* index of string descriptor for describing interface */

	/* endpoint 1, DATA IN pipe */
	0x07, /* length */
	0x05, /* descriptor type endpoint = 5 */
	0x81, /* Endpoint Address. D7: direction (0:out, 1:in), D6..4: reserved, */
	// D3..0: endpoint number
	0x02, /* attributes: 0:control, 1:isochronous, 2:bulk, 3:interrupt */
	HS_PACKET_SIZE_LB, /* Low Byte max packet size = 512 */
	HS_PACKET_SIZE_HB, /* High Byte max packet size = 512 */
	0x00, /* polling interval, 1 for isochronous, 1~255 for interrupt */

	/* endpoint 2, DATA OUT pipe */
	0x07, /* length */
	0x05, /* Descriptor type endpoint = 5 */
	0x02, /* endpoint address: D7 for direction(0:out, 1:in), */
	// D3..0 endpoint number
	0x02, /* attribute: 0 control, 1:isochronous, 2:bulk, 3:interrupt */
	HS_PACKET_SIZE_LB, /* Low Byte max packet size = 512 */
	HS_PACKET_SIZE_HB, /* High Byte max packet size = 512 */
	0x00, /* polling interval */

	/* interface 1 */
	0x09, /* length */
	0x04, /* descriptor type, interface = 4 */
	0x00, /* interface value */
	0x01, /* alternative setting value */
	0x04, /* endpoints number */
	0x08, /* interface class, MASS SOTRAGE CLASS */
	0x06, /* interface sub-class, SFF8020i */
	0x62, /* interface protocol, UAS PROTOCAL */
	0x00, /* index of string descriptor for describing interface */

	/* endpoint 1, DATA IN pipe */
	0x07, /* length */
	0x05, /* descriptor type endpoint = 5 */
	0x81, /* Endpoint Address. D7: direction (0:out, 1:in), D6..4: reserved, */
	// D3..0: endpoint number
	0x02, /* attributes: 0:control, 1:isochronous, 2:bulk, 3:interrupt */
	HS_PACKET_SIZE_LB, /* Low Byte max packet size = 512 */
	HS_PACKET_SIZE_HB, /* High Byte max packet size = 512 */
	0x00, /* polling interval, 1 for isochronous, 1~255 for interrupt */

	/* endpoint 1 pipe usage class specific descriptor */
	0x04, /* length */
	0x24, /* descriptor type = Usage class specific descriptor = 0x24 */
	0x03, /* pipe ID. data in pipe */
	0x00, /* reserved */

	/* endpoint 2, DATA-OUT pipe */
	0x07, /* length */
	0x05, /* Descriptor type endpoint = 5 */
	0x02, /* endpoint address: D7 for direction(0:out, 1:in), */
	// D3..0 endpoint number
	0x02, /* attribute: 0 control, 1:isochronous, 2:bulk, 3:interrupt */
	HS_PACKET_SIZE_LB, /* Low Byte max packet size = 512 */
	HS_PACKET_SIZE_HB, /* High Byte max packet size = 512 */
	0x00, /* polling interval */

	/* endpoint 2 pipe usage class specific descriptor */
	0x04, /* length */
	0x24, /* descriptor type = Usage class specific descriptor = 0x24 */
	0x04, /* pipe ID. data out pipe */
	0x00, /* reserved */

	/* endpoint 3, STATUS pipe */
	0x07, /* length */
	0x05, /* descriptor type endpoint = 5 */
	0x83, /* Endpoint Address. D7: direction (0:out, 1:in), D6..4: reserved, */
	// D3..0: endpoint number
	0x02, /* attributes: 0:control, 1:isochronous, 2:bulk, 3:interrupt */
	HS_PACKET_SIZE_LB, /* Low Byte max packet size = 512 */
	HS_PACKET_SIZE_HB, /* High Byte max packet size = 512 */
	0x00, /* polling interval, 1 for isochronous, 1~255 for interrupt */

	/* endpoint 3 pipe usage class specific descriptor */
	0x04, /* length */
	0x24, /* descriptor type = Usage class specific descriptor = 0x24 */
	0x02, /* pipe ID. status pipe */
	0x00, /* reserved */

	/* endpoint 4, COMMOND pipe */
	0x07, /* length */
	0x05, /* Descriptor type endpoint = 5 */
	0x04, /* endpoint address: D7 for direction(0:out, 1:in), */
	// D3..0 endpoint number
	0x02, /* attribute: 0 control, 1:isochronous, 2:bulk, 3:interrupt */
	HS_PACKET_SIZE_LB, /* Low Byte max packet size = 512 */
	HS_PACKET_SIZE_HB, /* High Byte max packet size = 512 */
	0x00, /* polling interval */

	/* endpoint 4 pipe usage class specific descriptor */
	0x04, /* length */
	0x24, /* descriptor type = Usage class specific descriptor = 0x24 */
	0x01, /* pipe ID. command pipe */
	0x00, /* reserved */

};

AOM_USBC U8 const gaubSsConfigDescriptor_BOT[] = {
	/*[CONFIG_LENGTH] // Super Speed Configuration*/
	0x09U, /* length*/
	0x02U, /* descriptor type configuration = 2*/
	0x2cU, /*CONFIG_LENGTH,// total length*/
	0x00U, /*CONFIG_LENGTH>>8,*/
	0x01U, /* number of interface*/
	0x01U, /* configuration value*/
	0x00U, /* index of string descriptor for configuration description*/
	0x80U, /* Configuration attributes D7: Reserved, must 1, D6: Self powered, D5: Remote wakeup*/
	0x70U, /* max power, unit = 8mA, Self-Powered*/

	/* interface 0*/
	0x09U, /* length*/
	0x04U, /* descriptor type, interface = 4*/
	0x00U, /* interface value*/
	0x00U, /* alternative setting value*/
	0x02U, /* endpoints number*/
	0x08U, /* interface class, MASS SOTRAGE CLASS*/
	0x06U, /* interface sub-class, SFF8020i*/
	0x50U, /* interface protocol, BULK ONLY PROTOCAL*/
	0x00U, /* index of string descriptor for describing interface*/

	/* endpoint 1*/
	0x07U, /* length*/
	0x05U, /* descriptor type endpoint = 5*/
	0x81U, /* Endpoint Address. D7: direction (0:out, 1:in), D6..4: reserved,*/
	/* D3..0: endpoint number*/
	0x02U, /* attributes: 0:control, 1:isochronous, 2:bulk, 3:interrupt*/
	SS_PACKET_SIZE_LB, /* Low Byte max packet size = 1024 */
	SS_PACKET_SIZE_HB, /* High Byte max packet size = 1024 */
	0x00U, /* polling interval, 1 for isochronous, 1~255 for interrupt*/

	/*endpoint 1 companion.*/
	0x06U, /* length*/
	SUPERSPEED_0X30, /* descriptor type = endpoint companion */
	0x07U, /* maximum burst. 0 = one packet per burst, 15 = 16 packets per burst*/
	0x00U, /* maximum stream. 0 = no stream*/
	0x00U, /* reserved for periodic endpoints*/
	0x00U, /* reserved for periodic endpoints*/

	/*endpoint 2*/
	0x07U, /* length*/
	0x05U, /* Descriptor type endpoint = 5*/
	0x02U, /* endpoint address: D7 for direction(0:out, 1:in),*/
	/* D3..0 endpoint number*/
	0x02U, /* attribute: 0 control, 1:isochronous, 2:bulk, 3:interrupt*/
	SS_PACKET_SIZE_LB, /* Low Byte max packet size = 1024 */
	SS_PACKET_SIZE_HB, /* High Byte max packet size = 1024 */
	0x00U, /* polling interval*/

	/*endpoint 2 companion.*/
	0x06U, /* length*/
	SUPERSPEED_0X30, /* descriptor type = endpoint companion*/
	0x07U, /* maximum burst. 0 = one packet per burst, 15 = 16 packets per burst*/
	0x00U, /* maximum stream. 0 = no stream*/
	0x00U, /* reserved for periodic endpoints*/
	0x00U, /* reserved for periodic endpoints*/
};
U8 const gaubHSConfigDescriptor_BOT[] = {
	/*[CONFIG_LENGTH]={ 		// High Speed Configuration*/
	0x09U, /* length*/
	0x02U, /* descriptor type configuration = 2*/
	CONFIG_LENGTH20_BOT, /*CONFIG_LENGTH,	// total length*/
	0x00U, /*CONFIG_LENGTH>>8,*/
	0x01U, /* number of interface*/
	0x01U, /* configuration value???*/
	0x00U, /* index of string descriptor for configuration description*/
	0x80U, /* Configuration attributes D7: Reserved, must 1, D6: Self powered, D5: Remote wakeup*/
	CONFIGURATION_DESCRIPTOR_HS_B8, // max power, unit = 2mA, Self-Powered*/

	/* interface 0 (0)*/
	0x09U, /* length*/
	0x04U, /* descriptor type, interface = 4*/
	0x00U, /* interface value*/
	0x00U, /* alternative setting value*/
	0x02U, /* endpoints number*/
	0x08U, /* interface class, MASS SOTRAGE CLASS*/
	0x06U, /* interface sub-class, SFF8020i*/
	0x50U, /* interface protocol, BULK ONLY PROTOCAL*/
	0x00U, /* index of string descriptor for describing interface*/

	/* end point 1*/
	0x07U, /* length*/
	0x05U, /* descriptor type endpoint = 5*/
	0x81U, /* Endpoint Address. D7: direction (0:out, 1:in), D6..4: reserved,*/
	/* D3..0: endpoint number*/
	0x02U, /* attributes: 0:control, 1:isochronous, 2:bulk, 3:interrupt*/
	HS_PACKET_SIZE_LB, /* Low Byte max packet size = 512 */
	HS_PACKET_SIZE_HB, /* High Byte max packet size = 512 */
	0x00U, /* polling interval, 1 for isochronous, 1~255 for interrupt*/

	/* end point 2*/
	0x07U, /* length*/
	0x05U, /* Descriptor type endpoint = 5*/
	0x02U, /* endpoint address: D7 for direction(0:out, 1:in),*/
	/* D3..0 endpoint number*/
	0x02U, /* attribute: 0 control, 1:isochronous, 2:bulk, 3:interrupt*/
	HS_PACKET_SIZE_LB, /* Low Byte max packet size = 512 */
	HS_PACKET_SIZE_HB, /* High Byte max packet size = 512 */
	0x00U, /* polling interval*/
	/* end point 3*/
	/*0x07, // length*/
	/*0x05,	// descriptor type endpoint = 5*/
	/*0x83,	// Endpoint Address. D7: direction (0:out, 1:in), D6..4: reserved,*/
	/*      // D3..0: endpoint number*/
	/*0x03,	// attributes: 0:control, 1:isochronous, 2:bulk, 3:interrupt*/
	/*0x40,	// max packet size = 0x40*/
	/*0x00,*/
	/*0x04,	// polling interval, 1 for isochronous, 1~255 for interrupt*/
};
U8 const gaubFSConfigDescriptor_BOT[] = {
	/*[CONFIG_LENGTH]={ 		// Full speed configuration*/
	0x09U, /* length*/
	0x02U, /* descriptor type configuration = 2*/
	0x20U, /*CONFIG_LENGTH, // total length*/
	0x00U, /*CONFIG_LENGTH>>8,*/
	0x01U, /* number of interface*/
	0x01U, /* configuration value???*/
	0x00U, /* index of string descriptor for configuration description*/
	0x80U, /* Configuration attributes D7: Reserved, must 1, D6: Self powered, D5: Remote wakeup*/
	CONFIGURATION_DESCRIPTOR_FS_B8, /* max power, unit=2mA for hispeed. Self-Powered*/

	/* interface 0 (0)*/
	0x09U, /* length*/
	0x04U, /* descriptor type, interface = 4*/
	0x00U, /* interface value*/
	0x00U, /* alternative setting value*/
	0x02U, /* endpoints number*/
	0x08U, /* interface class, MASS SOTRAGE CLASS*/
	0x06U, /* interface sub-class, SFF8020i*/
	0x50U, /* interface protocol, BULK ONLY PROTOCAL*/
	0x00U, /* index of string descriptor for describing interface*/

	/* end point 1*/
	0x07U, /* length*/
	0x05U, /* descriptor type endpoint = 5*/
	0x81U, /* Endpoint Address. D7: direction (0:out, 1:in), D6..4: reserved,*/
	/* D3..0: endpoint number*/
	0x02U, /* attributes: 0:control, 1:isochronous, 2:bulk, 3:interrupt*/
	FS_PACKET_SIZE_LB, /* Low Byte max packet size = 64 */
	FS_PACKET_SIZE_HB, /* High Byte max packet size = 64 */
	0x00U, /* polling interval, 1 for isochronous, 1~255 for interrupt*/

	/* end point 2*/
	0x07U, /* length*/
	0x05U, /* Descriptor type endpoint = 5*/
	0x02U, /* endpoint address: D7 for direction(0:out, 1:in),*/
	/* D3..0 endpoint number*/
	0x02U, /* attribute: 0 control, 1:isochronous, 2:bulk, 3:interrupt*/
	FS_PACKET_SIZE_LB, /* Low Byte max packet size = 64 */
	FS_PACKET_SIZE_HB, /* High Byte max packet size = 64 */
	0x00U, /* polling interval*/

	/* end point 3*/
	/*0x07, // length*/
	/*0x05, // descriptor type endpoint = 5*/
	/*0x83, // Endpoint Address. D7: direction (0:out, 1:in), D6..4: reserved,*/
	/* D3..0: endpoint number*/
	/*0x03, // attributes: 0:control, 1:isochronous, 2:bulk, 3:interrupt*/
	/*0x40, // max packet size = 0x40*/
	/*0x00,*/
	/*0x01, // polling interval, 1 for isochronous, 1~255 for interrupt*/
};

AOM_USBC U8 const gaubBinaryObjectStore[] = {
	0x05U, /* bLength*/
	0x0fU, /* bDescriptorType = 0x0f = BOS*/
	0x2AU, /* wTotalLength, for this descriptors and all the other descriptors*/
	0x00U, /* wTotalLength, high byte*/
	0x03U, /* bNumDeviceCaps, number of device capabilities*/

	0x07U, /* bLength, for the descriptor of USB 2.0 extension*/
	0x10U, /* bDescriptorType = 16 = device capability*/
	0x02U, /* bDeviceCapabilityType, 02 = USB 2.0 extension descriptor*/
	0x06U, /* bmAttribute, bit 1 = LPM capability (Link Power Management)*/
	0x00U, /* reserved*/
	0x00U, /* reserved*/
	0x00U, /* reserved*/

	0x0aU, /* bLength, for the descriptor of superspeed USB capability*/
	0x10U, /* bDescriptorType = 16 = device capability*/
	0x03U, /* bDeviceCapabilityType, 03 = supper speed USB specific device level capability*/
	0x00U, /* 0x02U, bmAttribute, bit 1 = LTM capability (Latency Tolerance Messages capability*/
	0x0eU, /* wSpeedSupported, bit3= SuperSpeed, bit2=hi_speed, bit1=full_speed, bit0=low_speed*/
	0x00U, /* wSpeedSupported, high byte*/
	0x02U, /* bFunctionalitySupport. which lowest speed supporte all the function. bit1=full speed*/
	0x0aU, /* bU1DevExitLat, U1 device exit latency. latency to transition from U1 to U0, unit = usec*/
	0xffU, /* wU2DevExitLat, U2 device exit latency. latency to transition from U2 to U0, unit = usec*/
	0x07U, /* wU2DevExitLat, high byte. 0x0800 us = 2048us*/

	//SuperSpeedPlus USB Device Capability
	0x14, 					//bLength, Descriptor size is 20 bytes
	0x10,					//bDescriptorType, DEVICE_CAPABILITY Descriptor Type
	0x0A, 					//bDevCapabiltyType, SUPERSPEED_PLUS
	0x00, 					//bReserved, Reserved
	0x01, 0x00, 0x00, 0x00, //bmAttributes, Bitmap encoding of supported SuperSpeedPlus features:
	//Bit	 Value			  Description
	//4:0	 01 				Sublink Speed Attribute Count (SSAC)
	//8:5	 00 				Sublink Speed ID Count (SSIC)
	//31:9	0x00000000	  Reserved
	0x00, 0x11, 			//wFunctionalitySupport, The device shall support full functionality at all reported
	//Bit	 Value			  Description
	//3:0	   00				  Sublink Speed Attribute ID (SSID)
	//7:4	   00				  Reserved. Shall be set to zero
	//11:8	  01				 Min Rx Lane Count
	//15:12  01 				Min Tx Lane Count
	0x00, 0x00, //wReserved, Reserved. Shall be set to zero.
	0x30, 0x40, 0x0A, 0x00, //bmSublinkSpeedAttr[0], Bitmap encoding of a Sublink characteristics:
	//Bit	 Value			  Description
	//3:0	 00 				Sublink Speed Attribute ID (SSID)
	//5:4	 03 				Lane Speed Exponent (LSE)
	//6 	   0				 Sublink Type: Symmetric
	//7 	   0				 Sublink Type: Sublink operates in Receive mode
	//13:8	  0x0			  Reserved
	//15:14   1 			   Link Protocol: SuperSpeedPlus
	//31:16   0x000A	  Lane Speed Mantissa (LSM)
	0xB0, 0x40, 0x0A, 0x00, //	bmSublinkSpeedAttr[1], Bitmap encoding of a Sublink characteristics:
	//Bit	 Value			  Description
	//3:0	 00 				Sublink Speed Attribute ID (SSID)
	//5:4	 03 				Lane Speed Exponent (LSE)
	//6 	   0				 Sublink Type: Symmetric
	//7 	   1				 Sublink Type: Sublink operates in Transmit mode
	//13:8	  0x0			  Reserved
	//15:14   1 			   Link Protocol: SuperSpeedPlus
	//31:16   0x000A	  Lane Speed Mantissa (LSM)


};
AOM_USBC U8 const gaubDeviceQualifier[] = {
	/*[DEVICE_QUALIFIER_LENGTH]=*/
	10U,   /*DEVICE_QUALIFIER_LENGTH,// length*/
	0x06U, /*DEVICE_QUALIFIER, // Device Qualifier Type*/
	0x00U, /* USB version: 0x0200, little endian*/
	0x02U,
	0x00U, /* device class, FS*/
	0x00U, /* device sub-class, FS*/
	0x00U, /* device protocol, FS*/
	0x40U, /* max packet size, FS*/
	0x01U, /* Number of Other-speed Configurations, FS*/
	0x00U  /* Reserved*/
};
AOM_USBC U8 const  gaubOtherSpeedConfigurationHS[0x20U] = {
	/*[OTHER_SPEED_CONFIGURATION_LENGTH]={*/
	0x09U, /* bLength*/
	0x07U, /* OTHER_SPEED_CONFIGURATION,  // bDescriptorType*/
	0x20U, /* OTHER_SPEED_CONFIGURATION_LENGTH,// wTotalLength*/
	0x00U, /* OTHER_SPEED_CONFIGURATION_LENGTH>>8,*/
	0x01U, /* bNumberInterfaces*/
	0x01U, /* bConfigurationValue*/
	0x00U, /* iConfiguration*/
	0x80U, /* Configuration attributes D7: Reserved, must 1, D6: Self powered,*/
	/* D5: Remote wakeup*/
	0xF9U, /* bMaxPower, mA*/

	/* interface 0 (0)*/
	0x09U, /* length*/
	0x04U, /* descriptor type, interface = 4*/
	0x00U, /* interface value*/
	0x00U, /* alternative setting value*/
	0x02U, /* endpoints number*/
	0x08U, /* interface class, MASS SOTRAGE CLASS*/
	0x06U, /* interface sub-class, SFF8020i*/
	0x50U, /* interface protocol, BULK ONLY PROTOCAL*/
	0x00U, /* index of string descriptor for describing interface*/

	/* endpoint 1*/
	0x07U, /* length*/
	0x05U, /* descriptor type endpoint = 5*/
	0x81U, /* Endpoint Address. D7: direction (0:out, 1:in), D6..4: reserved,*/
	/* D3..0: endpoint number*/
	0x02U, /* attributes: 0:control, 1:isochronous, 2:bulk, 3:interrupt*/
	0x40U, /*EPASIZE, // max packet size = 0*/
	0x00U,
	0x00U, /* polling interval, 1 for isochronous, 1~255 for interrupt*/

	/* endpoint 2*/
	0x07U, /* length*/
	0x05U, /* Descriptor type endpoint = 5*/
	0x02U, /* endpoint address: D7 for direction(0:out, 1:in),*/
	/* D3..0 endpoint number*/
	0x02U, /* attribute: 0 control, 1:isochronous, 2:bulk, 3:interrupt*/
	0x40U, /* max packet size*/
	0x00U, /**/
	0x00U, /* polling interval*/

	/* end point 3*/
	/*0x07, // length*/
	/*0x05, // descriptor type endpoint = 5*/
	/*0x83, // Endpoint Address. D7: direction (0:out, 1:in), D6..4: reserved,*/
	/*      //D3..0: endpoint number*/
	/*0x03, // attributes: 0:control, 1:isochronous, 2:bulk, 3:interrupt*/
	/*0x40, // max packet size = 0x40*/
	/*0x00,*/
	/*0x04, // polling interval, 1 for isochronous, 1~255 for interrupt*/
};
AOM_USBC U8 const gaubOtherSpeedConfigurationFS[0x20U] = {
	/*[OTHER_SPEED_CONFIGURATION_LENGTH]={*/
	0x09U, /* bLength*/
	0x07U, /* OTHER_SPEED_CONFIGURATION, // bDescriptorType*/
	0x20U, /* OTHER_SPEED_CONFIGURATION_LENGTH, // wTotalLength*/
	0x00U, /* OTHER_SPEED_CONFIGURATION_LENGTH>>8,*/
	0x01U, /* bNumberInterfaces*/
	0x01U, /* bConfigurationValue*/
	0x00U, /* iConfiguration*/
	0x80U, /* Configuration attributes D7: Reserved, must 1, D6: Self powered,*/
	/* D5: Remote wakeup*/
	0xF9U, /* bMaxPower, mA*/

	/* interface 0 (0)*/
	0x09U, /* length*/
	0x04U, /* descriptor type, interface = 4*/
	0x00U, /* interface value*/
	0x00U, /* alternative setting value*/
	0x02U, /* endpoints number*/
	0x08U, /* interface class, MASS SOTRAGE CLASS*/
	0x06U, /* interface sub-class, SFF8020i*/
	0x50U, /* interface protocol, BULK ONLY PROTOCAL*/
	0x00U, /* index of string descriptor for describing interface*/

	/* endpoint 1*/
	0x07U, /* length*/
	0x05U, /* descriptor type endpoint = 5*/
	0x81U, /* Endpoint Address. D7: direction (0:out, 1:in), D6..4: reserved,*/
	/* D3..0: endpoint number*/
	0x02U, /* attributes: 0:control, 1:isochronous, 2:bulk, 3:interrupt*/
	0x00U, /*EPASIZE, // max packet size = 512*/
	0x02U,
	0x00U, /* polling interval, 1 for isochronous, 1~255 for interrupt*/

	/* endpoint 2*/
	0x07U, /* length*/
	0x05U, /* Descriptor type endpoint = 5*/
	0x02U, /* endpoint address: D7 for direction(0:out, 1:in),*/
	/* D3..0 endpoint number*/
	0x02U, /* attribute: 0 control, 1:isochronous, 2:bulk, 3:interrupt*/
	0x00U, /* max packet size*/
	0x02U, /**/
	0x00U, /* polling interval*/

	/* end point 3*/
	/*0x07,		// length*/
	/*0x05,		// descriptor type endpoint = 5*/
	/*0x83,		// Endpoint Address. D7: direction (0:out, 1:in), D6..4: reserved,*/
	/*			//	D3..0: endpoint number*/
	/*0x03,		// attributes: 0:control, 1:isochronous, 2:bulk, 3:interrupt*/
	/*0x40,	// max packet size = 0x40*/
	/*0x00,*/
	/*0x01,		// polling interval, 1 for isochronous, 1~255 for interrupt*/
};

U8 gaubResponseBuffer[128];

#endif /* (USB == HOST_MODE) */

