#ifndef _FTL_BFEA_H_
#define _FTL_BFEA_H_

#include "tt/TT_api.h"
#if BFEA_EN
#define BFEA_TEST_EN						(FALSE)
#define BFEA_GEN_TEST_PATTERN_EN			(FALSE)
#define BFEA_SMART_RESYNC_BECBINSEARCH_EN 	(FALSE)
#define BFEA_UNIT_LIST_VERIFY_EN			(TRUE)

#define BFEA_BF_SCAN_TEST_NO_ORDER_EN	(TRUE)

#define BFEA_BF_TOTAL_NUM	(64)
#define BFEA_BF_TOTAL_NUM_MASK	(0x3F)
#define BFEA_MAX_DIE_NUM	(32)
#define BFEA_MAX_DIE_NUM_PER_CHANNEL (8)
#define BFEA_INVALID_BF		(0xFF)
#define BFEA_INVALID_INFO		(0xFF)
#define BFEA_INVALID_INFO_WORD	(0xFFFF)
#define BFEA_INVALID_INFO_LONG	(0xFFFFFFFF)
#define BFEA_UNIT_LIST_INVALID_UNIT	(0x3FF)

#define BFEA_BF_SCAN_UNIT_NUM (0x3)
#define BFEA_BF_SCAN_DIE_NUM (16)
#define BFEA_BF_SCAN_PAGE_NUM (0x3)
#define BFEA_BF_SCAN_MAX_OLDEST_BF_NUM	(2)
#define BFEA_BF_SCAN_WLG2_XP_PAGE_BASE	(1138)
#define BFEA_BF_SCAN_WLG2_XP_PAGE_NUM	(151)
#define BFEA_BF_SCAN_RSO_CNT_PER_TRIM	(4)
#define BFEA_SCAN_MAX_TIME	(10)

#define BFEA_ACTIVE_BF_OLDEST	(0xDD)
#define BFEA_ACTIVE_BF_YOUNGEST (0xDE)

#define BFEA_MAX_SAMPLE_MOD	(BFEA_BIN7)//Total 6:  1->2 2->3 3->4 4->5 5->6 6->7
#define BFEA_MAX_UNIT_NUM		(556)//(745)

#define BFEA_DIE_NUM (gubDieNumber*gubCENumber)

#define BFEA_MEASURE_TRIM_REGISTER (0x0016)

#define BFEA_AVERAGE_BECBINSEARCH_BIN_NUM	(4)

#define BFEA_BECBINSEARCH_MAX_TIME	(10*BFEA_BF_SCAN_UNIT_NUM*(BFEA_BF_SCAN_PAGE_NUM+1)*BFEA_AVERAGE_BECBINSEARCH_BIN_NUM)

#define BFEA_LPM_WAKE_MAX_TIME_DIFFERENCE	(3600000)//1hr

#define BFEA_BECBIN_SEARCH_BLOCK_NUM (3)
#define BFEA_BEC_BIN_SEARCH_PAGE_NUM	(3)

#define BFEA_GEN_CRC_FAIL_SEED_INIT		(0xFFFF)

#define BFEA_BEC_BIN_SEARCH_CMD_MT	(0)
#define BFEA_BEC_BIN_SEARCH_DMA_MT	(1)

#define BFEA_CODEWORD_PER_FRAME_SHIFT	(1)

// Terry Question 0721, Limit of ECC?
#define BFEA_UECC_LIMIT_BIT_NUM			(150)
#define BFEA_BEC_UNC_MAX_BIT_CNT	(BFEA_UECC_LIMIT_BIT_NUM << BFEA_CODEWORD_PER_FRAME_SHIFT)

#define BFEA_BEC_BIN_SEARCH_BEC_OFFSET	(5)
#define BFEA_BEC_BIN_SEARCH_4K_FRAME_CRITERIA	(28)

#define BFEA_QUICK_SYNC_LIMIT_TIME	(100)

#define BFEA_CALCULATE_BLOCK_HEAD_VCA(uwUnitTemp, ubDieTemp)        ((uwUnitTemp << gub4kEntrysPerUnitAlignLog) + (ubDieTemp << (gubBurstsPerBankLog + gub4kEntrysPerPlaneLog)))

#define M_GET_BFEA_TIMER()            ((U32)(M_GET_FW_TIMER() + (guoFWStartTime * MILLISECOND_PER_SECOND)))

enum SAVE_BFEA_TABLE_REASON {
	BFEA_SAVE_TABLE_NONE,
	BFEA_SAVE_TABLE_INIT_NEW_TABLE,
	BFEA_SAVE_TABLE_BIND_BF,
	BFEA_SAVE_TABLE_UNBIND_BF,
	BFEA_SAVE_TABLE_RETURN_BF,
	BFEA_SAVE_TABLE_QUICK_SYNC,
	BFEA_SAVE_TABLE_SMART_RESYNC,
	BFEA_SAVE_TABLE_BF_SCAN,
};

enum CREATE_BF_REASON {
	BFEA_NO_CREATE_BF,
	BFEA_CREATE_BF_ELAPSE_TIME,
	BFEA_CREATE_BF_TEMP_DIFF,
	BFEA_CREATE_BF_QUICK_SYNC
};

#define M_BFEA_MEASURE_RAND_PICK_WLG_XP_PAGE() (BFEA_BF_SCAN_WLG2_XP_PAGE_BASE + ((U32)RngGetRandomValue() % BFEA_BF_SCAN_WLG2_XP_PAGE_NUM) * 3)

AOM_BFEA void BFEAWriteTableBackNVM(void);
AOM_BFEA void BFEAReadTableFromNVM(void);
AOM_BFEA void BFEABECBinSearchReadDataMT(U8 ubMTIdx, U8 ubQIdx, U32 ulFSA, U8 ubBin, U8 ubMode);
AOM_BFEA U8 BFEABECBinSearch(U16 uwUnit, U8 ubGlobalDie, U8 ubStartBin);
AOM_BFEA void BFEABFSort(U8 ubVictimBF, U8 ubBFMinBin, U8 ubBFOriginalBin);
AOM_BFEA U8 BFEABFPoolPop(void);
AOM_BFEA void BFEABFPoolPush(U8 ubBF);

/*SMART RESYNC*/
AOM_BFEA void BFEASmartReSyncInit(void);
AOM_BFEA void BFEASmartReSyncFindMeasureEndBF(void);
AOM_BFEA void BFEASmartReSyncFindMeasureStartBF(void);
AOM_BFEA void BFEASmartReSyncBFMeasure(void);
AOM_BFEA void BFEASmartReSync(void);

/*QUICK SYNC*/
AOM_BFEA void BFEAQuickSyncInit(void);
AOM_BFEA void BFEAQuickSyncFindMeasureEndBF(void);
AOM_BFEA void BFEAQuickSyncFindMeasureStartBF(void);
AOM_BFEA void BFEAQuickSyncRead(void);
AOM_BFEA void BFEAQuickSync(void);

//Terry Debug BFEA 0728
#if DEBUG_BFEA_BEC_BIN_SEARCH_DEBUG
AOM_BFEA void BFEAReadTestBECBinSearch(void);
#endif /*DEBUG_BFEA_BEC_BIN_SEARCH_DEBUG*/

/*BF SCAN*/
AOM_BFEA void BFEANewBF(U16 uwUnit);
FW_BTCM0_SECTION void BFEATimer(void);
AOM_BFEA void BFEABFScan(void);
AOM_BFEA void BFEABFScanFindOldestBF(void);
AOM_BFEA void BFEAFindMeasureList(U8 ubMeasureBF);
AOM_BFEA void BFEABFScanTableUpdateBlockFamily(void);
AOM_BFEA U8 BFEAGetBFMinBinPtr(U8 ubBF);
AOM_BFEA void BFEABFScanTableUpdate(void);
AOM_BFEA void BFEABFScanCheckRoundAndTableUpdate(void);
AOM_BFEA U8 BFEABFValley7OffsetToBin(S16 swOffset);

/*Test & Debug*/
AOM_BFEA void BFEATablePrint();
#if BFEA_GEN_TEST_PATTERN_EN
AOM_BFEA void BFEATestRandomIncreaseBFBin(U8 ubBF);
AOM_BFEA void BFEATestQuickSyncIncreaseBFBin(void);
AOM_BFEA U8 BFEATestGetBFMinBinPtr(U8 ubBF);
AOM_BFEA void BFEATestSmartReSyncIncrease(U8 ubBF, U8 ubSelectDie);
#endif /*BFEA_GEN_TEST_PATTERN_EN*/
#endif/*BFEA_EN*/
#endif/*_FTL_BFEA_H_*/
