/** @file buffer_queue.h
 *  @brief
 *
 *
 *  <AUTHOR>  @bug No know bugs.
 */

#ifndef _BUFFER_QUEUE_H_
#define _BUFFER_QUEUE_H_

#include "typedef.h"

//******************************************
//	Define
//******************************************
#define BQ_NUM	32

//******************************************
//	Struct Def
//******************************************

typedef struct BufferQueue {
	U8 ubNext;
	U8 ubPrevious;
	U16 uwBufferIndex;
	U32 ulSectorCnt;
	U32 ulVir4kIndex;
	U32 ulDataMask;
	U32 ulSpareValid;
	U8 ubZcode[4];
	U8 ubRBQIIndex;
	U8 btWrite : 1;
	U8 ub4kNum : 4;
	U8 btNoUse : 3;
} BufferQueue_t;

typedef struct {
	U8 ubLinkFirst;
	U8 ubLinkLast;
	U8 ubLinkLastUse;
	U8 ubLinkActive;
	U8 ubLinkNum;
	U8 ubWLinkNum;
	U32 ulHostWriteWaitBufferFlag;
	BufferQueue_t BufferQueue[BQ_NUM];
} BufferQueueInfo_t;

//******************************************
//	Marco
//******************************************

#define M_ADD_TO_BUFFER_QUEUE(ubLink) do { \
	gBQI.ubLinkLastUse = ubLink; \
	gBQI.ubLinkNum++; \
	gBQI.ubLinkActive=0xFF; \
} while(0)

#define M_REMOVE_WRITE_BUFFER_QUEUE(ubLink) do{\
                        gBQI.ubLinkNum--;\
                        if ((ubLink)!=gBQI.ubLinkLast){\
                                if ((ubLink)==gBQI.ubLinkFirst) {\
                                        gBQI.ubLinkFirst = gBQI.BufferQueue[ubLink].ubNext;\
                                        gBQI.BufferQueue[gBQI.ubLinkFirst].ubPrevious = ubLink;\
                                        gBQI.BufferQueue[ubLink].ubPrevious = gBQI.ubLinkLast;\
                                        gBQI.BufferQueue[ubLink].ubNext = gBQI.ubLinkFirst;\
								}\
								else {\
                                        U8 ubPrevious = gBQI.BufferQueue[ubLink].ubPrevious;\
                                        U8 ubNext = gBQI.BufferQueue[ubLink].ubNext;\
                                        if((ubLink)==gBQI.ubLinkLastUse){\
                                                gBQI.ubLinkLastUse = gBQI.BufferQueue[ubLink].ubPrevious;\
										}\
                                        gBQI.BufferQueue[ubPrevious].ubNext = ubNext;\
                                        gBQI.BufferQueue[ubNext].ubPrevious = ubPrevious;\
                                        gBQI.BufferQueue[ubLink].ubPrevious = gBQI.ubLinkLast;\
                                        gBQI.BufferQueue[ubLink].ubNext = gBQI.ubLinkFirst;\
                                        gBQI.BufferQueue[gBQI.ubLinkFirst].ubPrevious=ubLink;\
		                       }\
                                gBQI.BufferQueue[gBQI.ubLinkLast].ubNext = ubLink;\
                                gBQI.ubLinkLast = ubLink;\
						}\
						else{\
                                gBQI.ubLinkLastUse = gBQI.BufferQueue[ubLink].ubPrevious;\
						}\
}while(0)


//******************************************
//	Variable
//******************************************
extern BufferQueueInfo_t	gBQI;

//******************************************
//	Function
//******************************************
void BufferQueueInit();

#endif /* _BUFFER_QUEUE_H_ */