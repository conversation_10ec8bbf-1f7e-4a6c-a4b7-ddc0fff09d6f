#ifndef _FW_TAGID_H_
#define _FW_TAGID_H_

#include "setup.h"
#include "typedef.h"
#include "aom/aom_api.h"

#define HW_NUM					(6)
#define TAG_NUM					(254)		//Tag num for each hw  (1 ~ 255) , 0 for active cmd tag
#define TAG_ID_BASE				(1)
#define TAG_MASK				(0xFF)
#define TAG_ID_DEFAULT			(0)			// Default Tag ID 0, not in tag pool.
#define TAG_ID_ALLOCATE			(0x1000)	// Used in COP0 command
#define DEFAULT_ERASE_TAG_ID	(0xFFFD)
#define TAG_ID_COP0_MAX_TAG		(TAG_ID_BASE + TAG_NUM)

#define DB_MGR_COP0_EXT_TAG_SKIP_ERROR			(BIT13) // Skip error
#define DB_MGR_COP0_EXT_TAG_SYSTEM_AREA			(BIT12) // Read: HB Retry only, Program/Erase:  Retrun COP0 CQ with error MSG type 
#define DB_MGR_COP0_EXT_TAG_BACKUPP4KWROKAROUND_PROGRAM_BIT		(BIT10) // Read: HB Retry only, Program/Erase:  Retrun COP0 CQ with error MSG type 
#define DB_MGR_COP0_EXT_TAG_COP1_SIGN_BIT		(BIT15) // FW extend tag should be skip this bit

typedef struct {
	U16 uwPool[TAG_NUM];
	U16 uwNumInPool;
	U16 uwFront;
	U16 uwRear;
} tag_id_t;

typedef enum {
	COP0_TAG_POOL_ID = 0,
	COP1_TAG_POOL_ID,
	BMU_TAG_POOL_ID,
	DMAC_TAG_POOL_ID,
	XZIP_TAG_POOL_ID,
	RAIDECC_TAG_POOL_ID
} tag_pool_id_t;

AOM_INIT void FTLInitTagIdPool(void);
U16 FTLPopTagPool(U8 ubTagPoolId);
void FTLPushTagPool(U8 ubTagPoolId, U16 ubTagId);
U16 FTLGetTagPoolNum(U8 ubTagPoolId);
#if RDT_MODE_EN
U16 FTLGetTagID(U8 ubTagPoolId);
#endif

#endif /* _FW_TAGID_H_ */
