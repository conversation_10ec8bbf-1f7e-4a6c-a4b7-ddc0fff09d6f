#ifndef SRC_FW_FTL_FTL_LPM_H_
#define SRC_FW_FTL_FTL_LPM_H_

#include "env.h"
#include "setup.h"
#include "typedef.h"
#include "aom/aom_api.h"
#include "hal/dmac/dmac_api.h"
#include "hal/xzip/xzip_api.h"

#define LPM_BACKUP_COP1_ST1					((volatile U32 *)LPM_ST1_REG_TEMP_ADDR)

#define LPM_TOTAL_MODE_NUM				(3)

#define LPM_WAIT_RTT0_LIMIT				(1)

#define LPM_POWERSTATE_BITMAP_INDEX     (1)

#define LPM_SIM_REG_0_DEFAULT_VALUE (0xFF)
#define LPM_SIM_REG_8_DEFAULT_VALUE (0xFF)

#if (!PS5017_EN)
#define M_LPM_RECORD_DRIVING_FAIL(x)		((((U8 *)DBUF_SET_DRIVING_FAIL_CNT_ADDRESS)[0]) = (x))
#define M_LPM_GET_DRIVING_FAIL()			((((U8 *)DBUF_SET_DRIVING_FAIL_CNT_ADDRESS)[0]))
#define M_LPM_RECORD_ODT_FAIL(x)		((((U8 *)DBUF_SET_ODT_FAIL_CNT_ADDRESS)[0]) = (x))
#define M_LPM_GET_ODT_FAIL()			((((U8 *)DBUF_SET_ODT_FAIL_CNT_ADDRESS)[0]))
#define M_LPM_RECORD_FC_RETRY_FAIL(x)		((((U8 *)DBUF_FC_RETRY_FAIL_CNT_ADDRESS)[0]) = (x))
#define M_LPM_GET_FC_RETRY_FAIL()			((((U8 *)DBUF_FC_RETRY_FAIL_CNT_ADDRESS)[0]))
#define M_LPM_RECORD_FC_RETRY(x)		((((U8 *)DBUF_FC_RETRY_CNT_ADDRESS)[0]) = (x))
#define M_LPM_GET_FC_RETRY()			((((U8 *)DBUF_FC_RETRY_CNT_ADDRESS)[0]))
#endif /* (!PS5017_EN) */

#define M_LPM_RECORD_SLC_POOL_NUM(x)		(((U32 *)DBUF_LPM_SLC_POOL_NUM_ADDRESS)[0] = (x))
#define M_LPM_GET_RECORED_SLC_POOL_NUM()	(((U32 *)DBUF_LPM_SLC_POOL_NUM_ADDRESS)[0])
#define M_LPM_SET_EXTRA_BACK_UP_FLAG(x)		(((U8 *)DBUF_LPM_EXTRA_BACKUP_FLAG_ADDRESS)[0] = (x))
#define M_LPM_RECORD_FW_BUFFER_SEQUENCE(x,y)		(((U8 *)DBUF_LPM_FW_BUFFER_SEQUENCE_ADDRESS)[y] = (x))
#define M_LPM_GET_FW_BUFFER_SEQUENCE(y)	(((U8 *)DBUF_LPM_FW_BUFFER_SEQUENCE_ADDRESS)[y])

//Case define:
typedef enum {
	LPM_ADMINCMD = 0,
	LPM_NVMECMD
} LPMWakeCMDType_t;

//Parameter define:
#define LPM_INVALID_BACKUP_PB_OFFSET	(0xFFFF)

typedef enum {
	LPM_FW_CHECK_HAS_TABLE_DOING = BIT0,
	LPM_FW_CHECK_HAS_TABLE_EVENT_WAITING = BIT1,
	LPM_FW_CHECK_FTL_NOT_ZERO = BIT2,
	LPM_FW_CHECK_READ_VERIFY_DOING = BIT3,
	LPM_FW_CHECK_READ_DISTURB_DOING = BIT4,
	LPM_FW_CHECK_HAS_BARRIER_EVENT = BIT5,
	LPM_FW_CHECK_CLEAR_XZIP_DOING = BIT6,
	LPM_FW_CHECK_MEDIA_SCAN_DOING = BIT7,
} LPMCheckFWBitmap_t;

//Macro
#define M_LPM_APST_RTT_FLAG_CLEAR(x)	    do { \
                                                 R32_SYS0_RTT[R32_SYS0_RTT_TO_LMTH] = RTT_HIGH_MAX_LIMIT; \
                                                 gLPM.ubRtt0INTFlag = FALSE;\
                                            } while (0)

//Function:
/* The following three callbacks will be called in LPM flow, means that code banking will not occur.
    So they can put into LPM's bank. */
AOM_LPM void LPMCalculateDBUFCRC_Callback(DMACCQRst_t *pDMACCQRst);
AOM_LPM void LPMClearXZIP_Callback(XZIPCQRst_t *pXZIPCQRst);
AOM_LPM void LPMRecoverTable_CallBack(void);

AOM_LPM void LPMWriteCheck(void);
AOM_LPM void LPMReadCheck(void);
AOM_LPM void LPMTableCheck(void);
AOM_LPM void LPMPreReadCheck(void);
AOM_LPM void LPMCheckIPIdle(void);
AOM_LPM void LPMTableBackUp(void);
AOM_LPM void LPMRecoverDBUF(void);
#if (!PS5017_EN)
AOM_LPM void LPMMergeToTemp(void);
AOM_LPM void LPMRecoverFromTemp(void);
#endif /* (!PS5017_EN) */
AOM_LPM void LPMStoreTable(void);
AOM_LPM void LPMRegisterGPIOWakeup(void);
AOM_LPM U8 LPMCheckFWFlowStatus(void);
AOM_LPM void LPMPasteTempDriveLog(void);
AOM_LPM void LPMRecoverHostTable(void);
AOM_LPM void LPMStoreHostTable(void);
AOM_LPM void LPMCalculateDBUFCRC(void);
#if(DELL_PERSISTENT_EVENT_LOG_EN)
AOM_LPM U8 LPMCheckPowerStateNeedTableBackup(void);
#endif /* (DELL_PERSISTENT_EVENT_LOG_EN) */
#if (NVME == HOST_MODE)
AOM_LPM void LPMParsingFWBufferOffset(void);
AOM_LPM U8 LPMNVMeWakeUpCMD(void);
AOM_LPM void LPMSetCMGRIdleInterrupt(void);
#endif

#endif /* SRC_FW_FTL_FTL_LPM_H_ */
