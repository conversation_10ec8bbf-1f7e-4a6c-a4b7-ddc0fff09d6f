#ifndef _FTL_XFER_DATA_OUT_H_
#define _FTL_XFER_DATA_OUT_H_

#include "aom/aom_api.h"

#if PS5021_EN
#define MULTI_RCQ_NUM		(DB_APU_RD_CQ_DEPTH)
#else /* PS5021_EN */
#define MULTI_RCQ_NUM		(DB_BMU_CQ_DEPTH)
#endif /* PS5021_EN */

#define FROM_RCQ			(BIT0)
#define FROM_CALLBACK		(BIT1)

#define DEFAULT_RCQ_OFFSET	(0xFF)
#define DEFAULT_RCQ_SOURCE	(0)
#define FTL_BLOCK_GC_RD_QD_CNT (4)

typedef struct {
	U8 ubRCQSource;			//check FROM_CALLBACK or FROM_RCQ
	RCQOffset_t RPTR;
	RCQOffset_t WPTR;
	RCQOffset_t ReadCount;			//now in queue
	RCQOffset_t MultiRCQNum;		//handling total RCQ number
	RCQOffset_t Queue[MULTI_RCQ_NUM];
} MultiRCQ_t;

typedef union {
	U64 uoAll;
	struct {
#if PS5021_EN
		U64 uwRLBPBAddr 		: 10; // Fw allocate PB for RLB
		U64 uwPLB2ndPBAddr		: 10; // PLB cross frame PB offset
		U64 ubVldBmp 	        : 8;
		U64 ubOffsetInPLB		: 3; // Start offsert in PLB
		U64 ubResv              : 1;
#else /* PS5021_EN */
		U64 uwRLBPBAddr 		: 9; // Fw allocate PB for RLB
		U64 uwPLB2ndPBAddr		: 9; // PLB cross frame PB offset
		U64 ubVldBmp 	        : 8;
		U64 ubOffsetInPLB		: 3; // Start offsert in PLB
		U64 ubResv              : 3;
#endif /* PS5021_EN */
		U64 ulE3D4K				: 24;
		U64	ubZInfo				: 6;
		U64 btZeroFlag			: 1;
		U64 ubResv2             : 1;
	};
} RCQExtInfo_t;

extern MultiRCQ_t gMultiRCQMgr;
extern RCQExtInfo_t gRCQExtInfo[MULTI_RCQ_NUM];
AOM_INIT_2 void MultiRCQInit(void);
U8 MultiRCQCheckFull(void);
void FTLXferDataOutHitLAPreProcess(U8 ubHitMode, RCQOffset_t Offset);
void FTLXferDataOutHitLAEventDone(U8 ubHitMode, RCQOffset_t Offset);

#endif /* _FTL_XFER_DATA_OUT_H_ */
