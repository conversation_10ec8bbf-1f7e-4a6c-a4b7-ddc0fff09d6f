/*
 * rdt_log.c
 *
 *  Created on: 20202る18ら
 *      Author: user
 */

#include "rdt_api.h"

#if (RDT_MODE_EN)

#if ENABLE_PH
#include "vuc/VUC_ScanPH.h"
#endif

U32 gulAddPowerOnTime = 0; // ms
extern U8 ERL_Bypass_Record_HB_RETRY_INFO[16][16][4];//Dylan ERL Record HB INFO
#if (RDT_RUN_ONLINE)
#include "hal/pic/uart/shr_hal_pic_uartxfer.h"
extern RFL_LOG_STRUCT *gpResultFinal;
#endif

#if PCA_2_ADDR_DEBUG
static void pca_to_addr_printf(U32 ulPCA, U8 ubSLCMode)
{
	U8 ubPCARule = PCA_RULE(ubSLCMode);
	U8 ubCE = (U8)PCA_2_BANK(ubPCARule, ulPCA);
	U8 ubCH = (U8)PCA_2_CHANNEL(ubPCARule, ulPCA);
	U8 ubDie = (U8)PCA_2_DIE(ubPCARule, ulPCA);
	U8 ubPlane = (U8)PCA_2_PLANE(ubPCARule, ulPCA);
	U16 uwBlock = (U16)PCA_2_BLOCK(ubPCARule, ulPCA);
	U16 uwPage = (U16)PCA_2_PAGE(ubPCARule, ulPCA);
	U8 ubEntry = (U8)PCA_2_NODE(ubPCARule, ulPCA);
	U8 ubLMU = (ubSLCMode ? 0 : (U8)PCA_2_LMU(ubPCARule, ulPCA));
	M_UART(RDT_TEST_, "\nAddr->CH%d,CE%d,Die%d,VB%d,Pln%d,Page%d,LMU%d,Entry%d", ubCH, ubCE, ubDie, uwBlock, ubPlane, uwPage, ubLMU, ubEntry);
}
#endif
#if RDT_RECORD_ECC_DISTRIBUTION
/* -------------------------------------------------------------------------------
    RDT_LOG_ECC_DISTRIBUTION_LOG Buffer ?诲叡64KB锛屾?涓�浠紼DL澶у?涓?K
	---------------
	|    32KB     |  瀛樻斁Block璇诲?瀹屾??庣?EDL锛屾弧32K涔嬪??欏叆Flash
	---------------
	|    16KB     |  ?傛椂瀛樻斁CH0?凚lock璇诲?杩囩?涓??愮?EDL锛屽彲?彺IL4 4P
	---------------
	|    16KB     |  ?傛椂瀛樻斁CH1?凚lock璇诲?杩囩?涓??愮?EDL锛屽彲?彺IL4 4P
	---------------
---------------------------------------------------------------------------------- */
BOOL rdt_flash_test_read_callback(TIEOUT_FORMAT_t uoResult, void *cb_obj, U32 ulPCA)
{
	RDT_API_STRUCT_PTR rdt = (RDT_API_STRUCT_PTR)cb_obj;
	U8 ubSLCMode = uoResult.HL.B32_to_B63.Info.btSLCMode;

	if (!rdt->param.rdt_record_ecc_distribution.record_edl_enable) {
		return PASS;
	}

	if ( (1 == ubSLCMode && rdt->saving_info.test_loop == 0 && rdt->saving_info.test_cycle == 0)
		|| (0 == ubSLCMode && rdt->saving_info.test_loop == 0 && (rdt->saving_info.test_cycle == 0 || rdt->saving_info.test_cycle == 1))) {
		//Only Record SLC loop0 Cycle0, TLC loop0 Cycle0/1 -- andry ********
	}
	else {
		return PASS;
	}

	U8 ubPCARule = PCA_RULE(ubSLCMode);
	U8 ubCE = (U8)PCA_2_BANK(ubPCARule, ulPCA);
	U8 ubCH = (U8)PCA_2_CHANNEL(ubPCARule, ulPCA);
	U8 ubDie = (U8)PCA_2_DIE(ubPCARule, ulPCA);
	U8 ubPlane = (U8)PCA_2_PLANE(ubPCARule, ulPCA);
	U16 uwBlock = (U16)PCA_2_BLOCK(ubPCARule, ulPCA);
	U16 uwPage = (U16)PCA_2_PAGE(ubPCARule, ulPCA);
	U8 ubEntry = (U8)PCA_2_NODE(ubPCARule, ulPCA);
	U8 ubLMU = (ubSLCMode ? 0 : (U8)PCA_2_LMU(ubPCARule, ulPCA));
	U16 ubECCBit = 0;
	FPL_GEOMETRY_STRUCT_PTR geometry = &(rdt->fpl->geometry);

	U32 ulBufOffset = (rdt->log[RDT_LOG_ECC_DISTRIBUTION_LOG].buf_total_size / 2) + (ubCH * (rdt->log[RDT_LOG_ECC_DISTRIBUTION_LOG].buf_total_size / 4));
	ulBufOffset += (ubCE * geometry->die_per_ce * geometry->plane_per_die + ubDie * geometry->plane_per_die + ubPlane) * sizeof(EDL_LOG_STRUCT);

	EDL_LOG_STRUCT_PTR edl_log = (EDL_LOG_STRUCT_PTR)(rdt->log[RDT_LOG_ECC_DISTRIBUTION_LOG].buf_base + ulBufOffset);

	U16 uwWLNum = geometry->d1_page_per_block;
	U8 ubLMUNum = geometry->lmu_number;
#if(YMTC_FSP_EN || MICRON_FSP_EN || SAMSUNG_FSP_EN || INTEL_FSP_EN)
#if(FW_CATEGORY_FLASH == FLASH_YMTC_TAS_TLC || FW_CATEGORY_FLASH == FLASH_YMTC_WTS_TLC)
	ubLMUNum = 3;
#endif
	uwWLNum = geometry->d2_page_per_block / ubLMUNum;
	if (!ubSLCMode) {
		ubLMU = uwPage % ubLMUNum;
		uwPage = uwPage / ubLMUNum;
	}
#endif

	if ((uwPage == 0) && (ubLMU == 0) && (ubEntry == 0)) {
		memset(edl_log, 0, sizeof(EDL_LOG_STRUCT));
		edl_log->slc_mode = ubSLCMode;
		edl_log->flash_ce = ubCE;
		edl_log->flash_channel = ubCH;
		edl_log->flash_die = ubDie;
		edl_log->flash_plane = ubPlane;
		edl_log->flash_block = uwBlock;
		edl_log->test_loop = rdt->saving_info.test_loop;
		edl_log->test_cycle = rdt->saving_info.test_cycle;
	}

	if (ubEntry == 0) {
		edl_log->error_had_happened = TMSG_TYPE_NO_ERROR;
	}

	if (uoResult.HL.B32_to_B63.Info.btMSG_TYPE == TMSG_TYPE_ERROR) {
		edl_log->error_had_happened = TMSG_TYPE_ERROR;
	}

	if ((ubEntry == 3) && (edl_log->error_had_happened == TMSG_TYPE_ERROR)) {
		edl_log->error_page_num++;
		M_SET_BITMAP(edl_log->HB.error_page_bit_map, uwPage);
	}

	/*	//Reip
	if (uoResult.HL.B32_to_B63.Info.btMSG_TYPE == TMSG_TYPE_NO_ERROR) {

		ubECCBit = M_FIP_GET_ECC_INF(edl_log->flash_channel);

		if ((ubEntry == 0) || (edl_log->flash_page_max_ecc < ubECCBit)) {
			edl_log->flash_page_max_ecc = ubECCBit;
		}

		if (ubEntry == 3) {
			if (edl_log->flash_page_max_ecc >= 256) {
				M_UART(RDT_DBG_, "\n Page Max ECC Over Flow !!! Flash Info : SLCMode%d - CE%d - CH%d - Die%d - Plane%d - Block%d - Page%d - LMU%d - ECC%d \n", ubSLCMode, ubCE, ubCH, ubDie, ubPlane, uwBlock, uwPage, ubLMU, ubECCBit);
			}
			else {
				edl_log->HB.Error_Distribution[edl_log->flash_page_max_ecc]++;
			}
		}

		if (ubSLCMode) {
			if ((uwPage == (geometry->d1_page_per_block - 1)) && (ubEntry == 3)) {
				rdt_api_edl_log_add(rdt, edl_log);
			}
		}
		else {
			if ((uwPage == (uwWLNum - 1)) && (ubLMU == (ubLMUNum - 1)) && (ubEntry == 3)) {
				rdt_api_edl_log_add(rdt, edl_log);
			}
		}
	}
	else {
		M_UART(RDT_DBG_, "\n Flash UNC !!! Flash Info : SLCMode%d - CE%d - CH%d - Die%d - Plane%d - Block%d - Page%d - LMU%d \n", ubSLCMode, ubCE, ubCH, ubDie, ubPlane, uwBlock, uwPage, ubLMU);
	}
	*/

	ubECCBit = M_FIP_GET_ECC_INF(edl_log->flash_channel);
	if (ubECCBit >= 0xF0) {
		ubECCBit = 0xF0;
	}

	if ((ubEntry == 0) || (edl_log->flash_page_max_ecc < ubECCBit)) {
		edl_log->flash_page_max_ecc = ubECCBit;
	}

	if (ubEntry == 3) {
		edl_log->HB.Error_Distribution[edl_log->flash_page_max_ecc]++;
	}

	if (ubSLCMode) {
		if ((uwPage == (geometry->d1_page_per_block - 1)) && (ubEntry == 3)) {
			rdt_api_edl_log_add(rdt, edl_log);
		}
	}
	else {
		if ((uwPage == (uwWLNum - 1)) && (ubLMU == (ubLMUNum - 1)) && (ubEntry == 3)) {
			rdt_api_edl_log_add(rdt, edl_log);
		}
	}

	return PASS;
}
#endif

static BOOL rdt_scan_log_callback(TIEOUT_FORMAT_t uoResult, void *cb_obj, U32 ulPCA)
{
	RDT_API_STRUCT *rdt = (RDT_API_STRUCT *)cb_obj;
	U32 pca = ulPCA;
	U8 i = 0;
	U16 temp_blk;

	U8 pca_rule = 0;
	U8 slc_mode = LOG_BLOCK_SLC_MODE;
	U32 data_buf;

	U32 ulTieoutLCA;
	//U8 ubResult = TRUE;

	/* Choose PCA rule */
	pca_rule = PCA_RULE(slc_mode);

	data_buf = rdt->temp_read_buffer;

	//rdt->done_cnt++;

	if (uoResult.HL.B32_to_B63.Info.btMSG_TYPE) {
		//ignore the scan log read fail, the reason maybe is early bad
		return TRUE;
	}

	if (rdt->scan_header_mode == 0) { //always scan system block header with MAX LPDC mode
		//return TRUE;
	}

	//pca_to_addr_printf(pca, 1);
	//M_UART(RDT_TEST_,"LCA = %l LDPC = %l", uoResult.HL.B0_to_B31.Read_1st.ulLCA,R32_FCON[R32_FCON_LDPC_CFG]);

	ulTieoutLCA = uoResult.HL.B0_to_B31.Read_1st.ulLCA;

	if (ulTieoutLCA != 0) {
		M_UART(RDT_DBG_, "\n mark:%l", ulTieoutLCA);
	}

	if (0 != rdt->ulScanSpecificLogLCA && ulTieoutLCA != rdt->ulScanSpecificLogLCA) {
		return TRUE;
	}

	if (ulTieoutLCA == SPARE_LCA_CODEBLOCK) { //LCA_FOR_CODEBLK  //MARK_CODE_BLK
		M_UART(RDT_TEST_, "\nCODE = %l", pca);
#if PCA_2_ADDR_DEBUG
		pca_to_addr_printf(pca, 1);
#endif
#if 0 //No id page
		if ( rdt->id_page_find == 0 ) {
			//if( *(U8 *)(0x02804000) == 'R')
			{
				dmac_api_blocking_copy( 0x02804000, 0x02808000, 0x4000);//put id page to 0x02808000
				rdt->id_page_find = 1;
				M_UART(RDT_TEST_, "\nFIND ID PAGE");

			}
		}
#endif

		if (rdt->sys_blk_record.code_pointer_block_cnt < MAX_CODE_BLOCK_COPY) {
			rdt->sys_blk_record.code_pointer_block_addr[rdt->sys_blk_record.code_pointer_block_cnt] = pca;
			rdt->sys_blk_record.code_pointer_block_cnt ++;

			temp_blk = (U8)PCA_2_BLOCK(pca_rule, pca);
			if (temp_blk > rdt->skip_checking_blk_idx) {
				rdt->skip_checking_blk_idx = temp_blk;
			}
		}
	}
	else if (ulTieoutLCA == SPARE_LCA_DBT_HEADER) {
		M_UART(RDT_TEST_, "\nBBM = %l", pca);
#if PCA_2_ADDR_DEBUG
		pca_to_addr_printf(pca, 1);
#endif
		if (rdt->sys_blk_record.bbm_block_cnt < MAX_SYSTEM_BLOCK_COPY) {
			rdt->sys_blk_record.bbm_block_addr[rdt->sys_blk_record.bbm_block_cnt] = pca;
			rdt->sys_blk_record.bbm_block_cnt++;

			/* Record DBT and RUT info in BBM header */
			rdt->dbt.data_bbm_start_page = *(U32 *)(data_buf + BBM_DBT_START_PAGE_OFFSET);
			//rdt->rut_l1.data_bbm_start_page = *(U32 *)(data_buf + BBM_RUTL1_START_PAGE_OFFSET);
			//rdt->rut_l2.data_bbm_start_page = *(U32 *)(data_buf + BBM_RUTL2_START_PAGE_OFFSET);
			//rdt->rut_st.data_bbm_start_page = *(U32 *)(data_buf + BBM_RUTST_START_PAGE_OFFSET);

			rdt->dbt.data_bbm_size = *(U32 *)(data_buf + BBM_DBT_SIZE_OFFSET);
			//rdt->rut_l1.data_bbm_size = *(U32 *)(data_buf + BBM_RUTL1_SIZE_OFFSET);
			//rdt->rut_l2.data_bbm_size = *(U32 *)(data_buf + BBM_RUTL2_SIZE_OFFSET);
			//rdt->rut_st.data_bbm_size = *(U32 *)(data_buf + BBM_RUTST_SIZE_OFFSET);

			rdt->dbt.data_bbm_page_num = rdt_api_page_align_count(rdt->dbt.data_bbm_size);
			//rdt->rut_l1.data_bbm_page_num = rdt_api_page_align_count(rdt->rut_l1.data_bbm_size);
			//rdt->rut_l2.data_bbm_page_num = rdt_api_page_align_count(rdt->rut_l2.data_bbm_size);
			//rdt->rut_st.data_bbm_page_num = rdt_api_page_align_count(rdt->rut_st.data_bbm_size);

			M_UART(RDT_DBG_, "\ndbt_start_page=%l,size=%l,page_num=%l", rdt->dbt.data_bbm_start_page, rdt->dbt.data_bbm_size, rdt->dbt.data_bbm_page_num);
			//M_UART(RDT_TEST_,"\nrutl1_start_page=%l,size=%l,page_num=%l", rdt->rut_l1.data_bbm_start_page, rdt->rut_l1.data_bbm_size, rdt->rut_l1.data_bbm_page_num);
			//M_UART(RDT_TEST_,"\nrutl2_start_page=%l,size=%l,page_num=%l", rdt->rut_l2.data_bbm_start_page, rdt->rut_l2.data_bbm_size, rdt->rut_l2.data_bbm_page_num);
			//M_UART(RDT_TEST_,"\nrutst_start_page=%l,size=%l,page_num=%l", rdt->rut_st.data_bbm_start_page, rdt->rut_st.data_bbm_size, rdt->rut_st.data_bbm_page_num);

			temp_blk = (U8)PCA_2_BLOCK(pca_rule, pca);
			if (temp_blk > rdt->skip_checking_blk_idx) {
				rdt->skip_checking_blk_idx = temp_blk;
			}
#if ENABLE_PH
			U32 pca_PH;
			U8 ubPHIdx = 0;
			U16 uwUnit;
			U8 ubPlane, ubBank, ubCh;
			DBTBlockHeader_t *pDBTBlockHeader = (DBTBlockHeader_t *)data_buf;
			DBTHeaderSector0_t *pDBTHeader = (DBTHeaderSector0_t *) & (pDBTBlockHeader->DBTHeaderSector0);

			//gSystemArea.uoDBTSerialNumber = pDBTHeader->uoDBTSerialNumber;
			while ((ubPHIdx < PH_NUM) && (rdt->sys_blk_record.bbm_block_cnt == 1)) {
				//M_UART(RDT_TEST_,"\nRecord PH");
				gPH.PHBlk[ubPHIdx].uwAll = pDBTHeader->PH[ubPHIdx].uwAll;

				if (gPH.PHBlk[ubPHIdx].uwAll != INVALID_BLOCK) {
					uwUnit = gPH.PHBlk[rdt->sys_blk_record.ph_block_cnt].Info.ubBlockOrder / rdt->fpl->geometry.plane_per_die;
					ubPlane = gPH.PHBlk[rdt->sys_blk_record.ph_block_cnt].Info.ubBlockOrder % rdt->fpl->geometry.plane_per_die;
					ubBank = gPH.PHBlk[rdt->sys_blk_record.ph_block_cnt].Info.CEperCH;
					ubCh = gPH.PHBlk[rdt->sys_blk_record.ph_block_cnt].Info.Channel;
					pca_PH = PCA_VALUE(pca_rule, 0, uwUnit, ubBank, ubCh, ubPlane, 0, 0, 0);

					rdt->sys_blk_record.ph_block_addr[rdt->sys_blk_record.ph_block_cnt] = pca_PH;
					rdt->sys_blk_record.ph_block_cnt++;

					M_UART(RDT_DBG_, "\ngProductHistory exist [%b]=%l uwUnit=%l ubPlane=%b ubBank=%b ubCh=%b pca_PH=%l", ubPHIdx, gPH.PHBlk[ubPHIdx].uwAll, uwUnit, ubPlane, ubBank, ubCh, pca_PH);

					/*Skip PH blocks*/
					temp_blk = (U8)PCA_2_BLOCK(pca_rule, pca_PH);
					if (temp_blk > rdt->skip_checking_blk_idx) {
						rdt->skip_checking_blk_idx = temp_blk;
					}
				}

				ubPHIdx++;
			}
#endif
		}
	}
#if (RDT_RUN_ONLINE)
	else if (ulTieoutLCA == SPARE_LCA_PH_MP_LOG) {
		M_UART(RDT_TEST_, "\nPH = %l", pca);
		if (rdt->sys_blk_record.ph_block_cnt < MAX_PRODUCT_HISTORY_COPY) {
			rdt->sys_blk_record.ph_block_addr[rdt->sys_blk_record.ph_block_cnt] = pca;
			rdt->sys_blk_record.ph_block_cnt++;
			/*Skip PH blocks*/
			temp_blk = (U8)PCA_2_BLOCK(pca_rule, pca);
			if (temp_blk > rdt->skip_checking_blk_idx) {
				rdt->skip_checking_blk_idx = temp_blk;
			}
		}
	}
#endif
	else if (ulTieoutLCA == SPARE_LCA_SYSTEM) { //Add reading system block (info block)
		M_UART(RDT_TEST_, "\nINFO BLK = %l", pca);
#if PCA_2_ADDR_DEBUG
		pca_to_addr_printf(pca, 1);
#endif

		if ( rdt->info_blk_find == 0 ) {
			//if( *(U8 *)(0x02804000) == 'R')s
			{
				rdt_api_dmac_copy(rdt->temp_read_buffer, rdt->temp_info_block_buffer, 0x4000);//put info block
				rdt->info_blk_find = 1;
			}
		}

		if (rdt->sys_blk_record.sys_block_cnt < MAX_SYS_BLOCK_COPY) {
			rdt->sys_blk_record.sys_block_addr[rdt->sys_blk_record.sys_block_cnt] = pca;
			rdt->sys_blk_record.sys_block_cnt ++;

			temp_blk = (U8)PCA_2_BLOCK(pca_rule, pca);
			if (temp_blk > rdt->skip_checking_blk_idx) {
				rdt->skip_checking_blk_idx = temp_blk;
			}
		}
	}
	else if (ulTieoutLCA == SPARE_LCA_ERROR_RECORD_LOG_BLOCK) {
		M_UART(RDT_TEST_, "\nERL = %l", pca);
#if PCA_2_ADDR_DEBUG
		pca_to_addr_printf(pca, 1);
#endif

		for (i = 0 ; i < MAX_BLOCK_LOG ; i++) { //zerio change
			if (rdt->log[RDT_LOG_ERROR_RECORD].log_block_addr[i] == INVALID_PCA_VALUE) {

				rdt->log[RDT_LOG_ERROR_RECORD].log_block_addr[i] = pca;

				if (i < 2) {
					rdt->scan_log_bitmap |= (0x01ULL << RDT_LOG_ERROR_RECORD);
				}

				temp_blk = (U8)PCA_2_BLOCK(pca_rule, pca);
				if (temp_blk > rdt->skip_checking_blk_idx) {
					rdt->skip_checking_blk_idx = temp_blk;
				}

				break;
			}
		}
	}
	else if (ulTieoutLCA == SPARE_LCA_ERROR_RECORD_LOG2_BLOCK) {
		M_UART(RDT_TEST_, "\nERL2 = %l", pca);
#if PCA_2_ADDR_DEBUG
		pca_to_addr_printf(pca, 1);
#endif

		for (i = 0 ; i < MAX_BLOCK_LOG ; i++) { //zerio change
			if (rdt->log[RDT_LOG_ERROR_RECORD_2].log_block_addr[i] == INVALID_PCA_VALUE) {

				rdt->log[RDT_LOG_ERROR_RECORD_2].log_block_addr[i] = pca;

				if (i < 2) {
					rdt->scan_log_bitmap |= (0x01ULL << RDT_LOG_ERROR_RECORD);
				}

				temp_blk = (U8)PCA_2_BLOCK(pca_rule, pca);
				if (temp_blk > rdt->skip_checking_blk_idx) {
					rdt->skip_checking_blk_idx = temp_blk;
				}

				break;
			}
		}
	}
	else if (ulTieoutLCA == SPARE_LCA_TEMPERATURE_DIVERSITY_LOG_BLOCK) {
		M_UART(RDT_TEST_, "\nTDL = %l", pca);
#if PCA_2_ADDR_DEBUG
		pca_to_addr_printf(pca, 1);
#endif

		for (i = 0 ; i < MAX_BLOCK_LOG ; i++) {
			if (rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].log_block_addr[i] == INVALID_PCA_VALUE) {

				rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].log_block_addr[i] = pca;

				if (i < 2) {
					rdt->scan_log_bitmap |= (0x01ULL << RDT_LOG_TEMPERATURE_DIVERSITY);
				}

				temp_blk = (U8)PCA_2_BLOCK(pca_rule, pca);
				if (temp_blk > rdt->skip_checking_blk_idx) {
					rdt->skip_checking_blk_idx = temp_blk;
				}

				break;
			}
		}
	}
	else if (ulTieoutLCA == SPARE_LCA_RDT_TESTMARK_LOG_BLOCK) {
		M_UART(RDT_TEST_, "\nRML = %l", pca);
#if PCA_2_ADDR_DEBUG
		pca_to_addr_printf(pca, 1);
#endif

		for (i = 0 ; i < MAX_BLOCK_LOG ; i++) {
			if (rdt->log[RDT_LOG_RDT_TESTMARK].log_block_addr[i] == INVALID_PCA_VALUE) {

				rdt->log[RDT_LOG_RDT_TESTMARK].log_block_addr[i] = pca;

				if (i < 2) {
					rdt->scan_log_bitmap |= (0x01ULL << RDT_LOG_RDT_TESTMARK);
				}

				temp_blk = (U8)PCA_2_BLOCK(pca_rule, pca);
				if (temp_blk > rdt->skip_checking_blk_idx) {
					rdt->skip_checking_blk_idx = temp_blk;
				}

				break;
			}
		}
	}
	else if (ulTieoutLCA == SPARE_LCA_FLASH_TESTMARK_LOG_BLOCK) {
		M_UART(RDT_TEST_, "\nFML = %l", pca);
#if PCA_2_ADDR_DEBUG
		pca_to_addr_printf(pca, 1);
#endif

		for (i = 0 ; i < MAX_BLOCK_LOG ; i++) {
			if (rdt->log[RDT_LOG_FLASH_TESTMARK].log_block_addr[i] == INVALID_PCA_VALUE) {
				rdt->log[RDT_LOG_FLASH_TESTMARK].log_block_addr[i] = pca;

				if (i < 2) {
					rdt->scan_log_bitmap |= (0x01ULL << RDT_LOG_FLASH_TESTMARK);
				}

				temp_blk = (U8)PCA_2_BLOCK(pca_rule, pca);
				if (temp_blk  > rdt->skip_checking_blk_idx) {
					rdt->skip_checking_blk_idx = temp_blk;
				}

				break;
			}
		}
	}
#if RDT_POWER_RESUME
	else if (ulTieoutLCA == SPARE_LCA_POWER_RESUME_LOG_BLOCK) {
		M_UART(RDT_TEST_, "\nPRL = %l", pca);
#if PCA_2_ADDR_DEBUG
		pca_to_addr_printf(pca, 1);
#endif

		for (i = 0 ; i < MAX_BLOCK_LOG ; i++) {
			if (rdt->log[RDT_LOG_POWER_RESUME].log_block_addr[i] == INVALID_PCA_VALUE) {
				rdt->log[RDT_LOG_POWER_RESUME].log_block_addr[i] = pca;

				if (i < 2) {
					rdt->scan_log_bitmap |= (0x01ULL << RDT_LOG_POWER_RESUME);
				}

				temp_blk = (U8)PCA_2_BLOCK(pca_rule, pca);
				if (temp_blk  > rdt->skip_checking_blk_idx) {
					rdt->skip_checking_blk_idx = temp_blk;
				}
				break;
			}
		}
	}
#endif
	else if (ulTieoutLCA == SPARE_LCA_SAMPLE_BLOCK_TEST_LOG_BLOCK) {
		M_UART(RDT_TEST_, "\nSTL = %l", pca);
#if PCA_2_ADDR_DEBUG
		pca_to_addr_printf(pca, 1);
#endif

		for (i = 0 ; i < MAX_BLOCK_LOG ; i++) {
			if (rdt->log[RDT_LOG_SAMPLE_BLOCK_TEST_LOG].log_block_addr[i] == INVALID_PCA_VALUE) {
				rdt->log[RDT_LOG_SAMPLE_BLOCK_TEST_LOG].log_block_addr[i] = pca;

				if (i < 2) {
					rdt->scan_log_bitmap |= (0x01ULL << RDT_LOG_SAMPLE_BLOCK_TEST_LOG);
				}

				temp_blk = (U8)PCA_2_BLOCK(pca_rule, pca);
				if (temp_blk  > rdt->skip_checking_blk_idx) {
					rdt->skip_checking_blk_idx = temp_blk;
				}

				break;
			}
		}
	}
	else if (ulTieoutLCA == SPARE_LCA_TP_TESTMARK_LOG_BLOCK) {
		M_UART(RDT_TEST_, "\nTML = %l", pca);
#if PCA_2_ADDR_DEBUG
		pca_to_addr_printf(pca, 1);
#endif

		for (i = 0 ; i < MAX_BLOCK_LOG ; i++) {
			if (rdt->log[RDT_LOG_TP_TESTMARK].log_block_addr[i] == INVALID_PCA_VALUE) {
				rdt->log[RDT_LOG_TP_TESTMARK].log_block_addr[i] = pca;

				if (i < 2) {
					rdt->scan_log_bitmap |= (0x01ULL << RDT_LOG_TP_TESTMARK);
				}

				temp_blk = (U8)PCA_2_BLOCK(pca_rule, pca);
				if (temp_blk  > rdt->skip_checking_blk_idx) {
					rdt->skip_checking_blk_idx = temp_blk;
				}

				break;
			}
		}
	}
#if RDT_RECORD_SCAN_WINDOW_LOG
	else if (ulTieoutLCA == SPARE_LCA_SCAN_WINDOW_LOG_BLOCK) {
		M_UART(RDT_TEST_, "\nWIN = %l", pca);

		for (i = 0 ; i < MAX_BLOCK_LOG ; i++) {
			if (rdt->log[RDT_LOG_SCAN_WIN].log_block_addr[i] == INVALID_PCA_VALUE) {
				rdt->log[RDT_LOG_SCAN_WIN].log_block_addr[i] = pca;

				if (i < 2) {
					rdt->scan_log_bitmap |= (0x01ULL << RDT_LOG_SCAN_WIN);
				}

				temp_blk = (U8)PCA_2_BLOCK(pca_rule, pca);
				if (temp_blk  > rdt->skip_checking_blk_idx) {
					rdt->skip_checking_blk_idx = temp_blk;
				}

				break;
			}
		}
	}
#endif
#if RDT_RECORD_ECC_DISTRIBUTION
	else if (ulTieoutLCA == SPARE_LCA_ECC_DISTRIBUTION_LOG_BLOCK) {
		M_UART(RDT_TEST_, "\nEDL = %l", pca);

		for (i = 0 ; i < MAX_BLOCK_LOG ; i++) {
			if (rdt->log[RDT_LOG_ECC_DISTRIBUTION_LOG].log_block_addr[i] == INVALID_PCA_VALUE) {
				rdt->log[RDT_LOG_ECC_DISTRIBUTION_LOG].log_block_addr[i] = pca;

				if (i < 2) {
					rdt->scan_log_bitmap |= (0x01ULL << RDT_LOG_ECC_DISTRIBUTION_LOG);
				}

				temp_blk = (U8)PCA_2_BLOCK(pca_rule, pca);
				if (temp_blk  > rdt->skip_checking_blk_idx) {
					rdt->skip_checking_blk_idx = temp_blk;
				}

				break;
			}
		}
	}
	else if (ulTieoutLCA == SPARE_LCA_ECC_DISTRIBUTION_LOG2_BLOCK) {
		M_UART(RDT_TEST_, "\nEDL2 = %l", pca);

		for (i = 0 ; i < MAX_BLOCK_LOG ; i++) {
			if (rdt->log[RDT_LOG_ECC_DISTRIBUTION_LOG2].log_block_addr[i] == INVALID_PCA_VALUE) {
				rdt->log[RDT_LOG_ECC_DISTRIBUTION_LOG2].log_block_addr[i] = pca;
				rdt->scan_log_bitmap |= (0x01ULL << RDT_LOG_ECC_DISTRIBUTION_LOG2);

				temp_blk = (U8)PCA_2_BLOCK(pca_rule, pca);
				if (temp_blk  > rdt->skip_checking_blk_idx) {
					rdt->skip_checking_blk_idx = temp_blk;
				}
				break;
			}
		}
	}
	else if (ulTieoutLCA == SPARE_LCA_ECC_DISTRIBUTION_LOG3_BLOCK) {
		M_UART(RDT_TEST_, "\nEDL3 = %l", pca);

		for (i = 0 ; i < MAX_BLOCK_LOG ; i++) {
			if (rdt->log[RDT_LOG_ECC_DISTRIBUTION_LOG3].log_block_addr[i] == INVALID_PCA_VALUE) {
				rdt->log[RDT_LOG_ECC_DISTRIBUTION_LOG3].log_block_addr[i] = pca;
				rdt->scan_log_bitmap |= (0x01ULL << RDT_LOG_ECC_DISTRIBUTION_LOG3);

				temp_blk = (U8)PCA_2_BLOCK(pca_rule, pca);
				if (temp_blk  > rdt->skip_checking_blk_idx) {
					rdt->skip_checking_blk_idx = temp_blk;
				}
				break;
			}
		}
	}
	else if (ulTieoutLCA == SPARE_LCA_ECC_DISTRIBUTION_LOG4_BLOCK) {
		M_UART(RDT_TEST_, "\nEDL4 = %l", pca);

		for (i = 0 ; i < MAX_BLOCK_LOG ; i++) {
			if (rdt->log[RDT_LOG_ECC_DISTRIBUTION_LOG4].log_block_addr[i] == INVALID_PCA_VALUE) {
				rdt->log[RDT_LOG_ECC_DISTRIBUTION_LOG4].log_block_addr[i] = pca;
				rdt->scan_log_bitmap |= (0x01ULL << RDT_LOG_ECC_DISTRIBUTION_LOG4);

				temp_blk = (U8)PCA_2_BLOCK(pca_rule, pca);
				if (temp_blk  > rdt->skip_checking_blk_idx) {
					rdt->skip_checking_blk_idx = temp_blk;
				}
				break;
			}
		}
	}
	else if (ulTieoutLCA == SPARE_LCA_ECC_DISTRIBUTION_LOG5_BLOCK) {
		M_UART(RDT_TEST_, "\nEDL5 = %l", pca);

		for (i = 0 ; i < MAX_BLOCK_LOG ; i++) {
			if (rdt->log[RDT_LOG_ECC_DISTRIBUTION_LOG5].log_block_addr[i] == INVALID_PCA_VALUE) {
				rdt->log[RDT_LOG_ECC_DISTRIBUTION_LOG5].log_block_addr[i] = pca;
				rdt->scan_log_bitmap |= (0x01ULL << RDT_LOG_ECC_DISTRIBUTION_LOG5);

				temp_blk = (U8)PCA_2_BLOCK(pca_rule, pca);
				if (temp_blk  > rdt->skip_checking_blk_idx) {
					rdt->skip_checking_blk_idx = temp_blk;
				}
				break;
			}
		}
	}
	else if (ulTieoutLCA == SPARE_LCA_ECC_DISTRIBUTION_LOG6_BLOCK) {
		M_UART(RDT_TEST_, "\nEDL6 = %l", pca);

		for (i = 0 ; i < MAX_BLOCK_LOG ; i++) {
			if (rdt->log[RDT_LOG_ECC_DISTRIBUTION_LOG6].log_block_addr[i] == INVALID_PCA_VALUE) {
				rdt->log[RDT_LOG_ECC_DISTRIBUTION_LOG6].log_block_addr[i] = pca;
				rdt->scan_log_bitmap |= (0x01ULL << RDT_LOG_ECC_DISTRIBUTION_LOG6);

				temp_blk = (U8)PCA_2_BLOCK(pca_rule, pca);
				if (temp_blk  > rdt->skip_checking_blk_idx) {
					rdt->skip_checking_blk_idx = temp_blk;
				}
				break;
			}
		}
	}
	else if (ulTieoutLCA == SPARE_LCA_ECC_DISTRIBUTION_LOG7_BLOCK) {
		M_UART(RDT_TEST_, "\nEDL7 = %l", pca);

		for (i = 0 ; i < MAX_BLOCK_LOG ; i++) {
			if (rdt->log[RDT_LOG_ECC_DISTRIBUTION_LOG7].log_block_addr[i] == INVALID_PCA_VALUE) {
				rdt->log[RDT_LOG_ECC_DISTRIBUTION_LOG7].log_block_addr[i] = pca;
				rdt->scan_log_bitmap |= (0x01ULL << RDT_LOG_ECC_DISTRIBUTION_LOG7);

				temp_blk = (U8)PCA_2_BLOCK(pca_rule, pca);
				if (temp_blk  > rdt->skip_checking_blk_idx) {
					rdt->skip_checking_blk_idx = temp_blk;
				}
				break;
			}
		}
	}
	else if (ulTieoutLCA == SPARE_LCA_ECC_DISTRIBUTION_LOG8_BLOCK) {
		M_UART(RDT_TEST_, "\nEDL8 = %l", pca);

		for (i = 0 ; i < MAX_BLOCK_LOG ; i++) {
			if (rdt->log[RDT_LOG_ECC_DISTRIBUTION_LOG8].log_block_addr[i] == INVALID_PCA_VALUE) {
				rdt->log[RDT_LOG_ECC_DISTRIBUTION_LOG8].log_block_addr[i] = pca;
				rdt->scan_log_bitmap |= (0x01ULL << RDT_LOG_ECC_DISTRIBUTION_LOG8);

				temp_blk = (U8)PCA_2_BLOCK(pca_rule, pca);
				if (temp_blk  > rdt->skip_checking_blk_idx) {
					rdt->skip_checking_blk_idx = temp_blk;
				}
				break;
			}
		}
	}
#endif

	//ASSERT(0, i < MAX_BLOCK_LOG);
	return TRUE;
}


static U32 rdt_set_log_spare_buf(RDT_API_STRUCT_PTR rdt, U8 log_type)
{
	U32 mark;

	switch (log_type) {
	case RDT_LOG_DBT:
		if (rdt->dbt_to_bbm_header) {
			mark = SPARE_LCA_DBT_HEADER;
		}
		else
			//mark = MARK_DBT_BLK;
		{
			mark = SPARE_LCA_DBT;
		}
		break;

	case RDT_LOG_ERROR_RECORD:
		mark = SPARE_LCA_ERROR_RECORD_LOG_BLOCK;
		break;

	case RDT_LOG_TEMPERATURE_DIVERSITY:
		mark = SPARE_LCA_TEMPERATURE_DIVERSITY_LOG_BLOCK;
		break;

	case RDT_LOG_RDT_TESTMARK:
		mark = SPARE_LCA_RDT_TESTMARK_LOG_BLOCK;
		break;

	case RDT_LOG_FLASH_TESTMARK:
		mark = SPARE_LCA_FLASH_TESTMARK_LOG_BLOCK;
		break;

	case RDT_LOG_POWER_RESUME:
		mark = SPARE_LCA_POWER_RESUME_LOG_BLOCK;
		break;

	case RDT_LOG_ERROR_RECORD_2:
		mark = SPARE_LCA_ERROR_RECORD_LOG2_BLOCK;
		break;

	case RDT_LOG_SAMPLE_BLOCK_TEST_LOG:
		mark = SPARE_LCA_SAMPLE_BLOCK_TEST_LOG_BLOCK;
		break;

	case RDT_LOG_TP_TESTMARK:
		mark = SPARE_LCA_TP_TESTMARK_LOG_BLOCK;
		break;

#if RDT_RECORD_SCAN_WINDOW_LOG
	case RDT_LOG_SCAN_WIN:
		mark = SPARE_LCA_SCAN_WINDOW_LOG_BLOCK;
		break;
#endif

#if RDT_RECORD_ECC_DISTRIBUTION
	case RDT_LOG_ECC_DISTRIBUTION_LOG:
		mark = SPARE_LCA_ECC_DISTRIBUTION_LOG_BLOCK;
		break;
	case RDT_LOG_ECC_DISTRIBUTION_LOG2:
		mark = SPARE_LCA_ECC_DISTRIBUTION_LOG2_BLOCK;
		break;
	case RDT_LOG_ECC_DISTRIBUTION_LOG3:
		mark = SPARE_LCA_ECC_DISTRIBUTION_LOG3_BLOCK;
		break;
	case RDT_LOG_ECC_DISTRIBUTION_LOG4:
		mark = SPARE_LCA_ECC_DISTRIBUTION_LOG4_BLOCK;
		break;
	case RDT_LOG_ECC_DISTRIBUTION_LOG5:
		mark = SPARE_LCA_ECC_DISTRIBUTION_LOG5_BLOCK;
		break;
	case RDT_LOG_ECC_DISTRIBUTION_LOG6:
		mark = SPARE_LCA_ECC_DISTRIBUTION_LOG6_BLOCK;
		break;
	case RDT_LOG_ECC_DISTRIBUTION_LOG7:
		mark = SPARE_LCA_ECC_DISTRIBUTION_LOG7_BLOCK;
		break;
	case RDT_LOG_ECC_DISTRIBUTION_LOG8:
		mark = SPARE_LCA_ECC_DISTRIBUTION_LOG8_BLOCK;
		break;
#endif
#if (RDT_RUN_ONLINE)
	case RDT_LOG_MP_PH_LOG:
		mark = SPARE_LCA_PH_MP_LOG;
		break;
#endif
	default:
		//ASSERT(0, 0);
		mark = 0;
		break;
	}

	return mark;
}

void rdt_api_dbt_log_add(RDT_API_STRUCT_PTR rdt, U32 pca, BBM_BLOCK_MARK mark, U8 slc_mode)
{
	DBT_LOG_STRUCT dbt;
	//U16 vb, logic_plane;
	//U8 die_index;
	U32 bm_tbl_offset;
	U32 bm_count_offset; //, bm_count_offset_idx;
	//PCA_RULE_STRUCT *p_pca_rule = (rdt->fpl->pca_rule + slc_mode);

	U8 pca_rule = 0;
	U8 plane, ch, bank, die;
	U16 block;
	FPL_GEOMETRY_STRUCT_PTR geometry;

	/* Parsing PCA */
	pca_rule = PCA_RULE(slc_mode);

	geometry = &rdt->fpl->geometry;

	//dbt.bm_count = (RDT_BBM_BM_COUNT_PTR)(rdt->log[RDT_LOG_DBT].buf_base + SHARED_DBUF_BBM_BUF_SIZE);
	//dbt.bm_tbl = (BBM_BLOCK_MARK_PTR)(rdt->log[RDT_LOG_DBT].buf_base + SHARED_DBUF_BBM_BUF_SIZE + SHARED_DBUF_BBM_BMCNT_SIZE);

	//dbt.bm_tbl = (BBM_BLOCK_MARK_PTR)(rdt->log[RDT_LOG_DBT].buf_base);
	dbt.bm_tbl = (BBM_BLOCK_MARK_PTR)(rdt->log[RDT_LOG_DBT].buf_base + SHARED_DBUF_BBM_BUF_SIZE);

	plane = PCA_2_PLANE(pca_rule, pca);
	ch = PCA_2_CHANNEL(pca_rule, pca);
	bank = PCA_2_BANK(pca_rule, pca);
	die = PCA_2_DIE(pca_rule, pca);
	block = PCA_2_BLOCK(pca_rule, pca); //unit
	//uart
	U16 WLIndex;
	U8 ublmu;
	U16 physical_page;
	WLIndex = PCA_2_PAGE(pca_rule, pca);
	ublmu = PCA_2_LMU(pca_rule, pca);

	if (slc_mode) { //SLC
		physical_page = WLIndex;
	}
	else { //TLC
#if (MICRON_FSP_EN || YMTC_FSP_EN || SAMSUNG_FSP_EN || INTEL_FSP_EN)
		physical_page = WLIndex;
#else
		physical_page = (WLIndex * rdt->fpl->geometry.lmu_number) + ublmu;
#endif
	}
#if 1
	M_UART(RDT_TEST_, "\n ----ADD DBT INFO ------SLCMODE = %d unit=%d,ch=%d,ce=%d,plane=%d,WL =%d physical_page = %dis later bad", slc_mode, block, ch, bank, plane, WLIndex, physical_page);
#endif
	//uart



#if 0 //Ignore DIE
	bm_tbl_offset = (ch * geometry->total_bank * geometry->plane_per_die * geometry->block_per_plane) +
		(bank * geometry->plane_per_die * geometry->block_per_plane) +
		(block * geometry->plane_per_die) +
		plane;
#endif
	bm_tbl_offset = (ch * geometry->die_per_ce * geometry->total_bank * geometry->plane_per_die * geometry->block_per_plane) +
		(((bank * geometry->die_per_ce) + die) * geometry->plane_per_die * geometry->block_per_plane) +
		(block * geometry->plane_per_die) +
		plane;

	/*vb = (U16)PCA_2_VB(p_pca_rule, pca);
	logic_plane = (U16)PCA_2_PLANE_AMONG_VB(p_pca_rule, pca);
	die_index = PCA_2_BANK(p_pca_rule, pca);
	bm_tbl_offset = (vb * rdt->geometry.plane_per_vb) + logic_plane;
	bm_count_offset = (die_index * rdt->geometry.plane_per_vb) + logic_plane;*/

	dbt.bm_count = (RDT_BBM_BM_COUNT_PTR)(rdt->log[RDT_LOG_DBT].buf_base + SHARED_DBUF_BBM_BMCNT_EARLY_OFFSET);

	bm_count_offset = (ch * geometry->die_per_ce * geometry->total_bank * geometry->plane_per_die) +
		(((bank * geometry->die_per_ce) + die) * geometry->plane_per_die) +
		plane;
	//bm_count_offset = ((bm_count_offset_idx * BMCNT_SIZE) + BMCNT_OFFSET);

#if 0
	M_UART(RDT_TEST_, "\nADD DBT:");
	UartPrintf_DEC("vb =", vb, FALSE);
	UartPrintf_DEC(" ,logic_plane =", logic_plane, FALSE);
	UartPrintf_DEC(" ,die_index =", die_index, FALSE);
	UartPrintf_DEC(" ,bm_tbl_offset =", bm_tbl_offset, FALSE);
	UartPrintf_DEC(", block_mode =", dbt.bm_tbl[bm_tbl_offset].fields.block_mode, FALSE);
#endif


	//express_read_test
	//U8 skip_test_plane_offset = plane + (ch * geometry->plane_per_die) + (bank * geometry->channel_per_bank * geometry->plane_per_die) + (die * geometry->die_per_ce * geometry->channel_per_bank * geometry->plane_per_die);
	//gubSkipTestPlaneMap[skip_test_plane_offset] = 1;

	if ( dbt.bm_tbl[bm_tbl_offset].fields.bad_type == DBT_GOOD_BLOCK) {
		dbt.bm_tbl[bm_tbl_offset].fields.bad_type = mark.fields.bad_type;

		//if (mark.fields.pre_later_cnt != TMP_FOR_FIND_BLOCK)
		{
			//dbt.bm_count[bm_count_offset].early_bad++;
			dbt.bm_count[bm_count_offset].bad_cnt++;//this is total bad_count when RDT finish.

			/*  add later bad dbt.bm_count */
			{
				dbt.bm_count = (RDT_BBM_BM_COUNT_PTR)(rdt->log[RDT_LOG_DBT].buf_base + SHARED_DBUF_BBM_BMCNT_LATER_OFFSET);
				dbt.bm_count[bm_count_offset].bad_cnt++;//this is later bad count
			}
			rdt->saving_info.flash_later_bad_cnt++;
			//add later  bad count

			if (dbt.bm_count[bm_count_offset].bad_cnt > rdt->saving_info.max_bad_cnt_per_pln) {
				rdt->saving_info.max_bad_cnt_per_pln = dbt.bm_count[bm_count_offset].bad_cnt;
			}

			//M_UART(RDT_TEST_,"\nAdd DBT: ch=%b bank=%b die=%b block=%l plane=%b bm_tbl_offset=%l bm_count_offset=%l bad_cnt=%l max_bad_cnt_per_pln=%l", ch, bank, die, block, plane, bm_tbl_offset, bm_count_offset, dbt.bm_count[bm_count_offset].bad_cnt, rdt->saving_info.max_bad_cnt_per_pln);
		}
	}
}

void rdt_api_scan_log(RDT_API_STRUCT_PTR rdt)
{
	U8 bank, ch, pln, block;
	U32 pca;

	U8 slc_mode = LOG_BLOCK_SLC_MODE;
	U8 pca_rule = 0;

	U32 data_buf;
#if LDPC_MODE_MODIFY
	U8 ubECCMode = 0;
#endif
	//BOOL temp_RRenable;
	//rdt->trig_cnt = 0;
	//rdt->done_cnt = 0;
#if (RDT_REFERENCE_WINDOW_RESULT)
	U8 ubClockDivBackUp = 0;
	//Write Read Flash by low speed, while DLL Offset not valid or not scan window yet. -- ******** andry
	ubClockDivBackUp = rdt_api_cop0_clock_div_backup();
	if (FALSE == rdt->btReferenceWindowResult) {
		rdt_api_cop0_clock_div_set(3);
	}
#endif
	rdt->current_state = RDT_STATE_SCAN_LOG_BLOCK;

	//temp_RRenable = rdt->cop0->err_hdl->skip_retry;
	//rdt->cop0->err_hdl->skip_retry = 1;
	//rdt->cop0->err_hdl->rebuild = 1;

	/* Choose PCA rule */
	pca_rule = PCA_RULE(slc_mode);

	if (rdt->scan_header_mode) {
		M_UART(RDT_TEST_, "\n--Scan all log block header--");

		//R32_FALL[R32_FCTL_OTHER_SET] |= SET_BCH_COR_EN;
		//R32_FALL[R32_FCTL_ECC_CFG] |= SET_LDPC_COR_EN;
#if LDPC_MODE_MODIFY
		//temp_ECC_mode = R32_FCON[R32_FCON_LDPC_CFG];
		//R32_FCON[R32_FCON_LDPC_CFG] &= ~CHK_ECC_MODE_MASK;         //clear LDPC mode [2:0]
		//R32_FCON[R32_FCON_LDPC_CFG] |= SET_ECC_MODE_7;             //set LDPC mode[2:0]
		ubECCMode = (U8)M_GET_ECC_MODE();
		M_SET_ECC_MODE(SET_ECC_MODE_7);
#if E19_FIP_SNAP_READ_WORKAROUND
		FIP_SET_SNAP_READ_DMY(0, 0, 0, 0);
#else
		FIP_SET_SNAP_READ_DMY(gFlhEnv.uwLDPCMode7DMYByteLen[0], gFlhEnv.uwLDPCMode7DMYByteLen[1], gFlhEnv.uwLDPCMode7DMYByteLen[2], gFlhEnv.uwLDPCMode7DMYByteLen[3]);
#endif
#endif

		//HAL_FLH_TOP_LMU_ADR_GEN_DIS();
		//hal_flh_setup_conv_tbl_boot(PAGE_SIZE, rdt->fpl->pca_rule[FPL_ADDR_SLC_RULE].rule_page.bit_no);
	}
	else {
		M_UART(RDT_TEST_, "\n--Scan log block--");
		//rdt_setup_conv(CONV_EN, 0); //set once?  Need to confirm???
		//HAL_FLH_TOP_LMU_ADR_GEN_EN();
		//hal_flh_setup_conv_tbl(rdt->fpl->geometry.node_per_page, rdt->fpl->geometry.d1_page_per_block, rdt->fpl->geometry.d2_page_per_block, FPL_CONV_BUF_BASE, TRUE);
	}
	//while (gubRDTTest);
	//U8 ubi;
	//for (ubi = 0; ubi < 0x20; ubi++) {
	//hal_cop0_iram_release(ubi, 0x00, 0, 0);
	//}

	M_UART(RDT_DBG_, "\nrdt->read_retry=%b", rdt->read_retry_en);
	data_buf = rdt->temp_read_buffer;

	for ( block = 0; block < MAX_SCAN_RDT_BLOCK_NUM ; block++) {
		for ( bank = 0 ; bank < rdt->fpl->geometry.total_bank; bank++) {
			for ( ch = 0 ; ch < rdt->fpl->geometry.channel_per_bank; ch++) {
				for (pln = 0 ; pln < rdt->fpl->geometry.plane_per_die ; pln++) {
					pca = PCA_VALUE(pca_rule, 0, block, bank, ch, pln, 0, 0, 0);
					//pca = (pln << p_pca_rule->rule_plane.shift) | (block << p_pca_rule->rule_block.shift) | (bank << p_pca_rule->rule_bank.shift) | (ch << p_pca_rule->rule_ch.shift) ;
					//ASSERT(0, pca != INVALID_ADDR_VALUE);

					//memset((void *)(DBUF_PB_RAM_ADDRESS + 0x4000), 0x0, PAGE_BYTE_SIZE);
					memset((void *)data_buf, 0x0, PAGE_BYTE_SIZE);

					//M_UART(RDT_TEST_,"\nblock=%b bank=%b ch=%b pln=%b pca=%l", block, bank, ch, pln, pca);

					rdt_api_cop0_read(rdt_scan_log_callback, (U32)pca, rdt, slc_mode, (U32)data_buf, SYS_BLK);

					/* Recieve all read CQ */
					rdt_api_delegate_CQ(BUF_TYPE_READ);
				}
			}
		}
	}

	if (rdt->scan_header_mode) {
		/* Restore LDPC mode */
		//R32_FALL[R32_FCTL_OTHER_SET] |= SET_BCH_COR_EN;
		//R32_FALL[R32_FCTL_ECC_CFG] |= SET_LDPC_COR_EN;
#if LDPC_MODE_MODIFY
		//R32_FCON[R32_FCON_LDPC_CFG] &= ~CHK_ECC_MODE_MASK;         // clear LDPC mode [2:0]
		//R32_FCON[R32_FCON_LDPC_CFG] |= temp_ECC_mode;             //set  LDPC mode[2:0]
		M_SET_ECC_MODE(ubECCMode);
		FIP_SET_SNAP_READ_DMY(gFlhEnv.uwNormalDMYByteLen[0], gFlhEnv.uwNormalDMYByteLen[1], gFlhEnv.uwNormalDMYByteLen[2], gFlhEnv.uwNormalDMYByteLen[3]);
#endif
	}

	rdt->scan_block_disable_uart = 0;

#if !ICE_MODE_EN
	if (rdt->sys_blk_record.code_pointer_block_cnt != MAX_CODE_BLOCK_COPY) {
		M_UART(RDT_DBG_, "\nNo find enough code blk (%b)", rdt->sys_blk_record.code_pointer_block_cnt);
	}
#endif

	if (rdt->sys_blk_record.bbm_block_cnt != MAX_SYSTEM_BLOCK_COPY) {
		M_UART(RDT_DBG_, "\nNo find enough DBT (%b)", rdt->sys_blk_record.bbm_block_cnt);
	}

#if !ICE_MODE_EN
	if (rdt->sys_blk_record.sys_block_cnt != MAX_SYS_BLOCK_COPY) {
		M_UART(RDT_DBG_, "\nNo find enough info block (%b)", rdt->sys_blk_record.sys_block_cnt);
	}
#endif
#if (RDT_REFERENCE_WINDOW_RESULT)
	//Recover clock div. -- ******** andry
	rdt_api_cop0_clock_div_set(ubClockDivBackUp);
#endif
	//M_UART(RDT_TEST_, "\n--Scan end--");
	//while(1);

	//rdt->cop0->err_hdl->skip_retry = temp_RRenable;
}


void rdt_api_tdl_log_add(RDT_API_STRUCT_PTR rdt, TDL_LOG_STRUCT_PTR tdl_log)
{
	U32 tdl_buf;
	if ((rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].buf_offset + sizeof(TDL_LOG_STRUCT)) > rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].buf_total_size) {
		rdt_api_write_log_to_flash(rdt, RDT_LOG_TEMPERATURE_DIVERSITY);
	}

	tdl_buf = (rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].buf_base + rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].buf_offset);
	memcpy(((U8 *)(tdl_buf)), tdl_log, sizeof(TDL_LOG_STRUCT));
	rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].buf_offset += sizeof(TDL_LOG_STRUCT);
	rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].page_num = rdt_api_page_align_count(rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].buf_offset);
}

void rdt_api_erl_log_add(RDT_API_STRUCT_PTR rdt, ERL_LOG_STRUCT_PTR erl_log)
{
	U32 erl_buf;

	//for system info
	rdt->saving_info.err_num_cnt++;

	if ((rdt->log[RDT_LOG_ERROR_RECORD].buf_offset < rdt->log[RDT_LOG_ERROR_RECORD].buf_total_size) || (rdt->log[RDT_LOG_ERROR_RECORD].page_offset_in_block > (rdt->fpl->geometry.d1_page_per_block * 2 - 9))) {//reserve 2 * 4(erl buf size in debuf) +1 to avoid erl log overflow
		erl_buf = (rdt->log[RDT_LOG_ERROR_RECORD].buf_base + rdt->log[RDT_LOG_ERROR_RECORD].buf_offset);
		memcpy(((U8 *)(erl_buf)), erl_log, sizeof(ERL_LOG_STRUCT));
		rdt->log[RDT_LOG_ERROR_RECORD].buf_offset += sizeof(ERL_LOG_STRUCT); // next log address
		rdt->log[RDT_LOG_ERROR_RECORD].page_num = rdt_api_page_align_count(rdt->log[RDT_LOG_ERROR_RECORD].buf_offset);
	}
}

void rdt_api_fml_log_add(RDT_API_STRUCT_PTR rdt, FML_LOG_STRUCT_PTR fml_log)
{
	U32 fml_buf;
	if ((rdt->log[RDT_LOG_FLASH_TESTMARK].buf_offset + sizeof(FML_LOG_STRUCT)) > rdt->log[RDT_LOG_FLASH_TESTMARK].buf_total_size) {
		rdt_api_write_log_to_flash(rdt, RDT_LOG_FLASH_TESTMARK);
	}
	fml_buf = (rdt->log[RDT_LOG_FLASH_TESTMARK].buf_base + rdt->log[RDT_LOG_FLASH_TESTMARK].buf_offset);
	memcpy(((U8 *)(fml_buf)), fml_log, sizeof(FML_LOG_STRUCT));
	rdt->log[RDT_LOG_FLASH_TESTMARK].buf_offset += sizeof(FML_LOG_STRUCT);
	rdt->log[RDT_LOG_FLASH_TESTMARK].page_num = rdt_api_page_align_count(rdt->log[RDT_LOG_FLASH_TESTMARK].buf_offset);
}


void rdt_api_rml_log_add(RDT_API_STRUCT_PTR rdt, U32 identify_mark)
{
	RML_LOG_STRUCT_PTR rml_log;

	if ((rdt->log[RDT_LOG_RDT_TESTMARK].buf_offset + sizeof(RML_LOG_STRUCT)) > rdt->log[RDT_LOG_RDT_TESTMARK].buf_total_size) {
		rdt_api_write_log_to_flash(rdt, RDT_LOG_RDT_TESTMARK);
	}
	rml_log = (RML_LOG_STRUCT_PTR) (rdt->log[RDT_LOG_RDT_TESTMARK].buf_base + rdt->log[RDT_LOG_RDT_TESTMARK].buf_offset);

	memset (rml_log, 0, sizeof (RML_LOG_STRUCT));

	rml_log->identify_mark       = identify_mark; //Each state start-end mark Chip id

	if (rdt->rdt_err) {
		rml_log->error_identify_mark = 0xeeeeeeee;
	}
	else {
		rml_log->error_identify_mark = 0xcccccccc;
	}

	if ((identify_mark == RML_IMARK_RDT_TEST_END) && gulAddPowerOnTime != 0) {
		// compensate the re-power on time
		guoOperationTime   += (U64)(gulAddPowerOnTime);
	}

	rml_log->current_time        = rdt_api_rtt_get_timer_count() / 1000; //s
	rml_log->fatal_error_id      = rdt->rdt_err;
	rml_log->err_state           = rdt->current_state;
	if (identify_mark == RML_IMARK_RDT_TEST_START) {
		if (gFlhEnv.ulFlashDefaultType.BitMap.btDCCFail) {
			rml_log->dcc_fail = gFlhEnv.ulFlashDefaultType.BitMap.btDCCFail;
		}

		if (gFlhEnv.ulFlashDefaultType.BitMap.btZQCLFail) {
			rml_log->zqcl_fail = gFlhEnv.ulFlashDefaultType.BitMap.btZQCLFail;
		}

		if (gpIDPage->ubTotalCe != (gFlhEnv.ubCENumber * gFlhEnv.ubLUNperTarget)) {
			rml_log->CE_Num_CMP = TRUE;
		}

		rml_log->totalCE = gFlhEnv.ubCENumber * gFlhEnv.ubLUNperTarget;

		if (gsUartPortInfo.B.ubTag == 'D') {
			memcpy(&rml_log->rsv_ddr_result[0], gsUartPortInfo.ubData, 8);
		}
	}

	rml_log->chip_id[0]			 = M_GET_CHIP_ID0();
	rml_log->chip_id[1]			 = M_GET_CHIP_ID1();


	rdt->log[RDT_LOG_RDT_TESTMARK].buf_offset += sizeof(RML_LOG_STRUCT);
	rdt->log[RDT_LOG_RDT_TESTMARK].page_num = rdt_api_page_align_count(rdt->log[RDT_LOG_RDT_TESTMARK].buf_offset);
}

static void rdt_prog_log_header(RDT_API_STRUCT_PTR rdt, U32 pca, U8 log_type)
{
	U32 mark;
	U8 slc_mode = LOG_BLOCK_SLC_MODE;
	//U32 temp_ECC_mode = 0;
#if LDPC_MODE_MODIFY
	U8 ubECCMode = 0;
#endif
	/* Get LCA */
	rdt->dbt_to_bbm_header = 1;
	mark = rdt_set_log_spare_buf(rdt, log_type);
	rdt->dbt_to_bbm_header = 0;

	/* Prepare 4K data for header */
	rdt_api_dmac_setvalue((U32)(0x0), (U32)(rdt->temp_write_buffer), (U32)LC_BYTE_SIZE);
	((U16 *)(rdt->temp_write_buffer))[0] = 0xFFFF;	//Reip

	//Add ASCII in header(todo)

	M_UART(RDT_DBG_, "\nprog %b log header: data_buf=%l ulPCA=%l LCA=%l", log_type, rdt->temp_write_buffer, pca, mark);
#if PCA_2_ADDR_DEBUG
	pca_to_addr_printf(pca, 1);
#endif

#if LDPC_MODE_MODIFY
	//temp_ECC_mode = R32_FCON[R32_FCON_LDPC_CFG];
	//R32_FCON[R32_FCON_LDPC_CFG] &= ~CHK_ECC_MODE_MASK;         // clear LDPC mode [2:0]
	//R32_FCON[R32_FCON_LDPC_CFG] |= SET_ECC_MODE_7;             //set  LDPC mode[2:0]
	ubECCMode = (U8)M_GET_ECC_MODE();
	M_SET_ECC_MODE(SET_ECC_MODE_7);
#if E19_FIP_SNAP_READ_WORKAROUND
	FIP_SET_SNAP_READ_DMY(0, 0, 0, 0);
#else
	FIP_SET_SNAP_READ_DMY(gFlhEnv.uwLDPCMode7DMYByteLen[0], gFlhEnv.uwLDPCMode7DMYByteLen[1], gFlhEnv.uwLDPCMode7DMYByteLen[2], gFlhEnv.uwLDPCMode7DMYByteLen[3]);
#endif
#endif

	//rdt_api_cop0_prog(rdt_cb_prog_log, (U32)pca, rdt, RDT_COP0_W_PROGRAM, MULTIPLANE_NO_USE, slc_mode, (U32)(rdt->temp_write_buffer), mark, (SYS_BLK | SYS_BLK_HEADER));
	rdt_api_cop0_prog((void *)NULL, (U32)pca, rdt, RDT_COP0_W_PROGRAM, MULTIPLANE_NO_USE, slc_mode, (U32)(rdt->temp_write_buffer), mark, (SYS_BLK | SYS_BLK_HEADER));

	/* Recieve all program CQ */
	rdt_api_delegate_CQ(BUF_TYPE_WRITE);

#if LDPC_MODE_MODIFY
	//R32_FCON[R32_FCON_LDPC_CFG] &= ~CHK_ECC_MODE_MASK;         // clear LDPC mode [2:0]
	//R32_FCON[R32_FCON_LDPC_CFG] |= temp_ECC_mode;             //set  LDPC mode[2:0]
	M_SET_ECC_MODE(ubECCMode);
	FIP_SET_SNAP_READ_DMY(gFlhEnv.uwNormalDMYByteLen[0], gFlhEnv.uwNormalDMYByteLen[1], gFlhEnv.uwNormalDMYByteLen[2], gFlhEnv.uwNormalDMYByteLen[3]);
#endif

}


static U32 rdt_set_log_pg_data_buf(RDT_API_STRUCT_PTR rdt, U8 log_type, U8 page)
{

	RDT_LOG_STURCT_PTR log = &rdt->log[log_type];

	U32 data_buf;
	U32 remain_data;
	U32 align_byte;
	U32 temp_addr;
	U32 tmp_buf_offset;
	U8 i, mark;
	if (page == (log->page_num - 1)) { // the last page
		tmp_buf_offset = log->buf_offset;
		temp_addr = (log->buf_base + tmp_buf_offset);

		if (log_type == RDT_LOG_RDT_TESTMARK) { //record the saving_info to the rml block
			memcpy((RDT_SAVING_INFO_STRUCT_PTR)temp_addr, (RDT_SAVING_INFO_STRUCT_PTR)&rdt->saving_info, sizeof(RDT_SAVING_INFO_STRUCT));

#if 0
			UartPrintf_HEX("log base =", log->buf_base, FALSE);
			UartPrintf_HEX(" ,buf_offset =", tmp_buf_offset, FALSE);
			UartPrintf_HEX(" ,temp_addr =", temp_addr, TRUE);

			UartPrintf_HEX("rdt->flash_early_bad_cnt =", rdt->saving_info.flash_early_bad_cnt, FALSE);
			UartPrintf_HEX(" ,rml->flash_early_bad_cnt =", &rdt->saving_info->flash_early_bad_cnt, TRUE);
#endif
			tmp_buf_offset += sizeof(RDT_SAVING_INFO_STRUCT);
			temp_addr = (log->buf_base + tmp_buf_offset);
		}

		remain_data = tmp_buf_offset % PAGE_BYTE_SIZE;
		if (remain_data) { // needs to align page
			align_byte = PAGE_BYTE_SIZE - remain_data - 16;// 16 bytes :pre dummy mark: needs to define
			for (i = 0; i < 4; i++) {
				mark = 0xD0 + i;
				memset((U8 *)temp_addr, mark, 4);
				temp_addr += 4;
			}
			memset((U8 *)temp_addr, 0xFF, align_byte);
		}
	}
	data_buf = log->buf_base + (page * PAGE_BYTE_SIZE);

	return data_buf;
}

void rdt_api_prl_program(RDT_API_STRUCT_PTR rdt, U8 prl_state)
{
#if RDT_POWER_RESUME
	PRL_LOG_STRUCT_PTR prl_buffer = (PRL_LOG_STRUCT_PTR)rdt->log[RDT_LOG_POWER_RESUME].buf_base;
	prl_buffer->prl_state = prl_state;

	//add record test pattern in low power mode
	prl_buffer->test_in_normal_power_mode = rdt->test_in_normal_power_mode;//U17
	prl_buffer->inversion_flag = rdt->inversion_flag;
	prl_buffer->ulTotalPowerOnTime = rdt_api_rtt_get_timer_count() + gulAddPowerOnTime;
	memcpy((void *)prl_buffer->port_id, (void *)rdt->port_id, sizeof(rdt->port_id));

	rdt->log[RDT_LOG_POWER_RESUME].page_num = 1;

	rdt_api_write_log_to_flash(rdt, RDT_LOG_POWER_RESUME);
#endif
}
//Dylan for test power resume count
void rdt_api_prl_program_test_power_resume_count(RDT_API_STRUCT_PTR rdt, U8 prl_state, U16 unit)
{
#if RDT_POWER_RESUME
	PRL_LOG_STRUCT_PTR prl_buffer = (PRL_LOG_STRUCT_PTR)rdt->log[RDT_LOG_POWER_RESUME].buf_base;
	prl_buffer->prl_state = prl_state;

	//add record test pattern in low power mode
	prl_buffer->test_in_normal_power_mode = rdt->test_in_normal_power_mode;//U17
	prl_buffer->inversion_flag = rdt->inversion_flag;
	prl_buffer->ulTotalPowerOnTime = rdt_api_rtt_get_timer_count() + gulAddPowerOnTime;

	memcpy((void *)prl_buffer->port_id, (void *)rdt->port_id, sizeof(rdt->port_id));

	rdt->log[RDT_LOG_POWER_RESUME].page_num = 1;

	rdt_api_write_log_to_flash(rdt, RDT_LOG_POWER_RESUME);
#endif
}


#if RDT_TT_LOG
void rdt_add_tt_log_to_tdl(RDT_API_STRUCT_PTR rdt)
{
	U32 tdl_buf;
	if ((rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].buf_offset + sizeof(TDL_LOG_STRUCT)) > rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].buf_total_size) {
		rdt_api_write_log_to_flash(rdt, RDT_LOG_TEMPERATURE_DIVERSITY);
	}

	tdl_buf = rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].buf_base + 8192;

	rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].buf_offset = 8192;
	memcpy(((RDT_TEMPERATURE_STRUCT_PTR)(tdl_buf)), ((U32 *)(rdt->tt_log)), RDT_TT_LOG_LENGTH * 4);
	rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].buf_offset += rdt->base.temperature_size;
	rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].page_num = rdt_api_page_align_count(rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].buf_offset);
}
#endif

#define DIRECT_READ_DEBUG 0

void rdt_api_write_log_to_flash(RDT_API_STRUCT_PTR rdt, U8 log_type)
{
	RDT_LOG_STURCT_PTR pLog4update = &rdt->log[log_type];
	RDT_LOG_STURCT_PTR pLog4addr = &rdt->log[log_type];//zerio add
	//COP0_PROG_PAGE_BUF page_buf_prog;
	//PCA_RULE_STRUCT *p_pca_rule = (rdt->fpl->pca_rule + 1);
	//U32 opt;
	U32 addr;
	U32 data_buf;

	//COP0_CMD_DATA_PCA pca;
	U8 page, block ;
	//U8 i;
	U32 temp_inv;
	//PCA_t ulPCA;
	U32 ulLocalPCA;
	U8 slc_mode = LOG_BLOCK_SLC_MODE;
	U8 pca_rule = 0;
	U32 LCA = 0;

	U8 temp_record_erl;
#if (RDT_REFERENCE_WINDOW_RESULT)
	U8 ubClockDivBackUp = 0;
#endif
#if ENABLE_RDT_NO_LOG
	return;
#endif

	temp_inv = R32_FALL[R32_FCTL_CHNL_SET];
	rdt_api_setup_inv(DISABLE);
#if (RDT_REFERENCE_WINDOW_RESULT)
	//Write Read Flash by low speed, while DLL Offset not valid or not scan window yet. -- ******** andry
	ubClockDivBackUp = rdt_api_cop0_clock_div_backup();
	if (FALSE == rdt->btReferenceWindowResult) {
		rdt_api_cop0_clock_div_set(3);
	}
#endif
	/*temp_inv = r32_FLH_CH_ALL[FLHL_CH_CHNL_SET];
	hal_fip_disable_inversion(0xFF);*/

	//Do not record RDT ERL
	temp_record_erl = rdt->record_rdt_erl;
	rdt->record_rdt_erl = 0;

	/* Choose PCA rule */
	pca_rule = PCA_RULE(slc_mode);

	for (page = 0; page < pLog4update->page_num; page++) { //page need to program
		for (block = 0; block < MAX_BLOCK_LOG; block++) {//1 prog & 1 backup //zerio change

			if ( pLog4update->page_offset_in_block < rdt->fpl->geometry.d1_page_per_block ) {
				//log in range
			}
			else if (log_type == RDT_LOG_ERROR_RECORD || log_type == RDT_LOG_ERROR_RECORD_2) {
				if (pLog4update->page_offset_in_block < (rdt->fpl->geometry.d1_page_per_block * 2)) {
					//log in range
				}
				else {
					M_UART(RDT_TEST_, "\n log(%d) out of range", log_type);
					rdt->erllog_full = 1;
				}
			}
			else if (log_type >= RDT_LOG_ECC_DISTRIBUTION_LOG && log_type <= RDT_LOG_ECC_DISTRIBUTION_LOG8) {
				U32 ulNeed_edl_num = rdt->fpl->geometry.die_per_ce * rdt->fpl->geometry.total_bank * rdt->fpl->geometry.channel_per_bank * rdt->fpl->geometry.block_per_plane * rdt->fpl->geometry.plane_per_die * 3;
				U16 uwNeed_edl_page = ulNeed_edl_num / 16 + ((ulNeed_edl_num % 16) ? 1 : 0);
				//M_UART(RDT_TEST_, "\n uwNeed_edl_page=%d,ulNeed_edl_num=%d", uwNeed_edl_page, ulNeed_edl_num);
				U8 ubEdl_block = 0;
				for (U8 i = 1; i <= 8; ++i) {
					if (uwNeed_edl_page <= (rdt->fpl->geometry.d1_page_per_block - 1) * i) {
						ubEdl_block = i;
						break;
					}
				}
				//M_UART(RDT_TEST_, "\n ubEdl_block=%d", ubEdl_block);
				if (pLog4update->page_offset_in_block < (rdt->fpl->geometry.d1_page_per_block * ubEdl_block)) {
					//log in range
				}
				else {
					M_UART(RDT_TEST_, "\n log(%d) out of range,ubEdl_block=%d,uwNeed_edl_page=%d,ulNeed_edl_num=%d", log_type, ubEdl_block, uwNeed_edl_page, ulNeed_edl_num);
					rdt->erllog_full = 1;
				}
			}
			else {
				M_UART(RDT_TEST_, "\n log(%d) out of range", log_type);
				rdt->erllog_full = 1;
			}

			for (U8 i = 0; i < 8; ++i) {

				if (pLog4update->page_offset_in_block == rdt->fpl->geometry.d1_page_per_block * i) {
					if ((log_type == RDT_LOG_ERROR_RECORD) && (i == 1)) {
						log_type = RDT_LOG_ERROR_RECORD_2;
						pLog4addr = &rdt->log[log_type];
					}
					else if (log_type >= RDT_LOG_ECC_DISTRIBUTION_LOG && log_type <= RDT_LOG_ECC_DISTRIBUTION_LOG8) {
						log_type = RDT_LOG_ECC_DISTRIBUTION_LOG + i;
						pLog4addr = &rdt->log[log_type];
					}

					addr = pLog4addr->log_block_addr[block];
					ulLocalPCA = addr;

#if (FW_CATEGORY_FLASH == FLASH_INTEL_N38A_QLC)
					for (U8 i = 0; i < 3; i++) { //N38A分三段Erase
						U32 node = ulLocalPCA;
						if (slc_mode) {
							node |= (((768 * i) & gPCARule_Page.ulMask) << gPCARule_Page.ubShift[pca_rule]);
						}
						else {
							node |= (((3072 * i) & gPCARule_Page.ulMask) << gPCARule_Page.ubShift[pca_rule]);
						}
						U8 cop0_opt = MULTIPLANE_NO_USE;
						rdt_api_cop0_erase((void *)NULL, (U32)node, rdt, cop0_opt, slc_mode);
						rdt_api_delegate_CQ(BUF_TYPE_ERASE);
					}
#else
					//rdt_api_cop0_erase(rdt_cb_erase_log, (U32)ulLocalPCA, rdt, 0, slc_mode);
					rdt_api_cop0_erase((void *)NULL, (U32)ulLocalPCA, rdt, 0, slc_mode);

					/* Recieve all erase CQ */
					rdt_api_delegate_CQ(BUF_TYPE_ERASE);
#endif

					/* Program header at page 0 */
					//rdt_api_prog_log_header(rdt, (U32)(ulPCA.FormatFor512GB.ulPCA), log_type);
					rdt_prog_log_header(rdt, (U32)ulLocalPCA, log_type);

					if (block == (BLOCK_LOG_PROG - 1)) {
						pLog4update->page_offset_in_block++; //only add header page once
					}
					addr |= (GET_FPAGE(1) << gPCARule_Page.ubShift[pca_rule]);
					break;
				}
				else if (pLog4update->page_offset_in_block < rdt->fpl->geometry.d1_page_per_block * (i + 1)) {
					if ((log_type == RDT_LOG_ERROR_RECORD) && (i == 1)) {
						log_type = RDT_LOG_ERROR_RECORD_2;
						pLog4addr = &rdt->log[log_type];
					}
					else if (log_type >= RDT_LOG_ECC_DISTRIBUTION_LOG && log_type <= RDT_LOG_ECC_DISTRIBUTION_LOG8) {
						log_type = RDT_LOG_ECC_DISTRIBUTION_LOG + i;
						pLog4addr = &rdt->log[log_type];
					}
					addr = pLog4addr->log_block_addr[block];
					addr |= (GET_FPAGE(pLog4update->page_offset_in_block % rdt->fpl->geometry.d1_page_per_block) << gPCARule_Page.ubShift[pca_rule]);
					break;
				}
			}

			if (log_type >= RDT_LOG_ECC_DISTRIBUTION_LOG && log_type <= RDT_LOG_ECC_DISTRIBUTION_LOG8) {
				data_buf = rdt_set_log_pg_data_buf(rdt, RDT_LOG_ECC_DISTRIBUTION_LOG, page);
			}
			else if (log_type == RDT_LOG_ERROR_RECORD_2) {
				data_buf = rdt_set_log_pg_data_buf(rdt, RDT_LOG_ERROR_RECORD, page);
			}
			else {
				data_buf = rdt_set_log_pg_data_buf(rdt, log_type, page);
			}

			ulLocalPCA = addr;
			LCA = rdt_set_log_spare_buf(rdt, log_type);

			M_UART(RDT_DBG_, "\nprog log(%d): data_buf=%l ulPCA=%l LCA=%l", log_type, data_buf, ulLocalPCA, LCA);

#if PCA_2_ADDR_DEBUG
			pca_to_addr_printf(ulLocalPCA, 1);
#endif

			//rdt_api_cop0_prog(rdt_cb_prog_log, (U32)ulLocalPCA, rdt, COP0_W_RDT_PROGRAM, MULTIPLANE_NO_USE, slc_mode, (U32)data_buf, LCA, 1);
			rdt_api_cop0_prog((void *)NULL, (U32)ulLocalPCA, rdt, RDT_COP0_W_PROGRAM, MULTIPLANE_NO_USE, slc_mode, (U32)data_buf, LCA, SYS_BLK);
			/* Recieve all program CQ */
			rdt_api_delegate_CQ(BUF_TYPE_WRITE);

#if DIRECT_READ_DEBUG //temp_read_buffer
			U8 ubFrameIndex = 0;
			U32 data_readbuf;
			///////////E13 test read//////////////
			//memset((void *)DBUF_PB_RAM_ADDRESS, 0x00, PAGE_BYTE_SIZE); //0x22000000
			memset((void *)rdt->temp_read_buffer, 0x00, PAGE_BYTE_SIZE);  //0x22000000
			for (ubFrameIndex = 0; ubFrameIndex < gub4kEntrysPerPlane; ++ubFrameIndex) {
				//ulPCA.FormatFor512GB.ulPCA = (addr | ubFrameIndex);
				ulLocalPCA = (addr | ubFrameIndex);
				//data_readbuf = DBUF_PB_RAM_ADDRESS + (LC_BYTE_SIZE * ubFrameIndex);
				data_readbuf = rdt->temp_read_buffer + (LC_BYTE_SIZE * ubFrameIndex);

				//cop0_api_read(rdt_cb_read_log, (U32)(ulPCA.FormatFor512GB.ulPCA), rdt, slc_mode, (U32)data_readbuf, 1);
				M_UART(RDT_TEST_, "\nread log id:%d  data_buf=%l ulPCA=%l ", log_type, data_readbuf, ulLocalPCA);
				rdt_api_cop0_read((void *)NULL, (U32)ulLocalPCA, rdt, slc_mode, (U32)data_readbuf, 1);
			}

			/* Recieve all erase CQ */
			rdt_api_delegate_CQ(BUF_TYPE_READ);

			M_UART(RDT_TEST_, "\nr data_buf=%x", *(U32 *)rdt->temp_read_buffer);
#endif

		}
		pLog4update->page_offset_in_block++;

	}

	pLog4update->buf_offset = 0;
	pLog4update->page_num = 0;

	//Restore record RDT ERL settings
	rdt->record_rdt_erl = temp_record_erl;

	R32_FALL[R32_FCTL_CHNL_SET] = temp_inv;
	//r32_FLH_CH_ALL[FLHL_CH_CHNL_SET] = temp_inv;
#if (RDT_REFERENCE_WINDOW_RESULT)
	//Recover clock div. -- ******** andry
	rdt_api_cop0_clock_div_set(ubClockDivBackUp);
#endif

#if 0
	COP0_READ_PAGE_BUF page_buf_read;
	U32 lc;
	node = 0;
	for (lc = 0; lc < gRdtThreadStruct.p_fpl->geometry.node_per_page; lc++) {

		node |= pLog4update->log_block_addr[0] << (p_pca_rule->rule_block.shift); ;
		node |= pLog4update->page_offset_in_block << (p_pca_rule->rule_page.shift);
		node |= lc;
		memcpy(&page_buf_read, &gRdtThreadStruct.burnin.page_buf_read, sizeof(page_buf_read));
		page_buf_read.pca.fields.pca_l = 0;
		page_buf_read.pca.fields.pca_h = 0;
		page_buf_read.pca.u64 |=  node;
		page_buf_read.buf.phy_fields.bufaddr = 0x02804000 + lc * 0x1000;

		while (!cop0_api_read(&gC1Cop0ApiStruct, rdt_cb_read_log, 2, &gRdtThreadStruct, &page_buf_read, COP0_API_OPT_SLC_MODE | COP0_API_OPT_PRIORITY(COP0_ATTR_QLINK_H0) | COP0_API_OPT_MT_TEMPL(MT_TEMPLATE_TBL_R)  )) {
			rdt->thread_yield();
		}


		gRdtThreadStruct.trig_cnt ++;


	}
	while (gRdtThreadStruct.trig_cnt != gRdtThreadStruct.done_count) {
		rdt->thread_yield();
	}
#endif
}

#if RDT_RECORD_SCAN_WINDOW_LOG
void rdt_api_program_win_log(RDT_API_STRUCT_PTR rdt, U8 is_last)
{
	U32 ping_pong_size = rdt->log[RDT_LOG_SCAN_WIN].buf_total_size / 2;

	if (is_last || (rdt->log[RDT_LOG_SCAN_WIN].buf_offset >= ping_pong_size)) {
		rdt_api_write_log_to_flash(rdt, RDT_LOG_SCAN_WIN);
	}
}

void rdt_api_win_log_add(RDT_API_STRUCT_PTR rdt, PAGE_1_SDLL_NAND_Config_t *win_log)
{
	U32 ping_pong_size = rdt->log[RDT_LOG_SCAN_WIN].buf_total_size / 2;

	if ((rdt->log[RDT_LOG_SCAN_WIN].buf_offset < ping_pong_size)) {
		U32 win_buf = (rdt->log[RDT_LOG_SCAN_WIN].buf_base + rdt->log[RDT_LOG_SCAN_WIN].buf_offset);
		memcpy(((U8 *)(win_buf)), win_log, sizeof(PAGE_1_SDLL_NAND_Config_t));
		rdt->log[RDT_LOG_SCAN_WIN].buf_offset += sizeof(PAGE_1_SDLL_NAND_Config_t);
		rdt->log[RDT_LOG_SCAN_WIN].page_num = rdt_api_page_align_count(rdt->log[RDT_LOG_SCAN_WIN].buf_offset);
	}
}
#endif

#if RDT_RECORD_ECC_DISTRIBUTION
void rdt_api_program_edl_log(RDT_API_STRUCT_PTR rdt, U8 is_last)
{
	if (!rdt->param.rdt_record_ecc_distribution.record_edl_enable) {
		return;
	}

	U32 ping_pong_size = rdt->log[RDT_LOG_ECC_DISTRIBUTION_LOG].buf_total_size / 2;

	if (is_last || (rdt->log[RDT_LOG_ECC_DISTRIBUTION_LOG].buf_offset >= ping_pong_size)) {
		rdt_api_write_log_to_flash(rdt, RDT_LOG_ECC_DISTRIBUTION_LOG);
	}
}

void rdt_api_edl_log_add(RDT_API_STRUCT_PTR rdt, EDL_LOG_STRUCT_PTR edl_log)
{
	U32 ping_pong_size = rdt->log[RDT_LOG_ECC_DISTRIBUTION_LOG].buf_total_size / 2;

	if ((rdt->log[RDT_LOG_ECC_DISTRIBUTION_LOG].buf_offset < ping_pong_size)) {
		U32 edl_buf = (rdt->log[RDT_LOG_ECC_DISTRIBUTION_LOG].buf_base + rdt->log[RDT_LOG_ECC_DISTRIBUTION_LOG].buf_offset);
		memcpy(((U8 *)(edl_buf)), edl_log, sizeof(EDL_LOG_STRUCT));
		rdt->log[RDT_LOG_ECC_DISTRIBUTION_LOG].buf_offset += sizeof(EDL_LOG_STRUCT);
		rdt->log[RDT_LOG_ECC_DISTRIBUTION_LOG].page_num = rdt_api_page_align_count(rdt->log[RDT_LOG_ECC_DISTRIBUTION_LOG].buf_offset);
	}
}
#endif

void rdt_api_program_erl_log(RDT_API_STRUCT_PTR rdt, U8 is_last)
{
	U32 ping_pong_size;

	ping_pong_size = rdt->log[RDT_LOG_ERROR_RECORD].buf_total_size / 2;

	if (is_last || (rdt->log[RDT_LOG_ERROR_RECORD].buf_offset > ping_pong_size)) {
		rdt_api_write_log_to_flash(rdt, RDT_LOG_ERROR_RECORD);
	}
}


void rdt_api_set_erl_log_info(U8 cmd_type, U8 slc_mode, U32 tie_out_pca, U32 int_info, U16 ecc_bit)
{
	//COP0_ERR_LOG_STRUCT_PTR err_log = (COP0_ERR_LOG_STRUCT_PTR)rdt->base.ecc_for_callback_base;

	U8 i;
	U8 record_cnt;
	//U8 lmu;
	//U16 ecc_bit;
	//U16 page;

	//U32 pca[2]; // only consider 2 plane flash now
	BBM_BLOCK_MARK bbm_mark;
	ERL_LOG_STRUCT erl_log;

	U8 ubCMD;
	U8 ubCEIndex, ubPhyPlaneIndex, ubFrame;
	U16 uwPageIndex;
	//U8 slc_mode, pca_rule;
	U8 pca_rule;
	U16 uwUnit;
	U8 ubBank, ubCH;
	U8 ublmu;

	RDT_API_STRUCT_PTR rdt = &gRdtApiStruct;

	/* Do not update RDT ERL and DBT */
#if 1//E13_MODE_EN
	if (!rdt->record_rdt_erl) {
		return;
	}
#else
	if ( rdt->current_state < RDT_STATE_FIND_GOOD_BLOCK_FOR_LOG) {
		M_UART(RDT_DBG_, "--info : state = %d\n", rdt->current_state);
		return;
	}
#endif

	/* Parsing PCA */
	//slc_mode = puoResult->HL.B32_to_B63.Info.btSLCMode;

	pca_rule = PCA_RULE(slc_mode);

	//ubCMD = (U8)puoResult->HL.B32_to_B63.Info.ubCMD;
	ubCMD = cmd_type;

#if 0
	/* get fail count and error pca */
	if (tie_data->common.cmd == COP0_FMAN_CMD_READ) {
		record_cnt = 1;
		pca[0] = tie_out_pca;
	}
	else if ((tie_data->common.cmd == COP0_FMAN_CMD_WRITE) || (tie_data->common.cmd == COP0_FMAN_CMD_ERASE)) {
		record_cnt = rdt_get_plane_error_info(pca_rule, pca, tie_out_pca, tie_data->common.opt_sts);
	}
	else {
		ASSERT(0, 0);
	}
#endif

	record_cnt = 1;
	int j = 0;
	if (!rdt_api_dbt_check_bad(rdt, tie_out_pca, slc_mode)) {
		for (i = 0; i < record_cnt; i++) {
			ubPhyPlaneIndex = PCA_2_PLANE(pca_rule, tie_out_pca);
			ubCEIndex = PCA_2_BANK(pca_rule, tie_out_pca);
			uwPageIndex = PCA_2_PAGE(pca_rule, tie_out_pca);
			ubFrame = PCA_2_NODE(pca_rule, tie_out_pca);
			uwUnit = PCA_2_BLOCK(pca_rule, tie_out_pca);
			ubBank = PCA_2_BANK(pca_rule, tie_out_pca);
			ubCH = PCA_2_CHANNEL(pca_rule, tie_out_pca);
			ublmu = (U8)PCA_2_LMU(pca_rule, tie_out_pca);

			//Dump_RDT(M_UART(RDT_TEST_,"\n new:LCA=%l CMD:%b MSG_TYPE=%b FN=%b SLCmode=%b, ulPCA=%l CE=%b, plane=%b, page=%l, frame=%b, uwUnit=%l, bank=%b, ubCH=%b state=%b",
			//tie_out_pca, cmd_type, cmd_type, cmd_type, slc_mode, tie_out_pca, ubCEIndex, ubPhyPlaneIndex, uwPageIndex, ubFrame, uwUnit, ubBank, ubCH, rdt->rdt_state));


			//			if (ubCMD) {
			//				REG32 *pFlaReg1 = R32_FCTL_CH[0];
			//				FlaCEControl(0, 0, ENABLE);
			//				pFlaReg1[R32_FCTL_PIO_CMD] = 0xCF;
			//				FlaCEControl(0, 0, DISABLE);
			//			}

			//			M_UART(RDT_TEST_,"\n info:CMD:%b SLCmode=%b, ulPCA=%l CE=%b, plane=%b, page=%l, frame=%b, uwUnit=%l, bank=%b, ubCH=%b state=%b , int_info = %x",
			//			(U8)ubCMD, slc_mode, tie_out_pca, ubCEIndex, ubPhyPlaneIndex, uwPageIndex, ubFrame, uwUnit, ubBank, ubCH, rdt->current_state, int_info);

			memset(&erl_log, 0x00, sizeof(ERL_LOG_STRUCT));

			if (slc_mode) { //SLC
				erl_log.err_bbl.physical_page = uwPageIndex;
				erl_log.err_bbl.d1_d3_info_is_a2 = TRUE; //?
			}
			else { //TLC
#if ((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC))
				erl_log.err_bbl.physical_page = uwPageIndex;
#elif((FW_CATEGORY_FLASH == FLASH_YMTC_TAS_TLC) || (FW_CATEGORY_FLASH == FLASH_YMTC_WTS_TLC) || (FW_CATEGORY_FLASH == FLASH_YMTC_EMS_QLC))//zerio wts add
				erl_log.err_bbl.physical_page = uwPageIndex;
#elif(CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
				erl_log.err_bbl.physical_page = uwPageIndex;
#elif(FW_CATEGORY_FLASH == FLASH_INTEL_N38A_QLC)
				erl_log.err_bbl.physical_page = uwPageIndex;
#else
				erl_log.err_bbl.physical_page = (uwPageIndex * rdt->fpl->geometry.lmu_number) + ublmu;
#endif
				erl_log.err_bbl.d1_d3_info_is_a2 = FALSE; //?
			}
			/////////////debug
			//			M_UART(RDT_TEST_,"\n info:CMD:%b SLCmode=%b, ulPCA=%l CE=%b, plane=%b, page=%l, frame=%b, uwUnit=%l, bank=%b, ubCH=%b state=%b , int_info = %x",
			//				(U8)ubCMD, slc_mode, tie_out_pca, ubCEIndex, ubPhyPlaneIndex, uwPageIndex, ubFrame, uwUnit, ubBank, ubCH, rdt->current_state, int_info);
			///////////debug
			if (ubCMD == TIE_OUT_READ) {
				//if ( (puoResult->HL.B32_to_B63.Info.btMSG_TYPE) ) {
				/*if (1) {
					//bbm_mark.u8 = SET_BLOCK_MODE(0, 0, RDT_LATER_BAD);
					//bbm_mark.u8 = SET_BLOCK_MODE(0, RDT_LATER_BAD);
					bbm_mark.fields.bad_type = SET_BLOCK_MODE(0, RDT_LATER_BAD);
					rdt_api_dbt_log_add(rdt, tie_out_pca, bbm_mark, slc_mode);
					erl_log.err_bbl.fail_type =  ERL_READ_RETRY_FAIL ;
					M_UART(RDT_TEST_,"--Read Fail and mark bad.\n");
				}
				else {
					erl_log.err_bbl.fail_type =  ERL_READ_RETRY_OK ;
					M_UART(RDT_TEST_,"--Read retry pass.\n");
				}*/

				if (int_info & UNCORR_UPD_BIT) {
					if (!rdt->eot_detect) {
						bbm_mark.fields.bad_type = SET_BLOCK_MODE(0, DBT_FW_READ_HB_UNC);	//Reip
						rdt_api_dbt_log_add(rdt, tie_out_pca, bbm_mark, slc_mode);
						erl_log.err_bbl.over_err_bit = ECC_MAX_BFC_MASK;
						if (rdt->read_retry_en) {
							erl_log.err_bbl.fail_type =  ERL_READ_RETRY_FAIL ;
							M_UART(RDT_TEST_, "--info1 : Retry Fail and mark bad.\n");
						}
						else {
							erl_log.err_bbl.fail_type =  ERL_READ_FAIL ;
							M_UART(RDT_TEST_, "--info2 : Read Fail and mark bad.\n");
						}
					}
					else {
						erl_log.err_bbl.over_err_bit = ecc_bit;
						if (erl_log.err_bbl.over_err_bit == ECC_MAX_BFC_MASK) {
							bbm_mark.fields.bad_type = SET_BLOCK_MODE(0, DBT_FW_READ_HB_UNC);	//Reip
							rdt_api_dbt_log_add(rdt, tie_out_pca, bbm_mark, slc_mode);
							if (rdt->read_retry_en) {
								erl_log.err_bbl.fail_type =  ERL_READ_RETRY_FAIL ;
								M_UART(RDT_TEST_, "--info3 : Retry Fail and mark bad.\n");
							}
							else {
								erl_log.err_bbl.fail_type =  ERL_READ_FAIL ;
								M_UART(RDT_TEST_, "--info4 : Read Fail and mark bad.\n");
								//M_UART(RDT_TEST_,"error !! CH:%d CE:%d unit:%d plane:%d page:%d Die:%d ecc_cnt:%d PCA:%x\n", ubCH, ubBank, uwUnit, ubPhyPlaneIndex, uwPageIndex, (U8)PCA_2_DIE(pca_rule, tie_out_pca), ecc_bit, tie_out_pca);
							}
						}
						else {
							if (!rdt->micron_record_bec_histogram) {
								bbm_mark.fields.bad_type = SET_BLOCK_MODE(0, DBT_FW_READ_HB_UNC);	//Reip
								rdt_api_dbt_log_add(rdt, tie_out_pca, bbm_mark, slc_mode);
								erl_log.err_bbl.fail_type =  ERL_READ_OVER_ECC ;
								M_UART(RDT_TEST_, "--info5 : Read over ECC and mark bad. retry_en = %d, ecc_bit = %d\n", rdt->read_retry_en, erl_log.err_bbl.over_err_bit);
							}
						}
					}
				}
				else if (int_info & OVER_ECC_ERR_UPD_BIT) {
					if (!rdt->micron_record_bec_histogram) {
						bbm_mark.fields.bad_type = SET_BLOCK_MODE(0, DBT_FW_READ_HB_UNC);	//Reip
						rdt_api_dbt_log_add(rdt, tie_out_pca, bbm_mark, slc_mode);
						erl_log.err_bbl.fail_type =  ERL_READ_OVER_ECC;
						erl_log.err_bbl.over_err_bit = ecc_bit;
						M_UART(RDT_TEST_, "--info6 : Read over ECC and mark bad. retry_en = %d, ecc_bit = %d\n", rdt->read_retry_en, erl_log.err_bbl.over_err_bit);
					}
					else if (int_info & CRC16_ERR_UPD_BIT) {
						bbm_mark.fields.bad_type = SET_BLOCK_MODE(0, DBT_FW_READ_HB_UNC);	//Reip
						rdt_api_dbt_log_add(rdt, tie_out_pca, bbm_mark, slc_mode);
						erl_log.err_bbl.over_err_bit = ecc_bit;
						M_UART(RDT_TEST_, "--info7 : CRC16 Fail no UNC and mark bad, unit = %d.\n", uwUnit);
						erl_log.err_bbl.fail_type = ERL_CRC16_TEST_FAIL;
						//rdt->rdt_err = 1; //Not sure
					}
					else if (int_info & (ERASE_PAGE_UPD_BIT | LCA_ERR_UPD_BIT | LCA_SER_ERR_UPD_BIT)) {
						bbm_mark.fields.bad_type = SET_BLOCK_MODE(0, DBT_FW_READ_HB_UNC);	//Reip
						rdt_api_dbt_log_add(rdt, tie_out_pca, bbm_mark, slc_mode);
						erl_log.err_bbl.over_err_bit = ecc_bit;
						M_UART(RDT_TEST_, "--info8 :Data compare fail(int : %x) mark bad.\n", int_info);
						erl_log.err_bbl.fail_type = ERL_READ_CMP_DATA_FAIL;
						//rdt->rdt_err = 1; //Not sure
					}
					else if (int_info & LCA_ERR_UPD_BIT) {
						bbm_mark.fields.bad_type = SET_BLOCK_MODE(0, DBT_FW_READ_HB_UNC);	//Reip
						rdt_api_dbt_log_add(rdt, tie_out_pca, bbm_mark, slc_mode);
						erl_log.err_bbl.over_err_bit = ecc_bit;
						erl_log.err_bbl.fail_type = ERL_LCA_COMPARE_ERROR;
						M_UART(RDT_TEST_, "--info9 : LCA compare error, intinfo = %x.\n", int_info);
						rdt->rdt_err = ERR_LCA_COMPARE_FAIL;			//for interrupt test
					}
					else {
						erl_log.err_bbl.fail_type =  ERL_READ_RETRY_OK ;
						erl_log.err_bbl.over_err_bit = ecc_bit;
						M_UART(RDT_TEST_, "--info10 : Read retry pass(ecc%d).\n", ecc_bit);
					}

					erl_log.err_bbl.opt_stage = (slc_mode) ? ERL_OPT_SLC_READ : ERL_OPT_READ;
				}	//TIE_OUT_READ
#if 1
				else if (int_info & CRC16_ERR_UPD_BIT) {//Jeffrey
					bbm_mark.fields.bad_type = SET_BLOCK_MODE(0, DBT_FW_READ_HB_UNC);	//Reip
					rdt_api_dbt_log_add(rdt, tie_out_pca, bbm_mark, slc_mode);
					erl_log.err_bbl.over_err_bit = ecc_bit;
					M_UART(RDT_TEST_, "--info11 : CRC16 Fail no UNC and mark bad, unit = %d.\n", uwUnit);
					erl_log.err_bbl.fail_type = ERL_READ_FAIL;
					//rdt->rdt_err = 1; //Not sure
				}
				else if (int_info & (ERASE_PAGE_UPD_BIT | LCA_ERR_UPD_BIT | LCA_SER_ERR_UPD_BIT)) {
					bbm_mark.fields.bad_type = SET_BLOCK_MODE(0, DBT_FW_READ_HB_UNC);	//Reip
					rdt_api_dbt_log_add(rdt, tie_out_pca, bbm_mark, slc_mode);
					erl_log.err_bbl.over_err_bit = ecc_bit;
					M_UART(RDT_TEST_, "--info12 :Data compare fail(int : %x) mark bad.\n", int_info);
					erl_log.err_bbl.fail_type = ERL_READ_CMP_DATA_FAIL;
					//rdt->rdt_err = 1; //Not sure
				}
				else { //Dylan Record RETRY Status in ERL
					erl_log.err_bbl.fail_type =  ERL_READ_RETRY_OK ;
					erl_log.err_bbl.over_err_bit = ecc_bit;
					//M_UART(RDT_TEST_, "--info : Read retry pass.,Succed Step is %d \n", ecc_bit);
					M_UART(RDT_TEST_, "--info13 : Read retry pass(ecc%d).\n", ecc_bit);
				}
				erl_log.err_bbl.opt_stage = (slc_mode) ? ERL_OPT_SLC_READ : ERL_OPT_READ;
#endif
			}
			else if (ubCMD == TIE_OUT_WRITE) {
				//if ( (puoResult->HL.B32_to_B63.Info.btMSG_TYPE) ) {
				if (1) {
#if RDT_CONTINUE_TEST_PROGRAM_FAIL
					bbm_mark.fields.bad_type = SET_BLOCK_MODE(0, DBT_FW_PROGRAM_FAIL);
#else
					bbm_mark.fields.bad_type = SET_BLOCK_MODE(0, DBT_FW_PROGRAM_FAIL);	//Reip
#endif
					rdt_api_dbt_log_add(rdt, tie_out_pca, bbm_mark, slc_mode);
					erl_log.err_bbl.opt_stage = (slc_mode) ? ERL_OPT_SLC_PROGRAM : ERL_OPT_PROGRAM;
					erl_log.err_bbl.fail_type = ERL_PROGRAM_FAIL;
					M_UART(RDT_TEST_, "--info12 : PROG Fail and mark bad.\n");
					i++;

				}
			}	//TIE_OUT_WRITE
			else if (ubCMD == TIE_OUT_ERASE) {
				//if ( (puoResult->HL.B32_to_B63.Info.btMSG_TYPE) ) {
				if (1) {
					bbm_mark.fields.bad_type = SET_BLOCK_MODE(0, DBT_FW_ERASE_FAIL);	//Reip
					rdt_api_dbt_log_add(rdt, tie_out_pca, bbm_mark, slc_mode);
					erl_log.err_bbl.opt_stage = (slc_mode) ? ERL_OPT_SLC_PRE_ERASE : ERL_OPT_PRE_ERASE;
					erl_log.err_bbl.fail_type = ERL_ERASE_FAIL;
					M_UART(RDT_TEST_, "--info13 : Erase Fail and mark bad.\n");
				}
			}	//TIE_OUT_ERASE

			erl_log.err_bbl.loop_number     = rdt->saving_info.test_loop;
			erl_log.err_bbl.cycle_number    = rdt->saving_info.test_cycle;
			erl_log.err_bbl.flash_ce        = rdt_api_show_phy_ce(rdt, ubCEIndex, ubCH);
			erl_log.err_bbl.flash_channel   = ubCH;
			//erl_log.err_bbl.flash_die       = (U8)PCA_2_PHYSICAL_DIE(pca_rule, tie_out_pca); //die_interleave?
			erl_log.err_bbl.flash_die       = (U8)PCA_2_DIE(pca_rule, tie_out_pca);
			erl_log.err_bbl.physical_block  = uwUnit;
			erl_log.err_bbl.flash_plane     = ubPhyPlaneIndex;
			erl_log.err_bbl.rdt_state       = rdt->current_state;
			erl_log.err_bbl.logic_ce        = ubCEIndex;
			if (rdt->program_fail_type == 1) { //0 is program fail , 1 is tp_test fail
				erl_log.err_bbl.current_time = (U16)rdt->tp_total_time;
			}
			else {
				erl_log.err_bbl.current_time = (U16)(rdt_api_rtt_get_timer_count() / (1000 * 60));	//min
			}
			//erl_log.err_bbl.current_time    = (U16)(rdt_api_rtt_get_timer_count() / (1000 * 60));
			//erl_log.err_bbl.page_frame      = (U8)(((pca[i]) >> pca_rule->rule_node.shift) & pca_rule->rule_node.mask);
			erl_log.err_bbl.page_frame      = ubFrame;
			erl_log.err_bbl.ctl_temperature = rdt_read_CTRL_temperature();
			erl_log.err_bbl.d1_d3_info_rsv  = rdt->read_retry_en;


			//[1110] for unknown fail debug
			if (erl_log.err_bbl.fail_type == 0) {
				erl_log.err_bbl.fail_type =  ERL_HANG_FAIL;
				erl_log.err_bbl.ctl_temperature = ubCMD;
				erl_log.err_bbl.over_err_bit = (int_info >> 16) & 0xFFFF;
				erl_log.err_bbl.logic_ce = (int_info >> 8) & 0xFF;
				erl_log.err_bbl.flash_temperature = int_info & 0xFF;
				M_UART(RDT_TEST_, "int_info14: %d", int_info);
				j++;
				M_UART(RDT_TEST_, "times j:/n", j);
				M_UART(RDT_TEST_, "unknow fail! CMD:%b, int_info:%x CH:%d CE:%d Die:%d ecc_cnt:%d \n", ubCMD, int_info, ubCH, ubBank, (U8)PCA_2_DIE(pca_rule, tie_out_pca), ecc_bit);
			}
			if ((erl_log.err_bbl.fail_type != ERL_READ_RETRY_OK) || (ERL_Bypass_Record_HB_RETRY_INFO[erl_log.err_bbl.flash_ce][erl_log.err_bbl.flash_die][erl_log.err_bbl.flash_plane] == 0)) {
				rdt_api_erl_log_add(rdt, &erl_log);   // should not out of memory
			}
			//Change ERL_Record global variable
			if (ERL_Bypass_Record_HB_RETRY_INFO[erl_log.err_bbl.flash_ce][erl_log.err_bbl.flash_die][erl_log.err_bbl.flash_plane] == 0) {

				ERL_Bypass_Record_HB_RETRY_INFO[erl_log.err_bbl.flash_ce][erl_log.err_bbl.flash_die][erl_log.err_bbl.flash_plane] = 1;
			}
			/* set ERL and set bbm mark */
		}
	}
#if CONFIG_RDT_OVER_ECC_EN
	//reset min ecc bit  //Dylan
	guwRdtRecordRRMinEccbitInCh[PCA_2_CHANNEL(pca_rule, tie_out_pca)] = ECC_MAX_BFC_MASK;
#endif
}




static void rdt_write_dbt_to_flash(RDT_API_STRUCT_PTR rdt, U8 log_type)
{
	RDT_LOG_STURCT_PTR pLog = &rdt->log[log_type];
	//COP0_PROG_PAGE_BUF page_buf_prog;
	//PCA_RULE_STRUCT *p_pca_rule = (rdt->fpl->pca_rule + 1);
	//U32 opt;
	U32 addr;
	U32 data_buf;
	//U32 data_readbuf;
	//COP0_CMD_DATA_PCA pca;
	U8 page, block;//, page_idx;
	//U8 block_prog_idx;
	//U8 i;
	U32 temp_inv;
	//PCA_t ulPCA;
	U32 ulLocalPCA;
	U8 slc_mode = LOG_BLOCK_SLC_MODE;
	U8 pca_rule = 0;
	U32 LCA = 0;
	U32 page_idx;

	//U32 temp_ECC_mode = 0;
	U8 ubECCMode = 0;

	temp_inv = R32_FALL[R32_FCTL_CHNL_SET];
	rdt_api_setup_inv(DISABLE);
	/*temp_inv = r32_FLH_CH_ALL[FLHL_CH_CHNL_SET];
	hal_fip_disable_inversion(0xFF);*/
#if (RDT_REFERENCE_WINDOW_RESULT)
	U8 ubClockDivBackUp = 0;
	//Write Read Flash by low speed, while DLL Offset not valid or not scan window yet. -- ******** andry
	ubClockDivBackUp = rdt_api_cop0_clock_div_backup();
	if (FALSE == rdt->btReferenceWindowResult) {
		rdt_api_cop0_clock_div_set(3);
	}
#endif
	/* Choose PCA rule */
	pca_rule = PCA_RULE(slc_mode);

	for (page_idx = 0; page_idx < rdt->fpl->geometry.d1_page_per_block; ++page_idx) { //page need to program
		for (block = 0; block < MAX_BLOCK_LOG; block++) { //1 prog & 1 backup //zerio change
			if (pLog->page_offset_in_block >= (rdt->fpl->geometry.d1_page_per_block * 2) ) { //(rdt->fpl->geometry.d1_page_per_block * 10)
				M_UART(RDT_TEST_, "\n DBT log(%d) out of range", log_type);
				rdt->erllog_full = 1;
			}
			else if ( pLog->page_offset_in_block < rdt->fpl->geometry.d1_page_per_block ) { //use idx 0-1 to program
				addr = pLog->log_block_addr[block];
				//block_prog_idx = 0;
			}

			//M_UART(RDT_TEST_,"\n dbt addr: %l", addr);

			if (addr == INVALID_PCA_VALUE) {
				continue;
			}

			if (pLog->page_offset_in_block == 0) {

			}
			else {
				addr |= (GET_FPAGE(pLog->page_offset_in_block % rdt->fpl->geometry.d1_page_per_block) << gPCARule_Page.ubShift[pca_rule]);
				//addr |= (pLog->page_offset_in_block % rdt->fpl->geometry.d1_page_per_block) << p_pca_rule->rule_page.shift;
			}

			//M_UART(RDT_TEST_,"\n dbt addr with page: %l %l", addr, pLog->page_offset_in_block);

			page = page_idx % (pLog->page_num);
			//data_buf = rdt_set_log_pg_data_buf(rdt, log_type, page); //Penny
			data_buf = pLog->buf_base + (page * PAGE_BYTE_SIZE);
			//M_UART(RDT_TEST_,"\n dbt data_buf: %l base: %l page_num=%l page_idx=%l page=%l", data_buf, pLog->buf_base, pLog->page_num, page_idx, page);
			//ulPCA.FormatFor512GB.ulPCA = addr;
			ulLocalPCA = addr;

			if (page == 0) {
				rdt->dbt_to_bbm_header = 1; //DBT header
#if LDPC_MODE_MODIFY
				//temp_ECC_mode = R32_FCON[R32_FCON_LDPC_CFG];
				//R32_FCON[R32_FCON_LDPC_CFG] &= ~CHK_ECC_MODE_MASK;         // clear LDPC mode [2:0]
				//R32_FCON[R32_FCON_LDPC_CFG] |= SET_ECC_MODE_7;             //set  LDPC mode[2:0]
				ubECCMode = (U8)M_GET_ECC_MODE();
				M_SET_ECC_MODE(SET_ECC_MODE_7);
#if E19_FIP_SNAP_READ_WORKAROUND
				FIP_SET_SNAP_READ_DMY(0, 0, 0, 0);
#else
				FIP_SET_SNAP_READ_DMY(gFlhEnv.uwLDPCMode7DMYByteLen[0], gFlhEnv.uwLDPCMode7DMYByteLen[1], gFlhEnv.uwLDPCMode7DMYByteLen[2], gFlhEnv.uwLDPCMode7DMYByteLen[3]);
#endif
#endif
			}
			LCA = rdt_set_log_spare_buf(rdt, log_type);
			rdt->dbt_to_bbm_header = 0;

			//(M_UART(RDT_TEST_,"\nprog log: data_buf=%l ulLocalPCA=%l LCA=%l page=%l page_idx=%l", data_buf, ulLocalPCA, LCA, page, page_idx));

			//cop0_api_prog(rdt_cb_prog_log, (U32)(ulPCA.FormatFor512GB.ulPCA), rdt, MULTIPLANE_NO_USE, slc_mode, (U32)data_buf, LCA, 1);
			//cop0_api_prog(rdt_cb_prog_log, (U32)(ulPCA.FormatFor512GB.ulPCA), rdt, MULTIPLANE_NO_USE, slc_mode, (U32)data_buf, LCA, (page == 0) ? (SYS_BLK | SYS_BLK_HEADER) : SYS_BLK);
			rdt_api_cop0_prog((void *)NULL, (U32)ulLocalPCA, rdt, RDT_COP0_W_PROGRAM, MULTIPLANE_NO_USE, slc_mode, (U32)data_buf, LCA, (page == 0) ? (SYS_BLK | SYS_BLK_HEADER) : SYS_BLK);

			/* Recieve all program CQ */
			rdt_api_delegate_CQ(BUF_TYPE_WRITE);


			/* Restore LDPC mode */
			if (page == 0) {
#if LDPC_MODE_MODIFY
				//R32_FCON[R32_FCON_LDPC_CFG] &= ~CHK_ECC_MODE_MASK;         // clear LDPC mode [2:0]
				//R32_FCON[R32_FCON_LDPC_CFG] |= temp_ECC_mode;             //set  LDPC mode[2:0]
				M_SET_ECC_MODE(ubECCMode);
				FIP_SET_SNAP_READ_DMY(gFlhEnv.uwNormalDMYByteLen[0], gFlhEnv.uwNormalDMYByteLen[1], gFlhEnv.uwNormalDMYByteLen[2], gFlhEnv.uwNormalDMYByteLen[3]);
#endif
			}

		}
		pLog->page_offset_in_block++;

	}

	pLog->buf_offset = 0;
	pLog->page_num = 0;

	R32_FALL[R32_FCTL_CHNL_SET] = temp_inv;
	//r32_FLH_CH_ALL[FLHL_CH_CHNL_SET] = temp_inv;
#if (RDT_REFERENCE_WINDOW_RESULT)
	//Recover clock div. -- ******** andry
	rdt_api_cop0_clock_div_set(ubClockDivBackUp);
#endif
}

void rdt_api_exchange_dbt_to_bbm(RDT_API_STRUCT_PTR rdt)
{
	U8 i;
	//COP0_CMD_DATA_PCA pca ;
	//PCA_t ulPCA;
	U32 ulLocalPCA;
	U8 slc_mode = LOG_BLOCK_SLC_MODE;

	for (i = 0 ; i < rdt->sys_blk_record.bbm_block_cnt ; i++) {
		if (rdt->sys_blk_record.bbm_block_addr[i] == INVALID_PCA_VALUE) {
			continue;
		}

		//ulPCA.FormatFor512GB.ulPCA = rdt->sys_blk_record.bbm_block_addr[i];
		ulLocalPCA = rdt->sys_blk_record.bbm_block_addr[i];

		//cop0_api_erase(rdt_erase_test_callback, (U32)(ulPCA.FormatFor512GB.ulPCA), rdt, 0, slc_mode); //rdt_cb_erase_log
		//rdt_api_cop0_erase(rdt_erase_test_callback, (U32)ulLocalPCA, rdt, 0, slc_mode); //rdt_cb_erase_log
#if (FW_CATEGORY_FLASH == FLASH_INTEL_N38A_QLC)
		for (U8 i = 0; i < 3; i++) { //N38A分三段Erase
			U32 node = ulLocalPCA;
			if (slc_mode) {
				node |= (((768 * i) & gPCARule_Page.ulMask) << gPCARule_Page.ubShift[PCA_RULE(slc_mode)]);
			}
			else {
				node |= (((3072 * i) & gPCARule_Page.ulMask) << gPCARule_Page.ubShift[PCA_RULE(slc_mode)]);
			}
			U8 cop0_opt = MULTIPLANE_NO_USE;
			rdt_api_cop0_erase((void *)NULL, (U32)node, rdt, cop0_opt, slc_mode);
			rdt_api_delegate_CQ(BUF_TYPE_ERASE);
		}
#else
		rdt_api_cop0_erase((void *)NULL, (U32)ulLocalPCA, rdt, 0, slc_mode); //rdt_cb_erase_log

		/* Recieve all erase CQ */
		rdt_api_delegate_CQ(BUF_TYPE_ERASE);
#endif

	}

	//chagne dbt-
	rdt->log[RDT_LOG_DBT].log_block_addr[0] = rdt->sys_blk_record.bbm_block_addr[0];
	rdt->log[RDT_LOG_DBT].log_block_addr[1] = rdt->sys_blk_record.bbm_block_addr[1];
	M_UART(RDT_DBG_, "\n dbt page_offset_in_block: %l page_num:%l", rdt->log[RDT_LOG_DBT].page_offset_in_block, rdt->log[RDT_LOG_DBT].page_num);
	//rdt->log[RDT_LOG_DBT].page_num = rdt->log[RDT_LOG_DBT].page_offset_in_block;
	rdt->log[RDT_LOG_DBT].page_offset_in_block = 0;
	//rdt->dbt_to_bbm_header = 1;
	rdt_write_dbt_to_flash(rdt, RDT_LOG_DBT);
	//rdt->dbt_to_bbm_header = 0;
}

void rdt_api_save_log(RDT_API_STRUCT_PTR rdt)
{
	U8 log_type;
	rdt->current_state = RDT_STATE_FINAL_SAVE_LOG_BLOCK;

	//for (log_type = RDT_LOG_DBT; log_type < RDT_LOG_NUM - 1; log_type++) //PRL don't need last save
	//for (log_type = RDT_LOG_DBT; log_type < RDT_LOG_NUM; log_type++) //PRL don't need last save
	for (log_type = RDT_LOG_ERROR_RECORD; log_type < RDT_LOG_NUM; log_type++) { //DBT and PRL don't need last save
		if ((log_type == RDT_LOG_ERROR_RECORD_2) || (log_type == RDT_LOG_POWER_RESUME) || (log_type >= RDT_LOG_ECC_DISTRIBUTION_LOG2 && log_type <= RDT_LOG_ECC_DISTRIBUTION_LOG8)) {
			continue;
		}
		//zerio change
		rdt_api_write_log_to_flash(rdt, log_type);
		//rdt_api_log_add_end_mark(rdt, log_type, 0);
	}
}

#if ENABLE_PARSE_LOG
//slc mode
BOOL vuc_read_log(RDT_API_STRUCT_PTR rdt, U32 log_pca, U16 page, U32 data_buf)
{

	U8  lc;
	U32 node;

	for (lc = 0; lc < rdt->fpl->geometry.node_per_page; lc++) {
		node = log_pca;
		node |= ((GET_FPAGE(page) & gPCARule_Page.ulMask) << gPCARule_Page.ubShift[PCA_RULE(TRUE)]);
		node |= lc;
		//M_UART(RDT_DBG_, "\nread log: data_buf=%l ulPCA=%l ", data_buf, node);
		rdt_api_cop0_read((void *)NULL, (U32)node, rdt, 1, (U32)data_buf, SYS_BLK);
		rdt_api_delegate_CQ(BUF_TYPE_READ);

		//M_UART(RDT_DBG_, "\ndata_buf=%x ", *(U32 *)data_buf, node);
		//M_UART(RDT_DBG_, "\ndata_buf=%x ", ((U32 *)data_buf)[1], node);
		data_buf += LC_BYTE_SIZE;

	}

	return TRUE;
}

void rdt_parse_log(U8 log_id, U32 data_buf )
{
	U16 rdoffset;
	U32 mark;

	if ((log_id == RDT_LOG_ERROR_RECORD) || (log_id == RDT_LOG_ERROR_RECORD_2)) {
		//erl
		U8  erl_failtype;
		U8  erl_flhce;
		U8  erl_flhdie;
		U16 erl_blk;
		U16 erl_page;
		U8  erl_plane;
		U8  erl_ch;
		U8  cycle_number;
		U8  state;
		U16 ecc_bit;
		U8  stage;
		U8 testloop;
		U8 page_frame;
		U16 current_time;

		U8 slc_mode;
		U32 erase_rdy_time;
		U32 prog_rdy_time_avg;
		U32 prog_rdy_time_max;

		U8 RVAddress;
		U8 RVBytesId;
		U8 RVOffset1;
		U8 RVOffset2;
		U32 ZeroDifference;

		for (rdoffset = 0; rdoffset < 512; rdoffset++) {

			if (((*(U32 *)(data_buf + (rdoffset * 32))) == 0) && (rdoffset != 0)) {
				M_UART(RDT_TEST_, "\n----- data end ------");
				break;
			}

			memcpy(&mark, (U32 *)(data_buf + ((rdoffset * 32) + 0)), 4);
			if (mark == 0xEEEEEEEE) {

				break;
			}
			if (mark == 0xD0D0D0D0) {

				break;
			}


			erl_failtype = (*(U32 *)(data_buf + ((rdoffset * 32) + 0) ) & 0x000000ff);
			if ((erl_failtype == ERL_RDY_INFO) \
				|| (erl_failtype == ERL_RDY_SLC_ERASE_OVERTHRED) || (erl_failtype == ERL_RDY_SLC_PROG_OVERTHRED) \
				|| (erl_failtype == ERL_RDY_TLC_ERASE_OVERTHRED) || (erl_failtype == ERL_RDY_TLC_PROG_OVERTHRED)) {	//Reip
				slc_mode = (*(U32 *)(data_buf + ((rdoffset * 32) + 1) ) & 0x000000ff);
				erl_flhce = (*(U32 *)(data_buf + ((rdoffset * 32) + 2) ) & 0x000000ff);
				erl_ch = (*(U32 *)(data_buf + ((rdoffset * 32) + 3) ) & 0x000000ff);
				erl_flhdie = (*(U32 *)(data_buf + ((rdoffset * 32) + 4) ) & 0x000000ff);
				erl_blk =  ((*(U32 *)(data_buf + ((rdoffset * 32) + 6) ) << 8) & 0x0000ff00) + (*(U32 *)(data_buf + ((rdoffset * 32) + 5) ) & 0x000000ff);
				erl_plane = ( *(U32 *)(data_buf + ((rdoffset * 32) + 7) ) & 0x000000ff);
				erase_rdy_time = ((*(U32 *)(data_buf + ((rdoffset * 32) + 11) ) << 24) & 0xff000000) + ((*(U32 *)(data_buf + ((rdoffset * 32) + 10) ) << 16) & 0x00ff0000)
					+ ((*(U32 *)(data_buf + ((rdoffset * 32) + 9) ) << 8) & 0x0000ff00) + (*(U32 *)(data_buf + ((rdoffset * 32) + 8) ) & 0x000000ff);
				prog_rdy_time_avg = ((*(U32 *)(data_buf + ((rdoffset * 32) + 15) ) << 24) & 0xff000000) + ((*(U32 *)(data_buf + ((rdoffset * 32) + 14) ) << 16) & 0x00ff0000)
					+ ((*(U32 *)(data_buf + ((rdoffset * 32) + 13) ) << 8) & 0x0000ff00) + (*(U32 *)(data_buf + ((rdoffset * 32) + 12) ) & 0x000000ff);
				prog_rdy_time_max = ((*(U32 *)(data_buf + ((rdoffset * 32) + 19) ) << 24) & 0xff000000) + ((*(U32 *)(data_buf + ((rdoffset * 32) + 18) ) << 16) & 0x00ff0000)
					+ ((*(U32 *)(data_buf + ((rdoffset * 32) + 17) ) << 8) & 0x0000ff00) + (*(U32 *)(data_buf + ((rdoffset * 32) + 16) ) & 0x000000ff);
			}
			else if ((erl_failtype == ERL_RETRY_SET_FEATURE_TIMEOUT) \
				|| (erl_failtype == ERL_RETRY_SET_FEATURE_FAIL) \
				|| (erl_failtype == ERL_RETRY_VERIFY_FAIL)) {
				RVAddress = (*(U32 *)(data_buf + ((rdoffset * 32) + 1) ) & 0x000000ff);
				RVBytesId = (*(U32 *)(data_buf + ((rdoffset * 32) + 2) ) & 0x000000ff);
				RVOffset1 = (*(U32 *)(data_buf + ((rdoffset * 32) + 3) ) & 0x000000ff);
				RVOffset2 = (*(U32 *)(data_buf + ((rdoffset * 32) + 4) ) & 0x000000ff);
				ZeroDifference = ((*(U32 *)(data_buf + ((rdoffset * 32) + 8) ) << 24) & 0xff000000) + ((*(U32 *)(data_buf + ((rdoffset * 32) + 7) ) << 16) & 0x00ff0000)
					+ ((*(U32 *)(data_buf + ((rdoffset * 32) + 6) ) << 8) & 0x0000ff00) + (*(U32 *)(data_buf + ((rdoffset * 32) + 5) ) & 0x000000ff);
			}
			else {
				erl_flhce = (*(U32 *)(data_buf + ((rdoffset * 32) + 1) ) & 0x000000ff);
				erl_ch = (*(U32 *)(data_buf + ((rdoffset * 32) + 2) ) & 0x000000ff);
				erl_flhdie = (*(U32 *)(data_buf + ((rdoffset * 32) + 3) ) & 0x000000ff);
				erl_blk =  ((*(U32 *)(data_buf + ((rdoffset * 32) + 5) ) << 8) & 0x0000ff00) + (*(U32 *)(data_buf + ((rdoffset * 32) + 4) ) & 0x000000ff);
				erl_page =  ((*(U32 *)(data_buf + ((rdoffset * 32) + 7) ) << 8) & 0x0000ff00) + (*(U32 *)(data_buf + ((rdoffset * 32) + 6)) & 0x000000ff);
				erl_plane = ( *(U32 *)(data_buf + ((rdoffset * 32) + 8) ) & 0x000000ff);
				testloop = ( *(U32 *)(data_buf + ((rdoffset * 32) + 9) ) & 0x000000ff);
				cycle_number = ( *(U32 *)(data_buf + ((rdoffset * 32) + 10) ) & 0x000000ff);
				stage = ( *(U32 *)(data_buf + ((rdoffset * 32) + 11) ) & 0x000000ff);
				ecc_bit = ( *(U32 *)(data_buf + ((rdoffset * 32) + 12) ) & 0x0000ffff);
				state = ( *(U32 *)(data_buf + ((rdoffset * 32) + 14) ) & 0x000000ff);
				current_time = (( *(U32 *)(data_buf + ((rdoffset * 32) + 17) ) & 0x0000FF00) << 8) || ( *(U32 *)(data_buf + ((rdoffset * 32) + 16) ) & 0x000000ff);
				page_frame = (( *(U32 *)(data_buf + ((rdoffset * 32) + 18) ) & 0x000000ff) );
			}


			switch (erl_failtype) {
			case ERL_FACTORY_BAD_BLOCK :
				M_UART(RDT_TEST_, "\n Factory bad block");
				break;
			case ERL_ERASE_FAIL :
				M_UART(RDT_TEST_, "\n Erase fail       ");
				break;
			case  ERL_PROGRAM_FAIL :
				M_UART(RDT_TEST_, "\n Program fail     ");
				break;
			case  ERL_READ_FAIL :
				M_UART(RDT_TEST_, "\n Read unc fail    ");
				break;
			case  ERL_READ_RETRY_OK :
				M_UART(RDT_TEST_, "\n Read unc-rr pass ");
				break;
			case  ERL_READ_RETRY_FAIL :
				M_UART(RDT_TEST_, "\n Read unc-rr fail ");
				break;
			case  ERL_READ_CMP_DATA_FAIL :
				M_UART(RDT_TEST_, "\n Read data compare fail ");
				break;
			case  ERL_LCA_COMPARE_ERROR:
				M_UART(RDT_TEST_, "\n LCA compare fail ");
				break;
			case  ERL_DDR_SRAM_FAIL :
				M_UART(RDT_TEST_, "\n ERL_DDR_SRAM_FAIL");
				break;
			case ERL_READ_OVER_ECC:
				M_UART(RDT_TEST_, "\n ERL_READ_OVER_ECC");
				break;
			case ERL_RDY_INFO:
				M_UART(RDT_TEST_, "\n ERL_RDY_INFO");
				break;
			case ERL_RDY_SLC_ERASE_OVERTHRED:
				M_UART(RDT_TEST_, "\n ERL_RDY_SLC_ERASE_OVERTHRED");	//Reip
				break;
			case ERL_RDY_SLC_PROG_OVERTHRED:
				M_UART(RDT_TEST_, "\n ERL_RDY_SLC_PROG_OVERTHRED");		//Reip
				break;
			case ERL_RDY_TLC_ERASE_OVERTHRED:
				M_UART(RDT_TEST_, "\n ERL_RDY_TLC_ERASE_OVERTHRED");	//Reip
				break;
			case ERL_RDY_TLC_PROG_OVERTHRED:
				M_UART(RDT_TEST_, "\n ERL_RDY_TLC_PROG_OVERTHRED");		//Reip
				break;
			case ERL_RETRY_SET_FEATURE_TIMEOUT:
				M_UART(RDT_TEST_, "\n ERL_RETRY_SET_FEATURE_TIMEOUT");
				break;
			case ERL_RETRY_SET_FEATURE_FAIL:
				M_UART(RDT_TEST_, "\n ERL_RETRY_SET_FEATURE_FAIL");
				break;
			case ERL_RETRY_VERIFY_FAIL:
				M_UART(RDT_TEST_, "\n ERL_RETRY_VERIFY_FAIL");
				break;
			default:
				M_UART(RDT_TEST_, "\n failtype:%d", erl_failtype);
				break;

			}

			if (erl_failtype == ERL_DDR_SRAM_FAIL) {
				switch (erl_flhce & 0x0f) {
				case  0x04 :
					M_UART(RDT_TEST_, " DRAM normal test");
					break;
				case  0x05 :
					M_UART(RDT_TEST_, " DRAM scan window test");
					break;
				case  0x06 :
					M_UART(RDT_TEST_, " DRAM test zebra");
					break;
				case  0x07 :
					M_UART(RDT_TEST_, " DRAM test march");
					break;
				case  0x08 :
					M_UART(RDT_TEST_, " DRAM test burst");
					break;
				case  0x09 :
					M_UART(RDT_TEST_, " DRAM test selfrefresh");
					break;
				case  0x0A :
					M_UART(RDT_TEST_, " SRAM share buffer");
					break;
				case  0x0B :
					M_UART(RDT_TEST_, " SRAM IRAM");
					break;
				case  0x0E :
					M_UART(RDT_TEST_, " other info");
					break;
				case  0x0F :
					M_UART(RDT_TEST_, " the end of log");
					break;
				default:
					M_UART(RDT_TEST_, "\n failtype:%d", erl_failtype);
					break;
				}

				switch (erl_flhce & 0xF0) {
				case  0x10 :
					M_UART(RDT_TEST_, " HW compare fail");
					break;
				case  0x20 :
					M_UART(RDT_TEST_, " CPU compare fail");
					break;
				case  0x30 :
					M_UART(RDT_TEST_, " CRC check fail");
					break;
				case  0xA0 :
					M_UART(RDT_TEST_, " emergency save");
					break;
				case  0xB0 :
					M_UART(RDT_TEST_, " power back");
					break;
				case  0xF0 :
					M_UART(RDT_TEST_, " cycle no errors happen");
					break;

				default:
					M_UART(RDT_TEST_, "\n failtype:%d", erl_failtype);
					break;
				}
				U32 Fail_addr = ( *(U32 *)(data_buf + ((rdoffset * 32) + 4) ) & 0xffffffff);
				U32 Error_data = ( *(U32 *)(data_buf + ((rdoffset * 32) + 8) ) & 0xffffffff);
				U32 Expect_data = ( *(U32 *)(data_buf + ((rdoffset * 32) + 12) ) & 0xffffffff);
				U8 rank_info = ( *(U32 *)(data_buf + ((rdoffset * 32) + 21) ) & 0x000000ff);


				M_UART(RDT_TEST_, " ZONE:%d", (*(U32 *)(data_buf + ((rdoffset * 32) + 2) ) & 0x000000ff));
				M_UART(RDT_TEST_, " Fail addr:%x", Fail_addr);
				M_UART(RDT_TEST_, " Error data:%x", Error_data);
				M_UART(RDT_TEST_, " Expect Data:%x", Expect_data);
				M_UART(RDT_TEST_, " rank_info:%x", rank_info);
				M_UART(RDT_TEST_, " State:%d", (*(U32 *)(data_buf + ((rdoffset * 32) + 3) ) & 0x000000ff));
				M_UART(RDT_TEST_, " temp:%d", (*(U32 *)(data_buf + ((rdoffset * 32) + 20) ) & 0x000000ff));
				M_UART(RDT_TEST_, " cycle:%d", (( *(U32 *)(data_buf + ((rdoffset * 32) + 17) ) & 0x0000FF00) << 8) || ( *(U32 *)(data_buf + ((rdoffset * 32) + 16) ) & 0x000000ff));
				M_UART(RDT_TEST_, " time:%d", (( *(U32 *)(data_buf + ((rdoffset * 32) + 19) ) & 0x0000FF00) << 8) || ( *(U32 *)(data_buf + ((rdoffset * 32) + 18) ) & 0x000000ff));
			}
			if ((erl_failtype == ERL_RDY_INFO) \
				|| (erl_failtype == ERL_RDY_SLC_ERASE_OVERTHRED) || (erl_failtype == ERL_RDY_SLC_PROG_OVERTHRED) \
				|| (erl_failtype == ERL_RDY_TLC_ERASE_OVERTHRED) || (erl_failtype == ERL_RDY_TLC_PROG_OVERTHRED)) {	//Reip
				M_UART(RDT_TEST_, " slc_mode:%d", slc_mode);
				M_UART(RDT_TEST_, " ce:%d", erl_flhce);
				M_UART(RDT_TEST_, " ch:%d", erl_ch);
				M_UART(RDT_TEST_, " die:%d", erl_flhdie);
				M_UART(RDT_TEST_, " blk:%d", erl_blk);
				M_UART(RDT_TEST_, " pln:%d", erl_plane);
				M_UART(RDT_TEST_, " erase_rdy_time:%d", erase_rdy_time);
				M_UART(RDT_TEST_, " prog_rdy_time_avg:%d", prog_rdy_time_avg);
				M_UART(RDT_TEST_, " prog_rdy_time_max:%d", prog_rdy_time_max);
			}
			else if ((erl_failtype == ERL_RETRY_SET_FEATURE_TIMEOUT) \
				|| (erl_failtype == ERL_RETRY_SET_FEATURE_FAIL) \
				|| (erl_failtype == ERL_RETRY_VERIFY_FAIL)) {
				M_UART(RDT_TEST_, " RVAddress:%x", RVAddress);
				M_UART(RDT_TEST_, " RVBytesId:%d", RVBytesId);
				M_UART(RDT_TEST_, " RVOffset1:%x", RVOffset1);
				M_UART(RDT_TEST_, " RVOffset2:%x", RVOffset2);
				M_UART(RDT_TEST_, " ZeroDifference:%d", ZeroDifference);
			}
			else {
				M_UART(RDT_TEST_, " ce:%d", erl_flhce);
				M_UART(RDT_TEST_, " ch:%d", erl_ch);
				M_UART(RDT_TEST_, " die:%d", erl_flhdie);
				M_UART(RDT_TEST_, " blk:%d", erl_blk);
				M_UART(RDT_TEST_, " page:%d", erl_page);
				M_UART(RDT_TEST_, " pln:%d", erl_plane);
				M_UART(RDT_TEST_, " frame:%d", page_frame);
				M_UART(RDT_TEST_, " loop:%d", testloop);
				M_UART(RDT_TEST_, " cyc:%d", cycle_number);
				M_UART(RDT_TEST_, " stage:%d", stage);
				M_UART(RDT_TEST_, " ecc_bit:%d", ecc_bit);
				M_UART(RDT_TEST_, " state:%d", state);
				M_UART(RDT_TEST_, " time:%d", current_time);
			}
		}

	}
	else if (log_id == RDT_LOG_TEMPERATURE_DIVERSITY) {

#if RDT_TT_LOG
		M_UART(RDT_TEST_, "\n TT INFO:");

		U32 tt_log;
		U8 temp_i = 0;
		rdoffset = 0;
		for (temp_i = 0 ; temp_i < RDT_TT_LOG_LENGTH; temp_i++) {

			tt_log = (*(U32 *)(data_buf + ((rdoffset + 8192 + (temp_i * 4)) ) ) & 0xffffffff);
			M_UART(RDT_TEST_, " %l", tt_log);
		}
#endif
		for (rdoffset = 0; rdoffset < 16384 / 8; rdoffset++) {

			if (((*(U32 *)(data_buf + (rdoffset * 8))) == 0) && (rdoffset != 0)) {
				M_UART(RDT_TEST_, "\n----- data end ------");
				break;
			}

			memcpy(&mark, (U32 *)(data_buf + ((rdoffset * 8) + 0)), 4);
			if ( mark == 0xDDDDDDDD ) {

				break;
			}
			if ( mark == 0xD0D0D0D0 ) {

				if ((*(U32 *)(data_buf + FRAME_SIZE)) == 0xFFFFFFFF) {
					break;
				}

				//parse all ce flash temperature
				M_UART(RDT_TEST_, "\n -- all CE flash temperature -- \n");
				U16 offset;
				for (offset = 0; offset < 256 / 8; offset ++) {

					U8 max_t = (*(U32 *)(data_buf + FRAME_SIZE + offset * 4 + 0) ) & 0x000000ff;
					U8 avg_t = (*(U32 *)(data_buf + FRAME_SIZE + offset * 4 + 1) ) & 0x000000ff;
					U8 min_t = (*(U32 *)(data_buf + FRAME_SIZE + offset * 4 + 2) ) & 0x000000ff;

					if (offset == 0) {
						M_UART(RDT_TEST_, "\n CTRL");
					}
					else if (offset == 1) {
						M_UART(RDT_TEST_, "\n EXT sensor");
					}
					else {
						M_UART(RDT_TEST_, "\n CE%d", offset - RDT_CE_START_INDEX);
					}

					M_UART(RDT_TEST_, " max_temperature:%d", max_t);
					M_UART(RDT_TEST_, " avg_temperature:%d", avg_t);
					M_UART(RDT_TEST_, " min_temperature:%d", min_t);

				}
				break;
			}
			if ( mark == 0xE0E0E0E0 ) {

				break;
			}


			U8 loop = (*(U32 *)(data_buf + ((rdoffset * 8) + 0) ) & 0x000000ff);
			U8 cycle = (*(U32 *)(data_buf + ((rdoffset * 8) + 1) ) & 0x000000ff);
			U8 log_count = (*(U32 *)(data_buf + ((rdoffset * 8) + 2) ) & 0x000000ff);
			U8 temperature = (*(U32 *)(data_buf + ((rdoffset * 8) + 3) ) & 0x000000ff);
			U32 time_stamp = (*(U32 *)(data_buf + ((rdoffset * 8) + 4) ) & 0xffffffff);


			M_UART(RDT_TEST_, "\n loop:%d", loop);
			M_UART(RDT_TEST_, " cycle:%d", cycle);
			M_UART(RDT_TEST_, " %s", (log_count & 0x20) ? "SLC" : "TLC");
			M_UART(RDT_TEST_, " temperature:%d", temperature);
			M_UART(RDT_TEST_, " time_stamp:%d", time_stamp);
			M_UART(RDT_TEST_, " round:%d", log_count & 0x0f);


		}

	}
	else if (log_id == RDT_LOG_RDT_TESTMARK) {
		for (rdoffset = 0; rdoffset < 1; rdoffset++) {

			if (((*(U32 *)(data_buf + (rdoffset * 64))) == 0) && (rdoffset != 0)) {
				M_UART(RDT_TEST_, "\n----- data end ------");
				break;
			}

			memcpy(&mark, (U32 *)(data_buf + ((rdoffset * 64) + 0)), 4);

			if ( mark == 0xDDDDDDDD ) {

				break;
			}
			if ( mark == 0xD0D0D0D0 ) {

				break;
			}
			U32 Identify_mark = (*(U32 *)(data_buf + ((rdoffset * 64) + 0) ) & 0xffffffff);
			U32 Error_Identify_Mark = (*(U32 *)(data_buf + ((rdoffset * 64) + 4) ) & 0xffffffff);
			U32 Current_Time = (*(U32 *)(data_buf + ((rdoffset * 64) + 8) ) & 0xffffffff);
			U8 Fatal_Error_Id = (*(U32 *)(data_buf + ((rdoffset * 64) + 12) ) & 0x000000ff);
			U8 Error_State = (*(U32 *)(data_buf + ((rdoffset * 64) + 13) ) & 0x000000ff);

			M_UART(RDT_TEST_, "\n");
			switch (Identify_mark) {
			case RML_IMARK_DDR_TEST_START :
				M_UART(RDT_TEST_, " DDR_TEST_START ");
				break;
			case  RML_IMARK_DDR_TEST_END :
				M_UART(RDT_TEST_, " DDR_TEST_END   ");
				break;
			case RML_IMARK_SRAM_TEST_START :
				M_UART(RDT_TEST_, " SRAM_TEST_START");
				break;
			case RML_IMARK_SRAM_TEST_END :
				M_UART(RDT_TEST_, " SRAM_TEST_END  ");
				break;
			case RML_IMARK_TLC_TEST_START :
				M_UART(RDT_TEST_, " TLC_TEST_START ");
				break;
			case RML_IMARK_TLC_TEST_END :
				M_UART(RDT_TEST_, " TLC_TEST_END   ");
				break;
			case RML_IMARK_SLC_TEST_START :
				M_UART(RDT_TEST_, " SLC_TEST_START ");
				break;
			case RML_IMARK_SLC_TEST_END :
				M_UART(RDT_TEST_, " SLC_TEST_END   ");
				break;
			case RML_IMARK_RDT_ERROT_OCCUR :
				M_UART(RDT_TEST_, " RDT_ERROT_OCCUR   ");
				break;
			case RML_IMARK_LPM_TEST_START :
				M_UART(RDT_TEST_, " RML_IMARK_LPM_TEST_START   ");
				break;
			case RML_IMARK_LPM_TEST_END :
				M_UART(RDT_TEST_, " RML_IMARK_LPM_TEST_END   ");
				break;
			case RML_IMARK_IC_PATTERN_TEST_START :
				M_UART(RDT_TEST_, " IC_PATTERN_TEST_START  ");
				break;
			case RML_IMARK_IC_PATTERN_TEST_END :
				M_UART(RDT_TEST_, " IC_PATTERN_TEST_END    ");
				break;
			case RML_IMARK_TLC_SAMPLE_BLOCK_START  :
				M_UART(RDT_TEST_, " TLC_SAMPLE_BLOCK_START   ");
				break;
			case RML_IMARK_TLC_SAMPLE_BLOCK_END    :
				M_UART(RDT_TEST_, " TLC_SAMPLE_BLOCK_END   ");
				break;
			case RML_IMARK_SLC_SAMPLE_BLOCK_START  :
				M_UART(RDT_TEST_, " SLC_SAMPLE_BLOCK_START   ");
				break;
			case RML_IMARK_SLC_SAMPLE_BLOCK_END    :
				M_UART(RDT_TEST_, " SLC_SAMPLE_BLOCK_END   ");
				break;
			/*
			case RML_IMARK_DBUF_TEST_START  :
			M_UART(RDT_TEST_," DBUF_TEST_START ");
			break;
			case RML_IMARK_DBUF_TEST_END    :
			M_UART(RDT_TEST_," DBUF_TEST_END   ");
			break;
			*/
			case RML_IMARK_TP_TEST_START    :
				M_UART(RDT_TEST_, " TP_TEST_START  ");
				break;
			case RML_IMARK_TP_TEST_END      :
				M_UART(RDT_TEST_, " TP_TEST_END    ");
				break;
			case RML_IMARK_RR_FUNCTION_TEST_START:
				M_UART(RDT_TEST_, " RR_FUN_TEST_START ");
				break;
			case RML_IMARK_RR_FUNCTION_TEST_END:
				M_UART(RDT_TEST_, " RR_FUN_TEST_END ");
				break;

			case RML_IMARK_BURNIN_TEST_START    :
				M_UART(RDT_TEST_, " BURNIN_TEST_START ");
				break;
			case RML_IMARK_BURNIN_TEST_END      :
				M_UART(RDT_TEST_, " BURNIN_TEST_END ");
				break;

			case RML_IMARK_ENTER_LOW_POWER_MODE    :
				M_UART(RDT_TEST_, " ENTER_LOW_POWER_MODE ");
				break;
			case RML_IMARK_ENTER_NORMAL_POWER_MODE      :
				M_UART(RDT_TEST_, " ENTER_NORMAL_POWER_MODE ");
				break;

			case RML_IMARK_VRLC_TEST_START    :
				M_UART(RDT_TEST_, " VRLC_TEST_START ");
				break;
			case RML_IMARK_VRLC_TEST_END      :
				M_UART(RDT_TEST_, " VRLC_TEST_END ");
				break;

			case RML_IMARK_FIP_IBUF_TEST_START    :
				M_UART(RDT_TEST_, " FIP_IBUF_TEST_START ");
				break;
			case RML_IMARK_FIP_IBUF_TEST_END      :
				M_UART(RDT_TEST_, " IBUF_TEST_END ");
				break;

			case RML_IMARK_COP1_SEARCH_TEST_START    :
				M_UART(RDT_TEST_, " COP1_SEARCH_TEST_START ");
				break;
			case RML_IMARK_COP1_SEARCH_TEST_END      :
				M_UART(RDT_TEST_, " COP1_SEARCH_TEST_END ");
				break;

			case RML_IMARK_RDT_TEST_START    :
				M_UART(RDT_TEST_, " RDT_TEST_START ");
				break;
			case RML_IMARK_RDT_TEST_END      :
				M_UART(RDT_TEST_, " RDT_TEST_END ");
				break;
			case RML_IMARK_WIN_TEST_SLC_START      :
				M_UART(RDT_TEST_, " WIN_TEST_SLC_START ");
				break;
			case RML_IMARK_WIN_TEST_SLC_END      :
				M_UART(RDT_TEST_, " WIN_TEST_SLC_END ");
				break;
			case RML_IMARK_WIN_TEST_TLC_START      :
				M_UART(RDT_TEST_, " WIN_TEST_TLC_START ");
				break;
			case RML_IMARK_WIN_TEST_TLC_END      :
				M_UART(RDT_TEST_, " WIN_TEST_TLC_END ");
				break;
			case RML_IMARK_PAGE_REG_TEST_START      :
				M_UART(RDT_TEST_, " PAGE_REG_TEST_START  ");
				break;
			case RML_IMARK_PAGE_REG_TEST_END     :
				M_UART(RDT_TEST_, " PAGE_REG_TEST_START  ");
				break;
			case RML_IMARK_RETRY_VERIFY_TEST_START      :
				M_UART(RDT_TEST_, " RETRY_VERIFY_TEST_START  ");
				break;
			case RML_IMARK_RETRY_VERIFY_TEST_END     :
				M_UART(RDT_TEST_, " RETRY_VERIFY_TEST_END  ");
				break;
			case RML_IMARK_RDY_TEST_SLC_START      :
				M_UART(RDT_TEST_, " RDY_TEST_SLC_START  ");
				break;
			case RML_IMARK_RDY_TEST_SLC_END     :
				M_UART(RDT_TEST_, " RDY_TEST_SLC_END  ");
				break;
			case RML_IMARK_RDY_TEST_TLC_START      :
				M_UART(RDT_TEST_, " RDY_TEST_TLC_START  ");
				break;
			case RML_IMARK_RDY_TEST_TLC_END     :
				M_UART(RDT_TEST_, " RDY_TEST_TLC_END  ");
				break;

			default:
				if ((Identify_mark & 0xFFFFFF00) == 0x66677700) {
					M_UART(RDT_TEST_, " TLC_TEST_PERCENT_%d0 ", ((Identify_mark & 0x000000FF) >> 4));
				}
				else if ((Identify_mark & 0xFFFFFF00) == 0x88899900) {
					M_UART(RDT_TEST_, " SLC_TEST_PERCENT_%d0 ", ((Identify_mark & 0x000000FF) >> 4));
				}
				else {
					M_UART(RDT_TEST_, "\n Identify_mark:%x", Identify_mark);
				}
				break;
			}

			switch (Error_Identify_Mark) {
			case 0xCCCCCCCC:
				M_UART(RDT_TEST_, " No Error");
				break;
			case 0xEEEEEEEE:
				M_UART(RDT_TEST_, " Error Happen");
				break;
			}
			M_UART(RDT_TEST_, " Current_Time:%d", Current_Time);
			M_UART(RDT_TEST_, " Error_ID:%d", Fatal_Error_Id);
			M_UART(RDT_TEST_, " Error_State :%d", Error_State);

		}
	}
	else if (log_id == RDT_LOG_FLASH_TESTMARK) {

		for (rdoffset = 0; rdoffset < 16384 / 16; rdoffset++) {

			if (((*(U32 *)(data_buf + (rdoffset * 16))) == 0) && (rdoffset != 0)) {
				M_UART(RDT_TEST_, "\n----- data end ------");
				break;
			}

			FML_RECORD_RETRY_PASS_CNT_PTR pRetryPassCnt = (FML_RECORD_RETRY_PASS_CNT_PTR)(data_buf + (rdoffset * 16) + 0);
			if (pRetryPassCnt->identify_mark == 0x99999999) {
				M_UART(RDT_TEST_, "\n Loop%d,Cycle%d,CH%d,Bank%d,SLCMode(%d),RRPassCnt=%d", pRetryPassCnt->ubLoop, pRetryPassCnt->ubCycle, pRetryPassCnt->ubCH, pRetryPassCnt->ubBank, pRetryPassCnt->ubSLCMode, pRetryPassCnt->ulRetryPassCount);
			}
			else if (pRetryPassCnt->identify_mark == 0xAAAAAAAA || pRetryPassCnt->identify_mark == 0xCCCCCCCC) {
				FML_RECORD_BAD_CNT_PTR pBadCnt = (FML_RECORD_BAD_CNT_PTR)(data_buf + (rdoffset * 16) + 0);
				M_UART(RDT_TEST_, "\n test_cycle:%d", pBadCnt->flash_test_cycle);
				M_UART(RDT_TEST_, " early_bad:%d", pBadCnt->early_bad_cnt);
				M_UART(RDT_TEST_, " later_bad:%d", pBadCnt->later_bad_cnt);
				M_UART(RDT_TEST_, " total_bad:%d", pBadCnt->total_bad_cnt);
				M_UART(RDT_TEST_, " current_time:%d", pBadCnt->curr_time_ms);

				switch (pBadCnt->identify_mark) {
				case 0xAAAAAAAA:
					M_UART(RDT_TEST_, " Normal loop");
					break;
				case 0xCCCCCCCC:
					M_UART(RDT_TEST_, " endloop");
					break;
				}
			}
			else if (pRetryPassCnt->identify_mark == 0xD3D3D3D3) {
				break;
			}
		}
	}
#if RDT_RECORD_SCAN_WINDOW_LOG && RDT_SHOW_UART_LOG
	else if (log_id == RDT_LOG_SCAN_WIN) {
		for (rdoffset = 0; rdoffset < 8; rdoffset++) {
			if ((((*(U32 *)(data_buf + (rdoffset * 2048) + 2)) != 0) || ((*(U32 *)(data_buf + ((rdoffset * 2048) + 3)) & 0x000000ff) != 0)) && (rdoffset != 0)) {
				M_UART(RDT_TEST_, "\n----- data end ------");
				break;
			}

			U8 ubFlashSetting0 = (*(U32 *)(data_buf + ((rdoffset * 2048) + 0)) & 0x000000ff);
			U8 ubFlashSetting1 = (*(U32 *)(data_buf + ((rdoffset * 2048) + 1)) & 0x000000ff);

			M_UART(RDT_TEST_, "\n flash Setting : SREn - ODTEn - SDLLEn - DPSEn");
			M_UART(RDT_TEST_, "\n setting 0 : %d - %d - %d - %d", (ubFlashSetting0 & BIT0), ((ubFlashSetting0 >> 1) & BIT0), ((ubFlashSetting0 >> 2) & BIT0), ((ubFlashSetting0 >> 3) & BIT0));
			M_UART(RDT_TEST_, "\n setting 1 : %d - %d - %d - %d", (ubFlashSetting1 & BIT0), ((ubFlashSetting1 >> 1) & BIT0), ((ubFlashSetting1 >> 2) & BIT0), ((ubFlashSetting1 >> 3) & BIT0));

			M_UART(RDT_TEST_, "\n\r\n Parameter");
			U8 ubParamIndex, ubParamValue;
			for (ubParamIndex = 0; ubParamIndex <= 60; ubParamIndex++) {
				ubParamValue = (*(U32 *)(data_buf + ((rdoffset * 2048) + 117 + ubParamIndex)) & 0x000000ff);

				if ((ubParamIndex % 10) == 0) {
					M_UART(RDT_TEST_, "\n");
				}

				M_UART(RDT_TEST_, "%b ", ubParamValue);
			}

			U16 uwChannel0MDLLValue = (*(U32 *)(data_buf + ((rdoffset * 2048) + 512)) & 0x0000ffff);
			U16 uwChannel1MDLLValue = (*(U32 *)(data_buf + ((rdoffset * 2048) + 514)) & 0x0000ffff);
			M_UART(RDT_TEST_, "\n\r\n Channel 0 & Channel 1 MDLL Value : %d & %d", uwChannel0MDLLValue, uwChannel1MDLLValue);

			M_UART(RDT_TEST_, "\n Channel - CE - Die - SDLL Value R - SDLL Value W");

			U8 ubChannel, ubCE, ubDie;
			U16 uwOffset, uwSDLLValueR, uwSDLLValueW;
			for (ubChannel = 0; ubChannel < MAX_CHANNEL; ubChannel++) {
				for (ubCE = 0; ubCE < gFlhEnv.ubCENumberInCh[ubChannel]; ubCE++) {
					if (!(gFlhEnv.ulPhysicalCEBMP & BIT(gubLogicalCEMapPhysicalCE[ubChannel][ubCE]))) {
						continue;
					}

					for (ubDie = 0; ubDie < gFlhEnv.ubLUNperTarget; ubDie++) {
						uwOffset = 516 + (ubChannel * MAX_CE_PER_CHANNEL * MAX_LUN_NUM + ubCE * MAX_LUN_NUM + ubDie) * 2;
						uwSDLLValueR = (*(U32 *)(data_buf + ((rdoffset * 2048) + uwOffset)) & 0x0000ffff);
						uwSDLLValueW = (*(U32 *)(data_buf + ((rdoffset * 2048) + uwOffset + 128)) & 0x0000ffff);
						M_UART(RDT_TEST_, "\n %d - %d - %d - %d - %d", ubChannel, ubCE, ubDie, uwSDLLValueR, uwSDLLValueW);
					}
				}
			}

			U16 uwSDLLMinR, uwSDLLMaxR, uwSDLLMinW, uwSDLLMaxW;
			M_UART(RDT_TEST_, "\n\r\n Channel - CE - Die - [READ] : Min, Max, Max-Min [WRITE] : Min, Max, Max-Min");
			for (ubChannel = 0; ubChannel < MAX_CHANNEL; ubChannel++) {
				for (ubCE = 0; ubCE < gFlhEnv.ubCENumberInCh[ubChannel]; ubCE++) {
					if (!(gFlhEnv.ulPhysicalCEBMP & BIT(gubLogicalCEMapPhysicalCE[ubChannel][ubCE]))) {
						continue;
					}

					for (ubDie = 0; ubDie < gFlhEnv.ubLUNperTarget; ubDie++) {
						uwOffset = 772 + (ubChannel * MAX_CE_PER_CHANNEL * MAX_LUN_NUM + ubCE * MAX_LUN_NUM + ubDie) * 4;
						uwSDLLMinR = (*(U32 *)(data_buf + ((rdoffset * 2048) + uwOffset)) & 0x0000ffff);
						uwSDLLMaxR = (*(U32 *)(data_buf + ((rdoffset * 2048) + uwOffset + 2)) & 0x0000ffff);
						uwSDLLMinW = (*(U32 *)(data_buf + ((rdoffset * 2048) + uwOffset + 256)) & 0x0000ffff);
						uwSDLLMaxW = (*(U32 *)(data_buf + ((rdoffset * 2048) + uwOffset + 256 + 2)) & 0x0000ffff);
						M_UART(RDT_TEST_, "\n %d - %d - %d - [READ] : %d, %d, %d [WRITE] : %d, %d, %d", ubChannel, ubCE, ubDie, uwSDLLMinR, uwSDLLMaxR, uwSDLLMaxR - uwSDLLMinR, uwSDLLMinW, uwSDLLMaxW, uwSDLLMaxW - uwSDLLMinW);
					}
				}
			}

			M_UART(RDT_TEST_, "\n");
			U8 ubDQIndex, ubDQRxDelay, ubDQTxDelay;
			for (ubChannel = 0; ubChannel < MAX_CHANNEL; ubChannel++) {
				for (ubCE = 0; ubCE < gFlhEnv.ubCENumberInCh[ubChannel]; ubCE++) {
					if (!(gFlhEnv.ulPhysicalCEBMP & BIT(gubLogicalCEMapPhysicalCE[ubChannel][ubCE]))) {
						continue;
					}

					for (ubDie = 0; ubDie < gFlhEnv.ubLUNperTarget; ubDie++) {
						M_UART(RDT_TEST_, "\n Channel%d - CE%d - Die%d DQ Delay [Rx - Tx]", ubChannel, ubCE, ubDie);
						uwOffset = 1286 + (ubChannel * MAX_CE_PER_CHANNEL * MAX_LUN_NUM + ubCE * MAX_LUN_NUM + ubDie) * 8;
						for (ubDQIndex = 0; ubDQIndex < 8; ubDQIndex++) {
							ubDQRxDelay = (*(U32 *)(data_buf + ((rdoffset * 2048) + uwOffset + ubDQIndex)) & 0x0000ff) & 0x0f;
							ubDQTxDelay = ((*(U32 *)(data_buf + ((rdoffset * 2048) + uwOffset + ubDQIndex)) & 0x0000ff) >> 4) & 0x0f;

							if ((ubDQIndex % 4) == 0) {
								M_UART(RDT_TEST_, "\n");
							}

							M_UART(RDT_TEST_, "DQ[%d] %d - %d ", ubDQIndex, ubDQRxDelay, ubDQTxDelay);
						}
					}
				}
			}
		}
	}
#endif
#if	RDT_RECORD_ECC_DISTRIBUTION && RDT_SHOW_UART_LOG
	else if (log_id == RDT_LOG_ECC_DISTRIBUTION_LOG) {
		for (rdoffset = 0; rdoffset < 16; rdoffset++) {
			if ((((*(U32 *)(data_buf + (rdoffset * 1024) + 4)) == 0) || ((*(U32 *)(data_buf + ((rdoffset * 1024) + 0)) & 0x000000ff) >= 2)) && (rdoffset != 0)) {
				M_UART(RDT_TEST_, "\n----- data end ------");
				break;
			}

			U8 slc_mode = (*(U32 *)(data_buf + ((rdoffset * 1024) + 0)) & 0x000000ff);
			U8 flash_ce = (*(U32 *)(data_buf + ((rdoffset * 1024) + 2)) & 0x000000ff);
			U8 flash_channel = (*(U32 *)(data_buf + ((rdoffset * 1024) + 3)) & 0x000000ff);
			U8 flash_die = (*(U32 *)(data_buf + ((rdoffset * 1024) + 4)) & 0x000000ff);
			U8 flash_plane = (*(U32 *)(data_buf + ((rdoffset * 1024) + 5)) & 0x000000ff);
			U16 flash_block = (*(U32 *)(data_buf + ((rdoffset * 1024) + 6)) & 0x0000ffff);

			M_UART(RDT_TEST_, "\n flash info : SLCMode%d - CE%d - CH%d - Die%d - Plane%d - Block%d ", slc_mode, flash_ce, flash_channel, flash_die, flash_plane, flash_block);

			U16 error_distribution_index, error_distribution_value;
			for (error_distribution_index = 0; error_distribution_index < 32; error_distribution_index++) {
				error_distribution_value = (*(U32 *)(data_buf + ((rdoffset * 1024) + 10 + error_distribution_index * 2)) & 0x0000ffff);

				if ((error_distribution_index & 0x0F) == 0) {
					M_UART(RDT_TEST_, "\n ErrorBit[%w~%w]:", error_distribution_index, (error_distribution_index + 0x0F));
				}
				M_UART(RDT_TEST_, "%w ", error_distribution_value);
			}
		}
	}
#endif

}

#endif

void rdt_parse_Dbt(RDT_API_STRUCT_PTR rdt)
{
	DBT_LOG_STRUCT dbt;
	U32 bm_tbl_offset;
	U8 plane, ch, bank, die;
	U16 block;
	FPL_GEOMETRY_STRUCT_PTR geometry;
	geometry = &rdt->fpl->geometry;

	dbt.bm_tbl = (BBM_BLOCK_MARK_PTR)(rdt->log[RDT_LOG_DBT].buf_base + SHARED_DBUF_BBM_BUF_SIZE);

	for (ch = 0; ch < geometry->channel_per_bank; ch++) {
		for (bank = 0; bank < geometry->total_bank; bank++) {
			for (die = 0; die < geometry->die_per_ce; die++) {
				for (plane = 0; plane < geometry->plane_per_die; plane++) {
					for (block = 0; block < geometry->block_per_plane; block++) {
						bm_tbl_offset = (ch * geometry->die_per_ce * geometry->total_bank * geometry->plane_per_die * geometry->block_per_plane) +
							(((bank * geometry->die_per_ce) + die) * geometry->plane_per_die * geometry->block_per_plane) +
							(block * geometry->plane_per_die) +
							plane;
						if (dbt.bm_tbl[bm_tbl_offset].fields.bad_type) {
#if (RDT_RUN_ONLINE)
							if (rdt->btRDTOnlineRecordResultFinal) {
								if (SET_BLOCK_MODE(0, DBT_RDT_EARLY_BAD) == dbt.bm_tbl[bm_tbl_offset].fields.bad_type) {
									gpResultFinal->uwEarlyBadPerPlane[ch][bank][die][plane]++;
								}
								else {
									gpResultFinal->uwLaterBadPerPlane[ch][bank][die][plane]++;
								}
							}
#endif
						}
					}
				}
			}
		}
	}
}

BOOL vuc_api_get_rdt_log(RDT_API_STRUCT_PTR rdt, U8 log_id, U32 page_offset, U32 sector_offset)
{
	U8 start_blk_idx = 0;
	U8 end_blk_idx = 0;
	U32 log_pca = 0;
	U32 data_buf = rdt->read.data_buf_base;
	//UartPrintf_DEC("\nvuc_get_rdt_log, page_offset =", page_offset, FALSE);
	//UartPrintf_DEC("\nvuc_get_rdt_log, sector_offset =", sector_offset, FALSE);
	//UartPrintf_DEC(", log id =", log_id, TRUE);
	memset((void *)data_buf, 0x00, PAGE_BYTE_SIZE);  //0x22000000
	if (page_offset < rdt->fpl->geometry.d1_page_per_block) { // block 0~1
		start_blk_idx = 0;
		end_blk_idx = 1;
	}
	else {
		return 1;
	}

	for (start_blk_idx = 0; start_blk_idx <= end_blk_idx; start_blk_idx++) {
		log_pca = rdt->log[log_id].log_block_addr[start_blk_idx];
		if (log_pca != INVALID_PCA_VALUE) {

			if (vuc_read_log(rdt, log_pca, page_offset, data_buf)) {
				break;
			}
		}
	}

	rdt_parse_log(log_id, data_buf);
	return 0;
}

void rdt_api_parse_all_log(RDT_API_STRUCT_PTR rdt)
{
	U32 page;

	//U8 id = 1; //erl
	M_UART(RDT_TEST_, "\n\r\n------READ ERL LOG------");
	for (page = RDT_LOG_HEADER; page < rdt->log[RDT_LOG_ERROR_RECORD].page_offset_in_block; page++) {
		vuc_api_get_rdt_log(rdt, RDT_LOG_ERROR_RECORD, page, 0);
	}

	//id = 2;
	M_UART(RDT_TEST_, "\n\r\n------READ TDL LOG------");
	for (page = RDT_LOG_HEADER; page < rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].page_offset_in_block; page++) {
		vuc_api_get_rdt_log(rdt, RDT_LOG_TEMPERATURE_DIVERSITY, page, 0);
	}

	//id = 3;
	M_UART(RDT_TEST_, "\n\r\n------READ RML LOG------");
	for (page = RDT_LOG_HEADER; page < rdt->log[RDT_LOG_RDT_TESTMARK].page_offset_in_block; page++) {
		vuc_api_get_rdt_log(rdt, RDT_LOG_RDT_TESTMARK, page, 0);
	}

	//id = 4;
	M_UART(RDT_TEST_, "\n\r\n------READ FML LOG------");
	for (page = RDT_LOG_HEADER; page < rdt->log[RDT_LOG_FLASH_TESTMARK].page_offset_in_block; page++) {
		vuc_api_get_rdt_log(rdt, RDT_LOG_FLASH_TESTMARK, page, 0);
	}

#if RDT_RECORD_SCAN_WINDOW_LOG
	M_UART(RDT_TEST_, "\n\r\n------READ WIN LOG------");
	for (page = RDT_LOG_HEADER; page < rdt->log[RDT_LOG_SCAN_WIN].page_offset_in_block; page++) {
		vuc_api_get_rdt_log(rdt, RDT_LOG_SCAN_WIN, page, 0);
	}
#endif

#if RDT_RECORD_ECC_DISTRIBUTION
	M_UART(RDT_TEST_, "\n\r\n------READ EDL LOG------");
	for (page = RDT_LOG_HEADER; page < rdt->log[RDT_LOG_ECC_DISTRIBUTION_LOG].page_offset_in_block; page++) {
		vuc_api_get_rdt_log(rdt, RDT_LOG_ECC_DISTRIBUTION_LOG, page, 0);
	}
#endif

}


#endif /* RDT_MODE_EN */
