#include "hal/pic/uart/uart_api.h"
#include "host/VUC_handler.h"
#include "nvme_api/nvme/shr_hal_nvme.h"
#include "vuc/VUC_WriteReg.h"

void VUC_WriteReg(VUC_OPT_HCMD_PTR_t pCmd)
{
	U8 *ubDesBuf = (U8 *)(pCmd->vuc_sqcmd.vendor.ReadWriteReg.ulRegAddr);
	U8 *ubSrcBuf = (U8 *)(gulVUCBufAddr);
	U8  ubByteCnt = pCmd->vuc_sqcmd.vendor.ReadWriteReg.ubSubFeature;
	U8  ubi;

	M_UART(VUC_, "\nVUC_WRITE_REG");

	if ((1 == ubByteCnt) || (1 == ubByteCnt) || (4 == ubByteCnt) || (8 == ubByteCnt)) {
		for (ubi = 0; ubi < ubByteCnt; ubi++ ) {
			ubDesBuf[ubi] = ubSrcBuf[ubi];
		}
	}
	else {
		pCmd->ubState = CMD_ERROR;
		gVUCVar.ubVucCmdErrStatus = HOST_ERROR_PARAMETER;
	}

	M_UART(VUC_, "\nBuf[%l] to Des[%l] Len %l", ubSrcBuf, (pCmd->vuc_sqcmd.vendor.ReadWriteReg.ulRegAddr), ubByteCnt);

}
