#ifndef _FTL_TRIM_API_H_
#define _FTL_TRIM_API_H_
#include "ftl/ftl_journal.h"
#include "ftl/ftl_api.h"
#include "ftl/ftl_barrier.h"
#include "table/initinfo_vt/initinfo_vt_api.h"

//==============================================================================
//	Definitions
//==============================================================================
#define JOURNAL_MAX_NUM_FOR_A_TRIM_CMD	(2)
#define TRIM_PMD_BMP_SIZE				((PGD_ENTRY_NUM) / (BITS_PER_BYTE)) //Size of PMD Bitmap (PMD involved in Trim PMD)
#define TRIM_INSERT_NODE_PCA			(0xFFFFFFF1) //Special PCA for Trim-Nodes
#define TRIM_CMD_RANGE_NUM_MAX 			(256) //Possible max Range # in a Trim CMD
#if (HOST_MODE == SATA)
#define TRIM_SKIP_CMD_RANGE_NUM_THRESHOLD		(65) //SATA RAID0 DSM every cmd would have 64 ranges.
#else /* (HOST_MODE == SATA) */
#define TRIM_SKIP_CMD_RANGE_NUM_THRESHOLD		(10) //If RangeNum < this threshold, then try DO NOTHING if all Ranges are already trimmed
#endif /* (HOST_MODE == SATA) */
//Trim Queue Depth must be 2^n.
//Otherwise may need to modify expressions involved with Heads & Tail.
//MAX Use is "Depth - 1" for easier full/empty detection
#define TRIM_QUEUE_DEPTH			(256)
#define TRIM_SYNC_QUEUE_DEPTH		(8)

#if PS5021_EN
#define TRIM_LBA_DATA_CTAG		(0x1FD)  //510
#else /* PS5021_EN */
#define TRIM_LBA_DATA_CTAG		(0xF9)
#endif /* PS5021_EN */
//==============================================================================
//	Macro Functions
//==============================================================================
#define M_TRIM_IS_DOING_BG_TRIM()					((TRIM_EN) && gpVT->FTL.btNowTrim && TrimIsDoingBGTrim())
#define M_TRIM_IS_DOING_NON_BG_TRIM()				((TRIM_EN) && gpVT->FTL.btNowTrim && TrimIsDoingNonBGTrim())
#define M_TRIM_IS_DOING_SPOR_TRIM_DELAY_CASE()		((TRIM_EN) && gpVT->FTL.btNowTrim && TrimIsDoingSPORDelayMode())
#define M_TRIM_IS_TRIM_USING_APU_DEP()				((TRIM_EN) && gpVT->FTL.btNowTrim && TrimIsTrimUsingAPUDep())

//==============================================================================
// Types and Structures
//==============================================================================
typedef struct {
	U32 ulAttribute;
	U32 ulLength;
	U64 uoStartLBA;
} TrimRangeNVMERaw_t;

typedef struct {
	U64 uoStartLBA 	: 48;
	U64 ulLength    : 16;
} TrimRangeSATARaw_t;

#if ((NVME == HOST_MODE) || (USB == HOST_MODE))
typedef TrimRangeNVMERaw_t TrimRangeRaw_t;
#else //((NVME == HOST_MODE) || (USB == HOST_MODE))
typedef TrimRangeSATARaw_t TrimRangeRaw_t;
#endif //((NVME == HOST_MODE) || (USB == HOST_MODE))

//OPAL Mode always in NVME-Format (for larger "Length")
#if (PS5017_EN || PS5021_EN)
//follow DMAC Merge output format
typedef struct {
	U64 uoLength  : 33;
	U64 uoReserve : 31;
	U64 uoStartLBA;
} TrimRange_t;
#else /* (PS5017_EN || PS5021_EN) */
typedef struct {
	U64 uoReserve : 32;
	U64 uoLength  : 32;
	U64 uoStartLBA;
} TrimRange_t;
#endif /* (PS5017_EN || PS5021_EN) */

typedef enum {
	TRIM_MODE_DSM,
	TRIM_MODE_FORMAT_NVM,
	TRIM_MODE_SANITIZE,
	TRIM_MODE_WRITE_ZERO,
	TRIM_MODE_WRITE_ZERO_FUA,
	TRIM_MODE_SPOR,
	TRIM_MODE_SPOR_DELAY,
	TRIM_MODE_OPAL,
	TRIM_MODE_BG,
	TRIM_MODE_VUC_MICRON_UNIFIED_EVENT_LOG,
	TRIM_MODE_NULL,
	TRIM_MODE_NUM
} TrimModeEnum_t;

typedef struct {
	JournalTrimEntry_t *puoFirstTrimEntry;	//The 1st Trim Entry in Journal
	U16 uwTrimEntryNum;	//The number of Trim Entry in Journal
} SPORBackupTrimJournalInfo_t;

typedef enum {
	TRIM_SPOR_TRIM_LBA_DATA_CASE_NOT_ALL_FOUND,
	TRIM_SPOR_TRIM_LBA_DATA_CASE_ALL_FOUND,
	TRIM_SPOR_TRIM_LBA_DATA_CASE_NUM,
} SPORTrimLBADataFoundCaseEnum_t;

//==============================================================================
// Types and Structures
//==============================================================================
typedef enum {
	TRIM_STATE_INIT,
	TRIM_STATE_FG,
	TRIM_STATE_TRIM_CONDITIONAL //For both FG/BG Trim.	Do Trim-Conditional Jobs.
} TrimStateEnum_t;

typedef enum {
	TRIM_INSERT_RANGE_INIT,
	TRIM_INSERT_RANGE_COPY_PRP_WAIT,
	TRIM_INSERT_RANGE_SORT_RANGE,
	TRIM_INSERT_RANGE_SORT_RANGE_WAIT,
	TRIM_INSERT_RANGE_MERGE_RANGE,
	TRIM_INSERT_RANGE_MERGE_RANGE_WAIT,
	TRIM_INSERT_RANGE_HANDLE_MERGE_DONE,
	TRIM_INSERT_RANGE_WAIT_RW_EMPTY,
	TRIM_INSERT_RANGE_PROGRAM_JOURNAL_ADD_DEP,
	TRIM_INSERT_RANGE_FINISH
} TrimInsertRangeStateEnum_t;

typedef enum {
	TRIM_WAIT_RW_EMPTY_INIT,
	TRIM_WAIT_RW_EMPTY_WAIT_W_FIFO_EMPTY,
	TRIM_WAIT_RW_EMPTY_WAIT_WLB_EMPTY,
	TRIM_WAIT_RW_EMPTY_FINISH
} TrimWaitReadWriteEmptyStateEnum_t;

typedef enum {
	TRIM_PROGRAM_JOURNAL_INIT,
	TRIM_PROGRAM_JOURNAL_INSERT_ENTRY,
	TRIM_PROGRAM_JOURNAL_WAIT_SEND_CMD,
	TRIM_PROGRAM_JOURNAL_FINISH
} TrimProgramJournalStateEnum_t;

enum {
	TRIM_LBA_INIT = 0,
	TRIM_LBA_ALLOCATE_PB_SEND_CMD = 1,
	TRIM_LBA_ALLOCATE_PB_WAIT = 2,
	TRIM_LBA_SET_ZEROS_SEND_CMD = 3,
	TRIM_LBA_SET_ZEROS_WAIT = 4,
	TRIM_LBA_GEN_WCQ = 5,
};

//Trim Range Info for FW manage
typedef struct {
	U32 ulStartLBA_bt0_31; //Range Start LBA
	U32	ulEndLBA_bt0_31;  //Range End LBA (End is INCLUDED)
	U16	uwStartLBA_bt32_35  : 4; //Range Start LBA
	U16	uwEndLBA_bt32_35    : 4;  //Range End LBA (End is INCLUDED)
	U16     uwTrimLBAState      : 4;
	U16     uwRsv               : 4;
#if PS5021_EN
	U32 TrimLBAPBOffset			: 10;
	U32 DepTag					: 9; //Tag in Dependency Table
	U32	NSID					: 3;
	U32 btIsTrimCMDFristTrimPMD : 1; //Set if it's the 1st Trim-PMD Task for the Trim CMD
	U32							: 1;
#else /* PS5021_EN */
	U16 TrimLBAPBOffset			: 9;
	U16 btIsTrimCMDFristTrimPMD : 1; //Set if it's the 1st Trim-PMD Task for the Trim CMD
	U16 NSID		    : 5; //E21 & U17 use 5 bits;E13 uses 3 bits;S17 uses 0 bit
	U16	             	    : 1;
	CTAG_t DepTag; //Tag in Dependency Table
#endif /* PS5021_EN */
	union {
		U8 ubAll;
		struct {
			U8 btTrimPTE1st		: 1; //ST3 Trim PTE (1st)
			U8 btTrimPTE2nd		: 1; //ST3 Trim PTE (2nd)
			U8 btTrimPMD		: 1; //ST3 Trim PMD
			U8 btST1			: 1; //ST1 Trim
			U8 btTrimLBA1st		: 1; //Trim Sectors (1st)
			U8 btTrimLBA2nd		: 1; //Trim Sectors (2nd)
			U8 btInsertNode1st	: 1; //Insert Nodes (1st)
			U8 btInsertNode2nd	: 1; //Insert Nodes (2nd)
		};
	} Task;
} TrimRangeInfo_t;

typedef struct {
	TrimSkipLBAAndCnt_t TrimSkipLBAAndCnt;

	U8 aubDiskAlreadyTrimmedBMP[TRIM_ALREADY_TRIMMED_BMP_SIZE_IN_BYTE];
	U8 btIsDiskAllTrimmed	: 1;
	U8 Rsv				: 7;
} TrimSkipInfo_t; //(TRIM_TRY_SKIP_CMD_EN) E13-8253

TYPE_SIZE_CHECK(TrimRangeInfo_t, 16); //Trim Queue Entry Size
STATIC_ASSERT((sizeof(TrimRangeInfo_t) * (TRIM_QUEUE_DEPTH)) <= ((FWLB_TRIM_RANGES_INFO_SIZE_IN_4K) * (BUFFER_SIZE)), "TrimRangeInfo_t Size Error!"); //Trim Queue Total Size <= 4K

typedef struct {
	U32 InsertRangeTotalNum		: 9; //# of Ranges for current Trim CMD
	U32 ConsumeRangeCnt			: 9; //# of Ranges for current Trim CMD that has been added into Dep Table/Trim Q
	U32 InsertJournalCnt		: 9; //# of Ranges for current Trim CMD that has inserted into Journal
	U32							: 5;

	U32 CheckInsertNodeL4KCnt	: 11; //For Insert Node trigger decision. Cnt L4K num for same-PTE
	U32 CheckInsertNodePTE		: 21; //For Insert Node trigger decision. Record the Last PTE.

	U32 btMergeRangeCmdResultSTAFlag	: 1;
	U32 MergeRangeCmdFailIdx			: 17;
	U32									: 14;

	U16 uwCheckWriteLockStartTrimRangeIdx; //DMAC reports this trim range index hit write lock range
	U16 uwCheckWriteLockStartLockingRangeIdx; //Check Tool report this locking range index is the following checked index

	U32 ulHugeRangePartialTrimPMDNextLCA;
	U32 ulHugeRangeTrimPMDLCAStart;
	U32 ulHugeRangeTrimPMDLCAEnd;

	U64 uoPartialInsertRangeLBAStart;	//Since APU Dep has LCA length limit, a Range may be divided into 2+ Ranges.  Record next partial start LBA.
	U8 ubCheckInsertNodeRangeStart;		//For Insert Node trigger decision. Record the current same-PTE Ranges Start.start_pd1_timer
	U8 ubNewTail;//New Tail when adding Ranges. Apply to Trim Q Tail once the new Ranges get ready.
	TrimInsertRangeStateEnum_t State;
	TrimProgramJournalStateEnum_t ProgramJournalState;
	TrimWaitReadWriteEmptyStateEnum_t WaitRWEmptyState;
} TrimInsertRangeFlow_t;

typedef struct {
	U8 btIsSendHostFlowDone				: 1;
	U8 btHasSentAddDepRequest			: 1; //For add-dep. Can only send request once!
	U8 btHasPendingTrimConditional		: 1; //Set whenever send CMD "Trim PMD"
	U8 btDoingInsertRange				: 1; //Set to trigger sub-flow "Insert Range"
	U8 btDoingTrimConditional			: 1; //Set to start sending Trim Cond
	U8 btTrimConditionalGCPTEMutexLock	: 1; //Set to get lock. CMD "Trim Cond" & "GCPTE" share registers, Cannot do concurrently.
	U8 btIsTimeOut						: 1; //Set if Time-Out
	U8 btIsHandleCustomizedFlowDone		: 1; //Set when "TrimHandleCustomizedFlow()" handled

	U8 btWaitingST1TrimDone				: 1;
	U8 btWaitingST3TrimDone				: 1;
	U8 btWaitingTrimConditionalDone		: 1;
	U8 btWaitingSyncDone				: 1;
	U8 btIsProgramJournalFlowDone		: 1; //Set when Program Journal Flow is completed
	U8 btIsWaitRWEmptyFlowDone			: 1; //Set when Wait Read/Write Flow is completed
	U8 btIsAddDepBlockedByPrevTrim		: 1; //Set when Add Dep fail due to LCA Range overlapped with previous Trim CMD
	U8 btIsCallBackHasError				: 1; //Set when an error occurs in Trim CallBack Function

	U8 btIsMultipleTrimCMDs				: 1; //Set when currently doing multiple Trim CMDs
	U8 btIsJournalEndingEntryDone		: 1; //Set when the "Ending Entry" in Journal has inserted
	U8 btIsTrimLBALastDataGoToJournal	: 1;
	U8 btIsFirstTrimPMDFound			: 1;
	U8 btIsNeedFWSetTrimMark			: 1; //Set if need to Set Trim Mark in FW Zone for Programming Data/Journal
	U8 btIsSPORTrimDelayModeAllowStart	: 1; //Set if allow SPOR Delay Case to Start
	U8 btIsNeedCheckWriteLockErrorByDMAC: 1; //Set if need to check Wrtie lock error by DMAC Trim Merge
	U8 btIsNeedCheckWriteLockErrorByCPU	: 1; //Set if need to check Wrtie lock error by CPU

	U8 btIsNOAddDep					: 1; //Set to avoid adding Dependency Table
	U8 btIsNoProgramJournal			: 1; //Set to avoid writting Journal
	U8 btIsNOTrimST1				: 1; //Set to avoid generate Trim-ST1 Tasks
	U8 btIsNOTrimLBA				: 1; //Set to avoid generate Trim-LBA Tasks
	U8 btIsNOWaitRWEmpty			: 1; //Set to avoid waiting for Read/Write empty. NO get Host Mgr.
	U8 btIsNOAllowOtherFlowSync		: 1; //Set to avoid Other flows moving GR Sync PTR
	U8 btIsNeedExpandSATADummyRange : 1; //Set to expand SATA Dummy Range after Merge-Range Done. (Only for SATA Format-CMD)
	U8								: 1;

	U8 btIsNeedWaitDepReadEmpty		: 1; //Set if need to wait until both R/W empty before Trim Start
	U8 btIsNeedCloseDSABeforeStart	: 1; //Set if need to close DSA Before Trim Start
	U8 btIsNeedCloseDSAWhenDone		: 1; //Set if need to close DSA Before send Host done
	U8 btIsNeedAddSyncQueue			: 1; //Set if need to add Sync-Info into Trim-Sync-Queue
	U8 btIsNeedWaitSyncDone			: 1; //Set if need to wait Sync Event done before Trim Flow ends
	U8 btIsForceTrimConditional		: 1; //Set if need force clear all Trim-Cond Tasks and then Sync
	U8 btIsSourceFromJournal		: 1; //Set if Trim-Ranges Source is from Journals
	U8 btIsUsingDepTable			: 1; //Set if Trim Flow is using APU Dep Table

	U8 btIsAbortTrimCMD					: 1; //Set if Trim Cmd should be aborted
	U8 btIsNeedCheckGCSustain			: 1; //Set if may need to delay send Host for GC Sustain
	U8 btIsAllowSendHostEarly			: 1; //Set to allow send Host early if Time-Out
	U8 btIsAllowBGTrimBeforeTimeOut		: 1; //Set to allow Trim-Conditional before Time-Out
	U8 btIsForceNVMEFormat				: 1; //Set if need to consider the input Trim-Ranges in NVME-Format
	U8 btIsWaitTrimStableBeforeSendHost	: 1; //Set to force Trim-Cmd-Journal to be stable before send Host Done
	U8 btIsDoingBarrierForTrimStable	: 1; //Set when waiting for Barrier
	U8 btIsCurrentCmd4KMode				: 1; //Set if current Trim Cmd is in 4K Mode

	U32 btIsHugeRangeCaseDoing			: 1; //Set to force to do the New Range's Trim-ST1 & Trim-PMD for Huge-Range Case
	U32 btIsHugeRangeTrimST1Doing		: 1;
	U32 btIsHugeRangeTrimST1Done		: 1;
	U32 btIsHugeRangeTrimPMDDoing		: 1;
	U32 btIsHugeRangeTrimPMDDone		: 1;
	U32 btIsPausingGC					: 1;
	U32 btIsWaitRWEmptyWithoutHostManagement	: 1;
	U32 btIsDRATEn						: 1;
#if (USB == HOST_MODE)
	U32 btIsBreakByHostEvt				: 1;
	U32									: 23;
#else /*(USB == HOST_MODE)*/
	U32									: 24;
#endif /*(USB == HOST_MODE)*/
} TrimFlags_t;

typedef struct {
	SPORBackupTrimJournalInfo_t	*pJournalInfo;

	U32 ubDoingJournalIdx		: 8; //The doing-Journal Index (as Trim Range Source) in SPOR Trim Flow
	U32 TotalJournalNum			: 9; //Total # of Journal in SPOR Trim Flow (as Trim Range Source)
	U32 JournalEntryDoneCnt		: 9; //The # of handled entry in current Journal
	U32							: 6;

	U32 ulVTGRPlaneForSPOR;
	U16 uwJournalBufferOffsetHead;	//from Spor, when done Trim in run-time, give PB back.
} TrimSPOR_t;
#if (IM_B27A || IM_BICS5 || IM_BICS6 || IM_BICS8 || IM_BICS6_QLC)//zerio BICS8 Add//zerio bics6 qlc add

typedef union {
	struct {
		U32 aulElement[3];
	};
	struct {
		U32 GRListUnitIdx			: 3;
		U32 			                    : 29;

		U32 PlaneIdx				       : 20;
		U32 SectorsInPlane			: 5;
		U32 Caller					: 6;
		U32 			                            : 1;

		U32 TrimCmdJournalCnt		: 7;
		U32 ubRangeIdx				: 8;
		U32 btIsWaitTrimConditional	: 1;
		U32 uwGRDataCnt				: 16;
	};
} TrimSyncInfo_t;
#else
typedef union {
	U64 uoAll;
	struct {
		U32 GRListUnitIdx			: 3;
		U32 PlaneIdx				: 18;
		U32 SectorsInPlane			: 5;
		U32 Caller					: 6;

		U32 TrimCmdJournalCnt		: 7;
		U32 ubRangeIdx				: 8;
		U32 btIsWaitTrimConditional	: 1;
		U32 uwGRDataCnt				: 16;
	};
} TrimSyncInfo_t;
#endif

typedef struct {
	U16 Head	: 3;
	U16 Tail	: 3;
	U16 Size	: 4;
	U16			: 6;

	TrimSyncInfo_t uoEntries[TRIM_SYNC_QUEUE_DEPTH];
} TrimSyncQueue_t;

typedef struct {
	TrimSyncQueue_t SyncQueue;
	SyncPtrInfo_t ulAddSyncQueuePtr; //Record the Ptr for the last Trim-CMD-Journal/Trim-LBA-Data in GR
	SyncPtrInfo_t ulLastDSAPtr; //Record the previous DSA GR Ptr (When Trim Flow Ends, will Assign this Value to Latest Safe Ptr)

	U32 PreviousRangePTEEnd	: 20; //Record the previous Range's ending PTE index. (For Estimate Dirty Table Cnt)
	U32 PreviousRangePMDEnd	: 10;  //Record the previous Range's ending PMD index. (For Estimate Dirty Table Cnt)
	U32 btIsTrimNodeInContainer	: 1;
	U32 btIsTrimNodeInDSA		: 1;
} TrimSync_t;

typedef struct {
	U64 uoStartTime;
	U32 ulCopyRangeAddr;
	U32 ulPartialInsertNodeNextLCA;	//Flow "Insert Node" may be partially done due to DSA full. Record next LCA.
	U32 ulPartialTrimPMDNextLCA;	//Trim PMD may be divided into 2+ CMDs for a large Range. Record next LCA.

	U32 ubInsertNodeHead	: 8; //Current Range to Insert Node to ST1
	U32 ubTrimST1Head		: 8; //Current Range to Trim ST1
	U32 ubTrimST3Head		: 8; //Current Range to Trim ST3
	U32 ubTrimLBAHead		: 8; //Current Range to Trim LBAs

	U32 ubHead				: 8; //Trim Queue Head
	U32 ubTail				: 8; //Trim Queue Tail (Excluded)
	U32 Size				: 9; //# of Ranges in queue.  Min: 0  Max: 256
	U32 					: 7;

	U32 TrimLBARemainingCnt	: 10;
#if (PS5021_EN)
	U32 CTag                : 9; //Trim CMD Tag
	U32 NSID                : 4;
	U32                         : 9;
#else /* (PS5021_EN) */
	U32 CTag				: 8; //Trim CMD Tag
#if(USB == HOST_MODE)
	U16 NSID				: 5;
	U16						: 9;
#else/* (USB == HOST_MODE) */
	U32 NSID				: 4;
	U32						: 10;
#endif/* (USB == HOST_MODE) */
#endif /* (PS5021_EN) */

	U8 ubStartRangeIdxForLatestTrimCMD;//Record the Starting Range Index when receive a Trim CMD
	TrimStateEnum_t			State;
	TrimModeEnum_t			Mode;
	TrimFlags_t				Flags;
	TrimSync_t				Sync;
	TrimSPOR_t				SPOR;
	TrimInsertRangeFlow_t	InsertRange; //Trim Sub-Flow: Merge Trim Ranges, Program Journal, Add Dep & Trim Q
	TrimRangeInfo_t			*pRanges;
	TrimSkipInfo_t				SkipInfo; //JIRA_E13-4218, 8253

	U32 ulDebugTrimLBADoneCnt;
	U32 ulDebugTrimLBATaskTotalCnt;
	U64 uoDebugMaxElapsedTime;

	U32 ulCallBackErrorInfoLow;
	U32 ulCallBackErrorInfoHigh;
	U8 ulCallBackErrorCode;
} Trim_t;

/*
 * ---------------------------------------------------------------------------------------------------
 *   extern global variables
 * ---------------------------------------------------------------------------------------------------
 */
extern Trim_t gTrim;

//==============================================================================
//	Functions
//==============================================================================
#if TRIM_EN
AOM_TRIM void Trim(void);
AOM_SPOR U8 TrimSPORInit(SPORBackupTrimJournalInfo_t *pJournalInfo, U16 uwTotalJournalNum, SPORTrimLBADataFoundCaseEnum_t ubSPORTrimLBACase);
AOM_TRIM void TrimSPORDelayModeInit(SPORBackupTrimJournalInfo_t *pJournalInfo, U16 uwTotalJournalNum);

//Table-Update APIs
AOM_TABLE_UPDATE void TrimGetTrimPTERange(U32 *pulLCAStart, U32 *pulLCAEnd);
AOM_TABLE_UPDATE void TrimGetTrimPMDRange(U32 *pulLCAStart, U32 *pulLCAEnd, U16 uwJobNum);
AOM_TABLE_UPDATE void TrimNotifyTrimPTEDone(void);
AOM_TABLE_UPDATE void TrimNotifyTrimPMDDone(void);
AOM_TABLE_UPDATE void TrimNotifyTrimNeedSync(U8 ubGRListUnitIdx, U32 ulPlaneIdx, U8 ubSectorsInPlane, SyncCallerEnum_t ubSyncCaller);
AOM_TABLE_UPDATE void TrimUpdateAllSyncPtrs(U8 ubGRListUnitShiftNum);
AOM_TABLE_UPDATE U8 TrimIsCurrentlyNOProduceTrimLBAData(void);

//Init API
AOM_INIT_2 void TrimInitAll(void);

//Host & NRW APIs
AOM_NRW void TrimSetup(U32 ulPhyAddr, U32 ulNSID, CTAG_t CTag, U16 uwNR, TrimModeEnum_t ubCmdMode);
AOM_NRW void TrimSetTime(void); //Called by TrimSetup()

//Info Block API
AOM_SYSTEM_AREA_INIT void TrimSetFlagFromInfoBlk(U32 *pulSystemBlkTableAddr);

//General APIs, NO Trim-Overlay
void TrimNotifyAllFWSetMarkDone(void);
void TrimNotifyTrimPTEDone(void);
AOM_TABLE_UPDATE void TrimResetSyncGRDataCnts(void);
void TrimSetAddSyncQueuePtr(U8 ubGRListUnitIdx, U32 ulPlaneIdx, U8 ubSectorsInPlane, SyncCallerEnum_t ubCaller);
AOM_TABLE_UPDATE void TrimSetLastDSAPtr(U8 ubGRListUnitIdx, U32 ulPlaneIdx, U8 ubSectorsInPlane, SyncCallerEnum_t ubCaller);
void TrimNotifyTrimJournalProgramCmdSent(void);
#else //TRIM_EN
#define TrimSetTime()								M_FW_ASSERT(ASSERT_TRIM_0x0A60, FALSE)
#define TrimInitAll()								M_FW_ASSERT(ASSERT_TRIM_0x0A61, FALSE)
#define Trim()										M_FW_ASSERT(ASSERT_TRIM_0x0A62, FALSE)
#define TrimSetup(...)								M_FW_ASSERT(ASSERT_TRIM_0x0A63, FALSE)
#define TrimSPORDelayModeInit(...)					M_FW_ASSERT(ASSERT_TRIM_0x0A64, FALSE)
#define TrimNotifyAllFWSetMarkDone()				M_FW_ASSERT(ASSERT_TRIM_0x0A65, FALSE)
#define TrimNotifyTrimPTEDone()						M_FW_ASSERT(ASSERT_TRIM_0x0A66, FALSE)
#define TrimNotifyTrimPMDDone()						M_FW_ASSERT(ASSERT_TRIM_0x0A67, FALSE)
#define TrimGetTrimPTERange(...)					M_FW_ASSERT(ASSERT_TRIM_0x0A68, FALSE)
#define TrimGetTrimPMDRange(...)					M_FW_ASSERT(ASSERT_TRIM_0x0A69, FALSE)
#define TrimNotifyTrimNeedSync(...)					M_FW_ASSERT(ASSERT_TRIM_0x0A6A, FALSE)
#define TrimResetSyncInsertDSACounters()			M_FW_ASSERT(ASSERT_TRIM_0x0A6B, FALSE)
#define TrimNotifyTrimJournalProgramCmdSent()		M_FW_ASSERT(ASSERT_TRIM_0x0A6C, FALSE)
#define TrimResetSyncGRDataCnts()					M_FW_ASSERT(ASSERT_TRIM_0x0A6D, FALSE)
#define TrimSPORInit(...)							(0)
#define TrimIsCurrentlyNOProduceTrimLBAData()		(0)
#define TrimUpdateAllSyncPtrs(...)					(0)
#define TrimSetAddSyncQueuePtr(...)					(0)
#define TrimSetLastDSAPtr(...)						(0)
#define TrimSetFlagFromInfoBlk(...)					(0)
#endif //TRIM_EN

#if (TRIM_TRY_SKIP_CMD_EN)
#if (HOST_MODE == NVME)
AOM_NRW U8 TrimIsRangesAlreadyTrimmed(TrimRangeRaw_t *pRanges, U16 uwNR);
AOM_NRW U8 TrimIsBMPAllTrimmed(U32 ulPMDStartIdx, U32 ulPMDEndIdx);
#elif (HOST_MODE == SATA)
AOM_SATA U8 TrimIsRangesAlreadyTrimmed(TrimRangeRaw_t *pRanges, U16 uwNR);
AOM_SATA U8 TrimIsBMPAllTrimmed(U32 ulPMDStartIdx, U32 ulPMDEndIdx);
#elif (USB == HOST_MODE)
AOM_USB U8 TrimIsRangesAlreadyTrimmed(TrimRangeRaw_t *pRanges, U16 uwNR);
AOM_USB U8 TrimIsBMPAllTrimmed(U32 ulPMDStartIdx, U32 ulPMDEndIdx);
#endif /* (HOST_MODE == NVME) */
#else /* not (TRIM_TRY_SKIP_CMD_EN) */
#define TrimIsRangesAlreadyTrimmed(...)			(0)
#define TrimIsBMPAllTrimmed(...)				(0)
#endif /* (TRIM_TRY_SKIP_CMD_EN) */

/*
 * ---------------------------------------------------------------------------------------------------
 *   inline functions
 * ---------------------------------------------------------------------------------------------------
 */
#if (TRIM_TRY_SKIP_CMD_EN)
INLINE void TrimSetDiskAlreadyTrimmedBMP(U16 uwIdx)
{
	M_SET_BITMAP(gTrim.SkipInfo.aubDiskAlreadyTrimmedBMP, uwIdx);
}

INLINE void TrimClearDiskAlreadyTrimmedBMP(U16 uwIdx)
{
	M_CLEAR_BITMAP(gTrim.SkipInfo.aubDiskAlreadyTrimmedBMP, uwIdx);
}

//Source: BTCM
//Target: VTDBUF (Sync)
INLINE void TrimMemoryCopySkipInfoToVTDBUFSync(void)
{
	memcpy((void *)(&gpVTDBUF->TrimSkipBMPForSync.aubDiskAlreadyTrimmedBMP[0]), (void *)(&gTrim.SkipInfo.aubDiskAlreadyTrimmedBMP[0]), TRIM_ALREADY_TRIMMED_BMP_SIZE_IN_BYTE); //array copy
	gpVTDBUF->TrimSkipBMPForSync.btIsDiskAllTrimmed = gTrim.SkipInfo.btIsDiskAllTrimmed; //bit copy
	gpVTDBUF->TrimSkipLBAAndCntForSync = gTrim.SkipInfo.TrimSkipLBAAndCnt; //struct copy
}

//Source: VTDBUF (Sync)
//Target: BTCM
INLINE void TrimMemoryCopySkipInfoFromVTDBUFSync(void)
{
	memcpy((void *)(&gTrim.SkipInfo.aubDiskAlreadyTrimmedBMP[0]), (void *)(&gpVTDBUF->TrimSkipBMPForSync.aubDiskAlreadyTrimmedBMP[0]), TRIM_ALREADY_TRIMMED_BMP_SIZE_IN_BYTE); //array copy
	gTrim.SkipInfo.btIsDiskAllTrimmed = gpVTDBUF->TrimSkipBMPForSync.btIsDiskAllTrimmed; //bit copy
	gTrim.SkipInfo.TrimSkipLBAAndCnt = gpVTDBUF->TrimSkipLBAAndCntForSync; //struct copy
}

//Source: BTCM
//Target: VTDBUF (LPM)
INLINE void TrimMemoryCopySkipInfoToVTDBUFLPM(void)
{
	memcpy((void *)(&gpVTDBUF->TrimSkipBMPForLPM.aubDiskAlreadyTrimmedBMP[0]), (void *)(&gTrim.SkipInfo.aubDiskAlreadyTrimmedBMP[0]), TRIM_ALREADY_TRIMMED_BMP_SIZE_IN_BYTE); //array copy
	gpVTDBUF->TrimSkipBMPForLPM.btIsDiskAllTrimmed = gTrim.SkipInfo.btIsDiskAllTrimmed; //bit copy
	gpVTDBUF->TrimSkipLBAAndCntForLPM = gTrim.SkipInfo.TrimSkipLBAAndCnt; //struct copy
}

//Source: VTDBUF (LPM)
//Target: BTCM
INLINE void TrimMemoryCopySkipInfoFromVTDBUFLPM(void)
{
	memcpy((void *)(&gTrim.SkipInfo.aubDiskAlreadyTrimmedBMP[0]), (void *)(&gpVTDBUF->TrimSkipBMPForLPM.aubDiskAlreadyTrimmedBMP[0]), TRIM_ALREADY_TRIMMED_BMP_SIZE_IN_BYTE); //array copy
	gTrim.SkipInfo.btIsDiskAllTrimmed = gpVTDBUF->TrimSkipBMPForLPM.btIsDiskAllTrimmed; //bit copy
	gTrim.SkipInfo.TrimSkipLBAAndCnt = gpVTDBUF->TrimSkipLBAAndCntForLPM; //struct copy
}

//Reset Skip-Trim-Info when there are Write Cmds: 1) Confirmed by W Cnt (APUGetCmdLog). 2) Right in InsertCOP1 functions.
INLINE void TrimResetSkipCmdInfo(void)
{
	gTrim.SkipInfo.TrimSkipLBAAndCnt.uoContinuousTrimCmdLBAStart = 0;
	gTrim.SkipInfo.TrimSkipLBAAndCnt.uoContinuousTrimCmdLBAEnd = 0;
	gTrim.SkipInfo.btIsDiskAllTrimmed = FALSE;
}
#else /* not (TRIM_TRY_SKIP_CMD_EN) */
#define TrimSetDiskAlreadyTrimmedBMP(...)		(0)
#define TrimClearDiskAlreadyTrimmedBMP(...)		(0)
#define TrimMemoryCopySkipInfoToVTDBUFSync()	(0)
#define TrimMemoryCopySkipInfoFromVTDBUFSync()	(0)
#define TrimMemoryCopySkipInfoToVTDBUFLPM()	(0)
#define TrimMemoryCopySkipInfoFromVTDBUFLPM()	(0)
#define TrimResetSkipCmdInfo()				(0)
#endif /* (TRIM_TRY_SKIP_CMD_EN) */

#if (TRIM_TRY_SKIP_CMD_PREFORMAT_INIT_EN)
//Set VTDBUF (Sync) all 1s
INLINE void TrimMemorySetDiskAlreadyTrimmedBMPOnVTDBUFSync(void)
{
	memset((void *)(&gpVTDBUF->TrimSkipBMPForSync.aubDiskAlreadyTrimmedBMP[0]), 0xFF, TRIM_ALREADY_TRIMMED_BMP_SIZE_IN_BYTE);
}

//Set VTDBUF (LPM) all 1s
INLINE void TrimMemorySetDiskAlreadyTrimmedBMPOnVTDBUFLPM(void)
{
	memset((void *)(&gpVTDBUF->TrimSkipBMPForLPM.aubDiskAlreadyTrimmedBMP[0]), 0xFF, TRIM_ALREADY_TRIMMED_BMP_SIZE_IN_BYTE);
}
#else /* not (TRIM_TRY_SKIP_CMD_PREFORMAT_INIT_EN) */
#define TrimMemorySetDiskAlreadyTrimmedBMPOnVTDBUFSync()	(0)
#define TrimMemorySetDiskAlreadyTrimmedBMPOnVTDBUFLPM()	(0)
#endif /* (TRIM_TRY_SKIP_CMD_PREFORMAT_INIT_EN) */

#if TRIM_EN
//=================================================
//	Purpose of this function:
//		(1)Stop doing Trim-Cond Jobs
//
//	[Note]
//		(1)This function can be called at anytime
//		(2)Still need to wait for Trim Flow Done.
//=================================================
INLINE void TrimStopTrimConditional(void)
{
	gTrim.Flags.btDoingTrimConditional = FALSE;
}

INLINE void TrimStopSanitizeTrimConditional(void)
{
	gTrim.Flags.btIsForceTrimConditional = FALSE;
	gTrim.Flags.btDoingTrimConditional = FALSE;
	gTrim.Flags.btIsAllowBGTrimBeforeTimeOut = FALSE;
}

INLINE U8 TrimIsDoingBGTrim(void)
{
	return (TRIM_MODE_BG == gTrim.Mode);
}

INLINE U8 TrimIsDoingNonBGTrim(void)
{
	return (TRIM_MODE_BG != gTrim.Mode);
}

INLINE U8 TrimIsDoingSPORDelayMode(void)
{
	return (TRIM_MODE_SPOR_DELAY == gTrim.Mode);
}

INLINE U8 TrimIsTrimUsingAPUDep(void)
{
	return gTrim.Flags.btIsUsingDepTable;
}

INLINE U8 TrimIsNeedFWSetTrimMark(void)
{
	return gTrim.Flags.btIsNeedFWSetTrimMark;
}

INLINE U8 TrimIsForbitSync(void)
{
	return gTrim.Flags.btIsNOAllowOtherFlowSync;
}

INLINE U8 TrimGetTrimLBALastDataGoJournalFlag(void)
{
	return gTrim.Flags.btIsTrimLBALastDataGoToJournal;
}

INLINE void TrimSetTrimLBALastDataGoJournalFlag(U8 ubValue)
{
	gTrim.Flags.btIsTrimLBALastDataGoToJournal = ubValue;
}

INLINE U8 TrimGetPendingTrimConditionalFlag(void)
{
	return gTrim.Flags.btHasPendingTrimConditional;
}

INLINE void TrimSetPendingTrimConditionalFlag(U8 ubValue)
{
	gTrim.Flags.btHasPendingTrimConditional = ubValue;
}

INLINE void TrimSetSPORDelayCaseBuffer(U16 uwLBOffset)
{
	gTrim.SPOR.uwJournalBufferOffsetHead = uwLBOffset;
}

INLINE void TrimAllowSPORDelayCaseStart(void)
{
	gTrim.Flags.btIsSPORTrimDelayModeAllowStart = TRUE;
}

INLINE U8 TrimGetTrimConditionalGCPTEMutexLock(void)
{
	return gTrim.Flags.btTrimConditionalGCPTEMutexLock;
}

INLINE void TrimSetTrimConditionalGCPTEMutexLock(U8 ubValue)
{
	gTrim.Flags.btTrimConditionalGCPTEMutexLock = ubValue;
}

INLINE U8 TrimGetWaitingTrimConditionalFlag(void)
{
	return gTrim.Flags.btWaitingTrimConditionalDone;
}

INLINE void TrimSetWaitingTrimConditionalFlag(U8 ubValue)
{
	gTrim.Flags.btWaitingTrimConditionalDone = ubValue;
}

INLINE U8 TrimGetWaitingST3Flag(void)
{
	return gTrim.Flags.btWaitingST3TrimDone;
}

INLINE U8 TrimIsPausingGC(void)
{
	return (
			(gTrim.Flags.btIsPausingGC) &&
			(INSERT_GCSA_INSERT_GCSA == gpVT->GC.InsertGCSA.State) &&
			(FALSE == gGC.ubSuspendEnable) &&
			(FALSE == M_BUF_CHECK_BUF_STATE(BUF_USING_STATE_OTHER, BUF_SOURCE_DMAC_TEMP)) &&
			(FALSE == M_BUF_CHECK_BUF_STATE(BUF_USING_STATE_OTHER, BUF_SOURCE_COPY_BUF))
		);
}

//===================================================================================
//	Purpose of this Function:
//		Notify Trim-Flow to send the Trim-PMD Job "again"
//
//	[Note]
//		This is a special case that the Trim-PMD is aborted due to Buffer-Shortage.
//===================================================================================
INLINE void TrimNotifyTrimPMDAborted(void)
{
	//Just Unset the "Waiting-Flag" without removing the Trim-Range's Job-Flag
	gTrim.Flags.btWaitingST3TrimDone = 0;
}

//=============================================================
//	Purpose of this Function:
//		Avoid frequently switching code banking.
//
//		Current Policy:
//			NO enter Trim Flow if waiting for Trim PTE/PMD.
//=============================================================
INLINE U8 TrimCheckBarrierTrimJournalDone(void)
{
	return ((gBarrierManager.uwEventDoing.data.uwEvent & BIT_BARRIER_EVENT_TRIM_JOURNAL) && ((gBarrierManager.ubDone & BIT_BARRIER_CMD_COP0_ALL) == BIT_BARRIER_CMD_COP0_ALL));
}

INLINE U8 TrimIsWaiting(void)
{
	return ((TRUE == TrimCheckBarrierTrimJournalDone()) ? FALSE : (gTrim.Flags.btWaitingST3TrimDone || gTrim.Flags.btWaitingTrimConditionalDone));
}

//______________________________________________
//|
//|	btIsTrimNodeInContainer
//|_____________________________________________
INLINE U8 TrimIsTrimNodeInContainer(void)
{
	return gTrim.Sync.btIsTrimNodeInContainer;
}

INLINE void TrimSetBitNodeInContainer(void)
{
	gTrim.Sync.btIsTrimNodeInContainer = TRUE;
}

INLINE void TrimClearBitNodeInContainer(void)
{
	gTrim.Sync.btIsTrimNodeInContainer = FALSE;
}

//______________________________________________
//|
//|	btIsTrimNodeInDSA
//|_____________________________________________
INLINE U8 TrimIsTrimNodeInDSA(void)
{
	return gTrim.Sync.btIsTrimNodeInDSA;
}

INLINE void TrimSetBitNodeInDSA(U8 ubValue)
{
	gTrim.Sync.btIsTrimNodeInDSA = ubValue;
}

INLINE void TrimClearBitNodeInDSA(void)
{
	gTrim.Sync.btIsTrimNodeInDSA = FALSE;
}

INLINE void TrimBackupInfoInVTDBUF(void)
{
	gpVTDBUF->uwTrimInfo.btIsTrimNodeInContainer = gTrim.Sync.btIsTrimNodeInContainer; // TrimNodeInDSA will always be 0 for LPM/Standby Flow
}

#if (FALSE == TRIM_COUNT_TABLE)
INLINE void TrimSetBitPausingGC(void)
{
	if (M_TRIM_IS_DOING_NON_BG_TRIM()) {
		gTrim.Flags.btIsPausingGC = TRUE;
	}
}
INLINE void TrimClearBitPausingGC(void)
{
	if (M_TRIM_IS_DOING_NON_BG_TRIM()) {
		if ((FALSE == gTrim.Flags.btIsSendHostFlowDone) && (FALSE == M_BMU_LB_IS_EMPTY(LB_ID_WRITE_0))) {
			gTrim.Flags.btIsPausingGC = FALSE;
		}
	}
}
#else /* TRIM_COUNT_TABLE */
#define TrimSetBitPausingGC()	(0)
#define TrimClearBitPausingGC()	(0)
#endif /* TRIM_COUNT_TABLE */

#else //TRIM_EN
#define TrimNotifyTrimPMDAborted()					M_FW_ASSERT(ASSERT_TRIM_0x0A6E, FALSE)
#define TrimSetPendingTrimConditionalFlag(...)		M_FW_ASSERT(ASSERT_TRIM_0x0A6F, FALSE)
#define TrimSetSPORDelayCaseBuffer(...)				M_FW_ASSERT(ASSERT_TRIM_0x0A70, FALSE)
#define TrimAllowSPORDelayCaseStart()				M_FW_ASSERT(ASSERT_TRIM_0x0A71, FALSE)
#define TrimSetTrimConditionalGCPTEMutexLock(...)	M_FW_ASSERT(ASSERT_TRIM_0x0A72, FALSE)
#define TrimStopTrimConditional()					M_FW_ASSERT(ASSERT_TRIM_0x0A73, FALSE)
#define TrimStopSanitizeTrimConditional()			M_FW_ASSERT(ASSERT_TRIM_0x0A74, FALSE)
#define TrimSetTrimLBALastDataGoJournalFlag(...)	M_FW_ASSERT(ASSERT_TRIM_0x0A75, FALSE)
#define TrimIsDoingBGTrim()							(0)
#define TrimIsDoingNonBGTrim()						(0)
#define TrimIsDoingSPORDelayMode()					(0)
#define TrimIsTrimUsingAPUDep()						(0)
#define TrimIsNeedFWSetTrimMark()					(0)
#define TrimGetTrimLBALastDataGoJournalFlag()		(0)
#define TrimGetPendingTrimConditionalFlag()			(0)
#define TrimGetTrimConditionalGCPTEMutexLock()		(0)
#define TrimGetWaitingTrimConditionalFlag()			(0)
#define TrimSetWaitingTrimConditionalFlag(...)		(0)
#define TrimGetWaitingST3Flag()						(0)
#define TrimIsPausingGC()							(0)
#define TrimIsForbitSync()							(0)
#define TrimIsWaiting()								(0)
#define TrimCheckBarrierTrimJournalDone()			(0)
#define TrimIsTrimNodeInContainer()					(0)
#define TrimSetBitNodeInContainer()					(0)
#define TrimClearBitNodeInContainer()				(0)
#define TrimIsTrimNodeInDSA()						(0)
#define TrimSetBitNodeInDSA(...)					(0)
#define TrimClearBitNodeInDSA()						(0)
#define TrimBackupInfoInVTDBUF()				(0)
#endif //TRIM_EN
#endif //_FTL_TRIM_API_H_
