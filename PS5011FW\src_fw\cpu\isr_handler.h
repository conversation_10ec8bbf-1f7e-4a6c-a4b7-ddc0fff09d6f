/**************************************************************************/
/*                                                                        */
/*           Copyright (c) 2000-2017 by Phison Electronics Corp.          */
/*                                                                        */
/**************************************************************************/

/**************************************************************************/
/*                                                                        */
/*  FILE : isr_handler.h                   PROJECT : PS5011               */
/*                                                                        */
/*                                                                        */
/*  DESCRIPTION                                                           */
/*                                                                        */
/*    This file implements interrupt handler	                  		  */
/*                                                                        */
/*  RELEASE HISTORY                                                       */
/*                                                                        */
/*    DATE              NAME                DESCRIPTION                   */
/*                                                                        */
/*  2017-05-xx     				            Initial Version 1.0           */
/*                                                                        */
/**************************************************************************/


#ifndef _ISR_HANDLER_H_
#define _ISR_HANDLER_H_

typedef struct {
	U32 ulEventCounter;         // #counter of IRQ interrupt trigger Time
	U32 ubEventGroup;           // #IRQ Interrupt Group
	U32 ulEventCounterLog;   	// #record for FIQ RTT1 Event judging whether the same IRQ interrupt Process
	U32 ulRTT0Time;				// #record RTT0 Cnt in FIQ RTT1 Event Trigger
	U8 ubDoing;                 // #flag represent there is an IRQ Interrupt Doing.
	U8 ubSingleCounter;         // #counter which is counted by  FIQ RTT1 Event during one IRQ interrupt process.
	U8 ubSingleMaxCount;        // #Max Value of gISRDebug.ubSingleCounter
	U8 ubSingleMaxCountGroup;   // Interrupt Group when Max Value of gISRDebug.ubSingleCounter
	U8 ubFIQFinalPosition;		// Record FIQ entry final position
	U8 ubIRQFinalPosition;		// Record IRQ entry final position
} ISRDebug_t;
extern ISRDebug_t gISRDebug;

#if (U17_EN)
typedef struct {
	U32 ulCheckEventBit;
	U32 ulClearEventBit;
	U32 ulDebugCnt; //debug code
	U8 btIPStallFlag: 1;
	U8 Reserved: 7;
} ISREventStallHandle_t;

typedef enum ISRStallIPEnum {
	ISR_STALL_HANDLE_IP_BMU,
	ISR_STALL_HANDLER_IP_NUM,
} ISRStallIPEnum_t;
#endif /* (U17_EN) */

void ISRPMUSystemEvent(void);
void ISRParityErrorEvent(void);
void ISRHostEvent(void);
void ISRPCIEEvent(void);
void ISRCOP1Event(void);
void ISRBMUEvent(void);
void ISRDmacEvent(void);
void ISRPicUartEvent(void);
void ISRD2HEvent(void);
void ISRMREvent(void);
void ISRSmbusEvent(void);
void ISRCriticalAssertEvent(void);
void ISRClearOnlyEvent(void);
void ISRRTTHandle(void);

#if (PS5013_EN)
void ISRVoutFCFallingEvent(void);
void ISRVoutFIO12FallingEvent(void);
void ISRVoutFIO18FallingEvent(void);
void ISRVoutFCRisingEvent(void);
void ISRVoutFIO12RisingEvent(void);
void ISRVoutFIO18RisingEvent(void);
void ISRRtt0SystemEvent(void);
void ISRRtt10SystemEvent(void);
void ISRRtt11SystemEvent(void);
void ISRAxiMonitorDBUF2Event(void);
void ISRAxiMonitorDBUF3Event(void);
void ISRStartPLNEvent(void);
#endif /* (PS5013_EN) */

#if (PS5017_EN)
void ISRSystemEvent0(void);
void ISRSystemEvent1(void);
void ISRSystemEvent2(void);
void ISRCop0InterruptWrap0Event(void);
void ISRRSInterruptEvent(void);
#endif /* (PS5017_EN) */

#if (PS5021_EN)
void ISRMilliSecondTimerEvent(void);
void ISRRTTEvent(void);
#endif /* (PS5021_EN) */

void ISRSearchEvent(U32 ulFIQInterrupt);

#endif  // _ISR_HANDLER_H_
