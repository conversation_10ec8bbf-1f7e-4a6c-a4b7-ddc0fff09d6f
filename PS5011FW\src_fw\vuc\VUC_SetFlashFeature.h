#ifndef _VUC_SETFLASHFEATURE_H_
#define _VUC_SETFLASHFEATURE_H_

#include "nvme_api/nvme/shr_hal_nvme.h"

#define CLOCK_WRITE_SHIFT		(16)
#define CLOCK_READ_SHIFT		(12)
#define ODT_SHIFT				(8)
#define DRIVE_SHIFT				(4)
#define INTERFACE_SHIFT			(0)

#define SET_FEATURE_DIFFERENTIAL_SHIFT	(0)
#define SET_FEATURE_DIFFERENTIAL_MASK	BIT_MASK(3)
#define SET_FEATURE_ODT_SHIFT			(4)
#define SET_FEATURE_ODT_MASK			BIT_MASK(4)


#define GET_FLASH_FEATURE			(0x00)
#define RETRIEVE_FLASH_FEATURE		(0x01)
#define SET_FLASH_FEATURE_INTERFACE	(0x02)
#define SET_FLASH_DRIVING			(0x03)
#define SET_FLASH_ODT				(0x04)
#define SET_FLASH_FREQUENCY			(0x05)
#define VUC_SET_FLASH_FEATURE_VERIFY_EARLY_LATER_BICS4		(0x07)

#define TOSHIBA_GET_FEATURE_MIN_REQUIRE_BYTE	(4)

AOM_VUC void VUC_FlashFeatureConfigure(VUC_OPT_HCMD_PTR_t pCmd);


#endif /* _VUC_SETFLASHFEATURE_H_ */

