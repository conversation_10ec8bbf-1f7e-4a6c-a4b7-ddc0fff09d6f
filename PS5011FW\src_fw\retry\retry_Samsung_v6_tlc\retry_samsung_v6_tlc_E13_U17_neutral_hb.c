#include "retry/retry.h"
#include "retry/retry_api.h"
#include "retry/retry_hb.h"
#include "retry_samsung_v6_tlc_E13_U17_neutral_hb.h"
#include "hal/pic/uart/uart_api.h"
#include "retry/retry_version_api.h" // Retry Version
#include "table/initinfo_vt/initinfo_vt.h" // VT
#include "ftl/ftl_api.h" // FW Timer
#include "hal/fip/fpu.h"
#include "table/sys_block/sys_block_api.h" //RetryInitSystemBlockReadRetryTable
//V7 USE V6 RDT Setting
#if (((PS5013_EN) || (PS5017_EN)) && ((CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)))
/*
 * ----------------------------------------------------------------------------
 *   Definitions
 * ----------------------------------------------------------------------------
 */

#define RETRY_HYNIX_V6_SLC_RRT_BASE		(DBUF_RETRY_RR_TABLE)
#define RETRY_HYNIX_V6_TLC_RRT_BASE		(DBUF_RETRY_RR_TABLE + DEF_KB(2))

#define OTP_RR_CNT_SITE                 (8)
#define OTP_RR_REG_CNT_SITE             (16)

#define OTP_DEFAULT_RR_CNT              (50)
#define OTP_DEFAULT_RR_REG_CNT_SLC      (HBIT_RETRY_SLC_FEA_DATA_NUM)
#define OTP_DEFAULT_RR_REG_CNT_TLC      (HBIT_RETRY_TLC_FEA_DATA_NUM)

#define OTP_DEFAULT_SET_NUM             (8)
#define OTP_NORMAL_SEQ                  (0)
#define OTP_INVERSE_SEQ                 (1)
#define OTP_SEQ_CNT_PER_SET             (2)

/*
 * ----------------------------------------------------------------------------
 *   Enum
 * ----------------------------------------------------------------------------
 */

/*
 * ----------------------------------------------------------------------------
 *   Data Types
 * ----------------------------------------------------------------------------
 */

typedef struct {
	U8 ubSeq[OTP_DEFAULT_SET_NUM][OTP_SEQ_CNT_PER_SET][OTP_DEFAULT_RR_CNT * OTP_DEFAULT_RR_REG_CNT_TLC];
} OTPSeq_t;
/*
 * ----------------------------------------------------------------------------
 *   Macros
 * ----------------------------------------------------------------------------
 */

/*
 * ----------------------------------------------------------------------------
 *   Extern Global Variables
 * ----------------------------------------------------------------------------
 */
/*
 *	SAMSUNG V6
 *  Low Page              Middle Page           Upper Page
 * P0  P1  P2   P3     P0   P1   P2   P3     P0   P1   P2   P3
 * R1  R5              R2   R4   R6          R3   R7
 * 1   1    0    0      1    1    1    0      1    1    0    0
 */
const U8 gubRetryReadRetryTableParameterIdxToPageType[HBIT_RETRY_SELECT_NON_SLC_FEA_DATA_NUM] = {
	HB_LRU_EDGE_SLC, HB_LRU_MLC_MSB, HB_LRU_MLC_LSB, HB_LRU_MLC_MSB,
	HB_LRU_TLC_LSB, HB_LRU_TLC_CSB, HB_LRU_TLC_MSB, HB_LRU_TLC_CSB,
	HB_LRU_TLC_LSB, HB_LRU_TLC_CSB, HB_LRU_TLC_MSB
};

/*
 * ----------------------------------------------------------------------------
 *   Static Global Variables
 * ----------------------------------------------------------------------------
 */

/*
 * ----------------------------------------------------------------------------
 *   Private Prototypes
 * ----------------------------------------------------------------------------
 */

/*
 * ----------------------------------------------------------------------------
 *   Private Prototypes
 * ----------------------------------------------------------------------------
 */
INLINE void HBRetryInitRegisterRetryTable(void);
INLINE void HBRetryInitAdjustVthFPU(void);
INLINE U16 SAMSUNG_TLC_HBRetrySetFeatureFPU(U8 ubStep, U8 ubPageType, U8 ubSLCMode);
INLINE U16 SAMSUNG_TLC_HBRetryCheckFeatureFPU(U8 ubStep, U8 ubPageType, U8 ubSLCMode);
/*
 * ----------------------------------------------------------------------------
 *   Private
 * ----------------------------------------------------------------------------
 */

U8 HBRetryGetPageType(U64 uoiFSA, U8 ubALUSel)
{
	RetryPCARuleSet_t *pRuleSet = gpOtherInfo->pRuleSet;
	if (((ubALUSel >> 1) & BIT0) || (FTLGetCoord(((uoiFSA >> pRuleSet->pPage->ubShift[ubALUSel]) & pRuleSet->pPage->ulMask), IM_GETCOORD_WIN_SIZE) == 1)) { //Reip
		return 0;
	}
	else if (FTLGetCoord(((uoiFSA >> pRuleSet->pPage->ubShift[ubALUSel]) & pRuleSet->pPage->ulMask), IM_GETCOORD_WIN_SIZE) == 3) {
		return FTLGetCoord(((uoiFSA >> pRuleSet->pPage->ubShift[ubALUSel]) & pRuleSet->pPage->ulMask), IM_GETCOORD_X_VAL) + 3;
	}
	else {
		return FTLGetCoord(((uoiFSA >> pRuleSet->pPage->ubShift[ubALUSel]) & pRuleSet->pPage->ulMask), IM_GETCOORD_X_VAL);
	}
}

void HBRetryInitRegisterRetryTable(void)
{
	gpHBParameterArray[FPL_ADDR_TLC_RULE].pubRetryTable = (U8 *)(SAMSUNG_TLC_RRT_BASE);			// TLC
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubTotalRetryCnt = (gpIDPage->ubRetryGroupCount ? (gpIDPage->ubRetryGroupCount + 1) : HBIT_RETRY_SAMSUNG_TLC_512G_STEP_NUM);
	gpHBParameterArray[FPL_ADDR_SLC_RULE].pubRetryTable = (U8 *)(SAMSUNG_SLC_RRT_BASE);			// SLC
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubTotalRetryCnt = (gpIDPage->ubD1RetryGroupCount ? (gpIDPage->ubD1RetryGroupCount + 1) : HBIT_RETRY_SAMSUNG_SLC_512G_STEP_NUM);
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubCurrentStep = HB_READ_START_STEP;
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubLastSuccessIndex = 0;
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubRetryParameterSize = (gpIDPage->ubRetryParamCount ? gpIDPage->ubRetryParamCount : HBIT_RETRY_TLC_FEA_DATA_NUM);
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubParameterNumPerFPU = (gpIDPage->ubRetryParamCount ? gpIDPage->ubRetryParamCount : HBIT_RETRY_TLC_FEA_DATA_NUM); // Hynix only need 1 SetFeature CMD to mod FA
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubCurrentStep = HB_READ_START_STEP;
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubLastSuccessIndex = 0;
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubRetryParameterSize = (gpIDPage->ubD1RetryParamCount ? gpIDPage->ubD1RetryParamCount : HBIT_RETRY_SLC_FEA_DATA_NUM);
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubParameterNumPerFPU = (gpIDPage->ubD1RetryParamCount ? gpIDPage->ubD1RetryParamCount : HBIT_RETRY_SLC_FEA_DATA_NUM);
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubScratchTableIdx = INVALID_RETRY_TABLE_INDEX;
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubScratchTableIdx = INVALID_RETRY_TABLE_INDEX;

	M_UART(RDT_TEST_, " \nubD3RetryGroupCnt=%d, ubD3RetryParamCnt=%d",
		gpHBParameterArray[FPL_ADDR_TLC_RULE].ubTotalRetryCnt,
		gpHBParameterArray[FPL_ADDR_TLC_RULE].ubRetryParameterSize);
	M_UART(RDT_TEST_, " \nubD1RetryGroupCnt=%d, ubD1RetryParamCnt=%d",
		gpHBParameterArray[FPL_ADDR_SLC_RULE].ubTotalRetryCnt,
		gpHBParameterArray[FPL_ADDR_SLC_RULE].ubRetryParameterSize);

}

void HBRetryInitAdjustVthFPU(void)
{
	// Register set feature fpu
	gFpuEntryList.fpu_set_feature[0] = FPU_ADR_GEN;
	gFpuEntryList.fpu_set_feature[1] = FPU_CMD(0x78);
	gFpuEntryList.fpu_set_feature[2] = FPU_ADR(4);
	gFpuEntryList.fpu_set_feature[3] = FPU_DLY(0x10);
	gFpuEntryList.fpu_set_feature[4] = FPU_CMD(0xEF); // Set feature cmd
	gFpuEntryList.fpu_set_feature[27] = FPU_END;

	// Register read and compare feature data fpu
	gFpuEntryList.fpu_read_and_compare_feature_data[0] = FPU_CMD(0xEE); // Get feature cmd
	gFpuEntryList.fpu_read_and_compare_feature_data[1] = FPU_DLY(0x10);
}


U16 SAMSUNG_TLC_HBRetrySetFeatureFPU(U8 ubStep, U8 ubPageType, U8 ubSLCMode)
{
	FPL_HBIT_RETRY_PARAM_STRUCT_PTR pParam = &(gpHBParameterArray[ubSLCMode]);
	U8 ubValidParameterSize = pParam->ubParameterNumPerFPU;
	//U8 ubi;
	U16 uwFPUPtr;
	U16 *puwFPU;
	U8 ubFeatureData[HBIT_RETRY_TLC_FEA_DATA_NUM] = {0};
	U8 ubFPUIdx;

	if (ubStep < pParam->ubTotalRetryCnt) { //Prevent Access Array Over flow
		U16 uwTableOffset = ubStep * pParam->ubRetryParameterSize;
		// Copy set feature from HB retry table
		memcpy((void *)ubFeatureData, (void *)&pParam->pubRetryTable[uwTableOffset], ubValidParameterSize);
	}
#if (RETRY_HB_FEEDBACK_CACULATE_READ_LEVEL_TO_LRU)
	// Check if CurrentStep is "SB Pass Read Level", ubTotalRetryCnt = RR_Table + Scratch_Table
	else {
		U8 ubReadLevelTableIdx = ubStep - gpHBParameterArray[FPL_ADDR_TLC_RULE].ubTotalRetryCnt; // Get Node Idx
		RETRY_HB_LRU_FEEDBACK_READ_LEVEL_TABLE_STRUCT_PTR pFeedBackReadLevelTable = M_RETRY_GET_FEEDBACK_CACULATE_READ_LEVEL_TABLE(gHBParamMgr.ubSLCMode, gHBParamMgr.ubGlobalDie, gHBParamMgr.ubPageType);
		U8 ubValidIdx = 0;
		for (ubi = 0; ubi < ubValidParameterSize; ubi++) {
			if (ubPageType == M_RETRY_GET_PAGE_TYPE_BY_READ_RETRY_TABLE_PARAMETER_IDX(ubi)) {
				ubFeatureData[ubi] = pFeedBackReadLevelTable[ubReadLevelTableIdx].ubSBPassReadLevelTable[ubValidIdx]; // Same Page Type Read Level Can't Cross Two Round(89/8A).
				++ubValidIdx;
			}
		}
	}
#endif /* (RETRY_HB_FEEDBACK_CACULATE_READ_LEVEL_TO_LRU) */
	M_HB_DEBUG_UART("\n [HB] set feature:");
	// Get FPU PTR
	uwFPUPtr = FPU_PTR_OFFSET(fpu_set_feature);
	puwFPU = (U16 *)(IRAM_BASE + uwFPUPtr);

	// Set parameter cmd, already fill FPU[0] in init state
	//puwFPU[0] = FPU_CMD(0x36);
	puwFPU += 5;
	ubFPUIdx = 0;

	if (ubSLCMode) {
		puwFPU[ubFPUIdx++] = FPU_ADR_1B(0x8D);
		// Delay (tADL)
		puwFPU[ubFPUIdx++] = FPU_DLY(0x10);
		// Set feature data
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(ubFeatureData[0]);
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(0x00);
	}
	else if (ubPageType == HB_LRU_EDGE_SLC) {
		puwFPU[ubFPUIdx++] = FPU_ADR_1B(0x8D);
		// Delay (tADL)
		puwFPU[ubFPUIdx++] = FPU_DLY(0x10);
		// Set feature data
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(ubFeatureData[0]);

		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(0x00);
	}
	else if (ubPageType == HB_LRU_MLC_LSB) {
		puwFPU[ubFPUIdx++] = FPU_ADR_1B(0x8A);
		// Delay (tADL)
		puwFPU[ubFPUIdx++] = FPU_DLY(0x10);
		// Set feature data
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(ubFeatureData[2]);

		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(0x00);
	}
	else if (ubPageType == HB_LRU_MLC_MSB) {
		puwFPU[ubFPUIdx++] = FPU_ADR_1B(0x89);
		// Delay (tADL)
		puwFPU[ubFPUIdx++] = FPU_DLY(0x10);
		// Set feature data
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(ubFeatureData[1]);

		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(ubFeatureData[3]);
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(0x00);
	}
	else if (ubPageType == HB_LRU_TLC_LSB) {
		puwFPU[ubFPUIdx++] = FPU_ADR_1B(0x89);
		// Delay (tADL)
		puwFPU[ubFPUIdx++] = FPU_DLY(0x10);
		// Set feature data
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(ubFeatureData[4]);

		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(ubFeatureData[8]);
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(0x00);
	}
	else if (ubPageType == HB_LRU_TLC_CSB) {
		puwFPU[ubFPUIdx++] = FPU_ADR_1B(0x8A);
		// Delay (tADL)
		puwFPU[ubFPUIdx++] = FPU_DLY(0x10);
		// Set feature data
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(ubFeatureData[5]);

		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(ubFeatureData[7]);
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(ubFeatureData[9]);
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(0x00);
	}
	else if (ubPageType == HB_LRU_TLC_MSB) {
		puwFPU[ubFPUIdx++] = FPU_ADR_1B(0x89);
		// Delay (tADL)
		puwFPU[ubFPUIdx++] = FPU_DLY(0x10);
		// Set feature data
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(0x00);

		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(ubFeatureData[6]);
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(ubFeatureData[10]);
	}

	// FPU end
	puwFPU[ubFPUIdx++] = FPU_END;
	return uwFPUPtr;
}

U16 SAMSUNG_TLC_HBRetryCheckFeatureFPU(U8 ubStep, U8 ubPageType, U8 ubSLCMode)
{
	FPL_HBIT_RETRY_PARAM_STRUCT_PTR pParam =  &(gpHBParameterArray[ubSLCMode]);
	U8 ubValidParameterSize = pParam->ubParameterNumPerFPU;
	//U8 ubi;
	U16 uwFPUPtr;
	U16 *puwFPU;
	U8 ubFeatureData[HBIT_RETRY_TLC_FEA_DATA_NUM] = {0};
	U8 ubFPUIdx;

	if (ubStep < pParam->ubTotalRetryCnt) { //Prevent Access Array Over flow
		U16 uwTableOffset = ubStep * pParam->ubRetryParameterSize;
		// Copy set feature from HB retry table
		memcpy((void *)ubFeatureData, (void *)&pParam->pubRetryTable[uwTableOffset], ubValidParameterSize);
	}
#if (RETRY_HB_FEEDBACK_CACULATE_READ_LEVEL_TO_LRU)
	// Check if CurrentStep is "SB Pass Read Level", ubTotalRetryCnt = RR_Table + Scratch_Table
	else {
		U8 ubReadLevelTableIdx = ubStep - gpHBParameterArray[FPL_ADDR_TLC_RULE].ubTotalRetryCnt; // Get Node Idx
		RETRY_HB_LRU_FEEDBACK_READ_LEVEL_TABLE_STRUCT_PTR pFeedBackReadLevelTable = M_RETRY_GET_FEEDBACK_CACULATE_READ_LEVEL_TABLE(gHBParamMgr.ubSLCMode, gHBParamMgr.ubGlobalDie, gHBParamMgr.ubPageType);
		U8 ubValidIdx = 0;
		for (ubi = 0; ubi < ubValidParameterSize; ubi++) {
			if (ubPageType == M_RETRY_GET_PAGE_TYPE_BY_READ_RETRY_TABLE_PARAMETER_IDX(ubi)) {
				ubFeatureData[ubi] = pFeedBackReadLevelTable[ubReadLevelTableIdx].ubSBPassReadLevelTable[ubValidIdx]; // Same Page Type Read Level Can't Cross Two Round(89/8A).
				++ubValidIdx;
			}
		}
	}
#endif /* (RETRY_HB_FEEDBACK_CACULATE_READ_LEVEL_TO_LRU) */
	M_HB_DEBUG_UART("\n [HB] get feature:");

	// Get FPU PTR
	uwFPUPtr = FPU_PTR_OFFSET(fpu_read_and_compare_feature_data);
	puwFPU = (U16 *)(IRAM_BASE + uwFPUPtr);

	// Get parameter cmd, already fill FPU[0] in init state
	puwFPU += 2;

	ubFPUIdx = 0;

	if (ubSLCMode) {
		puwFPU[ubFPUIdx++] = FPU_ADR_1B(0x8D);
		// Delay
		puwFPU[ubFPUIdx++] = FPU_CMD_DQS(0X70);
		puwFPU[ubFPUIdx++] = FPU_DLY(0X40);
		puwFPU[ubFPUIdx++] = FPU_POL_MASK(0xE0);
		puwFPU[ubFPUIdx++] = FPU_CMD(0x00);
		puwFPU[ubFPUIdx++] = FPU_DLY(0x10);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(ubFeatureData[0]);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);
	}
	else if (ubPageType == HB_LRU_EDGE_SLC) {
		puwFPU[ubFPUIdx++] = FPU_ADR_1B(0x8D);
		// Delay
		puwFPU[ubFPUIdx++] = FPU_CMD_DQS(0X70);
		puwFPU[ubFPUIdx++] = FPU_DLY(0X40);
		puwFPU[ubFPUIdx++] = FPU_POL_MASK(0xE0);
		puwFPU[ubFPUIdx++] = FPU_CMD(0x00);
		puwFPU[ubFPUIdx++] = FPU_DLY(0x10);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(ubFeatureData[0]);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);
	}
	else if (ubPageType == HB_LRU_MLC_LSB) {
		puwFPU[ubFPUIdx++] = FPU_ADR_1B(0x8A);
		// Delay
		puwFPU[ubFPUIdx++] = FPU_CMD_DQS(0X70);
		puwFPU[ubFPUIdx++] = FPU_DLY(0X40);
		puwFPU[ubFPUIdx++] = FPU_POL_MASK(0xE0);
		puwFPU[ubFPUIdx++] = FPU_CMD(0x00);
		puwFPU[ubFPUIdx++] = FPU_DLY(0x10);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(ubFeatureData[2]);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);
	}
	else if (ubPageType == HB_LRU_MLC_MSB) {
		//BICS5 TLC: 12h or 89h for lower/middle page & 13h or 8Ah for upper page
		puwFPU[ubFPUIdx++] = FPU_ADR_1B(0x89);
		// FPU Delay
		puwFPU[ubFPUIdx++] = FPU_CMD_DQS(0X70);
		puwFPU[ubFPUIdx++] = FPU_DLY(0X40);
		puwFPU[ubFPUIdx++] = FPU_POL_MASK(0xE0);
		puwFPU[ubFPUIdx++] = FPU_CMD(0x00);
		puwFPU[ubFPUIdx++] = FPU_DLY(0x10);
		// Get feature data
		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(ubFeatureData[1]);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(ubFeatureData[3]);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);
	}
	else if (ubPageType == HB_LRU_TLC_LSB) {
		//BICS5 TLC: 12h or 89h for lower/middle page & 13h or 8Ah for upper page
		puwFPU[ubFPUIdx++] = FPU_ADR_1B(0x89);
		// Delay
		puwFPU[ubFPUIdx++] = FPU_CMD_DQS(0X70);
		puwFPU[ubFPUIdx++] = FPU_DLY(0X40);
		puwFPU[ubFPUIdx++] = FPU_POL_MASK(0xE0);
		puwFPU[ubFPUIdx++] = FPU_CMD(0x00);
		puwFPU[ubFPUIdx++] = FPU_DLY(0x10);
		// Get feature data
		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(ubFeatureData[4]);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(ubFeatureData[8]);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);
	}
	else if (ubPageType == HB_LRU_TLC_CSB) {
		//BICS5 TLC: 12h or 89h for lower/middle page & 13h or 8Ah for upper page
		puwFPU[ubFPUIdx++] = FPU_ADR_1B(0x8A);
		// Delay
		puwFPU[ubFPUIdx++] = FPU_CMD_DQS(0X70);
		puwFPU[ubFPUIdx++] = FPU_DLY(0X40);
		puwFPU[ubFPUIdx++] = FPU_POL_MASK(0xE0);
		puwFPU[ubFPUIdx++] = FPU_CMD(0x00);
		puwFPU[ubFPUIdx++] = FPU_DLY(0x10);
		// Get feature data
		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(ubFeatureData[5]);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(ubFeatureData[7]);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(ubFeatureData[9]);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);
	}
	else if (ubPageType == HB_LRU_TLC_MSB) {
		//BICS5 TLC: 12h or 89h for lower/middle page & 13h or 8Ah for upper page
		puwFPU[ubFPUIdx++] = FPU_ADR_1B(0x89);
		// Delay
		puwFPU[ubFPUIdx++] = FPU_CMD_DQS(0X70);
		puwFPU[ubFPUIdx++] = FPU_DLY(0X40);
		puwFPU[ubFPUIdx++] = FPU_POL_MASK(0xE0);
		puwFPU[ubFPUIdx++] = FPU_CMD(0x00);
		puwFPU[ubFPUIdx++] = FPU_DLY(0x10);
		// Get feature data
		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(ubFeatureData[6]);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(ubFeatureData[10]);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);
	}
	// FPU end
	puwFPU[ubFPUIdx++] = FPU_END;

	return uwFPUPtr;
}

/*
 * ------------------------------------
 * 		Over Wirte Weak Function
 * ------------------------------------
 */
void HBRetryInitParameter(void)
{
	switch (gpOtherInfo->ubMakerCode) {
	case ID_SAMSUNG:
		switch (gpOtherInfo->ubProcess) {
		case RETRY_SAMSUNG_FLASH_PROCESS_V6_512G:
			HBRetryInitRegisterRetryTable();
			HBRetryInitAdjustVthFPU();
			break;

		default:
			M_FW_ASSERT(ASSERT_RETRY_0x0870, FALSE);
			break;
		}

		break;

	default:
		M_FW_ASSERT(ASSERT_RETRY_0x0871, FALSE);
		break;
	}
}

void HBRetryPreconditionSetFeaturePass_Callback(U8 ubMTIdx)
{
	gHBParamMgr.ubSetFeatureDoneMTCnt++;

	if (gHBParamMgr.ubSetFeatureDoneMTCnt == gHBParamMgr.ubSetFeatureToDoMTCnt) { // All set feature MT done
		gHBParamMgr.ubGetFeatureDoneMTCnt = 0;
		if (NCS_V2_EN && gSystmAreaFWSettingFromInfoBlk.Init.Flag.btNCS2En) {
			gHBTask.ubState = RETRY_HB_STATE_SEARCH_PASS_STEP_READ;
		}
		else {
			gHBTask.ubState = RETRY_HB_STATE_CHECK_PRECONDITION; // Check set feature result
		}
	}
	else {
		gHBTask.ubState = RETRY_HB_STATE_PRECONDITION; // Execute remain set feature MT
	}
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_HB_RETRY;
}

void HBRetryPostconditionSetFeaturePass_Callback(U8 ubMTIdx)
{
	gHBParamMgr.ubSetFeatureDoneMTCnt++;

	if (gHBParamMgr.ubSetFeatureDoneMTCnt == gHBParamMgr.ubSetFeatureToDoMTCnt) {
		gHBParamMgr.ubGetFeatureDoneMTCnt = 0;
		if (NCS_V2_EN && gSystmAreaFWSettingFromInfoBlk.Init.Flag.btNCS2En) {
			gHBTask.ubState = RETRY_HB_STATE_POSTCONDITION_DONE_RESET_FLASH; // Check set feature result
		}
		else {
			gHBTask.ubState = RETRY_HB_STATE_CHECK_POSTCONDITION; // Check set feature result
		}
	}
	else {
		gHBTask.ubState = RETRY_HB_STATE_POSTCONDITION;
	}
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_HB_RETRY;
}

U16 HBRetrySelectResetCMDFPU(void)
{
	return FPU_PTR_OFFSET(fpu_entry_reset_fc);
}

U16 HBRetryPreconditionSetFeatureFPU(void)
{
	return SAMSUNG_TLC_HBRetrySetFeatureFPU(gHBParamMgr.pParam->ubCurrentStep, gHBParamMgr.ubPageType, gHBParamMgr.ubSLCMode);
}

U16 HBRetryPreconditionCheckFeatureFPU(void)
{
	return SAMSUNG_TLC_HBRetryCheckFeatureFPU(gHBParamMgr.pParam->ubCurrentStep, gHBParamMgr.ubPageType, gHBParamMgr.ubSLCMode);
}

U16 HBRetryPostconditionSetFeatureFPU(void)
{
	HB_LRU_TABLE_STRUCT_PTR pLRUTable = M_RETRY_GET_LRU_TABLE(gHBParamMgr.ubSLCMode, gHBParamMgr.ubGlobalDie, gHBParamMgr.ubPageType);
	return SAMSUNG_TLC_HBRetrySetFeatureFPU(pLRUTable[0].param.ubTableIdx, gHBParamMgr.ubPageType, gHBParamMgr.ubSLCMode);
}

U16 HBRetryPostconditionCheckFeatureFPU(void)
{
	HB_LRU_TABLE_STRUCT_PTR pLRUTable = M_RETRY_GET_LRU_TABLE(gHBParamMgr.ubSLCMode, gHBParamMgr.ubGlobalDie, gHBParamMgr.ubPageType);
	return SAMSUNG_TLC_HBRetryCheckFeatureFPU(pLRUTable[0].param.ubTableIdx, gHBParamMgr.ubPageType, gHBParamMgr.ubSLCMode);
}

#endif /* ((PS5013_EN) && (FLASH_HYNIXV6_TLC == FW_CATEGORY_FLASH) && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER)) */
