#ifndef _RETRY_HYNIX_V6_TLC_E13_NEUTRAL_HB_H_
#define _RETRY_HYNIX_V6_TLC_E13_NEUTRAL_HB_H_
/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */

#include "env.h"
//V7 USE V6 RDT Setting
#if (((PS5013_EN) || (PS5017_EN)) && ((FLASH_V6_TLC == FW_CATEGORY_FLASH)||(FLASH_V7_TLC == FW_CATEGORY_FLASH)||(FLASH_V8_TLC == FW_CATEGORY_FLASH)||(FLASH_V5_TLC == FW_CATEGORY_FLASH)) && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER))
/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */

#define HBIT_RETRY_V6_TLC_512G_STEP_NUM			(50 + 1)
#define HBIT_RETRY_V6_SLC_512G_STEP_NUM			(50 + 1)

/*
 * ---------------------------------------------------------------------------------------------------
 *  enum
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */

#endif /* ((PS5013_EN) && (FLASH_HYNIXV6_TLC == FW_CATEGORY_FLASH) && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER)) */
#endif /* _RETRY_HYNIX_V6_TLC_E13_NEUTRAL_HB_H_ */