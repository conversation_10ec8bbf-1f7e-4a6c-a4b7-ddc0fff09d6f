/****************************************************************************
 *
 *  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
 *  All rights reserved
 *
 *  The content of this document is confidential and shall be applied
 *  subject to the terms and conditions of the license agreement and
 *  other applicable laws. Any unauthorized access, use or disclosure
 *  of this document is strictly prohibited and may be punishable
 *  under laws.
 *
 *  raideccmap_for_N48R.c
 *
 *  (THIS FILE SUPPOSES BOTH "RAIDECC_BLOCK_PROTECTION_EN" AND "TWO_WORDLINE_PROTECTION_EN" ARE TRUE)
 *
 *
 *
 ****************************************************************************/

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "aom/aom_api.h"
#include "env.h"
#include "ftl/ftl_api.h"
#include "raideccmap.h"
#include "raideccmap_api.h"
#include "raideccmap_for_N48R.h"
#include "raideccmap_inline_api.h"
#include "typedef.h"

#if (IM_N48R)
/*
 * ---------------------------------------------------------------------------------------------------
 *   definitions
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *  enum
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *  data types
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *   macros
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *   extern global variables
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *   static global variables
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *   private prototypes
 * ---------------------------------------------------------------------------------------------------
 */
static AOM_RS_2 U16 RaidECCMapSLCFindLastSameTagPageByPage(U16 uwPage);

/*
 * ---------------------------------------------------------------------------------------------------
 *   private
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *   public
 * ---------------------------------------------------------------------------------------------------
 */
/*
 * Function : RaidECCMapGetParityTagIdxByCoord
 * ----------------------------------------------------------------------------
 *   (Support both WL Protect & Block Raid)
 *
 *   Calculate doc tag according to ParityMapMode and coord.
 *
 *   @param ParityMapMode: The type of parity mode
 *   @param uwX: X axis of micron
 *   @param uwY: Y axis of micron
 *
 *   @return: The result doc tag
 */
U16 RaidECCMapGetParityTagIdxByCoord(RaidECCMapParityMapEnum_t ParityMapMode, U16 uwX, U16 uwY, U8 ubPlane)
{
	U16 uwFWParityTagIdx = 0;
	U16 uwParityTagBase = 0;
	U16 uwShiftBase = 0;
	U16 uwShiftOffset = 0;
	U16 uwModNum = 0;

	switch (ParityMapMode) {
	case RAIDECCMAP_PARITYMAP_MODE_GR_SLC:
		if (M_RAIDECCMAP_IS_BLOCK_PROTECTION_MODE()) {
			uwModNum = RAIDECCMAP_SLC_DATA_BLOCK_PROTECT_BASIC_TAG_NUM;
			uwShiftOffset = uwX;
		}
		else {
			// SLC is 2WL Protect
			uwModNum = RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM;
			uwParityTagBase = (uwY & BIT0) * RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM;
			uwShiftBase   = (uwY / RAIDECCMAP_SLC_WORDLINE_PROTECT_NUM);  // 2WL
			uwShiftOffset = uwX;
		}
		break;
	case RAIDECCMAP_PARITYMAP_MODE_GR_XLC:
	case RAIDECCMAP_PARITYMAP_MODE_GC:
		if (M_RAIDECCMAP_IS_BLOCK_PROTECTION_MODE()) {
			uwModNum = RAIDECCMAP_XLC_DATA_BLOCK_PROTECT_BASIC_TAG_NUM;
			uwShiftOffset = uwX;
		}
		else {
			// QLC is 4WL Protect
			uwModNum = RAIDECCMAP_1WL_PROTECTION_IN_XLC_PARITY_TAG_IDX_NUM;
			uwParityTagBase = (uwY % RAIDECCMAP_XLC_WORDLINE_PROTECT_NUM) * RAIDECCMAP_1WL_PROTECTION_IN_XLC_PARITY_TAG_IDX_NUM;
			uwShiftBase = (uwY / RAIDECCMAP_XLC_WORDLINE_PROTECT_NUM);  // 4WL
			uwShiftOffset = uwX;
		}
		break;
	case RAIDECCMAP_PARITYMAP_MODE_TABLE:
	default:
		break;
	}

	uwFWParityTagIdx = uwParityTagBase + ((uwShiftBase + uwShiftOffset) % uwModNum);

	return uwFWParityTagIdx;
}

/*
 * Function : RaidECCMapGetParityTagIdxByProgramOrderTable
 * ----------------------------------------------------------------------------
 *   (Support both WL Protect & Block Raid)
 *
 *   Calculate doc tag according to ParityModeMode and plane idx.
 *
 *   @param ParityMapMode: The type of parity mode
 *   @param ulPlaneIdx: Unit plane idx
 *
 *   @return: The result doc tag
 */
U16 RaidECCMapGetParityTagIdxByProgramOrderTable(RaidECCMapParityMapEnum_t ParityMapMode, U32 ulPlaneIdx)
{
	U16 uwFWTagIdx = 0;
	U8 ubPlaneOffset = 0;
	if (RAIDECCMAP_PARITYMAP_MODE_TABLE == ParityMapMode) {
		uwFWTagIdx = (ulPlaneIdx / gubPlanesPerSuperPage) % 2;
	}
	else if (RAIDECCMAP_PARITYMAP_MODE_GR_SLC == ParityMapMode) {
		// WL Protect & Block Raid are same in this part
		U16 uwSuperPageIdx = ulPlaneIdx / gubPlanesPerSuperPage;
		U16 uwX = uwSuperPageIdx % RAIDECCMAP_SLC_PAGE_PER_WL;
		U16 uwY = uwSuperPageIdx / RAIDECCMAP_SLC_PAGE_PER_WL;
		uwFWTagIdx = RaidECCMapGetParityTagIdxByCoord(ParityMapMode, uwX, uwY, ubPlaneOffset);
	}
	else {
		// WL Protect & Block Raid are same in this part
		U16 uwSuperPageIdx = 0;
		U8 uwX, uwY = 0;
		FTLPlaneInx2Physical(ulPlaneIdx, &uwSuperPageIdx, &ubPlaneOffset);
		uwX = FTLGetCoord(uwSuperPageIdx, IM_GETCOORD_X_VAL);
		uwY = FTLGetCoord(uwSuperPageIdx, IM_GETCOORD_Y_VAL);
		uwFWTagIdx = RaidECCMapGetParityTagIdxByCoord(ParityMapMode, uwX, uwY, ubPlaneOffset);
	}
	return uwFWTagIdx;
}

/*
 * Function : RaidECCMapInvertCoord
 * ----------------------------------------------------------------------------
 *   (Support both WL Protect & Block Raid)
 *
 *   Calculate (physical) page index according to coord.  This function only
 *   accepts XLC coord, doesn't support SLC coord.
 *
 *   @param uwX: X axis of micron
 *   @param uwY: Y axis of micron
 *
 *   @return: The result (physical) page index.  0xFFFF denotes invalid result.
 */
U16 RaidECCMapInvertCoord(U8 ubX, U16 uwY)
{
	U8 ubCase = 0xFF;
	U16 uwPageIdx = 0xFFFF;

	if (0 == uwY) {
		if ((ubX % 4) < 2) {
			ubCase = 0;
			uwPageIdx = 0;
		}
	}
	else if (uwY < 88) {
		ubCase = 1;
		uwPageIdx = IM_N48_SECTION_1;
		uwY -= 1;
	}
	else if (uwY < 90) {
		if ((ubX % 4) < 2) {
			ubCase = 0;
			uwPageIdx = IM_N48_SECTION_2;
			uwY -= 88;
		}
	}
	else if (uwY < 177) {
		ubCase = 1;
		uwPageIdx = IM_N48_SECTION_3;
		uwY -= 90;
	}
	else if (177 == uwY) {
		if ((ubX % 4) < 2) {
			ubCase = 0;
			uwPageIdx = IM_N48_SECTION_4;
			uwY -= 177;
		}
	}

	switch (ubCase) {
	case 0:  // MLC
		uwPageIdx += (uwY * 8 + (ubX / 4 * 2) + (ubX % 4));
		break;
	case 1:  // QLC
		uwPageIdx += (uwY * 16 + ubX);
		break;
	default:
		break;
	}

	return uwPageIdx;
}

/*
 * Function : IsInValidCoordPage
 * ----------------------------------------------------------------------------
 *   (Support both WL Protect & Block Raid)
 *
 *   Check given coord is INVALID page or not.  INVALID means the coord is not
 *   indexed with page index. (see shared pages in spec)
 *
 *   @param uwX: X axis of micron
 *   @param uwY: Y axis of micron
 *
 *   @return: TRUE denotes INVALID.  FALSE denotes valid page.
 */
U8 IsInValidCoordPage(int slX, int slY)
{
	switch (slY) {
	case 0:
	case 88:
	case 89:
	case 177:
		if ((slX % 4) > 1) {
			return TRUE;
		}
		break;
	default:
		break;
	}
	return FALSE;
}

/*
 * Function : RaidECCMapSLCFindLastSameTagPageByPage
 * ----------------------------------------------------------------------------
 *   (Only support WL Protect)
 *
 *   Find the previous page whose tag is the same as current page (located by
 *   page index) for SLC data unit.
 *
 *   @param uwPage: The given current page
 *
 *   @return: The target page index.  0xFFFF if not found.
 */
U16 RaidECCMapSLCFindLastSameTagPageByPage(U16 uwPage)
{
	// WL0 and WL1 have no previous 2WL, there must be no previous page with same tag.
	if (uwPage < (RAIDECCMAP_SLC_WORDLINE_PROTECT_NUM * RAIDECCMAP_SLC_PAGE_PER_WL)) {
		return 0xFFFF;
	}

	U8 ubShiftOffset = uwPage % RAIDECCMAP_SLC_PAGE_PER_WL;  // 0 ~ 3 in N48R SLC
	U8 ubPageShift = (RAIDECCMAP_SLC_PAGE_PER_WL * RAIDECCMAP_SLC_WORDLINE_PROTECT_NUM) - 1;  // 7 in N48R SLC
	U8 ubRingBackShift = 0;

	/*
	 * Only catch the page with (tag) shift offset 3.
	 *
	 * Each tag with shift offset 3 in current page means this tag must have
	 * shift offset 0 in previous 2 WL.  So we need to concern the ring back
	 * length 4 to locate correct page with same tag in previous 2WL. (see
	 * RSMap doc)
	 */
	if (ubShiftOffset == (RAIDECCMAP_SLC_PAGE_PER_WL - 1)) {
		ubRingBackShift = RAIDECCMAP_SLC_PAGE_PER_WL;  // 4
	}

	return (uwPage - ubPageShift - ubRingBackShift);
}

/*
 * Function : RaidECCMapXLCFindLastSameTagPageByCoord
 * ----------------------------------------------------------------------------
 *   (Only support WL Portect)
 *
 *   Find the previous page whose tag is the same as current page (located by X
 *   and Y).
 *
 *   @param ubX: X axis of micron
 *   @param uwY: Y axis of micron
 *
 *   @return: The target page index.  0xFFFF if not found.
 */
U32 RaidECCMapXLCFindLastSameTagPageByCoord(U8 ubX, U16 uwY)
{
	U16 uwTargetX = ubX;
	U16 uwTargetY = uwY;

	//=============== Backward tracking ================
	do {
		if (uwTargetY < 4) { // WL 0 ~ 3 are pioneers
			return 0xFFFF;
		}
		// Handle x : x+1
		uwTargetX = (uwTargetX + 1) % 16;
		// Handle y: backtracking 4 WLs before current WL
		uwTargetY -= 4;
		// IsInValidCoordPage indicates whether (x,y) is an empty page (non-existent), if yes, do the calculation again: backtracking 2 WLs before current WL.
	} while (IsInValidCoordPage(uwTargetX, uwTargetY));

	return RaidECCMapInvertCoord(uwTargetX, uwTargetY);
}

/*
 * Function : RaidECCMapSwapFlowFindLastParityPlanePtr
 * ----------------------------------------------------------------------------
 *   (Only support WL Portect)
 *
 *   Find the "Plane Idx Offset" of previous parity in parity unit whose tag is
 *   the same as current page.
 *
 *   "Plnae Idx Offset" is related to "Base Plane Idx" of parity unit, real
 *   plane idx would be "Base Plane Idx" + "Plane Idx Offset".  Because of
 *   arrangement of parity unit, one super page generate one parity in parity
 *   unit, so we can use page idx to indicate "Plane Idx Offset".
 *
 *   @param ubRSEncodeMode: Encode type
 *   @param ubParityMapMode: Encode type
 *   (TODO: Should remove one of above two param)
 *   @param ubIsSlcMode: Is SLC or not
 *   @param uwPageIdx: Given page index
 *   @param ubPlaneBank: For multi plane protect, no use in N48R
 *
 *   @return: Plane idx of parity unit.
 */
U32 RaidECCMapSwapFlowFindLastParityPlanePtr(U8 ubRSEncodeMode, RaidECCMapParityMapEnum_t ubParityMapMode, U8 ubIsSlcMode, U16 uwPageIdx, U8 ubPlaneBank)
{
	U32 ulParityUnitPlanePtr = gulPlanesPerUnit; // Invalid

	if (ubIsSlcMode) {
		ulParityUnitPlanePtr = RaidECCMapSLCFindLastSameTagPageByPage(uwPageIdx);
	}
	else {
		U8 ubX = FTLGetCoord(uwPageIdx, IM_GETCOORD_X_VAL);
		U16 uwY = FTLGetCoord(uwPageIdx, IM_GETCOORD_Y_VAL);
		ulParityUnitPlanePtr = RaidECCMapXLCFindLastSameTagPageByCoord(ubX, uwY);
	}

	// Case 0xFFFF should be blocked by RaidECCMapCheckNeedSwapTag(), so no error handle here
	//M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (0xFFFF != ulParityUnitPlanePtr));

	return ulParityUnitPlanePtr;
}

/*
 * Function : RaidECCMapCheckNeedSwapTag
 * ----------------------------------------------------------------------------
 *   (Block Raid directly return FALSE)
 *
 *   Check is "SUPER PAGE" of given plane index a parity super page or invalid
 *   page.
 *
 *   This function is actually used to check "SUPER PAGE" of given plane index
 *   need swap or not, there are 2 modes,
 *   1. IsRemoveTag = TRUE, only check parity super page
 *   2. IsRemoveTag = FALSE, check both parity super page and invalid page.
 *
 *   IsRemoveTag = TRUE only used in GR swap tag flow.  Only XLC GR needs swap
 *   tag.  However, XLC GR always has open block, so swap flow doesn't need
 *   program temp parity to parity unit (open block cover this part), it can
 *   directly move to load parity part.  In this case, swap flow simply remove
 *   tag from RS IP and tag mgr.  So we name this flag as IsRemoveTag.
 *
 *   @param ubRSEncodeMode: RaidECC type to be checked (RaidEccMode_t only has
 *                          data info, no SLC or XLC info)
 *   @param ubSLCMode: ulPlaneIdx belongs to SLC unit or XLC unit
 *   @param ulPlaneIdx: Given plane idx to be checked
 *   @param IsRemoveTag: Flag for XLC GR to check is parity super page (see
 *                       above note)
 *
 *   @return: TRUE if it is not parity super page and invalid page
 */
U8 RaidECCMapCheckNeedSwapTag(U8 ubRSEncodeMode, U8 ubSLCMode, U32 ulPlaneIdx, U8 IsRemoveTag)
{
	// Block raid doesn't need swap flow, always return false.
	if (M_RAIDECCMAP_IS_BLOCK_PROTECTION_MODE()) {
		return FALSE;
	}

	U8 ubNeedSwap = TRUE;
	U16 uwPage = 0;
	U8 ubPlaneBank = 0;

	RAIDECCMAPPlaneIdx2Physical(ulPlaneIdx, &uwPage, &ubPlaneBank, ubSLCMode);

	if (IsRemoveTag) {
		ubNeedSwap = !(RaidECCMapCheckParityInline(ubRSEncodeMode, ubSLCMode, uwPage, ubPlaneBank));
	}
	else {
		if (ubSLCMode) {
			uwPage = RaidECCMapSLCFindLastSameTagPageByPage(uwPage);
		}
		else {
			U8 ubX = FTLGetCoord(uwPage, IM_GETCOORD_X_VAL);
			U16 uwY = FTLGetCoord(uwPage, IM_GETCOORD_Y_VAL);
			uwPage = RaidECCMapXLCFindLastSameTagPageByCoord(ubX, uwY);
		}

		if ((uwPage == 0xFFFF) || RaidECCMapCheckParityInline(ubRSEncodeMode, ubSLCMode, uwPage, (gubPlanesPerSuperPage - 1))) {
			ubNeedSwap = FALSE;
		}
	}

	return ubNeedSwap;
}

#endif  /* (IM_N48R) */
