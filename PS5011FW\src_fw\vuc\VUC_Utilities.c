#include "setup.h"
#include "hal/fip/fip_api.h"
#include "vuc/VUC_Utilities.h"


U8 PhysicalCE2LogicalCHCE(U8 ubCEDecodeEN, U8 ubPhysicalCE, U8 *pubCH, U8 *pubCE)
{
	if (ubCEDecodeEN) {
		*pubCH = ubPhysicalCE / MAX_PHYSICAL_CE_PER_CH;
		*pubCE = ubPhysicalCE % MAX_PHYSICAL_CE_PER_CH;
	}
	else {
		U8 ubLogicalCE = M_FIP_GET_LOGICAL_CE(ubPhysicalCE); // logical enable bit for physical CE
		if (ubLogicalCE > MAX_PHYSICAL_CE_NUM) { //means current CE not used
			return FALSE;
		}
		*pubCH = ubLogicalCE / MAX_PHYSICAL_CE_PER_CH;
		*pubCE = ubLogicalCE % MAX_PHYSICAL_CE_PER_CH;
	}
	return TRUE;
}
