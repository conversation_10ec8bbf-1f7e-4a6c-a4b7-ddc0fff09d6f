#ifndef _RETRY_HB_H_
#define _RETRY_HB_H_


/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "env.h"
#include "hal/fip/fip_api.h"
#include "aom/aom_api.h"
#include "common/fw_common.h"
#include "retry_setup.h"
#include "retry/retry_api.h"
#include "retry/retry.h"

/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */

//M2CMP01-721 BiCS FLASH Gen4 HDR 256Gb cTLC Read Retry with Set Feature Rev1.0.pdf, P11.
#define RETRY_HB_CHECK_TABLE_LEVEL_HIT_HB_TASK_TRIGGER_THRESHOLD	(100)

#define HB_MAX_SET_FEATURE_RETRY_CNT		(10)

#if RETRY_INITIAL_READ_EN
#define	HB_ADJUST_VTH_STEP			(0)
#else /*RETRY_INITIAL_READ_EN*/
#define	HB_ADJUST_VTH_STEP			(1)
#endif /*RETRY_INITIAL_READ_EN*/

#define HB_READ_START_STEP			(HB_ADJUST_VTH_STEP)

#define HB_MAX_4K_FRAME_NUM			(4)

#define HB_NORMAL_READ_STEP			(0xFE)
#define INVALID_MT_INDEX			(0xFF)
#define INVALID_RETRY_TABLE_INDEX 	(0xFF)
#define INVALID_RETRY_FEATURE_DATA	(0xFF)//Dylan add HB define


#if RETRY_HARDBIT_FOR_SDK_EN
#define HB_RETRY_MAX_TLC_STEP_NUM       (1 + 1)
#define HB_RETRY_MAX_SLC_STEP_NUM      	(1 + 1)
#else /* RETRY_HARDBIT_FOR_SDK_EN */
#if (HYNIX_FSP_EN || SANDISK_FSP_EN || YMTC_FSP_EN || MICRON_FSP_EN || INTEL_FSP_EN) //ems mst add--karl //Dylan Add V6 RDT (Add HB Retry)//zerio wts add//zerio n48r add
#define HB_RETRY_MAX_TLC_STEP_NUM       (HB_RETRY_XLC_RRT_STEP_NUM + RETRY_HB_FEEDBACK_NODE_NUM)
#define HB_RETRY_MAX_SLC_STEP_NUM      	(HB_RETRY_SLC_RRT_STEP_NUM + RETRY_HB_FEEDBACK_NODE_NUM)		//NowMax: Nicks B47 = 10 Steps
#else /* MICRON_FSP_EN */
#define HB_RETRY_MAX_TLC_STEP_NUM       (40 + 1 + HB_RETRY_TSB_SB_PASS_READ_OFFSET_STEP_MAX_NUM) //NowMax: BICS4HDR256G = 40 Steps
#define HB_RETRY_MAX_SLC_STEP_NUM      	(27 + 1)	//NowMax: BICS4HDR CM Wafer = 27 Steps
#endif /* MICRON_FSP_EN */

#define HB_RETRY_TABLE_BM_BYTE_NUM  	((HB_RETRY_MAX_TLC_STEP_NUM >> BITS_PER_BYTE_LOG) + 1)
#define HB_LRU_TLC_MAX_NUM				(RETRY_HB_LRU_TABLE_LIMIT_NODE_NUM_EN ? 6 : HB_RETRY_XLC_RRT_STEP_NUM)		//only BiCs4 512Gb need LRU with all step num
#define HB_LRU_SLC_MAX_NUM				(RETRY_HB_LRU_TABLE_LIMIT_NODE_NUM_EN ? 6 : HB_RETRY_SLC_RRT_STEP_NUM)//Dylan Add V6 RDT (Add HB Retry)

#define HB_RETRY_TSB_SB_PASS_READ_OFFSET_STEP_MAX_NUM	(RETRY_HB_FEEDBACK_CACULATE_READ_LEVEL_TO_LRU ? 8 : 0)
#define RETRY_LRU_FEEDBACK_READ_LEVEL_MAX_NUM	((4 + (RETRY_HB_FEEDBACK_NODE_CHECK_VALID_EN ? 1 : 0)))//Dylan build V6 RDT(Add HB Retry) use V6 FW Setting


#define RETRY_LRU_FEEDBACK_READ_LEVEL_VALID_IDX (RETRY_LRU_FEEDBACK_READ_LEVEL_MAX_NUM - 1)


#define HB_TRAPPING_SET_ENABLE_LLR_VAL		(0x5)
#define HB_TRAPPING_SET_DISABLE_LLR_VAL		(0xA)

#define TRAPPING_SET_THRESHOLD	(0x32)

#define RETRY_HB_LRU_SB_PASS_READ_LEVEL_INVALID_TABLE_IDX	(0xFF)
#define RETRY_HB_LRU_INVALID_LRU_TABLE_IDX (0xFF)

#endif /* RETRY_HARDBIT_FOR_SDK_EN */

/*
 * ---------------------------------------------------------------------------------------------------
 *  enum
 * ---------------------------------------------------------------------------------------------------
 */

enum {
	HB_LOWER = 0,
	HB_MIDDLE,
	HB_UPPER,
	HB_TOP,		//Reip Porting 3D-V7 QLC Add
	HB_LMU_NUM
};

/*
 *  HARDBIT RETRY STATE MACHINE
 *  ===========================
 */
typedef enum HBStateEnum {
	RETRY_HB_STATE_IDLE = 0,
	RETRY_HB_STATE_START,
	RETRY_HB_STATE_SELECT_RETRY_TABLE,
	RETRY_HB_STATE_RESET_FLASH,
	RETRY_HB_STATE_PRECONDITION,
	RETRY_HB_STATE_CHECK_PRECONDITION,
	RETRY_HB_STATE_PRECONDITION_WITHOUT_SETFEATURE,
	RETRY_HB_STATE_SEARCH_PASS_STEP_READ,
	RETRY_HB_STATE_REMAIN_READ,
	RETRY_HB_STATE_POSTCONDITION,
	RETRY_HB_STATE_CHECK_POSTCONDITION,
	RETRY_HB_STATE_POSTCONDITION_DONE_RESET_FLASH,
	RETRY_HB_STATE_READ_AGAIN,
	RETRY_HB_STATE_CLEAR_READ_OFFSET,
	RETRY_HB_STATE_DONE,

	RETRY_HB_STATE_WAIT_RESET_FLASH,
	RETRY_HB_STATE_WAIT_PRECONDITION,
	RETRY_HB_STATE_WAIT_CHECK_PRECONDITION,
	RETRY_HB_STATE_WAIT_SEARCH_PASS_STEP_READ,
	RETRY_HB_STATE_WAIT_REMAIN_READ,
	RETRY_HB_STATE_WAIT_POSTCONDITION,
	RETRY_HB_STATE_WAIT_CHECK_POSTCONDITION,
	RETRY_HB_STATE_WAIT_POSTCONDITION_DONE_RESET_FLASH,
	RETRY_HB_STATE_WAIT_READ_AGAIN,
	RETRY_HB_STATE_WAIT_CHECK_CLEAR_READ_OFFSET
} HBStateEnum_t;

typedef enum NCSHBInfoEnum {
	NCS_HB_FRAME_INFO = 0,
	NCS_HB_ERROR_BIT_INFO
} NCSHBInfoEnum_t;

typedef enum HBDMAModeEnum {
	HB_READ_MODE_INVALID = 0,
	HB_READ_MODE_SEARCH_PASS_STEP_READ,	// Use 1st error frame to find retry table
	HB_READ_MODE_REMAIN_READ			// After HB_DMA_SEARCH_PASS_STEP_READ, remain error frame use this mode to do DMA
} HBDMAModeEnum_t;

typedef enum RetryHBDataBitLength {
	RETRY_HB_U8_BIT_LENGTH = 8,
	RETRY_HB_U16_BIT_LENGTH = 16,
	RETRY_HB_U32_BIT_LENGTH = 32,
	RETRY_HB_U64_BIT_LENGTH = 64,
} RetryHBDataBitLength_t;

#if RETRY_HARDBIT_FOR_SDK_EN
#else /* RETRY_HARDBIT_FOR_SDK_EN */
enum {
	HB_LRU_STATE = 0,
	HB_SCRATCH_STATE = 1,
	HB_LRU_DONE_STATE,
	RETRY_HB_REMAIN_STATE,
	RETRY_HB_REMAIN_DONE_STATE
};

typedef enum HBLRUEnum {
#if (MICRON_FSP_EN)///Dylan for V6 RDT porting,by pass HB retry.
	HB_LRU_MICRON_SLC_WL,
	HB_LRU_MICRON_MLC_WL_LOWER,
	HB_LRU_MICRON_MLC_WL_UPPER,
	HB_LRU_MICRON_TLC_WL_LOWER,
	HB_LRU_MICRON_TLC_WL_UPPER,
	HB_LRU_MICRON_TLC_WL_EXTRA,
#if ((FLASH_TYPE_MICRON_3D_QLC == CONFIG_FLASH_TYPE))//Dylan for V6 RDT porting,by pass HB retry.
	HB_LRU_MICRON_QLC_WL_LOWER,
	HB_LRU_MICRON_QLC_WL_UPPER,
	HB_LRU_MICRON_QLC_WL_EXTRA,
	HB_LRU_MICRON_QLC_WL_TOP,
#endif /* (FLASH_TYPE_MICRON_3D_QLC == CONFIG_FLASH_TYPE) */
#elif(FLASH_TYPE_SAMSUNG_3D_TLC == CONFIG_FLASH_TYPE)
	HB_LRU_EDGE_SLC,
	HB_LRU_MLC_LSB,
	HB_LRU_MLC_MSB,
	HB_LRU_TLC_LSB,
	HB_LRU_TLC_CSB,
	HB_LRU_TLC_MSB,
#else /*MICRON_FSP_EN*/
	HB_LRU_LOWER = 0,
	HB_LRU_MIDDLE,
	HB_LRU_UPPER,
	HB_LRU_TOP,	//Reip Porting 3D-V7 QLC Add
#endif /*MICRON_FSP_EN*/
	HB_LRU_TABLE_PAGE_TYPE_NUM,
} HBLRUEnum_t;

#endif /* RETRY_HARDBIT_FOR_SDK_EN */

/*
 * ---------------------------------------------------------------------------------------------------
 *  data types
 * ---------------------------------------------------------------------------------------------------
 */
typedef struct HBTask_t									HB_TASK_STRUCT, *HB_TASK_STRUCT_PTR;
typedef struct HBRetryParameterMgr_t					HB_PARAM_MGR_STRUCT, *HB_PARAM_MGR_STRUCT_PTR;
typedef struct fpl_hbit_retry_param_struct              FPL_HBIT_RETRY_PARAM_STRUCT, *FPL_HBIT_RETRY_PARAM_STRUCT_PTR;

#if RETRY_HARDBIT_FOR_SDK_EN
#else /* RETRY_HARDBIT_FOR_SDK_EN */
typedef struct HBLRUTask_t										HB_LRU_TASK_STRUCT, *HB_LRU_TASK_STRUCT_PTR;
typedef union HBLRUTable_t										HB_LRU_TABLE_STRUCT, *HB_LRU_TABLE_STRUCT_PTR;
typedef struct RetryHBReadRetryTableTLCStatus_t					RETRY_HB_READ_RETRY_TABLE_TLC_STATUS_STRUCT, *RETRY_HB_READ_RETRY_TABLE_TLC_STATUS_STRUCT_PTR;
typedef struct RetryHBLRUTopInfo_t								RETRY_HB_LRU_TOP_INFO_STRUCT, *RETRY_HB_LRU_TOP_INFO_STRUCT_PTR;
typedef struct RetryHBLRUDieInfo_t								RETRY_HB_LRU_DIE_INFO_STRUCT, *RETRY_HB_LRU_DIE_INFO_STRUCT_PTR;
typedef struct RetryHBLRUDynamicReadRetryTableInfo_t				RETRY_HB_LRU_DYNAMIC_READ_RETRY_TABLE_INFO_STRUCT, *RETRY_HB_LRU_DYNAMIC_READ_RETRY_TABLE_INFO_STRUCT_PTR;
typedef struct RetryHBLRUFeedbackReadLevelTable_t				RETRY_HB_LRU_FEEDBACK_READ_LEVEL_TABLE_STRUCT, *RETRY_HB_LRU_FEEDBACK_READ_LEVEL_TABLE_STRUCT_PTR;
#endif /* RETRY_HARDBIT_FOR_SDK_EN */

struct fpl_hbit_retry_param_struct {
	U8      ubLastSuccessIndex;		// last success retry table index
	U8      ubCurrentStep;			// current retry step
	U8      ubLastStep;			// current retry step
	U8      ubTotalRetryCnt;		// total retry parameter count
	U8      ubRetryParameterSize;	// retry parameter size, for Bics3 tlc: 7, slc: 1
	U8      ubParameterNumPerFPU;	// retry parameter number to be set per MT
	U8      *pubRetryTable;			// retry table base address
	U8      ubScratchTableIdx;	// scratch table Idx in HB retry table
};

struct HBRetryParameterMgr_t {
	U8 ubSetFeatureToDoMTCnt;    // SLC 1, TLC 2 QLC 1
	U8 ubSetFeatureDoneMTCnt;
	U8 ubSetFeatureRetryCnt;
	U8 ubGetFeatureDoneMTCnt;
	U8 ubSLCMode;
	U8 ubChannel;
	U8 ubBank;
	U8 ubDie;
	U8 ubPlane;
	U8 ubGlobalDie;
	U8 bt1stRetryReadCMD;
	U8 ubPageType;

	U8 ubLRUOnly;
	U8 ubWithoutPrefix;
	U8 ubResetDie;
	U8 ubACRR;
	U8 ubCurACRR;
	U8	ubMinSWIdx[HBIT_RETRY_MICRON_MIN_SW_NUM];
	U16 uwMinSWValue[HBIT_RETRY_MICRON_MIN_SW_NUM];
	FPL_HBIT_RETRY_PARAM_STRUCT_PTR			pParam;
};
#if ((CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC))   // Dylan build Hynix V6 RDT (Add HB Retry) use FW Setting //Reip Porting 3D-V7 QLC Add
typedef struct {
	U8 ubSLCReadRetryTable[RETRY_MAX_GLOBAL_DIE_NUM][HB_RETRY_SLC_RRT_STEP_NUM][HBIT_RETRY_SLC_FEA_DATA_NUM];
	U8 ubReserve0[415];
	U8 ubSLCReadRetryCount;
} SystemBlkSLCReadRetry_t;
//TYPE_SIZE_CHECK(SystemBlkSLCReadRetry_t, 0x800);  //Gary add for build hynix v6

typedef struct {
	U8 ubReadRetryTable[RETRY_MAX_GLOBAL_DIE_NUM][HB_RETRY_XLC_RRT_STEP_NUM][HBIT_RETRY_SELECT_NON_SLC_FEA_DATA_NUM];
	U8 ubReserve0[862];
	U8 ubReadRetryCount;
	U8 ubValidMark;
} SystemBlkReadRetry_t;
//TYPE_SIZE_CHECK(SystemBlkReadRetry_t, 0x3000);  //Gary add for build hynix v6
#else /* (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC) */

typedef struct {
	U8 ubSLCReadRetryTable[HBIT_RETRY_SLC_FEA_DATA_NUM * HB_RETRY_MAX_SLC_STEP_NUM]; //24 Byte
	U8 ubSLCReadRetryCount;
	U8 ubReserve0[2023];//byte 25-2047
} SystemBlkSLCReadRetry_t;

typedef struct {
	U8 ubReadRetryTable[HBIT_RETRY_SELECT_NON_SLC_FEA_DATA_NUM * HB_RETRY_MAX_TLC_STEP_NUM]; //777 Byte
	U8 ubReadRetryCount;
	U8 ubScratchTableIdx;
	U8 ubWithoutPrefix;
	U8 ubLRUOnly;
	U8 ubReserve0[3314];//byte 781-4094
	U8 ubValidMark;
} SystemBlkReadRetry_t;
#endif
struct HBTask_t {
	FlhMT_t MTTemplate; // Set Retry PCA & Frame number & Retry read setting in this template
	U32 ulBufAddr[FRAMES_PER_PAGE];
	U32 ulSprOffset[FRAMES_PER_PAGE];
	U8 ubHBRetryFrameMap;
	U8 ubHBFailFrameMap;
	U8 ubState;
	U8 btCreateByRaidECC	: 1;
	///added for RS
#if (MICRON_FSP_EN)
	U8 btReserved1: 7;
#endif /* (MICRON_FSP_EN) */
#if RETRY_HARDBIT_FOR_SDK_EN
	///added for RS
	U8 btQos : 1;
	U8 RSV : 7;
#else /* RETRY_HARDBIT_FOR_SDK_EN */
#if (MICRON_FSP_EN)
	U8 btTrappingSetFlowEn : 1;
	U8 btQos : 1;
	U8 btTLCCloseFlowDone: 1;
	U8 btQLC1stPassFlowDone: 1;
	U8 btReserved2 : 3;
	U8 btSetReadRetry: 1;
	U8 btSetReadOffset: 1;
	U8 btPartialBlkMediaScan: 1;
	U8 Reserved1: 6;
	U8 ubWordLineBypassRecord;
#else /* (MICRON_FSP_EN) */
	U8 btTrappingSetFlowEn : 1;
	U8 btQos : 1;
	U8 Reserved	: 5;
#endif /* (MICRON_FSP_EN) */
	U8 ubBlkRefresh;
#endif /* RETRY_HARDBIT_FOR_SDK_EN */
#if RDT_MODE_EN
	U16 uwMinEccbitInHBTask; //In read retry process, record the min ecc bit of each retry step
#endif
};

#if RETRY_HARDBIT_FOR_SDK_EN
#else /* RETRY_HARDBIT_FOR_SDK_EN */
union HBLRUTable_t {
	U8 ubAll;
	struct  {
		U8 ubTableIdx;
	} param;
};

typedef struct RetryHBLRUFeedbackReadLevelTable_t {
	U8 ubSBPassReadLevelTable[RETRY_LRU_FEEDBACK_READ_LEVEL_MAX_NUM];
} RetryHBLRUFeedbackReadLevelTable_t;

typedef struct RetryHBLRUDynamicReadRetryTableInfo_t {
	U8 ubHBTaskTriggerCount;		//Count_RR
	U8 ubSBTriggerdFlag;
	/*
	* <--Read Retry Table--> | <--Scratch Table--> | <--SB Pass Read Level-->
	* | 0, 1, 2..................39 | 40, 41.................42 | 43, 44, ..................... 50
	*/
	U8 ubReadRetryTableLevelHitBMP[HB_RETRY_TABLE_BM_BYTE_NUM];	//RR_Table check if HitCount==0
	U8 ubReadRetryTableEnableBMP[HB_RETRY_TABLE_BM_BYTE_NUM];	//RR_Table Enable BMP,  Step RetryTable -> BMP
} RetryHBLRUDynamicReadRetryTableInfo_t;

struct RetryHBReadRetryTableTLCStatus_t {
#if (RETRY_HB_DYNAMIC_READ_RETRY_TABLE)
	RetryHBLRUDynamicReadRetryTableInfo_t RetryHBLRUDynamicReadRetryTableInfo;
#endif /* RETRY_HB_DYNAMIC_READ_RETRY_TABLE */
#if (RETRY_HB_FEEDBACK_CACULATE_READ_LEVEL_TO_LRU)
	//For SB Pass Read Level, Every PageType Need there own SBPassReadLevelTable
	RetryHBLRUFeedbackReadLevelTable_t RetryHBLRUFeedbackReadLevelTable[HB_RETRY_TSB_SB_PASS_READ_OFFSET_STEP_MAX_NUM];
#endif /* (RETRY_HB_FEEDBACK_CACULATE_READ_LEVEL_TO_LRU) */
};

#if (RETRY_HB_LRU_IN_DBUF)
struct RetryHBLRUTopInfo_t { // 0: TLC, 1: SLC
	U16 uwLRUNodeMaxNum[FPL_ADDR_RULE_NUM];
	U8 ubLRUFeedbackReadLevelNodeMaxNum[FPL_ADDR_RULE_NUM];
	U8 ubLRUTableDieNum;
	U8 ubReserve[1];
};

typedef struct RetryHBLRUDieInfo_t {
	U8 ubLastLRUUpdateGlobalPlane[RETRY_GLOBAL_PLANE_ARRAY_SIZE];
} RetryHBLRUDieInfo_t;
#endif /* (RETRY_HB_LRU_IN_DBUF) */

struct HBLRUTask_t {
	union {
		U32 ulAll;
		struct {
			U8 ubHBLRUIdx;
			U8 ubHBScratchTableIdx;
			U8 ubHBRemainReadIdx;		// use to remain read select rr table
			U8 ubHBRemainReadDoneCnt; 	// use to record remain read cnt
		} Idx;
	} Index;
	U8 ubHBChooseIdxState;
	U8 ubHBLastMTPass;
	//Debug
	U16 uwPassDMARemainCnt;
	U16 uwFailDMARemainCnt;
};
#endif /* RETRY_HARDBIT_FOR_SDK_EN */

/*
 * ---------------------------------------------------------------------------------------------------
 *	 macros
 * ---------------------------------------------------------------------------------------------------
 */
#if HB_DEBUG_UART_EN
#define M_HB_DEBUG_UART(VAR, ...)			M_UART(ERROR_HARDBIT_, VAR, ##__VA_ARGS__);
#else /* HB_DEBUG_UART_EN */
#define M_HB_DEBUG_UART(VAR, ...)
#endif /* HB_DEBUG_UART_EN */

#define M_RETRY_LRU_MOVE_NODE_IDX(LRU_TABLE,DST_IDX,SRC_IDX)	((LRU_TABLE)[(DST_IDX)].ubAll = (LRU_TABLE)[(SRC_IDX)].ubAll)

/*
 * LRU By Die means every die have one set of LRU Table, and there are SLC + Non SLC Page Type LRU Table in one set.
 * If RAM Size not enough for all Die, you can change to use Channel or Other,
 * That means all macro of LRU have DIE input will change to Other Scale.
 */

#if (RETRY_HB_LRU_IN_DBUF) // Only Support LRU By DIE
#define M_RETRY_GET_LRU_TABLE(SLC,DIE,PAGETYPE)	((HB_LRU_TABLE_STRUCT_PTR)((SLC) ?\
													(&(gpRetryHBLRUSLCTable[((DIE) * gpRetryHBLRUTopInfo->uwLRUNodeMaxNum[FPL_ADDR_SLC_RULE])])) :\
													(&(gpRetryHBLRUNonSLCTable[(((DIE) * HB_LRU_TABLE_PAGE_TYPE_NUM + (PAGETYPE)) * gpRetryHBLRUTopInfo->uwLRUNodeMaxNum[FPL_ADDR_TLC_RULE])]))))
#define M_RETRY_GET_LAST_UPDATE_LRU_TABLE_GLOBAL_PLANE_IDX(DIE,PAGETYPE)		(gpRetryHBLRUDieInfo[(DIE)].ubLastLRUUpdateGlobalPlane[(PAGETYPE)])
#define M_RETRY_SET_LAST_UPDATE_LRU_TABLE_GLOBAL_PLANE_IDX(DIE,PAGETYPE,VALUE)	(gpRetryHBLRUDieInfo[(DIE)].ubLastLRUUpdateGlobalPlane[(PAGETYPE)] = (VALUE))
#define M_RETRY_GET_LAST_UPDATE_LRU_TABLE(DIE)	(gpRetryHBLRUDieInfo[(DIE)].ubLastLRUUpdateGlobalPlane)
#define M_RETRY_GET_LRU_TABLE_MAX_NODE_NUMBER(SLC)	((SLC) ? gpRetryHBLRUTopInfo->uwLRUNodeMaxNum[FPL_ADDR_SLC_RULE] : gpRetryHBLRUTopInfo->uwLRUNodeMaxNum[FPL_ADDR_TLC_RULE])
#define M_RETRY_GET_LRU_BY_DIE_TABLE_NUMBER()	(gpRetryHBLRUTopInfo->ubLRUTableDieNum)
/*
 *   Add Feedback Read Level
 */
//Dylan build V6 RDT (Add HB Retry)
#define M_RETRY_GET_FEEDBACK_CACULATE_READ_LEVEL_TABLE(SLC,DIE,PAGETYPE)	(SLC && RETRY_HB_FEEDBACK_NODE_SLC_FEEDBACK_EN ? (&(gpRetryHBSLCLRUFeedbackReadLevelTable[((DIE) * (gpRetryHBLRUTopInfo->ubLRUFeedbackReadLevelNodeMaxNum[FPL_ADDR_SLC_RULE]))])) : (&(gpRetryHBLRUFeedbackReadLevelTable[((((DIE) * HB_LRU_TABLE_PAGE_TYPE_NUM) + (PAGETYPE)) * (gpRetryHBLRUTopInfo->ubLRUFeedbackReadLevelNodeMaxNum[FPL_ADDR_TLC_RULE]))])))
#define M_RETRY_GET_FEEDBACK_READ_LEVEL_MAX_NODE_NUM(SLC)	(gpRetryHBLRUTopInfo->ubLRUFeedbackReadLevelNodeMaxNum[SLC])
/*
 *	 Dynamic Read Retry Table
 */
#define M_RETRY_GET_DYNAMIC_READ_RETRY_TABLE_INFO(DIE,PAGETYPE)	(&(gpRetryHBLRUDynamicReadRetryTableInfo[(((DIE) * HB_LRU_TABLE_PAGE_TYPE_NUM) + (PAGETYPE))]))
#else /* (RETRY_HB_LRU_IN_DBUF) */
#define M_RETRY_GET_LRU_TABLE_MAX_NODE_NUMBER(SLC)	((SLC) ? HB_LRU_SLC_MAX_NUM : HB_LRU_TLC_MAX_NUM)
/*
 *   Add Feedback Read Level (Not By Die in BTCM)
 */
// //Dylan build V6 RDT (Add HB Retry)
#define M_RETRY_GET_FEEDBACK_CACULATE_READ_LEVEL_TABLE(SLC,DIE,PAGETYPE)	(gRetryHBReadRetryTableTLCStatistics[(PAGETYPE)].RetryHBLRUFeedbackReadLevelTable)
#define M_RETRY_GET_FEEDBACK_READ_LEVEL_MAX_NODE_NUM(SLC)	(RETRY_HB_FEEDBACK_NODE_NUM)
/*
 *	 Dynamic Read Retry Table (Not By Die in BTCM)
 */
#define M_RETRY_GET_DYNAMIC_READ_RETRY_TABLE_INFO(DIE,PAGETYPE)	(&(gRetryHBReadRetryTableTLCStatistics[(PAGETYPE)].RetryHBLRUDynamicReadRetryTableInfo))
#if (RETRY_HB_LRU_BY_DIE)
#define M_RETRY_GET_LRU_TABLE(SLC,DIE,PAGETYPE)	((SLC) ? (gHBSLCLRUTable[(DIE)]) : (gHBLRUTable[(DIE)][(PAGETYPE)]))
#define M_RETRY_GET_LAST_UPDATE_LRU_TABLE_GLOBAL_PLANE_IDX(DIE,PAGETYPE)		(gpRetry->ubLastLRUUpdateGlobalPlane[(DIE)][(PAGETYPE)])
#define M_RETRY_SET_LAST_UPDATE_LRU_TABLE_GLOBAL_PLANE_IDX(DIE,PAGETYPE,VALUE)	(gpRetry->ubLastLRUUpdateGlobalPlane[(DIE)][(PAGETYPE)] = (VALUE))
#define M_RETRY_GET_LAST_UPDATE_LRU_TABLE(DIE)	(gpRetry->ubLastLRUUpdateGlobalPlane[(DIE)])
#define M_RETRY_GET_LRU_BY_DIE_TABLE_NUMBER()	(RETRY_HB_LRU_MAX_DIE_NUM)
#else /* (RETRY_HB_LRU_BY_DIE) */
#define M_RETRY_GET_LRU_TABLE(SLC,DIE,PAGETYPE) ((SLC) ? (gHBSLCLRUTable) : (gHBLRUTable[(PAGETYPE)]))
#define M_RETRY_GET_LAST_UPDATE_LRU_TABLE_GLOBAL_PLANE_IDX(DIE,PAGETYPE)		(gpRetry->ubLastLRUUpdateGlobalPlane[(PAGETYPE)])
#define M_RETRY_SET_LAST_UPDATE_LRU_TABLE_GLOBAL_PLANE_IDX(DIE,PAGETYPE,VALUE)	(gpRetry->ubLastLRUUpdateGlobalPlane[(PAGETYPE)] = (VALUE))
#define M_RETRY_GET_LAST_UPDATE_LRU_TABLE(DIE)	(gpRetry->ubLastLRUUpdateGlobalPlane)
#define M_RETRY_GET_LRU_BY_DIE_TABLE_NUMBER()	(1)
#endif /* (RETRY_HB_LRU_BY_DIE) */
#endif /* (RETRY_HB_LRU_IN_DBUF) */

#define M_RETRY_GET_PAGE_TYPE_BY_READ_RETRY_TABLE_PARAMETER_IDX(IDX) (gubRetryReadRetryTableParameterIdxToPageType[(IDX)])

#if (BURNER_MODE_EN)
#define M_RETRY_HB_COPY_READ_RETRY_TABLE(TARGET,SOURCE,SIZE)	(memcpy((void *)(TARGET), (void *)(SOURCE), (SIZE)))
#else /* (BURNER_MODE_EN) */
#define M_RETRY_HB_COPY_READ_RETRY_TABLE(TARGET,SOURCE,SIZE)
#endif /* (BURNER_MODE_EN) */

/*
 * ---------------------------------------------------------------------------------------------------
 *   extern global variables
 * ---------------------------------------------------------------------------------------------------
 */
extern HB_TASK_STRUCT gHBTask;
extern const U8 gubRetryReadRetryTableParameterIdxToPageType[HBIT_RETRY_SELECT_NON_SLC_FEA_DATA_NUM];
extern FPL_HBIT_RETRY_PARAM_STRUCT_PTR gpHBParameterArray;
extern HB_PARAM_MGR_STRUCT gHBParamMgr;
extern U8 gubHbitRetryData[HB_RETRY_MAX_TLC_STEP_NUM * HBIT_RETRY_SELECT_NON_SLC_FEA_DATA_NUM];
extern U8 gubSlcHbitRetryData[HB_RETRY_MAX_SLC_STEP_NUM * HBIT_RETRY_SLC_FEA_DATA_NUM];
extern U8 gubRetryTableUsed_BM[HB_RETRY_TABLE_BM_BYTE_NUM];
extern U8 gubRetryTablePageUsed_BM[HB_RETRY_TABLE_BM_BYTE_NUM];

#if RETRY_HARDBIT_FOR_SDK_EN
#else /* RETRY_HARDBIT_FOR_SDK_EN */
extern HB_LRU_TASK_STRUCT gHBLRUTask;
#if (RETRY_HB_LRU_IN_DBUF)
extern RETRY_HB_LRU_TOP_INFO_STRUCT_PTR gpRetryHBLRUTopInfo;
extern RETRY_HB_LRU_DIE_INFO_STRUCT_PTR gpRetryHBLRUDieInfo;
extern RETRY_HB_LRU_DYNAMIC_READ_RETRY_TABLE_INFO_STRUCT_PTR gpRetryHBLRUDynamicReadRetryTableInfo;
extern RETRY_HB_LRU_FEEDBACK_READ_LEVEL_TABLE_STRUCT_PTR gpRetryHBLRUFeedbackReadLevelTable;
extern RETRY_HB_LRU_FEEDBACK_READ_LEVEL_TABLE_STRUCT_PTR gpRetryHBSLCLRUFeedbackReadLevelTable;

extern HB_LRU_TABLE_STRUCT_PTR 			gpRetryHBLRUSLCTable;
extern HB_LRU_TABLE_STRUCT_PTR			gpRetryHBLRUNonSLCTable;
#else /* (RETRY_HB_LRU_IN_DBUF) */
#if (RETRY_HB_FEEDBACK_CACULATE_READ_LEVEL_TO_LRU || RETRY_HB_DYNAMIC_READ_RETRY_TABLE)
extern RETRY_HB_READ_RETRY_TABLE_TLC_STATUS_STRUCT gRetryHBReadRetryTableTLCStatistics[HB_LRU_TABLE_PAGE_TYPE_NUM];
#endif /* (RETRY_HB_FEEDBACK_CACULATE_READ_LEVEL_TO_LRU || RETRY_HB_DYNAMIC_READ_RETRY_TABLE) */
#if (RETRY_HB_LRU_BY_DIE)
extern HB_LRU_TABLE_STRUCT gHBLRUTable[RETRY_HB_LRU_MAX_DIE_NUM][HB_LRU_TABLE_PAGE_TYPE_NUM][HB_LRU_TLC_MAX_NUM];
extern HB_LRU_TABLE_STRUCT gHBSLCLRUTable[RETRY_HB_LRU_MAX_DIE_NUM][HB_LRU_SLC_MAX_NUM];
#else /* (RETRY_HB_LRU_BY_DIE) */
extern HB_LRU_TABLE_STRUCT gHBLRUTable[HB_LRU_TABLE_PAGE_TYPE_NUM][HB_LRU_TLC_MAX_NUM];
extern HB_LRU_TABLE_STRUCT gHBSLCLRUTable[HB_LRU_SLC_MAX_NUM];
#endif /* (RETRY_HB_LRU_BY_DIE) */
#endif /* (RETRY_HB_LRU_IN_DBUF) */
#endif /* RETRY_HARDBIT_FOR_SDK_EN */


/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */
AOM_RETRY void HBRetryInit(U32 hb_res_base, U32 hb_res_size);
AOM_RETRY_LOAD_TABLE NO_INLINE void HBRetryInitTable(void);
AOM_RETRY void HBRetryMain(void);
AOM_RETRY U8 RetryHBCheckSPORContinueCondition(void);

#if RETRY_HARDBIT_FOR_SDK_EN
#else /* RETRY_HARDBIT_FOR_SDK_EN */
//HB LRU
AOM_RETRY_2 void HBRetryLRUSLCInit (void);
AOM_RETRY_2 void HBRetryLRUInit (void);
AOM_RETRY void HBRetryTableUsedBMPInit (void);
AOM_RETRY void HBRetrySelectFirstLRU (void);

AOM_RETRY void RetryHBInitReadRetryTableTLCStatus(void);
AOM_RETRY void RetryHBCheckHBTaskTriggerCnt(U8 ubGlobalDie, U8 ubPageType);
AOM_FLH_ERR_SB_CALLBACK void RetryHBLRUAddSBPassReadLevelNode (U8 ubGlobalDie, U8 ubPageType, U8 *pubSBPassReadLevel);

//for Strong callback usage
AOM_RETRY void HBRetryLRUIdxInit(void);
AOM_RETRY void HBRetryLRUAddNode (U8 ubIndex);
AOM_RETRY void HBRetryTableUsedPassedBMPInit(void);
AOM_RETRY U8 HBRetryGetRetryTableIdx(void);

//-------------Weak function-------------
AOM_RETRY void HBRetryUpdateErrorRecoveryStatistics(U8 ubMode, U8 ubSearchPassDMAFrameNum);
AOM_RETRY U8 HBRetryGetPageType(U64 uoiFSA, U8 ubALUSel);
AOM_RETRY void HBRetrySelectParameterTable(void);
AOM_RETRY void HBRetryInitParameter(void);
AOM_RETRY U8 HBRetryLRUSelectIdx(void);
AOM_RETRY void HBRetryResetFlashPass_Callback(U8 ubMTIdx);
AOM_RETRY void HBRetryReadDMAFail_Callback(U8 ubMTIdx);
AOM_RETRY void HBRetryReadDMAPass_Callback(U8 ubMTIdx);
AOM_RETRY void HBRetryPostconditionSetFeaturePass_Callback(U8 ubMTIdx);
AOM_RETRY void HBRetryClearReadOffsetPassCallback(U8 ubMTIdx);
AOM_RETRY void HBRetryPostconditionGetFeatureCheckPass_Callback(U8 ubMTIdx);
AOM_RETRY void HBRetryPostconditionGetFeatureCheckFail_Callback(U8 ubMTIdx);
//FPU (KIC Template)
AOM_RETRY U16 HBRetrySelectResetCMDFPU(void);
AOM_RETRY U16 HBRetrySelectReadCMDFPU(void);
AOM_RETRY U16 HBRetryPreconditionSetFeatureFPU(void);
AOM_RETRY U16 HBRetryGetFeatureFPU(void);
AOM_RETRY U16 HBRetryPreconditionGetFeatureFPU(void);
AOM_RETRY U16 HBRetryPostconditionGetFeatureFPU(void);
AOM_RETRY U16 HBRetryPreconditionCheckFeatureFPU(void);
AOM_RETRY U16 HBRetryPostconditionSetFeatureFPU(void);
AOM_RETRY U16 HBRetryPostconditionCheckFeatureFPU(void);
AOM_RETRY U16 HBRetryClearReadOffsetFPU(void);
//--------------------------------------

#endif /* RETRY_HARDBIT_FOR_SDK_EN */
#endif /* _RETRY_HB_H_*/
