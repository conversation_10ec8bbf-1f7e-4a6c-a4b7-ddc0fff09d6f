#ifndef _VUC_LISTBADBLOCK_H_
#define _VUC_LISTBADBLOCK_H_
#include "aom/aom_api.h"
#include "setup.h"

#define VUC_LIST_BAD_MAX_DIE_NUMBER		(4)
#define	VUC_INVALID_DATA_VALUE		(0xFFFFFFFF)
#define VUC_LIST_BAD_SUMMARY_PER_CE         0x00
#define VUC_LIST_BAD_SUMMARY_PER_PLANE      0x01
#define VUC_LIST_BAD_BLOCK_INFO             0x02

typedef struct {
	U16 uwEarlyBadCount;
	U16 uwReadFailCount;
	U16 uwProgramFailCount;
	U16 uwEraseFailCount;
} ListBadBlock_t;

typedef union {
	U32 ulAll;
	struct {
		U32 BadBlk		: 14;
		U32 BadBlkDie	: 2;
		U32 BadBlkInCE	: 8;
		U32 PreLaterCnt	: 2;
		U32 Reserve		: 1;
		U32 D1			: 1;
		U32 BadType		: 2;
		U32 BlkMode		: 2;
	} A;
} BadBlkInfo_t;

typedef enum {
	BAD_TYPE_EARLY = 0,
	BAD_TYPE_READ,
	BAD_TYPE_WRITE,
	BAD_TYPE_ERASE,
	BAD_TYPE_NUM
} ListBadType_t;

typedef struct {
	U16 uwBadBlockPerPlaneBank[BAD_TYPE_NUM][MAX_CHANNEL][MAX_CE_PER_CHANNEL][VUC_LIST_BAD_MAX_DIE_NUMBER][PLANE_NUMBER];	//2* 4*4*4*4*4 = 2048
	U16 uwBadBlockPerCE[BAD_TYPE_NUM][MAX_CHANNEL][MAX_CE_PER_CHANNEL];													//2* 4*4*4 = 128
} BadBlockCnt_t;

AOM_VUC void VUC_ListBadBlock(VUC_OPT_HCMD_PTR_t pCmd);


#endif /* _VUC_LISTBADBLOCK_H_ */
