/*
 * rdt_main_Online.c
 *
 *  Created on: 2024.12
 *      Author: user
 */

#include <string.h>
#include <stdlib.h>
#include <setjmp.h>
#include "setup.h"
#include "hal/sys/api/clk/clk.h"
#include "hal/sys/api/itc/itc_api.h"
#include "rdt_api.h"
#include "burner/Burner.h"
#include "host_handler/hostevt_errhdl_api.h"
#include "host_handler/host_management_api.h"
/* ------ RDT_RECORD_SCAN_WINDOW_LOG ------ */
#include "vuc/VUC_ScanFlashWindowParameter.h"
#include "vuc/VUC_ReadScanFlashWindow.h"
/* ------------ RDT_RUN_ONLINE ------------ */
#include "vuc/VUC_EraseAll.h"
#include "vuc/VUC_Utilities.h"
#include "host/VUC_handler.h"
#include "hal/nvme/nvme.h"
#include "hal/spi/spi_reg_5017.h"
#include "hal/spi/spi_api.h"
#include "hal/pic/uart/shr_hal_pic_uartxfer.h"
#include "hal/fip/fip_reg.h"
#include "hal/fip/fip_api.h"
#include "hal/fip/fip.h"

#if (RDT_MODE_EN)
#if (RDT_RUN_ONLINE)
jmp_buf env_start;
RDT_API_STRUCT gRdtApiStruct;
FPL_API_STRUCT gC1Cop0FplApiStruct;
U32 gFlashTestStartTime;
U8 gubCheckUSBLinkForRDT = 0;
U8 ERL_Bypass_Record_HB_RETRY_INFO[16][16][4]; //[Global CE][DIE][Plane];MAX_Globa CE = 16, MAX DIE = 16,Plane=4
UART_STATUS *gpLastStatus = (UART_STATUS *)(PAD_PAGE_BASE + PAD_PAGE_REAL_SIZE);
RFL_LOG_STRUCT *gpResultFinal = (RFL_LOG_STRUCT *)(PAD_PAGE_BASE + PAD_PAGE_REAL_SIZE);

extern UART_PORT_INFO gsUartPortInfo;
extern VendorGetStatusTemplate_t gLastCmd;
extern void ISPJumpResetIP(void);
extern void Nand2DefaultModeFlow(void);
extern void EFuseCutSoftwaremode(U8 ubEfuAddr, U8 ubEfuData);

#define VUC_ONLINE_FW_INIT              0x01
#define VUC_ONLINE_GET_FLASH_ID 		0x02
#define VUC_ONLINE_CHECK_EARLY_BAD 	    0x03
#define VUC_ONLINE_GET_PROGRESS	        0x04
#define VUC_ONLINE_START_RDT_FLOW		0x05
#define VUC_ONLINE_GET_UNIQUE_ID		0x06
#define VUC_ONLINE_GET_PH_UNIQUE_ID     0x07


static void OnlineGetUniqueID(VUC_OPT_HCMD_PTR_t pCmd)
{
	U8 ubPhysicalCE, ubChannel, ubCE, ubDie;
	ubDie = ((pCmd->vuc_sqcmd.raw_data.dw[14] >> 8) & 0xFF);
	ubPhysicalCE = ((pCmd->vuc_sqcmd.raw_data.dw[14]) & 0xFF);
	memset((void *)gulVUCBufAddr, 0x00, 512);
	if (TRUE == PhysicalCE2LogicalCHCE(0, ubPhysicalCE, &ubChannel, &ubCE)) {
		FIPScanFlashUniqueID(ubChannel, ubCE, ubDie, (U8 *)gulVUCBufAddr);
	}
}

static void OnlineGetFlashID(VUC_OPT_HCMD_PTR_t pCmd)
{
	U8 ubChannel, ubCE, ubLogicalCE, ubi;
	U8 ubCE0FlashID[READ_ID_DATA_NUM];
	U8 *ubFlashID = (U8 *)gulVUCBufAddr;
	U16 uwBufferOffset = 0;
	//RDT_API_STRUCT_PTR rdt = &gRdtApiStruct;
	U32 ulTmpAddrBase = gulVUCBufAddr + 1024;
	FLH_ENV_STRUCT_t *pFlhEnvTmp = (FLH_ENV_STRUCT_t *)(ulTmpAddrBase);
	FLH_ENV_STRUCT_MP_t *pFlhEnvMPTmp = (FLH_ENV_STRUCT_MP_t *)(ulTmpAddrBase + sizeof(FLH_ENV_STRUCT_t));

	//Set Ctl/Flh back to boot code state
	ClockSwitchModeAndACTiming(CLOCK_FLH_IF_RESET);
	Nand2DefaultModeFlow();
	FlaIOTypeSetting(CH_ALL, FIP_IO_SINGLE_END);
	FlaInterfaceSetting(CH_ALL, LEGACY_INTERFACE);

#if 0
	//Trim 6 Cycle Addr
	EFuseCutSoftwaremode(24, 5);
#endif

	memcpy(pFlhEnvTmp, (void *)&gFlhEnv, sizeof(FLH_ENV_STRUCT_t));
	memcpy(pFlhEnvMPTmp, (void *)&gFlhEnvMP, sizeof(FLH_ENV_STRUCT_MP_t));
	memset((void *)&gFlhEnv, 0x00, sizeof(FLH_ENV_STRUCT_t));
	memset((void *)&gFlhEnvMP, 0x00, sizeof(FLH_ENV_STRUCT_MP_t));

	gFlhEnv.ubChannelExistNum = FlaChannelDetect();
	FipMapLogicalCEToPhysicalCE();

	M_UART(RDT_TEST_, "\ngFlhEnv.ubChannelExistNum=%d", gFlhEnv.ubChannelExistNum);
	M_UART(RDT_TEST_, "\ngulVUCBufAddr=%x", gulVUCBufAddr);
	memset((U8 *)gulVUCBufAddr, 0x00, 512);
	memcpy((U8 *)(gulVUCBufAddr + 504), gsUartPortInfo.ubData, 8);
	gFlhEnvMP.ulFailedCEBMP = 0;
	if (0 == gFlhEnv.ubChannelExistNum) {
		gFlhEnv.ulFlashDefaultType.BitMap.btNoSupport = TRUE;
		goto END_GET_FLASH_ID;
	}

	for (ubChannel = 0; ubChannel < MAX_CHANNEL; ubChannel++) {
		for (ubCE = 0; ubCE < MAX_CE_PER_CHANNEL; ubCE++) {
			M_FIP_CALCULATE_LOGICAL_CE(ubChannel, ubCE, ubLogicalCE);
			if ((gFlhEnv.ulCEBMP & BIT(ubLogicalCE)) != BIT(ubLogicalCE)) {
				continue;
			}

			uwBufferOffset = (ubCE * MAX_CHANNEL + ubChannel) << 3;

			FlaReadID(ubChannel, ubCE, 0x00, &ubFlashID[uwBufferOffset]);
			if ((CH0 == ubChannel) && (CE0 == ubCE)) {
				memcpy(ubCE0FlashID, &ubFlashID[uwBufferOffset], READ_ID_DATA_NUM);
			}

			M_UART(RDT_TEST_, "\nChannel%d - CE%d - ID", ubChannel, ubCE);

			for (ubi = 0; ubi < READ_ID_DATA_NUM; ubi++) {
				M_UART(RDT_TEST_, " %b", ubFlashID[uwBufferOffset + ubi]);
				if (ubFlashID[uwBufferOffset + ubi] != ubCE0FlashID[ubi]) {
					gFlhEnvMP.ulFailedCEBMP |= BIT(gubLogicalCEMapPhysicalCE[ubChannel][ubCE]);
					gFlhEnv.ulFlashDefaultType.BitMap.btNoSupport = TRUE;
				}
			}
		}
	}

END_GET_FLASH_ID:
	if (gFlhEnv.ulFlashDefaultType.BitMap.btNoSupport) {
		return;
	}
#if 0
	memcpy((void *)&gFlhEnv, pFlhEnvTmp, sizeof(FLH_ENV_STRUCT_t));
	memcpy((void *)&gFlhEnvMP, pFlhEnvMPTmp, sizeof(FLH_ENV_STRUCT_MP_t));
	ParseNandConfigTable((PAGE_1_SDLL_NAND_Config_t *)((LPM3_LOADER) ? LPM_PADPAGE_BACKUP_ADDR : PAD_PAGE_BASE), NAND_CONFIG_ONLY_PAD_MODE);
	FlaSetFlashMode(FIP_SET_FLASH_MODE_INIT);
	ParseNandConfigTable((PAGE_1_SDLL_NAND_Config_t *)((LPM3_LOADER) ? LPM_PADPAGE_BACKUP_ADDR : PAD_PAGE_BASE), NAND_CONFIG_INIT_MODE);
#endif
}

static void OnlineGetPHUniqueID(VUC_OPT_HCMD_PTR_t pCmd, RDT_API_STRUCT_PTR rdt)
{
	U8 ubi, ubj, ubClockDivBackUp;
	Product_History_Log *pPH = (Product_History_Log *)(SYSAREA_SCAN_RECORD_BUFFER);
	memset((void *)gulVUCBufAddr, 0x00, sizeof(Product_History_Log));
	if (0 == rdt->sys_blk_record.ph_block_cnt) {
		rdt->scan_header_mode = 1;
		rdt->ulScanSpecificLogLCA = SPARE_LCA_PH_MP_LOG;//Find PH Block(if exist) -- andry 20250603
		rdt_api_scan_log(rdt);
		rdt->ulScanSpecificLogLCA = 0;
	}
	if (0 != rdt->sys_blk_record.ph_block_cnt) {
#if (RDT_REFERENCE_WINDOW_RESULT)
		//Write Read Flash by low speed, while DLL Offset not valid or not scan window yet. -- 20250328 andry
		ubClockDivBackUp = rdt_api_cop0_clock_div_backup();
		if (FALSE == rdt->btReferenceWindowResult) {
			rdt_api_cop0_clock_div_set(3);
		}
#endif
		for (ubi = 0; ubi < rdt->sys_blk_record.ph_block_cnt; ubi++) {
			if (INVALID_PCA_VALUE != rdt->sys_blk_record.ph_block_addr[ubi]) {

				vuc_read_log(rdt, rdt->sys_blk_record.ph_block_addr[ubi], 1, SYSAREA_SCAN_RECORD_BUFFER);
				if (pPH->header.ubHeaderTag[0] == 'P' && pPH->header.ubHeaderTag[1] == 'H') {
					for (ubj = 0; ubj < 42; ubj++) {
						if (pPH->header.ubPHUniqueID[ubj] < 0x20) {
							break;
						}
					}
					if (42 != ubj) {
						continue;
					}
				}
				else {
					continue;
				}

				memcpy((void *)gulVUCBufAddr, (void *)(pPH), sizeof(Product_History_Log));
				break;
			}
		}
#if (RDT_REFERENCE_WINDOW_RESULT)
		//Recover clock div. -- 20250328 andry
		rdt_api_cop0_clock_div_set(ubClockDivBackUp);
#endif
	}
}

static void OnlineStartRDTFlow(RDT_API_STRUCT_PTR rdt)
{
	U8 LED_ERROR_SET = 0;
	if (rdt->rdt_test_flow_index < RDT_TEST_FLOW_MAX) {
		U8 error_happened_force_to_save_log = 0;
		gLastCmd.ubSubFeature = rdt->rdt_test_flow[rdt->rdt_test_flow_index];
		gpLastStatus->B.ubPRLState = rdt->rdt_test_flow[rdt->rdt_test_flow_index];
		gpLastStatus->B.ubPRLProgress = 0;
		gpLastStatus->B.ubTansferState = 0;
		gpLastStatus->B.ulRxDataCount = 0;
		gpLastStatus->B.ubFlowIndex = rdt->rdt_test_flow_index;

		switch (rdt->rdt_test_flow[rdt->rdt_test_flow_index]) {

		case PRL_STATE_ERASE_ALL_TEST: {
				gubLEDState = RDT_NAND_TEST;
				M_UART(RDT_TEST_, "\n RDT Check time: flow [SCAN_EARLY_BAD] Start");
				memset(&gEraseAll, 0x00, sizeof(gEraseAll));
				gEraseAll.uwEraseMode = VUC_ERASE_ALL_UPDATE_DBT | VUC_ERASE_ALL_ERASE_DBT | VUC_ERASE_ALL_ERASE_ALL | VUC_ERASE_ALL_SKIP_ERASE_SYS_BLK;
				gEraseAll.State = ERASEALL_INIT;
				gEraseAll.ubEraseAllDoing = FALSE;
				while (ERASEALL_FINISH != gEraseAll.State) {
					VUC_EraseAllBLK(SLC_A2_MODE, MODE_D1);
				}
				M_UART(RDT_TEST_, "\n RDT Check time: flow [SCAN_EARLY_BAD] finish");
			}
			break;
		case PRL_STATE_TEST_PREPARATION:
			if (!rdt_scan_system_area()) {
				gpLastStatus->B.ubCmdError = rdt->rdt_err = ERR_READ_INFO_BLK_FAIL;
				gubLEDState = RDT_TEST_ERROR;
				rdt->rdt_test_flow[rdt->rdt_test_flow_index + 1] = PRL_STATE_NO_ACTION;
				break;
			}
			if (!rdt_api_test_preparation(rdt)) {
				gubLEDState = RDT_TEST_ERROR;
				rdt->rdt_test_flow[rdt->rdt_test_flow_index + 1] = PRL_STATE_NO_ACTION;
				gpLastStatus->B.ubCmdError = rdt->rdt_err;
			}
			break;
		case PRL_STATE_SCAN_PAGE_REGISTER_TEST:
#if RDT_RECORD_SCAN_WINDOW_LOG
			gubLEDState = RDT_NAND_TEST;
			M_UART(RDT_TEST_, "\n RDT Check time: flow [SCAN_PAGE_REGISTER_TEST] Start");
			rdt_api_rml_log_add(rdt, RML_IMARK_PAGE_REG_TEST_START);
			rdt_api_write_log_to_flash(rdt, RDT_LOG_RDT_TESTMARK);
			rdt_api_ScanPageRegister(rdt);
			rdt_api_rml_log_add(rdt, RML_IMARK_PAGE_REG_TEST_END);
			rdt_api_write_log_to_flash(rdt, RDT_LOG_RDT_TESTMARK);
			M_UART(RDT_TEST_, "\n RDT Check time: flow [SCAN_PAGE_REGISTER_TEST] Finish");
#endif
			break;

		case PRL_STATE_START_TEST:
			rdt_api_rml_log_add(rdt, RML_IMARK_RDT_TEST_START);
			rdt_api_write_log_to_flash(rdt, RDT_LOG_RDT_TESTMARK);
			rdt_api_prl_program(rdt, rdt->rdt_test_flow_index);//change to store flow index
			if (rdt->btRDTOnlineRecordResultFinal) {
				gpResultFinal->btFlashDCCFail = gFlhEnv.ulFlashDefaultType.BitMap.btDCCFail;
				gpResultFinal->btFlashZQCLFail = gFlhEnv.ulFlashDefaultType.BitMap.btZQCLFail;
			}
			break;

		case PRL_STATE_NO_ACTION:
			//stop test flow
			if (!LED_ERROR_SET) {
				gubLEDState = RDT_TEST_FINISH;
			}
			//gubLEDState = RDT_TEST_FINISH;

#if !ENABLE_RDT_NO_LOG
#if ENABLE_PH
			rdt_api_choose_PH_blk(rdt);
#endif
			rdt_api_exchange_dbt_to_bbm(rdt);
#endif

			rdt->rdt_test_flow_index = RDT_TEST_FLOW_FINISH_INDEX;//stop test
			rdt_api_prl_program(rdt, rdt->rdt_test_flow_index);//change to store flow index

			rdt_api_rml_log_add(rdt, RML_IMARK_RDT_TEST_END);
			rdt_api_write_log_to_flash(rdt, RDT_LOG_RDT_TESTMARK);
#if (!RDT_RUN_ONLINE)
			//rdt_api_erase_by_dbt(rdt);
			rdt_api_parse_all_log(rdt);
#endif
			rdt_parse_Dbt(rdt);
			M_UART(RDT_TEST_, "\n RDT Check time: flow [RDT] Finish");
			break;

		case PRL_STATE_SRAM_TEST:
			gubLEDState = RDT_SRAM_TEST;
			M_UART(RDT_TEST_, "\n RDT Check time: flow [SRAM_TEST] Start");
			if (!rdt_api_atcm_sram_test(rdt) ||  !rdt_api_sram_wr_test(rdt) ) {
				error_happened_force_to_save_log = 1;
				gpLastStatus->B.ubCmdError = rdt->rdt_err;
			}
			M_UART(RDT_TEST_, "\n current core power %b \n", rdt_api_read_voltage_setting() >> COREPOWER_SEL_SHIFT);
			M_UART(RDT_TEST_, "\n RDT Check time: flow [SRAM_TEST] finish ");
			break;

		case PRL_STATE_TLC_FLASH_TEST:
			gubLEDState = RDT_NAND_TEST;
			M_UART(RDT_TEST_, "\n RDT Check time: flow [TLC_FLASH_TEST] Start");
			rdt_init_eot_setting(rdt, rdt->param.tlc_ecc_bit_threshold);
			if ( !rdt_api_general_flash_test(rdt, 0) ) {
				LED_ERROR_SET = 1;
				error_happened_force_to_save_log = 1;
				gpLastStatus->B.ubCmdError = rdt->rdt_err;
			}
			M_UART(RDT_TEST_, "\n RDT Check time: flow [TLC_FLASH_TEST] finish1");

			break;

		case PRL_STATE_SLC_FLASH_TEST:
			gubLEDState = RDT_NAND_TEST;
			M_UART(RDT_TEST_, "\n RDT Check time: flow [SLC_FLASH_TEST] Start ");
			rdt_init_eot_setting(rdt, rdt->param.slc_ecc_bit_threshold);
			if ( !rdt_api_general_flash_test(rdt, 1) ) {
				LED_ERROR_SET = 1;
				error_happened_force_to_save_log = 1;
				gpLastStatus->B.ubCmdError = rdt->rdt_err;
			}
			M_UART(RDT_TEST_, "\n RDT Check time: flow [SLC_FLASH_TEST] finish ");
			break;

		case PRL_STATE_SCAN_WINDOW_1_TEST:
#if RDT_RECORD_SCAN_WINDOW_LOG
			gubLEDState = RDT_NAND_TEST;
			rdt_api_rml_log_add(rdt, RML_IMARK_WIN_TEST_SLC_START);
			rdt_api_write_log_to_flash(rdt, RDT_LOG_RDT_TESTMARK);
			rdt_api_ScanWindowFlow(rdt, TRUE);
			rdt_api_rml_log_add(rdt, RML_IMARK_WIN_TEST_SLC_END);
			rdt_api_write_log_to_flash(rdt, RDT_LOG_RDT_TESTMARK);
#endif
			break;

		case PRL_STATE_SCAN_WINDOW_2_TEST:
#if RDT_RECORD_SCAN_WINDOW_LOG
			gubLEDState = RDT_NAND_TEST;
			rdt_api_rml_log_add(rdt, RML_IMARK_WIN_TEST_TLC_START);
			rdt_api_write_log_to_flash(rdt, RDT_LOG_RDT_TESTMARK);
			rdt_api_ScanWindowFlow(rdt, TRUE);
			rdt_api_rml_log_add(rdt, RML_IMARK_WIN_TEST_TLC_END);
			rdt_api_write_log_to_flash(rdt, RDT_LOG_RDT_TESTMARK);
#endif
			break;

		case PRL_STATE_RETRY_VERIFY_TEST:
#if (RDT_RETRY_CMD_VERIFY)
			M_UART(RDT_TEST_, "\n RDT Check time: flow [RETRY_VERIFY_TEST] Start");
			gubLEDState = RDT_NAND_TEST;
			rdt_api_rml_log_add(rdt, RML_IMARK_RETRY_VERIFY_TEST_START);
			rdt_api_write_log_to_flash(rdt, RDT_LOG_RDT_TESTMARK);
			rdt_flash_retry_cmd_verify(rdt);
			rdt_api_rml_log_add(rdt, RML_IMARK_RETRY_VERIFY_TEST_END);
			rdt_api_write_log_to_flash(rdt, RDT_LOG_RDT_TESTMARK);
			M_UART(RDT_TEST_, "\n RDT Check time: flow [RETRY_VERIFY_TEST] Finish");
#endif
			break;
		case PRL_STATE_SLC_RDY_TIME_TEST:
#if (RDT_RDY_TIME_TEST)	//For TPROG, Reip
			M_UART(RDT_TEST_, "\n RDT Check time: flow [SLC_RDY_TIME_TEST] Start ");
			gubLEDState = RDT_NAND_TEST;
			rdt_api_rml_log_add(rdt, RML_IMARK_RDY_TEST_SLC_START);
			rdt_api_write_log_to_flash(rdt, RDT_LOG_RDT_TESTMARK);
			rdt_flash_rdy_time_test_process(rdt, TRUE);
			rdt_api_rml_log_add(rdt, RML_IMARK_RDY_TEST_SLC_END);
			rdt_api_write_log_to_flash(rdt, RDT_LOG_RDT_TESTMARK);
			M_UART(RDT_TEST_, "\n RDT Check time: flow [SLC_RDY_TIME_TEST] Finish ");
#endif
			break;

		case PRL_STATE_TLC_RDY_TIME_TEST:
#if (RDT_RDY_TIME_TEST)	//For TPROG, Reip
			M_UART(RDT_TEST_, "\n RDT Check time: flow [TLC_RDY_TIME_TEST] Start ");
			gubLEDState = RDT_NAND_TEST;
			rdt_api_rml_log_add(rdt, RML_IMARK_RDY_TEST_TLC_START);
			rdt_api_write_log_to_flash(rdt, RDT_LOG_RDT_TESTMARK);
			rdt_flash_rdy_time_test_process(rdt, FALSE);
			rdt_api_rml_log_add(rdt, RML_IMARK_RDY_TEST_TLC_END);
			rdt_api_write_log_to_flash(rdt, RDT_LOG_RDT_TESTMARK);
			M_UART(RDT_TEST_, "\n RDT Check time: flow [TLC_RDY_TIME_TEST] Finish ");
#endif
			break;
		case PRL_STATE_IC_TEST:
			gubLEDState = RDT_IC_TEST;
			M_UART(RDT_TEST_, "\n\r RDT Check time: flow [IC_TEST] Start ");
			if ( !rdt_api_ic_pattern_test(rdt) ) {
				error_happened_force_to_save_log = 1;
				gpLastStatus->B.ubCmdError = rdt->rdt_err;
				break;
			}
			M_UART(RDT_TEST_, "\n current core power %b \n", rdt_api_read_voltage_setting() >> COREPOWER_SEL_SHIFT);
			M_UART(RDT_TEST_, "\n\r RDT Check time: flow [IC_TEST] finish ");
			break;

		case PRL_STATE_ENTER_LOW_POWER:
			M_UART(RDT_TEST_, "\n RDT Check time: flow [ENTER_LOW_POWER] Start ");
			rdt_enter_low_power_mode(rdt);
			M_UART(RDT_TEST_, "\n RDT Check time: flow [ENTER_LOW_POWER] finish ");
			break;

		case PRL_STATE_ENTER_NORMAL_POWER:
			M_UART(RDT_TEST_, "\n RDT Check time: flow [ENTER_NORMAL_POWER] Start ");
			rdt_enter_normal_power_mode(rdt);
			M_UART(RDT_TEST_, "\n RDT Check time: flow [ENTER_NORMAL_POWER] finish ");
			break;

		case PRL_STATE_SAVE_LOG:
			//Led status change
			M_UART(RDT_TEST_, "\n RDT Check time: flow [SAVE_LOG] Start ");
			rdt_api_save_log(rdt);
			rdt_api_update_ph_block(rdt);
			M_UART(RDT_TEST_, "\n RDT Check time: flow [SAVE_LOG] finish ");
			break;

		default:
			break;
		}

		if (error_happened_force_to_save_log) {
			gubLEDState = RDT_TEST_ERROR;
			//error happened, force to save log
			rdt->rdt_test_flow_index = RDT_TEST_FLOW_MAX - 3;//avoid overflow
			rdt->rdt_test_flow[rdt->rdt_test_flow_index + 1] = PRL_STATE_SAVE_LOG;
			rdt->rdt_test_flow[rdt->rdt_test_flow_index + 2] = PRL_STATE_NO_ACTION;
		}

		gpLastStatus->B.ubPRLProgress = 100;
		rdt->rdt_test_flow_index += ((rdt->rdt_test_flow_index >= RDT_TEST_FLOW_MAX) ? 0 : 1);
	}
	else {
		M_UART(RDT_TEST_, "\nRDT Finish, Index=0x%b", rdt->rdt_test_flow_index);
		gpLastStatus->B.ubFlowIndex = 0xFF;
		rdt->btRDTOnlineRunning = FALSE;
		//gubUartDebugShow = FALSE;
	}
}

void VUC_RDTOnlinePassThrough(VUC_OPT_HCMD_PTR_t pCmd)
{
	RDT_API_STRUCT_PTR rdt = &gRdtApiStruct;

	M_UART(RDT_TEST_, "\nVendor Fea=0x%b,Sub=0x%b.", (U8)pCmd->vuc_sqcmd.raw_data.dw[12], (U8)((pCmd->vuc_sqcmd.raw_data.dw[12] >> 8) & 0xFF));

	switch (((pCmd->vuc_sqcmd.raw_data.dw[12] >> 8) & 0xFF)) {
	case VUC_ONLINE_FW_INIT:
		gMainJumpManager.btFWInitOnlineFlag = TRUE;
		//gubUartDebugShow = FALSE;
		//Set Ctl/Flh back to boot code state
		ClockSwitchModeAndACTiming(CLOCK_FLH_IF_RESET);
		Nand2DefaultModeFlow();
		FlaIOTypeSetting(CH_ALL, FIP_IO_SINGLE_END);
		FlaInterfaceSetting(CH_ALL, LEGACY_INTERFACE);
		memset((void *)(&gFlhEnv), 0x00, sizeof(FLH_ENV_STRUCT_t));
		memset((void *)(&gC1Cop0FplApiStruct), 0x00, sizeof(FPL_API_STRUCT));
		memset((void *)(&gRdtApiStruct), 0x00, sizeof(RDT_API_STRUCT));
		memset((void *)(&ERL_Bypass_Record_HB_RETRY_INFO), 0x00, sizeof(ERL_Bypass_Record_HB_RETRY_INFO));
		rdt->fpl = &gC1Cop0FplApiStruct;
		FWInit();
		rdt_api_basic_init(rdt);
		//gubUartDebugShow = FALSE;
		if (rdt->err_bitmap.fields.rdt_init_flow_fail) {
			pCmd->ubState = CMD_ERROR;
			gVUCVar.ubVucCmdErrStatus = HOST_ERROR_CMD_FAIL;
		}
		else {
			rdt->btRDTOnlineInited = TRUE;
		}
		gMainJumpManager.btFWInitOnlineFlag = FALSE;
		break;
	case VUC_ONLINE_GET_FLASH_ID:
		OnlineGetFlashID(pCmd);
		rdt->btRDTOnlineInited = FALSE;
		break;
	case VUC_ONLINE_GET_UNIQUE_ID:
		OnlineGetUniqueID(pCmd);
		break;
	case VUC_ONLINE_GET_PH_UNIQUE_ID:
		OnlineGetPHUniqueID(pCmd, rdt);
		break;
	case VUC_ONLINE_START_RDT_FLOW:
		if (!rdt->btRDTOnlineInited) {
			pCmd->ubState = CMD_ERROR;
			gVUCVar.ubVucCmdErrStatus = HOST_ERROR_CMD_FAIL;
			break;
		}
		//gubUartDebugShow = FALSE;
		rdt->btRDTOnlineRunning = TRUE;
		rdt->btRDTOnlineReRun = TRUE;
		rdt->rdt_test_flow_index = 0;
		rdt->btRDTOnlineRecordResultFinal = TRUE;
		rdt_api_basic_init(rdt);
		if (!rdt->info_blk_find || rdt->err_bitmap.fields.rdt_init_flow_fail) {
			pCmd->ubState = CMD_ERROR;
			gVUCVar.ubVucCmdErrStatus = HOST_ERROR_CMD_FAIL;
			rdt->btRDTOnlineRunning = FALSE;
			rdt->btRDTOnlineReRun = FALSE;
		}
		else {
			if (rdt->btRDTOnlineRecordResultFinal) {
				memset((void *)(gpResultFinal + sizeof(UART_STATUS)), 0x00, sizeof(RFL_LOG_STRUCT) - sizeof(UART_STATUS));
				gpResultFinal->ubRFLVersion[0] = RFL_VERSION_MAJOR;
				gpResultFinal->ubRFLVersion[1] = RFL_VERSION_MINOR;
				gpResultFinal->ullCEBitMap = gFlhEnv.ulPhysicalCEBMP;
				memset(gpResultFinal->uwWindowWidthR, 0xFF, sizeof(gpResultFinal->uwWindowWidthR));
				memset(gpResultFinal->uwWindowWidthW, 0xFF, sizeof(gpResultFinal->uwWindowWidthW));
			}
		}
		break;
	default:
		break;
	}

	M_UART(RDT_TEST_, "\nVendor Fea=0x%b,Sub=0x%b, Finish.", (U8)pCmd->vuc_sqcmd.raw_data.dw[12], (U8)((pCmd->vuc_sqcmd.raw_data.dw[12] >> 8) & 0xFF));
}

void RDT_Main_Online(void)
{
	memset((void *)(&gC1Cop0FplApiStruct), 0x00, sizeof(FPL_API_STRUCT));
	memset((void *)(&gRdtApiStruct), 0x00, sizeof(RDT_API_STRUCT));
	memset((void *)(&ERL_Bypass_Record_HB_RETRY_INFO), 0x00, sizeof(ERL_Bypass_Record_HB_RETRY_INFO));
	memset((void *)gpLastStatus, 0x00, sizeof(UART_STATUS));

	gpLastStatus->B.ubTag[0] = 'L';
	gpLastStatus->B.ubTag[1] = 'S';
	gpLastStatus->B.ubAddr = gsUartPortInfo.B.ubSlaveID;

	RDT_API_STRUCT_PTR rdt = &gRdtApiStruct;
	rdt->fpl = &gC1Cop0FplApiStruct;
	U8 ubValue = setjmp(env_start);//长跳转:这里设置起始位置,通过longjmp(env_start,1)可以跳回此处.
	if (1 == ubValue) {
		//FW_ASSET() jump back
		if (gpLastStatus->B.ubErrorCode || gpLastStatus->B.ubCmdError) {
			gubLEDState = RDT_TEST_ERROR;
			rdt->btRDTOnlineRunning = 0;
		}
	}
	else if (2 == ubValue) {
		//FW Force Stop jump back
		gpLastStatus->B.ubFlowIndex = 0xFF;
		gpLastStatus->B.ubFlowCount = 0xFF;
		gubLEDState = STATE_NORMAL;
		rdt->btRDTOnlineRunning = 0;
	}
	while (1) {

		if (rdt->btRDTOnlineRunning) {
			OnlineStartRDTFlow(rdt);
		}

#if (RDT_UART_TRANSFER)
		//Deal with Uart Command
		if (UART_VUC_MODE_EN) {
			U8 ubVUCState = STATE_NORMAL;	// Initial VUC state

			if (rdt->btRDTOnlineRunning) {
				gUartVendorParam.ubUartVendorEn = FALSE;
			}

			if (gUartVendorParam.ubUartVendorEn) {
				ubVUCState = STATE_VUC;
			}
			else {
				ubVUCState = STATE_NORMAL;
			}

			if (ubVUCState == STATE_VUC) {
				M_UART(RDT_TEST_, "\n ubVUCState == STATE_VUC \n");
				/* Run vuc */
				VUCDealCommandIn((UartVendorParam_t *)&gUartVendorParam);

				ubVUCState = STATE_NORMAL;
			}
		}
#else
		//Deal with USB Command
		USBGetUSBProtocol();

		FTLSyncCmdHandler_USB(&gUSBCUrrentCMD, (U32)M_DB_GET_RD_CNT(DB_APU_CMD_CQ));

		if (FALSE == M_USB_CHK_HOST_EVENT()) {
			if (M_DB_GET_RD_CNT(DB_APU_TD_CQ)) {
				USBTDHandler();
			}
		}

		if ((M_CHECK_HOST_EVENT() & ERROR_HANDLE_APU_WRITE_ERROR_BIT) || M_USB_CHK_HOST_EVENT() || M_USB_CHECK_HOST_EVENT_LPM() || M_USB_CHK_HOST_EVENT_L1()) {
			HostEventHandler();
			M_UART(USB_DEBUG_, "\nHostEventHandler()\n");
		}

		if (gHostManager.ubType && (HOST_STATE_FW_DOING != gHostManager.ubState)) {
			HostDelegateMgr();
			M_UART(USB_DEBUG_, "\bHostDelegateMgr()\n");
		}
#endif
	}
}
#endif
#endif /* RDT_MODE_EN */
