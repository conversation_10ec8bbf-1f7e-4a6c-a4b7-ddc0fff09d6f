#include "burner/Burner_api.h"
#include "hal/cop0/cop0_api.h"
#include "hal/dmac/dmac_api.h"
#include "hal/dmac/dmac_pop_cmd.h"
#include "hal/pic/uart/uart_api.h"
#include "hal/sys/api/efuc/efuse_api.h"
#include "nvme_api/nvme/shr_hal_nvme.h"
#include "host/VUC_handler.h"
#include "vuc/VUC_command.h"
#include "vuc/VUC_Utilities.h"
#include "vuc/VUC_ProgramFlash.h"

void VUCProgram(U8 ubDumpMode, U8 ubSLCMode, U8 ubLMU, U32 ulPCA, U32 ulBufAdr, U32 ulSeed, U8 ubFWSpecific)
{
	U8 ubFramIdx;
	U32 ulLCA[FRAMES_PER_PAGE] = {0};
	COP0Status_t eCOP0Status;
	COP0WriteSQPara_t WriteSQPara = {{0}};
	BUF_TYPE_t ulBufPara[FRAMES_PER_PAGE] = {{0}};
	FWSetPara_t ulFWSet[FRAMES_PER_PAGE] = {{0}};
	cmd_table_t uoCallbackInfo = {0};

	if (FALSE == ubDumpMode) {
		M_FIP_VUC_DIRECTED_READ_SOURCE_DATA();

		//R32_FALL[R32_FCTL_CHNL_SET] &= CLR_INV_BYPASS_DIS;

		M_CLR_COP0_CONV(COP0_MT_TEMP_BUF_ADDR_WRITE);
	}
	else {
		M_FIP_VUC_DIRECTED_READ_RAW_DATA();

		//R32_FALL[R32_FCTL_CHNL_SET] |= SET_INV_BYPASS_EN;

		M_SET_COP0_CONV(COP0_MT_TEMP_BUF_ADDR_WRITE);
	}

	for (ubFramIdx = 0; ubFramIdx < (gFlhEnv.uwPageByteCnt >> 12); ubFramIdx++) {
		ulBufPara[ubFramIdx].A.ulBUF_ADR = ulBufAdr + FRAME_SIZE * ubFramIdx;
		ulFWSet[ubFramIdx].ubZInfo = MAX_ZINFO;
	}

	COP0API_FillCOP0WriteSQUserData0Para(COP0_W_SYSTEM_PROGRAM, &WriteSQPara);
	WriteSQPara.UserData0.btSLCMode = ubSLCMode ? TRUE : FALSE;
	if (!ubSLCMode) { // TLC
		WriteSQPara.UserData0.btVUCSinglePlane = TRUE;
		if (0x02 != ubLMU) {
			//WriteSQPara.UserData0.ubMultiPlaneMode = MULTIPLANE_ING;
			WriteSQPara.UserData0.btNeedCQ = FALSE;
			WriteSQPara.UserData0.uwTagID = TAG_ID_DEFAULT;
		}
		else {
			//WriteSQPara.UserData0.ubMultiPlaneMode = MULTIPLANE_END;
			uoCallbackInfo.ulData.ulFLHInfo.btKeepTag = TRUE;
		}
	}
	else { // SLC
		uoCallbackInfo.ulData.ulFLHInfo.btKeepTag = TRUE;
	}
	WriteSQPara.ulPCA.ulAll = ulPCA;
	WriteSQPara.BufVld.ubBufType = BUF_TYPE_A;
	WriteSQPara.BufVld.pulBufInfoPtr = ulBufPara;
	WriteSQPara.pulFWSetPtr = ulFWSet;
	WriteSQPara.pulLCAPtr = ulLCA;

	WriteSQPara.ulSeed.Seed = ulSeed;
	WriteSQPara.UserData0.btUseParamSeed = (ubFWSpecific) ? FALSE : TRUE;
#if PS5017_EN
	if (FALSE == WriteSQPara.UserData0.btUseParamSeed) {
		WriteSQPara.UserData0.btSysArea = (ulSeed) ? TRUE : FALSE;
	}
#endif /* PS5017_EN */

	eCOP0Status = COP0API_SendWriteSQ(&WriteSQPara, &uoCallbackInfo);
	M_FW_ASSERT(ASSERT_VUC_0x0AC6, eCOP0Status.btSendCmdSuccess);

	if ((TRUE == ubSLCMode) || (ubLMU == (gubLMUNumber - 1))) {
		BurnerWaitCQDone(WriteSQPara.UserData0.uwTagID);
	}
}

void VUC_ProgPage(VUC_OPT_HCMD_PTR_t pCmd)
{
	if (TRUE == gFlhEnv.ulFlashDefaultType.BitMap.btNoSupport) {
		pCmd->ubState = CMD_ERROR;
		gVUCVar.ubVucCmdErrStatus = HOST_ERROR_FEATURE;
		return;
	}

	U8 ubRawData, ubSLCMode, ubA2_or_DA, ubCE, ubRuleIndex, ubFWSpecific;
	U16 uwBlock, uwPage, uwNode;
	U32 uli, ulWritePageCount = 0, ulPCA = 0, ulTmpPCA = 0, ulSeed;
	U32 ulCOP0_TMP, ulFCTL_ECC_CFG_TMP, ulFCON_CRC_EN_TMP;

	FlashAccessInfo_t FlaInfo = {0};

	M_UART(VUC_, "\nVUC_Program_Page");

	// -------Save register config-------
	M_FIP_VUC_DIRECTED_WRITE_BACK_UP(ulCOP0_TMP, ulFCTL_ECC_CFG_TMP, ulFCON_CRC_EN_TMP);

	ubRawData = pCmd->vuc_sqcmd.vendor.DirectWirteFlash.btRawData;    // 0: sourece data , 1: raw data
	ubSLCMode = ((pCmd->vuc_sqcmd.vendor.DirectWirteFlash.CmdMode & BIT1) >> 1);// 0: Normal , 1: else
	ubA2_or_DA = (pCmd->vuc_sqcmd.vendor.DirectWirteFlash.CmdMode & BIT0);// 0: A2 , 1: DA
	ulWritePageCount = (pCmd->vuc_sqcmd.vendor.DirectWirteFlash.ulLength * BYTE_PER_DW) / gFlhEnv.uwPageByteCnt;
	ulSeed = (pCmd->vuc_sqcmd.vendor.DirectWirteFlash.uwSeed_H << 16) | pCmd->vuc_sqcmd.vendor.DirectWirteFlash.uwSeed;
	ubFWSpecific = pCmd->vuc_sqcmd.vendor.DirectWirteFlash.FWSpecific.btSeedModeEn;

	if ((pCmd->vuc_sqcmd.vendor.DirectWirteFlash.ulLength * BYTE_PER_DW) % gFlhEnv.uwPageByteCnt) {
		ulWritePageCount++;
	}

	uwNode = pCmd->vuc_sqcmd.vendor.DirectWirteFlash.uwNode;
	uwPage = pCmd->vuc_sqcmd.vendor.DirectWirteFlash.uwPage;
	uwBlock = pCmd->vuc_sqcmd.vendor.DirectWirteFlash.uwBlock;
	ubCE = pCmd->vuc_sqcmd.vendor.DirectWirteFlash.ubCE;

	FlaInfo.ubChannel = ubCE % gFlhEnv.ubChannelExistNum;
	FlaInfo.ubFlashCE = ubCE / gFlhEnv.ubChannelExistNum;
	FlaInfo.ubFrame = 0;
	FlaInfo.ubPlane = (U8)(uwBlock & gubBurstsPerBankMask);
	FlaInfo.ubLUN = (uwBlock >> gubBurstsPerBankLog) >> gPCARule_Block.ubBit_No;
	FlaInfo.ulBlock = (uwBlock >> gubBurstsPerBankLog) & gPCARule_Block.ulMask;
	FlaInfo.uwPage = uwPage;
	FlaInfo.ulBufBase = gulVUCBufAddr;

	ubRuleIndex = (ubSLCMode == FALSE) ? COP0_PCA_RULE_0 : COP0_PCA_RULE_2;

	ulPCA = FlaGetPCA(FlaInfo.ubPlane, FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, FlaInfo.uwPage, FlaInfo.ulBlock, ubRuleIndex);

	if (FALSE == ubSLCMode) {	// TLC
		for (uli = 0; uli < 3; uli++) {
			ulTmpPCA = ulPCA + ((uli & gPCARule_LMU.ulMask) << gPCARule_LMU.ubShift[ubRuleIndex]);
			VUCProgram(ubRawData, ubSLCMode, (U8) uli, FTLReverseCop0CieInPCA(ulTmpPCA), FlaInfo.ulBufBase, ulSeed, ubFWSpecific);

			FlaInfo.ulBufBase += gFlhEnv.uwPageByteCnt;
		}
	}
	else {	// SLC
		for (uli = 0; uli < ulWritePageCount; uli++) {
			ulTmpPCA = ulPCA + ((uli & gPCARule_Page.ulMask) << gPCARule_Page.ubShift[ubRuleIndex]);
			VUCProgram(ubRawData, ubSLCMode, 0, FTLReverseCop0CieInPCA(ulTmpPCA), FlaInfo.ulBufBase, ulSeed, ubFWSpecific);

			FlaInfo.ulBufBase += gFlhEnv.uwPageByteCnt;
		}
	}

	//-------Restore register config-------
	M_FIP_VUC_DIRECTED_WRITE_RESTORE(ulCOP0_TMP, ulFCTL_ECC_CFG_TMP, ulFCON_CRC_EN_TMP);
}

void VUC_ProgPCA(VUC_OPT_HCMD_PTR_t pCmd)
{
	if (TRUE == gFlhEnv.ulFlashDefaultType.BitMap.btNoSupport) {
		pCmd->ubState = CMD_ERROR;
		gVUCVar.ubVucCmdErrStatus = HOST_ERROR_FEATURE;
		return;
	}

	U8 ubRawData, ubSLCMode, ubA2_or_DA, ubRuleIndex, ubFWSpecific;
	U32 uli, ulWritePageCount, ulBufAdr = gulVUCBufAddr, ulPCA = 0, ulTmpPCA = 0, ulSeed;
	U32 ulCOP0_TMP, ulFCTL_ECC_CFG_TMP, ulFCON_CRC_EN_TMP;
	DMACParam_t DMACParam = {{0}};

	// -------Save register config-------
	M_FIP_VUC_DIRECTED_WRITE_BACK_UP(ulCOP0_TMP, ulFCTL_ECC_CFG_TMP, ulFCON_CRC_EN_TMP);

	M_UART(VUC_, "\nVUC_Program_PCA");

	ubRawData = pCmd->vuc_sqcmd.vendor.DirectWirteFlash.btRawData;    // 0: sourece data , 1: raw data
	ubSLCMode = ((pCmd->vuc_sqcmd.vendor.DirectWirteFlash.CmdMode & BIT1) >> 1);// 0: Normal , 1: else
	ubA2_or_DA = (pCmd->vuc_sqcmd.vendor.DirectWirteFlash.CmdMode & BIT0);// 0: A2 , 1: DA
	ulWritePageCount = (pCmd->vuc_sqcmd.vendor.DirectWirteFlash.ulLength * BYTE_PER_DW) / gFlhEnv.uwPageByteCnt;
	ulSeed = (pCmd->vuc_sqcmd.vendor.DirectWirteFlash.uwSeed_H << 16) | pCmd->vuc_sqcmd.vendor.DirectWirteFlash.uwSeed;
	ubFWSpecific = pCmd->vuc_sqcmd.vendor.DirectWirteFlash.FWSpecific.btSeedModeEn;

	ulPCA = pCmd->vuc_sqcmd.raw_data.dw[13];

	if ((pCmd->vuc_sqcmd.vendor.DirectWirteFlash.ulLength * BYTE_PER_DW) % gFlhEnv.uwPageByteCnt) {
		ulWritePageCount++;
	}

	ubRuleIndex = (ubSLCMode == FALSE) ? COP0_PCA_RULE_0 : COP0_PCA_RULE_2;

	if ((ubSLCMode == FALSE) && (((ulPCA >> gPCARule_LMU.ubShift[ubRuleIndex]) & gPCARule_LMU.ulMask) != (gubLMUNumber - 1))) {
		ulBufAdr += gFlhEnv.uwPageByteCnt * (2 - ((ulPCA >> gPCARule_LMU.ubShift[ubRuleIndex]) & gPCARule_LMU.ulMask));
		DMACParam.DMACDataCopy.ulSourceAddr = (U32)gulVUCBufAddr;
		DMACParam.DMACDataCopy.ulDestAddr = ulBufAdr;
		DMACParam.DMACDataCopy.ul32ByteNum = SIZE_IN_32B(gFlhEnv.uwPageByteCnt);
		gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
		DMACCopy(DMAC_MODE_NORMAL_QUEUE, &DMACParam, gulDMACDirectWaitDone_Callback, 0);
		while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
			DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
		}
	}

	for (uli = 0; uli < ulWritePageCount; uli++) {
		ulTmpPCA = ulPCA + ((uli & gPCARule_Page.ulMask) << gPCARule_Page.ubShift[ubRuleIndex]);
		VUCProgram(ubRawData, ubSLCMode, (ulPCA >> gPCARule_LMU.ubShift[ubRuleIndex]) & gPCARule_LMU.ulMask, ulTmpPCA, ulBufAdr, ulSeed, ubFWSpecific);
		ulBufAdr += gFlhEnv.uwPageByteCnt;
	}

	//-------Restore register config-------
	M_FIP_VUC_DIRECTED_WRITE_RESTORE(ulCOP0_TMP, ulFCTL_ECC_CFG_TMP, ulFCON_CRC_EN_TMP);
}
