#ifndef _VUC_MICRONGETDEFECTLIST_H_
#define _VUC_MICRONGETDEFECTLIST_H_
#include "aom/aom_api.h"

#define VUC_MICRON_GET_DEFECT_LIST_CLASS	(0x0004)
#define VUC_MICRON_GET_DEFECT_LIST_CODE		(0x000C)

#define VUC_MICRON_GET_DEFECT_LIST_HEADER_LENGTH			(12)
#define VUC_MICRON_GET_DEFECT_LIST_PAYLOAD_HEADER_LENGTH	(32)

#define VUC_MICRON_GET_DEFECT_TYPE_NUM		(3)

#define VUC_MICRON_GET_DEFECT_ALL_BLOCK		(0xffff)
#define VUC_MICRON_GET_DEFECT_LIST_TOTAL_LENGTH	(64)

typedef enum DefectTypeList {
	OTP_RETIRED_BLOCK,
	BURNIN_RETIRED_BLOCK,
	GROWN_RETIRED_BLOCK,
	FIRMWARE_RESERVED_BLOCK,
	DEFECT_TYPE_NUM
} DefectTypeListEnum_t;

#pragma pack(push)
#pragma pack(1)
typedef struct {
	U8 ubResponseHeaderFormatVersion;
	U8 ubResponseDataFotmatVersion;
	U16 uwCMDClass;
	U16 uwCMDCode;
	U16 uwStatus;
	U32 ulDataPayloadSize;
} GetDefectListResponseHEADER_t;
#pragma pack(pop)

typedef struct {
	U16 uwChannel;
	U16 uwCE;
	U16 uwLUN;
} GetDefectListInputData_t;


AOM_VUC_3 void VUCMicronGetDefectList(U32 ulInputPayloadAddr, U32 ulResponsePayloadAddr);

#endif /* _VUC_MICRONGETDEFECTLIST_H_ */
