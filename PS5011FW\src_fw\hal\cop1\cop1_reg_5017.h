#ifndef _S17_COP1_REG_H_
#define _S17_COP1_REG_H_

#include "symbol.h"
#include "typedef.h"

/*=============	COP1 Base offset ============= */
#define COP1_SH_BLK_OFFSET				(0x000)
#define COP1_ST1_BLK_OFFSET				(0x100)
#define COP1_GC_BLK_OFFSET				(0x200)
#define COP1_ST3_BLK_OFFSET				(0x900) //KC:0x300-->0x900, E17 spec
#define COP1_ST3C_BLK_OFFSET			(0x400)
#define COP1_CMDINTF_BLK_OFFSET			(0x500)
#define COP1_A2S_BLK_OFFSET				(0x600)
#define COP1_BUSINTF_BLK_OFFSET			(0x740)
#define COP1_BRG_BLK_OFFSET				(0x800)

#define COP1_REG_BASE					(COP1_REG_ADDRESS)

#define R8_COP1							((volatile U8 *)  COP1_REG_BASE)
#define R16_COP1						((volatile U16 *) COP1_REG_BASE)
#define R32_COP1						((volatile U32 *) COP1_REG_BASE)

#define R8_COP1_SH						((volatile U8 *) (COP1_REG_BASE + COP1_SH_BLK_OFFSET))
#define R16_COP1_SH						((volatile U16 *) (COP1_REG_BASE + COP1_SH_BLK_OFFSET))
#define R32_COP1_SH						((volatile U32 *) (COP1_REG_BASE + COP1_SH_BLK_OFFSET))

#define R8_COP1_ST1						((volatile U8 *)  (COP1_REG_BASE + COP1_ST1_BLK_OFFSET))
#define R16_COP1_ST1					((volatile U16 *) (COP1_REG_BASE + COP1_ST1_BLK_OFFSET))
#define R32_COP1_ST1					((volatile U32 *) (COP1_REG_BASE + COP1_ST1_BLK_OFFSET))

#define R8_COP1_GC						((volatile U8 *)  (COP1_REG_BASE + COP1_GC_BLK_OFFSET))
#define R16_COP1_GC						((volatile U16 *) (COP1_REG_BASE + COP1_GC_BLK_OFFSET))
#define R32_COP1_GC						((volatile U32 *) (COP1_REG_BASE + COP1_GC_BLK_OFFSET))

#define R8_COP1_ST3						((volatile U8 *)  (COP1_REG_BASE + COP1_ST3_BLK_OFFSET))
#define R16_COP1_ST3					((volatile U16 *) (COP1_REG_BASE + COP1_ST3_BLK_OFFSET))
#define R32_COP1_ST3					((volatile U32 *) (COP1_REG_BASE + COP1_ST3_BLK_OFFSET))

#define R8_COP1_ST3C					((volatile U8 *)  (COP1_REG_BASE + COP1_ST3C_BLK_OFFSET))
#define R16_COP1_ST3C					((volatile U16 *) (COP1_REG_BASE + COP1_ST3C_BLK_OFFSET))
#define R32_COP1_ST3C					((volatile U32 *) (COP1_REG_BASE + COP1_ST3C_BLK_OFFSET))

#define R8_COP1_CMDINTF					((volatile U8 *)  (COP1_REG_BASE + COP1_CMDINTF_BLK_OFFSET))
#define R16_COP1_CMDINTF				((volatile U16 *) (COP1_REG_BASE + COP1_CMDINTF_BLK_OFFSET))
#define R32_COP1_CMDINTF				((volatile U32 *) (COP1_REG_BASE + COP1_CMDINTF_BLK_OFFSET))

#define R8_COP1_A2S						((volatile U8 *)  (COP1_REG_BASE + COP1_A2S_BLK_OFFSET))
#define R16_COP1_A2S					((volatile U16 *) (COP1_REG_BASE + COP1_A2S_BLK_OFFSET))
#define R32_COP1_A2S					((volatile U32 *) (COP1_REG_BASE + COP1_A2S_BLK_OFFSET))

#define R8_COP1_BUSINTF					((volatile U8 *)  (COP1_REG_BASE + COP1_BUSINTF_BLK_OFFSET))
#define R16_COP1_BUSINTF				((volatile U16 *) (COP1_REG_BASE + COP1_BUSINTF_BLK_OFFSET))
#define R32_COP1_BUSINTF				((volatile U32 *) (COP1_REG_BASE + COP1_BUSINTF_BLK_OFFSET))

#define R8_COP1_BRG						((volatile U8 *)  (COP1_REG_BASE + COP1_BRG_BLK_OFFSET))
#define R16_COP1_BRG					((volatile U16 *) (COP1_REG_BASE + COP1_BRG_BLK_OFFSET))
#define R32_COP1_BRG					((volatile U32 *) (COP1_REG_BASE + COP1_BRG_BLK_OFFSET))

/*=============	SH ============= */
#define R32_COP1_SH_CR_DB_SEL					(0x00 >> 2)
#define 	SH_CR_DB_SEL_SHIFT						(0)
#define 	SH_CR_DB_SEL_MASK						(BIT_MASK(6))

#define R32_COP1_SH_SR_CNT						(0x0C >> 2)
#define 	EXEC_CNT_SHIFT							(0)
#define 	EXEC_CNT_MASK							(BIT_MASK(7))
#define 	IDLE_SHIFT								(7)
#define 	IDLE_MASK								(BIT_MASK(1))
#define 	SR_SH_IDLE							(IDLE_MASK << IDLE_SHIFT)

#define R32_COP1_SH_SR_RSLTQ					(0x10 >> 2)
#define 	CMDIF_CMDQ_EMPTY_SHIFT					(0)
#define 	CMDIF_CMDQ_EMPTY_MASK					(BIT_MASK(1))
#define 	CMDIF_CMDQ_FULL_SHIFT					(1)
#define 	CMDIF_CMDQ_FULL_MASK					(BIT_MASK(1))
#define 	CMDIF_RSLTQ_EMPTY_SHIFT					(2)
#define 	CMDIF_RSLTQ_EMPTY_MASK					(BIT_MASK(1))
#define 	CMDIF_RSLTQ_FULL_SHIFT					(3)
#define 	CMDIF_RSLTQ_FULL_MASK					(BIT_MASK(1))
#define 	CORE_WLB1_EMPTY_SHIFT					(4)
#define 	CORE_WLB1_EMPTY_MASK					(BIT_MASK(1))
#define 	CORE_WLB1_FULL_SHIFT					(5)
#define 	CORE_WLB1_FULL_MASK						(BIT_MASK(1))
#define 	CORE_WLB2_EMPTY_SHIFT					(6)
#define 	CORE_WLB2_EMPTY_MASK					(BIT_MASK(1))
#define 	CORE_WLB2_FULL_SHIFT					(7)
#define 	CORE_WLB2_FULL_MASK						(BIT_MASK(1))
#define 	CORE_ST1_EMPTY_SHIFT					(8)
#define 	CORE_ST1_EMPTY_MASK						(BIT_MASK(1))
#define 	CORE_ST1_FULL_SHIFT						(9)
#define 	CORE_ST1_FULL_MASK						(BIT_MASK(1))
#define 	CORE_XZIP_EMPTY_SHIFT					(10)
#define 	CORE_XZIP_EMPTY_MASK					(BIT_MASK(1))
#define 	CORE_XZIP_FULL_SHIFT					(11)
#define 	CORE_XZIP_FULL_MASK						(BIT_MASK(1))
#define 	CORE_ST3_EMPTY_SHIFT					(12)
#define 	CORE_ST3_EMPTY_MASK						(BIT_MASK(1))
#define 	CORE_ST3_FULL_SHIFT						(13)
#define 	CORE_ST3_FULL_MASK						(BIT_MASK(1))
#define 	CORE_RSLTQ_EMPTY_SHIFT					(14)
#define 	CORE_RSLTQ_EMPTY_MASK					(BIT_MASK(1))
#define 	CORE_RSLTQ_FULL_SHIFT					(15)
#define 	CORE_RSLTQ_FULL_MASK					(BIT_MASK(1))

#define R32_COP1_SH_SR_DB_PORT_LOW				(0x14 >> 2)

#define R32_COP1_SH_SR_DB_PORT_HIGH				(0x18 >> 2)
#define 	SR_DB_PORT_HIGH_SHIFT					(0)
#define 	SR_DB_PORT_HIGH_MASK					(BIT_MASK(16))

/*=============	ST1 ============= */
#define R32_COP1_ST1_LCA_START					(0x00 >> 2)

#define R32_COP1_ST1_LCA_END					(0x04 >> 2)

#define R32_COP1_ST1_SORTING_LENGTH				(0x08 >> 2)
#define 	SORTING_LENGTH_SHIFT					(0)
#define 	SORTING_LENGTH_MASK						(BIT_MASK(11))

#define R32_COP1_ST1_SORTING_VALID				(0x0C >> 2)
#define 	SORTING_VALID_SHIFT						(0)
#define 	SORTING_VALID_MASK						(BIT_MASK(21))

#define R32_COP1_ST1_DSA_LENGTH					(0x10 >> 2)
#define 	DSA_LENGTH_SHIFT						(0)
#define 	DSA_LENGTH_MASK							(BIT_MASK(14))
#define 	DSA_VALID_SHIFT							(16)
#define 	DSA_VALID_MASK							(BIT_MASK(8))

#define R32_COP1_ST1_IP_RST_N					(0x14 >> 2)
#define 	IP_RST_N_V_SHIFT						(0)
#define 	IP_RST_N_V_MASK							(BIT_MASK(1))

#define R32_COP1_ST1_WLB_ID						(0x18 >> 2)
#define 	WLB_ID_SHIFT							(0)
#define 	WLB_ID_MASK								(BIT_MASK(3))
#define		FLUSH_BIT_SHIFT							(16)
#define		FLUSH_BIT_MASK							(BIT_MASK(1))

#define R32_COP1_ST1_START_END_INDEX			(0x1C >> 2)
#define 	DSA_START_INDEX_SHIFT					(0)
#define 	DSA_START_INDEX_MASK					(BIT_MASK(14))
#define 	DSA_END_INDEX_SHIFT						(16)
#define 	DSA_END_INDEX_MASK						(BIT_MASK(4))

#define R32_COP1_ST1_DSA_CTRL					(0x20 >> 2)
#define 	SORT_RULE_SHIFT							(0)
#define 	SORT_RULE_MASK							(BIT_MASK(8))
#define 	SORTED_PTR_SHIFT						(16)
#define 	SORTED_PTR_MASK							(BIT_MASK(4))
#define 	RELEASE_PTR_SHIFT						(24)
#define 	RELEASE_PTR_MASK						(BIT_MASK(4))

#define R32_COP1_ST1_SRCH_INDEX					(0x24 >> 2)
#define 	SRCH_START_INDEX_SHIFT					(0)
#define 	SRCH_START_INDEX_MASK					(BIT_MASK(14))
#define 	PRE_SRCH_START_INDEX_SHIFT				(16)
#define 	PRE_SRCH_START_INDEX_MASK				(BIT_MASK(14))

#define R32_COP1_ST1_IDLE						(0x28 >> 2)
#define 	IDLE_LIST_SHIFT							(0)
#define 	IDLE_LIST_MASK							(BIT_MASK(12))
#define     SR_ST1_IDLE 							(IDLE_LIST_MASK << IDLE_LIST_SHIFT)

#define R32_COP1_ST1_STM						(0x2C >> 2)
#define 	CC_MAIN_SHIFT							(0)
#define 	CC_MAIN_MASK							(BIT_MASK(10))
/*=============	GC ============= */
#define R32_COP1_GC_CR_COP1_COMMON				(0x00 >> 2)
#define 	VB_MASK_SHIFT							(0)
#define 	VB_MASK_MASK							(BIT_MASK(13))
#define 	VB_SHIFT_SHIFT							(16)
#define 	VB_SHIFT_MASK							(BIT_MASK(5))

#define R32_COP1_GC_CR_CUR_VB_G0				(0x04 >> 2)
#define 	CUR_VB0_SHIFT							(0)
#define 	CUR_VB0_MASK							(BIT_MASK(13))//E17_porting_4TB
#define 	CUR_VB1_SHIFT							(16)
#define 	CUR_VB1_MASK							(BIT_MASK(13))//E17_porting_4TB

#define R32_COP1_GC_CR_CUR_VB_G1				(0x08 >> 2)
#define 	CUR_VB2_SHIFT							(0)
#define 	CUR_VB2_MASK							(BIT_MASK(13))//E17_porting_4TB
#define 	CUR_VB3_SHIFT							(16)
#define 	CUR_VB3_MASK							(BIT_MASK(13))//E17_porting_4TB

#define R32_COP1_GC_CR_CUR_VB_G2				(0x0C >> 2)
#define 	CUR_VB4_SHIFT							(0)
#define 	CUR_VB4_MASK							(BIT_MASK(13))//E17_porting_4TB
#define 	CUR_VB5_SHIFT							(16)
#define 	CUR_VB5_MASK							(BIT_MASK(13))//E17_porting_4TB

#define R32_COP1_GC_CR_CUR_VB_G3				(0x10 >> 2)
#define 	CUR_VB6_SHIFT							(0)
#define 	CUR_VB6_MASK							(BIT_MASK(13))//E17_porting_4TB
#define 	CUR_VB7_SHIFT		 					(16)
#define 	CUR_VB7_MASK							(BIT_MASK(13))//E17_porting_4TB

// dirty Cache Log Start Address must align 16 byte.
#define R32_COP1_GC_CR_DT_ST_ADR				(0x14 >> 2)
#define 	DT_ST_ADR_SHIFT							(0)
#define 	DT_ST_ADR_MASK							(BIT_MASK(28))

#define R32_COP1_GC_CR_DT_END_ADR				(0x18 >> 2)
#define 	DT_END_ADR_SHIFT						(0)
#define 	DT_END_ADR_MASK							(BIT_MASK(28))

#define R32_COP1_GC_CR_DT_PTR					(0x1C >> 2)
#define 	DT_PTR_SHIFT							(0)
#define 	DT_PTR_MASK								(BIT_MASK(19))

#define R32_COP1_GC_CR_SLC_POOL_NUM				(0x20 >> 2)

#define R32_COP1_GC_CR_VC_SRAM_TBL				(0x24 >> 2)
#define 	VC_START_BIT_SHIFT						(0)
#define 	VC_START_BIT_MASK						(BIT_MASK(5))
#define 	VC_BIT_NUM_SHIFT						(16)
#define 	VC_BIT_NUM_MASK							(BIT_MASK(5))

#define R32_COP1_GC_CR_VB_MON					(0x28 >> 2)
#define 	CR_MON_VB_SHIFT							(0)
#define 	CR_MON_VB_MASK							(BIT_MASK(13))//E17_porting_4TB
#define 	CR_CLN_MON_VB_SHIFT						(16)
#define 	CR_CLN_MON_VB_MASK						(BIT_MASK(1))
#define 	CR_CLN_ERR_VC_SHIFT						(17)
#define 	CR_CLN_ERR_VC_MASK						(BIT_MASK(1))
#define 	CR_CLN_SLV_ERR_SHIFT					(18)
#define 	CR_CLN_SLV_ERR_MASK						(BIT_MASK(1))
#define 	CR_CLN_DT_ERR_SHIFT						(19)
#define 	CR_CLN_DT_ERR_MASK						(BIT_MASK(1))
#define 	CR_CLN_SFE_ERR_SHIFT					(20)
#define 	CR_CLN_SFE_ERR_MASK						(BIT_MASK(1))
#define 	CR_CLN_ALL_SHIFT					        (21)
#define 	CR_CLN_ALL_MASK						(BIT_MASK(1))
#define 	CR_CLN_SLC_NUM_ERR_SHIFT					(22)
#define 	CR_CLN_SLC_NUM_ERR_MASK						(BIT_MASK(1))
#define 	CR_CLN_RST_DT_SYNC_CNT_SHIFT					(23)
#define 	CR_CLN_RST_DT_SYNC_CNT_MASK						(BIT_MASK(1))


#define R32_COP1_GC_CR_AXI_OUTSTD				(0x2C >> 2)
#define 	AXI_OUTSTD_SHIFT						(0)
#define 	AXI_OUTSTD_MASK							(BIT_MASK(4))

#define R32_COP1_GC_CR_MINUS_CNT				(0x30 >> 2)
#define 	CR_MINUS_CNT_SHIFT						(0)
#define 	CR_MINUS_CNT_MASK						(BIT_MASK(19))

#define R32_COP1_GC_CR_VB_MAX_NODE				(0x34 >> 2)
#define 	CR_VB_MAX_NODE_SHIFT					(0)
#define 	CR_VB_MAX_NODE_MASK						(BIT_MASK(29))  //KC: 30-->29, E17 Spec 

#define R32_COP1_GC_CR_DT_MAX_CNT				(0x38 >> 2)
#define 	CR_DT_MAX_CNT_SHIFT						(0)
#define 	CR_DT_MAX_CNT_MASK						(BIT_MASK(19))

#define R32_COP1_GC_CR_SLC_POOL_NUM_MAX			(0x3C >> 2)

#define R32_COP1_GC_CR_TBL_MAX_NODE             (0x40 >> 2)
#define     CR_TBL_VB_MAX_NODE_MASK          	   (BIT_MASK(29))    //KC: 30-->29, E17 Spec 
//E17_porting_4TB,R32_COP1_GC_SR_VB_ERR_0/1/2/3
#define R32_COP1_GC_SR_VB_ERR_0					(0x80 >> 2)
#define 	VB_ERR_NO0_SHIFT						(0)
#define 	VB_ERR_NO0_MASK							(BIT_MASK(13))
#define 	ERR_0_S0_SHIFT							(13)
#define 	ERR_0_S0_MASK							(BIT_MASK(1))
#define 	ERR_0_S1_SHIFT							(14)
#define 	ERR_0_S1_MASK							(BIT_MASK(1))
#define 	ERR_0_S2_SHIFT							(15)
#define 	ERR_0_S2_MASK							(BIT_MASK(1))

#define R32_COP1_GC_SR_VB_ERR_1					(0x84 >> 2)
#define 	VB_ERR_NO1_SHIFT						(0)
#define 	VB_ERR_NO1_MASK							(BIT_MASK(13))
#define 	ERR_1_S0_SHIFT							(13)
#define 	ERR_1_S0_MASK							(BIT_MASK(1))
#define 	ERR_1_S1_SHIFT							(14)
#define 	ERR_1_S1_MASK							(BIT_MASK(1))
#define 	ERR_1_S2_SHIFT							(15)
#define 	ERR_1_S2_MASK							(BIT_MASK(1))

#define R32_COP1_GC_SR_VB_ERR_2					(0x88 >> 2)
#define 	VB_ERR_NO2_SHIFT						(0)
#define 	VB_ERR_NO2_MASK							(BIT_MASK(13))
#define 	ERR_2_S0_SHIFT							(13)
#define 	ERR_2_S0_MASK							(BIT_MASK(1))
#define 	ERR_2_S1_SHIFT							(14)
#define 	ERR_2_S1_MASK							(BIT_MASK(1))
#define 	ERR_2_S2_SHIFT							(15)
#define 	ERR_2_S2_MASK							(BIT_MASK(1))

#define R32_COP1_GC_SR_VB_ERR_3					(0x8C >> 2)
#define 	VB_ERR_NO3_SHIFT						(0)
#define 	VB_ERR_NO3_MASK							(BIT_MASK(13))
#define 	ERR_3_S0_SHIFT							(13)
#define 	ERR_3_S0_MASK							(BIT_MASK(1))
#define 	ERR_3_S1_SHIFT							(14)
#define 	ERR_3_S1_MASK							(BIT_MASK(1))
#define 	ERR_3_S2_SHIFT							(15)
#define 	ERR_3_S2_MASK							(BIT_MASK(1))

#define R32_COP1_GC_SR_REG0						(0x90 >> 2)
#define 	GC_IDLE_SHIFT							(0)
#define 	GC_IDLE_MASK							(BIT_MASK(1))
#define 	SR_GC_IDLE								(GC_IDLE_MASK << GC_IDLE_SHIFT)
#define 	SFE_SHIFT								(1)
#define 	SFE_MASK								(BIT_MASK(1))
#define 	SLV_ERR_SHIFT							(16)
#define 	SLV_ERR_MASK							(BIT_MASK(2))

#define R32_COP1_GC_SR_REG1						(0x94 >> 2)

#define R32_COP1_GC_SR_REG2						(0x98 >> 2)

#define R32_COP1_GC_SR_REG3						(0x9C >> 2)

#define R32_COP1_GC_SR_REG4						(0xA0 >> 2)

#define R32_COP1_GC_SR_REG5						(0xA4 >> 2)

#define R32_COP1_GC_SR_SLC_POOL_NUM0			(0xA8 >> 2)

#define R32_COP1_GC_SR_SLC_POOL_NUM1			(0xAC >> 2)
#define 	UP_SHIFT								(0)
#define 	UP_MASK									(BIT_MASK(1))

#define R32_COP1_GC_SR_SR_DT_CNT				(0xB0 >> 2)
#define 	DT_CNT_SHIFT							(0)
#define 	DT_CNT_MASK								(BIT_MASK(19))
#define 	DT_CNT_ERR_SHIFT						(30)
#define 	DT_CNT_ERR_MASK							(BIT_MASK(2))

#define R32_COP1_GC_SR_DT_GET_CNT				(0xB4 >> 2)
#define 	SR_DT_GET_CNT_SHIFT						(0)
#define 	SR_DT_GET_CNT_MASK						(BIT_MASK(10))//E17_porting_4TB

#define R32_COP1_GC_SR_DT_DONE_CNT				(0xB8 >> 2)
#define 	SR_DT_DONE_CNT_SHIFT					(0)
#define 	SR_DT_DONE_CNT_MASK						(BIT_MASK(10))//E17_porting_4TB
/*=============	ST3 ============= */
/* ST3 */
// 000h + N*2Byte, N=0~7
#define R16_COP1_ST3_CR_NS_PMD_VAL_N			(0x00 >> 1)
#define 	CR_NS_N_PMD_VAL_SHIFT					(0)
#define 	CR_NS_N_PMD_VAL_MASK					(BIT_MASK(9))

#define R32_COP1_ST3_CR_NS_PMD_VAL_G0			(0x00 >> 2)
#define 	CR_NS0_PMD_VAL_SHIFT					(0)
#define 	CR_NS0_PMD_VAL_MASK						(BIT_MASK(9))
#define 	CR_NS1_PMD_VAL_SHIFT					(16)
#define 	CR_NS1_PMD_VAL_MASK						(BIT_MASK(9))

#define R32_COP1_ST3_CR_NS_PMD_VAL_G1			(0x04 >> 2)
#define 	CR_NS2_PMD_VAL_SHIFT					(0)
#define 	CR_NS2_PMD_VAL_MASK						(BIT_MASK(9))
#define 	CR_NS3_PMD_VAL_SHIFT					(16)
#define 	CR_NS3_PMD_VAL_MASK						(BIT_MASK(9))

#define R32_COP1_ST3_CR_NS_PMD_VAL_G2			(0x08 >> 2)
#define 	CR_NS4_PMD_VAL_SHIFT					(0)
#define 	CR_NS4_PMD_VAL_MASK						(BIT_MASK(9))
#define 	CR_NS5_PMD_VAL_SHIFT					(16)
#define 	CR_NS5_PMD_VAL_MASK						(BIT_MASK(9))

#define R32_COP1_ST3_CR_NS_PMD_VAL_G3			(0x0C >> 2)
#define 	CR_NS6_PMD_VAL_SHIFT					(0)
#define 	CR_NS6_PMD_VAL_MASK						(BIT_MASK(9))
#define 	CR_NS7_PMD_VAL_SHIFT					(16)
#define 	CR_NS7_PMD_VAL_MASK						(BIT_MASK(9))

// 010h + N*2Byte, N=0~7
#define R16_COP1_ST3_CR_GC_INSERT_VB_N			(0x10 >> 1)
#define 	CR_GC_INSERT_VB_N_SHIFT					(0)
#define 	CR_GC_INSERT_VB_N_MASK					(BIT_MASK(13))

#define R32_COP1_ST3_CR_GC_INSERT_VB_G0			(0x10 >> 2)
#define 	CR_GC_INSERT_VB0_SHIFT					(0)
#define 	CR_GC_INSERT_VB0_MASK					(BIT_MASK(13))
#define 	CR_GC_INSERT_VB1_SHIFT					(16)
#define 	CR_GC_INSERT_VB1_MASK					(BIT_MASK(13))

#define R32_COP1_ST3_CR_GC_INSERT_VB_G1			(0x14 >> 2)
#define 	CR_GC_INSERT_VB2_SHIFT					(0)
#define 	CR_GC_INSERT_VB2_MASK					(BIT_MASK(13))
#define 	CR_GC_INSERT_VB3_SHIFT					(16)
#define 	CR_GC_INSERT_VB3_MASK					(BIT_MASK(13))

#define R32_COP1_ST3_CR_GC_INSERT_VB_G2			(0x18 >> 2)
#define 	CR_GC_INSERT_VB4_SHIFT					(0)
#define 	CR_GC_INSERT_VB4_MASK					(BIT_MASK(13))
#define 	CR_GC_INSERT_VB5_SHIFT					(16)
#define 	CR_GC_INSERT_VB5_MASK					(BIT_MASK(13))

#define R32_COP1_ST3_CR_GC_INSERT_VB_G3			(0x1C >> 2)
#define 	CR_GC_INSERT_VB6_SHIFT					(0)
#define 	CR_GC_INSERT_VB6_MASK					(BIT_MASK(13))
#define 	CR_GC_INSERT_VB7_SHIFT					(16)
#define 	CR_GC_INSERT_VB7_MASK					(BIT_MASK(13))

// 020h + N*2Byte, N=0~7
#define R16_COP1_ST3_CR_GC_PTE_VB_N				(0x20 >> 1)
#define 	CR_GC_FLOW_VB_N_SHIFT					(0)
#define 	CR_GC_FLOW_VB_N_MASK					(BIT_MASK(13))

#define R32_COP1_ST3_CR_GC_PTE_VB_G0			(0x20 >> 2)
#define 	CR_GC_FLOW_VB0_SHIFT					(0)
#define 	CR_GC_FLOW_VB0_MASK						(BIT_MASK(13))
#define 	CR_GC_FLOW_VB1_SHIFT					(16)
#define 	CR_GC_FLOW_VB1_MASK						(BIT_MASK(13))

#define R32_COP1_ST3_CR_GC_PTE_VB_G1			(0x24 >> 2)
#define 	CR_GC_FLOW_VB2_SHIFT					(0)
#define 	CR_GC_FLOW_VB2_MASK						(BIT_MASK(13))
#define 	CR_GC_FLOW_VB3_SHIFT					(16)
#define 	CR_GC_FLOW_VB3_MASK						(BIT_MASK(13))

#define R32_COP1_ST3_CR_GC_PTE_VB_G2			(0x28 >> 2)
#define 	CR_GC_FLOW_VB4_SHIFT					(0)
#define 	CR_GC_FLOW_VB4_MASK						(BIT_MASK(13))
#define 	CR_GC_FLOW_VB5_SHIFT					(16)
#define 	CR_GC_FLOW_VB5_MASK						(BIT_MASK(13))

#define R32_COP1_ST3_CR_GC_PTE_VB_G3			(0x2C >> 2)
#define 	CR_GC_FLOW_VB6_SHIFT					(0)
#define 	CR_GC_FLOW_VB6_MASK						(BIT_MASK(13))
#define 	CR_GC_FLOW_VB7_SHIFT					(16)
#define 	CR_GC_FLOW_VB7_MASK						(BIT_MASK(13))

#define R32_COP1_ST3_CR_LCA_START				(0x30 >> 2)

#define R32_COP1_ST3_CR_LCA_END					(0x34 >> 2)

#define R32_COP1_ST3_CR_PMD_BADR				(0x38 >> 2)
#define	M_SET_ST3_REG_PMD_BASE_ADDR(ADDR) 			(R32_COP1_ST3[R32_COP1_ST3_CR_PMD_BADR] = (ADDR))

#define R32_COP1_ST3_CR_PTE_BADR				(0x3C >> 2)
#define	M_SET_ST3_REG_PTE_BASE_ADDR(ADDR) 			(R32_COP1_ST3[R32_COP1_ST3_CR_PTE_BADR] = (ADDR))

#define R32_COP1_ST3_CR_GATHER_BADR				(0x40 >> 2)
#define 	CR_GATHER_BADR_SHIFT					(0)
#define 	CR_GATHER_BADR_MASK						(BIT_MASK(27))

#define R32_COP1_ST3_CR_PGD_BADR				(0x44 >> 2)

#define R32_COP1_ST3_CR_GCSA_PMDLOG_BADR		(0x48 >> 2)
#define 	CR_GCSA_PMDLOG_BADR_SHIFT				(0)
#define 	CR_GCSA_PMDLOG_BADR_MASK				(BIT_MASK(20))

#define R32_COP1_ST3_CR_COMMON					(0x4C >> 2)
#define 	CR_VB_MASK_SHIFT						(0)
#define 	CR_VB_MASK_MASK							(BIT_MASK(13))
#define 	CR_VB_SHIFT_SHIFT						(16)
#define 	CR_VB_SHIFT_MASK						(BIT_MASK(5))
#define 	CR_SRCH_MOVE_RULE_SHIFT					(24)
#define 	CR_SRCH_MOVE_RULE_MASK					(BIT_MASK(2))
#define 	CR_CLEAF_EN_SHIFT						(28)
#define 	CR_CLEAF_EN_MASK						(BIT_MASK(1))
#define 	CR_CACHE_TYPE_SHIFT						(29)
#define 	CR_CACHE_TYPE_MASK						(BIT_MASK(1))
#define 	CR_GCSA_BG_TRIM_SHIFT					(30)
#define 	CR_GCSA_BG_TRIM_MASK					(BIT_MASK(1))

#define R32_COP1_ST3_CR_TPTE_LCA_START			(0x50 >> 2)

#define R32_COP1_ST3_CR_TPTE_LCA_END			(0x54 >> 2)

#define R32_COP1_ST3_CR_CMD_LIMIT				(0x58 >> 2)
#define 	CR_SH_DPMD_SHIFT						(0)
#define		CR_SH_DPMD_MASK							(BIT_MASK(3))
#define 	CR_CMD_DPMD_SHIFT						(4)
#define 	CR_CMD_DPMD_MASK						(BIT_MASK(3))
#define 	CR_LBR_SHIFT							(8)
#define 	CR_LBR_MASK								(BIT_MASK(6))
#define 	CR_LBW_SHIFT							(16)
#define 	CR_LBW_MASK								(BIT_MASK(6))

#define R32_COP1_ST3_CR_DB_SEL					(0x5C >> 2)
#define 	ST3_CR_DB_SEL_SHIFT						(0)
#define 	ST3_CR_DB_SEL_MASK						(BIT_MASK(10))

#define R32_COP1_ST3_CR_PMD_TBL_BITMAP_0		(0x60 >> 2)

#define R32_COP1_ST3_CR_PMD_TBL_BITMAP_1		(0x64 >> 2)

#define R32_COP1_ST3_CR_PMD_TBL_BITMAP_2		(0x68 >> 2)

#define R32_COP1_ST3_CR_PMD_TBL_BITMAP_3		(0x6C >> 2)

#define R32_COP1_ST3_CR_PMD_TBL_BITMAP_4		(0x70 >> 2)

#define R32_COP1_ST3_CR_PMD_TBL_BITMAP_5		(0x74 >> 2)

#define R32_COP1_ST3_CR_PMD_TBL_BITMAP_6		(0x78 >> 2)

#define R32_COP1_ST3_CR_PMD_TBL_BITMAP_7		(0x7C >> 2)

#define R32_COP1_ST3_CR_PMD_TBL_BITMAP_8		(0x80 >> 2)

#define R32_COP1_ST3_CR_PMD_TBL_BITMAP_9		(0x84 >> 2)

#define R32_COP1_ST3_CR_PMD_TBL_BITMAP_10		(0x88 >> 2)

#define R32_COP1_ST3_CR_PMD_TBL_BITMAP_11		(0x8C >> 2)

#define R32_COP1_ST3_CR_PMD_TBL_BITMAP_12		(0x90 >> 2)

#define R32_COP1_ST3_CR_PMD_TBL_BITMAP_13		(0x94 >> 2)

#define R32_COP1_ST3_CR_PMD_TBL_BITMAP_14		(0x98 >> 2)

#define R32_COP1_ST3_CR_PMD_TBL_BITMAP_15		(0x9C >> 2)

#define R32_COP1_ST3_CR_GCSA_SIZE				(0xA0 >> 2)
#define 	CR_GCSA_SIZE_SHIFT						(0)
#define 	CR_GCSA_SIZE_MASK						(BIT_MASK(14))

#define R32_COP1_ST3_CR_NORMAL_PTE_PCA_SETTING	(0xA4 >> 2)
#define 	CR_NORMAL_PTE_PCA_SETTING_SHIFT			(0)
#define 	CR_NORMAL_PTE_PCA_SETTING_MASK			(BIT_MASK(2))

#define R32_COP1_ST3_CR_UPD_OLD_PCA				(0xA8 >> 2)
#define 	CR_UPD_OLD_PCA_SHIFT					(0)
#define 	CR_UPD_OLD_PCA_MASK						(BIT_MASK(1))

#define R32_COP1_ST3_SR_ID_BMT0					(0xAC >> 2)

#define R32_COP1_ST3_SR_ID_BMT1					(0xB0 >> 2)

#define R32_COP1_ST3_SR_ID_TASK0				(0xB4 >> 2)

#define R32_COP1_ST3_SR_ID_TASK1				(0xB8 >> 2)

#define R32_COP1_ST3_SR_ID_TASK2				(0xBC >> 2)

#define R32_COP1_ST3_SR_ID_TASK3				(0xC0 >> 2)

#define R32_COP1_ST3_SR_ID_TASK4				(0xC4 >> 2)

#define R32_COP1_ST3_SR_ID_TASK5				(0xC8 >> 2)

#define R32_COP1_ST3_SR_ID_TASK6				(0xCC >> 2)

#define R32_COP1_ST3_SR_ID_TASK7				(0xD0 >> 2)

#define R32_COP1_ST3_SR_DB_PORT0				(0xD4 >> 2)

#define R32_COP1_ST3_SR_DB_PORT1				(0xD8 >> 2)
#define 	SR_DB_PORT1_SHIFT						(0)
#define 	SR_DB_PORT1_MASK						(BIT_MASK(16))

#define R32_COP1_ST3_SR_ST3_IDLE				(0xDC >> 2)
#define 	SR_ST3_IDLE_SHIFT						(0)
#define		SR_ST3_IDLE_MASK						(BIT_MASK(1))
#define		SR_ST3_IDLE					         	(SR_ST3_IDLE_MASK << SR_ST3_IDLE_SHIFT)

#define R32_COP1_ST3_SR_ST3_CMD_CNT0			(0xE0 >> 2)
#define 	PTE_CMD_CNT_SHIFT						(0)
#define 	PTE_CMD_CNT_MASK						(BIT_MASK(8))
#define 	PMD_CMD_CNT_SHIFT						(8)
#define 	PMD_CMD_CNT_MASK						(BIT_MASK(8))
#define 	SH_CMD_CNT_SHIFT						(16)
#define 	SH_CMD_CNT_MASK							(BIT_MASK(8))

#define R32_COP1_ST3_SR_ST3_CMD_CNT1			(0xE4 >> 2)
#define 	SHAXI_RCMD_CNT_SHIFT					(0)
#define 	SHAXI_RCMD_CNT_MASK						(BIT_MASK(8))
#define 	LBAXI_RCMD_CNT_SHIFT					(8)
#define 	LBAXI_RCMD_CNT_MASK						(BIT_MASK(8))
#define 	LBAXI_WCMD_CNT_SHIFT					(16)
#define 	LBAXI_WCMD_CNT_MASK						(BIT_MASK(8))

#define R32_COP1_ST3_SR_ST3_CMD_CNT2			(0xE8 >> 2)
#define 	DPMDAXI_RCMD_CNT_SHIFT					(0)
#define 	DPMDAXI_RCMD_CNT_MASK					(BIT_MASK(8))
#define 	DPMDAXI_WCMD_CNT_SHIFT					(8)
#define 	DPMDAXI_WCMD_CNT_MASK					(BIT_MASK(8))

#define R32_COP1_ST3_SR_DB_WAIT					(0xEC >> 2)
#define 	SR_DB_WAIT_CNT_SHIFT					(0)
#define 	SR_DB_WAIT_CNT_MASK						(BIT_MASK(28))

#define R32_COP1_ST3_CR_NOTIFY_SIZE				(0xF0 >> 2)
#define 	NOTIFY_SIZE_SHIFT						(0)
#define 	NOTIFY_SIZE_MASK						(BIT_MASK(10))

#define R32_COP1_ST3_CR_RW_RATIO				(0xF4 >> 2)
#define 	RW_RATIO_SHIFT							(0)
#define 	RW_RATIO_MASK							(BIT_MASK(8))

#define R16_COP1_ST3_CR_GC_NO_INSERT_VB_N		(0xFC >> 1)
#define 	CR_GC_NO_INSERT_VB_N_SHIFT				(0)
#define 	CR_GC_NO_INSERT_VB_N_MASK				(BIT_MASK(13))

#define R32_COP1_ST3_CR_GC_NO_INSERT_VB_G0		(0xFC >> 2)
#define 	CR_GC_NO_INSERT_VB0_SHIFT				(0)
#define 	CR_GC_NO_INSERT_VB0_MASK				(BIT_MASK(13))
#define 	CR_GC_NO_INSERT_VB1_SHIFT				(16)
#define 	CR_GC_NO_INSERT_VB1_MASK				(BIT_MASK(13))

#define R32_COP1_ST3_CR_GC_NO_INSERT_VB_G1		(0x100 >> 2)
#define 	CR_GC_NO_INSERT_VB2_SHIFT				(0)
#define 	CR_GC_NO_INSERT_VB2_MASK				(BIT_MASK(13))
#define 	CR_GC_NO_INSERT_VB3_SHIFT				(16)
#define 	CR_GC_NO_INSERT_VB3_MASK				(BIT_MASK(13))

#define R32_COP1_ST3_CR_GC_NO_INSERT_VB_G2		(0x104 >> 2)
#define 	CR_GC_NO_INSERT_VB4_SHIFT				(0)
#define 	CR_GC_NO_INSERT_VB4_MASK				(BIT_MASK(13))
#define 	CR_GC_NO_INSERT_VB5_SHIFT				(16)
#define 	CR_GC_NO_INSERT_VB5_MASK				(BIT_MASK(13))

#define R32_COP1_ST3_CR_GC_NO_INSERT_VB_G3		(0x108 >> 2)
#define 	CR_GC_NO_INSERT_VB6_SHIFT				(0)
#define 	CR_GC_NO_INSERT_VB6_MASK				(BIT_MASK(13))
#define 	CR_GC_NO_INSERT_VB7_SHIFT				(16)
#define 	CR_GC_NO_INSERT_VB7_MASK				(BIT_MASK(13))

#define R32_COP1_ST3_CR_GCSA_PMDLOG_BADR_1		(0x10C >> 2)
#define 	CR_GCSA_PMDLOG_BADR_1_SHIFT				(0)
#define 	CR_GCSA_PMDLOG_BADR_1_MASK				(BIT_MASK(22))

#define R32_COP1_ST3_CR_GCSA_SIZE_1				(0x110 >> 2)
#define 	CR_GCSA_SIZE_1_SHIFT						(0)
#define 	CR_GCSA_SIZE_1_MASK						(BIT_MASK(14))

#define R32_COP1_ST3_CR_PMD_TBL_BITMAP_16		(0x120 >> 2)

#define R32_COP1_ST3_CR_PMD_TBL_BITMAP_17		(0x124 >> 2)

#define R32_COP1_ST3_CR_PMD_TBL_BITMAP_18		(0x128 >> 2)

#define R32_COP1_ST3_CR_PMD_TBL_BITMAP_19		(0x12C >> 2)

#define R32_COP1_ST3_CR_PMD_TBL_BITMAP_20		(0x130 >> 2)

#define R32_COP1_ST3_CR_PMD_TBL_BITMAP_21		(0x134 >> 2)

#define R32_COP1_ST3_CR_PMD_TBL_BITMAP_22		(0x138 >> 2)

#define R32_COP1_ST3_CR_PMD_TBL_BITMAP_23		(0x13C >> 2)

#define R32_COP1_ST3_CR_PMD_TBL_BITMAP_24		(0x140 >> 2)

#define R32_COP1_ST3_CR_PMD_TBL_BITMAP_25		(0x144 >> 2)

#define R32_COP1_ST3_CR_PMD_TBL_BITMAP_26		(0x148 >> 2)

#define R32_COP1_ST3_CR_PMD_TBL_BITMAP_27		(0x14C >> 2)

#define R32_COP1_ST3_CR_PMD_TBL_BITMAP_28		(0x150 >> 2)

#define R32_COP1_ST3_CR_PMD_TBL_BITMAP_29		(0x154 >> 2)

#define R32_COP1_ST3_CR_PMD_TBL_BITMAP_30		(0x158 >> 2)

#define R32_COP1_ST3_CR_PMD_TBL_BITMAP_31		(0x15C >> 2)
/*=============	ST3C ============= */
#define R32_COP1_ST3C_CACHE_SETTING0			(0x00 >> 2)
#define 	CR_MODE_SHIFT							(0)
#define 	CR_MODE_MASK							(BIT_MASK(1))
#define 	CR_KICK_NODE_EN_SHIFT					(1)
#define 	CR_KICK_NODE_EN_MASK					(BIT_MASK(1))
#define 	CR_MON_EN_SHIFT							(2)
#define 	CR_MON_EN_MASK							(BIT_MASK(1))
#define 	CR_SW_RST_SHIFT							(3)
#define 	CR_SW_RST_MASK							(BIT_MASK(1))
#define 	CR_NON_NORMAL_PTE_PCA_SHIFT				(4)
#define 	CR_NON_NORMAL_PTE_PCA_MASK				(BIT_MASK(2))
#define 	CR_CHG_MIN_SIZE_SHIFT                   (9)
#define 	CR_CHG_MIN_SIZE_MASK                    (BIT_MASK(1))
#define 	CR_CHG_THRESHOLD_SHIFT                  (10)
#define 	CR_CHG_THRESHOLD_MASK                   (BIT_MASK(1))
#define 	CR_DYNAMIC_CHG_SHIFT                    (11)
#define 	CR_DYNAMIC_CHG_MASK                     (BIT_MASK(1))
#define 	CR_CMDTBL0_SIZE_SHIFT					(16)
#define 	CR_CMDTBL0_SIZE_MASK					(BIT_MASK(6))
#define 	CR_CMDTBL1_SIZE_SHIFT					(24)
#define 	CR_CMDTBL1_SIZE_MASK					(BIT_MASK(6))

#define R32_COP1_ST3C_CACHE_SETTING1			(0x04 >> 2)
#define 	CR_C_CNT_SHIFT							(0)
#define 	CR_C_CNT_MASK							(BIT_MASK(20))//E17_porting_4TB

#define R32_COP1_ST3C_CACHE_SETTING2			(0x08 >> 2)
#define 	CR_JUNIOR_MIN_SIZE_SHIFT				(0)
#define 	CR_JUNIOR_MIN_SIZE_MASK					(BIT_MASK(9))

#define R32_COP1_ST3C_CACHE_SETTING3			(0x0C >> 2)
#define 	CR_MISS_CNT_THRESHOLD_SHIFT				(0)
#define 	CR_MISS_CNT_THRESHOLD_MASK				(BIT_MASK(24))

#define R32_COP1_ST3C_INVALID_CID				(0x10 >> 2)
#define 	CR_INVALID_CID_SHIFT					(0)
#define 	CR_INVALID_CID_MASK						(BIT_MASK(21))//E17_porting_4TB

#define R32_COP1_ST3C_PTE_INVALID_INDEX			(0x14 >> 2)
#define 	CR_INVALID_IDX_SHIFT					(0)
#define 	CR_INVALID_IDX_MASK						(BIT_MASK(21))//E17_porting_4TB

#define R32_COP1_ST3C_CIDTBL_ST_ADDR			(0x18 >> 2)
#define 	CR_CIDTBL_ST_ADDR_SHIFT					(0)
#define CR_CIDTBL_ST_ADDR_MASK                   (BIT_MASK(31))//(BIT_MASK(29))

#define R32_COP1_ST3C_CTBL_ST_ADDR				(0x1C >> 2)
#define 	CR_CTBL_ST_ADDR_SHIFT					(0)
#define 	CR_CTBL_ST_ADDR_MASK					(BIT_MASK(31))//(BIT_MASK(29))

#define R32_COP1_ST3C_PTE_C_ST_ADDR     		(0x20 >> 2)
#define 	CR_PTE_C_ST_ADDR_SHIFT					(0)
#define 	CR_PTE_C_ST_ADDR_MASK					(BIT_MASK(31))//(BIT_MASK(29))

#define R32_COP1_ST3C_DT_ST_ADDR        		(0x24 >> 2)
#define 	CR_DT_ST_ADDR_SHIFT						(0)
#define 	CR_DT_ST_ADDR_MASK						(BIT_MASK(29))

#define R32_COP1_ST3C_DT_END_ADDR       		(0x28 >> 2)
#define 	CR_DT_END_ADDR_SHIFT					(0)
#define 	CR_DT_END_ADDR_MASK						(BIT_MASK(29))

#define R32_COP1_ST3C_GCPTE_ST_ADDR     		(0x2C >> 2)
#define 	CR_GCPTE_ST_ADDR_SHIFT					(0)
#define 	CR_GCPTE_ST_ADDR_MASK					(BIT_MASK(29))

#define R32_COP1_ST3C_GCPTE_END_ADDR    		(0x30 >> 2)
#define 	CR_GCPTE_END_ADDR_SHIFT					(0)
#define 	CR_GCPTE_END_ADDR_MASK					(BIT_MASK(29))

#define R32_COP1_ST3C_FW_ST_ADDR				(0x34 >> 2)
#define 	CR_FW_ST_ADDR_SHIFT						(0)
#define 	CR_FW_ST_ADDR_MASK						(BIT_MASK(29))

#define R32_COP1_ST3C_FW_END_ADDR				(0x38 >> 2)
#define 	CR_FW_END_ADDR_SHIFT					(0)
#define 	CR_FW_END_ADDR_MASK						(BIT_MASK(29))

#define R32_COP1_ST3C_COP0_ATTR0         		(0x3C >> 2)
#define 	CR_COP0_ATTRIBUTE_0_SHIFT				(0)
#define 	CR_COP0_ATTRIBUTE_0_MASK				(BIT_MASK(20))
#define 	CR_COP0_UEN_SHIFT						(24)
#define 	CR_COP0_UEN_MASK						(BIT_MASK(1))
#define 	CR_COP0_CFG_SHIFT						(25)
#define 	CR_COP0_CFG_MASK						(BIT_MASK(4))

#define R32_COP1_ST3C_COP0_ATTR1         		(0x40 >> 2)
#define 	CR_COP0_ATTRIBUTE_1_SHIFT				(0)
#define 	CR_COP0_ATTRIBUTE_1_MASK				(BIT_MASK(20))

#define R32_COP1_ST3C_COP0_ATTR2         		(0x44 >> 2)
#define 	CR_COP0_ATTRIBUTE_2_SHIFT				(0)
#define 	CR_COP0_ATTRIBUTE_2_MASK				(BIT_MASK(20))

#define R32_COP1_ST3C_COP0_ATTR3         		(0x48 >> 2)
#define 	CR_COP0_ATTRIBUTE_3_SHIFT				(0)
#define 	CR_COP0_ATTRIBUTE_3_MASK				(BIT_MASK(20))

#define R32_ST3C_COP0_USER_DEFINE   			(0x4C >> 2)

#define R32_COP1_ST3C_CPU_DB_SEL        		(0x50 >> 2)
#define 	CR_DBSEL_SHIFT							(0)
#define 	CR_DBSEL_MASK							(BIT_MASK(6))
#define     CR_DEBUG_MODE_BIT                       (BIT6)
#define     CR_DEBUG_STEP_BIT                       (BIT7)
#define     CR_CACHE_RST_BIT                        (BIT8)
#define 	CR_CACHE_EN_SHIFT						(16)
#define 	CR_CACHE_EN_MASK						(BIT_MASK(1))
#define 	CR_BYPASS_EN_SHIFT						(24)
#define 	CR_BYPASS_EN_MASK						(BIT_MASK(7))

#define R32_COP1_ST3C_SPARE_MASK_PMD			(0x54 >> 2)

#define R32_COP1_ST3C_SPARE_MASK_PTE			(0x58 >> 2)

#define R32_COP1_ST3C_SPARE_PMD					(0x5C >> 2)

#define R32_COP1_ST3C_SPARE_PTE					(0x60 >> 2)

#define R32_COP1_ST3C_ERR_EN                      (0x64 >> 2)
#define 	CR_ERR_EN_SHIFT							(0)
#define CR_ERR_EN_MASK                                (BIT_MASK(31))

#define R32_COP1_ST3C_CTBL_WPTR_UPEN          (0x68 >> 2)
#define 	CR_WPTR_FW_SHIFT						(0)
#define 	CR_WPTR_FW_MASK							(BIT_MASK(20))//E17_porting_4TB
#define 	CR_WPTR_FW_EN_SHIFT						(24)
#define 	CR_WPTR_FW_EN_MASK						(BIT_MASK(1))

#define R32_COP1_ST3C_FREE_CID_CFG                 (0x6C >> 2)
#define 	CR_FULL_CMD_SHIFT						(0)
#define 	CR_FULL_CMD_MASK						(BIT_MASK(6))
#define  MODE_COW_EN                                   (BIT0)
#define  MODE_RELEASE_LEAF_EN                     (BIT4)
#define  MODE_PREFETCH_CID_EN                     (BIT5)

#define R32_COP1_ST3C_FREE_CID_SIZE           (0x70 >> 2)
#define 	FREE_CID_SIZE_SHIFT						(0)
#define 	FREE_CID_SIZE_MASK   					(BIT_MASK(31))

#define R32_COP1_ST3C_FREE_CID_BASE               (0x74 >> 2)
#define 	FREE_CID_BASE_SHIFT						(0)
#define 	FREE_CID_BASE_MASK						(BIT_MASK(32))//E17_porting_4TB

#define R32_COP1_ST3C_CPU_DB_PORT_L           (0x78 >> 2)
#define     ALLOCATE_STATUS_SHIFT               (14)
#define     ALLOCATE_STATUS_MASK               (BIT_MASK(4))
#define     AXI_WRITE_CMD_BUSY_BIT           (BIT27)
#define     AXI_WRITE_CMD_BUSY_SHIFT           (27)

#define R32_COP1_ST3C_CPU_DB_PORT_H           (0x7C >> 2)
#define     CACHE_TABLE_STATUS_SHIFT            (4)
#define     CACHE_TABLE_STATUS_MASK            (BIT_MASK(3))

#define R32_COP1_ST3C_IDLE                      (0x80 >> 2)
#define 	SR_IDLE_SHIFT							(0)
#define 	SR_IDLE_MASK							(BIT_MASK(1))
#define     SR_ST3C_IDLE                            (SR_IDLE_MASK << SR_IDLE_SHIFT)
#define     AXIW_IDLE_BIT                           (BIT2)
#define     AXIW_IDLE_SHIFT                         (2)

#define R32_COP1_ST3C_FREE_CID_LEVEL          (0x84 >> 2)

#define R32_COP1_ST3C_ST3_CMD_CNT                 (0x88 >> 2)

#define R32_COP1_ST3C_ST3_MISS_CNT                (0x8C >> 2)

#define R32_COP1_ST3C_LINK_LIST_SIZE_0            (0x90 >> 2)
#define 	EMPTY_SIZE_SHIFT						(0)
#define 	EMPTY_SIZE_MASK							(BIT_MASK(9))
#define 	FREE_SIZE_SHIFT							(9)
#define 	FREE_SIZE_MASK							(BIT_MASK(9))
#define 	JUNIOR_SIZE_SHIFT						(18)
#define 	JUNIOR_SIZE_MASK						(BIT_MASK(9))

#define R32_COP1_ST3C_LINK_LIST_SIZE_1            (0x94 >> 2)
#define 	SENIOR_SIZE_SHIFT						(0)
#define 	SENIOR_SIZE_MASK						(BIT_MASK(9))
#define 	FW_SIZE_SHIFT							(9)
#define 	FW_SIZE_MASK							(BIT_MASK(9))

#define R32_COP1_ST3C_CMDT_VAL_SIZE               (0x98 >> 2)

#define R32_COP1_ST3C_ERR_ST                        (0x9C >> 2)
#define CR_ERR_SHIFT                             (0)
#define CR_ERR_MASK                                   (BIT_MASK(32))//E17_porting_4TB
#define ST3C_ERR_ST_AXI_ERROR_UPDATE_INVALID_INDEX_TO_CTBL		(BIT20)
#define ST3C_ERR_ST_AXI_ERROR_READ_WRITE_CIDTBL					(BIT21)
#define ST3C_ERR_ST_AXI_ERROR_READ_CTBL_OR_CID_OVERFLOW			(BIT22)
#define ST3C_ERR_ST_AXI_ERROR_FREE_CID_RW_CACHE_ALLOCATE		(BIT23)
#define ST3C_ERR_ST_AXI_ERROR_FREE_CID_RW_RELEASE_LEAF			(BIT24)
#define ST3C_ERR_ST_CID_OVERFLOW								(BIT25)
#define ST3C_ERR_ST_CACHE_FULL_ERROR							(BIT26)
#define ST3C_ERR_ST_DMAC_RSP_ERROR								(BIT27)
#define ST3C_ERR_ST_READ_CTBL_ERROR_RELEASE_LEAF				(BIT28)
#define ST3C_ERR_ST_FREE_CID_POOL_WRITE_ERROR					(BIT29)
#define ST3C_ERR_ST_CTBL_SCAN_DYNAMIC_REG_CHANGE_ERROR			(BIT30)

#define R32_COP1_ST3C_SENIOR_TAIL_MISS_CNT        (0xa0 >> 2)
#define CR_SENIOR_TAIL_MISS_CNT_SHIFT        (0)
#define CR_SENIOR_TAIL_MISS_CNT_MASK         (BIT_MASK(32))//E17_porting_4TB

#define R32_COP1_ST3C_CTBL_ALLOC_PTR          (0xa4 >> 2)
#define CR_CTBL_ALLOC_PTR_SHIFT         (0)
#define CR_CTBL_ALLOC_PTR_MASK     (BIT_MASK(20))//E17_porting_4TB

/*=============	CMDINTF ============= */
#define R32_COP1_CMDINT_INT_EN           (0x0C >> 2)
#define CR_INT_EN_SHIFT                          (0)
#define CR_INT_EN_MASK                           (BIT_MASK(17))//E17_porting_4TB
#define  INT_EN_ST1                                   (BIT0)
#define  INT_EN_ST3                                    (BIT1)
#define  INT_EN_ST3C                                  (BIT2)
#define  INT_EN_GC                                     (BIT3)
#define  INT_EN_SH                                     (BIT4)
#define  INT_EN_BRG                                   (BIT5)
#define  INT_EB_PE						(BIT16)

#define R32_COP1_CMDINT_IDLE			(0x14 >> 2)
#define COP1_CMDINT_IDLE				(BIT0)//E17
#define COP1_CMDINT_FROM_MR_IDLE		(BIT1)
#define COP1_CMDINT_TO_MR_IDLE			(BIT2)//E17

#define R32_COP1_CMDINT_POP_EMPTY_INFO	(0x20 >> 2)
#define CMDINT_POP_EMPTY_INFO_SHIFT                          (0)
#define CMDINT_POP_EMPTY_INFO_MASK                           (BIT_MASK(12))

#define  CMDINT_POP_EMPTY_INFO_ST1_CPU	(BIT0)
#define  CMDINT_POP_EMPTY_INFO_ST3_CPU	(BIT1)
#define  CMDINT_POP_EMPTY_INFO_ST3C_CPU	(BIT2)
#define  CMDINT_POP_EMPTY_INFO_GC_CPU	(BIT3)
#define  CMDINT_POP_EMPTY_INFO_SH_CPU	(BIT4)
#define  CMDINT_POP_EMPTY_INFO_DBG_CPU	(BIT5)

#define R32_COP1_CMDINT_ST1_CPU_DATA	(0x28 >> 2)
#define R32_COP1_CMDINT_ST3_CPU_DATA	(0x30 >> 2)
#define R32_COP1_CMDINT_ST3C_CPU_DATA	(0x38 >> 2)
#define R32_COP1_CMDINT_GC_CPU_DATA	(0x40 >> 2)
#define R32_COP1_CMDINT_SH_CPU_DATA	(0x48 >> 2)

/*=============	BRG ============= */
#define R32_COP1_BRG_COMMON						(0x00 >> 2)
#define R32_COP1_BRG_VC_ST_ADR					(0x04 >> 2)
#define R32_COP1_BRG_VC_REQ_CNT					(0x08 >> 2)
#define R32_COP1_BRG_VC_END_ADR					(0x0C >> 2)
#define R32_COP1_BRG_BRG_ILDE					(0x10 >> 2)
#define 	BRG_IDLE_SHIFT							(0)
#define 	BRG_IDLE_MASK							(BIT_MASK(1))
#define     SR_BRG_IDLE                             (BRG_IDLE_MASK << BRG_IDLE_SHIFT)
#define R32_COP1_BRG_BRG_PTR					(0x14 >> 2)


#endif /* _S17_COP1_REG_H_ */