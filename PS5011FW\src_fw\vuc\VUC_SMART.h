#ifndef _VUC_SMART_H_
#define _VUC_SMART_H_

#define PHY_ERROR_THRESHOLD 				50
#define SMART_FLASH_TYPE_SLC				1
#define SMART_FLASH_TYPE_MLC				2
#define SMART_FLASH_TYPE_3D_MLC				4
#define SMART_FLASH_TYPE_3D_TLC				5
#if (USB == HOST_MODE)
#define SMART_FLASH_TYPE_QLC				6
#endif /* (USB == HOST_MODE) */
#define SMART_SSD_PROTECT_MODE_RW			0
#define SMART_SSD_PROTECT_MODE_READ_ONLY	3
#define VUC_SMART_SET_VALUE_NON_CHANGE		(0xEEEEEEEEEEEEEEEE)
#define VUC_ONE_HOUR_TO_SECONDS				(3600)
#define VUC_ONE_MINUTE_TO_MILLISECOND		(60000)

/* Vendor Get SMART*/
//SMART Attribute Size
#define SMART_FLASH_UNC_ERROR_CNT			(0x0100)
#define 	SMART_FLASH_UNC_ERROR_CNT_SIZE			(8)

#define SMART_HOST_UNC_ERROR_CNT			(0x0103)
#define 	SMART_HOST_UNC_ERROR_CNT_SIZE			(4)

#define SMART_DATA_E3D_ERROR				(0x0104)
#define 	SMART_DATA_E3D_ERROR_SIZE				(4)

#define SMART_PHY_ERROR_CNT				(0x0107)
#define 	SMART_PHY_ERROR_CNT_SIZE				(4)

#define SMART_CRC_ERROR_CNT				(0x0108)
#define 	SMART_CRC_ERROR_CNT_SIZE				(4)

#define SMART_D3_REMAIN_PERCENT			(0x0201)
#define 	SMART_D3_REMAIN_PERCENT_SIZE		(8)

#define SMART_D3_USED_PERCENT			(0x0202)
#define 	SMART_D3_USED_PERCENT_SIZE			(8)

#define SMART_NAND_ERASE_SECTOR			(0x0204)
#define 	SMART_NAND_ERASE_SECTOR_SIZE		(8)

#define SMART_TOTAL_ERASE_CNT			(0x0205)
#define 	SMART_TOTAL_ERASE_CNT_SIZE			(8)

#define SMART_TOTAL_D1_ERASE_CNT			(0x0206)
#define 	SMART_TOTAL_D1_ERASE_CNT_SIZE	    (8)

#define SMART_TOTAL_D2D3_ERASE_CNT		(0x0207)
#define 	SMART_TOTAL_D2D3_ERASE_CNT_SIZE		(8)

#define SMART_D1_MAX_ERASE_CNT		    (0x0208)
#define 	SMART_D1_MAX_ERASE_CNT_SIZE	    	(4)

#define SMART_D2D3_MAX_ERASE_CNT		(0x0209)
#define 	SMART_D2D3_MAX_ERASE_CNT_SIZE		(4)

#define SMART_D1_AVG_ERASE_CNT		    (0x020A)
#define 	SMART_D1_AVG_ERASE_CNT_SIZE	    	(4)

#define SMART_D2D3_AVG_ERASE_CNT		(0x020B)
#define 	SMART_D2D3_AVG_ERASE_CNT_SIZE		(4)

#define SMART_D2D3_MIN_ERASE_CNT		(0x020C)
#define 	SMART_D2D3_MIN_ERASE_CNT_SIZE		(4)

#if (USB == HOST_MODE)
#define SMART_D1_MIN_ERASE_CNT			(0x020D)
#define 	SMART_D1_MIN_ERASE_CNT_SIZE			(4)
#endif /* (USB == HOST_MODE) */

#define SMART_D3_USED_PERCENT_WITH_PREVIOUS_EC	(0x020E)
#define 	SMART_D3_USED_PERCENT_WITH_PREVIOUS_EC_SIZE		(1)

#define SMART_D3_REMAIN_PERCENT_WITH_PREVIOUS_EC	(0x020F)
#define 	SMART_D3_REMAIN_PERCENT_SIZE_WITH_PREVIOUS_EC	(1)

#define SMART_D1_USED_PERCENT			(0x0210)
#define 	SMART_D1_USED_PERCENT_SIZE			(1)

#define SMART_D1_REMAIN_PERCENT			(0x0211)
#define 	SMART_D1_REMAIN_PERCENT_SIZE		(1)

#define SMART_D1_USED_PERCENT_WITH_PREVIOUS_EC	(0x0212)
#define 	SMART_D1_USED_PERCENT_WITH_PREVIOUS_EC_SIZE		(1)

#define SMART_D1_REMAIN_PERCENT_WITH_PREVIOUS_EC	(0x0213)
#define 	SMART_D1_REMAIN_PERCENT_SIZE_WITH_PREVIOUS_EC	(1)

#define SMART_PREVIOUS_AVG_ERASE_CNT	(0x0220)
#define 	SMART_PREVIOUS_AVG_ERASE_CNT_SIZE	(2)

#define SMART_D1_PREVIOUS_AVG_ERASE_CNT	(0x0221)
#define 	SMART_D1_PREVIOUS_AVG_ERASE_CNT_SIZE	(2)

#define SMART_TOTAL_BADBLOCK_CNT		(0x0300)
#define 	SMART_TOTAL_BADBLOCK_CNT_SIZE		(4)

#define SMART_EARLY_BADBLOCK_CNT		(0x0301)
#define 	SMART_EARLY_BADBLOCK_CNT_SIZE		(4)

#define SMART_LATER_BADBLOCK_CNT		(0x0302)
#define 	SMART_LATER_BADBLOCK_CNT_SIZE		(4)

#define SMART_EARLY_AND_LATER_BADBLOCK_CNT			(0x0303)
#define 	SMART_EARLY_AND_LATER_BADBLOCK_CNT_SIZE			(8)
#define 	SMART_EARLY_AND_LATER_BADBLOCK_LATERBAD_SHIFT	(32)

#define SMART_CURRENT_PERCENT_SPARE_D3_CONSUME		(0x0304)
#define 	SMART_CURRENT_PERCENT_SPARE_D3_CONSUME_SIZE		(8)

#define SMART_CURRENT_PERCENT_SPARE_D3_REMAINING	(0x0305)
#define 	SMART_CURRENT_PERCENT_SPARE_D3_REMAINING_SIZE	(8)

#if (USB == HOST_MODE)
#define SMART_HOST_WRITE            	(0x0400)
#define 	SMART_HOST_WRITE_SIZE				(8)
#endif /* (USB == HOST_MODE) */

#define SMART_NAND_WRITE				(0x0410)
#define 	SMART_NAND_WRITE_SIZE				(8)

#if (USB == HOST_MODE)
#define SMART_HOST_READ             	(0x0420)
#define 	SMART_HOST_READ_SIZE				(8)
#endif /* (USB == HOST_MODE) */

#define SMART_NAND_READ					(0x0430)
#define 	SMART_NAND_READ_SIZE				(8)

#define SMART_READ_FAIL_CNT				(0x0500)
#define 	SMART_READ_FAIL_CNT_SIZE			(4)

#define SMART_PROGRAM_FAIL_CNT			(0x0501)
#define 	SMART_PROGRAM_FAIL_CNT_SIZE			(4)

#define SMART_ERASE_FAIL_CNT			(0x0502)
#define 	SMART_ERASE_FAIL_CNT_SIZE			(4)

#if (USB == HOST_MODE)
#define SMART_POWERON_HOURS				(0x0600)
#define 	SMART_POWERON_HOURS_SIZE			(8)

#define SMART_POWER_CYCLE				(0x0607)
#define 	SMART_POWER_CYCLE_SIZE				(8)

#define SMART_UNEXPECTER_POWER_LOSS_CNT				(0x0608)
#define 	SMART_UNEXPECTER_POWER_LOSS_CNT_SIZE			(8)
#endif /* (USB == HOST_MODE) */

#define SMART_DEVICE_CAPACITY			(0x0700)
#define 	SMART_DEVICE_CAPACITY_SIZE			(8)

#define SMART_USER_CAPACITY				(0x0701)
#define 	SMART_USER_CAPACITY_SIZE			(8)

#define SMART_FW_UPDATE_CNT				(0x0704)
#define 	SMART_FW_UPDATE_CNT_SIZE			(8)

#define SMART_SYSTEM_TABLE_COPY_CNT			(0x0706)
#define 	SMART_SYSTEM_TABLE_COPY_CNT_SIZE			(8)

#define SMART_READMOVE_TABLE_COPY_CNT		(0x0707)
#define 	SMART_READMOVE_TABLE_COPY_CNT_SIZE	(8)

#define SMART_WP_WATER_MARK				(0x0709)
#define 	SMART_WP_WATER_MARK_SIZE			(1)

#define SMART_WEAR_LEVEL_CNT			(0x070B)
#define 	SMART_WEAR_LEVEL_CNT_SIZE			(4)

#define SMART_ALREADY_ALLOCATED_LBA		(0x070C)
#define 	SMART_ALREADY_ALLOCATED_LBA_SIZE	(4)

#define SMART_NAND_PE_CYCLE				(0x0710)
#define 	SMART_NAND_PE_CYCLE_SIZE			(4)

#define SMART_FLASH_TYPE				(0x0711)
#define 	SMART_FLASH_TYPE_SIZE				(2)

#define SMART_SSD_PROTECT_MODE			(0x0712)
#define 	SMART_SSD_PROTECT_MODE_SIZE			(2)

#define SMART_BACKGROUND_READ_CNT		(0x0713)
#define 	SMART_BACKGROUND_READ_CNT_SIZE		(8)

#define SMART_SPARE_BLK_AVAILABLE		(0x0717)
#define 	SMART_SPARE_BLK_AVAILABLE_SIZE		(2)

#define SMART_REMAIN_SPARE_BLK			(0x0718)
#define 	SMART_REMAIN_SPARE_BLK_SIZE			(2)

#define SMART_GOOD_CODE_BLK_COUNT			(0x0723)
#define 	SMART_GOOD_CODE_BLK_COUNT_SIZE		(1)

#define SMART_TEMPERATURE_CURRENT_LOWEST_HIGHEST	(0x0800)
#define 	SMART_TEMPERATURE_CURRENT_LOWEST_HIGHEST_SIZE		(8)
#define 	SMART_TEMPERATURE_LOWEST_SHIFT	(16)
#define 	SMART_TEMPERATURE_HIGHEST_SHIFT	(32)

#define SMART_HIGHEST_TEMPERATURE		(0x0802)
#define 	SMART_HIGHEST_TEMPERATURE_SIZE		(2)

#define SMART_CHIP_IN_TEMPERATURE		(0x0803)
#define 	SMART_CHIP_IN_TEMPERATURE_SIZE		(2)

#define SMART_THERMAL_THROTTLE			(0x0810)
#define 	SMART_THERMAL_THROTTLE_SIZE			(2)

#define SMART_THERMAL_THROTTLE_TIME		(0x0811)
#define 	SMART_THERMAL_THROTTLE_TIME_SIZE	(2)

#define SMART_HOST_WRITE_UNC_SECTOR_CNT			(0x0904)
#define 	SMART_HOST_WRITE_UNC_SECTOR_CNT_SIZE		(4)

#if (USB == HOST_MODE)
#define SMART_PS2_ENTER_SUCCESS			(0x090A)
#define 	SMART_PS2_ENTER_SUCCESS_SIZE   		(4)
#endif /* (USB == HOST_MODE) */

#define SMART_PS3_ENTER					(0x0910)
#define 	SMART_PS3_ENTER_SIZE				(4)

#define SMART_PS3_ENTER_FAIL			(0x0911)
#define 	SMART_PS3_ENTER_FAIL_SIZE			(4)

#define SMART_PS3_ENTER_SUCCESS			(0x0912)
#define 	SMART_PS3_ENTER_SUCCESS_SIZE		(4)

#define SMART_PS4_ENTER					(0x0913)
#define 	SMART_PS4_ENTER_SIZE				(4)

#define SMART_PS4_ENTER_FAIL			(0x0914)
#define 	SMART_PS4_ENTER_FAIL_SIZE			(4)

#define SMART_PS4_ENTER_SUCCESS			(0x0915)
#define 	SMART_PS4_ENTER_SUCCESS_SIZE		(4)

#define SMART_HB_RETRY_CNT			    (0xF010)
#define 	SMART_HB_RETRY_CNT_SIZE		        (4)

#define SMART_SB_RETRY_CNT			    (0xF011)
#define 	SMART_SB_RETRY_CNT_SIZE		        (4)

#if (USB == HOST_MODE)
#define SMART_U1_ENTER					(0x0916)
#define 	SMART_U1_ENTER_SIZE					(4)

#define SMART_U2_ENTER					(0x0917)
#define 	SMART_U2_ENTER_SIZE					(4)

#define SMART_U3_ENTER					(0x0918)
#define 	SMART_U3_ENTER_SIZE					(4)

#define SMART_RX_VAILD_LOSS_COUNT				(0x0920)//SR_LOSS_SYM_NUM
#define 	SMART_RX_VAILD_LOSS_COUNT_SIZE				(8)

#define SMART_WRONG_SYMBOL_COUNT				(0x0921)//SR_8B10B_ERR_NUM
#define 	SMART_WRONG_SYMBOL_COUNT_SIZE				(8)

#define SMART_ENTER_RECOVERY_COUNT				(0x0922)//SR_SS_LNK_ERR_NUM
#define 	SMART_ENTER_RECOVERY_COUNT_SIZE				(8)

#define SMART_SRAM_PARITY_ERROR_COUNT			(0x0923)
#define 	SMART_SRAM_PARITY_ERROR_COUNT_SIZE			(8)

#define SMART_SUPER_SPEED_PLUS_CNT				(0x0906)
#define 	SMART_SUPER_SPEED_PLUS_CNT_SIZE				(8)

#define SMART_SUPER_SPEED_CNT					(0x0907)
#define 	SMART_SUPER_SPEED_CNT_SIZE					(8)

#define SMART_HIGH_LOW_SPEED_CNT				(0x0908)
#define 	SMART_HIGH_LOW_SPEED_CNT_SIZE				(8)

#define SMART_FULL_SPEED_CNT					(0x0909)
#define 	SMART_FULL_SPEED_CNT_SIZE					(8)

#define SMART_TOTAL_D1              	(0x0E01)
#define 	SMART_TOTAL_D1_SIZE					(2)

#define SMART_TOTAL_D3              	(0x0E02)
#define 	SMART_TOTAL_D3_SIZE					(2)

#define SMART_TOTAL_BLOCK           	(0x0E03)
#define 	SMART_TOTAL_BLOCK_SIZE				(2)
#endif /* (USB == HOST_MODE) */

#define SMART_RAIDECC_RETRY_CNT			(0xF012)
#define 	SMART_RAIDECC_RETRY_CNT_SIZE		(4)

#define SMART_RAIDECC_FAIL_CNT			(0xF013)
#define 	SMART_RAIDECC_FAIL_CNT_SIZE			(4)

#define SMART_DATA_READRETRY_CNT		(0xF014)
#define 	SMART_DATA_READRETRY_CNT_SIZE		(4)

#define SMART_SMART_VERSION_30			(0xFE30)
#define 	SMART_SMART_VERSION_30_SIZE			(1)

#define SMART_SMART_VERSION_33			(0xFE33)
#define 	SMART_SMART_VERSION_33_SIZE			(1)

#define SMART_CONTROLLER_MODEL_50		(0xFE50)
#define 	SMART_CONTROLLER_MODEL_50_SIZE		(1)

#define SMART_CONTROLLER_MODEL_ED		(0xFEED)
#define 	SMART_CONTROLLER_MODEL_ED_SIZE		(1)

#define SMART_SET_FOR_APACER			(0xFE00)
#define 	SMART_SET_FOR_APACER_VALUE			(0xFF)

#define SMART_RESERVE_FOR_APACER		(0xFF00)
#define 	SMART_RESERVE_FOR_APACER_SIZE		(0xFF)

#if (USB == HOST_MODE)
#define SMART_FAKE_DATA					(0x0E00)
#define 	SMART_FAKE_DATA_SIZE  				(6)
#endif /* (USB == HOST_MODE) */

#define SMART_INVALID_ID					(0x0000)

#if (USB == HOST_MODE)
typedef struct {
	U8 ubID;         // 0
	U8 ubFlag[2];    // 1-2
	U8 ubValue;      // 3
	U8 ubWorst;      // 4
	U8 ubRaw[6];     // 5-10
	U8 ubThreshold;  // 12
} SMARTATTRIBUTE_t;

typedef struct {
	U8 rev[3];
	U8 Error;
	U8 count;
	U8 LBA_L;
	U8 LBA_M;
	U8 LBA_H;
	U8 Device;
	U8 Status;
	U8 ubReserved[2];
} ATA12_RETURN_STATUS;
#endif /* (USB == HOST_MODE) */

typedef struct {
	U64 uoPowerOnHour;
	U64 uoPowerCycleCnt;
	U64 uoTotalEC;
	U64 uoMaxEC;
	U64 uoAbnormalPowerCycleCnt;
	U64 uoFlashReadSectorCnt;
	U64 uoFlashWriteSectorCnt;
	U64 uoHostReadSectorCnt;
	U64 uoHostWriteSectorCnt;
	U64 uoAvailableSpare;
	U64 uoHostReadCmd;
	U64 uoHostWriteCmd;
	U64 uoTotalECBase;
	U64 uoControllerBusyTime;
	U64 uoTempLogSamplePeriod;
	U8	ubReserved[400];

} *VUC_SET_SMART_VALUE_PTR_t;

typedef struct nvme_dell_smart_log    DELL_SMART_LOG_T, *  DELL_SMART_LOG_PTR;

struct nvme_dell_smart_log {
	U8     ubReAssignedSecCnt;//B0
	U8     ubWorstCaseComponentProgramFail;//B1
	U8     ubSSDTotalProgramFail;//B2
	U8     ubWorstCaseComponentEraseFail;//B3
	U8     ubSSDTotalEraseFail;//B4
	U8     ubWearLevelingCount;//B5
	U8     ubWorstCaseComponentUsedReservedBlockCnt;//B6
	U8     ubSSDTotalUsedReservedBlockCnt;//B7
	U32    ulReservedBlockCount;//B11-B8
	U8     ubRsvd2[500];//B511-B12
};
LIB_SIZE_CHECK(DELL_SMART_LOG_T, SIZE_512B);
#if (USB == HOST_MODE)
AOM_NRW_3 void VUC_CleanSMART(PUSBCURRCMD_t pCurrentCMD);
AOM_NRW_2 void VUC_GetSMARTInfo(VUC_OPT_HCMD_PTR_t pCmd);
AOM_VUC_3 void VUCSetSMARTValue(VUC_OPT_HCMD_PTR_t pCmd);
#else /* (USB == HOST_MODE) */
AOM_VUC_4 void VUC_CleanSMART(VUC_OPT_HCMD_PTR_t pCmd);
AOM_VUC_4 void VUC_GetSMARTInfo(VUC_OPT_HCMD_PTR_t pCmd);
AOM_VUC_4 static void ReadWriteSMARTInfo(VUC_OPT_HCMD_PTR_t pCmd);
AOM_VUC_4 void VUCSetSMARTValue(VUC_OPT_HCMD_PTR_t pCmd);
#endif /* (USB == HOST_MODE) */
AOM_NRW_2 void VUCSetSMARTUpdateRatio(VUC_OPT_HCMD_PTR_t pCmd);
#if(HOST_MODE == NVME && DELL_EN)
AOM_VUC_3 void VUC_GetDellSMARTInfo(U32 ulPhyAddr, U8 ubFillLog);
#endif /*(HOST_MODE == NVME && DELL_EN)*/
AOM_NRW_RMALOG void VUCRefreshSMARTInfo(void);
#endif /* _VUC_SMART_H_ */
