#ifndef _VUC_SIDEBANDIOTEST_H
#define _VUC_SIDEBANDIOTEST_H

typedef struct gBackUp gBackUp_t;
struct gBackUp {
	U64 uoAll;
	U8 ublock;
	union {
		struct {
			U32 ulMux;
			U32 ulPadc;
			U8 ublock;
		} LED;
		struct {
			U32 ulPadc;
			U32 ulPcie;
			U8 ublock;
		} CLKREQ;
	};
};
/* define lock */
#define VUC_SIDEBANDIOTEST_BACKUP_LOCK_ENABLE (1)
#define VUC_SIDEBANDIOTEST_BACKUP_LOCK_DISABLE (0)

typedef void GetSetGpioStatus (VUC_OPT_HCMD_PTR_t pCmd, U8 ubGetSet);
/* Define buffer size */
#define VUC_SIDEBANDIOTEST_BUFFERSIZE (512)
/* Define Command */
#define VUC_SIDEBANDIOTEST_GET_DEVICE_SUPPORT_MODE (0x00)
#define VUC_SIDEBANDIOTEST_GET_GPIO_STATUS (0x01)
#define VUC_SIDEBANDIOTEST_SET_GPIO_STATUS (0x02)

/* Define Support Bit */
#define VUC_SIDEBANDIOTEST_SUPPORT_SWITCH_OUTPUT_MODE 	(BIT0)
#define VUC_SIDEBANDIOTEST_SUPPORT_OUTPUT_MODE_SET_HIGH (BIT1)
#define VUC_SIDEBANDIOTEST_SUPPORT_OUTPUT_MODE_SET_LOW 	(BIT2)
#define VUC_SIDEBANDIOTEST_SUPPORT_SWITCH_INPUT_MODE 	(BIT4)
#define VUC_SIDEBANDIOTEST_SUPPORT_INPUT_MODE_PULL_HIGH (BIT5)
#define VUC_SIDEBANDIOTEST_SUPPORT_INPUT_MODE_PULL_LOW 	(BIT6)

/* Define Table */
#define VUC_SIDEBANDIOTEST_TABLE_SIZE			(13)
#define VUC_SIDEBANDIOTEST_TABLE_ALL_SUPPORT ( BIT0 | BIT1 | BIT2 | BIT4 | BIT5 | BIT6)
#define VUC_SIDEBANDIOTEST_TABLE_NOT_SUPPORT (0)
/* LED (DAS) 0x02 */
#define VUC_SIDEBANDIOTEST_TABLE_LED \
(VUC_SIDEBANDIOTEST_SUPPORT_SWITCH_OUTPUT_MODE 		\
|VUC_SIDEBANDIOTEST_SUPPORT_OUTPUT_MODE_SET_LOW		\
|VUC_SIDEBANDIOTEST_SUPPORT_SWITCH_INPUT_MODE 		\
|VUC_SIDEBANDIOTEST_SUPPORT_INPUT_MODE_PULL_LOW		\
)
/* PERST 0x08 */
#define VUC_SIDEBANDIOTEST_TABLE_PERST (VUC_SIDEBANDIOTEST_TABLE_NOT_SUPPORT)

/* CLKREQ 0x09 */
#define	VUC_SIDEBANDIOTEST_TABLE_CLKREQ \
(VUC_SIDEBANDIOTEST_SUPPORT_SWITCH_OUTPUT_MODE \
|VUC_SIDEBANDIOTEST_SUPPORT_OUTPUT_MODE_SET_LOW	\
|VUC_SIDEBANDIOTEST_SUPPORT_SWITCH_INPUT_MODE \
)

/* not use*/
/*
define port id
#define VUC_SIDEBANDIOTEST_ID_PWRDIS 		(0x00) //gpio9 pwrgd ?
#define VUC_SIDEBANDIOTEST_ID_PLN		(0x01) //n
#define VUC_SIDEBANDIOTEST_ID_LED		(0x02) //n ==DAS == led_1(first)
#define VUC_SIDEBANDIOTEST_ID_WP_FIG		(0x03) //gpio6 write protect?
#define VUC_SIDEBANDIOTEST_ID_PLA_S3		(0x04) //n
#define VUC_SIDEBANDIOTEST_ID_SMB_CLK		(0x05) //gpio5
#define VUC_SIDEBANDIOTEST_ID_SMB_DATA		(0x06) //gpio4
#define VUC_SIDEBANDIOTEST_ID_PFAIL_DETECT	(0x07) //n
#define VUC_SIDEBANDIOTEST_ID_PERST		(0x08) //n (first)
#define VUC_SIDEBANDIOTEST_ID_CLKREQ		(0x09) //n (first)
#define VUC_SIDEBANDIOTEST_ID_UART_TX		(0x0A) //gpio3
#define VUC_SIDEBANDIOTEST_ID_UART_RX		(0x0B) //gpio0
#define VUC_SIDEBANDIOTEST_ID_ERASE		(0x0C) //gpio8
*/

/* SET_OUTPUT_MODE */
#define VUC_SIDEBANDIOTEST_SET_OUTPUT_MODE		(0)
#define VUC_SIDEBANDIOTEST_SET_OUTPUT_MODE_LOW		(0)
#define VUC_SIDEBANDIOTEST_SET_OUTPUT_MODE_HIGH		(1)

/* SET_INPUT_MODE */
#define VUC_SIDEBANDIOTEST_SET_INPUT_MODE		(1)
#define VUC_SIDEBANDIOTEST_SET_INPUT_MODE_PULL_LOW	(0)
#define VUC_SIDEBANDIOTEST_SET_INPUT_MODE_PULL_HIGH	(1)
/* Reset mode */
#define VUC_SIDEBANDIOTEST_RESET_MODE	(2)

/* Define LED IO */
#define VUC_SIDEBANDIOTEST_LED_INPUT		(0x00)
#define VUC_SIDEBANDIOTEST_LED_OUTPUT		(0x01)

/* Define CLKREQ IO */
#define VUC_SIDEBANDIOTEST_CLKREQ_INPUT		(0x00)
#define VUC_SIDEBANDIOTEST_CLKREQ_OUTPUT		(0x01)

/* define function */
void VUCSideBandIOTest(VUC_OPT_HCMD_PTR_t pCmd);
void VUCSideBandIOTestGetDeviceSupportMode(VUC_OPT_HCMD_PTR_t pCmd);
void VUCSideBandIOTestGetSetGpioStatus(VUC_OPT_HCMD_PTR_t pCmd, U8 ubGetSet);
void VUC_SIDEBANDIOTEST_ID_PWRDIS(VUC_OPT_HCMD_PTR_t pCmd, U8 ubGetSet);
void VUC_SIDEBANDIOTEST_ID_PLN (VUC_OPT_HCMD_PTR_t pCmd, U8 ubGetSet);
void VUC_SIDEBANDIOTEST_ID_LED (VUC_OPT_HCMD_PTR_t pCmd, U8 ubGetSet);
void VUC_SIDEBANDIOTEST_ID_WP_FIG		(VUC_OPT_HCMD_PTR_t pCmd, U8 ubGetSet) ;
void VUC_SIDEBANDIOTEST_ID_PLA_S3		(VUC_OPT_HCMD_PTR_t pCmd, U8 ubGetSet) ;
void VUC_SIDEBANDIOTEST_ID_SMB_CLK		(VUC_OPT_HCMD_PTR_t pCmd, U8 ubGetSet) ;
void VUC_SIDEBANDIOTEST_ID_SMB_DATA		(VUC_OPT_HCMD_PTR_t pCmd, U8 ubGetSet) ;
void VUC_SIDEBANDIOTEST_ID_PFAIL_DETECT	(VUC_OPT_HCMD_PTR_t pCmd, U8 ubGetSet) ;
void VUC_SIDEBANDIOTEST_ID_PERST		(VUC_OPT_HCMD_PTR_t pCmd, U8 ubGetSet) ;
void VUC_SIDEBANDIOTEST_ID_CLKREQ		(VUC_OPT_HCMD_PTR_t pCmd, U8 ubGetSet) ;
void VUC_SIDEBANDIOTEST_ID_UART_TX		(VUC_OPT_HCMD_PTR_t pCmd, U8 ubGetSet) ;
void VUC_SIDEBANDIOTEST_ID_UART_RX		(VUC_OPT_HCMD_PTR_t pCmd, U8 ubGetSet) ;
void VUC_SIDEBANDIOTEST_ID_ERASE		(VUC_OPT_HCMD_PTR_t pCmd, U8 ubGetSet) ;
#endif /* _VUC_SIDEBANDIOTEST_H */
