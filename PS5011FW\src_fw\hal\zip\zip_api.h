#ifndef _ZIP_API_H_
#define _ZIP_API_H_

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "setup.h"
#include "aom/aom_api.h"
#if VS_SIM_EN
#include "ip/zip/zip.h"
#endif /* VS_SIM_EN */

/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */
#define MAX_ZIP_RATE (4)

#define DZIP_REG_BASE                  (DZIP_REG_ADDRESS) // S17: 0xF80180D0

#define R8_DZIP			    ((volatile U8 *) DZIP_REG_BASE)
#define R16_DZIP			((volatile U16 *) DZIP_REG_BASE)
#define R32_DZIP			((volatile U32 *) DZIP_REG_BASE)

#define R32_DZIP_ZIP_CFG			(0x0 >> 2)
#define DZIP_RESOLUTION_MODE_SHIFT		(8)
#define DZIP_RESOLUTION_512B			(0)
#define DZIP_RESOLUTION_1KB				(1)
#define DZIP_RESOLUTION_2KB				(2)
#define DZIP_RESOLUTION_BYPASS			(3)
#define DZIP_RESOLUTION_MAX				(DZIP_RESOLUTION_BYPASS)
#define DZIP_CLR_RESOLUTION				(~(BIT8|BIT9))
#define DZIP_RESOLUTION_MODE_MASK       (BIT_MASK(2))


#define DZIP_DISABLE_INFO_BLOCK_BIT	(BIT7)  //Refer to PS5013 SystemInfo.xlsx

#define R32_DZIP_ZIP_ERR_HND				(0x4 >> 2)
#define		DZIP_WRITE_RAM_SWITCH_BIT	    (BIT0)
#if PS5017_EN
#define		DZIP_AXIS_TOUT_CNT_SHIFT 		(24)
#define		DZIP_AXIS_TOUT_CNT_MASK 		(BIT_MASK(4))
#define     DZIP_AXIS_TOUT_CNT_MAX          (DZIP_AXIS_TOUT_CNT_MASK)
#define     DZIP_CLR_AXIE_TOUT_CNT          (~(BIT24|BIT25|BIT26|BIT27))
#endif /* PS5017_EN */

/*
 * ---------------------------------------------------------------------------------------------------
 *  enum
 * ---------------------------------------------------------------------------------------------------
 */
enum ZInfoEnum {
	ZINFO_0 = 0,
	ZINFO_1,
	ZINFO_2,
	ZINFO_3,
	ZINFO_4,
	ZINFO_5,
	ZINFO_6,
	ZINFO_7,
	ZINFO_NUM = 8,
};

/*
 * ---------------------------------------------------------------------------------------------------
 *  data types
 * ---------------------------------------------------------------------------------------------------
 */
typedef struct {
	U32 ulLCA;
	U32 ubLogicalValidBMP: 8;
	U32 FWSet: 24;

	U64 E3D4K: 24;
	U64 : 40;
} ZipDataFormatInfo_t ;
TYPE_SIZE_CHECK(ZipDataFormatInfo_t, SIZE_16B);

typedef struct {
	U8 btDZIPEn		: 1;
	U8 btSetByInfoBlock	: 1;
	U8 Resolution		: 2;
	U8			: 4;
} DZIPSettingInfo_t;

/*
 * ---------------------------------------------------------------------------------------------------
 *   macros
 * ---------------------------------------------------------------------------------------------------
 */
#if PS5017_EN
/* APU receive ZIP timeout with default timeout count value. Set Timeout count to maximum to prevent ZIP timeout. */
#define M_DZIP_SET_TIMEOUT(VALUE) do { \
	R32_DZIP[R32_DZIP_ZIP_ERR_HND] &= DZIP_CLR_AXIE_TOUT_CNT; \
    R32_DZIP[R32_DZIP_ZIP_ERR_HND] |= (((VALUE) & DZIP_AXIS_TOUT_CNT_MASK) << DZIP_AXIS_TOUT_CNT_SHIFT); \
}while (0)
#endif /* PS5017_EN */
#define SEQTABLE_ZINFO                                 ((gPCAInfo.ubResolution == DZIP_RESOLUTION_2KB) ? ZINFO_3 : ZINFO_1)
#define M_DZIP_SET_RESOLUTION(RES) do { \
	R32_DZIP[R32_DZIP_ZIP_CFG] &= DZIP_CLR_RESOLUTION; \
	R32_DZIP[R32_DZIP_ZIP_CFG] |= (((RES) & DZIP_RESOLUTION_MODE_MASK) << DZIP_RESOLUTION_MODE_SHIFT); \
}while(0)

#define M_DZIP_SET_FW_CAN_ACCESS_DZIP_RAM()		(R32_DZIP[R32_DZIP_ZIP_ERR_HND] |= DZIP_WRITE_RAM_SWITCH_BIT)
/*
 * ---------------------------------------------------------------------------------------------------
 *   extern global variables
 * ---------------------------------------------------------------------------------------------------
 */
extern DZIPSettingInfo_t gDZIP;

/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */
AOM_INIT_2 void DZIPInit(void);
#endif /* _ZIP_API_H_ */
