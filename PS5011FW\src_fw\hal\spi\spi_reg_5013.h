#ifndef _E13_SPI_REG_H_
#define _E13_SPI_REG_H_

#include "mem.h"

#define	R8_SPI				((REG8 *) 	(SPI_REG_ADDRESS))
#define	R16_SPI				((REG16 *) 	(SPI_REG_ADDRESS))
#define	R32_SPI				((REG32 *) 	(SPI_REG_ADDRESS))
#define	R64_SPI				((REG64 *) 	(SPI_REG_ADDRESS))

#define R32_SPI_TRIG				(0x34 >> 2)
#define		SPI_DMA_TRIGGER_BIT			BIT0
#define		READ_DATA_LOCATION_BIT		BIT1
#define		BANKING_AUTO_ERASE_BIT		BIT8

#endif /* _E13_SPI_REG_H_ */
