#include "hal/pic/uart/uart_api.h"
#include "host/VUC_handler.h"
#include "nvme_api/nvme/shr_hal_nvme.h"
#include "vuc/VUC_SideBandIOTest.h"
#include "hal/sys/api/mux/mux_api.h"

#include "hal/sys/reg/sys_pd0_reg.h"
#include "hal/sys/api/padc/padc_api.h"

#include "hal/sys/api/led/led_api.h"
#include "hal/sys/api/padc/padc_api.h"
#include "nvme_api/pcie/shr_hal_pcie_reg.h"

#if (HOST_MODE == NVME && BURNER_MODE_EN)
gBackUp_t gIOBackUp;
void VUCSideBandIOTest(VUC_OPT_HCMD_PTR_t pCmd)
{
	M_UART(SIDEBAND_, "\n%s", __FUNCTION__);
	U8 ubGetSet;
	ubGetSet = pCmd->vuc_sqcmd.vendor.SideBandIOTest.ubSubFeature;
	if (VUC_SIDEBANDIOTEST_GET_DEVICE_SUPPORT_MODE == ubGetSet) {
		VUCSideBandIOTestGetDeviceSupportMode(pCmd);
	}
	else {
		VUCSideBandIOTestGetSetGpioStatus(pCmd, ubGetSet);
	}
}

void VUCSideBandIOTestGetDeviceSupportMode(VUC_OPT_HCMD_PTR_t pCmd)
{
	memset((void *)pCmd->ulCurrentPhysicalMemoryAddr, 0, VUC_SIDEBANDIOTEST_BUFFERSIZE);
	M_UART(SIDEBAND_, "\n%s", __FUNCTION__);
	U8 *ubReturnBuf = (U8 *)gulVUCBufAddr;
	U8 ubSupportTable[] = {
		VUC_SIDEBANDIOTEST_TABLE_NOT_SUPPORT,	/* VUC_SIDEBANDIOTEST_ID_PWRDIS */
		VUC_SIDEBANDIOTEST_TABLE_NOT_SUPPORT,	/* VUC_SIDEBANDIOTEST_ID_PLN		*/
		VUC_SIDEBANDIOTEST_TABLE_LED,		/* VUC_SIDEBANDIOTEST_ID_LED(DAS)	*/
		VUC_SIDEBANDIOTEST_TABLE_NOT_SUPPORT,	/* VUC_SIDEBANDIOTEST_ID_WP_FIG		*/
		VUC_SIDEBANDIOTEST_TABLE_NOT_SUPPORT,		/* VUC_SIDEBANDIOTEST_ID_PLA_S3		*/
		VUC_SIDEBANDIOTEST_TABLE_NOT_SUPPORT,	/* VUC_SIDEBANDIOTEST_ID_SMB_CLK		*/
		VUC_SIDEBANDIOTEST_TABLE_NOT_SUPPORT,		/* VUC_SIDEBANDIOTEST_ID_SMB_DATA		*/
		VUC_SIDEBANDIOTEST_TABLE_NOT_SUPPORT,	/* VUC_SIDEBANDIOTEST_ID_PFAIL_DETECT	*/
		VUC_SIDEBANDIOTEST_TABLE_PERST	,/* VUC_SIDEBANDIOTEST_ID_PERST		*/
		VUC_SIDEBANDIOTEST_TABLE_CLKREQ		,/* VUC_SIDEBANDIOTEST_ID_CLKREQ		*/
		VUC_SIDEBANDIOTEST_TABLE_NOT_SUPPORT	,	/* VUC_SIDEBANDIOTEST_ID_UART_TX		*/
		VUC_SIDEBANDIOTEST_TABLE_NOT_SUPPORT,		/* VUC_SIDEBANDIOTEST_ID_UART_RX		*/
		VUC_SIDEBANDIOTEST_TABLE_NOT_SUPPORT		/* VUC_SIDEBANDIOTEST_ID_ERASE		*/
	};
	memcpy((void *) ubReturnBuf, (void *)ubSupportTable, VUC_SIDEBANDIOTEST_TABLE_SIZE);  // back up bootloader section info which is inherrited from bootcode
}

/*
 * VUCSideBandIOTestGetGpioStatus
 * define return byte 0
 * 0 -> output mode
 * 1 -> input mode
 * define return byte 1
 * if byte 0 == outputmode return output status
 * if byte 0 == inputmode return input value
 */

/*
 * VUCSideBandIOTestSetGpioStauts
 * define input value byte 0
 * byte0 == 0 set output mode
 * byte0 == 1 set input mode
 * define input value byte 1
 * if byte 0 == set outpumode
 * byte 1 == 0 set output low
 * byte 1 == 1 set output high
 * if byte 0 == set input mode
 * byte 1 == 0 set input pull low
 * byte 1 == 1 set input pull high
 */
void VUCSideBandIOTestGetSetGpioStatus(VUC_OPT_HCMD_PTR_t pCmd, U8 ubGetSet)
{
	M_UART(SIDEBAND_, "\n%s", __FUNCTION__);
	U8 ubId = pCmd->vuc_sqcmd.vendor.SideBandIOTest.ubId;
	GetSetGpioStatus *table[13] = {
		VUC_SIDEBANDIOTEST_ID_PWRDIS,
		VUC_SIDEBANDIOTEST_ID_PLN,
		VUC_SIDEBANDIOTEST_ID_LED,
		VUC_SIDEBANDIOTEST_ID_WP_FIG,
		VUC_SIDEBANDIOTEST_ID_PLA_S3,
		VUC_SIDEBANDIOTEST_ID_SMB_CLK,
		VUC_SIDEBANDIOTEST_ID_SMB_DATA,
		VUC_SIDEBANDIOTEST_ID_PFAIL_DETECT,
		VUC_SIDEBANDIOTEST_ID_PERST,
		VUC_SIDEBANDIOTEST_ID_CLKREQ,
		VUC_SIDEBANDIOTEST_ID_UART_TX,
		VUC_SIDEBANDIOTEST_ID_UART_RX,
		VUC_SIDEBANDIOTEST_ID_ERASE
	};
	table[ubId](pCmd, ubGetSet);
	M_UART(SIDEBAND_, "\nubGetSet = %d\n", ubGetSet);
}


void VUC_SIDEBANDIOTEST_ID_PWRDIS(VUC_OPT_HCMD_PTR_t pCmd, U8 ubGetSet)
{

}

void VUC_SIDEBANDIOTEST_ID_PLN (VUC_OPT_HCMD_PTR_t pCmd, U8 ubGetSet)
{

}

void VUC_SIDEBANDIOTEST_ID_LED (VUC_OPT_HCMD_PTR_t pCmd, U8 ubGetSet)
{
#if (!E21_TODO)
	M_UART(SIDEBAND_, "\n%s", __FUNCTION__);
	/* Back Up first */
	if (VUC_SIDEBANDIOTEST_BACKUP_LOCK_DISABLE == gIOBackUp.LED.ublock) {
		gIOBackUp.LED.ulMux = R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL1];
		gIOBackUp.LED.ulPadc = R32_SYS0_PADC[R32_SYS0_PAD_CTRL_REG1];
		gIOBackUp.LED.ublock = VUC_SIDEBANDIOTEST_BACKUP_LOCK_ENABLE;
	}
	if (VUC_SIDEBANDIOTEST_GET_GPIO_STATUS == ubGetSet) {
		U8 *ubReturnBuf = (U8 *)gulVUCBufAddr;
		U8 ubMode = 0, ubDataStatus = 0;
		/* CR_MUX_LED_OE  switch to CTRL_BY_PADC  */
		/* Back up mux setting switch control by led */
		//M_MUX_BACK_UP_SETTING_CTRL1(ulTempmux);
		M_MUX_CTRL_BY_PADC();
		ubMode = M_PADC_GET_LED_OE();
		M_UART(SIDEBAND_, "\nubMode = %d\n", ubMode);
		if (VUC_SIDEBANDIOTEST_LED_INPUT == ubMode) {
			/* use pad reg */
			ubDataStatus = M_PADC_GET_LED_INPUT(); 	//data
			M_UART(SIDEBAND_, "\nubDataStatus = %d\n", ubDataStatus);
		}
		else if (VUC_SIDEBANDIOTEST_LED_OUTPUT == ubMode) {
			/* use led control reg */
			//ubDataStatus = M_LED_GET_STATUS(); 	//status
			/* only output 0 by mail */
			ubDataStatus = 0;
		}
		/* host define output 0 input 1*/
		ubReturnBuf[0] = (ubMode == 0 ? 1 : 0);
		ubReturnBuf[1] = ubDataStatus;
		//M_MUX_RESTROE_SETTING_CTRL1(ulTempmux);
	}
	else if (VUC_SIDEBANDIOTEST_SET_GPIO_STATUS == ubGetSet) {
		U8 *ubCommandBuf = (U8 *)gulVUCBufAddr;
		U8 ubInputOutput = ubCommandBuf[0];
		M_UART(SIDEBAND_, "\nubInputOutput = %d\n", ubInputOutput);
		if (VUC_SIDEBANDIOTEST_SET_OUTPUT_MODE == ubInputOutput) {
			M_MUX_CTRL_BY_PADC();
			M_PADC_SET_LED_OE();
			//M_MUX_CTRL_BY_LED();
			if ( VUC_SIDEBANDIOTEST_SET_OUTPUT_MODE_LOW == ubCommandBuf[1]) {
				//M_LED_DISABLE();
			}
			else if (VUC_SIDEBANDIOTEST_SET_OUTPUT_MODE_HIGH == ubCommandBuf[1]) {
				//M_LED_ENABLE();
			}
		}
		else if (VUC_SIDEBANDIOTEST_SET_INPUT_MODE == ubInputOutput) {
			U8 ubMode = 0;
			M_MUX_CTRL_BY_PADC();
			M_PADC_CLR_LED_OE();
			ubMode = M_PADC_GET_LED_OE();
			M_UART(SIDEBAND_, "\nubMode = %d\n", ubMode);
			if (VUC_SIDEBANDIOTEST_SET_INPUT_MODE_PULL_LOW == ubCommandBuf[1]) {
				M_PADC_SET_LED_PD();
			}
			else if (VUC_SIDEBANDIOTEST_SET_INPUT_MODE_PULL_HIGH == ubCommandBuf[1]) {
				/* not support */
				M_PADC_CLR_LED_PD();
			}
		}
		else if (VUC_SIDEBANDIOTEST_RESET_MODE == ubInputOutput) {
			R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL1] = gIOBackUp.LED.ulMux ;
			R32_SYS0_PADC[R32_SYS0_PAD_CTRL_REG1] = gIOBackUp.LED.ulPadc;
			gIOBackUp.LED.ublock = VUC_SIDEBANDIOTEST_BACKUP_LOCK_DISABLE;
		}
	}
#endif /*(!E21_TODO)*/
}
void VUC_SIDEBANDIOTEST_ID_WP_FIG		(VUC_OPT_HCMD_PTR_t pCmd, U8 ubGetSet) {}
void VUC_SIDEBANDIOTEST_ID_PLA_S3		(VUC_OPT_HCMD_PTR_t pCmd, U8 ubGetSet) {}
void VUC_SIDEBANDIOTEST_ID_SMB_CLK		(VUC_OPT_HCMD_PTR_t pCmd, U8 ubGetSet) {}
void VUC_SIDEBANDIOTEST_ID_SMB_DATA		(VUC_OPT_HCMD_PTR_t pCmd, U8 ubGetSet) {}
void VUC_SIDEBANDIOTEST_ID_PFAIL_DETECT	(VUC_OPT_HCMD_PTR_t pCmd, U8 ubGetSet) {}
void VUC_SIDEBANDIOTEST_ID_PERST		(VUC_OPT_HCMD_PTR_t pCmd, U8 ubGetSet) {}

void VUC_SIDEBANDIOTEST_ID_CLKREQ		(VUC_OPT_HCMD_PTR_t pCmd, U8 ubGetSet)
{
#if (!E21_TODO)
	M_UART(SIDEBAND_, "\n%s", __FUNCTION__);
	/* Back up first */
	if (VUC_SIDEBANDIOTEST_BACKUP_LOCK_DISABLE == gIOBackUp.CLKREQ.ublock) {
		gIOBackUp.CLKREQ.ulPadc = R32_SYS0_PADC[R32_SYS0_PAD_CTRL_REG0];
		gIOBackUp.CLKREQ.ulPcie = r32_PCIE_MISC[OPT_CTRL1_W32] ;
		gIOBackUp.CLKREQ.ublock = VUC_SIDEBANDIOTEST_BACKUP_LOCK_ENABLE;
	}

	if (VUC_SIDEBANDIOTEST_GET_GPIO_STATUS == ubGetSet) {
		U8 *ubReturnBuf = (U8 *)gulVUCBufAddr;
		U8 ubMode = 0, ubDataStatus = 0;
		M_SET_CR_CLKREQB_OE_FW_MODE();  /* need to reset (? */
		ubMode =	M_GET_CR_CLKREQB_OE();
		M_UART(SIDEBAND_, "\nubMode = %d\n", ubMode);
		if (VUC_SIDEBANDIOTEST_CLKREQ_INPUT == ubMode) {
			ubDataStatus =	M_GET_XCLKREQB_I();
			M_UART(SIDEBAND_, "\nubDataStatus = %d\n", ubDataStatus);
		}
		else if (VUC_SIDEBANDIOTEST_CLKREQ_OUTPUT == ubMode) {
			/* only output 0 by mail */
			ubDataStatus = 0;
		}
		/* host define output 0 input 1*/
		ubReturnBuf[0] = (ubMode == 0 ? 1 : 0);
		ubReturnBuf[1] = ubDataStatus;
	}
	else if (VUC_SIDEBANDIOTEST_SET_GPIO_STATUS == ubGetSet) {
		U8 *ubCommandBuf = (U8 *)gulVUCBufAddr;
		U8 ubInputOutput = ubCommandBuf[0];
		M_UART(SIDEBAND_, "\nubInputOutput = %d\n", ubInputOutput);
		if (VUC_SIDEBANDIOTEST_SET_OUTPUT_MODE == ubInputOutput) {
			M_SET_CR_CLKREQB_OE_FW_MODE();
			M_SET_CR_CLKREQB_OE();
			if ( VUC_SIDEBANDIOTEST_SET_OUTPUT_MODE_LOW == ubCommandBuf[1]) {
				/* not support */
			}
			else if (VUC_SIDEBANDIOTEST_SET_OUTPUT_MODE_HIGH == ubCommandBuf[1]) {
				/* not support */
			}
		}
		else if (VUC_SIDEBANDIOTEST_SET_INPUT_MODE == ubInputOutput) {
			M_SET_CR_CLKREQB_OE_FW_MODE();
			M_CLR_CR_CLKREQB_OE(); /* input mode */
			M_PCIE_SET_DISABLE_BLK_D2H(); /* look by mail */
			/*
			if (VUC_SIDEBANDIOTEST_SET_INPUT_MODE_PULL_LOW == ubCommandBuf[1]) {
				M_SET_CR_CLKREQB_PD();
			}
			else if (VUC_SIDEBANDIOTEST_SET_INPUT_MODE_PULL_HIGH == ubCommandBuf[1]) {
				M_CLR_CR_CLKREQB_PD();
			}
			*/
		}
		else if (VUC_SIDEBANDIOTEST_RESET_MODE == ubInputOutput ) {
			R32_SYS0_PADC[R32_SYS0_PAD_CTRL_REG0] = gIOBackUp.CLKREQ.ulPadc ;
			r32_PCIE_MISC[OPT_CTRL1_W32] = gIOBackUp.CLKREQ.ulPcie ;
			gIOBackUp.CLKREQ.ublock = VUC_SIDEBANDIOTEST_BACKUP_LOCK_DISABLE;
		}
	}
#endif /*(!E21_TODO)*/
}

void VUC_SIDEBANDIOTEST_ID_UART_TX		(VUC_OPT_HCMD_PTR_t pCmd, U8 ubGetSet) {}
void VUC_SIDEBANDIOTEST_ID_UART_RX		(VUC_OPT_HCMD_PTR_t pCmd, U8 ubGetSet) {}
void VUC_SIDEBANDIOTEST_ID_ERASE		(VUC_OPT_HCMD_PTR_t pCmd, U8 ubGetSet) {}

#endif /* (HOST_MODE == NVME) */
