#include "hal/pic/uart/uart_api.h"
#include "hal/sys/api/mux/mux_api.h"
#include "nvme_api/nvme/shr_hal_nvme.h"
#include "vuc/VUC_command.h"

#if (BURNER_MODE_EN)
void VUC_ISPReadROM(VUC_OPT_HCMD_PTR_t pCmd)
{
	U32 ulPackByteCnt = pCmd->vuc_sqcmd.vendor.ISPReadROM.ulLength * BYTE_PER_DW;
	U32 ulTempMux;
	M_MUX_BACK_UP_SETTING(ulTempMux);
	mux_spi_miso();
	mux_spi_mosi();
	mux_spi_io3();
	mux_spi_cs_and_clk();
	mux_spi_io2();
	M_UART(VUC_, "\nVUC_ISP_Read_ROM");

	M_UART(VUC_, "\nByteCnt\t%d", ulPackByteCnt);

	memcpy((void *)BURNER_VENDOR_BUF_BASE, (void *)SPI_RAM_ADDRESS, ulPackByteCnt);

	M_MUX_RESTROE_SETTING(ulTempMux);

}
#endif /* BURNER_MODE_EN */
