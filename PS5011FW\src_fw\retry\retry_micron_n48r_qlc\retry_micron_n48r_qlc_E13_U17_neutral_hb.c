#include "retry/retry.h"
#include "retry/retry_api.h"
#include "retry/retry_hb.h"
#include "retry_micron_n48r_qlc_E13_U17_neutral_hb.h"
#include "hal/pic/uart/uart_api.h"
#include "retry/retry_version_api.h" // Retry Version
#include "table/initinfo_vt/initinfo_vt.h" // VT
#include "ftl/ftl_api.h" // FW Timer
#include "hal/fip/fpu.h"
#include "table/sys_block/sys_block_api.h" //RetryInitSystemBlockReadRetryTable
#include "retry/err_hdl_fpl_RS.h"

#if RDT_MODE_EN
#include "rdt/rdt_api.h"
#endif

#if ((PS5013_EN || PS5017_EN) && (FLASH_N48R_QLC == FW_CATEGORY_FLASH) && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER))
/*
 * ----------------------------------------------------------------------------
 *   Definitions
 * ----------------------------------------------------------------------------
 */

#define RETRY_HYNIX_V6_SLC_RRT_BASE		(DBUF_RETRY_RR_TABLE)
#define RETRY_HYNIX_V6_TLC_RRT_BASE		(DBUF_RETRY_RR_TABLE + DEF_KB(2))

#define OTP_RR_CNT_SITE                 (8)
#define OTP_RR_REG_CNT_SITE             (16)

#define OTP_DEFAULT_RR_CNT              (50)
#define OTP_DEFAULT_RR_REG_CNT_SLC      (HBIT_RETRY_SLC_FEA_DATA_NUM)
#define OTP_DEFAULT_RR_REG_CNT_TLC      (HBIT_RETRY_TLC_FEA_DATA_NUM)

#define OTP_DEFAULT_SET_NUM             (8)
#define OTP_NORMAL_SEQ                  (0)
#define OTP_INVERSE_SEQ                 (1)
#define OTP_SEQ_CNT_PER_SET             (2)
#define HB_MICRON_PREFIX_RRTABLE_NUM		(19)

/*
 * ----------------------------------------------------------------------------
 *   Enum
 * ----------------------------------------------------------------------------
 */

/*
 * ----------------------------------------------------------------------------
 *   Data Types
 * ----------------------------------------------------------------------------
 */

typedef struct {
	U8 ubSeq[OTP_DEFAULT_SET_NUM][OTP_SEQ_CNT_PER_SET][OTP_DEFAULT_RR_CNT * OTP_DEFAULT_RR_REG_CNT_TLC];
} OTPSeq_t;

/*
 * ----------------------------------------------------------------------------
 *   Macros
 * ----------------------------------------------------------------------------
 */

/*
 * ----------------------------------------------------------------------------
 *   Extern Global Variables
 * ----------------------------------------------------------------------------
 */

/*
 *	MICRON N48R
 * Lower Page         Upper Page           Extra Page          Top Page
 * P0  P1  P2  P3     P0  P1  P2  P3       P0  P1  P2  P3      P0  P1  P2   P3
 * R1  R4  R6  R11    R3  R7  R9  R13      R2  R8  R14 00      R5  R10 R12  R15
 * 1   1   1    1     1   1   1   1        1   1   1   0       1   1   1    1
 */
const U8 gubRetryReadRetryTableParameterIdxToPageType[HBIT_RETRY_SELECT_NON_SLC_FEA_DATA_NUM] = {
	HB_LRU_MICRON_QLC_WL_LOWER, HB_LRU_MICRON_QLC_WL_LOWER, HB_LRU_MICRON_QLC_WL_LOWER, HB_LRU_MICRON_QLC_WL_LOWER,
	HB_LRU_MICRON_QLC_WL_UPPER, HB_LRU_MICRON_QLC_WL_UPPER, HB_LRU_MICRON_QLC_WL_UPPER, HB_LRU_MICRON_QLC_WL_UPPER,
	HB_LRU_MICRON_QLC_WL_EXTRA, HB_LRU_MICRON_QLC_WL_EXTRA, HB_LRU_MICRON_QLC_WL_EXTRA, 00,
	HB_LRU_MICRON_QLC_WL_TOP, HB_LRU_MICRON_QLC_WL_TOP, HB_LRU_MICRON_QLC_WL_TOP, HB_LRU_MICRON_QLC_WL_TOP
};

/*
 * ----------------------------------------------------------------------------
 *   Static Global Variables
 * ----------------------------------------------------------------------------
 */

/*
 * ----------------------------------------------------------------------------
 *   Private Prototypes
 * ----------------------------------------------------------------------------
 */

INLINE void HBRetryInitRegisterRetryTable(void);
INLINE void HBRetryInitAdjustVthFPU(void);
INLINE U16 MicronN48RQLCHBRetrySetFeatureFPU(void);//U8 ubStep, U8 ubPageType, U8 ubSLCMode
INLINE U16 MicronN48RQLCHBRetryCheckFeatureFPU(void);//U8 ubStep, U8 ubPageType, U8 ubSLCMode
INLINE U16 MicronN48RQLCHBRetryGetFeatureFPU(void);//U8 ubStep, U8 ubPageType, U8 ubSLCMode

/*
 * ----------------------------------------------------------------------------
 *   Private
 * ----------------------------------------------------------------------------
 */
void HBRetryInitRegisterRetryTable(void)//INLINE
{
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubLastSuccessIndex = 0;			// TLC
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubCurrentStep = HB_READ_START_STEP;
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubTotalRetryCnt = (gpIDPage->ubRetryGroupCount ? (gpIDPage->ubRetryGroupCount + 1) : HBIT_RETRY_MICRON_N48R_QLC_1024G_STEP_NUM);
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubRetryParameterSize = (gpIDPage->ubRetryParamCount ? gpIDPage->ubRetryParamCount : HBIT_RETRY_QLC_FEA_DATA_NUM);//HBIT_RETRY_MICRON_FEA_DATA_NUM
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubParameterNumPerFPU = (gpIDPage->ubRetryParamCount ? gpIDPage->ubRetryParamCount : HBIT_RETRY_QLC_FEA_DATA_NUM);//PARAMETER_NUM_PER_FPU
	gpHBParameterArray[FPL_ADDR_TLC_RULE].pubRetryTable = (U8 *)(MICRON_TLC_RRT_BASE);
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubScratchTableIdx = INVALID_RETRY_TABLE_INDEX;

	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubLastSuccessIndex = 0;			// SLC
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubCurrentStep = HB_READ_START_STEP;
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubTotalRetryCnt = (gpIDPage->ubD1RetryGroupCount ? (gpIDPage->ubD1RetryGroupCount + 1) : HBIT_RETRY_MICRON_N48R_SLC_1024G_STEP_NUM);
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubRetryParameterSize = (gpIDPage->ubD1RetryParamCount ? gpIDPage->ubD1RetryParamCount : HBIT_RETRY_SLC_FEA_DATA_NUM);
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubParameterNumPerFPU = (gpIDPage->ubD1RetryParamCount ? gpIDPage->ubD1RetryParamCount : HBIT_RETRY_SLC_FEA_DATA_NUM);//PARAMETER_NUM_PER_FPU
	gpHBParameterArray[FPL_ADDR_SLC_RULE].pubRetryTable = (U8 *)(MICRON_SLC_RRT_BASE);
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubScratchTableIdx = INVALID_RETRY_TABLE_INDEX;

	M_UART(RETRY_, " \nubD3RetryGroupCnt=%d, ubD3RetryParamCnt=%d ubD3ParameterNumPerFPU=%d", gpHBParameterArray[FPL_ADDR_TLC_RULE].ubTotalRetryCnt, gpHBParameterArray[FPL_ADDR_TLC_RULE].ubRetryParameterSize, gpHBParameterArray[FPL_ADDR_TLC_RULE].ubParameterNumPerFPU);
	M_UART(RETRY_, " \nubD1RetryGroupCnt=%d, ubD1RetryParamCnt=%d ubD1ParameterNumPerFPU=%d", gpHBParameterArray[FPL_ADDR_SLC_RULE].ubTotalRetryCnt, gpHBParameterArray[FPL_ADDR_SLC_RULE].ubRetryParameterSize, gpHBParameterArray[FPL_ADDR_SLC_RULE].ubParameterNumPerFPU);
}

void HBRetryInitAdjustVthFPU(void)
{
	// Register set feature fpu
	gFpuEntryList.fpu_set_feature[0] = FPU_CMD(0xEF); // Set feature cmd
	gFpuEntryList.fpu_set_feature[7] = FPU_END;
	// 	gFpuEntryList.fpu_set_feature[0] = FPU_CMD(0xD5); // Set feature cmd
	// 	gFpuEntryList.fpu_set_feature[7] = FPU_END;

	// Register get feature fpu
	gFpuEntryList.fpu_get_feature[0] = FPU_CMD(0xEE); // Get feature cmd
	gFpuEntryList.fpu_get_feature[7] = FPU_END;
	// 	gFpuEntryList.fpu_get_feature[0] = FPU_CMD(0xD4); // Get feature cmd
	// 	gFpuEntryList.fpu_get_feature[3] = FPU_END;

	// Register read and compare feature data fpu
	gFpuEntryList.fpu_read_and_compare_feature_data[0] = FPU_CMD(0x00); // Switch to read mode
	gFpuEntryList.fpu_read_and_compare_feature_data[1] = FPU_NOP;
	gFpuEntryList.fpu_read_and_compare_feature_data[10] = FPU_END;
}

/*
 * ------------------------------------
 * 		Over Wirte Weak Function
 * ------------------------------------
 */

U8 HBRetryGetPageType(U64 uoiFSA, U8 ubALUSel)
{
	/*	M_UART(RDT_TEST_, "\n HBRetryGetPageType");*/
	U8 ubPageType;
	RetryPageCoordinateInfo_t PageCoordinateInfo = RetryGetSharedPageType((U32)uoiFSA, ubALUSel);

	if (gHBTask.MTTemplate.dma.ALUSelect & ALU_RULE_SLC_BIT) {
		ubPageType = HB_LRU_MICRON_SLC_WL;
	}
	else {
		if ((0 == PageCoordinateInfo.uwY) || (88 == PageCoordinateInfo.uwY) || (89 == PageCoordinateInfo.uwY) || (177 == PageCoordinateInfo.uwY) ) {
			if (HB_PAGE_LOWER == PageCoordinateInfo.ubLUX) {
				ubPageType = HB_LRU_MICRON_MLC_WL_LOWER;
			}
			else {
				ubPageType = HB_LRU_MICRON_MLC_WL_UPPER;
			}
		}
		else {
			ubPageType = HB_LRU_MICRON_QLC_WL_LOWER + PageCoordinateInfo.ubLUX;
		}
	}

	/*	M_UART(RDT_TEST_, "\n ubPageType:%d", ubPageType);*/
	M_UART(RETRY_, "\n [HB] P:%d X:%d Y:%d LUX:%d T:%d \n", PageCoordinateInfo.uwPage, PageCoordinateInfo.ubX, PageCoordinateInfo.uwY, PageCoordinateInfo.ubLUX, gHBParamMgr.ubPageType);
	return ubPageType;
}

// void HBRetryClearReadOffsetPassCallback(U8 ubMTIdx)
// {
// }

// void HBRetryPostconditionGetFeatureCheckPass_Callback(U8 ubMTIdx)//check
// {
// }
//
// void HBRetryPostconditionGetFeatureCheckFail_Callback(U8 ubMTIdx)
// {
// }

// void HBRetryReadDMAPass_Callback(U8 ubMTIdx)
// {
// }
//
// void HBRetryReadDMAFail_Callback(U8 ubMTIdx)
// {
// }
U16 MicronN48RQLCHBRetrySetFeatureFPU(void)//U8 ubStep, U8 ubPageType, U8 ubSLCMode
{
	FPL_HBIT_RETRY_PARAM_STRUCT_PTR pParam = gHBParamMgr.pParam;
	U8 ubValidParameterSize = pParam->ubParameterNumPerFPU;
	/*M_UART(RDT_TEST_, " \n MicronN48RQLCHBRetrySetFeatureFPU");*/
	U8 ubi;

	U16 uwFPUPtr;
	U16 *puwFPU;

	U8 ubFeatureData[HBIT_RETRY_QLC_FEA_DATA_NUM] = {0};
	U8 ubFPUIdx;
	U8 ubPrefixOffset;

	M_UART(RETRY_, " \n [HB] Micron N48R set Retry:");

	if (!gHBParamMgr.ubSLCMode && pParam->ubCurrentStep < 25) { //vt steps
		uwFPUPtr = FPU_PTR_OFFSET(fpu_set_feature);
		puwFPU = (U16 *)(IRAM_BASE + uwFPUPtr);

		// 	puwFPU[0] = FPU_CMD(0xD5);
		// 	puwFPU[1] = FPU_ADR_1B(gHBParamMgr.ubDie);
		puwFPU[1] = FPU_ADR_1B(0x96);
		// Delay
		if (FIP_FLASH_CLOCK_400MHZ == gFlhEnv.ubTargetFlashClock) {
			// flh clk 400
			puwFPU[2] = FPU_DLY(0x15);		// Decimal 21
		}
		else if (FIP_FLASH_CLOCK_333MHZ == gFlhEnv.ubTargetFlashClock) {
			// flh clk 333
			puwFPU[2] = FPU_DLY(0xF);		// Decimal 15
		}
		else {
			puwFPU[2] = FPU_DLY(0x10);		// Decimal 16
		}
		puwFPU[3] = FPU_DAT_W_1B(0x0C);
		puwFPU[4] = FPU_DAT_W_1B(0x00);
		puwFPU[5] = FPU_DAT_W_1B(0x00);
		puwFPU[6] = FPU_DAT_W_1B(0x00);

		return uwFPUPtr;
	}
	else if (pParam->ubCurrentStep < pParam->ubTotalRetryCnt) { //Prevent Access Array Over flow
		U16 uwTableOffset = (pParam->ubCurrentStep - 25) * pParam->ubRetryParameterSize;//subtract  VT steps
		// Copy set feature from HB retry table
		memcpy((void *)ubFeatureData, (void *)&pParam->pubRetryTable[uwTableOffset], ubValidParameterSize);
	}


	// Get FPU PTR
	if (gHBParamMgr.ubSLCMode) {
		uwFPUPtr = FPU_PTR_SLC_RETRY_READ_ACRR_CMD(0);
		ubPrefixOffset = 2;
	}
	else {
		uwFPUPtr = FPU_PTR_TLC_RETRY_READ_ACRR_CMD(0);
		ubPrefixOffset = 1;
	}
	puwFPU = (U16 *)(IRAM_BASE + uwFPUPtr);

	// Set parameter cmd, already fill FPU[0] in init state
	puwFPU += ubPrefixOffset;
	ubFPUIdx = 0;

	/*M_UART(RDT_TEST_, "\npParam->ubCurrentStep %d gHBParamMgr.ubPageType %d", pParam->ubCurrentStep - 25, gHBParamMgr.ubPageType);*/
	for (ubi = 0; ubi < ubValidParameterSize; ++ubi) {
		if (gHBParamMgr.ubSLCMode || gHBParamMgr.ubPageType == M_RETRY_GET_PAGE_TYPE_BY_READ_RETRY_TABLE_PARAMETER_IDX(ubi)) {
			// Set feature data
			puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(ubFeatureData[ubi]);
			M_UART(RETRY_, "\nData[%d]=[%x]", ubi, ubFeatureData[ubi]);
		}
	}

	return uwFPUPtr;

}

U16 MicronN48RQLCHBRetryCheckFeatureFPU(void)//U8 ubStep, U8 ubPageType, U8 ubSLCMode
{
	FPL_HBIT_RETRY_PARAM_STRUCT_PTR pParam = gHBParamMgr.pParam;
	U8 ubValidParameterSize = pParam->ubParameterNumPerFPU;
	/*M_UART(RDT_TEST_, " \n MicronN48RQLCHBRetryCheckFeatureFPU");*/
	U8 ubi;
	U16 uwFPUPtr;
	U16 *puwFPU;
	U8 ubFeatureData[HBIT_RETRY_QLC_FEA_DATA_NUM] = {0};

	M_UART(RETRY_, "\n [HB] check feature:");
	// Get FPU PTR
	uwFPUPtr = FPU_PTR_OFFSET(fpu_read_and_compare_feature_data);
	puwFPU = (U16 *)(IRAM_BASE + uwFPUPtr);

	if (pParam->ubCurrentStep < 25) { //vt steps
		puwFPU[2] = FPU_DAT_R_CMP(0x0C);
		puwFPU[3] = FPU_DAT_R_MASK(0xFF);
		return uwFPUPtr;
	}
	else {
		U8 ubFPUIdx = 2;
		for (ubi = 0; ubi < ubValidParameterSize; ++ubi) {
			if (gHBParamMgr.ubSLCMode || gHBParamMgr.ubPageType == M_RETRY_GET_PAGE_TYPE_BY_READ_RETRY_TABLE_PARAMETER_IDX(ubi)) {
				// Get feature data
				puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(ubFeatureData[ubi]);
				M_UART(RETRY_, "\nData[%d]=[%x]", ubi, ubFeatureData[ubi]);
				puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);
			}
		}
		return uwFPUPtr;
	}
}

U16 MicronN48RQLCHBRetryGetFeatureFPU(void)
{
	return 0;
	/*M_UART(RDT_TEST_, "\n MicronN48RQLCHBRetryGetFeatureFPU");*/
	U16 uwFPUPtr = 0;
	U16 *puwFPU;
	uwFPUPtr = FPU_PTR_OFFSET(fpu_get_feature);
	puwFPU = (U16 *)(IRAM_BASE + uwFPUPtr);
	// 	puwFPU[0] = FPU_CMD(0xD4);
	// 	puwFPU[1] = FPU_ADR_1B(gHBParamMgr.ubDie);
	puwFPU[1] = FPU_ADR_1B(0x96);
	return uwFPUPtr;
}

void HBRetryInitParameter(void)
{
	M_FW_ASSERT(ASSERT_RETRY_0x0870, (ID_MICRON == gpOtherInfo->ubMakerCode) && (RETRY_MICRON_FLASH_PROCESS_N48R == gpOtherInfo->ubProcess));
	HBRetryInitRegisterRetryTable();
	HBRetryInitAdjustVthFPU();
}

// U16 HBRetrySelectResetCMDFPU(void)
// {
// 	//Reset CMD 0xFA is By LUN(Die)
// 	return guwFPUEntryResetFAh[gHBParamMgr.ubDie];
// }

U16 HBRetrySelectReadCMDFPU(void)//zerio n48r
{
	/*M_UART(RDT_TEST_, "\n HBRetrySelectReadCMDFPU");*/

	if (gHBParamMgr.ubSLCMode || gHBParamMgr.pParam->ubCurrentStep >= 25) { //vt steps
		return 0;
	}

	U16 uwRetFPUPtr = 0;
	if (gHBTask.MTTemplate.dma.ALUSelect & ALU_RULE_SLC_BIT) { // BIT1 = SLC
		uwRetFPUPtr = FPU_PTR_SLC_READ_CMD;//FPU_PTR_SLC_RETRY_READ_ACRR_CMD(0)
	}
	else {
		uwRetFPUPtr = FPU_PTR_TLC_1_PLANE_READ_CMD;//FPU_PTR_TLC_RETRY_READ_ACRR_CMD(gHBParamMgr.ubCurACRR)
	}
	return uwRetFPUPtr;
}

U16 HBRetryPreconditionSetFeatureFPU(void)
{
	return MicronN48RQLCHBRetrySetFeatureFPU();//gHBParamMgr.pParam->ubCurrentStep, gHBParamMgr.ubPageType, gHBParamMgr.ubSLCMode
}

U16 HBRetryPreconditionCheckFeatureFPU(void)
{
	return MicronN48RQLCHBRetryCheckFeatureFPU();//gHBParamMgr.pParam->ubCurrentStep, gHBParamMgr.ubPageType, gHBParamMgr.ubSLCMode
}

U16 HBRetryPreconditionGetFeatureFPU(void)
{
	return MicronN48RQLCHBRetryGetFeatureFPU();
}

U16 HBRetryPostconditionSetFeatureFPU(void)
{
	/*HB_LRU_TABLE_STRUCT_PTR pLRUTable = M_RETRY_GET_LRU_TABLE(gHBParamMgr.ubSLCMode, gHBParamMgr.ubGlobalDie, gHBParamMgr.ubPageType);*/
	return MicronN48RQLCHBRetrySetFeatureFPU();//pLRUTable[0].param.ubTableIdx, gHBParamMgr.ubPageType, gHBParamMgr.ubSLCMode
}

U16 HBRetryPostconditionCheckFeatureFPU(void)
{
	//HB_LRU_TABLE_STRUCT_PTR pLRUTable = M_RETRY_GET_LRU_TABLE(gHBParamMgr.ubSLCMode, gHBParamMgr.ubGlobalDie, gHBParamMgr.ubPageType);
	return MicronN48RQLCHBRetryCheckFeatureFPU();//pLRUTable[0].param.ubTableIdx, gHBParamMgr.ubPageType, gHBParamMgr.ubSLCMode
}
U16 HBRetryPostconditionGetFeatureFPU(void)
{
	//HB_LRU_TABLE_STRUCT_PTR pLRUTable = M_RETRY_GET_LRU_TABLE(gHBParamMgr.ubSLCMode, gHBParamMgr.ubGlobalDie, gHBParamMgr.ubPageType);
	return MicronN48RQLCHBRetryGetFeatureFPU();//pLRUTable[0].param.ubTableIdx, gHBParamMgr.ubPageType, gHBParamMgr.ubSLCMode
}

// U16 HBRetryPreconditionSetFeatureFPU(void)
// {
// }

// U16 HBRetryPreconditionCheckFeatureFPU(void)
// {
// }

// U16 HBRetryPostconditionSetFeatureFPU(void)
// {
// }

// U16 HBRetryPostconditionCheckFeatureFPU(void)
// {
// }

// U16 HBRetryClearReadOffsetFPU(void)
// {
// }

#endif /* ((PS5013_EN || PS5017_EN) && (FLASH_N48R_QLC == FW_CATEGORY_FLASH) && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER)) */

