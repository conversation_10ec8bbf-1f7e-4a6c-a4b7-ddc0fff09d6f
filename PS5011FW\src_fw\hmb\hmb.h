#ifndef _HMB_H_
#define _HMB_H_

//******************************************
//	Define
//******************************************
#define HMB_RESET_FLOW_WAIT_COP_AND_FIP_IDLE_LIMIT		(5000) // 5ms
#define HMB_BLOCK_RX_DELAY								(10) //10us
#define HMB_RESET_IP	(CR_RST_N_PIC_BIT | CR_RST_N_COP0_BIT | CR_RST_N_COP1_BIT | CR_RST_N_AXI_BIT |CR_RST_N_SEC_BIT | CR_RST_N_SPI_BIT | CR_RST_N_MR_BIT | CR_RST_N_DMAC_BIT | CR_RST_N_HOST_ND_BIT | CR_RST_N_AHB_BIT | CR_RST_N_NVME_BIT | CR_RST_N_WDT_BIT | CR_RST_N_ZIP_BIT | CR_RST_N_BMU_BIT | CR_RST_N_DBUF_BIT)

#endif /* _HMB_H_ */
