/****************************************************************************
*
*  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
*  All rights reserved
*
*  The content of this document is confidential and shall be applied
*  subject to the terms and conditions of the license agreement and
*  other applicable laws. Any unauthorized access, use or disclosure
*  of this document is strictly prohibited and may be punishable
*  under laws.
*
*  fip_toshiba_cmd.c
*
*
*
****************************************************************************/
/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include <string.h>
#include "hal/fip/fip_api.h"
#include "hal/fip/fip.h"
#include "retry/retry_api.h"
#include "debug/debug.h"
/*
 * ---------------------------------------------------------------------------------------------------
 *   public
 * ---------------------------------------------------------------------------------------------------
 */
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_TLC || CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_QLC)//zerio bics6 qlc add

void FipGetFlashReadRetryTable(U8 ubChannel, U8 ubFlashCE, U8 ubDie, U8 *pubBufAddr, U32 ulOffset, U8 ubFormat)
{
	memset((void *)(pubBufAddr), 0x00, DEF_512B * 8);
	RetryFillFlashReadRetryTable(pubBufAddr, ubFormat);
}

#if BURNER_MODE_EN
void FipGetTrimTable(U8 ubChannel, U8 ubFlashCE, U8 ubDie, U8 *pubBufAddr)
{
	U8 ubToshibaBICS4 = (TSB_3D_BICS4 == gFlhEnv.ulFlashDefaultType.BitMap.NAND) ? TRUE : FALSE;
	U16 uwi, uwAddrIdx = 0;
	U16 uwTrimTableSet1Size = 256, uwTrimTableSet2Size = 44;
	REG32 *pFlaReg = (REG32 *)R32_FCTL_CH[ubChannel];
	U32 ulFlaSetTemp, ulFDivTemp;

	memset((void *)(pubBufAddr), 0x00, DEF_512B);
	FlaCEControl(ubChannel, ubFlashCE, ENABLE);

	ulFDivTemp = pFlaReg[R32_FCTL_FDIV_CFG] & FLH_CLK_DIV_VAL_MASK; //backup flh div
	ulFlaSetTemp = pFlaReg[R32_FCTL_FLH_SET];
	M_FIP_SET_FLH_DIV(ubChannel, FLH_CLK_DIV16_VAL);  //need to check
	M_FIP_DIV_WAIT_BUSY(ubChannel);

	if ((pFlaReg[R32_FCTL_FLH_SET] & FLH_IF_TYPE_MASK) == SET_TOGGLE_MODE) {
		pFlaReg[R32_FCTL_FLH_SET] &= CLR_LEGACY_MODE;
	}
	pFlaReg[R32_FCTL_PIO_CMD] = 0xFF;

	pFlaReg[R32_FCTL_PIO_CMD] = 0x70;
	IdlePC(80);	// at least 120ns
	while ((pFlaReg[R32_FCTL_PIO_DAT] & 0xE0) != 0xE0);

	pFlaReg[R32_FCTL_PIO_CMD] = 0x5C;
	pFlaReg[R32_FCTL_PIO_CMD] = 0xC5;
	pFlaReg[R32_FCTL_PIO_CMD] = 0x55;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
	pFlaReg[R32_FCTL_PIO_DAT] = 0x0101;
	FlaWaitFlashReady(GERNAL_TIMEOUT_THRESHOLD);
	U8 ubFn = 0xF0 + ubDie + 1;
	U8 *pubData = (U8 *)(pubBufAddr);
	for (uwi = 0; uwi < uwTrimTableSet1Size; uwi++) {
		pFlaReg[R32_FCTL_PIO_CMD] = ubFn;
		pFlaReg[R32_FCTL_PIO_CMD] = 0x55 + ubToshibaBICS4; //BiCS3 0x55; BiCS4 0x56
		pFlaReg[R32_FCTL_PIO_ADR] = 0x01;
		pFlaReg[R32_FCTL_PIO_DAT] = 0x0606;

		if (!ubToshibaBICS4) {
			FlaWaitFlashReady(GERNAL_TIMEOUT_THRESHOLD);
			pFlaReg[R32_FCTL_PIO_CMD] = 0x5F;
		}
		pFlaReg[R32_FCTL_PIO_CMD] = 0x00;
		pFlaReg[R32_FCTL_PIO_ADR] = (U8)(uwi);	//triming table addr offset
		pFlaReg[R32_FCTL_PIO_CMD] = 0x5F;
		FlaWaitFlashReady(GERNAL_TIMEOUT_THRESHOLD);
		pubData[uwi] = (U8)(pFlaReg[R32_FCTL_PIO_DAT] & BIT_MASK(8));
		if (!ubToshibaBICS4) {
			pFlaReg[R32_FCTL_PIO_CMD] = 0x5F;
			pFlaReg[R32_FCTL_PIO_CMD] = 0x00;
			pFlaReg[R32_FCTL_PIO_CMD] = 0x5F;
			pFlaReg[R32_FCTL_PIO_CMD] = 0x5F;
			FlaWaitFlashReady(GERNAL_TIMEOUT_THRESHOLD);
		}
	}
	if (ubToshibaBICS4) {
		for (uwAddrIdx = 0; uwi < (uwTrimTableSet1Size + uwTrimTableSet2Size); uwi++, uwAddrIdx++) {
			pFlaReg[R32_FCTL_PIO_CMD] = ubFn;
			pFlaReg[R32_FCTL_PIO_CMD] = 0x56;
			pFlaReg[R32_FCTL_PIO_ADR] = 0x01;
			pFlaReg[R32_FCTL_PIO_DAT] = 0x0606;
			pFlaReg[R32_FCTL_PIO_CMD] = 0x56;
			pFlaReg[R32_FCTL_PIO_ADR] = 0xFF;
			pFlaReg[R32_FCTL_PIO_DAT] = 0x0101;

			pFlaReg[R32_FCTL_PIO_CMD] = 0x00;
			pFlaReg[R32_FCTL_PIO_ADR] = (U8)(uwAddrIdx);	//triming table addr offset
			pFlaReg[R32_FCTL_PIO_CMD] = 0x5F;
			FlaWaitFlashReady(GERNAL_TIMEOUT_THRESHOLD);
			pubData[uwi] = (U8)(pFlaReg[R32_FCTL_PIO_DAT] & BIT_MASK(8));
			pFlaReg[R32_FCTL_PIO_CMD] = 0x56;
			pFlaReg[R32_FCTL_PIO_ADR] = 0xFF;
			pFlaReg[R32_FCTL_PIO_DAT] = 0;
		}
	}
	pFlaReg[R32_FCTL_PIO_CMD] = 0xFF;
	FlaWaitFlashReady(GERNAL_TIMEOUT_THRESHOLD);
	M_FIP_SET_FLH_DIV(ubChannel, ulFDivTemp);
	M_FIP_DIV_WAIT_BUSY(ubChannel);
	pFlaReg[R32_FCTL_FLH_SET] = ulFlaSetTemp;

	FlaCEControl(ubChannel, ubFlashCE, DISABLE);

	if (TRUE == gFlhEnv.ulFlashDefaultType.BitMap.btNoSupport) {
		gFlhEnvMP.ulFailedCEBMP |= BIT(gubLogicalCEMapPhysicalCE[ubChannel][ubFlashCE]);
#if (!BURNER_MODE_EN)
		M_FW_CRITICAL_ASSERT(ASSERT_HAL_FIP_0x06BB, FALSE);//This means check status timeout, something wrong
#endif /* (!BURNER_MODE_EN) */
	}
}

#endif /* BURNER_MODE_EN */

void FipGetUniqueID(U8 ubChannel, U8 ubFlashCE, U8 ubDie, U8 *pubBufAddr)
{
	U16 uwUniqueIDSize = 256;
	U16 uwDatatmp;
	U8 *pubUniqueID = (U8 *)(pubBufAddr);
	REG32 *pFlaReg = (REG32 *)R32_FCTL_CH[ubChannel];
	U16 uwi;
	U8 ubAddr = 0x28;

	memset((void *)(pubBufAddr), 0x00, DEF_512B);
	FlaResetAllCE(0xFF);
	FlaCEControl(ubChannel, ubFlashCE, ENABLE);

	switch (gFlhEnv.ulFlashDefaultType.BitMap.NAND) {
	case TSB_3D_BICS5:
		ubAddr = 0x04;
		break;
	case TSB_3D_BICS6:
		ubAddr = 0x50;//zerio change
		break;
	case TSB_3D_BICS6_QLC:
		ubAddr = 0x50;//zerio bics6 qlc add
		break;
	case TSB_3D_BICS8: //zerio BICS8 Add
		ubAddr = 0x08;
		break;
	default:
		ubAddr = 0x28;//BiCS2/3/4
		break;
	}

	pFlaReg[R32_FCTL_PIO_CMD] = 0x5A;
	pFlaReg[R32_FCTL_PIO_CMD] = 0xB5;
	pFlaReg[R32_FCTL_PIO_CMD] = 0x00;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
	pFlaReg[R32_FCTL_PIO_ADR] = ubAddr;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
	pFlaReg[R32_FCTL_PIO_ADR] = ubDie * 8;
	pFlaReg[R32_FCTL_PIO_CMD] = 0x30;
	if ( FAIL == FlaWaitFlashReady(GERNAL_TIMEOUT_THRESHOLD)) {
		gFlhEnvMP.ulFailedCEBMP |= BIT(gubLogicalCEMapPhysicalCE[ubChannel][ubFlashCE]);
#if (!BURNER_MODE_EN)
		M_FW_CRITICAL_ASSERT(ASSERT_HAL_FIP_0x06BB, FALSE);//This means check status timeout , something wrong
#endif /* (!BURNER_MODE_EN) */
	}
	pFlaReg[R32_FCTL_PIO_CMD] = 0x05;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
	pFlaReg[R32_FCTL_PIO_ADR] = ubAddr;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
	pFlaReg[R32_FCTL_PIO_CMD] = 0xE0;
	//wait 1ms
	M_RTT_IDLE_MS(1);
	pFlaReg[R32_FCTL_HS_MODE] |= SET_DAT_DEFAULT;
	if (gFlhEnv.ubCurrentInterface == LEGACY_INTERFACE) {
		for (uwi = 0; uwi < uwUniqueIDSize; uwi++) {
			pubUniqueID[uwi] = pFlaReg[R32_FCTL_PIO_DAT];
		}
	}
	else if ((gFlhEnv.ubCurrentInterface == TOGGLE_INTERFACE) || (gFlhEnv.ubCurrentInterface == TOGGLE2_INTERFACE)) {
		for (uwi = 0; uwi < uwUniqueIDSize; uwi += 2) {
			pFlaReg[R32_FCTL_HS_MODE] &= CLR_DAT_MID;
			if (uwi == 0) {
				pFlaReg[R32_FCTL_HS_MODE] |= PIO_DAT_FIRST_BIT;
			}
			else if (uwi == (uwUniqueIDSize - 2)) {
				pFlaReg[R32_FCTL_HS_MODE] |= PIO_DAT_LAST_BIT;
			}
			uwDatatmp = (U16)pFlaReg[R32_FCTL_PIO_DAT];
			pubUniqueID[uwi] = (U8)(uwDatatmp & 0xFF);
			pubUniqueID[uwi + 1] = (U8)((uwDatatmp >> 8) & 0xFF);
		}
	}
	pFlaReg[R32_FCTL_HS_MODE] |= SET_DAT_DEFAULT;
	FlaCEControl(ubChannel, ubFlashCE, DISABLE);
	FlaResetAllCE(0xFF);
}
void FIPIdentifyEarlyFinalBICS4(U8 ubChannel, U8 ubFlashCE, U8 *pubBufAddr)
{
#if ((FW_CATEGORY_FLASH == FLASH_BICS3TLC)||(FW_CATEGORY_FLASH == FLASH_BICS4QLC) || (FW_CATEGORY_FLASH == FLASH_BICS4PSLC) || (FW_CATEGORY_FLASH == FLASH_BICS4TLCHDR))
	U16 uwReadDataSize = 4096;
	U8 ubData[16];
	U8 ubCurrentInterface = gFlhEnv.ubCurrentInterface;
	U16 uwDatatemp = 0;
	U8 *pubData = (U8 *)(pubBufAddr);
	REG32 *pFlaReg = (REG32 *)R32_FCTL_CH[ubChannel];
	U16 uwDataIdx = 0;
	U32 ulCheckPIOMap = 0;
	if (TOGGLE_INTERFACE == ubCurrentInterface) {
		ulCheckPIOMap = DQS_VALID_BIT;
	}
	else if (TOGGLE2_INTERFACE == ubCurrentInterface) {
		ulCheckPIOMap = DQS_VALID_BIT | DQSB_VALID_BIT;
	}

	FlaResetAllCE();
	FlaCEControl(ubChannel, ubFlashCE, ENABLE);

	pFlaReg[R32_FCTL_PIO_CMD] = 0x5A;
	pFlaReg[R32_FCTL_PIO_CMD] = 0xB5;
	pFlaReg[R32_FCTL_PIO_CMD] = 0x00;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x38;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
	pFlaReg[R32_FCTL_PIO_CMD] = 0x30;
	if ( FAIL == FlaWaitFlashReady(GERNAL_TIMEOUT_THRESHOLD)) {
		gFlhEnvMP.ulFailedCEBMP |= BIT(gubLogicalCEMapPhysicalCE[ubChannel][ubFlashCE]);
#if (!BURNER_MODE_EN)
		M_FW_CRITICAL_ASSERT(ASSERT_FLASH_0x060B, FALSE);//This means check status timeout , something wrong
#endif /* (!BURNER_MODE_EN) */
	}
	pFlaReg[R32_FCTL_PIO_CMD] = 0x05;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
	pFlaReg[R32_FCTL_PIO_CMD] = 0xE0;
	//wait 1ms
	M_RTT_IDLE_MS(1);
	pFlaReg[R32_FCTL_HS_MODE] |= SET_DAT_DEFAULT;
	if (LEGACY_INTERFACE == ubCurrentInterface) {
		for (uwDataIdx = 0; uwDataIdx < uwReadDataSize; uwDataIdx++) {
			pubData[uwDataIdx] = pFlaReg[R32_FCTL_PIO_DAT];
		}
	}
	else if ((TOGGLE_INTERFACE == ubCurrentInterface) || (TOGGLE2_INTERFACE == ubCurrentInterface)) {
		// Disable/Enable DQSB_CHK_EN
		M_FIP_CLR_DMA_DQSB_CHK(ubChannel);
		for (uwDataIdx = 0; uwDataIdx < uwReadDataSize; uwDataIdx += 2) {
			M_FIP_SET_DMA_DQSB_CHK(ubChannel);
			uwDatatemp = (U16)pFlaReg[R32_FCTL_PIO_DAT];
			pubData[uwDataIdx] = (U8)(uwDatatemp & 0xFF);
			pubData[uwDataIdx + 1] = (U8)((uwDatatemp >> 8) & 0xFF);
			if ((pFlaReg[R32_FCTL_DMA_CFG] & ulCheckPIOMap) != ulCheckPIOMap) {
				gFlhEnvMP.ulFailedCEBMP |= BIT(gubLogicalCEMapPhysicalCE[ubChannel][ubFlashCE]);
#if (BURNER_MODE_EN)
				guwDebugBurnerErrorCode = ASSERT_FW_INIT_0x0D0E;
#endif
			}
			// Clear DQSB_CHK_EN
			M_FIP_CLR_DMA_DQSB_CHK(ubChannel);
		}
	}
	pFlaReg[R32_FCTL_HS_MODE] |= SET_DAT_DEFAULT;
	FlaCEControl(ubChannel, ubFlashCE, DISABLE);
	FlaResetAllCE();
	for (uwDataIdx = 0; uwDataIdx < 16; uwDataIdx++) {
		ubData[uwDataIdx] = pubData[uwDataIdx * 0x100 + 4];
	}
	memset((void *)(pubBufAddr), 0x00, DEF_512B);
	memcpy((void *)(pubBufAddr), (void *)(&ubData[0]), 16);
#endif /* ((FW_CATEGORY_FLASH == FLASH_BICS3TLC)||(FW_CATEGORY_FLASH == FLASH_BICS4QLC) || (FW_CATEGORY_FLASH == FLASH_BICS4PSLC) || (FW_CATEGORY_FLASH == FLASH_BICS4TLCHDR)) */
}

#endif /* ((FW_CATEGORY_FLASH == FLASH_BICS3TLC)||(FW_CATEGORY_FLASH == FLASH_BICS4QLC) || (FW_CATEGORY_FLASH == FLASH_BICS4PSLC) || (FW_CATEGORY_FLASH == FLASH_BICS4TLCHDR) || (FW_CATEGORY_FLASH == FLASH_BICS5TLC)) */
