#include <string.h>
#include <stdlib.h>
#include "fw_common.h"
#include "burner/Burner.h"
#include "burner/Burner_api.h"
#include "hal/pic/uart/uart.h"
#include "hal/pic/uart/uart_api.h"
#include "burner/Burner_Test_Code.h"
#include "hal/cop0/cop0.h"
#include "hal/cop0/cop0_api.h"
#include "vuc/VUC_api.h"
#include "hal/dmac/dmac_pop_cmd.h"
#include "err_handle/err_handle_copyblock_api.h"


#if ((HOST_MODE == SATA))
#include "hal/sata/sata.h"
#include "hal/sata/sata_api.h"
#include "hal/sata/sata_cmd.h"
#include "hal/sata/sata_vuc.h"
#include "hal/sata/sata_reg.h"
#include "host_handler/hostevt_errhdl_api.h"
#elif (USB == HOST_MODE) /* ((HOST_MODE == SATA)) */
#include "hal/usb/usb.h"
#include "hal/usb/usb_api.h"
#include "hal/usb/usb_cmd.h"
#include "hal/usb/usb_reg.h"
#include "hal/cop1/cop1_api.h"
#include "hal/cop1/cop1_pop_cmd.h"
#endif /* ((HOST_MODE == SATA)) */


U8 Burner_debug_wait, Gary_run_once = TRUE;

#define M_GET_PCA_WITH_LMU(LUN, BLOCK, PAGE, CE, CH, LMU, PLANE, ENTRY, PCA_RULE) (((LUN) << gPCARule_LUN.ubShift[PCA_RULE]) | \
						((BLOCK) << gPCARule_Block.ubShift[PCA_RULE]) | \
						((PAGE) << gPCARule_Page.ubShift[PCA_RULE]) | \
						((PLANE) << gPCARule_Plane.ubShift[PCA_RULE]) | \
						((CE) << gPCARule_Bank.ubShift[PCA_RULE]) | \
						((LMU) << gPCARule_LMU.ubShift[PCA_RULE]) | \
						((CH) << gPCARule_Channel.ubShift[PCA_RULE]) + (ENTRY))

#define WriteDataVerify (TRUE) // prog should be workable if use this flag



void VUC_Program(COP0WriteModeEnum_t COP0WriteMode, U32 ulPCA, U32 Write_buffer)
{


	//UartPrintf("\n [ Enter on %s ] \n", __func__);
	U8	ubFramIdx;
	U32 ulLCA[FRAMES_PER_PAGE] = {0};
	COP0Status_t eCOP0Status;
	COP0WriteSQPara_t WriteSQPara = {{0}};
	BUF_TYPE_t ulBufPara[FRAMES_PER_PAGE] = {{0}};
	FWSetPara_t ulFWSet[FRAMES_PER_PAGE] = {{0}};
	cmd_table_t uoCallbackInfo = {0};


	//write 16K data
	for (ubFramIdx = 0; ubFramIdx < gub4kEntrysPerPlane; ubFramIdx++) {
		ulBufPara[ubFramIdx].A.ulBUF_ADR = Write_buffer + (ubFramIdx * BC_4KB);
		ulLCA[ubFramIdx] = ((U32 *)Write_buffer)[ubFramIdx];
		ulFWSet[ubFramIdx].ubZInfo = MAX_ZINFO;
	}

	COP0API_FillCOP0WriteSQUserData0Para(COP0WriteMode, &WriteSQPara);
	uoCallbackInfo.ulData.ulFLHInfo.btKeepTag = TRUE;
	WriteSQPara.ulPCA.ulAll = ulPCA;
	WriteSQPara.BufVld.ubBufType = BUF_TYPE_A;
	WriteSQPara.BufVld.pulBufInfoPtr = ulBufPara;
	WriteSQPara.pulLCAPtr = &ulLCA[0];
	WriteSQPara.pulFWSetPtr = ulFWSet;
	WriteSQPara.uoWriteConvRsInfo.btConvOnly = TRUE;
	WriteSQPara.UserData0.btSkipError = TRUE;
	WriteSQPara.UserData0.btSLCMode = TRUE;
	WriteSQPara.UserData0.btD1 = TRUE;
	WriteSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;	//Tie in
	WriteSQPara.UserData0.btRUTBps		  = TRUE; //bypass
	WriteSQPara.UserData0.btVBRMPBps	  = TRUE; //bypass
	WriteSQPara.UserData0.btDisableCacheProgram = TRUE;

	eCOP0Status = COP0API_SendWriteSQ(&WriteSQPara, &uoCallbackInfo);
	M_FW_ASSERT(ASSERT_BURNER_0x0175, eCOP0Status.btSendCmdSuccess);

	while (!TagCOP0CheckDoneRelease(WriteSQPara.UserData0.uwTagID)) {
		if (!M_DB_CHECK_EMPTY(DB_ERR_MSG_CQ)) {
			FWErrRecorder();
		}
		if (!M_DB_CHECK_EMPTY(DB_COP0_CQ)) {
			COP0DelegateCmd();
		}
	}
	//UartPrintf("\n [ END of %s ] \n", __func__);
}

void VUC_Erase_VBUnit(int Target_Block)
{
	UartPrintf("\n [ Enter on %s erase for Block=%d] \n", __func__, Target_Block);


	/* ERASE */
	COP0EraseSQPara_t EraseSQPara = {{0}};
	cmd_table_t uoCallbackInfo = {0};
	COP0Status_t eCOP0Status;
	PCA_t ulFWPCA = {0};
	U32 ulPCA;


	int LUN = 0, BLOCK = Target_Block, PAGE = 0, CE = 0, CH = 0, PLANE = 0, FRAME = 0, LMU = 0;

	// fill out Erase parameter
	EraseSQPara.UserData0.uwTagID = TAG_ID_ALLOCATE;
	EraseSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;
	EraseSQPara.UserData0.btSLCMode = FALSE;
	EraseSQPara.UserData0.btD1 = FALSE;


	EraseSQPara.UserData0.btNeedCQ = TRUE;
	EraseSQPara.UserData0.btVBRMPBps = TRUE;
	EraseSQPara.UserData0.btRUTBps = TRUE;
	M_FWPCA_ZBYTE_SET(ulFWPCA.ulAll, gPCAInfo.ubMaxZByte);
	M_FWPCA_CROSSFRAME_SET(ulFWPCA.ulAll, 0);
	uoCallbackInfo.ulData.btKeepTag = TRUE;


	for (PAGE = 0; PAGE < 30; PAGE++) {
		for (CE = 0; CE < gubCENumber; CE++) {
			for (CH = 0; CH < gubPlanesPerBurst; CH++) {
				for (LMU = 0; LMU < gubLMUNumber; LMU++) {
					for (PLANE = 0; PLANE < gubBurstsPerBank; PLANE++) {
						ulPCA = M_GET_PCA_WITH_LMU(LUN, BLOCK, PAGE, CE, CH, LMU, PLANE, FRAME, COP0_PCA_RULE_0); // TLC use rule 0
						//UartPrintf("\n	Lun:%d Block:%d Page:%d ce:%d ch%d LMU:%d plane:%d frame:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, LMU, PLANE, FRAME, ulPCA);

						EraseSQPara.ulPCA.ulAll = ulPCA;
						if ( PLANE == gubBurstsPerBank - 1 ) {
							EraseSQPara.UserData0.ubMultiPlaneMode = MULTIPLANE_END;
						}
						else {
							EraseSQPara.UserData0.ubMultiPlaneMode = MULTIPLANE_ING;
						}

						eCOP0Status = COP0API_SendEraseSQ(&EraseSQPara, &uoCallbackInfo);
						if (eCOP0Status.btSendCmdSuccess != TRUE) {
							UartPrintf("\n\n ------------- erase CMD Fail... ----------------");
							while (1);
						}

					}
				}
			}
		}
		while (TRUE == M_COP0_GET_IDLE()) {
			FWCop0Waiting();
		}

	}


	UartPrintf("\n [ END of %s ] \n", __func__);

}

void VUC_TLC_ERASE_1P(int BLOCK)
{
	UartPrintf("\n [ Enter on %s - COP0 Staus: %x] \n", __func__, M_GET_COP0_STATUS());

	/* ERASE */

	COP0EraseSQPara_t EraseSQPara = {{0}};
	cmd_table_t uoCallbackInfo = {0};
	COP0Status_t eCOP0Status;
	PCA_t ulFWPCA = {0};
	U32 ulPCA;


	int LUN = 0,  PAGE = 0, CE = 0, CH = 0, PLANE = 0;

	// fill out Erase parameter
	EraseSQPara.UserData0.uwTagID = TAG_ID_ALLOCATE;
	EraseSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;
	EraseSQPara.UserData0.btSLCMode = FALSE;
	EraseSQPara.UserData0.btD1 = FALSE;


	EraseSQPara.UserData0.btNeedCQ = TRUE;
	EraseSQPara.UserData0.btVBRMPBps = TRUE;
	EraseSQPara.UserData0.btRUTBps = TRUE;
	M_FWPCA_ZBYTE_SET(ulFWPCA.ulAll, gPCAInfo.ubMaxZByte);
	M_FWPCA_CROSSFRAME_SET(ulFWPCA.ulAll, 0);
	uoCallbackInfo.ulData.btKeepTag = TRUE;


	for (PLANE = 0; PLANE < 1; PLANE++) {

		ulPCA = M_GET_PCA(LUN, BLOCK, PAGE, CE, CH, PLANE, 0, COP0_PCA_RULE_3);
		UartPrintf("\n  Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%x, rule3 \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);
		EraseSQPara.UserData0.ubMultiPlaneMode = MULTIPLANE_NO_USE;
		EraseSQPara.ulPCA.ulAll = ulPCA;

		eCOP0Status = COP0API_SendEraseSQ(&EraseSQPara, &uoCallbackInfo);
		if (eCOP0Status.btSendCmdSuccess != TRUE) {
			UartPrintf("\n\n ------------- erase CMD Fail... ----------------");
			while (1);
		}

	}

	while (!TagCOP0CheckDoneRelease(EraseSQPara.UserData0.uwTagID)) {
		if (!M_DB_CHECK_EMPTY(DB_ERR_MSG_CQ)) {
			FWErrRecorder();
		}
		if (!M_DB_CHECK_EMPTY(DB_COP0_CQ)) {
			COP0DelegateCmd();
		}
	}
	UartPrintf("\n [ END of %s - COP0 Staus: %x] \n", __func__, M_GET_COP0_STATUS());



}

void VUC_TLC_ERASE_4P(int BLOCK)
{
	UartPrintf("\n [ Enter on %s - COP0 Staus: %x] \n", __func__, M_GET_COP0_STATUS());

	/* ERASE */
	COP0EraseSQPara_t EraseSQPara = {{0}};
	cmd_table_t uoCallbackInfo = {0};
	COP0Status_t eCOP0Status;
	PCA_t ulFWPCA = {0};
	U32 ulPCA;
#if 1
	//// PIO DBG  ////
	REG32 *pFlaReg = R32_FCTL_CH[0];
	FlaCEControl(0, 0, ENABLE);
	pFlaReg[R32_FCTL_PIO_CMD] = 0xD5;
	FlaCEControl(0, 0, DISABLE);
	//// PIO DBG  ////
#endif


	int LUN = 0,  PAGE = 5, CE = 0, CH = 0, PLANE = 0;

	// fill out Erase parameter
	EraseSQPara.UserData0.uwTagID = TAG_ID_ALLOCATE;
	EraseSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;
	EraseSQPara.UserData0.btSLCMode = FALSE;
	EraseSQPara.UserData0.btD1 = FALSE;


	EraseSQPara.UserData0.btNeedCQ = TRUE;
	EraseSQPara.UserData0.btVBRMPBps = TRUE;
	EraseSQPara.UserData0.btRUTBps = TRUE;
	M_FWPCA_ZBYTE_SET(ulFWPCA.ulAll, gPCAInfo.ubMaxZByte);
	M_FWPCA_CROSSFRAME_SET(ulFWPCA.ulAll, 0);
	uoCallbackInfo.ulData.btKeepTag = TRUE;

	for (PLANE = 0; PLANE < gubBurstsPerBank; PLANE++) {
		ulPCA = M_GET_PCA(LUN, BLOCK, PAGE, CE, CH, PLANE, 0, COP0_PCA_RULE_3);
		//UartPrintf("\n  Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%x \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);

		EraseSQPara.ulPCA.ulAll = ulPCA;
		if ( PLANE == gubBurstsPerBank - 1 ) {
			EraseSQPara.UserData0.ubMultiPlaneMode = MULTIPLANE_END;
		}
		else {
			EraseSQPara.UserData0.ubMultiPlaneMode = MULTIPLANE_ING;
		}


		eCOP0Status = COP0API_SendEraseSQ(&EraseSQPara, &uoCallbackInfo);
		if (eCOP0Status.btSendCmdSuccess != TRUE) {
			UartPrintf("\n\n ------------- erase CMD Fail... ----------------");
			while (1);
		}

	}


	while (!TagCOP0CheckDoneRelease(EraseSQPara.UserData0.uwTagID)) {
		if (!M_DB_CHECK_EMPTY(DB_ERR_MSG_CQ)) {
			FWErrRecorder();
		}
		if (!M_DB_CHECK_EMPTY(DB_COP0_CQ)) {
			COP0DelegateCmd();
		}
	}



	UartPrintf("\n [ END of %s - COP0 Staus: %x] \n", __func__, M_GET_COP0_STATUS());



}

void VUC_TLC_PROG_1P(int BLOCK) // use normal queue
{
	UartPrintf("\n [ Enter on %s - COP0 Staus: %x] \n", __func__, M_GET_COP0_STATUS());

	U32 Write_buffer = BURNER_VENDOR_BUF_BASE;
	U32 ulPCA;
	U8 ubFramIdx = 0;
	int LUN = 0,  PAGE = 0, CE = 0, CH = 0, PLANE = 0, FRAME = 0, LMU = 0;

	U32 ulLCA[FRAMES_PER_PAGE] = {0};
	COP0Status_t eCOP0Status;
	COP0WriteSQPara_t WriteSQPara = {{0}};
	BUF_TYPE_t ulBufPara[FRAMES_PER_PAGE] = {{0}};
	FWSetPara_t ulFWSet[FRAMES_PER_PAGE] = {{0}};
	cmd_table_t uoCallbackInfo = {0};
#if 1
	//// PIO DBG  ////
	REG32 *pFlaReg = R32_FCTL_CH[0];
	FlaCEControl(0, 0, ENABLE);
	pFlaReg[R32_FCTL_PIO_CMD] = 0xD5;
	FlaCEControl(0, 0, DISABLE);
	//// PIO DBG  ////
#endif


	//fill out write buffer with 16K for each LMU page
	memset((void *)Write_buffer, 0x0, (BC_16KB * gubLMUNumber) );
	U8 buf_num = 0;
	for (LMU = 0; LMU < gubLMUNumber; LMU++) {
		buf_num = LMU;
		U32 addr = Write_buffer + (buf_num * BC_16KB);
		if (buf_num == 0) {
			memset((void *)addr, 0x7878, BC_16KB);
		}
		else {
			memset((void *)addr, 0x1111 * buf_num, BC_16KB);
		}

#if 0 //print out write buffer
		//UartPrintf("\n\n [ 16K Write_buffer for LMU(%d),Write_buffer address=%x ]", LMU, Addr);
		U32 *w_ptr = (U32 *) addr;
		for (int j = 0; j < (4 * 1024 ); j++) { //print 4K data only
			//dump few data to check when copare fail
			if ( (j % 1024 == 0) || (j % 1024 == 1)  ) {
				UartPrintf("\n Write buff, LMU=%d, [ w_ptr=%x ]", LMU, *w_ptr);
			}
			w_ptr++;
		}
#endif

	}

	//Write SQ
	for (PLANE = 0; PLANE < 1; PLANE++) { //Duson
		for (LMU = 0; LMU < gubLMUNumber; LMU++) {
			buf_num = LMU;
			U32 Addr = Write_buffer + (buf_num * BC_16KB);
			for (ubFramIdx = 0; ubFramIdx < gub4kEntrysPerPlane; ubFramIdx++) {
				ulBufPara[ubFramIdx].A.ulBUF_ADR = Addr + (ubFramIdx * BC_4KB);
				ulLCA[ubFramIdx] = ((U32 *)Addr)[ubFramIdx];
				ulFWSet[ubFramIdx].ubZInfo = MAX_ZINFO;
			}

			ulPCA = M_GET_PCA_WITH_LMU(LUN, BLOCK, PAGE, CE, CH, LMU, PLANE, FRAME, COP0_PCA_RULE_0); // TLC use rule 0
			UartPrintf("\n  Lun:%d Block:%d Page:%d ce:%d ch%d LMU:%d plane:%d frame:%d, ulPCA:%x \n", LUN, BLOCK, PAGE, CE, CH, LMU, PLANE, FRAME, ulPCA);

			COP0API_FillCOP0WriteSQUserData0Para(COP0_W_TABLE_PROGRAM, &WriteSQPara);
			uoCallbackInfo.ulData.ulFLHInfo.btKeepTag = TRUE;
			WriteSQPara.ulPCA.ulAll = ulPCA;
			WriteSQPara.BufVld.ubBufType = BUF_TYPE_A;
			WriteSQPara.BufVld.pulBufInfoPtr = ulBufPara;
			WriteSQPara.pulLCAPtr = &ulLCA[0];
			WriteSQPara.pulFWSetPtr = ulFWSet;
			WriteSQPara.uoWriteConvRsInfo.btConvOnly = TRUE;
			WriteSQPara.UserData0.btSkipError = TRUE;
			WriteSQPara.UserData0.btSLCMode = FALSE; //TLC
			WriteSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;  //Tie in
			WriteSQPara.UserData0.btRUTBps        = TRUE; //bypass
			WriteSQPara.UserData0.btVBRMPBps      = TRUE; //bypass
			WriteSQPara.UserData0.btDisableCacheProgram = TRUE;
			WriteSQPara.UserData0.ubMultiPlaneMode = MULTIPLANE_NO_USE;
			//WriteSQPara.UserData0.uwTagID = TAG_ID_ALLOCATE; // Duson
			if ( LMU ==  gubLMUNumber - 1) {
				WriteSQPara.UserData0.ubMultiPlaneMode = MULTIPLANE_END;
			}
			else {
				WriteSQPara.UserData0.ubMultiPlaneMode = MULTIPLANE_ING;
			}

			eCOP0Status = COP0API_SendWriteSQ(&WriteSQPara, &uoCallbackInfo);
			M_FW_ASSERT(ASSERT_BURNER_0x0176, eCOP0Status.btSendCmdSuccess);

			//UartPrintf("\n Duson TLC One Plane Program!");
		}
	}
	int i = 0;
	while (!TagCOP0CheckDoneRelease(WriteSQPara.UserData0.uwTagID)) {
		if (!M_DB_CHECK_EMPTY(DB_ERR_MSG_CQ)) {
			FWErrRecorder();
			//UartPrintf("\n Duson TLC One Plane Err!  times:%d " ,i);
		}
		if (!M_DB_CHECK_EMPTY(DB_COP0_CQ)) {
			COP0DelegateCmd();
			//UartPrintf("\n Duson TLC One Plane Delegate!times:%d ", i);
		}
		i++;
	}

	UartPrintf("\n [ END of %s - COP0 Staus: %x] \n", __func__, M_GET_COP0_STATUS());

}

void VUC_TLC_PROG_4P(int BLOCK) // use normal queue
{
	UartPrintf("\n [ Enter on %s - COP0 Staus: %x] \n", __func__, M_GET_COP0_STATUS());

	U32 Write_buffer = BURNER_VENDOR_BUF_BASE;
	U32 ulPCA;
	U8 ubFramIdx = 0;
	int LUN = 0,  PAGE = 0, CE = 1, CH = 1, PLANE = 0, LMU = 0, FRAME = 0;

	U32 ulLCA[FRAMES_PER_PAGE] = {0};
	COP0Status_t eCOP0Status;
	COP0WriteSQPara_t WriteSQPara = {{0}};
	BUF_TYPE_t ulBufPara[FRAMES_PER_PAGE] = {{0}};
	FWSetPara_t ulFWSet[FRAMES_PER_PAGE] = {{0}};
	cmd_table_t uoCallbackInfo = {0};
#if 1
	//// PIO DBG  ////
	REG32 *pFlaReg = R32_FCTL_CH[0];
	FlaCEControl(0, 0, ENABLE);
	pFlaReg[R32_FCTL_PIO_CMD] = 0xCE;
	FlaCEControl(0, 0, DISABLE);
	//// PIO DBG  ////
#endif

	//fill out write buffer with 16K for each LMU * Plane page
	memset((void *)Write_buffer, 0x0, (gubLMUNumber * gubBurstsPerBank)* BC_16KB);
	U8 buf_num = 0;

	for (PLANE = 0; PLANE < gubBurstsPerBank; PLANE++) {
		for (LMU = 0; LMU < gubLMUNumber; LMU++) {
			buf_num = ( LMU * gubBurstsPerBank) + PLANE;

			U32 addr = Write_buffer + (buf_num * BC_16KB);
			if (buf_num == 0) {
				memset((void *)addr, 0x222222, BC_16KB);
			}
			else {
				memset((void *)addr, 0x1111 * buf_num, BC_16KB);
			}

#if 0 // print out
			U32 *w_ptr = (U32 *) addr;
			for (int j = 0; j < (1 * 1024 ); j++) { //print 4K data only
				//dump few data to check when copare fail
				if ( (j % 1024 == 0) || (j % 1024 == 1)  ) {
					UartPrintf("\nWrite Buff LMU=%d, PLANE=%d , [ w_ptr=%x ] address=%x", LMU, PLANE, *w_ptr, addr);
				}
				w_ptr++;
			}
#endif
		}

	}

	for (PAGE = 77; PAGE < 82; PAGE++) {

		buf_num = 0;

		for (LMU = 0; LMU < gubLMUNumber; LMU++) {
			for (PLANE = 0; PLANE < gubBurstsPerBank; PLANE++) {
				buf_num = ( LMU * gubBurstsPerBank) + PLANE;
				U32 Addr = Write_buffer + (buf_num * BC_16KB);
				U32 *w_ptr = (U32 *) Addr;
				for (int j = 0; j < (1 * 1024 ); j++) { //3個4K (因為U32, 1024=4K )

					//dump few data to check when copare fail
					if ( (j % 1024 == 0) || (j % 1024 == 1)  /*|| (j % 1024 == 2) || (j % 1024 == 3)	|| (j % 1024 == 4)*/ ) {
						UartPrintf("\n Write Buff, LMU=%d, PLANE=%d , [ r_ptr=%x ] address=%x", LMU, PLANE, *w_ptr, Addr);
					}
					w_ptr++;
				}
				for (ubFramIdx = 0; ubFramIdx < gub4kEntrysPerPlane; ubFramIdx++) {
					ulBufPara[ubFramIdx].A.ulBUF_ADR = Addr + (ubFramIdx * BC_4KB);
					ulLCA[ubFramIdx] = ((U32 *)Addr)[ubFramIdx];
					ulFWSet[ubFramIdx].ubZInfo = MAX_ZINFO;
				}
				//UartPrintf("	ulBUF_ADR:%x ", Addr);

				ulPCA = M_GET_PCA_WITH_LMU(LUN, BLOCK, PAGE, CE, CH, LMU, PLANE, FRAME, COP0_PCA_RULE_0); // TLC use rule 0
				//UartPrintf("\n	Lun:%d Block:%d Page:%d ce:%d ch%d LMU:%d plane:%d frame:%d, ulPCA:%x \n", LUN, BLOCK, PAGE, CE, CH, LMU, PLANE, FRAME, ulPCA);

				COP0API_FillCOP0WriteSQUserData0Para(COP0_W_TABLE_PROGRAM, &WriteSQPara);
				uoCallbackInfo.ulData.ulFLHInfo.btKeepTag = TRUE;
				WriteSQPara.ulPCA.ulAll = ulPCA;
				WriteSQPara.BufVld.ubBufType = BUF_TYPE_A;
				WriteSQPara.BufVld.pulBufInfoPtr = ulBufPara;
				WriteSQPara.pulLCAPtr = &ulLCA[0];
				WriteSQPara.pulFWSetPtr = ulFWSet;
				WriteSQPara.uoWriteConvRsInfo.btConvOnly = TRUE;
				WriteSQPara.UserData0.btSkipError = TRUE;
				WriteSQPara.UserData0.btSLCMode = FALSE; //TLC
				WriteSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;  //Tie in
				WriteSQPara.UserData0.btRUTBps        = TRUE; //bypass
				WriteSQPara.UserData0.btVBRMPBps      = TRUE; //bypass
				WriteSQPara.UserData0.btDisableCacheProgram = TRUE;

#if 0 //change to single plane
				if ( LMU ==  gubLMUNumber - 1) {
					WriteSQPara.UserData0.btVUCSinglePlane = TRUE;
				}
#else
				if ( ( LMU ==  gubLMUNumber - 1) && (PLANE ==  gubBurstsPerBank - 1) ) {
					WriteSQPara.UserData0.ubMultiPlaneMode = MULTIPLANE_END;
				}
				else {
					WriteSQPara.UserData0.ubMultiPlaneMode = MULTIPLANE_ING;
				}
#endif


				eCOP0Status = COP0API_SendWriteSQ(&WriteSQPara, &uoCallbackInfo);
				M_FW_ASSERT(ASSERT_BURNER_0x0177, eCOP0Status.btSendCmdSuccess);

			}
		}


		U8 cnt = 10;
		while (TRUE == M_COP0_GET_IDLE()) {
			if (cnt > 0) {
				UartPrintf("\n [COP0 Staus: %x] \n",  M_GET_COP0_STATUS());
				UartPrintf( "\n ubCh=%d ubCE=%d Interrupt Information Register: %x", CH, CE, M_FIP_GET_INT_INFO(CH));
				cnt--;
			}
			FWCop0Waiting();
		}

	}
	UartPrintf("\n [ END of %s - COP0 Staus: %x] \n", __func__, M_GET_COP0_STATUS());
}

void VUC_TLC_READ_1P(int BLOCK)
{

	UartPrintf("\n [ Enter on %s - COP0 Staus: %x] \n", __func__, M_GET_COP0_STATUS());

	int LUN = 0,  PAGE = 0, CE = 0, CH = 0, PLANE = 0, FRAME = 0, LMU = 0;
	U32 Read_buffer = BURNER_VENDOR_BUF_BASE + BC_512KB ;  //Read Buffer
	memset((void *)Read_buffer, 0x0, (BC_16KB * gubLMUNumber) );

	U32 ulPCA;
	U8 buf_num = 0;

	COP0ReadSQPara_t ReadSQPara = {{0}};
	COP0Status_t eCOP0Status;
	BUF_TYPE_t ulBufPara = {0};
	cmd_table_t uoCallbackInfo = {0};
#if 1
	//// PIO DBG  ////
	REG32 *pFlaReg = R32_FCTL_CH[0];
	FlaCEControl(0, 0, ENABLE);
	pFlaReg[R32_FCTL_PIO_CMD] = 0xD6;
	FlaCEControl(0, 0, DISABLE);
	//// PIO DBG  ////
#endif

	//Read SQ
	for (LMU = 0; LMU < gubLMUNumber; LMU++) {
		buf_num = LMU;
		ulPCA = M_GET_PCA_WITH_LMU(LUN, BLOCK, PAGE, CE, CH, LMU, PLANE, FRAME, COP0_PCA_RULE_0); // TLC use rule 0
		//UartPrintf("\nLun:%d Block:%d Page:%d ce:%d ch%d LMU:%d plane:%d frame:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, LMU, PLANE, FRAME, ulPCA);


		M_CLR_COP0_LCA_CMP(); //disable LCA compare
		ulBufPara.A.ulBUF_ADR = Read_buffer + (buf_num * BC_16KB );
		COP0API_FillCOP0ReadSQUserData0Para(COP0_R_DEBUG_UNC, &ReadSQPara);
		//UartPrintf("	ulBUF_ADR:%x ", ulBufPara.A.ulBUF_ADR);

		ReadSQPara.UserData0.btSLCMode = FALSE;
		ReadSQPara.UserData0.btD1 = FALSE;
		ReadSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;
		ReadSQPara.UserData0.btRUTBps = TRUE;
		ReadSQPara.UserData0.btVBRMPBps = TRUE;
		ReadSQPara.UserData0.TagID = TAG_ID_ALLOCATE;
		ReadSQPara.UserData0.btNeedCQ = TRUE;

		M_FWPCA_PCA_SET(ReadSQPara.ulPCA.ulAll, ulPCA);
		ReadSQPara.BufVld.ubBufType = BUF_TYPE_A;
		ReadSQPara.BufVld.pulBufInfoPtr = &ulBufPara;


		eCOP0Status = COP0API_SendReadSQ(&ReadSQPara, &uoCallbackInfo);
		M_FW_ASSERT(ASSERT_BURNER_0x0178, eCOP0Status.btSendCmdSuccess);

	}


	/************************************************************************
	 *                     Wait done                                          *
	 ************************************************************************/

	while (TRUE == M_COP0_GET_IDLE()) {
		FWCop0Waiting();
	}

	/*	while (!TagCOP0CheckDoneRelease(ReadSQPara.UserData0.TagID)) {
			if (!M_DB_CHECK_EMPTY(DB_ERR_MSG_CQ)) {
				FWErrRecorder();
			}
			if (!M_DB_CHECK_EMPTY(DB_COP0_CQ)) {
				COP0DelegateCmd();
			}
		}*/


#if 1 // Print out
	buf_num = 0;
	for (LMU = 0; LMU < gubLMUNumber; LMU++) {
		buf_num =  LMU ;
		U32 addr = Read_buffer + (buf_num * BC_16KB );
		U32 *r_ptr = (U32 *) addr;
		for (int j = 0; j < (1 * 1024 ); j++) { //3個4K (因為U32, 1024=4K )

			//dump few data to check when copare fail
			if ( (j % 1024 == 0) || (j % 1024 == 1)  /*|| (j % 1024 == 2) || (j % 1024 == 3)	|| (j % 1024 == 4)*/ ) {
				UartPrintf("\n Read Buff, LMU=%d, [ r_ptr=%x ] address=%x", LMU, *r_ptr, addr);
			}
			r_ptr++;
		}

	}
#endif

	UartPrintf("\n [ End of %s - COP0 Staus: %x] \n", __func__, M_GET_COP0_STATUS());
}

void VUC_TLC_READ_4P(int BLOCK)
{

	UartPrintf("\n [ Enter on %s - COP0 Staus: %x] \n", __func__, M_GET_COP0_STATUS());

	int LUN = 0,  PAGE = 0, CE = 1, CH = 1, PLANE = 0, FRAME = 0, LMU = 0;
	U32 Read_buffer = BURNER_VENDOR_BUF_BASE + BC_512KB ;  //Read Buffer
	memset((void *)Read_buffer, 0x0, BC_16KB * (gubLMUNumber * gubBurstsPerBank)  );

	U32 ulPCA;
	//U8 buf_num = 0;

	COP0ReadSQPara_t ReadSQPara = {{0}};
	COP0Status_t eCOP0Status;
	BUF_TYPE_t ulBufPara = {0};
	//cmd_table_t uoCallbackInfo = {0};

#if 1
	//// PIO DBG  ////
	REG32 *pFlaReg = R32_FCTL_CH[0];
	FlaCEControl(0, 0, ENABLE);
	pFlaReg[R32_FCTL_PIO_CMD] = 0xD6;
	FlaCEControl(0, 0, DISABLE);
	//// PIO DBG  ////
#endif

	for (PAGE = 77; PAGE < 82; PAGE++ ) {
		//Read SQ
		for (LMU = 0; LMU < gubLMUNumber; LMU++) {
			for (FRAME = 0; FRAME < gub4kEntrysPerPlane; FRAME++) {
				ulPCA = M_GET_PCA_WITH_LMU(LUN, BLOCK, PAGE, CE, CH, LMU, PLANE, FRAME, COP0_PCA_RULE_0); // TLC use rule 0
				//UartPrintf("\n READ Lun:%d Block:%d Page:%d ce:%d ch%d LMU:%d plane:%d frame:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, LMU, PLANE, FRAME, ulPCA);

				M_CLR_COP0_LCA_CMP(); //disable LCA compare
				ulBufPara.A.ulBUF_ADR = Read_buffer + LMU * BC_16KB * 4 + (PLANE * BC_16KB ) + FRAME * BC_4KB; //read 4K data foreach
				COP0API_FillCOP0ReadSQUserData0Para(COP0_R_DEBUG_UNC, &ReadSQPara);

				ReadSQPara.UserData0.btSLCMode = FALSE;
				ReadSQPara.UserData0.btD1 = FALSE;
				ReadSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;
				ReadSQPara.UserData0.btRUTBps = TRUE;
				ReadSQPara.UserData0.btVBRMPBps = TRUE;
				ReadSQPara.UserData0.TagID = TAG_ID_ALLOCATE;
				ReadSQPara.UserData0.btZIP = FALSE;


				M_FWPCA_PCA_SET(ReadSQPara.ulPCA.ulAll, ulPCA);
				ReadSQPara.BufVld.ubBufType = BUF_TYPE_A;
				ReadSQPara.BufVld.pulBufInfoPtr = &ulBufPara;
				//uoCallbackInfo.ulData.ulFLHInfo.btKeepTag = TRUE;

				eCOP0Status = COP0API_SendReadSQ(&ReadSQPara, NULL);
				M_FW_ASSERT(ASSERT_BURNER_0x0179, eCOP0Status.btSendCmdSuccess);

			}

		}


		/************************************************************************
		 *                     Wait done                                          *
		 ************************************************************************/

		/*while (TRUE == M_COP0_GET_IDLE()) {
			FWCop0Waiting();
		}*/
		//UartPrintf("\n Duson wait");
		//int i = 0;
		while (!TagCOP0CheckDoneRelease(ReadSQPara.UserData0.TagID)) {
			if (!M_DB_CHECK_EMPTY(DB_ERR_MSG_CQ)) {
				FWErrRecorder();
				//UartPrintf("\n Duson TLC One Plane Err!  times:%d ", i);
			}
			if (!M_DB_CHECK_EMPTY(DB_COP0_CQ)) {
				COP0DelegateCmd();
				//UartPrintf("\n Duson TLC One Plane Delegate!times:%d ", i);
			}

		}
	}
	//****************//compare
	//Step 5. verify
	U8 cmp_res = 1;
	U32 Write_buffer = BURNER_VENDOR_BUF_BASE;
	cmp_res = memcmp((void *)Write_buffer, (void *)Read_buffer, (BC_4KB * gubLMUNumber));

	if (cmp_res == 0) {
		UartPrintf("\n [ Compare Pass ! ulPCA:%d] ", ulPCA);
	}
	else {
		//UartPrintf("\nPage:%d  BLOCK:%d  ce:%d  ch%d	plane:%d ulPCA:%d \n", PAGE, BLOCK, CE, CH, PLANE, ulPCA);
		UartPrintf("\n [ Compare Fail ! ]\n");
		UartPrintf("\nAt Page:%d  BLOCK:%d	ce:%d  ch%d  plane:%d ulPCA:%d \n", PAGE, BLOCK, CE, CH, PLANE, ulPCA);

		//debug for dump detail
		U32 *r_ptr;
		U32 *w_ptr;
		r_ptr = (U32 *) Read_buffer; // 4 byte
		w_ptr = (U32 *) Write_buffer;
		//UartPrintf("\n [ after r_ptr -> Read_buffer , Read_buffer address=%x	] \n", Read_buffer);
		for (int j = 1; j < 4 * 1024; j++) { //4個4K (4*1024)

			if (*r_ptr != *w_ptr) {
				//dump few data to check when copare fail
				if ( (j % 1024 == 0) || (j % 1024 == 1)  || (j % 1024 == 2) || (j % 1024 == 3)	|| (j % 1024 == 4) ) {
					UartPrintf("\n j=%d Diff, [ r_ptr=%x ],[ w_ptr=%x ]  \n", j, *r_ptr, *w_ptr);
				}
				//UartPrintf("\n j=%d Diff, [ r_ptr=%x ],[ w_ptr=%x ]  \n", j, *r_ptr, *w_ptr);
			}
			else {
				if ( (j % 1024 == 0) || (j % 1024 == 1)  || (j % 1024 == 2) || (j % 1024 == 3)	|| (j % 1024 == 4) ) {
					UartPrintf("\n j=%d Same, [ r_ptr=%x ],[ w_ptr=%x ]  \n", j, *r_ptr, *w_ptr);
				}

			}
			r_ptr++;
			w_ptr++;
		}
		UartPrintf("\n [ debug Done ]\n");
		while (1);
	}

#if 0 // Print out
	buf_num = 0;
	for (LMU = 0; LMU < gubLMUNumber; LMU++) {
		for (PLANE = 0; PLANE < gubBurstsPerBank; PLANE++) {
			buf_num = ( LMU * gubBurstsPerBank) + PLANE;
			U32 addr = Read_buffer + (buf_num * BC_16KB );
			U32 *r_ptr = (U32 *) addr;
			for (int j = 0; j < (1 * 1024 ); j++) { //3個4K (因為U32, 1024=4K )

				//dump few data to check when copare fail
				if ( (j % 1024 == 0) || (j % 1024 == 1)  /*|| (j % 1024 == 2) || (j % 1024 == 3)	|| (j % 1024 == 4)*/ ) {
					UartPrintf("\n Read Buff, LMU=%d, PLANE=%d , [ r_ptr=%x ] address=%x", LMU, PLANE, *r_ptr, addr);
				}
				r_ptr++;
			}

		}
	}
#endif

	UartPrintf("\n [ End of %s - COP0 Staus: %x] \n", __func__, M_GET_COP0_STATUS());
}


void VUC_TLC_Erase_Prog_Read_Verify(int TARGET_BLOCK)
{
	//Step 1. ERASE
	COP0EraseSQPara_t EraseSQPara = {{0}};
	cmd_table_t uoCallbackInfo = {0};
	COP0Status_t eCOP0Status;
	PCA_t ulFWPCA = {0};
	U32 ulPCA;

	if (TARGET_BLOCK <= 0) {
		UartPrintf("\n\n ------------- Error input of Block num (%d) ----------------", TARGET_BLOCK);
		return ;
	}
	int LUN = 0,  PAGE = 0, CE = 0, CH = 0, PLANE = 0, FRAME = 0, LMU = 0;




	for (PAGE = 0; PAGE < 2; PAGE++) {
		for (CE = 0; CE < gubCENumber; CE++) {
			for (CH = 0; CH < gubPlanesPerBurst; CH++) {
				for (PLANE = 0; PLANE < gubBurstsPerBank; PLANE++) {
					for (LMU = 0; LMU < gubLMUNumber; LMU++) {
						ulPCA = M_GET_PCA_WITH_LMU(LUN, TARGET_BLOCK, PAGE, CE, CH, LMU, PLANE, FRAME, COP0_PCA_RULE_0); // TLC use rule 0
						//UartPrintf("\n  Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d, rule:%d \n", LUN, TARGET_BLOCK, PAGE, CE, CH, PLANE, ulPCA, COP0_PCA_RULE_0);
						// fill out Erase parameter
						EraseSQPara.UserData0.uwTagID = TAG_ID_ALLOCATE;
						EraseSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;
						EraseSQPara.UserData0.btSLCMode = FALSE;
						EraseSQPara.UserData0.btD1 = FALSE;

						EraseSQPara.UserData0.btNeedCQ = TRUE;
						EraseSQPara.UserData0.btVBRMPBps = TRUE;
						EraseSQPara.UserData0.btRUTBps = TRUE;
						M_FWPCA_ZBYTE_SET(ulFWPCA.ulAll, gPCAInfo.ubMaxZByte);
						M_FWPCA_CROSSFRAME_SET(ulFWPCA.ulAll, 0);
						uoCallbackInfo.ulData.btKeepTag = TRUE;

						EraseSQPara.UserData0.ubMultiPlaneMode = MULTIPLANE_NO_USE;
						EraseSQPara.ulPCA.ulAll = ulPCA;

						eCOP0Status = COP0API_SendEraseSQ(&EraseSQPara, &uoCallbackInfo);
						if (eCOP0Status.btSendCmdSuccess != TRUE) {
							UartPrintf("\n\n ------------- erase CMD Fail... ----------------");
							while (1);
						}

						while (!TagCOP0CheckDoneRelease(EraseSQPara.UserData0.uwTagID)) {
							if (!M_DB_CHECK_EMPTY(DB_ERR_MSG_CQ)) {
								FWErrRecorder();
							}
							if (!M_DB_CHECK_EMPTY(DB_COP0_CQ)) {
								COP0DelegateCmd();
							}
						}
					}
				}
			}
		}
	}
	while (TRUE == M_COP0_GET_IDLE()) {
		FWCop0Waiting();
	}


	UartPrintf("\n\n ----------- Start prog & read ----------------");
	U8 ubFramIdx = 0;
	U8 cmp_res = 1;

	LUN = 0,  PAGE = 0, CE = 0, CH = 0, PLANE = 0, FRAME = 0, LMU = 0;

	U32 ulLCA[FRAMES_PER_PAGE] = {0};
	COP0WriteSQPara_t WriteSQPara = {{0}};
	BUF_TYPE_t prog_ulBufPara[FRAMES_PER_PAGE] = {{0}};
	FWSetPara_t prog_ulFWSet[FRAMES_PER_PAGE] = {{0}};
	cmd_table_t prog_uoCallbackInfo = {0};

	COP0ReadSQPara_t ReadSQPara = {{0}};
	BUF_TYPE_t read_ulBufPara = {0};

	U32 Write_buffer = BURNER_VENDOR_BUF_BASE;
	memset((void *)Write_buffer, 0x00, (gubLMUNumber * BC_16KB) );

	U32 Read_buffer = BURNER_VENDOR_BUF_BASE + BC_256KB ;
	memset((void *)Read_buffer, 0x00, (gubLMUNumber * BC_4KB) );

#if 1

	for (PAGE = 0; PAGE < 2; PAGE++) {
		for (CE = 0; CE < gubCENumber; CE++) {
			for (CH = 0; CH < gubPlanesPerBurst; CH++) {
				for (PLANE = 0; PLANE < gubBurstsPerBank; PLANE++) {
#endif
					//Step 2.PROG
					for (LMU = 0; LMU < gubLMUNumber; LMU++) {
						// fill out write buffer into buf para by frame(4K) for each LMU page
						memset((void *)Write_buffer, 0x78, BC_16KB );
						for (ubFramIdx = 0; ubFramIdx < gub4kEntrysPerPlane; ubFramIdx++) {
							prog_ulBufPara[ubFramIdx].A.ulBUF_ADR = (Write_buffer) + (ubFramIdx * BC_4KB);
							ulLCA[ubFramIdx] = ((U32 *)Write_buffer)[ubFramIdx];
							prog_ulFWSet[ubFramIdx].ubZInfo = MAX_ZINFO;
						}

						ulPCA = M_GET_PCA_WITH_LMU(LUN, TARGET_BLOCK, PAGE, CE, CH, LMU, PLANE, FRAME, COP0_PCA_RULE_0); // TLC use rule 0
						//UartPrintf("\n PROG Lun:%d Block:%d Page:%d ce:%d ch%d LMU:%d plane:%d frame:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, LMU, PLANE, FRAME, ulPCA);


						COP0API_FillCOP0WriteSQUserData0Para(COP0_W_TABLE_PROGRAM, &WriteSQPara);
						prog_uoCallbackInfo.ulData.ulFLHInfo.btKeepTag = TRUE;
						WriteSQPara.ulPCA.ulAll = ulPCA;
						WriteSQPara.BufVld.ubBufType = BUF_TYPE_A;
						WriteSQPara.BufVld.pulBufInfoPtr = prog_ulBufPara;
						WriteSQPara.pulLCAPtr = &ulLCA[0];
						WriteSQPara.pulFWSetPtr = prog_ulFWSet;
						WriteSQPara.uoWriteConvRsInfo.btConvOnly = TRUE;
						WriteSQPara.UserData0.btSkipError = TRUE;
						WriteSQPara.UserData0.btSLCMode = FALSE; //TLC
						WriteSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;	//Tie in
						WriteSQPara.UserData0.btRUTBps		  = TRUE; //bypass
						WriteSQPara.UserData0.btVBRMPBps	  = TRUE; //bypass
						WriteSQPara.UserData0.btDisableCacheProgram = TRUE;
						WriteSQPara.UserData0.ubMultiPlaneMode = MULTIPLANE_NO_USE;

						if ( LMU ==  gubLMUNumber - 1) {
							WriteSQPara.UserData0.btVUCSinglePlane = TRUE;
						}

						eCOP0Status = COP0API_SendWriteSQ(&WriteSQPara, &prog_uoCallbackInfo);
						M_FW_ASSERT(ASSERT_BURNER_0x017A, eCOP0Status.btSendCmdSuccess);
					}
					while (TRUE == M_COP0_GET_IDLE()) {
						FWCop0Waiting();
					}



					//Step 3.Read
					for (LMU = 0; LMU < gubLMUNumber; LMU++) {
						ulPCA = M_GET_PCA_WITH_LMU(LUN, TARGET_BLOCK, PAGE, CE, CH, LMU, PLANE, FRAME, COP0_PCA_RULE_0); // TLC use rule 0
						//UartPrintf("\n READ Lun:%d Block:%d Page:%d ce:%d ch%d LMU:%d plane:%d frame:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, LMU, PLANE, FRAME, ulPCA);

						M_CLR_COP0_LCA_CMP(); //disable LCA compare
						read_ulBufPara.A.ulBUF_ADR = Read_buffer + (BC_4KB * LMU ); //read 4K data foreach
						COP0API_FillCOP0ReadSQUserData0Para(COP0_R_DEBUG_UNC, &ReadSQPara);

						ReadSQPara.UserData0.btSLCMode = FALSE;
						ReadSQPara.UserData0.btD1 = FALSE;
						ReadSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;
						ReadSQPara.UserData0.btRUTBps = TRUE;
						ReadSQPara.UserData0.btVBRMPBps = TRUE;
						ReadSQPara.UserData0.TagID = TAG_ID_ALLOCATE;


						M_FWPCA_PCA_SET(ReadSQPara.ulPCA.ulAll, ulPCA);
						ReadSQPara.BufVld.ubBufType = BUF_TYPE_A;
						ReadSQPara.BufVld.pulBufInfoPtr = &read_ulBufPara;

						eCOP0Status = COP0API_SendReadSQ(&ReadSQPara, NULL);
						M_FW_ASSERT(ASSERT_BURNER_0x017B, eCOP0Status.btSendCmdSuccess);



					}

					while (TRUE == M_COP0_GET_IDLE()) {
						FWCop0Waiting();
					}
#if 0
					U32 *r_ptr2;
					r_ptr2 = (U32 *) Read_buffer; // 4 byte
					UartPrintf("\n [ after r_ptr -> Read_buffer , Read_buffer address=%x	] \n", Read_buffer);
					for (int j = 0; j < (gubLMUNumber * 1024 ); j++) { //print 4個4K (因為U32, 1024=4K )

						//dump few data to check when copare fail
						if ( (j % 1024 == 0) || (j % 1024 == 1) /* || (j % 1024 == 2) || (j % 1024 == 3)	|| (j % 1024 == 4)*/ ) {
							UartPrintf("\n j=%d , [ r_ptr=%x ]	", j, *r_ptr2);
						}
						r_ptr2++;
					}
#endif

					//Step 5. verify
					cmp_res = memcmp((void *)Write_buffer, (void *)Read_buffer, (BC_4KB * gubLMUNumber));

					if (cmp_res == 0) {
						UartPrintf("\n [ Compare Pass ! ulPCA:%d] ", ulPCA);
					}
					else {
						//UartPrintf("\nPage:%d  BLOCK:%d  ce:%d  ch%d  plane:%d ulPCA:%d \n", PAGE, BLOCK, CE, CH, PLANE, ulPCA);
						UartPrintf("\n [ Compare Fail ! ]\n");
						UartPrintf("\nAt Page:%d  BLOCK:%d  ce:%d  ch%d  plane:%d ulPCA:%d \n", PAGE, TARGET_BLOCK, CE, CH, PLANE, ulPCA);

						//debug for dump detail
						U32 *r_ptr;
						U32 *w_ptr;
						r_ptr = (U32 *) Read_buffer; // 4 byte
						w_ptr = (U32 *) Write_buffer;
						//UartPrintf("\n [ after r_ptr -> Read_buffer , Read_buffer address=%x  ] \n", Read_buffer);
						for (int j = 1; j < 4 * 1024; j++) { //4個4K (4*1024)

							if (*r_ptr != *w_ptr) {
								//dump few data to check when copare fail
								if ( (j % 1024 == 0) || (j % 1024 == 1)  || (j % 1024 == 2) || (j % 1024 == 3)  || (j % 1024 == 4) ) {
									UartPrintf("\n j=%d Diff, [ r_ptr=%x ],[ w_ptr=%x ]  \n", j, *r_ptr, *w_ptr);
								}
								//UartPrintf("\n j=%d Diff, [ r_ptr=%x ],[ w_ptr=%x ]  \n", j, *r_ptr, *w_ptr);
							}
							else {
								if ( (j % 1024 == 0) || (j % 1024 == 1)  || (j % 1024 == 2) || (j % 1024 == 3)  || (j % 1024 == 4) ) {
									UartPrintf("\n j=%d Same, [ r_ptr=%x ],[ w_ptr=%x ]  \n", j, *r_ptr, *w_ptr);
								}

							}
							r_ptr++;
							w_ptr++;
						}
						UartPrintf("\n [ debug Done ]\n");
						while (1);
					}

#if 1
				}
			}
		}
	}

#endif

	if (cmp_res == 0) {
		UartPrintf("\n [ ALL Compare Pass ! ]\n");
	}

}


void VUC_SLC_ERASE_1P(int BLOCK)
{
	UartPrintf("\n [ Enter on %s ] \n", __func__);

	/* ERASE */
	COP0EraseSQPara_t EraseSQPara = {{0}};
	cmd_table_t uoCallbackInfo = {0};
	COP0Status_t eCOP0Status;
	PCA_t ulFWPCA = {0};
	U32 ulPCA;


	int LUN = 0, PAGE = 0, CE = 0, CH = 0, PLANE = 0;

	// fill out Erase parameter
	EraseSQPara.UserData0.uwTagID = TAG_ID_ALLOCATE;
	EraseSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;
	EraseSQPara.UserData0.btSLCMode = TRUE;
	EraseSQPara.UserData0.btD1 = TRUE;
	EraseSQPara.UserData0.btNeedCQ = TRUE;
	EraseSQPara.UserData0.btVBRMPBps = TRUE;
	EraseSQPara.UserData0.btRUTBps = TRUE;
	M_FWPCA_ZBYTE_SET(ulFWPCA.ulAll, gPCAInfo.ubMaxZByte);
	M_FWPCA_CROSSFRAME_SET(ulFWPCA.ulAll, 0);
	uoCallbackInfo.ulData.btKeepTag = TRUE;

	for (PLANE = 0; PLANE < 1; PLANE++) {

		ulPCA = M_GET_PCA(LUN, BLOCK, PAGE, CE, CH, PLANE, 0, COP0_PCA_RULE_3);
		UartPrintf("\n  Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%x, rule3 \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);
		EraseSQPara.UserData0.ubMultiPlaneMode = MULTIPLANE_NO_USE;
		EraseSQPara.ulPCA.ulAll = ulPCA;

		eCOP0Status = COP0API_SendEraseSQ(&EraseSQPara, &uoCallbackInfo);
		if (eCOP0Status.btSendCmdSuccess != TRUE) {
			UartPrintf("\n\n ------------- erase CMD Fail... ----------------");
			while (1);
		}

	}

	while (!TagCOP0CheckDoneRelease(EraseSQPara.UserData0.uwTagID)) {
		if (!M_DB_CHECK_EMPTY(DB_ERR_MSG_CQ)) {
			FWErrRecorder();
		}
		if (!M_DB_CHECK_EMPTY(DB_COP0_CQ)) {
			COP0DelegateCmd();
		}
	}

	UartPrintf("\n [ END of %s - COP0 Staus: %x] \n", __func__, M_GET_COP0_STATUS());


}

void VUC_SLC_ERASE_4P(int BLOCK)
{

	UartPrintf("\n [ Enter %s ] \n", __func__);

	/* ERASE */
	COP0EraseSQPara_t EraseSQPara = {{0}};
	cmd_table_t uoCallbackInfo = {0};
	COP0Status_t eCOP0Status;
	PCA_t ulFWPCA = {0};
	U32 ulPCA;


	int LUN = 0, PAGE = 0, CE = 0, CH = 0, PLANE = 0;

	// fill out Erase parameter
	EraseSQPara.UserData0.uwTagID = TAG_ID_ALLOCATE;
	EraseSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;
	EraseSQPara.UserData0.btSLCMode = TRUE;
	EraseSQPara.UserData0.btD1 = TRUE;

	EraseSQPara.UserData0.btNeedCQ = TRUE;
	EraseSQPara.UserData0.btVBRMPBps = TRUE;
	EraseSQPara.UserData0.btRUTBps = TRUE;
	M_FWPCA_ZBYTE_SET(ulFWPCA.ulAll, gPCAInfo.ubMaxZByte);
	M_FWPCA_CROSSFRAME_SET(ulFWPCA.ulAll, 0);
	uoCallbackInfo.ulData.btKeepTag = TRUE;

	for (PLANE = 0; PLANE < gubBurstsPerBank; PLANE++) {
		ulPCA = M_GET_PCA(LUN, BLOCK, PAGE, CE, CH, PLANE, 0, COP0_PCA_RULE_3);
		//UartPrintf("\n  Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%x \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);

		EraseSQPara.ulPCA.ulAll = ulPCA;
		if ( PLANE == gubBurstsPerBank - 1 ) {
			EraseSQPara.UserData0.ubMultiPlaneMode = MULTIPLANE_END;
		}
		else {
			EraseSQPara.UserData0.ubMultiPlaneMode = MULTIPLANE_ING;
		}


		eCOP0Status = COP0API_SendEraseSQ(&EraseSQPara, &uoCallbackInfo);
		if (eCOP0Status.btSendCmdSuccess != TRUE) {
			UartPrintf("\n\n ------------- erase CMD Fail... ----------------");
			while (1);
		}

	}

	while (!TagCOP0CheckDoneRelease(EraseSQPara.UserData0.uwTagID)) {
		if (!M_DB_CHECK_EMPTY(DB_ERR_MSG_CQ)) {
			FWErrRecorder();
		}
		if (!M_DB_CHECK_EMPTY(DB_COP0_CQ)) {
			COP0DelegateCmd();
		}
	}
	UartPrintf("\n [ END of %s - COP0 Staus: %x] \n", __func__, M_GET_COP0_STATUS());


}


void VUC_SLC_PROC_1P(int BLOCK)
{

	UartPrintf("\n [ Enter on %s ] \n", __func__);

	COP0WriteSQPara_t WriteSQPara = {{0}};
	cmd_table_t uoCallbackInfo = {0};
	COP0Status_t eCOP0Status;

	BUF_TYPE_t ulBufPara[FRAMES_PER_PAGE] = {{0}};
	FWSetPara_t ulFWSet[FRAMES_PER_PAGE] = {{0}};
	U32 ulPCA;
	U32 ulLCA[FRAMES_PER_PAGE] = {0};
	U32 Write_buffer = BURNER_VENDOR_BUF_BASE;
	U8	ubFramIdx;
	int LUN = 0, PAGE = 4, CE = 0, CH = 0, PLANE = 0;

	//fill out write buffer with 16K
	memset((void *)Write_buffer, 0x0, (BC_16KB) );

	U8 buf_num = 0;
	for (PLANE = 0; PLANE < 1; PLANE++) {
		buf_num = PLANE;
		U32 addr = Write_buffer + (buf_num * BC_16KB);

		memset((void *)addr, 0x8080, BC_16KB);
#if 1 //print out write buffer
		UartPrintf(" Print 1st byte on each 4k");
		U32 *w_ptr = (U32 *) addr;
		for (int j = 0; j < (4 * 1024 ); j++) { //print 4K data only
			//dump few data to check when copare fail
			if ( (j % 1024 == 1) ) {
				UartPrintf("\n Write buff, %d=[ w_ptr=%x ]", j, *w_ptr);
			}
			w_ptr++;
		}
#endif
	}


	/*PROG*/
	for (PLANE = 0; PLANE < 1; PLANE++) {
		buf_num = PLANE;
		U32 Addr = Write_buffer + (buf_num * BC_16KB);
		for (ubFramIdx = 0; ubFramIdx < gub4kEntrysPerPlane; ubFramIdx++) {
			ulBufPara[ubFramIdx].A.ulBUF_ADR = Addr + (ubFramIdx * BC_4KB);
			ulLCA[ubFramIdx] = ((U32 *)Addr)[ubFramIdx];
			ulFWSet[ubFramIdx].ubZInfo = MAX_ZINFO;
		}

		ulPCA = M_GET_PCA(LUN, BLOCK, PAGE, CE, CH, PLANE, 0, COP0_PCA_RULE_3);
		UartPrintf("\n Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%x \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);

		COP0API_FillCOP0WriteSQUserData0Para(COP0_W_TABLE_PROGRAM, &WriteSQPara);
		uoCallbackInfo.ulData.ulFLHInfo.btKeepTag = TRUE;
		WriteSQPara.ulPCA.ulAll = ulPCA;
		WriteSQPara.BufVld.ubBufType = BUF_TYPE_A;
		WriteSQPara.BufVld.pulBufInfoPtr = ulBufPara;
		WriteSQPara.pulLCAPtr = &ulLCA[0];
		WriteSQPara.pulFWSetPtr = ulFWSet;
		WriteSQPara.uoWriteConvRsInfo.btConvOnly = TRUE;
		WriteSQPara.UserData0.btSkipError = TRUE;
		WriteSQPara.UserData0.btSLCMode = TRUE;
		WriteSQPara.UserData0.btD1 = TRUE;
		WriteSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;  //Tie in
		WriteSQPara.UserData0.btRUTBps        = TRUE; //bypass
		WriteSQPara.UserData0.btVBRMPBps      = TRUE; //bypass
		//WriteSQPara.UserData0.btDisableCacheProgram = TRUE;

		eCOP0Status = COP0API_SendWriteSQ(&WriteSQPara, &uoCallbackInfo);
		M_FW_ASSERT(ASSERT_BURNER_0x017C, eCOP0Status.btSendCmdSuccess);
	}

	while (!TagCOP0CheckDoneRelease(WriteSQPara.UserData0.uwTagID)) {
		if (!M_DB_CHECK_EMPTY(DB_ERR_MSG_CQ)) {
			FWErrRecorder();
		}
		if (!M_DB_CHECK_EMPTY(DB_COP0_CQ)) {
			COP0DelegateCmd();
		}
	}

	UartPrintf("\n [ END of %s - COP0 Staus: %x] \n", __func__, M_GET_COP0_STATUS());

}


void VUC_SLC_PROC_4P(int BLOCK)
{

	UartPrintf("\n [ Enter on %s ] \n", __func__);

	COP0WriteSQPara_t WriteSQPara = {{0}};
	cmd_table_t uoCallbackInfo = {0};
	COP0Status_t eCOP0Status;

	BUF_TYPE_t ulBufPara[FRAMES_PER_PAGE] = {{0}};
	FWSetPara_t ulFWSet[FRAMES_PER_PAGE] = {{0}};
	U32 ulPCA;
	U32 ulLCA[FRAMES_PER_PAGE] = {0};
	U32 Write_buffer = BURNER_VENDOR_BUF_BASE;
	U8	ubFramIdx;
	int LUN = 0, PAGE = 0, CE = 0, CH = 0, PLANE = 0;

	//fill out write buffer with 16K
	memset((void *)Write_buffer, 0x0, (BC_16KB) );

	U8 buf_num = 0;
	for (PLANE = 0; PLANE < gubBurstsPerBank; PLANE++) {
		buf_num = PLANE;
		U32 addr = Write_buffer + (buf_num * BC_16KB);

		if (buf_num == 0) {
			memset((void *)addr, 0x66778899, BC_16KB);
		}
		else {
			memset((void *)addr, 0x1111 * buf_num, BC_16KB);
		}

#if 1 //print out write buffer
		UartPrintf("\n\nPrint 1st byte on each 4k PLANE:%d", PLANE);
		U32 *w_ptr = (U32 *) addr;
		for (int j = 0; j < (4 * 1024 ); j++) { //print 4K data only
			//dump few data to check when copare fail
			if ( (j % 1024 == 1) ) {
				UartPrintf("\n Write buff, %d=[ w_ptr=%x ]", j, *w_ptr);
			}
			w_ptr++;
		}
#endif
	}


	/*PROG*/
	for (PLANE = 0; PLANE < gubBurstsPerBank; PLANE++) {
		buf_num = PLANE;
		U32 Addr = Write_buffer + (buf_num * BC_16KB);
		for (ubFramIdx = 0; ubFramIdx < gub4kEntrysPerPlane; ubFramIdx++) {
			ulBufPara[ubFramIdx].A.ulBUF_ADR = Addr + (ubFramIdx * BC_4KB);
			ulLCA[ubFramIdx] = ((U32 *)Addr)[ubFramIdx];
			ulFWSet[ubFramIdx].ubZInfo = MAX_ZINFO;
		}

		ulPCA = M_GET_PCA(LUN, BLOCK, PAGE, CE, CH, PLANE, 0, COP0_PCA_RULE_3);
		//UartPrintf("\n Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%x \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);

		COP0API_FillCOP0WriteSQUserData0Para(COP0_W_TABLE_PROGRAM, &WriteSQPara);
		uoCallbackInfo.ulData.ulFLHInfo.btKeepTag = TRUE;
		WriteSQPara.ulPCA.ulAll = ulPCA;
		WriteSQPara.BufVld.ubBufType = BUF_TYPE_A;
		WriteSQPara.BufVld.pulBufInfoPtr = ulBufPara;
		WriteSQPara.pulLCAPtr = &ulLCA[0];
		WriteSQPara.pulFWSetPtr = ulFWSet;
		WriteSQPara.uoWriteConvRsInfo.btConvOnly = TRUE;
		WriteSQPara.UserData0.btSkipError = TRUE;
		WriteSQPara.UserData0.btSLCMode = TRUE;
		WriteSQPara.UserData0.btD1 = TRUE;
		WriteSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;  //Tie in
		WriteSQPara.UserData0.btRUTBps        = TRUE; //bypass
		WriteSQPara.UserData0.btVBRMPBps      = TRUE; //bypass
		WriteSQPara.UserData0.btDisableCacheProgram = TRUE;

		eCOP0Status = COP0API_SendWriteSQ(&WriteSQPara, &uoCallbackInfo);
		M_FW_ASSERT(ASSERT_BURNER_0x017D, eCOP0Status.btSendCmdSuccess);
	}

	while (!TagCOP0CheckDoneRelease(WriteSQPara.UserData0.uwTagID)) {
		if (!M_DB_CHECK_EMPTY(DB_ERR_MSG_CQ)) {
			FWErrRecorder();
		}
		if (!M_DB_CHECK_EMPTY(DB_COP0_CQ)) {
			COP0DelegateCmd();
		}
	}

	UartPrintf("\n [ END of %s - COP0 Staus: %x] \n", __func__, M_GET_COP0_STATUS());

}


void VUC_SLC_READ_1P(int BLOCK)
{
	UartPrintf("\n [ Enter on %s ]\n", __func__);

	COP0Status_t eCOP0Status;
	U32 Read_buffer = BURNER_VENDOR_BUF_BASE + BC_512KB;
	U32 ulLocalPCA = INVALID_PCA;
	int LUN = 0, PAGE = 0, CE = 0, CH = 0, PLANE = 0;
	cmd_table_t uoCallbackInfo = {0};//Duson
	COP0ReadSQPara_t ReadSQPara = {{0}};
	BUF_TYPE_t ulBufPara = {0};

	memset((void *)Read_buffer, 0x0, BC_16KB);
	for (PLANE = 0; PLANE < 1; PLANE++) {

		ulLocalPCA = M_GET_PCA(LUN, BLOCK, PAGE, CE, CH, PLANE, 0, COP0_PCA_RULE_3);
		UartPrintf("\n Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%x \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulLocalPCA);

		M_CLR_COP0_LCA_CMP(); //disable LCA compare
		ulBufPara.A.ulBUF_ADR = Read_buffer + (PLANE * BC_16KB ); //read 4K data foreach plane only
		COP0API_FillCOP0ReadSQUserData0Para(COP0_R_DEBUG_UNC, &ReadSQPara);

		ReadSQPara.UserData0.btSLCMode = TRUE;
		ReadSQPara.UserData0.btD1 = TRUE;
		ReadSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;
		ReadSQPara.UserData0.btRUTBps = TRUE;
		ReadSQPara.UserData0.btVBRMPBps = TRUE;
		ReadSQPara.UserData0.btNeedCQ = TRUE;

		ReadSQPara.UserData0.TagID = TAG_ID_ALLOCATE;//Duson

		M_FWPCA_PCA_SET(ReadSQPara.ulPCA.ulAll, ulLocalPCA);
		ReadSQPara.BufVld.ubBufType = BUF_TYPE_A;
		ReadSQPara.BufVld.pulBufInfoPtr = &ulBufPara;

		eCOP0Status = COP0API_SendReadSQ(&ReadSQPara, &uoCallbackInfo);//Duson
		M_FW_ASSERT(ASSERT_BURNER_0x017E, eCOP0Status.btSendCmdSuccess);

		//ulLocalPCA = ulLocalPCA + gub4kEntrysPerPlane ; //increase PCA by 4 ( 4 frame per plane)

	}

	/************************************************************************
	 *                     Wait done                                          *
	 ************************************************************************/
	while (!TagCOP0CheckDoneRelease(ReadSQPara.UserData0.TagID)) {
		if (!M_DB_CHECK_EMPTY(DB_ERR_MSG_CQ)) {
			FWErrRecorder();
		}
		if (!M_DB_CHECK_EMPTY(DB_COP0_CQ)) {
			COP0DelegateCmd();
		}
	}

#if 1 // Print out
	UartPrintf(" Print 1st byte on each 4k (Read 4K only)");
	U8 buf_num = 0;
	for (PLANE = 0; PLANE < 1; PLANE++) {
		buf_num =  PLANE ;
		U32 addr = Read_buffer + (buf_num * BC_16KB );
		U32 *r_ptr = (U32 *) addr;
		for (int j = 0; j < (4 * 1024 ); j++) { //3個4K (因為U32, 1024=4K )
			//dump few data to check when copare fail
			if ( (j % 1024 == 1) ) {
				UartPrintf("\n Read buff, %d=[ r_ptr=%x ]", j, *r_ptr);
			}
			r_ptr++;
		}

	}
#endif

	UartPrintf("\n [ END of %s - COP0 Staus: %x] \n", __func__, M_GET_COP0_STATUS());
}


void VUC_SLC_READ_4P(int BLOCK)
{
	UartPrintf("\n [ Enter on %s ]\n", __func__);

	COP0Status_t eCOP0Status;
	U32 Read_buffer = BURNER_VENDOR_BUF_BASE + BC_512KB;
	U32 ulLocalPCA = INVALID_PCA;
	int LUN = 0, PAGE = 0, CE = 0, CH = 0, PLANE = 0;
	cmd_table_t uoCallbackInfo = {0};//Duson
	COP0ReadSQPara_t ReadSQPara = {{0}};
	BUF_TYPE_t ulBufPara = {0};

	memset((void *)Read_buffer, 0x0, gubBurstsPerBank * BC_16KB);
	for (PLANE = 0; PLANE < gubBurstsPerBank; PLANE++) {

		ulLocalPCA = M_GET_PCA(LUN, BLOCK, PAGE, CE, CH, PLANE, 0, COP0_PCA_RULE_3);
		//UartPrintf("\n Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%x \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulLocalPCA);

		M_CLR_COP0_LCA_CMP(); //disable LCA compare
		ulBufPara.A.ulBUF_ADR = Read_buffer + (PLANE * BC_16KB ); //read 4K data foreach plane only
		COP0API_FillCOP0ReadSQUserData0Para(COP0_R_DEBUG_UNC, &ReadSQPara);

		ReadSQPara.UserData0.btSLCMode = TRUE;
		ReadSQPara.UserData0.btD1 = TRUE;
		ReadSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;
		ReadSQPara.UserData0.btRUTBps = TRUE;
		ReadSQPara.UserData0.btVBRMPBps = TRUE;
		ReadSQPara.UserData0.TagID = TAG_ID_ALLOCATE;
		ReadSQPara.UserData0.btNeedCQ = TRUE;


		M_FWPCA_PCA_SET(ReadSQPara.ulPCA.ulAll, ulLocalPCA);
		ReadSQPara.BufVld.ubBufType = BUF_TYPE_A;
		ReadSQPara.BufVld.pulBufInfoPtr = &ulBufPara;

		eCOP0Status = COP0API_SendReadSQ(&ReadSQPara, &uoCallbackInfo);//Duson
		M_FW_ASSERT(ASSERT_BURNER_0x017F, eCOP0Status.btSendCmdSuccess);

		//ulLocalPCA = ulLocalPCA + gub4kEntrysPerPlane ; //increase PCA by 4 ( 4 frame per plane)

	}

	/************************************************************************
	 *                     Wait done                                          *
	 ************************************************************************/
	while (!TagCOP0CheckDoneRelease(ReadSQPara.UserData0.TagID)) {
		if (!M_DB_CHECK_EMPTY(DB_ERR_MSG_CQ)) {
			FWErrRecorder();
		}
		if (!M_DB_CHECK_EMPTY(DB_COP0_CQ)) {
			COP0DelegateCmd();
		}
	}

#if 1 // Print out

	U8 buf_num = 0;
	for (PLANE = 0; PLANE < gubBurstsPerBank; PLANE++) {
		UartPrintf("\n\nPrint 1st byte on each 4k  (Read 4K only) PLANE:%d", PLANE);
		buf_num =  PLANE ;
		U32 addr = Read_buffer + (buf_num * BC_16KB );
		U32 *r_ptr = (U32 *) addr;
		for (int j = 0; j < (4 * 1024 ); j++) { //3個4K (因為U32, 1024=4K )
			//dump few data to check when copare fail
			if ( (j % 1024 == 1) ) {
				UartPrintf("\n Read buff, %d=[ r_ptr=%x ]", j, *r_ptr);
			}
			r_ptr++;
		}

	}
#endif


	UartPrintf("\n [ END of %s - COP0 Staus: %x] \n", __func__, M_GET_COP0_STATUS());
}


void VUC_SLC_Erase_Prog_Read_Verify_1P(int BLOCK, int PAGE_CNT)
{
	/*Step 1. ERASE */
	COP0EraseSQPara_t EraseSQPara = {{0}};
	cmd_table_t uoCallbackInfo = {0};
	COP0Status_t eCOP0Status;
	PCA_t ulFWPCA = {0};
	U32 ulPCA;




	int LUN = 0, PAGE = 0, CE = 0, CH = 0, PLANE = 0;

	UartPrintf("\n [ Erase, gubBurstsPerBank=%d BLOCK_NUM=%d run %d page] \n", gubBurstsPerBank, BLOCK, PAGE_CNT);
#if 1
	for ( PAGE = 0; PAGE < PAGE_CNT; PAGE++) {
		for ( LUN = 0; LUN < gubDieNumber; LUN++) {
			for ( CE = 0; CE < gubCEsPerSuperPage / 2; CE ++) {
				for ( CH = 0; CH < 2; CH++) {
					for ( PLANE = 0 ; PLANE < gubBurstsPerBank; PLANE ++) {
#endif
						// fill out Erase parameter
						EraseSQPara.UserData0.uwTagID = TAG_ID_ALLOCATE;
						EraseSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;
						EraseSQPara.UserData0.btSLCMode = TRUE;
						EraseSQPara.UserData0.btD1 = TRUE;
						EraseSQPara.UserData0.btNeedCQ = TRUE;
						EraseSQPara.UserData0.btVBRMPBps = TRUE;
						EraseSQPara.UserData0.btRUTBps = TRUE;
						M_FWPCA_ZBYTE_SET(ulFWPCA.ulAll, gPCAInfo.ubMaxZByte);
						M_FWPCA_CROSSFRAME_SET(ulFWPCA.ulAll, 0);
						uoCallbackInfo.ulData.btKeepTag = TRUE;

						//1. single plane erase
						ulPCA = M_GET_PCA(LUN, BLOCK, PAGE, CE, CH, PLANE, 0, COP0_PCA_RULE_3);
						//UartPrintf("\n	Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%x \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);
						EraseSQPara.UserData0.ubMultiPlaneMode = MULTIPLANE_NO_USE;
						EraseSQPara.ulPCA.ulAll = ulPCA;

						eCOP0Status = COP0API_SendEraseSQ(&EraseSQPara, &uoCallbackInfo);
						if (eCOP0Status.btSendCmdSuccess != TRUE) {
							UartPrintf("\n\n ------------- erase CMD Fail... ----------------");
							UartPrintf("\n	Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);
							while (1);
						}

						while (!TagCOP0CheckDoneRelease(EraseSQPara.UserData0.uwTagID)) {
							if (!M_DB_CHECK_EMPTY(DB_ERR_MSG_CQ)) {
								FWErrRecorder();
							}
							if (!M_DB_CHECK_EMPTY(DB_COP0_CQ)) {
								COP0DelegateCmd();
							}
						}
#if 1
					}
				}
			}
		}
		if (PAGE % 10 == 0) {
			UartPrintf("\n	Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);
		}

	}
#endif

	UartPrintf("\n [ R/W Test start ] \n");


	COP0WriteSQPara_t WriteSQPara = {{0}};
	BUF_TYPE_t ulBufPara[FRAMES_PER_PAGE] = {{0}};
	FWSetPara_t ulFWSet[FRAMES_PER_PAGE] = {{0}};
	U32 ulLCA[FRAMES_PER_PAGE] = {0};
	U8	ubFramIdx;
	cmd_table_t write_uoCallbackInfo = {0};

	COP0ReadSQPara_t ReadSQPara = {{0}};
	BUF_TYPE_t ulBufPara_read = {0};

	U32 Read_buffer = BURNER_VENDOR_BUF_BASE + BC_512KB;
	U32 Write_buffer = BURNER_VENDOR_BUF_BASE;
	U8 cmp_res = 1;


	memset((void *)Write_buffer, 0x0, BC_16KB);
	memset((void *)Read_buffer, 0x0, BC_16KB);

#if 1
	for ( PAGE = 0; PAGE < PAGE_CNT; PAGE++) {
		for ( LUN = 0; LUN < gubDieNumber; LUN++) {
			for ( CE = 0; CE < gubCEsPerSuperPage / 2; CE ++) {
				for ( CH = 0; CH < 2; CH++) {
					for ( PLANE = 0 ; PLANE < gubBurstsPerBank; PLANE ++) {
#endif
						ulPCA = M_GET_PCA(LUN, BLOCK, PAGE, CE, CH, PLANE, 0, COP0_PCA_RULE_3);
						//UartPrintf("\nPage:%d  BLOCK:%d  ce:%d  ch%d  plane:%d ulPCA:%d \n", PAGE, BLOCK, CE, CH, PLANE, ulPCA);

						//Step 2. prepare write data in buffer with address rule
						U32 *ww_ptr;
						ww_ptr = (U32 *) Write_buffer;
#if 0 // use fixed pattern
						memset((void *)Write_buffer, 0x777, BC_16KB);
#else //use address pattern
						for (int j = 0; j < 4096; j++) {
							*ww_ptr = (U32 )ww_ptr;
							if ( (j % 1024 == 0) || (j % 1024 == 1) ) {
								//UartPrintf("\n *ww_ptr=%x \n", *ww_ptr);
							}
							ww_ptr++;
						}
#endif
						//Step 3. program
						//VUC_Program(COP0_W_TABLE_PROGRAM, ulPCA, Write_buffer);
						U32 Addr = Write_buffer;
						for (ubFramIdx = 0; ubFramIdx < gub4kEntrysPerPlane; ubFramIdx++) {
							ulBufPara[ubFramIdx].A.ulBUF_ADR = Addr + (ubFramIdx * BC_4KB);
							ulLCA[ubFramIdx] = ((U32 *)Addr)[ubFramIdx];
							ulFWSet[ubFramIdx].ubZInfo = MAX_ZINFO;
						}

						ulPCA = M_GET_PCA(LUN, BLOCK, PAGE, CE, CH, PLANE, 0, COP0_PCA_RULE_3);
						//UartPrintf("\n Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%x \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);

						COP0API_FillCOP0WriteSQUserData0Para(COP0_W_TABLE_PROGRAM, &WriteSQPara);
						write_uoCallbackInfo.ulData.ulFLHInfo.btKeepTag = TRUE;
						WriteSQPara.ulPCA.ulAll = ulPCA;
						WriteSQPara.BufVld.ubBufType = BUF_TYPE_A;
						WriteSQPara.BufVld.pulBufInfoPtr = ulBufPara;
						WriteSQPara.pulLCAPtr = &ulLCA[0];
						WriteSQPara.pulFWSetPtr = ulFWSet;
						WriteSQPara.uoWriteConvRsInfo.btConvOnly = TRUE;
						WriteSQPara.UserData0.btSkipError = TRUE;
						WriteSQPara.UserData0.btSLCMode = TRUE;
						WriteSQPara.UserData0.btD1 = TRUE;
						WriteSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;	//Tie in
						WriteSQPara.UserData0.btRUTBps		  = TRUE; //bypass
						WriteSQPara.UserData0.btVBRMPBps	  = TRUE; //bypass
						WriteSQPara.UserData0.btDisableCacheProgram = TRUE;

						eCOP0Status = COP0API_SendWriteSQ(&WriteSQPara, &write_uoCallbackInfo);
						M_FW_ASSERT(ASSERT_BURNER_0x0180, eCOP0Status.btSendCmdSuccess);


						while (!TagCOP0CheckDoneRelease(WriteSQPara.UserData0.uwTagID)) {
							if (!M_DB_CHECK_EMPTY(DB_ERR_MSG_CQ)) {
								FWErrRecorder();
							}
							if (!M_DB_CHECK_EMPTY(DB_COP0_CQ)) {
								COP0DelegateCmd();
							}
						}



#if 1
						//Step 4. read

						memset((void *)Read_buffer, 0, BC_16KB);
						//UartPrintf("\n [ Before check Read_buffer - 64B, Read_buffer=%x  ] \n", Read_buffer);
						//VUC_ReadSinglePage(Read_buffer, ulPCA, 0xF/*NULL*/, gub4kEntrysPerPlane, 0);


						for ( ubFramIdx = 0; ubFramIdx < 4; ubFramIdx++) {
							ulPCA = M_GET_PCA(LUN, BLOCK, PAGE, CE, CH, PLANE, ubFramIdx, COP0_PCA_RULE_3);
							M_CLR_COP0_LCA_CMP(); //disable LCA compare
							ulBufPara_read.A.ulBUF_ADR = Read_buffer + (ubFramIdx * BC_4KB ); //read 4K data foreach plane only
							COP0API_FillCOP0ReadSQUserData0Para(COP0_R_DEBUG_UNC, &ReadSQPara);

							ReadSQPara.UserData0.btSLCMode = TRUE;
							ReadSQPara.UserData0.btD1 = TRUE;
							ReadSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;
							ReadSQPara.UserData0.btRUTBps = TRUE;
							ReadSQPara.UserData0.btVBRMPBps = TRUE;
							ReadSQPara.UserData0.TagID = TAG_ID_ALLOCATE;


							M_FWPCA_PCA_SET(ReadSQPara.ulPCA.ulAll, ulPCA);
							ReadSQPara.BufVld.ubBufType = BUF_TYPE_A;
							ReadSQPara.BufVld.pulBufInfoPtr = &ulBufPara_read;

							eCOP0Status = COP0API_SendReadSQ(&ReadSQPara, NULL);
							M_FW_ASSERT(ASSERT_BURNER_0x0181, eCOP0Status.btSendCmdSuccess);

						}
						while (TRUE == M_COP0_GET_IDLE()) {
							FWCop0Waiting();
						}



						//UartPrintf("\n [ Read Done with PCA=%d]\n", ulPCA);


						//Step 5. verify
						cmp_res = memcmp((void *)Write_buffer, (void *)Read_buffer, BC_16KB);

						if (cmp_res == 0) {
							//UartPrintf("\n [ Compare Pass ! ]\n");

						}
						else {
							//UartPrintf("\nPage:%d  BLOCK:%d  ce:%d  ch%d  plane:%d ulPCA:%d \n", PAGE, BLOCK, CE, CH, PLANE, ulPCA);
							UartPrintf("\n [ Compare Fail ! ]\n");
							UartPrintf("\nAt Page:%d  BLOCK:%d  ce:%d  ch%d  plane:%d ulPCA:%d \n", PAGE, BLOCK, CE, CH, PLANE, ulPCA);

							//debug for dump detail
							U32 *r_ptr;
							U32 *w_ptr;
							r_ptr = (U32 *) Read_buffer; // 4 byte
							w_ptr = (U32 *) Write_buffer;
							//UartPrintf("\n [ after r_ptr -> Read_buffer , Read_buffer address=%x  ] \n", Read_buffer);
							for (int j = 1; j < 4 * 1024; j++) { //4個4K (4*1024)

								if (*r_ptr != *w_ptr) {
									//dump few data to check when copare fail
									if ( (j % 1024 == 0) || (j % 1024 == 1)  || (j % 1024 == 2) || (j % 1024 == 3)  || (j % 1024 == 4) ) {
										UartPrintf("\n j=%d Diff, [ r_ptr=%x ],[ w_ptr=%x ]  \n", j, *r_ptr, *w_ptr);
									}
									//UartPrintf("\n j=%d Diff, [ r_ptr=%x ],[ w_ptr=%x ]  \n", j, *r_ptr, *w_ptr);
								}
								else {
									if ( (j % 1024 == 0) || (j % 1024 == 1)  || (j % 1024 == 2) || (j % 1024 == 3)  || (j % 1024 == 4) ) {
										UartPrintf("\n j=%d Same, [ r_ptr=%x ],[ w_ptr=%x ]  \n", j, *r_ptr, *w_ptr);
									}

								}
								r_ptr++;
								w_ptr++;
							}
							UartPrintf("\n [ debug Done ]\n");
							while (1);
						}
#endif
#if 1

					}
				}
			}
		}
		if (PAGE % 10 == 0) {
			UartPrintf("\n	Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);
		}
	}
#endif

	if (cmp_res == 0) {
		UartPrintf("\n [ ALL Compare Pass ! ]\n");
	}

	UartPrintf("\n [ END on %s ] \n", __func__);


}

void VUC_SLC_PROC_CACHE_1P(int BLOCK, int PAGE_CNT)
{

	UartPrintf("\n [ Enter on %s !] \n", __func__);

	/**
		Erase first
	*/
	COP0EraseSQPara_t EraseSQPara = {{0}};
	cmd_table_t uoCallbackInfo = {0};
	COP0Status_t eCOP0Status;
	PCA_t ulFWPCA = {0};
	U32 ulPCA;

	int LUN = 0, PAGE = 0, CE = 0, CH = 0, PLANE = 0;

	UartPrintf("\n [ Erase, gubBurstsPerBank=%d BLOCK_NUM=%d run %d page] \n", gubBurstsPerBank, BLOCK, PAGE_CNT);
	for ( PAGE = 0; PAGE < PAGE_CNT; PAGE++) {
		//for ( PLANE = 0 ; PLANE < 1; PLANE ++) {
		// fill out Erase parameter
		EraseSQPara.UserData0.uwTagID = TAG_ID_ALLOCATE;
		EraseSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;
		EraseSQPara.UserData0.btSLCMode = TRUE;
		EraseSQPara.UserData0.btD1 = TRUE;
		EraseSQPara.UserData0.btNeedCQ = TRUE;
		EraseSQPara.UserData0.btVBRMPBps = TRUE;
		EraseSQPara.UserData0.btRUTBps = TRUE;
		M_FWPCA_ZBYTE_SET(ulFWPCA.ulAll, gPCAInfo.ubMaxZByte);
		M_FWPCA_CROSSFRAME_SET(ulFWPCA.ulAll, 0);
		uoCallbackInfo.ulData.btKeepTag = TRUE;

		//1. single plane erase
		ulPCA = M_GET_PCA(LUN, BLOCK, PAGE, CE, CH, PLANE, 0, COP0_PCA_RULE_3);
		//UartPrintf("\n	Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);
		EraseSQPara.UserData0.ubMultiPlaneMode = MULTIPLANE_NO_USE;
		EraseSQPara.ulPCA.ulAll = ulPCA;
		eCOP0Status = COP0API_SendEraseSQ(&EraseSQPara, &uoCallbackInfo);
		if (eCOP0Status.btSendCmdSuccess != TRUE) {
			UartPrintf("\n\n ------------- erase CMD Fail... ----------------");
			UartPrintf("\n	Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);
			while (1);
		}

		while (!TagCOP0CheckDoneRelease(EraseSQPara.UserData0.uwTagID)) {
			if (!M_DB_CHECK_EMPTY(DB_ERR_MSG_CQ)) {
				FWErrRecorder();
			}
			if (!M_DB_CHECK_EMPTY(DB_COP0_CQ)) {
				COP0DelegateCmd();
			}
		}
		//}

		/*if (PAGE % 10 == 0) {
			UartPrintf("\n	Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);
		}*/

	}

	/**
		program
	*/
	UartPrintf("\n [ cache program, gubBurstsPerBank=%d BLOCK_NUM=%d run %d page] \n", gubBurstsPerBank, BLOCK, PAGE_CNT);

#if 0
	//// PIO DBG  ////
	REG32 *pFlaReg = R32_FCTL_CH[0];
	FlaCEControl(0, 0, ENABLE);
	pFlaReg[R32_FCTL_PIO_CMD] = 0x98;
	FlaCEControl(0, 0, DISABLE);
	//// PIO DBG  ////
#endif

	COP0WriteModeEnum_t COP0WriteMode;
	COP0WriteMode =  COP0_W_TABLE_PROGRAM; //check this

	U32 Write_buffer = BURNER_VENDOR_BUF_BASE;
	U8	ubFramIdx;
	LUN = 0, PAGE = 0, CE = 0, CH = 0, PLANE = 0;


	U32 ulLCA[FRAMES_PER_PAGE] = {0};
	COP0WriteSQPara_t WriteSQPara = {{0}};
	BUF_TYPE_t ulBufPara[FRAMES_PER_PAGE] = {{0}};
	FWSetPara_t ulFWSet[FRAMES_PER_PAGE] = {{0}};

	gpComm2Andes_Info->ubDisable_Cache_Prog = FALSE;

	memset((void *)Write_buffer, 0x78, BC_16KB);

	//write 16K data
	for (ubFramIdx = 0; ubFramIdx < gub4kEntrysPerPlane; ubFramIdx++) {
		ulBufPara[ubFramIdx].A.ulBUF_ADR = Write_buffer + (ubFramIdx * BC_4KB);
		ulLCA[ubFramIdx] = ((U32 *)Write_buffer)[ubFramIdx];
		ulFWSet[ubFramIdx].ubZInfo = MAX_ZINFO;
	}

	COP0API_FillCOP0WriteSQUserData0Para(COP0WriteMode, &WriteSQPara);
	uoCallbackInfo.ulData.ulFLHInfo.btKeepTag = TRUE;
	WriteSQPara.BufVld.ubBufType = BUF_TYPE_A;
	WriteSQPara.BufVld.pulBufInfoPtr = ulBufPara;
	WriteSQPara.pulLCAPtr = &ulLCA[0];
	WriteSQPara.pulFWSetPtr = ulFWSet;
	WriteSQPara.uoWriteConvRsInfo.btConvOnly = TRUE;
	WriteSQPara.UserData0.btSkipError = TRUE;
	WriteSQPara.UserData0.btSLCMode = TRUE;
	WriteSQPara.UserData0.btD1 = TRUE;
	WriteSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;	//Tie in
	WriteSQPara.UserData0.btRUTBps		  = TRUE; //bypass
	WriteSQPara.UserData0.btVBRMPBps	  = TRUE; //bypass
	WriteSQPara.UserData0.btDisableCacheProgram = FALSE;
	WriteSQPara.UserData0.ubMultiPlaneMode = MULTIPLANE_NO_USE;
	//		WriteSQPara.UserData0.btLock = TRUE;
	//		WriteSQPara.UserData0.ubPriority = COP0_LINK_PIORITY_HIGH_1;


	for ( PAGE = 0; PAGE < PAGE_CNT; PAGE++) {
		//for ( PLANE = 0 ; PLANE < 1; PLANE ++) {
		ulPCA = M_GET_PCA(LUN, BLOCK, PAGE, CE, CH, PLANE, 0, COP0_PCA_RULE_3);
		//UartPrintf("\n  Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);

		WriteSQPara.ulPCA.ulAll = ulPCA;
		//M_SET_GPIO_O(0, 0); // GPIO 0 pull low
		//M_SET_GPIO_O(0, 1); // GPIO 0 pull high

		eCOP0Status = COP0API_SendWriteSQ(&WriteSQPara, &uoCallbackInfo);
		//M_FW_ASSERT(ASSERT_BURNER_0x0182, eCOP0Status.btSendCmdSuccess);
		//}
	}

	while (!TagCOP0CheckDoneRelease(WriteSQPara.UserData0.uwTagID)) {
		if (!M_DB_CHECK_EMPTY(DB_ERR_MSG_CQ)) {
			FWErrRecorder();
		}
		if (!M_DB_CHECK_EMPTY(DB_COP0_CQ)) {
			COP0DelegateCmd();
		}
	}



	//UartPrintf("\n [ gpComm2Andes_Info->ubProg_TimeOut_Cnt=%d ] \n",	gpComm2Andes_Info->ubProg_TimeOut_Cnt);
	//UartPrintf("\n [ gpComm2Andes_Info->ProgCache_Cnt=%d ] \n",	gpComm2Andes_Info->ubRead_TimeOut_Cnt);

	UartPrintf("\n [ END of %s ] \n", __func__);

}
void VUC_SLC_PROC_CACHE_MULTI_PLANE(int BLOCK, int PAGE_CNT)
{

	UartPrintf("\n [ Enter on %s !] \n", __func__);

	/**
		Erase first
	*/
	COP0EraseSQPara_t EraseSQPara = {{0}};
	cmd_table_t uoCallbackInfo = {0};
	COP0Status_t eCOP0Status;
	PCA_t ulFWPCA = {0};
	U32 ulPCA;

	int LUN = 0, PAGE = 0, CE = 0, CH = 0, PLANE = 0;

	UartPrintf("\n [ Erase, gubBurstsPerBank=%d BLOCK_NUM=%d run %d page] \n", gubBurstsPerBank, BLOCK, PAGE_CNT);
	for ( PAGE = 0; PAGE < PAGE_CNT; PAGE++) {
		for ( PLANE = 0 ; PLANE < gubBurstsPerBank; PLANE ++) {
			// fill out Erase parameter
			EraseSQPara.UserData0.uwTagID = TAG_ID_ALLOCATE;
			EraseSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;
			EraseSQPara.UserData0.btSLCMode = TRUE;
			EraseSQPara.UserData0.btD1 = TRUE;
			EraseSQPara.UserData0.btNeedCQ = TRUE;
			EraseSQPara.UserData0.btVBRMPBps = TRUE;
			EraseSQPara.UserData0.btRUTBps = TRUE;
			M_FWPCA_ZBYTE_SET(ulFWPCA.ulAll, gPCAInfo.ubMaxZByte);
			M_FWPCA_CROSSFRAME_SET(ulFWPCA.ulAll, 0);
			uoCallbackInfo.ulData.btKeepTag = TRUE;

			//1. single plane erase
			ulPCA = M_GET_PCA(LUN, BLOCK, PAGE, CE, CH, PLANE, 0, COP0_PCA_RULE_3);
			//UartPrintf("\n	Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);
			EraseSQPara.UserData0.ubMultiPlaneMode = MULTIPLANE_NO_USE;
			EraseSQPara.ulPCA.ulAll = ulPCA;
			eCOP0Status = COP0API_SendEraseSQ(&EraseSQPara, &uoCallbackInfo);
			if (eCOP0Status.btSendCmdSuccess != TRUE) {
				UartPrintf("\n\n ------------- erase CMD Fail... ----------------");
				UartPrintf("\n	Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);
				while (1);
			}

			while (!TagCOP0CheckDoneRelease(EraseSQPara.UserData0.uwTagID)) {
				if (!M_DB_CHECK_EMPTY(DB_ERR_MSG_CQ)) {
					FWErrRecorder();
				}
				if (!M_DB_CHECK_EMPTY(DB_COP0_CQ)) {
					COP0DelegateCmd();
				}
			}
		}

		/*if (PAGE % 10 == 0) {
			UartPrintf("\n	Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);
		}*/

	}

	/**
		program
	*/
	UartPrintf("\n [ Multi-cache program, gubBurstsPerBank=%d BLOCK_NUM=%d run %d page] \n", gubBurstsPerBank, BLOCK, PAGE_CNT);

#if 1
	//// PIO DBG  ////
	REG32 *pFlaReg = R32_FCTL_CH[0];
	FlaCEControl(0, 0, ENABLE);
	pFlaReg[R32_FCTL_PIO_CMD] = 0x98;
	FlaCEControl(0, 0, DISABLE);
	//// PIO DBG  ////
#endif

	COP0WriteModeEnum_t COP0WriteMode;
	COP0WriteMode =  COP0_W_TABLE_PROGRAM; //check this

	U32 Write_buffer = BURNER_VENDOR_BUF_BASE;
	U8	ubFramIdx;
	LUN = 0, PAGE = 0, CE = 0, CH = 0, PLANE = 0;


	U32 ulLCA[FRAMES_PER_PAGE] = {0};
	COP0WriteSQPara_t WriteSQPara = {{0}};
	BUF_TYPE_t ulBufPara[FRAMES_PER_PAGE] = {{0}};
	FWSetPara_t ulFWSet[FRAMES_PER_PAGE] = {{0}};

	gpComm2Andes_Info->ubDisable_Cache_Prog = FALSE;


	//write 16K data
	memset((void *)Write_buffer, 0x78, BC_16KB);
	for (ubFramIdx = 0; ubFramIdx < gub4kEntrysPerPlane; ubFramIdx++) {
		ulBufPara[ubFramIdx].A.ulBUF_ADR = Write_buffer + (ubFramIdx * BC_4KB);
		ulLCA[ubFramIdx] = ((U32 *)Write_buffer)[ubFramIdx];
		ulFWSet[ubFramIdx].ubZInfo = MAX_ZINFO;
	}
	COP0API_FillCOP0WriteSQUserData0Para(COP0WriteMode, &WriteSQPara);
	uoCallbackInfo.ulData.ulFLHInfo.btKeepTag = TRUE;
	WriteSQPara.BufVld.ubBufType = BUF_TYPE_A;
	WriteSQPara.BufVld.pulBufInfoPtr = ulBufPara;
	WriteSQPara.pulLCAPtr = &ulLCA[0];
	WriteSQPara.pulFWSetPtr = ulFWSet;
	WriteSQPara.uoWriteConvRsInfo.btConvOnly = TRUE;
	WriteSQPara.UserData0.btSkipError = TRUE;
	WriteSQPara.UserData0.btSLCMode = TRUE;
	WriteSQPara.UserData0.btD1 = TRUE;
	WriteSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;	//Tie in
	WriteSQPara.UserData0.btRUTBps		  = TRUE; //bypass
	WriteSQPara.UserData0.btVBRMPBps	  = TRUE; //bypass
	WriteSQPara.UserData0.btDisableCacheProgram = FALSE;


	for ( PAGE = 0; PAGE < PAGE_CNT; PAGE++) {
		for ( PLANE = 0 ; PLANE < gubBurstsPerBank; PLANE ++) {
			ulPCA = M_GET_PCA(LUN, BLOCK, PAGE, CE, CH, PLANE, 0, COP0_PCA_RULE_3);
			//UartPrintf("\n  Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);


			WriteSQPara.ulPCA.ulAll = ulPCA;
			if ( PLANE == gubBurstsPerBank - 1 ) {
				WriteSQPara.UserData0.ubMultiPlaneMode = MULTIPLANE_END;
			}
			else {
				WriteSQPara.UserData0.ubMultiPlaneMode = MULTIPLANE_ING;
			}

			eCOP0Status = COP0API_SendWriteSQ(&WriteSQPara, &uoCallbackInfo);
			//M_FW_ASSERT(ASSERT_BURNER_0x0183, eCOP0Status.btSendCmdSuccess);
		}
	}

	while (!TagCOP0CheckDoneRelease(WriteSQPara.UserData0.uwTagID)) {
		if (!M_DB_CHECK_EMPTY(DB_ERR_MSG_CQ)) {
			FWErrRecorder();
		}
		if (!M_DB_CHECK_EMPTY(DB_COP0_CQ)) {
			COP0DelegateCmd();
		}
	}
	UartPrintf("\n [ gpComm2Andes_Info->ubProg_TimeOut_Cnt=%d ] \n",	gpComm2Andes_Info->ubProg_TimeOut_Cnt);
	UartPrintf("\n [ gpComm2Andes_Info->ProgCache_Cnt=%d ] \n",	gpComm2Andes_Info->ubRead_TimeOut_Cnt);

	UartPrintf("\n [ END of %s ] \n", __func__);

}


void VUC_SLC_PROC_SINGLE_PLANE(int BLOCK)
{

	UartPrintf("\n [ Enter on %s ] \n", __func__);
	/* ERASE */
	COP0EraseSQPara_t EraseSQPara = {{0}};
	cmd_table_t uoCallbackInfo = {0};
	COP0Status_t eCOP0Status;
	PCA_t ulFWPCA = {0};
	U32 ulPCA;


	int LUN = 0, PAGE = 0, CE = 0, CH = 0, PLANE = 0;

	// fill out Erase parameter
	EraseSQPara.UserData0.uwTagID = TAG_ID_ALLOCATE;
	EraseSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;
	EraseSQPara.UserData0.btSLCMode = TRUE;
	EraseSQPara.UserData0.btD1 = TRUE;


	EraseSQPara.UserData0.btNeedCQ = TRUE;
	EraseSQPara.UserData0.btVBRMPBps = TRUE;
	EraseSQPara.UserData0.btRUTBps = TRUE;
	M_FWPCA_ZBYTE_SET(ulFWPCA.ulAll, gPCAInfo.ubMaxZByte);
	M_FWPCA_CROSSFRAME_SET(ulFWPCA.ulAll, 0);
	uoCallbackInfo.ulData.btKeepTag = TRUE;


	for (PLANE = 0; PLANE < 1; PLANE++) {

		ulPCA = M_GET_PCA(LUN, BLOCK, PAGE, CE, CH, PLANE, 0, COP0_PCA_RULE_3);
		//UartPrintf("\n  Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d, rule3 \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);
		EraseSQPara.UserData0.ubMultiPlaneMode = MULTIPLANE_NO_USE;
		EraseSQPara.ulPCA.ulAll = ulPCA;

		eCOP0Status = COP0API_SendEraseSQ(&EraseSQPara, &uoCallbackInfo);
		if (eCOP0Status.btSendCmdSuccess != TRUE) {
			UartPrintf("\n\n ------------- erase CMD Fail... ----------------");
			while (1);
		}

	}

	while (!TagCOP0CheckDoneRelease(EraseSQPara.UserData0.uwTagID)) {
		if (!M_DB_CHECK_EMPTY(DB_ERR_MSG_CQ)) {
			FWErrRecorder();
		}
		if (!M_DB_CHECK_EMPTY(DB_COP0_CQ)) {
			COP0DelegateCmd();
		}
	}

	/*PROG*/

	COP0WriteModeEnum_t COP0WriteMode;
	COP0WriteMode =  COP0_W_TABLE_PROGRAM; //check this

	U32 Write_buffer = BURNER_VENDOR_BUF_BASE;
	U8	ubFramIdx;
	LUN = 0, PAGE = 0, CE = 0, CH = 0, PLANE = 0;


	U32 ulLCA[FRAMES_PER_PAGE] = {0};

	COP0WriteSQPara_t WriteSQPara = {{0}};
	BUF_TYPE_t ulBufPara[FRAMES_PER_PAGE] = {{0}};
	FWSetPara_t ulFWSet[FRAMES_PER_PAGE] = {{0}};

	ulPCA = M_GET_PCA(LUN, BLOCK, PAGE, CE, CH, PLANE, 0, COP0_PCA_RULE_3);
	UartPrintf("\n  Write Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);

	//write 16K data
	//memset((void *)Write_buffer, 0x70, BC_16KB);
	memset((void *)Write_buffer, 0x80, BC_16KB);
	for (ubFramIdx = 0; ubFramIdx < gub4kEntrysPerPlane; ubFramIdx++) {
		ulBufPara[ubFramIdx].A.ulBUF_ADR = Write_buffer + (ubFramIdx * BC_4KB);
		ulLCA[ubFramIdx] = ((U32 *)Write_buffer)[ubFramIdx];
		ulFWSet[ubFramIdx].ubZInfo = MAX_ZINFO;
	}

	UartPrintf("\n [ Print out: Write Buffer ] \n");
	U32 *r_ptr2;
	r_ptr2 = (U32 *) Write_buffer; // 4 byte
	//UartPrintf("\n [ after r_ptr -> Read_buffer , Read_buffer address=%x	] \n", Read_buffer);
	for (int j = 0; j < 1 * 1024; j++) { //多少個4K (因為U32, 1024=4K )

		//dump few data to check when copare fail
		if ( (j % 1024 == 0) || (j % 1024 == 1)  || (j % 1024 == 2) || (j % 1024 == 3)	|| (j % 1024 == 4) ) {
			UartPrintf("\n j=%d , [ r_ptr=%x ]	\n", j, *r_ptr2);
		}
		r_ptr2++;
	}

	COP0API_FillCOP0WriteSQUserData0Para(COP0WriteMode, &WriteSQPara);
	uoCallbackInfo.ulData.ulFLHInfo.btKeepTag = TRUE;
	WriteSQPara.ulPCA.ulAll = ulPCA;
	WriteSQPara.BufVld.ubBufType = BUF_TYPE_A;
	WriteSQPara.BufVld.pulBufInfoPtr = ulBufPara;
	WriteSQPara.pulLCAPtr = &ulLCA[0];
	WriteSQPara.pulFWSetPtr = ulFWSet;
	WriteSQPara.uoWriteConvRsInfo.btConvOnly = TRUE;
	WriteSQPara.UserData0.btSkipError = TRUE;
	WriteSQPara.UserData0.btSLCMode = TRUE;
	WriteSQPara.UserData0.btD1 = TRUE;
	WriteSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;  //Tie in
	WriteSQPara.UserData0.btRUTBps        = TRUE; //bypass
	WriteSQPara.UserData0.btVBRMPBps      = TRUE; //bypass
	WriteSQPara.UserData0.btDisableCacheProgram = TRUE;

	eCOP0Status = COP0API_SendWriteSQ(&WriteSQPara, &uoCallbackInfo);
	M_FW_ASSERT(ASSERT_BURNER_0x0184, eCOP0Status.btSendCmdSuccess);

	while (!TagCOP0CheckDoneRelease(WriteSQPara.UserData0.uwTagID)) {
		if (!M_DB_CHECK_EMPTY(DB_ERR_MSG_CQ)) {
			FWErrRecorder();
		}
		if (!M_DB_CHECK_EMPTY(DB_COP0_CQ)) {
			COP0DelegateCmd();
		}
	}

	UartPrintf("\n [ END of %s ] \n", __func__);

}



void VUC_SLC_PROG_MULTI_PLANE(int BLOCK) //
{
	UartPrintf("\n [ Enter on %s ] \n", __func__);

	/* ERASE */
	COP0EraseSQPara_t EraseSQPara = {{0}};
	cmd_table_t uoCallbackInfo = {0};
	COP0Status_t eCOP0Status;
	PCA_t ulFWPCA = {0};
	U32 ulPCA;


	int LUN = 0, PAGE = 0, CE = 0, CH = 0, PLANE = 0;

	// fill out Erase parameter
	EraseSQPara.UserData0.uwTagID = TAG_ID_ALLOCATE;
	EraseSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;
	EraseSQPara.UserData0.btSLCMode = TRUE;
	EraseSQPara.UserData0.btD1 = TRUE;

	EraseSQPara.UserData0.btNeedCQ = TRUE;
	EraseSQPara.UserData0.btVBRMPBps = TRUE;
	EraseSQPara.UserData0.btRUTBps = TRUE;
	M_FWPCA_ZBYTE_SET(ulFWPCA.ulAll, gPCAInfo.ubMaxZByte);
	M_FWPCA_CROSSFRAME_SET(ulFWPCA.ulAll, 0);
	uoCallbackInfo.ulData.btKeepTag = TRUE;

	for (PLANE = 0; PLANE < gubBurstsPerBank; PLANE++) {

		ulPCA = M_GET_PCA(LUN, BLOCK, PAGE, CE, CH, PLANE, 0, COP0_PCA_RULE_3);
		//UartPrintf("\n  Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);

		EraseSQPara.ulPCA.ulAll = ulPCA;
		if ( PLANE == gubBurstsPerBank - 1 ) {
			EraseSQPara.UserData0.ubMultiPlaneMode = MULTIPLANE_END;
			//UartPrintf("\n  MULTIPLANE_END!!!~ vbrmp \n");
		}
		else {
			EraseSQPara.UserData0.ubMultiPlaneMode = MULTIPLANE_ING;
		}


		eCOP0Status = COP0API_SendEraseSQ(&EraseSQPara, &uoCallbackInfo);
		if (eCOP0Status.btSendCmdSuccess != TRUE) {
			UartPrintf("\n\n ------------- erase CMD Fail... ----------------");
			while (1);
		}

	}

	while (!TagCOP0CheckDoneRelease(EraseSQPara.UserData0.uwTagID)) {
		if (!M_DB_CHECK_EMPTY(DB_ERR_MSG_CQ)) {
			FWErrRecorder();
		}
		if (!M_DB_CHECK_EMPTY(DB_COP0_CQ)) {
			COP0DelegateCmd();
		}
	}

	/* PROG */
	COP0WriteModeEnum_t COP0WriteMode;
	COP0WriteMode =  COP0_W_TABLE_PROGRAM; //check this
	cmd_table_t uoCallbackInfo2 = {0};


	U32 Write_buffer = BURNER_VENDOR_BUF_BASE;
	U8	ubFramIdx;
	LUN = 0,  PAGE = 0, CE = 0, CH = 0, PLANE = 0;

	U32 ulLCA[FRAMES_PER_PAGE] = {0};
	COP0WriteSQPara_t WriteSQPara = {{0}};
	BUF_TYPE_t ulBufPara[FRAMES_PER_PAGE] = {{0}};
	FWSetPara_t ulFWSet[FRAMES_PER_PAGE] = {{0}};

#if 0
	//// PIO DBG  ////
	REG32 *pFlaReg = R32_FCTL_CH[0];
	FlaCEControl(0, 0, ENABLE);
	pFlaReg[R32_FCTL_PIO_CMD] = 0x98;
	FlaCEControl(0, 0, DISABLE);
	//// PIO DBG  ////
#endif

	for (PLANE = 0; PLANE < gubBurstsPerBank; PLANE++) {
		ulPCA = M_GET_PCA(LUN, BLOCK, PAGE, CE, CH, PLANE, 0, COP0_PCA_RULE_3);
		//UartPrintf("\n  Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);

		//write 16K data
		memset((void *)Write_buffer, 0x78, BC_16KB);
		for (ubFramIdx = 0; ubFramIdx < gub4kEntrysPerPlane; ubFramIdx++) {
			ulBufPara[ubFramIdx].A.ulBUF_ADR = Write_buffer + BC_4KB;
			ulLCA[ubFramIdx] = ((U32 *)Write_buffer)[ubFramIdx];
			ulFWSet[ubFramIdx].ubZInfo = MAX_ZINFO;
		}

		COP0API_FillCOP0WriteSQUserData0Para(COP0WriteMode, &WriteSQPara);
		//uoCallbackInfo.ulData.ulFLHInfo.btKeepTag = TRUE;
		WriteSQPara.UserData0.uwTagID = TAG_ID_ALLOCATE;
		WriteSQPara.ulPCA.ulAll = ulPCA;
		WriteSQPara.BufVld.ubBufType = BUF_TYPE_A;
		WriteSQPara.BufVld.pulBufInfoPtr = ulBufPara;
		WriteSQPara.pulLCAPtr = &ulLCA[0];
		WriteSQPara.pulFWSetPtr = ulFWSet;
		WriteSQPara.uoWriteConvRsInfo.btConvOnly = TRUE;
		WriteSQPara.UserData0.btSkipError = TRUE;
		WriteSQPara.UserData0.btSLCMode = TRUE;
		WriteSQPara.UserData0.btD1 = TRUE;
		WriteSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;  //Tie in
		WriteSQPara.UserData0.btRUTBps        = TRUE; //bypass
		WriteSQPara.UserData0.btVBRMPBps      = TRUE; //bypass
		WriteSQPara.UserData0.btDisableCacheProgram = TRUE;
		uoCallbackInfo2.ulData.btKeepTag = TRUE;

		if ( PLANE == gubBurstsPerBank - 1 ) {
			WriteSQPara.UserData0.ubMultiPlaneMode = MULTIPLANE_END;
		}
		else {
			WriteSQPara.UserData0.ubMultiPlaneMode = MULTIPLANE_ING;
		}

		eCOP0Status = COP0API_SendWriteSQ(&WriteSQPara, &uoCallbackInfo2);
		M_FW_ASSERT(ASSERT_BURNER_0x0185, eCOP0Status.btSendCmdSuccess);


	}


	while (!TagCOP0CheckDoneRelease(WriteSQPara.UserData0.uwTagID)) {
		if (!M_DB_CHECK_EMPTY(DB_ERR_MSG_CQ)) {
			FWErrRecorder();
		}
		if (!M_DB_CHECK_EMPTY(DB_COP0_CQ)) {
			COP0DelegateCmd();
		}
	}

	UartPrintf("\n [ END of %s  11] \n", __func__);
}


void VUC_SLC_READ_CACHE_1P(int BLOCK, int PAGE_CNT)
{

	UartPrintf("\n [ Enter on %s ]\n", __func__);

	UartPrintf("\n [ Need erase, prog First, gubBurstsPerBank=%d BLOCK_NUM=%d run %d page] \n", gubBurstsPerBank, BLOCK, PAGE_CNT);
	/**
		Erase first
	*/
	COP0EraseSQPara_t EraseSQPara = {{0}};
	cmd_table_t uoCallbackInfo = {0};
	COP0Status_t eCOP0Status;
	PCA_t ulFWPCA = {0};
	U32 ulPCA;

	int LUN = 0, PAGE = 0, CE = 0, CH = 0, PLANE = 0;

	UartPrintf("\n [ use slc Erase, gubBurstsPerBank=%d BLOCK_NUM=%d run %d page] \n", gubBurstsPerBank, BLOCK, PAGE_CNT);
	for ( PAGE = 0; PAGE < PAGE_CNT; PAGE++) {
		//for ( PLANE = 0 ; PLANE < 1; PLANE ++) {
		// fill out Erase parameter
		EraseSQPara.UserData0.uwTagID = TAG_ID_ALLOCATE;
		EraseSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;
		EraseSQPara.UserData0.btSLCMode = TRUE;
		EraseSQPara.UserData0.btD1 = TRUE;
		EraseSQPara.UserData0.btNeedCQ = TRUE;
		EraseSQPara.UserData0.btVBRMPBps = TRUE;
		EraseSQPara.UserData0.btRUTBps = TRUE;
		M_FWPCA_ZBYTE_SET(ulFWPCA.ulAll, gPCAInfo.ubMaxZByte);
		M_FWPCA_CROSSFRAME_SET(ulFWPCA.ulAll, 0);
		uoCallbackInfo.ulData.btKeepTag = TRUE;

		//1. single plane erase
		ulPCA = M_GET_PCA(LUN, BLOCK, PAGE, CE, CH, PLANE, 0, COP0_PCA_RULE_3);
		//UartPrintf("\n	Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);
		EraseSQPara.UserData0.ubMultiPlaneMode = MULTIPLANE_NO_USE;
		EraseSQPara.ulPCA.ulAll = ulPCA;
		eCOP0Status = COP0API_SendEraseSQ(&EraseSQPara, &uoCallbackInfo);
		if (eCOP0Status.btSendCmdSuccess != TRUE) {
			UartPrintf("\n\n ------------- erase CMD Fail... ----------------");
			UartPrintf("\n	Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);
			while (1);
		}

		while (!TagCOP0CheckDoneRelease(EraseSQPara.UserData0.uwTagID)) {
			if (!M_DB_CHECK_EMPTY(DB_ERR_MSG_CQ)) {
				FWErrRecorder();
			}
			if (!M_DB_CHECK_EMPTY(DB_COP0_CQ)) {
				COP0DelegateCmd();
			}
		}
		//}

		/*if (PAGE % 10 == 0) {
			UartPrintf("\n	Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);
		}*/

	}

	/**
		program
	*/
	UartPrintf("\n [ use slc program, gubBurstsPerBank=%d BLOCK_NUM=%d run %d page] \n", gubBurstsPerBank, BLOCK, PAGE_CNT);


	COP0WriteModeEnum_t COP0WriteMode;
	COP0WriteMode =  COP0_W_TABLE_PROGRAM; //check this

	U32 Write_buffer = BURNER_VENDOR_BUF_BASE;
	U8	ubFramIdx;
	LUN = 0, PAGE = 0, CE = 0, CH = 0, PLANE = 0;


	U32 ulLCA[FRAMES_PER_PAGE] = {0};
	COP0WriteSQPara_t WriteSQPara = {{0}};
	BUF_TYPE_t ulBufPara[FRAMES_PER_PAGE] = {{0}};
	FWSetPara_t ulFWSet[FRAMES_PER_PAGE] = {{0}};

	for ( PAGE = 0; PAGE < PAGE_CNT; PAGE++) {
		//for ( PLANE = 0 ; PLANE < 1; PLANE ++) {
		ulPCA = M_GET_PCA(LUN, BLOCK, PAGE, CE, CH, PLANE, 0, COP0_PCA_RULE_3);
		//UartPrintf("\n  Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);

		//write 16K data
		memset((void *)Write_buffer, 0x78, BC_16KB);
		for (ubFramIdx = 0; ubFramIdx < gub4kEntrysPerPlane; ubFramIdx++) {
			ulBufPara[ubFramIdx].A.ulBUF_ADR = Write_buffer + (ubFramIdx * BC_4KB);
			ulLCA[ubFramIdx] = ((U32 *)Write_buffer)[ubFramIdx];
			ulFWSet[ubFramIdx].ubZInfo = MAX_ZINFO;
		}

		COP0API_FillCOP0WriteSQUserData0Para(COP0WriteMode, &WriteSQPara);
		uoCallbackInfo.ulData.ulFLHInfo.btKeepTag = TRUE;
		WriteSQPara.ulPCA.ulAll = ulPCA;
		WriteSQPara.BufVld.ubBufType = BUF_TYPE_A;
		WriteSQPara.BufVld.pulBufInfoPtr = ulBufPara;
		WriteSQPara.pulLCAPtr = &ulLCA[0];
		WriteSQPara.pulFWSetPtr = ulFWSet;
		WriteSQPara.uoWriteConvRsInfo.btConvOnly = TRUE;
		WriteSQPara.UserData0.btSkipError = TRUE;
		WriteSQPara.UserData0.btSLCMode = TRUE;
		WriteSQPara.UserData0.btD1 = TRUE;
		WriteSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;	//Tie in
		WriteSQPara.UserData0.btRUTBps		  = TRUE; //bypass
		WriteSQPara.UserData0.btVBRMPBps	  = TRUE; //bypass
		WriteSQPara.UserData0.btDisableCacheProgram = FALSE;
		WriteSQPara.UserData0.ubMultiPlaneMode = MULTIPLANE_NO_USE;

		eCOP0Status = COP0API_SendWriteSQ(&WriteSQPara, &uoCallbackInfo);
		M_FW_ASSERT(ASSERT_BURNER_0x0186, eCOP0Status.btSendCmdSuccess);
		//}
	}

	while (!TagCOP0CheckDoneRelease(WriteSQPara.UserData0.uwTagID)) {
		if (!M_DB_CHECK_EMPTY(DB_ERR_MSG_CQ)) {
			FWErrRecorder();
		}
		if (!M_DB_CHECK_EMPTY(DB_COP0_CQ)) {
			COP0DelegateCmd();
		}
	}

	/**
		cache read
	*/

	UartPrintf("\n [ cache read, gubBurstsPerBank=%d BLOCK_NUM=%d run %d page] \n", gubBurstsPerBank, BLOCK, PAGE_CNT);



	//U16 uwTagId = TAG_ID_ALLOCATE;              //TAG
	U32 Read_buffer = BURNER_VENDOR_BUF_BASE + BC_16KB ;  //Read Buffer

	LUN = 0, PAGE = 0, CE = 0, CH = 0, PLANE = 0 ;
	U32 ulLocalPCA = INVALID_PCA;

	//UartPrintf("\n Start from Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d test write 4*16K\n", LUN, BLOCK, PAGE, CE, CH, Plane, ulLocalPCA);


#if 1
	//// PIO DBG  ////
	REG32 *pFlaReg = R32_FCTL_CH[0];
	FlaCEControl(0, 0, ENABLE);
	pFlaReg[R32_FCTL_PIO_CMD] = 0x98;
	FlaCEControl(0, 0, DISABLE);
	//// PIO DBG  ////
#endif

	COP0ReadSQPara_t ReadSQPara = {{0}};
	BUF_TYPE_t ulBufPara_cache = {0};

	gpComm2Andes_Info->ubDisable_Cache_Read = FALSE;

	//read each plane
	memset((void *)Read_buffer, 0x0, BC_16KB);

	//UartPrintf("\n Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, Plane, ulLocalPCA);
	for ( PAGE = 0; PAGE < PAGE_CNT; PAGE++) {
		ulLocalPCA = M_GET_PCA(LUN, BLOCK, PAGE, CE, CH, PLANE, 0, COP0_PCA_RULE_3);
		M_CLR_COP0_LCA_CMP(); //disable LCA compare

		ulBufPara_cache.A.ulBUF_ADR = Read_buffer; //read 4K data only
		COP0API_FillCOP0ReadSQUserData0Para(COP0_R_DEBUG_UNC, &ReadSQPara);

		ReadSQPara.UserData0.btSLCMode = TRUE;
		ReadSQPara.UserData0.btD1 = TRUE;
		ReadSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;
		ReadSQPara.UserData0.btRUTBps = TRUE;
		ReadSQPara.UserData0.btVBRMPBps = TRUE;
		//ReadSQPara.UserData0.TagID = TAG_ID_ALLOCATE;
		//UartPrintf("\n TagID:%x \n", ReadSQPara.UserData0.TagID);


		M_FWPCA_PCA_SET(ReadSQPara.ulPCA.ulAll, ulLocalPCA);
		ReadSQPara.BufVld.ubBufType = BUF_TYPE_A;
		ReadSQPara.BufVld.pulBufInfoPtr = &ulBufPara_cache;

		eCOP0Status = COP0API_SendReadSQ(&ReadSQPara, NULL);
		M_FW_ASSERT(ASSERT_BURNER_0x0187, eCOP0Status.btSendCmdSuccess);
		//uwTagID = ReadSQPara.UserData0.TagID;
		//UartPrintf("\n After TagID:%x ... \n", ReadSQPara.UserData0.TagID);
	}

	/************************************************************************
	 *                     Wait done                                          *
	 ************************************************************************/

#if 0 // Print out
	//UartPrintf("\n [ Print out: ] \n");
	U32 *r_ptr;
	r_ptr = (U32 *) Read_buffer; // 4 byte
	//UartPrintf("\n [ after r_ptr -> Read_buffer , Read_buffer address=%x	] \n", Read_buffer);
	for (int j = 0; j <  1024; j++) { //4個4K (因為U32, 1024=4K )

		//dump few data to check when copare fail
		if ( (j % 1024 == 0) || (j % 1024 == 1)  || (j % 1024 == 2) || (j % 1024 == 3)	|| (j % 1024 == 4) ) {
			UartPrintf("\n j=%d , [ r_ptr=%x ]	\n", j, *r_ptr);
		}
		r_ptr++;
	}
#endif

	/*while (!TagCOP0CheckDoneRelease(ReadSQPara.UserData0.TagID)) {
		UartPrintf("\n After btDone:%x \n", gpuoCmdTableMgr[COP0_CMD_TABLE_ID][ReadSQPara.UserData0.TagID].ulData.ulFLHInfo.btDone);

		if (!M_DB_CHECK_EMPTY(DB_ERR_MSG_CQ)) {
			FWErrRecorder();
		}
		if (!M_DB_CHECK_EMPTY(DB_COP0_CQ)) {
			COP0DelegateCmd();
		}
	}*/

	UartPrintf("\n [ END of %s ] \n", __func__);
}

void VUC_SLC_READ_CACHE_MULTI_PLANE(int BLOCK, int PAGE_CNT)
{

	UartPrintf("\n [ Enter on %s ]\n", __func__);

	UartPrintf("\n [ Need erase, prog First, gubBurstsPerBank=%d BLOCK_NUM=%d run %d page] \n", gubBurstsPerBank, BLOCK, PAGE_CNT);
	/**
		Erase first
	*/
	COP0EraseSQPara_t EraseSQPara = {{0}};
	cmd_table_t uoCallbackInfo = {0};
	COP0Status_t eCOP0Status;
	PCA_t ulFWPCA = {0};
	U32 ulPCA;

	int LUN = 0, PAGE = 0, CE = 0, CH = 0, PLANE = 0;

	UartPrintf("\n [ use slc Erase, gubBurstsPerBank=%d BLOCK_NUM=%d run %d page] \n", gubBurstsPerBank, BLOCK, PAGE_CNT);
	for ( PAGE = 0; PAGE < PAGE_CNT; PAGE++) {
		for ( PLANE = 0 ; PLANE < gubBurstsPerBank; PLANE ++) {
			// fill out Erase parameter
			EraseSQPara.UserData0.uwTagID = TAG_ID_ALLOCATE;
			EraseSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;
			EraseSQPara.UserData0.btSLCMode = TRUE;
			EraseSQPara.UserData0.btD1 = TRUE;
			EraseSQPara.UserData0.btNeedCQ = TRUE;
			EraseSQPara.UserData0.btVBRMPBps = TRUE;
			EraseSQPara.UserData0.btRUTBps = TRUE;
			M_FWPCA_ZBYTE_SET(ulFWPCA.ulAll, gPCAInfo.ubMaxZByte);
			M_FWPCA_CROSSFRAME_SET(ulFWPCA.ulAll, 0);
			uoCallbackInfo.ulData.btKeepTag = TRUE;

			//1. single plane erase
			ulPCA = M_GET_PCA(LUN, BLOCK, PAGE, CE, CH, PLANE, 0, COP0_PCA_RULE_3);
			//UartPrintf("\n	Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);
			EraseSQPara.UserData0.ubMultiPlaneMode = MULTIPLANE_NO_USE;
			EraseSQPara.ulPCA.ulAll = ulPCA;
			eCOP0Status = COP0API_SendEraseSQ(&EraseSQPara, &uoCallbackInfo);
			if (eCOP0Status.btSendCmdSuccess != TRUE) {
				UartPrintf("\n\n ------------- erase CMD Fail... ----------------");
				UartPrintf("\n	Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);
				while (1);
			}

			while (!TagCOP0CheckDoneRelease(EraseSQPara.UserData0.uwTagID)) {
				if (!M_DB_CHECK_EMPTY(DB_ERR_MSG_CQ)) {
					FWErrRecorder();
				}
				if (!M_DB_CHECK_EMPTY(DB_COP0_CQ)) {
					COP0DelegateCmd();
				}
			}
		}

		/*if (PAGE % 10 == 0) {
			UartPrintf("\n	Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);
		}*/

	}

	/**
		program
	*/
	UartPrintf("\n [ use slc program, gubBurstsPerBank=%d BLOCK_NUM=%d run %d page] \n", gubBurstsPerBank, BLOCK, PAGE_CNT);


	COP0WriteModeEnum_t COP0WriteMode;
	COP0WriteMode =  COP0_W_TABLE_PROGRAM; //check this

	U32 Write_buffer = BURNER_VENDOR_BUF_BASE;
	U8	ubFramIdx;
	LUN = 0, PAGE = 0, CE = 0, CH = 0, PLANE = 0;


	U32 ulLCA[FRAMES_PER_PAGE] = {0};
	COP0WriteSQPara_t WriteSQPara = {{0}};
	BUF_TYPE_t ulBufPara[FRAMES_PER_PAGE] = {{0}};
	FWSetPara_t ulFWSet[FRAMES_PER_PAGE] = {{0}};

	for ( PAGE = 0; PAGE < PAGE_CNT; PAGE++) {
		for ( PLANE = 0 ; PLANE < gubBurstsPerBank; PLANE ++) {
			ulPCA = M_GET_PCA(LUN, BLOCK, PAGE, CE, CH, PLANE, 0, COP0_PCA_RULE_3);
			//UartPrintf("\n  Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);

			//write 16K data
			memset((void *)Write_buffer, 0x78, BC_16KB);
			for (ubFramIdx = 0; ubFramIdx < gub4kEntrysPerPlane; ubFramIdx++) {
				ulBufPara[ubFramIdx].A.ulBUF_ADR = Write_buffer + (ubFramIdx * BC_4KB);
				ulLCA[ubFramIdx] = ((U32 *)Write_buffer)[ubFramIdx];
				ulFWSet[ubFramIdx].ubZInfo = MAX_ZINFO;
			}

			COP0API_FillCOP0WriteSQUserData0Para(COP0WriteMode, &WriteSQPara);
			uoCallbackInfo.ulData.ulFLHInfo.btKeepTag = TRUE;
			WriteSQPara.ulPCA.ulAll = ulPCA;
			WriteSQPara.BufVld.ubBufType = BUF_TYPE_A;
			WriteSQPara.BufVld.pulBufInfoPtr = ulBufPara;
			WriteSQPara.pulLCAPtr = &ulLCA[0];
			WriteSQPara.pulFWSetPtr = ulFWSet;
			WriteSQPara.uoWriteConvRsInfo.btConvOnly = TRUE;
			WriteSQPara.UserData0.btSkipError = TRUE;
			WriteSQPara.UserData0.btSLCMode = TRUE;
			WriteSQPara.UserData0.btD1 = TRUE;
			WriteSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;	//Tie in
			WriteSQPara.UserData0.btRUTBps		  = TRUE; //bypass
			WriteSQPara.UserData0.btVBRMPBps	  = TRUE; //bypass
			WriteSQPara.UserData0.btDisableCacheProgram = FALSE;
			WriteSQPara.UserData0.ubMultiPlaneMode = MULTIPLANE_NO_USE;

			eCOP0Status = COP0API_SendWriteSQ(&WriteSQPara, &uoCallbackInfo);
			M_FW_ASSERT(ASSERT_BURNER_0x0188, eCOP0Status.btSendCmdSuccess);
		}
	}

	while (!TagCOP0CheckDoneRelease(WriteSQPara.UserData0.uwTagID)) {
		if (!M_DB_CHECK_EMPTY(DB_ERR_MSG_CQ)) {
			FWErrRecorder();
		}
		if (!M_DB_CHECK_EMPTY(DB_COP0_CQ)) {
			COP0DelegateCmd();
		}
	}

	/**
		cache read
	*/

	UartPrintf("\n [ cache read, gubBurstsPerBank=%d BLOCK_NUM=%d run %d page] \n", gubBurstsPerBank, BLOCK, PAGE_CNT);



	//U16 uwTagId = TAG_ID_ALLOCATE;              //TAG
	U32 Read_buffer = BURNER_VENDOR_BUF_BASE + BC_16KB ;  //Read Buffer

	LUN = 0, PAGE = 0, CE = 0, CH = 0, PLANE = 0 ;
	U32 ulLocalPCA = INVALID_PCA;

	//UartPrintf("\n Start from Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d test write 4*16K\n", LUN, BLOCK, PAGE, CE, CH, Plane, ulLocalPCA);


#if 1
	//// PIO DBG  ////
	REG32 *pFlaReg = R32_FCTL_CH[0];
	FlaCEControl(0, 0, ENABLE);
	pFlaReg[R32_FCTL_PIO_CMD] = 0x98;
	FlaCEControl(0, 0, DISABLE);
	//// PIO DBG  ////
#endif

	COP0ReadSQPara_t ReadSQPara = {{0}};
	BUF_TYPE_t ulBufPara_cache = {0};

	gpComm2Andes_Info->ubDisable_Cache_Read = FALSE;
	//	gpComm2Andes_Info->ubDisable_Cache_Read = TRUE;


	//read each plane
	memset((void *)Read_buffer, 0x0, BC_16KB);

	//UartPrintf("\n Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, Plane, ulLocalPCA);
	for ( PAGE = 0; PAGE < PAGE_CNT; PAGE++) {
		for ( PLANE = 0 ; PLANE < gubBurstsPerBank; PLANE ++) {
			ulLocalPCA = M_GET_PCA(LUN, BLOCK, PAGE, CE, CH, PLANE, 0, COP0_PCA_RULE_3);
			M_CLR_COP0_LCA_CMP(); //disable LCA compare

			ulBufPara_cache.A.ulBUF_ADR = Read_buffer; //read 4K data only
			COP0API_FillCOP0ReadSQUserData0Para(COP0_R_DEBUG_UNC, &ReadSQPara);

			ReadSQPara.UserData0.btSLCMode = TRUE;
			ReadSQPara.UserData0.btD1 = TRUE;
			ReadSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;
			ReadSQPara.UserData0.btRUTBps = TRUE;
			ReadSQPara.UserData0.btVBRMPBps = TRUE;
			//ReadSQPara.UserData0.TagID = TAG_ID_ALLOCATE;
			//UartPrintf("\n TagID:%x \n", ReadSQPara.UserData0.TagID);


			M_FWPCA_PCA_SET(ReadSQPara.ulPCA.ulAll, ulLocalPCA);
			ReadSQPara.BufVld.ubBufType = BUF_TYPE_A;
			ReadSQPara.BufVld.pulBufInfoPtr = &ulBufPara_cache;

			eCOP0Status = COP0API_SendReadSQ(&ReadSQPara, NULL);
			M_FW_ASSERT(ASSERT_BURNER_0x0189, eCOP0Status.btSendCmdSuccess);
			//uwTagID = ReadSQPara.UserData0.TagID;
			//UartPrintf("\n After TagID:%x ... \n", ReadSQPara.UserData0.TagID);
		}
	}

	/************************************************************************
	 *                     Wait done                                          *
	 ************************************************************************/

#if 0 // Print out
	//UartPrintf("\n [ Print out: ] \n");
	U32 *r_ptr;
	r_ptr = (U32 *) Read_buffer; // 4 byte
	//UartPrintf("\n [ after r_ptr -> Read_buffer , Read_buffer address=%x	] \n", Read_buffer);
	for (int j = 0; j <  1024; j++) { //4個4K (因為U32, 1024=4K )

		//dump few data to check when copare fail
		if ( (j % 1024 == 0) || (j % 1024 == 1)  || (j % 1024 == 2) || (j % 1024 == 3)	|| (j % 1024 == 4) ) {
			UartPrintf("\n j=%d , [ r_ptr=%x ]	\n", j, *r_ptr);
		}
		r_ptr++;
	}
#endif


	/*while (!TagCOP0CheckDoneRelease(ReadSQPara.UserData0.TagID)) {
		UartPrintf("\n After btDone:%x \n", gpuoCmdTableMgr[COP0_CMD_TABLE_ID][ReadSQPara.UserData0.TagID].ulData.ulFLHInfo.btDone);

		if (!M_DB_CHECK_EMPTY(DB_ERR_MSG_CQ)) {
			FWErrRecorder();
		}
		if (!M_DB_CHECK_EMPTY(DB_COP0_CQ)) {
			COP0DelegateCmd();
		}
	}*/

	UartPrintf("\n [ END of %s ] \n", __func__);
}


//VUC_SLC_READ_SINGLE_PLANE 已包含erase, prog
void VUC_SLC_READ_SINGLE_PLANE(int BLOCK)
{
	UartPrintf("\n [ Enter on %s ] \n", __func__);

	/* ERASE */
	COP0EraseSQPara_t EraseSQPara = {{0}};
	cmd_table_t uoCallbackInfo = {0};
	COP0Status_t eCOP0Status;
	PCA_t ulFWPCA = {0};
	U32 ulPCA;


	int LUN = 0, PAGE = 10, CE = 1, CH = 0, PLANE = 0;

	// fill out Erase parameter
	EraseSQPara.UserData0.uwTagID = TAG_ID_ALLOCATE;
	EraseSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;
	EraseSQPara.UserData0.btSLCMode = TRUE;
	EraseSQPara.UserData0.btD1 = TRUE;


	EraseSQPara.UserData0.btNeedCQ = TRUE;
	EraseSQPara.UserData0.btVBRMPBps = TRUE;
	EraseSQPara.UserData0.btRUTBps = TRUE;
	M_FWPCA_ZBYTE_SET(ulFWPCA.ulAll, gPCAInfo.ubMaxZByte);
	M_FWPCA_CROSSFRAME_SET(ulFWPCA.ulAll, 0);
	uoCallbackInfo.ulData.btKeepTag = TRUE;

	//	if (EraseSQPara.UserData0.btSLCMode) {
	//		UartPrintf("\n  btSLCMode=TRUE, ubMultiPlaneMode=%d \n", EraseSQPara.UserData0.ubMultiPlaneMode );    //gary
	//	}


	for (PLANE = 0; PLANE < 1; PLANE++) {

		ulPCA = M_GET_PCA(LUN, BLOCK, PAGE, CE, CH, PLANE, 0, COP0_PCA_RULE_3);
		//UartPrintf("\n  Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d, rule3 \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);
		EraseSQPara.UserData0.ubMultiPlaneMode = MULTIPLANE_NO_USE;
		EraseSQPara.ulPCA.ulAll = ulPCA;

		eCOP0Status = COP0API_SendEraseSQ(&EraseSQPara, &uoCallbackInfo);
		if (eCOP0Status.btSendCmdSuccess != TRUE) {
			UartPrintf("\n\n ------------- erase CMD Fail... ----------------");
			while (1);
		}

	}

	while (!TagCOP0CheckDoneRelease(EraseSQPara.UserData0.uwTagID)) {
		if (!M_DB_CHECK_EMPTY(DB_ERR_MSG_CQ)) {
			FWErrRecorder();
		}
		if (!M_DB_CHECK_EMPTY(DB_COP0_CQ)) {
			COP0DelegateCmd();
		}
	}

	/* PROG & READ */
	U32 Read_buffer = BURNER_VENDOR_BUF_BASE + BC_16KB;
	LUN = 0,  PAGE = 0, CE = 0, CH = 0, PLANE = 0;

#if (WriteDataVerify)
	U32 Write_buffer = BURNER_VENDOR_BUF_BASE;
#endif

	ulPCA = M_GET_PCA(LUN, BLOCK, PAGE, CE, CH, PLANE, 0, COP0_PCA_RULE_3);
	//UartPrintf("\n  Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);

#if (WriteDataVerify)
	UartPrintf("\n [ Prepare write data in buffer with address rule ] \n");

	U32 *ww_ptr;
	ww_ptr = (U32 *) Write_buffer;

	for (int j = 0; j < 4096; j++) {
		*ww_ptr = (U32 )ww_ptr;
		if ( (j % 1024 == 0) || (j % 1024 == 1) ) {
			//UartPrintf("\n *ww_ptr=%x \n", *ww_ptr);
		}

		ww_ptr++;
	}

	VUC_Program(COP0_W_TABLE_PROGRAM, ulPCA, Write_buffer);
	while (TRUE == M_COP0_GET_IDLE()) {
		FWCop0Waiting();
	}
#endif

	memset((void *)Read_buffer, 0x0, BC_16KB);
	VUC_ReadSinglePage(Read_buffer, ulPCA, 0xF/*NULL*/, gub4kEntrysPerPlane, 0); //read 16K

#if (WriteDataVerify)

	// debug for dump detail
	UartPrintf("\n [ dump detail for read buffer and write buffer ] \n");
	U32 *r_ptr;
	U32 *w_ptr;
	r_ptr = (U32 *) Read_buffer; // 4 byte
	w_ptr = (U32 *) Write_buffer;

	for (int j = 0; j < 4 * 1024; j++) { //4個4K (4*1024)

		//dump few data to check when copare fail
		if ( (j % 1024 == 0) || (j % 1024 == 1)  || (j % 1024 == 2) || (j % 1024 == 3)	|| (j % 1024 == 4) ) {
			UartPrintf("\n j=%d , [ r_ptr=%x ],[ w_ptr=%x ]  \n", j, *r_ptr, *w_ptr);
		}
		r_ptr++;
		w_ptr++;
	}
	U8 cmp_res = memcmp((void *)Write_buffer, (void *)Read_buffer, BC_16KB);
	if (cmp_res == 0) {
		UartPrintf("\n [ Compare Pass ! ]\n");

	}
	else {
		UartPrintf("\n [ Compare FAIL ! ]\n");

	}

#else
	UartPrintf("\n [ Print out: ] \n");
	U32 *r_ptr;
	r_ptr = (U32 *) Read_buffer; // 4 byte
	//UartPrintf("\n [ after r_ptr -> Read_buffer , Read_buffer address=%x	] \n", Read_buffer);
	for (int j = 1; j < 4 * 1024; j++) { //4個4K (因為U32, 1024=4K )

		//dump few data to check when copare fail
		if ( (j % 1024 == 0) || (j % 1024 == 1)  || (j % 1024 == 2) || (j % 1024 == 3)	|| (j % 1024 == 4) ) {
			UartPrintf("\n j=%d , [ r_ptr=%x ]	\n", j, *r_ptr);
		}
		r_ptr++;
	}

#endif


	while (TRUE == M_COP0_GET_IDLE()) {
		FWCop0Waiting();
	}
	UartPrintf("\n [ END of %s ] \n", __func__);

}

//寫入前要確認有先erase
void VUC_SLC_READ_MULTI_PLANE(int BLOCK)
{

	UartPrintf("\n [ Enter on %s ]\n", __func__);

	/************************************************************************
	 *						Init						     	     *
	 ************************************************************************/

	/* ERASE */
	COP0EraseSQPara_t EraseSQPara = {{0}};
	cmd_table_t uoCallbackInfo = {0};
	COP0Status_t eCOP0Status;
	PCA_t ulFWPCA = {0};
	U32 ulPCA;


	int LUN = 0, PAGE = 0, CE = 0, CH = 0, PLANE = 0;

	// fill out Erase parameter
	EraseSQPara.UserData0.uwTagID = TAG_ID_ALLOCATE;
	EraseSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;
	EraseSQPara.UserData0.btSLCMode = TRUE;
	EraseSQPara.UserData0.btD1 = TRUE;

	EraseSQPara.UserData0.btNeedCQ = TRUE;
	EraseSQPara.UserData0.btVBRMPBps = TRUE;
	EraseSQPara.UserData0.btRUTBps = TRUE;
	M_FWPCA_ZBYTE_SET(ulFWPCA.ulAll, gPCAInfo.ubMaxZByte);
	M_FWPCA_CROSSFRAME_SET(ulFWPCA.ulAll, 0);
	uoCallbackInfo.ulData.btKeepTag = TRUE;

	for (PLANE = 0; PLANE < gubBurstsPerBank; PLANE++) {

		ulPCA = M_GET_PCA(LUN, BLOCK, PAGE, CE, CH, PLANE, 0, COP0_PCA_RULE_3);
		//UartPrintf("\n  Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);

		EraseSQPara.ulPCA.ulAll = ulPCA;
		if ( PLANE == gubBurstsPerBank - 1 ) {
			EraseSQPara.UserData0.ubMultiPlaneMode = MULTIPLANE_END;
			//UartPrintf("\n  MULTIPLANE_END!!!~ vbrmp \n");
		}
		else {
			EraseSQPara.UserData0.ubMultiPlaneMode = MULTIPLANE_ING;
		}


		eCOP0Status = COP0API_SendEraseSQ(&EraseSQPara, &uoCallbackInfo);
		if (eCOP0Status.btSendCmdSuccess != TRUE) {
			UartPrintf("\n\n ------------- erase CMD Fail... ----------------");
			while (1);
		}

	}

	while (!TagCOP0CheckDoneRelease(EraseSQPara.UserData0.uwTagID)) {
		if (!M_DB_CHECK_EMPTY(DB_ERR_MSG_CQ)) {
			FWErrRecorder();
		}
		if (!M_DB_CHECK_EMPTY(DB_COP0_CQ)) {
			COP0DelegateCmd();
		}
	}

	//U16 uwTagId = TAG_ID_ALLOCATE;              //TAG
	U32 Read_buffer = BURNER_VENDOR_BUF_BASE + BC_16KB ;  //Read Buffer
	U32 Write_buffer = BURNER_VENDOR_BUF_BASE;

	LUN = 0, PAGE = 0, CE = 0, CH = 0, PLANE = 0 ;
	U32 ulLocalPCA = INVALID_PCA;
	ulLocalPCA = M_GET_PCA(LUN, BLOCK, PAGE, CE, CH, PLANE, 0, COP0_PCA_RULE_3);
	//UartPrintf("\n Start from Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d test write 4*16K\n", LUN, BLOCK, PAGE, CE, CH, Plane, ulLocalPCA);



	/* PROG & READ */
	//UartPrintf("\n [ Prepare write data in buffer with address rule ] \n");
	U32 *ww_ptr;
	ww_ptr = (U32 *) Write_buffer;

	U32 temp_pca = ulLocalPCA; //先寫入data到要讀的4個plane 16K data
	for (PLANE = 0; PLANE < gubBurstsPerBank; PLANE++) {
		if (PLANE == 0 ) {
			memset((void *)Write_buffer, (0x11111111), BC_16KB);
		}
		else {
			memset((void *)Write_buffer, (0x22222222 * PLANE), BC_16KB);
		}

		VUC_Program(COP0_W_TABLE_PROGRAM, temp_pca, Write_buffer);
		temp_pca = temp_pca +  gub4kEntrysPerPlane ;
	}
	while (TRUE == M_COP0_GET_IDLE()) {
		FWCop0Waiting();
	}


#if 1
	//// PIO DBG  ////
	REG32 *pFlaReg = R32_FCTL_CH[0];
	FlaCEControl(0, 0, ENABLE);
	pFlaReg[R32_FCTL_PIO_CMD] = 0x98;
	FlaCEControl(0, 0, DISABLE);
	//// PIO DBG  ////
#endif

	COP0ReadSQPara_t ReadSQPara = {{0}};
	BUF_TYPE_t ulBufPara = {0};

	//read each plane
	memset((void *)Read_buffer, 0x0, BC_16KB);
	for (PLANE = 0; PLANE < gubBurstsPerBank; PLANE++) {
		//UartPrintf("\n Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, Plane, ulLocalPCA);

		M_CLR_COP0_LCA_CMP(); //disable LCA compare

		ulBufPara.A.ulBUF_ADR = Read_buffer + (PLANE * BC_4KB ); //read 4K data foreach plane only
		COP0API_FillCOP0ReadSQUserData0Para(COP0_R_DEBUG_UNC, &ReadSQPara);

		ReadSQPara.UserData0.btSLCMode = TRUE;
		ReadSQPara.UserData0.btD1 = TRUE;
		ReadSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;
		ReadSQPara.UserData0.btRUTBps = TRUE;
		ReadSQPara.UserData0.btVBRMPBps = TRUE;
		ReadSQPara.UserData0.TagID = TAG_ID_ALLOCATE;


		M_FWPCA_PCA_SET(ReadSQPara.ulPCA.ulAll, ulLocalPCA);
		ReadSQPara.BufVld.ubBufType = BUF_TYPE_A;
		ReadSQPara.BufVld.pulBufInfoPtr = &ulBufPara;

		eCOP0Status = COP0API_SendReadSQ(&ReadSQPara, NULL);
		M_FW_ASSERT(ASSERT_BURNER_0x018A, eCOP0Status.btSendCmdSuccess);

		ulLocalPCA = ulLocalPCA + gub4kEntrysPerPlane ; //increase PCA by 4 ( 4 frame per plane)

	}

	/************************************************************************
	 *                     Wait done                                          *
	 ************************************************************************/

#if 1 // Print out
	//UartPrintf("\n [ Print out: ] \n");
	U32 *r_ptr;
	r_ptr = (U32 *) Read_buffer; // 4 byte
	//UartPrintf("\n [ after r_ptr -> Read_buffer , Read_buffer address=%x	] \n", Read_buffer);
	for (int j = 1; j < 4 * 1024; j++) { //4個4K (因為U32, 1024=4K )

		//dump few data to check when copare fail
		if ( (j % 1024 == 0)  || (j % 1024 == 1)  || (j % 1024 == 2) || (j % 1024 == 3)	|| (j % 1024 == 4) ) {
			UartPrintf("\n j=%d , [ r_ptr=%x ]	\n", j, *r_ptr);
		}
		r_ptr++;
	}
#endif

	/*
		while (!TagCOP0CheckDoneRelease(uwTagID)) {
			if (!M_DB_CHECK_EMPTY(DB_ERR_MSG_CQ)) {
				FWErrRecorder();
			}
			if (!M_DB_CHECK_EMPTY(DB_COP0_CQ)) {
				COP0DelegateCmd();
			}
		}*/
	UartPrintf("\n [ END of %s ] \n", __func__);
}

void VUC_SLC_ERASE_SINGLE_PLANE(int BLOCK)
{
	UartPrintf("\n [ Enter on %s ] \n", __func__);

	/* ERASE */
	COP0EraseSQPara_t EraseSQPara = {{0}};
	cmd_table_t uoCallbackInfo = {0};
	COP0Status_t eCOP0Status;
	PCA_t ulFWPCA = {0};
	U32 ulPCA;


	int LUN = 0, PAGE = 10, CE = 0, CH = 0, PLANE = 0;

	// fill out Erase parameter
	EraseSQPara.UserData0.uwTagID = TAG_ID_ALLOCATE;
	EraseSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;
	EraseSQPara.UserData0.btSLCMode = TRUE;
	EraseSQPara.UserData0.btD1 = TRUE;


	EraseSQPara.UserData0.btNeedCQ = TRUE;
	EraseSQPara.UserData0.btVBRMPBps = TRUE;
	EraseSQPara.UserData0.btRUTBps = TRUE;
	M_FWPCA_ZBYTE_SET(ulFWPCA.ulAll, gPCAInfo.ubMaxZByte);
	M_FWPCA_CROSSFRAME_SET(ulFWPCA.ulAll, 0);
	uoCallbackInfo.ulData.btKeepTag = TRUE;

	//	if (EraseSQPara.UserData0.btSLCMode) {
	//		UartPrintf("\n  btSLCMode=TRUE, ubMultiPlaneMode=%d \n", EraseSQPara.UserData0.ubMultiPlaneMode );    //gary
	//	}


	for (PLANE = 0; PLANE < 1; PLANE++) {

		ulPCA = M_GET_PCA(LUN, BLOCK, PAGE, CE, CH, PLANE, 0, COP0_PCA_RULE_3);
		//UartPrintf("\n  Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d, rule3 \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);
		EraseSQPara.UserData0.ubMultiPlaneMode = MULTIPLANE_NO_USE;
		EraseSQPara.ulPCA.ulAll = ulPCA;

		eCOP0Status = COP0API_SendEraseSQ(&EraseSQPara, &uoCallbackInfo);
		if (eCOP0Status.btSendCmdSuccess != TRUE) {
			UartPrintf("\n\n ------------- erase CMD Fail... ----------------");
			while (1);
		}

	}

	while (!TagCOP0CheckDoneRelease(EraseSQPara.UserData0.uwTagID)) {
		if (!M_DB_CHECK_EMPTY(DB_ERR_MSG_CQ)) {
			FWErrRecorder();
		}
		if (!M_DB_CHECK_EMPTY(DB_COP0_CQ)) {
			COP0DelegateCmd();
		}
	}

	UartPrintf("\n [ END of %s ] \n", __func__);


}

void VUC_SLC_ERASE_MULTI_PLANE(int BLOCK)
{

	UartPrintf("\n [ Enter %s ] \n", __func__);

	/* ERASE */
	COP0EraseSQPara_t EraseSQPara = {{0}};
	cmd_table_t uoCallbackInfo = {0};
	COP0Status_t eCOP0Status;
	PCA_t ulFWPCA = {0};
	U32 ulPCA;


	int LUN = 0, PAGE = 0, CE = 0, CH = 0, PLANE = 0;

	// fill out Erase parameter
	EraseSQPara.UserData0.uwTagID = TAG_ID_ALLOCATE;
	EraseSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;
	EraseSQPara.UserData0.btSLCMode = TRUE;
	EraseSQPara.UserData0.btD1 = TRUE;

	EraseSQPara.UserData0.btNeedCQ = TRUE;
	EraseSQPara.UserData0.btVBRMPBps = TRUE;
	EraseSQPara.UserData0.btRUTBps = TRUE;
	M_FWPCA_ZBYTE_SET(ulFWPCA.ulAll, gPCAInfo.ubMaxZByte);
	M_FWPCA_CROSSFRAME_SET(ulFWPCA.ulAll, 0);
	uoCallbackInfo.ulData.btKeepTag = TRUE;

	for (PLANE = 0; PLANE < gubBurstsPerBank; PLANE++) {

		ulPCA = M_GET_PCA(LUN, BLOCK, PAGE, CE, CH, PLANE, 0, COP0_PCA_RULE_3);
		//UartPrintf("\n  Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);

		EraseSQPara.ulPCA.ulAll = ulPCA;
		if ( PLANE == gubBurstsPerBank - 1 ) {
			EraseSQPara.UserData0.ubMultiPlaneMode = MULTIPLANE_END;
			//UartPrintf("\n  MULTIPLANE_END!!!~ vbrmp \n");
		}
		else {
			EraseSQPara.UserData0.ubMultiPlaneMode = MULTIPLANE_ING;
		}


		eCOP0Status = COP0API_SendEraseSQ(&EraseSQPara, &uoCallbackInfo);
		if (eCOP0Status.btSendCmdSuccess != TRUE) {
			UartPrintf("\n\n ------------- erase CMD Fail... ----------------");
			while (1);
		}

	}

	while (!TagCOP0CheckDoneRelease(EraseSQPara.UserData0.uwTagID)) {
		if (!M_DB_CHECK_EMPTY(DB_ERR_MSG_CQ)) {
			FWErrRecorder();
		}
		if (!M_DB_CHECK_EMPTY(DB_COP0_CQ)) {
			COP0DelegateCmd();
		}
	}
	UartPrintf("\n [ END of %s ] \n", __func__);


}


void VUC_SLC_Erase_Prog_Read_Verify(int BLOCK, int PAGE_CNT)
{
	/*Step 1. ERASE */
	COP0EraseSQPara_t EraseSQPara = {{0}};
	cmd_table_t uoCallbackInfo = {0};
	COP0Status_t eCOP0Status;
	PCA_t ulFWPCA = {0};
	U32 ulPCA;

	int LUN = 0, PAGE = 0, CE = 0, CH = 0, PLANE = 0;

	UartPrintf("\n [ Erase, gubBurstsPerBank=%d BLOCK_NUM=%d run %d page] \n", gubBurstsPerBank, BLOCK, PAGE_CNT);
#if 1
	for ( PAGE = 0; PAGE < PAGE_CNT; PAGE++) {
		for ( LUN = 0; LUN < gubDieNumber; LUN++) {
			for ( CE = 0; CE < gubCEsPerSuperPage / 2; CE ++) {
				for ( CH = 0; CH < 2; CH++) {
					for ( PLANE = 0 ; PLANE < gubBurstsPerBank; PLANE ++) {
#endif
						// fill out Erase parameter
						EraseSQPara.UserData0.uwTagID = TAG_ID_ALLOCATE;
						EraseSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;
						EraseSQPara.UserData0.btSLCMode = TRUE;
						EraseSQPara.UserData0.btD1 = TRUE;
						EraseSQPara.UserData0.btNeedCQ = TRUE;
						EraseSQPara.UserData0.btVBRMPBps = TRUE;
						EraseSQPara.UserData0.btRUTBps = TRUE;
						M_FWPCA_ZBYTE_SET(ulFWPCA.ulAll, gPCAInfo.ubMaxZByte);
						M_FWPCA_CROSSFRAME_SET(ulFWPCA.ulAll, 0);
						uoCallbackInfo.ulData.btKeepTag = TRUE;

						//1. single plane erase
						ulPCA = M_GET_PCA(LUN, BLOCK, PAGE, CE, CH, PLANE, 0, COP0_PCA_RULE_3);
						//UartPrintf("\n	Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);
						EraseSQPara.UserData0.ubMultiPlaneMode = MULTIPLANE_NO_USE;
						EraseSQPara.ulPCA.ulAll = ulPCA;

						eCOP0Status = COP0API_SendEraseSQ(&EraseSQPara, &uoCallbackInfo);
						if (eCOP0Status.btSendCmdSuccess != TRUE) {
							UartPrintf("\n\n ------------- erase CMD Fail... ----------------");
							UartPrintf("\n	Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);
							while (1);
						}

						while (!TagCOP0CheckDoneRelease(EraseSQPara.UserData0.uwTagID)) {
							if (!M_DB_CHECK_EMPTY(DB_ERR_MSG_CQ)) {
								FWErrRecorder();
							}
							if (!M_DB_CHECK_EMPTY(DB_COP0_CQ)) {
								COP0DelegateCmd();
							}
						}
#if 1
					}
				}
			}
		}
		if (PAGE % 10 == 0) {
			UartPrintf("\n	Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);
		}
	}
#endif

	UartPrintf("\n [ R/W Test start ] \n");


	COP0WriteModeEnum_t COP0WriteMode;
	COP0WriteMode = COP0_W_TABLE_PROGRAM;

	U32 Read_buffer = BURNER_VENDOR_BUF_BASE + BC_16KB;
	U32 Write_buffer = BURNER_VENDOR_BUF_BASE;
	U8 cmp_res;

	memset((void *)Write_buffer, 0x0, BC_16KB);
	memset((void *)Read_buffer, 0x0, BC_16KB);
#if 1
	for ( PAGE = 0; PAGE < PAGE_CNT; PAGE++) {
		for ( LUN = 0; LUN < gubDieNumber; LUN++) {
			for ( CE = 0; CE < gubCEsPerSuperPage / 2; CE ++) {
				for ( CH = 0; CH < 2; CH++) {
					for ( PLANE = 0 ; PLANE < gubBurstsPerBank; PLANE ++) {
#endif
						ulPCA = M_GET_PCA(LUN, BLOCK, PAGE, CE, CH, PLANE, 0, COP0_PCA_RULE_3);
						//UartPrintf("\nPage:%d  BLOCK:%d  ce:%d  ch%d  plane:%d ulPCA:%d \n", PAGE, BLOCK, CE, CH, PLANE, ulPCA);

						//Step 2. prepare write data in buffer with address rule
						U32 *ww_ptr;
						ww_ptr = (U32 *) Write_buffer;
#if 0 // use fixed pattern
						memset((void *)Write_buffer, 0x777, BC_16KB);
#else //use address pattern
						for (int j = 0; j < 4096; j++) {
							*ww_ptr = (U32 )ww_ptr;
							if ( (j % 1024 == 0) || (j % 1024 == 1) ) {
								//UartPrintf("\n *ww_ptr=%x \n", *ww_ptr);
							}

							ww_ptr++;
						}
#endif
						//Step 3. program
						VUC_Program(COP0WriteMode, ulPCA, Write_buffer);

						while (TRUE == M_COP0_GET_IDLE()) {
							FWCop0Waiting();
						}
						//UartPrintf("\n [ Program Done with PCA=%d]\n", ulPCA);


						//Step 4. read
						U32 *point;
						memset((void *)Read_buffer, 0, BC_16KB);
						point = (U32 *)Read_buffer;
						//UartPrintf("\n [ Before check Read_buffer - 64B, Read_buffer=%x  ] \n", Read_buffer);
						VUC_ReadSinglePage(Read_buffer, ulPCA, 0xF/*NULL*/, gub4kEntrysPerPlane, 0);


						while (TRUE == M_COP0_GET_IDLE()) {
							FWCop0Waiting();
						}
						//UartPrintf("\n [ Read Done with PCA=%d]\n", ulPCA);


						//Step 5. verify
						cmp_res = memcmp((void *)Write_buffer, (void *)Read_buffer, BC_16KB);

						if (cmp_res == 0) {
							//UartPrintf("\n [ Compare Pass ! ]\n");

						}
						else {
							//UartPrintf("\nPage:%d  BLOCK:%d  ce:%d  ch%d  plane:%d ulPCA:%d \n", PAGE, BLOCK, CE, CH, PLANE, ulPCA);
							UartPrintf("\n [ Compare Fail ! ]\n");
							UartPrintf("\nAt Page:%d  BLOCK:%d  ce:%d  ch%d  plane:%d ulPCA:%d \n", PAGE, BLOCK, CE, CH, PLANE, ulPCA);

							//debug for dump detail
							U32 *r_ptr;
							U32 *w_ptr;
							r_ptr = (U32 *) Read_buffer; // 4 byte
							w_ptr = (U32 *) Write_buffer;
							//UartPrintf("\n [ after r_ptr -> Read_buffer , Read_buffer address=%x  ] \n", Read_buffer);
							for (int j = 1; j < 4 * 1024; j++) { //4個4K (4*1024)

								if (*r_ptr != *w_ptr) {
									//dump few data to check when copare fail
									if ( (j % 1024 == 0) || (j % 1024 == 1)  || (j % 1024 == 2) || (j % 1024 == 3)  || (j % 1024 == 4) ) {
										UartPrintf("\n j=%d Diff, [ r_ptr=%x ],[ w_ptr=%x ]  \n", j, *r_ptr, *w_ptr);
									}
									//UartPrintf("\n j=%d Diff, [ r_ptr=%x ],[ w_ptr=%x ]  \n", j, *r_ptr, *w_ptr);
								}
								else {
									if ( (j % 1024 == 0) || (j % 1024 == 1)  || (j % 1024 == 2) || (j % 1024 == 3)  || (j % 1024 == 4) ) {
										UartPrintf("\n j=%d Same, [ r_ptr=%x ],[ w_ptr=%x ]  \n", j, *r_ptr, *w_ptr);
									}

								}
								r_ptr++;
								w_ptr++;
							}
							UartPrintf("\n [ debug Done ]\n");
							while (1);
						}
#if 1

					}
				}
			}
		}
		if (PAGE % 10 == 0) {
			UartPrintf("\n	Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);
		}
	}
#endif

	if (cmp_res == 0) {
		UartPrintf("\n [ ALL Compare Pass ! ]\n");
	}

	UartPrintf("\n [ END on %s ] \n", __func__);


}

#if 0
void TestGlobalTriggerErase()
{
	BlockInfo_t ulEraseBlk;
	UartPrintf("\n [ Enter on %s ] \n", __func__);

	ulEraseBlk.B.Block 			= 0;
	ulEraseBlk.B.CE			    = 0;
	ulEraseBlk.B.PhysicalPlane  = 0;
	ulEraseBlk.B.LUN 			= 0;
	ulEraseBlk.B.DieIL 			= 0;
	ulEraseBlk.B.btSLC 			= TRUE;
	ulEraseBlk.B.btD1 			= FALSE;

	if (CopyBlockErase(&ulEraseBlk)) {
		UartPrintf("\n [ Success] \n");
		//no break;
	}
	UartPrintf("\n [ END of %s ] \n", __func__);


}
#endif
#if 0
void TestGlobalTriggerRead(int BLOCK, U8 ubPCARule) // Referemce from CopyBlockDirectRead4k
{

	UartPrintf("\n [ Enter on %s ] \n", __func__);
	VUC_SLC_PROC_SINGLE_PLANE(BLOCK);

	int LUN = 0, PAGE = 0, CE = 0, CH = 0, PLANE = 0;


#if 1
	//// PIO DBG  ////
	REG32 *pFlaReg = R32_FCTL_CH[0];
	FlaCEControl(0, 0, ENABLE);
	pFlaReg[R32_FCTL_PIO_CMD] = 0x98;
	FlaCEControl(0, 0, DISABLE);
	//// PIO DBG  ////
#endif



	U32 COP0_Read_buffer = BURNER_VENDOR_BUF_BASE + BC_16KB ; //要注意上一次read有使用到

	U32 ulBufAddr = BURNER_VENDOR_BUF_BASE; //要注意上一次writer是否有使用到

	/* Global Trigger Read*/
	{


		U32 ulDataPCA;
		memset((void *)ulBufAddr, 0x0, BC_16KB); //如果write 使用相同buffer addr記得清空
		UartPrintf("\n [ Before Global Read Print out(%x): ] \n", ulBufAddr);

		U32 *r_ptr1;
		r_ptr1 = (U32 *) ulBufAddr; // 4 byte
		for (int j = 1; j < 1 * 1023; j++) { //多少個4K (因為U32, 1024=4K )

			//dump few data to check when copare fail
			if ( (j % 1024 == 0) || (j % 1024 == 1)  || (j % 1024 == 2) || (j % 1024 == 3)	|| (j % 1024 == 4) ) {
				UartPrintf("\n j=%d , [ r_ptr=%x ]	\n", j, *r_ptr1);
			}
			r_ptr1++;
		}

		UartPrintf("\n [ After Global Trigger Read Print out: ] \n");
		LUN = 0, PAGE = 0, CE = 0, CH = 0, PLANE = 0;
		ulDataPCA = M_GET_PCA(LUN, BLOCK, PAGE, CE, CH, PLANE, 0, ubPCARule);
		UartPrintf("\n Read Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulDataPCA);


		U8 ubReadFail = FALSE;
		CopyBlkFormMTParameter_t uoFormMTParameter;
		MTCfg_t MT_Cfg;
		U64 uoPCA = ulDataPCA;

		U8 ubQIdx = 0;
		U8 ubSLC = TRUE;
		U8 ubD1 = TRUE;
		U16 uwFormMTConfig = 0;
		U8 ubCh = 0, ubCE = 0, ubDieIL = 0;
		U8 ubMTidx_cmd, ubMTidx_dma;
		//		U8 ubL4Knum = 1;
		//U8 ubFailPhase = ERROR_LOG_PHASE_COPY;
		//ErrLogInfo_t ErrorLogInfo;

		//gErrHdl.ProgramFail.uoCopyBlk.ReadFail_BM = 0;



		M_PCA_RULE_IDX(ubPCARule, ubSLC, ubD1);
		M_PCA_GET_INFO(ubCh, ulDataPCA, gPCARule_Channel, ubPCARule);
		M_PCA_GET_INFO(ubCE, ulDataPCA, gPCARule_Bank, ubPCARule);
		M_PCA_GET_INFO(ubDieIL, ulDataPCA, gPCARule_Die_IL, ubPCARule);
		//UartPrintf( "\nCE:%b, CH: %b, DIEIL :%b, ubPCARULE: %b", ubCh, ubCE, ubDieIL, ubPCARule);


		//CMD
		ubQIdx = M_GLOBAL_Q_IDX(ubCh, ubCE, BIT(gPCARule_Channel.ubBit_No));
		ubMTidx_cmd = CopyBlockGetFreeMT();
		uoFormMTParameter.uoAll = 0;
		uoFormMTParameter.Cmd = COPYBLOCK_CMD_READ;
		uoFormMTParameter.PCARule = ubPCARule;
		//uoFormMTParameter.RandomSeedMode = gErrHdl.ProgramFail.Rescue.RandomSeedMode;
		uoFormMTParameter.L4KNum = 2; //read 4k only
		uoFormMTParameter.ubQueueIdx = ubQIdx;
		uoFormMTParameter.ubMTIdx = ubMTidx_cmd;
		uoFormMTParameter.uwFPU = CopyBlockGetFPU(COPYBLOCK_CMD_READ, ubSLC, 0);
		uoFormMTParameter.uwMTConfig = (COPYBLOCK_AUTOPOLL_EN_BIT);
		CopyBlockFormCMDMT(uoFormMTParameter, uoPCA, FIP_INVALID_VIRTUAL_ADDR);
		MT_Cfg.uoAll = 0;
		MT_Cfg.bits_recc.ubMT_IDX = ubMTidx_cmd;
		MT_Cfg.bits_recc.ubQUE_IDX = ubQIdx;
		FlaGlobalTrigger(&MT_Cfg);
		CopyBlockGetFIPCQ(ubMTidx_cmd);



		//DMA
		ubMTidx_dma = CopyBlockGetFreeMT();
		uwFormMTConfig = (COPYBLOCK_BURNER_SEED_BIT);
		uoFormMTParameter.ubMTIdx = ubMTidx_dma;
		uoFormMTParameter.uwFPU = CopyBlockGetFPU(COPYBLOCK_CMD_DATAOUT, ubSLC, 0);

		uoFormMTParameter.uwMTConfig = uwFormMTConfig;
		CopyBlockFormDMAMT(uoFormMTParameter, uoPCA, 0, 0);
		CopyBlockSetL4kTable(ubMTidx_dma, 0, 0, 0, ulBufAddr); // wrong

		MT_Cfg.uoAll = 0;
		MT_Cfg.bits_recc.ubMT_IDX = ubMTidx_dma;
		MT_Cfg.bits_recc.ubQUE_IDX = ubQIdx;
		FlaGlobalTrigger(&MT_Cfg);
		CopyBlockGetFIPCQ(ubMTidx_dma);

		if (gMTMgr.ubMTDoneMsg[ubMTidx_dma - MT_RETRY_START_INDEX].btMTStop) {
			ubReadFail = TRUE;
		}

		FlaAddFreeMTIndex(ubMTidx_cmd);
		FlaAddFreeMTIndex(ubMTidx_dma);

		if (ubReadFail) {
			UartPrintf("\n [ Read Fail] \n");
			UartPrintf( "\n Global Trigger Read!! ubCh=%d ubCE=%d Interrupt Information Register: %x", ubCh, ubCE, M_FIP_GET_INT_INFO(ubCh));
		}


		UartPrintf("\n [ Print out: ] \n");
		U32 *r_ptr;
		r_ptr = (U32 *) ulBufAddr; // 4 byte
		//UartPrintf("\n [ after r_ptr -> Read_buffer , Read_buffer address=%x	] \n", Read_buffer);
		for (int j = 0; j < 1 * 1024; j++) { //多少個4K (因為U32, 1024=4K )

			//dump few data to check when copare fail
			if ( (j % 1024 == 0) || (j % 1024 == 1)  || (j % 1024 == 2) || (j % 1024 == 3)	|| (j % 1024 == 4) ) {
				UartPrintf("\n j=%d , [ r_ptr=%x ]	\n", j, *r_ptr);
			}
			r_ptr++;
		}


	}

	/*COP0 Read*/

	{
		COP0Status_t eCOP0Status;
		U32 ulPCA;


		memset((void *)COP0_Read_buffer, 0x0, BC_16KB); //先清空Read buffer起始後的16K的大小

		LUN = 0, PAGE = 0, CE = 0, CH = 0, PLANE = 0;

		/* PROG & READ */
		ulPCA = M_GET_PCA(LUN, BLOCK, PAGE, CE, CH, PLANE, 0, COP0_PCA_RULE_3);

		COP0ReadSQPara_t ReadSQPara = {{0}};
		BUF_TYPE_t ulBufPara = {0};

		//read each plane
		UartPrintf("\n Read Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);
		M_CLR_COP0_LCA_CMP(); //disable LCA compare


		ulBufPara.A.ulBUF_ADR = COP0_Read_buffer; //read 4K data foreach plane only
		COP0API_FillCOP0ReadSQUserData0Para(COP0_R_DEBUG_UNC, &ReadSQPara);

		ReadSQPara.UserData0.btSLCMode = TRUE;
		ReadSQPara.UserData0.btD1 = TRUE;
		ReadSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;
		ReadSQPara.UserData0.btRUTBps = TRUE;
		ReadSQPara.UserData0.btVBRMPBps = TRUE;
		ReadSQPara.UserData0.TagID = TAG_ID_ALLOCATE;
		//ReadSQPara.UserData0.DataDef &= ~COP0_SEED_EN_BIT;
		//ReadSQPara.UserData0.btUseParamSeed = 1;
		//ReadSQPara.ulSeed.Seed = 0;



		M_FWPCA_PCA_SET(ReadSQPara.ulPCA.ulAll, ulPCA);
		ReadSQPara.BufVld.ubBufType = BUF_TYPE_A;
		ReadSQPara.BufVld.pulBufInfoPtr = &ulBufPara;

		eCOP0Status = COP0API_SendReadSQ(&ReadSQPara, NULL);
		M_FW_ASSERT(ASSERT_BURNER_0x018B, eCOP0Status.btSendCmdSuccess);


		UartPrintf("\n [ COP0_Read_buffer Print out(%x): ] \n", COP0_Read_buffer);

		U32 *r_ptr2;
		r_ptr2 = (U32 *) COP0_Read_buffer; // 4 byte
		//UartPrintf("\n [ after r_ptr -> Read_buffer , Read_buffer address=%x	] \n", Read_buffer);
		for (int j = 0; j < 1 * 1024; j++) { //多少個4K (因為U32, 1024=4K )

			//dump few data to check when copare fail
			if ( (j % 1024 == 0) || (j % 1024 == 1)  || (j % 1024 == 2) || (j % 1024 == 3)	|| (j % 1024 == 4) ) {
				UartPrintf("\n j=%d , [ r_ptr=%x ]	\n", j, *r_ptr2);
			}
			r_ptr2++;
		}
	}
	//UartPrintf( "\n COP0 Read!! ubCh=%d Interrupt Information Register: %x", (CH), M_FIP_GET_INT_INFO(CH));


	U8 cmp_res = memcmp((void *)ulBufAddr, (void *)COP0_Read_buffer, BC_16KB);
	if (cmp_res == 0) {
		UartPrintf("\n [ Compare Pass ! ]\n");

	}
	else {
		UartPrintf("\n [ Compare FAIL ! ]\n");

	}

	UartPrintf("\n [ END of %s~ ] \n", __func__);


}
#endif
void DirectReadSinglePage(U8 Use_TLC, int BLOCK)
{
	UartPrintf("\n [ Enter on %s ] \n", __func__);

	/* READ */
	int LUN = 0,  PAGE = 0, CE = 0, CH = 0, PLANE = 0;
	U32 ulPCA;

	U32 Read_buffer = BURNER_VENDOR_BUF_BASE + BC_16KB;
	memset((void *)Read_buffer, 0x0, BC_16KB);

	if (Use_TLC)	{
		ulPCA = M_GET_PCA(LUN, BLOCK, PAGE, CE, CH, PLANE, 0, COP0_PCA_RULE_0);
		VUC_ReadSinglePage(Read_buffer, ulPCA, 0xE/*NULL*/, gub4kEntrysPerPlane, 4); //read 16K
	}
	else {
		ulPCA = M_GET_PCA(LUN, BLOCK, PAGE, CE, CH, PLANE, 0, COP0_PCA_RULE_3);
		VUC_ReadSinglePage(Read_buffer, ulPCA, 0xF/*NULL*/, gub4kEntrysPerPlane, 0); //read 16K
	}


	//UartPrintf("\n  Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);
	UartPrintf("\n [ Print out: ] \n");
	U32 *r_ptr;
	r_ptr = (U32 *) Read_buffer; // 4 byte
	//UartPrintf("\n [ after r_ptr -> Read_buffer , Read_buffer address=%x	] \n", Read_buffer);
	for (int j = 1; j < 4 * 1024; j++) { //4個4K (因為U32, 1024=4K )

		//dump few data to check when copare fail
		if ( (j % 1024 == 0) || (j % 1024 == 1)  || (j % 1024 == 2) || (j % 1024 == 3)	|| (j % 1024 == 4) ) {
			UartPrintf("\n j=%d , [ r_ptr=%x ]	\n", j, *r_ptr);
		}
		r_ptr++;
	}


	while (TRUE == M_COP0_GET_IDLE()) {
		FWCop0Waiting();
	}
	UartPrintf("\n [ END of %s ] \n", __func__);

}

void DirectRead4K_FROM_PCA(U8 Use_PCA, U32 Input)
{
	/*Use for SLC mode */

	UartPrintf("\n [ Gary Enter on %s with %d ] \n", __func__, Input);


	U32 ulPCA;
	int LUN = 0, BLOCK = 0, PAGE = 0, CE = 0, CH = 0, PLANE = 0;

	if (Use_PCA) {
		ulPCA = Input;
		UartPrintf("\n ulPCA:%d \n",  ulPCA);
	}
	else {
		BLOCK = Input;
		ulPCA = M_GET_PCA(LUN, BLOCK, PAGE, CE, CH, PLANE, 0, COP0_PCA_RULE_3);
		UartPrintf("\n Lun:%d Block:%d Page:%d ce:%d ch%d plane:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, PLANE, ulPCA);
	}

#if 0 // call 
	U32 Read_buffer = BURNER_VENDOR_BUF_BASE + BC_16KB;
	memset((void *)Read_buffer, 0x0, BC_16KB);

	UartPrintf("\n [ Enter VUC_ReadSinglePage] \n");

	VUC_ReadSinglePage(Read_buffer, ulPCA, 0xF/*NULL*/, gub4kEntrysPerPlane, 0); //read 16K

	UartPrintf("\n [ Print out: ] \n");
	U32 *r_ptr;
	r_ptr = (U32 *) Read_buffer; // 4 byte
	//UartPrintf("\n [ after r_ptr -> Read_buffer , Read_buffer address=%x	] \n", Read_buffer);
	for (int j = 1; j < 4 * 1024; j++) { //4個4K (因為U32, 1024=4K )

		//dump few data to check when copare fail
		if ( (j % 1024 == 0) || (j % 1024 == 1)  || (j % 1024 == 2) || (j % 1024 == 3)	|| (j % 1024 == 4) ) {
			UartPrintf("\n j=%d , [ r_ptr=%x ]	\n", j, *r_ptr);
		}
		r_ptr++;
	}
#else

	UartPrintf("\n [ Enter DirectRead] \n");
	U32 Read_buffer = BURNER_VENDOR_BUF_BASE + BC_16KB;
	memset((void *)Read_buffer, 0x0, BC_16KB);

	COP0ReadSQPara_t ReadSQPara = {{0}};
	BUF_TYPE_t ulBufPara = {0};
	COP0Status_t eCOP0Status;


	//read each plane
	for (PLANE = 0; PLANE < gubBurstsPerBank; PLANE++) {

		M_CLR_COP0_LCA_CMP(); //disable LCA compare

		ulBufPara.A.ulBUF_ADR = Read_buffer + (PLANE * BC_4KB ); ; //read 4K data foreach plane only
		COP0API_FillCOP0ReadSQUserData0Para(COP0_R_DEBUG_UNC, &ReadSQPara);

		ReadSQPara.UserData0.btSLCMode = TRUE;
		ReadSQPara.UserData0.btD1 = TRUE;
		ReadSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;
		ReadSQPara.UserData0.btRUTBps = TRUE;
		ReadSQPara.UserData0.btVBRMPBps = TRUE;
		ReadSQPara.UserData0.TagID = TAG_ID_ALLOCATE;
		//ReadSQPara.UserData0.DataDef |= COP0_SEED_EN_BIT;
		//ReadSQPara.ulSeed.Seed = BURNER_RANDOM_SEED;
		M_FIP_VUC_DIRECTED_READ_SOURCE_DATA();


		M_FWPCA_PCA_SET(ReadSQPara.ulPCA.ulAll, ulPCA);
		ReadSQPara.BufVld.ubBufType = BUF_TYPE_A;
		ReadSQPara.BufVld.pulBufInfoPtr = &ulBufPara;

		eCOP0Status = COP0API_SendReadSQ(&ReadSQPara, NULL);
		M_FW_ASSERT(ASSERT_BURNER_0x018C, eCOP0Status.btSendCmdSuccess);

		ulPCA = ulPCA + gub4kEntrysPerPlane ; //increase PCA by 4 ( 4 frame per plane)

	}



	UartPrintf("\n [ Print out: ] \n");
	U32 *r_ptr2;
	r_ptr2 = (U32 *) Read_buffer; // 4 byte
	//UartPrintf("\n [ after r_ptr -> Read_buffer , Read_buffer address=%x	] \n", Read_buffer);
	for (int j = 1; j < 1 * 1024; j++) { //4個4K (因為U32, 1024=4K )

		//dump few data to check when copare fail
		if ( (j % 1024 == 0) || (j % 1024 == 1)  || (j % 1024 == 2) || (j % 1024 == 3)	|| (j % 1024 == 4) ) {
			UartPrintf("\n j=%d , [ r_ptr=%x ]	\n", j, *r_ptr2);
		}
		r_ptr2++;
	}

	while (TRUE == M_COP0_GET_IDLE()) {
		FWCop0Waiting();
	}
#endif

	UartPrintf("\n [ END of %s ] \n", __func__);

}
void RunALLFrameByPCA()
{
	// just for check PCA rule

	int LUN = 0,  BLOCK = 0, PAGE = 0, CE = 0, CH = 0, PLANE = 0, FRAME = 0, LMU = 0;
	U32 ulPCA;

	UartPrintf("\n [ Enter on %s ] \n", __func__);

	for (BLOCK = 0; BLOCK < 2; BLOCK++)
		for (PAGE = 0; PAGE < 20; PAGE++)
			for (CE = 0; CE < gubCENumber; CE++)
				for (CH = 0; CH < gubPlanesPerBurst; CH++)
					for (LMU = 0; LMU < gubLMUNumber; LMU++)
						for (PLANE = 0; PLANE < gubBurstsPerBank; PLANE++) {
							//for (FRAME = 0; FRAME < 4; FRAME++) {
							ulPCA = M_GET_PCA_WITH_LMU(LUN, BLOCK, PAGE, CE, CH, LMU, PLANE, FRAME, COP0_PCA_RULE_0); // TLC use rule 0
							UartPrintf("\n	Lun:%d Block:%d Page:%d ce:%d ch%d LMU:%d plane:%d frame:%d, ulPCA:%d \n", LUN, BLOCK, PAGE, CE, CH, LMU, PLANE, FRAME, ulPCA);
							//}
						}

	UartPrintf("\n [ END on %s ] \n", __func__);
}

#define turnon  1

void VUC_Burner_Test()
{

	UartPrintf("\n\n****** Enter %s, run Burner Test (new version 0530-1822)  ******\n", __func__);


	/* slc test code */

#if turnon
	while (1) {
		int block_num = 14;

		//int block_num = 1400;


		//int page_cnt = 200;



		//VUC_TLC_ERASE_1P(block_num);
		//VUC_TLC_PROG_1P(block_num);
		//VUC_TLC_READ_1P(block_num);
		//VUC_TLC_Erase_Prog_Read_Verify(block_num);
		//VUC_SLC_ERASE_4P(block_num);
		//VUC_SLC_PROC_4P(block_num);
		//VUC_SLC_READ_4P(block_num);


		//VUC_TLC_ERASE_4P(block_num);
		//VUC_TLC_PROG_1P(block_num);
		//VUC_TLC_READ_4P(block_num);

		VUC_TLC_ERASE_4P(block_num);
		VUC_TLC_PROG_4P(block_num);
		VUC_TLC_READ_4P(block_num);

		break;
	}
#endif

	/* Erase Test */
	//	VUC_SLC_ERASE_SINGLE_PLANE(block_num);
	//	VUC_SLC_ERASE_MULTI_PLANE(block_num);


	/* Prog Test */
	//	VUC_SLC_PROC_SINGLE_PLANE(block_num);
	//	VUC_SLC_PROG_MULTI_PLANE(block_num);


	/* Read Test */
	//	VUC_SLC_READ_SINGLE_PLANE(block_num);
	//	VUC_SLC_READ_MULTI_PLANE(block_num);


	//	VUC_SLC_Erase_Prog_Read_Verify(block_num, page_cnt); //可以單跑
	//	VUC_SLC_Erase_Prog_Read_Verify_1P(block_num, 300); //ok


	/* CACHE */
	//	VUC_SLC_PROC_CACHE_1P(block_num, page_cnt);
	//	VUC_SLC_PROC_CACHE_MULTI_PLANE(block_num, page_cnt);
	//	VUC_SLC_READ_CACHE_1P(block_num, page_cnt);
	//	VUC_SLC_READ_CACHE_MULTI_PLANE(block_num, page_cnt);

	/* Global Trigger*/
	//	TestGlobalTriggerErase();
	//	TestGlobalTriggerRead(block_num, COP0_PCA_RULE_3);


	/* SLC Verify*/
	//VUC_SLC_ERASE_1P(block_num);
	//VUC_SLC_PROC_1P(block_num);
	//VUC_SLC_READ_1P(block_num);

	//VUC_SLC_ERASE_4P(block_num);
	//VUC_SLC_PROC_4P(block_num);
	//VUC_SLC_READ_4P(block_num);
	//VUC_SLC_Erase_Prog_Read_Verify(block_num, 10); //可以單跑


	UartPrintf("\n****** Enter Debug waiting loop ****** BURNER_VENDOR_BUF_BASE=%x \n", BURNER_VENDOR_BUF_BASE);
	Burner_debug_wait = turnon;

	while (Burner_debug_wait) {

	}
	//	UartPrintf("\n****** Go next Round ******\n\n\n");


	return;
}
