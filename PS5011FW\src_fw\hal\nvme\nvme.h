#ifndef _NVME_H_
#define	_NVME_H_

#include "nvme_api.h"
#include "env.h"
#include "nvme_api/nvme/shr_hal_nvme.h"

extern void Burner_nvme_send_cq_entry(CTAG_t CTAG, U8 ubStatusCodeType, U8 ubStatusCode, U32 ulCmdSpecific, U8 ubMore, U8 ubDoNotRetry);
static void Burner_nvme_create_cq(VUC_OPT_HCMD_PTR_t pCmd);
static void Burner_nvme_delete_cq(VUC_OPT_HCMD_PTR_t pCmd);
static void Burner_nvme_create_sq();
static void Burner_nvme_delete_sq();
static void Burner_nvme_async_req(VUC_OPT_HCMD_PTR_t pCmd);
void Burner_nvmeHCMD_parser(REGHCMD_PTR pCmd, VUC_OPT_HCMD_PTR_t curCmdSlot);
void Burner_nvme_sync_write(VUC_OPT_HCMD_PTR_t pCmd);
void Burner_nvme_sync_read(VUC_OPT_HCMD_PTR_t pCmd);
void nvme_admIO_setup(VUC_OPT_HCMD_PTR_t pCmd, U32 ulSize);
void Burner_nvme_admIO_trig(VUC_OPT_HCMD_PTR_t pCmd, U32 ulMemAddr, U8 ubDir);
void Burner_nvme_init_var(U8 ubRestTyp);
extern U32 Burner_nvme_htd_handle(void);
extern void Burner_nvme_get_log(VUC_OPT_HCMD_PTR_t pCmd);
extern void BurnerNVMEIdentify(VUC_OPT_HCMD_PTR_t pCmd);
void NVMESectorReceive(VUC_OPT_HCMD_PTR_t pCmd, U8 ubPathCmdIn);
void NVMESectorSend(VUC_OPT_HCMD_PTR_t pCmd, U8 ubPathCmdIn);

#endif /* _NVME_H_ */
