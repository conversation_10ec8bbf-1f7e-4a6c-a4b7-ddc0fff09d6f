#include "hal/pic/uart/uart_api.h"
#include "nvme_api/nvme/shr_hal_nvme.h"
#include "vuc/Vth_Parsing.h"
#include "host/VUC_handler.h"
#include "vuc/VUC_command.h"

#if (BURNER_MODE_EN)
void VUC_NandVerifyRead(VUC_OPT_HCMD_PTR_t pCmd)
{
	if (TRUE == gFlhEnv.ulFlashDefaultType.BitMap.btNoSupport) {
		pCmd->ubState = CMD_ERROR;
		gVUCVar.ubVucCmdErrStatus = HOST_ERROR_FEATURE;
		return;
	}

	U32 ulBuf_addr;
	U8 ubSubfeature = (U8)pCmd->vuc_sqcmd.vendor.NandVerificationRead.ubType;

	switch (ubSubfeature) {
	case 0x00:
		ulBuf_addr = VENDOR_NAND_VERIFICATION_SHADOW_DATA;
		break;
	case 0x01:
		ulBuf_addr = VENDOR_NAND_VERIFICATION_SHADOW_CMD;
		break;
	case 0x02:
		ulBuf_addr = VENDOR_NAND_VERIFICATION_SHADOW_ECC;
		break;
	case 0x03:
		ulBuf_addr = VENDOR_NAND_VERIFICATION_SHADOW_TIMER;
		break;
	case 0x04:
		ulBuf_addr = VENDOR_NAND_VERIFICATION_SHADOW_TIMER;
		break;
	default:
		ulBuf_addr = VENDOR_NAND_VERIFICATION_SHADOW_DATA;

	}

	memcpy((void *)BURNER_VENDOR_BUF_BASE, (void *)ulBuf_addr, DEF_4B * pCmd->vuc_sqcmd.vendor.NandVerificationRead.ulLength);
}
#endif /* BURNER_MODE_EN */
