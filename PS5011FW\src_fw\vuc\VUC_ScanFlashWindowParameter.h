#ifndef _VUC_SCANFLASHWINDOWPARAMETER_H_
#define _VUC_SCANFLASHWINDOWPARAMETER_H_
#include "hal/fip/fip.h"
#include "nvme_api/nvme/shr_hal_nvme.h"

#define VUC_SCAN_FLASH_WINDOW_PARAMETER_ZQ_FAIL_ERROR_CODE		(0xE1)
#define VUC_SCAN_FLASH_WINDOW_BACKDOOR_FOR_3D_RANDOMIZER		(0xFE)
typedef struct {
	union {
		U8 ubAll;
		struct {
			U8 btBitScan		: 1;
			U8 btIsWrite		: 1;
			U8 btScanWindowECCEn		: 1;
			U8 btDQTrainEn	: 1;
			U8 ubRWduty		: 4;
		} A;
	} SubFeature;
	U16 uwECCCorrectBitNum;
	union {
		U32 ulAll;
		struct {
			U16 uwStartOffset;
			U16	uwEndOffset;
		} A;
	} SLBA1;
	union {
		U32 ulAll;
		struct {
			U8 ubNandChannelSelect;
			U8 ubNandBankSelect;
			U8 ubBitScanPos;
			U8 btReferenceWindowResult: 1;
			U8 btReserved: 7;
		} A;
	} SLBA2;
	U8 btScanWindowSetted;

} SdllWindowParameter_t;

enum SCAN_WINDOW_SATE {
	SCAN_WINDOW_INIT,
	SCAN_WINDOW_SET_PARAMETER,
	SCAN_WINDOW_SET_PAD,
	SCAN_WINDOW_READ,
	SCAN_WINDOW_WRITE_PATH,
	SCAN_WINDOW_READ_PATH,
	SCAN_WINDOW_WAIT_READ_FINISH,
	SCAN_WINDOW_FINISH,
	SCAN_WINDOW_ERROR,
	SCAN_WINDOW_DQS_TX_DELAY,
	SCAN_WINDOW_DQS_RX_DELAY,
	SCAN_WINDOW_DQ_TX_DELAY,
	SCAN_WINDOW_DQ_RX_DELAY,
	SCAN_WINDOW_DQ_FINISH
};
enum  SCAN_WINDOW_FINISH_PERCENTAGE {
	SCAN_WINDOW_FIFTEEN_PERCENT		= 15,
	SCAN_WINDOW_THIRTY_PERCENT			= 30,
	SCAN_WINDOW_FORTY_PERCENT			= 40,
	SCAN_WINDOW_FIFTY_PERCENT			= 50,
	SCAN_WINDOW_SIXTY_PERCENT			= 60,
	SCAN_WINDOW_SEVENTY_PERCENT 		= 70,
	SCAN_WINDOW_EIGHTY_FIVE_PERCENT 	= 85,
	SCAN_WINDOW_ONE_HUNDRED_PERCENT	= 100
};

extern SdllWindowParameter_t gScanWindow;
extern ScanWindowParam_t gFipCurWindowParam;
void VUC_ScanFlashWindowParameter(VUC_OPT_HCMD_PTR_t pCmd);

#endif /* _VUC_SCANFLASHWINDOWPARAMETER_H_ */
