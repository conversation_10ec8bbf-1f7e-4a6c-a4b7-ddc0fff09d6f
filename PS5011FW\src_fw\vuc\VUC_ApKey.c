#include "hal/pic/uart/uart_api.h"
#include "host/VUC_handler.h"
#include "nvme_api/nvme/shr_hal_nvme.h"
#include "vuc/VUC_ApKey.h"

void VUC_ApKey(VUC_OPT_HCMD_PTR_t pCmd)
{
	M_UART(VUC_, "\nVUC_AP_KEY");

	if (DEF_APKEY == pCmd->vuc_sqcmd.vendor.usrdef.ulRAM_L) {
		gVUCVar.ubApKeyValid = ENABLE;
	}
	else {
		gVUCVar.ubApKeyValid = DISABLE;
	}

	if (pCmd->vuc_sqcmd.vendor.usrdef.ubSubFeature) {
		gVUCVar.ubVucAPKeyShutdown = 1;
	}

	M_UART(VUC_, "\nAP_KEY = %d ulApkeyValue = %l", gVUCVar.ubApKeyValid, pCmd->vuc_sqcmd.raw_data.dw[13]);
}
