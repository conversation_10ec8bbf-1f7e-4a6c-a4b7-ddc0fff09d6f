#ifndef _VUC_MICRONGETTEMPERATURE_H_
#define _VUC_MICRONGETTEMPERATURE_H_
#include "aom/aom_api.h"

#define VUC_MICRON_GET_TEMPERATURE_COMMAND_CLASS	(0x0002)
#define VUC_MICRON_GET_TEMPERATURE_COMMAND_CODE		(0x0011)
#define VUC_MICRON_TEMPERSTURE_BASE	(37)

#pragma pack(push)
#pragma pack(1)
typedef struct {
	U8 ubResponseHeaderFormatVersion;
	U8 ubResponseDataFotmatVersion;
	U16 uwCMDClass;
	U16 uwCMDCode;
	U16 uwStatus;
	U32 ulDataPayloadSize;
} GetTemperatureResponseHEADER_t;
#pragma pack(pop)

AOM_VUC_3 void VUCMicronGetTemperatureSensor(U32 ulPayloadAddr);

#endif /* _VUC_MICRONGETTEMPERATURE_H_ */
