#ifndef _VT_SWEEP_H_
#define _VT_SWEEP_H_
/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "aom/aom_api.h"

#if (VT_SWEEP_EN && PS5017_EN)
/*
 * ---------------------------------------------------------------------------------------------------
 *   definitions
 * ---------------------------------------------------------------------------------------------------
 */
#define VTSWEEP_RANGE								(0x100)
#define VTSWEEP_TRIM_CLEAR							(0x00)
#define VTSWEEP_TRIM_352H							(0x0352)
#define VTSWEEP_STOP_SCAN_THR						(5)

#define VTSWEEP_DUMMY_FSA							(0xFEEDFEED)

#define VTSWEEP_FLAG_BK_NUM							0//(3)
#define VTSWEEP_SLC_BK_NUM							0//(8)
#define VTSWEEP_MLC_BK_NUM							0//(8)
#define VTSWEEP_QLC_BK_NUM							(32)

#define VTSWEEP_BK_NUM								(32)
#define VTSWEEP_TRIM_LP_BK_NUM						(2)
#define VTSWEEP_TRIM_LP_QLC_EXT_BK_NUM				(6)
#define VTSWEEP_TOTAL_BK_NUM						(VTSWEEP_BK_NUM + VTSWEEP_TRIM_LP_BK_NUM + VTSWEEP_TRIM_LP_QLC_EXT_BK_NUM)

#define VTSWEEP_STRUCT_SIZE							(32 * 34) //1088B, use ATCM DMAC need to align 32B


#define VUC_MICRON_GET_VT_SWEEP_DATA_PAYLOAD_OFFSET		(12)
#define VUC_MICRON_GET_VT_SWEEP_HISTOGRAM_DATA_OFFSET	(VUC_MICRON_GET_VT_SWEEP_DATA_PAYLOAD_OFFSET + 16)

#define VUC_MICRON_GET_VT_SWEEP_VT_STEP_RANGE			(0x100)

#define VUC_MICRON_GET_VT_SWEEP_RESPONSE_HEADER_FORMAT_VERSION	(0x00)
#define VUC_MICRON_GET_VT_SWEEP_RESPONSE_DATA_FORMAT_VERSION	(0x02)
#define VUC_MICRON_GET_VT_SWEEP_RESPONSE_COMMAND_CLASS			(0x0004)
#define VUC_MICRON_GET_VT_SWEEP_RESPONSE_COMMAND_CODE			(0x002A)
#define VUC_MICRON_GET_VT_SWEEP_RESPONSE_DATA_PAYLOAD_SIZE		(0x1010)

typedef union VtSweepDataPayload VT_SWEEP_PAYLOAD_T, *VT_SWEEP_PAYLOAD_PTR;
union VtSweepDataPayload {
	struct {
		U16 uwCH;
		U16 uwCE;
		U16 uwLUN;
		U16 uwPhysicalBlock;
		U16 uwPhysicalPage;
		U16 uwVtStopValue;
		U16 uwVtStepSize;
		U16 uwRFU;
	} InputDataPayload;
	struct {
		U16 uwCH;
		U16 uwCE;
		U16 uwLUN;
		U16 uwPhysicalBlock;
		U16 uwPhysicalPage;
		U8 ubDieTemperature;
		U8 ubReserve;
		U32 ulPOH;
	} ResponseDataPayload;
};


typedef enum VtSweepOperationModeEnum {
	DEFAULT_MODE,
	MANUAL_MODE,
} VtSweepOperationModeEnum_t;

/*
 * ---------------------------------------------------------------------------------------------------
 *  data types
 * ---------------------------------------------------------------------------------------------------
 */
typedef struct {
	U32 ulHistogramData[VTSWEEP_RANGE];		//1024 byte
	U8 ubBackUpMLBi[VTSWEEP_TOTAL_BK_NUM];	//1052 byte (+28)
	U8 ubValidFlag;
	U8 ubChannel;
	U8 ubBank;
	U8 ubDie;								//1056 byte
	U16 uwPhysicalBlock;
	U16 uwPhysicalSrcPage;					//1060 byte
	U16 uwPhysicalReadPage;					//1062 byte
	U8 ubALUSelect;							//1063 byte
	U8 ubWordLineType;						//1064 byte
	U8 ubMLBiVerifyFail;					//1065 byte
	U8 rsv[VTSWEEP_STRUCT_SIZE - 1065];
} MicronFrey2VtSweep_t;



typedef struct {
	U8 ubResponseHeaderFormatVersion;
	U8 ubResponseDataFotmatVersion;
	U16 uwCMDClass;
	U16 uwCMDCode;
	U16 uwReserve;
	U32 ulDataPayloadSize;
} GetVtSweepResponseHEADER_t;

/*
 * ---------------------------------------------------------------------------------------------------
 *   extern global variables
 * ---------------------------------------------------------------------------------------------------
 */
extern MicronFrey2VtSweep_t *gpFrey2VtSweep;
extern U8 gubAddrHit;
extern VT_SWEEP_PAYLOAD_T *gpVtSweepPayLoad;

/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */
AOM_VUC_5 U16 VtSweepSetMLBiFPU(U16 uwTrimReg, U8 ubDie, U8 ubInputData);
AOM_VUC_5 U16 VtSweepCheckMLBiFPU(U8 ubCompareMLBiData);
AOM_VUC_5 U16 VtSweepGetMLBiFPU(U16 uwTrimReg, U8 ubDie);
AOM_VUC_5 void VtSweepIssueMTData(U8 ubQIndex, U8 ubMTIndex, U8 ubDie, U8 ubALU, U32 ulErrorFSA);
AOM_VUC_5 void VtSweepTrimDMAMT(U8 ubQIndex, U8 ubMTIndex, U8 ubDie, U32 ulBufAddr);
AOM_VUC_5 void VtSweepSetTrimRegister(U8 ubChannel, U8 ubCE, U8 ubDieIdx, U8 ubInputData, U16 uwTrimReg);
AOM_VUC_5 void VtSweepReadData(U8 ubChannel, U8 ubCE, U8 ubDieIdx, U8 ubALUSelect, U32 ulErrorFSA);
AOM_VUC_5 void VtSweepGetTrimRegister(U8 ubChannel, U8 ubCE, U8 ubDieIdx, U16 uwTrimReg);
AOM_VUC_5 void MicronVtSweep(U8 ubALUSelect, U32 ulErrorFSA, U8 ubOperationMode);
AOM_VUC_5 U16 FTLInvertCoord( U8 ubX, U16 uwY);

#endif /* VT_SWEEP_EN */
#endif /* _VT_SWEEP_H_ */
