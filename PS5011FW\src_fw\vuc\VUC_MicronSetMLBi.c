#include "VUC_MicronResponse.h"
#include "hal/dmac/dmac_pop_cmd.h"
#include "VUC_MicronSetMLBi.h"
#include "vuc/VUC_api.h"

#if (VUC_MICRON_NAND_VS_COMMANDS_EN)
void VUCMicronSetMLBi(U32 ulInputPayloadAddr, U32 ulPayloadAddr)
{
	SetMLBiInputData_t *pInputData;
	SetMLBiResponseHeader_t *pResponseHeader;
	DMACParam_t DMACParam;
	pResponseHeader = (SetMLBiResponseHeader_t *)ulPayloadAddr;
	pInputData = (SetMLBiInputData_t *)(ulInputPayloadAddr + VUC_MICRON_SET_MLBI_HEADER_LENGTH);

	DMACParam.DMACSetValue.ulDestAddr = ulPayloadAddr;
	DMACParam.DMACSetValue.ulLowValue = 0;
	DMACParam.DMACSetValue.ulHighValue = 0;
	DMACParam.DMACSetValue.ul32ByteNum = SIZE_IN_32B(DEF_16B);
	DMACParam.DMACSetValue.btValidate = FALSE;
	gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
	DMACSetValue(&DMACParam, gulDMACDirectWaitDone_Callback, 0);
	while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
		DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
	}
	pResponseHeader->ubResponseHeaderFormatVersion = VUC_MICRON_RESPONSE_HEADER_FORMAT_VERSION_0;
	pResponseHeader->ubResponseDataFotmatVersion = VUC_MICRON_RESPONSE_FORMAT_BIN;
	pResponseHeader->uwCMDClass = VUC_MICRON_NAND_VS_COMMANDS;
	pResponseHeader->uwCMDCode = VUC_MICRON_SET_MLBI;
	pResponseHeader->uwStatus = 0;
	pResponseHeader->ulDataPayloadSize = VUC_MICRON_SET_MLBI_PAYLOAD_LENGTH;
	VUCMicronFlaSetMLBi((U8)pInputData->uwCH, (U8)pInputData->uwCE, (U8)pInputData->uwLUN, pInputData->uwTrimRegAddr, pInputData->uwTrimRegData);
	UartPrintf("\n SetMLBi %x CH:%x CE:%x LUN:%x data:%x ", pInputData->uwTrimRegAddr, pInputData->uwCH, pInputData->uwCE, pInputData->uwLUN, pInputData->uwTrimRegData);
}
#endif /* VUC_MICRON_NAND_VS_COMMANDS_EN */
