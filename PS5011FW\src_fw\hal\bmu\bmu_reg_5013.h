#ifndef _E13_BMU_REG_H_
#define _E13_BMU_REG_H_
#include "mem.h"
//*****************************************
//BMU register
//*****************************************
//--------------------5011 BMU wrapper IP Register Define--------------------
#define BMU_REG_BASE										(BMU_REG_ADDRESS)
#define BMU_E3D512_REG_BASE										(E3D512_REG_ADDRESS)

#define BMU_E3D512_MAX_NUM   (32)

#define R8_BMU												((volatile U8 *)  BMU_REG_BASE)
#define R16_BMU												((volatile U16 *) BMU_REG_BASE)
#define R32_BMU												((volatile U32 *) BMU_REG_BASE)
#define R64_BMU												((volatile U64 *) BMU_REG_BASE)

#define	R16_BMU_E3D512											( (volatile U16 *)BMU_E3D512_REG_BASE)
#define	R32_BMU_E3D512											( (volatile U32 *)BMU_E3D512_REG_BASE)

#define	R32_BMU_E3D512_VALID_OFFSET		(0)
#define	R16_BMU_E3D512_PB_OFFSET		(0x10 >> 1)

#define BMU_PBNA_RAM_BASE									(0x002E4000)
#define BMU_PBNA_RAM_SIZE									(0x1C80)		// 7.125KB
#define BMU_LBNA_RAM_BASE									(0x002E8000)
#define BMU_LBNA_RAM_SIZE									(0x4A00)		// 19KB


//--------------------5011 BMU Register Macro--------------------
#define R32_BMU_CMD_SQ_WRR_WEIGHT							(0x0000 >> 2)
#define 	CMD_SQ_WRR_WEIGHT_SHIFT							(0)
#define 	CMD_SQ_WRR_WEIGHT_MASK							(BIT_MASK(4))

#define R32_BMU_TIMEOUT_RESOLUTION							(0x0008 >> 2)
#define 	TIMEOUT_RESOLUTION_SHIFT							(0)
#define 	TIMEOUT_RESOLUTION_MASK							(BIT_MASK(3))

//Offset: 010h + N*1h, N=0~14
#define R32_BMU_MR_Q_ID_N									(0x0010 >> 2)
#define R8_BMU_MR_Q_ID_N									(0x0010)
#define 	MR_Q_ID_N_SHIFT									(0)
#define 	MR_Q_ID_N_MASK									(BIT_MASK(8))

//Offset: 080h + N*4h, N=0~7
#define R32_BMU_LB_N_HEAD_TAIL								(0x0080 >> 2)
#define 	LB_HEAD_PTR_N_SHIFT								(0)
#define 	LB_HEAD_PTR_N_MASK								(BIT_MASK(11))
#define 	LB_TAIL_PTR_N_SHIFT								(16)
#define 	LB_TAIL_PTR_N_MASK								(BIT_MASK(11))
#define 	LB_OFFSET_RING_BIT								(BIT10)
#define 	LB_OFFSET_MASK								(BIT_MASK(10))

//Offset: 0A0h + N*2h, N=0~7
#define R32_BMU_LOWER_LIMIT_N								(0x00A0 >> 2)
#define R16_BMU_LOWER_LIMIT_N								(0x00A0 >> 1)
#define 	LOWER_LIMIT_N_SHIFT								(0)
#define 	LOWER_LIMIT_N_MASK								(BIT_MASK(9))

//Offset: 0B0h + N*2h, N=0~7
#define R32_BMU_UPPER_LIMIT_N								(0x00B0 >> 2)
#define R16_BMU_UPPER_LIMIT_N								(0x00B0 >> 1)
#define 	UPPER_LIMIT_N_SHIFT								(0)
#define 	UPPER_LIMIT_N_MASK								(BIT_MASK(9))

//Offset: 0C0h + N*2h, N=0~7
#define R32_BMU_LB_QUOTA_CNT_N								(0x00C0 >> 2)
#define R16_BMU_LB_QUOTA_CNT_N								(0x00C0 >> 1)
#define 	LB_QUOTA_CNT_N_SHIFT								(0)
#define 	LB_QUOTA_CNT_N_MASK								(BIT_MASK(9))

#define R32_BMU_LL_FREE_CNT									(0x00D0 >> 2)
#define 	LL_FREE_CNT_00D0_SHIFT								(0)
#define 	LL_FREE_CNT_00D0_MASK								(BIT_MASK(9))

#define R32_BMU_REPLENISH_STATUS								(0x00E0 >> 2)
#define 	POOL_FREE_CNT_SHIFT								(0)
#define 	POOL_FREE_CNT_MASK								(BIT_MASK(9))
#define 	LL_FREE_CNT_00E0_SHIFT								(9)
#define 	LL_FREE_CNT_00E0_MASK								(BIT_MASK(9))
#define 	LL_OUT_CNT_SHIFT									(18)
#define 	LL_OUT_CNT_MASK									(BIT_MASK(3))
#define 	WAIT_REPLENISH_SHIFT								(24)
#define 	WAIT_REPLENISH_MASK								(BIT_MASK(1))
#define 	POOL_INSUFFICIENT_SHIFT							(25)
#define 	POOL_INSUFFICIENT_MASK							(BIT_MASK(1))

#define R32_BMU_STATIC_EVENT_EN								(0x0200 >> 2)
#define 	STATIC_EVENT_VALID_BITMAP_SHIFT						(0)
#define 	STATIC_EVENT_VALID_BITMAP_MASK						(BIT_MASK(8))
#define 	STATIC_EVENT_FULL_SHIFT							(8)
#define 	STATIC_EVENT_FULL_MASK							(BIT_MASK(8))
#define 	STATIC_EVENT_E3D4K_SHIFT							(16)
#define 	STATIC_EVENT_E3D4K_MASK							(BIT_MASK(8))

//Offset = 204h~207h, [4*N+3:4*N]: static_event_target_id[N], N=0~7
#define R32_BMU_STATIC_EVENT_TARGET_ID_N						(0x0204 >> 2)
#define R8_BMU_STATIC_EVENT_TARGET_ID_N						(0x0204)
#define 	STATIC_EVENT_TARGET_ID_N_SHIFT_L4B					(0) //for N=0,2,4,6
#define 	STATIC_EVENT_TARGET_ID_N_SHIFT_H4B					(4) //for N=1,3,5,7
#define 	STATIC_EVENT_TARGET_ID_N_MASK						(BIT_MASK(4))

#define R32_BMU_STATIC_EVENT_XZIP								(0x0208 >> 2)
#define 	XZIP_LB_SHIFT									(0)
#define 	XZIP_LB_MASK									(BIT_MASK(8))

//Offset = 210h+N*2h, N=0~7
#define R32_BMU_EVENT_PTR_N									(0x0210 >> 2)
#define 	EVENT_PTR_N_SHIFT								(0)
#define 	EVENT_PTR_N_MASK								(BIT_MASK(11))

#define R32_BMU_STATIC_EVENT_STATUS							(0x0220 >> 2)
#define 	WAIT_MONITOR_SHIFT								(0)
#define 	WAIT_MONITOR_MASK								(BIT_MASK(8))
#define 	WAIT_RESERVED_SHIFT								(8)
#define 	WAIT_RESERVED_MASK								(BIT_MASK(8))


#define R32_BMU_SPECIFY_LB_ID								(0x0280 >> 2)
#define 	RLB_ID_SHIFT									(8)
#define 	RLB_ID_MASK									(BIT_MASK(3))

#define R32_BMU_IN_ORDER_FREE								(0x0284 >> 2)
#define 	WLB_IN_ORDER_FREE_EN_SHIFT							(0)
#define 	WLB_IN_ORDER_FREE_EN_MASK							(BIT_MASK(8))

#define R32_BMU_APU_FREE_CNT_EVENT							(0x0288 >> 2)
#define 	FREE_CNT_EVENT_TARGET_ID_SHIFT						(0)
#define 	FREE_CNT_EVENT_TARGET_ID_MASK						(BIT_MASK(4))

#define R32_BMU_APU_FREE_CNT								(0x028C >> 2)
#define 	APU_FREE_CNT_SHIFT								(0)
#define 	APU_FREE_CNT_MASK								(BIT_MASK(11))

#define R32_BMU_POOL_SIZE									(0x0300 >> 2)
#define 	POOL_SIZE_SHIFT									(0)
#define 	POOL_SIZE_MASK									(BIT_MASK(9))

#define R32_BMU_LB_DOUBLE_FREE								(0x0304 >> 2)
#define 	LB_DF_EN_SHIFT									(0)
#define 	LB_DF_EN_MASK									(BIT_MASK(8))

#define R32_BMU_RAM_FREE_CNT								(0x0310 >> 2)
#define 	RAM_FREE_CNT_SHIFT								(0)
#define 	RAM_FREE_CNT_MASK								(BIT_MASK(9))

#define R32_BMU_PREFETCH_Q_CNT								(0x0314 >> 2)
#define 	PREFETCH_Q_CNT_SHIFT								(0)
#define 	PREFETCH_Q_CNT_MASK								(BIT_MASK(3))

#define R32_BMU_POOL_FREE_CNT								(0x0318 >> 2)
#define 	POOL_FREE_CNT_SHIFT								(0)
#define 	POOL_FREE_CNT_MASK								(BIT_MASK(9))

#define R32_BMU_E3D512_RSC_CNT								(0x031C >> 2)
#define 	E3D512_RSC_CNT_SHIFT								(0)
#define 	E3D512_RSC_CNT_MASK								(BIT_MASK(6))

#define R32_BMU_DYNAMIC_CNT								(0x0320 >> 2)
#define 	DYNAMIC_CNT_SHIFT								(0)
#define 	DYNAMIC_CNT_MASK								(BIT_MASK(5))

#define R32_BMU_CONTROL									(0x0400 >> 2)
#define 	POOL_EN_SHIFT									(0)
#define 	POOL_EN_MASK									(BIT_MASK(1))
#define 	LOWER_LIMIT_EN_SHIFT								(8)
#define 	LOWER_LIMITL_EN_MASK								(BIT_MASK(1))
#define 	APU_FREE_CNT_EN_SHIFT								(16)
#define 	APU_FREE_CNT_EN_MASK								(BIT_MASK(1))

#define R32_BMU_INIT_STATUS									(0x0404 >> 2)
#define 	LBNA_INIT_DONE_SHIFT								(0)
#define 	LBNA_INIT_DONE_MASK								(BIT_MASK(1))
#define 	PBNA_INIT_DONE_SHIFT								(8)
#define 	PBNA_INIT_DONE_MASK								(BIT_MASK(1))

#define R32_BMU_ERROR_STATUS								(0x0410 >> 2)
#define 	LB_ERROR_STALL_SHIFT								(0)
#define 	LB_ERROR_STALL_MASK								(BIT_MASK(1))
#define 	PB_ERROR_STALL_SHIFT								(8)
#define 	PB_ERROR_STALL_MASK								(BIT_MASK(1))
#define 	WLBS_ERROR_STALL_SHIFT							(16)
#define 	WLBS_ERROR_STALL_MASK							(BIT_MASK(1))
#define 	LBNA_PARITY_ERROR_SHIFT							(24)
#define 	LBNA_PARITY_ERROR_MASK							(BIT_MASK(1))
#define 	PBNA_PARITY_ERROR_SHIFT							(25)
#define 	PBNA_PARITY_ERROR_MASK							(BIT_MASK(1))

#define R32_BMU_CMD_HALT									(0x0414 >> 2)
#define 	CMD_SQ_IN_HALT_SHIFT								(0)
#define 	CMD_SQ_IN_HALT_MASK								(BIT_MASK(13))
#define 	CMD_SQ_OUT_HALT_SHIFT							(16)
#define 	CMD_SQ_OUT_HALT_MASK							(BIT_MASK(1))
#define 	CMD_CQ_HALT_SHIFT								(24)
#define 	CMD_CQ_HALT_MASK								(BIT_MASK(1))

#define R32_BMU_LB_PB_HALT									(0x0418 >> 2)
#define 	LB_SQ_HALT_SHIFT								(0)
#define 	LB_SQ_HALT_MASK									(BIT_MASK(1))
#define 	LB_CQ_HALT_SHIFT								(1)
#define 	LB_CQ_HALT_MASK									(BIT_MASK(1))
#define 	HEAD_FSM_HALT_SHIFT								(2)
#define 	HEAD_FSM_HALT_MASK								(BIT_MASK(1))
#define 	PB_CTRL_HALT_SHIFT								(8)
#define 	PB_CTRL_HALT_MASK								(BIT_MASK(1))
#define 	PB_RSC_HALT_SHIFT								(9)
#define 	PB_RSC_HALT_MASK								(BIT_MASK(1))
#define 	EVENT_HANDLER_HALT_SHIFT							(16)
#define 	EVENT_HANDLER_HALT_MASK							(BIT_MASK(1))
#define 	WLBS_HALT_SHIFT									(24)
#define 	WLBS_HALT_MASK									(BIT_MASK(1))

#define R32_BMU_IDLE										(0x041C >> 2)
#define 	BMU_IDLE_SHIFT									(0)
#define 	BMU_IDLE_MASK									(BIT_MASK(1))
#define     BMU_IDLE                                        (BMU_IDLE_MASK << BMU_IDLE_SHIFT)
#define 	CMD_SQ_IDLE_SHIFT								(8)
#define 	CMD_SQ_IDLE_MASK								(BIT_MASK(1))
#define 	WLBS_IDLE_SHIFT									(9)
#define 	WLBS_IDLE_MASK									(BIT_MASK(1))
#define 	LB_SQ_PROC_IDLE_SHIFT								(10)
#define 	LB_SQ_PROC_IDLE_MASK								(BIT_MASK(1))
#define 	PB_CTRL_IDLE_SHIFT								(11)
#define 	PB_CTRL_IDLE_MASK								(BIT_MASK(1))
#define 	EVENT_HANDLER_IDLE_SHIFT							(12)
#define 	EVENT_HANDLER_IDLE_MASK							(BIT_MASK(1))
#define 	PB_MON_IDLE_SHIFT								(13)
#define 	PB_MON_IDLE_MASK								(BIT_MASK(1))
#define 	LB_CQ_PROC_IDLE_SHIFT								(14)
#define 	LB_CQ_PROC_IDLE_MASK								(BIT_MASK(1))
#define 	HEAD_FSM_IDLE_SHIFT								(15)
#define 	HEAD_FSM_IDLE_MASK								(BIT_MASK(1))
#define 	LB_CQ_IDLE_SHIFT									(16)
#define 	LB_CQ_IDLE_MASK									(BIT_MASK(1))
#define 	CMD_CQ_IDLE_SHIFT								(17)
#define 	CMD_CQ_IDLE_MASK								(BIT_MASK(1))

#define R32_BMU_BACKUP									(0x0420 >> 2)
#define 	BACKUP_REQ_SHIFT								(0)
#define 	BACKUP_REQ_MASK								(BIT_MASK(1))
#define 	BACKUP_RSC_RDY_SHIFT								(1)
#define 	BACKUP_RSC_RDY_MASK								(BIT_MASK(1))

#define R32_BMU_RESTORE									(0x0424 >> 2)
#define 	RESTORE_MANUAL_SET_SHIFT							(0)
#define 	RESTORE_MANUAL_SET_MASK							(BIT_MASK(1))

#define R32_BMU_RRC_CTRL									(0x0440 >> 2)
#define 	LBNA_RRC_LS_ENABLE_SHIFT							(0)
#define 	LBNA_RRC_LS_ENABLE_MASK							(BIT_MASK(1))
#define 	PBNA_RRC_LS_ENABLE_SHIFT							(1)
#define 	PBNA_RRC_LS_ENABLE_MASK							(BIT_MASK(1))
#define 	LBNA_RRC_ERROR_INJECT_SHIFT						(8)
#define 	LBNA_RRC_ERROR_INJECT_MASK							(BIT_MASK(1))
#define 	PBNA_RRC_ERROR_INJECT_SHIFT						(9)
#define 	PBNA_RRC_ERROR_INJECT_MASK						(BIT_MASK(1))

#define R32_BMU_LBNA_RRC_INFO								(0x0450 >> 2)
#define 	LBNA_RRC_INFO_SHIFT								(0)
#define 	LBNA_RRC_INFO_MASK								(BIT_MASK(13))
#define 	LBNA_RRC_ERROR_MASTER_SHIFT						(16)
#define 	LBNA_RRC_ERROR_MASTER_MASK						(BIT_MASK(9))

#define R32_BMU_PBNA_RRC_INFO								(0x0454 >> 2)
#define 	PBNA_RRC_INFO_SHIFT								(0)
#define 	PBNA_RRC_INFO_MASK								(BIT_MASK(11))
#define 	PBNA_RRC_ERROR_MASTER_SHIFT						(16)
#define 	PBNA_RRC_ERROR_MASTER_MASK						(BIT_MASK(5))

#define R32_BMU_CMD_SQ_DEBUG                     (0x610 >> 2)
#define  CMD_SQ_DEBUG_APU_WRITE_BLOCKING_QUEUE_EMPTY (BIT(5))

#define	R16_BMU_DEBUG_REGISTER_1		(0x640 >> 1)
#define	BMU_LBSQ_IDLE					(0x0001)

#define	R32_BMU_DEBUG_REGISTER_2		(0x658 >> 2)
#define	BMU_OUT_CNT_SHIFT			(29)
#define	BMU_OUT_CNT_MASK			(BIT_MASK(2))

#endif /*_E13_BMU_REG_H_*/
