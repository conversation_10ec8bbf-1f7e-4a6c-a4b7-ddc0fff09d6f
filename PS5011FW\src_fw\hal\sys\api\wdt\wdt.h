#ifndef _WDT_H_
#define _WDT_H_

#include "hal/sys/reg/sys_pd1_reg.h"

#define M_WDT_CLEAR_TIMEOUT_FLAG()                  (R32_SYS1_WDT[R32_SYS1_WDT_CTRL] |= WDT_TO_F_BIT)                                    //  Write any value to SYS1L_WDT_CTRL[16:8] will clear timeout flag
#define M_WDT_GET_TIMEOUT_FLAG()                    (R32_SYS1_WDT[R32_SYS1_WDT_CTRL] & WDT_TO_F_BIT)                                    //  Write any value to SYS1L_WDT_CTRL[16:8] will clear timeout flag
#define M_WDT_GET_TIMEOUT_LIMIT()          			(R32_SYS1_WDT[R32_SYS1_WDT_TO_LMT])
#define M_WDT_SET_TIMEOUT_LIMIT(X)                  (R32_SYS1_WDT[R32_SYS1_WDT_TO_LMT] = (X))
#define M_WDT_RESET_EN(X)							(R32_SYS1_WDT[R32_SYS1_WDT_CTRL] |= (X))

#endif /* _WDT_H_ */
