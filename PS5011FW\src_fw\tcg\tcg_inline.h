/****************************************************************************
*
*  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
*  All rights reserved
*
*  The content of this document is confidential and shall be applied
*  subject to the terms and conditions of the license agreement and
*  other applicable laws. Any unauthorized access, use or disclosure
*  of this document is strictly prohibited and may be punishable
*  under laws.
*
*  tcg_inline.h
*
*
*
****************************************************************************/

#ifndef TCG_INLINE_H_
#define TCG_INLINE_H_

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "tcg.h"
#include "nvme_api/nvme/shr_hal_nvme_api.h"
#include "hal/sata/sata_api.h"
/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */
#if (TCG_EN && (PS5013_EN || PS5021_EN || (PS5017_EN && !BURNER_MODE_EN)) && !RDT_MODE_EN && !LPM3_LOADER && !BOOTLOADER_MODE_EN)
INLINE void TcgClearTcgVTKey(void)
{
	memset(gTcg.pVT->KekRange, 0x00, sizeof(gTcg.pVT->KekRange));
	memset(gTcg.pVT->Dek, 0x00, sizeof(gTcg.pVT->Dek));
	memset(&gTcg.pVT->D2HDek, 0x00, sizeof(gTcg.pVT->D2HDek));
}

INLINE void TcgSetOpalTableEventInline(U32 ulEvent, U32 ulTargetFlashIdx, U32 ulTargetAddr)
{
	if (gTcg.BufManager.ulCmdEvent == ulEvent
		&& gTcg.BufManager.ulCmdTargetFlashIdx == ulTargetFlashIdx
		&& gTcg.BufManager.ulCmdTargetAddr == ulTargetAddr) {
		return;
	}
	else {
		gTcg.BufManager.ulCmdEvent = ulEvent;
		gTcg.BufManager.ulCmdTargetFlashIdx = ulTargetFlashIdx;
		gTcg.BufManager.ulCmdTargetAddr = ulTargetAddr;

		if (OpalTableState_Unload == ulEvent) {
			//Because there is only 4 K for OPAL Buffer, Unload is nothing to do.
			if ((TcgFlashTableInvalidIndex == gTcg.BufManager.ulBufFlashIdx)
				&& (OPAL_NULL_TARGET_ADDR == ulTargetAddr)) {
				M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, 0);
			}
			return;
		}
		else if ((OpalTableState_Load == ulEvent)
			&& (OPAL_NULL_TARGET_ADDR == ulTargetAddr)
			&& (gTcg.BufManager.ulBufFlashIdx == ulTargetFlashIdx)) {
			if ((TRUE == TCG_OBJECT_TABLE_SECURITY_KEY_PROTECT)
				&& (SECURITY_AES_OFFLINE_ENCRYPT == gTcg.BufManager.ubFlags.btAESDirection)
				&& (FALSE == M_TCG_CHECK_INIT_REASON(TCG_INIT_REASON_BURNER_PREFORMAT | TCG_INIT_REASON_DLMC_PREFORMAT))) {
				// Need decrypt if last time encrypt the same index
				gTcg.BufManager.ubFlags.btAESDirection = SECURITY_AES_OFFLINE_DECRYPT;
				gTcg.BufManager.OpalTableState = OpalTableState_AES;
				gTcg.BufManager.ulSecurityAddr = gTcg.BufManager.ulBufAddr;
			}
			return;
		}
		else if ((OpalTableState_Save == ulEvent)
			&& (TcgFlashTableInvalidIndex == gTcg.BufManager.ulBufFlashIdx)
			&& (OPAL_NULL_TARGET_ADDR == ulTargetAddr)) {
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, 0);
		}
		else if ((OpalTableState_Save == ulEvent)
			&& (ulTargetFlashIdx >= TcgFlashTableOriginAdmin1Index)) {
			if (FALSE == gTcg.BufManager.ubFlags.btRepairTableDoing) {
				U32 ulTargetOriginalFlashIndex = M_TCG_GET_SP_TABLE_ORIGINAL_IDX(ulTargetFlashIdx);
				M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, ulTargetOriginalFlashIndex <= TCG_SP_TABLE_SYNC_BIT_MAX_IDX);
				gpVT->OPAL.uwTcgTableSyncBMP &= ~(BIT(ulTargetOriginalFlashIndex));
			}
		}
		gTcg.BufManager.OpalTableState = ulEvent;
	}
}
#endif /* (TCG_EN && (PS5013_EN || PS5021_EN || (PS5017_EN && !BURNER_MODE_EN)) && !RDT_MODE_EN && !LPM3_LOADER && !BOOTLOADER_MODE_EN) */

#if (TCG_EN && !BURNER_MODE_EN && !RDT_MODE_EN && !LPM3_LOADER && !BOOTLOADER_MODE_EN)
INLINE void TcgAddInitDoneDrivelog(void)
{
	if (DRIVE_LOG_EN) {
		U8 uwInitReason = 0;
		uwInitReason = ((((FALSE == gpVT->OPAL.btInitDone) && (FALSE == gubEnterFTLTask)) ? BIT0 : 0) /* Power On Init */
				| (((FALSE == gpVT->OPAL.btInitDone) && (TRUE == gubEnterFTLTask)) ? BIT1 : 0)); /* 1st cmd Init */
		TcgAddDrivelog(TcgInitDoneLog, uwInitReason, TCG_DRIVELOG_NO_PAYLOAD);
	}
}

INLINE void TcgAddTimeoutDrivelog(void)
{
	if (DRIVE_LOG_EN) {
		TcgAddDrivelog(TcgTimeoutLog, gTcg.uwLastMethodID, TCG_DRIVELOG_NO_PAYLOAD);
	}
}

INLINE void TcgAddEventDrivelog(U16 uwEventID, U8 ubPayload)
{
	if (DRIVE_LOG_EN) {
		TcgAddDrivelog(TcgEventLog, uwEventID, ubPayload);
	}
}

INLINE void TcgAddErrorDrivelog(U16 uwEventID)
{
	if (DRIVE_LOG_EN) {
		TcgAddDrivelog(TcgErrorLog, uwEventID, TCG_DRIVELOG_NO_PAYLOAD);
	}
}
#else /* (TCG_EN && !BURNER_MODE_EN && !RDT_MODE_EN && !LPM3_LOADER && !BOOTLOADER_MODE_EN) */
#define TcgAddInitDoneDrivelog()
#define TcgAddTimeoutDrivelog()
#define TcgAddEventDrivelog(...)
#define TcgAddErrorDrivelog(...)
#endif /* (TCG_EN && !BURNER_MODE_EN && !RDT_MODE_EN && !LPM3_LOADER && !BOOTLOADER_MODE_EN) */

#if (DEBUG_UART_TCG_METHOD)
INLINE void TcgMethodUart(U16 uwLogCode)
{
	M_UART(TCG_, "\nOM-%b%b", (((uwLogCode) >> 8) & BIT_MASK(8)), ((uwLogCode) & BIT_MASK(8)));
}
#else /* (DEBUG_UART_TCG_METHOD) */
#define TcgMethodUart(...)
#endif /* (DEBUG_UART_TCG_METHOD) */

#if (DEBUG_UART_TCG_INFO)
INLINE void TcgInfoUart(U16 uwLogCode)
{
	M_UART(TCG_, "\nOI-%b%b", (((uwLogCode) >> 8) & BIT_MASK(8)), ((uwLogCode) & BIT_MASK(8)));
}
#else /* (DEBUG_UART_TCG_INFO) */
#define TcgInfoUart(...)
#endif /* (DEBUG_UART_TCG_INFO) */

#if (DEBUG_UART_TCG_ERROR)
INLINE void TcgErrorUart(U16 uwLogCode)
{
	M_UART(TCG_, "\nOE-%b%b", (((uwLogCode) >> 8) & BIT_MASK(8)), ((uwLogCode) & BIT_MASK(8)));
}
#else /* (DEBUG_UART_TCG_ERROR) */
#define TcgErrorUart(...)
#endif /* (DEBUG_UART_TCG_ERROR) */

#if (DEBUG_UART_TCG_EVENT)
INLINE void TcgEventUart(U16 uwLogCode)
{
	M_UART(TCG_, "\nOS-%b%b", (((uwLogCode) >> 8) & BIT_MASK(8)), ((uwLogCode) & BIT_MASK(8)));
}
#else /* (DEBUG_UART_TCG_EVENT) */
#define TcgEventUart(...)
#endif /* (DEBUG_UART_TCG_EVENT) */

#if (HOST_MODE == SATA)
INLINE void TcgSetATASecuritySupportByTCGLifeCycle(U8 ubLockingSPActivated)
{
	if (TRUE == ubLockingSPActivated) {
		// Disable ATA Security support
		AtaCfg->ubSecurityStatus &= ~(SATA_SECURITY_SUPPORT_BIT | SATA_SECURITY_EN_BIT | SATA_SECURITY_LOCK_BIT | SATA_SECURITY_FROZEN_BIT | SATA_SECURITY_CNT_EXPIRED_BIT | SATA_SECURITY_ENHANCE_ERASE_SUPPORT_BIT | SATA_SECURITY_MASTER_PASSWORD_MAX_BIT);
		IDT.W82.btSecuritySupport = FALSE;
	}
	else {
		// Recover ATA Security support
		if (gSATAInfoBlk.uoFeature.btSecuritySupport) {
			AtaCfg->ubSecurityStatus |= SATA_SECURITY_SUPPORT_BIT;
			AtaCfg->ubSecurityStatus |= ((gSATAInfoBlk.uoFeature.btSecurityEnhanceEraseSupport) ? SATA_SECURITY_ENHANCE_ERASE_SUPPORT_BIT : 0);
			IDT.W82.btSecuritySupport = TRUE;
		}
	}
	gSATAVar.ubLockingSPLifeCycleChange = TRUE;
}
#endif /* (HOST_MODE == SATA) */

INLINE void TcgSetSanitizeSupportbyTCGLifeCycle(U8 ubLockingSPActivated)
{
#if (SATA == HOST_MODE)
	if (gSATAInfoBlk.uoFeature.btSanitizeSupport) {
		IDT.W59.btSanitizeSupport				= (FALSE == ubLockingSPActivated); // Bit12
		IDT.W59.btACS3CmdAllowedBySanitize		= (FALSE == ubLockingSPActivated); // Bit11
		IDT.W59.btSanitizeAntifreezeLockSupport = ((FALSE == ubLockingSPActivated) && SANITIZE_ANTIFREEZE_LOCK_SUPPORT); // Bit10
		IDT.W59.btCryptoScrambleSupport 		= ((FALSE == ubLockingSPActivated) && CRYPTO_SCRAMBLE_SUPPORT && M_TCG_AES_EN()); // Bit13
		IDT.W59.btOverwriteSupport				= ((FALSE == ubLockingSPActivated) && OVERWRITE_SUPPORT); // Bit14
		IDT.W59.btBlockEraseSupport 			= ((FALSE == ubLockingSPActivated) && BLOCK_ERASE_SUPPORT); // Bit15

		if (TRUE == ubLockingSPActivated) {
			AtaCfg->ubSanitizeValue &= ~(BIT_SANITIZE_OPERATION_COMPLETED_WITHOUT_ERROR_VALUE | BIT_SANITIZE_FAILURE_MODE_POLICY_VALUE | BIT_SANITIZE_ANTIFREEZE_VALUE | BIT_RESTRICTED_SANITIZE_OVERRIDES_SECURITY);
		}
		else if (TRUE == RESTRICTED_SANITIZE_OVERRIDES_SECURITY_ENABLE) {
			AtaCfg->ubSanitizeValue |= BIT_RESTRICTED_SANITIZE_OVERRIDES_SECURITY;
		}
	}
#elif (NVME == HOST_MODE) /* (SATA == HOST_MODE) */
	if (TRUE == SUPPORT_SANITIZE_EN) {
		P_HOST_GEOMETRY_PTR pHostPrtInfo = NULL;
		pHostPrtInfo = (P_HOST_GEOMETRY_PTR) gpHostPrtInfo;
		U32 uli = 0;

		for (uli = 0; uli < MAX_CTR_NUMBER; uli++) {
			pHostPrtInfo->pVarIdfy[uli]->btSanitizeBlockEraseSup = ((FALSE == ubLockingSPActivated) && IDFY_CTRL_SUP_SANICAP_BERASE);
			pHostPrtInfo->pVarIdfy[uli]->btSanitizeCryptoEraseSup = ((FALSE == ubLockingSPActivated) && gpHostPrtInfo->pVarIdfy[uli]->btSanitizeCryptoEraseInfoBlkSup);
			pHostPrtInfo->pVarIdfy[uli]->btSanitizeOverWrSup = ((FALSE == ubLockingSPActivated) && IDFY_CTRL_SUP_SANICAP_OVERWRITE);
		}
	}
#endif /* (SATA == HOST_MODE) */
}

INLINE void TcgSetCmdFeature(U8 ubLockingSPActivated)
{
	// Activate
	//   D9-1-3-1-1
	// Revert or RevertSP
	//   D10-1-2-1-2
	//   D10-2-2-1-2
	//   D10-3-3-1-2
#if (HOST_MODE == SATA)
	TcgSetATASecuritySupportByTCGLifeCycle(ubLockingSPActivated);
#endif /* (HOST_MODE == SATA) */
	if (TRUE == NVME_ATA_SECURITY_SUPPORT) {
		gpHostPrtInfo->pNVMeATASecurity->Status.btSupport = !ubLockingSPActivated;
	}
	TcgSetSanitizeSupportbyTCGLifeCycle(ubLockingSPActivated);
}

#endif // TCG_INLINE_H_
