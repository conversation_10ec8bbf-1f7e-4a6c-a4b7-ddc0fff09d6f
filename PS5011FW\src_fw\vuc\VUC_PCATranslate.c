#include "vuc/VUC_PCATranslate.h"
#include "ftl/ftl_api.h"
#include "vuc/VUC_DumpTable.h"
#include "hal/cop1/cop1_pop_cmd.h"

static PCA_t gulPCA;
static U8 gubWaitCallBack;

void VUCPCATranslate_Callback(COP1CQRst_t uoResult)
{
	gulPCA.ulAll = uoResult.uoSHBlkSCHLCARst.ulRespInfo.ulAll;
	gubWaitCallBack = FALSE;
}

void VUC_PCATranslate(VUC_OPT_HCMD_PTR pCmd)
{
	U8 ubSubFeature = pCmd->vuc_sqcmd.vendor.PCATranslate.ubSubFeature;
	U32 ulAddress = pCmd->vuc_sqcmd.vendor.PCATranslate.ulAddress;

	if ( VUC_PCATRANSLATE_LCA_TO_PCA_MODE == ubSubFeature) {
#if (BURNER_MODE_EN)

		Get_PGD(pCmd);
		U32 ulPMDIdx, ulPTEIdx;
		U32 ulOffsetInPTE, ulOffsetInPMD;
		ulPTEIdx = ulAddress >> PTE_ENTRY_NUM_LOG;
		ulOffsetInPTE = ulAddress % PTE_ENTRY_NUM;
		ulPMDIdx = ulPTEIdx >> PMD_ENTRY_NUM_LOG;
		ulOffsetInPMD = ulPTEIdx % PMD_ENTRY_NUM;

		U32 *pulPMDAddr = (U32 *)BURNER_VENDOR_BUF_BASE;

		U32 ulPMDPCA = *(pulPMDAddr + ulPMDIdx);
		PCA_t ulReadPCA;
		U32 ulBufAddr;
		ulReadPCA.ulAll = ulPMDPCA;

		ulBufAddr = BURNER_VENDOR_BUF_BASE;

		// Load PMD
		FTLCOP0ReadN4kEntry_AOM_INIT_2((FTLCOP0ReadN4kEntryMode_t) {
			R_GETLCA_FROM_L4KTABLE, COP0_R_PGDPMD_READ, 1, NULL
		}, (PCA_t)FTLReverseCop0CieInPCA(ulReadPCA.ulAll),   &ulBufAddr);

		U32 *pulPTEAddr = (U32 *)ulBufAddr;
		U32 ulPTEPCA = *(pulPTEAddr + ulOffsetInPMD);

		if (INVALID_PCA_VALUE == ulPTEPCA) {
			// Read invalid PTE PCA
			memcpy((void *) pCmd->ulCurrentPhysicalMemoryAddr, (void *) &ulPTEPCA, sizeof(ulPTEPCA));

			return;
		}

		ulReadPCA.ulAll = ulPTEPCA;

		//Load PTE
		FTLCOP0ReadN4kEntry_AOM_INIT_2((FTLCOP0ReadN4kEntryMode_t) {
			R_GETLCA_FROM_L4KTABLE, COP0_R_PGDPMD_READ, 1, NULL
		}, (PCA_t)FTLReverseCop0CieInPCA(ulReadPCA.ulAll),   &ulBufAddr);

		U32 *pulDataAddr = (U32 *)ulBufAddr;
		U32 ulDataPCA = *(pulDataAddr + ulOffsetInPTE);

		memcpy((void *) pCmd->ulCurrentPhysicalMemoryAddr, (void *) &ulDataPCA, sizeof(ulDataPCA));

#else /*(BURNER_MODE_EN)*/

		cmd_table_t uoCallbackInfo = {(U32)VUCPCATranslate_Callback, {0}};
		COP1APISHBlkSCHLCA(ulAddress, (BIT_SEARCH_ST1 | BIT_SEARCH_ST3), COP1_SH_NOT_FW_LOAD_TABLE, COP1_SH_LBIDSEL_NOT_LBIDSEL, COP1_COP0_ATTR2, &uoCallbackInfo);
		gubWaitCallBack = TRUE;
		while (TRUE == gubWaitCallBack) {
			FWCOP1Waiting();
		}

		memcpy((void *) pCmd->ulCurrentPhysicalMemoryAddr, (void *) &gulPCA.ulAll, sizeof(gulPCA.ulAll));

		U32 ulLocalPCA;
		U8 ubLocalZByte, ubLocalCrossFrame, ubLocalLPCRC;
		M_FWPCA_GET(ulLocalPCA, ubLocalZByte, ubLocalCrossFrame, ubLocalLPCRC, gulPCA.ulAll);

		U8 ubIsSLC = gpuwVBRMP[ulLocalPCA >> gub4kEntrysPerUnitAlignLog].B.btIsSLCMode;
		memcpy((void *) (pCmd->ulCurrentPhysicalMemoryAddr + sizeof(gulPCA.ulAll)), (void *) &ubIsSLC, 1);

#endif /*(BURNER_MODE_EN)*/
	}
}
