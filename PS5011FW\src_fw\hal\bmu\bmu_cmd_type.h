#ifndef BMU_CMD_TYPE_H_
#define BMU_CMD_TYPE_H_

#include "setup.h"
#include "typedef.h"
#include "math_op.h"

/*****************************************************************
			BMU_
******************************************************************/
#define CPU_ID (0)
#define DEFAULT_TAG_ID (0)

#define BMU_NOBLK_ID (0)
#define BMU_BLK_ID (1)


enum BMUAPICMD {
	BMU_ALLOCATE_CMD = 1,
	BMU_REALLOCATE_CMD = 2,
	BMU_FREE_CMD = 3,
	BMU_VALIDATE_CMD = 4,
	BMU_LOCK_CMD = 5,
	BMU_UNLOCK_CMD = 6,
	BMU_ALLOCATE_PB_CMD = 7,
	BMU_FREE_PB_CMD = 8,
	BMU_ALLOCATE_LB_CMD = 9,
	BMU_FREE_LB_CMD = 10,               // 0x0A
	BMU_GET_LBNA_CMD = 11,              // 0x0B
	BMU_GET_PBNA_CMD = 12,              // 0x0C
	BMU_ADD_EVENT_CMD = 13,             // 0x0D
	BMU_DELETE_EVENT_CMD = 14,          // 0x0E
	BMU_EVENT_LISTENER_CMD = 15,        // 0x0F
	BMU_VALIDATE_ALLOCATE_LB_CMD = 16,  // 0x10
	BMU_VALIDATE_SIZE_CMD = 17,			//0x11
	BMU_RESERVE_LB_CMD = 18,            // 0x12
	BMU_ALLOCATE_PB_LINK_CMD = 19,		// 0x13
	BMU_WLB_SEARCH_CMD = 20,			// 0x14
	BMU_LINK_LB_CMD	= 21,				// 0x15
	BMU_UPDATE_LL_CMD = 22,				// 0x16
	BMU_BOOKING_CMD = 23,				// 0x17
	BMU_APU_FREE_CNT_EVENT = 24,		// 0x18
	BMU_HEAD_CHECK = 30,				// 0x1E
	BMU_STATIC_EVENT = 31				// 0x1F
};

typedef	struct BMURst {

	U32 ubOpcode: 5;
	U32 ubResult: 2;
	U32 ubTargetID: 4;
	U32 uwReserved1: 12;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 ulReserved2;
	U32 ulReserved3;
	U32 ulReserved4;

} BMURst_t;

/******************************
	OP Code = 1
	Allocate
******************************/
typedef struct BMUAllocateCmd {
	U32 ubOpcode: 5;
	U32 ubReserved1: 2;
	U32 ubTargetID: 4;
	U32 btOption: 1;
	U32 btAF: 1;
	U32 ubReserved2: 3;
	U32 ubTimeout: 4;
	U32 ubReserved3: 2;
	U32 btFUA: 1;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 ulReserved4: 19;
	U32 ubLBID: 3;
	U32 ubReserved5: 2;
	U32 ubCTag: 8;
	U32 ulLCA;
	U32 ubSize: 8;
	U32 ubStreamID: 4;
	U32 ulReserved6: 18;
#if PS5021_EN
	U32 ubHWReserved1: 1;
	U32 ubCTag2: 1;
#else /* PS5021_EN */
	U32 ubHWReserved1: 2;
#endif /* PS5021_EN */

} BMUAllocateCmd_t;

typedef struct BMUAllocateRst {
	U32 ubOpcode: 5;
	U32 ubResult: 2;
	U32 ubTargetID: 4;
	U32 btOption: 1;
	U32 btAF: 1;
	U32 ubReserved1: 3;
	U32 ubTimeout: 4;
	U32 ubReserved2: 2;
	U32 btFUA: 1;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 uwPBAddress: 9;
	U32 uwLBOffset: 10;
	U32 ubLBID: 3;
	U32 ubReserved3: 2;
	U32 ubCTag: 8;
	U32 ulLCA;
	U32 ubDoneCount: 8;
	U32 ubStreamID: 4;
	U32 uwReserved4: 18;
#if PS5021_EN
	U32 uwPBAddress2: 1;
	U32 ubCTag2: 1;
#else /* PS5021_EN */
	U32 ubHWReserved1: 2;
#endif /* PS5021_EN */

} BMUAllocateRst_t;

/******************************
	OP Code = 2
	ReAllocate
******************************/
typedef struct BMUReAllocateCmd {
	U32 ubOpcode: 5;
	U32 ubReserved1: 2;
	U32 ubTargetID: 4;
	U32 btOption: 1;
	U32 btROP: 1;
	U32 btLOP: 1;
	U32 ubReserved2: 2;
	U32 ubTimeout: 4;
	U32 ubReserved3: 3;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 uwReserved4: 9;
	U32 uwLBOffset: 10;
	U32 ubLBID: 3;
	U32 ubReserved5: 2;
	U32 ubCTag: 8;
	U32 uwTargetLBOffset: 10;
	U32 ubTargetLBID: 3;
	U32 ulReserved6: 19;
	U32 ubSize: 8;
	U32 ubStreamID: 4;
#if PS5021_EN
	U32 uwReserved7 		:	8;
	U32 ubCTag1 			:	1;
	U32 uwReserved8 		:	11;
#else /* PS5021_EN */
	U32 uwReserved7: 9;
	U32 uwHWReserved2: 9;
	U32 ubHWReserved1: 2;
#endif /* PS5021_EN */
} BMUReAllocateCmd_t;

typedef struct BMUReAllocateRst {
	U32 ubOpcode: 5;
	U32 ubResult: 2;
	U32 btTargetID: 4;
	U32 btOption: 1;
	U32 btROP: 1;
	U32 btLOP: 1;
	U32 ubReserved1: 2;
	U32 ubTimeout: 4;
	U32 ubReserved2: 3;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 uwPBAddress: 9;
	U32 uwLBOffset: 10;
	U32 ubLBID: 3;
#if PS5021_EN
	U32 ubE3D4KFlag: 1;
	U32 ubFull: 1;
#else /* PS5021_EN */
	U32 ubReserved3: 2;
#endif /* PS5021_EN */
	U32 ubCTag: 8;
	U32 uwTargetLBOffset: 10;
	U32 ubTargetLBID: 3;
	U32 ulReserved4: 19;
	U32 ubDoneCount: 8;
	U32 ubStreamID: 4;
#if PS5021_EN
	U32 ulReserved5: 8;
	U32 ubCTag2: 1;
	U32 ulReserved6: 9;
	U32 uwPBAddress2: 1;
	U32 ulReserved7: 1;
#else /* PS5021_EN */
	U32 ulReserved5: 18;
	U32 ubHWReserved1: 2;
#endif /* PS5021_EN */

} BMUReAllocateRst_t;

/******************************
	OP Code = 3
	Free
******************************/
typedef struct BMUFreeCmd {
	U32 ubOpcode: 5;
	U32 ubReserved1: 2;
	U32 ubTargetID: 4;
	U32 btOption: 1;
	U32 btReserved2 : 1;//U32 btFree: 1;
	U32 btFreeAsReserved: 1;
	U32 uwReserved3: 8;
	U32 btFree: 1;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 uwReserved4: 9;
	U32 uwLBOffset: 10;
	U32 ubLBID: 3;
	U32 uwReserved5: 10;
	U32 ulReserved6;
	U32 ubSize: 8;
	U32 ulReserved7: 22;
	U32 ubHWReserved1: 2;
} BMUFreeCmd_t;

typedef struct BMUFreeRst {
	U32 ubOpcode: 5;
	U32 ubResult: 2;
	U32 ubTargetID: 4;
	U32 btOption: 1;
#if PS5021_EN
	U32 uwReserved0: 1;
#else /* PS5021_EN */
	U32 btFree: 1;
#endif /* PS5021_EN */
	U32 btFreeAsReserved: 1;
#if PS5021_EN
	U32 uwReserved1: 8;
	U32 btFree: 1;
#else /* PS5021_EN */
	U32 uwReserved1: 9;
#endif /* PS5021_EN */
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 uwPBAddress: 9;
	U32 uwLBOffset: 10;
	U32 ubLBID: 3;
	U32 uwReserved3: 10;
	U32 ulReserved4;
	U32 ubDoneCount: 8;
	U32 ulReserved5: 22;
#if PS5021_EN
	U32 uwPBAddress2: 1;
	U32 ubHWReserved1: 1;
#else /* PS5021_EN */
	U32 ubHWReserved1: 2;
#endif /* PS5021_EN */

} BMUFreeRst_t;

/******************************
	OP Code = 4
	Validate
******************************/
typedef struct BMUValidateCmd {
	U32 ubOpcode: 5;
	U32 ubReserved1: 2;
	U32 ubTargetID: 4;
	U32 ubOper: 2;
	U32 ubZinfo: 3;
#if PS5021_EN
	U32 ubReserved2: 1;
	U32 btStreamID1: 1;
	U32 btRCLOP: 1;
	U32 btStreamID0: 1;
#else /* PS5021_EN */
	U32 ubReserved2: 2;
	U32 btRCLOP: 1;
	U32 btStreamID0: 1;
#endif /* PS5021_EN */
	U32 btZero: 1;
	U32 btCmdEnd: 1;
	U32 btPEOP: 1;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 uwPBAddress: 9;
	U32 uwLBOffset: 10;
	U32 ubLBID: 3;
	U32 btE3D4KFlag: 1;
	U32 btFull: 1;
	U32 ubValidBitmap: 8;
	U32 ulLCA;
	U32 ulE3D4K: 24;
#if PS5021_EN
	U32 ubPCACRC: 4;
	U32 ubReserved3: 2;
	U32 uwPBAddress2: 1;
	U32 ubReserved4: 1;
#else /* PS5021_EN */
	U32 ubPCACRC: 8;
#endif /* PS5021_EN */
} BMUValidateCmd_t;

typedef struct BMUValidateRst {
	U32 ubOpcode: 5;
	U32 ubResult: 2;
	U32 ubTargetID: 4;
	U32 ubOper: 2;
	U32 ubZinfo: 3;
#if PS5021_EN
	U32 ubReserved2: 1;
	U32 btStreamID1: 1;
#else /* PS5021_EN */
	U32 ubReserved2: 2;
#endif /* PS5021_EN */

	U32 btRCLOP: 1;
	U32 btStreamID0: 1;
	U32 btZero: 1;
	U32 btCmdEnd: 1;
	U32 btPEOP: 1;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 uwPBAddress: 9;
	U32 uwLBOffset: 10;
	U32 ubLBID: 3;
	U32 btE3D4KFlag: 1;
	U32 btFull: 1;
	U32 ubValidBitmap: 8;
	U32 ulLCA;
	U32 ulE3D4K: 24;
#if PS5021_EN
	U32 ubPCACRC: 4;
	U32 ubReserved3: 2;
	U32 uwPBAddress2: 1;
	U32 ubReserved4: 1;
#else /* PS5021_EN */
	U32 ubPCACRC: 8;
#endif /* PS5021_EN */

} BMUValidateRst_t;

/******************************
	OP Code = 5
	Lock
******************************/
typedef struct BMULockCmd {
	U32 ubOpcode: 5;
	U32 ubReserved1: 2;
	U32 ubTargetID: 4;
	U32 btOption: 1;
	U32 uwReserved2: 11;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 uwPBAddress: 9;
	U32 uwLBOffset: 10;
	U32 ubLBID: 3;
	U32 uwReserved3: 10;
	U32 ulLCA;
#if PS5021_EN
	U32 ulReserved5: 30;
	U32 uwPBAddress2: 1;
	U32 ulReserved6: 1;
#else /* PS5021_EN */
	U32 ulReserved5;
#endif /* PS5021_EN */
} BMULockCmd_t;

typedef struct BMULockRst {
	U32 ubOpcode: 5;
	U32 ubResult: 2;
	U32 ubTargetID: 4;
	U32 btOption: 1;
	U32 uwReserved1: 11;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 uwPBAddress: 9;
	U32 uwLBOffset: 10;
	U32 ubLBID: 3;
	U32 btE3D4KFlag: 1;
	U32 btFull: 1;
	U32 ubReserved2: 8;
	U32 ulLCA;
#if PS5021_EN
	U32 ulReserved4: 30;
	U32 uwPBAddress2: 1;
	U32 ulReserved5: 1;
#else /* PS5021_EN */
	U32 ulReserved4;
#endif /* PS5021_EN */
} BMULockRst_t;

/******************************
	OP Code = 6
	UnLock
******************************/
typedef struct BMUUnLockCmd {
	U32 ubOpcode: 5;
	U32 ubReserved1: 2;
	U32 ubTargetID: 4;
	U32 btReserved2: 1;
	U32 btQOP: 1;
	U32 uwReserved3: 10;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 uwPBAddress: 9;
	U32 uwReserved4: 10;
	U32 ubLBID: 3;
	U32 uwReserved5: 10;
	U32 ulReserved6;
#if PS5021_EN
	U32 ulReserved7: 30;
	U32 uwPBAddress2: 1;
	U32 ulReserved8: 1;
#else /* PS5021_EN */
	U32 ulReserved7;
#endif /* PS5021_EN */
} BMUUnLockCmd_t;

typedef struct BMUUnLockRst {
	U32 ubOpcode: 5;
	U32 ubResult: 2;
	U32 ubTargetID: 4;
	U32 btReserved1: 1;
	U32 btQOP: 1;
	U32 uwReserved2: 10;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 uwPBAddress: 9;
#if PS5021_EN
	U32 ulReserved3: 10;
	U32 ubLBID: 3;
	U32 ulReserved7 : 10;
#else /* PS5021_EN */
	U32 ulReserved3: 23;
#endif /* PS5021_EN */
	U32 ulReserved4;
#if PS5021_EN
	U32 ulReserved5: 30;
	U32 uwPBAddress2: 1;
	U32 ulReserved6: 1;
#else /* PS5021_EN */
	U32 ulReserved5;
#endif
} BMUUnLockRst_t;

/******************************
	OP Code = 7
	Allocate PB
******************************/
typedef struct BMUAllocatePBCmd {
	U32 ubOpcode: 5;
	U32 ubReserved1: 2;
	U32 ubTargetID: 4;
	U32 btOption: 1;
	U32 btQOP: 1;
	U32 ubReserved2: 3;
	U32 ubTimeout: 4;
	U32 ubReserved3: 2;
	U32 btFUA: 1;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 ulReserved4: 19;
	U32 ubLBID: 3;
	U32 ubReserved5: 2;
	U32 ubCTag: 8;
	U32 ulLCA;
	U32 ubReserved6: 8;
	U32 ubStreamID: 4;
#if PS5021_EN
	U32 ulrerved7: 19;
	U32 ubCTag2: 1;
#else /* PS5021_EN */
	U32 ulrerved7: 20;
#endif /* PS5021_EN */
} BMUAllocatePBCmd_t;

typedef struct BMUAllocatePBRst {
	U32 ubOpcode: 5;
	U32 ubResult: 2;
	U32 ubTargetID: 4;
	U32 btOption: 1;
	U32 btQOP: 1;
	U32 ubReserved1: 3;
	U32 ubTimeout: 4;
	U32 ubReserved2: 2;
	U32 btFUA: 1;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 uwPBAddress: 9;
	U32 uwReserved3: 10;
	U32 ubLBID: 3;
	U32 ubReserved4: 2;
	U32 ubCTag: 8;
	U32 ulLCA;
	U32 ubReserved5: 8;
	U32 ubStreamID: 4;
#if PS5021_EN
	U32 ulReserved6: 18;
	U32 uwPBAddress2: 1;
	U32 ubCTag2: 1;
#else /* PS5021_EN */
	U32 ulReserved6: 20;
#endif /* PS5021_EN */

} BMUAllocatePBRst_t;

/******************************
	OP Code = 8
	Free PB
******************************/
typedef struct BMUFreePBCmd {
	U32 ubOpcode: 5;
	U32 ubReserved1: 2;
	U32 ubTargetID: 4;
	U32 btReserved2: 1;
	U32 btQOP: 1;
	U32 uwReserved3: 9;
	U32 btFreeOp: 1;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 uwPBAddress: 9;
	U32 ulReserved4: 10;
	U32 ubLBID	: 3;
	U32 uwReserved5 : 10;
	U32 ulReserved6;
#if PS5021_EN
	U32 ulReserved7: 30;
	U32 uwPBAddress2: 1;
	U32 ulReserved8: 1;
#else /* PS5021_EN */
	U32 ulReserved7;
#endif /* PS5021_EN */
} BMUFreePBCmd_t;

typedef struct BMUFreePBRst {
	U32 ubOpcode: 5;
	U32 ubResult: 2;
	U32 ubTargetID: 4;
	U32 btReserved2: 1;
#if PS5021_EN
	U32 btQOP: 1;
	U32 uwReserved3: 9;
	U32 btFreeOp: 1;
#else /* PS5021_EN */
	U32 btFreeOp: 1;
	U32 uwReserved3: 10;
#endif /* PS5021_EN */
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 uwPBAddress: 9;
#if PS5021_EN
	U32 ulReserved4: 10;
	U32 ubLBID: 3;
	U32 ulReserved8: 10;
#else /* PS5021_EN */
	U32 ulReserved4: 23;
#endif /* PS5021_EN */
	U32 ulReserved5;
#if PS5021_EN
	U32 ulReserved6: 30;
	U32 uwPBAddress2: 1;
	U32 ulReserved7: 1;
#else /* PS5021_EN */
	U32 ulReserved6;
#endif /* PS5021_EN */

} BMUFreePBRst_t;

/******************************
	OP Code = 9
	Allocate_LB
******************************/
typedef struct BMUAllocateLBCmd {
	U32 ubOpcode: 5;
	U32 ubReserved1: 2;
	U32 ubTargetID: 4;
	U32 btReserved2: 1;
	U32 btQOP: 1;
	U32 ubReserved3: 3;
	U32 ubTimeout: 4;
	U32 ubReserved4: 3;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 uwPBAddr: 9;
	U32 uwReserved5: 10;
	U32 ubLBID: 3;
	U32 uwReserved6: 10;
	U32 ulReserved7;
#if PS5021_EN
	U32 ulReserved8: 30;
	U32 uwPBAddress2: 1;
	U32 ulReserved9: 1;
#else /* PS5021_EN */
	U32 ulReserved8;
#endif /* PS5021_EN */
} BMUAllocateLBCmd_t;

typedef struct BMUAllocateLBRst {
	U32 ubOpcode: 5;
	U32 ubResult: 2;
	U32 ubTargetID: 4;
	U32 btReserved2: 1;
	U32 btQOP: 1;
	U32 ubReserved3: 3;
	U32 ubTimeout: 4;
	U32 ubReserved4: 3;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 uwPBAddr: 9;
	U32 uwLBOffset: 10;
	U32 ubLBID: 3;
#if PS5021_EN
	U32 ubE3D4KFlag: 1;
	U32 ubFull: 1;
	U32 uwReserved6: 8;
	U32 ulReserved7;
	U32 ulReserved8: 30;
	U32 uwPBAddr2: 1;
	U32 ulReserved9: 1;
#else /* PS5021_EN */
	U32 uwReserved6: 10;
	U32 ulReserved7;
	U32 ulReserved8;
#endif /* PS5021_EN */

} BMUAllocateLBRst_t;

/******************************
	OP Code = 10
	Free_LB
******************************/
typedef struct BMUFreeLBCmd {
	U32 ubOpcode: 5;
	U32 ubReserved1: 2;
	U32 ubTargetID: 4;
	U32 ubReserved2: 1;
	U32 btQop: 1;
	U32 btFreeAsReserved: 1;
	U32 uwReserved3: 9;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 uwReserved4: 9;
	U32 uwLBOffset: 10;
	U32 ubLBID: 3;
	U32 uwReserved5: 10;
	U32 ulReserved6;
	U32 ulReserved7;
} BMUFreeLBCmd_t;

typedef struct BMUFreeLBRst {
	U32 ubOpcode: 5;
	U32 ubResult: 2;
	U32 ubTargetID: 4;
#if PS5021_EN
	U32 ubReserved1: 1;
	U32 btQop: 1;
#else /* PS5021_EN */
	U32 ubReserved1: 2;
#endif /* PS5021_EN */
	U32 btFreeAsReserved: 1;
	U32 uwReserved2: 9;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 uwPBAddress: 9;
	U32 uwLBOffset: 10;
	U32 ubLBID: 3;
	U32 uwReserved3: 10;
	U32 ulReserved4;
#if PS5021_EN
	U32 ulReserved5: 30;
	U32 uwPBAddress2: 1;
	U32 ulReserved6: 1;
#else /* PS5021_EN */
	U32 ulReserved5;
#endif /* PS5021_EN */

} BMUFreeLBRst_t;

/******************************
	OP Code = 11
	Get_LBNA
******************************/
typedef struct BMUGetLBNACmd {
	U32 ubOpcode: 5;
	U32 ubReserved1: 2;
	U32 ubTargetID: 4;
	U32 ubReserved2: 12;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 uwReserved3: 9;
	U32 uwLBOffset: 10;
	U32 ubLBID: 3;
	U32 uwReserved4: 10;
	U32 ulReserved5;
	U32 ulReserved6;
} BMUGetLBNACmd_t;

typedef struct BMUGetLBNARst {
	U32 ubOpcode: 5;
	U32 ubResult: 2;
	U32 ubTargetID: 4;
	U32 uwReserved2: 12;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 uwPBAddress: 9;
	U32 uwLBOffset: 10;
	U32 ubLBID: 3;
	U32 uwReserved4: 10;
	U32 ulReserved5;
#if PS5021_EN
	U32 ulReserved6: 30;
	U32 uwPBAddress2: 1;
	U32 ulReserved7: 1;
#else /* PS5021_EN */
	U32 ulReserved6;
#endif /* PS5021_EN */

} BMUGetLBNARst_t;

/******************************
	OP Code = 12
	Get_PBNA
******************************/
typedef struct BMUGetPBNACmd {
	U32 ubOpcode: 5;
	U32 ubReserved1: 2;
	U32 ubTargetID: 4;
	U32 uwReserved2: 12;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 uwPBAddress: 9;
	U32 uwLBOffset: 10;
	U32 ubLBID: 3;
	U32 uwReserved4: 10;
	U32 ulReserved5;
#if PS5021_EN
	U32 ulReserved6: 30;
	U32 uwPBAddress2: 1;
	U32 ulReserved7: 1;
#else /* PS5021_EN */
	U32 ulReserved6;
#endif /* PS5021_EN */
} BMUGetPBNACmd_t;

typedef struct BMUGetPBNARst {
	U32 ubOpcode: 5;
	U32 ubResult: 2;
	U32 ubTargetID: 4;
	U32 btReserved1: 1;
	U32 btHFflag: 1;
	U32 ubZinfo: 3;
	U32 btFree: 1;
	U32 btDF: 1;
#if PS5021_EN
	U32 btStreamID1: 1;
#else /* PS5021_EN */
	U32 btReserved2: 1;
#endif /* PS5021_EN */
	U32 btStreamID0: 1;
	U32 btZero: 1;
	U32 btCmdEnd: 1;
	U32 btFUA: 1;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 uwPBAddress: 9;
	U32 ubCTAG: 8;
	U32 ubLock: 2;
	U32 ubReserved3: 3;
	U32 btE3D4Kflag: 1;
	U32 btFull: 1;
	U32 ubValidBitmap: 8;
	U32 ulLCA;
	U32 ulE3D4K: 24;
#if PS5021_EN
	U32 ubPCACRC: 4;
	U32 ubReserved4: 2;
	U32 uwPBAddress2: 1;
	U32 ubCTAG2: 1;
#else /* PS5021_EN */
	U32 ubPCACRC: 8;
#endif /* PS5021_EN */

} BMUGetPBNARst_t;
/******************************
	OP Code = 13
	Add_Event
******************************/
typedef struct BMUAddEventCmd {
	U32 ubOpcode: 5;
	U32 ubReserved1: 2;
	U32 ubTargetID: 4;
	U32 btOption: 1;
	U32 uwReserved2: 11;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 uwPBAddress: 9;
	U32 ulReserved3: 23;
	U32	ubEventTargetID: 4;
	U32 uwReserved4: 12;
	U32 ubEventTag: 8;
	U32 ubReserved5: 8;
#if PS5021_EN
	U32 ulReserved6: 30;
	U32 uwPBAddress2: 1;
	U32 ulReserved7: 1;
#else /* PS5021_EN */
	U32 ulReserved6;
#endif /* PS5021_EN */
} BMUAddEventCmd_t;

typedef struct BMUAddEventRst {
	U32 ubOpcode: 5;
	U32 ubResult: 2;
	U32 ubTargetID: 4;
	U32 btOption: 1;
	U32 uwReserved2: 11;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 uwPBAddress: 9;
	U32 ulReserved3: 23;
	U32 uwReserved4: 16;
	U32 ubEventTag: 8;
	U32 ubReserved5: 8;
#if PS5021_EN
	U32 ulReserved6: 30;
	U32 uwPBAddress2: 1;
	U32 ulReserved7: 1;
#else /* PS5021_EN */
	U32 ulReserved6;
#endif /* PS5021_EN */

} BMUAddEventRst_t;

/******************************
	OP Code = 14
	Delete_Event
******************************/
typedef struct BMUDeleteEventCmd {
	U32 ubOpcode: 5;
	U32 ubReserved1: 2;
	U32 ubTargetID: 4;
	U32 uwReserved2: 12;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 ulReserved3;
	U32 uwReserved4: 16;
	U32 ubEventTag: 8;
	U32 ubReserved5: 8;
	U32 ulReserved6;
} BMUDeleteEventCmd_t;

typedef struct BMUDeleteEventRst {
	U32 ubOpcode: 5;
	U32 ubResult: 2;
	U32 ubTargetID: 4;
	U32 uwReserved1: 12;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 ulReserved2;
	U32 uwReserved3: 16;
	U32 ubEventTag: 8;
	U32 ubReserved4: 8;
	U32 ulReserved5;
} BMUDeleteEventRst_t;
//
/******************************
	OP Code = 15 No Cmd
	Event Listener
******************************/
typedef struct BMUEventListenerStaticRst {
	U32 ubOpcode: 5;
	U32 btET: 1;
	U32 btFlagError: 1;
	U32 ubTargetID: 4;
	U32 btXZipFirstHit: 1;
	U32 ubReserved1: 5;
	U32 btXZipHit: 1;
	U32 btXZipBuild: 1;
	U32 uwLBOffset: 10;
	U32 ubLBID: 3;
	U32 ubStreamID: 4;
	U32 btXZipErr: 1;
	U32 btPCACRC0: 1;
#if PS5021_EN
	U32 ubCTag2: 1;
	U32 ubReserved2: 1;
#else /* PS5021_EN */
	U32 ubReserved2: 2;
#endif /* PS5021_EN */

	U32 ubXZipIndex: 8;
	U32 ubZinfo: 3;
	U32 btCmdEnd: 1;
	U32 btFUA: 1;
	U32 btZero: 1;
	U32 btE3D4Kflag: 1;
	U32 btFull: 1;
	U32 ubCTag: 8;
	U32 ulLCA;
	U32 ulXZipPCA;
} BMUEventListenerStaticRst_t;

typedef struct BMUEventListenerDynamicRst {
	U32 ubOpcode: 5;
	U32 btET: 1;
	U32 btRsv: 1;
	U32 ubTargetID: 4;
	U32 ulReserved1: 21;
	U32 uwPBOffset: 9;
	U32 uwReserved2: 15;
	U32 ubValidBitmap: 8;
	U32 ubReserved3: 16;
	U32 ubEventTag: 8;
	U32 ubReserved4: 8;
#if PS5021_EN
	U32 ulReserved5 : 30;
	U32 uwPBOffset2: 1;
	U32 ulReserved6 : 1;
#else /* PS5021_EN */
	U32 ulReserved5;
#endif /* PS5021_EN */
} BMUEventListenerDynamicRst_t;

/******************************
	OP Code = 16
	Validate_Allocate_LB
******************************/
typedef struct BMUVldAllocateCmd {
	U32 ubOpcode: 5;
	U32 ubReserved1: 2;
	U32 ubTargetID: 4;
	U32 btReserved2: 1;
	U32 btQOP: 1;
	U32 ubZinfo: 3;
	U32 ubTimeout: 4;
	U32 btZero: 1;
	U32 btCmdEnd: 1;
	U32 btPEOP: 1;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 uwPBAddress: 9;
	U32 uwReserved3: 10;
	U32 ubLBID: 3;
	U32 btE3D4KFlag: 1;
	U32 btFull: 1;
	U32 ubVldBitmap: 8;
	U32 ulLCA;
	U32 ulE3D4K: 24;
#if PS5021_EN
	U32 ubPCACRC: 4;
	U32 uwReserved4: 2;
	U32 uwPBAddress2: 1;
	U32 uwReserved5: 1;
#else /* PS5021_EN */
	U32 ubPCACRC: 8;
#endif /* PS5021_EN */
} BMUVldAllocateCmd_t;

typedef struct BMUVldAllocateRst {
	U32 ubOpcode: 5;
	U32 ubResult: 2;
	U32 ubTargetID: 4;
	U32 btReserved1: 1;
	U32 btQOP: 1;
	U32 ubZinfo: 3;
	U32 ubTimeout: 4;
	U32 btZero: 1;
	U32 btCmdEnd: 1;
	U32 btPEOP: 1;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 uwPBAddress: 9;
	U32 uwLBOffset: 10;
	U32 ubLBID: 3;
	U32 btE3D4KFlag: 1;
	U32 btFull: 1;
	U32 ubVldBitmap: 8;
	U32 ulLCA;
	U32 ulE3D4K: 24;
#if PS5021_EN
	U32 ubPCACRC: 4;
	U32 btReserved2: 2;
	U32 uwPBAddress2: 1;
	U32 btReserved3: 1;
#else /* PS5021_EN */
	U32 ubPCACRC: 8;
#endif /* PS5021_EN */

} BMUVldAllocateRst_t;

/******************************
	OP Code = 17
	Validate_Size
******************************/
typedef struct BMUVldSizeCmd {
	U32 ubOpcode: 5;
	U32 ubReserved1: 2;
	U32 ubTargetID: 4;
	U32 ubOper: 2;
	U32 ubZinfo: 3;
#if PS5021_EN
	U32 ubReserved2: 1;
	U32 btStreamID1: 1;
#else /* PS5021_EN */
	U32 ubReserved2: 2;
#endif /* PS5021_EN */
	U32 btRCLOP: 1;
	U32 btStreamID0: 1;
	U32 btZero: 1;
	U32 btCmdEnd: 1;
	U32 btReserved3: 1;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 uwPBAddress: 9;
	U32 uwLBOffset: 10;
	U32 ubLBID: 3;
	U32 btReserved4: 1;
	U32 btFull: 1;
	U32 ubValidBitmap: 8;
	U32 ulLCA;
	U32 ubSize: 8;
#if PS5021_EN
	U32 ulReserved5: 22;
	U32 uwPBAddress2: 1;
	U32 ulReserved6: 1;
#else /* PS5021_EN */
	U32 ulReserved5: 24;
#endif /* PS5021_EN */
} BMUVldSizeCmd_t;

typedef struct BMUVldSizeRst {
	U32 ubOpcode: 5;
	U32 ubResult: 2;
	U32 ubTargetID: 4;
	U32 ubOper: 2;
	U32 ubZinfo: 3;
#if PS5021_EN
	U32 ubReserved1: 1;
	U32 btStreamID1: 1;
#else /* PS5021_EN */
	U32 ubReserved1: 2;
#endif /* PS5021_EN */

	U32 btRCLOP: 1;
	U32 btStreamID0: 1;
	U32 btZero: 1;
	U32 btCmdEnd: 1;
	U32 btReserved2: 1;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 uwPBAddress: 9;
	U32 uwLBOffset: 10;
	U32 ubLBID: 3;
	U32 btReserved3: 1;
	U32 btFull: 1;
	U32 ubVldBitmap: 8;
	U32	ulLCA;
	U32 ubDoneCount: 8;
#if PS5021_EN
	U32 ulReserved4: 22;
	U32 uwPBAddress2: 1;
	U32 ulReserved5: 1;
#else /* PS5021_EN */
	U32 ulReserved4: 24;
#endif /* PS5021_EN */

} BMUVldSizeRst_t;

/******************************
	OP Code = 18
	Reserve LB
******************************/
typedef struct BMUReserveLBCmd {
	U32 ubOpcode: 5;
	U32 ubReserved1: 2;
	U32 ubTargetID: 4;
	U32 uwReserved2: 12;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32	ulReserved3: 19;
	U32 ubLBID: 3;
	U32 uwReserved4: 10;
	U32	ulReserved5;
	U32 ubSize: 8;
	U32	ulReserved6: 24;
} BMUReserveLBCmd_t;

typedef struct BMUReserveLBRst {
	U32 ubOpcode: 5;
	U32 ubResult: 2;
	U32 ubTargetID: 4;
	U32 uwReserved2: 12;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 uwReserved3: 9;
	U32 uwLBOffset: 10;
	U32 ubLBID: 3;
	U32 uwReserved4: 10;
	U32 ulReserved5;
	U32 ubDoneCount: 8;
	U32	ulReserved6: 24;
} BMUReserveLBRst_t;

/******************************
	OP Code = 19
	Allocate PB Link
******************************/
typedef struct BMUAllocatePBLinkCmd {
	U32 ubOpcode: 5;
	U32 ubReserved1: 2;
	U32 ubTargetID: 4;
	U32 btOption: 1;
	U32 ubReserved2: 4;
	U32 ubTimeout: 4;
	U32 ubReserved3: 3;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 uwReserved4: 9;
	U32 uwLBOffset: 10;
	U32 ubLBID: 3;
	U32 ubReserved5: 2;
	U32 ubCTag: 8;
	U32 ulLCA;
	U32 ubSize: 8;
	U32 ubStreamID: 4;
#if PS5021_EN
	U32 ulReserved6: 19;
	U32 ubCTag2: 1;
#else /* PS5021_EN */
	U32	ulReserved6: 20;
#endif /* PS5021_EN */
} BMUAllocatePBLinkCmd_t;

typedef struct BMUAllocatePBLinkRst {
	U32 ubOpcode: 5;
	U32 ubResult: 2;
	U32 ubTargetID: 4;
	U32 btOption: 1;
	U32 ubReserved1: 4;
	U32 ubTimeout: 4;
	U32 ubReserved2: 3;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 uwPBAddr: 9;
	U32 uwLBOffset: 10;
	U32 ubLBID: 3;
	U32 ubReserved3: 2;
	U32 ubCTag: 8;
	U32 ulLCA;
	U32 ubDoneCount: 8;
	U32 ubStreamID: 4;
#if PS5021_EN
	U32 ulReserved4: 18;
	U32 uwPBAddr2: 1;
	U32 ubCTag2: 1;
#else /* PS5021_EN */
	U32 ulReserved4: 20;
#endif /* PS5021_EN */

} BMUAllocatePBLinkRst_t;

/******************************
	OP Code = 20
	WLB Search
******************************/
typedef struct BMUWLBSearchCmd {
	U32 ubOpcode: 5;
	U32 ubReserved1: 2;
	U32 ubTargetID: 4;
	U32 btOption: 1;
	U32 uwReserved2: 11;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 uwReserved3: 9;
	U32 uwLBOffset: 10;
	U32 ubLBID: 3;
	U32 uwReserved4: 10;
	U32 ulLCA;
	U32	ulReserved5;
} BMUWLBSearchCmd_t;

typedef struct BMUWLBSearchRst {
	U32 ubOpcode: 5;
	U32 ubResult: 2;
	U32 ubTargetID: 4;
	U32 btOption: 1;
	U32 ubReserved1: 8;
	U32 btZero: 1;
	U32 ubReserved2: 2;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 uwPBAddr: 9;
	U32 uwLBOffset: 10;
	U32 ubLBID: 3;
	U32 btE3D4KFlag: 1;
	U32 btFull: 1;
	U32 ubReserved3: 8;
	U32 ulLCA;
#if PS5021_EN
	U32 ulReserved4: 30;
	U32 uwPBAddr2: 1;
	U32 ulReserved5: 1;
#else /* PS5021_EN */
	U32 ulReserved4;
#endif /* PS5021_EN */

} BMUWLBSearchRst_t;

/******************************
	OP Code = 21
	Link LB
******************************/
typedef struct BMULinkLBCmd {
	U32 ubOpcode: 5;
	U32 ubReserved1: 2;
	U32 ubTargetID: 4;
	U32 btReserved2: 1;
	U32 btQOP: 1;
	U32 uwReserved3: 10;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 uwPBAddr: 9;
	U32 uwLBOffset: 10;
	U32 ubLBID: 3;
	U32 uwReserved4: 10;
	U32	ulReserved5;
#if PS5021_EN
	U32 ulReserved6: 30;
	U32 uwPBAddr2: 1;
	U32 ulReserved7: 1;
#else /* PS5021_EN */
	U32	ulReserved6;
#endif /* PS5021_EN */
} BMULinkLBCmd_t;

typedef struct BMULinkLBRst {
	U32 ubOpcode: 5;
	U32 ubResult: 2;
	U32 ubTargetID: 4;
	U32 btReserved2: 1;
	U32 btQOP: 1;
	U32 uwReserved3: 10;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 uwPBAddr: 9;
	U32 uwLBOffset: 10;
	U32 ubLBID: 3;
#if PS5021_EN
	U32 ubE3D4KFlag: 1;
	U32 ubFull: 1;
	U32 uwReserved4: 8;
#else /* PS5021_EN */
	U32 uwReserved4: 10;
#endif /* PS5021_EN */

	U32	ulReserved5;
#if PS5021_EN
	U32 ulReserved6: 30;
	U32 uwPBAddr2: 1;
	U32 ulReserved7: 1;
#else /* PS5021_EN */
	U32 ulReserved6;
#endif /* PS5021_EN */

} BMULinkLBRst_t;

/******************************
	OP Code = 22
	Update LL
******************************/
typedef struct BMUUpdateLLCmd {
	U32 ubOpcode: 5;
	U32 ubReserved1: 2;
	U32 ubTargetID: 4;
	U32 uwReserved2: 12;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 uwTargetValue: 9;
	U32 uwReserved3: 10;
	U32 ubLBID: 3;
	U32 uwReserved4: 10;
	U32	ulReserved5;
#if PS5021_EN
	U32 ulReserved6: 30;
	U32 uwTargetValue2: 1;
	U32 ulReserved7: 1;
#else /* PS5021_EN */
	U32	ulReserved6;
#endif /* PS5021_EN */
} BMUUpdateLLCmd_t;

typedef struct BMUUpdateLLRst {
	U32 ubOpcode: 5;
	U32 ubResult: 2;
	U32 ubTargetID: 4;
	U32 uwReserved2: 12;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 uwTargetValue: 9;
	U32 uwReserved3: 10;
	U32 ubLBID: 3;
	U32 uwReserved4: 10;
	U32	ulReserved5;
#if PS5021_EN
	U32 ulReserved6: 30;
	U32 uwTargetValue2: 1;
	U32 ulReserved7: 1;
#else /* PS5021_EN */
	U32 ulReserved6;
#endif /* PS5021_EN */
} BMUUpdateLLRst_t;

/******************************
	OP Code = 23
	Booking
******************************/
typedef struct BMUBookingCmd {
	U32 ubOpcode: 5;
	U32 ubReserved1: 2;
	U32 ubTargetID: 4;
	U32 ubOper: 2;
	U32 uwReserved2: 10;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 uwRequestValue: 9;
	U32 ulReserved3: 23;
	U32	ulReserved4;
#if PS5021_EN
	U32 ulReserved5: 30;
	U32 uwRequestValue2: 1;
	U32 ulReserved6: 1;
#else /* PS5021_EN */
	U32	ulReserved5;
#endif /* PS5021_EN */
} BMUBookingCmd_t;

typedef struct BMUBookingRst {
	U32 ubOpcode: 5;
	U32 ubResult: 2;
	U32 ubTargetID: 4;
	U32 ubOper: 2;
	U32 uwReserved2: 10;
	U32 btNoCQ: 1;
	U32 ubTagID: 8;
	U32 uwGrantValue: 9;
	U32	ulReserved3: 23;
#if PS5021_EN
	U32 uwAPUFreeCnt: 12;
	U32 ulReserved4: 20;
	U32 ulReserved5: 30;
	U32 uwGrantValue2: 1;
	U32 ulReserved6: 1;
#else /* PS5021_EN */
	U32 uwAPUFreeCnt: 11;
	U32 ulReserved4: 21;
	U32 ulReserved5;
#endif /* PS5021_EN */

} BMUBookingRst_t;

/******************************
	OP Code = 24	No Cmd
	APU Free Cnt Event
******************************/
typedef struct BMUAPUFreeCntEventRst {
	U32 ubOpcode: 5;
	U32 ubReserved1: 2;
	U32 ubTargetID: 4;
	U32	ulReserved2: 21;
	U32 uwThreshold: 9;
	U32 ulReserved3: 23;
#if PS5021_EN
	U32 uwAPUFreeCnt: 12;
	U32 ulReserved4: 20;
	U32 ulReserved5: 30;
	U32 uwThreshold2: 1;
	U32 ulReserved6: 1;
#else /* PS5021_EN */
	U32 uwAPUFreeCnt: 11;
	U32 ulReserved4: 21;
	U32 ulReserved5;
#endif /* PS5021_EN */

} BMUAPUFreeCntEventRst_t;

/******************************
	OP Code = 30
	Head Check
******************************/
typedef struct BMUHeadCheck {
	U32 ubOpcode: 5;
	U32	ulReserved1: 27;
	U32 uwPBAddress: 9;
	U32 uwLBOffset: 10;
	U32 ubLBID: 3;
	U32 uwReserved2: 10;
	U32	ulReserved3;
	U32	ulReserved4;
} BMUHeadCheck_t;

/******************************
	OP Code = 31
	Static Event
******************************/
typedef struct BMUStaticEvent {
	U32 ubOpcode: 5;
	U32	ulReserved1: 27;
	U32 uwPBAddress: 9;
	U32 uwLBOffset: 10;
	U32 ubLBID: 3;
	U32 uwReserved2: 10;
	U32	ulReserved3;
	U32	ulReserved4;
} BMUStaticEvent_t;

// BMU Allocate
// 1st 4Byte
#define BMU_CMD_ALLOCATE_OPCODE_SHIFT							(0)
#define BMU_CMD_ALLOCATE_OPCODE_MASK							(BIT_MASK(5))
#define BMU_CMD_ALLOCATE_TARGET_ID_SHIFT						(7)
#define BMU_CMD_ALLOCATE_TARGET_ID_MASK							(BIT_MASK(4))
#define BMU_CMD_ALLOCATE_OPTION_SHIFT							(11)
#define BMU_CMD_ALLOCATE_OPTION_MASK							(BIT_MASK(1))
#define BMU_CMD_ALLOCATE_AF_SHIFT								(12)
#define BMU_CMD_ALLOCATE_AF_MASK								(BIT_MASK(1))
#define BMU_CMD_ALLOCATE_TIMEOUT_SHIFT							(16)
#define BMU_CMD_ALLOCATE_TIMEOUT_MASK							(BIT_MASK(4))
#define BMU_CMD_ALLOCATE_FUA_SHIFT								(22)
#define BMU_CMD_ALLOCATE_FUA_MASK								(BIT_MASK(1))
#define BMU_CMD_ALLOCATE_NOCQ_SHIFT								(23)
#define BMU_CMD_ALLOCATE_NOCQ_MASK								(BIT_MASK(1))
#define BMU_CMD_ALLOCATE_TAG_ID_SHIFT							(24)
#define BMU_CMD_ALLOCATE_TAG_ID_MASK							(BIT_MASK(8))
// 2nd 4Byte
#define BMU_CMD_ALLOCATE_LB_ID_SHIFT							(19)
#define BMU_CMD_ALLOCATE_LB_ID_MASK								(BIT_MASK(3))
#define BMU_CMD_ALLOCATE_CTAG_SHIFT								(24)
#define BMU_CMD_ALLOCATE_CTAG_MASK								(BIT_MASK(8))
// 3rd 4Byte
#define BMU_CMD_ALLOCATE_LCA_SHIFT								(0)
// 4th 4Byte
#define BMU_CMD_ALLOCATE_SIZE_SHIFT								(0)
#define BMU_CMD_ALLOCATE_SIZE_MASK								(BIT_MASK(8))
#define BMU_CMD_ALLOCATE_STREAM_ID_SHIFT						(8)
#define BMU_CMD_ALLOCATE_STREAM_ID_MASK							(BIT_MASK(4))
#if PS5021_EN
#define BMU_CMD_ALLOCATE_CTAG2_SHIFT							(31)
#define BMU_CMD_ALLOCATE_CTAG2_MASK								(BIT_MASK(1))
#endif /* PS5021_EN */

// BMU Reallocate
// 1st 4Byte
#define BMU_CMD_REALLOCATE_OPCODE_SHIFT							(0)
#define BMU_CMD_REALLOCATE_OPCODE_MASK							(BIT_MASK(5))
#define BMU_CMD_REALLOCATE_TARGET_ID_SHIFT						(7)
#define BMU_CMD_REALLOCATE_TARGET_ID_MASK						(BIT_MASK(4))
#define BMU_CMD_REALLOCATE_OPTION_SHIFT							(11)
#define BMU_CMD_REALLOCATE_OPTION_MASK							(BIT_MASK(1))
#define BMU_CMD_REALLOCATE_ROP_SHIFT							(12)
#define BMU_CMD_REALLOCATE_ROP_MASK								(BIT_MASK(1))
#define BMU_CMD_REALLOCATE_LOP_SHIFT							(13)
#define BMU_CMD_REALLOCATE_LOP_MASK								(BIT_MASK(1))
#define BMU_CMD_REALLOCATE_TIMEOUT_SHIFT						(16)
#define BMU_CMD_REALLOCATE_TIMEOUT_MASK							(BIT_MASK(4))
#define BMU_CMD_REALLOCATE_NOCQ_SHIFT							(23)
#define BMU_CMD_REALLOCATE_NOCQ_MASK							(BIT_MASK(1))
#define BMU_CMD_REALLOCATE_TAG_ID_SHIFT							(24)
#define BMU_CMD_REALLOCATE_TAG_ID_MASK							(BIT_MASK(8))
// 2nd 4Byte
#define BMU_CMD_REALLOCATE_LB_OFFSET_SHIFT						(9)
#define BMU_CMD_REALLOCATE_LB_OFFSET_MASK						(BIT_MASK(10))
#define BMU_CMD_REALLOCATE_LB_ID_SHIFT							(19)
#define BMU_CMD_REALLOCATE_LB_ID_MASK							(BIT_MASK(3))
#define BMU_CMD_REALLOCATE_CTAG_SHIFT							(24)
#define BMU_CMD_REALLOCATE_CTAG_MASK							(BIT_MASK(8))
// 3rd 4Byte
#define BMU_CMD_REALLOCATE_TARGET_LB_OFFSET_SHIFT				(0)
#define BMU_CMD_REALLOCATE_TARGET_LB_OFFSET_MASK				(BIT_MASK(10))
#define BMU_CMD_REALLOCATE_TARGET_LB_ID_SHIFT					(10)
#define BMU_CMD_REALLOCATE_TARGET_LB_ID_MASK					(BIT_MASK(3))
// 4th 4Byte
#define BMU_CMD_REALLOCATE_SIZE_SHIFT							(0)
#define BMU_CMD_REALLOCATE_SIZE_MASK							(BIT_MASK(8))
#define BMU_CMD_REALLOCATE_STREAM_ID_SHIFT						(8)
#define BMU_CMD_REALLOCATE_STREAM_ID_MASK						(BIT_MASK(4))
#if PS5021_EN
#define BMU_CMD_REALLOCATE_CTAG2_SHIFT							(20)
#define BMU_CMD_REALLOCATE_CTAG2_MASK							(BIT_MASK(1))
#endif /* PS5021_EN */

// BMU Free
// 1st 4Byte
#define BMU_CMD_FREE_OPCODE_SHIFT								(0)
#define BMU_CMD_FREE_OPCODE_MASK								(BIT_MASK(5))
#define BMU_CMD_FREE_TARGET_ID_SHIFT							(7)
#define BMU_CMD_FREE_TARGET_ID_MASK								(BIT_MASK(4))
#define BMU_CMD_FREE_OPTION_SHIFT								(11)
#define BMU_CMD_FREE_OPTION_MASK								(BIT_MASK(1))
#define BMU_CMD_FREE_FAR_SHIFT									(13)
#define BMU_CMD_FREE_FAR_MASK									(BIT_MASK(1))
#define BMU_CMD_FREE_FREE_OP_SHIFT								(22)
#define BMU_CMD_FREE_FREE_OP_MASK								(BIT_MASK(1))
#define BMU_CMD_FREE_NOCQ_SHIFT									(23)
#define BMU_CMD_FREE_NOCQ_MASK									(BIT_MASK(1))
#define BMU_CMD_FREE_TAG_ID_SHIFT								(24)
#define BMU_CMD_FREE_TAG_ID_MASK								(BIT_MASK(8))
// 2nd 4Byte
#define BMU_CMD_FREE_LB_OFFSET_SHIFT							(9)
#define BMU_CMD_FREE_LB_OFFSET_MASK								(BIT_MASK(10))
#define BMU_CMD_FREE_LB_ID_SHIFT								(19)
#define BMU_CMD_FREE_LB_ID_MASK									(BIT_MASK(3))
// 3rd 4Byte
// 4th 4Byte
#define BMU_CMD_FREE_SIZE_SHIFT									(0)
#define BMU_CMD_FREE_SIZE_MASK									(BIT_MASK(8))

// BMU Validate
// 1st 4Byte
#define BMU_CMD_VALIDATE_OPCODE_SHIFT							(0)
#define BMU_CMD_VALIDATE_OPCODE_MASK							(BIT_MASK(5))
#define BMU_CMD_VALIDATE_TARGET_ID_SHIFT						(7)
#define BMU_CMD_VALIDATE_TARGET_ID_MASK							(BIT_MASK(4))
#define BMU_CMD_VALIDATE_OPER_SHIFT								(11)
#define BMU_CMD_VALIDATE_OPER_MASK								(BIT_MASK(2))
#define BMU_CMD_VALIDATE_ZINFO_SHIFT							(13)
#define BMU_CMD_VALIDATE_ZINFO_MASK								(BIT_MASK(3))
#if PS5021_EN
#define BMU_CMD_VALIDATE_STREAM_ID1_SHIFT						(17)
#define BMU_CMD_VALIDATE_STREAM_ID1_MASK						(BIT_MASK(1))
#endif /* PS5021_EN */

#define BMU_CMD_VALIDATE_RCLOP_SHIFT							(18)
#define BMU_CMD_VALIDATE_RCLOP_MASK								(BIT_MASK(1))
#define BMU_CMD_VALIDATE_STREAM_ID0_SHIFT						(19)
#define BMU_CMD_VALIDATE_STREAM_ID0_MASK						(BIT_MASK(1))
#define BMU_CMD_VALIDATE_ZERO_SHIFT								(20)
#define BMU_CMD_VALIDATE_ZERO_MASK								(BIT_MASK(1))
#define BMU_CMD_VALIDATE_CMD_END_SHIFT							(21)
#define BMU_CMD_VALIDATE_CMD_END_MASK							(BIT_MASK(1))
#define BMU_CMD_VALIDATE_PEOP_SHIFT								(22)
#define BMU_CMD_VALIDATE_PEOP_MASK								(BIT_MASK(1))
#define BMU_CMD_VALIDATE_NOCQ_SHIFT								(23)
#define BMU_CMD_VALIDATE_NOCQ_MASK								(BIT_MASK(1))
#define BMU_CMD_VALIDATE_TAG_ID_SHIFT							(24)
#define BMU_CMD_VALIDATE_TAG_ID_MASK							(BIT_MASK(8))
// 2nd 4Byte
#define BMU_CMD_VALIDATE_PB_ADDR_SHIFT							(0)
#define BMU_CMD_VALIDATE_PB_ADDR_MASK							(BIT_MASK(9))
#define BMU_CMD_VALIDATE_LB_OFFSET_SHIFT						(9)
#define BMU_CMD_VALIDATE_LB_OFFSET_MASK							(BIT_MASK(10))
#define BMU_CMD_VALIDATE_LB_ID_SHIFT							(19)
#define BMU_CMD_VALIDATE_LB_ID_MASK								(BIT_MASK(3))
#define BMU_CMD_VALIDATE_E3D4K_FLAG_SHIFT						(22)
#define BMU_CMD_VALIDATE_E3D4K_FLAG_MASK						(BIT_MASK(1))
#define BMU_CMD_VALIDATE_FULL_SHIFT								(23)
#define BMU_CMD_VALIDATE_FULL_MASK								(BIT_MASK(1))
#define BMU_CMD_VALIDATE_VALIDBMP_SHIFT							(24)
#define BMU_CMD_VALIDATE_VALIDBMP_MASK							(BIT_MASK(8))
// 3rd 4Byte
#define BMU_CMD_VALIDATE_LCA_SHIFT								(0)
// 4th 4Byte
#define BMU_CMD_VALIDATE_E3D4K_SHIFT							(0)
#define BMU_CMD_VALIDATE_E3D4K_MASK								(BIT_MASK(24))
#define BMU_CMD_VALIDATE_PCACRC_SHIFT							(24)
#define BMU_CMD_VALIDATE_PCACRC_MASK							(BIT_MASK(4))
#if PS5021_EN
#define BMU_CMD_VALIDATE_PB_ADDR2_SHIFT							(30)
#define BMU_CMD_VALIDATE_PB_ADDR2_MASK							(BIT_MASK(1))
#endif /* PS5021_EN */

// BMU Lock
// 1st 4Byte
#define BMU_CMD_LOCK_OPCODE_SHIFT								(0)
#define BMU_CMD_LOCK_OPCODE_MASK								(BIT_MASK(5))
#define BMU_CMD_LOCK_TARGET_ID_SHIFT							(7)
#define BMU_CMD_LOCK_TARGET_ID_MASK								(BIT_MASK(4))
#define BMU_CMD_LOCK_OPTION_SHIFT								(11)
#define BMU_CMD_LOCK_OPTION_MASK								(BIT_MASK(1))
#define BMU_CMD_LOCK_NOCQ_SHIFT									(23)
#define BMU_CMD_LOCK_NOCQ_MASK									(BIT_MASK(1))
#define BMU_CMD_LOCK_TAG_ID_SHIFT								(24)
#define BMU_CMD_LOCK_TAG_ID_MASK								(BIT_MASK(8))
// 2nd 4Byte
#define BMU_CMD_LOCK_PB_ADDR_SHIFT								(0)
#define BMU_CMD_LOCK_PB_ADDR_MASK								(BIT_MASK(9))
#define BMU_CMD_LOCK_LB_OFFSET_SHIFT							(9)
#define BMU_CMD_LOCK_LB_OFFSET_MASK								(BIT_MASK(10))
#define BMU_CMD_LOCK_LB_ID_SHIFT								(19)
#define BMU_CMD_LOCK_LB_ID_MASK									(BIT_MASK(3))
// 3rd 4Byte
#define BMU_CMD_LOCK_LCA_SHIFT									(0)
// 4th 4Byte
#if PS5021_EN
#define BMU_CMD_LOCK_PB_ADDR2_SHIFT								(30)
#define BMU_CMD_LOCK_PB_ADDR2_MASK								(BIT_MASK(1))
#endif /* PS5021_EN */

// BMU Unlock
// 1st 4Byte
#define BMU_CMD_UNLOCK_OPCODE_SHIFT								(0)
#define BMU_CMD_UNLOCK_OPCODE_MASK								(BIT_MASK(5))
#define BMU_CMD_UNLOCK_TARGET_ID_SHIFT							(7)
#define BMU_CMD_UNLOCK_TARGET_ID_MASK							(BIT_MASK(4))
#define BMU_CMD_UNLOCK_QOP_SHIFT								(12)
#define BMU_CMD_UNLOCK_QOP_MASK									(BIT_MASK(1))
#define BMU_CMD_UNLOCK_NOCQ_SHIFT								(23)
#define BMU_CMD_UNLOCK_NOCQ_MASK								(BIT_MASK(1))
#define BMU_CMD_UNLOCK_TAG_ID_SHIFT								(24)
#define BMU_CMD_UNLOCK_TAG_ID_MASK								(BIT_MASK(8))
// 2nd 4Byte
#define BMU_CMD_UNLOCK_PB_ADDR_SHIFT							(0)
#define BMU_CMD_UNLOCK_PB_ADDR_MASK								(BIT_MASK(9))
#define BMU_CMD_UNLOCK_LB_ID_SHIFT								(19)
#define BMU_CMD_UNLOCK_LB_ID_MASK								(BIT_MASK(3))
// 3rd 4Byte
// 4th 4Byte
#if PS5021_EN
#define BMU_CMD_UNLOCK_PB_ADDR2_SHIFT							(30)
#define BMU_CMD_UNLOCK_PB_ADDR2_MASK							(BIT_MASK(1))
#endif /* PS5021_EN */

// BMU Allocate PB
// 1st 4Byte
#define BMU_CMD_ALLOCATE_PB_OPCODE_SHIFT						(0)
#define BMU_CMD_ALLOCATE_PB_OPCODE_MASK							(BIT_MASK(5))
#define BMU_CMD_ALLOCATE_PB_TARGET_ID_SHIFT						(7)
#define BMU_CMD_ALLOCATE_PB_TARGET_ID_MASK						(BIT_MASK(4))
#define BMU_CMD_ALLOCATE_PB_OPTION_SHIFT						(11)
#define BMU_CMD_ALLOCATE_PB_OPTION_MASK							(BIT_MASK(1))
#define BMU_CMD_ALLOCATE_PB_QOP_SHIFT							(12)
#define BMU_CMD_ALLOCATE_PB_QOP_MASK							(BIT_MASK(1))
#define BMU_CMD_ALLOCATE_PB_TIMEOUT_SHIFT						(16)
#define BMU_CMD_ALLOCATE_PB_TIMEOUT_MASK						(BIT_MASK(4))
#define BMU_CMD_ALLOCATE_PB_FUA_SHIFT							(22)
#define BMU_CMD_ALLOCATE_PB_FUA_MASK							(BIT_MASK(1))
#define BMU_CMD_ALLOCATE_PB_NOCQ_SHIFT							(23)
#define BMU_CMD_ALLOCATE_PB_NOCQ_MASK							(BIT_MASK(1))
#define BMU_CMD_ALLOCATE_PB_TAG_ID_SHIFT						(24)
#define BMU_CMD_ALLOCATE_PB_TAG_ID_MASK							(BIT_MASK(8))
// 2nd 4Byte
#define BMU_CMD_ALLOCATE_PB_LB_ID_SHIFT							(19)
#define BMU_CMD_ALLOCATE_PB_LB_ID_MASK							(BIT_MASK(3))
#define BMU_CMD_ALLOCATE_PB_CTAG_SHIFT							(24)
#define BMU_CMD_ALLOCATE_PB_CTAG_MASK							(BIT_MASK(8))
// 3rd 4Byte
#define BMU_CMD_ALLOCATE_PB_LCA_SHIFT							(0)
// 4th 4Byte
#define BMU_CMD_ALLOCATE_PB_STREAM_ID_SHIFT						(8)
#define BMU_CMD_ALLOCATE_PB_STREAM_ID_MASK						(BIT_MASK(4))
#if PS5021_EN
#define BMU_CMD_ALLOCATE_PB_CTAG2_SHIFT							(31)
#define BMU_CMD_ALLOCATE_PB_CTAG2_MASK							(BIT_MASK(1))
#endif /* PS5021_EN */

// BMU Free PB
// 1st 4Byte
#define BMU_CMD_FREE_PB_OPCODE_SHIFT							(0)
#define BMU_CMD_FREE_PB_OPCODE_MASK								(BIT_MASK(5))
#define BMU_CMD_FREE_PB_TARGET_ID_SHIFT							(7)
#define BMU_CMD_FREE_PB_TARGET_ID_MASK							(BIT_MASK(4))
#define BMU_CMD_FREE_PB_QOP_SHIFT								(12)
#define BMU_CMD_FREE_PB_QOP_MASK								(BIT_MASK(1))
#define BMU_CMD_FREE_PB_FREE_OP_SHIFT							(22)
#define BMU_CMD_FREE_PB_FREE_OP_MASK							(BIT_MASK(1))
#define BMU_CMD_FREE_PB_NOCQ_SHIFT								(23)
#define BMU_CMD_FREE_PB_NOCQ_MASK								(BIT_MASK(1))
#define BMU_CMD_FREE_PB_TAG_ID_SHIFT							(24)
#define BMU_CMD_FREE_PB_TAG_ID_MASK								(BIT_MASK(8))
// 2nd 4Byte
#define BMU_CMD_FREE_PB_PB_ADDR_SHIFT							(0)
#define BMU_CMD_FREE_PB_PB_ADDR_MASK							(BIT_MASK(9))
#define BMU_CMD_FREE_PB_LB_ID_SHIFT								(19)
#define BMU_CMD_FREE_PB_LB_ID_MASK								(BIT_MASK(3))
// 3rd 4Byte
// 4th 4Byte
#if PS5021_EN
#define BMU_CMD_FREE_PB_PB_ADDR2_SHIFT							(30)
#define BMU_CMD_FREE_PB_PB_ADDR2_MASK							(BIT_MASK(1))
#endif /* PS5021_EN */

// BMU Allocate LB
// 1st 4Byte
#define BMU_CMD_ALLOCATE_LB_OPCODE_SHIFT						(0)
#define BMU_CMD_ALLOCATE_LB_OPCODE_MASK							(BIT_MASK(5))
#define BMU_CMD_ALLOCATE_LB_TARGET_ID_SHIFT						(7)
#define BMU_CMD_ALLOCATE_LB_TARGET_ID_MASK						(BIT_MASK(4))
#define BMU_CMD_ALLOCATE_LB_QOP_SHIFT							(12)
#define BMU_CMD_ALLOCATE_LB_QOP_MASK							(BIT_MASK(1))
#define BMU_CMD_ALLOCATE_LB_TIMEOUT_SHIFT						(16)
#define BMU_CMD_ALLOCATE_LB_TIMEOUT_MASK						(BIT_MASK(4))
#define BMU_CMD_ALLOCATE_LB_NOCQ_SHIFT							(23)
#define BMU_CMD_ALLOCATE_LB_NOCQ_MASK							(BIT_MASK(1))
#define BMU_CMD_ALLOCATE_LB_TAG_ID_SHIFT						(24)
#define BMU_CMD_ALLOCATE_LB_TAG_ID_MASK							(BIT_MASK(8))
// 2nd 4Byte
#define BMU_CMD_ALLOCATE_LB_PB_ADDR_SHIFT						(0)
#define BMU_CMD_ALLOCATE_LB_PB_ADDR_MASK						(BIT_MASK(9))
#define BMU_CMD_ALLOCATE_LB_LB_ID_SHIFT							(19)
#define BMU_CMD_ALLOCATE_LB_LB_ID_MASK							(BIT_MASK(3))
// 3rd 4Byte
// 4th 4Byte
#if PS5021_EN
#define BMU_CMD_ALLOCATE_LB_PB_ADDR2_SHIFT						(30)
#define BMU_CMD_ALLOCATE_LB_PB_ADDR2_MASK						(BIT_MASK(1))
#endif /* PS5021_EN */

// BMU Free LB
// 1st 4Byte
#define BMU_CMD_FREE_LB_OPCODE_SHIFT							(0)
#define BMU_CMD_FREE_LB_OPCODE_MASK								(BIT_MASK(5))
#define BMU_CMD_FREE_LB_TARGET_ID_SHIFT							(7)
#define BMU_CMD_FREE_LB_TARGET_ID_MASK							(BIT_MASK(4))
#define BMU_CMD_FREE_LB_QOP_SHIFT								(12)
#define BMU_CMD_FREE_LB_QOP_MASK								(BIT_MASK(1))
#define BMU_CMD_FREE_LB_FAR_SHIFT								(13)
#define BMU_CMD_FREE_LB_FAR_MASK								(BIT_MASK(1))
#define BMU_CMD_FREE_LB_NOCQ_SHIFT								(23)
#define BMU_CMD_FREE_LB_NOCQ_MASK								(BIT_MASK(1))
#define BMU_CMD_FREE_LB_TAG_ID_SHIFT							(24)
#define BMU_CMD_FREE_LB_TAG_ID_MASK								(BIT_MASK(8))
// 2nd 4Byte
#define BMU_CMD_FREE_LB_LB_OFFSET_SHIFT							(9)
#define BMU_CMD_FREE_LB_LB_OFFSET_MASK							(BIT_MASK(10))
#define BMU_CMD_FREE_LB_LB_ID_SHIFT								(19)
#define BMU_CMD_FREE_LB_LB_ID_MASK								(BIT_MASK(3))
// 3rd 4Byte
// 4th 4Byte

// BMU Get LBNA
// 1st 4Byte
#define BMU_CMD_GET_LBNA_OPCODE_SHIFT							(0)
#define BMU_CMD_GET_LBNA_OPCODE_MASK							(BIT_MASK(5))
#define BMU_CMD_GET_LBNA_TARGET_ID_SHIFT						(7)
#define BMU_CMD_GET_LBNA_TARGET_ID_MASK							(BIT_MASK(4))
#define BMU_CMD_GET_LBNA_NOCQ_SHIFT								(23)
#define BMU_CMD_GET_LBNA_NOCQ_MASK								(BIT_MASK(1))
#define BMU_CMD_GET_LBNA_TAG_ID_SHIFT							(24)
#define BMU_CMD_GET_LBNA_TAG_ID_MASK							(BIT_MASK(8))
// 2nd 4Byte
#define BMU_CMD_GET_LBNA_LB_OFFSET_SHIFT						(9)
#define BMU_CMD_GET_LBNA_LB_OFFSET_MASK							(BIT_MASK(10))
#define BMU_CMD_GET_LBNA_LB_ID_SHIFT							(19)
#define BMU_CMD_GET_LBNA_LB_ID_MASK								(BIT_MASK(3))
// 3rd 4Byte
// 4th 4Byte

// BMU Get PBNA
// 1st 4Byte
#define BMU_CMD_GET_PBNA_OPCODE_SHIFT							(0)
#define BMU_CMD_GET_PBNA_OPCODE_MASK							(BIT_MASK(5))
#define BMU_CMD_GET_PBNA_TARGET_ID_SHIFT						(7)
#define BMU_CMD_GET_PBNA_TARGET_ID_MASK							(BIT_MASK(4))
#define BMU_CMD_GET_PBNA_NOCQ_SHIFT								(23)
#define BMU_CMD_GET_PBNA_NOCQ_MASK								(BIT_MASK(1))
#define BMU_CMD_GET_PBNA_TAG_ID_SHIFT							(24)
#define BMU_CMD_GET_PBNA_TAG_ID_MASK							(BIT_MASK(8))
// 2nd 4Byte
#define BMU_CMD_GET_PBNA_PB_ADDR_SHIFT							(0)
#define BMU_CMD_GET_PBNA_PB_ADDR_MASK							(BIT_MASK(9))
#define BMU_CMD_GET_PBNA_LB_OFFSET_SHIFT						(9)
#define BMU_CMD_GET_PBNA_LB_OFFSET_MASK							(BIT_MASK(10))
#define BMU_CMD_GET_PBNA_LB_ID_SHIFT							(19)
#define BMU_CMD_GET_PBNA_LB_ID_MASK								(BIT_MASK(3))
// 3rd 4Byte
// 4th 4Byte
#if PS5021_EN
#define BMU_CMD_GET_PBNA_PB_ADDR2_SHIFT							(30)
#define BMU_CMD_GET_PBNA_PB_ADDR2_MASK							(BIT_MASK(1))
#endif /* PS5021_EN */

// BMU Add Event
// 1st 4Byte
#define BMU_CMD_ADD_EVENT_OPCODE_SHIFT							(0)
#define BMU_CMD_ADD_EVENT_OPCODE_MASK							(BIT_MASK(5))
#define BMU_CMD_ADD_EVENT_TARGET_ID_SHIFT						(7)
#define BMU_CMD_ADD_EVENT_TARGET_ID_MASK						(BIT_MASK(4))
#define BMU_CMD_ADD_EVENT_OPTION_SHIFT							(11)
#define BMU_CMD_ADD_EVENT_OPTION_MASK							(BIT_MASK(1))
#define BMU_CMD_ADD_EVENT_NOCQ_SHIFT							(23)
#define BMU_CMD_ADD_EVENT_NOCQ_MASK								(BIT_MASK(1))
#define BMU_CMD_ADD_EVENT_TAG_ID_SHIFT							(24)
#define BMU_CMD_ADD_EVENT_TAG_ID_MASK							(BIT_MASK(8))
// 2nd 4Byte
#define BMU_CMD_ADD_EVENT_PB_ADDR_SHIFT							(0)
#define BMU_CMD_ADD_EVENT_PB_ADDR_MASK							(BIT_MASK(9))
// 3rd 4Byte
#define BMU_CMD_ADD_EVENT_EVENT_TARGET_ID_SHIFT					(0)
#define BMU_CMD_ADD_EVENT_EVENT_TARGET_ID_MASK					(BIT_MASK(4))
#define BMU_CMD_ADD_EVENT_EVENT_TAG_SHIFT						(16)
#define BMU_CMD_ADD_EVENT_EVENT_TAG_MASK						(BIT_MASK(8))
// 4th 4Byte
#if PS5021_EN
#define BMU_CMD_ADD_EVENT_PB_ADDR2_SHIFT						(30)
#define BMU_CMD_ADD_EVENT_PB_ADDR2_MASK							(BIT_MASK(1))
#endif /* PS5021_EN */

// BMU Delete Event
// 1st 4Byte
#define BMU_CMD_DELETE_EVENT_OPCODE_SHIFT						(0)
#define BMU_CMD_DELETE_EVENT_OPCODE_MASK						(BIT_MASK(5))
#define BMU_CMD_DELETE_EVENT_TARGET_ID_SHIFT					(7)
#define BMU_CMD_DELETE_EVENT_TARGET_ID_MASK						(BIT_MASK(4))
#define BMU_CMD_DELETE_EVENT_NOCQ_SHIFT							(23)
#define BMU_CMD_DELETE_EVENT_NOCQ_MASK							(BIT_MASK(1))
#define BMU_CMD_DELETE_EVENT_TAG_ID_SHIFT						(24)
#define BMU_CMD_DELETE_EVENT_TAG_ID_MASK						(BIT_MASK(8))
// 2nd 4Byte
// 3rd 4Byte
#define BMU_CMD_DELETE_EVENT_EVENT_TAG_SHIFT					(16)
#define BMU_CMD_DELETE_EVENT_EVENT_TAG_MASK						(BIT_MASK(8))
// 4th 4Byte

// BMU Validate Allocate LB
// 1st 4Byte
#define BMU_CMD_VALIDATE_ALLOCATE_LB_OPCODE_SHIFT				(0)
#define BMU_CMD_VALIDATE_ALLOCATE_LB_OPCODE_MASK				(BIT_MASK(5))
#define BMU_CMD_VALIDATE_ALLOCATE_LB_TARGET_ID_SHIFT			(7)
#define BMU_CMD_VALIDATE_ALLOCATE_LB_TARGET_ID_MASK				(BIT_MASK(4))
#define BMU_CMD_VALIDATE_ALLOCATE_LB_QOP_SHIFT					(12)
#define BMU_CMD_VALIDATE_ALLOCATE_LB_QOP_MASK					(BIT_MASK(1))
#define BMU_CMD_VALIDATE_ALLOCATE_LB_ZINFO_SHIFT				(13)
#define BMU_CMD_VALIDATE_ALLOCATE_LB_ZINFO_MASK					(BIT_MASK(3))
#define BMU_CMD_VALIDATE_ALLOCATE_LB_TIMEOUT_SHIFT				(16)
#define BMU_CMD_VALIDATE_ALLOCATE_LB_TIMEOUT_MASK				(BIT_MASK(4))
#define BMU_CMD_VALIDATE_ALLOCATE_LB_ZERO_SHIFT					(20)
#define BMU_CMD_VALIDATE_ALLOCATE_LB_ZERO_MASK					(BIT_MASK(1))
#define BMU_CMD_VALIDATE_ALLOCATE_LB_CMD_END_SHIFT				(21)
#define BMU_CMD_VALIDATE_ALLOCATE_LB_CMD_END_MASK				(BIT_MASK(1))
#define BMU_CMD_VALIDATE_ALLOCATE_LB_PEOP_SHIFT					(22)
#define BMU_CMD_VALIDATE_ALLOCATE_LB_PEOP_MASK					(BIT_MASK(1))
#define BMU_CMD_VALIDATE_ALLOCATE_LB_NOCQ_SHIFT					(23)
#define BMU_CMD_VALIDATE_ALLOCATE_LB_NOCQ_MASK					(BIT_MASK(1))
#define BMU_CMD_VALIDATE_ALLOCATE_LB_TAG_ID_SHIFT				(24)
#define BMU_CMD_VALIDATE_ALLOCATE_LB_TAG_ID_MASK				(BIT_MASK(8))
// 2nd 4Byte
#define BMU_CMD_VALIDATE_ALLOCATE_LB_PB_ADDR_SHIFT				(0)
#define BMU_CMD_VALIDATE_ALLOCATE_LB_PB_ADDR_MASK				(BIT_MASK(9))
#define BMU_CMD_VALIDATE_ALLOCATE_LB_LB_ID_SHIFT				(19)
#define BMU_CMD_VALIDATE_ALLOCATE_LB_LB_ID_MASK					(BIT_MASK(3))
#define BMU_CMD_VALIDATE_ALLOCATE_LB_E3D4K_FLAG_SHIFT			(22)
#define BMU_CMD_VALIDATE_ALLOCATE_LB_E3D4K_FLAG_MASK			(BIT_MASK(1))
#define BMU_CMD_VALIDATE_ALLOCATE_LB_FULL_SHIFT					(23)
#define BMU_CMD_VALIDATE_ALLOCATE_LB_FULL_MASK					(BIT_MASK(1))
#define BMU_CMD_VALIDATE_ALLOCATE_LB_VALIDBMP_SHIFT				(24)
#define BMU_CMD_VALIDATE_ALLOCATE_LB_VALIDBMP_MASK				(BIT_MASK(8))
// 3rd 4Byte
#define BMU_CMD_VALIDATE_ALLOCATE_LB_LCA_SHIFT					(0)
// 4th 4Byte
#define BMU_CMD_VALIDATE_ALLOCATE_LB_E3D4K_SHIFT				(0)
#define BMU_CMD_VALIDATE_ALLOCATE_LB_E3D4K_MASK					(BIT_MASK(24))
#define BMU_CMD_VALIDATE_ALLOCATE_LB_PCACRC_SHIFT				(24)
#define BMU_CMD_VALIDATE_ALLOCATE_LB_PCACRC_MASK				(BIT_MASK(4))
#if PS5021_EN
#define BMU_CMD_VALIDATE_ALLOCATE_LB_PB_ADDR2_SHIFT				(30)
#define BMU_CMD_VALIDATE_ALLOCATE_LB_PB_ADDR2_MASK				(BIT_MASK(1))
#endif /* PS5021_EN */

// BMU Validate Size
// 1st 4Byte
#define BMU_CMD_VALIDATE_SIZE_OPCODE_SHIFT						(0)
#define BMU_CMD_VALIDATE_SIZE_OPCODE_MASK						(BIT_MASK(5))
#define BMU_CMD_VALIDATE_SIZE_TARGET_ID_SHIFT					(7)
#define BMU_CMD_VALIDATE_SIZE_TARGET_ID_MASK					(BIT_MASK(4))
#define BMU_CMD_VALIDATE_SIZE_OPER_SHIFT						(11)
#define BMU_CMD_VALIDATE_SIZE_OPER_MASK							(BIT_MASK(2))
#define BMU_CMD_VALIDATE_SIZE_ZINFO_SHIFT						(13)
#define BMU_CMD_VALIDATE_SIZE_ZINFO_MASK						(BIT_MASK(3))

#if PS5021_EN
#define BMU_CMD_VALIDATE_SIZE_STREAM_ID1_SHIFT					(17)
#define BMU_CMD_VALIDATE_SIZE_STREAM_ID1_MASK					(BIT_MASK(1))
#endif /* PS5021_EN */
#define BMU_CMD_VALIDATE_SIZE_RCLOP_SHIFT						(18)
#define BMU_CMD_VALIDATE_SIZE_RCLOP_MASK						(BIT_MASK(1))
#define BMU_CMD_VALIDATE_SIZE_STREAM_ID0_SHIFT					(19)
#define BMU_CMD_VALIDATE_SIZE_STREAM_ID0_MASK					(BIT_MASK(1))
#define BMU_CMD_VALIDATE_SIZE_ZERO_SHIFT						(20)
#define BMU_CMD_VALIDATE_SIZE_ZERO_MASK							(BIT_MASK(1))
#define BMU_CMD_VALIDATE_SIZE_CMD_END_SHIFT						(21)
#define BMU_CMD_VALIDATE_SIZE_CMD_END_MASK						(BIT_MASK(1))
#define BMU_CMD_VALIDATE_SIZE_NOCQ_SHIFT						(23)
#define BMU_CMD_VALIDATE_SIZE_NOCQ_MASK							(BIT_MASK(1))
#define BMU_CMD_VALIDATE_SIZE_TAG_ID_SHIFT						(24)
#define BMU_CMD_VALIDATE_SIZE_TAG_ID_MASK						(BIT_MASK(8))
// 2nd 4Byte
#define BMU_CMD_VALIDATE_SIZE_PB_ADDR_SHIFT						(0)
#define BMU_CMD_VALIDATE_SIZE_PB_ADDR_MASK						(BIT_MASK(9))
#define BMU_CMD_VALIDATE_SIZE_LB_OFFSET_SHIFT					(9)
#define BMU_CMD_VALIDATE_SIZE_LB_OFFSET_MASK					(BIT_MASK(10))
#define BMU_CMD_VALIDATE_SIZE_LB_ID_SHIFT						(19)
#define BMU_CMD_VALIDATE_SIZE_LB_ID_MASK						(BIT_MASK(3))
#define BMU_CMD_VALIDATE_SIZE_FULL_SHIFT						(23)
#define BMU_CMD_VALIDATE_SIZE_FULL_MASK							(BIT_MASK(1))
#define BMU_CMD_VALIDATE_SIZE_VALIDBMP_SHIFT					(24)
#define BMU_CMD_VALIDATE_SIZE_VALIDBMP_MASK						(BIT_MASK(8))
// 3rd 4Byte
#define BMU_CMD_VALIDATE_SIZE_LCA_SHIFT							(0)
// 4th 4Byte
#define BMU_CMD_VALIDATE_SIZE_SIZE_SHIFT						(0)
#define BMU_CMD_VALIDATE_SIZE_SIZE_MASK							(BIT_MASK(8))
#if PS5021_EN
#define BMU_CMD_VALIDATE_SIZE_PB_ADDR2_SHIFT					(30)
#define BMU_CMD_VALIDATE_SIZE_PB_ADDR2_MASK						(BIT_MASK(1))
#endif /* PS5021_EN */

// BMU Reserve LB
// 1st 4Byte
#define BMU_CMD_RESERVE_LB_OPCODE_SHIFT							(0)
#define BMU_CMD_RESERVE_LB_OPCODE_MASK							(BIT_MASK(5))
#define BMU_CMD_RESERVE_LB_TARGET_ID_SHIFT						(7)
#define BMU_CMD_RESERVE_LB_TARGET_ID_MASK						(BIT_MASK(4))
#define BMU_CMD_RESERVE_LB_NOCQ_SHIFT							(23)
#define BMU_CMD_RESERVE_LB_NOCQ_MASK							(BIT_MASK(1))
#define BMU_CMD_RESERVE_LB_TAG_ID_SHIFT							(24)
#define BMU_CMD_RESERVE_LB_TAG_ID_MASK							(BIT_MASK(8))
// 2nd 4Byte
#define BMU_CMD_RESERVE_LB_LB_ID_SHIFT							(19)
#define BMU_CMD_RESERVE_LB_LB_ID_MASK							(BIT_MASK(3))
// 3rd 4Byte
// 4th 4Byte
#define BMU_CMD_RESERVE_LB_SIZE_SHIFT							(0)
#define BMU_CMD_RESERVE_LB_SIZE_MASK							(BIT_MASK(8))

// BMU Allocate PB Link
// 1st 4Byte
#define BMU_CMD_ALLOCATE_PB_LINK_OPCODE_SHIFT					(0)
#define BMU_CMD_ALLOCATE_PB_LINK_OPCODE_MASK					(BIT_MASK(5))
#define BMU_CMD_ALLOCATE_PB_LINK_TARGET_ID_SHIFT				(7)
#define BMU_CMD_ALLOCATE_PB_LINK_TARGET_ID_MASK					(BIT_MASK(4))
#define BMU_CMD_ALLOCATE_PB_LINK_OPTION_SHIFT					(11)
#define BMU_CMD_ALLOCATE_PB_LINK_OPTION_MASK					(BIT_MASK(1))
#define BMU_CMD_ALLOCATE_PB_LINK_TIMEOUT_SHIFT					(16)
#define BMU_CMD_ALLOCATE_PB_LINK_TIMEOUT_MASK					(BIT_MASK(4))
#define BMU_CMD_ALLOCATE_PB_LINK_NOCQ_SHIFT						(23)
#define BMU_CMD_ALLOCATE_PB_LINK_NOCQ_MASK						(BIT_MASK(1))
#define BMU_CMD_ALLOCATE_PB_LINK_TAG_ID_SHIFT					(24)
#define BMU_CMD_ALLOCATE_PB_LINK_TAG_ID_MASK					(BIT_MASK(8))
// 2nd 4Byte
#define BMU_CMD_ALLOCATE_PB_LINK_LB_OFFSET_SHIFT				(9)
#define BMU_CMD_ALLOCATE_PB_LINK_LB_OFFSET_MASK					(BIT_MASK(10))
#define BMU_CMD_ALLOCATE_PB_LINK_LB_ID_SHIFT					(19)
#define BMU_CMD_ALLOCATE_PB_LINK_LB_ID_MASK						(BIT_MASK(3))
#define BMU_CMD_ALLOCATE_PB_LINK_CTAG_SHIFT						(24)
#define BMU_CMD_ALLOCATE_PB_LINK_CTAG_MASK						(BIT_MASK(8))
// 3rd 4Byte
#define BMU_CMD_ALLOCATE_PB_LINK_LCA_SHIFT						(0)
// 4th 4Byte
#define BMU_CMD_ALLOCATE_PB_LINK_SIZE_SHIFT						(0)
#define BMU_CMD_ALLOCATE_PB_LINK_SIZE_MASK						(BIT_MASK(8))
#define BMU_CMD_ALLOCATE_PB_LINK_STREAM_ID_SHIFT				(8)
#define BMU_CMD_ALLOCATE_PB_LINK_STREAM_ID_MASK					(BIT_MASK(4))
#if PS5021_EN
#define BMU_CMD_ALLOCATE_PB_LINK_CTAG2_SHIFT					(31)
#define BMU_CMD_ALLOCATE_PB_LINK_CTAG2_MASK						(BIT_MASK(1))
#endif /* PS5021_EN */

// BMU WLB Search
// 1st 4Byte
#define BMU_CMD_WLB_SEARCH_OPCODE_SHIFT							(0)
#define BMU_CMD_WLB_SEARCH_OPCODE_MASK							(BIT_MASK(5))
#define BMU_CMD_WLB_SEARCH_TARGET_ID_SHIFT						(7)
#define BMU_CMD_WLB_SEARCH_TARGET_ID_MASK						(BIT_MASK(4))
#define BMU_CMD_WLB_SEARCH_OPTION_SHIFT							(11)
#define BMU_CMD_WLB_SEARCH_OPTION_MASK							(BIT_MASK(1))
#define BMU_CMD_WLB_SEARCH_NOCQ_SHIFT							(23)
#define BMU_CMD_WLB_SEARCH_NOCQ_MASK							(BIT_MASK(1))
#define BMU_CMD_WLB_SEARCH_TAG_ID_SHIFT							(24)
#define BMU_CMD_WLB_SEARCH_TAG_ID_MASK							(BIT_MASK(8))
// 2nd 4Byte
#define BMU_CMD_WLB_SEARCH_LB_OFFSET_SHIFT						(9)
#define BMU_CMD_WLB_SEARCH_LB_OFFSET_MASK						(BIT_MASK(10))
#define BMU_CMD_WLB_SEARCH_LB_ID_SHIFT							(19)
#define BMU_CMD_WLB_SEARCH_LB_ID_MASK							(BIT_MASK(3))
// 3rd 4Byte
#define BMU_CMD_WLB_SEARCH_LCA_SHIFT							(0)
// 4th 4Byte

// BMU Link LB
// 1st 4Byte
#define BMU_CMD_LINK_LB_OPCODE_SHIFT							(0)
#define BMU_CMD_LINK_LB_OPCODE_MASK								(BIT_MASK(5))
#define BMU_CMD_LINK_LB_TARGET_ID_SHIFT							(7)
#define BMU_CMD_LINK_LB_TARGET_ID_MASK							(BIT_MASK(4))
#define BMU_CMD_LINK_LB_QOP_SHIFT								(12)
#define BMU_CMD_LINK_LB_QOP_MASK								(BIT_MASK(1))
#define BMU_CMD_LINK_LB_NOCQ_SHIFT								(23)
#define BMU_CMD_LINK_LB_NOCQ_MASK								(BIT_MASK(1))
#define BMU_CMD_LINK_LB_TAG_ID_SHIFT							(24)
#define BMU_CMD_LINK_LB_TAG_ID_MASK								(BIT_MASK(8))
// 2nd 4Byte
#define BMU_CMD_LINK_LB_PB_ADDR_SHIFT							(0)
#define BMU_CMD_LINK_LB_PB_ADDR_MASK							(BIT_MASK(9))
#define BMU_CMD_LINK_LB_LB_OFFSET_SHIFT							(9)
#define BMU_CMD_LINK_LB_LB_OFFSET_MASK							(BIT_MASK(10))
#define BMU_CMD_LINK_LB_LB_ID_SHIFT								(19)
#define BMU_CMD_LINK_LB_LB_ID_MASK								(BIT_MASK(3))
// 3rd 4Byte
// 4th 4Byte
#if PS5021_EN
#define BMU_CMD_LINK_LB_PB_ADDR2_SHIFT							(30)
#define BMU_CMD_LINK_LB_PB_ADDR2_MASK							(BIT_MASK(1))
#endif /* PS5021_EN */

// BMU Update LL
// 1st 4Byte
#define BMU_CMD_UPDATE_LL_OPCODE_SHIFT							(0)
#define BMU_CMD_UPDATE_LL_OPCODE_MASK							(BIT_MASK(5))
#define BMU_CMD_UPDATE_LL_TARGET_ID_SHIFT						(7)
#define BMU_CMD_UPDATE_LL_TARGET_ID_MASK						(BIT_MASK(4))
#define BMU_CMD_UPDATE_LL_NOCQ_SHIFT							(23)
#define BMU_CMD_UPDATE_LL_NOCQ_MASK								(BIT_MASK(1))
#define BMU_CMD_UPDATE_LL_TAG_ID_SHIFT							(24)
#define BMU_CMD_UPDATE_LL_TAG_ID_MASK							(BIT_MASK(8))
// 2nd 4Byte
#define BMU_CMD_UPDATE_LL_TARGET_VALUE_SHIFT					(0)
#define BMU_CMD_UPDATE_LL_TARGET_VALUE_MASK						(BIT_MASK(9))
#define BMU_CMD_UPDATE_LL_LB_ID_SHIFT							(19)
#define BMU_CMD_UPDATE_LL_LB_ID_MASK							(BIT_MASK(3))
// 3rd 4Byte
// 4th 4Byte
#if PS5021_EN
#define BMU_CMD_UPDATE_LL_TARGET_VALUE2_SHIFT					(30)
#define BMU_CMD_UPDATE_LL_TARGET_VALUE2_MASK					(BIT_MASK(1))
#endif /* PS5021_EN */

// BMU Booking
// 1st 4Byte
#define BMU_CMD_BOOKING_OPCODE_SHIFT							(0)
#define BMU_CMD_BOOKING_OPCODE_MASK								(BIT_MASK(5))
#define BMU_CMD_BOOKING_TARGET_ID_SHIFT							(7)
#define BMU_CMD_BOOKING_TARGET_ID_MASK							(BIT_MASK(4))
#define BMU_CMD_BOOKING_OPER_SHIFT								(11)
#define BMU_CMD_BOOKING_OPER_MASK								(BIT_MASK(2))
#define BMU_CMD_BOOKING_NOCQ_SHIFT								(23)
#define BMU_CMD_BOOKING_NOCQ_MASK								(BIT_MASK(1))
#define BMU_CMD_BOOKING_TAG_ID_SHIFT							(24)
#define BMU_CMD_BOOKING_TAG_ID_MASK								(BIT_MASK(8))
// 2nd 4Byte
#define BMU_CMD_BOOKING_REQUEST_VALUE_SHIFT						(0)
#define BMU_CMD_BOOKING_REQUEST_VALUE_MASK						(BIT_MASK(9))
// 3rd 4Byte
// 4th 4Byte
#if PS5021_EN
#define BMU_CMD_BOOKING_REQUEST_VALUE2_SHIFT					(30)
#define BMU_CMD_BOOKING_REQUEST_VALUE2_MASK						(BIT_MASK(1))
#endif /* PS5021_EN */

#endif
