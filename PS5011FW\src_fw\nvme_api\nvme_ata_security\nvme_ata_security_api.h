/****************************************************************************
*
*  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
*  All rights reserved
*
*  The content of this document is confidential and shall be applied
*  subject to the terms and conditions of the license agreement and
*  other applicable laws. Any unauthorized access, use or disclosure
*  of this document is strictly prohibited and may be punishable
*  under laws.
*
*  nvme_ata_security_api.h
*
*
*
****************************************************************************/

#ifndef NVME_ATA_SECURITY_API_H_
#define NVME_ATA_SECURITY_API_H_
/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *  data types
 * ---------------------------------------------------------------------------------------------------
 */
typedef enum NVMeATASecurityCmdSpSp {	// Cannot change order
	NVME_ATA_SECURITY_CMD_SPSP_GET_STATUS,			// 0
	NVME_ATA_SECURITY_CMD_SPSP_SET_PASSWORD,		// 1
	NVME_ATA_SECURITY_CMD_SPSP_GET_UNLOCK,			// 2
	NVME_ATA_SECURITY_CMD_SPSP_GET_ERASE_PREPARE,	// 3
	NVME_ATA_SECURITY_CMD_SPSP_GET_ERASE,			// 4
	NVME_ATA_SECURITY_CMD_SPSP_GET_FREEZE_LOCK,		// 5
	NVME_ATA_SECURITY_CMD_SPSP_GET_DISABLE_PASSWORD	// 6
} NVMeATASecurityCmdSpSp_t;

typedef enum NVMeATASecurityInitMode {
	NVME_ATA_SECURITY_INIT_MODE_PREFORMAT,
	NVME_ATA_SECURITY_INIT_MODE_POWER_CYCLE,
	NVME_ATA_SECURITY_INIT_MODE_HRST,
	NVME_ATA_SECURITY_INIT_MODE_LPM3,
	NVME_ATA_SECURITY_INIT_MODE_DLMC
} NVMeATASecurityInitMode_t;

typedef enum {
	NVME_ATA_SECURITY_SEND_STATE_INIT = 0,
	NVME_ATA_SECURITY_SEND_STATE_ALLOCATE_BUF,
	NVME_ATA_SECURITY_SEND_STATE_HANDLE,
	NVME_ATA_SECURITY_SEND_STATE_ERASE_HANDLE,
	NVME_ATA_SECURITY_SEND_STATE_FREE_BUF,
	NVME_ATA_SECURITY_SEND_STATE_DONE
} NVMeATASecuritySendState_t;

typedef enum {
	NVME_ATA_SECURITY_ALLOCATE_BUF_STATE_INIT = 0,
	NVME_ATA_SECURITY_ALLOCATE_BUF_STATE_STOP_PREREAD,
	NVME_ATA_SECURITY_ALLOCATE_BUF_STATE_ADJUST_LOW_LIMIT,
	NVME_ATA_SECURITY_ALLOCATE_BUF_STATE_WAIT_ADJUST_LOW_LIMIT,
	NVME_ATA_SECURITY_ALLOCATE_BUF_STATE_ALLOCATE,
	NVME_ATA_SECURITY_ALLOCATE_BUF_STATE_SETUP_BUFFER,
	NVME_ATA_SECURITY_ALLOCATE_BUF_STATE_DONE
} NVMeATASecurityAllocateBufState_t;

typedef enum {
	NVME_ATA_SECURITY_FREE_BUF_STATE_INIT = 0,
	NVME_ATA_SECURITY_FREE_BUF_STATE_FREE,
	NVME_ATA_SECURITY_FREE_BUF_STATE_ADJUST_LOW_LIMIT,
	NVME_ATA_SECURITY_FREE_BUF_STATE_DONE
} NVMeATASecurityFreeBufState_t;

typedef struct {
	NVMeATASecuritySendState_t NVMeATASecuritySendState;
	NVMeATASecurityAllocateBufState_t NVMeATASecurityAllocateBufState;
	NVMeATASecurityFreeBufState_t NVMeATASecurityFreeBufState;
	U32 ulSHABufAddr;
	U8 btBufExist				: 1;
	U8 btNeedErase				: 1;
	U8 btNeedSaveHostTable		: 1;
	U8 btRLBLowLimitAdjust		: 1;
	U8 btInfoBlkSupport			: 1;
	U8 Reserved					: 3;
} NVMeATASecurity_t;
/*
 * ---------------------------------------------------------------------------------------------------
 *   macros
 * ---------------------------------------------------------------------------------------------------
 */
#define M_NVME_ATA_SECURITY_FLOW_ADJUST_RLB_LOW_LIMIT()	(TRUE == gNVMeATASecurity.btRLBLowLimitAdjust)
extern NVMeATASecurity_t gNVMeATASecurity;
/*
 * ---------------------------------------------------------------------------------------------------
 *	public prototypes
 * ---------------------------------------------------------------------------------------------------
 */
AOM_NVME_ATA_SECURITY void NVMeATASecurityInit(U8 ubMode);

#if (!BURNER_MODE_EN && !LPM3_LOADER)
AOM_NVME_ATA_SECURITY U8 NVMeATASecuritySendCheckStatus(OPT_HCMD_PTR pCmd);
AOM_NVME_ATA_SECURITY U8 NVMeATASecurityReceiveCheckStatus(OPT_HCMD_PTR pCmd);
AOM_NVME_ATA_SECURITY void NVMeATASecurityGetStatus(OPT_HCMD_PTR pCmd);
AOM_NVME_ATA_SECURITY void NVMeATASecuritySend(OPT_HCMD_PTR pCmd);
AOM_NVME_ATA_SECURITY U8 NVMeATASecurityAbortCmd(OPT_HCMD_PTR pCmd);
#endif /* (!BURNER_MODE_EN && !LPM3_LOADER) */

#endif // NVME_ATA_SECURITY_API_H_
