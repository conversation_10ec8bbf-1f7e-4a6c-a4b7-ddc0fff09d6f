#ifndef _VUC_SMARTRESCUE_H_
#define _VUC_SMARTRESCUE_H_

#include "aom/aom_api.h"
#include "host/VUC_handler_api.h"
#include "host/VUC_host.h"

#if (SMART_RESCUE_EN)
#define SMART_RESCUE_SPARE_ALIGN_BYTE            (BC_1KB) //In output buffer, align spare of each FWPlane with 1KB.
#define MAX_ZIP_DATA_IN_ONE_FW_PLANE	         (16) //Remark: Need to ensure gPCAInfo.ubResolution being 1.
#define SMART_RESCUE_OUTPUT_BUFFER_SIZE          (MAX_ZIP_DATA_IN_ONE_FW_PLANE * BC_4KB) //Data output size //64KB
#define SMART_RESCUE_DATA_SPARE_RATIO            (SMART_RESCUE_OUTPUT_BUFFER_SIZE / SMART_RESCUE_SPARE_ALIGN_BYTE) //64 //Output 64 times 64KB Data, then 1 time 64KB Spare.
#if (HOST_MODE == SATA)
AOM_BURNER void VUCSmartRescueS17(VUC_OPT_HCMD_PTR_t pCmd);
#endif /*(HOST_MODE == SATA)*/
#endif /*(SMART_RESCUE_EN)*/

#endif /* _VUC_SMARTRESCUE_H_ */
