#include "setup.h"
#include "typedef.h"
#include "xzip_api.h"
#include "xzip_reg.h"
#include "xzip_cmd_type.h"
#include "hal/db/db_api.h"
#include "hal/apu/apu_api.h"
#include "db_mgr/fw_tagid.h"
#include "db_mgr/fw_cmd_table.h"
#include "debug/debug.h"
#include "debug/debug_setup.h"
#include "log/log_api.h"
#if VS_SIM_EN
#include "hw_sim_macro.h"
#include "ip/XZIP/xzip.h"
#endif

#define XZIP_FW_WAIT_PCA	(1)

typedef void (* XZIPCmdDoingFunc_t)(XZIPCQRst_t *pXZIPCQRst);

#if (XZIP_EN)
static void XZIPForceAdjustLenDone_CallBack(XZIPCQRst_t *pXZIPCQRst);
U8 gubXZIPCmdDone = FALSE;
FW_CALLBACK_SECTION U32 gulXZIPForceAdjustLenDone_CallBack = (U32)XZIPForceAdjustLenDone_CallBack;
#endif /* (XZIP_EN) */

void XZIPDelegateCmd(void)
{
#if XZIP_EN
	while (!M_DB_CHECK_EMPTY(DB_XZIP_CQ)) {
		XZIPCQRst_t *pXZIPCQRst = (XZIPCQRst_t *)M_DB_GET_QBODY_PTR( DB_XZIP_CQ, gDBQueueCnt.B.uwXZIPCQCnt);
		U8 ubXZIPTagID = (U8)pXZIPCQRst->XZIPCQResultType.ubTagID;
		XZIPCmdDoingFunc_t Callback = (XZIPCmdDoingFunc_t)gpuoCmdTableMgr[XZIP_CMD_TABLE_ID][ubXZIPTagID].ulCmdFuncPTR;

		if (Callback != NULL) {
			Callback(pXZIPCQRst);
		}

		FTLPushTagPool(XZIP_TAG_POOL_ID, ubXZIPTagID);

		M_DB_TRIGGER_READ_CNT(DB_XZIP_CQ, 1);
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_XZIP_CQ, gDBQueueCnt.QueueCnt[(DB_XZIP_CQ)] ) == M_DB_GET_RPTR((DB_XZIP_CQ)));
	}
#endif/*XZIP_EN*/
}

//-------------------------------------------------------------------
// Function : XZIPInterruptEnable
// Description : XZIP has two interrupts, both are enabled by SYS (INTERRUPT_GROUP_ID_PARITY_ERROR)
// Input  : N/A
// return : N/A
//-------------------------------------------------------------------
void XZIPInterruptEnable(void)
{
	//1. when sram parity happens, interrupt to SYS
	M_SET_INT_SRAM_PARITY();
}

void XZIPRegSETPgrmRsltMiss(U8 ubSet)
{
#if VS_SIM_EN
	EnterCriticalSection(&gXZIPCSLock);
#endif

	if (ubSet) {
		M_SET_XZIP_PROGRAM_RSLT_MISS();
	}
	else {
		M_CLEAR_XZIP_PROGRAM_RSLT_MISS();
	}

#if VS_SIM_EN
	if (ubSet) {
		M_SIM_XZIP_WR(R32_XZIP_FW_CTRL_0 + FW_SET_PGRM_RSLT_MISS_SHIFT);
	}
	else {
		M_SIM_XZIP_CLEAN(R32_XZIP_FW_CTRL_0 + FW_SET_PGRM_RSLT_MISS_SHIFT);
	}
	LeaveCriticalSection(&gXZIPCSLock);
#endif
}

void XZIPRegSETReadRsltMiss(U8 ubSet)
{
#if VS_SIM_EN
	EnterCriticalSection(&gXZIPCSLock);
#endif

	if (ubSet) {
		M_SET_XZIP_READ_RSLT_MISS();
	}
	else {
		M_CLEAR_XZIP_READ_RSLT_MISS();
	}

#if VS_SIM_EN
	if (ubSet) {
		M_SIM_XZIP_WR(R32_XZIP_FW_CTRL_0 + FW_SET_READ_RSLT_MISS_SHIFT);
	}
	else {
		M_SIM_XZIP_CLEAN(R32_XZIP_FW_CTRL_0 + FW_SET_READ_RSLT_MISS_SHIFT);
	}
	LeaveCriticalSection(&gXZIPCSLock);
#endif
}

U8 XZIPLock(U8 ubXZIPIndex, U32 ulCallBackAddr, U16 uwCallBackData)
{
#if XZIP_EN
	XZIPSQCmd_t *puoXZIPSQCmdPTR = NULL;
	cmd_table_t *puoCmdTable = NULL;
	U8 ubTagID;
	if (M_DB_CHECK_FULL(DB_XZIP_SQ)) {
		return XZIP_PUSH_SQ_FAIL;
	}

	ubTagID = (U8)FTLPopTagPool(XZIP_TAG_POOL_ID);
#if USE_FW_DB
	puoXZIPSQCmdPTR = (XZIPSQCmd_t *) M_DB_GET_QBODY_PTR( DB_XZIP_SQ, gDBQueueCnt.B.uwXZIPSQCnt); //M_DB_GET_WPTR(DB_APU_CMPL_SQ);
#else
	puoXZIPSQCmdPTR = (XZIPSQCmd_t *)M_DB_GET_WPTR(DB_XZIP_SQ);
#endif
	M_CLEAR_XZIP_QBODY(puoXZIPSQCmdPTR);
	puoXZIPSQCmdPTR->XZIPLockCmd.ubOpcode		= XZIP_CMD_OPCODE_LOCK;
	puoXZIPSQCmdPTR->XZIPLockCmd.ubTagID		= ubTagID;
	puoXZIPSQCmdPTR->XZIPLockCmd.ubXZIPIndex 	= ubXZIPIndex;
	M_DB_TRIGGER_WRITE_CNT(DB_XZIP_SQ, 1);
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_XZIP_SQ, gDBQueueCnt.QueueCnt[(DB_XZIP_SQ)] ) == M_DB_GET_WPTR((DB_XZIP_SQ)));
	puoCmdTable = &gpuoCmdTableMgr[XZIP_CMD_TABLE_ID][ubTagID];
	puoCmdTable->ulCmdFuncPTR = ulCallBackAddr;
	puoCmdTable->ulData.ulAll = 0;
	puoCmdTable->ulData.uwPrivate = uwCallBackData;
#endif /* XZIP_EN */
	return XZIP_PUSH_SQ_SUCCESS;

}



void XZIPClearRegister(U32 ulCallBackAddr)
{
#if XZIP_EN
	XZIPSQCmd_t *puoXZIPSQCmdPTR = NULL;
	cmd_table_t *puoCmdTable = NULL;
	U8 ubTagID = (U8)FTLPopTagPool(XZIP_TAG_POOL_ID);

	while (M_DB_CHECK_FULL(DB_XZIP_SQ));

#if USE_FW_DB
	puoXZIPSQCmdPTR = (XZIPSQCmd_t *) M_DB_GET_QBODY_PTR( DB_XZIP_SQ, gDBQueueCnt.B.uwXZIPSQCnt); //M_DB_GET_WPTR(DB_APU_CMPL_SQ);
#else
	puoXZIPSQCmdPTR = (XZIPSQCmd_t *)M_DB_GET_WPTR(DB_XZIP_SQ);
#endif
	M_CLEAR_XZIP_QBODY(puoXZIPSQCmdPTR);
	puoXZIPSQCmdPTR->XZIPClearRegCmd.ubOpcode		= XZIP_CMD_OPCODE_CLEAR_REGISTER;
	puoXZIPSQCmdPTR->XZIPClearRegCmd.ubTagID		= ubTagID;

	M_DB_TRIGGER_WRITE_CNT(DB_XZIP_SQ, 1);
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_XZIP_SQ, gDBQueueCnt.QueueCnt[(DB_XZIP_SQ)] ) == M_DB_GET_WPTR((DB_XZIP_SQ)));
	puoCmdTable = &gpuoCmdTableMgr[XZIP_CMD_TABLE_ID][ubTagID];
	puoCmdTable->ulCmdFuncPTR = ulCallBackAddr;
	puoCmdTable->ulData.ulAll = 0;
#endif /* XZIP_EN */
}

U8 XZIPUnLock(U8 ubXZIPIndex, U32 ulCallBackAddr, U16 uwCallBackData)
{
#if XZIP_EN
	XZIPSQCmd_t *puoXZIPSQCmdPTR = NULL;
	cmd_table_t *puoCmdTable = NULL;
	U8 ubTagID;

	if (M_DB_CHECK_FULL(DB_XZIP_SQ)) {
		return XZIP_PUSH_SQ_FAIL;
	}

	if (FTLGetTagPoolNum(XZIP_TAG_POOL_ID) == 0) {
		return XZIP_PUSH_SQ_FAIL;
	}

	ubTagID = (U8)FTLPopTagPool(XZIP_TAG_POOL_ID);

#if USE_FW_DB
	puoXZIPSQCmdPTR = (XZIPSQCmd_t *) M_DB_GET_QBODY_PTR( DB_XZIP_SQ, gDBQueueCnt.B.uwXZIPSQCnt); //M_DB_GET_WPTR(DB_APU_CMPL_SQ);
#else
	puoXZIPSQCmdPTR = (XZIPSQCmd_t *)M_DB_GET_WPTR(DB_XZIP_SQ);
#endif
	M_CLEAR_XZIP_QBODY(puoXZIPSQCmdPTR);
	puoXZIPSQCmdPTR->XZIPUnLockCmd.ubOpcode    = XZIP_CMD_OPCODE_UNLOCK;
	puoXZIPSQCmdPTR->XZIPUnLockCmd.ubTagID	   = ubTagID;
	puoXZIPSQCmdPTR->XZIPUnLockCmd.ubXZIPIndex = ubXZIPIndex;

	M_DB_TRIGGER_WRITE_CNT(DB_XZIP_SQ, 1);
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_XZIP_SQ, gDBQueueCnt.QueueCnt[(DB_XZIP_SQ)] ) == M_DB_GET_WPTR((DB_XZIP_SQ)));
	puoCmdTable = &gpuoCmdTableMgr[XZIP_CMD_TABLE_ID][ubTagID];
	puoCmdTable->ulCmdFuncPTR = ulCallBackAddr;
	puoCmdTable->ulData.ulAll = 0;
	puoCmdTable->ulData.uwPrivate = uwCallBackData;
#endif /* XZIP_EN */
	return XZIP_PUSH_SQ_SUCCESS;
}

U8 XZIPSearchPCA(U32 ulPCA, U32 ulCallBackAddr, U16 uwCallBackData)
{
	XZIPSQCmd_t *puoXZIPSQCmdPTR = NULL;
	cmd_table_t *puoCmdTable = NULL;
	U8 ubTagID;

	if (M_DB_CHECK_FULL(DB_XZIP_SQ)) {
		return XZIP_PUSH_SQ_FAIL;
	}

	ubTagID = (U8)FTLPopTagPool(XZIP_TAG_POOL_ID);
	puoXZIPSQCmdPTR = (XZIPSQCmd_t *)M_DB_GET_QBODY_PTR( DB_XZIP_SQ, gDBQueueCnt.B.uwXZIPSQCnt);
	M_CLEAR_XZIP_QBODY(puoXZIPSQCmdPTR);
	puoXZIPSQCmdPTR->XZIPSearchPCACmd.ubOpcode		= XZIP_CMD_OPCODE_SEARCH_PCA;
	puoXZIPSQCmdPTR->XZIPSearchPCACmd.ubTagID		= ubTagID;
	puoXZIPSQCmdPTR->XZIPSearchPCACmd.ulPCA			= ulPCA;

	M_DB_TRIGGER_WRITE_CNT(DB_XZIP_SQ, 1);
	M_FW_ASSERT(ASSERT_HAL_OTHER_0x0700, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_XZIP_SQ, gDBQueueCnt.QueueCnt[(DB_XZIP_SQ)] ) == M_DB_GET_WPTR((DB_XZIP_SQ)));
	puoCmdTable = &gpuoCmdTableMgr[XZIP_CMD_TABLE_ID][ubTagID];
	puoCmdTable->ulCmdFuncPTR = ulCallBackAddr;
	puoCmdTable->ulData.ulAll = 0;
	puoCmdTable->ulData.uwPrivate = uwCallBackData;

	return XZIP_PUSH_SQ_SUCCESS;
}
#if XZIP_EN
U8 XZIPAdjustLen(U16 uwEntryNum, U32 ulCallBackAddr, U16 uwCallBackData)
{
	XZIPSQCmd_t *puoXZIPSQCmdPTR = NULL;
	cmd_table_t *puoCmdTable = NULL;
	U8 ubTagID;
	U16 uwLength;

	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, uwEntryNum > 0);
	uwLength = uwEntryNum - 1;
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, uwLength < XZIP_MAX_ENTRY_NUM);

	if (M_DB_CHECK_FULL(DB_XZIP_SQ)) {
		return XZIP_PUSH_SQ_FAIL;
	}
	ubTagID = (U8)FTLPopTagPool(XZIP_TAG_POOL_ID);
	puoXZIPSQCmdPTR = (XZIPSQCmd_t *)M_DB_GET_QBODY_PTR( DB_XZIP_SQ, gDBQueueCnt.B.uwXZIPSQCnt);
	M_CLEAR_XZIP_QBODY(puoXZIPSQCmdPTR);
	puoXZIPSQCmdPTR->XZIPAdjustLenCmd.ubOpcode		= XZIP_CMD_OPCODE_ADJUST_LENGTH;
	puoXZIPSQCmdPTR->XZIPAdjustLenCmd.ubTagID		= ubTagID;
	puoXZIPSQCmdPTR->XZIPAdjustLenCmd.ubAdjustLen	= (U8)uwLength;
	//	M_UART(XZIP_, "puoXZIPSQCmdPTR = %x", (U32)puoXZIPSQCmdPTR);
	M_DB_TRIGGER_WRITE_CNT(DB_XZIP_SQ, 1);
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_XZIP_SQ, gDBQueueCnt.QueueCnt[(DB_XZIP_SQ)] ) == M_DB_GET_WPTR((DB_XZIP_SQ)));
	puoCmdTable = &gpuoCmdTableMgr[XZIP_CMD_TABLE_ID][ubTagID];
	puoCmdTable->ulCmdFuncPTR = ulCallBackAddr;
	puoCmdTable->ulData.ulAll = 0;
	puoCmdTable->ulData.uwPrivate = uwCallBackData;

	if (LOG_EN) {
		LogAdd(LOG_TAG_ID_00108);
	}

	return XZIP_PUSH_SQ_SUCCESS;


}
#endif

#if (XZIP_EN)
void XZIPForceAdjustLenDone_CallBack(XZIPCQRst_t *pXZIPCQRst)
{
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, pXZIPCQRst->AdjustLengthResult.btAbort == FALSE);
	gubXZIPCmdDone = TRUE;
}
#endif /* (XZIP_EN) */


void XZIPForceAdjustLen(U16 uwEntryNum)
{
#if XZIP_EN
	while (XZIP_PUSH_SQ_SUCCESS != XZIPAdjustLen(uwEntryNum, (U32)gulXZIPForceAdjustLenDone_CallBack, 0));
	gubXZIPCmdDone = FALSE;
	while (!gubXZIPCmdDone) {
		XZIPDelegateCmd();
	}
#endif /* XZIP_EN */
}

U8 XZIPRegCheckGenInstEqualZero(void)
{
#if VS_SIM_EN
	EnterCriticalSection(&gXZIPCSLock);
	gXZIPRegs[XZIP_REGS_UPDATE_FLAG] = 1;	//let FW clear XZIP even if XZIP has entry without valid PCA.
	M_SIM_XZIP_WR(R32_XZIP_FW_CTRL_0 + GEN_INST_EQUAL_ZERO_SHIFT);
	LeaveCriticalSection(&gXZIPCSLock);
#endif /* VS_SIM_EN */

	if (R16_XZIP[R16_XZIP_FW_RESULT_0] & (GEN_INST_EQUAL_ZERO_MASK << GEN_INST_EQUAL_ZERO_SHIFT)) {
		return TRUE;	// update all PCA done
	}
	else {
		return FALSE;
	}
}

void XZIPEntryPCADiff(U32 ulCallBackAddr, U8 ubClrPCA, U8 ubClrFirstHit)
{
	XZIPSQCmd_t *puoXZIPSQCmdPTR = NULL;
	cmd_table_t *puoCmdTable = NULL;
	U8 ubTagID = (U8)FTLPopTagPool(XZIP_TAG_POOL_ID);

	while (M_DB_CHECK_FULL(DB_XZIP_SQ));

#if USE_FW_DB
	puoXZIPSQCmdPTR = (XZIPSQCmd_t *) M_DB_GET_QBODY_PTR( DB_XZIP_SQ, gDBQueueCnt.B.uwXZIPSQCnt); //M_DB_GET_WPTR(DB_APU_CMPL_SQ);
#else
	puoXZIPSQCmdPTR = (XZIPSQCmd_t *)M_DB_GET_WPTR(DB_XZIP_SQ);
#endif
	M_CLEAR_XZIP_QBODY(puoXZIPSQCmdPTR);
	puoXZIPSQCmdPTR->XZIPEntryPCADiffCmd.ubOpcode		= XZIP_CMD_OPCODE_ENTRY_PCA_DIFF;
	puoXZIPSQCmdPTR->XZIPEntryPCADiffCmd.ubTagID		= ubTagID;

	puoXZIPSQCmdPTR->XZIPEntryPCADiffCmd.btClrPCA		= ubClrPCA;
	puoXZIPSQCmdPTR->XZIPEntryPCADiffCmd.btClrFistHit		= ubClrFirstHit;
	M_DB_TRIGGER_WRITE_CNT(DB_XZIP_SQ, 1);
	M_FW_ASSERT(ASSERT_HAL_OTHER_0x0701, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_XZIP_SQ, gDBQueueCnt.QueueCnt[(DB_XZIP_SQ)] ) == M_DB_GET_WPTR((DB_XZIP_SQ)));
	puoCmdTable = &gpuoCmdTableMgr[XZIP_CMD_TABLE_ID][ubTagID];
	puoCmdTable->ulCmdFuncPTR = ulCallBackAddr;
	puoCmdTable->ulData.ulAll = 0;
}


