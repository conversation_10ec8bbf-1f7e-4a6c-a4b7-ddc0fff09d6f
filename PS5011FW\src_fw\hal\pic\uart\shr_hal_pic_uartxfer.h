#ifndef _SHR_HAL_PIC_UART_XFER_H_
#define _SHR_HAL_PIC_UART_XFER_H_

#include "setup.h"
#include "typedef.h"
#include "symbol.h"
#include "mem.h"

#define MAX_PACKET_LEN   1024
#define MAX_UART_DEVICE  24

#define UART_TX_SIZE            	(16)
#define UART_MAX_OP_SIZE			(5)

#define BUFFER_BASE_INIT           (0xFFFFFFFF)

typedef union _UART_STATUS   UART_STATUS;
union _UART_STATUS {
	U8 ubData[16 + MAX_UART_DEVICE];
	struct {
		U8 ubTag[2];//0~1
		U8 ubAddr;//2
		U8 ubFeature;//3
		U8 ubSubFeature;//4
		U8 ubPRLState;//5
		U8 ubPRLProgress;//6
		U8 ubFlowIndex;//7
		U8 ubFlowCount;//8
		U8 ubCmdError;//9
		U8 ubErrorCode;//10
		U8 ubTansferState;//11
		U32 ulRxDataCount;//12~15
		U8 ubAnormalDevice[MAX_UART_DEVICE];
	} B;
} __attribute__((packed));
TYPE_SIZE_CHECK(UART_STATUS, (16 + MAX_UART_DEVICE));

#define RFL_VERSION_MAJOR  (1)
#define RFL_VERSION_MINOR  (0)
typedef struct result_final_log_struct  RFL_LOG_STRUCT, *RFL_LOG_STRUCT_PTR;
struct result_final_log_struct { // 4096Bytes
	UART_STATUS lastStatus;//16+24
	U8 ubRFLVersion[2];//v1.0
	U64 ullCEBitMap;
	U16 uwWindowWidthR[MAX_CHANNEL][MAX_CE_PER_CHANNEL][MAX_LUN_NUM];//[CH][Bank][Die]
	U16 uwWindowWidthW[MAX_CHANNEL][MAX_CE_PER_CHANNEL][MAX_LUN_NUM];//[CH][Bank][Die]
	U16 uwEarlyBadPerPlane[MAX_CHANNEL][MAX_CE_PER_CHANNEL][MAX_LUN_NUM][8];//[CH][Bank][Die][Plane]
	U16 uwLaterBadPerPlane[MAX_CHANNEL][MAX_CE_PER_CHANNEL][MAX_LUN_NUM][8];//[CH][Bank][Die][Plane]
	U16 uwEraseAvgBusyTime[MAX_CHANNEL][MAX_CE_PER_CHANNEL][MAX_LUN_NUM][2];//[CH][Bank][Die][XLC/SLC]
	U16 uwWriteAvgBusyTime[MAX_CHANNEL][MAX_CE_PER_CHANNEL][MAX_LUN_NUM][2];//[CH][Bank][Die][XLC/SLC]

	U8 btScanWindowFail : 1;//1->fail, 0-> pass
	U8 btScanPageRegisterFail : 1;//1->fail, 0-> pass
	U8 btFlashDCCFail : 1;
	U8 btFlashZQCLFail : 1;
	U8 btRetryVerifyFail: 1;
	U8 btReserved : 3;

	U8 ubReserved[1229];
} __attribute__((packed));
TYPE_SIZE_CHECK(RFL_LOG_STRUCT, 4096);

#if (RDT_UART_TRANSFER)

typedef union {
	U32 All;
	struct {
		U32 Data 		: 8;
		U32 btIsEmpty	: 1;
		U32 Reserved	: 23;
	} B;
} Uart_Buf_s;

enum ENUM_CBW_TYPE {
	CBT_GET_STATUS = 0, //Device->Host(Get current CSW status from device)
	CBT_GET_ADDR,       //Device->Host(Get Addr by CSW)
	CBT_GET_DATA,       //Device->Host(Get DATA Direct from RAM)
	CBT_SET_DATA_BEGIN, //Host->Device(Tell device how many packet will be transfer)
	CBT_SET_DATA_ING,   //Host->Device(Set DATA Direct to RAM)
	CBT_SET_DATA_CHK,   //Host->Device(Check if packet have been received)
	CBT_SET_DATA_END,   //Host->Device(Ask device how many packet have been received)
	CBT_SET_VUC,        //Host->Device(Set VUC to gsUartVendorParam)
	CBT_SET_FORCE_STOP, //Host->Device(FW Force Stop)
	CBT_MAX_TYPE
};

enum ENUM_CSW_TYPE {
	CST_GET_STATUS = 0,      //Device->Host(Return current CSW status from device)
	CST_GET_ADDR,            //Device->Host(Return Addr by CSW)
	CST_GET_DATA,            //Device->Host(Return Data Direct from RAM)
	CST_GET_DATA_BY_VUC_READ,//Device->Host(Return Data Via VUC)
	CST_SET_DATA_CHK,        //Device->Host(Tell host tansfer check result)
	CST_SET_DATA_END,        //Device->Host(Tell host tansfer is finish)
	CST_MAX_TYPE
};

#define US_NO_ERROR           (0)
#define US_VUC_ERROR          (1)
#define US_DATA_ERROR         (2)

//CBW:Command Block Wrapper
typedef union _UART_CBW   UART_CBW;
union _UART_CBW {
	U8 ubData[16];
	struct {
		//Header
		U8 ubTag[2];    //0~1 "UB"
		U8 ubAddr;      //2
		U8 ubAddrN;     //3
		U8 ubHeaderCRC8;//4
		//Content
		U8 ubType;              //5
		U8 ubPacketCRCType;     //6
		U8 ubPacketLength_L;//7~8
		U8 ubPacketLength_H;
		U8 ubPacketOffset_LL;//9~12
		U8 ubPacketOffset_LH;
		U8 ubPacketOffset_HL;
		U8 ubPacketOffset_HH;
		U8 ubReserved[2];       //13~14
		U8 ubContentCRC8;       //15
	} B;
} __attribute__((packed));
TYPE_SIZE_CHECK(UART_CBW, 16);

//CSW:Command Status Wrapper
typedef union _UART_CSW   UART_CSW;
union _UART_CSW {
	U8 ubData[16];
	struct {
		//Header
		U8 ubTag[2];  //0~1 "US"
		U8 ubAddr;    //2
		U8 ubAddrN;   //3
		U8 ubHeaderCRC8;//4
		//Content
		U8 ubType;         //5
		U8 ubPacketCRCType;//6
		U8 ubPacketLength_L;//7~8
		U8 ubPacketLength_H;
		U8 ubPacketOffset_LL;//9~12
		U8 ubPacketOffset_LH;
		U8 ubPacketOffset_HL;
		U8 ubPacketOffset_HH;
		U8 ubCmdState;     //13
		U8 ubCmdStatus;     //14
		U8 ubContentCRC8;   //15
	} B;
} __attribute__((packed));
TYPE_SIZE_CHECK(UART_CSW, 16);

#define UART_CBW_WAITING     0
#define UART_CBW_READY       1


extern UART_STATUS gsUartStatus;
extern UART_CBW gsUartCBWRequest;
extern UART_CBW gsUartCBW;
extern UART_CSW gsUartCSW;
extern U8 gubUartCBWState;

extern void Uart_Reset();
extern U8 Uart_Rx_Data(U32 ulRevCont, U64 ulltimeout, U8 *pTable_uart);
extern U32 Uart_Tx_Data(U32 ulTransCont, U8 *pTable_trans);
extern void Uart_DelayMS(U64 ullMSec);
extern void Uart_Build_CBW(U8 ubAddr, U8 ubType, U8 ubPacketCRCType, U16 uwPacketLength, U32 ulPacketOffset);
extern void Uart_Build_CSW(U8 ubCmdStatus, U8 ubType, U8 ubPacketCRCType, U16 uwPacketLength, U32 ulPacketOffset);

extern void VUCSendDataByUartXfer(void *pBufferAddr, U32 ulsize);

#endif//end #if (UART_TRANSFER_HOST || UART_TRANSFER_DEVICE)

#endif//end #ifndef _SHR_HAL_PIC_UART_XFER_H_
