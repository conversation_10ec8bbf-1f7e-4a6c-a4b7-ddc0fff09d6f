#ifndef _BURNER_H_
#define _BURNER_H_

#include "flash_spec.h"
#include "setup.h"
#include "common/fw_common.h"
#include "burner/Burner_Def.h"
#include "nvme_api/nvme/shr_hal_nvme.h"

#if (RDT_MODE_EN)
#if (PS5017_EN)
#include "rdt/rdt_api.h"
#else
#include "rdt/rdt.h"
#endif /* (PS5017_EN) */
#endif /* (RDT_MODE_EN) */

//==============================================================================
// Define Register Base
//==============================================================================
#define FWBIN_AES_DEAL_SECTION	(4096)

// R/W Buffer
#define PROGRAM_SPARE_IRAM_OFFSET_SCAN_WINDOW	(DEF_KB(33))
#define PROGRAM_SPARE_SIZE_SCAN_WINDOW			(ENTRY_PER_PLANE * SPARE_SIZE * MAX_CHANNEL)
#define READ_SPARE_IRAM_OFFSET_SCAN_WINDOW		(DEF_KB(33) +PROGRAM_SPARE_SIZE_SCAN_WINDOW)

// 4K frame size
#define DEF_4K_FRAME			(4096)

//==============================================================================
// Define Error
//==============================================================================
#define DEF_PROG_SUCCESS		(0)
#define DEF_RET_OK				(0)
#define DEF_ERASE_FAIL			(BIT0)
#define DEF_READ_FAIL			(BIT1)
#define DEF_CODESIGN_ERROR		(BIT2)
#define DEF_INIT_FAIL			(BIT3)
#define DEF_BAD_FLASH			(BIT4)
#if (PS5017_EN)
#define DEF_PICKCODEBLOCK_FAIL	(BIT5)
#endif /* (PS5017_EN) */
#define DEF_PROG_FAIL			(BIT7)

//==============================================================================
//  FIP Error Message Map (Bit24~Bit31)
//==============================================================================
#define FIP_PFA			(BIT7)
#define FIP_MT_STOP		(BIT6)
#define FIP_STA			(BIT5)
#define FIP_EOT			(BIT4)
#define FIP_ABOURT_DONE	(BIT3)
#define FIP_DMA_DONE	(BIT2)
#define FIP_ERASE_PAGE	(BIT1)
#define FIP_ALL_DONE	(BIT0)

//==============================================================================
// PMIC & GPIO detect
//==============================================================================
#define BURNER_HW_STATUS_NORMAL			(0)
#define BURNER_LOAD_SWITCH_FAIL_BIT		(BIT(0))
#define BURNER_GPIO_SHORT_BIT			(BIT(1))
#define BURNER_TCK_BIT					(BIT(2))
#define BURNER_TMS_BIT					(BIT(3))
#define BURNER_IC_VERSION_BIT			(BIT(4))
#define BURNER_RTT_CALIBRATE_FAIL_BIT	(BIT(5))
#define BURNER_PMIC_FAIL_BIT			(BIT(6))
#define BURNER_PMU_LOAD_SWITCH_FAIL_BIT	(BIT(7))

#define BURNER_INIT_CHECK_EARLY_FINAL_BICS4_BIT         (BIT0)

typedef struct {
	U32 ulLCA;

	union {
		struct {
			U32 ulAll : 24;
			U32 ubP4K_SPRV : 8;
		};
		struct {
			U8  ubRevisionID;
			U8  ubSectionID;
			U8  ubCodeMark;
			U8  ubResv;
		} BitMap;
	} FW;

} P4KTableHigh8B_t;
TYPE_SIZE_CHECK(P4KTableHigh8B_t, 8);

typedef struct {
	U8 ubFrame;			// 1		// Page frame
	U8 ubPlane;			// 1
	U8 ubChannel;		// 1
	U8 ubFlashCE;		// 1
	U8 ubLUN;			// 1
	U8 ubRsv;			// 1
	U16 uwPage;			// 2
	U32 ulBlock;		// 4		// block per plane
	U32 ulBufBase;		// 4
	//U32 ulSprBase;		// 4
	//U32 ulBlockPerCE;	// 4
} FlashAccessInfo_t; 	// 16Byte
TYPE_SIZE_CHECK(FlashAccessInfo_t, 16);

#define	ATCM_SEC_CNT		0
#define CODEBANK_SEC_CNT	1
#define	BTCM_SEC_CNT		2
#define	ANDES_SEC_CNT		3
#define	IRAM_SEC_CNT		4

typedef struct {
	U32 ulCodeBufBase_Boot;	// 4		Code buffer base infomation from boot code
	U16 uwSectorCnt[5]; 	// 10	// 0 ATCM 1 CodeBank 2 BTCM 3 Andes 4 IRAM
	U8	ubFWSlotLoc[14];	// 14	// FW slot location[0~13]		 0: No this slot, 1: Same code block as code pointer, 2: Other code block	//14
	U16 uwFWCodePageIdx[5];	// 16	Last page index for each FW code block. use 5 as the maximum temporarily
} BootSectionInfo_t;

extern volatile U16 guwBurnerBGTag;

void Burner_Daemon(void);
U8 BurnerScanWindowFlow(void);
U8 BurnerScanPageRegister(U8 ubCheckEcc);

void BurnerPerBitTrain(void);
void BurnerResetAllCE();
void BurnerScanAllCE(void);
U8 Burner_Decrypt(U8 ubEncrypt, U32 ulByteOffset, U32 ulTotalSize);

#if (!PS5017_EN)
U8 BurnerDQDelayFindMin(U8 ubLeftAlign0, U8 ubLeftAlign1, U8 ubRightAlign0, U8 ubRightAlign1);
void BurnerSetDQDelay(void);
void BurnerPerBitTrainMappingDelay(void);
#endif /* (!PS5017_EN) */

U8 CalculateCRC32Separately(U32 ulSrcAddr, U32 ulDstAddr, U32 ulTranByteCnt, U32 ulSeed);
U8 CalcualteCRC32(U32 ulSrcAddr, U32 ulDstAddr, U32 ulTranByteCnt);
U16 FlaFastPageRule(U16 uwPageIndex);
#if (PS5017_EN)
U8 BurnerReadSetCop0SQ(FlashAccessInfo_t *pFlaInfo, P4KTableHigh8B_t *pL4KInfo);
#else /* (PS5017_EN) */
AOM_NRW_2 U8 BurnerReadSetCop0SQ(FlashAccessInfo_t *pFlaInfo, P4KTableHigh8B_t *pL4KInfo);
#endif /* (PS5017_EN) */

#endif /* _BURNER_H_ */
