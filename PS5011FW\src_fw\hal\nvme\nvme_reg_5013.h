#ifndef _NVME_REG_H_
#define _NVME_REG_H_

#include "common/math_op.h"
#include "mem.h"

#define NVME_OFFSET                                                     (0x20000)
#define NVME_REG_BASE                                                (NVME_REG_ADDRESS + NVME_OFFSET)

#define	R8_NVME								((REG8 *)NVME_REG_BASE)
#define	R16_NVME								((REG16 *)NVME_REG_BASE)
#define	R32_NVME								((REG32 *)NVME_REG_BASE)
#define	R64_NVME								((REG64 *)NVME_REG_BASE)

/*======================== NVME Layer Status ================================*/
#define	R32_NVME_NL_STS							(0x00000000 >> 2)
#define R8_NVME_NL_STS                          (0x00000000)
#define		NL_INT_CE_CHG_BIT					(BIT0)
#define		NL_INT_SHN_CHG_BIT					(BIT1)
#define		NL_CE_ST_BIT						(BIT2)
#define		NL_SHN_ST_SHIFT						(3)
#define		NL_SHN_ST_MASK						(BIT_MASK(2))
#define		NL_INT_DB_WP_BIT					(BIT5)
#define		NL_INT_NSSR_BIT						(BIT6)
#define		NL_INT_BPR_BIT						(BIT7)

//#define     NVME_CLR_CMGR_LOCK_INT()            (R32_CMGR[R32_NVME_NL_STS] = NL_INT_CE_CHG_BIT)

#define	R32_NVME_NL_AXI_ESTS					(0x00000004 >> 2)
#define		NL_INT_W_ABN_ADDR_BIT				(BIT0)
#define		NL_INT_HST_W_DERR_BIT				(BIT1)
#define		NL_INT_CMD_R_PERR_BIT				(BIT2)
#define		NL_INT_CMD_R_RERR_BIT				(BIT3)
#define		NL_INT_CPL_WERR_BIT					(BIT4)
#define		NL_INT_MSIX_WERR_BIT				(BIT5)
#define		NL_INT_SRAM_PAR_ERR_BIT				(BIT6)

#define	R16_NL_INVLD_SQDB_VAL					(0x00000008 >> 1)
#define	R16_NL_INVLD_CQDB_VAL					(0x0000000A >> 1)
#define	R16_NL_INVLD_SQDB_REG					(0x0000000C >> 1)
#define	R16_NL_INVLD_CQDB_REG					(0x0000000E >> 1)

#define	R32_NVME_NL_INT_EN						(0x00000010 >> 2)
#define		NL_INT_CC_EN_BIT					(BIT0)
#define		NL_INT_SHN_EN_BIT					(BIT1)
#define		NL_INT_DB_WP_EN_BIT					(BIT2)
#define		NL_INT_NSSR_EN_BIT					(BIT3)
#define		NL_INT_BPR_EN_BIT					(BIT4)
#define		NL_INT_ABN_EN_BIT					(BIT8)
#define		NL_INT_HST_AXI_EN_BIT				(BIT9)
#define		NL_INT_AXIR_EN_BIT					(BIT10)
#define		NL_INT_CPL_EN_BIT					(BIT11)
#define		NL_INT_MSIX_EN_BIT					(BIT12)
#define		NL_INT_SRAM_PERR_EN					(BIT13)
#define		NL_INT_SQDB_VAL_EN_BIT				(BIT16)
#define		NL_INT_CQDB_VAL_EN_BIT				(BIT17)
#define		NL_INT_SQDB_REG_EN_BIT				(BIT18)
#define		NL_INT_CQDB_REG_EN_BIT				(BIT19)

#define	R64_NVME_NL_ABN_ADDR					(0x00000020 >> 3)
#define	R8_NVME_NL_SRAM_ERR_ADDR				(0x00000028)
#define		NL_SRAM_ERR_ADDR_SHIFT				(0)
#define		NL_SRAM_ERR_ADDR_MASK				(BIT_MASK(6))
/*====================== END: NVME Layer Status =============================*/

/*======================== NVME Layer Control ================================*/
#define	R32_NVME_NL_CTRL						(0x00000040 >> 2)
#define		NL_FW_RST_BIT						(BIT0)
#define		NL_BP_LAST_TRG_BIT					(BIT8)
#define		NL_WL_MASK_EN_BIT					(BIT9)
#define		NL_SRAM_PAR_CHK_EN_BIT				(BIT10)
#define		NL_URG_MASK_EN_BIT					(BIT11)
#define		NL_SRAM_ERR_INJ_BIT					(BIT16)
#define		NL_CPL_FLUSH_BIT					(BIT24)

#define	R32_NVME_NL_ARB							(0x00000044 >> 2)
#define	R8_NVME_NL_ARB							(0x00000044)
#define		NL_ARB_BURST_SHIFT					(0)
#define		NL_ARB_BURST_MASK					(BIT_MASK(3))
#define		NL_1ST_ADM_BIT						(BIT3)
#define		NL_CMDBUF_THR_SHIFT					(4)
#define		NL_CMDBUF_THR_MASK					(BIT_MASK(3))
#define     NL_WGT_ALL_SHIFT                    (8)
#define     NL_WGT_ALL_MASK                     (BIT_MASK(24))
#define	R8_NL_ARB_WGT_LO						(0x00000045)
#define	R8_NL_ARB_WGT_MED						(0x00000046)
#define	R8_NL_ARB_WGT_HI						(0x00000047)

#define	R16_NVME_NL_IDLE_ST						(0x00000048 >> 1)
#define		NL_CMDBUF_EMPTY_BIT					(BIT0)
#define		NL_AXIS_IDLE_BIT					(BIT1)
#define		NL_CPU_IDLE_BIT						(BIT2)
#define		NL_SRAM_IDLE_BIT					(BIT3)
#define		NL_ACQ_FULL_BIT						(BIT4)
#define		NFE_IDLE_BIT						(BIT8)
#define		NFE_NO_OUTSTD_BIT					(BIT9)
#define		NFE_NO_CMD_BIT						(BIT10)

#define	R16_NVME_NL_SQ_MASK_ST					(0x0000004A >> 1)   // r16
#define		NL_SQ_MASK_STS_SHIFT				(0)
#define		NL_SQ_MASK_STS_MASK					(BIT_MASK(9))

#define	R32_NVME_NL_WLL_THR						(0x0000004C >> 2)
#define	R32_NVME_NL_WLR_THR						(0x00000050 >> 2)
#define	R16_NVME_NL_SQ_MASK						(0x00000054 >> 1)   //r16
#define		NVME_NL_SQ_MASK_SHIFT				(0)
#define		NVME_NL_SQ_MASK_MASK				(BIT_MASK(9))

#define	R16_NVME_NL_INT_COA_DIS					(0x00000056 >> 1)   //r16
#define		NL_IC_DIS_SHIFT						(0)
#define		NL_IC_DIS_MASK						(BIT_MASK(9))

#define	R8_NVME_NL_INT_COA_CTRL_THR				(0x00000058)
#define		NL_INT_COA_THR_MASK					(BIT_MASK(8))
#define	R8_NVME_NL_INT_COA_CTRL_TIME		    (0x00000059)
#define		NL_INT_COA_TIME_MASK				(BIT_MASK(8))
#define	R16_NVME_NL_INT_COA_CTRL_UNIT	    	(0x0000005A >> 1)
#define		NL_INT_COA_UNIT_MASK				(BIT_MASK(16))

#define	R32_NVME_NL_SRAM_CTRL					(0x0000005C >> 2)
#define		NL_SRAM_INIT_BIT					(BIT0)
#define		NL_SRAM_LS_THR_SHIFT				(8)
#define		NL_SRAM_LS_THR_MASK					(BIT_MASK(8))
#define		NL_SRAM_CLK_GAT_THR_SHIFT			(16)
#define		NL_SRAM_CLK_GAT_THR_MASk			(BIT_MASK(8))
#define		NL_SRAM_LOCK_BIT					(BIT24)

/*====================== END: NVME Layer Control =============================*/

/*======================== NVME Controller Cap and Config ====================*/
#define	R32_NVME_NL_CAP0						(0x00000080 >> 2)
#define		NL_CAP_MQES_SHIFT					(0)
#define		NL_CAP_MQES_MASK					(BIT_MASK(16))
#define     NL_CAP_CQR_SHIFT                    (16)
#define		NL_CAP_CQR_BIT						(BIT16)
#define		NL_CAP_AMS_SHIFT					(17)
#define		NL_CAP_AMS_MASK						(BIT_MASK(2))
#define		NL_CAP_TO_SHIFT						(24)
#define		NL_CAP_TO_MASK						(BIT_MASK(8))

#define	R32_NVME_NL_CAP1						(0x00000084 >> 2)
#define		NL_CAP_DSTRD_SHIFT					(0)
#define		NL_CAP_DSTRD_MASK					(BIT_MASK(4))
#define		NL_CAP_NSSRS_BIT					(BIT4)
#define		NL_CAP_CSS_SHIFT					(5)
#define		NL_CAP_CSS_MASK						(BIT_MASK(8))
#define		NL_CAP_BPS_BIT						(BIT13)
#define		NL_CAP_MPSMIN_SHIFT					(16)
#define		NL_CAP_MPSMIN_MASK					(BIT_MASK(4))
#define		NL_CAP_MPSMAX_SHIFT					(20)
#define		NL_CAP_MPSMAX_MASK					(BIT_MASK(4))

#define	R32_NVME_NL_VS							(0x00000088 >> 2)
#define	R32_NVME_NL_INTM						(0x0000008C >> 2)
#define	R32_NVME_NL_CC							(0x00000090 >> 2)
#define		NL_CC_EN_BIT						(BIT0)
#define		NL_CC_CSS_SHIFT						(4)
#define		NL_CC_CSS_MASK						(BIT_MASK(3))
#define		NL_CC_MPS_SHIFT						(7)
#define		NL_CC_MPS_MASK						(BIT_MASK(4))
#define		NL_CC_AMS_SHIFT						(11)
#define		NL_CC_AMS_MASK						(BIT_MASK(3))
#define		NL_CC_SNH_SHIFT						(14)
#define		NL_CC_SNH_MASK						(BIT_MASK(2))
#define		NL_CC_IOSQES_SHIFT					(16)
#define		NL_CC_IOSQES_MASK					(BIT_MASK(4))
#define		NL_CC_IOCQES_SHIFT					(20)
#define		NL_CC_IOCQES_MASK					(BIT_MASK(4))

#define	R32_NVME_NL_CSTS						(0x00000094 >> 2)
#define		NL_CSTS_RDY_BIT						(BIT0)
#define     NL_CSTS_CFS_SHIFT                   (1)
#define		NL_CSTS_CFS_BIT						(BIT1)
#define		NL_CSTS_SHST_SHIFT					(2)
#define		NL_CSTS_SHST_MASK					(BIT_MASK(2))
#define     NL_CSTS_NSSRO_SHIFT                 (4)
#define		NL_CSTS_NSSRO_BIT					(BIT4)
#define     NL_CSTS_PP_SHIFT                    (5)
#define		NL_CSTS_PP_BIT						(BIT5)

#define CHECK_NL_CSTS_RDY_BIT()					(R32_NVME[R32_NVME_NL_CSTS] & NL_CSTS_RDY_BIT)

#define	R32_NVME_NL_BPINFO						(0x000000A0 >> 2)
#define	R32_NVME_NL_BPRSEL						(0x000000A4 >> 2)
#define	R32_NVME_NL_BPMBL						(0x000000A8 >> 2)
/*====================== END: NVME Controller Cap and Config =================*/

/*======================== NVME Layer Complete Information ======================*/
#define	R32_NVME_NL_CPL_STS						(0x000000C0 >> 2)
#define		NL_CPL_CID_SHIFT					(0)
#define		NL_CPL_CID_MASK						(BIT_MASK(16))
#define		NL_CPL_ST_SHIFT						(16)
#define		NL_CPL_ST_MASK						(BIT_MASK(15))
#define     NL_CPL_PUSH_SHIFT                   (31)
#define		NL_CPL_PUSH_BIT						(BIT31)

#define	R32_NVME_NL_CPL_SPC						(0x000000C4 >> 2)
#define		NL_CPL_SPC_SHIFT					(0)
#define		NL_CPL_SPC_MASK						(BIT_MASK(32))

#define	R32_NVME_NL_CPL_INFO					(0x000000C8 >> 2)
#define		NL_CPL_NLB_SHIFT					(0)
#define		NL_CPL_NLB_MASK						(BIT_MASK(16))
#define		NL_CPL_LBA4K_BIT					(BIT16)
#define		NL_CPL_SQID_SHIFT					(17)
#define		NL_CPL_SQID_MASK					(BIT_MASK(4))
#define		NL_CPL_IOCMD_BIT					(BIT21)

/*====================== END: NVME Layer Complete Information ===================*/

/*======================== NVME Layer Queue Information ======================*/

#define	NVME_NL_QINFO_SIZE						(0x40)
#define 	NVME_NL_QINFO_CNT                        (8)
#define	NVME_NL_QINFO_OFFSET					(0x100)

#define 	NVME_NL_QINFO_SQSIZE                     (8)
#define 	NVME_NL_QINFO_CQSIZE                     (8)
#define	R64_NVME_NL_QINFO						((volatile U64 (*)[NVME_NL_QINFO_SIZE >> 3])(NVME_REG_BASE + NVME_NL_QINFO_OFFSET))
// offset minus 0x100
//#define	NVME_NL_SQ_DESP0_OFFSET					(0x000 >> 3)
#define	R64_NVME_NL_SQ_DESP0_LO_OFFSET			(0x000 >> 3)
#define		NL_SQ_EN_BIT						(BIT0)
#define		NL_SQ_PRI_SHIFT						(1)
#define		NL_SQ_PRI_MASK						(BIT_MASK(2))
#define		NL_SQ_BASE_SHIFT					(12)
#define		NL_SQ_BASE_MASK						(BIT_MASK64(52))
#define	R64_NVME_NL_SQ_DESP0_HI_OFFSET			(0x008 >> 3)
#define		NL_SQ_CQID_SHIFT					(0)
#define		NL_SQ_CQID_MASK						(BIT_MASK(4))
#define		NL_SQ_BRC_SHIFT						(16)
#define		NL_SQ_BRC_MASK						(BIT_MASK(16))

//#define	NVME_NL_SQ_DESP1_OFFSET					(0x010 >> 3)
#define	R64_NVME_NL_SQ_DESP1_LO_OFFSET			(0x010 >> 3)
#define		NL_SQ_WL_SHIFT						(0)
#define		NL_SQ_WL_MASK						(BIT_MASK(32))
#define		NL_SQ_HEAD_SHIFT					(32)
#define		NL_SQ_HEAD_MASK						(BIT_MASK(16))
#define		NL_SQ_SIZE_SHIFT					(48)
#define		NL_SQ_SIZE_MASK						(BIT_MASK(16))
#define	R64_NVME_NL_SQ_DESP1_HI_OFFSET			(0x018 >> 3)
#define		NL_SQ_TAIL_SHIFT					(0)
#define		NL_SQ_TAIL_MASK						(BIT_MASK(16))
#define		NL_SQ_WHEAD_SHIFT					(16)
#define		NL_SQ_WHEAD_MASK					(BIT_MASK(16))

//#define	NVME_NL_CQ_DESP0_OFFSET					(0x020 >> 3)
#define	R64_NVME_NL_CQ_DESP0_LO_OFFSET			(0x020 >> 3)
#define		NL_CQ_EN_BIT						(BIT0)
#define		NL_CQ_IE_BIT						(BIT1)
#define		NL_CQ_BASE_SHIFT					(12)
#define		NL_CQ_BASE_MASK						(BIT_MASK64(52))
#define	R64_NVME_NL_CQ_DESP0_HI_OFFSET			(0x028 >> 3)
#define		NL_CQ_IV_SHIFT						(0)
#define		NL_CQ_IV_MASK						(BIT_MASK(4))

//#define	NVME_NL_CQ_DESP1_OFFSET					(0x030 >> 3)
#define	R64_NVME_NL_CQ_DESP1_LO_OFFSET			(0x030 >> 3)
#define		NL_CQ_FULL_BIT						(BIT1)
#define		NL_CQ_P_BIT							(BIT2)
#define		NL_CQ_WTAIL_SHIFT					(32)
#define		NL_CQ_WTAIL_MASK					(BIT_MASK(16))
#define		NL_CQ_SIZE_SHIFT					(48)
#define		NL_CQ_SIZE_MASK						(BIT_MASK(16))
#define	R64_NVME_NL_CQ_DESP1_HI_OFFSET			(0x038 >> 3)
#define		NL_CQ_TAIL_SHIFT					(0)
#define		NL_CQ_TAIL_MASK						(BIT_MASK(16))
#define		NL_CQ_HEAD_SHIFT					(16)
#define		NL_CQ_HEAD_MASK						(BIT_MASK(16))

/*====================== END: NVME Layer Queue Information ===================*/

/*=============================== NVME MSIX Table ===========================*/
#define	NVME_NL_MSIX_TABLE_SIZE					(0x10)
#define	NVME_NL_MSIX_TABLE_OFFSET				(0x400)
#define	R64_NVME_NL_MSIX_TABLE					((volatile U64 (*)[NVME_NL_MSIX_TABLE_SIZE >> 3])(NVME_REG_BASE + NVME_NL_MSIX_TABLE_OFFSET))
#define	R64_NVME_NL_MSIX_TABLE_LO_OFFSET		(0x000 >> 3)
#define		NL_MSIX_MSG_ADDR_SHIFT				(0)
#define		NL_MSIX_MSG_ADDR_MASK				(BIT_MASK64(64))
#define	R64_NVME_NL_MSIX_TABLE_HI_OFFSET		(0x008 >> 3)
#define		NL_MSIX_MSG_DATA_SHIFT				(0)
#define		NL_MSIX_MSG_DATA_MASK				(BIT_MASK(32))
#define		NL_MSIX_MASK_BIT					(BIT32)

/*============================= END: NVME MSIX Table ========================*/

#endif /* _NVME_REG_H_ */
