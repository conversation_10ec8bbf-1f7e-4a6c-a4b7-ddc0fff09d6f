#ifndef _TCG_CONF_H_
#define _TCG_CONF_H_
#include "setup.h"
#include "env.h"

#if MICRON_FSP_EN
#define DEBUG_UART_TCG_INFO         (1 && (!PERFORMANCE_TEST_EN) && (!RELEASED_FW) && (!IM_N48R))
#define DEBUG_UART_TCG_ERROR        (1 && (!PERFORMANCE_TEST_EN) && (!RELEASED_FW) && (!IM_N48R))
#define DEBUG_UART_TCG_METHOD       (1 && (!PERFORMANCE_TEST_EN) && (!RELEASED_FW) && (!IM_N48R))
#define DEBUG_UART_TCG_EVENT		(1 && (!PERFORMANCE_TEST_EN) && (!RELEASED_FW) && (!IM_N48R))
#else /* MICRON_FSP_EN*/
#define DEBUG_UART_TCG_INFO         (1 && (!PERFORMANCE_TEST_EN) && (!RELEASED_FW))
#define DEBUG_UART_TCG_ERROR        (1 && (!PERFORMANCE_TEST_EN) && (!RELEASED_FW))
#define DEBUG_UART_TCG_METHOD       (1 && (!PERFORMANCE_TEST_EN) && (!RELEASED_FW))
#define DEBUG_UART_TCG_EVENT		(1 && (!PERFORMANCE_TEST_EN) && (!RELEASED_FW))
#endif /* MICRON_FSP_EN*/

// Security key source
#define TCG_SECURITY_KEY_CHIP_ID (0)
#define TCG_SECURITY_KEY_EFUSE   (1)
#define TCG_SECURITY_KEY_SELECT (TCG_SECURITY_KEY_CHIP_ID)

#define TCG_DATA_STORE_BYTE_TABLE_SECURITY_KEY_PROTECT (MICRON_FSP_EN)
#define TCG_MBR_BYTE_TABLE_SECURITY_KEY_PROTECT (FALSE)
#define TCG_OBJECT_TABLE_SECURITY_KEY_PROTECT (MICRON_FSP_EN)
#define TCG_CREDENTIAL_SECURITY_KEY_HMAC_PROTECT (MICRON_FSP_EN)
#define TCG_OBJECT_TABLE_INTEGRITY_CHECK (MICRON_FSP_EN)
#define TCG_WINMAGIC_WORKAROUND (TRUE)
#define TCG_SUPPORT_OVERWRITE_ERASE (FALSE)

// -------------- DEBUG SETUP -------------- //
#define TCG_DEBUG_VERIFY_INIT_FLOW (TRUE && (!PERFORMANCE_TEST_EN) && (!RELEASED_FW))
#define TCG_DEBUG_RANDOM_TABLE_DAMAGED (FALSE)
#define TCG_DEBUG_TABLE_DAMAGED_RATE (64) /* Damaged rate: 1/x */

#endif /* _TCG_CONF_H_ */
