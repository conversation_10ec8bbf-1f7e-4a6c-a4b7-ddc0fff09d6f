#include "hal/pic/uart/uart_api.h"
#include "nvme_api/nvme/shr_hal_nvme.h"
#include "vuc/VUC_ReadSram.h"
#include "host/VUC_handler.h"

void VUC_ReadSram(VUC_OPT_HCMD_PTR_t pCmd)
{
	M_UART(VUC_, "\nVUC_READ_SRAM");

	memcpy((void *)gulVUCBufAddr, (void *)(pCmd->vuc_sqcmd.vendor.ReadWriteSram.ulRamAddr), DEF_4B * pCmd->vuc_sqcmd.vendor.ReadWriteSram.ulLength);

	M_UART(VUC_, "\nSrc[%l] to Buf[%l] Len %l", (pCmd->vuc_sqcmd.vendor.ReadWriteSram.ulRamAddr), gulVUCBufAddr, DEF_4B * pCmd->vuc_sqcmd.vendor.ReadWriteSram.ulLength);
}
