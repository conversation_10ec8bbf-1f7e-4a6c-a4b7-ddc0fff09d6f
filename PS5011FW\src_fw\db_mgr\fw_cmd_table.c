#include "setup.h"
#include "typedef.h"
#include "ftl/ftl.h"
#include "db_mgr/fw_tagid.h"
#include "debug/debug.h"
#include "db_mgr/fw_cmd_table.h"

cmd_table_t guoCOP0CmdTable[CMD_TABLE_SIZE];
cmd_table_t guoCOP1CmdTable[CMD_TABLE_SIZE];
cmd_table_t guoBMUCmdTable[CMD_TABLE_SIZE];
cmd_table_t guoDMACCmdTable[CMD_TABLE_SIZE];
cmd_table_t guoXZIPCmdTable[CMD_TABLE_SIZE];

cmd_table_t *gpuoCmdTableMgr[CMD_TABLE_ID_NUM] = {guoCOP0CmdTable,
		guoCOP1CmdTable,
		guoBMUCmdTable,
		guoDMACCmdTable,
		guoXZIPCmdTable
	};

const cmd_table_t guoDefaultCmdTableValue = {0};

