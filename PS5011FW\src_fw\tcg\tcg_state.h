#ifndef _TCG_STATE_H_
#define _TCG_STATE_H_

typedef enum TcgInitState {
	TcgInitState_Initial,
	TcgInitState_AllocateManagersAndTcgVTBuf,
	TcgInitState_ValidState,
	TcgInitState_GetSecurityKey,
	TcgInitState_LoadWriteProtectTCGTable,
	TcgInitState_LoadOpalVT,
	TcgInitState_RestoreSp,
	TcgInitState_UpdateSecurityVersion,
	TcgInitState_ForceSetSupportedFeature,
	TcgInitState_SetLockingRangeRegitser,
	TcgInitState_KATTest,
	TcgInitState_InitFlagAndRandomNumberGenerator,
	TcgInitState_LoadOPALTable,
	TcgInitState_GetNonVolatileTcgVT,
	TcgInitState_ResetAdminTries,
	TcgInitState_GetLifeCycle,
	TcgInitState_MBRDoneOnReset,
	TcgInitState_ResetLockingTries,
	TcgInitState_LockingLockOnReset,
	TcgInitState_DecryptUnlockDek,
	TcgInitState_GenD2HKey,
	TcgInitState_GenAllDek,
	TcgInitState_Pyrite1SecurityKeyProtectDek_Encrypt,
	TcgInitState_GenKek,
	TcgInitState_GenAllKekRange,
	TcgInitState_EncryptAllDektoEdekRange,
	TcgInitState_EncryptAllKekRangetoEkekRange,
	TcgInitState_EncryptAllDektoEdek,
	TcgInitState_EncryptKekAndPassword,
	TcgInitState_SetgpVT,
	TcgInitState_GenSalt,
	TcgInitState_AdminTableInit,
	TcgInitState_LockingTableInit,
	TcgInitState_ComIDPropInit,
	TcgInitState_TperPropInit,
	TcgInitState_HostPropInit,
	TcgInitState_SessionPropInit,
	TcgInitState_ClearBlockSIDFlag,
	TcgInitState_ClearHRSTFlag,
	TcgInitState_InitTest,
	TcgInitState_SaveTable,
	TcgInitState_InvalidState,
	TcgInitState_ClearTcgVTKey,
	TcgInitState_IntegritySHA,
	TcgInitState_EncryptTable,
	TcgInitState_SetInitInfoVTSaveTcg,
	TcgInitState_FreeManagersAndTcgVTBuf,
	TcgInitState_SaveOpalVT,
	TcgInitState_SetMBRWithPowerOnResetEffect,
	TcgInitState_Pyrite1SecurityKeyProtectDek_Decrypt,
	TcgInitState_SetLockingRangeWithPowerOnResetEffect,
	TcgInitState_Done,
} TcgInitState_t;

typedef enum OpalTableState {
	OpalTableState_Initial,
	OpalTableState_Idle,
	OpalTableState_Load,
	OpalTableState_Unload,
	OpalTableState_Save,
	OpalTableState_Wait,
	OpalTableState_AES,
	OpalTableState_IntegrityCheck,
} OpalTableState_t;

typedef enum TcgRepairTableState {
	TcgRepairTableState_Initial,
	TcgRepairTableState_LoadSpare,
	TcgRepairTableState_SaveOriginal,
	TcgRepairTableState_Done,
} TcgRepairTableState_t;

typedef enum SaveOpalVTState {
	SaveOpalVTState_Initial,
	SaveOpalVTState_Save,
	SaveOpalVTState_Done,
} SaveOpalVTState_t;

typedef enum LoadOpalVTState {
	LoadOpalVTState_Initial,
	LoadOpalVTState_Load,
	LoadOpalVTState_Done,
} LoadOpalVTState_t;

typedef enum SecuritySendState {
	SecuritySendState_Initial,
	SecuritySendState_InitTcg,
	SecuritySendState_AllocateManagersAndTcgVTBuf,
	SecuritySendState_AllocateTcgPayLoadBuf,
	SecuritySendState_FetchAllPayload,
	SecuritySendState_LoadOpalVT,
	SecuritySendState_ValidState,
	SecuritySendState_GetSecurityKey,
	SecuritySendState_LoadWriteProtectTCGTable,
	SecuritySendState_InitStatusAndHostCmd,
	SecuritySendState_OpalProcess,
	SecuritySendState_InvalidState,
	SecuritySendState_SaveOpalVT,
	SecuritySendState_FreeManagersAndTcgVTBuf,
	SecuritySendState_Done,
} TcgSecuritySendState_t;

typedef enum SecuritySendSecurityProtocol2State {
	SecuritySendSecurityProtocol2State_Initial,
	SecuritySendSecurityProtocol2State_BlockSid,
	SecuritySendSecurityProtocol2State_HandleComIDReq,
	SecuritySendSecurityProtocol2State_TperReset,
	SecuritySendSecurityProtocol2State_Done,
} SecuritySendSecurityProtocol2State_t;

typedef enum RestoreSpState {
	RestoreSpState_Initial,
	RestoreSpState_TriggerLoadAuthority,
	RestoreSpState_GetAdminAuthenticatedArray,
	RestoreSpState_TriggerUnloadAuthority,
	RestoreSpState_TriggerLoadCPin,
	RestoreSpState_GetTriesArray,
	RestoreSpState_TriggerUnloadCPin,
	RestoreSpState_TriggerLoadSpareAuthority,
	RestoreSpState_SetAdminAuthenticatedArray,
	RestoreSpState_TriggerSaveSpareAuthority,
	RestoreSpState_TriggerLoadSpareCPin,
	RestoreSpState_SetTriesArray,
	RestoreSpState_TriggerSaveSpareCPin,
	RestoreSpState_SyncSP,
	RestoreSpState_ForceSetSupportedFeature,
	RestoreSpState_ClearFlag,
	RestoreSpState_Done,
} RestoreSpState_t;

typedef enum SessionInitState {
	SessionInitState_Initial,
	SessionInitState_PropertyInitial,
	SessionIniState_TriggerLoadAdminAuthority,
	SessionIniState_ClearAdminAuthority,
	SessionIniState_TriggerSaveAdminAuthority,
	SessionIniState_TriggerLoadLockingAuthority,
	SessionIniState_ClearLockingAuthority,
	SessionIniState_TriggerSaveLockingAuthority,
	SessionInitState_Done,
} SessionInitState_t;

typedef enum SecuritySendSecurityProtocol1State {
	SecuritySendSecurityProtocol1State_Initial,
	SecuritySendSecurityProtocol1State_CheckTimeOut,
	SecuritySendSecurityProtocol1State_CheckComID,
	SecuritySendSecurityProtocol1State_decode,
	SecuritySendSecurityProtocol1State_SwitchCmdSeq,
	SecuritySendSecurityProtocol1State_Done,
} SecuritySendSecurityProtocol1State_t;

typedef enum SessionCheckTimeOutState {
	SessionCheckTimeOutState_Initial,
	SessionCheckTimeOutState_CheckWaitTime,
	SessionCheckTimeOutState_RestoreSp,
	SessionCheckTimeOutState_SessionPropertyInit,
	SessionCheckTimeOutState_ResetSessionTimer,
	SessionCheckTimeOutState_Done,
} SessionCheckTimeOutState_t;

typedef enum EndTransactionState {
	EndTransactionState_Initial,
	EndTransactionState_CheckTransactionStatus,
	EndTransactionState_GetUnlockedRange,
	EndTransactionState_CheckDecryptContinue,
	EndTransactionState_DecryptUnlockedRange,
	EndTransactionState_SetLockingRangeAndKey,
	EndTransactionState_SaveTable,
	EndTransactionState_RestoreTable,
	EndTransactionState_FillBuffer,
	EndTransactionState_SetBufferHeader,
	EndTransactionState_SetTansactionProperty,
	EndTransactionState_CloseSession,
	EndTransactionState_Done,
} EndTransactionState_t;

typedef enum CheckUnlockedRangeState {
	CheckUnlockedRangeState_Initial,
	CheckUnlockedRangeState_LoadLockingTable,
	CheckUnlockedRangeState_MakeUnlockedRangeBMP,
	CheckUnlockedRangeState_UnloadLockingTable,
	CheckUnlockedRangeState_Done,
} CheckUnlockedRangeState_t;

typedef enum SetBufferHeaderState {
	SetBufferHeaderState_Initial,
	SetBufferHeaderState_CheckBufferDataLength,
	SetBufferHeaderState_CheckTransaction,
	SetBufferHeaderState_RestoreSp,
	SetBufferHeaderState_CloseSessionFillBuffer,
	SetBufferHeaderState_SessionPropertyInit,
	SetBufferHeaderState_SetHeader,
	SetBufferHeaderState_Done,
} SetBufferHeaderState_t;

typedef enum CloseSessionState {
	CloseSessionState_Initial,
	CloseSessionState_RestoreSp,
	CloseSessionState_FillBuffer,
	CloseSessionState_SetBufferHeader,
	CloseSessionState_SessionPropertyInit,
	CloseSessionState_Done,
} CloseSessionState_t;

typedef enum StartTransactionState {
	StartTransactionState_Initial,
	StartTransactionState_CloseSession,
	StartTransactionState_SetBufferHeader,
	StartTransactionState_Done,
} StartTransactionState_t;

typedef enum SaveTableState {
	SaveTableState_Initial,
	SaveTableState_SpTable,
	SaveTableState_MBRTable,
	SaveTableState_DataStoreTable,
	SaveTableState_SaveVT,
	SaveTableState_Done,
} SaveTableState_t;

typedef enum SaveSpState {
	SaveSpState_Initial,
	SaveSpState_CheckDekChange,
	SaveSpState_TrimDekChangeRange,
	SaveSpState_CheckRangeChange,
	SaveSpState_TrimAllChangeRange,
	SaveSpState_SyncSP,
	SaveSpState_DecryptUnlockDek,
	SaveSpState_SetLockingRangeAndKey,
	SaveSpState_SetShadowMBR,
	SaveSpState_Done,
} SaveSpState_t;

typedef enum TrimDEKChangeRangeState {
	TrimDEKChangeRangeState_Initial,
	TrimDEKChangeRangeState_CheckDEKChange,
	TrimDEKChangeRangeState_TriggerLoadLockingTable,
	TrimDEKChangeRangeState_CheckGlobalRange,
	TrimDEKChangeRangeState_PrepareNonGlobalTrimRange,
	TrimDEKChangeRangeState_PrepareGlobalTrimRanges,
	TrimDEKChangeRangeState_TrimRanges,
	TrimDEKChangeRangeState_CheckContinue,
	TrimDEKChangeRangeState_TriggerUnloadLockingTable,
	TrimDEKChangeRangeState_Done,
} TrimDEKChangeRangeState_t;

typedef enum CheckDataRemovalMechanismState {
	CheckDataRemovalMechanismState_Initial,
	CheckDataRemovalMechanismState_TriggerLoadDataRemovalTable,
	CheckDataRemovalMechanismState_TriggerUnloadDataRemovalTable,
	CheckDataRemovalMechanismState_Done,
} CheckDataRemovalMechanismState_t;

typedef enum TrimAllChangedRangesState {
	TrimAllChangedRangesState_Initial,
	TrimAllChangedRangesState_TriggerLoadSpareLockingTable,
	TrimAllChangedRangesState_GetOldLockingsRanges,
	TrimAllChangedRangesState_TriggerUnloadSpareLockingTable,
	TrimAllChangedRangesState_TriggerLoadLockingTable,
	TrimAllChangedRangesState_PrepareTrimRanges,
	TrimAllChangedRangesState_TriggerUnloadLockingTable,
	TrimAllChangedRangesState_TrimRanges,
	TrimAllChangedRangesState_Done,
} TrimAllChangedRangesState_t;

typedef enum SyncSpState {
	SyncSpState_Initial,
	SyncSpState_TriggerLoadSourceTable,
	SyncSpState_TriggerSaveTargetTable,
	SyncSpState_CheckContinue,
	SyncSpState_SyncVTNonVolatile,
	SyncSpState_Done,
} SyncSpState_t;

typedef enum SetLockOnResetKeyState {
	SetLockOnResetKeyState_Initial,
	SetLockOnResetKeyState_SetLockingRangeAndKey,
	SetLockOnResetKeyState_CheckContinue,
	SetLockOnResetKeyState_Done,
} SetLockOnResetKeyState_t;

typedef enum SetLockingRangeAndKeyState {
	SetLockingRangeAndKeyState_Initial,
	SetLockingRangeAndKeyState_SetRangeKey,
	SetLockingRangeAndKeyState_SetD2HKey,
	SetLockingRangeAndKeyState_TriggerLoadLockingTable,
	SetLockingRangeAndKeyState_SetRangeAndGlobalIndex,
	SetLockingRangeAndKeyState_TriggerUnloadLockingTable,
	SetLockingRangeAndKeyState_Done,
} SetLockingRangeAndKeyState_t;

typedef enum SetShadowMBRState {
	SetShadowMBRState_Initial,
	SetShadowMBRState_TriggerLoadLockingTableTable,
	SetShadowMBRState_CheckMBRControlTable,
	SetShadowMBRState_SettingMBR,
	SetShadowMBRState_DefaultSetting,
	SetShadowMBRState_TriggerUnloadLockingTableTable,
	SetShadowMBRState_Done,
} SetShadowMBRState_t;

typedef enum MethodInvokingState {
	MethodInvokingState_Initial,
	MethodInvokingState_CheckHeaderAndGetInvokingUidLAndMethodUidL,
	MethodInvokingState_CheckCtrlSession,
	MethodInvokingState_SessionManagementMethodInvoking,
	MethodInvokingState_SimpleMethodInvoking,
	MethodInvokingState_CheckRestPayLoadToken,
	MethodInvokingState_Done,
} MethodInvokingState_t;

typedef enum SessionManagementMethodInvokingState {
	SessionManagementMethodInvokingState_Initial,
	SessionManagementMethodInvokingState_CheckMethodUidL,
	SessionManagementMethodInvokingState_Property,
	SessionManagementMethodInvokingState_PropertyResponse,
	SessionManagementMethodInvokingState_StartSession,
	SessionManagementMethodInvokingState_SyncSession,
	SessionManagementMethodInvokingState_CloseSession,
	SessionManagementMethodInvokingState_Done,
} SessionManagementMethodInvokingState_t;

typedef enum StartSessionState {
	StartSessionState_Initial,
	StartSessionState_CheckSessionPropertyAndInitParameter,
	StartSessionState_CheckGrammar,
	StartSessionState_ErrorHandleParameter,
	StartSessionState_FailHandle,
	StartSessionState_Authenticate,
	StartSessionState_SetSessionProperty,
	StartSessionState_SaveTable,
	StartSessionState_FillBuffer,
	StartSessionState_SetBufferHeader,
	StartSessionState_Done,
} StartSessionState_t;

typedef enum StartSessionErrorHandleState {
	StartSessionErrorHandleState_Initial,
	StartSessionErrorHandleState_GetLifeCycle,
	StartSessionErrorHandleState_GetTableIndex,
	StartSessionErrorHandleState_TriggerLoadAuthority,
	StartSessionErrorHandleState_CheckAuthorityTableElement,
	StartSessionErrorHandleState_TriggerUnoadAuthority,
	StartSessionErrorHandleState_Done,
} StartSessionErrorHandleState_t;

typedef enum SimpleMethodInvokingState {
	SimpleMethodInvokingState_Initial,
	SimpleMethodInvokingState_CheckMethodUidH,
	SimpleMethodInvokingState_CheckACL,
	SimpleMethodInvokingState_CheckReadWriteSession,
	SimpleMethodInvokingState_SwitchMethodUidL,
	SimpleMethodInvokingState_GetACL,
	SimpleMethodInvokingState_Next,
	SimpleMethodInvokingState_GenKey,
	SimpleMethodInvokingState_RevertSp,
	SimpleMethodInvokingState_Get,
	SimpleMethodInvokingState_Set,
	SimpleMethodInvokingState_Authenticate,
	SimpleMethodInvokingState_Revert,
	SimpleMethodInvokingState_Activate,
	SimpleMethodInvokingState_Random,
	SimpleMethodInvokingState_Reactivate,
	SimpleMethodInvokingState_Erase,
	SimpleMethodInvokingState_Done,
} SimpleMethodInvokingState_t;

typedef enum AuthorityAuthenticateState {
	AuthorityAuthenticateState_Initial,
	AuthorityAuthenticateState_CheckAuthority,
	AuthorityAuthenticateState_TriggerLoadAdmin3,
	AuthorityAuthenticateState_CopyEkekandEpassword,
	AuthorityAuthenticateState_TriggerSaveAdmin3,
	AuthorityAuthenticateState_TriggerLoadAuthorityTable,
	AuthorityAuthenticateState_GetAuthorityTable,
	AuthorityAuthenticateState_TriggerUnloadAuthorityTable,
	AuthorityAuthenticateState_CheckIsAnybody,
	AuthorityAuthenticateState_TriggerLoadCPinTable,
	AuthorityAuthenticateState_CheckTryLimit,
	AuthorityAuthenticateState_DecryptEpassword,
	AuthorityAuthenticateState_DecryptEkek,
	AuthorityAuthenticateState_TriggerSaveCPinTable,
	AuthorityAuthenticateState_CheckAuthenticationNum,
	AuthorityAuthenticateState_SetAuthorityTable,
	AuthorityAuthenticateState_TriggerSaveAuthorityTable,
	AuthorityAuthenticateState_FixTempEkek,
	AuthorityAuthenticateState_DecryptUnlockDek,
	AuthorityAuthenticateState_EncryptAllDektoEDek,
	AuthorityAuthenticateState_MakeSureKeyRangeNecessary,
	AuthorityAuthenticateState_CheckContinue,
	AuthorityAuthenticateState_Done
} AuthorityAuthenticateState_t;

typedef enum GetAclState {
	GetAclState_Initial,
	GetAclState_CheckGrammar,
	GetAclState_AdminAcl,
	GetAclState_LockingAcl,
	GetAclState_FillBuffer,
	GetAclState_SetBufferHeader,
	GetAclState_Done,
} GetAclState_t;

typedef enum CheckAclState {
	CheckAclState_Initial,
	CheckAclState_GetAdminAcl,
	CheckAclState_GetLockingAcl,
	CheckAclState_GetTableIndex,
	CheckAclState_TriggerLoadACETable,
	CheckAclState_TriggerUnloadACETable,
	CheckAclState_TriggerLoadAuthorityTable,
	CheckAclState_CheckAuthority,
	CheckAclState_TriggerUnloadAuthorityTable,
	CheckAclState_CheckContinue2,
	CheckAclState_CheckContinue,
	CheckAclState_Done,
} CheckAclState_t;

typedef enum NextState {
	NextState_Initial,
	NextState_CheckGrammar,
	NextState_FillMaxRows,
	NextState_CheckGrammar_ErrorHandleParameter,
	NextState_FillStartList,
	NextState_CalculateFlashIndex,
	NextState_TriggerLoadTable,
	NextState_FillNextResponse,
	NextState_TriggerUnloadTable,
	NextState_CheckContinue,
	NextState_FillEndList,
	NextState_SetBufferHeader,
	NextState_Done,
} NextState_t;

typedef enum GenKeyState {
	GenKeyState_Initial,
	GenKeyState_CheckGrammar,
	GenKeyState_GenDek,
	GenKeyState_EncryptDek,
	GenKeyState_MakeSureKeyRangeNecessary,
	GenKeyState_SetLockingRangeAndKey,
	GenKeyState_SaveTable,
	GenKeyState_FillBuffer,
	GenKeyState_SetBufferHeader,
	GenKeyState_Done,
} GenKeyState_t;

typedef enum MakeSureKeyRangeNexessaryState {
	MakeSureKeyRangeNexessaryState_Initial,
	MakeSureKeyRangeNexessaryState_TriggerLoadLockingTablel,
	MakeSureKeyRangeNexessaryState_TriggerUnloadLockingTablel,
	MakeSureKeyRangeNexessaryState_TriggerLoadRelativeEncryptedRangeKeyAndClear,
	MakeSureKeyRangeNexessaryState_TriggerSaveRelativeEncryptedRangeKey,
	MakeSureKeyRangeNexessaryState_GenKekRange,
	MakeSureKeyRangeNexessaryState_EncryptKekRange,
	MakeSureKeyRangeNexessaryState_EncryptDekToEdekRange,
	MakeSureKeyRangeNexessaryState_Done,

} MakeSureKeyRangeNexessaryState_t;

typedef enum RevertSpState {
	RevertSpState_Initial,
	RevertSpState_CheckGrammar,
	RevertSpState_CheckGlobalRangeReadWriteLock,
	RevertSpState_DecryptEkekRange,
	RevertSpState_RevertDataStore,
	RevertSpState_RevertMBR,
	RevertSpState_CheckDataRemovalMechanism,
	RevertSpState_TrimAllRange,
	RevertSpState_GenAllDek,
	RevertSpState_EncryptAllDek,
	RevertSpState_EncryptAllDekRange,
	RevertSpState_LockingSPInit,
	RevertSpState_DecryptGlobalRangeDek,
	RevertSpState_MakeSureKeyRangeNecessary,
	RevertSpState_CheckContinue,
	RevertSpState_EncryptKekAndPassword,
	RevertSpState_ResetLifecycle,
	RevertSpState_SaveTable,
	RevertSpState_FillBuffer,
	RevertSpState_SetBufferHeader,
	RevertSpState_Done,
} RevertSpState_t;

typedef enum RevertSpCheckGlobalRangeReadWriteLockState {
	RevertSpCheckGlobalRangeReadWriteLockState_Initial,
	RevertSpCheckGlobalRangeReadWriteLockState_TriggerLoadLockingTable,
	RevertSpCheckGlobalRangeReadWriteLockState_TriggerUnloadLockingTable,
	RevertSpCheckGlobalRangeReadWriteLockState_Done,
} RevertSpCheckGlobalRangeReadWriteLockState_t;

typedef enum EncryptDekState {
	EncryptDekState_Initial,
	EncryptDekState_TriggerLoadEdek,
	EncryptDekState_EncryptandSet,
	EncryptDekState_TriggerSaveEdek,
	EncryptDekState_Done,
} EncryptDekState_t;

typedef enum AdminInitState {
	AdminInitState_Initial,
	AdminInitState_TriggerLoadAdmin1Table,
	AdminInitState_TriggerSaveAdmin1Table,
	AdminInitState_TriggerLoadAdmin2Table,
	AdminInitState_SetAdmin2Table,
	AdminInitState_TriggerSaveAdmin2Table,
	AdminInitState_TriggerLoadAdmin3Table,
	AdminInitState_TriggerSaveAdmin3Table,
	AdminInitState_Done,
} AdminInitState_t;

typedef enum LockingInitState {
	LockingInitState_Initial,
	LockingInitState_TriggerLoadLockingTable1,
	LockingInitState_SetLockingTable1,
	LockingInitState_TriggerSaveLockingTable1,
	LockingInitState_TriggerLoadLockingTable2,
	LockingInitState_SetLockingTable2,
	LockingInitState_TriggerSaveLockingTable2,
	LockingInitState_TriggerLoadLockingTable3,
	LockingInitState_SetLockingTable3,
	LockingInitState_TriggerSaveLockingTable3,
	LockingInitState_TriggerLoadLockingTable4,
	LockingInitState_SetLockingTable4,
	LockingInitState_TriggerSaveLockingTable4,
	LockingInitState_TriggerLoadLockingTable5,
	LockingInitState_SetLockingTable5,
	LockingInitState_TriggerSaveLockingTable5,
	LockingInitState_TriggerLoadLockingTable6,
	LockingInitState_SetLockingTable6,
	LockingInitState_TriggerSaveLockingTable6,
	LockingInitState_TriggerLoadLockingTable7,
	LockingInitState_SetLockingTable7,
	LockingInitState_TriggerSaveLockingTable7,
	LockingInitState_TriggerLoadLockingTable8,
	LockingInitState_SetLockingTable8,
	LockingInitState_TriggerSaveLockingTable8,
	LockingInitState_Done,
} LockingInitState_t;

typedef enum GetState {
	GetState_Initial,
	GetState_FillMaxRowAndColumn,
	GetState_CheckGrammar,
	GetState_FillStartList,
	GetState_FillGetResponse,
	GetState_FillEndlist,
	GetState_SetBufferHeader,
	GetState_Done,
} GetState_t;

typedef enum SetState {
	SetState_Initial,
	SetState_FillMaxRowAndColumn,
	SetState_CheckGrammar,
	SetState_ErrorHandleParameter,
	SetState_TriggerLoadTable,
	SetState_GetTablePtr,
	SetState_SetTable,
	SetState_CheckLockingRangeValidAndCheckDecryptKey,
	SetState_TriggerSaveTable,
	SetState_DecryptEkekRangeandDek,
	SetState_CPinKeyProtection,
	SetState_MakeSureKeyRangeNecessary,
	SetState_DecryptUnlockDek,
	SetState_SetLockingRangeAndKey,
	SetState_SaveTable,
	SetState_FillBuffer,
	SetState_SetBufferHeader,
	SetState_Done,
} SetState_t;

typedef enum AuthenticateState {
	AuthenticateState_Initial,
	AuthenticateState_CheckGrammar,
	AuthenticateState_AuthorityAuthenticate,
	AuthenticateState_FillBuffer,
	AuthenticateState_SetBufferHeader,
	AuthenticateState_Done,
} AuthenticateState_t;

typedef enum RevertState {
	RevertState_Initial,
	RevertState_CheckGrammar,
	RevertState_GetLifeCycle,
	RevertState_RevertDataStore,
	RevertState_RevertMBR,
	RevertState_CheckDataRemovalMechanism,
	RevertState_TrimAllRange,
	RevertState_DecryptUnlockDek,
	RevertState_AdminInit,
	RevertState_LockingInit,
	RevertState_GenAllDek,
	RevertState_GenKek,
	RevertState_GenAllKekRange,
	RevertState_EncryptDek,
	RevertState_EncryptDekRange,
	RevertState_EncryptKekRange,
	RevertState_EncryptKekAndPassword,
	RevertState_SaveTable,
	RevertState_FillBuffer,
	RevertState_SetBufferHeader,
	RevertState_Done,
} RevertState_t;

typedef enum RevertByteTableState {
	RevertByteTableState_Initial,
	RevertByteTableState_Trim,
	RevertByteTableState_Done,
} RevertByteTableState_t;

typedef enum ActivateState {
	ActivateState_Initial,
	ActivateState_CheckGrammar,
	ActivateState_GetLifeCycle,
	ActivateState_TriggerLoadAdminCPinTable,
	ActivateState_TriggerUnloadAdminCPinTable,
	ActivateState_TriggerLoadLockingCPinTable,
	ActivateState_TriggerSaveLockingCPinTable,
	ActivateState_TriggerLoadAdmin3andCopy,
	ActivateState_TriggerSaveAdmin3,
	ActivateState_SingleUserModeEnable,
	ActivateState_SetDataStoreAndLockingAceRows,
	ActivateState_SaveTable,
	ActivateState_FillBuffer,
	ActivateState_SetBufferHeader,
	ActivateState_Done,
} ActivateState_t;

typedef enum ReactivateState {
	ReactivateState_Initial,
	ReactivateState_CheckGrammar,
	ReactivateState_CheckAllReadWriteLockEnable,
	ReactivateState_RestoreLockingSPtoOrigianlFactoryState,
	ReactivateState_EncryptKekAndPassword,
	ReactivateState_TriggerLoadEkekAndEncryptKek,
	ReactivateState_TriggerSaveEkek,
	ReactivateState_TriggerLoadCPinSetPinDigestAndInitTableTableDataStoreRows,
	ReactivateState_TriggerSaveCPin,
	ReactivateState_SetDataStoreRows,
	ReactivateState_SingleUserModeEnable,
	ReactivateState_SessionPropInit,
	ReactivateState_SaveTable,
	ReactivateState_FillBuffer,
	ReactivateState_SetBufferHeader,
	ReactivateState_Done,
} ReactivateState_t;

typedef enum EraseState {
	EraseState_Initial,
	EraseState_TriggerLoadLockingTableAndReset,
	EraseState_TriggerSaveLockingTable,
	EraseState_TriggerLoadCPinTableAndReset,
	EraseState_TriggerSaveCPinTable,
	EraseState_EncryptKekAndPassword,
	EraseState_GenDek,
	EraseState_EncryptDek,
	EraseState_MakeSureKeyRangeNecessary,
	EraseState_SaveTable,
	EraseState_FillBuffer,
	EraseState_SetBufferHeader,
	EraseState_Done,
} EraseState_t;

typedef enum RandomState {
	RandomState_Initial,
	RandomState_FillBuffer,
	RandomState_SetBufferHeader,
	RandomState_Done,
} RandomState_t;

typedef enum SecurityRecvState {
	SecurityRecvState_Initial,
	SecurityRecvState_InitTcg,
	SecurityRecvState_CheckReturnNoFurtherData,
	SecurityRecvState_AllocateManagersAndTcgVTBuf,
	SecurityRecvState_LoadOpalVT,
	SecurityRecvState_ValidState,
	SecurityRecvState_GetSecurityKey,
	SecurityRecvState_LoadWriteProtectTCGTable,
	SecurityRecvState_InitHostCmdAndReceiveBufferAddr,
	SecurityRecvState_OpalProcess,
	SecurityRecvState_InvalidState,
	SecurityRecvState_SaveOpalVT,
	SecurityRecvState_FreeManagersAndTcgVTBuf,
	SecurityRecvState_FreePayLoadBuf,
	SecurityRecvState_Done,
	SecurityRecvState_Done2,
} TcgSecurityRecvState_t;

typedef enum TcgSetPsidState {
	TcgSetPsidState_Initial,
	TcgSetPsidState_AllocateManagersAndTcgVTBuf,
	TcgSetPsidState_ValidState,
	TcgSetPsidState_GetSecurityKey,
	TcgSetPsidState_LoadWriteProtectTCGTable,
	TcgSetPsidState_TriggerLoadAdmin3Table,
	TcgSetPsidState_TriggerSaveAdmin3Table,
	TcgSetPsidState_TriggerLoadAdmin2Table,
	TcgSetPsidState_TriggerSaveAdmin2Table,
	TcgSetPsidState_InvalidState,
	TcgSetPsidState_FreeManagersAndTcgVTBuf,
	TcgSetPsidState_Done,
} TcgSetPsidState_t;

typedef enum SecurityRecvSecp1State {
	SecurityRecvSecp1State_Initial,
	SecurityRecvSecp1State_Level0Discovery,
	SecurityRecvSecp1State_CheckTimeOut,
	SecurityRecvSecp1State_CloseSession,
	SecurityRecvSecp1State_CheckOutStandingData,
	SecurityRecvSecp1State_CheckSendFirstToken,
	SecurityRecvSecp1State_MethodInvoking,
	SecurityRecvSecp1State_SetBufferHeader,
	SecurityRecvSecp1State_EndSessionResponse,
	SecurityRecvSecp1State_StartTransaction,
	SecurityRecvSecp1State_EndTransaction,
	SecurityRecvSecp1State_Done,
} SecurityRecvSecp1State_t;

typedef enum Level0DiscoveryState {
	Level0DiscoveryState_Initial,
	Level0DiscoveryState_GetLifeCycle,
	Level0DiscoveryState_TriggerLoadMBRControlTable,
	Level0DiscoveryState_TriggerUnloadMBRControlTable,
	Level0DiscoveryState_TriggerLoadLockingTable,
	Level0DiscoveryState_FillBuffer,
	Level0DiscoveryState_TriggerUnloadLockingTable,
	Level0DiscoveryState_Done,
} Level0DiscoveryState_t;

typedef enum EndSessionState {
	EndSessionState_Initial,
	EndSessionState_RestoreSp,
	EndSessionState_FlushByteTable,
	EndSessionState_WaitFlush,
	EndSessionState_FillBuffer,
	EndSessionState_SetBufferHeader,
	EndSessionState_SessionPropertyInit,
	EndSessionState_ClearKek,
	EndSessionState_Done,
} EndSessionState_t;

typedef enum ResetCPinTableTriesState {
	ResetCPinTableTriesState_Initial,
	ResetCPinTableTriesState_TriggerLoadCPinTable,
	ResetCPinTableTriesState_ResetTries,
	ResetCPinTableTriesState_TriggerSaveCPinTable,
	ResetCPinTableTriesState_Done,
} ResetCPinTableTriesState_t;

typedef enum MBRTableDoneOnResetState {
	MBRTableDoneOnResetState_Initial,
	MBRTableDoneOnResetState_TriggerLoadMBRTable,
	MBRTableDoneOnResetState_ResetDone,
	MBRTableDoneOnResetState_TriggerSaveMBRTable,
	MBRTableDoneOnResetState_Done,
} MBRTableDoneOnResetState_t;

typedef enum ReadWrtieLockOnResetState {
	ReadWrtieLockOnResetState_Initial,
	ReadWrtieLockOnResetState_TriggerLoadLockingTable,
	ReadWrtieLockOnResetState_ResetReadWriteLock,
	ReadWrtieLockOnResetState_TriggerSaveLockingTable,
	ReadWrtieLockOnResetState_Done,
} ReadWrtieLockOnResetState_t;

typedef enum DecryptEkekRangeState {
	DecryptEkekRangeState_Initial,
	DecryptEkekRangeState_TriggerLoadEkekRange,
	DecryptEkekRangeState_DecryptEkekRangeAndSet,
	DecryptEkekRangeState_TriggerUnloadEkekRange,
	DecryptEkekRangeState_Done,
} DecryptEkekRangeState_t;

typedef enum DecryptAllEkekRangeState {
	DecryptAllEkekRangeState_Initial,
	DecryptAllEkekRangeState_Decrypt,
	DecryptAllEkekRangeState_CheckContinue,
	DecryptAllEkekRangeState_Done,
} DecryptAllEkekRangeState_t;

typedef enum DecryptEdekState {
	DecryptEdekState_Initial,
	DecryptEdekState_TriggerLoadEdek,
	DecryptEdekState_DecryptEdekAndSet,
	DecryptEdekState_TriggerUnloadEdek,
	DecryptEdekState_Done,
} DecryptEdekState_t;

typedef enum DecryptAllEdekState {
	DecryptAllEdekState_Initial,
	DecryptAllEdekState_Decrypt,
	DecryptAllEdekState_CheckContinue,
	DecryptAllEdekState_Done,
} DecryptAllEdekState_t;

typedef enum EncryptAllDekState {
	EncryptAllDekState_Initial,
	EncryptAllDekState_Encrypt,
	EncryptAllDekState_AddIndex,
	EncryptAllDekState_CheckContinue,
	EncryptAllDekState_Done,
} EncryptAllDekState_t;

typedef enum EncryptKekAndPasswordWhenInitState {
	EncryptKekandPasswordWhenInitState_Initial,
	EncryptKekAndPasswordWhenInitState_TriggerLoadCPin,
	EncryptKekAndPasswordWhenInitState_GetCPin,
	EncryptKekAndPasswordWhenInitState_TriggerUnloadCPin,
	EncryptKekAndPasswordWhenInitState_TriggerLoadAdmin3,
	EncryptKekAndPasswordWhenInitState_SetEkekAndEpassword,
	EncryptKekAndPasswordWhenInitState_TriggerSaveAdmin3,
	EncryptKekAndPasswordWhenInitState_Done,
} EncryptKekAndPasswordWhenInitState_t;

typedef enum EncryptKekRangeState {
	EncryptKekRangeState_Initial,
	EncryptKekRangeState_TriggerLoadEkek,
	EncryptKekRangeState_SetEkek,
	EncryptKekRangeState_TriggerSaveEkek,
	EncryptKekRangeState_Done,
} EncryptKekRangeState_t;

typedef enum EncryptAllKekRangeState {
	EncryptAllKekRangeState_Initial,
	EncryptAllKekRangeState_Encrypt,
	EncryptAllKekRangeState_CheckContinue,
	EncryptAllKekRangeState_Done,
} EncryptAllKekRangeState_t;

typedef enum ForceSetSupportedFeatureState {
	ForceSetSupportedFeature_Initial,
	ForceSetSupportedFeature_LoadTable,
	ForceSetSupportedFeature_ReadLifeCycle,
	ForceSetSupportedFeature_Done,
} ForceSetSupportedFeatureState_t;

typedef enum ReadWriteLockingSpLifeCycle {
	ReadWriteLockingSpLifeCycle_Initial,
	ReadWriteLockingSpLifeCycle_LoadTable,
	ReadWriteLockingSpLifeCycle_ReadWriteLifeCycle,
	ReadWriteLockingSpLifeCycle_UnloadTable,
	ReadWriteLockingSpLifeCycle_Done,
} ReadWriteLockingSpLifeCycle_t;

typedef enum SaveMBRState {
	SaveMBRState_Initial,
	SaveMBRState_CopyOrigintoSpare,
	SaveMBRState_WriteSpare,
	SaveMBRState_WriteOrigin,
	SaveMBRState_Wait,
	SaveMBRState_Done,
} SaveMBRState_t;

typedef enum SaveDataStoreState {
	SaveDataStoreState_Initial,
	SaveDataStoreState_SwitchEvent,
	SaveDataStoreState_CopySparetoOrigin,
	SaveDataStoreState_CopyOrigintoSpare,
	SaveDataStoreState_CalculateDataStoreShift,
	SaveDataStoreState_WriteSpare,
	SaveDataStoreState_WriteOrigin,
	SaveDataStoreState_Wait,
	SaveDataStoreState_Done,
} SaveDataStoreState_t;

typedef enum HandleComIdRequestState {
	HandleComIdRequestState_Initial,
	HandleComIdRequestState_VerifyComIDValid,
	HandleComIdRequestState_CheckStackReset,
	HandleComIdRequestState_StackReset,
	HandleComIdRequestState_Done,
} HandleComIdRequestState_t;

typedef enum TperResetState {
	TperResetState_Initial,
	TperResetState_TriggerLoadTperInfo,
	TperResetState_TriggerUnloadTperInfo,
	TperResetState_RestoreSp,
	TperResetState_ComIdTperHostPropertyInit,
	TperResetState_SessionPropertyInit,
	TperResetState_LockOnReset,
	TperResetState_MBRDoneOnReset,
	TperResetState_SetReadWriteLock,
	TperResetState_CheckContinue,
	TperResetState_SetMBR,
	TperResetState_Done,
} TperResetState_t;

typedef enum StackResetState {
	StackResetState_Initial,
	StackResetState_RestoreSp,
	StackResetState_HostTperPropertyInit,
	StackResetState_SessionPropertyInit,
	StackResetState_Done,
} StackResetState_t;

typedef enum SendBufferDecodeState {
	SendBufferDecodeState_Initial,
	SendBufferDecodeState_CheckHeader,
	SendBufferDecodeState_ParsingToken,
	SendBufferDecodeState_InvokingMethod,
	SendBufferDecodeState_Done,
} SendBufferDecodeState_t;

typedef enum ProrpertyResponseState {
	ProrpertyResponseState_Initial,
	ProrpertyResponseState_FillBuffer,
	ProrpertyResponseState_SetBufferHeader,
	ProrpertyResponseState_Done,
} ProrpertyResponseState_t;

typedef enum GetLockingAclState {
	GetLockingAclState_Initial,
	GetLockingAclState_TriggerLoadLockingTable,
	GetLockingAclState_GetAcl,
	GetLockingAclState_TriggerUnoadLockingTable,
	GetLockingAclState_Done,
} GetLockingAclState_t;

typedef enum SingleUserModeEnableState {
	SingleUserModeEnableState_Initial,
	SingleUserModeEnableState_TriggerLoadLockingInfoTable,
	SingleUserModeEnableState_SetPolicy,
	SingleUserModeEnableState_SetLockingInfo,
	SingleUserModeEnableState_TriggerSaveLockingInfoTable,
	SingleUserModeEnableState_TriggeLoadLockingAceTable,
	SingleUserModeEnableState_TriggerSaveLockingAceTable,
	SingleUserModeEnableState_CheckContinue,
	SingleUserModeEnableState_Done,
} SingleUserModeEnableState_t;

typedef enum SetDataStoreRowsAndTableTableState {
	SetDataStoreRowsAndTableTableState_Initial,
	SetDataStoreRowsAndTableTableState_TriggerLoadTableTable,
	SetDataStoreRowsAndTableTableState_TriggerSaveTableTable,
	SetDataStoreRowsAndTableTableState_Done,
} SetDataStoreRowsAndTableTableState_t;

typedef enum CheckAllReadWriteLockEnableState {
	CheckAllReadWriteLockEnableState_Initial,
	CheckAllReadWriteLockEnableState_TriggerLoadLockingTable,
	CheckAllReadWriteLockEnableState_TriggerUnloadLockingTable,
	CheckAllReadWriteLockEnableState_Done,
} CheckAllReadWriteLockEnableState_t;

typedef enum FillMaxRowAndColumnState {
	FillMaxRowAndColumnState_Initial,
	FillMaxRowAndColumnState_TriggerLoadTableTable,
	FillMaxRowAndColumnState_TriggerUnloadTableTable,
	FillMaxRowAndColumnState_Done,
} FillMaxRowAndColumnState_t;

typedef enum GetTableState {
	GetTableState_Initial,
	GetTableState_FillByteLength,
	GetTableState_CalculateAdditionalDataStoreShift,
	GetTableState_ByteReadandFillBuffer,
	GetTableState_WaitRead,
	GetTableState_FillStartList,
	GetTableState_CheckAce,
	GetTableState_TriggerLoadTable,
	GetTableState_TriggerGetTablePtr,
	GetTableState_FillBufferByTablePtr,
	GetTableState_TriggerUnloadTable,
	GetTableState_CheckContinue,
	GetTableState_Done,
} GetTableState_t;

typedef enum CalculateDataStoreShiftState {
	CalculateDataStoreShiftState_Initial,
	CalculateDataStoreShiftState_TriggerLoadTableTable,
	CalculateDataStoreShiftState_TriggerUnloadTableTable,
	CalculateDataStoreShiftState_Done,
} CalculateDataStoreShiftState_t;

typedef enum CheckAceColumnState {
	CheckAceColumnState_Initial,
	CheckAceColumnState_GetFlashIndex,
	CheckAceColumnState_TriggerLoadAceTable,
	CheckAceColumnState_TriggerUnloadAceTable,
	CheckAceColumnState_TriggerLoadAuthorityTable,
	CheckAceColumnState_TriggerUnloadAuthorityTable,
	CheckAceColumnState_CheckContinue,
	CheckAceColumnState_Done,
} CheckAceColumnState_t;

typedef enum SetErrorHandleState {
	SetErrorHandleState_Initial,
	SetErrorHandleState_TriggerLoadTableTable,
	SetErrorHandleState_TriggerUnloadTableTable,
	SetErrorHandleState_CheckAceColumn,
	SetErrorHandleState_CheckContinue,
	SetErrorHandleState_Done,
} SetErrorHandleState_t;

typedef enum SetCPinKeyProtectionState {
	SetCPinKeyProtectionState_Initial,
	SetCPinKeyProtectionState_TriggerLoadAdmin3,
	SetCPinKeyProtectionState_TriggerSaveAdmin3,
	SetCPinKeyProtectionState_Done,
} SetCPinKeyProtectionState_t;

typedef enum GetNonVolatileTcgVTState {
	GetNonVolatileTcgVTState_Initial,
	GetNonVolatileTcgVTState_LoadTable,
	GetNonVolatileTcgVTState_UnloadTable,
	GetNonVolatileTcgVTState_Done,
} GetNonVolatileTcgVTState_t;

typedef enum DecryptUnlockDekState {
	DecryptUnlockDekState_Initial,
	DecryptUnlockDekState_DecryptEkekRange,
	DecryptUnlockDekState_DecryptEdekRange,
	DecryptUnlockDekState_Done,
} DecryptUnlockDekState_t;

typedef enum LoadWriteProtectTCGTableState {
	LoadWriteProtectTCGTableState_Initial,
	LoadWriteProtectTCGTableState_LoadTCGTable,
	LoadWriteProtectTCGTableState_Done
} LoadWriteProtectTCGTableState_t;

typedef enum UpdateSecurityVersionState {
	UpdateSecurityVersionState_Initial,
	UpdateSecurityVersionState_AllocateTcgPayload,
	UpdateSecurityVersionState_LoadSecurityVersion,
	UpdateSecurityVersionState_UnlockPORStatus,
	UpdateSecurityVersionState_CheckSecurityVersion,
	UpdateSecurityVersionState_SyncTCGTable,
	UpdateSecurityVersionState_SetSpareTableValid,
	UpdateSecurityVersionState_UpdateOriginalTable,
	UpdateSecurityVersionState_LoadTcgVT,
	UpdateSecurityVersionState_ClearSessionTransactionFlag,
	UpdateSecurityVersionState_SaveTcgVT,
	UpdateSecurityVersionState_UpdateTableSecurityVersion,
	UpdateSecurityVersionState_SaveSecurityVersion,
	UpdateSecurityVersionState_LoadAdmin1Table,
	UpdateSecurityVersionState_FreeTcgPayload,
	UpdateSecurityVersionState_Done,
} UpdateSecurityVersionState_t;

typedef enum UpdateSecurityVersion3to4State {
	UpdateSecurityVersion3to4State_Initial,
	UpdateSecurityVersion3to4State_TriggerLoadTableVT3,
	UpdateSecurityVersion3to4State_TriggerSHANonVolatile,
	UpdateSecurityVersion3to4State_TriggerSaveTableVT3,
	UpdateSecurityVersion3to4State_CheckSpIdx,
	UpdateSecurityVersion3to4State_TriggerLoadSpTable,
	UpdateSecurityVersion3to4State_TriggerSaveSpTable,
	UpdateSecurityVersion3to4State_Done,
} UpdateSecurityVersion3to4State_t;

typedef enum LoadTableState {
	LoadTableState_Initial,
	LoadTableState_CheckVT,
	LoadTableState_SyncSpareTable,
	LoadTableState_Done,
} LoadTableState_t;

typedef enum BlockSidAuthenticateState {
	BlockSidAuthenticateState_Initial,
	BlockSidAuthenticateState_Authenticate,
	BlockSidAuthenticateState_Done,
} BlockSidAuthenticateState_t;

typedef enum TcgCryptoEraseState {
	TcgCryptoEraseState_Initial,
	TcgCryptoEraseState_AllocateManagersAndTcgVTBuf,
	TcgCryptoEraseState_ValidState,
	TcgCryptoEraseState_LoadWriteProtectTCGTable,
	TcgCryptoEraseState_LoadOpalVT,
	TcgCryptoEraseState_DecryptEkekRange,
	TcgCryptoEraseState_GenAllDek,
	TcgCryptoEraseState_GetSecurityKey,
	TcgCryptoEraseState_Pyrite1SecurityKeyProtectDek,
	TcgCryptoEraseState_GenAllKekRange,
	TcgCryptoEraseState_EncryptKekRange,
	TcgCryptoEraseState_EncryptAllDektoEdekRange,
	TcgCryptoEraseState_EncryptAllDektoEdek,
	TcgCryptoEraseState_SetLockOnResetKey,
	TcgCryptoEraseState_SaveTable,
	TcgCryptoEraseState_SaveOpalVT,
	TcgCryptoEraseState_InvalidState,
	TcgCryptoEraseState_FreeManagersAndTcgVTBuf,
	TcgCryptoEraseState_Done,
} TcgCryptoEraseState_t;

typedef enum TcgAllocateManagersAndTcgVTBufState {
	TcgAllocateManagersAndTcgVTBufState_Initial,
	//-------------------- NVME --------------------
	TcgAllocateManagersAndTcgVTBufState_StopPreread,
	TcgAllocateManagersAndTcgVTBufState_AdjustLowLimit,
	TcgAllocateManagersAndTcgVTBufState_WaitAdjustLowLimit,
	TcgAllocateManagersAndTcgVTBufState_AllocateManagersAndTcgVTBuf,
	//-------------------- SATA --------------------
	TcgAllocateManagersAndTcgVTBufState_AllocateManagersBuf,
	TcgAllocateManagersAndTcgVTBufState_WaitAllocateManagersBuf,
	TcgAllocateManagersAndTcgVTBufState_AllocateTcgVTBuf,
	//--------------- Write Protect ----------------
	TcgAllocateManagersAndTcgVTBufState_AllocateWriteProtectTcgBuf,
	TcgAllocateManagersAndTcgVTBufState_SetupWriteProtectBufAddr,
	//----------------------------------------------
	TcgAllocateManagersAndTcgVTBufState_SetupBufAddr,
	TcgAllocateManagersAndTcgVTBufState_Done,
} TcgAllocateManagersAndTcgVTBufState_t;

typedef enum TcgFreeManagersAndTcgVTBufState {
	TcgFreeManagersAndTcgVTBufState_Initial,
	//-------------------- NVME --------------------
	TcgFreeManagersAndTcgVTBufState_FreeManagersAndTcgVTBuf,
	TcgFreeManagersAndTcgVTBufState_AdjustLowLimit,
	//-------------------- SATA --------------------
	TcgFreeManagersAndTcgVTBufState_FreeManagersBuf,
	TcgFreeManagersAndTcgVTBufState_FreeTcgVTBuf,
	//----------------------------------------------
	TcgFreeManagersAndTcgVTBufState_Done,
} TcgFreeManagersAndTcgVTBufState_t;

typedef enum Pyrite1SecurityKeyProtectDekState {
	Pyrite1SecurityKeyProtectDekState_Initial,
	Pyrite1SecurityKeyProtectDekState_TriggerLoadAdmin1,
	Pyrite1SecurityKeyProtectDekState_AES,
	Pyrite1SecurityKeyProtectDekState_TriggerSaveAdmin1,
	Pyrite1SecurityKeyProtectDekState_Done,
} Pyrite1SecurityKeyProtectDekState_t;

#endif /* _TCG_STATE_H_ */
