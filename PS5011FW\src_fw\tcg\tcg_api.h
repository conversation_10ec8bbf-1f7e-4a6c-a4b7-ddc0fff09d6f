/****************************************************************************
*
*  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
*  All rights reserved
*
*  The content of this document is confidential and shall be applied
*  subject to the terms and conditions of the license agreement and
*  other applicable laws. Any unauthorized access, use or disclosure
*  of this document is strictly prohibited and may be punishable
*  under laws.
*
*  tcg_api.h
*
*
*
****************************************************************************/

#ifndef _TCG_API_H_
#define _TCG_API_H_

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "common/mem.h"
#include "tcg_conf.h"
#include "tcg_def.h"
#include "tcg_security_log.h"
#include "tcg_state.h"
#include "tcg_struct.h"
#include "aom/aom_api.h"
#include "nvme_api/nvme/shr_hal_nvme.h"
#include "ftl/ftl.h"
#include "table/sys_block/sys_block_api.h"
/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */
#define TCG_OPAL_SP_TABLE_PLANE_CNT     (12)
#define TCG_OPAL_VT_PLANE_CNT           (3)
#define TCG_OPAL_TABLE_PLANE_CNT        (2 * TCG_OPAL_SP_TABLE_PLANE_CNT + TCG_OPAL_VT_PLANE_CNT)

#define TCG_PREFORMAT_BUF_SIZE          (2+ TCG_OPAL_VT_PLANE_CNT+ TCG_OPAL_SP_TABLE_PLANE_CNT) //Variables Manager + DRBG Buffer + 3 TcgVT + 3 AdminTable + 9 LockingTable 
#define TCG_PREFORMAT_DRBG_BUF_OFFSET           (1)
#define TCG_PREFORMAT_OPAL_SP_TABLE_BUF_OFFSET  (2)
#define TCG_PREFORMAT_BUF_BASE          (PREFORMAT_TCG_BUF_ADDR) // size 80K

#define TCG_INIT_MODE_FW_INIT           (0)
#define TCG_INIT_MODE_PREFORMAT         (1)

#define TCG_LOG_MODE_EVENT				(0)
#define TCG_LOG_MODE_INFO				(1)
#define TCG_LOG_MODE_METHOD				(2)
#define TCG_LOG_MODE_ERROR				(3)

#define TCG_VUC_SETPSID_LENGTH	(32)
/*
 * ---------------------------------------------------------------------------------------------------
 *   macros
 * ---------------------------------------------------------------------------------------------------
 */
#define M_TCG_CHECK_DISABLE_LPM()			((TRUE == gTcg.btDisableLPM) || (TRUE == gTcg.btPayLoadExist) || (TRUE == gTcg.btWriteProtectBufferExist))
#define M_TCG_CHECK_HOST_COMMAND_SUPPORT()	(gTcg.btHostCmdEnable)
#define M_TCG_FLOW_ADJUST_RLB_LOW_LIMIT()	(TRUE == gTcg.btRLBLowLimitAdjust)
#define M_TCG_CHECK_PYRITE_VERSION_1()  ((TCG_SSC_PYRITE_1 == gTcg.SSCMode))
#define M_TCG_CHECK_PYRITE_VERSION_2()  ((TCG_SSC_PYRITE_2 == gTcg.SSCMode))
#define M_TCG_CHECK_PYRITE()            (M_TCG_CHECK_PYRITE_VERSION_1() || M_TCG_CHECK_PYRITE_VERSION_2())
#define M_TCG_CHECK_OPAL()              ((TCG_SSC_OPAL == gTcg.SSCMode))
#define M_TCG_GET_ADMIN_SP_CPIN_PSID_TABLE_IDX() 		(M_TCG_CHECK_OPAL() ? TCG_OPAL_ADMIN_SP_CPIN_PSID_TABLE_IDX : TCG_PYRITE_ADMIN_SP_CPIN_PSID_TABLE_IDX)
#define M_TCG_GET_LOCKING_SP_CPIN_TABLE_LENGTH()        (M_TCG_CHECK_OPAL() ? TCG_OPAL_LOCKING_SP_C_PIN_TABLE_LENGTH : TCG_PYRITE_LOCKING_SP_C_PIN_TABLE_LENGTH)

#define M_TCG_AES_EN() 						(gTcg.btDataEncrypted)
#define M_TCG_OPAL_PYRITE2_AES_EN() 		((gTcg.btDataEncrypted && M_TCG_CHECK_PYRITE_VERSION_2()) || M_TCG_CHECK_OPAL())
#define M_TCG_PYRITE1_AES_EN() 				(gTcg.btDataEncrypted && M_TCG_CHECK_PYRITE_VERSION_1())
#define M_TCG_PYRITE2_AES_EN() 				(gTcg.btDataEncrypted && M_TCG_CHECK_PYRITE_VERSION_2())
#define M_TCG_CHECK_PREFORMAT_MODE()        ((0 != gPreformat.ubDoing) || (TRUE == gpVT->DLMC.B.btFWInitPreformat))
#define M_TCG_INIT_INFOBLK_VARIABLE(pInfoBlk)	do { \
													if (TCG_EN) { \
														gTcg.SSCMode = pInfoBlk->Sector0_Info.ubIPB_TCG_SSC.B.SSCMode; \
														gTcg.btDataEncrypted = ((!pInfoBlk->Sector0_Info.ubIPB_TCG_SSC.B.btPyriteDataEncryptedDisable) && M_TCG_CHECK_PYRITE()) || ( M_TCG_CHECK_OPAL()); \
														gTcg.btHostCmdEnable = !pInfoBlk->Sector0_Info.ubIPB_TCG_SSC.B.btHostCmdDisable; \
													} \
													else { \
														gTcg.SSCMode = 0;\
														gTcg.btDataEncrypted = FALSE;\
														gTcg.btHostCmdEnable = FALSE;\
														} \
												} while (0)
#define M_TCG_CLEAR_SECURITY_KEY() (memset((void *)&gTcg.aubSecurityKey[0], 0x00, TCG_SECURITY_KEY_SIZE))
#if PS5013_EN
#define M_TCG_CHECK_ANY_RANGE_READ_LOCK()			(SECURITY_AES_RANGE_ALL_RANGE_READ_UNLOCK != M_SECURITY_GET_READ_LOCK())
#define M_TCG_CHECK_ANY_RANGE_WRITE_LOCK()			(SECURITY_AES_RANGE_ALL_RANGE_WRITE_UNLOCK != M_SECURITY_GET_WRITE_LOCK())
#define M_TCG_CHECK_MBR_EN()						(TRUE == M_SECURITY_GET_AES_RANGE_MBR_EN(SECURITY_DEFAULT_NSID))
#else /* PS5013_EN */
#define M_TCG_CHECK_ANY_RANGE_READ_LOCK()			((SECURITY_AES_RANGE_ALL_RANGE_READ_UNLOCK != TcgGetAllAESRangeReadLock()) || (TRUE == M_APU_CHECK_AES_GLOBAL_RANGE_READ_LOCK(SECURITY_DEFAULT_NSID)))
#define M_TCG_CHECK_ANY_RANGE_WRITE_LOCK()			((SECURITY_AES_RANGE_ALL_RANGE_WRITE_UNLOCK != TcgGetAllAESRangeWriteLock()) || (TRUE == M_APU_CHECK_AES_GLOBAL_RANGE_WRITE_LOCK(SECURITY_DEFAULT_NSID)))
#define M_TCG_CHECK_MBR_EN()						(M_APU_CHECK_AES_GLOBAL_RANGE_MBR_ENABLE(SECURITY_DEFAULT_NSID))
#endif /* PS5013_EN */
/*
 * ---------------------------------------------------------------------------------------------------
 *   enums
 * ---------------------------------------------------------------------------------------------------
 */
typedef enum {
	TCG_CHECK_PADDING_LOG_FILL_BUFFER,
	TCG_CHECK_PADDING_LOG_FILL_BUFFER_DONE,
	TCG_CHECK_PADDING_LOG_TRIGGER_DATA_DONE
} TCGCheckPaddingCallerEnum_t;

/*
 * ---------------------------------------------------------------------------------------------------
 *   extern global variables
 * ---------------------------------------------------------------------------------------------------
 */
extern Tcg_t gTcg;
extern U8 gaubTcgPsidEpassword[TCG_EPASSWORD_SIZE];

/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */
AOM_TCG_COMMON void TcgRepairTable(void);
AOM_TCG_INIT void TcgFWInit(void);
AOM_TCG_INIT void TcgInitAll(U8 ubInitMode);
AOM_TCG_INIT void TcgInit(void);
AOM_TCG_INIT void TcgCalculateAndSetSecurityKey(void);

AOM_TCG_SEND void TcgSecuritySend (OPT_HCMD_PTR pCmd );
AOM_TCG_SEND void TcgTrustedSend (void);
AOM_TCG_RECEIVE void TcgSecurityReceive (OPT_HCMD_PTR pCmd );
AOM_TCG_RECEIVE void TcgTrustedReceive (void);
AOM_TCG_SEND void TcgChangeCmdStateAndResetReturnType(void);
AOM_TCG_SEND U8 TcgCheckTrimRangeOverlapTcgRanges(TrimRange_t *pTrimRange, U16 *puwNextRangeIndex);
AOM_TCG_ONCE void TcgSetPsidAll(U32 ulBufAddr, U32 ulPsidAddr);
AOM_TCG_ONCE void TcgSetPsid(U32 ulBufAddr, U32 ulPsidAddr);
AOM_TCG_COMMON void TcgSetSATALPMEnable (void);
AOM_TCG_COMMON void TcgSetSATALPMDisable (void);
AOM_HOST_RESET void TcgHRST(void);
AOM_TCG_REVERT void TcgCryptoErase(void);
AOM_TCG_COMMON U8 TcgGetTcgFlowBMP(void);
AOM_TCG_RECEIVE U64 TcgGetAllAESRangeReadLock(void);
AOM_TCG_RECEIVE U64 TcgGetAllAESRangeWriteLock(void);

#if (TRUE == TCG_EN)
AOM_TCG_RECEIVE void TcgAddLog(U8 ubLogMode, U16 uwLogCode);
#else
#define TcgAddLog(...)
#endif

//No CodeBank
AOM_TCG_COMMON void OPALTableBufferManager_AOM_TCG_COMMON(void);
void OPALTableBufferManager(void);
void TcgReadTcgTable_Callback(TIEOUT_FORMAT_t uoResult);

#endif /* _TCG_API_H_ */
