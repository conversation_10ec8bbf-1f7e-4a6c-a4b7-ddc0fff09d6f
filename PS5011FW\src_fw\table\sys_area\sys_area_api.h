#ifndef _SYS_AREA_API_H_
#define _SYS_AREA_API_H_
#include "common/fw_common.h"
#include "hal/cop0/cop0_cmd.h"

#define NEW_SYSTEM_AREA_UART_EN	FALSE

#define SYSTEM_AREA_BLK_SCAN_RANGE		(64)	//System???CE????64?Blk
#define SYSTEM_AREA_FREE_BLK_MAX_NUM	(32)	//SystemAreaFreeQ?, ??CH?????32?FreeBlk
#define	SYSTEM_AREA_DBT_BLK_NUM			(2)
#define	SYSTEM_AREA_SYSTEM_BLK_NUM		(2)
#define SYSTEM_AREA_RMA_LOG_BLK_ORIGINAL_NUM	(2)//??FW?SystemArea??????????RMA LOG BLK
#define SYSTEM_AREA_RMA_LOG_BLK_MAX_NUM	(5)

#define SYSTEM_AREA_CODE_BLK_OP_NUM		(3)
#define SYSTEM_AREA_OTHER_BLK_OP_NUM	(2)

#define SYSTEM_AREA_RELEASE_BANKING_BLK_THRESHOLD	(SYSTEM_AREA_CODE_BLK_OP_NUM)

#define SYSTEM_AREA_BANKING_BLK_NUM_PER_CH	CODE_BLK_NUM_PER_CH

enum {
	SYSTEM_AREA_CODE_FIRST_REVISION = 0,
	SYSTEM_AREA_CODE_SECOND_REVISION,
	SYSTEM_AREA_CODE_REVISION_NUM,
};

#define	SYSTEM_AREA_CODE_REVISION_SHIFT (0x12)	// U17 MP fill 0x12, 0x13 in ID Page; Info Block also need to fill 0x12, 0x13

#define SYSTEM_AREA_DISCARD_FREE_BLK	(FALSE)
#define SYSTEM_AREA_ADD_FREE_BLK		(TRUE)

#define SYSAREA_CODE_REVISION_RESERVE 		(0xFF)
#define SYSTEM_AREA_DEFAULT_RMA_LOG_REVISION	(0x1)

#define MAX_EXIST_NUM	(SYSTEM_AREA_FREE_BLK_MAX_NUM * MAX_CHANNEL)
#define MAX_SCAN_PARALLEL_CE	MAX_CE

#define	PH_NUM 2
#define PH_PLANE_BANK (2)
#define	PH_BLK_ORDER 20

typedef enum {
	SYSTEM_AREA_SCAN = 0,
	SYSTEM_AREA_LPM_READ_SYSTEM_BLK,
	SYSTEM_AREA_LPM_READ_RMALOG_DBT,
	SYSTEM_AREA_LPM_READ_CODE_BANKING,
	SYSTEM_AREA_SCAN_DONE
} SystemAreaScanState_t;

enum {
	SYSTEM_AREA_SYSTEM_BLK = 0,
	SYSTEM_AREA_DBT_BLK,
	SYSTEM_AREA_CODE_BLK,
	SYSTEM_AREA_BANKING_BLK,
	SYSTEM_AREA_TEMP_BANKING_BLK,
	SYSTEM_AREA_OTHER_BLK,
	SYSTEM_AREA_BLK_TYPE_NUM
};

#define SYSTEM_AREA_SYSTEM_BLK_BIT	BIT(SYSTEM_AREA_SYSTEM_BLK)
#define SYSTEM_AREA_DBT_BLK_BIT		BIT(SYSTEM_AREA_DBT_BLK)
#define SYSTEM_AREA_CODE_BLK_BIT		BIT(SYSTEM_AREA_CODE_BLK)
#define SYSTEM_AREA_BANKING_BLK_BIT	BIT(SYSTEM_AREA_BANKING_BLK)
#define SYSTEM_AREA_CHECK_BLK_BMP	(SYSTEM_AREA_SYSTEM_BLK_BIT | SYSTEM_AREA_DBT_BLK_BIT | SYSTEM_AREA_CODE_BLK_BIT)

#define SYSTEM_AREA_DESIGNATE_HEAD_BIT				(BIT0)
#define SYSTEM_AREA_DESIGNATE_CHANNEL_BIT		(BIT1)
#define SYSTEM_AREA_DESIGNATE_CE_BIT			(BIT2)
#define SYSTEM_AREA_DESIGNATE_FLASH_PLANE_BIT	(BIT3)
#define SYSTEM_AREA_EXCLUDE_CE_BIT				(BIT4)
#define SYSTEM_AREA_EXCLUDE_FLASH_PLANE_BIT		(BIT5)
#define SYSTEM_AREA_CHECK_BOOTCODE_SCANRANGE_BIT (BIT6)

#define SYSTEM_AREA_DESIGNATE_DIRECTION		(SYSTEM_AREA_DESIGNATE_HEAD_BIT)
#define SYSTEM_AREA_DESIGNATE_POSITION		(SYSTEM_AREA_DESIGNATE_CHANNEL_BIT | SYSTEM_AREA_DESIGNATE_CE_BIT | SYSTEM_AREA_DESIGNATE_FLASH_PLANE_BIT | SYSTEM_AREA_EXCLUDE_CE_BIT | SYSTEM_AREA_EXCLUDE_FLASH_PLANE_BIT | SYSTEM_AREA_CHECK_BOOTCODE_SCANRANGE_BIT)

#define	SYSTEM_AREA_GETFREE_AVERAGE 		(0) //?Free Cnt???Channel, pop?????block
#define	SYSTEM_AREA_GETFREE_CODE_BLK 		(SYSTEM_AREA_DESIGNATE_HEAD_BIT | SYSTEM_AREA_DESIGNATE_CHANNEL_BIT | SYSTEM_AREA_DESIGNATE_CE_BIT | SYSTEM_AREA_CHECK_BOOTCODE_SCANRANGE_BIT) //???CH, CE0 pop?????block
#define SYSTEM_AREA_GETFREE_TEMP_BANKING	(SYSTEM_AREA_DESIGNATE_CHANNEL_BIT)
#define	SYSTEM_AREA_GETFREE_CASUAL_BANKING	(SYSTEM_AREA_DESIGNATE_CHANNEL_BIT) //???CH pop?????block
#if PS5017_EN
#define	SYSTEM_AREA_GETFREE_FIRST_BANKING	(SYSTEM_AREA_DESIGNATE_CHANNEL_BIT) //???CH pop?????block
#define	SYSTEM_AREA_GETFREE_SECOND_BANKING 	(SYSTEM_AREA_DESIGNATE_CHANNEL_BIT | SYSTEM_AREA_EXCLUDE_CE_BIT) //???CH???CE? pop?????block
#else /* PS5017_EN */
#define	SYSTEM_AREA_GETFREE_FIRST_BANKING	(SYSTEM_AREA_DESIGNATE_CHANNEL_BIT | SYSTEM_AREA_DESIGNATE_CE_BIT) //???CH, CE pop?????block
#define	SYSTEM_AREA_GETFREE_SECOND_BANKING 	(SYSTEM_AREA_DESIGNATE_CHANNEL_BIT | SYSTEM_AREA_DESIGNATE_CE_BIT | SYSTEM_AREA_EXCLUDE_FLASH_PLANE_BIT) //???CH, CE???plane? pop?????block
#endif /* PS5017_EN */

#define SYSTEM_AREA_NORMAL_MODE	0 //?16K Data
#define SYSTEM_AREA_HEADER_MODE	1 //????4k, ??LDPC Mode7??

#define SYSTEM_AREA_NOT_WAIT_COP0_IDLE_AND_LOCK	(0)
#define SYSTEM_AREA_WAIT_COP0_IDLE_AND_LOCK		(1)

#define SYSTEM_AREA_INVALID_BLK	0xFFFF
#define SYSAREA_OPERATION_TIMEOUT	(100)

#define M_SIZE_BYTE_TO_PLANE(BYTE_NUM) ((BYTE_NUM + ((SIZE_4KB << gub4kEntrysPerPlaneLog) - 1)) / (SIZE_4KB << gub4kEntrysPerPlaneLog))
#define M_SIZE_BYTE_TO_4KENTRY(BYTE_NUM) ((BYTE_NUM + (SIZE_4KB - 1)) / SIZE_4KB)

#define SYSTEM_AREA_BANKING_BLK_START_PAGE_2_MODE	(0)
#define SYSTEM_AREA_BANKING_BLK_START_PAGE_1_MODE	(1)

#define SYSTEM_AREA_BANKING_BLK_OVERLAY_START_PAGE	(1)
#define SYSTEM_AREA_BANKING_BLK_PAGE_SHIFT_NUM		(1)

#define SYSTEM_AREA_BOOTCODE_SUPPORTED_BIT_WIDTH_MIN      (7)
#define SYSTEM_AREA_BOOTCODE_SUPPORTED_BIT_WIDTH_MAX      (13)
#define SYSTEM_AREA_BOOTCODE_DEFUALT_SCAN_BLK_NUM	      (8)   //when scan block, we cover the block from 0 ~ MAX_SCAN_BLK_NUM
#define SYSTEM_AREA_BOOTCODE_SCAN_CODE_BLK_MAX_NUM        (32)

#pragma pack(push)
#pragma pack(1)
typedef struct {
	U8 ubBankingBlkMode;
	U8 ubReserve[4095];
} SystemAreaBankingBlkHeader_t;
#pragma pack(pop)

#define SYSTEM_AREA_NVMe_VENDOR_FEATURE_PLN_BIT			(BIT1)

enum {
	TABLE_NOT_IN_RAM = 0,
	TABLE_PART_IN_RAM,
	TABLE_ALL_IN_RAM,
};

typedef enum SysTableLogEnum {
	SYS_HANDLE_BUFFER = 0,
	SYSTEM_BLK,
	SYS_RUT_SPARE,
	SYS_IN_RAM_STATE_NUM,
} SysTableLogEnum_t;

typedef union {
	U8 uball;
	struct {
		U8 UsedPercentage: 7;
		U8 btEnable: 1;
	} B;
} FencingConfig_t;

typedef struct {
	U8 ubGetType;
	union {
		U8 ubAll;
		struct {
			U8 Channel	: 2;
			U8 CE			: 3;
			U8 FlashPlane	: 2;
			U8 Reserve		: 1;
		} DesignateInfo;
	};
} SystemAreaGetFreeInfo_t;

typedef union {
	U8 ubAll;
	struct {
		U8 btStatus					: 1;//success or fail
		U8 BadSystemBlockBitmap	: 7;//Bit_0: 1st SystemBlk, Bit_1: 2rd SystemBlk...
	} Info;
} SystemAreaBlockStatus_t;

typedef union {
	U16 uwAll;
	struct {
		U16 ubBlockOrder		: 8;
		U16 CEperCH			: 4;
		U16 Channel			: 4;
	} Info;
} SystemAreaBlock_t;

typedef struct {
	struct {
		U16 UpdateHandleState : 4;
		U16 btSystemAreaDBTLogUpdate : 1;
		U16 btCodeBlkCanNotUse 	: 1;
		U16 btRefreshDBTBlkWithoutMarkBad 		: 1;
		U16 btGetFreeBlkFail : 1;
		U16 btReadSystemBlkAllUnc 	: 1;
		U16 btReadDBTBlkAllUnc 		: 1;
		U16 btReadCodeBlockAllError : 1;
		U16 btNeedUpdateCodeBlk 	: 1;
		U16 btNeedUpdateBankingBlk 	: 1;
		U16 btNeedUpdateTempBankingBlk : 1;
		U16 btBootCodeUseOldCode	: 1;
		U16 btBootCodeUseAbnormalCode	:	1;
		U8 btSystemAreaInitialHandleDone	: 1;
		U8 btReleaseAllBankingBlk	: 1;
		U8 btVUCWriteInfoUpdateSystemBlk	: 1;
		U8 btVUCGetLatestSystemBlk	: 1;
		U8 btKingstonVUCWriteData	: 1;
		U8 Reserve	: 3;
		U8 ubGotoLPMForChangeCode;
	} Info;
	U8 ubSystemBlkUpdateCntWhenFWInit;
	U8 ubDBTBlkUpdateCntWhenFWInit;
	U8 ubCodeBlkRefreshCntWhenFWInit;
	U8 ubBankingBlkRefreshCntWhenFWInit;
	U32 ulHostInitBuf;
} SystemAreaHandleInfo_t;

typedef union {
	U8 ubAll;
	struct {
		U32 btCOP0CmdDoing 		: 1;
		U32 btCOP0CmdFail 		: 1;
		U32 Reserve : 6;
	} B;
} SystemAreaOperation_t;

typedef union {
	U8 ubAll;
	struct {
		U8 btFirstRevisionProgramDone:	1;
		U8 btSecondRevisionProgramDone:	1;
		U8 btTempBankingExist:			1;
		U8 btInitialHandleDLMC:			1;
		U8 btFWAllowUseBankingBlk:		1;
		U8 btBankingBlkIsReady:			1;
		U8							:	2;
	} Flag;
} HandleCodeStatus_t;

typedef union {
	U8 ubAll;
	struct {
		U8 btSystemAreaFirstEnlarge	: 1;
		U8 Reserve					: 7;
	} Flag;
} SystemAreaNewFeatureBMP_t;

typedef struct {
	SystemAreaBlock_t uwReturnBlk[E17_ALLIGN_E13_MAX_CH];
	SystemAreaBlock_t uwNewCodeBlk[E17_ALLIGN_E13_MAX_CH][CODE_BLK_NUM_PER_CH];
	SystemAreaBlock_t uwOldCodeBlk[E17_ALLIGN_E13_MAX_CH][CODE_BLK_NUM_PER_CH];
	U8 ubNewCodeBlkNum[E17_ALLIGN_E13_MAX_CH];
	U8 ubOldCodeBlkNum[E17_ALLIGN_E13_MAX_CH];
	U8 ubOldCodeBlkRevision[SYSTEM_AREA_CODE_REVISION_NUM];
} SystemAreaUpdateCodeInfo_t;

typedef struct {
	SystemAreaBlock_t uwNewSystemBlk[SYSTEM_AREA_SYSTEM_BLK_NUM];
	SystemAreaBlock_t uwNewDBTBlk[SYSTEM_AREA_DBT_BLK_NUM];
	U8 ubNewSystemBlkCnt;
	U8 ubNewDBTBlkCnt;
} SystemAreaUpdateInfo_t;

typedef struct {
	U32 *pulBuffer;
	U16 uwBufSizeInKByte;
	union {
		U16 uwAll;
		struct {
			U16 btAllocatedRAM 	: 1;
			U16 InRamStatus  	: 2;
			U16 UserCnt	   		: 4;//??????????RAM
			U16 btPBFromPool	: 1;
			U16 btPBFromRLB		: 1;
			U16	btPBStatic		: 1;
			U16 btPBVUC	  		: 1;
			U16 Reserve	  		: 5;
		} Info;
	};
} TableInRAMInfo_t;

#define SYS_TABLE_MAX_USER_CNT (BIT(4) - 1)

typedef struct {
	SystemAreaBlock_t FreeBlock[4][SYSTEM_AREA_FREE_BLK_MAX_NUM];	//????4CH, ??CH???32?Free Blk
	U8 ubFreeBlkCntPerCH[4];			//????4CH
} SystemAreaFreeTable_t;

typedef struct {
	U64 uoSystemRevision;
	U64 uoDBTRevision;
	U8 ubCodeBlkRevision[SYSTEM_AREA_CODE_REVISION_NUM];

	SystemAreaBlock_t SystemBlock[SYSTEM_AREA_SYSTEM_BLK_NUM];
	SystemAreaBlock_t DBTBlock[SYSTEM_AREA_DBT_BLK_NUM];
	SystemAreaBlock_t CodeBlock[E17_ALLIGN_E13_MAX_CH][CODE_BLK_NUM_PER_CH];
	SystemAreaBlock_t BankingBlk[E17_ALLIGN_E13_MAX_CH][CODE_BLK_NUM_PER_CH];
	SystemAreaBlock_t uwTempBankingBlk[E17_ALLIGN_E13_MAX_CH];
	SystemAreaBlock_t uwRMALogBlk[SYSTEM_AREA_RMA_LOG_BLK_MAX_NUM];
	SystemAreaBlock_t DLMCBinFileBlk;

	TableInRAMInfo_t TableInRamInfo[SYS_IN_RAM_STATE_NUM];

	U16 uwHighPriorityRMALogBMP;
	U8 ubSystemBlockNum;
	U8 ubDBTBlockNum;
	U8 ubCodeBlockNum[E17_ALLIGN_E13_MAX_CH];
	U8 ubBankingBlkNum[E17_ALLIGN_E13_MAX_CH];
	U8 ubRMALogBlkNum;
	U8 ubTempBankingBlkNum;

	HandleCodeStatus_t ubHandleCodeStatus;
} SystemArea_t;

#pragma pack(push)
#pragma pack(1)
typedef struct {
	SystemAreaBlock_t uwLPMSystemBlk[SYSTEM_AREA_SYSTEM_BLK_NUM];
	SystemAreaBlock_t uwLPMRMALogBlk[SYSTEM_AREA_RMA_LOG_BLK_MAX_NUM];
	U8 ubLPMSystemBlkNum;
	U8 ubLPMRMALogBlkNum;
} SystemAreaLPMRecord_t;
#pragma pack(pop)

typedef struct {
	struct {
		U64	uoSystemBlkRevision[MAX_EXIST_NUM];
		U64	uoDBTBlkRevisionInSystemTable[MAX_EXIST_NUM];
		U8 ubCodeBlkRevisionInSystemTable[MAX_EXIST_NUM][SYSTEM_AREA_CODE_REVISION_NUM];//??systable????codeBlock Version
		SystemAreaBlock_t uwRedundantBlk[MAX_EXIST_NUM];
		SystemAreaBlock_t uwDBTBlkInSystemTable[MAX_EXIST_NUM][SYSTEM_AREA_DBT_BLK_NUM];
		SystemAreaBlock_t uwCodeBlkInSystemTable[MAX_EXIST_NUM][CODE_BLK_CH_NUM][CODE_BLK_NUM_PER_CH];
		SystemAreaBlock_t uwBankingBlkInSystemTable[MAX_EXIST_NUM][CODE_BLK_CH_NUM][CODE_BLK_NUM_PER_CH];
		U8 ubSystemBlkNumInSystemTable[MAX_EXIST_NUM];
		U8 ubDBTBlkNumInSystemTable[MAX_EXIST_NUM];
		U8 ubCodeBlkNumInSystemTable[MAX_EXIST_NUM][CODE_BLK_CH_NUM];
		U8 ubBankingBlkNumInSystemTable[MAX_EXIST_NUM][CODE_BLK_CH_NUM];
		HandleCodeStatus_t ubCodeStatus[MAX_EXIST_NUM];
		SystemAreaNewFeatureBMP_t ubNewFeatureBMPInSystemTable[MAX_EXIST_NUM];
	} SystemBlkResult;

	struct {
		U64	uoDBTRevision[MAX_EXIST_NUM];
		SystemAreaBlock_t uwRedundantBlk[MAX_EXIST_NUM];
	} DBTBlkResult;

	struct {
		U8 ubCodeRevision[MAX_EXIST_NUM];
		SystemAreaBlock_t uwRedundantBlk[MAX_EXIST_NUM];
	} CodeBlkResult;

	struct {
		U8 ubBankingBlkMode[MAX_EXIST_NUM];
		SystemAreaBlock_t uwRedundantBlk[MAX_EXIST_NUM];
	} BankingBlkResult;

	struct {
		SystemAreaBlock_t uwRedundantBlk[MAX_EXIST_NUM];
	} RMALogBlkResult;

	struct {
		SystemAreaBlock_t uwRedundantBlk[MAX_EXIST_NUM];
	} OtherBlkResult;

	struct {
		SystemAreaBlock_t uwRedundantBlk[MAX_EXIST_NUM];
		U8 ubIsBadBlk[MAX_EXIST_NUM];
	} NeedEraseBlkResult;

	struct {
		U32	ul4KBuffer;
		SystemAreaBlock_t SystemAreaBlk;
		union {
			U8 ubAll;
			struct {
				U8 btSystemBlk		: 1;
				U8 btDBTBlk			: 1;
				U8 btCodeBlk		: 1;
				U8 btBankingBlk		: 1;
				U8 btRMALogBlk	: 1;
				U8 btOtherBlk 	: 1;
				U8 Reserve			: 2;
			} Info;
		} BlkType;
		U8 ubReserve;//for alignment
	} ParallelBlk[MAX_SCAN_PARALLEL_CE];

	U8 ubBlkScanMaxPerCE[MAX_CHANNEL][MAX_CE_PER_CHANNEL];

	U8 ubSystemRedundantBlkNum;
	U8 ubDBTRedundantBlkNum;
	U8 ubCodeRedundantBlkNum;
	U8 ubBankingRedundantBlkNum;
	U8 ubOtherBlkNum;
	U8 ubNeedEraseBlkNum;
	U8 ubRMALogBlkNum;
	U8 ubNeedCheckMissingBlkBMP;

	U8 ubParallelNum;
	U8 ubParallelDoingNum;
	SystemAreaScanState_t ubSystemAreaScanState;
} SystemAreaScanRecord_t;

typedef struct {
	union {
		struct {
			U32 ulAll : 24;
			U32 ubSpareValid : 8;
		};
		struct {
			U8  ubRevisionID;
			U8  ubSectionID;
			U8  ubCodeMark;
			U8  ubReserve;
		} CodeInfo;
	} FirmwareFill;
} SystemL4KTable_t;

extern SystemArea_t gSystemArea;
extern SystemAreaScanRecord_t *gpSystemAreaScanRecord;
extern SystemAreaOperation_t gSystemAreaOperation;
extern SystemAreaHandleInfo_t gSystemAreaHandle;
extern SystemAreaUpdateCodeInfo_t gSystemAreaUpdateCodeInfo;

//SysArea GenFail RollBack Structure
typedef struct {
	SystemAreaBlock_t DiscardFreeBlock[MAX_CHANNEL * SYSTEM_AREA_FREE_BLK_MAX_NUM];
	U8 ubSystemAreaDiscardCnt;
	U8 ubSystemAreaTotalFreeCnt;
} SystemAreaFreeTableRollBack_t;
extern SystemAreaFreeTableRollBack_t gSystemAreaFreeTableRollBack;

typedef struct {
	struct {
		U16 uwLPMWaitCMGRIdleTime;
		U16 uwWakeForBGTime;
		U8 ubLPMAPSTCorrectPercent;
		union {
			U8 ubAll;
			struct {
				U8 btLPMEn				: 1;
				U8 btLPMWakeForBGEn		: 1;
				U8 btBGLPMEn			: 1;
				U8 btCheckSLCPoolNumEn	: 1;
				U8 btSMBEn              : 1;
				U8 Reserve				: 3;
			} Flag;
		} Setup;
		U16 Reserve;
	} LPM;
	struct {
		U32 ulSLCPoolNum;
		U32 ulBGGCStopThreshold;
		U32 ulClearSLCPoolThreshold;
		U16 uwSLCMaxPECount;
		U16 uwBGStartThreshold;        // t1, Time from last I/O completion to L1.x
		U16 uwBGActiveIdleTime;        // From L1.0 to L1.2
		U8 ulClearD3VCPercent; //D3 Unit VC %
		U8 ubSLCPoolEn;
		union {
			U8 ubAll;
			struct {
				U8 btBGEn				: 1;
				U8 btBGReleaseSLCEn		: 1;
				U8 btBGReleaseUnitEn	: 1;
				U8 btBGWLEn				: 1;
				U8 btBGReleaseD1En		: 1;
				U8 Reserve				: 3;
			} Flag;
		} Setup;
		union {
			U16 uwAll;
			struct {
				U8 ubFreeD3UnitRatioForAdvanceGC;
				U8 ubLowerBoundVCRatioForAdvanceGC;
			} B;
		} AdvanceGCSetup;
		U32 ulWriteRangeThresholdForSLCPoolCondition;
	} BG;
	struct {
		union {
			U8 ubAll;
			struct {
				U8 btSLCProgram: 1;
				U8 Reserve: 7;
			} Flag;
		} Setup;
		union {
			U8 ubAll;
			struct {
				U8 btSLCProgramFail: 1;
				U8 Reserve: 7;
			} Flag;
		} Fail;
	} OpenBlkRaidECCMap;
	struct {
		union {
			U8 ubAll;
			struct {
				U8 btAlwaysForceCopy: 1;
				U8 Reserve: 7;
			} Flag;
		} Control;
	} SPOR;
	struct {
		U16 uwInitalDelay;
		U16 uwMinDelay;
		U16 uwMaxDelay;
	} Sustain;
	struct {
		union {
			U8 ubAll;
			struct {
				U8 btSetNESFWSetForNormalDataEn	: 1;
				U8 bt3DRandomizerEnableForNES : 1;
				U8 btNCS2En : 1;
				U8 btNRCEn : 1;
				U8 Reserve: 4;
			} Flag;
		} ;
	} Init;
#if ((USB == HOST_MODE) && USB_WORKAROUND_NUMP_EN)
	struct {
		U32 ulRWModeDelayus;
	} RWMode;
#endif /* ((USB == HOST_MODE) && USB_WORKAROUND_NUMP_EN) */
} SystmAreaSystemInfo_t;
extern SystmAreaSystemInfo_t gSystmAreaFWSettingFromInfoBlk;

#if(NVME == HOST_MODE)
typedef struct {
	U8 ubSendLargeLTRTime;	//Unit = ms
	U8 ubCapabilityTimeOut; //CAPTO, unit = 500ms
	U16 uwCCENDelayForGCTime; //Unit = 50ms
	U8 ubMaxMemoryPageSize;
	U8 ubMinMemoryPageSize;
	U32 ulRTD3Resume;
	U32 ulRTD3Entry;
	union {
		U32 ulAll;
		struct {
			U32 ubThreshold	: 8;
			U32 ubTime		: 8;
			U32 uwUnitCnt	: 16;
		} B;
	} InterruptCoalescing;
	U32 ulLTRLargeValue;
	U32 ulLTRSmallValue;
	U32 ulPSEntryLatency[5]; //5 = IDFY_CTRL_MAX_NUM_PWR_STATE
	U32 ulPSExitLatency[5];
	U16 uwPSMaxPower[5];
	union {
		U8 ubAll;
		struct {
			U8 NumofPowerState : 3;
			U8 btPSDLatencyReferInfoBlk : 1;
			U8 btPSDMaxPowerReferInfoBlk : 1;
			U8 : 3;
		};
	} PowerStateDescriptor;
	struct {
		U8 btPLNEn  : 1;
		U8 : 7;
	};
} SystemAreaNVMeInfo_t;
extern SystemAreaNVMeInfo_t gSystmAreaNVMeInfo;
#endif /* NVME == HOST_MODE */

typedef struct _Product_History_Header Product_History_Header;
struct _Product_History_Header { //128Byte
	U8 ubHeaderTag[2];//"PH"
	U16 uwLastIndex;
	U16 uwTotalItem;
	U8 ubPHVersion;
	U8 ubPHUniqueID[42];
	U8 ubReserved[79];
} __attribute__((packed));
TYPE_SIZE_CHECK(Product_History_Header, 128);

typedef struct _Product_History_Item Product_History_Item;
struct _Product_History_Item { //128Byte
	U8 ubItemTag[2];//"ph"
	U16 uwIndex;
	U8 ubWorkOrder[16];
	U8 ubToolType;
	U8 ubCurStation;
	U8 ubAPVerMajor;
	U8 ubAPVerMinor;
	U8 ubAPVerSub;
	U8 ubAPVerYear;
	U8 ubAPVerMonth;
	U8 ubAPVerDay;
	U8 ubMPYear;
	U8 ubMPMonth;
	U8 ubMPDay;
	U64 ullMSTChipID;
	U64 ullCurChipID;
	U8 ubFWVer[20];
	U8 ubBurnerVer[20];
	U8 ubCustomerID;
	U16 uwBinResult;
	U8 ubChainNum[16];
	U8 ubReserved[22];
} __attribute__((packed));
TYPE_SIZE_CHECK(Product_History_Header, 128);

typedef struct _Product_History_Log Product_History_Log;
struct _Product_History_Log { //Total 8192Byte
	Product_History_Header header;
	Product_History_Item item[63];
} __attribute__((packed));
TYPE_SIZE_CHECK(Product_History_Log, 8192);

extern U32 gulVCUnderFlowRMALogRevision;
extern FW_CALLBACK_SECTION U32 gulSysAreaOperation_CallBack;

AOM_SYSTEM_AREA_INIT void SystemAreaInitVariable(void);
AOM_SYSTEM_AREA void SystemAreaClearUpdateCodeInfo(void);
AOM_SYSTEM_AREA void SystemAreaHandleAllBankingBlk(U8 ubBufMode);
AOM_SYSTEM_AREA void SystemAreaGenerateTempBankingBlk(U8 ubBufMode);
AOM_SYSTEM_AREA void SystemAreaCleanBankingBlk(U8 ubFWAllowUseBankingBlk, U8 ubBufSrc);
AOM_SYSTEM_AREA void SystemAreaReturnBlk(U8 ubBufMode);
AOM_SYSTEM_AREA void SystemAreaHandleCodeRevision(void);
AOM_SYSTEM_AREA void SystemAreaWaitAndLockCOP0(void);
AOM_SYSTEM_AREA_INIT void SystemAreaInitRMALogBlkInfo(U32 ulAddr, U8 ubRMABlkLimitNum);
AOM_SYSTEM_AREA_INIT void SystemAreaInitCodeBlkInfo(void);
AOM_SYSTEM_AREA void SystemAreaSetRmaLogBlk(SystemAreaFreeTable_t *pSystemAreaFreeTable);
AOM_SYSTEM_AREA U8 SystemAreaAddFreeBlk(SystemAreaBlock_t NewSystemFreeBlock);
AOM_SYSTEM_AREA U8 SystemAreaBankingGetTwoFreeBlk(SystemAreaBlock_t *puwNewBankingBlk, U8 ubChannel);
AOM_SYSTEM_AREA SystemAreaBlock_t SystemAreaSecondBankingGetFreeBlk(U8 ubChannel, U8 ubCE, U8 ubPlaneIdx);
AOM_SYSTEM_AREA SystemAreaBlock_t SystemAreaGetFreeBlk(SystemAreaGetFreeInfo_t *puwGetFreeInfo);
AOM_SYSTEM_AREA U8 SystemAreaDeleteSpecificFreeBlk(SystemAreaBlock_t DeleteFreeBlock);
AOM_SYSTEM_AREA U8 SysAreaEraseBlock(SystemAreaBlock_t SysBlock);
AOM_SYSTEM_AREA U8 SysAreaProgramPlane(U32 *pul4KBufferAddr, U32 *pulLCA, SystemAreaBlock_t SysArea_Blk, U32 ulPlaneIndex, U8 ubDataMode, SystemL4KTable_t *L4KInfo, U8 ubWaitCOP0);
AOM_SYSTEM_AREA U8 SysAreaRead4KEntry(U32 ulBufAddr, U32 ulTieInLCA, SystemAreaBlock_t SysArea_Blk, U32 ulPlaneIndex, U8 ub4kEntryPtr, U8 ubDataMode);
AOM_SYSTEM_AREA void SystemAreaUpdateWithHandleBuf(U8 ubBufSrc);
AOM_SYSTEM_AREA U8 SystemAreaUpdate(U8 ubPBMode);
AOM_SYSTEM_AREA_2 void SystemAreaCheckMissingBlk(void);
AOM_SYSTEM_AREA void SystemAreaInitialNeedEraseRedundantBlkQueue(void);
AOM_SYSTEM_AREA void SystemAreaInitRead(void);
AOM_SYSTEM_AREA void SystemAreaCheckWriteProtect(void);
AOM_BURNER	void SystemAreaScanBlk(void);
void SysAreaOperation_CallBack(TIEOUT_FORMAT_t uoResult);
AOM_SYSTEM_AREA_2 U16 SystemAreaGetCodeBlkList(U16 *uwBootBlkList);
#if ((MICRON_FSP_EN) && BURNER_MODE_EN && (!IM_B47R) && (!IM_B37R) && (!IM_N48R))
void SystemAreaSLCInitialize(SystemAreaBlock_t uwBlk);
#endif /* ((MICRON_FSP_EN) && BURNER_MODE_EN && (!IM_B47R)&&(!IM_N48R_NEED_CHECK)) */
#endif
