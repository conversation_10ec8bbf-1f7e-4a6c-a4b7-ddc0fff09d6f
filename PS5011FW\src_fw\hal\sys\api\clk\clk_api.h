#ifndef _CLK_API_H_
#define _CLK_API_H_

#include "typedef.h"
#include "hal/sys/reg/sys_pd0_reg.h"
#include "aom/aom_api.h"
#if PS5017_EN
#include "debug/debug.h" // for M_FW_TODO()
#include "hal/sys/api/angc/angc_api.h"

#define CLOCK_667MHz					(667)
#define CLOCK_800MHz					(800)
#define CLOCK_533MHz					(533)
#define CLOCK_100MHz					(100)
#define CLOCK_42MHz						(42)	//41.7
#define CLOCK_DEFAULT					(0)

#define CLOCK_SELECT_10M				(0)
#define CLOCK_SELECT_40M				(1)
#define CLOCK_SELECT_50M				(2)
#define CLOCK_SELECT_166M				(3)
#define CLOCK_SELECT_200M				(4)
#define CLOCK_SELECT_333M				(5)
#define CLOCK_SELECT_400M				(6)
#define CLOCK_SELECT_533M				(7)
#define CLOCK_SELECT_667M				(8)
#define CLOCK_SELECT_800M				(9)
#define CLOCK_SELECT_1200M				(10)
#define CLOCK_SELECT_1066M				(11)
#define CLOCK_SELECT_900M				(12)
#define CLOCK_SELECT_1400M				(13)
#define CLOCK_SELECT_1600M				(14)
#define CLOCK_SELECT_450M				(15)

#define CLOCK_DIVIDER_GAP				(1) // E17 divider design change

#define CLOCK_INVALID					(0xFF)

/************************************************
				SEL
************************************************/
//CTRL0
#define CLOCK_REF_SEL_OFFSET			(0)
#define CLOCK_SYS_SEL_OFFSET			(2)
#define CLOCK_COP0_RATE_SEL_OFFSET		(8)
#define CLOCK_RCOSC_SEL_OFFSET			(10)
#define CLOCK_ZQ_SEL_OFFSET				(11)
#define CLOCK_ROM_SEL_OFFSET			(16)
#define CLOCK_CPU_SEL_OFFSET			(17)
#define CLOCK_PIC_SEL_OFFSET			(25)

#define CLOCK_REF_SEL_MASK				(BIT_MASK(2))
#define CLOCK_SYS_SEL_MASK				(BIT_MASK(2))
#define CLOCK_COP0_RATE_SEL_MASK		(BIT_MASK(2))
#define CLOCK_RCOSC_SEL_MASK			(BIT_MASK(1))
#define CLOCK_ZQ_SEL_MASK				(BIT_MASK(2))
#define CLOCK_ROM_SEL_MASK				(BIT_MASK(1))
#define CLOCK_CPU_SEL_MASK				(BIT_MASK(2))
#define CLOCK_PIC_SEL_MASK				(BIT_MASK(1))

//CTRL1
#define CLOCK_AES_SEL_OFFSET			(1)
#define CLOCK_SHA_SEL_OFFSET			(9)
#define CLOCK_SM4_SEL_OFFSET			(17)
#define CLOCK_LZSS_SEL_OFFSET			(25)

#define CLOCK_AES_SEL_MASK				(BIT_MASK(2))
#define CLOCK_SHA_SEL_MASK				(BIT_MASK(2))
#define CLOCK_SM4_SEL_MASK				(BIT_MASK(2))
#define CLOCK_LZSS_SEL_MASK				(BIT_MASK(2))

//CTRL2
#define CLOCK_FLHR_SEL_OFFSET			(1)
#define CLOCK_FLHW_SEL_OFFSET			(9)
#define CLOCK_ECC_SEL_OFFSET			(17)

#define CLOCK_FLHR_SEL_MASK				(BIT_MASK(2))
#define CLOCK_FLHW_SEL_MASK				(BIT_MASK(2))
#define CLOCK_ECC_SEL_MASK				(BIT_MASK(2))

//CTRL3
#define CLOCK_SPI_SEL_OFFSET			(1)
#define CLOCK_TS_SEL_OFFSET				(8)
#define CLOCK_TRACE_SEL_OFFSET			(16)

#define CLOCK_SPI_SEL_MASK				(BIT_MASK(1))
#define CLOCK_TS_SEL_MASK				(BIT_MASK(1))
#define CLOCK_TRACE_SEL_MASK			(BIT_MASK(1))

//CTRL4
//CTRL5
//CTRL6

/************************************************
				DIV
************************************************/
//CTRL0
#define CLOCK_SYS_DIV_OFFSET			(4)
#define CLOCK_CPU_DIV_OFFSET			(20)
#define CLOCK_PIC_DIV_OFFSET			(28)

#define CLOCK_SYS_DIV_MASK				(BIT_MASK(4))
#define CLOCK_CPU_DIV_MASK				(BIT_MASK(4))
#define CLOCK_PIC_DIV_MASK				(BIT_MASK(4))

//CTRL1
#define CLOCK_AES_DIV_OFFSET			(4)
#define CLOCK_SHA_DIV_OFFSET			(12)
#define CLOCK_SM4_DIV_OFFSET			(20)
#define CLOCK_LZSS_DIV_OFFSET			(28)

#define CLOCK_AES_DIV_MASK				(BIT_MASK(4))
#define CLOCK_SHA_DIV_MASK				(BIT_MASK(4))
#define CLOCK_SM4_DIV_MASK				(BIT_MASK(4))
#define CLOCK_LZSS_DIV_MASK				(BIT_MASK(4))

//CTRL2
#define CLOCK_ECC_DIV_OFFSET			(19)

#define CLOCK_ECC_DIV_MASK				(BIT_MASK(4))

//CTRL3
#define CLOCK_SPI_DIV_OFFSET			(4)
#define CLOCK_TS_DIV_OFFSET				(12)
#define CLOCK_TRACE_DIV_OFFSET			(20)

#define CLOCK_SPI_DIV_MASK				(BIT_MASK(4))
#define CLOCK_TS_DIV_MASK				(BIT_MASK(4))
#define CLOCK_TRACE_DIV_MASK			(BIT_MASK(4))

//CTRL4
//CTRL5
//CTRL6


enum OSC_ID {
	OSC_1 = 0,
	OSC_2,
	OSC_3,
	LFOSC,
};

enum KOUT_ID {
	KOUT_1 = 0,
	KOUT_2,
	KOUT_3,
	KOUT_4,
};


enum CLOCK_CENTER_ID {
	//not used in E17
	CLOCK_CENTER_0 = NULL,
	CLOCK_CENTER_1 = NULL,
	CLOCK_CNETER_ID_NUM = 2
};

enum CLOCK_IP_GEN_ID {
	CLOCK_IP_REF = 0,
	CLOCK_IP_CPU,
	CLOCK_IP_ROM,
	CLOCK_IP_FPHY_X2_R,
	CLOCK_IP_FPHY_X2_W,
	CLOCK_IP_FLH_ECC,
	CLOCK_IP_AES,
	CLOCK_IP_LZSS,
	CLOCK_IP_SHA,
	CLOCK_IP_SPI,
	CLOCK_IP_ZQ,
	CLOCK_IP_SYS,
	CLOCK_IP_PIC,
	CLOCK_IP_RCOSC,
	CLOCK_IP_SM4,
	CLOCK_IP_TRACE,
	CLOCK_IP_TS,
	CLOCK_IP_ID_NUM
};

enum CLOCK_CTRL {
	CLK_CTRL0 = 0,
	CLK_CTRL1,
	CLK_CTRL2,
	CLK_CTRL3,
	CLK_CTRL4,
	CLK_CTRL5,
	CLK_CTRL6,
	CLK_CTRL_NUM
};

enum CLOCK_DIV_NUM {
	CLOCK_DIV_1 = 0, // E17
	CLOCK_DIV_2,
	CLOCK_DIV_3,
	CLOCK_DIV_4,
	CLOCK_DIV_5,
	CLOCK_DIV_6,
	CLOCK_DIV_7,
	CLOCK_DIV_8,
	CLOCK_DIV_9,
	CLOCK_DIV_10,
	CLOCK_DIV_11,
	CLOCK_DIV_12,
	CLOCK_DIV_13,
	CLOCK_DIV_14,
	CLOCK_DIV_15,
	CLOCK_DIV_16,
	CLOCK_DIV_MAX
};

typedef enum CLOCK_FLH_IF_MODE {
	CLOCK_FLH_IF_MODE_NORMAL_SPEED = 0,
	CLOCK_FLH_IF_MODE_MAX_SPEED,
	CLOCK_FLH_IF_MODE_NUM
} CLOCK_FLH_IF_MODE_t;

typedef enum CLOCK_FLH_IF {
	CLOCK_FLH_IF_1400 = 0,
	CLOCK_FLH_IF_1200 = 1,
	CLOCK_FLH_IF_1066 = 2,
	CLOCK_FLH_IF_800  = 3,
	CLOCK_FLH_IF_667  = 4,
	CLOCK_FLH_IF_533  = 5,
	CLOCK_FLH_IF_450  = 6,
	CLOCK_FLH_IF_400  = 7,
	CLOCK_FLH_IF_200  = 8,
	CLOCK_FLH_IF_41p7 = 9,
	CLOCK_FLH_IF_33p3 = 10,
	CLOCK_FLH_IF_10p4 = 11,
	CLOCK_FLH_IF_RESET = 12,
	CLOCK_FLH_IF_NUM
} CLOCK_FLH_IF_t;

typedef enum CLOCK_PLL_SEL {
	CLOCK_PLL_466 = M_FW_TODO(),
	CLOCK_PLL_NUM
} CLOCK_PLL_SEL_t;

typedef enum CLOCK_ADJUST_CPU_CLOCK {
	CLOCK_ADJUST_CPU_CLOCK = M_FW_TODO(),
	CLOCK_RECOVER_CPU_CLOCK
} CLOCK_ADJUST_CPU_CLOCK_t;

#define M_CLOCK_GET_NEXT_DIV(Idx)					M_FW_TODO()
#define M_SET_FLL_VALUE(X)							M_FW_TODO()
#define M_CLOCK_ALL_CENTER0_SELECT_TO_REF_CLOCK()	M_FW_TODO()
#define M_CLOCK_ALL_CENTER0_SELECT_CLOCK(x)			M_FW_TODO()
#define M_CLOCK_ALL_CENTER0_GET_CLOCK()				M_FW_TODO()
#define M_CLOCK_GET_CENTER0_SRC(ubClockIPSel)	  	M_FW_TODO()
#define M_CLOCK_GET_CENTER0_DIVIER(ubClockIPSel)	M_FW_TODO()
#define M_CLOCK_BACKUP_CLOCK_SETTING(X)				(X = 0)
#define M_CLOCK_SWITCH_TO_LOW_FREQUENCY()			M_FW_TODO()
#define M_CLOCK_RECOVER_CLOCK_SETTING(X)			(X = 0)

#define M_GET_FLL_VALUE()							M_FW_TODO()
#define M_SET_FLL_VALUE(X)							M_FW_TODO()

#define M_CLK_SET_FPHY_RCLK(x) 						(R32_SYS0_CLK[R32_SYS0_CLK_CTRL2] = (x << CR_FLHR_CLK_SEL_SHIFT)| ((R32_SYS0_CLK[R32_SYS0_CLK_CTRL2]) & (~(CR_FLHR_CLK_SEL_MASK	<< CR_FLHR_CLK_SEL_SHIFT))))
#define M_CLK_SET_FPHY_WCLK(x) 						(R32_SYS0_CLK[R32_SYS0_CLK_CTRL2] = (x << CR_FLHW_CLK_SEL_SHIFT)| ((R32_SYS0_CLK[R32_SYS0_CLK_CTRL2]) & (~(CR_FLHW_CLK_SEL_MASK	<< CR_FLHW_CLK_SEL_SHIFT)))

#define M_CLK_GET_FPHY_RCLK() 						(R32_SYS0_CLK[R32_SYS0_CLK_CTRL2] >> CR_FLHR_CLK_SEL_SHIFT) & (CR_FLHR_CLK_SEL_MASK)
#define M_CLK_GET_FPHY_WCLK() 						(R32_SYS0_CLK[R32_SYS0_CLK_CTRL2] >> CR_FLHW_CLK_SEL_SHIFT) & (CR_FLHW_CLK_SEL_MASK)


#define M_SET_CLK_CTRL_CKEN(VAL) 					(R32_SYS0_CLK[R32_SYS0_CLK_CTRL6] |= (VAL))
#define M_CLR_CLK_CTRL_CKEN(VAL) 					(R32_SYS0_CLK[R32_SYS0_CLK_CTRL6] &= ~(VAL))

#elif PS5021_EN /* PS5017_EN */

#define CLOCK_667MHz								(667)
#define CLOCK_800MHz								(800)
#define CLOCK_533MHz								(533)
#define CLOCK_100MHz								(100)
#define CLOCK_20MHz									(20)
#define CLOCK_42MHz									(42)	//41.7

/*
 *  +-----------------------------------------------------------------------+
 *  |					CPU				     								|
 *  +-----------------------------------------------------------------------+
 */
// Src
#define M_CLK_SET_RCOSC_SEL(x)              		(R32_SYS0_ANALOG_CTRL[R32_SYS0_AIP_OSC_CTRL4] = (x) & SR_OSC_SYS_SEL)
#define M_CLK_GET_RCOSC_SEL()              			(R32_SYS0_ANALOG_CTRL[R32_SYS0_AIP_OSC_CTRL4] & SR_OSC_SYS_SEL)
#define CPU_CLK_SRC_1000                           	(0x3) // 1000MHz
#define CPU_CLK_SRC_667                           	(0x2) // 667MHz
#define CPU_CLK_SRC_400                           	(0x1) // 400MHz
#define CPU_CLK_SRC_200                           	(0x0) // 200MHz
#define CPU_CLK_SRC_NUM								(4)

// Div
#define M_CLK_SET_CPU_CLK_DIV(x)					do { \
													(R32_SYS0_SYS_CTRL[R32_SYS0_CLK_SEL] = (CPU_CLK_SW_BIT | ((x) & CPU_CLK_SEL))); \
													(R32_SYS0_SYS_CTRL[R32_SYS0_CLK_SEL] = ((x) & CPU_CLK_SEL)); \
													} while(0)
#define M_CLK_GET_CPU_CLK_DIV()						(R32_SYS0_SYS_CTRL[R32_SYS0_CLK_SEL] & CPU_CLK_SEL)
#define CPU_CLK_DIV_1          						(0x1) // 1/1
#define CPU_CLK_DIV_2         	 					(0x2) // 1/2
#define CPU_CLK_DIV_2_5          					(0x4) // 2/5
#define CPU_CLK_REF_20             					(0x8) // 20MHz

/*
 *  +-----------------------------------------------------------------------+
 *  |					SYS				     								|
 *  +-----------------------------------------------------------------------+
 */
// Src -> CPU Src + CPU Div

// Div
#define M_CLK_SET_SYS_CLK_DIV(x)                	(R32_SYS0_SYS_CTRL[R32_SYS0_CLK_SEL1] = (x) & SYSTEM_HCLK_SEL)
#define M_CLK_GET_SYS_CLK_DIV()                		(R32_SYS0_SYS_CTRL[R32_SYS0_CLK_SEL1] & SYSTEM_HCLK_SEL)
#define SYS_CLK_DIV_2               				(0x0)
#define SYS_CLK_DIV_3               				(0x1)
#define SYS_CLK_DIV_4               				(0x2)
#define SYS_CLK_DIV_8               				(0x3)
#define SYS_CLK_DIV_16              				(0x4)
#define SYS_CLK_DIV_NUM								(5)

/*
 *  +-----------------------------------------------------------------------+
 *  |					AES				     								|
 *  +-----------------------------------------------------------------------+
 */
// Src -> CPU Src

// Div
#define M_CLK_SET_AES_CLK_DIV(x)             		(R32_SYS0_IP_CTRL[R32_SYS0_AES_IP_CLK_SEL] = (x))
#define M_CLK_GET_AES_CLK_DIV()             		(R32_SYS0_IP_CTRL[R32_SYS0_AES_IP_CLK_SEL] & AES_CLK_SEL_BIT)
#define AES_CLK_DIV_2                        		(0x1)
#define AES_CLK_DIV_2_5                        		(0x0)
#define AES_CLK_DIV_NUM                  			(2)

/*
 *  +-----------------------------------------------------------------------+
 *  |					SPI				     								|
 *  +-----------------------------------------------------------------------+
 */
// Src -> CPU Src + CPU Div

// Div
#define M_CLK_SET_SPI_CLK_DIV(x)             		(R32_SYS0_MISC_CTRL[R32_SYS0_SPI_CFG] = (x) & SPI_CDIV_REG)
#define M_CLK_GET_SPI_CLK_DIV()             		(R32_SYS0_MISC_CTRL[R32_SYS0_SPI_CFG] & SPI_CDIV_REG)
#define SPI_CLK_DIV_8          						(0x1) // both 0x0 & 0x1 are div 8
#define SPI_CLK_DIV_16         						(0x2)
#define SPI_CLK_DIV_32         						(0x3)
#define SPI_CLK_DIV_NUM         					(4)

/*
 *  +-----------------------------------------------------------------------+
 *  |					FLH				     								|
 *  +-----------------------------------------------------------------------+
 */
// Src
#define FLH_CLK_SRC_200                       		(0x0)
#define FLH_CLK_SRC_400                       		(0x1)
#define FLH_CLK_SRC_533                       		(0x2)
#define FLH_CLK_SRC_667                       		(0x3)
#define FLH_CLK_SRC_800                       		(0x4)
#define FLH_CLK_SRC_1066                      		(0x5)
#define FLH_CLK_SRC_1200                      		(0x6)
#define FLH_CLK_SRC_1600                      		(0x7)
#define FLH_CLK_SRC_NUM								(0x8)

// Div -> define in Fip_api.h

//===========================================================================
#define CPU_CLK_1000								(0)
#define CPU_CLK_667									(1)
#define CPU_CLK_400									(2)
#define CPU_CLK_200									(3)
#define CPU_CLK_20                    				(4)

#define IP_DIV_NO_CHANGE                        	(0xFF)
#define CLOCK_MODE_SEL								(CLOCK_MODE_CPU1000_SYS500_AES500)

typedef enum CLOCK_MODE {
	CLOCK_MODE_CPU1000_SYS500_AES500 = 0,
	CLOCK_MODE_CPU667_SYS333_AES333 = 1,
	CLOCK_MODE_CPU400_SYS200_AES200 = 2,
	CLOCK_MODE_CPU200_SYS100_AES100 = 3,
	CLOCK_MODE_NUM
} CLOCK_MODE_t;

enum CLOCK_IP_GEN_ID {
	CLOCK_IP_CPU = 0,
	CLOCK_IP_SYS = 1,
	CLOCK_IP_AES = 2,
	CLOCK_IP_SPI = 3,
	CLOCK_IP_ID_NUM
};

typedef enum CLOCK_FLH_IF {
	CLOCK_FLH_IF_1600 = 0,
	CLOCK_FLH_IF_1200 = 1,
	CLOCK_FLH_IF_1066 = 2,
	CLOCK_FLH_IF_800  = 3,
	CLOCK_FLH_IF_667  = 4,
	CLOCK_FLH_IF_533  = 5,
	CLOCK_FLH_IF_400  = 6,
	CLOCK_FLH_IF_333  = 7,
	CLOCK_FLH_IF_200  = 8,
	CLOCK_FLH_IF_166  = 9,
	CLOCK_FLH_IF_50   = 10,
	CLOCK_FLH_IF_41p7 = 11,	// 41.7
	CLOCK_FLH_IF_33p3 = 12,	// 33.3
	CLOCK_FLH_IF_10p4 = 13,	// 10.4
	CLOCK_FLH_IF_NUM
} CLOCK_FLH_IF_t;

typedef enum CLOCK_ADJUST_CPU_CLOCK {
	CLOCK_ADJUST_CPU_CLOCK = 0,
	CLOCK_RECOVER_CPU_CLOCK
} CLOCK_ADJUST_CPU_CLOCK_t;

typedef struct {
	U8	ubCPUClockSrc;
	U8	ubCPUClockDiv;
	U8	ubCPUAdjustFlag;
} ClockAdjustCPUClock_t;

#define CLOCK_SELECT_10M							(0)
#define CLOCK_SELECT_40M							(1)
#define CLOCK_SELECT_50M							(2)
#define CLOCK_SELECT_166M							(3)
#define CLOCK_SELECT_200M							(4)
#define CLOCK_SELECT_333M							(5)
#define CLOCK_SELECT_400M							(6)
#define CLOCK_SELECT_533M							(7)
#define CLOCK_SELECT_667M							(8)
#define CLOCK_SELECT_800M							(9)

#define M_CLOCK_BACKUP_CLOCK_SETTING(X)				do { \
													(X) = R32_SYS0_SYS_CTRL[R32_SYS0_CLK_SEL] & CPU_CLK_SEL; \
													} while(0)
#define M_CLOCK_SWITCH_TO_LOW_FREQUENCY()			do { \
													M_CLK_SET_CPU_CLK_DIV(CPU_CLK_REF_20); \
													M_RTT_IDLE_PC(2500); \
													} while(0)
#define M_CLOCK_RECOVER_CLOCK_SETTING(X)			do { \
													M_CLK_SET_CPU_CLK_DIV(X); \
													M_RTT_IDLE_PC(2500); \
													} while(0)


#else /* PS5017_EN */

// Clock source
#define OSC1_KOUT1_800					(0xA)	//P0C 800
#define OSC1_KOUT2_533					(0x8)	//P0A 533
#define OSC1_KOUT3_100					(0x9)	//P0B 100
#define OSC2_KOUT1_667					(0xC)	//P1A 667
#define OSC2_KOUT6_41p7					(0x0)	//R0A 41.7

#define	PLL_CLKOUT1						(0xE)
#define	PLL_CLKOUT2						(0xD)

#define CLOCK_667MHz					(667)
#define CLOCK_800MHz					(800)
#define CLOCK_533MHz					(533)
#define CLOCK_100MHz					(100)
#define CLOCK_42MHz						(42)	//41.7
#define CLOCK_DEFAULT					(0)

#define CLOCK_SELECT_10M				(0x00)
#define CLOCK_SELECT_40M				(0x01)
#define CLOCK_SELECT_50M				(0x02)
#define CLOCK_SELECT_166M				(0x03)
#define CLOCK_SELECT_200M				(0x04)
#define CLOCK_SELECT_333M				(0x05)
#define CLOCK_SELECT_400M				(0x06)
#define CLOCK_SELECT_533M				(0x07)
#define CLOCK_SELECT_667M				(0x08)
#define CLOCK_SELECT_800M				(0x09)

#define CLOCK_DIVIDER_GAP				(2)

/*
 *
 *  +-------------------------------------------+
 *  |                Clock Table                |
 *  |-------------------------------------------|
 *  |                | CLOCK_IP_0 | SYSx2       |
 *  |                | CLOCK_IP_1 | CPU         |
 *  |                | CLOCK_IP_2 | SPI         |
 *  |                | CLOCK_IP_3 | AES         |
 *  | CLOCK_CENTER_0 | CLOCK_IP_4 | SHA         |
 *  |                | CLOCK_IP_5 | ECC         |
 *  |                | CLOCK_IP_6 | LZSS        |
 *  |                | CLOCK_IP_7 | TDBG        |
 *  |                | CLOCK_IP_8 | TRACE       |
 *  |                | CLOCK_IP_9 | ZQ          |
 *  |----------------+------------+-------------|
 *  |                | CLOCK_IP_0 | FLH CH0,1 R |
 *  |                | CLOCK_IP_1 | FLH CH0,1 W |
 *  |                | CLOCK_IP_2 | FLH CH2,3 R |
 *  |                | CLOCK_IP_3 | FLH CH2,3 W |
 *  | CLOCK_CENTER_1 | CLOCK_IP_4 | SMS         |
 *  |                | CLOCK_IP_5 | No Use      |
 *  |                | CLOCK_IP_6 | No Use      |
 *  |                | CLOCK_IP_7 | No Use      |
 *  |                | CLOCK_IP_8 | No Use      |
 *  |                | CLOCK_IP_9 | No Use      |
 *  +-------------------------------------------+
 *
 */

enum CLOCK_CENTER_ID {
	CLOCK_CENTER_0 = 0,
	CLOCK_CENTER_1,
	CLOCK_CNETER_ID_NUM
};

enum CLOCK_IP_GEN_ID {
	CLOCK_IP_0 = 0,
	CLOCK_IP_1,
	CLOCK_IP_2,
	CLOCK_IP_3,
	CLOCK_IP_4,
	CLOCK_IP_5,
	CLOCK_IP_6,
	CLOCK_IP_7,
	CLOCK_IP_8,
	CLOCK_IP_9,
	CLOCK_IP_ID_NUM
};

enum CLOCK_DIV_NUM {
	CLOCK_DIV_2 = 0,
	CLOCK_DIV_3,
	CLOCK_DIV_4,
	CLOCK_DIV_5,
	CLOCK_DIV_6,
	CLOCK_DIV_7,
	CLOCK_DIV_8,
	CLOCK_DIV_9,
	CLOCK_DIV_10,
	CLOCK_DIV_11,
	CLOCK_DIV_12,
	CLOCK_DIV_13,
	CLOCK_DIV_14,
	CLOCK_DIV_15,
	CLOCK_DIV_16,
	CLOCK_DIV_1,
	CLOCK_DIV_MAX
};

typedef enum CLOCK_FLH_IF {
	CLOCK_FLH_IF_800 = 0,
	CLOCK_FLH_IF_667 = 1,
	CLOCK_FLH_IF_533 = 2,
	CLOCK_FLH_IF_400 = 3,
	CLOCK_FLH_IF_333 = 4,
	CLOCK_FLH_IF_200 = 5,
	CLOCK_FLH_IF_166 = 6,
	CLOCK_FLH_IF_50 = 7,
	CLOCK_FLH_IF_41p7 = 8, // 41.7
	CLOCK_FLH_IF_33p3 = 9, // 33.3
	CLOCK_FLH_IF_10p4 = 10,	// 10.4
	CLOCK_FLH_IF_NUM
} CLOCK_FLH_IF_t;

typedef enum CLOCK_PLL_SEL {
	CLOCK_PLL_466 = 0,
	CLOCK_PLL_NUM
} CLOCK_PLL_SEL_t;

typedef enum CLOCK_ADJUST_CPU_CLOCK {
	CLOCK_ADJUST_CPU_CLOCK = 0,
	CLOCK_RECOVER_CPU_CLOCK
} CLOCK_ADJUST_CPU_CLOCK_t;

typedef struct {
	U8	ubCPUClockSrc;
	U8	ubCPUClockDiv;
	U8	ubCPUAdjustFlag;
} ClockAdjustCPUClock_t;

extern ClockAdjustCPUClock_t gClockAdjustCPUClock;

#define	M_SET_SEL_CLK_CTRL(CENTER_IP,SRC)			(R8_SYS0_CLK[(CENTER_IP)] = (((SRC) & CR_CTR0_SEL_0_MASK) << CR_CTR0_SEL_0_SHIFT))
#define M_SET_CLK_CTRL_DIV_VAL(CENTER_IP,VAL)		(R8_SYS0_CLK[(CENTER_IP)] = (((VAL) & CR_CTR0_DIV_0_MASK) << CR_CTR0_DIV_0_SHIFT))
#define M_CLOCK_GET_NEXT_DIV(Idx)					((Idx == CLOCK_DIV_1) ? CLOCK_DIV_2 : (((Idx + 2) * 2) - 2))
#define M_CLOCK_GET_CENTER0_SRC(ubClockIPSel)		(R8_SYS0_CLK[(R8_SYS0_CLK_CTRL0_SEL0 + ubClockIPSel)] & CR_CTR0_SEL_0_MASK)
#define M_CLOCK_GET_CENTER0_DIVIER(ubClockIPSel) 	(R8_SYS0_CLK[(R8_SYS0_CLK_CTRL0_DIV_VAL0 + ubClockIPSel)] & CR_CTR0_DIV_0_MASK)
#define	M_CLK_SET_PCIE_SYSTEM_CLOCK(VAL)			(R32_SYS0_CLK[R32_SYS0_CLK_PCIE_SYS_CHG] = PCIE_SYSCLK_FREQ_VLD_BIT | (((VAL) & PCIE_SYSCLK_FREQ_MASK) << PCIE_SYSCLK_FREQ_SHIFT))
#define M_GET_FLL_VALUE()							((R32_SYS0_CLK[R32_SYS0_CLK_CFG0] >> CR_FLL_VAL_SHIFT) & CR_FLL_VAL_MASK)
#define M_SET_FLL_VALUE(X)							do { \
													R32_SYS0_CLK[R32_SYS0_CLK_CFG0] &= ~(CR_FLL_VAL_MASK << CR_FLL_VAL_SHIFT); \
													R32_SYS0_CLK[R32_SYS0_CLK_CFG0] |= ((X) & CR_FLL_VAL_MASK) << CR_FLL_VAL_SHIFT; \
													} while(0)
#define M_CLOCK_ALL_CENTER0_SELECT_TO_REF_CLOCK()	do { \
													(R16_SYS0_CLK[R16_SYS0_CLK_CTR0_SEL] = 0); \
													__asm("DSB"); \
													} while(0)
#define M_CLOCK_ALL_CENTER0_SELECT_CLOCK(x)			do { \
													(R16_SYS0_CLK[R16_SYS0_CLK_CTR0_SEL] = (U16)(x)); \
													__asm("DSB"); \
													} while(0)
#define M_CLOCK_ALL_CENTER0_GET_CLOCK()				(R16_SYS0_CLK[R16_SYS0_CLK_CTR0_SEL])
#define M_CLOCK_BACKUP_CLOCK_SETTING(X)				do { \
													(X) = (U32)(M_CLOCK_ALL_CENTER0_GET_CLOCK()); \
													} while(0)
#define M_CLOCK_SWITCH_TO_LOW_FREQUENCY()			do { \
													M_CLOCK_ALL_CENTER0_SELECT_TO_REF_CLOCK(); \
													} while(0)
#define M_CLOCK_RECOVER_CLOCK_SETTING(X)			do { \
													M_CLOCK_ALL_CENTER0_SELECT_CLOCK(X); \
													} while(0)
#endif /* PS5017_EN */

#if ((USB == HOST_MODE) && (FW_CATEGORY_CUSTOMER == CUSTOMER_MICRON_NICKS) && (FW_CATEGORY_FLASH == FLASH_N48R_QLC))
#if (!BURNER_MODE_EN)
U8 GetOSCTargetTrimValue(OSCx_TRIM_t *pOSCxDefault, OSCx_TRIM_t *pOSCxAdjust);
void TrimOSCConfig(OSC_TRIM_SEL_t ubOSCSel);
#endif /* (!BURNER_MODE_EN) */
#elif(!BURNER_MODE_EN)//Duson Porting BICS5 Add
U8 GetOSCTargetTrimValue(OSCx_TRIM_t *pOSCxDefault, OSCx_TRIM_t *pOSCxAdjust);
void TrimOSCConfig(OSC_TRIM_SEL_t ubOSCSel);
#else
#define GetOSCTargetTrimValue(...)		(0)
#define TrimOSCConfig(...)				(0)
#endif

#if PS5017_EN /* PS5017_EN */
AOM_COMMON void ClockSetIPClock(U8 ubClkCenter, U8 ubClkIpSel, U8 ubClkSrc, U8 ubClkDiv); //Any change in ClockSetIPClock() should sync to ClockSetIPClockForDLMC()
AOM_INIT void ClockSetIPFlhRW(U8 ubClockIpSel, U8 ubMode);
#elif PS5021_EN
void ClockChangeBaseOnHCLK(void);
void ClockFlhClockSetting(CLOCK_FLH_IF_t FLHIFSel);
void ClockCPUClockSetting(U8 ubCpuClkSel);
U8 ClockMPClockToFlashInterfaceMapping(U8 ubFlashDataRate);
void ClockIPClockDivSetting(U8 ubHclkDiv, U8 ubAesClkDiv, U8 ubSPIClkDiv);
U32 ClockGetCPUClkMHz(void);
U32 ClockGetSysClkMHz(void);
U32 ClockGetFlhClkMHz(void);
U32 ClockGetSPIClkMHz(void);
U32 ClockGetAESClkMHz(void);
#if Enable_E21AB_FW_WORKAROUND_1
void ClockPMUOSCIssueFWWorkaround(void);
#else /* Enable_E21AB_FW_WORKAROUND_1 */
define ClockPMUOSCIssueFWWorkaround(...)	(0)
#endif /* Enable_E21AB_FW_WORKAROUND_1 */
#else /* PS5017_EN */
AOM_INIT void ClockSetIPClock(U8 ubClkCenter, U8 ubClkIpSel, U8 ubClkSrc, U8 ubClkDiv); //Any change in ClockSetIPClock() should sync to ClockSetIPClockForDLMC()
AOM_LPM void ClockSetIPClockForDLMC(U8 ubClockCenter, U8 ubClockIpSelect, U8 ubClockSrc, U8 ubClockDiv); //Any change in ClockSetIPClockForDLMC() should sync to ClockSetIPClock()
AOM_INIT void ClockSetPLL(void);
AOM_INIT void ClockOSCConfig(void);
AOM_INIT void ClockAdjustFromIDPage(void);
#endif /* PS5017_EN */

AOM_VUC void ClockGetReadWriteFrequency(U8 *pubReadClock, U8 *pubWriteClock);
AOM_INIT void ClockSwitchModeAndACTiming(CLOCK_FLH_IF_t FLHIFSel);
#if BOOTLOADER_EN
AOM_INIT void ClockSwitchModeAndACTimingInZQCInit(CLOCK_FLH_IF_t FLHIFSel);
#endif /* BOOTLOADER_EN */
AOM_INIT void ClockIPConfig(void);
AOM_COMMON U8 ClockGetDiv(U8 ubClkCenter, U8 ubClkIPSel);  // consider Power state transition time, pls don't change overlay. -YK
AOM_COMMON U8 ClockGetSrc(U8 ubClkCenter, U8 ubClkIPSel);  // consider Power state transition time, pls don't change overlay. -YK
AOM_INIT_4 U16 ClockGetIPClockSrcMHz(U8 ubClockCenter, U8 ubClockIPSelect);
AOM_INIT_4 U16 ClockGetIPClockMHz(U8 ubClockCenter, U8 ubClockIPSelect);
AOM_INIT void ClockSetPCIeSystemClock(void);
AOM_SECURITY void ClockAdjustCPUClockToAccessROM(CLOCK_ADJUST_CPU_CLOCK_t ModeSelect);

extern U8 gubClockSrcCenter0[CLOCK_IP_ID_NUM];

#endif /* _CLK_API_H_ */
