#ifndef _TCG_DRBG_H_
#define _TCG_DRBG_H_
#include "common/typedef.h"
#include "aom/aom_api.h"

#define TCG_RNG_NOISE_LEN               (4)
#define TCG_RNG_INIT_PERIOD             (2048)
//#define TCG_RNG_LN_2_INV                (1.45)// 1/ln(2), where ln(2)=0.69
#define TCG_RNG_LN_2_INV_AMPLIFIED      (389231411) //TCG_RNG_LN_2_INV * (1 << 28)  
#define TCG_RNG_POOL_BUF_SIZE           (32)
#define TCG_RNG_SEED_BUF_SIZE           (36)
#define TCG_RNG_MIN_ENTROPY             (4)//minimum acceptable entropy for RNG providing entropy input
#define TCG_DRBG_CTR_AES_DF_EN          (FALSE)
#define TCG_DRBG_CTR_AES_PS_EN          (FALSE)
#define TCG_DRBG_CTR_AES_AD_EN          (TRUE)

#define TCG_DRBG_MAX_KEY_LEN            (32)//256 bits
#define TCG_DRBG_MAX_V_LEN              (32)//256 bits
#define TCG_DRBG_ENTROPY_LEN            (128)//for SHA 256
#define TCG_DRBG_NONCE_LEN              (128)
#define TCG_DRBG_MAX_RBG_LEN            (128)//(64)//mod by Andrew.Lin for support max RSA 2048
#define M_TCG_BIT_TO_BYTE(x)               ((x)>>3)
#define M_TCG_BYTE_TO_BIT(x)               ((x)<<3)

#define TCG_DRBG_SEC_BUFFER_SOURCE_OFFSET (0)
#define TCG_DRBG_SEC_BUFFER_TARGET_OFFSET (1024)
#define TCG_DRBG_RNG_BUFFER_OFFSET (2048)
#define TCG_DRBG_RNG_BUFFER_SIZE (2048)
#define TCG_FLOAT_AMPLIFIER (1<<28)

#define TCG_DRBG_TEST_CNT_NON_KAT		(0xFF)
#define TCG_DRBG_TEST_CNT_KAT1	(0x00)
#define TCG_DRBG_TEST_CNT_KAT2	(0x01)
#define TCG_DRBG_TEST_CNT_KAT3	(0x02)
// #define RNG_DBUF_BASE               (SHARED_DBUF_SEC_AES_SHA_BUF_BASE)




typedef enum DRBGStatusTag {
	DRBGSOK,
	DRBGSError,
	DRBGSInavaildReqSS,//invaild request instantiation security strength
	DRBGSPersonalStringTooLong,//persionalization string too long
	DRBGSCatastrophicFail,//Catastrophic failure of the entropy source
} DRBGStatus;

//------------------------------------------------
typedef enum DRBGSecurityStrengthTag {
	DRBGSsInavaild,
	DRBGSs112,
	DRBGSs128,
	DRBGSs192,
	DRBGSs256
} DRBGSecurityStrength;

//------------------------------------------------
typedef enum DRBGGenerateBaseTag {
	DRBGBaseInvalid,
	DRBGBaseHash,
	DRBGBaseHashMAC,
	DRBGBaseTDEA,
	DRBGBaseAES128,
	DRBGBaseAES192,
	DRBGBaseAES256,// 128, 192, 256
	DRBGBaseDualECC// 256, 384, 521
} DRBGGenerateBase;

//------------------------------------------------


typedef struct TcgDRBGInfo_t TcgDRBGInfo_t, *TcgDRBGInfoPtr_t;
typedef struct TcgDRBGSeedMaterial_t TcgDRBGSeedMaterial_t, *TcgDRBGSeedMaterialPtr_t;
typedef struct TcgDRBGState_t TcgDRBGState_t, *TcgDRBGStatePtr_t;

typedef struct TcgDRBGKatStruct_t TcgDRBGKatStruct_t, *TcgDRBGKatStructPtr_t;
typedef struct TcgDRBGStruct_t TcgDRBGStruct_t, *TcgDRBGStructPtr_t;



struct TcgDRBGInfo_t {
	DRBGGenerateBase Ebase;
	U32 ulReseedInterval;
	U16 uwSecurityStrength;
	U16 uwMaxSecurityStrength;
	U16 uwOutBlockLen;
	U16 uwKeyLen;
	U16 uwMinEntropyLen;
	U16 uwMaxEntropyLen;
	U16 uwMaxPersonalStringLen;
	U16 uwMaxAdditionalLen;
	U16 uwMaxRequestBitLen;
	U16 uwSeedLen;

	U8 ubIsSupportPr; //PredictionResistanceFlag;
	U8 ubCtrCnt;
	U16 uwRsv;
};

struct TcgDRBGSeedMaterial_t {
	U16 uwPersonalStringLen;
	U16 uwAdditionalInputLen;
	U16 uwEntropyInputLen;
	U16 uwNonceInputLen;
	U8 *pubEntropyInput;
	U8 *pubNonce;
	U8 *pubPersonalString;
	U8 *pubAdditionalInput;

};

struct TcgDRBGState_t {
	U8 ubErrorState;
	U8 ubRsv;
	U16 uwRsv;
	U32 ulReseedCnt;
	U8 ubValue[TCG_DRBG_MAX_V_LEN];
	U8 ubKey[TCG_DRBG_MAX_KEY_LEN];
};


struct TcgDRBGKatStruct_t {
	U8  *pubKatEntropyInput;
	U8  *pubKatEntropyPr1;
	U8  *pubKatEntropyPr2;
	U8  *pubKatNonce;

	U32 ulKatBuf[32 / 4]; // RNG_DRBGKAT_uplBuf[32 / 4];
};


struct TcgDRBGStruct_t {
	TcgDRBGInfo_t Info;                                    // gDRBGInfo;
	TcgDRBGSeedMaterial_t DRBGSeedMaterial;                      // gDRBGSeedMaterial;
	TcgDRBGState_t State;                                   // gDRBGState;
	TcgDRBGKatStruct_t Kat;



	U8 ubNonce[256];                              // gNonce[256];
	U8 ubEntropyInput[256];
	U8 ubSeedMaterial[256];                      // gSeedMaterial[256];
	U8 ubOldRng[256];                            // gOldRNG[256];
	U8 ubEm[256];                                 // gEM[256];
	U8 ubRngPoolBuf[64];
	U8 ubRngSeedBuf[64];

	U8 ubEntropy;
	U8 ubIsEnableRng;
	U8 ubTestCnt;
	U8 ubMacInput[445];                          // gMacInput[448];
	U8 ubPad[128];                                // gpad[128];

	U64 uoSampleCntBase8;
	U32 ulMaxRngVal;
	U32 ulTotalCnt;

	U32 ulDRBGBufferBase;
};


extern AOM_TCG_INIT void TcgRngGenSeed(U8 *pubRng) ;
extern AOM_TCG_INIT void TcgRngSha(U8 *pubSrc, U8 *pubDst, U32 ulLen, TcgDRBGStructPtr_t DRBG) ;
extern AOM_TCG_INIT void TcgRngEstimator(TcgDRBGStructPtr_t DRBG, U32 ulSrcLen, U8 *pubEntropyInput, U16 *puwOutLen) ;
extern AOM_TCG_INIT void TcgRngEntropyMonitor(TcgDRBGStructPtr_t DRBG, U8 *pubIsEnableRng, U64 *puoEntropyCntBase8) ;
extern AOM_TCG_INIT void TcgRngInit(TcgDRBGStructPtr_t DRBG) ;
extern AOM_TCG_INIT void TcgRngDRBGInstantiate(TcgDRBGStructPtr_t DRBG, DRBGGenerateBase Ebase, DRBGSecurityStrength ReqSs, U8 ubIsEnablePr, U8 *pubPString, U16 uwPStringLen) ;
extern AOM_TCG_INIT void TcgRngDRBGInit(TcgDRBGStructPtr_t DRBG, DRBGGenerateBase Ebase) ;
extern AOM_TCG_INIT void TcgRngGetNonce(TcgDRBGStructPtr_t DRBG, U16 *puwOutputLen) ;
extern AOM_TCG_INIT void TcgRngLogBaseE(U64 *puoRes, U64 uoN) ;
extern AOM_TCG_INIT void TcgRngGenerate(TcgDRBGStructPtr_t DRBG, U8 *pubDst, U64 puoReqBits, DRBGSecurityStrength ReqSs, U8 ubIsEnablePr, U8 *pubAdditionalInput, U16 uwAdditionalInputLen) ;
extern AOM_TCG_INIT void TcgRngGetEntropyInput(TcgDRBGStructPtr_t DRBG, U16 *puwOutputLen) ;
extern AOM_TCG_INIT void TcgRngDRBGReseed(TcgDRBGStructPtr_t DRBG, U8 *pubAdditionalInput, U16 uwAdditionalInputLen) ;
extern AOM_TCG_INIT void TcgRngHmac(U8 ubShaType, U8 *pubKey, U32 ulKeyLength, U8 *ubInput, U32 ulInputLength, U8 *pubOutput, TcgDRBGStructPtr_t DRBG) ;
extern AOM_TCG_INIT void TcgRngUpdateState(TcgDRBGStructPtr_t DRBG, U8 *pubProvidedData, U16 uwInputLen) ;
extern AOM_TCG_INIT void TcgRngInstantiate(TcgDRBGStructPtr_t DRBG) ;
extern AOM_TCG_INIT void TcgRngDRBGGenerate(TcgDRBGStructPtr_t DRBG, U32 ulReqBits, U8 *pubDst, TcgDRBGSeedMaterialPtr_t DRBGSeedMaterial) ;
extern AOM_TCG_INIT void TcgRngReseed(TcgDRBGStructPtr_t DRBG, TcgDRBGSeedMaterialPtr_t DRBGSeedMaterial) ;
extern AOM_TCG_INIT void TcgRngBlockEncrypt(TcgDRBGStructPtr_t DRBG, U8 *pubOutputBlock, U8 ubInputLen) ;
extern AOM_TCG_INIT void TcgRngGenerator(TcgDRBGStructPtr_t DRBG, U8 *pubDst, U16 uwLen) ;

// extern AOM_TCG_INIT void TcgRngDRBGKat(TcgDRBGStructPtr_t DRBG) ;
#endif /* _TCG_DRBG_H_ */

