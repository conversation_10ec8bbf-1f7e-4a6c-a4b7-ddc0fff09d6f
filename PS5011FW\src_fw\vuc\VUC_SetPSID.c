#include "init/fw_init.h"
#include "init/fw_preformat.h"
#include "hal/dmac/dmac_pop_cmd.h"
#include "host/VUC_handler.h"
#include "table/sys_block/sys_block_api.h"
#include "vuc/VUC_SetPSID.h"
#include "vuc/VUC_DumpTable.h"
#include "hal/security/security_api.h"
#include "tcg/tcg_api.h"

#if (TCG_EN && !LPM3_LOADER && !RDT_MODE_EN  && !BOOTLOADER_MODE_EN)

void VUCTcgPsid(VUC_OPT_HCMD_PTR_t pCmd)
{
	U8 ubSubFeature = pCmd->vuc_sqcmd.vendor.TcgPsid.ubSubFeature;

	if (TRUE == gFlhEnv.ulFlashDefaultType.BitMap.btNoSupport) {
		pCmd->ubState = CMD_ERROR;
		gVUCVar.ubVucCmdErrStatus = HOST_ERROR_FEATURE;
		return;
	}

	switch (ubSubFeature) {
	case VUC_TCG_PSID_SET_PSID_ID: // Data-In
		VUCSetPsid(pCmd);
		break;
	case VUC_TCG_PSID_LOAD_PSID_DIGEST_ID: // Non-Data
		VUCLoadPsidDigest(pCmd);
		break;
	default:
		break;
	}
}

void VUCSetPsid(VUC_OPT_HCMD_PTR_t pCmd)
{
	if (!BURNER_MODE_EN && (HOST_MODE != USB)) {
		M_UART(VUC_, "\nVUC_Set_Psid");

		U32 ulBufAddr;

		if ((FALSE == gpVT->OPAL.btVUCSetPsid)
			|| ((VENDOR_WRITEDATA != pCmd->vuc_sqcmd.vendor.ubOPCode) && (VENDOR_ENCRYPT_WRITE_DATA != pCmd->vuc_sqcmd.vendor.ubOPCode))) {
			pCmd->ubState = CMD_ERROR;
			gVUCVar.ubVucCmdErrStatus = HOST_ERROR_FEATURE;
			return;
		}

		// Release ST3C cache
		BufferAdjustST3CCache_AOM_TABLE(BIT_BUFFER_FLOW_VUC_READ_WRITE, GET_BUFFER);
		// Allocate buffer
		BufferAllocateFWLBPBLink(FWLB_VUC_READ_WRITE_BIT, ALLOCATE_NO_USE_CTAG, ALLOCATE_NO_USE_LCA);
		ulBufAddr = M_LB_TO_ADDR(LB_ID_FW, gFWLBMgr.Type[FWLB_VUC_READ_WRITE].uwLBOffset);

		TcgSetPsidAll(ulBufAddr, gulVUCBufAddr);

		// Release buffer
		BufferFreeFWLBPBLink(FWLB_VUC_READ_WRITE_BIT);
		// Return buffer resource to ST3C
		BufferAdjustST3CCache_AOM_TABLE(BIT_BUFFER_FLOW_VUC_READ_WRITE, RETURN_BUFFER);
		pCmd->ubState = FW_PROCESS_DONE;
	}
	else {
		pCmd->ubState = CMD_ERROR;
		gVUCVar.ubVucCmdErrStatus = HOST_ERROR_FEATURE;
	}
}

void VUCLoadPsidDigest(VUC_OPT_HCMD_PTR_t pCmd)
{
	if (BURNER_MODE_EN) {
		U8 ubLoadVTSuccess = FALSE;
		M_UART(VUC_, "\nVUC_Load_Psid_Digest");

		if ((VENDOR_NODATA != pCmd->vuc_sqcmd.vendor.ubOPCode) && (VENDOR_ENCRYPT_NO_DATA != pCmd->vuc_sqcmd.vendor.ubOPCode)) {
			pCmd->ubState = CMD_ERROR;
			gVUCVar.ubVucCmdErrStatus = HOST_ERROR_FEATURE;
			return;
		}

		SystemAreaScanBlk();

		if (gSystemArea.ubSystemBlockNum) {
			/* Scan VT */
			if (PASS == VUCScanRUT()) {
				VUC_SCAN_VT();

				if (VT_CHECK_MARK_BTCM == gpVT->ulCheckMark) {
					ubLoadVTSuccess = TRUE;
					gpuwVBRMP[gpVT->InfoUnit.uwUnit.B.uwUnit].uwAll = gpVT->uwInitInfoVB;

					/* Read InfoBlk */
					if (SUCCESS == SysAreaRead4KEntry(SYSAREA_SCAN_RECORD_BUFFER, SPARE_LCA_SYSTEM, gSystemArea.SystemBlock[0], 0, 0, SYSTEM_AREA_HEADER_MODE)) {
						SystemBlock_t *pInfoBlk = (SystemBlock_t *)SYSAREA_SCAN_RECORD_BUFFER;
						M_TCG_INIT_INFOBLK_VARIABLE(pInfoBlk);
						memset((void *) SYSAREA_SCAN_RECORD_BUFFER, 0x00, SIZE_4KB);

						/* Read Psid Digest */
						FTLPreFormatReadPsidDigest();
					}
				}
			}
		}

		if ((TRUE == gPreformat.ubValidPsid) || ((TRUE == ubLoadVTSuccess) && (TRUE == gpVT->OPAL.btVUCSetPsid))) {
			// Psid Exist or not set Psid
			pCmd->ubState = FW_PROCESS_DONE;
		}
		else {
			pCmd->ubState = CMD_ERROR;
			gVUCVar.ubVucCmdErrStatus = HOST_ERROR_FEATURE;
		}
	}
	else {
		pCmd->ubState = CMD_ERROR;
		gVUCVar.ubVucCmdErrStatus = HOST_ERROR_FEATURE;
	}
}

#endif /* (TCG_EN && !LPM3_LOADER && !RDT_MODE_EN  && !BOOTLOADER_MODE_EN) */
