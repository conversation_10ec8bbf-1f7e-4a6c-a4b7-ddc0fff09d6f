/** @file flash_spec.h
 *  @brief
 *
 *
 *  <AUTHOR>  @bug No know bugs.
 */
#ifndef _FLASH_SPEC_H_
#define _FLASH_SPEC_H_
#include "symbol.h"
#include "env.h"

//******************************************
//	Flash
//******************************************
#if !VS_SIM_EN
#define _TLC_MODE_TSB_BICS				(FALSE)
#define _TLC_MODE_MICRON_B0KB			(FALSE)
#define _MULTI_DIE_EN					(FALSE) //FW no use

#define _CH_NUMBER						(PS5017_EN ? 2 : 4) //max:4
#define _CE_NUMBER						(2) //for each channel //max : 8

#if _MULTI_DIE_EN
#if ((_TLC_MODE_TSB_BICS == 1) || (_TLC_MODE_MICRON_B0KB == 1))
#define _LUN_NUMBER						(2) //max : 4 //tlc & multidie
#else /* ((_TLC_MODE_TSB_BICS == 1) || (_TLC_MODE_MICRON_B0KB == 1)) */
#define _LUN_NUMBER						(4) //max : 4 //mlc % multidie
#endif /* ((_TLC_MODE_TSB_BICS == 1) || (_TLC_MODE_MICRON_B0KB == 1)) */
#else /* _MULTI_DIE_EN */
#define _LUN_NUMBER						(1) //max : 1 //singledie
#endif /* _MULTI_DIE_EN */

#define _PLANE_NUMBER					(4)  //max :4
#define _BLOCK_PER_PLANEBANK			(1024) //max : 1024
#if _TLC_MODE_TSB_BICS //TSB tlc
#define _PAGE_PER_BLOCK					(64 / 2 * 3) //max : 384
#elif _TLC_MODE_MICRON_B0KB // B0KB tlc
#define _PAGE_PER_BLOCK					(1536) //max : 1536
#else //TSB mlc
#define _PAGE_PER_BLOCK					(64) //max : 256
#endif
#define _BYTES_PER_PAGES				(16384) // 16kbytes
#define _BYTES_PER_SPARE_PAGES			(1280)

#define _RESERVE_BLOCK_PER_PLANEBANK	(0) //max : 45  //[z_cmt] Enable for LaterBad in the future.
#define _GLOBAL_CE_NUMBER				(_CE_NUMBER * _CH_NUMBER)
#endif /* !VS_SIM_EN */

#define BIG_RANGE_EN 				(TRUE)

#define QLC_DISK_SIZE_IN_MB_PER_CE  (170*1024)
#if BIG_RANGE_EN
#if VS_SIM_EN
#define DISK_SIZE_IN_MB				(512)		///< Disk Size (Maga Bytes)
#else /* VS_SIM_EN */
#define DISK_SIZE_IN_MB				(8*1024)	///< Disk Size (Maga Bytes)
#endif /* VS_SIM_EN */
#define DISK_SIZE_IN_MB_LOG			(9)
#else /* BIG_RANGE_EN */
#define DISK_SIZE_IN_MB				(512)		///< Disk Size (Maga Bytes)
#endif /* BIG_RANGE_EN */
#define PLANE_NUMBER				_PLANE_NUMBER	///< the Number of Planes
#define CE_NUMBER					_CE_NUMBER		//[z_cmt] This is local CE. For global CE pls do "CE * CH".
#define BLOCK_NUMBER_PER_CE_ONE_LUN	_BLOCK_PER_PLANEBANK * _PLANE_NUMBER
#define BLOCK_PER_PLANEBANK			_BLOCK_PER_PLANEBANK
#define PAGE_NUMBER					_PAGE_PER_BLOCK
#define LUN_NUMBER					_LUN_NUMBER
#define SECTORS_PER_ENTRY			(8)
#define ENTRY_PER_PLANE				(4)

#define	BYTES_PER_PAGES				(16384)			///< The Page is 16kbytes
#define	BYTES_PER_SPARE_PAGES		(1280)

#if (IM_B27B) || (IM_N28)
#define B27_SLC_WORDLINE_SIZE		(12)
#elif (IM_B47R) || (IM_B37R)
#define B27_SLC_WORDLINE_SIZE		(4)
#endif /* (IM_B27) || (IM_N28) */

#endif	// _FLASH_SPEC_H_
