/****************************************************************************
*
*  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
*  All rights reserved
*
*  The content of this document is confidential and shall be applied
*  subject to the terms and conditions of the license agreement and
*  other applicable laws. Any unauthorized access, use or disclosure
*  of this document is strictly prohibited and may be punishable
*  under laws.
*
*  ftl_nrw.h
*
*
*
****************************************************************************/
#ifndef _FTL_NRW_H_
#define _FTL_NRW_H_

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "nvme_api/nvme/shr_hal_nvme_api.h"
#include "ftl_nrw_api.h"
#include "hal/sata/sata_api.h"

/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */
/* Self-Test */
#if (NVME == HOST_MODE)
#define SELF_TEST_MAX_USE_READ_QUEUE_NUM_PER_NS    (RQ_SELF_TEST_RQ_NUM / NRW_MAX_NS_NUM) // NVMe mode only use one queue in each NS (temporarily)
#define SELF_TEST_TIME_SHORT                       (((U64)60 * 3/2 * 1000)) //1.5mins
#define SELF_TEST_TIME_EXTENDED                    (((U64)60 * IDFY_CTRL_DST_EDSTT * 3 / 4 * 1000))
#elif (USB == HOST_MODE)
#define SELF_TEST_MAX_NS_NUM                     	M_FW_TODO()
#define SELF_TEST_MAX_USE_READ_QUEUE_NUM_PER_NS 	M_FW_TODO()
#define SELF_TEST_TIME_SHORT                		M_FW_TODO()
#define SELF_TEST_TIME_EXTENDED             		M_FW_TODO()
#define SELF_TEST_TIME_CONVEYANCE           		M_FW_TODO()
#define SELF_TEST_TIME_OFFLINE_ROUTINE      		M_FW_TODO()
#else /* (NVME == HOST_MODE) */
#define SELF_TEST_MAX_USE_READ_QUEUE_NUM_PER_NS    (RQ_SELF_TEST_RQ_NUM / NRW_MAX_NS_NUM) // SATA mode use multi queue in same time 24 ~ 31 (max:8)
#define SELF_TEST_TIME_SHORT                       ((U64)(SMART_SELFTEST_SHORT_EXECUTE_TIME           * 60 * 1000)) // 2min
#define SELF_TEST_TIME_EXTENDED                    ((U64)(SMART_SELFTEST_EXTEND_EXECUTE_TIME          * 60 * 1000)) // 23min
#define SELF_TEST_TIME_CONVEYANCE                  ((U64)(SMART_SELFTEST_CONVEYANCE_EXECUTE_TIME      * 60 * 1000)) // 7min
#define SELF_TEST_TIME_OFFLINE_ROUTINE             ((U64)(SMART_SELFTEST_OFFLINE_ROUTINE_EXECUTE_TIME * 60 * 1000)) // 600min
#endif /* (NVME == HOST_MODE) */

#define SELF_TEST_UPDATA_TIME_THREASHOLD	(SELF_TEST_TIME_SHORT / 10)

#define SELF_TEST_VALID_FIELD_NSID	(BIT(0))
#define SELF_TEST_VALID_FIELD_FLBA	(BIT(1))
#define M_IS_SYNC_READ_QUEUE_EMPTY() ((gpRQIMgr[SYNC_CMD_QUEUE_ID]->ubNum)? TRUE : FALSE)
#define FTL_SANITIZE_TEMP_CTAG	(0x88)
#define FTL_SANITIZE_NORMAL_PB	(0)
#define FTL_SANITIZE_INVERT_PB	(1)
#define FTL_SANITIZE_OVERWRITE_SIZE			(BIT(12))
#define FTL_SANITIZE_OVERWRITE_SIZE_MASK	(BIT_MASK(12))
#define FTL_SANITIZE_PROGRESS_TRIM_DONE		(50)
#define FTL_SANITIZE_PROGRESS_FINISH		(100)
#define DLMC_FORCE_AESKEY1	(0)
#if (NVME == HOST_MODE)
#define DLMC_READ_CHECK		(1)
#else /* (NVME == HOST_MODE) */
#define DLMC_READ_CHECK		(0)
#endif /* (NVME == HOST_MODE) */
#define DLMC_UART_ENABLE	(0)
#define DLMC_UART_ENABLE_2	(0)
#define	DLMC_HMAC_EN		(FALSE)

#define DLMC_WRITE_SPI					(0)
#define DLMC_INSERT_IDPG				(1)
#define DLMC_KEEP_DATA_IN_READBUFF		(1)
#define DLMC_FWIMAGE_L2P_ENTRY_SIZE 	(8)  // unit: sector
#define DLMC_INVALID_PCA_FW_IMAGE_L2P 	(0xFFFF)
#define DLMC_INIT_FW_IMAGE_L2P_VALUE	(0xFFFFFFFF)
#define DLMC_INVALID_PCA_READBUFFER 	(0xFFFFFFFF)
#define	DLMC_RSA_DECRYPT_OFFSET			(SIGNDIGEST_SIZE)
#define	DLMC_DUMMY_LOW_VALUE			(0x55AA55AA)
#define	DLMC_DUMMY_HIGH_VALUE			(0x3C3C3C3C)
#define	DLMC_INVALID_SECURITY_VERSION	(0x55AA55AA)
#define	DLMC_INVALID_SECURITY_VERSION_LENGTH	(4)
#define DLMC_FLASH_TYPE_IDX				(4)
#define	DLMC_HP_MODULE_VERSION_LENGTH	(3)
#define	DLMC_HP_REVISION_NUM_IDX		(3)
#define	DLMC_HP_REVISION_SECURITY_IDX	(4)
#define	DLMC_HP_REVISION_SECURITY		('T')
#define	DLMC_DELL_MODULE_VERSION_LENGTH	(3)
#define	DLMC_DELL_VERSION_NUM_IDX		(5)
#define	DLMC_DELL_VERSION_NUM_LENGTH	(3)
#define	DLMC_MICRON_MODULE_VERSION_LENGTH	(4)
#define	DLMC_MICRON_MAJOR_REVISION_IDX	(4)
#define	DLMC_MICRON_MAJOR_REVISION_NUM	(2)
#define	DLMC_MICRON_MINOR_VERSION_IDX	(6)

// Seagate
#define	DLMC_SEAGATE_PRODUCT_ID_IDX		(1)
#define	DLMC_SEAGATE_PRODUCT_ID_LENGTH	(4)
#define	DLMC_SEAGATE_BLOCK_POINT_IDX	(5)

#define DLMC_ALLOCATE_DATA_BUF			(0)
#define DLMC_ALLOCATE_TABLE_BUF			(1)

#define NRW_VALIDBMP_LENGTH  (8)

#define DLMC_GET_NONE_BLK					(0)
#define DLMC_GET_BIN_FILE_BLK				(1)
#define DLMC_GET_CODE_BANK_BLK			(2)

#define SATA_DLMC_SEGMENT_DOWNLOAD_ACTIVE_MODE	(0x03)
#define SATA_DLMC_ONCE_DOWNLOAD_ACTIVE_MODE	(0x07)
#define SATA_DLMC_SEGMENT_DOWNLOAD_NO_ACTIVE_MODE	(0x0E)
#define SATA_DLMC_DIRECT_ACTIVE_MODE		(0x0F)

#define SATA_DLMC_SECTOR_CNT_REG_EXPECT_MORE_CMD		(0x01)
#define SATA_DLMC_SECTOR_CNT_REG_ACTIVE_TO_NEW_CODE		(0x02)
#define SATA_DLMC_SECTOR_CNT_REG_WAIT_ACTIVE_TO_NEW_CODE		(0x03)

#define TELEMETRY_UNIT_SIZE				(20 * 1024)
#define TELEMETRY_START_FRAMEOFFSET		(0)
#define TELEMETRY_START_PAGEOFFSET		(0)
#define TELEMETRY_END_FRAMEOFFSET		(4)
#define TELEMETRY_AES_KEY				(0)
#define TELEMETRY_AES_INIT_VECTOR		(8)

typedef enum FTL_DLMC_READ_OLD_CODE_BLK {
	FTL_DLMC_READ_ID_PAGE = 0,
	FTL_DLMC_READ_CODE_POINTER,
#if PS5017_EN
	FTL_DLMC_READ_PAD_PAGE
#endif /* PS5017_EN */
} FTL_DLMC_READ_OLD_CODE_BLK_t;

typedef enum FTL_DLMC_SECURITY_LOG_TYPE {
	FTL_DLMC_AUTHENTICATE_FAIL = 0,
	FTL_DLMC_SED_TO_NON_SED,
	FTL_DLMC_NON_SED_TO_SED,
	FTL_DLMC_NON_HP_TO_HP,
	FTL_DLMC_HP_TO_NON_HP
} FTL_DLMC_SECURITY_LOG_TYPE_t;

typedef enum {
	FTL_DLMC_INVALID_IMAGE,
	FTL_DLMC_DRIVE_LOG_INVALID_SIGNATURE
} FTLDLMCAttestation_t;

typedef enum {
	FTL_DLMC_INVALID_SECURITY_VERSION,
	FTL_DLMC_INVALID_PUBLIC_KEY,
	FTL_DLMC_INVALID_CUSTOMER_CODE,
	FTL_DLMC_INVALID_FW_SLOT_ID,
} FTLDLMCInvalidEnum_t;

/*
 * ---------------------------------------------------------------------------------------------------
 *   macros
 * ---------------------------------------------------------------------------------------------------
 */

#if (SATA == HOST_MODE)
// SATA Sanitize Operation Cmd end Sanitize flow in SANITIZE_PROCESS_FINISH state, then stay in SANITIZE_PROCESS_FINISH_WAIT_CHANGE_STATE state wait Sanitize Status Cmd to change to SANITIZE_FINISH state
#define M_SET_NRW_SANITIZE_FLOW_FINISH()    (gNRWCmdState.Sanitize.ubState = SANITIZE_PROCESS_FINISH)
#else /* (SATA == HOST_MODE) */
#define M_SET_NRW_SANITIZE_FLOW_FINISH()    (gNRWCmdState.Sanitize.ubState = SANITIZE_FINISH)
#endif /* (SATA == HOST_MODE) */
#define M_SELF_TEST_SET_NEED_FINISH(ubNSID)		(gpVT->SelfTest.ubNeedFinishBitmap |= BIT(ubNSID))
#define M_SELF_TEST_CLR_NEED_FINISH(ubNSID)		(gpVT->SelfTest.ubNeedFinishBitmap &= ~(BIT(ubNSID)))
#define M_SELF_TEST_CHECK_NEED_FINISH(ubNSID)	(gpVT->SelfTest.ubNeedFinishBitmap & BIT(ubNSID))
#if (NVME == HOST_MODE)
#define M_WRUNC_GET_SLBA(CTAG) (HAL_CTSRAM_GET_SLBA_API(CTAG))
#define M_WRUNC_GET_NLB(CTAG)  (HAL_CTSRAM_GET_NLB_API(CTAG))
#elif (USB == HOST_MODE) /* (NVME == HOST_MODE) */
#define M_WRUNC_GET_SLBA(CTAG)	(0)
#define M_WRUNC_GET_NLB(CTAG)	(0)
#else /* (NVME == HOST_MODE) */
#define M_WRUNC_GET_SLBA(CTAG) (M_SATA_GET_CMD_START_LBA())
#define M_WRUNC_GET_NLB(CTAG)  ((M_SATA_GET_TOTAL_SECTOR_CNT() == 0 ? 65535 : M_SATA_GET_TOTAL_SECTOR_CNT()) - 1)
#endif /* (NVME == HOST_MODE) */

/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */
AOM_NRW_2 void NRW_PowerDoneReady(void);
#if ((!BURNER_MODE_EN) && (!RDT_MODE_EN))
#if (SATA == HOST_MODE)
AOM_NRW_3 U8 NRWReadVerifySetup(U64 uoStartLBA, U64 uoEndLBA);
AOM_NRW_3 void NRWReadVerify(void);
AOM_NRW_3 void NRWSelfTestCaptiveTest(U8 ubNSIDBitmap);
#endif /* (SATA == HOST_MODE) */
AOM_NRW_3 void NRWSelfTestSetup(U8 ubStc, U8 ubNSIDBitmap);
AOM_NRW_3 void NRWSelfTest(U8 ubTestValidBitmap);
AOM_NRW_3 U8 NRWSelfTestBGStop(void);
AOM_NRW_2 void NRWFormatNVM(U8 ubIsSanitize);
AOM_NRW_2 void NRWSanitizeSetup(U8 ubSanitizeType);
AOM_NRW_2 void NRWSanitizeStop(void);
AOM_NRW_2 void NRWSanitize(void);
AOM_NRW_3 void NRWWRUNC(CTAG_t CTAG);

//NRWWRZero() must keep in AOM_NRW
AOM_NRW void NRWWRZero(OPT_HCMD_PTR pCmd);

AOM_NRW_2 void NRWCompare(OPT_HCMD_PTR pCmd);
#endif /* ((!BURNER_MODE_EN) && (!RDT_MODE_EN)) */
AOM_DLMC U8 NRWDLMCCommitEntry(U8 ubCA, U8 ubFWSlot);
AOM_NRW_2 void NRWDLMCFWInit (U32 ulTransferLen, U32 ulFwImgOffset);
AOM_NRW_2 U8 NRWDLMCSendDownloadFW (U32 ulPhyAddr, U32 ulTransferLength);
AOM_NRW_2 void NRWDLMCClearFW (void);
AOM_NRW_2 void NRWFormatNVMSetup(U8 ubNSIdBitmap, U8 btIsFormatAll, U8 ubSes);
#if (NVME == HOST_MODE)
AOM_NRW void NRWFlush(U8 btIsFlushCmd);
AOM_EXTEND_LCA void NRWSyncRead (OPT_HCMD_PTR pCmd);
AOM_EXTEND_LCA void NRWFakeRead (OPT_HCMD_PTR pCmd);
#elif  (USB == HOST_MODE) /* (NVME == HOST_MODE) */
AOM_LPM void NRWFlush(U8 btIsFlushCmd); // Put into same overlay with SATACmdDispatcher() to improve performance
#else /* (NVME == HOST_MODE) */
AOM_SATA void NRWFlush(U8 btIsFlushCmd); // Put into same overlay with SATACmdDispatcher() to improve performance
AOM_SATA void NRWSyncRead(PSATAHCMD_t pCmd);
#endif /* (NVME == HOST_MODE) */
#if TCG_EN
AOM_TCG_RECEIVE void ExtendLCASetup(U8 ubCmdType, U32 ulStartByte, U32 ulLen, U32 ulBufAddr);
#else /* TCG_EN */
AOM_EXTEND_LCA void ExtendLCASetup(U8 ubCmdType, U32 ulStartByte, U32 ulLen, U32 ulBufAddr);
#endif /* TCG_EN */
AOM_EXTEND_LCA void FTLHandleExtendLCA(void);
AOM_NRW void TelemetryInit (U32 ulNUMDU, U64 uoLPOUL);
AOM_NRW_3 void TelemetryFillBuffer (OPT_HCMD *pulCurrentCMD);

AOM_DLMC void NRWDLMCGetBinFileSize(U32 ulAddr);
AOM_DLMC U8 NRWSATADLMCCheckMPHeader(U32 ulAddr, U16 uwDLMCBlkCnt, U8 ubDLMCMode);
#endif /* _FTL_NRW_H_ */
