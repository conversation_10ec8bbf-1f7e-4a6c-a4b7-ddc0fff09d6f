/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  err_hdl_fpl_SoftbitRaid.c                                          */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "debug/debug_setup.h"

#include <string.h>
#include "retry.h"
#include "err_hdl_fpl_SoftbitRaid.h"
#include "hal/fip/fpu.h"
#include "retry.h"
#include "hal/dmac/dmac_pop_cmd.h"
#include "hal/dmac/dmac_api.h"
#include "common/mem.h"
#include "err_hdl_fpl_RS.h"
#include "buffer/buffer.h"
#include "ftl/ftl_api.h"
//NCS SBRAID
#include "err_hdl_fpl_sb_retry_api.h"
#include "hal/sys/api/rng/rng_api.h"
/*
 * ---------------------------------------------------------------------------------------------------
 *   definitions
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *  enum
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *  data types
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *   macros
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *   extern global variables
 * ---------------------------------------------------------------------------------------------------
 */
SOFTBITRAID_TASK_t gSBRaidTask = {0};

/*
 * ---------------------------------------------------------------------------------------------------
 *   static global variables
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *   private prototypes
 * ---------------------------------------------------------------------------------------------------
 */
AOM_RETRY_SBRAID U8 RetrySBRAIDCheckLoopBoundary(void);
AOM_RETRY_SBRAID void RetrySBRAIDDataXOR(U8  ubDestIdx, U8  ubSourceIdx);
AOM_RETRY_SBRAID void RetrySBRAIDGotoEndFlow(U8 ubPass);
AOM_RETRY_SBRAID void RetrySBRAIDNCSVUCSendInfo(U8 ubMTIdx, FlhMT_t *pMTTemplate, U32 ulBufAddr, U32 ulSpareAddr, U8 ubMode);
AOM_RETRY_SBRAID void RetrySBRAIDNCSCompareXORAll(FlhMT_t *pMTTemplate, U8 ubBufIdx, U8 ubIdx);
AOM_RETRY_SBRAID void RetrySBRAIDNCSSB6Compare(FlhMT_t *pMTTemplate, U32 ulBufAddr, U32 ulSpareAddr);
/*
 * ---------------------------------------------------------------------------------------------------
 *   private
 * ---------------------------------------------------------------------------------------------------
 */
#if (RETRY_SBRAID_EN)
void RetrySBRAIDInit(void)
{
	U8 ubi = 0;
	gSBRaidTask.ubState = SOFTBITRAID_IDLE;
	gSBRaidTask.ubIterationCnt = 0;
	gSBRaidTask.ubAllocated4KBufCnt = 0;
	gSBRaidTask.ubStopSBRAIDFlag = 0;
	gSBRaidTask.ubRSDecodeFlowLoopBreakFlag = 0;
	gSBRaidTask.ubErrPageTotalCnt = 0;
	gSBRaidTask.ubErrPageTotalCntMax = 0;
	gSBRaidTask.ubErrPageTotalCntIdx = 0;
	gSBRaidTask.ubErrPageOfAllPageIdx = 0;
	gSBRaidTask.ubOriginErrPageOfAllPageIdx = SOFTBITRAID_ORIGIN_ERRPAGE_NON_INIT_MARK;

	for (ubi = 0 ; ubi < SOFTBITRAID_MAX_BITMAP_LENGTH_IN_BYTE; ubi++) {
		gSBRaidTask.ubErrPageBMP[ubi] = 0;
	}
	for (ubi = 0 ; ubi < SOFTBITRAID_MAX_BITMAP_LENGTH_IN_BYTE; ubi++) {
		gSBRaidTask.ubErrPageCorrectedBMP[ubi] = 0;

	}
	gSBRaidTask.ubDealingWithErrPageFlag = SBRAID_IDLE_MODE;
	gSBRaidTask.ubTargetPageDone = FALSE;

	gSBRaidTask.ulBackupOriginErrVCA = 0;
	gSBRaidTask.ulBackupTargetErrVCA = 0;
	gSBRaidTask.ulBackupTargetErrPCA = 0;

	if (RETRY_SBRAID_PRE_ALLOCATE_BUF) {
		if (BUFFER_SUCCESS == gFWLBMgr.Type[FWLB_SBRAID].uwState) {
			gSBRaidTask.ubAllocated4KBufCnt = (FWLB_SBRAID_SIZE_IN_4K - 1); // 1 ,4K for backup Spare
			gulSBRAIDAddr = M_LB_TO_ADDR(LB_ID_FW, gFWLBMgr.Type[FWLB_SBRAID].uwLBOffset);
			gulSBRAIDSpareAddr = gulSBRAIDAddr + (BC_4KB * (FWLB_SBRAID_SIZE_IN_4K - 1));	// 1, 4K for backup Spare

			gSBRaidTask.ulDataBaseAddr = gulSBRAIDAddr;
			gSBRaidTask.ulSpareBaseAddr = gulSBRAIDSpareAddr;

		}
		else {
			gSBRaidTask.ubStopSBRAIDFlag = TRUE;
		}
	}
	else {
		if ((gSBRaidTask.ubAllocated4KBufCnt / gub4kEntrysPerPlane) <= SOFTBITRAID_BUF_START_OFFSET_BY_PAGE) {
			gSBRaidTask.ubStopSBRAIDFlag = TRUE;
		}
	}
	///init gSBRaid_Task.ubBackup_Optimal_shift_value
	memset((void *)gSBRaidTask.ubBackupOptimalShiftValue, 0, sizeof(gSBRaidTask.ubBackupOptimalShiftValue));
}

U8 RetrySBRAIDCheckLoopBoundary(void)
{
	gSBRaidTask.ubErrPageOfAllPageIdx ++;
	if (gSBRaidTask.ubDealingWithErrPageFlag) {
		M_UART(ERROR_SOFTBIT_RAID_, "[SBRAID] LB_Doing ErrPage  \n");

		if (SOFTBITRAID_SB_CORR_PASS == gSBRaidTask.ubSBCORRResult) {
			if (RETRY_SBRAID_CONTINUE_PROCESSING_OTHER_FAIL_PAGE) {
				gSBRaidTask.ubDealingWithErrPageFlag = SBRAID_IDLE_MODE;
			}
			else {
				gSBRaidTask.ubDealingWithErrPageFlag = SBRAID_TARGET_DONE;
				// go to pass flow
				RetrySBRAIDGotoEndFlow(TRUE);
			}
		}
		else {
			gSBRaidTask.ubDealingWithErrPageFlag = SBRAID_IDLE_MODE;
		}
	}
	else if (gSBRaidTask.ubTargetPageDone && (1 == gSBRaidTask.ubErrPageTotalCnt)) {	//RETRY_SBRAID_CONTINUE_PROCESSING_OTHER_FAIL_PAGE == TRUE, Target Page already fix, Normal, // Directly Normal Page only 1 Fail,  Target page is Fixed
		// do ErrPage RS, Update Result to SBRAID Buff
		RetrySBRAIDDataXOR((gSBRaidTask.ubErrPageTotalCntIdx + SOFTBITRAID_BUF_START_OFFSET_BY_PAGE), SBRAID_XORALL_BUF_IDX);

		//Return Normal Page fix by XOR (SB6) to NCS
		if (NCS_SBRAID_EN) {

			M_UART(RETRY_SBRAID_, "\nNormal_SB6_SP: %x, %d, %d", gpRS_Task.ultmp_SpareAddr, gSBTask.ubCurr_frm_idx, gSBTask.ubCurrentLDPCFrameIdx);

			//Copy Spare Info From DBUF to IRAM
			memcpy((void *)(gpRS_Task.ultmp_SpareAddr), (void *)(gSBRaidTask.ulSpareBaseAddr +  ((gSBRaidTask.ubErrPageTotalCntIdx + SOFTBITRAID_BUF_START_OFFSET_BY_PAGE) * L4K_SIZE * gub4kEntrysPerPlane)), L4K_SIZE);
			//NCS Compare SB6
			RetrySBRAIDNCSSB6Compare(&gpRS_Task.MTTemplate, (gSBRaidTask.ulDataBaseAddr +  ((gSBRaidTask.ubErrPageTotalCntIdx + SOFTBITRAID_BUF_START_OFFSET_BY_PAGE) * BC_4KB * gub4kEntrysPerPlane)), gpRS_Task.ultmp_SpareAddr);
		}

		// copy data from Fail_page_buf(BufIdx1) to Retry Buf
		RetrySBRAIDDMACCopy(SBRAID_FAIL_PAGE_BUF_IDX,  SOFTBITRAID_RETRY_TO_RETRYBUF_MODE, TRUE);

		gSBRaidTask.ubDealingWithErrPageFlag = SBRAID_NORMAL_FAIL_ALL_DONE;
		// go to pass flow
		RetrySBRAIDGotoEndFlow(TRUE);

	}
	else if (gSBRaidTask.ubErrPageTotalCntIdx == gSBRaidTask.ubErrPageTotalCntMax) {
		M_UART(ERROR_SOFTBIT_RAID_, "[SBRAID] LB_PageTotalCnt MAX   \n");
		M_UART(ERROR_SOFTBIT_RAID_, "[SBRAID]   ErrPageTotalCntIdx :%d   \n", gSBRaidTask.ubErrPageTotalCntIdx);
		M_UART(ERROR_SOFTBIT_RAID_, "[SBRAID]   ErrPageTotalCntMax :%d   \n", gSBRaidTask.ubErrPageTotalCntMax);

		if (0 == gSBRaidTask.ubErrPageTotalCnt) {
			M_UART(ERROR_SOFTBIT_RAID_, "[SBRAID] LB_Do ErrPage RS  \n");
			// do ErrPage RS
			RetrySBRAIDDataXOR(SBRAID_FAIL_PAGE_BUF_IDX, SBRAID_XORALL_BUF_IDX);

			//Return Normal Page fix by XOR (SB6) to NCS
			if (NCS_SBRAID_EN) {
				M_UART(RETRY_SBRAID_, "\nTarget_Fix_SB6_SP: %x, %d, %d", gpRS_Task.ultmp_SpareAddr, gSBTask.ubCurr_frm_idx, gSBTask.ubCurrentLDPCFrameIdx);

				//Copy Spare Info From DBUF to IRAM
				memcpy((void *)(gpRS_Task.ultmp_SpareAddr), (void *)(gSBRaidTask.ulSpareBaseAddr +  (SBRAID_FAIL_PAGE_BUF_IDX * L4K_SIZE * gub4kEntrysPerPlane)), L4K_SIZE);
				//NCS Compare SB6
				RetrySBRAIDNCSSB6Compare(&gpRS_Task.MTTemplate, (gSBRaidTask.ulDataBaseAddr +  (SBRAID_FAIL_PAGE_BUF_IDX * BC_4KB * gub4kEntrysPerPlane)), gpRS_Task.ultmp_SpareAddr);
			}

			// copy data from Fail_page_buf(BufIdx1) to Retry Buf
			RetrySBRAIDDMACCopy(SBRAID_FAIL_PAGE_BUF_IDX,  SOFTBITRAID_RETRY_TO_RETRYBUF_MODE, TRUE);

			gSBRaidTask.ubDealingWithErrPageFlag = SBRAID_ONLY_RAIDECC_MODE;
			M_UART(ERROR_SOFTBIT_RAID_, "[SBRAID] LB SBRAID_ONLY_RAIDECC_MODE \n");
			// go to pass flow
			RetrySBRAIDGotoEndFlow(TRUE);
		}
		else {
			M_UART(ERROR_SOFTBIT_RAID_, "[SBRAID] LB Still Fail in Normal pages  \n");
			if ((RETRY_SBRAID_MAX_ITERATION_CNT - 1) == gSBRaidTask.ubIterationCnt) {
				if (gSBRaidTask.ubTargetPageDone) {		//RETRY_SBRAID_CONTINUE_PROCESSING_OTHER_FAIL_PAGE == TRUE, Target Page already fix, go to pass flow

					M_UART(RETRY_SBRAID_, "\n[SBRAID] go to fail flow_3 \n");

					// Directly Normal Page still fail, but Target page is Fixed
					// go to Retry Pass flow

					// copy data from Fail_page_buf(BufIdx1) to Retry Buf
					RetrySBRAIDDMACCopy(SBRAID_FAIL_PAGE_BUF_IDX,  SOFTBITRAID_RETRY_TO_RETRYBUF_MODE, TRUE);

					gSBRaidTask.ubDealingWithErrPageFlag = SBRAID_ONLY_RAIDECC_MODE;
					M_UART(ERROR_SOFTBIT_RAID_, "[SBRAID] Normal Fail, Target Pass \n");
					// go to pass flow
					RetrySBRAIDGotoEndFlow(TRUE);
				}
				else {
					// Directly Fail
					// go to fail flow

					M_UART(RETRY_SBRAID_, "\n[SBRAID] go to fail flow_2 \n");
					M_UART(RETRY_SBRAID_, "\nNormal_Full IterationCnt: %d", gSBRaidTask.ubIterationCnt);

					gSBRaidTask.ubDealingWithErrPageFlag = SBRAID_TARGET_CANNOT_RECOVER;
					RetrySBRAIDGotoEndFlow(FALSE);

				}
			}
			else {
				gSBRaidTask.ubIterationCnt++;

				M_UART(RETRY_SBRAID_, "\nNormal IterationCnt: %d", gSBRaidTask.ubIterationCnt);

				gSBRaidTask.ubErrPageTotalCntIdx = 0;
				gSBRaidTask.ubErrPageOfAllPageIdx = 0;

				if (gSBRaidTask.ubTargetPageDone) {		//RETRY_SBRAID_CONTINUE_PROCESSING_OTHER_FAIL_PAGE == TRUE, Target Page already fix, no need to do SBRAID for target page
					M_UART(RETRY_SBRAID_, "\n[SBRAID] Target Done_Jump \n");

					gSBRaidTask.ubDealingWithErrPageFlag = SBRAID_IDLE_MODE;
					gSBRaidTask.ubState = SOFTBITRAID_CHECK_NEXT_ERRPAGE;

					return gSBRaidTask.ubDealingWithErrPageFlag;
				}
				else {
					// do ErrPage RS
					RetrySBRAIDDataXOR(SBRAID_XORALL_BUF_IDX, SBRAID_FAIL_PAGE_BUF_IDX);

					// copy data from Buf0 (XORALL_Buf) to Retry Buf
					RetrySBRAIDDMACCopy(SBRAID_XORALL_BUF_IDX,  SOFTBITRAID_RETRY_TO_RETRYBUF_MODE, FALSE);

					gSBRaidTask.ubDealingWithErrPageFlag = SBRAID_RAIDECC_AND_SB_MODE;
					M_UART(ERROR_SOFTBIT_RAID_, "[SBRAID] LB SBRAID_RAIDECC_AND_SB_MODE \n");
					// go to SB flow
					gSBRaidTask.ubSBCORRResult = SOFTBITRAID_SB_CORR_NULL;
					gpRetry->ubRetryState = FLH_RETRY__SBRAID_CREATE_RS_TASK;
					gSBRaidTask.ubState = SOFTBITRAID_BUSY_DOING_RAID;
					gSBRaidTask.ubRSDecodeFlowLoopBreakFlag = 1;
				}

			}
		}
		gSBRaidTask.ubErrPageOfAllPageIdx = gSBRaidTask.ubOriginErrPageOfAllPageIdx;
	}
	else {
		M_UART(ERROR_SOFTBIT_RAID_, "[SBRAID] LB_IDLE Mode \n");
		gSBRaidTask.ubDealingWithErrPageFlag = SBRAID_IDLE_MODE;
	}


	return gSBRaidTask.ubDealingWithErrPageFlag;
}

void RetrySBRAIDDataXOR(U8  ubDestIdx, U8  ubSourceIdx)
{
	// XOR DATA
	gpRetry->ubWaitDMACDoneFlag = TRUE;
	DMAC_BitmapXOR((gSBRaidTask.ulDataBaseAddr +  (ubDestIdx * BC_4KB * gub4kEntrysPerPlane)),  (gSBRaidTask.ulDataBaseAddr +  (ubSourceIdx * BC_4KB * gub4kEntrysPerPlane)),  SIZE_IN_32B((BC_4KB * gub4kEntrysPerPlane)), gulReadRetryDMACDone_CallBack);
	while (gpRetry->ubWaitDMACDoneFlag == TRUE) {
		DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
	}

	if (DEBUG_SBRAID_EN) {
		M_UART(RETRY_SBRAID_, "\nXORALL S:%d", ubSourceIdx);	//NCS Dump XORALL result, After XOR
		M_UART(RETRY_SBRAID_, "\nXORALL_Spare_0:%x", *((U32 *)((gSBRaidTask.ulSpareBaseAddr +  (ubSourceIdx * L4K_SIZE * gub4kEntrysPerPlane)) + (0 * L4K_SIZE))));
		M_UART(RETRY_SBRAID_, "\nXORALL_Spare_1:%x", *((U32 *)((gSBRaidTask.ulSpareBaseAddr +  (ubSourceIdx * L4K_SIZE * gub4kEntrysPerPlane)) + (1 * L4K_SIZE))));
		M_UART(RETRY_SBRAID_, "\nXORALL_Spare_2:%x", *((U32 *)((gSBRaidTask.ulSpareBaseAddr +  (ubSourceIdx * L4K_SIZE * gub4kEntrysPerPlane)) + (2 * L4K_SIZE))));
		M_UART(RETRY_SBRAID_, "\nXORALL_Spare_3:%x", *((U32 *)((gSBRaidTask.ulSpareBaseAddr +  (ubSourceIdx * L4K_SIZE * gub4kEntrysPerPlane)) + (3 * L4K_SIZE))));

		U32 *pubDataPtr = (U32 *)(gSBRaidTask.ulDataBaseAddr +  (ubSourceIdx * BC_4KB * gub4kEntrysPerPlane));
		U32 ulIdx = 0;
		ulIdx = 1;
		M_UART(RETRY_SBRAID_, "\nData Idx %d, %x ", ulIdx, *(pubDataPtr + ulIdx));
		ulIdx = 2;
		M_UART(RETRY_SBRAID_, "\nData Idx %d, %x ", ulIdx, *(pubDataPtr + ulIdx));
		M_UART(RETRY_SBRAID_, "\n ");

		M_UART(RETRY_SBRAID_, "\nXORALL D:%d", ubDestIdx);	//NCS Dump XORALL result, After XOR
		M_UART(RETRY_SBRAID_, "\nXORALL_Spare_0:%x", *((U32 *)((gSBRaidTask.ulSpareBaseAddr +  (ubDestIdx * L4K_SIZE * gub4kEntrysPerPlane)) + (0 * L4K_SIZE))));
		M_UART(RETRY_SBRAID_, "\nXORALL_Spare_1:%x", *((U32 *)((gSBRaidTask.ulSpareBaseAddr +  (ubDestIdx * L4K_SIZE * gub4kEntrysPerPlane)) + (1 * L4K_SIZE))));
		M_UART(RETRY_SBRAID_, "\nXORALL_Spare_2:%x", *((U32 *)((gSBRaidTask.ulSpareBaseAddr +  (ubDestIdx * L4K_SIZE * gub4kEntrysPerPlane)) + (2 * L4K_SIZE))));
		M_UART(RETRY_SBRAID_, "\nXORALL_Spare_3:%x", *((U32 *)((gSBRaidTask.ulSpareBaseAddr +  (ubDestIdx * L4K_SIZE * gub4kEntrysPerPlane)) + (3 * L4K_SIZE))));

		pubDataPtr = (U32 *)(gSBRaidTask.ulDataBaseAddr +  (ubDestIdx * BC_4KB * gub4kEntrysPerPlane));

		ulIdx = 1;
		M_UART(RETRY_SBRAID_, "\nData Idx %d, %x ", ulIdx, *(pubDataPtr + ulIdx));
		ulIdx = 2;
		M_UART(RETRY_SBRAID_, "\nData Idx %d, %x ", ulIdx, *(pubDataPtr + ulIdx));
		M_UART(RETRY_SBRAID_, "\n ");

	}

	// XOR SPARE
	gpRetry->ubWaitDMACDoneFlag = TRUE;
	DMAC_BitmapXOR((gSBRaidTask.ulSpareBaseAddr +  (ubDestIdx * L4K_SIZE * gub4kEntrysPerPlane)),  (gSBRaidTask.ulSpareBaseAddr +  (ubSourceIdx * L4K_SIZE * gub4kEntrysPerPlane)),  SIZE_IN_32B((L4K_SIZE * gub4kEntrysPerPlane)), gulReadRetryDMACDone_CallBack);
	while (gpRetry->ubWaitDMACDoneFlag == TRUE) {
		DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
	}
	__asm("DSB");
}

/*
 * ---------------------------------------------------------------------------------------------------
 *   public
 * ---------------------------------------------------------------------------------------------------
 */

void RetrySBRAIDAddErrPage(U8 ubErrPageIdx, U8 ubMode, U8 ubBuffAllocate)
{
	U8 ubIdx = 0;
	U8 ubBitmap;
	U8 ubBufOffsetByPage = 0;
	U8 ubBufCntVaildForNormalPage;
	DMACParam_t DMACParam;

	// Check if buf is enough
	if ( TRUE == gSBRaidTask.ubStopSBRAIDFlag ) {
		return;
	}
	else {
		if (RETRY_SBRAID_PRE_ALLOCATE_BUF) {
			//SBRAID 1102 for Bufer not enough assert
			if (CORR_FAIL_FRAME_MODE == ubMode) {
				if ((gSBRaidTask.ubAllocated4KBufCnt / gub4kEntrysPerPlane) > SOFTBITRAID_BUF_START_OFFSET_BY_PAGE) {
					ubBufCntVaildForNormalPage = ((gSBRaidTask.ubAllocated4KBufCnt / gub4kEntrysPerPlane) - SOFTBITRAID_BUF_START_OFFSET_BY_PAGE);
					if (ubBufCntVaildForNormalPage <= gSBRaidTask.ubErrPageTotalCnt) {
						//[SBRS] Buffer not enough, cancel SBRS
						M_UART(RETRY_SBRAID_, "\n Buf_C_V_N: %d , Err_P_T %d  \n", ubBufCntVaildForNormalPage, gSBRaidTask.ubErrPageTotalCnt);	//[SBRAID] BufCntVaildForNormalPage: %d , ErrPageTotalCnt %d  \n

						gSBRaidTask.ubStopSBRAIDFlag = TRUE;
						return;
					}
				}
				else {
					gSBRaidTask.ubStopSBRAIDFlag = TRUE;
					return;
				}
			}
		}
	}

	// set bitmap
	if ((BACKUP_RAIDECC_RESULT_TO_BUF0_MODE != ubMode) && (BACKUP_RAIDECC_RESULT_TO_BUF1_MODE != ubMode)) {
		// set bitmap
		M_SET_BITMAP(gSBRaidTask.ubErrPageBMP, ubErrPageIdx);
		M_UART(ERROR_SOFTBIT_RAID_, "[SBRAID],DecodeRSIndex: %x, Err PageIdx: %x, Set ErrPage BM \n", gpRS_Task.ubDecodeRSIndex, ubErrPageIdx);
	}

	if (CORR_FAIL_FRAME_MODE == ubMode) {
		ubBufOffsetByPage = SOFTBITRAID_BUF_START_OFFSET_BY_PAGE + gSBRaidTask.ubErrPageTotalCnt;
		ubBitmap = gpRS_Task.ubErrFrameBMP_ForHBSBRetry_Ori;
		gSBRaidTask.ubErrPageTotalCnt++;

		M_UART(RETRY_SBRAID_, "\n Err_P_T++ %d  \n", gSBRaidTask.ubErrPageTotalCnt);	//[SBRAID] BufCntVaildForNormalPage: %d , ErrPageTotalCnt %d  \n

	}
	else if (CORR_ALL_FRAME_MODE == ubMode) {
		//TBD
		ubBitmap = 0xFF;
	}
	else if (BACKUP_RAIDECC_RESULT_TO_BUF0_MODE == ubMode) {
		ubBufOffsetByPage = SBRAID_XORALL_BUF_IDX;
		ubBitmap = gpRS_Task.ubErrFrameBMP_ForHBSBRetry_Ori;
		M_UART(ERROR_SOFTBIT_RAID_, "B0 \n");	//[SBRAID] TO BUF0
	}
	else { //  BACKUP_RAIDECC_RESULT_TO_BUF1_MODE
		ubBufOffsetByPage = SBRAID_FAIL_PAGE_BUF_IDX;
		ubBitmap = 0xFF;
		M_UART(ERROR_SOFTBIT_RAID_, "B1 \n");	//[SBRAID] TO BUF1
	}
	// clear DATA buf
	memset((void *)&DMACParam, 0, sizeof(DMACParam));
	DMACParam.DMACSetValue.ulDestAddr =  (gSBRaidTask.ulDataBaseAddr + (  ubBufOffsetByPage * (BC_4KB * gub4kEntrysPerPlane))) ;
	DMACParam.DMACSetValue.ulLowValue = 0;
	DMACParam.DMACSetValue.ulHighValue = 0;
	DMACParam.DMACSetValue.ul32ByteNum = SIZE_IN_32B((BC_4KB * gub4kEntrysPerPlane));
	gpRetry->ubWaitDMACDoneFlag = TRUE;
	DMACSetValue(&DMACParam, gulReadRetryDMACDone_CallBack, 0);
	while (gpRetry->ubWaitDMACDoneFlag == TRUE) {
		DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
	}
	// clear SPARE BUF
	memset((void *)(gSBRaidTask.ulSpareBaseAddr + (  ubBufOffsetByPage  * (L4K_SIZE * gub4kEntrysPerPlane)) + (ubIdx * L4K_SIZE)),  0, (L4K_SIZE * gub4kEntrysPerPlane));

	// copy
	for (ubIdx = 0; ubIdx < gub4kEntrysPerPlane; ubIdx++) {
		if ((ubBitmap & BIT(ubIdx))) {
			memset((void *)&DMACParam, 0, sizeof(DMACParam));

			// copy data
			DMACParam.ulSourceAddr = gpRetry->ulBufAddr[ubIdx];
			DMACParam.ulDestAddr = (gSBRaidTask.ulDataBaseAddr + (  ubBufOffsetByPage * (BC_4KB * gub4kEntrysPerPlane)) + (ubIdx * BC_4KB));
			DMACParam.ul32ByteNum = SIZE_IN_32B(BC_4KB);
			gpRetry->ubWaitDMACDoneFlag = TRUE;
			DMACCopy(DMAC_MODE_NORMAL_QUEUE, &DMACParam, gulReadRetryDMACDone_CallBack, 0);
			while (gpRetry->ubWaitDMACDoneFlag == TRUE) {
				DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
			}
			// copy spare
			if (BACKUP_RAIDECC_RESULT_TO_BUF0_MODE  == ubMode) {
				memcpy((void *)(gSBRaidTask.ulSpareBaseAddr + (  ubBufOffsetByPage  * (L4K_SIZE * gub4kEntrysPerPlane)) + (ubIdx * L4K_SIZE)), (void *)(gpRS_Task.ulSBASE_Addr  + ubIdx * L4K_SIZE), L4K_SIZE);
			}
			else {
				memcpy((void *)(gSBRaidTask.ulSpareBaseAddr + (  ubBufOffsetByPage  * (L4K_SIZE * gub4kEntrysPerPlane)) + (ubIdx * L4K_SIZE)), (void *)(gSBTask.ulSpareAddr  + ubIdx * L4K_SIZE), L4K_SIZE);
			}

			M_UART(ERROR_SOFTBIT_RAID_, "H \n");	//[SBRAID] H
		}
	}

}

void RetrySBRAIDRemoveErrPage(U8 ubErrPageIdx)
{
	if (gSBRaidTask.ubOriginErrPageOfAllPageIdx == ubErrPageIdx) {

		M_UART(RETRY_SBRAID_, "\n Origin: %d \n", ubErrPageIdx);

		gSBRaidTask.ubTargetPageDone = TRUE;
	}
	else { //Update ubErrPageTotalCnt only when Not Target page
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, gSBRaidTask.ubErrPageTotalCnt > 0);
		gSBRaidTask.ubErrPageTotalCnt--;

		M_UART(RETRY_SBRAID_, "\n [SBRAID] SBRAID_ErrPageTotalCnt--, %d \n", gSBRaidTask.ubErrPageTotalCnt);

		//Set ErrPage Corrected BM
		M_SET_BITMAP(gSBRaidTask.ubErrPageCorrectedBMP, ubErrPageIdx);
	}
}

void RetrySBRAIDGotoEndFlow(U8 ubPass)
{
	if (TRUE == ubPass) {		//Pass flow
		gpRetry->RetryStateCnt.ulRetrySBRSPass++;
		gpRetry->RetryJobList[gpRetry->ubHead].ubErrorP4KBMP = 0;
		if (RETRY_FAIL_HANDLE) {
			gpRetry->RetryJobList[gpRetry->ubHead].btRetryFail = FALSE;
		}
	}
	else { 	//Fail flow
		gpRetry->RetryStateCnt.ulRetrySBRSFail++;
		if (RETRY_FAIL_HANDLE) {
			gpRetry->RetryJobList[gpRetry->ubHead].btRetryFail = TRUE;
		}
	}
	gpRetry->ubRetryState = FLH_RETRY__RETRY_DONE_RS;
	gSBRaidTask.ubRSDecodeFlowLoopBreakFlag = 1;
	gpRS_Task.ubState = RAIDECC_RETRY_DECODE_IDLE;
	gSBRaidTask.ubState = SOFTBITRAID_IDLE;
}

void RetrySBRAIDDMACCopy(U8 ubBufIdx,  U8 ubMode, U8 ubIsFullBitmapOrNot)
{
	DMACParam_t DMACParam;
	U8 ubIdx;
	U8 ubBitmap;
	memset((void *)&DMACParam, 0, sizeof(DMACParam));

	if (TRUE == ubIsFullBitmapOrNot) {
		ubBitmap = 0xFF;
	}
	else {
		ubBitmap = gpRS_Task.ubErrFrameBMP_ForHBSBRetry_Ori;
	}

	for (ubIdx = 0; ubIdx < gub4kEntrysPerPlane; ubIdx++) {
		if ((ubBitmap & BIT(ubIdx))) {

			// copy data
			if (SOFTBITRAID_RETRY_FROM_RETRYBUF_MODE == ubMode) {
				DMACParam.ulSourceAddr = gpRetry->ulBufAddr[ubIdx];
				DMACParam.ulDestAddr = (gSBRaidTask.ulDataBaseAddr +  (ubBufIdx * BC_4KB * gub4kEntrysPerPlane) + (ubIdx * BC_4KB));

				//SBRAID Check Point 0415
				if (DEBUG_SBRAID_EN) {
					M_UART(RETRY_SBRAID_, "\n SBRAID BufIdx:%d", ubBufIdx);
					U32 *pubDataPtr = (U32 *)(gpRetry->ulBufAddr[ubIdx]);
					U32 ulIdx = 0;
					ulIdx = 1;
					M_UART(RETRY_SBRAID_, "\nSource Data Idx %d, %x ", ulIdx, *(pubDataPtr + ulIdx));
					ulIdx = 2;
					M_UART(RETRY_SBRAID_, "\nSource Data Idx %d, %x ", ulIdx, *(pubDataPtr + ulIdx));
					M_UART(RETRY_SBRAID_, "\n ");

					pubDataPtr = (U32 *)(gSBRaidTask.ulDataBaseAddr +  (ubBufIdx * BC_4KB * gub4kEntrysPerPlane) + (ubIdx * BC_4KB));

					ulIdx = 1;
					M_UART(RETRY_SBRAID_, "\nDesti Data Idx %d, %x ", ulIdx, *(pubDataPtr + ulIdx));
					ulIdx = 2;
					M_UART(RETRY_SBRAID_, "\nDesti Data Idx %d, %x ", ulIdx, *(pubDataPtr + ulIdx));
					M_UART(RETRY_SBRAID_, "\n ");

				}
			}
			else {
				DMACParam.ulDestAddr = gpRetry->ulBufAddr[ubIdx];
				DMACParam.ulSourceAddr = (gSBRaidTask.ulDataBaseAddr +  (ubBufIdx * BC_4KB * gub4kEntrysPerPlane) + (ubIdx * BC_4KB));
			}
			DMACParam.ul32ByteNum = SIZE_IN_32B(BC_4KB);
			gpRetry->ubWaitDMACDoneFlag = TRUE;
			DMACCopy(DMAC_MODE_NORMAL_QUEUE, &DMACParam, gulReadRetryDMACDone_CallBack, 0);
			while (gpRetry->ubWaitDMACDoneFlag == TRUE) {
				DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
			}

			// copy spare
			if (SOFTBITRAID_RETRY_FROM_RETRYBUF_MODE == ubMode) {
				memcpy((void *)(gSBRaidTask.ulSpareBaseAddr + (  ubBufIdx  * (L4K_SIZE * gub4kEntrysPerPlane)) + (ubIdx * L4K_SIZE)), (void *)(gSBTask.ulSpareAddr + ubIdx * L4K_SIZE), L4K_SIZE);
			}
			else {
				memcpy((void *)(IRAM_BASE + GC_BACKUP_RETRY_OFF + ubIdx * L4K_SIZE), (void *)(gSBRaidTask.ulSpareBaseAddr + (  ubBufIdx  * (L4K_SIZE * gub4kEntrysPerPlane)) + (ubIdx * L4K_SIZE)), L4K_SIZE);
			}
		}
	}

}


void RetrySBRAIDNCSVUCSendInfo(U8 ubMTIdx, FlhMT_t *pMTTemplate, U32 ulBufAddr, U32 ulSpareAddr, U8 ubMode)
{
	FlhMT_t *pMT = (FlhMT_t *)M_MT_ADDR(ubMTIdx);
	MTCfg_t uoMTConfig;
	U16 *puwFPUPtr = (U16 *)(IRAM_BASE + FPU_NCS_OFF);

	L4KTable16B_t *pul4k_ptr = NULL;

	if (SBRAID_NCS_COMPARE_SBRAID_XORALL == ubMode) {
		puwFPUPtr[4] = FPU_ADR_1B(0xE0); // R0 : XOR ALL Data
		puwFPUPtr[5] = FPU_ADR_1B(0x08); // R1+R2: Size 2048(ECC Frame size) + 8(Spare Size) = 0x0808 Byte
		puwFPUPtr[6] = FPU_ADR_1B(0x08);
	}
	else if (SBRAID_NCS_COMPARE_SBRAID_SB6 == ubMode) {
		puwFPUPtr[4] = FPU_ADR_1B(0xE1); // R0 : SB6 Data
		puwFPUPtr[5] = FPU_ADR_1B(0x08); // R1+R2: Size 2048(ECC Frame size) + 8(Spare Size) = 0x0808 Byte
		puwFPUPtr[6] = FPU_ADR_1B(0x08);
	}
	else {
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
	}
	// Copy Data to VUC Address
	memcpy((void *)gulVUCAddr, (void *)ulBufAddr, BC_2KB);

	M_UART(RETRY_SBRAID_, "\n NCS_C: %x", puwFPUPtr[4]);	//NCS CMD

	// Reuse original MT index, clear done message
	gMTMgr.ubMTDoneMsg[ubMTIdx - MT_RETRY_START_INDEX].ubAll = 0;

	memcpy((void *)pMT, (void *)pMTTemplate, MT_SIZE);

	pMT->dma.btForce_R_Fail = FALSE;
	pMT->dma.btBMUAllocateEn = FALSE;
	pMT->dma.uwFPUPtr = FPU_NCS_OFF;
	pMT->cmd.btBusy = TRUE;
	pMT->cmd.btUpdPollingSequence = TRUE;
#if (FIP_VIRTUAL_ADDRESS_WORKAROUND_EN)
	M_FIP_SET_POL_SEQUENCE_SELECT(pMT->cmd.POL_SEQ_SEL, (M_FIP_GET_FSA_DIE_NUMBER(pMT->cmd.uliFSA0_1, pMT->cmd.ALUSelect) ? POL_SEQ_FPU_ENTRY_READ_STATUS_BUSY_20_DIE1 : POL_SEQ_FPU_ENTRY_READ_STATUS_BUSY_20));        //should Notice FPU still busy after Recieve FIP CQ
#else /* (FIP_VIRTUAL_ADDRESS_WORKAROUND_EN) */
	M_FIP_SET_POL_SEQUENCE_SELECT(pMT->cmd.POL_SEQ_SEL, POL_SEQ_FPU_ENTRY_READ_STATUS_BUSY_20); 	   //should Notice FPU still busy after Recieve FIP CQ, this function is no use
#endif /* (FIP_VIRTUAL_ADDRESS_WORKAROUND_EN) */

	pMT->dma.btConversionBypass = TRUE;
	pMT->dma.btGC = TRUE;

	pMT->dma.btLDPCCorrectEn = FALSE;
	pMT->dma.btCRCCheckDis = TRUE;
	pMT->dma.btDisableUDMA = TRUE;
	pMT->cmd.NormalTargetCPU = MT_TARGET_CPU0;
	pMT->cmd.ErrorTargetCPU = MT_TARGET_CPU0;

	pMT->dma.FrameNum = 1;
	pMT->dma.btZipEn = FALSE;
	pMT->dma.btCompareEn = FALSE;
	pMT->dma.btBufferMode = FALSE;
#if (PS5017_EN || PS5021_EN)
	pMT->dma.btFpuPCAEn = TRUE;
#endif /* (PS5017_EN || PS5021_EN) */
	// Prepare another L4K table for retry MT
	pul4k_ptr = (L4KTable16B_t *)(IRAM_BASE + GC_BACKUP_RETRY_SKIP_DMA_OFF);
	pul4k_ptr->BitMap.Prog.ubSpareValid = BIT_MASK(SECTORS_PER_4K);
	pul4k_ptr->BitMap.Prog.BADR = gulVUCAddr >> SECTOR_SIZE_SHIFT;

	pul4k_ptr->BitMap.Prog.FW = (U32)(ulSpareAddr - IRAM_BASE);

	M_UART(RETRY_SBRAID_, "\n NCS_Sp:%x, LCA:%x", ulSpareAddr, pul4k_ptr->BitMap.Prog.ulLCA);

	// point to new L4K offset
	pMT->dma.L4KSparePtr = GC_BACKUP_RETRY_SKIP_DMA_OFF;

	// Global Trigger MT
	uoMTConfig.uoAll = 0;
	uoMTConfig.bits_recc.ubQUE_IDX = pMT->dma.ubCEValue;
	uoMTConfig.bits_recc.ubMT_IDX = ubMTIdx;
	uoMTConfig.bits_recc.btQOS = 0;
	uoMTConfig.u32.ulMT_CFG1 = 0;
	FlaGlobalTrigger(&uoMTConfig);

	while (!gMTMgr.ubMTDoneMsg[ubMTIdx - MT_RETRY_START_INDEX].btAllDone) {
		FWErrRecorder();
		FIPDelegateCmd();
	}
	//should Notice FPU still busy after Recieve FIP CQ

}

void RetrySBRAIDNCSCompareXORAll(FlhMT_t *pMTTemplate, U8 ubBufIdx, U8 ubIdx)
{
	// Get a new MT to avoid covering ECC info during delegating VUC flash CQ
	U8 ubNCSMTIdx = FlaGetFreeMTIndex();
	if (DEBUG_SBRAID_EN) {
		M_UART(RETRY_SBRAID_, "\nSBRAID_X, B:%d, Fr:%d", ubBufIdx, ubIdx);	//NCS Dump XORALL result, After XOR
		M_UART(RETRY_SBRAID_, "\nSprSBRAID_X_B_0: %x", *((U32 *)(gSBRaidTask.ulSpareBaseAddr + (  ubBufIdx  * (L4K_SIZE * gub4kEntrysPerPlane)) + 0 * L4K_SIZE)));	//SBRAID_FAIL_PAGE_BUF_IDX
		M_UART(RETRY_SBRAID_, "\nSprSBRAID_X_B_1:%x", *((U32 *)(gSBRaidTask.ulSpareBaseAddr + (  ubBufIdx  * (L4K_SIZE * gub4kEntrysPerPlane)) + 1 * L4K_SIZE)));	//SBRAID_FAIL_PAGE_BUF_IDX
		M_UART(RETRY_SBRAID_, "\nSprSBRAID_X_B_2:%x", *((U32 *)(gSBRaidTask.ulSpareBaseAddr + (  ubBufIdx  * (L4K_SIZE * gub4kEntrysPerPlane)) + 2 * L4K_SIZE)));	//SBRAID_FAIL_PAGE_BUF_IDX
		M_UART(RETRY_SBRAID_, "\nSprSBRAID_X_B_3:%x", *((U32 *)(gSBRaidTask.ulSpareBaseAddr + (  ubBufIdx  * (L4K_SIZE * gub4kEntrysPerPlane)) + 3 * L4K_SIZE)));	//SBRAID_FAIL_PAGE_BUF_IDX
	}
	// copy spare from SBRAID Backup buff to IRAM temp
	memcpy((void *)(gSBTask.ultmp_SpareAddr + ubIdx * L4K_SIZE), (void *)(gSBRaidTask.ulSpareBaseAddr + (  ubBufIdx  * (L4K_SIZE * gub4kEntrysPerPlane)) + (ubIdx * L4K_SIZE)), L4K_SIZE);
	if (DEBUG_SBRAID_EN) {
		M_UART(RETRY_SBRAID_, "\nSprSBRAID_X_B_0: %x", *((U32 *)(gSBTask.ultmp_SpareAddr + 0 * L4K_SIZE)));	//SBRAID_XORALL_BUF_IDX
		M_UART(RETRY_SBRAID_, "\nSprSBRAID_X_B_1:%x", *((U32 *)(gSBTask.ultmp_SpareAddr + 1 * L4K_SIZE)));	//SBRAID_XORALL_BUF_IDX
		M_UART(RETRY_SBRAID_, "\nSprSBRAID_X_B_1:%x", *((U32 *)(gSBTask.ultmp_SpareAddr + 2 * L4K_SIZE)));	//SBRAID_XORALL_BUF_IDX
		M_UART(RETRY_SBRAID_, "\nSprSBRAID_X_B_1:%x", *((U32 *)(gSBTask.ultmp_SpareAddr + 3 * L4K_SIZE)));	//SBRAID_XORALL_BUF_IDX
	}
	RetrySBRAIDNCSVUCSendInfo(ubNCSMTIdx, pMTTemplate, (gSBRaidTask.ulDataBaseAddr +  (ubBufIdx * BC_4KB * gub4kEntrysPerPlane) + (ubIdx * BC_4KB)), (gSBTask.ultmp_SpareAddr + ubIdx * L4K_SIZE), SBRAID_NCS_COMPARE_SBRAID_XORALL);
	FlaAddFreeMTIndex(ubNCSMTIdx);
}

void RetrySBRAIDNCSSB6Compare(FlhMT_t *pMTTemplate, U32 ulBufAddr, U32 ulSpareAddr)
{
	// Get a new MT to avoid covering ECC info during delegating VUC flash CQ
	U8 ubNCSMTIdx = FlaGetFreeMTIndex();
	RetrySBRAIDNCSVUCSendInfo(ubNCSMTIdx, pMTTemplate, ulBufAddr, ulSpareAddr, SBRAID_NCS_COMPARE_SBRAID_SB6);
	FlaAddFreeMTIndex(ubNCSMTIdx);
}

void RetrySBRAIDMain()
{
	U8 ubResult;

	do {
		switch (gSBRaidTask.ubState) {
		case SOFTBITRAID_START:
			// Trigger DMAC XOR : ( (Buf 0, backup RS decode result on this buf) XOR (Buf 1, result of SB after RS for ErrPage) ) => Buf 0
			// using DMAC_BitmapXOR(), XORALL = (P+N) xor (F')
			RetrySBRAIDDataXOR(SBRAID_XORALL_BUF_IDX, SBRAID_FAIL_PAGE_BUF_IDX);

			//Report XORALL result, after tartget page RAID result + SB decoding flow
			if (NCS_SBRAID_EN) {
				M_UART(RETRY_SBRAID_, "\nSBRAID, Return XORALL\n");

				//NES Compare XORAll
				RetrySBRAIDNCSCompareXORAll(&gpRS_Task.MTTemplate, SBRAID_XORALL_BUF_IDX, 0);
			}
			//SBRAID Check Point 1109
			if (DEBUG_SBRAID_DATA_COMPARE) {
				U32 *pulXOR_ALLData = NULL;
				U32 *pulXOR_ALLSpare = NULL;
				U8 ubFrame = 0;
				U8 ub2kFrame = 0;

				if (gpRetry->ubPageFailNotFix) {
					M_UART(ERROR_SOFTBIT_RAID_, "[SBRAID] True Normal Page Fail skip check_F \n");
				}
				else {
					for (ubFrame = 0 ; ubFrame < gub4kEntrysPerPlane ; ubFrame++) {
						if (gpRS_Task.ubErrFrameBMP & BIT(ubFrame)) {
							M_UART(ERROR_SOFTBIT_RAID_, "Check XOR_ALL for Frame: %d \n", ubFrame);
							for (ub2kFrame = 0 ; ub2kFrame < 2; ub2kFrame++) {
								//Data
								pulXOR_ALLData = (U32 *)(gSBRaidTask.ulDataBaseAddr + ubFrame * BC_4KB + ub2kFrame * BC_2KB + (gpRS_Task.ulTargetVCAToRead % BC_2KB)); //Use VCA to be rand number
								M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, *pulXOR_ALLData == 0);

							}
							//Spare
							pulXOR_ALLSpare = (U32 *)(gSBRaidTask.ulSpareBaseAddr + ubFrame * L4K_SIZE);
							M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, *pulXOR_ALLSpare == 0);
						}
					}
				}
			}
			if (gSBRaidTask.ubTargetPageDone && (1 == gSBRaidTask.ubErrPageTotalCnt)) {	// Directly Normal Page only 1 Fail,  Target page is Fixed

				M_UART(RETRY_SBRAID_, "\nSBRAID Buff Idx: %d", gSBRaidTask.ubErrPageTotalCntIdx + SOFTBITRAID_BUF_START_OFFSET_BY_PAGE);

				// do ErrPage RS, Update Result to SBRAID Buff
				RetrySBRAIDDataXOR((gSBRaidTask.ubErrPageTotalCntIdx + SOFTBITRAID_BUF_START_OFFSET_BY_PAGE), SBRAID_XORALL_BUF_IDX);

				//Return Normal Page fix by XOR (SB6) to NCS
				if (NCS_SBRAID_EN) {
					M_UART(RETRY_SBRAID_, "\nNormal_SB6_SP: %x, %d, %d", gpRS_Task.ultmp_SpareAddr, gSBTask.ubCurr_frm_idx, gSBTask.ubCurrentLDPCFrameIdx);

					//Copy Spare Info From DBUF to IRAM
					memcpy((void *)(gpRS_Task.ultmp_SpareAddr), (void *)(gSBRaidTask.ulSpareBaseAddr +  ((gSBRaidTask.ubErrPageTotalCntIdx + SOFTBITRAID_BUF_START_OFFSET_BY_PAGE) * L4K_SIZE * gub4kEntrysPerPlane)), L4K_SIZE);
					//NCS Compare SB6
					RetrySBRAIDNCSSB6Compare(&gpRS_Task.MTTemplate, (gSBRaidTask.ulDataBaseAddr +  ((gSBRaidTask.ubErrPageTotalCntIdx + SOFTBITRAID_BUF_START_OFFSET_BY_PAGE) * BC_4KB * gub4kEntrysPerPlane)), gpRS_Task.ultmp_SpareAddr);
				}
				// copy data from Fail_page_buf(BufIdx1) to Retry Buf
				RetrySBRAIDDMACCopy(SBRAID_FAIL_PAGE_BUF_IDX,  SOFTBITRAID_RETRY_TO_RETRYBUF_MODE, TRUE);

				gSBRaidTask.ubDealingWithErrPageFlag = SBRAID_NORMAL_FAIL_ALL_DONE;
				// go to pass flow
				RetrySBRAIDGotoEndFlow(TRUE);
			}
			else {
				gSBRaidTask.ubState = SOFTBITRAID_CHECK_NEXT_ERRPAGE;
				M_UART(ERROR_SOFTBIT_RAID_, "[SBRAID] SOFTBITRAID_START \n");
			}
			break;
		case SOFTBITRAID_BACK_TO_CHECK_LOOP:
			// update SB result to parity, so do XOR
			if (gSBRaidTask.ubDealingWithErrPageFlag) {
				RetrySBRAIDDataXOR(SBRAID_XORALL_BUF_IDX, SBRAID_FAIL_PAGE_BUF_IDX);
			}
			else {
				RetrySBRAIDDataXOR(SBRAID_XORALL_BUF_IDX, (gSBRaidTask.ubErrPageTotalCntIdx + SOFTBITRAID_BUF_START_OFFSET_BY_PAGE));
			}

			//SBRAID Check Point 0415
			M_UART(RETRY_SBRAID_, "\n Back_to_check_Loop_Update_XORALL %d", gSBRaidTask.ubErrPageTotalCntIdx);

			if (NCS_SBRAID_EN) {
				//NES Compare XORAll
				RetrySBRAIDNCSCompareXORAll(&gpRS_Task.MTTemplate, SBRAID_XORALL_BUF_IDX, 0);
			}

			//SBRAID Check Point 1109
			if (DEBUG_SBRAID_DATA_COMPARE) {
				U32 *pulXOR_ALLData = NULL;
				U32 *pulXOR_ALLSpare = NULL;
				U8 ubFrame = 0;
				if (gpRetry->ubPageFailNotFix) {
					M_UART(ERROR_SOFTBIT_RAID_, "[SBRAID] True Normal Page Fail skip check_N \n");

				}
				else {
					for (ubFrame = 0 ; ubFrame < gub4kEntrysPerPlane ; ubFrame++) {
						if (gpRS_Task.ubErrFrameBMP & BIT(ubFrame)) {

							M_UART(ERROR_SOFTBIT_RAID_, "Check XOR_ALL for Frame: %d \n", ubFrame);

							//Data
							//First 2k
							pulXOR_ALLData = (U32 *)(gSBRaidTask.ulDataBaseAddr + ubFrame * BC_4KB + ((U32)RngGetRandomValue() % 1024));
							M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, *pulXOR_ALLData == 0);
							//Second 2k
							pulXOR_ALLData = (U32 *)(gSBRaidTask.ulDataBaseAddr + ubFrame * BC_4KB  + BC_2KB + ((U32)RngGetRandomValue() % 1024));
							M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, *pulXOR_ALLData == 0);
							//Spare
							pulXOR_ALLSpare = (U32 *)(gSBRaidTask.ulSpareBaseAddr + ubFrame * L4K_SIZE);
							M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, *pulXOR_ALLSpare == 0);
						}
					}
				}

			}

			M_UART(RETRY_SBRAID_, "\n[SBRAID] Update XORALL, Back 2 chk loop \n");
			if (SBRAID_IDLE_MODE == gSBRaidTask.ubDealingWithErrPageFlag) {
				gSBRaidTask.ubErrPageTotalCntIdx++;
			}
			ubResult = RetrySBRAIDCheckLoopBoundary();
			if (ubResult) {
				M_UART(ERROR_SOFTBIT_RAID_, "[SBRAID] Dealwith Errpage Flag :%d \n", gSBRaidTask.ubDealingWithErrPageFlag);
				break;
			}
		//No break
		case SOFTBITRAID_CHECK_ITERATION:
		case SOFTBITRAID_CHECK_NEXT_ERRPAGE:
			// Main Loop
			do {
				//Check ErrPage BitMap
				if (M_CHK_BITMAP(gSBRaidTask.ubErrPageBMP, gSBRaidTask.ubErrPageOfAllPageIdx )) {
					M_UART(ERROR_SOFTBIT_RAID_, "[SBRAID] ubErrPageBMP Match \n");
					//Check ErrPage Corrected BitMap
					if (M_CHK_BITMAP(gSBRaidTask.ubErrPageCorrectedBMP, gSBRaidTask.ubErrPageOfAllPageIdx)) {
						M_UART(ERROR_SOFTBIT_RAID_, "[SBRAID] ALREADY DONE, DO NOTHING \n");
						// ALREADY DONE, DO NOTHING .
						gSBRaidTask.ubErrPageTotalCntIdx++;
					}
					else {
						if (gSBRaidTask.ubErrPageOfAllPageIdx != gSBRaidTask.ubOriginErrPageOfAllPageIdx) {

							M_UART(RETRY_SBRAID_, "\nGet Normal Page from XORALL :%d", gSBRaidTask.ubErrPageTotalCntIdx);

							// do XOR
							RetrySBRAIDDataXOR(SBRAID_XORALL_BUF_IDX, (gSBRaidTask.ubErrPageTotalCntIdx + SOFTBITRAID_BUF_START_OFFSET_BY_PAGE));

							// Copy Buf0 to Retry Buf
							RetrySBRAIDDMACCopy(SBRAID_XORALL_BUF_IDX,  SOFTBITRAID_RETRY_TO_RETRYBUF_MODE, FALSE);

							M_UART(ERROR_SOFTBIT_RAID_, "[SBRAID] SOFTBITRAID_CHECK_NEXT_ERRPAGE \n");
							M_UART(ERROR_SOFTBIT_RAID_, "[SBRAID] ErrPageTotalCntIdx: %d, ErrPageOfAllPage_Idx: %d,  \n", gSBRaidTask.ubErrPageTotalCntIdx, gSBRaidTask.ubErrPageOfAllPageIdx);

							// do SB decoding flow (First go RS flow to get PCA)
							gSBRaidTask.ubSBCORRResult = SOFTBITRAID_SB_CORR_NULL;
							gpRetry->ubRetryState = FLH_RETRY__SBRAID_CREATE_RS_TASK;
							gSBRaidTask.ubState = SOFTBITRAID_BUSY_DOING_RAID;
							gSBRaidTask.ubRSDecodeFlowLoopBreakFlag = 1;

							break;
						}
					}
				}
				RetrySBRAIDCheckLoopBoundary();
			} while (gSBRaidTask.ubErrPageTotalCntIdx  <  gSBRaidTask.ubErrPageTotalCntMax);
			break;

		case SOFTBITRAID_BUSY_DOING_SB:
		case SOFTBITRAID_BUSY_DOING_SBRAID:
		case SOFTBITRAID_IDLE:
		default:
			break;
		}
		if ((CALLBACK_FUNC_IN_COMMON_CODEBANK == FALSE) && (gSBRaidTask.ubRSDecodeFlowLoopBreakFlag)) {
			break;
		}
	} while ( (CALLBACK_FUNC_IN_COMMON_CODEBANK == FALSE)  );

}

#endif /*(RETRY_SBRAID_EN)*/
