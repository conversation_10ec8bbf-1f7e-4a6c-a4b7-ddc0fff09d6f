#ifndef _FTL_IOR_H_
#define _FTL_IOR_H_
/*
* ---------------------------------------------------------------------------------------------------
*   header files
* ---------------------------------------------------------------------------------------------------
*/
#include "typedef.h"
#include "fw_vardef.h"
#include "hal/db/db_api.h"
#if (NVME == HOST_MODE)
#include "nvme_api/nvme/shr_hal_nvme.h"
#elif (USB == HOST_MODE) /*(NVME == HOST_MODE)*/
#include "hal/usb/usb.h"
#else /*(NVME == HOST_MODE)*/
#include "hal/sata/sata.h"
#endif /*(NVME == HOST_MODE)*/

/*
 * ---------------------------------------------------------------------------------------------------
 *   definitions
 * ---------------------------------------------------------------------------------------------------
 */
#define GROUP_NUM						(8)
#define IOT_DRIVELOG_ENTRY_NUM			((DEBUG_IOR_DRIVELOG) ? (6) : (0))
#define IOR_INCREASE_LOWER_LIMIT_AND_GROUP_SIZE	(0)
#define IOR_DECREASE_LOWER_LIMIT_AND_GROUP_SIZE	(1)
#define BIT_IOR_EVENT_PREREAD			(BIT0)
#define BIT_IOR_EVENT_GC				(BIT1)

#define BIT_IOR_EVENT_TEST				(BIT7)

#define RCQ_POP_CNT_FOR_TEST_CHANGE_RLB_LOWER_LIMIT	(1000)
#if PS5017_EN
#define MAX_IOT_ENTRY_NUM				(128)
#else /* PS5017_EN */
#define MAX_IOT_ENTRY_NUM				(32)
#endif /* PS5017_EN */

#define IOT_ENTRY_SIZE	32
#define NONALIGN4K_TNLC_NUM				(2) //non-align 4k read command occupied 2 LCA
/*
 * ---------------------------------------------------------------------------------------------------
 *  enum
 * ---------------------------------------------------------------------------------------------------
 */
typedef enum IORReserveRLBStateEnum {
	IOR_RESERVE_INIT,
#if (USB == HOST_MODE)
	IOR_CHECK_IOT_ALIGNMENT,
#endif /* (USB == HOST_MODE) */
	IOR_PUSH_FIP_FIFO,
	IOR_RESERVE_RLB,
	IOR_PUSH_APU_IOT,
} IORReserveRLBStateEnum_t;

typedef enum IORChangeRLBLowerLimitStateEnum {
	IOR_CHANGE_RLB_LOWER_LIMIT_INIT,
	IOR_CHANGE_RLB_LOWER_LIMIT_CHECK_GROUP_FULL,
	IOR_CHANGE_RLB_LOWER_LIMIT_WAIT_BUF,
	IOR_CHANGE_RLB_LOWER_LIMIT_CHECK_SUFFICIENT,
	IOR_CHANGE_RLB_LOWER_LIMIT_WAIT_FIP,
	IOR_CHANGE_RLB_LOWER_LIMIT_WAIT_BARRIER,
	IOR_CHANGE_RLB_LOWER_LIMIT_WAIT_BOOKING_TABLE,
	IOR_CHANGE_RLB_LOWER_LIMIT_UPDATE_GROUP_SIZE,
	IOR_CHANGE_RLB_LOWER_LIMIT_DONE
} IORChangeRLBLowerLimitStateEnum_t;

/*
 * ---------------------------------------------------------------------------------------------------
 *  data types
 * ---------------------------------------------------------------------------------------------------
 */
#if PS5017_EN
typedef union {
	U32 ulAll;
	struct {
		U32 Size 			: 8;
		U32 btMultiTrigger	: 1; // indicate this info configured to iot is not the last part of this ctag.
		U32 GroupIdx 		: 3;
		U32 btHold 			: 1; // indicate the cmd (and the later configured cmds) will transfer until previous outstanding cmds are done.
		U32 Offset 			: 10;
		U32 CTag 			: 8;
		U32 btCmd 			: 1; //1'b0:Read, 1'b1:Configure
	};
} IOTEntry_t;
#else /* PS5017_EN */
typedef union {
	U32 ulAll;
	struct {
		U32 Size 			: 11;
		U32 btMultiTrigger	: 1; // indicate this info configured to iot is not the last part of this ctag.
		U32 GroupIdx 		: 3;
		U32 btHold 			: 1; // indicate the cmd (and the later configured cmds) will transfer until previous outstanding cmds are done.
		U32 Offset 			: 10;
		U32 CTag 			: 5;
		U32 btCmd 			: 1; //1'b0:Read, 1'b1:Configure
	};
} IOTEntry_t;
#endif  /* PS5017_EN */

typedef struct {
	U16 uwChangeGroupSize;		// New GS & LL (FW now use GS == LL)
	U16 uwTestChangeLowerLimitPopRCQCnt;// for BIT_IOR_EVENT_TEST use
	U16 uwDebugAPUFreeCnt;		// debug use
	U8 ubChangeRLBEvent;		// Only handle one event once
	U8 ChangeSizeGroupIdx : 4;
	U8 btStopReserveRLB	: 1;	// to avoid SyncCmdHandler reserve RLB only when update RLB LL success
	U8 btNeedChangeSize	: 1;	// for BIT_IOR_EVENT_TEST use
	U8 btChangeGroupSizeOption : 1; // valid when ubChangeRLBEvent != 0
	U8 btIORCOP0Barrier	: 1;	// for assert Send IOR cmd after barrier
	IORChangeRLBLowerLimitStateEnum_t State;
} IORChangeRLBLowerLimitInfo;

typedef struct {
	U16 uwRLBOffset; 			// RLB Offset of RCQ Head
	U16 uwRLBSize; 				// Res RLB total num for RCQ use
	U16 uwGroupSize;			// GroupSize == LowerLimit == ApuFreeCnt
	U16 uwCurrentGroupHeadRCQOffset;// record CurrentGroupIdx RCQ head offset
	U16 uwSendCop0SQNum;		// CurrentGroupIdx Send Cop0 SQ num == GroupSize, can reserve next group (no need to wait CQ)
	U16 uwReserveRLBNum;		// ReserveGroupIdx reserved RLB num
	U8 CurrentGroupIdx : 4;		// for COP0API_SendSQ_InOrderRead send cop0 SQ use
	U8 ReserveGroupIdx : 4;		// for FTLIORReserveRLB reserve RLB use
	U8 btCOP1BarrierEvent : 1;	// when FW send COP1 barrier, RCQ need to process head only (can use barrier mgr doing event + type to check)
	U8 SendIOTCnt : 3;
	U8 btIOTGroupCheckOff: 1;
	U8 btIORFullDriveLogEn: 1;
	U8 : 2;
	IORReserveRLBStateEnum_t ReserveRLBState;
	IOTEntry_t IOTEntry;
	IORChangeRLBLowerLimitInfo ChangeRLBLowerLimit;
} InOrderReadInfo;

typedef union {
	U8 ubAll;
	struct {
		U8 btChangeSuccess	: 1;
		U8 btBufLack		: 1;
		U8 btEventPending	: 1;
		U8 btLowerLimitInsufficient	: 1;
		U8 btOverRLBUpperLimit		: 1;
		U8 btHostEventStop	: 1;
		U8					: 2;
	};
} IORChangeRLBLowerLimitStatus_t;


/*
 * ---------------------------------------------------------------------------------------------------
 *   macros
 * ---------------------------------------------------------------------------------------------------
 */
#define M_IOR_GET_NEXT_GROUP_IDX(GROUP_IDX) ((GROUP_IDX + 1) & (GROUP_NUM - 1))

/*
 * ---------------------------------------------------------------------------------------------------
 *   extern global variables
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *   static global variables
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *   private prototypes
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *   private
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *   public
 * ---------------------------------------------------------------------------------------------------
 */
void FTLIORCheckRCQTableValid(RCQTableInfo_t *ulRCQTableInfo);
#if (NVME == HOST_MODE)
U8 FTLIORReserveRLB(OPT_HCMD_PTR pCmd);
#elif (USB == HOST_MODE) /*(NVME == HOST_MODE)*/
U8 FTLIORReserveRLB(PUSBCURRCMD_t pCurrentCMD);
#else /*(NVME == HOST_MODE)*/
U8 FTLIORReserveRLB(SATAHCMD_t *pNReadWriteCQHeaderInfo);
#endif /*(NVME == HOST_MODE)*/
IORChangeRLBLowerLimitStatus_t FTLIORChangeRLBLowerLimit(U8 ubEvent, U16 uwNewRLBLowerLimit);
void IORTestChangeGSLLFlow(void);


#endif /* _FTL_IOR_H_ */
