#ifndef _S17_DMAC_CMD_TYPE_H_
#define _S17_DMAC_CMD_TYPE_H_
#include "setup.h"
#include "typedef.h"

//Common
#define DMAC_CMD_COMMON_OP_SHIFT            (0)
#define DMAC_CMD_COMMON_ENTRY_SIZE_SHIFT    (8)
#define DMAC_CMD_COMMON_RND_EN_SHIFT    (10)
#define DMAC_CMD_COMMON_E2E_EN_SHIFT        (11)
#define DMAC_CMD_COMMON_MASK_ENABLE_SHIFT   (12)
#define DMAC_CMD_COMMON_DEPEND_SHIFT        (13)
#define DMAC_CMD_COMMON_SETCQ_SHIFT         (15)
#define DMAC_CMD_COMMON_TAGID_SHIFT         (16)

//SEARCH
#define DMAC_CMD_SEARCH_DW0_OP_SHIFT 			(0)
#define DMAC_CMD_SEARCH_DW0_ENTRY_SIZE_SHIFT 	(8)
#define DMAC_CMD_SEARCH_DW0_SEARCH_MODE_SHIFT 	(10)
#define DMAC_CMD_SEARCH_DW0_FIND_LAST_ONE_SHIFT (12)
#define DMAC_CMD_SEARCH_DW0_DEPEND_SHIFT 		(13)
#define DMAC_CMD_SEARCH_DW0_ERR_INS_SHIFT 		(14)
#define DMAC_CMD_SEARCH_DW0_SETCQ_SHIFT 		(15)
#define DMAC_CMD_SEARCH_DW0_TAGID_SHIFT 			(16)
#define DMAC_CMD_SEARCH_DW0_FIND_ONE_ONLY_SHIFT (24)

//XOR
#define DMAC_CMD_XOR_DW0_OP_SHIFT			(0)
#define DMAC_CMD_XOR_DW0_OPERATOR_SHIFT 	(8)
#define DMAC_CMD_XOR_DW0_XOR_FAST_SHIFT 	(10)
#define DMAC_CMD_XOR_DW0_MASK_EN_SHIFT		(12)
#define DMAC_CMD_XOR_DW0_DEPEND_SHIFT 		(13)
#define DMAC_CMD_XOR_DW0_SETCQ_SHIFT 		(15)
#define DMAC_CMD_XOR_DW0_TAGID_SHIFT 			(16)



//CRC32
#define DMAC_CMD_CRC32_OP_SHIFT            		(0)
#define DMAC_CMD_CRC32_CHECK_EN_SHIFT           (8)
#define DMAC_CMD_CRC32_CAL_MODE_SHIFT           (9)
#define DMAC_CMD_CRC32_ROM_EN_SHIFT				(10)
#define DMAC_CMD_CRC32_DEPEND_SHIFT			(13)
#define DMAC_CMD_CRC32_SETCQ_SHIFT				(15)
#define DMAC_CMD_CRC32_TAGID_SHIFT				(16)

//COPY
#define DMAC_CMD_COPY_DW0_OP_SHIFT							(0)
#define DMAC_CMD_COPY_DW0_MERGE_IP_EN_SHIFT 				(5)
#define DMAC_CMD_COPY_DW0_COP1_IP_EN_SHIFT 					(6)
#define DMAC_CMD_COPY_DW0_E2E_CHECK_EN_SHIFT 				(8)
#define DMAC_CMD_COPY_DW0_E2E_CHECK_TYPE_SHIFT 				(9)
#define DMAC_CMD_COPY_DW0_E2E_GEN_EN_SHIFT 					(10)
#define DMAC_CMD_COPY_DW0_E2E_GEN_TYPE_SHIFT 				(11)
#define DMAC_CMD_COPY_DW0_MASK_EN_SHIFT 					(12)
#define DMAC_CMD_COPY_DW0_DEPEND_SHIFT 						(13)
#define DMAC_CMD_COPY_DW0_ERR_INS_SHIFT 					(14)
#define DMAC_CMD_COPY_DW0_SETCQ_SHIFT 						(15)
#define DMAC_CMD_COPY_DW0_TAGID_SHIFT 						(16)

//Scan Valid
#define DMAC_CMD_SCAN_VALID_COPY_DW0_OP_SHIFT			(0)
#define DMAC_CMD_SCAN_VALID_DW0_ATOM_SHIFT 				(7)
#define DMAC_CMD_SCAN_VALID_DW0_SCAN_TYPE_SHIFT 		(10)
#define DMAC_CMD_SCAN_VALID_DW0_DEPEND_SHIFT 			(13)
#define DMAC_CMD_SCAN_VALID_DW0_ERR_INS_SHIFT 			(14)
#define DMAC_CMD_SCAN_VALID_DW0_SETCQ_SHIFT 			(15)
#define DMAC_CMD_SCAN_VALID_DW0_TAGID_SHIFT 			(16)
#define DMAC_CMD_SCAN_VALID_DW0_START_IDX0_SHIFT 		(24)

#define DMAC_CMD_SCAN_VALID_DW1_START_IDX1_SHIFT 		(0)
#define DMAC_CMD_SCAN_VALID_DW1_SOURCE_ADDR_SHIFT 		(5)

#define DMAC_CMD_SCAN_VALID_DW2_START_IDX2_SHIFT 		(0)
#define DMAC_CMD_SCAN_VALID_DW2_TARGET_ADDR_SHIFT 		(5)

#define DMAC_CMD_SCAN_VALID_DW3_SEARCH_VB0_SHIFT 		(0)
#define DMAC_CMD_SCAN_VALID_DW3_SEARCH_VB1_SHIFT 		(13)
#define DMAC_CMD_SCAN_VALID_DW3_START_PTE0_SHIFT		(26)

#define DMAC_CMD_SCAN_VALID_DW4_SEARCH_VB2_SHIFT 		(0)
#define DMAC_CMD_SCAN_VALID_DW4_SEARCH_VB3_SHIFT 		(13)
#define DMAC_CMD_SCAN_VALID_DW4_START_PTE1_SHIFT		(26)

#define DMAC_CMD_SCAN_VALID_DW5_TARGET_LEN_SHIFT 		(0)
#define DMAC_CMD_SCAN_VALID_DW5_SOURCE_LEN_SHIFT 		(16)

#define DMAC_CMD_SCAN_VALID_DW6_SEARCH_VB4_SHIFT 		(0)
#define DMAC_CMD_SCAN_VALID_DW6_SEARCH_VB5_SHIFT 		(13)
#define DMAC_CMD_SCAN_VALID_DW6_START_PTE2_SHIFT		(26)

#define DMAC_CMD_SCAN_VALID_DW7_SEARCH_VB6_SHIFT 		(0)
#define DMAC_CMD_SCAN_VALID_DW7_SEARCH_VB7_SHIFT 		(13)
#define DMAC_CMD_SCAN_VALID_DW7_START_PTE3_SHIFT		(26)

//TrimMerge
#define DMAC_CMD_TRIM_MERGE_OP_SHIFT            (0)
#define DMAC_CMD_TRIM_MERGE_FORMAT_SHIFT            (8)
#define DMAC_CMD_TRIM_MERGE_MODE_SHIFT            (9)
#define DMAC_CMD_TRIM_MERGE_WRITE_LOCK_ERR_CHECK_EN_SHIFT            (10)
#define DMAC_CMD_TRIM_MERGE_DEPEND_SHIFT            (13)
#define DMAC_CMD_TRIM_MERGE_SETCQ_SHIFT            (15)
#define DMAC_CMD_TRIM_MERGE_TAGID_SHIFT            (16)

typedef struct DMACCommonCmd {
	U32 ulDW0;
	U32 ulDW1;
	U32 ulDW2;
	U32 ulDW3;
	U32 ulDW4;
	U32 ulDW5;
	U32 ulDW6;
	U32 ulDW7;
} DMACCommonCmd_t;
TYPE_SIZE_CHECK(DMACCommonCmd_t, SIZE_32B);

typedef struct DMACCommonRst {
	union {
		U32 ulDW0;
		struct {
			U8 btSTA				: 1;	// [0]
			U8 ubAXIReadErrSlave	: 2;	// [2:1]
			U8 ubAXIWriteErrSlave	: 2;	// [4:3]
			U8 btParityErrFail		: 1;	// [5]
			U8 btParityErrFixed		: 1;	// [6]
			U8						: 1;	// [7]
			U8 ubTagID				: 8;	// [15:8]
			U8 						: 8;	// [31:16]
		};
	};

	struct {
		U32 ulDW1;
	};
} DMACCommonRst_t;
TYPE_SIZE_CHECK(DMACCommonRst_t, SIZE_8B);


//********************************************************
//				DMAC	Copy
//********************************************************
typedef struct DMACCopyCmd {
	struct {
		U32 ubOP			: 5;	// [4:0]
		U32 btMergeIPEn		: 1;	// [5]
		U32 btCOP1IPEn		: 1;	// [6]
		U32 				: 1;	// [7]
		U32	btE2EChkEn		: 1;	// [8]
		U32 btE2EChkType	: 1;	// [9]
		U32 btE2EGenEn		: 1;	// [10]
		U32 btE2EGenType	: 1;	// [11]
		U32	btMskEn			: 1;	// [12]
		U32 btDepend		: 1;	// [13]
		U32	btErrIns		: 1;	// [14]
		U32	btSetCQ			: 1;	// [15]
		U32	ubTagID			: 8;	// [23:16]
		U32 				: 8;	// [31:24]
	};

	struct {
		U32	ulSADDR; // Need 32 byte align, hardware don't care bit[4:0].
	};

	struct {
		U32	ulTADDR; // Need 32 byte align, hardware don't care bit[4:0].
	};

	struct {
		U32 ulMergeFWSet	: 24;	// [23:0]
		U32 btMergeFWSetVLD	: 1;	// [24]
		U32					: 7;	// [31:25]
	};

	struct {
		U32 ulMsk;
	};

	struct {
		U32 ulLen			: 24;	// [23:0]
		U32	ubPCACRC		: 8;	// [31:24]
	};

	struct {
		U32 ulLCA;						//E17_porting_4TB
	};

	struct {
		U32 btValidateSource			: 1;	// [0]
		U32	btValidateTarget			: 1;	// [1]
		U32 btValidateAllocateLBSource	: 1;	// [2]
		U32 btValidateAllocateLBTarget	: 1;	// [3]
		U32 btUnlockSource				: 1;	// [4]
		U32 btUnlockTarget				: 1;	// [5]
		U32 btFreeSource				: 1;	// [6]
		U32	btFreeTarget				: 1;	// [7]
		U32	btGetPBNASource				: 1;	// [8]
		U32 btPEOP						: 1;	// [9]		//E17_porting_4TB
		U32	ubLBIDSource				: 3;	// [12:10]
		U32	btTargetFullFlag			: 1;	// [13]
		U32	btTargetZeroFlag			: 1;	// [14]
		U32	btTargetCmdEnd				: 1;	// [15]
		U32	btSourceFAR					: 1;	// [16]
		U32	btSourceQOP					: 1;	// [17]
		U32	ubTargetOPER				: 2;	// [19:18]
		U32	btUNAESEn					: 1;	// [20]
		U32 btUNZIPEn					: 1;	// [21]
		U32	btAESEn						: 1;	// [22]
		U32	btZIPEn						: 1;	// [23]
		U32	btCTSEn						: 1;	// [24]
		U32	ubZInfoSource				: 3;	// [27:25]
		U32	ubLBIDTarget				: 3;	// [30:28]
		U32 							: 1;	// [31]
	};
} DMACCopyCmd_t;
TYPE_SIZE_CHECK(DMACCopyCmd_t, SIZE_32B);

typedef struct DMACCopyRst {
	struct {
		U8 btSTA							: 1;	// [0]
		U8 ubAXIReadErrSlave				: 2;	// [2:1]
		U8 ubAXIWriteErrSlave				: 2;	// [4:3]
		U8 btParityErrFail					: 1;	// [5]
		U8 btParityErrFixed					: 1;	// [6]
		U8 btE2EType						: 1;	// [7]
		U8 ubTagID							: 8;	// [15:8]
		U8 btSourceGetPBNAFail				: 1;	// [0]
		U8 btTargetGetPBNAFail				: 1;	// [1]
		U8 btSourceValidateFail				: 1;	// [2]
		U8 btTargetValidateFail				: 1;	// [3]
		U8 btSourceValidateAllocateLBFail	: 1;	// [4]
		U8 btTargetValidateAllocateLBFail	: 1;	// [5]
		U8 btSourceUnlockFail				: 1;	// [6]
		U8 btTargetUnlockFail				: 1;	// [7]
		U8 btSourceFreeFail					: 1;	// [8]
		U8 btTargetFreeFail					: 1;	// [9]
		U8 btGetPBNABitmapAndZinfoEMax		: 1;	// [10]
		U8 btGetPBNABitmapAndZinfoNEMax		: 1;	// [11]
		U8 btReadDataBitmapError			: 1;	// [12]
		U8 ubZinfoTarget					: 3;	// [15:13]
	};

	union {
		struct {
			U32	AXIReadErr4KOffset			: 12;	// [11:0]
			U32 							: 4;	// [15:12]
			U32 AXIWriteErr4KOffset			: 12;	// [27:16]
			U32								: 4;	// [31:28]
		};
		struct {
			U32 uwLBOffset					: 10;	// [9:0]
			U32								: 22;	// [31:10]
		};
	};
} DMACCopyRst_t;
TYPE_SIZE_CHECK(DMACCopyRst_t, SIZE_8B);

//********************************************************
//				DMAC	SetValue
//********************************************************
typedef struct DMACSetValueCmd {
	struct {
		U32 ubOP		: 5;	// [4:0]
		U32 			: 3;	// [7:5]
		U32	ubEntrySize	: 2;	// [9:8]
		U32 btRndEn		: 1;	// [10]
		U32 btE2EGenEn	: 1;	// [11]
		U32	btMskEn		: 1;	// [12]
		U32 btDepend	: 1;	// [13]
		U32				: 1;	// [14]
		U32	btSetCQ		: 1;	// [15]
		U32	ubTagID		: 8;	// [23:16]
		U32 			: 8;	// [31:24]
	};

	struct {
		U32 ulValue1;			// [64:32]
	};

	struct {
		U32	ulTADDR; // Need 32 byte align, hardware don't care bit[4:0].
	};

	struct {
		U32 ulValue0;			// [64:32]
	};

	struct {
		U32 ulMsk;
	};

	struct {
		U32 ulLen				: 24;	// [23:0]
		U32						: 8;	// [31:24]
	};

	struct {
		U32 ulLCA;						//E17_porting_4TB
	};

	struct {
		U32 					: 1;	// [0]
		U32	btValidateTarget	: 1;	// [1]
		U32 					: 11;	// [12:2]
		U32	btTargetFullFlag	: 1;	// [13]
		U32	btTargetZeroFlag	: 1;	// [14]
		U32	btTargetCmdEnd		: 1;	// [15]
		U32						: 2;	// [17:16]
		U32	ubTargetOPER		: 2;	// [19:18]
		U32 					: 12;	// [31:20]
	};
} DMACSetValueCmd_t;
TYPE_SIZE_CHECK(DMACSetValueCmd_t, SIZE_32B);

typedef struct DMACSetValueRst {
	struct {
		U8 btSTA						: 1;	// [0]
		U8 ubAXIReadErrSlave			: 2;	// [2:1]
		U8 ubAXIWriteErrSlave			: 2;	// [4:3]
		U8 btParityErrFail				: 1;	// [5]
		U8 btParityErrFixed				: 1;	// [6]
		U8 btE2EType					: 1;	// [7]
		U8 ubTagID						: 8;	// [15:8]
		U8 								: 3;	// [18:16]
		U8 btTargetValidateFail			: 1;	// [19]
		U8								: 4;	// [23:20]
		U8								: 8;	// [31:24]
	};

	union {
		struct {
			U32 AXIWriteErr4KOffset		: 12;	// [27:16]
			U32							: 20;	// [31:28]
		};
		struct {
			U32 uwLBOffset				: 16;	// [15:0]
			U32							: 16;	// [31:10]
		};
	};
} DMACSetValueRst_t;
TYPE_SIZE_CHECK(DMACSetValueRst_t, SIZE_8B);


//********************************************************
//				DMAC	XOR
//********************************************************
typedef struct DMACXORCmd {
	struct {
		U32 ubOP		: 5;	// [4:0]
		U32				: 3;	// [7:5]
		U32	ubOperator	: 2;	// [9:8]
		U32 btXORFast	: 1;	// [10]
		U32				: 1;	// [11]
		U32	btMskEn		: 1;	// [12]
		U32 btDepend	: 1;	// [13]
		U32				: 1;	// [14]
		U32	btSetCQ		: 1;	// [15]
		U32	ubTagID		: 8;	// [23:16]
		U32				: 8;	// [31:24]
	};

	struct {
		U32	ulSADDR0; // Need 32 byte align, hardware don't care bit[4:0].
	};

	struct {
		U32	ulTADDR; // Need 32 byte align, hardware don't care bit[4:0].
	};

	struct {
		U32	ulSADDR1; // Need 32 byte align, hardware don't care bit[4:0].
	};

	struct {
		U32 ulMsk;
	};

	struct {
		U32 ulLen		: 24;	// [23:0]
		U32				: 8;	// [31:24]
	};

	struct {
		U32 ulLCA;				// [31:0]
	};

	struct {
		U32	ulRsvDW7;
	};
} DMACXORCmd_t;
TYPE_SIZE_CHECK(DMACXORCmd_t, SIZE_32B);

typedef struct DMACXORRst {
	struct {
		U8 btDone				: 1;	// [0]
		U8 ubAXIReadErrSlave	: 2;	// [2:1]
		U8 ubAXIWriteErrSlave	: 2;	// [4:3]
		U8 btParityErrFail		: 1;	// [5]
		U8 btParityErrFixed		: 1;	// [6]
		U8						: 1;	// [7]
		U8 ubTagID				: 8;	// [15:8]
		U16						: 16;	// [31:16]
	};

	struct {
		U32 ulRsvDW1;
	};
} DMACXORRst_t;
TYPE_SIZE_CHECK(DMACXORRst_t, SIZE_8B);

typedef struct DMACE3DCmd {
	struct {
		U32 ubOP		: 5;	// [4:0]
		U32				: 2;	// [6:5]
		U32 btATOM		: 1;	// [7]
		U32	E3D_CHK_TYPE		: 1;	// [8]
		U32 				: 3;	// [11:9]
		U32	btMskEn		: 1;	// [12]
		U32 btDepend	: 1;	// [13]
		U32				: 1;	// [14]
		U32	btSetCQ		: 1;	// [15]
		U32	ubTagID		: 8;	// [23:16]
		U32				: 8;	// [31:24]
	};

	struct {
		U32 ulSADDR;
	};

	struct {
		U32 ulRsvDW2;
	};

	struct {
		U32 ulRsvDW3;
	};

	struct {
		U32 ulRsvDW4;
	};

	struct {
		U32 ulLen		: 24;	// [23:0]
		U32				: 8;	// [31:24]
	};

	struct {
		U32 ulLCA;
	};

	struct {
		U32 btUN_AES_EN	: 1;	// [0]
		U32 btUN_ZIP_EN	: 1;	// [1]
		U32 ZINFO			: 3;	// [4:2]
		U32 				: 3;	// [7:5]
		U32 E3D4K			: 24;	// [31:8]
	};
} DMACE3DCmd_t;
TYPE_SIZE_CHECK(DMACE3DCmd_t, SIZE_32B);


typedef struct {
	struct {
		U8 btStatus				: 1;	// [0]
		U8 ubAXIReadErrSlave	: 2;	// [2:1]
		U8 ubAXIWriteErrSlave	: 2;	// [4:3]
		U8 btParityErrFail		: 1;	// [5]
		U8 btParityErrFixed		: 1;	// [6]
		U8						: 1;	// [7]
		U8 ubTagID				: 8;	// [15:8]
		U16						: 8;	// [23:16]
		U16 E3D512ErrMP			: 8;	// [31:24]
	};

	struct {
		U32 ulResult;
	};
} DMACE3DRst_t;
TYPE_SIZE_CHECK(DMACE3DRst_t, SIZE_8B);
//********************************************************
//				DMAC	CRC32
//********************************************************
typedef struct {
	struct {
		U32 ubOP		: 5;	// [4:0]
		U32				: 2;	// [6:5]
		U32 btATOM		: 1;	// [7]
		U32	btCHKEn		: 1;	// [8]
		U32 btCalMode	: 1;	// [9]
		U32 btROMEn		: 1;	// [10]
		U32				: 1;	// [11]
		U32	btMskEn		: 1;	// [12]
		U32 btDepend	: 1;	// [13]
		U32				: 1;	// [14]
		U32	btSetCQ		: 1;	// [15]
		U32	ubTagID		: 8;	// [23:16]
		U32				: 8;	// [31:24]
	};

	struct {
		U32 ulSADDR;
	};

	struct {
		U32 ulRsvDW2;
	};

	struct {
		U32 ulSeed;
	};

	struct {
		U32 ulRsvDW4;
	};

	struct {
		U32 ulLen		: 24;	// [23:0]
		U32				: 8;	// [31:24]
	};

	struct {
		U32 ulRsvDW6;
	};

	struct {
		U32 ulRsvDW7;
	};
} DMACCRC32Cmd_t;
TYPE_SIZE_CHECK(DMACCRC32Cmd_t, SIZE_32B);

typedef struct {
	struct {
		U8 btStatus				: 1;	// [0]
		U8 ubAXIReadErrSlave	: 2;	// [2:1]
		U8 ubAXIWriteErrSlave	: 2;	// [4:3]
		U8 btParityErrFail		: 1;	// [5]
		U8 btParityErrFixed		: 1;	// [6]
		U8						: 1;	// [7]
		U8 ubTagID				: 8;	// [15:8]
		U16						: 16;	// [31:16]
	};

	struct {
		U32 ulResult;
	};
} DMACCRC32Rst_t;
TYPE_SIZE_CHECK(DMACCRC32Rst_t, SIZE_8B);

//********************************************************
//				DMAC	Sort
//********************************************************
typedef struct DMACSortCmd {
	struct {
		U32 ubOP		: 5;	// [4:0]
		U32				: 3;	// [7:5]
		U32	ubSortOP	: 3;	// [10:8]
		U32 btLast		: 1;	// [11]
		U32	btOrder		: 1;	// [12]
		U32 btDepend	: 1;	// [13]
		U32	btErrIns	: 1;	// [14]
		U32	btSetCQ		: 1;	// [15]
		U32	ubTagID		: 8;	// [23:16]
		U32 			: 8;	// [31:24]
	};

	struct {
		U32	ulSADDR; // Need 32 byte align, hardware don't care bit[4:0].
	};

	struct {
		U32	ulTADDR; // Need 32 byte align, hardware don't care bit[4:0].
	};

	struct {
		U32 ubEntrySize	: 2;	// [1:0]
		U32				: 6;	// [7:2]
		U32 ubKeyIdx	: 5;	// [12:8]
		U32				: 3;	// [15:13]
		U32	ubKeyBMask	: 4;	// [19:16]
		U32				: 12;	// [31:20]
	};

	struct {
		U32 ulRsvDW4;
	};

	struct {
		U32 ulLen		: 24;	// [23:0]
		U32				: 8;	// [31:24]
	};

	struct {
		U32 ulRsvDW6;
	};

	struct {
		U32 ulRsvDW7;
	};
} DMACSortCmd_t;
TYPE_SIZE_CHECK(DMACSortCmd_t, SIZE_32B);

typedef struct DMACSortRst {
	struct {
		U8 btSTA				: 1;	// [0]
		U8 ubAXIReadErrSlave	: 2;	// [2:1]
		U8 ubAXIWriteErrSlave	: 2;	// [4:3]
		U8 btParityErrFail		: 1;	// [5]
		U8 btParityErrFixed		: 1;	// [6]
		U8						: 1;	// [7]
		U8 ubTagID				: 8;	// [15:8]
		U16						: 16;	// [31:16]
	};

	struct {
		U32 ulRsvDW1;
	};
} DMACSortRst_t;
TYPE_SIZE_CHECK(DMACSortRst_t, SIZE_8B);

//********************************************************
//				DMAC	SMAX
//********************************************************
typedef struct DMACSMaxCmd {
	struct {
		U32 ubOP		: 5;	// [4:0]
		U32 			: 3;	// [7:5]
		U32	ubEntrySize	: 2;	// [9:8]
		U32 btSMaxSMin	: 1;	// [10]
		U32 btIgnoreEn	: 1;	// [11]
		U32				: 1;	// [12]
		U32 btDepend	: 1;	// [13]
		U32	btErrIns	: 1;	// [14]
		U32	btSetCQ		: 1;	// [15]
		U32	ubTagID		: 8;	// [23:16]
		U32 			: 8;	// [31:24]
	};

	struct {
		U32	ulSADDR; // Need 32 byte align, hardware don't care bit[4:0].
	};

	struct {
		U32	ulTADDR; // Need 32 byte align, hardware don't care bit[4:0].
	};

	struct {
		U32 ulSearchValueMask;	// [64:32]
	};

	struct {
		U32 ulMaskValueMask;
	};

	struct {
		U32 ulLen		: 24;	// [23:0]
		U32				: 8;	// [31:24]
	};

	struct {
		U32 ulIgnoreValue;		// [31:0]
	};

	struct {
		U32 ulMatchValue;		// [31:0]
	};
} DMACSMaxCmd_t;
TYPE_SIZE_CHECK(DMACSMaxCmd_t, SIZE_32B);

typedef struct DMACSMaxRst {
	struct {
		U8 btSTA				: 1;	// [0]
		U8 ubAXIReadErrSlave	: 2;	// [2:1]
		U8 ubAXIWriteErrSlave	: 2;	// [4:3]
		U8 btParityErrFail		: 1;	// [5]
		U8 btParityErrFixed		: 1;	// [6]
		U8						: 1;	// [7]
		U8 ubTagID				: 8;	// [15:8]
		U16						: 16;	// [31:16]
	};

	struct {
		U32 ulRsvDW1;
	};
} DMACSMaxRst_t;
TYPE_SIZE_CHECK(DMACSMaxRst_t, SIZE_8B);

//********************************************************
//				DMAC	SBIT
//********************************************************
typedef struct DMACSBitCmd {
	struct {
		U32 ubOP		: 5;	// [4:0]
		U32 			: 3;	// [7:5]
		U32	btFindOne	: 1;	// [8]
		U32				: 4;	// [12:9]
		U32 btDepend	: 1;	// [13]
		U32	btErrIns	: 1;	// [14]
		U32	btSetCQ		: 1;	// [15]
		U32	ubTagID		: 8;	// [23:16]
		U32 			: 8;	// [31:24]
	};

	struct {
		U32	ulSADDR; // Need 32 byte align, hardware don't care bit[4:0].
	};

	struct {
		U32	ulRsvDW2;
	};

	struct {
		U32 ulRsvDW3;
	};

	struct {
		U32 ulRsvDW4;
	};

	struct {
		U32 ulLen		: 24;	// [23:0]
		U32				: 8;	// [31:24]
	};

	struct {
		U32 ulRsvDW6;		// [31:0]
	};

	struct {
		U32 ulRsvDW7;		// [31:0]
	};
} DMACSBitCmd_t;
TYPE_SIZE_CHECK(DMACSBitCmd_t, SIZE_32B);

typedef struct DMACSBitRst {
	struct {
		U8 btHit				: 1;	// [0]
		U8 ubAXIReadErrSlave	: 2;	// [2:1]
		U8 ubAXIWriteErrSlave	: 2;	// [4:3]
		U8 btParityErrFail		: 1;	// [5]
		U8 btParityErrFixed		: 1;	// [6]
		U8						: 1;	// [7]
		U8 ubTagID				: 8;	// [15:8]
		U16						: 16;	// [31:16]
	};

	struct {
		U32 ulFisrtBitOffset;           //Unit Bit
	};
} DMACSBitRst_t;
TYPE_SIZE_CHECK(DMACSBitRst_t, SIZE_8B);

//********************************************************
//				DMAC	Search
//********************************************************
typedef struct DMACSearchCmd {
	struct {
		U32 ubOP			: 5;	// [4:0]
		U32 				: 2;	// [6:5]
		U32	btFindOneOnly	: 1;	// [7]
		U32	ubEntrySize		: 2;	// [9:8]
		U32 ubSearchMode	: 2;	// [11:10]
		U32	btFindLastOne	: 1;	// [12]
		U32 btDepend		: 1;	// [13]
		U32	btErrIns		: 1;	// [14]
		U32	btSetCQ			: 1;	// [15]
		U32	ubTagID			: 8;	// [23:16]
		U32 				: 8;	// [31:24]
	};

	struct {
		U32	ulSADDR; // Need 32 byte align, hardware don't care bit[4:0].
	};

	struct {
		U32	ulTADDR; // Need 32 byte align, hardware don't care bit[4:0].
	};

	struct {
		U32 ulMaskedValue1;			// [64:32]
	};

	struct {
		U32 ulMaskedValue2;
	};

	struct {
		U32 ulLen			: 24;	// [23:0]
		U32					: 8;	// [31:24]
	};

	struct {
		U32 ulRefValue1;			// [31:0]
	};

	struct {
		U32 ulRefValue2;			// [31:0]
	};
} DMACSearchCmd_t;
TYPE_SIZE_CHECK(DMACSearchCmd_t, SIZE_32B);

typedef struct DMACSearchRst {
	struct {
		U8 btHit				: 1;	// [0]
		U8 ubAXIReadErrSlave	: 2;	// [2:1]
		U8 ubAXIWriteErrSlave	: 2;	// [4:3]
		U8 btParityErrFail		: 1;	// [5]
		U8 btParityErrFixed		: 1;	// [6]
		U8 btSearchType			: 1;	// [7]
		U8 ubTagID				: 8;	// [15:8]
		U16						: 16;	// [31:16]
	};

	struct {
		U16 uwMatchNum;			// [15:0]
		U16	uwResultOffset;		// [31:16]
	};
} DMACSearchRst_t;
TYPE_SIZE_CHECK(DMACSearchRst_t, SIZE_8B);


//********************************************************
//				DMAC	Scan Zcode
//********************************************************
typedef struct DMACScanZcodeCmd {
	struct {
		U32 ubOP				: 5;	// [4:0]
		U32 					: 3;	// [7:5]
		U32 btScanType			: 1;	// [8]
		U32 ZInfoMsk			: 3;	// [11:9]
		U32	btZInfoMskEn		: 1;	// [12]
		U32 btDepend			: 1;	// [13]
		U32	btErrIns			: 1;	// [14]
		U32	btSetCQ				: 1;	// [15]
		U32	ubTagID				: 8;	// [23:16]
		U32 					: 8;	// [31:24]
	};

	struct {
		U32	ulSADDR; // Need 32 byte align, hardware don't care bit[4:0].
	};

	struct {
		U32	ulTADDR; // Need 32 byte align, hardware don't care bit[4:0].
	};

	struct {
		U32 uwSearchVB0			: 13;	// [12:0]
		U32	uwSearchVB1			: 13;	// [25:13]
		U32 ubTargetTableLen0	: 6;	// [31:26]
	};

	struct {
		U32 uwSearchVB2			: 13;	// [12:0]
		U32	uwSearchVB3			: 13;	// [25:13]
		U32 ubTargetTableLen1	: 6;	// [31:26]
	};

	struct {
		U32 ulLen				: 24;	// [23:0]
		U32						: 8;	// [31:24]
	};

	struct {
		U32 uwSearchVB4			: 13;	// [12:0]
		U32	uwSearchVB5			: 13;	// [25:13]
		U32 ubTargetTableLen2	: 6;	// [31:26]
	};

	struct {
		U32 uwSearchVB6			: 13;	// [12:0]
		U32	uwSearchVB7			: 13;	// [25:13]
		U32 ubTargetTableLen3	: 6;	// [31:26]
	};
} DMACScanZcodeCmd_t;
TYPE_SIZE_CHECK(DMACScanZcodeCmd_t, SIZE_32B);

typedef struct DMACScanZcodeRst {
	struct {
		U8 btSTA				: 1;	// [0]
		U8 ubAXIReadErrSlave	: 2;	// [2:1]
		U8 ubAXIWriteErrSlave	: 2;	// [4:3]
		U8 btParityErrFail		: 1;	// [5]
		U8 btParityErrFixed		: 1;	// [6]
		U8						: 1;	// [7]
		U8 ubTagID				: 8;	// [15:8]
		U16						: 16;	// [31:16]
	};

	struct {
		U32 ulRsvDW1;
	};
} DMACScanZcodeRst_t;
TYPE_SIZE_CHECK(DMACScanZcodeRst_t, SIZE_8B);


//********************************************************
//				DMAC	Scan Vld
//********************************************************
typedef struct DMACScanVldCmd {
	struct {
		U32 ubOP		: 5;	// [4:0]
		U32 			: 2;	// [6:5]
		U32 btAtom		: 1;	// [7]
		U32 			: 2;	// [9:8]
		U32 btScanType	: 1;	// [10]
		U32				: 2;	// [12:11]
		U32 btDepend	: 1;	// [13]
		U32	btErrIns	: 1;	// [14]
		U32	btSetCQ		: 1;	// [15]
		U32	ubTagID		: 8;	// [23:16]
		U32 ubStartIdx0	: 8;	// [31:24]
	};

	struct {
		U32	ubStartIdx1	: 5;	// [4:0]
		U32	ulSADDR		: 27;	// [31:5] // Need 32 byte align, hardware don't care bit[4:0].
	};

	struct {
		U32	ubStartIdx2	: 3;	// [2:0]
		U32				: 2;	// [4:3]
		U32	ulTADDR		: 27;	// [31:5] // Need 32 byte align, hardware don't care bit[4:0].
	};

	struct {
		U32 uwSearchVB0	: 13;	// [12:0]
		U32	uwSearchVB1	: 13;	// [25:13]
		U32 ubStartPTE0	: 6;	// [31:26]
	};

	struct {
		U32 uwSearchVB2	: 13;	// [12:0]
		U32	uwSearchVB3	: 13;	// [25:13]
		U32 ubStartPTE1 : 6;	// [31:26]
	};

	struct {
		U32 uwTarLen	: 16;	// [15:0]
		U32	uwSrcLen	: 16;	// [31:16]
	};

	struct {
		U32 uwSearchVB4	: 13;	// [12:0]
		U32	uwSearchVB5	: 13;	// [25:13]
		U32 ubStartPTE2	: 6;	// [31:26]
	};

	struct {
		U32 uwSearchVB6	: 13;	// [12:0]
		U32	uwSearchVB7	: 13;	// [25:13]
		U32 ubStartPTE3	: 4;	// [29:26]
		U32 			: 2;	// [31:30]
	};
} DMACScanVldCmd_t;
TYPE_SIZE_CHECK(DMACScanVldCmd_t, SIZE_32B);

typedef struct DMACScanVldRst {
	struct {
		U8 btSTA				: 1;	// [0]
		U8 ubAXIReadErrSlave	: 2;	// [2:1]
		U8 ubAXIWriteErrSlave	: 2;	// [4:3]
		U8 btParityErrFail		: 1;	// [5]
		U8 btParityErrFixed		: 1;	// [6]
		U8						: 1;	// [7]
		U8 ubTagID;						// [15:8]
		U16	uwSrcStopIndex;				// [31:16]
	};

	struct {
		U32	ulVldCnt			: 17;	// [16:0]
		U32						: 3;	// [19:17]
		U32	btSrcStopIndexOV	: 1;	// [20]
		U32						: 11;	// [31:21]
	};
} DMACScanVldRst_t;
TYPE_SIZE_CHECK(DMACScanVldRst_t, SIZE_8B);
//********************************************************
//				DMAC	Compare
//********************************************************
typedef struct DMACCompareCmd {
	struct {
		U32 ubOP				: 5;	// [4:0]
		U32 ubXZIPEn			: 2;	// [6:5]
		U32 					: 1;	// [7]
		U32 ubCmpFast			: 4;	// [11:8]
		U32	btMaskEn			: 1;	// [12]
		U32 btDepend			: 1;	// [13]
		U32	btErrIns			: 1;	// [14]
		U32	btSetCQ				: 1;	// [15]
		U32	ubTagID				: 8;	// [23:16]
		U32 					: 8;	// [31:24]
	};

	struct {
		U32	ulSADDR0; // Need 32 byte align, hardware don't care bit[4:0].
	};

	struct {
		U32	ulReserve1;
	};

	struct {
		U32	ulSADDR1; // Need 32 byte align, hardware don't care bit[4:0].
	};

	struct {
		U32 ulMask;
	};

	struct {
		U32 ulLen				: 24;	// [23:0]
		U32						: 8;	// [31:24]
	};

	struct {
		U32	ulReserve2;
	};

	struct {
		U32	ulReserve3;
	};
} DMACCompareCmd_t;
TYPE_SIZE_CHECK(DMACCompareCmd_t, SIZE_32B);

typedef struct DMACCompareRst {
	struct {
		U8 btSTA				: 1;	// [0]
		U8 ubAXIReadErrSlave	: 2;	// [2:1]
		U8 ubAXIWriteErrSlave	: 2;	// [4:3]
		U8 btParityErrFail		: 1;	// [5]
		U8 btParityErrFixed		: 1;	// [6]
		U8						: 1;	// [7]
		U8 ubTagID				: 8;	// [15:8]
		U16 btDataCmpErr		: 1;	// [16]      //data cmp err
		U16						: 15;	// [31:17]
	};

	struct {
		U32 ulErrOffset;
	};
} DMACCompareRst_t;
//********************************************************
//				DMAC	Trim Merge
//********************************************************
typedef struct DMACTrimMergeCmd {
	struct {
		U32 ubOP					: 5;	// [4:0]
		U32 						: 2;	// [6:5]
		U32 btAtom					: 1;	// [7]
		U32 btFormat				: 1;	// [8]	SATA-format:0	NVMe-Format:1
		U32 btMode					: 1;	// [9]	512B-mode:0	4KB-mode:1    (only for NVMe format)
		U32 btWriteLockErrorCheckEn	: 1;	// [10]
		U32 btLBA2LCAEn				: 1;	// [11]
		U32 						: 1;	// [12]
		U32 btDepend				: 1;	// [13]	No:0		Yes:1
		U32 btErrIns				: 1;	// [14]
		U32 btSetCQ					: 1;	// [15]
		U32 ubTagID					: 8;	// [23:16]
		U32							: 8;	// [31:24]
	};


	struct {
		U32	ulSAddr; //32B/512B alignment
	};

	struct {
		U32	ulTAddr; //32B/512B alignment
	};

	struct {
		U32				: 32;	// [31:0]
	};

	struct {
		U32				: 32;	// [31:0]
	};

	struct {
		U32 ulTransferByteCount	: 24;	// [23:0]
		U32						:  8;	// [31:24]
	};

	struct {
		//E17_porting_4TB
		U32	ubNSID		: 5;	// [4:0]
		U32 			: 27;	// [31:5]
	};

	struct {
		U32				: 32;
	};
} DMACTrimMergeCmd_t;
TYPE_SIZE_CHECK(DMACTrimMergeCmd_t, SIZE_32B);

typedef struct DMACTrimMergeRst {
	struct {
		U8 btSTA				: 1;	// [0]	Done:0	WLErr:1
		U8 ubAXIReadErrSlave	: 2;	// [2:1]
		U8 ubAXIWriteErrSlave	: 2;	// [4:3]
		U8 btParityErrFail		: 1;	// [5]
		U8 btParityErrFixed		: 1;	// [6]
		U8						: 1;	// [7]
		U8 ubTagID;						// [15:8]
		U16						: 16;	// [31:16]
	};

	struct {
		U32	ulMergeValidNum		: 17;	// [16:0]
		U32						: 15;	// [31:17]
	};
} DMACTrimMergeRst_t;
TYPE_SIZE_CHECK(DMACTrimMergeRst_t, SIZE_8B);

//********************************************************
//				DMAC	Dummy
//********************************************************
typedef struct DMACDmyCmd {
	struct {
		U32 ubOP		: 5;	// [4:0]
		U32 			: 3;	// [7:5]
		U32 			: 7;	// [14:8]
		U32	btSetCQ		: 1;	// [15]
		U32	ubTagID		: 8;	// [23:16]
		U32 			: 8;	// [31:24]
	};
	U32 ulDW1;
	U32 ulDW2;
	U32 ulDW3;
	U32 ulDW4;
	struct {
		U32 ulLen				: 24;	// [23:0]
		U32						: 8;	// [31:24]
	};
	U32 ulDW6;
	U32 ulDW7;
} DMACDmyCmd_t;
TYPE_SIZE_CHECK(DMACDmyCmd_t, SIZE_32B);

//********************************************************
//				DMAC	Seq PTE
//********************************************************
typedef struct DMACSeqPTECmd {
	struct {
		U32 ubOP		: 5;	// [4:0]
		U32 			: 2;	// [6:5]
		U32 btAtom		: 1;	// [7]
		U32 ubZInfo		: 3;	// [10:8]
		U32				: 2;	// [12:11]
		U32 btDepend	: 1;	// [13]
		U32	btErrIns	: 1;	// [14]
		U32	btSetCQ		: 1;	// [15]
		U32	ubTagID		: 8;	// [23:16]
		U32 			: 8;	// [31:24]
	};

	struct {
		U32	ulStartPCA;	// [31:0]
	};

	U32	ulTADDR;	// [31:5] // Need 32 byte align, hardware don't care bit[4:0].

	union {
		U32 ulAll;
		struct {
			U32 uwSkipPCAOfs0	: 13;	// [12:0]
			U32	btSkipPCAVld0	: 1;	// [13]
			U32					: 2;	// [15:14]
			U32 uwSkipPCAOfs1	: 13;	// [28:16]
			U32	btSkipPCAVld1	: 1;	// [13]
			U32					: 2;	// [15:14]
		};
	} SkipPCA01;

	union {
		U32 ulAll;
		struct {
			U32 uwSkipPCAOfs2	: 13;	// [12:0]
			U32	btSkipPCAVld2	: 1;	// [13]
			U32					: 2;	// [15:14]
			U32 uwSkipPCAOfs3	: 13;	// [28:16]
			U32	btSkipPCAVld3	: 1;	// [13]
			U32					: 2;	// [15:14]
		};
	} SkipPCA23;

	struct {
		U32 ulLen		: 24;	// [23:0]
		U32				: 8;	// [31:24]
	};

	struct {
		U32 ulLCA;			//E17_porting_4TB
	};

	struct {
		U32 ubSkipPCASize	: 8;	// [7:0]
		U32					: 24;	// [31:8]
	};
} DMACSeqPTECmd_t;
TYPE_SIZE_CHECK(DMACSeqPTECmd_t, SIZE_32B);

typedef struct DMACSeqPTERst {
	struct {
		U32 btSTA				: 1;	// [0]
		U32 ubAXIReadErrSlave	: 2;	// [2:1]
		U32 ubAXIWriteErrSlave	: 2;	// [4:3]
		U32 btParityErrFail		: 1;	// [5]
		U32 btParityErrFixed	: 1;	// [6]
		U32						: 1;	// [7]
		U32 ubTagID				: 8;	// [15:8]
		U32						: 16;	// [31:16]
	};

	struct {
		U32	ulRsv;				// [31:0]
	};
} DMACSeqPTERst_t;
TYPE_SIZE_CHECK(DMACSeqPTERst_t, SIZE_8B);

//********************************************************
//				DMAC	Cnt PTE
//********************************************************
typedef struct DMACCntPTECmd {
	struct {
		U32 OP		: 5;	// [4:0]
		U32 Reserve0		: 2;	// [6:5]
		U32 btAtom		: 1;	// [7]
		U32 PTESize		: 2;	// [9:8]
		U32 PMDSize		: 2;	// [11:10]
		U32 Reserve1		: 1;	// [12]
		U32 btDepend		: 1;	// [13]
		U32 btErrIns		: 1;	// [14]
		U32 btSetCQ		: 1;	// [15]
		U32 ubTagID		: 8;	// [23:16]
		U32 Reserve2		: 8;	// [31:24]
	};

	struct {
		U32 ulSADDR;			// [31:0] // Need 32 byte align, hardware don't care bit[4:0]
	};

	struct {
		U32 Reserve3;		// [31:0]
	};

	struct {
		U32 HeadSkipNum	: 2;	// [1:0] // DSA Start Index--> # of non-32 byte-align Nodes
		U32 Reserve4		: 30;	// [31:2]
	};

	struct {
		U32 PTEMsk;			// [31:0]
	};

	struct {
		U32 TransferByteCnt	: 24;	// [23:0]
		U32 Reserve5		: 8;	// [31:24]
	};

	struct {
		U32 Reserve6;		// [31:0]
	};

	struct {
		U32 Reserve7;		// [31:0]
	};
} DMACCntPTECmd_t;
TYPE_SIZE_CHECK(DMACCntPTECmd_t, SIZE_32B);

typedef struct DMACCntPTERst {
	struct {
		U32 btSTA			: 1;	// [0]
		U32 AXIReadErrSlave		: 2;	// [2:1]
		U32 AXIWriteErrSlave	: 2;	// [4:3]
		U32 btParityErrFail		: 1;	// [5]
		U32 btParityErrFixed		: 1;	// [6]
		U32 Reserve0			: 1;	// [7]
		U32 ubTagID			: 8;	// [15:8]
		U32 Reserve1			: 16;	// [31:16]
	};

	struct {
		U32 uwPTECnt		: 16;	// [15:0]
		U32 uwPMDCnt		: 16;	// [31:16]
	};
} DMACCntPTERst_t;
TYPE_SIZE_CHECK(DMACCntPTERst_t, SIZE_8B);

#endif /* _S17_DMAC_CMD_TYPE_H_ */
