/**************************************************************************/
/*                                                                        */
/*           Copyright (c) 2000-2017 by Phison Electronics Corp.          */
/*                                                                        */
/**************************************************************************/

/**************************************************************************/
/*                                                                        */
/*  FILE : shr_hal_debug.h               PROJECT : PS5011                     */
/*                                                                        */
/*                                                                        */
/*  DESCRIPTION                                                           */
/*                                                                        */
/*    This is the header file for type definition                         */
/*                                                                        */
/*  RELEASE HISTORY                                                       */
/*                                                                        */
/*    DATE              NAME                DESCRIPTION                   */
/*                                                                        */
/*  2017-03-29     Eddie Chiang             Initial Version 1.0           */
/*  2017-05-03     Boyu Chen 	            Ver. 1.1 Coding Style         */
/*                                                                        */
/**************************************************************************/


#ifndef _SHR_HAL_DEBUG_H_
#define _SHR_HAL_DEBUG_H_
#include "debug/debug.h"
#include "env.h"

#if !VS_SIM_EN
#define LIB_SIZE_CHECK(TYPE, SIZE_IN_BYTE)	STATIC_ASSERT(sizeof(TYPE)==(SIZE_IN_BYTE), #TYPE " Lib Size Error!")
#else
#define LIB_SIZE_CHECK(TYPE, SIZE_IN_BYTE)
#endif

#endif  // _SHR_HAL_PIC_UART_H_

