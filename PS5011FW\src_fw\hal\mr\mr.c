#include "common/typedef.h"
#include "common/symbol.h"
#include "mr_api.h"
#include "mr.h"
#include "common/math_op.h"

U8 gubMRLogMessageSlaveQueueDummyCnt;

U16 MrGetLinkValidCnt(void)
{
	U8 ubLinkIdx = 0;
	U16 uwMailRouterLinkValidCnt = 0;
	for (ubLinkIdx = 0; ubLinkIdx < MR_LINK_VALID_FLAG_REGISTER_NUM; ubLinkIdx++) {
		U32 ulTempValue = 0;
		M_MR_GET_LINK_VALID_FLAG(ubLinkIdx, ulTempValue);
		uwMailRouterLinkValidCnt += CntingBits(ulTempValue);
	}
	return uwMailRouterLinkValidCnt;
}

U8 MRCheckCOP0SlaveIdle(void)
{
	return	((SQR_EMT_BIT == M_MR_GET_SQR_EMT(MR_COP0_CMD_READ_SLAVE_QUEUE_ID)) &&  //cop0r SQ to cop0
			(SQR_EMT_BIT == M_MR_GET_SQR_EMT(MR_COP0_CMD_WRITE_SLAVE_QUEUE_ID)) &&     //cop0w SQ to cop0
			(SQR_EMT_BIT == M_MR_GET_SQR_EMT(MR_COP1_RESPOND_TO_COP0_SLAVE_QUEUE_ID)) &&     //cop1 rsp to cop0
			(SQR_EMT_BIT == M_MR_GET_SQR_EMT(MR_BMU_RESPOND_TO_COP0_SLAVE_QUEUE_ID)) &&     //bmu nblk to cop0
			(SQR_EMT_BIT == M_MR_GET_SQR_EMT(MR_COP0_CMD_WRITE_2_SLAVE_QUEUE_ID)) &&     //cop0w2 SQ to cop0
			(SQR_EMT_BIT == M_MR_GET_SQR_EMT(MR_DMAC_RESPOND_TO_MERG_SLAVE_QUEUE_ID)) &&     //dmac rsp to merg
			(SQR_EMT_BIT == M_MR_GET_SQR_EMT(MR_BMU_RESPOND_TO_MERG_SLAVE_QUEUE_ID)) &&     //bmu all rsp to merg
			(SQR_EMT_BIT == M_MR_GET_SQR_EMT(MR_BMU_NON_BLOCK_RESPOND_TO_FIP_SLAVE_QUEUE_ID)) &&     //bmu nblk rsp to fip
			(SQR_EMT_BIT == M_MR_GET_SQR_EMT(MR_BMU_BLOCK_RESPOND_TO_FIP_SLAVE_QUEUE_ID)) &&     //bmu blk rsp to fip
			(SQR_EMT_BIT == M_MR_GET_SQR_EMT(MR_BMU_RESPOND_TO_RS_SLAVE_QUEUE_ID)))	;
}

U8 MRCheckDMACSlaveIdle(void)
{
	return	((SQR_EMT_BIT == M_MR_GET_SQR_EMT(MR_DMAC_HIGH_PRIORITY_SLAVE_QUEUE_ID)) &&
			(SQR_EMT_BIT == M_MR_GET_SQR_EMT(MR_DMAC_NORMAL_PRIORITY_SLAVE_QUEUE_ID)) &&
			(SQR_EMT_BIT == M_MR_GET_SQR_EMT(MR_BMU_RESPOND_TO_DMAC_SLAVE_QUEUE_ID)) &&
			(SQR_EMT_BIT == M_MR_GET_SQR_EMT(MR_AES_RESPOND_TO_DMAC_SLAVE_QUEUE_ID)));
}

U8 MRCheckDMACDecoderIdle(void)
{
	return (((R8_MR[R8_MR_DEC_N_INFO + MR_DMAC_HIGH_PRIORITY_SQ_DECODER_NUM] & MR_DECODER_EVEN_IDLE_BIT_MASK) == MR_DECODER_EVEN_STATUS_IDLE) &&
			((R8_MR[R8_MR_DEC_N_INFO + MR_DMAC_NORMAL_PRIORITY_SQ_DECODER_NUM] & MR_DECODER_ODD_IDLE_BIT_MASK) == MR_DECODER_ODD_STATUS_IDLE) &&
			((R8_MR[R8_MR_DEC_N_INFO + MR_AES_SEND_TO_OTHERS_DECODER_NUM] & MR_DECODER_EVEN_IDLE_BIT_MASK) == MR_DECODER_EVEN_STATUS_IDLE) &&
			((R8_MR[R8_MR_DEC_N_INFO + MR_COP1_SEND_TO_OTHERS_DECODER_NUM] & MR_DECODER_ODD_IDLE_BIT_MASK) == MR_DECODER_ODD_STATUS_IDLE) &&
			((R8_MR[R8_MR_DEC_N_INFO + MR_COP0_SEND_TO_COP1_DECODER_NUM] & MR_DECODER_EVEN_IDLE_BIT_MASK) == MR_DECODER_EVEN_STATUS_IDLE) &&
			((R8_MR[R8_MR_DEC_N_INFO + MR_BMU_SEND_TO_COP1_DECODER_NUM] & MR_DECODER_EVEN_IDLE_BIT_MASK) == MR_DECODER_EVEN_STATUS_IDLE));

}
U8 MRCheckCOP0DecoderIdle(void)
{
	return (((R8_MR[R8_MR_DEC_N_INFO + MR_COP0_READ_SQ_DECODER_NUM] & MR_DECODER_ODD_IDLE_BIT_MASK) == MR_DECODER_ODD_STATUS_IDLE) &&
			((R8_MR[R8_MR_DEC_N_INFO + MR_COP0_WRITE_SQ_DECODER_NUM] & MR_DECODER_EVEN_IDLE_BIT_MASK) == MR_DECODER_EVEN_STATUS_IDLE) &&
			((R8_MR[R8_MR_DEC_N_INFO + MR_COP0_WRITE_1_SQ_DECODER_NUM] & MR_DECODER_ODD_IDLE_BIT_MASK) == MR_DECODER_ODD_STATUS_IDLE) &&
			((R8_MR[R8_MR_DEC_N_INFO + MR_COP1_SEND_TO_OTHERS_DECODER_NUM] & MR_DECODER_ODD_IDLE_BIT_MASK) == MR_DECODER_ODD_STATUS_IDLE) &&
			((R8_MR[R8_MR_DEC_N_INFO + MR_DMAC_SEND_TO_COP1_DECODER_NUM] & MR_DECODER_ODD_IDLE_BIT_MASK) == MR_DECODER_ODD_STATUS_IDLE) &&
			((R8_MR[R8_MR_DEC_N_INFO + MR_BMU_SEND_TO_COP1_DECODER_NUM] & MR_DECODER_EVEN_IDLE_BIT_MASK) == MR_DECODER_EVEN_STATUS_IDLE));
}

void MRLockCOP1Decoder(void)
{
	/*decode stop the command into slave queue*/
	R32_MR[R32_MR_DECODE_STOP] |= (MR_DECODER_STOP_COP1_SQ_BIT_NUM |
			MR_DECODER_STOP_APU_SEND_TO_COP1_BIT_NUM |
			MR_DECODER_STOP_COP0_SEND_TO_COP1_BIT_NUM |
			MR_DECODER_STOP_DMAC_SEND_TO_COP1_BIT_NUM |
			MR_DECODER_STOP_BMU_SEND_TO_COP1_BIT_NUM);

}

void MRUnLockCOP1Decoder(void)
{
	/*decode stop release*/
	R32_MR[R32_MR_DECODE_STOP] &= ~(MR_DECODER_STOP_COP1_SQ_BIT_NUM |
			MR_DECODER_STOP_APU_SEND_TO_COP1_BIT_NUM |
			MR_DECODER_STOP_COP0_SEND_TO_COP1_BIT_NUM |
			MR_DECODER_STOP_DMAC_SEND_TO_COP1_BIT_NUM |
			MR_DECODER_STOP_BMU_SEND_TO_COP1_BIT_NUM);
}

#if (BMU_BUG_WORKAROUND_EN)
U8 MRCheckAllSQIdle(void)
{
	U8 ubSQIdx = 0;
	U8 ubResult = TRUE;
	for (ubSQIdx = 0; ubSQIdx < MR_TOTAL_USED_SQ_NUM; ubSQIdx++) {
		if (MR_LOG_MESSAGE_SLAVE_QUEUE_ID == ubSQIdx) {
			continue;
		}
		if (SQR_EMT_BIT != M_MR_GET_SQR_EMT(ubSQIdx)) {
			ubResult = FALSE;
			break;
		}
	}
	return ubResult;
}
#endif/*(BMU_BUG_WORKAROUND_EN)*/
