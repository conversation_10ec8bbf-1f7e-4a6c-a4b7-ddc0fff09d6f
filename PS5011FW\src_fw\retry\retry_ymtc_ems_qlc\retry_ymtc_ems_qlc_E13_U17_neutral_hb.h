#ifndef _RETRY_YMYC_EMS_QLC_E13_NEUTRAL_HB_H_
#define _RETRY_YMYC_EMS_QLC_E13_NEUTRAL_HB_H_
/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */

#include "env.h"

#if(((PS5013_EN) || (PS5017_EN)) && (FLASH_YMTC_EMS_QLC == FW_CATEGORY_FLASH) && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER))
/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */

#define HBIT_RETRY_YMTC_EMS_QLC_1024G_STEP_NUM			(66 + 1)//test
#define HBIT_RETRY_YMTC_EMS_SLC_1024G_STEP_NUM			(9 + 1)

/*
 * ---------------------------------------------------------------------------------------------------
 *  enum
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */

#endif /* ((PS5013_EN) && (FLASH_YMTC_EMS_QLC == FW_CATEGORY_FLASH) && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER)) */
#endif /* _RETRY_YMYC_EMS_QLC_E13_NEUTRAL_HB_H_ */
