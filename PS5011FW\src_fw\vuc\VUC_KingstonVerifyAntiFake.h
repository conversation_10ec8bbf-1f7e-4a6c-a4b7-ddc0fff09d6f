#ifndef _VUC_KINGSTONVERIFYANTIFAKE_H_
#define _VUC_KINGSTONVERIFYANTIFAKE_H_

#include "aom/aom_api.h"

#if (NVME == HOST_MODE)
AOM_VUC_KINGSTON U8 VUCKingstonVerifyAntiFakeChecksum(VUC_OPT_HCMD_PTR_t pCmd, U8 *ubDW12, U8 *ubDW13);
#endif /* (NVME == HOST_MODE) */
AOM_VUC_KINGSTON void VUCKingstonVerifyAntiFakeWrite(U32 ulVUCWriteBufAddr);
AOM_VUC_KINGSTON U8 VUCKingstonVerifyAntiFakeRead(U32 ulVUCReturnBufAddr);
AOM_VUC_KINGSTON void VUCReleaseKingston4KBuf(void);

#endif /* _VUC_KINGSTONVERIFYANTIFAKE_H_ */
