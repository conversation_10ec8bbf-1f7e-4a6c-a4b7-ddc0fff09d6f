#define _HAL_NSRMP_C_

#include "hal/apu/apu_api.h"
#include "nsrmp/hal_nsrmp.h"

void ApuLRSramInit(void)
{
	U32 ul_i;

	// initial ns pool
	for (ul_i = 0; ul_i < PMD_NUM; ul_i++) {
		HAL_APU_SET_NS_POOL_NODE(ul_i, ul_i);
	}

	// initial ns offset
	for (ul_i = 0; ul_i < NS_NUM; ul_i++) {
		HAL_APU_SET_NS_OFS_NODE(ul_i, 0);
	}

	// inital ns inverse table
	for (ul_i = 0; ul_i < PMD_NUM; ul_i++) {
		HAL_APU_SET_NS_INVERSE_NODE(ul_i, 0, ul_i);
	}

	return;
}

void hal_nsrmp_table_init(void)
{
	U32 ul_i;

	// initial ns pool
	for (ul_i = 0; ul_i < PMD_NUM; ul_i++) {
		HAL_APU_SET_NS_POOL_NODE(ul_i, ul_i);
	}

	return;
}
