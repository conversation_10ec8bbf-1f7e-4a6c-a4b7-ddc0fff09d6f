/** @file symbol.h
 *  @brief Static Symbols
 *
 *	Define a lot of static symbols, like BITs, MASKs, SIZEs, FLOOR and CEILING.
 *
 */
#ifndef _SYMBOL_H_
#define _SYMBOL_H_

//=============================================================================
#undef TRUE
#define TRUE				(1)
#undef FALSE
#define FALSE				(0)

#define ENABLE				(1)
#define DISABLE				(0)

#define PASS				(0)
#define SUCCESS				(0)
#define FAIL				(1)

#define HIGH				(1)
#define LOW					(0)

//=============================================================================
#define BIT(n)				(1UL << (n))
#define BIT64(n)			(1ULL << (n))
#define BIT_MASK(n)			((1UL << (n)) - 1)
#define BIT_MASK64(n)		(((n) == 64) ? 0xFFFFFFFFFFFFFFFF : (1ULL << (n)) - 1)

//=============================================================================
#define BIT0				BIT(0)
#define BIT1				BIT(1)
#define BIT2				BIT(2)
#define BIT3				BIT(3)
#define BIT4				BIT(4)
#define BIT5				BIT(5)
#define BIT6				BIT(6)
#define BIT7				BIT(7)
#define BIT8				BIT(8)
#define BIT9				BIT(9)
#define BIT10				BIT(10)
#define BIT11				BIT(11)
#define BIT12				BIT(12)
#define BIT13				BIT(13)
#define BIT14				BIT(14)
#define BIT15				BIT(15)
#define BIT16				BIT(16)
#define BIT17				BIT(17)
#define BIT18				BIT(18)
#define BIT19				BIT(19)
#define BIT20				BIT(20)
#define BIT21				BIT(21)
#define BIT22				BIT(22)
#define BIT23				BIT(23)
#define BIT24				BIT(24)
#define BIT25				BIT(25)
#define BIT26				BIT(26)
#define BIT27				BIT(27)
#define BIT28				BIT(28)
#define BIT29				BIT(29)
#define BIT30				BIT(30)
#define BIT31				BIT(31)
#define BIT32				BIT64(32)
#define BIT33				BIT64(33)
#define BIT34				BIT64(34)
#define BIT35				BIT64(35)
#define BIT36				BIT64(36)
#define BIT37				BIT64(37)
#define BIT38				BIT64(38)
#define BIT39				BIT64(39)
#define BIT40				BIT64(40)
#define BIT41				BIT64(41)
#define BIT42				BIT64(42)
#define BIT43				BIT64(43)
#define BIT44				BIT64(44)
#define BIT45				BIT64(45)
#define BIT46				BIT64(46)
#define BIT47				BIT64(47)
#define BIT48				BIT64(48)
#define BIT49				BIT64(49)
#define BIT50				BIT64(50)
#define BIT51				BIT64(51)
#define BIT52				BIT64(52)
#define BIT53				BIT64(53)
#define BIT54				BIT64(54)
#define BIT55				BIT64(55)
#define BIT56				BIT64(56)
#define BIT57				BIT64(57)
#define BIT58				BIT64(58)
#define BIT59				BIT64(69)
#define BIT60				BIT64(60)
#define BIT61				BIT64(61)
#define BIT62				BIT64(62)
#define BIT63				BIT64(63)

//=============================================================================
#define SET_BIT0			BIT0
#define SET_BIT1			BIT1
#define SET_BIT2			BIT2
#define SET_BIT3			BIT3
#define SET_BIT4			BIT4
#define SET_BIT5			BIT5
#define SET_BIT6			BIT6
#define SET_BIT7			BIT7
#define SET_BIT8			BIT8
#define SET_BIT9			BIT9
#define SET_BIT10			BIT10
#define SET_BIT11			BIT11
#define SET_BIT12			BIT12
#define SET_BIT13			BIT13
#define SET_BIT14			BIT14
#define SET_BIT15			BIT15
#define SET_BIT16			BIT16
#define SET_BIT17			BIT17
#define SET_BIT18			BIT18
#define SET_BIT19			BIT19
#define SET_BIT20			BIT20
#define SET_BIT21			BIT21
#define SET_BIT22			BIT22
#define SET_BIT23			BIT23
#define SET_BIT24			BIT24
#define SET_BIT25			BIT25
#define SET_BIT26			BIT26
#define SET_BIT27			BIT27
#define SET_BIT28			BIT28
#define SET_BIT29			BIT29
#define SET_BIT30			BIT30
#define SET_BIT31			BIT31
#define SET_BIT32			BIT32
#define SET_BIT33			BIT33
#define SET_BIT34			BIT34
#define SET_BIT35			BIT35
#define SET_BIT36			BIT36
#define SET_BIT37			BIT37
#define SET_BIT38			BIT38
#define SET_BIT39			BIT39
#define SET_BIT40			BIT40
#define SET_BIT41			BIT41
#define SET_BIT42			BIT42
#define SET_BIT43			BIT43
#define SET_BIT44			BIT44
#define SET_BIT45			BIT45
#define SET_BIT46			BIT46
#define SET_BIT47			BIT47
#define SET_BIT48			BIT48
#define SET_BIT49			BIT49
#define SET_BIT50			BIT50
#define SET_BIT51			BIT51
#define SET_BIT52			BIT52
#define SET_BIT53			BIT53
#define SET_BIT54			BIT54
#define SET_BIT55			BIT55
#define SET_BIT56			BIT56
#define SET_BIT57			BIT57
#define SET_BIT58			BIT58
#define SET_BIT59			BIT59
#define SET_BIT60			BIT60
#define SET_BIT61			BIT61
#define SET_BIT62			BIT62
#define SET_BIT63			BIT63

//=============================================================================
#define CLR_BIT0			(~BIT0)
#define CLR_BIT1			(~BIT1)
#define CLR_BIT2			(~BIT2)
#define CLR_BIT3			(~BIT3)
#define CLR_BIT4			(~BIT4)
#define CLR_BIT5			(~BIT5)
#define CLR_BIT6			(~BIT6)
#define CLR_BIT7			(~BIT7)
#define CLR_BIT8			(~BIT8)
#define CLR_BIT9			(~BIT9)
#define CLR_BIT10			(~BIT10)
#define CLR_BIT11			(~BIT11)
#define CLR_BIT12			(~BIT12)
#define CLR_BIT13			(~BIT13)
#define CLR_BIT14			(~BIT14)
#define CLR_BIT15			(~BIT15)
#define CLR_BIT16			(~BIT16)
#define CLR_BIT17			(~BIT17)
#define CLR_BIT18			(~BIT18)
#define CLR_BIT19			(~BIT19)
#define CLR_BIT20			(~BIT20)
#define CLR_BIT21			(~BIT21)
#define CLR_BIT22			(~BIT22)
#define CLR_BIT23			(~BIT23)
#define CLR_BIT24			(~BIT24)
#define CLR_BIT25			(~BIT25)
#define CLR_BIT26			(~BIT26)
#define CLR_BIT27			(~BIT27)
#define CLR_BIT28			(~BIT28)
#define CLR_BIT29			(~BIT29)
#define CLR_BIT30			(~BIT30)
#define CLR_BIT31			(~BIT31)
#define CLR_BIT32			(~BIT32)
#define CLR_BIT33			(~BIT33)
#define CLR_BIT34			(~BIT34)
#define CLR_BIT35			(~BIT35)
#define CLR_BIT36			(~BIT36)
#define CLR_BIT37			(~BIT37)
#define CLR_BIT38			(~BIT38)
#define CLR_BIT39			(~BIT39)
#define CLR_BIT40			(~BIT40)
#define CLR_BIT41			(~BIT41)
#define CLR_BIT42			(~BIT42)
#define CLR_BIT43			(~BIT43)
#define CLR_BIT44			(~BIT44)
#define CLR_BIT45			(~BIT45)
#define CLR_BIT46			(~BIT46)
#define CLR_BIT47			(~BIT47)
#define CLR_BIT48			(~BIT48)
#define CLR_BIT49			(~BIT49)
#define CLR_BIT50			(~BIT50)
#define CLR_BIT51			(~BIT51)
#define CLR_BIT52			(~BIT52)
#define CLR_BIT53			(~BIT53)
#define CLR_BIT54			(~BIT54)
#define CLR_BIT55			(~BIT55)
#define CLR_BIT56			(~BIT56)
#define CLR_BIT57			(~BIT57)
#define CLR_BIT58			(~BIT58)
#define CLR_BIT59			(~BIT59)
#define CLR_BIT60			(~BIT60)
#define CLR_BIT61			(~BIT61)
#define CLR_BIT62			(~BIT62)
#define CLR_BIT63			(~BIT63)

//=============================================================================

#define SIZE_1B				BIT(0)
#define SIZE_2B				BIT(1)
#define SIZE_4B				BIT(2)
#define SIZE_8B				BIT(3)
#define SIZE_12B			(SIZE_8B + SIZE_4B)
#define SIZE_16B			BIT(4)
#define SIZE_32B			BIT(5)
#define SIZE_64B			BIT(6)
#define SIZE_128B			BIT(7)
#define SIZE_256B			BIT(8)
#define SIZE_512B			BIT(9)
#define SIZE_1KB			BIT(10)
#define SIZE_2KB			BIT(11)
#define SIZE_4KB			BIT(12)
#define SIZE_6KB			(SIZE_4KB + SIZE_2KB) // U17
#define SIZE_8KB			BIT(13)
#define SIZE_12KB			(SIZE_8KB + SIZE_4KB)
#define SIZE_16KB			BIT(14)
#define SIZE_20KB			(SIZE_16KB + SIZE_4KB)
#define SIZE_32KB			BIT(15)
#define SIZE_64KB			BIT(16)
#define SIZE_128KB			BIT(17)
#define SIZE_256KB			BIT(18)
#define SIZE_512KB			BIT(19)
#define SIZE_1MB			BIT(20)
#define SIZE_2MB			BIT(21)
#define SIZE_4MB			BIT(22)
#define SIZE_8MB			BIT(23)
#define SIZE_16MB			BIT(24)
#define SIZE_32MB			BIT(25)
#define SIZE_64MB			BIT(26)
#define SIZE_128MB			BIT(27)
#define SIZE_256MB			BIT(28)
#define SIZE_512MB			BIT(29)
#define SIZE_1GB			BIT(30)
#define SIZE_1p5GB		(BIT(30) | BIT(29))
#define SIZE_2GB			BIT(31)
#define SIZE_4GB			BIT64(32)
#define SIZE_8GB			BIT64(33)
#define SIZE_16GB			BIT64(34)
#define SIZE_32GB			BIT64(35)
#define SIZE_64GB			BIT64(36)
#define SIZE_128GB			BIT64(37)

//=============================================================================
#define BITS_PER_BYTE 			(8)
#define BITS_PER_BYTE_MASK 		(7)
#define BITS_PER_BYTE_LOG 		(3)

#define BITS_PER_U64        (sizeof(U64) * BITS_PER_BYTE)
#define BITS_PER_U32		(sizeof(U32) * BITS_PER_BYTE)

//=============================================================================
// Firmware Host Category
#define HOST_MODE_NONE      (0)
#define NVME				(1)
#define SATA				(2)
#define USB				(3) // U17

// Firmware Controller Category
#define CONTROLLER_NONE					(10)
#define CONTROLLER_PS5013				(11)
#define CONTROLLER_PS5913				(12)
#define CONTROLLER_PS5017				(13)
#define CONTROLLER_PS5019				(14)
#define CONTROLLER_PS5021				(15)

// Build mode
#define BUILD_BURNER        (0)
#define BUILD_BOOT_LOADER	(1)
#define BUILD_FW			(2)
#define BUILD_LPM_LOADER	(3)

// Firmware Customer Category
#define CUSTOMER_NONE					(10)
#define CUSTOMER_MAINSTREAM				(11)
#define CUSTOMER_COSAIR					(12)
#define CUSTOMER_KINGSTON				(13)
#define CUSTOMER_ADVENTECH				(14)
#define CUSTOMER_POSTAR					(15)
#define CUSTOMER_CHINA					(16)
#define CUSTOMER_KINGSTON_HP			(17)
#define CUSTOMER_KINGSTON_DELL			(18)
#define CUSTOMER_HP						(19)
#define CUSTOMER_SOEM					(20)
#define CUSTOMER_MICRON_NICKS			(21)
#define CUSTOMER_SEAGATE				(22)

// Firmware Flash Category
#define FLASH_NONE						(10)
#define FLASH_BICS3TLC					(11)
#define FLASH_MICRONTLC					(12)
#define FLASH_BICS4QLC					(13)
#define FLASH_BICS4PSLC					(14)
#define FLASH_N18_QLC				    (15)
#define FLASH_BICS4TLCHDR				(16)
#define FLASH_N28_QLC				    (17)
#define FLASH_B27A_TLC				    (18)
#define FLASH_B47R_TLC				    (19)
#define FLASH_N48R_QLC				    (20) //zerio n48r add
#define FLASH_BICS5TLC   				(21)
#define FLASH_JGSTLC					(22)
#define FLASH_V6_TLC					(23)//dylan 2022/03/10 porting RDT from V6RDT
#define FLASH_V7_TLC					(24) //Duson Porting V7 RDT Add
#define FLASH_SANDISK_BICS5_TLC 		(27) //Duson Porting BICS5 RDT Add
#define FLASH_SANDISK_BICS6_TLC			(28) //BICS6 RDT Add
#define FLASH_YMTC_TAS_TLC		    	(29) //andry 20240927 Add
#define FLASH_V7_QLC					(30) //Reip Porting 3D-V7 QLC Add
#define FLASH_YMTC_EMS_QLC				(31) //ems mst add--karl
#define FLASH_V8_TLC					(32) //jeffrey Porting 3D V8 RDT Add
#define FLASH_SANDISK_BICS8_TLC			(33) //zerio BICS8 Add
#define FLASH_V5_TLC					(34) //jeffrey Porting 3D V5 RDT Add
#define FLASH_YMTC_WTS_TLC		     	(35) //zerio wts Add
#define FLASH_SAMSUNG_V6_TLC			(36) //samsung v6 add
#define FLASH_SAMSUNG_V6P_TLC			(37) //samsung v6p add
#define FLASH_SAMSUNG_V7_TLC			(38) //Samsung v7 mst add--Reip
#define FLASH_SANDISK_BICS6_QLC			(39) //zerio bics6 qlc add
#define FLASH_SAMSUNG_V8_TLC			(40) //Samsung v8 mst add--Reip
#define FLASH_SAMSUNG_V5_TLC			(41) //samsung v5 mst add--Jeffrey
#define FLASH_B37R_TLC				    (42)
#define FLASH_INTEL_N38A_QLC			(43) //Intel N38A add

// Firmware Build Version
#define FW_VERSION_FULL_MAX_XZIP_ON				(0)	// FW_INTERNALVERSION2: 0
#define FW_VERSION_FULL_MAX_XZIP_OFF			(1)	// FW_INTERNALVERSION2: 1
#define FW_VERSION_FULL_8G_XZIP_ON				(2)	// FW_INTERNALVERSION2: 2
#define FW_VERSION_FULL_PERFORMANCE_XZIP_ON		(3)	// FW_INTERNALVERSION2: 3
#define FW_VERSION_FULL_PERFORMANCE_XZIP_OFF	(5)	// FW_INTERNALVERSION2: 5

// TCG Mode
#define TCG_MODE_NOT_SUPPORT			(0)
#define TCG_MODE_OPAL					(1)
#define TCG_MODE_PYRITE					(2)
#define TCG_MODE_COMBINED				(3)

#define U32_MAX             (0xFFFFFFFF)
#define U16_MAX				(0xFFFF)
#define U8_MAX				(0xFF)
#endif /* _SYMBOL_H_ */
