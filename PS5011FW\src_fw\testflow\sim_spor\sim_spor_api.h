#ifndef _SIM_SPOR_API_H_
#define _SIM_SPOR_API_H_
#include "sim_spor.h"

#if SIM_SPOR_EN
#define M_SIM_SPOR_EVENT_CHECKER(ubBaseCode, ubSubCode, ulParameter1, ulParameter2)            do { \
                                                                                                SimSPOREventChecker(ubBaseCode, ubSubCode, ulParameter1, ulParameter2); \
                                                                                            } while(0)
#else
#define M_SIM_SPOR_EVENT_CHECKER(ubBaseCode, ubSubCode, ulParameter1, ulParameter2)             do{}while(0)
#endif /* SIM_SPOR_EN */

AOM_SPOR_2 void SimSPORFlow(void);
AOM_SPOR_2 void SimSPORSelectInitEvent(void);
AOM_SPOR_2 void SimSPORInit(void);
AOM_SPOR_2 void SimSPORVendorPowerHandler(U32 ulSPOREventEn);
#endif /* _SIM_SPOR_API_H_ */
