#ifndef _E13_COP1_CMD_TYPE_H_
#define _E13_COP1_CMD_TYPE_H_

#include "setup.h"
#include "typedef.h"

#define COP1_DEFAULT_SRC	(0x0)

#define COP1_UNFINISH		(0x0)
#define COP1_FINISH			(0x1)

#define COP1_EGLCA1_BMU_FREE_DISEN	(0)
#define COP1_EGLCA1_BMU_FREE_EN		(1)

#define COP1_EGLCA1_WLBID_0			(0)
#define COP1_EGLCA1_WLBID_1			(1)

#define COP1_EGLCA1_SET_FREE_OP_0			(0)		//BMU Clear Double Free Flag(if DF is 1), or Free(if DF is 0)
#define COP1_EGLCA1_SET_FREE_OP_1			(1)		//BMU Don't care Double Free Flag, always Free

#define MODE_EGLCA2_NOT_NEED_UPDATE_XZIP_PCA	(0)
#define MODE_EGLCA2_NEED_UPDATE_XZIP_PCA		(1)

#define COP1_EGLCA2_INVALID_XZIP_INDEX		(0)

#define COP1_EGLCA2_NOT_NEED_UPDATE_XZIP	(0)
#define COP1_EGLCA2_NEED_UPDATE_XZIP		(1)

#define COP1_INIT_LINK_DEFAULT_MODE         (0x5)
#define COP1_ALLO_PB_TIMEOUT_INFINITE       (0xF)

#define COP1_SHBLK_ID               (0x2)
#define COP1_GCBLK_ID               (0x3)
#define COP1_ST1BLK_ID              (0x4)
#define COP1_ST3CBLK_ID             (0x5)
#define COP1_ST3BLK_ID              (0x7)

/* Search */
#define COP1_SHBLK_OP_SHLCA         (0x2)
#define COP1_SHBLK_OP_BARRIER         (0x3)

/* ST1 */
#define COP1_ST1BLK_OP_TRIM		(0x2)
#define COP1_ST1BLK_OP_FLUSH		(0x3)
#define COP1_ST1BLK_OP_RELEASE		(0x4)
#define COP1_ST1BLK_OP_EGLCA		(0x5)

/* GC */
#define COP1_GCBLK_OP_REGR			(0x0)
#define COP1_GCBLK_OP_REGW			(0x1)
#define COP1_GCBLK_OP_INIT			(0x6)
#define COP1_GCBLK_OP_SET_VC        (0x2)
#define COP1_GCBLK_OP_CNT_UPD       (0x4)
#define COP1_GCBLK_OP_FLUSH         (0x5)

/* ST3 */
#define COP1_ST3BLK_OP_REGR         (0x0)
#define COP1_ST3BLK_OP_REGW         (0x1)
#define COP1_ST3BLK_OP_INSERT_DSA   (0x2)
#define COP1_ST3BLK_OP_INSERT_GCSA  (0x3)
#define COP1_ST3BLK_OP_TRIM_PMD     (0x4)
#define COP1_ST3BLK_OP_TRIM_PTE     (0x5)
#define COP1_ST3BLK_OP_TRIM_COND     (0x6)
#define COP1_ST3BLK_OP_GC_PTE       (0x7)
#define COP1_ST3BLK_OP_UPDPMD       (0x8)
#define COP1_ST3BLK_OP_NOTIFY		(0x9)	//[CC_NEW]
#define COP1_ST3BLK_OP_SAVELOAD     (0xA)
#define COP1_ST3BLK_OP_PB_RELEASE   (0xC)

/* ST3C */
#define COP1_ST3CBLK_OP_INIT_LINK       		(0x0)
#define COP1_ST3CBLK_OP_LOCK_DT         		(0x1)
#define COP1_ST3CBLK_OP_UNLOCK_DT       		(0x2)
#define COP1_ST3CBLK_OP_UNLOCK_GCPTE    		(0x3)
#define COP1_ST3CBLK_OP_UNLOCK_FW       		(0x4)
#define COP1_ST3CBLK_OP_ALLOC_PB        		(0x5)
#define COP1_ST3CBLK_OP_FREE_PB         		(0x6)
#define COP1_ST3CBLK_OP_REMOVE_TBL      		(0x8)
#define COP1_ST3CBLK_OP_FLUSH_FREE_CID  		(0x9)
#define COP1_ST3CBLK_OP_SET_FREE_CID    		(0xA)
#define COP1_ST3CBLK_OP_BYPASS_OR_EXP			(0xB)
#define COP1_ST3CBLK_OP_REGR             		(0x10)
#define COP1_ST3CBLK_OP_REGW             		(0x11)
#define COP1_ST3CBLK_OP_LOAD_TABLE				(0x15)

#define COP1_ST3CBLK_OP_ABORT					(0x14)

#define COP1_CMD_DONE						(0x3)

//for search result
enum {
	SEARCH_LOC_ERROR = 0,
	SEARCH_LOC_ST1 = 1,
	SEARCH_LOC_ST3_NORMAL_PCA = 2,
	SEARCH_LOC_ST3_NONNORMAL_PCA = 6,
	SEARCH_LOC_WLB = 8,
	SEARCH_LOC_WLB_LOCK_FULL = 11,
	SEARCH_LOC_ST1_XZIP_HIT = 13,
	SEARCH_LOC_ST3_XZIP_HIT = 14,
	SEARCH_LOC_XZIP_LOCK_FULL = 15
};

typedef struct COP1SQCommonCmd {
	U32 ulDW0;
	U32 ulDW1;
} COP1SQCommonCmd_t;

typedef struct COP1CQRst {
	U32 ulData;
	U32 TagId		: 10;
	U32 btReserved0	: 1;
	U32 btFinish	: 1;
	U32 Source		: 4;
	U32 Reserved1 	: 4;
	U32 Error		: 4;
	U32 BlkOP		: 4;
	U32 TargetBlk	: 4;
} COP1CQRstStruct_t;

/*****************************************************************
			SH Blk
******************************************************************/
typedef union {
	U32	ulAll;
	//Terry 0529
	struct {
		U32 ulPCA;
	} PCA;
	struct {
		U32 PBAddr	: 9;
		U32 Reserved0	: 21;
		U32 btE3D4KFlag	: 1;
		U32 btZip : 1;
	} HitBMUWLB;
	struct {
		U32 CID		: 9;
		U32 Reserved0	: 7;
		U32 PBAddr	: 9;
		U32 Reserved1	: 4;
		U32 btFWLinkBit	: 1;
		U32 btDeallocateBit : 1;
		U32 btTrimBit	: 1;
	} FWLinkPMDPTEInSRAM;
	struct {
		U32 CID			: 19;
		U32 Reserved1	: 10;
		U32 btFWLinkBit	: 1;
		U32 btDeallocateBit : 1;
		U32 btTrimBit	: 1;
	} FWLinkPMDPTEInHMB;

	struct {
		U32 PBAddr	 :  9;
		U32 Reserved	 : 21;
		U32 btE3D4KFlag	 :  1;
		U32 btZeroFlag	 :  1;
	} WLB;

	struct {
		U32 PBAddr	  :  9;
		U32 ubXZIPIndex	  :  8;
		U32 Reserved	  : 15;
	} XZIP;

} RespInfo_t;

typedef struct COP1SHBlkSCHLCACmd {
	U32 ulLCA;
	U32 TagID		: 10;
	U32 btReserved0	: 1;
	U32 btFinish	: 1;
	U32 Source		: 4;
	U32 COP0		: 2;
	U32 BMS			: 4;
	U32 btFW		: 1;
	U32 btLBIDSEL	: 1;
	U32 BlkOP		: 4;
	U32 TargetBlk	: 4;
} COP1SHBlkSCHLCACmd_t;

typedef struct COP1SHBlkSCHLCARst {
	RespInfo_t ulRespInfo;
	U32 TagId		: 10;
	U32 btReserved0	: 1;
	U32 btFinish	: 1;
	U32 Source		: 4;
	U32 Loc			: 4;
	U32 Error		: 4;
	U32 BlkOP		: 4;
	U32 TargetBlk	: 4;
} COP1SHBlkSCHLCARst_t;

/*****************************************************************
			ST1 Blk
******************************************************************/

typedef struct COP1ST1BlkEGLCA1Cmd {
	U32	ulLCA;
	U32 WLBOffset	: 10;
	U32 btReserved0	: 1;
	U32 btFinish	: 1;
	U32 Source		: 4;
	U32 btFreeOP	: 1;
	U32 btWLBID		: 1;
	U32 btBMUFreeEn	: 1;
	U32 Reserved1	: 5;
	U32 BlkOP		: 4;
	U32 TargetBlk	: 4;
} COP1ST1BlkEGLCA1Cmd_t;

typedef struct COP1ST1BlkEGLCA2Cmd {
	U32	ulPCA;
	U32 TagId				: 10;
	U32 btNeedUpdateXZIPPCA	: 1;
	U32 btFinish			: 1;
	U32 Source				: 4;
	U32 ubXZIPIndex			: 8;
	U32 BlkOP				: 4;
	U32 TargetBlk			: 4;
} COP1ST1BlkEGLCA2Cmd_t;

typedef struct COP1ST1BlkTrimCmd {
	U32 ulReserved0;
	U32 TagId			: 10;
	U32 btReserved1		: 1;
	U32 btFinish		: 1;
	U32 Source			: 4;
	U32 ubReserved2		: 8;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1ST1BlkTrimCmd_t;

typedef struct COP1ST1BlkTrimRst {
	U32 ulReserved0;
	U32 TagId			: 10;
	U32 btReserved1		: 1;
	U32 btFinish		: 1;
	U32 Source			: 4;
	U32 ubReserved2		: 8;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1ST1BlkTrimRst_t;

typedef struct COP1ST1BlkFlushCmd {
	U32 ulReserved0;
	U32 TagId			: 10;
	U32 btReserved1		: 1;
	U32 btFinish		: 1;
	U32 Source			: 4;
	U32 ubReserved2		: 8;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1ST1BlkFlushCmd_t;

typedef struct COP1ST1BlkFlushRst {
	U32 ulReserved0;
	U32 TagId			: 10;
	U32 btReserved1		: 1;
	U32 btFinish		: 1;
	U32 Source			: 4;
	U32 btError			: 1;
	U32 Reserved2		: 7;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1ST1BlkFlushRst_t;

typedef struct COP1ST1BlkReleaseCmd {
	U32 btSortingRule	: 1;
	U32 Reserved0		: 7;
	U32 ubReleaseLoc	: 8;
	U32 uwReserved1		: 16;
	U32 TagId			: 10;
	U32 btReserved2		: 1;
	U32 btFinish		: 1;
	U32 Source			: 4;
	U32 ubReserved3		: 8;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1ST1BlkReleaseCmd_t;

typedef struct COP1ST1BlkReleaseRst {
	U32 ulReserved0;
	U32 TagId			: 10;
	U32 btReserved1		: 1;
	U32 btFinish		: 1;
	U32 Source			: 4;
	U32 Reserved2		: 5;
	U32 Error			: 3;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1ST1BlkReleaseRst_t;

/*****************************************************************
			ST3 Blk
******************************************************************/
typedef struct COP1ST3BlkInsertDSACmd {
	U32 DSAStartIdx		: 13;
	U32 Reserved0		: 3;
	U32 JobNum			: 14;
	U32 Reserved1		: 2;
	U32 TagId			: 10;
	U32 btReserved2		: 1;
	U32 btFinish		: 1;
	U32 Source			: 4;
	U32 COP0			: 2;
	U32 Reserved3		: 4;
	U32 Type			: 2;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1ST3BlkInsertDSACmd_t;

typedef struct COP1ST3BlkInsertDSARst {
	U32 DSAEndIdx		: 13;
	U32 Reserved		: 3;
	U32 Done			: 2;
	U32 Reserved1		: 14;
	U32 TagId			: 10;
	U32 btReserved2		: 1;
	U32 btFinish		: 1;
	U32 Source			: 4;
	U32 Reserved3		: 3;
	U32 btZero			: 1;
	U32 btReserved4		: 1;
	U32 btErrorGC		: 1;
	U32 btErrorAXI		: 1;
	U32 btErrorST3C		: 1;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1ST3BlkInsertDSARst_t;

typedef struct COP1ST3BlkInsertGCSACmd {
	U32 GCSAStartIdx	: 13;
	U32 Reserved0		: 3;
	U32 JobNum			: 14;
	U32 Reserved1		: 2;
	U32 TagId			: 10;
	U32 btReserved2		: 1;
	U32 btFinish		: 1;
	U32 Source			: 4;
	U32 COP0			: 2;
	U32 Reserved3		: 4;
	U32 Type			: 2;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1ST3BlkInsertGCSACmd_t;

typedef struct COP1ST3BlkInsertGCSARst {
	U32 GCSAEndIdx		: 13;
	U32 Reserved		: 3;
	U32 Done			: 2;
	U32 Reserved1		: 14;
	U32 TagId			: 10;
	U32 btReserved2		: 1;
	U32 btFinish		: 1;
	U32 Source			: 4;
	U32 Reserved3		: 3;
	U32 btZero			: 1;
	U32 btReserved4		: 1;
	U32 btErrorGC		: 1;
	U32 btErrorAXI		: 1;
	U32 btErrorST3C		: 1;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1ST3BlkInsertGCSARst_t;

typedef struct {
	U32 ulReserved0;
	U32 TagId			: 10;
	U32 btReserved1		: 1;
	U32 btFinish			: 1;
	U32 Source			: 4;
	U32 COP0			: 2;
	U32 Reserved2		: 4;
	U32 Type			: 2;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1ST3BlkTrimPTECmd_t;

typedef struct {
	U32 ulReserved0;
	U32 TagId			: 10;
	U32 btReserved1		: 1;
	U32 btFinish		: 1;
	U32 Source			: 4;
	U32 Reserved2		: 3;
	U32 btZero			: 1;
	U32 btReserved3		: 1;
	U32 btErrorGC		: 1;
	U32 btErrorAXI		: 1;
	U32 btErrorST3C		: 1;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1ST3BlkTrimPTERst_t;

typedef struct {
	U32 ulReserved0;
	U32 TagId			: 10;
	U32 btReserved1		: 1;
	U32 btFinish		: 1;
	U32 Source			: 4;
	U32 COP0			: 2;
	U32 Reserved2		: 4;
	U32 Type			: 2;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1ST3BlkTrimPMDCmd_t;

typedef struct {
	U32 ulReserved0;
	U32 TagId			: 10;
	U32 btReserved1		: 1;
	U32 btFinish		: 1;
	U32 Source			: 4;
	U32 Reserved2		: 5;
	U32 btErrorGC		: 1;
	U32 btErrorAXI		: 1;
	U32 btErrorST3C		: 1;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1ST3BlkTrimPMDRst_t;

typedef struct {
	U32 LogNum			: 13;
	U32 Reserved0		: 19;
	U32 TagId			: 10;
	U32 btReserved1		: 1;
	U32 btFinish		: 1;
	U32 Source			: 4;
	U32 COP0			: 2;
	U32 Reserved2		: 4;
	U32 Type			: 2;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1ST3BlkTrimCondCmd_t;

typedef struct {
	U32 ResultLogNum	: 13;
	U32 Reserved0		: 3;
	U32 Done			: 2;
	U32 Reserved1		: 14;
	U32 TagId			: 10;
	U32 btReserved2		: 1;
	U32 btFinish		: 1;
	U32 Source			: 4;
	U32 Reserved3		: 3;
	U32 btZero			: 1;
	U32 btReserved4		: 1;
	U32 btErrorGC		: 1;
	U32 btErrorAXI		: 1;
	U32 btErrorST3C		: 1;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1ST3BlkTrimCondRst_t;

typedef struct COP1ST3BlkUpdatePMDCmd {
	U32 PMDLogStartIdx	: 13;
	U32 Reserved0		: 3;
	U32 JobNum			: 14;
	U32 Reserved1		: 2;
	U32 TagId			: 10;
	U32 btReserved2		: 1;
	U32 btFinish		: 1;
	U32 Source			: 4;
	U32 COP0			: 2;
	U32 btReserved3		: 1;
	U32 btMode			: 1;
	U32 Reserved4		: 2;
	U32 Type			: 2;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1ST3BlkUpdatePMDCmd_t;

typedef struct COP1ST3BlkUpdatePMDRst {
	U32 PMDLogEndIdx	: 13;
	U32 Reserved		: 3;
	U32 Done			: 2;
	U32 Reserved1		: 14;
	U32 TagId			: 10;
	U32 btReserved2		: 1;
	U32 btFinish		: 1;
	U32 Source			: 4;
	U32 Reserved3		: 3;
	U32 btZero			: 1;
	U32 btReserved4		: 1;
	U32 btErrorGC		: 1;
	U32 btErrorAXI		: 1;
	U32 btErrorST3C		: 1;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1ST3BlkUpdatePMDRst_t;

typedef struct COP1ST3BlkGCPTECmd {
	U32 GCPTELogNum		: 13;
	U32 Reserved0		: 19;
	U32 TagId			: 10;
	U32 btReserved1		: 1;
	U32 btFinish		: 1;
	U32 Source			: 4;
	U32 COP0			: 2;
	U32 Reserved2		: 4;
	U32 Type			: 2;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1ST3BlkGCPTECmd_t;

typedef struct COP1ST3BlkGCPTERst {
	U32 ResultLogNum	: 13;
	U32 Reserved0		: 3;
	U32 Done			: 2;
	U32 Reserved1		: 14;
	U32 TagId			: 10;
	U32 btReserved2		: 1;
	U32 btFinish		: 1;
	U32 Source			: 4;
	U32 Reserved3		: 5;
	U32 btErrorGC		: 1;
	U32 btErrorAXI		: 1;
	U32 btErrorST3C		: 1;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1ST3BlkGCPTERst_t;
typedef struct COP1ST3BlkNotifyRst {
	U32 CollectSize		: 10;
	U32 Reserved1		: 22;
	U32 TagId			: 10;
	U32 btReserved2		: 1;
	U32 btFinish		: 1;
	U32 Source			: 4;
	U32 Reserved3		: 3;
	U32 btZero			: 1;
	U32 btReserved4		: 1;
	U32 btErrorGC		: 1;
	U32 btErrorAXI		: 1;
	U32 btErrorST3C		: 1;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1ST3BlkNotifyRst_t;
/*****************************************************************
			ST3C Blk
******************************************************************/
typedef struct COP1ST3CBlkInitialLinkCmd {
	U32 Mode			: 3;
	U32 btReserved0     : 1;
	U32 Head          : 9;
	U32 Tail          : 9;
	U32 uwSize          : 9;
	U32 btReserved1     : 1;
	U32 TagId			: 10;
	U32 btReserved2		: 1;
	U32 btFinish		: 1;
	U32 Source		: 4;
	U32 ubReserved1		: 8;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;

} COP1ST3CBlkInitialLinkCmd_t;

typedef struct COP1ST3CBlkInitialLinkRst {
	U32 ulReserved0;
	U32 TagId			: 10;
	U32 btReserved1		: 1;
	U32 btFinish		: 1;
	U32 Source			: 4;
	U32 ubReserved2		: 8;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1ST3CBlkInitialLinkRst_t;

typedef struct COP1ST3CBlkAllocatePBCmd {
	U32 AllocatePBNum : 9;
	U32 Reserved0     : 7;
	U32 TimeOut       : 4;
	U32 LBID          : 3;
	U32 btQOB           : 1;
	U32 btFUA           : 1;
	U32 Reserved1     : 7;
	U32 TagId			: 10;
	U32 btReserved2		: 1;
	U32 btFinish		: 1;
	U32 Source			: 4;
	U32 ubReserved3		: 8;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1ST3CBlkAllocatePBCmd_t;

typedef struct COP1ST3CBlkAllocatePBRst {
	U32 ResultPBNum   : 9;
	U32 Reserved0     : 11;
	U32 LBID          : 3;
	U32 btQOB           : 1;
	U32 btFUA           : 1;
	U32 Reserved1     : 7;
	U32 TagId			: 10;
	U32 btReserved2		: 1;
	U32 btFinish		: 1;
	U32 Source		: 4;
	U32 Reserved3		: 5;
	U32 btError         : 1;
	U32 Reserved4     : 2;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1ST3CBlkAllocatePBRst_t;

typedef struct COP1ST3CBlkFreePBCmd {
	U32 FreePBNum 	: 9;
	U32 Reserved0     : 11;
	U32 LBID          : 3;
	U32 btQOB           : 1;
	U32 btFUA           : 1;
	U32 Reserved1     : 7;
	U32 TagId			: 10;
	U32 btReserved2		: 1;
	U32 btFinish		: 1;
	U32 Source		: 4;
	U32 ubReserved3		: 8;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;

} COP1ST3CBlkFreePBCmd_t;

typedef struct COP1ST3CBlkFreePBRst {
	U32 ResultPBNum   : 9;
	U32 Reserved0     : 11;
	U32 LBID          : 3;
	U32 btQOB           : 1;
	U32 btFUA           : 1;
	U32 Reserved1     : 7;
	U32 TagId			: 10;
	U32 btReserved2		: 1;
	U32 btFinish		: 1;
	U32 Source		: 4;
	U32 Reserved3		: 5;
	U32 btError         : 1;
	U32 Reserved4     : 2;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1ST3CBlkFreePBRst_t;

typedef struct COP1ST3CBlkLockDTCmd {
	U32 uwStartIdx		: 16;
	U32 uwLockCnt		: 16;
	U32 TagId			: 10;
	U32 btReserved0		: 1;
	U32 btFinish		: 1;
	U32 Source		: 4;
	U32 ubReserved1		: 8;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1ST3CBlkLockDTCmd_t;

typedef struct COP1ST3CBlkLockDTRst {
	U32 ulReserved0;
	U32 TagId			: 10;
	U32 btReserved1		: 1;
	U32 btFinish		: 1;
	U32 Source			: 4;
	U32 Reserved2		: 5;
	U32 btErrorReadDTLog: 1;
	U32 btErrorReadCTBL	: 1;
	U32 btReserved3		: 1;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1ST3CBlkLockDTRst_t;

typedef struct COP1ST3CBlkUnlockDTCmd {
	U32 uwStartIdx		: 16;
	U32 uwLockCnt		: 16;
	U32 TagId			: 10;
	U32 btReserved0		: 1;
	U32 btFinish		: 1;
	U32 Source		: 4;
	U32 btToFreeCIDPool	: 1;
	U32 Reserved1		: 7;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1ST3CBlkUnlockDTCmd_t;

typedef struct COP1ST3CBlkUnlockDTRst {
	U32 ulReserved0;
	U32 TagId			: 10;
	U32 btReserved1		: 1;
	U32 btFinish		: 1;
	U32 Source			: 4;
	U32 Reserved2		: 5;
	U32 btErrorReadDTLog: 1;
	U32 btErrorReadCTBL	: 1;
	U32 btErrorRWCidPool: 1;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1ST3CBlkUnlockDTRst_t;

typedef struct COP1ST3CBlkRemoveTableCmd {
	U32 CID				: 9;
	U32 Reserved0		: 21;
	U32 btIgnoreFW		: 1;
	U32 btAll			: 1;
	U32 TagId			: 10;
	U32 btReserved1		: 1;
	U32 btFinish		: 1;
	U32 Source			: 4;
	U32 Type			: 3;
	U32 btToHead		: 1;
	U32 Reserved2		: 4;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1ST3CBlkRemoveTableCmd_t;

typedef struct COP1ST3CBlkRemoveTableRst {
	U32 CID				: 9;
	U32 Reserved0		: 21;
	U32 btIgnoreFW		: 1;
	U32 btAll			: 1;
	U32 TagId			: 10;
	U32 btReserved1		: 1;
	U32 btFinish		: 1;
	U32 Source			: 4;
	U32 Type			: 3;
	U32 btFail			: 1;
	U32 Reserved2		: 4;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1ST3CBlkRemoveTableRst_t;

typedef union COP1ST3CBlkBypassOrExpansionCmd {
	U64 uoAll;
	struct {
		U32 ulLow;
		U32 ulHigh;
	};
	struct {
		U32 				: 4;
		U32 btSt3PMDHpCmd	: 1;
		U32 btSt3PMDCmd		: 1;
		U32 btSt3PTEHpCmd	: 1;
		U32 btSt3PTECmd		: 1;
		U32 btCop0			: 1;
		U32 btDmac			: 1;
		U32 btBmu			: 1;
		U32 btGc			: 1;
		U32 btSt3PMDRlt		: 1;
		U32 btSt3PTERlt		: 1;
		U32 btExp			: 1;
		U32 ubAbort			: 2;
		U32 btFWCopy		: 1;
		U32 Reserved0		: 14;
		U32 TagId			: 10;
		U32 btReserved1		: 1;
		U32 btFinish		: 1;
		U32 Source			: 4;
		U32 Type			: 3;
		U32 btFail			: 1;
		U32 ubReserved2		: 4;
		U32 BlkOP			: 4;
		U32 TargetBlk		: 4;
	};
} COP1ST3CBlkBypassOrExpansionCmd_t;


typedef struct COP1ST3CBlkBypassOrExpansionRst {
	U32 Cnt				: 4;
	U32 btSt3PMDHpCmd	: 1;
	U32 btSt3PMDCmd		: 1;
	U32 btSt3PTEHpCmd	: 1;
	U32 btSt3PTECmd		: 1;
	U32 btCop0			: 1;
	U32 btDmac			: 1;
	U32 btBmu			: 1;
	U32 btGc			: 1;
	U32 btSt3PMDRlt		: 1;
	U32 btSt3PTERlt		: 1;
	U32 btExp			: 1;
	U32 ubAbort			: 2;
	U32 btFWCopy		: 1;
	U32 Reserved0		: 14;
	U32 TagId			: 10;
	U32 btReserved1		: 1;
	U32 btFinish		: 1;
	U32 Source			: 4;
	U32 Type			: 3;
	U32 btFail			: 1;
	U32 Reserved2		: 4;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1ST3CBlkBypassOrExpansionRst_t;

typedef struct COP1ST3CBlkUnlockFWCmd {
	U32 uwStartIdx		: 16;
	U32 uwLockCnt		: 16;
	U32 TagId			: 10;
	U32 btReserved0		: 1;
	U32 btFinish		: 1;
	U32 Source			: 4;
	U32	btToFreeCIDPool	: 1;
	U32 Reserved1		: 7;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1ST3CBlkUnlockFWCmd_t;

typedef struct COP1ST3CBlkUnlockFWRst {
	U32 ulReserved0;
	U32 TagId			: 10;
	U32 btReserved1		: 1;
	U32 btFinish		: 1;
	U32 Source			: 4;
	U32 Reserved2		: 5;
	U32 btError0		: 1;
	U32 Reserved3		: 2;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1ST3CBlkUnlockFWRst_t;

typedef struct COP1ST3CBlkUnlockGCPTECmd {
	U32 uwStartIdx		: 16;
	U32 uwLockCnt		: 16;
	U32 TagId			: 10;
	U32 btReserved0		: 1;
	U32 btFinish		: 1;
	U32 Source			: 4;
	U32	btToFreeCIDPool	: 1;
	U32 Reserved1		: 7;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1ST3CBlkUnlockGCPTECmd_t;

typedef struct COP1ST3CBlkUnlockGCPTERst {
	U32 ulReserved0;
	U32 TagId			: 10;
	U32 btReserved1		: 1;
	U32 btFinish		: 1;
	U32 Source		: 4;
	U32 Reserved2		: 5;
	U32 btErrorReadDTLog: 1;
	U32 btErrorReadCTBL	: 1;
	U32 btErrorReadWriteCidTable	: 1;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1ST3CBlkUnlockGCPTERst_t;

typedef struct COP1ST3CBlkFlushFreeCidCmd {
	U32 ulReserved0;
	U32 TagId			: 10;
	U32 btReserved1		: 1;
	U32 btFinish		: 1;
	U32 Source		: 4;
	U32 ubReserved2		: 8;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1ST3CBlkFlushFreeCidCmd_t;

typedef union {
	struct {
		U32 WPTR			: 31;
		U32	btEmpty			: 1;
		U32 TagId			: 10;
		U32 btReserved1		: 1;
		U32 btFinish		: 1;
		U32 Source			: 4;
		U32 ubReserved2		: 8;
		U32 BlkOP			: 4;
		U32 TargetBlk		: 4;
	} Result0;
	struct {
		U32 RPTR			: 31;
		U32	btReserved0		: 1;
		U32 TagId			: 10;
		U32 btReserved1		: 1;
		U32 btFinish		: 1;
		U32 Source			: 4;
		U32 ubReserved2		: 8;
		U32 BlkOP			: 4;
		U32 TargetBlk		: 4;
	} Result1;
	struct {
		U32 ulBufLevel;
		U32 TagId			: 10;
		U32 btReserved0		: 1;
		U32 btFinish		: 1;
		U32 Source			: 4;
		U32 ubReserved1		: 8;
		U32 BlkOP			: 4;
		U32 TargetBlk		: 4;
	} Result2;
} COP1ST3CBlkFlushFreeCidRst_t;

typedef union {
	struct {
		U32 WPTR			: 31;
		U32	btReserved0		: 1;
		U32 TagId			: 10;
		U32 btReserved1		: 1;
		U32 btFinish		: 1;
		U32 Source			: 4;
		U32 ubReserved2		: 8;
		U32 BlkOP			: 4;
		U32 TargetBlk		: 4;
	} Command0;
	struct {
		U32 RPTR			: 31;
		U32	btReserved0		: 1;
		U32 TagId			: 10;
		U32 btReserved1		: 1;
		U32 btFinish		: 1;
		U32 Source			: 4;
		U32 ubReserved2		: 8;
		U32 BlkOP			: 4;
		U32 TargetBlk		: 4;
	} Command1;
	struct {
		U32 ulBufLevel;
		U32 TagId			: 10;
		U32 btReserved0		: 1;
		U32 btFinish		: 1;
		U32 Source			: 4;
		U32 ubReserved1		: 8;
		U32 BlkOP			: 4;
		U32 TargetBlk		: 4;
	} Command2;
} COP1ST3CBlkSetFreeCidCmd_t;

typedef struct COP1ST3CBlkSetFreeCidRst {
	U32 ulReserved0;
	U32 TagId			: 10;
	U32 btReserved1		: 1;
	U32 btFinish		: 1;
	U32 Source			: 4;
	U32 Reserved2		: 5;
	U32 btError0		: 1;
	U32 Reserved3		: 2;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1ST3CBlkSetFreeCidRst_t;
/*****************************************************************
			GC Blk
******************************************************************/

typedef struct COP1GCBlkDTFlushCmd {
	U32 ulReserved0;
	U32 TagId			: 10;
	U32 btInit			: 1;
	U32 btFinish		: 1;
	U32 Source			: 4;
	U32 ubReserved1		: 8;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1GCBlkDTFlushCmd_t;

typedef struct COP1GCBlkDTFlushRst {
	U32 DTCnt			: 19;
	U32 Reserved		: 11;
	U32 CntError		: 2;
	U32 TagId			: 10;
	U32 btInit			: 1;
	U32 btFinish		: 1;
	U32 Source			: 4;
	U32 Reserved4		: 7;
	U32 btError			: 1;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1GCBlkDTFlushRst_t;

typedef union COP1GCBlkSetVCCmd {
	struct {
		U32 ulValidMask;
		U32 TagId		: 10;
		U32 btReserved0	: 1;
		U32 btFinish	: 1;
		U32 Source		: 4;
		U32 ubReserved1	: 8;
		U32 BlkOP		: 4;
		U32 TargetBlk	: 4;
	} SQ1;
	struct {
		U32 ulValidCnt;
		U32 TagId		: 10;
		U32 btReserved0	: 1;
		U32 btFinish	: 1;
		U32 Source		: 4;
		U32 ubReserved1	: 8;
		U32 BlkOP		: 4;
		U32 TargetBlk	: 4;
	} SQ2;
	struct {
		U32 VB			: 12;
		U32 btInvalid 	: 1;
		U32 Reserved0 	: 19;
		U32 TagId		: 10;
		U32 btReserved1	: 1;
		U32 btFinish	: 1;
		U32 Source		: 4;
		U32 ubReserved2	: 8;
		U32 BlkOP		: 4;
		U32 TargetBlk	: 4;
	} SQ3;
} COP1GCBlkSetVCCmd_t;

typedef struct COP1GCBlkSetVCRst {
	U32 ulReserved;
	U32 TagId			: 10;
	U32 btInit			: 1;
	U32 btFinish		: 1;
	U32 Source			: 4;
	U32 Reserved4		: 7;
	U32 btError			: 1;
	U32 BlkOP			: 4;
	U32 TargetBlk		: 4;
} COP1GCBlkSetVCRst_t;

typedef struct COP1GCBlkUpdateCntCmd {
	U32 VB1			: 12;
	U32 btInvalid1	: 1;
	U32 Reserved1	: 2;
	U32 btOP1		: 1;
	U32 VB2			: 12;
	U32 btInvalid2	: 1;
	U32 Reserved2	: 2;
	U32 btOP2		: 1;

	U32 TagId 		: 10;
	U32 btReturn 	: 1;
	U32 btFinish	: 1;
	U32 Source		: 4;
	U32 ubReserved3	: 8;
	U32 BlkOP		: 4;
	U32 TargetBlk	: 4;
} COP1GCBlkUpdateCntCmd_t;

typedef struct COP1GCBlkUpdateCntRst {
	U32 ulReserved;
	U32 TagId		: 10;
	U32 btReserved0	: 1;
	U32 btFinish	: 1;
	U32 Source		: 4;
	U32 btZbit		: 1;
	U32 Reserved1 	: 5;
	U32 Error		: 2;
	U32 BlkOP		: 4;
	U32 TargetBlk	: 4;
} COP1GCBlkUpdateCntRst_t;
//***********************************************************************************************************************

typedef union {
	COP1SQCommonCmd_t			uoCOP1SQCommonCmd;
	COP1SHBlkSCHLCACmd_t		uoSHBlkSCHLCACmd;
	COP1ST1BlkEGLCA1Cmd_t		uoST1BlkEGLCA1Cmd;
	COP1ST1BlkEGLCA2Cmd_t		uoST1BlkEGLCA2Cmd;
	COP1ST1BlkTrimCmd_t			uoST1BlkTrimCmd;
	COP1ST1BlkFlushCmd_t		uoST1BlkFlushCmd;
	COP1ST1BlkReleaseCmd_t		uoST1BlkReleaseCmd;
	COP1ST3BlkInsertDSACmd_t	uoST3BlkInsertDSACmd;
	COP1ST3BlkInsertGCSACmd_t	uoST3BlkInsertGCSACmd;
	COP1ST3BlkTrimPTECmd_t		uoST3BlkTrimPTECmd;
	COP1ST3BlkTrimPMDCmd_t		uoST3BlkTrimPMDCmd;
	COP1ST3BlkTrimCondCmd_t		uoST3BlkTrimCondCmd;
	COP1ST3BlkUpdatePMDCmd_t	uoST3BlkUpdatePMDCmd;
	COP1ST3BlkGCPTECmd_t		uoST3BlkGCPTECmd;
	COP1ST3CBlkInitialLinkCmd_t uoST3CBlkInitialLinkCmd;
	COP1ST3CBlkAllocatePBCmd_t  uoST3CBlkAllocatePBCmd;
	COP1ST3CBlkFreePBCmd_t  	uoST3CBlkFreePBCmd;
	COP1ST3CBlkLockDTCmd_t 		uoST3CBlkLockDTCmd;
	COP1ST3CBlkUnlockDTCmd_t 	uoST3CBlkUnlockDTCmd;
	COP1ST3CBlkRemoveTableCmd_t uoST3CBlkRemoveTableCmd;
	COP1GCBlkDTFlushCmd_t		uoGCBlkDTFlushCmd;
	COP1GCBlkSetVCCmd_t			uoGCBlkSetVCCmd;
	COP1GCBlkUpdateCntCmd_t		uoGCBlkUpdateCntCmd;
	COP1ST3CBlkUnlockFWCmd_t	uoST3CBlkUnlockFWCmd;
	COP1ST3CBlkUnlockGCPTECmd_t	uoST3CBlkUnlockGCPTECmd;
	COP1ST3CBlkFlushFreeCidCmd_t  uoST3CBlkFlushFreeCidCmd;
	COP1ST3CBlkSetFreeCidCmd_t	uoST3CBlkSetFreeCidCmd;
} COP1SQCmd_t;

typedef union {
	COP1CQRstStruct_t			uoCOP1CQRstStruct;
	COP1SHBlkSCHLCARst_t		uoSHBlkSCHLCARst;
	COP1ST1BlkTrimRst_t			uoST1BlkTrimRst;
	COP1ST1BlkFlushRst_t		uoST1BlkFlushRst;
	COP1ST1BlkReleaseRst_t		uoST1BlkReleaseRst;
	COP1ST3BlkInsertDSARst_t	uoST3BlkInsertDSARst;
	COP1ST3BlkInsertGCSARst_t	uoST3BlkInsertGCSARst;
	COP1ST3BlkTrimPTERst_t		uoST3BlkTrimPTERst;
	COP1ST3BlkTrimPMDRst_t		uoST3BlkTrimPMDRst;
	COP1ST3BlkTrimCondRst_t		uoST3BlkTrimCondRst;
	COP1ST3BlkUpdatePMDRst_t	uoST3BlkUpdatePMDRst;
	COP1ST3BlkGCPTERst_t		uoST3BlkGCPTERst;
	COP1ST3BlkNotifyRst_t         uoST3BlkNotifyRst;
	COP1ST3CBlkInitialLinkRst_t uoST3CBlkInitialLinkRst;
	COP1ST3CBlkAllocatePBRst_t  uoST3CBlkAllocatePBRst;
	COP1ST3CBlkFreePBRst_t  	uoST3CBlkFreePBRst;
	COP1ST3CBlkLockDTRst_t 		uoST3CBlkLockDTRst;
	COP1ST3CBlkUnlockDTRst_t 	uoST3CBlkUnlockDTRst;
	COP1ST3CBlkRemoveTableRst_t uoST3CBlkRemoveTableRst;
	COP1ST3CBlkBypassOrExpansionRst_t uoST3CBlkBypassOrExpansionRst;
	COP1GCBlkDTFlushRst_t		uoGCBlkDTFlushRst;
	COP1GCBlkSetVCRst_t			uoGCBlkSetVCRst;
	COP1GCBlkUpdateCntRst_t		uoGCBlkUpdateCntRst;
} COP1CQRst_t;

//Common
#define COP1_CMD_TAGID_SHIFT				(32)
#define COP1_CMD_TAGID_MASK					(BIT_MASK64(10))
#define COP1_CMD_FINISH_SHIFT				(43)
#define COP1_CMD_FINISH_MASK				(BIT_MASK64(1))
#define COP1_CMD_SOURCE_SHIFT				(44)
#define COP1_CMD_SOURCE_MASK				(BIT_MASK64(4))
#define COP1_CMD_BLKOP_SHIFT				(56)
#define COP1_CMD_BLKOP_MASK					(BIT_MASK64(4))
#define COP1_CMD_TARGETBLK_SHIFT			(60)
#define COP1_CMD_TARGETBLK_MASK				(BIT_MASK64(4))

//SH Blk
//SCHLCA
#define COP1_SH_CMD_SCHLCA_LCA_SHIFT					(0)
#define COP1_SH_CMD_SCHLCA_LCA_MASK						(BIT_MASK64(32))
#define COP1_SH_CMD_SCHLCA_COP0_SHIFT					(48)
#define COP1_SH_CMD_SCHLCA_COP0_MASK					(BIT_MASK64(2))
#define COP1_SH_CMD_SCHLCA_BMS_SHIFT					(50)
#define COP1_SH_CMD_SCHLCA_BMS_MASK						(BIT_MASK64(4))
#define COP1_SH_CMD_SCHLCA_FW_SHIFT						(54)
#define COP1_SH_CMD_SCHLCA_FW_MASK						(BIT_MASK64(1))
#define COP1_SH_CMD_SCHLCA_LBIDSEL_SHIFT				(55)
#define COP1_SH_CMD_SCHLCA_LBIDSEL_MASK					(BIT_MASK64(1))

//ST1 Blk
//Release
#define COP1_ST1_CMD_RELEASE_SORTINGRULE_SHIFT			(0)
#define COP1_ST1_CMD_RELEASE_SORTINGRULE_MASK			(BIT_MASK64(1))
#define COP1_ST1_CMD_RELEASE_RELEASELOC_SHIFT			(8)
#define COP1_ST1_CMD_RELEASE_RELEASELOC_MASK			(BIT_MASK64(8))

//EGLCA1
#define COP1_ST1_CMD_EGLCA1_LCA_SHIFT					(0)
#define COP1_ST1_CMD_EGLCA1_LCA_MASK					(BIT_MASK64(32))
#define COP1_ST1_CMD_EGLCA1_WLBOFFSET_SHIFT				(32)
#define COP1_ST1_CMD_EGLCA1_WLBOFFSET_MASK				(BIT_MASK64(10))
#define COP1_ST1_CMD_EGLCA1_FREEOP_SHIFT				(48)
#define COP1_ST1_CMD_EGLCA1_FREEOP_MASK					(BIT_MASK64(1))
#define COP1_ST1_CMD_EGLCA1_WLBID_SHIFT					(49)
#define COP1_ST1_CMD_EGLCA1_WLBID_MASK					(BIT_MASK64(1))
#define COP1_ST1_CMD_EGLCA1_BMUFREEEN_SHIFT				(50)
#define COP1_ST1_CMD_EGLCA1_BMUFREEEN_MASK				(BIT_MASK64(1))

//EGLCA2
#define COP1_ST1_CMD_EGLCA2_PCA_SHIFT					(0)
#define COP1_ST1_CMD_EGLCA2_PCA_MASK					(BIT_MASK64(32))
#define COP1_ST1_CMD_EGLCA2_NEEDUPDATEXZIPPCA_SHIFT		(42)
#define COP1_ST1_CMD_EGLCA2_NEEDUPDATEXZIPPCA_MASK		(BIT_MASK64(1))
#define COP1_ST1_CMD_EGLCA2_XZIPINDEX_SHIFT				(48)
#define COP1_ST1_CMD_EGLCA2_XZIPINDEX_MASK				(BIT_MASK64(8))

//GC Blk
//Set VC
//SQ1
#define COP1_GC_CMD_SET_VC_SQ1_VALIDMASK_SHIFT			(0)
#define COP1_GC_CMD_SET_VC_SQ1_VALIDMASK_MASK			(BIT_MASK64(32))
//SQ2
#define COP1_GC_CMD_SET_VC_SQ2_VALIDCNT_SHIFT			(0)
#define COP1_GC_CMD_SET_VC_SQ2_VALIDCNT_MASK			(BIT_MASK64(32))
//SQ3
#define COP1_GC_CMD_SET_VC_SQ3_VB_SHIFT					(0)
#define COP1_GC_CMD_SET_VC_SQ3_VB_MASK					(BIT_MASK64(12))
#define COP1_GC_CMD_SET_VC_SQ3_INVALID_SHIFT			(12)
#define COP1_GC_CMD_SET_VC_SQ3_INVALID_MASK				(BIT_MASK64(1))

//Update Cnt
#define COP1_GC_CMD_UPDATE_CNT_VB1_SHIFT				(0)
#define COP1_GC_CMD_UPDATE_CNT_VB1_MASK					(BIT_MASK64(12))
#define COP1_GC_CMD_UPDATE_CNT_INVALID1_SHIFT			(12)
#define COP1_GC_CMD_UPDATE_CNT_INVALID1_MASK			(BIT_MASK64(1))
#define COP1_GC_CMD_UPDATE_CNT_OP1_SHIFT				(15)
#define COP1_GC_CMD_UPDATE_CNT_OP1_MASK					(BIT_MASK64(1))
#define COP1_GC_CMD_UPDATE_CNT_VB2_SHIFT				(16)
#define COP1_GC_CMD_UPDATE_CNT_VB2_MASK					(BIT_MASK64(12))
#define COP1_GC_CMD_UPDATE_CNT_INVALID2_SHIFT			(28)
#define COP1_GC_CMD_UPDATE_CNT_INVALID2_MASK			(BIT_MASK64(1))
#define COP1_GC_CMD_UPDATE_CNT_OP2_SHIFT				(31)
#define COP1_GC_CMD_UPDATE_CNT_OP2_MASK					(BIT_MASK64(1))
#define COP1_GC_CMD_UPDATE_CNT_RETURN_SHIFT				(42)
#define COP1_GC_CMD_UPDATE_CNT_RETURN_MASK				(BIT_MASK64(1))

//DT Flush
#define COP1_GC_CMD_DT_FLUSH_INIT_SHIFT					(42)
#define COP1_GC_CMD_DT_FLUSH_INIT_MASK					(BIT_MASK64(1))

//ST3 Blk
//Insert DSA
#define COP1_ST3_CMD_INSERT_DSA_DSASTARTIDX_SHIFT			(0)
#define COP1_ST3_CMD_INSERT_DSA_DSASTARTIDX_MASK			(BIT_MASK64(13))
#define COP1_ST3_CMD_INSERT_DSA_JOBNUM_SHIFT				(16)
#define COP1_ST3_CMD_INSERT_DSA_JOBNUM_MASK					(BIT_MASK64(14))
#define COP1_ST3_CMD_INSERT_DSA_COP0_SHIFT					(48)
#define COP1_ST3_CMD_INSERT_DSA_COP0_MASK					(BIT_MASK64(2))
#define COP1_ST3_CMD_INSERT_DSA_TYPE_SHIFT					(54)
#define COP1_ST3_CMD_INSERT_DSA_TYPE_MASK					(BIT_MASK64(2))

//Insert GCSA
#define COP1_ST3_CMD_INSERT_GCSA_GCSASTARTIDX_SHIFT			(0)
#define COP1_ST3_CMD_INSERT_GCSA_GCSASTARTIDX_MASK			(BIT_MASK64(13))
#define COP1_ST3_CMD_INSERT_GCSA_JOBNUM_SHIFT				(16)
#define COP1_ST3_CMD_INSERT_GCSA_JOBNUM_MASK				(BIT_MASK64(14))
#define COP1_ST3_CMD_INSERT_GCSA_COP0_SHIFT					(48)
#define COP1_ST3_CMD_INSERT_GCSA_COP0_MASK					(BIT_MASK64(2))
#define COP1_ST3_CMD_INSERT_GCSA_TYPE_SHIFT					(54)
#define COP1_ST3_CMD_INSERT_GCSA_TYPE_MASK					(BIT_MASK64(2))

//Trim PMD
#define COP1_ST3_CMD_TRIM_PMD_COP0_SHIFT					(48)
#define COP1_ST3_CMD_TRIM_PMD_COP0_MASK						(BIT_MASK64(2))
#define COP1_ST3_CMD_TRIM_PMD_TYPE_SHIFT					(54)
#define COP1_ST3_CMD_TRIM_PMD_TYPE_MASK						(BIT_MASK64(2))

//Trim PTE
#define COP1_ST3_CMD_TRIM_PTE_COP0_SHIFT					(48)
#define COP1_ST3_CMD_TRIM_PTE_COP0_MASK						(BIT_MASK64(2))
#define COP1_ST3_CMD_TRIM_PTE_TYPE_SHIFT					(54)
#define COP1_ST3_CMD_TRIM_PTE_TYPE_MASK						(BIT_MASK64(2))

//Trim Cond
#define COP1_ST3_CMD_TRIM_COND_LOGNUM_SHIFT					(0)
#define COP1_ST3_CMD_TRIM_COND_LOGNUM_MASK					(BIT_MASK64(13))
#define COP1_ST3_CMD_TRIM_COND_COP0_SHIFT					(48)
#define COP1_ST3_CMD_TRIM_COND_COP0_MASK					(BIT_MASK64(2))
#define COP1_ST3_CMD_TRIM_COND_TYPE_SHIFT					(54)
#define COP1_ST3_CMD_TRIM_COND_TYPE_MASK					(BIT_MASK64(2))

//GCPTE
#define COP1_ST3_CMD_GCPTE_GCPTELOGNUM_SHIFT				(0)
#define COP1_ST3_CMD_GCPTE_GCPTELOGNUM_MASK					(BIT_MASK64(13))
#define COP1_ST3_CMD_GCPTE_COP0_SHIFT						(48)
#define COP1_ST3_CMD_GCPTE_COP0_MASK						(BIT_MASK64(2))
#define COP1_ST3_CMD_GCPTE_TYPE_SHIFT						(54)
#define COP1_ST3_CMD_GCPTE_TYPE_MASK						(BIT_MASK64(2))

//Update PMD
#define COP1_ST3_CMD_UPDATE_PMD_PMDLOGSTARTIDX_SHIFT		(0)
#define COP1_ST3_CMD_UPDATE_PMD_PMDLOGSTARTIDX_MASK			(BIT_MASK64(13))
#define COP1_ST3_CMD_UPDATE_PMD_JOBNUM_SHIFT				(16)
#define COP1_ST3_CMD_UPDATE_PMD_JOBNUM_MASK					(BIT_MASK64(14))
#define COP1_ST3_CMD_UPDATE_PMD_COP0_SHIFT					(48)
#define COP1_ST3_CMD_UPDATE_PMD_COP0_MASK					(BIT_MASK64(2))
#define COP1_ST3_CMD_UPDATE_PMD_MODE_SHIFT					(51)
#define COP1_ST3_CMD_UPDATE_PMD_MODE_MASK					(BIT_MASK64(1))
#define COP1_ST3_CMD_UPDATE_PMD_TYPE_SHIFT					(54)
#define COP1_ST3_CMD_UPDATE_PMD_TYPE_MASK					(BIT_MASK64(2))

//ST3C Blk
//Initial Link
#define COP1_ST3C_CMD_INITIAL_LINK_MODE_SHIFT				(0)
#define COP1_ST3C_CMD_INITIAL_LINK_MODE_MASK				(BIT_MASK64(3))
#define COP1_ST3C_CMD_INITIAL_LINK_HEAD_SHIFT				(4)
#define COP1_ST3C_CMD_INITIAL_LINK_HEAD_MASK				(BIT_MASK64(9))
#define COP1_ST3C_CMD_INITIAL_LINK_TAIL_SHIFT				(13)
#define COP1_ST3C_CMD_INITIAL_LINK_TAIL_MASK				(BIT_MASK64(9))
#define COP1_ST3C_CMD_INITIAL_LINK_SIZE_SHIFT				(22)
#define COP1_ST3C_CMD_INITIAL_LINK_SIZE_MASK				(BIT_MASK64(9))

//Lock DT
#define COP1_ST3C_CMD_LOCK_DT_STARTIDX_SHIFT				(0)
#define COP1_ST3C_CMD_LOCK_DT_STARTIDX_MASK					(BIT_MASK64(16))
#define COP1_ST3C_CMD_LOCK_DT_LOCKCNT_SHIFT					(16)
#define COP1_ST3C_CMD_LOCK_DT_LOCKCNT_MASK					(BIT_MASK64(16))

//Unlock DT
#define COP1_ST3C_CMD_UNLOCK_DT_STARTIDX_SHIFT				(0)
#define COP1_ST3C_CMD_UNLOCK_DT_STARTIDX_MASK				(BIT_MASK64(16))
#define COP1_ST3C_CMD_UNLOCK_DT_LOCKCNT_SHIFT				(16)
#define COP1_ST3C_CMD_UNLOCK_DT_LOCKCNT_MASK				(BIT_MASK64(16))
#define COP1_ST3C_CMD_UNLOCK_DT_TOFREECIDPOOL_SHIFT			(48)
#define COP1_ST3C_CMD_UNLOCK_DT_TOFREECIDPOOL_MASK			(BIT_MASK64(1))

//Unlock GCPTE
#define COP1_ST3C_CMD_UNLOCK_GCPTE_STARTIDX_SHIFT			(0)
#define COP1_ST3C_CMD_UNLOCK_GCPTE_STARTIDX_MASK			(BIT_MASK64(16))
#define COP1_ST3C_CMD_UNLOCK_GCPTE_LOCKCNT_SHIFT			(16)
#define COP1_ST3C_CMD_UNLOCK_GCPTE_LOCKCNT_MASK				(BIT_MASK64(16))
#define COP1_ST3C_CMD_UNLOCK_GCPTE_TOFREECIDPOOL_SHIFT		(48)
#define COP1_ST3C_CMD_UNLOCK_GCPTE_TOFREECIDPOOL_MASK		(BIT_MASK64(1))

//Unlock FW
#define COP1_ST3C_CMD_UNLOCK_FW_STARTIDX_SHIFT				(0)
#define COP1_ST3C_CMD_UNLOCK_FW_STARTIDX_MASK				(BIT_MASK64(16))
#define COP1_ST3C_CMD_UNLOCK_FW_LOCKCNT_SHIFT				(16)
#define COP1_ST3C_CMD_UNLOCK_FW_LOCKCNT_MASK				(BIT_MASK64(16))
#define COP1_ST3C_CMD_UNLOCK_FW_TOFREECIDPOOL_SHIFT			(48)
#define COP1_ST3C_CMD_UNLOCK_FW_TOFREECIDPOOL_MASK			(BIT_MASK64(1))

//Allocate PB
#define COP1_ST3C_CMD_ALLOCATE_PB_ALLOCATEPBNUM_SHIFT		(0)
#define COP1_ST3C_CMD_ALLOCATE_PB_ALLOCATEPBNUM_MASK		(BIT_MASK64(9))
#define COP1_ST3C_CMD_ALLOCATE_PB_TIMEOUT_SHIFT				(16)
#define COP1_ST3C_CMD_ALLOCATE_PB_TIMEOUT_MASK				(BIT_MASK64(4))
#define COP1_ST3C_CMD_ALLOCATE_PB_LBID_SHIFT				(20)
#define COP1_ST3C_CMD_ALLOCATE_PB_LBID_MASK					(BIT_MASK64(3))
#define COP1_ST3C_CMD_ALLOCATE_PB_QOB_SHIFT					(23)
#define COP1_ST3C_CMD_ALLOCATE_PB_QOB_MASK					(BIT_MASK64(1))
#define COP1_ST3C_CMD_ALLOCATE_PB_FUA_SHIFT					(24)
#define COP1_ST3C_CMD_ALLOCATE_PB_FUA_MASK					(BIT_MASK64(1))

//Free PB
#define COP1_ST3C_CMD_FREE_PB_FREEPBNUM_SHIFT				(0)
#define COP1_ST3C_CMD_FREE_PB_FREEPBNUM_MASK				(BIT_MASK64(9))
#define COP1_ST3C_CMD_FREE_PB_LBID_SHIFT					(20)
#define COP1_ST3C_CMD_FREE_PB_LBID_MASK						(BIT_MASK64(3))
#define COP1_ST3C_CMD_FREE_PB_QOB_SHIFT						(23)
#define COP1_ST3C_CMD_FREE_PB_QOB_MASK						(BIT_MASK64(1))
#define COP1_ST3C_CMD_FREE_PB_FUA_SHIFT						(24)
#define COP1_ST3C_CMD_FREE_PB_FUA_MASK						(BIT_MASK64(1))

//Remove Table
#define COP1_ST3C_CMD_REMOVE_TABLE_CID_SHIFT				(0)
#define COP1_ST3C_CMD_REMOVE_TABLE_CID_MASK					(BIT_MASK64(9))
#define COP1_ST3C_CMD_REMOVE_TABLE_IGNOREFW_SHIFT			(30)
#define COP1_ST3C_CMD_REMOVE_TABLE_IGNOREFW_MASK			(BIT_MASK64(1))
#define COP1_ST3C_CMD_REMOVE_TABLE_ALL_SHIFT				(31)
#define COP1_ST3C_CMD_REMOVE_TABLE_ALL_MASK					(BIT_MASK64(1))
#define COP1_ST3C_CMD_REMOVE_TABLE_TYPE_SHIFT				(48)
#define COP1_ST3C_CMD_REMOVE_TABLE_TYPE_MASK				(BIT_MASK64(3))
#define COP1_ST3C_CMD_REMOVE_TABLE_TOHEAD_SHIFT				(51)
#define COP1_ST3C_CMD_REMOVE_TABLE_TOHEAD_MASK				(BIT_MASK64(1))

//Set Free CID
//Command0
#define COP1_ST3C_CMD_SET_FREE_CID_CMD0_WPTR_SHIFT			(0)
#define COP1_ST3C_CMD_SET_FREE_CID_CMD0_WPTR_MASK			(BIT_MASK64(31))
//Command1
#define COP1_ST3C_CMD_SET_FREE_CID_CMD1_RPTR_SHIFT			(0)
#define COP1_ST3C_CMD_SET_FREE_CID_CMD1_RPTR_MASK			(BIT_MASK64(31))
//Command2
#define COP1_ST3C_CMD_SET_FREE_CID_CMD2_BUFLEVEL_SHIFT		(0)
#define COP1_ST3C_CMD_SET_FREE_CID_CMD2_BUFLEVEL_MASK		(BIT_MASK64(32))

#endif /* _E13_COP1_CMD_TYPE_H_ */
