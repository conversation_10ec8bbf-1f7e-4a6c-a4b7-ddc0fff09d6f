#include "hal/pic/uart/uart_api.h"
#include "hal/spi/spi_api.h"
#include "nvme_api/nvme/shr_hal_nvme.h"
#include "vuc/VUC_command.h"

#if (BURNER_MODE_EN)
void VUC_IspRom(VUC_OPT_HCMD_PTR_t pCmd)
{
	M_UART(VUC_, "\nVUC_ISP_ROM");
#if (!(PS5017_EN || E21_TODO))
	U32 ulLength = pCmd->vuc_sqcmd.vendor.cacheW.ulLength * BYTE_PER_DW;

	SPIRomProgramFlow(BURNER_VENDOR_BUF_BASE, ulLength);
#endif /* (!(PS5017_EN || E21_TODO)) */
}
#endif /* BURNER_MODE_EN */
