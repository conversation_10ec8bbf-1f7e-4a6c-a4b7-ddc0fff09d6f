#ifndef _SATA_VENDOR_CMD_H_
#define _SATA_VENDOR_CMD_H_

#if (HOST_MODE == SATA)

/*=====================================================================================
                                    Basic Definition
=====================================================================================*/
#define SATA_KINGSTON_VUC_WRITE			(0)
#define SATA_KINGSTON_VUC_READ			(1)
#define SATA_KINGSTON_VUC_FEATURE		(0xF8)
#define SATA_KINGSTON_VUC_WRITE_SECTOR_CNT	    (0x01)
#define SATA_KINGSTON_VUC_READ_SECTOR_CNT	    (0x02)
#define SATA_KINGSTON_VUC_DEVICE_NUM	(0)
#define SATA_KINGSTON_VUC_LBA			(0)
/*
 * ---------------------------------------------------------------------------------------------------
 *  header files
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */

// CK tool
AOM_SATA_2 void SATAVendorSetPIOCmd(void);

// CUSTOMER_KINGSTON
AOM_SATA_2 void SATAVendorKingstonWriteBuf(void);
AOM_SATA_2 void SATAVendorKingstonReadBuf(void);

#endif /* (HOST_MODE == SATA) */

#endif /* _SATA_VENDOR_CMD_H_ */
