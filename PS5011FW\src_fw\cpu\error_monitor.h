/**************************************************************************/
/*                                                                        */
/*           Copyright (c) 2000-2017 by Phison Electronics Corp.          */
/*                                                                        */
/**************************************************************************/

/**************************************************************************/
/*                                                                        */
/*  FILE : error_monitor.h                 PROJECT : PS5011               */
/*                                                                        */
/*                                                                        */
/*  DESCRIPTION                                                           */
/*                                                                        */
/*    This file implements implement error handler		                  */
/*                                                                        */
/*  RELEASE HISTORY                                                       */
/*                                                                        */
/*    DATE              NAME                DESCRIPTION                   */
/*                                                                        */
/*  2017-05-16     Eddie Chiang             Initial Version 1.0           */
/*                                                                        */
/**************************************************************************/

#ifndef _ERROR_MONITOR_H_
#define _ERROR_MONITOR_H_

void CPUErrorHandler(U32 ulErrorType, U32 ulErrorPC, U32 ulErrorDFAR, U32 ulErrorIFAR);

#endif  //_ERROR_MONITOR_H_
