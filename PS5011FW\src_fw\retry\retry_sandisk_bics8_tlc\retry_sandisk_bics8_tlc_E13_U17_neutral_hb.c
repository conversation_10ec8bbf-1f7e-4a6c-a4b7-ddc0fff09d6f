#include "retry/retry.h"
#include "retry/retry_api.h"
#include "retry/retry_hb.h"
#include "retry_sandisk_bics8_tlc_E13_U17_neutral_hb.h"
#include "hal/pic/uart/uart_api.h"
#include "retry/retry_version_api.h" // Retry Version
#include "table/initinfo_vt/initinfo_vt.h" // VT
#include "ftl/ftl_api.h" // FW Timer
#include "hal/fip/fpu.h"
#include "table/sys_block/sys_block_api.h" //RetryInitSystemBlockReadRetryTable

#if (((PS5013_EN) || (PS5017_EN)) && ((FW_CATEGORY_FLASH == FLASH_SANDISK_BICS8_TLC)||(FW_CATEGORY_FLASH == FLASH_SANDISK_BICS6_TLC))  && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER))//BICS8 Add
/*
 * ----------------------------------------------------------------------------
 *   Definitions
 * ----------------------------------------------------------------------------
 */

#define RETRY_HYNIX_V6_SLC_RRT_BASE		(DBUF_RETRY_RR_TABLE)
#define RETRY_HYNIX_V6_TLC_RRT_BASE		(DBUF_RETRY_RR_TABLE + DEF_KB(2))

#define OTP_RR_CNT_SITE                 (8)
#define OTP_RR_REG_CNT_SITE             (16)

#define OTP_DEFAULT_RR_CNT              (50)
#define OTP_DEFAULT_RR_REG_CNT_SLC      (HBIT_RETRY_SLC_FEA_DATA_NUM)
#define OTP_DEFAULT_RR_REG_CNT_TLC      (HBIT_RETRY_TLC_FEA_DATA_NUM)

#define OTP_DEFAULT_SET_NUM             (8)
#define OTP_NORMAL_SEQ                  (0)
#define OTP_INVERSE_SEQ                 (1)
#define OTP_SEQ_CNT_PER_SET             (2)

/*
 * ----------------------------------------------------------------------------
 *   Enum
 * ----------------------------------------------------------------------------
 */

/*
 * ----------------------------------------------------------------------------
 *   Data Types
 * ----------------------------------------------------------------------------
 */

typedef struct {
	U8 ubSeq[OTP_DEFAULT_SET_NUM][OTP_SEQ_CNT_PER_SET][OTP_DEFAULT_RR_CNT * OTP_DEFAULT_RR_REG_CNT_TLC];
} OTPSeq_t;
/*
 * ----------------------------------------------------------------------------
 *   Macros
 * ----------------------------------------------------------------------------
 */

/*
 * ----------------------------------------------------------------------------
 *   Extern Global Variables
 * ----------------------------------------------------------------------------
 */
extern U8 gubHynixtADLDelay;
extern U8 gubHynixtWHRDelay;
/*
 *	Sandisk Bics8
 *  89h: L, 89h: U, 89h: L, 89h: U, 8Ah: M, 8Ah: M, 8Ah: M
 */
const U8 gubRetryReadRetryTableParameterIdxToPageType[HBIT_RETRY_SELECT_NON_SLC_FEA_DATA_NUM] = {
	HB_LRU_LOWER, HB_LRU_UPPER, HB_LRU_LOWER, HB_LRU_UPPER, HB_LRU_MIDDLE, HB_LRU_MIDDLE, HB_LRU_MIDDLE
};
/*
 *	Sandisk Bics8
 *  Transition 0 to 6
 *  P0 | P1 | P2 | P3 | P4 | P5 | P6
 *  LOW| UP | LOW| UP | MID| MID| MID
 */
const U8 gubRetryTransitionToPageType[HBIT_RETRY_SELECT_NON_SLC_FEA_DATA_NUM] = {
	LOWER_PAGE_SEL, UPPER_PAGE_SEL, LOWER_PAGE_SEL, UPPER_PAGE_SEL, MIDDLE_PAGE_SEL, MIDDLE_PAGE_SEL, MIDDLE_PAGE_SEL
};

/*
 * ----------------------------------------------------------------------------
 *   Static Global Variables
 * ----------------------------------------------------------------------------
 */

/*
 * ----------------------------------------------------------------------------
 *   Private Prototypes
 * ----------------------------------------------------------------------------
 */
AOM_RETRY_LOAD_TABLE const U8 gubSandiskBICS8TLCHBRetryData512G[HBIT_RETRY_SANDISK_BICS8_TLC_512G_STEP_NUM * HBIT_RETRY_TLC_FEA_DATA_NUM] = {
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,// 0 for Initial Read
	0xD0, 0xF4, 0xF8, 0xF4, 0xF4, 0xF8, 0xFC,// 1st
	0xD4, 0xEC, 0xE4, 0xD0, 0xE0, 0xD4, 0xC8,// 2nd
	0xD8, 0x10, 0xEC, 0xE4, 0xEC, 0xE4, 0xDC,// 3rd
	0xDC, 0xFC, 0x00, 0xFC, 0xFC, 0xF4, 0xE4,// 4th
	0xE0, 0x08, 0xE8, 0xE0, 0xFC, 0xFC, 0xF4,// 5th
	0xE4, 0xEC, 0xEC, 0xD8, 0xF8, 0xF4, 0xE8,// 6th
	0xE8, 0xF8, 0x04, 0x04, 0xEC, 0xE8, 0xE4,// 7th
	0xEC, 0xE8, 0xD8, 0xD0, 0xF0, 0xE0, 0xCC,// 8th
	0xF0, 0xEC, 0xEC, 0xEC, 0xF8, 0xEC, 0xDC,// 9th
	0xF4, 0x10, 0x00, 0x04, 0xE8, 0xE0, 0xD0,// 10th
	0xF8, 0x18, 0x04, 0x08, 0xF4, 0xE8, 0xE4,// 11th
	0xFC, 0xE8, 0xE8, 0xE4, 0x00, 0xF8, 0xF0,// 12th
	0x00, 0x04, 0x08, 0x08, 0xF4, 0xF4, 0xF4,// 13th
	0x04, 0xF4, 0xFC, 0x00, 0xE4, 0xD8, 0xCC,// 14th
	0x08, 0x14, 0xF8, 0xF4, 0xF0, 0xF0, 0xF0,// 15th
	0x0C, 0x08, 0xF4, 0xE8, 0x04, 0x00, 0xF8,// 16th
	0x10, 0x10, 0x08, 0x08, 0xF0, 0xF0, 0xE4,// 17th
	0x14, 0xF4, 0xF0, 0xF0, 0xF8, 0xF8, 0x00,// 18th
	0x18, 0xF4, 0xE4, 0xDC, 0xEC, 0xE8, 0xD8,// 19th
	0x00, 0xF4, 0xE4, 0xD4, 0xF8, 0xEC, 0xE8,// 20th
	0x00, 0x08, 0xF8, 0xF0, 0xF0, 0xE8, 0xD0,// 21st
	0x00, 0x08, 0xFC, 0xFC, 0x08, 0x04, 0xFC,// 22nd
	0x00, 0xFC, 0xF0, 0xE0, 0xFC, 0xF4, 0xF8,// 23rd
	0x00, 0xF4, 0xDC, 0xD0, 0x08, 0x08, 0x08,// 24th
	0x00, 0x00, 0xFC, 0x04, 0xFC, 0xF8, 0xEC,// 25th
	0x00, 0xFC, 0xE4, 0xE0, 0xE8, 0xDC, 0xCC,// 26th
	0x00, 0xEC, 0xFC, 0xFC, 0xDC, 0xD4, 0xC0,// 27th
	0x00, 0x0C, 0xF0, 0xF0, 0xEC, 0xE0, 0xD4,// 28th
	0x00, 0xFC, 0xEC, 0xE8, 0xFC, 0xF0, 0xE0,// 29th
	0x00, 0x18, 0xFC, 0xFC, 0xFC, 0xFC, 0x04,// 30th
	0x00, 0x04, 0xE4, 0xD8, 0x00, 0x00, 0x08,// 31th
	0x00, 0xF4, 0xE8, 0xE4, 0xF8, 0xF0, 0xF0,// 32th
	0x00, 0xFC, 0xE0, 0xD4, 0xF4, 0xE8, 0xD4,// 33th
	0x00, 0xF4, 0xEC, 0xDC, 0xE8, 0xE0, 0xD8,// 34th
	0x00, 0x04, 0xF4, 0xF8, 0xE4, 0xDC, 0xD0,// 35th
	0x00, 0xEC, 0xF0, 0xE4, 0x04, 0x00, 0x00,// 36th
	0x00, 0x10, 0xF8, 0xFC, 0xFC, 0x00, 0xFC,// 37th
	0x00, 0xFC, 0xF8, 0xE8, 0xF0, 0xEC, 0xE0,// 38th
	0x00, 0x08, 0xEC, 0xE8, 0xF0, 0xEC, 0xE8,// 39th
	0x00, 0xEC, 0xE0, 0xD8, 0xFC, 0xF4, 0xE8,// 40th
	0x00, 0xEC, 0xF4, 0xF8, 0xF4, 0xEC, 0xD8,// 41th
	0x00, 0xFC, 0xE8, 0xD8, 0xF8, 0xF8, 0xF4,// 42th
	0x00, 0x14, 0xF0, 0xEC, 0x0C, 0x08, 0x04,// 43th
	0x00, 0x00, 0xF0, 0xF0, 0x00, 0xFC, 0xFC,// 44th
	0x00, 0xFC, 0xF8, 0xF4, 0xF8, 0xF0, 0xE0,// 45th
	0x00, 0x08, 0x04, 0x04, 0xE8, 0xE0, 0xC8,// 46th
	0x00, 0xF4, 0xF4, 0xE8, 0xE4, 0xD8, 0xC0,// 47th
	0x00, 0xEC, 0xF8, 0xEC, 0xF0, 0xE4, 0xDC,// 48th
	0x00, 0xF0, 0x04, 0x04, 0xE4, 0xDC, 0xC4,// 49th
	0x00, 0xF8, 0xF4, 0xF8, 0x0C, 0x0C, 0x08,// 50th
	0xFC, 0x00, 0xFC, 0xF8, 0xFC, 0xF8, 0xEC,// 51th
	0xF8, 0x00, 0xFC, 0xF4, 0x00, 0xFC, 0xF0,// 52th
	0x00, 0x00, 0x00, 0xF8, 0xFC, 0xFC, 0xFC,// 53th
	0xF0, 0xF8, 0xF8, 0xF4, 0x00, 0xF4, 0xE8,// 54th
	0x04, 0xFC, 0xFC, 0x00, 0xFC, 0xFC, 0xF4,// 55th
	0xE8, 0xF8, 0xFC, 0xF4, 0x00, 0x00, 0xFC,// 56th
	0x08, 0xFC, 0xFC, 0xF0, 0x00, 0xFC, 0xFC,// 57th
	0x10, 0xFC, 0xF8, 0xF0, 0xFC, 0xF8, 0xF8,// 58th
	0x18, 0xF0, 0xF4, 0xEC, 0x00, 0xFC, 0xF8,// 59th
	0x1C, 0xF4, 0xF4, 0xE8, 0xFC, 0xF8, 0xF4,// 60th
	0x00, 0x0C, 0x00, 0xFC, 0xF8, 0xF8, 0xEC,// 61th
	0x00, 0xFC, 0x04, 0x08, 0x04, 0x00, 0xF8,// 62th
	0x00, 0x08, 0xF8, 0xF8, 0x04, 0xFC, 0xF8 // 63th
};

AOM_RETRY_LOAD_TABLE const U8 gubSandiskBICS8TLCHBRetryData1024G[HBIT_RETRY_SANDISK_BICS8_TLC_1024G_STEP_NUM * HBIT_RETRY_TLC_FEA_DATA_NUM] = {
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,// 0 for Initial Read
	0xFC, 0x00, 0xFC, 0xF8, 0xFC, 0xF8, 0xEC,// 1st
	0xF8, 0x00, 0xFC, 0xF4, 0x00, 0xFC, 0xF0,// 2nd
	0x00, 0x00, 0x00, 0xF8, 0xFC, 0xFC, 0xFC,// 3rd
	0xF4, 0x08, 0xFC, 0xFC, 0xF8, 0xF4, 0xE4,// 4th
	0xF0, 0xF8, 0xF8, 0xF4, 0x00, 0xF4, 0xE8,// 5th
	0x04, 0xFC, 0xFC, 0x00, 0xFC, 0xFC, 0xF4,// 6th
	0xEC, 0xF4, 0xFC, 0xFC, 0xF8, 0xF0, 0xE0,// 7th
	0xE8, 0xF8, 0xFC, 0xF4, 0x00, 0x00, 0xFC,// 8th
	0x08, 0xFC, 0xFC, 0xF0, 0x00, 0xFC, 0xFC,// 9th
	0xE0, 0xF8, 0xF8, 0xF0, 0xF8, 0xF8, 0xE8,// 10th
	0x0C, 0x04, 0xF8, 0xF8, 0xFC, 0x00, 0x04,// 11th
	0x10, 0xFC, 0xF8, 0xF0, 0xFC, 0xF8, 0xF8,// 12th
	0x14, 0xF8, 0x00, 0x00, 0xFC, 0xFC, 0xEC,// 13th
	0x18, 0xF0, 0xF4, 0xEC, 0x00, 0xFC, 0xF8,// 14th
	0x1C, 0xF4, 0xF4, 0xE8, 0xFC, 0xF8, 0xF4,// 15th
	0x20, 0xF0, 0xFC, 0xF8, 0xF8, 0xFC, 0xF8,// 16th
	0xDC, 0xF0, 0xF8, 0xFC, 0xF0, 0xEC, 0xDC,// 17th
	0x24, 0xF4, 0xF0, 0xE4, 0xF8, 0xF0, 0xE4,// 18th
	0xD8, 0xF8, 0xF8, 0xEC, 0xF0, 0xE8, 0xD4,// 19th
	0x28, 0x08, 0xFC, 0xF8, 0xF8, 0xF4, 0xEC,// 20th
	0x00, 0xFC, 0xF4, 0xEC, 0xF8, 0xF8, 0xF0,// 21st
	0x00, 0xF8, 0xF0, 0xE0, 0xFC, 0x04, 0x00,// 22nd
	0x00, 0xF8, 0xFC, 0x04, 0xFC, 0x00, 0xF8,// 23rd
	0x00, 0x0C, 0x00, 0xFC, 0xF8, 0xF8, 0xEC,// 24th
	0x00, 0x08, 0xF8, 0xF4, 0x00, 0x00, 0xF8,// 25th
	0x00, 0xF0, 0xF8, 0xF4, 0xF8, 0xF8, 0xF4,// 26th
	0x00, 0xF0, 0xF4, 0xF4, 0xF8, 0xF8, 0xFC,// 27th
	0x00, 0xF0, 0xE8, 0xDC, 0xEC, 0xE4, 0xD0,// 28th
	0x00, 0x0C, 0xFC, 0xFC, 0xF8, 0xF4, 0xF4,// 29th
	0x00, 0x04, 0xF4, 0xF4, 0x00, 0x08, 0x0C,// 30th
	0x00, 0xFC, 0x04, 0x08, 0x04, 0x00, 0xF8,// 31th
	0x00, 0x08, 0xF8, 0xF8, 0x04, 0xFC, 0xF8,// 32th
	0x00, 0xF0, 0xFC, 0xF4, 0xF4, 0xF8, 0xF8,// 33th
	0x00, 0xF4, 0xF0, 0xF0, 0xF4, 0xF4, 0xF0,// 34th
	0x00, 0xFC, 0xF0, 0xEC, 0xF4, 0xF0, 0xEC,// 35th
	0x00, 0xF4, 0xF4, 0xFC, 0xF8, 0xEC, 0xE4,// 36th
	0x00, 0x0C, 0xF8, 0xF8, 0x04, 0x08, 0x0C,// 37th
	0x00, 0x0C, 0xF8, 0xFC, 0xFC, 0x04, 0xF4,// 38th
	0x00, 0xF4, 0x00, 0x00, 0xF4, 0xF0, 0xF4,// 39th
	0x00, 0x0C, 0xF8, 0xF4, 0xEC, 0xDC, 0xD0,// 40th
	0x00, 0x00, 0xF0, 0xEC, 0xFC, 0x00, 0x00,// 41th
	0x00, 0x10, 0xFC, 0xFC, 0xF8, 0x00, 0x00,// 42th
	0x00, 0xF0, 0xE4, 0xD8, 0xFC, 0x04, 0x04,// 43th
	0x00, 0xEC, 0xF4, 0xF4, 0xF8, 0xFC, 0xFC,// 44th
	0x00, 0x10, 0x00, 0xFC, 0xF8, 0xFC, 0x00 // 45th
};

AOM_RETRY_LOAD_TABLE const U8 gubSandiskBICS8SLCHBRetryData512G[HBIT_RETRY_SANDISK_BICS8_SLC_512G_STEP_NUM * HBIT_RETRY_SLC_FEA_DATA_NUM] = {
	0x00,						  // 0 for Initial Read
	0xB0, 0xB8, 0xC0, 0xC8, 0xD0, // 1 ~ 5th
	0xD8, 0xE0, 0xE8, 0xF0, 0xF8, // 6th ~ 10th
	0x00, 0x08, 0x10, 0x18, 0x20, // 11th ~ 15th
	0x28, 0x30, 0x38, 0x40, 0x48, // 16th ~ 20th
	0x50, 0x58, 0x60, 0x68, 0x70, // 21th ~ 26th
	0x78						  // 26th
};

AOM_RETRY_LOAD_TABLE const U8 gubSandiskBICS8SLCHBRetryData1024G[HBIT_RETRY_SANDISK_BICS8_SLC_1024G_STEP_NUM * HBIT_RETRY_SLC_FEA_DATA_NUM] = {
	0x00,						  // 0 for Initial Read
	0xB0, 0xB8, 0xC0, 0xC8, 0xD0, // 1 ~ 5th
	0xD8, 0xE0, 0xE8, 0xF0, 0xF8, // 6th ~ 10th
	0x00, 0x08, 0x10, 0x18, 0x20, // 11th ~ 15th
	0x28, 0x30, 0x38, 0x40, 0x48, // 16th ~ 20th
	0x50, 0x58, 0x60, 0x68, 0x70, // 21th ~ 26th
	0x78						  // 26th
};
/*
 * ----------------------------------------------------------------------------
 *   Private Prototypes
 * ----------------------------------------------------------------------------
 */
INLINE void HBRetryInitRegisterRetryTable(void);
INLINE void HBRetryInitAdjustVthFPU(void);
AOM_RETRY_LOAD_TABLE NO_INLINE void HBRetryInitTable(void);
INLINE U16 SandiskBics8HBRetrySetFeatureFPU(U8 ubStep, U8 ubPageType, U8 ubSLCMode);
INLINE U16 SandiskBics8HBRetryCheckFeatureFPU(U8 ubStep, U8 ubPageType, U8 ubSLCMode);
/*
 * ----------------------------------------------------------------------------
 *   Private
 * ----------------------------------------------------------------------------
 */

void HBRetryInitRegisterRetryTable(void)
{
	gpHBParameterArray[FPL_ADDR_TLC_RULE].pubRetryTable = (gpIDPage->ubRetryGroupCount ? ((U8 *)(SANDISK_TLC_RRT_BASE)) : ((U8 *)(&gubHbitRetryData)));			// TLC
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubTotalRetryCnt = (gpIDPage->ubRetryGroupCount ? (gpIDPage->ubRetryGroupCount + 1) : HBIT_RETRY_SANDISK_BICS8_TLC_512G_STEP_NUM);
	gpHBParameterArray[FPL_ADDR_SLC_RULE].pubRetryTable = (gpIDPage->ubRetryGroupCount ? ((U8 *)(SANDISK_SLC_RRT_BASE)) : ((U8 *)(&gubSlcHbitRetryData)));		// SLC
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubTotalRetryCnt = (gpIDPage->ubD1RetryGroupCount ? (gpIDPage->ubD1RetryGroupCount + 1) : HBIT_RETRY_SANDISK_BICS8_SLC_512G_STEP_NUM);


	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubCurrentStep = HB_READ_START_STEP;
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubLastSuccessIndex = 0;
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubRetryParameterSize = (gpIDPage->ubRetryParamCount ? gpIDPage->ubRetryParamCount : HBIT_RETRY_TLC_FEA_DATA_NUM);
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubParameterNumPerFPU = (gpIDPage->ubRetryParamCount ? gpIDPage->ubRetryParamCount : HBIT_RETRY_TLC_FEA_DATA_NUM); // Hynix only need 1 SetFeature CMD to mod FA
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubCurrentStep = HB_READ_START_STEP;
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubLastSuccessIndex = 0;
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubRetryParameterSize = (gpIDPage->ubD1RetryParamCount ? gpIDPage->ubD1RetryParamCount : HBIT_RETRY_SLC_FEA_DATA_NUM);
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubParameterNumPerFPU = (gpIDPage->ubD1RetryParamCount ? gpIDPage->ubD1RetryParamCount : HBIT_RETRY_SLC_FEA_DATA_NUM);
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubScratchTableIdx = INVALID_RETRY_TABLE_INDEX;
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubScratchTableIdx = INVALID_RETRY_TABLE_INDEX;


	//zerio debug

	// 	U8 ubFeatureData[HBIT_RETRY_TLC_FEA_DATA_NUM] = {0};
	// 	FPL_HBIT_RETRY_PARAM_STRUCT_PTR pParam = &(gpHBParameterArray[0]);
	// 	U8 ubValidParameterSize = pParam->ubParameterNumPerFPU;
	// 	if (ubStep < pParam->ubTotalRetryCnt) { //Prevent Access Array Over flow
	// 		U16 uwTableOffset = ubStep * pParam->ubRetryParameterSize;
	// 		// Copy set feature from HB retry table
	// 		memcpy((void *)ubFeatureData, (void *)&pParam->pubRetryTable[uwTableOffset], ubValidParameterSize);
	// 	}
	//
	// 	for (U8 i = 0; i < ubValidParameterSize; i++) {
	// 		/* code */
	// 		UartPrintf("%x ", ubFeatureData[i]);
	// 	}
	// 	for (U16 i = 0; i < 322; i += 7) {
	//
	// 		// Copy set feature from HB retry table
	// 		memcpy((void *)ubFeatureData, (void *)&pParam->pubRetryTable[i], ubValidParameterSize);
	//
	// 		for (U8 i = 0; i < ubValidParameterSize; i++) {
	// 			/* code */
	// 			UartPrintf("%x ", ubFeatureData[i]);
	// 		}
	// 		UartPrintf("\n\r");
	// 	}
}

void HBRetryInitAdjustVthFPU(void)
{
	// Register set feature fpu
	gFpuEntryList.fpu_set_feature[0] = FPU_ADR_GEN;
	gFpuEntryList.fpu_set_feature[1] = FPU_CMD(0x78);
	gFpuEntryList.fpu_set_feature[2] = FPU_ADR(3);
	gFpuEntryList.fpu_set_feature[3] = FPU_DLY(0x10);//
	gFpuEntryList.fpu_set_feature[4] = FPU_CMD(0xEF);//BICS8 Set Feature CMD

	gFpuEntryList.fpu_set_feature[27] = FPU_END;

	// Register read and compare feature data fpu
	//	gFpuEntryList.fpu_read_and_compare_feature_data[0] = FPU_ADR_GEN;
	//	gFpuEntryList.fpu_read_and_compare_feature_data[1] = FPU_CMD(0x78);
	//	gFpuEntryList.fpu_read_and_compare_feature_data[2] = FPU_ADR(3);
	//	gFpuEntryList.fpu_read_and_compare_feature_data[3] = FPU_DLY(0x10);
	//	gFpuEntryList.fpu_read_and_compare_feature_data[4] = FPU_CMD(0xEE); // BICS8 Get feature cmd
	//	gFpuEntryList.fpu_read_and_compare_feature_data[5] = FPU_DLY(0x10);
	gFpuEntryList.fpu_read_and_compare_feature_data[0] = FPU_CMD(0xEE); // BICS8 Get feature cmd
	gFpuEntryList.fpu_read_and_compare_feature_data[1] = FPU_DLY(0x10);
}

U16 SandiskBics8HBRetrySetFeatureFPU(U8 ubStep, U8 ubPageType, U8 ubSLCMode)
{
	FPL_HBIT_RETRY_PARAM_STRUCT_PTR pParam = &(gpHBParameterArray[ubSLCMode]);
	U8 ubValidParameterSize = pParam->ubParameterNumPerFPU;
	U16 uwFPUPtr;
	U16 *puwFPU;
	U8 ubFeatureData[HBIT_RETRY_TLC_FEA_DATA_NUM] = {0};
	U8 ubFPUIdx;
	//// PIO DBG  ////
	//	REG32 *pFlaReg0 = R32_FCTL_CH[0];
	//	FlaCEControl(0, 0, ENABLE);
	//	pFlaReg0[R32_FCTL_PIO_CMD] = 0xCD;
	//	FlaCEControl(0, 0, DISABLE);
	//// PIO DBG  ////

	if (ubStep < pParam->ubTotalRetryCnt) { //Prevent Access Array Over flow
		U16 uwTableOffset = ubStep * pParam->ubRetryParameterSize;
		// Copy set feature from HB retry table
		memcpy((void *)ubFeatureData, (void *)&pParam->pubRetryTable[uwTableOffset], ubValidParameterSize);
#if 0//zerio debug
		UartPrintf("\n");
		if (ubSLCMode) {
			UartPrintf("\n Step:%u, TableOffset:%u, ubSLCMode:%u", ubStep, uwTableOffset, ubSLCMode);
		}
		else {
			UartPrintf("\n Step:%u, TableOffset:%u, PageType:%u", ubStep, uwTableOffset, ubPageType);

		}
		UartPrintf("\n Group %u:", ubStep);
		for (U8 i = 0; i < ubValidParameterSize; i++) {
			/* code */
			UartPrintf("%x ", ubFeatureData[i]);

		}
#endif
	}
#if (RETRY_HB_FEEDBACK_CACULATE_READ_LEVEL_TO_LRU)
	// Check if CurrentStep is "SB Pass Read Level", ubTotalRetryCnt = RR_Table + Scratch_Table
	else {
		U8 ubReadLevelTableIdx = ubStep - gpHBParameterArray[FPL_ADDR_TLC_RULE].ubTotalRetryCnt; // Get Node Idx
		RETRY_HB_LRU_FEEDBACK_READ_LEVEL_TABLE_STRUCT_PTR pFeedBackReadLevelTable = M_RETRY_GET_FEEDBACK_CACULATE_READ_LEVEL_TABLE(gHBParamMgr.ubSLCMode, gHBParamMgr.ubGlobalDie, gHBParamMgr.ubPageType);
		U8 ubValidIdx = 0;
		for (U8 ubi = 0; ubi < ubValidParameterSize; ubi++) {
			if (ubPageType == M_RETRY_GET_PAGE_TYPE_BY_READ_RETRY_TABLE_PARAMETER_IDX(ubi)) {
				ubFeatureData[ubi] = pFeedBackReadLevelTable[ubReadLevelTableIdx].ubSBPassReadLevelTable[ubValidIdx]; // Same Page Type Read Level Can't Cross Two Round(89/8A).
				++ubValidIdx;
			}
		}
	}
#endif /* (RETRY_HB_FEEDBACK_CACULATE_READ_LEVEL_TO_LRU) */


	// Get FPU PTR
	uwFPUPtr = FPU_PTR_OFFSET(fpu_set_feature);
	puwFPU = (U16 *)(IRAM_BASE + uwFPUPtr);

	// Set parameter cmd, already fill FPU[0] in init state
	//puwFPU[0] = FPU_CMD(0x36);
	// reset fpu entry
	//	puwFPU[0] = FPU_ADR_GEN;
	//	puwFPU[1] = FPU_CMD(0x78);
	//	puwFPU[2] = FPU_ADR(3);
	//	puwFPU[3] = FPU_DLY(0x10);
	//	puwFPU[4] = FPU_CMD(0xEF); // Sandisk set Vth by setting feature

	puwFPU += 5;
	//UartPrintf("\n\r set ubFeatureData[0] = %x", ubFeatureData[0]);
	ubFPUIdx = 0;
	if (ubSLCMode) {
		//BICS8 SLC: 14h or 8Bh
		puwFPU[ubFPUIdx++] = FPU_ADR_1B(0x8B);
		// Delay (tADL)
		puwFPU[ubFPUIdx++] = FPU_DLY(gubHynixtADLDelay);
		// Set feature data
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(ubFeatureData[0]);
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(0x00);
	}
	else if (ubPageType == HB_LRU_LOWER) {
		//BICS8 TLC: 12h or 89h for lower/upper page & 13h or 8Ah for middle page
		puwFPU[ubFPUIdx++] = FPU_ADR_1B(0x89);
		// Delay (tADL)
		puwFPU[ubFPUIdx++] = FPU_DLY(gubHynixtADLDelay);
		// Set feature data
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(ubFeatureData[0]);

		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(ubFeatureData[2]);
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(0x00);
	}
	else if (ubPageType == HB_LRU_MIDDLE) {
		//BICS8 TLC: 12h or 89h for lower/upper page & 13h or 8Ah for middle page
		puwFPU[ubFPUIdx++] = FPU_ADR_1B(0x8A);
		// Delay (tADL)
		puwFPU[ubFPUIdx++] = FPU_DLY(gubHynixtADLDelay);
		// Set feature data
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(ubFeatureData[4]);
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(ubFeatureData[5]);
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(ubFeatureData[6]);
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(0x00);
	}
	else if (ubPageType == HB_LRU_UPPER) {
		//BICS8 TLC: 12h or 89h for lower/upper page & 13h or 8Ah for middle page
		puwFPU[ubFPUIdx++] = FPU_ADR_1B(0x89);
		// Delay (tADL)
		puwFPU[ubFPUIdx++] = FPU_DLY(gubHynixtADLDelay);
		// Set feature data
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(ubFeatureData[1]);
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_W_1B(ubFeatureData[3]);
	}

	// FPU end
	puwFPU[ubFPUIdx++] = FPU_END;
	return uwFPUPtr;
}


U16 SandiskBics8HBRetryCheckFeatureFPU(U8 ubStep, U8 ubPageType, U8 ubSLCMode)
{
	FPL_HBIT_RETRY_PARAM_STRUCT_PTR pParam =  &(gpHBParameterArray[ubSLCMode]);
	U8 ubValidParameterSize = pParam->ubParameterNumPerFPU;
	U16 uwFPUPtr;
	U16 *puwFPU;
	U8 ubFeatureData[HBIT_RETRY_TLC_FEA_DATA_NUM] = {0};
	U8 ubFPUIdx;
	//// PIO DBG  ////
	//	REG32 *pFlaReg1 = R32_FCTL_CH[0];
	//	FlaCEControl(0, 0, ENABLE);
	//	pFlaReg1[R32_FCTL_PIO_CMD] = 0xCE;
	//	FlaCEControl(0, 0, DISABLE);
	//// PIO DBG  ////

	if (ubStep < pParam->ubTotalRetryCnt) { //Prevent Access Array Over flow
		U16 uwTableOffset = ubStep * pParam->ubRetryParameterSize;
		// Copy set feature from HB retry table
		memcpy((void *)ubFeatureData, (void *)&pParam->pubRetryTable[uwTableOffset], ubValidParameterSize);
	}
#if (RETRY_HB_FEEDBACK_CACULATE_READ_LEVEL_TO_LRU)
	// Check if CurrentStep is "SB Pass Read Level", ubTotalRetryCnt = RR_Table + Scratch_Table
	else {
		U8 ubReadLevelTableIdx = ubStep - gpHBParameterArray[FPL_ADDR_TLC_RULE].ubTotalRetryCnt; // Get Node Idx
		RETRY_HB_LRU_FEEDBACK_READ_LEVEL_TABLE_STRUCT_PTR pFeedBackReadLevelTable = M_RETRY_GET_FEEDBACK_CACULATE_READ_LEVEL_TABLE(gHBParamMgr.ubSLCMode, gHBParamMgr.ubGlobalDie, gHBParamMgr.ubPageType);
		U8 ubValidIdx = 0;
		for (U8 ubi = 0; ubi < ubValidParameterSize; ubi++) {
			if (ubPageType == M_RETRY_GET_PAGE_TYPE_BY_READ_RETRY_TABLE_PARAMETER_IDX(ubi)) {
				ubFeatureData[ubi] = pFeedBackReadLevelTable[ubReadLevelTableIdx].ubSBPassReadLevelTable[ubValidIdx]; // Same Page Type Read Level Can't Cross Two Round(89/8A).
				++ubValidIdx;
			}
		}
	}
#endif /* (RETRY_HB_FEEDBACK_CACULATE_READ_LEVEL_TO_LRU) */

	// Get FPU PTR
	uwFPUPtr = FPU_PTR_OFFSET(fpu_read_and_compare_feature_data);
	puwFPU = (U16 *)(IRAM_BASE + uwFPUPtr);

	// Get parameter cmd, already fill FPU[0] in init state
	//puwFPU[0] = FPU_CMD(0x37);
	// reset fpu entry
	//	puwFPU[0] = FPU_ADR_GEN;
	//	puwFPU[1] = FPU_CMD(0x78);
	//	puwFPU[2] = FPU_ADR(3);
	//	puwFPU[3] = FPU_DLY(0x10);
	//	puwFPU[4] = FPU_CMD(0xEE);
	//	puwFPU[5] = FPU_DLY(0x10);
	puwFPU += 2;

	if ((uwFPUPtr & 0x3) == 0x2) {
		// For FPU_DAT_R_CMP, make the FPU sequence align 4B
		puwFPU++;
	}
	//UartPrintf("\n\r get ubFeatureData[0] = %x", ubFeatureData[0]);

	ubFPUIdx = 0;
	if (ubSLCMode) {
		//BICS8 SLC: 14h or 8Bh
		puwFPU[ubFPUIdx++] = FPU_ADR_1B(0x8B);
		// Delay
		puwFPU[ubFPUIdx++] = FPU_CMD_DQS(0X70);
		puwFPU[ubFPUIdx++] = FPU_DLY(0X40);
		puwFPU[ubFPUIdx++] = FPU_POL_MASK(0xE0);
		puwFPU[ubFPUIdx++] = FPU_CMD(0x00);
		puwFPU[ubFPUIdx++] = FPU_DLY(gubHynixtADLDelay);
		//puwFPU[ubFPUIdx++] = FPU_CMD(0x70);
		//puwFPU[ubFPUIdx++] = FPU_POL_MASK(0xE0);
		//puwFPU[ubFPUIdx++] = FPU_DLY(0xFF);
		// Get feature data
		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(ubFeatureData[0]);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);
	}
	else if (ubPageType == HB_LRU_LOWER) {
		//BICS8 TLC: 12h or 89h for lower/upper page & 13h or 8Ah for middle page
		puwFPU[ubFPUIdx++] = FPU_ADR_1B(0x89);
		// Delay
		puwFPU[ubFPUIdx++] = FPU_CMD_DQS(0X70);
		puwFPU[ubFPUIdx++] = FPU_DLY(0X40);
		puwFPU[ubFPUIdx++] = FPU_POL_MASK(0xE0);
		puwFPU[ubFPUIdx++] = FPU_CMD(0x00);
		puwFPU[ubFPUIdx++] = FPU_DLY(gubHynixtADLDelay);
		//puwFPU[ubFPUIdx++] = FPU_DLY(0xFF);
		// Get feature data
		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(ubFeatureData[0]);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(ubFeatureData[2]);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);
	}
	else if (ubPageType == HB_LRU_MIDDLE) {
		//BICS8 TLC: 12h or 89h for lower/upper page & 13h or 8Ah for middle page
		puwFPU[ubFPUIdx++] = FPU_ADR_1B(0x8A);
		// FPU Delay
		puwFPU[ubFPUIdx++] = FPU_CMD_DQS(0X70);
		puwFPU[ubFPUIdx++] = FPU_DLY(0X40);
		puwFPU[ubFPUIdx++] = FPU_POL_MASK(0xE0);
		puwFPU[ubFPUIdx++] = FPU_CMD(0x00);
		puwFPU[ubFPUIdx++] = FPU_DLY(gubHynixtADLDelay);
		// Get feature data
		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(ubFeatureData[4]);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(ubFeatureData[5]);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(ubFeatureData[6]);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);
	}
	else if (ubPageType == HB_LRU_UPPER) {
		//BICS8 TLC: 12h or 89h for lower/upper page & 13h or 8Ah for middle page
		puwFPU[ubFPUIdx++] = FPU_ADR_1B(0x89);
		// Delay
		puwFPU[ubFPUIdx++] = FPU_CMD_DQS(0X70);
		puwFPU[ubFPUIdx++] = FPU_DLY(0X40);
		puwFPU[ubFPUIdx++] = FPU_POL_MASK(0xE0);
		puwFPU[ubFPUIdx++] = FPU_CMD(0x00);
		puwFPU[ubFPUIdx++] = FPU_DLY(gubHynixtADLDelay);
		// Get feature data
		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(ubFeatureData[1]);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(0x00);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);

		puwFPU[ubFPUIdx++] = FPU_DAT_R_CMP(ubFeatureData[3]);
		puwFPU[ubFPUIdx++] = FPU_DAT_R_MASK(0xFF);
	}
	// FPU end
	puwFPU[ubFPUIdx++] = FPU_END;
	//zerio debug
#if(0)
	ubFPUIdx = 0;
	UartPrintf("\n Set feature FPU Sequence: ");
	while (puwFPU[ubFPUIdx] != NULL) {
		/* code */
		UartPrintf("%x ", puwFPU[ubFPUIdx++]);

	}
#endif

	return uwFPUPtr;
}

/*
 * ------------------------------------
 * 		Over Wirte Weak Function
 * ------------------------------------
 */
void HBRetryInitParameter(void)
{
	//zerio debug
	UartPrintf("\r\n[HB MakerCode %x, Process Type%x]", gpOtherInfo->ubMakerCode, gpOtherInfo->ubProcess);
	M_FW_ASSERT(ASSERT_RETRY_0x0872, (ID_SANDISK == gpOtherInfo->ubMakerCode) && ((RETRY_SANDISK_FLASH_PROCESS_BICS5_512G == gpOtherInfo->ubProcess) || (RETRY_SANDISK_FLASH_PROCESS_BICS5_1024G == gpOtherInfo->ubProcess)));
	HBRetryInitRegisterRetryTable();
	HBRetryInitAdjustVthFPU();
	if (gpIDPage->ubRetryGroupCount) {
		//Do not do HBRetryInitTable();
	}
	else {
		HBRetryInitTable();
	}
}

void HBRetryInitTable(void)
{
	U8 ubHBRetryDataStepNum = HBIT_RETRY_SANDISK_BICS8_TLC_512G_STEP_NUM;
	U8 ubSLCHBRetryDataStepNum = HBIT_RETRY_SANDISK_BICS8_SLC_512G_STEP_NUM;

	switch (gpOtherInfo->ubMakerCode) {
	case ID_SANDISK:
		switch (gpOtherInfo->ubProcess) {
		case RETRY_SANDISK_FLASH_PROCESS_BICS5_512G:
			memcpy((&gubHbitRetryData), (&gubSandiskBICS8TLCHBRetryData512G), (HBIT_RETRY_SANDISK_BICS8_TLC_512G_STEP_NUM * HBIT_RETRY_TLC_FEA_DATA_NUM));
			memcpy((&gubSlcHbitRetryData), (&gubSandiskBICS8SLCHBRetryData512G), (HBIT_RETRY_SANDISK_BICS8_SLC_512G_STEP_NUM * HBIT_RETRY_SLC_FEA_DATA_NUM));
			ubHBRetryDataStepNum = HBIT_RETRY_SANDISK_BICS8_TLC_512G_STEP_NUM;
			ubSLCHBRetryDataStepNum = HBIT_RETRY_SANDISK_BICS8_SLC_512G_STEP_NUM;
			break;
		case RETRY_SANDISK_FLASH_PROCESS_BICS5_1024G:
			memcpy((&gubHbitRetryData), (&gubSandiskBICS8TLCHBRetryData1024G), HBIT_RETRY_SANDISK_BICS8_TLC_1024G_STEP_NUM * HBIT_RETRY_TLC_FEA_DATA_NUM);
			memcpy((&gubSlcHbitRetryData), (&gubSandiskBICS8SLCHBRetryData1024G), (HBIT_RETRY_SANDISK_BICS8_SLC_1024G_STEP_NUM * HBIT_RETRY_SLC_FEA_DATA_NUM));
			ubHBRetryDataStepNum = HBIT_RETRY_SANDISK_BICS8_TLC_512G_STEP_NUM;
			ubSLCHBRetryDataStepNum = HBIT_RETRY_SANDISK_BICS8_SLC_1024G_STEP_NUM;
			break;

		default:
			M_FW_ASSERT(ASSERT_RETRY_0x0870, FALSE);
			break;
		}

		break;

	default:
		M_FW_ASSERT(ASSERT_RETRY_0x0871, FALSE);
		break;
	}
	//zerio debug
	// 	UartPrintf("\n\r gubHbitRetryData = %l,gubSandiskBICS8TLCHBRetryData1024G = %l, HBIT_RETRY_SANDISK_BICS8_TLC_1024G_STEP_NUM * HBIT_RETRY_TLC_FEA_DATA_NUM = %l", sizeof(gubHbitRetryData), sizeof(gubSandiskBICS8TLCHBRetryData1024G), HBIT_RETRY_SANDISK_BICS8_TLC_1024G_STEP_NUM * HBIT_RETRY_TLC_FEA_DATA_NUM);
	// 	UartPrintf("\n\r pubRetryTable address = %x", gubHbitRetryData);
	// 	UartPrintf("\n\r gubSandiskBICS8TLCHBRetryData1024G address = %x", gubSandiskBICS8TLCHBRetryData1024G);
	gpHBParameterArray[FPL_ADDR_TLC_RULE].pubRetryTable = (U8 *)(&gubHbitRetryData);
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubTotalRetryCnt = ubHBRetryDataStepNum;
	gpHBParameterArray[FPL_ADDR_SLC_RULE].pubRetryTable = (U8 *)(&gubSlcHbitRetryData);
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubTotalRetryCnt = ubSLCHBRetryDataStepNum;
}

void HBRetryPreconditionSetFeaturePass_Callback(U8 ubMTIdx)
{
	gHBParamMgr.ubSetFeatureDoneMTCnt++;

	if (gHBParamMgr.ubSetFeatureDoneMTCnt == gHBParamMgr.ubSetFeatureToDoMTCnt) { // All set feature MT done
		gHBParamMgr.ubGetFeatureDoneMTCnt = 0;
		if (NCS_V2_EN && gSystmAreaFWSettingFromInfoBlk.Init.Flag.btNCS2En) {
			gHBTask.ubState = RETRY_HB_STATE_SEARCH_PASS_STEP_READ;
		}
		else {
			gHBTask.ubState = RETRY_HB_STATE_CHECK_PRECONDITION; // Check set feature result
		}
	}
	else {
		gHBTask.ubState = RETRY_HB_STATE_PRECONDITION; // Execute remain set feature MT
	}
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_HB_RETRY;
}

void HBRetryPostconditionSetFeaturePass_Callback(U8 ubMTIdx)
{
	gHBParamMgr.ubSetFeatureDoneMTCnt++;

	if (gHBParamMgr.ubSetFeatureDoneMTCnt == gHBParamMgr.ubSetFeatureToDoMTCnt) {
		gHBParamMgr.ubGetFeatureDoneMTCnt = 0;
		if (NCS_V2_EN && gSystmAreaFWSettingFromInfoBlk.Init.Flag.btNCS2En) {
			gHBTask.ubState = RETRY_HB_STATE_POSTCONDITION_DONE_RESET_FLASH; // Check set feature result
		}
		else {
			gHBTask.ubState = RETRY_HB_STATE_CHECK_POSTCONDITION; // Check set feature result
		}
	}
	else {
		gHBTask.ubState = RETRY_HB_STATE_POSTCONDITION;
	}
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_HB_RETRY;
}

U16 HBRetrySelectResetCMDFPU(void)
{
	return FPU_PTR_OFFSET(fpu_entry_reset_ff);
}

U16 HBRetryPreconditionSetFeatureFPU(void)
{
	return SandiskBics8HBRetrySetFeatureFPU(gHBParamMgr.pParam->ubCurrentStep, gHBParamMgr.ubPageType, gHBParamMgr.ubSLCMode);
}

U16 HBRetryPreconditionCheckFeatureFPU(void)
{
	return SandiskBics8HBRetryCheckFeatureFPU(gHBParamMgr.pParam->ubCurrentStep, gHBParamMgr.ubPageType, gHBParamMgr.ubSLCMode);
}

U16 HBRetryPostconditionSetFeatureFPU(void)
{
	HB_LRU_TABLE_STRUCT_PTR pLRUTable = M_RETRY_GET_LRU_TABLE(gHBParamMgr.ubSLCMode, gHBParamMgr.ubGlobalDie, gHBParamMgr.ubPageType);
	return SandiskBics8HBRetrySetFeatureFPU(pLRUTable[0].param.ubTableIdx, gHBParamMgr.ubPageType, gHBParamMgr.ubSLCMode);
}

U16 HBRetryPostconditionCheckFeatureFPU(void)
{
	HB_LRU_TABLE_STRUCT_PTR pLRUTable = M_RETRY_GET_LRU_TABLE(gHBParamMgr.ubSLCMode, gHBParamMgr.ubGlobalDie, gHBParamMgr.ubPageType);
	return SandiskBics8HBRetryCheckFeatureFPU(pLRUTable[0].param.ubTableIdx, gHBParamMgr.ubPageType, gHBParamMgr.ubSLCMode);
}

#endif /* ((PS5013_EN) && (FLASH_HYNIXV6_TLC == FW_CATEGORY_FLASH) && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER)) */
