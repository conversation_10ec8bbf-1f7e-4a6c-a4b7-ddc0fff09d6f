/**************************************************************************/
/*                                                                        */
/*           Copyright (c) 2000-2017 by Phison Electronics Corp.          */
/*                                                                        */
/**************************************************************************/

/**************************************************************************/
/*                                                                        */
/*  HAL UART DEFINITION                                    RELEASE        */
/*                                                                        */
/*    shr_hal_d2h.h                                      ARM Compiler     */
/*                                                           X.X          */
/*  AUTHOR                                                                */
/*                                                                        */
/*                                                                        */
/*  DESCRIPTION                                                           */
/*                                                                        */
/*    This file contains hal doorbell definitions for this system.        */
/*                                                                        */
/*                                                                        */
/*  RELEASE HISTORY                                                       */
/*                                                                        */
/*    DATE              NAME                      DESCRIPTION             */
/*                                                                        */
/*  26-04-2017                                                            */
/*                                                                        */
/**************************************************************************/
#ifndef _SHR_HAL_D2H_H_
#define _SHR_HAL_D2H_H_

// goodid
#if 0 //merge@@
#include "misc/shr_datatype.h"
#include "misc/shr_def.h"
#else
#include "common/typedef.h"
#include "common/symbol.h"
#include "common/math_op.h"
#include "nvme_api/d2h/shr_hal_d2h_reg.h"
#endif
// goodid
// #include "misc\shr_config.h"
// goodid
//#include "hal\nvme\shr_hal_nvme.h"
#include "nvme_api/nvme/shr_hal_nvme.h"
// goodid
//#include "nvme_api\bmu\shr_hal_bmu_types.h"


#ifdef EXTERN
#undef EXTERN
#endif

#define D2H_AES_Z1_HOSTLEN_ALIGN      0x220ULL
#define D2H_AES_Z1_LOCALLEN_ALIGN     0x200ULL
#define D2H_AES_Z1_HOSTADDR_ALIGN     0x10ULL

#define D2H_AES_Z2_HOSTLEN_ALIGN      0x202ULL
#define D2H_AES_Z2_LOCALLEN_ALIGN     0x200ULL
#define D2H_AES_Z2_HOSTADDR_ALIGN     0x10ULL
#define D2H_REMP_HOSTADDR_ALIGN       0x10ULL
#define D2H_RESERVED_INTERNAL_USED    512   // must larger than 512 bytes (512 * 4  = 2048 bytes)
#define MAX_HMB_ENTRY_FROM_HOST       0x20
#define MAX_HMB_ENTEY_SUPPORT         16
#define REMAP_MAP_INDEX               MAX_HMB_ENTEY_SUPPORT + 1

#define BMU_ALLOCATE_UNIT             0x1000UL
#define D2H_Z1_MAX_SIZE               0x96969600UL //5011  2.5GB device size
#define D2H_Z2_MAX_SIZE               0x01FE0000UL //5011  32MB devuce size
#define D2H_REMAP_MAX_SIZE            0x01FE0000UL //5011  32MB devuce size
#define HMB_LIGHT                     0x0

#define HMB_RANGE_START_LBA_OFFSET		(64)
#define HMB_RANGE_START_LBA_BIT_NUM 	(64)

#define M_D2H_INT_EN_ALL()			do {\
	CPUARMSetCriticalRegisterBit((R32_D2H + R32_D2H_CR_ERR_INFO), (D2H_INT_EN_MASK << D2H_INT_EN_SHIFT));\
} while(0)
#define M_D2H_INT_DISABLE_ALL()		do {\
	CPUARMClearCriticalRegisterBit((R32_D2H + R32_D2H_CR_ERR_INFO), (D2H_INT_EN_MASK << D2H_INT_EN_SHIFT));\
} while (0)
#define M_D2H_GET_INT_FLAG()		(R32_D2H[R32_D2H_CR_ERR_INFO] & (D2H_INT_FLAG_MASK << D2H_INT_FLAG_SHIFT))
#define M_D2H_CHECK_D2H_IDLE()		(R32_D2H[D2HL_CR_BUS_STATUS_0x214] & D2H_IDLE_ST)
#define M_D2H_CLEAR_INT_FLAG()		do {\
	CPUARMSetCriticalRegisterBit((R32_D2H + R32_D2H_CR_ERR_INFO), (D2H_INT_FLAG_MASK << D2H_INT_FLAG_SHIFT));\
} while(0)

// HW data in zone2 after first bank may offset MAX 16 bytes, preserve 16 byte for every bank (include bank1)
#define D2H_HW_RESERVED_LENGTH 16

enum HMB_TYPE {
	D2H_EMPTY = 0,
	D2H_Z1 = 1,       // zone 1
	D2H_Z2,           // zone 2
	D2H_REMAP,        // remap
	D2H_TYPE_MAX,
};

enum d2h_mem_access {
	D2H_DEVICE, // mem access from device side
	D2H_HOST,   // mem access from host side
};

typedef struct {
	U64	startAddress; // host memory address
	U64 endAddress;
	U64	freeBytes;    // in bytes
} HMB_LIST_ENTRY;

typedef struct {
	U32 size;
	HMB_LIST_ENTRY entry[MAX_HMB_ENTRY_FROM_HOST];
} HMB_LIST;

typedef struct {
	U32 type;         // HMB_TYPE
	U32	index;        // index to HMB_LIST's index
	U64	address;      // allocated start address
	U64	length;       // in bytes
} HMB_MEMORY_MAP_ENTRY;

typedef struct {
	S32 freeEntry;
	HMB_MEMORY_MAP_ENTRY entry[REMAP_MAP_INDEX];
} HMB_MEMORY_MAP;

typedef struct {
	U32 operation;
	U32 dataFormat;
	U32 type;
	U32 memAllocCat;
} HMB_TEST_PARAMETER;

typedef struct {
	volatile U32 status;
	volatile U32 address;
	volatile U32 count;
} HMB_TEST_INTERRUPT_INFO;


typedef struct {
	//U32 reservedBuffer[D2H_RESERVED_INTERNAL_USED]; // for data copy and compare, must be multiple of 512 bytes
	//HMB_TEST_PARAMETER testParam;
	HMB_TEST_INTERRUPT_INFO intInfo;
} HMB_GLOBAL_DATA;

typedef struct {
	HMB_LIST *pFreeMemList;
	HMB_MEMORY_MAP *pD2HMemMap;
	HMB_GLOBAL_DATA *data;
} HMB_HANDLE;

/* public API */
AOM_HMB HMB_HANDLE *init_HMB(void);
AOM_HMB U32 HMB_malloc(HMB_HANDLE *handle, U32 type, U64 expected_length);
AOM_NRW_2 void HMB_free(HMB_HANDLE *handle, U32 type);
AOM_NRW_2 U64 HMB_get_free_count(HMB_HANDLE *handle, U32 type);
AOM_HMB U64 HMB_get_allocate_size(HMB_HANDLE *handle, U32 type);
AOM_NRW_2 void d2h_Int_set();
AOM_NRW_2 void d2h_auto_resp_set(U32 setting);


/* internal function  */
AOM_HMB void d2h_Backup_Reg_for_RMP(U32 *pulRmpDataLength, U32 *pulDataAddr, U64 *puoHostAddr);
AOM_HMB void d2h_Restore_Reg_for_RMP(U32 *pulRmpDataLength, U32 *pulDataAddr, U64 *puoHostAddr);
AOM_HMB void d2h_AES_PATH_init(U8 bank_id, U32 length, U32 localAddr, U64 hostAddr);
AOM_HMB void d2h_RMP_PATH_init(U32 length, U32 localAddr, U64 hostAddr);
AOM_HMB void d2h_RMP_PATH_get(U32 *length, U32 *localAddr, U64 *hostAddr);
AOM_HMB void d2h_AES_PATH_get(U8 bank_id, U32 *length, U32 *localAddr, U64 *hostAddr);
AOM_HMB U32 d2h_get_start_address(U32 type);
AOM_HMB U32 d2h_align_unit(U32 type, U32 memSide);
AOM_HMB HMB_HANDLE *d2h_get_HMB_command(U32 ulHMDLEC, U64 ullHMDL, U32 ulMPS);
AOM_HMB void d2h_int_handler();
AOM_HMB void d2h_Err_insert(U32 setting);
AOM_HMB void d2h_flush_map(HMB_MEMORY_MAP *memMap);

U32 d2h_rmp_copy_to_host(U64 ullHostAddr, U32 ulSrcAddr, U32 ulByteCount);
#endif

