#ifndef _VUC_MICRONCLEAREVENTLOG_H_
#define _VUC_MICRONCLEAREVENTLOG_H_
#include "aom/aom_api.h"

#define VUC_MICRON_CLEAR_EVENT_LOG_CLASS	(0x0002)
#define VUC_MICRON_CLEAR_EVENT_LOG_CODE		(0x0004)

#define VUC_MICRON_CLEAR_EVENT_LOG_HEADER_LENGTH			(12)

#define VUC_MICRON_CLEAR_EVENT_LOG_UNIFIED_NAND_EVENT_LOG	(0xC8)

#pragma pack(push)
#pragma pack(1)
typedef struct {
	U8 ubResponseHeaderFormatVersion;
	U8 ubResponseDataFotmatVersion;
	U16 uwCMDClass;
	U16 uwCMDCode;
	U16 uwStatus;
	U32 ulDataPayloadSize;
} ClearEventLogResponseHEADER_t;
#pragma pack(pop)

extern U8 gubLastClearEventLogType;

AOM_VUC_3 void VUCMicronClearEventLog(U32 ulInputPayloadAddr, U32 ulResponsePayloadAddr);

#endif /* _VUC_MICRONCLEAREVENTLOG_H_ */
