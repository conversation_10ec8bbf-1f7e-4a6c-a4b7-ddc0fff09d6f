/****************************************************************************
*
*  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
*  All rights reserved
*
*  The content of this document is confidential and shall be applied
*  subject to the terms and conditions of the license agreement and
*  other applicable laws. Any unauthorized access, use or disclosure
*  of this document is strictly prohibited and may be punishable
*  under laws.
*
*  ftl_sustain.h
*
*
*
****************************************************************************/

#ifndef _FTL_SUSTAIN_H_
#define _FTL_SUSTAIN_H_

/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */
#if(D1_UNIT_EN || D3_GR_ALWAYS_SLC_EN)
#define FTL_SUSTAIN_MAX_D1_FREE_UNIT (FTL_GC_SRC_UNIT_MAX)
#define FTL_SUSTAIN_MAX_D3_FREE_UNIT (FTL_GC_SRC_UNIT_MAX - (GC_SRC_VB_NUM / gubLMUNumber))
#define FTL_SUSTAIN_MAX_GRCNT ((FIND_SRC_D1_FINISH == gpVT->GC.FindSrc.State) ? ((D3_GR_ALWAYS_SLC_EN) ? (FTL_SUSTAIN_MAX_D3_FREE_UNIT) : (FTL_SUSTAIN_MAX_D1_FREE_UNIT)) : (FTL_SUSTAIN_MAX_D3_FREE_UNIT))
#define FTL_SUSTAIN_NORMAL_GRCNT (3)
#endif /*(D1_UNIT_EN || D3_GR_ALWAYS_SLC_EN)*/
#define FTL_SUSTAIN_INSERT_DSA_TIME_INCREMENT_IN_MS		((D1_UNIT_EN || D3_GR_ALWAYS_SLC_EN) ? (50) : (500))
#define FTL_SUSTAIN_GC_TABLE_TIME_INCREMENT_IN_MS		((D1_UNIT_EN || D3_GR_ALWAYS_SLC_EN) ? (100):(1000))
#define FTL_SUSTAIN_EXPECT_TIME_FOR_DAMPING_IN_MS		((D1_UNIT_EN || D3_GR_ALWAYS_SLC_EN) ? (0):(1000))

#define FTL_SUSTAIN_RESERVED_4K_NUM			(256)

#define FTL_SUSTAIN_PROGRAM_PLANE_CNT_DIFFERENCE_THRESHOLD	(100)

#define FTL_SUSTAIN_DELAY_TRANSITION_THRESHOLD			((D1_UNIT_EN|| D3_GR_ALWAYS_SLC_EN) ? (2) : (5))
#define FTL_SUSTAIN_SMOOTH_INCREASE_DELAY_UNIT_NUM		(2)

#define FTL_SUSTAIN_UNIT_DIFFERENTIAL_THRESHOLD_FOR_COPY_UNIT_ADD_DELAY	(5) //WL & Read Error Handle

#define FTL_SUSTAIN_MIN_GR_REMAIN_P4K		(1)

#define FTL_SUSTAIN_INIT_LOG    (BIT31)

#define FW_USE_D3_NUM (1 + 1 + 1 + 1)    //VT, InitInfo, DriveLog, RSUnit

#define FTL_PRESUSTAIN_OFF			(0)

#define FTL_PRESUSTAIN_NORMAL		(1)

#define FTL_PRESUSTAIN_BG			(2)

#define FTL_PRESUSTAIN_STATE_INIT		(0)

#define FTL_PRESUSTAIN_STATE_ENTER		(1)

#define FTL_PRESUSTAIN_STATE_PROCESS	(2)

#define FTL_PRESUSTAIN_STATE_EXIT		(3)

#endif /* _FTL_SUSTAIN_H_ */
