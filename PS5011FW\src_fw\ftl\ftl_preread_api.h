#ifndef _FTL_PREREAD_API_H_
#define _FTL_PREREAD_API_H_

#include "aom/aom_api.h"
#include "typedef.h"

#define PREREAD_LENGTH								(8)
#define PREREAD_MISS_ALLOW_CNT						(2)
#define PREREAD_LOAD_NEXT_RANGE_L4K_NUM				(1)

#define PREREAD_DEBUG_ENTRY_NUM			(160)
#define PREREAD_DEBUG_RQ_INFO_NUM		(16)

#define M_PREREAD_GET_LAST_LCA()			(gPreReadInfo.ulStartLCA + gPreReadInfo.uwLength - 1)

typedef struct PreReadDebugEntry {
	U32 ulLCA;
	U8 ubLoc;			//PCA/XZIP idx/WLB idx
	U8 btHitType: 1;	//HW or FW detect PreRead
	U8 LocType: 7;		//RQ Loc
} PreReadDebugEntry_t;

typedef struct PreReadDebugInfo {
	U32 ulLCA;
	U8 ubLoc;			//PCA/XZIP idx/WLB idx
	U8 ubLocType;		//RQ LOC
} PreReadDebugInfo_t;

typedef struct DebugPreRead {
	U8 ubWPTR;
	U8 ubPreReadStartOffset;
	PreReadDebugEntry_t Entry[PREREAD_DEBUG_ENTRY_NUM];
	PreReadDebugInfo_t RQInfo[PREREAD_DEBUG_RQ_INFO_NUM];
} PreReadDebug_t;

typedef enum PreReadStateEnum {
	STATE_PREREAD_IDLE = 0,
	STATE_PREREAD_SET_FLUSH_W_CACHE,
	STATE_PREREAD_ADJUST_LOW_LIMIT,
	STATE_PREREAD_CHECK_W_CACHE,
	STATE_PREREAD_SECTOR_READ_CHECK_W_CACHE,
	STATE_PREREAD_LOAD_DATA,
	STATE_PREREAD_STOP,
	STATE_PREREAD_RECOVERY_LOW_LIMIT,
} PreReadStateEnum_t;

typedef enum PreReadAdjustLowLimitStateEnum {
	STATE_ADJUST_INIT = 0,
	STATE_ADJUST_RLB_LOW_LIMIT,
	STATE_ADJUST_PREREAD_LB_LOW_LIMIT
} PreReadAdjustLowLimitStateEnum_t;

typedef struct FTLPreRead {
	union {
		U64	uoAll;		// 8bits * 8 (len) = 64 bits
		U8	ubEntry[PREREAD_LENGTH];
	} ReferenceCnt;
	U32 ulLastLCA1;
	U32 ulLastLCA2;

	U32 ulStartLCA;			// PreRead start LCA
	U32 ulRQStartLCA;		// RQ start LCA
	U32 ulLockLCA;			// read cache lock LCA
	U16 uwLBOffset;			//	start offset in pre-read LB
	U16	uwLength;			// 	PR reserved length

	U16 uwAddRQLBOffset;	//start offset in pre-read LB


	/* continue counter */
	U8	ubContinueReadCnt;
	U8	ubNonAlignContinueCnt;

	/*	RCQ hit preread cnt */
	U8 	ubRCacheCnt;		//waiting to handle cnt (read cache)

	/*	Used to deal with RCQ LinkList */
	RCQOffset_t	LinkHead;
	RCQOffset_t	LinkTail;

	/* QD # detect */
	U8	ubIOPSThreshold;
	U8	ubRCacheAllocatedPBCnt;

	/* meta data*/
	U8	btLockExist			: 1;		//	read cache is lock
	U8	btSectorReadMode	: 1;		//	continue to read next data
	U8	btSATAInorderMode	: 1;		//	Set SATA Inorder Mode
	U8 	btRCacheUsed		: 1;		//	PreRead Use Read Cache
	U8	btAdjustLowLimit	: 1;		//	force stop and need to wait adjust low limit
	U8	btStopRCache		: 1;
	U8	btPreReadEn	: 1;
	U8	btPreReadMode		: 1;
	U8	ubAddRQCnt;

	U8 ubInvalidBMP;
	U8 ubMissCnt;

	/* state */
	PreReadStateEnum_t ubMainState;
	PreReadAdjustLowLimitStateEnum_t ubDoingStep;

} FTLPreRead_t;
TYPE_SIZE_CHECK(FTLPreRead_t, 56);

extern FTLPreRead_t gPreReadInfo;
extern PreReadDebug_t PreReadDebug;

AOM_INIT_2 void FTLPreReadInit(void);
AOM_PRE_READ void FTLPreReadResetVariable(void);
AOM_PRE_READ void FTLSetPreReadStop(void);
AOM_PRE_READ void FTLPreRead(void);
#endif /* _FTL_PREREAD_API_H_ */
