#ifndef _RMA_API_H_
#define _RMA_API_H_

#include "typedef.h"
#include "symbol.h"
#include "hal/cop0/cop0_cmd.h"
#include "table/sys_area/sys_area_api.h"


//==============================================================================
// Definition
//==============================================================================
#define RMALOG_LOW_PRIORITY_BLK_OVER_WRITE_EN			(TRUE)
#define RMALOG_HEADER_PAGE_IDX	(0)
#define RMALOG_SYSTEMINFO_PAGE_IDX	(2)

#define RMALOG_READ_VERIFY	(FALSE)
#define RMALOG_UART			(FALSE)

#define RMALOG_HEADER_SIZE			(0x1000)	// 4KB

#define RMALOG_HEADER					(0x11FF)

#define RMALOG_ASSERT_REASON_NUM	(10)

//==============================================================================
// Structures
//==============================================================================
typedef struct {
	U8 ubRMALogProgramDoing;
	U8 ubGlobalQueue;
	U8 ubRMALogBlkLimitNum;
	U32 ulRMALogRevision;
	SystemAreaBlock_t RmaLogBlock;
	U16 uwCurrentProgramPage;
	U16 uwAssertReason[RMALOG_ASSERT_REASON_NUM];
} RmaLog_t;
//==============================================================================
// Variables
//==============================================================================
extern RmaLog_t gRmaLog;
//==============================================================================
// Function API
//==============================================================================
void RmaLog(void);
void RmaLogCopyFlashAndRaidECCReg(U32 ulCopyFlashRegAddr, U32 ulCopyRaidECCRegAddr);
void RmaLogResetCmd(U8 ubQIdx);
void RmaLogErase(U8 ubQIdx);
void RmaLogWrite(U32 ulRAMAddr, U32 ulSizeInByte, U32 ulTempMTWRAMAddr, U8 ubNeedTempWRAM, U8 ubL4kNumPerPage);
#endif /* _ERR_HANDLE_API_H_ */
