#include "aom/aom_api.h"
#include "mux.h"
#include "mux_api.h"


#if PS5021_EN
// GPIO[0] =========================================================
void mux_gpio_0(void)
{
	M_SET_MUX_GPIO_BY_VALUE(BIT0);
}

// GPIO[1] =========================================================
void mux_gpio_1(void)
{
	M_SET_MUX_GPIO_BY_VALUE(BIT1);
}

AOM_INIT void mux_uart_rx(void)
{
	M_CLR_MUX_GPIO_BY_VALUE(BIT1);
}

void mux_etm_data2(void)
{
	M_CLR_MUX_GPIO_BY_VALUE(BIT7);
	M_SET_MUX_FW_OPTION_2();
}

#if E21_TODO
void mux_spi_io3(void)
{
	;
}
#endif /* E21_TODO */

// GPIO[2] =========================================================
void mux_gpio_2(void)
{
	M_SET_MUX_GPIO_BY_VALUE(BIT2);
}

void mux_spi_miso(void)
{
	M_CLR_MUX_GPIO_BY_VALUE(BIT2);
	M_SET_MUX_FW_OPTION_1();
}

// GPIO[3] =========================================================
void mux_gpio_3(void)
{
	M_SET_MUX_GPIO_BY_VALUE(BIT3);
}

void mux_etm_data1(void)
{
	M_CLR_MUX_GPIO_BY_VALUE(BIT6);
	M_SET_MUX_FW_OPTION_2();
}

AOM_INIT void mux_gpio_3_uart_tx_0(void)
{
	M_CLR_MUX_GPIO_BY_VALUE(BIT3);
}

// GPIO[4-5] =======================================================
void mux_gpio_4_5(void)
{
	M_SET_MUX_GPIO_BY_VALUE(BIT4 | BIT5);
}

AOM_INIT void mux_smb_sda_and_scl(void)
{
	M_CLR_MUX_GPIO_BY_VALUE(BIT14 | BIT15);
}

void mux_etm_data0_and_clk(void)
{
	M_CLR_MUX_GPIO_BY_VALUE(BIT5 | BIT12);
	M_SET_MUX_FW_OPTION_2();
}

// GPIO[6] =========================================================
void mux_gpio_6(void)
{
	M_SET_MUX_GPIO_BY_VALUE(BIT6);
}

void mux_etm_data3(void)
{
	M_CLR_MUX_GPIO_BY_VALUE(BIT8);
	M_SET_MUX_FW_OPTION_2();
}

void mux_spi_mosi(void)
{
	M_CLR_MUX_GPIO_BY_VALUE(BIT6);
	M_SET_MUX_FW_OPTION_1();
}

// GPIO[7-8] =======================================================
void mux_gpio_7_8(void)
{
	M_SET_MUX_GPIO_BY_VALUE(BIT7 | BIT8);
}

void mux_spi_cs_and_clk(void)
{
	M_CLR_MUX_GPIO_BY_VALUE(BIT7 | BIT8);
	M_SET_MUX_FW_OPTION_1();
}

// GPIO[9] =========================================================
void mux_gpio_9(void)
{
	M_SET_MUX_GPIO_BY_VALUE(BIT9);
}

void mux_pwrg_pd1(void)
{
	M_CLR_MUX_GPIO_BY_VALUE(BIT9);
}

// GPIO[10-12] =====================================================
void mux_gpio_10_11_12(void)
{
	M_SET_MUX_GPIO_BY_VALUE(BIT10 | BIT11 | BIT12);
}

void mux_host_perf_chk(void)
{
	;
}

void mux_i2cm_scl_and_sda(void)
{
	M_CLR_MUX_GPIO_BY_VALUE(BIT10 | BIT11);
}

#if E21_TODO
void mux_spi_io2(void)
{
	;
}
#endif /* E21_TODO */

#else /* PS5021_EN */
// GPIO[0] =========================================================
void mux_gpio_0(void)
{
	// CLR BIT0, BIT1
	M_CLR_MUX_GPIO_BY_VALUE(0x3);
}

void mux_gpio_0_uart_tx_0(void)
{
	M_CLR_MUX_GPIO(0);
	M_SET_MUX_GPIO(1);
}

// GPIO[1] =========================================================
void mux_gpio_1(void)
{
	// CLR BIT2, BIT3, BIT4
	M_CLR_MUX_GPIO_BY_VALUE(0x1C);
}

void mux_spi_io3(void)
{
	// CLR BIT2
	// SET BIT3, BIT4
	M_CLR_MUX_GPIO_BY_VALUE(0x1C);
	M_SET_MUX_GPIO_BY_VALUE(0x18);
}

AOM_INIT void mux_uart_rx(void)
{
	// SET BIT2
	// CLR BIT3, BIT4
	M_CLR_MUX_GPIO_BY_VALUE(0x1C);
	M_SET_MUX_GPIO_BY_VALUE(0x04);
}

AOM_INIT void mux_tdat(void)
{
#if PS5017_EN
	// SET BIT23
	// CLR BIT21, BIT22
	M_CLR_MUX_GPIO_BY_VALUE(0xE00000);
	M_SET_MUX_GPIO_BY_VALUE(0x800000);
#else /*PS5017_EN*/
	// SET BIT2, BIT4
	// CLR BIT3
	M_CLR_MUX_GPIO_BY_VALUE(0x1C);
	M_SET_MUX_GPIO_BY_VALUE(0x14);
#endif /*PS5017_EN*/
}

void mux_etm_data2(void)
{
	// SET BIT2, BIT3
	// CLR BIT4
	M_CLR_MUX_GPIO_BY_VALUE(0x1C);
	M_SET_MUX_GPIO_BY_VALUE(0x0C);
}

// GPIO[2] =========================================================
void mux_gpio_2(void)
{
	// CLR BIT5, BIT6, BIT7
	M_CLR_MUX_GPIO_BY_VALUE(0xE0);
}

void mux_gpio_2_uart_tx_0(void)
{
	// SET BIT5, BIT6
	// CLR BIT7
	M_CLR_MUX_GPIO_BY_VALUE(0xE0);
	M_SET_MUX_GPIO_BY_VALUE(0x60);
}

void mux_spi_miso(void)
{
	// SET BIT6
	// CLR BIT5, BIT7
	M_CLR_MUX_GPIO_BY_VALUE(0xE0);
	M_SET_MUX_GPIO_BY_VALUE(0x40);
}

void mux_bfh_selector(void)
{
	// SET BIT5
	// CLR BIT6, BIT7
	M_CLR_MUX_GPIO_BY_VALUE(0xE0);
	M_SET_MUX_GPIO_BY_VALUE(0x20);
}

// GPIO[3] =========================================================
void mux_gpio_3(void)
{
	// CLR BIT8, BIT9, BIT10
	M_CLR_MUX_GPIO_BY_VALUE(0x700);
}

void mux_gpio_3_uart_tx_1(void)
{
	// SET BIT10
	// CLR BIT8, BIT9
	M_CLR_MUX_GPIO_BY_VALUE(0x700);
	M_SET_MUX_GPIO_BY_VALUE(0x400);
}

AOM_INIT void mux_trstn(void)
{
	// SET BIT8, BIT9
	// CLR BIT10
	M_CLR_MUX_GPIO_BY_VALUE(0x700);
	M_SET_MUX_GPIO_BY_VALUE(0x300);
}

void mux_etm_data1(void)
{
	// SET BIT9
	// CLR BIT8, BIT10
	M_CLR_MUX_GPIO_BY_VALUE(0x700);
	M_SET_MUX_GPIO_BY_VALUE(0x200);
}

AOM_INIT void mux_gpio_3_uart_tx_0(void)
{
	// SET BIT8
	// CLR BIT9, BIT10
	M_CLR_MUX_GPIO_BY_VALUE(0x700);
	M_SET_MUX_GPIO_BY_VALUE(0x100);
}

// GPIO[4-5] =======================================================
void mux_gpio_4_5(void)
{
	// CLR BIT11, BIT12, BIT13
	M_CLR_MUX_GPIO_BY_VALUE(0x3800);
}

AOM_INIT void mux_gpio_4_5_uart_tx_1(void)
{
	// SET BIT11, BIT12
	// CLR BIT13
	M_CLR_MUX_GPIO_BY_VALUE(0x3800);
	M_SET_MUX_GPIO_BY_VALUE(0x1800);
}

AOM_INIT void mux_smb_sda_and_scl(void)
{
	// SET BIT12
	// CLR BIT11, BIT13
	M_CLR_MUX_GPIO_BY_VALUE(0x3800);
	M_SET_MUX_GPIO_BY_VALUE(0x1000);
}

void mux_etm_data0_and_clk(void)
{
#if PS5017_EN
	// CLR BIT11
	// SET BIT12, BIT13
	M_CLR_MUX_GPIO_BY_VALUE(0x3800);
	M_SET_MUX_GPIO_BY_VALUE(0x3000);
#else /*PS5017_EN*/
	// SET BIT11
	// CLR BIT12, BIT13
	M_CLR_MUX_GPIO_BY_VALUE(0x3800);
	M_CLR_MUX_GPIO_BY_VALUE(0x0800);
#endif /*PS5017_EN*/
}

// GPIO[6] =========================================================
void mux_gpio_6(void)
{
	// CLR BIT14, BIT15, BIT16
	M_CLR_MUX_GPIO_BY_VALUE(0x1C000);
}

AOM_INIT void mux_tclk(void)
{
#if PS5017_EN
	// SET BIT23
	// CLR BIT21, BIT22
	M_CLR_MUX_GPIO_BY_VALUE(0xE00000);
	M_SET_MUX_GPIO_BY_VALUE(0x800000);
#else /*PS5017_EN*/
	// SET BIT16
	// CLR BIT14, BIT15
	M_CLR_MUX_GPIO_BY_VALUE(0x1C000);
	M_SET_MUX_GPIO_BY_VALUE(0x10000);
#endif /*PS5017_EN*/
}

void mux_write_protect(void)
{
	// SET BIT14
	// CLR BIT15, BIT16
	M_CLR_MUX_GPIO_BY_VALUE(0x1C000);
	M_SET_MUX_GPIO_BY_VALUE(0x04000);
}

void mux_etm_data3(void)
{
#if PS5017_EN
	// SET BIT5
	// CLR BIT6, BIT7
	M_CLR_MUX_GPIO_BY_VALUE(0xE0);
	M_SET_MUX_GPIO_BY_VALUE(0x20);
#else /*PS5017_EN*/
	// SET BIT15
	// CLR BIT14, BIT16
	M_CLR_MUX_GPIO_BY_VALUE(0x1C000);
	M_SET_MUX_GPIO_BY_VALUE(0x08000);
#endif /*PS5017_EN*/
}

void mux_spi_mosi(void)
{
	// SET BIT14, BIT15
	// CLR BIT16
	M_CLR_MUX_GPIO_BY_VALUE(0x1C000);
	M_SET_MUX_GPIO_BY_VALUE(0x0C000);
}

// GPIO[7-8] =======================================================
void mux_gpio_7_8(void)
{
	// CLR BIT17, BIT18, BIT24
	M_CLR_MUX_GPIO_BY_VALUE(0x1060000);
}

void mux_etm_4_5(void)
{
	// SET BIT24
	// CLR BIT17, BIT18
	M_CLR_MUX_GPIO_BY_VALUE(0x1060000);
	M_SET_MUX_GPIO_BY_VALUE(0x1000000);
}

void mux_gpio_7_8_uart_tx_1(void)
{
	// SET BIT17
	// CLR BIT18, BIT24
	M_CLR_MUX_GPIO_BY_VALUE(0x1060000);
	M_SET_MUX_GPIO_BY_VALUE(0x0020000);
}

void mux_spi_cs_and_clk(void)
{
	// SET BIT18
	// CLR BIT17, BIT24
	M_CLR_MUX_GPIO_BY_VALUE(0x1060000);
	M_SET_MUX_GPIO_BY_VALUE(0x0040000);
}

// GPIO[9] =========================================================
void mux_gpio_9(void)
{
	// CLR BIT19, BIT20
	M_CLR_MUX_GPIO_BY_VALUE(0x180000);
}

void mux_pwrg_pd1(void)
{
	// SET BIT19
	// CLR BIT20
	M_CLR_MUX_GPIO_BY_VALUE(0x180000);
	M_SET_MUX_GPIO_BY_VALUE(0x080000);
}

// GPIO[10-12] =====================================================
void mux_gpio_10_11_12(void)
{
	// CLR BIT21, BIT22, BIT23
	M_CLR_MUX_GPIO_BY_VALUE(0xE00000);
}

void mux_etm_6_7(void)
{
	// SET BIT21, BIT23
	// CLR BIT22
	M_CLR_MUX_GPIO_BY_VALUE(0xE00000);
	M_SET_MUX_GPIO_BY_VALUE(0xA00000);
}

void mux_cpu_perf_chk(void)
{
	// SET BIT23
	// CLR BIT21, BIT22
	M_CLR_MUX_GPIO_BY_VALUE(0xE00000);
	M_SET_MUX_GPIO_BY_VALUE(0x800000);
}

void mux_host_perf_chk(void)
{
	// SET BIT21, BIT22
	// CLR BIT23
	M_CLR_MUX_GPIO_BY_VALUE(0xE00000);
	M_SET_MUX_GPIO_BY_VALUE(0x600000);
}

void mux_spi_io2(void)
{
	// SET BIT22
	// CLR BIT21, BIT23
	M_CLR_MUX_GPIO_BY_VALUE(0xE00000);
	M_SET_MUX_GPIO_BY_VALUE(0x400000);
}

void mux_i2cm_scl_and_sda(void)
{
	// SET BIT21
	// CLR BIT22, BIT23
	M_CLR_MUX_GPIO_BY_VALUE(0xE00000);
	M_SET_MUX_GPIO_BY_VALUE(0x200000);
}

#if PS5017_EN
void mux_gpio_10_uart_tx_0(void)
{
	// SET BIT22
	// CLR BIT21, BIT23
	M_CLR_MUX_GPIO_BY_VALUE(0xE00000);
	M_SET_MUX_GPIO_BY_VALUE(0x400000);
}
#endif /* PS5017_EN */
#endif /* PS5021_EN */

// GPIO all enable
void mux_gpio_all_enable(void)
{
	mux_gpio_0();
	mux_gpio_1();
	mux_gpio_2();
	mux_gpio_3();
	mux_gpio_4_5();
	mux_gpio_6();
	mux_gpio_7_8();
	mux_gpio_9();
	mux_gpio_10_11_12();
}

#if PS5017_EN
// GPIO 0 ~ 8 enable
void mux_gpio_QFN88_enable(void)
{
	mux_gpio_0();
	mux_gpio_1();
	mux_gpio_2();
	mux_gpio_3();
	mux_gpio_4_5();
	mux_gpio_6();
	mux_gpio_7_8();
}
#endif /* PS5017_EN */

