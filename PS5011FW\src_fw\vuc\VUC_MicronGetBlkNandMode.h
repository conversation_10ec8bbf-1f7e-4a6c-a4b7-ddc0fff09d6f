#ifndef _VUC_MICRONGETBLKNANDMODE_H_
#define _VUC_MICRONGETBLKNANDMODE_H_
#include "aom/aom_api.h"

#define VUC_MICRON_GET_BLOCK_NAND_MODE_HEADER_LENGTH (12)

typedef struct {
	U16 uwCH;
	U16 uwCE;
	U16 uwLUN;
	U16 uwBlk;
} GetBlkNandModeInputData_t;

typedef struct {
	U8 ubResponseHeaderFormatVersion;
	U8 ubResponseDataFotmatVersion;
	U16 uwCMDClass;
	U16 uwCMDCode;
	U16 uwStatus;
	U32 ulDataPayloadSize;
} GetBlkNandModeResponseHEADER_t;

enum {VUC_BLK_NAND_SLC_MODE = 1, VUC_BLK_NAND_MLC_MODE = 2, VUC_BLK_NAND_TLC_MODE = 3, VUC_BLK_NAND_QLC_MODE = 4};
AOM_VUC_3 void VUCMicronGetBlkNandMode(U32 ulInputPayloadAddr, U32 ulPayloadAddr);

#endif /* _VUC_MICRONGETBLKNANDMODE_H_ */

