#include "rdt/rdt_security_self_test.h"
#include "rdt/rdt_pio_force_wr_log.h"

#if RDT_MODE_EN

BOOL rdt_api_security_self_test(RDT_API_STRUCT_PTR rdt)
{
	U8 ubi;
	U8 ubResultBitmap = 0;
	U16 ubLoop;
	//UartPrintf2("\n[RDT] ===== Security Self Test Start =====");

	for (ubLoop = 0; ubLoop < RDT_SECURITY_ST_TEST_LOOP && (ubResultBitmap == 0); ubLoop++) {
		//UartPrintf2("\n[MTFW] Self Test Loop = %d", ubLoop);
		for (ubi = RDT_SECURITY_ST_SHA1; ubi < RDT_SECURITY_ST_NUM && (ubResultBitmap == 0); ubi++) {
			M_RTT_IDLE_US(100); //idle 100us
			switch (ubi) {
			case RDT_SECURITY_ST_SHA1:
				*(volatile U32 *)(0xF800A004) |= BIT2;
				while (!(*(volatile U32 *)(0xF80180F0) & BIT0)) {};
				if (!(*(volatile U32 *)(0xF80180F0) & BIT16)) {
					ubResultBitmap |= BIT(RDT_SECURITY_ST_SHA1);
				}
				break;
			case RDT_SECURITY_ST_SHA256:
				* (volatile U32 *)(0xF800A004) |= BIT3;
				while (!(*(volatile U32 *)(0xF80180F0) & BIT1)) {};
				if (!(*(volatile U32 *)(0xF80180F0) & BIT17)) {
					ubResultBitmap |= BIT(RDT_SECURITY_ST_SHA256);
				}
				break;
			case RDT_SECURITY_ST_SHA512:
				* (volatile U32 *)(0xF800A004) |= BIT4;
				while (!(*(volatile U32 *)(0xF80180F0) & BIT2)) {};
				if (!(*(volatile U32 *)(0xF80180F0) & BIT18)) {
					ubResultBitmap |= BIT(RDT_SECURITY_ST_SHA512);
				}
				break;
			case RDT_SECURITY_ST_SM3:
				* (volatile U32 *)(0xF800A004) |= BIT5;
				while (!(*(volatile U32 *)(0xF80180F0) & BIT3)) {};
				if (!(*(volatile U32 *)(0xF80180F0) & BIT19)) {
					ubResultBitmap |= BIT(RDT_SECURITY_ST_SM3);
				}
				break;
			case RDT_SECURITY_ST_SHA512_256:
				* (volatile U32 *)(0xF800A004) |= BIT6;
				while (!(*(volatile U32 *)(0xF80180F0) & BIT4)) {};
				if (!(*(volatile U32 *)(0xF80180F0) & BIT20)) {
					ubResultBitmap |= BIT(RDT_SECURITY_ST_SHA512_256);
				}
				break;
			case RDT_SECURITY_ST_PKE:
				* (volatile U32 *)(0xF800A108) |= BIT1;
				while (!(*(volatile U32 *)(0xF80180F0) & BIT7)) {};
				if (!(*(volatile U32 *)(0xF80180F0) & BIT23)) {
					ubResultBitmap |= BIT(RDT_SECURITY_ST_PKE);
				}
				break;
			case RDT_SECURITY_ST_SM4:
				* (volatile U32 *)(0xF800A400) |= BIT5;
				while (!(*(volatile U32 *)(0xF80180F0) & BIT6)) {};
				if (!(*(volatile U32 *)(0xF80180F0) & BIT22)) {
					ubResultBitmap |= BIT(RDT_SECURITY_ST_SM4);
				}
				break;
			case RDT_SECURITY_ST_AES:
				* (volatile U32 *)(0xF800A400) |= BIT4;
				while (!(*(volatile U32 *)(0xF80180F0) & BIT5)) {};
				if (!(*(volatile U32 *)(0xF80180F0) & BIT21)) {
					ubResultBitmap |= BIT(RDT_SECURITY_ST_AES);
				}
				break;
			default:
				//M_UART(MTFW_TEST_, "\nUnknown security self test flow");
				break;
			}
		}
	}

	//UartPrintf("\n[RDT] ===== Security Self Test =  %d =====", ubResultBitmap);

	if (ubResultBitmap) {
		rdt_api_pio_force_write_fail_log(rdt, RDT_PIO_FORCE_WR_LOG_PIO_CTL_ISSUE | RDT_PIO_FORCE_WR_LOG_SEC_ERROR, ubResultBitmap, TRUE);
	}
	if (ubResultBitmap == 0) {
		M_UART(RDT_TEST_, "\n [IC pattern] RDT Security TEST PASS");
		return PASS;
	}
	else {
		M_UART(RDT_TEST_, "\n [IC pattern] RDT Security TEST FAIL : %x ", ubResultBitmap);
		return FAIL;
	}

	//return (ubResultBitmap == 0) ? PASS : FAIL;
}

#endif //RDT_MODE_EN
