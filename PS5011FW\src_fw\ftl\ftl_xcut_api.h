#ifndef _FTL_XCUT_API_H_
#define _FTL_XCUT_API_H_

#include "aom/aom_api.h"
#include "env.h"
#include "ftl/ftl_xcut.h"

/************************************************
 * 					Enum
 ************************************************/
enum enumXCUTWriteEvent {
	XCUT_BUILD = 0,
	XCUT_INVALIDBUILD,
	XCUT_ADD,
	XCUT_GET,
	XCUT_DEFAULT
};

enum enumXCUTState {
	XCUT_STATE_OFF = 0,
	XCUT_STATE_INIT,
	XCUT_STATE_INIT_DONE,
	XCUT_STATE_BUILD,
	XCUT_STATE_ACTIVE,
	XCUT_STATE_FORCE_CLEAN
};

/************************************************
 * 					Function
 ************************************************/
INLINE void XCUTSetState(U8 ubState)
{
	gXCUTMeta.ubState = ubState;
}

INLINE U8 XCUTGetState(void)
{
	return gXCUTMeta.ubState;
}

INLINE void XCUTSetCDMOff(void)
{
	gXCUTMeta.ubCDMOn = FALSE;
}

AOM_INIT_2 void XCUTInitVariable(void);

#endif /* _FTL_XCUT_API_H_ */
