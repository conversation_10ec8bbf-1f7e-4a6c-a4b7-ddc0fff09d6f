#ifndef _FIP_H_
#define _FIP_H_

#include "common/typedef.h"
#include "common/mem.h"
#include "aom/aom_api.h"
#include "fip_api.h"
#include "hal/sys/api/rtt/rtt_api.h"
#include "hal/pic/uart/uart_api.h"
#include "debug/debug.h"
/*Flash Spec Seting*/
#define PA_OFFSET					(0)
#define PA_LENGTH					(10) // L06B : 10, 	B16A : 12
#define BA_OFFSET					(PA_OFFSET + PA_LENGTH)
#define BA_LENGTH					(12) // L06B : 12, 	B16A : 10
/*End-Flash Spec Seting*/

#define ALL_CHANNEL_CE0				(PS5017_EN ? (0x3) : (0xF))
#define ALL_CE_IN_CHANNEL			(0xA)  // Enable CE need to pay attention to DQ reverse (Positive /  Negative CE)
#define ALL_CHANNEL					(0xB)
#define ALL_CHANNEL_BIT				(PS5017_EN ? (0x00000101) : (0x01010101))
#define ALL_CE_IN_CHANNEL_BIT		(0xFF)
#define FPU_OFFSET					(0x0000)
#define PROGRAM_SPARE_IRAM_OFFSET	(SPARE_RESERVE_OFFSET)
#define READ_SPARE_IRAM_OFFSET		(PROGRAM_SPARE_IRAM_OFFSET + 64)
#define	CRC64_SIZE					(8)
#define FIP_MINIMUM_WORDLINE_WIDTH_FOR_RANDOMIZE	(6)
#define FIP_MAXIMUM_WORDLINE_WIDTH_FOR_RANDOMIZE	(13)
#define CE_NUM_IN_4B_REMAP_MATRIX (4 / MAX_CHANNEL) // PS5017_EN
#define ROW_ADDR_CYCLE_CNT		(( gFlhEnv.ulFlashDefaultType.BitMap.bt6CycleAddr ) ? 4 : 3) //E17 need to check
#define M_R32_ALL_CE_SETVALUE(VALUE)	(R32_FCON[R32_FCON_FCE_ENB] = VALUE)
//#define M_R32_PIO_CMD_PER_CH(VALUE, FCH_DEF)	( ((volatile U32*)(FLASH_REG_ADDRESS + FCH_DEF))[R32_FCTL_PIO_CMD] = VALUE )
#define M_R32_PIO_CMD_PER_CH(VALUE, CH)	( ((volatile U32*)(FLH_CTL_REG_BASE + CH*FLH_CH_GAP))[R32_FCTL_PIO_CMD] = VALUE )

/* 3D Nnad Seed Max Number*/
#define FLH_NAND_SEED_HW_MAX_SIZE	(67)

//--LDPC mode max ecc BIT
#define LDPC_MODE0_MAX_ERR_BIT (135)
#define LDPC_MODE1_MAX_ERR_BIT (125)
#define LDPC_MODE2_MAX_ERR_BIT (115)
#define LDPC_MODE3_MAX_ERR_BIT (105)
#define LDPC_MODE4_MAX_ERR_BIT (85)
#define LDPC_MODE5_MAX_ERR_BIT (65)
#define LDPC_MODE6_MAX_ERR_BIT (50)
#define LDPC_MODE7_MAX_ERR_BIT (170)
#define LDPC_MODE8_MAX_ERR_BIT (160)

//--LDPC Threshold Setup

#if (CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_QLC)
/**************************************************/
/*(CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_QLC)*/
/**************************************************/
#define LDPC_EARLY_THRS1_VALUE   (PS5017_EN) ? (0x23A) : (855)
#define LDPC_EARLY_THRS2_VALUE   (PS5017_EN) ? (0x00) : (760)
#define LDPC_MSA_ITER_THRS_VALUE (PS5021_EN) ? (0x19) : (0x06)
#define LDPC_CONV_THRS_VALUE	 (PS5017_EN) ? (0xE6) :(360)
#define LDPC_ABS_APP_THRS_VALUE  (0x05)
#define LDPC_FHB_MAX_ITE_VALUE   (0x14)
#define LDPC_NHB_MAX_ITE_VALUE   (PS5017_EN) ? (0x01) : (0x3C)
#define LDPC_GDBF_MAX_ITE_VALUE  (0x96)
#define LDPC_MSA_MAX_ITE_VALUE   (0x14)

#elif (FW_CATEGORY_FLASH == FLASH_BICS4TLCHDR)  /*(CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_QLC)*/
/**************************************************/
/*    (CONFIG_FLASH_TYPE == FLASH_BICS4TLCHDR)    */
/**************************************************/
#define LDPC_EARLY_THRS1_VALUE   (PS5017_EN) ? (0x23A) : (625)
#define LDPC_EARLY_THRS2_VALUE   (PS5017_EN) ? (0x00) : (590)
#define LDPC_MSA_ITER_THRS_VALUE (PS5021_EN) ? (0x19) : (0x06)
#define LDPC_CONV_THRS_VALUE	 (PS5017_EN) ? (0xE6) : (260)
#define LDPC_ABS_APP_THRS_VALUE  (0x05)
#define LDPC_FHB_MAX_ITE_VALUE   (0x14)
#define LDPC_NHB_MAX_ITE_VALUE   (PS5017_EN) ? (0x01) : (0x3C)
#define LDPC_GDBF_MAX_ITE_VALUE  (0x96)
#define LDPC_MSA_MAX_ITE_VALUE   (0x14)

#elif ((FW_CATEGORY_FLASH == FLASH_BICS5TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_QLC))  /*(CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_QLC)*///zerio BICS8 Add//zerio bics6 qlc add
/**************************************************/
/*    (CONFIG_FLASH_TYPE == FLASH_BICS5TLC)    */
/**************************************************/
#define LDPC_EARLY_THRS1_VALUE   (PS5017_EN) ? (0x23A) : (625)
#define LDPC_EARLY_THRS2_VALUE   (PS5017_EN) ? (0x00) : (590)
#define LDPC_MSA_ITER_THRS_VALUE (PS5021_EN) ? (0x19) : (0x06)
#define LDPC_CONV_THRS_VALUE	 (PS5017_EN) ? (0xE6) : (260)
#define LDPC_ABS_APP_THRS_VALUE  (0x05)
#define LDPC_FHB_MAX_ITE_VALUE   (0x14)
#define LDPC_NHB_MAX_ITE_VALUE   (PS5017_EN) ? (0x01) : (0x3C)
#define LDPC_GDBF_MAX_ITE_VALUE  (0x96)
#define LDPC_MSA_MAX_ITE_VALUE   (0x14)

#elif (IM_V6 || IM_V7 || IM_V8|| IM_V5)   /*(CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_QLC)*/  //Dylan Porting V7 RDT //Jeffrey Porting 3D V8 TLC Add
/**************************************************/
/*    (CONFIG_FLASH_TYPE == FLASH_HYNIXV6_TLC)    */
/**************************************************/
#define LDPC_EARLY_THRS1_VALUE   (0x262)
#define LDPC_EARLY_THRS2_VALUE   (0x00)
#define LDPC_MSA_ITER_THRS_VALUE (0x06)
#define LDPC_CONV_THRS_VALUE	 (0xFA)
#define LDPC_ABS_APP_THRS_VALUE  (0x05)
#define LDPC_FHB_MAX_ITE_VALUE   (0x14)
#define LDPC_NHB_MAX_ITE_VALUE   (0x01)
#define LDPC_GDBF_MAX_ITE_VALUE  (0x96)
#define LDPC_MSA_MAX_ITE_VALUE   (0x14)

#elif (MICRON_FSP_EN)                        /*(CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_QLC)*/
/**************************************************/
/*    (MICRON_FSP_EN)    */
/**************************************************/
#if ((IM_B47R) || IM_B37R || (IM_N48R))//zerio n48r
#define LDPC_EARLY_THRS1_VALUE   (570)
#define LDPC_EARLY_THRS2_VALUE   (0)
#define LDPC_MSA_ITER_THRS_VALUE (0x6)
#define LDPC_CONV_THRS_VALUE	 (230)
#define LDPC_ABS_APP_THRS_VALUE  (0x05)
#define LDPC_FHB_MAX_ITE_VALUE   (0x14)
#define LDPC_NHB_MAX_ITE_VALUE   (0x01)
#define LDPC_GDBF_MAX_ITE_VALUE  (0x96)
#define LDPC_MSA_MAX_ITE_VALUE   (0x14)
#else /* ((IM_B47R) || (IM_N48R)) */
#define LDPC_EARLY_THRS1_VALUE   (700)
#define LDPC_EARLY_THRS2_VALUE   (650)
#define LDPC_MSA_ITER_THRS_VALUE (PS5021_EN) ? (0x19) : (0x06)
#define LDPC_CONV_THRS_VALUE	 (300)
#define LDPC_ABS_APP_THRS_VALUE  (0x05)
#define LDPC_FHB_MAX_ITE_VALUE   (0x14)
#define LDPC_NHB_MAX_ITE_VALUE   (PS5017_EN) ? (0x01) : (0x3C)
#define LDPC_GDBF_MAX_ITE_VALUE  (0x96)
#define LDPC_MSA_MAX_ITE_VALUE   (0x14)
#endif /* ((IM_B47R) || (IM_N48R)) */
#else                                        /*(CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_QLC)*/
/**************************************************/
/*                  Other Case                    */
/**************************************************/
#define LDPC_EARLY_THRS1_VALUE   (625)
#define LDPC_EARLY_THRS2_VALUE   (590)
#define LDPC_MSA_ITER_THRS_VALUE (PS5021_EN) ? (0x19) : (0x06)
#define LDPC_CONV_THRS_VALUE	 (260)
#define LDPC_ABS_APP_THRS_VALUE  (0x05)
#define LDPC_FHB_MAX_ITE_VALUE   (0x14)
#define LDPC_NHB_MAX_ITE_VALUE   (0x3C)
#define LDPC_GDBF_MAX_ITE_VALUE  (0x96)
#define LDPC_MSA_MAX_ITE_VALUE   (0x14)
#endif                                       /*(CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_QLC)*/

//--LDPC Iteration Setup

//scan window use define
#define GOLDEN_R_DELAY		(140)
#define GOLDEN_W_DELAY		(127)
#if ASIC
#define WORK_AROUND_SHIFT_TOGGLE	(0)
#else
#define WORK_AROUND_SHIFT_TOGGLE	(5)
#endif
#define LEGACY_W_DLL_VALUE			(16)
#define WORK_AROUND_SHIFT_LEGACY	(0)
#define FIP_TLC_LMU_CNT			(3)
#define FIP_QLC_LMU_CNT			(4)	//Reip Porting 3D-V7 QLC Add
#define FIP_CHANNEL_PER_GROUP			(2)

#define PIO_SET_FEATURE_CMD			(0xEF)
#define PIO_SET_FEATURE_CMD_BY_LUN	        (0xD5)
#define PIO_GET_FEATURE_CMD			(0xEE)
#define PIO_GET_FEATURE_CMD_BY_LUN	        (0xD4)
#define PIO_FEATURE_ADR_TOSHIBASANDISK			(0x80)
#define PIO_FEATURE_ADR_INTELMICRON			(0x01)
#define PIO_FEATURE_ADR_SKHYNIX			(0x01)
#define TOSHIBA_TOGGLE_FEATURE	(0x00)
#define TOSHIBA_LEGACY_FEATURE	(0x01)
#define FIP_FIND_DQ_PASS_VALUE		(TRUE)
#define FIP_FIND_DQ_FAIL_VALUE		(FALSE)
#define FIP_DQ_TXRX_DELAY_MAX_VALUE	(16)
#define FIP_DQ_WRITE		(0)
#define FIP_DQ_READ		(1)
#define FIP_SCAN_WINDOW_TIMEOUT_THERSHOLD	(70)
#define FIP_WINDOW_THRESHOLD			(50)
#define FIP_SCAN_WINDOW_INTERLEAVE_EN (TRUE)
#define FIP_SDLL_TOLERANCE_VALUE	(5)
#define FIP_SDLL_PASS_SPEC_IN_PICO_SECOND (550)
#define FIP_SDLL_MAX_VALUE			(0x200)
#define FIP_SDLL_VALUE_MASK		(0x1FF)
#define FIP_DQ_ALL_BIT_FIND_PASS	(0xFF)
#define FIP_DQ_ALL_BIT_DONE		(0xFF)
#define C6_ERASE_START		(20)

#define FIP_TRAIN_DQS_FIND_FIRST_FAIL_VALUE			(BIT15)
#define FIP_NEED_TO_TRIGGER_WRITE			(1)
#define FIP_NEED_TO_TRIGGER_READ			(2)
#define FIP_NO_NEED_TO_TRIGGER_WRITE_READ		(0)
//workaround
#define	WORKAOURND_DLL_WIRET_NOP_CYCLES		(33) // formula: CEILING((Fsys / Fdll) * 2) / (Fsys/Fcpu)
#define WORKAOURND_DLL_WRITE_NOP()			(IdlePC(WORKAOURND_DLL_WIRET_NOP_CYCLES))

//
#define FPU_SRC_PTR_SHIFT		(3)
#define FPU_SRC_PTR_MASK		(BIT_MASK(3))
#define FPU_SRC_PTR_SHIFT_MASK		(FPU_SRC_PTR_MASK<<FPU_SRC_PTR_SHIFT)
#define FPU_READ_RAW_DES_PTR_SHIFT		(0)
#define FPU_READ_RAW_DES_PTR_MASK		(BIT_MASK(3))
#define FPU_READ_RAW_DES_PTR_SHIFT_MASK		(FPU_READ_RAW_DES_PTR_MASK<<FPU_READ_RAW_DES_PTR_SHIFT)
#define FPU_READ_RAW_LOGIC_SHIFT			(6)
#define FPU_READ_RAW_LOGIC_MASK			(BIT_MASK(2))
#define FPU_READ_RAW_LOGIC_SHIFT_MASK	(FPU_READ_RAW_LOGIC_MASK << FPU_READ_RAW_LOGIC_SHIFT)
#define FPU_READ_RAW_MODE_SHIFT			(8)
#define FPU_READ_RAW_MODE_MASK			(BIT_MASK(2))
#define FPU_READ_RAW_MODE_SHIFT_MASK	(FPU_READ_RAW_MODE_MASK	<<FPU_READ_RAW_MODE_SHIFT)
#define M_SET_FPU_SRC_PTR(x)			((x)<<FPU_SRC_PTR_SHIFT)
#define FPU_IBF_DIRECTION_BACKUP		BIT1
#define FPU_IBF_DIRECTION_RESTORE		CLR_BIT1
#define FPU_IBF_PTR_SHIFT		(2)
#define FPU_IBF_PTR_MASK		(BIT_MASK(3))
#define FPU_IBF_PTR_SHIFT_MASK		(FPU_IBF_PTR_MASK<<FPU_IBF_PTR_SHIFT)
#define M_SET_FPU_IBF_PTR(x)			((x)<<FPU_IBF_PTR_SHIFT)
#define BYPASS_ADR_GEN_OFFSET			2
#define R16_DMA_RAW_CMD_OFFSET		(0xE>>1)
//

// PIO
#define M_FIP_SET_DMA_DQSB_CHK(ubChannel) (R32_FCTL_CH[(ubChannel)][R32_FCTL_DMA_CFG] |= DQS_DQSB_CHK_EN)
#define M_FIP_CLR_DMA_DQSB_CHK(ubChannel) (R32_FCTL_CH[(ubChannel)][R32_FCTL_DMA_CFG] &= ~(DQS_DQSB_CHK_EN))

/* AC timing */
#define ENABLE_ADJUST_AC_TIMING_REG	(TRUE)
#define ENABLE_ADJUST_AC_TIMING_FPU	(TRUE)
//time number
#define TIME_NUM_FPU_TOGGLE ((PS5017_EN || PS5021_EN) ? 12 : 11)
#define TIME_NUM_REGISTER_TOGGLE ((PS5017_EN || PS5021_EN) ? 21 : 17)
//FPU - TOGGLE
#define IRAM2B   ((unsigned short *)FLASH_IRAM_ADDRESS)

#define TIME_ADL_TGL			0	//page program
#define TIME_AR_TGL 			1	//read ID
#define TIME_CH_TGL			2	//cmd latch, adr latch
#define TIME_CLR_TGL			3
#define TIME_CWAW_TGL		4
#define TIME_RR_TGL			5	//get feature, page read, page random read
#define TIME_WHR_TGL			6	//read ID, status read
#define TIME_WHR2_TGL		7	//page random read
#define TIME_WW_TGL			8
#define TIME_FPU_RESERVED_0	9
#define TIME_FPU_RESERVED_1	10
#if (PS5017_EN || PS5021_EN)
#define TIME_FPU_TRHW		11
#endif /* (PS5017_EN || PS5021_EN) */
//REGISTER - TOGGLE
#if (PS5013_EN)
#define TIME_TGL_3_0	0
#define TIME_TGL_6_4	1
#define TIME_TGL_11_8	2
#define TIME_TGL_14_12	3
#define TIME_TGL_19_16	4
#define TIME_TGL_23_20	5
#define TIME_TGL_27_24	6
#define TIME_TGL_31_28	7
#define TIME_REG_RESERVED_0	8
#define TIME_RTY_16	9
#define TIME_RTY_18_17	10
#define TIME_RTY_20_19	11
#define TIME_RTY_21	12
#define TIME_RTY_22	13
#define TIME_ONFI_23_20 14
#define TIME_ONFI_26_24	15
#define TIME_ONFI_30_28	16
#else /*(PS5013_EN)*/
#define TIME_TGL_3_0	0
#define TIME_TGL_6_4	1
#define TIME_TGL_11_8	2
#define TIME_TGL_14_12	3
#define TIME_TGL_19_16	4
#define TIME_TGL_23_20	5
#define TIME_TGL_27_24	6
#define TIME_TGL_31_28	7
#define TIME_RTY_16	8
#define TIME_RTY_18_17	9
#define TIME_RTY_20_19	10
#define TIME_RTY_21	11
#define TIME_RTY_22	12
#define TIME_TGL2_4_0	13
#define TIME_TGL2_20_16	14
#define TIME_TGL6_12_8	15
#define TIME_TGL6_26_24	16
#define TIME_TGL6_30_28	17
#define HS_MODE_15_14	18
#define HS_MODE_26_24	19
#define HS_MODE_30_27	20
#endif /* (PS5013_EN) */

#define FIP_ACTIMING_TRUNK					(0)
#define FIP_ACTIMING_BRANCH_STAGE6			(1)
#define FIP_ACTIMING_BRANCH_STAGE3			(2)
#define FIP_ACTIMING_BRANCH_MICRON_B27B		(3)
#define FIP_ACTIMING_BRANCH_MICRON_B16		(4)
#define FIP_ACTIMING_BRANCH_KINGSTON_HP		(5)
#define FIP_ACTIMING_BRANCH_KINGSTON_DELL	(6)
#define FIP_ACTIMING_BRANCH_KINGSTON		(7)
/* End of AC timing */

#define FEATURE_ADDRESS_DCC_TRAINING						(0x20)
#define FEATURE_VALUE_EXPLICIT_DCC_TRAINING_EN 				(0x01)
#define FEATURE_VALUE_EXPLICIT_DCC_TRAINING_EN_SAMSUNG_V7	(0x03)//Samsung v7 mst add--Reip
#define FEATURE_VALUE_EXPLICIT_DCC_TRAINING_DIS				(0x00)
#define FEATURE_VALUE_EXPLICIT_DCC_TRAINING_P3      		(0x11)
#define FEATURE_VALUE_EXPLICIT_DCC_TRAINING_P3_BICS5 		(0x05)//Duson Porting BICS5 Add
#define FEATURE_VALUE_EXPLICIT_DCC_TRAINING_P3_BICS6 		(0x0B)//Duson Porting BICS5 Add
#define FEATURE_VALUE_EXPLICIT_DCC_TRAINING_P3_BICS8 		(0x0B)//zerio BICS8 Add
#define FEATURE_VALUE_EXPLICIT_DCC_TRAINING_P3_SSV6	 		(0x03)
#define FEATURE_VALUE_EXPLICIT_DCC_TRAINING_P3_INTEL		(0x10)


#if(MICRON_FSP_EN)
#if IM_B27B
#define FIP_SET_FLASH_UNDERDRIVE		(0x03)
#define FIP_SET_FLASH_DEFAULTDRIVE		(0x02)
#elif IM_N28
#define FIP_SET_FLASH_UNDERDRIVE		(0x03)
#define FIP_SET_FLASH_DEFAULTDRIVE		(0x02)
#elif IM_B27A
#define FIP_SET_FLASH_UNDERDRIVE		(0x03)
#define FIP_SET_FLASH_DEFAULTDRIVE		(0x02)
#define FIP_SET_FLASH_OVERDRIVE			(0x01)
#elif IM_B47R
#define FIP_SET_FLASH_UNDERDRIVE		(0x03)
#define FIP_SET_FLASH_DEFAULTDRIVE		(0x04)
#elif IM_B37R
#define FIP_SET_FLASH_UNDERDRIVE		(0x03)
#define FIP_SET_FLASH_DEFAULTDRIVE		(0x04)
#elif IM_N48R
#define FIP_SET_FLASH_UNDERDRIVE		(0x03)
#define FIP_SET_FLASH_DEFAULTDRIVE		(0x04)
#else
#define FIP_SET_FLASH_UNDERDRIVE		(0x03)
#define FIP_SET_FLASH_DEFAULTDRIVE		(0x02)
#define FIP_SET_FLASH_OVERDRIVE			(0x01)
#endif

#define FIP_GET_UNIQUEID_CMD			(0xED)
#define FIP_GET_UNIQUEID_ADDR			(0x00)
#define FIP_GET_UNIQUEID_DATA_LENGTH	(256)

#define FIP_GET_TEMPERATURE_CMD			(0xD4)
#define FIP_GET_TEMPERATURE_ADDR		(0xE7)
#define FIP_GET_TEMPERATURE_LENGTH		(4)

#elif (YMTC_FSP_EN || SAMSUNG_FSP_EN || INTEL_FSP_EN)
#define FIP_GET_UNIQUEID_CMD			(0xED)
#define FIP_GET_UNIQUEID_ADDR			(0x00)
#define FIP_GET_UNIQUEID_DATA_LENGTH	(256)

#else//(MICRON_FSP_EN)
#define FIP_SET_FLASH_UNDERDRIVE		(0x02)
#define FIP_SET_FLASH_DEFAULTDRIVE		(0x04)
#define FIP_SET_FLASH_OVERDRIVE			(0x06)
#endif//(MICRON_FSP_EN)

#define FIP_1_DIE_PER_CH			(1)
#define FIP_2_DIE_PER_CH			(2)
#define FIP_4_DIE_PER_CH			(4)
#define FIP_8_DIE_PER_CH			(8)

#define FIP_SET_FEATURE_LENGTH	(0x04)

#define FIP_SET_FEATURE_BYTE_0	(0x00)
#define FIP_SET_FEATURE_BYTE_1	(0x01)
#define FIP_SET_FEATURE_BYTE_2	(0x02)
#define FIP_SET_FEATURE_BYTE_3	(0x03)

#define FIP_TSB_INTERFACE_CHANGE_ADDR	(0x80)
#define FIP_TSB_TOGGLE2_SETTING_ADDR		(0x02)

#define FIP_TSB_SDR_DATA		(0x01)
#define FIP_TSB_TOGGLE2_DATA		(0x07)

#define FIP_SCAN_WINDOW_NANO_SECOND_DIVIDEND (1000000000)
#define FIP_SCAN_WINDOW_PICO_SECOND_DIVISOR (1000)
#define M_FIP_CALCULATE_CLOCK_RATE_IN_NANO_SECOND(DataRate)	(FIP_SCAN_WINDOW_NANO_SECOND_DIVIDEND / ((DataRate) / 2))
#define M_FIP_TRANSFER_SDLL_VALUE_TO_PICO_SECOND(ClockPeriod,MDLLVaule,SdllMax,SdllMin)  (((((ClockPeriod) / 4) / (26 + (MDLLVaule))) * ((SdllMax) - (SdllMin))) / (FIP_SCAN_WINDOW_PICO_SECOND_DIVISOR));
// Flash Interface

#define SW_LO_SPPED TRUE
#define SW_HI_SPPED FALSE
#define SW_WRITE TRUE
#define SW_READ FALSE

enum DQ_TRAIN_DIRECTION {
	DQ_TRAIN_TX,
	DQ_TRAIN_RX
};

enum DQ_TRAIN_STATUS {
	DQ_TRAIN_ONGOING,
	DQ_TRAIN_FINISH
};

enum MDLL_SETTING_SOURCE {
	MDLL_SETTING_FROM_VUC_TRAIN = 1,
	MDLL_SETTING_FROM_VUC_READ,
	MDLL_SETTING_FROM_BURNER_RESTORE,
	MDLL_SETTING_FROM_ICE_MODE_DEFAULT
};

enum {
	INTERFACE_ERR_0 = 0,
	INTERFACE_ERR_1 = 1,
	INTERFACE_ERR_2 = 2,
	INTERFACE_ERR_3 = 3,
	INTERFACE_ERR_4 = 4,
	INTERFACE_ERR_5 = 5,
	INTERFACE_ERR_6 = 6,
	INTERFACE_ERR_7 = 7,
	INTERFACE_ERR_8 = 8,
	INTERFACE_ERR_9 = 9,
	INTERFACE_ERR_10 = 10,
	INTERFACE_ERR_11 = 11,
	INTERFACE_ERR_12 = 12,
	INTERFACE_ERR_Default = 0xFF
};

#define INTERFACE_UP					(0)
#define INTERFACE_DOWN					(1)

#define GERNAL_TIMEOUT_THRESHOLD		(100)
#define GENERAL_NO_TIMEOUT				(0)
#define POLL_SPECIFIC_DIE				(0)
#define POLL_ALL_CE_SINGLEDIE			(1)

extern REG32_Ptr gpFREG;
#define FIP_SET_FLASH_MODE_INIT			(0)
#define FIP_SET_FLASH_MODE_SATA_MP		(1)

#define FIP_NCS_DPS_VERIFY_NUMBER_OF_DPS_OFFSET		(2)
#define FIP_NCS_DPS_DEFAULT_VALUE_START_OFFSET		(1)	// 1 for number of Address need to do DPSPlusData
#define FIP_NCS_DPS_VERIFY_START_OFFSET				(1 + (FIP_NCS_DPS_VERIFY_NUMBER_OF_DPS_OFFSET << 1) + 1)	// 1 for number of Address need to do DPSPlusData, 1 for number of Address need to do DPS Verify


#define FIP_MAX_INTERRUPT_TYPE_MAY_OCCUR		(3)
/* Expect Multi Interrupt Types & MapInfo bits:
* 1. When Non_ZIP:
* ----Compare LCA FW error (4bits)
* ----CRC-16 error (4bits)
* 2. When ZIP:
* ----Bad LCA error (16bits)
* ----LCA search error (16bits)
* ----CRC-16 error (16bits)
*/

#define FIP_INTERRUPT_INVALID_MAP				(0xFF)

#define FIP_RB_PIN_DELAY (80) // wait tWB 100ns, tWHR 80-120ns , IdlePC(80) = 2030 us (S17)

#if (COP0_BACKUP_P4K_WORKAROUND)
#define FIP_GC_BACKUPP4K_WORST_SYS_CYCLE     (260)
#endif /*COP0_BACKUP_P4K_WORKAROUND*/
/*!
	@brief Wait flash to steable ready

	@retval 0   PASS Flash is ready
	@retval 1   FAIL Flash is not ready in 100ms
*/
INLINE U8 FlaWaitFlashReady(U32 ulTimeLimit_ms)
{
	U8 ubi;

	IdlePC(FIP_RB_PIN_DELAY);

#if LPM3_LOADER
	//Loader not to get RTT0.
	for (ubi = 0; ubi < 5; ubi++) {
		while ((R32_FCTL_CH[0][R32_FCTL_RBY_INF] & FLH_CE_RBY_AND_SGN_BIT) == 0) {
			ubi = 0;
			// double confirm : only check Channel 0, since all channel's RBY pins are short to one pin
		}
	}
#else
	// NES poll until ready
	if (NES_GEN1_EN || NES_GEN2_EN) {
		while ((R32_FCTL_CH[0][R32_FCTL_RBY_INF] & FLH_CE_RBY_AND_SGN_BIT) == 0);
	}
	else {
		RTTSelfTimeoutCondition_t RttTimer;
		for (ubi = 0; ubi < 5; ubi++) {
			RTTSetSelfTimeout(&RttTimer, ulTimeLimit_ms * 1000);
			while ((R32_FCTL_CH[0][R32_FCTL_RBY_INF] & FLH_CE_RBY_AND_SGN_BIT) == 0) {	// double confirm : only check Channel 0, since all channel's RBY pins are short to one pin
				ubi = 0;
				if (TRUE == RTTCheckSelfTimeout(&RttTimer)) {
					M_UART(FIP_, "RBY Timeout\n");
#if(BURNER_MODE_EN)
					gFlhEnv.ulFlashDefaultType.BitMap.btNoSupport = TRUE;
					guwDebugBurnerErrorCode = ASSERT_FLASH_0x0600;
#endif  /* (BURNER_MODE_EN) */
					return FAIL;
				}
			}
		}  // end of for
	}
#endif
	return PASS;
}

INLINE U8 FIPWaitFIPInternalReady(U8 ubChannel, U32 ulTimeLimit_ms)
{
	U8 ubi;
	if (NES_GEN1_EN || NES_GEN2_EN) {
		while (R32_FCTL_CH[ubChannel][R32_FCTL_MT_TRIG] & CHK_QUEUE_7_TO_0_MT_BUSY_MASK);
		while ((R32_FCTL_CH[ubChannel][R32_FCTL_MT_STATE] & WP_STATE_SHIFT_MASK) != (WP_STATE_IDLE));
		while (R32_FCTL_CH[ubChannel][R32_FCTL_FPU_TRIG] & CHK_FPU_BUSY);
		while (MTQ_INT_RDY_AUTOPOLL_ALLCLEAR != R32_FCTL_CH[ubChannel][R32_FCTL_INT_RDY]);
	}
	else {
		RTTSelfTimeoutCondition_t RttTimer1;
		RTTSelfTimeoutCondition_t RttTimer2;
		RTTSelfTimeoutCondition_t RttTimer3;
		RTTSelfTimeoutCondition_t RttTimer4;
		for (ubi = 0; ubi < 5; ubi++) {
			RTTSetSelfTimeout(&RttTimer1, ulTimeLimit_ms * 1000);
			while (R32_FCTL_CH[ubChannel][R32_FCTL_MT_TRIG] & CHK_QUEUE_7_TO_0_MT_BUSY_MASK) {
				ubi = 0;
				if (TRUE == RTTCheckSelfTimeout(&RttTimer1)) {
					M_UART(FIP_, "MT_BUSY\n");
					return FAIL;
				}
			}
			RTTSetSelfTimeout(&RttTimer2, ulTimeLimit_ms * 1000);
			while ((R32_FCTL_CH[ubChannel][R32_FCTL_MT_STATE] & WP_STATE_SHIFT_MASK) != (WP_STATE_IDLE)) {
				ubi = 0;
				if (TRUE == RTTCheckSelfTimeout(&RttTimer2)) {
					M_UART(FIP_, "MT_STATE_BUSY\n");
					return FAIL;
				}
			}
			RTTSetSelfTimeout(&RttTimer3, ulTimeLimit_ms * 1000);
			while (M_FIP_CHK_FPU_BUSY(ubChannel)) {
				ubi = 0;
				if (TRUE == RTTCheckSelfTimeout(&RttTimer3)) {
					M_UART(FIP_, "FPU BUSY\n");
					return FAIL;
				}
			}
			RTTSetSelfTimeout(&RttTimer4, ulTimeLimit_ms * 1000);
			while (MTQ_INT_RDY_AUTOPOLL_ALLCLEAR != R32_FCTL_CH[ubChannel][R32_FCTL_INT_RDY]) {
				ubi = 0;
				if (TRUE == RTTCheckSelfTimeout(&RttTimer4)) {
					M_UART(FIP_, "INT BUSY\n");
					return FAIL;
				}
			}
		}  // end of for
	}
	return PASS;
}
AOM_INIT void FlaReadID(U8 ubCH, U8 ubCE, U8 ubAddr, U8 *pubFlashID);

AOM_INIT void FIPPowerOnCheckFlashID(void);
AOM_INIT void FlaSetToggleTempFunction(void);
#if (ASIC == FALSE)
AOM_INIT_2 void FlaScanDll(void);
#endif /* ASIC == FALSE */
#if (PS5017_EN)
AOM_INIT_2 void FlaDMAConfig(U8 ubChannel, U8 ubFrameStartPtr, U8 ubFrameCnt, U32 ulSpareAdr, U32 ulDatAdr);
#else /* (PS5017_EN) */
AOM_VUC void FlaDMAConfig(U8 ubChannel, U8 ubFrameStartPtr, U8 ubFrameCnt, U32 ulSpareAdr, U32 ulDatAdr);
#endif /* (PS5017_EN) */
AOM_INIT U8 FlaChannelDetect(void);
#if MST_MODE_EN
AOM_INIT U8 FIPCheck_WP_DQS_DQSB(U8 ubChannel, U8 ubCheckInterface);//Rule from Boot Code
#else
AOM_INIT U8 FIPCheckWriteProtectPin(U8 ubChannel);
AOM_INIT U32 FIPCheckDQSDQSB(U8 ubChannel);
#endif
AOM_INIT void FlaSetRandomizeConfig(U8 ubIs3DRandomizer, U8 ubConvEnable, U8 ubInvsEnable, U8 ubFramePerPage);
AOM_INIT void ACTiming_Setting_Register(void);
AOM_INIT void ACTiming_Setting_FPU(void);
AOM_INIT void FlaChkType(void);
AOM_INIT void FlaACTimingInit(void);
AOM_INIT void FlaSetFlashMode(U8 ubMode);
AOM_INIT U8 FlaSetParameterOverload(U8 ubCH, U8 ubCE, U8 ubLUN);
AOM_INIT void FlaDisableMicronPLA(void);
AOM_INIT U8 FIPSetInterface(U8 ubMode, U8 ubForceSetFeature, U8 ubDirection);
AOM_LPM void FIPSetFeatureInLPM(U8 ubCH, U8 ubCE, U8 ubAddr, U8 *pubFeature);
AOM_LPM U8 FIPGetFeatureInLPM(U8 ubCH, U8 ubCE, U8 ubAddr, U8 *pubFeature, U8 ubDataMask, U8 ubCompareData, U8 ubCompareEn, U8 ubRetryTime, U16 uwAssertCode);
AOM_LPM U8 FIPSetInterfaceInLPM(U8 ubMode, U8 ubForceSetFeature, U8 ubDirection);
AOM_LPM void FlaIOTypeSettingInLPM(U8 ubChannel, U8 ubIOType);
AOM_LPM void FIPSetFlashToDefaultModeLPM(void);
AOM_LPM void FIPResetPADInLPM(void);
AOM_INIT U8 FlaSetNVDDRMode(U8 ubMode);
AOM_INIT void FlaZQC(void);
AOM_INIT void FlaInterfaceSetting(U8 ubCH, U8 ubIFType);
AOM_INIT U16 FlaCalculateDllNOP(void);
#if VRLC_INIT_USTP1_WITH_RETRY || RDT_MODE_EN
AOM_INIT void MicronSwitchUSTP1(void);
AOM_INIT void FIPWaitFlashReadyLite(void);
#endif /* VRLC_INIT_USTP1_WITH_RETRY */
void FlaDQTrainFinish(void);

U8 FIPCheckMTQQueueIsProgramParityForOpenBlockRaidECC(U8 ubChannel, U8 ubBank, U8 ubParityTag);
#if (BURNER_MODE_EN || RDT_MODE_EN)
U8 FlaReadStatus(U8 ubChannel, U8 ubFlashCE, U8 ubReadCont);
#endif /* (BURNER_MODE_EN || RDT_MODE_EN) */
#if BURNER_MODE_EN || RDT_RECORD_SCAN_WINDOW_LOG
void FlaScanDllWindowCompare(U32 ulPatternSize, U32 ulPatternAddr, U8 ubChannelSelect, U8 ubBankSelect, U8 ubDieSelect, U32 ulDelayRead, U32 ulDelayWrite, U8 ubBitScan, U8 ubBitScanPosition, U8 ubNeedTriggerWriteR, U16 *pDllWindowValuelMin, U16 *pDllWindowValuelMax);
void FlaScanDllWindowWrite(U8 isSpeedLo, U32 ulPatternSize, U32 ulPatternAddr, U32 ulDelayWrite, U8 ubChannelSelect, U8 ubBankSelect, U8 ubDieSelect, U8 ubNeedTriggerWrite);
U8 FlaScanDllWindowRead(U8 isSpeedLo, U32 ulPatternSize, U32 ulPatternAddr, U8 ubChannelSelect, U8 ubBankSelect, U8 ubDieSelect, U32 ulDelayRead, U32 ulDelayWrite, U8 ubBitScan, U8 ubBitScanPosition, U8 ubNeedTriggerRead, U16 *pDllWindowValuelMin, U16 *pDllWindowValuelMax);
void FlaScanDQSTXDelay(U32 ulPatternSize, U32 ulPatternAddr, U8 ubChannelSelect, U8 ubBankSelect, U8 ubDieSelect, U8 ubNeedTriggerWrite);
U8 FlaScanDQSRXDelay(U32 ulPatternSize, U32 ulPatternAddr, U8 ubChannelSelect, U8 ubBankSelect, U8 ubDieSelect, U8 ubNeedTriggerRead, U8 ubDQBit);
U8 FlaScanDQSCompare(U32 ulPatternSize, U32 ulPatternAddr, U8 ubChannelSelect, U8 ubBankSelect, U8 ubDQBit);
void FipDQSTXFindLastPassBit(ScanWindowParam_t *pCurState, U8 ubCh, U8 ubCE, U8 ubDie);
void FipDQSRXFindLastPassBit(ScanWindowParam_t *pCurState, U8 ubCh, U8 ubCE, U8 ubDie);
U32 FipCalculateScanWindowTimeInPicoSecond(U8 ubChannel, U8 ubCE, U8 ubDie, U8 ubReadWindow);
void FlaBackupAndResetAllChannelInterrupt(void);
U16 FipDQGroupAlignSDllValue(U8 ubChannel, U8 ubCECnt, U8 ubDie, U8 btRead);
#endif	/* BURNER_MODE_EN */
#if FIP_DPS_FEATURE_EN
AOM_INIT void FIPReduceProgramTime();
AOM_INIT void FIPSetNandParameterTable(U8 ubChannel, U8 ubCE, U8 ubDie, U8 ubAddr, U16 uwData);
AOM_INIT U8 FIPGetNandParameterTable(U8 ubChannel, U8 ubCE, U8 ubDie, U8 ubAddr);
AOM_INIT void FIPNandExitTestMode(U8 ubChannel, U8 ubCE);
AOM_INIT void FIPNandTestOutputMode(U8 ubChannel, U8 ubCE);
AOM_INIT void FIPNandTestInputMode(U8 ubChannel, U8 ubCE);
#endif /*FIP_DPS_FEATURE_EN */


#if((CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC))//Dylan //Reip Porting 3D-V7 QLC Add
AOM_RETRY U8 RetryInitGenHynixRRT(U32 ulTLCReadRetryTableBufAddr, U32 ulSLCReadRetryTableBufAddr);
#endif

#if ((MST_MODE_EN) && (!LPM3_LOADER) && (!RDT_RUN_ONLINE))
void FIPLoadRRTFromCodeBlock(void);
#endif


#if (PS5021_EN && (!BURNER_MODE_EN))
void FIPReadPadPage(U32 ulReadBuffer)
#endif /*(PS5021_EN && (!BURNER_MODE_EN))*/

AOM_INIT void FIPFPUTrigger(U8 ubChannel, U16 uwFpu_offset);
AOM_INIT void FIPBackupRestore(U8 ubChannel, U32 ulDBufAddr, U32 ulIRamAddr, U8 ubIBufPtr, U8 ubDirection, U8 ubMode, U8 ubFrameMask, U16 uwLen);

AOM_INIT U8 FlaScanDllDMAWrite(U8 ubChannel, U8 ubBank, U8 ubDie, U16 uwUnit, U8 ubPlane, U16 uwPage, U32 ulPatternSize, U32 ulBuffAddr, U8 ubTrigFPU);
AOM_INIT U8 FlaScanDllDMARead(U8 ubChannel, U8 ubBank, U8 ubDie, U16 uwUnit, U8 ubPlane, U16 uwPage, U32 ulPatternSize, U32 ulBuffAddr, U8 ubTrigFPU);
AOM_INIT U8 FlaScanDllRawWrite(U8 ubChannel, U8 ubBank, U8 ubDie, U16 uwUnit, U8 ubPlane, U16 uwPage, U32 ulPatternSize, U32 ulBuffAddr);
AOM_INIT U8 FlaScanDllRawRead(U8 ubChannel, U8 ubBank, U8 ubDie, U16 uwUnit, U8 ubPlane, U16 uwPage, U32 ulPatternSize, U32 ulBuffAddr);

#endif /* _FIP_H_ */
