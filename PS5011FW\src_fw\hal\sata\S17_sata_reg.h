#ifndef _S17_SATA_REG_H_
#define _S17_SATA_REG_H_

#if (HOST_MODE == SATA)

#define SATA_REG_BASE                               (SATA_REG_ADDRESS)

#define R8_SATA                                     ((volatile U8 *)SATA_REG_BASE)
#define R16_SATA                                    ((volatile U16 *)SATA_REG_BASE)
#define R32_SATA                                    ((volatile U32 *)SATA_REG_BASE)
#define R64_SATA                                    ((volatile U64 *)SATA_REG_BASE)

/* Based on PS5013 SATA APU register defines (Rev 0.6) */

/*=====================================================================================
                        System Control Register (see Table 3)
=====================================================================================*/

#define R32_SATA_INT_FLAG                           (0x00 >> 2)
#define     SATA_BUF_DONE_INT_BIT                   (BIT0)
#define     SATA_RCV_CMD_INT_BIT                    (BIT1)
#define     SATA_SRST_CLR_INT_BIT                   (BIT2)
#define     SATA_PLLLOCK_CHG_INT_BIT                (BIT3)
#define     SATA_LNK_COMRI_INT_BIT                  (BIT4)
#define     SATA_LNK_COMWAKE_INT_BIT                (BIT5)
#define     SATA_PHYRDY_CHG_INT_BIT                 (BIT6)
#define     SATA_LNK_ESTB_CHG_INT_BIT               (BIT7)
#define     SATA_LNK_REQPRCV_INT_BIT                (BIT8)  // Received PMREQ P interrupt
#define     SATA_LNK_REQSRCV_INT_BIT                (BIT9)
#define     SATA_TRS_BISTRCV_INT_BIT                (BIT10)
#define     SATA_ERR_EVT_INT_BIT                    (BIT12)
#define     SATA_CMD_FINISH_INT_BIT                 (BIT13)
#define     SATA_NCQ_EXPR_INT_BIT                   (BIT15) // NCQ command expired interrupt
#define     SATA_RCV_CTRL_INT_BIT                   (BIT16) // Received control fis interrupt
#define     SATA_DIPM_TT_INT_BIT                    (BIT17) // DIPM transition timer interrupt
#define     SATA_P2S_WC_TT_INT_BIT                  (BIT18) // P to S with COMWAKE transition timer int
#define     SATA_AUTO_P2S_TT_INT_BIT                (BIT19) // Auto P to S transition timer interrupt
#define     SATA_INTERVAL_TMR_INT_BIT               (BIT20) // Interval timer timeout interrupt
#define     SATA_IDLE_TMR_INT_BIT                   (BIT21) // Idle timer timeout interrupt
#define     SATA_CMD_ERR_INT_BIT                    (BIT22) // Command error interrupt

#define R32_SATA_INT_EN                             (0x04 >> 2)
#define     SATA_BUF_DONE_INT_EN_BIT                (BIT0)
#define     SATA_RCV_CMD_INT_EN_BIT                 (BIT1)
#define     SATA_SRST_CLR_INT_EN_BIT                (BIT2)
#define     SATA_PLLLOCK_CHG_INT_EN_BIT             (BIT3)
#define     SATA_LNK_COMRI_INT_EN_BIT               (BIT4)
#define     SATA_LNK_COMWAKE_INT_EN_BIT             (BIT5)
#define     SATA_PHYRDY_CHG_INT_EN_BIT              (BIT6)
#define     SATA_LNK_ESTB_CHG_INT_EN_BIT            (BIT7)
#define     SATA_LNK_REQPRCV_INT_EN_BIT             (BIT8)
#define     SATA_LNK_REQSRCV_INT_EN_BIT             (BIT9)
#define     SATA_TRS_BISTRCV_INT_EN_BIT             (BIT10)
#define     SATA_ERR_EVT_INT_EN_BIT                 (BIT12)
#define     SATA_CMD_FINISH_INT_EN_BIT              (BIT13)
#define     SATA_NCQ_EXPR_INT_EN_BIT                (BIT15)
#define     SATA_RCV_CTRL_INT_EN_BIT                (BIT16)
#define     SATA_DIPM_TT_INT_EN_BIT                 (BIT17) // DIPM transition timer interrupt
#define     SATA_P2S_WC_TT_INT_EN_BIT               (BIT18) // DIPM P to S with COMWAKE transition timer int
#define     SATA_AUTO_P2S_TT_INT_EN_BIT             (BIT19) // Auto P to S transition timer interrupt
#define     SATA_INTERVAL_TMR_INT_EN_BIT            (BIT20)
#define     SATA_IDLE_TMR_INT_EN_BIT                (BIT21)
#define     SATA_CMD_ERR_INT_EN_BIT                 (BIT22)

#define R16_SATA_FEATURE                            (0x08 >> 1)
#define R8_SATA_FEATURE                             (0x08)
#define R8_SATA_FEATURE_EXP                         (0x09)

#define R16_SATA_SECTOR_CNT                         (0x0A >> 1)
#define R8_SATA_SECTOR_CNT                          (0x0A)
#define R8_SATA_SECTOR_CNT_EXP                      (0x0B)

#define R8_SATA_LBA_L                               (0x0C)
#define R8_SATA_LBA_M                               (0x0D)
#define R8_SATA_LBA_H                               (0x0E)
#define R8_SATA_LBA_L_EXP                           (0x0F)
#define R8_SATA_LBA_M_EXP                           (0x10) // LBA bit[39:32]
#define R8_SATA_LBA_H_EXP                           (0x11) // LBA bit[47:40]

#define R8_SATA_DEVICE                              (0x12)
#define     SATA_CHS_HEAD_SHIFT                     (0)           // CHS mode
#define     SATA_CHS_HEAD_MASK                      (BIT_MASK(4)) // CHS mode
#define     SATA_LBA_24_27_BIT_SHIFT                (0)           // 28-bit LBA mode [27:24]
#define     SATA_LBA_24_27_BIT_MASK                 (BIT_MASK(4)) // 28-bit LBA mode [27:24]
#define     SATA_CHECK_LBA_MODE_BIT                 (BIT6) // 0b:CHS, 1b:LBA mode

#define R8_SATA_COMMAND                             (0x13)

#define R8_SATA_STATUS                              (0x14)
#define     SATA_WRITE_PROTECT_STATUS_BIT           (BIT5) // Set bit5 as Write Protect
#define     SATA_NCQ_NORMAL_STATUS                  (0x40) // NCQ Cmd No Error
#define     SATA_NCQ_ERROR_STATUS                   (0x41) // NCQ Cmd Error
#define     SATA_NCQ_WRITE_PROTECT_STATUS           (0x41 | BIT5)
#define     SATA_NONNCQ_NORMAL_STATUS               (0x50) // Non-NCQ Cmd No Error
#define     SATA_NONNCQ_ERROR_STATUS                (0x51) // Non-NCQ Cmd Error
#define     SATA_NONNCQ_WRITE_PROTECT_STATUS        (0x51 | BIT5)
#define     SATA_DEVICE_FUALT_STATUS                (0x61) // HW Error Occur

#define R8_SATA_ERROR                               (0x15)
#define     SATA_NO_ERROR                           (0x00) // NO Error
#define     SATA_CMD_ABORT                          (0x04) // Cmd ABORT
#define     SATA_ID_ERR                             (0x10) // ID Not Found
#define     SATA_DATA_ERR                           (0x40) // UNC Error
#define     SATA_CRC_ERR                            (0x84) // CRC Error

#define R8_SATA_CONTROL                             (0x16)
#define     SATA_SRST_BIT                           (BIT2) // Soft reset (SRST) set this bit first (1st control FIS with SRST bit set) then clear it (2nd control FIS with SRST bit clear) (see SerialATA Rev3.3 11.4 & 13.16.2.3)

#define R32_SATA_SACTIVE                            (0x18 >> 2)
#define     SATA_SET_ACTIVE_ALL                     (0xFFFFFFFF) // Set all NCQ Tag active
#define     SATA_CLEAR_ACTIVE_ALL                   (0x00000000) // Clear all active NCQ Tag

#define R32_SATA_AUXILIARY                          (0xF8 >> 2)

#define R8_SATA_FLAG_CTRL                           (0x1D)
#define     SATA_RD_TRG_DONE_SEL_BIT                (BIT0)
#define     SATA_DDT_EN_BIT                         (BIT1)
#define     SATA_EXLNK_BIT                          (BIT3)
#define     SATA_TSP_IDLE_SEL_SHIFT                 (4)
#define     SATA_TSP_IDLE_SEL_MASK                  (BIT_MASK(2))
#define     SATA_TSP_IDLE_SEL_16_CLK_DELAY          (0x01)
#define     SATA_TSP_IDLE_SEL_64_CLK_DELAY          (0x03)

#define R8_SATA_CMD_INFO                            (0x1C)
#define     SATA_NCQ_VLD_WR_EXIST_BIT               (BIT0)
#define     SATA_NCQ_VLD_RD_EXIST_BIT               (BIT1)
#define     SATA_RCV_CON_ZERO_DATA_BIT              (BIT2)
#define     SATA_USER_DATA_SYNC_BIT                 (BIT4)
#define     SATA_HW_DEL_PIOIN_EN_BIT                (BIT5)
#define     SATA_DMA_TRG_FAIL_BIT                   (BIT6)
#define     SATA_APU_IDLE_BIT                       (BIT7)

#define R16_SATA_CMD_INFO2                          (0x1E >> 1)
#define     SATA_PIO_DMA_CMD_BIT                    (BIT0)
#define     SATA_NCQ_CMD_BIT                        (BIT1)
#define     SATA_WR_CMD_BIT                         (BIT2)
#define     SATA_LBA48_CMD_BIT                      (BIT3)
#define     SATA_2BSEC_CMD_BIT                      (BIT4)
#define     SATA_LBA_CONT_BIT                       (BIT5)
#define     SATA_CONT_LBA_CHK_CMD_BIT               (BIT6)
#define     SATA_MUL_PIO_CMD_BIT                    (BIT7)
#define     SATA_RCV_NCQ_TAG_SHIFT                  (8)
#define     SATA_RCV_NCQ_TAG_MASK                   (BIT_MASK(5))
#define     SATA_DUP_NCQ_TAG_BIT                    (BIT13)
#define     SATA_LBA_ERR_BIT                        (BIT14)
#define     SATA_NCQ_LBA_OVLP_BIT                   (BIT15)
#define R8_SATA_CMD_INFO2_LOWBYTE                   (0x1E)
#define R8_SATA_CMD_INFO2_HIGHBYTE                  (0x1F)

#define R8_SATA_NCQ_SET_TAG                         (0x21)
#define     SATA_CUR_TAG_SHIFT                      (0)
#define     SATA_CUR_TAG_MASK                       (BIT_MASK(5))

#define R8_SATA_NCQ_SET_N_A                         (0x71)
#define     SATA_DMA_SUP_A_BIT                      (BIT0)
#define     SATA_SDB_FIS_N_BIT                      (BIT1)

#define R16_SATA_SYS_CTRL                           (0x22 >> 1)
#define     SATA_TQ_WR_BY_ORD_BIT                   (BIT0)
#define     SATA_REG_SATA_EN_BIT                    (BIT1)
#define     SATA_BLK_FIS_EN_BIT                     (BIT5)
#define     SATA_NCQ_D2H_BLK_DIS_BIT                (BIT6)
#define     SATA_LBA48_IGR_CHS_BIT                  (BIT7)
#define     SATA_REG_GENMAX_SHIFT                   (8)
#define     SATA_REG_GENMAX_MASK                    (BIT_MASK(2))
#define     SATA_REG_GENMAX_GEN1                    (BIT8)
#define     SATA_REG_GENMAX_GEN2                    (BIT9)
#define     SATA_REG_GENMAX_GEN3                    (BIT8 | BIT9)
#define     SATA_LNK_DATA_CHK_BIT                   (BIT10)
#define     SATA_AUTO_CLINTCLR_BIT                  (BIT11)
#define     SATA_MUL_PIO_DRQ_SIZE_SHIFT             (12) // Multiple PIO DRQ Size (MUL_PIO_DRQ_SIZE) (default 0.5KB)
#define     SATA_MUL_PIO_DRQ_SIZE_MASK              (BIT_MASK(3))
#define     SATA_MUL_PIO_DRQ_SIZE_512B              (0)           // 000b: 0.5KB
#define     SATA_MUL_PIO_DRQ_SIZE_1KB               (BIT0)        // 001b: 1KB
#define     SATA_MUL_PIO_DRQ_SIZE_2KB               (BIT1)        // 010b: 2KB
#define     SATA_MUL_PIO_DRQ_SIZE_4KB               (BIT0 | BIT1) // 011b: 4KB
#define     SATA_MUL_PIO_DRQ_SIZE_8KB               (BIT2)        // 1xxb: 8KB
#define     SATA_FW_ASR_CLINTCLR_BIT                (BIT15) // FW send REG_CLINTCLR to clear OOB_MINT & OOB_CINT

#define R16_SATA_DATA_TRG                           (0x24 >> 1)
#define R8_SATA_DATA_TRG                            (0x24)
#define     SATA_DATA_TRG_BIT                       (BIT0)
#define     SATA_APU_RST_BIT                        (BIT1)
#define     SATA_TRG_INFO_BIT                       (BIT3)
#define     SATA_CTRL_RFIFO_RST_BIT                 (BIT4)
#define     SATA_CMD_ENDREQ_BIT                     (BIT8)
#define     SATA_CMD_ABRTREQ_BIT                    (BIT9)
#define     SATA_FIFO_HOLD_THR_SHIFT                (10)
#define     SATA_FIFO_HOLD_THR_MASK                 (BIT_MASK(2))
#define     SATA_FIFO_HOLD_THR_40DWS                (0x00)
#define     SATA_FIFO_HOLD_THR_42DWS                (0x01)
#define     SATA_FIFO_HOLD_THR_46DWS                (0x03)
#define     SATA_HOLD_NCQ_DIS_BIT                   (BIT15)

#define R16_SATA_AUTO_FIS_CTRL                      (0x26 >> 1)
#define     SATA_PIO_AUTO_REG_W_BIT                 (BIT0)
#define     SATA_DMA_AUTO_REG_R_BIT                 (BIT1)
#define     SATA_DMA_AUTO_REG_W_BIT                 (BIT2)
#define     SATA_NCQ_AUTO_SDB_R_BIT                 (BIT3)
#define     SATA_NCQ_AUTO_SDB_W_BIT                 (BIT4)
#define     SATA_NCQ_AUTO_REG_BIT                   (BIT5)
#define     SATA_COMRI_ASR_APU_RST_BIT              (BIT8)
#define     SATA_SRST_ASR_APU_RST_BIT               (BIT9)
#define     SATA_DERR_ASR_APU_RST_BIT               (BIT10)
#define     SATA_NCQ_ASR_XHOLD_BIT                  (BIT11)
#define     SATA_E_STATUS_SEL_BIT                   (BIT12)
#define     SATA_WR_BLK_EN_BIT                      (BIT13)
#define     SATA_NONDATA_RETRY_SET_SHIFT            (14)
#define     SATA_NONDATA_RETRY_SET_MASK             (BIT_MASK(2))
#define     SATA_REG_ALL_AUTO_FIS_EN                (BIT0 | BIT1 | BIT2 | BIT3 | BIT4 | BIT5) // 0x3F
#define     SATA_REG_ALL_AUTO_FIS_DIS               (~SATA_REG_ALL_AUTO_FIS_EN)
#define     SATA_REG_ALL_AUTO_FIS_R_EN              (BIT1 | BIT3 | BIT5) // 0x2A, Turn on only read auto FIS
#define     SATA_REG_ALL_AUTO_FIS_R_DIS             (~SATA_REG_ALL_AUTO_FIS_R_EN)
#define     SATA_PIO_DMA_SDB_AUTO_FIS_EN            (BIT0 | BIT1 | BIT2 | BIT3 | BIT4) // 0x1F
#define     SATA_PIO_DMA_SDB_AUTO_FIS_DIS           (~SATA_PIO_DMA_SDB_AUTO_FIS_EN)
#define     SATA_REG_DMA_SDB_AUTO_FIS_R_EN          (BIT1 | BIT3)
#define     SATA_REG_DMA_SDB_AUTO_FIS_R_DIS         (~SATA_REG_DMA_SDB_AUTO_FIS_R_EN)
#define     SATA_REG_NCQ_AUTO_REG_EN                (BIT5)
#define     SATA_REG_NCQ_AUTO_REG_DIS               (~SATA_REG_NCQ_AUTO_REG_EN)

#define R32_SATA_BUF_TRG_SEC                        (0x28 >> 2)
#define     SATA_BUF_TRG_SEC_SHIFT                  (0)
#define     SATA_BUF_TRG_SEC_MASK                   (BIT_MASK(17))

#define R32_SATA_CMD_REMAIN_SEC                     (0x2C >> 2)
#define     SATA_CMD_REMAIN_SEC_SHIFT               (0)
#define     SATA_CMD_REMAIN_SEC_MASK                (BIT_MASK(17))

#define R16_SATA_TRIM                               (0x38 >> 1)
#define     SATA_TRIM_EN_BIT                        (BIT0)
#define     SATA_TRIM_ERR_BIT                       (BIT8)

#define R8_SATA_REG_REQ                             (0x3A)
#define     SATA_REG_REQ_BIT                        (BIT0)
#define     SATA_REG_IBIT_BIT                       (BIT1)

#define R8_SATA_SDB_REQ                             (0x3B)
#define     SATA_SDB_REQ_BIT                        (BIT0)
#define     SATA_SDB_IBIT_BIT                       (BIT1)

#define R16_SATA_ERR_CTRL                           (0x3C >> 1)
#define     SATA_REG_CRC_TDATA_P_ERR_BIT            (BIT0)
#define     SATA_REG_CONTOFF_BIT                    (BIT1)
#define     SATA_REG_CRC_REGERR_BIT                 (BIT2)
#define     SATA_REG_CRC_DATAERR_BIT                (BIT3)
#define     SATA_REG_CRC_DIR_BIT                    (BIT4)
#define     SATA_APU_ERROR_INSERT_BIT               (BIT5)
#define     SATA_REG_XHOLD_BIT                      (BIT6)
#define     SATA_REG_MASK_DMAT_BIT                  (BIT7)
#define     SATA_FORCE_SIZE_ERR_BIT                 (BIT8)
#define     SATA_DIS_EXCEDD_DATA_BIT                (BIT9)
#define     SATA_BLK_TRG_BIT                        (BIT10)
#define     SATA_UPD_L_CMD_ONLY_BIT                 (BIT11)
#define     SATA_CLR_DUP_CMD_LOG_BIT                (BIT12)
#define     SATA_SMART_TMR_CTRL_BIT                 (BIT13)
#define     SATA_FORCE_HOLD_BIT                     (BIT14)
#define     SATA_INTERVAL_TMR_EN_BIT                (BIT15)

#define R16_SATA_SATA_INFO                          (0x3E >> 1)
#define     SATA_DEVSLP_BIT                         (BIT0)
#define     SATA_PARTIAL_BIT                        (BIT1)
#define     SATA_SLUMBER_BIT                        (BIT2)
#define     SATA_LNK_PMDENY_BIT                     (BIT3)
#define     SATA_LNK_BUSYDENY_BIT                   (BIT4)
#define     SATA_LNK_PHYRDY_BIT                     (BIT5)
#define     SATA_LNK_IDLEST_BIT                     (BIT6)
#define     SATA_TRS_IDLEST_BIT                     (BIT7)
#define     SATA_PLLLOCK_BIT                        (BIT8)
#define     SATA_LNK_ESTABLISH_BIT                  (BIT9)
#define     SATA_MCLKMCK_BIT                        (BIT10)
#define     SATA_HW_DENY_DIPM_BIT                   (BIT12)
#define     SATA_COMRI_ACT_BIT                      (BIT13)
#define     LNK_ESTSPD_SHIFT                        (14)
#define     LNK_ESTSPD_MASK                         (BIT_MASK(2))
#define R8_SATA_GEN                                 (0x3F)

#define R32_SATA_ERR_INT_EN                         (0x40 >> 2)
#define     SATA_E3D_INT_EN_BIT                     (BIT0)
#define     SATA_LNK_DB_ERR_INT_EN_BIT              (BIT1)
#define     SATA_PARITY_INT_EN_BIT                  (BIT2)
#define     SATA_LNK_NOCOMERR_INT_EN_BIT            (BIT4)
#define     SATA_LNK_DH_ERR_INT_EN_BIT              (BIT5)
#define     SATA_LNK_DS_ERR_INT_EN_BIT              (BIT6)
#define     SATA_TRS_DT_ERR_INT_EN_BIT              (BIT7)
#define     SATA_TRS_DF_ERR_INT_EN_BIT              (BIT8)
#define     SATA_D2H_D_RERR_INT_EN_BIT              (BIT9)
#define     SATA_H2D_D_RERR_INT_EN_BIT              (BIT10)
#define     SATA_D2H_ND_RERR_INT_EN_BIT             (BIT11)
#define     SATA_H2D_ND_RERR_INT_EN_BIT             (BIT12)
#define     SATA_D2H_ND_RETRY_INT_EN_BIT            (BIT13)
#define     SATA_H2D_RERR_CRC_INT_EN_BIT            (BIT14)
#define     SATA_H2D_RERR_NCRC_INT_EN_BIT           (BIT15)
#define     SATA_H2D_D_RERR_CRC_INT_EN_BIT          (BIT16)
#define     SATA_H2D_D_RERR_NCRC_INT_EN_BIT         (BIT17)
#define     SATA_H2D_ND_RERR_CRC_INT_EN_BIT         (BIT18)
#define     SATA_H2D_ND_RERR_NCRC_INT_EN_BIT        (BIT19)
#define     SATA_TRS_SYNCRCV_INT_EN_BIT             (BIT20)
#define     SATA_TRS_DACK_DMAT_INT_EN_BIT           (BIT21)
#define     SATA_TRS_DACK_SIZERR_INT_EN_BIT         (BIT22)
#define     SATA_TRS_FISACK_SYNC_INT_EN_BIT         (BIT23)
#define     SATA_SCT_W_TMO_INT_EN_BIT               (BIT24)
#define     SATA_TRS_DRERREND_INT_EN_BIT            (BIT25)
#define     SATA_FIFO_UNOVFLOW_INT_EN_BIT           (BIT26)
#define     SATA_RSPERR_P_INT_EN_BIT                (BIT27)
#define     SATA_RCV_UNEXP_CMD_INT_EN_BIT           (BIT28)
#define     SATA_RD_UNC_INT_EN_BIT                  (BIT29)
#define     SATA_PHY_EVT_CNT_OV_INT_EN_BIT          (BIT30)
#define     SATA_SCT_R_TMO_INT_EN_BIT               (BIT31)

#define R32_SATA_ERR_INT_FLAG                       (0x44 >> 2)
#define     SATA_E3D_INT_BIT                        (BIT0)
#define     SATA_LNK_DB_ERR_INT_BIT                 (BIT1)
#define     SATA_PARITY_INT_BIT                     (BIT2)
#define     SATA_LNK_NOCOMERR_INT_BIT               (BIT4)
#define     SATA_LNK_DH_ERR_INT_BIT                 (BIT5)
#define     SATA_LNK_DS_ERR_INT_BIT                 (BIT6)
#define     SATA_TRS_DT_ERR_INT_BIT                 (BIT7)
#define     SATA_TRS_DF_ERR_INT_BIT                 (BIT8)
#define     SATA_D2H_D_RERR_INT_BIT                 (BIT9)
#define     SATA_H2D_D_RERR_INT_BIT                 (BIT10)
#define     SATA_D2H_ND_RERR_INT_BIT                (BIT11)
#define     SATA_H2D_ND_RERR_INT_BIT                (BIT12)
#define     SATA_D2H_ND_RETRY_INT_BIT               (BIT13)
#define     SATA_H2D_RERR_CRC_INT_BIT               (BIT14)
#define     SATA_H2D_RERR_NCRC_INT_BIT              (BIT15)
#define     SATA_H2D_D_RERR_CRC_INT_BIT             (BIT16)
#define     SATA_H2D_D_RERR_NCRC_INT_BIT            (BIT17)
#define     SATA_H2D_ND_RERR_CRC_INT_BIT            (BIT18)
#define     SATA_H2D_ND_RERR_NCRC_INT_BIT           (BIT19)
#define     SATA_TRS_SYNCRCV_INT_BIT                (BIT20)
#define     SATA_TRS_DACK_DMAT_INT_BIT              (BIT21)
#define     SATA_TRS_DACK_SIZERR_INT_BIT            (BIT22)
#define     SATA_TRS_FISACK_SYNC_INT_BIT            (BIT23)
#define     SATA_SCT_W_TMO_INT_BIT                  (BIT24)
#define     SATA_TRS_DRERREND_INT_BIT               (BIT25)
#define     SATA_FIFO_UNOVFLOW_INT_BIT              (BIT26)
#define     SATA_RSPERR_P_INT_BIT                   (BIT27)
#define     SATA_RCV_UNEXP_CMD_INT_BIT              (BIT28)
#define     SATA_RD_UNC_INT_BIT                     (BIT29)
#define     SATA_PHY_EVT_CNT_OV_INT_BIT             (BIT30)
#define     SATA_SCT_R_TMO_INT_BIT                  (BIT31)

#define R32_SATA_MISC                               (0x48 >> 2)
#define     SATA_BUF_NEARFUL_REG_OUT_EN_BIT         (BIT0)
#define     SATA_HW_AUTO_DIPM_CLR_SEL_BIT           (BIT1)
#define     SATA_PHY_EVT_CNT_00A_CTRL_BIT           (BIT2)
#define     SATA_AUTO_SDB_SEL_BIT                   (BIT3)
#define     SATA_OOB_RCV_INT_SEL_SHIFT              (4)
#define     SATA_OOB_RCV_INT_SEL_MASK               (BIT_MASK(2))
#define     SATA_LAST_CMD_TYPE_SHIFT                (6)
#define     SATA_LAST_CMD_TYPE_MASK                 (BIT_MASK(2))
#define     SATA_TQ_BY_ORDER_BIT                    (BIT10)
#define     SATA_AUTO_PMNAK_BIT                     (BIT11)
#define     SATA_TQ_TRG_DLY_SEL_BIT                 (BIT12)
#define     SATA_DIPM_BLK_EN_BIT                    (BIT13)
#define     SATA_RD_DLY_EN_BIT                      (BIT14)
#define     SATA_TQ_DLY_EN_BIT                      (BIT15)
#define     SATA_MISC_IDLE_THR_SHIFT                (16)
#define     SATA_MISC_IDLE_THR_MASK                 (BIT_MASK(16))

#define R8_SATA_PM_CTRL                             (0x4C)
#define R16_SATA_PM_CTRL                            (0x4C >> 1)
#define     SATA_REG_PMACK_P_BIT                    (BIT0)
#define     SATA_REG_PMNAK_P_BIT                    (BIT1)
#define     SATA_REG_PMACK_S_BIT                    (BIT2)
#define     SATA_REG_PMNAK_S_BIT                    (BIT3)
#define     SATA_REG_PMREQ_P_BIT                    (BIT4)
#define     SATA_REG_PMREQ_S_BIT                    (BIT5)
#define     SATA_REG_PMCANCEL_BIT                   (BIT6)
#define     SATA_IPM_EN_BIT                         (BIT7)
#define     SATA_AUTO_ACK_HIPM_BIT                  (BIT8)
#define     SATA_COMRI_ACT_DENY_IPM_BIT             (BIT9)
#define     SATA_FORCE_IPM_NAK_BIT                  (BIT10)
#define     SATA_CR_OOBN_IPMACK_BIT                 (BIT11)
#define     SATA_CR_OOB_WAKE_EN_BIT                 (BIT12)
#define     SATA_HW_AUTO_DIPM_PS_BIT                (BIT13)
#define     SATA_HW_AUTO_DIPM_EN_BIT                (BIT14)
#define     SATA_REG_APSREQ_BIT                     (BIT15)

#define R8_SATA_PM_CTRL2                            (0x4FC)
#define     SATA_HIPM_AUTO_NAK_BIT                  (BIT0) // This bit has high priority then H_AUTO_ACK_HIPM (bit8) of HW_PM_CTRL

#define R16_SATA_DIPM_TIMER_THR                     (0x4E >> 1)
#define R8_SATA_DIPM_TIMER_THR                      (0x4E)
#define     SATA_DIPM_AUTO_TMR_THR_SHIFT            (0)
#define     SATA_DIPM_AUTO_TMR_THR_MASK             (BIT_MASK(7))
#define     SATA_DIPM_AUTO_TMR_UNIT_SEL_BIT         (BIT7) // DIPM Auto Timer Unit Select (DIPM_AUTO_TMR_UNIT_SEL_US) is defined in SPEC (0:us, 1:ms)
#define     SATA_DIPM_AUTO_P2S_THR_SHIFT            (8)    // DIPM auto wake from Partial and enter Slumber timer Threshold (DIPM_AUTO_WAKE_THEN_SLUMBER_THR)
#define     SATA_DIPM_AUTO_P2S_THR_MASK             (BIT_MASK(8))
#define R8_SATA_DIPM_AUTO_P2S_THR                   (0x4F)

#define R16_SATA_AUTOP2S_THR                        (0x52 >> 1) // HB_DIPM_AUTO_P2S_THR need to clear to zero

#define R16_SATA_CONT_LBA_TAG                       (0x50 >> 1)
#define     SATA_CONT_LBA_TAG_SHIFT                 (0)
#define     SATA_CONT_LBA_TAG_MASK                  (BIT_MASK(5))
#define     SATA_CUR_TRG_NCQ_CONT_BIT               (BIT8)

#define R32_SATA_CMD_TSF_SEC                        (0x54 >> 2) // Total transferred sectors
#define     SATA_CMD_TSF_SEC_SHIFT                  (0)
#define     SATA_CMD_TSF_SEC_MASK                   (BIT_MASK(17))

#define R64_SATA_LAST_END_LBA                       (0x5C8 >> 3)
#define     SATA_LAST_END_LBA_SHIFT                 (0)
#define     SATA_LAST_END_LBA_MASK                  (BIT_MASK64(40))

#define R64_SATA_MAX_LBA_CHS                        (0x58 >> 3)
#define		R64_SATA_MAX_LBA_CHS_SHIFT              (0)
#define		R64_SATA_MAX_LBA_CHS_MASK               (BIT_MASK64(40))

#define R64_SATA_MAX_LBA                            (0x60 >> 3) // 0x60 ~ 0x67, Include R8_SATA_MAX_HEAD &  R8_SATA_MAX_SECTOR
#define 	R64_SATA_MAX_LBA_SHIFT                  (0)
#define 	R64_SATA_MAX_LBA_MASK                   (BIT_MASK64(40))
#define R8_SATA_MAX_HEAD                            (0x66)
#define     SATA_MAX_HEAD_SHIFT                     (0)
#define     SATA_MAX_HEAD_MASK                      (BIT_MASK(5))
#define R8_SATA_MAX_SECTOR                          (0x67)

#define R16_SATA_MAX_CYLINDER                       (0x68 >> 1)

#define R16_SATA_CMD_LBA_INFO                       (0x6A >> 1)
#define     SATA_C2L_ACT_BIT                        (BIT0)
#define     SATA_C2L_S_ERR_BIT                      (BIT1)
#define     SATA_C2L_E_ERR_BIT                      (BIT2)
#define     SATA_LBA28_S_ERR_BIT                    (BIT3)
#define     SATA_LBA28_E_ERR_BIT                    (BIT4)
#define     SATA_LBA_S_ERR_BIT                      (BIT5)
#define     SATA_LBA_E_ERR_BIT                      (BIT6)
#define     SATA_LBA_MODE_BIT                       (BIT7)
#define     SATA_AES_LBA_ERR_BIT                    (BIT15)

#define R8_SATA_LBA_CHK_EN                          (0x6F)
#define     SATA_LBA_CHK_EN_BIT                     (BIT0)

#define R64_SATA_CMD_S_LBA                          (0x5D8 >> 3) // Command start LBA register
#define     SATA_CMD_S_LBA_SHIFT                    (0)
#define     SATA_CMD_S_LBA_MASK                     (BIT_MASK64(40))

#define R8_SATA_AES_CTRL                            (0x70)
#define     SATA_AES_CHK_LBA_EN_BIT                 (BIT1)

#define R16_SATA_AXI_CTRL                           (0x72 >> 1)
#define     SATA_AXI_RD_BURST_EN_BIT                (BIT0)
#define     SATA_AXI_WR_BURST_EN_BIT                (BIT1)
#define     SATA_WR_WAIT_AXI_FLUSH_BIT              (BIT5)
#define     SATA_WR_DLY_FIFO_TSF_BIT                (BIT7)
#define     SATA_FORCE_APU_RST_BIT                  (BIT8)

#define R32_SATA_CMD_ZERO_INFO                      (0x9C >> 2)

#define R32_SATA_E3D_PARITY_ERROR                   (0xA0 >> 2)
#define     SATA_E3D_BLK_DATA_BIT                   (BIT4)
#define     SATA_E3D_ERR_INS_BIT                    (BIT5)
#define     SATA_PARITY_CHK_EN_BIT                  (BIT9)
#define     SATA_PAR_ERR_INS_BIT                    (BIT10)
#define     SATA_RCV_PAR_ERR_INS_BIT                (BIT11)
#define     SATA_PAR_BLK_DATA_BIT                   (BIT12)
#define     SATA_E3D_ERR_BIT                        (BIT15)
#define     SATA_PARITY_ERR_BIT                     (BIT23)

#define R8_SATA_MZ_LEN                              (0xA3)
#define     SATA_RRESP_INS_BIT                      (BIT0)
#define     SATA_RESERVED_BIT                       (BIT1)
#define     SATA_MZ_LEN_SHIFT                       (2)
#define     SATA_MZ_LEN_MASK                        (BIT_MASK(6))

#define R32_SATA_AUTO_STATUS                        (0xB4 >> 2)
#define R8_SATA_AUTO_SDB_STATUS                     (0xB4)
#define R8_SATA_AUTO_D2H_STATUS                     (0xB6)

#define R64_SATA_RD_UNC_LBA                         (0x5E0 >> 3)
#define     SATA_RD_UNC_LBA_SHIFT                   (0)
#define     SATA_RD_UNC_LBA_MASK                    (BIT_MASK64(40))

#define R64_SATA_AES_CHK_LBA_S                      (0x5E8 >> 3)
#define     SATA_AES_CHK_LBA_S_SHIFT                (0)
#define     SATA_AES_CHK_LBA_S_MASK                 (BIT_MASK64(40))

#define R64_SATA_AES_CHK_LBA_E                      (0x5F0 >> 3)
#define     SATA_AES_CHK_LBA_E_SHIFT                (0)
#define     SATA_AES_CHK_LBA_E_MASK                 (BIT_MASK64(40))

#define R8_SATA_AES_CHK_LBA_TRG                     (0xF0)
#define     SATA_AES_CHK_LBA_TRG_BIT                (BIT0)

#define R8_SATA_AES_CHK_LBA_GRP                     (0xF1)
#define     SATA_AES_CHK_LBA_GRP_SHIFT              (0)
#define     SATA_AES_CHK_LBA_GRP_MASK               (BIT_MASK(5))

#define R8_SATA_AES_CHK_LBA_SUM                     (0xF2)
#define     SATA_AES_CHK_LBA_SUM_SHIFT              (0)
#define     SATA_AES_CHK_LBA_SUM_MASK               (BIT_MASK(5))

#define R64_SATA_AES_CHK_LBA_LEN                    (0x5F8 >> 3)
#define     SATA_AES_CHK_LBA_LEN_SHIFT              (0)
#define     SATA_AES_CHK_LBA_LEN_MASK               (BIT_MASK64(40))

#define R8_SATA_W_HOLD_THR                          (0x2DC)
#define     SATA_W_HOLD_THR_SHIFT                   (0)
#define     SATA_W_HOLD_THR_MASK                    (BIT_MASK(7))

#define R32_SATA_R_SEC_SUM                          (0x2F8 >> 2)

#define R32_SATA_W_SEC_SUM                          (0x2FC >> 2)

#define R32_SATA_R_SUM                              (0x320 >> 2)
#define R32_SATA_W_SUM                              (0x324 >> 2) // Host write sum register

/*=====================================================================================
                     NCQ Command Related Register (see Table 4)
=====================================================================================*/

#define R8_SATA_MTQ_VLD_NUM                         (0xC3)
#define     SATA_MTQ_VLD_NUM_SHIFT                  (0)
#define     SATA_MTQ_VLD_NUM_MASK                   (BIT_MASK(5))

#define R32_SATA_NCQ_LBA_OVLP_TAG                   (0xCC >> 2)

#define R32_SATA_NCQ_VLD_BIT                        (0xD0 >> 2)

#define R32_SATA_VLD_NCQ_NUM                        (0xD4 >> 2)
#define     SATA_NCQ_VLD_NUM_SHIFT                  (0)
#define     SATA_NCQ_VLD_NUM_MASK                   (BIT_MASK(6))
#define     SATA_TQ_VLD_NUM_SHIFT                   (8)
#define     SATA_TQ_VLD_NUM_MASK                    (BIT_MASK(4))
#define     SATA_QD1_CNT_SHIFT                      (16)
#define     SATA_QD1_CNT_MASK                       (BIT_MASK(4))
#define     SATA_NCQ_OVLP_NUM_SHIFT                 (24)
#define     SATA_NCQ_OVLP_NUM_MASK                  (BIT_MASK(6))
#define R8_SATA_NCQ_VLD_NUM                         (0xD4)
#define R8_SATA_TQ_VLD_NUM                          (0xD5)
#define R8_SATA_QD1_CNT                             (0xD6)
#define R8_SATA_NCQ_OVLP_NUM                        (0xD7)

#define R32_SATA_TQ_CTRL                            (0xD8 >> 2)
#define     SATA_ACC_SDB_EN_BIT                     (BIT9)
#define     SATA_NCQ_OVLAP_CHK_BIT                  (BIT10)
#define     SATA_ACC_SDB_NUM_SHIFT                  (16)
#define     SATA_ACC_SDB_NUM_MASK                   (BIT_MASK(6))
#define     SATA_ACC_SDB_IDLE_SHIFT                 (24)
#define     SATA_ACC_SDB_IDLE_MASK                  (BIT_MASK(5))

#define R16_SATA_NCQ_DLY_FIS                        (0xDC >> 1)
#define     SATA_NCQ_DLY_CNT_SHIFT                  (0)
#define     SATA_NCQ_DLY_CNT_MASK                   (BIT_MASK(6))
#define     SATA_DLY_SDB_FIS_EN_BIT                 (BIT6)
#define     SATA_DLY_DMA_SUP_FIS_SHIFT              (7)
#define     SATA_DLY_DMA_SUP_FIS_EN_BIT             (BIT7)
#define     SATA_NCQ_DLY_CMD_THR_SHIFT              (8)
#define     SATA_NCQ_DLY_CMD_THR_MASK               (BIT_MASK(5))

#define R16_SATA_TQ_TRG_DLY                         (0xDE >> 1)

#define R16_SATA_CMD_REMAIN_EC                      (0xE0 >> 1)  // host command remain entry count register
#define     SATA_CMD_REMAIN_EC_SHIFT                (0)
#define     SATA_CMD_REMAIN_EC_MASK                 (BIT_MASK(14))

#define R64_SATA_NCQ_LBA(N)                         ((0x600 + 8 * (N)) >> 3)
#define     SATA_NCQN_LBA_SHIFT                     (0)
#define     SATA_NCQN_LBA_MASK                      (BIT_MASK64(40))

#define R32_SATA_NCQ_INFO(N)                        ((0x180 + 4 * (N)) >> 2)
#define     SATA_NCQN_SEC_CNT_SHIFT                 (0)
#define     SATA_NCQN_SEC_CNT_MASK                  (BIT_MASK(16))
#define     SATA_NCQN_FUA_BIT                       (BIT16)
#define     SATA_NCQN_PRIO_BIT                      (BIT17)
#define     SATA_NCQN_WR_BIT                        (BIT18)
#define     SATA_NCQN_VLD_BIT                       (BIT19)
#define     SATA_NCQN_EOFST_SHIFT                   (20)
#define     SATA_NCQN_EOFST_MASK                    (BIT_MASK(3))
#define     SATA_NCQN_DEVICE_SHIFT                  (24)
#define     SATA_NCQN_DEVICE_MASK                   (BIT_MASK(8))

#define R32_SATA_NCQ_AUXILIARY(N)                   ((0x500 + 4 * (N)) >> 2)

#define R16_SATA_NCQ_ENTRY_COUNT(N)                 ((0x340 + 2 * (N)) >> 1)
#define     SATA_NCQN_EC_SHIFT                      (0)
#define     SATA_NCQN_EC_MASK                       (BIT_MASK(14)) //command entry count of a received NCQ command

#define R16_SATA_TQ_INFO                            (0xE2 >> 1)
#define     SATA_CUR_TQ_NUM_SHIFT                   (0)
#define     SATA_CUR_TQ_NUM_MASK                    (BIT_MASK(5))
#define     SATA_TQ_ACT_BIT                         (BIT8)

#define R32_SATA_TQ_VLD_BITS                        (0xE4 >> 2)

#define R8_SATA_TQ_RD_SUM                           (0x330)
#define     SATA_TQ_RD_SUM_SHIFT                    (0)
#define     SATA_TQ_RD_SUM_MASK                     (BIT_MASK(6))
#define R8_SATA_TQ_WR_SUM                           (0x331)
#define     SATA_TQ_WR_SUM_SHIFT                    (0)
#define     SATA_TQ_WR_SUM_MASK                     (BIT_MASK(6))

#define R32_SATA_TQ_VLD_TAG                         (0x334 >> 2)

#define R8_SATA_TQ_VLDB_31                          (0x4A0)
#define R8_SATA_TQ_VLDB_30                          (0x4A4)

#define R32_SATA_MTQ_VLDB(N)                        ((0x4A8 + 4 * (N)) >> 2)
#define     SATA_MTQ_VLDB_SHIFT                     (0)
#define     SATA_MTQ_VLDB_MASK                      (BIT_MASK(8))

/*=====================================================================================
                        Debug Register (see Table 5 & 6)
=====================================================================================*/
// Spec no define
#define R8_SATA_DEBUG_PORT_0xB9                     (0xB9)
#define     SATA_BLK_FIS_BIT                        (BIT0)

#define R8_SATA_DEBUG_PORT_0xBA                     (0xBA)
#define     SATA_NOT_REPLY_D2H_BIT                  (BIT0)

#define R8_SATA_NON_NCQ_IDLE                        (0xBB) // This register is used to check the non-NCQ cmd is completed except the PIO read
#define     SATA_NON_NCQ_BUSY_BIT                   (BIT0)

// SATA_DUPE_CMD_BLK_1 ~ 5 with same structure
#define R32_SATA_DUPE_CMD_BLK_N_1(N)                ((0x200 + 16 * (5 - (N))) >> 2)
#define     SATA_DUPE_CMD_BLK_FEATURE_SHIFT         (0)  // NCQ Size - FEATURE
#define     SATA_DUPE_CMD_BLK_FEATURE_MASK          (BIT_MASK(8))
#define     SATA_DUPE_CMD_BLK_FEATURE_EXP_SHIFT     (8)
#define     SATA_DUPE_CMD_BLK_FEATURE_EXP_MASK      (BIT_MASK(8))
#define     SATA_DUPE_CMD_BLK_SECTOR_SHIFT          (16) // NCQ Tag - SECTOR[7:3]
#define     SATA_DUPE_CMD_BLK_SECTOR_MASK           (BIT_MASK(8))
#define     SATA_DUPE_CMD_BLK_SECTOR_EXP_SHIFT      (24)
#define     SATA_DUPE_CMD_BLK_SECTOR_EXP_MASK       (BIT_MASK(8))

#define R32_SATA_DUPE_CMD_BLK_N_2(N)                ((0x204 + 16 * (5 - (N))) >> 2)
#define     SATA_DUPE_CMD_BLK_LBA_LOW_SHIFT         (0)
#define     SATA_DUPE_CMD_BLK_LBA_LOW_MASK          (BIT_MASK(8))
#define     SATA_DUPE_CMD_BLK_LBA_MID_SHIFT         (8)
#define     SATA_DUPE_CMD_BLK_LBA_MID_MASK          (BIT_MASK(8))
#define     SATA_DUPE_CMD_BLK_LBA_HIGH_SHIFT        (16)
#define     SATA_DUPE_CMD_BLK_LBA_HIGH_MASK         (BIT_MASK(8))
#define     SATA_DUPE_CMD_BLK_LBA_LOW_EXP_SHIFT     (24)
#define     SATA_DUPE_CMD_BLK_LBA_LOW_EXP_MASK      (BIT_MASK(8))

#define R32_SATA_DUPE_CMD_BLK_N_3(N)                ((0x208 + 16 * (5 - (N))) >> 2)
#define     SATA_DUPE_CMD_BLK_LBA_MID_EXP_SHIFT     (0)
#define     SATA_DUPE_CMD_BLK_LBA_MID_EXP_MASK      (BIT_MASK(8))
#define     SATA_DUPE_CMD_BLK_LBA_HIGH_EXP_SHIFT    (8)
#define     SATA_DUPE_CMD_BLK_LBA_HIGH_EXP_MASK     (BIT_MASK(8))
#define     SATA_DUPE_CMD_BLK_DEVICE_SHIFT          (16) // FUA bit - DEVICE[7]
#define     SATA_DUPE_CMD_BLK_DEVICE_MASK           (BIT_MASK(8))
#define     SATA_DUPE_CMD_BLK_COMMAND_SHIFT         (24)
#define     SATA_DUPE_CMD_BLK_COMMAND_MASK          (BIT_MASK(8))

#define R32_SATA_DUPE_CMD_BLK_N_4(N)                ((0x20C + 16 * (5 - (N))) >> 2)
#define     SATA_DUPE_CMD_BLK_CONTROL_SHIFT         (0)
#define     SATA_DUPE_CMD_BLK_CONTROL_MASK          (BIT_MASK(8))

#define R32_SATA_RCV_CMD_TMR(N)                     ((0x250 + 4 * (5 - (N))) >> 2)

#define R32_SATA_CUR_SMART_TMR                      (0x264 >> 2)

#define R32_SATA_SCT_TMR                            (0x268 >> 2)
#define R16_SATA_SCT_W_TMR                          (0x268 >> 1)
#define R16_SATA_SCT_R_TMR                          (0x26A >> 1)

#define R16_SATA_SCT_NCQ_SUM                        (0x26C >> 1)
#define     SATA_SCT_NCQ_WR_SUM_SHIFT               (0)
#define     SATA_SCT_NCQ_WR_SUM_MASK                (BIT_MASK(6))
#define     SATA_SCT_NCQ_RD_SUM_SHIFT               (8)
#define     SATA_SCT_NCQ_RD_SUM_MASK                (BIT_MASK(6))
#define R8_SATA_SCT_NCQ_WR_SUM                      (0x26C)
#define R8_SATA_SCT_NCQ_RD_SUM                      (0x26D)

#define R16_SATA_SCT_W1_TMR                         (0x308 >> 1)
#define R16_SATA_SCT_R1_TMR                         (0x30A >> 1)

#define R16_SATA_SCT_W2_TMR                         (0x30C >> 1)
#define R16_SATA_SCT_R2_TMR                         (0x30E >> 1)

#define R32_SATA_SCT_W1_TMO_CNT                     (0x310 >> 2)
#define R32_SATA_SCT_R1_TMO_CNT                     (0x314 >> 2)
#define R32_SATA_SCT_W2_TMO_CNT                     (0x318 >> 2)
#define R32_SATA_SCT_R2_TMO_CNT                     (0x31C >> 2)

// Non-NCQ: 1st Cmd -> R32_SATA_INTERVAL_TMR[0], 2nd Cmd -> R32_SATA_INTERVAL_TMR[1], ...
// NCQ: Tag 0 -> R32_SATA_INTERVAL_TMR[0], Tag 1 -> R32_SATA_INTERVAL_TMR[1], ... , Tag 31 -> R32_SATA_INTERVAL_TMR[31],
#define R32_SATA_INTERVAL_TMR(N)                    ((0x100 + 4 * (N)) >> 2) // unit: ms

#define R8_SATA_INTERVAL_TMR_PTR                    (0xFC)
#define     SATA_INTERVAL_TMR_PTR_SHIFT             (0)
#define     SATA_INTERVAL_TMR_PTR_MASK              (BIT_MASK(5))

#define R32_SATA_INTERVAL_TMR_CLR                   (0xE8 >> 2)
#define R32_SATA_INTERVAL_TMR_STOP                  (0xEC >> 2)
#define R32_SATA_INTERVAL_TMR_THR                   (0xF4 >> 2)

#define R32_SATA_INTERVAL_TMR_INT_TABLE             (0x338 >> 2)
#define R32_SATA_INTERVAL_TMR_INT_TABLE_CLR         (0x33C >> 2)

#define R32_SATA_IDLE_TMR                           (0x4F8 >> 2)

#define R32_SATA_IDLE_TMR_THR                       (0x4F4 >> 2)

#define R8_SATA_IDLE_TMR_STOP                       (0x4F0)
#define     SATA_IDLE_TMR_STOP_BIT                  (BIT0)
#define     SATA_IDLE_TMR_EN_BIT                    (BIT1)

#define R32_SATA_DBG_CTRL                           (0x270 >> 2)
#define     SATA_SATA_CTRL_DBG_SEL_SHIFT            (0)
#define     SATA_SATA_CTRL_DBG_SEL_MASK             (BIT_MASK(4))
#define     SATA_SCT_HW_BLK_EN_BIT                  (BIT4)
#define     SATA_DBG_PORT_EN_BIT                    (BIT8)
#define     SATA_DBG_HLDW_SEL_BIT                   (BIT9)
#define     AA_SWITCH_ON_LA_BIT                     (BIT10)
#define     SATA_APU_ARC_INFX_DBG_EN_BIT            (BIT11)
#define     SATA_APU_ARC_INFX_DBG_SEL_SHIFT         (12)
#define     SATA_APU_ARC_INFX_DBG_SEL_MASK          (BIT_MASK(4))
#define     SATA_NCQ_EXPR_THR_SHIFT                 (16)
#define     SATA_NCQ_EXPR_THR_MASK                  (BIT_MASK(8))

#define R64_SATA_DBG_PORT                           (0x274 >> 3)
#define     SATA_DBG_PORT_SHIFT                     (0)
#define     SATA_DBG_PORT_MASK                      (BIT_MASK64(48))

#define R16_SATA_AXI_FIS_STAT                       (0x27A >> 1)
#define R8_SATA_AXI_FIS_STAT                        (0x27A)
#define     SATA_NCQ_UPD_BUSY_BIT                   (BIT0)
#define     D2H_BIT                                 (BIT1)
#define     SDB_BIT                                 (BIT2)
#define     DMA_SUP_BIT                             (BIT3)
#define     DMA_ACTIVE_BIT                          (BIT4)
#define     PIO_SUP_BIT                             (BIT5)
#define     CMD_BUSY_BIT                            (BIT7)
#define     SATA_RCV_SAME_CMD_BIT                   (BIT8)
#define     SATA_LAST_TRG_TYPE_SHIFT                (9)
#define     SATA_LAST_TRG_TYPE_MASK                 (BIT_MASK(2))
#define     SATA_LAST_TRG_WR_BIT                    (BIT11)

#define R8_SATA_PHY_EVT_CNT_001                     (0x280)
#define R8_SATA_PHY_EVT_CNT_003                     (0x281)
#define R8_SATA_PHY_EVT_CNT_004                     (0x282)
#define R8_SATA_PHY_EVT_CNT_006                     (0x283)

#define R8_SATA_PHY_EVT_CNT_007                     (0x284)
#define R8_SATA_PHY_EVT_CNT_008                     (0x285)
#define R8_SATA_PHY_EVT_CNT_009                     (0x286)
#define R8_SATA_PHY_EVT_CNT_00A                     (0x287)

#define R8_SATA_PHY_EVT_CNT_00F                     (0x288)
#define R8_SATA_PHY_EVT_CNT_010                     (0x289)
#define R8_SATA_PHY_EVT_CNT_012                     (0x28A)
#define R8_SATA_PHY_EVT_CNT_013                     (0x28B)

#define R32_SATA_PHY_EVT_CNT_CTRL                   (0x28C >> 2)
#define     SATA_PHY_EVT_CNT_EN_SHIFT               (0)
#define     SATA_PHY_EVT_CNT_EN_MASK                (BIT_MASK(12))
#define     SATA_PHY_EVT_CNT_OV_FLAG_SHIFT          (16)
#define     SATA_PHY_EVT_CNT_OV_FLAG_MASK           (BIT_MASK(12))
#define R16_SATA_PHY_EVT_CNT_EN                     (0x28C >> 1)
#define R16_SATA_PHY_EVT_CNT_OV_FLAG                (0x28E >> 1)
#define     SATA_ICRC_ERROR                         (BIT0)
#define     SATA_D2H_DATA_RERR                      (BIT1)
#define     SATA_H2D_DATA_RERR                      (BIT2)
#define     SATA_D2H_FIS_RERR                       (BIT3)
#define     SATA_H2D_FIS_RERR                       (BIT4)
#define     SATA_H2D_FIS_RETRY                      (BIT5)
#define     SATA_PHYRDY2N                           (BIT6)
#define     SATA_CRST_D2H                           (BIT7)
#define     SATA_H2D_DATA_RERR_CRC                  (BIT8)
#define     SATA_H2D_DATA_RERR_NON_CRC              (BIT9)
#define     SATA_H2D_FIS_RERR_CRC                   (BIT10)
#define     SATA_H2D_FIS_RERR_NON_CRC               (BIT11)

#define R16_SATA_NCQ_ORDER_INFO                     (0x290 >> 1)
#define     SATA_NCQN_TAG_SHIFT                     (0) // NCQ Order N Tag
#define     SATA_NCQN_TAG_MASK                      (BIT_MASK(5))
#define     SATA_NCQN_VLD_ORDER_BIT                 (BIT8) // NCQ Order 0 Valid
#define     SATA_NCQN_WR_ORDER_BIT                  (BIT9) // NCQ Order 0 R/W (0:Read, 1:Write)
#define     SATA_NCQN_SZ_BIT                        (BIT10)

#define R8_SATA_NCQ_ORDER_0_DLY_CNT                 (0x2D0)

#define R8_SATA_NCQ_ORDER_VLD_SUM                   (0x2D1)
#define     SATA_NCQ_ORDER_VLD_SUM_SHIFT            (0)
#define     SATA_NCQ_ORDER_VLD_SUM_MASK             (BIT_MASK(6))

#define R16_SATA_NCQ_ORDER_RD_CHK                   (0x2D2 >> 1)
#define R8_SATA_NCQ_R_CHK_SZ                        (0x2D2)
#define     SATA_NCQ_R_CHK_SZ_SEL_BIT               (BIT8)

#define R64_SATA_AES_GN                             (0x700 >> 3)
#define R64_SATA_AES_GN_OFFSET                      (0x10 >> 3)
#define R64_SATA_AES_GN_S_ENTRY1                    (0x000 >> 3)
#define     SATA_AES_GN_S_SHIFT                     (0)
#define     SATA_AES_GN_S_MASK                      (BIT_MASK64(40))
#define R64_SATA_AES_GN_E_ENTRY1                    (0x008 >> 3)
#define     SATA_AES_GN_E_SHIFT                     (0)
#define     SATA_AES_GN_E_MASK                      (BIT_MASK64(40))

#define R32_SATA_AES_RLOCK                          (0x480 >> 2)
#define     SATA_AES_RLOCK_SHIFT                    (0)
#define     SATA_AES_RLOCK_MASK                     (BIT_MASK(17))

#define R32_SATA_AES_WLOCK                          (0x484 >> 2)
#define     SATA_AES_WLOCK_SHIFT                    (0)
#define     SATA_AES_WLOCK_MASK                     (BIT_MASK(17))
#define     GLOBAL_REGION_LOCK_IDX                  (0)

#define R32_SATA_AES_IDX_1                          (0x488 >> 2)
#define     SATA_AES_G01_IDX_SHIFT                  (0)
#define     SATA_AES_G01_IDX_MASK                   (BIT_MASK(5))
#define     SATA_AES_G02_IDX_SHIFT                  (8)
#define     SATA_AES_G02_IDX_MASK                   (BIT_MASK(5))
#define     SATA_AES_G03_IDX_SHIFT                  (16)
#define     SATA_AES_G03_IDX_MASK                   (BIT_MASK(5))
#define     SATA_AES_G04_IDX_SHIFT                  (24)
#define     SATA_AES_G04_IDX_MASK                   (BIT_MASK(5))

#define R32_SATA_AES_IDX_2                          (0x48C >> 2)
#define     SATA_AES_G05_IDX_SHIFT                  (0)
#define     SATA_AES_G05_IDX_MASK                   (BIT_MASK(5))
#define     SATA_AES_G06_IDX_SHIFT                  (8)
#define     SATA_AES_G06_IDX_MASK                   (BIT_MASK(5))
#define     SATA_AES_G07_IDX_SHIFT                  (16)
#define     SATA_AES_G07_IDX_MASK                   (BIT_MASK(5))
#define     SATA_AES_G08_IDX_SHIFT                  (24)
#define     SATA_AES_G08_IDX_MASK                   (BIT_MASK(5))

#define R32_SATA_AES_IDX_3                          (0x490 >> 2)
#define     SATA_AES_G09_IDX_SHIFT                  (0)
#define     SATA_AES_G09_IDX_MASK                   (BIT_MASK(5))
#define     SATA_AES_G10_IDX_SHIFT                  (8)
#define     SATA_AES_G10_IDX_MASK                   (BIT_MASK(5))
#define     SATA_AES_G11_IDX_SHIFT                  (16)
#define     SATA_AES_G11_IDX_MASK                   (BIT_MASK(5))
#define     SATA_AES_G12_IDX_SHIFT                  (24)
#define     SATA_AES_G12_IDX_MASK                   (BIT_MASK(5))

#define R32_SATA_AES_IDX_4                          (0x494 >> 2)
#define     SATA_AES_G13_IDX_SHIFT                  (0)
#define     SATA_AES_G13_IDX_MASK                   (BIT_MASK(5))
#define     SATA_AES_G14_IDX_SHIFT                  (8)
#define     SATA_AES_G14_IDX_MASK                   (BIT_MASK(5))
#define     SATA_AES_G15_IDX_SHIFT                  (16)
#define     SATA_AES_G15_IDX_MASK                   (BIT_MASK(5))
#define     SATA_AES_G16_IDX_SHIFT                  (24)
#define     SATA_AES_G16_IDX_MASK                   (BIT_MASK(5))

#define R32_SATA_AES_IDX_5                          (0x498 >> 2)
#define     SATA_AES_G_IDX_SHIFT                    (0)
#define     SATA_AES_G_IDX_MASK                     (BIT_MASK(5))

#define R64_SATA_MBR_REGION_S                       (0x418 >> 3)
#define     SATA_MBR_S_SHIFT                        (0)
#define     SATA_MBR_S_MASK                         (BIT_MASK64(40))

#define R64_SATA_MBR_REGION_E                       (0x420 >> 3)
#define     SATA_MBR_E_SHIFT                        (0)
#define     SATA_MBR_E_MASK                         (BIT_MASK64(40))

#define R64_SATA_CURRENT_LBA                        (0x428 >> 3)
#define     SATA_CURRENT_LBA_SHIFT                  (0)
#define     SATA_CURRENT_LBA_MASK                   (BIT_MASK64(40))

#define R32_SATA_PHY_MISC1                          (0x300 >> 2)
#define     SATA_WAKE_STOP_BIT                      (BIT2)
#define     SATA_OOB_STOP_BIT                       (BIT3)
#define     SATA_BSTERRCLR_BIT                      (BIT4)
#define     SATA_DECERREN_BIT                       (BIT5)
#define     SATA_DISERREN_BIT                       (BIT6)
#define     SATA_BIST_FORCEPHYRDY_BIT               (BIT8)
#define     SATA_BIST_RST_BIT                       (BIT16)
#define     SATA_BIST_EN_BIT                        (BIT17)
#define     SATA_BIST_SEL_SHIFT                     (18)
#define     SATA_BIST_SEL_MASK                      (BIT_MASK(4))
#define     SATA_BIST_EXT_BIT                       (BIT22)
#define     SATA_BIST_LBK_BIT                       (BIT23)

#define R32_SATA_PHY_MISC2                          (0x304 >> 2)
#define     SATA_SATA_BIST_ACT_BIT                  (BIT0)
#define R8_SATA_BISTHEAD                            (0x305)
#define     SATA_ERRCNT_SHIFT                       (16)
#define     SATA_ERRCNT_MASK                        (BIT_MASK(9))

#endif /* (HOST_MODE == SATA) */

#endif /* _S17_SATA_REG_H_ */
