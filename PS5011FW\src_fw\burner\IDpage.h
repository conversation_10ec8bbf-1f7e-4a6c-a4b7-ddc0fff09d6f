#ifndef _IDPAGE_H_
#define _IDPAGE_H_

#include "burner/Burner_Def.h"


//ID Page
#define IDPG_BASE								(gulIDPGBaseAddr)
#define IDPGB                                   ((volatile U8 *)IDPG_BASE)
#define IDPGW                                   ((volatile U16 *)IDPG_BASE)
#define IDPGL                                   ((volatile U32 *)IDPG_BASE)

#define IDPG_SIZE								(4096)
#define	INVALID_FW_REVISION_ID					(0xFF)

#define ID_FEATURE_ENABLE  						(0x33)
//==============================================================================
// ID Page Reversion Mark
//==============================================================================
#define IDW_HEADER                              (0x00 >> 1)		//The ID Page Header mark to indicate this page is the ID page
#define ID_HEADER_HEXVAL						(0x4449)		//hex code for 'I''D'
#define IDB_HEADER_0                       	 	(0x00 >> 0)		//The first ID Page Header mark, must be 'I'
#define IDB_HEADER_1                        	(0x01 >> 0)		//The second ID PAge Header mark, must be 'D'
#define IDB_FW_REVISION_ID						(0x02 >> 0)		//The revision identifier of the Firmware Code, this is used to differentiate between 2 sets of Firmware Code

//==============================================================================
// Check CRC Enable
//==============================================================================
#define IDB_CRC32_CHECK_IDTABLE					(0x03 >> 0)		//If this byte is equal to 0x33, then bootcode will check CRC of 4KB ID Table and code ptr
#define IDB_CRC32_CHECK_FWSECTION				(0x04 >> 0)		//If this byte is equal to 0x33, then bootcode will check CRC32 of each FW section

//==============================================================================
// Load Code Speed Enhance realted
//==============================================================================
#define IDB_LOAD_CODE_SPEED_ENHANCE				(0x05 >> 0)		//If 0x33, bootcode will issue 0x28~0x2D to do set feature command
#define IDB_LOAD_CODE_SPEED_ENHANCE_CHECK		(0x06 >> 0)		//If 0x33, bootcode will issue get feature command to check feature
#define IDB_FLASH_INTERFACE_AFTER_ENHANCE		(0x07 >> 0)		//Flash Interface after set feature 0:Legacy 2:toggle mode 
#define INTERFACE_LEGACY						(0)
#define INTERFACE_TOGGLE						(2)

//==============================================================================
// Full FW Reversion Mark
//==============================================================================
#define IDB_FW_VERSION_CHAR						(0x08 >> 0)

//==============================================================================
// PCIE Vendor and Device ID
//==============================================================================
#define IDW_PCIE_VENDOR_ID						(0x10 >> 1)		//only assign when non-zero value  0: DISABLE
#define IDW_PCIE_DEVICE_ID						(0x12 >> 1)		//only assign when non-zero value  0: DISABLE

//==============================================================================
//ID table CRC32
//==============================================================================
#define IDL_IDPAGE_CRC							(0x014 >> 2)	//The CRC32 value of the 1st 4KB of ID table

//==============================================================================
// NAND Memory organization info.
//==============================================================================
#define IDB_SECTOR_PER_PAGE_LOGBIT				(0x18 >> 0)		//The number of sectors per page represented in powers of 2 (sector per Page == 1 << sector per Page bit)
#define IDB_PAGE_ADR_BIT_WIDTH					(0x19 >> 0)		//The number of bit that page address occupy
#define IDB_FRAME_PER_PAGE						(0x1A >> 0)		//The number of DMA frames are involved in one page.

//==============================================================================
// NEW FPU  MDLL ENABLE
//==============================================================================
#define IDB_MDLL_DIRVAL_EN						(0x1B >> 0)
#define IDB_NEW_FPU_EN							(0x1C >> 0)
#define IDB_NEW_FPU_CMD							(0x1D >> 0)
#define IDB_NEW_FPU_ADR_NUM						(0x1E >> 0)

//==============================================================================
// LDPC Mode
//==============================================================================
#define IDB_LDPC_MODE							(0x20 >> 0)		//The LDPC mode for loading FW code

//==============================================================================
//PCIE Link
//==============================================================================
#define IDB_PCIE_DELAY_LINK						(0x21 >> 0)		//If this byte is equal to 0x33, then we will not set PCIe link in bootcode, only in FW

//==============================================================================
//PCIE Link
//==============================================================================
#define IDB_FLH_6ADR_NUM						(0x22 >> 0)		//If this byte is equal to 0x33, Set FPU to 6 Address else Set FPU to 5 Address

//==============================================================================
// SLC Method
//==============================================================================
#define IDB_SLC_METHOD							(0x23 >> 0)		//Toshiba Sandisk 0xA2; Micron 0xDA 0x91 0x40 0x3B; Hynix 0xDA 0xBF

//==============================================================================
// Code Pointer Info
//==============================================================================
#define IDW_CODEPTR_PAGEIDX						(0x24 >> 1)		//Check Full Intergrity

//==============================================================================
// Set Feature Related
//==============================================================================
#define IDB_ZQ_IP_ENHANCE						(0x26 >> 0)		//If this byte is equal to 0x33, do IP ZQ calibration on when speed enhance
#define IDB_ZQ_NAND_ENHANCE						(0x27 >> 0)		//If this byte is equal to 0x33, do NAND ZQ calibration on when speed enhance
#define IDB_SETFEATURE_CMD						(0x28 >> 0)
#define IDB_SETFEATURE_ADR						(0x29 >> 0)
#define IDB_SETFEATURE_DAT0						(0x2A >> 0)
#define IDB_SETFEATURE_DAT1						(0x2B >> 0)
#define IDB_SETFEATURE_DAT2						(0x2C >> 0)
#define IDB_SETFEATURE_DAT3						(0x2D >> 0)
#define IDW_SETFEATURE_BUSYTIME					(0x2E >> 1) //Unit : us

//==============================================================================
//BackDoor OPcode Info
//==============================================================================
#define IDB_PCIE_BACKDOOR0_START_ENTRY			(0x30 >> 0)		//The first PCIe use back door entry number, these back door will process before host interface init
#define IDB_PCIE_BACKDOOR0_ENTRY_COUNT			(0x31 >> 0) 	//The total number of PCIe back door entry count,  these back door will process before host interface init
#define IDB_PCIE_BACKDOOR1_START_ENTRY			(0x32 >> 0) 	//The first PCIe use back door entry number, these back door will process after host interface init
#define IDB_PCIE_BACKDOOR1_ENTRY_COUNT			(0x33 >> 0) 	//The total number of PCIe back door entry count,  these back door will process after host interface init
#if (PS5017_EN)
#define IDB_NORMAL_BACKDOOR_START_ENTRY			(0x36 >> 0) 	//The first PCIe use back door entry number, These back door will process after ID Page load.
#define IDB_NORMAL_BACKDOOR_ENTRY_COUNT			(0x37 >> 0) 	//The total number of normal back door entry count. These back door will process after ID Page load.
#define IDB_LAST_BACKDOOR_START_ENTRY			(0x38 >> 0) 	//The Last back door entry number,  these back door will process before PRAM enable jump to FW.
#define IDB_LAST_BACKDOOR_ENTRY_COUNT			(0x39 >> 0) 	//The Last back door entry count, these back door will process before PRAM enable jump to FW.
#elif(PS5021_EN)/* (PS5017_EN) */
#define IDB_PCIE_BACKDOOR2_START_ENTRY    		(0x34 >> 0)
#define IDB_PCIE_BACKDOOR2_ENTRY_COUNT    		(0x35 >> 0)
#define IDB_NORMAL_BACKDOOR_START_ENTRY         (0x36 >> 0)     //The first PCIe use back door entry number, These back door will process after ID Page load.
#define IDB_NORMAL_BACKDOOR_ENTRY_COUNT         (0x37 >> 0)     //The total number of normal back door entry count. These back door will process after ID Page load.
#define IDB_LAST_BACKDOOR_START_ENTRY           (0x38 >> 0)     //The Last back door entry number,  these back door will process before PRAM enable jump to FW.
#define IDB_LAST_BACKDOOR_ENTRY_COUNT           (0x39 >> 0)     //The Last back door entry count, these back door will process before PRAM enable jump to FW.
#define IDB_HOST_BACKDOOR_START_ENTRY           (0x3A >> 0)     //The Host back door entry number, these back door will process before PCIE/SATA link init.
#define IDB_HOST_BACKDOOR_ENTRY_COUNT           (0x3B >> 0)     //The Host back door entry count, these back door will process before PCIE/SATA link init.
#else /* (PS5017_EN) */
#define IDB_NORMAL_BACKDOOR_START_ENTRY			(0x34 >> 0) 	//The first PCIe use back door entry number, These back door will process after ID Page load.
#define IDB_NORMAL_BACKDOOR_ENTRY_COUNT			(0x35 >> 0) 	//The total number of normal back door entry count. These back door will process after ID Page load.
#define IDB_LAST_BACKDOOR_START_ENTRY			(0x36 >> 0) 	//The Last back door entry number,  these back door will process before PRAM enable jump to FW.
#define IDB_LAST_BACKDOOR_ENTRY_COUNT			(0x37 >> 0) 	//The Last back door entry count, these back door will process before PRAM enable jump to FW.
#endif /* (PS5017_EN) */

//==============================================================================
//Efuse Over write
//==============================================================================
#if (PS5017_EN || PS5021_EN)
#define IDB_SECURITY_OPTION						(0x3C >> 0)     //0 : According to Efuse BIT0: Code sign enable  BIT1: HMAC Enable  BIT2: FIPS-140 Enable
#define IDB_VREF_ENHANCE						(0x3D >> 0)     //If this byte is equal to 0x33, set VREF on when speed enhance
#define IDB_CELL_TYPE							(0x3E >> 0) 	//0: According to ID 1: SLC 2 : MLC 3 : TLC 4 : QLC
#define IDB_REMAPPING_INFO						(0x3F >> 0) 	//0: 4CH 1 CH0+CH1 2 CH2+CH3
#define IDB_SET_FEATURE_DQSH					(0x40 >> 0)     //0x33 Enable DQS High when set feature
#define IDB_EDO_ENABLE							(0x41 >> 0)     //0x33 EDO On
#define IDB_UART_DISABLE						(0x42 >> 0)		//0x33 Uart Disable 
#define IDB_CE_DECODER_ENABLE					(0x43 >> 0)     //0x33 CE Decoder Enable
#else /* (PS5017_EN || PS5021_EN) */
#define IDB_SECURITY_OPTION						(0x38 >> 0)     //0 : According to Efuse BIT0: Code sign enable  BIT1: HMAC Enable  BIT2: FIPS-140 Enable
#define IDB_VREF_ENHANCE						(0x39 >> 0)     //If this byte is equal to 0x33, set VREF on when speed enhance
#define IDB_CELL_TYPE							(0x3A >> 0) 	//0: According to ID 1: SLC 2 : MLC 3 : TLC 4 : QLC
#define IDB_REMAPPING_INFO						(0x3B >> 0) 	//0: 4CH 1 CH0+CH1 2 CH2+CH3
#define IDB_SET_FEATURE_DQSH					(0x3C >> 0)     //0x33 Enable DQS High when set feature
#define IDB_EDO_ENABLE							(0x3D >> 0)     //0x33 EDO On
#define IDB_UART_DISABLE						(0x3E >> 0)		//0x33 Uart Disable 
#define IDB_CE_DECODER_ENABLE					(0x3F >> 0)     //0x33 CE Decoder Enable
#endif/* (PS5017_EN || PS5021_EN) */
//==============================================================================
// Clock Setting
//==============================================================================
#if (PS5017_EN)
#define IDB_SYS_CLK_SOURCE						(0x44 >> 0)
#define IDB_SYS_CLK_DIVIDER						(0x45 >> 0)
#define IDB_ECC_CLK_SOURCE						(0x46 >> 0)
#define IDB_ECC_CLK_DIVIDER						(0x47 >> 0)
#define IDB_FLH_IF_CLK							(0x4A >> 0)
#define IDB_CLK_CHANGE_EN						(0x4B >> 0)    //0x33 Do Clock setting
#define IDB_SHA_CLK_SOURCE						(0x4C >> 0)
#define IDB_SHA_CLK_DIVIDER						(0x4D >> 0)


#define IDW_MDLL_CH0_READVAL					(0x50 >> 1)
#define IDW_MDLL_CH1_READVAL					(0x52 >> 1)

#define IDW_MDLL_CH0_WRITEVAL					(0x54 >> 1)
#define IDW_MDLL_CH1_WRITEVAL					(0x56 >> 1)


//Back Door Offset
#define IDW_BACKDOOR_START_OFFSET				(0x5C >> 1)    // The Started Byte Offset to access Backdoor OP code

#define IDB_SATA_GEN							(0x5E >> 0)
#define IDB_PCIE_GEN							(0x5F >> 0)
#define IDB_DCC_SETTING							(0x61 >> 0)
#define IDB_CRS_DELAY_OFF						(0x62 >> 0)
#define IDB_PCIE_LANE							(0x60 >> 0)


//==============================================================================
// Pade Page Info
//==============================================================================
#define IDB_PAD_PAGEIDX							(0x70)
#define IDW_PADPAGE_PAGEIDX						(0x70 >> 1)		//PAD Page

#define IDB_TARGET_FLASH_CLOCK						(0xDB)

//==============================================================================
// 3D Randomize Agitation Value
//==============================================================================
#define IDB_AGITATION_TABLE_NUMBER								(0x9F >> 0)
#define IDPG_RANDOM_AGITATION_TABLE_START_OFFSET				(0xA0) // 8 Table * 6B = 48B
#define IDPG_RANDOM_AGITATION_TABLE_START_OFFSET_SIZE			(48)
#define IDPG_RANDOM_AGITATION_TABLE_START_OFFSET_EXTEND			(0xE0) // 42 Table * 6B = 252B
#define IDPG_RANDOM_AGITATION_TABLE_START_OFFSET_EXTEND_SIZE	(252)

//==============================================================================
//PCIE ID
//==============================================================================
#define	IDW_PCIE_SSVID							(0x254 >> 1)
#define	IDW_PCIE_SSDID							(0x256 >> 1)
#define	IDB_PCIE_RID							(0x258)

//==============================================================================
//SATA
//==============================================================================
#define	IDB_ASIC_TYPE							(0x25A)
#elif (PS5021_EN)/* (PS5017_EN) */
#define IDB_SYSx2_CLK_SOURCE                    (0x44 >> 0)       //Bit0~3 is Kout select, Bit4~6 is RCOSC Select, Bit7 is need to change CPU clk
#define CHANGE_CPU_CLK                          (BIT7)
#define IDB_SYSx2_CLK_DIVIDER                   (0x45 >> 0)
#define CHANGE_ZQ_RCOSC3_DIV2                	(BIT7)
#define IDB_ECC_CLK_SOURCE                      (0x46 >> 0)
#define ECC_CLK_FROM_OSC1                       (BIT2)
//#define LDPC_IS_ECC_CLK_DIV2                  (BIT3)
#define IDB_AES_CLK_SEL_DIVIDER              	(0x47 >> 0)
#define IDB_FLH_IF_CLK                          (0x48 >> 0)
#define IDB_CLK_CHANGE_EN                       (0x49 >> 0)    //0x33 Do Clock setting
#define IDB_FLH_DQSD_TIMING                     (0x4A >> 0)
#define IDB_SHA_CLK_DIVIDER                     (0x4B >> 0)

#define IDW_MDLL_CH0_READVAL                    (0x4C >> 1)
#define IDW_MDLL_CH1_READVAL                    (0x4E >> 1)
#define IDW_MDLL_CH2_READVAL                    (0x50 >> 1)
#define IDW_MDLL_CH3_READVAL                    (0x52 >> 1)
#define IDW_MDLL_CH0_WRITEVAL                   (0x54 >> 1)
#define IDW_MDLL_CH1_WRITEVAL                   (0x56 >> 1)
#define IDW_MDLL_CH2_WRITEVAL                   (0x58 >> 1)
#define IDW_MDLL_CH3_WRITEVAL                   (0x5A >> 1)

#define IDW_BACKDOOR_START_OFFSET               (0x5C >> 1)    // The Started Byte Offset to access Backdoor OP code

#define IDB_SATA_GEN                            (0x5E >> 0)
#define IDB_PCIE_GEN                            (0x5F >> 0)
#define IDB_PCIE_LANE                           (0x60 >> 0)

#define IDB_DCC_SETTING                         (0x61 >> 0)
#define     DCC_EN_BIT                          (BIT0)
#define     DCC_TRAIN_SIZE_SHIFT                (1)
#define     DCC_TRAIN_SIZE_MASK                 BIT_MASK(2)
#define     DCC_TSB_MODE_SHIFT                  (3)
#define     DCC_TSB_MODE_MASK                   BIT_MASK(1)

#define IDB_CRS_DELAY                           (0x62 >> 0)


#define IDB_READ_RETRY_VADDR_EN                 (0x63 >> 0)
#define IDB_READ_RETRY_VADDR                    (0x64 >> 0)
#define IDB_CHANGE_READ_COLUMN_VADDR_EN         (0x65 >> 0)
#define IDB_CHANGE_READ_COLUMN_VADDR            (0x66 >> 0)

#define IDB_PER_FRAME_DUMMY_EN					(0x67 >> 0)
#define IDW_FRAME0_DUMMY_BYTE					(0x68 >> 1)
#define IDW_FRAME1_DUMMY_BYTE					(0x6A >> 1)
#define IDW_FRAME2_DUMMY_BYTE					(0x6C >> 1)
#define IDW_FRAME3_DUMMY_BYTE					(0x6E >> 1)

//==============================================================================
// PCIE Sub-System Vendor and Device ID
//==============================================================================
#define IDW_PCIE_SSVID			(0x70 >> 1)
#define IDW_PCIE_SSDID			(0x72 >> 1)
#define IDB_PCIE_SUBSYSTEM_ID_VENDOR_EN			(0x74 >> 0)
#define IDB_MMOD_FOR_SEED_SELECTION_EN          (0x75 >> 0)
#define IDW_PAGEPERBLOCK_OR_DIVIDER             (0x76 >> 1)

#define IDW_SEED_AGITATION_1_PAGEIDX            (0x78 >> 1)
#define IDW_SEED_AGITATION_2_PAGEIDX            (0x7A >> 1)
#define IDL_SEED_AGITATION_1                    (0x7C >> 2)
#define IDL_SEED_AGITATION_2                    (0x80 >> 2)
#define IDW_SEED_AGITATION_3_PAGEIDX            (0x84 >> 1)
#define IDW_SEED_AGITATION_4_PAGEIDX            (0x86 >> 1)
#define IDL_SEED_AGITATION_3                    (0x88 >> 2)
#define IDL_SEED_AGITATION_4                    (0x8C >> 2)
#define IDW_SEED_AGITATION_5_PAGEIDX            (0x90 >> 1)
#define IDW_SEED_AGITATION_6_PAGEIDX            (0x92 >> 1)
#define IDL_SEED_AGITATION_5                    (0x94 >> 2)
#define IDL_SEED_AGITATION_6                    (0x98 >> 2)
#define IDW_SEED_AGITATION_7_PAGEIDX            (0x9C >> 1)
#define IDW_SEED_AGITATION_8_PAGEIDX            (0x9E >> 1)
#define IDL_SEED_AGITATION_7                    (0xA0 >> 2)
#define IDL_SEED_AGITATION_8                    (0xA4 >> 2)
#define IDW_SEED_AGITATION_9_PAGEIDX            (0xA8 >> 1)
#define IDW_SEED_AGITATION_10_PAGEIDX           (0xAA >> 1)
#define IDL_SEED_AGITATION_9                    (0xAC >> 2)
#define IDL_SEED_AGITATION_10                   (0xB0 >> 2)
#define IDW_SEED_AGITATION_11_PAGEIDX           (0xB4 >> 1)
#define IDW_SEED_AGITATION_12_PAGEIDX           (0xB6 >> 1)
#define IDL_SEED_AGITATION_11                   (0xB8 >> 2)
#define IDL_SEED_AGITATION_12                   (0xBC >> 2)
#else /* (PS5017_EN) */
#define IDB_SYS_CLK_SOURCE						(0x40 >> 0)
#define IDB_SYS_CLK_DIVIDER						(0x41 >> 0)
#define IDB_ECC_CLK_SOURCE						(0x42 >> 0)
#define IDB_ECC_CLK_DIVIDER						(0x43 >> 0)
#define IDB_FLH_IF_CLK							(0x44 >> 0)
#define IDB_CLK_CHANGE_EN						(0x45 >> 0)    //0x33 Do Clock setting
#define IDB_SHA_CLK_SOURCE						(0x46 >> 0)
#define IDB_SHA_CLK_DIVIDER						(0x47 >> 0)


#define IDW_MDLL_CH0_READVAL					(0x48 >> 1)
#define IDW_MDLL_CH1_READVAL					(0x4A >> 1)
#define IDW_MDLL_CH2_READVAL					(0x4C >> 1)
#define IDW_MDLL_CH3_READVAL					(0x4E >> 1)
#define IDW_MDLL_CH0_WRITEVAL					(0x50 >> 1)
#define IDW_MDLL_CH1_WRITEVAL					(0x52 >> 1)
#define IDW_MDLL_CH2_WRITEVAL					(0x54 >> 1)
#define IDW_MDLL_CH3_WRITEVAL					(0x56 >> 1)

//Back Door Offset
#define IDW_BACKDOOR_START_OFFSET				(0x58 >> 1)    // The Started Byte Offset to access Backdoor OP code

#define IDB_SATA_GEN							(0x5A >> 0)
#define IDB_PCIE_GEN							(0x5B >> 0)
#define IDB_DCC_SETTING							(0x5C >> 0)
#define IDB_CRS_DELAY_OFF						(0x5D >> 0)
#define IDB_PCIE_LANE							(0x5E >> 0)

#define	IDB_FORM_FACTOR							(0x66)

//PCIE ID
#define	IDW_PCIE_SSVID							(0x72 >> 1)
#define	IDW_PCIE_SSDID							(0x74 >> 1)
#define	IDB_PCIE_RID							(0x76)
#endif /* (PS5017_EN) */
//==============================================================================
// Clock Setting (0x25B~0x26B)
//==============================================================================
#if (PS5021_EN)
#define	IDB_BACKUP_START_OFFSET					(IDB_PCIE_RID)
#define	IDB_PCIE_RID							(0x259 >> 0)
#define IDB_TARGET_FLASH_CLOCK					(0x25A >> 0)
#endif /* (PS5021_EN) */

#define	IDB_PLL_SSC								(0x25B >> 0)
#define	IDB_CLK_ADJ_EN							(0x25C >> 0)
#define		IDPAGE_SYSTEM_CLOCK_BIT				(BIT0)
#define		IDPAGE_CPU_CLOCK_BIT				(BIT1)
#define		IDPAGE_ECC_CLOCK_BIT				(BIT2)
#define		IDPAGE_AES_CLOCK_BIT				(BIT3)
#define		IDPAGE_SHA_CLOCK_BIT				(BIT4)
#define	IDPAGE_ADJUST_CLOCK_NUM					(5)
#define	IDB_SYSTEM_CLK_SRC						(0x25D >> 0)
#define	IDB_SYSTEM_CLK_DIV						(0x25E >> 0)
#define	IDB_CPU_CLK_SRC							(0x25F >> 0)
#define	IDB_CPU_CLK_DIV							(0x260 >> 0)
#define	IDB_ECC_CLK_SRC							(0x261 >> 0)
#define	IDB_ECC_CLK_DIV							(0x262 >> 0)
#define	IDB_AES_CLK_SRC							(0x263 >> 0)
#define	IDB_AES_CLK_DIV							(0x264 >> 0)
#define	IDB_SHA_CLK_SRC							(0x265 >> 0)
#define	IDB_SHA_CLK_DIV							(0x266 >> 0)

#define IDB_DPS_ENABLE							(0x26F)
#define IDB_DPS_VERSION							(0x270)
#define IDB_DPS_CHANNEL_NUM						(0x271)
#define IDB_DPS_CE_NUM							(0x272)
#define IDB_DPS_DIE_NUM							(0x273)
#define IDB_DPS_SLC_SET_END_OFFSET				(0x274)
#define IDB_DPS_TLC_SET_END_OFFSET				(0x275)
#define IDB_DPS_SLC_PLUS_END_OFFSET				(0x276)
#define IDB_DPS_TLC_PLUS_END_OFFSET				(0x277)
#define IDB_DPS_SLC_MINUS_END_OFFSET			(0x278)
#define IDB_DPS_TLC_MINUS_END_OFFSET			(0x279)
#define IDB_DPS_TABLE							(0x27A)
#define IDB_MICRON_WORKAROUND					(0x2FA)
#define IDB_FLASH_ENVIRONMENT					(0x2FB)
//==============================================================================
// FLL and RC Calibration (0x26C~0x26E)
//==============================================================================
#define IDW_FLL_AND_RC_VARIAION					(0x26C >> 1)
#define IDB_FLL_VALUE							(0x26E >> 0)

#if (PS5017_EN || PS5021_EN)
//==============================================================================
// L1 config (0x380~0x380)
//==============================================================================
#define IDB_L1_CONFIG							(0x380)

//==============================================================================
// 3D Randomizer config (0x384~0x38F)
//==============================================================================
#define IDW_CONV_REMAP_OFST_INIT				(0x384)
#define IDW_CONV_REMAP_OFST_DIFF_INIT			(0x386)
#define IDW_CONV_CYCLE_INIT						(0x388)
#define IDW_CONV_CYCLE_DIFF_INIT				(0x38A)
#define IDW_CONV_D1								(0x38C)
#define IDW_CONV_D2								(0x38E)

#else /* (PS5017_EN || PS5021_EN) */
//==============================================================================
// L1 config (0x37C~0x37C)
//==============================================================================
#define IDB_L1_CONFIG							(0x37C)

//==============================================================================
// 3D Randomizer config (0x380~0x38B)
//==============================================================================
#define IDW_CONV_REMAP_OFST_INIT				(0x380)
#define IDW_CONV_REMAP_OFST_DIFF_INIT			(0x382)
#define IDW_CONV_CYCLE_INIT						(0x384)
#define IDW_CONV_CYCLE_DIFF_INIT				(0x386)
#define IDW_CONV_D1								(0x388)
#define IDW_CONV_D2								(0x38A)
#endif /* (PS5017_EN || PS5021_EN) */

#if (PS5021_EN)
#define IDL_RANDOMIZER_PARA0            (0x3F8 >>2)		// boot code
#define IDL_RANDOMIZER_PARA1            (0x3FC >>2)		// boot code
#endif /* (PS5021_EN) */

//==============================================================================
// Fast Page Rule (0x400~0x5FF)
//==============================================================================
#define MAX_FAST_PG								(256)  						//Supports up to MAX_FAST_PG Wordlines/Fast Pages as Code Pages
#define IDW_FAST_PAGE_RULE						(0x400 >> 1)				//The table that translates the logical code pages into the actual physical code pages
#define IDW_FAST_PAGE(X)						(IDW_FAST_PAGE_RULE + (X))

//==============================================================================
// Back Door (0x600~0xFFF)
//==============================================================================
#define MAX_OPCODE_NUM							(160)					 //Total 160 Op Code can filed
#define OP_CODE_SIZE							(16)
#define IDB_OP_CODE_BASE						(0x600 >> 0)
#define IDL_OP_CODE_BASE						(0x600 >> 2)
#define IDL_OP_CODE_END							(0x1000 >> 2)
#if (PS5017_EN)
#define	IDPAGE_BACKUP_SIZE						(0x12D)//back up 0x254-0x380
#elif (PS5021_EN)/* (PS5017_EN) */
#define	IDPAGE_BACKUP_SIZE						(0x128)//back up 0x259-0x380
#else /* (PS5017_EN) */
#define	IDPAGE_BACKUP_SIZE						(0x315)
#endif /* (PS5017_EN) */
//==============================================================================
// Back door macro
//==============================================================================
#define M_SETOP(ulOPAddr, ubOP, ubSubOp, ubCtrl, ulMask, ulValue, ulAddr) {\
	IDPGL[((ulOPAddr) >> 2)] = (((ubCtrl) << 16) | ((ubSubOp) << 8) | (ubOP));\
	IDPGL[((ulOPAddr) >> 2) + 1] = (ulMask);\
	IDPGL[((ulOPAddr) >> 2) + 2] = (ulValue);\
	IDPGL[((ulOPAddr) >> 2) + 3] = (ulAddr);\
}

// OP code
#define	OP_IDLE		(0x01)
#define		IDLE_PC				(0x00)
#define		IDLE_US				(0x01)
#define		IDLE_MS				(0x02)
#define		IDLE_S				(0x03)
#define	OP_POLL		(0x02)
#define		SUB_POLL_UNTIL_NEQ	(0x00)
#define		SUB_POLL_UNTIL_EQ	(0x01)
#define	OP_SET		(0x10)
#define	OP_OR		(0x20)
#define	OP_AND		(0x30)
#define		SUB_VAL				(0x00)
#define		SUB_PTR				(0x01)
#define			R32_ACCESS			(0x00)
#define			R16_ACCESS			BIT0
#define			R8_ACCESS			BIT1

#endif //end _IDPAGE_H_
