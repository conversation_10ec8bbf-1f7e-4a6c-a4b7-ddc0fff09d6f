#ifndef _E13_I2C_MASTER_REGISTER_H_
#define _E13_I2C_MASTER_REGISTER_H_

#include "setup.h"
#include "typedef.h"
#include "symbol.h"
#include "mem.h"



#define I2CM_REGISTER_OFFSET            (0x0400)
#define I2CM_REGISTER_ADDRESS           (PIC_REG_ADDRESS + I2CM_REGISTER_OFFSET)

#define R8_I2CM                         ((REG8 *)  I2CM_REGISTER_ADDRESS)
#define R16_I2CM                        ((REG16 *) I2CM_REGISTER_ADDRESS)
#define R32_I2CM                        ((REG32 *) I2CM_REGISTER_ADDRESS)
#define R64_I2CM                        ((REG64 *) I2CM_REGISTER_ADDRESS)

//--------------------------


#define R32_I2CM_GP1_SLAVE_ADDR         ((0x00) >> 2)
#define R16_I2CM_GP1_SLAVE_ADDR         ((0x00) >> 1)
#define R16_I2CM_GP1_MASTER_CODE        ((0x02) >> 1)
#define R32_I2CM_GP1_OP0_REG            ((0x04) >> 2)
#define R8_I2CM_GP1_OP_REG0             ((0x04) >> 0)
#define R8_I2CM_GP1_OP_REG1             ((0x05) >> 0)
#define R8_I2CM_GP1_OP_REG2             ((0x06) >> 0)
#define R8_I2CM_GP1_OP_REG3             ((0x07) >> 0)
#define R32_I2CM_GP1_OP1_REG            ((0x08) >> 2)
#define R8_I2CM_GP1_OP_REG4             ((0x08) >> 0)
#define R8_I2CM_GP1_OP_REG5             ((0x09) >> 0)
#define R8_I2CM_GP1_OP_REG6             ((0x0A) >> 0)
#define R8_I2CM_GP1_OP_REG7             ((0x0B) >> 0)
#define R32_I2CM_GP1_CTRL               ((0x0C) >> 2)
#define GP_MODE_SHIFT	0
#define GP_DATA_CNT_SHIFT	8
#define GP_OP_CNT_SHIFT	16
#define     GP_I2C_START_BIT                (BIT0)
#define     GP_DONE_INT_EN_BIT              (BIT1)
#define     GP_HS_MODE_BIT                  (BIT2)
#define     GP_COMBI_MODE_BIT               (BIT3)
#define     GP_BUF_SEL_BIT                  (BIT4)
#define     PD1_CTRL_BIT                    (BIT5)
#define     GP_A10_EN_BIT                   (BIT6)
#define     GP_DIR_BIT                      (BIT7)
#define     GP_EOP_MODE_BIT                 (BIT24)
#define     GP_EOP_ENA_BIT                  (BIT25)
#define     GP_EOP_CTRL_BIT                 (BIT26)
#define R8_I2CM_GP1_CTRL                ((0x0C) >> 0)
#define R8_I2CM_GP1_DATA_CNT            ((0x0D) >> 0)
#define R8_I2CM_GP1_OP_CNT              ((0x0E) >> 0)
#define R32_I2CM_GP1_NACK               ((0x10) >> 2)
#define     GP_ANACK_BIT                    (BIT0)
#define     GP_DNACK_BIT                    (BIT1)
#define R16_I2CM_HS_CLK_DIV_CNT      	((0x14) >> 1)
#define R16_I2CM_CLK_DIV_CNT      	    ((0x16) >> 1)
#define R32_I2CM_MAIN_CTRL_REG          ((0x18) >> 2)
#define     INT_EVENT_BIT                   (BIT0)
#define     GP4_I2C_DONE_INT_EN_BIT         (BIT3)
#define     GP3_I2C_DONE_INT_EN_BIT         (BIT4)
#define     GP2_I2C_DONE_INT_EN_BIT         (BIT5)
#define     GP1_I2C_DONE_INT_EN_BIT         (BIT6)
#define     INT_EN_BIT                      (BIT7)
#define		SCL_DRV_H_BIT					(BIT16)
#define R8_I2CM_GP1_EOP_REG0       	    ((0x1C) >> 0)
#define R8_I2CM_GP1_EOP_REG1       	    ((0x1D) >> 0)
#define R32_I2CM_GP2_SLAVE_ADDR         ((0x20) >> 2)
#define R16_I2CM_GP2_SLAVE_ADDR         ((0x20) >> 1)
#define R16_I2CM_GP2_MASTER_CODE        ((0x22) >> 1)
#define R32_I2CM_I2CM_GP2_OP0_REG       ((0x24) >> 2)
#define R8_I2CM_GP2_OP_REG0             ((0x24) >> 0)
#define R8_I2CM_GP2_OP_REG1             ((0x25) >> 0)
#define R8_I2CM_GP2_OP_REG2             ((0x26) >> 0)
#define R8_I2CM_GP2_OP_REG3             ((0x27) >> 0)
#define R32_I2CM_GP2_OP1_REG            ((0x28) >> 2)
#define R8_I2CM_GP2_OP_REG4             ((0x28) >> 0)
#define R8_I2CM_GP2_OP_REG5             ((0x29) >> 0)
#define R8_I2CM_GP2_OP_REG6             ((0x2A) >> 0)
#define R8_I2CM_GP2_OP_REG7             ((0x2B) >> 0)
#define R32_I2CM_GP2_CTRL               ((0x2C) >> 2)
#define     PD2_CTRL_BIT                    (BIT5)
#define R8_I2CM_GP2_CTRL                ((0x2C) >> 0)
#define R8_I2CM_GP2_DATA_CNT            ((0x2D) >> 0)
#define R8_I2CM_GP2_OP_CNT              ((0x2E) >> 0)
#define R32_I2CM_GP2_NACK               ((0x30) >> 2)
#define R8_I2CM_GP2_EOP_REG0       	    ((0x3C) >> 0)
#define R8_I2CM_GP2_EOP_REG1       	    ((0x3D) >> 0)
#define R32_I2CM_GP3_SLAVE_ADDR         ((0x40) >> 2)
#define R16_I2CM_GP3_SLAVE_ADDR         ((0x40) >> 1)
#define R16_I2CM_GP3_MASTER_CODE        ((0x42) >> 1)
#define R32_I2CM_GP3_OP0_REG            ((0x44) >> 2)
#define R8_I2CM_GP3_OP_REG0             ((0x44) >> 0)
#define R8_I2CM_GP3_OP_REG1             ((0x45) >> 0)
#define R8_I2CM_GP3_OP_REG2             ((0x46) >> 0)
#define R8_I2CM_GP3_OP_REG3             ((0x47) >> 0)
#define R32_I2CM_GP3_OP1_REG            ((0x48) >> 2)
#define R8_I2CM_GP3_OP_REG4             ((0x48) >> 0)
#define R8_I2CM_GP3_OP_REG5             ((0x49) >> 0)
#define R8_I2CM_GP3_OP_REG6             ((0x4A) >> 0)
#define R8_I2CM_GP3_OP_REG7             ((0x4B) >> 0)
#define R32_I2CM_GP3_CTRL               ((0x4C) >> 2)
#define     PD3_CTRL_BIT                    (BIT5)
#define R8_I2CM_GP3_CTRL                ((0x4C) >> 0)
#define R8_I2CM_GP3_DATA_CNT            ((0x4D) >> 0)
#define R8_I2CM_GP3_OP_CNT              ((0x4E) >> 0)
#define R32_I2CM_GP3_NACK               ((0x50) >> 2)
#define R8_I2CM_GP3_EOP_REG0       	    ((0x5C) >> 0)
#define R8_I2CM_GP3_EOP_REG1       	    ((0x5D) >> 0)
#define R32_I2CM_GP4_SLAVE_ADDR         ((0x60) >> 2)
#define R16_I2CM_GP4_SLAVE_ADDR         ((0x60) >> 1)
#define R16_I2CM_GP4_MASTER_CODE        ((0x62) >> 1)
#define R32_I2CM_GP4_OP0_REG            ((0x64) >> 2)
#define R8_I2CM_GP4_OP_REG0             ((0x64) >> 0)
#define R8_I2CM_GP4_OP_REG1             ((0x65) >> 0)
#define R8_I2CM_GP4_OP_REG2             ((0x66) >> 0)
#define R8_I2CM_GP4_OP_REG3             ((0x67) >> 0)
#define R32_I2CM_GP4_OP1_REG            ((0x68) >> 2)
#define R8_I2CM_GP4_OP_REG4             ((0x68) >> 0)
#define R8_I2CM_GP4_OP_REG5             ((0x69) >> 0)
#define R8_I2CM_GP4_OP_REG6             ((0x6A) >> 0)
#define R8_I2CM_GP4_OP_REG7             ((0x6B) >> 0)
#define R32_I2CM_GP4_CTRL               ((0x6C) >> 2)
#define     PU1_CTRL_BIT                    (BIT5)
#define R8_I2CM_GP4_CTRL                ((0x6C) >> 0)
#define R8_I2CM_GP4_DATA_CNT            ((0x6D) >> 0)
#define R8_I2CM_GP4_OP_CNT              ((0x6E) >> 0)
#define R32_I2CM_GP4_NACK               ((0x70) >> 2)
#define R8_I2CM_GP4_EOP_REG0       	    ((0x7C) >> 0)
#define R8_I2CM_GP4_EOP_REG1       	    ((0x7D) >> 0)
#define R32_I2CM_BUF0_REG               ((0x80) >> 2)
#define R16_I2CM_BUF0_REG               ((0x80) >> 1)
#define R8_I2CM_BUF0_REG                ((0x80) >> 0)
#define R32_I2CM_BUF1_REG               ((0xC0) >> 2)
#define R16_I2CM_BUF1_REG               ((0xC0) >> 1)
#define R8_I2CM_BUF1_REG                ((0xC0) >> 0)


//--------------------------


#endif /* _E13_I2C_MASTER_REGISTER_H_ */
