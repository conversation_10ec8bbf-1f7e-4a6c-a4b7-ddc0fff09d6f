/****************************************************************************
*
*  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
*  All rights reserved
*
*  The content of this document is confidential and shall be applied
*  subject to the terms and conditions of the license agreement and
*  other applicable laws. Any unauthorized access, use or disclosure
*  of this document is strictly prohibited and may be punishable
*  under laws.
*
*  Initinfo_vt_api.h
*
*
*
****************************************************************************/
#ifndef _INITINFO_VT_API_H_
#define _INITINFO_VT_API_H_

#include "setup.h"
#include "table/sys_area/sys_area_api.h"
#include "typedef.h"

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */
#if PS5017_EN
#define TRIM_ALREADY_TRIMMED_BMP_SIZE_IN_BYTE	(128) //Need 128 Bytes to support for max size 4TB.
#else /* PS5017_EN */
#define TRIM_ALREADY_TRIMMED_BMP_SIZE_IN_BYTE	(64)
#endif /* PS5017_EN */
#define INITINFO_VT_MEDIA_SCAN_MAX_CE           (MAX_CE*2)
#if MICRON_FSP_EN
#define INITINFO_VT_MEDIA_SCAN_ERROR_BIT_LEVEL  (8+8)
#elif ((HYNIX_FSP_EN)||(SANDISK_FSP_EN)||MST_MODE_EN)
#define INITINFO_VT_MEDIA_SCAN_ERROR_BIT_LEVEL  (8) //Dylan for V6 RDT PORINTG,Reference V6 RDT code set to 8
#else /* MICRON_FSP_EN */
#define INITINFO_VT_MEDIA_SCAN_ERROR_BIT_LEVEL  (4)
#endif /* MICRON_FSP_EN */

/*
 * ---------------------------------------------------------------------------------------------------
 *  data types
 * ---------------------------------------------------------------------------------------------------
 */
typedef struct {
	U8 aubDiskAlreadyTrimmedBMP[TRIM_ALREADY_TRIMMED_BMP_SIZE_IN_BYTE];
	U8 btIsDiskAllTrimmed	: 1;
	U8 				: 7;
	U8 ubReserved0;
	U8 ubReserved1;
	U8 ubReserved2;
} TrimSkipBMP_t; //(TRIM_TRY_SKIP_CMD_EN) E13-8253

typedef struct {
	U64 uoPreviousAPUWCmdCnt;
	U64 uoContinuousTrimCmdLBAEnd; //End is EXcluded
	U64 uoContinuousTrimCmdLBAStart	: 33;
	U64 uoSkipCnt				: 31;
} TrimSkipLBAAndCnt_t; //(TRIM_TRY_SKIP_CMD_EN) E13-8253

typedef struct {
	U8 btIsTrimNodeInContainer	: 1; //Save for LPM/Standby Flow. [Note] TrimNodeInDSA will always be 0 for LPM/Standby Flow
	U8							: 7;
	U8 ubReserved;
} VTDBUFTrimInfo_t;

typedef struct {
	U16 uwUnit[10];	//for each blk type
	U16 uwScanIdx[10];	//for each blk type
	U16 uwCurrentPlaneIdx[7]; //N48R: only partial blks need it.
	U32 ulErrorBitRecordForLDPCFrame[INITINFO_VT_MEDIA_SCAN_MAX_CE][INITINFO_VT_MEDIA_SCAN_ERROR_BIT_LEVEL];
	SystemAreaBlock_t  uwCurrentSLCNonDataBlk[21]; //CH, CE, Blk order
	U16 uwTotalTableUnit[16];
#if IM_N48R
	U8 ubTotalTableUnitIdx : 4;	//16
	U8 ubTotalTableUnitCnt : 4;
	U16 uwQLCStartUnit;
	U16 uwSLCStartUnit;
	U64 uoFullScanGroupStartTime;
	U64 uoScanGroupStartTime;
#else /* IM_N48R */
	U8 ubWeakPageDoneCnt;
	U16 uwSLCStartIdx;
	U16 uwTLCStartIdx;
	U64 uoPreviousScanTimeStamp;
	U64 uoPreviousScanTimeStampPartial;
#endif /* IM_N48R */
	U64 uoScanGroupCostTime[8];	//for each scan group
	U16 uwValleyHealthCheckCnt[8];	//for each scan group
	U16 uwNeedFoldingCnt[8];	//for each scan group
	U16 uwQLCScanUnitCnt[8];	//for each scan group
	U16 uwSLCScanUnitCnt[8];	//for each scan group
	U8 ubScanSuperPageCurrentCnt;
	U16 btInitialPartialScanDone : 1;
	U16 ubEventIdx : 4;	//10
	U16 ubCurrentSLCNonDataEventIdx : 5; //21
	U16 ubStartScanGroup : 4;
	U16 btStartNewScanGroup : 1;
	U16 btMandatoryWordLineDone : 1;
	U8 ubVictimScanGroup : 4;
	U8 btFirstTimeWakeUpFromPOR : 1;
	U8 btIsScanGroupIdle : 1;
	U8 btIsFullScanGroupIdle : 1;
	U8 ubReserve : 1;
} VTDBUFMediaScan_t;
SIZE_CHECK_ALIGN_4(VTDBUFMediaScan_t);
#if ((FW_CATEGORY_FLASH == FLASH_B47R_TLC) || (FW_CATEGORY_FLASH == FLASH_B37R_TLC) || (FW_CATEGORY_FLASH == FLASH_N48R_QLC))//zerio n48r
TYPE_SIZE_CHECK(VTDBUFMediaScan_t, 2336);
#elif ((FW_CATEGORY_FLASH == FLASH_BICS4TLCHDR) || (FW_CATEGORY_FLASH == FLASH_BICS5TLC))
TYPE_SIZE_CHECK(VTDBUFMediaScan_t, 568);
#elif((FW_CATEGORY_FLASH == FLASH_V6_TLC)||(FW_CATEGORY_FLASH ==FLASH_V7_TLC)||(FW_CATEGORY_FLASH ==FLASH_V8_TLC)||(FW_CATEGORY_FLASH ==FLASH_V5_TLC)|| (FW_CATEGORY_FLASH == FLASH_V7_QLC))//Reip Porting 3D-V7 QLC Add//Jeffrey Porting 3D V8 TLC Add
TYPE_SIZE_CHECK(VTDBUFMediaScan_t, 1312);
#elif((CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_QLC))//Duson Porting BICS5 Add//zerio BICS8 Add//zerio bics6 qlc add
TYPE_SIZE_CHECK(VTDBUFMediaScan_t, 1312);
#elif((FW_CATEGORY_FLASH == FLASH_YMTC_TAS_TLC)|| (FW_CATEGORY_FLASH == FLASH_YMTC_WTS_TLC) || (FW_CATEGORY_FLASH == FLASH_YMTC_EMS_QLC))//ems mst add--karl//zerio wts add
TYPE_SIZE_CHECK(VTDBUFMediaScan_t, 1312);
#elif(FW_CATEGORY_FLASH == FLASH_SAMSUNG_V6_TLC || FLASH_SAMSUNG_V6P_TLC == FW_CATEGORY_FLASH \
		|| FW_CATEGORY_FLASH == FLASH_SAMSUNG_V7_TLC || FW_CATEGORY_FLASH == FLASH_SAMSUNG_V8_TLC || FW_CATEGORY_FLASH == FLASH_SAMSUNG_V5_TLC)//Samsung v7/v8 mst add--Reip
TYPE_SIZE_CHECK(VTDBUFMediaScan_t, 1312);
#elif(CONFIG_FLASH_TYPE == FLASH_TYPE_INTEL_3D_QLC)
TYPE_SIZE_CHECK(VTDBUFMediaScan_t, 1312);
#else /* ((FW_CATEGORY_FLASH == FLASH_B47R_TLC) || (FW_CATEGORY_FLASH == FLASH_N48R_QLC)) */
STATIC_ASSERT(FALSE, #TYPE " Pls Check Size") // Pls Check MediaScan Size and set support flash type
#endif /* ((FW_CATEGORY_FLASH == FLASH_B47R_TLC) || (FW_CATEGORY_FLASH == FLASH_N48R_QLC)) */

/*
 * ---------------------------------------------------------------------------------------------------
 *   macros
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *   extern global variables
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */
AOM_FTL_EXT U8 InitInfoCheckUnitIsInitInfoOldUnit(U16 uwCheckUnit);
AOM_SPOR void InitInfoSaveUntilDone_AOM_SPOR(U32 ulEvent);
AOM_SPOR_4 void InitInfoSaveUntilDone_AOM_SPOR_4(U32 ulEvent);
AOM_INIT_2 void InitInfoSaveUntilDone_AOM_INIT_2(U32 ulEvent);
AOM_WL void InitInfoSaveUntilDone_AOM_WL(U32 ulEvent);
AOM_POR_SCAN void InitInfoSaveUntilDone_AOM_POR_SCAN(U32 ulEvent);
AOM_VUC_3 void InitInfoSaveUntilDone_AOM_VUC_3(U32 ulEvent);

#endif /* _INITINFO_VT_API_H_ */
