/****************************************************************************
*
*  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
*  All rights reserved
*
*  The content of this document is confidential and shall be applied
*  subject to the terms and conditions of the license agreement and
*  other applicable laws. Any unauthorized access, use or disclosure
*  of this document is strictly prohibited and may be punishable
*  under laws.
*
*  VUC_GetFlashInfo.c
*
*
*
****************************************************************************/
/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "hal/fip/fip.h"
#include "hal/fip/fip_api.h"
#include "hal/pic/uart/uart_api.h"
#include "hal/sys/api/efuc/efuse_api.h"
#if (USB == HOST_MODE)
#include "hal/usb/usb.h"
#endif /* (USB == HOST_MODE) */
#include "nvme_api/nvme/shr_hal_nvme.h"
#include "vuc/VUC_command.h"
#include "vuc/VUC_GetFlashInfo.h"
#include "vuc/VUC_Utilities.h"
#include "host/VUC_handler.h"
#include "retry/retry_api.h"
#include "ftl/ftl_api.h"
#include "init/fw_init.h"
#include "init/fw_preformat.h"
/*
 * ---------------------------------------------------------------------------------------------------
 *   private prototypes
 * ---------------------------------------------------------------------------------------------------
 */
AOM_VUC static void VUCGetFlashReadRetryTable(VUC_OPT_HCMD_PTR_t pCmd);
AOM_VUC static void VUCAssignReadRetryTable(VUC_OPT_HCMD_PTR_t pCmd);
AOM_VUC static void VUC_GetFlashParameterTable(VUC_OPT_HCMD_PTR_t pCmd);
AOM_VUC static void VUC_GetUniqueID(VUC_OPT_HCMD_PTR_t pCmd);
#if (USB != HOST_MODE)
AOM_VUC static void VUCGetMaxBlockPerPlane(VUC_OPT_HCMD_PTR_t pCmd);
#endif /* (USB != HOST_MODE) */
/*
 * ---------------------------------------------------------------------------------------------------
 *   private
 * ---------------------------------------------------------------------------------------------------
 */
void VUCGetFlashReadRetryTable(VUC_OPT_HCMD_PTR_t pCmd)
{
	U8 ubChannel, ubCE;
	U8 ubPhysicalCE = pCmd->vuc_sqcmd.vendor.FlashInfo.ubCE;
	U8 ubDie = pCmd->vuc_sqcmd.vendor.FlashInfo.ubDie;
	M_UART(VUC_, "\nGetFlashReadRetryTable");
	M_UART(VUC_, "\nPCE %b Die %b", ubPhysicalCE, ubDie);
	if (TRUE == PhysicalCE2LogicalCHCE(0, ubPhysicalCE, &ubChannel, &ubCE)) {
#if (MICRON_FSP_EN)
		// to be continue ...
#else /*MICRON_FSP_EN*/
		U8 ubFormat =  pCmd->vuc_sqcmd.raw_u8_data.by[56];
		FipGetFlashReadRetryTable(ubChannel, ubCE, ubDie, (U8 *)gulVUCBufAddr, 0, ubFormat);
#endif /*MICRON_FSP_EN*/
	}
}

void VUC_GetFlashParameterTable(VUC_OPT_HCMD_PTR_t pCmd)
{
	U8 ubCH, ubCE;
	U8 ubPhysicalCE = pCmd->vuc_sqcmd.vendor.FlashInfo.ubCE;

	// TODO: Add CE Decoder
	M_UART(VUC_, "\nVUC_Get_Flash_Parameter_Table");

	if (TRUE == PhysicalCE2LogicalCHCE(((EFU_BANK3 *)gEFuseBank2347Info.ubEFuseBank3)->dw0.btCeDecoder, ubPhysicalCE, &ubCH, &ubCE)) {
		FlaGetParameterTable(ubCH, ubCE, (U8 *)gulVUCBufAddr);
	}
}

void VUC_GetUniqueID(VUC_OPT_HCMD_PTR_t pCmd)
{
	U8 ubChannel, ubCE;
	U8 ubPhysicalCE = pCmd->vuc_sqcmd.vendor.FlashInfo.ubCE;
	U8 ubDie = pCmd->vuc_sqcmd.vendor.FlashInfo.ubDie;
	M_UART(VUC_, "\nVUC_GET_UNIQUE_ID");
	M_UART(VUC_, "\nPCE %b Die %b", ubPhysicalCE, ubDie);
	if (TRUE == PhysicalCE2LogicalCHCE(0, ubPhysicalCE, &ubChannel, &ubCE)) {
		FIPScanFlashUniqueID(ubChannel, ubCE, ubDie, (U8 *)pCmd->ulCurrentPhysicalMemoryAddr);
	}
}
#if (USB == HOST_MODE) && (!RDT_MODE_EN)
void VUCGetMaxBlockPerPlane(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD)
{
	U64 uoLBASize;
	U32 ulUnitSize;
	U32 ulData4kEntrysPerUnit = M_GET_DATA_4KENTRY_PER_UNIT(FW_RUN_SLC_MODE_EN);
	U16 uwMaxBadBlock;
	U16 uwRequireUnitNum;
	U16 *puwMaxBadBlock = NULL;
	U8 ubMinOPVBNum;
	U8 ubVTUnitNum;
	U8 ubSystemAreaBlkNum = 0;

	puwMaxBadBlock = (U16 *)ulBufAddr;
	memset((void *)(ulBufAddr), 0x00, DEF_4B);//report as physical order

	// user unit
	uoLBASize = (U64)(pCurrentCMD->APUCQ.all[2]) + ((U64)(pCurrentCMD->APUCQ.info.scsi_cmd.scsi_B9) << 32);
	if (0 == uoLBASize) {
		U32 ulDiskSizeInMB;
		U16 uwRealPagePerUnit = (FW_RUN_SLC_MODE_EN) ? guwFastPagePlanesPerBlock : gFlhEnv.uwPagePerBlock;
		U8 ubDiskSizeInMBLog;
		ulDiskSizeInMB = (((gFlhEnv.uwPageByteCnt >> VUC_BYTE_TO_KB_LOG) * uwRealPagePerUnit * gFlhEnv.ulBlockPerPlaneBank * gFlhEnv.ubPlanePerTarget * gFlhEnv.ubCENumber) >> VUC_KB_TO_MB_LOG);
		ubDiskSizeInMBLog = CalulateFloorLog2(ulDiskSizeInMB);
		ulDiskSizeInMB = (1 << ubDiskSizeInMBLog);
		uoLBASize = (U32)((((IDEMA_PARAM_B - (IDEMA_PARAM_A * IDEMA_PARAM_C)) * SIZE_1KB) + ((U64)(IDEMA_PARAM_A) * ulDiskSizeInMB)) / SIZE_1KB);
	}
	ulUnitSize = (ulData4kEntrysPerUnit << SECTORS_PER_4K_LOG);
	uwRequireUnitNum = (U16)CEILING_DIV((uoLBASize + PREFORMAT_512MB_EXTEND_LCA_UNIT_OP_NUMBER + PREFORMAT_1536MB_EXTEND_LCA_FOR_UNIFIED_LOG_UNIT_OP_NUMBER), ulUnitSize);

	// OP
	ubMinOPVBNum = CEILING_DIV(uwRequireUnitNum * VUC_GET_MAX_BAD_OP_PERCENT, VUC_GET_MAX_BAD_OVER_PROVISION);
	if (MIN_OP_UNIT_NUM_THRESHOLD > ubMinOPVBNum) {
		ubMinOPVBNum = MIN_OP_UNIT_NUM_THRESHOLD;
	}
	uwRequireUnitNum += ubMinOPVBNum;

	// RSV
	ubVTUnitNum = M_DIV_CEILING(VT_BLOCK_NUM_LIMIT, gubPlanesPerSuperPage);
	uwRequireUnitNum += gubTotalTableUnitNum;
	uwRequireUnitNum += (ubVTUnitNum + VT_CHILD_NUM + VT_OLD_CHILD_NUM + INIT_INFO_NUM);//for DBT and (VT) and (VT child) and (info blk) and (GR)
	uwRequireUnitNum += (META_RESERVE_NUM + GCGR_RESERVE_NUM + ERROR_RESERVE_NUM + WL_OP_RESERVE_NUM);//Add Reserve Unit
	if (WORDLINE_FOLDING_EN) {
		uwRequireUnitNum += WORDLINE_FOLDING_RESERVE_NUM;
	}
	if (D1_UNIT_EN || D3_GR_ALWAYS_SLC_EN) {
		uwRequireUnitNum += D3_GC_TRIGGER_THERSHOLD;
	}
	if (DRIVE_LOG_EN) {
		uwRequireUnitNum += DRIVE_LOG_RESERVE_NUM;
	}
	if (OPEN_BLOCK_RSMAP_EN) {
		uwRequireUnitNum += OPEN_BLOCK_RAIDECCMAP_UNIT_NUM;
		uwRequireUnitNum += OPEN_BLOCK_RAIDECCMAP_UNIT_RESERVE_NUM;
	}
	if (TWO_WORDLINE_PROTECTION_EN) {
		uwRequireUnitNum += PARITY_FOR_GCGR_UNIT_RESERVE_NUM;
	}

	// D1
	if (D1_UNIT_EN) {
		uwRequireUnitNum += D1_ATLEAST_NUM;//(D1_ERROR_RESERVE_NUM + D1_META_RESERVE_NUM + D1_GC_TRIGGER_THERSHOLD + D1_ENOUGH_UNIT);
	}

	// SYS, workaround: block number allocated by FTLPreFormatAssignBlock will increase unit number
	ubSystemAreaBlkNum = FTLPreFormatAssignBlock(0); // CE0 get most block num
	uwRequireUnitNum +=  (ubSystemAreaBlkNum / gFlhEnv.PlanePerDie); // System area unit num

#if (PHBLOCK_EN)
	// PH
	uwRequireUnitNum += PH_RESERVE_NUM;				// Product history block
#endif /* (PHBLOCK_EN) */

	if ((gFlhEnv.ulBlockPerPlaneBank * ((LUN_FOR_MORE_UNIT_EN) ? gFlhEnv.ubLUNperTarget : 1)) < uwRequireUnitNum) {
		//pCmd->ubState = CMD_ERROR;
		return;
	}

	uwMaxBadBlock = ((gFlhEnv.ulBlockPerPlaneBank * ((LUN_FOR_MORE_UNIT_EN) ? gFlhEnv.ubLUNperTarget : 1)) - uwRequireUnitNum) /
		((LUN_FOR_MORE_UNIT_EN) ? gFlhEnv.ubLUNperTarget : 1);
	puwMaxBadBlock[VUC_GET_MAX_BAD_NUMBER_PER_PLANE] = uwMaxBadBlock;
	puwMaxBadBlock[VUC_GET_MAX_BAD_NUMBER_PER_DIE] = uwMaxBadBlock * gubBurstsPerBank;

	pCurrentCMD->CmdInfo.btIsNoNeedBufCmd = FALSE;
}
#else /* (USB == HOST_MODE) */
void VUCGetMaxBlockPerPlane(VUC_OPT_HCMD_PTR_t pCmd)
{
	U64 uoLBASize = 0;
	U32 ulUnitSize = 0;
	U32 ulData4kEntrysPerUnit = M_GET_DATA_4KENTRY_PER_UNIT(FW_RUN_SLC_MODE_EN);
	U16 uwMaxBadBlockPerPlane;
	U16 uwMaxUnitNum = 0;
	U16 uwRequireUnitNum = 0;
	U16 *puwMaxBadBlock = NULL;
	U16 uwMinOPVBNum;
	U8 ubVTUnitNum;
	U8 ubSystemAreaBlkNum = 0;

	puwMaxBadBlock = (U16 *)gulVUCBufAddr;
	memset((void *)(gulVUCBufAddr), 0x00, DEF_512B);//report as physical order
	uoLBASize = pCmd->vuc_sqcmd.vendor.GetMaxBadPerPlane.uoLBA;
	if (0 == uoLBASize) {
		uoLBASize = guoDiskInfo.uoNativeSize;
	}

	ubSystemAreaBlkNum = FTLPreFormatAssignBlock(0); // CE0 get most block num
	ulUnitSize = ulData4kEntrysPerUnit << SECTORS_PER_4K_LOG;
	uwRequireUnitNum = (U16)CEILING_DIV((uoLBASize + PREFORMAT_512MB_EXTEND_LCA_UNIT_OP_NUMBER + ((D1_N28_WORKAROUND) ? 0 : PREFORMAT_1536MB_EXTEND_LCA_FOR_UNIFIED_LOG_UNIT_OP_NUMBER)), ulUnitSize);
	ubVTUnitNum = M_DIV_CEILING(VT_BLOCK_NUM_LIMIT, gubPlanesPerSuperPage);
	uwMinOPVBNum = CEILING_DIV(uwRequireUnitNum * VUC_GET_MAX_BAD_OP_PERCENT, VUC_GET_MAX_BAD_OVER_PROVISION);

	if (MIN_OP_UNIT_NUM_THRESHOLD > uwMinOPVBNum) {
		uwMinOPVBNum = MIN_OP_UNIT_NUM_THRESHOLD;
	}

	uwRequireUnitNum += (ubSystemAreaBlkNum / gFlhEnv.PlanePerDie); // System area unit num
	uwRequireUnitNum += PH_RESERVE_NUM;				// Product history block
	uwRequireUnitNum += uwMinOPVBNum;
	uwRequireUnitNum += (ubVTUnitNum + VT_CHILD_NUM + VT_OLD_CHILD_NUM + INIT_INFO_NUM);//for DBT and (VT) and (VT child) and (info blk) and (GR)
	uwRequireUnitNum += (META_RESERVE_NUM + GCGR_RESERVE_NUM + ERROR_RESERVE_NUM + WL_OP_RESERVE_NUM);//Add Reserve Unit
	if (WORDLINE_FOLDING_EN) {
		uwRequireUnitNum += WORDLINE_FOLDING_RESERVE_NUM;
	}
	if (DRIVE_LOG_EN) {
		uwRequireUnitNum += DRIVE_LOG_RESERVE_NUM;
	}
	if (OPEN_BLOCK_RSMAP_EN) {
		uwRequireUnitNum += OPEN_BLOCK_RAIDECCMAP_UNIT_NUM;
		uwRequireUnitNum += OPEN_BLOCK_RAIDECCMAP_UNIT_RESERVE_NUM;
	}
	if (TWO_WORDLINE_PROTECTION_EN) {
		uwRequireUnitNum += PARITY_FOR_GCGR_UNIT_RESERVE_NUM;
	}
	if (D1_UNIT_EN) {
		U16 uwD1LowLimit;
		uwRequireUnitNum += D3_GC_TRIGGER_THERSHOLD;
		FTL_D1_LOWER_LIMIT_GET(uwD1LowLimit);
		uwRequireUnitNum += uwD1LowLimit;
	}
	uwRequireUnitNum += gubTotalTableUnitNum;

	uwMaxUnitNum = (gFlhEnv.ulBlockPerPlaneBank * ((LUN_FOR_MORE_UNIT_EN) ? gFlhEnv.ubLUNperTarget : 1));
	if (uwMaxUnitNum < uwRequireUnitNum) {
		pCmd->ubState = CMD_ERROR;
		return;
	}
	uwMaxBadBlockPerPlane = (uwMaxUnitNum - uwRequireUnitNum) / ((LUN_FOR_MORE_UNIT_EN) ? gFlhEnv.ubLUNperTarget : 1);

	puwMaxBadBlock[VUC_GET_MAX_BAD_NUMBER_PER_PLANE] = uwMaxBadBlockPerPlane;
	puwMaxBadBlock[VUC_GET_MAX_BAD_NUMBER_PER_DIE] = uwMaxBadBlockPerPlane * gubBurstsPerBank;
}
#endif /* (USB == HOST_MODE) */

#if BURNER_MODE_EN
void VUCAssignReadRetryTable(VUC_OPT_HCMD_PTR_t pCmd)
{
	U8 *pubBufAddr = (U8 *)gulVUCBufAddr;
	U8 ubVersion = pCmd->vuc_sqcmd.raw_u8_data.by[52];
	U8 ubNandType = pCmd->vuc_sqcmd.raw_u8_data.by[53];
	U8 ubMonoDie = pCmd->vuc_sqcmd.raw_u8_data.by[54];
	RetryAssignReadRetryTable(pubBufAddr, ubVersion, ubNandType, ubMonoDie);
}
#endif /*BURNER_MODE_EN*/
/*
 * ---------------------------------------------------------------------------------------------------
 *   public
 * ---------------------------------------------------------------------------------------------------
 */
#if BURNER_MODE_EN
void VUCDownloadRetryTable(VUC_OPT_HCMD_PTR_t pCmd)
{
	U8 ubRelaxMode = (pCmd->vuc_sqcmd.raw_u8_data.by[49] & BIT0);
	U8 ubSLCInitReadCnt = pCmd->vuc_sqcmd.raw_u8_data.by[50];
	U8 ubTLCScratchTableCnt = pCmd->vuc_sqcmd.raw_u8_data.by[58];
	U8 ubTLCInitReadCnt = pCmd->vuc_sqcmd.raw_u8_data.by[59];
	U8 ubSLCStepNum = pCmd->vuc_sqcmd.raw_u8_data.by[52];
	U8 ubTLCStepNum = pCmd->vuc_sqcmd.raw_u8_data.by[53] + (pCmd->vuc_sqcmd.raw_u8_data.by[54] << 8);
	U8 ubVersion = pCmd->vuc_sqcmd.raw_u8_data.by[55];
	U8 ubNandType = pCmd->vuc_sqcmd.raw_u8_data.by[56];
	U8 ubMonoDie = pCmd->vuc_sqcmd.raw_u8_data.by[57];
	U8 *pubData = (U8 *)(pCmd->ulCurrentPhysicalMemoryAddr);
	if (FAIL == RetryDownloadRetryTable(pubData, ubRelaxMode, ubSLCInitReadCnt, ubTLCScratchTableCnt, ubTLCInitReadCnt, ubSLCStepNum, ubTLCStepNum, ubVersion, ubNandType, ubMonoDie)) {
		pCmd->ubState = CMD_ERROR;
		gVUCVar.ubVucCmdErrStatus = HOST_ERROR_FEATURE;
		return;
	}
}

#endif /*BURNER_MODE_EN*/

#if (USB != HOST_MODE) || RDT_MODE_EN
void VUC_GetFlashInfo(VUC_OPT_HCMD_PTR_t pCmd)
{
	U8 ubSubFea = pCmd->vuc_sqcmd.vendor.FlashInfo.ubSubFeature;
#if RDT_MODE_EN
	M_UART(RDT_INIT_, "\nVUC_GET_FLASH_INFO, sub fea = %b", ubSubFea);
#else
	M_UART(VUC_, "\nVUC_GET_FLASH_INFO");
#endif
	switch (ubSubFea) {
	case VUC_GET_ALL_FLASH_ID:
	case VUC_GET_SPECIFIC_FLASH_ID:
		VUC_GetFlashID(pCmd);
		break;
	case VUC_GET_UNIQUE_ID:
		VUC_GetUniqueID(pCmd);
		break;
#if !RDT_MODE_EN
	case VUC_GET_PARAMETER_TABLE:
		VUC_GetFlashParameterTable(pCmd);
		break;
	case VUC_GET_FLASH_READ_RETRY_TABLE:
		VUCGetFlashReadRetryTable(pCmd);
		break;
	case VUC_ASSIGN_READ_RETRY_TABLE:
		M_UART(VUC_, "\nVUCAssignReadRetryTable");

#if BURNER_MODE_EN
		VUCAssignReadRetryTable(pCmd);
#endif /*BURNER_MODE_EN*/
		break;
	case VUC_GET_MAX_BAD_PER_PLANE:
		VUCGetMaxBlockPerPlane(pCmd);
		break;

	case VUC_GET_BLOCK_NUMBER:
	case VUC_GET_SOFT_BIT_TABLE:
#endif /* !RDT_MODE_EN */
	default:
		pCmd->ubState = CMD_ERROR;
		break;
	}
}
#endif /* (USB != HOST_MODE) */

#if (USB == HOST_MODE) && (!RDT_MODE_EN)
void VUC_GetFlashID(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD)
{
	U8 ubSubFea = pCurrentCMD->APUCQ.info.scsi_cmd.scsi_B3;
	U8 ubCEDecoderEn = pCurrentCMD->APUCQ.info.scsi_cmd.scsi_B4;
	U8 ubPhysicalCE = pCurrentCMD->APUCQ.info.scsi_cmd.scsi_B5;
	U8 ubBackup = ((EFU_BANK3 *)gEFuseBank2347Info.ubEFuseBank3)->dw0.btCeDecoder;
	//	U8 *pBuf;

	M_UART(VUC_, "\nUSB_VUC_GET_FLASH_ID");
	M_UART(VUC_, "\nSubFea %b PCE %b CeDecoderEn %b", ubSubFea, ubPhysicalCE, ubCEDecoderEn);

	//	UartPrintf("\nUSB_VUC_GET_FLASH_ID");
	//	UartPrintf( "\nSubFea:%b PhyCE:%b CeDecoderEn:%b", ubSubFea, ubPhysicalCE, ubCEDecoderEn);

	((EFU_BANK3 *)gEFuseBank2347Info.ubEFuseBank3)->dw0.btCeDecoder = ubCEDecoderEn;

	memset((void *)ulBufAddr, 0x00, SIZE_512B);
	pCurrentCMD->CmdInfo.btIsNoNeedBufCmd = FALSE;
	//	pBuf = (void *)ulBufAddr;

	U8 ubCH, ubCE;
#if (MICRON_FSP_EN)
	//FlaResetAllCE(0xFF);
#else /*MICRON_FSP_EN*/
	FlaResetAllCE();
#endif /*MICRON_FSP_EN*/
	if (VUC_GET_ALL_FLASH_ID == ubSubFea) {
		U8 ubi;
		for (ubi = 0; ubi < MAX_PHYSICAL_CE_NUM; ubi++) { //for all max physical 32CE
			//			UartPrintf("\nCE:%d, ", ubi);
			if (TRUE == PhysicalCE2LogicalCHCE(ubCEDecoderEn, ubi, &ubCH, &ubCE)) {
				FlaReadID(ubCH, ubCE, 0x00, (U8 *)(ulBufAddr + ubi * DEF_8B));
			}
			//			UartPrintf("CH:%d, ubCE:%d --> id:%x", ubCH, ubCE, pBuf[ubi * DEF_8B]);
		}
	}
	else if (VUC_GET_SPECIFIC_FLASH_ID == ubSubFea) { //input CE is physical CE we need to change to logical
		if (TRUE == PhysicalCE2LogicalCHCE(ubCEDecoderEn, ubPhysicalCE, &ubCH, &ubCE)) {
			FlaReadID(ubCH, ubCE, 0x00, (U8 *)(ulBufAddr));
		}
	}
	((EFU_BANK3 *)gEFuseBank2347Info.ubEFuseBank3)->dw0.btCeDecoder = ubBackup;

}
#else /* (USB == HOST_MODE) && (!RDT_MODE_EN) */
void VUC_GetFlashID(VUC_OPT_HCMD_PTR_t pCmd)
{

	U8 ubPhysicalCE = pCmd->vuc_sqcmd.vendor.FlashInfo.ubCE;
	U8 ubSubFea = pCmd->vuc_sqcmd.vendor.FlashInfo.ubSubFeature;
	U8 ubCEDecoderEn = (U8) (pCmd->vuc_sqcmd.raw_data.dw[13] & BIT_MASK(8));
	U8 ubBackup = ((EFU_BANK3 *)gEFuseBank2347Info.ubEFuseBank3)->dw0.btCeDecoder;

#if RDT_MODE_EN
	M_UART(RDT_INIT_, "\nVUC_GET_FLASH_ID");
	M_UART(RDT_INIT_, "\nSubFea %b PCE %b CeDecoderEn %b", ubSubFea, ubPhysicalCE, ubCEDecoderEn);
#else
	M_UART(VUC_, "\nVUC_GET_FLASH_ID");
	M_UART(VUC_, "\nSubFea %b PCE %b CeDecoderEn %b", ubSubFea, ubPhysicalCE, ubCEDecoderEn);
#endif
	((EFU_BANK3 *)gEFuseBank2347Info.ubEFuseBank3)->dw0.btCeDecoder = ubCEDecoderEn;

	memset((void *)(gulVUCBufAddr), 0x00, DEF_512B);//report as physical order

#if (BURNER_MODE_EN)
	U8 ubCH, ubCE;
#if (MICRON_FSP_EN)
	//FlaResetAllCE();
#else /*MICRON_FSP_EN*/
	FlaResetAllCE();
#endif /*MICRON_FSP_EN*/

	if (VUC_GET_ALL_FLASH_ID == ubSubFea) {
		U8 ubi;
		for (ubi = 0; ubi < MAX_PHYSICAL_CE_NUM; ubi++) { //for all max physical 32CE
			if (TRUE == PhysicalCE2LogicalCHCE(ubCEDecoderEn, ubi, &ubCH, &ubCE)) {
				FlaReadID(ubCH, ubCE, 0x00, (U8 *)(gulVUCBufAddr + ubi * DEF_8B));
			}
		}
	}
	else if (VUC_GET_SPECIFIC_FLASH_ID == ubSubFea) { //input CE is physical CE we need to change to logical
		if (TRUE == PhysicalCE2LogicalCHCE(ubCEDecoderEn, ubPhysicalCE, &ubCH, &ubCE)) {
			FlaReadID(ubCH, ubCE, 0x00, (U8 *)(gulVUCBufAddr));
		}
	}
	else {
		pCmd->ubState = CMD_ERROR;
		//gsVucVar.ubVucCmdErrStatus = ERROR_PARAMETER;
	}

	if (TRUE == gFlhEnv.ulFlashDefaultType.BitMap.btNoSupport) {
		pCmd->ubState = CMD_ERROR;
		gVUCVar.ubVucCmdErrStatus = HOST_ERROR_FLH_STATUS_TIMEOUT;
	}
#else /* (BURNER_MODE_EN) */
	if (VUC_GET_ALL_FLASH_ID == ubSubFea) {
		U8 ubi;
		for (ubi = 0; ubi < MAX_PHYSICAL_CE_NUM; ubi++) { //for all max physical 32CE
			if (INVALID_CE_INDEX != M_FIP_GET_LOGICAL_CE(ubi)) {
				memcpy((U8 *)(gulVUCBufAddr + (ubi * DEF_8B)), (U8 *)gFlhEnv.ubFlashID, DEF_8B);
			}
			else {
				memset((U8 *)(gulVUCBufAddr + (ubi * DEF_8B)), 0, DEF_8B);
			}
		}
	}
	else if (VUC_GET_SPECIFIC_FLASH_ID == ubSubFea) { //input CE is physical CE we need to change to logical
		if (INVALID_CE_INDEX != M_FIP_GET_LOGICAL_CE(ubPhysicalCE)) {
			memcpy((U8 *)(gulVUCBufAddr), (U8 *)gFlhEnv.ubFlashID, DEF_8B);
		}
	}
	else {
		pCmd->ubState = CMD_ERROR;
	}
#endif /* (BURNER_MODE_EN) */
	((EFU_BANK3 *)gEFuseBank2347Info.ubEFuseBank3)->dw0.btCeDecoder = ubBackup;

}
#endif /* (USB == HOST_MODE) && (!RDT_MODE_EN) */

void VUC_GetTrimTable(VUC_OPT_HCMD_PTR_t pCmd)
{
	U8 ubSubFeature = pCmd->vuc_sqcmd.vendor.FlashInfo.ubSubFeature;
	if (VUC_GET_DPS_TABLE_SUBFEATURE == ubSubFeature) {
		VUC_GetDPSTable(pCmd);
#if !BURNER_MODE_EN
		pCmd->ubState = FW_PROCESS_DONE;
#endif
	}
#if BURNER_MODE_EN
	else if (VUC_GET_TRIM_TABLE_SUBFEATURE == ubSubFeature) {
		U8 ubChannel, ubCE;
		U8 ubPhysicalCE = pCmd->vuc_sqcmd.vendor.FlashInfo.ubCE;
		U8 ubDie = pCmd->vuc_sqcmd.vendor.FlashInfo.ubDie;

		M_UART(VUC_, "\nVUC_GET_TRIM_TABLE");
		M_UART(VUC_, "\nPCE %b Die %b", ubPhysicalCE, ubDie);
		if (TRUE == PhysicalCE2LogicalCHCE(0, ubPhysicalCE, &ubChannel, &ubCE)) {
#if (MICRON_FSP_EN)
			// to be continue ...
#else /*MICRON_FSP_EN*/
			FipGetTrimTable(ubChannel, ubCE, ubDie, (U8 *)gulVUCBufAddr);
			if (TRUE == gFlhEnv.ulFlashDefaultType.BitMap.btNoSupport) {
				pCmd->ubState = CMD_ERROR;
				gVUCVar.ubVucCmdErrStatus = HOST_ERROR_FLH_STATUS_TIMEOUT;
			}
#endif /*MICRON_FSP_EN*/
		}
	}
#endif
	else {
		pCmd->ubState = CMD_ERROR;
	}
}

void VUC_GetDPSTable(VUC_OPT_HCMD_PTR_t pCmd)
{
#if (!MICRON_FSP_EN)
	U8 ubCnt = 0, ubByteCntOffset = 0;
#if BURNER_MODE_EN
	U8 ubChannel, ubCECnt, ubDie;
	*(U8 *)(gulVUCBufAddr + ubByteCntOffset) = gFlhEnv.ubCENumber * gFlhEnv.ubLUNperTarget;
	ubByteCntOffset ++;
	*(U8 *)(gulVUCBufAddr + ubByteCntOffset) = FIP_DPS_TOTAL_SET_ADDR_NUM;
	ubByteCntOffset ++;
	*(U8 *)(gulVUCBufAddr + ubByteCntOffset) = FIP_DPS_SLC_NEED_SET_ADDR_NUM;
	ubByteCntOffset ++;
	*(U8 *)(gulVUCBufAddr + ubByteCntOffset) = FIP_DPS_SLC_NEED_PLUS_ADDR_NUM;
	ubByteCntOffset ++;
	for (ubCnt = 0; ubCnt < FIP_DPS_TOTAL_SET_ADDR_NUM; ubCnt++) {
		*(U8 *)(gulVUCBufAddr + ubByteCntOffset) = gubDPSAddr[ubCnt];
		ubByteCntOffset ++;
	}
	for (ubCnt = 0; ubCnt < FIP_DPS_TOTAL_SET_ADDR_NUM; ubCnt++) {
		if (ubCnt < FIP_DPS_FORTH_PARAMETER) {
			*(U8 *)(gulVUCBufAddr + ubByteCntOffset) = gubDPSData[ubCnt];
			ubByteCntOffset ++;
		}
		else {
			for (ubChannel = 0; ubChannel < gFlhEnv.ubChannelExistNum; ubChannel++) {
				for (ubCECnt = 0; ubCECnt  < gFlhEnv.ubCENumberInCh[ubChannel]; ubCECnt ++) {
					for (ubDie = 0; ubDie < gFlhEnv.ubLUNperTarget ; ubDie++) {
						*(U8 *)(gulVUCBufAddr + ubByteCntOffset) = gubDPSPlusData[ubChannel][ubCECnt][ubDie][ubCnt % FIP_DPS_FORTH_PARAMETER] - gubDPSData[ubCnt];
						ubByteCntOffset ++;
					}
				}

			}
		}
	}
#else
	memset((void *)gulVUCBufAddr, 0, sizeof(U8) * MAX_CHANNEL * MAX_CE_PER_CHANNEL * MAX_DIE_NUM * FIP_DPS_SLC_NEED_PLUS_ADDR_NUM);
	*(U8 *)(gulVUCBufAddr + ubByteCntOffset) = gFlhEnv.ubCENumber * gFlhEnv.ubLUNperTarget;
	ubByteCntOffset ++;
	*(U8 *)(gulVUCBufAddr + ubByteCntOffset) = FIP_DPS_TOTAL_SET_ADDR_NUM;
	ubByteCntOffset ++;
	*(U8 *)(gulVUCBufAddr + ubByteCntOffset) = FIP_DPS_SLC_NEED_SET_ADDR_NUM;
	ubByteCntOffset ++;
	*(U8 *)(gulVUCBufAddr + ubByteCntOffset) = FIP_DPS_SLC_NEED_PLUS_ADDR_NUM;
	ubByteCntOffset ++;
	for (ubCnt = 0; ubCnt < FIP_DPS_TOTAL_SET_ADDR_NUM; ubCnt++) {
		*(U8 *)(gulVUCBufAddr + ubByteCntOffset) = gubDPSAddr[ubCnt];
		ubByteCntOffset ++;
	}
	memcpy((void *)(gulVUCBufAddr + ubByteCntOffset), (void *)gubDPSPlusData, sizeof(U8) * MAX_CHANNEL * MAX_CE_PER_CHANNEL * MAX_DIE_NUM * FIP_DPS_SLC_NEED_PLUS_ADDR_NUM);
#endif
#endif /*!MICRON_FSP_EN*/
}

#if (USB == HOST_MODE) && (!RDT_MODE_EN)
void VUC_GetFlashStatus(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD)
{
	U8 ubChannel, ubCE, ubStatus;
	REG32 *pFlaReg = NULL;
	U8 *pBuf = (void *)ulBufAddr;

	memset((void *)ulBufAddr, 0x00, SIZE_16B);
	for (ubChannel = 0; ubChannel < gFlhEnv.ubChannelExistNum; ubChannel++) {
		pFlaReg = R32_FCTL_CH[ubChannel];
		R8_FCON[R8_FCON_FCE_ENB + (ubChannel)] = 0;//clear CE of this channel
		for (ubCE = 0; ubCE < gFlhEnv.ubCENumberInCh[ubChannel]; ubCE++) {
			FlaCEControl(ubChannel, ubCE, ENABLE);

			pFlaReg[R32_FCTL_PIO_CMD] = 0x70; //Send 0x70 to check status
			IdlePC(80);	//delay at least 50ns ( 4.5 ns * 12 = 54 ns )

			pFlaReg[R32_FCTL_HS_MODE] |= SET_DAT_DEFAULT;
			do {
				ubStatus = ((U8)pFlaReg[R32_FCTL_PIO_DAT]);
			} while ((ubStatus & 0xE0) != 0xE0);
			pBuf[(ubChannel * gFlhEnv.ubCENumberInCh[ubChannel]) + ubCE] = ubStatus;
			FlaCEControl(ubChannel, ubCE, DISABLE);
		}
		R8_FCON[R8_FCON_FCE_ENB + (ubChannel)] = 0;//clear CE of this channel
	}
}
#endif /* (USB == HOST_MODE) */
