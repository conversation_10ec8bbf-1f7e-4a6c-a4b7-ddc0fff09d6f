
/****************************************************************************
*
*  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
*  All rights reserved
*
*  The content of this document is confidential and shall be applied
*  subject to the terms and conditions of the license agreement and
*  other applicable laws. Any unauthorized access, use or disclosure
*  of this document is strictly prohibited and may be punishable
*  under laws.
*
*  raidecc_api.h
*
*
*
****************************************************************************/

#ifndef RAIDECCMAP_PARITYMAP_H_
#define RAIDECCMAP_PARITYMAP_H_

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "typedef.h"
#include "aom/aom_api.h"

/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */


/*
 * ---------------------------------------------------------------------------------------------------
 *  enum
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *  data types
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *   macros
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *   extern global variables
 * ---------------------------------------------------------------------------------------------------
 */


/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */

#endif /* RAIDECCMAPPARITYMAP_H_ */
