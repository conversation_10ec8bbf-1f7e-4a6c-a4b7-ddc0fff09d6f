/**************************************************************************/
/*                                                                        */
/*           Copyright (c) 2000-2017 by Phison Electronics Corp.          */
/*                                                                        */
/**************************************************************************/

/**************************************************************************/
/*                                                                        */
/*  FILE : shr_hal_sys_pmu_reg.h          PROJECT : PS5011                */
/*                                                                        */
/*                                                                        */
/*  DESCRIPTION                                                           */
/*                                                                        */
/*    This is the header file for pmu register re-definition                 */
/*                                                                        */
/*  RELEASE HISTORY                                                       */
/*                                                                        */
/*    DATE              NAME                DESCRIPTION                   */
/*                                                                        */
/*  2017-05-24          Eddie 	            Initial Version 1.0           */
/*                                                                        */
/**************************************************************************/

#ifndef _SHR_HAL_SYSTEM_PMU_REG_H_
#define _SHR_HAL_SYSTEM_PMU_REG_H_


#include "hal/sys/api/pmu/pmu_api.h" //merge@@@@@
//this file to do define mapping to E11 hal\sys\pmu\pmu_api.h //merge@@@@@

#define r8_SYS_PMU                               (R8_SYS_PMU)
#define r16_SYS_PMU                             (R16_SYS_PMU)
#define r32_SYS_PMU                             (R32_SYS_PMU)
#define r64_SYS_PMU                             (R64_SYS_PMU)
#define SYS0L_PMU_RST_CTRL               (R32_SYS0_PMU_RST_CTRL)
#define SYS0_DEASSERT_PCIERST()        (M_SYS0_RLS_PCIERST())
#if (E21_TODO)
#define SYS0_ASSERT_PCIERST()            do { \
	CPUARMClearCriticalRegisterBit((R32_SYS0_HOST_CTRL + R32_SYS0_PCIE_CTRL_REG), (PCIE_RESETN_BIT|PCIE_PHY_2RSTN_BIT));\
} while(0)
#else /* (E21_TODO) */
#define SYS0_ASSERT_PCIERST()            do { \
	CPUARMClearCriticalRegisterBit((r32_SYS_PMU + R32_SYS0_PMU_RSTN0_CTL), (CR_PCIE_PERSTN_BIT | CR_PCIE_RESETN_BIT));\
} while(0)
#endif /* (E21_TODO) */
#define SYS0_SET_RST_HW_MODE()        (M_SYS0_SET_RST_HW_MODE())
#define PMU_CLKREQB_DET_EN()            (M_PMU_CLKREQB_DET_EN())
#define PMU_GPIO_DET_EN()                   (M_PMU_CLKREQB_DET_EN())

#endif  // _SHR_HAL_SYSTEM_PMU_REG_H_
