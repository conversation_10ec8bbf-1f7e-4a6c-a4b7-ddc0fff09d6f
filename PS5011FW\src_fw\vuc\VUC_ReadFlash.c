#include "typedef.h"
#include "aom/aom_api.h"
#include "common/mem.h"
#include "nvme_api/nvme/shr_hal_nvme.h"
#include "hal/cop0/cop0.h"
#include "hal/cop0/cop0_api.h"
#include "hal/fip/fip.h"
#include "hal/dmac/dmac_api.h"
#include "hal/dmac/dmac_pop_cmd.h"
#include "hal/sys/api/efuc/efuse_api.h"
#if PS5017_EN
#include "hal/mr/mr_api.h"
#endif /* PS5017_EN */
#include "ftl/ftl_api.h"
#include "hal/fip/fpu.h"
#include "host/VUC_handler.h"
#include "vuc/VUC_command.h"
#include "vuc/VUC_ReadFlash.h"
#include "vuc/VUC_Utilities.h"
#include "vuc/VUC_DumpTable.h"
#include "VUC_MicronGetVTSweep.h"
#include "burner/Burner_api.h"
#include "retry/retry_api.h"

#define VUC_READFLASH_FRAMECOUNT_MAX (5)

static U32 gulVUCDirectReadAddr = 0;
U32 gulVUCDirectedCieOutInfo;
U32 gVUCReadCallBackAddr = (U32)VUCRead_Callback;
U32 gVUCReadChannel = 0;

void VUCRead_Callback(TIEOUT_FORMAT_t uoResult)
{


	if ((TMSG_TYPE_NO_ERROR == uoResult.HL.B32_to_B63.Info.btMSG_TYPE) && (FALSE == uoResult.HL.B32_to_B63.Read_1st_2.btErasePage)) { /* parasoft-suppress BD-PB-CC "uoResult is modified from another process" */
		gulVUCDirectedCieOutInfo = VUC_DIRECT_READ_NO_ERROR; // No error
	}

	gulTieoutLCA[TIEOUT_SINGLE_4K_LCA_OFFSET] = uoResult.HL.B0_to_B31.Read_1st.ulLCA;
}

AOM_VUC void static VUC_CallCop0Read(U32 ulPhysicalAddr, U32 ulPCA, U8 ubFrameNum, U8 ubNormal_or_Else, U16 uwRmpBlk, U32 ulSeed, U8 ubFWSpecific)
{
	COP0Status_t eCOP0Status;
	COP0ReadSQPara_t ReadSQPara = {{0}};
	BUF_TYPE_t ulBufPara = {0};
	P4KTable16B_t *pP4KTable = NULL;
	U32 ulECCInfo = 0;
	gulVUCDirectedCieOutInfo = 0;

	FWSetPara_t ulFWSet = {0};
#if (PS5017_EN || PS5021_EN)
	ulFWSet.ulFWSet = DBUF_VUC_BACKUP_READ_L4K_IDX + (ubFrameNum); //S17-802
#else /* (PS5017_EN || PS5021_EN) */
	ulFWSet.ulFWSet = IRAM1_RSV_OFF + (ubFrameNum * sizeof (P4KTable16B_t));
#endif /* (PS5017_EN || PS5021_EN) */
	ulFWSet.ubZInfo = MAX_ZINFO;

	cmd_table_t uoCallbackInfo = {(U32)VUCRead_Callback, {0}};

	COP0API_FillCOP0ReadSQUserData0Para(COP0_R_INITIAL_SCAN, &ReadSQPara);

	ulBufPara.A.ulBUF_ADR = ulPhysicalAddr + ubFrameNum * DEF_KB(4);

	ReadSQPara.UserData0.btSLCMode = (ubNormal_or_Else == 0 ) ? FALSE : TRUE;
	ReadSQPara.UserData0.btSLCSetMethod = (uwRmpBlk == 0) ? (COP0_TIE_D1SLC_MODE_FROM_TIEIN) : (COP0_TIE_D1SLC_MODE_FROM_VBRMP);
	ReadSQPara.UserData0.TagID = TAG_ID_ALLOCATE;
	uoCallbackInfo.ulData.ulFLHInfo.btKeepTag = TRUE;
	ReadSQPara.ulPCA.ulAll = (ulPCA + ubFrameNum);
	ReadSQPara.UserData0.btRUTBps = (uwRmpBlk == 0) ? TRUE : FALSE;
	ReadSQPara.UserData0.btVBRMPBps = (uwRmpBlk == 0) ? TRUE : FALSE;
	ReadSQPara.UserData0.AttrMTTemplate = COP0_MT_TEMP_BUF_ADDR_READ;
	ReadSQPara.UserData0.L4KNum = 0;

	ReadSQPara.UserData0.DataDef |= COP0_SEED_EN_BIT;
	ReadSQPara.ulSeed.Seed = ulSeed;

	ReadSQPara.BufVld.ubBufType = BUF_TYPE_A;
	ReadSQPara.BufVld.pulBufInfoPtr = &ulBufPara;
	ReadSQPara.UserData0.btVUCRead = (uwRmpBlk == 0) ? TRUE : FALSE;
	ReadSQPara.UserData0.btUseParamSeed = (ubFWSpecific) ? FALSE : TRUE;
#if PS5017_EN
	if (FALSE == ReadSQPara.UserData0.btUseParamSeed) {
		ReadSQPara.UserData0.btSysArea = (ulSeed) ? TRUE : FALSE;
	}
#endif /* PS5017_EN */
	ReadSQPara.UserData0.btReadBackUp4K = TRUE;
	ReadSQPara.UserData0.DataDef |= COP0_FWSET_EN_BIT;
	ReadSQPara.pulFWSetPtr = &ulFWSet;
	eCOP0Status = COP0API_SendReadSQ(&ReadSQPara, &uoCallbackInfo);
	M_FW_ASSERT(ASSERT_VUC_0x0AC8, eCOP0Status.btSendCmdSuccess);

	gulTieoutLCA[TIEOUT_SINGLE_4K_LCA_OFFSET] = LCA_BEFORE_READ;
	while (LCA_BEFORE_READ == gulTieoutLCA[TIEOUT_SINGLE_4K_LCA_OFFSET]) {
		FWCop0Waiting();
	}

	if (gulVUCDirectedCieOutInfo & CRC16_ERR_UPD_BIT) {
		ulECCInfo |= VUC_DIRECT_READ_CRC_BIT;
	}
	if (gulVUCDirectedCieOutInfo & ERASE_PAGE_UPD_BIT) {
		ulECCInfo |= VUC_DIRECT_READ_ERASE_BIT;
	}
	if (gulVUCDirectedCieOutInfo & UNCORR_UPD_BIT) {
		ulECCInfo |= VUC_DIRECT_READ_ECC_BIT;

	}

#if (PS5017_EN || PS5021_EN)
	pP4KTable = (P4KTable16B_t *)(DBUF_VUC_BACKUP_READ_L4K_BASE);
#else /* (PS5017_EN || PS5021_EN) */
	pP4KTable = (P4KTable16B_t *)(IRAM_BASE + IRAM1_RSV_OFF);
#endif /* (PS5017_EN || PS5021_EN) */

	memcpy((void *)(ulPhysicalAddr + DEF_KB(16) + VUC_DIRECT_READ_P4K_FIRST_4BYTE_OFFSET + ubFrameNum * DEF_4B), (void *)& pP4KTable[ubFrameNum].ulL4K_LCA, DEF_4B);
	memcpy((void *)(ulPhysicalAddr + DEF_KB(16) + VUC_DIRECT_READ_P4K_SECOND_4BYTE_OFFSET + ubFrameNum * DEF_4B), (void *)&pP4KTable[ubFrameNum].Para0x04.ulAll, DEF_4B);
	memcpy((void *)(ulPhysicalAddr + DEF_KB(16) + VUC_DIRECT_READ_P4K_THIRD_4BYTE_OFFSET + ubFrameNum * DEF_4B), (void *)&pP4KTable[ubFrameNum].Para0x08.ulAll, DEF_4B);
	memcpy((void *)(ulPhysicalAddr + DEF_KB(16) + VUC_DIRECT_READ_P4K_FOURTH_4BYTE_OFFSET + ubFrameNum * DEF_4B), (void *)&pP4KTable[ubFrameNum].Para0x0C.ulAll, DEF_4B);
	memcpy((void *)(ulPhysicalAddr + DEF_KB(16) + VUC_DIRECT_READ_UNC_OFFSET + ubFrameNum * DEF_4B), (void *)&ulECCInfo, sizeof(ulECCInfo));

}

#if (BURNER_MODE_EN)

void static VUC_CallFpuRead(U32 ulPCA, U8 ubFrameNum, U8 ubNormal_or_Else)
{
#if !E21_TODO
	U8 ubFrame, ubLMU = 0, ubCH, ubCE;
	U8 ubRuleIndex = (ubNormal_or_Else == 0) ? COP0_PCA_RULE_0 : COP0_PCA_RULE_2;
	U32 ulIBFDumpAdr = 0;

	M_VUC_DIRECTED_READ_SET_ALL_CH_NON_INT();
	M_VUC_DIRECTED_READ_SET_ALL_CH_FORCE_DATA();
	FlaSetRandomizeConfig(M_GET_3DRANDOMIZER_ENABLE(), DISABLE, DISABLE, gFlhEnv.uwPageByteCnt >> 12);

	if (0 == ubNormal_or_Else) {

		ubFrame = ((ulPCA >> gPCARule_Entry.ubShift[ubRuleIndex]) & gPCARule_Entry.ulMask);
		ubLMU = ((ulPCA >> gPCARule_LMU.ubShift[ubRuleIndex]) & gPCARule_LMU.ulMask) ;
		ubCH = ((ulPCA >> gPCARule_Channel.ubShift[ubRuleIndex]) & gPCARule_Channel.ulMask);
		ubCE = ((ulPCA >> gPCARule_Bank.ubShift[ubRuleIndex])& gPCARule_Bank.ulMask);
#if (MICRON_FSP_EN)
#if (((IM_B47R) || IM_B37R || (IM_N48R_NEED_CHECK)) && (!PS5017_EN))
		M_FIP_SET_FPU(ubCH, fpu_entry_tlc_1p_30_read_bin0);
#elif (MICRON_S17_E21_140S_EN)
		M_FIP_SET_FPU(ubCH, fpu_entry_tlc_1p_30_read);
#else /* (((IM_B47R) || (IM_N48R_NEED_CHECK)) && (!PS5017_EN)) */
		M_FIP_SET_FPU(ubCH, fpu_entry_m3d_tlc_1p_30_read);
#endif /* (((IM_B47R) || (IM_N48R_NEED_CHECK)) && (!PS5017_EN)) */
#else /*(MICRON_FSP_EN)*/
		if (LOWER_PAGE == ubLMU) {
			M_FIP_SET_FPU(ubCH, fpu_entry_tlc_read_lower);
		}
		else if (MIDDLE_PAGE == ubLMU) {
			M_FIP_SET_FPU(ubCH, fpu_entry_tlc_read_middle);
		}
		else if (UPPER_PAGE == ubLMU) {
			M_FIP_SET_FPU(ubCH, fpu_entry_tlc_read_upper);
		}
#endif /*(MICRON_FSP_EN)*/
	}
	else {
		ubFrame = ((ulPCA >> gPCARule_Entry.ubShift[ubRuleIndex]) & gPCARule_Entry.ulMask);
		ubCH = ((ulPCA >> gPCARule_Channel.ubShift[ubRuleIndex]) & gPCARule_Channel.ulMask);
		ubCE = ((ulPCA >> gPCARule_Bank.ubShift[ubRuleIndex])& gPCARule_Bank.ulMask);
#if (PS5017_EN)
		M_FIP_SET_FPU(ubCH, fpu_entry_slc_read);
#else /* (PS5017_EN) */
		M_FIP_SET_FPU(ubCH, fpu_entry_slc_read_bin0);
#endif /* (PS5017_EN) */
	}

	M_FIP_VUC_SET_NORMAL_AND_ERROR_TARGET_TO_CPU0(ubCH);
	M_FIP_VUC_ASSIGN_IFSA(ubCH, ulPCA, ubFrameNum);

	FlaCEControl(ubCH, ubCE, ENABLE);

	FlaDMAConfig(ubCH, ubFrame + ubFrameNum, 1, READ_SPARE_IRAM_OFFSET + ubFrameNum * L4K_SIZE, BURNER_VENDOR_BUF_BASE + ubFrameNum * DEF_KB(4));

	M_FIP_TRIG_FPU(ubCH);

	M_RTT_IDLE_MS(3);
	FlaCheckFPUBusy(ubCH, 50);

	//M_UART(VUC_, "This is generated by FPU => CH: %u CE: %u Frame: %u Plane: %u Block: %lu Page: %lu LMU: %u", ubCH, ubCE, ubFrameNum, ubPlane, uwBlock, uwPage, ubLMU );

	// init 0xA & 0xD
	((U16 *)gFpuEntryList.fpu_entry_dma_r_raw)[R16_DMA_RAW_CMD_OFFSET] &= ~FPU_READ_RAW_MODE_SHIFT_MASK; //MODE
	((U16 *)gFpuEntryList.fpu_entry_dma_r_raw)[R16_DMA_RAW_CMD_OFFSET] &= ~FPU_READ_RAW_LOGIC_SHIFT_MASK; //LOGIC
	((U16 *)gFpuEntryList.fpu_entry_dma_r_raw)[R16_DMA_RAW_CMD_OFFSET] &= ~FPU_READ_RAW_DES_PTR_SHIFT_MASK; //DST PTR
	((U16 *)gFpuEntryList.fpu_entry_dump_ibuf)[0] &= ~FPU_IBF_PTR_SHIFT_MASK;

	// read first frame
	((U16 *)gFpuEntryList.fpu_entry_dma_r_raw)[R16_DMA_RAW_CMD_OFFSET] |= (2 << FPU_READ_RAW_LOGIC_SHIFT); // Logic: Nop
	((U16 *)gFpuEntryList.fpu_entry_dma_r_raw)[R16_DMA_RAW_CMD_OFFSET] |= (0 << FPU_READ_RAW_MODE_SHIFT); // Mode: fisrt LDPC frame
	((U16 *)gFpuEntryList.fpu_entry_dma_r_raw)[R16_DMA_RAW_CMD_OFFSET] |= (2 << FPU_READ_RAW_DES_PTR_SHIFT); // Des: IBF2

	M_FIP_READ_RAW_AUTO_SHIFT_DIS(ubCH);
	M_FIP_SET_FPU(ubCH, fpu_entry_dma_r_raw);
	M_FIP_TRIG_FPU(ubCH);

	FlaCheckFPUBusy(ubCH, 50);

	// restore 0xA & 0xD
	((U16 *)gFpuEntryList.fpu_entry_dma_r_raw)[R16_DMA_RAW_CMD_OFFSET] &= ~FPU_READ_RAW_MODE_SHIFT_MASK; //MODE
	((U16 *)gFpuEntryList.fpu_entry_dma_r_raw)[R16_DMA_RAW_CMD_OFFSET] &= ~FPU_READ_RAW_LOGIC_SHIFT_MASK; //LOGIC
	((U16 *)gFpuEntryList.fpu_entry_dma_r_raw)[R16_DMA_RAW_CMD_OFFSET] &= ~FPU_READ_RAW_DES_PTR_SHIFT_MASK; //DST PTR

	// read second frame
	((U16 *)gFpuEntryList.fpu_entry_dma_r_raw)[R16_DMA_RAW_CMD_OFFSET] |= (2 << FPU_READ_RAW_LOGIC_SHIFT);// Logic: Nop
	((U16 *)gFpuEntryList.fpu_entry_dma_r_raw)[R16_DMA_RAW_CMD_OFFSET] |= (1 << FPU_READ_RAW_MODE_SHIFT);// Mode: second LDPC frame
	((U16 *)gFpuEntryList.fpu_entry_dma_r_raw)[R16_DMA_RAW_CMD_OFFSET] |= (2 << FPU_READ_RAW_DES_PTR_SHIFT);// Des: IBF2

	M_FIP_READ_RAW_AUTO_SHIFT_EN(ubCH);
	M_FIP_SET_FPU(ubCH, fpu_entry_dma_r_raw);
	M_FIP_TRIG_FPU(ubCH);

	FlaCheckFPUBusy(ubCH, 50);

	// restore 0xA & 0xD
	((U16 *)gFpuEntryList.fpu_entry_dma_r_raw)[R16_DMA_RAW_CMD_OFFSET] &= ~FPU_READ_RAW_MODE_SHIFT_MASK; //MODE
	((U16 *)gFpuEntryList.fpu_entry_dma_r_raw)[R16_DMA_RAW_CMD_OFFSET] &= ~FPU_READ_RAW_LOGIC_SHIFT_MASK; //LOGIC
	((U16 *)gFpuEntryList.fpu_entry_dma_r_raw)[R16_DMA_RAW_CMD_OFFSET] &= ~FPU_READ_RAW_DES_PTR_SHIFT_MASK; //DST PTR

	// dump to dbuf
	M_FIP_DRAM_MULTI_DIS(ubCH);
	ulIBFDumpAdr = BURNER_VENDOR_BUF_BASE + ALL_IBF_DATA_SIZE * ubFrameNum ;
#if (!PS5017_EN)
	M_FIP_SET_RAW_DMA_ADR(ubCH, ulIBFDumpAdr);
#endif /* (!PS5017_EN) */
	((U16 *)gFpuEntryList.fpu_entry_dump_ibuf)[0] |= M_SET_FPU_IBF_PTR(2); // IbufPtr: IBF2
	M_FIP_SET_FPU(ubCH, fpu_entry_dump_ibuf);
	M_FIP_TRIG_FPU(ubCH);

	FlaCheckFPUBusy(ubCH, 50);

	M_FIP_VUC_CLR_IFSA();
	if (M_FIP_GET_INT_BUSY(ubCH) & CHK_INT_BUSY) {
		M_FIP_VUC_CLEAR_BUSY();
	}

	while (!M_DB_CHECK_EMPTY(DB_FLH_MSG_CQ)) {
		M_DB_TRIGGER_READ_CNT(DB_FLH_MSG_CQ, 1);
	}
	FlaCEControl(ubCH, ubCE, DISABLE);
#endif /* !E21_TODO */
}

#endif

void VUC_ReadPca(VUC_OPT_HCMD_PTR_t pCmd)
{
	/*
	NVME_FW: 4KB -> 4KB -> 4KB -> 4KB -> 3584B
	SATA_FW: 16KB -> 3584B
	*/

	if (TRUE == gFlhEnv.ulFlashDefaultType.BitMap.btNoSupport) {
		pCmd->ubState = CMD_ERROR;
		gVUCVar.ubVucCmdErrStatus = HOST_ERROR_FEATURE;
		return;
	}

	if ((0 == pCmd->vuc_sqcmd.vendor.DirectReadFlash.btRawData) && (((pCmd->vuc_sqcmd.vendor.DirectReadFlash.ulLength << BYTE_PER_DW_LOG) >> FRAME_SIZE_LOG) >= VUC_READFLASH_FRAMECOUNT_MAX)) {
		pCmd->ubState = CMD_ERROR;
		gVUCVar.ubVucCmdErrStatus = HOST_ERROR_LENGTH_NOT_SUPPORT;
		return;
	}

	U8 ubDumpMode, ubNormal_or_Else, ubStartFrame, ubLdpcMode, ubRuleIndex, ubFWSpecific, ubFrameNum = 0;
	U16 uwRemapBlk;
	U32 ulReadFrameCount, ulPCA, ulSeed, ulPhysicalAddr = 0 ;

	U8 ubLdpcTmp;
	U32 ulFCTL_NIT_CFG_TMP, ulCOP0_TMP, ulFCTL_FSA_SEL_TMP, ulFCTL_BACK_RESTORE_TMP;

	DMACParam_t DMACParam;

	// -------Save register config-------

	M_FIP_VUC_DIRECTED_READ_BACK_UP(ulFCTL_NIT_CFG_TMP, ulCOP0_TMP, ubLdpcTmp, ulFCTL_FSA_SEL_TMP, ulFCTL_BACK_RESTORE_TMP);

	ubLdpcMode = pCmd->vuc_sqcmd.vendor.DirectReadFlash.LDPCMode;
	ulSeed = (pCmd->vuc_sqcmd.vendor.DirectReadFlash.uwSeed_H << 16) | pCmd->vuc_sqcmd.vendor.DirectReadFlash.uwSeed;
	ubDumpMode = pCmd->vuc_sqcmd.vendor.DirectReadFlash.btRawData;
	ubNormal_or_Else = pCmd->vuc_sqcmd.vendor.DirectReadFlash.CmdMode;
	uwRemapBlk = pCmd->vuc_sqcmd.vendor.DirectReadFlash.btByPass;
	ulReadFrameCount = (pCmd->vuc_sqcmd.vendor.DirectReadFlash.ulLength << BYTE_PER_DW_LOG) >> FRAME_SIZE_LOG;
	ulPCA = (pCmd->vuc_sqcmd.vendor.DirectReadFlash.ulPCA);
	ubRuleIndex = (ubNormal_or_Else == 0) ? COP0_PCA_RULE_0 : COP0_PCA_RULE_2;
	ubFWSpecific = pCmd->vuc_sqcmd.vendor.DirectReadFlash.FWSpecific.btSeedModeEn;
	if (0 == pCmd->ulDoneLCA) {
#if (BURNER_MODE_EN)
		ulPhysicalAddr = pCmd->ulCurrentPhysicalMemoryAddr;
#else /*(BURNER_MODE_EN)*/
		BufferAdjustST3CCache_AOM_TABLE(BIT_BUFFER_FLOW_VUC_READ_WRITE, GET_BUFFER);
		BufferAllocateFWLBPBLink(FWLB_VUC_READ_WRITE_BIT, ALLOCATE_NO_USE_CTAG, ALLOCATE_NO_USE_LCA);
		ulPhysicalAddr = M_LB_TO_ADDR(LB_ID_FW, gFWLBMgr.Type[FWLB_VUC_READ_WRITE].uwLBOffset);
		gulVUCDirectReadAddr = ulPhysicalAddr;
#endif /*(BURNER_MODE_EN)*/
		if (ubNormal_or_Else) {
			ubStartFrame = (U8)((ulPCA >> gPCARule_Entry.ubShift[COP0_PCA_RULE_2]) & gPCARule_Entry.ulMask);
		}
		else {
			ubStartFrame = (U8)((ulPCA >> gPCARule_Entry.ubShift[COP0_PCA_RULE_0]) & gPCARule_Entry.ulMask);
		}

		M_CLR_COP0_LCA_CMP();

		if (0 == ubDumpMode) {
			FlaSwitchLDPCHandler(ubLdpcMode);
		}

		DMACParam.DMACSetValue.ulDestAddr = ulPhysicalAddr;
		DMACParam.DMACSetValue.ulLowValue = 0;
		DMACParam.DMACSetValue.ulHighValue = 0;
		DMACParam.DMACSetValue.ul32ByteNum = SIZE_IN_32B(pCmd->vuc_sqcmd.vendor.DirectReadFlash.ulLength << BYTE_PER_DW_LOG);
		DMACParam.DMACSetValue.btValidate = FALSE;
		gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
		DMACSetValue(&DMACParam, gulDMACDirectWaitDone_Callback, 0);
		while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
			DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
		}

		if (0 == ubDumpMode) {
			M_FIP_VUC_DIRECTED_READ_SOURCE_DATA();
			M_CLR_COP0_CONV(COP0_MT_TEMP_BUF_ADDR_READ);
		}
		else if (1 == ubDumpMode) {

			M_FIP_VUC_DIRECTED_READ_RAW_DATA();
			M_SET_COP0_CONV(COP0_MT_TEMP_BUF_ADDR_READ);

		}
		if (0 == ubDumpMode) {
			while ((ubStartFrame < gub4kEntrysPerPlane) && (ubFrameNum < ulReadFrameCount) ) {  /* parasoft-suppress BD-PB-CC "Variable's value was reference to HW Register"*/
				VUC_CallCop0Read(ulPhysicalAddr, ulPCA, ubFrameNum, ubNormal_or_Else, uwRemapBlk, ulSeed, ubFWSpecific);

				ubFrameNum++;
				ubStartFrame++;

			}

		}
		else {
#if (BURNER_MODE_EN)
			if (!VT_SWEEP_EN) {
				while ((ubStartFrame < gub4kEntrysPerPlane) && (ubFrameNum < ulReadFrameCount) ) {
					VUC_CallFpuRead(FTLTransferToCop0CieInPCA(ulPCA), ubFrameNum, ubNormal_or_Else);

					ubFrameNum++;
					ubStartFrame++;

				}
			}
			else {
#if (VT_SWEEP_EN && PS5013_EN)
				VUCMicronReadPageRawData(BURNER_VENDOR_BUF_BASE, ulPCA, ubRuleIndex);
#endif /* (VT_SWEEP_EN && PS5013_EN) */
			}
#endif /* (BURNER_MODE_EN) */
		}
#if (!BURNER_MODE_EN)
#if (HOST_MODE == NVME)
		DMACParam.ulSourceAddr = ulPhysicalAddr;
		DMACParam.ulDestAddr = pCmd->ulCurrentPhysicalMemoryAddr;
		DMACParam.ul32ByteNum = SIZE_IN_32B(BC_4KB) ;
		gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
		DMACCopy(DMAC_MODE_NORMAL_QUEUE, &DMACParam, gulDMACDirectWaitDone_Callback, 0);
		while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
			DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
		}
#else /*(HOST_MODE == NVME)*/
		U8 ubDone4KCount = 0;

		for (ubDone4KCount = 0; ubDone4KCount < ulReadFrameCount; ubDone4KCount++) {
			DMACParam.ulSourceAddr = gulVUCDirectReadAddr + (U32)BC_4KB * (ubDone4KCount);
			DMACParam.ulDestAddr = pCmd->ulCurrentPhysicalMemoryAddr + (U32)BC_4KB * (ubDone4KCount);
			DMACParam.ul32ByteNum = SIZE_IN_32B(BC_4KB);
			gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
			DMACCopy(DMAC_MODE_NORMAL_QUEUE, &DMACParam, gulDMACDirectWaitDone_Callback, 0);
			while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
				DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
			}
		}

		if ((pCmd->vuc_sqcmd.vendor.DirectReadFlash.ulLength << BYTE_PER_DW_LOG) <= VUC_HANDLE_SIZE_UNIT_SATA) {
			BufferFreeFWLBPBLink(FWLB_VUC_READ_WRITE_BIT);
			BufferAdjustST3CCache_AOM_TABLE(BIT_BUFFER_FLOW_VUC_READ_WRITE, RETURN_BUFFER);
		}
#endif /*(HOST_MODE == NVME)*/
#endif /*(!BURNER_MODE_EN)*/
	}
	else {
		DMACParam.ulSourceAddr = gulVUCDirectReadAddr + (U32)BC_4KB * (pCmd->ulDoneLCA );
		DMACParam.ulDestAddr = pCmd->ulCurrentPhysicalMemoryAddr;
		DMACParam.ul32ByteNum = SIZE_IN_32B(BC_4KB) ;
		gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
		DMACCopy(DMAC_MODE_NORMAL_QUEUE, &DMACParam, gulDMACDirectWaitDone_Callback, 0);
		while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
			DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
		}
	}
#if (!BURNER_MODE_EN)
	if (pCmd->ulDoneLCA == pCmd->ulTotalLCA - 1) {
		BufferFreeFWLBPBLink(FWLB_VUC_READ_WRITE_BIT);
		BufferAdjustST3CCache_AOM_TABLE(BIT_BUFFER_FLOW_VUC_READ_WRITE, RETURN_BUFFER);
	}
#endif /*(!BURNER_MODE_EN)*/

	//-------Restore register config-------

	M_FIP_VUC_DIRECTED_READ_RESTORE(ulFCTL_NIT_CFG_TMP, ulCOP0_TMP, ulFCTL_FSA_SEL_TMP, ulFCTL_BACK_RESTORE_TMP);
	FlaSwitchLDPCHandler(ubLdpcTmp);

}

void VUC_ReadPage(VUC_OPT_HCMD_PTR_t pCmd)
{
	/*
	NVME_FW: 4KB -> 4KB -> 4KB -> 4KB -> 3584B
	SATA_FW: 16KB -> 3584B
	*/

	if (TRUE == gFlhEnv.ulFlashDefaultType.BitMap.btNoSupport) {
		pCmd->ubState = CMD_ERROR;
		gVUCVar.ubVucCmdErrStatus = HOST_ERROR_FEATURE;
		return;
	}

	if ((0 == pCmd->vuc_sqcmd.vendor.DirectReadFlash.btRawData) && (((pCmd->vuc_sqcmd.vendor.DirectReadFlash.ulLength << BYTE_PER_DW_LOG) >> FRAME_SIZE_LOG) >= VUC_READFLASH_FRAMECOUNT_MAX)) {
		pCmd->ubState = CMD_ERROR;
		gVUCVar.ubVucCmdErrStatus = HOST_ERROR_LENGTH_NOT_SUPPORT;
		return;
	}

	U8 ubDumpMode, ubNormal_or_Else, ubCE, ubLMU, ubRuleIndex, ubLdpcMode, ubFWSpecific, ubFrameNum = 0;
	U16 uwBlock, uwPage, uwNode, uwRmpBlk;
	U32 ulReadFrameCount, ulSeed, ulPCA = 0, ulPhysicalAddr = 0;

	U8  ubLdpcTmp;
	U8 ubLMUNum = 0;
	U32 ulFCTL_NIT_CFG_TMP, ulCOP0_TMP, ulFCTL_FSA_SEL_TMP, ulFCTL_BACK_RESTORE_TMP;

	DMACParam_t DMACParam;

	// -------Save register config-------

	M_FIP_VUC_DIRECTED_READ_BACK_UP(ulFCTL_NIT_CFG_TMP, ulCOP0_TMP, ubLdpcTmp, ulFCTL_FSA_SEL_TMP, ulFCTL_BACK_RESTORE_TMP);

	FlashAccessInfo_t FlaInfo = {0};

	ubLdpcMode = pCmd->vuc_sqcmd.vendor.DirectReadFlash.LDPCMode;
	ulSeed = (pCmd->vuc_sqcmd.vendor.DirectReadFlash.uwSeed_H << 16) | pCmd->vuc_sqcmd.vendor.DirectReadFlash.uwSeed;
	ubDumpMode = pCmd->vuc_sqcmd.vendor.DirectReadFlash.btRawData;
	ubNormal_or_Else = pCmd->vuc_sqcmd.vendor.DirectReadFlash.CmdMode;
	uwRmpBlk = pCmd->vuc_sqcmd.vendor.DirectReadFlash.btByPass;
	ulReadFrameCount = (pCmd->vuc_sqcmd.vendor.DirectReadFlash.ulLength << BYTE_PER_DW_LOG) >> FRAME_SIZE_LOG;
	ubFWSpecific = pCmd->vuc_sqcmd.vendor.DirectReadFlash.FWSpecific.btSeedModeEn;

	uwNode = pCmd->vuc_sqcmd.vendor.DirectReadFlash.uwNode;
	uwPage = pCmd->vuc_sqcmd.vendor.DirectReadFlash.uwPage;
	uwBlock = pCmd->vuc_sqcmd.vendor.DirectReadFlash.uwBlock;
	ubLMU = pCmd->vuc_sqcmd.vendor.DirectReadFlash.ubLMU;
	ubCE = pCmd->vuc_sqcmd.vendor.DirectReadFlash.ubCE;

	FlaInfo.ubChannel = ubCE % gFlhEnv.ubChannelExistNum;
	FlaInfo.ubFlashCE = ubCE / gFlhEnv.ubChannelExistNum;
	FlaInfo.ubFrame = uwNode;
	FlaInfo.ubPlane = (U8)(uwBlock & gubBurstsPerBankMask);
	FlaInfo.ubLUN = (uwBlock >> gubBurstsPerBankLog) >> gPCARule_Block.ubBit_No;
	FlaInfo.ulBlock = (uwBlock >> gubBurstsPerBankLog) & gPCARule_Block.ulMask;
	FlaInfo.uwPage = uwPage;

	M_UART(VUC_, "AP block: %lu, gubBurstsPerBankLog: %lu, gPCARule_Block.ubBit_No: %lu, gPCARule_Block.ulMask: %lu\n", uwBlock, gubBurstsPerBankLog, gPCARule_Block.ubBit_No, gPCARule_Block.ulMask );

	ubRuleIndex = (ubNormal_or_Else == 0) ? COP0_PCA_RULE_0 : COP0_PCA_RULE_2;
	ubLMUNum = (ubNormal_or_Else == 0) ? gubLMUNumber : 1;
	if (uwRmpBlk) {
		ulPCA = FlaInfo.ubFrame + (FlaInfo.ubPlane * gub4kEntrysPerPlane) + (ubCE * gub4kEntrysPerPlane * gubBurstsPerBank * ubLMUNum);
		ulPCA += (FlaInfo.ubLUN * gub4kEntrysPerPlane * gubBurstsPerBank * ubLMUNum * gubCENumber);
		ulPCA += (FlaInfo.uwPage * gub4kEntrysPerPlane * gubBurstsPerBank * ubLMUNum * gubCENumber * gubDieNumber);
		ulPCA += (FlaInfo.ulBlock << gub4kEntrysPerUnitAlignLog);

		if (COP0_PCA_RULE_0 == ubRuleIndex) {
			ulPCA += (ubLMU * gub4kEntrysPerPlane * gubBurstsPerBank);
		}
		ulPCA = FTLTransferToCop0CieInPCA(ulPCA);
	}
	else {
		ulPCA = FlaGetPCA(FlaInfo.ubPlane, FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubLUN, FlaInfo.uwPage, FlaInfo.ulBlock, ubRuleIndex);
		ulPCA |= (FlaInfo.ubFrame & gPCARule_Entry.ulMask) << gPCARule_Entry.ubShift[ubRuleIndex];
		if (COP0_PCA_RULE_0 == ubRuleIndex) {
			ulPCA |= (ubLMU & gPCARule_LMU.ulMask) << gPCARule_LMU.ubShift[ubRuleIndex];
		}
	}

	M_UART(VUC_, "CH: %u CE: %u Frame: %u Plane: %u Lun: %u Block: %lu Page: %lu", FlaInfo.ubChannel, FlaInfo.ubFlashCE, FlaInfo.ubFrame, FlaInfo.ubPlane, FlaInfo.ubLUN, FlaInfo.ulBlock, FlaInfo.uwPage );
	M_UART(VUC_, "PCA: %lu\n", ulPCA);


	if (0 == pCmd->ulDoneLCA) {
#if (BURNER_MODE_EN)
		ulPhysicalAddr = pCmd->ulCurrentPhysicalMemoryAddr;
#else /*(BURNER_MODE_EN)*/
		BufferAdjustST3CCache_AOM_TABLE(BIT_BUFFER_FLOW_VUC_READ_WRITE, GET_BUFFER);
		BufferAllocateFWLBPBLink(FWLB_VUC_READ_WRITE_BIT, ALLOCATE_NO_USE_CTAG, ALLOCATE_NO_USE_LCA);
		ulPhysicalAddr = M_LB_TO_ADDR(LB_ID_FW, gFWLBMgr.Type[FWLB_VUC_READ_WRITE].uwLBOffset);
		gulVUCDirectReadAddr = ulPhysicalAddr;
#endif /*(BURNER_MODE_EN)*/
		M_CLR_COP0_LCA_CMP();

		if (0 == ubDumpMode) {
			FlaSwitchLDPCHandler(ubLdpcMode);
		}

		DMACParam.DMACSetValue.ulDestAddr = ulPhysicalAddr;
		DMACParam.DMACSetValue.ulLowValue = 0;
		DMACParam.DMACSetValue.ulHighValue = 0;
		DMACParam.DMACSetValue.ul32ByteNum = SIZE_IN_32B(pCmd->vuc_sqcmd.vendor.DirectReadFlash.ulLength << BYTE_PER_DW_LOG);
		DMACParam.DMACSetValue.btValidate = FALSE;
		gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
		DMACSetValue(&DMACParam, gulDMACDirectWaitDone_Callback, 0);
		while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
			DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
		}

		if (0 == ubDumpMode) {

			M_FIP_VUC_DIRECTED_READ_SOURCE_DATA();
			M_CLR_COP0_CONV(COP0_MT_TEMP_BUF_ADDR_READ);
		}
		else if (1 == ubDumpMode) {

			M_FIP_VUC_DIRECTED_READ_RAW_DATA();
			M_SET_COP0_CONV(COP0_MT_TEMP_BUF_ADDR_READ);

		}
		if (0 == ubDumpMode) {
			while ((FlaInfo.ubFrame < gub4kEntrysPerPlane) && (ubFrameNum < ulReadFrameCount) ) {

				VUC_CallCop0Read(ulPhysicalAddr, FTLReverseCop0CieInPCA(ulPCA), ubFrameNum, ubNormal_or_Else, uwRmpBlk, ulSeed, ubFWSpecific);

				ubFrameNum++;
				FlaInfo.ubFrame++;

			}
		}
		else {
#if (BURNER_MODE_EN)
			if (!VT_SWEEP_EN) {
				while ((FlaInfo.ubFrame < gub4kEntrysPerPlane) && (ubFrameNum < ulReadFrameCount) ) { /* parasoft-suppress BD-PB-CC "gub4kEntrysPerPlane is global variable" */
					VUC_CallFpuRead(ulPCA, ubFrameNum, ubNormal_or_Else);

					ubFrameNum++;
					FlaInfo.ubFrame++;

				}
			}
			else {
#if (VT_SWEEP_EN && PS5013_EN)
				VUCMicronReadPageRawData(BURNER_VENDOR_BUF_BASE, ulPCA, ubRuleIndex);
#endif /* (VT_SWEEP_EN && PS5013_EN) */
			}
#endif /* (BURNER_MODE_EN) */
		}
#if (!BURNER_MODE_EN)
#if (HOST_MODE == NVME)
		DMACParam.ulSourceAddr = ulPhysicalAddr;
		DMACParam.ulDestAddr = pCmd->ulCurrentPhysicalMemoryAddr;
		DMACParam.ul32ByteNum = SIZE_IN_32B(BC_4KB) ;
		gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
		DMACCopy(DMAC_MODE_NORMAL_QUEUE, &DMACParam, gulDMACDirectWaitDone_Callback, 0);
		while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
			DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
		}
#else /*(HOST_MODE == NVME)*/
		U8 ubDone4KCount = 0;

		for (ubDone4KCount = 0; ubDone4KCount < ulReadFrameCount; ubDone4KCount++) {
			DMACParam.ulSourceAddr = gulVUCDirectReadAddr + (U32)BC_4KB * (ubDone4KCount);
			DMACParam.ulDestAddr = pCmd->ulCurrentPhysicalMemoryAddr + (U32)BC_4KB * (ubDone4KCount);
			DMACParam.ul32ByteNum = SIZE_IN_32B(BC_4KB);
			gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
			DMACCopy(DMAC_MODE_NORMAL_QUEUE, &DMACParam, gulDMACDirectWaitDone_Callback, 0);
			while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
				DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
			}
		}

		if ((pCmd->vuc_sqcmd.vendor.DirectReadFlash.ulLength << BYTE_PER_DW_LOG) <= VUC_HANDLE_SIZE_UNIT_SATA) {
			BufferFreeFWLBPBLink(FWLB_VUC_READ_WRITE_BIT);
			BufferAdjustST3CCache_AOM_TABLE(BIT_BUFFER_FLOW_VUC_READ_WRITE, RETURN_BUFFER);
		}
#endif /*(HOST_MODE == NVME)*/
#endif /*(!BURNER_MODE_EN)*/
	}
	else {
		DMACParam.ulSourceAddr = gulVUCDirectReadAddr + (U32)BC_4KB * (pCmd->ulDoneLCA );
		DMACParam.ulDestAddr = pCmd->ulCurrentPhysicalMemoryAddr;
		DMACParam.ul32ByteNum = SIZE_IN_32B(BC_4KB) ;
		gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
		DMACCopy(DMAC_MODE_NORMAL_QUEUE, &DMACParam, gulDMACDirectWaitDone_Callback, 0);
		while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
			DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
		}
	}
#if (!BURNER_MODE_EN)
	if (pCmd->ulDoneLCA == pCmd->ulTotalLCA - 1) {
		BufferFreeFWLBPBLink(FWLB_VUC_READ_WRITE_BIT);
		BufferAdjustST3CCache_AOM_TABLE(BIT_BUFFER_FLOW_VUC_READ_WRITE, RETURN_BUFFER);
	}
#endif /*(!BURNER_MODE_EN)*/
	//-------Restore register config-------

	M_FIP_VUC_DIRECTED_READ_RESTORE(ulFCTL_NIT_CFG_TMP, ulCOP0_TMP, ulFCTL_FSA_SEL_TMP, ulFCTL_BACK_RESTORE_TMP);
	FlaSwitchLDPCHandler(ubLdpcTmp);
}
#if (HOST_MODE == SATA)
void VUC_DirectRead(VUC_OPT_HCMD_PTR_t pCmd)
{
	if (NCS_EN) {
		COP0Status_t COP0Status;
		cmd_table_t uoCallbackInfo = {0};
		COP0ReadSQPara_t ReadSQParameter = {{0}};
		BUF_TYPE_t ulBufParameter = {0};
		COP0_Attr_t ulCOP0AttributeBackup;  // backup cop0 attr
		FWSetPara_t ulFWSet[FRAMES_PER_PAGE] = {{0}};

		U8 ubBank, ubChannel, ubQueueIdx, ubPlane, ubALU, ubFrameIdx, ubLMU = 0, ubLUN = 0, ubNCSVerifyFlag;
#if PS5017_EN
		U8 ubNCSHaveEnteredRetryFlag = 0;
#endif /* PS5017_EN */
		U16 uwPage, uwBlk;
		U32 ulPCA;

		// indicate if direct read comes from NCS verification flow
		ubNCSVerifyFlag = pCmd->vuc_sqcmd.raw_u8_data.by[60];
		ubALU = pCmd->vuc_sqcmd.raw_u8_data.by[49];
		uwPage = pCmd->vuc_sqcmd.raw_u16_data.wd[27];
		uwBlk = pCmd->vuc_sqcmd.raw_u16_data.wd[28];
		ubQueueIdx = pCmd->vuc_sqcmd.raw_u8_data.by[59];
		ubFrameIdx = pCmd->vuc_sqcmd.raw_u8_data.by[40];
		ubLMU = pCmd->vuc_sqcmd.raw_u8_data.by[58];
		ubChannel = ubQueueIdx & gPCARule_Channel.ulMask;
		ubBank = ubQueueIdx / gubPlanesPerBurst;
		ubPlane = uwBlk & gubBurstsPerBankMask;
		uwBlk = uwBlk >> gubBurstsPerBankLog;
		ulPCA = 0;
		ulPCA |= (ubFrameIdx & gPCARule_Entry.ulMask) << gPCARule_Entry.ubShift[ubALU];		// Frame(Entry)
		ulPCA |= (ubPlane & gPCARule_Plane.ulMask) << gPCARule_Plane.ubShift[ubALU];		// Plane
		ulPCA |= (ubLMU & gPCARule_LMU.ulMask) << gPCARule_LMU.ubShift[ubALU];
		ulPCA |= ((ubChannel + (ubBank << gPCARule_Channel.ubBit_No)) & gPCARule_G_CE.ulMask) << gPCARule_G_CE.ubShift[ubALU];
		ulPCA |= (uwPage & gPCARule_Page.ulMask) << gPCARule_Page.ubShift[ubALU]; 	// Page
		ulPCA |= (uwBlk & gPCARule_Block.ulMask) << gPCARule_Block.ubShift[ubALU];		// Block
		ulPCA |= (ubLUN & gPCARule_LUN.ulMask) << gPCARule_LUN.ubShift[ubALU];			// LUN

#if (!PS5017_EN)
		ulCOP0AttributeBackup.ulAll = M_COP0_GET_ATTR(COP0_MT_ATTR2);
		M_CLR_COP0_LCA_CMP();
#endif /* (!PS5017_EN) */
		// NCS directly read Pln 1, Blk 0, Pg 0 in all CH to detect CE
		// Change LDPC mode to 7 (ID page) to avoid UNC INT
		if (NCS_EN) {
			gubNCSTLCOpenFlow = pCmd->vuc_sqcmd.raw_u8_data.by[61] & BIT0;
			if ((0 == uwBlk) && (0 == uwPage)) {  /* parasoft-suppress BD-PB-CC "uwBlk is referenced from HW reg."*/
				FlaSwitchLDPCHandler(SET_ECC_MODE_7);
			}
#if PS5017_EN
			gubNCSAssignACRR = pCmd->vuc_sqcmd.raw_u8_data.by[62] & 7;
#endif /* PS5017_EN */
		}

		// NCS doesn't need conversion, enable bypass
#if PS5017_EN
		if (NCS_EN) {
			ulCOP0AttributeBackup.ulAll = M_COP0_GET_ATTR(COP0_MT_ATTR2);
			M_CLR_COP0_LCA_CMP();
			if (ubNCSVerifyFlag) {

				//WorkaRound, Clear over errbit threshold INT
				M_FIP_EOT_INTERRUPT_DIS(); /* ECC Over Bits */

				M_COP0_GET_ATTR(COP0_MT_ATTR2) |= SET_ATTR_CONV_BYPASS_EN;
#if (RETRY_INITIAL_READ_EN)
				if (FALSE == gubNeedSendNCSFlag) {
					RetryResetLRUTable();
				}
#endif /*(RETRY_INITIAL_READ_EN)*/
				gubNeedSendNCSFlag = TRUE;
			}
			//Wait COP0 Idle
			while ((TRUE == M_COP0_GET_D2H_PATH_BUSY()) || (FALSE == MRCheckCOP0DecoderIdle()) || (FALSE == MRCheckCOP0SlaveIdle())) {
				FWCop0Waiting();
			}
		}
#else /* PS5017_EN */
		if (NCS_EN) {
			if (ubNCSVerifyFlag) {
				M_COP0_GET_ATTR(COP0_MT_ATTR2) |= SET_ATTR_CONV_BYPASS_EN;
			}
		}
#endif /* PS5017_EN */

		ulBufParameter.A.ulBUF_ADR = pCmd->ulCurrentPhysicalMemoryAddr;
		COP0API_FillCOP0ReadSQUserData0Para(COP0_R_COPYUNIT_READ, &ReadSQParameter);
		ReadSQParameter.UserData0.btSLCMode = ubALU ? TRUE : FALSE;
		ReadSQParameter.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;
		ReadSQParameter.UserData0.TagID = TAG_ID_ALLOCATE;
		ReadSQParameter.UserData0.btReadTieOutMethod = TRUE;
		ReadSQParameter.ulPCA.ulAll = FTLReverseCop0CieInPCA(ulPCA);
		ReadSQParameter.UserData0.btRUTBps = TRUE;
		ReadSQParameter.UserData0.btVBRMPBps = TRUE;
		ReadSQParameter.UserData0.AttrMTTemplate = COP0_MT_TEMP_BUF_ADDR_READ;
		ReadSQParameter.UserData0.L4KNum = 0;
		ReadSQParameter.UserData0.btSerial = FALSE;
		ReadSQParameter.BufVld.ubBufType = BUF_TYPE_A;
		ReadSQParameter.BufVld.pulBufInfoPtr = &ulBufParameter;
		ReadSQParameter.pulFWSetPtr = ulFWSet;
		// Buffer addess with serial setting
#if (PS5017_EN || PS5021_EN)
		ulFWSet[0].ulFWSet = DBUF_GC_BACKUP_COPY_UNIT_IDX;
#else /* (PS5017_EN || PS5021_EN) */
		ulFWSet[0].ulFWSet = GC_BACKUP_COPY_UNIT_OFF;
#endif /* (PS5017_EN || PS5021_EN) */
		ulFWSet[0].ubZInfo = MAX_ZINFO;

		COP0Status = COP0API_SendReadSQ(&ReadSQParameter, &uoCallbackInfo);
		M_FW_ASSERT(ASSERT_VUC_0x0AC9, TRUE == COP0Status.btSendCmdSuccess);
#if PS5017_EN
		while (0 != gpuoCmdTableMgr[COP0_CMD_TABLE_ID][ReadSQParameter.UserData0.TagID].ulData.ulFLHInfo.uwCnt) {
			if (NCS_EN) {
				COP0DelegateCmd();
				FWErrRecorder();
				if (gpRetry->ubCnt) {
					ubNCSHaveEnteredRetryFlag = TRUE;
					Retry_Job_Handling();
				}
			}
			else {
				FWCop0Waiting();
			}
		}
#endif /* PS5017_EN */
		/*
		 * NCS verification
		 * we need re-write data back to NCS by flash VUC.
		 */
		if (NCS_EN) {
			if (ubNCSVerifyFlag) {
#if (PS5017_EN || PS5021_EN)
				// Get old and new MT index, Notice: should get MT_IDX before any CodeBank switch
				U8 ubOriginMTIdx = M_GET_LAST_GLOBAL_TRIGGER_MT_IDX();

				/*
				 * Only need to re-write data when no error case in this step.
				 * Leave failure case to retry job (which includes trapping set).
				 */
				if (!ubNCSHaveEnteredRetryFlag) {
					U8 ubNewMTIdx = FlaGetFreeMTIndex();  // new MT used to send data back to NCS
					// Get ECC count one before trig any MT
					guwDMACntOneValue = M_FIP_GET_ECC_INF(M_GET_LAST_GLOBAL_TRIGGER_QUEUE_IDX() % gFlhEnv.ubChannelExistNum);
					// Dump trapping set setting
					ReadRetryDumpHWSetting(ubLMU, 0xD0, 0, 0, 1);
					// Send data and ECC info to NCS
					FIPVUCSendNCSInfo(ubNewMTIdx, (FlhMT_t *)M_MT_ADDR(ubOriginMTIdx), NCS_WRITE_FRAME_INFO, ulBufParameter.A.ulBUF_ADR, DBUF_GC_BACKUP_COPY_UNIT_IDX/*GC_BACKUP_COPY_UNIT_OFF*/, 0);
					FIPVUCSendNCSInfo(ubNewMTIdx, (FlhMT_t *)M_MT_ADDR(ubOriginMTIdx), NCS_WRITE_ERROR_BIT_INFO, 0, 0, 0);
					FIPVUCSendNCSInfo(ubNewMTIdx, (FlhMT_t *)M_MT_ADDR(ubOriginMTIdx), NCS_WRITE_HW_SETTING_INFO, 0, 0, 0);
					// Return retry MT
					FlaAddFreeMTIndex(ubNewMTIdx);
				}
#else /* (PS5017_EN || PS5021_EN) */
				gubNeedSendNCSFlag = TRUE;

				// Wait until any normal CQ or error CQ appears
				while (M_DB_CHECK_EMPTY(DB_COP0_CQ) && (0 == gpRetry->ubCnt)) {
					FWErrRecorder();
				}
				/*
				 * Only need to re-write data when no error case in this step.
				 * Leave failure case to retry job (which includes trapping set).
				 */
				if (0 == gpRetry->ubCnt) {
					// Get old and new MT index
					U8 ubOriginMTIdx = M_GET_LAST_GLOBAL_TRIGGER_MT_IDX();
					U8 ubNewMTIdx = FlaGetFreeMTIndex();  // new MT used to send data back to NCS
					// Get ECC count one before trig any MT
					guwDMACntOneValue = M_FIP_GET_ECC_INF(M_GET_LAST_GLOBAL_TRIGGER_QUEUE_IDX() % gFlhEnv.ubChannelExistNum);
					// Send data and ECC info to NCS
					FIPVUCSendNCSInfo(ubNewMTIdx, (FlhMT_t *)M_MT_ADDR(ubOriginMTIdx), NCS_WRITE_FRAME_INFO, ulBufParameter.A.ulBUF_ADR, GC_BACKUP_COPY_UNIT_OFF, 0);
					FIPVUCSendNCSInfo(ubNewMTIdx, (FlhMT_t *)M_MT_ADDR(ubOriginMTIdx), NCS_WRITE_ERROR_BIT_INFO, 0, 0, 0);
					// Return retry MT
					FlaAddFreeMTIndex(ubNewMTIdx);
				}
#endif /* (PS5017_EN || PS5021_EN) */
			}
		}

#if (!PS5017_EN)
		while (0 != gpuoCmdTableMgr[COP0_CMD_TABLE_ID][ReadSQParameter.UserData0.TagID].ulData.ulFLHInfo.uwCnt) {
			if (NCS_EN) {
				COP0DelegateCmd();
				FWErrRecorder();
				if (gpRetry->ubCnt) {
					Retry_Job_Handling();
				}
			}
			else {
				FWCop0Waiting();
			}
		}
#endif /* (!PS5017_EN) */

		// Restore cop0 attr back
		M_COP0_GET_ATTR(COP0_MT_ATTR2) = ulCOP0AttributeBackup.ulAll;

		// Change LDPC mode back to 2
		if (NCS_EN) {
			if ((0 == uwBlk) && (0 == uwPage)) { /* parasoft-suppress BD-PB-CC "uwBlk is referenced from HW reg."*/
				FlaSwitchLDPCHandler(gFlhEnv.ubDefaultLDPCMode);
			}
		}
	}
}
#endif	/*(HOST_MODE == SATA)*/
