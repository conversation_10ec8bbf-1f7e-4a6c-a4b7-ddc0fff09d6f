/**************************************************************************/
/*                                                                        */
/*           Copyright (c) 2000-2017 by Phison Electronics Corp.          */
/*                                                                        */
/**************************************************************************/

/**************************************************************************/
/*                                                                        */
/*  HAL UART DEFINITION                                    RELEASE        */
/*                                                                        */
/*    shr_hal_db.h                                       ARM Compiler     */
/*                                                           X.X          */
/*  AUTHOR                                                                */
/*                                                                        */
/*                                                                        */
/*  DESCRIPTION                                                           */
/*                                                                        */
/*    This file contains hal doorbell re-definitions for this system.        */
/*                                                                        */
/*                                                                        */
/*  RELEASE HISTORY                                                       */
/*                                                                        */
/*    DATE              NAME                      DESCRIPTION             */
/*                                                                        */
/*  2017-05-03      Eddie Chiang                 Initial Version 1.0      */
/*                                                                        */
/**************************************************************************/
#ifndef _SHR_HAL_DOORBELL_H_
#define _SHR_HAL_DOORBELL_H_


#include "hal/db/db_reg.h"
//re-define to E11 hal\db\db_reg.h //merge@@@@@

#define DB_CTRL_BASE_ADDRESS       (DOORBELL_REG_ADDRESS)

#define r8_DB                      ((REG8  *) (DB_CTRL_BASE_ADDRESS))
#define r16_DB                     ((REG16 *) (DB_CTRL_BASE_ADDRESS))
#define r32_DB                     ((REG32 *) (DB_CTRL_BASE_ADDRESS))
#define r64_DB                     ((REG64 *) (DB_CTRL_BASE_ADDRESS))


#define DBB_CTRL_SIZE_PER_QUEUE    (DB_CTRL_SIZE_PER_QUEUE)
#define DBW_CTRL_SIZE_PER_QUEUE   (DB_CTRL_SIZE_PER_QUEUE >> 1)
#define DBL_CTRL_SIZE_PER_QUEUE    (DB_CTRL_SIZE_PER_QUEUE >> 2)

//============
#define DBL_WPTR                    ( R32_DB_WPTR) // Write Pointer Register
#define DBL_RPTR                     (R32_DB_RPTR) // Read Pointer Register
#define DBW_WPIU                   (R16_DB_WPIU) // Write Pointer Increase Unit Register
#define DBW_WR_CNT              (R16_DB_WR_CNT) // Writable Count Register
#define DBW_RPIU                    (R16_DB_RPIU) // Read Pointer Increase Unit Register
#define DBW_RD_CNT               (R16_DB_RD_CNT) // Readable Count Register
#define DBB_CTRL                     (R8_DB_CTRL) // Control Register
#define VALID_QUEUE            (BIT0) // Valid Queue
#define RESET_QUEUE            (BIT1) // Reset Queue

#define M_DB_QINFO_W8(DBID, OFFSET)       (r8_DB[(DBID * DBB_CTRL_SIZE_PER_QUEUE) + OFFSET])
#define M_DB_QINFO_W16(DBID, OFFSET)      (r16_DB[(DBID * DBW_CTRL_SIZE_PER_QUEUE) + OFFSET])
#define M_DB_QINFO_W32(DBID, OFFSET)      (r32_DB[(DBID * DBL_CTRL_SIZE_PER_QUEUE) + OFFSET])
//
#define M_DB_QINFO_GET_WPTR(DBID)	    	M_DB_QINFO_W32(DBID, DBL_WPTR)
#define M_DB_QINFO_GET_WR_CNT(DBID)	    	M_DB_QINFO_W16(DBID, DBW_WR_CNT)
#define M_DB_QINFO_GET_RPTR(DBID)	    	       M_DB_QINFO_W32(DBID, DBL_RPTR)
#define M_DB_QINFO_GET_RD_CNT(DBID)	    	M_DB_QINFO_W16(DBID, DBW_RD_CNT)

//#define M_DB_QINFO_START_SQ(DBID, CNT)   	M_DB_QINFO_W16(DBID, DBW_WPIU) = CNT
//#define M_DB_QINFO_INC_CQ(DBID, CNT)     	M_DB_QINFO_W16(DBID, DBW_RPIU) = CNT
#if VS_SIM_EN
#define M_DB_QINFO_START_SQ(DBID, CNT)   	{ M_DB_QINFO_W16(DBID, DBW_WPIU) = CNT;}
#define M_DB_QINFO_INC_CQ(DBID, CNT)     	{ M_DB_QINFO_W16(DBID, DBW_RPIU) = CNT;}
#else
#define M_DB_QINFO_START_SQ(DBID, CNT)   	{ __asm volatile("DSB");M_DB_QINFO_W16(DBID, DBW_WPIU) = CNT;}
#define M_DB_QINFO_INC_CQ(DBID, CNT)     	{ __asm volatile("DSB");M_DB_QINFO_W16(DBID, DBW_RPIU) = CNT;}
#endif
//#define DB_ADD_WPIU(QID, x)        { __asm volatile("DSB"); r16_DB[WPIU_W16 + ((DB_CTRL_SIZE_PER_QUEUE * QID) >> 1)] = x; }
//
// Doorbell ID definition
//

#define DB_BMU_WR_CQ_2                  (DB_BMU_WR_CQ2)
#define DB_APU_COMPL_SQ                  (DB_APU_CMPL_SQ)


#endif  // _SHR_HAL_DOORBELL_H_

