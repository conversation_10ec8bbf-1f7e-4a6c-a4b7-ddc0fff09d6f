#include "hal/cop0/cop0_api.h"
#include "hal/dmac/dmac_api.h"
#include "hal/dmac/dmac_pop_cmd.h"
#include "hal/pic/uart/uart_api.h"
#include "init/fw_init.h"
#include "nvme_api/nvme/shr_hal_nvme.h"
#include "table/vbmap/vbmap_api.h"
#include "host/VUC_handler.h"
#include "vuc/VUC_command.h"
#include "vuc/VUC_DumpTable.h"

#if ((BURNER_MODE_EN || RDT_MODE_EN) && !MST_MODE_EN)
U8 VUCGetEraseCount(void)
{
	U8 ubVTNotFound;
	U8 ubResult = FAIL;
	U16 uwVTChild;

	if (0 == gSystemArea.ubSystemBlockNum) {
		return ubResult;
	}

	if (VUCScanRUT()) {
		return ubResult;
	}

	if (SUCCESS != SysAreaRead4KEntry(SYSAREA_SCAN_RECORD_BUFFER, SPARE_LCA_SYSTEM, gSystemArea.SystemBlock[0], 0, 0, SYSTEM_AREA_HEADER_MODE)) {
		return ubResult;
	}
	else {
		// Setting VBRMP of VT mother
		U8 ubi; // need fix
		Sector_6_FW_RSV_t *pFWSetting = &(((SystemBlock_t *)SYSAREA_SCAN_RECORD_BUFFER)->Sector6_Info);
		for (ubi = 0; ubi < MAX_VT_UNIT_NUM; ubi ++) {
			U16 uwVBIndex;
			uwVBIndex = pFWSetting->VBRMPForVT[ubi].uwVBIndex;
			gpVT->VT.uwUnit[ubi].B.uwUnit = uwVBIndex;
			gpVT->VT.uwUnit[ubi].B.btSingleSLC = TRUE;
			gpuwVBRMP[uwVBIndex].B.ToRUT_Unit = pFWSetting->VBRMPForVT[ubi].uwPBIndex;
			gpuwVBRMP[uwVBIndex].B.btIsSLCMode = (gFlhEnv.ulFlashDefaultType.BitMap.CellType == FLH_SLC) ? FALSE : TRUE;
		}
	}

	///******************************************************************
	///					Scan VT
	///******************************************************************
	/// Need add scan VT -> Load VTChild -> Load InitInfo Blk / Scan TableTarget

	/// Scan VT
	if (FTLScanVT(SCAN_VT_MOTHER, INIT_NOT_CHECK_UNSTABLE_VT)) {
		//no VT found
		return ubResult;
	}

	uwVTChild = gpVT->uwVTChildVB;

	gpuwVBRMP[gpVT->VTChild.uwUnit.B.uwUnit].uwAll  =  uwVTChild;


	ubVTNotFound = FTLScanVTChild();
	if (TRUE == ubVTNotFound) {
		return ubResult;
	}
	gpuwVBRMP[gpVT->InfoUnit.uwUnit.B.uwUnit].uwAll = gpVT->uwInitInfoVB;

	// Load EC
	{
		U32 ulRAMAddrForCopyBuffer[FRAMES_PER_PAGE];
		PCA_t ulFWPCA;
		U32 ulLocalPCA;
		U8 ubLocalLPCRC = 0;
		U8 ubi, ubj;
		U8 ubECPlaneNum = (U8)(DBUF_ERASE_CNT_SIZE / gFlhEnv.uwPageByteCnt);//4K size: (U8)((SIZE_4B * MAX_UNIT) / SIZE_4KB)
		ulLocalPCA = M_GET_VCA_PLANE(gpVT->InfoUnit.uwUnit.B.uwUnit, gpVT->InfoUnit.ulPlaneIndexForEC);
		M_FWPCA_SET(ulFWPCA.ulAll, ulLocalPCA, gPCAInfo.ubMaxZByte, 0, ubLocalLPCRC);

		//Load EC (16K)
		for (ubi = 0; ubi < ubECPlaneNum; ubi++) {
			for (ubj = 0; ubj < gub4kEntrysPerPlane; ubj++) {
				ulRAMAddrForCopyBuffer[ubj] = ((U32)gpulEC + (((ubi << gub4kEntrysPerPlaneLog) + ubj) * SIZE_4KB));
			}
			FTLCOP0ReadN4kEntry_AOM_INIT_2((FTLCOP0ReadN4kEntryMode_t) {
				R_GETLCA_FROM_L4KTABLE, COP0_R_INIT_SCAN_VT_INITINFO, gub4kEntrysPerPlane, NULL
			}, ulFWPCA, ulRAMAddrForCopyBuffer);
			for (ubj = 0; ubj < gub4kEntrysPerPlane; ubj++) {
				if (SPARE_LCA_EC != gulTieoutLCA[ubj]) {
					return ubResult;
				}
			}
			ulFWPCA.ulAll += gub4kEntrysPerPlane;
			if (RaidECCMAPIsParity(RS_MODE_WRITEINITINFO, ((ulFWPCA.ulAll >> gub4kEntrysPerPlaneLog) & gulPlanesPerUnitAlignMask), TRUE)) {
				ulFWPCA.ulAll += gub4kEntrysPerPlane;
			}
		}
		ubResult = PASS;
	}
	return ubResult;
}

void VUC_GetEraseCount(VUC_OPT_HCMD_PTR_t pCmd)
{
	if (TRUE == gFlhEnv.ulFlashDefaultType.BitMap.btNoSupport) {
		pCmd->ubState = CMD_ERROR;
		gVUCVar.ubVucCmdErrStatus = HOST_ERROR_FEATURE;
		return;
	}

	U8 ubVTNotFound;
	U16 uwVTChild;

	if (SCAN_NONE == FTLInitScanSystemArea(0, 0)) {// Scan VT
		M_FW_ASSERT(ASSERT_VUC_0x0AB5, FALSE);
	}

	///******************************************************************
	///					Scan VT
	///******************************************************************
	/// Need add scan VT -> Load VTChild -> Load InitInfo Blk / Scan TableTarget

	/// Scan VT
	if (FTLScanVT(SCAN_VT_MOTHER, INIT_NOT_CHECK_UNSTABLE_VT)) {
		//no VT found
		M_FW_ASSERT(ASSERT_VUC_0x0AB6, FALSE);
	}

	uwVTChild = gpVT->uwVTChildVB;

	gpuwVBRMP[gpVT->VTChild.uwUnit.B.uwUnit].uwAll  =  uwVTChild;

	if (SCAN_VT_MULTI_CHANNEL_EN) { //single channel also can use
		ubVTNotFound = FTLScanVTChild();
	}
	else {
		ubVTNotFound = FTLScanVT(SCAN_VT_CHILD, INIT_NOT_CHECK_UNSTABLE_VT);  /// Scan VT Child and load VT  ///*** still need to add fail flow
	}
	if (TRUE == ubVTNotFound) {
		M_UART(VUC_, "\n No VT Child found");
	}

	gpuwVBRMP[gpVT->InfoUnit.uwUnit.B.uwUnit].uwAll = gpVT->uwInitInfoVB;
	// Load EC
	{
		U32 ulRAMAddrForCopyBuffer[FRAMES_PER_PAGE];
		PCA_t ulDataFWPCA;
		U32 ulDataLocalPCA;
		U8 ubDataLocalLPCRC = 0;
		U8 ubi;
		U8 ubSize4kEntrysForEC = 0;
		U32 ulReadPTR;
		DMACParam_t DMACParam;

		ulDataLocalPCA = M_GET_VCA_PLANE(gpVT->InfoUnit.uwUnit.B.uwUnit, gpVT->InfoUnit.ulPlaneIndexForEC);
		M_FWPCA_SET(ulDataFWPCA.ulAll, ulDataLocalPCA, gPCAInfo.ubMaxZByte, 0, ubDataLocalLPCRC);

		for (ubi = 0; ubi < gub4kEntrysPerPlane; ubi++) {
			ulRAMAddrForCopyBuffer[ubi] = BURNER_HOST_BIN_FILE_BASE + (ubi * FRAME_SIZE);
		}

#if (IM_N48R)
		ubSize4kEntrysForEC = (U8)((SIZE_4B * (MAX_UNIT - COPY_UNIT_UNC_BMP_EC_BASE)) / SIZE_4KB); //N48R borrow EC for UNC Bitmap
#else /*(IM_N48R)*/
		ubSize4kEntrysForEC = (U8)((SIZE_4B * MAX_UNIT) / SIZE_4KB); //Load EC (Total 16K)
#endif /*(IM_N48R)*/

		for (ubi = 0; ubi < ubSize4kEntrysForEC; ++ubi) {
			if (0 == (ubi % gub4kEntrysPerPlane)) {
				FTLCOP0ReadN4kEntry_AOM_INIT_2((FTLCOP0ReadN4kEntryMode_t) {
					R_GETLCA_FROM_L4KTABLE, COP0_R_INIT_SCAN_VT_INITINFO, gub4kEntrysPerPlane, NULL
				}, ulDataFWPCA, &ulRAMAddrForCopyBuffer[ubi]);
				ulDataFWPCA.ulAll += gub4kEntrysPerPlane;
				ulReadPTR = ((ulDataFWPCA.ulAll >> gub4kEntrysPerPlaneLog) & gulPlanesPerUnitAlignMask);
				if (RaidECCMAPIsParity(RS_MODE_WRITEINITINFO, ulReadPTR, TRUE)) {
					ulDataFWPCA.ulAll += gub4kEntrysPerPlane;
				}
			}
			if (SPARE_LCA_EC != gulTieoutLCA[ubi % gub4kEntrysPerPlane]) {
				pCmd->ubState = CMD_ERROR;
				return;
			}
		}

		//**********************************************
		//		CPY EC to BURNER_HOST_BUFFER_BASE
		//**********************************************
		gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
		DMACParam.ulSourceAddr = (U32) BURNER_HOST_BIN_FILE_BASE;
		DMACParam.ulDestAddr = (U32) BURNER_VENDOR_BUF_BASE;
		DMACParam.ul32ByteNum = M_BYTE_TO_32BYTE_ALIGN((FRAME_SIZE * gub4kEntrysPerPlane));
		DMACCopy(DMAC_MODE_NORMAL_QUEUE, &DMACParam, gulDMACDirectWaitDone_Callback, 0);
		while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
			DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
		}

	}
}
#endif /* (BURNER_MODE_EN || RDT_MODE_EN) */
