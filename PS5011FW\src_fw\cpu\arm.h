#ifndef _ARM_API_H
#define _ARM_API_H

#include "common/mem.h"

#define ARM_IRQ		(0)
#define ARM_FIQ		(1)

#define ARM_SVC_MODE		(0x13)
#define ARM_ABORT_MODE		(0x17)
#define ARM_UNDEFINED_MODE	(0x1B)

#define ARM_MODE_MASK		(BIT_MASK(5))

#define ARM_DEBUG_INFO_ERROR_TYPE_OFFSET	(56)
#define ARM_DEBUG_INFO_ERROR_TYPE_ADDRESS	(BTCM1_DEBUG_INFO_ADDRESS + ARM_DEBUG_INFO_ERROR_TYPE_OFFSET)
#define ARM_DEBUG_INFO_ERROR_INFO_SIZE		(16)

#define ARM_DEBUG_INFO_IRQ_INFO_OFFSET		(ARM_DEBUG_INFO_ERROR_TYPE_OFFSET + ARM_DEBUG_INFO_ERROR_INFO_SIZE)
#define ARM_DBEUG_INFO_IRQ_INFO_SIZE		(8)

#define ARM_DEBUG_INFO_FIQ_INFO_OFFSET		(ARM_DEBUG_INFO_IRQ_INFO_OFFSET + ARM_DBEUG_INFO_IRQ_INFO_SIZE)
#define ARM_DEBUG_INFO_FIQ_INFO_SIZE		(8)

#define ARM_DEBUG_INFO_AOM_INFO_OFFSET		(ARM_DEBUG_INFO_FIQ_INFO_OFFSET + ARM_DEBUG_INFO_FIQ_INFO_SIZE)
#define ARM_DEBUG_INFO_AOM_INFO_ADDRESS		(BTCM1_DEBUG_INFO_ADDRESS + ARM_DEBUG_INFO_AOM_INFO_OFFSET)

#endif	/* _ARM_API_H */
