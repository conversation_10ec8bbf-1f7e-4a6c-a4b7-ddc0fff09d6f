#include "retry/retry.h"
#include "retry/retry_api.h"
#include "retry/retry_hb.h"
#include "retry_kioxia_bics4hdr_tlc_S17_neutral_hb.h"
#include "hal/pic/uart/uart_api.h"
#include "retry/retry_version_api.h" // Retry Version
#include "table/initinfo_vt/initinfo_vt.h" // VT
#include "ftl/ftl_api.h" // FW Timer
#include "hal/fip/fpu.h"

#if (PS5017_EN && (FLASH_BICS4TLCHDR == FW_CATEGORY_FLASH) && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER))
/*
 * ----------------------------------------------------------------------------
 *   Definitions
 * ----------------------------------------------------------------------------
 */

/*
 * ----------------------------------------------------------------------------
 *   Enum
 * ----------------------------------------------------------------------------
 */

/*
 * ----------------------------------------------------------------------------
 *   Data Types
 * ----------------------------------------------------------------------------
 */

/*
 * ----------------------------------------------------------------------------
 *   Macros
 * ----------------------------------------------------------------------------
 */

/*
 * ----------------------------------------------------------------------------
 *   Extern Global Variables
 * ----------------------------------------------------------------------------
 */

/*
 * ----------------------------------------------------------------------------
 *   Static Global Variables
 * ----------------------------------------------------------------------------
 */

// M2CMP01-721 BiCS FLASH Gen4 HDR 256Gb cTLC Read Retry with Set Feature Rev1.0.pdf
U8 gubToshibaBICS4HDRSLCHBRetryData256G[HBIT_RETRY_BICS4_HDR_SLC_256G_STEP_NUM * HBIT_RETRY_SLC_FEA_DATA_NUM] = {
	0x00, 0xF8, 0x08, 0xF0, 0x10, 0xE0, 0x30, 0x20, // 0th ~ 7th
};

// M2CMP01-764 BiCS FLASH Gen4 HDR 512Gb cTLC Read Retry with Set Feature Rev1.0
U8 gubToshibaBICS4HDRSLCHBRetryData512G[HBIT_RETRY_BICS4_HDR_SLC_512G_STEP_NUM * HBIT_RETRY_SLC_FEA_DATA_NUM] = {
	0x00, 0xF8, 0x08, 0xF0, 0x10, 0xE0, 0x30, 0x20, // 0th ~ 7th
};

// M2CMP01-721 BiCS FLASH Gen4 HDR 256Gb cTLC Read Retry with Set Feature Rev1.0.pdf
U8 gubToshibaBICS4HDRTLCHBRetryData256G[HBIT_RETRY_BICS4_HDR_TLC_256G_STEP_NUM * HBIT_RETRY_TLC_FEA_DATA_NUM] = {
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 0 for Initial Read
	0x00, 0x02, 0x05, 0x06, 0x03, 0x03, 0x06, // 1st
	0xFC, 0xFD, 0xFE, 0xF9, 0xFF, 0xFE, 0xFC,// 2nd
	0xFB, 0xFB, 0xFB, 0xF6, 0xFE, 0xFB, 0xF9,// 3rd
	0x00, 0x00, 0xFF, 0xFC, 0x03, 0x01, 0xFE,// 4th
	0x04, 0xFD, 0xFF, 0xFC, 0x03, 0xFF, 0xFC,// 5th
	0xF8, 0xFA, 0xF7, 0xF2, 0xFD, 0xF9, 0xF5,// 6th
	0xF6, 0xF9, 0xF5, 0xEF, 0xFB, 0xF9, 0xF4,// 7th
	0xF6, 0xF7, 0xF1, 0xEE, 0xFA, 0xF8, 0xF3,// 8th
	0xF4, 0xF6, 0xF3, 0xEC, 0xF9, 0xF7, 0xF1,// 9th
	0xFA, 0x00, 0x00, 0x00, 0x02, 0x01, 0x00,// 10th
	0xF2, 0x00, 0xFF, 0xFA, 0x00, 0x00, 0xFE,// 11th
	0xF8, 0xFE, 0xFE, 0xFE, 0x00, 0xFF, 0xFE,// 12th
	0xF4, 0x02, 0x01, 0xFC, 0x02, 0x02, 0x00,// 13th
	0xFA, 0xFD, 0xF9, 0xF2, 0xFF, 0xFC, 0xF6,// 14th
	0xFE, 0xFE, 0xF8, 0xF0, 0x00, 0xFC, 0xF4,// 15th
	0xF8, 0xF9, 0xFC, 0xFF, 0xFD, 0xFD, 0xFE,// 16th
	0xFC, 0xFA, 0xFC, 0xFC, 0xFC, 0xFB, 0xFC,// 17th
	0xF0, 0xF3, 0xF4, 0xF5, 0xF6, 0xF5, 0xF5,// 18th
	0xEE, 0xF2, 0xEF, 0xF2, 0xF4, 0xF5, 0xF4,// 19th
	0xEE, 0xF0, 0xF2, 0xF1, 0xF3, 0xF4, 0xF3,// 20th
	0xF1, 0xEF, 0xF0, 0xEF, 0xF2, 0xF3, 0xF1,// 21st
	0xF2, 0xF9, 0xFD, 0x03, 0xFB, 0xFD, 0x00,// 22nd
	0xF0, 0xF7, 0xFB, 0x01, 0xF9, 0xFB, 0xFE,// 23rd
	0xF4, 0xFB, 0xFE, 0xFF, 0xFB, 0xFE, 0x00,// 24th
	0xF2, 0xF7, 0xF6, 0xF5, 0xF8, 0xF8, 0xF6,// 25th
	0xF6, 0xF6, 0xF9, 0xF3, 0xF7, 0xF7, 0xF3,// 26th
	0xFE, 0xF9, 0xF4, 0xFA, 0xFA, 0xFC, 0xFB,// 27th
	0x00, 0xF6, 0xFB, 0xFA, 0xFE, 0xFF, 0x01,// 28th
	0xFA, 0xFC, 0xF4, 0xF0, 0xFB, 0xFA, 0xF8,// 29th
	0x04, 0xF4, 0xFB, 0xF0, 0xF9, 0xF9, 0xF8,// 30th
	0xF4, 0xF1, 0xF7, 0xED, 0xF3, 0xF2, 0xF1,// 31st
	0xF3, 0xEA, 0xEB, 0xE7, 0xF0, 0xEC, 0xEA,// 32nd
	0xF4, 0xEE, 0xF1, 0xEA, 0xF2, 0xEF, 0xEE,// 33rd
	0xFC, 0xF9, 0xF5, 0xEC, 0xFD, 0xF7, 0xF2,// 34th
	0xFA, 0xF8, 0xF2, 0xE8, 0xFD, 0xF6, 0xEF,// 35th
	0xFA, 0xF7, 0xEF, 0xE3, 0xFC, 0xF5, 0xEC,// 36th
	0xF9, 0xF7, 0xED, 0xDF, 0xFB, 0xF3, 0xE8,// 37th
	0xF5, 0xF3, 0xEF, 0xEA, 0xF7, 0xF2, 0xEF,// 38th
	0xF3, 0xF1, 0xED, 0xE6, 0xF5, 0xF1, 0xEC,// 39th
	0xF1, 0xF0, 0xEA, 0xE2, 0xF4, 0xEF, 0xE8, // 40th

};

// M2CMP01-764 BiCS FLASH Gen4 HDR 512Gb cTLC Read Retry with Set Feature Rev1.0.pdf
U8 gubToshibaBICS4HDRTLCHBRetryData512G[HBIT_RETRY_BICS4_HDR_TLC_512G_STEP_NUM * HBIT_RETRY_TLC_FEA_DATA_NUM] = {
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // 0 for Initial Read
	0x05, 0xFF, 0xFA, 0xF9, 0x03, 0xFF, 0xFA, // 1st
	0x02, 0xFC, 0xF5, 0xEE, 0x01, 0xFD, 0xF3, // 2nd
	0x03, 0xFC, 0xF8, 0xF4, 0x02, 0xFE, 0xF7, // 3rd
	0xFD, 0xF8, 0xF7, 0xFC, 0xFC, 0xFB, 0xFA, // 4th
	0xF9, 0xF5, 0xF4, 0xF1, 0xFA, 0xF9, 0xF3, // 5th
	0xF7, 0xFE, 0xF8, 0xF5, 0x01, 0xFF, 0xF7, // 6th
	0xF4, 0x01, 0xFD, 0xFA, 0x02, 0x03, 0xFC, // 7th
	0xEF, 0xF7, 0xF5, 0xF8, 0xFA, 0xFB, 0xF7, // 8th
	0xED, 0xFA, 0xFA, 0xFD, 0xFB, 0xFF, 0xFC, // 9th
	0xF2, 0xFC, 0xF9, 0xF9, 0xFE, 0xFD, 0xF8, // 10th
	0xFA, 0xF5, 0xEF, 0xE9, 0xFB, 0xF5, 0xEE, // 11th
	0xF8, 0xF4, 0xEE, 0xE6, 0xFA, 0xF4, 0xEC, // 12th
	0xF6, 0xF3, 0xEB, 0xE3, 0xF8, 0xF2, 0xE9, // 13th
	0xF8, 0xF2, 0xE9, 0xDE, 0xF7, 0xF0, 0xE6, // 14th
	0xFE, 0xFA, 0xF5, 0xF0, 0xFF, 0xFE, 0xF5, // 15th
	0xFC, 0xF9, 0xF3, 0xED, 0xFE, 0xFB, 0xF2, // 16th
	0xFA, 0xF8, 0xF1, 0xEA, 0xFD, 0xF9, 0xEF, // 17th
	0xFE, 0xFA, 0xF0, 0xE8, 0xFE, 0xF6, 0xED, // 18th
	0xFC, 0xF8, 0xED, 0xE4, 0xFC, 0xF3, 0xEA, // 19th
	0xFA, 0xF7, 0xEB, 0xE0, 0xFA, 0xF1, 0xE8, // 20th
	0xF4, 0xF6, 0xE9, 0xDC, 0xF9, 0xEF, 0xE6, // 21th
	0xF2, 0xEF, 0xEC, 0xEC, 0xF4, 0xF1, 0xEE, // 22th
	0xF0, 0xEE, 0xEB, 0xE9, 0xF3, 0xF0, 0xEC, // 23th
	0xEE, 0xED, 0xE8, 0xE6, 0xF1, 0xEE, 0xE9, // 24th
	0x07, 0x00, 0xF8, 0xF6, 0x04, 0x01, 0xFC, // 25th
	0xF6, 0xF8, 0xF2, 0xF4, 0x00, 0xFA, 0xF1, // 26th
	0xF5, 0xF7, 0xF5, 0xEE, 0xFC, 0xF7, 0xF1, // 27th
	0xFB, 0xF2, 0xFB, 0xED, 0xF9, 0xF6, 0xF0, // 28th
	0xF4, 0xF1, 0xF0, 0xE8, 0xF7, 0xF5, 0xED, // 29th
	0x00, 0x02, 0x04, 0x01, 0x00, 0x03, 0x02, // 30th
	0x0A, 0xFE, 0xFC, 0xFA, 0xFE, 0xFC, 0xFA, // 31th
	0x0D, 0x01, 0xFB, 0xF9, 0x00, 0xFD, 0xFA, // 32th
	0x0F, 0x01, 0xFD, 0xFA, 0x01, 0x00, 0xFB, // 33th
};

// FRL_TSB_BiCSx_RRT_Summary_20200907.xlsx
// M2CMP01-721 BiCS FLASH Gen4 HDR 256Gb cTLC Read Retry with Set Feature Rev1.0.pdf
U8 gubToshibaBICS4HDRScratchTable256G[HBIT_RETRY_BICS4_HDR_256G_SCRATCH_TABLE_NUM * HBIT_RETRY_TLC_FEA_DATA_NUM] = {
	0xFE, 0xFE, 0xF8, 0xF9, 0xFF, 0xFD, 0xFA, // 1st
	0x00, 0xFC, 0xF8, 0xF2, 0x00, 0xF9, 0xF4, // 2nd
	0xFC, 0xFC, 0xF8, 0xF2, 0x00, 0xF9, 0xF4 // 3rd
};
// FRL_TSB_BiCSx_RRT_Summary_20200907.xlsx
// M2CMP01-764 BiCS FLASH Gen4 HDR 512Gb cTLC Read Retry with Set Feature Rev1.0.pdf
U8 gubToshibaBICS4HDRScratchTable512G[HBIT_RETRY_BICS4_HDR_512G_SCRATCH_TABLE_NUM * HBIT_RETRY_TLC_FEA_DATA_NUM] = {
	0x00, 0xFC, 0xF9, 0xF2, 0xFC, 0xF9, 0xF4, // 1st
	0xF4, 0x00, 0xFC, 0xF8, 0xFC, 0xFC, 0xFC, // 2nd
};

U8 gubSlcHbitRetryData[HB_RETRY_MAX_SLC_STEP_NUM * HBIT_RETRY_SLC_FEA_DATA_NUM] = {
	0x00, 0xF8, 0x08, 0xF0, 0x10, 0xE0, 0x30, 0x20, // 0th ~ 7th
};
/*
 * ----------------------------------------------------------------------------
 *   Private Prototypes
 * ----------------------------------------------------------------------------
 */


/*
 * ----------------------------------------------------------------------------
 *   Private
 * ----------------------------------------------------------------------------
 */

INLINE void HBRetryInitRegisterRetryTable(void)
{
	gpHBParameterArray[FPL_ADDR_TLC_RULE].pubRetryTable = (U8 *)(&gubHbitRetryData);
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubTotalRetryCnt = HBIT_RETRY_BICS4_HDR_TLC_256G_STEP_NUM;//default Retry table
	gpHBParameterArray[FPL_ADDR_SLC_RULE].pubRetryTable = (U8 *)(&gubSlcHbitRetryData);
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubTotalRetryCnt = HBIT_RETRY_BICS4_HDR_SLC_256G_STEP_NUM;//default Retry table
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubCurrentStep = HB_READ_START_STEP;
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubLastSuccessIndex = 0;
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubRetryParameterSize = HBIT_RETRY_TLC_FEA_DATA_NUM;
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubParameterNumPerFPU = PARAMETER_NUM_PER_FPU;
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubCurrentStep = HB_READ_START_STEP;
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubLastSuccessIndex = 0;
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubRetryParameterSize = HBIT_RETRY_SLC_FEA_DATA_NUM;
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubParameterNumPerFPU = PARAMETER_NUM_PER_FPU;
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubScratchTableIdx = INVALID_RETRY_TABLE_INDEX;
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubScratchTableIdx = INVALID_RETRY_TABLE_INDEX;
}

INLINE void HBRetryInitAdjustVthFPU(void)
{
	// Register get feature fpu
	gFpuEntryList.fpu_get_feature[0] = FPU_CMD(0xD4); // Get feature cmd
	gFpuEntryList.fpu_get_feature[3] = FPU_END;

	// Register read and compare feature data fpu
	gFpuEntryList.fpu_read_and_compare_feature_data[0] = FPU_CMD(0x00); // Switch to read mode
	gFpuEntryList.fpu_read_and_compare_feature_data[10] = FPU_END;
}

/*
 * ------------------------------------
 * 		Over Wirte Weak Function
 * ------------------------------------
 */
void HBRetryInitParameter(void)
{
	M_FW_ASSERT(ASSERT_HARDBIT_RETRY_0x0D81, (ID_TOSHIBA == gpOtherInfo->ubMakerCode) && (RETRY_TOSHIBA_FLASH_PROCESS_BICS4_HDR_256G == gpOtherInfo->ubProcess));
	HBRetryInitRegisterRetryTable();
	HBRetryInitAdjustVthFPU();
#if ((BURNER_MODE_EN || PS5017_EN))
	HBRetryInitTable();
#endif/* ((BURNER_MODE_EN || PS5017_EN)) */
}

void HBRetryInitTable(void)
{
	U8 ubHBRetryDataStepNum = HBIT_RETRY_BICS4_HDR_TLC_256G_STEP_NUM;//default Retry table step num
	U8 ubSLCHBRetryDataStepNum = HBIT_RETRY_BICS4_HDR_SLC_256G_STEP_NUM;//default SLC Retry table step num

	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubScratchTableIdx = INVALID_RETRY_TABLE_INDEX;
	// NAND_Type: Normal
	if (RETRY_TYPE_NORMAL == gpRetry->RetryVersion.Retry.Idx.ubNandType) {
		if (RETRY_DESIGNRULE_BICS4_HDR == gpRetry->RetryVersion.Retry.Idx.ubDesignRule) {
			// Die: 256Gbit
			if (RETRY_DIE_256G == gpRetry->RetryVersion.Retry.Idx.ubMonoDie) {
				M_RETRY_HB_COPY_READ_RETRY_TABLE((&gubHbitRetryData), (&gubToshibaBICS4HDRTLCHBRetryData256G), (HBIT_RETRY_BICS4_HDR_TLC_256G_STEP_NUM * HBIT_RETRY_TLC_FEA_DATA_NUM));
				ubHBRetryDataStepNum = HBIT_RETRY_BICS4_HDR_TLC_256G_STEP_NUM;
				if (RETRY_READ_HB_SCRATCH_TABLE_EN) {
					gpHBParameterArray[FPL_ADDR_TLC_RULE].ubScratchTableIdx = HBIT_RETRY_BICS4_HDR_TLC_256G_STEP_NUM;
					memcpy((void *)(&gubHbitRetryData[HBIT_RETRY_BICS4_HDR_TLC_256G_STEP_NUM * HBIT_RETRY_TLC_FEA_DATA_NUM]), (void *)(&gubToshibaBICS4HDRScratchTable256G), HBIT_RETRY_BICS4_HDR_256G_SCRATCH_TABLE_NUM * HBIT_RETRY_TLC_FEA_DATA_NUM);
					ubHBRetryDataStepNum += HBIT_RETRY_BICS4_HDR_256G_SCRATCH_TABLE_NUM;
				}
				M_RETRY_HB_COPY_READ_RETRY_TABLE((&gubSlcHbitRetryData), (&gubToshibaBICS4HDRSLCHBRetryData256G), (HBIT_RETRY_BICS4_HDR_SLC_256G_STEP_NUM * HBIT_RETRY_SLC_FEA_DATA_NUM));
				ubSLCHBRetryDataStepNum = HBIT_RETRY_BICS4_HDR_SLC_256G_STEP_NUM;
			}
			// Die: 512Gbit
			else if (RETRY_DIE_512G == gpRetry->RetryVersion.Retry.Idx.ubMonoDie) {
				M_RETRY_HB_COPY_READ_RETRY_TABLE((&gubHbitRetryData), (&gubToshibaBICS4HDRTLCHBRetryData512G), (HBIT_RETRY_BICS4_HDR_TLC_512G_STEP_NUM * HBIT_RETRY_TLC_FEA_DATA_NUM));
				ubHBRetryDataStepNum = HBIT_RETRY_BICS4_HDR_TLC_512G_STEP_NUM;
				if (RETRY_READ_HB_SCRATCH_TABLE_EN) {
					gpHBParameterArray[FPL_ADDR_TLC_RULE].ubScratchTableIdx = HBIT_RETRY_BICS4_HDR_TLC_512G_STEP_NUM;
					memcpy((void *)(&gubHbitRetryData[HBIT_RETRY_BICS4_HDR_TLC_512G_STEP_NUM * HBIT_RETRY_TLC_FEA_DATA_NUM]), (void *)(&gubToshibaBICS4HDRScratchTable512G), HBIT_RETRY_BICS4_HDR_512G_SCRATCH_TABLE_NUM * HBIT_RETRY_TLC_FEA_DATA_NUM);
					ubHBRetryDataStepNum += HBIT_RETRY_BICS4_HDR_512G_SCRATCH_TABLE_NUM;
				}
				M_RETRY_HB_COPY_READ_RETRY_TABLE((&gubSlcHbitRetryData), (&gubToshibaBICS4HDRSLCHBRetryData512G), (HBIT_RETRY_BICS4_HDR_SLC_512G_STEP_NUM * HBIT_RETRY_SLC_FEA_DATA_NUM));
				ubSLCHBRetryDataStepNum = HBIT_RETRY_BICS4_HDR_SLC_512G_STEP_NUM;
			}
		}
	}

	gpHBParameterArray[FPL_ADDR_TLC_RULE].pubRetryTable = (U8 *)(&gubHbitRetryData);
	gpHBParameterArray[FPL_ADDR_TLC_RULE].ubTotalRetryCnt = ubHBRetryDataStepNum;
	gpHBParameterArray[FPL_ADDR_SLC_RULE].pubRetryTable = (U8 *)(&gubSlcHbitRetryData);
	gpHBParameterArray[FPL_ADDR_SLC_RULE].ubTotalRetryCnt = ubSLCHBRetryDataStepNum;
}
#endif /* (PS5017_EN && (FLASH_BICS4TLCHDR == FW_CATEGORY_FLASH) && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER)) */

