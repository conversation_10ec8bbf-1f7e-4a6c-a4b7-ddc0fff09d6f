#include "hal/bmu/bmu_api.h"

#include "hal/db/db_api.h"
#include "hal/bmu/bmu_pop_cmd.h"
#if VS_SIM_EN
#include "ip/Bmu/Bmu.h"
#endif /*VS_SIM_EN*/
#include "debug/debug.h"
#include "db_mgr/fw_tagid.h"
#include "db_mgr/fw_cmd_table.h"
#include "common/fw_common.h"
#include "fw_vardef.h"
#include "buffer/buffer.h"

#define M_CLEAR_BMU_QBODY(Q_PTR) do{\
									((BMUCmdSQ *)(Q_PTR))->All.uoResult_Lo = 0;\
									((BMUCmdSQ *)(Q_PTR))->All.uoResult_Hi = 0;\
								 }while(0)
U8 gubBMUCallBackDone = FALSE;
U8 gubBMUMinLowerLimitRLB = 0;
/***************************************************************************
	BMU CMD API: Allocate (OP Code = 1)
	reference HW api: ubBmuCmdApiAllocate
***************************************************************************/
U8 BMUAPICmdAllocate(U8 btNoCQ, U8 ubTimeout, U8 ubLBID, U8 ubSize, U32 ulLCA, CTAG_t CTAG, U8 ubStreamID, U8 btOption, U8 btFUA, U8 btAF, U32 ulCallBackAddr, U16 uwCallBackData)
{
	U32 *pBMUSQCmdPTR = (U32 *)M_DB_GET_QBODY_PTR( DB_BMU_BLK_SQ, gDBQueueCnt.B.uwBMUBlockingSQCnt); //Get WPTR (FW maintain)
	cmd_table_t *puoCmdTable = NULL;
	U8 ubTagID;

	if (M_DB_CHECK_FULL(DB_BMU_BLK_SQ)) {
		return BMU_CMD_STATUS_FAIL;
	}
	ubTagID = (U8)FTLPopTagPool(BMU_TAG_POOL_ID);

	*pBMUSQCmdPTR = ((((U32)BMU_ALLOCATE_CMD & BMU_CMD_ALLOCATE_OPCODE_MASK) << BMU_CMD_ALLOCATE_OPCODE_SHIFT)
			| (((U32)BMU_BLK_CQ_ID & BMU_CMD_ALLOCATE_TARGET_ID_MASK) << BMU_CMD_ALLOCATE_TARGET_ID_SHIFT)
			| (((U32)btOption & BMU_CMD_ALLOCATE_OPTION_MASK) << BMU_CMD_ALLOCATE_OPTION_SHIFT)
			| (((U32)btAF & BMU_CMD_ALLOCATE_AF_MASK) << BMU_CMD_ALLOCATE_AF_SHIFT)
			| (((U32)ubTimeout & BMU_CMD_ALLOCATE_TIMEOUT_MASK) << BMU_CMD_ALLOCATE_TIMEOUT_SHIFT)
			| (((U32)btFUA & BMU_CMD_ALLOCATE_FUA_MASK) << BMU_CMD_ALLOCATE_FUA_SHIFT)
			| (((U32)btNoCQ & BMU_CMD_ALLOCATE_NOCQ_MASK) << BMU_CMD_ALLOCATE_NOCQ_SHIFT)
			| (((U32)ubTagID & BMU_CMD_ALLOCATE_TAG_ID_MASK) << BMU_CMD_ALLOCATE_TAG_ID_SHIFT));
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = ((((U32)ubLBID & BMU_CMD_ALLOCATE_LB_ID_MASK) << BMU_CMD_ALLOCATE_LB_ID_SHIFT)
			| (((U32)CTAG & BMU_CMD_ALLOCATE_CTAG_MASK) << BMU_CMD_ALLOCATE_CTAG_SHIFT));
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = ((U32)ulLCA << BMU_CMD_ALLOCATE_LCA_SHIFT);
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = ((((U32)ubSize & BMU_CMD_ALLOCATE_SIZE_MASK) << BMU_CMD_ALLOCATE_SIZE_SHIFT)
#if PS5021_EN
			| (((U32)ubStreamID & BMU_CMD_ALLOCATE_STREAM_ID_MASK) << BMU_CMD_ALLOCATE_STREAM_ID_SHIFT)
			| ((((U32)CTAG >> BMU_CTAG_BIT_EXTEND_START) & BMU_CMD_ALLOCATE_CTAG2_MASK) << BMU_CMD_ALLOCATE_CTAG2_SHIFT));
#else /* PS5021_EN */
			| (((U32)ubStreamID & BMU_CMD_ALLOCATE_STREAM_ID_MASK) << BMU_CMD_ALLOCATE_STREAM_ID_SHIFT));
#endif /* PS5021_EN */

#if VS_SIM_EN
	pBMUSQCmdPTR->ubTargetID = BMU_BLK_ID;
	while (BMU_CMD_STATUS_SUCCESS != ubBmuCmdApiAllocate((U8)pBMUSQCmdPTR->ubTargetID, (U16)pBMUSQCmdPTR->ubTagID, (U8)pBMUSQCmdPTR->ubCTag, (U8)pBMUSQCmdPTR->ubTimeout, pBMUSQCmdPTR->ulLCA, (U8)pBMUSQCmdPTR->ubLBID, 0,
			(U8)pBMUSQCmdPTR->btAF, (U8)pBMUSQCmdPTR->ubSize, (U8)pBMUSQCmdPTR->btOption, (U8)pBMUSQCmdPTR->btFUA, (U16)pBMUSQCmdPTR->ubStreamID, NULL));
#endif /*VS_SIM_EN*/
	M_DB_TRIGGER_WRITE_CNT(DB_BMU_BLK_SQ, 1);
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_BLK_SQ, gDBQueueCnt.QueueCnt[(DB_BMU_BLK_SQ)] ) == M_DB_GET_WPTR((DB_BMU_BLK_SQ)));
	puoCmdTable = &gpuoCmdTableMgr[BMU_CMD_TABLE_ID][ubTagID];
	puoCmdTable->ulCmdFuncPTR = ulCallBackAddr;
	puoCmdTable->ulData.uwPrivate = uwCallBackData;

	return BMU_CMD_STATUS_SUCCESS;
}


/***************************************************************************
	BMU CMD API: ReAllocate (OP Code = 2)
	reference HW api: ubBmuCmdApiReallocate
***************************************************************************/
U8 BMUAPICmdReAllocate(U8 btNoCQ, U8 ubTimeout, U8 ubSrcLBID, U16 uwSrcLBOffset, U8 ubTargetLBID, U16 uwTargetLBOffset, U8 ubSize, CTAG_t CTAG, U8 ubStreamID, U8 btOption, U8 btROP, U8 btLOP, BMUCmdResult_t *pBMUCmdResult)
{
	U16 uwCQRDCNT;
	U32 ulCQWPTR;

	U32 *pBMUSQCmdPTR = (U32 *)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_SQ, gDBQueueCnt.B.uwBMUNonBlockingSQCnt); //Get WPTR (FW maintain)
#if VS_SIM_EN
	BmuCmdStatus_t *pBMUCQPTR;
#else /*VS_SIM_EN*/
	BMUCmdResult_t *pBMUCQPTR = NULL;
#endif /*VS_SIM_EN*/

	if (M_DB_CHECK_FULL(DB_BMU_NOBLK_SQ)) {
		return BMU_CMD_STATUS_FAIL;
	}

	*pBMUSQCmdPTR = ((((U32)BMU_REALLOCATE_CMD & BMU_CMD_REALLOCATE_OPCODE_MASK) << BMU_CMD_REALLOCATE_OPCODE_SHIFT)
			| (((U32)BMU_NOBLK_CQ_ID & BMU_CMD_REALLOCATE_TARGET_ID_MASK) << BMU_CMD_REALLOCATE_TARGET_ID_SHIFT)
			| (((U32)btOption & BMU_CMD_REALLOCATE_OPTION_MASK) << BMU_CMD_REALLOCATE_OPTION_SHIFT)
			| (((U32)btROP & BMU_CMD_REALLOCATE_ROP_MASK) << BMU_CMD_REALLOCATE_ROP_SHIFT)
			| (((U32)btLOP & BMU_CMD_REALLOCATE_LOP_MASK) << BMU_CMD_REALLOCATE_LOP_SHIFT)
			| (((U32)ubTimeout & BMU_CMD_REALLOCATE_TIMEOUT_MASK) << BMU_CMD_REALLOCATE_TIMEOUT_SHIFT)
			| (((U32)btNoCQ & BMU_CMD_REALLOCATE_NOCQ_MASK) << BMU_CMD_REALLOCATE_NOCQ_SHIFT)
			| (((U32)DEFAULT_TAG_ID & BMU_CMD_REALLOCATE_TAG_ID_MASK) << BMU_CMD_REALLOCATE_TAG_ID_SHIFT));
	// btOption TRUE: update CTAG of PBNA
	// btROP TRUE: update stream ID of PBNA
	// btLOP TRUE: target LBID and offset need have been reserved, FALSE: reallocate to the tail in target LBID, and don't care uwTargetLBOffset
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = ((((U32)uwSrcLBOffset & BMU_CMD_REALLOCATE_LB_OFFSET_MASK) << BMU_CMD_REALLOCATE_LB_OFFSET_SHIFT)
			| (((U32)ubSrcLBID & BMU_CMD_REALLOCATE_LB_ID_MASK) << BMU_CMD_REALLOCATE_LB_ID_SHIFT)
			| (((U32)CTAG & BMU_CMD_REALLOCATE_CTAG_MASK) << BMU_CMD_REALLOCATE_CTAG_SHIFT));
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = ((((U32)uwTargetLBOffset & BMU_CMD_REALLOCATE_TARGET_LB_OFFSET_MASK) << BMU_CMD_REALLOCATE_TARGET_LB_OFFSET_SHIFT)
			| (((U32)ubTargetLBID & BMU_CMD_REALLOCATE_TARGET_LB_ID_MASK) << BMU_CMD_REALLOCATE_TARGET_LB_ID_SHIFT));
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = ((((U32)ubSize & BMU_CMD_REALLOCATE_SIZE_MASK) << BMU_CMD_REALLOCATE_SIZE_SHIFT)
#if PS5021_EN
			| (((U32)ubStreamID & BMU_CMD_REALLOCATE_STREAM_ID_MASK) << BMU_CMD_REALLOCATE_STREAM_ID_SHIFT)
			| ((((U32)CTAG >> BMU_CTAG_BIT_EXTEND_START) & BMU_CMD_REALLOCATE_CTAG2_MASK) << BMU_CMD_REALLOCATE_CTAG2_SHIFT));
#else /* PS5021_EN */
			| (((U32)ubStreamID & BMU_CMD_REALLOCATE_STREAM_ID_MASK) << BMU_CMD_REALLOCATE_STREAM_ID_SHIFT));
#endif /* PS5021_EN */

	uwCQRDCNT = M_DB_GET_RD_CNT(DB_BMU_NOBLK_CQ);
#if USE_FW_DB
	ulCQWPTR = (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_CQ, gDBQueueCnt.B.uwBMUCQ2Cnt); //Get WPTR (FW maintain)
#else
	ulCQWPTR = M_DB_GET_WPTR(DB_BMU_NOBLK_CQ);
#endif
#if VS_SIM_EN
	pBMUSQCmdPTR->ubTargetID = BMU_BLK_ID;
	while (BMU_CMD_STATUS_SUCCESS != ubBmuCmdApiReallocate(
			(U8)pBMUSQCmdPTR->ubTargetID,	/* ubQueueID */
			(U16)pBMUSQCmdPTR->ubTagID,		/* uwTag */
			(!pBMUSQCmdPTR->btNoCQ),		/* ubIsReturnCQ */
			(U8)pBMUSQCmdPTR->ubTimeout,	/* ubTimeoutValue */
			(U8)pBMUSQCmdPTR->ubSize,		/* ubSize */
			(U8)pBMUSQCmdPTR->ubTargetLBID,	/* ubTargetLogicalBufferID */
			(U16)pBMUSQCmdPTR->uwLBOffset,	/* uwSourceLogicalBufferOffset */
			(U8)pBMUSQCmdPTR->ubLBID,		/* ubSourceLogicalBufferID */
			(U8)pBMUSQCmdPTR->ubCTag,		/* ubCTAG */
			(U8)pBMUSQCmdPTR->btOption,		/* ubOption */
			NULL							/* pBmuCmdStatus */
		));
#endif
	M_DB_TRIGGER_WRITE_CNT(DB_BMU_NOBLK_SQ, 1);
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_SQ, gDBQueueCnt.QueueCnt[(DB_BMU_NOBLK_SQ)] ) == M_DB_GET_WPTR((DB_BMU_NOBLK_SQ)));
	while (M_DB_GET_RD_CNT(DB_BMU_NOBLK_CQ) < (uwCQRDCNT + 1));

#if VS_SIM_EN
	memset(pBMUCmdResult, 0, sizeof(BMUCmdResult_t));
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
	//not implement
	/*pBMUCQPTR = (BmuCmdStatus_t *)ulCQWPTR;
	pBMUCQPTR->Free_Sts_t.ubOpcode = BMU_REALLOCATE_CMD;
	pBMUCmdResult->BMUReAllocateRst.ubOpcode	= pBMUCQPTR->Free_Sts_t.ubOpcode;
	pBMUCmdResult->BMUReAllocateRst.ubResult	= pBMUCQPTR->Free_Sts_t.ubStatus;
	pBMUCmdResult->BMUReAllocateRst.btOption	= pBMUCQPTR->Free_Sts_t.ubFullCheckOption;
	pBMUCmdResult->BMUReAllocateRst.ubDoneCount = pBMUCQPTR->Free_Sts_t.ubSize;
	pBMUCmdResult->BMUReAllocateRst.ubTagID		= (U8)pBMUCQPTR->Free_Sts_t.uwTagID;	//size not the same
	pBMUCmdResult->BMUReAllocateRst.uwPBAddress = pBMUCQPTR->Free_Sts_t.LBNA.B.uwPoolOffset;
	pBMUCmdResult->BMUReAllocateRst.ubLBID		= pBMUCQPTR->Free_Sts_t.ubLogicalBufferID & 0x7;
	pBMUCmdResult->BMUReAllocateRst.uwLBOffset	= pBMUCQPTR->Free_Sts_t.uwLogicalBufferOffset & 0x3FF;
	pBMUCmdResult->BMUReAllocateRst.ubTargetID	= pBMUCQPTR->Free_Sts_t.ubSrcTagID;
	*/
#else /*VS_SIM_EN*/
	pBMUCQPTR = (BMUCmdResult_t *)ulCQWPTR;
	pBMUCmdResult->All = pBMUCQPTR->All;
#endif /*VS_SIM_EN*/

	if (pBMUCmdResult->BMUFreeRst.ubOpcode == BMU_REALLOCATE_CMD) {

#if VS_SIM_EN
		vQueuePop (CMD_CQ_GET_QUEUE_PTR (pBMUCmdResult->BMURst.ubTargetID), pBMUCQPTR);
#endif /*VS_SIM_EN*/
		M_DB_TRIGGER_READ_CNT(DB_BMU_NOBLK_CQ, 1);
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_CQ, gDBQueueCnt.QueueCnt[(DB_BMU_NOBLK_CQ)] ) == M_DB_GET_RPTR((DB_BMU_NOBLK_CQ)));
	}
	else {
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);//DB_BMU_NOBLK_CQ setting depth = 1
	}

	//CQ Is occupied by blocking
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, 0 == M_DB_CHECK_FULL(DB_BMU_NOBLK_CQ));

	return BMU_CMD_STATUS_SUCCESS;

}

/***************************************************************************
	BMU CMD API: Free (OP Code = 3)
	reference HW api: ubBmuCmdApiFreePBLink and ubBmuCmdApiFree
***************************************************************************/
U8 BMUAPICmdFree(U8 btNoCQ, U8 ubLBID, U16 uwLBOffset, U8 ubSize, U8 btOption, U8 btFree, U8 btFAR, BMUCmdResult_t *pBMUCmdResult)
{
#if(!BURNER_MODE_EN)
	U16 uwCQRDCNT;
	U32 ulCQWPTR;
#if VS_SIM_EN
	BmuCmdStatus_t *pBMUCQPTR;
#else /*VS_SIM_EN*/
	BMUCmdResult_t *pBMUCQPTR = NULL;
#endif /*VS_SIM_EN*/

	U32 *pBMUSQCmdPTR = (U32 *)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_SQ, gDBQueueCnt.B.uwBMUNonBlockingSQCnt);
	if (M_DB_CHECK_FULL(DB_BMU_NOBLK_CQ) || M_DB_CHECK_FULL(DB_BMU_NOBLK_SQ)) {
		return BMU_CMD_STATUS_FAIL;
	}

	*pBMUSQCmdPTR = ((((U32)BMU_FREE_CMD & BMU_CMD_FREE_OPCODE_MASK) << BMU_CMD_FREE_OPCODE_SHIFT)
			| (((U32)BMU_NOBLK_CQ_ID & BMU_CMD_FREE_TARGET_ID_MASK) << BMU_CMD_FREE_TARGET_ID_SHIFT)
			| (((U32)btOption & BMU_CMD_FREE_OPTION_MASK) << BMU_CMD_FREE_OPTION_SHIFT)
			| (((U32)btFAR & BMU_CMD_FREE_FAR_MASK) << BMU_CMD_FREE_FAR_SHIFT)
			| (((U32)btFree & BMU_CMD_FREE_FREE_OP_MASK) << BMU_CMD_FREE_FREE_OP_SHIFT)
			| (((U32)btNoCQ & BMU_CMD_FREE_NOCQ_MASK) << BMU_CMD_FREE_NOCQ_SHIFT)
			| (((U32)DEFAULT_TAG_ID & BMU_CMD_FREE_TAG_ID_MASK) << BMU_CMD_FREE_TAG_ID_SHIFT));
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = ((((U32)uwLBOffset & BMU_CMD_FREE_LB_OFFSET_MASK) << BMU_CMD_FREE_LB_OFFSET_SHIFT)
			| (((U32)ubLBID & BMU_CMD_FREE_LB_ID_MASK) << BMU_CMD_FREE_LB_ID_SHIFT));
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = 0;
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = (((U32)ubSize & BMU_CMD_FREE_SIZE_MASK) << BMU_CMD_FREE_SIZE_SHIFT);

	uwCQRDCNT = M_DB_GET_RD_CNT(DB_BMU_NOBLK_CQ);
#if USE_FW_DB
	ulCQWPTR = (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_CQ, gDBQueueCnt.B.uwBMUCQ2Cnt); //Get WPTR (FW maintain)
#else
	ulCQWPTR = M_DB_GET_WPTR(DB_BMU_NOBLK_CQ);
#endif
#if VS_SIM_EN
	pBMUSQCmdPTR->ubTargetID = BMU_NOBLK_ID;
	if (btFAR == BMU_FREE_FAR_EN) {
		while (BMU_CMD_STATUS_SUCCESS != ubBmuCmdApiFreePBLink ((U8)pBMUSQCmdPTR->ubTargetID, (U16)pBMUSQCmdPTR->ubTagID, (U8)(!pBMUSQCmdPTR->btNoCQ), (U8)pBMUSQCmdPTR->btOption, (U8)pBMUSQCmdPTR->ubSize, (U16)pBMUSQCmdPTR->uwLBOffset, (U8)pBMUSQCmdPTR->ubLBID, NULL));
	}
	else {
		while (BMU_CMD_STATUS_SUCCESS != ubBmuCmdApiFree((U8)pBMUSQCmdPTR->ubTargetID, (U16)pBMUSQCmdPTR->ubTagID, (U8)(!pBMUSQCmdPTR->btNoCQ), (U8)pBMUSQCmdPTR->btOption, (U8)pBMUSQCmdPTR->ubSize, (U16)pBMUSQCmdPTR->uwLBOffset, (U8)pBMUSQCmdPTR->ubLBID, NULL));
	}
#endif /*VS_SIM_EN*/
	M_DB_TRIGGER_WRITE_CNT(DB_BMU_NOBLK_SQ, 1);
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_SQ, gDBQueueCnt.QueueCnt[(DB_BMU_NOBLK_SQ)] ) == M_DB_GET_WPTR((DB_BMU_NOBLK_SQ)));
	while (M_DB_GET_RD_CNT(DB_BMU_NOBLK_CQ) < (uwCQRDCNT + 1));

#if VS_SIM_EN
	memset(pBMUCmdResult, 0, sizeof(BMUCmdResult_t));

	pBMUCQPTR = (BmuCmdStatus_t *)ulCQWPTR;
	pBMUCQPTR->Free_Sts_t.ubOpcode = BMU_FREE_CMD;
	pBMUCmdResult->BMUFreeRst.ubOpcode = pBMUCQPTR->Free_Sts_t.ubOpcode;
	pBMUCmdResult->BMUFreeRst.ubResult = pBMUCQPTR->Free_Sts_t.ubStatus;
	pBMUCmdResult->BMUFreeRst.btOption = pBMUCQPTR->Free_Sts_t.ubFullCheckOption;
	pBMUCmdResult->BMUFreeRst.ubDoneCount = pBMUCQPTR->Free_Sts_t.ubSize;
	pBMUCmdResult->BMUFreeRst.ubTagID = (U8)pBMUCQPTR->Free_Sts_t.uwTagID;	//size not the same
	pBMUCmdResult->BMUFreeRst.uwPBAddress = pBMUCQPTR->Free_Sts_t.LBNA.B.uwPoolOffset;
	pBMUCmdResult->BMUFreeRst.ubLBID = pBMUCQPTR->Free_Sts_t.ubLogicalBufferID & 0x7;
	pBMUCmdResult->BMUFreeRst.uwLBOffset = pBMUCQPTR->Free_Sts_t.uwLogicalBufferOffset & 0x3FF;
	pBMUCmdResult->BMUFreeRst.ubTargetID = pBMUCQPTR->Free_Sts_t.ubSrcTagID;
#else /*VS_SIM_EN*/
	pBMUCQPTR = (BMUCmdResult_t *)ulCQWPTR;
	pBMUCmdResult->All = pBMUCQPTR->All;
#endif /*VS_SIM_EN*/

	if (pBMUCmdResult->BMUFreeRst.ubOpcode == BMU_FREE_CMD) {

#if VS_SIM_EN
		vQueuePop (CMD_CQ_GET_QUEUE_PTR (pBMUCmdResult->BMURst.ubTargetID), pBMUCQPTR);
#endif /*VS_SIM_EN*/
		M_DB_TRIGGER_READ_CNT(DB_BMU_NOBLK_CQ, 1);
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_CQ, gDBQueueCnt.QueueCnt[(DB_BMU_NOBLK_CQ)] ) == M_DB_GET_RPTR((DB_BMU_NOBLK_CQ)));
	}
	else {
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);//DB_BMU_NOBLK_CQ setting depth = 1
	}

	//CQ Is occupied by blocking
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, 0 == M_DB_CHECK_FULL(DB_BMU_NOBLK_CQ));
#endif/* (!BURNER_MODE_EN) */
	return BMU_CMD_STATUS_SUCCESS;
}

/*****************************************************
	BMU CMD API: Lock (OP Code = 5)
	reference HW api: ubBmuCmdApiLock
*****************************************************/
U8 BMUAPICmdLock (U8 btNoCQ, U16 uwPBAddr, U8 ubLBID, U32 ulLCA, BMUCmdResult_t *pBMUCmdResult)
{
	U16 uwCQRDCNT;
	U32 ulCQWPTR;
#if VS_SIM_EN
	LBNA_t lbna;
	BmuCmdStatus_t *pBMUCQPTR;
#else
	BMUCmdResult_t *pBMUCQPTR = NULL;
#endif
	U32 *pBMUSQCmdPTR = (U32 *)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_SQ, gDBQueueCnt.B.uwBMUNonBlockingSQCnt);
	if (M_DB_CHECK_FULL(DB_BMU_NOBLK_CQ) || M_DB_CHECK_FULL(DB_BMU_NOBLK_SQ)) {
		return BMU_CMD_STATUS_FAIL;
	}

	*pBMUSQCmdPTR = ((((U32)BMU_LOCK_CMD & BMU_CMD_LOCK_OPCODE_MASK) << BMU_CMD_LOCK_OPCODE_SHIFT)
			| (((U32)BMU_NOBLK_CQ_ID & BMU_CMD_LOCK_TARGET_ID_MASK) << BMU_CMD_LOCK_TARGET_ID_SHIFT)
			| (((U32)TRUE & BMU_CMD_LOCK_OPTION_MASK) << BMU_CMD_LOCK_OPTION_SHIFT)
			| (((U32)btNoCQ & BMU_CMD_LOCK_NOCQ_MASK) << BMU_CMD_LOCK_NOCQ_SHIFT)
			| (((U32)DEFAULT_TAG_ID & BMU_CMD_LOCK_TAG_ID_MASK) << BMU_CMD_LOCK_TAG_ID_SHIFT));
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = ((((U32)uwPBAddr & BMU_CMD_LOCK_PB_ADDR_MASK) << BMU_CMD_LOCK_PB_ADDR_SHIFT)
			| (((U32)ubLBID & BMU_CMD_LOCK_LB_ID_MASK) << BMU_CMD_LOCK_LB_ID_SHIFT));
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = ((U32)ulLCA << BMU_CMD_LOCK_LCA_SHIFT);
	pBMUSQCmdPTR++;
#if PS5021_EN
	*pBMUSQCmdPTR = ((((U32)uwPBAddr >> BMU_PB_BIT_EXTEND_START) & BMU_CMD_LOCK_PB_ADDR2_MASK) << BMU_CMD_LOCK_PB_ADDR2_SHIFT);
#else /* PS5021_EN */
	*pBMUSQCmdPTR = 0;
#endif /* PS5021_EN */

	uwCQRDCNT = M_DB_GET_RD_CNT(DB_BMU_NOBLK_CQ);
#if USE_FW_DB
	ulCQWPTR = (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_CQ, gDBQueueCnt.B.uwBMUCQ2Cnt); //Get WPTR (FW maintain)
#else
	ulCQWPTR = M_DB_GET_WPTR(DB_BMU_NOBLK_CQ);
#endif
#if VS_SIM_EN
	pBMUSQCmdPTR->ubTargetID = BMU_NOBLK_ID;
	lbna.uwLBNA = (U16)pBMUSQCmdPTR->uwPBAddress;
	while (BMU_CMD_STATUS_SUCCESS != ubBmuCmdApiLock((U8)pBMUSQCmdPTR->ubTargetID, (U16)pBMUSQCmdPTR->ubTagID, (U8)(!pBMUSQCmdPTR->btNoCQ), TIME_INFINITE, (U8)pBMUSQCmdPTR->btOption, ulLCA, lbna, 0, ubLBID, NULL));
#endif
	M_DB_TRIGGER_WRITE_CNT(DB_BMU_NOBLK_SQ, 1);
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_SQ, gDBQueueCnt.QueueCnt[(DB_BMU_NOBLK_SQ)] ) == M_DB_GET_WPTR((DB_BMU_NOBLK_SQ)));
	while (M_DB_GET_RD_CNT(DB_BMU_NOBLK_CQ) < (uwCQRDCNT + 1));

#if VS_SIM_EN
	memset(pBMUCmdResult, 0, sizeof(BMUCmdResult_t));

	pBMUCQPTR = (BmuCmdStatus_t *)ulCQWPTR;

	pBMUCmdResult->BMULockRst.ubOpcode = pBMUCQPTR->Lock_Sts_t.ubOpcode;
	pBMUCmdResult->BMULockRst.ubResult = pBMUCQPTR->Lock_Sts_t.ubStatus;
	pBMUCmdResult->BMULockRst.ubTagID = (U8)pBMUCQPTR->Lock_Sts_t.uwTagID;	//size not the same
	pBMUCmdResult->BMULockRst.uwPBAddress = pBMUCQPTR->Lock_Sts_t.LBNA.B.uwPoolOffset;
	pBMUCmdResult->BMULockRst.ubTargetID = pBMUCQPTR->Lock_Sts_t.ubSrcTagID;
#else
	pBMUCQPTR = (BMUCmdResult_t *)ulCQWPTR;
	pBMUCmdResult->All = pBMUCQPTR->All;
#endif

	if (pBMUCmdResult->BMUFreeRst.ubOpcode == BMU_LOCK_CMD) {
#if VS_SIM_EN
		vQueuePop (CMD_CQ_GET_QUEUE_PTR (pBMUCmdResult->BMURst.ubTargetID), pBMUCQPTR);
#endif
		M_DB_TRIGGER_READ_CNT(DB_BMU_NOBLK_CQ, 1);
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_CQ, gDBQueueCnt.QueueCnt[(DB_BMU_NOBLK_CQ)] ) == M_DB_GET_RPTR((DB_BMU_NOBLK_CQ)));
	}
	else {
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);//DB_BMU_NOBLK_CQ setting depth = 1
	}

	//CQ Is occupied by blocking
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, 0 == M_DB_CHECK_FULL(DB_BMU_NOBLK_CQ));

	return BMU_CMD_STATUS_SUCCESS;
}

/*****************************************************
	BMU CMD API: Unlock (OP Code = 6)
	reference HW api: ubBmuCmdApiUnLock
*****************************************************/
U8 BMUAPICmdUnLock (U8 btNoCQ, U16 uwPBAddr, U8 btQOP,  U8 ubLBID, BMUCmdResult_t *pBMUCmdResult)
{
	U16 uwCQRDCNT;
	U32 ulCQWPTR;
#if VS_SIM_EN
	LBNA_t lbna;
	BmuCmdStatus_t *pBMUCQPTR;
#else /*VS_SIM_EN*/
	BMUCmdResult_t *pBMUCQPTR = NULL;
#endif /*VS_SIM_EN*/
	U32 *pBMUSQCmdPTR = (U32 *)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_SQ, gDBQueueCnt.B.uwBMUNonBlockingSQCnt);
	if (M_DB_CHECK_FULL(DB_BMU_NOBLK_CQ) || M_DB_CHECK_FULL(DB_BMU_NOBLK_SQ)) {
		return BMU_CMD_STATUS_FAIL;
	}

	*pBMUSQCmdPTR = ((((U32)BMU_UNLOCK_CMD & BMU_CMD_UNLOCK_OPCODE_MASK) << BMU_CMD_UNLOCK_OPCODE_SHIFT)
			| (((U32)BMU_NOBLK_CQ_ID & BMU_CMD_UNLOCK_TARGET_ID_MASK) << BMU_CMD_UNLOCK_TARGET_ID_SHIFT)
			| (((U32)btQOP & BMU_CMD_UNLOCK_QOP_MASK) << BMU_CMD_UNLOCK_QOP_SHIFT)
			| (((U32)btNoCQ & BMU_CMD_UNLOCK_NOCQ_MASK) << BMU_CMD_UNLOCK_NOCQ_SHIFT)
			| (((U32)DEFAULT_TAG_ID & BMU_CMD_UNLOCK_TAG_ID_MASK) << BMU_CMD_UNLOCK_TAG_ID_SHIFT));
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = ((((U32)uwPBAddr & BMU_CMD_UNLOCK_PB_ADDR_MASK) << BMU_CMD_UNLOCK_PB_ADDR_SHIFT)
			| (((U32)ubLBID & BMU_CMD_UNLOCK_LB_ID_MASK) << BMU_CMD_UNLOCK_LB_ID_SHIFT));
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = 0;
	pBMUSQCmdPTR++;
#if PS5021_EN
	*pBMUSQCmdPTR = ((((U32)uwPBAddr >> BMU_PB_BIT_EXTEND_START) & BMU_CMD_UNLOCK_PB_ADDR2_MASK) << BMU_CMD_UNLOCK_PB_ADDR2_SHIFT);
#else /* PS5021_EN */
	*pBMUSQCmdPTR = 0;
#endif /* PS5021_EN */

	uwCQRDCNT = M_DB_GET_RD_CNT(DB_BMU_NOBLK_CQ);
#if USE_FW_DB
	ulCQWPTR = (U32) M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_CQ, gDBQueueCnt.B.uwBMUCQ2Cnt); //Get WPTR (FW maintain)
#else
	ulCQWPTR = M_DB_GET_WPTR(DB_BMU_NOBLK_CQ);
#endif
#if VS_SIM_EN
	pBMUSQCmdPTR->ubTargetID = BMU_NOBLK_ID;
	lbna.uwLBNA = (U16)pBMUSQCmdPTR->uwPBAddress;
	while (BMU_CMD_STATUS_SUCCESS != ubBmuCmdApiUnLock((U8)pBMUSQCmdPTR->ubTargetID, (U16)pBMUSQCmdPTR->ubTagID, (U8)(!pBMUSQCmdPTR->btNoCQ), lbna, ubLBID, NULL));
#endif /*VS_SIM_EN*/
	M_DB_TRIGGER_WRITE_CNT(DB_BMU_NOBLK_SQ, 1);
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_SQ, gDBQueueCnt.QueueCnt[(DB_BMU_NOBLK_SQ)] ) == M_DB_GET_WPTR((DB_BMU_NOBLK_SQ)));
	while (M_DB_GET_RD_CNT(DB_BMU_NOBLK_CQ) < (uwCQRDCNT + 1));

#if VS_SIM_EN
	memset(pBMUCmdResult, 0, sizeof(BMUCmdResult_t));

	pBMUCQPTR = (BmuCmdStatus_t *)ulCQWPTR;

	pBMUCmdResult->BMUUnLockRst.ubOpcode = pBMUCQPTR->Unlock_Sts_t.ubOpcode;
	pBMUCmdResult->BMUUnLockRst.ubResult = pBMUCQPTR->Unlock_Sts_t.ubStatus;
	pBMUCmdResult->BMUUnLockRst.ubTagID = (U8)pBMUCQPTR->Unlock_Sts_t.uwTagID;	//size not the same
	pBMUCmdResult->BMUUnLockRst.uwPBAddress = pBMUCQPTR->Unlock_Sts_t.LBNA.B.uwPoolOffset;
	pBMUCmdResult->BMUUnLockRst.ubTargetID = pBMUCQPTR->Unlock_Sts_t.ubSrcTagID;
#else /*VS_SIM_EN*/
	pBMUCQPTR = (BMUCmdResult_t *)ulCQWPTR;
	pBMUCmdResult->All = pBMUCQPTR->All;
#endif /*VS_SIM_EN*/

	if (pBMUCmdResult->BMUFreeRst.ubOpcode == BMU_UNLOCK_CMD) {
#if VS_SIM_EN
		vQueuePop (CMD_CQ_GET_QUEUE_PTR (pBMUCmdResult->BMURst.ubTargetID), pBMUCQPTR);
#endif /*VS_SIM_EN*/
		M_DB_TRIGGER_READ_CNT(DB_BMU_NOBLK_CQ, 1);
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_CQ, gDBQueueCnt.QueueCnt[(DB_BMU_NOBLK_CQ)] ) == M_DB_GET_RPTR((DB_BMU_NOBLK_CQ)));
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, BMU_RESULT_SUCCESS == pBMUCmdResult->BMUUnLockRst.ubResult);
	}
	else {
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);//DB_BMU_NOBLK_CQ setting depth = 1
	}

	//CQ Is occupied by blocking
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, 0 == M_DB_CHECK_FULL(DB_BMU_NOBLK_CQ));

	return BMU_CMD_STATUS_SUCCESS;
}


/******************************************
	BMU CMD API: Allocate_PB (OP Code = 7)
	reference HW api : ubBmuCmdApiAllocatePB
******************************************/
U8 BMUAPICmdAllocatePB(U8 btNoCQ, U8 ubTimeout, U32 ulLCA, CTAG_t CTAG, U8 ubStreamID, U8 btOption, U8 btFUA, U8 btQOP, U8 ubLBID, U32 ulCallBackAddr, U16 uwCallBackData)
{
	U32 *pBMUSQCmdPTR = (U32 *)M_DB_GET_QBODY_PTR( DB_BMU_BLK_SQ, gDBQueueCnt.B.uwBMUBlockingSQCnt); //Get WPTR (FW maintain)
	cmd_table_t *puoCmdTable = NULL;
	U8 ubTagID;

	if (M_DB_CHECK_FULL(DB_BMU_BLK_SQ)) {
		return BMU_CMD_STATUS_FAIL;
	}
	ubTagID = (U8)FTLPopTagPool(BMU_TAG_POOL_ID);

	*pBMUSQCmdPTR = ((((U32)BMU_ALLOCATE_PB_CMD & BMU_CMD_ALLOCATE_PB_OPCODE_MASK) << BMU_CMD_ALLOCATE_PB_OPCODE_SHIFT)
			| (((U32)BMU_BLK_CQ_ID & BMU_CMD_ALLOCATE_PB_TARGET_ID_MASK) << BMU_CMD_ALLOCATE_PB_TARGET_ID_SHIFT)
			| (((U32)btOption & BMU_CMD_ALLOCATE_PB_OPTION_MASK) << BMU_CMD_ALLOCATE_PB_OPTION_SHIFT)
			| (((U32)btQOP & BMU_CMD_ALLOCATE_PB_QOP_MASK) << BMU_CMD_ALLOCATE_PB_QOP_SHIFT)
			| (((U32)ubTimeout & BMU_CMD_ALLOCATE_PB_TIMEOUT_MASK) << BMU_CMD_ALLOCATE_PB_TIMEOUT_SHIFT)
			| (((U32)btFUA & BMU_CMD_ALLOCATE_PB_FUA_MASK) << BMU_CMD_ALLOCATE_PB_FUA_SHIFT)
			| (((U32)btNoCQ & BMU_CMD_ALLOCATE_PB_NOCQ_MASK) << BMU_CMD_ALLOCATE_PB_NOCQ_SHIFT)
			| (((U32)ubTagID & BMU_CMD_ALLOCATE_PB_TAG_ID_MASK) << BMU_CMD_ALLOCATE_PB_TAG_ID_SHIFT));
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = ((((U32)ubLBID & BMU_CMD_ALLOCATE_PB_LB_ID_MASK) << BMU_CMD_ALLOCATE_PB_LB_ID_SHIFT)
			| (((U32)CTAG & BMU_CMD_ALLOCATE_PB_CTAG_MASK) << BMU_CMD_ALLOCATE_PB_CTAG_SHIFT));
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = ((U32)ulLCA << BMU_CMD_ALLOCATE_PB_LCA_SHIFT);
	pBMUSQCmdPTR++;
#if PS5021_EN
	*pBMUSQCmdPTR = ((((U32)ubStreamID & BMU_CMD_ALLOCATE_PB_STREAM_ID_MASK) << BMU_CMD_ALLOCATE_PB_STREAM_ID_SHIFT)
			| ((((U32)CTAG >> BMU_CTAG_BIT_EXTEND_START) & BMU_CMD_ALLOCATE_CTAG2_MASK) << BMU_CMD_ALLOCATE_CTAG2_SHIFT));
#else /* PS5021_EN */
	*pBMUSQCmdPTR = (((U32)ubStreamID & BMU_CMD_ALLOCATE_PB_STREAM_ID_MASK) << BMU_CMD_ALLOCATE_PB_STREAM_ID_SHIFT);
#endif /* PS5021_EN */

#if VS_SIM_EN
	pBMUSQCmdPTR->ubTargetID = BMU_BLK_ID;
	while (BMU_CMD_STATUS_SUCCESS != ubBmuCmdApiAllocatePB((U8)pBMUSQCmdPTR->ubTargetID, (U16)pBMUSQCmdPTR->ubTagID, CTAG, (U8)pBMUSQCmdPTR->ubTimeout, pBMUSQCmdPTR->ulLCA, 0, ubLBID, btFUA, btQOP, NULL));
#endif /*VS_SIM_EN*/
	M_DB_TRIGGER_WRITE_CNT(DB_BMU_BLK_SQ, 1);
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_BLK_SQ, gDBQueueCnt.QueueCnt[(DB_BMU_BLK_SQ)] ) == M_DB_GET_WPTR((DB_BMU_BLK_SQ)));
	puoCmdTable = &gpuoCmdTableMgr[BMU_CMD_TABLE_ID][ubTagID];
	puoCmdTable->ulCmdFuncPTR = ulCallBackAddr;
	puoCmdTable->ulData.uwPrivate = uwCallBackData;

	return BMU_CMD_STATUS_SUCCESS;
}

/*****************************************************
	BMU CMD API: Free PB (OP Code = 8)
	reference HW api: ubBmuCmdApiFreePB
*****************************************************/
U8 BMUAPICmdFreePB (U8 btNoCQ, U16 uwPBAddr, U8 ubFreeOp, U8 ubQOP, U8 ubLBID, BMUCmdResult_t *pBMUCmdResult)
{
#if VS_SIM_EN
	LBNA_t lbna;
	BmuCmdStatus_t *pBMUCQPTR;
#else /*VS_SIM_EN*/
	BMUCmdResult_t *pBMUCQPTR = NULL;
#endif /*VS_SIM_EN*/
	U16 uwCQRDCNT;
	U32 ulCQWPTR;
	U32 *pBMUSQCmdPTR = (U32 *)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_SQ, gDBQueueCnt.B.uwBMUNonBlockingSQCnt);
	if (M_DB_CHECK_FULL(DB_BMU_NOBLK_CQ) || M_DB_CHECK_FULL(DB_BMU_NOBLK_SQ)) {
		return BMU_CMD_STATUS_FAIL;
	}

	*pBMUSQCmdPTR = ((((U32)BMU_FREE_PB_CMD & BMU_CMD_FREE_PB_OPCODE_MASK) << BMU_CMD_FREE_PB_OPCODE_SHIFT)
			| (((U32)BMU_NOBLK_CQ_ID & BMU_CMD_FREE_PB_TARGET_ID_MASK) << BMU_CMD_FREE_PB_TARGET_ID_SHIFT)
			| (((U32)ubQOP & BMU_CMD_FREE_PB_QOP_MASK) << BMU_CMD_FREE_PB_QOP_SHIFT)
			| (((U32)ubFreeOp & BMU_CMD_FREE_PB_FREE_OP_MASK) << BMU_CMD_FREE_PB_FREE_OP_SHIFT)
			| (((U32)btNoCQ & BMU_CMD_FREE_PB_NOCQ_MASK) << BMU_CMD_FREE_PB_NOCQ_SHIFT)
			| (((U32)DEFAULT_TAG_ID & BMU_CMD_FREE_PB_TAG_ID_MASK) << BMU_CMD_FREE_PB_TAG_ID_SHIFT));
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = ((((U32)uwPBAddr & BMU_CMD_FREE_PB_PB_ADDR_MASK) << BMU_CMD_FREE_PB_PB_ADDR_SHIFT)
			| (((U32)ubLBID & BMU_CMD_FREE_PB_LB_ID_MASK) << BMU_CMD_FREE_PB_LB_ID_SHIFT));
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = 0;
	pBMUSQCmdPTR++;
#if PS5021_EN
	*pBMUSQCmdPTR = ((((U32)uwPBAddr >> BMU_PB_BIT_EXTEND_START) & BMU_CMD_FREE_PB_PB_ADDR2_MASK) << BMU_CMD_FREE_PB_PB_ADDR2_SHIFT);
#else /* PS5021_EN */
	*pBMUSQCmdPTR = 0;
#endif /* PS5021_EN */

	uwCQRDCNT = M_DB_GET_RD_CNT(DB_BMU_NOBLK_CQ);
#if USE_FW_DB
	ulCQWPTR = (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_CQ, gDBQueueCnt.B.uwBMUCQ2Cnt); //Get WPTR (FW maintain)
#else
	ulCQWPTR = M_DB_GET_WPTR(DB_BMU_NOBLK_CQ);
#endif
#if VS_SIM_EN
	pBMUSQCmdPTR->ubTargetID = BMU_NOBLK_ID;
	lbna.uwLBNA = (U16)pBMUSQCmdPTR->uwPBAddress;
	while (BMU_CMD_STATUS_SUCCESS != ubBmuCmdApiFreePB((U8)pBMUSQCmdPTR->ubTargetID, (U16)pBMUSQCmdPTR->ubTagID, (U8)(!pBMUSQCmdPTR->btNoCQ), ubFreeOp, BMU_FREE_NEED_UPDATE_QUOTA, ubLBID, lbna, NULL));
#endif /*VS_SIM_EN*/
	M_DB_TRIGGER_WRITE_CNT(DB_BMU_NOBLK_SQ, 1);
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_SQ, gDBQueueCnt.QueueCnt[(DB_BMU_NOBLK_SQ)] ) == M_DB_GET_WPTR((DB_BMU_NOBLK_SQ)));
	while (M_DB_GET_RD_CNT(DB_BMU_NOBLK_CQ) < (uwCQRDCNT + 1));

#if VS_SIM_EN
	memset(pBMUCmdResult, 0, sizeof(BMUCmdResult_t));

	pBMUCQPTR = (BmuCmdStatus_t *)ulCQWPTR;

	pBMUCmdResult->BMUFreePBRst.ubOpcode = pBMUCQPTR->FreePB_Sts_t.ubOpcode;
	pBMUCmdResult->BMUFreePBRst.ubResult = pBMUCQPTR->FreePB_Sts_t.ubStatus;
	pBMUCmdResult->BMUFreePBRst.ubTagID = (U8)pBMUCQPTR->FreePB_Sts_t.uwTagID;	//size not the same
	pBMUCmdResult->BMUFreePBRst.uwPBAddress = pBMUCQPTR->FreePB_Sts_t.LBNA.B.uwPoolOffset;
	pBMUCmdResult->BMUFreePBRst.ubTargetID = pBMUCQPTR->FreePB_Sts_t.ubSrcTagID;
#else /*VS_SIM_EN*/
	pBMUCQPTR = (BMUCmdResult_t *)ulCQWPTR;
	pBMUCmdResult->All = pBMUCQPTR->All;
#endif /*VS_SIM_EN*/

	if (pBMUCmdResult->BMUFreePBRst.ubOpcode == BMU_FREE_PB_CMD) {
#if VS_SIM_EN
		vQueuePop (CMD_CQ_GET_QUEUE_PTR (pBMUCmdResult->BMURst.ubTargetID), pBMUCQPTR);
#endif /*VS_SIM_EN*/
		M_DB_TRIGGER_READ_CNT(DB_BMU_NOBLK_CQ, 1);
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_CQ, gDBQueueCnt.QueueCnt[(DB_BMU_NOBLK_CQ)] ) == M_DB_GET_RPTR((DB_BMU_NOBLK_CQ)));
	}
	else {
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);//DB_BMU_NOBLK_CQ setting depth = 1
	}

	//CQ Is occupied by blocking
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, 0 == M_DB_CHECK_FULL(DB_BMU_NOBLK_CQ));

	return BMU_CMD_STATUS_SUCCESS;
}

/******************************************
	BMU CMD API: Allocate_LB (OP Code = 9)
	reference HW api : ubBmuCmdApiAllocateLB
******************************************/
U8 BMUAPICmdAllocateLB (U8 btNoCQ, U16 uwPBAddr, U8 btQOP, U8 ubLBID, BMUCmdResult_t *pBMUCmdResult)
{
	U32 *pBMUSQCmdPTR = (U32 *)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_SQ, gDBQueueCnt.B.uwBMUNonBlockingSQCnt); //Get WPTR (FW maintain)
	U16 uwCQRDCNT;
	U32 ulCQWPTR;
#if VS_SIM_EN
	LBNA_t LBNA;
	BmuCmdStatus_t *pBMUCQPTR;
#else
	BMUCmdResult_t *pBMUCQPTR = NULL;
#endif /*VS_SIM_EN*/
	if (M_DB_CHECK_FULL(DB_BMU_NOBLK_SQ)) {
		return BMU_CMD_STATUS_FAIL;
	}

	*pBMUSQCmdPTR = ((((U32)BMU_ALLOCATE_LB_CMD & BMU_CMD_ALLOCATE_LB_OPCODE_MASK) << BMU_CMD_ALLOCATE_LB_OPCODE_SHIFT)
			| (((U32)BMU_NOBLK_CQ_ID & BMU_CMD_ALLOCATE_LB_TARGET_ID_MASK) << BMU_CMD_ALLOCATE_LB_TARGET_ID_SHIFT)
			| (((U32)btQOP & BMU_CMD_ALLOCATE_LB_QOP_MASK) << BMU_CMD_ALLOCATE_LB_QOP_SHIFT)
			| (((U32)btNoCQ & BMU_CMD_ALLOCATE_LB_NOCQ_MASK) << BMU_CMD_ALLOCATE_LB_NOCQ_SHIFT)
			| (((U32)DEFAULT_TAG_ID & BMU_CMD_ALLOCATE_LB_TAG_ID_MASK) << BMU_CMD_ALLOCATE_LB_TAG_ID_SHIFT));
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = ((((U32)uwPBAddr & BMU_CMD_ALLOCATE_LB_PB_ADDR_MASK) << BMU_CMD_ALLOCATE_LB_PB_ADDR_SHIFT)
			| (((U32)ubLBID & BMU_CMD_ALLOCATE_LB_LB_ID_MASK) << BMU_CMD_ALLOCATE_LB_LB_ID_SHIFT));
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = 0;
	pBMUSQCmdPTR++;
#if PS5021_EN
	*pBMUSQCmdPTR = ((((U32)uwPBAddr >> BMU_PB_BIT_EXTEND_START) & BMU_CMD_FREE_PB_PB_ADDR2_MASK) << BMU_CMD_FREE_PB_PB_ADDR2_SHIFT);
#else /* PS5021_EN */
	*pBMUSQCmdPTR = 0;
#endif /* PS5021_EN */

	uwCQRDCNT = M_DB_GET_RD_CNT(DB_BMU_NOBLK_CQ);
#if USE_FW_DB
	ulCQWPTR = (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_CQ, gDBQueueCnt.B.uwBMUCQ2Cnt); //Get WPTR (FW maintain)
#else
	ulCQWPTR = M_DB_GET_WPTR(DB_BMU_NOBLK_CQ);
#endif

#if VS_SIM_EN
	LBNA.B.uwPoolID = 0;
	LBNA.B.uwPoolOffset = uwPBAddr;
	pBMUSQCmdPTR->ubTargetID = BMU_NOBLK_ID;
	while (BMU_CMD_STATUS_SUCCESS != ubBmuCmdApiAllocateLB((U8)pBMUSQCmdPTR->ubTargetID, (U16)pBMUSQCmdPTR->ubTagID, !btNoCQ, ubLBID, LBNA, pBMUSQCmdPTR->ubTimeout, NULL));
#endif /*VS_SIM_EN*/
	M_DB_TRIGGER_WRITE_CNT(DB_BMU_NOBLK_SQ, 1);
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_SQ, gDBQueueCnt.QueueCnt[(DB_BMU_NOBLK_SQ)] ) == M_DB_GET_WPTR((DB_BMU_NOBLK_SQ)));
	while (M_DB_GET_RD_CNT(DB_BMU_NOBLK_CQ) < (uwCQRDCNT + 1));

#if VS_SIM_EN
	memset(pBMUCmdResult, 0, sizeof(BMUCmdResult_t));

	pBMUCQPTR = (BmuCmdStatus_t *)ulCQWPTR;

	pBMUCmdResult->BMUAllocateLBRst.ubOpcode = pBMUCQPTR->AllocateLB_Sts_t.ubOpcode;
	pBMUCmdResult->BMUAllocateLBRst.ubResult = pBMUCQPTR->AllocateLB_Sts_t.ubStatus;
	pBMUCmdResult->BMUAllocateLBRst.ubTagID = (U8)pBMUCQPTR->AllocateLB_Sts_t.uwTagID;	//size not the same
	pBMUCmdResult->BMUAllocateLBRst.ubTargetID = pBMUCQPTR->AllocateLB_Sts_t.ubSrcTagID;
#else /*VS_SIM_EN*/
	pBMUCQPTR = (BMUCmdResult_t *)ulCQWPTR;
	pBMUCmdResult->All = pBMUCQPTR->All;
#endif /*VS_SIM_EN*/

	if (pBMUCmdResult->BMUAllocateLBRst.ubOpcode == BMU_ALLOCATE_LB_CMD) {
#if VS_SIM_EN
		vQueuePop (CMD_CQ_GET_QUEUE_PTR (pBMUCmdResult->BMURst.ubTargetID), pBMUCQPTR);
#endif /*VS_SIM_EN*/
		M_DB_TRIGGER_READ_CNT(DB_BMU_NOBLK_CQ, 1);
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_CQ, gDBQueueCnt.QueueCnt[(DB_BMU_NOBLK_CQ)] ) == M_DB_GET_RPTR((DB_BMU_NOBLK_CQ)));
	}
	else {
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);//DB_BMU_NOBLK_CQ setting depth = 1
	}

	return BMU_CMD_STATUS_SUCCESS;
}
/***************************************************************************
	BMU CMD API: Free LB(OP Code = 10)
	reference HW api: ubBmuCmdApiFree
***************************************************************************/
U8 BMUAPICmdFreeLB(U8 btNoCQ, U8 ubLBID, U16 uwLBOffset, U8 btFAR, U8 ubQuotaOption, BMUCmdResult_t *pBMUCmdResult)
{
	U16 uwCQRDCNT;
	U32 ulCQWPTR;
#if VS_SIM_EN
	BmuCmdStatus_t *pBMUCQPTR;
#else /*VS_SIM_EN*/
	BMUCmdResult_t *pBMUCQPTR = NULL;
#endif /*VS_SIM_EN*/

	U32 *pBMUSQCmdPTR = (U32 *)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_SQ, gDBQueueCnt.B.uwBMUNonBlockingSQCnt);
	if (M_DB_CHECK_FULL(DB_BMU_NOBLK_CQ) || M_DB_CHECK_FULL(DB_BMU_NOBLK_SQ)) {
		return BMU_CMD_STATUS_FAIL;
	}

	*pBMUSQCmdPTR = ((((U32)BMU_FREE_LB_CMD & BMU_CMD_FREE_LB_OPCODE_MASK) << BMU_CMD_FREE_LB_OPCODE_SHIFT)
			| (((U32)BMU_NOBLK_CQ_ID & BMU_CMD_FREE_LB_TARGET_ID_MASK) << BMU_CMD_FREE_LB_TARGET_ID_SHIFT)
			| (((U32)ubQuotaOption & BMU_CMD_FREE_LB_QOP_MASK) << BMU_CMD_FREE_LB_QOP_SHIFT)
			| (((U32)btFAR & BMU_CMD_FREE_LB_FAR_MASK) << BMU_CMD_FREE_LB_FAR_SHIFT)
			| (((U32)btNoCQ & BMU_CMD_FREE_LB_NOCQ_MASK) << BMU_CMD_FREE_LB_NOCQ_SHIFT)
			| (((U32)DEFAULT_TAG_ID & BMU_CMD_FREE_LB_TAG_ID_MASK) << BMU_CMD_FREE_LB_TAG_ID_SHIFT));
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = ((((U32)uwLBOffset & BMU_CMD_FREE_LB_LB_OFFSET_MASK) << BMU_CMD_FREE_LB_LB_OFFSET_SHIFT)
			| (((U32)ubLBID & BMU_CMD_FREE_LB_LB_ID_MASK) << BMU_CMD_FREE_LB_LB_ID_SHIFT));
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = 0;
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = 0;

	uwCQRDCNT = M_DB_GET_RD_CNT(DB_BMU_NOBLK_CQ);
#if USE_FW_DB
	ulCQWPTR = (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_CQ, gDBQueueCnt.B.uwBMUCQ2Cnt); //Get WPTR (FW maintain)
#else
	ulCQWPTR = M_DB_GET_WPTR(DB_BMU_NOBLK_CQ);
#endif
#if VS_SIM_EN
	pBMUSQCmdPTR->ubTargetID = BMU_NOBLK_ID;
	if (btFAR == BMU_FREE_FAR_EN) {
		while (BMU_CMD_STATUS_SUCCESS != ubBmuCmdApiFreePBLink ((U8)pBMUSQCmdPTR->ubTargetID, (U16)pBMUSQCmdPTR->ubTagID, (U8)(!pBMUSQCmdPTR->btNoCQ), (U8)pBMUSQCmdPTR->btOption, (U8)pBMUSQCmdPTR->ubSize, (U16)pBMUSQCmdPTR->uwLBOffset, (U8)pBMUSQCmdPTR->ubLBID, NULL));
	}
	else {
		while (BMU_CMD_STATUS_SUCCESS != ubBmuCmdApiFree((U8)pBMUSQCmdPTR->ubTargetID, (U16)pBMUSQCmdPTR->ubTagID, (U8)(!pBMUSQCmdPTR->btNoCQ), (U8)pBMUSQCmdPTR->btOption, (U8)pBMUSQCmdPTR->ubSize, (U16)pBMUSQCmdPTR->uwLBOffset, (U8)pBMUSQCmdPTR->ubLBID, NULL));
	}
#endif /*VS_SIM_EN*/
	M_DB_TRIGGER_WRITE_CNT(DB_BMU_NOBLK_SQ, 1);
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_SQ, gDBQueueCnt.QueueCnt[(DB_BMU_NOBLK_SQ)] ) == M_DB_GET_WPTR((DB_BMU_NOBLK_SQ)));
	while (M_DB_GET_RD_CNT(DB_BMU_NOBLK_CQ) < (uwCQRDCNT + 1));

#if VS_SIM_EN
	memset(pBMUCmdResult, 0, sizeof(BMUCmdResult_t));

	pBMUCQPTR = (BmuCmdStatus_t *)ulCQWPTR;
	pBMUCQPTR->Free_Sts_t.ubOpcode = BMU_FREE_CMD;
	pBMUCmdResult->BMUFreeRst.ubOpcode = pBMUCQPTR->Free_Sts_t.ubOpcode;
	pBMUCmdResult->BMUFreeRst.ubResult = pBMUCQPTR->Free_Sts_t.ubStatus;
	pBMUCmdResult->BMUFreeRst.btOption = pBMUCQPTR->Free_Sts_t.ubFullCheckOption;
	pBMUCmdResult->BMUFreeRst.ubDoneCount = pBMUCQPTR->Free_Sts_t.ubSize;
	pBMUCmdResult->BMUFreeRst.ubTagID = (U8)pBMUCQPTR->Free_Sts_t.uwTagID;	//size not the same
	pBMUCmdResult->BMUFreeRst.uwPBAddress = pBMUCQPTR->Free_Sts_t.LBNA.B.uwPoolOffset;
	pBMUCmdResult->BMUFreeRst.ubLBID = pBMUCQPTR->Free_Sts_t.ubLogicalBufferID & 0x7;
	pBMUCmdResult->BMUFreeRst.uwLBOffset = pBMUCQPTR->Free_Sts_t.uwLogicalBufferOffset & 0x3FF;
	pBMUCmdResult->BMUFreeRst.ubTargetID = pBMUCQPTR->Free_Sts_t.ubSrcTagID;
#else /*VS_SIM_EN*/
	pBMUCQPTR = (BMUCmdResult_t *)ulCQWPTR;
	pBMUCmdResult->All = pBMUCQPTR->All;
#endif /*VS_SIM_EN*/

	if (pBMUCmdResult->BMUFreeRst.ubOpcode == BMU_FREE_LB_CMD) {

#if VS_SIM_EN
		vQueuePop (CMD_CQ_GET_QUEUE_PTR (pBMUCmdResult->BMURst.ubTargetID), pBMUCQPTR);
#endif /*VS_SIM_EN*/
		M_DB_TRIGGER_READ_CNT(DB_BMU_NOBLK_CQ, 1);
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_CQ, gDBQueueCnt.QueueCnt[(DB_BMU_NOBLK_CQ)] ) == M_DB_GET_RPTR((DB_BMU_NOBLK_CQ)));
	}
	else {
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);//DB_BMU_NOBLK_CQ setting depth = 1
	}

	//CQ Is occupied by blocking
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, 0 == M_DB_CHECK_FULL(DB_BMU_NOBLK_CQ));

	return BMU_CMD_STATUS_SUCCESS;
}

/******************************************
	BMU CMD API: Get_LBNA (OP Code = 11)
	reference HW api : ubBmuCmdApiGetLBNA
******************************************/
U8 BMUAPICmdGetLBNA (U8 btNoCQ, U8 ubLBID, U16 uwLBOffset, BMUCmdResult_t *pBMUCmdResult)
{
#if VS_SIM_EN
	BmuCmdStatus_t *pBMUCQPTR;
#else /*VS_SIM_EN*/
	BMUCmdResult_t *pBMUCQPTR = NULL;
#endif /*VS_SIM_EN*/
	U16 uwCQRDCNT;
	U32 ulCQWPTR;
	U32 *pBMUSQCmdPTR = (U32 *)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_SQ, gDBQueueCnt.B.uwBMUNonBlockingSQCnt); //Get WPTR (FW maintain)
	if (M_DB_CHECK_FULL(DB_BMU_NOBLK_CQ) || M_DB_CHECK_FULL(DB_BMU_NOBLK_SQ)) {
		return BMU_CMD_STATUS_FAIL;
	}

	*pBMUSQCmdPTR = ((((U32)BMU_GET_LBNA_CMD & BMU_CMD_GET_LBNA_OPCODE_MASK) << BMU_CMD_GET_LBNA_OPCODE_SHIFT)
			| (((U32)BMU_NOBLK_CQ_ID & BMU_CMD_GET_LBNA_TARGET_ID_MASK) << BMU_CMD_GET_LBNA_TARGET_ID_SHIFT)
			| (((U32)btNoCQ & BMU_CMD_GET_LBNA_NOCQ_MASK) << BMU_CMD_GET_LBNA_NOCQ_SHIFT)
			| (((U32)DEFAULT_TAG_ID & BMU_CMD_GET_LBNA_TAG_ID_MASK) << BMU_CMD_GET_LBNA_TAG_ID_SHIFT));
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = ((((U32)uwLBOffset & BMU_CMD_GET_LBNA_LB_OFFSET_MASK) << BMU_CMD_GET_LBNA_LB_OFFSET_SHIFT)
			| (((U32)ubLBID & BMU_CMD_GET_LBNA_LB_ID_MASK) << BMU_CMD_GET_LBNA_LB_ID_SHIFT));
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = 0;
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = 0;

	uwCQRDCNT = M_DB_GET_RD_CNT(DB_BMU_NOBLK_CQ);
#if USE_FW_DB
	ulCQWPTR = (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_CQ, gDBQueueCnt.B.uwBMUCQ2Cnt); //Get WPTR (FW maintain)
#else
	ulCQWPTR = M_DB_GET_WPTR(DB_BMU_NOBLK_CQ);
#endif

#if VS_SIM_EN
	pBMUSQCmdPTR->ubTargetID = BMU_NOBLK_ID;
	while (BMU_CMD_STATUS_SUCCESS != ubBmuCmdApiGetLBNA ((U8)pBMUSQCmdPTR->ubTargetID, (U16)pBMUSQCmdPTR->ubTagID, (U8)(!pBMUSQCmdPTR->btNoCQ), (U16)pBMUSQCmdPTR->uwLBOffset, (U8)pBMUSQCmdPTR->ubLBID, NULL));
#endif /*VS_SIM_EN*/
	M_DB_TRIGGER_WRITE_CNT(DB_BMU_NOBLK_SQ, 1);
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_SQ, gDBQueueCnt.QueueCnt[(DB_BMU_NOBLK_SQ)] ) == M_DB_GET_WPTR((DB_BMU_NOBLK_SQ)));
	while (M_DB_GET_RD_CNT(DB_BMU_NOBLK_CQ) < (uwCQRDCNT + 1));

#if VS_SIM_EN
	memset(pBMUCmdResult, 0, sizeof(BMUCmdResult_t));

	pBMUCQPTR = (BmuCmdStatus_t *)ulCQWPTR;

	pBMUCmdResult->BMUGetLBNARst.ubOpcode = pBMUCQPTR->GetLBNA_Sts_t.ubOpcode;
	pBMUCmdResult->BMUGetLBNARst.ubResult = pBMUCQPTR->GetLBNA_Sts_t.ubStatus;
	pBMUCmdResult->BMUGetLBNARst.ubTagID = (U8)pBMUCQPTR->GetLBNA_Sts_t.uwTagID;	//size not the same
	pBMUCmdResult->BMUGetLBNARst.uwPBAddress = pBMUCQPTR->GetLBNA_Sts_t.LBNA.B.uwPoolOffset;
	pBMUCmdResult->BMUGetLBNARst.ubLBID = pBMUCQPTR->GetLBNA_Sts_t.ubLogicalBufferID & 0x7;
	pBMUCmdResult->BMUGetLBNARst.uwLBOffset = pBMUCQPTR->GetLBNA_Sts_t.uwLogicalBufferOffset & 0x3FF;
	pBMUCmdResult->BMUGetLBNARst.ubTargetID = pBMUCQPTR->GetLBNA_Sts_t.ubSrcTagID;
#else /*VS_SIM_EN*/
	pBMUCQPTR = (BMUCmdResult_t *)ulCQWPTR;
	pBMUCmdResult->All = pBMUCQPTR->All;
#endif /*VS_SIM_EN*/

	if (pBMUCmdResult->BMUFreePBRst.ubOpcode == BMU_GET_LBNA_CMD) {
#if VS_SIM_EN
		vQueuePop (CMD_CQ_GET_QUEUE_PTR (pBMUCmdResult->BMURst.ubTargetID), pBMUCQPTR);
#endif /*VS_SIM_EN*/
		M_DB_TRIGGER_READ_CNT(DB_BMU_NOBLK_CQ, 1);
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_CQ, gDBQueueCnt.QueueCnt[(DB_BMU_NOBLK_CQ)] ) == M_DB_GET_RPTR((DB_BMU_NOBLK_CQ)));
	}
	else {
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);//DB_BMU_NOBLK_CQ setting depth = 1
	}

	//CQ Is occupied by blocking
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, 0 == M_DB_CHECK_FULL(DB_BMU_NOBLK_CQ));

	return BMU_CMD_STATUS_SUCCESS;
}
/******************************************
	BMU CMD API: Get_PBNA (OP Code = 12)
	reference HW api : ubBmuCmdApiGetPBNA
******************************************/
U8 BMUAPICmdGetPBNA(U8 btNoCQ, U16 uwPBAddr, U8 ubLBID, U16 uwLBOffset, BMUCmdResult_t *pBMUCmdResult)
{
#if VS_SIM_EN
	LBNA_t lbna;
	BmuCmdStatus_t *pBMUCQPTR;
#else /*VS_SIM_EN*/
	BMUCmdResult_t *pBMUCQPTR = NULL;
#endif /*VS_SIM_EN*/
	U16 uwCQRDCNT;
	U32 ulCQWPTR;
	U32 *pBMUSQCmdPTR = (U32 *)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_SQ, gDBQueueCnt.B.uwBMUNonBlockingSQCnt); //Get WPTR (FW maintain)

	if (M_DB_CHECK_FULL(DB_BMU_NOBLK_CQ) || M_DB_CHECK_FULL(DB_BMU_NOBLK_SQ)) {
		return BMU_CMD_STATUS_FAIL;
	}

	*pBMUSQCmdPTR = ((((U32)BMU_GET_PBNA_CMD & BMU_CMD_GET_PBNA_OPCODE_MASK) << BMU_CMD_GET_PBNA_OPCODE_SHIFT)
			| (((U32)BMU_NOBLK_CQ_ID & BMU_CMD_GET_PBNA_TARGET_ID_MASK) << BMU_CMD_GET_PBNA_TARGET_ID_SHIFT)
			| (((U32)btNoCQ & BMU_CMD_GET_PBNA_NOCQ_MASK) << BMU_CMD_GET_PBNA_NOCQ_SHIFT)
			| (((U32)DEFAULT_TAG_ID & BMU_CMD_GET_PBNA_TAG_ID_MASK) << BMU_CMD_GET_PBNA_TAG_ID_SHIFT));
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = ((((U32)uwPBAddr & BMU_CMD_GET_PBNA_PB_ADDR_MASK) << BMU_CMD_GET_PBNA_PB_ADDR_SHIFT)
			| (((U32)uwLBOffset & BMU_CMD_GET_PBNA_LB_OFFSET_MASK) << BMU_CMD_GET_PBNA_LB_OFFSET_SHIFT)
			| (((U32)ubLBID & BMU_CMD_GET_PBNA_LB_ID_MASK) << BMU_CMD_GET_PBNA_LB_ID_SHIFT));
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = 0;
	pBMUSQCmdPTR++;
#if PS5021_EN
	*pBMUSQCmdPTR = ((((U32)uwPBAddr >> BMU_PB_BIT_EXTEND_START) & BMU_CMD_GET_PBNA_PB_ADDR2_MASK) << BMU_CMD_GET_PBNA_PB_ADDR2_SHIFT);
#else /* PS5021_EN */
	*pBMUSQCmdPTR = 0;
#endif /* PS5021_EN */

	uwCQRDCNT = M_DB_GET_RD_CNT(DB_BMU_NOBLK_CQ);
#if USE_FW_DB
	ulCQWPTR = (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_CQ, gDBQueueCnt.B.uwBMUCQ2Cnt); //Get WPTR (FW maintain)
#else
	ulCQWPTR = M_DB_GET_WPTR(DB_BMU_NOBLK_CQ);
#endif
#if VS_SIM_EN
	pBMUSQCmdPTR->ubTargetID = BMU_NOBLK_ID;
	lbna.uwLBNA = (U16)pBMUSQCmdPTR->uwPBAddress;
	while (BMU_CMD_STATUS_SUCCESS != ubBmuCmdApiGetPBNA ((U8)pBMUSQCmdPTR->ubTargetID, (U16)pBMUSQCmdPTR->ubTagID,  (U8)(!pBMUSQCmdPTR->btNoCQ), /*ubOption*/0, lbna, (U16)pBMUSQCmdPTR->uwLBOffset, (U8)pBMUSQCmdPTR->ubLBID, NULL));
#endif /*VS_SIM_EN*/
	M_DB_TRIGGER_WRITE_CNT(DB_BMU_NOBLK_SQ, 1);
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_SQ, gDBQueueCnt.QueueCnt[(DB_BMU_NOBLK_SQ)] ) == M_DB_GET_WPTR((DB_BMU_NOBLK_SQ)));
	while (M_DB_GET_RD_CNT(DB_BMU_NOBLK_CQ) < (uwCQRDCNT + 1));


#if VS_SIM_EN
	memset(pBMUCmdResult, 0, sizeof(BMUCmdResult_t));
	pBMUCQPTR = (BmuCmdStatus_t *)ulCQWPTR;
	pBMUCmdResult->BMUGetPBNARst.ubOpcode = pBMUCQPTR->GetPBNA_Sts_t.ubOpcode;
	pBMUCmdResult->BMUGetPBNARst.ubResult = pBMUCQPTR->GetPBNA_Sts_t.ubStatus;
	pBMUCmdResult->BMUGetPBNARst.ubTagID = (U8)pBMUCQPTR->GetPBNA_Sts_t.uwTagID;//size not the same
	pBMUCmdResult->BMUGetPBNARst.btNoCQ = (!((pBMUCQPTR->GetPBNA_Sts_t.uwTagID & 0x8000) >> 15));
	pBMUCmdResult->BMUGetPBNARst.uwPBAddress = (pBMUCQPTR->GetPBNA_Sts_t.LBNA.B.uwPoolOffset);
	pBMUCmdResult->BMUGetPBNARst.ulLCA = pBMUCQPTR->GetPBNA_Sts_t.ulLCA;
	pBMUCmdResult->BMUGetPBNARst.ubCTAG = pBMUCQPTR->GetPBNA_Sts_t.ubCTAG;
	pBMUCmdResult->BMUGetPBNARst.ubZinfo = pBMUCQPTR->GetPBNA_Sts_t.ubZByte;
	pBMUCmdResult->BMUGetPBNARst.ubLock = pBMUCQPTR->GetPBNA_Sts_t.ubLockCount;
	pBMUCmdResult->BMUGetPBNARst.btE3D4Kflag = pBMUCQPTR->GetPBNA_Sts_t.ubE3D4KFlag;
	pBMUCmdResult->BMUGetPBNARst.btFUA = pBMUCQPTR->GetPBNA_Sts_t.ubFUA;
	pBMUCmdResult->BMUGetPBNARst.btDF = pBMUCQPTR->GetPBNA_Sts_t.ubDoubleFree;
	pBMUCmdResult->BMUGetPBNARst.ulE3D4K = pBMUCQPTR->GetPBNA_Sts_t.ulE3D4K;
	pBMUCmdResult->BMUGetPBNARst.ubTargetID = pBMUCQPTR->GetPBNA_Sts_t.ubSrcTagID;
	pBMUCmdResult->BMUGetPBNARst.btFull = pBMUCQPTR->GetPBNA_Sts_t.ubFullFlag;
	pBMUCmdResult->BMUGetPBNARst.btZero = pBMUCQPTR->GetPBNA_Sts_t.ubZeroFlag;
	pBMUCmdResult->BMUGetPBNARst.btCmdEnd = pBMUCQPTR->GetPBNA_Sts_t.ubCommandEndFlag;
	pBMUCmdResult->BMUGetPBNARst.btFree = pBMUCQPTR->GetPBNA_Sts_t.ubFreeFlag;
	pBMUCmdResult->BMUGetPBNARst.ubValidBitmap = pBMUCQPTR->GetPBNA_Sts_t.ubValidBitmap;

#else /*VS_SIM_EN*/
	pBMUCQPTR = (BMUCmdResult_t *)ulCQWPTR;
	pBMUCmdResult->All = pBMUCQPTR->All;
#endif /*VS_SIM_EN*/

	if (pBMUCmdResult->BMUGetPBNARst.ubOpcode == BMU_GET_PBNA_CMD) {
#if VS_SIM_EN
		vQueuePop (CMD_CQ_GET_QUEUE_PTR (pBMUCmdResult->BMURst.ubTargetID), pBMUCQPTR);
#endif /*VS_SIM_EN*/
		M_DB_TRIGGER_READ_CNT(DB_BMU_NOBLK_CQ, 1);
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_CQ, gDBQueueCnt.QueueCnt[(DB_BMU_NOBLK_CQ)] ) == M_DB_GET_RPTR((DB_BMU_NOBLK_CQ)));
	}
	else {
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);//DB_BMU_NOBLK_CQ setting depth = 1
	}

	//CQ Is occupied by blocking
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, 0 == M_DB_CHECK_FULL(DB_BMU_NOBLK_CQ));

	return BMU_CMD_STATUS_SUCCESS;
}


/******************************************
	BMU CMD API: Add Event (OP Code = 13)
	reference HW api : ubBmuCmdApiAddEvent
******************************************/
U8 BMUAPICmdAddEvent(U8 btNoCQ, U16 uwPBAddr, U8 ubOption, BMUCmdResult_t *pBMUCmdResult, U32 ulCallBackAddr, U16 uwCallBackData)
{
#if VS_SIM_EN
	LBNA_t lbna;
	BmuCmdStatus_t *pBMUCQPTR;
#else /*VS_SIM_EN*/
	BMUCmdResult_t *pBMUCQPTR = NULL;
#endif /*VS_SIM_EN*/
	U16 uwCQRDCNT;
	U32 ulCQWPTR;
	cmd_table_t *puoCmdTable = NULL;
	U8 ubEventTagID;
	U32 *pBMUSQCmdPTR = (U32 *)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_SQ, gDBQueueCnt.B.uwBMUNonBlockingSQCnt); //Get WPTR (FW maintain)

	if (M_DB_CHECK_FULL(DB_BMU_NOBLK_CQ) || M_DB_CHECK_FULL(DB_BMU_NOBLK_SQ)) {
		return BMU_CMD_STATUS_FAIL;
	}
	ubEventTagID = (U8)FTLPopTagPool(BMU_TAG_POOL_ID);

	*pBMUSQCmdPTR = ((((U32)BMU_ADD_EVENT_CMD & BMU_CMD_ADD_EVENT_OPCODE_MASK) << BMU_CMD_ADD_EVENT_OPCODE_SHIFT)
			| (((U32)BMU_NOBLK_CQ_ID & BMU_CMD_ADD_EVENT_TARGET_ID_MASK) << BMU_CMD_ADD_EVENT_TARGET_ID_SHIFT)
			| (((U32)ubOption & BMU_CMD_ADD_EVENT_OPTION_MASK) << BMU_CMD_ADD_EVENT_OPTION_SHIFT)
			| (((U32)btNoCQ & BMU_CMD_ADD_EVENT_NOCQ_MASK) << BMU_CMD_ADD_EVENT_NOCQ_SHIFT)
			| (((U32)DEFAULT_TAG_ID & BMU_CMD_ADD_EVENT_TAG_ID_MASK) << BMU_CMD_ADD_EVENT_TAG_ID_SHIFT));
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = (((U32)uwPBAddr & BMU_CMD_ADD_EVENT_PB_ADDR_MASK) << BMU_CMD_ADD_EVENT_PB_ADDR_SHIFT);
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = ((((U32)BMU_BLK_CQ_ID & BMU_CMD_ADD_EVENT_EVENT_TARGET_ID_MASK) << BMU_CMD_ADD_EVENT_EVENT_TARGET_ID_SHIFT)
			| (((U32)ubEventTagID & BMU_CMD_ADD_EVENT_EVENT_TAG_MASK) << BMU_CMD_ADD_EVENT_EVENT_TAG_SHIFT));
	pBMUSQCmdPTR++;
#if PS5021_EN
	*pBMUSQCmdPTR = ((((U32)uwPBAddr >> BMU_PB_BIT_EXTEND_START) & BMU_CMD_ADD_EVENT_PB_ADDR2_MASK) << BMU_CMD_ADD_EVENT_PB_ADDR2_SHIFT);
#else /* PS5021_EN */
	*pBMUSQCmdPTR = 0;
#endif /* PS5021_EN */

	//TargetID:
	//0->CQ0		--->		BMU_NOBLK_ID(0)
	//2->CQ1		--->		BMU_BLK_ID(1)

	uwCQRDCNT = M_DB_GET_RD_CNT(DB_BMU_NOBLK_CQ);
#if USE_FW_DB
	ulCQWPTR = (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_CQ, gDBQueueCnt.B.uwBMUCQ2Cnt); //Get WPTR (FW maintain)
#else
	ulCQWPTR = M_DB_GET_WPTR(DB_BMU_NOBLK_CQ);
#endif

#if VS_SIM_EN
	BMUDelegateCmd();//avoid BMU thread (blocking CQ full) & FW thread (wait non-block CQ back) deadlock
#endif /*VS_SIM_EN*/

#if VS_SIM_EN
	pBMUSQCmdPTR->ubTargetID = BMU_NOBLK_ID;
	pBMUSQCmdPTR->ubEventTargetID = BMU_BLK_ID;
	lbna.uwLBNA = (U16)pBMUSQCmdPTR->uwPBAddress;
	while (BMU_CMD_STATUS_SUCCESS != ubBmuCmdApiAddEvent((U8)pBMUSQCmdPTR->ubTargetID, (U16)pBMUSQCmdPTR->ubTagID, (U8)ubOption, (U8)(!pBMUSQCmdPTR->btNoCQ), lbna, (U8)pBMUSQCmdPTR->ubEventTag, NULL));
#endif /*VS_SIM_EN*/
	M_DB_TRIGGER_WRITE_CNT(DB_BMU_NOBLK_SQ, 1);
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_SQ, gDBQueueCnt.QueueCnt[(DB_BMU_NOBLK_SQ)] ) == M_DB_GET_WPTR((DB_BMU_NOBLK_SQ)));
	while (M_DB_GET_RD_CNT(DB_BMU_NOBLK_CQ) < (uwCQRDCNT + 1));


#if VS_SIM_EN
	memset(pBMUCmdResult, 0, sizeof(BMUCmdResult_t));
	pBMUCQPTR = (BmuCmdStatus_t *)ulCQWPTR;
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, pBMUCQPTR->AddEvent_Sts_t.ubStatus == 0);
	pBMUCmdResult->BMUAddEventRst.ubOpcode = pBMUCQPTR->AddEvent_Sts_t.ubOpcode;
	pBMUCmdResult->BMUAddEventRst.ubResult = pBMUCQPTR->AddEvent_Sts_t.ubStatus;
	pBMUCmdResult->BMUAddEventRst.ubTagID = (U8)pBMUCQPTR->AddEvent_Sts_t.uwTagID;//size not the same
	pBMUCmdResult->BMUAddEventRst.btNoCQ = (!((pBMUCQPTR->AddEvent_Sts_t.uwTagID & 0x8000) >> 15));
	pBMUCmdResult->BMUAddEventRst.uwPBAddress = (pBMUCQPTR->AddEvent_Sts_t.LBNA.B.uwPoolOffset);
	pBMUCmdResult->BMUAddEventRst.ubEventTag = pBMUCQPTR->AddEvent_Sts_t.ubEventID;
	pBMUCmdResult->BMUAddEventRst.ubTargetID = pBMUCQPTR->AddEvent_Sts_t.ubSrcTagID;
#else /*VS_SIM_EN*/
	pBMUCQPTR = (BMUCmdResult_t *)ulCQWPTR;
	pBMUCmdResult->All = pBMUCQPTR->All;
#endif /*VS_SIM_EN*/

	if (pBMUCmdResult->BMUAddEventRst.ubOpcode == BMU_ADD_EVENT_CMD) {
#if VS_SIM_EN
		vQueuePop (CMD_CQ_GET_QUEUE_PTR (pBMUCmdResult->BMURst.ubTargetID), pBMUCQPTR);
#endif /*VS_SIM_EN*/
		M_DB_TRIGGER_READ_CNT(DB_BMU_NOBLK_CQ, 1);
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_CQ, gDBQueueCnt.QueueCnt[(DB_BMU_NOBLK_CQ)] ) == M_DB_GET_RPTR((DB_BMU_NOBLK_CQ)));
	}
	else {
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);//DB_BMU_NOBLK_CQ setting depth = 1
	}

	//CQ Is occupied by blocking
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, 0 == M_DB_CHECK_FULL(DB_BMU_NOBLK_CQ));

	if (pBMUCmdResult->BMUAddEventRst.ubResult == BMU_CMD_STATUS_RSC_FAIL) {
		FTLPushTagPool(BMU_TAG_POOL_ID, ubEventTagID);
		return BMU_CMD_STATUS_FAIL;
	}
	else {
		puoCmdTable = &gpuoCmdTableMgr[BMU_CMD_TABLE_ID][ubEventTagID];
		puoCmdTable->ulCmdFuncPTR = ulCallBackAddr;
		puoCmdTable->ulData.uwPrivate = uwCallBackData;
	}
	return BMU_CMD_STATUS_SUCCESS;

}
/******************************************
	BMU CMD API:Validate_Allocate_LB (OP Code = 16)
	reference HW api : ubBmuCmdApiValidateAllocateLB
******************************************/
U8 BMUAPICmdValidateAllocateLB(U8 btNoCQ, U16 uwPBAddr, U8 ubLBID, U32 ulLCA, U8 btZeroFlag, U8 btCmdEndFlag, U8 btFullFlag, U8 ubValidBitmap, U8 ubZInfo, U8 ubTimeoutValue, U32 ulE3D4K, U8 ubE3D4KFlag, BMUCmdResult_t *pBMUCmdResult)
{
#if(!BURNER_MODE_EN)
#if VS_SIM_EN
	LBNA_t lbna;
	BmuCmdStatus_t *pBMUCQPTR;
#else /*VS_SIM_EN*/
	BMUCmdResult_t *pBMUCQPTR = NULL;
#endif /*VS_SIM_EN*/
	U16 uwCQRDCNT;
	U32 ulCQWPTR;
	U32 *pBMUSQCmdPTR = (U32 *)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_SQ, gDBQueueCnt.B.uwBMUNonBlockingSQCnt); //Get WPTR (FW maintain)

	if (M_DB_CHECK_FULL(DB_BMU_NOBLK_CQ) || M_DB_CHECK_FULL(DB_BMU_NOBLK_SQ)) {
		return BMU_CMD_STATUS_FAIL;
	}

	*pBMUSQCmdPTR = ((((U32)BMU_VALIDATE_ALLOCATE_LB_CMD & BMU_CMD_VALIDATE_ALLOCATE_LB_OPCODE_MASK) << BMU_CMD_VALIDATE_ALLOCATE_LB_OPCODE_SHIFT)
			| (((U32)BMU_NOBLK_CQ_ID & BMU_CMD_VALIDATE_ALLOCATE_LB_TARGET_ID_MASK) << BMU_CMD_VALIDATE_ALLOCATE_LB_TARGET_ID_SHIFT)
			| (((U32)ubZInfo & BMU_CMD_VALIDATE_ALLOCATE_LB_ZINFO_MASK) << BMU_CMD_VALIDATE_ALLOCATE_LB_ZINFO_SHIFT)
			| (((U32)ubTimeoutValue & BMU_CMD_VALIDATE_ALLOCATE_LB_TIMEOUT_MASK) << BMU_CMD_VALIDATE_ALLOCATE_LB_TIMEOUT_SHIFT)
			| (((U32)btZeroFlag & BMU_CMD_VALIDATE_ALLOCATE_LB_ZERO_MASK) << BMU_CMD_VALIDATE_ALLOCATE_LB_ZERO_SHIFT)
			| (((U32)btCmdEndFlag & BMU_CMD_VALIDATE_ALLOCATE_LB_CMD_END_MASK) << BMU_CMD_VALIDATE_ALLOCATE_LB_CMD_END_SHIFT)
			| (((U32)btNoCQ & BMU_CMD_VALIDATE_ALLOCATE_LB_NOCQ_MASK) << BMU_CMD_VALIDATE_ALLOCATE_LB_NOCQ_SHIFT)
			| (((U32)DEFAULT_TAG_ID & BMU_CMD_VALIDATE_ALLOCATE_LB_TAG_ID_MASK) << BMU_CMD_VALIDATE_ALLOCATE_LB_TAG_ID_SHIFT));
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = ((((U32)uwPBAddr & BMU_CMD_VALIDATE_ALLOCATE_LB_PB_ADDR_MASK) << BMU_CMD_VALIDATE_ALLOCATE_LB_PB_ADDR_SHIFT)
			| (((U32)ubLBID & BMU_CMD_VALIDATE_ALLOCATE_LB_LB_ID_MASK) << BMU_CMD_VALIDATE_ALLOCATE_LB_LB_ID_SHIFT)
			| (((U32)ubE3D4KFlag & BMU_CMD_VALIDATE_ALLOCATE_LB_E3D4K_FLAG_MASK) << BMU_CMD_VALIDATE_ALLOCATE_LB_E3D4K_FLAG_SHIFT)
			| (((U32)btFullFlag & BMU_CMD_VALIDATE_ALLOCATE_LB_FULL_MASK) << BMU_CMD_VALIDATE_ALLOCATE_LB_FULL_SHIFT)
			| (((U32)ubValidBitmap & BMU_CMD_VALIDATE_ALLOCATE_LB_VALIDBMP_MASK) << BMU_CMD_VALIDATE_ALLOCATE_LB_VALIDBMP_SHIFT));
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = ((U32)ulLCA << BMU_CMD_VALIDATE_ALLOCATE_LB_LCA_SHIFT);
	pBMUSQCmdPTR++;
#if PS5021_EN
	*pBMUSQCmdPTR = ((((U32)ulE3D4K & BMU_CMD_VALIDATE_ALLOCATE_LB_E3D4K_MASK) << BMU_CMD_VALIDATE_ALLOCATE_LB_E3D4K_SHIFT)
			| ((((U32)uwPBAddr >> BMU_PB_BIT_EXTEND_START) & BMU_CMD_VALIDATE_ALLOCATE_LB_PB_ADDR2_MASK) << BMU_CMD_VALIDATE_ALLOCATE_LB_PB_ADDR2_SHIFT));
#else /* PS5021_EN */
	*pBMUSQCmdPTR = (((U32)ulE3D4K & BMU_CMD_VALIDATE_ALLOCATE_LB_E3D4K_MASK) << BMU_CMD_VALIDATE_ALLOCATE_LB_E3D4K_SHIFT);
#endif /* PS5021_EN */

	/*
		new struct:
		U32 btQOP: 1;
		U32 btPEOP: 1;
		U32 ubPCACRC: 8;

	*/

	uwCQRDCNT = M_DB_GET_RD_CNT(DB_BMU_NOBLK_CQ);

#if USE_FW_DB
	ulCQWPTR = (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_CQ, gDBQueueCnt.B.uwBMUCQ2Cnt); //Get WPTR (FW maintain)
#else
	ulCQWPTR = M_DB_GET_WPTR(DB_BMU_NOBLK_CQ);
#endif
#if VS_SIM_EN
	pBMUSQCmdPTR->ubTargetID = BMU_NOBLK_ID;
	lbna.uwLBNA = (U16)pBMUSQCmdPTR->uwPBAddress;
	while (BMU_CMD_STATUS_SUCCESS != ubBmuCmdApiValidateAllocateLB ((U8)pBMUSQCmdPTR->ubTargetID, (U16)pBMUSQCmdPTR->ubTagID, (U8)(!pBMUSQCmdPTR->btNoCQ), pBMUSQCmdPTR->ulLCA, (U8)pBMUSQCmdPTR->btZero, (U8)pBMUSQCmdPTR->btCmdEnd, (U8)pBMUSQCmdPTR->btFull, (U8)pBMUSQCmdPTR->ubVldBitmap, (U8)pBMUSQCmdPTR->ubTimeout,
			(U8)pBMUSQCmdPTR->ubLBID, lbna, (U8)pBMUSQCmdPTR->ubZinfo, pBMUSQCmdPTR->ulE3D4K, (U8)pBMUSQCmdPTR->btE3D4KFlag, NULL));
#endif /*VS_SIM_EN*/
	M_DB_TRIGGER_WRITE_CNT(DB_BMU_NOBLK_SQ, 1);
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_SQ, gDBQueueCnt.QueueCnt[(DB_BMU_NOBLK_SQ)] ) == M_DB_GET_WPTR((DB_BMU_NOBLK_SQ)));

#if VS_SIM_EN
	while (M_DB_GET_RD_CNT(DB_BMU_NOBLK_CQ) < (uwCQRDCNT + 1));

	memset(pBMUCmdResult, 0, sizeof(BMUCmdResult_t));
	pBMUCQPTR = (BmuCmdStatus_t *)ulCQWPTR;

	pBMUCmdResult->BMUVldAllocateRst.ubOpcode = pBMUCQPTR->ValidateAllocateLB_Sts_t.ubOpcode;
	pBMUCmdResult->BMUVldAllocateRst.ubResult = pBMUCQPTR->ValidateAllocateLB_Sts_t.ubStatus;
	pBMUCmdResult->BMUVldAllocateRst.ubTargetID = pBMUCQPTR->ValidateAllocateLB_Sts_t.ubSrcTagID;
	//pBMUCmdResult->BMUVldAllocateRst.btQOP =;
	pBMUCmdResult->BMUVldAllocateRst.ubZinfo = pBMUCQPTR->ValidateAllocateLB_Sts_t.ubZByte;
	pBMUCmdResult->BMUVldAllocateRst.ubTimeout = pBMUCQPTR->ValidateAllocateLB_Sts_t.ubTimeoutValue;
	pBMUCmdResult->BMUVldAllocateRst.btZero = pBMUCQPTR->ValidateAllocateLB_Sts_t.ubZeroFlag;
	pBMUCmdResult->BMUVldAllocateRst.btCmdEnd = pBMUCQPTR->ValidateAllocateLB_Sts_t.ubCommandEndFlag;
	//pBMUCmdResult->BMUVldAllocateRst.btPEOP = ;
	pBMUCmdResult->BMUVldAllocateRst.btNoCQ = (!((pBMUCQPTR->ValidateAllocateLB_Sts_t.uwTagID & 0x8000) >> 15));
	pBMUCmdResult->BMUVldAllocateRst.ubTagID = (U8)pBMUCQPTR->ValidateAllocateLB_Sts_t.uwTagID;//size not the same
	pBMUCmdResult->BMUVldAllocateRst.uwPBAddress = (pBMUCQPTR->ValidateAllocateLB_Sts_t.LBNA.B.uwPoolOffset);
	pBMUCmdResult->BMUVldAllocateRst.uwLBOffset = pBMUCQPTR->ValidateAllocateLB_Sts_t.uwLogicalBufferOffset;
	pBMUCmdResult->BMUVldAllocateRst.ubLBID = (pBMUCQPTR->ValidateAllocateLB_Sts_t.ubLogicalBufferID & 0x07);
	pBMUCmdResult->BMUVldAllocateRst.btE3D4KFlag = pBMUCQPTR->ValidateAllocateLB_Sts_t.ubE3D4KFlag;
	pBMUCmdResult->BMUVldAllocateRst.btFull = pBMUCQPTR->ValidateAllocateLB_Sts_t.ubFullFlag;
	pBMUCmdResult->BMUVldAllocateRst.ubVldBitmap = (U8)pBMUCQPTR->ValidateAllocateLB_Sts_t.ubValidBitmap;
	pBMUCmdResult->BMUVldAllocateRst.ulLCA = pBMUCQPTR->ValidateAllocateLB_Sts_t.ulLCA;

	if (pBMUCmdResult->BMUVldAllocateRst.ubOpcode == BMU_VALIDATE_ALLOCATE_LB_CMD) {

		vQueuePop (CMD_CQ_GET_QUEUE_PTR (pBMUCmdResult->BMURst.ubTargetID), pBMUCQPTR);
		M_DB_TRIGGER_READ_CNT(DB_BMU_NOBLK_CQ, 1);
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_CQ, gDBQueueCnt.QueueCnt[(DB_BMU_NOBLK_CQ)] ) == M_DB_GET_RPTR((DB_BMU_NOBLK_CQ)));
	}
	else {
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);//DB_BMU_NOBLK_CQ setting depth = 1
	}
#else /*VS_SIM_EN*/
	if (BMU_CMD_NEED_CQ == btNoCQ) {
		while (M_DB_GET_RD_CNT(DB_BMU_NOBLK_CQ) < (uwCQRDCNT + 1));

		pBMUCQPTR = (BMUCmdResult_t *)ulCQWPTR;
		pBMUCmdResult->All = pBMUCQPTR->All;
		if (pBMUCmdResult->BMUVldSizeRst.ubOpcode == BMU_VALIDATE_ALLOCATE_LB_CMD) {
			M_DB_TRIGGER_READ_CNT(DB_BMU_NOBLK_CQ, 1);
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_CQ, gDBQueueCnt.QueueCnt[(DB_BMU_NOBLK_CQ)] ) == M_DB_GET_RPTR((DB_BMU_NOBLK_CQ)));
		}
		else {
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);//DB_BMU_NOBLK_CQ setting depth = 1
		}
	}
#endif /*VS_SIM_EN*/

	//CQ Is occupied by blocking
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, 0 == M_DB_CHECK_FULL(DB_BMU_NOBLK_CQ));
#endif /* !BURNER_MODE_EN */
	return BMU_CMD_STATUS_SUCCESS;
}


/******************************************
	BMU CMD API:Validate_Size (OP Code = 17)
	reference HW api : ubBmuCmdApiValidateSize
******************************************/
U8 BMUAPICmdValidateSize(U8 btNoCQ, U16 uwPBAddr, U8 ubLBID, U16 uwLBOffset, U8 ubSize, U32 ulLCA, U8 btRCLOP, U8 btStreamID0, U8 btZeroFlag, U8 btCmdEndFlag, U8 btFullFlag, U8 ubOperator, U8 ubValidBitmap, U8 ubZInfo, BMUCmdResult_t *pBMUCmdResult)
{
#if VS_SIM_EN
	LBNA_t lbna;
	BmuCmdStatus_t *pBMUCQPTR;
#else /*VS_SIM_EN*/
	BMUCmdResult_t *pBMUCQPTR = NULL;
#endif /*VS_SIM_EN*/
	U16 uwCQRDCNT;
	U32 ulCQWPTR;
	U32 *pBMUSQCmdPTR = (U32 *)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_SQ, gDBQueueCnt.B.uwBMUNonBlockingSQCnt); //Get WPTR (FW maintain)

	if (M_DB_CHECK_FULL(DB_BMU_NOBLK_CQ) || M_DB_CHECK_FULL(DB_BMU_NOBLK_SQ)) {
		return BMU_CMD_STATUS_FAIL;
	}

	*pBMUSQCmdPTR = ((((U32)BMU_VALIDATE_SIZE_CMD & BMU_CMD_VALIDATE_SIZE_OPCODE_MASK) << BMU_CMD_VALIDATE_SIZE_OPCODE_SHIFT)
			| (((U32)BMU_NOBLK_CQ_ID & BMU_CMD_VALIDATE_SIZE_TARGET_ID_MASK) << BMU_CMD_VALIDATE_SIZE_TARGET_ID_SHIFT)
			| (((U32)ubOperator & BMU_CMD_VALIDATE_SIZE_OPER_MASK) << BMU_CMD_VALIDATE_SIZE_OPER_SHIFT)
			| (((U32)ubZInfo & BMU_CMD_VALIDATE_SIZE_ZINFO_MASK) << BMU_CMD_VALIDATE_SIZE_ZINFO_SHIFT)
#if PS5021_EN
			| ((((U32)btStreamID0 >> BMU_STREAM_ID1_BIT_SHIFT) & BMU_CMD_VALIDATE_SIZE_STREAM_ID1_MASK) << BMU_CMD_VALIDATE_SIZE_STREAM_ID1_SHIFT)
#endif /* PS5021_EN */
			| (((U32)btRCLOP & BMU_CMD_VALIDATE_SIZE_RCLOP_MASK) << BMU_CMD_VALIDATE_SIZE_RCLOP_SHIFT)
			| (((U32)btStreamID0 & BMU_CMD_VALIDATE_SIZE_STREAM_ID0_MASK) << BMU_CMD_VALIDATE_SIZE_STREAM_ID0_SHIFT)
			| (((U32)btZeroFlag & BMU_CMD_VALIDATE_SIZE_ZERO_MASK) << BMU_CMD_VALIDATE_SIZE_ZERO_SHIFT)
			| (((U32)btCmdEndFlag & BMU_CMD_VALIDATE_SIZE_CMD_END_MASK) << BMU_CMD_VALIDATE_SIZE_CMD_END_SHIFT)
			| (((U32)btNoCQ & BMU_CMD_VALIDATE_SIZE_NOCQ_MASK) << BMU_CMD_VALIDATE_SIZE_NOCQ_SHIFT)
			| (((U32)DEFAULT_TAG_ID & BMU_CMD_VALIDATE_SIZE_TAG_ID_MASK) << BMU_CMD_VALIDATE_SIZE_TAG_ID_SHIFT));
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = ((((U32)uwPBAddr & BMU_CMD_VALIDATE_SIZE_PB_ADDR_MASK) << BMU_CMD_VALIDATE_SIZE_PB_ADDR_SHIFT)
			| (((U32)uwLBOffset & BMU_CMD_VALIDATE_SIZE_LB_OFFSET_MASK) << BMU_CMD_VALIDATE_SIZE_LB_OFFSET_SHIFT)
			| (((U32)ubLBID & BMU_CMD_VALIDATE_SIZE_LB_ID_MASK) << BMU_CMD_VALIDATE_SIZE_LB_ID_SHIFT)
			| (((U32)btFullFlag & BMU_CMD_VALIDATE_SIZE_FULL_MASK) << BMU_CMD_VALIDATE_SIZE_FULL_SHIFT)
			| (((U32)ubValidBitmap & BMU_CMD_VALIDATE_SIZE_VALIDBMP_MASK) << BMU_CMD_VALIDATE_SIZE_VALIDBMP_SHIFT));
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = ((U32)ulLCA << BMU_CMD_VALIDATE_SIZE_LCA_SHIFT);
	pBMUSQCmdPTR++;
#if PS5021_EN
	*pBMUSQCmdPTR = ((((U32)ubSize & BMU_CMD_VALIDATE_SIZE_SIZE_MASK) << BMU_CMD_VALIDATE_SIZE_SIZE_SHIFT)
			| ((((U32)uwPBAddr >> BMU_PB_BIT_EXTEND_START) & BMU_CMD_VALIDATE_SIZE_PB_ADDR2_MASK) << BMU_CMD_VALIDATE_SIZE_PB_ADDR2_SHIFT));
#else /* PS5021_EN */
	*pBMUSQCmdPTR = (((U32)ubSize & BMU_CMD_VALIDATE_SIZE_SIZE_MASK) << BMU_CMD_VALIDATE_SIZE_SIZE_SHIFT);
#endif /* PS5021_EN */

	uwCQRDCNT = M_DB_GET_RD_CNT(DB_BMU_NOBLK_CQ);
#if USE_FW_DB
	ulCQWPTR = (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_CQ, gDBQueueCnt.B.uwBMUCQ2Cnt); //Get WPTR (FW maintain)
#else
	ulCQWPTR = M_DB_GET_WPTR(DB_BMU_NOBLK_CQ);
#endif
#if VS_SIM_EN
	pBMUSQCmdPTR->ubTargetID = BMU_NOBLK_ID;
	lbna.uwLBNA = (U16)pBMUSQCmdPTR->uwPBAddress;
	while (BMU_CMD_STATUS_SUCCESS != ubBmuCmdApiValidateSize((U8)pBMUSQCmdPTR->ubTargetID, (U16)pBMUSQCmdPTR->ubTagID, (U8)(!pBMUSQCmdPTR->btNoCQ), (U8)pBMUSQCmdPTR->ubSize, (U8)pBMUSQCmdPTR->ubOper, pBMUSQCmdPTR->ulLCA, (U8)pBMUSQCmdPTR->btZero, (U8)pBMUSQCmdPTR->btCmdEnd, (U8)pBMUSQCmdPTR->btFull, (U8)pBMUSQCmdPTR->ubValidBitmap,
			(U16)pBMUSQCmdPTR->uwLBOffset, (U8)pBMUSQCmdPTR->ubLBID, lbna, (U8)pBMUSQCmdPTR->ubZinfo, NULL));
#endif /*VS_SIM_EN*/
	M_DB_TRIGGER_WRITE_CNT(DB_BMU_NOBLK_SQ, 1);
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_SQ, gDBQueueCnt.QueueCnt[(DB_BMU_NOBLK_SQ)] ) == M_DB_GET_WPTR((DB_BMU_NOBLK_SQ)));
	while (M_DB_GET_RD_CNT(DB_BMU_NOBLK_CQ) < (uwCQRDCNT + 1));

#if VS_SIM_EN
	memset(pBMUCmdResult, 0, sizeof(BMUCmdResult_t));
	pBMUCQPTR = (BmuCmdStatus_t *)ulCQWPTR;
	if (pBMUCQPTR->ValidateSize_Sts_t.ubOpcode == VALIDATE_SIZE_CMD) {
		pBMUCQPTR->ValidateSize_Sts_t.ubOpcode = BMU_VALIDATE_SIZE_CMD;//op code 23->17
	}
	pBMUCmdResult->BMUVldSizeRst.ubOpcode = pBMUCQPTR->ValidateSize_Sts_t.ubOpcode;
	pBMUCmdResult->BMUVldSizeRst.ubResult = pBMUCQPTR->ValidateSize_Sts_t.ubStatus;
	pBMUCmdResult->BMUVldSizeRst.ubOper = ((pBMUCQPTR->ValidateSize_Sts_t.ubOperatorHigh << 1) | (pBMUCQPTR->ValidateSize_Sts_t.ubOperatorLow));
	pBMUCmdResult->BMUVldSizeRst.ubVldBitmap = (U8)pBMUCQPTR->ValidateSize_Sts_t.ubValidBitmap;
	pBMUCmdResult->BMUVldSizeRst.ubTagID = (U8)pBMUCQPTR->ValidateSize_Sts_t.uwTagID;//size not the same
	pBMUCmdResult->BMUVldSizeRst.btNoCQ = (!((pBMUCQPTR->ValidateSize_Sts_t.uwTagID & 0x8000) >> 15));
	pBMUCmdResult->BMUVldSizeRst.ulLCA = pBMUCQPTR->ValidateSize_Sts_t.ulLCA;
	pBMUCmdResult->BMUVldSizeRst.btFull = pBMUCQPTR->ValidateSize_Sts_t.ubFullFlag;
	//pBMUCQPTR->ValidateSize_Sts_t.ubE3D4KFlag;
	pBMUCmdResult->BMUVldSizeRst.ubLBID = (pBMUCQPTR->ValidateSize_Sts_t.ubLogicalBufferID & 0x07);
	pBMUCmdResult->BMUVldSizeRst.uwLBOffset = pBMUCQPTR->ValidateSize_Sts_t.uwLogicalBufferOffset;
	pBMUCmdResult->BMUVldSizeRst.uwPBAddress = (pBMUCQPTR->ValidateSize_Sts_t.LBNA.B.uwPoolOffset);
	pBMUCmdResult->BMUVldSizeRst.ubDoneCount = pBMUCQPTR->ValidateSize_Sts_t.ubSize;
	pBMUCmdResult->BMUVldSizeRst.ubTargetID = pBMUCQPTR->ValidateSize_Sts_t.ubSrcTagID;
	pBMUCmdResult->BMUVldSizeRst.ubZinfo = pBMUCQPTR->ValidateSize_Sts_t.ubZByte;


#else
	pBMUCQPTR = (BMUCmdResult_t *)ulCQWPTR;
	pBMUCmdResult->All = pBMUCQPTR->All;

#endif

	if (pBMUCmdResult->BMUVldSizeRst.ubOpcode == BMU_VALIDATE_SIZE_CMD) {
#if VS_SIM_EN
		vQueuePop (CMD_CQ_GET_QUEUE_PTR (pBMUCmdResult->BMURst.ubTargetID), pBMUCQPTR);
#endif /*VS_SIM_EN*/
		M_DB_TRIGGER_READ_CNT(DB_BMU_NOBLK_CQ, 1);
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_CQ, gDBQueueCnt.QueueCnt[(DB_BMU_NOBLK_CQ)] ) == M_DB_GET_RPTR((DB_BMU_NOBLK_CQ)));
	}
	else {
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);//DB_BMU_NOBLK_CQ setting depth = 1
	}

	//CQ Is occupied by blocking
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, 0 == M_DB_CHECK_FULL(DB_BMU_NOBLK_CQ));

	return BMU_CMD_STATUS_SUCCESS;
}

/*****************************************************
	BMU CMD API: Reserve LB (OP Code = 18)
	reference HW api: ubBmuCmdApiReserveLB
*****************************************************/
U8 BMUAPICmdReserveLB(U8 btNoCQ, U8 ubLBID, U8 ubSize, BMUCmdResult_t *pBMUCmdResult)
{
#if(!BURNER_MODE_EN)
#if VS_SIM_EN
	BmuCmdStatus_t *pBMUCQPTR;
#else /*VS_SIM_EN*/
	BMUCmdResult_t *pBMUCQPTR = NULL;
#endif /*VS_SIM_EN*/
	U16 uwCQRDCNT;
	U32 ulCQWPTR;
	U32 *pBMUSQCmdPTR = (U32 *)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_SQ, gDBQueueCnt.B.uwBMUNonBlockingSQCnt);
	if (M_DB_CHECK_FULL(DB_BMU_NOBLK_CQ) || M_DB_CHECK_FULL(DB_BMU_NOBLK_SQ)) {
		return BMU_CMD_STATUS_FAIL;
	}

	*pBMUSQCmdPTR = ((((U32)BMU_RESERVE_LB_CMD & BMU_CMD_RESERVE_LB_OPCODE_MASK) << BMU_CMD_RESERVE_LB_OPCODE_SHIFT)
			| (((U32)BMU_NOBLK_CQ_ID & BMU_CMD_RESERVE_LB_TARGET_ID_MASK) << BMU_CMD_RESERVE_LB_TARGET_ID_SHIFT)
			| (((U32)btNoCQ & BMU_CMD_RESERVE_LB_NOCQ_MASK) << BMU_CMD_RESERVE_LB_NOCQ_SHIFT)
			| (((U32)DEFAULT_TAG_ID & BMU_CMD_RESERVE_LB_TAG_ID_MASK) << BMU_CMD_RESERVE_LB_TAG_ID_SHIFT));
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = (((U32)ubLBID & BMU_CMD_RESERVE_LB_LB_ID_MASK) << BMU_CMD_RESERVE_LB_LB_ID_SHIFT);
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = 0;
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = (((U32)ubSize & BMU_CMD_RESERVE_LB_SIZE_MASK) << BMU_CMD_RESERVE_LB_SIZE_SHIFT);

	uwCQRDCNT = M_DB_GET_RD_CNT(DB_BMU_NOBLK_CQ);
#if USE_FW_DB
	ulCQWPTR = (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_CQ, gDBQueueCnt.B.uwBMUCQ2Cnt); //Get WPTR (FW maintain)
#else
	ulCQWPTR = M_DB_GET_WPTR(DB_BMU_NOBLK_CQ);
#endif
#if VS_SIM_EN
	pBMUSQCmdPTR->ubTargetID = BMU_NOBLK_ID;
	while (BMU_CMD_STATUS_SUCCESS != ubBmuCmdApiReserveLB((U8)pBMUSQCmdPTR->ubTargetID, (U16)pBMUSQCmdPTR->ubTagID, (U8)(!pBMUSQCmdPTR->btNoCQ), (U8)pBMUSQCmdPTR->ubSize, (U8)pBMUSQCmdPTR->ubLBID, BMU_CMD_TIME_INFINITE, NULL));
#endif /*VS_SIM_EN*/
	M_DB_TRIGGER_WRITE_CNT(DB_BMU_NOBLK_SQ, 1);
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_SQ, gDBQueueCnt.QueueCnt[(DB_BMU_NOBLK_SQ)] ) == M_DB_GET_WPTR((DB_BMU_NOBLK_SQ)));
	while (M_DB_GET_RD_CNT(DB_BMU_NOBLK_CQ) < (uwCQRDCNT + 1));


#if VS_SIM_EN
	memset(pBMUCmdResult, 0, sizeof(BMUCmdResult_t));
	pBMUCQPTR = (BmuCmdStatus_t *)ulCQWPTR;
	if (pBMUCQPTR->ReserveLB_Sts_t.ubOpcode == Reserve_LB) {
		pBMUCQPTR->ReserveLB_Sts_t.ubOpcode = BMU_RESERVE_LB_CMD;//op code 20->18
	}
	pBMUCmdResult->BMUReserveLBRst.ubOpcode = pBMUCQPTR->ReserveLB_Sts_t.ubOpcode;
	pBMUCmdResult->BMUReserveLBRst.ubResult = pBMUCQPTR->ReserveLB_Sts_t.ubStatus;
	pBMUCmdResult->BMUReserveLBRst.ubDoneCount = pBMUCQPTR->ReserveLB_Sts_t.ubSize;
	pBMUCmdResult->BMUReserveLBRst.ubTagID = (U8)pBMUCQPTR->ReserveLB_Sts_t.uwTagID;//size not the same
	pBMUCmdResult->BMUReserveLBRst.btNoCQ = (!((pBMUCQPTR->ReserveLB_Sts_t.uwTagID & 0x8000) >> 15));
	//(pBMUCQPTR->ReserveLB_Sts_t.LBNA.B.uwPoolOffset);
	//pBMUCQPTR->ReserveLB_Sts_t.ubTimeoutValue;
	pBMUCmdResult->BMUReserveLBRst.ubLBID = (pBMUCQPTR->ReserveLB_Sts_t.ubLogicalBufferID & 0x07);
	pBMUCmdResult->BMUReserveLBRst.uwLBOffset = pBMUCQPTR->ReserveLB_Sts_t.uwLogicalBufferOffset;
	pBMUCmdResult->BMUReserveLBRst.ubTargetID = pBMUCQPTR->ReserveLB_Sts_t.ubSrcTagID;

#else /*VS_SIM_EN*/
	pBMUCQPTR = (BMUCmdResult_t *)ulCQWPTR;
	pBMUCmdResult->All = pBMUCQPTR->All;

#endif /*VS_SIM_EN*/

	if (pBMUCmdResult->BMUVldSizeRst.ubOpcode == BMU_RESERVE_LB_CMD) {
#if VS_SIM_EN
		vQueuePop (CMD_CQ_GET_QUEUE_PTR (pBMUCmdResult->BMURst.ubTargetID), pBMUCQPTR);
#endif /*VS_SIM_EN*/
		M_DB_TRIGGER_READ_CNT(DB_BMU_NOBLK_CQ, 1);
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_CQ, gDBQueueCnt.QueueCnt[(DB_BMU_NOBLK_CQ)] ) == M_DB_GET_RPTR((DB_BMU_NOBLK_CQ)));
	}
	else {
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);//DB_BMU_NOBLK_CQ setting depth = 1
	}

	//CQ Is occupied by blocking
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, 0 == M_DB_CHECK_FULL(DB_BMU_NOBLK_CQ));
#endif/* !BURNER_MODE_EN */
	return BMU_CMD_STATUS_SUCCESS;
}

/*****************************************************
	BMU CMD API: Allocate PB Link (OP Code = 19)
	reference HW api: ubBmuCmdApiAllocatePBLink
*****************************************************/
U8 BMUAPICmdAllocatePBLink(U8 btNoCQ, U8 ubTimeout, U8 ubLBID, U16 uwLBOffset, U8 ubSize, U32 ulLCA, CTAG_t CTAG, U8 ubStreamID, U8 btOption, U32 ulCallBackAddr, U16 uwCallBackData)
{
#if(!BURNER_MODE_EN)
	U32 *pBMUSQCmdPTR = (U32 *)M_DB_GET_QBODY_PTR( DB_BMU_BLK_SQ, gDBQueueCnt.B.uwBMUBlockingSQCnt);
	cmd_table_t *puoCmdTable = NULL;
	U8 ubTagID;

	if (M_DB_CHECK_FULL(DB_BMU_BLK_SQ)) {
		return BMU_CMD_STATUS_FAIL;
	}
	if (LB_ID_FW == ubLBID) {
		gST3CRAMDist.ubFWLB_AllocNum += ubSize;
	}
	ubTagID = (U8)FTLPopTagPool(BMU_TAG_POOL_ID);

	*pBMUSQCmdPTR = ((((U32)BMU_ALLOCATE_PB_LINK_CMD & BMU_CMD_ALLOCATE_PB_LINK_OPCODE_MASK) << BMU_CMD_ALLOCATE_PB_LINK_OPCODE_SHIFT)
			| (((U32)BMU_BLK_CQ_ID & BMU_CMD_ALLOCATE_PB_LINK_TARGET_ID_MASK) << BMU_CMD_ALLOCATE_PB_LINK_TARGET_ID_SHIFT)
			| (((U32)btOption & BMU_CMD_ALLOCATE_PB_LINK_OPTION_MASK) << BMU_CMD_ALLOCATE_PB_LINK_OPTION_SHIFT)
			| (((U32)ubTimeout & BMU_CMD_ALLOCATE_PB_LINK_TIMEOUT_MASK) << BMU_CMD_ALLOCATE_PB_LINK_TIMEOUT_SHIFT)
			| (((U32)btNoCQ & BMU_CMD_ALLOCATE_PB_LINK_NOCQ_MASK) << BMU_CMD_ALLOCATE_PB_LINK_NOCQ_SHIFT)
			| (((U32)ubTagID & BMU_CMD_ALLOCATE_PB_LINK_TAG_ID_MASK) << BMU_CMD_ALLOCATE_PB_LINK_TAG_ID_SHIFT));
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = ((((U32)uwLBOffset & BMU_CMD_ALLOCATE_PB_LINK_LB_OFFSET_MASK) << BMU_CMD_ALLOCATE_PB_LINK_LB_OFFSET_SHIFT)
			| (((U32)ubLBID & BMU_CMD_ALLOCATE_PB_LINK_LB_ID_MASK) << BMU_CMD_ALLOCATE_PB_LINK_LB_ID_SHIFT)
			| (((U32)CTAG & BMU_CMD_ALLOCATE_PB_LINK_CTAG_MASK) << BMU_CMD_ALLOCATE_PB_LINK_CTAG_SHIFT));
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = ((U32)ulLCA << BMU_CMD_ALLOCATE_PB_LINK_LCA_SHIFT);
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = ((((U32)ubSize & BMU_CMD_ALLOCATE_PB_LINK_SIZE_MASK) << BMU_CMD_ALLOCATE_PB_LINK_SIZE_SHIFT)
#if PS5021_EN
			| (((U32)ubStreamID & BMU_CMD_ALLOCATE_PB_LINK_STREAM_ID_MASK) << BMU_CMD_ALLOCATE_PB_LINK_STREAM_ID_SHIFT)
			| ((((U32)CTAG >> BMU_CTAG_BIT_EXTEND_START) & BMU_CMD_ALLOCATE_PB_LINK_CTAG2_MASK) << BMU_CMD_ALLOCATE_PB_LINK_CTAG2_SHIFT));
#else /* PS5021_EN */
			| (((U32)ubStreamID & BMU_CMD_ALLOCATE_PB_LINK_STREAM_ID_MASK) << BMU_CMD_ALLOCATE_PB_LINK_STREAM_ID_SHIFT));
#endif /* PS5021_EN */

#if VS_SIM_EN
	pBMUSQCmdPTR->ubTargetID = BMU_BLK_ID;
	while (BMU_CMD_STATUS_SUCCESS !=  ubBmuCmdApiAllocatePBLink ((U8)pBMUSQCmdPTR->ubTargetID, (U16)pBMUSQCmdPTR->ubTagID, (U8)pBMUSQCmdPTR->ubSize, (U8)pBMUSQCmdPTR->ubCTag, (U8)pBMUSQCmdPTR->ubTimeout, pBMUSQCmdPTR->ulLCA, E11_BUFFER_ID, (U8)pBMUSQCmdPTR->ubLBID, (U16)pBMUSQCmdPTR->uwLBOffset, NULL));	//ubPoolID unknow
#endif /*VS_SIM_EN*/
	puoCmdTable = &gpuoCmdTableMgr[BMU_CMD_TABLE_ID][ubTagID];
	puoCmdTable->ulCmdFuncPTR = ulCallBackAddr;
	puoCmdTable->ulData.uwPrivate = uwCallBackData;
	M_DB_TRIGGER_WRITE_CNT(DB_BMU_BLK_SQ, 1);
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_BLK_SQ, gDBQueueCnt.QueueCnt[(DB_BMU_BLK_SQ)] ) == M_DB_GET_WPTR((DB_BMU_BLK_SQ)));
#endif/*!BURNER_MODE_EN */
	return BMU_CMD_STATUS_SUCCESS;
}

/******************************************
	BMU CMD API:WLB_Search (OP Code = 20)
	reference HW api : ubBmuCmdApiWLBSearch
******************************************/
U8 BMUAPICmdWLBSearch(U8 btNoCQ, U32 ulLCA, U8 ubLBID, U16 uwLBOffset, U8 btRule, U32 ulCallBackAddr, U16 uwCallBackData)
{
	U32 *pBMUSQCmdPTR = (U32 *)M_DB_GET_QBODY_PTR( DB_BMU_SEARCH_SQ, gDBQueueCnt.B.uwBMUSearchSQCnt); //Get WPTR (FW maintain)
	cmd_table_t *puoCmdTable = NULL;
	U8 ubTagID;

	if ( M_DB_CHECK_FULL(DB_BMU_SEARCH_SQ)) {
		return BMU_CMD_STATUS_FAIL;
	}
	ubTagID = (U8)FTLPopTagPool(BMU_TAG_POOL_ID);

	*pBMUSQCmdPTR = ((((U32)BMU_WLB_SEARCH_CMD & BMU_CMD_WLB_SEARCH_OPCODE_MASK) << BMU_CMD_WLB_SEARCH_OPCODE_SHIFT)
			| (((U32)BMU_BLK_CQ_ID & BMU_CMD_WLB_SEARCH_TARGET_ID_MASK) << BMU_CMD_WLB_SEARCH_TARGET_ID_SHIFT)
			| (((U32)btRule & BMU_CMD_WLB_SEARCH_OPTION_MASK) << BMU_CMD_WLB_SEARCH_OPTION_SHIFT)
			| (((U32)btNoCQ & BMU_CMD_WLB_SEARCH_NOCQ_MASK) << BMU_CMD_WLB_SEARCH_NOCQ_SHIFT)
			| (((U32)ubTagID & BMU_CMD_WLB_SEARCH_TAG_ID_MASK) << BMU_CMD_WLB_SEARCH_TAG_ID_SHIFT));
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = ((((U32)uwLBOffset & BMU_CMD_WLB_SEARCH_LB_OFFSET_MASK) << BMU_CMD_WLB_SEARCH_LB_OFFSET_SHIFT)
			| (((U32)ubLBID & BMU_CMD_WLB_SEARCH_LB_ID_MASK) << BMU_CMD_WLB_SEARCH_LB_ID_SHIFT));
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = ((U32)ulLCA << BMU_CMD_WLB_SEARCH_LCA_SHIFT);
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = 0;
	//(FW x , HW v)

#if VS_SIM_EN
	pBMUSQCmdPTR->ubTargetID = BMU_BLK_ID;
	while (BMU_CMD_STATUS_SUCCESS != ubBmuCmdApiWLBSearch((U8)pBMUSQCmdPTR->ubTargetID, (U16)pBMUSQCmdPTR->ubTagID, (U8)(!pBMUSQCmdPTR->btNoCQ), pBMUSQCmdPTR->ulLCA, (U8)pBMUSQCmdPTR->ubLBID, (U16)pBMUSQCmdPTR->uwLBOffset,
			(U8)pBMUSQCmdPTR->btOption, NULL));
#endif /*VS_SIM_EN*/
	M_DB_TRIGGER_WRITE_CNT(DB_BMU_SEARCH_SQ, 1);
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_SEARCH_SQ, gDBQueueCnt.QueueCnt[(DB_BMU_SEARCH_SQ)] ) == M_DB_GET_WPTR((DB_BMU_SEARCH_SQ)));
	puoCmdTable = &gpuoCmdTableMgr[BMU_CMD_TABLE_ID][ubTagID];
	puoCmdTable->ulCmdFuncPTR = ulCallBackAddr;
	puoCmdTable->ulData.uwPrivate = uwCallBackData;

	return BMU_CMD_STATUS_SUCCESS;
}

/******************************************
	BMU CMD API:Validate (OP Code = 4)
	reference HW api :
******************************************/
U8 BMUAPICmdValidate (U8 btNoCQ, U16 uwPBAddr, U8 ubLBID, U16 uwLBOffset, U32 ulLCA, U8 btRCLOP, U8 btStreamID0, U8 btZeroFlag, U8 btCmdEndFlag, U8 btFullFlag, U8 ubOperator, U8 ubValidBitmap, U8 ubZInfo, U8 btPEOP, U8 btE3D4KFlag, U32 ulE3D4K, U8 ubPCACRC, BMUCmdResult_t *pBMUCmdResult)
{
#if(!BURNER_MODE_EN)
	U32 *pBMUSQCmdPTR = (U32 *)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_SQ, gDBQueueCnt.B.uwBMUNonBlockingSQCnt); //Get WPTR (FW maintain)
#if VS_SIM_EN
	LBNA_t lbna;
	BmuCmdStatus_t *puoBMUCQPTR;
#else
	BMUCmdResult_t *puoBMUCQPTR = NULL;
#endif
	U16 uwCQRDCNT;
	U32 ulCQWPTR ;
	if (M_DB_CHECK_FULL(DB_BMU_NOBLK_CQ) || M_DB_CHECK_FULL(DB_BMU_NOBLK_SQ)) {
		return BMU_CMD_STATUS_FAIL;
	}

	*pBMUSQCmdPTR = ((((U32)BMU_VALIDATE_CMD & BMU_CMD_VALIDATE_OPCODE_MASK) << BMU_CMD_VALIDATE_OPCODE_SHIFT)
			| (((U32)BMU_NOBLK_CQ_ID & BMU_CMD_VALIDATE_TARGET_ID_MASK) << BMU_CMD_VALIDATE_TARGET_ID_SHIFT)
			| (((U32)ubOperator & BMU_CMD_VALIDATE_OPER_MASK) << BMU_CMD_VALIDATE_OPER_SHIFT)
			| (((U32)ubZInfo & BMU_CMD_VALIDATE_ZINFO_MASK) << BMU_CMD_VALIDATE_ZINFO_SHIFT)
#if PS5021_EN
			| ((((U32)btStreamID0 >> BMU_STREAM_ID1_BIT_SHIFT) & BMU_CMD_VALIDATE_STREAM_ID1_MASK) << BMU_CMD_VALIDATE_STREAM_ID1_SHIFT)
#endif /* PS5021_EN */
			| (((U32)btRCLOP & BMU_CMD_VALIDATE_RCLOP_MASK) << BMU_CMD_VALIDATE_RCLOP_SHIFT)
			| (((U32)btStreamID0 & BMU_CMD_VALIDATE_STREAM_ID0_MASK) << BMU_CMD_VALIDATE_STREAM_ID0_SHIFT)
			| (((U32)btZeroFlag & BMU_CMD_VALIDATE_ZERO_MASK) << BMU_CMD_VALIDATE_ZERO_SHIFT)
			| (((U32)btCmdEndFlag & BMU_CMD_VALIDATE_CMD_END_MASK) << BMU_CMD_VALIDATE_CMD_END_SHIFT)
			| (((U32)btPEOP & BMU_CMD_VALIDATE_PEOP_MASK) << BMU_CMD_VALIDATE_PEOP_SHIFT)
			| (((U32)btNoCQ & BMU_CMD_VALIDATE_NOCQ_MASK) << BMU_CMD_VALIDATE_NOCQ_SHIFT)
			| (((U32)DEFAULT_TAG_ID & BMU_CMD_VALIDATE_TAG_ID_MASK) << BMU_CMD_VALIDATE_TAG_ID_SHIFT));
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = ((((U32)uwPBAddr & BMU_CMD_VALIDATE_PB_ADDR_MASK) << BMU_CMD_VALIDATE_PB_ADDR_SHIFT)
			| (((U32)uwLBOffset & BMU_CMD_VALIDATE_LB_OFFSET_MASK) << BMU_CMD_VALIDATE_LB_OFFSET_SHIFT)
			| (((U32)ubLBID & BMU_CMD_VALIDATE_LB_ID_MASK) << BMU_CMD_VALIDATE_LB_ID_SHIFT)
			| (((U32)btE3D4KFlag & BMU_CMD_VALIDATE_E3D4K_FLAG_MASK) << BMU_CMD_VALIDATE_E3D4K_FLAG_SHIFT)
			| (((U32)btFullFlag & BMU_CMD_VALIDATE_FULL_MASK) << BMU_CMD_VALIDATE_FULL_SHIFT)
			| (((U32)ubValidBitmap & BMU_CMD_VALIDATE_VALIDBMP_MASK) << BMU_CMD_VALIDATE_VALIDBMP_SHIFT));
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = ((U32)ulLCA << BMU_CMD_VALIDATE_LCA_SHIFT);
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = ((((U32)ulE3D4K & BMU_CMD_VALIDATE_E3D4K_MASK) << BMU_CMD_VALIDATE_E3D4K_SHIFT)
#if PS5021_EN
			| (((U32)ubPCACRC & BMU_CMD_VALIDATE_PCACRC_MASK) << BMU_CMD_VALIDATE_PCACRC_SHIFT)
			| ((((U32)uwPBAddr >> BMU_PB_BIT_EXTEND_START) & BMU_CMD_VALIDATE_PB_ADDR2_MASK) << BMU_CMD_VALIDATE_PB_ADDR2_SHIFT));
#else /* PS5021_EN */
			| (((U32)ubPCACRC & BMU_CMD_VALIDATE_PCACRC_MASK) << BMU_CMD_VALIDATE_PCACRC_SHIFT));
#endif /* PS5021_EN */

	uwCQRDCNT = M_DB_GET_RD_CNT(DB_BMU_NOBLK_CQ);
#if USE_FW_DB
	ulCQWPTR = (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_CQ, gDBQueueCnt.B.uwBMUCQ2Cnt); //Get WPTR (FW maintain)
#else
	ulCQWPTR = M_DB_GET_WPTR(DB_BMU_NOBLK_CQ);
#endif

#if VS_SIM_EN
	pBMUSQCmdPTR->ubTargetID = BMU_NOBLK_ID;
	lbna.B.uwPoolOffset = uwPBAddr;
	lbna.B.uwPoolID = 0;
	while (BMU_CMD_STATUS_SUCCESS != ubBmuCmdApiValidate((U8)pBMUSQCmdPTR->ubTargetID, (U16)pBMUSQCmdPTR->ubTagID,
			(U8)(!pBMUSQCmdPTR->btNoCQ), ubOperator, pBMUSQCmdPTR->ulLCA, btZeroFlag, btCmdEndFlag, btFullFlag, ubValidBitmap,
			(U16)pBMUSQCmdPTR->uwLBOffset, (U8)pBMUSQCmdPTR->ubLBID, lbna, ubZInfo, ulE3D4K, btE3D4KFlag, NULL));
#endif /*VS_SIM_EN*/
	M_DB_TRIGGER_WRITE_CNT(DB_BMU_NOBLK_SQ, 1);
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_SQ, gDBQueueCnt.QueueCnt[(DB_BMU_NOBLK_SQ)] ) == M_DB_GET_WPTR((DB_BMU_NOBLK_SQ)));
	while (1) {
		while (M_DB_GET_RD_CNT(DB_BMU_NOBLK_CQ) < (uwCQRDCNT + 1));

#if VS_SIM_EN
		puoBMUCQPTR = (BmuCmdStatus_t *)ulCQWPTR;
		pBMUCmdResult->BMUValidateRst.ubOpcode = puoBMUCQPTR->Validate_Sts_t.ubOpcode;
		pBMUCmdResult->BMUValidateRst.ubResult = puoBMUCQPTR->Validate_Sts_t.ubStatus;
		pBMUCmdResult->BMUValidateRst.ubTagID = (U8)puoBMUCQPTR->Validate_Sts_t.uwTagID;//size not the same
		pBMUCmdResult->BMUValidateRst.ubLBID = (puoBMUCQPTR->Validate_Sts_t.ubLogicalBufferID & 0x07);
		pBMUCmdResult->BMUValidateRst.ubTargetID = puoBMUCQPTR->Validate_Sts_t.ubSrcTagID;
#else /*VS_SIM_EN*/
		puoBMUCQPTR = (BMUCmdResult_t *)ulCQWPTR;
		pBMUCmdResult->All = puoBMUCQPTR->All;
#endif

		if (pBMUCmdResult->BMUValidateRst.ubOpcode == BMU_VALIDATE_CMD) {
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, pBMUCmdResult->BMUValidateRst.ubResult == 0);
			M_DB_TRIGGER_READ_CNT(DB_BMU_NOBLK_CQ, 1);
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_CQ, gDBQueueCnt.QueueCnt[(DB_BMU_NOBLK_CQ)] ) == M_DB_GET_RPTR((DB_BMU_NOBLK_CQ)));
#if VS_SIM_EN
			vQueuePop (CMD_CQ_GET_QUEUE_PTR (pBMUCmdResult->BMURst.ubTargetID), puoBMUCQPTR);
#endif
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, pBMUCmdResult->BMUValidateRst.ubResult == BMU_CMD_STATUS_SUCCESS);
			break;
		}
		else {
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);//DB_BMU_NOBLK_CQ setting depth = 1
		}
	}
#endif/*!BURNER_MODE_EN*/
	return BMU_CMD_STATUS_SUCCESS;

	//return pBmuCQ->CmdSts_t.ubResult;
}

/******************************************
        BMU CMD API: Link_LB (OP Code = 21)
        reference HW api : ???
******************************************/
U8 BMUAPICmdLinkLB (U8 btNoCQ, U16 uwPBAddr, U8 btQOP, U8 ubLBID, U16 uwLBOffset, BMUCmdResult_t *pBMUCmdResult)
{
	U32 *pBMUSQCmdPTR = (U32 *)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_SQ, gDBQueueCnt.B.uwBMUNonBlockingSQCnt); //Get WPTR (FW maintain)
	U16 uwCQRDCNT;
	U32 ulCQWPTR;
#if VS_SIM_EN
	LBNA_t LBNA;
	BmuCmdStatus_t *pBMUCQPTR;
#else
	BMUCmdResult_t *pBMUCQPTR = NULL;
#endif
	if (M_DB_CHECK_FULL(DB_BMU_NOBLK_SQ)) {
		return BMU_CMD_STATUS_FAIL;
	}

	*pBMUSQCmdPTR = ((((U32)BMU_LINK_LB_CMD & BMU_CMD_LINK_LB_OPCODE_MASK) << BMU_CMD_LINK_LB_OPCODE_SHIFT)
			| (((U32)BMU_NOBLK_CQ_ID & BMU_CMD_LINK_LB_TARGET_ID_MASK) << BMU_CMD_LINK_LB_TARGET_ID_SHIFT)
			| (((U32)btQOP & BMU_CMD_LINK_LB_QOP_MASK) << BMU_CMD_LINK_LB_QOP_SHIFT)
			| (((U32)btNoCQ & BMU_CMD_LINK_LB_NOCQ_MASK) << BMU_CMD_LINK_LB_NOCQ_SHIFT)
			| (((U32)DEFAULT_TAG_ID & BMU_CMD_LINK_LB_TAG_ID_MASK) << BMU_CMD_LINK_LB_TAG_ID_SHIFT));
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = ((((U32)uwPBAddr & BMU_CMD_LINK_LB_PB_ADDR_MASK) << BMU_CMD_LINK_LB_PB_ADDR_SHIFT)
			| (((U32)uwLBOffset & BMU_CMD_LINK_LB_LB_OFFSET_MASK) << BMU_CMD_LINK_LB_LB_OFFSET_SHIFT)
			| (((U32)ubLBID & BMU_CMD_LINK_LB_LB_ID_MASK) << BMU_CMD_LINK_LB_LB_ID_SHIFT));
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = 0;
	pBMUSQCmdPTR++;
#if PS5021_EN
	*pBMUSQCmdPTR = ((((U32)uwPBAddr >> BMU_PB_BIT_EXTEND_START) & BMU_CMD_LINK_LB_PB_ADDR2_MASK) << BMU_CMD_LINK_LB_PB_ADDR2_SHIFT);
#else /* PS5021_EN */
	*pBMUSQCmdPTR = 0;
#endif /* PS5021_EN */

	uwCQRDCNT = M_DB_GET_RD_CNT(DB_BMU_NOBLK_CQ);
#if USE_FW_DB
	ulCQWPTR = (U32) M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_CQ, gDBQueueCnt.B.uwBMUCQ2Cnt); //Get WPTR (FW maintain)
#else
	ulCQWPTR = M_DB_GET_WPTR(DB_BMU_NOBLK_CQ);
#endif
#if VS_SIM_EN
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
	LBNA.B.uwPoolID = 0;
	LBNA.B.uwPoolOffset = uwPBAddr;
	pBMUSQCmdPTR->ubTargetID = BMU_NOBLK_ID;
	while (BMU_CMD_STATUS_SUCCESS != ubBmuCmdApiAllocateLB((U8)pBMUSQCmdPTR->ubTargetID, (U16)pBMUSQCmdPTR->ubTagID, !btNoCQ, ubLBID, LBNA, pBMUSQCmdPTR->ubTimeout, NULL));
#endif
	M_DB_TRIGGER_WRITE_CNT(DB_BMU_NOBLK_SQ, 1);
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_SQ, gDBQueueCnt.QueueCnt[(DB_BMU_NOBLK_SQ)] ) == M_DB_GET_WPTR((DB_BMU_NOBLK_SQ)));
	while (M_DB_GET_RD_CNT(DB_BMU_NOBLK_CQ) < (uwCQRDCNT + 1));

#if VS_SIM_EN
	memset(pBMUCmdResult, 0, sizeof(BMUCmdResult_t));

	pBMUCQPTR = (BmuCmdStatus_t *)ulCQWPTR;

	pBMUCmdResult->BMULinkLBRst.ubOpcode = pBMUCQPTR->AllocateLB_Sts_t.ubOpcode;
	pBMUCmdResult->BMULinkLBRst.ubResult = pBMUCQPTR->AllocateLB_Sts_t.ubStatus;
	pBMUCmdResult->BMULinkLBRst.ubTagID = (U8)pBMUCQPTR->AllocateLB_Sts_t.uwTagID;    //size not the same
	pBMUCmdResult->BMULinkLBRst.ubTargetID = pBMUCQPTR->AllocateLB_Sts_t.ubSrcTagID;
#else /*VS_SIM_EN*/
	pBMUCQPTR = (BMUCmdResult_t *)ulCQWPTR;
	pBMUCmdResult->All = pBMUCQPTR->All;
#endif /*VS_SIM_EN*/

	if (pBMUCmdResult->BMULinkLBRst.ubOpcode == BMU_LINK_LB_CMD) {
#if VS_SIM_EN
		vQueuePop (CMD_CQ_GET_QUEUE_PTR (pBMUCmdResult->BMURst.ubTargetID), pBMUCQPTR);
#endif /*VS_SIM_EN*/
		M_DB_TRIGGER_READ_CNT(DB_BMU_NOBLK_CQ, 1);
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_CQ, gDBQueueCnt.QueueCnt[(DB_BMU_NOBLK_CQ)] ) == M_DB_GET_RPTR((DB_BMU_NOBLK_CQ)));
	}
	else {
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);//DB_BMU_NOBLK_CQ setting depth = 1
	}

	return BMU_CMD_STATUS_SUCCESS;
}



/******************************************
	BMU CMD API:Update_LL (OP Code = 22)
	reference HW api :
******************************************/
U8 BMUAPICmdUpdateLL (U8 btNoCQ, U8 ubLBID, U16 uwTargetValue, BMUCmdResult_t *pBMUCmdResult)
{
	U32 *pBMUSQCmdPTR = (U32 *)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_SQ, gDBQueueCnt.B.uwBMUNonBlockingSQCnt); //Get WPTR (FW maintain)
#if VS_SIM_EN
	BmuCmdStatus_t *puoBMUCQPTR;
#else /*VS_SIM_EN*/
	BMUCmdResult_t *puoBMUCQPTR = NULL;
#endif /*VS_SIM_EN*/
	U16 uwCQRDCNT;
	U32 ulCQWPTR ;
	//M_UART(BMU_, "Adjust LL ID=%x\n", ubLBID);
	if (M_DB_CHECK_FULL(DB_BMU_NOBLK_CQ) || M_DB_CHECK_FULL(DB_BMU_NOBLK_SQ)) {
		return BMU_CMD_STATUS_FAIL;
	}

	*pBMUSQCmdPTR = ((((U32)BMU_UPDATE_LL_CMD & BMU_CMD_UPDATE_LL_OPCODE_MASK) << BMU_CMD_UPDATE_LL_OPCODE_SHIFT)
			| (((U32)BMU_NOBLK_CQ_ID & BMU_CMD_UPDATE_LL_TARGET_ID_MASK) << BMU_CMD_UPDATE_LL_TARGET_ID_SHIFT)
			| (((U32)btNoCQ & BMU_CMD_UPDATE_LL_NOCQ_MASK) << BMU_CMD_UPDATE_LL_NOCQ_SHIFT)
			| (((U32)DEFAULT_TAG_ID & BMU_CMD_UPDATE_LL_TAG_ID_MASK) << BMU_CMD_UPDATE_LL_TAG_ID_SHIFT));
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = ((((U32)uwTargetValue & BMU_CMD_UPDATE_LL_TARGET_VALUE_MASK) << BMU_CMD_UPDATE_LL_TARGET_VALUE_SHIFT)
			| (((U32)ubLBID & BMU_CMD_UPDATE_LL_LB_ID_MASK) << BMU_CMD_UPDATE_LL_LB_ID_SHIFT));
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = 0;
	pBMUSQCmdPTR++;
#if PS5021_EN
	*pBMUSQCmdPTR = ((((U32)uwTargetValue >> BMU_PB_BIT_EXTEND_START) & BMU_CMD_UPDATE_LL_TARGET_VALUE2_MASK) << BMU_CMD_UPDATE_LL_TARGET_VALUE2_SHIFT);
#else /* PS5021_EN */
	*pBMUSQCmdPTR = 0;
#endif /* PS5021_EN */

	uwCQRDCNT = R16_DB[DB_BMU_NOBLK_CQ][R16_DB_RD_CNT];
#if USE_FW_DB
	ulCQWPTR = (U32) M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_CQ, gDBQueueCnt.B.uwBMUCQ2Cnt); //Get WPTR (FW maintain)
#else
	ulCQWPTR = M_DB_GET_WPTR(DB_BMU_NOBLK_CQ);
#endif
#if VS_SIM_EN
	pBMUSQCmdPTR->ubTargetID = BMU_NOBLK_ID;
	while (BMU_CMD_STATUS_SUCCESS != ubBmuCmdApiUpdateLL((U8)pBMUSQCmdPTR->ubTargetID, (U16)pBMUSQCmdPTR->ubTagID, (U8)(!pBMUSQCmdPTR->btNoCQ), (U16)pBMUSQCmdPTR->uwTargetValue, (U8)pBMUSQCmdPTR->ubLBID, NULL));
#endif /*VS_SIM_EN*/

	M_DB_TRIGGER_WRITE_CNT(DB_BMU_NOBLK_SQ, 1);
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_SQ, gDBQueueCnt.QueueCnt[(DB_BMU_NOBLK_SQ)] ) == M_DB_GET_WPTR((DB_BMU_NOBLK_SQ)));

	while (R16_DB[DB_BMU_NOBLK_CQ][R16_DB_RD_CNT] < (uwCQRDCNT + 1));

#if VS_SIM_EN
	memset(pBMUCmdResult, 0, sizeof(BMUCmdResult_t));
	puoBMUCQPTR = (BmuCmdStatus_t *)ulCQWPTR;
	if (puoBMUCQPTR->UpdateLL_Sts_t.ubOpcode == UPDATE_LL_CMD) {
		puoBMUCQPTR->UpdateLL_Sts_t.ubOpcode = BMU_UPDATE_LL_CMD;//op code 20->18
	}
	pBMUCmdResult->BMUUpdateLLRst.ubOpcode = puoBMUCQPTR->UpdateLL_Sts_t.ubOpcode;
	pBMUCmdResult->BMUUpdateLLRst.ubResult = puoBMUCQPTR->UpdateLL_Sts_t.ubStatus;
	pBMUCmdResult->BMUUpdateLLRst.ubTagID = (U8)puoBMUCQPTR->UpdateLL_Sts_t.uwTagID;//size not the same
	pBMUCmdResult->BMUUpdateLLRst.btNoCQ = (!((puoBMUCQPTR->UpdateLL_Sts_t.uwTagID & 0x8000) >> 15));
	pBMUCmdResult->BMUUpdateLLRst.ubLBID = (puoBMUCQPTR->UpdateLL_Sts_t.ubLogicalBufferID & 0x07);
	pBMUCmdResult->BMUUpdateLLRst.ubTargetID = puoBMUCQPTR->UpdateLL_Sts_t.ubSrcTagID;
	pBMUCmdResult->BMUUpdateLLRst.uwTargetValue = puoBMUCQPTR->UpdateLL_Sts_t.uwTargetValue;

#else /*VS_SIM_EN*/
	puoBMUCQPTR = (BMUCmdResult_t *)ulCQWPTR;
	pBMUCmdResult->All = puoBMUCQPTR->All;
#endif

	if (pBMUCmdResult->BMUUpdateLLRst.ubOpcode == BMU_UPDATE_LL_CMD) {
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, pBMUCmdResult->BMUUpdateLLRst.ubResult == 0);
#if VS_SIM_EN
		vQueuePop (CMD_CQ_GET_QUEUE_PTR (pBMUCmdResult->BMURst.ubTargetID), puoBMUCQPTR);
#endif /*VS_SIM_EN*/
		M_DB_TRIGGER_READ_CNT(DB_BMU_NOBLK_CQ, 1);
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_CQ, gDBQueueCnt.QueueCnt[(DB_BMU_NOBLK_CQ)] ) == M_DB_GET_RPTR((DB_BMU_NOBLK_CQ)));
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, pBMUCmdResult->BMUUpdateLLRst.ubResult == BMU_CMD_STATUS_SUCCESS);
	}
	else {
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);//DB_BMU_NOBLK_CQ setting depth = 1
	}

	return BMU_CMD_STATUS_SUCCESS;

	//return pBmuCQ->CmdSts_t.ubResult;
}

U16 BMUAPICmdSPORAllocate(U8 ubSize)
{
	BMUCmdResult_t *pBMUCQPTR = NULL;
#if(!BURNER_MODE_EN)
	U32 *pBMUSQCmdPTR = (U32 *)M_DB_GET_QBODY_PTR( DB_BMU_BLK_SQ, gDBQueueCnt.B.uwBMUBlockingSQCnt); //Get WPTR (FW maintain)
	U16 uwCQRDCNT;
	U32 ulCQWPTR;

	while (M_DB_CHECK_FULL(DB_BMU_BLK_SQ)) {
		BMUDelegateCmd();
	}

	*pBMUSQCmdPTR = ((((U32)BMU_ALLOCATE_CMD & BMU_CMD_ALLOCATE_OPCODE_MASK) << BMU_CMD_ALLOCATE_OPCODE_SHIFT)
			| (((U32)BMU_NOBLK_CQ_ID & BMU_CMD_ALLOCATE_TARGET_ID_MASK) << BMU_CMD_ALLOCATE_TARGET_ID_SHIFT)
			| (((U32)BMU_ALLOCATE_NOT_GET_E3D512 & BMU_CMD_ALLOCATE_OPTION_MASK) << BMU_CMD_ALLOCATE_OPTION_SHIFT)
			| (((U32)BMU_CMD_TIME_INFINITE & BMU_CMD_ALLOCATE_TIMEOUT_MASK) << BMU_CMD_ALLOCATE_TIMEOUT_SHIFT)
			| (((U32)BMU_CMD_NEED_CQ & BMU_CMD_ALLOCATE_NOCQ_MASK) << BMU_CMD_ALLOCATE_NOCQ_SHIFT)
			| (((U32)DEFAULT_TAG_ID & BMU_CMD_ALLOCATE_TAG_ID_MASK) << BMU_CMD_ALLOCATE_TAG_ID_SHIFT));
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = ((((U32)LB_ID_NRW & BMU_CMD_ALLOCATE_LB_ID_MASK) << BMU_CMD_ALLOCATE_LB_ID_SHIFT)
			| (((U32)ALLOCATE_NO_USE_CTAG & BMU_CMD_ALLOCATE_CTAG_MASK) << BMU_CMD_ALLOCATE_CTAG_SHIFT));
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = ((U32)ALLOCATE_NO_USE_LCA << BMU_CMD_ALLOCATE_LCA_SHIFT);
	pBMUSQCmdPTR++;
	*pBMUSQCmdPTR = ((((U32)ubSize & BMU_CMD_ALLOCATE_SIZE_MASK) << BMU_CMD_ALLOCATE_SIZE_SHIFT)
#if PS5021_EN
			| ((((U32)ALLOCATE_NO_USE_CTAG >> BMU_CTAG_BIT_EXTEND_START) & BMU_CMD_ALLOCATE_CTAG2_MASK) << BMU_CMD_ALLOCATE_CTAG2_SHIFT));
#else /* PS5021_EN */
			| (((U32)BMU_CMD_STREAM_ID_DEFAULT & BMU_CMD_ALLOCATE_STREAM_ID_MASK) << BMU_CMD_ALLOCATE_STREAM_ID_SHIFT));
#endif /* PS5021_EN */

	uwCQRDCNT = M_DB_GET_RD_CNT(DB_BMU_NOBLK_CQ);
#if USE_FW_DB
	ulCQWPTR = (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_CQ, gDBQueueCnt.B.uwBMUCQ2Cnt); //Get WPTR (FW maintain)
#else
	ulCQWPTR = M_DB_GET_WPTR(DB_BMU_NOBLK_CQ);
#endif

	M_DB_TRIGGER_WRITE_CNT(DB_BMU_BLK_SQ, 1);
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_BLK_SQ, gDBQueueCnt.QueueCnt[(DB_BMU_BLK_SQ)] ) == M_DB_GET_WPTR((DB_BMU_BLK_SQ)));
	while (M_DB_GET_RD_CNT(DB_BMU_NOBLK_CQ) < (uwCQRDCNT + 1));

	pBMUCQPTR = (BMUCmdResult_t *)ulCQWPTR;

	if (pBMUCQPTR->BMUAllocateRst.ubOpcode == BMU_ALLOCATE_CMD) {
		M_DB_TRIGGER_READ_CNT(DB_BMU_NOBLK_CQ, 1);
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_NOBLK_CQ, gDBQueueCnt.QueueCnt[(DB_BMU_NOBLK_CQ)] ) == M_DB_GET_RPTR((DB_BMU_NOBLK_CQ)));
	}
	else {
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
	}

	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, pBMUCQPTR->BMUAllocateRst.ubDoneCount == ubSize);
#endif/*!BURNER_MODE_EN */
	return pBMUCQPTR->BMUAllocateRst.uwLBOffset;
}

void BMUSimpleCallBack(BMUCmdResult_t *pBMUCmdResult)
{
	gubBMUCallBackDone = TRUE;
}
