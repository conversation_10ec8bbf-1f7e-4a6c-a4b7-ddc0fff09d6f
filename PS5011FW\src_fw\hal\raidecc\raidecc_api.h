
/****************************************************************************
*
*  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
*  All rights reserved
*
*  The content of this document is confidential and shall be applied
*  subject to the terms and conditions of the license agreement and
*  other applicable laws. Any unauthorized access, use or disclosure
*  of this document is strictly prohibited and may be punishable
*  under laws.
*
*  raidecc_api.h
*
*
*
****************************************************************************/

#ifndef RAIDECC_API_H_
#define RAIDECC_API_H_

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "aom/aom_api.h"
#include "raidecc_reg.h"

/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */
#define RAIDECC_MAX_PARITY_TAG_NUM							(64)
#define RAIDECC_STRAT_VIRTUAL_PB_NUM						(0)
#define RAIDECC_MAX_VIRTUAL_PB_NUM							(34)
#define RAIDECC_INVALID_VIRTUAL_PB_NUM                      (RAIDECC_MAX_VIRTUAL_PB_NUM)
#define RAIDECC_MAX_EXTERNAL_BUF_IDX						(31)
#define RAIDECC_MAX_INTERNAL_BUF_IDX						(3)
#define RAIDECC_INTERNAL_BUF_IDX_DEFAULT					(0xFF)


#define RAIDECC_PARITY_DATA_SIZE_PER_PLANE					(SIZE_4KB * gub4kEntrysPerPlane)
#define RAIDECC_PARITY_SPARE_SIZE_PER_FRAME					(SIZE_16B)
#define RAIDECC_PARITY_SPARE_SIZE_PER_PLANE					(RAIDECC_PARITY_SPARE_SIZE_PER_FRAME *  gub4kEntrysPerPlane)

/*
 * ---------------------------------------------------------------------------------------------------
 *  enum
 * ---------------------------------------------------------------------------------------------------
 */


/*
 * ---------------------------------------------------------------------------------------------------
 *  data types
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *   macros
 * ---------------------------------------------------------------------------------------------------
 */

//0x00
#define M_RAIDECC_CLEAR_PBUF_INIT()						do { (R32_RAIDECC[R32_RAIDECC_MODE_0] &= ~(RAIDECC_PBUF_INIT_MASK << RAIDECC_PBUF_INIT_SHIFT)); }while(0)
#define M_RAIDECC_CLEAR_PBUF_LOAD()						(R32_RAIDECC[R32_RAIDECC_MODE_0] &= ~(BIT(RAIDECC_PBUF_LOAD_SHIFT)))
#define M_RAIDECC_SET_BMU_FORCE_LOAD_EN()				(R32_RAIDECC[R32_RAIDECC_MODE_0] |= BIT(RAIDECC_BMU_FORCE_LOAD_EN_SHIFT))
#define M_RAIDECC_SET_PBUF_LOAD() 						(R32_RAIDECC[R32_RAIDECC_MODE_0] |= BIT(RAIDECC_PBUF_LOAD_SHIFT))
#define M_RAIDECC_CLEAR_BMU_FORCE_LOAD_EN()				(R32_RAIDECC[R32_RAIDECC_MODE_0] &= ~(BIT(RAIDECC_BMU_FORCE_LOAD_EN_SHIFT)))

//0x08
#define M_RAIDECC_CLEAR_PAGE_CNT()						(R32_RAIDECC[R32_RAIDECC_BP_CONF] &= ~(RAIDECC_PAGE_CNT_MASK << RAIDECC_PAGE_CNT_SHIFT))
#define M_RAIDECC_SET_PAGE_CNT(CNT)						(R32_RAIDECC[R32_RAIDECC_BP_CONF] |= ((CNT) << RAIDECC_PAGE_CNT_SHIFT))

//0x0C
#define M_RAIDECC_TRIGGER_FORCE_LOAD_FUNCTION()			(R32_RAIDECC[R32_RAIDECC_TRIG] |= BIT(RAIDECC_TRIG_ENC_SHIFT))
#define M_RAIDECC_WAIT_FORCE_LOAD_FUNCTION() 			do{ while ((R32_RAIDECC[R32_RAIDECC_TRIG] & BIT(RAIDECC_TRIG_ENC_SHIFT))); }while(0)

//0x18
#define M_RAIDECC_GET_DATA_BASE(DATA_BASE_ADDR)			do{ DATA_BASE_ADDR = R32_RAIDECC[R32_RAIDECC_DBASE_0]; }while(0)
#define M_RAIDECC_SET_DATA_BASE_ADDR(ADDR)				(R32_RAIDECC[R32_RAIDECC_DBASE_0] = (ADDR))

//0x1C
#define M_RAIDECC_GET_SPARE_BASE(SPARE_BASE_ADDR)		do{ SPARE_BASE_ADDR  = R32_RAIDECC[R32_RAIDECC_SBASE_0]; }while(0)
#define M_RAIDECC_SET_SPARE_BASE_ADDR(ADDR)   			(R32_RAIDECC[R32_RAIDECC_SBASE_0] = (ADDR))
#define M_RAIDECC_GET_SPARE_BASE_ADDR()					(R32_RAIDECC[R32_RAIDECC_SBASE_0])

//0x5C
#define M_RAIDECC_SET_PBUF_LOCK_SELECT(INTERNAL_BUF_IDX)	do{ \
																U32 ulValue; \
																ulValue = R32_RAIDECC[R32_PBUF_SEL_CFG]; \
																ulValue &= ~(PBUF_LOCK_SELECT_MASK << PBUF_LOCK_SELECT_SHIFT); \
																ulValue |= ((INTERNAL_BUF_IDX) & PBUF_LOCK_SELECT_MASK) << PBUF_LOCK_SELECT_SHIFT;\
																R32_RAIDECC[R32_PBUF_SEL_CFG] = ulValue; \
															}while(0)

#define M_RAIDECC_SET_PBUF_STATUS_SELECT(PBUF_IDX)		do{ \
															U32 ulValue; \
															ulValue = R32_RAIDECC[R32_PBUF_SEL_CFG]; \
															ulValue &= ~(PBUF_STATUS_SELECT_MASK << PBUF_STATUS_SELECT_SHIFT); \
															ulValue |= ((PBUF_IDX) & PBUF_STATUS_SELECT_MASK) << PBUF_STATUS_SELECT_SHIFT;\
															R32_RAIDECC[R32_PBUF_SEL_CFG] = ulValue; \
														}while(0)

#define M_RAIDECC_SET_PBUF_FORCE_SELECT(INTERNAL_BUF_IDX)	do{ \
																U32 ulValue; \
																ulValue = R32_RAIDECC[R32_PBUF_SEL_CFG]; \
																ulValue &= ~(PBUF_FORCE_SELECT_MASK << PBUF_FORCE_SELECT_SHIFT); \
																ulValue |= ((INTERNAL_BUF_IDX) & PBUF_FORCE_SELECT_MASK) << PBUF_FORCE_SELECT_SHIFT;\
																R32_RAIDECC[R32_PBUF_SEL_CFG] = ulValue; \
															}while(0)

//0x60

#define M_RAIDECC_SET_TAG_ENCODED_CNT(ENCODED_CNT)		do{\
															U32 ulValue;\
															R32_RAIDECC[R32_RAIDECC_TAG_CONFIG_0] &= ~(RAIDECC_TAG_PEC_MASK << RAIDECC_TAG_PEC_SHIFT); \
															ulValue = R32_RAIDECC[R32_RAIDECC_TAG_CONFIG_0]; \
															ulValue |= (((ENCODED_CNT) << RAIDECC_TAG_PEC_SHIFT) | (0xFF << RAIDECC_TAG_PRC_SHIFT)); \
															ulValue |= (TAG_PRC_DEFAULT << RAIDECC_TAG_PRC_SHIFT); \
															ulValue |= BIT(RAIDECC_TAG_VALID_SHIFT); \
															R32_RAIDECC[R32_RAIDECC_TAG_CONFIG_0] = ulValue; \
														}while(0)

#define M_RAIDECC_CLRAR_TAG_ENCODED_CNT(TAG)			do{\
															M_RAIDECC_SET_TAG_SELECT(TAG);\
															R32_RAIDECC[R32_RAIDECC_TAG_CONFIG_0] = (TAG);\
														}while(0)

#define M_RAIDECC_SET_READY_CNT_AND_READY_FLAG(READY_CNT)		do{\
															U32 ulValue;\
															ulValue = R32_RAIDECC[R32_RAIDECC_TAG_CONFIG_0]; \
															ulValue &= ~(RAIDECC_TAG_PRC_MASK << RAIDECC_TAG_PRC_SHIFT); \
															ulValue |= ((READY_CNT) << RAIDECC_TAG_PRC_SHIFT); \
															ulValue |= BIT(RAIDECC_TAG_PRF_SHIFT); \
															R32_RAIDECC[R32_RAIDECC_TAG_CONFIG_0] = ulValue; \
														}while(0)


//0x68
#define M_RAIDECC_TRIGGER_FORCE_SAVE_FUNCTION()			do{ R32_RAIDECC[R32_RAIDECC_PBUF_CTL] |= (FORCE_SAVE_RAIDECC_PBUF_MASK << FORCE_SAVE_RAIDECC_PBUF_SHIFT); }while(0)
#define M_RAIDECC_WAIT_FORCE_SAVE_FUNCTION_DONE()		do{ while(R32_RAIDECC[R32_RAIDECC_PBUF_CTL] & CHK_FORCE_SAVE_PBUF_STATUS); }while(0)
#define M_RAIDECC_SET_LOCK_RAIDECC_PBUF()				(R32_RAIDECC[R32_RAIDECC_PBUF_CTL] |= BIT(PBUF_LOCK_SELECT_SHIFT))
#define M_RAIDECC_CLEAR_LOCK_RAIDECC_PBUF()				(R32_RAIDECC[R32_RAIDECC_PBUF_CTL] &= ~BIT(PBUF_LOCK_SELECT_SHIFT))

//0x70
#define M_RAIDECC_CHECK_TAG_VALID_CONTROL_READ()		((R32_RAIDECC[R32_RAIDECC_TAG_VLD_CTL] >> RAIDECC_TAG_RD_SHIFT) & RAIDECC_TAG_RD_MASK)

//0x78
#define M_RAIDECC_SET_TAG_SELECT(TAG)					do { \
															R32_RAIDECC[R32_RAIDECC_TAG_SEL] &= ~(RAIDECC_TAG_SEL_MASK); \
															R32_RAIDECC[R32_RAIDECC_TAG_SEL] |= (TAG); \
															R32_RAIDECC[R32_RAIDECC_TAG_VLD_CTL] |= BIT(RAIDECC_TAG_RD_SHIFT); \
															while (M_RAIDECC_CHECK_TAG_VALID_CONTROL_READ()); \
														} while(0)
//0x80
#define M_RAIDECC_GET_PBUF_CONTROL_1_VALUE(REG_VALUE)	do{ REG_VALUE = R32_RAIDECC[R32_RAIDECC_PBUF_CTL_1]; }while(0)
#define M_RAIDECC_SET_PBUF_CONTROL_1_VALUE(REG_VALUE)	do{ R32_RAIDECC[R32_RAIDECC_PBUF_CTL_1] = (REG_VALUE); }while(0)
#define M_RAIDECC_IS_PB_NUM_VALID()						((R32_RAIDECC[R32_RAIDECC_PBUF_CTL_1] >> RAIDECC_PB_NUM_VLD_SHIFT) & RAIDECC_PB_NUM_VLD_MASK)
#define M_RAIDECC_GET_PBUF_TAG_NUM()					((R32_RAIDECC[R32_RAIDECC_PBUF_CTL_1] >> RAIDECC_PBUF_RAIDECC_TAG_NUM_SHIFT) & RAIDECC_PBUF_RAIDECC_TAG_NUM_MASK)
#define M_RAIDECC_GET_PB_NUM()                    			((R32_RAIDECC[R32_RAIDECC_PBUF_CTL_1] >> RAIDECC_RAIDECC_PB_NUM_SHIFT) & RAIDECC_RAIDECC_PB_NUM_MASK)

#define M_RAIDECC_FORCELOAD_SET_TAG_VIRTUALPB(TAG, VIRTUALPB)	do{ \
																	U32 ulValue; \
																	ulValue =  (BIT(RAIDECC_PBUF_STATUS_SHIFT) | BIT(RAIDECC_PB_NUM_VLD_SHIFT)); \
																	ulValue |= ((TAG) << RAIDECC_PBUF_RAIDECC_TAG_NUM_SHIFT); \
																	ulValue |= ((VIRTUALPB) << RAIDECC_RAIDECC_PB_NUM_SHIFT);\
																	R32_RAIDECC[R32_RAIDECC_PBUF_CTL_1] = ulValue; \
																}while(0)
//0x8C
#define M_RAIDECC_GET_EXTERNAL_PB_FLAG()      			    (R32_RAIDECC[R32_RAIDECC_EX_PB_FLAG])
#define M_RAIDECC_GET_EMPTY_EXTERNAL_IDX(EMPTY_EXTERNAL_IDX)	do{ \
																	U32 ubExternalBufIdx;\
																	for(ubExternalBufIdx = 0; ubExternalBufIdx < RAIDECC_MAX_EXTERNAL_BUF_IDX;ubExternalBufIdx++){\
																		if((R32_RAIDECC[R32_RAIDECC_EX_PB_FLAG] & BIT(ubExternalBufIdx)) && (R32_RS[R32_RAIDECC_EX_PB_VLD] & BIT(ubExternalBufIdx))){\
																			EMPTY_EXTERNAL_IDX = ubExternalBufIdx;\
																			break;\
																		}\
																	}\
																} while (0)
#define M_RAIDECC_SET_EXTERNAL_PB_FLAG(EXTERNAL_BUF_IDX)	do{ \
																U32 ulValue;\
																ulValue = R32_RAIDECC[R32_RAIDECC_EX_PB_FLAG]; \
																ulValue |= BIT(EXTERNAL_BUF_IDX); \
																R32_RAIDECC[R32_RAIDECC_EX_PB_FLAG] = ulValue; \
															}while(0)

#define M_RAIDECC_CLEAR_EXTERNAL_PB_FLAG(EXTERNAL_BUF_IDX)		(R32_RAIDECC[R32_RAIDECC_EX_PB_FLAG] &= (~(BIT(EXTERNAL_BUF_IDX))))

//0x90
#define M_RAIDECC_CLEAR_EXTERNAL_BUF_VALID(EXTERNAL_BUF_IDX)	(R32_RAIDECC[R32_RAIDECC_EX_PB_VLD] &= (~(BIT(EXTERNAL_BUF_IDX))))
#define M_RAIDECC_GET_EXTERNAL_PB_VALID()						(R32_RAIDECC[R32_RAIDECC_EX_PB_VLD])
#define M_RAIDECC_SET_EXTERNAL_PB_VALID(EXTERNAL_BUF_IDX)		do{ \
																	U32 ulValue; \
																	ulValue = R32_RAIDECC[R32_RAIDECC_EX_PB_VLD]; \
																	ulValue &= ~(BIT(EXTERNAL_BUF_IDX)); \
																	R32_RAIDECC[R32_RAIDECC_EX_PB_VLD] = ulValue; \
																}while(0)

//0x9C
#define M_RAIDECC_SET_EXTERNAL_PB_SELECT(RAIDECC_TAG)	do{ (R32_RAIDECC[R32_RAIDECC_EX_PB_SEL] = ((RAIDECC_TAG) & RAIDECC_EX_PB_TAG_MASK)); }while(0)
#define M_RAIDECC_GET_INTERNAL_PB_VALID()				((R32_RAIDECC[R32_RAIDECC_EX_PB_SEL] >> RAIDECC_IN_PB_VLD_SHIFT) & RAIDECC_IN_PB_VLD_MASK)
#define M_RAIDECC_GET_INTERNAL_PB_NUM()					((R32_RAIDECC[R32_RAIDECC_EX_PB_SEL] >> RAIDECC_IN_PB_NUM_SHIFT) & RAIDECC_IN_PB_NUM_MASK)
#define M_RAIDECC_GET_INTERNAL_BUF_IDX(INTERNAL_BUF_IDX)	do{ (INTERNAL_BUF_IDX) = M_RAIDECC_GET_INTERNAL_PB_NUM(); }while(0)
#define M_RAIDECC_GET_EXTERNAL_PB_NUM()					((R32_RAIDECC[R32_RAIDECC_EX_PB_SEL] >> RAIDECC_EX_PB_NUM_SHIFT) & RAIDECC_EX_PB_NUM_MASK)
#define M_RAIDECC_SET_PB_TAG(TAG)						do{ \
															U32 ulValue; \
															ulValue = R32_RAIDECC[R32_RAIDECC_EX_PB_SEL]; \
															ulValue &= ~(RAIDECC_EX_PB_TAG_MASK << RAIDECC_EX_PB_TAG_SHIFT); \
															ulValue |= ((TAG) & RAIDECC_EX_PB_TAG_MASK) << RAIDECC_EX_PB_TAG_SHIFT;\
															R32_RAIDECC[R32_RAIDECC_EX_PB_SEL] = ulValue; \
														}while(0)

//0xA8
#define M_RAIDECC_GET_ENCODED_NUM()          			((R32_RAIDECC[R32_RAIDECC_BMU_ENC_PB_CTL] >> RAIDECC_ENCODE_PB_NUM_SHIFT) & RAIDECC_ENCODE_PB_NUM_MASK)
#define M_RAIDECC_GET_ENCODED_PB_CNT()					((R32_RAIDECC[R32_RAIDECC_BMU_ENC_PB_CTL] >> RAIDECC_ENCODE_PB_CNT_SHIFT) & RAIDECC_ENCODE_PB_CNT_MASK)
#define M_RAIDECC_CLEAR_BMU_ENC_PB_CTL()				do{R32_RAIDECC[R32_RAIDECC_BMU_ENC_PB_CTL] = 0; }while(0)
#define M_RAIDECC_SET_ENCODED_PB_CNT(ENCODED_PB_CNT)   	do{ \
															U32 ulValue; \
															ulValue = R32_RAIDECC[R32_RAIDECC_BMU_ENC_PB_CTL]; \
															ulValue &= ~(RAIDECC_ENCODE_PB_CNT_MASK << RAIDECC_ENCODE_PB_CNT_SHIFT); \
															ulValue |= ((ENCODED_PB_CNT) & RAIDECC_ENCODE_PB_CNT_MASK) << RAIDECC_ENCODE_PB_CNT_SHIFT;\
															R32_RAIDECC[R32_RAIDECC_BMU_ENC_PB_CTL] = ulValue; \
														}while(0)


//0xAC
#define M_RAIDECC_GET_EX_PB_TAG()						((R32_RAIDECC[R32_RAIDECC_BMU_EX_PB_CTL] >> RAIDECC_EX_PB_TAG_NUM_SHIFT) & RAIDECC_EX_PB_TAG_NUM_MASK)
#define M_RAIDECC_GET_EX_PB_PBUF()						((R32_RAIDECC[R32_RAIDECC_BMU_EX_PB_CTL] >> RAIDECC_EX_PB_PBUF_NUM_SHIFT) & RAIDECC_EX_PB_PBUF_NUM_MASK)


//0xAC 0xB4
#define M_RAIDECC_SET_BMU_EXTERNAL_PB_SELECT(EXTERNAL_BUF_IDX)	(R32_RAIDECC[R32_RAIDECC_BMU_EX_PB_SEL] = ((EXTERNAL_BUF_IDX) & RAIDECC_EX_PB_SEL_MASK))
#define M_RAIDECC_SET_EXTERNAL_BUF_INFO(EXTERNAL_BUF_IDX,RAIDECC_TAG_IDX,VIRTUAL_PB_IDX)	do{\
												R32_RAIDECC[R32_RAIDECC_BMU_EX_PB_SEL] = (EXTERNAL_BUF_IDX);\
												R32_RAIDECC[R32_RAIDECC_BMU_EX_PB_CTL] = ((((RAIDECC_TAG_IDX) & RAIDECC_EX_PB_TAG_NUM_MASK) << RAIDECC_EX_PB_TAG_NUM_SHIFT)\
																						| (RAIDECC_EX_PB_TAG_NUM_VLD_MASK << RAIDECC_EX_PB_TAG_NUM_VLD_SHIFT)\
																						| (((VIRTUAL_PB_IDX) & RAIDECC_EX_PB_PBUF_NUM_MASK) << RAIDECC_EX_PB_PBUF_NUM_SHIFT)\
																						| (RAIDECC_EX_PB_PBUF_NUM_VLD_MASK << RAIDECC_EX_PB_PBUF_NUM_VLD_SHIFT));\
											}while(0)

#define M_RAIDECC_SET_FORCELOAD_BMU_EXTERNAL_PB_CONTROL(EXTERNAL_BUF_IDX, TAG, VIRTUAL_PB_IDX) do { \
                                                                U32 ulValue = BIT(RAIDECC_EX_PB_TAG_NUM_VLD_SHIFT) | (((TAG) & RAIDECC_EX_PB_TAG_NUM_MASK) << RAIDECC_EX_PB_TAG_NUM_SHIFT) | BIT(RAIDECC_EX_PB_PBUF_NUM_VLD_SHIFT) | (((VIRTUAL_PB_IDX) & RAIDECC_EX_PB_PBUF_NUM_MASK) << RAIDECC_EX_PB_PBUF_NUM_SHIFT); \
                                                                R32_RAIDECC[R32_RAIDECC_BMU_EX_PB_CTL] = ulValue; \
                                                            }while(0)


//0xB0
#define M_RAIDECC_SET_BMU_ENCODED_PB_SELECT(ENCODED_PB_IDX) (R32_RAIDECC[R32_RAIDECC_BMU_ENC_PB_SEL] = (ENCODED_PB_IDX) & RAIDECC_ENCODE_PB_SEL_MASK)

/*
 * ---------------------------------------------------------------------------------------------------
 *   extern global variables
 * ---------------------------------------------------------------------------------------------------
 */
extern U32	*gpulRaidECCSpare;

/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */

U8 RaidECCForceSave(U8 ubInternalBufIdx, U32 ulBufAddrData, U32 ulBufAddrSpare, U8 ubRestorePBUFInfo);
AOM_RS_2 void RaidECCSetFIPVirtualPB(U8 ubTagIdx, U8 ubVirtualPBIdx);

#endif /* RAIDECC_API_H_ */
