#ifndef _FIP_API_H_
#define _FIP_API_H_

#include "setup.h"
#include "typedef.h"
#include "hal/fip/fip_reg.h"
#include "hal/cop0/cop0_cmd.h"
#include "aom/aom_api.h"
#include "common/fw_common.h"
#include "hal/sys/api/padc/padc_api.h"


#define FDLL_CH_GROUP		(PS5017_EN ? 1 : 2)  //S17 only 1 Grp for CH0&1

#define FIP_MT_CLK_DIV_MAX		((PS5017_EN || PS5021_EN) ? 0x0 : 0x3F) // S17 / 16 , E13 / 1
#define FIP_INVALID_VIRTUAL_ADDR (0xFF)
#define VBRMP_Entries			(4096)
#define L4K_FW_MARK				(0x5566)
#define L4K_SIZE 				(16)
#define L4K_FWSET_OFFSET		(4)		//FWSet Start from Byte_4 in L4K Spare data(16 Byte)
#define MT_SIZE 				(40)
#define SPR_SIZE_PER_CH 		(64)
#define SPR_OFFSET					(SPR_OFF)
#define SPR_READ_OFFSET_CH(ch)	(SPR_OFFSET + (SPR_SIZE_PER_CH) * (ch))
#define SPR_PROG_OFFSET_CH(ch)	(SPR_OFFSET + (SPR_SIZE_PER_CH) * (MAX_CHANNEL + (ch)))
#define M_SET_L4KBADR(x)				((x) >> 9) //512Byte Align
#define L4K_SPARE_ALL_VALID			(0xFF)
#define L4K_BUFFER_ALL_VALID		(0xFF)
#define L4K_NEXTPTR_DEFAULT			(0xFFFF)
#define MT_PROGRAM_BUFFER_ALL_VALID	(0xF)
#define ALU_RULE_D1_BIT				(BIT0)
#define ALU_RULE_SLC_BIT			(BIT1)
#define MAX_ALU_SUBRULE_NUM		(8)

#if PS5021_EN
#define MT_FORMAT_40BYTE	(5)
#define MT_FORMAT_80BYTE 	(10)
#endif /* PS5021_EN */

#define M_GET_ECC_MODE()			(R32_FCON[R32_FCON_LDPC_CFG] & LDPC_MODE_MASK)
#define M_SET_ECC_MODE(MODE)	do { \
									U8 ubTemp = (R8_FCON[R8_FCON_LDPC_CFG] & (~LDPC_MODE_MASK)) | (MODE & LDPC_MODE_MASK); \
									R8_FCON[R8_FCON_LDPC_CFG] = ubTemp;\
								} while(0)
#define M_FIP_CLEAR_LDPC_DSP_EN()	do { \
									R32_FCON[R32_FCON_LDPC_CFG] &= ~(DSP_EN_BIT); \
									__asm("DSB"); \
									while (R32_FCON[R32_FCON_LDPC_CFG] & (DSP_EN_BIT)); \
								} while(0)
#define M_FIP_SET_LDPC_DSP_EN() do { \
									R32_FCON[R32_FCON_LDPC_CFG] |= DSP_EN_BIT; \
									__asm("DSB"); \
								} while(0)

#define M_FIP_SET_DSP2_GROUP_TABLE(ulGroupTable)	(R32_FCON[R32_FCON_DSP2_TBL] = (ulGroupTable))

#define M_FIP_WAIT_DSP_TRIGGER_DSP2_GENERATE_SB6_SB7(ubChannel) do { \
		R32_FCON[R32_FCON_DSP_TRIG] &= ~DSP_IBF_PTR_SHIFT_MASK; \
		R32_FCON[R32_FCON_DSP_TRIG] |= (0 << DSP_IBF_PTR_SHIFT); \
		while (R32_FCON[R32_FCON_DSP_TRIG] & ((BIT(ubChannel)) << DSP_TRIG_SHIFT));  \
		R32_FCON[R32_FCON_DSP_TRIG] |= ((BIT(ubChannel)) << DSP2_TRIG_SHIFT); \
		while (R32_FCON[R32_FCON_DSP_TRIG] & ((BIT(ubChannel)) << DSP2_TRIG_SHIFT)); \
								} while(0)

#define M_FIP_SET_DSP_UPDATE_LLR_TABLE_EN()	(R32_FCON[R32_FCON_LDPC_CFG] |= DSP_LLR_UPD_BIT)

#define M_FIP_SET_LDPC_CONFIG_PAGE_SELECT(ubPageType) do { \
									R32_FCON[R32_FCON_LDPC_CFG] &=  ~( (PAGE_SELECT_MASK) << PAGE_SELECT_SHIFT ); \
									R32_FCON[R32_FCON_LDPC_CFG] |=  (((ubPageType) & PAGE_SELECT_MASK) << PAGE_SELECT_SHIFT); \
								} while (0)
#define M_FIP_CHANNEL_COUNTONE_EN(ubChannel)	(R32_FCTL_CH[(ubChannel)][R32_FCTL_CNT_ONE] |= SET_COUNT_ONE)
#define M_FIP_CHANNEL_COUNTZERO_EN(ubChannel)	(R32_FCTL_CH[(ubChannel)][R32_FCTL_CNT_ONE] &= CLR_COUNT_ZERO)
#define M_FIP_CHANNEL_GET_COUNTONE_RESULT(ubChannel)	((R32_FCTL_CH[(ubChannel)][R32_FCTL_CNT_ONE] & CNT_ONE_FUNC_RES_SHIFT_MASK) >> CNT_ONE_FUNC_RES_SHIFT)

#define FIP_SET_SNAP_READ_DMY(DMY_0, DMY_1, DMY_2, DMY_3)	do { \
									R32_FCON[R32_FCON_PAGE_CFG] &= ~FRM_0_DMY_LEN_SHIFT_MASK; \
									R32_FCON[R32_FCON_PAGE_CFG] &= ~FRM_1_DMY_LEN_SHIFT_MASK; \
									R32_FCON[R32_FCON_PAGE_CFG] |= (DMY_0 & FRM_0_DMY_LEN_MASK) << FRM_0_DMY_LEN_SHIFT; \
									R32_FCON[R32_FCON_PAGE_CFG] |= (DMY_1 & FRM_1_DMY_LEN_MASK) << FRM_1_DMY_LEN_SHIFT; \
									R32_FCON[R32_FCON_PAGE_CFG_1] &= ~FRM_2_DMY_LEN_SHIFT_MASK; \
									R32_FCON[R32_FCON_PAGE_CFG_1] &= ~FRM_3_DMY_LEN_SHIFT_MASK; \
									R32_FCON[R32_FCON_PAGE_CFG_1] |= (DMY_2 & FRM_2_DMY_LEN_MASK) << FRM_2_DMY_LEN_SHIFT; \
									R32_FCON[R32_FCON_PAGE_CFG_1] |= (DMY_3 & FRM_3_DMY_LEN_MASK) << FRM_3_DMY_LEN_SHIFT; \
								} while(0)

#define M_FIP_SET_VIRTUAL_PB_IDX(VIRTUAL_PB_IDX)			(R32_FCON[R32_FCON_RAIDECC_PB_SEL] = VIRTUAL_PB_IDX)
#define M_FIP_GET_VIRTUAL_PB_STATUS()		(R32_FCON[R32_FCON_RAIDECC_PB_STS] & RAIDECC_PB_STS_BIT)
#define M_FIP_GET_VIRTUAL_PB_TAG()		((R32_FCON[R32_FCON_RAIDECC_PB_STS] >> RAIDECC_PB_TAG_NUM_SHIFT) & RAIDECC_PB_TAG_NUM_MASK)

#define M_FIP_CLEAR_NODMA_ERROR_BM(CH)	(R32_FCON[R32_FCON_IOR_CFG_4] |= ((BIT(CH) & NODMA_TO_CH_MASK) << NODMA_TO_CH_SHIFT));	//Write 1 to clear
#define IFSA_HIDDEN_BIT_NUM		((PS5017_EN || PS5021_EN) ? 0 : 8)
#define MAX_INT_FIFO_MAP_CNT	(2)
#define INVALID_MT_CFG			(0xFFFFFFFF)
#define INVALID_MT_INDEX		(0xFF)
#define INVALID_CE_INDEX		(0xFF)
#define INVALID_CH_INDEX		(0xFF)
#define INVALID_INDEX			(0xFF)
#define MTQ_DEPTH				(2)
#define ABORT_FIP_ALLOC_BUF_THRESHOLD	(0x50000)
#if IOR_EN
#define RETRY_WAIT_READY_TIME_CNT   (500)
#else
#define RETRY_WAIT_READY_TIME_CNT   (2000)
#endif
#define IOR_GROUP_NUM			(8)
#define IOR_GROUP_IDX_MASK		(7)
#define NO_NEED_ABORT_DMA		(0)
#define NEED_ABORT_DMA			(1)
#define WAIT_FIP_IDLE_DONE		(0)
#define WAIT_FIP_IDLE_TIMEOUT	(1)
#define RETRY_ALL_CH_NO_INT (0)
#define RETRY_CUR_CH_INT    (1)
#define RETRY_OTHER_CH_INT  (2)
#define TSB_TLC_PLANE0_ERROR_BIT ( BIT1  )
#define TSB_TLC_PLANE1_ERROR_BIT ( BIT2  )
#define READ_ID_DATA_NUM	(6)
#if BURNER_MODE_EN
#define FIP_P4K_BACKUP_NUM		(16)
#else /* BURNER_MODE_EN */
#if (PS5017_EN || PS5021_EN)
#define FIP_P4K_BACKUP_NUM	    ((DBUF_GC_BACKUP_POR_INIT_SIZE + DBUF_GC_BACKUP_COPY_BLK_SIZE + DBUF_GC_BACKUP_READ_N4K_SIZE + DBUF_GC_BACKUP_READ_GCSA_P4K_SIZE) / L4K_SIZE)
#else /*(PS5017_EN || PS5021_EN)*/
#define FIP_P4K_BACKUP_NUM		((GC_BACKUP_POR_INIT_LEN + GC_BACKUP_COPY_BLK_LEN + GC_BACKUP_READ_N4K_LEN + GC_READ_GCSA_P4K_LEN) / L4K_SIZE)
#endif /* (PS5017_EN || PS5021_EN) */
#endif /* BURNER_MODE_EN */

#define FIP_IO_SINGLE_END	0
#define FIP_IO_DIFFERENTIAL	(0xF)
#define FIP_ALL_IO_DIFFERENTIAL	(0xF)
#define FIP_MICON_INTERFACE_RETRY	10
#define FIP_ZQCAL_DEFAULT	0x7FFF

#define FIP_ZQ_NOT_SUPPORT		(BIT1)

#define INVALID_ERROR_LOGICAL_PCA	(0xFFFFFFFF)
#define PLANE_A		BIT0
#define PLANE_B		BIT1
#define PLANE_C	 	BIT2
#define PLANE_D	 	BIT3
#define CACHE_PROG BIT4

#define PLANE_FAIL_SUM	BIT0
#define PLANE_A_FAIL	BIT1
#define PLANE_B_FAIL	BIT2

#define FIP_VALLEY_TRACK_READOUT_BYTE_COUNT	(38)
#define FIP_VALLEY_TRACK_READOUT_MODE_CBC			(0)
#define FIP_VALLEY_TRACK_READOUT_MODE_READ_OFFSET	(1)

#define FIP_DIE_DENSITY_256Gb	(256)
#define FIP_DIE_DENSITY_512Gb	(512)
#define FIP_DIE_DENSITY_1024Gb	(1024)

#define FIP_TARGET_DENSITY_256Gb	(256)
#define FIP_TARGET_DENSITY_512Gb	(512)
#define FIP_TARGET_DENSITY_1024Gb	(1024)

#define LOGICAL_PLANE_BIT_NUM		(2) // max support 4 plane

/*scan window*/
#define FIP_DQ_BIT_SIZE				(8)
#define FIP_SCAN_WINDOW_CNT			(2)
#define FIP_FLASH_ODT_SHIFT			(4)
#if(CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
#define FIP_FLASH_ODT_READ_SHIFT	(6)
#endif
#define FIP_LEFT_ALIGN		(0)
#define FIP_RIGHT_ALIGN		(1)
#define FIP_DQ_DQS_INITIAL_VALUE		(0)
#define FIP_DQ_GROUP_CNT			(8)
#define FIP_DQ_CE_PER_GROUP		(2)
#define FIP_DQ_RESUT_INITIAL_VALUE		(0xF)
#define FIP_DQS_DELAY_TX_MASK		(0x0F)
#define FIP_DQS_DELAY_RX_MASK		(0xF0)
#define FIP_BYTE_FULL_MASK			(0xFF)
#define FIP_BYTE_HIGH_4BIT_MASK	(0xF0)
#define FIP_BYTE_LOW_4BIT_MASK	(0x0F)
#define FIP_NO_USE_PARAMETER		(0)
#define FIP_SCAN_ALL_CE					(0xFF)
#define FIP_SCAN_ALL_CHANNEL			(0xFF)
#define FIP_FOURK_PAGE_LOG				(12)
#define FIP_WORST_PATTERN_ARRAY_SZIE	(128)
#define FIP_NEXT_CHANNEL_CNT			(1)
#define FIP_DQ_TRAIN_RESULT_DIE_IDX		(0)
#define FIP_ONE_BYTE_VIRTUAL_ADDR             (1)
/* Toshiba Set Feature Address */
#define TOSHIBA_FEATURE_ADDR_TOGGLE2		(0x02)
#define TOSHIBA_FEATURE_ADDR_MODE			(0x80)
#define 	TOSHIBA_FEATURE_SDR				(0x01)
#define 	TOSHIBA_FEATURE_TOOGLE			(0x00)
#define TOSHIBA_FEATURE_DRIVE					(0x10)
#if(FW_CATEGORY_FLASH == FLASH_V8_TLC)//Jeffrey Porting 3D V8 TLC Add
#define TOSHIBA_FEATURE_ODT_SETTING					(0x83)
#endif
#define MICRON_FEATURE_TIMINGMODE              (0x01)
#define MICRON_FEATURE_NVDDR3CONFIG         (0x02)
#define MICRON_FEATURE_DRIVE				(0x10)
#define MICRON_FEATURE_PPMCONFIG        (0xEB)
#define     MICRON_FEATURE_2LUN_GROUP       (0x00)
#define     MICRON_FEATURE_4LUN_GROUP       (0x01)
#define     MICRON_FEATURE_8LUN_GROUP       (0x02)
#define     MICRON_FEATURE_16LUN_GROUP       (0x03)
#define     MICRON_FEATURE_PPM_LUN_PER_GROUP (MICRON_FEATURE_4LUN_GROUP)


/* MICRON ADWLSV Constant define*/
#if MICRON_FSP_EN
#define MICRON_FEATURE_ADR_PLA					(0xF2)
#define MICRON_PLA_ENABLE						(0x0)
#define MICRON_PLA_DISABLE						(0x1)
#define MICRON_FEATURE_ADWLSV                   (0xDC)
#define MICRON_ADWLSV_PARAMETER_LENGTH          (4)
#if (IM_B27A)
#define MICRON_BLK_ADDR_CYCLE					(3)
#define MICRON_BLK_ADDR_SHIFT                   (5)
#define MICRON_BLK_ADDR_LENGTH					(10)
#elif (IM_N28) /*(IM_B27A)*/
#define MICRON_BLK_ADDR_CYCLE					(3)
#define MICRON_BLK_ADDR_SHIFT                   (5)
#define MICRON_BLK_ADDR_LENGTH					(11)
#elif ((IM_B47R) || IM_B37R || (IM_N48R)) /*(IM_B27A)*/
#define MICRON_BLK_ADDR_CYCLE					(3)
#define MICRON_BLK_ADDR_SHIFT                   (6)
#define MICRON_BLK_ADDR_LENGTH					(10)
#else /*(IM_B27A)*/
#define MICRON_BLK_ADDR_CYCLE					(2)
#define MICRON_BLK_ADDR_SHIFT                   (4)
#define MICRON_BLK_ADDR_LENGTH					(11)
#endif /*(IM_B27A)*/
#define MICRON_ADWLSV_ENTRIES_AVAILABLE         (0)
#define MICRON_ADWLSV_ALL_ENTRIES_USED          (1)
#define MICRON_ARRAY_READY_BIT                  (BIT5)
#define MICRON_ADWLSV_ALL_PLANEBANK             (0xFF)
#define MICRON_ADWLSV_CLEAR_ALL_BLK_ADDR       (0x00)
#define MICRON_ADWLSV_TEST_GET_FEATURE_BIT      (BIT1)
#define MICRON_ADWLSV_TEST_FULL_THE_LIST_BIT    (BIT2)
#define MICRON_ADWLSV_TEST_CLEAR_RESULT_BIT     (BIT3)
#define MICRON_ADWLSV_TEST_GET_INIT_FEATURE_BIT (BIT4)
#define MICRON_ADWLSV_ERROR_HANDLE_RETRY_CNT	(3)

#define MICRON_FEATURE_FLAG_CHECK_FUNCTIONALITY	(0xDF)
#define MICRON_FLAG_CHECK_FUNCTIONALITY_DISABLED_BIT	(~BIT5)
#define MICRON_FLAG_CHECK_FUNCTIONALITY_ENABLED_BIT		(BIT5)

#endif /*MICRON_FSP_EN*/

//78h
#define PE_FAIL         BIT0
#define PE_FAIL_CACHE   BIT1

// for fip channel group
#define 	FIP_CHANNEL_GROUP_0			(0)
// Flash clock Div.
#if (PS5021_EN)
#define FLH_CLOCK_DIV_RESET	    (0xFF)
#define FLH_CLOCK_DIV_1			(0x0) // E21: fip at least divide by 2
#define FLH_CLOCK_DIV_2			(0x0)
#define FLH_CLOCK_DIV_4			(0x1)
#define FLH_CLOCK_DIV_8			(0x2)
#define FLH_CLOCK_DIV_16		(0x3)
#define FLH_CLOCK_DIV_32		(0x4)
#define FLH_CLOCK_DIV_64		(0x5)
#define FLH_CLOCK_DIV_128		(0x6)
#define FLH_CLOCK_DIV_SEL_NUM	(0x7)
#elif (PS5017_EN)
#define FLH_CLOCK_DIV_1		    (0x0)  // E17: fip at least divide by 2
#define FLH_CLOCK_DIV_2			(0x0)
#define FLH_CLOCK_DIV_4			(0x1)
#define FLH_CLOCK_DIV_8			(0x2)
#define FLH_CLOCK_DIV_16		(0x3)
#define FLH_CLOCK_DIV_32		(0x4)
#define FLH_CLOCK_DIV_64		(0x5)
#define FLH_CLOCK_DIV_128		(0x6)
#else /* (PS5013_EN) */
#define FLH_CLOCK_DIV_1			(0x3F)
#define FLH_CLOCK_DIV_2			(0)
#define FLH_CLOCK_DIV_4			(0x2)
#define FLH_CLOCK_DIV_8			(0x6)
#define FLH_CLOCK_DIV_16		(0xE)
#define FLH_CLOCK_DIV_32		(0x1E)
#define FLH_CLOCK_DIV_64		(0x3E)
#endif /* (PS5021_EN) */


#define	ECC_MODE_0_BYTE_LEN				(256)			//2048 bits = 256 Bytes
#define	ECC_MODE_1_BYTE_LEN				(240)			//1920 bits = 240 Bytes
#define	ECC_MODE_2_BYTE_LEN				(224)			//1792 bits = 224 Bytes
#define	ECC_MODE_3_BYTE_LEN				(208)			//1664 bits = 208 Bytes
#define	ECC_MODE_4_BYTE_LEN				(176)			//1408 bits = 176 Bytes
#define	ECC_MODE_5_BYTE_LEN				(144)			//1152 bits = 144 Bytes
#define	ECC_MODE_6_BYTE_LEN				(128)			//1024 bits = 128 Bytes
#define	ECC_MODE_7_BYTE_LEN				(304)			//2432 bits = 304 Bytes
#define	ECC_MODE_8_BYTE_LEN				(288)			//2304 bits = 288 Bytes


#define FIP_P4K_RAND_SEED_BIT_LENGTH 	(4)

#define P4K_FRAME_NUM	(4)
#define P2K_FRAME_NUM	(8)
#define INIT_PTYCHSUM_NUM			(0xFFFF)
#define INIT_PTYCHSUM_THRESHOLD	((CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_QLC) ? 0xFFFF : 820)
#define INIT_PTYCHSUM_GEN_MAX	(1000)//(820)

#define ERROR_RECORDER		(1)
#define FIP_DELEGATE_CMD			(2)
#define FIP_DELEGATE_CMD_Q_STUCK	(3)

#define FIP_ALU_SELECT_0		(0) // SLC=0, D1=0
#define FIP_ALU_SELECT_1		(1) // SLC=0, D1=1
#define FIP_ALU_SELECT_2		(2) // SLC=1, D1=0
#define FIP_ALU_SELECT_3		(3) // SLC=1, D1=1

#define FIP_FEATURE_COUNT		(4)
#define FIP_ZQ_TIMEOUT			(0xffffffff)
#define FIP_EARLY_BICS4_256GB_MARK		                (0x43)
#define FIP_FINAL_BICS4_256GB_MARK		                (0x4E)

#define FIP_SET_FEATURE_RETRY_TIME		(5)
//for DPS feature
#if (FIP_DPS_FEATURE_EN)
#define FIP_DPS_TOTAL_SET_ADDR_NUM		(6)
#define FIP_DPS_SLC_NEED_PLUS_ADDR_NUM		(2)
#define FIP_DPS_SLC_NEED_MINUS_ADDR_NUM		(0)
#define FIP_DPS_SLC_NEED_SET_ADDR_NUM		(4)
#define FIP_DPS_TLC_NEED_PLUS_ADDR_NUM		(0)
#define FIP_DPS_TLC_NEED_MINUS_ADDR_NUM		(0)
#define FIP_DPS_TLC_NEED_SET_ADDR_NUM		(0)
#define FIP_DPS_FORTH_PARAMETER				(4)
#define FIP_DPS_FIFTH_PARAMETER				(5)
#endif /*FIP_DPS_FEATURE_EN*/
#define FIP_TOSHIBA_ID_FIRST_BYTE			(0x98)
#define FIP_ID_FIRST_BYTE_INDEX			(0)

//Backup P4k workaround
#define FIP_WORKAROUND_BACKUP_P4K_ADDR_OFFSET					(0)
#define FIP_WORKAROUND_BACKUP_P4K_FRAME_2_RANDOM_VALUE_OFFSET	(10)
#define FIP_WORKAROUND_BACKUP_P4K_FRAME_3_RANDOM_VALUE_OFFSET	(14)
#define FIP_WORKAROUND_BACKUP_P4K_BMU_CMD_OFFSET				(23)
#define FIP_WORKAROUND_BACKUP_P4K_OFFSET						(8)

#define FIP_PAD_ZQ_P_DEFAULT_VALUE_12V	(0x11)
#define FIP_PAD_ZQ_N_DEFAULT_VALUE_12V	(0xf)

#if PS5021_EN
#define FIP_DEFAULT_WARMUP_CYCLE_R (0)
#define FIP_DEFAULT_WARMUP_CYCLE_W (0)
#endif /* PS5021_EN */

/***********************
 *  MT Field Constant  *
 ***********************/
//=====  Byte 00 - 03  =====
// Bit [4]  BUSY
#define FIP_MT_BUSY_POLLING_DIS			(0)
#define FIP_MT_BUSY_POLLING_EN			(1)
//=====  Byte 04 - 07  =====
//=====  Byte 08 - 11  =====
//=====  Byte 12 - 15  =====
// Bit [8]  IO_TYP
#define FIP_MT_IO_TYPE_DIFFERENTIAL		(0)
#define FIP_MT_IO_TYPE_SINGLE_END		(1)
// Bit [17:16]  FLH_TYP
#define FIP_MT_FLASH_TYPE_LEGACY		(0x0)
#define FIP_MT_FLASH_TYPE_TOGGLE		(0x2)
#define FIP_MT_FLASH_TYPE_ONFI			(0x3)
//=====  Byte 16 - 19  =====
//=====  Byte 20 - 23  =====
//=====  Byte 24 - 27  =====
//=====  Byte 28 - 31  =====
//=====  Byte 32 - 35  =====
//=====  Byte 36 - 39  =====

#define FIP_MT_CONV_PAGE_L_LEN			(9)
#define FIP_MT_CONV_PAGE_H_LEN			(4)

#if (HOST_MODE == USB)
// JIRA@**********-1048, AC Timing verification for USB PPS
#define FIP_ACTIMING_NUM	(64)
#endif /* (HOST_MODE == USB) */

enum {
	LEGACY_INTERFACE  = 0,
	TOGGLE_INTERFACE  = 1,
	TOGGLE2_INTERFACE = 2,
	TOGGLE3_INTERFACE = 3,
	NVDDR_INTERFACE   = 4,
	NVDDR2_INTERFACE  = 5,
	NVDDR3_INTERFACE  = 6,
	ALL_INTERFACE_NUM = 7,
	DEFAULT_INTERFACE = 0xFF
};

enum {
	FIP_AUTO_POLL_CACHE_READY,
	FIP_AUTO_POLL_TRUE_READY,
	FIP_AUTO_POLL_LUN_0_READY,
	FIP_AUTO_POLL_LUN_1_READY
};

#if (PS5021_EN)
enum {
	FIP_FLASH_CLOCK_10MHZ   = 0, //legacy10 or toggle-20
	FIP_FLASH_CLOCK_33MHZ   = 1, //legacy33 or toggle-66
	FIP_FLASH_CLOCK_41P7MHZ = 2, //legacy40 or toggle-80
	FIP_FLASH_CLOCK_50MHZ   = 3, //toggle-100
	FIP_FLASH_CLOCK_100MHZ  = 4, //toggle-200
	FIP_FLASH_CLOCK_166MHZ  = 5, //toggle-332
	FIP_FLASH_CLOCK_200MHZ  = 6, //toggle-400
	FIP_FLASH_CLOCK_266MHZ  = 7, //toggle-533
	FIP_FLASH_CLOCK_333MHZ  = 8, //toggle-667
	FIP_FLASH_CLOCK_400MHZ  = 9, //toggle-800
	FIP_FLASH_CLOCK_600MHZ  = 10, //toggle-1200
	FIP_FLASH_CLOCK_533MHZ  = 11, //toggle-1066
	FIP_FLASH_CLOCK_700MHZ  = 13, //toggle-1400
	FIP_FLASH_CLOCK_800MHZ  = 14, //toggle-1600
	FIP_FLASH_CLOCK_225MHZ  = 15, //toggle-450
	FIP_FLASH_CLOCK_NUM,
	FIP_FLASH_CLOCK_DEFAULT = 0xFF
};
#elif (PS5017_EN)
#if (U17_EN) // U17 MP clock parameter diff
enum {
	FIP_FLASH_CLOCK_10MHZ  = 0, //legacy10 or toggle-20
	FIP_FLASH_CLOCK_33MHZ  = 1, //legacy33 or toggle-66
	FIP_FLASH_CLOCK_41P7MHZ = 2, //legacy40 or toggle-80
	FIP_FLASH_CLOCK_100MHZ = 3, //toggle-200
	FIP_FLASH_CLOCK_RSV4   = 4,
	FIP_FLASH_CLOCK_RSV5   = 5,
	FIP_FLASH_CLOCK_200MHZ = 6, //toggle-400
	FIP_FLASH_CLOCK_225MHZ = 7, // U17 add for compiler error
	FIP_FLASH_CLOCK_266MHZ = 8, //toggle-533
	FIP_FLASH_CLOCK_RSV9   = 9,
	FIP_FLASH_CLOCK_333MHZ = 10, //toggle-667
	FIP_FLASH_CLOCK_RSV11  = 11,
	FIP_FLASH_CLOCK_400MHZ = 12, //toggle-800
	FIP_FLASH_CLOCK_533MHZ = 13, //E17
	FIP_FLASH_CLOCK_600MHZ = 14, //E17
	FIP_FLASH_CLOCK_700MHZ = 15, //E17
	FIP_FLASH_CLOCK_NUM,
	FIP_FLASH_CLOCK_DEFAULT = 0xFF
};
#else /*(U17_EN)*/
enum {
	FIP_FLASH_CLOCK_10MHZ   = 0,  //legacy10 or toggle-20
	FIP_FLASH_CLOCK_33MHZ   = 1,  //legacy33 or toggle-66
	FIP_FLASH_CLOCK_41P7MHZ = 2,  //legacy40 or toggle-80
	FIP_FLASH_CLOCK_RSV3 	= 3,
	FIP_FLASH_CLOCK_100MHZ  = 4,  //toggle-200
	FIP_FLASH_CLOCK_RSV5 	= 5,
	FIP_FLASH_CLOCK_200MHZ  = 6,  //toggle-400
	FIP_FLASH_CLOCK_266MHZ  = 7,  //toggle-533
	FIP_FLASH_CLOCK_333MHZ  = 8,  //toggle-667
	FIP_FLASH_CLOCK_400MHZ  = 9,  //toggle-800
	FIP_FLASH_CLOCK_600MHZ  = 10, //toggle-1200
	FIP_FLASH_CLOCK_533MHZ  = 11, //toggle-1066
	FIP_FLASH_CLOCK_450MHZ  = 12, //E17
	FIP_FLASH_CLOCK_700MHZ  = 13, //toggle-1400
	FIP_FLASH_CLOCK_800MHZ	= 14, //toggle-1600
	FIP_FLASH_CLOCK_225MHZ  = 15, //toggle-450
	FIP_FLASH_CLOCK_NUM,
	FIP_FLASH_CLOCK_DEFAULT = 0xFF
};
#endif /* (U17_EN) */
#else /*(PS5017_EN)*/
enum {
	FIP_FLASH_CLOCK_10MHZ  = 0, //legacy10 or toggle-20
	FIP_FLASH_CLOCK_33MHZ  = 1, //legacy33 or toggle-66
	FIP_FLASH_CLOCK_41P7MHZ	= 2, //legacy40 or toggle-80
	FIP_FLASH_CLOCK_50MHZ  = 3, //toggle-100
	FIP_FLASH_CLOCK_100MHZ = 4, //toggle-200
	FIP_FLASH_CLOCK_166MHZ = 5, //toggle-332
	FIP_FLASH_CLOCK_200MHZ = 6, //toggle-400
	FIP_FLASH_CLOCK_266MHZ = 7, //toggle-533
	FIP_FLASH_CLOCK_333MHZ = 8, //toggle-667
	FIP_FLASH_CLOCK_400MHZ = 9, //toggle-800
	FIP_FLASH_CLOCK_NUM,
	FIP_FLASH_CLOCK_DEFAULT = 0xFF
};
#endif /* (PS5021_EN) */

#define FIP_MAX_PHYSICAL_CE_PER_CHANNEL	(MAX_PHYSICAL_CE_PER_CH)

enum {MT_TARGET_CPU0 = 1, MT_TARGET_CPU1 = 2, MT_TARGET_COP0 = 4};
enum {FIP_GRP0 = 0, FIP_GRP1, FIP_GRP2, FIP_GRP3, FIP_GRP4, FIP_GRP5, FIP_GRP6, FIP_GRP7};
enum {FLH_SLC = 0, FLH_MLC = 1, FLH_TLC = 2, FLH_QLC = 3, FLH_pTLC = 4, ALL_CELLTYPE_NUM = 5};
enum {TOSHIBASANDISK = 0, INTELMICRON = 1, SKHYNIX = 2, YMTC = 3, SAMSUNG = 4, ALL_SUPPORT_VENDOR_NUM};
enum {XLC_MODE = 0, SLC_A2_MODE = 0xA2, SLC_40_MODE = 0x40, SLC_F91_MODE = 0x91, SLC_BF_MODE = 0xBF, SLC_DA_MODE = 0xDA, SLC_3B_MODE = 0x3B}; //XLC_MODE also means A2 DISABLE
typedef enum {
	TSB_3D_BICS3 = 0,
	TSB_3D_BICS4,
	TSB_3D_BICS5,
	TSB_3D_BICS6,
	TSB_3D_BICS6_QLC,//zerio bics6 qlc add
	TSB_3D_BICS8,//zerio BICS8 Add
	MICRON_3D,
	MICRON_3D_QLC,//zerio n48r add qlc
	HYNIX_3D_V6,//V6 TLC porting
	HYNIX_3D_V7,//V7 TLC porting
	HYNIX_3D_V7_QLC,//V7 QLC porting
	HYNIX_3D_V8,//Jeffrey V8 porting
	HYNIX_3D_V5,//Jeffrey V5 porting
	YMTC_3D_EMS,
	YMTC_3D_TAS,
	YMTC_3D_WTS,//zerio wts add
	SAMSUNG_3D_V6,
	SAMSUNG_3D_V6P,
	SAMSUNG_3D_V7,//Samsung v7 mst add--Reip
	SAMSUNG_3D_V8,//Samsung v8 mst add--Reip
	SAMSUNG_3D_V5,//Samsung v5 mst add--Jeffrey
	INTEL_3D,
	ALL_SUPPORT_NAND_NUM
} NAND_FLASH_t;

typedef enum {
	FIP_FLASH_64_GBIT_PER_DIE = 0,
	FIP_FLASH_128_GBIT_PER_DIE,
	FIP_FLASH_256_GBIT_PER_DIE,
	FIP_FLASH_512_GBIT_PER_DIE,
	FIP_FLASH_1024_GBIT_PER_DIE,
	FIP_FLASH_1360_GBIT_PER_DIE,
} FipFlashSizePerLun_t;

enum {
	FLH_LOWER_PAGE = 0,
	FLH_MIDDLE_PAGE,
	FLH_UPPER_PAGE,
	FLH_FSP_PAGE_NUM,
};

enum {
	FIP_STOP_ALLOCATE_EVENT_AOM = 0,
	FIP_STOP_ALLOCATE_EVENT_PROGRAM_ERROR_HANDLE,
	FIP_STOP_ALLOCATE_EVENT_SB_WAIT_MT_DONE,
	FIP_STOP_ALLOCATE_EVENT_RAIDECC_WAIT_MT_DONE,
	FIP_STOP_ALLOCATE_EVENT_TURBORAIN_WAIT_MT_DONE,
	FIP_STOP_ALLOCATE_EVENT_POLL_MTQ_STATUS,
	FIP_STOP_ALLOCATE_EVENT_CHECK_FPU_TIMEOUT,
	FIP_STOP_ALLOCATE_EVENT_RMA_LOG,
	FIP_STOP_ALLOCATE_EVENT_PATCH_CMD,
	FIP_STOP_ALLOCATE_EVENT_PATCH_LITE,
	FIP_STOP_ALLOCATE_EVENT_SB_WAIT_PATCH_DONE,
	FIP_STOP_ALLOCATE_EVENT_SB_WAIT_MTP_STALL,
	FIP_STOP_ALLOCATE_EVENT_RAIDECC_WAIT_PATCH_DONE,
	FIP_STOP_ALLOCATE_EVENT_RAIDECC_WAIT_MTP_STALL,
	FIP_STOP_ALLOCATE_EVENT_RETRY_WAIT_MT_DONE,
	FIP_STOP_ALLOCATE_EVENT_ADWLSV_WAIT_MT_DONE,
	FIP_STOP_ALLOCATE_EVENT_UNEXPECT,
	FIP_STOP_ALLOCATE_EVENT_NUM
};

typedef union {
	U32 ulItem;
	struct {
		//Byte0----------------------------------
		U32 btNoSupport: 1;		//bit 0
		U32 btA2Support: 1;		//bit 1
		U32 btDASupport: 1;		//bit 2
		U32 bt3BSupport: 1;     //bit 3
		U32 bt3D: 1;	       	//bit 4
		U32 VendorType: 3;		//bit5~7

		//Byte1----------------------------------
		U32 CellType: 2;		//bit8~9
		FipFlashSizePerLun_t SizePerDie: 4;	//bit10~13
		U32 bt6CycleAddr: 1;		//bit14
		U32 btRHWDelay: 1;		//bit15 Micron Only

		//Byte2----------------------------------
		U32 btToggleNand: 1;	//bit16
		U32 btNVDDR3: 1;	   	//bit17
		U32 btDifferential: 1;	//bit18
		U32 btAIPRFlagOn: 1;	//bit19
		U32 btpTLC:		1;		//bit20
		U32 btDCCFail:	1;		//bit21
		U32 btZQCLFail:	1;		//bit22
		U32 Reserved:	1;		//bit23

		//Byte3----------------------------------
		NAND_FLASH_t NAND : 8;	//bit[24:31]
	}
	BitMap;
} FlashType_t;

typedef union {			//maybe change to 4 bytes
	U32 All;
	struct {
		U32 ubNVDDRTimingMode: 8;  // bit 0-7, B141
		U32 ubNVDDR2TimingMode: 8; // bit 8-15  , B142
		U32 btNotSupport: 1; // bit 16,
		U32 btNandSupplyByEmulator: 1; // bit 17
		U32 btNandEmulatorNoSLCPool: 1; // bit 18
		U32 btNVDDR: 1; // bit19,  B6_b5
		U32 btNVDDR2: 1; // bit20  , B6_b10
		U32 btNVDDR2TimingMode8: 1;	// bit 21
		U32 btNouse: 2; // bit 22-23
		U32 ubAsynchronousTimingMode: 8; // bit 24-31, B129
	} ONFI;

	struct {
		U32 uwToggleDDRSpeedGrade: 16;	// bit0-15
		U32 ubDriveSupport: 8; 			// bit 16-23
		U32 btNotSupport: 1;			// bit 24
		U32 btNoUse: 7; // bit25-bit31
	} JEDEC;
} FLASH_PARAMETER_PAGE;

typedef struct {
	//----------FLASH TYPE----------//
	U8 ubFlashID[8];//8     								//The Flash ID of the Flash at Channel 0, CE0

	U16 uwPageByteCnt;// 2									//The usable size of a page in units of bytes, page size only be referenced by vendor command "ReadSystemInfo"
	U16 uwPageTotalByteCnt;// 2							//The real size of page  this size include page spare size
	U16 uwPagePerBlock;// 2								//The number of page per block
	U8  ubPagePerBlockLog;// 1							//The number of page per block represented in powers of 2 (page per block == 1 << page per block bit), TLC not reference this bit
	U8  ubSectorsPerPageLog;// 1							//The number of sectors per Page represented in powers of 2 (sector per plane == 1 << sector per plane bit)

	U8 ubChannelExistNum; // 1							//The total number of channels with a CE attached in the first bank ie (the CE number is the same as the channel number)
	U8 ubChannel_Exist_Bit;// 1							//The bit representation of which channel has a CE attached to its first bank (eg. Channel 0 and channel 3 => 0x09)
	U8 ubFastPageRule;	// 1						 		//Indicates the type of fast page rule that is to be used for MLC
	U8 ubTRHWDelayCount; // 1
	U8 ubChannel_DQ_Revert_Bit[4];// 4			//Bit mask of DQ revert, bit represet as each CE(eg. 1 was revert)
	U8 ubDQSDetectResult;									//BIT0 CH0 has detected dqs BIT1 CH1 has detected dqs...  BIT4 CH0 has detected dqsb.. BIT5 CH1 has detected dqsb
	U8 ubLDPCMode7FrameCntInPage;
	U8 ubMicronNandWorkaround;							// Indicates whether should implement Micron nand's bug's workaround.
	U8 ubReserve[1];

	FlashType_t ulFlashDefaultType;// 4					 //A structure that records various information on the flash attached in a bit wise representation
	U8 ubSLCMethod;              // 1					//record SLC method code block use
	U8 ubDefaultInterface;        // 1					//record NAND Interface Default value;
	U8 ubCurrentInterface;		 // 1
	U8 ubCurrentFlashClock;								//record Current Flash Clock
	U8 ubTargetInterface;								//record the Flash Interface that want to switch
	U8 ubTargetFlashClock;								//record the Flash Clock that want to switch
	U8 ubSetInterfaceErrCode;							//record Error Code when switch Interface Fail
	U8 ubBootCodeInterface;
	U8 ubBootCodeFlashClock;
	U8 ubZQ;
	U8 ubDCC;
	U8 ubLUNperTarget;			 // 1
	U8 ubPlanePerTarget; 		 // 1

	U32 ubCENumber : 8; // 1
	U32 NandD3PECycle: 20;
	U32 PlanePerDie: 4;
	U32 ulBlockPerPlaneBank; //4

	U32 ulCEBMP;				 // 4					//The Exist CE Bit Map (logical CE)
	U32 ulPhysicalCEBMP;		 // 4					//The Exist CE Bit Map (physical CE)
	U8 ubCENumberInCh[E17_ALLIGN_E13_MAX_CH];    // 4
	U8 ubCHNumberInCE[MAX_CE_PER_CHANNEL];		// S17:8, E13:4

	U16	uwNormalDMYByteLen[4];	//8					//Dummy data length per Frame
	U16	uwLDPCMode7DMYByteLen[4];	//8			//Dummy data length per Frame under LDPC Mode7
	FLASH_PARAMETER_PAGE ulParamPageInfo;
	U8 ubCE_Interface[MAX_CE];                  //Contains information on the Flash Environment
	U8 ubCE_DefaultInterface[MAX_CE];           //Flash default interface by channel detect
	U8 ubPageADRBitWidth; // 1					// Each Flash Page Address Bit Width
	U8 ubDefaultLDPCMode;
} FLH_ENV_STRUCT_t; //128Byte   ENV:environment
TYPE_SIZE_CHECK(FLH_ENV_STRUCT_t, DBUF_FW_FLH_ENV_SIZE);

typedef struct {
	U32 ulFailedCEBMP;		// 4					//CE failed bitmap
	U32 ulD1PECycle;		// 4
	U8 ubFIPDPSFeature;		// 1
	U8 ubNandPECycleRatio;          // 1					//PECycle Ratio of D1/D3
#if VRLC_EN
	U8 ubInfoBlkVRLCDiscardFlag;		// 1
	U8 ubReserved[21];	        // 21				// Reserved
#else /* VRLC_EN */
	U8 ubReserved[22];
#endif	/* VRLC_EN */
} FLH_ENV_STRUCT_MP_t;
TYPE_SIZE_CHECK(FLH_ENV_STRUCT_MP_t, DBUF_FW_FLH_ENV_MP_SIZE);

typedef struct {
	U32 ulZQCLFailLoc[MAX_CHANNEL];
	U8 ubNandZQCLStatus;
	U8 ubNandZQCLEn;
} FIP_ZQC_INFO_t;

#if PS5021_EN
#pragma pack(push)
#pragma pack(1)
typedef struct {
	union {
		struct {
			U32 ulL0;
			U32 ulL1;
			U32 ulL2;
			U32 ulL3;
		} ulong;

		struct {
			U32 ulLCA;

			U64 FW		: 24;
			U64 uwRsv   : 16;
			U64	ubValid	: 8;
			U64 uwRev1  : 16;

			U32 ubRsv2	: 8;
			U32 BADR	: 24;
		} common;

		struct {
			U32 ulLCA;

			U32 FW				: 24;
			U32 uwHeaderL		: 8;

			U64 uwHeaderH		: 8;
			U64 ubSpareValid	: 8;
			U64 E3D4K			: 24;
			U64 BADR			: 24;
		} Prog;

		struct {
			U32 ulLCA;

			U64 FW				: 24;
			U64	uwRsv           : 16;
			U64 ubBufferValid   : 8;
			U64 Zinfo			: 3;
			U64 btRsv			: 1;
			U64 PCA				: 2;
			U64 Rsv1			: 2;
			U64 ubRsv2			: 8;

			U32 ubRsv3			: 8;
			U32 BADR			: 24;
		} Read;

		struct {
			U32 ulLCA;

			U32 FW				: 24;
			U32 uwHeaderL		: 8;

			U64 uwHeaderH		: 8;
			U64 ubSpareValid	: 8;
			U64 E3D4K			: 24;
			U64 BADR			: 24;
		} Prog_NoBMU;

		struct {
			U32 ulLCA;

			U32 FW				: 24;
			U32 uwHeaderL		: 8;

			U64 uwHeaderH		: 8;
			U64 ubSpareValid	: 8;
			U64 E3D4K			: 24;
			U64 PB_ADDR			: 10;
			U64 LB_OFS 			: 10;
			U64 LB_ID			: 3;
			U64 btBMUCmdOption	: 1;
		} Prog_WithBMU;

		struct {
			U32 ulLCA;

			U64 ulFW			: 24;
			U64 uwRsv         	: 16;
			U64	ubBufferValid	: 8;
			U64 Zinfo			: 3;
			U64 btRsv			: 1;
			U64 PCA				: 2;
			U64 btRsv1			: 10;

			U32 ubRsv2			: 8;
			U32 BADR			: 23;
			U32 btLPCRC			: 1;
		} Read_Before_NoBMU;

		struct {
			U32 ulLCA;
			U64 FWSet			: 24;
			U64 uwHeader		: 16;
			U64 ubSpareValid	: 8;
			U64 ZInfo			: 3;
			U64 btReserve0		: 1;
			U64 PCA				: 2;
			U64 btReserve1		: 2;
			U64 ubBufValid		: 8;
			U32 ubReserve2		: 8;
			U32 BADR			: 24; // This naming follow L4K table format of FIP Spec.
		} ReadAfterWithSpareValidWithoutE3D;

		struct {
			U32 ulLCA;

			U64 ulFW			: 24;
			U64 uwRsv			: 16;
			U64	ubBufferValid	: 8;
			U64 Zinfo			: 3;
			U64 btRsv			: 1;
			U64 PCA				: 2;
			U64 btCTagH			: 1;
			U64 btAllocPBLink   : 1;
			U64 btRsv1			: 8;

			U32 ubCTagL			: 8;
			U32 PB_ADDR			: 10;
			U32 LB_OFS			: 10;
			U32 LB_ID			: 3;
			U32 btLPCRC			: 1;
		} Read_Before_WithBMU;

		struct {
			U32 ulLCA;

			U32 FW				: 24;
			U32	uwHeaderL		: 8;
			U64 uwHeaderH		: 8;
			U64 ubBufferValid	: 8;
			U64 E3D4K			: 24;
			U64 Rand			: 4;
			U64 CRC				: 4;
			U64 uwCRC16			: 16;
		} P4K;

		struct {
			U32 ulLCA;

			U64 ubFWRevisionID	: 8;
			U64 ubSectionID	    : 8;
			U64 ubCodeMark		: 8;
			U64 uwRsv           : 16;
			U64 ubBufferValid	: 8;
			U64 Zinfo			: 3;
			U64 btRsv			: 1;
			U64 PCA				: 2;
			U64 Rsv				: 2;
			U64 ubRsv1			: 8;

			U32 ubRsv2			: 8;
			U32 BADR			: 24;
		} Boot;
	} BitMap;
} L4KTable16B_t;
#pragma pack(pop)

#else /* PS5021_EN */

typedef struct {
	union {
		struct {
			U32 ulL0;
			U32 ulL1;
			U32 ulL2;
			U32 ulL3;
		} ulong;

		struct {
			U32 ulLCA;

			U32 FW		: 24;
			U32	ubValid	: 8;

			U32 ulL2;

			U32 ubRsv2	: 8;
			U32 BADR	: 24;
		} common;

		struct {
			U32 ulLCA;

			U32 FW				: 24;
			U32 ubSpareValid	: 8;

			U64 uwHeader		: 16;
			U64 E3D4K			: 24;
			U64 BADR			: 24;
		} Prog;
		struct {
			U32 ulLCA;

			U32 FW				: 24;
			U32	ubBufferValid	: 8;

			U32 uwL4kNextPtr	: 16;
			U32 Zinfo			: 3;
			U32 btRsv			: 1;
			U32 PCA				: 2;
			U32 Rsv1			: 2;
			U32 ubRsv2			: 8;

			U32 ubRsv3			: 8;
			U32 BADR			: 24;
		} Read;
		struct {
			U32 ulLCA;

			U32 FW				: 24;
			U32 ubSpareValid	: 8;

			U64 uwHeader		: 16;
			U64 E3D4K			: 24;
			U64 BADR			: 24;
		} Prog_NoBMU;

		struct {
			U32 ulLCA;

			U32 FW				: 24;
			U32 ubSpareValid	: 8;

			U64 uwHeader		: 16;
			U64 E3D4K			: 24;
			U64 PB_ADDR			: 9;
			U64 LB_OFS 			: 10;
			U64 LB_ID			: 3;
			U64 btFAR			: 1;
			U64 btBMUCmdOption	: 1;
		} Prog_WithBMU;

		struct {
			U32 ulLCA;

			U32 ulFW			: 24;
			U32	ubBufferValid	: 8;

			U32 uwL4kNextPtr	: 16;
			U32 Zinfo			: 3;
			U32 btRsv			: 1;
			U32 PCA				: 2;
			U32 btRsv1			: 1;
			U32 btCross			: 1;
			U32 ubRsv2			: 8;

			U32 ubRsv3			: 8;
			U32 BADR			: 23;
			U32 btLPCRC			: 1;
		} Read_Before_NoBMU;

		struct {
			U32 ulLCA;
			U32 FWSet			: 24;
			U32 ubSpareValid	: 8;
			U32 uwHeader		: 16;
			U32 ZInfo			: 3;
			U32 btReserve0			: 1;
			U32 PCA				: 2;
			U32 btReserve1			: 1;
			U32 btCrossPage			: 1;
			U32 ubBufValid		: 8;
			U32 ubReserve2			: 8;
			U32 BADR			: 24; // This naming follow L4K table format of FIP Spec.
		} ReadAfterWithSpareValidWithoutE3D;

		struct {
			U32 ulLCA;

			U32 FW				: 24;
			U32	ubBufferValid	: 8;

			U32 uwL4kNextPtr	: 16;
			U32 Zinfo			: 3;
			U32 btRsv0			: 1;
			U32 PCA				: 2;
			U32 btRsv1			: 1;
			U32 btCross			: 1;
			U32 ubRsv2			: 8;

			U32 ubRsv3			: 8;
			U32 PB_ADDR			: 9;
			U32 LB_OFS			: 10;
			U32 LB_ID			: 3;
			U32 btAllocPBLink   : 1;
			U32 btLPCRC			: 1;
		} Read_Before_WithBMU;
		struct {
			U32 ulLCA;

			U32 FW				: 24;
			U32	ubBufferValid	: 8;

			U64 uwHeader		: 16;
			U64 E3D4K			: 24;
			U64 Rand			: 4;
			U64 CRC				: 4;
			U64 uwCRC16			: 16;
		} P4K;
		struct {
			U32 ulLCA;

			U32  ubFWRevisionID	: 8;
			U32  ubSectionID	: 8;
			U32  ubCodeMark		: 8;
			U32  ubBufferValid	: 8;

			U32 uwL4kNextPtr	: 16;
			U32 Zinfo			: 3;
			U32 btRsv			: 1;
			U32 PCA				: 2;
			U32 Rsv				: 2;
			U32 ubRsv1			: 8;

			U32 ubRsv2			: 8;
			U32 BADR			: 24;
		} Boot;
	} BitMap;
} L4KTable16B_t;
#endif /* PS5021_EN */

extern volatile FLH_ENV_STRUCT_t gFlhEnv;		//Contains information on the Flash Environment
extern volatile FLH_ENV_STRUCT_MP_t gFlhEnvMP;		//Contains information on the Flash Environment for MP
extern volatile FLH_ENV_STRUCT_t *gpeFUSEParam;
extern volatile FIP_ZQC_INFO_t gFIPZQCInfo;

extern U8 gubFipCEmapping[MAX_PHYSICAL_CE_NUM];
extern U16 guwDMACntOneValue;
extern U16 guwLDPCIteration;
extern U8 gubNeedSendNCSFlag;
extern const U8 gubTADLValue[FIP_FLASH_CLOCK_NUM]; // Flash SPEC Time of Address to Data Loading

typedef struct {
	U32 Reserved0 : 12;
	U32 DieILBMP : 4; // bit12~bit15
	U32 PlaneP2LMapping : 8; // bit16~bit23 // Program & Erase Special //
	U32 btCacheCmd : 1; // bit24
	U32 btRemapBypass : 1; // bit25
	U32 btReserved1 : 1;
	U32 btToshibaFastRead : 1; // bit27
	U32 btWLStatusBypass : 1; // bit 28
	U32 Reserved2 : 1; //bit 29
	U32 btMixPlaneUnit : 1; //bit 30 // same Unit in Fail CE got src.plane != tar.plane, so whole Unit's cmd in Fail CE need "Manual Trigger".
	U32 btGenFail : 1;
} MTP_UserDefine_t;

#if (PS5021_EN)
typedef struct {
	// byte 0x28 ~ 0x2F
	U8 ubFSA0_RV_OFSA_0;
	U8 ubFSA0_RV_OFSA_1;
	U8 ubFSA0_RV_OFSA_2;
	U8 ubFSA0_RV_OFSA_3;
	U8 ubFSA0_RV_OFSA_4;
	U8 ubFSA0_RV_OFSA_5;
	U8 ubFSA0_RV_OFSA_6;
	U8 ubFSA0_RV_OFSA_7;

	// byte 0x30 ~ 0x37
	U8 ubFSA1_RV_OFSA_0;
	U8 ubFSA1_RV_OFSA_1;
	U8 ubFSA1_RV_OFSA_2;
	U8 ubFSA1_RV_OFSA_3;
	U8 ubFSA1_RV_OFSA_4;
	U8 ubFSA1_RV_OFSA_5;
	U8 ubFSA1_RV_OFSA_6;
	U8 ubFSA1_RV_OFSA_7;

	// byte 0x38 ~ 0x3F
	U8 ubFSA2_RV_OFSA_0;
	U8 ubFSA2_RV_OFSA_1;
	U8 ubFSA2_RV_OFSA_2;
	U8 ubFSA2_RV_OFSA_3;
	U8 ubFSA2_RV_OFSA_4;
	U8 ubFSA2_RV_OFSA_5;
	U8 ubFSA2_RV_OFSA_6;
	U8 ubFSA2_RV_OFSA_7;

	// byte 0x40 ~ 0x47
	U8 ubFSA3_RV_OFSA_0;
	U8 ubFSA3_RV_OFSA_1;
	U8 ubFSA3_RV_OFSA_2;
	U8 ubFSA3_RV_OFSA_3;
	U8 ubFSA3_RV_OFSA_4;
	U8 ubFSA3_RV_OFSA_5;
	U8 ubFSA3_RV_OFSA_6;
	U8 ubFSA3_RV_OFSA_7;

	// byte 0x48 ~ 0x4F
	U8 ubFSA0_RV_OFSA_8;
	U8 ubFSA0_RV_OFSA_9;
	U8 ubFSA1_RV_OFSA_8;
	U8 ubFSA1_RV_OFSA_9;
	U8 ubFSA2_RV_OFSA_8;
	U8 ubFSA2_RV_OFSA_9;
	U8 ubFSA3_RV_OFSA_8;
	U8 ubFSA3_RV_OFSA_9;
} FlhMTExtend_t;

typedef struct {
	// byte 0x00 ~ 0x03
	U32	btFirstOp		          : 1;						// Set high if the first MT of multiplane Program/Erase
	U32 btPFAInterruptEn          : 1;                        // 1        Y       program fail interrupt enable
	U32 btForce_Read_Fail         : 1;                        // 0        Y       gen. error
	U32 					      : 1;
	U32 btBusy                    : 1;                        // 0        N       follow by auto poll sequence
	U32 btMTPFormat               : 1;                        // 0        Y       indicate this is 40B format 0
	U32 btStatusNoStop            : 1;                        // 1        Y       not stop mtq when status compare fail
	U32	btNextUDMADisable         : 1;                        // 0        N       erase suspend enable
	U32 ubiFSA1_h                 : 8;                        // 0        N       internal fsa 1
	U32 ubiFSA2_h                 : 8;                        // 0        N       internal fsa 2
	U32 ubiFSA3_h                 : 8;                        // 0        N       internal fsa 3

	// byte 0x04 ~ 0x07
	U32 ConversionPage            : 9;                        // 0        Y       user defined conversion page index
	U32 btConversionBypass        : 1;                        // 0        Y       conversion bypass
	U32 btConversionPageEn        : 1;                        // 0        Y       user defined conversion page index function enable
	U32 ALUSelect                 : 2;                        // 0        N       index of alu rule
	U32 btDisableUDMA             : 1;                        // 0        Y       disable ultra dma rw
	U32 btInterruptVectorEn       : 1;                        // 1        Y       interrupt vector enable
	U32 btMTWarmUpBypass          : 1;                        // 1        Y       internal fsa enable
	U32 ubRECCPage                : 8;                        // 0        N       raidecc page index
	U32 btCheckStatus             : 1;                        // 1        Y       To inform HW this MT is a check status operation.
	U32 btRECCLastPage            : 1;                        // 0        N       last raidecc page program
	U32 btRECCParity_2nd_Page     : 1;                        // 0        N       program first/seconde raidecc page
	U32 btRECCDisParCnt           : 1;                        // 0        N       rollback raidecc parity by program flow, internal parity cnt--
	U32 btRECCOneParityEn         : 1;                        // 0        N       raidecc one parity
	U32 btCDQSHBypass             : 1;                        // 0        Y       Reserve
	U32 btMTQDelayEn              : 1;                        // 0        Y       delay between two mtq
	U32	btRSV1	                  : 1;                        // 0        Y       Reserve

	// byte 0x08 ~ 0x0B
	U32 NormalTargetCPU           : 3;                        // 0        N       receive success result cpu
	U32 ErrorTargetCPU            : 3;                        // 1        N       receive error result cpu
	U32 RandomizeSeedMode         : 2;                        // 0        Y       firmware random seed mode
	U32 InterruptVector           : 16;                       // 0        N
	U32 CnvWECnt                  : 8;                        // 0        N

	// byte 0x0C ~ 0x0F
	U32 btRECCParityTLCProgram    : 1;                        // 1        Y       program tlc raidecc parity buffer
	U32 btAllowSwitch             : 1;                        // 0        N       allow switch from/to QoS queue
	U32 btErase                   : 1;                        // 0        N       set 1 for erase (w_dma must set by 0), other case set 0
	U32 SpdRsmSeqSel              : 3;                        // 0        N       suspend sequence index
	U32 ADGSelectPointer          : 2;                        // 0        Y       2KB dma, 0 first 2KB, 1 second 2KB
	U32 btIoType                  : 1;                        // 0        N       0: single-end, 1:differential
	U32 btRSV2				      : 1;						  // 0
	U32 FCLK_DIV                  : 3;                        // 0        Y
	U32 btRaideccParityKeepPec	  : 1;						  //	Program es parity and keep PEC information
	U32 btRaideccParityDbuf		  : 1;						  //	Program rs parity write to DBUF
	U32         				  : 1;						  //
	U32 FlashType                 : 2;                        // 0        N       0: legacy, 1: toggle, 2: onfi
	U32 btTimeConfigSelect        : 1;                        // 0        Y       timing configure 0 or 1
	U32 btUpdPollingSequence      : 1;                        // 1        Y
	U32 btNoReadDMA               : 1;                        // 0        Y
	U32 btPTOEn                   : 1;                        // 0        N       polling timeout enable
	U32 PTOLengthSelect           : 2;                        // 0        N       polling timeout group select
	U32 ubFTA                     : 8;                        // 0        Y       FSA[7:0], also is cmd type of cop0: COP0_FMAN_CMD_READ, COP0_FMAN_CMD_WRITE...

	// byte 0x10 ~ 0x13
	U32 VA0                       : 8;                        // 0        Y	//[7:0] if btNoReadDMA = 1, VA0[0] is used to set NODMA_VALIDATE_EN.
	U32 VA1                       : 8;                        // 0        Y
	U32 ubiFSA0_h                 : 8;                        // 0        N
	U32 RSV3              		  : 2;                        // 0        N
	U32 btBMUAllocateEn           : 1;                        // 0        N
	U32 VAMode             		  : 2;                        // 0        Y
	U32 btRSV4                    : 1;						  // 0
	U32 CEDelayMode               : 2;                        // 0        Y

	// byte 0x14 ~ 0x17
	U32 uwFPUPtr                  : 16;                       // 0        N       fpu sequence offset from fpu base (fpu address = iram_base + FLHL_TOP_FPU_ADR_BASE + ulFpuPtr)
	U32 ubCEValue                 : 8;                        // 0        N       operation ce index or bitmap value
	U32	btFpuRdDatEn              : 1;						// 0
	U32	RSV5                      : 2;						// 0
	U32 POL_SEQ_SEL               : 4;                        // 1        N       polling fpu sequence offset
	U32 btCESelectMode            : 1;                        // 0        Y       1: value mode, 0: bitmap mode

	// byte 0x18 ~ 0x1B
	U32 uliFSA0_1;                                            // 0        N       lower byte of internal fsa 0

	// byte 0x1C ~ 0x1F
	U32 uliFSA1_1;                                            // 0        N       lower byte of internal fsa 1

	// byte 0x20 ~ 0x23
	U32 uliFSA2_1;                                            // 0        N       lower byte of internal fsa 2

	// byte 0x24 ~ 0x27
	union {
		U32 ulRdData;
		U32 uliFSA3_1;                                        // 0        N       lower byte of internal fsa 3
	} ;
#if FIP_MT_80B_EN
	FlhMTExtend_t FSA_RV_OFSA;
#endif /*FIP_MT_80B_EN*/
} FlhCmdMT_t ;

typedef struct {
	// byte 0x00 ~ 0x03
	U32	btFirstOp					: 1;						// Set high if the first MT of multiplane Program/Erase
	U32 btPFAInterruptEn              : 1;                        // 1        Y
	U32 btForce_R_Fail            : 1;                        // 0        N
	U32 btForce_W_Fail            : 1;                        // 0        N
	U32 btBusy                    : 1;                        // 0        N
	U32 btMTPFormat              : 1;                        // 1        Y       indicate this is 40B format 1
	U32 btStatusNoStop             : 1;                        // 1        Y
	U32 btNextUDMADisalbe            : 1;                        // 0        N
	U32 ConversionPage_H             : 4;                        // 0        Y
	U32 btLPCRCFail             : 1;
	U32	btLPCRCCheckEn     		: 1;
	U32	btPCACRCCheck			: 1;
	U32 btCheckStatus           : 1;                        // 0        Y
	U32 ubRECCSkipCount           : 8;                        // 0        N       raidecc skiped program page number
	U32 btUltra_W_EN              : 1;                        // 0        Y
	U32 btWriteDMA                   : 1;                        // 0        Y       set 1 for program (erase bit must set by 0), other case set 0
	U32 VAMode                        : 2;                        // 0        Y
	U32 ProgramBufferValid            : 4;                        // 0x0F     N

	// byte 0x04 ~ 0x07
	U32 ConversionPage               : 9;                        // 0        Y
	U32 btConversionBypass                : 1;                        // 0        Y
	U32 btConversionPageEn            : 1;                        // 0        Y
	U32 ALUSelect                 : 2;                        // 0        N
	U32 btDisableUDMA                : 1;                        // 0        Y
	U32 btInterruptVectorEn              : 1;                        // 1        Y
	U32 btMTWarmUpBypass                 : 1;                        // 1        Y
	U32 ubRECCPage               : 8;                        // 0        N
	U32 btLDPCCorrectEn             : 1;                        // 1        Y
	U32 btRECCLastPage          : 1;                        // 0        N
	U32 btRECCParity2ndPage    : 1;                        // 0        N
	U32 btRECCDisParityCount        : 1;                        // 0        N
	U32 btRECCOneParityEn      : 1;                        // 0        N
	U32 btBCHBps                 : 1;                        // 0        Y
	U32 btMTQDelayEn              : 1;                        // 0        Y
	U32 btEOTChkEn                : 1;                        // 0

	// byte 0x08 ~ 0x0B
	U32 NorTarCPU             : 3;                        // 0        N
	U32 ErrTarCPU             : 3;                        // 1        N
	U32 RandomSeedMode           : 2;                        // 0        Y
	U32 InterruptVector       : 16;                       // 0        N
	U32 CnvWECnt              : 8;

	// byte 0x0C ~ 0x0F
	U32 btRECCParityTLCProgram    : 1;                        // 0        N       program tlc raidecc parity buffer
	U32 btAllowSwitch            : 1;                        // 0        N
	U32 btErase                   : 1;                        // 0        N       set 1 for erase (w_dma must set by 0), other case set 0
	U32 SpdRsmSeqSel         : 3;                        // 0        N
	U32 ADGSelectPointer              : 2;                        // 0        Y       2KB dma, 0 first 2KB, 1 second 2KB
	U32 btIoType                 : 1;                        // 0        N       0: single-end, 1:differential
	U32 btFpuPCAEn				: 1;						// Y		1: HW get PCA from FTCL_PCA_CFG[31:0], 0 : HW get PCA from vector table
	U32 FCLK_DIV                : 3;                        // 0        Y
	U32 btRaideccParityKeepPec	: 1;						//	Program es parity and keep PEC information
	U32 btRaideccParityDbuf		: 1;						//	Program rs parity write to DBUF
	U32 btRberBps				: 1;						//	bypass error bit cnt calculation
	U32 FlashType                : 2;                        // 0        N       0: legacy, 1: toggle, 2: onfi
	U32 btTimeConfigSelect            : 1;                        // 0        Y       timing configure 0 or 1
	U32 btUpdPollingSequence             : 1;                        // 1        Y
	U32 btNoReadDMA                        : 1;                        // 0        Y    [DY] FW use for error handle
	U32 btPTOEn                  : 1;                        // 0        N       polling timeout enable
	U32 PTOLengthSelect             : 2;                        // 0        N       polling timeout group select
	U32 ubFTA                     : 8;                        // 0        Y       FSA[7:0], also is cmd type of cop0: COP0_FMAN_CMD_READ, COP0_FMAN_CMD_WRITE...

	// byte 0x10 ~ 0x13
	U32 VA0                 		: 8;                        // 0        Y
	U32 VA1        			        : 8;                        // 0        Y
	U32 ubiFSA0_h                 : 8;                        // 0        N
	U32 btScaleMode              : 2;                        // 0        N
	U32 btBMUAllocateEn             : 1;                        // 0        N
	U32 btCRCCheckDis             : 1;                        // 0        Y
	U32 btSgnDis                 : 4;                        // 0        Y

	// byte 0x14 ~ 0x17
	U32 uwFPUPtr                 : 16;                       // 0        N
	U32 ubCEValue                : 8;                        // 0        N
	U32 DecodeMode             : 3;                        // 0        N       0: HB decode mode for LDPC, 1: for HB+SB1+SB2+SB3+SB4
	U32 POL_SEQ_SEL             : 4;                        // 1        N
	U32 btCESelectMode             : 1;                        // 0        N

	// byte 0x18 ~ 0x1B
	U32 uliFSA0_1;

	// byte 0x1C ~ 0x1F
	U32 SeedInit               : 32;                       // 0        Y       ulSeedInit defined here combines ulSeedInit (22 bits) and ulCnvWecnt (10 bits) in fip spec

	// byte 0x20 ~ 0x23
	union {
		U32 ulUserDefine;                                         //                  previous VB
		struct {
			U32 Reserved0 : 12;
			U32 DieILBmp : 4; // bit12~bit15
			U32 PlaneBmp : 8; // bit16~bit23
			U32 btCacheCmd : 1; // bit24
			U32 btRmpBypass : 1; // bit25
			U32 btReserved1 : 1;
			U32 btToshibaFastRead : 1; // bit27
			U32 btWLStatusBypass : 1; // bit 28
			U32 btWLStatusBypassNeedSwitchStatus : 1;
			U32 btWLStatusBypasseXtraPageOverride : 1;
			U32 btGenFail : 1;
		} ulUserDefineReadDma;
		struct {
		} ulUserDefineProgramDma;
	};                                        //                  previous VB

	// byte 0x24 ~ 0x27
	U32 L4KSparePtr             : 17;                       // 0        N       l4k buffer pointer
	U32 btGC                      :  1;                       // 0        N       ulGc read/write mode (auto backup/restore fw spare byte) or normal read/write mode
	U32 btWDMAMaskFlhIO           :  1;                       // 0        N
	U32 L4KNum                 :  5;                       // 0        Y
	U32 FrameNum               :  3;                       // 0        Y       dma frame number
	U32 btZipEn                  :  1;                       // 0        Y
	U32 btCompareEn                  :  1;                       // 0        N
	U32 CEDelayMod                      :  2;                       // 0        Y
	U32 btBufferMode                :  1;                       // 0        N       bmu mode enable. 0: physical buffer address / 512, 1: PB or LB
#if FIP_MT_80B_EN
	FlhMTExtend_t FSA_RV_OFSA;
#endif /*FIP_MT_80B_EN*/
} FlhDmaMT_t;
#elif (PS5017_EN)

typedef struct {
	// byte 0x00 ~ 0x03
	//U32 btFPUEn                  : 1;                        // 1        Y
	U32 btFirstOp					: 1;						// Set high if the first MT of multiplane Program/Erase		//[0]
	U32 btPFAInterruptEn              : 1;                        // 1        Y       program fail interrupt enable
	U32 btForce_Read_Fail            : 1;                        // 0        Y       gen. error
	U32 btForce_Write_Fail            : 1;                        // 0        Y       gen. error
	U32 btBusy                    : 1;                        // 0        N       follow by auto poll sequence
	U32 btMTPFormat              : 1;                        // 0        Y       indicate this is 40B format 0
	U32 btStatusNoStop             : 1;                        // 1        Y       not stop mtq when status compare fail
	U32	btNXT_UDMA_DIS           : 1;                        // 0        N
	U32 ubiFSA1_h                 : 8;                        // 0        N       internal fsa 1
	U32 ubiFSA2_h                 : 8;                        // 0        N       internal fsa 2
	U32 ubiFSA3_h                 : 8;                        // 0        N       internal fsa 3

	// byte 0x04 ~ 0x07
	U32 ConversionPage               : 9;                        // 0        Y       user defined conversion page index
	U32 btConversionBypass                : 1;                        // 0        Y       conversion bypass
	U32 btConversionPageEn            : 1;                        // 0        Y       user defined conversion page index function enable
	U32 ALUSelect                 : 2;                        // 0        N       index of alu rule
	U32 btDisableUDMA                : 1;                        // 0        Y       disable ultra dma rw
	U32 btInterruptVectorEn              : 1;                        // 1        Y       interrupt vector enable
	U32 btiFSAEn                 : 1;                        // 1        Y       internal fsa enable
	U32 ubRECCPage               : 8;                        // 0        N       raidecc page index
	U32 btCheckStatus                : 1;                        // 1        Y       To inform HW this MT is a check status operation.	//[24]
	U32 btRECCLastPage          : 1;                        // 0        N       last raidecc page program
	U32 btRECCParity_2nd_Page    : 1;                        // 0        N       program first/seconde raidecc page
	U32 btRECCDisParCnt        : 1;                        // 0        N       rollback raidecc parity by program flow, internal parity cnt--
	U32 btRECCOneParityEn      : 1;                        // 0        N       raidecc one parity
	U32 btBCH_Bps                : 1;                        // 0        Y       BCH bypass
	U32 btMTQDelayEn              : 1;                        // 0        Y       delay between two mtq
	U32	btRSV	               : 1;                        // 0        Y       Reserve

	// byte 0x08 ~ 0x0B
	U32 NormalTargetCPU             : 3;                        // 0        N       receive success result cpu
	U32 ErrorTargetCPU             : 3;                        // 1        N       receive error result cpu
	U32 RandomizeSeedMode           : 2;                        // 0        Y       firmware random seed mode
	U32 InterruptVector                 : 24;                       // 0        N       interrupt vector

	// byte 0x0C ~ 0x0F
	U32 btRECCParityTLCProgram    : 1;                        // 1        Y       program tlc raidecc parity buffer
	U32 btAllowSwitch            : 1;                        // 0        N       allow switch from/to QoS queue
	U32 btErase                   : 1;                        // 0        N       set 1 for erase (w_dma must set by 0), other case set 0
	U32 SpdRsmSeqSel         : 3;                        // 0        N       suspend sequence index							//[5:3]
	U32 ADGSelectPointer              : 2;                        // 0        Y       2KB dma, 0 first 2KB, 1 second 2KB	//[7:6]
	U32 btIoType                 : 1;                        // 0        N       0: single-end, 1:differential				//[8]
	//U32 btFCLK_DIV_EN             : 1;                        // 0        Y
	U32 btRSV2				: 1;						// 0																//[9]
	U32 FCLK_DIV                : 3;                        // 0        Y													//[12:10]
	U32 btRaideccParityKeepPec	: 1;						//	Program es parity and keep PEC information					//[13]
	U32 btRaideccParityDbuf		: 1;						//	Program rs parity write to DBUF								//[14]
	U32 btRberBps				: 1;						//	bypass error bit cnt calculation							//[15],SPEC_FLH_2.9 without this description
	U32 FlashType                : 2;                        // 0        N       0: legacy, 1: toggle, 2: onfi
	U32 btTimeConfigSelect            : 1;                        // 0        Y       timing configure 0 or 1
	U32 btUpdPollingSequence             : 1;                        // 1        Y
	U32 btNoReadDMA                        : 1;                        // 0        Y
	U32 btPTOEn                  : 1;                        // 0        N       polling timeout enable
	U32 PTOLengthSelect             : 2;                        // 0        N       polling timeout group select
	U32 ubFTA                     : 8;                        // 0        Y       FSA[7:0], also is cmd type of cop0: COP0_FMAN_CMD_READ, COP0_FMAN_CMD_WRITE...

	// byte 0x10 ~ 0x13
	U32 VA0                 		: 8;                        // 0        Y	//[7:0] if btNoReadDMA = 1, VA0[0] is used to set NODMA_VALIDATE_EN.
	U32 VA1                			: 8;                        // 0        Y	//[15:8]
	U32 ubiFSA0_h                 	: 8;                        // 0        N	//[23:16]
	U32 RSV3              			: 2;                        // 0        N	//[25:24]
	U32 btBMUAllocateEn             : 1;                        // 0        N	//[26]
	U32 VAMode             			: 2;                        // 0        Y	//[28:27]
	U32 btRSV4						: 1;						// 0			//[29]
	U32 CEDelayMode                 : 2;                        // 0        Y	//[31:30]

	// byte 0x14 ~ 0x17
	U32 uwFPUPtr                 : 16;                       // 0        N       fpu sequence offset from fpu base (fpu address = iram_base + FLHL_TOP_FPU_ADR_BASE + ulFpuPtr)
	U32 ubCEValue                : 8;                        // 0        N       operation ce index or bitmap value
	//U32 btDecode_Mode            : 3;                        // 0        Y
	U32	btFpuRdDatEn				: 1;						// 0														//[24]
	U32	RSV5						: 2;						// 0
	U32 POL_SEQ_SEL             : 4;                        // 1        N       polling fpu sequence offset
	U32 btCESelectMode             : 1;                        // 0        Y       1: value mode, 0: bitmap mode

	// byte 0x18 ~ 0x1B
	U32 uliFSA0_1;                                            // 0        N       lower byte of internal fsa 0

	// byte 0x1C ~ 0x1F
	U32 uliFSA1_1;                                            // 0        N       lower byte of internal fsa 1

	// byte 0x20 ~ 0x23
	U32 uliFSA2_1;                                            // 0        N       lower byte of internal fsa 2

	// byte 0x24 ~ 0x27
	U32 uliFSA3_1;                                            // 0        N       lower byte of internal fsa 3

} FlhCmdMT_t ;

typedef struct {
	// byte 0x00 ~ 0x03
	//U32 btFPUEn                  : 1;                        // 1        Y
	U32 btFirstOp					: 1;						// Set high if the first MT of multiplane Program/Erase		//[0]
	U32 btPFAInterruptEn              : 1;                        // 1        Y
	U32 btForce_R_Fail            : 1;                        // 0        N
	U32 btForce_W_Fail            : 1;                        // 0        N
	U32 btBusy                    : 1;                        // 0        N
	U32 btMTPFormat              : 1;                        // 1        Y       indicate this is 40B format 1
	U32 btStatusNoStop             : 1;                        // 1        Y
	U32 btES_En                   : 1;                        // 0        N
	U32 ConversionPage_H             : 4;                        // 0        Y
	U32 btLPCRCFail             : 1;
	U32	btLPCRCCheck			: 1;
	U32	btPCACRCCheck			: 1;
	U32 btCheckStatus              : 1;                        // 0        Y
	U32 ubRECCSkipCount           : 8;                        // 0        N       raidecc skiped program page number
	U32 btUltra_W_EN              : 1;                        // 0        Y
	U32 btWriteDMA                   : 1;                        // 0        Y       set 1 for program (erase bit must set by 0), other case set 0
	U32 VAMode                         : 2;                        // 0        Y
	U32 ProgramBufferValid            : 4;                        // 0x0F     N

	// byte 0x04 ~ 0x07
	U32 ConversionPage               : 9;                        // 0        Y
	U32 btConversionBypass                : 1;                        // 0        Y
	U32 btConversionPageEn            : 1;                        // 0        Y
	U32 ALUSelect                 : 2;                        // 0        N
	U32 btDisableUDMA                : 1;                        // 0        Y
	U32 btInterruptVectorEn              : 1;                        // 1        Y
	U32 btiFSAEn                 : 1;                        // 1        Y
	U32 ubRECCPage               : 8;                        // 0        N
	U32 btLDPCCorrectEn             : 1;                        // 1        Y
	U32 btRECCLastPage          : 1;                        // 0        N
	U32 btRECCParity2ndPage    : 1;                        // 0        N
	U32 btRECCDisParityCount        : 1;                        // 0        N
	U32 btRECCOneParityEn      : 1;                        // 0        N
	U32 btBCHBps                 : 1;                        // 0        Y
	U32 btMTQDelayEn              : 1;                        // 0        Y
	U32 EOTChkEn                 : 1;                        // 0        Y   // 5011 no?

	// byte 0x08 ~ 0x0B
	U32 NorTarCPU             : 3;                        // 0        N
	U32 ErrTarCPU             : 3;                        // 1        N
	U32 RandomSeedMode           : 2;                        // 0        Y
	U32 InterruptVector                 : 24;                       // 0        N

	// byte 0x0C ~ 0x0F
	U32 btRECCParityTLCProgram    : 1;                        // 0        N       program tlc raidecc parity buffer
	U32 btAllowSwitch            : 1;                        // 0        N
	U32 btErase                   : 1;                        // 0        N       set 1 for erase (w_dma must set by 0), other case set 0
	U32 SpdRsmSeqSel         : 3;                        // 0        N																			//[5:3]
	U32 ADGSelectPointer              : 2;                        // 0        Y       2KB dma, 0 first 2KB, 1 second 2KB						//[7:6]
	U32 btIoType                 : 1;                        // 0        N       0: single-end, 1:differential									//[8]
	//U32 btFCLK_DIV_EN             : 1;                        // 0        Y
	U32 btFpuPCAEn				: 1;						// Y		1: HW get PCA from FTCL_PCA_CFG[31:0], 0 : HW get PCA from vector table	//[9]
	U32 FCLK_DIV                : 3;                        // 0        Y																		//[12:10]
	U32 btRaideccParityKeepPec	: 1;						//	Program es parity and keep PEC information										//[13]
	U32 btRaideccParityDbuf		: 1;						//	Program rs parity write to DBUF													//[14]
	U32 btRberBps				: 1;						//	bypass error bit cnt calculation
	U32 FlashType                : 2;                        // 0        N       0: legacy, 1: toggle, 2: onfi
	U32 btTimeConfigSelect            : 1;                        // 0        Y       timing configure 0 or 1
	U32 btUpdPollingSequence             : 1;                        // 1        Y
	U32 btNoReadDMA                        : 1;                        // 0        Y    [DY] FW use for error handle
	U32 btPTOEn                  : 1;                        // 0        N       polling timeout enable
	U32 PTOLengthSelect             : 2;                        // 0        N       polling timeout group select
	U32 ubFTA                     : 8;                        // 0        Y       FSA[7:0], also is cmd type of cop0: COP0_FMAN_CMD_READ, COP0_FMAN_CMD_WRITE...

	// byte 0x10 ~ 0x13
	U32 VA0                 		: 8;                        // 0        Y	//[7:0] if btNoReadDMA = 1, VA0[0] is used to set NODMA_VALIDATE_EN.
	U32 VA1        			        : 8;                        // 0        Y		//[15:8]
	U32 ubiFSA0_h                 : 8;                        // 0        N			//[23:16]
	U32 btScaleMode              : 2;                        // 0        N			//[25:24]
	U32 btBMUAllocateEn             : 1;                        // 0        N		//[26]
	U32 btCRCCheckDis             : 1;                        // 0        Y			//[27]
	U32 btSgnDis                 : 4;                        // 0        Y			//[31:28]

	// byte 0x14 ~ 0x17
	U32 uwFPUPtr                 : 16;                       // 0        N
	U32 ubCEValue                : 8;                        // 0        N
	U32 DecodeMode             : 3;                        // 0        N       0: HB decode mode for LDPC, 1: for HB+SB1+SB2+SB3+SB4
	U32 POL_SEQ_SEL             : 4;                        // 1        N
	U32 btCESelectMode             : 1;                        // 0        N

	// byte 0x18 ~ 0x1B
	U32 uliFSA0_1;

	// byte 0x1C ~ 0x1F
#if PS5017_EN
	U32 SeedInit               : 32;                       // 0        Y       ulSeedInit defined here combines ulSeedInit (22 bits) and ulCnvWecnt (10 bits) in fip spec
#else /* PS5017_EN */
	U32 SeedInit               : 22;                       // 0        Y       ulSeedInit defined here combines ulSeedInit (22 bits) and ulCnvWecnt (10 bits) in fip spec
	U32 CnvWecnt               : 10;                       // 0        N
#endif /* PS5017_EN */

	// byte 0x20 ~ 0x23
	union {
		U32 ulUserDefine;                                         //                  previous VB
		struct {
			U32 Reserved0 : 12;
			U32 DieILBmp : 4; // bit12~bit15
			U32 PlaneBmp : 8; // bit16~bit23
			U32 btCacheCmd : 1; // bit24
			U32 btRmpBypass : 1; // bit25
			U32 btReserved1 : 1;
			U32 btToshibaFastRead : 1; // bit27
			U32 btWLStatusBypass : 1; // bit 28
			U32 btWLStatusBypassNeedSwitchStatus : 1;
			U32 btWLStatusBypasseXtraPageOverride : 1;
			U32 btGenFail : 1;
		} ulUserDefineReadDma;
		struct {
			U32 Reserved0 : 8;
			U32 NewOPTStatus : 4; // bit8~bit11 // Set by FW // after Error Handle Mix Plane, Multiplane CMD may change to Singleplane CMD, so physical plane valid is changed, but OPT_STS in Vector Table already formed, and Vertor table cannot be modify, so we update the New OPTStatus here.
			U32 DieILBmp : 4; // bit12~bit15
			U32 PlaneP2LMapping : 8; // bit16~bit23 // Program & Erase Special //
			U32 btCacheCmd : 1; // bit24
			U32 btRmpBypass : 1; // bit25
			U32 btReserved1 : 1;
			U32 btToshibaFastRead : 1; // bit27
			U32 btWLStatusBypass : 1; // bit 28
			U32 btNewOPTStatusValid : 1; //bit 29 // Set by FW //when this flag is ON, Check Fail need to refer to MT.Userdefine.NewOPTStatus to know Physical Plane Valid, instead of refer to ErrorCQ.Userdefine.Userdefine
			U32 btMixPlaneUnit : 1; //bit 30 // // Set by FW //same Unit in Fail CE got src.plane != tar.plane, so whole Unit's cmd in Fail CE need "MixPlaneHandler".
			U32 btGenFail : 1;
		} ulUserDefineProgramDma;

	};

	// byte 0x24 ~ 0x27
	U32 L4KSparePtr             : 17;                       // 0        N       l4k buffer pointer
	U32 btGC                      :  1;                       // 0        N       ulGc read/write mode (auto backup/restore fw spare byte) or normal read/write mode
	U32                         :  1;                       // 0        N
	U32 L4KNum                 :  5;                       // 0        Y
	U32 FrameNum               :  3;                       // 0        Y       dma frame number
	U32 btZipEn                  :  1;                       // 0        Y
	U32 btCompareEn                  :  1;                       // 0        N
	U32 CEDelayMod                      :  2;                       // 0        Y
	U32 btBufferMode                :  1;                       // 0        N       bmu mode enable. 0: physical buffer address / 512, 1: PB or LB
} FlhDmaMT_t;
#else /* (PS5017_EN) */
typedef struct {
	// byte 0x00 ~ 0x03
	U32 btFPUEn                  : 1;                        // 1        Y       w/wo fpu seq trigger
	U32 btPFAInterruptEn              : 1;                        // 1        Y       program fail interrupt enable
	U32 btForce_Read_Fail            : 1;                        // 0        Y       gen. error
	U32 btForce_Write_Fail            : 1;                        // 0        Y       gen. error
	U32 btBusy                    : 1;                        // 0        N       follow by auto poll sequence
	U32 btMTPFormat              : 1;                        // 0        Y       indicate this is 40B format 0
	U32 btStatusNoStop             : 1;                        // 1        Y       not stop mtq when status compare fail
	U32			                : 1;                        // 0        N       erase suspend enable
	U32 ubiFSA1_h                 : 8;                        // 0        N       internal fsa 1
	U32 ubiFSA2_h                 : 8;                        // 0        N       internal fsa 2
	U32 ubiFSA3_h                 : 8;                        // 0        N       internal fsa 3

	// byte 0x04 ~ 0x07
	U32 ConversionPage               : 9;                        // 0        Y       user defined conversion page index
	U32 btConversionBypass                : 1;                        // 0        Y       conversion bypass
	U32 btConversionPageEn            : 1;                        // 0        Y       user defined conversion page index function enable
	U32 ALUSelect                 : 2;                        // 0        N       index of alu rule
	U32 btDisableUDMA                : 1;                        // 0        Y       disable ultra dma rw
	U32 btInterruptVectorEn              : 1;                        // 1        Y       interrupt vector enable
	U32 btiFSAEn                 : 1;                        // 1        Y       internal fsa enable
	U32 ubRECCPage               : 8;                        // 0        N       raidecc page index
	U32 btLDPCBps                : 1;                        // 1        Y       LDPC hard-bit correct enable
	U32 btRECCLastPage          : 1;                        // 0        N       last raidecc page program
	U32 btRECCParity_2nd_Page    : 1;                        // 0        N       program first/seconde raidecc page
	U32 btRECCDisParCnt        : 1;                        // 0        N       rollback raidecc parity by program flow, internal parity cnt--
	U32 btRECCOneParityEn      : 1;                        // 0        N       raidecc one parity
	U32 btBCH_Bps                : 1;                        // 0        Y       BCH bypass
	U32 btMTQDelayEn              : 1;                        // 0        Y       delay between two mtq
	U32	btRSV	               : 1;                        // 0        Y       Reserve

	// byte 0x08 ~ 0x0B
	U32 NormalTargetCPU             : 3;                        // 0        N       receive success result cpu
	U32 ErrorTargetCPU             : 3;                        // 1        N       receive error result cpu
	U32 RandomizeSeedMode           : 2;                        // 0        Y       firmware random seed mode
	U32 InterruptVector                 : 24;                       // 0        N       interrupt vector

	// byte 0x0C ~ 0x0F
	U32 btRECCParityTLCProgram    : 1;                        // 1        Y       program tlc raidecc parity buffer
	U32 btAllowSwitch            : 1;                        // 0        N       allow switch from/to QoS queue
	U32 btErase                   : 1;                        // 0        N       set 1 for erase (w_dma must set by 0), other case set 0
	U32 SpdRsmSeqSel         : 2;                        // 0        N       suspend sequence index
	U32 btCheckStatus                 : 1;                        // 0        Y
	U32 ADGSelectPointer              : 2;                        // 0        Y       2KB dma, 0 first 2KB, 1 second 2KB
	U32 btIoType                 : 1;                        // 0        N       0: single-end, 1:differential
	U32 btFCLK_DIV_EN             : 1;                        // 0        Y
	U32 FCLK_DIV                : 6;                        // 0        Y
	U32 FlashType                : 2;                        // 0        N       0: legacy, 1: toggle, 2: onfi
	U32 btTimeConfigSelect            : 1;                        // 0        Y       timing configure 0 or 1
	U32 btUpdPollingSequence             : 1;                        // 1        Y
	U32 btNoReadDMA                        : 1;                        // 0        Y
	U32 btPTOEn                  : 1;                        // 0        N       polling timeout enable
	U32 PTOLengthSelect             : 2;                        // 0        N       polling timeout group select
	U32 ubFTA                     : 8;                        // 0        Y       FSA[7:0], also is cmd type of cop0: COP0_FMAN_CMD_READ, COP0_FMAN_CMD_WRITE...

	// byte 0x10 ~ 0x13
	U32 FSASelect                 : 7;                        // 0        Y
	U32 FSA2Select                : 7;                        // 0        Y
	U32 FSA2Count                : 2;                        // 0        Y
	U32 ubiFSA0_h                 : 8;                        // 0        N
	U32 btScaleMode              : 2;                        // 0        N
	U32 btBMUAllocateEn             : 1;                        // 0        N
	U32 btCRCCheckDis             : 1;                        // 0        Y
	U32 btProgramE3DCheckDis        : 1;                        // 0        Y // 5011 no?
	U32 btSgnDis                 : 1;                        // 0        Y
	U32 btTBufferMode               : 1;                        // 0        N       0: channels internal buffer, 1: top internal buffer
	U32 btForeceE2EDisable       : 1;                        // 0        Y // 5011 no?

	// byte 0x14 ~ 0x17
	U32 uwFPUPtr                 : 16;                       // 0        N       fpu sequence offset from fpu base (fpu address = iram_base + FLHL_TOP_FPU_ADR_BASE + ulFpuPtr)
	U32 ubCEValue                : 8;                        // 0        N       operation ce index or bitmap value
	U32 btDecode_Mode            : 3;                        // 0        Y
	U32 POL_SEQ_SEL             : 4;                        // 1        N       polling fpu sequence offset
	U32 btCESelectMode             : 1;                        // 0        Y       1: value mode, 0: bitmap mode

	// byte 0x18 ~ 0x1B
	U32 uliFSA0_1;                                            // 0        N       lower byte of internal fsa 0

	// byte 0x1C ~ 0x1F
	U32 uliFSA1_1;                                            // 0        N       lower byte of internal fsa 1

	// byte 0x20 ~ 0x23
	U32 uliFSA2_1;                                            // 0        N       lower byte of internal fsa 2

	// byte 0x24 ~ 0x27
	U32 uliFSA3_1;                                            // 0        N       lower byte of internal fsa 3

} FlhCmdMT_t ;

typedef struct {
	// byte 0x00 ~ 0x03
	U32 btFirstOp					: 1;						// Set high if the first MT of multiplane Program/Erase		//[0]
	U32 btPFAInterruptEn              : 1;                        // 1        Y
	U32 btForce_R_Fail            : 1;                        // 0        N
	U32 btForce_W_Fail            : 1;                        // 0        N
	U32 btBusy                    : 1;                        // 0        N
	U32 btMTPFormat              : 1;                        // 1        Y       indicate this is 40B format 1 (dma)
	U32 btStatusNoStop             : 1;                        // 1        Y
	U32 btES_En                   : 1;                        // 0        N
	U32 ConversionPage_H             : 4;                        // 0        Y
	U32 btLPCRCFail             : 1;
	U32	btLPCRCCheck			: 1;
	U32	btPCACRCCheck			: 1;
	U32                         : 1;                        // 0        Y
	U32 ubRECCSkipCount           : 8;                        // 0        N       raidecc skiped program page number
	U32 btUltra_W_EN              : 1;                        // 0        Y
	U32 btWriteDMA                   : 1;                        // 0        Y       set 1 for program (erase bit must set by 0), other case set 0
	U32                         : 2;                        // 0        Y
	U32 ProgramBufferValid            : 4;                        // 0x0F     N

	// byte 0x04 ~ 0x07
	U32 ConversionPage               : 9;                        // 0        Y
	U32 btConversionBypass                : 1;                        // 0        Y
	U32 btConversionPageEn            : 1;                        // 0        Y
	U32 ALUSelect                 : 2;                        // 0        N
	U32 btDisableUDMA                : 1;                        // 0        Y
	U32 btInterruptVectorEn              : 1;                        // 1        Y
	U32 btiFSAEn                 : 1;                        // 1        Y
	U32 ubRECCPage               : 8;                        // 0        N
	U32 btLDPCCorrectEn             : 1;                        // 1        Y
	U32 btRECCLastPage          : 1;                        // 0        N
	U32 btRECCParity2ndPage    : 1;                        // 0        N
	U32 btRECCDisParityCount        : 1;                        // 0        N
	U32 btRECCOneParityEn      : 1;                        // 0        N
	U32 btBCHBps                 : 1;                        // 0        Y
	U32 btMTQDelayEn              : 1;                        // 0        Y
	U32                 : 1;                        // 0        Y   // 5011 no?

	// byte 0x08 ~ 0x0B
	U32 NorTarCPU             : 3;                        // 0        N
	U32 ErrTarCPU             : 3;                        // 1        N
	U32 RandomSeedMode           : 2;                        // 0        Y
	U32 InterruptVector                 : 24;                       // 0        N

	// byte 0x0C ~ 0x0F
	U32 btRECCParityTLCProgram    : 1;                        // 0        N       program tlc raidecc parity buffer
	U32 btAllowSwitch            : 1;                        // 0        N
	U32 btErase                   : 1;                        // 0        N       set 1 for erase (w_dma must set by 0), other case set 0
	U32 SpdRsmSeqSel         : 2;                        // 0        N
	U32 btCheckStatus                 : 1;                        // 0        Y
	U32 ADGSelectPointer              : 2;                        // 0        Y       2KB dma, 0 first 2KB, 1 second 2KB
	U32 btIoType                 : 1;                        // 0        N       0: single-end, 1:differential
	U32 btFCLK_DIV_EN             : 1;                        // 0        Y
	U32 FCLK_DIV                : 6;                        // 0        Y
	U32 FlashType                : 2;                        // 0        N       0: legacy, 1: toggle, 2: onfi
	U32 btTimeConfigSelect            : 1;                        // 0        Y       timing configure 0 or 1
	U32 btUpdPollingSequence             : 1;                        // 1        Y
	U32 btNoReadDMA                        : 1;                        // 0        Y    [DY] FW use for error handle
	U32 btPTOEn                  : 1;                        // 0        N       polling timeout enable
	U32 PTOLengthSelect             : 2;                        // 0        N       polling timeout group select
	U32 ubFTA                     : 8;                        // 0        Y       FSA[7:0], also is cmd type of cop0: COP0_FMAN_CMD_READ, COP0_FMAN_CMD_WRITE...

	// byte 0x10 ~ 0x13
	U32 FSASelect                 : 7;                        // 0        Y
	U32 FSA2Select                : 7;                        // 0        Y
	U32 FSA2Count                : 2;                        // 0        Y
	U32 ubiFSA0_h                 : 8;                        // 0        N
	U32 btScaleMode              : 2;                        // 0        N
	U32 btBMUAllocateEn             : 1;                        // 0        N
	U32 btCRCCheckDis             : 1;                        // 0        Y

	U32 btSgnDis                 : 4;                        // 0        Y			//[31:28]

	// byte 0x14 ~ 0x17
	U32 uwFPUPtr                 : 16;                       // 0        N
	U32 ubCEValue                : 8;                        // 0        N
	U32 DecodeMode             : 3;                        // 0        N       0: HB decode mode for LDPC, 1: for HB+SB1+SB2+SB3+SB4
	U32 POL_SEQ_SEL             : 4;                        // 1        N
	U32 btCESelectMode             : 1;                        // 0        N

	// byte 0x18 ~ 0x1B
	U32 uliFSA0_1;

	// byte 0x1C ~ 0x1F
	U32 SeedInit               : 22;                       // 0        Y       ulSeedInit defined here combines ulSeedInit (22 bits) and ulCnvWecnt (10 bits) in fip spec
	U32 CnvWecnt               : 10;                       // 0        N

	// byte 0x20 ~ 0x23
	union {
		U32 ulUserDefine;                                         //                  previous VB
		struct {
			U32 Reserved0 : 12;
			U32 DieILBmp : 4; // bit12~bit15
			U32 PlaneBmp : 8; // bit16~bit23
			U32 btCacheCmd : 1; // bit24
			U32 btRmpBypass : 1; // bit25
			U32 btReserved1 : 1;
			U32 btToshibaFastRead : 1; // bit27
			U32 btWLStatusBypass : 1; // bit 28
			U32 btWLStatusBypassNeedSwitchStatus : 1;
			U32 btWLStatusBypasseXtraPageOverride : 1;
			U32 btGenFail : 1;
		} ulUserDefineReadDma;
		struct {
			U32 Reserved0 : 8;
			U32 NewOPTStatus : 4; // bit8~bit11 // Set by FW // after Error Handle Mix Plane, Multiplane CMD may change to Singleplane CMD, so physical plane valid is changed, but OPT_STS in Vector Table already formed, and Vertor table cannot be modify, so we update the New OPTStatus here.
			U32 DieILBmp : 4; // bit12~bit15
			U32 PlaneP2LMapping : 8; // bit16~bit23 // Program & Erase Special //
			U32 btCacheCmd : 1; // bit24
			U32 btRmpBypass : 1; // bit25
			U32 btReserved1 : 1;
			U32 btToshibaFastRead : 1; // bit27
			U32 btWLStatusBypass : 1; // bit 28
			U32 btNewOPTStatusValid : 1; //bit 29 // Set by FW //when this flag is ON, Check Fail need to refer to MT.Userdefine.NewOPTStatus to know Physical Plane Valid, instead of refer to ErrorCQ.Userdefine.Userdefine
			U32 btMixPlaneUnit : 1; //bit 30 // // Set by FW //same Unit in Fail CE got src.plane != tar.plane, so whole Unit's cmd in Fail CE need "MixPlaneHandler".
			U32 btGenFail : 1;
		} ulUserDefineProgramDma;

	};

	// byte 0x24 ~ 0x27
	U32 L4KSparePtr             : 17;                       // 0        N       l4k buffer pointer
	U32 btGC                      :  1;                       // 0        N       ulGc read/write mode (auto backup/restore fw spare byte) or normal read/write mode
	U32                         :  1;                       // 0        N
	U32 L4KNum                 :  5;                       // 0        Y
	U32 FrameNum               :  3;                       // 0        Y       dma frame number
	U32 btZipEn                  :  1;                       // 0        Y
	U32 btCompareEn                  :  1;                       // 0        N
	U32                         :  1;                       // 0        Y
	U32 btForceFixData          :  1;                       // 0        Y       force read fixed data
	U32 btBufferMode                :  1;                       // 0        N       bmu mode enable. 0: physical buffer address / 512, 1: PB or LB
} FlhDmaMT_t;
#endif /* (PS5017_EN) */

typedef union {
	struct {
		U32 ulCtrlMap;
		U32 ulRsCfg;
		U32 ulIntCfg;
		U32 ulMiscCfg0;
		U32 ulMiscCfg1;
		U32 ulMiscCfg2;
		U32 ulIfsa0_1;
		U32 ulMiscCfg3;
		U32 ulMiscCfg4;
		U32 ulMiscCfg5;
	} u32;

	FlhCmdMT_t  cmd;
	FlhDmaMT_t dma;
} FlhMT_t;

typedef union {
	U8 ubAll;
	struct {
		U8 btAllDone	: 1;
		U8 btErasePage	: 1;
		U8 btDMADone	: 1;
		U8 btAbortDone	: 1;
		U8 btEOT		: 1;
		U8 btSTA		: 1;
		U8 btMTStop		: 1;
		U8 btPFA		: 1;
	};
} FlhIntInfo8Bit_t;

//----------------------------
// FIP Interrupt Format
//----------------------------
#if (PS5021_EN)
typedef union {
	struct {
		U64 uoAll;
		U64 uoAll2;
	};

	struct {
		U8 ubVector;
		U8 ubVector2;
		U8 ubVector3;
		FlhIntInfo8Bit_t Info;
		U32 TargetCPU 	      : 3;
		U32 btIndexMode       : 1;
		U32 btCPUInt	      : 1;
		U32 uwFailFlag	      : 16;
		U32 ERR_LVL           : 4;   //E21 Add
		U8 ECCFrm0BitFlipCnt  : 7;
		U8 btReserved1        : 1;
		U8 ECCFrm1BitFlipCnt  : 7;
		U8 btReserved2        : 1;
		U8 ECCFrm2BitFlipCnt  : 7;
		U8 btReserved3        : 1;
		U8 ECCFrm3BitFlipCnt  : 7;
		U8 btReserved4        : 1;
		U8 ECCFrm4BitFlipCnt  : 7;
		U8 btReserved5        : 1;
		U8 ECCFrm5BitFlipCnt  : 7;
		U8 btReserved6        : 1;
		U8 ECCFrm6BitFlipCnt  : 7;
		U8 btReserved7        : 1;
		U8 ECCFrm7BitFlipCnt  : 7;
		U8 ubReserved;
	} U128ForPrint;

	struct {
		U64 Index		: 7;
		U64 Rsv			: 2;
		U64 btQOS		: 1;
		U64 Rsv1		: 14;
		U64 btAllDone	: 1;
		U64 btErasePage	: 1;
		U64 btDMADone	: 1;
		U64 btAbortDone	: 1;
		U64 btEOT		: 1;
		U64 btSTA		: 1;
		U64 btMTStop	: 1;
		U64 btPFA		: 1;
		U64 TargetCPU 	: 3;
		U64 btIndexMode : 1;
		U64 btCPUInt	: 1;
		U64 uwFailFlag	: 16;
		U32 ERR_LVL     : 4;   //E21 Add
		U8 ECCFrm0BitFlipCnt  : 7;
		U8 btReserved1        : 1;
		U8 ECCFrm1BitFlipCnt  : 7;
		U8 btReserved2        : 1;
		U8 ECCFrm2BitFlipCnt  : 7;
		U8 btReserved3        : 1;
		U8 ECCFrm3BitFlipCnt  : 7;
		U8 btReserved4        : 1;
		U8 ECCFrm4BitFlipCnt  : 7;
		U8 btReserved5        : 1;
		U8 ECCFrm5BitFlipCnt  : 7;
		U8 btReserved6        : 1;
		U8 ECCFrm6BitFlipCnt  : 7;
		U8 btReserved7        : 1;
		U8 ECCFrm7BitFlipCnt  : 7;
		U8 ubReserved;
	};
} FlhIntMsg_t;
#else /* (PS5021_EN) */

typedef union {
	U64 uoAll;
	struct {
		U8 ubVector;
		U8 ubVector2;
		U8 ubVector3;
		FlhIntInfo8Bit_t Info;
		U32 TargetCPU 	: 3;
		U32 btIndexMode : 1;
		U32 btCPUInt	: 1;
		U32 uwFailFlag	: 16;
		U32 Rsv			: 11;
	} U64ForPrint;
	struct {
		//U64 Vector		: 24;
		U64 Index		: 7;
		U64 Rsv			: 2;
		U64 btQOS		: 1;
		U64 Rsv1		: 6;
		U64 ubProgBusyTimeLow	: 8;	//For TPROG, Reip
		U64 btAllDone	: 1;
		U64 btErasePage	: 1;
		U64 btDMADone	: 1;
		U64 btAbortDone	: 1;
		U64 btEOT		: 1;
		U64 btSTA		: 1;
		U64 btMTStop	: 1;
		U64 btPFA		: 1;
		U64 TargetCPU 	: 3;
		U64 btIndexMode : 1;
		U64 btCPUInt	: 1;
		U64 uwProgBusyTimeHigh	: 16;	//For TPROG, Reip
		U64 Rsv2		: 11;
	};
} FlhIntMsg_t;
#endif /* (PS5021_EN) */

typedef struct {
	U8 ubFrame;			// 1		// Page frame
	U8 ubPlane;			// 1
	U8 ubChannel;		// 1
	U8 ubFlashCE;		// 1

	U32 ulBlock;		// 4		// block per plane

	U16 uwPage;			// 2
	U8 ubLUN;			// 1
} FlhPCAInfo_t;

#if (PS5021_EN)
#if E21_TODO
typedef union {
	U32 ulAll;
	struct {
		U32 ubL4K_NUM0			: 5;
		U32 ubGRP0				: 3;
		U32 ubL4K_NUM1			: 5;
		U32 ubGRP1				: 3;
		U32 btPOL_SEQ_SEL_EXT0  : 1;
		U32 btBOOKING_CHK_BPS	: 1;
		U32 ubMT_DLY_CNT_SEL	: 4;
		U32 ubDIE_NUM			: 2;
		U32 btMTP_DEC_SIZE  	: 1;
		U32 ubLDPC_MODE 		: 4;
		U32 EOT_SEL             : 2;
		U32 btPOL_SEQ_SEL_EXT1	: 1;
	} ;
} MT_CFG_1_t;
#endif /* E21_TODO */

typedef union {
	U64 uoAll;

	struct {
		U32 ulMT_CFG0;
		U32 ulMT_CFG1;
	} u32;

	struct {
		U32 ubMT_IDX			: 7;
		U32 ubMTP_SMP_REQ		: 1;
		U32 ubRECC_TAG			: 9;
		U32 ubRECC_GRP_NUM		: 3;
		U32 btFRC_EMP			: 1;
		U32 btMT_SFT_LOCK		: 1;
		U32 btRECC_OTF_EN		: 1;
		U32 btQOS				: 1;
		U32 ubQUE_IDX			: 5;
		U32 btMT_HRD_LCK		: 1;
		U32 btRECC_PROG_PAR		: 1;
		U32 btMTP_GRO_PRI_DEF	: 1;

		U32 ubL4K_NUM0			: 5;
		U32 ubGRP0				: 3;
		U32 ubL4K_NUM1			: 5;
		U32 ubGRP1				: 3;
		U32 btPOL_SEQ_SEL_EXT0  : 1;
		U32 btBOOKING_CHK_BPS	: 1;
		U32 ubMT_DLY_CNT_SEL	: 4;
		U32 ubDIE_NUM			: 2;
		U32 btMTP_DEC_SIZE  	: 1;
		U32 ubLDPC_MODE 		: 4;
		U32 EOT_SEL             : 2;
		U32 btPOL_SEQ_SEL_EXT1	: 1;
	} bits_recc;

	struct {
		U32 ubMT_IDX			: 7;
		U32 ubMTP_SMP_REQ		: 1;
		U32 ubLB_OFS			: 10;
		U32 ubLB_OFS_RING		: 1;
		U32 ubLB_OFS_EN			: 1;
		U32 btFRC_EMP			: 1;
		U32 btMT_SFT_LOCK		: 1;
		U32 btRECC_OTF_EN		: 1;
		U32 btQOS				: 1;
		U32 ubQUE_IDX			: 5;
		U32 btMT_HRD_LCK		: 1;
		U32 btRECC_PROG_PAR		: 1;
		U32 btMTP_GRO_PRI_DEF	: 1;

		U32 ubL4K_NUM0			: 5;
		U32 ubGRP0				: 3;
		U32 ubL4K_NUM1			: 5;
		U32 ubGRP1				: 3;
		U32 btPOL_SEQ_SEL_EXT0  : 1;
		U32 btBOOKING_CHK_BPS	: 1;
		U32 ubMT_DLY_CNT_SEL	: 4;
		U32 ubDIE_NUM			: 2;
		U32 btMTP_DEC_SIZE  	: 1;
		U32 ubLDPC_MODE 		: 4;
		U32 EOT_SEL             : 2;
		U32 btPOL_SEQ_SEL_EXT1	: 1;
	} bits_lb_ofst;
} MTCfg_t;
#elif (PS5017_EN) /* (PS5021_EN) */

typedef union {
	U64 uoAll;

	struct {
		U32 ulMT_CFG0;
		U32 ulMT_CFG1;
	} u32;

	struct {
		U32 ubMT_IDX			: 7;
		U32 ubMTP_SMP_REQ		: 1;
		U32 ubRECC_TAG			: 9;
		U32 ubRECC_GRP_NUM		: 3;
		U32 btFRC_EMP			: 1;
		U32 btMT_SFT_LOCK		: 1;
		U32 btRECC_OTF_EN		: 1;
		U32 btQOS				: 1;
		U32 ubQUE_IDX			: 5;
		U32 btMT_HRD_LCK		: 1;
		U32 btRECC_PROG_PAR		: 1;
		U32 btMTP_GRO_PRI_DEF	: 1;

		U32 ubL4K_NUM0			: 5;
		U32 ubGRP0				: 3;
		U32 ubL4K_NUM1			: 5;
		U32 ubGRP1				: 3;
		U32 btCROSS_PAGE		: 1;
		U32 btBOOKING_CHK_BPS	: 1;
		U32 ubMT_DLY_CNT_SEL	: 4;
		U32 ubDIE_NUM			: 2;
		U32 btBDC_EN			: 1;
		U32 ubBDC_GRP			: 5;
		U32 ubRSV				: 2;
	} bits_recc;

	struct {
		U32 ubMT_IDX			: 7;
		U32 ubMTP_SMP_REQ		: 1;
		U32 ubLB_OFS			: 10;
		U32 ubLB_OFS_RING		: 1;
		U32 ubLB_OFS_EN			: 1;
		U32 btFRC_EMP			: 1;
		U32 btMT_SFT_LOCK		: 1;
		U32 btRECC_OTF_EN		: 1;
		U32 btQOS				: 1;
		U32 ubQUE_IDX			: 5;
		U32 btMT_HRD_LCK		: 1;
		U32 btRECC_PROG_PAR		: 1;
		U32 btMTP_GRO_PRI_DEF	: 1;

		U32 ubL4K_NUM0			: 5;
		U32 ubGRP0				: 3;
		U32 ubL4K_NUM1			: 5;
		U32 ubGRP1				: 3;
		U32 btCROSS_PAGE		: 1;
		U32 btBOOKING_CHK_BPS	: 1;
		U32 ubMT_DLY_CNT_SEL	: 4;
		U32 ubDIE_NUM			: 2;
		U32 btBDC_EN			: 1;
		U32 ubBDC_GRP			: 5;
		U32 ubRSV				: 2;
	} bits_lb_ofst;
} MTCfg_t;
#else /* (PS5021_EN) */
typedef union {
	U64 uoAll;

	struct {
		U32 ulMT_CFG0;
		U32 ulMT_CFG1;
	} u32;

	struct {
		U32 ubMT_IDX			: 7;
		U32 ubMTP_SMP_REQ		: 1;
		U32 ubLB_OFS_BIT0		: 1;
		U32 ubRECC_TAG			: 8;
		U32 ubRECC_GRP_NUM		: 3;
		U32 btFRC_EMP			: 1;
		U32 btMT_SFT_LOCK		: 1;
		U32 btRECC_OTF_EN		: 1;
		U32 btQOS				: 1;
		U32 ubQUE_IDX			: 5;
		U32 btMT_HRD_LCK		: 1;
		U32 btRECC_PROG_PAR		: 1;
		U32 btMTP_GRO_PRI_DEF	: 1;

		U32 ubL4K_NUM0			: 5;
		U32 ubGRP0				: 3;
		U32 ubL4K_NUM1			: 5;
		U32 ubGRP1				: 3;
		U32 btCROSS_PAGE		: 1;
		U32 btBOOKING_CHK_BPS	: 1;
		U32 uwRSV				: 14;
	} bits_recc;

	struct {
		U32 ubMT_IDX			: 7;
		U32 ubMTP_SMP_REQ		: 1;
		U32 ubLB_OFS			: 10;
		U32 ubLB_OFS_RING		: 1;
		U32 ubLB_OFS_EN			: 1;
		U32 btFRC_EMP			: 1;
		U32 btMT_SFT_LOCK		: 1;
		U32 btRECC_OTF_EN		: 1;
		U32 btQOS				: 1;
		U32 ubQUE_IDX			: 5;
		U32 btMT_HRD_LCK		: 1;
		U32 btRECC_PROG_PAR		: 1;
		U32 btMTP_GRO_PRI_DEF	: 1;

		U32 ubL4K_NUM0			: 5;
		U32 ubGRP0				: 3;
		U32 ubL4K_NUM1			: 5;
		U32 ubGRP1				: 3;
		U32 btCROSS_PAGE		: 1;
		U32 btBOOKING_CHK_BPS	: 1;
		U32 uwRSV				: 14;
	} bits_lb_ofst;
} MTCfg_t;
#endif /* (PS5021_EN) */

//======================================
//MT Template(64B) & Trigger Data(16B)
//======================================
#if (PS5021_EN)
typedef struct {         // use union type to optimize.
	union {
		U32 ulAll;
		struct {
			U32 rsv1                : 8;    // LSB
			U32 ubIFSA1_h             : 8;
			U32 ubIFSA2_h             : 8;
			U32 ubIFSA3_h             : 8;
		} fmt0_bits;
		struct {
			U32 btFirstOP              : 1;    // LSB
			U32 btPfa_int_en          : 1;
			U32 btForce_r_fail        : 1;
			U32 btForce_w_fail        : 1;
			U32 btBusy                : 1;
			U32 rsv1                  : 1;
			//U32 btFormat              : 1;
			U32 btSta_no_stop         : 1;
			U32 nxt_udma_dis        : 1;     // default: COP0_ATTR[16]
			U32 Conv_page_9_12      : 4;
			U32 btLpcrc_fail          : 1;
			U32 btLpcrc_chk          : 1;
			U32 btPcacrc_chk          : 1;
			U32 rsv2                : 1;
			U32 Rs_skip_cnt         : 8;
			U32 btUltra_w_en          : 1;
			U32 btW_dma               : 1;
			U32 rsv3                : 2;
			U32 prog_buf_valid      : 4;
		} bits;
	} dw0_ctrl_map;

	union {
		U32 ulAll;
		struct {
			U32 Conv_page           : 9;     // LSB
			U32 btConv_bps            : 1;
			U32 btConv_page_en        : 1;
			U32 ALU_Sel             : 2;
			U32 btDis_udma            : 1;
			U32 btInt_vct_en          : 1;
			U32 btIFSA_en             : 1;
			U32 ubRs_page             : 8;
			U32 ldpc_cor_en           : 1;            //default: 1
			U32 btRs_last_page        : 1;
			U32 btRs_parity_2nd_page  : 1;
			U32 btRs_dis_par_cnt      : 1;
			U32 btRs_one_parity_en    : 1;
			U32 bch_bps               : 1;            //default: 0
			U32 btMtq_dly_en          : 1;
			U32 eot_chk_en            : 1;       //default: COP0_ATTR[27]
		} bits;
	} dw1_rs_cfg;

	union {
		U32 ulAll;
		struct {
			U32 nor_tar_cpu         : 3;     // LSB
			U32 err_tar_cpu         : 3;
			U32 rnd_seed_mode       : 2;
			U32 int_vct             : 16;
			U32 cnv_wecnt           : 8;
		} bits;
	} dw2_int_cfg;

	union {
		U32 ulAll;
		struct {
			U16 btRs_parity_tlc_prog  : 1;        // LSB
			U32 btAllow_switch        : 1;
			U32 btErase               : 1;
			U32 Spd_rsm_seq_sel     : 3;        // suspend/resume sequence select.
			U32 Adg_sl_ptr          : 2;
			U32 btIo_type             : 1;			//U32 btFclk_div_en         : 1;
			U32 fpu_pca_en          : 1;  //default: 0
			U32 btFclk_div          : 3;  //default: COP0_CLKSW_ATTR[4:2]
			U32 raidecc_prog_keep_pec : 1;  //Tiein.PS_PROG_KEEP_PEC
			U32 raidecc_prog_dbuf   : 1;  //Tiein.PS_PROG_DBUF
			U32 rber_bps            : 1;  //default: 0
			U32 btFlh_type            : 2;
			U32 btTime_cfg_sel        : 1;
			U32 btUpd_pol_seq         : 1;
			U32 btNoReadDMA                : 1;
			U32 btPto_en              : 1;
			U32 Pto_len_sel         : 2;
			U32 ubFta_h               : 8;
		} bits;
	} dw3_misc_cfg_0;

	union {
		U32 ulAll;
		struct {
			U32 va0                 : 8;  //default: 0
			U32 va1                 : 8;  //default: 0
			U32 rsv0                : 8;
			U32 Scale_mode          : 2;
			U32 btBmu_alct_en       : 1;
			U32 btCrc_chk_dis       : 1;
			U32 sgn_dis0            : 1;
			U32 sgn_dis1            : 1;
			U32 sgn_dis2            : 1;
			U32 sgn_dis3            : 1;
		} bits;
	} dw4_misc_cfg_1;

	union {
		U32 ulAll;
		struct {
			U32 Fpu_ptr             : 16;     // LSB
			U32 ubCe_value          : 8;
			U32 Decode_mode         : 3;
			U32 POL_SEQ_SEL         : 4;
			U32 btCe_sel_mode       : 1;
		} bits;
	} dw5_misc_cfg_2;

	U32 dw6_RSV;

	union {
		U32 ulAll;
		struct {
			U32 Seed_init;   // LSB
		} bits;
	} dw7_seed_cfg;

	U32 dw8_UserDefine;

	union {
		U32 ulAll;
		struct {
			U32 L4k_spr_ptr         : 17;   // LSB
			U32 btGc                  : 1;
			U32 wdma_msk_flh_io     : 1;  //default: COP0_ATTR[28]
			U32 L4k_num             : 5;
			U32 Frame_num           : 3;
			U32 btZip_en              : 1;
			U32 btCmp_en              : 1;
			U32 rsv1                : 2;
			U32 btBuf_mode            : 1;
		} bits;

	} dw9_l4k_cfg;

	U32 dw10_iFSA0;

	union {
		U32 ulAll;
		struct {
			U8 ubIFSA0_h            : 8;
			U8 ubIFSA1_h            : 8;
			U8 ubIFSA2_h            : 8;
			U8 ubIFSA3_h            : 8;
		} bits;
	} dw11_iFSA_H;

	U32 dw12_iFSA1;
	U32 dw13_iFSA2;
	U32 dw14_iFSA3;

	union {
		U32 ulAll;
		struct {
			U32 ubVA0               : 8;
			U32 ubVA1               : 8;
			U32 ubVA2               : 8;
			U32 ubVA3               : 8;
		} bits;
	} dw15_VA;
} MtpMT_t;

///==========================
typedef struct {         // use union type to optimize.
	union {
		U32 all;
		struct {
			U32 OptStatus           : 8;    // LSB
			U32 par_rls               : 1;    // 2nd Byte
			U32 err_cnt_en            : 1;
			U32 cq_attr               : 1;
			U32 OptStatusB8           : 1;
			U32 PolSeqSelExt          : 2;
			U32 rsv1                  : 1;
			U32 d1                    : 1;

			U32 Seed_ini_src          : 1;
			U32 RRTB_VA_Vld           : 4;    // 1 from DCCM, 0 from MRIN SEED_INIT
			U32 RRTB_en               : 1;
			U32 mtp_stall             : 1;
			U32 mtp_high_prio         : 1;

			U32 ubL4KFW2              : 8;
		} bits;
	} dw0_data;

	union {
		U32 all;
		struct {
			U32 page_valid          : 4;
			U32 cmd                  : 3;
			U32 slc_mode             : 1;
			U32 seq_wr               : 1;    // Byte6
			U32 nor_cq_rsp         : 1;
			U32 q_idx                 : 6;
			U32 param_RAM_index        : 9;	// Byte7 ,8
			U32 barrier_cmd_01        : 2;
			U32 mt_format_set    : 1;
			U32 ior_en                 : 1;
			U32 zip_en                 : 1;
			U32 barrier_cmd_23   : 2;
		} bits;
	} dw1_data;

	U32 dw2_FCON_MT_CFG_1;

	union {
		U32 all;
		struct {
			U32 fw_rls_idx          : 9;
			U32 fw_rls_qos          : 1;
			U32 fw_rls_sel          : 1;
			U32 rsv0                : 1;
			U32 RRTB_fsa_vld        : 4;
			U32 chk_sts           : 1;
			U32 rber_bps          : 1;
			U32 mtq_dly           : 4;
			U32 die_num           : 2;
			U32 ce_delay_mode     : 2;
			U32 va_num            : 2;
			U32 fpu_get_feature   : 1;
			U32 rev1              : 3;
		} bits;
	} dw3_data;
} MtpMTData_t;
#elif (PS5017_EN) /* PS5021_EN */

typedef struct {         // use union type to optimize.
	union {
		U32 ulAll;
		struct {
			U32 rsv1                : 8;    // LSB
			U32 ubIFSA1_h             : 8;
			U32 ubIFSA2_h             : 8;
			U32 ubIFSA3_h             : 8;
		} fmt0_bits;
		struct {
			U32 btFirstOP              : 1;    // LSB
			U32 btPfa_int_en          : 1;
			U32 btForce_r_fail        : 1;
			U32 btForce_w_fail        : 1;
			U32 btBusy                : 1;
			U32 rsv1                  : 1;
			//U32 btFormat              : 1;
			U32 btSta_no_stop         : 1;
			U32 nxt_udma_dis        : 1;     // default: COP0_ATTR[16]
			U32 Conv_page_9_12      : 4;
			U32 btLpcrc_fail          : 1;
			U32 btLpcrc_chk          : 1;
			U32 btPcacrc_chk          : 1;
			U32 rsv2                : 1;
			U32 Rs_skip_cnt         : 8;
			U32 btUltra_w_en          : 1;
			U32 btW_dma               : 1;
			U32 rsv3                : 2;
			U32 prog_buf_valid      : 4;
		} bits;
	} dw0_ctrl_map;

	union {
		U32 ulAll;
		struct {
			U32 Conv_page           : 9;     // LSB
			U32 btConv_bps            : 1;
			U32 btConv_page_en        : 1;
			U32 ALU_Sel             : 2;
			U32 btDis_udma            : 1;
			U32 btInt_vct_en          : 1;
			U32 btIFSA_en             : 1;
			U32 ubRs_page             : 8;
			U32 ldpc_cor_en           : 1;            //default: 1
			U32 btRs_last_page        : 1;
			U32 btRs_parity_2nd_page  : 1;
			U32 btRs_dis_par_cnt      : 1;
			U32 btRs_one_parity_en    : 1;
			U32 bch_bps               : 1;            //default: 0
			U32 btMtq_dly_en          : 1;
			U32 eot_chk_en            : 1;       //default: COP0_ATTR[27]
		} bits;
	} dw1_rs_cfg;

	union {
		U32 ulAll;
		struct {
			U32 nor_tar_cpu         : 3;     // LSB
			U32 err_tar_cpu         : 3;
			U32 rnd_seed_mode       : 2;
			U32 int_vct             : 24;
		} bits;

	} dw2_int_cfg;

	union {
		U32 ulAll;
		struct {
			U16 btRs_parity_tlc_prog  : 1;        // LSB
			U32 btAllow_switch        : 1;
			U32 btErase               : 1;
			U32 Spd_rsm_seq_sel     : 3;        // suspend/resume sequence select.
			//U32 chk_sts             : 1;
			U32 Adg_sl_ptr          : 2;
			U32 btIo_type             : 1;
			//U32 btFclk_div_en         : 1;
			//U32 btFclk_div            : 6;
			U32 fpu_pca_en          : 1;  //default: 0
			U32 btFclk_div          : 3;  //default: COP0_CLKSW_ATTR[4:2]
			U32 raidecc_prog_keep_pec : 1;  //Tiein.PS_PROG_KEEP_PEC
			U32 raidecc_prog_dbuf   : 1;  //Tiein.PS_PROG_DBUF
			U32 rber_bps            : 1;  //default: 0
			U32 btFlh_type            : 2;
			U32 btTime_cfg_sel        : 1;
			U32 btUpd_pol_seq         : 1;
			U32 btNoReadDMA                : 1;
			U32 btPto_en              : 1;
			U32 Pto_len_sel         : 2;
			U32 ubFta_h               : 8;
		} bits;
	} dw3_misc_cfg_0;

	union {
		U32 ulAll;
		struct {
			U32 va0                 : 8;  //default: 0
			U32 va1                 : 8;  //default: 0
			U32 rsv0                : 8;
			U32 Scale_mode          : 2;
			U32 btBmu_alct_en       : 1;
			U32 btCrc_chk_dis       : 1;
			U32 sgn_dis0            : 1;
			U32 sgn_dis1            : 1;
			U32 sgn_dis2            : 1;
			U32 sgn_dis3            : 1;
		} bits;
	} dw4_misc_cfg_1;

	union {
		U32 ulAll;
		struct {
			U32 Fpu_ptr             : 16;     // LSB
			U32 ubCe_value            : 8;
			U32 Decode_mode     : 3;
			U32 POL_SEQ_SEL         : 4;
			U32 btCe_sel_mode         : 1;
		} bits;

	} dw5_misc_cfg_2;

	U32 dw6_RSV;

	union {
		U32 ulAll;
		struct {
			U32 Seed_init           : 22;   // LSB
			U32 Conv_we_cnt         : 10;
		} bits;

	} dw7_seed_cfg;

	union {
		U32 ulAll;
		MTP_UserDefine_t bits;
	} dw8_UserDefine;

	union {
		U32 ulAll;
		struct {
			U32 L4k_spr_ptr         : 17;   // LSB
			U32 btGc                  : 1;
			U32 wdma_msk_flh_io     : 1;  //default: COP0_ATTR[28]
			U32 L4k_num             : 5;
			U32 Frame_num           : 3;
			U32 btZip_en              : 1;
			U32 btCmp_en              : 1;
			U32 rsv1                : 2;
			U32 btBuf_mode            : 1;
		} bits;

	} dw9_l4k_cfg;

	U32 dw10_iFSA0;

	union {
		U32 ulAll;
		struct {
			U8 ubIFSA0_h             : 8;
			U8 ubIFSA1_h            : 8;
			U8 ubIFSA2_h            : 8;
			U8 ubIFSA3_h            : 8;
		} bits;
	} dw11_iFSA_H;

	U32 dw12_iFSA1;

	U32 dw13_iFSA2;

	U32 dw14_iFSA3;

	U32 dw15_NoUse;
} MtpMT_t;


///==========================
typedef struct {         // use union type to optimize.
	union {
		U32 all;
		struct {
			U32 OptStatus           : 8;    // LSB
			U32 par_rls               : 1;    // 2nd Byte
			U32 rsv0                   : 1;
			U32 cq_attr               : 1;
			U32 rsv1                  : 4;
			U32 d1                      : 1;
			U32 seed_init_src           : 1;    // 1 from DCCM, 0 from MRIN SEED_INIT
			U32 pca_sel               : 4;
			U32 rsv2                   : 1;
			U32 mtp_stall            : 1;
			U32 rsv3                   : 1;
			U32 bc_group                : 5;    // base on PCA rule and FSA, calculate bad column group for FIP.
			U32 rsv4                   : 2;
			U32 bc_en                   : 1;    // bad column enable for FIP reference
		} bits;
	} dw0_data;

	union {
		U32 all;
		struct {
			U32 page_valid          : 4;
			U32 cmd                  : 3;
			U32 slc_mode             : 1;
			U32 seq_wr               : 1;    // Byte6
			U32 nor_cq_rsp         : 1;
			U32 q_idx                 : 6;
			U32 param_RAM_index        : 9;	// Byte7 ,8
			U32 barrier_cmd_01        : 2;
			U32 mt_format_set    : 1;
			U32 ior_en                 : 1;
			U32 zip_en                 : 1;
			U32 barrier_cmd_23   : 2;
		} bits;
	} dw1_data;

	union {
		U32 all;
		struct {
			U32 ubMT_IDX			: 7;
			U32 ubMTP_SMP_REQ		: 1;
			U32 ubLB_OFS_BIT0		: 1;
			U32 ubRECC_TAG			: 8;
			U32 ubRECC_GRP_NUM		: 3;
			U32 btFRC_EMP			: 1;
			U32 btMT_SFT_LOCK		: 1;
			U32 btRECC_OTF_EN		: 1;
			U32 btQOS				: 1;
			U32 ubQUE_IDX			: 5;
			U32 btMT_HRD_LCK		: 1;
			U32 btRECC_PROG_PAR 	: 1;
			U32 btMTP_GRO_PRI_DEF	: 1;
		} bits;
	} dw2_data;

	union {
		U32 all;
		struct {
			U32 fw_rls_idx          : 9;
			U32 fw_rls_qos          : 1;
			U32 rsv0                    : 5;
			U32 fw_rls_sel           : 1;
			U32 chk_sts           : 1;
			U32 rber_bps          : 1;
			U32 mtq_dly           : 4;
			U32 die_num           : 2;
			U32 ce_delay_mode     : 2;
			U32 va_num            : 2;
			U32 fpu_get_feature   : 1;
			U32 rev1              : 3;
		} bits;
	} dw3_data;

} MtpMTData_t;
#else /* (PS5021_EN) */
typedef struct {         // use union type to optimize.
	union {
		U32 ulAll;
		struct {
			U32 rsv1                : 8;    // LSB
			U32 ubIFSA1_h             : 8;
			U32 ubIFSA2_h             : 8;
			U32 ubIFSA3_h             : 8;
		} fmt0_bits;
		struct {
			U32 btFirstOP              : 1;    // LSB
			U32 btPfa_int_en          : 1;
			U32 btForce_r_fail        : 1;
			U32 btForce_w_fail        : 1;
			U32 btBusy                : 1;
			U32 btFormat              : 1;
			U32 btSta_no_stop         : 1;
			U32 btEs_en               : 1;
			U32 Conv_page_9_12      : 4;
			U32 btLpcrc_fail          : 1;
			U32 btLpcrc_chk          : 1;
			U32 btPcacrc_chk          : 1;
			U32 rsv2                : 1;
			U32 Rs_skip_cnt         : 8;
			U32 btUltra_w_en          : 1;
			U32 btW_dma               : 1;
			U32 rsv3                : 2;
			U32 prog_buf_valid      : 4;
		} bits;
	} dw0_ctrl_map;

	union {
		U32 ulAll;
		struct {
			U32 Conv_page           : 9;     // LSB
			U32 btConv_bps            : 1;
			U32 btConv_page_en        : 1;
			U32 ALU_Sel             : 2;
			U32 btDis_udma            : 1;
			U32 btInt_vct_en          : 1;
			U32 btIFSA_en             : 1;
			U32 ubRs_page             : 8;
			U32 btLdpc_bps            : 1;
			U32 btRs_last_page        : 1;
			U32 btRs_parity_2nd_page  : 1;
			U32 btRs_dis_par_cnt      : 1;
			U32 btRs_one_parity_en    : 1;
			U32 rsv2                : 1;
			U32 btMtq_dly_en          : 1;
			U32 rsv            : 1;
		} bits;
	} dw1_rs_cfg;

	union {
		U32 ulAll;
		struct {
			U32 nor_tar_cpu         : 3;     // LSB
			U32 err_tar_cpu         : 3;
			U32 rnd_seed_mode       : 2;
			U32 int_vct             : 24;
		} bits;

	} dw2_int_cfg;

	union {
		U32 ulAll;
		struct {
			U16 btRs_parity_tlc_prog  : 1;        // LSB
			U32 btAllow_switch        : 1;
			U32 btErase               : 1;
			U32 Spd_rsm_seq_sel     : 3;        // suspend/resume sequence select.
			//U32 chk_sts             : 1;
			U32 Adg_sl_ptr          : 2;
			U32 btIo_type             : 1;
			U32 btFclk_div_en         : 1;
			U32 btFclk_div            : 6;
			U32 btFlh_type            : 2;
			U32 btTime_cfg_sel        : 1;
			U32 btUpd_pol_seq         : 1;
			U32 btNoReadDMA                : 1;
			U32 btPto_en              : 1;
			U32 Pto_len_sel         : 2;
			U32 ubFta_h               : 8;
		} bits;
	} dw3_misc_cfg_0;

	union {
		U32 ulAll;
		struct {
			U32 Fsa_sel             : 7;     // LSB
			U32 Fsa2_sel            : 7;
			U32 Fsa2_cnt            : 2;
			U32 rsv0                : 8;
			U32 Scale_mode     : 2;
			U32 btBmu_alct_en    : 1;
			U32 btCrc_chk_dis      : 1;
			U32 Sgn_dis             : 4;
		} bits;
	} dw4_misc_cfg_1;

	union {
		U32 ulAll;
		struct {
			U32 Fpu_ptr             : 16;     // LSB
			U32 ubCe_value            : 8;
			U32 Decode_mode     : 3;
			U32 POL_SEQ_SEL         : 4;
			U32 btCe_sel_mode         : 1;
		} bits;

	} dw5_misc_cfg_2;

	U32 dw6_RSV;

	union {
		U32 ulAll;
		struct {
			U32 Seed_init           : 22;   // LSB
			U32 Conv_we_cnt         : 10;
		} bits;

	} dw7_seed_cfg;

	union {
		U32 ulAll;
		MTP_UserDefine_t bits;
	} dw8_UserDefine;

	union {
		U32 ulAll;
		struct {
			U32 L4k_spr_ptr         : 17;   // LSB
			U32 btGc                  : 1;
			U32 rsv1                : 1;
			U32 L4k_num             : 5;
			U32 Frame_num           : 3;
			U32 btZip_en              : 1;
			U32 btCmp_en              : 1;
			U32 rsv2                : 1;
			U32 btForce_fix_data      : 1;    // only for read
			U32 btBuf_mode            : 1;
		} bits;

	} dw9_l4k_cfg;

	U32 dw10_iFSA0;

	union {
		U32 ulAll;
		struct {
			U8 ubIFSA0_h             : 8;
			U8 ubIFSA1_h            : 8;
			U8 ubIFSA2_h            : 8;
			U8 ubIFSA3_h            : 8;
		} bits;
	} dw11_iFSA_H;

	U32 dw12_iFSA1;

	U32 dw13_iFSA2;

	U32 dw14_iFSA3;

	U32 dw15_NoUse;
} MtpMT_t;


///==========================
typedef struct {         // use union type to optimize.
	union {
		U32 all;
		struct {
			U32 OptStatus           : 8;    // LSB
			U32 par_rls               : 1;    // 2nd Byte
			U32 rsv0                   : 1;
			U32 cq_attr               : 1;
			U32 fsa_vld               : 4;
			U32 d1                      : 1;
			U32 rsv1                   : 1;	// 3rd Byte
			U32 pca_sel               : 4;
			U32 rsv2                   : 1;
			U32 mtp_stall            : 1;
			U32 rsv3                   : 1;
			U32 l4k_fw2              : 8;	// 4th Byte
		} bits;
	} dw0_data;

	union {
		U32 all;
		struct {
			U32 page_valid          : 4;
			U32 cmd                  : 3;
			U32 slc_mode             : 1;
			U32 seq_wr               : 1;    // Byte6
			U32 nor_cq_rsp         : 1;
			U32 q_idx                 : 6;
			U32 param_RAM_index        : 9;	// Byte7 ,8
			U32 barrier_cmd_01        : 2;
			U32 mt_format_set    : 1; // format 0: cmd, 1: dma
			U32 ior_en                 : 1;
			U32 zip_en                 : 1;
			U32 barrier_cmd_23   : 2;
		} bits;
	} dw1_data;

	union {
		U32 all;
		struct {
			U32 ubMT_IDX			: 7;
			U32 ubMTP_SMP_REQ		: 1;
			U32 ubLB_OFS_BIT0		: 1;
			U32 ubRECC_TAG			: 8;
			U32 ubRECC_GRP_NUM		: 3;
			U32 btFRC_EMP			: 1;
			U32 btMT_SFT_LOCK		: 1;
			U32 btRECC_OTF_EN		: 1;
			U32 btQOS				: 1;
			U32 ubQUE_IDX			: 5;
			U32 btMT_HRD_LCK		: 1;
			U32 btRECC_PROG_PAR 	: 1;
			U32 btMTP_GRO_PRI_DEF	: 1;
		} bits;
	} dw2_data;

	union {
		U32 all;
		struct {
			U32 fw_rls_idx          : 10;
			U32 fw_rls_qos          : 1;
			U32 rsv0                    : 4;
			U32 fw_rls_sel           : 1;
			U32 rsv1                    : 16;
		} bits;
	} dw3_data;

} MtpMTData_t;
#endif /* (PS5021_EN) */

typedef struct DQTrain {
	U8 ubRxBitValue		: 4;
	U8 ubTxBitValue		: 4;
} DQTrain_t;
typedef struct DQTrainResult {
	DQTrain_t	DQ[FIP_DQ_BIT_SIZE];
} DQTrainResult_t;

typedef union {		//For TPROG, Reip
	U32 ubAll;
	struct {
		U32 ubProgBusyTimeLow	: 8;
		U32 uwProgBusyTimeHigh	: 16;
		U32 rsv					: 8;
	};
} RDYBusyTime32Bit_t;

typedef struct {
	U8 ubHead;
	U8 ubTail;
	U8 ubFreeMTCnt;
	U8 ubRecordExtInfoMT; //mt index that need to record special information
	U8 ubFreeMTPool[MT_RETRY_NUM];
	FlhIntInfo8Bit_t ubMTDoneMsg[MT_RETRY_NUM];
	RDYBusyTime32Bit_t	ubRDYBusyTime[MT_RETRY_NUM];	//For TPROG, Reip
	U32 ulINTInfo[MT_RETRY_NUM];
	U32 ulMapInfo[MT_RETRY_NUM];
	U16	uwPTYCHSUM[FRAMES_PER_PAGE]; //record INI_PTYCHSUM
	U16 uwECCErrorBitNum;  // only used in partial block media scan
#if PS5021_EN
	union {
		U16 all;
		struct {
			U16 pol_seq_sel_ext : 2;
			U16 rsv 			: 14;
		} A;
		struct {
			U16 pol_seq_sel_ext0 : 1;
			U16 pol_seq_sel_ext1 : 1;
			U16 rsv 			 : 14;
		} B;
	} MTCfg1Temp;
#else /* PS5021_EN */
	U16 uwReserved;
#endif /* PS5021_EN */
} MTMgr_t;


typedef struct {
	MTCfg_t uoBackupMTCfg[MAX_CHANNEL][MAX_CE_PER_CHANNEL][MTQ_DEPTH];
	U8  ubNonExecuteNum[MAX_CHANNEL][MAX_CE_PER_CHANNEL];
} MTBackup_t;
typedef struct SingleBankSdllValue {
	union {
		U32 ulAll;
		struct {
			U16 uwSdllMin;
			U16 uwSdllMax;
		} A;
	} B;
} SingleBankSdllValue_t;
typedef struct BankSdllValue {
	SingleBankSdllValue_t SingleBankSdllValue[MAX_LUN_NUM];
} BankSdllValue_t;
typedef struct  TotalRWSdll {
	BankSdllValue_t BankSdllValue[MAX_PHYSICAL_CE_PER_CH];	//
} TotalRWSdll_t;
typedef struct FAE_LOG {
	TotalRWSdll_t Read;
	TotalRWSdll_t Write;
} FAE_LOG_t;
typedef struct ScanWindowParam {
	U8 ubChannel;
	U8 ubCE;
	U8 ubDie;
	U8 btTestWriteFinishW: 1;
	U8 btTestReadFinishW: 1;
	U8 btTestWriteFinishR: 1;
	U8 btTestReadFinishR: 1;
	U8 btWPathFinish: 1;
	U8 btRPathFinish: 1;
	U8 btDieFinish: 1;
	U8 btCEFinish: 1;
	U8 btChannelFinish: 1;
	U8 btInit: 1;
	U8 ubRsv: 6;
	U8 ubDQBitPos;
	union {
		U8 ubAll;
		struct {
			U8 btFindDQSDLY: 1;
			U8 btDQTXFinish: 1;
			U8 btDQTXInitial: 1;
			U8 btDQRXFinish: 1;
			U8 btDQRXInitial: 1;
			U8 btDQBitFinish: 1;
			U8 btValueFinish: 1;
			U8 btReserv: 1;
		} FINISHBT;
	} B1;
	U8 ubValue;
	U16 uwReadWindowOffset;
	U16 uwWriteWindowOffset;
	U32 ulPatternAddr;
	U32 ulPatternSize;
} ScanWindowParam_t;

#if (PS5017_EN || PS5021_EN)
typedef struct ScanWindowPerBitTrain {
	U16 uwFipPassValue[MAX_PHYSICAL_CE_NUM][MAX_LUN_NUM];
	U8 ubFipDQTrainResult[MAX_PHYSICAL_CE_NUM][MAX_LUN_NUM];
	U8 ubFipTrainDQSFindPass;
	U16 uwFipPassValue_temp[MAX_PHYSICAL_CE_NUM][MAX_LUN_NUM];
	U8 ubContinuousFailedCnt[MAX_PHYSICAL_CE_NUM][MAX_LUN_NUM];
} ScanWindowPerBitTrain_t;
#else /*(PS5017_EN || PS5021_EN)*/
typedef struct ScanWindowPerBitTrain {
	U16 uwFipScanWindowWriteAlignValue[MAX_PHYSICAL_CE_NUM][MAX_LUN_NUM];
	U16 uwFipScanWindowReadAlignValue[MAX_PHYSICAL_CE_NUM][MAX_LUN_NUM];
	U16 uwFipPassValue[MAX_PHYSICAL_CE_NUM][MAX_LUN_NUM];
	U16 uwFipPerBitPassValue[MAX_PHYSICAL_CE_NUM][MAX_LUN_NUM][FIP_DQ_GROUP_CNT];
	U8 ubFipDQTrainResult[MAX_PHYSICAL_CE_NUM][MAX_LUN_NUM];
	U8 ubFipFindTXLastPassBit[MAX_PHYSICAL_CE_NUM][MAX_LUN_NUM];
	U8 ubFipFindRXLastPassBit[MAX_PHYSICAL_CE_NUM][MAX_LUN_NUM];
	U8 ubDQTrainGroupFlag;
	U8 ubFipTrainDQSFindPass;
} ScanWindowPerBitTrain_t;
#endif/*(PS5017_EN || PS5021_EN)*/
//Gen PFA
typedef struct {
	U32 ulVCA;
	U32 ulFSA;
	U16 uwTagId0;
	U8 ubMTIdx;
	union {
		U8 ubAll;
		struct {
			U8 btSLC :				1;
			U8 btD1 :				1;
			U8 RandomSeedMode :	2;
			U8 Reserved :			4;
		} bits;

	} param;
	U16 uwOPT_STS;
	U8 ubPlaneP2LMapping; // 2 bit for 1 physical plane, 4 plane = 8 bit
} FailMTInfo_t;

typedef struct {
	U8 ubStatus;
	/*
	each 2 bit represent 1 Physical Plane -> Logical Plane (Max: 4 plane)
	 Example:
	bit0~1: 2 // Physical Plane A -> Logical Plane C
	bit2~3: 2 // Physical Plane B -> Logical Plane C
	bit4~5: 3 // Physical Plane C -> Logical Plane D
	bit6~7: 0 // Physical Plane D -> Logical Plane A
	*/
	U8 ubPlaneP2LMapping;
	U8 ubOPTStatus;
} CheckFailStatus_t;

//Debug	GEN_FIP_PROG_FAIL_EN
typedef struct {
	U32 ulVCA;
	U32 ulFSA;
	U16 uwTagId0;
	U8 ubMTIdx;
	U8 btSLC;
	U16 uwOPT_STS;
	U8 btD1;
	U8 ubCheckFlag;
	U8 ubGenFailFlag;		//For only gen Fail once a time
} GenFailMT_t;

typedef struct {
	U32 btBufMode		: 1;
	U32 btSerialMode	: 1;
	U32 Reserved		: 6;
	U32 BufAddr		: 24;
} RestoreP4KBackupAddrBufInfo_t;

typedef struct {
	RestoreP4KBackupAddrBufInfo_t ulBufInfo[FIP_P4K_BACKUP_NUM];
	U16 uwIRAMOffset[FIP_P4K_BACKUP_NUM];
	U8 ubTableIdx;
} RestoreP4KBackupAddrTable_t;

typedef struct {
	union {
		U8 ubFPUACTiming[14];
		struct {
			U8 ubADL_TGL;
			U8 ubAR_TGL;
			U8 ubCH_TGL;
			U8 ubCLR_TGL;
			U8 ubCWAW_TGL;
			U8 ubRR_TGL;
			U8 ubWHR_TCL;
			U8 ubWHR2_TGL;
			U8 ubWW_TGL;
			U8 ubResvered[5];
		} type;
	} parameter;
	U8 ubVersion;
	U8 ubBranch;
} FPUACTiming_t;
extern FPUACTiming_t gFpuACTiming;

#if (HOST_MODE == USB) // JIRA@**********-1048, AC Timing verification for USB PPS
typedef struct {
	//header: 256 bytes
	struct {
		U8 ubFWVersion[16];
		struct {
			U8 ubVersion[12];
			U8 ubReserved[4];
		} ACTimingVersion;
		U8 ubReserved[224];
	} ACTimingHeader;
	//Parameter: 256 bytes (32 + 224)
	struct {
		struct {
			U16 uwACTimingNum;
			U16 uwTargetFlashClock;
			struct {
				U8 ubFlaID[8];
			} FlashID;
			U8 ubReserved[20];
		} Header;
		union {
			U16 ubACTimingCheck[FIP_ACTIMING_NUM];
			struct {
				U16 uwtADL;
				U16 uwtAR;
				U16 uwtCALH_CLE;
				U16 uwtCALH_ALE;
				U16 uwtCALS_CLE;
				U16 uwtCALS_ALE;
				U16 uwtCALS2_CLE_TX;
				U16 uwtCALS2_ALE_TX;
				U16 uwtCALS2_RX;
				U16 uwtCAH_CMD;
				U16 uwtCAH_ADR;
				U16 uwtCAS_CMD;
				U16 uwtCAS_ADR;
				U16 uwtCDQSH;
				U16 uwtCH;
				U16 uwtCHZ;
				U16 uwtCLHZ;
				U16 uwtCLR;
				U16 uwtCOH;
				U16 uwtCR;
				U16 uwtCRES;
				U16 uwtCS;
				U16 uwtCS2;
				U16 uwtCS2_TX;
				U16 uwtCS2_RX;
				U16 uwtCWAW;
				U16 uwtDH;
				U16 uwtDQSH;
				U16 uwtDQSL;
				U16 uwtDQSQ;
				U16 uwtDQSRE;
				U16 uwtDSC;
				U16 uwtDS;
				U16 uwtDVW;
				U16 uwtFEAT;
				U16 uwtQH;
				U16 uwtQHS;
				U16 uwtRC;
				U16 uwtREH;
				U16 uwtRP;
				U16 uwtRPP;
				U16 uwtRPRE;
				U16 uwtRPRE2;
				U16 uwtRPST;
				U16 uwtRPSTH;
				U16 uwtDQSRH;
				U16 uwtDQSD;
				U16 uwtRR;
				U16 uwtRST;
				U16 uwtWB;
				U16 uwtWC_CMD;
				U16 uwtWC_ADR;
				U16 uwtWH;
				U16 uwtWHR;
				U16 uwtWHR2;
				U16 uwtWP_CMD;
				U16 uwtWP_ADR;
				U16 uwtWPRE;
				U16 uwtWPRE2;
				U16 uwtWPST;
				U16 uwtWPSTH;
				U16 uwtWW;
				U16 uwtQSH;
				U16 uwtQSL;
				U8 ubReserved[160];
			} symbol;
		} Parameter;
	} ACTimingParameter;
} ACTimingCheck_t;
extern ACTimingCheck_t gACTimingCheck;
#endif /* (HOST_MODE == USB) */

typedef struct {
	U16 uwRemapOffsetInit;
	U16 uwRemapOffsetDiffInit;
	U16 uwCycleInit;
	U16 uwCycleDiffInit;
	U16 uwD1;
	U16 uwD2;
} RandomizerParam_t;
TYPE_SIZE_CHECK(RandomizerParam_t, 12);
extern RandomizerParam_t gRandomizerParam;

extern volatile FLH_ENV_STRUCT_t *gpeFUSEParam;
extern MTMgr_t gMTMgr;
extern MTBackup_t gMTBackupMgr;
extern CheckFailStatus_t gCheckFailStatus;
#if BURNER_MODE_EN || RDT_RECORD_SCAN_WINDOW_LOG
extern U8 gubFipCEmapping[MAX_PHYSICAL_CE_NUM];
extern ScanWindowParam_t gFipCurWindowParam;
extern U16 guwFipMdllValue[MAX_CHANNEL] ;
extern FAE_LOG_t gFipDllWindowValue[FIP_SCAN_WINDOW_CNT][MAX_CHANNEL];
extern FAE_LOG_t gFipDllWindowValue_temp[MAX_CHANNEL];
#if (PS5017_EN || PS5021_EN)
extern DQTrainResult_t gFipDQTrain[MAX_PHYSICAL_CE_NUM][MAX_LUN_NUM];
#else /*(PS5017_EN || PS5021_EN)*/
extern DQTrainResult_t gFipDQTrain[MAX_PHYSICAL_CE_NUM][MAX_LUN_NUM][FIP_SCAN_WINDOW_CNT] ;
#endif /* (PS5017_EN || PS5021_EN) */
extern ScanWindowPerBitTrain_t gFipPerBitTrain;
#endif	/*BURNER_MODE_EN*/
#if (FIP_DPS_FEATURE_EN)
extern U8 gubDPSAddr[FIP_DPS_TOTAL_SET_ADDR_NUM];
extern U8 gubDPSData[FIP_DPS_TOTAL_SET_ADDR_NUM];
extern U8 gubDPSPlusData[MAX_CHANNEL][MAX_CE_PER_CHANNEL][MAX_DIE_NUM][FIP_DPS_SLC_NEED_PLUS_ADDR_NUM];
#endif /*FIP_DPS_FEATURE_EN*/
extern U8 gubLogicalCEMapPhysicalCE[MAX_CHANNEL][MAX_CE_PER_CHANNEL];

extern FailMTInfo_t gFailMTInfo;
//Debug	GEN_FIP_PROG_FAIL_EN
extern GenFailMT_t gGenFailMT;
extern RestoreP4KBackupAddrTable_t gRestoreP4KBackupAddrTable;
AOM_INIT void FIPReset(void);
AOM_INIT U8 FlaInitIP(void);
AOM_INIT void FlaMTQInit(void);
#if MICRON_FSP_EN
AOM_INIT void FIPModifyFPUFor4RowAddr(void);
AOM_INIT void FlaFixMicronNandWorkaround(void);

#if BURNER_MODE_EN
void FlaDetectMicronNandBug(void);
#endif
#endif
AOM_INIT void FlaSetFeature(U8 ubCH, U8 ubCE, U8 ubAddr, U8 *pubFeature);
AOM_INIT U8 FlaGetFeature(U8 ubCH, U8 ubCE, U8 ubAddr, U8 *pubFeature, U8 ubDataMask, U8 ubCompareData, U8 ubCompareEn, U8 ubRetryTime, U16 uwAssertCode);
AOM_MEDIA_SCAN_VALLEY_HEALTH_CHECK void FIPSetFeatureForMediaScan(U8 ubCH, U8 ubCE, U8 ubAddr, U8 *pubFeature);	//VHC need to be in same code bank
AOM_MEDIA_SCAN_VALLEY_HEALTH_CHECK void FIPGetFeatureForMediaScan(U8 ubCH, U8 ubCE, U8 ubAddr, U8 *pubFeature, U8 ubDataMask, U8 ubCompareData, U8 ubCompareEn, U8 ubRetryTime, U16 uwAssertCode);	//VHC need to be in same code bank
AOM_INIT U8 FlaSetGetFeatureCompare(U8 ubCH, U8 ubCE, U8 ubAddr, U8 *pubFeature);
AOM_INIT void FlaSetMLBi(U8 ubChannel, U8 ubCE, U16 uwAddr, U8 pubFeature);
AOM_INIT void FlaGetMLBi(U8 ubChannel, U8 ubCE, U16 uwAddr, U8 *pubFeature);
AOM_MEDIA_SCAN_VALLEY_HEALTH_CHECK void FIPSetMLBiForMediaScan(U8 ubChannel, U8 ubCE, U16 uwAddr, U8 pubFeature);	//VHC need to be in same code bank
AOM_MEDIA_SCAN_VALLEY_HEALTH_CHECK void FIPGetMLBiForMediaScan(U8 ubChannel, U8 ubCE, U16 uwAddr, U8 *pubFeature);	//VHC need to be in same code bank
#if MICRON_NICKS_PREPROGRAM_EN
void FlaPreprogramMLBiSet(U8 *ubTrimFeatureData);
void FlaPreprogramMLBiRecovery(U8 *ubTrimFeatureData);
#endif /* MICRON_NICKS_PREPROGRAM_EN */
AOM_INIT void FlaSetFeatureByLUN(U8 ubChannel, U8 ubCE, U8 ubLUN, U8 ubAddr, U8 *pubFeature);
AOM_INIT void FlaGetFeatureByLUN(U8 ubChannel, U8 ubCE, U8 ubLUN, U8 ubAddr, U8 *pubFeature);
#if (!MICRON_FSP_EN)
AOM_INIT void FIPAIPRSetFeature(U8 ubChannel, U8 ubCE, U8 ubLUN, U8 ubAddr, U8 *pubFeature);
AOM_INIT void FIPAIPRGetFeature(U8 ubChannel, U8 ubCE, U8 ubLUN, U8 ubAddr, U8 *pubFeature);
#endif
AOM_INIT void FlaSetClock(U8 ubChannel, U8 ubDiv);
AOM_INIT void FlaIOTypeSetting(U8 ubChannel, U8 ubIOType);
#if PS5021_EN
U8 FlaCheckFPUBusy(U8 ubChannel, U32 ulTimeLimit_ms);
#else /* PS5021_EN */
AOM_BURNER U8 FlaCheckFPUBusy(U8 ubChannel, U32 ulTimeLimit_ms);
#endif /* PS5021_EN */
AOM_BURNER U32 FlaGetPCA(U8 ubPlane, U8 ubChannel, U8 ubCE, U8 ubLUN, U16 uwPage, U32 ulBlock, U8 ubRuleIdx);
AOM_BURNER U8 FlaGetDieBitOffset();
#if (D1_UNIT_EN)
AOM_BURNER_2 U32 FIPINTSettingRead(void);
AOM_BURNER_2 void FIPINTSettingEnable(U32 ulBitmap);
AOM_BURNER_2 void FIPINTSettingDisable(U32 ulBitmap);
AOM_BURNER_2 U32 FIPCorrectSettingRead(void);
AOM_BURNER_2 void FIPCorrectSettingEnable(U32 ulBitmap);
AOM_BURNER_2 void FIPCorrectSettingDisable(U32 ulBitmap);
AOM_BURNER_2 U32 FIPApiChannelSettingRead(void);
AOM_BURNER_2 void FIPChannelSettingEnable(U32 ulBitmap);
AOM_BURNER_2 void FIPChannelSettingDisable(U32 ulBitmap);
#else /*(D1_UNIT_EN)*/
AOM_BURNER U32 FIPINTSettingRead(void);
AOM_BURNER void FIPINTSettingEnable(U32 ulBitmap);
AOM_BURNER void FIPINTSettingDisable(U32 ulBitmap);
AOM_BURNER U32 FIPCorrectSettingRead(void);
AOM_BURNER void FIPCorrectSettingEnable(U32 ulBitmap);
AOM_BURNER void FIPCorrectSettingDisable(U32 ulBitmap);
AOM_BURNER U32 FIPApiChannelSettingRead(void);
AOM_BURNER void FIPChannelSettingEnable(U32 ulBitmap);
AOM_BURNER void FIPChannelSettingDisable(U32 ulBitmap);
#endif /*(D1_UNIT_EN)*/
AOM_BURNER U16 FlaEraseLevelWorkaroundPIO(U16 uwStartBlk);
AOM_VUC void FIPScanFlashUniqueID(U8 ubChannel, U8 ubCE, U8 ubDie, U8 *pubFlashUniqueIDAddr);
AOM_VUC_3 void FIPScanTemperature(U8 ubChannel, U8 ubCE, U8 ubDie, U8 *pubFlashTemperatureAddr);

void FlaCEControl(U8 ubCH, U8 ubCE, U8 ubEnable);
void FlaClearInterruptBusy(U8 ubChannel);
U32 FlaGetIntFIFOMapInfo(U8 ubChannel, U8 ubMapSel);
U32 FlaGetInterruptInfo(U8 ubChannel);
U8 FlaCheckNextFIFOMapExist(U8 ubChannel);
void FlaSwitchFIFOMap(U8 ubChannel);
void FlaGlobalTrigger(MTCfg_t *pMTCfg);

void FIPCheckTrueReady(U8 ubChannel, U8 ubCE);
void FIPSendResetCmd(U8 ubChannel, U8 ubCE);
#if (N28_VALLEY_CHECK_EN)
AOM_RETRY_2 void FIPSetFeatureValleyCheckInit(U8 ubChannel, U8 ubCE, U8 ubAddr, U8 *pubFeature);
AOM_RETRY_2 U8 FIPGetFeatureValleyCheckInit(U8 ubChannel, U8 ubCE, U8 ubAddr, U8 *pubFeature, U8 ubDataMask, U8 ubCompareData, U8 ubCompareEn, U8 ubRetryTime, U16 uwAssertCode);
#endif /* (N28_VALLEY_CHECK_EN) */

//Physical CEBMP
#if(PS5021_EN)
#define M_FIP_CHECK_PHYSICAL_CEBMP(PHYSICALCEBMP, CH, CE) (((PHYSICALCEBMP & BIT(CH * MAX_PHYSICAL_CE_PER_CH + CE)) == (BIT(CH * MAX_PHYSICAL_CE_PER_CH + CE))) ? TRUE : FALSE)
#elif (PS5017_EN)
#define M_FIP_CHECK_PHYSICAL_CEBMP(PHYSICALCEBMP, CH, CE) (((PHYSICALCEBMP & ((BGA169 == gubASICType) ? BIT(CH * MAX_PHYSICAL_CE_PER_CH + CE) : BIT(CE * MAX_CHANNEL + CH))) == ((BGA169 == gubASICType) ? BIT(CH * MAX_PHYSICAL_CE_PER_CH + CE) : BIT(CE * MAX_CHANNEL + CH))) ? TRUE : FALSE)
#else
#define M_FIP_CHECK_PHYSICAL_CEBMP(PHYSICALCEBMP, CH, CE) (((PHYSICALCEBMP & BIT(CE * MAX_CHANNEL + CH)) == (BIT(CE * MAX_CHANNEL + CH))) ? TRUE : FALSE)
#endif

// Scan setting
#define M_INIT_SCAN_SET_FIP_NON_STOP() do { \
		R32_FALL[R32_FCTL_IBF_CTL] &= (~SET_FORCE_DATA_COR_EN);\
		R32_FCON[R32_FCON_ERASE_PAG] |= SET_EP_COR_DIS;\
	} while (0)
#define M_INIT_SCAN_RESTORE_FIP_STOP() (R32_FCON[R32_FCON_ERASE_PAG] &= (~SET_EP_COR_DIS))

#if PS5017_EN
#define M_FIP_SET_MTQ_DELAY(DELAY)	(R32_FCON[R32_FCON_MTQ_DLY_0] = (DELAY & MTQ_DELAY_MASK))
#define M_FIP_GET_MTQ_DELAY()	(R32_FCON[R32_FCON_MTQ_DLY_0] & MTQ_DELAY_MASK)
#if(MST_MODE_EN/*(CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC)\
		||(CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_TLC)\
		||(CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_QLC)\
		||(CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_TLC)\
		||(CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_QLC)\
		||(CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC)\
		||(CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC)\
		||(CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)*/)//Duson Porting BICS5 Add //Reip Porting 3D-V7 QLC Add//ems mst add--karl//zerio bics6 qlc add
#define M_FIP_SET_MTQ_DELAY_1(DELAY)	(R32_FCON[R32_FCON_MTQ_DLY_1] = (DELAY & MTQ_DELAY_MASK)) //TT use
#endif  //(CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC)

#else /* PS5017_EN */
#define M_FIP_SET_MTQ_DELAY(DELAY)	(R32_FALL[R32_FCTL_MTQ_DLY] = ((DELAY & MTQ_DELAY_MASK) << MTQ_DELAY_SHIFT))
#define M_FIP_GET_MTQ_DELAY()	((R32_FALL[R32_FCTL_MTQ_DLY]>>MTQ_DELAY_SHIFT) & MTQ_DELAY_MASK)
#endif /* PS5017_EN */

// VUC

#define M_FIP_VUC_DIRECTED_WRITE_BACK_UP(ulCOP0_TMP, ulFCTL_ECC_CFG_TMP, ulFCON_CRC_EN_TMP) do{ \
		(ulCOP0_TMP) = R32_COP0[R32_COP0_ATTR10];\
		(ulFCTL_ECC_CFG_TMP) = R32_FALL[R32_FCTL_ECC_CFG] & LDPC_COR_EN_BIT;\
		(ulFCON_CRC_EN_TMP) = R32_FCON[R32_FCON_CRC_EN] & CRC16_EN_BIT;\
	}while(0)
#define M_FIP_VUC_DIRECTED_WRITE_RESTORE(ulCOP0_TMP, ulFCTL_ECC_CFG_TMP, ulFCON_CRC_EN_TMP) do{ \
			R32_COP0[R32_COP0_ATTR10] = (ulCOP0_TMP);\
			R32_FALL[R32_FCTL_ECC_CFG] &= CLR_LDPC_COR_DIS;\
			R32_FALL[R32_FCTL_ECC_CFG] |= (ulFCTL_ECC_CFG_TMP);\
			R32_FCON[R32_FCON_CRC_EN] &= ~CRC16_EN_BIT;\
			R32_FCON[R32_FCON_CRC_EN] |= (ulFCON_CRC_EN_TMP);\
	}while(0)

#define M_FIP_VUC_DIRECTED_READ_BACK_UP(ulFCTL_NIT_CFG_TMP, ulCOP0_TMP, ubLdpcTmp, ulFCTL_FSA_SEL_TMP, ulFCTL_BACK_RESTORE_TMP) do{ \
			(ubLdpcTmp) = (U8)M_GET_ECC_MODE();\
			(ulFCTL_NIT_CFG_TMP) = R32_FALL[R32_FCTL_INT_CFG];\
			(ulCOP0_TMP) = R32_COP0[R32_COP0_ATTR2];\
			(ulFCTL_FSA_SEL_TMP) = R32_FALL[R32_FCTL_IFSA0];\
			(ulFCTL_BACK_RESTORE_TMP)= R32_FALL[R32_FCTL_BACK_RESTORE];\
	}while(0)
#define M_FIP_VUC_DIRECTED_READ_RESTORE(ulFCTL_NIT_CFG_TMP, ulCOP0_TMP, ulFCTL_FSA_SEL_TMP, ulFCTL_BACK_RESTORE_TMP) do{ \
			R32_FALL[R32_FCTL_INT_CFG] = (ulFCTL_NIT_CFG_TMP);\
			R32_COP0[R32_COP0_ATTR2] = (ulCOP0_TMP);\
			R32_FALL[R32_FCTL_IBF_CTL] &= CLR_FORCE_DATA_COR_EN;\
			R32_FALL[R32_FCTL_IFSA0] = (ulFCTL_FSA_SEL_TMP);\
			R32_FALL[R32_FCTL_BACK_RESTORE] = (ulFCTL_BACK_RESTORE_TMP);\
	}while(0)
#define M_FIP_VUC_MICRON_READ_RAW_ERROR_BIT_BACK_UP(ubLdpcTmp, ulCOP0_TMP, ulFCON_CRC_EN_TMP) do{ \
				(ubLdpcTmp) = (U8)M_GET_ECC_MODE();\
				(ulCOP0_TMP) = R32_COP0[R32_COP0_ATTR2];\
				(ulFCON_CRC_EN_TMP) = R32_FCON[R32_FCON_CRC_EN];\
		}while(0)

#define M_FIP_VUC_MICRON_READ_RAW_ERROR_BIT_RESTORE(ulCOP0_TMP, ulFCON_CRC_EN_TMP) do{ \
				R32_FCON[R32_FCON_CRC_EN] = (ulFCON_CRC_EN_TMP);\
				R32_COP0[R32_COP0_ATTR2] = (ulCOP0_TMP);\
		}while(0)

#define M_FIP_VUC_DIRECTED_READ_SOURCE_DATA() do{ \
			R32_FALL[R32_FCTL_ECC_CFG] |= SET_LDPC_COR_EN;\
			R32_FCON[R32_FCON_CRC_EN] |= CRC16_EN_BIT;\
	}while(0)

#define M_FIP_VUC_MICRON_SOURCE_DATA(ubChannel) do{ \
			R32_FCTL_CH[(ubChannel)][R32_FCTL_ECC_CFG] |= SET_LDPC_COR_EN;\
			R32_FCON[R32_FCON_CRC_EN] |= CRC16_EN_BIT;\
	}while(0)

#define M_FIP_VUC_MICRON_GENERIC_NAND_PASSTHROUGH_BACKUP(ubChannel, ulFCTL_ECC_CFG_TMP, ulFCON_CRC_EN_TMP, ulFCTL_CMP_CFG_TMP, ulFCTL_IBF_CTL_TMP) do{ \
			(ulFCTL_ECC_CFG_TMP) = (R32_FCTL_CH[(ubChannel)][R32_FCTL_ECC_CFG] & LDPC_COR_EN_BIT);\
			(ulFCON_CRC_EN_TMP) = (R32_FCON[R32_FCON_CRC_EN] & CRC16_EN_BIT);\
			(ulFCTL_CMP_CFG_TMP) = (R32_FCTL_CH[(ubChannel)][R32_FCTL_CMP_CFG] & CMP_EN_BIT);\
			(ulFCTL_IBF_CTL_TMP) = (R32_FCTL_CH[(ubChannel)][R32_FCTL_IBF_CTL] & SET_FORCE_DATA_COR_EN);\
	}while(0)

#define M_FIP_VUC_MICRON_GENERIC_NAND_PASSTHROUGH_RESTORE(ubChannel, ulFCTL_ECC_CFG_TMP, ulFCON_CRC_EN_TMP, ulFCTL_CMP_CFG_TMP, ulFCTL_IBF_CTL_TMP) do{ \
			R32_FCTL_CH[(ubChannel)][R32_FCTL_ECC_CFG] = (ulFCTL_ECC_CFG_TMP);\
			R32_FCON[R32_FCON_CRC_EN] = (ulFCON_CRC_EN_TMP);\
			R32_FCTL_CH[(ubChannel)][R32_FCTL_CMP_CFG] = (ulFCTL_CMP_CFG_TMP);\
			R32_FCTL_CH[(ubChannel)][R32_FCTL_IBF_CTL] = (ulFCTL_IBF_CTL_TMP);\
	}while(0)

#define M_FIP_VUC_DIRECTED_READ_RAW_DATA() do{ \
			R32_FCON[R32_FCON_CRC_EN] &= ~CRC16_EN_BIT;\
			R32_FALL[R32_FCTL_ECC_CFG] &= CLR_LDPC_COR_DIS;\
	}while(0)

#define M_FIP_VUC_DIRECTED_READ_CLOSE_CRC() (R32_FCON[R32_FCON_CRC_EN] &= ~CRC16_EN_BIT)

#if (!PS5021_EN)
#define M_FIP_VUC_ASSIGN_IFSA(ubCH,ulPCA,ubFrameNum) do{ \
			R32_FCTL_CH[(ubCH)][R32_FCTL_FSA_SEL] |= IFSA_EN_BIT;\
			R32_FCTL_CH[(ubCH)][R32_FCTL_IFSA0] = (ulPCA) + (ubFrameNum);\
	}while(0)
#endif /*(!PS5021_EN)*/

#define M_FIP_VUC_SET_NORMAL_AND_ERROR_TARGET_TO_CPU0(ubCH) do{ \
			R32_FCTL_CH[ubCH][R32_FCTL_INT_VCT] &= (CLR_NORMAL_CPU & CLR_ERROR_CPU);\
			R32_FCTL_CH[ubCH][R32_FCTL_INT_VCT] |= (SET_NORMAL_CPU0 | SET_ERROR_CPU0);\
	}while(0)

#define M_FIP_DIV_BACK_UP(ubCH, ulFlashClockTmp) ((ulFlashClockTmp) = R32_FCTL_CH[(ubCH)][R32_FCTL_FDIV_CFG])
#define M_FIP_DIV_RESTORE(ubCH, ulFlashClockTmp) (R32_FCTL_CH[(ubCH)][R32_FCTL_FDIV_CFG] = (ulFlashClockTmp) )

#if PS5013_EN
#define M_FIP_CLR_FLH_DIV_ALL_CH() do{ \
                                       R32_FALL[R32_FCTL_FDIV_CFG] &= ~FLH_CLK_DIV_EN_BIT; \
                                    } while(0)

#define M_FIP_CLR_FLH_DIV(CH) do{ \
                                  R32_FCTL_CH[CH][R32_FCTL_FDIV_CFG] &= ~FLH_CLK_DIV_EN_BIT; \
                              } while(0)

#else /*PS5013_EN*/
#define M_FIP_CLR_FLH_DIV_ALL_CH() do{ \
                                        R32_FALL[R32_FCTL_FDIV_CFG] |= (FLH_CLK_DIV_DIS_TRIG_BIT); \
                                    } while(0)

#define M_FIP_CLR_FLH_DIV(CH) do{ \
                                  R32_FCTL_CH[CH][R32_FCTL_FDIV_CFG] |= (FLH_CLK_DIV_DIS_TRIG_BIT); \
                              } while(0)
#endif /*PS5013_EN*/

#define M_FIP_SET_FLH_DIV_EN(CH) do { \
                                    R32_FCTL_CH[CH][R32_FCTL_FDIV_CFG] |= FLH_CLK_DIV_EN_BIT; \
                                 } while(0)

#define M_FIP_SET_FLH_DIV_EN_ALL_CH(CH) do { \
                                            R32_FALL[R32_FCTL_FDIV_CFG] |= FLH_CLK_DIV_EN_BIT; \
                                        } while(0)

#define M_FIP_SET_FLH_DIV_All_CH(DIV_VALUE) do{ \
                                                M_FIP_CLR_FLH_DIV_ALL_CH(); \
                                                R32_FALL[R32_FCTL_FDIV_CFG] &= ~FLH_CLK_DIV_VAL_SHIFT_MASK; \
                                            	R32_FALL[R32_FCTL_FDIV_CFG] |= ((DIV_VALUE & FLH_CLK_DIV_VAL_MASK) << FLH_CLK_DIV_VAL_SHIFT); \
                                            	R32_FALL[R32_FCTL_FDIV_CFG] |= FLH_CLK_DIV_EN_BIT; \
                                            } while(0)
#define M_FIP_SET_FLH_DIV(CH, DIV_VALUE) do{ \
                                            M_FIP_CLR_FLH_DIV(CH); \
                                            R32_FCTL_CH[CH][R32_FCTL_FDIV_CFG] &= ~FLH_CLK_DIV_VAL_SHIFT_MASK; \
                                        	R32_FCTL_CH[CH][R32_FCTL_FDIV_CFG] |= ((DIV_VALUE & FLH_CLK_DIV_VAL_MASK) << FLH_CLK_DIV_VAL_SHIFT); \
                                        	R32_FCTL_CH[CH][R32_FCTL_FDIV_CFG] |= FLH_CLK_DIV_EN_BIT; \
                                        } while(0)

#if PS5013_EN
#define M_FIP_DIV_WAIT_BUSY(CH)       (NULL)
#define M_FIP_DIV_WAIT_BUSY_ALL_CH()  (NULL)
#elif PS5017_EN
#define M_FIP_DIV_WAIT_BUSY(CH)  do { \
                                        while (R32_FCTL_CH[CH][R32_FCTL_FDIV_CFG] & DIV_BS_BIT); \
                                    } while(0)
#define M_FIP_DIV_WAIT_BUSY_ALL_CH()  do { \
                                        while (R32_FCTL_CH[CH0][R32_FCTL_FDIV_CFG] & DIV_BS_BIT); \
                                        while (R32_FCTL_CH[CH1][R32_FCTL_FDIV_CFG] & DIV_BS_BIT); \
                                    } while(0)
#else /*PS5013_EN*/
#define M_FIP_DIV_WAIT_BUSY(CH)  do { \
                                        while (R32_FCTL_CH[CH][R32_FCTL_FDIV_CFG] & DIV_BS_BIT); \
                                    } while(0)
#define M_FIP_DIV_WAIT_BUSY_ALL_CH()  do { \
                                        while (R32_FCTL_CH[CH0][R32_FCTL_FDIV_CFG] & DIV_BS_BIT); \
                                        while (R32_FCTL_CH[CH1][R32_FCTL_FDIV_CFG] & DIV_BS_BIT); \
                                        while (R32_FCTL_CH[CH2][R32_FCTL_FDIV_CFG] & DIV_BS_BIT); \
                                        while (R32_FCTL_CH[CH3][R32_FCTL_FDIV_CFG] & DIV_BS_BIT); \
                                    } while(0)
#endif /*PS5013_EN*/

//calculate logic CE ,now temp in 16CE
#define M_FIP_CALCULATE_LOGICAL_CE(CH,CE,LogicalCE)		do{\
	(LogicalCE) = (CH) * MAX_CE_PER_CHANNEL + (CE);\
}while(0)

#define M_FIP_CALCULATE_PHYSICAL_CE(CH,CE,PhysicalCE)do{\
	(PhysicalCE) = (gubLogicalCEMapPhysicalCE[CH][CE]);\
}while(0)
#if (!PS5017_EN)
#define M_FIP_SET_DQS_OUTPUT_ENABLE()  R32_FCON[R32_FCON_MODE_SET] |= DQS_OE_DRV_EN_BIT;
#endif /* (!PS5017_EN) */
#define M_FIP_ECC_CLOCK_GATING_DIS()	R32_FCON[R32_FCON_MODE_SET] |= (CTRL_ECC_CLK_BIT)
#define M_FIP_ECC_CLOCK_GATING_EN()		R32_FCON[R32_FCON_MODE_SET] &= ~(CTRL_ECC_CLK_BIT)
#define M_FIP_COR_CLOCK_GATING_DIS()	R32_FCON[R32_FCON_MODE_SET] |= (CTRL_COR_CLK_BIT)
#define M_FIP_COR_CLOCK_GATING_EN()		R32_FCON[R32_FCON_MODE_SET] &= ~(CTRL_COR_CLK_BIT)

#if E21_TODO
#define M_FIP_VUC_CLR_IFSA() (NULL)
#else /* E21_TODO */
#define M_FIP_VUC_CLR_IFSA() (R32_FALL[R32_FCTL_FSA_SEL] &= ~IFSA_EN_BIT)
#endif /* E21_TODO */

#define M_FIP_VUC_CLEAR_BUSY() (R32_FALL[R32_FCTL_INT_INF] &= ~(CHK_INT_BUSY | MAP_FIFO_PNT_RST_BIT))

#define M_VUC_SET_IO_SET(Channel)				R32_FCTL_CH[(Channel)][R32_FCTL_IO_SET] |= IO_PAD_TYPE_SEL_BIT;
#define M_VUC_CLR_IO_SET(Channel)				R32_FCTL_CH[(Channel)][R32_FCTL_IO_SET] &= ~IO_PAD_TYPE_SEL_BIT;
#define M_FIP_CHECK_IO_TYPE(Channel)			(R32_FCTL_CH[(Channel)][R32_FCTL_IO_SET] & IO_PAD_TYPE_SEL_BIT)

#define M_FIP_FLH_SET_BACK_UP(ubCH, ulFipConfTmp)	((ulFipConfTmp) = R32_FCTL_CH[(ubCH)][R32_FCTL_FLH_SET])
#define M_FIP_FLH_SET_RESTORE(ubCH, ulFipConfTmp)	(R32_FCTL_CH[(ubCH)][R32_FCTL_FLH_SET] = (ulFipConfTmp))
#define M_FIP_CLR_IF_LEGACY_MODE(Channel)		R32_FCTL_CH[(Channel)][R32_FCTL_FLH_SET] &= ~CLR_LEGACY_MODE;
#define M_FIP_SET_LEGACY_MODE(ubCH)				R32_FCTL_CH[(ubCH)][R32_FCTL_FLH_SET] &= CLR_LEGACY_MODE;
#define M_FIP_SET_TOGGLE_MODE(ubCH)				R32_FCTL_CH[(ubCH)][R32_FCTL_FLH_SET] |= SET_TOGGLE_MODE
#define M_FIP_CHECK_FLH_IF(Channel)				(R32_FCTL_CH[(Channel)][R32_FCTL_FLH_SET] & FLH_IF_TYPE_SHIFT_MASK)
#define M_FIP_CLEAR_FTA_COL()					(R32_FALL[R32_FCTL_FTA_COL] = 0x00)


#define M_FIP_CLR_PIO_FISRT_LAST_BIT(ubCH)	(R32_FCTL_CH[(ubCH)][R32_FCTL_HS_MODE] &= ~(PIO_DAT_FIRST_BIT | PIO_DAT_LAST_BIT))
#define M_FIP_SET_PIO_FIRST_LAST_BIT(ubCH)	(R32_FCTL_CH[(ubCH)][R32_FCTL_HS_MODE] |= (PIO_DAT_FIRST_BIT | PIO_DAT_LAST_BIT))
#define M_FIP_SET_PIO_FIRST_BIT(ubCH)	(R32_FCTL_CH[(ubCH)][R32_FCTL_HS_MODE] |= PIO_DAT_FIRST_BIT)
#define M_FIP_SET_PIO_LAST_BIT(ubCH)	(R32_FCTL_CH[(ubCH)][R32_FCTL_HS_MODE] |= PIO_DAT_LAST_BIT)
#define M_FIP_GET_PIO_DATA(ubCH) (R32_FCTL_CH[(ubCH)][R32_FCTL_PIO_DAT] & 0xFFFF)
#define M_FIP_SET_PIO_DATA(ubCH, data) (R32_FCTL_CH[(ubCH)][R32_FCTL_PIO_DAT] = (data))
#define M_FIP_LCA_CMP_EN_BY_CHANNEL(ubChannel)	(R32_FCTL_CH[(ubChannel)][R32_FCTL_CMP_CFG] |= CMP_EN_BIT)
#define M_FIP_LCA_CMP_DIS_BY_CHANNEL(ubChannel)	(R32_FCTL_CH[(ubChannel)][R32_FCTL_CMP_CFG] &= (~CMP_EN_BIT))
#define M_FIP_LCA_CMP_EN()  (R32_FALL[R32_FCTL_CMP_CFG] |= CMP_EN_BIT)
#define M_FIP_LCA_CMP_DIS()  (R32_FALL[R32_FCTL_CMP_CFG] &= (~CMP_EN_BIT))
#define M_FIP_INVERSE_EN() (R32_FALL[R32_FCTL_CHNL_SET] &= CLR_INV_BYPASS_DIS)
#define M_FIP_INVERSE_DIS() (R32_FALL[R32_FCTL_CHNL_SET] |= SET_INV_BYPASS_EN)
#define M_FIP_ASSIGN_1TH_COL_ADDR(ubChannel, ubVAL) (R32_FCTL_CH[(ubChannel)][R32_FCTL_FTA_COL] = ((R32_FCTL_CH[(ubChannel)][R32_FCTL_FTA_COL]& (~(ADR_PHA_1TH_ADR_BYTE_VAL_MASK << ADR_PHA_1TH_ADR_BYTE_VAL_SHIFT)))| (ubVAL)) )
#define M_FIP_ASSIGN_2TH_COL_ADDR(ubChannel, ubVAL) (R32_FCTL_CH[(ubChannel)][R32_FCTL_FTA_COL] = ((R32_FCTL_CH[(ubChannel)][R32_FCTL_FTA_COL]& (~(ADR_PHA_2TH_ADR_BYTE_VAL_MASK << ADR_PHA_2TH_ADR_BYTE_VAL_SHIFT)))| (ubVAL)) )
#define M_FIP_ASSIGN_3TH_ROW_ADDR(ubChannel, ubVAL) (R32_FCTL_CH[(ubChannel)][R32_FCTL_FTA_ROW] = ((R32_FCTL_CH[(ubChannel)][R32_FCTL_FTA_ROW]& (~(ADR_PHA_3TH_ADR_BYTE_VAL_MASK << ADR_PHA_3TH_ADR_BYTE_VAL_SHIFT)))| (ubVAL)) )
#define M_FIP_ASSIGN_4TH_ROW_ADDR(ubChannel, ubVAL) (R32_FCTL_CH[(ubChannel)][R32_FCTL_FTA_ROW] = ((R32_FCTL_CH[(ubChannel)][R32_FCTL_FTA_ROW]& (~(ADR_PHA_4TH_ADR_BYTE_VAL_MASK << ADR_PHA_4TH_ADR_BYTE_VAL_SHIFT)))| (ubVAL)) )
#define M_FIP_ASSIGN_5TH_ROW_ADDR(ubChannel, ubVAL) (R32_FCTL_CH[(ubChannel)][R32_FCTL_FTA_ROW] = ((R32_FCTL_CH[(ubChannel)][R32_FCTL_FTA_ROW]& (~(ADR_PHA_5TH_ADR_BYTE_VAL_MASK << ADR_PHA_5TH_ADR_BYTE_VAL_SHIFT)))| (ubVAL)) )
#define M_FIP_ASSIGN_6TH_ROW_ADDR(ubChannel, ubVAL) (R32_FCTL_CH[(ubChannel)][R32_FCTL_FTA_ROW] = ((R32_FCTL_CH[(ubChannel)][R32_FCTL_FTA_ROW]& (~(ADR_PHA_6TH_ADR_BYTE_VAL_MASK << ADR_PHA_6TH_ADR_BYTE_VAL_SHIFT)))| (ubVAL)) )
#define M_FIP_ASSIGN_ALL_ROW_ADDR(ubChannel, ubVAL) (R32_FCTL_CH[(ubChannel)][R32_FCTL_FTA_ROW] = (ubVAL))
#define M_FIP_ASSIGN_PIO_ADDR(ubCH,ubVAL) (R32_FCTL_CH[(ubCH)][R32_FCTL_PIO_ADR] = (ubVAL))
#define M_FIP_ASSIGN_PIO_CMD(ubCH,ubVAL) (R32_FCTL_CH[(ubCH)][R32_FCTL_PIO_CMD] = (ubVAL))



#define M_FIP_SET_SEED(ulSeed) (R32_FALL[R32_FCTL_SEED_INIT] = (ulSeed))
#define M_FIP_SET_FPU(ubCH, FPU) (R32_FCTL_CH[(ubCH)][R32_FCTL_FPU_ENTRY] = FPU_PTR_OFFSET(FPU))
#define M_FIP_SET_FPU_ENTRY(ubChannel, uwOffset)	(R32_FCTL_CH[(ubChannel)][R32_FCTL_FPU_ENTRY] = (uwOffset))
#define M_FIP_TRIG_FPU(ubCH) (R32_FCTL_CH[(ubCH)][R32_FCTL_FPU_TRIG] |= FPU_TRIGGER_BIT)
#define M_FIP_READ_RAW_AUTO_SHIFT_EN(ubCH) (R32_FCTL_CH[(ubCH)][R32_FCTL_FSA_SEL] |= 1 << ADG_SL_PTR_SHIFT)
#define M_FIP_READ_RAW_AUTO_SHIFT_DIS(ubCH)(R32_FCTL_CH[(ubCH)][R32_FCTL_FSA_SEL] &= ~(1 << ADG_SL_PTR_SHIFT))
#if (PS5017_EN || PS5021_EN)
#define M_FIP_DRAM_MULTI_DIS(ubCH) (R32_FCTL_CH[(ubCH)][R32_FCTL_BACK_RESTORE] &=  ~(DRAM_MULTI_EN))  //E17 modify : disable means always use DRAM_ADDR0/iFSA0
#else /*(PS5017_EN || PS5021_EN)*/
#define M_FIP_DRAM_MULTI_DIS(ubCH) (R32_FCTL_CH[(ubCH)][R32_FCTL_BACK_RESTORE] &=  ~(DRAM_MULTI_EN | DRAM_ADDR_SEL_SHIFT_MASK))
#endif /*(PS5017_EN || PS5021_EN)*/
#define M_FIP_CLEAR_DRAM_MULTI(ubCH) (R32_FCTL_CH[ubCH][R32_FCTL_BACK_RESTORE] &= ~DRAM_MULTI_EN)
#define M_FIP_SET_BACK_RESTORE_LENGTH(ubCH, uwLength) do{ \
			R32_FCTL_CH[ubCH][R32_FCTL_BACK_RESTORE] &= (~BR_W_LEN_SHIFT_MASK);\
			R32_FCTL_CH[ubCH][R32_FCTL_BACK_RESTORE] |= M_SET_BR_W_LEN(uwLength);\
	}while(0)

#if (PS5013_EN)
#define M_FIP_SET_RAW_DMA_ADR(ubCH, ulIBFDumpAdr) (R32_FCTL_CH[(ubCH)][R32_FCTL_RAW_DMA_ADR] = (ulIBFDumpAdr))
#define M_FIP_SET_RAW_DMA_IRAM_ADR(ubCH, ulIRAMAddr) do {\
			R32_FCTL_CH[ubCH][R32_FCTL_BACK_RESTORE] &= ~BACK_RESTORE_IADDR_MASK;\
			R32_FCTL_CH[ubCH][R32_FCTL_BACK_RESTORE] |= (ulIRAMAddr & BACK_RESTORE_IADDR_MASK);\
		}while(0)
#else /* (PS5013_EN) */
#define M_FIP_SET_RAW_DMA_ADR(ubCH, ulIBFDumpAdr) (R32_FCTL_CH[ubCH][R32_FCTL_IFSA0] = (ulIBFDumpAdr))
#define M_FIP_SET_RAW_DMA_IRAM_ADR(ubCH, ulIRAMAddr) do {\
			R32_FCTL_CH[ubCH][R32_FCTL_IFSA_HIGH] = 0;\
			R8_FCTL_CH[ubCH][R8_FCTL_IN_FSA0] = ulIRAMAddr & IN_FSA0_MASK;\
			R8_FCTL_CH[ubCH][R8_FCTL_IN_FSA1] = (ulIRAMAddr >> 8) & IN_FSA1_MASK;\
			R8_FCTL_CH[ubCH][R8_FCTL_IN_FSA2] = (PS5021_EN)? ((ulIRAMAddr >> 16) & BIT_MASK(1)) : 0;\
		} while(0)
#endif /* (PS5013_EN) */

#define M_VUC_DIRECTED_READ_SET_ALL_CH_NON_INT() (R32_FALL[R32_FCTL_INT_CFG] &= ~(UNCORR_ECC_INT_EN_BIT | SGN_OFF_MSK_EN_BIT | ZINFO_ERR_INT_EN_BIT | OVR_ERR_INT_EN_BIT | COUNT01_THRESHOLD_EN_BIT | IBF_PAR_INT_EN_BIT | P4K_VALID_ERR_INT_EN_BIT | ERASE_PG_INT_EN_BIT | CMP_LCA_INT_EN_BIT | AUTO_POL_STA_FAIL_INT_EN_BIT | LCA_SEARCH_ERR_INT_EN_BIT | RS_ENC_PTY_ERR_INT_EN_BIT | CRC16_FAIL_INT_EN_BIT))
#define M_VUC_DIRECTED_READ_SET_ALL_CH_NON_CMP()	(R32_FALL[R32_FCTL_CMP_CFG] &= ~CMP_EN_BIT)
#define M_VUC_DIRECTED_READ_SET_ALL_CH_FORCE_DATA()	(R32_FALL[R32_FCTL_IBF_CTL] |= SET_FORCE_DATA_COR_EN)
#define M_FIP_GET_BACKUP_P4K_WORKAROUND_ADDR(uwPBAddr, ubFrame2P4KRandValue, ubFrame3P4KRandValue) (((((U32)uwPBAddr) << FIP_WORKAROUND_BACKUP_P4K_ADDR_OFFSET)	| (((U32)ubFrame2P4KRandValue) << FIP_WORKAROUND_BACKUP_P4K_FRAME_2_RANDOM_VALUE_OFFSET)| ((U32)ubFrame3P4KRandValue) << FIP_WORKAROUND_BACKUP_P4K_FRAME_3_RANDOM_VALUE_OFFSET | ((U32)1) << FIP_WORKAROUND_BACKUP_P4K_BMU_CMD_OFFSET ) << FIP_WORKAROUND_BACKUP_P4K_OFFSET)

#define M_FIP_GET_TADL() ((FIP_FLASH_CLOCK_DEFAULT == gFlhEnv.ubTargetFlashClock) ? (0x15) : (gubTADLValue[gFlhEnv.ubTargetFlashClock]))

#if (PS5017_EN || PS5021_EN)
#define M_FIP_GET_PARITY_ERROR_FLAG(data)	  (data = R32_FCON[R32_FCON_PARITY_ERR_FLAG_0])
#define M_FIP_GET_PARITY_ERROR_ADR(data)	  (data = R32_FCON[R32_FCON_PARITY_ERR_FLAG_1])

#define FDQS_TX_DLY_MASK    (0xFFFFFFF0)
#define FDQS_RX_DLY_MASK    (0xFFFF0FFF)
#define M_FIP_SET_FDQS_TX_DELAY(Channel,x)		    R32_FCTL_CH[Channel][R32_FCTL_PAD_DLY_1] = ((((x) & FLH_DQS_TX_DLY_MASK) << FLH_DQS_TX_DLY_SHIFT) | (R32_FCTL_CH[Channel][R32_FCTL_PAD_DLY_1] & (FDQS_TX_DLY_MASK)))
#define M_FIP_SET_FDQS_RX_DELAY(Channel,x)          R32_FCTL_CH[Channel][R32_FCTL_PAD_DLY_1] = ((((x) & FLH_DQS_RX_DLY_MASK) << FLH_DQS_RX_DLY_SHIFT) | (R32_FCTL_CH[Channel][R32_FCTL_PAD_DLY_1] & (FDQS_RX_DLY_MASK)))

#define M_FIP_GET_FDQS_TX_DELAY(Channel)		    ((R32_FCTL_CH[Channel][R32_FCTL_PAD_DLY_1] >> (FLH_DQS_TX_DLY_SHIFT)) & FLH_DQS_TX_DLY_MASK)
#define M_FIP_GET_FDQS_RX_DELAY(Channel)          	((R32_FCTL_CH[Channel][R32_FCTL_PAD_DLY_1] >> (FLH_DQS_RX_DLY_SHIFT)) & FLH_DQS_RX_DLY_MASK)

#define M_FIP_SET_DQ_TX_DELAY(Channel, x, y)    (R32_FCTL_CH[Channel][R32_FCTL_PAD_DLY_0] = ((((x) & FLH_DQ0_TX_DLY_MASK) << (FLH_DQ0_TX_DLY_SHIFT + 4 * y)) | (R32_FCTL_CH[Channel][R32_FCTL_PAD_DLY_0] & ~(FLH_DQ0_TX_DLY_MASK << (4 * y)))))
#define M_FIP_SET_DQ_RX_DELAY(Channel, x, y)    (R32_FCTL_CH[Channel][R32_FCTL_PAD_DLY_2] = ((((x) & FLH_DQ0_RX_DLY_MASK) << (FLH_DQ0_RX_DLY_SHIFT + 4 * y)) | (R32_FCTL_CH[Channel][R32_FCTL_PAD_DLY_2] & ~(FLH_DQ0_RX_DLY_MASK << (4 * y)))))

#define M_FIP_SET_FDAT_TX_DELAY(Channel, val) (R32_FCTL_CH[Channel][R32_FCTL_PAD_DLY_0] = val)
#define M_FIP_SET_FDAT_RX_DELAY(Channel, val) (R32_FCTL_CH[Channel][R32_FCTL_PAD_DLY_2] = val)
#endif /*(PS5017_EN || PS5021_EN)*/

#if PS5021_EN
#define M_FIP_GET_POL_SEQ_SEL(X) ((X) & BIT_MASK(4))
#define M_FIP_GET_POL_SEQ_SEL_EXT(X) (((X) >> 4) & BIT_MASK(2))
#define M_FIP_SET_POL_SEQ_EXT(X) do {\
        R32_FCON[R32_FCON_MT_CFG_1] &= (~POL_SEQ_SEL_EXT_MASK_ALL);\
        R32_FCON[R32_FCON_MT_CFG_1] |= M_SET_POL_SEQ_SEL_EXT(X);\
    } while(0)
#define M_FIP_SET_POL_SEQUENCE_SELECT(ASSIGN_VALUE, FPU_SEQ) do {\
        ASSIGN_VALUE = M_FIP_GET_POL_SEQ_SEL(FPU_SEQ);\
        gMTMgr.MTCfg1Temp.A.pol_seq_sel_ext  = M_FIP_GET_POL_SEQ_SEL_EXT(FPU_SEQ);\
    } while (0)
#define M_FIP_GET_POL_SEQENCE_SELECT(MT_POL_SEQ) (MT_POL_SEQ + (gMTMgr.MTCfg1Temp.A.pol_seq_sel_ext << 4))
#else /* PS5021_EN */
#define M_FIP_SET_POL_SEQUENCE_SELECT(ASSIGN_VALUE, FPU_SEQ) do {\
        ASSIGN_VALUE = FPU_SEQ;\
    } while (0)
#define M_FIP_GET_POL_SEQENCE_SELECT(MT_POL_SEQ) (MT_POL_SEQ)
#endif /* PS5021_EN */

#define M_FIP_GET_FSA_DIE_NUMBER(iFSA0, ALU_SEL) ((iFSA0 >> gPCARule_LUN.ubShift[ALU_SEL]) & gPCARule_LUN.ulMask)


#define M_FIP_GET_DQ_BIT_REV(ubCH) (R32_FCTL_CH[(ubCH)][R32_FCTL_DQ_BIT_REV])

AOM_INIT U8 FlaGetLDPCMode(U16 uwSpareSize);
AOM_INIT U8 FlaCheckLogValue(U32 ulDataIn);
AOM_INIT void FlaMDLLTracking(U8 ubAutoTrackEn);

#if (PS5017_EN || PS5021_EN)
AOM_INIT U8 FlaReadStatus(U8 ubChannel, U8 ubFlashCE, U8 ubReadCont);
AOM_INIT void FlaDCC_SetFeature(U8 ubChannel, U8 ubFlashCE);
AOM_INIT void FlaDCC(void);
#endif /*(PS5017_EN || PS5021_EN)*/

AOM_INIT void FlaSetFPUDlyCycle(U8 *ubDelayCycleValue, U16 uwFPUSize);
#if(MICRON_FSP_EN || INTEL_FSP_EN)
AOM_INIT void FIPHardResetRestLUN(U8 ubResetCmd);
#endif//MICRON_FSP_EN
#if (MST_MODE_EN)
AOM_INIT void FlaResetAllCE(U8 ubResetCmd);
#else /* (FLASH_TYPE_HYNIX_3D_TLC == CONFIG_FLASH_TYPE) */
AOM_INIT void FlaResetAllCE(void);
#endif
#if (!PS5017_EN)
AOM_BURNER void FipSetDllOffset(U8 ubChannel, U8 ubCE, U16 uwWritePathValue, U16 uwReadPathValue);
#endif /* (!PS5017_EN) */

AOM_RETRY_SB U32 FIPValleyTrackReadOut(U32 ulIfsa, U8 ubMode);
AOM_RETRY_SB U8 FIPMicronGetTemperature(U32 ulIfsa);
U8 FIPCheckChannelForceEmpty(U8 ubChannel);
void FIPSetChannelForceEmpty(U8 ubChannel);
void FIPClearChannelForceEmpty(U8 ubChannel, U8 ubCheck);
void FlaMTQManualAbort(U8 ubChannel, U8 ubNeedDMAAbort);
void FIPResumeMTQManualAbort(U8 ubChannel);
void FlaClearSingleQForceEmpty(U8 ubChannel, U8 ubBank, U8 ubqos);
U8 FIPIsStopAllocateEventSet(U8 ubEvent);
void FIPSetStopAllocateBuf(U8 ubEvent);
void FIPClearStopAllocateBuf(U8 ubEvent);
void FIPPollUntilMTCanExecute(U8 ubChannel);
void FlaClearMTQ(U8 ubChannel, U8 ubBank, U8 ubqos);
#if BURNER_MODE_EN || RDT_RECORD_SCAN_WINDOW_LOG
void FlaScanDllWindow3Write1Read(U32 ulStartOffset, U32 ulEndOffset, U8 ubChannelSelect, U8 ubBankSelect, U8 btBitScan, U8 ubBitScanPosition, FAE_LOG_t *pDllWindowValuel, ScanWindowParam_t *pCurState); //for3W1R path
void FlaScanDllWindow4Write(U32 ulStartOffset, U32 ulEndOffset, U8 ubChannelSelect, U8 ubBankSelect, U8 btBitScan, U8 ubBitScanPosition, FAE_LOG_t *pDllWindowValuel, ScanWindowParam_t *pCurState); //for write path test
void FipScanDllWindowGenWorstPattern(U32 *pulAddr);
#if (!PS5017_EN)
void FlaDQSTXTrain(ScanWindowParam_t *pCurState);
void FlaDQSRXTrain(ScanWindowParam_t *pCurState);
#endif /* (!PS5017_EN) */
void FlaDQTXTrain(ScanWindowParam_t *pCurState);
void FlaDQRXTrain(ScanWindowParam_t *pCurState);
#if (PS5021_EN && (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC))
void FlaSetTimingMode_Micron(U32 ulTargetClk);
#endif /*(PS5021_EN && (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC))*/
void FipClearDQRxDelay(void);
void FipClearDQTxDelay(void);
void FipFindBiggerSDLLValue(void);
void FlaSetDMATimeOutEn(void);
void FlaClearDMATimeOutEn(void);
void FlaBackupDMATimeOutEn(void);
void FlaRecoverDMATimeOutEn(void);
void FlaScanWindowConfig(void);
void FlaRestoreAllChannelInterrupt(void);
#if (!PS5017_EN)
void FlaScanWindowPerBitTrainResultByCE(void);
#endif /* (!PS5017_EN) */
AOM_VUC U8 FIPCheckBICS4Type(U8 *pubBufAddr);
void FipGetTrimTable(U8 ubChannel, U8 ubFlashCE, U8 ubDie, U8 *pubBufAddr);
#else  /*BURNER_MODE_EN*/
AOM_SYSTEM_AREA_INIT void FlaSetDMATimeOutEn(void);
AOM_SYSTEM_AREA_INIT void FlaClearDMATimeOutEn(void);
#endif /*BURNER_MODE_EN*/
AOM_VUC void FIPIdentifyEarlyFinalBICS4(U8 ubChannel, U8 ubFlashCE, U8 *pubBufAddr);
AOM_INIT void FlaInitMTManager(void);
AOM_INIT void FIPBICS4AIPREn(void);
U8 FlaGetFreeMTIndex(void);
void FlaAddFreeMTIndex(U8 ubMTIndex);
void FIPDelegateCmd(void);
void FIPBackupNonExecuteMT(U8 ubChannel, U8 ubBank, U8 ubQOS);
AOM_RETRY U8 FIPCalculateP4KErrorFrameMap(U8 ubMTIndex);
void FIPGetParityCheckSumErrFrame(U8 ubChannel, U8 ubP2KErrFrameMap);
U16 FIPGetINIPTYCHSUM(U8 ubChannel, U8 ub2KFrame);
void FlaSwitchLDPCHandler(U8 ubAssignLDPCMode);//�n��bcommon��
#if !MICRON_FSP_EN
void FlaGetParameterTable(U8 ubCH, U8 ubFlashCE, U8 *pubBufAdr);
#else //(!MICRON_FSP_EN)
AOM_VUC void FlaGetParameterTable(U8 ubCH, U8 ubFlashCE, U8 *pubBufAdr);
#endif //!MICRON_FSP_EN)
AOM_VUC void FipGetFlashReadRetryTable(U8 ubChannel, U8 ubFlashCE, U8 ubDie, U8 *pubBufAddr, U32 ulOffset, U8 ubFormat);
AOM_VUC void FipGetUniqueID(U8 ubChannel, U8 ubFlashCE, U8 ubDie, U8 *pubBufAddr);
AOM_VUC void FipGetUniqueIDHynix(U8 ubChannel, U8 ubFlashCE, U8 ubDie, U8 *pubBufAddr);

void FlaRmaLogStopMT( U8 ubChannel, U8 ubCE );
void FlaRmaLogTriggerandAddMT(U8 ubMtIdx, U8 ubQIdx, U8 ubChannel );
void FlaRmaLogClearForceEmpty(void);
void FlaRmaLogGetFIPCQ(U8 ubMtIdx);
AOM_INIT void FIPCalculateDummy(void);//����AOM_INIT
AOM_SPOR_2 void FlaInitC6Erase(void);

void FIPGenProgramFailJob(U8 ubChannel, U8 ubBank);
void FIPDirectPushMTCmd(U8 ubQIndex, U8 ubMTIndex, U16 uwFPUOffset, U32 ulFSA0, U32 ulFSA1, U8 ubALUSelect, U8 ubAutoPollIdx, U8 ubReadCmdVirtualAddr);
#if (MICRON_FSP_EN||INTEL_FSP_EN)
U8 FlaCheckFailStatus(TIEOUT_FORMAT_t *puo1stResult, TIEOUT_FORMAT_t *puo2ndResult, U32 *pulErrVCA, U8 ubChannel, U8 ubBank, U8 ubMode, U8 ubErrorMTIdx, U8 *pubFailPlaneCnt);
#else /* (MICRON_FSP_EN) */
//support multi die
U8 FlaCheckFailStatus(TIEOUT_FORMAT_t *puo1stResult, TIEOUT_FORMAT_t *puo2ndResult, U32 *pulErrVCA, U8 ubChannel, U8 ubBank, U8 ubDie, U8 ubMode, U8 ubErrorMTIdx, U8 *pubFailPlaneCnt);
#endif /* (MICRON_FSP_EN) */
#if ((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC))
AOM_ERROR_HANDLE2 void FlaMicronGlobalTriggerMT(U8 ubDie, U8 ubQIndex, U8 ubMTIndex, U16 uwFPUOffset, U8 ubInternalBusy);
AOM_ERROR_HANDLE2 void FlaSetWordlineBypassStatus(U8 ubQos, U8 ubDie, U8 ubQueueIdx, U8 ubSetStatus);
AOM_ERROR_HANDLE2 void FlaCheckWordlineBypassStatus(U8 ubQos, U8 ubDie, U8 ubQueueIdx, U8 ubCheckStatus);
#endif/*((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC))*/
U8 FIPGetNonExecuteMTNum(U8 ubChannel, U8 ubBank);
AOM_ERROR_HANDLE U8 FIPSetModerateRead(U8 ubChannel, U8 ubCE, U8 ubLUN);
#if (MICRON_FSP_EN)
#if (RELEASED_FW)
void FIPADWLSVPIODummyProgram(U8 ubChannel, U8 *pubBlkAddr, U8 ubMultiPlaneEn);
void FIPADWLSVPIODummyProgramAllChannel(U8 ubChannel);
#else /* (RELEASED_FW) */
AOM_INITINFO void FIPADWLSVPIODummyProgram(U8 ubChannel, U8 ubBlkAddr1, U8 ubBlkAddr2, U8 ubMultiPlaneEn);
AOM_INITINFO void FIPADWLSVPIODummyProgramAllChannel(U8 ubChannel);
#endif /* (RELEASED_FW) */
#endif /* (MICRON_FSP_EN) */
#if (PERFORMANCE_TEST_EN)
AOM_INIT void FlaZQCInfoInit(void);
#else /*(PERFORMANCE_TEST_EN)*/
AOM_INIT_2 void FlaZQCInfoInit(void);
#endif /*(PERFORMANCE_TEST_EN)*/


AOM_TT void FIPSetNandZQCL(void);
AOM_TT void FIPNandZQCL(void);
AOM_TT void FIPNandZQCWait(void);
AOM_TT void FlaNandZQCL(void);
void FlaNandTTZQC(U16 uwCurrentTemperatureExternalInKelvin);

#if(MICRON_FSP_EN || INTEL_FSP_EN)
void FIPACRREn(U8 ubEnable);
#endif

/****************************
 * NCS Verification Related *
 ****************************/
/*
 * Variable Name : gulVUCAddr
 * Description :
 *     This variable records the buffer address which is used to store 4K data
 *     that should be sent to NCS.  The buffer is allocated with seven frames
 *     at FWInit().  The allocation only occurred under NCS verification mode.
 * Used Functions :
 *     FIPVUCSendNCSInfo  @fip.c
 *     FWInit  @fw_init.c
 *     Retry_RS_Decode_Flow  @err_hdl_fpl_RS.c
 *     Retry_SB_TLC_determine_coarse_tuning_level  @err_hdl_fpl_sb_retry_api.c
 *     Retrry_SB_ProgNCS_CMD  @err_hdl_fpl_sb_retry_api.c
 *     Retry_SB_TRIG_CORR_PASS  @err_hdl_fpl_sb_retry_api.c
 *     Retry_SB_backup_restore_IBF  @err_hdl_fpl_sb_retry_api.c
 */
extern U32 gulVUCAddr;
extern U32 gulHWSettingAddr;
extern U8 gubNCSTLCOpenFlow;
extern U8 gubNCSQLC2ndPassFlow;
extern U32 gulSBRAIDAddr;
extern U32 gulSBRAIDSpareAddr;
extern U8 gubNCSAssignACRR;
extern L4KTable16B_t gL4kSpareBackup;
typedef enum NCSWriteInfoEnum {
	NCS_WRITE_FRAME_INFO = 0,
	NCS_WRITE_ERROR_BIT_INFO,
	NCS_WRITE_HW_SETTING_INFO,
	NCS_WRITE_DPS_SETTING_INFO
} NCSWriteInfoEnum_t;

/*
 * Special function for NCS verification which used to write
 * retry data back to NCS.
 */
AOM_RETRY void FIPVUCSendNCSInfo(U8 ubMTIndex, FlhMT_t *pMTTemplate, U8 ubInfoMode, U32 ulBufAddr, U32 ulSpareOffset, U8 ubCurrentStep);

#define M_GET_LAST_GLOBAL_TRIGGER_MT_IDX()			(R32_FCON[R32_FCON_MT_CFG] & (MT_IDX_MASK >> MT_IDX_SHIFT))
#define M_GET_LAST_GLOBAL_TRIGGER_QUEUE_IDX()		((R32_FCON[R32_FCON_MT_CFG] & QUE_IDX_MASK) >> QUE_IDX_SHIFT)
/***********************************
 * End of NCS Verification Related *
 ***********************************/

/*************
FIP Reg Api
*************/
//ch
#define M_FIP_GET_DQS_RE_MISMATCH(CH)	(R32_FCTL_CH[CH][R32_FCTL_DQS_CNT]!=R32_FCTL_CH[CH][R32_FCTL_RD_CNT])
#define M_FIP_GET_FPU_TIMEOUT(CH) 	(R32_FCTL_CH[CH][R32_FCTL_FPU_TRIG]&TIMEOUT_OCC_BIT)
#define M_FIP_CLEAR_INTERRUPT_BUSY(ubChannel)      (R32_FCTL_CH[ubChannel][R32_FCTL_INT_INF] &= CLR_INT_BUSY)
#define M_FIP_GET_INT_BUSY(CH)              (R32_FCTL_CH[CH][R32_FCTL_INT_INF])
#define M_FIP_GET_FPU_TRIG(CH)              (R32_FCTL_CH[CH][R32_FCTL_FPU_TRIG])
#define M_FIP_GET_NODMA_TIMEOUT_BUSY(CH)    ((R32_FCON[R32_FCON_IOR_CFG_4]) & ((BIT(CH) & NODMA_TO_CH_MASK) << NODMA_TO_CH_SHIFT))
#define M_FIP_SET_STOP_QRY(CH)              (R8_FCTL_CH[CH][R8_FCTL_ZIP_CFG_STOP_QRY] |= STOP_QRY_BIT0)
#define M_FIP_CLR_STOP_QRY(CH)              (R8_FCTL_CH[CH][R8_FCTL_ZIP_CFG_STOP_QRY] &= (~STOP_QRY_BIT0))
#define M_FIP_SET_STOP_QRY_ALL_CH()         (R8_FALL[R8_FCTL_ZIP_CFG_STOP_QRY] |= STOP_QRY_BIT0)
#define M_FIP_CLR_STOP_QRY_ALL_CH()         (R8_FALL[R8_FCTL_ZIP_CFG_STOP_QRY] &= (~STOP_QRY_BIT0))
#define M_FIP_SET_NO_DMA_TERMINATE(CH)      ((IOR_EN) ? (R32_FCTL_CH[CH][R32_FCTL_ERR_HANDLE_SET] |= NODMA_TERMINATE) : (NULL))
#define M_FIP_CLR_NO_DMA_TERMINATE(CH)      ((IOR_EN) ? (R32_FCTL_CH[CH][R32_FCTL_ERR_HANDLE_SET] &= (~NODMA_TERMINATE)) : (NULL))
#define M_FIP_SET_NO_DMA_TERMINATE_ALL_CH() ((IOR_EN) ? (R32_FALL[R32_FCTL_ERR_HANDLE_SET] |= NODMA_TERMINATE) : (NULL))
#define M_FIP_CLR_NO_DMA_TERMINATE_ALL_CH() ((IOR_EN) ? (R32_FALL[R32_FCTL_ERR_HANDLE_SET] &= (~NODMA_TERMINATE)) : (NULL))
#define M_FIP_GET_WP_STATE(CH)              (R32_FCTL_CH[CH][R32_FCTL_MT_STATE] & WP_STATE_SHIFT_MASK)
#define M_FIP_GET_ECC_INF(CH)				(R32_FCTL_CH[CH][R32_FCTL_ECC_INF] & ECC_MAX_BFC_SHIFT_MASK)
#define M_FIP_DYNAMIC_ODT_DIS(CH)		(R32_FCTL_CH[CH][R32_FCTL_DYNAMIC_ODT] &= ~(DYNAMIC_ODT_EN_BIT))
#define M_FIP_GET_LDPC_ITERATION(CH)		(R32_FCTL_CH[CH][R32_FCTL_ECC_CFG] & BIT_MASK(8))

#define M_FIP_SET_ECC_PARAM(IDX, PARAM)		do {\
												R32_FCON[R32_FCON_ECC_PARAM_CFG] &= (~(ECC_PARAM_SEL_MASK << ECC_PARAM_SEL_SHIFT )); \
												R32_FCON[R32_FCON_ECC_PARAM_CFG] |= ((IDX) << ECC_PARAM_SEL_SHIFT); \
												R32_FCON[R32_FCON_ECC_PARAM] = (PARAM); \
											} while (0)
#define M_FIP_LOAD_ECC_PARAM(TARGET)		do {\
												R32_FCON[R32_FCON_ECC_PARAM_CFG] |= (TARGET); \
												while (R32_FCON[R32_FCON_ECC_PARAM_CFG] & (TARGET)); \
											} while (0)
#if (PS5017_EN || PS5021_EN)
#define M_FIP_SET_LDPC_CFG_HB_LLR(CH, VAL)  do {\
													R32_FCTL_CH[CH][R32_FCTL_TRN_CFG] &= (~HB_LLR_SHIFT_MASK);\
													R32_FCTL_CH[CH][R32_FCTL_TRN_CFG] |= (((VAL) & HB_LLR_MASK) << HB_LLR_SHIFT);\
												} while (0)
#else /* (PS5017_EN || PS5021_EN) */
#define M_FIP_SET_LDPC_CFG_HB_LLR(VAL)		do {\
													R32_FCON[R32_FCON_LDPC_CFG] &= (~HB_LLR_SHIFT_MASK);\
													R32_FCON[R32_FCON_LDPC_CFG] |= (((VAL) & HB_LLR_MASK) << HB_LLR_SHIFT);\
												} while (0)
#endif /* (PS5017_EN || PS5021_EN) */



//all ch
#define M_FIP_SET_FORCE_EMPTY_ALL_CH()           do{ \
													U8 ubChannel = 0; \
													for (ubChannel = 0; ubChannel < BIT(gPCARule_Channel.ubBit_No); ubChannel++) {\
														FIPSetChannelForceEmpty(ubChannel);\
													}\
												  }while(0)

#define M_FIP_CLR_FORCE_EMPTY_ALL_CH()            do{ \
													U8 ubChannel = 0; \
													for (ubChannel = 0; ubChannel < BIT(gPCARule_Channel.ubBit_No); ubChannel++) {\
														FIPClearChannelForceEmpty(ubChannel, TRUE);\
													}\
												  }while(0)

#define M_FIP_SET_ULTRA_DMA_ALL_CHANNEL()	      (R32_FALL[R32_FCTL_DMA_CFG] |= (ULTRA_DMA_BIT))
#define M_FIP_CNT_MODE_EN_ALL_CHANNEL()		  (R32_FALL[R32_FCTL_CNT_ONE] = (SET_COUNTER_MODE_EN))
#define M_FIP_SELECT_CNT_ZERO_ALL_CHANNEL()	  (R32_FALL[R32_FCTL_CNT_ONE] &= (CLR_COUNT_ZERO))
#define M_FIP_CLEAR_ERROR_MAP_ALL_CHANNEL()		  (R32_FALL[R32_FCTL_MAP_CFG] &= ~(SET_ALL_UPD_EN))
#define M_FIP_SET_ERROR_MAP_ALL_CHANNEL(x)		  (R32_FALL[R32_FCTL_MAP_CFG] |= (x))
#define M_FIP_WRTIE_PROTECT_ALL_CHANNEL_DIS()	  (R32_FALL[R32_FCTL_IO_SET] &= (CLR_FLH_WP_L))
#define M_FIP_SET_INTERRUPT_ALL_CHANNEL(Type)	  (R32_FALL[R32_FCTL_INT_CFG] |= (Type))
#define M_FIP_CLEAR_CPU_INTERRUPT_ALL_CHANNEL()	  (R32_FALL[R32_FCTL_INT_CFG_1] &= ~(SET_CPU_INT_ALL_ON))
//top
#define M_FIP_CLEAR_IRAM_PARITY_CHECK()			  (R32_FCON[R32_FCON_IRAM_PERR] &= ~(IRAM_PARITY_CHECK_EN_BIT))
#define M_FIP_SET_NONEXCUTE_MTQ_SELECT(Channel, Bank)	(R32_FCON[R32_FCON_MTQ_NON_EXE_SEL] = ( M_SET_MTQ_BODY_CH_SEL(Channel) | M_SET_MTQ_BODY_CE_SEL(Bank) ))
#define M_FIP_GET_NONEXCUTE_MTQ_PTR()		(((R32_FCON[R32_FCON_MTQ_NON_EXE_SEL]) & FCON_MTQ_RD_PTR_SHIFT_MASK) ? 1 : 0)
#define M_FIP_GET_NONEXCUTE_MTQ_MT_CONFIG(MTQStartPtr)		(R64_FCON[R64_FCONLL_MTQ_BODY_0 + MTQStartPtr])
#define M_FIP_CLEAR_CHANNEL_SELECT_FLAG()     (R32_FCON[R32_FCON_INFO] |= ~(FLH_CH_SEL_SHIFT_MASK))
#define M_FIP_SET_CHANNEL_SELECT_FLAG(x)	(R32_FCON[R32_FCON_INFO] |= (x))

#define M_FIP_CHK_INT_BUSY(N) (R32_FCTL_CH[N][R32_FCTL_INT_INF] & INT_BUSY_BIT)
#define M_FIP_CHK_FPU_BUSY(N) (R32_FCTL_CH[N][R32_FCTL_FPU_TRIG] & CHK_FPU_BUSY)
#define M_FIP_CHK_FPU_TIMEOUT_IDLE(N) (SRQ_SVL_BIT == (R32_FCTL_CH[N][R32_FCTL_FPU_TRIG] & (FPU_TRIGGER_BIT | SRQ_SVL_BIT)))
#define M_FIP_CHK_FPU_SIGN_OFF_BUSY(N) (R32_FCTL_CH[N][R32_FCTL_FPU_TRIG] & SIGN_OFF_BUSY_BIT)
#if (PS5017_EN)
#define M_FIP_CHK_ALL_CH_BUSY() (!( (!M_FIP_CHK_INT_BUSY(0)) && (!M_FIP_CHK_FPU_BUSY(0)) && (!M_FIP_CHK_INT_BUSY(1)) && (!M_FIP_CHK_FPU_BUSY(1))))
#else /* (PS5017_EN) */
#define M_FIP_CHK_ALL_CH_BUSY() (M_FIP_CHK_INT_BUSY(0) || M_FIP_CHK_FPU_BUSY(0) || M_FIP_CHK_INT_BUSY(1) || M_FIP_CHK_FPU_BUSY(1) || M_FIP_CHK_INT_BUSY(2) || M_FIP_CHK_FPU_BUSY(2) || M_FIP_CHK_INT_BUSY(3) || M_FIP_CHK_FPU_BUSY(3))
#endif /* (PS5017_EN) */
#define M_FIP_CHK_Q_NON_EXE_NUM(Channel, Bank) ((R32_FCTL_CH[Channel][R32_FCTL_MTQ_INF_0] >> ((Bank) * MTQ_NON_EXE_NUM_SHIFT)) & MTQ_NON_EXE_NUM_MASK)
#define M_FIP_CHK_Q_NON_EXE_NUM_1(Channel, Bank) ((R32_FCTL_CH[Channel][R32_FCTL_MTQ_INF_1] >> ((Bank) * MTQ_NON_EXE_NUM_SHIFT)) & MTQ_NON_EXE_NUM_MASK)
#if (PS5017_EN|| PS5021_EN)
#define M_FIP_CHK_MTQ_IS_BUSY(CH, BANK)			(R32_FCTL_CH[CH][R32_FCTL_MT_TRIG] & (BIT(BANK) << QUEUE_7_TO_0_ST_SHIFT))//(R32_FCON[R32_FCON_MT_STS_1] &  BIT(((BANK) * MAX_CHANNEL) + (CH)))
#else /* (PS5017_EN|| PS5021_EN) */
#define M_FIP_CHK_MTQ_IS_BUSY(CH, BANK)			(R32_FCON[R32_FCON_MT_STS_1] &  BIT(((BANK) * 4) + (CH)))
#endif /* (PS5017_EN|| PS5021_EN) */
#define M_FIP_CHK_FIP_INT_VCT(Channel)	(R32_FCTL_CH[Channel][R32_FCTL_FPU_INT_VCT] & CUR_FPU_INT_VCT_IND_MASK)
#define M_FIP_CHK_DMA_DONE_VCT(Channel) (R32_FCTL_CH[Channel][R32_FCTL_DMA_DONE_VCT] & DMA_DONE_INT_VCT_IND_MASK)
#define M_FIP_CHK_DMA_INT_VCT(Channel) (R32_FCTL_CH[Channel][R32_FCTL_FRONT_VCT] & FRONT_DMA_INT_VCT_IND_MASK)
#define M_FIP_SET_DBG_INF_CHK_PRELOAD_STATE(CHANNEL)  (R32_FCTL_CH[CHANNEL][R32_FCTL_DBG_INF] = 0x60000)
#define M_FIP_CHK_QUEUE_PRELOAD_STATE(CHANNEL, BANK)	(R32_FCTL_CH[CHANNEL][R32_FCTL_DBG_INF]&BIT((BANK)+24))
#define M_FIP_CLEAR_IRAM_COPY_CONTROL()	(R32_FCON[R32_FCON_IRC_CTL] = 0)
#define M_FIP_SET_IRAM_COPY_CONTROL(x)	(R32_FCON[R32_FCON_IRC_CTL] = (x))
#define M_FIP_SET_CE_REMAPPING(x,y)		(R32_FCON[R32_FCON_FCE_SEL_0 + y] = (x))
#define M_FIP_SET_IRAM_INIT()	        (R32_FCON[R32_FCON_IRC_CTL] |= SET_IRAM_INIT_TRIG)
#define M_FIP_CHECK_IRAM_INIT_BUSY()	(R32_FCON[R32_FCON_IRC_CTL] & CHK_IRAM_INIT_BUSY)
#define M_FIP_SET_ALL_CE_VALUE(x)		(R32_FCON[R32_FCON_FCE_ENB] = (x))
#define M_FIP_CE_EN(CE)					(R32_FCON[R32_FCON_FCE_ENB] |= (CE))
#define M_FIP_CE_DIS(CE)			(R32_FCON[R32_FCON_FCE_ENB] &= ~(CE))
#define M_FIP_SET_LCA_SRC_IRAM_L4K()	(R32_FCON[R32_FCON_FLH_FUNC] |= (LCA_SRC_SEL_BIT))
#define M_FIP_SPLIT_READ_WRITE_CLOCK()	(R32_FCON[R32_FCON_FLHCLK_SPLITE_EN] |= (SPLIT_RW_FLH_CLK_EN_BIT))
#define	M_FIP_GET_RANDOMIZE_PAGE_ADDR_REGION()			((R32_FCON[R32_FCON_GEN_CONV_IDX4] >> RND_PG_ADR_REGION_SHIFT) & RND_PG_ADR_REGION_MASK)

/*
#define M_FIP_CHK_ALL_CH_BUSY()	do {\
									U8 ubChannel;\
									for (ubChannel = 0; ubChannnel < MAX_CHANNEL; ubChannel++) { \
									M_FIP_CHK_FPU_BUSY(ubChannel);\
									M_FIP_CHK_INT_BUSY(ubChannel);\
								} while(0)
*/
#define M_FIP_GET_INT_INFO(CH)			(R32_FCTL_CH[CH][R32_FCTL_INT_INF])
#define M_FIP_GET_MAP_INFO(CH)			(R32_FCTL_CH[CH][R32_FCTL_MAP_INF])
#define M_FIP_GET_MAP_CONFIG(CH)			(R32_FCTL_CH[CH][R32_FCTL_MAP_CFG])
#define M_FIP_CHK_NEXT_FIFO_EXIST(CH)	((R32_FCTL_CH[CH][R32_FCTL_MAP_CFG] & CHK_NEXT_FIFO) != 0)
#define M_FIP_SWITCH_FIFO_MAP(CH)		(R32_FCTL_CH[CH][R32_FCTL_MAP_CFG] |= PING_PONG_FIFO_SWITCH_BIT)
#define M_FIP_CLEAR_PING_PONG_FIFO_SELECT(ubChannel) 	(R32_FCTL_CH[ubChannel][R32_FCTL_MAP_CFG] &= (~PING_PONG_FIFO_SEL_SHIFT_MASK))
#define M_FIP_SET_PING_PONG_FIFO_SELECT(ubChannel,x)	(R32_FCTL_CH[ubChannel][R32_FCTL_MAP_CFG] |= (x << PING_PONG_FIFO_SEL_SHIFT))
#define M_FIP_GET_QOS_INFO(CH, BANK)	(( R32_FCTL_CH[CH][R32_FCTL_QOS_DBG_INF_0] & (BIT0  << (MT_NOR0_QOS1_SEL_SHIFT + (BANK))) ) >> (MT_NOR0_QOS1_SEL_SHIFT + (BANK)))
#define M_FIP_CLR_FTA_COL()		(R32_FCTL_CH[CH_ALL][R32_FCTL_FTA_COL] = 0x00000000)
#define M_FIP_CHK_RBY_AND()		(R32_FCTL_CH[0][R32_FCTL_RBY_INF] & FLH_CE_RBY_AND_SGN_BIT)
#define M_FIP_CHK_RBY_AND_CHANNEL(CH)		(R32_FCTL_CH[CH][R32_FCTL_RBY_INF] & FLH_CE_RBY_AND_SGN_BIT)
#define M_FIP_CHK_CH_RUNC(N) (R32_FCTL_CH[N][R32_FCTL_INT_INF] & UNCORR_UPD_BIT)
#define M_FIP_CHK_CH_BUSY(N) (R32_FCTL_CH[N][R32_FCTL_INT_INF] & INT_BUSY_BIT)

#define M_FIP_CHECK_INT_RDY(CHANNEL, CE)		(R32_FCTL_CH[CHANNEL][R32_FCTL_INT_RDY] & BIT(CE))
#define M_FIP_CHECK_FORCE_EMPTY(CHANNEL, CE)		(R32_FCTL_CH[CHANNEL][R32_FCTL_MTQ_CFG] & BIT(CE))

//IOR related
#define M_FIP_GET_CUR_GRP()         	do {\
											R32_FCON[R32_FCON_IOR_CFG_2] &= ~(IDX_SEL_MASK << IDX_SEL_SHIFT); \
											R32_FCON[R32_FCON_IOR_CFG_2] |= (IOR_CUR_GRP_IDX << IDX_SEL_SHIFT); \
											((R32_FCON[R32_FCON_IOR_CFG] >> IDX_VAL_SHIFT) & IDX_VAL_MASK);\
										} while (0)
#define M_FIP_CUR_IDX_SEL(SEL_IDX)         	do {\
											R32_FCON[R32_FCON_IOR_CFG_2] &= ~(IDX_SEL_MASK << IDX_SEL_SHIFT); \
											R32_FCON[R32_FCON_IOR_CFG_2] |= ((SEL_IDX) << IDX_SEL_SHIFT); \
										} while (0)
#define M_FIP_GET_IOR_IDX_VAL()      	((R32_FCON[R32_FCON_IOR_CFG] >> IDX_VAL_SHIFT) & IDX_VAL_MASK)

#define	BOOKING_TABLE_SEL_ACCUMULATED_NUM	(0)
#define	BOOKING_TABLE_SEL_EXECUTED_NUM		(1)
#define	BOOKING_TABLE_SEL_GROUP_SIZE		(2)
#define BOOKING_TABLE_SEL_BOOKING_NUM		(3)
#define M_FIP_SET_IOR_BOOKING_TABLE_IDX_AND_SEL(GRP_IDX, BOOKING_TABLE_SEL) do {\
                                                     R32_FCON[R32_FCON_IOR_CFG_2] &= ~(BOOKING_TABLE_SEL_MASK << BOOKING_TABLE_SEL_SHIFT); \
                                                     R32_FCON[R32_FCON_IOR_CFG_2] |= (BOOKING_TABLE_SEL & BOOKING_TABLE_SEL_MASK) << BOOKING_TABLE_SEL_SHIFT; \
                                                     R32_FCON[R32_FCON_IOR_CFG_2] &= ~(BOOKING_TABLE_IDX_MASK << BOOKING_TABLE_IDX_SHIFT); \
                                                     R32_FCON[R32_FCON_IOR_CFG_2] |= ((GRP_IDX) & BOOKING_TABLE_IDX_MASK) << BOOKING_TABLE_IDX_SHIFT; \
                                                 } while (0)

#define M_FIP_GET_GROUP_SIZE_FIFO_FULL() (R32_FCON[R32_FCON_IOR_CFG_1] & CHK_GRP_SIZE_FIFO_ALL_FULL_BIT)
#define M_FIP_SET_GROUP_SIZE_FIFO(GRP_IDX, GRP_SIZE)	(R32_FCON[R32_FCON_IOR_CFG_1] = ((GRP_IDX & FW_GRP_IDX_MASK) << FW_GRP_IDX_SHIFT) | ((GRP_SIZE & FW_GRP_SIZE_MASK) << FW_GRP_SIZE_SHIFT))
#define M_FIP_GET_GROUP_SIZE_FIFO_EMPTY() (R8_FCON[R8_FCON_IOR_CFG_1_FIFO_EMPTY])

#define M_FIP_GET_BOOKING_TABLE_STATE()	((R32_FCON[R32_FCON_IOR_CFG_2] >> BK_STATE_SHIFT) & BK_STATE_MASK)

#define M_FIP_GET_BOOKING_TABLE_VALUE()	((R32_FCON[R32_FCON_IOR_CFG_3] >> BOOKING_TABLE_VAL_SHIFT) & BOOKING_TABLE_VAL_MASK)

#if (PS5017_EN)
#define BOOKING_TABLE_VALUE_PLUS_ONE			(0x1FF)  //E17
#define BOOKING_TABLE_VALUE_MINUS_ONE			(0x1FE)  //E17
#define M_FIP_SET_BOOKING_TABLE_VALUE(VALUE)	(R32_FCON[R32_FCON_IOR_CFG_3] = ((VALUE) & BOOKING_TABLE_VAL_MASK))  //E17
#endif /* (PS5017_EN) */
#define M_FIP_SET_BOOKING_NUM_RETURN() 	(R32_FCON[R32_FCON_IOR_CFG_4] |= RETURN_BOOKING_BIT)
#define M_FIP_CHECK_BOOKING_NUM_RETURN() (R32_FCON[R32_FCON_IOR_CFG_4] & RETURN_BOOKING_BIT)


#define	M_FIP_GET_LOGICAL_CE(PHYSICAL_CE) (R8_FCON[R8_FCON_FCE_SEL_0 + (PHYSICAL_CE)])

#define M_FIP_SET_RAIDECC_PB_SEL(PB_IDX) do{ \
											U32 ulValue; \
											ulValue = R32_FCON[R32_FCON_RAIDECC_PB_SEL]; \
											ulValue &= ~(RAIDECC_PB_SEL_MASK << RAIDECC_PB_SEL_SHIFT);\
											ulValue |= ((PB_IDX) & RAIDECC_PB_SEL_MASK) << RAIDECC_PB_SEL_SHIFT; \
											R32_FCON[R32_FCON_RAIDECC_PB_SEL] = ulValue; \
										}while(0)

#define M_FIP_RAIDECC_PB_STS(TAG_IDX) do{ \
										U32 ulValue; \
										ulValue = R32_FCON[R32_FCON_RAIDECC_PB_STS]; \
										ulValue &= ~(RAIDECC_PB_TAG_NUM_MASK << RAIDECC_PB_TAG_NUM_SHIFT);\
										ulValue |= ((TAG_IDX) & RAIDECC_PB_TAG_NUM_MASK) << RAIDECC_PB_TAG_NUM_SHIFT; \
										ulValue |= BIT(RAIDECC_PB_STS_SHIFT); \
										R32_FCON[R32_FCON_RAIDECC_PB_STS] = ulValue; \
									}while(0)
#if (PS5017_EN)
#if (IM_B47R || IM_B37R)
#define M_FIP_CHECK_FPU_IS_DMA_WITH_POLL(FPU) (FPU_PTR_OFFSET(fpu_entry_dma_r_06_e0_poll_ready) == (FPU))
#define M_FIP_CHECK_FPU_IS_NOT_DMA_WITH_POLL(FPU) (FPU_PTR_OFFSET(fpu_entry_dma_r_06_e0_poll_ready) != (FPU))
#else /* (IM_B47R) */
#define M_FIP_CHECK_FPU_IS_DMA_WITH_POLL(FPU) (FALSE) //(FPU_PTR_OFFSET(fpu_entry_dma_r_06_e0_poll_ready) == (FPU))
#define M_FIP_CHECK_FPU_IS_NOT_DMA_WITH_POLL(FPU) (TRUE) //(FPU_PTR_OFFSET(fpu_entry_dma_r_06_e0_poll_ready) != (FPU))
#endif /* (IM_B47R) */
#else /* (PS5017_EN) */
#define M_FIP_CHECK_FPU_IS_DMA_WITH_POLL(FPU) (FALSE) // E13 doesn't have fpu_entry_dma_r_06_e0_poll_ready
#define M_FIP_CHECK_FPU_IS_NOT_DMA_WITH_POLL(FPU) (TRUE) // E13 doesn't have fpu_entry_dma_r_06_e0_poll_ready
#endif /* (PS5017_EN) */

INLINE void FlaSetBackupRestoreAddr(U32 *ulBufAddr, U32 ulSprOffset, U8 ubBufNum)
{
	R32_FALL[R32_FCTL_BACK_RESTORE] |= DRAM_MULTI_EN;
#if (PS5013_EN)
	U8 ubi;
	for (ubi = 0; ubi  < ubBufNum; ubi ++) {
		/*
		 * FCTL_BACK_RESTORE[18](DRAM_MULTI_EN) 		 �]1�|�Ұ�Back-up and restore�䴩4��buffer address�i�H�]�w
		 *	FCTL_BACK_RESTORE[17:16](DRAM_ADDR_SEL) ��ܭn�]�w�ĴX��DRAM_ADDR(�粒��g FCTL_RAW_DMA_ADR)�`�@��4�ӥi�H�]�w
		 */
		R32_FALL[R32_FCTL_BACK_RESTORE] &= ~(DRAM_ADDR_SEL_SHIFT_MASK);
		R32_FALL[R32_FCTL_BACK_RESTORE] |= M_SET_DRAM_ADDR(ubi);
		R32_FALL[R32_FCTL_RAW_DMA_ADR] = ulBufAddr[ubi];
	}
	R32_FALL[R32_FCTL_BACK_RESTORE] &= ~BACK_RESTORE_IADDR_MASK;
	R32_FALL[R32_FCTL_BACK_RESTORE] |= (ulSprOffset & BACK_RESTORE_IADDR_MASK);
#endif /* (PS5013_EN) */
}

INLINE void FIPRestoreDefaultZQPNValue()
{
	if (M_GET_VUC_POWER_MODE()) { // 1.2v
		R32_FALL[R32_FCTL_PCA_CFG_1] &= ~ (PAD_ZQ_P_MASK << PAD_ZQ_P_SHIFT);
		R32_FALL[R32_FCTL_PCA_CFG_1] &= ~ (PAD_ZQ_N_MASK << PAD_ZQ_N_SHIFT);
		R32_FALL[R32_FCTL_PCA_CFG_1] |=   (FIP_PAD_ZQ_P_DEFAULT_VALUE_12V << PAD_ZQ_P_SHIFT);
		R32_FALL[R32_FCTL_PCA_CFG_1] |=   (FIP_PAD_ZQ_N_DEFAULT_VALUE_12V << PAD_ZQ_N_SHIFT);
	}
}

#define M_FIP_SET_RS_PB_SELECT(RS_TAG)	(R8_FCON[R8_FCON_RAIDECC_PB_SEL] = (RS_TAG))
#define M_FIP_SET_RS_PB_STATUS_BIT()		(R32_FCON[R32_FCON_RAIDECC_PB_STS] |= RAIDECC_PB_STS_BIT)
#define M_FIP_IS_PARITY_BUF_EXIST()		(R32_FCON[R32_FCON_RAIDECC_PB_STS] & RAIDECC_PB_STS_BIT)
#define M_FIP_GET_RS_TAG_BY_PB()		(R32_FCON[R32_FCON_RAIDECC_PB_STS] & RAIDECC_PB_TAG_NUM_MASK)
#define M_FIP_CLEAR_RAIDECC_PB_STATUS()	(R32_FCON[R32_FCON_RAIDECC_PB_STS] = 0)
#define M_FIP_GET_ZQC_EN() 			(R32_FCON[R32_FCON_ZQCAL_CFG] & ZQCAL_EN_BIT)

#define M_FIP_L4KSPARE_PTR_ALIGN_256_BYTE(PTR)	((PTR) & (~(BIT_MASK(8))))

#define M_FIP_SET_EOT(BITNUM) do { \
                            		R32_FALL[R32_FCTL_MAP_CFG] |= OVER_ECC_ERR_EN_BIT; \
                            		R32_FALL[R32_FCTL_INT_CFG] |= OVR_ERR_INT_EN_BIT; \
                            		R32_FCON[R32_FCON_SPECIAL] &= (~ECC_THRESHOLD_SHIFT_MASK); \
                            		R32_FCON[R32_FCON_SPECIAL] |= M_SET_ECC_THRESHOLD_NUM(BITNUM); \
                               } while(0)

#define M_FIP_EOT_INTERRUPT_DIS()	(R32_FALL[R32_FCTL_INT_CFG] &= (~OVR_ERR_INT_EN_BIT))

AOM_INIT void FIPResetPAD(void);
AOM_LPM void FIPFlashAndICToDefaultMode(void);
AOM_VUC_3 void FIPMicronDMAConfig(U8 ubCH, U8 ubFrameStartPtr, U8 ubFrameCnt, U32 ulSpareAdr, U32 ulDatAdr);
U8 FIPIsVirtualPBExist(U8 ubVirtualPBIdx);
U8 FIPIsVirtualPBExistAndMatchParityTagIdx(U8 ubVirtualPBIdx, U8 ubParityTagIdx);
void FIPSetAllChannelForceEmpty(void);
void FIPClearAllChannelForceEmpty(void);
AOM_INIT void MicronLoadBFEATrimTable(void);
AOM_INIT void FipMapLogicalCEToPhysicalCE(void);

#if ((PS5021_EN || PS5017_EN) && BURNER_MODE_EN) || (RDT_RECORD_SCAN_WINDOW_LOG)
U8 FIPRemapCE (U8 ubChannel, U8 ubLogicalCE);
#endif	/* (PS5017_EN && BURNER_MODE_EN) */

#if ((BURNER_MODE_EN) || (HOST_MODE == SATA))	//exclude NVME FW
AOM_INIT U8 FIPLoadSwitchTest(void);
#endif /* ((BURNER_MODE_EN) || (HOST_MODE == SATA)) */

#if (COP0_BACKUP_P4K_WORKAROUND)
AOM_INIT_4 void FIPSetBackupP4KWorkaroundDelay(void);
#endif /*COP0_BACKUP_P4K_WORKAROUND*/
#endif /*_FIP_API_H_ */
