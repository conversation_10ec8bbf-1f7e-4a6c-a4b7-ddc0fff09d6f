#include "vuc/VUC_SetSpecificGlobalValue.h"
#include "host/VUC_handler_api.h"
#include "nvme_api/nvme/shr_hal_nvme_log.h"
#include "nvme_api/nvme/shr_hal_nvme_api.h"
#include "ftl/ftl_nrw_api.h"

U32 gulVUCAvailableSpare = 0;
U32 gulVUCTotalReduceUnit = 0;

AOM_NRW_2 static void VUC_SetSpecificGlobalValueCheckSmart()
{
#if(HOST_MODE == NVME)
	U8 ubPercentageOfReduceUnits;
	U8 ubAvailableSpare;
	P_HOST_GEOMETRY_PTR pHostPrtInfo = (P_HOST_GEOMETRY_PTR) gpHostPrtInfo;
	SMART_LOG_SAVED_PTR pSmartLog = pHostPrtInfo->pSmartLogSave;
	ubPercentageOfReduceUnits = (AVAILABLE_SPARE_MAX * gulVUCTotalReduceUnit / gulVUCAvailableSpare);
	ubAvailableSpare = ((ubPercentageOfReduceUnits >= (AVAILABLE_SPARE_MAX - 1)) ? (AVAILABLE_SPARE_MIN + 1) : (AVAILABLE_SPARE_MAX - ubPercentageOfReduceUnits));
	if (ubAvailableSpare <= gNRWInfo.ubAvailableSpareThreshold) {
		nvme_smart_set_cri_warn_api(pSmartLog, SMARTH_CRITICAL_WARN_AVAIL_SPACE_BELOW_TH, TRUE);
		gpVTDBUF->HostReset.SMARTOfAEROnce.btSpare = TRUE;
	}
	else {
		nvme_smart_set_cri_warn_api(pSmartLog, SMARTH_CRITICAL_WARN_AVAIL_SPACE_BELOW_TH, FALSE);
		gpVTDBUF->HostReset.SMARTOfAEROnce.btSpare = FALSE;
	}

	if (gulPercentageUsedThreshold > (gpVT->ulTotalEraseCount * PERCENTAGE_USED_MAX)) {
		nvme_smart_set_cri_warn_api(pSmartLog, SMARTH_CRITICAL_WARN_RELIABILITY_DEGRADE, FALSE);
		gpVTDBUF->HostReset.SMARTOfAEROnce.btPrecentageUsed = FALSE;
	}
#endif /*(HOST_MODE == NVME)*/
}

void VUC_SetSpecificGlobalValue(VUC_OPT_HCMD_PTR_t pCmd)
{
	if (TRUE == gFlhEnv.ulFlashDefaultType.BitMap.btNoSupport) {
		pCmd->ubState = CMD_ERROR;
		gVUCVar.ubVucCmdErrStatus = HOST_ERROR_FEATURE;
		return;
	}

	U8 ubSubfeature = pCmd->vuc_sqcmd.vendor.SetSpecificGlobalValue.ubSubFeature;
	U32 ulValue = pCmd->vuc_sqcmd.vendor.SetSpecificGlobalValue.ulValue;

	switch (ubSubfeature) {
	case VUC_SET_TOTAL_EC:
		gpVT->ulTotalEraseCount = ulValue;
		VUC_SetSpecificGlobalValueCheckSmart();
		break;
	case VUC_SET_AVAILABLE_SPARE:
		gulVUCAvailableSpare = ulValue;
		VUC_SetSpecificGlobalValueCheckSmart();
		break;
	case VUC_TOTAL_REDUCE_UNIT:
		gulVUCTotalReduceUnit = ulValue;
		VUC_SetSpecificGlobalValueCheckSmart();
		break;
	default:
		break;
	}
}
