/****************************************************************************
*
*  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
*  All rights reserved
*
*  The content of this document is confidential and shall be applied
*  subject to the terms and conditions of the license agreement and
*  other applicable laws. Any unauthorized access, use or disclosure
*  of this document is strictly prohibited and may be punishable
*  under laws.
*
*  tcg_security_log.h
*
*
*
****************************************************************************/

#ifndef TCG_SECURITY_LOG_H_
#define TCG_SECURITY_LOG_H_

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "tcg_conf.h"

/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */
// Security log level
#define TCG_SECURITY_LOG_CLASS_SIGNIFICANT_EVENT    (0x1000)
#define TCG_SECURITY_LOG_CLASS_FW_ERROR             (0x2000)
#define TCG_SECURITY_LOG_CLASS_AUTHORITY_ERROR      (0x3000)
#define TCG_SECURITY_LOG_CLASS_NOT_SUPPORT_ERROR    (0x4000)
#define TCG_SECURITY_LOG_CLASS_SESSION_ERROR        (0x5000)
#define TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR  (0x6000)
#define TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR         (0x7000)

#define TCG_SECURITY_LOG_CLASS_REPORTED_ERROR       (0xA000)
#define TCG_SECURITY_LOG_CLASS_HANDLED_ERROR        (0xB000)
#define TCG_SECURITY_LOG_CLASS_WRITEPROTECT_ERROR   (0xC000)
#define TCG_SECURITY_LOG_CLASS_METHOD               (0xD000)
#define TCG_SECURITY_LOG_CLASS_INFO                 (0xE000)
#define TCG_SECURITY_LOG_CLASS_OTHERS               (0xF000)

#define TCG_SECURITY_LOG_CLASS_MASK        (0xF000)
#define TCG_SECURITY_LOG_LEVEL             (TCG_SECURITY_LOG_CLASS_SESSION_ERROR) // Save the log_class <= TCG_SECURITY_LOG_LEVEL

// Maximum Error code: 0xE8
// Maximum Method code: 0x29
// Maximum Info code: 0x9B

// Significant events (1)
#define TCG_SECURITY_LOG_SIGNIFICANT_EVENT_0x0001	(0x0001 | TCG_SECURITY_LOG_CLASS_SIGNIFICANT_EVENT) // Activate
#define TCG_SECURITY_LOG_SIGNIFICANT_EVENT_0x0002	(0x0002 | TCG_SECURITY_LOG_CLASS_SIGNIFICANT_EVENT) // Revert
#define TCG_SECURITY_LOG_SIGNIFICANT_EVENT_0x0003	(0x0003 | TCG_SECURITY_LOG_CLASS_SIGNIFICANT_EVENT) // RevertSP
#define TCG_SECURITY_LOG_SIGNIFICANT_EVENT_0x0004	(0x0004 | TCG_SECURITY_LOG_CLASS_SIGNIFICANT_EVENT) // Transaction recover tables - Power cycle
#define TCG_SECURITY_LOG_SIGNIFICANT_EVENT_0x0005	(0x0005 | TCG_SECURITY_LOG_CLASS_SIGNIFICANT_EVENT) // Transaction recover tables - RestoreSP
#define TCG_SECURITY_LOG_SIGNIFICANT_EVENT_0x0006	(0x0006 | TCG_SECURITY_LOG_CLASS_SIGNIFICANT_EVENT) // Transaction recover tables - Update security version
#define TCG_SECURITY_LOG_SIGNIFICANT_EVENT_0x0007	(0x0007 | TCG_SECURITY_LOG_CLASS_SIGNIFICANT_EVENT) // HRST
#define TCG_SECURITY_LOG_SIGNIFICANT_EVENT_0x0100	(0x0100 | TCG_SECURITY_LOG_CLASS_SIGNIFICANT_EVENT) // Start update security version: (StartVer)
#define TCG_SECURITY_LOG_SIGNIFICANT_EVENT_0x0200   (0x0200 | TCG_SECURITY_LOG_CLASS_SIGNIFICANT_EVENT) // Target update security version: (TargetVer)
#define TCG_SECURITY_LOG_SIGNIFICANT_EVENT_0x0300	(0x0300 | TCG_SECURITY_LOG_CLASS_SIGNIFICANT_EVENT) // Processing update security version: (ProcessingVer)

// Error(2) - FW - FW coding assert / Buffer related error / Table in flash error
#define TCG_SECURITY_LOG_FW_ERROR_0x0001   (0x0001 | TCG_SECURITY_LOG_CLASS_FW_ERROR)
#define TCG_SECURITY_LOG_FW_ERROR_0x0002   (0x0002 | TCG_SECURITY_LOG_CLASS_FW_ERROR)
#define TCG_SECURITY_LOG_FW_ERROR_0x0003   (0x0003 | TCG_SECURITY_LOG_CLASS_FW_ERROR)
#define TCG_SECURITY_LOG_FW_ERROR_0x0004   (0x0004 | TCG_SECURITY_LOG_CLASS_FW_ERROR) // Abandoned
#define TCG_SECURITY_LOG_FW_ERROR_0x0005   (0x0005 | TCG_SECURITY_LOG_CLASS_FW_ERROR) // Abandoned
#define TCG_SECURITY_LOG_FW_ERROR_0x0006   (0x0006 | TCG_SECURITY_LOG_CLASS_FW_ERROR) // Abandoned
#define TCG_SECURITY_LOG_FW_ERROR_0x00D0   (0x00D0 | TCG_SECURITY_LOG_CLASS_FW_ERROR)
#define TCG_SECURITY_LOG_FW_ERROR_0x00D1   (0x00D1 | TCG_SECURITY_LOG_CLASS_FW_ERROR)
#define TCG_SECURITY_LOG_FW_ERROR_0x00D2   (0x00D2 | TCG_SECURITY_LOG_CLASS_FW_ERROR)
#define TCG_SECURITY_LOG_FW_ERROR_0x00D3   (0x00D3 | TCG_SECURITY_LOG_CLASS_FW_ERROR)
#define TCG_SECURITY_LOG_FW_ERROR_0x00D4   (0x00D4 | TCG_SECURITY_LOG_CLASS_FW_ERROR) // Abandoned
#define TCG_SECURITY_LOG_FW_ERROR_0x00E3   (0x00E3 | TCG_SECURITY_LOG_CLASS_FW_ERROR)

// Error(3) - Authority - Login fail / block by block SID
#define TCG_SECURITY_LOG_AUTHORITY_ERROR_0x0009   (0x0009 | TCG_SECURITY_LOG_CLASS_AUTHORITY_ERROR)
#define TCG_SECURITY_LOG_AUTHORITY_ERROR_0x0040   (0x0040 | TCG_SECURITY_LOG_CLASS_AUTHORITY_ERROR)
#define TCG_SECURITY_LOG_AUTHORITY_ERROR_0x0044   (0x0044 | TCG_SECURITY_LOG_CLASS_AUTHORITY_ERROR)
#define TCG_SECURITY_LOG_AUTHORITY_ERROR_0x004D   (0x004D | TCG_SECURITY_LOG_CLASS_AUTHORITY_ERROR)
#define TCG_SECURITY_LOG_AUTHORITY_ERROR_0x004E   (0x004E | TCG_SECURITY_LOG_CLASS_AUTHORITY_ERROR)
#define TCG_SECURITY_LOG_AUTHORITY_ERROR_0x004F   (0x004F | TCG_SECURITY_LOG_CLASS_AUTHORITY_ERROR)
#define TCG_SECURITY_LOG_AUTHORITY_ERROR_0x0050   (0x0050 | TCG_SECURITY_LOG_CLASS_AUTHORITY_ERROR)
#define TCG_SECURITY_LOG_AUTHORITY_ERROR_0x0051   (0x0051 | TCG_SECURITY_LOG_CLASS_AUTHORITY_ERROR)
#define TCG_SECURITY_LOG_AUTHORITY_ERROR_0x0052   (0x0052 | TCG_SECURITY_LOG_CLASS_AUTHORITY_ERROR)
#define TCG_SECURITY_LOG_AUTHORITY_ERROR_0x0053   (0x0053 | TCG_SECURITY_LOG_CLASS_AUTHORITY_ERROR)
#define TCG_SECURITY_LOG_AUTHORITY_ERROR_0x0054   (0x0054 | TCG_SECURITY_LOG_CLASS_AUTHORITY_ERROR)
#define TCG_SECURITY_LOG_AUTHORITY_ERROR_0x0055   (0x0055 | TCG_SECURITY_LOG_CLASS_AUTHORITY_ERROR)
#define TCG_SECURITY_LOG_AUTHORITY_ERROR_0x0093   (0x0093 | TCG_SECURITY_LOG_CLASS_AUTHORITY_ERROR)
#define TCG_SECURITY_LOG_AUTHORITY_ERROR_0x00E8   (0x00E8 | TCG_SECURITY_LOG_CLASS_AUTHORITY_ERROR)

// Error(4) - NotSupport - function not enable / insufficient space
#define TCG_SECURITY_LOG_NOT_SUPPORT_ERROR_0x000D   (0x000D | TCG_SECURITY_LOG_CLASS_NOT_SUPPORT_ERROR)
#define TCG_SECURITY_LOG_NOT_SUPPORT_ERROR_0x0089   (0x0089 | TCG_SECURITY_LOG_CLASS_NOT_SUPPORT_ERROR)
#define TCG_SECURITY_LOG_NOT_SUPPORT_ERROR_0x008B   (0x008B | TCG_SECURITY_LOG_CLASS_NOT_SUPPORT_ERROR)

// Error(5) - Session - Session status error
#define TCG_SECURITY_LOG_SESSION_ERROR_0x000E   (0x000E | TCG_SECURITY_LOG_CLASS_SESSION_ERROR)
#define TCG_SECURITY_LOG_SESSION_ERROR_0x0019   (0x0019 | TCG_SECURITY_LOG_CLASS_SESSION_ERROR)
#define TCG_SECURITY_LOG_SESSION_ERROR_0x001B   (0x001B | TCG_SECURITY_LOG_CLASS_SESSION_ERROR)
#define TCG_SECURITY_LOG_SESSION_ERROR_0x001C   (0x001C | TCG_SECURITY_LOG_CLASS_SESSION_ERROR)
#define TCG_SECURITY_LOG_SESSION_ERROR_0x001F   (0x001F | TCG_SECURITY_LOG_CLASS_SESSION_ERROR)
#define TCG_SECURITY_LOG_SESSION_ERROR_0x0020   (0x0020 | TCG_SECURITY_LOG_CLASS_SESSION_ERROR)
#define TCG_SECURITY_LOG_SESSION_ERROR_0x0023   (0x0023 | TCG_SECURITY_LOG_CLASS_SESSION_ERROR)
#define TCG_SECURITY_LOG_SESSION_ERROR_0x0024   (0x0024 | TCG_SECURITY_LOG_CLASS_SESSION_ERROR)
#define TCG_SECURITY_LOG_SESSION_ERROR_0x0030   (0x0030 | TCG_SECURITY_LOG_CLASS_SESSION_ERROR)
#define TCG_SECURITY_LOG_SESSION_ERROR_0x0045   (0x0045 | TCG_SECURITY_LOG_CLASS_SESSION_ERROR)

// Error(6) - InvalidField - Payload field error
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x0007   (0x0007 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x0008   (0x0008 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x000A   (0x000A | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x000B   (0x000B | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x000C   (0x000C | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x000F   (0x000F | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x0011   (0x0011 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x0012   (0x0012 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x0013   (0x0013 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x0015   (0x0015 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x0016   (0x0016 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x0017   (0x0017 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x0018   (0x0018 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x001E   (0x001E | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x0022   (0x0022 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x0025   (0x0025 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x0026   (0x0026 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x0027   (0x0027 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x0029   (0x0029 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x002B   (0x002B | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x002E   (0x002E | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x002F   (0x002F | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x0033   (0x0033 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x0034   (0x0034 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x0038   (0x0038 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x0039   (0x0039 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x003A   (0x003A | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x003D   (0x003D | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x003E   (0x003E | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x003F   (0x003F | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x0041   (0x0041 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x0042   (0x0042 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x0046   (0x0046 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x0056   (0x0056 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x0057   (0x0057 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x0058   (0x0058 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x005A   (0x005A | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x005B   (0x005B | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x0061   (0x0061 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x0062   (0x0062 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x0064   (0x0064 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x006E   (0x006E | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x0078   (0x0078 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x007C   (0x007C | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x0083   (0x0083 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x008A   (0x008A | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x008D   (0x008D | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x008E   (0x008E | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x0090   (0x0090 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x0091   (0x0091 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x0094   (0x0094 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x0095   (0x0095 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x00A0   (0x00A0 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x00A4   (0x00A4 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x00A5   (0x00A5 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x00A7   (0x00A7 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x00A8   (0x00A8 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x00A9   (0x00A9 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x00AB   (0x00AB | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x00AD   (0x00AD | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x00AF   (0x00AF | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x00B1   (0x00B1 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x00B4   (0x00B4 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x00BB   (0x00BB | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x00BC   (0x00BC | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x00BF   (0x00BF | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x00C1   (0x00C1 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x00C5   (0x00C5 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x00C6   (0x00C6 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x00C7   (0x00C7 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x00C8   (0x00C8 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x00CE   (0x00CE | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x00CF   (0x00CF | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x00E6   (0x00E6 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)
#define TCG_SECURITY_LOG_INVALID_FIELD_ERROR_0x00E7   (0x00E7 | TCG_SECURITY_LOG_CLASS_INVALID_FIELD_ERROR)

// Error(7) - Syntax - Payload format error
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x0010   (0x0010 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x0014   (0x0014 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x001D   (0x001D | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x0021   (0x0021 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x002A   (0x002A | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x002C   (0x002C | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x002D   (0x002D | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x0032   (0x0032 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x0035   (0x0035 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x0036   (0x0036 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x0037   (0x0037 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x003B   (0x003B | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x003C   (0x003C | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x0043   (0x0043 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x0047   (0x0047 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x0048   (0x0048 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x0049   (0x0049 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x004A   (0x004A | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x004B   (0x004B | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x004C   (0x004C | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x0059   (0x0059 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x005C   (0x005C | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x005D   (0x005D | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x005E   (0x005E | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x005F   (0x005F | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x0063   (0x0063 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x0065   (0x0065 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x0066   (0x0066 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x0067   (0x0067 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x0068   (0x0068 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x006F   (0x006F | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x0070   (0x0070 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x0071   (0x0071 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x0072   (0x0072 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x0073   (0x0073 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x0074   (0x0074 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x0076   (0x0076 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x0077   (0x0077 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x007A   (0x007A | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x007D   (0x007D | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x007E   (0x007E | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x0080   (0x0080 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x0081   (0x0081 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x0084   (0x0084 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x0085   (0x0085 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x0086   (0x0086 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x0087   (0x0087 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x0088   (0x0088 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x008C   (0x008C | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x008F   (0x008F | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x0092   (0x0092 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x0096   (0x0096 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x0097   (0x0097 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x0098   (0x0098 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x0099   (0x0099 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x009A   (0x009A | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x009B   (0x009B | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x009C   (0x009C | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x009D   (0x009D | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x009E   (0x009E | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x00A1   (0x00A1 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x00A2   (0x00A2 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x00A3   (0x00A3 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x00A6   (0x00A6 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x00AA   (0x00AA | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x00AC   (0x00AC | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x00AE   (0x00AE | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x00B0   (0x00B0 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x00B2   (0x00B2 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x00B3   (0x00B3 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x00B5   (0x00B5 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x00B6   (0x00B6 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x00B7   (0x00B7 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x00B8   (0x00B8 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x00B9   (0x00B9 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x00BA   (0x00BA | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x00C0   (0x00C0 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x00C2   (0x00C2 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x00C3   (0x00C3 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x00C4   (0x00C4 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x00C9   (0x00C9 | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x00CA   (0x00CA | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x00CB   (0x00CB | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x00CC   (0x00CC | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)
#define TCG_SECURITY_LOG_SYNTAX_ERROR_0x00CD   (0x00CD | TCG_SECURITY_LOG_CLASS_SYNTAX_ERROR)

// Error(A) - Reported - Error by previous error condition
#define TCG_SECURITY_LOG_REPORTED_ERROR_0x001A   (0x001A | TCG_SECURITY_LOG_CLASS_REPORTED_ERROR)
#define TCG_SECURITY_LOG_REPORTED_ERROR_0x0028   (0x0028 | TCG_SECURITY_LOG_CLASS_REPORTED_ERROR)
#define TCG_SECURITY_LOG_REPORTED_ERROR_0x0031   (0x0031 | TCG_SECURITY_LOG_CLASS_REPORTED_ERROR)
#define TCG_SECURITY_LOG_REPORTED_ERROR_0x0060   (0x0060 | TCG_SECURITY_LOG_CLASS_REPORTED_ERROR)
#define TCG_SECURITY_LOG_REPORTED_ERROR_0x0075   (0x0075 | TCG_SECURITY_LOG_CLASS_REPORTED_ERROR)
#define TCG_SECURITY_LOG_REPORTED_ERROR_0x0079   (0x0079 | TCG_SECURITY_LOG_CLASS_REPORTED_ERROR)
#define TCG_SECURITY_LOG_REPORTED_ERROR_0x007B   (0x007B | TCG_SECURITY_LOG_CLASS_REPORTED_ERROR)
#define TCG_SECURITY_LOG_REPORTED_ERROR_0x007F   (0x007F | TCG_SECURITY_LOG_CLASS_REPORTED_ERROR)
#define TCG_SECURITY_LOG_REPORTED_ERROR_0x0082   (0x0082 | TCG_SECURITY_LOG_CLASS_REPORTED_ERROR)
#define TCG_SECURITY_LOG_REPORTED_ERROR_0x009F   (0x009F | TCG_SECURITY_LOG_CLASS_REPORTED_ERROR)
#define TCG_SECURITY_LOG_REPORTED_ERROR_0x00BD   (0x00BD | TCG_SECURITY_LOG_CLASS_REPORTED_ERROR)
#define TCG_SECURITY_LOG_REPORTED_ERROR_0x00BE   (0x00BE | TCG_SECURITY_LOG_CLASS_REPORTED_ERROR)

// Error(B) - Handled error - ErrorHandle functions
#define TCG_SECURITY_LOG_HANDLED_ERROR_0x0069   (0x0069 | TCG_SECURITY_LOG_CLASS_HANDLED_ERROR)
#define TCG_SECURITY_LOG_HANDLED_ERROR_0x006A   (0x006A | TCG_SECURITY_LOG_CLASS_HANDLED_ERROR)
#define TCG_SECURITY_LOG_HANDLED_ERROR_0x006B   (0x006B | TCG_SECURITY_LOG_CLASS_HANDLED_ERROR)
#define TCG_SECURITY_LOG_HANDLED_ERROR_0x006C   (0x006C | TCG_SECURITY_LOG_CLASS_HANDLED_ERROR)
#define TCG_SECURITY_LOG_HANDLED_ERROR_0x006D   (0x006D | TCG_SECURITY_LOG_CLASS_HANDLED_ERROR)

// Error(C) - Write protect error
#define TCG_SECURITY_LOG_WRITEPROTECT_ERROR_0x00D5   (0x00D5 | TCG_SECURITY_LOG_CLASS_WRITEPROTECT_ERROR)
#define TCG_SECURITY_LOG_WRITEPROTECT_ERROR_0x00D6   (0x00D6 | TCG_SECURITY_LOG_CLASS_WRITEPROTECT_ERROR)
#define TCG_SECURITY_LOG_WRITEPROTECT_ERROR_0x00D7   (0x00D7 | TCG_SECURITY_LOG_CLASS_WRITEPROTECT_ERROR)
#define TCG_SECURITY_LOG_WRITEPROTECT_ERROR_0x00D8   (0x00D8 | TCG_SECURITY_LOG_CLASS_WRITEPROTECT_ERROR)
#define TCG_SECURITY_LOG_WRITEPROTECT_ERROR_0x00D9   (0x00D9 | TCG_SECURITY_LOG_CLASS_WRITEPROTECT_ERROR)
#define TCG_SECURITY_LOG_WRITEPROTECT_ERROR_0x00DA   (0x00DA | TCG_SECURITY_LOG_CLASS_WRITEPROTECT_ERROR)
#define TCG_SECURITY_LOG_WRITEPROTECT_ERROR_0x00DB   (0x00DB | TCG_SECURITY_LOG_CLASS_WRITEPROTECT_ERROR)
#define TCG_SECURITY_LOG_WRITEPROTECT_ERROR_0x00DC   (0x00DC | TCG_SECURITY_LOG_CLASS_WRITEPROTECT_ERROR)
#define TCG_SECURITY_LOG_WRITEPROTECT_ERROR_0x00DE   (0x00DE | TCG_SECURITY_LOG_CLASS_WRITEPROTECT_ERROR)
#define TCG_SECURITY_LOG_WRITEPROTECT_ERROR_0x00DF   (0x00DF | TCG_SECURITY_LOG_CLASS_WRITEPROTECT_ERROR)
#define TCG_SECURITY_LOG_WRITEPROTECT_ERROR_0x00E0   (0x00E0 | TCG_SECURITY_LOG_CLASS_WRITEPROTECT_ERROR)
#define TCG_SECURITY_LOG_WRITEPROTECT_ERROR_0x00E1   (0x00E1 | TCG_SECURITY_LOG_CLASS_WRITEPROTECT_ERROR)
#define TCG_SECURITY_LOG_WRITEPROTECT_ERROR_0x00E2   (0x00E2 | TCG_SECURITY_LOG_CLASS_WRITEPROTECT_ERROR)
#define TCG_SECURITY_LOG_WRITEPROTECT_ERROR_0x00E4   (0x00E4 | TCG_SECURITY_LOG_CLASS_WRITEPROTECT_ERROR)
#define TCG_SECURITY_LOG_WRITEPROTECT_ERROR_0x00E5   (0x00E5 | TCG_SECURITY_LOG_CLASS_WRITEPROTECT_ERROR)

// Method(D)

// Info(E)

// Others(F)

/*
 * ---------------------------------------------------------------------------------------------------
 *  data types
 * ---------------------------------------------------------------------------------------------------
 */
typedef union {
	U32 ulAll;
	struct {
		U32 ubHeader	: 8;
		U32 uwLogCode	: 16;
		U32 ubPayload	: 8;
	};
} TcgSecurityLog_t;
TYPE_SIZE_CHECK(TcgSecurityLog_t, SIZE_4B);

/*
 * ---------------------------------------------------------------------------------------------------
 *   macros
 * ---------------------------------------------------------------------------------------------------
 */
#define M_TCG_METHOD_LOG(uwLogCode)	do{\
										TcgMethodUart(uwLogCode);\
										gTcg.uwLastMethodID = (uwLogCode);\
									} while (0)
#define M_TCG_INFO_LOG(uwLogCode)	do{\
										TcgInfoUart(uwLogCode);\
										gTcg.uwLastInfoID = (uwLogCode);\
									} while (0)
#define M_TCG_ERROR_LOG(uwLogCode)	do{\
										TcgErrorUart(uwLogCode);\
										if ((((uwLogCode) & TCG_SECURITY_LOG_CLASS_MASK) <= TCG_SECURITY_LOG_LEVEL)) {\
											TcgAddSecurityLog(uwLogCode, 0);\
											TcgAddErrorDrivelog((uwLogCode));\
										}\
									} while (0)
#define M_TCG_EVENT_LOG(uwLogCode)	do{\
										TcgEventUart(uwLogCode);\
										if ((((uwLogCode) & TCG_SECURITY_LOG_CLASS_MASK) <= TCG_SECURITY_LOG_LEVEL)) {\
											TcgAddSecurityLog(uwLogCode, 0);\
											TcgAddEventDrivelog((uwLogCode), TCG_DRIVELOG_NO_PAYLOAD);\
										}\
									} while (0)
#define M_TCG_EVENT_LOG_PAYLOAD(uwLogCode, ubPayload)	do{\
										TcgEventUart(uwLogCode);\
										if ((((uwLogCode) & TCG_SECURITY_LOG_CLASS_MASK) <= TCG_SECURITY_LOG_LEVEL)) {\
											TcgAddSecurityLog(uwLogCode, ubPayload);\
											TcgAddEventDrivelog((uwLogCode), (ubPayload));\
										}\
									} while (0)
/*
 * ---------------------------------------------------------------------------------------------------
 *	public prototypes
 * ---------------------------------------------------------------------------------------------------
 */
#if (NVME == HOST_MODE)
AOM_TCG_COMMON void TcgAddSecurityLog(U16 uwLogCode, U8 ubPayload);
#else /* (NVME == HOST_MODE) */
#define TcgAddSecurityLog(...)
#endif /* (NVME == HOST_MODE) */
#endif // TCG_SECURITY_LOG_H_
