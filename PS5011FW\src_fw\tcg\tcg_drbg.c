#define _TCG_DRBG_C_
#include "tcg_drbg.h"
#include "hal/sys/api/rng/rng_api.h"
#include "hal/security/security_api.h"
#include <string.h>
#include "hal/pic/uart/uart_api.h"
#if (TCG_EN && (PS5013_EN || PS5021_EN || (PS5017_EN && !BURNER_MODE_EN)) && !RDT_MODE_EN && !LPM3_LOADER && !BOOTLOADER_MODE_EN)

void TcgRngGenSeed(U8 *pubRng)
{
	U32 ulRandomNumber = RngGetRandomValue();
	memcpy(pubRng, &ulRandomNumber, TCG_RNG_NOISE_LEN);
}

void TcgRngSha(U8 *pubSrc, U8 *pubDst, U32 ulLen, TcgDRBGStructPtr_t DRBG)
{
	U32 ulBufSrc = (DRBG->ulDRBGBufferBase + TCG_DRBG_SEC_BUFFER_SOURCE_OFFSET);
	U32 ulBufTar = (DRBG->ulDRBGBufferBase + TCG_DRBG_SEC_BUFFER_TARGET_OFFSET);
	memcpy((void *)(ulBufSrc), (void *)pubSrc, ulLen);
	SecuritySHAExecute(ulBufSrc, ulLen, ulBufTar, SECURITY_SHA_FIRST_ROUND_BIT | SECURITY_SHA_FINAL_ROUND_BIT, SECURITY_MODE_SELECT_SHA_512, SECURITY_SHA_AXI_PAC32_DIS);
	memcpy((void *)pubDst, (void *)ulBufTar, 64);
}

void TcgRngEstimator(TcgDRBGStructPtr_t DRBG, U32 ulSrcLen, U8 *pubEntropyInput, U16 *puwOutLen)
{
	U32 uli, ulj;
	U16 uwBufBitLen;
	U16 uwSampleCnt;

	uwSampleCnt = ulSrcLen / (DRBG->ubEntropy);
	uwBufBitLen = uwSampleCnt << 3; // 8bit base
	uwBufBitLen = ((uwBufBitLen % 32) > 0) ? (uwBufBitLen + (32 - (uwBufBitLen % 32))) : uwBufBitLen; // 32 bits alignment

	*puwOutLen = (uwBufBitLen >> 3);
	memset((void *)DRBG->ubRngPoolBuf, 0x00, sizeof(DRBG->ubRngPoolBuf));
	memset((void *)DRBG->ubRngSeedBuf, 0x00, sizeof(DRBG->ubRngSeedBuf));

	for (uli = 0; uli < uwSampleCnt; uli++) {
		// gen random seed
		TcgRngGenSeed(DRBG->ubRngSeedBuf);

		//RNG Seed Buffer || RNG Pool Buffer
		for (ulj = 0; ulj < TCG_RNG_POOL_BUF_SIZE; ulj++) {
			(*(DRBG->ubRngSeedBuf + TCG_RNG_NOISE_LEN + ulj)) ^= (*(DRBG->ubRngPoolBuf + ulj));
		}

		TcgRngSha(DRBG->ubRngSeedBuf, DRBG->ubRngPoolBuf, TCG_RNG_SEED_BUF_SIZE, DRBG);
		memcpy((U8 *) & (pubEntropyInput + uli)[0], (void *)DRBG->ubRngPoolBuf, 4);
	}
}


void TcgRngEntropyMonitor(TcgDRBGStructPtr_t DRBG, U8 *pubIsEnableRng, U64 *puoEntropyCntBase8)
{
	U32 ulCnt = 0;
	//float pr = 0.0, fx = 0.0, flnx = 0.0;
	U64 pr = 0, fx = 0, flnx = 0;
	if (DRBG->ulTotalCnt < TCG_RNG_INIT_PERIOD) {
		return;
	}

	ulCnt = *(puoEntropyCntBase8 + DRBG->ulMaxRngVal);
	//pr = (float)ulCnt  / (float)DRBG->uoSampleCntBase8;//_gwTotalCnt;
	pr = ulCnt * TCG_FLOAT_AMPLIFIER / DRBG->uoSampleCntBase8;//_gwTotalCnt;
	fx = 1 * TCG_FLOAT_AMPLIFIER - pr;
	TcgRngLogBaseE(&flnx, fx);
	U64 uoTmpEntropy = TCG_RNG_LN_2_INV_AMPLIFIED * flnx / TCG_FLOAT_AMPLIFIER;
	if (uoTmpEntropy < TCG_RNG_MIN_ENTROPY * TCG_FLOAT_AMPLIFIER) {
		*pubIsEnableRng = FALSE;
	}
	DRBG->ubEntropy = uoTmpEntropy / TCG_FLOAT_AMPLIFIER;
}

void TcgRngInit(TcgDRBGStructPtr_t DRBG)
{
	do {
		U8 ubi, ubj;
		U64 *puoEntropyCnt = NULL;

		puoEntropyCnt = (U64 *)(DRBG->ulDRBGBufferBase + TCG_DRBG_RNG_BUFFER_OFFSET);
		DRBG->ubIsEnableRng = TRUE;

		memset(DRBG->ubRngPoolBuf, 0x00, TCG_RNG_POOL_BUF_SIZE);  // 32
		memset(DRBG->ubRngSeedBuf, 0x00, TCG_RNG_SEED_BUF_SIZE);  // 36
		memset(puoEntropyCnt, 0x00, TCG_DRBG_RNG_BUFFER_SIZE);

		DRBG->ubTestCnt          = TCG_DRBG_TEST_CNT_NON_KAT;
		DRBG->uoSampleCntBase8 = 0;
		DRBG->ulMaxRngVal       = 0;
		DRBG->ulTotalCnt         = 0;

		while (DRBG->ulTotalCnt < TCG_RNG_INIT_PERIOD) {
			//Generate RNG Seed
			TcgRngGenSeed(DRBG->ubRngSeedBuf);

			//RNG Seed Buffer || RNG Pool Buffer
			for (ubj = 0; ubj < TCG_RNG_POOL_BUF_SIZE; ubj++) {
				DRBG->ubRngSeedBuf[TCG_RNG_NOISE_LEN + ubj] ^= DRBG->ubRngPoolBuf[ubj];
			}

			//Churn RNG Seed Buffer, and output into RNG Pool Buffer
			TcgRngSha(DRBG->ubRngSeedBuf, DRBG->ubRngPoolBuf, TCG_RNG_SEED_BUF_SIZE, DRBG);

			for (ubi = 0; ubi < TCG_RNG_NOISE_LEN; ubi++) {
				//sample counter + 1;
				*(puoEntropyCnt + DRBG->ubRngPoolBuf[ubi]) += 1;

				//check maximum sample count
				if (*(puoEntropyCnt + DRBG->ubRngPoolBuf[ubi]) > *(puoEntropyCnt + DRBG->ulMaxRngVal)) {
					DRBG->ulMaxRngVal = DRBG->ubRngPoolBuf[ubi];
				}

				DRBG->uoSampleCntBase8++;
			}

			DRBG->ulTotalCnt++;
		}
		TcgRngEntropyMonitor(DRBG, &DRBG->ubIsEnableRng, puoEntropyCnt);
	} while (FALSE == DRBG->ubIsEnableRng);
	TcgRngDRBGInstantiate(DRBG, DRBGBaseHashMAC, DRBGSs256, TRUE, NULL, 0);
}

void TcgRngDRBGInstantiate(TcgDRBGStructPtr_t DRBG, DRBGGenerateBase Ebase, DRBGSecurityStrength ReqSs, U8 ubIsEnablePr, U8 *pubPString, U16 uwPStringLen)
{
	TcgDRBGInfoPtr_t Info = &DRBG->Info;
	TcgDRBGSeedMaterialPtr_t DRBGSeedMaterial = &DRBG->DRBGSeedMaterial;


	U16 uwSsTable[5] = {0, 112, 128, 192, 256};

	U16 uwLen;

	TcgRngDRBGInit(DRBG, Ebase);

	// requested_instantiation_security_strength > highest_supported_security_strength
	if (ReqSs > Info->uwMaxSecurityStrength) {
		return;
	}

	// check prediction_resistance_flag, if prediction resistance is support
	if (ubIsEnablePr == TRUE && Info->ubIsSupportPr == FALSE) {
		return;
	}

	// personalization_string > max_personalization_string_length
	if (uwPStringLen > Info->uwMaxPersonalStringLen) {
		return;
	}

	// set security_strength to the nearest security strength
	if (ReqSs >= Info->uwMaxSecurityStrength) {
		Info->uwSecurityStrength = Info->uwMaxSecurityStrength;
	}
	else {
		Info->uwSecurityStrength = Info->uwMaxSecurityStrength - 1;//set next security level
	}

	// Set min entropy length
	Info->uwMinEntropyLen = *(uwSsTable + Info->uwSecurityStrength);

	// Set personalization string
	DRBGSeedMaterial->pubPersonalString = pubPString;
	DRBGSeedMaterial->uwPersonalStringLen = uwPStringLen;

	// Get entropy input
	memset(DRBG->ubEntropyInput, 0x00, sizeof(DRBG->ubEntropyInput));

	TcgRngGetEntropyInput(DRBG, &uwLen);   // (U8*)&gRngGlobalVar.ubEntropyInput[0], &uwLen
	DRBGSeedMaterial->pubEntropyInput = DRBG->ubEntropyInput;
	DRBGSeedMaterial->uwEntropyInputLen = uwLen;


	// Get nonce
	memset(DRBG->ubNonce, 0x00, sizeof(DRBG->ubNonce));

	TcgRngGetNonce(DRBG, &uwLen);
	DRBGSeedMaterial->pubNonce = DRBG->ubNonce;
	DRBGSeedMaterial->uwNonceInputLen = uwLen;

	// Instantiating DRBG Mechanism
	TcgRngInstantiate(DRBG);
}


void TcgRngDRBGInit(TcgDRBGStructPtr_t DRBG, DRBGGenerateBase Ebase)
{
	TcgDRBGInfoPtr_t Info = &DRBG->Info;
	TcgDRBGStatePtr_t State = &DRBG->State;

	Info->Ebase = Ebase;
	Info->uwMaxSecurityStrength   = DRBGSs256;  //ref.SP 800-57
	Info->uwOutBlockLen = 256;
	Info->uwKeyLen = 256;
	Info->uwMaxEntropyLen = 1000;         //ref.SP 800-90, <= 2^35
	Info->uwMaxPersonalStringLen = 800;          //ref.SP 800-90, <= 2^35
	Info->uwMaxAdditionalLen = 800;          //ref.SP 800-90, <= 2^35
	Info->uwMaxRequestBitLen = 7500;         //ref.SP 800-90, <= 2^19
	Info->uwSeedLen = 440;          //ref.SP 800-90, tabel 2, p34
	Info->ulReseedInterval = 10000;        //ref.SP 800-90, <= 2^48
	Info->ubIsSupportPr = TRUE;
	Info->ubCtrCnt = 0;

	// Reset Internal State
	memset(State->ubKey,   0x00, M_TCG_BIT_TO_BYTE(Info->uwOutBlockLen));
	memset(State->ubValue, 0x00, M_TCG_BIT_TO_BYTE(Info->uwOutBlockLen));

	State->ubErrorState = 0;
	State->ulReseedCnt = 0;
}



void TcgRngGetNonce(TcgDRBGStructPtr_t DRBG, U16 *puwOutputLen)
{
	TcgDRBGKatStructPtr_t Kat = &DRBG->Kat;

	U8 *pubTestCnt = &DRBG->ubTestCnt;
	U8 *pubDst = DRBG->ubNonce;

	if (*pubTestCnt == 0xFF) {
		memset(pubDst, 0x00, TCG_DRBG_NONCE_LEN);
		TcgRngEstimator(DRBG, TCG_DRBG_ENTROPY_LEN, pubDst, (U16 *)puwOutputLen);//req_ss);
	}
	else {
		memcpy(pubDst, Kat->pubKatNonce, 16);
		*puwOutputLen = 16 << 3;
	}
}

void TcgRngLogBaseE(U64 *puoRes, U64 uoN)
{
	U16 uwi = 0;
	U64 uoResX1 = 1 * TCG_FLOAT_AMPLIFIER;
	for (uwi = 0; uwi <= 256; uwi++) {
		//U64 uoResX1 = 1;
		//for (uwj = 1; uwj < (uwi + 1); uwj++) {
		if (uwi != 0) {
			uoResX1 = uoResX1 * uoN / TCG_FLOAT_AMPLIFIER;
		}
		*puoRes = *puoRes + uoResX1 / (uwi + 1);
	}
}

void TcgRngGenerate(TcgDRBGStructPtr_t DRBG, U8 *pubDst, U64 puoReqBits, DRBGSecurityStrength ReqSs, U8 ubIsEnablePr, U8 *pubAdditionalInput, U16 uwAdditionalInputLen)
{
	TcgDRBGInfoPtr_t Info = &DRBG->Info;
	TcgDRBGStatePtr_t State = &DRBG->State;
	TcgDRBGSeedMaterialPtr_t DRBGSeedMaterial = &DRBG->DRBGSeedMaterial;


	// requested_number_of_bits > max_number_of_bits_per_request
	if (puoReqBits > Info->uwMaxRequestBitLen) {
		return;
	}

	// requested_security_strength > security_strength
	if (ReqSs > Info->uwSecurityStrength) {
		return;
	}

	// additional_input > max_additional_input_length
	if (uwAdditionalInputLen > Info->uwMaxAdditionalLen) {
		return;
	}

	// prediction_resistance
	if (ubIsEnablePr == TRUE && Info->ubIsSupportPr == FALSE) {
		return;
	}

	DRBGSeedMaterial->pubAdditionalInput = pubAdditionalInput;
	DRBGSeedMaterial->uwAdditionalInputLen = uwAdditionalInputLen;

	if (ubIsEnablePr || State->ulReseedCnt > Info->ulReseedInterval) {
		TcgRngDRBGReseed(DRBG, pubAdditionalInput, uwAdditionalInputLen);

		// NU64 Additional Input, If reseed
		memset(DRBGSeedMaterial->pubAdditionalInput, 0x00, M_TCG_BIT_TO_BYTE(DRBGSeedMaterial->uwAdditionalInputLen));
		DRBGSeedMaterial->uwAdditionalInputLen = 0;
	}
	TcgRngDRBGGenerate(DRBG, puoReqBits, pubDst, DRBGSeedMaterial);
}

void TcgRngGetEntropyInput(TcgDRBGStructPtr_t DRBG, U16 *puwOutputLen)
{
	TcgDRBGKatStructPtr_t Kat = &DRBG->Kat;

	U8 *pubEntropyInput = DRBG->ubEntropyInput;
	U8 *pubTestCnt = &DRBG->ubTestCnt;

	switch (*pubTestCnt) {
	case TCG_DRBG_TEST_CNT_NON_KAT:
		TcgRngEstimator(DRBG, TCG_DRBG_ENTROPY_LEN, pubEntropyInput, (U16 *)puwOutputLen);
		break;

	case TCG_DRBG_TEST_CNT_KAT1:
		memcpy((void *)pubEntropyInput, (void *)Kat->pubKatEntropyInput, 32);
		break;

	case TCG_DRBG_TEST_CNT_KAT2:
		memcpy((void *)pubEntropyInput, (void *)Kat->pubKatEntropyPr1, 32);
		break;

	case TCG_DRBG_TEST_CNT_KAT3:
		memcpy((void *)pubEntropyInput, (void *)Kat->pubKatEntropyPr2, 32);
		break;
	default:
		break;
	}

	*puwOutputLen = DRBG->Info.uwOutBlockLen;  // 256 bits, aslo = 32 bytes
}


void TcgRngDRBGReseed(TcgDRBGStructPtr_t DRBG, U8 *pubAdditionalInput, U16 uwAdditionalInputLen)
{
	TcgDRBGInfoPtr_t Info = &DRBG->Info;
	TcgDRBGSeedMaterialPtr_t DRBGSeedMaterial = &DRBG->DRBGSeedMaterial;


	U16 uwLen;

	// additional_input > max_additional_input_length
	if (uwAdditionalInputLen > Info->uwMaxAdditionalLen) {
		return;
	}

	// Set Additional Input
	DRBGSeedMaterial->pubAdditionalInput = pubAdditionalInput;
	DRBGSeedMaterial->uwAdditionalInputLen = uwAdditionalInputLen;

	// Set Entropy Input
	TcgRngGetEntropyInput(DRBG, &uwLen);

	DRBGSeedMaterial->pubEntropyInput = DRBG->ubEntropyInput;
	DRBGSeedMaterial->uwEntropyInputLen = uwLen;

	TcgRngReseed(DRBG, DRBGSeedMaterial);
}

void TcgRngHmac(U8 ubShaType, U8 *pubKey, U32 ulKeyLength, U8 *ubInput, U32 ulInputLength, U8 *pubOutput, TcgDRBGStructPtr_t DRBG)
{
	memset((void *)SECURITY_PKE_MEMORY_R_ADDR, 0x00, SIZE_256B);
	U32 ulBufSrc = (DRBG->ulDRBGBufferBase + TCG_DRBG_SEC_BUFFER_SOURCE_OFFSET);
	U32 ulBufTar = (DRBG->ulDRBGBufferBase + TCG_DRBG_SEC_BUFFER_TARGET_OFFSET);
	memcpy((void *)(ulBufSrc), (void *) ubInput, ulInputLength);
	SecurityHMACExecuteFirstStep(ubShaType, (volatile U8 *)pubKey, ulKeyLength, ulBufSrc, ulBufTar);
	SecurityHMACExecuteSecondStep(ubShaType, (volatile U8 *)(ulBufSrc), ulInputLength,  ulBufTar, ENABLE);
	SecurityHMACExecuteThirdStep(ubShaType, ulKeyLength, (volatile U8 *) ulBufTar);
	memcpy((void *)pubOutput, (void *) ulBufTar, 32);
}

void TcgRngUpdateState(TcgDRBGStructPtr_t DRBG, U8 *pubProvidedData, U16 uwInputLen)
{
	TcgDRBGInfoPtr_t Info = &DRBG->Info;
	TcgDRBGStatePtr_t State = &DRBG->State;

	U16 uwLen, uwILen, uwOutBlockLen;

	memset(DRBG->ubMacInput, 0x00, sizeof(DRBG->ubMacInput));
	uwILen = M_TCG_BIT_TO_BYTE(uwInputLen);
	uwOutBlockLen = M_TCG_BIT_TO_BYTE(Info->uwOutBlockLen);
	if (NULL == (U32)pubProvidedData) {
		uwILen = 0;
	}

	//V || 0X00 || provided data
	memcpy(DRBG->ubMacInput, State->ubValue, uwOutBlockLen);
	*(DRBG->ubMacInput + uwOutBlockLen) = 0x00;
	if (0 != uwILen) {
		memcpy((U8 *) & (DRBG->ubMacInput)[uwOutBlockLen + 1], pubProvidedData, uwILen);
	}
	uwLen = uwOutBlockLen + 1 + uwILen;

	//K = HMAC(K, V|| 0X00 || provided data)
	TcgRngHmac(SECURITY_MODE_SELECT_SHA_256, State->ubKey, uwOutBlockLen, DRBG->ubMacInput, uwLen, State->ubKey, DRBG);

	//V = HMAC(K, V)
	memset(DRBG->ubMacInput, 0x00, 256);
	memcpy(DRBG->ubMacInput, State->ubValue, uwOutBlockLen);

	TcgRngHmac(SECURITY_MODE_SELECT_SHA_256, State->ubKey, uwOutBlockLen, DRBG->ubMacInput, uwOutBlockLen, State->ubValue, DRBG);

	if (0 == uwILen) {
		return;
	}
	//-------------------------------------------------------------------------
	//V || 0X01 || provided data
	memset(DRBG->ubMacInput, 0x00, 256);
	memcpy(DRBG->ubMacInput, State->ubValue, uwOutBlockLen);
	*(DRBG->ubMacInput + uwOutBlockLen) = 0x01;
	memcpy((U8 *) & (DRBG->ubMacInput + uwOutBlockLen + 1)[0], (U8 *)&pubProvidedData[0], uwLen);
	uwLen = uwOutBlockLen + 1 + uwILen;

	//K = HMAC(K, V|| 0X01 || provided data)
	TcgRngHmac(SECURITY_MODE_SELECT_SHA_256, State->ubKey, uwOutBlockLen, DRBG->ubMacInput, uwLen, State->ubKey, DRBG);


	//V = HMAC(K, V)
	memset(DRBG->ubMacInput, 0x00, 256);
	memcpy(DRBG->ubMacInput, State->ubValue, uwOutBlockLen);
	TcgRngHmac(SECURITY_MODE_SELECT_SHA_256, State->ubKey, uwOutBlockLen, DRBG->ubMacInput, uwOutBlockLen, State->ubValue, DRBG);
}

void TcgRngInstantiate(TcgDRBGStructPtr_t DRBG)
{
	TcgDRBGSeedMaterialPtr_t DRBGSeedMaterial = &DRBG->DRBGSeedMaterial;
	TcgDRBGInfoPtr_t Info = &DRBG->Info;
	TcgDRBGStatePtr_t State = &DRBG->State;

	U16 uwEntropyLen, uwNonceLen, uwPSLen, uwSeedMaterialLen;

	U8 *pubSeedMaterial = DRBG->ubSeedMaterial;

	uwEntropyLen = M_TCG_BIT_TO_BYTE(DRBGSeedMaterial->uwEntropyInputLen);
	uwNonceLen = M_TCG_BIT_TO_BYTE(DRBGSeedMaterial->uwNonceInputLen);
	uwPSLen = M_TCG_BIT_TO_BYTE(DRBGSeedMaterial->uwPersonalStringLen);

	memset(DRBG->ubSeedMaterial, 0x00, sizeof(DRBG->ubSeedMaterial));//256);

	// Seed_Material = Entropy Input || Nonce || Personaliaztion String
	memcpy(pubSeedMaterial, DRBGSeedMaterial->pubEntropyInput, uwEntropyLen);  // 32 bytes
	memcpy((void *)&pubSeedMaterial[uwEntropyLen], DRBGSeedMaterial->pubNonce, uwNonceLen);  // 16 bytes
	memcpy((void *)&pubSeedMaterial[uwEntropyLen + uwNonceLen], DRBGSeedMaterial->pubPersonalString, uwPSLen);  // 0 bytes
	uwSeedMaterialLen = uwEntropyLen + uwNonceLen + uwPSLen;

	//Key = 0x00 00...00
	memset(State->ubKey, 0x00, M_TCG_BIT_TO_BYTE(Info->uwOutBlockLen));

	//V = 0x01 01...01
	memset(State->ubValue, 0x01, M_TCG_BIT_TO_BYTE(Info->uwOutBlockLen));

	//TcgRngUpdateState(gSeedMaterial, M_TCG_BYTE_TO_BIT(seed_material_len));
	TcgRngUpdateState(DRBG, pubSeedMaterial, M_TCG_BYTE_TO_BIT(uwSeedMaterialLen));

	State->ulReseedCnt = 1;
}

void TcgRngDRBGGenerate(TcgDRBGStructPtr_t DRBG, U32 ulReqBits, U8 *pubDst, TcgDRBGSeedMaterialPtr_t DRBGSeedMaterial)
{
	TcgDRBGInfoPtr_t Info = &DRBG->Info;
	TcgDRBGStatePtr_t State = &DRBG->State;

	U16 uwCnt, uwOutBlockLen;
	U8 *pubTmpTtr = (U8 *)(DRBG->ulDRBGBufferBase + TCG_DRBG_RNG_BUFFER_OFFSET);

	uwOutBlockLen = M_TCG_BIT_TO_BYTE(Info->uwOutBlockLen);

	// reseed_counter > ulReseedInterval, needs to return back to reseed
	if (State->ulReseedCnt > Info->ulReseedInterval) {
		return;
	}

	// check additional input
	if (DRBGSeedMaterial->uwAdditionalInputLen > 0) {
		TcgRngUpdateState(DRBG, DRBGSeedMaterial->pubAdditionalInput, DRBGSeedMaterial->uwAdditionalInputLen);
	}

	uwCnt = 0;

	//  uart_send("\n gDRBGState.value= ", 0, 0, 0);
	//  for (i=0; i<out_block_len; i++) {
	//      uart_send("", (gDRBGState.value[i]), 16, 1);
	//  }

	while (uwCnt < M_TCG_BIT_TO_BYTE(ulReqBits)) { // seedLen, 48byte/384bit for AES 256 CTR
		//CTR Block Cipher Encryption
		TcgRngBlockEncrypt(DRBG, State->ubValue, uwOutBlockLen);
		// temp = temp || V
		memcpy((void *)(pubTmpTtr + uwCnt), (void *)(State->ubValue), uwOutBlockLen);
		uwCnt += uwOutBlockLen;
	}// while

	//reseed counter + 1
	State->ulReseedCnt += 1;

	memcpy((U8 *)&pubDst[0], (U8 *)&pubTmpTtr[0], M_TCG_BIT_TO_BYTE(ulReqBits));
	TcgRngUpdateState(DRBG, DRBGSeedMaterial->pubAdditionalInput, DRBGSeedMaterial->uwAdditionalInputLen);
}


void TcgRngReseed(TcgDRBGStructPtr_t DRBG, TcgDRBGSeedMaterialPtr_t DRBGSeedMaterial)
{
	TcgDRBGStatePtr_t State = &DRBG->State;

	U16 uwSeedMaterialLen;
	U8 *pubSeedMaterial = DRBG->ubSeedMaterial;

	memset(pubSeedMaterial, 0x00, 256);

	//Seed Material = Entropy Input || Additional Input
	memcpy(pubSeedMaterial, DRBGSeedMaterial->pubEntropyInput, M_TCG_BIT_TO_BYTE(DRBGSeedMaterial->uwEntropyInputLen));
	memcpy((void *)&pubSeedMaterial[M_TCG_BIT_TO_BYTE(DRBGSeedMaterial->uwEntropyInputLen)], DRBGSeedMaterial->pubAdditionalInput, M_TCG_BIT_TO_BYTE(DRBGSeedMaterial->uwAdditionalInputLen));
	uwSeedMaterialLen = DRBGSeedMaterial->uwEntropyInputLen + DRBGSeedMaterial->uwAdditionalInputLen;

	TcgRngUpdateState(DRBG, pubSeedMaterial, uwSeedMaterialLen);

	State->ulReseedCnt = 1;
}


void TcgRngBlockEncrypt(TcgDRBGStructPtr_t DRBG, U8 *pubOutputBlock, U8 ubInputLen)
{
	TcgDRBGInfoPtr_t Info = &DRBG->Info;
	TcgDRBGStatePtr_t State = &DRBG->State;


	if (Info->Ebase == DRBGBaseAES256) {
	}
	else if (Info->Ebase == DRBGBaseHashMAC) {
		memset(DRBG->ubMacInput, 0x00, 96);
		memcpy(DRBG->ubMacInput, State->ubValue, ubInputLen);
		TcgRngHmac(SECURITY_MODE_SELECT_SHA_256, State->ubKey, M_TCG_BIT_TO_BYTE(Info->uwOutBlockLen), DRBG->ubMacInput, ubInputLen, pubOutputBlock, DRBG);
	}
}

void TcgRngGenerator(TcgDRBGStructPtr_t DRBG, U8 *pubDst, U16 uwLen)
{
	if (uwLen > 256) {
		while (1);
	}
	TcgRngGenerate(DRBG, &DRBG->ubEm[0], (U64)(256 << 3), DRBGSs256, 1, NULL, 0);
	memcpy(pubDst, DRBG->ubEm, uwLen);
}
#endif /* (TCG_EN && (PS5013_EN || PS5021_EN || (PS5017_EN && !BURNER_MODE_EN)) && !RDT_MODE_EN && !LPM3_LOADER && !BOOTLOADER_MODE_EN) */

#if (0)
//==============================================================================
//  Globle variable
//==============================================================================


//==============================================================================
// Define Function
//==============================================================================



/***********************************************************************************************
  HMAC SHA_256, DF:None, PR:Yes, PersonalizationString:No, AdditionalInput:No
************************************************************************************************/
U8  ubPat1Ans[32]  = {
	0xCC, 0xF3, 0x51, 0x05, 0x05, 0x60, 0x02, 0x02,
	0x1F, 0x66, 0x35, 0xC7, 0x3B, 0x61, 0xFC, 0x2C,
	0xF3, 0x6D, 0x69, 0x78, 0xCB, 0xC3, 0x01, 0x99,
	0xF7, 0xCC, 0x48, 0xD8, 0xF8, 0xCD, 0xFD, 0x56
};

U8  ubPat1EntropyInput[32]  = {
	0x20, 0x00, 0x2f, 0xf9, 0x20, 0x00, 0xb6, 0x5e,
	0x55, 0x03, 0x19, 0x4e, 0x15, 0x29, 0xc7, 0x5d,
	0x29, 0x67, 0xe9, 0x38, 0xa9, 0x9e, 0x5a, 0x51,
	0x12, 0xdc, 0xe5, 0xca, 0xab, 0xef, 0xc0, 0xa1
};

U8  ubPat1EntropyPr1[32]  = {
	0xad, 0xe2, 0xb3, 0xeb, 0x7f, 0x09, 0x5e, 0x18,
	0x46, 0x95, 0x39, 0x53, 0x39, 0x57, 0xcf, 0xd3,
	0x93, 0x78, 0x36, 0x9f, 0xf8, 0xfa, 0x94, 0xbd,
	0x0b, 0x8d, 0xd3, 0x98, 0x4f, 0x96, 0xa2, 0xb0
};

U8  ubPat1EntropyPr2[32]  = {
	0x1a, 0x83, 0x23, 0x84, 0x82, 0x5e, 0x29, 0x44,
	0x16, 0x17, 0x24, 0xf2, 0xb8, 0xca, 0x06, 0xcf,
	0x21, 0x9d, 0x11, 0x54, 0xa3, 0x30, 0xb6, 0x40,
	0x25, 0x46, 0xae, 0xe7, 0x5b, 0xb4, 0x58, 0xb8
};

U8  ubPat1Nonce[16]  = {
	0xf3, 0x07, 0x48, 0x2b, 0xa4, 0xad, 0x4d, 0x47,
	0xde, 0x98, 0x14, 0x68, 0x39, 0xfb, 0x88, 0xcf
};


/***********************************************************************************************
  HMAC SHA_256, DF:None, PR:Yes, PersonalizationString:No, AdditionalInput:Yes
************************************************************************************************/
U8  ubPat2AdditionalInput1[32]  = {
	0xe6, 0xcd, 0x94, 0x06, 0x10, 0x37, 0x5e, 0x50,
	0x4f, 0xa8, 0x04, 0x06, 0x12, 0x0b, 0x34, 0xd4,
	0x98, 0xb0, 0x22, 0x39, 0x34, 0x36, 0xe9, 0x10,
	0xc0, 0xba, 0x25, 0x60, 0x60, 0x3f, 0xd0, 0x66
};

U8  ubPat2AdditionalInput2[32]  = {
	0xd2, 0x00, 0x82, 0xc5, 0xbd, 0xf6, 0xf6, 0x71,
	0x1a, 0xf3, 0x91, 0xe7, 0xd0, 0x10, 0x46, 0xb9,
	0xd3, 0x61, 0x08, 0x27, 0xde, 0x63, 0xaa, 0x26,
	0x71, 0xa5, 0xf5, 0xad, 0x07, 0xb9, 0x08, 0x41
};

U8  ubPat2Ans[32]  = {
	0xD3, 0xC3, 0x6E, 0x4A, 0xE2, 0x5F, 0xF2, 0x1A,
	0x95, 0xA1, 0x57, 0xA8, 0x9F, 0x13, 0xEB, 0x97,
	0x63, 0x62, 0xA6, 0x95, 0xEA, 0x75, 0x5F, 0x04,
	0x65, 0xED, 0x4A, 0x7B, 0xB2, 0x0C, 0x5C, 0xB3
};


U8  ubPat2EntropyInput[32]  = {
	0x6c, 0x1f, 0x4b, 0xff, 0xc4, 0x76, 0xe4, 0x88,
	0xfb, 0x57, 0xeb, 0x80, 0xdc, 0x10, 0x6c, 0xf2,
	0xb4, 0x17, 0xba, 0xd2, 0x2b, 0x19, 0x6b, 0xaa,
	0x63, 0x46, 0x95, 0x82, 0x56, 0xdb, 0x49, 0x0f
};

U8  ubPat2EntropyPr1[32]  = {
	0xab, 0xac, 0xa6, 0x56, 0x95, 0xbd, 0x5d, 0x28,
	0x98, 0x80, 0x45, 0x38, 0x50, 0xfc, 0x82, 0x89,
	0xb7, 0x6f, 0x78, 0xb4, 0x3f, 0x97, 0x0e, 0xd3,
	0x2f, 0x41, 0x25, 0xa9, 0x41, 0x16, 0x55, 0x15
};

U8  ubPat2EntropyPr2[32]  = {
	0x4a, 0x39, 0xb6, 0x66, 0xcf, 0x86, 0x18, 0x16,
	0xd7, 0xd8, 0x2e, 0xf6, 0xe2, 0x3f, 0x70, 0xf1,
	0x49, 0xd7, 0x4d, 0x9b, 0xd4, 0x99, 0xee, 0xa1,
	0x9b, 0x62, 0x2e, 0x75, 0x1c, 0x43, 0xd8, 0x39
};

U8  ubPat2Nonce[16]  = {
	0x5f, 0x1b, 0x92, 0x22, 0x3e, 0x39, 0x09, 0xe4,
	0x36, 0x77, 0xda, 0x2f, 0x58, 0x8a, 0x6d, 0x19
};


/***********************************************************************************************
  HMAC SHA_256, DF:None, PR:Yes, PersonalizationString:Yes, AdditionalInput:No
************************************************************************************************/
U8  ubPat3PString[32]  = {
	0x73, 0x77, 0xdd, 0x29, 0x66, 0x61, 0x91, 0x73,
	0xe0, 0x39, 0xec, 0xdf, 0xf9, 0x87, 0x92, 0x44,
	0x0e, 0x12, 0x10, 0xda, 0x5c, 0xa9, 0x9b, 0x9e,
	0xdf, 0x35, 0x0c, 0x03, 0x6c, 0xfe, 0x1a, 0xdc
};

U8  ubPat3Ans[32]  = {
	0xBC, 0xF3, 0xFB, 0xC4, 0x0A, 0xED, 0x49, 0xE1,
	0x65, 0xF3, 0xB7, 0xFA, 0xE9, 0x1C, 0x9F, 0x01,
	0x6F, 0x05, 0x0C, 0x2D, 0x68, 0x82, 0xD3, 0x29,
	0x9A, 0x3C, 0xF2, 0x20, 0x2C, 0x49, 0x13, 0xB8
};

U8  ubPat3EntropyInput[32]  = {
	0xc2, 0x87, 0xd1, 0xfd, 0x8b, 0xe2, 0xcc, 0x61,
	0x97, 0xc8, 0x2b, 0x02, 0x54, 0xf9, 0x27, 0xdb,
	0x67, 0x77, 0x3b, 0x2a, 0x0c, 0x16, 0x9c, 0x98,
	0xb1, 0x3f, 0xf5, 0xa7, 0xcf, 0x06, 0x66, 0x5a
};

U8  ubPat3EntropyPr1[32]  = {
	0xc7, 0xdd, 0x4d, 0x65, 0xee, 0x75, 0x15, 0x8f,
	0x43, 0x86, 0xf9, 0xb5, 0x7b, 0x8e, 0xca, 0xcc,
	0x69, 0xad, 0xcd, 0xf9, 0x7d, 0xcc, 0x59, 0x58,
	0x7c, 0x11, 0xb3, 0x0d, 0x81, 0x81, 0x27, 0x8f
};

U8  ubPat3EntropyPr2[32]  = {
	0x56, 0xf9, 0x6d, 0x2c, 0x1b, 0xe8, 0xea, 0xd4,
	0xeb, 0x43, 0x8f, 0xc6, 0xf7, 0x8b, 0x57, 0x16,
	0x33, 0xfe, 0xa7, 0xb0, 0xf5, 0x64, 0x0d, 0xcb,
	0xd1, 0x1d, 0x3a, 0x92, 0xb2, 0x6a, 0x3c, 0x0f
};

U8  ubPat3Nonce[16]  = {
	0x4d, 0x25, 0x4e, 0x47, 0x09, 0x18, 0x73, 0x0c,
	0x5e, 0x51, 0xcb, 0x8b, 0x31, 0x3d, 0x81, 0x9b
};


/***********************************************************************************************
  HMAC SHA_256, DF:None, PR:Yes, PersonalizationString:Yes, AdditionalInput:Yes
************************************************************************************************/
U8  ubPat4PString[32]  = {
	0xc8, 0x79, 0xe5, 0xb6, 0x19, 0xc1, 0xc1, 0x4d,
	0x78, 0xf8, 0xfc, 0xdf, 0x26, 0xc3, 0x5b, 0x10,
	0x0f, 0x37, 0x25, 0x04, 0x50, 0x06, 0xc6, 0x25,
	0xe7, 0xeb, 0xa3, 0x38, 0xbb, 0x55, 0x3e, 0xa2
};

U8  ubPat4AdditionalInput1[32]  = {
	0x37, 0xf0, 0x25, 0xe1, 0x03, 0x4b, 0xae, 0x74,
	0xfc, 0xff, 0xbe, 0x6b, 0xc1, 0x93, 0x23, 0x66,
	0x95, 0x54, 0x67, 0x1c, 0x63, 0x05, 0xef, 0xa6,
	0xf0, 0xc9, 0x56, 0x0c, 0x61, 0x4f, 0x05, 0x0f
};

U8  ubPat4AdditionalInput2[32]  = {
	0xd4, 0xf8, 0x6f, 0x3a, 0xae, 0x93, 0x93, 0x42,
	0x0f, 0x11, 0xd7, 0xe3, 0x07, 0xfe, 0x33, 0x84,
	0xc3, 0xd9, 0xa2, 0xe2, 0xaa, 0x66, 0x5b, 0xfa,
	0xb7, 0x37, 0x56, 0x35, 0x17, 0x78, 0xfd, 0xd4
};

U8  ubPat4Ans[32]  = {
	0x8A, 0x66, 0x8A, 0xE2, 0x0D, 0xE7, 0xCC, 0xAE,
	0xEC, 0x9B, 0x22, 0x8E, 0xD1, 0x0C, 0xA4, 0x13,
	0xCD, 0xD9, 0x4B, 0xB9, 0x72, 0xF2, 0xC2, 0x12,
	0x88, 0x52, 0x5D, 0x90, 0x67, 0x69, 0x38, 0x22
};

U8  ubPat4EntropyInput[32]  = {
	0xf8, 0x48, 0xab, 0xfe, 0x9a, 0xf2, 0x3d, 0x9f,
	0x17, 0xda, 0xfe, 0xe1, 0x41, 0xcd, 0xfc, 0xa4,
	0x46, 0x74, 0xf7, 0x38, 0x32, 0x86, 0x02, 0x28,
	0xa6, 0x33, 0x5e, 0x42, 0xd9, 0xce, 0x95, 0xe9
};

U8  ubPat4EntropyPr1[32]  = {
	0x91, 0x62, 0x41, 0xa5, 0x9a, 0x5f, 0x90, 0x4d,
	0x53, 0x75, 0x11, 0x0a, 0x86, 0x1a, 0x96, 0x26,
	0x97, 0x3e, 0x20, 0x31, 0x79, 0xf4, 0xf8, 0x4d,
	0xf2, 0x60, 0x0d, 0xba, 0x12, 0xe2, 0x8f, 0xa3
};

U8  ubPat4EntropyPr2[32]  = {
	0xdd, 0x21, 0x60, 0x30, 0xfd, 0x6d, 0xea, 0x1a,
	0x2e, 0x0f, 0xe3, 0xcd, 0xb1, 0x2b, 0x10, 0x16,
	0x3b, 0x15, 0xd0, 0x92, 0x93, 0x1b, 0xd7, 0x14,
	0x6e, 0x90, 0x31, 0x52, 0x88, 0x1b, 0x75, 0xd5
};

U8  ubPat4Nonce[16]  = {
	0x7a, 0x26, 0x5a, 0xed, 0x72, 0xe0, 0x2d, 0xef,
	0xc3, 0xa2, 0x7f, 0x7f, 0x1a, 0x6e, 0xca, 0x85
};


/***********************************************************************************************
  HMAC SHA_256, DF:None, PR:No, PersonalizationString:No, AdditionalInput:No
************************************************************************************************/
U8  ubPat5Ans[32]  = {
	0x98, 0xEB, 0xBA, 0xDB, 0xEE, 0x0D, 0x67, 0xF7,
	0xB8, 0xB7, 0x07, 0x50, 0xB0, 0xDA, 0x5E, 0x7D,
	0x90, 0x57, 0x26, 0x82, 0xB3, 0x57, 0xBF, 0x58,
	0x0E, 0xD8, 0x8C, 0x94, 0x52, 0x9C, 0xCE, 0x7F
};

U8  ubPat5EntropyInput[32]  = {
	0xeb, 0xd1, 0x11, 0x32, 0xd7, 0x83, 0x79, 0x60,
	0x50, 0x0a, 0x43, 0x6e, 0x46, 0x7a, 0xba, 0x7d,
	0xd2, 0x85, 0x46, 0xfa, 0xf6, 0xe7, 0x4f, 0xa9,
	0x95, 0x0c, 0x56, 0xef, 0xb4, 0x05, 0x50, 0x5e
};

U8  ubPat5EntropyPr1[32]  = {
	0x78, 0x5d, 0xd9, 0x36, 0x0f, 0x2f, 0x52, 0xaa,
	0x91, 0x53, 0xeb, 0x72, 0x65, 0x36, 0xfc, 0xd4,
	0x70, 0xc7, 0x5a, 0x9b, 0x68, 0x05, 0xd6, 0x3b,
	0x77, 0xc5, 0xf7, 0x41, 0x13, 0xc8, 0xfa, 0xff
};

U8  ubPat5EntropyPr2[32]  = {};

U8  ubPat5Nonce[16]  = {
	0x9a, 0x5e, 0xbb, 0xb0, 0xfd, 0x78, 0x0a, 0x00,
	0xd5, 0x2e, 0xe4, 0x38, 0xe6, 0xf8, 0x70, 0x84
};


/***********************************************************************************************
  HMAC SHA_256, DF:None, PR:No, PersonalizationString:No, AdditionalInput:Yes
************************************************************************************************/
U8  ubPat6AdditionalInput1[32]  = {
	0x36, 0x2c, 0x1a, 0x1b, 0x89, 0x7a, 0xf2, 0x5e,
	0xe7, 0x82, 0xdb, 0x82, 0xe7, 0x21, 0xdc, 0xbe,
	0x46, 0x7f, 0x36, 0xe2, 0xe4, 0xb5, 0x88, 0x9f,
	0x01, 0xbf, 0x22, 0x13, 0xb7, 0xa3, 0xa7, 0x9a
};

U8  ubPat6AdditionalInput2[32]  = {
	0x9d, 0x3a, 0x1a, 0xa0, 0x62, 0xf0, 0xda, 0x22,
	0xd2, 0xe4, 0x78, 0x79, 0xb8, 0x3a, 0xe5, 0x91,
	0x3f, 0x48, 0xaa, 0x78, 0x33, 0x85, 0x43, 0xe7,
	0x7c, 0xb3, 0xbf, 0x16, 0xd6, 0x4e, 0x9c, 0xc0
};

U8  ubPat6AdditionalInputReseed[32]  = {
	0x5e, 0xf9, 0x34, 0x6f, 0xd8, 0xf1, 0x5e, 0x4d,
	0xc7, 0xdd, 0x82, 0x30, 0x5d, 0x91, 0x81, 0xdd,
	0x55, 0x60, 0x62, 0xb6, 0x87, 0xe6, 0x18, 0x27,
	0xcb, 0x17, 0x77, 0x4d, 0x12, 0x87, 0x73, 0xe7
};

U8  ubPat6Ans[32]  = {
	0x29, 0xDC, 0x62, 0xEC, 0xDF, 0x19, 0xCF, 0x21,
	0x28, 0xB8, 0x81, 0x1B, 0x89, 0xAA, 0xB4, 0xD6,
	0xBD, 0x31, 0x1F, 0x24, 0x83, 0x6D, 0xD6, 0x72,
	0xB1, 0x54, 0xAD, 0x02, 0xEF, 0xDC, 0xA4, 0xE0
};

U8  Pat6EntropyInput[32]  = {
	0xce, 0x27, 0x01, 0xf6, 0x44, 0xaf, 0x76, 0xe7,
	0x39, 0x23, 0x23, 0x44, 0x1a, 0x42, 0xee, 0x24,
	0x84, 0x6a, 0x32, 0x79, 0x20, 0x9c, 0xc0, 0xb9,
	0xc2, 0xcd, 0x15, 0xc9, 0xc3, 0x6e, 0xdb, 0x2c
};

U8  ubPat6EntropyPr1[32]  = {
	0x37, 0x09, 0x2a, 0x9b, 0x0e, 0x9a, 0xc7, 0xa9,
	0x41, 0x6f, 0x2c, 0xd5, 0x67, 0xbc, 0xee, 0xd3,
	0x68, 0x98, 0xa9, 0xa8, 0xbe, 0xea, 0x61, 0x39,
	0xec, 0x98, 0x1a, 0x16, 0x3e, 0xec, 0xb5, 0x81
};

U8  ubPat6EntropyPr2[32]  = {};

U8  ubPat6Nonce[16]  = {
	0xe4, 0xf8, 0x8d, 0xa9, 0xd6, 0x61, 0x7a, 0xdc,
	0x3d, 0xd7, 0x52, 0xc6, 0x9e, 0xe7, 0x36, 0xec
};


/***********************************************************************************************
  HMAC SHA_256, DF:None, PR:No, PersonalizationString:Yes, AdditionalInput:No
************************************************************************************************/
U8  ubPat7PString[32]  = {
	0x65, 0x53, 0xa5, 0xf2, 0x5b, 0x64, 0x06, 0x37,
	0xe3, 0xc2, 0x06, 0x97, 0xb8, 0x3e, 0x5c, 0x66,
	0x8f, 0x02, 0x48, 0x1d, 0x10, 0x3c, 0xb7, 0xa9,
	0x11, 0x08, 0x9a, 0xf4, 0x14, 0xa1, 0x81, 0xb3
};

U8  ubPat7Ans[32]  = {
	0x85, 0xCB, 0xA6, 0x03, 0x54, 0x58, 0x9F, 0xF6,
	0x20, 0x1D, 0x62, 0x84, 0xC8, 0x06, 0x01, 0x51,
	0xD5, 0x97, 0x3A, 0x08, 0x30, 0xCB, 0x54, 0xAB,
	0x84, 0x3D, 0x95, 0x13, 0x79, 0xB3, 0xC2, 0xFA
};

U8  ubPat7EntropyInput[32]  = {
	0x2c, 0x56, 0xc3, 0xcd, 0xc3, 0x66, 0x12, 0x56,
	0x3b, 0xc7, 0xbd, 0x99, 0x49, 0x76, 0x95, 0xb6,
	0x89, 0xd0, 0xf5, 0x48, 0x16, 0x5a, 0x82, 0xd4,
	0x69, 0xd3, 0x21, 0x71, 0x75, 0x7a, 0x38, 0xba
};

U8  ubPat7EntropyPr1[32]  = {
	0xb0, 0x48, 0x0f, 0xc2, 0x85, 0x63, 0xc2, 0xb3,
	0x91, 0xe4, 0x47, 0x28, 0x2c, 0xbd, 0x9e, 0x5d,
	0x15, 0x15, 0x5f, 0xaf, 0xa5, 0x33, 0x17, 0x2a,
	0x54, 0xa9, 0xe7, 0x53, 0x70, 0xc4, 0x59, 0x35
};

U8  ubPat7EntropyPr2[32]  = {};

U8  ubPat7Nonce[16]  = {
	0x8e, 0x19, 0x8f, 0x08, 0xfa, 0x4d, 0xae, 0x02,
	0x0f, 0x71, 0xe2, 0x46, 0x63, 0x0e, 0xf7, 0x20
};

/***********************************************************************************************
  HMAC SHA_256, DF:None, PR:No, PersonalizationString:Yes, AdditionalInput:Yes
************************************************************************************************/
U8  ubPat8PString[32]  = {
	0x85, 0x18, 0x3e, 0xfa, 0xf3, 0x65, 0xdc, 0xba,
	0xe9, 0xfa, 0xc7, 0xe9, 0xa8, 0xa7, 0xe2, 0xa0,
	0xcf, 0x1c, 0x6c, 0xdb, 0xae, 0x7a, 0x80, 0xa0,
	0xe8, 0x1a, 0xcf, 0x6f, 0x4e, 0x9b, 0x9f, 0xeb
};

U8  ubPat8AdditionalInput1[32]  = {
	0xa9, 0x34, 0x68, 0x70, 0x8a, 0xfa, 0x36, 0x58,
	0xee, 0x25, 0x79, 0xe7, 0x0f, 0x31, 0x82, 0x66,
	0x23, 0xf1, 0x87, 0xd5, 0x7d, 0xdb, 0xda, 0xd2,
	0xe0, 0x92, 0x6a, 0xc8, 0x25, 0xb4, 0x7e, 0x5b
};

U8  ubPat8AdditionalInput2[32]  = {
	0x63, 0x24, 0xe5, 0x51, 0xc3, 0xd0, 0x27, 0x5f,
	0x8f, 0x24, 0xef, 0x75, 0x9c, 0x11, 0x0f, 0xb0,
	0xdc, 0x2e, 0x7b, 0xcf, 0xb4, 0x3e, 0xc3, 0x5d,
	0x9b, 0xff, 0xcd, 0x1e, 0xf6, 0x34, 0xd1, 0xb9
};

U8  ubPat8AdditionalInputReseed[32]  = {
	0x10, 0xb5, 0x14, 0x1a, 0x14, 0x0b, 0xf0, 0x93,
	0x16, 0x12, 0xc8, 0x51, 0x95, 0x2f, 0x9f, 0x62,
	0x87, 0xf7, 0x14, 0xa1, 0xf3, 0xf6, 0x1f, 0x21,
	0xce, 0xc0, 0x06, 0xdb, 0x15, 0x3c, 0xa5, 0x95
};

U8  ubPat8Ans[32]  = {
	0x25, 0xC8, 0x03, 0x75, 0xAB, 0x2D, 0xB6, 0x53,
	0x0A, 0x47, 0x23, 0x83, 0x96, 0x74, 0x8A, 0xAA,
	0x05, 0x10, 0xBC, 0x69, 0xB7, 0xF3, 0x8C, 0x0E,
	0x22, 0xC0, 0x7F, 0x7E, 0xFF, 0xEB, 0x89, 0x64
};

U8  ubPat8EntropyInput[32]  = {
	0x9c, 0x63, 0x86, 0xa2, 0xa3, 0xa1, 0x72, 0xda,
	0x78, 0x77, 0xc4, 0xa1, 0x55, 0x38, 0xb2, 0x1f,
	0x52, 0x97, 0xa5, 0x6b, 0xe0, 0xd3, 0xd8, 0x58,
	0x8b, 0x23, 0x33, 0x7d, 0xef, 0x64, 0x67, 0x84
};

U8  d[32]  = {
	0x3b, 0x35, 0x6c, 0x83, 0x3e, 0x5d, 0x11, 0xed,
	0xc1, 0x64, 0x69, 0x8e, 0xb6, 0x8b, 0xd7, 0xab,
	0x57, 0xa4, 0x91, 0x97, 0x81, 0x9f, 0x17, 0xe2,
	0x56, 0x57, 0xef, 0xc4, 0x12, 0x9d, 0xa4, 0xcb
};

U8  ubPat8EntropyPr2[32]  = {};

U8  ubPat8Nonce[16]  = {
	0x75, 0xb5, 0xdb, 0x92, 0xb9, 0x37, 0x20, 0xa3,
	0x9f, 0xb1, 0x3c, 0xee, 0x0d, 0x3c, 0x22, 0x05
};


//==============================================================================
// *****************************  START ****************************************
//==============================================================================
void TcgRngDRBGKat(TcgDRBGStructPtr_t DRBG)
{
	//U32     uplBuf[32/4];
	TcgDRBGKatStructPtr_t Kat = &DRBG->Kat;
	U8 *ubTestCnt = &DRBG->ubTestCnt;


	//1 Test1 HMAC SHA_256, DF:None, PR:Yes, PersonalizationString:No, AdditionalInput:No

	memset(Kat->ulKatBuf, 0x00, sizeof(Kat->ulKatBuf));

	Kat->pubKatEntropyInput = ubPat1EntropyInput;
	Kat->pubKatEntropyPr1  = ubPat1EntropyPr1;
	Kat->pubKatEntropyPr2  = ubPat1EntropyPr2;
	Kat->pubKatNonce         = ubPat1Nonce;
	*ubTestCnt              = 0;

	TcgRngDRBGInstantiate(DRBG, DRBGBaseHashMAC, DRBGSs256, TRUE, NULL, 0);

	*ubTestCnt = 1;
	TcgRngGenerate(DRBG, (U8 *)Kat->ulKatBuf, 256, DRBGSs256, TRUE, NULL, 0);

	*ubTestCnt = 2;
	TcgRngGenerate(DRBG, (U8 *)Kat->ulKatBuf, 256, DRBGSs256, TRUE, NULL, 0);

	if (memcmp((void *)Kat->ulKatBuf, (void *)ubPat1Ans, 32) != 0) {
		UartPrintf("[OPAL] DRBG Test 1 Fail\n");
		return;
	}


	// // 1 Test2 HMAC SHA_256, DF:None, PR:Yes, PersonalizationString:No, AdditionalInput:Yes

	// Kat->pubKatEntropyInput = ubPat2EntropyInput;
	// Kat->pubKatEntropyPr1  = ubPat2EntropyPr1;
	// Kat->pubKatEntropyPr2  = ubPat2EntropyPr2;
	// Kat->pubKatNonce         = ubPat2Nonce;
	// *ubTestCnt              = 0;

	// TcgRngDRBGInstantiate(DRBG, DRBGBaseHashMAC, DRBGSs256, TRUE, NULL, 0);

	// *ubTestCnt = 1;
	// TcgRngGenerate(DRBG, (U8 *)Kat->ulKatBuf, 25, DRBGSs256, TRUE, ubPat2AdditionalInput1, 256);

	// *ubTestCnt = 2;
	// TcgRngGenerate(DRBG, (U8 *)Kat->ulKatBuf, 256, DRBGSs256, TRUE, ubPat2AdditionalInput2, 256);

	// if (memcmp((void *)Kat->ulKatBuf, (void *)ubPat2Ans, 32) ) {
	// 	UartPrintf("[OPAL] DRBG Test 2 Fail\n");
	// 	return;
	// }


	// //1 Test3 HMAC SHA_256, DF:None, PR:Yes, PersonalizationString:Yes, AdditionalInput:No

	// Kat->pubKatEntropyInput = ubPat3EntropyInput;
	// Kat->pubKatEntropyPr1  = ubPat3EntropyPr1;
	// Kat->pubKatEntropyPr2  = ubPat3EntropyPr2;
	// Kat->pubKatNonce         = ubPat3Nonce;
	// *ubTestCnt              = 0;

	// TcgRngDRBGInstantiate(DRBG, DRBGBaseHashMAC, DRBGSs256, TRUE, ubPat3PString, 256);

	// *ubTestCnt = 1;
	// TcgRngGenerate(DRBG, (U8 *)Kat->ulKatBuf, 256, DRBGSs256, TRUE, NULL, 0);

	// *ubTestCnt = 2;
	// TcgRngGenerate(DRBG, (U8 *)Kat->ulKatBuf, 256, DRBGSs256, TRUE, NULL, 0);

	// if (memcmp((void *)Kat->ulKatBuf, (void *)ubPat3Ans, 32) ) {
	// 	UartPrintf("[OPAL] DRBG Test 3 Fail\n");
	// 	return;
	// }


	// //1 Test4 HMAC SHA_256, DF:None, PR:Yes, PersonalizationString:Yes, AdditionalInput:Yes

	// Kat->pubKatEntropyInput = ubPat4EntropyInput;
	// Kat->pubKatEntropyPr1  = ubPat4EntropyPr1;
	// Kat->pubKatEntropyPr2  = ubPat4EntropyPr2;
	// Kat->pubKatNonce         = ubPat4Nonce;
	// *ubTestCnt              = 0;

	// TcgRngDRBGInstantiate(DRBG, DRBGBaseHashMAC, DRBGSs256, TRUE, ubPat4PString, 256);

	// *ubTestCnt = 1;
	// TcgRngGenerate(DRBG, (U8 *)Kat->ulKatBuf, 256, DRBGSs256, TRUE, ubPat4AdditionalInput1, 256);

	// *ubTestCnt = 2;
	// TcgRngGenerate(DRBG, (U8 *)Kat->ulKatBuf, 256, DRBGSs256, TRUE, ubPat4AdditionalInput2, 256);

	// if (memcmp((void *)Kat->ulKatBuf, (void *)ubPat4Ans, 32) ) {
	// 	UartPrintf("[OPAL] DRBG Test 4 Fail\n");
	// 	return;
	// }


	// //1 Test5 MAC SHA_256, DF:None, PR:No, PersonalizationString:No, AdditionalInput:No

	// Kat->pubKatEntropyInput = ubPat5EntropyInput;
	// Kat->pubKatEntropyPr1  = ubPat5EntropyPr1;
	// Kat->pubKatEntropyPr2  = ubPat5EntropyPr2;
	// Kat->pubKatNonce         = ubPat5Nonce;
	// *ubTestCnt       = 0;

	// TcgRngDRBGInstantiate(DRBG, DRBGBaseHashMAC, DRBGSs256, FALSE, NULL, 0);

	// *ubTestCnt = 1;
	// TcgRngGenerate(DRBG, (U8 *)Kat->ulKatBuf, 256, DRBGSs256, FALSE, NULL, 0);
	// TcgRngDRBGReseed(DRBG, NULL, 0);

	// *ubTestCnt = 2;
	// TcgRngGenerate(DRBG, (U8 *)Kat->ulKatBuf, 256, DRBGSs256, FALSE, NULL, 0);

	// if (memcmp((void *)Kat->ulKatBuf, (void *)ubPat5Ans, 32) ) {
	// 	UartPrintf("[OPAL] DRBG Test 5 Fail\n");
	// 	return;
	// }


	// //1 Test6 HMAC SHA_256, DF:None, PR:No, PersonalizationString:No, AdditionalInput:Yes

	// Kat->pubKatEntropyInput = Pat6EntropyInput;
	// Kat->pubKatEntropyPr1  = ubPat6EntropyPr1;
	// Kat->pubKatEntropyPr2  = ubPat6EntropyPr2;
	// Kat->pubKatNonce         = ubPat6Nonce;
	// *ubTestCnt              = 0;

	// TcgRngDRBGInstantiate(DRBG, DRBGBaseHashMAC, DRBGSs256, FALSE, NULL, 0);

	// *ubTestCnt = 1;
	// TcgRngGenerate(DRBG, (U8 *)Kat->ulKatBuf, 256, DRBGSs256, FALSE, ubPat6AdditionalInput1, 256);
	// TcgRngDRBGReseed(DRBG, ubPat6AdditionalInputReseed, 256);

	// *ubTestCnt = 2;
	// TcgRngGenerate(DRBG, (U8 *)Kat->ulKatBuf, 256, DRBGSs256, FALSE, ubPat6AdditionalInput2, 256);

	// if (memcmp((void *)Kat->ulKatBuf, (void *)ubPat6Ans, 32) ) {
	// 	UartPrintf("[OPAL] DRBG Test 6 Fail\n");
	// 	return;
	// }


	// //1 Test7 HMAC SHA_256, DF:None, PR:No, PersonalizationString:Yes, AdditionalInput:No

	// Kat->pubKatEntropyInput = ubPat7EntropyInput;
	// Kat->pubKatEntropyPr1  = ubPat7EntropyPr1;
	// Kat->pubKatEntropyPr2  = ubPat7EntropyPr2;
	// Kat->pubKatNonce         = ubPat7Nonce;
	// *ubTestCnt              = 0;

	// TcgRngDRBGInstantiate(DRBG, DRBGBaseHashMAC, DRBGSs256, FALSE, ubPat7PString, 256);

	// *ubTestCnt = 1;
	// TcgRngGenerate(DRBG, (U8 *)Kat->ulKatBuf, 256, DRBGSs256, FALSE, NULL, 0);
	// TcgRngDRBGReseed(DRBG, NULL, 0);

	// *ubTestCnt = 2;
	// TcgRngGenerate(DRBG, (U8 *)Kat->ulKatBuf, 256, DRBGSs256, FALSE, NULL, 0);

	// if (memcmp((void *)Kat->ulKatBuf, (void *)ubPat7Ans, 32) ) {
	// 	UartPrintf("[OPAL] DRBG Test 7 Fail\n");
	// 	return;
	// }


	// //1 Test8 HMAC SHA_256, DF:None, PR:No, PersonalizationString:No, AdditionalInput:Yes

	// Kat->pubKatEntropyInput = ubPat8EntropyInput;
	// Kat->pubKatEntropyPr1  = d;
	// Kat->pubKatEntropyPr2  = ubPat8EntropyPr2;
	// Kat->pubKatNonce         = ubPat8Nonce;
	// *ubTestCnt              = 0;

	// TcgRngDRBGInstantiate(DRBG, DRBGBaseHashMAC, DRBGSs256, FALSE, ubPat8PString, 256);

	// *ubTestCnt = 1;
	// TcgRngGenerate(DRBG, (U8 *)Kat->ulKatBuf, 256, DRBGSs256, FALSE, ubPat8AdditionalInput1, 256);
	// TcgRngDRBGReseed(DRBG, ubPat8AdditionalInputReseed, 256);

	// *ubTestCnt = 2;
	// TcgRngGenerate(DRBG, (U8 *)Kat->ulKatBuf, 256, DRBGSs256, FALSE, ubPat8AdditionalInput2, 256);

	// if (memcmp((void *)Kat->ulKatBuf, (void *)ubPat8Ans, 32) ) {
	// 	UartPrintf("[OPAL] DRBG Test 8 Fail\n");
	// 	return;
	// }

	UartPrintf("\n[OPAL] DRBG KAT PASS\n");
}

#endif /*(TRUE == (TCG_EN && (!LPM3_LOADER)))*/
