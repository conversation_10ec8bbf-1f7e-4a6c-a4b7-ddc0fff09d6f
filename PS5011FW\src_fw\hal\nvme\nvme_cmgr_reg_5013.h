#ifndef _NVME_CMGR_REG_H_
#define _NVME_CMGR_REG_H_

#include "mem.h"


#define NVME_CMGR_REG_BASE  (NVME_REG_ADDRESS)

#define	R8_CMGR									((REG8 *)NVME_CMGR_REG_BASE)
#define	R16_CMGR								((REG16 *)NVME_CMGR_REG_BASE)
#define	R32_CMGR								((REG32 *)NVME_CMGR_REG_BASE)
#define	R64_CMGR								((REG64 *)NVME_CMGR_REG_BASE)

/*=========================  CMGR interrupt  ================================*/
#define	R32_NVME_CMGR_INT						(0x00000000 >> 2)
#define 	CMGR_LOCK							(BIT0)
#define 	CMGR_IDLE_INT						(BIT1)
#define 	APU_R_CMD_ERR						(BIT2)
#define 	APU_W_CMD_ERR						(BIT3)
#define 	APU_R_UNC_ERR						(BIT4)
#define 	PNSRAM_PERR							(BIT5)
#define 	CTSRAM_PERR							(BIT6)
#define 	GPSRAM_PERR							(BIT7)
#define 	NLOG_BUSY_OV						(BIT8)
#define 	NLOG_RDAT_OV						(BIT9)
#define 	NLOG_RCMD_OV						(BIT10)
#define 	NLOG_WDAT_OV						(BIT11)
#define 	NLOG_WCMD_OV						(BIT12)
#define		RCH_LCA_ERR							(BIT13)

#define	R32_NVME_CMGR_INT_EN					(0x00000004 >> 2)
#define		CMGR_LOCK_EN_BIT					(BIT0)
#define		CMGR_IDLE_INT_EN_BIT				(BIT1)
#define		APU_R_CMD_ERR_EN_BIT				(BIT2)
#define		APU_W_CMD_ERR_EN_BIT				(BIT3)
#define		APU_R_UNC_ERR_EN_BIT				(BIT4)
#define		PNSRAM_PERR_EN_BIT					(BIT5)
#define		CTSRAM_PERR_EN_BIT					(BIT6)
#define		GPSRAM_PERR_EN_BIT					(BIT7)
#define		NLOG_BUSY_OV_EN_BIT					(BIT8)
#define		NLOG_RDAT_OV_EN_BIT					(BIT9)
#define		NLOG_RCMD_OV_EN_BIT					(BIT10)
#define		NLOG_WDAT_OV_EN_BIT					(BIT11)
#define		NLOG_WCMD_OV_EN_BIT					(BIT12)

/*=========================  CMGR Control  ================================*/

#define	R8_NVME_CMGR_CTRL						(0x00000010)
#define		CMGR_AUTO_LOCK_EN_BIT				(BIT0)
#define		CMGR_AES_CHK_EN_BIT					(BIT1)
#define		CMGR_CID_SCAN_EN_BIT				(BIT2)
#define		CMGR_IOR_ACPL_EN_BIT				(BIT3)
#define		CMGR_SRAM_AUTO_LS_EN_BIT			(BIT4)
#define		CMGR_DB_ACTIVE_BIT					(BIT5)
#define		CMGR_IOR_SYNC_BIT					(BIT6)
#define		CMGR_SRAM_AUTO_CG_BIT				(BIT7)
#define		CMGR_CTAG_EOFST_SHIFT				(8)
#define		CMGR_CTAG_EOFST_MASK				(BIT_MASK(7))
#define		CMGR_SRAM_ENTRY_THR_SHIFT			(16)
#define		CMGR_SRAM_ENTRY_THR_MASK			(BIT_MASK(8))
#define		CMGR_SRAM_EXIT_THR_SHIFT			(24)
#define		CMGR_SRAM_EXIT_THR_MASK				(BIT_MASK(8))

#define	R8_CMGR_SRAM_ENTRY_THR					(0x00000012)
#define	R8_CMGR_SRAM_EXIT_THR					(0x00000013)

#define	R32_NVME_CMGR_ERR_CTRL					(0x00000014 >> 2)
#define		APU_R_CMD_ERR_LOCK_EN_BIT			(BIT0)
#define		APU_W_CMD_ERR_LOCK_EN_BIT			(BIT1)
#define		APU_R_UNC_LOCK_EN_BIT				(BIT2)
#define		APU_SPAR_CHK_EN_BIT					(BIT3)
#define		APU_PAR_CHK_EN_BIT					(BIT4)
#define		APU_DRU_ERR_INJ_BIT					(BIT8)
#define		APU_DWU_ERR_INJ_BIT					(BIT9)

#define	R16_NVME_CMGR_APU_DLY_WR_TMR			(0x00000018 >> 1)

#define	R16_NVME_CMGR_APU_DLY_RD_TMR			(0x0000001A >> 1)

#define	R16_NVME_CMGR_APU_DLY_CTRL			    (0x0000001C >> 1)

#define	R8_NVME_CMGR_APU_DLY_CTRL				(0x0000001E)
#define		APU_DLY_WR_EN_BIT					(BIT0)
#define		APU_DLY_RD_EN_BIT					(BIT1)

#define	R8_NVME_CMGR_MDTS						(0x00000020)

#define	R8_NVME_CMGR_MPS						(0x00000021)

#define	R32_NVME_CMGR_NLOG_MS_UNIT				(0x00000024 >> 2)
#define R64_NVME_CMGR_FW_GPSRAM_QW_RDATA        (0x00000028 >> 3)
#define	R32_NVME_CMGR_IDLE_TMR					(0x00000030 >> 2)

#define	R32_NVME_CMGR_IDLE_TMR_THR				(0x00000034 >> 2)
#define	R32_NVME_CMGR_IDLE_TMR_CTRL				(0x00000038 >> 2)
#define		CMGR_IDLE_TMR_EN_BIT				(BIT0)
#define		CMGR_IDLE_TMR_PAUSE_BIT				(BIT1)

#define R32_NVME_CMGR_NLOG_SYS_CNT              (0x0000003C >> 2)

#define	R8_NVME_CMGR_FW_CLR_CVLD_CTAG			(0x00000048)
#define		CMGR_FW_CLR_CVLD_CTAG_SHIFT			(0)
#define		CMGR_FW_CLR_CVLD_CTAG_MASK			(BIT_MASK(7))

#define	R8_NVME_CMGR_FW_CLR_CVLD_REG			(0x00000049)
#define		CMGR_FW_CLR_CVLD_REQ_BIT			(BIT0)
#define		CMGR_FW_DROP_ACT_BIT				(BIT1)

#define	R32_NVME_CMGR_RST						(0x0000004C >> 2)
#define		CMGR_APU_RST_BIT					(BIT0)
#define		CMGR_APU_PAUSE_BIT					(BIT1)
#define		CMGR_APU_LSI_RST_BIT				(BIT2)

#define	R8_NVME_CMGR_NVME_QCNT					(0x00000050)

#define	R8_NVME_CMGR_BUSY						(0x00000052)
#define		CMGR_BUSY_BIT						(BIT0)

#define	R8_APU_UNC_CTAG							(0x00000062)
#define		APU_UNC_CTAG_SHIFT					(0)
#define		APU_UNC_CTAG_MASK					(BIT_MASK(7))

#define	R32_NVME_CMGR_APU_UNC_LBA				(0x00000068 >> 2)
#define	R32_NVME_CMGR_APU_UNC_NSID				(0x0000006C >> 2)
#define		APU_UNC_NSID_SHIFT					(0)
#define		APU_UNC_NSID_MASK					(BIT_MASK(3))

#define	R32_NVME_CMGR_RERR_INFO					(0x00000070 >> 2)
#define     RLS_ERR_CTAG_SHIFT                  (0)
#define     RLS_ERR_CTAG_MASK                   (BIT_MASK(7))
#define     RLS_ERR_CTAG                        (BITMSK(7,0))   // BIT [6:0]
#define		RLS_APU_PAR_ERR_BIT					(BIT16)
#define		RLS_PRP_ERR_BIT						(BIT17)
#define		RLS_PCIE_AXI_ERR_BIT				(BIT18)
#define		RLS_SYS_AXI_ERR_BIT					(BIT19)
#define		RLS_E3D_ERR_BIT						(BIT20)
#define		RLS_UNC_ERR_BIT						(BIT21)

#define	R32_NVME_CMGR_WERR_INFO					(0x00000074 >> 2)
#define     WLS_ERR_CTAG_SHIFT                  (0)
#define     WLS_ERR_CTAG_MASK                   (BIT_MASK(7))
#define     WLS_ERR_CTAG                        (BITMSK(7,0))   // BIT [6:0]
#define		WLS_PRP_ERR_BIT						(BIT16)
#define		WLS_PCIE_PAR_ERR_BIT				(BIT17)
#define		WLS_PCI_ERR_BIT						(BIT18)

/*
#define	CMGR_FIFO_VLD                                (0x620)//- Indicate the command fifo valid.
#define     APU_CHK_CMD_FIFO_VLD(N)                      (r8_APU[APUB_CMD_FIFO_VLD+(((U32)N) >> 3)] & BIT(N % 8))
#define     APU_GET_CMD_FIFO_VLD(N)                      (r32_APU[(APUB_CMD_FIFO_VLD >> 2)+((U32)N)])//- Indicate the command fifo valid.
#define     APU_CMD_FIFO_VLD_NUM                         (8)//256/32
*/

#define R32_NVME_CMGR_FCF_CMD_DW0					(0x00000080 >> 2)
#define R32_NVME_CMGR_FCF_CMD_DW1					(0x00000084 >> 2)
#define R32_NVME_CMGR_FCF_CMD_DW2					(0x00000088 >> 2)
#define R32_NVME_CMGR_FCF_CMD_DW3					(0x0000008c >> 2)
#define R32_NVME_CMGR_FCF_CMD_DW4					(0x00000090 >> 2)
#define R32_NVME_CMGR_FCF_CMD_DW5					(0x00000094 >> 2)
#define R32_NVME_CMGR_FCF_CMD_DW6					(0x00000098 >> 2)
#define R32_NVME_CMGR_FCF_CMD_DW7					(0x0000009c >> 2)
#define R32_NVME_CMGR_FCF_CMD_DW8					(0x000000A0 >> 2)
#define R32_NVME_CMGR_FCF_CMD_DW9					(0x000000A4 >> 2)
#define R32_NVME_CMGR_FCF_CMD_DW10					(0x000000A8 >> 2)
#define R32_NVME_CMGR_FCF_CMD_DW11					(0x000000AC >> 2)
#define R32_NVME_CMGR_FCF_CMD_DW12					(0x000000B0 >> 2)
#define R32_NVME_CMGR_FCF_CMD_DW13					(0x000000B4 >> 2)
#define R32_NVME_CMGR_FCF_CMD_DW14					(0x000000B8 >> 2)
#define R32_NVME_CMGR_FCF_CMD_DW15					(0x000000BC >> 2)
#define	R32_NVME_CMGR_FCF_INFO					    (0x00000080 >> 2)
#define		CMGR_GET_CFC_CMD_DWN(N)				(R32_CMGR[R32_NVME_CMGR_FCF_INFO + N]) // N = 0~15

#define	R32_NVME_CMGR_FCF_ERR_INFO				(0x000000C0 >> 2)
#define		CMGR_FCF_SQID_SHIFT					(0)
#define		CMGR_FCF_SQID_MASK					(BIT_MASK(4))
#define     CMGR_FCF_SQID                       (BITMSK(4,0))
#define		CMD_PRP2_ERR_BIT					(BIT16)
#define		CMD_PRP1_ERR_BIT					(BIT17)
#define		CMD_STRID_ERR_BIT					(BIT18) //RSV in HW Spec?
#define		CMD_LBA_ERR_BIT						(BIT19)
#define		CMD_WP_ERR_BIT						(BIT20)
#define		CMD_DSM_ERR_BIT						(BIT21)
#define		CMD_PSDT_ERR_BIT					(BIT22)
#define		CMD_FUSE_ERR_BIT					(BIT23)
#define		CMD_MDTS_ERR_BIT					(BIT24)
#define		CMD_AES_ERR_BIT						(BIT25)
#define		CMD_CID_ERR_BIT						(BIT26)
#define		CMD_NS_ERR_BIT						(BIT27)
#define		CMD_RPF_ERR_SHIFT					(28)
#define		CMD_RPF_ERR_MASK					(BIT_MASK(2))
#define     CMD_RPF_ERR                         (BITMSK(2,28))
#define		PRP_FETCH_AXI_ERR_BIT				(BIT28)
#define		PRP_INVALD_OFFSET_ERR_BIT			(BIT29)
#define		CMD_NL_ERR_BIT						(BIT30)
#define		CMD_DTYPE_ERR_BIT					(BIT31)

#define	R32_NVME_CMGR_RPF_ERR_OFST				(0x000000C4 >> 2)
#define		RPF_ERR_OFST_SHIFT					(0)
#define		RPF_ERR_OFST_MASK					(BIT_MASK(16))

/*================================= Write DMA temporary PRP ================================*/

#define	NVME_CMGR_GPSRAM_WCH_PRP_SIZE			(0x10)
#define	NVME_CMGR_GPSRAM_WCH_PRP_OFFSET			(0x800)
#define	R64_NVME_CMGR_PRP_ENTRY 				((volatile U64 (*)[NVME_CMGR_GPSRAM_WCH_PRP_SIZE >> 3])(NVME_CMGR_REG_BASE + NVME_CMGR_GPSRAM_WCH_PRP_OFFSET))

#define	R64_GPSRAM_WCH_PRP_ENTRY1_OFFSET			(0x000 >> 3)
#define	R64_GPSRAM_WCH_PRP_ENTRY2_OFFSET			(0x008 >> 3)

/*================================= Namespace Information ================================*/

/*
#define	NVME_CMGR_GPSRAM_NS_INFO_SIZE			(0x08)
#define	NVME_CMGR_GPSRAM_NS_INFO_OFFSET			(0x900 >> 3)
#define	R64_NVME_CMGR_GPSRAM_NS_INFO			((volatile U64 (*)[NVME_CMGR_GPSRAM_NS_INFO_SIZE >> 3])(NVME_CMGR_REG_BASE + NVME_CMGR_GPSRAM_NS_INFO_OFFSET))
#define		GPSRAM_NS_SIZE_SHIFT				(0)
#define		GPSRAM_NS_SIZE_MASK					(BIT_MASK(40))
#define		GPSRAM_NSID_SHIFT					(40)
#define		GPSRAM_NSID_MASK					(BIT_MASK(4))
#define 	GPSRAM_NS_LBAF_BIT					(BIT44)	// 0:512Byte	1:4KB
#define 	GPSRAM_NS_LBAF512					(0)
#define 	GPSRAM_NS_LBAF4K					(1)
#define		GPSRAM_NW_WP_BIT					(BIT45)
#define		GPSRAM_NS_ACT_BIT					(BIT46)
#define		GPSRAM_NS_STRID_EN_SHIFT			(48)
#define		GPSRAM_NS_STRID_EN_MASK				(BIT_MASK(8))
*/

#define	NVME_CMGR_GPSRAM_NS_INFO_QW_SIZE		(0x08 >> 3)
#define	NVME_CMGR_GPSRAM_NS_INFO_DW_SIZE		(0x08 >> 2)
#define	R64_NVME_CMGR_GPSRAM_NS_INFO			(0x900 >> 3)
#define		GPSRAM_NS_SIZE_SHIFT				(0)
#define		GPSRAM_NS_SIZE_MASK					(BIT_MASK64(40))
#define	R32_NVME_CMGR_GPSRAM_NS_INFO_DW0		(0x900 >> 2)
#define	R32_NVME_CMGR_GPSRAM_NS_INFO_DW1		(0x904 >> 2)
#define		GPSRAM_NSID_SHIFT					(8)
#define		GPSRAM_NSID_MASK					(BIT_MASK(4))
#define     GPSRAM_NSID                         (BITMSK(4,8))
#define     GPSRAM_NS_LBAF_SHIFT                (12)
#define		GPSRAM_NS_LBAF_BIT					(BIT12)	// 0:512Byte	1:4KB
#define		GPSRAM_NS_LBAF512					(0)
#define		GPSRAM_NS_LBAF4K					(1)
#define     GPSRAM_NW_WP_SHIFT                  (13)
#define		GPSRAM_NW_WP_BIT					(BIT13)
#define     GPSRAM_NS_ACT_SHIFT                 (14)
#define		GPSRAM_NS_ACT_BIT					(BIT14)
#define		GPSRAM_NS_STRID_EN_SHIFT			(16)
#define		GPSRAM_NS_STRID_EN_MASK				(BIT_MASK(8))
#define     GPSRAM_NS_STRID_EN                  (BITMSK(8,16))

/*================================= SMART/HEALTH Information LOG ================================*/
#define	R64_NVME_CMGR_NLOG_RCMD_CNT				(0x940 >> 3)
#define	R64_NVME_CMGR_NLOG_RDAT_CNT				(0x948 >> 3)
#define	R64_NVME_CMGR_NLOG_WCMD_CNT				(0x950 >> 3)
#define	R64_NVME_CMGR_NLOG_WDAT_CNT				(0x958 >> 3)
#define	R64_NVME_CMGR_NLOG_BUSY_MS				(0x960 >> 3)
#define	R64_NVME_CMGR_NLOG_BUSY_MIN				(0x968 >> 3)

/*================================= PRP Entry for read Command ================================*/

#define NVME_CMGR_PNSRAM_INFO_SIZE				(0x10)
#define	NVME_CMGR_PNSRAM_INFO_OFFSET			(0x1000)
#define	R64_NVME_CMGR_PNSRAM_INFO_ENTRY			((volatile U64 (*)[NVME_CMGR_PNSRAM_INFO_SIZE >> 3])(NVME_CMGR_REG_BASE + NVME_CMGR_PNSRAM_INFO_OFFSET))

#define	R64_PNSRAM_RD_PRP_ENTRY1_OFFSET			(0x0000 >> 3)
#define	R64_PNSRAM_RD_PRP_ENTRY2_OFFSET			(0x0008 >> 3)

/*================================= CMGR Command Table SRAM ================================*/
#define	NVME_CMGR_CMD_TABLE_SRAM_ENTRY_SIZE		(0x40)
#define CTSRAM_CMD_OFFSET_DW                    (NVME_CMGR_CMD_TABLE_SRAM_ENTRY_SIZE >> 2)
#define CTSRAM_CMD_OFFSET_QW                    (NVME_CMGR_CMD_TABLE_SRAM_ENTRY_SIZE >> 3)
#define	NVME_CMGR_CMD_TABLE_SRAM_OFFSET			(0x2000)
#define	R64_NVME_CMGR_CMD_TABLE_SRAM			((volatile U64 (*)[NVME_CMGR_CMD_TABLE_SRAM_ENTRY_SIZE >> 3])(NVME_CMGR_REG_BASE + NVME_CMGR_CMD_TABLE_SRAM_OFFSET))
// offset minus 0x2000
#define	R64_NVME_CMGR_CTSRAM_CMD_INFO_OFFSET	(0x0000 >> 3)
#define R64_NVME_CMGR_CTSRAM_CMD_INFO           (0x2000 >> 3)
#define CTSRAM_DW0                              (0x2000 >> 2)
#define     CTSRAM_OPC_SHIFT                    (0)
#define     CTSRAM_OPC_MASK                     (BIT_MASK(8))
#define     CTSRAM_OPC                          (BITMSK(8,0))   // BIT [7:0]
#define     CTSRAM_FUSE_SHIFT                   (8)
#define     CTSRAM_FUSE_MASK                    (BIT_MASK(2))
#define     CTSRAM_FUSE                         (BITMSK(2,8))   // BIT [9:8]
#define     CTSRAM_PSDT_SHIFT                   (14)
#define     CTSRAM_PSDT_MASK                    (BIT_MASK(2))
#define     CTSRAM_PSDT                         (BITMSK(2,14))  // BIT [15:14]
#define     CTSRAM_CID_SHIFT                    (16)
#define     CTSRAM_CID_MASK                     (BIT_MASK(16))
#define     CTSRAM_CID                          (BITMSK(16,16)) // BIT [31:16]
#define CTSRAM_DW1                              (0x2004 >> 2)
#define		CTSRAM_NSID_SHIFT					(0)
#define		CTSRAM_NSID_MASK					(BIT_MASK64(32))

#define	R64_NVME_CMGR_CTSRAM_CMD_STS_OFFSET		(0x0008 >> 3)
#define	R64_NVME_CMGR_CTSRAM_CMD_STS	        (0x2008 >> 3)
#define CTSRAM_DW2                              (0x2008 >> 2)
#define		CTSRAM_VLD_BIT						(BIT0)
#define		CTSRAM_CMD_TYPE_SHIFT				(1)
#define		CTSRAM_CMD_TYPE_MASK				(BIT_MASK(2))
#define     CTSRAM_CMD_TYPE                     (BITMSK(2,1))
#define		CTSRAM_CMD_STS_SHIFT				(3)
#define		CTSRAM_CMD_STS_MASK					(BIT_MASK(2))
#define     CTSRAM_CMD_STS                      (BITMSK(2,3))
#define     CTSRAM_PT_SHIFT                     (5)
#define		CTSRAM_PT_BIT						(BIT5)
#define		CTSRAM_ERR_SHIFT					(6)
#define		CTSRAM_ERR_BIT						(BIT6)
#define		CTSRAM_ACPL_BIT						(BIT7)
#define		CTSRAM_ADM_BIT						(BIT8)
#define		CTSRAM_LPN_BIT						(BIT13)
#define		CTSRAM_PNV_BIT						(BIT14)
#define     CTSRAM_LBAF_SHIFT                   (15)
#define		CTSRAM_LBAF_BIT						(BIT15)
#define         LBAF_512                        (0)
#define         LBAF_4K                         (1)
#define		CTSRAM_PNS_SHIFT					(16)
#define		CTSRAM_PNS_MASK						(BIT_MASK(6))
#define		CTSRAM_SSD_NSID_SHIFT				(24)
#define		CTSRAM_SSD_NSID_MASK				(BIT_MASK(3))
#define     CTSRAM_SSD_NSID                     (BITMSK(3,24))
#define CTSRAM_DW3                              (0x200C >> 2)
#define		CTSRAM_PNIDX_SHIFT					(0)
#define		CTSRAM_PNIDX_MASK					(BIT_MASK(6))
#define		CTSRAM_SQID_SHIFT					(8)
#define		CTSRAM_SQID_MASK					(BIT_MASK(4))
#define     CTSRAM_SQID                         (BITMSK(4,8))
#define		CTSRAM_CID_STS_SHIFT				(16)
#define		CTSRAM_CID_STS_MASK					(BIT_MASK(16))

#define	R64_NVME_CMGR_CTSRAM_RLBN_OFFSET		(0x0010 >> 3)
#define	R64_NVME_CMGR_CTSRAM_RLBN		        (0x2010 >> 3)
#define CTSRAM_DW4                              (0x2010 >> 2)
#define		CTSRAM_Z0_RLBN_SHIFT				(0)
#define		CTSRAM_Z0_RLBN_MASK					(BIT_MASK(17))
#define		CTSRAM_Z0_RLBN					    (BITMSK(17,0))
#define CTSRAM_DW5                              (0x2014 >> 2)
#define		CTSRAM_Z1_RLBN_SHIFT				(0)
#define		CTSRAM_Z1_RLBN_MASK					(BIT_MASK(17))
#define		CTSRAM_Z1_RLBN					    (BITMSK(17,0))

#define	R64_NVME_CMGR_CTSRAM_PRP1_OFFSET		(0x0018 >> 3)
#define	R64_NVME_CMGR_CTSRAM_PRP1		        (0x2018 >> 3)
#define CTSRAM_DW6                              (0x2018 >> 2)
#define CTSRAM_DW7                              (0x201C >> 2)

#define	R64_NVME_CMGR_CTSRAM_PRP2_OFFSET		(0x0020 >> 3)
#define	R64_NVME_CMGR_CTSRAM_PRP2   		    (0x2020 >> 3)
#define CTSRAM_DW8                              (0x2020 >> 2)
#define CTSRAM_DW9                              (0x2024 >> 2)

#define	R64_NVME_CMGR_CTSRAM_SLBA_OFFSET		(0x0028 >> 3)
#define	R64_NVME_CMGR_CTSRAM_SLBA		        (0x2028 >> 3)
#define CTSRAM_DW10                             (0x2028 >> 2)
#define CTSRAM_DW11                             (0x202C >> 2)

#define	R64_NVME_CMGR_CTSRAM_CMD_SPC_OFFSET		(0x0030 >> 3)
#define	R64_NVME_CMGR_CTSRAM_CMD_SPC		    (0x2030 >> 3)
#define CTSRAM_DW12                             (0x2030 >> 2)
#define		CTSRAM_NLB_SHIFT					(0)
#define		CTSRAM_NLB_MASK						(BIT_MASK(16))
#define     CTSRAM_NLB                          (BITMSK(16,0))
#define		CTSRAM_DTYPE_SHIFT					(20)
#define		CTSRAM_DTYPE_MASK					(BIT_MASK(4))
#define     CTSRAM_DTYPE                        (BITMSK(4,20))
#define		CTSRAM_PRINFO_SHIFT					(26)
#define		CTSRAM_PRINFO_MASK					(BIT_MASK(4))
#define     CTSRAM_PRINFO                       (BITMSK(4,26)) //RSV in HW spec...
#define     CTSRAM_FUA_SHIFT                    (30)
#define		CTSRAM_FUA_BIT						(BIT30)
#define     CTSRAM_LR_SHIFT                     (31)
#define		CTSRAM_LR_BIT						(BIT31)
#define CTSRAM_DW13                             (0x2034 >> 2)
#define		CTSRAM_DSM_SHIFT					(0)
#define		CTSRAM_DSM_MASK						(BIT_MASK(8))
#define     CTSRAM_DSM                          (BITMSK(8,0))
#define		CTSRAM_DSPEC_SHIFT					(16)
#define		CTSRAM_DSPEC_MASK					(BIT_MASK(16))
#define     CTSRAM_DSPEC                        (BITMSK(16,16))

#define	R64_NVME_CMGR_CTSRAM_QW7_OFFSET			(0x0038 >> 3)
#define	R64_NVME_CMGR_CTSRAM_QW7			    (0x2038 >> 3)
#define CTSRAM_DW14                             (0x2038 >> 2)
#define CTSRAM_DW15                             (0x203C >> 2)

/*========================= START : CMGR Debug Port =========================*/
#define R32_NVME_CMGR_LCA_MGR_DB_PORT           (0x0118 >> 2)
#define     CMGR_WRITE_PRP_DEBUG1			    (BIT12)
#define     CMGR_WRITE_PRP_DEBUG2				(BIT21)
#define     DMA_CNT                             (BITMSK(8,0))   // [3:0]: Write LCA cnt, [7:4]: Read LCA cnt
#define     DMA_IDLE_BIT                        (BIT30)

#define R32_NVME_CMGR_DEBUG_PORT_0X120          (0x0120 >> 2)
#define     READ_DMA_UPDATE_CMD_TABLE_IDLE_BIT      (BIT28)

#define R32_NVME_CMGR_CFMGR_DB_PORT         (0x00000128 >> 2)
#define     CFMGR_IDLE_BIT                          (BIT0)

#define R32_NVME_CMGR_LCA_MGR_DB_PORT3      (0x0000012C >> 2)
#define     CMGR_READ_PRP_DEBUG1_BIT            (BIT4)

#define	R32_NVME_CMGR_CFMGR_DB_PORT2		(0x00000134 >> 2)
#define		CMGR_PARSING_CMD_IDLE				(BIT0)

#define	R32_NVME_CMGR_MGR_DB_PORT		    (0x0000013C >> 2)
#define     CMGR_PNSRAM_FULL_BIT                (BIT4)

/*====================== END: CMGR register =============================*/

#endif /* _NVME_CMGR_REG_H_ */

