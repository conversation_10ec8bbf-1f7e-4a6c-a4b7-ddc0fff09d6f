/**************************************************************************/
/*                                                                        */
/*           Copyright (c) 2000-2017 by Phison Electronics Corp.          */
/*                                                                        */
/**************************************************************************/

/**************************************************************************/
/*                                                                        */
/*  FILE : shr_hal_bmu_reg.h             PROJECT : PS5011                 */
/*                                                                        */
/*                                                                        */
/*  DESCRIPTION                                                           */
/*                                                                        */
/*    This is the header file for BMU register definition                 */
/*                                                                        */
/*  RELEASE HISTORY                                                       */
/*                                                                        */
/*    DATE              NAME                DESCRIPTION                   */
/*                                                                        */
/*  2017-04-12        Jay Huang             Initial Version 1.0           */
/*                                                                        */
/**************************************************************************/
#ifndef _SHR_HAL_BMU_REG_H_
#define _SHR_HAL_BMU_REG_H_

// marco for lib


#include "hal/bmu/bmu_reg.h"


#define BMU_REGISTER_ADDRESS  (BMU_REG_ADDRESS)

//--------------------5011 BMU wrapper IP Register Define--------------------
#define BMU_REGISTER_BASE					   (BMU_REGISTER_ADDRESS)

#define r32_BMU                                             ((volatile U32 *) BMU_REGISTER_BASE)
#define r16_BMU                                             ((volatile U16 *) BMU_REGISTER_BASE)
#define r8_BMU                                             	((volatile U8 *)  BMU_REGISTER_BASE)

#define BMU_LB_N_HEAD_TAIL_OFF_BIT							(2)

#define BMUW_LB_N_HEAD(N)                                   ((0x080 + (((U32)(N)) << BMU_LB_N_HEAD_TAIL_OFF_BIT)) >> 1)
#define BMU_GET_LB_N_HEAD(N)                            (r16_BMU[BMUW_LB_N_HEAD(N)] & BITMSK(10,0))
#define BMUW_LB_N_TAIL(N)                                   ((0x082 + (((U32)(N)) << BMU_LB_N_HEAD_TAIL_OFF_BIT)) >> 1)
#define BMU_GET_LB_N_TAIL(N)                            (r16_BMU[BMUW_LB_N_TAIL(N)] & BITMSK(10,0))

#endif /* _SHR_HAL_BMU_REG_H_ */
