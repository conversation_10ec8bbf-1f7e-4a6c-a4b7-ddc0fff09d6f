/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2017, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  err_hdl_fpl_sb_retry_api.h                                          */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#ifndef _ERR_HDL_FPL_SB_RETRY_API_H_
#define _ERR_HDL_FPL_SB_RETRY_API_H_
/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */

#define  RETRY_SB_SLC_SB_EN		(FW_CATEGORY_FLASH == FLASH_BICS5TLC)
#define  RETRY_SB_TASK_IS_RETRY_TASK_CASE                                (0)
#define  RETRY_SB_TASK_IS_NORMAL_PAGE_DURING_RAIDECC_CASE           (1)
#define  RETRY_SB_TASK_IS_ERR_PAGE_AFTER_RAIDECC_CASE                   (2)

//SB Debug
#define RETRY_SB_SUBSTATE_NULL_DEBUG  		(0xFF)	//RETRY_SB_SUBSTATE_NULL = 0xFF from SoftBitSubStateEnum_t

#define RETRY_SB_ASSERT_CASE_SHARED_PAGE_OVER_RANGE	(BIT0)

/*
 * ---------------------------------------------------------------------------------------------------
 *  enum
 * ---------------------------------------------------------------------------------------------------
 */

/*
*  SOFTBIT STATE MACHINE
*  =====================
*/
#if(RETRY_SOFTBITT_FOR_SDK_EN==FALSE)
#if(TRUE == MICRON_FSP_EN)  //RETRY_MICRON
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)
typedef enum SoftBitStateEnum {
	RETRY_SB_STATE_INIT = 0,						// 0
	RETRY_SB_STATE_WAIT_QUEUE_IDLE,				// 1
	RETRY_SB_STATE_DET_COARSE_TUNE_LEVEL,						// 2
	RETRY_SB_STATE_SET_WLBYPASS,							//3
	RETRY_SB_STATE_MODIFY_RR_ADD_COARSE_TUNING_LEVEL,		// 4
	RETRY_SB_STATE_HB_WITH_AUTO_READ_CALIBRATION,				// 5
	RETRY_SB_STATE_SB_READ_WITH_OPTIMAL_READ_LEVEL,			// 6

	RETRY_SB_STATE_LOAD_DEFAULT_LLR_TABLE,						// 7
	RETRY_SB_STATE_SB_DECODE_WITH_DEFAULT_LLR,					// 8
	RETRY_SB_STATE_SB_DECODE_WITH_ADT_LLR,						// 9

	RETRY_SB_STATE_SB_DSP2,										// 10

	RETRY_SB_STATE_SB_WITH_OPT_READ_LEVEL__AFTER_RAIDECC,		// 11
	RETRY_SB_STATE_SET_DEFAULT_SBSBR_AND_CHK,							// 12
	RETRY_SB_STATE_SET_DEFAULT_FEATURE_AND_CHK,						// 13
	RETRY_SB_STATE_SB_RESET_FLH,								// 14

	RETRY_SB_STATE_CHECK_NEXT_STATE,							// 15
	RETRY_SB_STATE_DONE,										// 16
	RETRY_SB_STATE_TERMINATE_FAIL,								// 17
	RETRY_SB_STATE_TERMINATE_PASS,								// 18
	RETRY_SB_STATE_SCAN_ERR_FRAME,								// 19
	RETRY_SB_STATE_CHECK_ERR_LDPC_FRM,							// 20

	RETRY_SB_STATE_RECEIVE_CQ,									// 21

	RETRY_SB_STATE_READ_GOOD_2K_TO_BUF,							// 22

	//States for Micron Nicks Project
#if (IM_N48R)
	RETRY_SB_STATE_SB_READ_WITH_READ_RETRY_TABLE,

	RETRY_SB_STATE_HB_2K_RAW_READ_DECODE,
	RETRY_SB_STATE_SB_READ,
	RETRY_SB_STATE_LOAD_DEFAULT_LLR,
	RETRY_SB_STATE_SB_DECODE,
	RETRY_SB_STATE_HB_4K_READ_MT_DECODE,
	RETRY_SB_STATE_CR_TRIM_ENHANCE_TRIM,
	RETRY_SB_STATE_RESTORE_POR_TRIM,

	RETRY_SB_STATE_POSTCONDITION_FOR_STEPS,

#else
	RETRY_SB_STATE_SET_READ_RETRY_TABLE,
#endif

	RETRY_SB_STATE_SELECT_READ_RETRY_TABLE,
	RETRY_SB_STATE_SB_READ_WITH_READ_RETRY_TABLE_WITH_ARC,

	RETRY_SB_STATE_BACKIPSTATE_NON_STATE = 0xFF
} SoftBitStateEnum_t;

#else /* (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC) */
typedef enum SoftBitStateEnum {
	RETRY_SB_STATE_INIT = 0,						// 0
	RETRY_SB_STATE_WAIT_QUEUE_IDLE,				// 1
	RETRY_SB_STATE_DET_COARSE_TUNE_LEVEL,						// 2
	RETRY_SB_STATE_MODIFY_RR_ADD_COARSE_TUNING_LEVEL,		// 3
	RETRY_SB_STATE_HB_WITH_AUTO_READ_CALIBRATION,				// 4
	RETRY_SB_STATE_SB_READ_WITH_OPTIMAL_READ_LEVEL,			// 5

	RETRY_SB_STATE_LOAD_DEFAULT_LLR_TABLE,						// 6
	RETRY_SB_STATE_SB_DECODE_WITH_DEFAULT_LLR,					// 7
	RETRY_SB_STATE_SB_DECODE_WITH_ADT_LLR,						// 8

	RETRY_SB_STATE_SB_DSP2,										// 9

	RETRY_SB_STATE_SB_WITH_OPT_READ_LEVEL__AFTER_RAIDECC,		// 10

	RETRY_SB_STATE_SET_DEFAULT_FEATURE_AND_CHK,						// 11
	RETRY_SB_STATE_SB_RESET_FLH,									// 12

	RETRY_SB_STATE_CHECK_NEXT_STATE,								// 13
	RETRY_SB_STATE_DONE,											// 14
	RETRY_SB_STATE_TERMINATE_FAIL,								// 15
	RETRY_SB_STATE_TERMINATE_PASS,								// 16
	RETRY_SB_STATE_SCAN_ERR_FRAME,								// 17
	RETRY_SB_STATE_CHECK_ERR_LDPC_FRM,							// 18

	RETRY_SB_STATE_RECEIVE_CQ,									// 19

	RETRY_SB_STATE_READ_GOOD_2K_TO_BUF,							// 20

	//States for Micron Nicks Project
	RETRY_SB_STATE_SB_READ_WITH_READ_RETRY_TABLE,
	RETRY_SB_STATE_SELECT_READ_RETRY_TABLE,
	RETRY_SB_STATE_SB_READ_WITH_READ_RETRY_TABLE_WITH_ARC,

	//States for Micron Nicks B27B SB Support All PageType
	RETRY_SB_STATE_SB_SET_WORDLINE_BYPASS,
	RETRY_SB_STATE_SB_RESET_WORDLINE_BYPASS,

	RETRY_SB_STATE_BACKIPSTATE_NON_STATE = 0xFF
} SoftBitStateEnum_t;
#endif /* (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC) */

typedef enum SBWORDLINETYPEEnum {
	SB_WORD_LINE_SLC = 0,
	SB_WORD_LINE_MLC,
	SB_WORD_LINE_OPEN_TLC,
	SB_WORD_LINE_1ST_PASS_OPEN_QLC,
	SB_WORD_LINE_TLC,
	SB_WORD_LINE_OPEN_QLC,
	SB_WORD_LINE_CLOSE_QLC
} SBWORDLINETYPEEnum_t;

#else /* (TRUE == MICRON_FSP_EN) */	//RETRY_MICRON
typedef enum SoftBitStateEnum {
	RETRY_SB_STATE_INIT = 0,						// 0
	RETRY_SB_STATE_WAIT_QUEUE_IDLE,				// 1
	RETRY_SB_STATE_DET_COARSE_TUNE_LEVEL,						// 2
	RETRY_SB_STATE_MODIFY_RR_ADD_COARSE_TUNING_LEVEL,		// 3
	RETRY_SB_STATE_HB_WITH_COARSE_TUNING_READ_LEVEL,			// 4

	RETRY_SB_STATE_SB_READ_WITH_COARSE_TUNING_READ_LEVEL,	// 5
	RETRY_SB_STATE_LOAD_CORNER_CASE_FIX_LLR,					// 6
	RETRY_SB_STATE_SB_DECODE_WITH_CORNER_CASE_FIX_LLR_WITH_COARSE_TUNING_READ_LEVEL, // 7
	RETRY_SB_STATE_CACULATE_LOAD_ADT_LLR,						// 8
	RETRY_SB_STATE_SB_DECODE_WITH_ADT_LLR_WITH_COARSE_TUNING_READ_LEVEL,	 // 9
	RETRY_SB_STATE_SELECT_RR_TABLE,								// 10
	RETRY_SB_STATE_HB_WITH_OPT_READ_LEVEL,						// 11

	RETRY_SB_STATE_FORRS_LOAD_PAIRTY,							// 12


	RETRY_SB_STATE_SB_READ_WITH_OPT_LEVEL,						// 13
	RETRY_SB_STATE_LOAD_CORNER_CASE_FIX_LLR_OPT_LEVEL,		// 14
	RETRY_SB_STATE_SB_DECODE_WITH_CORNER_CASE_FIX_LLR_WITH_OPT_LEVEL,	// 15
	RETRY_SB_STATE_CACULATE_LOAD_ADT_LLR_OPT_LEVEL,			// 16
	RETRY_SB_STATE_SB_DECODE_WITH_ADT_LLR_WITH_OPT_READ_LEVEL,			// 17

	RETRY_SB_STATE_SB_WITH_DSP2,									// 18
	RETRY_SB_STATE_LOAD_GENERAL_FIX_LLR_OPT_LEVEL,				// 19
	RETRY_SB_STATE_SB_DECODE_WITH_GENERAL_FIX_LLR_WITH_OPT_READ_LEVEL,		// 20

	RETRY_SB_STATE_CHECK_NEXT_STATE,								// 21
	RETRY_SB_STATE_DONE,											// 22
	RETRY_SB_STATE_TERMINATE_FAIL,								// 23
	RETRY_SB_STATE_TERMINATE_PASS,								// 24
	RETRY_SB_STATE_SCAN_ERR_FRAME,								// 25
	RETRY_SB_STATE_CHECK_ERR_LDPC_FRM,							// 26

	RETRY_SB_STATE_READ_GOOD_2K_TO_BUF,						// 27
	RETRY_SB_STATE_SB_WITH_OPT_READ_LEVEL__AFTER_RAIDECC,			// 28
	RETRY_SB_STATE_SB_RESET_FLH,									// 29
	RETRY_SB_STATE_RECEIVE_CQ,											// 30
	RETRY_SB_STATE_SET_DEFAULT_FEATURE_AND_CHK,								// 31
	RETRY_SB_STATE_POLL_READY,										// 32
	RETRY_SB_STATE_BACKUPSTATE_NON_STATE = 0xFF
} SoftBitStateEnum_t;
#endif /* (TRUE == MICRON_FSP_EN) */	//RETRY_MICRON
typedef enum NCSSBRAIDWriteInfoEnum {
	RETRY_SB_NCS_WRITE_SBRAID_XORALL	 = (0),
	RETRY_SB_NCS_WRITE_SBRAID_SB6,
	RETRY_SB_NCS_WRITE_SBRAID_LLR,
	RETRY_SB_NCS_WRITE_SBRAID_HW_SETTING,
	RETRY_SB_NCS_WRITE_SBRAID_DSP_DISTRIBUTION,

	RETRY_SB_NCS_WRITE_SBRAID_DECODE_RESULT
} NCSSBRAIDWriteInfoEnum_t;

#else /* (RETRY_SOFTBITT_FOR_SDK_EN==FALSE) */

typedef enum SoftBitStateEnum {
	RETRY_SB_STATE_INIT = (0),
	RETRY_SB_STATE_DONE = (13),
	RETRY_SB_STATE_SB_RESET_FLH = (20),
	RETRY_SB_STATE_SET_DEFAULT_FEATURE_AND_CHK = (31)
} SoftBitStateEnum_t;
#if(TRUE == MICRON_FSP_EN)  //RETRY_MICRON
typedef enum SBWORDLINETYPEEnum {
	SB_WORD_LINE_SLC = 0,
	SB_WORD_LINE_MLC,
	SB_WORD_LINE_TLC,
	SB_WORD_LINE_OPEN_QLC,
	SB_WORD_LINE_CLOSE_QLC
} SBWORDLINETYPEEnum_t;
#endif /* (TRUE == MICRON_FSP_EN) */	//RETRY_MICRON
#endif  /* (RETRY_SOFTBITT_FOR_SDK_EN==FALSE) */

/*
 * ---------------------------------------------------------------------------------------------------
 *  data types
 * ---------------------------------------------------------------------------------------------------
 */
#if(RETRY_SOFTBITT_FOR_SDK_EN==FALSE)
#if(TRUE == MICRON_FSP_EN)  //RETRY_MICRON
typedef struct {
	U8 ubSharePageType;
	U32 ulSharePageFSA;
} NextWordLineSharedPage_t;

#endif	/* (TRUE == MICRON_FSP_EN) */	//RETRY_MICRON
#endif	/*(RETRY_SOFTBITT_FOR_SDK_EN==FALSE)*/

typedef struct RetrySBTask_struct                RETRY_SB_TASK_STRUCT,           *RETRY_SB_TASK_STRUCT_PTR;

struct RetrySBTask_struct {
	/*
	 *  GLOBAL INIT VARIABLES
	 */
#if(RETRY_SOFTBITT_FOR_SDK_EN==FALSE)
#if(TRUE == MICRON_FSP_EN)  //RETRY_MICRON
	///---------------------state ctrl------------------------------
	U8   ubState;                                             // current active state, use this to control the main state of softbit retry
	U8   ubPrevState;
	U8   ubBackUpedState;
	U8   ubSubstate;
	U8	ubBackUpedSubstate;
	U8   ubThirdstate;

	U8   ubFourthstate;
	U8   ubSubstate_After_Set_Default_Feature;
	U8   ubThirdstate_After_Set_Default_Feature;
	///--------------------------frm and bitmap ctrl-------------------------
	U8   ubErr_frm;

	U8   ubCurr_frm_idx;
	U8   ubCurrentLDPCFrameIdx;  /// 0:former  1:latter
	U16 uwLDPCCurrentErrMap;
	///--------------------------sb flow variablel-------------------------

	U8   ubDsp_en;
	U32 ulCount_one[7];  /// former 3 for normal, latter 4 for DEBUG
	U32 ulBackupDummyRead;

	U8   ubllr_index;
	U8   ubllr_index_MAX;
	U8   ubdecode_mode;

	///-----------------------simple information from PCA----------------------------
	U8   ubChannel;
	U8 	ubBank;
	U8   ublun;
	U8	ubPageType;
	U8	ubWLType;
	///------------------------- PCA   --------------------------
	U32 ulFSA_ori;
	U32 ulFSA_Align;
	U32 ulVCA;
	///----------------------- Address  ----------------------------
	U32 ulbackup_addr_base[FRAMES_PER_PAGE];
	U32 ulbackup_addr;
	U32 ulSpareAddr_base[FRAMES_PER_PAGE];
	U32 ulSpareAddr;

	U32 ultmp_addr;
	U32 ultmp_SpareAddr;
	U32 ulFPU_offsetAddr;

	FlhMT_t  MTTemplate;
	FlhMT_t  MT;
	U8  ubMTFailCnt;
	U8  ubMTCheckFeatureFailCnt;
	U8  ubIsAfterRaidECC;
	U8  ubUseQorOrNot;
	U8  ubSBDecodeFlowLoopBreakFlag;
	U8  ubCaseABCnt;
	U8  ubCaseABCntDefault;
	//Debug Info
	U8 ubTerminateState;
	U8 ubTerminateSubstate;
	U8 ubDeltaTablePassReadLevelOffset;
	U8 ubDeltaTableCoarseTuningReadLevelOffset;
	U8 ubCoarseTuningReadLevel;
	U32 ul16KCntOneInSBCoarseTuning;
	U8 ubDebug;
	U8 ubNeedToGetSB0Flag;
#if RETRY_HARDBIT_FOR_SDK_EN
#else /* RETRY_HARDBIT_FOR_SDK_EN */
	U8  ubTrappingSetCnt;
#endif /* RETRY_HARDBIT_FOR_SDK_EN */
	U8 ubARCRoundCnt;
	U32 ulShareLowerFSA_Align;
	U32 ulShareUpperFSA_Align;
	U32 ulShareExtraFSA_Align;
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)
	U32 ulShareeXtraFSA_Align;
#endif /* (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC) */
#if (IM_N48R)
	U8 ubTotalFeatureAddr;
	U8 ubCurrentFeatureAddr;
#else /*(IM_N48R)*/
	U8 ubSecondFeatureAddr;
#endif /*(IM_N48R)*/
	U8 ubTempPageType;				//Temp Page type for set feature, ARC, read offset
	NextWordLineSharedPage_t NextWordLineSharePage[4];	// 2 for MICRON TLC(Upper/eXtra, Lower/eXtra, Lower/Upper), for set feature, ARC, read offset in DSP2
	U8 ubSpecificMode;				//Mode to decide using specific retry table or read offset
	U8 ubSpecificRetryTable;			//Temp Retry table when every round of spceific retry table mode
	U8 ubSpecificRetryTableRound;	//Specific Retry table round cnt
	U8 ubSpecificReadOffsetRound;	//Specific Read offset round cnt
	U8 ubGetFeatureResult[4];
	U8 ubSetDefaultFeaturePlaneCnt;
	U8 ubResetDie;
	U8 ubDSPEnginePageType;
	U8 ubDoCoarseTuningFlag;
	U8 ubSBReadDecodeWithARC;		//Phison Flow temp use the same SB read flow with Micron Nicks
	U8 ubErrorRecoverySBStep;		//Phison Flow temp use the same SB read flow with Micron Nicks
	U8 ubBlkRefresh;					//Phison Flow temp use the same SB read flow with Micron Nicks
#if(TRUE == RETRY_MICRON_NICKS)
#if (TRUE == RETRY_MICRON_NICKS_ALL_PAGE_TYPE_SB_SUPPORT)
#if (IM_N28)
	U8 ubQLC1stPassFlowDone;
#endif /* (IM_N28) */
#if (IM_N48R)
	U8 ubIsFromTurboRain;
#endif /*(IM_N48R)*/
#endif	/*(TRUE == RETRY_MICRON_NICKS_ALL_PAGE_TYPE_SB_SUPPORT)*/
#endif	/*(TRUE == RETRY_MICRON_NICKS)*/
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)
	U8 ubDeltaSetFeatureCnt;
	U8 ubDeltaSetFeatureToDoCnt;
#endif /* (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC) */
	U8 ubTLCCloseFlowDone;
	U8 ubWordLineBypassRecord;
	U8 ubAssertCase;

	//For SB Flow Stage/Step, for Micron Nicks_2
#if (IM_N48R)
	U8 ubCurrentStage;
	U8 ubCurrentStep;
	U8 ub7thAddr;
	U8 ubSBSBRSecondRound;
	U8 ubCBCValue;
	U8 ubIsEnhance;
	U8 ubPreviousStage;
	U8 ubPreviousStep;
	U8 ubCRSetting;
	U8 ubSBRead1H1Sor1H2S;
#if (ERROR_HANDLE_VALIDATION)
	U16 uwWLgroup;
#endif /* (ERROR_HANDLE_VALIDATION) */
#endif /*(IM_N48R)*/
#else	/* (TRUE == MICRON_FSP_EN) */	//RETRY_MICRON
	///---------------------state ctrl------------------------------
	U8   ubState;                                             // current active state, use this to control the main state of softbit retry
	U8   ubPrevState;
	U8   ubBackUpedState;
	U8   ubSubstate;
	U8   ubThirdstate;

	U8   ubFourthstate;
	U8   ubSubstate_After_Set_Default_Feature;
	U8   ubThirdstate_After_Set_Default_Feature;
	///--------------------------frm and bitmap ctrl-------------------------
	U8   ubErr_frm;

	U8   ubCurr_frm_idx;
	U8   ubCurrentLDPCFrameIdx;  /// 0:former  1:latter
	U16 uwLDPCCurrentErrMap;
	///--------------------------sb flow variablel-------------------------

	U8   ubDsp_en;
	U32 ulCount_one[7];  /// former 3 for normal, latter 4 for DEBUG
	U32 ulBackupDummyRead;

	U8   ubllr_index;
	U8   ubllr_index_MAX;
	U8   ubdecode_mode;

	///-----------------------simple information from PCA----------------------------
	U8	 ubPlane;
	U8   ubChannel;
	U8   ublun;
	U8	 ubGlobalDie;
	U8	 ubBank;
	U8   ubPage_type;

	///------------------------- PCA   --------------------------
	U32 ulPCA_ori;
	U32 ulPCA_Align;
	U32 ulVCA;
	///----------------------- Address  ----------------------------
	U32 ulbackup_addr_base[FRAMES_PER_PAGE];
	U32 ulbackup_addr;
	U32 ulSpareAddr_base[FRAMES_PER_PAGE];
	U32 ulSpareAddr;

	U32 ultmp_addr;
	U32 ultmp_SpareAddr;
	U32 ulFPU_offsetAddr;

	FlhMT_t  MTTemplate;
	FlhMT_t  MT;
	U8  ubMTFailCnt;
	U8  ubMTCheckFeatureFailCnt;
	U8  ubIsAfterRaidECC;
	U8  ubUseQorOrNot;
	U8  ubSBDecodeFlowLoopBreakFlag;
	U8  ubCaseABCnt;
	U8  ubCaseABCntDefault;
	U8  ubSetDefaultFeaturePlaneCnt;
	//Debug Info
	U8 ubTerminateState;
	U8 ubTerminateSubstate;
	U8 ubDeltaTablePassReadLevelOffset;
	U8 ubDeltaTableCoarseTuningReadLevelOffset;
	U8 ubCoarseTuningReadLevel;
	U32 ul16KCntOneInSBCoarseTuning;
	U8 ubDebug;
	U8 ubSLCMode;
#if RETRY_SOFTBITT_FOR_SDK_EN
#else /* RETRY_SOFTBITT_FOR_SDK_EN */
	U8  ubTrappingSetCnt;
#endif /* RETRY_SOFTBITT_FOR_SDK_EN */
	U8 ubAssertCase;
	U8 ubResetDie;
#endif /* (TRUE == MICRON_FSP_EN) */	//RETRY_MICRON
#else /* (RETRY_SOFTBITT_FOR_SDK_EN==FALSE) */
	///---------------------state ctrl------------------------------
	U8   ubState;                                             // current active state, use this to control the main state of softbit retry
	U8 ubSubstate;
	U8  ubSBDecodeFlowLoopBreakFlag;
	///--------------------------frm and bitmap ctrl-------------------------
	U8 ubChannel;
	U8   ubErr_frm;
	U32 ulPCA_ori;
	U32 ulPCA_Align;
	U32 ulVCA;
	U8   ubUseQorOrNot;
	U32 ulbackup_addr_base[FRAMES_PER_PAGE];
	U32 ulbackup_addr;
	U32 ulSpareAddr_base[FRAMES_PER_PAGE];
	U32 ulSpareAddr;
	U32 ultmp_addr;
	U32 ultmp_SpareAddr;
	U8  ubMTFailCnt;
	U8  ubMTCheckFeatureFailCnt;
	U8  ubCaseABCnt;
	U8  ubCaseABCntDefault;
	//Debug Info
	U8 ubTerminateState;
	U8 ubTerminateSubstate;
	U8  ubIsAfterRaidECC;
	U8  ubBackUpedState;
	FlhMT_t  MTTemplate;
#endif  /* (RETRY_SOFTBITT_FOR_SDK_EN==FALSE) */
};

/*
 * ---------------------------------------------------------------------------------------------------
 *   extern global variables
 * ---------------------------------------------------------------------------------------------------
 */
extern RETRY_SB_TASK_STRUCT gSBTask;

/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */
AOM_RETRY_SB void SBRetryMain(void);
//NCS Compare
AOM_RETRY_SB void RetrySBNCSSB6Compare(U32 ulBufAddr, U32 ulSpareAddr);
AOM_RETRY_RS void RetrySBNCSRAIDCompareXORAll(FlhMT_t *pMTTemplate, U8 ubBufIdx, U8 ubIdx);
#endif  /* _ERR_HDL_FPL_SB_RETRY_API_H_ */
