/*
 *	buffer_queue.c
 */
/** @file buffer_queue.c
 *  @brief
 *
 *
 *  <AUTHOR>  @bug No know bugs.
 */
#include "typedef.h"
#include "queue/buffer_queue.h"


BufferQueueInfo_t	gBQI;

void BufferQueueInit()
{
	U8 ubBQIndex;
	gBQI.ubLinkNum = 0;
	gBQI.ubWLinkNum = 0;
	gBQI.BufferQueue[0].ubNext = 1;
	gBQI.BufferQueue[0].ubPrevious = BQ_NUM - 1;

	for (ubBQIndex = 1; ubBQIndex < (BQ_NUM - 1); ubBQIndex++) {
		gBQI.BufferQueue[ubBQIndex].ubNext = ubBQIndex + 1;
		gBQI.BufferQueue[ubBQIndex].ubPrevious = ubBQIndex - 1;
	}

	gBQI.BufferQueue[BQ_NUM - 1].ubNext = 0;
	gBQI.BufferQueue[BQ_NUM - 1].ubPrevious = BQ_NUM - 2;
	gBQI.ubLinkLast = BQ_NUM - 1;
	gBQI.ubLinkLastUse = gBQI.ubLinkLast;
	gBQI.ubLinkFirst = gBQI.BufferQueue[gBQI.ubLinkLastUse].ubNext;
}
