/**************************************************************************/
/*                                                                        */
/*           Copyright (c) 2000-2017 by Phison Electronics Corp.          */
/*                                                                        */
/**************************************************************************/

/**************************************************************************/
/*                                                                        */
/*  HAL UART DEFINITION                                    RELEASE        */
/*                                                                        */
/*    shr_hal_nvme_reg.h                                    GNU C         */
/*                                                           X.X          */
/*  AUTHOR                                                                */
/*                                                                        */
/*                                                                        */
/*  DESCRIPTION                                                           */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/*  RELEASE HISTORY                                                       */
/*                                                                        */
/*    DATE              NAME                      DESCRIPTION             */
/*                                                                        */
/*                                                                        */
/**************************************************************************/
#if (NVME == HOST_MODE)
#ifndef _SHR_HAL_NVME_REG_H_
#define _SHR_HAL_NVME_REG_H_

// goodid
#if 0 //merge@@
#include "misc/shr_datatype.h"
#else
#include "common/typedef.h"
#include "hal/nvme/nvme_reg_5013.h"

#define NVME_LAYER_OFFSET                      (0x20000)
#define NVME_REGISTER_ADDRESS (NVME_REG_ADDRESS+NVME_LAYER_OFFSET)
#endif

#define r8_NVME                             ((REG8 *)NVME_REGISTER_ADDRESS) //0x00CE2000
#define r16_NVME                            ((REG16 *)NVME_REGISTER_ADDRESS)
#define r32_NVME                            ((REG32 *)NVME_REGISTER_ADDRESS)
#define r64_NVME                            ((REG64 *)NVME_REGISTER_ADDRESS)


/*======================== NVME Layer Status ================================*/
#define INT_CE_CHG                      	(NL_INT_CE_CHG_BIT)
#define INT_SHN_CHG                     	(NL_INT_SHN_CHG_BIT)
#define CE_ST                           	(NL_CE_ST_BIT)
#define INT_SHN_ST1                         BIT3	//NL_SHN_ST[0]
#define INT_SHN_ST2                         BIT4	//NL_SHN_ST[1]
#define INT_DB_WP                       	(NL_INT_DB_WP_BIT)
#define INT_NSSR                        	(NL_INT_NSSR_BIT)
#define INT_BPR                         	(NL_INT_BPR_BIT)
#define NL_GET_CE_STS()							(r8_NVME[R8_NVME_NL_STS])
#define NL_CLR_CE_STS(N)						(r8_NVME[R8_NVME_NL_STS] = N)

#define NL_CHK_CE_STS()                         (r32_NVME[R32_NVME_NL_STS] & NL_CE_ST_BIT)
#define NL_CHK_CE_CHG_STS()                     (r32_NVME[R32_NVME_NL_STS] & NL_INT_CE_CHG_BIT)
#define NL_CLR_CE_CHG_STS()                     (r32_NVME[R32_NVME_NL_STS] = NL_INT_CE_CHG_BIT)
#define NL_CHK_SHN_CHG_STS()                    (r32_NVME[R32_NVME_NL_STS] & NL_INT_SHN_CHG_BIT)
#define NL_CLR_SHN_CHG_STS()                    (r32_NVME[R32_NVME_NL_STS] = NL_INT_SHN_CHG_BIT)
#define NL_CLR_CE_STS_CHG_STS()                 (r32_NVME[R32_NVME_NL_STS] = (NL_INT_CE_CHG_BIT | NL_CE_ST_BIT))
#define NL_CLR_SHN_STS()                        (r32_NVME[R32_NVME_NL_STS] = (NL_SHN_ST_MASK << NL_SHN_ST_SHIFT))

#define NL_CHK_DB_WP_STS()                    	(r32_NVME[R32_NVME_NL_STS] & NL_INT_DB_WP_BIT)
#define NL_CLR_DB_WP_STS()                    	(r32_NVME[R32_NVME_NL_STS] = NL_INT_DB_WP_BIT)
#define NL_CHK_NSSR_STS()                    	(r32_NVME[R32_NVME_NL_STS] & NL_INT_NSSR_BIT)
#define NL_CLR_NSSR_STS()                    	(r32_NVME[R32_NVME_NL_STS] = NL_INT_NSSR_BIT)
#define NL_CHK_BPR_STS()                        (r32_NVME[R32_NVME_NL_STS] & NL_INT_BPR_BIT)
#define NL_CLR_BPR_STS()                        (r32_NVME[R32_NVME_NL_STS] = NL_INT_BPR_BIT)

#define NL_CHK_SHN_ST(N)						((N & BITMSK(2,3)) >> 3 )

#define INT_W_ABN_ADDR                  	(NL_INT_W_ABN_ADDR_BIT)
#define INT_HST_W_DERR                      (NL_INT_HST_W_DERR_BIT)
#define INT_CMD_R_PERR                      (NL_INT_CMD_R_PERR_BIT)
#define INT_CMD_R_RERR                      (NL_INT_CMD_R_RERR_BIT)
#define INT_CPL_WERR                        (NL_INT_CPL_WERR_BIT)
#define INT_MSIX_WERR                       (NL_INT_MSIX_WERR_BIT)
#define SRAM_PAR_ERR                        (NL_INT_SRAM_PAR_ERR_BIT)

#define GET_NL_AXI_ESTS()                   (r32_NVME[R32_NVME_NL_AXI_ESTS])
#define CLR_NL_AXI_ESTS(x)                   (r32_NVME[R32_NVME_NL_AXI_ESTS]=x)

#define GET_NL_INVLD_SQDB_VAL()              (r16_NVME[R16_NL_INVLD_SQDB_VAL])
#define CLR_NL_INVLD_SQDB_VAL(x)             (r16_NVME[R16_NL_INVLD_SQDB_VAL]=x)
#define GET_NL_INVLD_CQDB_VAL()              (r16_NVME[R16_NL_INVLD_CQDB_VAL])
#define CLR_NL_INVLD_CQDB_VAL(x)             (r16_NVME[R16_NL_INVLD_CQDB_VAL]=x)

#define GET_NL_INVLD_SQDB_REG()              (r16_NVME[R16_NL_INVLD_SQDB_REG])
#define CLR_NL_INVLD_SQDB_REG(x)             (r16_NVME[R16_NL_INVLD_SQDB_REG]=x)
#define GET_NL_INVLD_CQDB_REG()              (r16_NVME[R16_NL_INVLD_CQDB_REG])
#define CLR_NL_INVLD_CQDB_REG(x)             (r16_NVME[R16_NL_INVLD_CQDB_REG]=x)


#define NL_INT_EN_ALL                       (0xFFFFFFFF)
#define M_SET_NL_INT_EN()                     (r32_NVME[R32_NVME_NL_INT_EN] = NL_INT_EN_ALL)

#define NL_GET_ABN_ADDR()              		(r64_NVME[R64_NVME_NL_ABN_ADDR] >> 0)
#define NL_SRAM_GET_ERR_ADDR()              (r8_NVME[R8_NVME_NL_SRAM_ERR_ADDR] & NL_SRAM_ERR_ADDR_MASK)

/*====================== END: NVME Layer Status =============================*/


/*======================== NVME Layer Control ================================*/
#define NL_CTRL_SET_FW_RST()                (r32_NVME[R32_NVME_NL_CTRL] |= NL_FW_RST_BIT)
#define NL_CTRL_GET_FW_RST()                (r32_NVME[R32_NVME_NL_CTRL] & NL_FW_RST_BIT)
#define NL_CTRL_SET_SRAM_PAR_CHK_EN()       (r32_NVME[R32_NVME_NL_CTRL] |= NL_SRAM_PAR_CHK_EN_BIT)

#define NL_ARB_ADDR                         (r32_NVME[R32_NVME_NL_ARB])
#define NL_ARB_CLR_WGT_ALL()                (r32_NVME[R32_NVME_NL_ARB] &=  ~(NL_WGT_ALL_MASK << NL_WGT_ALL_SHIFT))
#define NL_ARB_SET_WGT_LO(x)				(r8_NVME[R8_NL_ARB_WGT_LO]  = (x & BITMSK(8,0)))
#define NL_ARB_GET_WGT_LO()					(r8_NVME[R8_NL_ARB_WGT_LO])
#define NL_ARB_SET_WGT_MED(x)				(r8_NVME[R8_NL_ARB_WGT_MED] = (x & BITMSK(8,0)))
#define NL_ARB_GET_WGT_MED()				(r8_NVME[R8_NL_ARB_WGT_MED])
#define NL_ARB_SET_WGT_HI(x)				(r8_NVME[R8_NL_ARB_WGT_HI]  = (x & BITMSK(8,0)))
#define NL_ARB_GET_WGT_HI()					(r8_NVME[R8_NL_ARB_WGT_HI])

#define NL_ARB_SET_BURST(x)					(r8_NVME[R8_NVME_NL_ARB] |= ((x & NL_ARB_BURST_MASK) << NL_ARB_BURST_SHIFT))
#define NL_ARB_GET_BURST()					((r8_NVME[R8_NVME_NL_ARB] & (NL_ARB_BURST_MASK << NL_ARB_BURST_SHIFT)) >> NL_ARB_BURST_SHIFT)
#define NL_ARB_CLR_BURST()					(r8_NVME[R8_NVME_NL_ARB] &= ~((NL_ARB_BURST_MASK << NL_ARB_BURST_SHIFT)))
#define NL_ARB_SET_1ST_ADM()				(r8_NVME[R8_NVME_NL_ARB] |= NL_1ST_ADM_BIT)
#define NL_ARB_CLR_1ST_ADM()				(r8_NVME[R8_NVME_NL_ARB] &= ~(NL_1ST_ADM_BIT))
#define ADM_PRIO                    (NL_1ST_ADM_BIT)                         // Round Robin with Urgent Admin
//#define HAL_ARB_ADDR_API			(r32_NVME[R32_NVME_NL_ARB])
#define CMDBUFTHR(x)                (((U32)(x) & 0x0F) << 4)                 // Command Fetch Threshold
#define LPW(x)                      (((U32)(x) & 0xFF) << 8)                 // Low Priority Weight
#define MPW(x)                      (((U32)(x) & 0xFF) << 16)                // Medium Priority Weight
#define HPW(x)                      (((U32)(x) & 0xFF) << 24)                // High Priority Weight

#define NL_GET_IDLE_ST()					(r16_NVME[R16_NVME_NL_IDLE_ST] )
#define NL_CHK_CMDBUF_EMPTY()               (r16_NVME[R16_NVME_NL_IDLE_ST] & NL_CMDBUF_EMPTY_BIT)
#define NL_CHK_SRAM_IDLE()                  (r16_NVME[R16_NVME_NL_IDLE_ST] & NL_SRAM_IDLE_BIT)
#define NL_CHK_NO_OUTSTD_CMD()               (r16_NVME[R16_NVME_NL_IDLE_ST] & NFE_NO_OUTSTD_BIT)
#define NL_CHK_NO_CMD()      		         (r16_NVME[R16_NVME_NL_IDLE_ST] & NFE_NO_CMD_BIT)

#define NL_SQ_MASK_GET_ST() 				(r16_NVME[R16_NVME_NL_SQ_MASK_ST] & (NL_SQ_MASK_STS_MASK << NL_SQ_MASK_STS_SHIFT))
#define NL_SQx_MASK_GET_ST(x) 				((r16_NVME[R16_NVME_NL_SQ_MASK_ST] & BITMSK(1,x)) >> x )
#define  NL_CHK_ALLSQ_MASK()              	(r16_NVME[R16_NVME_NL_SQ_MASK_ST]  == 0x1FF)

#define  NL_SET_SQ_MASK(x)                (r16_NVME[R16_NVME_NL_SQ_MASK]  = (((U16)x & NL_SQ_MASK_STS_MASK) << NL_SQ_MASK_STS_SHIFT))
#define  NL_SET_ALLSQ_MASK()              (r16_NVME[R16_NVME_NL_SQ_MASK]  = (NL_SQ_MASK_STS_MASK << NL_SQ_MASK_STS_SHIFT))
#define  NL_CLR_ALLSQ_MASK()              (r16_NVME[R16_NVME_NL_SQ_MASK]  = 0x000)
#define NL_SET_SQx_MASK(qid) 			  (r16_NVME[R16_NVME_NL_SQ_MASK] |= (BITMSK(1,qid)))
#define NL_GET_SQx_MASK(qid) 			  (r16_NVME[R16_NVME_NL_SQ_MASK] & (BITMSK(1,qid))) == (BITMSK(1,qid))
#define NL_CLR_SQx_MASK(qid)              (r16_NVME[R16_NVME_NL_SQ_MASK] &=  ~(BITMSK(1,qid)))
#define NL_GET_ALLSQ_MASK()               ((r16_NVME[R16_NVME_NL_SQ_MASK] & (NL_SQ_MASK_STS_MASK << NL_SQ_MASK_STS_SHIFT)) >> NL_SQ_MASK_STS_SHIFT)

#define NL_WLL_GET_THR() 					(r32_NVME[R32_NVME_NL_WLL_THR])
#define NL_WLL_SET_THR(x) 					(r32_NVME[R32_NVME_NL_WLL_THR] = x)

#define NL_WLR_GET_THR() 					(r32_NVME[R32_NVME_NL_WLR_THR])
#define NL_WLR_SET_THR(x) 					(r32_NVME[R32_NVME_NL_WLR_THR] = x)

#define NL_IC_DIS_ADDR_W16                     (r16_NVME[R16_NVME_NL_INT_COA_DIS] )
#define NL_CLR_ALLIC_DIS()                	(r16_NVME[R16_NVME_NL_INT_COA_DIS]  = 0x000)
#define NL_CLR_IC_DIS(iv)                   (r16_NVME[R16_NVME_NL_INT_COA_DIS] &=  ~(BITMSK(1,iv)))
#define NL_GET_IC_DIS(x) 					((r16_NVME[R16_NVME_NL_INT_COA_DIS] & BITMSK(1,x)) >> x )  //IV0~IV8
#define NL_SET_IC_DIS(x,iv)	            	(r16_NVME[R16_NVME_NL_INT_COA_DIS] |= ((x & BITMSK(1,0)) << iv)  )

#define NL_INT_COA_UNIT(x)			(r16_NVME[R16_NVME_NL_INT_COA_CTRL_UNIT] = ((U16)x & NL_INT_COA_UNIT_MASK ))
#define NL_INT_COA_TIME(x)			(r8_NVME[R8_NVME_NL_INT_COA_CTRL_TIME] = ((U8)x & NL_INT_COA_TIME_MASK ))
#define NL_INT_COA_THR(x)			(r8_NVME[R8_NVME_NL_INT_COA_CTRL_THR] = ((U8)x & NL_INT_COA_THR_MASK ))
#define NL_GET_COA_TIME()			(r8_NVME[R8_NVME_NL_INT_COA_CTRL_TIME] & (NL_INT_COA_TIME_MASK))
#define NL_GET_COA_THR()			(r8_NVME[R8_NVME_NL_INT_COA_CTRL_THR] & (NL_INT_COA_THR_MASK))

#define SRAM_INIT                            (NL_SRAM_INIT_BIT)
#define NL_SET_SRAM_INIT() 	     	(r32_NVME[R32_NVME_NL_SRAM_CTRL] |= NL_SRAM_INIT_BIT)
#define NL_SRAM_CTRL_ADDR                   (r32_NVME[R32_NVME_NL_SRAM_CTRL])

#define  NL_WAIT_REG(reg,mask,val)           WAIT_REG(reg, mask,val)

/*====================== END: NVME Layer Control =============================*/


/*======================== NVME Controller Cap and Config ====================*/
//#define NLCAP_CQR                               BIT16
#define NLCAP_GET_CQR()                    ((r32_NVME[R32_NVME_NL_CAP0] & NL_CAP_CQR_BIT)>> NL_CAP_CQR_SHIFT)
#define NLCAP_SET_MQES(x)			(r32_NVME[R32_NVME_NL_CAP0] |= (x & NL_CAP_MQES_MASK) )
#define NLCAP_GET_MQES()			(r32_NVME[R32_NVME_NL_CAP0] & NL_CAP_MQES_MASK )
#define NLCAP_CLR_MQES()                  (r32_NVME[R32_NVME_NL_CAP0] &=  ~(NL_CAP_MQES_MASK))
#define NLCAP_SET_TO(x)				(r32_NVME[R32_NVME_NL_CAP0] |= (((U32)x & NL_CAP_TO_MASK) << NL_CAP_TO_SHIFT))
#define NLCAP_GET_TO()				((r32_NVME[R32_NVME_NL_CAP0] & (NL_CAP_TO_MASK << NL_CAP_TO_SHIFT)) >> NL_CAP_TO_SHIFT)
#define NLCAP_CLR_TO()                      (r32_NVME[R32_NVME_NL_CAP0] &=  ~((NL_CAP_TO_MASK << NL_CAP_TO_SHIFT)))
#define NLCAP_SET_AMS(x)			(r32_NVME[R32_NVME_NL_CAP0] |= (((U32)x & NL_CAP_AMS_MASK) << NL_CAP_AMS_SHIFT)  )
#define NLCAP_GET_AMS()			       ((r32_NVME[R32_NVME_NL_CAP0] & (NL_CAP_AMS_MASK << NL_CAP_AMS_SHIFT) ) >> NL_CAP_AMS_SHIFT)
#define NLCAP_CLR_AMS()                     (r32_NVME[R32_NVME_NL_CAP0] &=  ~((NL_CAP_AMS_MASK << NL_CAP_AMS_SHIFT)))

#define NLCAP_GET_NSSRS()                 ((r32_NVME[R32_NVME_NL_CAP1] & NL_CAP_NSSRS_BIT) >> NL_CAP_NSSRS_SHIFT)
#define NLCAP_CLR_NSSRS()                 (r32_NVME[R32_NVME_NL_CAP1] &=  ~(NL_CAP_NSSRS_BIT) )
#define NLCAP_GET_BPS                        ((r32_NVME[R32_NVME_NL_CAP1] & NL_CAP_BPS_BIT )>>NL_CAP_BPS_SHIFT)
#define NLCAP_CLR_BPS()                 (r32_NVME[R32_NVME_NL_CAP1] &=  ~(NL_CAP_BPS_BIT) )

#define NLCAP_SET_DSTRD(x)			(r32_NVME[R32_NVME_NL_CAP1] |= ((U32)x & NL_CAP_DSTRD_MASK) )
#define NLCAP_GET_DSTRD( )			(r32_NVME[R32_NVME_NL_CAP1] & NL_CAP_DSTRD_MASK )
#define NLCAP_CLR_DSTRD()                 (r32_NVME[R32_NVME_NL_CAP1] &=  ~(NL_CAP_DSTRD_MASK))
#define NLCAP_SET_CSS(x)			(r32_NVME[R32_NVME_NL_CAP1] |= (((U32)x & NL_CAP_CSS_MASK) << NL_CAP_CSS_SHIFT) )
#define NLCAP_GET_CSS()			       ((r32_NVME[R32_NVME_NL_CAP1] & (NL_CAP_CSS_MASK << NL_CAP_CSS_SHIFT)) >> NL_CAP_CSS_SHIFT )
#define NLCAP_CLR_CSS()                     (r32_NVME[R32_NVME_NL_CAP1] &=  ~((NL_CAP_CSS_MASK << NL_CAP_CSS_SHIFT)))
#define NLCAP_SET_MPSMIN(x)				(r32_NVME[R32_NVME_NL_CAP1] |= (((U32)x & NL_CAP_MPSMIN_MASK) << NL_CAP_MPSMIN_SHIFT) )
#define NLCAP_GET_MPSMIN()	    	       ((r32_NVME[R32_NVME_NL_CAP1] & (NL_CAP_MPSMIN_MASK << NL_CAP_MPSMIN_SHIFT) )>>NL_CAP_MPSMIN_SHIFT )
#define NLCAP_CLR_MPSMIN()               (r32_NVME[R32_NVME_NL_CAP1] &=  ~((NL_CAP_MPSMIN_MASK << NL_CAP_MPSMIN_SHIFT)))
#define NLCAP_SET_MPSMAX(x)				(r32_NVME[R32_NVME_NL_CAP1] |= (((U32)x & NL_CAP_MPSMAX_MASK) << NL_CAP_MPSMAX_SHIFT) )
#define NLCAP_GET_MPSMAX()		       ((r32_NVME[R32_NVME_NL_CAP1] & (NL_CAP_MPSMAX_MASK << NL_CAP_MPSMAX_SHIFT) ) >> NL_CAP_MPSMAX_SHIFT )
#define NLCAP_CLR_MPSMAX()              (r32_NVME[R32_NVME_NL_CAP1] &=  ~((NL_CAP_MPSMAX_MASK << NL_CAP_MPSMAX_SHIFT)))

#define NLVS_GET_VER()                      (r32_NVME[R32_NVME_NL_VS])
#define NLVS_SET_VER(x)                      (r32_NVME[R32_NVME_NL_VS] = x)

#define NLCC_CLR_CCEN()                     (r32_NVME[R32_NVME_NL_CC] &=  ~(NL_CC_EN_BIT))
#define NLCC_GET_CCEN()                     (r32_NVME[R32_NVME_NL_CC] & NL_CC_EN_BIT)
#define NLCC_SET_CCEN(N)                     (r32_NVME[R32_NVME_NL_CC] |= N)
#define NLCC_GET_CSS()				((r32_NVME[R32_NVME_NL_CC] & (NL_CC_CSS_MASK << NL_CC_CSS_SHIFT)) >> NL_CC_CSS_SHIFT )
#define NLCC_GET_MPS()				((r32_NVME[R32_NVME_NL_CC] & (NL_CC_MPS_MASK << NL_CC_MPS_SHIFT)) >> NL_CC_MPS_SHIFT )
#define NLCC_GET_AMS()				((r32_NVME[R32_NVME_NL_CC] & (NL_CC_AMS_MASK << NL_CC_AMS_SHIFT)) >> NL_CC_AMS_SHIFT )
#define NLCC_GET_SHN()				((r32_NVME[R32_NVME_NL_CC] & (NL_CC_SNH_MASK << NL_CC_SNH_SHIFT)) >> NL_CC_SNH_SHIFT )
#define NLCC_GET_IOSQES()			((r32_NVME[R32_NVME_NL_CC] & (NL_CC_IOSQES_MASK << NL_CC_IOSQES_SHIFT)) >> NL_CC_IOSQES_SHIFT )
#define NLCC_GET_IOCQES()			((r32_NVME[R32_NVME_NL_CC] & (NL_CC_IOCQES_MASK << NL_CC_IOCQES_SHIFT)) >> NL_CC_IOCQES_SHIFT )
#define	NLCC_GET_ALL()				(r32_NVME[R32_NVME_NL_CC])
#define NLCC_SET_ALL(x)				(r32_NVME[R32_NVME_NL_CC] = (x))

#define NLCSTS_GET_CSTS()           (r32_NVME[R32_NVME_NL_CSTS])
#define NLCSTS_GET_CFS()			((r32_NVME[R32_NVME_NL_CSTS] & NL_CSTS_CFS_BIT) >> NL_CSTS_CFS_SHIFT )
#define NLCSTS_SET_CFS(x)			(r32_NVME[R32_NVME_NL_CSTS] |= ((x & BITMSK(1,0)) << NL_CSTS_CFS_SHIFT) )
#define NLCSTS_CLR_CFS()			(r32_NVME[R32_NVME_NL_CSTS] &=  ~(NL_CSTS_CFS_BIT))
//
#define NLCSTS_GET_SHST()			((r32_NVME[R32_NVME_NL_CSTS] & (NL_CSTS_SHST_MASK << NL_CSTS_SHST_SHIFT)) >> NL_CSTS_SHST_SHIFT )
#define NLCSTS_SET_SHST(x)			do{\
                                        U32 ulCSTS = NLCSTS_GET_CSTS();\
                                        ulCSTS &= ~(NL_CSTS_SHST_MASK << NL_CSTS_SHST_SHIFT);\
                                        ulCSTS |= ((x & NL_CSTS_SHST_MASK) << NL_CSTS_SHST_SHIFT);\
                                        r32_NVME[R32_NVME_NL_CSTS] = ulCSTS;\
                                    }while(0)
#define NLCSTS_CLR_SHST()                  (r32_NVME[R32_NVME_NL_CSTS] &=  ~(NL_CSTS_SHST_MASK << NL_CSTS_SHST_SHIFT))
#define NLCSTS_SET_RDY()                  (r32_NVME[R32_NVME_NL_CSTS] |= NL_CSTS_RDY_BIT)
#define NLCSTS_CLR_RDY()                  (r32_NVME[NL_CSTS] &= (~NL_CSTS_RDY_BIT))
//#define NSSRO                                       BIT4
#define NLCSTS_GET_NSSRO()			((r32_NVME[R32_NVME_NL_CSTS] & NL_CSTS_NSSRO_BIT) >> NL_CSTS_NSSRO_SHIFT )
#define NLCSTS_SET_NSSRO(x)		(r32_NVME[R32_NVME_NL_CSTS] |= ((x & BITMSK(1,0)) << NL_CSTS_NSSRO_SHIFT) )
#define NLCSTS_CLR_NSSRO()			(r32_NVME[R32_NVME_NL_CSTS] &=  ~(NL_CSTS_NSSRO_BIT))
//#define PP                                             BIT5
#define NLCSTS_GET_PP()			       ((r32_NVME[R32_NVME_NL_CSTS] & NL_CSTS_PP_BIT) >> NL_CSTS_PP_SHIFT )
#define NLCSTS_SET_PP(x)			(r32_NVME[R32_NVME_NL_CSTS] |= ((x & BITMSK(1,0)) << NL_CSTS_PP_SHIFT) )
#define NLCSTS_CLR_PP()			       (r32_NVME[R32_NVME_NL_CSTS] &=  ~(NL_CSTS_PP_BIT))
#define SHST_PROCESS_OCCURRING 	(BIT0)
#define SHUTDOWN_PROCESS_COMPLETE 		(BIT1)
#define SHUTDOWN_RESERVED			(BIT2)

/*====================== END: NVME Controller Cap and Config =================*/


/*======================== NVME Layer Complete Information ======================*/
#define NLCPL_GET_STS()				(r32_NVME[R32_NVME_NL_CPL_STS])
#define NLCPL_CLR_STS()				(r32_NVME[R32_NVME_NL_CPL_STS] = 0)
#define NLCPL_SET_CID(x)			(r32_NVME[R32_NVME_NL_CPL_STS] |= (x & NL_CPL_CID_MASK))
#define NLCPL_SET_ST(x)				(r32_NVME[R32_NVME_NL_CPL_STS] |= (((U32)x & NL_CPL_ST_MASK) << NL_CPL_ST_SHIFT))
#define NLCPL_SET_PUSH()			(r32_NVME[R32_NVME_NL_CPL_STS] |= NL_CPL_PUSH_BIT)
#define NLCPL_GET_PUSH()            ((r32_NVME[R32_NVME_NL_CPL_STS] & NL_CPL_PUSH_BIT) >> NL_CPL_PUSH_SHIFT )
#define NLCPL_CHK_PUSH()			(r32_NVME[R32_NVME_NL_CPL_STS] & NL_CPL_PUSH_BIT)
#define NLCPL_PUSH()			(r32_NVME[R32_NVME_NL_CPL_STS] |= NL_CPL_PUSH_BIT)

#define NLCPL_CLR_SPC()				(r32_NVME[R32_NVME_NL_CPL_SPC] = 0)
#define NLCPL_SET_SPC(x)			(r32_NVME[R32_NVME_NL_CPL_SPC] = (x))

#define NLCPL_CLR_INFO()			 (r32_NVME[R32_NVME_NL_CPL_INFO] = 0 )
#define NLCPL_SET_NLB(x)           		(r32_NVME[R32_NVME_NL_CPL_INFO] |= (x & NL_CPL_NLB_MASK))
#define NLCPL_SET_LBA4K()           	(r32_NVME[R32_NVME_NL_CPL_INFO] |= NL_CPL_LBA4K_BIT)
#define NLCPL_SET_SQID(x)           	(r32_NVME[R32_NVME_NL_CPL_INFO] |= (((U32)x & NL_CPL_SQID_MASK) << NL_CPL_SQID_SHIFT))
#define NLCPL_SET_IOCMD()           	(r32_NVME[R32_NVME_NL_CPL_INFO] |= NL_CPL_IOCMD_BIT)


/*====================== END: NVME Layer Complete Information ===================*/


/*======================== NVME Layer Queue Information ======================*/



typedef union nvme_sq_info          		 SQINFO;
typedef union nvme_cq_info         		 CQINFO;

typedef volatile union nvme_sq_info         *SQINFO_PTR;      // total 0 ~ 8 QINFO Block
typedef volatile union nvme_cq_info         *CQINFO_PTR;      // total 0 ~ 8 QINFO Block


#define NL_QINFO_OFFSET                     (0x100) //SQ desp offset from NL REG..
#define NL_QINFO_OFFSET_W16                     (0x100 >> 1)
#define NL_QINFO_OFFSET_W64             (0x100 >> 3)
#define NL_QINFO_SIZE                       (0x40)
#define NL_QINFO_SIZE_W16                       (0x40 >> 1)
#define NL_QINFO_SIZE_W64                       (0x40 >> 3)
#define NL_QINFO_CQ_SIZE                 (0x20)
#define NL_QINFO_CQ_SIZE_W16                 (0x20 >> 1)
#define NL_QINFO_CQ_SIZE_W64         (0x20 >> 3)
#define NL_QINFO_CNT                        (8)

#define NL_QINFO_SQSIZE                     (8)
#define NL_QINFO_CQSIZE                     (8)

#define NL_MAX_QINFO_BLOCK_NUM				(9) // 1:Adm, 8:I/O

#define NL_GET_SQ_EN(SQID)		    (r8_NVME[NL_QINFO_OFFSET+((SQID) * NL_QINFO_SIZE)] & (NL_SQ_EN_BIT))
#define NL_GET_CQ_EN(CQID)		    (r8_NVME[NL_QINFO_OFFSET+((CQID) * NL_QINFO_SIZE)+NL_QINFO_CQ_SIZE] & (NL_CQ_EN_BIT))
//offset 8 byte to get NL_SQ_CQID
#define NL_GET_SQ_CQID(SQID)	    (r8_NVME[NL_QINFO_OFFSET+((SQID) * NL_QINFO_SIZE)+8] & (NL_SQ_CQID_MASK))
//#define gpNvmeQinfo                          ((QINFO_PTR)(NVME_REGISTER_ADDRESS + NL_QINFO_OFFSET))

#define NL_GET_SQ_BASE(SQID)	    (r64_NVME[NL_QINFO_OFFSET_W64+((SQID) * NL_QINFO_SIZE_W64)])
#define NL_GET_CQ_BASE(CQID)	    (r64_NVME[NL_QINFO_OFFSET_W64+((CQID) * NL_QINFO_SIZE_W64)+ NL_QINFO_CQ_SIZE_W64])


#define NL_GET_SQ_HEAD(SQID)      (r16_NVME[NL_QINFO_OFFSET_W16+((SQID) * NL_QINFO_SIZE_W16) + 10])
#define NL_GET_SQ_TAIL(SQID)        (r16_NVME[NL_QINFO_OFFSET_W16+((SQID) * NL_QINFO_SIZE_W16) + 12])
#define NL_GET_CQ_HEAD(CQID)     (r16_NVME[NL_QINFO_OFFSET_W16+((CQID) * NL_QINFO_SIZE_W16 + NL_QINFO_CQ_SIZE_W16) + 13])
#define NL_GET_CQ_TAIL(CQID)        (r16_NVME[NL_QINFO_OFFSET_W16+((CQID) * NL_QINFO_SIZE_W16 + NL_QINFO_CQ_SIZE_W16) + 12])

#define NL_GET_SQ_TAILHEAD(SQID)    (r64_NVME[NL_QINFO_OFFSET_W64+((SQID) * NL_QINFO_SIZE_W64) + 1])

#if VS_SIM_EN
#define NVME_PACKED
#else
#define NVME_PACKED  __attribute__((packed))
#endif

union nvme_sq_info {

	// SQ Descriptor //
	U32 ulDW[8];

	struct NVME_PACKED {
		U16 uwNlSqEn     : 1;
		U16 uwNlSqPri    : 2;    // SQ priority
		U16 uwResv0      : 9;
		U64 ullNlSqBase : 52;
		U16 uwNlSqCqId   : 4;
		U16 uwResv1     : 12;
		U16 uwNlSqBrc   : 16;
		U32 ulResv2         ;

		U32 ulNlSqWl        ;    // SQ workload
		U16 uwNlSqHead      ;
		U16 uwNlSqSize      ;
		U16 uwNlSqTail      ;
		U16 uwNlSqWhead     ;    // SQ working head
		U32 ulResv3         ;

	};
};



union nvme_cq_info {

	// CQ Descriptor //
	U32 ulDW[8];

	struct NVME_PACKED {

		U8  ubNlCqEn     : 1;
		U8  ubNlCqIe     : 1;
		U16 uwResv0     : 10;
		U64 ullNlCqBase : 52;
		U8  ubNlCqIv     : 4;
		U32 uwResv1     : 28;
		U32 ulResv2         ;

		U8  ubResv3      : 1;
		U8  ubNlCqFull   : 1;
		U8  ubNlCqPhtag  : 1;
		U32 uwResv4      : 29;

		U16 uwNlCqWtail     ;
		U16 uwNlCqSize      ;
		U16 uwNlCqTail      ;
		U16 uwNlCqHead      ;
		U32 ulResv5         ;
	};

};

/*
#define QPB_ENTRY_OFFSET                        (0x00000020 >> 2)
#define QPB_OFFSET_W32                          (0x00000100 >> 2)
#define 	NL_SQ_EN(QID,x)                     (r32_NVME[QPB_OFFSET_W32 + (QID) * QPB_ENTRY_OFFSET] |= (x & BITMSK(1,0))  )
#define 	SQ_GET_PRI()						((r32_NVME[NL_CSTS] & BITMSK(2,2)) >> 2 )
#define 	SQ_SET_PRI(QID,x)					(r32_NVME[QPB_OFFSET_W32 + (QID) * QPB_ENTRY_OFFSET] |= ((x & BITMSK(2,0)) << 1) )
#define     NLCSTS_CLR_SHST()                   	(r32_NVME[NL_CSTS] &=  ~(BITMSK(2,2)))
#define 	NSSRO                               	BIT4
#define 	PP                                  	BIT5


#define NVME_QPB_SQ_SET_EN(QID,x)  {}
#define NVME_QPB_SQ_SET_PRI(QID,x)  {}
#define NVME_QPB_SQ_SET_BASE(QID,x)  {}
#define NVME_QPB_SQ_SET_CQID(QID,x)  {}
#define NVME_QPB_SQ_SET_EN(QID,x)  {}




#define DB_ADD_WPIU(QID, x)        { __asm volatile("DSB"); r16_DB[WPIU_W16 + ((DB_CTRL_SIZE_PER_QUEUE * QID) >> 1)] = x; }
#define DB_ADD_RPIU(QID, x)        { __asm volatile("DSB"); r16_DB[RPIU_W16 + ((DB_CTRL_SIZE_PER_QUEUE * QID) >> 1)] = x; }
*/





/*====================== END: NVME Layer Queue Information ===================*/


/*=============================== NVME MSIX Table ===========================*/
typedef volatile struct nvme_msix_entry     *MSIXEntry_PTR;
#define NL_MSIX_OFFSET                      (0x400)
#define gpNvmeMsix                          ((MSIXEntry_PTR)(NVME_REGISTER_ADDRESS+NL_MSIX_OFFSET))


struct nvme_msix_entry {

	// MSIX Entry //
	union {
		U32 ulDW[4];

		struct NVME_PACKED {
			U64 ullMsgAddr      ;
			U32 ulMsgData       ;
			U32  ubMask       : 1;
			U32 ulResv0      : 31;
		} elem ;
	} MSIX_ENTRY;

};

//

/*============================= END: NVME MSIX Table ========================*/


#endif /* _SHR_REG_NVME_H_ */
#endif /* (NVME == HOST_MODE) */

