#ifndef _COP1_API_H_
#define _COP1_API_H_

#include "setup.h"
#include "symbol.h"
#include "typedef.h"
#include "mem.h"
#include "fw_vardef.h"
#include "aom/aom_api.h"
#include "db_mgr/fw_cmd_table.h"
#include "hal/pic/uart/uart_api.h"
#include "hal/cop1/cop1_cmd_type.h"
#include "hal/cop1/cop1_reg.h"
#include "hmb/hmb_api.h"

#if VS_SIM_EN
#include "sys_res.h"
EXTERN U8 gubST1InitDone;
#endif /* VS_SIM_EN */

#define COP1_NAMESPACE_MAX			(8)

#define TARGET_CPU0		(0)
#define TARGET_COP1		(3)

#define BIT_NOT_NEED_RET	(0)
#define BIT_NEED_RET		(1)

#define BIT_NOT_FINAL_CMD	(0)
#define BIT_FINAL_CMD		(1)

#define BIT_UPDATE_PMD		(0)
#define BIT_GC_PTE			(1)

#define ST3C_PMD			(0)
#define ST3C_PTE			(1)
#define ST3C_INIT			(2) //INIT: cache init status
#define ST3C_NOPB			(3) //NOPB: FW is borrowing cache now
//#define ST3C_PGD			(2)
//#define ST3C_GCPTE			(3)

#define BIT_SEARCH_ST3		(BIT0)
#define BIT_SEARCH_ST1		(BIT1)
#define BIT_SEARCH_WLB		(BIT2)
#define BIT_SEARCH_XZIP		(BIT3)

// ST3 CMD Result Error Bit
#define BIT_ERR_FLH	(BIT2)
#define BIT_ERR_AXI	(BIT1)
#define BIT_ERR_GC	(BIT0)

//for insert DSA/GCSA done bit
#define OPERATE_ALL_JOB_SHIFT	(0)
#define PARSING_END_SHIFT		(1)

//for Update PMD Result done bits
#define UPD_PMD_RESULT_DONE_BITS_ALL_DONE	(3)

//for Trim Conditional Result done bits
#define TRIM_COND_RESULT_DONE_BITS_ALL_DONE	(3)

//for InitCop1Reg Mode Select
#define	COP1_SRAM	(0)
#define COP1_HMB	(1)

#define COP1_DSA_NODE_NUM_PER_SLOT (1024)
#define COP1_DSA_NODE_NUM_PER_SLOT_LOG (10)
//============       API		==================

#define COP1_SH_NOT_FW_LOAD_TABLE		(0)
#define COP1_SH_FW_LOAD_TABLE			(1)
#define COP1_SH_LBIDSEL_NOT_LBIDSEL		(0)
#define COP1_SH_LOAD_TABLE_FOR_PMD		(0)
#define COP1_SH_LOAD_TABLE_FOR_PTE		(1)

#define COP1_RELEASE_SORTINGRULE_S2L		(0)
#define COP1_RELEASE_SORTINGRULE_L2S		(1)

#define COP1_MOVE_RULE_DONT_MOVE			(0x0)
#define COP1_MOVE_RULE_TO_JUNIOR			(0x1)
#define COP1_MOVE_RULE_TO_SENIOR			(0x2)

#define COP1_COP0_ATTR0						(0x0)
#define COP1_COP0_ATTR1						(0x1)
#define COP1_COP0_ATTR2						(0x2)
#define COP1_COP0_ATTR3						(0x3)

#define COP1_UNLOCK_DT_NOT_ADD_TO_FREE_POOL	(0x0)
#define COP1_UNLOCK_DT_ADD_TO_FREE_POOL		(0x1)

//************** ST1 **********************
#define COP1_ST1_REG_SIZE	(36)
#define COP1_ST1_REG_SIZE_IN_4BYTE	(COP1_ST1_REG_SIZE >> 2)
#define COP1_ST1_EGLCA_SQ_NUM	(2)
//************** ST3C **********************

//cop1 st3c remove_table cmd parameters
//type
#define COP1_ST3C_REMOVE_TBL_TYPE_EMPTY		(0)
#define COP1_ST3C_REMOVE_TBL_TYPE_FREE		(1)
#define COP1_ST3C_REMOVE_TBL_TYPE_JUNIOR	(2)
#define COP1_ST3C_REMOVE_TBL_TYPE_SENIOR	(3)
#define COP1_ST3C_REMOVE_TBL_TYPE_FW		(4)
//ignore fw
#define COP1_ST3C_REMOVE_TBL_FROM_FW		(0)
#define COP1_ST3C_REMOVE_TBL_IGNORE_FW		(1)

//all
#define COP1_ST3C_REMOVE_TBL_SINGLE			(0)
#define COP1_ST3C_REMOVE_TBL_ALL			(1)
//to head
#define COP1_ST3C_REMOVE_TBL_TO_TAIL		(0)
#define COP1_ST3C_REMOVE_TBL_TO_HEAD		(1)


//************** GC ************************
#define COP1_GC_DT_FLUSH_NOT_INIT			(0x0)
#define COP1_GC_DT_FLUSH_NEED_INIT			(0x1)

#define COP1_GC_CNT_UPD_ADD_VB1_BIT			BIT0
#define COP1_GC_CNT_UPD_MINUS_VB1_BIT		BIT1
#define COP1_GC_CNT_UPD_ADD_VB2_BIT			BIT2
#define COP1_GC_CNT_UPD_MINUS_VB2_BIT		BIT3
#define COP1_GC_CNT_UPD_VB_NO_USE 			(0xFFFF)

//************************************************************************************

#define COP1_ALL0_PCA_VALUE			(0xFFFFFFFD)
#define COP1_INVALID_PCA_MASK       (0xFFFFFFF0)
#define COP1_INVALID_PCA_VALUE      (0xFFFFFFFF)
#define COP1_INVALID_PCA_VALUE_PMD	(0x7FFFFFFF)
#define COP1_INVALID_LCA_VALUE      (0xFFFFFFFF)
#define INVALID_PCA_VALUE           (COP1_INVALID_PCA_VALUE)
#define INVALID_LCA_VALUE           (COP1_INVALID_LCA_VALUE)
#define COP1_ST3C_INVALID_PCA_VALUE      (0x7FFFFFFF)
#define COP1_PTE_PCA_TRIM_BIT	(BIT31)
#define COP1_PTE_INDEX_SHIFT        (10)
#define COP1_PTE_TBL_MASK                (0x3FF)
#define COP1_DEALLOCATE_PTE_PCA		(0xFFFFFFFF)

#define PTE_PCA_TRIM_BIT		(BIT31)

#define GCBLOCK_MAX_DIRTYLOG_CNT		(16 * 1024)
#define GCBLOCK_VC_SIZE			(16 * 1024)

#define SLC_POOL_NUM_BASE (0x80000000)
//************** ST3 ************************
#define COP1_PMDBMP_BYTE_PER_REG_SECTION		(64)
#define COP1_PMDNUM_PER_REG_SECTION				(512)
//**************************************

/*
* COP1 RAM:
* ---------------------------------------------------------------------------------------------------
*   COP1_RAM
* ---------------------------------------------------------------------------------------------------
*/
#if VS_SIM_EN
#define COP1_ST3_PGD_BASE					((U32)gSysResStruct.cop1_ram->cop1_st3_pgd)
#define COP1_CTRL_BASE						((U32)gSysResStruct.cop1_ram)
#define COP1_ST1_DSA_BANK0					((U32)gSysResStruct.cop1_ram->cop1_st1_dsa_bank0)
#define COP1_ST1_DSA_BANK1					((U32)gSysResStruct.cop1_ram->cop1_st1_dsa_bank0 + 0x28)
#define COP1_ST1_DSA_BANK2					((U32)gSysResStruct.cop1_ram->cop1_st1_dsa_bank0 + 0xB0)
#define COP1_ST1_DSA_BANK3					((U32)gSysResStruct.cop1_ram->cop1_st1_dsa_bank0 + 0x2B8)
#define COP1_ST1_DSA_BANK4					((U32)gSysResStruct.cop1_ram->cop1_st1_dsa_bank0 + 0xAC0)
#define COP1_ST1_DSA_BANK5					((U32)gSysResStruct.cop1_ram->cop1_st1_dsa_bank1)
#define COP1_ST3_PMD_UPDATE_LOG_BASE		((U32)gSysResStruct.cop1_ram->cop1_st3_pblog)
#else /* VS_SIM_EN */
#define COP1_CTRL_BASE						(0x0B200000)
#define COP1_ST3_PGD_BASE					(COP1_CTRL_BASE + 0x1D000)  //Used to temp restore FPU before IRAM init
#define COP1_CTRL_SIZE						(2 * 1024)
#define COP1_EG_FIFO_IN						(COP1_CTRL_BASE + 0x800)
#define COP1_GC_VCNT						(COP1_CTRL_BASE + 0x1800)
#define COP1_SH_MEMO						(COP1_CTRL_BASE + 0x1000)

#define COP1_ST1_DSA_BANK0					(COP1_CTRL_BASE + 0xA000)
#define COP1_ST1_DSA_BANK1					(COP1_CTRL_BASE + 0xA024)  // 0xA028)
#define COP1_ST1_DSA_BANK2					(COP1_CTRL_BASE + 0xA0A8)  //A0B0)
#define COP1_ST1_DSA_BANK3					(COP1_CTRL_BASE + 0xA2AC)  //A2B8)
#define COP1_ST1_DSA_BANK4					(COP1_CTRL_BASE + 0xA858)  // AAC0)
#define COP1_ST1_DSA_BANK5					(COP1_CTRL_BASE + 0xC85C)  // CAC8)
#define COP1_ST3_PMD_UPDATE_LOG_BASE		(COP1_CTRL_BASE + 0x36000)
#endif /* VS_SIM_EN */



#define COP1_ALIGN_32_BYTE_LOG		(5)
#define COP1_ST1_DSA_NODES			(8192) //2^13
#define COP1_ST1_DSA_NODES_DIV_1024	(8) //2^13
#define COP1_ST1_DSA_NODES_MASK		(BIT_MASK(13)) //2^13-1
#define COP1_ST1_DSA_ENTRY_SIZE		(8)
#define COP1_ST1_DSA_ENTRY_SIZE_LOG	(3)
#define COP1_ST1_DSA_SIZE			(COP1_ST1_DSA_ENTRY_SIZE * COP1_ST1_DSA_NODES)
#define COP1_ST1_CONTAINER_NODE_NUM	(1364)  //256 *4 + 64*4 + 16 * 4 + 4 * 4 + 1 * 4

#define EMPTY_LINK		(0)
#define FREE_LINK		(1)
#define JUNIOR_LINK		(2)
#define SENIOR_LINK		(3)
#define FW_LINK			(4)
#define TOTAL_LINK_NUM	(5)

/***************************** COP1 SRAM ***********************************/

#define COP1_SRAM_BASE_ADDR					(COP1_RAM_ADDRESS)
#if (PS5021_EN)
#define COP1_SRAM_SHMEM_SIZE				(0x400)
#define COP1_SRAM_ST1_CONTAINER_SIZE        (0x2B00)	//1376 * 8 // DSA_0 <=> DSA_1
#define COP1_SRAM_ST1_DSA_SIZE				(0x10000)
#define COP1_SRAM_ST1_DSA_RSV_SIZE			(0x100)		// HW Reserved
#define COP1_SRAM_ST3_LBUF_SIZE				(0x1000)
#define COP1_SRAM_ST3_CMDTBL_SIZE			(0x1000)
#define COP1_SRAM_ST3C_CTBL_SIZE			(0x900)
#define COP1_SRAM_ST3C_CMDTBL_SIZE			(0x400)
#define COP1_SRAM_ST3C_CMDTBL_RSV_SIZE		(0x300)		// HW Reserved
#define COP1_SRAM_VCTBL_SIZE				(0x8000)

#define COP1_SRAM_SHMEM_BASE_ADDR			(COP1_SRAM_BASE_ADDR)
#define COP1_SRAM_ST1_DSA_BASE_ADDR			(COP1_SRAM_SHMEM_BASE_ADDR + COP1_SRAM_SHMEM_SIZE)
#define COP1_SRAM_ST1_CONTAINER_BASE_ADDR	(COP1_SRAM_ST1_DSA_BASE_ADDR + COP1_SRAM_ST1_DSA_SIZE)
#define COP1_SRAM_ST3_LBUF_BASE_ADDR		(COP1_SRAM_ST1_CONTAINER_BASE_ADDR + COP1_SRAM_ST1_CONTAINER_SIZE + COP1_SRAM_ST1_DSA_RSV_SIZE)								//0x00CB3000
#define COP1_SRAM_ST3_CMDTBL_BASE_ADDR		(COP1_SRAM_ST3_LBUF_BASE_ADDR + COP1_SRAM_ST3_LBUF_SIZE)														//0x00CB4000
#define COP1_SRAM_ST3C_CTBL_BASE_ADDR		(COP1_SRAM_ST3_CMDTBL_BASE_ADDR + COP1_SRAM_ST3_CMDTBL_SIZE)													//0x00CB5000
#define COP1_SRAM_ST3C_CMDTBL_BASE_ADDR		(COP1_SRAM_ST3C_CTBL_BASE_ADDR + COP1_SRAM_ST3C_CTBL_SIZE)														//0x00CB5900
#define COP1_SRAM_VCTBL_BASE_ADDR			(COP1_SRAM_ST3C_CMDTBL_BASE_ADDR + COP1_SRAM_ST3C_CMDTBL_SIZE + COP1_SRAM_ST3C_CMDTBL_RSV_SIZE)					//0x00CB6000
// COP1 SRAM end at 0x00CBDFFF
#else /* (PS5021_EN) */
#define COP1_SRAM_SHMEM_SIZE				(0x400)
#define COP1_SRAM_ST1_DSA_SIZE				(0x13000)
#define COP1_SRAM_ST1_CONTAINER_SIZE          		(0x3000)//(0x4000)
#define COP1_SRAM_ST3_LBUF_SIZE				(0x1000)
#define COP1_SRAM_ST3_CMDTBL_SIZE			(0x1000)
#define COP1_SRAM_ST3C_CTBL_SIZE			(0x900)
#define COP1_SRAM_ST3C_CMDTBL_SIZE			(0x400)
#if PS5017_EN
#define COP1_SRAM_VCTBL_SIZE				(0x8000) //E17_porting_4TB
#else /* PS5017_EN */
#define COP1_SRAM_VCTBL_SIZE				(0x4000)
#endif /* PS5017_EN */
#define COP1_SRAM_SHMEM_BASE_ADDR			(COP1_SRAM_BASE_ADDR)															//0x002A1800
#define COP1_SRAM_ST1_DSA_BASE_ADDR			(COP1_SRAM_SHMEM_BASE_ADDR + COP1_SRAM_SHMEM_SIZE)								//0x002A1C00
#define COP1_SRAM_ST1_CONTAINER_BASE_ADDR	(COP1_SRAM_ST1_DSA_BASE_ADDR + COP1_ST1_DSA_NODES * COP1_ST1_DSA_ENTRY_SIZE)	//0x002B1C00
#define COP1_SRAM_ST3_LBUF_BASE_ADDR		(COP1_SRAM_ST1_DSA_BASE_ADDR + COP1_SRAM_ST1_DSA_SIZE)							//0x002B4C00
#define COP1_SRAM_ST3_CMDTBL_BASE_ADDR		(COP1_SRAM_ST3_LBUF_BASE_ADDR + COP1_SRAM_ST3_LBUF_SIZE)						//0x002B5C00
#define COP1_SRAM_ST3C_CTBL_BASE_ADDR		(COP1_SRAM_ST3_CMDTBL_BASE_ADDR + COP1_SRAM_ST3_CMDTBL_SIZE)					//0x002B6C00
#define COP1_SRAM_ST3C_CMDTBL_BASE_ADDR		(COP1_SRAM_ST3C_CTBL_BASE_ADDR + COP1_SRAM_ST3C_CTBL_SIZE)						//0x002B7500
#define COP1_SRAM_VCTBL_BASE_ADDR			(COP1_SRAM_ST3C_CMDTBL_BASE_ADDR + COP1_SRAM_ST3C_CMDTBL_SIZE)					//0x002B7900
#endif /* (PS5021_EN) */

/***********************************************************************/

//COP1_IDLE
#define M_COP1_ST3C_GET_IDLE()					(R32_COP1_ST3C[R32_COP1_ST3C_IDLE] & (SR_IDLE_MASK << SR_IDLE_SHIFT))
#define M_COP1_ST3_GET_IDLE()					(R32_COP1_ST3[R32_COP1_ST3_SR_ST3_IDLE] & (SR_ST3_IDLE_MASK << SR_ST3_IDLE_SHIFT))
#define M_COP1_SH_GET_IDLE()					(R32_COP1_SH[R32_COP1_SH_SR_CNT] & (IDLE_MASK << IDLE_SHIFT))
#define M_COP1_GC_GET_IDLE()					(R32_COP1_GC[R32_COP1_GC_SR_REG0] & (GC_IDLE_MASK << GC_IDLE_SHIFT))
#define M_COP1_ST1_GET_IDLE()					(R32_COP1_ST1[R32_COP1_ST1_IDLE] & (IDLE_LIST_MASK << IDLE_LIST_SHIFT))
#define M_COP1_BRG_GET_IDLE()					(R32_COP1_BRG[R32_COP1_BRG_BRG_ILDE] & (BRG_IDLE_MASK << BRG_IDLE_SHIFT))

#define M_COP1_ST3C_CHK_IDLE()					((R32_COP1_ST3C[R32_COP1_ST3C_IDLE] & (SR_IDLE_MASK << SR_IDLE_SHIFT)) == (SR_IDLE_MASK << SR_IDLE_SHIFT))
#define M_COP1_ST3_CHK_IDLE()					((R32_COP1_ST3[R32_COP1_ST3_SR_ST3_IDLE] & (SR_ST3_IDLE_MASK << SR_ST3_IDLE_SHIFT)) == (SR_ST3_IDLE_MASK << SR_ST3_IDLE_SHIFT))
#define M_COP1_SH_CHK_IDLE()					((R32_COP1_SH[R32_COP1_SH_SR_CNT] & (IDLE_MASK << IDLE_SHIFT)) == (IDLE_MASK << IDLE_SHIFT))
#define M_COP1_GC_CHK_IDLE()					((R32_COP1_GC[R32_COP1_GC_SR_REG0] & (GC_IDLE_MASK << GC_IDLE_SHIFT)) == (GC_IDLE_MASK << GC_IDLE_SHIFT))
#define M_COP1_ST1_CHK_IDLE()					((R32_COP1_ST1[R32_COP1_ST1_IDLE] & (IDLE_LIST_MASK << IDLE_LIST_SHIFT)) == (IDLE_LIST_MASK << IDLE_LIST_SHIFT))
#define M_COP1_BRG_CHK_IDLE()					((R32_COP1_BRG[R32_COP1_BRG_BRG_ILDE] & (BRG_IDLE_MASK << BRG_IDLE_SHIFT)) == (BRG_IDLE_MASK << BRG_IDLE_SHIFT))
#define M_COP1_CMDINT_CHK_DILE()				((R32_COP1_CMDINTF[R32_COP1_CMDINT_IDLE] & COP1_CMDINT_IDLE) == COP1_CMDINT_IDLE)

//COP1 CMDINT
#define M_COP1_CMDINT_GET_POP_EMPTY_INFO()		(R32_COP1_BUSINTF[R32_COP1_CMDINT_POP_EMPTY_INFO])
#define M_COP1_CMDINT_GET_ST3_CPU_DATA_ADDR()		(&R32_COP1_BUSINTF[R32_COP1_CMDINT_ST3_CPU_DATA])
#define M_COP1_CMDINT_GET_ST3C_CPU_DATA_ADDR()		(&R32_COP1_BUSINTF[R32_COP1_CMDINT_ST3C_CPU_DATA])
#define M_COP1_CMDINT_GET_SH_CPU_DATA_ADDR()		(&R32_COP1_BUSINTF[R32_COP1_CMDINT_SH_CPU_DATA])


//COP1 ST1
#define M_COP1_ST1_GET_DSA_LENGTH()				(R32_COP1_ST1[R32_COP1_ST1_DSA_LENGTH] & DSA_LENGTH_MASK)
#define M_COP1_ST1_GET_DSA_VALID()				((R32_COP1_ST1[R32_COP1_ST1_DSA_LENGTH] >> DSA_VALID_SHIFT) & DSA_VALID_MASK)
#define M_COP1_ST1_GET_FIRST_1K_SORTING_RULE()	(R32_COP1_ST1[R32_COP1_ST1_DSA_CTRL] & SORT_RULE_MASK & BIT0)
#define M_COP1_ST1_GET_FLUSH_BIT()				((R32_COP1_ST1[R32_COP1_ST1_WLB_ID] >> FLUSH_BIT_SHIFT) & FLUSH_BIT_MASK)
#define M_COP1_ST1_GET_SORTING_LENGTH_ADDR()  ((U32)&R32_COP1_ST1[R32_COP1_ST1_SORTING_LENGTH])

//COP1 ST3
#define M_COP1_ST3_GET_NORMAL_PTE_PCA_SETTING()	(R32_COP1_ST3[R32_COP1_ST3_CR_NORMAL_PTE_PCA_SETTING] & CR_NORMAL_PTE_PCA_SETTING_MASK)
#define COP1_ST3_NORMAL_PTE_PCA_SETTING_NO_BIT				(0x0)
#define COP1_ST3_NORMAL_PTE_PCA_SETTING_BIT30				(0x2)
#define COP1_ST3_NORMAL_PTE_PCA_SETTING_BIT30_BIT29		(0x3)


//COP1 ST3C
#define M_COP1_ST3C_GET_EMPTY_SIZE()			((R32_COP1_ST3C[R32_COP1_ST3C_LINK_LIST_SIZE_0] >> EMPTY_SIZE_SHIFT) & EMPTY_SIZE_MASK)
#define M_COP1_ST3C_GET_FREE_SIZE()				((R32_COP1_ST3C[R32_COP1_ST3C_LINK_LIST_SIZE_0] >> FREE_SIZE_SHIFT) & FREE_SIZE_MASK)
#define M_COP1_ST3C_GET_JUNIOR_SIZE()			((R32_COP1_ST3C[R32_COP1_ST3C_LINK_LIST_SIZE_0] >> JUNIOR_SIZE_SHIFT) & JUNIOR_SIZE_MASK)
#define M_COP1_ST3C_GET_SENIOR_SIZE()			((R32_COP1_ST3C[R32_COP1_ST3C_LINK_LIST_SIZE_1] >> SENIOR_SIZE_SHIFT) & SENIOR_SIZE_MASK)
#define M_COP1_ST3C_GET_FW_SIZE()				((R32_COP1_ST3C[R32_COP1_ST3C_LINK_LIST_SIZE_1] >> FW_SIZE_SHIFT) & FW_SIZE_MASK)

#define M_CLEAR_COP1_QBODY(PTR) do { \
	PTR->uoCOP1SQCommonCmd.ulDW0 = 0; \
	PTR->uoCOP1SQCommonCmd.ulDW1 = 0; \
} while(0)

#define M_COP1_ST3C_SET_FW_LOG_START_ADDR(ADDR) (R32_COP1_ST3C[R32_COP1_ST3C_FW_ST_ADDR] = (ADDR))
#define M_COP1_ST3C_SET_FW_LOG_END_ADDR(ADDR) (R32_COP1_ST3C[R32_COP1_ST3C_FW_END_ADDR] = (ADDR))

#define M_COP1_ST3C_GET_CACHE_ADDR_IN_HMB(CID) (R32_COP1_ST3C[R32_COP1_ST3C_PTE_C_ST_ADDR] + (CID << 12))

#define M_COP1_ST3C_GET_CIDTABLE_ADDR() (R32_COP1_ST3C[R32_COP1_ST3C_CIDTBL_ST_ADDR])

#define M_COP1_ST3C_GET_ERROR_STATUS() (R32_COP1_ST3C[R32_COP1_ST3C_ERR_ST])

#define M_COP1_GC_MINUS_DT_CNT() (R32_COP1_GC[R32_COP1_GC_CR_MINUS_CNT] = R32_COP1_GC[R32_COP1_GC_SR_SR_DT_CNT] & DT_CNT_MASK)
#define M_COP1_ST3_SET_GC_SOURCE(INDEX,UNIT) (R16_COP1_ST3[R16_COP1_ST3_CR_GC_INSERT_VB_N + (INDEX)] = (UNIT))
#define M_COP1_ST3_SET_GC_PTE_SOURCE(INDEX,UNIT) (R16_COP1_ST3[R16_COP1_ST3_CR_GC_PTE_VB_N + (INDEX)] = (UNIT))

#define M_COP1_GET_PMD_TBL_BITMAP_ADDR() ((U32)&R32_COP1_ST3[R32_COP1_ST3_CR_PMD_TBL_BITMAP_0])
#define M_COP1_CLR_PMD_TBL_BITMAP(INDEX) (R32_COP1_ST3[R32_COP1_ST3_CR_PMD_TBL_BITMAP_0 + (INDEX)] = 0)
#define M_COP1_SET_PMD_TBL_BITMAP(INDEX,VALUE) (R32_COP1_ST3[R32_COP1_ST3_CR_PMD_TBL_BITMAP_0 + (INDEX)] |= (VALUE))
#if PS5017_EN
#define M_COP1_GET_PMD_TBL_BITMAP_ADDR1() ((U32)&R32_COP1_ST3[R32_COP1_ST3_CR_PMD_TBL_BITMAP_16])
#define M_COP1_COPY_PMDBMP_ADDR1_TO_DBUF()        (memcpy((void *)(DBUF_TRIM_PMD_BITMAP + COP1_PMDBMP_BYTE_PER_REG_SECTION), (void *)(M_COP1_GET_PMD_TBL_BITMAP_ADDR1()),  (CEILING(guwTotalPMDNum - COP1_PMDNUM_PER_REG_SECTION, BITS_PER_BYTE) >> BITS_PER_BYTE_LOG)))
#define M_COP1_COPY_PMDBMP_DBUF_TO_ADDR1()        (memcpy((void *)(M_COP1_GET_PMD_TBL_BITMAP_ADDR1()), (void *)(DBUF_TRIM_PMD_BITMAP + COP1_PMDBMP_BYTE_PER_REG_SECTION), (CEILING(guwTotalPMDNum - COP1_PMDNUM_PER_REG_SECTION, BITS_PER_BYTE) >> BITS_PER_BYTE_LOG)))
#define M_COP1_COPY_PMDBMP_GCSRC_TO_ADDR1(GCSrc)  (memcpy((void *)(M_COP1_GET_PMD_TBL_BITMAP_ADDR1()), GCSrc + COP1_PMDBMP_BYTE_PER_REG_SECTION, (CEILING(guwTotalPMDNum - COP1_PMDNUM_PER_REG_SECTION, BITS_PER_BYTE) >> BITS_PER_BYTE_LOG)))
#define M_COP1_COPY_PMDBMP_ADDR1_TO_GCSRC(GCSrc)  (memcpy(GCSrc + COP1_PMDBMP_BYTE_PER_REG_SECTION, (void *)(M_COP1_GET_PMD_TBL_BITMAP_ADDR1()), (CEILING(guwTotalPMDNum - COP1_PMDNUM_PER_REG_SECTION, BITS_PER_BYTE) >> BITS_PER_BYTE_LOG)))
#define M_COP1_SET_PMDBMP_ADDR1(PMDIdx)     do {\
    U8 *pubST3RegPMDBMP1 = (U8 *)(M_COP1_GET_PMD_TBL_BITMAP_ADDR1()); \
	pubST3RegPMDBMP1[(PMDIdx) >> BITS_PER_BYTE_LOG] |= BIT((PMDIdx) & BITS_PER_BYTE_MASK); \
} while (0)
#define M_COP1_CLEAR_PMDBMP_ADDR1()               (memset((void *)M_COP1_GET_PMD_TBL_BITMAP_ADDR1(), 0, (CEILING(guwTotalPMDNum - COP1_PMDNUM_PER_REG_SECTION, BITS_PER_BYTE) >> BITS_PER_BYTE_LOG)))
#else /* PS5017_EN */
#define M_COP1_GET_PMD_TBL_BITMAP_ADDR1() (NULL)
#define M_COP1_COPY_PMDBMP_ADDR1_TO_DBUF()  {}
#define M_COP1_COPY_PMDBMP_DBUF_TO_ADDR1()  {}
#define M_COP1_COPY_PMDBMP_GCSRC_TO_ADDR1(GCSrc)  {}
#define M_COP1_COPY_PMDBMP_ADDR1_TO_GCSRC(GCSrc)  {}
#define M_COP1_SET_PMDBMP_ADDR1(PMDIdx)           {}
#define M_COP1_CLEAR_PMDBMP_ADDR1()         {}
#endif /* PS5017_EN */
#define M_COP1_SET_GCSA_PMDLOG_ADDR(ADDR) (R32_COP1_ST3[R32_COP1_ST3_CR_GCSA_PMDLOG_BADR]=(ADDR))
#define M_COP1_GET_GCSA_PMDLOG_ADDR() (R32_COP1_ST3[R32_COP1_ST3_CR_GCSA_PMDLOG_BADR])
#define M_COP1_SET_GCSA_SIZE(SIZE) (R32_COP1_ST3[R32_COP1_ST3_CR_GCSA_SIZE]=(SIZE))
#define M_COP1_GET_GCSA_SIZE() (R32_COP1_ST3[R32_COP1_ST3_CR_GCSA_SIZE])
#define M_COP1_SET_GATHER_ADDR(ADDR) (R32_COP1_ST3[R32_COP1_ST3_CR_GATHER_BADR]=(ADDR))
#define M_COP1_GET_GC_DT_CNT() (R32_COP1_GC[R32_COP1_GC_SR_SR_DT_CNT] & DT_CNT_MASK)
#define M_COP1_GET_VB_SHIFT() ((R32_COP1_GC[R32_COP1_GC_CR_COP1_COMMON] >> VB_SHIFT_SHIFT) & VB_SHIFT_MASK)
#define M_COP1_GET_VB_MASK() ((R32_COP1_GC[R32_COP1_GC_CR_COP1_COMMON] >> VB_MASK_SHIFT) & VB_MASK_MASK)
#define M_COP1_GET_INVALID_CID() (R32_COP1_ST3C[R32_COP1_ST3C_INVALID_CID])
#define M_COP1_GET_ST3_PTE_BASE_ADDR() (R32_COP1_ST3[R32_COP1_ST3_CR_PTE_BADR])
#define M_COP1_GET_ST3_PMD_BASE_ADDR() (R32_COP1_ST3[R32_COP1_ST3_CR_PMD_BADR])
#define M_COP1_GET_ST3C_DT_LOG_ST_ADDR() (R32_COP1_ST3C[R32_COP1_ST3C_DT_ST_ADDR])
#define M_COP1_GET_ST3C_DT_LOG_END_ADDR() (R32_COP1_ST3C[R32_COP1_ST3C_DT_END_ADDR])

//For Stall or Reset in Cache tbl for hmb mode
#define M_COP1_STALL_ST3C_CACHE_HMB()	(R32_COP1_ST3C[R32_COP1_ST3C_CPU_DB_SEL] |= CR_DEBUG_MODE_BIT)

#define M_COP1_DESTALL_ST3C_CACHE_HMB()	do{\
											R32_COP1_ST3C[R32_COP1_ST3C_CPU_DB_SEL] &= ~CR_DEBUG_MODE_BIT;\
											R32_COP1_ST3C[R32_COP1_ST3C_CPU_DB_SEL] |= CR_DEBUG_STEP_BIT;\
											R32_COP1_ST3C[R32_COP1_ST3C_CPU_DB_SEL] &= ~CR_DEBUG_STEP_BIT;\
										} while(0)

#define M_COP1_RESET_ST3C_CACHE_HMB()	do{\
											R32_COP1_ST3C[R32_COP1_ST3C_CPU_DB_SEL] |= CR_CACHE_RST_BIT;\
											R32_COP1_ST3C[R32_COP1_ST3C_CPU_DB_SEL] &= ~CR_CACHE_RST_BIT;\
										} while(0)

#define M_COP1_SET_ST3C_DEBUG_PORT(VALUE)	do{\
												R32_COP1_ST3C[R32_COP1_ST3C_CPU_DB_SEL] &= ~(CR_DBSEL_MASK << CR_DBSEL_SHIFT);\
												R32_COP1_ST3C[R32_COP1_ST3C_CPU_DB_SEL] |= (VALUE << CR_DBSEL_SHIFT);\
											} while(0)

#define ST3C_DEBUG_PORT_GET_CACHE_TABLE_STATUS	(2)
#define ST3C_DEBUG_PORT_GET_ALLOCATE_STATUS		(3)

#define M_COP1_ST3C_GET_ALLOCATE_STATUS()	((R32_COP1_ST3C[R32_COP1_ST3C_CPU_DB_PORT_L] >> ALLOCATE_STATUS_SHIFT) & ALLOCATE_STATUS_MASK)
#define	ALLOCATE_STATUS_4					(4)
#define	ALLOCATE_STATUS_7					(7)
#define	ALLOCATE_STATUS_8					(8)

#define M_COP1_ST3C_GET_CACHE_TABLE_STATUS() ((R32_COP1_ST3C[R32_COP1_ST3C_CPU_DB_PORT_H] >> CACHE_TABLE_STATUS_SHIFT) & CACHE_TABLE_STATUS_MASK)
#define CACHE_TABLE_STATUS_5				(5)

#define M_COP1_ST3C_GET_AXIW_CMD_BUSY()	((R32_COP1_ST3C[R32_COP1_ST3C_CPU_DB_PORT_L] & AXI_WRITE_CMD_BUSY_BIT) >> AXI_WRITE_CMD_BUSY_SHIFT)

#define M_COP1_ST3C_GET_AXIW_IDLE()		((R32_COP1_ST3C[R32_COP1_ST3C_IDLE] & AXIW_IDLE_BIT) >> AXIW_IDLE_SHIFT)

#define M_GET_INVALID_CID_VALUE()		 (R32_COP1_ST3C[R32_COP1_ST3C_INVALID_CID] & CR_INVALID_CID_MASK)

//=============================================================================
// Macro Functions
//=============================================================================
#if VS_SIM_EN
#define M_GET_ST1_REG_DSA_START_IDX()	((*(U32*)gSt1blkStruct.regs.start_end_index))
#define M_GET_ST1_REG_SORTED_PTR()		(((*(U32*)gSt1blkStruct.regs.dsa_ctrl) >> SORTED_PTR_SHIFT) & SORTED_PTR_MASK)
#define M_GET_GCBLK_SLC_POOL_NUM()	(gGcblkStruct.reg_array[GCBLK_REGS_SLC_POOL_NUM])
#else /*VS_SIM_EN*/
//ST1
#define M_GET_ST1_REG_SORTING_LENGTH()		((R32_COP1_ST1[R32_COP1_ST1_SORTING_LENGTH]		>>SORTING_LENGTH_SHIFT)	& SORTING_LENGTH_MASK)
#define M_GET_ST1_REG_SORTING_VALID()	((R32_COP1_ST1[R32_COP1_ST1_SORTING_VALID]		>>SORTING_VALID_SHIFT)	& SORTING_VALID_MASK)
#define M_GET_ST1_REG_DSA_LENGTH()			((R32_COP1_ST1[R32_COP1_ST1_DSA_LENGTH]			>>DSA_LENGTH_SHIFT)		& DSA_LENGTH_MASK)
#define M_GET_ST1_REG_DSA_VALID()		((R32_COP1_ST1[R32_COP1_ST1_DSA_LENGTH]			>>DSA_VALID_SHIFT)		& DSA_VALID_MASK)
#define M_GET_ST1_REG_FLUSH_BIT()		((R32_COP1_ST1[R32_COP1_ST1_WLB_ID]				>>FLUSH_BIT_SHIFT)		& FLUSH_BIT_MASK)
#define M_GET_ST1_REG_DSA_START_IDX()	((R32_COP1_ST1[R32_COP1_ST1_START_END_INDEX]	>>DSA_START_INDEX_SHIFT)& DSA_START_INDEX_MASK)
#define M_GET_ST1_REG_DSA_END_PTR()		((R32_COP1_ST1[R32_COP1_ST1_START_END_INDEX]	>>DSA_END_INDEX_SHIFT)	& DSA_END_INDEX_MASK)
#define M_GET_ST1_REG_SORT_RULE()		((R32_COP1_ST1[R32_COP1_ST1_DSA_CTRL]			>>SORT_RULE_SHIFT)		& SORT_RULE_MASK)
#define M_GET_ST1_REG_SORTED_PTR() 		((R32_COP1_ST1[R32_COP1_ST1_DSA_CTRL] >> SORTED_PTR_SHIFT) & SORTED_PTR_MASK)
#define M_GET_ST1_REG_RELEASE_PTR()		((R32_COP1_ST1[R32_COP1_ST1_SORTING_LENGTH]		>>RELEASE_PTR_SHIFT)	& RELEASE_PTR_MASK)
#define M_GET_ST1_DSA_START_FLOOR_OFFSET(X)	(SIZE_IN_32B_FLOORING(((X) << COP1_ST1_DSA_ENTRY_SIZE_LOG)) << COP1_ALIGN_32_BYTE_LOG)
#define M_GET_ST1_DSA_START_FLOOR_ADDR(X)	(COP1_SRAM_ST1_DSA_BASE_ADDR + M_GET_ST1_DSA_START_FLOOR_OFFSET(X))

//GC
#if (BURNER_MODE_EN || RDT_MODE_EN)
#define M_GET_GCBLK_SLC_POOL_NUM()  (gpVT->ulSLCPoolNum)
#else /* (BURNER_MODE_EN || RDT_MODE_EN) */
#define M_GET_GCBLK_SLC_POOL_NUM()	(R32_COP1_GC[R32_COP1_GC_CR_SLC_POOL_NUM])
#endif /* (BURNER_MODE_EN || RDT_MODE_EN) */
#define M_SET_GCBLK_SLC_POOL_NUM(X)	(R32_COP1_GC[R32_COP1_GC_CR_SLC_POOL_NUM] = (X))
#endif

#define M_COP1_GC_IS_VC_OVER_UNDER_FLOW()	(0 != (R32_COP1_GC[R32_COP1_GC_SR_VB_ERR_0] & ((ERR_0_S0_MASK << ERR_0_S0_SHIFT) | (ERR_0_S1_MASK << ERR_0_S1_SHIFT))))

#define M_IS_PCA_INVALID(PCA)		(((COP1_INVALID_PCA_MASK) == ((PCA) & (COP1_INVALID_PCA_MASK)))? TRUE : FALSE)

//=============================================================================
// Structures & Types
//=============================================================================
#if PS5017_EN
typedef union {
	U64 uoAll;
	struct {
		U64 cid: 20;		//[19:0]//E17_porting_4TB
		U64 NoUse1: 12;		//[31:20]//E17_porting_4TB
		U64 Index: 20;		//[51:32]//E17_porting_4TB
		U64 PBOffSet: 9;
		U64 btInvalid: 1;
		U64 btType: 1;
		U64 NoUse2: 1;		//[63]//E17_porting_4TB
	} B;
} COP1DTLOG_t;
typedef union {
	U64 uoAll;
	struct {
		U64 PTE_PCA : 32;
		U64 Index: 20;		//E17_porting_4TB
		U64 PBOffSet: 9;
		U64 btInvalid: 1;
		U64 btType: 1;
		U64 NoUse2: 1;		//E17_porting_4TB
	} B;
} COP1UPPMDLOG_t;
typedef union {
	U64 uoAll;
	struct {
		U64 cid: 20;		//E17_porting_4TB
		U64 NoUse1: 12;		//E17_porting_4TB
		U64 Index: 20;		//E17_porting_4TB
		U64 NoUs2: 12;		//E17_porting_4TB
	} B;
} COP1UNLOCK_FW_LOG_t;
#elif PS5021_EN /* PS5017_EN */
typedef union {
	U64 uoAll;
	struct {
		U64 cid:		20;
		U64 PBOffSet:	10;
		U64 NoUse1: 	2;
		U64 Index:		20;
		U64 NoUse2: 	9;
		U64 btInvalid:	1;
		U64 btType: 	1;
		U64 btErr:		1;
	} B;
} COP1DTLOG_t;
typedef union {
	U64 uoAll;
	struct {
		U64 PTE_PCA:	32;
		U64 Index:		20;
		U64 PBOffSet: 	9;
		U64 btInvalid:	1;
		U64 btType: 	1;
		U64 btErr:		1;
	} B;
} COP1UPPMDLOG_t;
typedef union {
	U64 uoAll;
	struct {
		U64 cid: 20;
		U64 NoUse1: 12;
		U64 Index: 20;
		U64 NoUs2: 12;
	} B;
} COP1UNLOCK_FW_LOG_t;
#else /* PS5017_EN */
typedef union {
	U64 uoAll;
	struct {
		U64 cid: 19;
		U64 NoUse1: 13;
		U64 Index: 19;
		U64 PBOffSet: 9;
		U64 btInvalid: 1;
		U64 btType: 1;
		U64 NoUse2: 2;
	} B;
} COP1DTLOG_t;
typedef union {
	U64 uoAll;
	struct {
		U64 PTE_PCA : 32;
		U64 Index: 19;
		U64 PBOffSet: 9;
		U64 btInvalid: 1;
		U64 btType: 1;
		U64 NoUse2: 2;
	} B;
} COP1UPPMDLOG_t;
typedef union {
	U64 uoAll;
	struct {
		U64 cid: 19;
		U64 NoUse1: 13;
		U64 Index: 19;
		U64 NoUs2: 13;
	} B;
} COP1UNLOCK_FW_LOG_t;
#endif /* PS5017_EN */
typedef union {
	U64 uoAll;
	struct {
		U32 ulPCA;
		U32 ulLCA;
	} B;
} Node_t;
typedef union {
	U32 ulAll;
	struct {
		U32 Index : 20;
		U32 btDirty : 1;
		U32 btlock : 1;
		U32 btCopy : 1;
		U32 btLcok_fw : 1;
		U32 : 8;
	} B;
} ST3CCTBLInHMB_t;

typedef struct {
	U8	ubAlreadySaveDSA;
	U8	ubAlreadySaveContainer;
	U32	ulST1RegSaveDSA[COP1_ST1_REG_SIZE_IN_4BYTE];
	U32	ulST1RegSaveContainer[COP1_ST1_REG_SIZE_IN_4BYTE];
} ST1REGSTATUS_t;

//=============================================================================
// Variable
//=============================================================================
extern ST1REGSTATUS_t gST1RegStatus;
extern U8 gubCOP1Done;
extern U8 gubCOP1AdjustCacheDone;
extern U16 guwCOP1AdjustCacheResultNum;
//=============================================================================
// Functions
//=============================================================================
#if RDT_MODE_EN
void rdt_COP1APISHBlkSCHLCA(U32 ulLCA, U8 ubBMS, U8 ubFW, U8 ubLBIDSel, U8 ubCOP0, cmd_table_t *puoCallbackInfo, U8 ubCnt);
#endif
void COP1APISHBlkSCHLCA(U32 ulLCA, U8 ubBMS, U8 ubFW, U8 ubLBIDSel, U8 ubCOP0, cmd_table_t *puoCallbackInfo);
void COP1APISHBlkBarrier(cmd_table_t *puoCallbackInfo);
AOM_TRIM void COP1APIST1BlkEGLCAForTrim(U32 ulLCA);
AOM_TABLE_UPDATE void COP1APIST1ReleaseDSA(U8 ubReleaseLoc, U8 btSortingRule, U32 ulCallBackAddr);
void COP1APIST1FlushST1(U32 ulCallBackAddr);
AOM_TRIM void COP1APIST1Trim(U32 ulLCAStart, U32 ulLCAEnd, U32 ulCallBackAddr);
AOM_TABLE_UPDATE void COP1APIST3InsertDSA(U8 ubMoveRule, U8 ubCOP0Attr, U16 uwJobNum, U16 uwDSAStartIdx, U32 ulCallBackAddr);
AOM_TABLE_UPDATE void COP1APIST3InsertGCSA(U8 ubMoveRule, U8 ubCOP0Attr, U16 uwJobNum, U16 uwGCSAStartIdx, U32 ulCallBackAddr);
AOM_TABLE_UPDATE void COP1APIST3TrimPTE(U8 ubMoveRule, U8 ubCOP0Attr, U32 ulLCAStart, U32 ulLCAEnd, U32 ulCallBackAddr);
void COP1APIST3TrimPMD(U8 ubMoveRule, U8 ubCOP0Attr, U32 ulLCAStart, U32 ulLCAEnd, U32 ulCallBackAddr);
AOM_TABLE_UPDATE void COP1APIST3TrimCond(U8 ubMoveRule, U8 ubCOP0Attr, U16 uwJobNum, U32 ulCallBackAddr);
AOM_TABLE_UPDATE void COP1APIST3UpdatePMD(U8 ubType, U8 btMode, U8 ubCOP0, U16 uwJobNum, U16 uwPMDLogStartIdx, U32 ulCallBackAddr);
AOM_TABLE_GC void COP1APIST3GCPTE(U8 ubType, U8 ubCOP0, U16 uwGCPTELogNum, U32 ulCallBackAddr);
AOM_INIT_2 void COP1APIST3CInitialLink(U32 ulCallBackAddr);
AOM_TABLE_UPDATE void COP1APIST3CLockDT(U16 uwStartIdx, U16 uwLockCnt, U32 ulCallBackAddr);
AOM_TABLE_UPDATE void COP1APIST3CUnlockDT(U16 uwStartIdx, U16 uwLockCnt, U8 btToFreeCIDPool, U32 ulCallBackAddr);
void COP1APIST3CAllocatePB(U8 ubFUA, U8 ubQOB, U8 ubLBID, U8 ubTimeOut, U16 uwAllocatePBNum, U32 ulCallBackAddr);
void COP1APIST3CFreePB(U8 ubFUA, U8 ubQOB, U8 ubLBID, U16 uwFreePBNum, U32 ulCallBackAddr);
void COP1APIST3CRemoveTable(U16 uwCID, U8 ubType, U8 btToHead, U8 btAll, U8 btIgnoreFW, U32 ulCallBackAddr);  //Callback funciton flow
void COP1APIST3CCollectCache(U16 uwAdjustNum);
void COP1APIST3CReleaseCache(U16 uwAdjustNum);
AOM_HMB void COP1APIGCDTFlush_AOM_HMB(U8 btInit, U32 ulCallBackAddr, U16 uwCallBackData);
AOM_INIT_2 void COP1APIGCDTFlush_AOM_INIT_2(U8 btInit, U32 ulCallBackAddr, U16 uwCallBackData);
AOM_TABLE_UPDATE void COP1APIGCDTFlush_AOM_Table_Update(U8 btInit, U32 ulCallBackAddr, U16 uwCallBackData);
void COP1APIGCSetVC(U32 ulValidMask, U32 ulValidCnt, U16 uwVB);
AOM_TABLE_UPDATE void COP1APIGCUpdateCount(U8 ubUpdateJob, U16 uwVB1, U16 uwVB2, U32 ulCallBackAddr);
U8 COP1ApiCmdST3CFWRemoveTable(U8 ubSource, U8 ubRet_bit, U16 uwTag, U8 ubRemovAll, U32 uwRemovCid, U8 ubTargetLink);
AOM_TABLE_UPDATE void COP1ApiSetNotifyNumber(U16 uwSetNotifyNum);
AOM_HMB void COP1APIST3CSetFreeCid(U32 ulWPTR, U32 ulRPTR, U32 ulBufLevel, U32 ulCallBackAddr);
AOM_TABLE_UPDATE void COP1APIST3CLockDT(U16 uwStartIdx, U16 uwLockCnt, U32 ulCallBackAddr);
AOM_TABLE_UPDATE void COP1APIST3CUnlockDT(U16 uwStartIdx, U16 uwLockCnt, U8 btToFreeCIDPool, U32 ulCallBackAddr);
void COP1APIST3CUnlockFW(U16 uwStartIdx, U16 uwLockCnt, U8 btToFreeCIDPool, U32 ulCallBackAddr);
AOM_TABLE_UPDATE void COP1APIST3CUnlockGCPTE(U16 uwStartIdx, U16 uwLockCnt, U8 btToFreeCIDPool, U32 ulCallBackAddr);
void COP1APIRemoveTable_ForGC(U32 ulCID, U32 ulPTEIndex, U8 ubBypassWaitRemoveDone);
AOM_TABLE_UPDATE void COP1SetDTlog(U32 ulDTAddr, U32 ulDTLogByteNum, U32 ulDTLogPtr);

AOM_INIT_2 void COP1SLCPoolNumSetup(void);
AOM_INIT_2 void InitCop1RAM(void);
AOM_INIT_2 void InitCop1Reg(U8 ubMode);
AOM_INIT_2 void InitCOP1Cache(void);
AOM_HMB void InitCOP1CacheInHMB(HMBMemoryMap_t *pMemoryMap);
AOM_HMB void COP1ApiWaitIdle(void);
AOM_GC void COP1SetST3COP0FlushBit(U8 ubBool);
AOM_SECURITY U8 COP1ApiGetD2HPathIdle(void);
//Callback
void ST3CAPIAllocatePBDone_Callback(COP1CQRst_t uoResult);
void COP1ForceLoad_CallBack(COP1CQRst_t uoResult);

extern FW_CALLBACK_SECTION U32 gulST3CAPIFreePBDone_Callback;
extern FW_CALLBACK_SECTION U32 gulCOP1Simple_CallBack;
#endif /* _COP1_API_H_ */
