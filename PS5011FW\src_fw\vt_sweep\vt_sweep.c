/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "vt_sweep/vt_sweep.h"
#include "common/fw_vardef.h"
#include "common/math_op.h"
#include "hal/fip/fip_api.h"
#include "hal/fip/fpu.h"
#include "retry/stall.h"
#include "retry/patch_cmd.h"
#include "hal/cop0/cop0_reg.h"
#include "hal/cop0/cop0_api.h"
#include "debug/debug.h"
#include "ftl/ftl_readverify_api.h"
#include "ftl/ftl_api.h"
#include "hal/fip/fip_api.h"
#include "table\vbmap\vbmap_api.h"
#include "media_scan/media_scan_api.h"

#if (VT_SWEEP_EN && PS5017_EN)
U8 gubAddrHit;
VT_SWEEP_PAYLOAD_T *gpVtSweepPayLoad;
MicronFrey2VtSweep_t *gpFrey2VtSweep = (MicronFrey2VtSweep_t *)(DBUF_VTSWEEP);

U16 FTLInvertCoord( U8 ubX, U16 uwY )
{
	U16 uwSuperpageIdx = 0;
#if (MICRON_FSP_EN)
	U16 uwPageIdx = 0xFFFF;
	U8 ubCase = 0xFF;
#if (IM_B27B || IM_B27A)
	U16 uwWordLine = uwY;
	U16 ubWordLineBase = 0xFF;
#endif

#if (IM_B47R)
	if (0 == uwY) {
		if (0 == (ubX % 3)) {
			ubCase = 0;
			uwPageIdx = 0;
		}
	}
	else if (uwY < 88) {
		ubCase = 1;
		uwPageIdx = IM_B47R_SECTION_1;
		uwY -= 1;
	}
	else if (uwY < 90) {
		if ((ubX % 3) < 2) {
			ubCase = 2;
			uwPageIdx = IM_B47R_SECTION_2;
			uwY -= 88;
		}
	}
	else if (uwY < 177) {
		ubCase = 1;
		uwPageIdx = IM_B47R_SECTION_3;
		uwY -= 90;
	}
	else if (177 == uwY) {
		if (0 == (ubX % 3)) {
			ubCase = 0;
			uwPageIdx = IM_B47R_SECTION_4;
		}
	}

	switch (ubCase) {
	case 0:
		uwPageIdx += (ubX / 3);
		break;
	case 1:
		uwPageIdx += (uwY * 12 + ubX);
		break;
	case 2:
		uwPageIdx += (uwY * 8 + (ubX / 3 * 2) + (ubX % 3));
		break;
	default:
		break;
	}
	uwSuperpageIdx = uwPageIdx;
#elif (IM_N48R)

	if (0 == uwY) {
		if ((ubX % 4) < 2) {
			ubCase = 0;
			uwPageIdx = 0;
		}
	}
	else if (uwY < 88) {
		ubCase = 1;
		uwPageIdx = IM_N48_SECTION_1;
		uwY -= 1;
	}
	else if (uwY < 90) {
		if ((ubX % 4) < 2) {
			ubCase = 0;
			uwPageIdx = IM_N48_SECTION_2;
			uwY -= 88;
		}
	}
	else if (uwY < 177) {
		ubCase = 1;
		uwPageIdx = IM_N48_SECTION_3;
		uwY -= 90;
	}
	else if (177 == uwY) {
		if ((ubX % 4) < 2) {
			ubCase = 0;
			uwPageIdx = IM_N48_SECTION_4;
			uwY -= 177;
		}
	}

	switch (ubCase) {
	case 0:  // MLC
		uwPageIdx += (uwY * 8 + (ubX / 4 * 2) + (ubX % 4));
		break;
	case 1:  // QLC
		uwPageIdx += (uwY * 16 + ubX);
		break;
	default:
		break;
	}
	uwSuperpageIdx = uwPageIdx;
#else  /* (IM_B47R) */  /* (IM_N48R) */
	U8 ubCheckFlashID = 4;

	switch (gFlhEnv.ubFlashID[ubCheckFlashID]) {
#if 0
	case 0xA1:	//B16A
	case 0xA6:  //B17A
		if ((uwY) < IM_B17_AXIS_X_SECTION_1) {
			if ((ubX) % 3) {
				uwSuperpageIdx = IM_B17_AXIS_X_NULL_VALUE;
			}
			else {
				uwSuperpageIdx = ((uwY * 4) + (ubX / 3));
			}
		}
		else if ((uwY) < IM_B17_AXIS_X_SECTION_2) {
			if (2 == ((ubX) % 3)) {
				uwSuperpageIdx = IM_B17_AXIS_X_NULL_VALUE;
			}
			else {
				uwSuperpageIdx = (((uwY - IM_B17_AXIS_X_SECTION_1) * 8) + ((ubX / 3) + (ubX % 3)) + IM_B17_SECTION_1);
			}
		}
		else if ((uwY) < IM_B17_AXIS_X_SECTION_4) {
			if ((ubX) % 3) {
				uwSuperpageIdx = ( ((uwY - IM_B17_AXIS_X_SECTION_2) * 12) + ( ((ubX / 3) * 3) + ( 2 - (ubX % 3)) ) + IM_B17_SECTION_3 );
			}
			else {
				if ((uwY) < IM_B17_AXIS_X_SECTION_3) {
					uwSuperpageIdx = (((uwY - IM_B17_AXIS_X_SECTION_2) * 4) + (ubX / 3) + IM_B17_SECTION_2) ;
				}
				else {
					uwSuperpageIdx = (((uwY - IM_B17_AXIS_X_SECTION_3) * 12) + ((ubX / 3) * 3) + 62) ;
				}
			}

		}
		else if ((uwY) < IM_B17_AXIS_X_SECTION_5) {  //189
			if ((ubX) % 3) {
				uwSuperpageIdx = ( ((uwY - IM_B17_AXIS_X_SECTION_4) * 16) + ( ((ubX / 3) * 3) + ( 2 - (ubX % 3)) ) + IM_B17_SECTION_4 );
			}
			else {
				uwSuperpageIdx = (((uwY - IM_B17_AXIS_X_SECTION_3) * 12) + ((ubX / 3) * 3) + 62) ;
			}
		}
		else if ((uwY) < IM_B17_AXIS_X_SECTION_6) {  //192
			if ((ubX) % 3) {
				uwSuperpageIdx = ( ((uwY - IM_B17_AXIS_X_SECTION_5) * 8) + ( ((ubX / 3) * 2) + ( 2 - (ubX % 3)) ) + 2268 );
			}
			else {
				uwSuperpageIdx = (((uwY - IM_B17_AXIS_X_SECTION_3) * 12) + ((ubX / 3) * 3) + 62) ;
			}
		}
		else if ((uwY) < IM_B17_AXIS_X_SECTION_7) {  //195
			if ( 2 == ((ubX) % 3)) {
				uwSuperpageIdx = IM_B17_AXIS_X_NULL_VALUE;
			}
			else {
				uwSuperpageIdx = ( ((uwY - IM_B17_AXIS_X_SECTION_6) * 16) + ( ((ubX / 3) * 4) + (ubX % 3) ) + 2268 );
			}
		}
		else {
			if ((ubX) % 3) {
				uwSuperpageIdx = IM_B17_AXIS_X_NULL_VALUE;
			}
			else {
				uwSuperpageIdx = ((uwY * 4) + (ubX / 3) + 2292);
			}
		}
		break; // break Case B17A
#endif
#if IM_B27A
	case 0xA2:	// B27A
		if (0 == uwWordLine) {	//SLC
			uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_0_PAGE_BASE;
			ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_0;
			ubWordLineBase = uwWordLine;
			if (0 != (ubX % 3)) {	//Upper, Xtra Page
				uwSuperpageIdx = IM_B27A_AXIS_INVALID_PAGE_IDX;
			}
		}
		else if (1 == uwWordLine) { //MLC
			uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_1_PAGE_BASE;
			ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_1;
			if (2 == (ubX % 3)) {	//Xtra Page
				uwSuperpageIdx = IM_B27A_AXIS_INVALID_PAGE_IDX;
			}
		}
		else if ((2 == uwWordLine)	|| (3 == uwWordLine)) {
			if (0 != (ubX % 3)) {	//Upper, Xtra Page
				uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_2_3_UPPER_EXTRA_PAGE_BASE;
				ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_2;
				ubWordLineBase = IM_B27A_AXIS_WORDLINE_2_3_WORDLINE_BASE;
			}
			else {	//Lower Page
				uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_2_3_LOWER_PAGE_BASE;
				ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_0;
				ubWordLineBase = IM_B27A_AXIS_WORDLINE_2_3_WORDLINE_BASE;
			}
		}
		else if ((4 <= uwWordLine) && (44 >= uwWordLine)) {
			uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_4_44_PAGE_BASE;
			ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_2;
			ubWordLineBase = IM_B27A_AXIS_WORDLINE_4_46_WORDLINE_BASE;
		}
		else if (45 == uwWordLine) {
			if (0 != (ubX % 3)) {	//Upper, Xtra Page
				uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_45_UPPER_EXTRA_PAGE_BASE;
				ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_3;
			}
			else {	//Lower Page
				uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_45_46_LOWER_PAGE_BASE;
				ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_2;
				ubWordLineBase = IM_B27A_AXIS_WORDLINE_4_46_WORDLINE_BASE;
			}
		}
		else if (46 == uwWordLine) {
			if (0 != (ubX % 3)) {	//Upper, Xtra Page
				uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_46_UPPER_EXTRA_PAGE_BASE;
				ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_4;
			}
			else {	//Lower Page
				uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_45_46_LOWER_PAGE_BASE;
				ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_2;
				ubWordLineBase = IM_B27A_AXIS_WORDLINE_4_46_WORDLINE_BASE;
			}
		}
		else if (47 == uwWordLine) {
			uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_47_PAGE_BASE;
			ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_5;
			if (2 == (ubX % 3)) {	//Xtra Page
				uwSuperpageIdx = IM_B27A_AXIS_INVALID_PAGE_IDX;
			}
		}
		else if (48 == uwWordLine) {
			if (0 != (ubX % 3)) {	//Upper, Xtra Page
				uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_48_UPPER_EXTRA_PAGE_BASE;
				ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_4;
			}
			else {	//Lower Page
				uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_48_LOWER_PAGE_BASE;
				ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_3;
			}
		}
		else if (49 == uwWordLine) {
			uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_49_PAGE_BASE;
			ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_5;
			if (2 == (ubX % 3)) {	//Xtra Page
				uwSuperpageIdx = IM_B27A_AXIS_INVALID_PAGE_IDX;
			}
		}
		else if (50 == uwWordLine) {
			if (0 !=  (ubX % 3)) {	//Upper, Xtra Page
				uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_50_UPPER_EXTRA_PAGE_BASE;
				ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_2;
				ubWordLineBase = uwWordLine;
			}
			else {	//Lower Page
				uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_50_LOWER_PAGE_BASE;
				ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_0;
				ubWordLineBase = uwWordLine;
			}
		}
		else if (51 == uwWordLine) {
			uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_51_PAGE_BASE;
			ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_1;
			if (2 == (ubX % 3)) {	//Xtra Page
				uwSuperpageIdx = IM_B27A_AXIS_INVALID_PAGE_IDX;
			}
		}
		else if (52 == uwWordLine) {
			if (0 != (ubX % 3)) {	//Upper, Xtra Page
				uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_52_UPPER_EXTRA_PAGE_BASE;
				ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_2;
				ubWordLineBase = IM_B27A_AXIS_WORDLINE_52_96_WORDLINE_BASE;
			}
			else {	//Lower Page
				uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_52_LOWER_PAGE_BASE;
				ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_0;
				ubWordLineBase = uwWordLine;
			}
		}
		else if (53 == uwWordLine) {
			if (0 != (ubX % 3)) {	//Upper, Xtra Page
				uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_53_UPPER_EXTRA_PAGE_BASE;
				ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_2;
				ubWordLineBase = IM_B27A_AXIS_WORDLINE_52_96_WORDLINE_BASE;
			}
			else {	//Lower Page
				uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_53_LOWER_PAGE_BASE;
				ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_3;
			}
		}
		else if ((54 <= uwWordLine) && (94 >= uwWordLine)) {
			uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_54_94_PAGE_BASE;
			ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_2;
			ubWordLineBase = IM_B27A_AXIS_WORDLINE_52_96_WORDLINE_BASE;
		}
		else if (95 == uwWordLine) {
			if (0 != (ubX % 3)) {	//Upper, Xtra Page
				uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_95_UPPER_EXTRA_PAGE_BASE;
				ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_3;
			}
			else {	//Lower Page
				uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_95_96_LOWER_PAGE_BASE;
				ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_2;
				ubWordLineBase = IM_B27A_AXIS_WORDLINE_52_96_WORDLINE_BASE;
			}
		}
		else if (96 == uwWordLine) {
			if (0 != (ubX % 3)) {	//Upper, Xtra Page
				uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_96_UPPER_EXTRA_PAGE_BASE;
				ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_4;
			}
			else {	//Lower Page
				uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_95_96_LOWER_PAGE_BASE;
				ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_2;
				ubWordLineBase = IM_B27A_AXIS_WORDLINE_52_96_WORDLINE_BASE;
			}
		}
		else if (97 == uwWordLine) {
			uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_97_PAGE_BASE;
			ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_5;
			if (2 == (ubX % 3)) {	//Xtra Page
				uwSuperpageIdx = IM_B27A_AXIS_INVALID_PAGE_IDX;
			}
		}
		else if (98 == uwWordLine) {
			uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_98_PAGE_BASE;
			ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_3;
			if (0 != (ubX % 3)) {	//Upper, Xtra Page
				uwSuperpageIdx = IM_B27A_AXIS_INVALID_PAGE_IDX;
			}
		}
		else {
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
		}
		if (IM_B27A_AXIS_INVALID_PAGE_IDX == uwSuperpageIdx) {
			break;
		}
		switch (ubCase) {
		case IM_B27A_AXIS_X_Y_TO_PAGE_CASE_0:
			uwSuperpageIdx += ((uwWordLine - ubWordLineBase) * 18  + (ubX / 3));
			break;
		case IM_B27A_AXIS_X_Y_TO_PAGE_CASE_1:
			uwSuperpageIdx += ((ubX / 3) * 2 + (ubX % 3));
			break;
		case IM_B27A_AXIS_X_Y_TO_PAGE_CASE_2:
			uwSuperpageIdx += (((uwWordLine - ubWordLineBase) - ((0 == (ubX % 3)) ? 2 : 0)) * 54 + ((ubX / 3) * 3) + (2 - (ubX % 3)));
			break;
		case IM_B27A_AXIS_X_Y_TO_PAGE_CASE_3:
			uwSuperpageIdx += ((ubX / 3) * 3 + (2 - ubX % 3));
			break;
		case IM_B27A_AXIS_X_Y_TO_PAGE_CASE_4:
			uwSuperpageIdx += (((ubX / 3) * 2) + (2 - (ubX % 3)));
			break;
		case IM_B27A_AXIS_X_Y_TO_PAGE_CASE_5:
			uwSuperpageIdx += ((ubX / 3) * 2 + (ubX % 3));
			break;

		default:
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
			break;
		}
		break; // break Case B27A
#elif IM_B27B


	case 0xE6:  //B27B
		if (0 == uwWordLine) {
			uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_0_PAGE_BASE;
			ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_0;
			ubWordLineBase = uwWordLine;
			if (2 > (ubX % 3)) {	//Upper, Xtra Page
				uwSuperpageIdx = IM_B27B_AXIS_INVALID_PAGE_IDX;
			}
		}
		else if (1 == uwWordLine) {
			uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_1_PAGE_BASE;
			ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_1;
			if (0 == (ubX % 3)) {	//Xtra Page
				uwSuperpageIdx = IM_B27B_AXIS_INVALID_PAGE_IDX;
			}
		}
		else if ((2 == uwWordLine) || (3 == uwWordLine)) {
			if (2 > (ubX % 3)) {	//Upper, Xtra Page
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_2_3_UPPER_EXTRA_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_2;
				ubWordLineBase = IM_B27B_AXIS_WORDLINE_2_3_WORDLINE_BASE;
			}
			else {	//Lower Page
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_2_3_LOWER_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_0;
				ubWordLineBase = IM_B27B_AXIS_WORDLINE_2_3_WORDLINE_BASE;
			}
		}
		else if ((4 <= uwWordLine) && (44 >= uwWordLine)) {
			uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_4_44_PAGE_BASE;
			ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_2;
			ubWordLineBase = IM_B27B_AXIS_WORDLINE_4_46_WORDLINE_BASE;
		}
		else if (45 == uwWordLine) {
			if (2 > (ubX % 3)) {	//Upper, Xtra Page
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_45_UPPER_EXTRA_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_3;
			}
			else {	//Lower Page
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_45_46_LOWER_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_2;
				ubWordLineBase = IM_B27B_AXIS_WORDLINE_4_46_WORDLINE_BASE;
			}
		}
		else if (46 == uwWordLine) {
			if (2 > (ubX % 3)) {	//Upper, Xtra Page
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_46_UPPER_EXTRA_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_4;
			}
			else {	//Lower Page
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_45_46_LOWER_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_2;
				ubWordLineBase = IM_B27B_AXIS_WORDLINE_4_46_WORDLINE_BASE;
			}
		}
		else if (47 == uwWordLine) {
			uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_47_PAGE_BASE;
			ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_5;
			if (0 == (ubX % 3)) {	//Xtra Page
				uwSuperpageIdx = IM_B27B_AXIS_INVALID_PAGE_IDX;
			}
		}
		else if (48 == uwWordLine) {
			if (2 > (ubX % 3)) {	//Upper, Xtra Page
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_48_UPPER_EXTRA_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_3;
			}
			else {	//Lower Page
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_48_LOWER_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_4;
			}
		}
		else if (49 == uwWordLine) {
			uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_49_PAGE_BASE;
			ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_5;
			if (0 == (ubX % 3)) {	//Xtra Page
				uwSuperpageIdx = IM_B27B_AXIS_INVALID_PAGE_IDX;
			}
		}
		else if (50 == uwWordLine) {
			if (2 > (ubX % 3)) {	//Upper, Xtra Page
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_50_UPPER_EXTRA_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_2;
				ubWordLineBase = uwWordLine;
			}
			else {	//Lower Page
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_50_LOWER_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_0;
				ubWordLineBase = uwWordLine;
			}
		}
		else if (51 == uwWordLine) {
			uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_51_PAGE_BASE;
			ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_1;
			if (0 == (ubX % 3)) {	//Xtra Page
				uwSuperpageIdx = IM_B27B_AXIS_INVALID_PAGE_IDX;
			}
		}
		else if (52 == uwWordLine) {
			if (2 > (ubX % 3)) {	//Upper, Xtra Page
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_52_UPPER_EXTRA_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_2;
				ubWordLineBase = IM_B27B_AXIS_WORDLINE_52_96_WORDLINE_BASE;
			}
			else {	//Lower Page
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_52_LOWER_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_0;
				ubWordLineBase = uwWordLine;
			}
		}
		else if (53 == uwWordLine) {
			if (2 > (ubX % 3)) {	//Upper, Xtra Page
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_53_UPPER_EXTRA_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_2;
				ubWordLineBase = IM_B27B_AXIS_WORDLINE_52_96_WORDLINE_BASE;
			}
			else {	//Lower Page
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_53_LOWER_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_4;
			}
		}
		else if ((54 <= uwWordLine) && (94 >= uwWordLine)) {
			uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_54_94_PAGE_BASE;
			ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_2;
			ubWordLineBase = IM_B27B_AXIS_WORDLINE_52_96_WORDLINE_BASE;
		}
		else if (95 == uwWordLine) {
			if (2 > (ubX % 3)) {	//Upper, Xtra Page
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_95_UPPER_EXTRA_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_3;
			}
			else {	//Lower Page
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_95_96_LOWER_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_2;
				ubWordLineBase = IM_B27B_AXIS_WORDLINE_52_96_WORDLINE_BASE;
			}
		}
		else if (96 == uwWordLine) {
			if (2 > (ubX % 3)) {	//Upper, Xtra Page
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_96_UPPER_EXTRA_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_4;
			}
			else {	//Lower Page
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_95_96_LOWER_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_2;
				ubWordLineBase = IM_B27B_AXIS_WORDLINE_52_96_WORDLINE_BASE;
			}
		}
		else if (97 == uwWordLine) {
			uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_97_PAGE_BASE;
			ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_5;
			if (0 == (ubX % 3)) {	//Xtra Page
				uwSuperpageIdx = IM_B27B_AXIS_INVALID_PAGE_IDX;
			}
		}
		else if (98 == uwWordLine) {
			uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_98_PAGE_BASE;
			ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_4;
			if (2 > (ubX % 3)) {	//Upper, Xtra Page
				uwSuperpageIdx = IM_B27B_AXIS_INVALID_PAGE_IDX;
			}
		}
		else {
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
		}
		if (IM_B27B_AXIS_INVALID_PAGE_IDX == uwSuperpageIdx) {
			break;
		}
		switch (ubCase) {
		case IM_B27B_AXIS_X_Y_TO_PAGE_CASE_0:
			uwSuperpageIdx += ((uwWordLine - ubWordLineBase) * 12  + (ubX / 3));
			break;
		case IM_B27B_AXIS_X_Y_TO_PAGE_CASE_1:
			uwSuperpageIdx += ((ubX / 3) * 2 + (2 - (ubX % 3)));
			break;
		case IM_B27B_AXIS_X_Y_TO_PAGE_CASE_2:
			uwSuperpageIdx += (((uwWordLine - ubWordLineBase) - ((2 == (ubX % 3)) ? 2 : 0)) * 36  + ubX);
			break;
		case IM_B27B_AXIS_X_Y_TO_PAGE_CASE_3:
			uwSuperpageIdx += ((ubX / 3) * 4 + (ubX % 3));
			break;
		case IM_B27B_AXIS_X_Y_TO_PAGE_CASE_4:
			uwSuperpageIdx += ubX;
			break;
		case IM_B27B_AXIS_X_Y_TO_PAGE_CASE_5:
			uwSuperpageIdx += ((ubX / 3) * 4 + (2 - (ubX % 3)));
			break;
		default:
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
			break;
		}
		break; // break Case B27B

#elif (IM_N28)
	case 0xC6: // N28
		ubCase = 0xFF;
		U8 ubOffset = 0;
		U8 ubLXUNumber = ubX % 4; // LUXT
		U16 uwPageIdx = 0xFFFF, uwBase;

		switch (ubLXUNumber) {
		case 2: //X
		case 1: //U
			if (uwY == 0 || uwY == 98) {
				break;
			}
			uwY += 1;
		case 0: //L
			if ((uwY > 4) && (uwY < 51)) { // 5 ~ 50
				uwBase = 132;
				uwY -= 5;
				ubCase = 2;
			}
			else if ((uwY > 56) && (uwY < 98)) { // 57 ~ 97
				uwBase = 2580;
				uwY -= 57;
				ubCase = 2;
			}
			else if (uwY == 0 || uwY == 1) { // 0 1
				uwBase = 0;
				ubCase = 0;
			}
			else if ((uwY > 1) && (uwY < 5)) { // 2 3 4
				uwBase = 24;
				uwY -= 2;
				ubCase = 1;
			}
			else if (uwY == 51 || uwY == 52) { // 51 52
				uwBase = 2340;
				uwY -= 51;
				ubCase = 1;
			}
			else if (uwY == 53) { // 53
				uwBase = 2412;
				uwY = 0;
				ubCase = 2;
			}
			else if (uwY == 54) { // 54
				uwBase = 2460;
				uwY = 0;
				ubCase = 1;
			}
			else if (uwY == 55) { // 55
				uwBase = 2496;
				uwY = 0;
				ubCase = 2;
			}
			else if (uwY == 56) { //56
				uwBase = 2544;
				uwY = 0;
				ubCase = 1;
			}
			else if (uwY == 98) { // 98
				uwBase = 4548;
				uwY = 0;
				ubCase = 3;
			}
			break;
		case 3: //T
			ubCase = 2;
			if ((uwY > 1) && (uwY < 47)) { // 2 ~ 46
				uwBase = 132;
				uwY -= 2;
			}
			else if ((uwY > 53) && (uwY < 95)) { // 54 ~ 94
				uwBase = 2580;
				uwY -= 54;
			}
			else if (uwY == 48) { // 48
				uwBase = 2292;
				uwY = 0;
			}
			else if (uwY == 50) { // 50
				uwBase = 2412;
				uwY = 0;
			}
			else if (uwY == 52) { // 52
				uwBase = 2496;
				uwY = 0;
			}
			else if (uwY == 95 || uwY == 96) { // 95 96
				uwBase = 4548;
				if (uwY == 96) {
					ubOffset = 3;
				}
				uwY = 0;
				ubCase = 3;
			}
			else {
				ubCase = 0xFF;
			}
			break;
		}
		if (ubCase == 0) { // R0
			uwPageIdx = uwY * 12 + (ubX >> 2);
		}
		else if (ubCase == 1) {
			uwPageIdx = uwBase + (ubX >> 2) * 3 + ((3 - (((ubX & 03) % 3))) % 3) + uwY * 36; // R1
		}
		else if (ubCase == 2) {
			uwPageIdx = uwBase + (ubX & ~(0x3)) + ((4 - (ubX & 0x3)) & 0x3) + uwY * 48; // R2
		}
		else if (ubCase == 3) {
			uwPageIdx = uwBase + ((ubX >> 2) * 5) + ((4 - (ubX & 0x3)) & 0x3) + ubOffset; // R3 missing Y
		}

		uwSuperpageIdx = uwPageIdx;
		break;
#endif

#if (IM_N18)
	case 0xAA:  //N18
		// TBD
		break; // break Case N18
#endif /* (IM_N18) */

	}
#endif  /* (IM_B47R) */  /* (IM_N48R) */
#endif /*(MICRON_FSP_EN)*/
	return uwSuperpageIdx;
}

U16 VtSweepSetMLBiFPU(U16 uwTrimReg, U8 ubDie, U8 ubInputData)
{
	U16 uwFPUPtr = FPU_PTR_OFFSET(fpu_micron_set_mlbi_cmd_by_MT[0]); //temp use 0
	U16 *puwFPU = (U16 *)(IRAM_BASE + uwFPUPtr);

	puwFPU[0] = FPU_CMD(0xEB);

	puwFPU[1] = FPU_ADR_1B((uwTrimReg & BIT_MASK(8)));
	puwFPU[2] = FPU_ADR_1B(((uwTrimReg >> 8) & BIT_MASK(8)));
	puwFPU[3] = FPU_ADR_1B(ubDie); //LUN

	// Delay
	if (FIP_FLASH_CLOCK_400MHZ == gFlhEnv.ubTargetFlashClock) {
		// flh clk 400
		puwFPU[4] = FPU_DLY(0x15);		// Decimal 21
	}
	else if (FIP_FLASH_CLOCK_333MHZ == gFlhEnv.ubTargetFlashClock) {
		// flh clk 333
		puwFPU[4] = FPU_DLY(0xF);		// Decimal 15
	}
	else {
		puwFPU[4] = FPU_DLY(0x10);		// Decimal 16
	}

	puwFPU[5] = FPU_DAT_W_1B(ubInputData);
	puwFPU[6] = FPU_END;

	return uwFPUPtr;
}

U16 VtSweepCheckMLBiFPU(U8 ubCompareMLBiData)
{
	U8 ubCompareFeatureData[PARAMETER_NUM_PER_FPU] = {0};
	U8 ubi;
	// Get FPU PTR
	U16 uwFPUPtr = FPU_PTR_OFFSET(fpu_read_and_compare_feature_data);
	U16 *puwFPU = (U16 *)(IRAM_BASE + uwFPUPtr);

	puwFPU[1] = FPU_DLY(0x10);

	ubCompareFeatureData[0] = ubCompareMLBiData;

	for (ubi = 0; ubi < 4; ubi++) {
		puwFPU[2 + (ubi << 1)] = FPU_DAT_R_CMP(ubCompareFeatureData[ubi]);
		puwFPU[3 + (ubi << 1)] = (0 == ubi) ? FPU_DAT_R_MASK(0xFF) : FPU_DAT_R_MASK(0); //Just compare P1 value
	}

	return uwFPUPtr;
}

U16 VtSweepGetMLBiFPU(U16 uwTrimReg, U8 ubDie)
{
	// Get FPU PTR
	U16 uwFPUPtr = FPU_PTR_OFFSET(fpu_micron_get_mlbi_cmd_by_MT[0]); //temp use 0
	U16 *puwFPU = (U16 *)(IRAM_BASE + uwFPUPtr);

	puwFPU[0] = FPU_CMD(0xEA);
	puwFPU[1] = FPU_ADR_1B((uwTrimReg & BIT_MASK(8)));
	puwFPU[2] = FPU_ADR_1B(((uwTrimReg >> 8) & BIT_MASK(8)));
	puwFPU[3] = FPU_ADR_1B(ubDie);
	puwFPU[4] = FPU_END;

	return uwFPUPtr;
}

void VtSweepIssueMTData(U8 ubQIndex, U8 ubMTIndex, U8 ubDie, U8 ubALU, U32 ulErrorFSA)
{
	U8  ubIdx;
	L4KTable16B_t *pL4KSparePtr;

	MTCfg_t uoMTCfg = {0};
	COP0_Clksw_t clksw_def_0  = {0};
	COP0_Attr_t  mt_attr_read = {0};
	FlhMT_t      MT           = {{0}};

	FlhMT_t *pMTP = NULL;

	pL4KSparePtr = (L4KTable16B_t *)(IRAM_BASE + SPR_RETRY_OFF);
	//
	clksw_def_0.ulAll  = R32_COP0[R32_COP0_CLKSW_ATTR0];
	mt_attr_read.ulAll = R32_COP0[R32_COP0_ATTR0];

	pMTP = (FlhMT_t *)  M_MT_ADDR(ubMTIndex);

	MT.dma.btMTPFormat = 1;

	MT.dma.ALUSelect       = ubALU;
	MT.dma.btCRCCheckDis   = TRUE;
	MT.dma.btConversionBypass = TRUE;
	MT.cmd.btInterruptVectorEn = TRUE;

	MT.dma.btiFSAEn	      = TRUE;
	MT.dma.btLDPCCorrectEn = FALSE;

	MT.dma.NorTarCPU = MT_TARGET_CPU0;
	MT.dma.ErrTarCPU = MT_TARGET_CPU0;

	MT.dma.btUpdPollingSequence  = TRUE;
	MT.dma.POL_SEQ_SEL          = POL_SEQ_FPU_ENTRY_READ_STATUS_LUN0_BUSY_20 + ubDie;
	MT.dma.btBusy = TRUE;
	MT.cmd.btCESelectMode        = 1;

	MT.dma.uliFSA0_1	 = ulErrorFSA;

	MT.dma.btIoType      = clksw_def_0.A.btIo_typ;
	MT.dma.btFpuPCAEn	= TRUE;
	MT.dma.FCLK_DIV      = clksw_def_0.A.ubFclkDiv;
	MT.dma.FlashType     = clksw_def_0.A.ubFlh_typ;

	MT.dma.uwFPUPtr    = FPU_PTR_OFFSET(fpu_entry_dma_r_05_e0);    //FPU_PTR_OFFSET(fpu_entry_dma_r_05_e0); // read data
	MT.dma.ubCEValue   = ubQIndex;

	MT.dma.SeedInit = BURNER_RANDOM_SEED;

	MT.dma.L4KSparePtr = (U32) ((U32) pL4KSparePtr - IRAM_AXI_BASE);
	MT.dma.FrameNum      = 4;
	MT.dma.btZipEn       = FALSE;
	MT.dma.btCompareEn   = FALSE;

	*pMTP = MT;

	for (ubIdx = (U8)(ulErrorFSA & gPCARule_Entry.ulMask); ubIdx < gub4kEntrysPerPlane; ubIdx++) {

		pL4KSparePtr->BitMap.Read.ulLCA         = VTSWEEP_DUMMY_FSA;
		pL4KSparePtr->BitMap.Read.FW            = 0;
		pL4KSparePtr->BitMap.Read.ubBufferValid = 0;
		pL4KSparePtr->BitMap.Read.Zinfo         = MAX_ZINFO;
		pL4KSparePtr->BitMap.Read.PCA           = 0;
		pL4KSparePtr->BitMap.Read.BADR          = 0;

		pL4KSparePtr++;
	}
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, ubIdx == gub4kEntrysPerPlane);

	uoMTCfg.uoAll = (((ubMTIndex & MT_IDX_MASK) << MT_IDX_SHIFT) | (ubQIndex << QUE_IDX_SHIFT));
	uoMTCfg.bits_lb_ofst.ubDIE_NUM = ubDie;
	FlaGlobalTrigger(&uoMTCfg);
}

void VtSweepTrimDMAMT(U8 ubQIndex, U8 ubMTIndex, U8 ubDie, U32 ulBufAddr)
{
	L4KTable16B_t *pL4KSparePtr;
	MTCfg_t uoMTCfg = {0};
	COP0_Clksw_t clksw_def_0  = {0};
	FlhMT_t      MT           = {{0}};

	FlhMT_t *pMTP = NULL;
	pL4KSparePtr = (L4KTable16B_t *)(IRAM_BASE + SPR_RETRY_OFF + ((ubMTIndex - MT_RETRY_START_INDEX) << 4)); // It's for 16 MT.

	clksw_def_0.ulAll  = R32_COP0[R32_COP0_CLKSW_ATTR0];

	pMTP = (FlhMT_t *)  M_MT_ADDR(ubMTIndex);

	MT.dma.btMTPFormat = 1;

	MT.dma.ALUSelect       = 0;
	MT.dma.btCRCCheckDis   = TRUE;
	MT.dma.btConversionBypass = TRUE;
	MT.cmd.btInterruptVectorEn = TRUE;

	MT.dma.btiFSAEn	      = TRUE;
	MT.dma.btLDPCCorrectEn = FALSE;

	MT.dma.NorTarCPU = MT_TARGET_CPU0;
	MT.dma.ErrTarCPU = MT_TARGET_CPU0;

	MT.dma.btUpdPollingSequence  = TRUE;
	MT.dma.POL_SEQ_SEL          = POL_SEQ_FPU_ENTRY_READ_STATUS_LUN0_BUSY_20 + ubDie;
	MT.dma.btBusy = TRUE;
	MT.cmd.btCESelectMode        = 1;

	MT.dma.uliFSA0_1	 = 0;

	MT.dma.btIoType      = clksw_def_0.A.btIo_typ;
	MT.dma.btFpuPCAEn	 = TRUE;
	MT.dma.FCLK_DIV      = clksw_def_0.A.ubFclkDiv;
	MT.dma.FlashType     = clksw_def_0.A.ubFlh_typ;

	MT.dma.uwFPUPtr    = FPU_PTR_OFFSET(fpu_entry_getfeature_dma); // read data no ADR_GEN
	MT.dma.ubCEValue   = ubQIndex;

	MT.dma.SeedInit 	= BURNER_RANDOM_SEED;

	MT.dma.L4KSparePtr	 = (U32) ((U32) pL4KSparePtr - IRAM_AXI_BASE);
	MT.dma.FrameNum      = 1;
	MT.dma.btZipEn       = FALSE;
	MT.dma.btCompareEn   = FALSE;
	MT.dma.btBufferMode	 = FALSE;
	*pMTP = MT;

	pL4KSparePtr->BitMap.Read.ulLCA         = VTSWEEP_DUMMY_FSA;
	pL4KSparePtr->BitMap.Read.FW            = (U32) pL4KSparePtr;
	pL4KSparePtr->BitMap.Read.ubBufferValid = BIT0;
	pL4KSparePtr->BitMap.Read.Zinfo         = MAX_ZINFO;
	pL4KSparePtr->BitMap.Read.PCA           = 0;
	pL4KSparePtr->BitMap.Read.BADR          = M_SET_L4KBADR(ulBufAddr);

	uoMTCfg.uoAll = (((ubMTIndex & MT_IDX_MASK) << MT_IDX_SHIFT) | (ubQIndex << QUE_IDX_SHIFT));
	uoMTCfg.bits_lb_ofst.ubDIE_NUM = ubDie;
	FlaGlobalTrigger(&uoMTCfg);
}

void VtSweepSetTrimRegister(U8 ubChannel, U8 ubCE, U8 ubDieIdx, U8 ubInputData, U16 uwTrimReg)
{
	U8 ubMTIdx[3];
	U16 uwFPU = 0;
	U8 ubGlobalCE = ((ubCE * MAX_CHANNEL) + ubChannel);

	ubMTIdx[0] = FlaGetFreeMTIndex();
	uwFPU = VtSweepSetMLBiFPU(uwTrimReg, ubDieIdx, ubInputData);
	FIPDirectPushMTCmd(ubGlobalCE, ubMTIdx[0], uwFPU, VTSWEEP_DUMMY_FSA, VTSWEEP_DUMMY_FSA, FIP_ALU_SELECT_2, POL_SEQ_FPU_ENTRY_READ_STATUS_LUN0_BUSY_20 + ubDieIdx, FIP_INVALID_VIRTUAL_ADDR);

	ubMTIdx[1] = FlaGetFreeMTIndex();
	uwFPU = VtSweepGetMLBiFPU(uwTrimReg, ubDieIdx);
	FIPDirectPushMTCmd(ubGlobalCE, ubMTIdx[1], uwFPU, VTSWEEP_DUMMY_FSA, VTSWEEP_DUMMY_FSA, FIP_ALU_SELECT_2, POL_SEQ_FPU_ENTRY_READ_STATUS_LUN0_BUSY_20 + ubDieIdx, FIP_INVALID_VIRTUAL_ADDR);

	ubMTIdx[2] = FlaGetFreeMTIndex();
	uwFPU = VtSweepCheckMLBiFPU(ubInputData);
	FIPDirectPushMTCmd(ubGlobalCE, ubMTIdx[2], uwFPU, VTSWEEP_DUMMY_FSA, VTSWEEP_DUMMY_FSA, FIP_ALU_SELECT_2, POL_SEQ_FPU_ENTRY_READ_STATUS_LUN0_BUSY_20 + ubDieIdx, FIP_INVALID_VIRTUAL_ADDR);

	uwFPU = 0;
	while (1) {
		if (gMTMgr.ubMTDoneMsg[ubMTIdx[uwFPU] - MT_RETRY_START_INDEX].btAllDone) {
			if (gMTMgr.ubMTDoneMsg[ubMTIdx[uwFPU] - MT_RETRY_START_INDEX].btMTStop) {
				gpFrey2VtSweep->ubMLBiVerifyFail = TRUE;
			}
			FlaAddFreeMTIndex(ubMTIdx[uwFPU]);
			uwFPU++;
			if (uwFPU == 3) {
				break;
			}
		}
		FIPDelegateCmd();
	}
}

void VtSweepReadData(U8 ubChannel, U8 ubCE, U8 ubDieIdx, U8 ubALUSelect, U32 ulErrorFSA)
{
	U8 ubMTIdx[2];
	U16 uwFPU = 0;
	U8 ubReadCmdVirtualAddr;
	U8 ubGlobalCE = ((ubCE * MAX_CHANNEL) + ubChannel);

	ubMTIdx[0] = FlaGetFreeMTIndex();
	uwFPU = (ubALUSelect >= COP0_PCA_RULE_2) ? FPU_PTR_OFFSET(fpu_entry_slc_1p_30_read) : FPU_PTR_OFFSET(fpu_entry_tlc_1p_30_read);
	ubReadCmdVirtualAddr = (ubALUSelect & ALU_RULE_SLC_BIT) ? 0 : 2;

	FIPDirectPushMTCmd(ubGlobalCE, ubMTIdx[0], uwFPU, ulErrorFSA, VTSWEEP_DUMMY_FSA, ubALUSelect, POL_SEQ_FPU_ENTRY_READ_STATUS_LUN0_BUSY_20 + ubDieIdx, ubReadCmdVirtualAddr);

	ubMTIdx[1] = FlaGetFreeMTIndex();
	VtSweepIssueMTData(ubGlobalCE, ubMTIdx[1], ubDieIdx, ubALUSelect, ulErrorFSA);

	uwFPU = 0;
	while (1) {
		if (gMTMgr.ubMTDoneMsg[ubMTIdx[uwFPU] - MT_RETRY_START_INDEX].btAllDone) {
			FlaAddFreeMTIndex(ubMTIdx[uwFPU]);
			uwFPU++;
			if (uwFPU == 2) {
				break;
			}
		}
		FIPDelegateCmd();
	}
}

void VtSweepGetTrimRegister(U8 ubChannel, U8 ubCE, U8 ubDieIdx, U16 uwTrimReg)
{
	U8 ubMTIdx[2];
	U16 uwFPU = 0;
	U8 ubGlobalCE = ((ubCE * MAX_CHANNEL) + ubChannel);

	ubMTIdx[0] = FlaGetFreeMTIndex();
	uwFPU = VtSweepGetMLBiFPU(uwTrimReg, ubDieIdx);
	FIPDirectPushMTCmd(ubGlobalCE, ubMTIdx[0], uwFPU, VTSWEEP_DUMMY_FSA, VTSWEEP_DUMMY_FSA, FIP_ALU_SELECT_2, POL_SEQ_FPU_ENTRY_READ_STATUS_LUN0_BUSY_20 + ubDieIdx, FIP_INVALID_VIRTUAL_ADDR);

	ubMTIdx[1] = FlaGetFreeMTIndex();
	VtSweepTrimDMAMT(ubGlobalCE, ubMTIdx[1], ubDieIdx, (U32)gpFrey2VtSweep);

	uwFPU = 0;
	while (1) {
		if (gMTMgr.ubMTDoneMsg[ubMTIdx[uwFPU] - MT_RETRY_START_INDEX].btAllDone) {
			FlaAddFreeMTIndex(ubMTIdx[uwFPU]);
			uwFPU++;
			if (uwFPU == 2) {
				break;
			}
		}
		FIPDelegateCmd();
	}
}


void MicronVtSweep(U8 ubALUSelect, U32 ulErrorFSA, U8 ubOperationMode)
{
	//-------------------------------------------
	//	Notice ulErrorPCA !! Only remap unit here
	//-------------------------------------------
	U8 ubBackUpNum, ubCH, ubCE, ubDie, ubScanStop, ubSubIdx;
	U8 ubSetTrimValue_L, ubSetTrimValue_H, ubTrimLoopMod = 4;
	U16 uwPhysicalBlkIdx, uwSrcPage, uwReadPage, uwWordlineIdx, uwTrimLP[2], uwQLCTrimLPGroup[6], *puwTrimRegSelect, uwi;
	U16 uwSetTrimValue;
	FTLWordLineTypeEnum_t ubWordLineType;
	REG32 *pFlaReg = NULL;
	//U16 uwTrimRegSLC[VTSWEEP_SLC_BK_NUM] = {0x0771, 0x078B, 0x07A5, 0x07BF, 0x07D9, 0x07F3, 0x080D, 0x0827};
	U16 uwTrimRegQLC[VTSWEEP_QLC_BK_NUM] = {0x040, 0x043, 0x045, 0x04A, 0x050, 0x053, 0x055, 0x05A,
			0x060, 0x063, 0x065, 0x06A, 0x070, 0x073, 0x075, 0x07A,
			0x080, 0x083, 0x085, 0x08A, 0x090, 0x093, 0x095, 0x09A,
			0x0A0, 0x0A3, 0x0A5, 0x0AA, 0x0B0, 0x0B3, 0x0B5, 0x0BA
		};

	ulErrorFSA = (ulErrorFSA >> gub4kEntrysPerPlaneLog) << gub4kEntrysPerPlaneLog; //Remove frame
	ubDie = ((ulErrorFSA >> gPCARule_LUN.ubShift[ubALUSelect]) & BIT_MASK(gPCARule_LUN.ubBit_No));
	ubCH = ((ulErrorFSA >> gPCARule_Channel.ubShift[ubALUSelect]) & BIT_MASK(gPCARule_Channel.ubBit_No));
	ubCE = ((ulErrorFSA >> gPCARule_Bank.ubShift[ubALUSelect]) & BIT_MASK(gPCARule_Bank.ubBit_No));
	uwPhysicalBlkIdx = (((ulErrorFSA >> gPCARule_Block.ubShift[ubALUSelect]) & BIT_MASK(gPCARule_Block.ubBit_No)) << gPCARule_Plane.ubBit_No)
		| ((ulErrorFSA >> gPCARule_Plane.ubShift[ubALUSelect]) & BIT_MASK(gPCARule_Plane.ubBit_No));

	//------------------------------
	//	trim lp address select
	//------------------------------
	pFlaReg = R32_FCTL_CH[ubCH];
	pFlaReg[R32_FCTL_INT_CFG] &= ~(ERASE_FRAME_INT_EN_BIT);

	uwSrcPage = ((ulErrorFSA >> gPCARule_Page.ubShift[ubALUSelect]) & BIT_MASK(gPCARule_Page.ubBit_No));
	uwWordlineIdx = FTLGetCoord(uwSrcPage, IM_GETCOORD_Y_VAL);

	if (ubALUSelect >= COP0_PCA_RULE_2) {
		if ((0 == uwWordlineIdx) || (88 == uwWordlineIdx) || (89 == uwWordlineIdx) || (177 == uwWordlineIdx)) { //Edge WL
			uwTrimLP[0] = 0x01D4; //[7:0]
			uwTrimLP[1] = 0x01D5; //[1:0]
		}
		else {
			uwTrimLP[0] = 0x01DE; //[7:0]
			uwTrimLP[1] = 0x01DF; //[1:0]
		}

		ubWordLineType = SLC_WL;
		//initial trim reg select
		ubBackUpNum = VTSWEEP_SLC_BK_NUM;
	}
	else {
		if ((0 == uwWordlineIdx) || (88 == uwWordlineIdx) || (89 == uwWordlineIdx) || (177 == uwWordlineIdx)) {
			uwTrimLP[0] = 0x01B6; //[7:0]
			uwTrimLP[1] = 0x01B7; //[1:0]

			ubWordLineType = MLC_WL;
			//initial trim reg select
			ubBackUpNum = VTSWEEP_MLC_BK_NUM;
		}
		else {
			uwTrimLP[0] = 0x00D0; //[7:0]
			uwTrimLP[1] = 0x00D1; //[1:0]

			uwQLCTrimLPGroup[0] = 0x00EE; //[7:0]
			uwQLCTrimLPGroup[1] = 0x00EF; //[1:0]
			uwQLCTrimLPGroup[2] = 0x0102; //[7:0]
			uwQLCTrimLPGroup[3] = 0x0103; //[1:0]
			uwQLCTrimLPGroup[4] = 0x0134; //[7:0]
			uwQLCTrimLPGroup[5] = 0x0135; //[1:0]

			ubWordLineType = QLC_WL;
			//initial trim reg select
			ubBackUpNum = VTSWEEP_QLC_BK_NUM;
			puwTrimRegSelect = &uwTrimRegQLC[0];
		}
	}

	//------------------------------
	//	Sample LP page for Vt Sweep
	//------------------------------
	uwReadPage = uwSrcPage;
	if (ubWordLineType != SLC_WL) {
		ubSubIdx = FTLGetCoord(uwSrcPage, IM_GETCOORD_X_VAL);
		ubSubIdx = (ubSubIdx / 4) * 4; // LP

		uwReadPage = FTLInvertCoord(ubSubIdx, uwWordlineIdx);
#if (!BURNER_MODE_EN)
		//uwReadPage = FTLGCGetPageFromCoord(ubSubIdx, uwWordlineIdx);
#endif
		ulErrorFSA = ((ulErrorFSA & ~(BIT_MASK(gPCARule_Page.ubBit_No) << gPCARule_Page.ubShift[ubALUSelect]))
				| ((uwReadPage & BIT_MASK(gPCARule_Page.ubBit_No)) << gPCARule_Page.ubShift[ubALUSelect]));
	}


	//------------------------------
	//	Setting parameters
	//------------------------------
	gpFrey2VtSweep->ubValidFlag = FALSE;

	gpFrey2VtSweep->ubChannel = ubCH;
	gpFrey2VtSweep->ubBank = ubCE;
	gpFrey2VtSweep->ubDie = ubDie;
	gpFrey2VtSweep->uwPhysicalBlock = uwPhysicalBlkIdx;
	gpFrey2VtSweep->uwPhysicalSrcPage = uwSrcPage;
	gpFrey2VtSweep->uwPhysicalReadPage = uwReadPage;
	gpFrey2VtSweep->ubALUSelect = ubALUSelect;
	gpFrey2VtSweep->ubWordLineType = ubWordLineType;
	gpFrey2VtSweep->ubMLBiVerifyFail = FALSE;

	//---------------------------------
	//	BackUp & clear trim offset reg
	//---------------------------------
	for (uwi = 0; uwi < ubBackUpNum; uwi++) {
		//Get initial value
		VtSweepGetTrimRegister(ubCH, ubCE, ubDie, puwTrimRegSelect[uwi]);
		//BackUp
		gpFrey2VtSweep->ubBackUpMLBi[uwi] = (U8)(gpFrey2VtSweep->ulHistogramData[0] & BIT_MASK(8));
		//Clear
		VtSweepSetTrimRegister(ubCH, ubCE, ubDie, VTSWEEP_TRIM_CLEAR, puwTrimRegSelect[uwi]);
	}
	//------------------------------
	//	Backup trim lp
	//------------------------------
	for (uwi = 0; uwi < VTSWEEP_TRIM_LP_BK_NUM; uwi++) {
		VtSweepGetTrimRegister(ubCH, ubCE, ubDie, uwTrimLP[uwi]);
		gpFrey2VtSweep->ubBackUpMLBi[ubBackUpNum + uwi] = (U8)(gpFrey2VtSweep->ulHistogramData[0] & BIT_MASK(8));
	}

	if (ubWordLineType == QLC_WL) {
		ubSetTrimValue_L = (VTSWEEP_TRIM_352H & BIT_MASK(8));

		for (uwi = 0; uwi < VTSWEEP_TRIM_LP_QLC_EXT_BK_NUM; uwi++) {
			VtSweepGetTrimRegister(ubCH, ubCE, ubDie, uwQLCTrimLPGroup[uwi]);
			gpFrey2VtSweep->ubBackUpMLBi[ubBackUpNum + VTSWEEP_TRIM_LP_BK_NUM + uwi] = (U8)(gpFrey2VtSweep->ulHistogramData[0] & BIT_MASK(8));
			if (uwi % 2) {  // [1:0]
				ubSetTrimValue_H = (((VTSWEEP_TRIM_352H >> 8) & BIT_MASK(2)) | (gpFrey2VtSweep->ubBackUpMLBi[VTSWEEP_BK_NUM + VTSWEEP_TRIM_LP_BK_NUM + uwi] & ~(BIT(2))));
				VtSweepSetTrimRegister(ubCH, ubCE, ubDie, ubSetTrimValue_H, uwQLCTrimLPGroup[uwi]);
			}
			else {  // [7:0]
				VtSweepSetTrimRegister(ubCH, ubCE, ubDie, (U8)(VTSWEEP_TRIM_352H & BIT_MASK(8)), uwQLCTrimLPGroup[uwi]);
			}
		}
	}

	//------------------------------
	//	Scan Vt Sweep
	//------------------------------
	R32_FCTL_CH[ubCH][R32_FCTL_CNT_ONE] |= SET_COUNTER_MODE_EN; //default enable
	R32_FCTL_CH[ubCH][R32_FCTL_CNT_ONE] &= CLR_COUNT_ZERO; //Count zero

	ubScanStop = 0;

	for (uwi = 0; uwi < VTSWEEP_RANGE; uwi++) {
		if (ubScanStop < VTSWEEP_STOP_SCAN_THR) {
			uwSetTrimValue = uwi * ubTrimLoopMod;
			ubSetTrimValue_L = (U8)(uwSetTrimValue & BIT_MASK(8));
			ubSetTrimValue_H = (U8)((gpFrey2VtSweep->ubBackUpMLBi[ubBackUpNum + 1] & ~(BIT_MASK(2))) | ((uwSetTrimValue >> 8) & BIT_MASK(2)));
			//Adjust Trim lp
			VtSweepSetTrimRegister(ubCH, ubCE, ubDie, ubSetTrimValue_L, uwTrimLP[0]);
			VtSweepSetTrimRegister(ubCH, ubCE, ubDie, ubSetTrimValue_H, uwTrimLP[1]);

			//Read
			VtSweepReadData(ubCH, ubCE, ubDie, ubALUSelect, ulErrorFSA);

			//Record count zero
			gpFrey2VtSweep->ulHistogramData[uwi] = M_FIP_CHANNEL_GET_COUNTONE_RESULT(ubCH);

			if (gpFrey2VtSweep->ulHistogramData[uwi] == 0) {
				ubScanStop++;
			}
		}
		else {
			gpFrey2VtSweep->ulHistogramData[uwi] = 0;
		}

	}

	gpFrey2VtSweep->ubValidFlag = TRUE;

	//------------------------------
	//	Restore trim lp & external (QLC only)
	//------------------------------
	for (uwi = 0; uwi < VTSWEEP_TRIM_LP_BK_NUM; uwi++) {
		VtSweepSetTrimRegister(ubCH, ubCE, ubDie, gpFrey2VtSweep->ubBackUpMLBi[ubBackUpNum + uwi], uwTrimLP[uwi]);
	}
	if (ubWordLineType == QLC_WL) {
		for (uwi = 0; uwi < VTSWEEP_TRIM_LP_QLC_EXT_BK_NUM; uwi++) {
			VtSweepSetTrimRegister(ubCH, ubCE, ubDie, gpFrey2VtSweep->ubBackUpMLBi[ubBackUpNum + VTSWEEP_TRIM_LP_BK_NUM + uwi], uwQLCTrimLPGroup[uwi]);
		}
	}

	//---------------------------------
	//	Restore trim offset reg (QLC only)
	//---------------------------------

	if (ubWordLineType == QLC_WL) {
		for (uwi = 0; uwi < ubBackUpNum; uwi++) {
			VtSweepSetTrimRegister(ubCH, ubCE, ubDie, gpFrey2VtSweep->ubBackUpMLBi[uwi], puwTrimRegSelect[uwi]);
		}
	}

	pFlaReg[R32_FCTL_INT_CFG] |= (ERASE_FRAME_INT_EN_BIT);
}

#endif /* VT_SWEEP_EN */
