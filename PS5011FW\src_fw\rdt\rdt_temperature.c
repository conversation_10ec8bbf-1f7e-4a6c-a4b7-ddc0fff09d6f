/*
 * rdt_temperature.c
 *
 *  Created on: 2020年3月11日
 *      Author: user
 */

#include "rdt_api.h"
#include "hal/pic/i2c/i2cm_reg.h"
#include "hal/pic/i2c/i2cm_api.h"
#include "hal/sys/api/mux/mux_api.h"

#include "vuc/VUC_MicronGetTemperature.h"
#include "tt/TT.h"
#include "tt/TT_api.h"

#if (RDT_MODE_EN)

#define BI_ROOM_DELTA                   (5) //U17 setting from lee_jin mail 20210305

#if !RDT_NEW_THERMAL_DETECT_FLOW && RDT_THERMAL_DETECT //modify thermal detect method from SSDRDT-2179 @20211206
U32 gulThermalDetectTimeStamp = 0;
U8 gubUnderCriteriaTimes = 0;
U8 gubFlashTemperature[DETECT_FAIL_MINUTE + 1];
U8 gubCTLTemperature[DETECT_FAIL_MINUTE + 1];
#endif

U8 rdt_api_read_temperature(RDT_API_STRUCT_PTR rdt)
{
	TTTemperatureCelsius_t ubTemp;
	ubTemp.ubAll = 0;
	if (rdt->thermal_sensor != I2C_DETECT_FAIL) {
		TTTemperatureCelsius_t ubTemperatureOffset; // only for on-die or external sensor.
		ubTemperatureOffset.ubAll = gTT.ubTemperatureOffsetExternalToFlash.ubAll;
		I2C_CTRL_PARAM i2c_param;
		U8 ubGroup = I2C_GROUP_4; //I2C_GROUP_1;
		U8 ubMode, ubOpCnt, ubDataCnt;
		U16 uwSlave_addr = rdt->thermal_sensor;
		U16 uwTemp;

		mux_i2cm_scl_and_sda();

		ubMode = GP_DIR_BIT;
		ubDataCnt = 2;
		ubOpCnt = 1;

		I2CConfig(&i2c_param, ubGroup, uwSlave_addr, ubMode, ubDataCnt, ubOpCnt);
		i2c_param.ubOp[0] = 0x00;
		I2COperation(&i2c_param);
		I2CTriggerProcess(&i2c_param);
		if (I2CIsAddrNACK(&i2c_param) || I2CIsDataNACK(&i2c_param)) {
			M_UART(RDT_TEST_, "There is no Thermal Sensor\n");
		}
		else {
			if (I2C_THERMAL_TMP102 == THERMAL_SENSOR ) {
				uwTemp = ((R8_I2CM[R8_I2CM_BUF0_REG] << I2C_THERMAL_TMP102_BYTE0_LEFT_OFFSET) | (R8_I2CM[R8_I2CM_BUF0_REG + 1] >> I2C_THERMAL_TMP102_BYTE1_RIGHT_OFFSET));
				uwTemp /= I2C_THERMAL_TMP102_TEMPERATURE_RESOLUTION;
				ubTemp.ubAll =  uwTemp;
			}
			else {
				ubTemp.ubAll = (R8_I2CM[R8_I2CM_BUF0_REG]);
			}
			if (ubTemp.B.btSign) {
				ubTemp.B.Degree = (~ubTemp.B.Degree);
			}

			/* update offset */
			if (ubTemperatureOffset.B.btSign == ubTemp.B.btSign) {
				ubTemp.B.Degree += ubTemperatureOffset.B.Degree;
			}
			else {
				if (ubTemperatureOffset.B.Degree > ubTemp.B.Degree) {
					ubTemp.B.Degree = ubTemperatureOffset.B.Degree - ubTemp.B.Degree;
					ubTemp.B.btSign = ubTemperatureOffset.B.btSign; /* change sign */
				}
				else {
					ubTemp.B.Degree -= ubTemperatureOffset.B.Degree;
				}
			}
		}
		//M_UART(RDT_TEST_, "\n external =%d", ubTemp.ubAll);
	}

	return ubTemp.ubAll;
}


U8 rdt_api_get_flash_temperature(U8 ubCh, U8 ubBank, U8 ubDie, U8 ubTriggerTTByCE0Temperature)
{
	U8 current_temp = 0xFF;
	TTTemperatureCelsius_t ubTemperature;
	ubTemperature.ubAll = 0;
#if ((CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_QLC)||(CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_TLC)||(CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_QLC))//zerio BICS8 Add//zerio bics6 qlc add//waiting check

	R8_FCON[R8_FCON_FCE_ENB + (ubCh)] = 0; //clear CE of this channel
	FlaCEControl(ubCh, ubBank, ENABLE);
	rdt_api_pio_70_and_wait_status(ubCh, 0xE0, GERNAL_TIMEOUT_THRESHOLD);

	R32_FCTL_CH[ubCh][R32_FCTL_PIO_CMD] = 0xB9;

	if (RDTWaitFlashReady(GERNAL_TIMEOUT_THRESHOLD) != PASS) {
		return 0xA7; //MAX VALUE
	}
	else {
		R32_FCTL_CH[ubCh][R32_FCTL_PIO_CMD] = 0x7C;
		if (RDTWaitFlashReady(GERNAL_TIMEOUT_THRESHOLD) != PASS) {
			return 0xA7; //MAX VALUE
		}
		current_temp = (U8)(R32_FCTL_CH[ubCh][R32_FCTL_PIO_DAT]);
		R8_FCON[R8_FCON_FCE_ENB + (ubCh)] = 0; //clear CE of this channel

		if (current_temp < 42) {
			ubTemperature.B.btSign = 1;
			ubTemperature.B.Degree = (U8)(42 - current_temp);
		}
		else {
			ubTemperature.B.Degree = (U8)(current_temp - 42);
		}
	}

#elif ((CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC))//Duson Porting BICS5 Add //Reip Porting 3D-V7 QLC Add
	current_temp = (U8)FlaGetFlashTemperatureWorkAround(ubCh, ubBank);
	ubTemperature.ubAll = current_temp;
	// #elif ((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC))
	//
	//
	// #if 1 //U17 use cop0 to read micron N28 flash temperature
	// 	U8 get_value = 0;
	// 	U8 ubTemperartureOffset;
	// 	U32 ulBuffAddr;
	// 	U32 ulBuffOffset;
	// 	U32 backup_fctl_chnl_setting;
	//
	// 	ulBuffAddr = BTCM0_GET_TEMPERATURE_ADDR ;
	// 	ulBuffOffset = SECTOR_SIZE;
	// 	ubTemperartureOffset = TT_MICRON_B16_ZERO_DEGREE_OFFSET;
	//
	// 	gTT.uwFlashTemperatureTagID = INVALID_TAG_ID;
	//
	// 	backup_fctl_chnl_setting = R32_FCTL_CH[ubCh][R32_FCTL_CHNL_SET];
	// 	//Bypass Inversion
	// 	rdt_manual_setting_MTQ(MTQ_ABORT);
	// 	R32_FCTL_CH[ubCh][R32_FCTL_CHNL_SET] |= SET_INV_BYPASS_EN;
	// 	rdt_manual_setting_MTQ(MTQ_RESUME);
	//
	// 	//send cop0
	// 	TTTriggerFlashTemperature();
	//
	// 	//M_UART(RDT_TEST_,"gTT.uwFlashTemperatureTagID = %d \n", gTT.uwFlashTemperatureTagID);
	// 	while (FTLGetTagPoolNum(COP0_TAG_POOL_ID) != TAG_NUM) {
	// 		COP0DelegateCmd();
	// 		FIPDelegateCmd(); //Send MT and its CQ will be set to send back to CPU not COP0
	// 	}
	// 	//M_UART(RDT_TEST_,"gTT.uwFlashTemperatureTagID = %d \n", gTT.uwFlashTemperatureTagID);
	//
	// 	//get temperature
	// 	get_value = ((U8 *)(ulBuffAddr + ((ubBank * MAX_CHANNEL + ubCh) % TT_GET_FLASH_TEMPERATURE_MAX_CE)  * ulBuffOffset))[0];
	//
	// 	current_temp = get_value - ubTemperartureOffset;
	// 	//M_UART(RDT_TEST_,"LUN[%d] CE[%d] CH[%d] = %d\n", ubDie, ubBank, ubCh, current_temp);
	//
	// 	rdt_manual_setting_MTQ(MTQ_ABORT);
	// 	R32_FCTL_CH[ubCh][R32_FCTL_CHNL_SET] = backup_fctl_chnl_setting;
	// 	rdt_manual_setting_MTQ(MTQ_RESUME);
	//
	// 	//only use CE0 temperature for TT()
	// 	if (ubTriggerTTByCE0Temperature) {
	// 		gRdtApiStruct.rdt_got_flash_temperature_by_cop0 = current_temp;
	// 	}
	// 	return current_temp;
	// #else
	// 	/*
	// 	//switch interface
	//
	// 		R8_FCON[R8_FCON_FCE_ENB + (ubCh)] = 0; //clear CE of this channel
	// 		FlaCEControl(ubCh, ubBank, ENABLE);
	//
	// 		R32_FCTL_CH[ubCh][R32_FCTL_PIO_CMD] = 0xEE; //Get FEATURE
	// 		R32_FCTL_CH[ubCh][R32_FCTL_PIO_ADR] = 0xE7; //Temperature Feature Address
	//
	// 		M_RTT_IDLE_US(40);
	//
	// 		R32_FCTL_CH[ubCh][R32_FCTL_PIO_CMD] = 0xE0; //poll status
	//
	// 		//wait RBY
	//
	// 		R32_FCTL_CH[ubCh][R32_FCTL_PIO_CMD] = 0x00;
	//
	// 		current_temp = (U8)(R32_FCTL_CH[ubCh][R32_FCTL_PIO_DAT]);
	//
	//
	// 		R8_FCON[R8_FCON_FCE_ENB + (ubCh)] = 0; //clear CE of this channel
	// 		//switch interface
	//
	// 		current_temp -= 37;
	// 		*/
	//
	// 	//wait Auto Poll All Clear
	// 	while (R32_FCTL_CH[ubCh][R32_FCTL_INT_RDY] != MTQ_INT_RDY_AUTOPOLL_ALLCLEAR);
	//
	// 	//rdt_api_stall_fip_pre_process(ubCh);
	// 	rdt_manual_setting_MTQ(MTQ_ABORT);
	//
	// 	R8_FCON[R8_FCON_FCE_ENB + (ubCh)] = 0; //clear CE of this channel
	// 	FlaCEControl(ubCh, ubBank, ENABLE);
	//
	// 	//R32_FCTL_CH[ubCh][R32_FCTL_PIO_CMD] = 0xEE; //Get FEATURE
	// 	R32_FCTL_CH[ubCh][R32_FCTL_PIO_CMD] = 0xD4; //Get FEATURE by lun
	// 	R32_FCTL_CH[ubCh][R32_FCTL_PIO_ADR] = ubDie; //lun
	// 	R32_FCTL_CH[ubCh][R32_FCTL_PIO_ADR] = 0xE7; //Temperature Feature Address
	//
	// 	M_RTT_IDLE_US(40);
	//
	// 	if (FlaWaitFlashReady(GERNAL_TIMEOUT_THRESHOLD) != PASS) {
	// 		rdt_manual_setting_MTQ(MTQ_RESUME);
	// 		return 0xA7; //MAX VALUE
	// 	}
	// 	else {
	// #if IM_N28 || IM_N48R
	// 		M_FIP_ASSIGN_PIO_CMD(ubCh, 0x71);//polling status by lun
	// 		R32_FCTL_CH[ubCh][R32_FCTL_PIO_ADR] = ubDie; //lun
	//
	// 		//M_FIP_ASSIGN_PIO_CMD(ubCh, 0x78);//polling status by lun
	// 		//rdt_api_pio_fill_flash_address(ubCh, ubDie, 0, 0, RDT_PIO_FILL_ADDRESS_ROW_ONLY);
	// 		U32 ulStartTime = rdt_api_rtt_get_timer_count();
	// 		R32_FALL[R32_FCTL_HS_MODE] |= SET_DAT_DEFAULT;
	// 		while ((M_FIP_GET_PIO_DATA(ubCh) & 0xFF) != 0xE0) {
	// 			U32 ulCurrentTime = rdt_api_rtt_get_timer_count();
	// 			if ((ulCurrentTime - ulStartTime) > 3000) {
	// 				// Timeout
	// 				M_UART(RDT_TEST_, "pio 0x71 wait status timeout");
	// 				M_UART(RDT_TEST_, "die:%b, ce:%b, ch:%b, M_FIP_GET_PIO_DATA(ubCh): %x \n", ubDie, ubBank, ubCh, M_FIP_GET_PIO_DATA(ubCh));
	// 				break;
	// 			}
	// 		}
	// #else
	// 		rdt_api_pio_70_and_wait_status(ubCh, 0xE0, 5000);
	// #endif
	//
	// 		R32_FCTL_CH[ubCh][R32_FCTL_PIO_CMD] = 0x00;
	//
	// 		current_temp = (U8)(R32_FCTL_CH[ubCh][R32_FCTL_PIO_DAT]);
	// 		R8_FCON[R8_FCON_FCE_ENB + (ubCh)] = 0; //clear CE of this channel
	//
	// 		//rdt_api_stall_fip_post_process(ubCh);
	// 		rdt_manual_setting_MTQ(MTQ_RESUME);
	// #if (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC)
	// 		if (ID_MICRON_B27A_BYTE4 == gFlhEnv.ubFlashID[4]) {
	// 			current_temp = current_temp * 1.2; // B27A scale
	// 		}
	// #endif /* (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC) */
	//
	// 		if (current_temp < 37) {
	// 			ubTemperature.B.btSign = 1;
	// 			ubTemperature.B.Degree = (U8)(37 - current_temp);
	// 		}
	// 		else {
	// 			ubTemperature.B.Degree = (U8)(current_temp - 37);
	// 		}
	// 	}
	// #endif //U17 use cop0 to get flash temperature

#elif(CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC || CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)

	R8_FCON[R8_FCON_FCE_ENB + (ubCh)] = 0; //clear CE of this channel
	FlaCEControl(ubCh, ubBank, ENABLE);
	rdt_api_pio_70_and_wait_status(ubCh, 0xE0, GERNAL_TIMEOUT_THRESHOLD);

	if (FlaWaitFlashReady(GERNAL_TIMEOUT_THRESHOLD) != PASS) {
		return 0xA7; //MAX VALUE
	}
	else {

		R32_FCTL_CH[ubCh][R32_FCTL_PIO_CMD] = 0xEE; //Get FEATURE
		R32_FCTL_CH[ubCh][R32_FCTL_PIO_ADR] = 0xE7; //Temperature Feature Address
		//wait RDY
		if (FlaWaitFlashReady(GERNAL_TIMEOUT_THRESHOLD) != PASS) {
			return 0xA7; //MAX VALUE
		}
		current_temp = (U8)(R32_FCTL_CH[ubCh][R32_FCTL_PIO_DAT]);

		R8_FCON[R8_FCON_FCE_ENB + (ubCh)] = 0; //clear CE of this channel

		if (current_temp < 37) {
			ubTemperature.B.btSign = 1;
			ubTemperature.B.Degree = (U8)(37 - current_temp);
		}
		else {
			ubTemperature.B.Degree = (U8)(current_temp - 37);
		}
		/*M_UART(RDT_TEST_, " ubTemperature.B.Degree:%d ", ubTemperature.B.Degree);*/
	}

#elif ((CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_QLC)\
		|| (FW_CATEGORY_FLASH == FLASH_INTEL_N38A_QLC))//ems mst add--karl
	R8_FCON[R8_FCON_FCE_ENB + (ubCh)] = 0; //clear CE of this channel
	FlaCEControl(ubCh, ubBank, ENABLE);
	rdt_api_pio_70_and_wait_status(ubCh, 0xE0, GERNAL_TIMEOUT_THRESHOLD);

	R32_FCTL_CH[ubCh][R32_FCTL_HS_MODE] |= (BIT0 | BIT1);
	R32_FCTL_CH[ubCh][R32_FCTL_PIO_CMD] = 0xD4;
	R32_FCTL_CH[ubCh][R32_FCTL_HS_MODE] &= ~(BIT0 | BIT1);
	R32_FCTL_CH[ubCh][R32_FCTL_PIO_ADR] = 0x00;//DieAddr
	R32_FCTL_CH[ubCh][R32_FCTL_PIO_ADR] = 0xE7;

	//M_UART(RDT_TEST_, "\n YMTC get Temp:");

	if (RDTWaitFlashReady(GERNAL_TIMEOUT_THRESHOLD) != PASS) {
		//M_UART(RDT_TEST_, "RDY Fail.");
		ubTemperature.B.btSign = 1;
		return 0xA7; //MAX VALUE
	}
	else {
		M_RTT_IDLE_US(80);
		current_temp = (U8)(R32_FCTL_CH[ubCh][R32_FCTL_PIO_DAT]);
		R8_FCON[R8_FCON_FCE_ENB + (ubCh)] = 0; //clear CE of this channel
		ubTemperature.B.btSign = 0;
#if (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_QLC)//ems mst add--karl
		ubTemperature.B.Degree = (U8)(current_temp - 42);
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_TLC)
		ubTemperature.B.Degree = (U8)(current_temp - 40);
#elif (FW_CATEGORY_FLASH == FLASH_INTEL_N38A_QLC)
		ubTemperature.B.Degree = (U8)(current_temp - 37);
#endif
		//M_UART(RDT_TEST_, "%d", ubTemperature.B.Degree);
	}
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)
	ubTemperature.ubAll = current_temp;
#endif

#if NEGATIVE_TEMP
	ubTemperature.B.btSign = NEGATIVE_TEMP ? 1 : (ubTemperature.B.btSign); //debug
#endif

	return ubTemperature.ubAll;
}


#if ((CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC)||(CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_TLC)||(CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_QLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC))//Duson Porting BICS5 Add //Reip Porting 3D-V7 QLC Add//zerio bics6 qlc add
volatile U16 guwFlashTemperature[2] = {0};
#define FLH_TEMP_RAW_DATA				(0)
#define	FLH_TEMP_CELSIUS 				(1)

void FlaSetParameter(U8 ubCH, U8 ubCE, U8 ubAddr, U8 ubFeature)
{
	REG32 *pFlaReg = R32_FCTL_CH[ubCH];
	U8 ubToggle = 0;

	if ((gFlhEnv.ubCE_Interface[ubCH * MAX_CE_PER_CHANNEL + ubCE] == TOGGLE_INTERFACE) || (gFlhEnv.ubCE_Interface[ubCH * MAX_CE_PER_CHANNEL + ubCE] == TOGGLE2_INTERFACE)) {
		pFlaReg[R32_FCTL_FLH_SET] |= SET_TOGGLE_MODE;
		ubToggle = 1;
	}
	else {
		pFlaReg[R32_FCTL_FLH_SET] &= CLR_LEGACY_MODE;
	}
	FlaCEControl(ubCH, ubCE, ENABLE);

	// Transfer Die
	pFlaReg[R32_FCTL_PIO_CMD] = 0x78;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;

	pFlaReg[R32_FCTL_PIO_CMD] = 0x36;
	pFlaReg[R32_FCTL_PIO_ADR] = ubAddr;
	__asm("DSB");

	if (ubToggle == TRUE) {
		pFlaReg[R32_FCTL_HS_MODE] &= CLR_DAT_MID;
		pFlaReg[R32_FCTL_HS_MODE] |= SET_DAT_DEFAULT;
		__asm("DSB");
		pFlaReg[R32_FCTL_PIO_DAT] = (ubFeature << 8 | ubFeature);
		pFlaReg[R32_FCTL_HS_MODE] &= CLR_DAT_MID;
		__asm("DSB");
	}
	else {
		pFlaReg[R32_FCTL_PIO_DAT] = ubFeature;
		__asm("DSB");
		M_UART(FIP_, "Legacy %l\n", ubFeature);
	}
	pFlaReg[R32_FCTL_HS_MODE] &= CLR_DAT_MID;
	pFlaReg[R32_FCTL_HS_MODE] |= SET_DAT_DEFAULT;

	pFlaReg[R32_FCTL_PIO_CMD] = 0x16;

	FlaCEControl(ubCH, ubCE, DISABLE);
}

void FlaGetParameter(U8 ubCH, U8 ubCE, U8 ubAddr, U8 *pubFeature)
{
	REG32 *pFlaReg = R32_FCTL_CH[ubCH];
	U8 ubToggle = 0;
	pFlaReg[R32_FCTL_HS_MODE] |= SET_DAT_DEFAULT;

	if ((gFlhEnv.ubCE_Interface[ubCH * MAX_CE_PER_CHANNEL + ubCE] == TOGGLE_INTERFACE) || (gFlhEnv.ubCE_Interface[ubCH * MAX_CE_PER_CHANNEL + ubCE] == TOGGLE2_INTERFACE)) {
		pFlaReg[R32_FCTL_FLH_SET] |= SET_TOGGLE_MODE;
		ubToggle = 1;
	}
	else {
		pFlaReg[R32_FCTL_FLH_SET] &= CLR_LEGACY_MODE;
	}

	FlaCEControl(ubCH, ubCE, ENABLE);

	pFlaReg[R32_FCTL_PIO_CMD] = 0x37;
	pFlaReg[R32_FCTL_PIO_ADR] = ubAddr;
	FlaWaitFlashReady(GERNAL_TIMEOUT_THRESHOLD);

	if (ubToggle == TRUE) {
		pFlaReg[R32_FCTL_HS_MODE] &= CLR_DAT_MID;
		pFlaReg[R32_FCTL_HS_MODE] |= SET_DAT_DEFAULT;
		__asm("DSB");
		pubFeature[0] = (U8)pFlaReg[R32_FCTL_PIO_DAT] ;
		pFlaReg[R32_FCTL_HS_MODE] &= CLR_DAT_MID;
		__asm("DSB");
	}
	else {
		pubFeature[0] = (U8)pFlaReg[R32_FCTL_PIO_DAT] ;
		__asm("DSB");
	}

	pFlaReg[R32_FCTL_HS_MODE] &= CLR_DAT_MID;
	pFlaReg[R32_FCTL_HS_MODE] |= SET_DAT_DEFAULT;

	FlaCEControl(ubCH, ubCE, DISABLE);
}



U16 FlaGetFlashTemperature(U8 ubCH, U8 ubCE, U8 btGetDQ)
{

	REG32 *pFlaReg = R32_FCTL_CH[ubCH];
	U16 uwFlashTempRawData = 0;
	U8	ubi, ubStatus;

	FlaCEControl(ubCH, ubCE, ENABLE);

	R32_FCTL_CH[ubCH][R32_FCTL_PIO_CMD] = 0x70;
	do {
		ubStatus = (U8) R32_FCTL_CH[ubCH][R32_FCTL_PIO_DAT];
	} while (0xE0 != (ubStatus & 0xE0));

	// Trans Die
	pFlaReg[R32_FCTL_PIO_CMD] = 0x78;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;

	// Trans Die end
	pFlaReg[R32_FCTL_PIO_CMD] = 0x00;

	pFlaReg[R32_FCTL_PIO_CMD] = 0x91;

	__asm("DSB");
	pFlaReg[R32_FCTL_PIO_CMD] = 0x78;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;
	pFlaReg[R32_FCTL_PIO_ADR] = 0x00;

	do {
		ubStatus = (U8) R32_FCTL_CH[ubCH][R32_FCTL_PIO_DAT];
		//M_UART(RDT_TEST_,"\n Sts1=%x", ubStatus);
	} while (0xE0 != (ubStatus & 0xE0));

	__asm("DSB");
	pFlaReg[R32_FCTL_PIO_CMD] = 0x00;

	if (btGetDQ) {
		pFlaReg[R32_FCTL_HS_MODE] |= SET_DAT_DEFAULT;

		for (ubi = 0; ubi < 2; ubi++) {
			pFlaReg[R32_FCTL_HS_MODE] &= CLR_DAT_MID;
			if (ubi == 0) {
				pFlaReg[R32_FCTL_HS_MODE] |= PIO_DAT_FIRST_BIT;
			}
			else if (ubi == 1) {
				pFlaReg[R32_FCTL_HS_MODE] |= PIO_DAT_LAST_BIT;
			}

			uwFlashTempRawData |= ((U16)(pFlaReg[R32_FCTL_PIO_DAT] & 0xFF)) << (ubi * 8);

		}

		pFlaReg[R32_FCTL_HS_MODE] |= SET_DAT_DEFAULT;
	}

	FlaCEControl(ubCH, ubCE, DISABLE);

	return uwFlashTempRawData;

}

U16 FlaConvertFlashTemperatureToCelsius(U16 uwFlashTempRawData)
{
	return 92 + ((384 - (S32)uwFlashTempRawData) * 0.0794);
}

U16 FlaGetFlashTemperatureWorkAround(U8 ubCH, U8 ubCE)
{
	U8 ubParamSet[4] = {0, 0, 0, 0};
	U8 ubParam_A[4] = {0, 0, 0, 0};
	U8 ubParam_B[4] = {0, 0, 0, 0};
	U8 ubParam_C[4] = {0, 0, 0, 0};
	U8 ubParam_D[4] = {0, 0, 0, 0};
	U16 uwFlashTempRawData = 0;
	U16 uwNoUse = 0;
	U8 ubStatus;
	U8 ubTmpCE;

	FlaMTQManualAbort(ubCH, TRUE);

	// Clear all CE in this CH
	for (ubTmpCE = 0 ; ubTmpCE < BIT(gPCARule_Bank.ubBit_No) ; ubTmpCE++) {
		FlaCEControl(ubCH, ubTmpCE, DISABLE);
	}

	FlaCEControl(ubCH, ubCE, ENABLE);

	R32_FCTL_CH[ubCH][R32_FCTL_PIO_CMD] = 0x70;
	do {
		ubStatus = (U8) R32_FCTL_CH[ubCH][R32_FCTL_PIO_DAT];
	} while (0xE0 != (ubStatus & 0xE0));

	FlaCEControl(ubCH, ubCE, DISABLE);

	FlaSetParameter(ubCH, ubCE, 0x41, 0x07);

	FlaGetParameter(ubCH, ubCE, 0xB2, ubParam_A);

	FlaGetParameter(ubCH, ubCE, 0xB5, ubParam_B);

	FlaGetParameter(ubCH, ubCE, 0xCD, ubParam_C);

	FlaGetParameter(ubCH, ubCE, 0xB8, ubParam_D);

	FlaSetParameter(ubCH, ubCE, 0x41, 0x00);

	uwFlashTempRawData = FlaGetFlashTemperature(ubCH, ubCE, TRUE);

	// M_UART(RDT_TEST_, "\n uwFlashTempRawData = %d", uwFlashTempRawData);

	FlaSetParameter(ubCH, ubCE, 0x41, 0x07);

	FlaSetParameter(ubCH, ubCE, 0xB2, 0xBB);

	FlaSetParameter(ubCH, ubCE, 0xB5, 0x7F);

	ubParamSet[0] = ubParam_C[0] - 0x0A;
	FlaSetParameter(ubCH, ubCE, 0xCD, ubParamSet[0]);

	ubParamSet[0] = ubParam_D[0] + 0x0C;
	FlaSetParameter(ubCH, ubCE, 0xB8, ubParamSet[0]);


	FlaSetParameter(ubCH, ubCE, 0x41, 0x00);

	uwNoUse = FlaGetFlashTemperature(ubCH, ubCE, FALSE);

	ubParamSet[0] = 0x07;
	FlaSetParameter(ubCH, ubCE, 0x41, 0x07);

	FlaSetParameter(ubCH, ubCE, 0xB2, ubParam_A[0]);

	FlaSetParameter(ubCH, ubCE, 0xB5, ubParam_B[0]);

	FlaSetParameter(ubCH, ubCE, 0xCD, ubParam_C[0]);

	FlaSetParameter(ubCH, ubCE, 0xB8, ubParam_D[0]);

	FlaSetParameter(ubCH, ubCE, 0x41, 0x00);

	FlaCEControl(ubCH, ubCE, ENABLE);

	R32_FCTL_CH[ubCH][R32_FCTL_PIO_CMD] = 0x70;
	do {
		ubStatus = (U8) R32_FCTL_CH[ubCH][R32_FCTL_PIO_DAT];
	} while (0xE0 != (ubStatus & 0xE0));

	FlaCEControl(ubCH, ubCE, DISABLE);

	FIPResumeMTQManualAbort(ubCH);
	FIPClearChannelForceEmpty(ubCH, FALSE);

	M_UART(RDT_DBG_, "\n[TEMP]CH:%d CE:%d\n", ubCH, ubCE);

	guwFlashTemperature[FLH_TEMP_RAW_DATA] = uwFlashTempRawData;

	// M_UART(RDT_TEST_, "\n guwFlashTemperature[FLH_TEMP_RAW_DATA] = %d", guwFlashTemperature[FLH_TEMP_RAW_DATA]);
	guwFlashTemperature[FLH_TEMP_CELSIUS]  = FlaConvertFlashTemperatureToCelsius(uwFlashTempRawData);
	// M_UART(RDT_TEST_, "\n guwFlashTemperature[FLH_TEMP_CELSIUS] = %d", guwFlashTemperature[FLH_TEMP_CELSIUS]);
	return guwFlashTemperature[FLH_TEMP_CELSIUS];
}
#endif //(CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC)


void rdt_temperature_record_update(TTTemperatureCelsius_t *ubTemperature, RDT_TEMPERATURE_STRUCT_PTR temperature_record, TEMPERATURE_CELSIUS_SUM_t *total_temperature)
{
	//Temperature sum to calculate average temperature
	if (ubTemperature->B.btSign == total_temperature->L.btSign) {
		total_temperature->L.Degree += (U32)ubTemperature->B.Degree;
	}
	else {
		if ((U32)ubTemperature->B.Degree > total_temperature->L.Degree) {
			total_temperature->L.Degree = (U32)((U32)ubTemperature->B.Degree - total_temperature->L.Degree);
			total_temperature->L.btSign = ubTemperature->B.btSign;
		}
		else {
			total_temperature->L.Degree -= (U32)ubTemperature->B.Degree;
		}
	}

	//Update max temperature
	if (ubTemperature->B.btSign == temperature_record->max_temp.B.btSign) {
		if (ubTemperature->B.btSign == FALSE) { //positive
			if (ubTemperature->B.Degree > temperature_record->max_temp.B.Degree) {
				temperature_record->max_temp.B.Degree = ubTemperature->B.Degree;
			}
		}
		else {  //negative
			if (ubTemperature->B.Degree < temperature_record->max_temp.B.Degree) {
				temperature_record->max_temp.B.Degree = ubTemperature->B.Degree;
			}
		}
	}
	else {
		if (ubTemperature->B.btSign == FALSE) {
			temperature_record->max_temp.B.btSign = FALSE;
			temperature_record->max_temp.B.Degree = ubTemperature->B.Degree;
		}
	}

	//Update min temperature
	if (ubTemperature->B.btSign == temperature_record->min_temp.B.btSign) {
		if (ubTemperature->B.btSign == FALSE) { //positive
			if (ubTemperature->B.Degree < temperature_record->min_temp.B.Degree) {
				temperature_record->min_temp.B.Degree = ubTemperature->B.Degree;
			}
		}
		else {  //negative
			if (ubTemperature->B.Degree > temperature_record->min_temp.B.Degree) {
				temperature_record->min_temp.B.Degree = ubTemperature->B.Degree;
			}
		}
	}
	else {
		if (ubTemperature->B.btSign == TRUE) {
			temperature_record->min_temp.B.btSign = TRUE;
			temperature_record->min_temp.B.Degree = ubTemperature->B.Degree;
		}
	}
	//M_UART(RDT_TEST_,"\ntotal_temperature->ubAll=%l,temperature_record->max_temp.ubAll=%d,temperature_record->min_temp.ubAll=%d,ubTemperature->ubAll=%d",total_temperature->ubAll,temperature_record->max_temp.ubAll,temperature_record->min_temp.ubAll,ubTemperature->ubAll);
}

U8 rdt_read_CTRL_temperature(void)
{
	S16 value = M_ANGC_GET_TCODE_SIGN() ? -M_ANGC_GET_TEMPERATURE_SENSOR_CELSIUS()
		: M_ANGC_GET_TEMPERATURE_SENSOR_CELSIUS();
	S16 offset = gTT.ubTemperatureOffsetOnDieToFlash.B.btSign
		? -gTT.ubTemperatureOffsetOnDieToFlash.B.Degree
		: gTT.ubTemperatureOffsetOnDieToFlash.B.Degree;
	S16 degree = value + offset;

	TTTemperatureCelsius_t t = { 0 };
	if (degree < 0) {
		t.B.btSign = 1;
		t.B.Degree = (U8)(-degree > 127 ? 127 : -degree);
	}
	else {
		t.B.btSign = 0;
		t.B.Degree = (U8)(degree > 127 ? 127 : degree);
	}

#if NEGATIVE_TEMP
	t.B.btSign = NEGATIVE_TEMP ? 1 : t.B.btSign;
#endif

	return t.ubAll;
}

U8 rdt_read_CTRL_temperature_without_offset(void)
{
	TTTemperatureCelsius_t t = { 0 };
	t.B.btSign = M_ANGC_GET_TCODE_SIGN();
	U8 degree = M_ANGC_GET_TEMPERATURE_SENSOR_CELSIUS();
	t.B.Degree = (degree > 127) ? 127 : degree;

#if NEGATIVE_TEMP
	t.B.btSign = NEGATIVE_TEMP ? 1 : t.B.btSign;
#endif

	return t.ubAll;
}

#if RDT_RECORD_TEMPERATURE
void rdt_merge_temperature_info_to_tdl(RDT_API_STRUCT_PTR rdt)
{
	U32 page_offset;
	U32 page_in_block;
	U8 start_blk_idx = 0;
	U8 end_blk_idx = 0;
	U8 blk_idx = 0;
	U32 log_pca = 0;
	U32 data_buf = rdt->temp_read_buffer;
	U32 tdl_buf = rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].buf_base;

	//put start mark
	memset((U8 *)tdl_buf, 0xE0E0E0E0, 4);
	rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].buf_offset += 4;


	for (page_offset = RDT_LOG_HEADER; page_offset < rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].page_offset_in_block; page_offset++) {

		if (page_offset < rdt->fpl->geometry.d1_page_per_block) {
			// block 0~1 (first block)
			start_blk_idx = 0;
			end_blk_idx = 1;
			page_in_block = page_offset;
		}
		else if ((page_offset >= rdt->fpl->geometry.d1_page_per_block) && (page_offset < (2 * rdt->fpl->geometry.d1_page_per_block))) {
			// block 2~3 (second block)
			start_blk_idx = 2;
			end_blk_idx = 3;
			page_in_block = (page_offset - (rdt->fpl->geometry.d1_page_per_block));
		}
		else {
			M_FW_ASSERT(ASSERT_RDT_0x084B, 0);
		}

		for (blk_idx = start_blk_idx; blk_idx <= end_blk_idx; blk_idx++) {
			log_pca = rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].log_block_addr[blk_idx];
			if (log_pca != INVALID_PCA_VALUE) {

				if (vuc_read_log(rdt, log_pca, page_in_block, data_buf)) {
					//M_UART(RDT_TEST_,"\nRead log pca=%l page_offset=%l page_in_block=%l log_id=%b", log_pca, page_offset, page_in_block, 2);
					break;
				}
			}
		}
		rdt_add_temperature_info_to_tdl(rdt, TRUE);
		//M_UART(RDT_TEST_,"\n Merge TDL Temperature info");
	}

	rdt_api_write_log_to_flash(rdt, RDT_LOG_TEMPERATURE_DIVERSITY);
}

void rdt_add_temperature_info_to_tdl(RDT_API_STRUCT_PTR rdt, BOOL final)
{
	U32 tdl_buf;
	if ((rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].buf_offset + sizeof(TDL_LOG_STRUCT)) > rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].buf_total_size) {
		return; //only program 1 page, accept 56 loop
		//rdt_api_write_log_to_flash(rdt, RDT_LOG_TEMPERATURE_DIVERSITY);
	}

	if (final) {
		//merge all tmperature info to the last tdl page
		tdl_buf = rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].buf_base + rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].buf_offset;
		memcpy(((U8 *)(tdl_buf)), (U8 *)(rdt->temp_read_buffer + FRAME_SIZE), rdt->base.temperature_size);
		rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].buf_offset += rdt->base.temperature_size;
		rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].page_num = rdt_api_page_align_count(rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].buf_offset);
	}
	else {
		//End mark for original TDL
		tdl_buf = (rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].buf_base + rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].buf_offset);
		memset((U8 *)tdl_buf, 0xD0D0D0D0, 4);

		//put Temperature info at 4096 for MP parsing
		tdl_buf = (rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].buf_base + FRAME_SIZE);
		rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].buf_offset = FRAME_SIZE;
		memcpy(((RDT_TEMPERATURE_STRUCT_PTR)(tdl_buf)), (RDT_TEMPERATURE_STRUCT_PTR)rdt->base.temperature_base, rdt->base.temperature_size);
		rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].buf_offset += rdt->base.temperature_size;
#if RDT_TT_LOG
		tdl_buf = (rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].buf_base + rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].buf_offset);
		*(U32 *)tdl_buf = 0xD0D0D0D0;
		*(U32 *)(tdl_buf + 4) = 0xFFFFFFFF; //add break mark for mp work around.
#endif
		rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].page_num = rdt_api_page_align_count(rdt->log[RDT_LOG_TEMPERATURE_DIVERSITY].buf_offset);
	}
}
#endif

#if RDT_THERMAL_DETECT
#if !RDT_NEW_THERMAL_DETECT_FLOW //cancel pre thermal detect from SSDRDT-2179 @20211206
void rdt_pre_thermal_detect(RDT_API_STRUCT_PTR rdt)
{
	ERL_LOG_STRUCT erl_log;
	U32 under_time  = 0;
	U8  check_temp  = 0;
	U8  ubi         = 0;
	U8  minute      = 0;
	U8  temperature[PRE_DETECT_FAIL_MINUTE + 1];
	U8  ubAmbientTemp = rdt->param.ambient_temperature;
	TTTemperatureCelsius_t ubTemperature;

	//consider delta of offset and BI room, need adjust check temperature value
	if (rdt->param.ambient_temperature > 0 && rdt->param.ambient_temperature > BI_ROOM_DELTA) {
		ubAmbientTemp = rdt->param.ambient_temperature - BI_ROOM_DELTA;
	}

	M_UART(RDT_TEST_, "\n RDT Pre-Thermal Detect Ambient Temperature: %d", ubAmbientTemp);
	while (1) {

		if (rdt_api_check_ctl_fatal_temperature(rdt)) {
			break;
		}

		//check_temp = rdt_api_get_flash_temperature(0, 0);//After 10 sec temperature
		ubTemperature.ubAll = rdt_api_get_flash_temperature(0, 0, 0, 0); //After 10 sec temperature
		check_temp = (ubTemperature.B.btSign == 0) ? ubTemperature.ubAll : 0;
		M_UART(RDT_TEST_, " check_temp: %d", check_temp);

		if (check_temp < ubAmbientTemp) {
			M_UART(RDT_TEST_, "\n check_temp < ambient temperature");
			//gubLEDState = RDT_TEST_ERROR;

			if ((under_time % PRE_DETECT_RECORD_TIMES) == 0) {
				//per minute
				temperature[minute] = check_temp;
				M_UART(RDT_TEST_, " %b minute", minute);
				minute++;
			}

			if (under_time == PRE_DETECT_FAIL_TIMES) {
				//10 minute
				M_UART(RDT_TEST_, "\n Pre-Thermal Detect TimeOut");
				for (ubi = 0; ubi <= PRE_DETECT_FAIL_MINUTE; ubi++) {
					memset(&erl_log, 0, sizeof(ERL_LOG_STRUCT));
					erl_log.err_bbl.fail_type         = ERL_THERMAL_TIME_OUT;
					erl_log.err_bbl.current_time      = (rdt_api_rtt_get_timer_count() / 1000) - ((PRE_DETECT_FAIL_MINUTE - ubi) * 60);
					erl_log.err_bbl.rdt_state         = rdt->current_state;
					erl_log.err_bbl.flash_temperature = temperature[ubi];
					erl_log.err_bbl.ctl_temperature   = rdt_read_CTRL_temperature();
					erl_log.err_bbl.over_err_bit      = ubAmbientTemp;

					rdt_api_erl_log_add(rdt, &erl_log);
				}
				rdt_api_program_erl_log(rdt, TRUE);
				//rdt->rdt_err = ERR_UNKNOWN_TIMEOUT; //rdt do not stop test even if pre-thermal temperature not meet criteria
				break;
			}
			under_time++;

			//idle 10s for next thermal detect
			for (ubi = 0; ubi < PRE_DETECT_POLLING_TIME; ubi++) {
				//10 second
				M_RTT_IDLE_S(1);
			}
			//detect_timeout = 0;
			continue;
		}
		else {
			rdt->temp_criteria = ubAmbientTemp;
			M_UART(RDT_TEST_, "\n rdt->temp_criteria: %d", rdt->temp_criteria);
			break;
		}
	}
}
#endif

void rdt_thermal_detect(RDT_API_STRUCT_PTR rdt, U8 test_cycle, U8 loop)
{
	// check temperature priority: external thermal > CTL Tj(+off set)
	U16 start_time_sec = rdt_api_rtt_get_timer_count() / 1000;
	U8 current_temperature = 0;
	TTTemperatureCelsius_t t;

	BOOL run_forever = rdt->param.check_temperature_timeout_sec == 0;
	U16 under_time_sec = 0;
	// +1 是因为第0秒也要记录
	const U8 record_num =
		run_forever ? 0 : rdt->param.check_temperature_timeout_sec / DETECT_RECORD_INTERVAL_SEC + 1;
	U8 flash_temperature[record_num];
	U8 ctrl_temperature[record_num];
	U8 i = 0;

	while (1) {
		if (rdt->thermal_sensor != I2C_DETECT_FAIL) {
			current_temperature = rdt_api_read_temperature(rdt);
		}
		else {
			t.ubAll = rdt_read_CTRL_temperature();
			current_temperature = t.B.btSign ? 0 : t.B.Degree;
		}
		if (current_temperature >= rdt->param.ambient_temperature) {
			M_UART(RDT_TEST_,
				"\ncurrent_temperature: %d >= rdt->param.ambient_temperature, exit "
				"rdt_thermal_detect",
				current_temperature);
			return;
		}

		M_UART(RDT_TEST_, "\nunder_time = %ds", under_time_sec);
		M_UART(RDT_TEST_, "\ncurrent_temperature: %d < rdt->param.ambient_temperature: %d",
			current_temperature, rdt->param.ambient_temperature);

		if (!run_forever && under_time_sec >= i * DETECT_RECORD_INTERVAL_SEC) {
			flash_temperature[i] = rdt_api_get_flash_temperature(0, 0, 0, 1);
			ctrl_temperature[i] = current_temperature;
			i++;
			M_UART(RDT_TEST_, ", record_num = %d", i);
		}

		if (!run_forever && under_time_sec >= rdt->param.check_temperature_timeout_sec) {
			break;
		}

		under_time_sec += DETECT_POLLING_INTERVAL_SEC;
		M_RTT_IDLE_S(DETECT_POLLING_INTERVAL_SEC);
	}

	M_UART(RDT_TEST_, "\nThermal Detect TimeOut");
	ERL_LOG_STRUCT erl;
	for (U8 i = 0; i < record_num; i++) {
		memset(&erl, 0, sizeof(ERL_LOG_STRUCT));
		erl.err_bbl.fail_type = ERL_THERMAL_TIME_OUT;
		erl.err_bbl.current_time = start_time_sec + i * DETECT_RECORD_INTERVAL_SEC;
		erl.err_bbl.cycle_number = test_cycle;
		erl.err_bbl.loop_number = loop;
		erl.err_bbl.rdt_state = rdt->current_state;
		erl.err_bbl.flash_temperature = flash_temperature[i];
		erl.err_bbl.ctl_temperature = ctrl_temperature[i];
		erl.err_bbl.over_err_bit = rdt->param.ambient_temperature;
		// erl.err_bbl.current_voltage   = rdt_api_get_current_voltage(rdt);
		rdt_api_erl_log_add(rdt, &erl);
	}
	gubLEDState = RDT_TEST_ERROR;
	rdt_api_program_erl_log(rdt, TRUE);
	rdt->rdt_err = ERR_UNKNOWN_TIMEOUT;
}
#endif

U8 rdt_api_check_ctl_fatal_temperature(RDT_API_STRUCT_PTR rdt)
{
	if ((!rdt->param.ctl_fatal_temperature) && (!gTT.ubOthers.B.btEnableTT)) {
		return PASS;
	}

	if (!M_ANGC_GET_TCODE_SIGN()) { // Positive
		U8 ubCTLTemperature = M_ANGC_GET_TEMPERATURE_SENSOR_CELSIUS();
		//if (ubCTLTemperature > RDT_CONTROLLER_FATAL_TMPERATURE_THRESHOLD) {
		if ((ubCTLTemperature > RDT_CONTROLLER_FATAL_TMPERATURE_THRESHOLD) || (gpVT->TT.TMTState == TT_TMT_FATAL)) {
			ERL_LOG_STRUCT erl;
			memset(&erl, 0, sizeof(ERL_LOG_STRUCT));
			erl.err_bbl.fail_type       = ERL_CTL_TEMP_OVER_SPEC;
			erl.err_bbl.rdt_state       = rdt->current_state;
			erl.err_bbl.cycle_number    = rdt->saving_info.test_cycle;
			erl.err_bbl.loop_number     = rdt->saving_info.test_loop;
			erl.err_bbl.opt_stage       = gpVT->TT.TMTState;
			erl.err_bbl.current_time    = (U16)(rdt_api_rtt_get_timer_count() / 1000);
			erl.err_bbl.ctl_temperature = rdt_read_CTRL_temperature();
			rdt_api_erl_log_add(rdt, &erl);
			rdt_api_program_erl_log(rdt, TRUE);
			rdt->rdt_err = ERR_FATAL_ERROR;
			M_UART(RDT_TEST_, "\n shut down");

			//PmicPowerOffAll();
			return FAIL;
		}
	}
	return PASS;
}
#endif /* RDT_MODE_EN */
