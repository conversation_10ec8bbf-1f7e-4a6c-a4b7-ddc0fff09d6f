#ifndef _RNG_API_H_
#define _RNG_API_H_

#include "typedef.h"
#include "hal/sys/reg/sys_pd1_reg.h"
#include "rng.h"
#include "aom/aom_api.h"

typedef enum RNG_MODE {
	RNG_NOISE_MODE = 0,
	RNG_PAD_MODE,
	RNG_METHOD_MODE,
	RNG_MODE_NUM
} RNG_MODE_t;

#define RANDOM_NUMBER_GENERATOR_CONFIG_APPLICATION2 			(0x9)

#if PS5017_EN
#define RANDOM_NUMBER_GENERATOR_CHANNEL_0 						(0)
#define RANDOM_NUMBER_GENERATOR_CHANNEL_1 						(1)
#define RANDOM_NUMBER_GENERATOR_CHANNEL_2 						(2)
#define RANDOM_NUMBER_GENERATOR_CHANNEL_3 						(3)

#define M_RANDOM_NUMBER_GENERATOR_SET_CONFIG(CHANNEL, MODE)		do { \
																R32_SYS1_RNG[R32_SYS1_RNG_AIP_CTRL1] &= (~(CR_RNG_CFG_CHANNEL0_MASK << (CR_RNG_CFG_CHANNEL0_SHIFT + ((CHANNEL) * CR_RNG_CFG_CHANNEL0_SHIFT_UNIT)))); \
																R32_SYS1_RNG[R32_SYS1_RNG_AIP_CTRL1] |= (((MODE) & CR_RNG_CFG_CHANNEL0_MASK) << (CR_RNG_CFG_CHANNEL0_SHIFT + ((CHANNEL) * CR_RNG_CFG_CHANNEL0_SHIFT_UNIT))); \
																} while(0)
#define M_RNG_INIT_RNG()										do{\
																M_ENABLE_ALL_CHANNEL_NOISE_GENERATOR();\
																M_SET_RNG_MODE(RNG_NOISE_MODE);\
																M_START_ALL_CHANNEL_RNG();\
																} while(0)
#elif PS5021_EN /* PS5017_EN */
#define M_RANDOM_NUMBER_GENERATOR_SET_CONFIG(MODE)				do { \
																(R16_SYS1_ANALOG_CTRL[R16_SYS1_ANALOG_NOISE_GEN_CFG] = ((U16)(MODE)) & CR_RNG_CFG); \
																} while(0)
#define M_RNG_INIT_RNG()										do{ \
																M_ENABLE_NOISE_GENERATOR(); \
																M_RANDOM_NUMBER_GENERATOR_SET_CONFIG(RANDOM_NUMBER_GENERATOR_CONFIG_APPLICATION2); \
																M_SET_RNG_ANOLOG_MODE(); \
																M_START_RNG(); \
																} while(0)
#else /* PS5017_EN */
#define M_RANDOM_NUMBER_GENERATOR_SET_CONFIG(MODE)				do { \
																R32_SYS1_RNG[R32_SYS1_RNG_CTRL] &= (~(CR_RNG_CFG_MASK << CR_RNG_CFG_SHIFT)); \
																R32_SYS1_RNG[R32_SYS1_RNG_CTRL] |= (((MODE) & CR_RNG_CFG_MASK) << CR_RNG_CFG_SHIFT); \
																} while(0)
#define M_RNG_INIT_RNG()										do{ \
																M_ENABLE_NOISE_GENERATOR(); \
																M_RANDOM_NUMBER_GENERATOR_SET_CONFIG(RANDOM_NUMBER_GENERATOR_CONFIG_APPLICATION2); \
																M_SET_RNG_MODE(RNG_NOISE_MODE); \
																M_START_RNG(); \
																} while(0)
#endif /* PS5017_EN */

#if PS5017_EN
#define M_RNG_DISABLE_RNG()										do{ \
																M_STOP_ALL_CHANNEL_RNG(); \
																M_DISABLE_ALL_CHANNEL_NOISE_GENERATOR(); \
																} while(0)
#else /* PS5017_EN */
#define M_RNG_DISABLE_RNG()										do{ \
																M_STOP_RNG(); \
																M_DISABLE_NOISE_GENERATOR(); \
																} while(0)
#endif /* PS5017_EN */

U32 RngGetRandomValue(void);

#endif /* _RNG_API_H_ */
