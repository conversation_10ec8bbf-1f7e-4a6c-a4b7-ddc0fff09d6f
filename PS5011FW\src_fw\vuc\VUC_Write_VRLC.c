#include "setup.h"
#if (VRLC_EN && (USB == HOST_MODE))
#include "hal/pic/uart/uart_api.h"
#include "host/VUC_handler.h"
#include "nvme_api/nvme/shr_hal_nvme.h"
#include "vuc/VUC_Write_VRLC.h"
#include "hal/dmac/dmac_pop_cmd.h"
#include "VRLC/VRLC_api.h"
#include "init/fw_preformat.h"
#include "buffer/buffer.h"

void VUC_Write_VRLC(VUC_OPT_HCMD_PTR_t pCmd)
{
	U8 ubCEIdx, ubLunIdx;
	VRLC_META *pMeta = NULL;
	U32 ulCopySize = 0, ulCopySrcBufAddr, ulCopyDstBufAddr;
	//PREFORMAT_VRLC_MMO_WRITE_READ_DATA_BUF
	//ulCopyDstBufAddr = (ulDstBufAddr + uwTempcoMetaSize + uwVRLCMetaOffset + (ubCEIdx * uwVRLCMetaSize));
	M_UART(VUC_, "\nVUC Write VRLC");
	RDT_MICRON_VRLC_LOG *pVRLCRDTLog = (RDT_MICRON_VRLC_LOG *)(pCmd->ulCurrentPhysicalMemoryAddr);
	/*UartPrintf("\nVUC Write VRLC addr:%x\n", pCmd->ulCurrentPhysicalMemoryAddr);
	//gMMOInfo.MMOSystemFlag.ubMMOSystemFlag.btFromRDT = TRUE;
	UartPrintf("RDT offset AGC:%d center:%d version:%d\n", (offsetof(RDT_MICRON_VRLC_LOG, ubQLCWLAGCOffsetSelect)), (offsetof(RDT_MICRON_VRLC_LOG, ubTrimCenterValue)), (offsetof(RDT_MICRON_VRLC_LOG, ubVersion)));
	for (ubLunIdx = 0; ubLunIdx < gubDieNumber; ubLunIdx++) {
		for (ubCEIdx = 0; ubCEIdx < gubCENumber; ubCEIdx++) {
			pMeta = &gpVRLCMeta[ubCEIdx];
			pVRLCRDTLog = &(((RDT_MICRON_VRLC_LOG *)(pCmd->ulCurrentPhysicalMemoryAddr))[ubCEIdx]);
			//QLCAGCOffsetSelect
			//TrimCenterValue
			//Version
			ulCopySrcBufAddr = (U32)pVRLCRDTLog + (offsetof(RDT_MICRON_VRLC_LOG, ubVersion));
			ulCopyDstBufAddr = (U32)gpVRLCVersion;
			UartPrintf("FW version:%c%c%c%c  addr:%x\n", pVRLCRDTLog->ubVersion[0], pVRLCRDTLog->ubVersion[1], pVRLCRDTLog->ubVersion[2], pVRLCRDTLog->ubVersion[3], ulCopySrcBufAddr);
		}
	}*/


	if ((VRLC_REVISION0 == pVRLCRDTLog->ubVersion[0]) && (VRLC_REVISION1 == pVRLCRDTLog->ubVersion[1])) {
		for (ubLunIdx = 0; ubLunIdx < gubDieNumber; ubLunIdx++) {
			for (ubCEIdx = 0; ubCEIdx < gubCENumber; ubCEIdx++) {
				pMeta = &gpVRLCMeta[ubCEIdx];
				pVRLCRDTLog = &(((RDT_MICRON_VRLC_LOG *)(pCmd->ulCurrentPhysicalMemoryAddr))[ubCEIdx]);

				//QLCAGCOffsetSelect
#if (IM_N48R)
				M_FW_CRITICAL_ASSERT(ASSERT_VRLC_0x0E08, (QLC_WL_TRIM_REG_NUM == pVRLCRDTLog->ulQLCAGCOffsetSelectSize));
				ulCopySrcBufAddr = (U32)pVRLCRDTLog + (offsetof(RDT_MICRON_VRLC_LOG, ubQLCWLAGCOffsetSelect));
				ulCopyDstBufAddr = (U32)pMeta + (offsetof(VRLC_META, ubQLCWLAGCOffsetSelect));
				ulCopySize = pVRLCRDTLog->ulQLCAGCOffsetSelectSize;
				memcpy((void *)ulCopyDstBufAddr, (void *)ulCopySrcBufAddr, ulCopySize);
#endif /* (IM_N48R) */

				//TrimCenterValue
				M_FW_CRITICAL_ASSERT(ASSERT_VRLC_0x0E08, (VRLC_TOTAL_TRIM_CNT_PER_CE == pVRLCRDTLog->ulTrimCenterValueSize));
				ulCopySrcBufAddr = (U32)pVRLCRDTLog + (offsetof(RDT_MICRON_VRLC_LOG, ubTrimCenterValue));
				ulCopyDstBufAddr = (U32)pMeta + (offsetof(VRLC_META, ubTrimCenterValue));
				ulCopySize = pVRLCRDTLog->ulTrimCenterValueSize;
				memcpy((void *)ulCopyDstBufAddr, (void *)ulCopySrcBufAddr, ulCopySize);

				//Version
				if ((gubCENumber - 1) == ubCEIdx && (gubDieNumber - 1) == ubLunIdx) {
					ulCopySrcBufAddr = (U32)pVRLCRDTLog + (offsetof(RDT_MICRON_VRLC_LOG, ubVersion));
					ulCopyDstBufAddr = (U32)gpVRLCVersion;
					ulCopySize = VRLC_VERSION_LENGTH;
					memcpy((void *)ulCopyDstBufAddr, (void *)ulCopySrcBufAddr, ulCopySize);
					//UartPrintf("FW version:%c%c%c%c\n", gpVRLCVersion->ubVersion[0], gpVRLCVersion->ubVersion[1], gpVRLCVersion->ubVersion[2], gpVRLCVersion->ubVersion[3]);
				}
			}
		}
		gMMOInfo.MMOSystemFlag.ubMMOSystemFlag.btInherit = TRUE;
		gFlhEnvMP.ubInfoBlkVRLCDiscardFlag = FALSE;
		//gVRLCSaveData.ubU17VRLCInherit = TRUE;
	}
	else {
		//UartPrintf("\n [vRLC] Get vRLC Fail\n");
		//while (1);
		gMMOInfo.MMOSystemFlag.ubMMOSystemFlag.btInherit = FALSE;
		gFlhEnvMP.ubInfoBlkVRLCDiscardFlag = TRUE;
		//gVRLCSaveData.ubU17VRLCInherit = FALSE;
	}
	//UartPrintf("\nWrite VRLC done Inherit:%d DiscardFlag:%d\n", gMMOInfo.MMOSystemFlag.ubMMOSystemFlag.btInherit, gFlhEnvMP.ubInfoBlkVRLCDiscardFlag);
	M_UART(VUC_, "\nWrite VRLC done");
}



void VUCMicronGetMAGInfomation(U32 ulPayloadAddr, PUSBCURRCMD_t pCurrentCMD)
{
#if (0)
	U16 uwTrimcnt;
	U8 ubDienum;
	U8 *pubMAGInfo;
	U8 ubcmdremain;
	ubcmdremain = gUSBVar.ulCmdRemainDataSize / 1024;
	pubMAGInfo = (U8 *)ulPayloadAddr;


	switch (ubcmdremain) {
	case 32:
		//0~4095 byte
		for (uwTrimcnt = 0; uwTrimcnt < 128; uwTrimcnt++) {
			for (ubDienum = 0; ubDienum < MAX_CE * FREY2_MAX_LUN_NUM; ubDienum++) {
				*pubMAGInfo = gVRLCSaveData.ubInitCenterValue[ubDienum][uwTrimcnt];
				pubMAGInfo++;
			}
		}

		//for LB boundary case
		ulPayloadAddr = M_LB_TO_ADDR(LB_ID_NRW, (gNRWLBMgr.Type[NRWLB_CMD].uwLBOffset + 1) % 1024);
		pubMAGInfo = (U8 *)ulPayloadAddr;

		//4096~8191
		for (uwTrimcnt = 128; uwTrimcnt < 256; uwTrimcnt++) {
			for (ubDienum = 0; ubDienum < MAX_CE * FREY2_MAX_LUN_NUM; ubDienum++) {
				*pubMAGInfo = gVRLCSaveData.ubInitCenterValue[ubDienum][uwTrimcnt];
				pubMAGInfo++;
			}
		}
		break;

	case 24:
		//8192~11007 byte
		for (uwTrimcnt = 256; uwTrimcnt < VRLC_TOTAL_TRIM_CNT_PER_CE; uwTrimcnt++) {
			for (ubDienum = 0; ubDienum < MAX_CE * FREY2_MAX_LUN_NUM; ubDienum++) {
				*pubMAGInfo = gVRLCSaveData.ubInitCenterValue[ubDienum][uwTrimcnt];
				pubMAGInfo++;
			}
		}

		//11008~12287 byte
		for (uwTrimcnt = 0; uwTrimcnt < 40; uwTrimcnt++) {
			for (ubDienum = 0; ubDienum < MAX_CE * FREY2_MAX_LUN_NUM; ubDienum++) {
				*pubMAGInfo = gVRLCSaveData.VRLCMeta[ubDienum].ubTrimCenterValue[uwTrimcnt];
				pubMAGInfo++;
			}
		}

		//for LB boundary case
		ulPayloadAddr = M_LB_TO_ADDR(LB_ID_NRW, (gNRWLBMgr.Type[NRWLB_CMD].uwLBOffset + 1) % 1024);
		pubMAGInfo = (U8 *)ulPayloadAddr;

		//12288~16383 byte
		for (uwTrimcnt = 40; uwTrimcnt < 168; uwTrimcnt++) {
			for (ubDienum = 0; ubDienum < MAX_CE * FREY2_MAX_LUN_NUM; ubDienum++) {
				*pubMAGInfo = gVRLCSaveData.VRLCMeta[ubDienum].ubTrimCenterValue[uwTrimcnt];
				pubMAGInfo++;
			}
		}
		break;

	case 16:
		//16384~20479 byte
		for (uwTrimcnt = 168; uwTrimcnt < 296; uwTrimcnt++) {
			for (ubDienum = 0; ubDienum < MAX_CE * FREY2_MAX_LUN_NUM; ubDienum++) {
				*pubMAGInfo = gVRLCSaveData.VRLCMeta[ubDienum].ubTrimCenterValue[uwTrimcnt];
				pubMAGInfo++;
			}
		}

		//for LB boundary case
		ulPayloadAddr = M_LB_TO_ADDR(LB_ID_NRW, (gNRWLBMgr.Type[NRWLB_CMD].uwLBOffset + 1) % 1024);
		pubMAGInfo = (U8 *)ulPayloadAddr;

		//20480~22015 byte
		for (uwTrimcnt = 296; uwTrimcnt < VRLC_TOTAL_TRIM_CNT_PER_CE; uwTrimcnt++) {
			for (ubDienum = 0; ubDienum < MAX_CE * FREY2_MAX_LUN_NUM; ubDienum++) {
				*pubMAGInfo = gVRLCSaveData.VRLCMeta[ubDienum].ubTrimCenterValue[uwTrimcnt];
				pubMAGInfo++;
			}
		}


		//22016 byte
		*pubMAGInfo = gVRLCInfoManager.ubDoingCE;  //vRLC_AllCentered
		break;

	case 8:
		//reserved
		//memset((void *)pubMAGInfo, 0, SIZE_8KB);
		break;

	default:
		break;
	}
#endif
}
#endif /* (VRLC_EN && (USB == HOST_MODE)) */
