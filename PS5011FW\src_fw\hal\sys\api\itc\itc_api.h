#ifndef _ITC_API_H_
#define _ITC_API_H_

#include "typedef.h"
#include "hal/sys/reg/sys_pd1_reg.h"
#include "aom/aom_api.h"
#if PS5017_EN
#include "setup.h"


/* [191017][Joy]
		13			17
	1	mask		un-mask		disable
	0	un-mask		mask		enable

	macro
		naming: mask	-> action: disable
		naming: unmask	-> action: enable
*/
#define		M_ITC_GET_EVENT0_STATUS0()					(R32_SYS1_ITC[R32_SYS1_SYS_EVT0_STS0])
#define		M_ITC_GET_EVENT0_STATUS1()					(R32_SYS1_ITC[R32_SYS1_SYS_EVT0_STS1])
#define		M_ITC_CLEAR_EVENT0_STATUS0(X)				(R32_SYS1_ITC[R32_SYS1_SYS_EVT0_STS0] |= (X))
#define		M_ITC_CLEAR_EVENT0_STATUS1(X)				(R32_SYS1_ITC[R32_SYS1_SYS_EVT0_STS1] |= (X))

#define		M_ITC_GET_EVENT1_STATUS0()					(R32_SYS1_ITC[R32_SYS1_SYS_EVT1_STS0])
#define		M_ITC_SET_EVENT1_STATUS0(X)					(R32_SYS1_ITC[R32_SYS1_SYS_EVT1_STS0] = (X))
#define		M_ITC_GET_EVENT1_STATUS1()					(R32_SYS1_ITC[R32_SYS1_SYS_EVT1_STS1])
#define		M_ITC_CLEAR_EVENT1_STATUS1(X)				(R32_SYS1_ITC[R32_SYS1_SYS_EVT1_STS1] |= (X))

#define		M_ITC_GET_EVENT2_STATUS()					(R32_SYS1_ITC[R32_SYS1_SYS_EVT2_STS])
#define		M_ITC_CLEAR_EVENT2_STATUS(X)				(R32_SYS1_ITC[R32_SYS1_SYS_EVT2_STS] |= (X))

#define		M_CLR_SYS_ROM_CR_ITC_VLD()					(R32_SYS1_ITC[R32_SYS1_ITC_VLD] &= ~CR_ITC_VLD_BIT) // E17 porting : move from E13 misc to E17 itc
#define		M_SET_SYS_ROM_CR_ITC_VLD()					(R32_SYS1_ITC[R32_SYS1_ITC_VLD] |= CR_ITC_VLD_BIT)  // E17 porting : move from E13 misc to E17 itc

#define		M_ITC_GET_AXIMON_ACCESS_ID()				(R32_SYS1_ITC[R32_SYS1_AXI_RANGE_CTRL])     // E17 porting : move from E13 misc to E17 itc
#define		M_ITC_GET_DBUF2_ACCESS_ADDRESS()			(R32_SYS1_ITC[R32_SYS1_AXIMON_ACCESS_ADR2])     // E17 porting : move from E13 misc to E17 itc
#define		M_ITC_GET_DBUF3_ACCESS_ADDRESS()			(R32_SYS1_ITC[R32_SYS1_AXIMON_ACCESS_ADR3])     // E17 porting : move from E13 misc to E17 itc

#define		M_ITC_GET_PMU_EVT_STS()						(R32_SYS1_ITC[R32_SYS1_PMU_EVT_STS])
#define		M_ITC_CLEAR_PMU_EVT_STS(X)					(R32_SYS1_ITC[R32_SYS1_PMU_EVT_STS] |= ((U32)(X)))

#define		M_ITC_GET_FIQ_EVT()							(R32_SYS1_ITC[R32_SYS1_FIQ_EVENT])
#define		M_ITC_GET_FIQ_VALID_EVT()					(R32_SYS1_ITC[R32_SYS1_FIQ_EVENT] & R32_SYS1_ITC[R32_SYS1_FIQ_MASK])

#define		M_ITC_GET_IRQ_EVT()							(R32_SYS1_ITC[R32_SYS1_IRQ_EVENT])
#define		M_ITC_GET_IRQ_VALID_EVT()					(R32_SYS1_ITC[R32_SYS1_IRQ_EVENT] & R32_SYS1_ITC[R32_SYS1_INT_IRQ_MASK])

#define		M_ITC_GET_HOST_INT()						(R32_SYS1_ITC[R32_SYS1_IRQ_EVENT] & ISR_HOST_EVENT_BIT)
#define		M_ITC_CLEAR_HOST_INT()						(R32_SYS1_ITC[R32_SYS1_IRQ_EVENT] = ISR_HOST_EVENT_BIT)
#define		M_ITC_CLEAR_UART_INT()						(R32_SYS1_ITC[R32_SYS1_IRQ_EVENT] = ISR_PIC_UART_EVENT_BIT)

#define		M_ITC_MASK_FIQ_BITMAP(X)					(R32_SYS1_ITC[R32_SYS1_FIQ_MASK] &= ~((U32)(X)))
#define		M_ITC_MASK_FIQ_ALL()						(R32_SYS1_ITC[R32_SYS1_FIQ_MASK] &= ~ITC_COLLECT)
#define		M_ITC_UNMASK_FIQ_BITMAP(X)					(R32_SYS1_ITC[R32_SYS1_FIQ_MASK] |= ((U32)(X)))
#define		M_ITC_UNMASK_FIQ_ALL()						(R32_SYS1_ITC[R32_SYS1_FIQ_MASK] |= ITC_COLLECT)
#define		M_ITC_GET_FIQ_MASK()						(R32_SYS1_ITC[R32_SYS1_FIQ_MASK])
#define		M_ITC_CLEAR_FIQ_INT(X)						(R32_SYS1_ITC[R32_SYS1_FIQ_EVENT] = ((U32)(X)))

#define		M_ITC_MASK_IRQ_BITMAP(X)					(R32_SYS1_ITC[R32_SYS1_INT_IRQ_MASK] &= ~((U32)(X)))
#define		M_ITC_MASK_IRQ_ALL()						(R32_SYS1_ITC[R32_SYS1_INT_IRQ_MASK] &= ~ITC_COLLECT)
#define		M_ITC_UNMASK_IRQ_BITMAP(X)					(R32_SYS1_ITC[R32_SYS1_INT_IRQ_MASK] |= ((U32)(X)))
#define		M_ITC_UNMASK_IRQ_ALL()						(R32_SYS1_ITC[R32_SYS1_INT_IRQ_MASK] |= ITC_COLLECT)
#define		M_ITC_GET_IRQ_MASK()						(R32_SYS1_ITC[R32_SYS1_INT_IRQ_MASK])
#define		M_ITC_CLEAR_IRQ_INT(X)						(R32_SYS1_ITC[R32_SYS1_IRQ_EVENT] = ((U32)(X)))

#if RDT_UART_TRANSFER
#define	ITC_COLLECT_WITHOUT_HOST			((U32)(INTERRUPT_EVENT_D2H | INTERRUPT_EVENT_COP1_ST3C |\
													INTERRUPT_EVENT_COP1 | INTERRUPT_EVENT_BMU | INTERRUPT_EVENT_MR | INTERRUPT_EVENT_SEC_PERR | INTERRUPT_EVENT_XZIP_PERR |\
													INTERRUPT_EVENT_DMAC	| INTERRUPT_EVENT_PMU_SYSTEM | INTERRUPT_EVENT_SYS_0 | INTERRUPT_EVENT_SYS_1 | INTERRUPT_EVENT_SYS_2 | INTERRUPT_EVENT_FLH_INT_WRAP |\
													INTERRUPT_EVENT_ATCM | INTERRUPT_EVENT_COP1_PARITY_ERR | INTERRUPT_EVENT_AHBX | INTERRUPT_EVENT_TDBG | INTERRUPT_EVENT_PIC_SMBUS | INTERRUPT_EVENT_AXI |\
													INTERRUPT_EVENT_PIC_UART | INTERRUPT_EVENT_PIC_SMBUS | INTERRUPT_EVENT_PIC_I2C | INTERRUPT_EVENT_COP0_INT_WRAP_0 | INTERRUPT_EVENT_COP0_INT_WRAP_1 | INTERRUPT_EVENT_COP0_INT_WRAP_2 |\
													INTERRUPT_EVENT_FIP_ERR | INTERRUPT_EVENT_RS_ERR | INTERRUPT_EVENT_RS))

#define	ITC_COLLECT							((U32)(INTERRUPT_EVENT_HOST | INTERRUPT_EVENT_PCIE | INTERRUPT_EVENT_D2H | INTERRUPT_EVENT_COP1_ST3C |\
													INTERRUPT_EVENT_COP1 | INTERRUPT_EVENT_BMU | INTERRUPT_EVENT_MR | INTERRUPT_EVENT_SEC_PERR | INTERRUPT_EVENT_XZIP_PERR |\
													INTERRUPT_EVENT_DMAC	| INTERRUPT_EVENT_PMU_SYSTEM | INTERRUPT_EVENT_SYS_0 | INTERRUPT_EVENT_SYS_1 | INTERRUPT_EVENT_SYS_2 | INTERRUPT_EVENT_FLH_INT_WRAP |\
													INTERRUPT_EVENT_ATCM | INTERRUPT_EVENT_COP1_PARITY_ERR | INTERRUPT_EVENT_AHBX | INTERRUPT_EVENT_TDBG | INTERRUPT_EVENT_PIC_SMBUS | INTERRUPT_EVENT_AXI |\
													INTERRUPT_EVENT_PIC_UART | INTERRUPT_EVENT_PIC_SMBUS | INTERRUPT_EVENT_PIC_I2C | INTERRUPT_EVENT_COP0_INT_WRAP_0 | INTERRUPT_EVENT_COP0_INT_WRAP_1 | INTERRUPT_EVENT_COP0_INT_WRAP_2 |\
													INTERRUPT_EVENT_FIP_ERR | INTERRUPT_EVENT_RS_ERR | INTERRUPT_EVENT_RS))

#else
#define	ITC_COLLECT_WITHOUT_HOST			((U32)(INTERRUPT_EVENT_D2H | INTERRUPT_EVENT_COP1_ST3C |\
													INTERRUPT_EVENT_COP1 | INTERRUPT_EVENT_BMU | INTERRUPT_EVENT_MR | INTERRUPT_EVENT_SEC_PERR | INTERRUPT_EVENT_XZIP_PERR |\
													INTERRUPT_EVENT_DMAC	| INTERRUPT_EVENT_PMU_SYSTEM | INTERRUPT_EVENT_SYS_0 | INTERRUPT_EVENT_SYS_1 | INTERRUPT_EVENT_SYS_2 | INTERRUPT_EVENT_FLH_INT_WRAP |\
													INTERRUPT_EVENT_ATCM | INTERRUPT_EVENT_COP1_PARITY_ERR | INTERRUPT_EVENT_AHBX | INTERRUPT_EVENT_TDBG | INTERRUPT_EVENT_PIC_SMBUS | INTERRUPT_EVENT_AXI |\
													/*INTERRUPT_EVENT_PIC_UART |*/ INTERRUPT_EVENT_PIC_SMBUS | INTERRUPT_EVENT_PIC_I2C | INTERRUPT_EVENT_COP0_INT_WRAP_0 | INTERRUPT_EVENT_COP0_INT_WRAP_1 | INTERRUPT_EVENT_COP0_INT_WRAP_2 |\
													INTERRUPT_EVENT_FIP_ERR | INTERRUPT_EVENT_RS_ERR | INTERRUPT_EVENT_RS))

#define	ITC_COLLECT							((U32)(INTERRUPT_EVENT_HOST | INTERRUPT_EVENT_PCIE | INTERRUPT_EVENT_D2H | INTERRUPT_EVENT_COP1_ST3C |\
													INTERRUPT_EVENT_COP1 | INTERRUPT_EVENT_BMU | INTERRUPT_EVENT_MR | INTERRUPT_EVENT_SEC_PERR | INTERRUPT_EVENT_XZIP_PERR |\
													INTERRUPT_EVENT_DMAC	| INTERRUPT_EVENT_PMU_SYSTEM | INTERRUPT_EVENT_SYS_0 | INTERRUPT_EVENT_SYS_1 | INTERRUPT_EVENT_SYS_2 | INTERRUPT_EVENT_FLH_INT_WRAP |\
													INTERRUPT_EVENT_ATCM | INTERRUPT_EVENT_COP1_PARITY_ERR | INTERRUPT_EVENT_AHBX | INTERRUPT_EVENT_TDBG | INTERRUPT_EVENT_PIC_SMBUS | INTERRUPT_EVENT_AXI |\
													/*INTERRUPT_EVENT_PIC_UART |*/ INTERRUPT_EVENT_PIC_SMBUS | INTERRUPT_EVENT_PIC_I2C | INTERRUPT_EVENT_COP0_INT_WRAP_0 | INTERRUPT_EVENT_COP0_INT_WRAP_1 | INTERRUPT_EVENT_COP0_INT_WRAP_2 |\
													INTERRUPT_EVENT_FIP_ERR | INTERRUPT_EVENT_RS_ERR | INTERRUPT_EVENT_RS))
#endif

#elif (PS5013_EN)
//***********************************************
//			ITC DEFINE
//***********************************************

#define ITC_GROUP_ID_0_SYSTEM_0				(0)
#define ITC_GROUP_ID_1_SYSTEM_1				(1)
#define ITC_GROUP_ID_2_TIMER				(2)
#define ITC_GROUP_ID_3_PARITY_ERROR			(3)
#define ITC_GROUP_ID_4_NVME_UFS_APU			(4)
#define ITC_GROUP_ID_5_PCIE					(5)
#define ITC_GROUP_ID_6_COP1					(6)
#define ITC_GROUP_ID_7_BMU					(7)
#define ITC_GROUP_ID_8_MR					(8)
#define ITC_GROUP_ID_9_DMAC					(9)
#define ITC_GROUP_ID_10_COP0				(10)
#define ITC_GROUP_ID_11_PIC					(11)
#define ITC_GROUP_ID_12_D2H					(12)
#define ITC_GROUP_ID_13_AHB					(13)
#define ITC_GROUP_ID_14_RS					(14)
#define ITC_GROUP_ID_15_TDBG				(15)
#define ITC_GROUP_ID_16_CPU					(16)
#define ITC_GROUP_ID_17_ITC					(17)
#define ITC_GROUP_ID_18_UNI					(18)
#define ITC_GROUP_ID_19_AXI					(19)
#define ITC_GROUP_ID_20_COP0_MR				(20)
#define ITC_MAX_INTERRUPT_GROUP				(21)

#define ITC_GROUP_ID_0_SYSTEM_0_BIT			(BIT(ITC_GROUP_ID_0_SYSTEM_0))
#define ITC_GROUP_ID_1_SYSTEM_1_BIT			(BIT(ITC_GROUP_ID_1_SYSTEM_1))
#define ITC_GROUP_ID_2_TIMER_BIT			(BIT(ITC_GROUP_ID_2_TIMER))
#define ITC_GROUP_ID_3_PARITY_ERROR_BIT		(BIT(ITC_GROUP_ID_3_PARITY_ERROR))
#define ITC_GROUP_ID_4_NVME_UFS_APU_BIT		(BIT(ITC_GROUP_ID_4_NVME_UFS_APU))
#define ITC_GROUP_ID_5_PCIE_BIT				(BIT(ITC_GROUP_ID_5_PCIE))
#define ITC_GROUP_ID_6_COP1_BIT				(BIT(ITC_GROUP_ID_6_COP1))
#define ITC_GROUP_ID_7_BMU_BIT				(BIT(ITC_GROUP_ID_7_BMU))
#define ITC_GROUP_ID_8_MR_BIT				(BIT(ITC_GROUP_ID_8_MR))
#define ITC_GROUP_ID_9_DMAC_BIT				(BIT(ITC_GROUP_ID_9_DMAC))
#define ITC_GROUP_ID_10_COP0_BIT			(BIT(ITC_GROUP_ID_10_COP0))
#define ITC_GROUP_ID_11_PIC_BIT				(BIT(ITC_GROUP_ID_11_PIC))
#define ITC_GROUP_ID_12_D2H_BIT				(BIT(ITC_GROUP_ID_12_D2H))
#define ITC_GROUP_ID_13_AHB_BIT				(BIT(ITC_GROUP_ID_13_AHB))
#define ITC_GROUP_ID_14_RS_BIT				(BIT(ITC_GROUP_ID_14_RS))
#define ITC_GROUP_ID_15_TDBG_BIT			(BIT(ITC_GROUP_ID_15_TDBG))
#define ITC_GROUP_ID_16_CPU_BIT				(BIT(ITC_GROUP_ID_16_CPU))
#define ITC_GROUP_ID_17_ITC_BIT				(BIT(ITC_GROUP_ID_17_ITC))
#define ITC_GROUP_ID_18_UNI_BIT				(BIT(ITC_GROUP_ID_18_UNI))
#define ITC_GROUP_ID_19_AXI_BIT				(BIT(ITC_GROUP_ID_19_AXI))
#define ITC_GROUP_ID_20_COP0_MR_BIT			(BIT(ITC_GROUP_ID_20_COP0_MR))

//***********************************************
//			ITC MACRO
//***********************************************

#define	M_ITC_GET_VDT1_SYSTEM_EVENT()				((R32_SYS1_ITC[R32_SYS1_INT_STS0] >> VDT1_SYS_EVT_SHIFT) & VDT1_SYS_EVT_MASK)
#define	M_ITC_GET_PERSTN_SYSTEM_EVENT()				(R32_SYS1_ITC[R32_SYS1_INT_STS1] & (PERSTN_SYS_EVT_MASK << PERSTN_SYS_EVT_SHIFT))
#define	M_ITC_CLEAR_RTT1_SYSTEM_EVENT()				(R32_SYS1_ITC[R32_SYS1_INT_STS2] = RTT1_SYS_EVT_1_BIT)
#define	M_ITC_GET_XZIP_PARITY_ERROR_EVENT()			(R32_SYS1_ITC[R32_SYS1_INT_STS3] & XZIP_PER_EVT_BIT)
#define	M_ITC_GET_HOST_INT()						(R32_SYS1_ITC[R32_SYS1_INT_STS7] & HOST_INT_BIT)
#define M_ITC_CLEAR_HOST_INT()						(R32_SYS1_ITC[R32_SYS1_INT_STS7] = HOST_INT_BIT)
#define	M_ITC_GET_COLLECT()							((R32_SYS1_ITC[R32_SYS1_INT_GROUP_STS] & (ITC_COLLECT_MASK << ITC_COLLECT_SHIFT)) >> 0)
#define	M_ITC_MASK_FIQ(X)							(R32_SYS1_ITC[R32_SYS1_FIQ_MASK] |= ((U32)(BIT0 << (X))))
#define	M_ITC_MASK_FIQ_BITMAP(X)					(R32_SYS1_ITC[R32_SYS1_FIQ_MASK] |= ((U32)(X)))
#define	M_ITC_MASK_FIQ_ALL()						(R32_SYS1_ITC[R32_SYS1_FIQ_MASK] |= (ITC_FIQ_MASK_MASK << ITC_FIQ_MASK_SHIFT))
#define	M_ITC_UNMASK_FIQ(X)							(R32_SYS1_ITC[R32_SYS1_FIQ_MASK] &= ~((U32)(BIT0 << (X))))
#define M_ITC_UNMASK_FIQ_BITMAP(X)					(R32_SYS1_ITC[R32_SYS1_FIQ_MASK] &= ~((U32)(X)))
#define	M_ITC_UNMASK_FIQ_ALL()						(R32_SYS1_ITC[R32_SYS1_FIQ_MASK] &= ~(ITC_FIQ_MASK_MASK << ITC_FIQ_MASK_SHIFT))
#define M_ITC_GET_FIQ_MASK()						(R32_SYS1_ITC[R32_SYS1_FIQ_MASK])
#define	M_ITC_MASK_IRQ(X)							(R32_SYS1_ITC[R32_SYS1_INT_IRQ_MASK] |= ((U32)(BIT0 << (X))))
#define	M_ITC_MASK_IRQ_BITMAP(X)					(R32_SYS1_ITC[R32_SYS1_INT_IRQ_MASK] |= ((U32)(X)))
#define	M_ITC_MASK_IRQ_ALL()						(R32_SYS1_ITC[R32_SYS1_INT_IRQ_MASK] |= (ITC_IRQ_MASK_MASK << ITC_IRQ_MASK_SHIFT))
#define	M_ITC_UNMASK_IRQ(X)							(R32_SYS1_ITC[R32_SYS1_INT_IRQ_MASK] &= ~((U32)(BIT0 << (X))))
#define	M_ITC_UNMASK_IRQ_BITMAP(X)					(R32_SYS1_ITC[R32_SYS1_INT_IRQ_MASK] &= ~((U32)(X)))
#define	M_ITC_UNMASK_IRQ_ALL()						(R32_SYS1_ITC[R32_SYS1_INT_IRQ_MASK] &= ~(ITC_IRQ_MASK_MASK << ITC_IRQ_MASK_SHIFT))
#define M_ITC_GET_IRQ_MASK()						(R32_SYS1_ITC[R32_SYS1_INT_IRQ_MASK])
#define	M_ITC_GET_HIGHEST_ID_REG()					(R32_SYS1_ITC[R32_SYS1_HIGHEST_ID])
#define	M_ITC_GET_FIQ_HIGHEST_ID(X)					(( (X) & (FIQ_HIGHEST_ID_MASK << FIQ_HIGHEST_ID_SHIFT)) >> FIQ_HIGHEST_ID_SHIFT)
#define	M_ITC_GET_FIQ_FLAG(X)						(( (X) & FIQ_FLAG_BIT) >> FIQ_FLAG_SHIFT)
#define	M_ITC_GET_IRQ_HIGHEST_ID(X)					(( (X) & (IRQ_HIGHEST_ID_MASK << IRQ_HIGHEST_ID_SHIFT)) >> IRQ_HIGHEST_ID_SHIFT)
#define	M_ITC_GET_IRQ_FLAG(X)						(( (X) & IRQ_FLAG_BIT) >> IRQ_FLAG_SHIFT)
#define	M_ITC_SET_SYS1_INT_PRIORITY_CFG(N, VAL)		(R8_SYS1_ITC[R8_SYS1_INT_PRIORITY_CFG0 + (N)] = (VAL))
#define	M_ITC_GET_SYS1_INT_PRIORITY_CFG(N)			(R8_SYS1_ITC[R8_SYS1_INT_PRIORITY_CFG0 + (N)] & ITC_PRI_CFG_MASK)
#define	M_ITC_CLR_SYS1_INT_PRIORITY_CFG(N)			(R8_SYS1_ITC[R8_SYS1_INT_PRIORITY_CFG0 + (N)] &= (~ITC_PRI_CFG_MASK))
#define M_ITC_GET_PMU_EVENT()						(R32_SYS1_ITC[R32_SYS1_INT_STS1] & PMU_SYS_EVT_MASK)
#define M_ITC_GET_ISR_GROUP_REGISTER(ulRegister, ulMask, ulEntryShift, ulSize)	\
													(((R32_SYS1_ITC[ulRegister] & ulMask) >> ulEntryShift) & BIT_MASK(ulSize))
#define	M_ITC_CHECK_HIGH_PRIORITY_PARITY_ERROR()	((0 < (R32_SYS1_ITC[R32_SYS1_INT_STS3] & DMAC_ROM_EVT_BIT)) || (0 < (R32_SYS1_ITC[R32_SYS1_INT_STS5] & RRAM_PERR_BIT)))
#define	M_ITC_CLEAR_INTERRUPT(ulRegister, ulBit)	do { \
														R32_SYS1_ITC[ulRegister] = BIT(ulBit); \
													} while(0)

#elif (PS5021_EN)

#define	M_ITC_GET_FIQ_EVT()							(R32_SYS1_INTERRUPT_CTRL[R32_SYS1_FIQ_EVENT])
#define	M_ITC_GET_FIQ_VALID_EVT()					(R32_SYS1_INTERRUPT_CTRL[R32_SYS1_FIQ_EVENT] & R32_SYS1_INTERRUPT_CTRL[R32_SYS1_FIQ_MASK])

#define	M_ITC_GET_IRQ_EVT()							(R32_SYS1_INTERRUPT_CTRL[R32_SYS1_IRQ_EVENT])
#define	M_ITC_GET_IRQ_VALID_EVT()					(R32_SYS1_INTERRUPT_CTRL[R32_SYS1_IRQ_EVENT] & R32_SYS1_INTERRUPT_CTRL[R32_SYS1_IRQ_MASK])

#define	M_ITC_CLEAR_FIQ_INT(X)						(R32_SYS1_INTERRUPT_CTRL[R32_SYS1_FIQ_EVENT] = ((U32)(X)))
#define	M_ITC_CLEAR_IRQ_INT(X)						(R32_SYS1_INTERRUPT_CTRL[R32_SYS1_IRQ_EVENT] = ((U32)(X)))

#define	M_ITC_MASK_FIQ_BITMAP(X)					(R32_SYS1_INTERRUPT_CTRL[R32_SYS1_FIQ_MASK] &= ~((U32)(X)))
#define	M_ITC_UNMASK_FIQ_BITMAP(X)					(R32_SYS1_INTERRUPT_CTRL[R32_SYS1_FIQ_MASK] |= ((U32)(X)))
#define	M_ITC_MASK_FIQ_ALL()						(R32_SYS1_INTERRUPT_CTRL[R32_SYS1_FIQ_MASK] &= ~NFIQ0_EN)
#define	M_ITC_GET_FIQ_MASK()						(R32_SYS1_INTERRUPT_CTRL[R32_SYS1_FIQ_MASK])

#define	M_ITC_MASK_IRQ_BITMAP(X)					(R32_SYS1_INTERRUPT_CTRL[R32_SYS1_IRQ_MASK] &= ~((U32)(X)))
#define	M_ITC_UNMASK_IRQ_BITMAP(X)					(R32_SYS1_INTERRUPT_CTRL[R32_SYS1_IRQ_MASK] |= ((U32)(X)))
#define	M_ITC_MASK_IRQ_ALL()						(R32_SYS1_INTERRUPT_CTRL[R32_SYS1_IRQ_MASK] &= ~NIRQ0_EN)
#define	M_ITC_GET_IRQ_MASK()						(R32_SYS1_INTERRUPT_CTRL[R32_SYS1_IRQ_MASK])

#define	M_ITC_GET_HOST_INT()						(R32_SYS1_INTERRUPT_CTRL[R32_SYS1_IRQ_EVENT] & INTERRUPT_EVENT_APU)

#define ITC_COLLECT_WITHOUT_HOST					((U32)(INTERRUPT_EVENT_D2H | INTERRUPT_EVENT_COP1_ST3C | INTERRUPT_EVENT_COP1 |\
													INTERRUPT_EVENT_BMU | INTERRUPT_EVENT_MR | INTERRUPT_EVENT_SEC_PERR | INTERRUPT_EVENT_XZIP_PERR |\
													INTERRUPT_EVENT_DMAC | INTERRUPT_EVENT_PMU | INTERRUPT_EVENT_TS | INTERRUPT_EVENT_VDT |\
													INTERRUPT_EVENT_GPIO | INTERRUPT_EVENT_FLH_INT_WRAP | INTERRUPT_EVENT_ATCM | INTERRUPT_EVENT_COP1_PARITY_ERR |\
													INTERRUPT_EVENT_AHBX | INTERRUPT_EVENT_MS_BASED | INTERRUPT_EVENT_US_BASED | INTERRUPT_EVENT_AXI |\
													INTERRUPT_EVENT_PIC_SMBUS | INTERRUPT_EVENT_PIC_I2C | INTERRUPT_EVENT_COP0_INT0 | INTERRUPT_EVENT_COP0_INT1 |\
													INTERRUPT_EVENT_COP0_INT2 | INTERRUPT_EVENT_FIP_ERR | INTERRUPT_EVENT_RS_ERR | INTERRUPT_EVENT_RS))

#define ITC_COLLECT									((U32)(INTERRUPT_EVENT_APU | INTERRUPT_EVENT_PCIE | INTERRUPT_EVENT_D2H | INTERRUPT_EVENT_COP1_ST3C |\
													INTERRUPT_EVENT_COP1 | INTERRUPT_EVENT_BMU | INTERRUPT_EVENT_MR | INTERRUPT_EVENT_SEC_PERR | INTERRUPT_EVENT_XZIP_PERR |\
													INTERRUPT_EVENT_DMAC | INTERRUPT_EVENT_PMU | INTERRUPT_EVENT_TS | INTERRUPT_EVENT_VDT | INTERRUPT_EVENT_GPIO | INTERRUPT_EVENT_FLH_INT_WRAP |\
													INTERRUPT_EVENT_ATCM | INTERRUPT_EVENT_COP1_PARITY_ERR | INTERRUPT_EVENT_AHBX | INTERRUPT_EVENT_MS_BASED | INTERRUPT_EVENT_US_BASED | INTERRUPT_EVENT_AXI |\
													INTERRUPT_EVENT_PIC_SMBUS | INTERRUPT_EVENT_PIC_I2C | INTERRUPT_EVENT_COP0_INT0 | INTERRUPT_EVENT_COP0_INT1 | INTERRUPT_EVENT_COP0_INT2 |\
													INTERRUPT_EVENT_FIP_ERR | INTERRUPT_EVENT_RS_ERR | INTERRUPT_EVENT_RS))

#endif /* PS5017_EN */

AOM_INIT void InterruptInit(void);
AOM_INIT void InterruptMaskAllGroup(void);
void InterruptEnable(U32 ulIntGroupIdBitMap);
void InterruptDisable(U32 ulIntGroupIdBitMap);

#endif /* _ITC_API_H_ */
