#ifndef _S17_DB_REG_H_
#define _S17_DB_REG_H_

#include "setup.h"
#include "typedef.h"
#include "mem.h"

#define DB_REG_BASE								(DOORBELL_REG_ADDRESS)
#define DB_QBODY_ADDRESS						(BTCM0_RAM_ADDRESS)

#define DB_CTRL_SIZE_PER_QUEUE					(0x20)

#define R8_DB  ((volatile U8  (*)[DB_CTRL_SIZE_PER_QUEUE >> 0])DB_REG_BASE)
#define R16_DB ((volatile U16 (*)[DB_CTRL_SIZE_PER_QUEUE >> 1])DB_REG_BASE)
#define R32_DB ((volatile U32 (*)[DB_CTRL_SIZE_PER_QUEUE >> 2])DB_REG_BASE)

//Doorbell Offset
#define R32_DB_QBASE_ADR						(0x00 >> 2) // Queue Base Address Register

#define R32_DB_WPTR								(0x04 >> 2) // Write Pointer Register

#define R32_DB_RPTR								(0x08 >> 2) // Read Pointer Register

#define R16_DB_WPIU								(0x0C >> 1) // Write Pointer Increase Unit Register

#define R16_DB_WR_CNT							(0x0E >> 1) // Writable Count Register

#define R16_DB_RPIU								(0x10 >> 1) // Read Pointer Increase Unit Register

#define R16_DB_RD_CNT							(0x12 >> 1) // Readable Count Register

#define R16_DB_BUF_LEN							(0x14 >> 1) // Buffer Length Register

#define R8_DB_DATA_SIZE							(0x16) // Data Size Register
#define		DATA_SIZE_1B_LOG						(0x00)
#define		DATA_SIZE_2B_LOG						(0x01)
#define		DATA_SIZE_4B_LOG						(0x02)
#define		DATA_SIZE_8B_LOG						(0x03)
#define		DATA_SIZE_16B_LOG						(0x04)
#define		DATA_SIZE_32B_LOG						(0x05)
#define		DATA_SIZE_64B_LOG						(0x06)
#define		DATA_SIZE_128B_LOG						(0x07)

#define R8_DB_CTRL								(0x17) // Control Register
#define 	VALID_QUEUE_SHIFT						(0) // Valid Queue
#define 	VALID_QUEUE_MASK						(BIT_MASK(1)) // Valid Queue
#define 	RESET_QUEUE_SHIFT						(1) // Reset Queue
#define 	RESET_QUEUE_MASK						(BIT_MASK(1)) // Reset Queue
#define 	QUEUE_FULL_SHIFT						(2)
#define 	QUEUE_FULL_MASK							(BIT_MASK(1))
#define 	QUEUE_EMPTY_SHIFT						(3)
#define 	QUEUE_EMPTY_MASK						(BIT_MASK(1))

//#define R32_DB_QTAIL_ADR						(0x18 >> 2) // Tail info register // E17 remove

#define R64_DB_EMPTY_MASK						(0x600 >> 3)
#define R64_DB_NONEMPTY_STATUS					(0x608 >> 3)
#define R32_DB_CHECK_MASK						(0x610 >> 2)
#define R32_DB_FBASE_ADDR						(0x614 >> 2)
#define R64_DB_CPU_DB_PORT						(0x618 >> 3)

#define R8_RCQ_INFO  ((volatile U8  *)(DB_REG_BASE))
#define R16_RCQ_INFO ((volatile U16 *)(DB_REG_BASE))
#define R32_RCQ_INFO ((volatile U32 *)(DB_REG_BASE))

#define R32_DB_RCQ_TABLE_INFO					(0x780 >> 2)
#define R8_DB_RCQ_TABLE_WPTR					(0x780)
#define R8_DB_RCQ_TABLE_RPTR					(0x781)
#define R8_DB_RCQ_TABLE_POS						(0x782)

#define R8_DB_RCQ_TABLE_INFO					(0x783)
#define R8_RCQ_TABLE_VLD_BIT						(BIT0)
#define R8_RCQ_TABLE_FRE_BIT						(BIT1)
#define R8_RCQ_TABLE_FRE_CLR_BIT					(BIT2)
#define R8_RCQ_TABLE_FRE_RST_BIT					(BIT3)

// Doorbell ID definition
#define DB_DMAC_HIGH_SQ									(0)
#define DB_DMAC_HIGH_CQ									(1)
#define DB_DMAC_NORM_SQ									(2)
#define DB_DMAC_NORM_CQ									(3)

#define DB_BMU_NOBLK_SQ									(4)
#define DB_BMU_BLK_SQ									(5)
#define DB_BMU_CQ										(6)
#define DB_BMU_WR_CQ									(7)

#define DB_APU_WR_SQ									(8)
#define DB_APU_CMPL_SQ									(9)
#define DB_APU_TD_CQ									(10)
#define DB_APU_CMD_CQ									(11)
#define DB_APU_RD_CQ									(12)

#define DB_XZIP_SQ										(13)
#define DB_XZIP_CQ										(14)

#define DB_COP0_RD_SQ									(15)
#define DB_COP0_WR_SQ									(16)
#define DB_COP0_CQ										(17)

#define DB_COP1_SQ										(18)
#define DB_COP1_CQ 										(19)

#define DB_FLH_MSG_CQ									(20)
#define DB_RS_MSG_CQ									(21)

#define DB_PIC_CQ										(22)
#define DB_LOG_MSG_CQ									(23)
#define DB_ERR_MSG_CQ									(24)

#define DB_FW_SQ0										(25)
#define DB_FW_CQ0										(26)
#define DB_FW_SQ1										(27)
#define DB_FW_CQ1										(28)
#define DB_FW_SQ2										(29)
#define DB_FW_CQ2										(30)
#define DB_FW_SQ3										(31)
#define DB_FW_CQ3										(32)

#define DB_BMU_WR_CQ2									(33)
#define DB_BMU_SEARCH_SQ								(34)
#define DB_BMU_NOBLK_CQ									(35) //non blocking CQ
#define DB_COP0_SQ1      								(36)
#define DB_QUEUE_CNT      								(37)

#endif /* _S17_DB_REG_H_ */
