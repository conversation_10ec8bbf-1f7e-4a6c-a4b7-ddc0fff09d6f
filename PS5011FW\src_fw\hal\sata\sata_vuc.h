#ifndef _SATA_VUC_H_
#define _SATA_VUC_H_

#if (HOST_MODE == SATA)

// a
#include "aom/aom_api.h"

/*=====================================================================================
                                     Enum Definition
=====================================================================================*/
/* SATA Burner VUC */
enum {
	SATA_VUC_READ  = 0x21,
	SATA_VUC_WRITE = 0x31,
};

#define M_SATA_VUC_CHECK_APKEY_FORMAT() ((0x00 == M_SATA_GET_FEATURE()) && (0x6f == M_SATA_GET_SECTOR_CNT()) && (0xfe == M_SATA_GET_LBA_L()) && (0xef == M_SATA_GET_LBA_M()) && (0xfa == M_SATA_GET_LBA_H()))

/*=====================================================================================
                                  Function Declaretion
=====================================================================================*/
#if (RDT_MODE_EN || RDT_BURNER_MODE_EN || (!PS5017_EN))
AOM_SATA void SATAVUCDataTransfer(U32 ulLength, U32 ulPBAddr, U8 ubReadWriteCmd);
#endif
AOM_SATA U8 SATAVUCCheckEnableAPKey(void);
#if (INDUSTRY_GRADE_EN && (!BURNER_MODE_EN) && (!RDT_MODE_EN))
AOM_SATA void SATAVUCCheckEnableAPKeyAdvantech(U32 ulBufAddr);
#endif /*(INDUSTRY_GRADE_EN && (!BURNER_MODE_EN) && (!RDT_MODE_EN))*/
AOM_SATA void SATAVUCReadSectorsAP(void);
AOM_SATA void SATAVUCWriteSectorsAP(void);

#endif /* (HOST_MODE == SATA) */

#endif /* _SATA_CMD_H_ */
