#include "hal/fip/fip.h"
#include "hal/fip/fip_api.h"
#include "hal/pic/i2c/i2cm_api.h"
#include "hal/pic/i2c/i2cm_device_api.h"
#include "hal/pic/uart/uart_api.h"
#include "nvme_api/nvme/shr_hal_nvme.h"
#include "vuc/VUC_command.h"
#include "vuc/VUC_DetectPeripheral.h"
#include "hal/sys/api/mux/mux.h"
#include "hal/sys/api/mux/mux_api.h"
#include "hal/sys/api/clk/clk_api.h"
#if (PS5017_EN)
#include "hal/sys/api/padc/padc_api.h"
#include "hal/sys/api/gpio/gpio_api.h"
#include "hal/sys/api/rtt/rtt_api.h"
#include "lpm/lpm_api.h"
#endif /* (PS5017_EN) */

void VUC_DetectPeripheral(VUC_OPT_HCMD_PTR_t pCmd)
{

#if (HOST_MODE == NVME)
	U8 ubReadWriteOperation = pCmd->vuc_sqcmd.vendor.DetectPeripheral.ubReadWriteOperation;
	U8 ubRegAddr = pCmd->vuc_sqcmd.vendor.DetectPeripheral.ubRegAddr;
	U8 ubWriteData = pCmd->vuc_sqcmd.vendor.DetectPeripheral.ubWriteData;
#endif /* (HOST_MODE == NVME) */
	I2C_CTRL_PARAM i2c_param;
	U8 ubGroup = I2C_GROUP_4;
	U8 ubMode, ubOpCnt, ubDataCnt, ubBuf1, ubResult;
	U16 uwSlaveAddr = pCmd->vuc_sqcmd.vendor.DetectPeripheral.ubSlaveAddr, uwBuf, uwBuf0;
	U8 ubSubFea = pCmd->vuc_sqcmd.vendor.DetectPeripheral.ubSubFeature;
	U32 ulTemp, ulTempMux;
	U32 ulReturnBuf = gulVUCBufAddr;
	U64 uoStartTriggerTime;
	U8 ubGroupOffset;
	switch (ubSubFea) {
#if (HOST_MODE == NVME)
	case VUC_PERIPHERAL_PMIC_ID:
#endif /* (HOST_MODE == NVME) */
	case VUC_PERIPHERAL_TML_ID:
#if (HOST_MODE == SATA)
	case VUC_PERIPHERAL_LOADSWITCH_ID:
#endif
#if (PS5017_EN) // S17 Needs to Pull high GPIO10&11 to 5.5K to use I2C Detect TS.
		M_CLR_PAD_PU_SETTING(M_GPIO_10);
		M_SET_PAD_PU_SETTING(M_GPIO_10, PULL_UP_5P5K); //[BC_N]pull-high 5.5k
		M_CLR_PAD_PU_SETTING(M_GPIO_11);
		M_SET_PAD_PU_SETTING(M_GPIO_11, PULL_UP_5P5K); //[BC_N]pull-high 5.5k
#endif /* (PS5017_EN) */

		ubGroupOffset = M_I2CM_GROUP_OFFSET(I2C_GROUP_1);
		uoStartTriggerTime = M_RTT_GET_CNT_MILLISECOND(RTT0);

		//Wait former I2C command from TT complete
		while (M_I2CM_GET_START_BIT(ubGroupOffset)) {
			if ( (M_RTT_GET_CNT_MILLISECOND(RTT0) - uoStartTriggerTime) > I2C_TRIGGER_TIMEOUT_MILLISECOND) {
				M_UART(DEBUG_, "I2C OT\n");
				((U8 *)ulReturnBuf)[0] = VUC_DETECT_PERIPHERAL_DETECT_FAIL;
				return ;
			}
		}
		M_MUX_BACK_UP_SETTING(ulTempMux);
		mux_i2cm_scl_and_sda();
		break;

	case VUC_PERIPHERAL_PMIC6117_ID:
		if ((VUC_ADM_NONDATA != pCmd->vuc_sqcmd.vendor.ubOPCode) && (VUC_ADMIN_ENCRYPT_NO_DATA != pCmd->vuc_sqcmd.vendor.ubOPCode)) {
			pCmd->ubState = CMD_ERROR;
			gVUCVar.ubVucCmdErrStatus = HOST_ERROR_PARAMETER;
			return;
		}

		M_MUX_BACK_UP_SETTING(ulTempMux);
		mux_gpio_10_11_12();
		break;

	default:
		pCmd->ubState = CMD_ERROR;
		gVUCVar.ubVucCmdErrStatus = HOST_ERROR_PARAMETER;
		return;
		break;
	}
	if (VUC_PERIPHERAL_TML_ID == ubSubFea) {     //Thermal sensor
		ubMode = (GP_DIR_BIT );
		ubDataCnt = 2;
		ubOpCnt = 1;
		I2CConfig(&i2c_param, ubGroup, uwSlaveAddr, ubMode, ubDataCnt, ubOpCnt);
		i2c_param.ubOp[0] = 0x00;
		I2COperation(&i2c_param);
		ubResult = I2CTriggerProcess(&i2c_param);
		if ((FAIL == ubResult) || I2CIsAddrNACK(&i2c_param) || I2CIsDataNACK(&i2c_param)) {
			M_UART(VUC_, "There is no Thermal Sensor\n");
			((U8 *)ulReturnBuf)[0] = VUC_DETECT_PERIPHERAL_DETECT_FAIL;
		}
		else {
			memset((void *)ulReturnBuf, VUC_DETECT_PERIPHERAL_DETECTED, sizeof(U8));
			uwBuf  = (U16)M_I2CM_GET_BUF0();
			uwBuf0 = ((U8)uwBuf << TML_DATA_SHIFT);
			ubBuf1 = (U8)((uwBuf & FIP_BYTE_HIGH_4BIT_MASK) >> TML_DATA_SHIFT);
			ulTemp = uwBuf0 | ubBuf1;

			ulTemp = ulTemp * TML_RES_DIVIDEN / TML_RES_DIVISOR;
			M_UART(VUC_, "Read Val:%d(C)\n", ulTemp);
		}
	}
#if (HOST_MODE == NVME)
	else if (VUC_PERIPHERAL_PMIC_ID == ubSubFea &&  VUC_DETECT_PERIPHERAL_READ == ubReadWriteOperation ) {     //PMIC
		ubDataCnt = 1;
		ubOpCnt = 1;
		ubMode = ( GP_DIR_BIT | GP_BUF_SEL_BIT);
		I2CConfig(&i2c_param, ubGroup, uwSlaveAddr, ubMode, ubDataCnt, ubOpCnt);
		i2c_param.ubOp[0] = ubRegAddr;
		I2COperation(&i2c_param);
		ubResult = I2CTriggerProcess(&i2c_param);
		if ((FAIL == ubResult) || I2CIsAddrNACK(&i2c_param) || I2CIsDataNACK(&i2c_param)) {
			M_UART(VUC_, "There is no PMIC\n");
			((U8 *)ulReturnBuf)[0] = VUC_DETECT_PERIPHERAL_DETECT_FAIL;
		}
		else if (ubRegAddr) {
			((U32 *)ulReturnBuf)[0] = M_I2CM_GET_BUF1();
		}
		else {
			((U8 *)ulReturnBuf)[0] = VUC_DETECT_PERIPHERAL_DETECTED;
		}
	}
	else if (VUC_PERIPHERAL_PMIC_ID == ubSubFea && VUC_DETECT_PERIPHERAL_WRITE == ubReadWriteOperation) {
		M_I2CM_GROUP_SLAVE_ADDR(I2C_GROUP_1, uwSlaveAddr);
		M_I2CM_GROUP_OPERATION_REG(I2C_GROUP_1, 0, ubRegAddr);
		M_I2CM_GROUP_OPERATION_REG(I2C_GROUP_1, 1, ubWriteData);
		M_I2CM_GROUP_CTRL(I2C_GROUP_1, 0, 0, 2); //(GroupId, ModeSelect, DataCnt, OperationCnt)
		M_I2CM_SET_START_BIT(I2C_GROUP_1);
		while (M_I2CM_GET_START_BIT(I2C_GROUP_1)) {
			if ( (M_RTT_GET_CNT_MILLISECOND(RTT0) - uoStartTriggerTime) > I2C_TRIGGER_TIMEOUT_MILLISECOND) {
				((U8 *)ulReturnBuf)[0] = VUC_DETECT_PERIPHERAL_DETECT_FAIL;
				M_UART(VUC_, "I2C Write Reg Time Out\n");
				return;
			}
		}
	}
#endif /* (HOST_MODE == NVME) */
#if (HOST_MODE == SATA)
	else if (VUC_PERIPHERAL_LOADSWITCH_ID == ubSubFea) {	//Load Switch
		SATAVUCDetectLoadSwitch(pCmd, ulReturnBuf);
	}
#endif /* (HOST_MODE == SATA) */
#if (PS5017_EN)
	else if (VUC_PERIPHERAL_PMIC6117_ID == ubSubFea) {

		//Use gubLPMGPIOWakeFlag for avoid enter LPM then effect measure result,
		//After detect flow, MP will power cycle
		gubLPMGPIOWakeFlag = TRUE;

		if (TRUE == pCmd->vuc_sqcmd.vendor.DetectPeripheral.btLowVoltageEn) {

			// Flash IO VDT chnage to interrupt mode
			if (M_GET_VUC_POWER_MODE()) {
				M_PMU_VDT_DISABLE(CR_FIO12_PG_PD1_BIT);
				M_GPIO_SET_VDT_DETECTOR(CR_VDT_DET_XVDT_FIO12_PG_BIT);
			}
			else {
				M_PMU_VDT_DISABLE(CR_FIO18_PG_PD0_BIT);
				M_GPIO_SET_VDT_DETECTOR(CR_VDT_DET_XVDT_FIO18_PG_BIT);
			}

			//Set GPIO12 to low, then change to output mode
			M_CLR_GPIO_O12();
			M_SET_PAD_DIR_OUT(PADC_GPIO_12);

			//Wait for VDT interrupt
			M_RTT_IDLE_MS(10);

			//if detect FIO VDT interrupt, then restore GPIO 12 to high
			if (MISC_SIM_LOG_8_DETECT_PMIC_VDT_CNT == M_GET_CR_SIM_CTRL8()) {
				M_SET_GPIO_O12();

				M_RTT_IDLE_MS(100);
			}

			// Flash IO VDT restore to reset mode
			if (M_GET_VUC_POWER_MODE()) {
				M_GPIO_CLEAR_VDT_DETECTOR(CR_VDT_DET_XVDT_FIO12_PG_BIT);
				M_PMU_VDT_ENABLE(CR_FIO12_PG_PD1_BIT);
			}
			else {
				M_GPIO_CLEAR_VDT_DETECTOR(CR_VDT_DET_XVDT_FIO18_PG_BIT);
				M_PMU_VDT_ENABLE(CR_FIO18_PG_PD0_BIT);
			}
		}
		else {
			//Set GPIO12 to high, then change to output mode
			M_SET_GPIO_O12();
			M_SET_PAD_DIR_OUT(PADC_GPIO_12);
		}
	}
#endif /*(PS5017_EN)*/

	switch (ubSubFea) {
#if (HOST_MODE == NVME)
	case VUC_PERIPHERAL_PMIC_ID:
#endif /* (HOST_MODE == NVME) */
	case VUC_PERIPHERAL_TML_ID:
#if (HOST_MODE == SATA)
	case VUC_PERIPHERAL_LOADSWITCH_ID:
	case VUC_PERIPHERAL_PMIC6117_ID:
#endif
		M_MUX_RESTROE_SETTING(ulTempMux);
		break;

	default:
		break;
	}

}

#if (HOST_MODE == SATA)
void SATAVUCDetectLoadSwitch (VUC_OPT_HCMD_PTR_t pCmd, U32 ulReturnBuf)
{
	U8 ubOriginInterface = 0, ubResult;
#if (!BURNER_MODE_EN)
	//FW need to slow down the flash clock first
	U8 ubOriginClock = gFlhEnv.ubCurrentFlashClock;
	gFlhEnv.ubTargetFlashClock = FIP_FLASH_CLOCK_41P7MHZ;
	ClockIPConfig();
	FlaMDLLTracking(ENABLE);
#endif	/* (!BURNER_MODE_EN) */
	if (gFlhEnv.ubCurrentInterface != gFlhEnv.ubDefaultInterface) {
		ubOriginInterface = gFlhEnv.ubCurrentInterface;
		gFlhEnv.ubTargetInterface = gFlhEnv.ubDefaultInterface;
		FlaSetFlashMode(FIP_SET_FLASH_MODE_SATA_MP);
	}

	if (INTERFACE_ERR_Default != gFlhEnv.ubSetInterfaceErrCode) {
		pCmd->ubState = CMD_ERROR;
		return;
	}

	ubResult = FIPLoadSwitchTest();

	if (FAIL == ubResult) {
		M_UART(VUC_, "There is no Load Switch or Load Switch is damaged\n");
		((U8 *)ulReturnBuf)[0] = VUC_DETECT_PERIPHERAL_DETECT_FAIL;
	}
	else {
		M_UART(VUC_, "Load Switch is good\n");
		memset((void *)ulReturnBuf, VUC_DETECT_PERIPHERAL_DETECTED, sizeof(U8));
	}

	if (gFlhEnv.ubCurrentInterface != ubOriginInterface) {
		gFlhEnv.ubTargetInterface = ubOriginInterface;
		FlaSetFlashMode(FIP_SET_FLASH_MODE_SATA_MP);
	}

	if (INTERFACE_ERR_Default != gFlhEnv.ubSetInterfaceErrCode) {
		pCmd->ubState = CMD_ERROR;
		return;
	}

#if (!BURNER_MODE_EN)
	//FW need to speedup the flash clock finally
	gFlhEnv.ubTargetFlashClock = ubOriginClock;
	ClockIPConfig();
	FlaMDLLTracking(ENABLE);
#endif	/* (!BURNER_MODE_EN) */
}
#endif /* (HOST_MODE == SATA) */
