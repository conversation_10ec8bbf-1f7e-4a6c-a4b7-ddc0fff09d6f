#ifndef _VUC_SMARTRESCUE_API_H_
#define _VUC_SMARTRESCUE_API_H_

#include "common/symbol.h"
#include "env.h"

#ifdef _SMARTRESCUE_EN_					/* Configuration Symbol in DS-5 IDE */
#define SMARTRESCUE_EN					(_SMARTRESCUE_EN_)
#else /* _SMARTRESCUE_EN_	 */
#define SMARTRESCUE_EN					(FALSE)
#endif /* _SMARTRESCUE_EN_	 */

//Note:
//Use MP only if SMART_RESCUE_EN == FALSE, otherwise CriticalAssert 0x8A04 would happen since ReadSysInfo try to Load VBRMP after MP clears VBRMP.
//Load BURNER with <PERSON><PERSON>_Tool if SMART_RESCUE_EN == TRUE.
#define SMART_RESCUE_EN (BURNER_MODE_EN && SMARTRESCUE_EN)

#endif /* _VUC_SMARTRESCUE_API_H_ */
