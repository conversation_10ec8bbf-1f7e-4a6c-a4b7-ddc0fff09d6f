/****************************************************************************
*
*  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
*  All rights reserved
*
*  The content of this document is confidential and shall be applied
*  subject to the terms and conditions of the license agreement and
*  other applicable laws. Any unauthorized access, use or disclosure
*  of this document is strictly prohibited and may be punishable
*  under laws.
*
*  zip.c
*
*
*
****************************************************************************/

/*
* ---------------------------------------------------------------------------------------------------
*   header files
* ---------------------------------------------------------------------------------------------------
*/
#include "setup.h"
#include "typedef.h"
#include "common/fw_common.h"
#include "hal/zip/zip_api.h"
#if VS_SIM_EN
#include "ip/zip/zip.h"
#endif /* VS_SIM_EN */

/*
 * ---------------------------------------------------------------------------------------------------
 *   definitions
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *  enum
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *  data types
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *   macros
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *   extern global variables
 * ---------------------------------------------------------------------------------------------------
 */
DZIPSettingInfo_t gDZIP;

/*
 * ---------------------------------------------------------------------------------------------------
 *   static global variables
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *   private prototypes
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *   private
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *   public
 * ---------------------------------------------------------------------------------------------------
 */
void DZIPInit(void)
{

#if (DZIP_EN)
	if (FALSE == gDZIP.btSetByInfoBlock) {
		if (VS_SIM_EN) {
			gDZIP.btDZIPEn = TRUE;
		}
		else {
			gDZIP.btDZIPEn = (TRUE == FPGA_RTL_2CH) ? FALSE : TRUE;
		}
	}
	gDZIP.Resolution = (TRUE == gDZIP.btDZIPEn) ? gPCAInfo.ubResolution : DZIP_RESOLUTION_BYPASS;
	M_DZIP_SET_RESOLUTION(gDZIP.Resolution);
#else /* (DZIP_EN) */
	gDZIP.Resolution = DZIP_RESOLUTION_BYPASS;
	M_DZIP_SET_RESOLUTION(gDZIP.Resolution);
#endif /* (DZIP_EN) */
#if PS5017_EN
	M_DZIP_SET_TIMEOUT(DZIP_AXIS_TOUT_CNT_MAX);
#endif /* PS5017_EN */
}
