#ifndef _MEDIA_SCAN_API_H_
#define _MEDIA_SCAN_API_H_

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "fw_vardef.h"
#include "aom/aom_api.h"
#include "symbol.h"
#include "setup.h"
#include "typedef.h"
#include "ftl/ftl_api.h"

/*
 * ---------------------------------------------------------------------------------------------------
 *  data types
 * ---------------------------------------------------------------------------------------------------
 */
typedef enum {
	MEDIASCAN_INIT = 0,
	MEDIASCAN_SELECT_VICTIM_UNIT,
	MEDIASCAN_SELECT_VICTIM_PLANE,
	MEDIASCAN_READ_VERIFY,
	MEDIASCAN_WAIT_READ_VERIFY_DONE,
#if VRLC_EN
	MEDIASCAN_SAVE_INITINFO,
	MEDIASCAN_WAIT_SAVE_INITINFO_DONE,
#endif /* VRLC_EN */
#if (PARTIAL_BLOCK_MEDIA_SCAN_EN)
	MEDIASCAN_NDEP_CHECK,
	MEDIASCAN_WAIT_NDEP_CHECK_DONE,
	MEDIASCAN_ALL_NDEP_CHECK_DONE,
	MEDIASCAN_NDEP_CHECK_DONE,
#endif
	MEDIASCAN_FINISH
} MediaScanState_t;

typedef enum {
#if (IM_N48R)
	MEDIA_SCAN_TYPE_TOTAL = 0,	//include normal and partial blk type
#else /* (IM_N48R) */
	MEDIA_SCAN_TYPE_NORMAL = 0,
	MEDIA_SCAN_TYPE_PARTIAL,
#endif /* (IM_N48R) */
#if VRLC_EN
	MEDIA_SCAN_TYPE_VRLC,
#endif /* VRLC_EN */
	MEDIA_SCAN_TYPE_NUM,
} MEDIA_SCAN_TYPE_t;

typedef enum {
#if IM_N48R
	MEDIA_SCAN_EVENT_NORMAL_QLC_DATA_UNIT = 0,
	MEDIA_SCAN_EVENT_NORMAL_SLC_FW,
	MEDIA_SCAN_EVENT_NORMAL_SLC_DATA_UNIT,
#else /* IM_N48R */
	MEDIA_SCAN_EVENT_NORMAL_STATIC_UNIT = 0,
#endif /*IM_N48R */
	MEDIA_SCAN_EVENT_PARTIAL_GR_UNIT,
	MEDIA_SCAN_EVENT_PARTIAL_TABLE_UNIT,
	MEDIA_SCAN_EVENT_PARTIAL_COPYUNIT,
	//B47R 4~6 can't move
	MEDIA_SCAN_EVENT_PARTIAL_INITINFO_UNIT,
	MEDIA_SCAN_EVENT_PARTIAL_VT_MOTHER,
	MEDIA_SCAN_EVENT_PARTIAL_VT_CHILD,
	//B47R 4~6 can't move
	MEDIA_SCAN_EVENT_PARTIAL_PARITY,
	MEDIA_SCAN_EVENT_NUM,
	MEDIA_SCAN_EVENT_PARTIAL_DRIVELOG,
	MEDIA_SCAN_EVENT_PARTIAL_OPENBLOCK_RS,
	MEDIA_SCAN_EVENT_PARTIAL_GCGR,          //no support
} MEDIA_SCAN_EVENT_t;

typedef enum {
	MEDIA_SCAN_SLC_EVENT_SYSTEM_BLK_0,
	MEDIA_SCAN_SLC_EVENT_SYSTEM_BLK_1,
	MEDIA_SCAN_SLC_EVENT_DBT_BLK_0,
	MEDIA_SCAN_SLC_EVENT_DBT_BLK_1,
	MEDIA_SCAN_SLC_EVENT_CODE_BLK_CH0_0,
	MEDIA_SCAN_SLC_EVENT_CODE_BLK_CH0_1,
	MEDIA_SCAN_SLC_EVENT_CODE_BLK_CH1_0,
	MEDIA_SCAN_SLC_EVENT_CODE_BLK_CH1_1,
#if(!PS5017_EN)
	MEDIA_SCAN_SLC_EVENT_CODE_BLK_CH2_0,
	MEDIA_SCAN_SLC_EVENT_CODE_BLK_CH2_1,
	MEDIA_SCAN_SLC_EVENT_CODE_BLK_CH3_0,
	MEDIA_SCAN_SLC_EVENT_CODE_BLK_CH3_1,
#endif
	MEDIA_SCAN_SLC_EVENT_BANKING_BLK_CH0_0,
	MEDIA_SCAN_SLC_EVENT_BANKING_BLK_CH0_1,
	MEDIA_SCAN_SLC_EVENT_BANKING_BLK_CH1_0,
	MEDIA_SCAN_SLC_EVENT_BANKING_BLK_CH1_1,
#if(!PS5017_EN)
	MEDIA_SCAN_SLC_EVENT_BANKING_BLK_CH2_0,
	MEDIA_SCAN_SLC_EVENT_BANKING_BLK_CH2_1,
	MEDIA_SCAN_SLC_EVENT_BANKING_BLK_CH3_0,
	MEDIA_SCAN_SLC_EVENT_BANKING_BLK_CH3_1,
#endif
	MEDIA_SCAN_SLC_EVENT_TABLE,
	MEDIA_SCAN_SLC_EVENT_NUM,
} MEDIA_SCAN_SLC_EVENT_t;

typedef enum {
#if MEDIA_SCAN_DEBUG_EN
	MEDIASCAN_QLC_TEST_START,
#endif /* MEDIA_SCAN_DEBUG_EN */
	MEDIASCAN_QLC_GET_READ_OFFSET,
	MEDIASCAN_QLC_FINE_CALIBRATION,
	MEDIASCAN_QLC_GET_FINE_READ_OFFSET,
	MEDIASCAN_QLC_DISABLE_CALIBRATION,
	MEDIASCAN_QLC_READ_SAMPLING_OFFSET,
	MEDIASCAN_QLC_CLEAR_PREFIX_READ,
#if MEDIA_SCAN_DEBUG_EN
	MEDIASCAN_QLC_TEST_END,
#endif /* MEDIA_SCAN_DEBUG_EN */
	MEDIASCAN_QLC_PAGE_SCAN_FLOW_FINISH
} MediaScanQLCPageScanFlowState_t;

typedef enum {
#if MEDIA_SCAN_DEBUG_EN
	MEDIASCAN_SLC_TEST_START,
#endif /* MEDIA_SCAN_DEBUG_EN */
	MEDIASCAN_SLC_AUTO_READ_CALIBRATION,
	MEDIASCAN_SLC_GET_READ_OFFSET,
	MEDIASCAN_SLC_DISABLE_AUTO_READ_CALIBRATION,
	MEDIASCAN_SLC_READ_SAMPLING_OFFSET,
	MEDIASCAN_SLC_CLEAR_PREFIX_READ,
#if MEDIA_SCAN_DEBUG_EN
	MEDIASCAN_SLC_TEST_END,
#endif /* MEDIA_SCAN_DEBUG_EN */
	MEDIASCAN_SLC_PAGE_SCAN_FLOW_FINISH
} MediaScanSLCPageScanFlowState_t;

typedef enum {
	MEDIASCAN_READ_CMD = 0,
	MEDIASCAN_READ_PREFIX_CMD,	//2E + 00-30
	MEDIASCAN_SET_TRIM_CMD,	//need trim addr.
	MEDIASCAN_GET_TRIM_CMD,	//need trim addr.
	//MEDIASCAN_CHECK_TRIM_CMD
} MediaScanCMDMTType_t;

typedef enum {
	MEDIASCAN_GET_EC = 0,	//05-E0 (no need buf)
	MEDIASCAN_GET_READ_OFFSET,	//07-E0 (need buf)
	MEDIASCAN_GET_READ_LEVEL	//only DMA_R (need buf)	//need trim addr.
} MediaScanDMAMTType_t;

typedef enum {
	PHYSICAL_BLK_SYSTEM = 0,
	PHYSICAL_BLK_DBT,
	PHYSICAL_BLK_CODE,
	PHYSICAL_BLK_BANKING,
	PHYSICAL_BLK_VTMOTHER,
	NON_PHYSICAL_BLK
} PhysicalBlkType_t;

#define MEDIA_SCAN_WEAK_PAGE_CNT		((IM_B47R || IM_B37R) ? 2 : 0) // Only B47R need to do weak page read
typedef union {
	volatile U8 ubAll;
	struct {
		volatile U8 btNormalUnit : 1;
			volatile U8 btPartialUnit : 1;
#if VRLC_EN
			volatile U8 btVRLCUnit    : 1;
			U8 reserved : 5;
#else /* VRLC_EN */
			U8 reserved : 6;
#endif /* VRLC_EN */
		} BT;
	} MediaScanType_t;

	typedef struct MediaScan_t MEDIA_SCAN, *MEDIA_SCAN_PTR;
	struct MediaScan_t {
	MediaScanType_t Request;
	MediaScanType_t Doing;

	//Time
	U64 uoStartTime[MEDIA_SCAN_TYPE_NUM];
	U16 uwFrequencySecound;        //N48 unit: 100 ms. Other unit: s.
	U64 uoPreviousScanInterval;	//check dummy read time
	U64 uoFullScanGroupStartTime;
	U64 uoScanGroupStartTime;
	U64 uoRequestStartTime;

	//scan flow
	U16 uwVictimUnit;
	U16 uwCurrentUnit[MEDIA_SCAN_EVENT_NUM];
	U16 uwScanIdx[MEDIA_SCAN_EVENT_NUM];
	U32 ulStartPlaneIdx[MEDIA_SCAN_EVENT_NUM];
	U32 ulSentinelPlaneIdx[MEDIA_SCAN_EVENT_NUM];
#if (IM_N48R)
	U16 uwCurrentPlaneIdx[MEDIA_SCAN_EVENT_NUM - 3];
#else /* (IM_N48R) */
	U16 uwCurrentPlaneIdx[MEDIA_SCAN_EVENT_NUM - 1];
	U8 ubPartialFrequencyHour; //unit: (hr)
	U8 ubEraseCheckBitMap[MEDIA_SCAN_EVENT_NUM];
	U16 uwMediaScanSLCWeakPageList[MEDIA_SCAN_WEAK_PAGE_CNT];
	U16 uwMediaScanTLCWeakPageList[MEDIA_SCAN_WEAK_PAGE_CNT];
	U16 uwTLCStartScanIdx;
	U16 uwSLCStartScanIdx;
	U64 uoCurrentScanInterval;
	U8 ubScanSuperPageCnt : 4;	//B47R: need scan 8 page
	U8 ubWeakPageDoneCnt : 3;
	U8 btReserve : 1;
#endif /* (IM_N48R) */
#if (IM_B47R || IM_B37R)
	U8 ubPartialScanErasePageIdx;
#endif
	//U32 ulErrorPlaneBankBMP;
	//U32 ulTotalPlaneCnt;	//temp remove

	//NDEP
	U16 uwPorNDEPCheckGRFailUnit;
	U16 btPorNDEPCheckGRFail : 1;

	//scan flow
	U16 btWaitReadVerifyDone : 1;
	U16 btStartMediaScanFromRequest : 1;
	U16 btWakeUpFromLPM3 : 1;
	U16 uwSLCVictimPage : 12;	//same as Scan Idx of static unit
	U8 ubVictimWordline;		//QLC: 3-182, without 92,93. SLC: 4-181, without 92,93.
	U8 ubVictimScanGroup : 4;	//0-7
	U8 ubVictimPageStack : 2;	//0-3
	U8 btVictim : 1;
	U8 btFirstVictimUnit : 1;

	U32 uwQLCStartUnit : 12;
	U32 uwSLCStartUnit : 12;
	U32 ubMLCPageBMP : 4;	//LP, UP
	U32 ubQLCPageBMP : 4;	//LP, UP, XP, TP
	U8 ubMLCSelectPageNum : 2;	//1(UP)
	U8 ubQLCSelectPageNum : 3;	//2(LP,TP)
	U8 ubScanSuperPageCurrentCnt : 2;
	U8 btIsPersistent : 1;
	U32 ulTotalScanSuperPageCnt;	// For Calculate DieOffset, add 1 when Set a Read Verify Event(Not reset to 0)
	U16 uwQLCDataVictimPageCnt;
	U16 uwSLCDataVictimPageCnt;

	U8 ubState : 4;
	U8 ubEventIdx : 4;	//10
	U8 ubCurrentSLCNonDataEventIdx : 5; //21
	U8 ubTestCnt : 7;	//max: 0x40
	U8 btEn : 1;
	U8 btPartialBlkEn : 1;
	U8 btStartFromInitial : 1;
	U8 btInitialPartialScanDone: 1;
	U32 btStartNewScanGroup : 1;
	U32 btIsScanGroupIdle : 1;
	U32 btIsFullScanGroupIdle : 1;

	//dummy read
	U32 ubDummyReadDone : 1;
	U32 ulPartialDummyReadDoneBMP : 7;
	U32 ulSLCFWDummyReadDoneBMP : 21;
	U16 uwTableUnitDummyReadDoneBMP;

	//mandatory wordline and starvation
	U8 ubMandatoryWordlineCnt : 4;
	U8 btMandatoryWordLineDone : 1;
	U8 ubSLCStarvationCnt : 2;	//Max cnt=3
	U8 btDoingSLCStarvation : 1;

	//SLC FW blk
	SystemAreaBlock_t uwCurrentSLCNonDataBlk[MEDIA_SCAN_SLC_EVENT_NUM]; //CH, CE, Blk order
	U16 uwTotalTableUnit[MAX_TABLE_UNIT_NUM];
	U8 ubTotalTableUnitIdx : 4; //0-15
	U8 ubTotalTableUnitCnt : 4;	//0-15
	U16 uwBootLoaderCodeSignHeaderPageIdx;//Record current FW slot BootLoader CodeSignHeader PageIdx
	U16 uwBootLoaderSignaturePageIdx;     //Record current FW slot BootLoader Signature PageIdx
	U16 uwFWCodeSignHeaderPageIdx;//Record current FW slot CodeSignHeader PageIdx
	U16 uwFWSignaturePageIdx;     //Record current FW slot Signature PageIdx
	U8 ubBankingSLCNonDataBlk : 5;

	//VHC
	U8 btUNC : 1;
	U8 btVHCFlag : 1;
	U8 btIsCBC7 : 1;
	U32 ulFirstCalibrationOffset;
	U32 ulSecondCalibrationOffset;
	U8 ubCombineCalibrationOffset;
	U8 ubQLCVHCState : 4;
	U8 ubSLCVHCState : 4;
	U16 uwFIPClock;

#if (PARTIAL_BLOCK_MEDIA_SCAN_EN && IM_N48R)
	U16 btNDEP : 1;
	U16 btErasePageChecked : 1;
	U16 btNDEPSendReadSQDone : 1;
	U16 uwNDEPReadCnt : 13;
	U16 uwNDEPUseTag;
	U16 uwNDEPSendSQBMP[MAX_CHANNEL][MAX_CE_PER_CHANNEL];
#endif /*(PARTIAL_BLOCK_MEDIA_SCAN_EN && IM_N48R)*/

	struct {
		U16 uwErrorCountPerCodeWord[3];	//left, right, center
		U16 uwTrimAddress;
		U8 ubReadLevel;
		U16 uwTrimCnt: 2;
		U16 uwDACCenterOffset : 10;
		U16 uwTest : 3;
		U16 uwDoingReadSamplingOffset : 1;
#if MEDIA_SCAN_DEBUG_CHECK_READ_OFFSET_PREFIX_EN
		U8 ubFeatureAddr;
#endif /* MEDIA_SCAN_DEBUG_CHECK_READ_OFFSET_PREFIX_EN */
	} ReadSamplingOffset;

	struct {
		U8 ubEOTRefreshBitNum;
		U8 ubEOTRecordBitNum;
		U8 ubForceRetryEn;
		U16 uwCurrentErrBitNum;
	} EOT;

	struct {
		U8 ubScanPercentCheck: 7;
		U8 ubScanBlkPercent: 7;
		U8 ubReserve : 2;
		U16 uwScanUnitCnt;
		U16 uwPreviousUnit;
		U8 ubPartialUnitBMP;
		U16 uwTableUnitBMP;
	} UNEL;
} ;
#if MEDIA_SCAN_DEBUG_CHECK_READ_LEVEL_BY_MT_EN
typedef struct {
	U8 ubReadOffset;			//byte 0
	U8 ubReserve[3];			//byte1-3
} GetTrimReadout4B_t;
#endif /* MEDIA_SCAN_DEBUG_CHECK_READ_LEVEL_BY_MT_EN */

typedef struct {
	U8 ubReserve0;				//byte 0
	U8 ubIndicator;				//byte 1
	U8 ubReserve1[32];			//byte2-33
	U8 ubCalibrationOffset[4];	//byte34-37
} ValleyTrackReadout38B_t;

typedef union {
	U8 ubAll;
	struct {
		U8 ubEvenUnit : 4;
		U8 ubOddUnit : 4;
	};
} Entry_t;

typedef struct {
	Entry_t Unit[817]; //unit_num
	Entry_t SystemBlk[(SYSTEM_AREA_SYSTEM_BLK_NUM) >> 1];
	Entry_t DBTblk[(SYSTEM_AREA_DBT_BLK_NUM) >> 1];
	Entry_t CodeBlk[(E17_ALLIGN_E13_MAX_CH * CODE_BLK_NUM_PER_CH) >> 1];
	Entry_t BankingBlk[(E17_ALLIGN_E13_MAX_CH * CODE_BLK_NUM_PER_CH) >> 1];
	U8 VTMother: 4;
	U8 InitFlag: 1;
} Unit_Temperature_t;

typedef enum {
	MEDIASCAN_N28_BEC_0_44 = 0,
	MEDIASCAN_N28_BEC_45_53,
	MEDIASCAN_N28_BEC_54_62,
	MEDIASCAN_N28_BEC_63_71,
	MEDIASCAN_N28_BEC_72_80,
	MEDIASCAN_N28_BEC_81_90,
	MEDIASCAN_N28_BEC_91_98,
	MEDIASCAN_N28_BEC_99_107,
	MEDIASCAN_N28_BEC_108_116,
	MEDIASCAN_N28_BEC_117_125,
	MEDIASCAN_N28_BEC_126_134,
	MEDIASCAN_N28_BEC_135_143,
	MEDIASCAN_N28_BEC_144_152,
	MEDIASCAN_N28_BEC_153_161,
	MEDIASCAN_N28_BEC_162_171,
	MEDIASCAN_N28_BEC_OVER_171,
	MEDIASCAN_N28_BEC_TYPE_CNT,
} MediaScanN28BECType_t;

typedef enum {
	MEDIASCAN_B47R_N48R_BEC_0_8 = 0,
	MEDIASCAN_B47R_N48R_BEC_9_17, 			// 1
	MEDIASCAN_B47R_N48R_BEC_18_26, 		// 2
	MEDIASCAN_B47R_N48R_BEC_27_35, 		// 3
	MEDIASCAN_B47R_N48R_BEC_36_44, 		// 4
	MEDIASCAN_B47R_N48R_BEC_45_53, 		// 5
	MEDIASCAN_B47R_N48R_BEC_54_62, 		// 6
	MEDIASCAN_B47R_N48R_BEC_63_71, 		// 7
	MEDIASCAN_B47R_N48R_BEC_72_80, 		// 8
	MEDIASCAN_B47R_N48R_BEC_81_89, 		// 9
	MEDIASCAN_B47R_N48R_BEC_90_98, 		// 10
	MEDIASCAN_B47R_N48R_BEC_99_107,		// 11
	MEDIASCAN_B47R_N48R_BEC_108_116,		// 12
	MEDIASCAN_B47R_N48R_BEC_117_125,		// 13
	MEDIASCAN_B47R_N48R_BEC_126_134,		// 14
	MEDIASCAN_B47R_N48R_BEC_OVER_135,	// 15
	MEDIASCAN_B47R_N48R_BEC_TYPE_CNT,
} MediaScanBECType_t;

#define MEDIA_SCAN_BEC_RECORD_MAX_IDX	(MEDIASCAN_B47R_N48R_BEC_TYPE_CNT)

#if (IM_N48R)
#define MEDIASCAN_NEED_COP0_SQ_NUM			(9)
#define MEDIASCAN_NDEP_ONCE_CHECK_PAGE_NUM  (16)
#define MEDIASCAN_NDEP_OFFSET				(0xF6) //-10
#else /*(IM_N48R)*/
#define MEDIASCAN_SLC_GAP			(35)
#define MEDIASCAN_TLC_GAP			(211)

#define MEDIASCAN_ERASEPAGE_CHECK_NUM		(6)
#define MEDIASCAN_ERASEPAGE_CHECK_ARRAY		{4,340,348,356,512,696}
#define MEDIASCAN_NEED_COP0_SQ_NUM			(5)
#endif /*(IM_N48R)*/

/*
 * ---------------------------------------------------------------------------------------------------
 *  macros
 * ---------------------------------------------------------------------------------------------------
 */
#define MEDIA_SCAN_GET_START_PLANE_IDX(SLC, SUPER_PAGE, PLANEBAMK) ((SLC) ? ((SUPER_PAGE) * gubPlanesPerSuperPage + PLANEBAMK) : (FTLPhysical2PlaneInx((SUPER_PAGE), PLANEBAMK)))

#if (IM_N48R)
#if VRLC_EN
#define MEDIA_SCAN_CLEAR_REQUEST() do { \
										if(MEDIA_SCAN_PARTIAL_REQUEST == gMediaScanManager.Request.ubAll) {\
											gMediaScanManager.btStartNewScanGroup = TRUE; }\
										gMediaScanManager.Request.ubAll = 0;\
										gMediaScanManager.uoStartTime[MEDIA_SCAN_TYPE_TOTAL] = M_GET_FW_TIMER();\
										gMediaScanManager.uoStartTime[MEDIA_SCAN_TYPE_VRLC] = gMediaScanManager.uoStartTime[MEDIA_SCAN_TYPE_TOTAL];\
									} while(0)
#else /* VRLC_EN */
#define MEDIA_SCAN_CLEAR_REQUEST() do { \
										if(MEDIA_SCAN_PARTIAL_REQUEST == gMediaScanManager.Request.ubAll) {\
											gMediaScanManager.btStartNewScanGroup = TRUE; }\
										gMediaScanManager.Request.ubAll = 0;\
										gMediaScanManager.uoStartTime[MEDIA_SCAN_TYPE_TOTAL] = M_GET_FW_TIMER();\
									} while(0)
#endif /* VRLC_EN */
#else /* (IM_N48R) */
#if VRLC_EN
#define MEDIA_SCAN_CLEAR_REQUEST() do { \
									  gMediaScanManager.Request.ubAll = 0;\
									  gMediaScanManager.uoStartTime[MEDIA_SCAN_TYPE_NORMAL] = M_GET_FW_TIMER();\
									  gMediaScanManager.uoStartTime[MEDIA_SCAN_TYPE_PARTIAL] = gMediaScanManager.uoStartTime[MEDIA_SCAN_TYPE_NORMAL];\
									  gMediaScanManager.uoStartTime[MEDIA_SCAN_TYPE_VRLC] = gMediaScanManager.uoStartTime[MEDIA_SCAN_TYPE_PARTIAL];\
								  } while(0)
#else /* VRLC_EN */
#define MEDIA_SCAN_CLEAR_REQUEST() do { \
									  gMediaScanManager.Request.ubAll = 0;\
									  gMediaScanManager.uoStartTime[MEDIA_SCAN_TYPE_NORMAL] = M_GET_FW_TIMER();\
									  gMediaScanManager.uoStartTime[MEDIA_SCAN_TYPE_PARTIAL] = gMediaScanManager.uoStartTime[MEDIA_SCAN_TYPE_NORMAL];\
								  } while(0)
#endif /* VRLC_EN */
#endif /* (IM_N48R) */

#define M_FPU_DELAY_COUNT(NanoSecond, DataRate)	(((NanoSecond) * (DataRate)) / 1000)	//FPU delay cycle in ns = 1000/DataRate

/*
 * ---------------------------------------------------------------------------------------------------
 *   extern global variables
 * ---------------------------------------------------------------------------------------------------
 */
extern MEDIA_SCAN gMediaScanManager;
extern const U8 gubMediaScanAGCOffsetArray[3];
extern const U8 gubQLCValleyHealthCheckBitErrorCountThresholdPerCodeWord[MAX_WORDLINE_GROUP_NUM];
extern const U16 guwCenterErrorCountThresholdPerCodeWord[MAX_WORDLINE_GROUP_NUM][2];
extern const U16 guwDiffErrorCountThresholdPerCodeWord[MAX_WORDLINE_GROUP_NUM][2];
extern Unit_Temperature_t *gUnitTemperature;
/*
 * ---------------------------------------------------------------------------------------------------
 *   definitions
 * ---------------------------------------------------------------------------------------------------
 */
#if (IM_N28)
#define MEDIA_SCAN_RECORD_MAX_ERROR_BIT	(171)
#define MEDIA_SCAN_RECORD_OFFSET	(45)
#define MEDIA_SCAN_RECORD_EACH_LEVEL_ERROR_BIT	(9)
#else /* (IM_N28) */
#define MEDIA_SCAN_RECORD_MAX_ERROR_BIT	(135)
#define MEDIA_SCAN_RECORD_OFFSET	(74)
#define MEDIA_SCAN_RECORD_EACH_LEVEL_ERROR_BIT	(9)
#endif /* (IM_N28) */
#if IM_N48R
#define MEDIA_SCAN_START_TIME_INTERVAL (gMediaScanManager.uwFrequencySecound)	//ACTIVE_AWAKE_SCAN_FREQ (100ms)
#else /*  IM_N48R */
#define MEDIA_SCAN_START_TIME_INTERVAL (gMediaScanManager.uwFrequencySecound * 1000)
#endif /*  IM_N48R */
#define MEDIA_SCAN_PARTIAL_START_TIME_INTERVAL  (gMediaScanManager.ubPartialFrequencyHour * 60 * 60 * 1000)
#define MEDIA_SCAN_ERROR_BIT_LEVEL  (8)
#define MEDIA_SCAN_UNIT_TRY_CNT		(0x40)
#define MEDIA_SCAN_NORMAL_REQUEST   (BIT0)
#define MEDIA_SCAN_PARTIAL_REQUEST  (BIT1)
#define MEDIA_SCAN_NORMAL_DOING   (BIT0)
#define MEDIA_SCAN_PARTIAL_DOING  (BIT1)

#define MEDIA_SCAN_RECORD_AND_REFRESH   (BIT0)
#define MEDIA_SCAN_REFRESH_CHECK        (BIT1)

#define MEDIA_SCAN_POWER_ON_INTERVAL    (10*1000) //unit: 10s

#define EOT_REFRESH_LEVEL_BIT_NUM	(gMediaScanManager.EOT.ubEOTRefreshBitNum)
#define EOT_REFRESH_LEVEL_BIT_CNT	(9)
#define EOT_REFRESH_LEVEL_MASK	(0xF)

#define MEDIA_SCAN_SELECT_UNIT_SEARCH       (0)
#define MEDIA_SCAN_SELECT_UNIT_FIND_VICTIM  (1)
#define MEDIA_SCAN_SELECT_UNIT_NOT_START_PROGRAM (2)
#define MEDIA_SCAN_SELECT_UNIT_NOT_MATCH_VT (3)
#define MEDIA_SCAN_SELECT_UNIT_GIVE_UP      (4)
#define MEDIA_SCAN_SELECT_UNIT_NOT_SLC (5)
#define MEDIA_SCAN_SELECT_UNIT_NOT_VALID (6)
#define MEDIA_SCAN_SELECT_PAGE_NOT_VALID (7)


#define MEDIA_SCAN_GET_ERROR_BIT_NUM	(0)
#define MEDIA_SCAN_UPDATE_ERROR_BIT_NUM	(1)

#if VRLC_EN
#define MEDIA_SCAN_VRLC_START_TIME_INTERVAL             (1 * 1000) // 1 SEC
#define MEDIA_SCAN_VRLC_CHECK_STARTUP_TIME_INTERVAL     (30 * 1000) // 30 Sec check
#define MEDIA_SCAN_VRLC_REQUEST                         (BIT2)
#define MEDIA_SCAN_VRLC_DOING                           (BIT2)
#endif /* VRLC_EN */


#define MEDIA_SCAN_SCAN_ITERATION	(8)
#if (IM_N48R)
#define MEDIASCAN_DUMMY_READ_IDLE_TIME	(10) //10ms
#define MEDIA_SCAN_STATIC_CASE_NUM				(3)
#else /* (IM_N48R) */
#define MEDIASCAN_DUMMY_READ_IDLE_TIME	(5) //5ms
#define MEDIA_SCAN_STATIC_CASE_NUM				(1)
#endif /* (IM_N48R) */

#define MEDIA_SCAN_FULL_SCAN_GROUP_INSTANCE_FREQUENCY	(1728000 * 1000)	//20 days = 480 hrs = 1728000 s (full scan group)
#define MEDIA_SCAN_SCAN_INSTANCE_FREQUENCY 				(216000 * 1000)		//2.5 days = 60 hrs = 216000 s (scan group)
#define MEDIA_SCAN_SLC_STARVATION_TRIGGER 		(3) 	//count (no scanning SLC blk)
#define MEDIA_SCAN_SCANGEAR 					(20)	//ratio (host CMD:scan) //N48:not support MediaScanDynamicScanTime();

#define MEDIA_SCAN_MANDATORY_WORDLINE_NUM		(8)
#define MEDIA_SCAN_GROUP_NUM					(8)
#define MEDIA_SCAN_MAX_PAGE_STACK_NUM			(4)
#define MEDIA_SCAN_READ_VERIFY_MAX_PAGE			(10)
#define PARITY_UNIT_MAX_ACTIVE_PAGE_NUM			(128)
#define SYSTEM_HEADER_SIZE_IN_PLANE				(1)
#define SYSTEM_ONE_VERSION_SIZE					(6)
#define BANKING_BLK_HEADER_SIZE_IN_PLANE		(1)

#define VALLEY_HEALTH_CHECK_STALL_CHANNEL		(0)
#define VALLEY_HEALTH_CHECK_UNSTALL_CHANNEL		(1)

#define VALLEY_TRACK_FEATURE_ADDRESS			(0x96)
#define MLC_READ_OFFSET_LEVEL_1_FEATURE_ADDRESS		(0xA0)
#define MLC_READ_OFFSET_LEVEL_3_FEATURE_ADDRESS		(0xA2)
#define SLC_READ_OFFSET_FEATURE_ADDRESS			(0xA4)
#define CLEAR_READ_PREFIX_ADDRESS				(0x86)

#define MEDIA_SCAN_READ_BIT_ERROR_COUNT_UNC_LIMIT			(800)
#define MEDIA_SCAN_EOT_VALLEY_HEALTH_CHECK_FOR_SLC_PER_CODEWORD			(45)
#define MEDIA_SCAN_EOT_VALLEY_HEALTH_CHECK_FOR_MLC_PER_CODEWORD			(45)
#define MEDIA_SCAN_CENTER_ERROR_COUNT_THRESHOLD_FOR_SLC_PER_CODEWORD	(132 >> 3)
#define MEDIA_SCAN_CENTER_ERROR_COUNT_THRESHOLD_FOR_MLC_PER_CODEWORD	(469 >> 3)
#define MEDIA_SCAN_DIFF_ERROR_COUNT_THRESHOLD_FOR_SLC_PER_CODEWORD		(263 >> 3)
#define MEDIA_SCAN_DIFF_ERROR_COUNT_THRESHOLD_FOR_MLC_PER_CODEWORD		(557 >> 3)
#define MEDIA_SCAN_VALLEY_OFFSET_THRESHOLD_FOR_SLC_AND_MLC_AND_QLC_LP	(15)	//MLC: WLG 0, 3, 4, 7
#define MEDIA_SCAN_VALLEY_OFFSET_THRESHOLD_FOR_QLC_TOP_PAGE				(30)
#define MEDIA_SCAN_TEMPERATURE_DELTA_THRESHOLD_FOR_SLC_AND_MLC	(70)	//MLC: only for WLG 0, 7
#define MEDIA_SCAN_TEMPERATURE_DELTA_THRESHOLD_FOR_MLC_AND_QLC	(30)

#define MEDIA_SCAN_UNIFIED_LOG_SCAN_GROUP_START_AND_PERCENTAGE (0)
#define MEDIA_SCAN_UNIFIED_LOG_NAND_DETECT_EMPTY_PAGE (1)
#define MEDIA_SCAN_UNIFIED_LOG_SCAN_GROUP_END (2)


/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */
AOM_SYSTEM_AREA_INIT void MediaScanInitFromInfoBlk(void);
AOM_INIT void MediaScanInitFromVT();
void MediaScanCheckErasePage_Callback(TIEOUT_FORMAT_t uoResult);
U8 MediaScanCheckRequest(void);
void MediaScanHandleErrorBitNum(U8 ubMode, U8 ubGlobalDie, U8 ubLevelIdx);
U8 MediaScanCheckErrorBitNum(U16 uwErrorBitNum, U8 ubGlobalDie);
AOM_READ_VERIFY_HANDLE_MEDIA_SCAN U32 MediaScanAddDieOffsetPlaneIdx(U32 ulPlaneIdx, U32 SentinelPlaneIdx);
AOM_READ_VERIFY_HANDLE_MEDIA_SCAN void MediaScanPollingDieECC(U16 uwTagId);
AOM_READ_VERIFY_HANDLE_MEDIA_SCAN U32 MediaScanGetLocalPCA(ReadVerifyInfo_t *pReadVerifyInfo);
AOM_READ_VERIFY_HANDLE_MEDIA_SCAN U8 MediaScanCheckDummyReadPage(void);
#if ((MEDIA_SCAN_EN) && (!BURNER_MODE_EN) && (!RDT_MODE_EN) && (IM_N48R))
AOM_MEDIA_SCAN_VALLEY_HEALTH_CHECK U16 MediaScanPreconditionSetMLBiFPU(U16 uwTrimAddr, U8 ubDie);	//VHC need to be in same code bank
AOM_MEDIA_SCAN_VALLEY_HEALTH_CHECK U16 MediaScanPreconditionGetMLBiFPU(U8 ubQLC, U16 uwTrimAddr, U8 ubDie);	//VHC need to be in same code bank
//AOM_MEDIA_SCAN_VALLEY_HEALTH_CHECK U16 MediaScanPreconditionCheckFeatureFPU(void);	//VHC need to be in same code bank
AOM_MEDIA_SCAN_VALLEY_HEALTH_CHECK void MediaScanStallOrUnstallChannel(RetryJob_t *pRetryJob, U8 ubMode);	//VHC need to be in same code bank
AOM_MEDIA_SCAN_VALLEY_HEALTH_CHECK void MediaScanReadDataPushCmdMT(U8 ubQLC, U8 ubMode, U16 uwTrimAddr);	//VHC need to be in same code bank
AOM_MEDIA_SCAN_VALLEY_HEALTH_CHECK void MediaScanReadDataPushDMAMT(U8 ubQLC, U8 ubMode, U16 uwTrimAddr);	//VHC need to be in same code bank
AOM_MEDIA_SCAN_VALLEY_HEALTH_CHECK void MediaScanReadSamplingOffset(U8 ubQLC, RetryJob_t *pCurrentRetryJob);	//VHC need to be in same code bank
AOM_MEDIA_SCAN_VALLEY_HEALTH_CHECK U8 MediaScanValleyHealthCheckMain(RetryJob_t *pRetryJob);	//VHC need to be in same code bank
AOM_MEDIA_SCAN_VALLEY_HEALTH_CHECK void MediaScanValleyHealthCheckForQLCWordLine(RetryJob_t *pRetryJob);	//VHC need to be in same code bank
AOM_MEDIA_SCAN_VALLEY_HEALTH_CHECK void MediaScanValleyHealthCheckForSLCAndMLCWordLine(RetryJob_t *pRetryJob);	//VHC need to be in same code bank
#endif /* ((MEDIA_SCAN_EN) && (!BURNER_MODE_EN) && (!RDT_MODE_EN) && (IM_N48R)) */
#if RECORD_FLASH_TEMPERATURE_EN
AOM_MEDIA_SCAN_TEMPERATURE void MediaScanRecordUnitTemperature(Unit_t uwUnit);
AOM_MEDIA_SCAN_TEMPERATURE void MediaScanRecordPhysicalBlkTemperature(U8 ubSystemAreaType, U8 ubChannel, U8 ubBlkIndex);
AOM_MEDIA_SCAN_TEMPERATURE U8 MediaScanGetUnitTemperature(U16 uwUnit);
AOM_MEDIA_SCAN_TEMPERATURE U8 MediaScanGetPhysicalBlkTemperature(U8 ubSystemAreaType, SystemAreaBlock_t ubSystemAreaBlock);
AOM_MEDIA_SCAN_TEMPERATURE U8 MediaScanLevelToTemperature(U8 ubLevel);
AOM_MEDIA_SCAN_TEMPERATURE U8 MediaScanTemperatureToLevel(TTTemperatureCelsius_t TTFlashTemperature);
AOM_MEDIA_SCAN_TEMPERATURE void MediaScanGetFlashTemperature();
#endif /* RECORD_FLASH_TEMPERATURE_EN */
AOM_CHECK_MEDIA_SCAN_REQUIREMENT void MediaScanCheckMandatoryWordLineFinishOrNot(U8 ubSLC);
AOM_CHECK_MEDIA_SCAN_REQUIREMENT U8 MediaScanCheckActiveUnit(U8 ubEventIdx);
AOM_CHECK_MEDIA_SCAN_REQUIREMENT U8 MediaScanCheckActivePage(U8 ubEventIdx, U8 ubCurrentSLCNonDataEventIdx);
AOM_CHECK_MEDIA_SCAN_REQUIREMENT void MediaScanSetupNextScanGroup(U8 ubIsStarvation);
AOM_MEDIA_SCAN void MediaScanReInit(U8 ubIsNormalEvent);
AOM_MEDIA_SCAN void MediaScanResetTime(void);
AOM_MEDIA_SCAN U8 MediaScanSelectVictimUnit(void);
AOM_MEDIA_SCAN U8 MediaScanSelectVictimPlane(void);
AOM_MEDIA_SCAN void MediaScanAddDriveLog(U8 ubState, U8 ubFindVictimUnit);
#if ((UNIFIED_LOG_EN || MEDIA_SCAN_DEBUG_UNIFIED_LOG_UART_EN) && IM_N48R)
AOM_MEDIA_SCAN_UNIFIED_LOG void MediaScanUnifiedLogEvent(U8 ubMode);
#endif /* ((UNIFIED_LOG_EN || MEDIA_SCAN_DEBUG_UNIFIED_LOG_UART_EN) && IM_N48R) */
AOM_MEDIA_SCAN void MediaScan(void);
#endif /* _MEDIA_SCAN_API_H_ */
