#ifndef _TCG_STRUCT_H_
#define _TCG_STRUCT_H_
#include "tcg_state.h"
#include "tcg_def.h"
#include "typedef.h"
#include "fw_vardef.h"
#include "trim/ftl_trim_api.h"
#include "tcg_drbg.h"
#include "hal/security/security_api.h"

typedef struct TcgVariablesManager_t   TcgVariablesManager_t, *TcgVariablesManagerPtr_t;
struct TcgVariablesManager_t {
	U32 ulLBId;
	U32 ulLBOffset;
	U32 ulLogicalBaseAddr;
	U32 ulCurrentPointer;
	U32 ulTotalSize;
};

typedef struct TcgBufManager_t   TcgBufManager_t, *TcgBufManagerPtr_t;
struct TcgBufManager_t {
	OpalTableState_t OpalTableNextState;
	OpalTableState_t OpalTableState;
	OpalTableState_t OpalTableRepairNextState;
	TcgRepairTableState_t RepairTableState;
	U32 ulBufFlashIdx;
	U32 ulBufAddr;
	U32 ulCmdTargetAddr;
	U32 ulCmdTargetFlashIdx;
	U32 ulCmdEvent;
	U32 ulSecurityAddr;
	U32 ulRepairSrcFlashIdx;
	U32 ulRepairTargetFlashIdx;
	union {
		U8 ubAll;
		struct {
			U8 btAESDirection            : 1;
			U8 btIntegrityCheckDirection : 1;
			U8 btRepairTableDoing        : 1;
			U8                           : 5;
		};
	} ubFlags;
};

typedef struct TcgCellBlockInfo_t  TcgCellBlockInfo_t, *TcgCellBlockInfoPtr_t;
struct TcgCellBlockInfo_t {
	U64	uoStartRow;
	U64	uoEndRow;
	U32	ulStartColumn;
	U32	ulEndColumn;
	U32	ulMaxColumnNum;
	U32	ulMaxRowNum;
	U8	ubByteTable;
	U8	ubReserved[3];
};
SIZE_CHECK_ALIGN_4(TcgCellBlockInfo_t);

typedef struct GetParameter_t GetParameter_t, *GetParameterPtr_t;
struct GetParameter_t {
	TcgCellBlockInfo_t CellBlock;
	U8	ubGetStartRow;
	U8	ubGetEndRow;
	U8	ubGetStartColumn;
	U8	ubGetEndColumn;
};
SIZE_CHECK_ALIGN_4(GetParameter_t);

typedef struct TcgName_t  TcgName_t, *TcgNamePtr_t;
struct TcgName_t {
	U8 ubName[TCG_MAX_NAME_SIZE];
	U8 ubLength;
	U8 ubReserved[3];
};
SIZE_CHECK_ALIGN_4(TcgName_t);

typedef struct TcgTokenData_t  TcgTokenData_t, *TcgTokenDataPtr_t;
struct TcgTokenData_t {
	U8 *pubToken;
	U16 uwDataLength;

	U8 btSign              : 1;
	U8 btDataType          : 1;
	U8 TokenType           : 2;
	U8 SimpleTokenType     : 2;
	U8 btSimpleTokenHeader : 1; // [To be modified] remove this parameter and integrate the parameter in simple token type
	U8 btReserved          : 1;

	U8 ubReserved;
};
SIZE_CHECK_ALIGN_4(TcgTokenData_t);

typedef struct Token_t  Token_t, *TokenPtr_t;
struct Token_t {
	TcgTokenData_t TokenData[TCG_MAX_TOKEN_LENGTH];
	U32 ulTokenLength;
	U8 ubSendFirstToken;
	U8 ubReserved[3];
};
SIZE_CHECK_ALIGN_4(Token_t);

typedef struct HeaderDecode_t  HeaderDecode_t, *HeaderDecodePtr_t;
struct HeaderDecode_t {
	U32 ulCompacketReserved;
	U16 uwCompacketComId;
	U16 uwCompacketExtensionComId;
	U32 ulCompacketOutstandingData;
	U32 ulCompacketMinimunTransfer;
	U32 ulCompacketLength ;

	U32 ulPacketDeviceSessionId ;
	U32 ulPacketHostSessionId ;
	U32 ulPacketSeqNumber ;
	U16 uwPacketReserved ;
	U16 uwPacketAckType ;
	U32 ulPacketAcknowledgement ;
	U32 ulPacketLength ;

	U32 ulSubpacketReserved1 ;
	U16 uwSubpacketReserved2 ;
	U16 uwSubpacketKind ;
	U32 ulSubpacketLength ;
};


typedef struct SpInfoTableElement_t     SpInfoTableElement_t, *SpInfoTableElementPtr_t;
struct SpInfoTableElement_t {
	U64	uoSpSessionTimeout;
	TcgSpInfoUidL_t	ulUidL;
	TcgSpUidL_t	SpUidL;
	U32	ulTcgLBASize;
	U32	ulAdminSpByteSize;
	U8	ubEnable;
};
SIZE_CHECK_ALIGN_4(SpInfoTableElement_t);

typedef struct SpTemplateTableElement_t     SpTemplateTableElement_t, *SpTemplateTableElementPtr_t;
struct SpTemplateTableElement_t {
	TcgSpTemplateUidL_t	ulUidL;
	TcgTemplateUidL_t	ulTemplateUidL;
};
SIZE_CHECK_ALIGN_4(SpTemplateTableElement_t);

typedef struct TableTableElement_t   TableTableElement_t, *TableTableElementPtr_t;
struct TableTableElement_t {
	TcgTableUidL_t	ulUidL;
	TcgTableKind_t	TableKind;
	U32	ulColumnNum;
	U32	ulRowNum;
	U32	ulMinRowNum;
	U32	ulMaxRowNum;
	U32	ulMadatoryWriteGranularity;
	U32	ulRecommandReadGranularity;
};
SIZE_CHECK_ALIGN_4(TableTableElement_t);

typedef struct MehtodTableElement_t   MehtodTableElement_t, *MehtodTableElementPtr_t;
struct MehtodTableElement_t {
	TcgMethodUidL_t	ulUidL;
};
SIZE_CHECK_ALIGN_4(MehtodTableElement_t);

typedef struct TcgACElementContent_t TcgACElementContent_t, *TcgACElementContentPtr_t;
struct TcgACElementContent_t {
	U32 AuthorityUidL : 28;
	U32 BooleanAce    : 4;
};
SIZE_CHECK_ALIGN_4(TcgACElementContent_t);

typedef struct ACElement_t   ACElement_t, *ACElementPtr_t;
struct ACElement_t {
	TcgACElementContent_t ACElementContent[TCG_MAX_AUTORITY_IN_ACE_ELEMENT];
	U32 ulReserved[3];	// @JIRA E13-564, Reserve 12 Bytes to keep ACElement_t has the same Size(Security Version 0 -> Secruity Version1)
	U8	ubAuthorityCnt;
	U8	ubBooleanAceCnt;
	U8	ubReserved[2];
};
SIZE_CHECK_ALIGN_4(ACElement_t);

typedef struct AceTableElement_t   AceTableElement_t, *AceTableElementPtr_t;
struct AceTableElement_t {
	TcgAceUidL_t ulUidL;
	ACElement_t	BooleanExpression;
	U8	ubAvailableColumn[TCG_MAX_AVAILABLE_COLUMN_NUM_IN_ACE_TABLE_ELEMENT];
	U8	ubAvailableColumnCnt;
};
SIZE_CHECK_ALIGN_4(AceTableElement_t);

typedef struct AuthorityTableElement_t   AuthorityTableElement_t, *AuthorityTableElementPtr_t;
struct AuthorityTableElement_t {
	TcgAuthorityUidL_t	ulUidL;
	TcgName_t	CommonName;
	TcgAuthorityUidL_t	ulAuthorityClassUidL;
	TcgAuthorityOperation_t  AuthorityOperation;
	TcgCPinUidL_t	ulCredentialCPinUidL;
	U8	ubClass;
	U8	ubEnable;
	U8	ubAuthenticate;
	U8 ubReserved;
};
SIZE_CHECK_ALIGN_4(AuthorityTableElement_t);

typedef struct TcgPin_t  TcgPin_t, *TcgPinPtr_t;
struct TcgPin_t {
	U8 aubPin[TCG_PIN_MAX_LENGTH];
	U8 ubLength;
	U8 ubReserved[3];
};
SIZE_CHECK_ALIGN_4(TcgPin_t);

typedef struct CPinTableElement_t  CPinTableElement_t, *CPinTableElementPtr_t;
struct CPinTableElement_t {
	TcgCPinUidL_t	ulUidL;
	U8 ubPinLength;
	U8 ubTryLimit;
	U8 ubTries;
	U8 ubPersistence;
};
SIZE_CHECK_ALIGN_4(CPinTableElement_t);

typedef struct SecretProtectTableElement_t  SecretProtectTableElement_t, *SecretProtectTableElementPtr_t;
struct SecretProtectTableElement_t {
	TcgSecretProtectUidL_t ulUidL;
	U32	ulTableTableElementAddrOffsetFromTcgLockingPart2;
	U32	ulColumnNumber;
	U8	ubProtectMechanisms;
	U8	ubReserved[3];
};
SIZE_CHECK_ALIGN_4(SecretProtectTableElement_t);

typedef struct KeyAes256TableElement_t  KeyAes256TableElement_t, *KeyAes256TableElementPtr_t;
struct KeyAes256TableElement_t {
	TcgKeyAes256UidL_t	ulUidL;
	TcgSymmetricMediaEncrypt_t	SymmetricMediaEncryptMode;
};
SIZE_CHECK_ALIGN_4(KeyAes256TableElement_t);

typedef struct LockingTableElement_t  LockingTableElement_t, *LockingTableElementPtr_t;
struct LockingTableElement_t {
	U64	uoStartLBA;
	U64	uoLBACnt;
	RangeLengthSettingPolicy_t  RangeLengthSettingPolicy;
	TcgLockingUidL_t ulUidL;
	TcgName_t	CommonName;
	TcgReset_t	LockOnReset[TCG_MAX_RESET_TYPE_NUM];
	U32	ulKeyAes256TableElementAddrOffsetFromTcgLockingPart3;
	U32	ulAuthorityTableElementAddrOffsetFromTcgLockingPart3;
	U8	ubReadLockEnable;
	U8	ubWriteLockEnable;
	U8	ubReadLock;
	U8	ubWriteLock;
	U8	ubLockOnResetCnt;
	U8	ubSingleUserModeEnable;
	U8	ubReserved[2];
};
SIZE_CHECK_ALIGN_4(LockingTableElement_t);

typedef struct LockingInfoTableElement_t  LockingInfoTableElement_t, *LockingInfoTableElementPtr_t;
struct LockingInfoTableElement_t {
	U64	uoLowestAlignedLBA;
	U64	uoAlignmentGranularity;
	RangeLengthSettingPolicy_t	RangeLengthSettingPolicy;
	TcgLockingInfoUidL_t	ulUidL;
	TcgEncryptSupport_t	EncryptSupport;
	U32	ulSingleUserModeLockingTableElementAddrOffsetFromTcgLockingPart3[TCG_MAX_AES_KEY_NUM];
	U32	ulMaxLockingRangesNum;
	U8	ubAlignmentRequired;
	U8	ubSingleUserModeRangesCnt;
	U8	ubSingleUserModeEntireLockingTable;
	U8 ubReserved;
};
SIZE_CHECK_ALIGN_4(LockingInfoTableElement_t);

typedef struct MBRControlTableElement_t  MBRControlTableElement_t, *MBRControlTableElementPtr_t;
struct MBRControlTableElement_t {
	TcgReset_t	MBRDoneOnReset[TCG_MAX_RESET_TYPE_NUM];
	TcgMBRControlUidL_t	ulUidL;
	U8	ubEnable;
	U8	ubDone;
	U8	ubMBRDoneOnResetCnt;
	U8 ubReserved[1];
};
SIZE_CHECK_ALIGN_4(MBRControlTableElement_t);

typedef struct TperInfoTableElement_t   TperInfoTableElement_t, *TperInfoTableElementPtr_t;
struct TperInfoTableElement_t {
	TcgName_t	SSC;
	TcgTperInfoUidL_t	ulUidL;
	U32	ulProtocolVersion;
	U8	ubGudId[TCG_GUDID_SIZE];
	U8 ubReserved[3];
	U8	ubProgramaticResetEnable;
};
SIZE_CHECK_ALIGN_4(TperInfoTableElement_t);

typedef struct TemplateTableElement_t   TemplateTableElement_t, *TemplateTableElementPtr_t;
struct TemplateTableElement_t {
	TcgTemplateUidL_t ulUidL;
};
SIZE_CHECK_ALIGN_4(TemplateTableElement_t);

typedef struct SpTableElement_t   SpTableElement_t, *SpTableElementPtr_t;
struct SpTableElement_t {
	TcgSpUidL_t			ulUidL;
	TcgLifeCycle_t	ulLifeCycle;
};
SIZE_CHECK_ALIGN_4(SpTableElement_t);


typedef struct DataRemovalMechanismTableElement_t   DataRemovalMechanismTableElement_t, *DataRemovalMechanismTableElementPtr_t;
struct DataRemovalMechanismTableElement_t {
	TcgDataRemovalMechanismUidL_t ulUidL;
	TcgDataRemovalMechanism_t	ulActiveDataRemovalMechanism;
};
SIZE_CHECK_ALIGN_4(DataRemovalMechanismTableElement_t);

typedef struct TcgDek_t TcgDek_t, *TcgDekPtr_t;
struct TcgDek_t {
	U8 ubKey[TCG_DEK_SIZE];
};
SIZE_CHECK_ALIGN_4(TcgDek_t);

typedef struct TcgEncryptedDek_t TcgEncryptedDek_t, TcgEncryptedDekPtr_t;
struct TcgEncryptedDek_t {
	U8 ubKey[TCG_EDEK_SIZE];
};
SIZE_CHECK_ALIGN_4(TcgEncryptedDek_t);

typedef struct TcgKek_t TcgKek_t, *TcgKekPtr_t;
struct TcgKek_t {
	U8 ubKey[TCG_KEK_SIZE];
};
SIZE_CHECK_ALIGN_4(TcgKek_t);

typedef struct TcgEncryptedKek_t TcgEncryptedKek_t, TcgEncryptedKekPtr_t;
struct TcgEncryptedKek_t {
	U8 ubKey[TCG_EKEK_SIZE];
};
SIZE_CHECK_ALIGN_4(TcgEncryptedKek_t);

typedef struct TcgEpassword_t TcgEpassword_t, TcgEpasswordPtr_t;
struct TcgEpassword_t {
	U8 ubKey[TCG_EPASSWORD_SIZE];
};
SIZE_CHECK_ALIGN_4(TcgEpassword_t);

typedef struct HostCmd_t   HostCmd_t, *HostCmdPtr_t;
struct HostCmd_t {
	U32	ulLength;
	U8	*pubSendBufferAddr;//[To be modified] integrate pubSendBufferAddr and pubRecvBufferAddr to one U8*
	U8	*pubRecvBufferAddr;
	U8	*pubHostInterfaceBufferAddr;
	U16	uwComId;
	U8	ubSecp;
	U8	ubRecvBuf4KNum;
};
SIZE_CHECK_ALIGN_4(HostCmd_t);

typedef struct HostProperty_t   HostProperty_t, *HostPropertyPtr_t;
struct HostProperty_t {
	U32  ulMaxMethods;
	U32  ulMaxSubpackets;
	U32  ulMaxPacketSize;
	U32  ulMaxPackets;
	U32  ulMaxCompacketSize;
	U32  ulMaxResponseCompacketSize;
	U32  ulMaxIndexTokenSize;
	U8 ubGetParameter;
};
SIZE_CHECK_ALIGN_4(HostProperty_t);

typedef struct TperProperty_t   TperProperty_t, *TperPropertyPtr_t;
struct TperProperty_t {
	U32 ulMaxMethods;
	U32 ulMaxSubpackets;
	U32 ulMaxPacketSize;
	U32 ulMaxPackets;
	U32 ulMaxCompacketSize;
	U32 ulMaxResponseCompacketSize;
	U32 ulMaxSessions;
	U32 ulMaxIndexTokenSize;
	U32 ulMaxAuthentications;
	U32 ulMaxTransactionLimits;
	U32 ulDefSessionTimeout;
};
SIZE_CHECK_ALIGN_4(TperProperty_t);

typedef struct ComIdProperty_t   ComIdProperty_t, *ComIdPropertyPtr_t;
struct ComIdProperty_t {
	TcgComIdState_t ulComIdResponse;
	TcgComIdState_t ulComIdState;
	U32 ulComId;
	U32 ulRequestComId;
	U8 ubReserved[3];
	U8 ubComIdRequestCode;
};
SIZE_CHECK_ALIGN_4(ComIdProperty_t);

typedef struct SessionProperty_t   SessionProperty_t, *SessionPropertyPtr_t;
struct SessionProperty_t {
	U64	session_start_time;
	U64 uoCurrentSessionTimeout;
	TcgSpUidL_t	ulSpUidL;
	U32	ulHostSessionId;
	U32	ulDeviceSessionId;
	U8	ubWriteSession;
	U8	ubStartSession;
	U8	ubCloseSession;
	U8	ubCtrlSession;
	U8	ubStartTransaction;
	U8	ubEndTransaction;
	U8	ubEndTransactionStatus;
	U8 ubReserved;
};
SIZE_CHECK_ALIGN_4(SessionProperty_t);

typedef struct UidDecode_t   UidDecode_t, *UidDecodePtr_t;
struct UidDecode_t {
	U32 ulInvokingUidH;
	U32 ulInvokingUidL;
	U32 ulMethodUidH;
	U32 ulMethodUidL;
};
SIZE_CHECK_ALIGN_4(UidDecode_t);

typedef struct PropertyParameter_t   PropertyParameter_t, *PropertyParameterPtr_t;
struct PropertyParameter_t {
	U32  ulMaxCompacketSize;
	U32  ulMaxResponseCompacketSize;
	U32  ulMaxPacketSize;
	U32  ulMaxSubpackets;
	U32  ulMaxIndexTokenSize;
	U32  ulMaxPackets;
	U32  ulMaxMethods;

	U8 ubGetMaxCompacketSize;
	U8 ubMaxResponseCompacketSize;
	U8 ubMaxPacketSize;
	U8 ubMaxSubpackets;
	U8 ubMaxIndexTokenSize;
	U8 ubMaxPackets;
	U8 ubMaxMethods;
	U8 ubReserved;
};
SIZE_CHECK_ALIGN_4(PropertyParameter_t);

typedef struct StartSessionParameter_t   StartSessionParameter_t, *StartSessionParameterPtr_t;
struct StartSessionParameter_t {
	TcgTableUidL_t		ulSpUidH;
	TcgSpUidL_t			ulSpUidL;
	TcgTableUidL_t		ulAuthorityUidH;
	TcgAuthorityUidL_t	ulAuthorityUidL;
	U32					ulHostChallengeLength;
	U8                  ubHostChallenge[TCG_MAX_HOST_CHALLENGE_LENGTH];
	U8	ubReserved;
	U8	ubWriteSession;
	U8	ubGetHostChallenge;
	U8	ubGetAuthority;
};
SIZE_CHECK_ALIGN_4(StartSessionParameter_t);

typedef struct AclList_t   AclList_t, *AclListPtr_t;
struct AclList_t {
	TcgAceUidL_t	Acl[TCG_MAX_ACL_NUM_IN_ACL_LIST];
	U8 ubReserved[3];
	U8	ubLength;
};
SIZE_CHECK_ALIGN_4(AclList_t);

typedef struct GetAclParameter_t   GetAclParameter_t, *GetAclParameterPtr_t;
struct GetAclParameter_t {
	U32 ulInvokingUidH;
	U32 ulInvokingUidL;
	U32 ulMethodUidH;
	U32 ulMethodUidL;
};
SIZE_CHECK_ALIGN_4(GetAclParameter_t);

typedef struct SetObjectTableParameter_t SetObjectTableParameter_t, *SetObjectTableParameterPtr_t;
struct SetObjectTableParameter_t {
	//[To be modified] Use union to decrease the structure size
	U8						ubTypeValue[TCG_MAX_SET_PARAMETER_RESET_TYPE_NUM];
	ACElement_t				BooleanExpression;
	TcgTokenSign_t				TokenSign;
	TcgTokenDataType_t			TokenDataType;
	U32						ulValueLength;
	U32						ulColumn;
	U8						*pubValue;
	U16						uwTypeValueNum;
	U8						ubBooleanExpression;
	U8						ubReserved;
};
SIZE_CHECK_ALIGN_4(SetObjectTableParameter_t);

typedef struct SetParameter_t SetParameter_t, *SetParameterPtr_t;
struct SetParameter_t {
	SetObjectTableParameter_t SetObjectTableParameter[TCG_MAX_SET_PARAMETER_OBJECT_TABLE_PARAMETER_COLUMN_NUM];
	U64	uoOldLockingRangeStartLBA;
	U64	uoOldLockingRangeLBACnt;
	U32	ulByteDataLength;
	U32	ulWhereInByte;
	U32	ulByteTableMaxRowNum;
	U8	*pubByteData;
	U16	uwSetObjectTalbeParameterLength;
	U8	ubByteTable;
	U8	ubGetWhere;
	U8	ubGetValue;
	U8	ubSetLockingRangeStart;
	U8	ubSetLockingRangeLBACnt;
	U8	ubReserved;
};
SIZE_CHECK_ALIGN_4(SetParameter_t);

typedef struct NextParameter_t NextParameter_t, *NextParameterPtr_t;
struct NextParameter_t {
	TcgTableUidL_t WhereUidH;
	U32 WhereUidL;
	U32 ulStartTableIndex;
	U32 ulMaxRows;
	U8 ubCount;
	U8 ubGetCount;
	U8 ubGetWhere;
	U8 ubReserved;
};
SIZE_CHECK_ALIGN_4(NextParameter_t);

typedef struct AuthenticateParameter_t AuthenticateParameter_t, *AuthenticateParameterPtr_t;
struct AuthenticateParameter_t {
	TcgTableUidL_t ulAuthorityUidH;
	TcgAuthorityUidL_t ulAuthorityUidL;
	U8 *pubProof;
	U32 ulProofLength;
	U8 ubAuthenticateResult;
};
SIZE_CHECK_ALIGN_4(AuthenticateParameter_t);

typedef struct ActivateParameter_t ActivateParameter_t, *ActivateParameterPtr_t;
struct ActivateParameter_t {
	TcgLockingUidL_t ulSelectedLockingUidL[TCG_MAX_LOCKING_SP_LOCKING_TABLE_LENGTH];
	RangeLengthSettingPolicy_t RangeLengthSettingPolicy;
	U32 ulDataStoreSizeList[TCG_MAX_LOCKING_SP_DATASTORE_NUM];
	U8 ubSingleUserModeEntireLockingTable;
	U8 ubSelectedLockingUidLCount;
	U8 ubDataStoreSizeListCount;
	U8 ubReserved;
};
SIZE_CHECK_ALIGN_4(ActivateParameter_t);


typedef struct ReactivateParameter_t ReactivateParameter_t, *ReactivateParameterPtr_t;
struct ReactivateParameter_t {
	TcgPin_t Admin1Pin;
	TcgLockingUidL_t ulSelectedLockingUidL[TCG_MAX_LOCKING_SP_LOCKING_TABLE_LENGTH];
	U32 ulDataStoreSizeList[TCG_MAX_LOCKING_SP_DATASTORE_NUM];
	RangeLengthSettingPolicy_t RangeLengthSettingPolicy;
	U8 ubSingleUserModeEntireLockingTable;
	U8 ubChangeAdmin1Pin;
	U8 ubSelectedLockingUidLCount;
	U8 ubDataStoreSizeListCount;
};
SIZE_CHECK_ALIGN_4(ReactivateParameter_t);

typedef struct RandomParameter_t RandomParameter_t, *RandomParameterPtr_t;
struct RandomParameter_t {
	U32 ulRandomByteNum;
};
SIZE_CHECK_ALIGN_4(RandomParameter_t);

typedef struct RevertSpParameter_t RevertSpParameter_t, *RevertSpParameterPtr_t;
struct RevertSpParameter_t {
	U8 ubReserved[3];
	U8 ubKeepGlobalRangeKey;
};
SIZE_CHECK_ALIGN_4(RevertSpParameter_t);

typedef struct Flag_t Flag_t, *FlagPtr_t;
struct Flag_t {
	U32 ulDekChangedBmp;
	U8 ubUpdateDataStoreTable[TCG_MAX_LOCKING_SP_DATASTORE_NUM];  // Flag - if update DataStore table
	U8 ubUpdateMBRTable;        // Flag - if update MBR table
	U8 ubRangeChanged;     // Flag - if set locking table about range
	U8 ubUpdateByteTable;
	U8 ubReserved;
};
SIZE_CHECK_ALIGN_4(Flag_t);

typedef struct {
	//---------------------------------------------------//
	//This Sec Buffer must be arranged at the head of the struct, because Sec buffer must align 128 bytes
	U8								ubSecuritySrcBufAddr128[TCG_SECURITY_SRC_BUF_SIZE];
	U8								ubSecurityDstBufAddr128[TCG_SECURITY_DST_BUF_SIZE];
	//---------------------------------------------------//
	//---------------------------------------------------------------//
	//The Dek and KekRange below is not save in flash, only a buffer for operation of opal
	TcgKek_t						KekRange[TCG_MAX_AES_KEY_NUM];
	TcgDek_t						Dek[TCG_MAX_AES_KEY_NUM];
	TcgDek_t						D2HDek;
	//---------------------------------------------------------------//
	U64								uoUserDataSize;
	Flag_t							Flag;
	HostCmd_t						HostCmd;
	HostProperty_t					HostProperty;
	TperProperty_t					TperProperty;
	ComIdProperty_t					ComIdProperty;
	SessionProperty_t				SessionProperty;
	TcgStatusCode_t					StatusCode;
	Token_t							AllToken;
	HeaderDecodePtr_t				pHeaderDecode;
	UidDecode_t						UidDecode;
	AclList_t						AclList;
	union {
		PropertyParameter_t					PropertyParameter;
		StartSessionParameter_t				StartSessionParameter;
		GetAclParameter_t					GetAclParameter;
		GetParameter_t						GetParameter;
		SetParameter_t						SetParameter;
		NextParameter_t						NextParameter;
		AuthenticateParameter_t				AuthenticateParameter;
		ActivateParameter_t					ActivateParameter;
		ReactivateParameter_t				ReactivateParameter;
		RandomParameter_t					RandomParameter;
		RevertSpParameter_t					RevertSpParameter;
	} Method;
	TcgDRBGStruct_t	DRBG;
	U8 aubReserved1[TCG_VARIABLE_TABLE_RESERVED_SIZE];
	//---------------------------------------------------------------//
	//NonVolatile TcgVariableTable
	//The size of non-volatile tcg vt is not allowed to be larger than 4KB, and current is 256B
	struct {
		U8 ubPbkdf2Salt[TCG_PBKDF2_SALT_SIZE];
		U8 btSIDValueState					: 1; // A bit to record whether current SID's pin is not equal to MSID's pin
		U8 btSIDBlockedState				: 1; // A bit to record current SID is blocked or not, we don't want to reset the value when back from loader, so store in Nonvolitile region
		U8 btSIDHRST						: 1; // A bit to record TCG HRST is a clear event for Block SID or not, we don't want to reset the value when back from loader, so store in Nonvolitile region
		U8 btCryptoEraseWithoutKek			: 1; // A bit to record new Dek need to be encrypted , set TRUE when not in session or anybody login LockingSP
		U8 btRevertLockingSpSIDTempEkek 	: 1; // A bit to record after reverting LockingSp, need to handle key protection flow
		U8 btRevertLockingSpAdmin1TempEkek 	: 1; // A bit to record after reverting LockingSp, need to handle key protection flow
		U8 									: 2;
		U8 aubReserved2[TCG_4B_ALIGNMENT_3B_RESERVED];
		U32 ulLockOnResetBmp;
		U8 aubReserved3[TCG_VARIABLE_TABLE_NONVOLATILE_RESERVED_SIZE];
		U8 aubCopy[TCG_VARIABLE_TABLE_NONVOLATILE_VARIABLE_SIZE];
		/*----------------------No integrity check after this line----------------------*/
		U8 aubNoIntegrityCheckReserved[TCG_NO_INTEGRITY_CHECK_RESERVED_SIZE];
		U8 aubVT3Digest[SECURITY_SHA_256_OUTPUT_LENGTH];
		U8 aubReserved4[TCG_TABLE_TAIL_RESERVED_SIZE];
	} NonVolatile;
	//---------------------------------------------------------------//
} TCGVariableTable_t;
TYPE_SIZE_CHECK(TCGVariableTable_t, SIZE_12KB);
TYPE_OFFSET_CHECK(TCGVariableTable_t, NonVolatile, (SIZE_12KB - (sizeof(U8) * TCG_VARIABLE_TABLE_NONVOLATILE_SIZE)));
TYPE_OFFSET_CHECK(TCGVariableTable_t, NonVolatile.aubVT3Digest, (SIZE_12KB - (sizeof(U8) * TCG_TABLE_TAIL_RESERVED_SIZE) - (sizeof(U8) * SECURITY_SHA_256_OUTPUT_LENGTH)));

typedef struct TcgAdminPartial1_t	TcgAdminPartial1_t, *TcgAdminPartial1Ptr_t;
struct TcgAdminPartial1_t {
	TcgEncryptedDek_t	Edek[TCG_MAX_AES_KEY_NUM];
	TcgEncryptedDek_t	EdekRange[TCG_MAX_AES_KEY_NUM];
	TcgEncryptedKek_t	EkekRange[TCG_MAX_AES_KEY_NUM];
	U8 aubReserved1[TCG_ADMIN_PARTITAL_1_RESERVED_SIZE];
	/*----------------------No integrity check after this line----------------------*/
	U8 aubNoIntegrityCheckReserved[TCG_NO_INTEGRITY_CHECK_RESERVED_SIZE];
	U8 aubAdminPartial1Digest[SECURITY_SHA_256_OUTPUT_LENGTH];
	U8 aubReserved2[TCG_ADMIN_PARTITAL_1_SECURITY_VERSION_INFO_RESERVED_SIZE];
	U8 ubValidTable;
	U8 ubReserved3;
	U8 ubSecurityVersion;
};
TYPE_SIZE_CHECK(TcgAdminPartial1_t, SIZE_4KB);
TYPE_OFFSET_CHECK(TcgAdminPartial1_t, aubAdminPartial1Digest, (SIZE_4KB - (sizeof(U8) * TCG_TABLE_TAIL_RESERVED_SIZE) - (sizeof(U8) * SECURITY_SHA_256_OUTPUT_LENGTH)));
TYPE_OFFSET_CHECK(TcgAdminPartial1_t, ubSecurityVersion, (SIZE_4KB - sizeof(U8))); //Cannot move location
TYPE_OFFSET_CHECK(TcgAdminPartial1_t, ubValidTable, (SIZE_4KB - (sizeof(U8) + sizeof(U8) + sizeof(U8)))); //Cannot move location

typedef struct TcgAdminPartial2_t	TcgAdminPartial2_t, *TcgAdminPartial2Ptr_t;
struct TcgAdminPartial2_t {
	TableTableElement_t			TableTable[TCG_MAX_ADMIN_SP_TABLE_TABLE_LENGTH]; // TableTable Element should be the first element @JIRA_E13-5206
	SpInfoTableElement_t		SpInfoTable[TCG_ADMIN_SP_SP_INFO_TABLE_LENGTH];
	SpTemplateTableElement_t	SpTemplateTable[TCG_ADMIN_SP_SP_TEMPLATE_TABLE_LENGTH];
	MehtodTableElement_t		MethodTable[TCG_ADMIN_SP_METHOD_TABLE_LENGTH];
	AceTableElement_t			AceTable[TCG_MAX_ADMIN_SP_ACE_TABLE_LENGTH];
	AuthorityTableElement_t		AuthorityTable[TCG_MAX_ADMIN_SP_AUTHORITY_TABLE_LENGTH];
	CPinTableElement_t			CPinTable[TCG_MAX_ADMIN_SP_C_PIN_TABLE_LENGTH];
	TcgPin_t					MsidPin;
	TperInfoTableElement_t		TperInfoTable[TCG_LOCKING_SP_TPER_INFO_TABLE_LENGTH];
	TemplateTableElement_t		TemplateTable[TCG_ADMIN_SP_TEMPLATE_TABLE_LENGTH];
	SpTableElement_t			SpTable[TCG_ADMIN_SP_SP_TABLE_LENGTH];
	DataRemovalMechanismTableElement_t DataRemovalMechanismTable[TCG_ADMIN_SP_DATA_REMOVAL_MECHANISM_LENGTH];
	TcgEpassword_t				PsidEpasswordBackup; //for inherit
	U8 aubReserved1[TCG_ADMIN_PARTITAL_2_RESERVED_SIZE];
	/*----------------------No integrity check after this line----------------------*/
	U8 aubNoIntegrityCheckReserved[TCG_NO_INTEGRITY_CHECK_RESERVED_SIZE];
	U8 aubAdminPartial2Digest[SECURITY_SHA_256_OUTPUT_LENGTH];
	U8 aubReserved2[TCG_TABLE_TAIL_RESERVED_SIZE];
};
SIZE_CHECK_ALIGN_4(TcgAdminPartial2_t);
TYPE_SIZE_CHECK(TcgAdminPartial2_t, SIZE_4KB);
TYPE_OFFSET_CHECK(TcgAdminPartial2_t, aubAdminPartial2Digest, (SIZE_4KB - (sizeof(U8) * TCG_TABLE_TAIL_RESERVED_SIZE) - (sizeof(U8) * SECURITY_SHA_256_OUTPUT_LENGTH)));

typedef struct TcgAdminPartial3_t	TcgAdminPartial3_t, *TcgAdminPartial3Ptr_t;
struct TcgAdminPartial3_t {
	// Important: Order of authorities of Ekek and Epassword need to be the same
	TcgEncryptedKek_t		Ekek[TCG_MAX_EKEK_NUM];
	TcgEpassword_t			Epassword[TCG_MAX_WRAPPED_PASSWORD_NUM];
	U8 aubReserved1[TCG_ADMIN_PARTITAL_3_RESERVED_SIZE];
	/*----------------------No integrity check after this line----------------------*/
	U8 aubNoIntegrityCheckReserved[TCG_NO_INTEGRITY_CHECK_RESERVED_SIZE];
	U8 aubAdminPartial3Digest[SECURITY_SHA_256_OUTPUT_LENGTH];
	U8 aubReserved2[TCG_TABLE_TAIL_RESERVED_SIZE];
};
SIZE_CHECK_ALIGN_4(TcgAdminPartial3_t);
TYPE_SIZE_CHECK(TcgAdminPartial3_t, SIZE_4KB);
TYPE_OFFSET_CHECK(TcgAdminPartial3_t, aubAdminPartial3Digest, (SIZE_4KB - (sizeof(U8) * TCG_TABLE_TAIL_RESERVED_SIZE) - (sizeof(U8) * SECURITY_SHA_256_OUTPUT_LENGTH)));

typedef struct TcgLockingPart1_t	TcgLockingPart1_t, *TcgLockingPart1Ptr_t;
struct TcgLockingPart1_t {
	//Reserved for Multi-namespace
	U8 aubReserved1[TCG_LOCKING_PART_1_RESERVED_SIZE];
	/*----------------------No integrity check after this line----------------------*/
	U8 aubNoIntegrityCheckReserved[TCG_NO_INTEGRITY_CHECK_RESERVED_SIZE];
	U8 aubLockingPart1Digest[SECURITY_SHA_256_OUTPUT_LENGTH];
	U8 aubReserved2[TCG_TABLE_TAIL_RESERVED_SIZE];
};
SIZE_CHECK_ALIGN_4(TcgLockingPart1_t);
TYPE_SIZE_CHECK(TcgLockingPart1_t, SIZE_4KB);
TYPE_OFFSET_CHECK(TcgLockingPart1_t, aubLockingPart1Digest, (SIZE_4KB - (sizeof(U8) * TCG_TABLE_TAIL_RESERVED_SIZE) - (sizeof(U8) * SECURITY_SHA_256_OUTPUT_LENGTH)));

typedef struct TcgLockingPart2_t	TcgLockingPart2_t, *TcgLockingPart2Ptr_t;
struct TcgLockingPart2_t {
	TableTableElement_t			TableTable[TCG_MAX_LOCKING_SP_TABLE_TABLE_LENGTH];
	CPinTableElement_t			CPinTable[TCG_MAX_LOCKING_SP_C_PIN_TABLE_LENGTH];
	MehtodTableElement_t		MethodTable[TCG_MAX_LOCKING_SP_METHOD_TABLE_LENGTH];
	SecretProtectTableElement_t	SecretProtectTable[TCG_LOCKING_SP_SECRET_PROTECT_TABLE_LENGTH];
	MBRControlTableElement_t    MBRControlTable[TCG_LOCKING_SP_MBR_CONTROL_TABLE_LENGTH];
	U8 aubReserved1[TCG_LOCKING_PART_2_RESERVED_SIZE];
	/*----------------------No integrity check after this line----------------------*/
	U8 aubNoIntegrityCheckReserved[TCG_NO_INTEGRITY_CHECK_RESERVED_SIZE];
	U8 aubLockingPart2Digest[SECURITY_SHA_256_OUTPUT_LENGTH];
	U8 aubReserved2[TCG_TABLE_TAIL_RESERVED_SIZE];
};
SIZE_CHECK_ALIGN_4(TcgLockingPart2_t);
TYPE_SIZE_CHECK(TcgLockingPart2_t, SIZE_4KB);
TYPE_OFFSET_CHECK(TcgLockingPart2_t, aubLockingPart2Digest, (SIZE_4KB - (sizeof(U8) * TCG_TABLE_TAIL_RESERVED_SIZE) - (sizeof(U8) * SECURITY_SHA_256_OUTPUT_LENGTH)));

typedef struct TcgLockingPart3_t	TcgLockingPart3_t, *TcgLockingPart3Ptr_t;
struct TcgLockingPart3_t {
	AuthorityTableElement_t		AuthorityTable[TCG_MAX_LOCKING_SP_AUTHORITY_TABLE_LENGTH];
	LockingTableElement_t		LockingTable[TCG_MAX_LOCKING_SP_LOCKING_TABLE_LENGTH];
	KeyAes256TableElement_t		KeyAes256Table[TCG_MAX_LOCKING_SP_KEY_AES_256_TABLE_LENGTH];
	LockingInfoTableElement_t	LockingInfoTable[TCG_LOCKING_SP_LOCKING_INFO_TABLE_LENGTH];
	SpInfoTableElement_t		SpInfoTable[TCG_LOCKING_SP_SP_INFO_TABLE_LENGTH];
	SpTemplateTableElement_t	SpTemplateTable[TCG_LOCKING_SP_SP_TEMPLATE_TABLE_LENGTH];
	U8 aubReserved1[TCG_LOCKING_PART_3_RESERVED_SIZE];
	/*----------------------No integrity check after this line----------------------*/
	U8 aubNoIntegrityCheckReserved[TCG_NO_INTEGRITY_CHECK_RESERVED_SIZE];
	U8 aubLockingPart3Digest[SECURITY_SHA_256_OUTPUT_LENGTH];
	U8 aubReserved2[TCG_TABLE_TAIL_RESERVED_SIZE];
};
SIZE_CHECK_ALIGN_4(TcgLockingPart3_t);
TYPE_SIZE_CHECK(TcgLockingPart3_t, SIZE_4KB);
TYPE_OFFSET_CHECK(TcgLockingPart3_t, aubLockingPart3Digest, (SIZE_4KB - (sizeof(U8) * TCG_TABLE_TAIL_RESERVED_SIZE) - (sizeof(U8) * SECURITY_SHA_256_OUTPUT_LENGTH)));

typedef struct TcgLockingPart4_t	TcgLockingPart4_t, *TcgLockingPart4Ptr_t;
struct TcgLockingPart4_t {
	AceTableElement_t	AceTablePart1[TCG_ACE_TABLE_ELEMENT_PER_4K];
	/*----------------------No integrity check after this line----------------------*/
	U8 aubNoIntegrityCheckReserved[TCG_NO_INTEGRITY_CHECK_RESERVED_SIZE];
	U8 aubLockingPart4Digest[SECURITY_SHA_256_OUTPUT_LENGTH];
	U8 aubReserved2[TCG_TABLE_TAIL_RESERVED_SIZE];
};
SIZE_CHECK_ALIGN_4(TcgLockingPart4_t);
TYPE_SIZE_CHECK(TcgLockingPart4_t, SIZE_4KB);
TYPE_OFFSET_CHECK(TcgLockingPart4_t, aubLockingPart4Digest, (SIZE_4KB - (sizeof(U8) * TCG_TABLE_TAIL_RESERVED_SIZE) - (sizeof(U8) * SECURITY_SHA_256_OUTPUT_LENGTH)));

typedef struct TcgLockingPart5_t	TcgLockingPart5_t, *TcgLockingPart5Ptr_t;
struct TcgLockingPart5_t {
	AceTableElement_t	AceTablePart2[TCG_ACE_TABLE_ELEMENT_PER_4K];
	/*----------------------No integrity check after this line----------------------*/
	U8 aubNoIntegrityCheckReserved[TCG_NO_INTEGRITY_CHECK_RESERVED_SIZE];
	U8 aubLockingPart5Digest[SECURITY_SHA_256_OUTPUT_LENGTH];
	U8 aubReserved2[TCG_TABLE_TAIL_RESERVED_SIZE];
};
SIZE_CHECK_ALIGN_4(TcgLockingPart5_t);
TYPE_SIZE_CHECK(TcgLockingPart5_t, SIZE_4KB);
TYPE_OFFSET_CHECK(TcgLockingPart5_t, aubLockingPart5Digest, (SIZE_4KB - (sizeof(U8) * TCG_TABLE_TAIL_RESERVED_SIZE) - (sizeof(U8) * SECURITY_SHA_256_OUTPUT_LENGTH)));

typedef struct TcgLockingPart6_t	TcgLockingPart6_t, *TcgLockingPart6Ptr_t;
struct TcgLockingPart6_t {
	AceTableElement_t	AceTablePart3[TCG_ACE_TABLE_ELEMENT_PER_4K];
	/*----------------------No integrity check after this line----------------------*/
	U8 aubNoIntegrityCheckReserved[TCG_NO_INTEGRITY_CHECK_RESERVED_SIZE];
	U8 aubLockingPart6Digest[SECURITY_SHA_256_OUTPUT_LENGTH];
	U8 aubReserved2[TCG_TABLE_TAIL_RESERVED_SIZE];
};
SIZE_CHECK_ALIGN_4(TcgLockingPart6_t);
TYPE_SIZE_CHECK(TcgLockingPart6_t, SIZE_4KB);
TYPE_OFFSET_CHECK(TcgLockingPart6_t, aubLockingPart6Digest, (SIZE_4KB - (sizeof(U8) * TCG_TABLE_TAIL_RESERVED_SIZE) - (sizeof(U8) * SECURITY_SHA_256_OUTPUT_LENGTH)));

typedef struct TcgLockingPart7_t	TcgLockingPart7_t, *TcgLockingPart7Ptr_t;
struct TcgLockingPart7_t {
	AceTableElement_t	AceTablePart4[TCG_ACE_TABLE_ELEMENT_PER_4K];
	/*----------------------No integrity check after this line----------------------*/
	U8 aubNoIntegrityCheckReserved[TCG_NO_INTEGRITY_CHECK_RESERVED_SIZE];
	U8 aubLockingPart7Digest[SECURITY_SHA_256_OUTPUT_LENGTH];
	U8 aubReserved2[TCG_TABLE_TAIL_RESERVED_SIZE];
};
SIZE_CHECK_ALIGN_4(TcgLockingPart7_t);
TYPE_SIZE_CHECK(TcgLockingPart7_t, SIZE_4KB);
TYPE_OFFSET_CHECK(TcgLockingPart7_t, aubLockingPart7Digest, (SIZE_4KB - (sizeof(U8) * TCG_TABLE_TAIL_RESERVED_SIZE) - (sizeof(U8) * SECURITY_SHA_256_OUTPUT_LENGTH)));

typedef struct TcgLockingPart8_t	TcgLockingPart8_t, *TcgLockingPart8Ptr_t;
struct TcgLockingPart8_t {
	AceTableElement_t	AceTablePart5[TCG_ACE_TABLE_ELEMENT_PER_4K];
	/*----------------------No integrity check after this line----------------------*/
	U8 aubNoIntegrityCheckReserved[TCG_NO_INTEGRITY_CHECK_RESERVED_SIZE];
	U8 aubLockingPart8Digest[SECURITY_SHA_256_OUTPUT_LENGTH];
	U8 aubReserved2[TCG_TABLE_TAIL_RESERVED_SIZE];
};
SIZE_CHECK_ALIGN_4(TcgLockingPart8_t);
TYPE_SIZE_CHECK(TcgLockingPart8_t, SIZE_4KB);
TYPE_OFFSET_CHECK(TcgLockingPart8_t, aubLockingPart8Digest, (SIZE_4KB - (sizeof(U8) * TCG_TABLE_TAIL_RESERVED_SIZE) - (sizeof(U8) * SECURITY_SHA_256_OUTPUT_LENGTH)));

typedef struct TcgLockingPart9_t	TcgLockingPart9_t, *TcgLockingPart9Ptr_t;
struct TcgLockingPart9_t {
	AceTableElement_t	AceTablePart6[TCG_ACE_TABLE_ELEMENT_PER_4K];
	/*----------------------No integrity check after this line----------------------*/
	U8 aubNoIntegrityCheckReserved[TCG_NO_INTEGRITY_CHECK_RESERVED_SIZE];
	U8 aubLockingPart9Digest[SECURITY_SHA_256_OUTPUT_LENGTH];
	U8 aubReserved2[TCG_TABLE_TAIL_RESERVED_SIZE];
};
SIZE_CHECK_ALIGN_4(TcgLockingPart9_t);
TYPE_SIZE_CHECK(TcgLockingPart9_t, SIZE_4KB);
TYPE_OFFSET_CHECK(TcgLockingPart9_t, aubLockingPart9Digest, (SIZE_4KB - (sizeof(U8) * TCG_TABLE_TAIL_RESERVED_SIZE) - (sizeof(U8) * SECURITY_SHA_256_OUTPUT_LENGTH)));

typedef struct RestoreSpVariables_t   RestoreSpVariables_t, *RestoreSpVariablesPtr_t;
struct RestoreSpVariables_t {
	union {
		U8 ubAdminAuthenticated[TCG_MAX_ADMIN_SP_AUTHORITY_TABLE_LENGTH];
		U8 ubLockingAuthenticated[TCG_MAX_LOCKING_SP_AUTHORITY_TABLE_LENGTH];
	};
	union {
		U8 ubAdminTries[TCG_MAX_ADMIN_SP_C_PIN_TABLE_LENGTH];
		U8 ubLockingTries[TCG_MAX_LOCKING_SP_C_PIN_TABLE_LENGTH];
	};
	U8 ubReserved;
};
SIZE_CHECK_ALIGN_4(RestoreSpVariables_t);

typedef struct CloseSessionVariables_t   CloseSessionVariables_t, *CloseSessionVariablesPtr_t;
struct CloseSessionVariables_t {
	U8 ubReserved[3];
	U8 ubStatusCode;
};
SIZE_CHECK_ALIGN_4(CloseSessionVariables_t);

typedef struct SetBufferHeaderVariables_t   SetBufferHeaderVariables_t, *SetBufferHeaderVariablesPtr_t;
struct SetBufferHeaderVariables_t {
	U32 ulBufferBaseAddr;
	U32 ulBufferEndAddr;
	U8 ubCloseSession;
	U8 ubReserved[3];
};
SIZE_CHECK_ALIGN_4(SetBufferHeaderVariables_t);

typedef struct EndTransactionVariables_t   EndTransactionVariables_t, *EndTransactionVariablesPtr_t;
struct EndTransactionVariables_t {
	U32 ulUnlockedRangeBMP;
	U8 ubDecryptKeyIdx;
	U8 ubReserved[3];
};
SIZE_CHECK_ALIGN_4(EndTransactionVariables_t);

typedef struct CheckUnlockedRangeVariables_t   CheckUnlockedRangeVariables_t, *CheckUnlockedRangeVariablesPtr_t;
struct CheckUnlockedRangeVariables_t {
	U32 ulUnlockedRangeBMP;
};
SIZE_CHECK_ALIGN_4(CheckUnlockedRangeVariables_t);

typedef struct CheckDataRemovalMechanismVariables_t   CheckDataRemovalMechanismVariables_t, *CheckDataRemovalMechanismVariablesPtr_t;
struct CheckDataRemovalMechanismVariables_t {
	U8 ubDataRemovalMechanism;
	U8 aubReserved[3];
};
SIZE_CHECK_ALIGN_4(CheckDataRemovalMechanismVariables_t);

typedef struct TrimDekChangedRangeVariables_t   TrimDekChangedRangeVariables_t, *TrimDekChangedRangeVariablesPtr_t;
struct TrimDekChangedRangeVariables_t {
	//----------------------------------------------------------------//
	//This Part must be arranged at the Head of Struct, because the trim range Ptr should align 32 Bytes
	U8 ubRsvForTrimAlign[TCG_RESERVED_SIZE_FOR_TRIM_RANGE_ALIGN_32];
	TrimRangeNVMERaw_t TrimRange[TCG_MAX_DEK_CHANGED_TRIM_RANGE_NUM];
	//----------------------------------------------------------------//
	TrimRangeNVMERaw_t *pTrimRange;
	U8 ubLockingRangeIndex;
	U8 ubTrimCount;
	U8 ubTrimMode;
	U8 udReserved;
};
SIZE_CHECK_ALIGN_4(TrimDekChangedRangeVariables_t);

typedef struct TrimAllChangeRangeVariables_t   TrimAllChangeRangeVariables_t, *TrimAllChangeRangeVariablesPtr_t;
struct TrimAllChangeRangeVariables_t {
	//----------------------------------------------------------------//
	//This Part must be arranged at the Head of Struct, because the trim range Ptr should align 32 Bytes
	U8 ubReservedForTrimAlign[TCG_RESERVED_SIZE_FOR_TRIM_RANGE_ALIGN_32];
	TrimRangeNVMERaw_t TrimRangeSpace[TCG_MAX_RANGE_CHANGED_TRIM_RANGE_NUM];
	//----------------------------------------------------------------//
	U64 uoOldLockingRangeStartLBA[TCG_MAX_LOCKING_SP_LOCKING_TABLE_LENGTH];
	U64 uoOldLockingRangeLBACnt[TCG_MAX_LOCKING_SP_LOCKING_TABLE_LENGTH];
	TrimRangeNVMERaw_t *pTrimRange;
	U8 ubTrimRangeCount;
	U8 ubReserved[3];
};
SIZE_CHECK_ALIGN_4(TrimAllChangeRangeVariables_t);

typedef struct SyncSpVariables_t   SyncSpVariables_t, *SyncSpVariablesPtr_t;
struct SyncSpVariables_t {
	U8 ubIndex;
	U8 ubReserved[3];
};
SIZE_CHECK_ALIGN_4(SyncSpVariables_t);

typedef struct MethodInvokingVariables_t   MethodInvokingVariables_t, *MethodInvokingVariablesPtr_t;
struct MethodInvokingVariables_t {
	U32 ulTokenIndex;
};
SIZE_CHECK_ALIGN_4(MethodInvokingVariables_t);

typedef struct SessionManagementMethodInvokingVariables_t   SessionManagementMethodInvokingVariables_t, *SessionManagementMethodInvokingVariablesPtr_t;
struct SessionManagementMethodInvokingVariables_t {
	U32 ulTokenIndex;
};
SIZE_CHECK_ALIGN_4(SessionManagementMethodInvokingVariables_t);

typedef struct StartSessionVariables_t   StartSessionVariables_t, *StartSessionVariablesPtr_t;
struct StartSessionVariables_t {
	TcgSessionManagementFail_t ulFailType;
	U32 ulTokenIndex;
};
SIZE_CHECK_ALIGN_4(StartSessionVariables_t);

typedef struct SimpleMethodInvokingVariables_t   SimpleMethodInvokingVariables_t, *SimpleMethodInvokingVariablesPtr_t;
struct SimpleMethodInvokingVariables_t {
	U32 ulTokenIndex;
};
SIZE_CHECK_ALIGN_4(SimpleMethodInvokingVariables_t);

typedef struct AuthorityAuthenticateVariables_t   AuthorityAuthenticateVariables_t, *AuthorityAuthenticateVariablesPtr_t;
struct AuthorityAuthenticateVariables_t {
	AuthorityAuthenticateState_t AuthorityAuthenticateNextState;
	AuthorityTableElement_t AuthorityTableElement;
	CPinTableElementPtr_t CPinTableElementPtr;
	TcgEncryptedKek_t Ekek;
	TcgEpassword_t Epassword;
	TcgSpUidL_t ulSpUidL;
	TcgAuthorityUidL_t ulAuthorityUidL;
	U32 ulChallengeLength;
	U32 ulAuthorityTableIndex;
	U8 ubChallenge[TCG_MAX_HOST_CHALLENGE_LENGTH];
	U8 ubAuthorityNum;
	U8 ubKeyIndex;
	U8 btResult 		: 1;
	U8 btPasswordMatch  : 1;
	U8 					: 6;
	U8 ubReserved;
};
SIZE_CHECK_ALIGN_4(AuthorityAuthenticateVariables_t);

typedef struct GetAclVariables_t   GetAclVariables_t, *GetAclVariablesPtr_t;
struct GetAclVariables_t {
	U32 ulTokenIndex;
};
SIZE_CHECK_ALIGN_4(GetAclVariables_t);

typedef struct NextVariables_t   NextVariables_t, *NextVariablesPtr_t;
struct NextVariables_t {
	U32 ulTokenIndex;
	U32 ulOpalTableFlashIndex;
	U8 *pubBuf;
	U8 ubReserved[3];
	U8 ubNextTableIndex;
};
SIZE_CHECK_ALIGN_4(NextVariables_t);

typedef struct GenKeyVariables_t   GenKeyVariables_t, *GenKeyVariablesPtr_t;
struct GenKeyVariables_t {
	U32 ulTokenIndex;
};
SIZE_CHECK_ALIGN_4(GenKeyVariables_t);

typedef struct CheckAclVariables_t   CheckAclVariables_t, *CheckAclVariablesPtr_t;
struct CheckAclVariables_t {
	AceTableElement_t AceTable;
	TcgMethodUidL_t ulMethodUidL;
	U32 ulTableIndex;
	U32 ulInvokingUidH;
	U32 ulInvokingUidL;
	U32 ulOpalTableFlashIndex;
	U8 ubAclIndex;
	U8 ubAuthorityIndex;
	U8 ubResult;
	U8 ubReserved;
};
SIZE_CHECK_ALIGN_4(CheckAclVariables_t);

typedef struct MakeSureRangeKeyNecessaryVariables_t   MakeSureRangeKeyNecessaryVariables_t, *MakeSureRangeKeyNecessaryVariablesPtr_t;
struct MakeSureRangeKeyNecessaryVariables_t {
	U8 ubReadLockEnable;
	U8 ubWriteLockEnable;
	U8 ubKeyIndex;
	U8 ubReserved;
};
SIZE_CHECK_ALIGN_4(MakeSureRangeKeyNecessaryVariables_t);

typedef struct RevertSpVariables_t   RevertSpVariables_t, *RevertSpVariablesPtr_t;
struct RevertSpVariables_t {
	U32 ulTokenIndex;
	U32 ulLockOnResetBmp;
	U8 ubKeyIdx;
	U8 ubChangeDek;
	U8 aubReserved[2];
};
SIZE_CHECK_ALIGN_4(RevertSpVariables_t);

typedef struct EncryptDekVariables_t   EncryptDekVariables_t, *EncryptDekVariablesPtr_t;
struct EncryptDekVariables_t {
	U8 ubReserved[2];
	U8 ubKeyIndex;
	U8 ubRange;
};
SIZE_CHECK_ALIGN_4(EncryptDekVariables_t);

typedef struct GetVariables_t   GetVariables_t, *GetVariablesPtr_t;
struct GetVariables_t {
	U32 ulTokenIndex;
	U8 *pubBuf;
};
SIZE_CHECK_ALIGN_4(GetVariables_t);

typedef struct SetVariables_t   SetVariables_t, *SetVariablesPtr_t;
struct SetVariables_t {
	U32 ulTokenIndex;
	U32 ulOpalTableFlashIndex;
	U32 ulTablePtr;
	U8 ubReadLockEnable;
	U8 ubWriteLockEnable;
	U8 ubReadLock;
	U8 ubWriteLock;
	U8 ubRangIdx;
	U8 aubReserved[3];
};
SIZE_CHECK_ALIGN_4(SetVariables_t);

typedef struct AuthenticateVariables_t   AuthenticateVariables_t, *AuthenticateVariablesPtr_t;
struct AuthenticateVariables_t {
	U32 ulTokenIndex;
};
SIZE_CHECK_ALIGN_4(AuthenticateVariables_t);

typedef struct RevertVariables_t   RevertVariables_t, *RevertVariablesPtr_t;
struct RevertVariables_t {
	U32 ulReadWriteLockingSpLifeCycleResult;
	U32 ulTokenIndex;
	U32 ulLockOnResetBmp;
	U8 ubChangeDekOrKek;
	U8 aubReserved[3];
};
SIZE_CHECK_ALIGN_4(RevertVariables_t);

typedef struct RevertByteTableVariables_t   RevertByteTableVariables_t, *RevertByteTableVariablesPtr_t;
struct RevertByteTableVariables_t {
	//----------------------------------------------------------------//
	//This Part must be arranged at the Head of Struct, because the trim range Ptr should align 32 Bytes
	U8 ubSpaceForAlign[TCG_RESERVED_SIZE_FOR_TRIM_RANGE_ALIGN_32];
	TrimRangeNVMERaw_t TrimRangeSpace;
	//----------------------------------------------------------------//
	TrimRangeNVMERaw_t *pTrimRange;
};
SIZE_CHECK_ALIGN_4(RevertByteTableVariables_t);

typedef struct ActivateVariables_t   ActivateVariables_t, *ActivateVariablesPtr_t;
struct ActivateVariables_t {
	U32 ulTokenIndex;
	U32 ulTableIndex;
	U8 ubSIDPinlength;
	U8 aubReserved[3];
};
SIZE_CHECK_ALIGN_4(ActivateVariables_t);

typedef struct ReactivateVariables_t   ReactivateVariables_t, *ReactivateVariablesPtr_t;
struct ReactivateVariables_t {
	U32 ulTokenIndex;
	U32 ulTableIndex;
};
SIZE_CHECK_ALIGN_4(ReactivateVariables_t);

typedef struct EraseVariables_t   EraseVariables_t, *EraseVariablesPtr_t;
struct EraseVariables_t {
	U32 ulCredential;
	U32 ulCPinTableIdx;
};
SIZE_CHECK_ALIGN_4(EraseVariables_t);

typedef struct SecurityRecvSecp1Variables_t SecurityRecvSecp1Variables_t, *SecurityRecvSecp1VariablesPtr_t;
struct SecurityRecvSecp1Variables_t {
	U32 ulOutStandingBufferEndAddr;
};
SIZE_CHECK_ALIGN_4(SecurityRecvSecp1Variables_t);

typedef struct Level0DiscoveryVariables_t   Level0DiscoveryVariables_t, *Level0DiscoveryVariablesPtr_t;
struct Level0DiscoveryVariables_t {
	U8 ubLockingSpLifeCycle;
	U8 ubMBRDone;
	U8 ubMBREnable;
	U8 ubReserved;
};
SIZE_CHECK_ALIGN_4(Level0DiscoveryVariables_t);

typedef struct ResetCPinTableTriesVariables_t   ResetCPinTableTriesVariables_t, *ResetCPinTableTriesVariablesPtr_t;
struct ResetCPinTableTriesVariables_t {
	U32 ulSpUidL;
	U8 ubReserved[3];
	U8 ubSave;
};
SIZE_CHECK_ALIGN_4(ResetCPinTableTriesVariables_t);

typedef struct MBRTableDoneOnResetVariables_t   MBRTableDoneOnResetVariables_t, *MBRTableDoneOnResetVariablesPtr_t;
struct MBRTableDoneOnResetVariables_t {
	TcgReset_t ResetType;
};
SIZE_CHECK_ALIGN_4(MBRTableDoneOnResetVariables_t);

typedef struct ReadWrtieLockOnResetVariables_t   ReadWrtieLockOnResetVariables_t, *ReadWrtieLockOnResetVariablesPtr_t;
struct ReadWrtieLockOnResetVariables_t {
	TcgReset_t ResetType;
	U32 ulLockedRangeBMP;
};
SIZE_CHECK_ALIGN_4(ReadWrtieLockOnResetVariables_t);

typedef struct DecryptEkekRangeVariables_t   DecryptEkekRangeVariables_t, *DecryptEkekRangeVariablesPtr_t;
struct DecryptEkekRangeVariables_t {
	U8 ubPinDigest[TCG_DIGEST_SIZE_OF_SHA_512];
	U32 ulKeyIndex;
	U32 ulTableIndex;
};
SIZE_CHECK_ALIGN_4(DecryptEkekRangeVariables_t);

typedef struct DecryptAllEkekRangeVariables_t   DecryptAllEkekRangeVariables_t, *DecryptAllEkekRangeVariablesPtr_t;
struct DecryptAllEkekRangeVariables_t {
	U8 ubReserved[3];
	U8 ubKeyIndex;
};
SIZE_CHECK_ALIGN_4(DecryptAllEkekRangeVariables_t);

typedef struct DecryptEdekVariables_t   DecryptEdekVariables_t, *DecryptEdekVariablesPtr_t;
struct DecryptEdekVariables_t {
	U8 ubReserved[2];
	U8 ubKeyIndex;
	U8 ubRange;
};
SIZE_CHECK_ALIGN_4(DecryptEdekVariables_t);

typedef struct DecryptAllEdekVariables_t   DecryptAllEdekVariables_t, *DecryptAllEdekVariablesPtr_t;
struct DecryptAllEdekVariables_t {
	U8 ubReserved[2];
	U8 ubKeyIndex;
	U8 ubRange;
};
SIZE_CHECK_ALIGN_4(DecryptAllEdekVariables_t);

typedef struct EncryptAllDekVariables_t   EncryptAllDekVariables_t, *EncryptAllDekVariablesPtr_t;
struct EncryptAllDekVariables_t {
	U8 ubReserved[2];
	U8 ubKeyIndex;
	U8 ubRange;
};
SIZE_CHECK_ALIGN_4(EncryptAllDekVariables_t);

typedef struct EncryptKekAndPasswordWhenInit_t   EncryptKekAndPasswordWhenInit_t, *EncryptKekAndPasswordWhenInitPtr_t;
struct EncryptKekAndPasswordWhenInit_t {
	TcgPin_t MsidPin;
	U8 ubKeyIdx;
	U8 ubSidPinLength;
	U8 aubReserved[2];
};
SIZE_CHECK_ALIGN_4(EncryptKekAndPasswordWhenInit_t);

typedef struct EncryptKekRangeVariables_t   EncryptKekRangeVariables_t, *EncryptKekRangeVariablesPtr_t;
struct EncryptKekRangeVariables_t {
	U8 ubReserved[3];
	U8 ubKeyIndex;
};
SIZE_CHECK_ALIGN_4(EncryptKekRangeVariables_t);

typedef struct EncryptAllKekRangeVariables_t   EncryptAllKekRangeVariables_t, *EncryptAllKekRangeVariablesPtr_t;
struct EncryptAllKekRangeVariables_t {
	U8 ubKeyIndex;
	U8 ubReserved[3];
};
SIZE_CHECK_ALIGN_4(EncryptAllKekRangeVariables_t);

typedef struct ReadWriteLockingSpLifeCycleVariables_t   ReadWriteLockingSpLifeCycleVariables_t, *ReadWriteLockingSpLifeCycleVariablesPtr_t;
struct ReadWriteLockingSpLifeCycleVariables_t {
	U32 ulMethodUidL;
	U8 ubReaadWrtieOperation;
	U8 ubReaadWrtieResult;
	U8 ubLockingSpLifeCycle;
	U8 ubReserved;
};
SIZE_CHECK_ALIGN_4(ReadWriteLockingSpLifeCycleVariables_t);

typedef struct SaveTcgVTVariables_t   SaveTcgVTVariables_t, *SaveTcgVTVariablesPtr_t;
struct SaveTcgVTVariables_t {
	U8 ubIndex;
	U8 ubSaveVT;
	U8 aubReserved[2];
};
SIZE_CHECK_ALIGN_4(SaveTcgVTVariables_t);

typedef struct SaveMBRVariables_t   SaveMBRVariables_t, *SaveMBRVariablesPtr_t;
struct SaveMBRVariables_t {
	ExtendLCACmdEnum_t  WriteOriginCmd;
	ExtendLCACmdEnum_t  WriteSpareCmd;
	U32 ulNextState;
	U8 ubReserved[3];
	U8 ubExtendLCAManagerExist;
};
SIZE_CHECK_ALIGN_4(SaveMBRVariables_t);

typedef struct DataStoreShiftAndSize_t   DataStoreShiftAndSize_t, *DataStoreShiftAndSizePtr_t;
struct DataStoreShiftAndSize_t {
	U32 ulSize;
	U32 ulShift;
};
SIZE_CHECK_ALIGN_4(DataStoreShiftAndSize_t);

typedef struct LoadTcgVTVariables_t   LoadTcgVTVariables_t, *LoadTcgVTVariablesPtr_t;
struct LoadTcgVTVariables_t {
	U8 ubReserved[3];
	U8 ubIndex;
};
SIZE_CHECK_ALIGN_4(LoadTcgVTVariables_t);

typedef struct SaveDataStoreVariables_t   SaveDataStoreVariables_t, *SaveDataStoreVariablesPtr_t;
struct SaveDataStoreVariables_t {
	DataStoreShiftAndSize_t DataStoreShiftandSize;
	SaveDataStoreState_t ulNextState;
	SaveDataStoreState_t ulNext2State;
	U8 ubCopySpareToNormal;
	U8 ubDataStoreIndex;
	U8 ubExtendLCAManagerExist;
	U8 ubReserved;
};
SIZE_CHECK_ALIGN_4(SaveDataStoreVariables_t);

typedef struct HandleComIDRequestVariables_t   HandleComIDRequestVariables_t, *HandleComIDRequestVariablesPtr_t;
struct HandleComIDRequestVariables_t {
	U8 ubResult;
	U8 ubReserved[3];
};
SIZE_CHECK_ALIGN_4(HandleComIDRequestVariables_t);

typedef struct TperResetVariables_t   TperResetVariables_t, *TperResetVariablesPtr_t;
struct TperResetVariables_t {
	U32 ulLockedRangeBMP;
	U8 ubResult;
	U8 ubResetEnable;
	U8 ubRangeIdx;
	U8 ubReserved;
};
SIZE_CHECK_ALIGN_4(TperResetVariables_t);

typedef struct StartSessionErrorHandleVariables_t   StartSessionErrorHandleVariables_t, *StartSessionErrorHandleVariablesPtr_t;
struct StartSessionErrorHandleVariables_t {
	U32 ulTableIndex;
	U32 ulFailType;
};
SIZE_CHECK_ALIGN_4(StartSessionErrorHandleVariables_t);

typedef struct GetLockingAclVariables_t   GetLockingAclVariables_t, *GetLockingAclVariablesPtr_t;
struct GetLockingAclVariables_t {
	U32 ulInvokingUidH;
	U32 ulInvokingUidL;
	U32 ulMethodUidL;
};
SIZE_CHECK_ALIGN_4(GetLockingAclVariables_t);

typedef struct SingleUserModeEnableVariables_t   SingleUserModeEnableVariables_t, *SingleUserModeEnableVariablesPtr_t;
struct SingleUserModeEnableVariables_t {
	SingleUserModeEnableState_t ulNextState;
	U32 ulACETableIdx;
	U32 ulNextACETableIdx;
	U32 ulACEUidL;
	U32 ulNextACEUidL;
	U32 ulOpalTableFlashIdx;
	U32 ulNextOpalTableFlashIdx;
	U32 ulAuthorityUidL;
	U32 ulMethodUidL;
	U8 ubLockingObjectIdx;
	U8 ubUserOffset;
	U8 ubLockingObjectCnt;
	U8 ubReserved;
};
SIZE_CHECK_ALIGN_4(SingleUserModeEnableVariables_t);

typedef struct SetDataStoreRowsAndTableTableVariables_t   SetDataStoreRowsAndTableTableVariables_t, *SetDataStoreRowsAndTableTableVariablesPtr_t;
struct SetDataStoreRowsAndTableTableVariables_t {
	U32 ulMethodUidL;
};
SIZE_CHECK_ALIGN_4(SetDataStoreRowsAndTableTableVariables_t);

typedef struct FillMaxRowandColumnVariables_t   FillMaxRowandColumnVariables_t, *FillMaxRowandColumnVariablesPtr_t;
struct FillMaxRowandColumnVariables_t {
	U32 ulMethodUidL;
	U32 ulOpalTableFlashIndex;
};
SIZE_CHECK_ALIGN_4(FillMaxRowandColumnVariables_t);

typedef struct GetTableVariables_t   GetTableVariables_t, *GetTableVariablesPtr_t;
struct GetTableVariables_t {
	DataStoreShiftAndSize_t DataStoreShiftandSize;
	U32 ulCheckColumnIndex;
	U32 ulTablePtr;
	U32 ulOpalTableFlashIndex;
	U8 *pubBuf;
	U8 ubReserved[3];
	U8 ubDataStore;
};
SIZE_CHECK_ALIGN_4(GetTableVariables_t);

typedef struct CalculateDataStoreShiftVariables_t   CalculateDataStoreShiftVariables_t, *CalculateDataStoreShiftVariablesPtr_t;
struct CalculateDataStoreShiftVariables_t {
	DataStoreShiftAndSize_t DataStoreShiftandSize;
	U32 ulInvokingUidH;
};
SIZE_CHECK_ALIGN_4(CalculateDataStoreShiftVariables_t);

typedef struct CheckAceColumnVariables_t   CheckAceColumnVariables_t, *CheckAceColumnVariablesPtr_t;
struct CheckAceColumnVariables_t {
	AceTableElement_t AceTableElement;
	U32 ulCheckColumnIndex;
	U32 ulOpalTableFlashIndex;
	U8 ubReserved[2];
	U8 ubResult;
	U8 ubCheckAclIndex;
};
SIZE_CHECK_ALIGN_4(CheckAceColumnVariables_t);

typedef struct SetErrorHandleVariables_t   SetErrorHandleVariables_t, *SetErrorHandleVariablesPtr_t;
struct SetErrorHandleVariables_t {
	U32 ulByteTableMandatoryWriteGranularity;
	U32 ulCheckIndex;
};
SIZE_CHECK_ALIGN_4(SetErrorHandleVariables_t);

typedef struct SetCPinKeyProtectionVariables_t   SetCPinKeyProtectionVariables_t, *SetCPinKeyProtectionVariablesPtr_t;
struct SetCPinKeyProtectionVariables_t {
	U32 ulPinIndex;
};
SIZE_CHECK_ALIGN_4(SetCPinKeyProtectionVariables_t);

typedef struct LoadTableVariables_t   LoadTableVariables_t, *LoadTableVariablesPtr_t;
struct LoadTableVariables_t {
	U8 ubReserved[2];
	U8 ubCheckOpalTableResult;
	U8 ubTableSyncDirection;
};
SIZE_CHECK_ALIGN_4(LoadTableVariables_t);

typedef struct AdminInitVariables_t 	AdminInitVariables_t, *AdminInitVariablesPtr_t;
struct AdminInitVariables_t {
	TcgEpassword_t PsidEpassword;
};
SIZE_CHECK_ALIGN_4(AdminInitVariables_t);

typedef struct BlockSidAuthenticateVariables_t   BlockSidAuthenticateVariables_t, *BlockSidAuthenticateVariablesPtr_t;
struct BlockSidAuthenticateVariables_t {
	U8 ubResult;
	U8 uabReserved[3];
};
SIZE_CHECK_ALIGN_4(BlockSidAuthenticateVariables_t);

typedef struct LoadWriteProtectTCGTableVariables_t LoadWriteProtectTCGTableVariables_t, *LoadWriteProtectTCGTableVariablesPtr_t;
struct LoadWriteProtectTCGTableVariables_t {
	TcgFlashTableIndex_t ulLoadTableBasedIdx;
	U16 uwLoadVTIdx;
	U16 uwLoadTableIdx;
};
SIZE_CHECK_ALIGN_4(LoadWriteProtectTCGTableVariables_t);

typedef struct UpdateSecurityVersionVariables_t   UpdateSecurityVersionVariables_t, *UpdateSecurityVersionVariablesPtr_t;
struct UpdateSecurityVersionVariables_t {
	TcgAdminPartial1Ptr_t pAdminPartial1BufAddr;
	UpdateSecurityVersionState_t UpdateSecurityVersionNextState;
	U8 ubTableSyncDirection;
	U8 ubUpdateVersionDone;
	U8 btNeedReload 	: 1;
	U8 					: 7;
	U8 aubReserved[1];
};
SIZE_CHECK_ALIGN_4(UpdateSecurityVersionVariables_t);

typedef struct UpdateSecurityVersion3to4Variables_t   UpdateSecurityVersion3to4Variables_t, *UpdateSecurityVersion3to4VariablesPtr_t;
struct UpdateSecurityVersion3to4Variables_t {
	U32 ulUpdatingTableBufAddr;
	U32 ulTempBufAddr;
	U8 ubTableIdx;
	U8 aubReserved[3];
};
SIZE_CHECK_ALIGN_4(UpdateSecurityVersion3to4Variables_t);

typedef struct SetLockOnResetKeyVariables_t   SetLockOnResetKeyVariables_t, *SetLockOnResetKeyVariablesPtr_t;
struct SetLockOnResetKeyVariables_t {
	U8 ubKeyIdx;
	U8 aubReserved[3];
};
SIZE_CHECK_ALIGN_4(SetLockOnResetKeyVariables_t);

typedef struct AllFunctionStates_t   AllFunctionStates_t, *AllFunctionStatesPtr_t;
struct AllFunctionStates_t {
	SecuritySendSecurityProtocol2State_t SecuritySendSecurityProtocol2State;
	RestoreSpState_t			RestoreSpState;
	RestoreSpVariablesPtr_t		pRestoreSpVariables;
	SessionInitState_t			SessionInitState;
	SecuritySendSecurityProtocol1State_t	SecuritySendSecurityProtocol1State;
	SessionCheckTimeOutState_t	SessionCheckTimeOutState;
	EndTransactionState_t			EndTransactionState;
	EndTransactionVariablesPtr_t	pEndTransactionVariables;
	CheckUnlockedRangeState_t			CheckUnlockedRangeState;
	CheckUnlockedRangeVariablesPtr_t 	pCheckUnlockedRangeVariables;
	SetBufferHeaderState_t			SetBufferHeaderState;
	SetBufferHeaderVariablesPtr_t	pSetBufferHeaderVariables;
	CloseSessionState_t			CloseSessionState;
	CloseSessionVariablesPtr_t	pCloseSessionVariables;
	StartTransactionState_t		StartTransactionState;
	SaveTableState_t			SaveTableState;
	SaveSpState_t				SaveSpState;
	TrimDEKChangeRangeState_t			TrimDEKChangeRangeState;
	TrimDekChangedRangeVariablesPtr_t	pTrimDekChangedRangeVariables;
	CheckDataRemovalMechanismState_t	CheckDataRemovalMechanismState;
	CheckDataRemovalMechanismVariablesPtr_t	pCheckDataRemovalMechanismVariables;
	TrimAllChangedRangesState_t			TrimAllChangedRangesState;
	TrimAllChangeRangeVariablesPtr_t	pTrimAllChangeRangeVariables;
	SyncSpState_t					SyncSpState;
	SyncSpVariablesPtr_t			pSyncSpVariables;
	SetLockOnResetKeyState_t		SetLockOnResetKeyState;
	SetLockOnResetKeyVariablesPtr_t	pSetLockOnResetKeyVariables;
	SetLockingRangeAndKeyState_t	SetLockingRangeAndKeyState;
	SetShadowMBRState_t				SetShadowMBRState;
	MethodInvokingState_t			MethodInvokingState;
	MethodInvokingVariablesPtr_t	pMethodInvokingVariables;
	SessionManagementMethodInvokingState_t			SessionManagementMethodInvokingState;
	SessionManagementMethodInvokingVariablesPtr_t	pSessionManagementMethodInvokingVariables;
	StartSessionState_t							StartSessionState;
	StartSessionVariablesPtr_t					pStartSessionVariables;
	SimpleMethodInvokingState_t					SimpleMethodInvokingState;
	SimpleMethodInvokingVariablesPtr_t			pSimpleMethodInvokingVariables;
	AuthorityAuthenticateState_t				AuthorityAuthenticateState;
	AuthorityAuthenticateVariablesPtr_t			pAuthorityAuthenticateVariables;
	GetAclState_t								GetAclState;
	GetAclVariablesPtr_t						pGetAclVariables;
	CheckAclState_t								CheckAclState;
	CheckAclVariablesPtr_t						pCheckAclVariables;
	NextState_t									NextState;
	NextVariablesPtr_t							pNextVariables;
	GenKeyState_t								GenKeyState;
	GenKeyVariablesPtr_t						pGenKeyVariables;
	MakeSureKeyRangeNexessaryState_t			MakeSureKeyRangeNexessaryState;
	MakeSureRangeKeyNecessaryVariablesPtr_t		pMakeSureRangeKeyNecessaryVariables;
	RevertSpState_t 							RevertSpState;
	RevertSpVariablesPtr_t						pRevertSpVariables;
	RevertSpCheckGlobalRangeReadWriteLockState_t	RevertSpCheckGlobalRangeReadWriteLockState;
	EncryptDekState_t							EncryptDekState;
	EncryptDekVariablesPtr_t					pEncryptDekVariables;
	LockingInitState_t							LockingInitState;
	GetState_t									GetState;
	GetVariablesPtr_t							pGetVariables;
	SetState_t									SetState;
	SetVariablesPtr_t							pSetVariables;
	AuthenticateState_t							AuthenticateState;
	AuthenticateVariablesPtr_t					pAuthenticateVariables;
	RevertState_t								RevertState;
	RevertVariablesPtr_t						pRevertVariables;
	RevertByteTableState_t						RevertByteTableState;
	RevertByteTableVariablesPtr_t				pRevertByteTableVariables;
	AdminInitState_t							AdminInitState;
	AdminInitVariablesPtr_t						pAdminInitVariables;
	ActivateState_t								ActivateState;
	ActivateVariablesPtr_t						pActivateVariables;
	ReactivateState_t							ReactivateState;
	ReactivateVariablesPtr_t					pReactivateVariables;
	EraseState_t								EraseState;
	EraseVariablesPtr_t							pEraseVariables;
	RandomState_t								RandomState;
	SecurityRecvSecp1State_t					SecurityRecvSecp1State;
	SecurityRecvSecp1VariablesPtr_t				pSecurityRecvSecp1Variables;
	Level0DiscoveryState_t						Level0DiscoveryState;
	Level0DiscoveryVariablesPtr_t				pLevel0DiscoveryVariables;
	EndSessionState_t							EndSessionState;
	ResetCPinTableTriesState_t					ResetCPinTableTriesState;
	ResetCPinTableTriesVariablesPtr_t			pResetCPinTableTriesVariables;
	MBRTableDoneOnResetState_t					MBRTableDoneOnResetState;
	MBRTableDoneOnResetVariablesPtr_t			pMBRTableDoneOnResetVariables;
	ReadWrtieLockOnResetState_t					ReadWrtieLockOnResetState;
	ReadWrtieLockOnResetVariablesPtr_t			pReadWrtieLockOnResetVariables;
	DecryptEkekRangeState_t						DecryptEkekRangeState;
	DecryptEkekRangeVariablesPtr_t				pDecryptEkekRangeVariables;
	DecryptAllEkekRangeState_t					DecryptAllEkekRangeState;
	DecryptAllEkekRangeVariablesPtr_t			pDecryptAllEkekRangeVariables;
	DecryptEdekState_t							DecryptEdekState;
	DecryptEdekVariablesPtr_t					pDecryptEdekVariables;
	DecryptAllEdekState_t						DecryptAllEdekState;
	DecryptAllEdekVariablesPtr_t				pDecryptAllEdekVariables;
	EncryptAllDekState_t						EncryptAllDekState;
	EncryptAllDekVariablesPtr_t					pEncryptAllDekVariables;
	EncryptKekAndPasswordWhenInitState_t		EncryptKekAndPasswordWhenInitState;
	EncryptKekAndPasswordWhenInitPtr_t			pEncryptKekAndPasswordWhenInitVariables;
	EncryptKekRangeState_t						EncryptKekRangeState;
	EncryptKekRangeVariablesPtr_t				pEncryptKekRangeVariables;
	EncryptAllKekRangeState_t					EncryptAllKekRangeState;
	EncryptAllKekRangeVariablesPtr_t			pEncryptAllKekRangeVariables;
	Pyrite1SecurityKeyProtectDekState_t			Pyrite1SecurityKeyProtectDekState;
	ForceSetSupportedFeatureState_t 			ForceSetSupportedFeatureState;
	ReadWriteLockingSpLifeCycle_t				ReadWriteLockingSpLifeCycleState;
	ReadWriteLockingSpLifeCycleVariablesPtr_t	pReadWriteLockingSpLifeCycleVariables;
	SaveOpalVTState_t 					SaveOpalVTState;
	SaveTcgVTVariablesPtr_t			pSaveTcgVTVariables;
	SaveMBRState_t 						SaveMBRState;
	SaveMBRVariablesPtr_t 				pSaveMBRVariables;
	SaveDataStoreState_t				SaveDataStoreState;
	SaveDataStoreVariablesPtr_t			pSaveDataStoreVariables;
	LoadOpalVTState_t					LoadOpalVTState;
	LoadTcgVTVariablesPtr_t			pLoadTcgVTVariables;
	HandleComIdRequestState_t			HandleComIdRequestState;
	HandleComIDRequestVariablesPtr_t	pHandleComIDRequestVariables;
	TperResetState_t					TperResetState;
	TperResetVariablesPtr_t				pTperResetVariables;
	StackResetState_t					StackResetState;
	SendBufferDecodeState_t				SendBufferDecodeState;
	StartSessionErrorHandleState_t			StartSessionErrorHandleState;
	StartSessionErrorHandleVariablesPtr_t	pStartSessionErrorHandleVariables;
	ProrpertyResponseState_t			ProrpertyResponseState;
	GetLockingAclState_t				GetLockingAclState;
	GetLockingAclVariablesPtr_t			pGetLockingACLVariables;
	SingleUserModeEnableState_t			SingleUserModeEnableState;
	SingleUserModeEnableVariablesPtr_t	pSingleUserModeEnableVariables;
	SetDataStoreRowsAndTableTableState_t		SetDataStoreRowsAndTableTableState;
	SetDataStoreRowsAndTableTableVariablesPtr_t	pSetDataStoreRowsAndTableTableVariables;
	CheckAllReadWriteLockEnableState_t	CheckAllReadWriteLockEnableState;
	FillMaxRowAndColumnState_t			FillMaxRowAndColumnState;
	FillMaxRowandColumnVariablesPtr_t	pFillMaxRowandColumnVariables;
	GetTableState_t						GetTableState;
	GetTableVariablesPtr_t				pGetTableVariables;
	CalculateDataStoreShiftState_t 			CalculateDataStoreShiftState;
	CalculateDataStoreShiftVariablesPtr_t	pCalculateDataStoreShiftVariables;
	CheckAceColumnState_t				CheckAceColumnState;
	CheckAceColumnVariablesPtr_t		pCheckAceColumnVariables;
	SetErrorHandleState_t 				SetErrorHandleState;
	SetErrorHandleVariablesPtr_t		pSetErrorHandleVariables;
	SetCPinKeyProtectionState_t			SetCPinKeyProtectionState;
	SetCPinKeyProtectionVariablesPtr_t	pSetCPinKeyProtectionVariables;
	LoadTableState_t					LoadTableState;
	LoadTableVariablesPtr_t				pLoadTableVariables;
	GetNonVolatileTcgVTState_t			GetNonVolatileTcgVTState;
	DecryptUnlockDekState_t				DecryptUnlockDekState;
	BlockSidAuthenticateState_t			BlockSidAuthenticateState;
	BlockSidAuthenticateVariablesPtr_t	pBlockSidAuthenticateVariables;
	LoadWriteProtectTCGTableState_t		LoadWriteProtectTCGTableState;
	LoadWriteProtectTCGTableVariablesPtr_t	pLoadWriteProtectTCGTableVariables;
	UpdateSecurityVersionState_t		UpdateSecurityVersionState;
	UpdateSecurityVersionVariablesPtr_t pUpdateSecurityVersionVariables;
	UpdateSecurityVersion3to4State_t 	UpdateSecurityVersion3to4State;
	UpdateSecurityVersion3to4VariablesPtr_t pUpdateSecurityVersion3to4Variables;
};
SIZE_CHECK_ALIGN_4(AllFunctionStates_t);

typedef struct {
	TcgKek_t 					Kek;
	TcgBufManager_t				BufManager;
	TcgVariablesManager_t		VariablesManager;
	TcgInitState_t				InitState;
	TcgCryptoEraseState_t		CryptoEraseState;
	TcgSecuritySendState_t		SecuritySendState;
	TcgSecurityRecvState_t		SecurityRecvState;
	TcgAllocateManagersAndTcgVTBufState_t	AllocateManagersAndTcgVTBufState;
	TcgFreeManagersAndTcgVTBufState_t FreeManagersAndTcgVTBufState;
	AllFunctionStatesPtr_t	pAllFunctionStates;
	ExtendLCA_t					*pExtendLCAManager;
	TCGVariableTable_t			*pVT;

	U8 aubSecurityKey[TCG_SECURITY_KEY_SIZE];
	U32 PayLoadLBOffset			: 10;
	U32	PayLoadDoingCnt			: 3;
	U32 PayLoadLBId				: 3;
	U32	CmdSequenceState		: 2;
	U32	HostStatusCode 			: 3;
	U32	ReturnType				: 2;
	U32	btPayLoadExist			: 1;
	U32	btSaveVT				: 1;
	U32	btReadWriteBusy			: 1;
	U32	btTableDamaged			: 1;
	U32	btEraseInitIntoUnit		: 1;
	U32	btTrimBusy				: 1;
	U32	btOutStandingData		: 1;
	U32	btDisableLPM			: 1;
	U32 btDataEncrypted			: 1;

	U8 btHostCmdEnable              : 1;
	U8 SetPsidState                 : 4;
	U8 btKekExist                   : 1;
	U8 btUpdateSecurityVersionDoing : 1;
	U8 btLockingSPActivated         : 1;

	U8 btRLBLowLimitAdjust          : 1;
	U8 SSCMode                      : 2;
	U8 btWriteProtectBufferExist    : 1;
	U8 btWriteProtectBufferValid    : 1;
	U8                              : 3;
	U8 ubTcgInitFlowReason;

	U32 ulLastInvokingUidH;
	U16 uwLastMethodID;
	U16 uwLastInfoID;
	U16 uwWriteProtectBufLBOffset;
	U8 ubWriteProtectBufLBID;
	U8 ubCurrentSecurityVersion;

	U64 uoFlowStartTimeStamp;
	U64 uoFlowEndTimeStamp;
} Tcg_t;
SIZE_CHECK_ALIGN_4(Tcg_t);


// Old security version structure
// ---- Security version 3 ----
typedef struct {
	U8								ubSecuritySrcBufAddr128[TCG_SECURITY_SRC_BUF_SIZE];
	U8								ubSecurityDstBufAddr128[TCG_SECURITY_DST_BUF_SIZE];
	TcgKek_t						KekRange[TCG_AES_KEY_NUM_VERSION_3];
	TcgDek_t						Dek[TCG_AES_KEY_NUM_VERSION_3];
	U32								ulUserDataSize;
	Flag_t							Flag;
	HostCmd_t						HostCmd;
	HostProperty_t					HostProperty;
	TperProperty_t					TperProperty;
	ComIdProperty_t					ComIdProperty;
	SessionProperty_t				SessionProperty;
	TcgStatusCode_t					StatusCode;
	Token_t							AllToken;
	HeaderDecodePtr_t				pHeaderDecode;
	UidDecode_t						UidDecode;
	AclList_t						AclList;
	union {
		PropertyParameter_t					PropertyParameter;
		StartSessionParameter_t				StartSessionParameter;
		GetAclParameter_t					GetAclParameter;
		GetParameter_t						GetParameter;
		SetParameter_t						SetParameter;
		NextParameter_t						NextParameter;
		AuthenticateParameter_t				AuthenticateParameter;
		ActivateParameter_t					ActivateParameter;
		ReactivateParameter_t				ReactivateParameter;
		RandomParameter_t					RandomParameter;
		RevertSpParameter_t					RevertSpParameter;
	} Method;
	TcgDRBGStruct_t	DRBG;
	union {
		U8 uabPsidDigest[SECURITY_SHA_256_OUTPUT_LENGTH];
		TcgDek_t D2HDek;
		U8 ubReserved[TCG_VARIABLE_TABLE_RESERVED_SIZE_VERSION_3];
	};
	struct {
		U8 ubPbkdf2Salt[TCG_PBKDF2_SALT_SIZE];
		U8 btSIDValueState				: 1;
		U8 btSIDBlockedState			: 1;
		U8 btCryptoEraseWithoutKek		: 1;
		U8 								: 4;
		U32 ulLockOnResetBmp;
	} NonVolatile;
} TCGVariableTableVer3_t;

#endif /* _TCG_STRUCT_H_ */
