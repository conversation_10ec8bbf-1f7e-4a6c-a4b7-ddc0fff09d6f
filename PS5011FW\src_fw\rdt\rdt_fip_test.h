#include "rdt/rdt_api.h"
#ifndef _RDT_FIP_TEST_H_
#define _RDT_FIP_TEST_H_

#include "rdt/rdt_ldpc_test.h"
#include "rdt/rdt_fip_instruction_fetching_test.h"
#include "rdt/rdt_pio_force_wr_log.h"
#include "hal/sys/api/clk/clk_api.h"
#include "hal/fip/fip_api.h"
#include "hal/sys/api/rng/rng_api.h"

#if (RDT_MODE_EN)

#define MAX_CH_CNT              				(4)
#define IBUF_SIZE 								(304 * 16)  //IBUF size:304*16Byte
#define IBUF_SIZE_ALIGN_512B					(((IBUF_SIZE + SIZE_512B - 1) / SIZE_512B) * SIZE_512B)
#define IBUF_NUM_OF_CH							(4)
#define IBUF_SIZE_IN_CH							(IBUF_SIZE * IBUF_NUM_OF_CH)
#define IBUF_SIZE_IN_CH_ALIGN_512B				((((IBUF_SIZE + SIZE_512B - 1) / SIZE_512B) * SIZE_512B) * IBUF_NUM_OF_CH)
#define IBUF_LDPC_DATA_SIZE						(4 * 4 * 304* 2) //4ch * 4frame * 304byte * 2LDPC data
#define IBUF_RANDOM_PATTERN_MAKE_LDPC_BIT_ONE	(16)
#define IBUF_RANDOM_PATTERN_MAKE_LDPC_BIT_ZERO	(16)
#define IBUF_RANDOM_SEED						(0x9F913F3)

#define	MAP_FIFO_PNT_RST_BIT_SHIFT				(8)
#define	MAP_FIFO_PNT_RST_BIT_MASK				BIT_MASK(1)
#define RDT_CHK_INT_INFO(CH)					((M_FIP_GET_MAP_CONFIG(CH) & PING_PONG_FIFO_INF_SHIFT_MASK) && (M_FIP_GET_INT_INFO(CH) & ~(MAP_FIFO_PNT_RST_BIT_MASK << MAP_FIFO_PNT_RST_BIT_SHIFT)))

#define LDPC_BIT_COVERAGE_EXP	        		(FALSE)

/*************** test pattern ***************/

typedef enum IBUF_BR_TEST_PATTERN {
	IBUF_BR_PATTERN_0_0     = 0,    //0xFFFF...FFFF (128 bits), 0x0 (128 bits), 0xFFFF...FFFF (128 bits), 0x0 (128 bits), .....
	IBUF_BR_PATTERN_0_1,			//0x0 (128 bits), 0xFFFF...FFFF (128 bits), 0x0 (128 bits), 0xFFFF...FFFF (128 bits), .....
	IBUF_BR_PATTERN_1_0,			//0xFFFF_FFFF_FFFF_FFFE (odd), 0xFFFF_FFFF_FFFF_FFFF (even), 0xFFFF_FFFF_FFFF_FFFE (odd), 0xFFFF_FFFF_FFFF_FFFF (even), ....
	IBUF_BR_PATTERN_1_1,			//0x0000_0000_0000_0001 (odd), 0x0000_0000_0000_0000 (even), 0x0000_0000_0000_0001 (odd), 0x0000_0000_0000_0000 (even), ....
	IBUF_BR_PATTERN_2_0,			//0x7FFF_FFFF_FFFF_FFFF (odd), 0xFFFF_FFFF_FFFF_FFFF (even), 0x7FFF_FFFF_FFFF_FFFF (odd), 0xFFFF_FFFF_FFFF_FFFF (even), ....
	IBUF_BR_PATTERN_2_1,			//0x8000_0000_0000_0000 (odd), 0x0000_0000_0000_0000 (even), 0x8000_0000_0000_0000 (odd), 0x0000_0000_0000_0000 (even), ....
	IBUF_BR_PATTERN_3,				//Random Data
	IBUR_BR_PATTERN_NUM
} IBUF_BR_TEST_PATTERN;

typedef enum IBUF_LDPC_TEST_PATTERN {
	IBUF_LDPC_PATTERN_0X00 = 0,
	IBUF_LDPC_PATTERN_0XFF_CORRECTABLE,
	IBUF_LDPC_PATTERN_0XAA55_ERR80,
	IBUF_LDPC_PATTERN_0X55AA_ERR80,
	IBUF_LDPC_PATTERN_0XFF_UECC,
	IBUF_LDPC_PATTERN_0XAA55_ERR125,
	IBUF_LDPC_PATTERN_0X55AA_ERR125,
	IBUR_LDPC_PATTERN_NUM,
} IBUF_LDPC_TEST_PATTERN;

#define IBUF_LDPC_PATTERN_SHIFT_8B_0XFF			(IBUR_LDPC_PATTERN_NUM + 1)
#define LDPC_MODE_NUM							(9)

#define IBUF_RAND_VEC_A   						(136204069ULL)      		// 3x7x11x13x17x23x29x4 + 1
#define IBUF_RAND_VEC_B   						(28500701229ULL)  			// 3x7x11x13x17x23x27x29x31

#ifndef FIRST_FSA_SHIFT  // borrow from E13_fip_reg.h because S17 do not define currently
#define		FIRST_FSA_SHIFT						(0)
#define		FIRST_FSA_MASK						BIT_MASK(7)
#define		FIRST_FSA_SHIFT_MASK				(FIRST_FSA_MASK << FIRST_FSA_SHIFT)
#define		M_SET_FSA_SEL(X)					((U32)(X) & FIRST_FSA_MASK)
#endif


/* Private function */
#if LDPC_BIT_COVERAGE_EXP
void rdt_LDPC_64_to_1_parity_bit_update (U32 parity_data_buf, U32 read_data_buf, U8 ldpc_mode, U8 direction);
void rdt_LDPC_bit_xor_update(U32 ldpc_data_buf, U32 ldpc_table_addr, U8 ldpc_mode, U8 direction);
#endif
U8 rdt_bank_to_phy_ce(U8 ubCh, U8 ubBank);
U64 rdt_getIntRandValue(U64 *random_value, U64 limit);
void rdt_ibuf_input_setup(U32 write_data_buf, U8 pattern);

/* API function */
BOOL rdt_api_fpu_write(U8 ubDie, U8 ubCh, U32 uwBlock, U32 uwPage, U8 ubFrameCnt, U32 data_buf);
BOOL rdt_api_fpu_read(U8 ubDie, U8 ubCh, U32 uwBlock, U32 uwPage, U8 ubFrameCnt, U32 data_buf);
BOOL rdt_api_fpu_trigger(U16 uwFpu_offset, U8 ubCh, U8 wait_ready);
BOOL rdt_api_fpu_check_read(U8 ubCh);
void rdt_api_switch_ldpc(U8 mode);
void rdt_fip_BackupRestore(U8 ubCh, U64 dram_addr, U32 iram_addr, U8 ibf_ptr, U8 direction, U8 mode, U8 bch_mode, U8 all, U8 frm_msk);

/* Test function */
BOOL rdt_fpu_prog_read_process(RDT_API_STRUCT_PTR rdt, U8 ubFrameCnt);
BOOL rdt_fpu_prog_ibuf_backup_process(U32 read_data_buf, U32 write_data_buf, U8 pattern);
BOOL rdt_api_fip_test(RDT_API_STRUCT_PTR rdt);
BOOL rdt_api_fpu_prog_read_test(RDT_API_STRUCT_PTR rdt);
BOOL rdt_api_fpu_prog_ibuf_backup_test(RDT_API_STRUCT_PTR rdt);
// extern BOOL rdt_ldpc_test(void);

/* Deprecating */
// void rdt_ibuf_pattern_input_setup(const U32 write_data_buf, U8 pattern);
// void rdt_ibuf_pattern_random_flip_bits(U32 data_buf, U32 data_buf_size, U16 number_of_bits);
// void rdt_ibuf_pattern_reformat(const U32 write_data_buf, U8 ldpc_mode);
// void rdt_ibuf_pattern_output_setup(U32 write_data_buf, U8 pattern);
// BOOL rdt_ibuf_br_test();
// void rdt_ibuf_restore_init();
// BOOL rdt_ibuf_br( U64 dram_addr, U32 iram_addr, U8 ibf_ptr, U8 direction, U8 mode, U8 bch_mode, U8 all, U8 frm_msk, U8 ubCh);
// BOOL rdt_ldpc_trigger_correct(U8 ubCh, U8 ubFrame);
// void rdt_ibuf_br_process(U32 read_data_buf, U32 write_data_buf, U8 ubPattern);

/* typedef struct {
	U8 ubData[2048];
	U8 ubSpr[8];
	U8 ubCrc[8];
	union {
		U8 ubAll[304];
		U8 ubMode0[256];
		U8 ubMode1[240];
		U8 ubMode2[224];
		U8 ubMode3[208];
		U8 ubMode4[176];
		U8 ubMode5[144];
		U8 ubMode6[128];
		U8 ubMode7[304];
		U8 ubMode8[288];
	} ubPty;
} LPDC_GOLDEN_PATTERN_FORMAT; */
#endif /* RDT_MODE_EN */
#endif  /* _RDT_FIP_TEST_H_ */
