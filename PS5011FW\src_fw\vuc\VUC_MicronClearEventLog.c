#include <string.h>
#include "VUC_MicronClearEventLog.h"
#include "VUC_MicronGetUnifiedEventLog.h"
#include "trim/ftl_trim_api.h"

U8 gubLastClearEventLogType = 0;

#if (VUC_MICRON_COMMON_VS_COMMANDS_EN)
void VUCMicronClearEventLog(U32 ulInputPayloadAddr, U32 ulResponsePayloadAddr)
{
	U32 ulCMDStartAddr = ulInputPayloadAddr + VUC_MICRON_CLEAR_EVENT_LOG_HEADER_LENGTH;

	U8 *pubCMD = (U8 *)ulCMDStartAddr;
	TrimRangeNVMERaw_t *pTrimRange;

	ClearEventLogResponseHEADER_t *pResponseHeader;

	memset((void *)ulResponsePayloadAddr, 0, VUC_MICRON_CLEAR_EVENT_LOG_HEADER_LENGTH);

	pResponseHeader = (ClearEventLogResponseHEADER_t *)ulResponsePayloadAddr;

	pResponseHeader->ubResponseHeaderFormatVersion = 0x00;
	pResponseHeader->ubResponseDataFotmatVersion = 0x00;
	pResponseHeader->uwCMDClass = VUC_MICRON_CLEAR_EVENT_LOG_CLASS;
	pResponseHeader->uwCMDCode = VUC_MICRON_CLEAR_EVENT_LOG_CODE;
	pResponseHeader->uwStatus = 0;
	pResponseHeader->ulDataPayloadSize = 0x00;

	switch (*pubCMD) {
	case VUC_MICRON_CLEAR_EVENT_LOG_UNIFIED_NAND_EVENT_LOG:
		gpVTDBUF->WriteProtect.btMicronNandMode = TRUE;
		pTrimRange = NULL;
#if (UNIFIED_LOG_EN)
		pTrimRange = (TrimRangeNVMERaw_t *)ulInputPayloadAddr;
		pTrimRange->ulLength = (gUnifiedLogVariable.ulLCATail - gUnifiedLogVariable.ulLCAHead) << SECTORS_PER_4K_LOG;
		pTrimRange->uoStartLBA = gUnifiedLogVariable.ulLCAHead << SECTORS_PER_4K_LOG;

		gubLastClearEventLogType = VUC_MICRON_CLEAR_EVENT_LOG_UNIFIED_NAND_EVENT_LOG;

		M_UART(NRW_, "\n [WZERO] TrimSetup");

		TrimSetup( ulInputPayloadAddr, 0, 0, 0, TRIM_MODE_VUC_MICRON_UNIFIED_EVENT_LOG);

		gUnifiedLogVariable.Flag.btTrimDoing = 1;
#endif /*(UNIFIED_LOG_EN)*/
		break;
	default :
		break;
	}


}

#endif /*(VUC_MICRON_COMMON_VS_COMMANDS_EN)*/