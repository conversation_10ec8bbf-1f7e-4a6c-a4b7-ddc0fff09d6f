#ifndef _TCG_H_
#define _TCG_H_
#include "debug/debug.h"
#include "aom/aom_api.h"
#include "tcg_def.h"
#include "hal/bmu/bmu_api.h"
#include "tcg_api.h"

#define TCG_INIT_REASON_POWER_ON_INIT     (BIT0)
#define TCG_INIT_REASON_1ST_CMD_INIT      (BIT1)
#define TCG_INIT_REASON_HRST              (BIT2)
#define TCG_INIT_REASON_BURNER_PREFORMAT  (BIT3)
#define TCG_INIT_REASON_DLMC_PREFORMAT    (BIT4)
#define TCG_INIT_REASON_LPM_WAKEUP        (BIT5)
#define TCG_INIT_REASON_INVALID           (BIT7)
#define TCG_INIT_REASON_ALL               (TCG_INIT_REASON_POWER_ON_INIT | TCG_INIT_REASON_1ST_CMD_INIT | TCG_INIT_REASON_HRST | TCG_INIT_REASON_BURNER_PREFORMAT | TCG_INIT_REASON_DLMC_PREFORMAT | TCG_INIT_REASON_LPM_WAKEUP)

#if ((PS5013_EN || PS5021_EN) && BURNER_MODE_EN)
#define M_TCG_CHECK_INIT_REASON(ubReason)   ((TCG_INIT_REASON_BURNER_PREFORMAT & (ubReason)) != 0)
#define M_TCG_VERIFY_INIT_REASON(ubReason)  ((TCG_INIT_REASON_BURNER_PREFORMAT & (ubReason)) != 0)
#else /* ((PS5013_EN || PS5021_EN) && BURNER_MODE_EN) */
#define M_TCG_CHECK_INIT_REASON(ubReason)   ((gTcg.ubTcgInitFlowReason & (ubReason)) != 0)
#if (TCG_DEBUG_VERIFY_INIT_FLOW)
#define M_TCG_VERIFY_INIT_REASON(ubReason)  ((gTcg.ubTcgInitFlowReason & (ubReason)) != 0)
#else /* (TCG_DEBUG_VERIFY_INIT_FLOW) */
#define M_TCG_VERIFY_INIT_REASON(...)       (TRUE)
#endif /* (TCG_DEBUG_VERIFY_INIT_FLOW) */
#endif /* ((PS5013_EN || PS5021_EN) && BURNER_MODE_EN) */

#define M_TCG_AES_BUG_WORKAROUND() 		(FALSE && M_TCG_CHECK_OPAL())  //@JIRA E13-293
#define M_TCG_GET_4K_ALIGN_OFFSET(ulCheckValue) (((U32)ulCheckValue) % SIZE_4KB)
#if (PS5017_EN)
#define M_TCG_GET_TUNNING_TIME()        ((TRUE == gRTTVariation.ubRTTVariationFlag) ? (M_GET_FW_TIMER()) : ((M_GET_FW_TIMER() * 105) / 100))  //Increase 105% value
#else /* (PS5017_EN) */
#define M_TCG_GET_TUNNING_TIME()        (M_GET_FW_TIMER())
#endif /* (PS5017_EN) */

#define M_TCG_NEED_INTEGRITY_CHECK() 	                ((TRUE == TCG_OBJECT_TABLE_INTEGRITY_CHECK) && (gTcg.ubCurrentSecurityVersion > 3) && (gTcg.BufManager.ulCmdTargetFlashIdx > TcgFlashTableVT2Index))
#define M_TCG_CHECK_WRITE_PROTECT_MODE()			    (0 != gpVTDBUF->WriteProtect.ubAll)
#define M_TCG_CHECK_WRITE_PROTECT_BUFFER_EXIST()	    ((0 != gpVTDBUF->WriteProtect.ubAll) && (TRUE == gTcg.btWriteProtectBufferExist))
#define M_TCG_CHECK_WRITE_PROTECT_BUFFER_VALID()	    ((0 != gpVTDBUF->WriteProtect.ubAll) && (TRUE == gTcg.btWriteProtectBufferExist) && (TRUE == gTcg.btWriteProtectBufferValid))

#define M_TCG_SUPPORT_PSID_FEATURE()                    (M_TCG_CHECK_OPAL() || M_TCG_CHECK_PYRITE())
#define M_TCG_SUPPORT_ADDITIONAL_DATASTORE_FEATURE()    (M_TCG_CHECK_OPAL())
#define M_TCG_SUPPORT_SINGLE_USER_MODE_FEATURE()        (M_TCG_CHECK_OPAL())
#define M_TCG_SUPPORT_BLOCK_SID_FEATURE()               (M_TCG_CHECK_PYRITE())

#define M_TCG_GET_AES_KEY_NUM()                         (M_TCG_CHECK_OPAL() ? TCG_OPAL_AES_KEY_NUM : TCG_PYRITE_AES_KEY_NUM)
#define M_TCG_GET_LOCKING_SP_ADMIN_NUM()                (M_TCG_CHECK_OPAL() ? TCG_OPAL_LOCKING_SP_ADMIN_NUM : TCG_PYRITE_LOCKING_SP_ADMIN_NUM)
#define M_TCG_GET_LOCKING_SP_USER_NUM()                 (M_TCG_CHECK_OPAL() ? TCG_OPAL_LOCKING_SP_USER_NUM : TCG_PYRITE_LOCKING_SP_USER_NUM)
#define M_TCG_GET_LOCKING_SP_DATASTORE_NUM()            (M_TCG_CHECK_OPAL() ? TCG_OPAL_LOCKING_SP_DATASTORE_NUM : TCG_PYRITE_LOCKING_SP_DATASTORE_NUM)
#define M_TCG_GET_LOCKING_SP_MBR_TABLE_IDX()            (M_TCG_CHECK_OPAL() ? TCG_OPAL_LOCKING_SP_TABLE_MBR_TABLE_IDX : TCG_PYRITE_LOCKING_SP_TABLE_MBR_TABLE_IDX)
#define M_TCG_GET_LOCKING_SP_DATASTORE_TABLE_IDX()      (M_TCG_CHECK_OPAL() ? TCG_OPAL_LOCKING_SP_TABLE_DATASTORE_TABLE_IDX : TCG_PYRITE_LOCKING_SP_TABLE_DATASTORE_TABLE_IDX)
#define M_TCG_GET_ADMIN_SP_AUTHORITY_TABLE_LENGTH()     ((M_TCG_CHECK_OPAL() ? TCG_OPAL_ADMIN_SP_AUTHORITY_TABLE_INCLUDE_PSID_LENGTH : TCG_PYRITE_ADMIN_SP_AUTHORITY_TABLE_INCLUDE_PSID_LENGTH) - (M_TCG_SUPPORT_PSID_FEATURE() ? 0 : TCG_ADMIN_SP_AUTHORITY_TABLE_PSID_LENGTH))
#define M_TCG_GET_ADMIN_SP_CPIN_TABLE_LENGTH()          ((M_TCG_CHECK_OPAL() ? TCG_OPAL_ADMIN_SP_C_PIN_TABLE_INCLUDE_PSID_LENGTH : TCG_PYRITE_ADMIN_SP_C_PIN_TABLE_INCLUDE_PSID_LENGTH) - (M_TCG_SUPPORT_PSID_FEATURE() ? 0 : TCG_ADMIN_SP_C_PIN_TABLE_PSID_LENGTH))
#define M_TCG_GET_LOCKING_SP_AUTHORITY_TABLE_LENGTH()   (M_TCG_CHECK_OPAL() ? TCG_OPAL_LOCKING_SP_AUTHORITY_TABLE_LENGTH : TCG_PYRITE_LOCKING_SP_AUTHORITY_TABLE_LENGTH)
#define M_TCG_GET_LOCKING_SP_TABLE_TABLE_LENGTH()       (M_TCG_CHECK_OPAL() ? TCG_OPAL_LOCKING_SP_TABLE_TABLE_LENGTH : TCG_PYRITE_LOCKING_SP_TABLE_TABLE_LENGTH)
#define M_TCG_GET_LOCKING_SP_METHOD_TABLE_LENGTH()      (M_TCG_CHECK_OPAL() ? TCG_OPAL_LOCKING_SP_METHOD_TABLE_LENGTH : TCG_PYRITE_LOCKING_SP_METHOD_TABLE_LENGTH)
#define M_TCG_GET_LOCKING_SP_ACE_TABLE_LENGTH()         (M_TCG_CHECK_OPAL() ? TCG_OPAL_LOCKING_SP_ACE_TABLE_LENGTH : TCG_PYRITE_LOCKING_SP_ACE_TABLE_LENGTH)
#define M_TCG_GET_LOCKING_SP_LOCKING_TABLE_LENGTH()     (M_TCG_CHECK_OPAL() ? TCG_OPAL_LOCKING_SP_LOCKING_TABLE_LENGTH : TCG_PYRITE_LOCKING_SP_LOCKING_TABLE_LENGTH)
#define M_TCG_CHECK_TABLE_IDX_ORIGINAL(ulTableIdx)  (((ulTableIdx) >= TcgFlashTableOriginAdmin1Index) && ((ulTableIdx) <= TcgFlashTableOriginLocking9Index))
#define M_TCG_CHECK_TABLE_IDX_SPARE(ulTableIdx)     (((ulTableIdx) >= TcgFlashTableSpareAdmin1Index) && ((ulTableIdx) <= TcgFlashTableSpareLocking9Index))
#define M_TCG_GET_SP_TABLE_ORIGINAL_IDX(ulTableIdx) ((ulTableIdx) - ((TRUE == M_TCG_CHECK_TABLE_IDX_SPARE((ulTableIdx))) ? OPAL_SP_TABLE_FLASH_CNT : 0))
#define M_TCG_CLEAR_LAST_METHOD_ID()   (gTcg.uwLastMethodID = 0)   // Should clear last method ID at the tcg flow initial state
#define M_TCG_CLEAR_LAST_INFO_ID()     (gTcg.uwLastInfoID = 0)     // Should clear last info ID at the tcg flow done state
#define M_TCG_CLEAR_LAST_INVOKING_UID_H()  (gTcg.ulLastInvokingUidH = 0)  // Should clear last invoking UID at the tcg send flow initial state

#define M_TCG_ADMIN_SP_SID_EKEK_IDX() 					(M_TCG_GET_LOCKING_SP_CPIN_TABLE_LENGTH())
#define M_TCG_ADMIN_SP_SID_EPASSWORD_IDX() 				(M_TCG_GET_LOCKING_SP_CPIN_TABLE_LENGTH())
#define M_TCG_ADMIN_SP_ADMIN1_EPASSWORD_IDX() 			(M_TCG_GET_LOCKING_SP_CPIN_TABLE_LENGTH() + 1)
#define M_TCG_ADMIN_SP_PSID_EPASSWORD_IDX() 			(M_TCG_GET_LOCKING_SP_CPIN_TABLE_LENGTH() + 2)

#if PS5013_EN
#define M_TCG_CHECK_GLOBAL_RANGE_WRITE_LOCK(NSID)		(M_SECURITY_GET_GLOBAL_RANGE_WRITE_LOCK(NSID))
#define M_TCG_CHECK_AES_RANGE_READ_LOCK(ubRangeIdx)		(M_SECURITY_GET_RANGE_READ_LOCK(ubRangeIdx))
#define M_TCG_CHECK_AES_RANGE_WRITE_LOCK(ubRangeIdx)	(M_SECURITY_GET_RANGE_WRITE_LOCK(ubRangeIdx))
#define M_TCG_GET_AES_RANGE_START_LCA(ubRangeIdx)		(M_SECURITY_GET_AES_RANGE_START_LCA(ubRangeIdx))
#define M_TCG_GET_AES_RANGE_END_LCA(ubRangeIdx)			(M_SECURITY_GET_AES_RANGE_END_LCA(ubRangeIdx))
#define M_TCG_GET_GLOBAL_RANGE_MBR_END_LCA(NSID)		(M_SECURITY_GET_AES_RANGE_MBR_END_LCA(NSID))
#else /* PS5013_EN */
#define M_TCG_CHECK_GLOBAL_RANGE_WRITE_LOCK(NSID)		(M_APU_CHECK_AES_GLOBAL_RANGE_WRITE_LOCK(NSID))
#define M_TCG_CHECK_AES_RANGE_READ_LOCK(ubRangeIdx)		(M_APU_CHECK_AES_RANGE_READ_LOCK(ubRangeIdx))
#define M_TCG_CHECK_AES_RANGE_WRITE_LOCK(ubRangeIdx)	(M_APU_CHECK_AES_RANGE_WRITE_LOCK(ubRangeIdx))
#define M_TCG_GET_AES_RANGE_START_LCA(ubRangeIdx)		(M_APU_GET_AES_RANGE_START_LCA(ubRangeIdx))
#define M_TCG_GET_AES_RANGE_END_LCA(ubRangeIdx)			(M_APU_GET_AES_RANGE_END_LCA(ubRangeIdx))
#define M_TCG_GET_GLOBAL_RANGE_MBR_END_LCA(NSID)		(M_APU_GET_AES_GLOBAL_RANGE_MBR_END_LCA(NSID))
#endif /* PS5013_EN */

#if (TCG_EN && (PS5013_EN || PS5021_EN || (PS5017_EN && !BURNER_MODE_EN)) && !RDT_MODE_EN && !LPM3_LOADER && !BOOTLOADER_MODE_EN)
//AOM_TCG_BUFFER-----------------------------------------------------------------------------------------------------------------//
AOM_TCG_BUFFER void TcgAllocateManagersAndTcgVTBuf(void);
AOM_TCG_BUFFER void TcgFreeManagersAndTcgVTBuf(void);
AOM_TCG_BUFFER void TcgSaveTcgVT(void);
AOM_TCG_BUFFER void TcgLoadTcgVT(void);
AOM_TCG_BUFFER void TcgSetOpalTableEventBuffer(U32 ulEvent, U32 ulTargetFlashIdx, U32 ulTargetAddr);
AOM_TCG_BUFFER void TcgSetupVariablManagerAndBufferManager(void);

//AOM_TCG_ONCE-----------------------------------------------------------------------------------------------------------------//
AOM_TCG_ONCE void TcgSetOpalTableEvent(U32 ulEvent, U32 ulTargetFlashIdx, U32 ulTargetAddr);
AOM_TCG_ONCE void TcgRandomNumGenerator(U8 *pubDestination, U32 ulLength);

AOM_TCG_ONCE void TcgPBKDF2(U8 *pubPassword, U32 ulPasswordLength, U32 ulDkLength, U32 ulIteration, U8 *pubOutput, U8 ubSaltPSIDMode);
AOM_TCG_ONCE void PBKDF2(U8 *pubPassword, U32 ulPasswordLength, U8 *pubSalt, U32 ulSaltLength, U32 ulDkLength, U32 ulIteration, U8 *pubOutput);
AOM_TCG_ONCE void TcgHmac(U8 ubShaType, U8 *pubKey, U32 ulKeyLength, U8 *pubInput, U32 ulInputLength, U8 *pubOutput);

AOM_TCG_RECEIVE void TcgSetAce(U32 ulTablePtr, U32 ulColumn, U32 VariableIndex);
AOM_TCG_RECEIVE void TcgSetAuthority(U32 ulTablePtr, U32 ulColumn, U32 VariableIndex);
AOM_TCG_RECEIVE void TcgSetCPin(U32 ulTablePtr, U32 ulColumn, U32 VariableIndex);
AOM_TCG_RECEIVE void TcgSetTperInfo(U32 ulTablePtr, U32 ulColumn, U32 VariableIndex);
AOM_TCG_RECEIVE void TcgSetLocking(U32 ulTablePtr, U32 ulColumn, U32 VariableIndex);
AOM_TCG_RECEIVE void TcgSetMBRControl(U32 ulTablePtr, U32 ulColumn, U32 VariableIndex);
AOM_TCG_RECEIVE void TcgSetDataRemovalMechanism(U32 ulTablePtr, U32 ulColumn, U32 VariableIndex);

AOM_TCG_RECEIVE U8 TcgCheckLockingRangeOverlap(U32 ulTablePtr);
AOM_TCG_RECEIVE U8 TcgCheckLockingRangeAlign(U32 ulTablePtr);
AOM_TCG_RECEIVE U8 TcgCheckLockingRangeExceedUserRange(U32 ulTablePtr);
AOM_TCG_RECEIVE void TcgRecoverLockingRangeStartAndLBACnt(LockingTableElementPtr_t pLockingTable);

AOM_TCG_RECEIVE U8 *TcgGetTableResponse(U8 *pubBuffer, U32 ulTablePtr, U32 ulColumn);
AOM_TCG_RECEIVE U8 *TcgGetSpInfoResponse(U8 *pubBuffer, U32 ulTablePtr, U32 ulColumn);
AOM_TCG_RECEIVE U8 *TcgGetSpTemplatesResponse(U8 *pubBuffer, U32 ulTablePtr, U32 ulColumn);
AOM_TCG_RECEIVE U8 *TcgGetMethodResponse(U8 *pubBuffer, U32 ulTablePtr, U32 ulColumn);
AOM_TCG_RECEIVE U8 *TcgGetAceResponse(U8 *pubBuffer, U32 ulTablePtr, U32 ulColumn);
AOM_TCG_RECEIVE U8 *TcgGetAuthorityResponse(U8 *pubBuffer, U32 ulTablePtr, U32 ulColumn);
AOM_TCG_RECEIVE U8 *TcgGetCPinResponse(U8 *pubBuffer, U32 ulTablePtr, U32 ulColumn);
AOM_TCG_RECEIVE U8 *TcgGetSecretProtectResponse(U8 *pubBuffer, U32 ulTablePtr, U32 ulColumn);
AOM_TCG_RECEIVE U8 *TcgGetTperInfoResponse(U8 *pubBuffer, U32 ulTablePtr, U32 ulColumn);
AOM_TCG_RECEIVE U8 *TcgGetTemplateResponse(U8 *pubBuffer, U32 ulTablePtr, U32 ulColumn);
AOM_TCG_RECEIVE U8 *TcgGetSpResponse(U8 *pubBuffer, U32 ulTablePtr, U32 ulColumn);
AOM_TCG_RECEIVE U8 *TcgGetLockingInfoResponse(U8 *pubBuffer, U32 ulTablePtr, U32 ulColumn);
AOM_TCG_RECEIVE U8 *TcgGetLockingResponse(U8 *pubBuffer, U32 ulTablePtr, U32 ulColumn);
AOM_TCG_RECEIVE U8 *TcgGetMBRControlResponse(U8 *pubBuffer, U32 ulTablePtr, U32 ulColumn);
AOM_TCG_RECEIVE U8 *TcgGetKeyAes256Response(U8 *pubBuffer, U32 ulTablePtr, U32 ulColumn);
AOM_TCG_RECEIVE U8 *TcgGetDataRemovalMechanismResponse(U8 *pubBuffer, U32 ulTablePtr, U32 ulColumn);

AOM_TCG_SEND U8 TcgPropertyCheckGrammar(U32 ulTokenIndex);
AOM_TCG_SEND U8 TcgStartSessionCheckGrammar(U32 ulTokenIndex);
AOM_TCG_SEND U8 TcgGetAclCheckGrammar(U32 ulTokenIndex);
AOM_TCG_SEND U8 TcgGetParseCellBlock(U32 *pulTokenIndex);
AOM_TCG_SEND U8 TcgGetParameterErrorHandle(void);
AOM_TCG_SEND U8 TcgNextCheckGrammar(U32 ulTokenIndex);
AOM_TCG_SEND void TcgGenKeyCheckGrammar(U32 ulTokenIndex);
AOM_TCG_SEND U8 TcgRevertSpCheckGrammar(U32 ulTokenIndex);
AOM_TCG_SEND void TcgRevertCheckGrammar(U32 ulTokenIndex);
AOM_TCG_SEND void TcgActivateCheckGrammar(U32 ulTokenIndex);
AOM_TCG_SEND void TcgAuthenticateCheckGrammar(U32 ulTokenIndex);
AOM_TCG_SEND void TcgGetCheckGrammar(U32 ulTokenIndex);
AOM_TCG_SEND U8 TcgSetCheckGrammar(U32 ulTokenIndex);
AOM_TCG_SEND void TcgRandomCheckGrammar(U32 ulTokenIndex);

AOM_TCG_SEND void TcgEraseCheckGrammar(U32 ulTokenIndex);
AOM_TCG_SEND U8 TcgActivateGetOptionParameter(U32 *pulTokenIndex);
AOM_TCG_SEND U8 TcgReactivateCheckGrammar(U32 ulTokenIndex);
AOM_TCG_SEND U8 TcgActivateReactivateGetDataStoreTableSizeList(U32 *pulTokenIndex, U32 ulMethodUidL);
AOM_TCG_SEND U8 TcgActivateReactivateGetSelectedLockingRangeList(U32 *pulTokenIndex, U32 ulMethodUidL);
AOM_TCG_SEND U8 TcgCheckTokenData(TcgSimpleTokenType_t SimpleTokenType, U32 ulTokenValue, U32 ulTokenIndex);//[To be modified] integrate this function into tcg_chekc_token_simple
AOM_TCG_SEND U8 TcgCheckSimpleTokenData(U32 ulTokenValue, U32 ulTokenIndex);
AOM_TCG_SEND U8 TcgCheckListToken(U32 ulTokenValue, U32 ulTokenIndex);

AOM_TCG_RECEIVE void TcgCloseSessionFillBuffer(U8 **ppubBuffer);
AOM_TCG_RECEIVE U8 *TcgSyncSessionFillBuffer(void);
AOM_TCG_RECEIVE U8 *TcgGetAclFillBuffer(void);
AOM_TCG_RECEIVE U8 *TcgSimpleFillBuffer(void);
AOM_TCG_RECEIVE U8 *TcgAuthenticateFillBuffer(void);
AOM_TCG_RECEIVE U8 *TcgRandomFillBuffer(void);
AOM_TCG_RECEIVE U8 *TcgPropertyFillBuffer(void);
AOM_TCG_RECEIVE U8 *TcgEncodeString(TcgEncodeString_t EncodeStringType, U8 *pubBuffer, U8 *pubString, U32 ulStringLength, U32 ulValue);
AOM_TCG_RECEIVE U8 *TcgEncodeValueWithLength(U8 *pubBuffer, U64 uoValue, U32 ulLength);
AOM_TCG_RECEIVE U8 *TcgEncodeUid(U8 *pubBuffer, U32 ulUidH, U32 ulUidL);
AOM_TCG_RECEIVE U8 *TcgEncodeValue(U8 *pubBuffer, U32 ulValue);

AOM_TCG_ONCE void TcgGenerateAllDek(U8 ubKeepGlobalRangeKey);
AOM_TCG_ONCE void TcgGenerateDek(U32 ulLockingUidL);
AOM_TCG_ONCE void TcgGenerateKek(void);
AOM_TCG_ONCE void TcgGenerateAllKekRange(void);
AOM_TCG_ONCE void TcgGenerateKekRange(U32 ulKeyIndex);

AOM_TCG_RECEIVE U32 TcgGetTableElementPtr(U32 ulSpUidL, U32 ulInvokingUidH, U32 ulInvokingUidL);
AOM_TCG_RECEIVE U32 TcgGetOpalTableFlashIndexByUid(U32 ulSpUidL, U32 ulInvokingUidH, U32 ulInvokingUidL);
AOM_TCG_RECEIVE U32 TcgGetOpalTableFlashIndexByAceTableIndex(U32 ulTableIndex);

AOM_TCG_COMMON void TcgEncryptAllDekToEdek(U8 ubKeepGlobalRangeKey, U8 ubRange, U8 ubDontCareLockOnResetBMP);
AOM_TCG_COMMON void TcgEncryptDekToEdek(U32 ulLockingUidL, U8 ubRange, U8 ubDontCareLockOnResetBMP);
AOM_TCG_SEND void TcgDecryptEdekToDek(U32 ulLockingUidL, U8 ubRange, U8 ubDontCareLockOnResetBMP);
AOM_TCG_COMMON void TcgEncryptAllKekRangeToEkekRange(U8 ubDontCareLockOnResetBMP);
AOM_TCG_COMMON void TcgEncryptKekRangeToEkekRange(U8 Ubkey_index, U8 ubDontCareLockOnResetBMP);
AOM_TCG_ONCE void TcgKeyWrap(U32 *pulKeyEncryptionKey, U8 *pubEncryptedKeyData, U8 *pubKeyData);
AOM_TCG_SEND U8 TcgKeyUnwrap(U32 *pulKeyEncryptionKey, U8 *pubEncryptedKeyData, U8 *pubKeyData, U8 ubMode);
AOM_TCG_ONCE void TcgEncryptKeyData(U8 *pubPin, U32 ulPinLength, U8 *pubKeyEncryptionKey, U8 *pubEncryptedKeyData, U8 *pubKeyData, U8 ubMode);
AOM_TCG_SEND U8 TcgDecryptKeyData(U8 *pubPin, U32 ulPinLength, U8 *pubKeyEncryptionKey, U8 *pubEncryptedKeyData, U8 *pubKeyData, U8 ubMode);
AOM_TCG_ONCE void TcgPyrite1SecurityKeyProtectDek(U8 ubAESDirection);
AOM_TCG_COMMON void TcgEncryptKekandPasswordWhenInit(U8 ubMode, U8 ubEkekIdx);
AOM_TCG_ONCE U32 TcgKeyIndexToLockingUidL(U8 ubKeyIndex);
AOM_TCG_ONCE U8 TcgLockingUidLToKeyIndex(U32 ulLockingUidL);
AOM_TCG_ONCE U32 TcgForceLBAddressLBID(U8 ubLBID, U32 ulAddr);
AOM_TCG_RECEIVE void TcgSortLockingRange(LockingTableElementPtr_t pLockingTable, U8 *pubSortedNum, U8 *pubSortedIndex);
AOM_TCG_RECEIVE void TcgAddTrimCntWithValidTrimLength(U8 *pubTrimCount, U64 uoTrimLength);
AOM_TCG_ONCE void TcgSetTransactionAndRegisterSaveVT(U8 ubValue);
AOM_TCG_ONCE void TcgSetShadowMBRAndRegisterSaveVT(U8 ubValue);
AOM_TCG_SEND U8 TcgCheckTwoRangeOverlap(U32 ulStart1, U32 ulLength1, U32 ulStart2, U32 ulLength2);
AOM_TCG_SEND U8 TcgCheckTrimRangeOverlapWriteLockedRange(TrimRange_t *pTrimRange, U8 ubRangeIdx);
AOM_TCG_ONCE void TcgSwitchOpalTableBufferToDRBG(void);
AOM_TCG_ONCE U8 TcgGetLCACnt(U32 ulLength);
AOM_TCG_RECEIVE void TcgSetLockingCheck(void);
AOM_TCG_SEND U8 TcgBlockSidAuthentication(void);
AOM_TCG_COMMON void TcgTableDamagedHandler(void);
AOM_TCG_COMMON void TcgAddDrivelog(TcgEventDrivelogReason_t ulDrivelogReason, U16 uwEventID, U8 ubPayload);
AOM_TCG_COMMON void TcgAddUNCDrivelog(void);
AOM_TCG_COMMON void TcgAddRepairDrivelog(void);
AOM_TCG_INIT U8 TcgGetInitFlowReason(void);
AOM_TCG_COMMON void TcgForceSetSupportedFeature(void);
//AOM_TCG_ONCE-----------------------------------------------------------------------------------------------------------------//

//AOM_TCG_RECEIVE-----------------------------------------------------------------------------------------------------------------//
AOM_TCG_RECEIVE U8 TcgCheckStablePayLoadOutStanding(U32 *pulOutStandingBufferEndAddr);
AOM_TCG_RECEIVE void TcgSaveOpalTable(void);
AOM_TCG_RECEIVE void TcgSaveSpTable(void);
AOM_TCG_RECEIVE void TcgSaveMBRTable(void);
AOM_TCG_RECEIVE void TcgSaveDataStoreTable(void);
AOM_TCG_RECEIVE void TcgTrimDekChangedRanges(U8 ubTrimMode);
AOM_TCG_RECEIVE void TcgTrimRangeChangedRanges(void);
AOM_TCG_RECEIVE void TcgSetShadowMBR(void);
AOM_TCG_COMMON void TcgSetLockOnResetKey(void);
AOM_TCG_RECEIVE void TcgSetLockingRangeAndKey(U8 ubMode, U8 ubRange, U8 ubRangeIndex);
AOM_TCG_RECEIVE void TcgSyncSpTable(U8 ubDirection);
AOM_TCG_RECEIVE void TcgMehtodInvokingSecurityReceive(void);
AOM_TCG_RECEIVE void TcgStartTransactionSecurityReceive(void);
AOM_TCG_RECEIVE void TcgEndTransactionSecurityReceive(void);
AOM_TCG_RECEIVE U32 TcgGetUnlockedLockingRange(void);
AOM_TCG_RECEIVE void TcgSessionManagementMethodInvokingSecurityReceive(void);
AOM_TCG_RECEIVE void TcgSimpleMethodInvokingSecurityReceive(void);
AOM_TCG_RECEIVE void TcgSyncSessionSecurityReceive(void);
AOM_TCG_RECEIVE void TcgPropertySecurityReceive(void);
AOM_TCG_RECEIVE void TcgGetAclSecurityReceive(void);
AOM_TCG_RECEIVE void TcgNextSecurityReceive(void);
AOM_TCG_RECEIVE U32 TcgNextGetUidLByTableIndex(U8 ubIndex);
AOM_TCG_RECEIVE void TcgGenKeySecurityReceive(void);
AOM_TCG_REVERT void TcgMakeSureKeyRangeProper(U32 ulLockingUidL, U8 ubMode);
AOM_TCG_REVERT void TcgRevertSpSecurityReceive (void);
AOM_TCG_REVERT void TcgTrimByteTable(U8 ubByteTable);//[To be modified] Modify the input parameter to ulLockingUidl
AOM_TCG_REVERT void TcgRevertSecurityReceive(void);
AOM_TCG_RECEIVE void TcgActivateSecurityReceive(void);
AOM_TCG_RECEIVE void TcgSingleUserModeEnable(U32 ulMethodUidL);
AOM_TCG_RECEIVE void TcgReactivateSecurityReceive(void);
AOM_TCG_RECEIVE void TcgEraseSecurityReceive(void);
AOM_TCG_RECEIVE void TcgActivateReactivateSetAdditionalDataStoreAndSingleUserModeFeatureInTableTable(U32 ulMethodUidL);
AOM_TCG_RECEIVE void TcgAuthenticateSecurityReceive(void);
AOM_TCG_RECEIVE void TcgGetSecurityReceive(void);
AOM_TCG_RECEIVE U8 *TcgGetFillByteTableResponse(U8 *pubBuffer);
AOM_TCG_RECEIVE U8 *TcgGetFillObjectTableResponse(U8 *pubBuffer);
AOM_TCG_RECEIVE void TcgSetSecurityReceive(void);
AOM_TCG_RECEIVE void TcgSetCPinKeyProtection(void);
AOM_TCG_RECEIVE void TcgRandomSecurityReceive(void);
AOM_TCG_RECEIVE void TcgSecurityReceiveSecurityProtocol2(void);
AOM_TCG_RECEIVE void TcgGetComId(void);
AOM_TCG_RECEIVE void TcgGetComIdResponse(void);
AOM_TCG_RECEIVE void TcgSecurityReceiveSecurityProtocol1(void);
AOM_TCG_RECEIVE void TcgEndSessionSecurityReceive(void);
AOM_TCG_RECEIVE void TcgLevel0Discovery(void);

AOM_TCG_REVERT U32 TcgGetTableIndex(TcgSpUidL_t ulSpUidL, TcgTableUidL_t ulUidH, U32 ulUidL);
AOM_TCG_REVERT U32 TcgGetTableIndexAssert(TcgSpUidL_t ulSpUidL, TcgTableUidL_t ulUidH, U32 ulUidL);
AOM_TCG_REVERT void TcgInitAdminSp(void);
AOM_TCG_REVERT void TcgInitLockingSp(U8 ubMode);
AOM_TCG_REVERT AceTableElementPtr_t TcgGetLockingSpAceTableElementPtr(U32 ulTableIndex, U32 ulBufferAddr);

AOM_TCG_RECEIVE U32 TcgGetDataSubpacketPaddingZeroLength(U32 ulDataLength);
AOM_TCG_RECEIVE void TcgSetPacketLength(U32 ulTotalDataLength, U32 *pulCompacketLength, U32 *pulPacketLength, U32 *pulSubpacketLength);
AOM_TCG_RECEIVE void TcgSetBufferHeader(U32 ulBufferEndAddr, U8 ubCaseBMP);
AOM_TCG_RECEIVE U8 TcgReadWriteLockingSpLifeCycle(U8 ubRW, U32 ulMethodUidL);
AOM_TCG_RECEIVE DataStoreShiftAndSize_t TcgCalculateDataStoreSizeAndShift(U32 ulInvokingUidH);
AOM_TCG_RECEIVE void TcgSetOpalTableEventSecurityReceive(U32 ulEvent, U32 ulTargetFlashIdx, U32 ulTargetAddr);
AOM_TCG_REVERT void TcgDecryptUnlockEnableDek(U8 ubDontCareLockOnResetBMP);
AOM_TCG_RECEIVE void TcgIfReceiveSetCompacket(U8 ubReturnType);
AOM_TCG_RECEIVE U16 TcgGetOverWriteEraseTime(U32 ulDiskSizeInMB);
AOM_TCG_RECEIVE U16 TcgGetBlockEraseTime(U32 ulDiskSizeInMB);

AOM_TCG_RECEIVE void TcgSetAESRangeLBAandKeyIdx(U8 ubIdx, U64 uoStartLBA, U64 uoEndLBA, U32 ulKeyIdx);
AOM_TCG_RECEIVE void TcgSetAESRangeReadLock(U8 ubIdx, U8 ubReadLock);
AOM_TCG_RECEIVE void TcgSetAESRangeWriteLock(U8 ubIdx, U8 ubWriteLock);
AOM_TCG_RECEIVE void TcgSetGlobalRangeKeyIdx(U32 ulNSID, U32 ulKeyIdx);
AOM_TCG_RECEIVE void TcgSetGlobalRangeReadLock(U32 ulNSID, U8 ubReadLock);
AOM_TCG_RECEIVE void TcgSetGlobalRangeWriteLock(U32 ulNSID, U8 ubWriteLock);
AOM_TCG_RECEIVE void TcgSetMBRRangeLBAAndCmdSync(U32 ulNSID, U64 uoEndLBA);
//AOM_TCG_RECEIVE-----------------------------------------------------------------------------------------------------------------//

//AOM_TCG_SEND-----------------------------------------------------------------------------------------------------------------//
AOM_TCG_SEND void TcgSecuritySendSecurityProtocol1(void);
AOM_TCG_SEND void TcgSecuritySendSecurityProtocol2(void);
AOM_TCG_SEND U8 TcgHandleComIdRequest(void);
AOM_TCG_SEND void TcgStackReset(void);
AOM_TCG_SEND U8 TcgTperReset(void);
AOM_TCG_SEND U32 TcgResetReadWriteLock(TcgReset_t ResetType);
AOM_TCG_SEND void TcgDecodeBufferSecuritySend(void);
AOM_TCG_SEND U8 TcgCheckHeader(void);
AOM_TCG_SEND U8 TcgParseToken(void);
AOM_TCG_SEND U32 TcgGetToken(U8 *pubToken);
AOM_TCG_SEND void TcgMethodInvokingSecuritySend(void);
AOM_TCG_SEND U8 TcgCheckHeaderAndGetInvokingUidLAndMethodUidL(U32 *pulTokenIndex);
AOM_TCG_SEND void TcgEndSessionSecuritySend(void);
AOM_TCG_SEND void TcgStartTransactionSecuritySend(void);
AOM_TCG_SEND void TcgEndTransactionSecuritySend(void);
AOM_TCG_SEND U8 TcgGetInvokingUid(U32 *pulTokenIndex);
AOM_TCG_SEND U8 TcgGetMethodUid(U32 *pulTokenIndex);
AOM_TCG_SEND U8 TcgCheckHeaderWithTperProperty(void);
AOM_TCG_SEND void TcgSessionManagementMehotdInvokingSecuritySend(U32 ulTokenIndex);
AOM_TCG_SEND void TcgSimpleMethodInvokingSecuritySend(U32 ulTokenIndex);
AOM_TCG_SEND void TcgPropertySecuritySend(U32 ulTokenIndex);
AOM_TCG_SEND void TcgPropertySetHostProperty(void);
AOM_TCG_SEND void TcgPropertyHostPropertyErrorHandle(void);
AOM_TCG_SEND U8 TcgCheckRestPayLoadToken(void);
AOM_TCG_SEND U8 TcgCheckAcl(U32 ulInvokingUidH, U32 ulInvokingUidL, TcgMethodUidL_t ulMethodUidL);
AOM_TCG_SEND void TcgGetAdminSpAcl(U32 ulInvokingUidH, U32 ulInvokingUidL, TcgMethodUidL_t ulMethodUidL);
AOM_TCG_SEND void TcgGetLockingSpAcl(U32 ulInvokingUidH, U32 ulInvokingUidL, TcgMethodUidL_t ulMethodUidL);
AOM_TCG_SEND void TcgAddAcl(TcgAceUidL_t ulAceUidL);
AOM_TCG_SEND void TcgGetAclSecuritySend(U32 ulTokenIndex);
AOM_TCG_SEND void TcgNextSecuritySend(U32 ulTokenIndex);
AOM_TCG_SEND void TcgNextParameterErrorHandle(void);
AOM_TCG_SEND void TcgStartSessionSecuritySend(U32 ulTokenIndex);
AOM_TCG_SEND void TcgStartSessionSetSessionProperty(void);
AOM_TCG_SEND U8 TcgStartSessionParameterErrorHandle(void);
AOM_TCG_SEND U8 TcgAuthenticateAuthority(TcgSpUidL_t ulSpUidL, TcgAuthorityUidL_t ulAuthorityUidL, U8 *ubChallenge, U32 ulChallengeLength);
AOM_TCG_SEND void TcgGenKeySecuritySend(U32 ulTokenIndex);
AOM_TCG_SEND void TcgRevertSpSecuritySend (U32 ulTokenIndex);
AOM_TCG_SEND void TcgRevertSpCheckGlobalRangeReadWriteLock(void);
AOM_TCG_SEND void TcgRevertSecuritySend(U32 ulTokenIndex);
AOM_TCG_SEND void TcgActivateSecuritySend(U32 ulTokenIndex);
AOM_TCG_SEND void TcgReactivateSecuritySend(U32 ulTokenIndex);
AOM_TCG_SEND void TcgReactivateCheckAllReadWriteLockEnable(void);
AOM_TCG_SEND void TcgEraseSecuritySend(U32 ulTokenIndex);
AOM_TCG_SEND void TcgAuthenticateSecuritySend(U32 ulTokenIndex);
AOM_TCG_SEND void TcgGetSecuritySend(U32 ulTokenIndex);
AOM_TCG_SEND void TcgGetSetNextFillMaxRowAndColumn(U32 ulMethodUidL);
AOM_TCG_SEND void TcgSetSecuritySend(U32 ulTokenIndex);
AOM_TCG_SEND void TcgSetParameterErrorHandle(void);
AOM_TCG_SEND void TcgRandomSecuritySend(U32 ulTokenIndex);

AOM_TCG_SEND void TcgRestoreSp(void);
AOM_TCG_RECEIVE void TcgCheckSessionTimeout(void);
AOM_TCG_RECEIVE U64 TcgGetSessionWaitTime(void);
AOM_TCG_RECEIVE void TcgResetSessionTimer(void);

AOM_TCG_SEND void TcgInitComIdProperty(void);
AOM_TCG_SEND void TcgInitTperProperty(void);
AOM_TCG_SEND void TcgInitHostProperty(void);
AOM_TCG_SEND void TcgInitSessionProperty(void);

AOM_TCG_SEND void TcgMBRControlDoneOnReset(TcgReset_t ResetType);
AOM_TCG_SEND void TcgCloseSessionSecuritySend(void);
AOM_TCG_RECEIVE U8 TcgCheckAceColumn(U32 ulColumnIndex);

AOM_TCG_SEND void TcgSetOpalTableEventSecuritySend(U32 ulEvent, U32 ulTargetFlashIdx, U32 ulTargetAddr);
AOM_TCG_SEND U8 TcgGetDataRemovalMechanism(void);
//AOM_TCG_SEND-----------------------------------------------------------------------------------------------------------------//

//AOM_TCG_INIT-----------------------------------------------------------------------------------------------------------------//
AOM_TCG_INIT void TcgResetCPinTries(U32 ulSpUidL);
AOM_TCG_INIT U8 TcgCheckOpalFlashTable(void);
AOM_TCG_INIT void TcgDecryptAllEdekToDek(U8 ubKeepGlobalRangeKey, U8 ubRange, U8 ubDontCareLockOnResetBMP);
AOM_TCG_INIT void TcgDecryptAllEkekRangetoKekRange(U8 ubDontCareLockOnResetBMP);
AOM_TCG_INIT void TcgDecryptEkekRangeToKekRange(U32 ulKeyIndex, U8 ubDontCareLockOnResetBMP);
AOM_TCG_INIT void TcgGetNonVolatileTcgVT(void);
AOM_TCG_INIT void TcgBufManagerResetCmdInfo();
AOM_TCG_INIT void TcgLoadWriteProtectTCGTable(void);
AOM_TCG_INIT void TcgUpdateSecurityVersion(void);
AOM_TCG_INIT void TcgUpdateSecurityVersion3to4(void);

//AOM_TCG_INIT-----------------------------------------------------------------------------------------------------------------//

//No CodeBank
U8 TcgValidVariables(U32 ulSize, void *pulPointer);
U8 TcgVariablesManagerCheckAvailable(U32 ulSize);
void TcgZeroInitialVariables(U32 ulSize);
void TcgPushVariables(U32 ulSize, void *pulPointer);
void TcgInvalidVariables(U32 ulSize, void *pulPointer);
void TcgAllocateManagersAndTcgVTBuf_CallBack(BMUCmdResult_t *pBmuCmdResult);
#endif /* (TCG_EN && (PS5013_EN || PS5021_EN || (PS5017_EN && !BURNER_MODE_EN)) && !RDT_MODE_EN && !LPM3_LOADER && !BOOTLOADER_MODE_EN) */

#if (TCG_EN && !LPM3_LOADER)
AOM_TCG_ONCE void TcgSha(U8 *pubSource, U8 *pubDestination, U32 ulLength, U8 ubSHAMode);
AOM_TCG_INIT void TcgChipIDSHA(U8 *pubDstAddr);
AOM_TCG_INIT void TcgEfuseKeyDerivationFunction(U8 *pubDstAddr);
AOM_TCG_ONCE void TcgKeyDerivationFunctionCounterMode(U8 *pubKDFKey, U32 ulKeyLength, U8 *pubInput, U32 ulInputLength, U8 *pubOutput, U32 ulOutputLength);
#endif /* (TCG_EN && !LPM3_LOADER) */

#endif /* _TCG_H_ */
