#ifndef _RDT_FLASH_PROGRAM_ORDER_H_
#define _RDT_FLASH_PROGRAM_ORDER_H_

#include "typedef.h"
#include "hal/fip/fip.h"
#include "ftl/ftl_api.h"

#define PROGRAM_ORDER_VERIFICATION (0)
#define UNDEFINED_PAGE (0xFFFF)

#if (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)
#if (FW_CATEGORY_FLASH == FLASH_N48R_QLC)
#define FLASH_WL_SCALE (1)
#else
#define FLASH_WL_SCALE (4)
#endif
#elif ((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC) && !(IM_B47R || IM_B37R))
#define FLASH_WL_SCALE (3)
#else
#define FLASH_WL_SCALE (1)
#endif

#define FLASH_BICS4_WL_COUNT (384)

/*
    MICRON B16 page order, page_num:2304
*/
#define FLASH_MICRON_B16_GROUP_0_BEGIN          (0)
#define FLASH_MICRON_B16_GROUP_0_END            (11)
#define FLASH_MICRON_B16_GROUP_1_BEGIN          (12)
#define FLASH_MICRON_B16_GROUP_1_END            (35)
#define FLASH_MICRON_B16_GROUP_2_BEGIN          (36)
#define FLASH_MICRON_B16_GROUP_2_END            (59)
#define FLASH_MICRON_B16_GROUP_3_BEGIN          (60)
#define FLASH_MICRON_B16_GROUP_3_END            (2219)
#define FLASH_MICRON_B16_GROUP_4_BEGIN          (2220)
#define FLASH_MICRON_B16_GROUP_4_END            (2267)
#define FLASH_MICRON_B16_GROUP_5_BEGIN          (2268)
#define FLASH_MICRON_B16_GROUP_5_END            (2291)
#define FLASH_MICRON_B16_GROUP_6_BEGIN          (2292)
#define FLASH_MICRON_B16_GROUP_6_END            (2303)

#define FLASH_MICRON_WITHOUT_PREREAD            (0)

#define RDT_PROGRAM_ORDER_NORMAL_PLANE_MODE      (0)
#define RDT_PROGRAM_ORDER_ONLY_SINGLE_PLANE_MODE (1)
#define RDT_PROGRAM_ORDER_ONLY_MULTI_PLANE_MODE  (2)

#if IM_N48R
#define IM_N48_SECTION_1_RDT	        (8)
#define IM_N48_SECTION_2_RDT	        (1400)
#define IM_N48_SECTION_3_RDT	        (1416)
#define IM_N48_SECTION_4_RDT	        (2808)
#define IM_N48_CELLNUM_SECTION_1_RDT	(4)
#define IM_N48_CELLNUM_SECTION_2_RDT	(352)
#define IM_N48_CELLNUM_SECTION_3_RDT	(360)
#define IM_N48_CELLNUM_SECTION_4_RDT	(708)

#define IM_N48_CELLNUM                  (712)

#elif IM_B47R
#define IM_B47R_SECTION_1_RDT	        (4)
#define IM_B47R_SECTION_2_RDT	        (1048)
#define IM_B47R_SECTION_3_RDT	        (1064)
#define IM_B47R_SECTION_4_RDT	        (2108)
#define IM_B47R_CELLNUM_SECTION_1_RDT	(4)
#define IM_B47R_CELLNUM_SECTION_2_RDT	(352)
#define IM_B47R_CELLNUM_SECTION_3_RDT	(360)
#define IM_B47R_CELLNUM_SECTION_4_RDT	(708)

#define IM_B47R_CELLNUM                  (712)

#elif IM_B37R
#define IM_B37R_SECTION_1_RDT	(4)
#define IM_B37R_SECTION_2_RDT	(760)
#define IM_B37R_SECTION_3_RDT	(776)
#define IM_B37R_SECTION_4_RDT	(1532)
#define IM_B37R_SECTION_5_RDT	(1536)

#define IM_B37R_CELLNUM_SECTION_1_RDT	(4)
#define IM_B37R_CELLNUM_SECTION_2_RDT	(256)
#define IM_B37R_CELLNUM_SECTION_3_RDT	(264)
#define IM_B37R_CELLNUM_SECTION_4_RDT	(516)

#define IM_B37R_CELLNUM                  (520)

#elif ((FW_CATEGORY_FLASH == FLASH_YMTC_EMS_QLC))//ems mst add--karl
#define IM_EMS_SECTION_1_RDT	        (18)
#define IM_EMS_SECTION_2_RDT	        (2706)
#define IM_EMS_SECTION_3_RDT	        (2778)
#define IM_EMS_SECTION_4_RDT	        (5466)
#define IM_EMS_SECTION_5_RDT	        (5520)
#define IM_EMS_CELLNUM_SECTION_1_RDT	(6)
#define IM_EMS_CELLNUM_SECTION_2_RDT	(678)
#define IM_EMS_CELLNUM_SECTION_3_RDT	(702)
#define IM_EMS_CELLNUM_SECTION_4_RDT	(1374)
#define IM_EMS_CELLNUM_SECTION_5_RDT	(1392)

#define IM_EMS_CELLNUM                  (1416)
#define IM_EMS_CELLNUM_SLC				(1392)

#elif (SAMSUNG_FSP_EN)
#if(FW_CATEGORY_FLASH == FLASH_SAMSUNG_V6_TLC)
#define IM_SSV6_SECTION_1_RDT			(16)
#define IM_SSV6_SECTION_2_RDT			(3064)
#define IM_SSV6_CELLNUM_SECTION_1_RDT	(8)
#define IM_SSV6_CELLNUM_SECTION_2_RDT	(1024)
#define IM_SSV6_CELLNUM						(1040)
#define IM_SAMSUNG_CELLNUM					(1040)

#elif(FW_CATEGORY_FLASH == FLASH_SAMSUNG_V6P_TLC)
#define IM_SSV6P_SECTION_1_RDT			(32)
#define IM_SSV6P_SECTION_2_RDT			(3176)
#define IM_SSV6P_CELLNUM_SECTION_1_RDT	(16)
#define IM_SSV6P_CELLNUM_SECTION_2_RDT	(1064)
#define IM_SSV6P_CELLNUM					(1080)
#define IM_SAMSUNG_CELLNUM					(1080)
#define IM_SSV6P_CELLNUM_SLC				(1072)

#elif (FW_CATEGORY_FLASH == FLASH_SAMSUNG_V7_TLC)//Samsung v7 mst add--Reip
#define IM_SSV7_SECTION_1_RDT			(12)
#define IM_SSV7_SECTION_2_RDT			(1434)
#define IM_SSV7_SECTION_3_RDT			(1446)
#define IM_SSV7_SECTION_4_RDT			(1458)
#define IM_SSV7_SECTION_5_RDT			(1470)
#define IM_SSV7_SECTION_6_RDT			(3144)
#define IM_SSV7_SECTION_7_RDT			(3156)
#define IM_SSV7_CELLNUM_SECTION_1_RDT	(6)
#define IM_SSV7_CELLNUM_SECTION_2_RDT	(480)
#define IM_SSV7_CELLNUM_SECTION_3_RDT	(486)
#define IM_SSV7_CELLNUM_SECTION_4_RDT	(498)
#define IM_SSV7_CELLNUM_SECTION_5_RDT	(504)
#define IM_SSV7_CELLNUM_SECTION_6_RDT	(1062)
#define IM_SSV7_CELLNUM_SECTION_7_RDT	(1068)
#define IM_SSV7_CELLNUM					(1080)
#define IM_SAMSUNG_CELLNUM				(1080)

#elif (FW_CATEGORY_FLASH == FLASH_SAMSUNG_V8_TLC)//Samsung v8 mst add--Reip
#define IM_SSV8_SECTION_1_RDT			(12)
#define IM_SSV8_SECTION_2_RDT			(4224)
#define IM_SSV8_SECTION_3_RDT			(4236)
#define IM_SSV8_CELLNUM_SECTION_1_RDT	(6)
#define IM_SSV8_CELLNUM_SECTION_2_RDT	(1410)
#define IM_SSV8_CELLNUM_SECTION_3_RDT	(1416)
#define IM_SSV8_CELLNUM					(1428)
#define IM_SAMSUNG_CELLNUM				(1428)
#elif(FW_CATEGORY_FLASH == FLASH_SAMSUNG_V5_TLC)//Samsung v5 mst add--Jeffrey
#define IM_SSV5_SECTION_1_RDT			(8)
#define IM_SSV5_SECTION_2_RDT			(1088)
#define IM_SSV5_SECTION_3_RDT			(1096)
#define IM_SSV5_CELLNUM_SECTION_1_RDT	(4)
#define IM_SSV5_CELLNUM_SECTION_2_RDT	(364)
#define IM_SSV5_CELLNUM_SECTION_3_RDT	(368)

#define IM_SSV5_CELLNUM					(376)
#define IM_SAMSUNG_CELLNUM				(376)
#endif

#elif(INTEL_FSP_EN)
#if(FW_CATEGORY_FLASH == FLASH_INTEL_N38A_QLC)
#define IM_N38A_SECTION_1_RDT			(32)
#define IM_N38A_SECTION_2_RDT			(160)
#define IM_N38A_SECTION_3_RDT			(2912)
#define IM_N38A_SECTION_4_RDT			(2992)
#define IM_N38A_SECTION_5_RDT			(3024)
#define IM_N38A_SECTION_6_RDT			(3072)

#define IM_N38A_CELLNUM_SECTION_1_RDT	(32)
#define IM_N38A_CELLNUM_SECTION_2_RDT	(720)
#define IM_N38A_CELLNUM_SECTION_3_RDT	(736)
#define IM_N38A_CELLNUM_SECTION_4_RDT	(752)
#define IM_N38A_CELLNUM_SECTION_5_RDT	(768)
#define IM_N38A_CELLNUM_SECTION_6_RDT	(784)

#define IM_N38A_CELLNUM					(2352)
#define IM_INTEL_CELLNUM				(2352)
#endif

#endif

typedef struct {
	S16 swWL;
	S16 swPreviousWL;
	U16 uwStartWL;
	U16 uwEndWL;
	U8 ubPass;
	U8 ubIsSLC;
	U8 ubPlaneMode;
	U8 ubCurrentPlane;
	U8 ubPlaneCount;
	U8 ubIsValidPO;
} ProgramOrder;

#if ((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC) || (FW_CATEGORY_FLASH == FLASH_N18_QLC))
typedef enum {
	L_PAGE,
	U_PAGE,
	X_PAGE,
	T_PAGE
} PageType;

PageType GetPageType(U16 uwPage);
#if (FLASH_MICRON_WITHOUT_PREREAD)
U16 GetMicronLowerPageStart(U16 uwPage);
#endif
#endif

ProgramOrder GetInitialPorgramOrder(U8 ubIsSLC, U16 uwStartWL, U16 uwEndWL);
U8 GetMaxPass(U8 ubIsSLC);
ProgramOrder GetNextProgramOrder(ProgramOrder poProgramOrder);
void PorgramOrderVerification(U8 ubIsSLC, U16 uwStartWL, U16 uwEndWL);
#if (IM_N48R || IM_B47R || IM_B37R || (CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC) || (FW_CATEGORY_FLASH == FLASH_INTEL_N38A_QLC))
U16 GetPageFromCellNumInRDT(U16 uwCellNum, U8 ubLMU);
#elif ((FW_CATEGORY_FLASH == FLASH_YMTC_EMS_QLC))//ems mst add--karl
U16 GetPageFromCellNumInRDT_EMS(U16 uwCellNum, U8 ubLMU);
#endif /*(IM_N48R)*/
#endif
