#include "setup.h"
#include "debug/debug.h"
#include "debug/debug_setup.h"
#include "hal/xzip/xzip_api.h"
#include "hal/apu/apu_api.h"
#include "hal/bmu/bmu_api.h"
#include "hal/bmu/bmu_pop_cmd.h"
#include "hal/nvme/nvme_spec.h"
#include "hal/pic/uart/uart_api.h"
#include "nvme_api/db/shr_hal_db.h"
#include "nvme_api/nvme/shr_hal_nvme_api.h"
#include "host_handler/host_management_api.h"
#include "host_handler/hostevt_errhdl_api.h"
#include "ftl/ftl_xzip_api.h"
#include "init/fw_init.h"
#include "testflow/sim_spor/sim_spor_api.h"

#if SIM_SPOR_EN

SimSPORInfo_t gSimSPORManager = { SIM_SPOR_INVALID_CODE, SIM_SPOR_INVALID_CODE };    // Non-zero data and data segment

U8 SimSPORSecurityVendorPowerHandler(U8 ubIsSecuritySend, U32 ulBufAddr)
{
	SimSPORSecurityVendorInfo_t *pVendorCmd = NULL;
	U8 ubEvent = SIM_SPOR_HOST_EVENT_NUM;
	U8 ubFinish = TRUE;

	// Report status
	pVendorCmd = (SimSPORSecurityVendorInfo_t *)ulBufAddr;

	if (ubIsSecuritySend) {
		// Security Send command
		if (SIM_SPOR_HOST_OPCODE == pVendorCmd->Send.ulOpCode) {

			ubEvent = (U8)pVendorCmd->Send.ulEvent;

			M_UART(SIM_SPOR_, "Host Request Power Event %x\n", ubEvent);

			switch (ubEvent) {
			case SIM_SPOR_HOST_EVENT_DIS:

				M_UART(SIM_SPOR_, "DISABLE_EVENT\n");

				if (FALSE == gSimSPORManager.Event.btNowSPOREventSet) {
					gSimSPORManager.Event.btNowSimSPOR = FALSE;
					gSimSPORManager.Event.btRequestSPOREvent = FALSE;
				}

				break;

			case SIM_SPOR_HOST_EVENT_NORMAL_STANDBY:

				ubFinish = FALSE;       // Pending CQ

				if (FALSE == gSimSPORManager.Event.btNowNormalStandby) {

					M_UART(SIM_SPOR_, "NORMAL_STANDBY_EVENT Start ...\n");

					gubEnterPowerDone = FALSE;

					M_HOST_EVENT_SET_FLAG(HOST_EVENT_NORMAL_SHUT_DOWN_BIT);

					gSimSPORManager.Event.btNowNormalStandby = TRUE;
				}
				else {
					// Wait standby completed

					if (FALSE == gubEnteringStandby) {

						M_UART(SIM_SPOR_, "NORMAL_STANDBY_EVENT Finish !\n");

						gSimSPORManager.Event.btNowNormalStandby = FALSE;
						ubFinish = TRUE;    // Send CQ
					}
				}
				break;

			case SIM_SPOR_HOST_EVENT_STANDBY:
				M_UART(SIM_SPOR_, "STANDBY_EVENT\n");

				if (FALSE == SIM_SPOR_SUPPORT_STANDBY_EN) {

					M_UART(SIM_SPOR_, "STANDBY_EVENT not support\n");

					break;
				}

				M_HOST_EVENT_SET_FLAG(HOST_EVENT_NORMAL_SHUT_DOWN_BIT);

			/* fall through*/
			case SIM_SPOR_HOST_EVENT_SPOR:

				if (SIM_SPOR_HOST_EVENT_SPOR == ubEvent) {
					M_UART(SIM_SPOR_, "SPOR_EVENT\n");
				}

				if (FALSE == gSimSPORManager.Event.btRequestSPOREvent) {
					M_UART(SIM_SPOR_, "Enable Host Request Event %x\n", ubEvent);

					gSimSPORManager.Event.btRequestSPOREvent = TRUE;
					SimSPORSelectEvent(SIM_SPOR_SECURITY_CMD, ubEvent);
				}
				else {
					M_UART(SIM_SPOR_, "SPOR Event is running\n");
				}

				break;

			case SIM_SPOR_HOST_EVENT_ALL_CMD_COMPLETED:

				M_UART(SIM_SPOR_, "ALL_CMD_COMPLETED_EVENT\n");

				gSimSPORManager.Event.btHostCmdDone = TRUE;

				break;

			case SIM_SPOR_HOST_EVENT_NOW_SPOR:

				M_UART(SIM_SPOR_, "SIM_SPOR_HOST_EVENT_NOW_SPOR\n");

				if (FALSE == gSimSPORManager.Event.btRequestSPOREvent) {
					M_UART(SIM_SPOR_, "Enable Host Request GR Vendor Event %x\n", ubEvent);

					gSimSPORManager.Event.btRequestSPOREvent = TRUE;
					SimSPORSelectEvent(SIM_SPOR_SECURITY_CMD, SIM_SPOR_HOST_EVENT_NOW_SPOR);
				}
				else {
					M_UART(SIM_SPOR_, "SPOR Event is running\n");
				}

				break;
			default:
				break;
			}
		}
	}
	else {
		// Security receive command
		// report FW status
		if (gSimSPORManager.Event.btRequestSPOREvent) {
			if (gSimSPORManager.Event.btNowSPOREventSet) {

				pVendorCmd->Receive.ulStatus = SIM_SPOR_STATUS_RECOVERY;

				M_UART(SIM_SPOR_, "SPOR Event Recovery\n");
			}
			else {
				pVendorCmd->Receive.ulStatus = SIM_SPOR_STATUS_CHECKING;
				M_UART(SIM_SPOR_, "SPOR Event checking\n");
			}
		}
		else {
			pVendorCmd->Receive.ulStatus = SIM_SPOR_STATUS_READY;
			M_UART(SIM_SPOR_, "SPOR Event ready for command\n");
		}
	}

	return ubFinish;
}

void SimSPORSecurityCmdHandler(void)
{
	OPT_HCMD_PTR pCmd = NULL;

	pCmd = &nvme_cur_HCMD;

	if (pCmd->valid) {
		if (pCmd->hcmd.info.ubADM) {
			if ((SECRUITY_SEND == pCmd->hcmd.info.ubOPCode) || (SECURITY_RECEIVE == pCmd->hcmd.info.ubOPCode)) {
				while (nvme_cur_HCMD.state < WAIT_FW_SAVE_INFO) {
					/************************************************************************
					 *						Pop HW Result						 *
					 ************************************************************************/
					BMUDelegateCmd();

					nvmeHCMD_handler(pCmd);

					if (M_DB_GET_RD_CNT(DB_APU_TD_CQ)) {
						nvme_htd_handle();
					}
				}
			}
		}

		// Abort NVME Lib state.
		pCmd->valid = FALSE;
		pCmd->state = FINISH;

		if ((SECRUITY_SEND == pCmd->hcmd.info.ubOPCode) || (SECURITY_RECEIVE == pCmd->hcmd.info.ubOPCode)) {
			;       // nvmeHCMD_handler will send CQ
		}
		else {
			nvme_send_success(pCmd->hcmd.info.CTAG);
		}
	}
}

void SimSPORGetAPUWriteFIFOCID(void)
{
	// Get FIFO CID
	if (0 != M_APU_GET_CMD_FIFO_WR_CMD_Q_CNT()) {

		NVMEHCMD *pNReadWriteCQHeaderInfo = NULL;
		U8 ubIdx = 0;
		U16 uwCID = 0;

		ubIdx = M_APU_GET_CMD_FIFO_WR_CMD_Q_IDX(M_APU_GET_CMD_FIFO_WR_CMD_Q_RD_ADDR());

		pNReadWriteCQHeaderInfo = (NVMEHCMD *)M_APU_CMD_N_FIFO_BASE(ubIdx);

		uwCID = M_CTSRAM_GET_CID(pNReadWriteCQHeaderInfo->info.CTAG);

		M_UART(SIM_SPOR_INFO_, "Release FIFO Idx %x CID %x\n", ubIdx, uwCID);
	}
}

void SimSPORUnlockPOR(void)
{
	REGHCMD_PTR  pHcmd = NULL;
	U8 ubUnlockPOR = TRUE;

	pHcmd = (REGHCMD_PTR)M_DB_QINFO_GET_RPTR(DB_APU_CMD_CQ);

	if (pHcmd->info.ubADM) {
		if ((ASYNC_REQ == pHcmd->info.ubOPCode) || (SECURITY_RECEIVE == pHcmd->info.ubOPCode) || (SECRUITY_SEND == pHcmd->info.ubOPCode)) {
			if (FW_PROCESS_SYNC_CMD == nvme_cur_HCMD.state) {
				FTLSyncCmdHandler_FTL(&nvme_cur_HCMD);
			}
			else {
				//For parsing CMD and reply CMD
				FTLSyncCmdHandler_NVME(&nvme_cur_HCMD, FW_INIT_HOST_CMD_IN_SPOR_STOP);
			}

			ubUnlockPOR = FALSE;
		}
	}

	if (ubUnlockPOR) {
		if (FALSE == InitInfoVTSetEvent(SAVE_INIT_INFO_VT_EVENT_APU_CMD_CQ_UNLOCK_POR)) {
			;
		}
		else {
			gpVT->NormalPowerOff.ubAll = 0;
			M_UART(SIM_SPOR_, "SPOR DB_APU_CMD_CQ unlock POR\n");
		}
	}
}

U8 SimSPORFreeWLB(void)
{
	U8 ubStatus = FALSE;

	while (M_DB_GET_RD_CNT(DB_BMU_WR_CQ)) {
		WriteCQInfo_t *pWriteCQInfo = NULL;
		BMUCmdResult_t BMUCmdResult;
		U16 uwWCQRPTR = 0;

		uwWCQRPTR = M_DB_GET_RPTR_OFFSET(DB_BMU_WR_CQ);
		pWriteCQInfo = (WriteCQInfo_t *)M_DB_GET_QBODY_PTR(DB_BMU_WR_CQ, uwWCQRPTR);

		M_UART(SIM_SPOR_INFO_, "Free WCQ ID %x ofst %x LCA %x LBA %x\n", pWriteCQInfo->DW0.B.ubLBID, pWriteCQInfo->DW0.B.uwLBOffset, pWriteCQInfo->ulLCA, pWriteCQInfo->ulLCA * 8);

		BMUAPICmdFree(BMU_CMD_NEED_CQ, pWriteCQInfo->DW0.B.ubLBID,  pWriteCQInfo->DW0.B.uwLBOffset, 1, BMU_FREE_OPTION_NOT_NEED_CHECK_FULL_FLAG, BMU_FREE_NOT_NEED_CHECK_DF_FLAG, BMU_FREE_FAR_DIS, &BMUCmdResult);

		M_DB_TRIGGER_READ_CNT(DB_BMU_WR_CQ, 1);
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_BMU_WR_CQ, gDBQueueCnt.QueueCnt[(DB_BMU_WR_CQ)] ) == M_DB_GET_RPTR((DB_BMU_WR_CQ)));
		ubStatus = TRUE;
	}

	return ubStatus;
}

void SimSPORFreeSyncCmd(U8 ubMode)
{
	U16 uwRPTR = 0;
	REGHCMD_PTR  pHcmd = NULL;
	U32 ulCmdSpecific = SIM_SPOR_EVENT_DIS_SUCCESS;
	U8 ubAttribute = ATTRI_READ;
	CTAG_t CTAG = 0;
	U8 ubDTAG = 0;
	U32 ulLCA = 0;
	U32 ulNLC = 0;
	U8 ubIdx = 0;

	// Scan Trim attr by DTAG if Trim command is completed.
	if (0 != M_APU_GET_DEP_TRIM_CNT()) {

		for (ubIdx = 0; ubIdx < APU_DEP_TABLE_ENTRY_NUM; ubIdx++) {

			if (M_APU_CHECK_DEP_SRAM_VALID(ubIdx)) {

				ubAttribute = M_APU_GET_DEP_SRAM_ATTRIBUTE(ubIdx);

				if (ATTRI_TRIM == ubAttribute) {

					ubDTAG = M_APU_GET_DEP_SRAM_DTAG(ubIdx);

					CTAG = M_APU_GET_DEP_SRAM_CTAG(ubIdx);

					ulNLC = M_APU_GET_DEP_SRAM_NUM_OF_LCA(ubIdx);
					ulLCA = M_APU_GET_DEP_SRAM_LCA(ubIdx);

					M_UART(SIM_SPOR_, "Del Dep. Idx %x, DTAG %x, CTAG %x, LCA %x, NLC %x\n", ubIdx, ubDTAG, CTAG, ulLCA, ulNLC);

					ApuDepDelete(ubDTAG, APU_DEL_REQ_DEP_TAG_MODE, ApuDepDelete_REQ_TRIM_ATTR);
				}
			}
		}
	}

	while (M_DB_GET_RD_CNT(DB_APU_CMD_CQ)) {
		uwRPTR = M_DB_GET_RPTR_OFFSET(DB_APU_CMD_CQ);

		pHcmd = (REGHCMD_PTR)M_DB_GET_QBODY_PTR(DB_APU_CMD_CQ, uwRPTR);

		if (SIM_SPOR_SECURITY_CMD == gSimSPORManager.Status.SPORSrc) {

			nvme_setupHCMD(pHcmd, &nvme_cur_HCMD);

			SimSPORSecurityCmdHandler();

			M_DB_TRIGGER_READ_CNT(DB_APU_CMD_CQ, 1);
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR(DB_APU_CMD_CQ, gDBQueueCnt.QueueCnt[(DB_APU_CMD_CQ)]) == M_DB_GET_RPTR((DB_APU_CMD_CQ)));
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, NVME_CHK_CMGR_LOCK_INT());

			if (M_APU_CHECK_CMD_DISPATCH_LOCK()) {
				M_APU_CLEAR_CMD_DISPATCH_LOCK();
			}

			NVME_CLR_CMGR_LOCK_INT();
		}
		else {

			if ((pHcmd->info.ubADM) && (ADMIN_ASYNCHRONOUS_EVENT_REQUEST == pHcmd->info.ubOPCode)) {
				// AER IN, Host info all command is completed
				M_UART(SIM_SPOR_INFO_, "Free Sync AER In\n");

				break;
			}
			else {
				if ((pHcmd->info.ubADM) && (ADMIN_CMD_VENDOR_POWER == pHcmd->info.ubOPCode)) {

					if (SIM_SPOR_RELEASE_POWER_DOWN == ubMode) {
						ulCmdSpecific = SIM_SPOR_EVENT_DIS_POWER_DOWN_FAIL;       // Report Host Disable SPOR Event fail
					}
					else {
						ulCmdSpecific = SIM_SPOR_EVENT_DIS_POWER_UP_FAIL;       // Report Host Disable SPOR Event fail
					}

					nvme_send_cq_completion(pHcmd->info.CTAG, GENERIC_CMD, SUCCESS_CPL, ulCmdSpecific, 0, 0, NULL);
				}
				else {
					nvme_send_success(pHcmd->info.CTAG);
				}

				M_UART(SIM_SPOR_, "Free Sync CTAG %x Opcode %x ADM %x\n", pHcmd->info.CTAG, pHcmd->info.ubOPCode, pHcmd->info.ubADM);

				M_DB_TRIGGER_READ_CNT(DB_APU_CMD_CQ, 1);
				M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (!DEBUG_DB_CHECK_EN) || (U32)M_DB_GET_QBODY_PTR( DB_APU_CMD_CQ, gDBQueueCnt.QueueCnt[(DB_APU_CMD_CQ)] ) == M_DB_GET_RPTR((DB_APU_CMD_CQ)));
				M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, NVME_CHK_CMGR_LOCK_INT());

				NVME_CLR_CMGR_LOCK_INT();
			}
		}
	}

	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, 0 == M_APU_GET_DEP_TRIM_CNT());
}

void SimSPORReleaseCmd(U8 ubMode)
{
	U16 uwRPTR = 0;
	U32 ulCmdSpecific = SIM_SPOR_EVENT_DIS_SUCCESS;
	REGHCMD_PTR  pHcmd = NULL;
	OPT_HCMD_PTR pCmd = NULL;
	U8 ubXZIPWaitPCA = 0;
	U32 ulEntryNum = 0;
	BMUCmdResult_t BMUCmdResult;

	if (XZIP_EN) {
		// Wait runtime pending XZIP callback
		if (CLEAR_XZIP_ENTRY_WAIT_XZIP_CLEAR_REG == gXZIP.ClearXZIPEntryState) {
			while (CLEAR_XZIP_ENTRY_FINISH != gXZIP.ClearXZIPEntryState) {
				XZIPDelegateCmd();
			}
			gXZIP.ClearXZIPEntryState = CLEAR_XZIP_ENTRY_INIT;
		}

		BlockXZIP(CLEAR_XZIP_FLOW);

		M_XZIP_TRIGGER_GEN_INST_CLR();

		while (!XZIPRegCheckGenInstEqualZero());
		XZIPClearRegister((U32)XZIPCleanRegDone_CallBack);
		gXZIP.ClearXZIPEntryState = CLEAR_XZIP_ENTRY_INIT;
		while (CLEAR_XZIP_ENTRY_FINISH != gXZIP.ClearXZIPEntryState) {
			XZIPDelegateCmd();
		}

		gXZIP.ClearXZIPEntryState = CLEAR_XZIP_ENTRY_INIT;

		if (M_XZIP_CHECK_FW_WAIT_PCA()) {
			ubXZIPWaitPCA = TRUE;
		}
		else {
			ubXZIPWaitPCA = FALSE;
		}

		M_CLEAR_FW_WAIT_PCA();      // avoid wait search PCA to block WCQ

		ulEntryNum = M_GET_XZIP_ENTRY_NUM();

		XZIPForceAdjustLen(SIM_SPOR_XZIP_ADJUST_LENGTH);

		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, 0 == M_XZIP_CHECK_FW_WAIT_PCA());
	}

	if (SIM_SPOR_RELEASE_POWER_DOWN == ubMode) {

		gSimSPORManager.Event.btHostCmdDone = FALSE;

		M_APU_SET_WRITE_BLOCK_FW_LOCK();
		M_APU_SET_NRW_FW_LOCK();
		M_APU_SET_READ_BLOCK_FW_LOCK();

		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, 0 == M_APU_GET_CMD_FIFO_RD_CMD_Q_CNT());

		// wait lock done
		while (!M_APU_CHK_CMD_FIFO_WR_LOCK()) {
			;
		}

		// Adjust WLB quota to max
		// Avoid WLB is reach quota count by xzip or error handle or Cop0 error and APU can not finish write command.
		// Skip any error and release WLB to make Host command complete.
		M_BMU_SET_LB_UPPER_LIMIT(LB_ID_WRITE_0, INIT_UL_WRITE_LB << 1);
		BMUAPICmdUpdateLL(BMU_CMD_NEED_CQ, LB_ID_WRITE_0, INIT_UL_WRITE_LB << 1, &BMUCmdResult);

		do {

			// Skip init flow
			if (ICE_MODE_EN) {
				if (FALSE == gSimSPORManager.Event.btNowFWInit) {
					// Complete any pending error handle
					FWCop0Waiting();
					COP0DelegateCmd();
				}
			}

			SimSPORFreeWLB();
		} while (((0 != M_APU_GET_WR_MGR_STATE()) || (0 != M_APU_GET_WR_MGR_REMAIN_4K_CNT()) || (0 != M_APU_GET_WR_MGR_FW_CMD_REMAIN_4K_CNT())
				|| 0 != M_BMU_GET_LB_N_LENGTH(LB_ID_WRITE_0)) || (0 != M_APU_GET_WR_MGR_GETPB_Q_CNT()) || (0 != M_APU_GET_WR_MGR_FLUSH_Q_CNT()) || (0 != M_APU_GET_WR_MGR_VLDT_Q_CNT()));

		SimSPORGetAPUWriteFIFOCID();

		M_APU_SET_WR_MGR_FW_FLUSH();

		while (M_APU_CHK_WR_MGR_FW_FLUSH()) {
			SimSPORFreeWLB();
		}

		// Free last write cache
		while (0 != M_BMU_GET_LB_N_LENGTH(LB_ID_WRITE_0)) {
			if (TRUE == SimSPORFreeWLB()) {
				break;
			}
		}

		pCmd = &nvme_cur_HCMD;

		if (pCmd->valid) {

			M_UART(SIM_SPOR_, "HCMD CTAG %x\n", pCmd->hcmd.info.CTAG);

			if (SIM_SPOR_SECURITY_CMD == gSimSPORManager.Status.SPORSrc) {
				// Check receive Status

				SimSPORSecurityCmdHandler();
			}
			else {
				if ((pCmd->hcmd.info.ubADM) && (ADMIN_CMD_VENDOR_POWER == pCmd->hcmd.info.ubOPCode)) {

					if (SIM_SPOR_RELEASE_POWER_DOWN == ubMode) {
						ulCmdSpecific = SIM_SPOR_EVENT_DIS_POWER_DOWN_FAIL;       // Report Host Disable SPOR Event fail
					}
					else {
						ulCmdSpecific = SIM_SPOR_EVENT_DIS_POWER_UP_FAIL;       // Report Host Disable SPOR Event fail
					}

					M_UART(SIM_SPOR_, "HCMD POWER DOWN CmdSpecific: %x\n", ulCmdSpecific);

					nvme_send_cq_completion(pCmd->hcmd.info.CTAG, GENERIC_CMD, SUCCESS_CPL, ulCmdSpecific, 0, 0, NULL);
				}
				else {
					nvme_send_cq_completion(pCmd->hcmd.info.CTAG, GENERIC_CMD, SUCCESS_CPL, 0, 0, 0, NULL); // Always send success, just skip ErrInfo PTR
				}
				// Abort NVME Lib state.
				pCmd->valid = FALSE;
				pCmd->state = FINISH;
			}
		}

		SimSPORFreeSyncCmd(ubMode);       // DB will reset by init flow
	}
	else {

		if (gSimSPORManager.Status.btIsAdminAERCmdToSynchronousCmd) {
			// Set AER to sync command
			M_APU_SET_ASYNCHRONOUS_CMD_DISPATCH_ATTRIBUTE_TO_SYNC();
		}

		M_APU_CLEAR_NRW_FW_LOCK();     // Need AER and free sync command

		do {

			if (M_APU_CHECK_CMD_DISPATCH_LOCK()) {
				M_APU_CLEAR_CMD_DISPATCH_LOCK();
			}

			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, 0 == M_APU_GET_CMD_FIFO_RD_CMD_Q_CNT());

			if ((0 == M_APU_GET_WR_MGR_STATE()) && (0 != M_APU_GET_CMD_FIFO_WR_CMD_Q_CNT())) {

				SimSPORGetAPUWriteFIFOCID();

				M_APU_SET_WR_BLK_ONCE_UNLOCK();

				while (M_APU_CHK_WR_BLK_ONCE_UNLOCK()) {
					;
				}
			}

			// wait lock done
			while (!M_APU_CHK_CMD_FIFO_WR_LOCK()) {
				;
			}

			do {
				SimSPORFreeWLB();
			} while (((0 != M_APU_GET_WR_MGR_STATE()) || (0 != M_APU_GET_WR_MGR_REMAIN_4K_CNT()) || (0 != M_APU_GET_WR_MGR_FW_CMD_REMAIN_4K_CNT())
					|| 0 != M_BMU_GET_LB_N_LENGTH(LB_ID_WRITE_0)) || (0 != M_APU_GET_WR_MGR_GETPB_Q_CNT()) || (0 != M_APU_GET_WR_MGR_FLUSH_Q_CNT()) || (0 != M_APU_GET_WR_MGR_VLDT_Q_CNT()));

			SimSPORFreeSyncCmd(ubMode);

			if (SIM_SPOR_SECURITY_CMD == gSimSPORManager.Status.SPORSrc) {
				if (gSimSPORManager.Event.btHostCmdDone) {

					// Tool must wait 1 sec for FW last cache flush and enter FTLTask
					M_UART(SIM_SPOR_, "Host all cmd done ! Enter FTLTask ...\n");

					M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, 0 == M_APU_GET_WR_MGR_STATE());

					M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, 0 == M_APU_GET_CMD_FIFO_WR_CMD_Q_CNT());

					M_APU_SET_WR_MGR_FW_FLUSH();

					while (M_APU_CHK_WR_MGR_FW_FLUSH()) {
						SimSPORFreeWLB();
					}

					while (0 != M_BMU_GET_LB_N_LENGTH(LB_ID_WRITE_0)) {

						if (TRUE == SimSPORFreeWLB()) {
							break;
						}
					}

					break;
				}
			}
			else {
				if ((0 == M_APU_GET_WR_MGR_STATE()) && (0 == M_APU_GET_CMD_FIFO_WR_CMD_Q_CNT())) {
					// Wait new AER and go to main loop
					if (!M_DB_CHECK_EMPTY(DB_APU_CMD_CQ)) {

						uwRPTR = M_DB_GET_RPTR_OFFSET(DB_APU_CMD_CQ);

						pHcmd = (REGHCMD_PTR)M_DB_GET_QBODY_PTR(DB_APU_CMD_CQ, uwRPTR);

						if ((pHcmd->info.ubADM) && (ADMIN_ASYNCHRONOUS_EVENT_REQUEST == pHcmd->info.ubOPCode)) {

							M_UART(SIM_SPOR_, "New AER In\n");

							M_APU_SET_WR_MGR_FW_FLUSH();

							while (M_APU_CHK_WR_MGR_FW_FLUSH()) {
								SimSPORFreeWLB();
							}

							while (0 != M_BMU_GET_LB_N_LENGTH(LB_ID_WRITE_0)) {

								if (TRUE == SimSPORFreeWLB()) {
									break;
								}
							}
							break;
						}
					}
				}
			}

		} while (1);

		if (XZIP_EN) {
			// avoid last host command to hit xzip but free by POR
			BlockXZIP(CLEAR_XZIP_FLOW);

			M_XZIP_TRIGGER_GEN_INST_CLR();

			while (!XZIPRegCheckGenInstEqualZero());
			XZIPClearRegister((U32)XZIPCleanRegDone_CallBack);
			gXZIP.ClearXZIPEntryState = CLEAR_XZIP_ENTRY_INIT;
			while (CLEAR_XZIP_ENTRY_FINISH != gXZIP.ClearXZIPEntryState) {
				XZIPDelegateCmd();
			}

			gXZIP.ClearXZIPEntryState = CLEAR_XZIP_ENTRY_INIT;

			XZIPForceAdjustLen(ulEntryNum);
		}

		M_APU_CLEAR_WRITE_BLOCK_FW_LOCK();

		M_APU_CLEAR_READ_BLOCK_FW_LOCK();       // NRW lock read/write FIFO

		if (TRUE == gSimSPORManager.Status.btIsAdminAERCmdToSynchronousCmd) {
			// Restore AER to async command
			M_APU_CLEAR_ASYNCHRONOUS_CMD_DISP_ATTRIBUTE_To_SYNC();

			if (M_APU_CHECK_CMD_DISPATCH_LOCK()) {
				M_APU_CLEAR_CMD_DISPATCH_LOCK();
			}
		}
	}

	if (XZIP_EN) {
		if (ubXZIPWaitPCA) {
			// Restore org XZIP setting
			M_SET_FW_WAIT_PCA();
		}
	}
}

void SimSPORResetAPURCQDoorBellPtr(void)
{
	// APU RLB same to BMU RLB
	M_APU_SET_READ_MANAGER_USR_READ_LB_TAIL(M_BMU_GET_LB_TAIL(LB_ID_READ));
	M_APU_SET_READ_MANAGER_USER_READ_SET_INIT();

	// APU RCQ same to DB and MR RCQ bitmap
	M_APU_SET_READ_MANAGER_RESET_RCQ_DOORBELL();

	while (M_APU_CHECK_READ_MANAGER_RESET_RCQ_DOORBELL());
}

void SimSPORInitVariable(void)
{
	gSimSPORManager.Event.ubAll = 0;
	gSimSPORManager.Status.ubAll = 0;

	gSimSPORManager.ubBaseCode = SIM_SPOR_INVALID_CODE;
	gSimSPORManager.ubSubCode = SIM_SPOR_INVALID_CODE;

	gSimSPORManager.ubBaseCodeNum = SIM_SPOR_BASE_CODE_NUM;

	// 1. Total
	gSimSPORManager.ubSubCodeNum[SIM_SPOR_BASE_VT] = SIM_SPOR_SUB_VT_NUM;
	gSimSPORManager.ubSubCodeNum[SIM_SPOR_BASE_GR] = SIM_SPOR_SUB_GR_NUM;
	gSimSPORManager.ubSubCodeNum[SIM_SPOR_BASE_GC] = SIM_SPOR_SUB_GC_NUM;
	gSimSPORManager.ubSubCodeNum[SIM_SPOR_BASE_TABLE] = SIM_SPOR_SUB_TABLE_NUM;
	gSimSPORManager.ubSubCodeNum[SIM_SPOR_BASE_STANDBY] = SIM_SPOR_SUB_STANDBY_NUM;
	gSimSPORManager.ubSubCodeNum[SIM_SPOR_BASE_INIT] = SIM_SPOR_SUB_INIT_NUM;
	gSimSPORManager.ubSubCodeNum[SIM_SPOR_BASE_INITNORMAL] = SIM_SPOR_SUB_INITNORMAL_NUM;

	// 2. Select
	gSimSPORManager.ubSubCodeSelectNum[SIM_SPOR_BASE_VT] = SIM_SPOR_SUB_VT_SELECT_NUM;
	gSimSPORManager.ubSubCodeSelectNum[SIM_SPOR_BASE_GR] = SIM_SPOR_SUB_GR_SELECT_NUM;
	gSimSPORManager.ubSubCodeSelectNum[SIM_SPOR_BASE_GC] = SIM_SPOR_SUB_GC_SELECT_NUM;
	gSimSPORManager.ubSubCodeSelectNum[SIM_SPOR_BASE_TABLE] = SIM_SPOR_SUB_TABLE_SELECT_NUM;
	gSimSPORManager.ubSubCodeSelectNum[SIM_SPOR_BASE_STANDBY] = SIM_SPOR_SUB_STANDBY_SELECT_NUM;
	gSimSPORManager.ubSubCodeSelectNum[SIM_SPOR_BASE_INIT] = SIM_SPOR_SUB_INIT_SELECT_NUM;
	gSimSPORManager.ubSubCodeSelectNum[SIM_SPOR_BASE_INITNORMAL] = SIM_SPOR_SUB_INITNORMAL_SELECT_NUM;

	// 3. Select Force
	gSimSPORManager.ubSubCodeForceNum[SIM_SPOR_BASE_VT] = SIM_SPOR_SUB_VT_FORCE_NUM;
	gSimSPORManager.ubSubCodeForceNum[SIM_SPOR_BASE_GR] = SIM_SPOR_SUB_GR_FORCE_NUM;
	gSimSPORManager.ubSubCodeForceNum[SIM_SPOR_BASE_GC] = SIM_SPOR_SUB_GC_FORCE_NUM;
	gSimSPORManager.ubSubCodeForceNum[SIM_SPOR_BASE_TABLE] = SIM_SPOR_SUB_TABLE_FORCE_NUM;
	gSimSPORManager.ubSubCodeForceNum[SIM_SPOR_BASE_STANDBY] = SIM_SPOR_SUB_STANDBY_FORCE_NUM;
	gSimSPORManager.ubSubCodeForceNum[SIM_SPOR_BASE_INIT] = SIM_SPOR_SUB_INIT_FORCE_NUM;
	gSimSPORManager.ubSubCodeForceNum[SIM_SPOR_BASE_INITNORMAL] = SIM_SPOR_SUB_INITNORMAL_FORCE_NUM;

	if (0 == M_APU_CHECK_ASYNCHRONOUS_CMD_DISP_ATTRIBUTE_To_SYNC()) {

		M_APU_SET_ASYNCHRONOUS_CMD_DISPATCH_ATTRIBUTE_TO_SYNC();

		gSimSPORManager.Status.btIsAdminAERCmdToSynchronousCmd = TRUE;
	}
	else {
		gSimSPORManager.Status.btIsAdminAERCmdToSynchronousCmd = FALSE;
	}
}

void SimSPORGRCounter(void)
{
	if (gSimSPORManager.Event.btNowSimSPOR) {
		if (0 != gSimSPORManager.uwGRCounter) {
			M_UART(SIM_SPOR_, "GRCounter:%x GRPtr:%x Unit:%x\n", gSimSPORManager.uwGRCounter, gpVT->GR.ulPlaneIndex, gpVT->GR.uwUnit[gpVT->GR.ubUnitIndex].B.uwUnit);
			gSimSPORManager.uwGRCounter--;

			if (0 == gSimSPORManager.uwGRCounter) {
				M_UART(SIM_SPOR_, "GR Set SPOR Event Base:%x Sub:%x\n", gSimSPORManager.ubBaseCode, gSimSPORManager.ubSubCode);
				SimSPORSetEvent(gSimSPORManager.ubBaseCode, gSimSPORManager.ubSubCode);
			}
		}
	}
}

void SimSPORSelectForceSubCode(U8 ubBaseCode)
{
	U8 ubSubCode = SIM_SPOR_INVALID_CODE;
	U8 ubSubCodeForceNum = 0;

	// Select force sub code
	ubSubCodeForceNum = gSimSPORManager.ubSubCodeForceNum[ubBaseCode] - (gSimSPORManager.ubSubCodeSelectNum[ubBaseCode] + 1);

	if (0 != ubSubCodeForceNum) {

		ubSubCode = ((U8)RTTGetCnt(RTT0) % ubSubCodeForceNum) + 1;

		gSimSPORManager.ubSubCodeForce[ubBaseCode] = gSimSPORManager.ubSubCodeSelectNum[ubBaseCode] + ubSubCode;
	}
	else {
		gSimSPORManager.ubSubCodeForce[ubBaseCode] = SIM_SPOR_INVALID_CODE;
	}

	M_UART(SIM_SPOR_, "Force Base %x Sub %x\n", ubBaseCode, gSimSPORManager.ubSubCodeForce[ubBaseCode]);
}

void SimSPORSelectInitEvent(void)
{
	if (SIM_SPOR_INIT_FLOW_EN) {

		if (FALSE == gMainJumpManager.btFromMainJumpFlag) {
			// 1. Total
			gSimSPORManager.ubSubCodeNum[SIM_SPOR_BASE_INIT] = SIM_SPOR_SUB_INIT_NUM;
			gSimSPORManager.ubSubCodeNum[SIM_SPOR_BASE_INITNORMAL] = SIM_SPOR_SUB_INITNORMAL_NUM;

			// 2. Select
			gSimSPORManager.ubSubCodeSelectNum[SIM_SPOR_BASE_INIT] = SIM_SPOR_SUB_INIT_SELECT_NUM;
			gSimSPORManager.ubSubCodeSelectNum[SIM_SPOR_BASE_INITNORMAL] = SIM_SPOR_SUB_INITNORMAL_SELECT_NUM;

			// 3. Select Force
			gSimSPORManager.ubSubCodeForceNum[SIM_SPOR_BASE_INIT] = SIM_SPOR_SUB_INIT_FORCE_NUM;
			gSimSPORManager.ubSubCodeForceNum[SIM_SPOR_BASE_INITNORMAL] = SIM_SPOR_SUB_INITNORMAL_FORCE_NUM;

			gSimSPORManager.ubInitSPORCounter = 0;

			gSimSPORManager.Event.btNowSimSPOR = TRUE;

			M_UART(SIM_SPOR_, "Recal SPOR Init\n");
		}

		if (0 == gSimSPORManager.ubInitSPORCounter) {
			gSimSPORManager.ubInitSPORCounter = ((U8)RTTGetCnt(RTT0) % SIM_SPOR_INIT_MAX_COUNTER + 1);
		}
		else {
			gSimSPORManager.ubInitSPORCounter--;
		}

		if (0 != gSimSPORManager.ubInitSPORCounter) {
			// Select force sub code
			SimSPORSelectForceSubCode(SIM_SPOR_BASE_INIT);
			SimSPORSelectForceSubCode(SIM_SPOR_BASE_INITNORMAL);

			M_UART(SIM_SPOR_, "SPOR Base Init Sub %x, Cnt %x\n", gSimSPORManager.ubSubCodeForce[SIM_SPOR_BASE_INIT], gSimSPORManager.ubInitSPORCounter);
		}
	}
	else {
		gSimSPORManager.ubInitSPORCounter = 0;
	}

	gSimSPORManager.Event.btNowFWInit = TRUE;      // Init flow control by ubInitSPORCounter
}

void SimSPORInit(void)
{
	if (FALSE == gSimSPORManager.Event.btRequestSPOREvent) {

		M_UART(SIM_SPOR_, "SimSPOR Init\n");

		SimSPORInitVariable();
	}
	else {

		M_UART(SIM_SPOR_, "Release cmd\n");

		SimSPORReleaseCmd(SIM_SPOR_RELEASE_POWER_ON);

		gSimSPORManager.Event.ubAll = 0;

		gSimSPORManager.ubBaseCode = SIM_SPOR_INVALID_CODE;
		gSimSPORManager.ubSubCode = SIM_SPOR_INVALID_CODE;
		gSimSPORManager.ubInitSPORCounter = 0;
	}
}

void SimSPORVendorPowerHandler(U32 ulSPOREventEn)
{
	if (SIM_SPOR_EVENT_EN == ulSPOREventEn) {
		if (FALSE == gSimSPORManager.Event.btRequestSPOREvent) {
			gSimSPORManager.Event.btRequestSPOREvent = TRUE;
			M_UART(SIM_SPOR_, "SPOR Enable Event\n");

			SimSPORSelectEvent(SIM_SPOR_VENDOR_CMD, SIM_SPOR_HOST_EVENT_SPOR);
		}
		else {
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
		}
	}
	else {

		M_UART(SIM_SPOR_, "SPOR Disable Event\n");

		// Do not reset base and sub code
		// Stop SimSPORMgr
		gSimSPORManager.Event.btRequestSPOREvent = FALSE;
		gSimSPORManager.Event.btNowSimSPOR = FALSE;
	}
}

void SimSPORSelectEvent(U8 ubSrc, U8 ubEvent)
{
	U8 ubBaseCode = SIM_SPOR_INVALID_CODE;

	if (FALSE == gSimSPORManager.Event.btNowSimSPOR) {
		if (gSimSPORManager.Event.btRequestSPOREvent) {
			if (SIM_SPOR_INVALID_CODE == gSimSPORManager.ubBaseCode) {

				gSimSPORManager.uwGRCounter = 0;

				if (SIM_SPOR_HOST_EVENT_SPOR == ubEvent) {
					do {
						ubBaseCode = (U8)RTTGetCnt(RTT0) % gSimSPORManager.ubBaseCodeNum;
						//M_UART(SIM_SPOR_, "Select Base Code %x\n", ubBaseCode);
						if (0 != gSimSPORManager.ubSubCodeSelectNum[ubBaseCode]) {
							break;
						}
					} while (1);

					gSimSPORManager.ubBaseCode = ubBaseCode;

					do {
						gSimSPORManager.ubSubCode = (U8)RTTGetCnt(RTT0) % gSimSPORManager.ubSubCodeSelectNum[ubBaseCode];

						if ((SIM_SPOR_BASE_GR == ubBaseCode) && (SIM_SPOR_SUB_GR_DSA_FULL == gSimSPORManager.ubSubCode)) {
							if (gpVT->ST1.uwDSACnt < ST1_DSA_SIZE) {
								gSimSPORManager.ubSubCode = SIM_SPOR_INVALID_CODE;
							}
						}

						if (SIM_SPOR_GR_ONLY_EN) {
							if ((SIM_SPOR_BASE_GR == ubBaseCode) && (gSimSPORManager.ubSubCode >= SIM_SPOR_GR_SUB_SELECT_NUM)) {
								gSimSPORManager.ubSubCode = SIM_SPOR_INVALID_CODE;
							}
						}

						if (SIM_SPOR_INVALID_CODE == gSimSPORManager.ubSubCode) {
							;
						}
						else {
							break;
						}

					} while (1);

					// Select force sub code
					for (ubBaseCode = 0; ubBaseCode < SIM_SPOR_BASE_NUM; ubBaseCode++) {

						SimSPORSelectForceSubCode(ubBaseCode);
					}
				}
				else {
					if (SIM_SPOR_HOST_EVENT_STANDBY == ubEvent) {
						// Standby
						M_UART(SIM_SPOR_, "Force standby event\n");
						gSimSPORManager.ubBaseCode = SIM_SPOR_BASE_STANDBY;
						gSimSPORManager.ubSubCode = SIM_SPOR_SUB_STANDBY_DONE;
						gSimSPORManager.ubSubCodeForce[SIM_SPOR_BASE_STANDBY] = SIM_SPOR_INVALID_CODE;
					}
					else {
						// SIM_SPOR_HOST_EVENT_NOW_SPOR
						M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, ubEvent == SIM_SPOR_HOST_EVENT_NOW_SPOR);
						gSimSPORManager.ubBaseCode = SIM_SPOR_BASE_GR;

						gSimSPORManager.ubSubCode = SIM_SPOR_SUB_GR_VENDOR_EVENT;

						gSimSPORManager.Event.btVendorSetEvent = TRUE;
					}
				}
			}

			gSimSPORManager.Event.btNowSimSPOR = TRUE;

			gSimSPORManager.ubInitSPORCounter = 0;

			M_UART(SIM_SPOR_, "SPOR Select Base %x, Sub %x\n", gSimSPORManager.ubBaseCode, gSimSPORManager.ubSubCode);

			if (SIM_SPOR_VENDOR_CMD == ubSrc) {
				gSimSPORManager.Status.SPORSrc = SIM_SPOR_VENDOR_CMD;
			}
			else {
				gSimSPORManager.Status.SPORSrc = SIM_SPOR_SECURITY_CMD;
			}
		}
	}
}

void SimSPORSetEvent(U8 ubBaseCode, U8 ubSubCode)
{
	gSimSPORManager.ulCounter[ubBaseCode][ubSubCode]++;

	if ((SIM_SPOR_BASE_INIT != ubBaseCode) && (SIM_SPOR_BASE_INITNORMAL != ubBaseCode)) {
		gSimSPORManager.ulTotalCnt++;
	}

	gSimSPORManager.Event.btNowSPOREventSet = TRUE;

	gSimSPORManager.ubBaseCode = ubBaseCode;        // Save code again, if set event by force code.
	gSimSPORManager.ubSubCode = ubSubCode;

	gSimSPORManager.Event.btVendorSetEvent = FALSE;

	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, 0 == gSimSPORManager.uwGRCounter);

	if (SIM_SPOR_BASE_INIT == ubBaseCode) {
		M_UART(SIM_SPOR_, "SPOR_INIT set event Base:%x Sub:%x Total : %x, Count: %x\n", ubBaseCode, ubSubCode, gSimSPORManager.ulTotalCnt, gSimSPORManager.ulCounter[ubBaseCode][ubSubCode]);
	}
	else {
		M_UART(SIM_SPOR_, "SPOR_FW set event Base:%x Sub:%x Total : %x, Count: %x\n", ubBaseCode, ubSubCode, gSimSPORManager.ulTotalCnt, gSimSPORManager.ulCounter[ubBaseCode][ubSubCode]);
	}

#if VS_SIM_EN
	// Wait Host AP kill process~ and reset call process
	while (1) {
		;
	}
#else
	if (TRUE == gSimSPORManager.Event.btRequestSPOREvent) {
		SimSPORReleaseCmd(SIM_SPOR_RELEASE_POWER_DOWN);
	}
	InitMainJump(0);
#endif     /* VS_SIM_EN */
}

U8 SimSPORGetEventStatus(U8 ubBaseCode, U8 ubSubCode)
{
	U8 ubSetEvent = FALSE;

	if (gSimSPORManager.Event.btNowFWInit) {
		if ((SIM_SPOR_BASE_INIT != ubBaseCode) && (SIM_SPOR_BASE_INITNORMAL != ubBaseCode)) {
			M_UART(SIM_SPOR_INFO_, "SPOR_Init skip check Base:%x Sub:%x GRCounter:%x GRPtr:%x\n", ubBaseCode, ubSubCode, gSimSPORManager.uwGRCounter, gpVT->GR.ulPlaneIndex);

			return ubSetEvent;
		}
		else {
			if (0 == gSimSPORManager.ubInitSPORCounter) {
				M_UART(SIM_SPOR_INFO_, "SPOR_Init done Base:%x Sub:%x GRCounter:%x GRPtr:%x\n", ubBaseCode, ubSubCode, gSimSPORManager.uwGRCounter, gpVT->GR.ulPlaneIndex);

				return ubSetEvent;
			}
		}
	}
	else {
		if ((SIM_SPOR_BASE_INIT == ubBaseCode) || (SIM_SPOR_BASE_INITNORMAL == ubBaseCode)) {
			M_UART(SIM_SPOR_INFO_, "SPOR_FW skip check Base:%x Sub:%x GRCounter:%x GRPtr:%x\n", ubBaseCode, ubSubCode, gSimSPORManager.uwGRCounter, gpVT->GR.ulPlaneIndex);

			return ubSetEvent;
		}
	}

	if (gSimSPORManager.Event.btNowSimSPOR) {
		if (0 == gSimSPORManager.uwGRCounter) {
			if (ubSubCode > gSimSPORManager.ubSubCodeSelectNum[ubBaseCode]) {

				if (ubSubCode > gSimSPORManager.ubSubCodeForceNum[ubBaseCode]) {

					if (ubSubCode < gSimSPORManager.ubSubCodeNum[ubBaseCode]) {
						M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, ubSubCode < gSimSPORManager.ubSubCodeNum[ubBaseCode]);
						M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, gSimSPORManager.ubSubCodeForceNum[ubBaseCode] > gSimSPORManager.ubSubCodeSelectNum[ubBaseCode]);

						if (SIM_SPOR_SUB_FORCE_EN) {
							gSimSPORManager.uwGRCounter = 0;

							ubSetEvent = TRUE;
							M_UART(SIM_SPOR_, "SPOR_F hit Base:%x Sub:%x GRCounter:%x GRPtr:%x\n", ubBaseCode, ubSubCode, gSimSPORManager.uwGRCounter, gpVT->GR.ulPlaneIndex);

						}
					}
				}
				else {
					if (ubSubCode == gSimSPORManager.ubSubCodeForce[ubBaseCode]) {
						if (SIM_SPOR_SUB_SELECT_FORCE_EN) {
							ubSetEvent = TRUE;

							gSimSPORManager.uwGRCounter = 0;

							M_UART(SIM_SPOR_, "SPOR_SF hit Base:%x Sub:%x GRCounter:%x GRPtr:%x\n", ubBaseCode, ubSubCode, gSimSPORManager.uwGRCounter, gpVT->GR.ulPlaneIndex);

						}
					}
				}
			}
			else {
				if ((gSimSPORManager.ubBaseCode == ubBaseCode) && (gSimSPORManager.ubSubCode == ubSubCode)) {

					if (SIM_SPOR_GR_COUNTER_EN) {
						if (SIM_SPOR_BASE_STANDBY == gSimSPORManager.ubBaseCode) {
							gSimSPORManager.uwGRCounter = 0;       // No write command for standby.
						}
						else {
							gSimSPORManager.uwGRCounter = ((U8)RTTGetCnt(RTT0) % SIM_SPOR_GR_MAX_COUNTER) + 1;
						}
					}
					else {
						gSimSPORManager.uwGRCounter = 0;
					}

					M_UART(SIM_SPOR_, "SPOR_S hit Base:%x Sub:%x GRCounter:%x GRPtr:%x\n", ubBaseCode, ubSubCode, gSimSPORManager.uwGRCounter, gpVT->GR.ulPlaneIndex);


					if (0 != gSimSPORManager.uwGRCounter) {
						;
					}
					else {
						ubSetEvent = TRUE;
					}
				}
			}
		}
	}

	return ubSetEvent;
}

U8 SimSPORBaseInitPreview(U8 ubBaseCode, U8 ubSubCode, U32 ulParameter1, U32 ulParameter2)
{
	U8 ubSetEvent = TRUE;

	switch (ubSubCode) {
	case SIM_SPOR_SUB_INIT_VT_MOTHER_PROGRAM_DOING: {
			U8 ubBackupCnt = 0;

			ubBackupCnt = (U8)ulParameter1;

			M_UART(SIM_SPOR_, "SPRO backcnt %x VT backcnt %x \n", gSimSPORManager.Status.VTBackupCnt, ubBackupCnt);

			if (ubBackupCnt == gSimSPORManager.Status.VTBackupCnt) {

				M_UART(SIM_SPOR_, "SPRO VT backcnt %x\n", ubBackupCnt);

				gSimSPORManager.Status.VTBackupCnt++;

				gSimSPORManager.Status.VTBackupCnt = gSimSPORManager.Status.VTBackupCnt % VT_BACKUP_CNT;
			}
			else {
				ubSetEvent = FALSE;
			}
		}
		break;

	case SIM_SPOR_SUB_INIT_INITINFO_CHANGED_N_VT_CHILD_PROGRAM: {
			Unit_t uwUnit = { 0 };
			U16 uwTagId = TAG_ID_ALLOCATE;
			U8	ubEraseBitMap[MAX_UNIT_INDEX * RUT_MAX_PLANEBANK / BITS_PER_BYTE] = { 0 };

			if (gInitInfoVTHdlMgr.ulEventDoing & SAVE_VT_EVENT_INITINFO_BITMASK) {

				// Initinfo Changed
				// Get new VT Child Unit
				FTLGetFreeUnit(MODE_D3, MODE_VT_CHILD_AND_INIT_INFO_UNIT, MODE_NON_STATIC, MODE_MIN_EC, &uwUnit);

				// Add old VT Child Unit to Free Pool
				FTLAddFreeUnit(MODE_VT_CHILD_AND_INIT_INFO_UNIT, FALSE, &uwUnit, ADD_FREE_SIM_SPOR_VT_CHILD);

				memset(ubEraseBitMap, 0xFF, sizeof(ubEraseBitMap));

				while (FALSE == FTLEraseUnit_Handler(MODE_D3, uwUnit, &uwTagId, TRUE, ubEraseBitMap, NULL)) {
					FWCop0Waiting();
				}

				M_UART(SIM_SPOR_, "SPRO Erase Unit %x\n", uwUnit);
			}
			else {
				ubSetEvent = FALSE;
			}

			break;
		}

	case SIM_SPOR_SUB_INIT_VT_CHILD_EMPTY:

		if (0 == gpVT->VTChild.ulPlaneIndex) {
			;
		}
		else {
			ubSetEvent = FALSE;
		}
		break;

	default:
		break;
	}

	return ubSetEvent;
}

U8 SimSPORSetEventPreview(U8 ubBaseCode, U8 ubSubCode, U32 ulParameter1, U32 ulParameter2)
{
	U8 ubSetEvent = TRUE;

	switch (ubBaseCode) {
	case SIM_SPOR_BASE_INIT:
		ubSetEvent = SimSPORBaseInitPreview(ubBaseCode, ubSubCode, ulParameter1, ulParameter2);
		break;

	default:
		break;
	}

	return ubSetEvent;
}

void SimSPOREventChecker(U8 ubBaseCode, U8 ubSubCode, U32 ulParameter1, U32 ulParameter2)
{
	U8 ubSetEvent = TRUE;

	ubSetEvent = SimSPORGetEventStatus(ubBaseCode, ubSubCode);

	if (ubSetEvent) {

		// Special case
		ubSetEvent = SimSPORSetEventPreview(ubBaseCode, ubSubCode, ulParameter1, ulParameter2);

		if (ubSetEvent) {
			SimSPORSetEvent(ubBaseCode, ubSubCode);
		}
	}
}

void SimSPORFlow(void)
{
	if (gSimSPORManager.Event.btNowSimSPOR) {
		switch (gSimSPORManager.ubBaseCode) {

		case SIM_SPOR_BASE_GR:
			if (gSimSPORManager.Event.btVendorSetEvent) {
				SimSPORSetEvent(SIM_SPOR_BASE_GR, SIM_SPOR_SUB_GR_VENDOR_EVENT);
			}
			break;
		default:
			break;
		}
	}
}

#endif /* SIM_SPOR_EN */

