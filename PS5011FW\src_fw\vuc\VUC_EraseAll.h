#ifndef _VUC_ERASEALL_H_
#define _VUC_ERASEALL_H_

#include "table/sys_area/sys_area_api.h"

#define VUC_ERASEALL_BLOCKPERCYCLE		(5)

//==============================================================================
//  Erase All Option
//==============================================================================
#define	VUC_ERASE_ALL_ERASE_ALL				(BIT0)
#define	VUC_ERASE_ALL_SKIP_ERASE_BAD_BLK	(BIT1)
#define	VUC_ERASE_ALL_SCAN_EARLY_BAD		(BIT2)
#define	VUC_ERASE_ALL_ERASE_DBT				(BIT3)		// 0: Keep DBT and don't erase bad block record in DBT
#define	VUC_ERASE_ALL_UPDATE_DBT			(BIT4)		// 1: Record erase fail and update to DBT
#define VUC_ERASE_ALL_CLEAR_LATER_BAD		(BIT5)
#define VUC_ERASE_ALL_CLEAR_ERASE_COUNT		(BIT6)
#define VUC_ERASE_ALL_FILL_DUMMY_ALL_BLK	(BIT7)
#define VUC_ERASE_ALL_PREPROGRAM_ALL_BLK	(BIT8)
#define VUC_ERASE_ALL_SKIP_ERASE_SYS_BLK    (BIT9)
#define VUC_ERASE_ALL_ERASE_COUNT_MASK		(0xFFFF0000)
#if (PS5017_EN)
#define VUC_ERASE_ALL_ERASE_COUNT_AND_D1_BIT_MASK		(0xFFFF0001)
#endif /* (PS5017_EN) */

#define VUC_ERASE_ALL_ERROR_PH_2ND_READ_DBT_HEADER_FAIL    (0xE0)
#define VUC_ERASE_ALL_ERROR_PH_BLK_LOST                    (0xE1)
#define VUC_ERASE_ALL_ERROR_PH_BLK_CHOOSE_THE_SAME         (0xE2)
#define VUC_ERASE_ALL_ERROR_SANDISK_PROGRAM_FAIL_TO_MUCH   (0xE3)
#define VUC_ERASE_ALL_ERROR_SET_MLBI_FAIL   			   (0xE4)

typedef enum ErseAll {
	ERASEALL_INIT,
	ERASEALL_FILL_DUMMY_ALL_BLK,
	ERASEALL_SEARCH_DBT_CODE,
	ERASEALL_CHECK_EARLYBAD_BLK,
	ERASEALL_ERASE,
	ERASEALL_BUILD_NEW_BAD_TABLE,
	ERASEALL_WAIT_BUILD_NEW_BAD_TABLE,
	ERASEALL_FINISH
} ErseAllStateEnum_t;

typedef struct {
	U16 uwEraseBlockIndex;
	U16 uwBlockPerCycle;
	U32 ulCodePCA[CODE_BLK_NUM_PER_CH * CODE_BLK_CH_NUM];
	U32 ulDBTPCA[SYSTEM_AREA_DBT_BLK_NUM];
	U32 ulPHPCA[PH_NUM];
	U16 uwFillDummyUnitIdx;
	U16 uwEraseMode;
	U8 ubDBTCount;
	U8 ubCodeCount;
	U8 ubPHCount;
	U8 ubEraseAllDoing;
	union {
		U8 ubAll;
		struct {
			U8 btDBTExist				: 1;
			U8 ubRsv				: 7;
		} B;
	} Others;

	ErseAllStateEnum_t State;
} EraseAllInfo_t;

extern EraseAllInfo_t gEraseAll;

void VUC_EraseAll(VUC_OPT_HCMD_PTR_t pCmd);

void VUC_EraseAllBLK(U8 ubSLCMode, U8 ubSelectMode);
void VUC_EraseAllBLK_State_EraseOldBadTable(U32 ulDBTPCA, U8 ubSLCMode);
void VUC_EraseAllBLK_State_Erase(U8 ubSLCMode, U8 ubSelectMode, U16 uwStartBlockIndex, U16 uwCount);

#endif /* _VUC_ERASEALL_H_ */
