#ifndef _VUC_SETSPECIFICGLOBALVALUE_H_
#define _VUC_SETSPECIFICGLOBALVALUE_H_

#include "aom/aom_api.h"
#include "vuc/VUC_command.h"

#define VUC_SET_TOTAL_EC		(0x00)
#define VUC_SET_AVAILABLE_SPARE	(0x01)
#define VUC_TOTAL_REDUCE_UNIT	(0x02)

extern U32 gulVUCAvailableSpare;
extern U32 gulVUCTotalReduceUnit;

AOM_NRW_2 void VUC_SetSpecificGlobalValue(VUC_OPT_HCMD_PTR_t pCmd);

#endif /* _VUC_SETSPECIFICGLOBALVALUE_H_ */
