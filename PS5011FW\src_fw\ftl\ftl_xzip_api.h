#ifndef _FTL_XZIP_API_H_
#define _FTL_XZIP_API_H_
#include "typedef.h"
#include "fw_vardef.h"
#include "mem.h"
#include "aom/aom_api.h"
#include "hal/xzip/xzip_cmd_type.h"
#include "hal/xzip/xzip_api.h"
#include "debug/debug_setup.h"

#define XZIP_E3D_INVALID            (0x0FFFFFFF)
#define XZIP_PCA_INVALID            (0xFFFFFFFF)
#define XZIP_LCA_INVALID            (0xFFFFFFFF)
#define XZIP_TWOPASS_INVALID        (0xFFFFFFFF)

#define XZIP_BLOCK_CLEAR			BIT0
#define CLEAR_XZIP_FLOW				(0)
#define CLEAR_XZIP_FLOW_DOING       (0)
#define CLEAR_XZIP_FLOW_FINISH      (1)

#if GC_DYNAMIC_DEBUG_UART_EN
#define GC_XZIP_HIT_CNT_LIMIT	(1)
#else
#if (DEBUG_CORNER_TEST_04_EN)
#define GC_XZIP_HIT_CNT_LIMIT	(1 + ((U32)RngGetRandomValue() & 3))
#else
#define GC_XZIP_HIT_CNT_LIMIT	(4)
#endif /* (DEBUG_CORNER_TEST_04_EN) */
#endif
#define XZIP_FIRST_HIT_TABLE_IN_4K_SIZE     (2)
#define XZIP_LCA_TABLE_IN_4K_SIZE           (1)
#define XZIP_RUNTIME_ADJUST_EN      FALSE
#define XZIP_BLOCK_ADJUST			BIT1
#define ADJUST_XZIP_FLOW			(1)
#define ADJUST_XZIP_FLOW_DOING		(0)
#define ADJUST_XZIP_FLOW_FINISH		(1)

#define XZIP_FIRST_HIT_TABLE_BASE_ADDR		(DBUF_GR_XZIP_FIRST_HIT_TABLE)		//(M_LB_TO_ADDR(LB_ID_FW, gFWLBMgr.Type[FWLB_XZIP_FIRST_HIT_TABLE].uwLBOffset))

#define XZIP_FIRST_HIT_TABLE_TOTAL_SIZE       (8192)

#define XZIP_FIRST_HIT_TABLE_ENTRY_SIZE       (8)         // 8 bytes = 4bytes PCA, 4bytes CRC

#define XZIP_FIRST_HIT_TABLE_TOTAL_ENTRY      (XZIP_FIRST_HIT_TABLE_TOTAL_SIZE/XZIP_FIRST_HIT_TABLE_ENTRY_SIZE)

#define XZIP_LCA_TABLE_OFFSET       (XZIP_FIRST_HIT_TABLE_TOTAL_SIZE)

#define XZIP_LCA_TABLE_BASE_ADDR    (XZIP_FIRST_HIT_TABLE_BASE_ADDR + XZIP_LCA_TABLE_OFFSET)

#define XZIP_LCA_TABLE_TOTAL_SIZE           (1024)

#define XZIP_CHECK_LIST_NUM		    (8) //MAX : 14

typedef enum {
	SEND_SET_FIRST_HIT_TABLE_VALUE_CMD,
	WAIT_SET_FIRST_HIT_TABLE_VALUE_CMD,
	SET_FIRST_HIT_TABLE_VALUE_CMD_DONE
} SetFirstHitTableValueStateEnum_t;

typedef enum {
	ADJUST_XZIP_ENTRY_INIT,
	ADJUST_XZIP_ENTRY_BLOCK_XZIP,
	ADJUST_XZIP_ENTRY_WAIT_XZIP_BARRIER,
	//ADJUST_XZIP_ENTRY_WAIT_XZIP_ADJ_LEN,
	ADJUST_XZIP_ENTRY_FINISH
} AdjustXZIPEntryStateEnum_t;

typedef enum {
	ALLOCATE_FIRST_HIT_TABLE_BUF_DOING,
	ALLOCATE_FIRST_HIT_TABLE_BUF_FINISH
} AllocateFirstHitTableBufStatus_t;

typedef enum {
	SET_FIRST_HIT_TABLE_DOING,
	SET_FIRST_HIT_TABLE_FINISH
} SetFirstHitTableStatus_t;

typedef enum {
	CLEAR_XZIP_ENTRY_INIT,
	CLEAR_XZIP_ENTRY_BLOCK_XZIP,
	CLEAR_XZIP_ENTRY_WAIT_XZIP_BARRIER,
	CLEAR_XZIP_ENTRY_WAIT_XZIP_CLEAR_REG,
	CLEAR_XZIP_ENTRY_FINISH
} ClearXZIPEntryStateEnum_t;

typedef struct {
	union {
		U8 ubControl;
		struct {
			U8 btNowBlockByClear	: 1;
			U8 btNowBlockByAdjust	: 1;
			U8 btNowNeedClear		: 1;
			U8 btNowNeedAdjust		: 1;
			U8 						: 4;
		} B;
	} Control;
	U8 ubXZIPEn;
	U8 ubGCXZIPEn;
	ClearXZIPEntryStateEnum_t ClearXZIPEntryState;
	AdjustXZIPEntryStateEnum_t AdjustXZIPEntryState;
} XZIPMudule_t;

typedef struct {
	U32 CRC24    : 28;//E3D 24bit + 4bit for E3D invalid
	U32 ubHitCnt : 4;
	PCA_t ulPCA;
} XZIPFirstHitTableEntry_t;

typedef struct {
	U32 ulLCA;
} XZIPLCATableEntry_t;

typedef struct {
	struct {
		U16 uwPTR;
	} FirstHitTableInfo;
	struct {
		PCA_t ulPCAList[XZIP_CHECK_LIST_NUM];
		U8	Num 				: 4;
		U8	btFirstHitTableFull : 1;
		U8	btListFull			: 1;
		U8  btXZIPAllMiss		: 1;
		U8 	btReserved			: 1;
	} EntryFullInfo;
} XZIPFirstHitTableInfo_t;

extern XZIPMudule_t gXZIP;

/***********************************************
 *      Function
 ***********************************************/
void InsertFirstHitTable(PCA_t ulPCA, U32 ulCRC24, U32 ulLCA);
U8 CheckXZIPFirstHitTableIsFull(void);
SetFirstHitTableStatus_t ResetXZIPFirstHitTableValue(U8 ubNeedWait);
#if (!MICRON_FSP_EN)
AOM_INIT AllocateFirstHitTableBufStatus_t AllocateFirstHitTableBuf(U8 ubNeedWait);
#endif /* (!MICRON_FSP_EN) */
U8 ProgramXZIPFirstHitTable(Unit_t uwCurrentGRUnit);
U8 FTLCheckNeedProgramFirstHitTable(Unit_t uwCurrentGRUnit);
AOM_SPOR U8 CheckIsFirstHitTable(U32 ulPlaneIndex, U8 ubIsSLCMode, U8 ubSingleSLC);

U8 ClearXZIPEntryFlow(void);
U8 AdjustXZIPEntryFlow(U8 ubSize);

U8 GetXZIPFirstHitTableFullFlag(void);
U8 GetXZIPFirstHitTableXZIPAllMiss(void);
void FullXZIPFirstHitFlow(PCA_t ulPCA);
U8 FindXZIPFullList(PCA_t ulPCA);

U8 GetXZIPFirstHitTablePlaneNum(void);
void InitXZIPEntryFullParam(void);
void BlockXZIP(U8 ubDoingFlow);
void UnblockXZIP(void);
void XZIPCleanRegDone_CallBack(XZIPCQRst_t *pXZIPCQRst);
void ClearXZIPBuildAndHitBitOnWLB(void);
void CheckXZIPHitCntFull(void);
AOM_INIT_2 void InitXZIPControlModule(void);
#endif /* _FTL_XZIP_API_H_ */
