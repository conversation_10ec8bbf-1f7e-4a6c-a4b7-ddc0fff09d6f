/****************************************************************************
*
*  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
*  All rights reserved
*
*  The content of this document is confidential and shall be applied
*  subject to the terms and conditions of the license agreement and
*  other applicable laws. Any unauthorized access, use or disclosure
*  of this document is strictly prohibited and may be punishable
*  under laws.
*
*  nvme_ata_security.h
*
*
*
****************************************************************************/

#ifndef NVME_ATA_SECURITY_H_
#define NVME_ATA_SECURITY_H_

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "nvme_api/nvme_ata_security/nvme_ata_security_api.h"
#include "hal/apu/apu_api.h"
/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */
#define NVME_ATA_SECURITY_PASSWORD_MAX_ATTEMPT_CNT                          (5)
#define NVME_ATA_SECURITY_DEFAULT_MASTER_PASSWORD_ID                        (0xFFFE)
#define NVME_ATA_SECURITY_MASTER_PASSWORD_ID_NOT_SUPPORT_DEFAULT_1 			(0x0000)
#define NVME_ATA_SECURITY_MASTER_PASSWORD_ID_NOT_SUPPORT_DEFAULT_2 			(0xFFFF)

#define NVME_ATA_SECURITY_GET_STATUS_TRANSFER_LENGTH            (0x10)
#define NVME_ATA_SECURITY_GET_STATUS_PARAMETER_LIST_LENGTH      (0x0E)
#define NVME_ATA_SECURITY_SEND_PAYLOAD_EXIST_TRANSFER_LENGTH    (0x24)
#define NVME_ATA_SECURITY_SEND_NO_PAYLOAD_TRANSFER_LENGTH       (0x00)

#define NVME_ATA_SECURITY_ENHANCE_ERASE_TIME_128GB_IN_SECOND    (0x0014)  //unit: second
#define NVME_ATA_SECURITY_NORMAL_ERASE_TIME_IN_SECOND           (0x0005)  //unit: second

#define NVME_ATA_SECURITY_MAX_PASSWORD_LENGTH	(32)

#define NVME_ATA_SECURITY_MASTER_PASSWORD_CAPABILITY_HIGH   (0)
#define NVME_ATA_SECURITY_MASTER_PASSWORD_CAPABILITY_MAX    (1)
/*
 * ---------------------------------------------------------------------------------------------------
 *  data types
 * ---------------------------------------------------------------------------------------------------
 */
typedef struct {
	U8 btMAXLVL:    1;
	U8 Reserved1:	7;
	U8 btMSTRPW:	1;
	U8 Reserved2:	7;
	U8 aubPassword[NVME_ATA_SECURITY_MAX_PASSWORD_LENGTH];
	U8 ubMasterIDHigh;
	U8 ubMasterIDLow;
} NVMeATASecurityParameterSetPassword_t;

typedef struct {
	U8 ubReserved;
	U8 btMSTRPW:    1;
	U8 Reserved2: 	7;
	U8 aubPassword[NVME_ATA_SECURITY_MAX_PASSWORD_LENGTH];
	U8 aubReserved3[2];
} NVMeATASecurityParameterUnlock_t;

typedef struct {
	U8 ubReserved;
	U8 btMSTRPW:    1;
	U8 Reserved2: 	7;
	U8 aubPassword[NVME_ATA_SECURITY_MAX_PASSWORD_LENGTH];
	U8 aubReserved3[2];
} NVMeATASecurityParameterDisPassword_t;

typedef struct {
	U8 btEN_ER:		1;
	U8 Reserved:	7;
	U8 btMSTRPW:	1;
	U8 Reserved2:	7;
	U8 aubPassword[NVME_ATA_SECURITY_MAX_PASSWORD_LENGTH];
	U8 aubReserved3[2];
}  NVMeATASecurityParameterErase_t;

typedef enum NVMeATASecurityComparePasswordResult {
	NVME_ATA_SECURITY_NOT_COMPARE = 0,
	NVME_ATA_SECURITY_MASTER_PASSWORD_CORRECT,
	NVME_ATA_SECURITY_MASTER_PASSWORD_INCORRECT,
	NVME_ATA_SECURITY_USER_PASSWORD_CORRECT,
	NVME_ATA_SECURITY_USER_PASSWORD_INCORRECT,
	NVME_ATA_SECURITY_NOT_ENABLE_ABORT_CMD,
} NVMeATASecurityComparePasswordResult_t;

typedef struct {
	U8  ubReserved1;
	U8  ubParameterListLength;
	U8  ubNormalEraseTimeHigh;
	U8  ubNormalEraseTimeLow;
	U8  ubEnhanceEraseTimeHigh;
	U8  ubEnhanceEraseTimeLow;
	U8  uwMasterPasswordIDHigh;
	U8  uwMasterPasswordIDLow;
	U8  btMaxSet    : 1;
	U8  Reserved2   : 7;
	union {
		U8 ubAll;
		struct {
			U8 btSupport 				: 1;
			U8 btEn 					: 1;
			U8 btLocked        			: 1;
			U8 btFrozen        			: 1;
			U8 btExpired        		: 1;
			U8 btEnhanceEraseSupport	: 1;
			U8 Reserved3 				: 2;
		};
	} Status;
} NVMeATASecurityStatus_t;
/*
 * ---------------------------------------------------------------------------------------------------
 *   macros
 * ---------------------------------------------------------------------------------------------------
 */
#define M_NVME_ATA_SECURITY_SET_LOCK() do{\
	M_SECURITY_SET_GLOBAL_RANGE_WRITE_LOCK(SECURITY_DEFAULT_NSID, TRUE);\
	M_SECURITY_SET_GLOBAL_RANGE_READ_LOCK(SECURITY_DEFAULT_NSID, TRUE);\
	SET_AES_ACTIVE();\
} while (0)
#define M_NVME_ATA_SECURITY_CLEAR_LOCK() do{\
	M_SECURITY_SET_GLOBAL_RANGE_WRITE_LOCK(SECURITY_DEFAULT_NSID, FALSE);\
	M_SECURITY_SET_GLOBAL_RANGE_READ_LOCK(SECURITY_DEFAULT_NSID, FALSE);\
	CLR_AES_ACTIVE();\
} while (0)
/*
 * ---------------------------------------------------------------------------------------------------
 *	public prototypes
 * ---------------------------------------------------------------------------------------------------
 */
#endif // NVME_ATA_SECURITY_H_
