#ifndef _VUC_PCIEEYEINFO_H_
#define _VUC_PCIEEYEINFO_H_

#if (NVME == HOST_MODE)

#include "aom/aom_api.h"

#define VUC_DO_PCIE_EYE_OPEN    (0x00)
#define VUC_DO_PCIE_EYE_MONITOR (0x01)

#define VUC_TARGET_LANE_NOT_SET (0xFF)

AOM_VUC void VUC_DoPCIEEyeFlow(VUC_OPT_HCMD_PTR_t pCmd);
AOM_VUC void VUC_GetPCIEEyeInfo(VUC_OPT_HCMD_PTR_t pCmd);

#endif /* (NVME == HOST_MODE) */

#endif /* _VUC_PCIEEYEINFO_H_ */
