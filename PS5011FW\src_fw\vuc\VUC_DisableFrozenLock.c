#include "vuc/VUC_DisableFrozenLock.h"
#include "typedef.h"
#include "hal/sata/sata_api.h"
#include "hal/sata/sata.h"
#include "table/initinfo_vt/initinfo_vt.h"


#if (SATA == HOST_MODE)

#if (!BURNER_MODE_EN)

void VUC_DisableFrozenLock(void)
{
	AtaCfg->ubSecurityStatus &= ~(SATA_SECURITY_FROZEN_BIT | SATA_SECURITY_LOCK_BIT);
	// TODO: DCO State clear??
	//AtaCfg->DCOState &= ~(SET_DCO_LOCK); // clear DCO lock
	//while (FALSE == SATASaveHostTable(SATA_HOST_DBUF_NO_STATE, SAVE_REASON_NORMAL));

}

#endif /* (!BURNER_MODE_EN) */

#endif /* (SATA == HOST_MODE) */
