#ifndef _MUX_H_
#define _MUX_H_

#include "typedef.h"
#include "hal/sys/reg/sys_pd0_reg.h"

#define	M_GPIO_OE_CR()							(R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL0] &= ~CR_MUX_GPIO_OE_BIT)
#define	M_GPIO_OE_PD()							(R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL0] |= CR_MUX_GPIO_OE_BIT)
#define	M_GPIO_PD_CR()							(R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL0] &= ~CR_MUX_PD_OE_BIT)
#define	M_GPIO_PD_XPSW()						(R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL0] |= CR_MUX_PD_OE_BIT)
#define	M_DISABLE_SPI_CS_OUTPUT()				(R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL0] &= ~CR_MUX_SPI_CS_OE_BIT)
#define	M_ENABLE_SPI_CS_OUTPUT()				(R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL0] |= CR_MUX_SPI_CS_OE_BIT)
#define	M_CLR_MUX_GPIO(X)						(R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL0] &= ~BIT(X))
#define	M_SET_MUX_GPIO(X)						(R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL0] |= BIT(X))
#define M_CLR_MUX()								(R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL0] = 0)
#define	M_WPB_ENABLE_BY_FLH_IP()				(R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL1] &= ~CR_MUX_FLH_WPB_BIT)
#define	M_WPB_ENABLE_BY_SYSTEM()				(R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL1] |= CR_MUX_FLH_WPB_BIT)
#define	M_COSO_ENABLE_BY_FLH_IP()				(R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL1] &= ~CR_MUX_FLH_COSO_BIT)
#define	M_COSO_ENABLE_BY_SYSTEM()				(R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL1] |= CR_MUX_FLH_COSO_BIT)
#define	M_ODT_ENABLE_BY_FLH_IP()				(R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL1] &= ~CR_MUX_FLH_ODT_BIT)
#define	M_ODT_ENABLE_BY_SYSTEM()				(R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL1] |= CR_MUX_FLH_ODT_BIT)
#if PS5013_EN
#define	M_MPHYRSTN_ENABLE()						(R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL1] |= CR_MUX_MPHYRSTN_BIT)
#endif /* PS5013_EN */
#define	M_DISABLE_DEBUG_PORT(X)					(R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL1] &= ~((U32)(X)))
#define	M_ENABLE_DEBUG_PORT(X)					(R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL1] |= ((U32)(X)))

#if PS5017_EN
#define	M_XDB_PSEL(X)							(((U32)(X) & (XDB_PSEL_MASK) << XDB_PSEL_SHIFT))
#define	M_XDB_TSEL(X)							(((U32)(X) & (XDB_TSEL_MASK) << XDB_TSEL_SHIFT))
#define M_XDB_ROTATE(X)							((U32)(X) & (XDB_ROTATE_MASK << XDB_ROTATE_SHIFT))
#define	M_DEBUG_PORT_SEL(ROTATE,TSEL, PSEL)		(R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL3] = (ROTATE << XDB_ROTATE_SHIFT)|(TSEL << XDB_TSEL_SHIFT)|(PSEL << XDB_PSEL_SHIFT))//(M_XDB_ROTATE(ROTATE) | M_XDB_TSEL(TSEL) | M_XDB_PSEL(PSEL)))
#elif PS5021_EN /* PS5017_EN */
#define	M_XDB_PSEL(X)							(R8_SYS0_MISC_CTRL[R8_SYS0_HW_DBG_IP_SEL] = (X) & DBG_IP_SEL_MASK)
#define	M_XDB_TSEL(X)							(R8_SYS0_MISC_CTRL[R8_SYS0_HW_DBG_PORT_SEL] = (X) & DBG_PORT_SEL_MASK)
#define	M_DEBUG_PORT_SEL(TSEL, PSEL)			do{ \
												M_XDB_TSEL(TSEL); \
												M_XDB_PSEL(PSEL); \
												} while(0)
#else /* PS5017_EN */
#define	M_XDB_PSEL(X)							(((U32)(X) & (XDB_PSEL_MASK << XDB_PSEL_SHIFT)) << 0)
#define	M_XDB_TSEL(X)							(((U32)(X) & (XDB_TSEL_MASK << XDB_TSEL_SHIFT)) << 8)
#define	M_DEBUG_PORT_SEL(TSEL, PSEL)			(R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL3] = (XDB_FPGA_SEL_EN_BIT | XDB_TSEL(TSEL) | XDB_PSEL(PSEL)))
#endif /* PS5017_EN */

#if PS5021_EN
#define	M_DEBUG_PORT_ENABLE()					(R32_SYS0_MISC_CTRL[R32_SYS0_HW_DBG_CTRL_PD0] |= SR_DBG_OUT_PD0_EN_BIT)
#else /* PS5021_EN */
#define	M_DEBUG_PORT_ENABLE()					(R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL3] |= XDB_FPGA_SEL_EN_BIT)
#define	M_DEBUG_PORT_SEL_TSEL(TSEL)				(R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL3] = (TSEL << XDB_TSEL_SHIFT))
#define	M_DEBUG_PORT_CLR_TSEL()					(R32_SYS_MUX[R32_SYS0_SYS_MUX_CTRL3] &=0xFFFFFF00FF)
#endif /* PS5021_EN */
#endif /* _MUX_H_ */
