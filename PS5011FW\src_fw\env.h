/** @file env.h
 *  @brief Firmware environment for compiler
 *
 *	Define symbols for compiler compatible
 *
 */
#ifndef ENV_H_
#define ENV_H_
#include "symbol.h"

#undef _MSC_VER

#ifdef _MSC_VER		// Visual Studio Predefined Macro, MicroSoft C++ compiler version
#include "sim_env.h"
#define VS_SIM_EN						TRUE
#else /* _MSC_VER */
#define VS_SIM_EN						FALSE
#endif /* _MSC_VER */

#define RAIDECC_ENCODE_EN		(TRUE)

//==============================================================================
//	Compiler Defined Parameter
//==============================================================================

#ifdef _BUILD_MODE_						/* Configuration Symbol in DS-5 IDE */
#if (_BUILD_MODE_ == BUILD_BOOT_LOADER)
#define BOOTLOADER_MODE_EN				(TRUE && BOOTLOADER_EN)
#define BURNER_MODE_EN					(FALSE)
#define LPM3_LOADER						(FALSE)
#elif (_BUILD_MODE_ == BUILD_BURNER)
#define BOOTLOADER_MODE_EN				(FALSE)
#define BURNER_MODE_EN					(TRUE)
#define LPM3_LOADER						(FALSE)
#elif (_BUILD_MODE_ == BUILD_LPM_LOADER)
#define BOOTLOADER_MODE_EN				(FALSE)
#define BURNER_MODE_EN					(FALSE)
#define LPM3_LOADER						(TRUE)
#else /*(_BUILD_MODE_ == BUILD_BOOT_LOADER)*/
#define BOOTLOADER_MODE_EN				(FALSE)
#define BURNER_MODE_EN					(FALSE)
#define LPM3_LOADER						(FALSE)
#endif /*(_BUILD_MODE_ == BUILD_BOOT_LOADER)*/
#else /* _BUILD_MODE_ */
#define BOOTLOADER_MODE_EN				(FALSE)
#define BURNER_MODE_EN					(FALSE)
#define LPM3_LOADER						(FALSE)
#endif /* _BUILD_MODE_ */

#ifdef _RDT_                            /* Configuration Symbol in DS-5 IDE */
#define RDT_MODE_EN                     (_RDT_)
#else /* _RDT_ */
#define RDT_MODE_EN                     (FALSE)
#endif /* _RDT_ */

/*================== Add For U17 Sorting ===================*/
#define MST_MODE_EN                     (1)
/*
1. RDT_RUN_ONLINE=0                       -->  Disable MST Online Mode
2. RDT_RUN_ONLINE=1, RDT_UART_TRANSFER=0  -->  Enable MST Online Mode, Only Support USB VUC
3. RDT_RUN_ONLINE=1, RDT_UART_TRANSFER=1  -->  Enable MST Online Mode, Only Support UART VUC
*/
#define RDT_RUN_ONLINE				 	(0)
#define RDT_UART_TRANSFER				(0) //UART_VUC_MODE_EN will be enable but not use XModem in this mode

#define RDT_RECORD_SCAN_WINDOW_LOG		(1)
#define RDT_RECORD_ECC_DISTRIBUTION  	(1)
#define RDT_SCAN_WINDOW_TEST			(0)

#define RDT_FLASH_TEST_SEQUENCE_FIX		(1)
#define RDT_CONTINUE_TEST_PROGRAM_FAIL  (0)

//for flash rdy time test enable or not		//Reip
#define RDT_RDY_TIME_TEST 				(1)
#define	RDT_RECORD_ALL_BLOCK_EDL		(1)	//Reip
#define RDT_REFERENCE_WINDOW_RESULT     (1)

#define RDT_GET_UNIQUE_ID				(1)
#define RDT_RETRY_CMD_VERIFY			(1)	//Reip test
#define RDT_FIX_SINGLE_CE_ISSUE			(1)

#define RDT_FAST_TEST_MODE				(0)
#define RDT_SHOW_UART_LOG				(0)
#define RDT_BYPASS_AP_KEY				(1)
#define RDT_DEBUG						(0)

#define RDT_DO_SRAM_TEST				(0)
#define RDT_DO_IC_TEST					(0)
/*==========================================================*/

#ifdef _RDT_BURNER_                     /* Configuration Symbol in DS-5 IDE */
#define RDT_BURNER_MODE_EN              (_RDT_BURNER_)
#else /* _RDT_BURNER_ */
#define RDT_BURNER_MODE_EN              (FALSE)
#endif /* _RDT_BURNER_ */

#ifdef CATEGORY_CONTROLLER
#define FW_CATEGORY_CONTROLLER			(CATEGORY_CONTROLLER)
#else /* CATEGORY_CONTROLLER */
#define FW_CATEGORY_CONTROLLER			(CONTROLLER_NONE)
#endif /* CATEGORY_CONTROLLER */

#ifdef CATEGORY_CUSTOMER				/* Configuration Symbol in DS-5 IDE */
#define FW_CATEGORY_CUSTOMER			(CATEGORY_CUSTOMER)
#else /* CATEGORY_CUSTOMER */
#define FW_CATEGORY_CUSTOMER			(CUSTOMER_NONE)
#endif /* CATEGORY_CUSTOMER */

#ifdef CATEGORY_FLASH					/* Configuration Symbol in DS-5 IDE */
#define FW_CATEGORY_FLASH				(CATEGORY_FLASH)
#else /* CATEGORY_FLASH */
#define FW_CATEGORY_FLASH				(FLASH_NONE)
#endif /* CATEGORY_FLASH */

#ifdef _HOST_MODE_					/* Configuration Host mode in DS-5 IDE */
#define HOST_MODE				(_HOST_MODE_)
#else /* CATEGORY_FLASH */
#define HOST_MODE				(HOST_MODE_NONE)
#endif /* CATEGORY_FLASH */

#ifdef _FW_BUILD_VERSION_				/* Configuration Loader mode in DS-5 IDE */
#define FW_BUILD_VERSION		(_FW_BUILD_VERSION_)
#else /* _FW_BUILD_VERSION_ */
#define FW_BUILD_VERSION		(FW_VERSION_FULL_MAX_XZIP_ON)
#endif /* _FW_BUILD_VERSION_ */

#ifdef _TCG_MODE_
#define TCG_MODE						(_TCG_MODE_)
#else /* _TCG_MODE_ */
#define TCG_MODE						(TCG_MODE_COMBINED)
#endif /* _TCG_MODE_ */

#ifdef _RELEASED_FW_					/* Configuration Loader mode in DS-5 IDE */
#define RELEASED_FW						(_RELEASED_FW_)
#else /* _RELEASED_FW_ */
#define RELEASED_FW						(FALSE)
#endif /* _RELEASED_FW_ */

#ifdef _OPEN_BLOCK_RSMAP_EN_			/* Configuration Loader mode in DS-5 IDE */
#define OPEN_BLOCK_RSMAP_EN				(_OPEN_BLOCK_RSMAP_EN_ && RAIDECC_ENCODE_EN)
#else /* _OPEN_BLOCK_RSMAP_EN_ */
#define OPEN_BLOCK_RSMAP_EN				(TRUE && RAIDECC_ENCODE_EN)
#endif /* OPEN_BLOCK_RSMAP_EN */

#ifdef _PARTIAL_BLOCK_MEDIA_SCAN_EN_
#define PARTIAL_BLOCK_MEDIA_SCAN_EN      (_PARTIAL_BLOCK_MEDIA_SCAN_EN_)
#else
#define PARTIAL_BLOCK_MEDIA_SCAN_EN      (FALSE)
#endif

#ifdef _RETRY_MICRON_NICKS_
#define RETRY_MICRON_NICKS      (_RETRY_MICRON_NICKS_)
#else
#define RETRY_MICRON_NICKS      (FALSE)
#endif

#ifdef _RETRY_MICRON_NICKS_ALL_PAGE_TYPE_SB_SUPPORT_
#define RETRY_MICRON_NICKS_ALL_PAGE_TYPE_SB_SUPPORT      (_RETRY_MICRON_NICKS_ALL_PAGE_TYPE_SB_SUPPORT_)
#else
#define RETRY_MICRON_NICKS_ALL_PAGE_TYPE_SB_SUPPORT      (FALSE)
#endif

#ifdef _RANDOMIZER_3D_AGITATION_RULE_
#define RANDOMIZER_3D_AGITATION_RULE				(_RANDOMIZER_3D_AGITATION_RULE_)
#else	/* RANDOMIZER_3D_AGITATION_RULE */
#define RANDOMIZER_3D_AGITATION_RULE				(FALSE)
#endif /* RANDOMIZER_3D_AGITATION_RULE */

#ifdef _D1_UNIT_EN_
#define D1_UNIT_EN				(_D1_UNIT_EN_)
#else	/* _D1_UNIT_EN_ */
#error "D1 Unit Enable no setting"
#endif /* _D1_UNIT_EN_ */

#ifdef _LUN_FOR_MORE_UNIT_EN_
#define LUN_FOR_MORE_UNIT_EN				(_LUN_FOR_MORE_UNIT_EN_)
#else	/* _LUN_FOR_MORE_UNIT_EN_ */
#define LUN_FOR_MORE_UNIT_EN				(TRUE)
#endif /* _LUN_FOR_MORE_UNIT_EN_ */

#ifdef _SUPPORT_4TB_SPARE_LCA_
#define SUPPORT_4TB_SPARE_LCA				(_SUPPORT_4TB_SPARE_LCA_)
#else /* _RELEASED_FW_ */
#define SUPPORT_4TB_SPARE_LCA				(FALSE)
#endif /* _RELEASED_FW_ */

//==============================================================================
//	Environment Define
//==============================================================================
#if VS_SIM_EN
#define INLINE								static __inline
#define NO_INLINE							//null
#define COUNT_ONE32(VAL)					//null
#define STATIC_ASSERT(EXPR, MSG)			static_assert((EXPR), MSG)
#else/* VS_SIM_EN*/

#if RDT_MODE_EN //SHRINK_CODE_SIZE
#define INLINE								static
#else
#define INLINE								static __attribute__((always_inline))
#endif
#define NO_INLINE                           __attribute__((noinline))
#define COUNT_ONE32(VAL)					__builtin_popcount((VAL))
#define FORCE_ADDR(ADDR)					__attribute__((section(".ARM.__at_" #ADDR)))
#define STATIC_ASSERT(EXPR, MSG)			_Static_assert((EXPR), MSG)
#define M_SIM_LB_ADDR_TO_VADDR(ADDR)
#define M_SIM_PB_ADDR_TO_VADDR(ADDR)
#endif/* VS_SIM_EN*/
#define WEAK								__attribute__((weak))

#define FPGA_RTL_2CH					(FALSE) // support FLH2ch_NO_PCIE_ZIP_SEC_NHB

#define FPGA_USE_V7						(0)
#define FPGA_USE_ULTRA					(1)
#define FPGA_USE_SIM_CODE				(2)

//#define FLH_USE_FIP						(0)
#define FLH_USE_COP0					(1)
#define FLH_USE_SIM_CODE				(2)

#define FLASH_SLC						(1)
#define FLASH_MLC						(2)
#define FLASH_TLC						(3)
#define FLASH_QLC						(4)	//Reip Porting 3D-V7 QLC Add

#define FLASH_TYPE_TOSHIBA_2D_TLC               (0)
#define FLASH_TYPE_TOSHIBA_3D_TLC               (1)		// BiCs3,
#define FLASH_TYPE_HYNIX_2D_TLC                 (2)
#define FLASH_TYPE_HYNIX_3D_TLC                 (3)
#define FLASH_TYPE_MICRON_3D_MLC                (4)
#define FLASH_TYPE_MICRON_3D_TLC                (5)
#define FLASH_TYPE_SANDISK_3D_TLC               (6)
#define FLASH_TYPE_TOSHIBA_SLC                  (7)		// 8k SLC
#define FLASH_TYPE_TOSHIBA_3D_QLC				(8)
#define FLASH_TYPE_MICRON_3D_QLC				(9)     //zerio n48r add
#define FLASH_TYPE_YMTC_3D_TLC                  (10)
#define FLASH_TYPE_BICSX_3D_TLC 				(11)	//Duson Porting BICS5 Add//zerio BICS8 Add
#define FLASH_TYPE_HYNIX_3D_QLC                 (12)	//Reip Porting 3D-V7 QLC Add
#define FLASH_TYPE_YMTC_3D_QLC					(13)	//ems mst add--karl
#define FLASH_TYPE_SAMSUNG_3D_TLC				(14)	//samsung add
#define FLASH_TYPE_BICSX_3D_QLC 				(15)	//zerio bics6 qlc add
#define FLASH_TYPE_INTEL_3D_QLC					(16)	//intel n38a add

#if !VS_SIM_EN
#define NULL							0
#define	EnterCriticalSection(X)
#define	LeaveCriticalSection(X)
#define	InitializeCriticalSection(X)
#define	hw_que_push(X,Y)				(1)
#define	hw_que_pop(X,Y)					(1)
#define	hw_que_blocking_push(X,Y)
#define	hw_que_wait_not_empty(X)
#endif

//************* Board ******************
#if VS_SIM_EN
#define FPGA_BOARD	FPGA_USE_SIM_CODE
#else /* VS_SIM_EN */
#if (FPGA_RTL_2CH == TRUE)
#define FPGA_BOARD	FPGA_USE_V7
#else /*(FPGA_RTL_2CH == TRUE)*/
#define FPGA_BOARD	FPGA_USE_ULTRA
#endif /*(FPGA_RTL_2CH == TRUE)*/
#define FPGA_BOARD_RTL_NEW_CLKCTR		(TRUE && (FPGA_BOARD == FPGA_USE_ULTRA))
#endif /* VS_SIM_EN */

//************* Controller ******************
#define PS5013_EN						(FW_CATEGORY_CONTROLLER == CONTROLLER_PS5013)
#define PS5913_EN						(FW_CATEGORY_CONTROLLER == CONTROLLER_PS5913)
#define PS5017_EN						(FW_CATEGORY_CONTROLLER == CONTROLLER_PS5017)
#define PS5019_EN						(FW_CATEGORY_CONTROLLER == CONTROLLER_PS5019)
#define PS5021_EN						(FW_CATEGORY_CONTROLLER == CONTROLLER_PS5021)

//************* FLH *********************
#define YMTC_EN					(FW_CATEGORY_FLASH == FLASH_JGSTLC)


// for Micron TLC/QLC FLH
#if (FW_CATEGORY_FLASH == FLASH_MICRONTLC)
#define MICRON_FSP_EN			(TRUE)
#define IM_B17					(FALSE)			// for B16/B17
#define IM_B27A					(FALSE)
#define IM_B27B					(FALSE)
#define IM_N18					(FALSE)
#define IM_N28					(FALSE)
#define IM_B47R					(TRUE)
#define IM_N48R					(FALSE)
#define IM_N48R_NEED_CHECK		(FALSE)
#define HYNIX_FSP_EN			(FALSE)
#define IM_V6					(FALSE)
#elif (FW_CATEGORY_FLASH == FLASH_B27A_TLC)
#define MICRON_FSP_EN			(TRUE)
#define IM_B17					(FALSE)
#define IM_B27A					(TRUE)
#define IM_B27B					(FALSE)
#define IM_N18					(FALSE)
#define IM_N28					(FALSE)
#define IM_B47R					(FALSE)
#define IM_N48R					(FALSE)
#define IM_N48R_NEED_CHECK		(FALSE)
#define HYNIX_FSP_EN			(FALSE)
#define IM_V6					(FALSE)
#elif (FW_CATEGORY_FLASH == FLASH_N18_QLC)
#define MICRON_FSP_EN			(TRUE)
#define IM_B17					(FALSE)
#define IM_B27A					(FALSE)
#define IM_B27B					(FALSE)
#define IM_N18					(TRUE)
#define IM_N28					(FALSE)
#define IM_B47R					(FALSE)
#define IM_N48R					(FALSE)
#define IM_N48R_NEED_CHECK		(FALSE)
#define HYNIX_FSP_EN			(FALSE)
#define IM_V6					(FALSE)
#elif (FW_CATEGORY_FLASH == FLASH_N28_QLC)
#define MICRON_FSP_EN			(TRUE)
#define IM_B17					(FALSE)
#define IM_B27A					(FALSE)
#define IM_B27B					(FALSE)
#define IM_N18					(FALSE)
#define IM_N28					(TRUE)
#define IM_B47R					(FALSE)
#define IM_N48R					(FALSE)
#define IM_N48R_NEED_CHECK		(FALSE)
#define HYNIX_FSP_EN			(FALSE)
#define IM_V6					(FALSE)
#elif (FW_CATEGORY_FLASH == FLASH_B37R_TLC)
#define MICRON_FSP_EN			(TRUE)
#define IM_B17					(FALSE)
#define IM_B27B					(FALSE)
#define IM_N18					(FALSE)
#define IM_N28					(FALSE)
#define IM_B27A                 (FALSE)
#define IM_B37R					(TRUE)
#define IM_B47R					(FALSE)
#define IM_N48R					(FALSE)
#define IM_BICS5				(FALSE)
#define IM_BICS6				(FALSE)
#define IM_BICS6_QLC			(FALSE)
#define IM_BICS8				(FALSE)
#define IM_N48R_NEED_CHECK		(FALSE)
#define N48_GC_TEST				(FALSE) // khaki: for GC read verify
#define HYNIX_FSP_EN			(FALSE)
#define SANDISK_FSP_EN			(FALSE)
#define YMTC_FSP_EN			    (FALSE)
#define SAMSUNG_FSP_EN			(FALSE)
#define INTEL_FSP_EN			(FALSE)
#define IM_V5					(FALSE)
#define IM_V6					(FALSE)
#define IM_V7					(FALSE)
#define IM_V8					(FALSE)
#elif (FW_CATEGORY_FLASH == FLASH_B47R_TLC)
#define MICRON_FSP_EN			(TRUE)
#define IM_B17					(FALSE)
#define IM_B27B					(FALSE)
#define IM_N18					(FALSE)
#define IM_N28					(FALSE)
#define IM_B27A					(FALSE)
#define IM_B47R					(TRUE)
#define IM_B37R					(FALSE)
#define IM_N48R					(FALSE)
#define IM_BICS5				(FALSE)
#define IM_BICS6				(FALSE)
#define IM_BICS6_QLC			(FALSE)
#define IM_BICS8				(FALSE)
#define IM_N48R_NEED_CHECK		(FALSE)
#define N48_GC_TEST				(FALSE) // khaki: for GC read verify
#define HYNIX_FSP_EN			(FALSE)
#define SANDISK_FSP_EN			(FALSE)
#define YMTC_FSP_EN			    (FALSE)
#define SAMSUNG_FSP_EN			(FALSE)
#define INTEL_FSP_EN			(FALSE)
#define IM_V5					(FALSE)
#define IM_V6					(FALSE)
#define IM_V7					(FALSE)
#define IM_V8					(FALSE)
#elif (FW_CATEGORY_FLASH == FLASH_N48R_QLC)//zerio n48r add
#define MICRON_FSP_EN			(TRUE)
#define IM_B17					(FALSE)
#define IM_B27B					(FALSE)
#define IM_N18					(FALSE)
#define IM_N28					(FALSE)
#define IM_B27A					(FALSE)
#define IM_B47R					(FALSE)
#define IM_B37R					(FALSE)
#define IM_N48R					(TRUE)
#define IM_BICS5				(FALSE)
#define IM_BICS6				(FALSE)
#define IM_BICS6_QLC			(FALSE)
#define IM_BICS8				(FALSE)
#define IM_N48R_NEED_CHECK		(FALSE)
#define N48_GC_TEST				(FALSE) // khaki: for GC read verify
#define HYNIX_FSP_EN			(FALSE)
#define SANDISK_FSP_EN			(FALSE)
#define YMTC_FSP_EN			    (FALSE)
#define SAMSUNG_FSP_EN			(FALSE)
#define INTEL_FSP_EN			(FALSE)
#define IM_V5					(FALSE)
#define IM_V6					(FALSE)
#define IM_V7					(FALSE)
#define IM_V8					(FALSE)
#elif(FW_CATEGORY_FLASH == FLASH_V6_TLC)//Dylan 2022/03/10 porting flow from V6 RDT
#define HYNIX_FSP_EN			(TRUE)
#define YMTC_FSP_EN			    (FALSE)
#define SAMSUNG_FSP_EN			(FALSE)
#define INTEL_FSP_EN			(FALSE)
#define IM_V5					(FALSE)
#define IM_V6					(TRUE)
#define IM_V7					(FALSE)
#define IM_V8					(FALSE)
#define IM_BICS5				(FALSE)
#define IM_BICS6				(FALSE)
#define IM_BICS6_QLC			(FALSE)
#define IM_BICS8				(FALSE)
#define SANDISK_FSP_EN			(FALSE)
#define MICRON_FSP_EN			(FALSE)
#define IM_B17					(FALSE)
#define IM_B27A					(FALSE)
#define IM_B27B					(FALSE)
#define IM_N18					(FALSE)
#define IM_N28					(FALSE)
#define IM_B47R					(FALSE)
#define IM_B37R					(FALSE)
#define IM_N48R					(FALSE)
#define IM_N48R_NEED_CHECK		(FALSE)
#define N48_GC_TEST				(FALSE)
#define IM_V7					(FALSE)

#elif(FW_CATEGORY_FLASH == FLASH_V7_TLC)//Duson porting V7 RDT
#define HYNIX_FSP_EN			(TRUE)
#define SANDISK_FSP_EN			(FALSE)
#define YMTC_FSP_EN			    (FALSE)
#define SAMSUNG_FSP_EN			(FALSE)
#define INTEL_FSP_EN			(FALSE)
#define IM_V5					(FALSE)
#define IM_V6					(FALSE)
#define IM_V7					(TRUE)
#define IM_V8					(FALSE)
#define IM_BICS5				(FALSE)
#define IM_BICS6				(FALSE)
#define IM_BICS6_QLC			(FALSE)
#define IM_BICS8				(FALSE)
#define MICRON_FSP_EN			(FALSE)
#define IM_B17					(FALSE)
#define IM_B27A					(FALSE)
#define IM_B27B					(FALSE)
#define IM_N18					(FALSE)
#define IM_N28					(FALSE)
#define IM_B47R					(FALSE)
#define IM_B37R					(FALSE)
#define IM_N48R					(FALSE)
#define IM_N48R_NEED_CHECK		(FALSE)
#define N48_GC_TEST				(FALSE)

#elif(FW_CATEGORY_FLASH == FLASH_V8_TLC)//Jeffrey porting V8 RDT
#define HYNIX_FSP_EN			(TRUE)
#define SANDISK_FSP_EN			(FALSE)
#define YMTC_FSP_EN			    (FALSE)
#define SAMSUNG_FSP_EN			(FALSE)
#define INTEL_FSP_EN			(FALSE)
#define IM_V5					(FALSE)
#define IM_V6					(FALSE)
#define IM_V7					(FALSE)
#define IM_V8					(TRUE)
#define IM_BICS5				(FALSE)
#define IM_BICS6				(FALSE)
#define IM_BICS6_QLC			(FALSE)
#define IM_BICS8				(FALSE)
#define MICRON_FSP_EN			(FALSE)
#define IM_B17					(FALSE)
#define IM_B27A					(FALSE)
#define IM_B27B					(FALSE)
#define IM_N18					(FALSE)
#define IM_N28					(FALSE)
#define IM_B47R					(FALSE)
#define IM_B37R					(FALSE)
#define IM_N48R					(FALSE)
#define IM_N48R_NEED_CHECK		(FALSE)
#define N48_GC_TEST				(FALSE)

#elif(FW_CATEGORY_FLASH == FLASH_V5_TLC)//Jeffrey porting V5 RDT
#define HYNIX_FSP_EN			(TRUE)
#define SANDISK_FSP_EN			(FALSE)
#define YMTC_FSP_EN			    (FALSE)
#define SAMSUNG_FSP_EN			(FALSE)
#define INTEL_FSP_EN			(FALSE)
#define IM_V5					(TRUE)
#define IM_V6					(FALSE)
#define IM_V7					(FALSE)
#define IM_V8					(FALSE)
#define IM_BICS5				(FALSE)
#define IM_BICS6				(FALSE)
#define IM_BICS6_QLC			(FALSE)
#define IM_BICS8				(FALSE)
#define MICRON_FSP_EN			(FALSE)
#define IM_B17					(FALSE)
#define IM_B27A					(FALSE)
#define IM_B27B					(FALSE)
#define IM_N18					(FALSE)
#define IM_N28					(FALSE)
#define IM_B47R					(FALSE)
#define IM_B37R					(FALSE)
#define IM_N48R					(FALSE)
#define IM_N48R_NEED_CHECK		(FALSE)
#define N48_GC_TEST				(FALSE)

#elif(FW_CATEGORY_FLASH == FLASH_SANDISK_BICS5_TLC)//Duson Porting BICS5 Add
#define HYNIX_FSP_EN			(FALSE)
#define SANDISK_FSP_EN			(TRUE)
#define YMTC_FSP_EN			    (FALSE)
#define SAMSUNG_FSP_EN			(FALSE)
#define INTEL_FSP_EN			(FALSE)
#define IM_V5					(FALSE)
#define IM_V6					(FALSE)
#define IM_V7					(FALSE)
#define IM_V8					(FALSE)
#define IM_BICS5				(TRUE)
#define IM_BICS6				(FALSE)
#define IM_BICS6_QLC			(FALSE)
#define IM_BICS8				(FALSE)
#define MICRON_FSP_EN			(FALSE)
#define IM_B17					(FALSE)
#define IM_B27A					(FALSE)
#define IM_B27B					(FALSE)
#define IM_N18					(FALSE)
#define IM_N28					(FALSE)
#define IM_B47R					(FALSE)
#define IM_B37R					(FALSE)
#define IM_N48R					(FALSE)
#define IM_N48R_NEED_CHECK		(FALSE)
#define N48_GC_TEST				(FALSE)

#elif(FW_CATEGORY_FLASH == FLASH_SANDISK_BICS6_TLC)//Duson Porting BICS5 Add
#define HYNIX_FSP_EN			(FALSE)
#define SANDISK_FSP_EN			(TRUE)
#define YMTC_FSP_EN			    (FALSE)
#define SAMSUNG_FSP_EN			(FALSE)
#define INTEL_FSP_EN			(FALSE)
#define IM_V5					(FALSE)
#define IM_V6					(FALSE)
#define IM_V7					(FALSE)
#define IM_V8					(FALSE)
#define IM_BICS5				(FALSE)
#define IM_BICS6				(TRUE)
#define IM_BICS6_QLC			(FALSE)
#define IM_BICS8				(FALSE)
#define MICRON_FSP_EN			(FALSE)
#define IM_B17					(FALSE)
#define IM_B27A					(FALSE)
#define IM_B27B					(FALSE)
#define IM_N18					(FALSE)
#define IM_N28					(FALSE)
#define IM_B47R					(FALSE)
#define IM_B37R					(FALSE)
#define IM_N48R					(FALSE)
#define IM_N48R_NEED_CHECK		(FALSE)
#define N48_GC_TEST				(FALSE)

#elif(FW_CATEGORY_FLASH == FLASH_SANDISK_BICS6_QLC)//zerio bics6 qlc add
#define HYNIX_FSP_EN			(FALSE)
#define SANDISK_FSP_EN			(TRUE)
#define YMTC_FSP_EN			    (FALSE)
#define SAMSUNG_FSP_EN			(FALSE)
#define INTEL_FSP_EN			(FALSE)
#define IM_V5					(FALSE)
#define IM_V6					(FALSE)
#define IM_V7					(FALSE)
#define IM_V8					(FALSE)
#define IM_BICS5				(FALSE)
#define IM_BICS6				(FALSE)
#define IM_BICS6_QLC			(TRUE)
#define IM_BICS8				(FALSE)
#define MICRON_FSP_EN			(FALSE)
#define IM_B17					(FALSE)
#define IM_B27A					(FALSE)
#define IM_B27B					(FALSE)
#define IM_N18					(FALSE)
#define IM_N28					(FALSE)
#define IM_B47R					(FALSE)
#define IM_B37R					(FALSE)
#define IM_N48R					(FALSE)
#define IM_N48R_NEED_CHECK		(FALSE)
#define N48_GC_TEST				(FALSE)

#elif(FW_CATEGORY_FLASH == FLASH_SANDISK_BICS8_TLC)//zerio BICS8 Add
#define HYNIX_FSP_EN			(FALSE)
#define SANDISK_FSP_EN			(TRUE)
#define YMTC_FSP_EN			    (FALSE)
#define SAMSUNG_FSP_EN			(FALSE)
#define INTEL_FSP_EN			(FALSE)
#define IM_V5					(FALSE)
#define IM_V6					(FALSE)
#define IM_V7					(FALSE)
#define IM_V8					(FALSE)
#define IM_BICS5				(FALSE)
#define IM_BICS6				(FALSE)
#define IM_BICS6_QLC			(FALSE)
#define IM_BICS8				(TRUE)
#define MICRON_FSP_EN			(FALSE)
#define IM_B17					(FALSE)
#define IM_B27A					(FALSE)
#define IM_B27B					(FALSE)
#define IM_N18					(FALSE)
#define IM_N28					(FALSE)
#define IM_B47R					(FALSE)
#define IM_B37R					(FALSE)
#define IM_N48R					(FALSE)
#define IM_N48R_NEED_CHECK		(FALSE)
#define N48_GC_TEST				(FALSE)

#elif(FW_CATEGORY_FLASH == FLASH_YMTC_WTS_TLC)//zerio wts add
#define HYNIX_FSP_EN			(FALSE)
#define SANDISK_FSP_EN			(FALSE)
#define YMTC_FSP_EN			    (TRUE)
#define SAMSUNG_FSP_EN			(FALSE)
#define INTEL_FSP_EN			(FALSE)
#define IM_V5					(FALSE)
#define IM_V6					(FALSE)
#define IM_V7					(FALSE)
#define IM_V8					(FALSE)
#define IM_BICS5				(FALSE)
#define IM_BICS6				(FALSE)
#define IM_BICS6_QLC			(FALSE)
#define IM_BICS8				(FALSE)
#define MICRON_FSP_EN			(FALSE)
#define IM_B17					(FALSE)
#define IM_B27A					(FALSE)
#define IM_B27B					(FALSE)
#define IM_N18					(FALSE)
#define IM_N28					(FALSE)
#define IM_B47R					(FALSE)
#define IM_B37R					(FALSE)
#define IM_N48R					(FALSE)
#define IM_N48R_NEED_CHECK		(FALSE)
#define N48_GC_TEST				(FALSE)

#elif(FW_CATEGORY_FLASH == FLASH_YMTC_TAS_TLC)
#define HYNIX_FSP_EN			(FALSE)
#define SANDISK_FSP_EN			(FALSE)
#define YMTC_FSP_EN			    (TRUE)
#define SAMSUNG_FSP_EN			(FALSE)
#define INTEL_FSP_EN			(FALSE)
#define IM_V5					(FALSE)
#define IM_V6					(FALSE)
#define IM_V7					(FALSE)
#define IM_V8					(FALSE)
#define IM_BICS5				(FALSE)
#define IM_BICS6				(FALSE)
#define IM_BICS6_QLC			(FALSE)
#define IM_BICS8				(FALSE)
#define MICRON_FSP_EN			(FALSE)
#define IM_B17					(FALSE)
#define IM_B27A					(FALSE)
#define IM_B27B					(FALSE)
#define IM_N18					(FALSE)
#define IM_N28					(FALSE)
#define IM_B47R					(FALSE)
#define IM_B37R					(FALSE)
#define IM_N48R					(FALSE)
#define IM_N48R_NEED_CHECK		(FALSE)
#define N48_GC_TEST				(FALSE)

#elif(FW_CATEGORY_FLASH == FLASH_YMTC_EMS_QLC)//ems mst add--karl
#define HYNIX_FSP_EN			(FALSE)
#define SANDISK_FSP_EN			(FALSE)
#define YMTC_FSP_EN				(TRUE)
#define SAMSUNG_FSP_EN			(FALSE)
#define INTEL_FSP_EN			(FALSE)
#define IM_V5					(FALSE)
#define IM_V6					(FALSE)
#define IM_V7					(FALSE)
#define IM_V8					(FALSE)
#define IM_BICS5				(FALSE)
#define IM_BICS6				(FALSE)
#define IM_BICS6_QLC			(FALSE)
#define IM_BICS8				(FALSE)
#define MICRON_FSP_EN			(FALSE)
#define IM_B17					(FALSE)
#define IM_B27A					(FALSE)
#define IM_B27B					(FALSE)
#define IM_N18					(FALSE)
#define IM_N28					(FALSE)
#define IM_B47R					(FALSE)
#define IM_B37R					(FALSE)
#define IM_N48R					(FALSE)
#define IM_N48R_NEED_CHECK		(FALSE)
#define N48_GC_TEST				(FALSE)

#elif(FW_CATEGORY_FLASH == FLASH_V7_QLC)//Reip Porting 3D-V7 QLC Add
#define HYNIX_FSP_EN			(TRUE)
#define SANDISK_FSP_EN			(FALSE)
#define YMTC_FSP_EN			    (FALSE)
#define SAMSUNG_FSP_EN			(FALSE)
#define INTEL_FSP_EN			(FALSE)
#define IM_V5					(FALSE)
#define IM_V6					(FALSE)
#define IM_V7					(TRUE)
#define IM_V8					(FALSE)
#define IM_BICS5				(FALSE)
#define IM_BICS6				(FALSE)
#define IM_BICS6_QLC			(FALSE)
#define IM_BICS8				(FALSE)
#define MICRON_FSP_EN			(FALSE)
#define IM_B17					(FALSE)
#define IM_B27A					(FALSE)
#define IM_B27B					(FALSE)
#define IM_N18					(FALSE)
#define IM_N28					(FALSE)
#define IM_B47R					(FALSE)
#define IM_B37R					(FALSE)
#define IM_N48R					(FALSE)
#define IM_N48R_NEED_CHECK		(FALSE)
#define N48_GC_TEST				(FALSE)
#elif(FW_CATEGORY_FLASH == FLASH_SAMSUNG_V6_TLC || FW_CATEGORY_FLASH == FLASH_SAMSUNG_V6P_TLC \
		|| FW_CATEGORY_FLASH == FLASH_SAMSUNG_V7_TLC || FW_CATEGORY_FLASH == FLASH_SAMSUNG_V8_TLC || FW_CATEGORY_FLASH == FLASH_SAMSUNG_V5_TLC)//Samsung v7/v8 mst add--Reip
#define HYNIX_FSP_EN			(FALSE)
#define SANDISK_FSP_EN			(FALSE)
#define YMTC_FSP_EN			    (FALSE)
#define SAMSUNG_FSP_EN			(TRUE)
#define INTEL_FSP_EN			(FALSE)
#define IM_V5					(FALSE)
#define IM_V6					(FALSE)
#define IM_V7					(FALSE)
#define IM_V8					(FALSE)
#define IM_BICS5				(FALSE)
#define IM_BICS6				(FALSE)
#define IM_BICS6_QLC			(FALSE)
#define IM_BICS8				(FALSE)
#define MICRON_FSP_EN			(FALSE)
#define IM_B17					(FALSE)
#define IM_B27A					(FALSE)
#define IM_B27B					(FALSE)
#define IM_N18					(FALSE)
#define IM_N28					(FALSE)
#define IM_B47R					(FALSE)
#define IM_B37R					(FALSE)
#define IM_N48R					(FALSE)
#define IM_N48R_NEED_CHECK		(FALSE)
#define N48_GC_TEST				(FALSE)
#elif(FW_CATEGORY_FLASH == FLASH_INTEL_N38A_QLC)
#define HYNIX_FSP_EN			(FALSE)
#define SANDISK_FSP_EN			(FALSE)
#define YMTC_FSP_EN			    (FALSE)
#define SAMSUNG_FSP_EN			(FALSE)
#define INTEL_FSP_EN			(TRUE)
#define IM_V5					(FALSE)
#define IM_V6					(FALSE)
#define IM_V7					(FALSE)
#define IM_V8					(FALSE)
#define IM_BICS5				(FALSE)
#define IM_BICS6				(FALSE)
#define IM_BICS6_QLC			(FALSE)
#define IM_BICS8				(FALSE)
#define MICRON_FSP_EN			(FALSE)
#define IM_B17					(FALSE)
#define IM_B27A					(FALSE)
#define IM_B27B					(FALSE)
#define IM_N18					(FALSE)
#define IM_N28					(FALSE)
#define IM_B47R					(FALSE)
#define IM_B37R					(FALSE)
#define IM_N48R					(FALSE)
#define IM_N48R_NEED_CHECK		(FALSE)
#define N48_GC_TEST				(FALSE)
#else /*(FW_CATEGORY_FLASH == FLASH_MICRONTLC)*/
#define MICRON_FSP_EN			(FALSE)
#define IM_BICS5				(FALSE)
#define IM_BICS6				(FALSE)
#define IM_BICS6_QLC			(FALSE)
#define IM_BICS8				(FALSE)
#define SANDISK_FSP_EN			(FALSE)
#define YMTC_FSP_EN			    (FALSE)
#define SAMSUNG_FSP_EN			(FALSE)
#define INTEL_FSP_EN			(FALSE)
#define IM_B17					(FALSE)
#define IM_B27A					(FALSE)
#define IM_B27B					(FALSE)
#define IM_N18					(FALSE)
#define IM_N28					(FALSE)
#define IM_B47R					(FALSE)
#define IM_B37R					(FALSE)
#define IM_N48R					(FALSE)
#define IM_N48R_NEED_CHECK		(FALSE)//need to define for V6
#define N48_GC_TEST				(FALSE) // khaki: for GC read verify
#define HYNIX_FSP_EN			(FALSE)
#define IM_V5					(FALSE)
#define IM_V6					(FALSE)
#define IM_V7					(FALSE)
#define IM_V8					(FALSE)

#endif /*(FW_CATEGORY_FLASH == FLASH_MICRONTLC)*/

#define MICRON_S17_E21_140S_EN ((IM_B47R || IM_N48R || IM_B37R) && (PS5017_EN || PS5021_EN))

#define MICRON_B47R_1600MHZ (TRUE && IM_B47R && PS5021_EN)

#define MICRON_FLH_INTERFACE	(LEGACY_INTERFACE)

#if (MICRON_FSP_EN)
#define FPU_SUPPORT_4_PLANE         (1)
#if ((FW_CATEGORY_FLASH == FLASH_N18_QLC) || (FW_CATEGORY_FLASH == FLASH_N28_QLC) || (FW_CATEGORY_FLASH == FLASH_N48R_QLC))
#define CONFIG_FLASH_TYPE                   (FLASH_TYPE_MICRON_3D_QLC)
#else /*(FW_CATEGORY_FLASH == FLASH_N18_QLC)*/
#define CONFIG_FLASH_TYPE                   (FLASH_TYPE_MICRON_3D_TLC)
#endif /*(FW_CATEGORY_FLASH == FLASH_N18_QLC)*/
#define PLANE_NUM               (4)
#define MICRON_MULTIPASS_PROGRAM_MAXIMAL_WINDOW_SIZE	(2)	 // consider as single-plane => e.g. TLC 2-8 programming has window size as 2 (upper & extra page)
#define WINDOW_PER_WORDLINE		(4)
#elif(HYNIX_FSP_EN)                //Dylan 2022/03/10 porting V6 V7 RDT
#if((FW_CATEGORY_FLASH == FLASH_V6_TLC)||(FW_CATEGORY_FLASH == FLASH_V7_TLC)||(FW_CATEGORY_FLASH == FLASH_V8_TLC)||(FW_CATEGORY_FLASH == FLASH_V5_TLC))//Jeffrey  porting 3D V8 TLC Add

#define CONFIG_FLASH_TYPE 					(FLASH_TYPE_HYNIX_3D_TLC)
#elif(FW_CATEGORY_FLASH == FLASH_V7_QLC) //Reip Porting 3D-V7 QLC Add
#define CONFIG_FLASH_TYPE 					(FLASH_TYPE_HYNIX_3D_QLC)
#endif
#define PLANE_NUM               (4)
#define FPU_SUPPORT_4_PLANE         (1)
#elif(SANDISK_FSP_EN)               //Duson Porting BICS5 Add
#if(FW_CATEGORY_FLASH == FLASH_SANDISK_BICS5_TLC || FW_CATEGORY_FLASH == FLASH_SANDISK_BICS6_TLC || FW_CATEGORY_FLASH == FLASH_SANDISK_BICS8_TLC)//BICS6 Add//zerio BICS8 add
#define CONFIG_FLASH_TYPE 					(FLASH_TYPE_BICSX_3D_TLC)//Duson Porting BICS5 Add
#elif(FW_CATEGORY_FLASH == FLASH_SANDISK_BICS6_QLC)//zerio bics6 qlc add
#define CONFIG_FLASH_TYPE 					(FLASH_TYPE_BICSX_3D_QLC)
#endif
#if(FW_CATEGORY_FLASH == FLASH_SANDISK_BICS5_TLC)
#define PLANE_NUM               (2)
#define FPU_SUPPORT_4_PLANE         (0)
#else
#define PLANE_NUM               (4)
#define FPU_SUPPORT_4_PLANE         (1)
#endif
#elif(YMTC_FSP_EN)
#if(FW_CATEGORY_FLASH == FLASH_YMTC_EMS_QLC)//ems mst add--karl
#define CONFIG_FLASH_TYPE 					(FLASH_TYPE_YMTC_3D_QLC)
#elif(FW_CATEGORY_FLASH == FLASH_YMTC_TAS_TLC)
#define CONFIG_FLASH_TYPE 					(FLASH_TYPE_YMTC_3D_TLC)
#elif(FW_CATEGORY_FLASH == FLASH_YMTC_WTS_TLC)//zerio wts add
#define CONFIG_FLASH_TYPE 					(FLASH_TYPE_YMTC_3D_TLC)
#endif
#define PLANE_NUM               (4)
#define FPU_SUPPORT_4_PLANE         (1)
#elif(SAMSUNG_FSP_EN)
#define CONFIG_FLASH_TYPE 					(FLASH_TYPE_SAMSUNG_3D_TLC)
#if(FW_CATEGORY_FLASH == FLASH_SAMSUNG_V6_TLC || FW_CATEGORY_FLASH == FLASH_SAMSUNG_V6P_TLC || FW_CATEGORY_FLASH == FLASH_SAMSUNG_V5_TLC)
#define PLANE_NUM               (2)
#define FPU_SUPPORT_4_PLANE         (0)
#elif(FW_CATEGORY_FLASH == FLASH_SAMSUNG_V7_TLC || FW_CATEGORY_FLASH == FLASH_SAMSUNG_V8_TLC)//Samsung v7/v8 mst add--Reip
#define PLANE_NUM               (4)
#define FPU_SUPPORT_4_PLANE         (1)
#endif
#elif(INTEL_FSP_EN)
#if(FW_CATEGORY_FLASH == FLASH_INTEL_N38A_QLC)
#define CONFIG_FLASH_TYPE 					(FLASH_TYPE_INTEL_3D_QLC)
#endif
#define PLANE_NUM               (4)
#define FPU_SUPPORT_4_PLANE         (1)
#else /*HYNIX_FSP_EN*/
#define FPU_SUPPORT_4_PLANE         (0)
#define PLANE_NUM               (2)
#endif /* (MICRON_FSP_EN) */

#if ((CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC)||(CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC)\
		||(CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_TLC)||(CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_QLC)\
		||(CONFIG_FLASH_TYPE==FLASH_TYPE_YMTC_3D_TLC)||(CONFIG_FLASH_TYPE==FLASH_TYPE_YMTC_3D_QLC)\
		||(CONFIG_FLASH_TYPE==FLASH_TYPE_MICRON_3D_TLC)\
		||(CONFIG_FLASH_TYPE==FLASH_TYPE_MICRON_3D_QLC)\
		||(CONFIG_FLASH_TYPE==FLASH_TYPE_SAMSUNG_3D_TLC)\
		||(CONFIG_FLASH_TYPE==FLASH_TYPE_INTEL_3D_QLC))//Duson Porting BICS5 Add //Reip Porting 3D-V7 QLC Add//zerio bics6 qlc add//zerio n48r add
#define FLASH_SUPPORT_4K_READ     	(FALSE)
#define FLASH_1_2V					(TRUE)
#else /* (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC) */
#define FLASH_SUPPORT_4K_READ     	(FALSE)
#define FLASH_1_2V					(FALSE)
#endif /* (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC) */


//************ 3D Randomizer ************
#define SUPPORT_BOOT_SEEDINIT	((TRUE) && (PS5017_EN == TRUE))

#if VS_SIM_EN
#define FLH_USE_IP	FLH_USE_SIM_CODE
#define CONFIG_FLASH_TYPE                      (FLASH_TYPE_TOSHIBA_SLC)

#if (CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_SLC)
#define FLASH_TYPE	FLASH_SLC
#else  /* (CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_SLC) */
#error "Unknown FLH Type"
#endif /* CONFIG_FLASH_TYPE */

#else /* VS_SIM_EN */

#define FLH_USE_IP	FLH_USE_COP0

#if (CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_SLC)
#define FLASH_TYPE	FLASH_SLC
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_MLC)
#define FLASH_TYPE	FLASH_MLC
#elif (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC)	//Reip Porting 3D-V7 QLC Add
#define FLASH_TYPE	FLASH_QLC
#elif (CONFIG_FLASH_TYPE==FLASH_TYPE_YMTC_3D_QLC)//ems mst add--karl
#define FLASH_TYPE	FLASH_QLC
#elif (CONFIG_FLASH_TYPE==FLASH_TYPE_BICSX_3D_QLC)
#define FLASH_TYPE	FLASH_QLC
#elif (CONFIG_FLASH_TYPE==FLASH_TYPE_INTEL_3D_QLC)
#define FLASH_TYPE	FLASH_QLC
#else /* (CONFIG_FLASH_TYPE != FLASH_TYPE_TOSHIBA_SLC) && (CONFIG_FLASH_TYPE != FLASH_TYPE_MICRON_3D_MLC) */
#define FLASH_TYPE	FLASH_TLC
#endif /* CONFIG_FLASH_TYPE */

#endif /* VS_SIM_EN */

//************ USB related feature and setting ************
#if ((PS5017_EN) && (USB == HOST_MODE))
#define U17_EN	(TRUE)
#else /* ((PS5017_EN) && (USB == HOST_MODE)) */
#define U17_EN	(FALSE)
#endif /* ((PS5017_EN) && (USB == HOST_MODE)) */

#endif /* _ENV_H_ */
