/**************************************************************************/
/*                                                                        */
/*  Copyright (C) 2000-2017, Phison Electronics Corp., Inc.               */
/*  All rights reserved                                                   */
/*                                                                        */
/*  The content of this document is confidential and shall be applied     */
/*  subject to the terms and conditions of the license agreement and      */
/*  other applicable laws. Any unauthorized access, use or disclosure     */
/*  of this document is strictly prohibited and may be punishable         */
/*  under laws.                                                           */
/*                                                                        */
/*  VUC_Protect.c                                                         */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#include "ftl/ftl_nrw_api.h"
#include "hal/sys/api/rng/rng_api.h"
#include "hal/security/security_api.h"
#include "hal/security/security_rsa_api.h"
#include "hal/dmac/dmac_api.h"
#include "hal/dmac/dmac_pop_cmd.h"
#include "host/VUC_handler_api.h"
#include "vuc/VUC_Protect.h"
#include "vuc/VUC_command.h"

#include "burner/codesignheader_api.h"
#if (PS5017_EN)
#include "common/fw_version.h"//E17_porting
#endif /* (PS5017_EN) */

#define VUC_PROTECT_PKE_USE_GOLDEN_PATTERN  (FALSE && PS5013_EN)
#define VUC_PROTECT_PKE_USE_GOLDEN_KEY      (FALSE && PS5013_EN)

#define VUC_PROTECT_DEBUG_UART(a, b, c) VUCProtectShowData(a, b, c)

static U32 gulFTLVUCProtectTimeStamp = 0;

/***********************************************/
/* private prototypes                                                  */
/************************************************/
const U32 gaulVUCProtectAESCipherBlockChainingInitializationVector[VUC_PROTECT_AES_CIPHERBLOCKCHAINING_INITIALIZATIONVECTOR_LENGTH] = {
	0x8215C782,
	0x08E2AF38,
	0xEF5726E1,
	0x99731A09
};
#if (VUC_PROTECT_PKE_USE_GOLDEN_PATTERN)
const U8 gaubVUCProtectGoldenPublicKey[VUC_PROTECT_ENCRYPT_RANDOM_SIZE] = {
	0x01, 0xAE, 0x84, 0x51, 0xD6, 0x70, 0xF8, 0x30, 0x65, 0xCD, 0x8D, 0x5D, 0x60, 0x3A, 0x62, 0x2D,
	0x83, 0xC9, 0x17, 0x8D, 0x6B, 0x87, 0xF3, 0x91, 0x79, 0x34, 0x14, 0xFF, 0xA2, 0x0A, 0x45, 0xE3,
	0xD6, 0x1E, 0x87, 0xE5, 0xA4, 0x14, 0xFF, 0x97, 0x82, 0x6B, 0xB1, 0x76, 0xB6, 0xF1, 0xEB, 0x95,
	0xC6, 0x7E, 0xFA, 0xFB, 0xFD, 0x09, 0x45, 0xC7, 0xEE, 0x41, 0xB2, 0x42, 0x8A, 0x1F, 0x6C, 0xB8,
	0xA6, 0xAE, 0x0A, 0xF1, 0xE1, 0x61, 0x5A, 0x20, 0x22, 0x39, 0x55, 0x20, 0xB3, 0x91, 0x5B, 0xA3,
	0xAD, 0xE7, 0xD5, 0xB5, 0x53, 0x08, 0xA4, 0x65, 0x29, 0xAA, 0x64, 0x2B, 0xAF, 0x14, 0x9C, 0xAD,
	0xCA, 0xF1, 0x7D, 0x45, 0xBF, 0x30, 0x59, 0xE5, 0xB7, 0xEC, 0xFD, 0x96, 0x2F, 0xC4, 0x69, 0x83,
	0x74, 0xA8, 0xDE, 0x27, 0xCE, 0x39, 0x85, 0x57, 0x25, 0x82, 0xF7, 0x4C, 0xB6, 0x83, 0x18, 0x96,
	0x35, 0x8F, 0xCD, 0x1A, 0x7B, 0x7A, 0xD7, 0x34, 0x76, 0x46, 0x3D, 0x9F, 0x5C, 0xA7, 0x08, 0x30,
	0x90, 0x0A, 0x90, 0xFE, 0x0A, 0x8B, 0xD8, 0xFA, 0xA2, 0x25, 0x0F, 0x47, 0xDE, 0xD4, 0xB6, 0x5D,
	0xFC, 0x5A, 0x8A, 0x13, 0x77, 0x43, 0x31, 0xB9, 0x76, 0x4B, 0x61, 0x76, 0x8C, 0xF7, 0x1F, 0x08,
	0x9E, 0x2D, 0x44, 0x42, 0xA2, 0x01, 0xCE, 0x6B, 0xAD, 0x70, 0x9B, 0xB2, 0x0B, 0x7B, 0x23, 0xF6,
	0x5E, 0x0A, 0xA5, 0xB5, 0xDE, 0x31, 0x00, 0xB0, 0x32, 0xC2, 0x81, 0xC4, 0x55, 0xCC, 0x37, 0x20,
	0x8E, 0x64, 0x66, 0x7E, 0xCD, 0x5A, 0x42, 0x73, 0x19, 0x81, 0x81, 0xDC, 0x1F, 0x1F, 0x4C, 0x57,
	0xFD, 0x47, 0xD7, 0xC1, 0x33, 0xDD, 0x67, 0xC1, 0x8A, 0x8F, 0xE8, 0x78, 0x95, 0xD1, 0xB6, 0xB7,
	0x59, 0xB6, 0x0A, 0xF2, 0x7D, 0x3B, 0x73, 0xE4, 0x0A, 0x9E, 0x22, 0x90, 0xA9, 0x33, 0xFF, 0x85
};
#endif /* VUC_PROTECT_PKE_USE_GOLDEN_PATTERN */

#if (VUC_PROTECT_PKE_USE_GOLDEN_KEY)
const U8 gaubVUCProtectPublicKeyNGolden[RSA_PUBLIC_KEY_N_SIZE] = {
	0xae, 0x45, 0xed, 0x56, 0x01, 0xce, 0xc6, 0xb8, 0xcc, 0x05, 0xf8, 0x03, 0x93, 0x5c, 0x67, 0x4d,
	0xdb, 0xe0, 0xd7, 0x5c, 0x4c, 0x09, 0xfd, 0x79, 0x51, 0xfc, 0x6b, 0x0c, 0xae, 0xc3, 0x13, 0xa8,
	0xdf, 0x39, 0x97, 0x0c, 0x51, 0x8b, 0xff, 0xba, 0x5e, 0xd6, 0x8f, 0x3f, 0x0d, 0x7f, 0x22, 0xa4,
	0x02, 0x9d, 0x41, 0x3f, 0x1a, 0xe0, 0x7e, 0x4e, 0xbe, 0x9e, 0x41, 0x77, 0xce, 0x23, 0xe7, 0xf5,
	0x40, 0x4b, 0x56, 0x9e, 0x4e, 0xe1, 0xbd, 0xcf, 0x3c, 0x1f, 0xb0, 0x3e, 0xf1, 0x13, 0x80, 0x2d,
	0x4f, 0x85, 0x5e, 0xb9, 0xb5, 0x13, 0x4b, 0x5a, 0x7c, 0x80, 0x85, 0xad, 0xca, 0xe6, 0xfa, 0x2f,
	0xa1, 0x41, 0x7e, 0xc3, 0x76, 0x3b, 0xe1, 0x71, 0xb0, 0xc6, 0x2b, 0x76, 0x0e, 0xde, 0x23, 0xc1,
	0x2a, 0xd9, 0x2b, 0x98, 0x08, 0x84, 0xc6, 0x41, 0xf5, 0xa8, 0xfa, 0xc2, 0x6b, 0xda, 0xd4, 0xa0,
	0x33, 0x81, 0xa2, 0x2f, 0xe1, 0xb7, 0x54, 0x88, 0x50, 0x94, 0xc8, 0x25, 0x06, 0xd4, 0x01, 0x9a,
	0x53, 0x5a, 0x28, 0x6a, 0xfe, 0xb2, 0x71, 0xbb, 0x9b, 0xa5, 0x92, 0xde, 0x18, 0xdc, 0xf6, 0x00,
	0xc2, 0xae, 0xea, 0xe5, 0x6e, 0x02, 0xf7, 0xcf, 0x79, 0xfc, 0x14, 0xcf, 0x3b, 0xdc, 0x7c, 0xd8,
	0x4f, 0xeb, 0xbb, 0xf9, 0x50, 0xca, 0x90, 0x30, 0x4b, 0x22, 0x19, 0xa7, 0xaa, 0x06, 0x3a, 0xef,
	0xa2, 0xc3, 0xc1, 0x98, 0x0e, 0x56, 0x0c, 0xd6, 0x4a, 0xfe, 0x77, 0x95, 0x85, 0xb6, 0x10, 0x76,
	0x57, 0xb9, 0x57, 0x85, 0x7e, 0xfd, 0xe6, 0x01, 0x09, 0x88, 0xab, 0x7d, 0xe4, 0x17, 0xfc, 0x88,
	0xd8, 0xf3, 0x84, 0xc4, 0xe6, 0xe7, 0x2c, 0x3f, 0x94, 0x3e, 0x0c, 0x31, 0xc0, 0xc4, 0xa5, 0xcc,
	0x36, 0xf8, 0x79, 0xd8, 0xa3, 0xac, 0x9d, 0x7d, 0x59, 0x86, 0x0e, 0xaa, 0xda, 0x6b, 0x83, 0xbb,
};

static U8 gaubVUCProtectPublicKeyEGolden[RSA_PUBLIC_KEY_E_SIZE] = {
	0x00, 0x01, 0x00, 0x01,
};

const U8 gaubVUCProtectPublicKeyRGolden[RSA_PUBLIC_KEY_R_SIZE] = {
	0x51, 0xba, 0x12, 0xa9, 0xfe, 0x31, 0x39, 0x47, 0x33, 0xfa, 0x07, 0xfc, 0x6c, 0xa3, 0x98, 0xb2,
	0x24, 0x1f, 0x28, 0xa3, 0xb3, 0xf6, 0x02, 0x86, 0xae, 0x03, 0x94, 0xf3, 0x51, 0x3c, 0xec, 0x57,
	0x20, 0xc6, 0x68, 0xf3, 0xae, 0x74, 0x00, 0x45, 0xa1, 0x29, 0x70, 0xc0, 0xf2, 0x80, 0xdd, 0x5b,
	0xfd, 0x62, 0xbe, 0xc0, 0xe5, 0x1f, 0x81, 0xb1, 0x41, 0x61, 0xbe, 0x88, 0x31, 0xdc, 0x18, 0x0a,
	0xbf, 0xb4, 0xa9, 0x61, 0xb1, 0x1e, 0x42, 0x30, 0xc3, 0xe0, 0x4f, 0xc1, 0x0e, 0xec, 0x7f, 0xd2,
	0xb0, 0x7a, 0xa1, 0x46, 0x4a, 0xec, 0xb4, 0xa5, 0x83, 0x7f, 0x7a, 0x52, 0x35, 0x19, 0x05, 0xd0,
	0x5e, 0xbe, 0x81, 0x3c, 0x89, 0xc4, 0x1e, 0x8e, 0x4f, 0x39, 0xd4, 0x89, 0xf1, 0x21, 0xdc, 0x3e,
	0xd5, 0x26, 0xd4, 0x67, 0xf7, 0x7b, 0x39, 0xbe, 0x0a, 0x57, 0x05, 0x3d, 0x94, 0x25, 0x2b, 0x5f,
	0xcc, 0x7e, 0x5d, 0xd0, 0x1e, 0x48, 0xab, 0x77, 0xaf, 0x6b, 0x37, 0xda, 0xf9, 0x2b, 0xfe, 0x65,
	0xac, 0xa5, 0xd7, 0x95, 0x01, 0x4d, 0x8e, 0x44, 0x64, 0x5a, 0x6d, 0x21, 0xe7, 0x23, 0x09, 0xff,
	0x3d, 0x51, 0x15, 0x1a, 0x91, 0xfd, 0x08, 0x30, 0x86, 0x03, 0xeb, 0x30, 0xc4, 0x23, 0x83, 0x27,
	0xb0, 0x14, 0x44, 0x06, 0xaf, 0x35, 0x6f, 0xcf, 0xb4, 0xdd, 0xe6, 0x58, 0x55, 0xf9, 0xc5, 0x10,
	0x5d, 0x3c, 0x3e, 0x67, 0xf1, 0xa9, 0xf3, 0x29, 0xb5, 0x01, 0x88, 0x6a, 0x7a, 0x49, 0xef, 0x89,
	0xa8, 0x46, 0xa8, 0x7a, 0x81, 0x02, 0x19, 0xfe, 0xf6, 0x77, 0x54, 0x82, 0x1b, 0xe8, 0x03, 0x77,
	0x27, 0x0c, 0x7b, 0x3b, 0x19, 0x18, 0xd3, 0xc0, 0x6b, 0xc1, 0xf3, 0xce, 0x3f, 0x3b, 0x5a, 0x33,
	0xc9, 0x07, 0x86, 0x27, 0x5c, 0x53, 0x62, 0x82, 0xa6, 0x79, 0xf1, 0x55, 0x25, 0x94, 0x7c, 0x45,
};

const U8 gaubVUCProtectPublicKeyRRGolden[RSA_PUBLIC_KEY_RR_SIZE] = {
	0x8d, 0x0f, 0xa9, 0x69, 0xdc, 0x99, 0xaf, 0x20, 0x0e, 0xb5, 0x16, 0x09, 0x28, 0x2e, 0x7a, 0xa7,
	0x23, 0xf4, 0xaa, 0xd2, 0x17, 0x59, 0x66, 0xc3, 0xd9, 0x6f, 0x6c, 0x54, 0x8a, 0x1a, 0xe3, 0xb8,
	0x8a, 0x82, 0x4d, 0x79, 0xdd, 0x14, 0xf0, 0x6b, 0x8a, 0x99, 0x35, 0x04, 0x90, 0xb8, 0x1e, 0xb1,
	0xe7, 0x88, 0xf0, 0x90, 0x25, 0x07, 0x3f, 0x6e, 0x82, 0xad, 0xb0, 0x94, 0x8a, 0x42, 0x66, 0x08,
	0x32, 0x69, 0xac, 0xfc, 0xde, 0x6f, 0xde, 0x00, 0x9c, 0xe1, 0x41, 0x85, 0x9d, 0xfd, 0x87, 0xee,
	0x93, 0x26, 0xc4, 0x65, 0xef, 0xe2, 0xf6, 0xaf, 0xdd, 0x49, 0x94, 0x83, 0x31, 0x29, 0xd0, 0x8e,
	0x98, 0x17, 0xa9, 0x54, 0x16, 0xa7, 0x5b, 0xc1, 0x5a, 0x6a, 0x4c, 0x4e, 0xce, 0x23, 0x99, 0x6f,
	0x2b, 0xae, 0x3b, 0x89, 0x1a, 0xf2, 0xca, 0x08, 0x2f, 0x2c, 0xe1, 0x07, 0x8f, 0x2b, 0x05, 0x44,
	0xff, 0xa5, 0x05, 0xad, 0x4c, 0x13, 0xa1, 0x0e, 0x64, 0x7f, 0x13, 0x9e, 0xa5, 0xb0, 0xae, 0x60,
	0x06, 0xa8, 0x01, 0x54, 0xb9, 0x20, 0xe7, 0xcd, 0xac, 0x80, 0x8b, 0x81, 0xdc, 0xcb, 0xb1, 0x74,
	0x51, 0x5f, 0x90, 0xc6, 0xa9, 0x1b, 0x7a, 0x00, 0x51, 0xa2, 0x6d, 0x3b, 0x75, 0x7d, 0xaf, 0xf4,
	0xfe, 0xb8, 0x9b, 0x00, 0x24, 0x2f, 0xf3, 0xb4, 0xe0, 0x12, 0x37, 0x1c, 0xa5, 0xfa, 0x5b, 0x2b,
	0xb1, 0x67, 0xcf, 0xbd, 0x51, 0x48, 0x6f, 0x05, 0xcb, 0x9f, 0x35, 0x36, 0xf8, 0x6d, 0x95, 0x98,
	0xfe, 0x91, 0xfc, 0x41, 0x1e, 0xb1, 0xa7, 0x73, 0x9b, 0xd7, 0x4f, 0x3d, 0x4f, 0x7f, 0x8d, 0x6d,
	0x3f, 0x50, 0x6b, 0x63, 0x76, 0x83, 0x9b, 0xed, 0x5d, 0x45, 0xae, 0x51, 0xe8, 0x1d, 0x53, 0xe5,
	0x7d, 0x67, 0xfa, 0x83, 0x9c, 0x8b, 0x73, 0xa9, 0xfe, 0xd3, 0xc2, 0x69, 0xd3, 0x0b, 0x22, 0xfc,
};

static U8 gaubVUCProtectPublicKeyN0Golden[RSA_PUBLIC_KEY_N0_SIZE] = {
	0x75, 0x8d, 0x36, 0x8d,
};
#endif /* (VUC_PROTECT_PKE_USE_GOLDEN_KEY) */

static void VUCProtectShowData(char *pStr, U32 ulAdr, U32 ulLen)
{
	M_UART(VUC_, "\n%s at  %x for %x", pStr, ulAdr, ulLen);
	for (U32 uli = 0; uli < ulLen; uli++) {
		if (0 == (uli % VUC_PROTECT_DEBUG_EACH_16BYTE_PRINT_ADDRESS)) {
			M_UART(VUC_, "\n%x:\t", ulAdr + uli);
		}
		M_UART(VUC_, "%b ", ((U8 *)(ulAdr))[uli]);
	}
	M_UART(VUC_, "\n");
}

static U8 VUCProtectRSAKnownAnserTest(U8 ubRSAAlgoType)
{
#if PS5017_EN
	return SecRSA4096KnownAnswerTest(ubRSAAlgoType, VUC_PROTECT_SIGNATURE_BASE, VUC_PROTECT_SHA512_BUF_BASE, VUC_PROTECT_DECRYPT_BUF_BASE, VUC_PROTECT_SECURITY_BUF_BASE);
#else /* PS5017_EN */
	return SecRSA4096KnownAnswerTest(ubRSAAlgoType, VUC_PROTECT_SIGNATURE_BASE, VUC_PROTECT_SHA256_BUF_BASE, VUC_PROTECT_DECRYPT_BUF_BASE, VUC_PROTECT_SECURITY_BUF_BASE);
#endif /* PS5017_EN */
}

AOM_SECURITY static void VUCProtectPassHandle(VUCProtect_t *pVUCProtect)
{
	pVUCProtect->ubVerifyFailCount = 0;
	gpVTDBUF->VUCProtect.ubVerifyFailCount = pVUCProtect->ubVerifyFailCount;
	pVUCProtect->ubProtectState = VUC_PROTECT_VERIFY_STATE_PASS;
	gpVTDBUF->VUCProtect.ubVUCProtectState = pVUCProtect->ubProtectState;
	gVUCVar.VUCSkipLPMBMP.btVUCProtect = FALSE;
	M_UART(VUC_, "\nVUC_Protect_PASS\n");
}

AOM_SECURITY static void VUCProtectFailHandle(VUCProtect_t *pVUCProtect)
{
	if (VUC_PROTECT_VERIFY_STATE_LOCK_DOWN != pVUCProtect->ubProtectState) {
		pVUCProtect->ubVerifyFailCount++;
		gpVTDBUF->VUCProtect.ubVerifyFailCount = pVUCProtect->ubVerifyFailCount;
		if (pVUCProtect->ubVerifyFailCount >= VUC_PROTECT_MAX_VERIFY_FAIL_COUNT) {
			pVUCProtect->ubProtectState = VUC_PROTECT_VERIFY_STATE_LOCK_DOWN;
			gpVTDBUF->VUCProtect.ubVUCProtectState = pVUCProtect->ubProtectState;
		}
		else {
			pVUCProtect->ubProtectState = pVUCProtect->ubProtectStateDefault;
			gpVTDBUF->VUCProtect.ubVUCProtectState = pVUCProtect->ubProtectState;
		}
	}
	gVUCVar.VUCSkipLPMBMP.btVUCProtect = FALSE;
	memset((void *)(pVUCProtect->aubASessionKey), 0x00, sizeof(pVUCProtect->aubASessionKey));
	memset((void *)(DBUF_FW_VUC_PROTECT_SESSION_KEY_ADDR), 0x00, sizeof(pVUCProtect->aubASessionKey));
	M_UART(VUC_, "\nVUC_Protect_FAIL : %s\n", (pVUCProtect->ubProtectState == 0) ? "VUC_PROTECT_VERIFY_STATE_LOCK_DOWN" : ((pVUCProtect->ubProtectState == 3) ? "VUC_PROTECT_VERIFY_STATE_HANDSHAKE_START" : "VUC_PROTECT_VERIFY_STATE_ENCRYPTION_DATA_SENT"));
}

void VUCProtectCheckTimeOut(void)
{
	VUCProtect_t *pvucprotect = NULL;
	pvucprotect = (VUCProtect_t *) & (gVUCVar.VUCProtect);

	if ((M_RTT_GET_CNT_SECOND(RTT0) - gulFTLVUCProtectTimeStamp) > VUC_PROTECT_TIMEOUT_LIMIT_IN_SECOND) {
		VUCProtectFailHandle(pvucprotect);
	}
}

U8 VUCProtectPassList(U8 ubAlwaysPassOP)
{
	U8 ubRet = PASS;
	switch (ubAlwaysPassOP) {
#if (HOST_MODE == NVME)
	case VUC_AP_KEY:
		break;
#endif
	case VUC_READ_SYS_INFO:
		break;
	case VUC_GET_STATUS:
		break;
	case VUC_SEND_HANDSHAKE_REQUEST:
		break;
	case VUC_RECEIEVE_ENCRYPTION_DATA:
		break;
	case VUC_SEND_ENCRYPTION_DATA:
		break;
	case VUC_DISABLE_VUC:
		break;
#if (HOST_MODE == NVME)
	case VUC_SECURITY_PASS_THROUGH:
		break;
#endif
	default:
		ubRet = FAIL;
		break;
	}
	return ubRet;
}

void VUCProtectInit(VUCProtect_t *pVUCProtect, U8 ubProtectLevelDefault, const SecureKeyInfo_t *pSecureKeyInfo)
{
	M_UART(VUC_, "\nVUC_Protect_en\n");
	pVUCProtect->ubProtectStateDefault = ubProtectLevelDefault;
	pVUCProtect->ubProtectState = pVUCProtect->ubProtectStateDefault;
	pVUCProtect->ubVerifyFailCount = 0;
	memcpy((void *)(pVUCProtect->ulAESCipherBlockChainingInitializayionVector), (void *)(gaulVUCProtectAESCipherBlockChainingInitializationVector), sizeof(pVUCProtect->ulAESCipherBlockChainingInitializayionVector));

	pVUCProtect->pSecureKeyInfo = (SecureKeyInfo_t *)pSecureKeyInfo;

	M_UART(VUC_, "\nRSA_KAT_TEST\n");
#if (PS5017_EN)
	if (FAIL == VUCProtectRSAKnownAnserTest(RSA_ALGORITHM_TYPE_SHA512_4096_PKCS1_V1_5))
#else /* (PS5017_EN) */
	if (FAIL == VUCProtectRSAKnownAnserTest(RSA_ALGORITHM_TYPE_PKCS1_V1_5))
#endif /* (PS5017_EN) */
	{
		M_UART(VUC_, "\nFail\n");
		M_FW_ASSERT(ASSERT_VUC_0x0AC7, FALSE);
	}
	else {
		M_UART(VUC_, "\nPass\n");
	}
}

/**/
/**/
/* @param key_status 0 is fail, non-0 is pass*/
/* @return 0 is pass, 1 is fail*/
/* */

U32 VUCProtectEngineeringModeKeyHandle(VUCProtect_t *pVUCProtect, U32 ulKeyStatus)
{
	if (VUC_PROTECT_VERIFY_STATE_PROTECT != pVUCProtect->ubProtectState) {
		VUCProtectFailHandle(pVUCProtect);
		return FAIL;
	}
	if (ulKeyStatus) {
		pVUCProtect->ubProtectState = VUC_PROTECT_VERIFY_STATE_ENGINEERING;
		return PASS;
	}
	else {
		VUCProtectFailHandle(pVUCProtect);
		return FAIL;
	}
}

/**
 * Host request to start the verification procedure
 *
 * @param vuc_ptr
 * @return
 */

void VUCProtectSendHandshakeRequest(VUC_OPT_HCMD_PTR_t pCmd)
{
	M_UART(VUC_, "\nVUC_handshake_request\n");
	VUCProtect_t *pvucprotect = NULL;
	pvucprotect = (VUCProtect_t *) & (gVUCVar.VUCProtect);
	if (VUC_PROTECT_VERIFY_STATE_ENGINEERING > pvucprotect->ubProtectState) {
		VUCProtectFailHandle(pvucprotect);
		pCmd->ubState = CMD_ERROR;
		pCmd->ubCurrentStatusCode = VUC_ERROR_STATUS_CODE_HANDSHAKE;
		return;
	}
	memset((void *) & (pvucprotect->aubAEncryptRandom[0]), 0x00, VUC_PROTECT_ENCRYPT_RANDOM_SIZE);
	pvucprotect->ubProtectState = VUC_PROTECT_VERIFY_STATE_HANDSHAKE_START;
	gpVTDBUF->VUCProtect.ubVUCProtectState = pvucprotect->ubProtectState;
	gVUCVar.VUCSkipLPMBMP.btVUCProtect = TRUE;
	gulFTLVUCProtectTimeStamp = M_RTT_GET_CNT_SECOND(RTT0);
	gLastCmd.ubProgress = 100;
}

/**
 * Host receives an encryption data from device
 *
 * @param vuc_ptr
 * @return
 */

void VUCProtectReceiveEncryptionData(VUC_OPT_HCMD_PTR_t pCmd)
{
	M_UART(VUC_, "\nVUC_recvencryption_data\n");
	VUCProtect_t *pvucprotect = NULL;
	U32 ulRandomSeed;
	VucProtectReceiveEncryptionData_t *pEncryptionData = NULL;
	pvucprotect = (VUCProtect_t *) & (gVUCVar.VUCProtect);
	U32 ulVucBufAddr = pCmd->ulCurrentPhysicalMemoryAddr;   // need to confirm
#if PS5017_EN
	U16 uwPageByteCnt = SIZE_1KB;
#else /* PS5017_EN */
	U16 uwPageByteCnt = SIZE_512B;
#endif /* PS5017_EN */

	//Check the VUC Protect state machine
	if (VUC_PROTECT_VERIFY_STATE_HANDSHAKE_START != pvucprotect->ubProtectState) {
		VUCProtectFailHandle(pvucprotect);
		pCmd->ubState = CMD_ERROR;
		pCmd->ubCurrentStatusCode = VUC_ERROR_STATUS_CODE_HANDSHAKE;
		return;
	}

	//Set value for the entire output data
	memset((void *)ulVucBufAddr, 0x00, uwPageByteCnt);

	//Create the random data
	pEncryptionData = (VucProtectReceiveEncryptionData_t *)ulVucBufAddr;

	ulRandomSeed = (U32)RngGetRandomValue();
	gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
	DMAC_GenRandom((U32)pEncryptionData, ulRandomSeed, 0x00, VUC_PROTECT_GEN_RANDOM_SIZE, gulDMACDirectWaitDone_Callback, 0);
	while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
		DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
	}

	// PKCS1-v1_5 encoding:
	// EM = 0x00 || BT || PS || 0x00 || M
	pEncryptionData->ubEncryptRandom[0] = 0x00; //First Pedding is 0x00
	pEncryptionData->ubEncryptRandom[1] = 0x02; // BT (Block Type) is 0x02 (Public key Operation)
	pEncryptionData->ubEncryptRandom[VUC_PROTECT_ENCRYPT_RANDOM_SIZE - VUC_PROTECT_PLAINTEXT_SIZE - 1] = 0x00; //PS For a Public key operation is 0x00
	pEncryptionData->ubEncryptRandom[VUC_PROTECT_ENCRYPT_RANDOM_SIZE - 1] = 0x01;
	pEncryptionData->ubEncryptRandom[VUC_PROTECT_ENCRYPT_RANDOM_SIZE - 2] = 0x00;

#if (VUC_PROTECT_PKE_USE_GOLDEN_PATTERN)
	SecReverseCopy((void *)pEncryptionData, (void *)(gaubVUCProtectGoldenPublicKey), VUC_PROTECT_ENCRYPT_RANDOM_SIZE);
#endif /* (VUC_PROTECT_PKE_USE_GOLDEN_PATTERN) */

	VUC_PROTECT_DEBUG_UART("Plain Text", (U32)pEncryptionData, VUC_PROTECT_ENCRYPT_RANDOM_SIZE);

	//This is stored as little endian
	memcpy((void *) & (pvucprotect->aubAEncryptRandom[0]), (void *)pEncryptionData, VUC_PROTECT_ENCRYPT_RANDOM_SIZE);

	pEncryptionData->ulCRC = 0;
	gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
	DMAC_CRC32(DMAC_MODE_NORMAL_QUEUE, (U32)pEncryptionData, 0, sizeof(*pEncryptionData), DISABLE, gulDMACDirectWaitCRCResult_Callback, 0);
	while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
		DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
	}
	pEncryptionData->ulCRC = gDMACDirectWaitCQInfo.ulCRC32Result;

	M_UART(VUC_, "crc 0x%x\n", pEncryptionData->ulCRC);

	RSAPublicKey_t *pRsaPKE = (RSAPublicKey_t *)(ulVucBufAddr + VUC_PROTECT_TEMP_KEY_OFFSET);

#if (VUC_PROTECT_PKE_USE_GOLDEN_KEY)
	SecReverseCopy((void *)&pRsaPKE->key.ulE,    (void *)gaubVUCProtectPublicKeyEGolden,   SIZE_4B);
	SecReverseCopy((void *)&pRsaPKE->ulN0,       (void *)gaubVUCProtectPublicKeyN0Golden,  SIZE_4B);
	SecReverseCopy((void *)pRsaPKE->key.ubN,     (void *)gaubVUCProtectPublicKeyNGolden,   SUPPORT_RSA_BYTE_NUM);
	SecReverseCopy((void *)pRsaPKE->ubR,         (void *)gaubVUCProtectPublicKeyRGolden,   SUPPORT_RSA_BYTE_NUM);
	SecReverseCopy((void *)pRsaPKE->ubRR,        (void *)gaubVUCProtectPublicKeyRRGolden,  SUPPORT_RSA_BYTE_NUM);
#else /* (VUC_PROTECT_PKE_USE_GOLDEN_KEY) */
	pRsaPKE->key.ulE  = M_SECURITY_INVERSE_LONG(pvucprotect->pSecureKeyInfo->PublicKey.key.ulE);
	pRsaPKE->ulN0     = (pvucprotect->pSecureKeyInfo->PublicKey.ulN0);
	SecReverseCopy((void *)pRsaPKE->key.ubN, (void *)pvucprotect->pSecureKeyInfo->PublicKey.key.ubN, SUPPORT_RSA_BYTE_NUM);
	SecReverseCopy((void *)pRsaPKE->ubR, (void *)pvucprotect->pSecureKeyInfo->PublicKey.ubR, SUPPORT_RSA_BYTE_NUM);
	SecReverseCopy((void *)pRsaPKE->ubRR, (void *)pvucprotect->pSecureKeyInfo->PublicKey.ubRR, SUPPORT_RSA_BYTE_NUM);
#endif /* (VUC_PROTECT_PKE_USE_GOLDEN_KEY) */
	M_UART(VUC_, "pRsaPKE address:  0x%x\n", (U32)pRsaPKE);

	//Encrypt the data
	SecRSADecode((U32)pEncryptionData, (U32)pEncryptionData, pRsaPKE);

	VUC_PROTECT_DEBUG_UART("Cipher Text", (U32)pEncryptionData, VUC_PROTECT_ENCRYPT_RANDOM_SIZE);

	pvucprotect->ubProtectState = VUC_PROTECT_VERIFY_STATE_ENCRYPTION_DATA_SENT;
	gpVTDBUF->VUCProtect.ubVUCProtectState = pvucprotect->ubProtectState;
	memcpy((void *)(ulVucBufAddr + VUC_PROTECT_TEMP_REVERSE_BUF_OFFSET), (void *)pEncryptionData, VUC_PROTECT_GEN_RANDOM_SIZE);
	SecReverseCopy((void *)pEncryptionData, (void *)(ulVucBufAddr + VUC_PROTECT_TEMP_REVERSE_BUF_OFFSET), VUC_PROTECT_ENCRYPT_RANDOM_SIZE);

#if (VUC_PROTECT_PKE_USE_GOLDEN_KEY)
	memset((void *)pvucprotect->pSecureKeyInfo->aubSessionKeyParameter, 0x0, sizeof(pvucprotect->aubASessionKey));
	U32 ulKeyCount, ulKeyOffset;
	for (ulKeyCount = 0; ulKeyCount < (VUC_PROTECT_ENCRYPT_RANDOM_SIZE / sizeof(pvucprotect->aubASessionKey)) ; ulKeyCount++) {
		for (ulKeyOffset = 0; ulKeyOffset < sizeof(pvucprotect->aubASessionKey); ulKeyOffset++) {
			pvucprotect->pSecureKeyInfo->aubSessionKeyParameter[ulKeyOffset] ^= gaubVUCProtectPublicKeyNGolden[(ulKeyCount * sizeof(pvucprotect->aubASessionKey) + ulKeyOffset)];
		}
	}
#endif /* (VUC_PROTECT_PKE_USE_GOLDEN_KEY) */

	U32 ulKeyCnt = 0;
	U32 ulKeyOffset = 0;

	memcpy((void *)(pvucprotect->aubASessionKey), (void *)(pvucprotect->pSecureKeyInfo->aubSessionKeyParameter), sizeof(pvucprotect->aubASessionKey));

	VUC_PROTECT_DEBUG_UART("ASK", (U32)pvucprotect->aubASessionKey, VUC_PROTECT_SESSION_KEY_SIZE);
	VUC_PROTECT_DEBUG_UART("RND", (U32)pvucprotect->aubAEncryptRandom, VUC_PROTECT_ENCRYPT_RANDOM_SIZE);

#if PS5017_EN
	// Session Key(32B): Session Param XOR the first 256B of Encrypt random
	for (ulKeyCnt = 0; ulKeyCnt < VUC_PROTECT_PLAINTEXT_OFFSET_IN_32BYTE; ulKeyCnt++)
#else /* PS5017_EN */
	// Session Key(32B): Session Param XOR the last 128B of Encrypt random
	for (ulKeyCnt = VUC_PROTECT_PLAINTEXT_OFFSET_IN_32BYTE; ulKeyCnt < (VUC_PROTECT_ENCRYPT_RANDOM_SIZE / sizeof(pvucprotect->aubASessionKey)) ; ulKeyCnt++)
#endif /* PS5017_EN */
	{
		for (ulKeyOffset = 0; ulKeyOffset < sizeof(pvucprotect->aubASessionKey); ulKeyOffset++) {
			pvucprotect->aubASessionKey[ulKeyOffset] ^= pvucprotect->aubAEncryptRandom[(ulKeyCnt * sizeof(pvucprotect->aubASessionKey) + ulKeyOffset)];
		}
	}

	memcpy((void *)DBUF_FW_VUC_PROTECT_SESSION_KEY_ADDR, (void *)pvucprotect->aubASessionKey, VUC_PROTECT_SESSION_KEY_SIZE);

	VUC_PROTECT_DEBUG_UART("ubSessionKeyParameter", (U32)pvucprotect->pSecureKeyInfo->aubSessionKeyParameter, VUC_PROTECT_SESSION_KEY_SIZE);


#if (!PS5017_EN)
	U8 ubSessionKeyParameter[VUC_PROTECT_SESSION_KEY_SIZE];
	memset((void *)ubSessionKeyParameter, 0x00, sizeof(pvucprotect->aubASessionKey));
	for (ulKeyCnt = 0; ulKeyCnt < (VUC_PROTECT_ENCRYPT_RANDOM_SIZE / sizeof(pvucprotect->aubASessionKey)) ; ulKeyCnt++) {
		for (ulKeyOffset = 0; ulKeyOffset < sizeof(pvucprotect->aubASessionKey); ulKeyOffset++) {
			ubSessionKeyParameter[ulKeyOffset] ^= pRsaPKE->key.ubN[(ulKeyCnt * sizeof(pvucprotect->aubASessionKey) + ulKeyOffset)];
		}
	}
	VUC_PROTECT_DEBUG_UART("ubSessionKeyParameter - gen from N", (U32)ubSessionKeyParameter, VUC_PROTECT_SESSION_KEY_SIZE);
#endif /* (!PS5017_EN) */
}

/**
 * Host sends the encryption data back to device for verification
 *
 *
 *
 * @param vuc_ptr
 * @return
 */
void VUCProtectSendEncryptionData(VUC_OPT_HCMD_PTR_t pCmd)
{
	M_UART(VUC_, "\nVUC_sendencryption_data\n");
	volatile SecurityAESParameter_t AESParameter = {0};
	VUCProtect_t *pvucprotect = NULL;
	VucProtectSendEncryptionData_t *pEncryptionData = NULL;
	U32 ulCRCBin, ulCRCCalc, ulVerifyResult;
	pvucprotect = (VUCProtect_t *) & (gVUCVar.VUCProtect);
	//Check the VUC Protect state machine
	if (VUC_PROTECT_VERIFY_STATE_ENCRYPTION_DATA_SENT != pvucprotect->ubProtectState) {
		VUCProtectFailHandle(pvucprotect);
		pCmd->ubState = CMD_ERROR;
		pCmd->ubCurrentStatusCode = VUC_ERROR_STATUS_CODE_HANDSHAKE;
		return;
	}

	VUC_PROTECT_DEBUG_UART("ASK", (U32)pvucprotect->aubASessionKey, VUC_PROTECT_SESSION_KEY_SIZE);
	VUC_PROTECT_DEBUG_UART("RND", (U32)pvucprotect->aubAEncryptRandom, VUC_PROTECT_ENCRYPT_RANDOM_SIZE);

	//Decrypt the random data sent back with the generated session key  // PS5013 & PS5017: 512B
	pEncryptionData = (VucProtectSendEncryptionData_t *)(pCmd->ulCurrentPhysicalMemoryAddr);

	VUC_PROTECT_DEBUG_UART("AIV", (U32)pvucprotect->ulAESCipherBlockChainingInitializayionVector, sizeof(pvucprotect->ulAESCipherBlockChainingInitializayionVector));
	VUC_PROTECT_DEBUG_UART("AK", (U32)pvucprotect->aubASessionKey, VUC_PROTECT_SESSION_KEY_SIZE);
	VUC_PROTECT_DEBUG_UART("A1", (U32)pEncryptionData->ubEncryptRandom, VUC_PROTECT_ENCRYPT_RANDOM_SIZE);
	AESParameter.ubMode = SECURITY_AES_OFFLINE_MODE_CBC;
	AESParameter.ubAESKeyMode = SECURITY_AES_OFFLINE_KEY_MODE_256;
	AESParameter.ubKeyIdx = SECURITY_OFFLINE_KEY_INDEX;
	AESParameter.EncryptOrDecrypt = SECURITY_AES_OFFLINE_DECRYPT;
	AESParameter.pulKey = (U32 *)pvucprotect->aubASessionKey;
	AESParameter.SetKeyEn = ENABLE;
	AESParameter.uoLBA = 0;
	AESParameter.ulSrcAddr = (U32)(pEncryptionData->ubEncryptRandom);
	AESParameter.ulTargetAddr = (U32)(pEncryptionData->ubEncryptRandom);
	AESParameter.ulDataSize = VUC_PROTECT_AES_ENCRYPT_RANDOM_SIZE;  // PS5013 & PS5017: 256B
	AESParameter.pulInitVector = (U32 *)pvucprotect->ulAESCipherBlockChainingInitializayionVector;
	SecurityAESOfflineExecute((SecurityAESParameter_t *)&AESParameter);

	VUC_PROTECT_DEBUG_UART("A2", (U32)pEncryptionData->ubEncryptRandom, VUC_PROTECT_ENCRYPT_RANDOM_SIZE);

	//Check the CRC after the decryption
	ulCRCBin = pEncryptionData->ulCRC;
	pEncryptionData->ulCRC = 0;
	gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
	DMAC_CRC32(DMAC_MODE_NORMAL_QUEUE, (U32)pEncryptionData, 0, sizeof(VucProtectSendEncryptionData_t), DISABLE, gulDMACDirectWaitCRCResult_Callback, 0);
	while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
		DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
	}
	ulCRCCalc = gDMACDirectWaitCQInfo.ulCRC32Result;

	ulVerifyResult = FAIL;
	//Check the CRC of the return data
	if (ulCRCBin == ulCRCCalc) {
		//Check the decrypted encrypt_random with the backed up encrypt_random, PS5013: 256B, PS5017: 508B
		if (0 == memcmp((void *)(pEncryptionData->ubEncryptRandom), (void *) & (pvucprotect->aubAEncryptRandom[0]), sizeof(pEncryptionData->ubEncryptRandom))) {
			ulVerifyResult = PASS;
		}
		else {
			M_UART(VUC_, "\nData compare fail ~\n");
		}
	}
	else {
		M_UART(VUC_, "\nCRC Fail, ulCRCBin = %x, ulCRCCalc = %x\n", ulCRCBin, ulCRCCalc);
	}
	M_UART(VUC_, "\nVUC_Send_encryptedata_Verifying\n");
	if (ulVerifyResult) {
		VUCProtectFailHandle(pvucprotect);
		pCmd->ubState = CMD_ERROR;
		return;
	}
	else {
		VUCProtectPassHandle(pvucprotect);
		gLastCmd.ubProgress = 100;
		return;
	}
}

#if (PS5017_EN || BURNER_MODE_EN || RDT_MODE_EN)
void VUCProtectDecryptCommand(VUC_OPT_HCMD_PTR_t pCmd)
{
	volatile SecurityAESParameter_t AESParameter = {0};
	VUCProtect_t *pvucprotect = NULL;
	pvucprotect = (VUCProtect_t *) & (gVUCVar.VUCProtect);

	memcpy((void *)pCmd->ulCurrentPhysicalMemoryAddr, (void *) & (pCmd->vuc_sqcmd.vendor.DecryptCmd.ulDw12), VUC_PROTECT_DECODE_COMMAND_SIZE);
	AESParameter.ubMode = SECURITY_AES_OFFLINE_MODE_CBC;
	AESParameter.ubAESKeyMode = SECURITY_AES_OFFLINE_KEY_MODE_256;
	AESParameter.ubKeyIdx = SECURITY_OFFLINE_KEY_INDEX;
	AESParameter.EncryptOrDecrypt = SECURITY_AES_OFFLINE_DECRYPT;
	AESParameter.pulKey = (U32 *)pvucprotect->aubASessionKey;
	AESParameter.SetKeyEn = ENABLE;
	AESParameter.uoLBA = 0;
	AESParameter.ulSrcAddr = pCmd->ulCurrentPhysicalMemoryAddr;
	AESParameter.ulTargetAddr = pCmd->ulCurrentPhysicalMemoryAddr;
	AESParameter.ulDataSize = VUC_PROTECT_DECODE_COMMAND_SIZE;
	AESParameter.pulInitVector = (U32 *)pvucprotect->ulAESCipherBlockChainingInitializayionVector;
	SecurityAESOfflineExecute((SecurityAESParameter_t *)&AESParameter);
	memcpy((void *) & (pCmd->vuc_sqcmd.vendor.DecryptCmd.ulDw12), (void *)pCmd->ulCurrentPhysicalMemoryAddr, VUC_PROTECT_DECODE_COMMAND_SIZE);

	return ;
}
#endif /* (PS5017_EN || BURNER_MODE_EN || RDT_MODE_EN) */

void VUCProtectFWDecryptCommand(U32 ulBufferAdr, VUC_OPT_HCMD_PTR pCmd)
{
	volatile SecurityAESParameter_t AESParameter = {0};
	VUCProtect_t *pvucprotect = NULL;
	pvucprotect = (VUCProtect_t *) & (gVUCVar.VUCProtect);

	memcpy((void *)ulBufferAdr, (void *) & (pCmd->vuc_sqcmd.vendor.DecryptCmd.ulDw12), VUC_PROTECT_DECODE_COMMAND_SIZE);
	AESParameter.ubMode = SECURITY_AES_OFFLINE_MODE_CBC;
	AESParameter.ubAESKeyMode = SECURITY_AES_OFFLINE_KEY_MODE_256;
	AESParameter.ubKeyIdx = SECURITY_OFFLINE_KEY_INDEX;
	AESParameter.EncryptOrDecrypt = SECURITY_AES_OFFLINE_DECRYPT;
	AESParameter.pulKey = (U32 *)pvucprotect->aubASessionKey;
	AESParameter.SetKeyEn = ENABLE;
	AESParameter.uoLBA = 0;
	AESParameter.ulSrcAddr = ulBufferAdr;
	AESParameter.ulTargetAddr = ulBufferAdr;
	AESParameter.ulDataSize = VUC_PROTECT_DECODE_COMMAND_SIZE;
	AESParameter.pulInitVector = (U32 *)pvucprotect->ulAESCipherBlockChainingInitializayionVector;
	SecurityAESOfflineExecute((SecurityAESParameter_t *)&AESParameter);
	memcpy((void *) & (pCmd->vuc_sqcmd.vendor.DecryptCmd.ulDw12), (void *)ulBufferAdr, VUC_PROTECT_DECODE_COMMAND_SIZE);

	return ;
}

void VUCProtectDisable(VUC_OPT_HCMD_PTR_t pCmd)
{
	M_UART(VUC_, "\nVUC_disable_data\n");
	VUCProtect_t *pvucprotect = NULL;
	pvucprotect = (VUCProtect_t *) & (gVUCVar.VUCProtect);
	pvucprotect->ubProtectState = pvucprotect->ubProtectStateDefault;
	gVUCVar.VUCSkipLPMBMP.btVUCProtect = FALSE;
	gpVTDBUF->VUCProtect.ubVUCProtectState = pvucprotect->ubProtectState;
}

#if (HOST_MODE == NVME)
U8 VUCProtectCheckMPHeader(U32 ulAddr)
{
	DLMCMPHeader_t *pMPHeader = (DLMCMPHeader_t *)ulAddr;

	if (FTL_MP_HEADER_MARK != pMPHeader->uwHeaderMark) {
		return FAIL;
	}
	if (FTL_MP_HEADER_MARK2 != pMPHeader->uwHeaderMark2) {
		return FAIL;
	}
	if (FTL_MP_HEADER_IC_VERSION != pMPHeader->uwICVersion) {
		return FAIL;
	}
	if (('E' != pMPHeader->ubICSubVersion[0]) || ('D' != pMPHeader->ubICSubVersion[1])) {
		return FAIL;
	}
	if ( (VUC_PROTECT_DLMC_KEY_DATASTRUCT_VERSION_FIRST_BYTE == pMPHeader->ubFW_DataStruct_Version[0]) && (VUC_PROTECT_DLMC_KEY_DATASTRUCT_VERSION_SECOND_BYTE == pMPHeader->ubFW_DataStruct_Version[1])) {
		gDLMC.uwOthers.B.btVUCProtect = TRUE;
		gVUCVar.VUCProtect.ubProtectState = VUC_PROTECT_VERIFY_STATE_ENGINEERING;
		gpVTDBUF->VUCProtect.ubVUCProtectState = gVUCVar.VUCProtect.ubProtectState;
		return PASS;
	}
	return PASS;
}
#endif /* (HOST_MODE == NVME) */