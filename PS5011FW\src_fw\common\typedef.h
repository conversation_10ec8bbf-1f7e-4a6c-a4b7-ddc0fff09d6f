/** @file typedef.h
 *  @brief Basic Type
 *
 *	Define basic type, basic type size check.
 *
 */
#ifndef _TYPEDEF_H_
#define _TYPEDEF_H_
#include <stddef.h>
#include "parasoft.h"
#include "env.h"

//=============================================================================
typedef unsigned char			U8;
typedef unsigned short			U16;
typedef unsigned long			U32;
typedef unsigned long long		U64;

typedef signed char				S8;
typedef short					S16;
typedef long					S32;
typedef long long				S64;

typedef volatile unsigned char      		REG8;
typedef volatile unsigned short     		REG16;
typedef volatile unsigned long      		REG32;
typedef volatile unsigned long long 		REG64;

typedef union {
	U32 L;
	struct {
		U16 W0: 16;
		U16 W1: 16;
	} W;
	struct {
		U32 B0: 8;
		U32 B1: 8;
		U32 B2: 8;
		U32 B3: 8;
	} B;
	struct {
		U32 BT0: 1;
		U32 BT1: 1;
		U32 BT2: 1;
		U32 BT3: 1;
		U32 BT4: 1;
		U32 BT5: 1;
		U32 BT6: 1;
		U32 BT7: 1;
		U32 BT8: 1;
		U32 BT9: 1;
		U32 BT10: 1;
		U32 BT11: 1;
		U32 BT12: 1;
		U32 BT13: 1;
		U32 BT14: 1;
		U32 BT15: 1;
		U32 BT16: 1;
		U32 BT17: 1;
		U32 BT18: 1;
		U32 BT19: 1;
		U32 BT20: 1;
		U32 BT21: 1;
		U32 BT22: 1;
		U32 BT23: 1;
		U32 BT24: 1;
		U32 BT25: 1;
		U32 BT26: 1;
		U32 BT27: 1;
		U32 BT28: 1;
		U32 BT29: 1;
		U32 BT30: 1;
		U32 BT31: 1;
	} BT;
} REG_t;

typedef volatile REG_t	*REG32_Ptr;

#if PS5021_EN
typedef  U16 CTAG_t;
typedef  U16 RCQOffset_t;
#else /* PS5021_EN */
typedef  U8 CTAG_t;
typedef  U8 RCQOffset_t;
#endif /* PS5021_EN */

//==========================================================
//  Common Parameter Define
//==========================================================
#define DEF_2B	    (2)
#define DEF_4B	    (4)
#define DEF_6B		(6)
#define DEF_8B	    (8)
#define DEF_16B	    (16)
#define DEF_32B	    (32)
#define DEF_64B	    (64)
#define DEF_128B	(128)
#define DEF_256B	(256)
#define DEF_384B	(384)
#define	DEF_512B	(512)
#define DEF_768B	(768)
#define SECTOR_BYTE	DEF_512B
#define	DEF_KB(X)	((U32)(X) << 10)


//==============================================================================

#define TYPE_SIZE_CHECK(TYPE, SIZE_IN_BYTE)	STATIC_ASSERT(sizeof(TYPE)==(SIZE_IN_BYTE), #TYPE " Size Error!")
#define TYPE_SIZE_CHECK_BELOW(TYPE, SIZE_IN_BYTE)	STATIC_ASSERT(sizeof(TYPE)<=(SIZE_IN_BYTE), #TYPE " Size Error!")
#define SIZE_CHECK_ALIGN_4(TYPE) STATIC_ASSERT(((sizeof(TYPE)%4) == 0), #TYPE " Structure Not align 4!")
#define	TYPE_OFFSET_CHECK(TYPE, VARIABLE, LOCATION)	STATIC_ASSERT(offsetof(TYPE, VARIABLE)==(LOCATION), #VARIABLE " Offset Error!")
#endif /* _TYPEDEF_H_ */
