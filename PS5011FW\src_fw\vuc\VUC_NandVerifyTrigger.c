#include "hal/fip/fip.h"
#include "hal/cop0/cop0_api.h"
#include "hal/fip/fpu.h"
#include "hal/pic/uart/uart_api.h"
#include "nvme_api/nvme/shr_hal_nvme.h"
#include "vuc/Vth_Parsing.h"
#include "host/VUC_handler.h"
#include "vuc/VUC_command.h"

#if (BURNER_MODE_EN)
void VUC_NandVerifyTrigger(VUC_OPT_HCMD_PTR_t pCmd)
{
	if (TRUE == gFlhEnv.ulFlashDefaultType.BitMap.btNoSupport) {
		pCmd->ubState = CMD_ERROR;
		gVUCVar.ubVucCmdErrStatus = HOST_ERROR_FEATURE;
		return;
	}

	U8  ubLDPCTemp;
	U32 ulInterruptInfoRegTemp, ulCOP0Attribute2RegTemp, ulInternalFlashSrcAddr0RegTemp, ulBackRestoreRegTemp;

	U8 ubCE = (U8)pCmd->vuc_sqcmd.vendor.NandVerificationTrigger.ubCE;
	U8 ubCH     = M_MOD(ubCE, gubPlanesPerBurst);
	U8 ubCEinCH = M_DIV(ubCE, gubPlanesPerBurst);
	memcpy((void *)VENDOR_NAND_VERIFICATION_SHADOW_CMD, (void *)BURNER_VENDOR_BUF_BASE, pCmd->vuc_sqcmd.vendor.NandVerificationTrigger.ulTransLen);
	memcpy((void *)VENDOR_NAND_VERIFICATION_SHADOW_FPU, (void *) &gFpuEntryList, sizeof(gFpuEntryList) );
	FlaCEControl(ubCH, ubCEinCH, ENABLE);
	M_FIP_VUC_DIRECTED_READ_BACK_UP(ulInterruptInfoRegTemp, ulCOP0Attribute2RegTemp, ubLDPCTemp, ulInternalFlashSrcAddr0RegTemp, ulBackRestoreRegTemp);
	M_FIP_VUC_SET_NORMAL_AND_ERROR_TARGET_TO_CPU0(ubCH);
	Vth_ToolParserHeaderAndAddress(pCmd, (void *)VENDOR_NAND_VERIFICATION_SHADOW_CMD, ubCH);
	M_FIP_VUC_DIRECTED_READ_RESTORE(ulInterruptInfoRegTemp, ulCOP0Attribute2RegTemp, ulInternalFlashSrcAddr0RegTemp, ulBackRestoreRegTemp);
	FlaCEControl(ubCH, ubCEinCH, DISABLE);
	memcpy( (void *)(FLASH_IRAM_ADDRESS + FPU_OFFSET), (void *) VENDOR_NAND_VERIFICATION_SHADOW_FPU, sizeof(gFpuEntryList) );
}
#endif /* BURNER_MODE_EN */
