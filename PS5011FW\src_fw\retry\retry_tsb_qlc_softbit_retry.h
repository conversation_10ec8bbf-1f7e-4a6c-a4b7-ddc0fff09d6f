/****************************************************************************/
//
//  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
//  All rights reserved
//
//  The content of this document is confidential and shall be applied
//  subject to the terms and conditions of the license agreement and
//  other applicable laws. Any unauthorized access, use or disclosure
//  of this document is strictly prohibited and may be punishable
//  under laws.
//
//  retry_tsb_qlc_softbit_retry.h
//
//
//
/****************************************************************************/

#ifndef _RETRY_TSB_QLC_SOFTBIT_RETRY_H_
#define _RETRY_TSB_QLC_SOFTBIT_RETRY_H_

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "setup.h"
#include "hal/fip/fip_api.h"
#include "common/fw_common.h"

#if (CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_QLC)
/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */
#define  LDPC_FRAME_NUMBER_PER_FRAME                    (2)

#if(RETRY_SOFTBITT_FOR_SDK_EN==FALSE)

#define NORMAL_MODE    (0)
#define INVERSE_MODE   (1)

#define RETRY_SB_BACKUP_RESTORE_TO_DRAMIRAM       (1)
#define RETRY_SB_BACKUP_RESTORE_TO_ONLYDRAM       (0)
#define RETRY_SB_BACKUP_RESTORE_TO_ONLYDRAM_AND_KEEP       (2)
#define RETRY_SB_BACKUP_RESTORE_FROM_ONLYDRAM       (3)
#define RETRY_SB_BACKUP_RESTORE_TO_DRAMIRAM_AND_KEEP       (4)

#define SWITCH_CLK__NORMAL_MODE            (0)
#define  SWITCH_CLK__LOW_CLK_MODE         (1)

#define RETRY_SB_INSERT_USED_MT_MODE                   (0)
#define RETRY_SB_INSERT_NEW_MT_MODE                    (1)
#define RETRY_SB_INSERT_USED_MT_MODE_AND_WRITE_SPARE   (2)

/// for gubRetrySBCnt1Result
#define CNT1_RESULT_ARRAY_PARM_LENGTH   (4)
#define CNT1_RESULT_ARRAY_PARM_CNT        (5)
#define CNT1_RESULT_ARRAY_FRAME_CNT      (6)
#define CNT1_RESULT_ARRAY_ROUND_CNT      (7)
#define CNT1_RESULT_SKIP_BIT_CNT      (8)
#define RETRY_SB_CNT1_RESULT_ARRAY_SIZE			(9)

#define COARSE_TABLE_CHECK_ROUND_MAX_TIMES  (3)

#define  COARSE_LEVEL_DIVISOR_1_IDX  (0)
#define  COARSE_LEVEL_DIVISOR_2_IDX  (1)
#define  COARSE_LEVEL_DIVISOR_3_IDX  (2)
#define  COARSE_LEVEL_THRESHOLD_1_IDX  (3)
#define  COARSE_LEVEL_THRESHOLD_2_IDX  (4)
#define  RETRY_SB_COARSE_LEVEL_ARRAY_SIZE		(5)

#define RETRY_SB_COARSE_TABLE_MAGIC_NUMBER   (9488)
#define RETRY_SB_MT_FAIL_RETRY_CNT_MAX            (0xF)
#define RETRY_SB_COARSE_LEVEL_ARRAY_SIZE (5)

#define SB_BICS3_BICS4_GENERAL_LLR_TABLE_START_IDX		(0)
#define SB_BICS3_BICS4_CORNER_CASE_LLR_TABLE_START_IDX	(1)

//SB Debug 0121
#define SB_FIRST_2K_DATA		BIT0
#define SB_FIRST_2K_SPARE		BIT1
#define SB_SECOND_2K_DATA		BIT2
#define SB_SECOND_2K_SPARE	BIT3
#else /* (RETRY_SOFTBITT_FOR_SDK_EN==FALSE) */

#endif /* (RETRY_SOFTBITT_FOR_SDK_EN==FALSE) */

#define SBIT_RETRY_STATE_READY                          (BIT0)
#define SBIT_RETRY_FORCE_SIG                            (BIT1)

#define SBIT_NAND_STATE_INIT                            (0)

#define SBIT_LOWER_PAGE_ID                              (0)         // page id low
#define SBIT_MIDDLE_PAGE_ID                             (1)         // page id middle
#define SBIT_UPPER_PAGE_ID                              (2)         // page id up
#define SBIT_TOP_PAGE_ID                                (3)         // page id top (QLC)
#define SBIT_MAX_SUPPORT_PAGE_NUM                       (4)         // max page number

#define SBIT_LLR_TBL_NUM                                (2)         // llr table buffer address, 4 llr table for lmut
#define SBIT_LLR_TABLE_SIZE                             (32)        // each table size is 32B (fixed by hardware)
#define SBIT_GRAY_CODE_TABLE_SIZE                       (20)        // gray code table size in byte (fixed by hardware)
#define SBIT_LUT_TABLE_SIZE                             (40)        // lookup table size in byte (fixed by hardware)
#define SBIT_DSP_PARAM_TABLE_SIZE                       (8)         // dsp param table size in byte (fixed by hardware)

#define SBIT_DSP_LLR_TABLE_SIZE                             (40)        // each DSP LLR table size is 40B (fixed by hardware)
#define SBIT_DSP_LLR_TABLE_BITS_PER_HW_ACCESS      (5)        // HW access LLR table per 5Bits
#define SBIT_DSP_LLR_TABLE_SIZE_IN_4_BYTES         ((SBIT_DSP_LLR_TABLE_SIZE << 3) / 32)        //10
#define SBIT_DSP_LLR_TABLE_SIZE_IN_40_BITS         ((SBIT_DSP_LLR_TABLE_SIZE << 3) / 40)        //8
#define SBIT_DSP_LLR_TABLE_SIZE_IN_5_BITS         ((SBIT_DSP_LLR_TABLE_SIZE << 3) / 5)			//64
#define SBIT_DSP_LLR_TABLE_5_BITS_TO_40_BITS_IDX		(8)		//	64 / 8
#define SBIT_DSP_LLR_TABLE_5_BITS_TO_40_BITS_IDX_LOG	(3)		//	Log(8)
#define SBIT_DSP_LLR_TABLE_5_BITS_TO_OPRATION_IDX		(16)		//	64 / 4
#define SBIT_DSP_LLR_TABLE_5_BITS_TO_OPRATION_IDX_LOG	(4)		//	Log(16)
#define SBIT_DSP_LLR_TABLE_BITS_PER_OPRATION	(2)		//	2Bits for +1, -1, 0

#define SBIT_DELTA_MAX_COL_NUM                          (8)         // delta table column number
#define SBIT_DELTA_MAX_ROW_NUM                          (7)         // delta table row number
#define SBIT_DELTA_N3R                                  (0)
#define SBIT_DELTA_N2R                                  (1)
#define SBIT_DELTA_N1R                                  (2)
#define SBIT_DELTA_P0R                                  (3)
#define SBIT_DELTA_P1R                                  (4)
#define SBIT_DELTA_P2R                                  (5)
#define SBIT_DELTA_P3R                                  (6)

#define SBIT_DELTA_P0                                   (0)
#define SBIT_DELTA_P1                                   (1)
#define SBIT_DELTA_P2                                   (2)
#define SBIT_DELTA_P3                                   (3)
#define SBIT_DELTA_P4                                   (4)
#define SBIT_DELTA_P5                                   (5)
#define SBIT_DELTA_P6                                   (6)
#define SBIT_DELTA_DUMMY                                (7)

#define SBIT_CENTER_MAX_COL_NUM                         (SBIT_DELTA_MAX_COL_NUM)
#define SBIT_CENTER_MAX_ROW_NUM                         (SBIT_DELTA_MAX_ROW_NUM - 1)
#define SBIT_CENTER_P0                                  (SBIT_DELTA_P0)
#define SBIT_CENTER_P1                                  (SBIT_DELTA_P1)
#define SBIT_CENTER_P2                                  (SBIT_DELTA_P2)
#define SBIT_CENTER_P3                                  (SBIT_DELTA_P3)
#define SBIT_CENTER_P4                                  (SBIT_DELTA_P4)
#define SBIT_CENTER_P5                                  (SBIT_DELTA_P5)
#define SBIT_CENTER_P6                                  (SBIT_DELTA_P6)

#define SOFTBIT_MAX_RETRY_STATE                         (10)             // softbit retry lib maximum support state number

#define SBIT_FPU_STEP_INIT                              (0)
#define SBIT_FPU_STEP_DONE                              (0xFE)
#define SBIT_FPU_STEP_INVALID                           (0xFF)

#define SBIT_CENTER_NUM                                 (4)

#define SBIT_TAG_STATUS_DONE                            (BIT0)
#define SBIT_TAG_STATUS_ALLOC_FAIL                      (BIT1)

#define SOFTBIT_RETRY_FPU_BUF_NUM                   (2)
#define SOFTBIT_RETRY_FPU_BUF_ENTRY_NUM             (64)
#define SOFTBIT_RETRY_FPU_TEMPLATE_BUF_ENTRY_NUM    (192)

#define SOFTBIT_RETRY_CASE_A				(0)
#define SOFTBIT_RETRY_CASE_B				(1)

#define SBIT_WL_NUM_PER_SHARE_WL	(4)
/*
 * ---------------------------------------------------------------------------------------------------
 *  enum
 * ---------------------------------------------------------------------------------------------------
 */
#if(RETRY_SOFTBITT_FOR_SDK_EN==FALSE)
/*
 *  SOFTBIT SUBSTATE MACHINE
 *  =====================
 */

typedef enum SoftBitSubStateEnum {
	RETRY_SB_SUBSTATE_DET_COARSE_LVL__INIT = 1,												// 1 	0x1
	RETRY_SB_SUBSTATE_DET_COARSE_LVL__DEFAULT_RD_LVL_I,										// 2 	0x2 RD_LVL_I to RD_LVL_IV should arranged in order
	RETRY_SB_SUBSTATE_DET_COARSE_LVL__DEFAULT_RD_LVL_II,									// 3 	0x3
	RETRY_SB_SUBSTATE_DET_COARSE_LVL__DEFAULT_RD_LVL_III,									// 4 	0x4
	RETRY_SB_SUBSTATE_DET_COARSE_LVL__DEFAULT_RD_LVL_IV,									// 5 	0x5

	RETRY_SB_SUBSTATE_DET_COARSE_LVL__RD_CNTONE_OP,											// 6 	0x6
	RETRY_SB_SUBSTATE_DET_COARSE_LVL__RD_CNTONE_DMA,										// 7 	0x7
	RETRY_SB_SUBSTATE_DET_COARSE_LVL__CHK_COARSE_TABLE,										// 8 	0x8

	RETRY_SB_SUBSTATE_DET_COARSE_LVL__RD_IDLE_OR_WAIT,										// 9 	0x9

	///--------------------------------------------------------------------
	/// DEBUG USAGE
	///---------------------------------------------------------------------
	RETRY_SB_SUBSTATE_DET_COARSE_LVL__RD_CNTONE_OP_DEBUG,									// 10	0xA
	RETRY_SB_SUBSTATE_DET_COARSE_LVL__RD_CNTONE_DMA_DEBUG,									// 11	0xB
	///---------------------------------------------------------------------

	RETRY_SB_SUBSTATE_DECODE_WITH_DEFAULT_LLR_WITH_OPT_READ_LEVEL_INIT,						// 12	0xC
	RETRY_SB_SUBSTATE_DECODE_WITH_DEFAULT_LLR_WITH_OPT_READ_LEVEL_SB_CORR,					// 13	0xD

	RETRY_SB_SUBSTATE_READ_WITH_COARSE_TUNING_LEVEL_INIT,									// 14	0xE
	RETRY_SB_SUBSTATE_READ_WITH_COARSE_TUNING_LEVEL_RD_HB,									// 15	0xF
	RETRY_SB_SUBSTATE_READ_WITH_COARSE_TUNING_LEVEL_RD_SB1,									// 16	0x10
	RETRY_SB_SUBSTATE_READ_WITH_COARSE_TUNING_LEVEL_RD_SB2,									// 17	0x11
	RETRY_SB_SUBSTATE_READ_WITH_COARSE_TUNING_LEVEL_RD_SB3,									// 18	0x12
	RETRY_SB_SUBSTATE_READ_WITH_COARSE_TUNING_LEVEL_RD_SB4,									// 19	0x13
	RETRY_SB_SUBSTATE_READ_WITH_COARSE_TUNING_LEVEL_RD_SB5,									// 20	0x14
	RETRY_SB_SUBSTATE_DECODE_WITH_CORNER_CASE_FIX_LLR_WITH_COARSE_TUNING_READ_LEVEL_INIT,	// 21	0x15
	RETRY_SB_SUBSTATE_DECODE_WITH_CORNER_CASE_FIX_LLR_WITH_COARSE_TUNING_READ_LEVEL_SB_CORR,// 22	0x16
	RETRY_SB_SUBSTATE_READ_WITH_COARSE_TUNING_LEVEL_IDLE_OR_WAIT,							// 23	0x17

	RETRY_SB_SUBSTATE_DECODE_WITH_ADT_LLR_WITH_COARSE_TUNING_READ_LEVEL_INIT,				// 24	0x18
	RETRY_SB_SUBSTATE_DECODE_WITH_ADT_LLR_WITH_COARSE_TUNING_READ_LEVEL_SB_CORR,			// 25	0x19

	RETRY_SB_SUBSTATE_RETURE_DEFAULT_FEATURE,												// 26	0x1A

	RETRY_SB_SUBSTATE_WITH_COARSE_TUNING_LVL_INIT,											// 27	0x1B
	RETRY_SB_SUBSTATE_WITH_COARSE_TUNING_LVL_HB_RD_OP,										// 28	0x1C
	RETRY_SB_SUBSTATE_WITH_COARSE_TUNING_LVL_HB_CORR_2K,									// 29	0x1D

	RETRY_SB_SUBSTATE_WITH_OPT_LVL_INIT,													// 30	0x1E
	RETRY_SB_SUBSTATE_WITH_OPT_LVL_HB_RD_OP,												// 31	0x1F
	RETRY_SB_SUBSTATE_WITH_OPT_LVL_HB_CORR_2K,												// 32	0x20
	RETRY_SB_SUBSTATE_WITH_OPT_LVL_INIT_SB_CORR,											// 33	0x21
	RETRY_SB_SUBSTATE_WITH_OPT_LVL_SB_CORR,													// 34	0x22

	RETRY_SB_SUBSTATE_READ_WITH_OPT_LEVEL_INIT,												// 35	0x23
	RETRY_SB_SUBSTATE_READ_WITH_OPT_LEVEL_RD_HB,											// 36	0x24
	RETRY_SB_SUBSTATE_READ_WITH_OPT_LEVEL_RD_SB1,											// 37	0x25
	RETRY_SB_SUBSTATE_READ_WITH_OPT_LEVEL_RD_SB2,											// 38	0x26
	RETRY_SB_SUBSTATE_READ_WITH_OPT_LEVEL_RD_SB3,											// 39	0x27
	RETRY_SB_SUBSTATE_READ_WITH_OPT_LEVEL_RD_SB4,											// 40	0x28
	RETRY_SB_SUBSTATE_READ_WITH_OPT_LEVEL_RD_SB5,											// 41	0x29

	RETRY_SB_SUBSTATE_DECODE_WITH_CORNER_CASE_FIX_LLR_WITH_OPT_READ_LEVEL_INIT,				// 42	0x2A
	RETRY_SB_SUBSTATE_DECODE_WITH_CORNER_CASE_FIX_LLR_WITH_OPT_READ_LEVEL_SB_CORR,			// 43	0x2B
	RETRY_SB_SUBSTATE_READ_WITH_OPT_LEVEL_IDLE_OR_WAIT,										// 44	0x2C
	RETRY_SB_SUBSTATE_DECODE_WITH_ADT_LLR_WITH_OPT_READ_LEVEL_INIT,							// 45	0x2D
	RETRY_SB_SUBSTATE_DECODE_WITH_ADT_LLR_WITH_OPT_READ_LEVEL_SB_CORR,						// 46	0x2E

	RETRY_SB_SUBSTATE_WITH_OPT_LEVEL_AND_APT_LLR__INIT,										// 47	0x2F

	RETRY_SB_SUBSTATE_DSP2_INIT,															// 48	0x30
	RETRY_SB_SUBSTATE_DSP2_SKIP_FOUR_PAGE_RD_HB_1,											// 49	0x31
	RETRY_SB_SUBSTATE_DSP2_SKIP_FOUR_PAGE_RD_HB_2,											// 50	0x32
	RETRY_SB_SUBSTATE_DSP2_SKIP_FOUR_PAGE_RD_HB_3,											// 51	0x33
	RETRY_SB_SUBSTATE_DSP2_GEN_SB6_SB7,														// 52	0x34
	RETRY_SB_SUBSTATE_DSP2_RD_HB,															// 53	0x35
	RETRY_SB_SUBSTATE_DSP2_RD_SB1,															// 54	0x36
	RETRY_SB_SUBSTATE_DSP2_RD_SB2,															// 55	0x37
	RETRY_SB_SUBSTATE_DSP2_RD_SB3,															// 56	0x38
	RETRY_SB_SUBSTATE_DSP2_RD_SB4,															// 57	0x39
	RETRY_SB_SUBSTATE_DSP2_RD_SB5,															// 58	0x3A
	RETRY_SB_SUBSTATE_DSP2__INT_SB_CORR,													// 59	0x3B
	RETRY_SB_SUBSTATE_DSP2__SB_CORR,														// 60	0x3C

	RETRY_SB_SUBSTATE_WITH_OPT_READ_LEVEL__AFTER_RAIDECC__INIT,								// 61	0x3D
	RETRY_SB_SUBSTATE_WITH_OPT_READ_LEVEL__AFTER_RAIDECC__HB,								// 62	0x3E
	RETRY_SB_SUBSTATE_WITH_OPT_READ_LEVEL__AFTER_RAIDECC__RD_SB1,							// 63	0x3F
	RETRY_SB_SUBSTATE_WITH_OPT_READ_LEVEL__AFTER_RAIDECC__RD_SB2,							// 64	0x40
	RETRY_SB_SUBSTATE_WITH_OPT_READ_LEVEL__AFTER_RAIDECC__RD_SB3,							// 65	0x41
	RETRY_SB_SUBSTATE_WITH_OPT_READ_LEVEL__AFTER_RAIDECC__RD_SB4,							// 66	0x42
	RETRY_SB_SUBSTATE_WITH_OPT_READ_LEVEL__AFTER_RAIDECC__BKUP_RESTORE_SB6,					// 67	0x43
	RETRY_SB_SUBSTATE_WITH_OPT_READ_LEVEL__AFTER_RAIDECC__TRIG_SBC_FOR_ADT_LLR,				// 68	0x44
	RETRY_SB_SUBSTATE_WITH_OPT_READ_LEVEL__AFTER_RAIDECC__INT_SB_CORR,						// 69	0x45
	RETRY_SB_SUBSTATE_WITH_OPT_READ_LEVEL__AFTER_RAIDECC__SB_CORR,							// 70	0x46
	RETRY_SB_SUBSTATE_WITH_OPT_READ_LEVEL__AFTER_RAIDECC__IDLE_OR_WAIT,						// 71	0x47

	RETRY_SB_SUBSTATE_DSP2_SKIP_FOUR_PAGE_RD_G_STATUS,										// 72	0x48

	//Gen ADT_LLR
	RETRY_SB_SUBSTATE_WITH_COARSE_TUNING_LVL_TRIG_SBC_FOR_ADT_LLR,							// 73	0x49
	RETRY_SB_SUBSTATE_WITH_OPT_LVL_TRIG_SBC_FOR_ADT_LLR,									// 74	0x4A
	RETRY_SB_SUBSTATE_DSP2__SB_TRIG_SBC_FOR_ADT_LLR,										// 75	0x4B

	RETRY_SB_SUBSTATE_STATE__INITIAL_VARIABLE_AND_THIRDSTATE,                               // 76	0x4C
	RETRY_SB_SUBSTATE_STATE__SET_DEFAULT_FEATURE,											// 77	0x4D
	RETRY_SB_SUBSTATE_STATE__GET_FEATURE_AND_CHECK,											// 78	0x4E

	RETRY_SB_SUBSTATE_NULL = 0xFF
} SoftBitSubStateEnum_t;

/*
 *  SOFTBIT 3RD STATE MACHINE
 *  =====================
 */
typedef enum SoftBitThirdStateEnum {
	RETRY_SB_3RD_STATE__RD_HB__INIT = 1,                                  //(1)
	RETRY_SB_3RD_STATE__RD_HB__SET_NEW_READ_LVL_I,                        //(2)
	RETRY_SB_3RD_STATE__RD_HB__SET_NEW_READ_LVL_II,                  	  //(3)
	RETRY_SB_3RD_STATE__RD_HB__SET_NEW_READ_LVL_III,					  //(4)
	RETRY_SB_3RD_STATE__RD_HB__SET_NEW_READ_LVL_IV,						  //(5)

	RETRY_SB_3RD_STATE__RD_HB__DIRECT_RD_OP,                              //(6)
	RETRY_SB_3RD_STATE__RD_HB__DIRECT_RD_DMA,                             //(7)

	RETRY_SB_3RD_STATE__RETURN_DEFAULT_RD_LVL_I,                          //(8) RD_LVL_I to RD_LVL_IV should arranged in order
	RETRY_SB_3RD_STATE__RETURN_DEFAULT_RD_LVL_II,                         //(9)
	RETRY_SB_3RD_STATE__RETURN_DEFAULT_RD_LVL_III,						  //(10)
	RETRY_SB_3RD_STATE__RETURN_DEFAULT_RD_LVL_IV,						  //(11)
	RETRY_SB_3RD_STATE__RETURN_DEFAULT_RD_LVL_IDLE,                       //(12)

	RETRY_SB_3RD_STATE__RD_SB1__INIT,                                     //(13)
	RETRY_SB_3RD_STATE__RD_SB1__HB_ADD2_DELTA,                            //(14)
	RETRY_SB_3RD_STATE__RD_SB1__HB_MINUS2_DELTA,                          //(15)
	RETRY_SB_3RD_STATE__RD_SB1__XOR,                                      //(16)
	RETRY_SB_3RD_STATE__RD_SB1__DMA,                                      //(17)
	RETRY_SB_3RD_STATE__RD_HB__RETRUN_DEFAULT_READ_LVL_I,       		  //(18)
	RETRY_SB_3RD_STATE__RD_HB__RETRUN_DEFAULT_READ_LVL_II,      		  //(19)

	RETRY_SB_3RD_STATE__RD_SB2__INIT,                                     //(20)
	RETRY_SB_3RD_STATE__RD_SB2__HB_ADD3_DELTA,                            //(21)
	RETRY_SB_3RD_STATE__RD_SB2__HB_MINUS3_DELTA,                       	  //(22)
	RETRY_SB_3RD_STATE__RD_SB2__DMA1,									  //(23)
	RETRY_SB_3RD_STATE__RD_SB2__ADDMINUS3_XOR,                            //(24)
	RETRY_SB_3RD_STATE__RD_SB2__MINUS1_DELTA,                             //(25)
	RETRY_SB_3RD_STATE__RD_SB2__ADDMINUS3_MINUS1_XOR,                     //(26)
	RETRY_SB_3RD_STATE__RD_SB2__ADD1_DELTA,                               //(27)
	RETRY_SB_3RD_STATE__RD_SB2__MINUS3_XOR,                               //(28)
	RETRY_SB_3RD_STATE__RD_SB2__DMA2,                                     //(29)
	RETRY_SB_3RD_STATE__AFTER_RD_SB2_SET_DELTA_I,						  //(30) //SET_DELTA_I to SET_DELTA_IV should arranged in order
	RETRY_SB_3RD_STATE__AFTER_RD_SB2_SET_DELTA_II, 						  //(31)
	RETRY_SB_3RD_STATE__AFTER_RD_SB2_SET_DELTA_III,  					  //(32)
	RETRY_SB_3RD_STATE__AFTER_RD_SB2_SET_DELTA_IV,					      //(33)

	RETRY_SB_3RD_STATE__RD_SB3__INIT,                                     //(34)
	RETRY_SB_3RD_STATE__RD_SB3__OP,                                       //(35)
	RETRY_SB_3RD_STATE__RD_SB3__DMA,                                      //(36)

	RETRY_SB_3RD_STATE__RD_SB4__INIT,                                     //(37)
	RETRY_SB_3RD_STATE__RD_SB4__OP,                                       //(38)
	RETRY_SB_3RD_STATE__RD_SB4__DMA,                                      //(39)

	RETRY_SB_3RD_STATE__RD_SB5__INIT,									  //(40)
	RETRY_SB_3RD_STATE__RD_SB5__OP, 									  //(41)
	RETRY_SB_3RD_STATE__RD_SB5__DMA, 									  //(42)

	RETRY_SB_3RD_STATE__GET_DEFAULT_RD_LVL_I,                             //(43) GET RD_LVL_I to RD_LVL_IV should arranged in order
	RETRY_SB_3RD_STATE__GET_DEFAULT_RD_LVL_II,                            //(44)
	RETRY_SB_3RD_STATE__GET_DEFAULT_RD_LVL_III,						  	  //(45)
	RETRY_SB_3RD_STATE__GET_DEFAULT_RD_LVL_IV,                            //(46)

	RETRY_SB_3RD_STATE__CHK_DEFAULT_RD_LVL_I,                             //(47) CHK RD_LVL_I to RD_LVL_IV should arranged in order
	RETRY_SB_3RD_STATE__CHK_DEFAULT_RD_LVL_II,                            //(48)
	RETRY_SB_3RD_STATE__CHK_DEFAULT_RD_LVL_III,							  //(49)
	RETRY_SB_3RD_STATE__CHK_DEFAULT_RD_LVL_IV,							  //(50)

	RETRY_SB_3RD_STATE__GET_NEW_RD_LVL_I,								  //(51)
	RETRY_SB_3RD_STATE__GET_NEW_RD_LVL_II,								  //(52)
	RETRY_SB_3RD_STATE__CHK_NEW_RD_LVL_I,								  //(53)
	RETRY_SB_3RD_STATE__CHK_NEW_RD_LVL_II								  //(54)
} SoftBitThirdStateEnum_t;

typedef enum SoftBitFourthStateEnum {
	RETRY_SB_4TH_STATE_MODIFY_RR_DATA = 1,                                         //(1)
	RETRY_SB_4TH_STATE_TRIGGER_MT                                                  //(2)
} SoftBitFourthStateEnum_t;

#else /* (RETRY_SOFTBITT_FOR_SDK_EN==FALSE) */

#endif  /* (RETRY_SOFTBITT_FOR_SDK_EN==FALSE) */
/*
 * ---------------------------------------------------------------------------------------------------
 *  data types
 * ---------------------------------------------------------------------------------------------------
 */
typedef struct fpl_sbit_retry_fip_llr_struct        FPL_SBIT_RETRY_FIP_LLR_STRUCT,   *FPL_SBIT_RETRY_FIP_LLR_STRUCT_PTR;
#if(RETRY_SOFTBITT_FOR_SDK_EN==FALSE)
typedef struct fpl_sbit_retry_param_struct          FPL_SBIT_RETRY_PARAM_STRUCT,     *FPL_SBIT_RETRY_PARAM_STRUCT_PTR;
#endif /* (RETRY_SOFTBITT_FOR_SDK_EN==FALSE) */

struct fpl_sbit_retry_fip_llr_struct {
	U8 entry[SBIT_LLR_TABLE_SIZE];
};

/*
 *           DETAL TABLE             =>          CENTER TABLE
 *           ===========                         ============
 *
 *                                                 +---------------------------------------+
 *                                                 | C0 |    |    |    | C1 |    |    |    |        <= center 0 and 1 for lower page
 *                                                 |    | C0 |    | C1 |    | C2 |    |    |        <= center 0, 1, and 2 for middle page
 *                                                 | C0 |    |    |    | C1 |    |    |    |        <= center 0 and 1 for upper page
 *  +--------------------------------------------------------------------------------------+
 *  |           | P0 | P1 |...| P7 |    |          | P0 | P1 | P2 | P3 | P4 | P6 | P6 | P7 |
 *  |------------------------------+----+--------------------------------------------------|
 *  | - detal 3 | ## | ## |...| ## |    |          |    |    |    |    |    |    |    |    |
 *  |           |    |    |   |    | => | level  0 | ## | ## | ## | ## | ## | ## | ## | ## |
 *  | - detal 2 | ## | ## |...| ## |    |          |    |    |    |    |    |    |    |    |
 *  |           |    |    |   |    | => | level  1 | ## | ## | ## | ## | ## | ## | ## | ## |
 *  | - detal 1 | ## | ## |...| ## |    |          |    |    |    |    |    |    |    |    |
 *  |           |    |    |   |    | => | level  2 | ## | ## | ## | ## | ## | ## | ## | ## |
 *  |   detal 0 | ## | ## |...| ## |    |          |    |    |    |    |    |    |    |    |
 *  |           |    |    |   |    | => | level  3 | ## | ## | ## | ## | ## | ## | ## | ## |
 *  | + detal 1 | ## | ## |...| ## |    |          |    |    |    |    |    |    |    |    |
 *  |           |    |    |   |    | => | level  4 | ## | ## | ## | ## | ## | ## | ## | ## |
 *  | + detal 2 | ## | ## |...| ## |    |          |    |    |    |    |    |    |    |    |
 *  |           |    |    |   |    | => | level  5 | ## | ## | ## | ## | ## | ## | ## | ## |
 *  | + detal 3 | ## | ## |...| ## |    |          |    |    |    |    |    |    |    |    |
 *  +--------------------------------------------------------------------------------------+
 *
 */
struct fpl_sbit_retry_param_struct {
	FPL_SBIT_RETRY_FIP_LLR_STRUCT_PTR default_llr[SBIT_MAX_SUPPORT_PAGE_NUM];
	FPL_SBIT_RETRY_FIP_LLR_STRUCT_PTR adt_llr[SBIT_LLR_TBL_NUM];

	U8  delta[SBIT_DELTA_MAX_ROW_NUM][SBIT_DELTA_MAX_COL_NUM];
	U8  center[SBIT_CENTER_MAX_ROW_NUM][SBIT_CENTER_MAX_COL_NUM];
	U8  dsp_center_idx[SBIT_CENTER_NUM];
};

#if(RETRY_SOFTBITT_FOR_SDK_EN==FALSE)
struct corse_tuning_element {
	U32 x_low;
	U32 x_upp;
	U32 y_low;
	U32 y_upp;
	U32 z_low;
	U32 z_upp;
};

typedef struct SoftBitLLR40Bits   SoftBitLLR40Bits_t;
struct SoftBitLLR40Bits {      // use union type to optimize.
	U8 ubLLR8Bits[5];
};

typedef struct SoftBitLLRTable   SoftBitLLRTable_t;
struct SoftBitLLRTable {      // use union type to optimize.
	union {
		struct {
			U32 ulLLR32bits[10];
		} AccessBy32bits;
		struct {
			U8 ubLLR8bits[40];
		} AccessBy8bits;
		SoftBitLLR40Bits_t LLR40Bits[8];
	} LLRTable;
};

typedef struct LLRTable40BitsAccess   LLRTable40BitsAccess_t;
struct LLRTable40BitsAccess {      // use union type to optimize.
	union {
		U64 uoAll;
		SoftBitLLR40Bits_t LLR40Bits;
	} LLRTable;
};

#else /* (RETRY_SOFTBITT_FOR_SDK_EN==FALSE) */
#endif /* (RETRY_SOFTBITT_FOR_SDK_EN==FALSE) */
#endif /*(CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_QLC)*/
#endif /* _RETRY_TSB_QLC_SOFTBIT_RETRY_H_ */

