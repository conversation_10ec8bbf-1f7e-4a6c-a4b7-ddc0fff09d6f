#include "vuc/VUC_DetectTxRx.h"
#include "nvme_api/pcie/shr_hal_pcie.h"
#include "hal/sys/api/mux/mux.h"
#include "env.h"
#if (NVME == HOST_MODE)
void VUCDetectTxRx(VUC_OPT_HCMD_PTR pCmd)
{
	U8 ubSubFeature = pCmd->vuc_sqcmd.vendor.DetectTxRx.ubSubFeature;
	if (VUC_DETECT_TXRX_TX_OPEN == ubSubFeature) {
		VUCDetectTxOpen(pCmd);
	}
	else if (VUC_DETECT_TXRX_RX_SHORT == ubSubFeature) {
		VUCDetectRxShort(pCmd);
	}
}
/*
 * if byte0 ==0  => all lane =0
 * if byte0 ==1 => byte1 = bitmap
 */
void VUCDetectTxOpen(VUC_OPT_HCMD_PTR pCmd)
{
#if (!E21_TODO)
	U8 *ReturnBuf = (U8 *)(pCmd->ulCurrentPhysicalMemoryAddr);
	U8 ubLaneMap = 0, ubIndex = 0;
	M_DEBUG_PORT_CLR_TSEL();
	M_DEBUG_PORT_SEL_TSEL(VUC_DETECT_TXRX_CPHY);
	M_PHY_PSEL_DEBUG_PORT_SWITCH();

	VUCDetectTxOpenSetLane(VUC_DETECT_TXRX_LANE0);

	ubLaneMap |= ((R32_SYS1_MISCL[R32_SYS1_SYS_DEBUG_STS0] & BIT29) >> 29);
	VUCDetectTxOpenSetLane(VUC_DETECT_TXRX_LANE1);
	ubLaneMap |= ((R32_SYS1_MISCL[R32_SYS1_SYS_DEBUG_STS0] & BIT29) >> 28);
	VUCDetectTxOpenSetLane(VUC_DETECT_TXRX_LANE2);
	ubLaneMap |= ((R32_SYS1_MISCL[R32_SYS1_SYS_DEBUG_STS0] & BIT29) >> 27);
	VUCDetectTxOpenSetLane(VUC_DETECT_TXRX_LANE3);
	ubLaneMap |= ((R32_SYS1_MISCL[R32_SYS1_SYS_DEBUG_STS0] & BIT29) >> 26);

	ReturnBuf[0] = ALL_LAND_ZERO;
	for (ubIndex = 0; ubIndex < 4 ; ubIndex++) {
		if (SOME_LAND_NONZERO == (ubLaneMap >> ubIndex & 1 )) {
			ReturnBuf[0] = SOME_LAND_NONZERO;
			ReturnBuf[1] = ubLaneMap;
			return;
		}
	}
#endif /* (!E21_TODO) */
}

void VUCDetectRxShort(VUC_OPT_HCMD_PTR pCmd)
{
	/* For lane0: address 0xCE40F0[15:8]: EW Result */
	U8 *EWResultTable = (U8 *)(pCmd->ulCurrentPhysicalMemoryAddr);

	EWResultTable[0] = (((*(U32 *)(PCIE_PHYSETTING_LANE_0)) >> 8 ) & 0xFF);
	EWResultTable[1] = (((*(U32 *)(PCIE_PHYSETTING_LANE_1)) >> 8 ) & 0xFF);
	EWResultTable[2] = (((*(U32 *)(PCIE_PHYSETTING_LANE_2)) >> 8 ) & 0xFF);
	EWResultTable[3] = (((*(U32 *)(PCIE_PHYSETTING_LANE_3)) >> 8 ) & 0xFF);

	//memcpy((void *) ReturnBuf, (void *) EWResultTable, 4);
}

void VUCDetectTxOpenSetLane(U8 ubLane)
{
	M_PHY_CLR_PSEL_LANE();
	M_PHY_SET_PSEL_LANE(ubLane);
}

#endif /*(NVME == HOST_MODE)*/
