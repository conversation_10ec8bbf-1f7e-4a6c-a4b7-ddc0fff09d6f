#ifndef _VUC_READFLASH_H_
#define _VUC_READFLASH_H_
#include "aom/aom_api.h"

#define BYTE_PER_DW_LOG		(2)
#define ALL_IBF_DATA_SIZE	(5120)
#define VUC_DIRECT_READ_P4K_FIRST_4BYTE_OFFSET 	(0)
#define VUC_DIRECT_READ_P4K_SECOND_4BYTE_OFFSET	(VUC_DIRECT_READ_P4K_FIRST_4BYTE_OFFSET + 4 * DEF_4B)
#define VUC_DIRECT_READ_P4K_THIRD_4BYTE_OFFSET	(VUC_DIRECT_READ_P4K_SECOND_4BYTE_OFFSET + 4 * DEF_4B)
#define VUC_DIRECT_READ_P4K_FOURTH_4BYTE_OFFSET	(VUC_DIRECT_READ_P4K_THIRD_4BYTE_OFFSET + 4 * DEF_4B)
#define VUC_DIRECT_READ_UNC_OFFSET	(VUC_DIRECT_READ_P4K_FOURTH_4BYTE_OFFSET + 4 * DEF_4B)
#define VUC_DIRECT_READ_ECCINFO_OFFSET	(VUC_DIRECT_READ_P4K_FOURTH_4BYTE_OFFSET + 4 * DEF_4B)

#define VUC_DIRECT_READ_CRC_BIT		(BIT31)
#define VUC_DIRECT_READ_ERASE_BIT	(BIT30)
#define VUC_DIRECT_READ_ECC_BIT	(BIT29)

#define VUC_DIRECT_READ_NO_ERROR (0)

extern U32 gulVUCDirectedCieOutInfo;
extern U32 gVUCReadCallBackAddr;
AOM_VUC void VUC_ReadPage(VUC_OPT_HCMD_PTR_t pCmd);
AOM_VUC void VUC_ReadPca(VUC_OPT_HCMD_PTR_t pCmd);
AOM_VUC void VUC_DirectRead(VUC_OPT_HCMD_PTR_t pCmd);
void VUCRead_Callback(TIEOUT_FORMAT_t uoResult);


#endif /* _VUC_READFLASH_H_ */
