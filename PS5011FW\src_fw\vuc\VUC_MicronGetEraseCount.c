#include "VUC_MicronGetEraseCount.h"
#include "VUC_MicronGetNandErrorRecoveryStatistics.h"
#include "VUC_MicronResponse.h"
#include "hal/dmac/dmac_pop_cmd.h"
#include "table/vbmap/vbmap_api.h"

#if (VUC_MICRON_NAND_VS_COMMANDS_EN)

void VUCMicronGetEraseCount(U32 ulPayloadAddr)
{
	U8 ubTmp[50] = "";
	U16 uwUnitNum;
	GetEraseCountResponseHEADER_t *pResponseHeader;

	pResponseHeader = (GetEraseCountResponseHEADER_t *)ulPayloadAddr;
	U32 ulResponsePayload = ulPayloadAddr + VUC_MICRON_RESPONSE_HEADER_SIZE;

	DMACParam_t DMACParam;
	DMACParam.DMACSetValue.ulDestAddr = ulPayloadAddr;
	DMACParam.DMACSetValue.ulLowValue = 0;
	DMACParam.DMACSetValue.ulHighValue = 0;
	DMACParam.DMACSetValue.ul32ByteNum = SIZE_IN_32B(DEF_KB(FRAMES_PER_PAGE << 2));
	DMACParam.DMACSetValue.btValidate = FALSE;
	gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
	DMACSetValue(&DMACParam, gulDMACDirectWaitDone_Callback, 0);
	while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
		DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
	}

	memset((void *)ulPayloadAddr, 0x00, VUC_MICRON_RESPONSE_HEADER_SIZE);
	pResponseHeader->ubResponseHeaderFormatVersion = 0x00;
	pResponseHeader->ubResponseDataFotmatVersion = VUC_MICRON_RESPONSE_FORMAT_JSON;
	pResponseHeader->uwCMDClass = VUC_MICRON_GET_ERASE_COUNT_CLASS;
	pResponseHeader->uwCMDCode = VUC_MICRON_GET_ERASE_COUNT_CODE;
	pResponseHeader->uwStatus = 0;
	pResponseHeader->ulDataPayloadSize = FRAME_SIZE * FRAMES_PER_PAGE;

	VUCMyStrcat((void *)ulResponsePayload, "{\n\t");
	VUCMyStrcat((void *)ulResponsePayload, "\"structVer\":1,\n\t");
	VUCMyStrcat((void *)ulResponsePayload, "\"Count\":[\n\t\t");

	for (uwUnitNum = 0; uwUnitNum < (gpVT->uwTotalD1UnitNum + gpVT->uwTotalD3UnitNum) ; uwUnitNum++) {
		VUCMyStrcat((void *)ulResponsePayload, "{\"Unit\":");
		VUCMyitoa(gpuwVBRMP[uwUnitNum].B.ToRUT_Unit, (void *)ubTmp, DECIMAL);
		VUCMyStrcat((void *)ulResponsePayload, (void *)ubTmp);
		VUCMyStrcat((void *)ulResponsePayload, ",\"Count\":");
		VUCMyitoa(gpulEC[uwUnitNum].B.uwEraseCnt, (void *)ubTmp, DECIMAL);
		VUCMyStrcat((void *)ulResponsePayload, (void *)ubTmp);

		if ( uwUnitNum == (gpVT->uwTotalD1UnitNum + gpVT->uwTotalD3UnitNum - 1)) {
			VUCMyStrcat((void *)ulResponsePayload, "}]\n}");
		}
		else {
			VUCMyStrcat((void *)ulResponsePayload, "},\n\t\t");
		}
	}

}

#endif /*(VUC_MICRON_NICKS_EN)*/
