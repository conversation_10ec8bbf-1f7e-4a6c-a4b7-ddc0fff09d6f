/****************************************************************************
 *
 *  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
 *  All rights reserved
 *
 *  The content of this document is confidential and shall be applied
 *  subject to the terms and conditions of the license agreement and
 *  other applicable laws. Any unauthorized access, use or disclosure
 *  of this document is strictly prohibited and may be punishable
 *  under laws.
 *
 *  raideccmap_for_N48R.h
 *
 *
 *
 ****************************************************************************/

#ifndef RAIDECCMAP_FOR_N48R_H_
#define RAIDECCMAP_FOR_N48R_H_

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "common/math_op.h"
#include "env.h"
#include "fw_common.h"
#include "setup.h"
#include "typedef.h"

#if (MICRON_FSP_EN && IM_N48R)
/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */
/*
 * Naming: RAIDECCMAP_[DataType]_[PurposeName]
 */

//**********************
// Data Parity Ratio
//**********************
// This define only works under WL protect and "% gubPlanesPerSuperPage" only works under odd CE
// Not a formal ratio definition
#define RAIDECCMAP_DATA_PARITY_RATIO		(64 - (64 % gubPlanesPerSuperPage))  // 63 Data : 1 Parity

#define RAIDECCMAP_TABLE_MIN_PLANE_NUM_PER_GROUP	(8)
#define RAIDECCMAP_TALBE_DATA_PARITY_RATIO			((1 == gubCENumber) ? RAIDECCMAP_TABLE_MIN_PLANE_NUM_PER_GROUP : gubPlanesPerSuperPage)


//**********************
// Plane Protect
//**********************
#define RAIDECCMAP_PLANE_PROTECT_NUM_BY_FLASH	(MULTI_PLANE_PROTECTION ? (4) : (1))


//**********************
// Flash Related
//**********************
#define RAIDECCMAP_SLC_PAGE_PER_SHARED_PAGE		(1)  // L
#define RAIDECCMAP_SLC_SHARED_PAGE_PER_WL		(4)

#define RAIDECCMAP_MLC_PAGE_PER_SHARED_PAGE		(2)  // LU
#define RAIDECCMAP_MLC_SHARED_PAGE_PER_WL		(4)

#define RAIDECCMAP_QLC_PAGE_PER_SHARED_PAGE		(4)  // LUXT
#define RAIDECCMAP_QLC_SHARED_PAGE_PER_WL		(4)

#define RAIDECCMAP_XLC_PAGE_PER_SHARED_PAGE		(RAIDECCMAP_QLC_PAGE_PER_SHARED_PAGE)  // N48R is QLC flash
#define RAIDECCMAP_XLC_SHARED_PAGE_PER_WL		(RAIDECCMAP_QLC_SHARED_PAGE_PER_WL)  // N48R is QLC flash

#define RAIDECCMAP_SLC_PAGE_PER_WL		(RAIDECCMAP_SLC_PAGE_PER_SHARED_PAGE * RAIDECCMAP_SLC_SHARED_PAGE_PER_WL)  // 4
#define RAIDECCMAP_MLC_PAGE_PER_WL		(RAIDECCMAP_MLC_PAGE_PER_SHARED_PAGE * RAIDECCMAP_MLC_SHARED_PAGE_PER_WL)  // 8
#define RAIDECCMAP_QLC_PAGE_PER_WL		(RAIDECCMAP_QLC_PAGE_PER_SHARED_PAGE * RAIDECCMAP_QLC_SHARED_PAGE_PER_WL)  // 16
#define RAIDECCMAP_XLC_PAGE_PER_WL		(RAIDECCMAP_QLC_PAGE_PER_WL)  // 16

#define RAIDECCMAP_MAX_WINDOW_SIZE		(RAIDECCMAP_XLC_PAGE_PER_SHARED_PAGE)  // 4


//**********************
// Basic Tag Num
//**********************
// -----  Block Protect -----
#define RAIDECCMAP_SLC_DATA_BLOCK_PROTECT_BASIC_TAG_NUM		(4)
#define RAIDECCMAP_XLC_DATA_BLOCK_PROTECT_BASIC_TAG_NUM		(RAIDECCMAP_XLC_PAGE_PER_SHARED_PAGE)  // 4
#define RAIDECCMAP_TABLE_BLOCK_PROTECT_BASIC_TAG_NUM		(1)  // This define means table tag num under (data) block raid mode, table is still block raid

// -----  WL Protect -----
#define RAIDECCMAP_SLC_WORDLINE_PROTECT_NUM		(2)
#define RAIDECCMAP_XLC_WORDLINE_PROTECT_NUM		(4)

// Old style define
#define RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM		(RAIDECCMAP_SLC_PAGE_PER_WL)  // 4
#define RAIDECCMAP_1WL_PROTECTION_IN_XLC_PARITY_TAG_IDX_NUM		(RAIDECCMAP_XLC_PAGE_PER_WL)  // 16

#define RAIDECCMAP_SLC_DATA_WL_PROTECT_BASIC_TAG_NUM		(RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM * RAIDECCMAP_SLC_WORDLINE_PROTECT_NUM)  // 8
#define RAIDECCMAP_XLC_DATA_WL_PROTECT_BASIC_TAG_NUM		(RAIDECCMAP_1WL_PROTECTION_IN_XLC_PARITY_TAG_IDX_NUM * RAIDECCMAP_XLC_WORDLINE_PROTECT_NUM)  // 64
#define RAIDECCMAP_TABLE_WL_PROTECT_BASIC_TAG_NUM			(1)  // This define means table tag num under (data) WL protect mode, table is still block raid

// N48R use gRaidECCMapBasicInfo
/*
// Required tag num without switch num
#define RAIDECCMAP_SLC_DATA_BASIC_TAG_NUM_BY_FLASH		//(gRaidECCMapBasicInfo)
#define RAIDECCMAP_XLC_DATA_BASIC_TAG_NUM_BY_FLASH		//(gRaidECCMapBasicInfo)
*/
#define RAIDECCMAP_TABLE_DATA_BASIC_TAG_NUM_BY_FLASH		(1)


//**********************
// Switch Num
//**********************
// -----  Block Protect -----
#define RAIDECCMAP_SLC_DATA_BLOCK_PROTECT_TAG_SWITCH_NUM	(1)
#define RAIDECCMAP_XLC_DATA_BLOCK_PROTECT_TAG_SWITCH_NUM	(1)
#define RAIDECCMAP_TABLE_BLOCK_PROTECT_TAG_SWITCH_NUM		(2)

// -----  WL Protect -----
#define RAIDECCMAP_SLC_DATA_WL_PROTECT_TAG_SWITCH_NUM	(1)
#define RAIDECCMAP_XLC_DATA_WL_PROTECT_TAG_SWITCH_NUM	(1)
#define RAIDECCMAP_TABLE_WL_PROTECT_TAG_SWITCH_NUM		(2)

#define RAIDECCMAP_SLC_DATA_TAG_SWITCH_NUM_BY_FLASH		(1)
#define RAIDECCMAP_XLC_DATA_TAG_SWITCH_NUM_BY_FLASH		(1)
#define RAIDECCMAP_TABLE_DATA_TAG_SWITCH_NUM_BY_FLASH	(2)


//**********************
// FW Tag Related
//**********************
// -----  Block Raid  -----
// SLC GR, 4
// XLC GR, 0
// XLC GCGR, 4
// Table, 2
// InitInfo, 2
// SLC Copy Unit, 4
// XLC Copy Unit, 4
// Table/InitInfo Copy Unit, 2
// SPOR SLC GR, 4
// SPOR XLC GR, 0
// SPOR Table, 2
#define RAIDECCMAP_SLC_GR_BLOCK_PROTECT_FW_TAG_NUM							(RAIDECCMAP_SLC_DATA_BLOCK_PROTECT_BASIC_TAG_NUM * RAIDECCMAP_SLC_DATA_BLOCK_PROTECT_TAG_SWITCH_NUM)
#define RAIDECCMAP_XLC_GR_BLOCK_PROTECT_FW_TAG_NUM							(0)
//#define RAIDECCMAP_XLC_GR_BLOCK_PROTECT_FW_TAG_NUM						(RAIDECCMAP_XLC_DATA_BLOCK_PROTECT_BASIC_TAG_NUM * RAIDECCMAP_XLC_DATA_BLOCK_PROTECT_TAG_SWITCH_NUM)
#define RAIDECCMAP_XLC_GCGR_BLOCK_PROTECT_FW_TAG_NUM						(RAIDECCMAP_XLC_DATA_BLOCK_PROTECT_BASIC_TAG_NUM * RAIDECCMAP_XLC_DATA_BLOCK_PROTECT_TAG_SWITCH_NUM)
#define RAIDECCMAP_TABLE_BLOCK_PROTECT_FW_TAG_NUM							(RAIDECCMAP_TABLE_BLOCK_PROTECT_BASIC_TAG_NUM * RAIDECCMAP_TABLE_BLOCK_PROTECT_TAG_SWITCH_NUM)
#define RAIDECCMAP_INITINFO_BLOCK_PROTECT_FW_TAG_NUM						(RAIDECCMAP_TABLE_BLOCK_PROTECT_BASIC_TAG_NUM * RAIDECCMAP_TABLE_BLOCK_PROTECT_TAG_SWITCH_NUM)
#define RAIDECCMAP_COPY_UNIT_SLC_DATA_BLOCK_PROTECT_FW_TAG_NUM				(RAIDECCMAP_SLC_DATA_BLOCK_PROTECT_BASIC_TAG_NUM * RAIDECCMAP_SLC_DATA_BLOCK_PROTECT_TAG_SWITCH_NUM)
#define RAIDECCMAP_COPY_UNIT_XLC_DATA_BLOCK_PROTECT_FW_TAG_NUM				(RAIDECCMAP_XLC_DATA_BLOCK_PROTECT_BASIC_TAG_NUM * RAIDECCMAP_XLC_DATA_BLOCK_PROTECT_TAG_SWITCH_NUM)
#define RAIDECCMAP_COPY_UNIT_TABLE_AND_INITINFO_BLOCK_PROTECT_FW_TAG_NUM	(RAIDECCMAP_TABLE_BLOCK_PROTECT_BASIC_TAG_NUM * RAIDECCMAP_TABLE_BLOCK_PROTECT_TAG_SWITCH_NUM)
#define RAIDECCMAP_SPOR_SLC_DATA_BLOCK_PROTECT_FW_TAG_NUM					(RAIDECCMAP_SLC_DATA_BLOCK_PROTECT_BASIC_TAG_NUM * RAIDECCMAP_SLC_DATA_BLOCK_PROTECT_TAG_SWITCH_NUM)
#define RAIDECCMAP_SPOR_XLC_DATA_BLOCK_PROTECT_FW_TAG_NUM					(0)
//#define RAIDECCMAP_SPOR_XLC_DATA_BLOCK_PROTECT_FW_TAG_NUM					(RAIDECCMAP_XLC_DATA_BLOCK_PROTECT_BASIC_TAG_NUM * RAIDECCMAP_XLC_DATA_BLOCK_PROTECT_TAG_SWITCH_NUM)
#define RAIDECCMAP_SPOR_TABLE_BLOCK_PROTECT_FW_TAG_NUM						(RAIDECCMAP_TABLE_BLOCK_PROTECT_BASIC_TAG_NUM * RAIDECCMAP_TABLE_BLOCK_PROTECT_TAG_SWITCH_NUM)

// SLC GR, 0 ~ 3
// XLC GR, --
// XLC GCGR, 4 ~ 7
// Table, 8 ~ 9
// InitInfo, 10 ~ 11
// SLC Copy Unit, 12 ~ 15
// XLC Copy Unit, 16 ~ 19
// Table/InitInfo Copy Unit, 20 ~ 21
// SPOR SLC GR, 22 ~ 25
// SPOR XLC GR, --
// SPOR Table, 26 ~ 27
// End, 28
#define RAIDECCMAP_SLC_GR_BLOCK_PROTECT_START_FW_TAG						(0)
#define RAIDECCMAP_XLC_GR_BLOCK_PROTECT_START_FW_TAG						(RAIDECCMAP_SLC_GR_BLOCK_PROTECT_START_FW_TAG + RAIDECCMAP_SLC_GR_BLOCK_PROTECT_FW_TAG_NUM)
#define RAIDECCMAP_XLC_GCGR_BLOCK_PROTECT_START_FW_TAG						(RAIDECCMAP_XLC_GR_BLOCK_PROTECT_START_FW_TAG + RAIDECCMAP_XLC_GR_BLOCK_PROTECT_FW_TAG_NUM)
#define RAIDECCMAP_TABLE_BLOCK_PROTECT_START_FW_TAG							(RAIDECCMAP_XLC_GCGR_BLOCK_PROTECT_START_FW_TAG + RAIDECCMAP_XLC_GCGR_BLOCK_PROTECT_FW_TAG_NUM)
#define RAIDECCMAP_INITINFO_BLOCK_PROTECT_START_FW_TAG						(RAIDECCMAP_TABLE_BLOCK_PROTECT_START_FW_TAG + RAIDECCMAP_TABLE_BLOCK_PROTECT_FW_TAG_NUM)
#define RAIDECCMAP_COPY_UNIT_SLC_DATA_BLOCK_PROTECT_START_FW_TAG			(RAIDECCMAP_INITINFO_BLOCK_PROTECT_START_FW_TAG + RAIDECCMAP_INITINFO_BLOCK_PROTECT_FW_TAG_NUM)
#define RAIDECCMAP_COPY_UNIT_XLC_DATA_BLOCK_PROTECT_START_FW_TAG			(RAIDECCMAP_COPY_UNIT_SLC_DATA_BLOCK_PROTECT_START_FW_TAG + RAIDECCMAP_COPY_UNIT_SLC_DATA_BLOCK_PROTECT_FW_TAG_NUM)
#define RAIDECCMAP_COPY_UNIT_TABLE_AND_INITINFO_BLOCK_PROTECT_START_FW_TAG	(RAIDECCMAP_COPY_UNIT_XLC_DATA_BLOCK_PROTECT_START_FW_TAG + RAIDECCMAP_COPY_UNIT_XLC_DATA_BLOCK_PROTECT_FW_TAG_NUM)
#define RAIDECCMAP_SPOR_SLC_DATA_BLOCK_PROTECT_START_FW_TAG					(RAIDECCMAP_COPY_UNIT_TABLE_AND_INITINFO_BLOCK_PROTECT_START_FW_TAG + RAIDECCMAP_COPY_UNIT_TABLE_AND_INITINFO_BLOCK_PROTECT_FW_TAG_NUM)
#define RAIDECCMAP_SPOR_XLC_DATA_BLOCK_PROTECT_START_FW_TAG					(RAIDECCMAP_SPOR_SLC_DATA_BLOCK_PROTECT_START_FW_TAG + RAIDECCMAP_SPOR_SLC_DATA_BLOCK_PROTECT_FW_TAG_NUM)
#define RAIDECCMAP_SPOR_TABLE_BLOCK_PROTECT_START_FW_TAG					(RAIDECCMAP_SPOR_XLC_DATA_BLOCK_PROTECT_START_FW_TAG + RAIDECCMAP_SPOR_XLC_DATA_BLOCK_PROTECT_FW_TAG_NUM)
#define RAIDECCMAP_BLOCK_PROTECT_END_FW_TAG									(RAIDECCMAP_SPOR_TABLE_BLOCK_PROTECT_START_FW_TAG + RAIDECCMAP_SPOR_TABLE_BLOCK_PROTECT_FW_TAG_NUM)

// -----  WL Protect  -----
// SLC GR, 8
// XLC GR, 0
// XLC GCGR, 64
// Table, 2
// InitInfo, 2
// SLC Copy Unit, 8
// XLC Copy Unit, 64
// Table/InitInfo Copy Unit, 2
// SPOR SLC GR, 8
// SPOR XLC GR, 0
// SPOR Table, 2
#define RAIDECCMAP_SLC_GR_WL_PROTECT_FW_TAG_NUM							(RAIDECCMAP_SLC_DATA_WL_PROTECT_BASIC_TAG_NUM * RAIDECCMAP_SLC_DATA_WL_PROTECT_TAG_SWITCH_NUM)
#define RAIDECCMAP_XLC_GR_WL_PROTECT_FW_TAG_NUM							(0)
//#define RAIDECCMAP_XLC_GR_WL_PROTECT_FW_TAG_NUM						(RAIDECCMAP_XLC_DATA_WL_PROTECT_BASIC_TAG_NUM * RAIDECCMAP_XLC_DATA_WL_PROTECT_TAG_SWITCH_NUM)
#define RAIDECCMAP_XLC_GCGR_WL_PROTECT_FW_TAG_NUM						(RAIDECCMAP_XLC_DATA_WL_PROTECT_BASIC_TAG_NUM * RAIDECCMAP_XLC_DATA_WL_PROTECT_TAG_SWITCH_NUM)
#define RAIDECCMAP_TABLE_WL_PROTECT_FW_TAG_NUM							(RAIDECCMAP_TABLE_WL_PROTECT_BASIC_TAG_NUM * RAIDECCMAP_TABLE_WL_PROTECT_TAG_SWITCH_NUM)
#define RAIDECCMAP_INITINFO_WL_PROTECT_FW_TAG_NUM						(RAIDECCMAP_TABLE_WL_PROTECT_BASIC_TAG_NUM * RAIDECCMAP_TABLE_WL_PROTECT_TAG_SWITCH_NUM)
#define RAIDECCMAP_COPY_UNIT_SLC_DATA_WL_PROTECT_FW_TAG_NUM				(RAIDECCMAP_SLC_DATA_WL_PROTECT_BASIC_TAG_NUM * RAIDECCMAP_SLC_DATA_WL_PROTECT_TAG_SWITCH_NUM)
#define RAIDECCMAP_COPY_UNIT_XLC_DATA_WL_PROTECT_FW_TAG_NUM				(RAIDECCMAP_XLC_DATA_WL_PROTECT_BASIC_TAG_NUM * RAIDECCMAP_XLC_DATA_WL_PROTECT_TAG_SWITCH_NUM)
#define RAIDECCMAP_COPY_UNIT_TABLE_AND_INITINFO_WL_PROTECT_FW_TAG_NUM	(RAIDECCMAP_TABLE_WL_PROTECT_BASIC_TAG_NUM * RAIDECCMAP_TABLE_WL_PROTECT_TAG_SWITCH_NUM)
#define RAIDECCMAP_SPOR_SLC_DATA_WL_PROTECT_FW_TAG_NUM					(RAIDECCMAP_SLC_DATA_WL_PROTECT_BASIC_TAG_NUM * RAIDECCMAP_SLC_DATA_WL_PROTECT_TAG_SWITCH_NUM)
#define RAIDECCMAP_SPOR_XLC_DATA_WL_PROTECT_FW_TAG_NUM					(0)
//#define RAIDECCMAP_SPOR_XLC_DATA_WL_PROTECT_FW_TAG_NUM				(RAIDECCMAP_XLC_DATA_WL_PROTECT_BASIC_TAG_NUM * RAIDECCMAP_XLC_DATA_WL_PROTECT_TAG_SWITCH_NUM)
#define RAIDECCMAP_SPOR_TABLE_WL_PROTECT_FW_TAG_NUM						(RAIDECCMAP_TABLE_WL_PROTECT_BASIC_TAG_NUM * RAIDECCMAP_TABLE_WL_PROTECT_TAG_SWITCH_NUM)

// SLC GR, 0 ~ 7
// XLC GR, --
// XLC GCGR, 8 ~ 71
// Table, 72 ~ 73
// InitInfo, 74 ~ 75
// SLC Copy Unit, 76 ~ 83
// XLC Copy Unit, 84 ~ 147
// Table/InitInfo Copy Unit, 148 ~ 149
// SPOR SLC GR, 150 ~ 157
// SPOR XLC GR, --
// SPOR Table, 158 ~ 159
// End, 160
#define RAIDECCMAP_SLC_GR_WL_PROTECT_START_FW_TAG						(0)
#define RAIDECCMAP_XLC_GR_WL_PROTECT_START_FW_TAG						(RAIDECCMAP_SLC_GR_WL_PROTECT_START_FW_TAG + RAIDECCMAP_SLC_GR_WL_PROTECT_FW_TAG_NUM)
#define RAIDECCMAP_XLC_GCGR_WL_PROTECT_START_FW_TAG						(RAIDECCMAP_XLC_GR_WL_PROTECT_START_FW_TAG + RAIDECCMAP_XLC_GR_WL_PROTECT_FW_TAG_NUM)
#define RAIDECCMAP_TABLE_WL_PROTECT_START_FW_TAG						(RAIDECCMAP_XLC_GCGR_WL_PROTECT_START_FW_TAG + RAIDECCMAP_XLC_GCGR_WL_PROTECT_FW_TAG_NUM)
#define RAIDECCMAP_INITINFO_WL_PROTECT_START_FW_TAG						(RAIDECCMAP_TABLE_WL_PROTECT_START_FW_TAG + RAIDECCMAP_TABLE_WL_PROTECT_FW_TAG_NUM)
#define RAIDECCMAP_COPY_UNIT_SLC_DATA_WL_PROTECT_START_FW_TAG			(RAIDECCMAP_INITINFO_WL_PROTECT_START_FW_TAG + RAIDECCMAP_INITINFO_WL_PROTECT_FW_TAG_NUM)
#define RAIDECCMAP_COPY_UNIT_XLC_DATA_WL_PROTECT_START_FW_TAG			(RAIDECCMAP_COPY_UNIT_SLC_DATA_WL_PROTECT_START_FW_TAG + RAIDECCMAP_COPY_UNIT_SLC_DATA_WL_PROTECT_FW_TAG_NUM)
#define RAIDECCMAP_COPY_UNIT_TABLE_AND_INITINFO_WL_PROTECT_START_FW_TAG	(RAIDECCMAP_COPY_UNIT_XLC_DATA_WL_PROTECT_START_FW_TAG + RAIDECCMAP_COPY_UNIT_XLC_DATA_WL_PROTECT_FW_TAG_NUM)
#define RAIDECCMAP_SPOR_SLC_DATA_WL_PROTECT_START_FW_TAG				(RAIDECCMAP_COPY_UNIT_TABLE_AND_INITINFO_WL_PROTECT_START_FW_TAG + RAIDECCMAP_COPY_UNIT_TABLE_AND_INITINFO_WL_PROTECT_FW_TAG_NUM)
#define RAIDECCMAP_SPOR_XLC_DATA_WL_PROTECT_START_FW_TAG				(RAIDECCMAP_SPOR_SLC_DATA_WL_PROTECT_START_FW_TAG + RAIDECCMAP_SPOR_SLC_DATA_WL_PROTECT_FW_TAG_NUM)
#define RAIDECCMAP_SPOR_TABLE_WL_PROTECT_START_FW_TAG					(RAIDECCMAP_SPOR_XLC_DATA_WL_PROTECT_START_FW_TAG + RAIDECCMAP_SPOR_XLC_DATA_WL_PROTECT_FW_TAG_NUM)
#define RAIDECCMAP_WL_PROTECT_END_FW_TAG								(RAIDECCMAP_SPOR_TABLE_WL_PROTECT_START_FW_TAG + RAIDECCMAP_SPOR_TABLE_WL_PROTECT_FW_TAG_NUM)


//**********************
// In RAM Tag Num Related (imply buffer usage)
//**********************
// -----  Block Raid  -----
#define RAIDECCMAP_DECODE_BLOCK_PROTECT_GROUP_SIZE				(4)
// SLC and XLC GR share the same group, so separately define them
#define RAIDECCMAP_SLC_GR_BLOCK_PROTECT_IN_RAM_TAG_NUM			(RAIDECCMAP_SLC_DATA_BLOCK_PROTECT_BASIC_TAG_NUM)  // 4
#define RAIDECCMAP_XLC_GR_BLOCK_PROTECT_IN_RAM_TAG_NUM			(0)  // Actually useless
#define RAIDECCMAP_GR_BLOCK_PROTECT_GROUP_SIZE					(MAX(RAIDECCMAP_SLC_GR_BLOCK_PROTECT_IN_RAM_TAG_NUM, RAIDECCMAP_XLC_GR_BLOCK_PROTECT_IN_RAM_TAG_NUM))
// RAIDECCMAP_GC_AND_COPY_UNIT_BLOCK_PROTECT_GROUP_SIZE should be the same as "RAM_GCGR_RAIDECC / FRAMES_PER_PAGE" in bmu_api.h
#define RAIDECCMAP_GC_AND_COPY_UNIT_BLOCK_PROTECT_GROUP_SIZE	(RAIDECCMAP_XLC_SHARED_PAGE_PER_WL)  // 4
#define RAIDECCMAP_TABLE_BLOCK_PROTECT_GROUP_SIZE				(1)
#define RAIDECCMAP_INITINFO_BLOCK_PROTECT_GROUP_SIZE			(1)
// No need to concern decode part
#define RAIDECCMAP_TOTAL_BLOCK_PROTECT_IN_RAM_TAG_NUM			(RAIDECCMAP_GR_BLOCK_PROTECT_GROUP_SIZE + RAIDECCMAP_GC_AND_COPY_UNIT_BLOCK_PROTECT_GROUP_SIZE + RAIDECCMAP_TABLE_BLOCK_PROTECT_GROUP_SIZE + RAIDECCMAP_INITINFO_BLOCK_PROTECT_GROUP_SIZE)

// -----  WL Protect  -----
#define RAIDECCMAP_DECODE_WL_PROTECT_GROUP_SIZE					(4)
// SLC and XLC GR share the same group, so separately define them
#define RAIDECCMAP_SLC_GR_WL_PROTECT_IN_RAM_TAG_NUM				(RAIDECCMAP_SLC_DATA_WL_PROTECT_BASIC_TAG_NUM)  // 8
#define RAIDECCMAP_XLC_GR_WL_PROTECT_IN_RAM_TAG_NUM				(0)  // Actually useless
#define RAIDECCMAP_GR_WL_PROTECT_GROUP_SIZE						(MAX(RAIDECCMAP_SLC_GR_WL_PROTECT_IN_RAM_TAG_NUM, RAIDECCMAP_XLC_GR_WL_PROTECT_IN_RAM_TAG_NUM))
// RAIDECCMAP_GC_AND_COPY_UNIT_WL_PROTECT_GROUP_SIZE should be the same as "RAM_GCGR_RAIDECC / FRAMES_PER_PAGE" in bmu_api.h
#define RAIDECCMAP_GC_AND_COPY_UNIT_WL_PROTECT_GROUP_SIZE		(RAIDECCMAP_XLC_SHARED_PAGE_PER_WL)  // 4
#define RAIDECCMAP_TABLE_WL_PROTECT_GROUP_SIZE					(1)
#define RAIDECCMAP_INITINFO_WL_PROTECT_GROUP_SIZE				(1)
// No need to concern decode part
#define RAIDECCMAP_TOTAL_WL_PROTECT_IN_RAM_TAG_NUM				(RAIDECCMAP_GR_WL_PROTECT_GROUP_SIZE + RAIDECCMAP_GC_AND_COPY_UNIT_WL_PROTECT_GROUP_SIZE + RAIDECCMAP_TABLE_WL_PROTECT_GROUP_SIZE + RAIDECCMAP_INITINFO_WL_PROTECT_GROUP_SIZE)

// N48R use gRaidECCMapBasicInfo
/*
#define RAIDECCMAP_DECODE_GROUP_SIZE_BY_FLASH					//(gRaidECCMapBasicInfo)
#define RAIDECCMAP_SLC_GR_IN_RAM_TAG_NUM_BY_FLASH				//(gRaidECCMapBasicInfo)
#define RAIDECCMAP_XLC_GR_IN_RAM_TAG_NUM_BY_FLASH				//(gRaidECCMapBasicInfo)  // Actually useless
#define RAIDECCMAP_GR_GROUP_SIZE_BY_FLASH						//(gRaidECCMapBasicInfo)
// RAIDECCMAP_GC_AND_COPY_UNIT_GROUP_SIZE_BY_FLASH should be the same as "RAM_GCGR_RAIDECC / FRAMES_PER_PAGE" in bmu_api.h
#define RAIDECCMAP_GC_AND_COPY_UNIT_GROUP_SIZE_BY_FLASH			//(gRaidECCMapBasicInfo)
#define RAIDECCMAP_TABLE_GROUP_SIZE_BY_FLASH					//(gRaidECCMapBasicInfo)
#define RAIDECCMAP_INITINFO_GROUP_SIZE_BY_FLASH					//(gRaidECCMapBasicInfo)
// No need to concern decode part
#define RAIDECCMAP_TOTAL_IN_RAM_TAG_NUM							(RAIDECCMAP_GR_GROUP_SIZE_BY_FLASH + RAIDECCMAP_GC_AND_COPY_UNIT_GROUP_SIZE_BY_FLASH + RAIDECCMAP_TABLE_GROUP_SIZE_BY_FLASH + RAIDECCMAP_INITINFO_GROUP_SIZE_BY_FLASH)
*/


//**********************
// Struct Size Related
//**********************
// Choose max value between WL Protect and Block Raid

#define RAIDECCMAP_PARITYMAP_NUM		(256)  // N48R use 237 at most

// WL Protect use 16, Block Protect use 12 (XLC part is actually useless)
#define RAIDECCMAP_TAG_MGR_ENTRY_NUM	(\
	RAIDECCMAP_SLC_GR_WL_PROTECT_IN_RAM_TAG_NUM * RAIDECCMAP_SLC_DATA_WL_PROTECT_TAG_SWITCH_NUM +\
	RAIDECCMAP_XLC_GR_WL_PROTECT_IN_RAM_TAG_NUM * RAIDECCMAP_XLC_DATA_WL_PROTECT_TAG_SWITCH_NUM +\
	RAIDECCMAP_GC_AND_COPY_UNIT_WL_PROTECT_GROUP_SIZE * RAIDECCMAP_XLC_DATA_WL_PROTECT_TAG_SWITCH_NUM +\
	RAIDECCMAP_TABLE_WL_PROTECT_GROUP_SIZE * RAIDECCMAP_TABLE_WL_PROTECT_TAG_SWITCH_NUM +\
	RAIDECCMAP_INITINFO_WL_PROTECT_GROUP_SIZE * RAIDECCMAP_TABLE_WL_PROTECT_TAG_SWITCH_NUM\
)

#define RAIDECCMAP_OPEN_BLOCK_DESCRIPTION_NUM		(RAIDECCMAP_XLC_SHARED_PAGE_PER_WL)  // LUXT -> 4 Tags

/*
 * TODO:
 * Using RS define is not correct solution to evaluate number of parity.
 */
#if (D1_UNIT_EN)
#define RAIDECCMAP_SPOR_SCAN_GR_MAX_PARITY_NUM		(RAIDECCMAP_SLC_GR_WL_PROTECT_FW_TAG_NUM)
#else  /* (D1_UNIT_EN) */
#error "N48R should enable D1 unit"
#endif  /* (D1_UNIT_EN) */

#define RAIDECCMAP_GR_VT_ARRAY_SIZE			(RAIDECCMAP_GR_WL_PROTECT_GROUP_SIZE)  // 8, WL 8, Block 4
#define RAIDECCMAP_GC_VT_ARRAY_SIZE			(RAIDECCMAP_GC_AND_COPY_UNIT_WL_PROTECT_GROUP_SIZE)  // 4, WL 4, Block 4
#define RAIDECCMAP_TABLE_VT_ARRAY_SIZE		(RAIDECCMAP_TABLE_WL_PROTECT_GROUP_SIZE)  // 1, WL 1, Block 1
#define RAIDECC_RESERVE_BYTE				(224)  // Choose min, WL 224, Block Raid 236

/*
 * ---------------------------------------------------------------------------------------------------
 *  enum
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *  data types
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *   macros
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *   extern global variables
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */

#endif  /* (MICRON_FSP_EN && IM_N48R) */

#endif  /* RAIDECCMAP_FOR_N48R_H_ */
