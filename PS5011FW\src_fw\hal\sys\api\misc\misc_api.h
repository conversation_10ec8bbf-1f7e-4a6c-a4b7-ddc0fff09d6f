#ifndef _MISC_API_H_
#define _MISC_API_H_

#include "setup.h"
#include "hal/sys/reg/sys_pd0_reg.h"
#include "hal/sys/reg/sys_pd1_reg.h"
#if PS5017_EN
#include "debug/debug.h" // For M_FW_TODO()
#endif /* PS5017_EN */

#define DEBUG_GPIO_ENABLE							(0)//V6 TEST
#define MISC_SIM_LOG_0_DEFAULT_VALUE				(0x00)
#define MISC_SIM_LOG_7_DEFAULT_VALUE				(0xFF)
#define MISC_SIM_LOG_8_DEFAULT_VALUE				(0xFF)
#if PS5017_EN
#define MISC_SIM_LOG_8_DETECT_PMIC_VDT_CNT			(0x01)
#endif /* PS5017_EN */

#if PS5021_EN
#define M_CLR_SIM_LOG(REG, VAL)						(R8_SYS0_MISC_CTRL[R8_SYS0_FW_SIM_INFO1_PD0 + ((REG > 7) ? (REG + 32) : REG)] &= ~(VAL))
#define M_SET_SIM_LOG(REG, VAL)						(R8_SYS0_MISC_CTRL[R8_SYS0_FW_SIM_INFO1_PD0 + ((REG > 7) ? (REG + 32) : REG)] |=  (VAL))
#define M_ASSIGN_SIM_LOG(REG, VAL)					(R8_SYS0_MISC_CTRL[R8_SYS0_FW_SIM_INFO1_PD0 + ((REG > 7) ? (REG + 32) : REG)]  =  (VAL))
#define M_GET_SIM_LOG(REG)							(R8_SYS0_MISC_CTRL[R8_SYS0_FW_SIM_INFO1_PD0 + ((REG > 7) ? (REG + 32) : REG)])
#define	M_GET_CHIP_ID0()							(R32_SYS0_MISC_CTRL[R32_SYS0_CHIP_ID_CFG1])
#define	M_GET_CHIP_ID1()							(R32_SYS0_MISC_CTRL[R32_SYS0_CHIP_ID_CFG2])
#define M_MISC_DCACHE_EN()							// E21 not support
#define M_MISC_XZIP_EN()							(R32_SYS1_MISC_CTRL[R32_SYS1_MISC_SYS_PD1_MISC] |= CR_SYS_XZIP_EN_BIT)
#define	M_COP0_CORE_STALL()							// E21 not support
#define	M_COP0_CORE_RUN()							// E21 not support
#else /* PS5021_EN */
#define M_CLR_SIM_LOG(REG, VAL)						(R8_SYS0_MISCL[R8_SYS0_SYS_SIM_CTRL0 + (REG)] &= ~(VAL))
#define M_SET_SIM_LOG(REG, VAL)						(R8_SYS0_MISCL[R8_SYS0_SYS_SIM_CTRL0 + (REG)] |= (VAL))
#define M_ASSIGN_SIM_LOG(REG, VAL)					(R8_SYS0_MISCL[R8_SYS0_SYS_SIM_CTRL0 + (REG)]  = (VAL))
#define M_GET_SIM_LOG(REG)							(R8_SYS0_MISCL[R8_SYS0_SYS_SIM_CTRL0 + (REG)])
#define	M_GET_CHIP_ID0()							(R32_SYS0_MISCL[R32_SYS0_SYS_CHIP_ID0])
#define	M_GET_CHIP_ID1()							(R32_SYS0_MISCL[R32_SYS0_SYS_CHIP_ID1])
#define M_MISC_DCACHE_EN()							(R32_SYS1_MISCH[R32_SYS1_SYS_CPU_CFG] |= CR_CPU_DCACHE_EN_BIT)
#define M_MISC_XZIP_EN()							(R32_SYS1_MISCH[R32_SYS1_SYS_PD1_MISCH_CTRL] |= CR_SYS_XZIP_EN_BIT)
#define	M_COP0_CORE_STALL()							(R32_SYS1_MISCH[R32_SYS1_SYS_COP_CONFIG] &= ~CR_COP0_CORE_RUN_BIT)
#define	M_COP0_CORE_RUN()							(R32_SYS1_MISCH[R32_SYS1_SYS_COP_CONFIG] |= CR_COP0_CORE_RUN_BIT)
#endif /* PS5021_EN */

#define M_CHECK_SYS_SATA_XTAL_SEL()					(R32_SYS0_MISCL[R32_SYS0_SYS_HW_SETTING1] & CR_SATA_XTAL_SEL_BIT)
#define M_ENABLE_SYS_SATA_XTAL()					(R32_SYS0_MISCL[R32_SYS0_SYS_HW_SETTING1] |= CR_SATA_XTAL_SEL_BIT)
#define M_DISABLE_SYS_SATA_XTAL()					(R32_SYS0_MISCL[R32_SYS0_SYS_HW_SETTING1] &= ~(CR_SATA_XTAL_SEL_BIT))
#define M_SYSTEM0_CHECK_DEVSLP_SIGNAL()				(R32_SYS0_MISCH[R32_SYS0_SYS_PAD_I] & SYS_PAD_I_XDEVSLP_I_BIT)
#define	M_RESET_HBA_PD1()							(R32_SYS0_MISCH[R32_SYS0_SYS_HBA_SYS_SRST] |= HBA_SYS_PD1_SRST_BIT)
#define	M_DE_RESET_HBA_PD1()						(R32_SYS0_MISCH[R32_SYS0_SYS_HBA_SYS_SRST] &= ~(HBA_SYS_PD1_SRST_BIT))
#define	M_GET_UNLOCK_STATUS()						((R32_SYS1_MISCL[R32_SYS1_SYS_JG_UNLOCK] & JG_UNLOCK_BIT) >> JG_UNLOCK_SHIFT)
#define	M_SET_UNLOCK_PASSWD(X)						(R32_SYS1_MISCL[R32_SYS1_SYS_JG_UNLOCK_PASSWD] = (X))

#if PS5013_EN
#define	M_CLR_SYS_ROM_CR_ITC_VLD()					(R32_SYS1_MISCH[R32_SYS1_SYS_ROM_CRC_GOLD1] &= ~CR_ITC_VLD)
#define	M_SET_SYS_ROM_CR_ITC_VLD()					(R32_SYS1_MISCH[R32_SYS1_SYS_ROM_CRC_GOLD1] |= CR_ITC_VLD)
#endif /* PS5013_EN */

#if PS5017_EN
#define M_SET_CR_SYS_PCAE2E_EN()					(R32_SYS0_MISCH[R32_SYS0_SYS_PCAE2E_CTRL] |= CR_SYS_PCAE2E_EN_BIT)
#define M_SET_CR_SYS_PCAE2E_CFG(X)					(R32_SYS0_MISCH[R32_SYS0_SYS_PCAE2E_CTRL] |= (X & CR_SYS_PCAE2E_CFG_MASK) << CR_SYS_PCAE2E_CFG_SHIFT)
#elif PS5021_EN /* PS5017_EN */
#define M_SET_CR_SYS_PCAE2E_EN()					(R32_SYS1_MISC_CTRL[R32_SYS1_MISC_PCAE2E_CTRL] |= CR_SYS_PCAE2E_EN_BIT)
#define M_SET_CR_SYS_PCAE2E_CFG(x)					(R32_SYS1_MISC_CTRL[R32_SYS1_MISC_PCAE2E_CTRL] = (R32_SYS1_MISC_CTRL[R32_SYS1_MISC_PCAE2E_CTRL] & (~CR_SYS_PCAE2E_CFG)) | (((x) << 1) & CR_SYS_PCAE2E_CFG))
#else /* PS5017_EN */
#define M_SET_CR_SYS_PCAE2E_EN()					(R32_SYS1_MISCH[R32_SYS0_SYS_PCAE2E_CTRL] |= CR_SYS_PCAE2E_EN_BIT)
#define M_SET_CR_SYS_PCAE2E_CFG(X)					(R32_SYS1_MISCH[R32_SYS0_SYS_PCAE2E_CTRL] |= (X & CR_SYS_PCAE2E_CFG_MASK) << CR_SYS_PCAE2E_CFG_SHIFT)
#endif /* PS5017_EN */

#if PS5021_EN
#define	M_DMAC_GET_IDLE()							(R32_SYS1_IP[R32_SYS1_IP_DMAC_REG] & SR_DMA_IDLE)
#else /* PS5021_EN */
#define	M_DMAC_GET_IDLE()							(R32_SYS1_MISCH[R32_SYS1_SYS_DMA_REG] & SR_DMA_IDLE)
#endif /* PS5021_EN */

/*
 *
 *  |--------------------------------------------------------------------------|
 *  | SIM Reg  | Default value | Usage                                         |
 *  |--------------------------------------------------------------------------|
 *  | 0        | 00            | [7:0] Boot Code Usage                         |
 *  |--------------------------------------------------------------------------|
 *  | 1        | 00            | [7:0] Boot Code Usage                         |
 *  |--------------------------------------------------------------------------|
 *  | 2        | FF            | [7:0] Host Store CTag for ISP Jump            |
 *  |--------------------------------------------------------------------------|
 *  | 3        | FF            | [7:0] HMB Reset                               |
 *  |--------------------------------------------------------------------------|
 *  | 4        | 00            | [0] ISPJump Log                               |
 *  |--------------------------------------------------------------------------|
 *  | 5        | 00            |                                               |
 *  |--------------------------------------------------------------------------|
 *  | 6        | FF            |                                               |
 *  |--------------------------------------------------------------------------|
 *  | 7        | FF            | [7:0] LPM power state bitmap                  |
 *  |--------------------------------------------------------------------------|
 *  | 8        | FF            | [7:0] LPM back up function bit                |
 *  |--------------------------------------------------------------------------|
 *
 */
// SIM0

// SIM1
#define	M_GET_CR_SIM_CTRL1()						(M_GET_SIM_LOG(1))
#define	M_SET_CR_SIM_CTRL1(x)						(M_ASSIGN_SIM_LOG(1, x))
#define	M_SET_OR_CR_SIM_CTRL1(x)					(M_SET_SIM_LOG(1, x))
// SIM2

// SIM3
#define	M_MISC_GET_HMB_LOG_IN_SIM_CTRL3()			(M_GET_SIM_LOG(3))
#define	M_MISC_SET_HMB_LOG_IN_SIM_CTRL3(x)			(M_CLR_SIM_LOG(3, x)) //default 0xFF
#define	M_MISC_CLEAR_HMB_LOG_IN_SIM_CTRL3(x)		(M_SET_SIM_LOG(3, x))	//default 0xFF
// SIM4
#if PS5013_EN
#define M_MISC_SET_SYS0_ISPJUMP()					(M_SET_SIM_LOG(4, SYS0_SYS_CR_ISPJUMP_BIT))
#define M_MISC_CLEAR_SYS0_ISPJUMP()					(M_CLR_SIM_LOG(4, SYS0_SYS_CR_ISPJUMP_BIT))
#define M_MISC_IS_SYS0_ISPJUMP()					((M_GET_SIM_LOG(4)) & SYS0_SYS_CR_ISPJUMP_BIT)
#else /* PS5013_EN */
#define M_MISC_SET_SYS0_ISPJUMP()					M_FW_TODO() // SYS0_SYS_CR_ISPJUMP_BIT -> by user define
#define M_MISC_CLEAR_SYS0_ISPJUMP()					M_FW_TODO() // SYS0_SYS_CR_ISPJUMP_BIT -> by user define
#define M_MISC_IS_SYS0_ISPJUMP()					M_FW_TODO() // SYS0_SYS_CR_ISPJUMP_BIT -> by user define
#endif /* PS5013_EN */

// SIM5

// SIM6

// SIM7
#define	M_GET_CR_SIM_CTRL7()						(M_GET_SIM_LOG(7))
#define	M_SET_CR_SIM_CTRL7(x)						(M_ASSIGN_SIM_LOG(7, x))
// SIM8
#define	M_GET_CR_SIM_CTRL8()						(M_GET_SIM_LOG(8))
#define	M_SET_CR_SIM_CTRL8(x)						(M_ASSIGN_SIM_LOG(8, x))
#define	M_MISC_CLEAR_SIM_CTRL8(x)					(M_CLR_SIM_LOG(8, x))

#define	M_MISC_GET_D2H_LOG_IN_SIM_STS0()			(R16_SYS1_MISCL[R16_SYS1_SYS_SIM_STS0])
#define	M_MISC_SET_D2H_LOG_IN_SIM_STS0(x)			(R16_SYS1_MISCL[R16_SYS1_SYS_SIM_STS0] = (x))
#define	M_MISC_CLEAR_D2H_LOG_IN_SIM_STS0()			(R16_SYS1_MISCL[R16_SYS1_SYS_SIM_STS0] = 0)

#define M_SYSTEM0_DBUF_INIT(x)						(R32_SYS0_MISCH[R32_SYS0_SYS_DBUF_INIT] |= (x))
#define M_SYSTEM1_DBUF_INIT(x)						(R32_SYS1_MISCH[R32_SYS1_SYS_DBUF_CFG]  |= (x))
#define M_SYSTEM0_CHECK_DBUF_INIT_BUSY(x)			(R32_SYS0_MISCH[R32_SYS0_SYS_DBUF_INIT] & (x))
#define	M_SYSTEM1_CHECK_DBUF_INIT_BUSY(x)			(R32_SYS1_MISCH[R32_SYS1_SYS_DBUF_CFG] & (x))

#define	M_SYSTEM1_CLEAR_MISCH_CONTROL()				(R32_SYS1_MISCH[R32_SYS1_SYS_PD1_MISCH_CTRL] = 0)
#define	M_SYSTEM1_SET_MISCH_CONTROL(x)				(R32_SYS1_MISCH[R32_SYS1_SYS_PD1_MISCH_CTRL] |= (x))
#define	M_SYSTEM1_GET_MISCH_CONTROL()				(R32_SYS1_MISCH[R32_SYS1_SYS_PD1_MISCH_CTRL])
#define	M_SYSTEM1_SET_MISCH_CONTROL_BY_REG(x)		(R32_SYS1_MISCH[R32_SYS1_SYS_PD1_MISCH_CTRL] = (x))
#define	M_SYSTEM1_CLEAR_DMAC_REG()					(R32_SYS1_MISCH[R32_SYS1_SYS_DMA_REG] = 0)
#define	M_SYSTEM1_SET_DMAC_REG(x)					(R32_SYS1_MISCH[R32_SYS1_SYS_DMA_REG] |= (x))
#define	M_SYSTEM1_CLEAR_DBUF_CONFIG()				(R32_SYS1_MISCH[R32_SYS1_SYS_DBUF_CFG] = 0)
#define	M_SYSTEM1_SET_DBUF_CONFIG(x)				(R32_SYS1_MISCH[R32_SYS1_SYS_DBUF_CFG] |= (x))
#if PS5017_EN
#define	M_SYSTEM1_GET_DBUF_CONFIG()					M_FW_TODO()
#define	M_SYSTEM1_SET_DBUF_CONFIG_BY_REG(x)			M_FW_TODO()
#else /* PS5017_EN */
#define	M_SYSTEM1_GET_DBUF_CONFIG()					(R32_SYS1_MISCH[R32_SYS1_SYS_DBUF_CFG])
#define	M_SYSTEM1_SET_DBUF_CONFIG_BY_REG(x)			(R32_SYS1_MISCH[R32_SYS1_SYS_DBUF_CFG] = (x))
#endif /* PS5017_EN */
#define	M_SYSTEM1_ALL_CE_OUTPUT_DIS()				(R32_SYS1_PADC[R32_SYS1_PAD_FCEB_OE] &= ~(CR_FCEB_OE))
#define	M_SYSTEM1_ALL_CE_OUTPUT_EN()				(R32_SYS1_PADC[R32_SYS1_PAD_FCEB_OE] = CR_FCEB_OE)

#if PS5017_EN
#define M_SYSTEM1_WRITE_PROTECT_EN()				do { \
													R32_SYS1_PADC[R32_SYS1_PAD_FWPB_CFG] &= ~(CR_FWPB_O_BIT); \
													R32_SYS1_PADC[R32_SYS1_PAD_FWPB_CFG1] &= ~(CR_FWPB_O_BIT); \
													} while (0)
#define	M_SYSTEM1_WRITE_PROTECT_DIS()				do { \
													R32_SYS1_PADC[R32_SYS1_PAD_FWPB_CFG] |= (CR_FWPB_O_BIT); \
													R32_SYS1_PADC[R32_SYS1_PAD_FWPB_CFG1] |= (CR_FWPB_O_BIT); \
													} while (0)
#elif PS5021_EN /* PS5017_EN */
#define M_SYSTEM1_WRITE_PROTECT_EN()				do { \
													R32_SYS1_FLH_PAD[R32_SYS1_SYS_FLH_PAD_CH0_FWPB] &= ~CR_A_WPB_BIT; \
													R32_SYS1_FLH_PAD[R32_SYS1_SYS_FLH_PAD_CH1_FWPB] &= ~CR_A_WPB_BIT; \
													R32_SYS1_FLH_PAD[R32_SYS1_SYS_FLH_PAD_CH2_FWPB] &= ~CR_A_WPB_BIT; \
													R32_SYS1_FLH_PAD[R32_SYS1_SYS_FLH_PAD_CH3_FWPB] &= ~CR_A_WPB_BIT; \
													} while (0)
#define	M_SYSTEM1_WRITE_PROTECT_DIS(x)				do { \
													R32_SYS1_FLH_PAD[R32_SYS1_SYS_FLH_PAD_CH0_FWPB] |= CR_A_WPB_BIT; \
													R32_SYS1_FLH_PAD[R32_SYS1_SYS_FLH_PAD_CH1_FWPB] |= CR_A_WPB_BIT; \
													R32_SYS1_FLH_PAD[R32_SYS1_SYS_FLH_PAD_CH2_FWPB] |= CR_A_WPB_BIT; \
													R32_SYS1_FLH_PAD[R32_SYS1_SYS_FLH_PAD_CH3_FWPB] |= CR_A_WPB_BIT; \
													} while (0)
#else /* PS5017_EN */
#define M_SYSTEM1_WRITE_PROTECT_EN()				(R32_SYS1_PADC[R32_SYS1_PAD_FWPB_CFG] &= ~(CR_FWPB_O_BIT))
#define	M_SYSTEM1_WRITE_PROTECT_DIS(x)				(R32_SYS1_PADC[R32_SYS1_PAD_FWPB_CFG] |= (CR_FWPB_O_BIT))
#endif /* PS5017_EN */

#define	M_SYSTEM1_SET_ATCM_SWITCH_SRC_ADDR(x)		(R32_SYS1_MISCH[R32_SYS1_SYS_DMA_CPY_SRC0] = (x))
#define	M_SYSTEM1_SET_BTCM_SWITCH_SRC_ADDR(x)		(R32_SYS1_MISCH[R32_SYS1_SYS_DMA_CPY_SRC1] = (x))
#define	M_SYSTEM1_SET_ATCM_SWITCH_TARGET_ADDR(x)	(R32_SYS1_MISCH[R32_SYS1_SYS_DMA_CPY_TRG0] = (x))
#define	M_SYSTEM1_SET_BTCM_SWITCH_TARGET_ADDR(x)	(R32_SYS1_MISCH[R32_SYS1_SYS_DMA_CPY_TRG1] = (x))
#define	M_SYSTEM1_SET_ATCM_SWITCH_LENGTH(x)			(R32_SYS1_MISCH[R32_SYS1_SYS_DMA_CPY_LEN0] = (x))
#define	M_SYSTEM1_SET_BTCM_SWITCH_LENGTH(x)			(R32_SYS1_MISCH[R32_SYS1_SYS_DMA_CPY_LEN1] = (x))

#if (PS5017_EN || PS5021_EN)
#define M_SYSTEM1_CLEAR_PD1_DBUF_PARITY_CHECK()		M_FW_TODO()
#define M_SYSTEM1_CLEAR_AXI_RESPONSE_ERROR()		M_FW_TODO()
#define M_SYSTEM1_SET_AXI_RESPONSE_ERROR()			M_FW_TODO()
#else /* (PS5017_EN || PS5021_EN) */
#define M_SYSTEM1_CLEAR_PD1_DBUF_PARITY_CHECK()		(R32_SYS1_MISCH[R32_SYS1_SYS_DBUF_CFG] &= ~(CR_DBUF_PERR_CHK_EN_BIT))
#define M_SYSTEM1_CLEAR_AXI_RESPONSE_ERROR()		(R32_SYS1_MISCH[R32_SYS1_SYS_PD1_MISCH_CTRL] &= ~(CR_AXIM_RSP_ERR_EN_BIT))
#define M_SYSTEM1_SET_AXI_RESPONSE_ERROR()			(R32_SYS1_MISCH[R32_SYS1_SYS_PD1_MISCH_CTRL] |= CR_AXIM_RSP_ERR_EN_BIT)
#endif /* (PS5017_EN || PS5021_EN) */

#define M_MISC_SET_PD0_DBUF_INIT(Region)			(R32_SYS0_MISCH[R32_SYS0_SYS_DBUF_INIT] |= (Region))
#define M_MISC_CHECK_PD0_DBUF_INIT(Region)			(R32_SYS0_MISCH[R32_SYS0_SYS_DBUF_INIT] & (Region))

#define M_MISC_SET_PD1_DBUF_INIT(Region)			(R32_SYS1_MISCH[R32_SYS1_SYS_DBUF_CFG] |= (Region))
#define M_MISC_CHECK_PD1_DBUF_INIT(Region)			(R32_SYS1_MISCH[R32_SYS1_SYS_DBUF_CFG] & (Region))

#if PS5017_EN
#define	M_MISC_SET_RRAM_BANK(x)						M_FW_TODO()
#define	M_MISC_GET_AXIMON_ACCESS_ID()				M_FW_TODO()
#define	M_MISC_GET_DBUF2_ACCESS_ADDRESS()			M_FW_TODO()
#define	M_MISC_GET_DBUF3_ACCESS_ADDRESS()			M_FW_TODO()
#define	M_MISC_SET_AXIMON_DBUF2_START_ADDRESS(x)	M_FW_TODO()
#define	M_MISC_SET_AXIMON_DBUF2_START_LENGTH(x)		M_FW_TODO()
#define	M_MISC_DISABLE_AXIMON_DBUF2()				M_FW_TODO()
#define	M_MISC_SET_AXIMON_DBUF3_START_ADDRESS(x)	M_FW_TODO()
#define	M_MISC_SET_AXIMON_DBUF3_START_LENGTH(x)		M_FW_TODO()
#define	M_MISC_DISABLE_AXIMON_DBUF3()				M_FW_TODO()
#else /* PS5017_EN */
#define	M_MISC_CHECK_FLASH_CORE_POWER()				(R32_SYS0_MISCH[R32_SYS0_SYS_PAD_I] & SYS_PAD_I_XLVCC_I_BIT)
#define	M_MISC_SET_RRAM_BANK(x)						(R8_SYS0_MISCL[R8_SYS0_SYS_RRAM_CFG] = ((R8_SYS0_MISCL[R8_SYS0_SYS_RRAM_CFG] & ~(SYS_CR_RRAM_IDX)) | (x)))
#define	M_MISC_GET_AXIMON_ACCESS_ID()				(R32_SYS1_MISCH[R32_SYS1_SYS_AXIMON_ACCESS_ID])
#define	M_MISC_GET_DBUF2_ACCESS_ADDRESS()			(R32_SYS1_MISCH[R32_SYS1_SYS_AXIMON_DBUF2_ACCESS_ADR])
#define	M_MISC_GET_DBUF3_ACCESS_ADDRESS()			(R32_SYS1_MISCH[R32_SYS1_SYS_AXIMON_DBUF3_ACCESS_ADR])
#define	M_MISC_SET_AXIMON_DBUF2_START_ADDRESS(x)	(R32_SYS1_MISCH[R32_SYS1_SYS_AXIMON_DBUF2_START_ADDR] = (x))
#define	M_MISC_SET_AXIMON_DBUF2_START_LENGTH(x)		(R16_SYS1_MISCH[R16_SYS1_SYS_AXIMON_DBUF2_START_LEN] = (x>>5))
#define	M_MISC_DISABLE_AXIMON_DBUF2()				(R16_SYS1_MISCH[R16_SYS1_SYS_AXIMON_DBUF2_START_LEN] = (0))
#define	M_MISC_SET_AXIMON_DBUF3_START_ADDRESS(x)	(R32_SYS1_MISCH[R32_SYS1_SYS_AXIMON_DBUF3_START_ADDR] = (x))
#define	M_MISC_SET_AXIMON_DBUF3_START_LENGTH(x)		(R16_SYS1_MISCH[R16_SYS1_SYS_AXIMON_DBUF3_START_LEN] = (x>>5))
#define	M_MISC_DISABLE_AXIMON_DBUF3()				(R16_SYS1_MISCH[R16_SYS1_SYS_AXIMON_DBUF3_START_LEN] = (0))
#endif /* PS5017_EN */

#if PS5017_EN
#define	M_MISC_GET_DMAC_ERROR_INFO()				M_FW_TODO()
#elif PS5021_EN /* PS5017_EN */
#define	M_MISC_GET_DMAC_ERROR_INFO()				(R32_SYS1_IP[R32_SYS1_IP_DMAC_ERR_STS])
#else /* PS5017_EN */
#define	M_MISC_GET_DMAC_ERROR_INFO()				(R32_SYS1_MISCH[R32_SYS1_SYS_DMA_ERR_STS])
#endif /* PS5017_EN */

#if PS5021_EN
#define	M_MISC_SET_DMAC_ERROR_SELECT(x)				(R32_SYS1_IP[R32_SYS1_IP_DMAC_ERR_SEL] = x)
#else /* PS5021_EN */
#define	M_MISC_SET_DMAC_ERROR_SELECT(x)				(R8_SYS1_MISCH[R8_SYS1_SYS_DMA_ERR_SEL] = x)
#endif /* PS5021_EN */

#if PS5017_EN
#define M_MISC_SET_DBUF_PARITY_ERROR()				M_FW_TODO()
#define	M_MISC_DMAC_ERROR_STALL_EN()				M_FW_TODO()
#define	M_MISC_DMAC_ERROR_STALL_DISABLE()			M_FW_TODO()
#else /* PS5017_EN */
#define M_MISC_SET_DBUF_PARITY_ERROR()				do{ \
													R32_SYS1_MISCH[R32_SYS1_SYS_PD1_MISCH_CTRL] &= (~CR_AXIM_RSP_ERR_EN_BIT); \
													R32_SYS1_MISCH[R32_SYS1_SYS_DBUF_CFG] &= (~CR_DBUF_RRC_PASS_INJ_BIT); \
													R32_SYS1_MISCH[R32_SYS1_SYS_DBUF_CFG] |= (CR_DBUF_PERR_INJ_BIT | CR_DBUF_PERR_CHK_EN_BIT); \
													} while(0)

#define	M_MISC_DMAC_ERROR_STALL_EN()				(R32_SYS1_MISCH[R32_SYS1_SYS_DMA_REG] |= CR_DMAC_ERR_STALL_EN_BIT)
#define	M_MISC_DMAC_ERROR_STALL_DISABLE()			(R32_SYS1_MISCH[R32_SYS1_SYS_DMA_REG] &= ~CR_DMAC_ERR_STALL_EN_BIT)
#endif /* PS5017_EN */

#if (DEBUG_GPIO_ENABLE || RDT_MODE_EN || OPEN_BLOCK_RSMAP_PERFORMANCE_ANALYSIS_EN)
#if PS5021_EN
#define M_SET_GPIO_O(IDX, DATA) 					do{\
													if (DATA) {(R16_SYS0_GPIO_CTRL[R16_SYS0_GPIO_SETTING_VALUE] |= (U16)((BIT(IDX))));} \
													else {(R16_SYS0_GPIO_CTRL[R16_SYS0_GPIO_SETTING_VALUE] &= ~(U16)((BIT(IDX))));} \
													} while(0)
#else /* PS5021_EN */
#define M_SET_GPIO_O(IDX, DATA)						(R8_SYS0_MISCH[(R32_SYS0_SYS_GPIO_O_3_0 << 2) + (IDX)] = (DATA))
#endif /* PS5021_EN */
#else /* (DEBUG_GPIO_ENABLE || RDT_MODE_EN || OPEN_BLOCK_RSMAP_PERFORMANCE_ANALYSIS_EN) */
#define M_SET_GPIO_O(IDX, DATA)
#endif /* (DEBUG_GPIO_ENABLE || RDT_MODE_EN || OPEN_BLOCK_RSMAP_PERFORMANCE_ANALYSIS_EN) */

#if PS5021_EN
#define M_SET_GPIO_9(DATA)							do{ \
													if (DATA) {(R16_SYS0_GPIO_CTRL[R16_SYS0_GPIO_SETTING_VALUE] |= (U16)((BIT(9))));} \
													else {(R16_SYS0_GPIO_CTRL[R16_SYS0_GPIO_SETTING_VALUE] &= ~(U16)((BIT(9))));} \
													} while(0)
#define	M_SET_GPIO_0(DATA)							do{ \
													if (DATA) {(R16_SYS0_GPIO_CTRL[R16_SYS0_GPIO_SETTING_VALUE] |= (U16)((BIT(0))));} \
													else {(R16_SYS0_GPIO_CTRL[R16_SYS0_GPIO_SETTING_VALUE] &= ~(U16)((BIT(0))));} \
													} while(0)
#define	M_SET_GPIO_2(DATA)							do{ \
													if (DATA) {(R16_SYS0_GPIO_CTRL[R16_SYS0_GPIO_SETTING_VALUE] |= (U16)((BIT(2))));} \
													else {(R16_SYS0_GPIO_CTRL[R16_SYS0_GPIO_SETTING_VALUE] &= ~(U16)((BIT(2))));} \
													} while(0)
#else /* PS5021_EN */
#define M_SET_GPIO_9(DATA)							(R8_SYS0_MISCH[(R32_SYS0_SYS_GPIO_O_3_0 << 2) + (9)] = (DATA))
#define	M_SET_GPIO_0(DATA)							(R8_SYS0_MISCH[(R32_SYS0_SYS_GPIO_O_3_0 << 2)] = (DATA))
#if (!PS5017_EN)
#define	M_SET_GPIO_2(DATA)							(R8_SYS0_MISCH[(R32_SYS0_SYS_GPIO_O_3_0 << 2) + (2)] = (DATA))
#endif /* (!PS5017_EN) */
#endif /* PS5021_EN */

#if PS5017_EN
#define M_GET_GPIO_O(IDX)							(R8_SYS0_MISCH[(R32_SYS0_SYS_GPIO_O_3_0 << 2) + (IDX)])
#define M_GET_GPIO_INPUT_0TO7()						M_FW_TODO()

#define M_SET_TCK_O()								M_FW_TODO()
#define M_SET_TMS_O()								M_FW_TODO()

#define M_CLR_TCK_O()								M_FW_TODO()
#define M_CLR_TMS_O()								M_FW_TODO()
#else /* PS5017_EN */
#define M_GET_GPIO_O(IDX)							(R8_SYS0_MISCH[(R32_SYS0_SYS_GPIO_O_3_0 << 2) + (IDX)])
#define M_GET_GPIO_INPUT_0TO7()						(R8_SYS0_MISCH[R8_SYS0_SYS_GPIO_I_ALL])

#define M_SET_TCK_O()								(R32_SYS0_MISCH[R32_SYS0_SYS_GPIO_O_12] |= CR_TCK_O_BIT)
#define M_SET_TMS_O()								(R32_SYS0_MISCH[R32_SYS0_SYS_GPIO_O_12] |= CR_TMS_O_BIT)

#define M_CLR_TCK_O()								(R32_SYS0_MISCH[R32_SYS0_SYS_GPIO_O_12] &= (~CR_TCK_O_BIT))
#define M_CLR_TMS_O()								(R32_SYS0_MISCH[R32_SYS0_SYS_GPIO_O_12] &= (~CR_TMS_O_BIT))
#endif /* PS5017_EN */

#define M_SET_GPIO_O12()							(R32_SYS0_MISCH[R32_SYS0_SYS_GPIO_O_12] |= CR_GPIO_O12_BIT)
#define M_CLR_GPIO_O12()							(R32_SYS0_MISCH[R32_SYS0_SYS_GPIO_O_12] &= (~CR_GPIO_O12_BIT))

#if PS5021_EN
#define M_ENABLE_ATCM_WDET()						do{ \
													R32_SYS1_SRAM_CTRL[R32_SYS1_ATCM_CTRL] |= ATCM_WDET_EN_BIT; \
													__asm("DSB"); \
													} while(0)
#define M_DISABLE_ATCM_WDET()						do{ \
													R32_SYS1_SRAM_CTRL[R32_SYS1_ATCM_CTRL] &= ~ATCM_WDET_EN_BIT; \
													__asm("DSB"); \
													} while(0)
#define M_SET_DBUF_RRC_SET_EN(x)					(R32_SYS1_MISC_CTRL[R32_SYS1_MISC_SYS_PD1_MISC] = (R32_SYS1_MISC_CTRL[R32_SYS1_MISC_SYS_PD1_MISC] & (~CR_DBUF_RRC_SET)) | (((x) << 6) & CR_DBUF_RRC_SET))
#define M_ENABLE_CHK_LBNA()							(R32_SYS1_MISC_CTRL[R32_SYS1_MISC_SYS_PD1_MISC] |= CR_SYS_CHK_LBNA_EN_BIT)
#define M_ENABLE_AXI_CHK_RANGE()					(R32_SYS1_MISC_CTRL[R32_SYS1_MISC_SYS_PD1_MISC] |= CR_SYS_CHK_RANGE_EN_BIT)
#define M_ENABLE_DMAC_ERR_STALL()					(R32_SYS1_IP[R32_SYS1_IP_DMAC_REG] |= CR_DMAC_ERR_STALL_EN_BIT)
#define M_ENABLE_DMAC_RRC_LS()						(R32_SYS1_IP[R32_SYS1_IP_DMAC_REG] |= CR_DMAC_RRC_LS_EN_BIT)
#define M_ENABLE_DBUF_PERR_CHK()					(R32_SYS1_IP[R32_SYS1_IP_DBUF1_CTRL] |= CR_DBUF_PERR_CHK_EN_BIT)
#define	M_ENABLE_AXI_INT_EVT(x) 					(R32_SYS1_IP[R32_SYS1_IP_AXI_INT_MASK] |= (x))
#define M_CLR_AXI_WPRTC_SRC(x)						(R32_SYS1_IP[R32_SYS1_AXI_WPRTC_SRC] = (x))
#define M_ENALBE_AXI_WPRTC_CFG(x)					(R32_SYS1_IP[R32_SYS1_AXI_WPRTC_CFG] |= (x))
#define M_DISALBE_AXI_WPRTC_CFG(x)					(R32_SYS1_IP[R32_SYS1_AXI_WPRTC_CFG] &= ~(x))
#endif /* PS5021_EN */

#define M_GPIO_0				(0)
#define M_GPIO_1				(1)
#define M_GPIO_2				(2)
#define M_GPIO_3				(3)
#define M_GPIO_4				(4)
#define M_GPIO_5				(5)
#define M_GPIO_6				(6)
#define M_GPIO_7				(7)
#define M_GPIO_8				(8)
#define M_GPIO_9				(9)
#define M_GPIO_10				(10)
#define M_GPIO_11				(11)
#define M_GPIO_12				(12)
#define M_GPIO_13				(13)
#define M_GPIO_14				(14)
#define M_GPIO_15				(15)
#define M_NUM_GPIO				((PS5021_EN) ? (16) : (13))

#endif /* _MISC_API_H_ */
