#include "burner/Burner.h"
#include "rdt/rdt_api.h"
#include "rdt/rdt_pio_force_wr_log.h"

#if RDT_MODE_EN
#define OPTC_RAM                        (1)
#define OPTC_RAM_COMPARE_CNT            (8)
#define OPTC_RAM_NEXT_CMD_OFFSET        (64)

#define PARAMETER_RAM                   (2)
#define PARAMETER_RAM_COMPARE_CNT       (22)
#define PARAMETER_RAM_NEXT_CMD_OFFSET   (352)

#define TEST_Q_NUM                      (3) // NormalQ, MergeQ 0, MergeQ 1

U32 failAddress = 0;
U32 currentData = 0;
U32 exceptAddress = 0;


U8 gubRDTCSCHQTest = 0;
//{0xF0900A69, 0x0, 0x15800080, 0x0, 0xF0000024, 0x0, 0x0, 0x0};
U32 optc_golden_data[OPTC_RAM_COMPARE_CNT] = {0xF0900A69, 0x0, 0x15000080, 0x0, 0xF0000024, 0x0, 0x0, 0x0};
//{0x00110120, 0x00110128, 0x0, 0x0, 0x00110130, 0x00110138, 0x0,
//		0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x000F0000, 0x0, 0x0, 0x0, 0x0, 0x000000FF, 0x000000FF, 0x000000FF, 0x000000FF
//	};
U32 vpram_golden_data[PARAMETER_RAM_COMPARE_CNT] = {0x00110640, 0x00110648, 0x00000000, 0x0, 0x00110650, 0x00110658, 0x0000000,
		0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x000F0000, 0x0, 0x0, 0x0, 0x0, 0x000000FF, 0x000000FF, 0x000000FF, 0x000000FF
	};


BOOL rdt_cop0_csch_test_prog(void *cb_func, U32 cb_id, void *cb_obj, U8 program_mode, U8 cop0_opt, U8 slc_mode, U32 page_prog_buf_addr, U32 LCA, U8 sysblk_mode)
{

	U16 uwTagId;
	//RDT_API_STRUCT_PTR rdt = &gRdtApiStruct;
	U32 ulBufAddrMatrix[4], ulLCA[4];
	U8 ubi;
	COP0Status_t eCOP0Status;

	PCA_t ulFWPCA = {};
	U32 ulLocalPCA;
	U8 ubLocalLPCRC = 0;
	U8 ubCnt = 0;

	/* PCA */
	ulLocalPCA = cb_id;

	/* Prepare write buffer, ZInfo and LCA */
	BUF_TYPE_t ulBufInfo[FRAMES_PER_PAGE] = {{0}}; //{0}
	FWSetPara_t ulFWSet[FRAMES_PER_PAGE] = {{0}};

	for (ubi = 0; ubi < gub4kEntrysPerPlane; ubi++) {
		ulLCA[ubi] = (LCA | ubi);
		//ulLCA[ubi] = 0x0; //LCA might need to modify
		ulBufAddrMatrix[ubi] = page_prog_buf_addr + (4096 * ubi);
		if (TRUE/*COP0_NEW_API_EN*/) {
			ulBufInfo[ubi].A.ulBUF_ADR = ulBufAddrMatrix[ubi];
			//ubZInfo[ubi].ulFWSet = (MAX_ZCODE & BIT_MASK(4)); //ZIP
			ulFWSet[ubi].ubZInfo = MAX_ZINFO;
			M_UART(RDT_DBG_, "\nulBufInfo[%b].A.ulBUF_ADR=%l", ubi, ulBufInfo[ubi].A.ulBUF_ADR);
		}
	}

	//M_FWPCA_SET(ulFWPCA.ulFormatForData, ulLocalPCA, MAX_ZCODE, 0, ubLocalLPCRC);
	M_FWPCA_SET(ulFWPCA.ulAll, ulLocalPCA, gPCAInfo.ubMaxZByte, 0, ubLocalLPCRC);

	if (TRUE/*COP0_NEW_API_EN*/) {
		/* Program tie-in */
		COP0WriteSQPara_t WriteSQPara = {};

		/* Assign callback */
		cmd_table_t uoCallbackInfo = {(U32)cb_func, {0}};

		//COP0API_FillCOP0WriteSQUserData0Para(COP0_W_SYSTEM_PROGRAM, &WriteSQPara);
		COP0API_FillCOP0WriteSQUserData0Para(program_mode, &WriteSQPara);

		WriteSQPara.UserData0.ubDataDef &= (~COP0_SEED_EN_BIT);

		if (gFlhEnv.ulFlashDefaultType.BitMap.CellType != FLH_SLC) {
			WriteSQPara.UserData0.btSLCMode = slc_mode; //TRUE
		}
		//WriteSQPara.ulPCA.ulAll = ulPCA.ulAll;
		WriteSQPara.ulPCA.ulAll = ulFWPCA.ulAll;
		WriteSQPara.BufVld.ubBufType = BUF_TYPE_A;
		WriteSQPara.BufVld.pulBufInfoPtr = ulBufInfo;
		WriteSQPara.pulLCAPtr = ulLCA;
		//WriteSQPara.pulFWSetPtr = ubZInfo;
		WriteSQPara.pulFWSetPtr = ulFWSet;

		/* Callback info */
		uoCallbackInfo.ulData.btKeepTag = FALSE;

		WriteSQPara.UserData0.ubMultiPlaneMode = cop0_opt;

		/* Send program tie-in */
		do {
			eCOP0Status = COP0API_SendWriteSQ(&WriteSQPara, &uoCallbackInfo);
			ubCnt++;
		} while (!eCOP0Status.btSendCmdSuccess && (ubCnt < SYSAREA_OPERATION_TIMEOUT)); //Avoid mail router is full
		M_FW_ASSERT(ASSERT_RDT_0x0845, eCOP0Status.btSendCmdSuccess == TRUE); //Need to log?

		uwTagId = WriteSQPara.UserData0.uwTagID;
		//pca_to_addr_printf(ulPCA.FormatFor512GB.ulPCA, slc_mode);
	}
	else {
		//FTLProgram16KDirectProgram2(ulBufAddrMatrix, 2, ulPCA, ulLCA,  uwTagId, gub4kEntrysPerPlane);
	}

	return 0;
}

BOOL rdt_cop0_csch_data_compare(U8 ubRAMType, U8 ubTestQIndex, U32 ulLocalPCA, U32 tag)
{
	U16 idx, data_cnt;
	U32 data_buf, *golden_data;

	if (ubRAMType == OPTC_RAM) {
		optc_golden_data[1] = optc_golden_data[3] = optc_golden_data[5] = ulLocalPCA;
		golden_data = optc_golden_data;
		data_buf = OPTC_RAM_BASE + OPTC_RAM_NEXT_CMD_OFFSET * ubTestQIndex;
		data_cnt = OPTC_RAM_COMPARE_CNT;
	}
	else { //PARAMETER_RAM
		vpram_golden_data[2] = ulLocalPCA;
		vpram_golden_data[6] = ulLocalPCA + 1;
		vpram_golden_data[10] = ulLocalPCA + 2;
		vpram_golden_data[14] = ulLocalPCA + 3;
		vpram_golden_data[12] = (tag << 16) |  tag ;
		golden_data = vpram_golden_data;
		data_buf = VP_RAM_BASE + PARAMETER_RAM_NEXT_CMD_OFFSET * ubTestQIndex;
		data_cnt = PARAMETER_RAM_COMPARE_CNT;
	}

	for (idx = 0; idx < data_cnt; idx++) {
		if (*(U32 *)(data_buf + idx * sizeof(U32)) != golden_data[idx]) {
			failAddress = data_buf + idx * sizeof(U32);
			currentData = *(U32 *)(data_buf + idx * sizeof(U32));
			exceptAddress = golden_data[idx];
			M_UART(RDT_TEST_, "\nTestQ(%b) -> %s[%b]=%l != golden=%l", gubRDTCSCHQTest, (ubRAMType == OPTC_RAM) ? "OPTC_RAM" : "PARAMETER_RAM", idx, *(U32 *)(data_buf + idx * sizeof(U32)), golden_data[idx]);
			return FAIL;
		}
	}
	return PASS;
}

#if (RDT_DO_IC_TEST)
BOOL rdt_api_cop0_csch_test(RDT_API_STRUCT_PTR rdt)
{
	////////////////////////////
	//Reset OPTC RAM index
	//1.ANDES stanby
	//2.Reset COP0
	//3.Init COP0
	//4.disable ANDES stanby
	R32_COP0[R32_COP0_ANDES_STANDBY] |= (COP0_STANDBY_REQ_BIT);
	while ((R32_COP0[R32_COP0_ANDES_STANDBY] & COP0_ANDES_STANDBY_BIT) == 0);
	LPMResetControl(CR_RST_N_COP0_BIT);
	COP0_Init();
	R32_COP0[R32_COP0_ANDES_STANDBY] &= (~COP0_STANDBY_REQ_BIT);
	////////////////////////////

	U8 ubChannel = 0;
	U8 ubBank = 0;
	U8 ubTestQIndex;
	U8 ubNonExecuteNum = 0;
	MTCfg_t uoMTCfg_1, uoMTCfg_2;

	U8 ubTestQ[TEST_Q_NUM] = {DB_COP0_RD_SQ, DB_COP0_SQ1, DB_COP0_WR_SQ};
	U8 pca_rule = PCA_RULE(TRUE);
	U32 ulLocalPCA = PCA_VALUE(pca_rule, 0, 0, 0, 0, 0, 2, 0, 0);
	U32 data_buf = 0x220C8000;

	U32 tag = 0;

	BOOL ubResult = PASS;

	M_UART(RDT_TEST_, "\n[IC pattern] Cop0 csch test start\n");
	rdt->current_state = RDT_STATE_COP0_CSCH_TEST;
	U32 start_time = rdt_api_rtt_get_timer_count();


	M_FIP_SET_FORCE_EMPTY_ALL_CH();

	for (ubTestQIndex = 0; ubTestQIndex < TEST_Q_NUM; ubTestQIndex++) {

		gubRDTCSCHQTest = ubTestQ[ubTestQIndex];

		COP0_STALL_SINGLE_MTP_SET(ubChannel, ubBank, 0, TRUE);

		tag = FTLGetTagID(COP0_TAG_POOL_ID);

		rdt_cop0_csch_test_prog((void *)NULL, (U32)ulLocalPCA, (void *)NULL, RDT_COP0_W_PROGRAM, MULTIPLANE_END, TRUE, (U32)data_buf, ulLocalPCA, 0);

		// Release MT
		COP0_STALL_SINGLE_MTP_CLR(ubChannel, ubBank, 0);
		while (!M_COP0_CHECK_COP0_IDLE() || FIPGetNonExecuteMTNum(ubChannel, ubBank) != 0) {
			COP0_STALL_SINGLE_MTP_SET(ubChannel, ubBank, 0, TRUE);
			ubNonExecuteNum = FIPGetNonExecuteMTNum(ubChannel, ubBank);
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, ubNonExecuteNum <= MAX_INT_FIFO_MAP_CNT);

			if (ubNonExecuteNum) {
				// Select non execute MTQ
				M_FIP_SET_NONEXCUTE_MTQ_SELECT(ubChannel, ubBank);
				U8 ubMTQStartPtr  = M_FIP_GET_NONEXCUTE_MTQ_PTR();

				// 1st untrigger MT
				uoMTCfg_1.uoAll = M_FIP_GET_NONEXCUTE_MTQ_MT_CONFIG(ubMTQStartPtr);
				COP0_IRAM_Release(uoMTCfg_1.bits_recc.ubMT_IDX, COP0_CQ_MSG_ALL_DONE_BIT, ERR_HANDLE_NONE_ERROR_FRAME, FALSE, ERR_HANDLE_NONE_ERROR_FRAME);

				if (ubNonExecuteNum > 1) {
					ubMTQStartPtr ^= 1;
					// 2nd untrigger MT
					uoMTCfg_2.uoAll = M_FIP_GET_NONEXCUTE_MTQ_MT_CONFIG(ubMTQStartPtr);
					COP0_IRAM_Release(uoMTCfg_2.bits_recc.ubMT_IDX, COP0_CQ_MSG_ALL_DONE_BIT, ERR_HANDLE_NONE_ERROR_FRAME, FALSE, ERR_HANDLE_NONE_ERROR_FRAME);
				}

				// Clear MTQ
				FlaClearMTQ(ubChannel, ubBank, 0);
			}
			COP0_STALL_SINGLE_MTP_CLR(ubChannel, ubBank, 0);
		}

		COP0DelegateCmd(); //FTLDelegateCOP0Cmd();
		// Release MT End


		//Compare OPTC RAM Data
		if (rdt_cop0_csch_data_compare(OPTC_RAM, ubTestQIndex, ulLocalPCA, tag)) {
			M_UART(RDT_DBG_, ("\nTestQ(%b) OPTC FAIL"), gubRDTCSCHQTest);
			ubResult = FAIL;
		}
		//Compare PARAMETER RAM Data
		if (rdt_cop0_csch_data_compare(PARAMETER_RAM, ubTestQIndex, ulLocalPCA, tag)) {
			M_UART(RDT_DBG_, ("\nTestQ(%b) Parameter RAM FAIL"), gubRDTCSCHQTest);
			ubResult = FAIL;
		}
	}

	if (ubResult) {
		rdt_api_add_sram_err_into_erl(rdt, ERL_TYPE_SRAM_SHARE, ERL_FAIL_CSCH, failAddress, currentData, exceptAddress, 0, 0);
		rdt_api_program_erl_log(rdt, TRUE);
	}


	M_UART(RDT_TEST_, "-- test time %d ms \n", rdt_api_rtt_get_timer_count() - start_time);
	M_UART(RDT_TEST_, "Cop0 csch test %s\n", ubResult ? "fail" : "pass");

	gubRDTCSCHQTest = 0;
	M_FIP_CLR_FORCE_EMPTY_ALL_CH();

	return ubResult;
}
#endif /*(RDT_DO_IC_TEST)*/
#endif /* RDT_MODE_EN */
