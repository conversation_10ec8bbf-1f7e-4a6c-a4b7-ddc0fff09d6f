/****************************************************************************
*
*  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
*  All rights reserved
*
*  The content of this document is confidential and shall be applied
*  subject to the terms and conditions of the license agreement and
*  other applicable laws. Any unauthorized access, use or disclosure
*  of this document is strictly prohibited and may be punishable
*  under laws.
*
*  raideccmap_for_seq_table_api.h
*
*
*
****************************************************************************/

#ifndef RAIDECCMAP_FOR_SEQ_TABLE_H_
#define RAIDECCMAP_FOR_SEQ_TABLE_H_

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "raideccmap_api.h"

/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *  enum
 * ---------------------------------------------------------------------------------------------------
 */


/*
 * ---------------------------------------------------------------------------------------------------
 *  data types
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *   macros
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *   extern global variables
 * ---------------------------------------------------------------------------------------------------
 */


/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */
U16 RaidECCSkipOffsetCalculate(U8 ubIsSLCMode, PCA_t ulStartFWPCA, U16 *puwOffsetNum, DMACSeqPTESkipTableEntry_t *puwSkipTable);
#if ((USB == HOST_MODE) || (TCG_EN && ((!PERFORMANCE_TEST_EN) || (SATA == HOST_MODE))) || ((FALSE == TCG_EN) && (NVME == HOST_MODE)))
AOM_SEQ_TABLE U16 RaidECCCalculateAlignSuperPageIdx(U8 ubIsSLCMode, U32 ulPCA);
AOM_SEQ_TABLE U16 RaidECCTransformIntoPageOffset(U32 ulPlaneIdx, U8 ubIsSLCMode);
#endif  /* ((USB == HOST_MODE) || (TCG_EN && ((!PERFORMANCE_TEST_EN) || (SATA == HOST_MODE))) || ((FALSE == TCG_EN) && (NVME == HOST_MODE))) */


#endif // RAIDECCMAP_FOR_SEQ_TABLE_H_
