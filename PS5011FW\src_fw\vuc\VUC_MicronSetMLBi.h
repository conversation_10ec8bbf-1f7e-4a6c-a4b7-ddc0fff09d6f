#ifndef _VUC_MICRONSETMLBI_H_
#define _VUC_MICRONSETMLBI_H_
#include "aom/aom_api.h"

#define VUC_MICRON_SET_MLBI_HEADER_LENGTH (12)
#define VUC_MICRON_SET_MLBI_PAYLOAD_LENGTH (0)

typedef struct {
	U16 uwCH;
	U16 uwCE;
	U16 uwLUN;
	U16 uwTrimRegAddr;
	U16 uwTrimRegData;
} SetMLBiInputData_t;

typedef struct {
	U8 ubResponseHeaderFormatVersion;
	U8 ubResponseDataFotmatVersion;
	U16 uwCMDClass;
	U16 uwCMDCode;
	U16 uwStatus;
	U32 ulDataPayloadSize;
} SetMLBiResponseHeader_t;
#if (HOST_MODE == NVME)
AOM_VUC_3 void VUCMicronSetMLBi(U32 ulInputPayloadAddr, U32 ulPayloadAddr);
#else /* (HOST_MODE == NVME) */
AOM_VUC_3 void VUCMicronSetMLBi(U32 ulInputPayloadAddr, U32 ulPayloadAddr);
#endif /* (HOST_MODE == NVME) */
#endif /* _VUC_MICRONSETMLBI_H_ */
