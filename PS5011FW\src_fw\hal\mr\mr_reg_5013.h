#ifndef _E13_MR_REG_H_
#define _E13_MR_REG_H_
#include "setup.h"
#include "mem.h"

#define MR_REG_BASE								(MR_REG_ADDRESS)

#define R8_MR										((volatile U8 *) MR_REG_BASE)
#define R16_MR										((volatile U16 *) MR_REG_BASE)
#define R32_MR										((volatile U32 *) MR_REG_BASE)
#define R64_MR										((volatile U64 *) MR_REG_BASE)

#define MR_SQ_N_INFO_SIZE	8
#define R32_MR_SQ			((volatile U32 (*)[MR_SQ_N_INFO_SIZE >> 2])MR_REG_BASE)

//000h + n*008h, n=0~63
#define R32_MR_SQ_N_INFO_L									(0x0000 >> 2)
#define R8_MR_SQ_N_INFO_L									(0x00)
#define 	SQR_ADR_SHIFT	(0)
#define 	SQR_ADR_MASK	(BIT_MASK(8))
#define 	SQR_PTR_SHIFT	(8)
#define 	SQR_PTR_MASK	(BIT_MASK(3))
#define 	SQR_STS_SHIFT	(19)
#define 	SQR_STS_MASK	(BIT_MASK(2))
#define 	SQR_CNT_SHIFT	(21)
#define 	SQR_CNT_MASK	(BIT_MASK(11))

//004h + n*008h, n=0~63
#define R32_MR_SQ_N_INFO_H									(0x0004 >> 2)
#define R8_MR_SQ_N_INFO_H									(0x04)
#define 	SQW_ADR_SHIFT	(0)
#define 	SQW_ADR_MASK	(BIT_MASK(8))
#define 	SQW_PTR_SHIFT	(8)
#define 	SQW_PTR_MASK	(BIT_MASK(3))
#define 	SQW_STS_SHIFT	(19)
#define 	SQW_STS_MASK	(BIT_MASK(2))
#define		SQW_STA_IDLE    (0)
#define		SQW_STA_LINK    (1)
#define		SQW_STA_RING    (2)
#define		SQW_STA_SRAM    (3)
#define 	SQR_LNK_CNT_SHIFT	(21)
#define 	SQR_LNK_CNT_MASK	(BIT_MASK(8))
#define 	SQR_EMT_BIT		BIT29
#define 	SQR_EMT_SHIFT	(29)
#define 	SQR_EMT_MASK	(BIT_MASK(1))

//200h + n*001h, n=0~63
#define R8_MR_MAX_SQ_N_LNK_CNT								(0x0200)
//240h + n*001h, n=0~63
#define R8_MR_SQ_N_LNK_LMT									(0x0240)
//280h + n*4bit, n=0~31
#define R8_MR_DEC_N_INFO									(0x0280)
#define	DEC_N_STS_0_SHIFT								(0)
#define	DEC_N_STS_0_MASK								(BIT_MASK(2))
#define	DEC_N_IDLE_0_SHIFT								(2)
#define	DEC_N_IDLE_0_MASK								(BIT_MASK(1))
#define	DEC_N_STS_4_SHIFT								(4)
#define	DEC_N_STS_4_MASK								(BIT_MASK(2))
#define	DEC_N_IDLE_4_SHIFT								(6)
#define	DEC_N_IDLE_4_MASK								(BIT_MASK(1))

#define R32_MR_LNK_INFO									(0x0290 >> 2)
#define	MAX_LNK_CNT_SHIFT								(0)
#define	MAX_LNK_CNT_MASK								(BIT_MASK(8))
#define	MAX_LNK_STS_SHIFT								(8)
#define	MAX_LNK_STS_MASK								(BIT_MASK(2))
#define	MAX_LNK_FUL_SHIFT								(10)
#define	MAX_LNK_FUL_MASK								(BIT_MASK(1))

//298h + n*1bit, n=0~255
#define R8_MR_LNK_VFG_N									(0x0298)
#define R32_MR_LNK_VFG_N								(0x0298 >> 2)
// 2B8h + n*4byte, n=0~63
#define R32_MR_LNK_N_DATA									(0x02B8 >> 2)

#define R32_MR_SRAM_CFG									(0x03B8 >> 2)
#define 	RRC_SET_SHIFT									(0)
#define 	RRC_SET_MASK									(BIT_MASK(2))
#define 	RRC_LS_EN_SHIFT									(2)
#define 	RRC_LS_EN_MASK									(BIT_MASK(1))
#define 	RRC_ERR_INJ_SHIFT								(3)
#define 	RRC_ERR_INJ_MASK								(BIT_MASK(1))
#define 	RRC_STOP_SHIFT									(4)
#define 	RRC_STOP_MASK									(BIT_MASK(1))
#define 	RRC_INFO_CLR_SHIFT								(5)
#define 	RRC_INFO_CLR_MASK								(BIT_MASK(1))
#define 	RRC_RRF_CNT_SHIFT								(8)
#define 	RRC_RRF_CNT_MASK								(BIT_MASK(8))
#define 	RRC_RRS_CNT_SHIFT								(16)
#define 	RRC_RRS_CNT_MASK								(BIT_MASK(8))
#define 	RRC_BACK_INFO_SHIFT								(24)
#define 	RRC_BACK_INFO_MASK								(BIT_MASK(6))
// 3C0h + n*4bit, n=0~15
#define R8_MR_MRIW_N_INFO									(0x03C0)
#define	MRIW_N_STS_0_SHIFT								(0)
#define	MRIW_N_STS_0_MASK								(BIT_MASK(3))
#define	MRIW_N_IDLE_0_SHIFT								(3)
#define	MRIW_N_IDLE_0_MASK								(BIT_MASK(1))
#define	MRIW_N_STS_4_SHIFT								(4)
#define	MRIW_N_STS_4_MASK								(BIT_MASK(3))
#define	MRIW_N_IDLE_4_SHIFT								(7)
#define	MRIW_N_IDLE_4_MASK								(BIT_MASK(1))

// 3C8h + n*4bit, n=0~31
#define R8_MR_MRIR_N_INFO									(0x03C8)
#define	MRIR_N_STS_0_SHIFT								(0)
#define	MRIR_N_STS_0_MASK								(BIT_MASK(3))
#define	MRIR_N_IDLE_0_SHIFT								(3)
#define	MRIR_N_IDLE_0_MASK								(BIT_MASK(1))
#define	MRIR_N_STS_4_SHIFT								(4)
#define	MRIR_N_STS_4_MASK								(BIT_MASK(3))
#define	MRIR_N_IDLE_4_SHIFT								(7)
#define	MRIR_N_IDLE_4_MASK								(BIT_MASK(1))





#define R32_MR_MON_HEAD_ADR								(0x03E0 >> 2)

#define R32_MR_MON_TAIL_ADR									(0x03E4 >> 2)

#define R32_MR_MON_CHK_EN_L								(0x03E8 >> 2)

#define R32_MR_MON_CHK_EN_H								(0x03EC >> 2)
#define 	MON_CHK_EN_H_SHIFT								(0)
#define 	MON_CHK_EN_H_MASK								(BIT_MASK(31))
#define 	MON_CHK_MODE_SHIFT								(31)
#define 	MON_CHK_MODE_MASK								(BIT_MASK(1))

#define R32_MR_SQ_FLUSH_EN_L								(0x03F0 >> 2)
#define MR_SQ_FLUSH_EN_L_ALL                                (0xFFFFFFFF)

#define R32_MR_SQ_FLUSH_EN_H								(0x03F4 >> 2)
#define MR_SQ_FLUSH_EN_H_ALL                                (0xFFFFFFFF)

#define R32_MR_HOST_LOG_HEAD_ADR							(0x03F8 >> 2)

#define R32_MR_HOST_LOG_TAIL_ADR								(0x03FC >> 2)

#define R32_MR_OTHERS_CFG_L									(0x0400 >> 2)
#define R8_MR_OTHERS_CFG_L									(0x0400)
#define 	DB_SQ10_TID_SHIFT								(0)
#define 	DB_SQ10_TID_MASK								(BIT_MASK(8))
#define 	DB_SQ11_TID_SHIFT								(8)
#define 	DB_SQ11_TID_MASK								(BIT_MASK(8))
#define 	DB_SQ12_TID_SHIFT								(16)
#define 	DB_SQ12_TID_MASK								(BIT_MASK(8))
#define 	DB_SQ13_TID_SHIFT								(24)
#define 	DB_SQ13_TID_MASK								(BIT_MASK(8))

#define R32_MR_OTHERS_CFG_H								(0x0404 >> 2)
#define 	AXIS_ACC_MSK_SHIFT								(0)
#define 	AXIS_ACC_MSK_MASK								(BIT_MASK(8))
#define 	AXIS_ACC_RNG_SHIFT								(8)
#define 	AXIS_ACC_RNG_MASK								(BIT_MASK(8))
#define 	SR0_STS_SHIFT									(16)
#define 	SR0_STS_MASK									(BIT_MASK(1))
#define 	SR0_IDLE_SHIFT									(17)
#define 	SR0_IDLE_MASK									(BIT_MASK(1))
#define 	SR1_STS_SHIFT									(18)
#define 	SR1_STS_MASK									(BIT_MASK(1))
#define 	SR1_IDLE_SHIFT									(19)
#define 	SR1_IDLE_MASK									(BIT_MASK(1))
#define 	AXIS_ACC_RNG_DAT_SHIFT							(24)
#define 	AXIS_ACC_RNG_DAT_MASK							(BIT_MASK(1))
#define 	AXIS_ACC_RNG_UPD_SHIFT							(25)
#define 	AXIS_ACC_RNG_UPD_MASK							(BIT_MASK(1))
#define 	AXIM_RD_ERR_INS_SHIFT								(26)
#define 	AXIM_RD_ERR_INS_MASK								(BIT_MASK(1))
#define 	AXIM_WR_ERR_INS_SHIFT							(27)
#define 	AXIM_WR_ERR_INS_MASK								(BIT_MASK(1))
#define 	SQ_RNG_EN_SHIFT									(28)
#define 	SQ_RNG_EN_MASK									(BIT_MASK(1))

// 408h + n*1bit, n=0~127
#define R8_MR_ITC_ERR_MASK_N								(0x0408)
#define R32_MR_ITC_ERR_MASK_N								(0x0408 >> 2)
#define   ITC_ERR_MASK_SRAM_PARITY_ERROR_BIT				(BIT0)

#define R32_MR_ITC_ERR_INFO_L								(0x0418 >> 2)
#define		FULL_INT_ERR									BIT(6)

#define R32_MR_ITC_ERR_INFO_H								(0x041C >> 2)
#define 	MRIW0_INT_ERR_TYPE_SHIFT							(0)
#define 	MRIW0_INT_ERR_TYPE_MASK							(BIT_MASK(2))
#define 	MRIW1_INT_ERR_TYPE_SHIFT							(2)
#define 	MRIW1_INT_ERR_TYPE_MASK							(BIT_MASK(2))
#define 	MRIW2_INT_ERR_TYPE_SHIFT							(4)
#define 	MRIW2_INT_ERR_TYPE_MASK							(BIT_MASK(2))
#define 	MRIW3_INT_ERR_TYPE_SHIFT							(6)
#define 	MRIW3_INT_ERR_TYPE_MASK							(BIT_MASK(2))
#define 	MRIW4_INT_ERR_TYPE_SHIFT							(8)
#define 	MRIW4_INT_ERR_TYPE_MASK							(BIT_MASK(2))
#define 	MRIW5_INT_ERR_TYPE_SHIFT							(10)
#define 	MRIW5_INT_ERR_TYPE_MASK							(BIT_MASK(2))
#define 	MRIW6_INT_ERR_TYPE_SHIFT							(12)
#define 	MRIW6_INT_ERR_TYPE_MASK							(BIT_MASK(2))
#define 	MRIW7_INT_ERR_TYPE_SHIFT							(14)
#define 	MRIW7_INT_ERR_TYPE_MASK							(BIT_MASK(2))
#define 	MRIW8_INT_ERR_TYPE_SHIFT							(16)
#define 	MRIW8_INT_ERR_TYPE_MASK							(BIT_MASK(2))
#define 	MRIW9_INT_ERR_TYPE_SHIFT							(18)
#define 	MRIW9_INT_ERR_TYPE_MASK							(BIT_MASK(2))
#define 	MRIW10_INT_ERR_TYPE_SHIFT							(20)
#define 	MRIW10_INT_ERR_TYPE_MASK							(BIT_MASK(2))
#define 	MRIW11_INT_ERR_TYPE_SHIFT							(22)
#define 	MRIW11_INT_ERR_TYPE_MASK							(BIT_MASK(2))
#define 	MRIW12_INT_ERR_TYPE_SHIFT							(24)
#define 	MRIW12_INT_ERR_TYPE_MASK							(BIT_MASK(2))
#define 	MRIW13_INT_ERR_TYPE_SHIFT							(26)
#define 	MRIW13_INT_ERR_TYPE_MASK							(BIT_MASK(2))
#define 	MRIW14_INT_ERR_TYPE_SHIFT							(28)
#define 	MRIW14_INT_ERR_TYPE_MASK							(BIT_MASK(2))
#define 	MRIR6_INT_ERR_TYPE_SHIFT							(30)
#define 	MRIR6_INT_ERR_TYPE_MASK							(BIT_MASK(1))

// 420h + n*1bit, n=0~127
#define R8_MR_ITC_ERR_FLAG_N								(0x0420)

#define R32_MR_ITC_FUL_MSK_L								(0x0430 >> 2)
#define R64_MR_ITC_FUL_MSK									(0x0430 >> 3)
#define R32_MR_ITC_FUL_MSK_H								(0x0434 >> 2)

#define R32_MR_ITC_FUL_INFO									(0x0438 >> 2)
#define 	ITC_FUL_POS_SHIFT								(0)
#define 	ITC_FUL_POS_MASK								(BIT_MASK(6))
#define 	ITC_FUL_CLR_SHIFT								(8)
#define 	ITC_FUL_CLR_MASK								(BIT_MASK(1))


#define R32_MR_ITC_FUL_FLAG_L								(0x0440 >> 2)
#define     ITC_FULL_FLAG_LOG_MESSAGE                        (BIT(14))

#define R32_MR_ITC_FUL_FLAG_H								(0x0444 >> 2)

#define R32_MR_DECODE_STOP								(0x0450 >> 2)
#define R8_MR_MRIW_DAT_SIZE            						(0x500)

#define R8_MR_MRIR_DAT_SIZE            						(0x508)

#define R32_MR_EMPTY_STATUS									(0x5A0 >> 2)
#define		ALL_EMPTY_BIT									(BIT4)


#endif /*_E13_MR_REG_H_*/
