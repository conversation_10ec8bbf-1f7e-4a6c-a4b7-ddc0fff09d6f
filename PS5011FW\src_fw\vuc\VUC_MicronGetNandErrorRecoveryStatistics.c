/****************************************************************************
*
*  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
*  All rights reserved
*
*  The content of this document is confidential and shall be applied
*  subject to the terms and conditions of the license agreement and
*  other applicable laws. Any unauthorized access, use or disclosure
*  of this document is strictly prohibited and may be punishable
*  under laws.
*
*  VUC_MicronGetNandErrorRecoveryStatistics.c
*
*
*
****************************************************************************/
/*
* ---------------------------------------------------------------------------------------------------
*	 header files
* ---------------------------------------------------------------------------------------------------
*/

#include "VUC_MicronResponse.h"
#include "VUC_MicronGetNandErrorRecoveryStatistics.h"
#include "VUC_MicronNandPass.h"
#include "VUC_command.h"
#include "hal/dmac/dmac_pop_cmd.h"
#include "hal/cop0/cop0_api.h"

#if (VUC_MICRON_NICKS_EN)
U8 VUCMyStrlen(const U8 *pubString)
{
	U8 ubCount = 0;
	for (; *pubString != '\0'; ubCount++, pubString++) {

	}

	return ubCount;
}

void VUCMyReverse(U8 *pubString)
{
	U8 ubi, ubj;
	U8 ubc;

	for (ubi = 0, ubj = VUCMyStrlen(pubString) - 1; ubi < ubj; ubi++, ubj--) {
		ubc = pubString[ubi];
		pubString[ubi] = pubString[ubj];
		pubString[ubj] = ubc;
	}
}

void VUCMyitoa(U32 uln, U8 *pubString, U8 ubBase)
{
	U32 uli;
	U8 ubrem;

	uli = 0;
	do {
		ubrem = uln % ubBase;
		pubString[uli++] = (ubrem > 9) ? (ubrem - 10) + 'a' : ubrem + '0';
	} while ((uln /= ubBase) > 0);

	pubString[uli] = '\0';
	VUCMyReverse(pubString);
}

U8 *VUCMyStrcat(U8 *pubString1, const char *pubString2)
{
	U8 *ubSave = pubString1;
	while (*ubSave != 0) {
		ubSave++;
	}
	for (; *pubString2 != '\0'; ubSave++, pubString2++) {
		*ubSave = *pubString2;
	}
	*ubSave = '\0';

	return (ubSave);
}
void VUCMyClearLastByte(U8 *pubString)
{
	U8 *ubSave = pubString;
	while (*ubSave != 0) {
		ubSave++;
	}
	ubSave--;
	*ubSave = '\0';
}

#if IM_N28
void VUCMicronGetNANDErrorRecoveryStatistics(U32 ulPayloadAddr)
{
	DMACParam_t DMACParam = {{0}};
	U32 *pulResponsePayload = NULL;

	gulResponseBufferAddr = ulPayloadAddr;

	pulResponsePayload = (U32 *)(gulResponseBufferAddr);
	DMACParam.DMACSetValue.ulDestAddr = gulResponseBufferAddr;
	DMACParam.DMACSetValue.ul32ByteNum = SIZE_IN_32B(DEF_KB(FRAMES_PER_PAGE));
	gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
	DMACSetValue(&DMACParam, gulDMACDirectWaitDone_Callback, 0);
	while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
		DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
	}

#if RETRY_MICRON_NICKS

#endif /*RETRY_MICRON_NICKS*/
}
#else /* IM_N28 */
void VUCMicronGetNANDErrorRecoveryStatistics(U32 ulPayloadAddr)
{
	gulResponseBufferAddr = ulPayloadAddr;
	U32 *pulResponsePayload = NULL;
	U8 ubCEIdx;
	U8 ubStepIdx;
	U8 ubDie;
	GetNandErrorRecoveryStatisticsResponseHeader_t *pResponseHeader;
	DMACParam_t DMACParam;
	pResponseHeader = (GetNandErrorRecoveryStatisticsResponseHeader_t *)(gulResponseBufferAddr);

	DMACParam.DMACSetValue.ulDestAddr = gulResponseBufferAddr;
	DMACParam.DMACSetValue.ulLowValue = 0;
	DMACParam.DMACSetValue.ulHighValue = 0;
	DMACParam.DMACSetValue.ul32ByteNum = SIZE_IN_32B(DEF_KB(FRAMES_PER_PAGE));
	DMACParam.DMACSetValue.btValidate = FALSE;
	gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
	DMACSetValue(&DMACParam, gulDMACDirectWaitDone_Callback, 0);
	while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
		DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
	}
	pResponseHeader->ubResponseHeaderFormatVersion = VUC_MICRON_RESPONSE_HEADER_FORMAT_VERSION_0;
	pResponseHeader->ubResponseDataFotmatVersion = VUC_MICRON_RESPONSE_FORMAT_BIN;
	pResponseHeader->uwCMDClass = VUC_MICRON_NAND_VS_COMMANDS;
	pResponseHeader->uwCMDCode = VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS;
	pResponseHeader->uwStatus = 0;
	pResponseHeader->ulDataPayloadSize = VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_DATA_PAYLOAD_LENGTH;
	for (ubCEIdx = 0; ubCEIdx < gFlhEnv.ubCENumber; ubCEIdx++) {
		for (ubDie = 0; ubDie < gFlhEnv.ubLUNperTarget; ++ubDie) {
			pulResponsePayload = (U32 *)(gulResponseBufferAddr + VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_DATA_PAYLOAD_OFFSET + ((((U32)ubDie * MAX_CE) + ubCEIdx) * VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_MAX_STEP * 4));
			for (ubStepIdx = 0; ubStepIdx < 27; ubStepIdx++) {
				pulResponsePayload[ubStepIdx] = gpMicronMAGERS->ulMicronQLCStepTriggerCnt[ubDie * MAX_CE + ubCEIdx][ubStepIdx];
			}
		}
	}
#if (IM_N48R)
	pulResponsePayload = (U32 *)(gulResponseBufferAddr + VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_DATA_PAYLOAD_OFFSET + ((U32)VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_MAX_DIE * VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS_MAX_STEP * 4));
	for (ubStepIdx = 0; ubStepIdx < 17; ubStepIdx++) {
		pulResponsePayload[ubStepIdx] = gpMicronMAGERS->ulMicronSLCStepTriggerCnt[ubStepIdx];
	}
#endif /* IM_N48R */
}
#endif /* IM_N28 */
#endif /*VUC_MICRON_NICKS_EN*/
