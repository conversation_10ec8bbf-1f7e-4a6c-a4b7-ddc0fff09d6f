/****************************************************************************/
//
//  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
//  All rights reserved
//
//  The content of this document is confidential and shall be applied
//  subject to the terms and conditions of the license agreement and
//  other applicable laws. Any unauthorized access, use or disclosure
//  of this document is strictly prohibited and may be punishable
//  under laws.
//
//  retry_tsb_tlc_softbit_retry.h
//
//
//
/****************************************************************************/

#ifndef _RETRY_TSB_TLC_SOFTBIT_RETRY_H_
#define _RETRY_TSB_TLC_SOFTBIT_RETRY_H_

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "setup.h"
#include "hal/fip/fip_api.h"
#include "common/fw_common.h"
#if (((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_MLC)) && ((TRUE == RETRY_MICRON_NICKS_ALL_PAGE_TYPE_SB_SUPPORT) && (TRUE == IM_B47R)))
/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */
#define  LDPC_FRAME_NUMBER_PER_FRAME                    (2)

#if(RETRY_SOFTBITT_FOR_SDK_EN==FALSE)

/*For choosing FSA when trigger DMA MT*/
#define SB_TARGET_PAGE_MODE						(0)
#define SB_SHARED_LOWER_PAGE_MODE			(1)
#define SB_SHARED_UPPER_PAGE_MODE			(2)

#define MICRON_LOWER_PAGE		(0)
#define MICRON_UPPER_PAGE		(1)
#define MICRON_EXTRA_PAGE		(2)
#define RETRY_SB_COARSE_TABLE_MAGIC_NUMBER   (18592)
#define RETRY_SB_COARSE_TABLE_MAGIC_NUMBER_MULTIPLIER	(4)

#define RETRY_SB_BACKUP_RESTORE_TO_DRAMIRAM       (1)
#define RETRY_SB_BACKUP_RESTORE_TO_ONLYDRAM       (0)
#define RETRY_SB_BACKUP_RESTORE_TO_ONLYDRAM_AND_KEEP       (2)
#define RETRY_SB_BACKUP_RESTORE_FROM_ONLYDRAM       (3)
#define RETRY_SB_BACKUP_RESTORE_TO_DRAMIRAM_AND_KEEP       (4)

#define NORMAL_MODE    (0)
#define INVERSE_MODE   (1)

#define RETRY_SB_BACKUP_RESTORE_TO_DRAMIRAM       (1)
#define RETRY_SB_BACKUP_RESTORE_TO_ONLYDRAM       (0)
#define RETRY_SB_BACKUP_RESTORE_TO_ONLYDRAM_AND_KEEP       (2)
#define RETRY_SB_BACKUP_RESTORE_FROM_ONLYDRAM       (3)
#define RETRY_SB_BACKUP_RESTORE_TO_DRAMIRAM_AND_KEEP       (4)

#define SWITCH_CLK__NORMAL_MODE            (0)
#define  SWITCH_CLK__LOW_CLK_MODE         (1)

#define RETRY_SB_INSERT_USED_MT_MODE                   (0)
#define RETRY_SB_INSERT_NEW_MT_MODE                    (1)
#define RETRY_SB_INSERT_USED_MT_MODE_AND_WRITE_SPARE   (2)

/// for gubCnt1Result
#define CNT1_RESULT_ARRAY_PARM_LENGTH   (4)
#define CNT1_RESULT_ARRAY_PARM_CNT        (5)
#define CNT1_RESULT_ARRAY_SIGN_BITMAP      (6)
#define CNT1_RESULT_ARRAY_ROUND_CNT      (7)
#define CNT1_RESULT_SKIP_BIT_CNT      (8)

#define COARSE_TABLE_CHECK_ROUND_MAX_TIMES  (3)

#define  COARSE_LEVEL_DIVISOR_1_IDX  (0)
#define  COARSE_LEVEL_DIVISOR_2_IDX  (1)
#define  COARSE_LEVEL_DIVISOR_3_IDX  (2)
#define  COARSE_LEVEL_THRESHOLD_1_IDX  (3)
#define  COARSE_LEVEL_THRESHOLD_2_IDX  (4)


#define RETRY_SB_MT_FAIL_RETRY_CNT_MAX            (0xF)

#define SB_BICS3_BICS4_GENERAL_LLR_TABLE_START_IDX		(0)
#define SB_BICS3_BICS4_CORNER_CASE_LLR_TABLE_START_IDX	(1)

//SB Debug 0121
#define SB_FIRST_2K_DATA		BIT0
#define SB_FIRST_2K_SPARE		BIT1
#define SB_SECOND_2K_DATA		BIT2
#define SB_SECOND_2K_SPARE	BIT3
#else /* (RETRY_SOFTBITT_FOR_SDK_EN==FALSE) */

#endif /* (RETRY_SOFTBITT_FOR_SDK_EN==FALSE) */

#define SBIT_RETRY_STATE_READY                          (BIT0)
#define SBIT_RETRY_FORCE_SIG                            (BIT1)

#define SBIT_NAND_STATE_INIT                            (0)

#define SBIT_LOWER_PAGE_ID                              (0)         // page id low
#define SBIT_MIDDLE_PAGE_ID                             (1)         // page id middle
#define SBIT_UPPER_PAGE_ID                              (2)         // page id up
#define SBIT_TOP_PAGE_ID                                (3)         // page id top (QLC)
#define SBIT_MAX_SUPPORT_PAGE_NUM                       (3)         // max page number

#define SBIT_LLR_TBL_NUM                                (2)         // llr table buffer address, 4 llr table for lmut
#define SBIT_LLR_TABLE_SIZE                             (32)        // each table size is 32B (fixed by hardware)
#define SBIT_GRAY_CODE_TABLE_SIZE                       (20)        // gray code table size in byte (fixed by hardware)
#define SBIT_LUT_TABLE_SIZE                             (40)        // lookup table size in byte (fixed by hardware)
#define SBIT_DSP_PARAM_TABLE_SIZE                       (8)         // dsp param table size in byte (fixed by hardware)

#define SBIT_DSP_LLR_TABLE_SIZE                             (40)        // each DSP LLR table size is 40B (fixed by hardware)
#define SBIT_DSP_LLR_TABLE_BITS_PER_HW_ACCESS      (5)        // HW access LLR table per 5Bits
#define SBIT_DSP_LLR_TABLE_SIZE_IN_4_BYTES         ((SBIT_DSP_LLR_TABLE_SIZE << 3) / 32)        //10
#define SBIT_DSP_LLR_TABLE_SIZE_IN_40_BITS         ((SBIT_DSP_LLR_TABLE_SIZE << 3) / 40)        //8
#define SBIT_DSP_LLR_TABLE_SIZE_IN_5_BITS         ((SBIT_DSP_LLR_TABLE_SIZE << 3) / 5)			//64
#define SBIT_DSP_LLR_TABLE_5_BITS_TO_40_BITS_IDX		(8)		//	64 / 8
#define SBIT_DSP_LLR_TABLE_5_BITS_TO_40_BITS_IDX_LOG	(3)		//	Log(8)
#define SBIT_DSP_LLR_TABLE_5_BITS_TO_OPRATION_IDX		(16)		//	64 / 4
#define SBIT_DSP_LLR_TABLE_5_BITS_TO_OPRATION_IDX_LOG	(4)		//	Log(16)
#define SBIT_DSP_LLR_TABLE_BITS_PER_OPRATION	(2)		//	2Bits for +1, -1, 0

#define SBIT_DELTA_MAX_COL_NUM                          (8)         // delta table column number
#define SBIT_DELTA_MAX_ROW_NUM                          (7)         // delta table row number
#define SBIT_DELTA_N3R                                  (0)
#define SBIT_DELTA_N2R                                  (1)
#define SBIT_DELTA_N1R                                  (2)
#define SBIT_DELTA_P0R                                  (3)
#define SBIT_DELTA_P1R                                  (4)
#define SBIT_DELTA_P2R                                  (5)
#define SBIT_DELTA_P3R                                  (6)

#define SBIT_DELTA_P0                                   (0)
#define SBIT_DELTA_P1                                   (1)
#define SBIT_DELTA_P2                                   (2)
#define SBIT_DELTA_P3                                   (3)
#define SBIT_DELTA_P4                                   (4)
#define SBIT_DELTA_P5                                   (5)
#define SBIT_DELTA_P6                                   (6)
#define SBIT_DELTA_DUMMY                                (7)

#define SBIT_CENTER_MAX_COL_NUM                         (SBIT_DELTA_MAX_COL_NUM)
#define SBIT_CENTER_MAX_ROW_NUM                         (SBIT_DELTA_MAX_ROW_NUM - 1)
#define SBIT_CENTER_P0                                  (SBIT_DELTA_P0)
#define SBIT_CENTER_P1                                  (SBIT_DELTA_P1)
#define SBIT_CENTER_P2                                  (SBIT_DELTA_P2)
#define SBIT_CENTER_P3                                  (SBIT_DELTA_P3)
#define SBIT_CENTER_P4                                  (SBIT_DELTA_P4)
#define SBIT_CENTER_P5                                  (SBIT_DELTA_P5)
#define SBIT_CENTER_P6                                  (SBIT_DELTA_P6)

#define SOFTBIT_MAX_RETRY_STATE                         (10)             // softbit retry lib maximum support state number

#define SBIT_FPU_STEP_INIT                              (0)
#define SBIT_FPU_STEP_DONE                              (0xFE)
#define SBIT_FPU_STEP_INVALID                           (0xFF)

#define SBIT_CENTER_NUM                                 (4)

#define SBIT_TAG_STATUS_DONE                            (BIT0)
#define SBIT_TAG_STATUS_ALLOC_FAIL                      (BIT1)

#define SOFTBIT_RETRY_FPU_BUF_NUM                   (2)
#define SOFTBIT_RETRY_FPU_BUF_ENTRY_NUM             (64)
#define SOFTBIT_RETRY_FPU_TEMPLATE_BUF_ENTRY_NUM    (192)

//#define SOFTBIT_RETRY_CASE_A				(0)
//#define SOFTBIT_RETRY_CASE_B				(1)
//#define SOFTBIT_RETRY_CASE_C				(2)
#define SB_ARC_MAX_ROUND_CNT				(4)

#if (TRUE == RETRY_MICRON_NICKS_ALL_PAGE_TYPE_SB_SUPPORT)	//Phison Flow temp use the same SB read flow with Micron Nicks
#define RETRY_SB_MICRON_ERROR_RECOVERY_READ_RETRY_ARC_STEP	(11)
#define RETRY_SB_MICRON_ERROR_RECOVERY_READ_RETRY_ARC_STEP_NUM	(4)	//Should sync with HBIT_RETRY_MICRON_ARC_STEP_NUM
#define RETRY_SB_MICRON_ERROR_RECOVERY_READ_RETRY_ARC_SB_STEP	(RETRY_SB_MICRON_ERROR_RECOVERY_READ_RETRY_ARC_STEP + RETRY_SB_MICRON_ERROR_RECOVERY_READ_RETRY_ARC_STEP_NUM)
#define RETRY_SB_MICRON_ERROR_RECOVERY_BLK_REFRESH_STEP	(12)
#endif	/*(TRUE == RETRY_MICRON_NICKS_ALL_PAGE_TYPE_SB_SUPPORT)*/

#define RETRY_SB_COARSE_TUNING_COFFICEINT_CONSTANT_FLOAT_SHIFT	(1000000)
#define RETRY_SB_COUNT_DELTA_VTH_BASE	(2288)
#define RETRY_SB_COUNT_DELTA_VTH_PARAMETER_A	(46)
#define RETRY_SB_COUNT_DELTA_VTH_PARAMETER_B	(4)

#define RETRY_SB_COUNT_DELTA_LEVEL(ubReadState_m)	(ubReadState_m + 1)

// Check over value boundary related
#define RETRY_SB_NO_OVER_BOUNDARY			(0)
#define RETRY_SB_OVER_POSITIVE_BOUNDARY		(1)
#define RETRY_SB_OVER_NEGATIVE_BOUNDARY		(2)

/*
 * ---------------------------------------------------------------------------------------------------
 *  enum
 * ---------------------------------------------------------------------------------------------------
 */
#if(RETRY_SOFTBITT_FOR_SDK_EN==FALSE)

/*
 *  SOFTBIT SUBSTATE MACHINE
 */
typedef enum SoftBitSubStateEnum {
	RETRY_SB_SUBSTATE_DET_COARSE_LVL__INIT = 1,							// 1

	//for Coarse Tuning Version2 in Phison Flow
	RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_SET_INIT_READ_LEVEL,
	RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_SET_READ_LEVEL,
	RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_SELECT_READ_SHARED_PAGE,
	//State blow should stay continuous
	RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_READ_SHARED_LOWER_PAGE,
	RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_READ_SHARED_UPPER_PAGE,
	RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_READ_SHARED_EXTRA_PAGE,
	//State above should stay continuous
	RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_DSP_TRIGGER,

	RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_CHK_COARSE_TABLE,
	RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_IDLE_AND_WAIT,

	RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_INIT, // 11
	RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_SET_READ_LEVEL_OFFSET,
	RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_ENABLE_AUTO_READ_CALIBRATION,
	RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_HB_READ,
	RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_HB_CORR_2K,

	RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_INIT,
	RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_SET_ARC_CALIBRATION_PERSISTENCE,
	RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_HB, // 18
	RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_SB1,
	RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_SB2,
	RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_SB3,
	RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_SB4,
	RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_IDLE_OR_WAIT,

	RETRY_SB_SUBSTATE_SB_DECODE_WITH_DEFAULT_LLR_INIT,
	RETRY_SB_SUBSTATE_SB_DECODE_WITH_DEFAULT_LLR_SB_CORR,

	RETRY_SB_SUBSTATE_SB_DECODE_WITH_ADT_LLR_INIT,
	RETRY_SB_SUBSTATE_SB_DECODE_WITH_ADT_LLR_SB_CORR,

	RETRY_SB_SUBSTATE_SB_DSP2_INIT,
	RETRY_SB_SUBSTATE_SB_DSP2_READ_TARGET_PAGE,
	RETRY_SB_SUBSTATE_SB_DSP2_GET_FEATURE_OF_ARC_RESULT,
	RETRY_SB_SUBSTATE_SB_DSP2_SET_READ_LEVEL_FOR_NEXT_WORDLINE_I,
	RETRY_SB_SUBSTATE_SB_DSP2_SET_READ_LEVEL_FOR_NEXT_WORDLINE_II,
	RETRY_SB_SUBSTATE_SB_DSP2_ENABLE_ARC_DISABLE_PERSISTENCE,
	RETRY_SB_SUBSTATE_SB_DSP2_READ_NEXT_WORDLINE_SHARE_PAGE_I,
	RETRY_SB_SUBSTATE_SB_DSP2_READ_NEXT_WORDLINE_SHARE_PAGE_II,
	RETRY_SB_SUBSTATE_SB_DSP2_GEN_SB6_SB7,
	RETRY_SB_SUBSTATE_SB_DSP2_DISABLE_ARC_ENABLE_PERSISTENCE,
	RETRY_SB_SUBSTATE_SB_DSP2_READ_LEVEL_HB,
	RETRY_SB_SUBSTATE_SB_DSP2_READ_LEVEL_SB1,
	RETRY_SB_SUBSTATE_SB_DSP2_READ_LEVEL_SB2,
	RETRY_SB_SUBSTATE_SB_DSP2_READ_LEVEL_SB3,
	RETRY_SB_SUBSTATE_SB_DSP2_READ_LEVEL_SB4,
	RETRY_SB_SUBSTATE_SB_DSP2_SB_TRIG_SBC_FOR_ADT_LLR,
	RETRY_SB_SUBSTATE_SB_DSP2_INT_SB_CORR,
	RETRY_SB_SUBSTATE_SB_DSP2_SB_CORR,

	RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_WITH_SPECIFIC_RETRY_TABLE_READ_OFFSET_INIT,
	RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_WITH_SPECIFIC_RETRY_TABLE_READ_OFFSET_SET_FEATURE,
	RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_WITH_SPECIFIC_RETRY_TABLE_READ_OFFSET_ENABLE_AUTO_READ_CALIBRATION,
	RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_WITH_SPECIFIC_RETRY_TABLE_READ_OFFSET_HB_READ,
	RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_WITH_SPECIFIC_RETRY_TABLE_READ_OFFSET_HB_CORR_2K,

	RETRY_SB_SUBSTATE_WITH_OPT_READ_LEVEL__AFTER_RAIDECC__INIT,

	RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_INIT,
	RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_ARC,
	//State blow should stay continuous
	RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_READ_OFFSET_LOWER,
	RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_READ_OFFSET_UPPER,
	RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_READ_OFFSET_EXTRA,
	//State above should stay continuous
	RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_SB_READ_OFFSET,
	RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_RETRY_TABLE,

	RETRY_SB_SUBSTATE_SET_WORDLINE_BYPASS_SET_FEATURE,
	RETRY_SB_SUBSTATE_SET_WORDLINE_BYPASS_GET_FEATURE,
	RETRY_SB_SUBSTATE_SET_WORDLINE_BYPASS_CHECK_FEATURE,

	//Phison Flow Clear Prefix Read Offset
	RETRY_SB_SUBSTATE_CLEAR_PREFIX_READ_OFFSET,

	RETRY_SB_SUBSTATE_NULL = 0xFF
} SoftBitSubStateEnum_t;

/*
 *  SOFTBIT 3RD STATE MACHINE
 *  =====================
 */
typedef enum SoftBitThirdStateEnum {
	RETRY_SB_3RD_STATE_HB_READ_INIT = 1,
	RETRY_SB_3RD_STATE_HB_READ_DIRECT_READ_OP,
	RETRY_SB_3RD_STATE_HB_READ_DIRECT_READ_DMA,

	RETRY_SB_3RD_STATE_SET_READ_LEVEL_OFFSET_INIT,
	//State blow should stay continuous
	RETRY_SB_3RD_STATE_SET_READ_LEVEL_OFFSET_I,
	RETRY_SB_3RD_STATE_SET_READ_LEVEL_OFFSET_II,
	RETRY_SB_3RD_STATE_SET_READ_LEVEL_OFFSET_III,
	RETRY_SB_3RD_STATE_SET_READ_LEVEL_OFFSET_IV,
	//State above should stay continuous

	//State blow should stay continuous
	RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_I,
	RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_II,
	RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_III,
	RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_IV,
	//State above should stay continuous

	//State blow should stay continuous
	RETRY_SB_3RD_STATE_CHECK_READ_LEVEL_OFFSET_I,
	RETRY_SB_3RD_STATE_CHECK_READ_LEVEL_OFFSET_II,
	RETRY_SB_3RD_STATE_CHECK_READ_LEVEL_OFFSET_III,
	RETRY_SB_3RD_STATE_CHECK_READ_LEVEL_OFFSET_IV,
	//State above should stay continuous

	RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_SET_FEATURE, // 17
	RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_GET_FEATURE, // 18
	RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_CHECK_FEATURE, // 19

	RETRY_SB_3RD_STATE_SETTING_RETRY_TABLE_SET_FEATURE,
	RETRY_SB_3RD_STATE_SETTING_RETRY_TABLE_GET_FEATURE,
	RETRY_SB_3RD_STATE_SETTING_RETRY_TABLE_CHECK_FEATURE,

	RETRY_SB_3RD_STATE_SB_READ_SB1_INIT,
	RETRY_SB_3RD_STATE_SB_READ_SB1_DELTA_SET_FEATURE,
	RETRY_SB_3RD_STATE_SB_READ_SB1_DELTA_READ,
	RETRY_SB_3RD_STATE_SB_READ_SB1_DELTA_DMA,

	RETRY_SB_3RD_STATE_SB_READ_SB2_INIT,
	RETRY_SB_3RD_STATE_SB_READ_SB2_DELTA_SET_FEATURE,
	RETRY_SB_3RD_STATE_SB_READ_SB2_DELTA_READ,
	RETRY_SB_3RD_STATE_SB_READ_SB2_DELTA_DMA,
	RETRY_SB_3RD_STATE_SB_READ_SB2_INIT_2,
	RETRY_SB_3RD_STATE_SB_READ_SB2_PLUS_MINUS_1_DELTA_SET_FEATURE,
	RETRY_SB_3RD_STATE_SB_READ_SB2_PLUS_MINUS_1_DELTA_READ,
	RETRY_SB_3RD_STATE_SB_READ_SB2_PLUS_MINUS_1_DELTA_DMA_WITH_XNOR,

	RETRY_SB_3RD_STATE_SB_READ_SB3_INIT,
	RETRY_SB_3RD_STATE_SB_READ_SB3_READ,
	RETRY_SB_3RD_STATE_SB_READ_SB3_DMA,

	RETRY_SB_3RD_STATE_SB_READ_SB4_INIT,
	RETRY_SB_3RD_STATE_SB_READ_SB4_READ,
	RETRY_SB_3RD_STATE_SB_READ_SB4_DMA,

	RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_FROM_ARC_INIT,
	//State blow should stay continuous
	RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_FROM_ARC_I,
	RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_FROM_ARC_II,
	RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_FROM_ARC_III,
	RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_FROM_ARC_IV,
	//State above should stay continuous

	//State blow should stay continuous
	RETRY_SB_3RD_STATE_UPDATE_ARC_READ_LEVEL_OFFSET_TABLE_I,
	RETRY_SB_3RD_STATE_UPDATE_ARC_READ_LEVEL_OFFSET_TABLE_II,
	RETRY_SB_3RD_STATE_UPDATE_ARC_READ_LEVEL_OFFSET_TABLE_III,
	RETRY_SB_3RD_STATE_UPDATE_ARC_READ_LEVEL_OFFSET_TABLE_IV,
	//State above should stay continuous

	RETRY_SB_3RD_STATE_SB_CLEAR_SB_OFFSET_INIT,
	RETRY_SB_3RD_STATE_SB_CLEAR_SB_OFFSET_DELTA_SET_FEATURE,

	//Phison Flow Clear Prefix Read Offset with Dummy Read CMD
	RETRY_SB_3RD_STATE_HB_READ_CLEAR_PREFIX_DUMMY_OP,

	RETRY_SB_3RD_STATE_NULL = 0xFF
} SoftBitThirdStateEnum_t;

typedef enum SoftBitFourthStateEnum {
	RETRY_SB_4TH_STATE_SB_READ_LEVEL_BY_DELTA_TABLE_SET_FEATURE = 1,                                         //(1)
	RETRY_SB_4TH_STATE_SB_READ_LEVEL_BY_DELTA_TABLE_GET_FEATURE,
	RETRY_SB_4TH_STATE_SB_READ_LEVEL_BY_DELTA_TABLE_CHECK_FEATURE,

} SoftBitFourthStateEnum_t;

#if (IM_B47R)

typedef enum SBPAGETYPEEnum {
	RETRY_SB_MICRON_SLC_PAGE = 0,
	RETRY_SB_MICRON_MLC_LOWER_PAGE,
	RETRY_SB_MICRON_MLC_UPPER_PAGE,
	RETRY_SB_MICRON_TLC_LOWER_PAGE,
	RETRY_SB_MICRON_TLC_UPPER_PAGE,
	RETRY_SB_MICRON_TLC_EXTRA_PAGE
} SBPAGETYPEEnum_t;

#else/* IM_B47R */
typedef enum SBPAGETYPEEnum {
	RETRY_SB_MICRON_SLC_PAGE = 0,
	RETRY_SB_MICRON_MLC_LOWER_PAGE,
	RETRY_SB_MICRON_MLC_UPPER_PAGE,
	RETRY_SB_MICRON_TLC_OPEN_PAGE,
	RETRY_SB_MICRON_TLC_LOWER_PAGE,
	RETRY_SB_MICRON_TLC_UPPER_PAGE,
	RETRY_SB_MICRON_TLC_EXTRA_PAGE
} SBPAGETYPEEnum_t;
#endif /* IM_B47R */

typedef enum SBDPSENGINEPAGETYPEEnum {
	RETRY_SB_DSP_ENGINE_LOWER_PAGE = 0,
	RETRY_SB_DSP_ENGINE_UPPER_PAGE,
	RETRY_SB_DSP_ENGINE_EXTRA_PAGE,
} SBDPSENGINEPAGETYPEEnum_t;

typedef enum SBTLCLEVELSTATEEnum {
	RETRY_SB_TLC_LEVEL_A_STATE = 0,
	RETRY_SB_TLC_LEVEL_B_STATE,
	RETRY_SB_TLC_LEVEL_C_STATE,
	RETRY_SB_TLC_LEVEL_D_STATE,
	RETRY_SB_TLC_LEVEL_E_STATE,
	RETRY_SB_TLC_LEVEL_F_STATE,
	RETRY_SB_TLC_LEVEL_G_STATE
} SBTLCLEVELSTATEEnum_t;

typedef enum RetrySBDataBitLength {
	RETRY_SB_U8_BIT_LENGTH = 8,
	RETRY_SB_U16_BIT_LENGTH = 16,
	RETRY_SB_U32_BIT_LENGTH = 32,
	RETRY_SB_U64_BIT_LENGTH = 64,
} RetrySBDataBitLength_t;

#else /* (RETRY_SOFTBITT_FOR_SDK_EN==FALSE) */

#endif  /* (RETRY_SOFTBITT_FOR_SDK_EN==FALSE) */
/*
 * ---------------------------------------------------------------------------------------------------
 *  data types
 * ---------------------------------------------------------------------------------------------------
 */
typedef struct fpl_sbit_retry_fip_llr_struct        FPL_SBIT_RETRY_FIP_LLR_STRUCT,   *FPL_SBIT_RETRY_FIP_LLR_STRUCT_PTR;
#if(RETRY_SOFTBITT_FOR_SDK_EN==FALSE)
typedef struct fpl_sbit_retry_param_struct          FPL_SBIT_RETRY_PARAM_STRUCT,     *FPL_SBIT_RETRY_PARAM_STRUCT_PTR;
#endif /* (RETRY_SOFTBITT_FOR_SDK_EN==FALSE) */

struct fpl_sbit_retry_fip_llr_struct {
	U8 entry[SBIT_LLR_TABLE_SIZE];
};

/*
 *           DETAL TABLE             =>          CENTER TABLE
 *           ===========                         ============
 *
 *                                                 +---------------------------------------+
 *                                                 | C0 |    |    |    | C1 |    |    |    |        <= center 0 and 1 for lower page
 *                                                 |    | C0 |    | C1 |    | C2 |    |    |        <= center 0, 1, and 2 for middle page
 *                                                 | C0 |    |    |    | C1 |    |    |    |        <= center 0 and 1 for upper page
 *  +--------------------------------------------------------------------------------------+
 *  |           | P0 | P1 |...| P7 |    |          | P0 | P1 | P2 | P3 | P4 | P6 | P6 | P7 |
 *  |------------------------------+----+--------------------------------------------------|
 *  | - detal 3 | ## | ## |...| ## |    |          |    |    |    |    |    |    |    |    |
 *  |           |    |    |   |    | => | level  0 | ## | ## | ## | ## | ## | ## | ## | ## |
 *  | - detal 2 | ## | ## |...| ## |    |          |    |    |    |    |    |    |    |    |
 *  |           |    |    |   |    | => | level  1 | ## | ## | ## | ## | ## | ## | ## | ## |
 *  | - detal 1 | ## | ## |...| ## |    |          |    |    |    |    |    |    |    |    |
 *  |           |    |    |   |    | => | level  2 | ## | ## | ## | ## | ## | ## | ## | ## |
 *  |   detal 0 | ## | ## |...| ## |    |          |    |    |    |    |    |    |    |    |
 *  |           |    |    |   |    | => | level  3 | ## | ## | ## | ## | ## | ## | ## | ## |
 *  | + detal 1 | ## | ## |...| ## |    |          |    |    |    |    |    |    |    |    |
 *  |           |    |    |   |    | => | level  4 | ## | ## | ## | ## | ## | ## | ## | ## |
 *  | + detal 2 | ## | ## |...| ## |    |          |    |    |    |    |    |    |    |    |
 *  |           |    |    |   |    | => | level  5 | ## | ## | ## | ## | ## | ## | ## | ## |
 *  | + detal 3 | ## | ## |...| ## |    |          |    |    |    |    |    |    |    |    |
 *  +--------------------------------------------------------------------------------------+
 *
 */
struct fpl_sbit_retry_param_struct {
	FPL_SBIT_RETRY_FIP_LLR_STRUCT_PTR default_llr[SBIT_MAX_SUPPORT_PAGE_NUM];
	FPL_SBIT_RETRY_FIP_LLR_STRUCT_PTR adt_llr[SBIT_LLR_TBL_NUM];

	U8  delta[SBIT_DELTA_MAX_ROW_NUM][SBIT_DELTA_MAX_COL_NUM];
	U8  center[SBIT_CENTER_MAX_ROW_NUM][SBIT_CENTER_MAX_COL_NUM];
	U8  dsp_center_idx[SBIT_CENTER_NUM];
};

#if(RETRY_SOFTBITT_FOR_SDK_EN==FALSE)
struct corse_tuning_element {
	U32 x_low;
	U32 x_upp;
	U32 y_low;
	U32 y_upp;
	U32 z_low;
	U32 z_upp;
};

typedef struct SoftBitLLR40Bits   SoftBitLLR40Bits_t;
struct SoftBitLLR40Bits {      // use union type to optimize.
	U8 ubLLR8Bits[5];
};

typedef struct SoftBitLLRTable   SoftBitLLRTable_t;
struct SoftBitLLRTable {      // use union type to optimize.
	union {
		struct {
			U32 ulLLR32bits[10];
		} AccessBy32bits;
		struct {
			U8 ubLLR8bits[40];
		} AccessBy8bits;
		SoftBitLLR40Bits_t LLR40Bits[8];
	} LLRTable;
};

typedef struct LLRTable40BitsAccess   LLRTable40BitsAccess_t;
struct LLRTable40BitsAccess {      // use union type to optimize.
	union {
		U64 uoAll;
		SoftBitLLR40Bits_t LLR40Bits;
	} LLRTable;
};

#else /* (RETRY_SOFTBITT_FOR_SDK_EN==FALSE) */
#endif /* (RETRY_SOFTBITT_FOR_SDK_EN==FALSE) */

#endif /*((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_MLC))*/
#endif /* _RETRY_TSB_TLC_SOFTBIT_RETRY_H_ */

