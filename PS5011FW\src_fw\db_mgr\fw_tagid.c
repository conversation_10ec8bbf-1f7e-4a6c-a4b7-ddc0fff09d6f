#include "setup.h"
#include "typedef.h"
#include "db_mgr/fw_tagid.h"
#include "debug/debug.h"
#include "drive_log/drive_log_api.h"
#include "ftl/ftl_api.h"
/**********************************
	if add hw tag pool,
	1. HW_NUM should change.
	2. add tag_id_t xxxxxTagIdPool.
	3. add gTagIdMgr member.
***********************************/

//const tag_id_t DefaultTagId = {{0}};

tag_id_t gCOP0TagIdPool;
tag_id_t gCOP1TagIdPool;
tag_id_t gBMUTagIdPool;
tag_id_t gDMACTagIdPool;
tag_id_t gXZIPTagIdPool;
tag_id_t gRAIDECCTagIdPool;

tag_id_t *gTagIdMgr[HW_NUM] = {
	&gCOP0TagIdPool,
	&gCOP1TagIdPool,
	&gBMUTagIdPool,
	&gDMACTagIdPool,
	&gXZIPTagIdPool,
	&gRAIDECCTagIdPool
};

const U8 gubTagNum[HW_NUM] = { 254, 254, 254, 254, 62, 64 };
const U8 gubTagIdBase[HW_NUM] = {1, 1, 1, 1, 1, 0};

//============================
//	Debug Code for Tag Pool
//============================
#if (DEBUG_TAG_POOL_EN)
typedef enum {
	DEBUG_TAG_POOL_COP0,
	DEBUG_TAG_POOL_NUM  //Currently only for COP0 (COP0_TAG_POOL_ID)
} DebugTagPoolTypeEnum_t;

typedef struct {
	U8 aubCOP0TagFreeBMP[DEBUG_TAG_POOL_COP0_BMP_SIZE];
} DebugTagPoolMgr_t;
DebugTagPoolMgr_t gDebugTagPoolMgr;

AOM_DEBUG_TAG_POOL void DebugTagPoolHandleError(U16 uwErrorCode, U8 ubTagPoolId, U8 ubTag)
{
	//==================
	//	Drive Log
	//==================
	if (DRIVE_LOG_EN) {
		DriveLogPayload_t DriveLogPayload = {{0}};
		DriveLogPayload.Payload_DebugTagPool.ubTag = gTagIdMgr[ubTagPoolId]->uwFront;
		if (ubTagPoolId < CMD_TABLE_ID_NUM) {
			DriveLogPayload.Payload_DebugTagPool.ulCmdTableCallback = gpuoCmdTableMgr[ubTagPoolId][ubTag].ulCmdFuncPTR;
			DriveLogPayload.Payload_DebugTagPool.ulCmdTableData = gpuoCmdTableMgr[ubTagPoolId][ubTag].ulData.ulAll;
		}
		DriveLogPayload.Payload_DebugTagPool.uwErrorCode = uwErrorCode;
		DriveLogPayload.Payload_DebugTagPool.ubPoolHead = gTagIdMgr[ubTagPoolId]->uwFront;
		DriveLogPayload.Payload_DebugTagPool.ubPoolTail = gTagIdMgr[ubTagPoolId]->uwRear;
		DriveLogPayload.Payload_DebugTagPool.ubPoolFreeTagNum = gTagIdMgr[ubTagPoolId]->uwNumInPool;
		DriveLogPayload.Payload_DebugTagPool.uwPopCOP0TagButPoolEmptyCnt = gpVTDBUF->uwPopCOP0TagButPoolEmptyCnt;
		DriveLogAdd(DEBUG_TAG_POOL, &DriveLogPayload, DRIVE_LOG_ADD_DEFAULT_MODE);
	}

	M_FW_CRITICAL_ASSERT(uwErrorCode, FALSE);
}

INLINE void DebugTagPoolInit(void)
{
	memset(&gDebugTagPoolMgr, 0, sizeof(gDebugTagPoolMgr));
}

INLINE void DebugTagPoolPopCOP0Tag(U8 ubTagPoolId, U8 ubTag)
{
	if (COP0_TAG_POOL_ID == ubTagPoolId) {
		//If tag is already being used
		if (0 == M_CHK_BITMAP(gDebugTagPoolMgr.aubCOP0TagFreeBMP, ubTag)) {
			DebugTagPoolHandleError(ASSERT_DB_MGR_0x0259, ubTagPoolId, ubTag);
		}
		M_CLEAR_BITMAP(gDebugTagPoolMgr.aubCOP0TagFreeBMP, ubTag);
	}
}

INLINE void DebugTagPoolPushCOP0Tag(U8 ubTagPoolId, U8 ubTag)
{
	if (COP0_TAG_POOL_ID == ubTagPoolId) {
		//If tag is NOT being used
		if (M_CHK_BITMAP(gDebugTagPoolMgr.aubCOP0TagFreeBMP, ubTag)) {
			DebugTagPoolHandleError(ASSERT_DB_MGR_0x025A, ubTagPoolId, ubTag);
		}
		M_SET_BITMAP(gDebugTagPoolMgr.aubCOP0TagFreeBMP, ubTag);
	}
}
#else /* (DEBUG_TAG_POOL_EN) */
#define DebugTagPoolInit(...)
#define DebugTagPoolPopCOP0Tag(...)
#define DebugTagPoolPushCOP0Tag(...)
#define DebugTagPoolHandleError(...)
#endif /* (DEBUG_TAG_POOL_EN) */

void FTLInitTagIdPool(void);
U16 FTLPopTagPool(U8 ubTagPoolId);
void FTLPushTagPool(U8 ubTagPoolId, U16 ubTagId);

void FTLInitTagIdPool(void)
{
	U8 ubTagIdMgrIndex;
	U16 uwPoolIndex;
#if (RDT_RUN_ONLINE)
	//Clear all Pool
	memset(&gCOP0TagIdPool, 0, sizeof(gCOP0TagIdPool));
	memset(&gCOP1TagIdPool, 0, sizeof(gCOP1TagIdPool));
	memset(&gBMUTagIdPool, 0, sizeof(gBMUTagIdPool));
	memset(&gDMACTagIdPool, 0, sizeof(gDMACTagIdPool));
	memset(&gXZIPTagIdPool, 0, sizeof(gXZIPTagIdPool));
	memset(&gRAIDECCTagIdPool, 0, sizeof(gRAIDECCTagIdPool));
#endif
	DebugTagPoolInit();
	for (ubTagIdMgrIndex = 0; ubTagIdMgrIndex < HW_NUM; ubTagIdMgrIndex++) {
		//*gTagIdMgr[ubTagIdMgrIndex] = DefaultTagId;

		for (uwPoolIndex = 0; uwPoolIndex < gubTagNum[ubTagIdMgrIndex]; uwPoolIndex++) {
			FTLPushTagPool(ubTagIdMgrIndex, (uwPoolIndex + gubTagIdBase[ubTagIdMgrIndex]));
		}
	}
}

INLINE void FTLTagAssertPoolNotEmpty(U8 ubTagPoolId)
{
	if (0 == gTagIdMgr[ubTagPoolId]->uwNumInPool) {
		gpVTDBUF->uwPopCOP0TagButPoolEmptyCnt++;
#if (DEBUG_TAG_POOL_EN)
		DebugTagPoolHandleError(ASSERT_DB_MGR_0x0256, ubTagPoolId, 0);
#else /* (DEBUG_TAG_POOL_EN) */
		M_FW_ASSERT(ASSERT_DB_MGR_0x0256, FALSE);
#endif /* (DEBUG_TAG_POOL_EN) */
	}
}

U16 FTLPopTagPool(U8 ubTagPoolId)
{
	U16 uwTagId;

	uwTagId = gTagIdMgr[ubTagPoolId]->uwPool[gTagIdMgr[ubTagPoolId]->uwFront];
	gTagIdMgr[ubTagPoolId]->uwFront = (gTagIdMgr[ubTagPoolId]->uwFront + 1) % gubTagNum[ubTagPoolId];
	FTLTagAssertPoolNotEmpty(ubTagPoolId);
	DebugTagPoolPopCOP0Tag(ubTagPoolId, uwTagId);
	gTagIdMgr[ubTagPoolId]->uwNumInPool--;
	if (ubTagPoolId < CMD_TABLE_ID_NUM) {
		memset(&gpuoCmdTableMgr[ubTagPoolId][uwTagId], 0, sizeof(gpuoCmdTableMgr[ubTagPoolId][uwTagId]));
	}
	return uwTagId;
}

void FTLPushTagPool(U8 ubTagPoolId, U16 uwTagId)
{
	M_FW_ASSERT(ASSERT_DB_MGR_0x0257, uwTagId != TAG_ID_ALLOCATE);
	if ((gTagIdMgr[ubTagPoolId]->uwFront == gTagIdMgr[ubTagPoolId]->uwRear) && (gTagIdMgr[ubTagPoolId]->uwNumInPool != 0)) {
		M_FW_ASSERT(ASSERT_DB_MGR_0x0258, FALSE);
	}
	DebugTagPoolPushCOP0Tag(ubTagPoolId, uwTagId);
	gTagIdMgr[ubTagPoolId]->uwPool[gTagIdMgr[ubTagPoolId]->uwRear] = uwTagId;
	gTagIdMgr[ubTagPoolId]->uwNumInPool++;
	gTagIdMgr[ubTagPoolId]->uwRear = (gTagIdMgr[ubTagPoolId]->uwRear + 1) % gubTagNum[ubTagPoolId];
}

U16 FTLGetTagPoolNum(U8 ubTagPoolId)
{
	return gTagIdMgr[ubTagPoolId]->uwNumInPool;
}

#if RDT_MODE_EN
U16 FTLGetTagID(U8 ubTagPoolId)
{
	U16 uwTagId;
	uwTagId = gTagIdMgr[ubTagPoolId]->uwPool[gTagIdMgr[ubTagPoolId]->uwFront];
	return uwTagId;
}
#endif
