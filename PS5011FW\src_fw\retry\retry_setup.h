#ifndef _RETRY_SETUP_H_
#define _RETRY_SETUP_H_

#include "setup.h"
#include "env.h"

#if RETRY_HARDBIT_FOR_SDK_EN
//Feature Setting by Controller, Custumer, Flash Type, ...
/*****************************
 *retry_kioxia_bics4hdr_tlc_S17_neutral
 *****************************/
#elif (PS5017_EN && (FLASH_BICS4TLCHDR == FW_CATEGORY_FLASH) && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER))
#define	RETRY_READ_HB_LRU_TABLE_EN						(TRUE)
#define RETRY_HB_LRU_TABLE_LIMIT_NODE_NUM_EN			(TRUE)
#define RETRY_HB_LRU_BY_DIE								(TRUE)
#define RETRY_HB_LRU_IN_DBUF							(TRUE && RETRY_HB_LRU_BY_DIE)
#define	RETRY_INITIAL_READ_EN							(TRUE)
#define	RETRY_READ_HB_SCRATCH_TABLE_EN					(TRUE)
/*****************************
 *retry_kioxia_bics5_tlc_S17_neutral
 *****************************/
#elif (PS5017_EN && (FLASH_BICS5TLC == FW_CATEGORY_FLASH) && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER))
#define	RETRY_READ_HB_LRU_TABLE_EN						(TRUE)
#define RETRY_HB_LRU_TABLE_LIMIT_NODE_NUM_EN			(TRUE)
#define RETRY_HB_LRU_BY_DIE								(TRUE)
#define RETRY_HB_LRU_IN_DBUF							(TRUE && RETRY_HB_LRU_BY_DIE)
#define	RETRY_INITIAL_READ_EN							(TRUE)
#define	RETRY_READ_HB_SCRATCH_TABLE_EN					(TRUE)
#define RETRY_HB_FEEDBACK_CACULATE_READ_LEVEL_TO_LRU	(TRUE)
/*****************************
 *retry_micron_b47r_tlc_E13_neutral
 *****************************/
#elif (PS5013_EN && (FLASH_B47R_TLC == FW_CATEGORY_FLASH) && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER))
#define	RETRY_READ_HB_LRU_TABLE_EN						(TRUE)
#define RETRY_HB_LRU_BY_DIE								(TRUE)

/*****************************
 *retry_micron_b47r_tlc_E13_S17_nicks
 *****************************/
#elif ((PS5013_EN || PS5017_EN) && (FLASH_B47R_TLC == FW_CATEGORY_FLASH) && (CUSTOMER_MICRON_NICKS == FW_CATEGORY_CUSTOMER))
/*****************************
 *retry_micron_n48r_qlc_E13_nicks
 *****************************/
#elif ((PS5013_EN || PS5017_EN) && (FLASH_N48R_QLC == FW_CATEGORY_FLASH) && (CUSTOMER_MICRON_NICKS == FW_CATEGORY_CUSTOMER))
/*****************************
 *retry_hynix_v6_tlc_E13_U17_neutral
 *****************************/     //V7 use V6 Setting Dylan porting V7 RDT HB retry
#elif (((PS5013_EN) || (PS5017_EN)) && ((FLASH_V6_TLC == FW_CATEGORY_FLASH)||(FLASH_V7_TLC == FW_CATEGORY_FLASH)||(FLASH_V8_TLC == FW_CATEGORY_FLASH)|| (FLASH_V7_QLC == FW_CATEGORY_FLASH)|| (FLASH_V5_TLC == FW_CATEGORY_FLASH)) && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER))//Reip Porting 3D-V7 QLC Add//Jeffrey Porting 3D V8 TLC Add
#define	RETRY_READ_HB_LRU_TABLE_EN						(TRUE)
#define RETRY_HB_LRU_BY_DIE								(TRUE)
#define RETRY_HB_LRU_IN_DBUF							(TRUE && RETRY_HB_LRU_BY_DIE)
#define	RETRY_HB_RRT_IN_DBUF							(TRUE)
#define	RETRY_INITIAL_READ_EN							(TRUE)
#define RETRY_HB_FEEDBACK_CACULATE_READ_LEVEL_TO_LRU	(FALSE)
#define RETRY_HB_FEEDBACK_NODE_NUM						(4)
#define HB_RETRY_XLC_RRT_STEP_NUM     					(50 + 1) 	//NowMax: V6 512G = 50 Steps  + 1 for Initial Read 
#define HB_RETRY_SLC_RRT_STEP_NUM						(50 + 1)	//NowMax: V6 512G = 50 Steps + 1 for Initial Read 
//Dylan  Build V6 RDT(add HB flow version)
/*****************************
 *retry_sandisk_bics5_tlc_E13_U17_neutral
 *****************************/
#elif (((PS5013_EN) || (PS5017_EN)) && ((CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_QLC)) && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER))//zerio BICS8 Add//zerio bics6 qlc add
#define	RETRY_READ_HB_LRU_TABLE_EN						(TRUE)
#define RETRY_HB_LRU_BY_DIE								(TRUE)
#define RETRY_HB_LRU_IN_DBUF							(TRUE && RETRY_HB_LRU_BY_DIE)
#define	RETRY_HB_RRT_IN_DBUF							(TRUE)
#define	RETRY_INITIAL_READ_EN							(TRUE)
#define RETRY_HB_FEEDBACK_CACULATE_READ_LEVEL_TO_LRU	(FALSE)
#define RETRY_HB_FEEDBACK_NODE_NUM						(4)
#define HB_RETRY_XLC_RRT_STEP_NUM     					(63 + 1) 	//NowMax: B5 512G = 50 Steps  + 1 for Initial Read 
#define HB_RETRY_SLC_RRT_STEP_NUM						(26 + 1)	//NowMax: B5 512G = 50 Steps + 1 for Initial Read 

#elif ((PS5017_EN) && (FLASH_YMTC_TAS_TLC == FW_CATEGORY_FLASH || FLASH_YMTC_WTS_TLC == FW_CATEGORY_FLASH))
#define	RETRY_READ_HB_LRU_TABLE_EN						(TRUE)
#define RETRY_HB_LRU_BY_DIE								(TRUE)
#define RETRY_HB_LRU_IN_DBUF							(TRUE && RETRY_HB_LRU_BY_DIE)
#define	RETRY_HB_RRT_IN_DBUF							(TRUE)
#define	RETRY_INITIAL_READ_EN							(TRUE)
#define RETRY_HB_FEEDBACK_CACULATE_READ_LEVEL_TO_LRU	(FALSE)
#define RETRY_HB_FEEDBACK_NODE_NUM						(4)
#define HB_RETRY_XLC_RRT_STEP_NUM     					(58 + 1) 	//NowMax: B5 512G = 50 Steps  + 1 for Initial Read 
#define HB_RETRY_SLC_RRT_STEP_NUM						(20 + 1)	//NowMax: B5 512G = 50 Steps + 1 for Initial Read 

#elif ((PS5017_EN) && (FLASH_YMTC_EMS_QLC == FW_CATEGORY_FLASH))//ems mst add--karl
#define	RETRY_READ_HB_LRU_TABLE_EN						(TRUE)
#define RETRY_HB_LRU_BY_DIE								(TRUE)
#define RETRY_HB_LRU_IN_DBUF							(TRUE && RETRY_HB_LRU_BY_DIE)
#define	RETRY_HB_RRT_IN_DBUF							(TRUE)
#define	RETRY_INITIAL_READ_EN							(TRUE)
#define RETRY_HB_FEEDBACK_CACULATE_READ_LEVEL_TO_LRU	(FALSE)
#define RETRY_HB_FEEDBACK_NODE_NUM						(4)
#define HB_RETRY_XLC_RRT_STEP_NUM     					(66 + 1) 	//NowMax: B5 512G = 50 Steps  + 1 for Initial Read
#define HB_RETRY_SLC_RRT_STEP_NUM						(9 + 1)	//NowMax: B5 512G = 50 Steps + 1 for Initial Read

#elif((PS5017_EN) && (FLASH_B47R_TLC == FW_CATEGORY_FLASH))
#define	RETRY_READ_HB_LRU_TABLE_EN						(TRUE)
#define RETRY_HB_LRU_BY_DIE								(TRUE)
#define RETRY_HB_LRU_IN_DBUF							(TRUE && RETRY_HB_LRU_BY_DIE)
#define	RETRY_HB_RRT_IN_DBUF							(TRUE)
#define	RETRY_INITIAL_READ_EN							(TRUE)
#define RETRY_HB_FEEDBACK_CACULATE_READ_LEVEL_TO_LRU	(FALSE)
#define RETRY_HB_FEEDBACK_NODE_NUM						(4)
#define HB_RETRY_XLC_RRT_STEP_NUM     					(81 + 1) 	//NowMax: B5 512G = 24 Steps  + 1 for Initial Read
#define HB_RETRY_SLC_RRT_STEP_NUM						(8 + 1)	//NowMax: B5 512G = 24 Steps + 1 for Initial Read

#elif((PS5017_EN) && (FLASH_N48R_QLC == FW_CATEGORY_FLASH))//zerio n48r add
#define	RETRY_READ_HB_LRU_TABLE_EN						(TRUE)
#define RETRY_HB_LRU_BY_DIE								(TRUE)
#define RETRY_HB_LRU_IN_DBUF							(TRUE && RETRY_HB_LRU_BY_DIE)
#define	RETRY_HB_RRT_IN_DBUF							(TRUE)
#define	RETRY_INITIAL_READ_EN							(TRUE)
#define RETRY_HB_FEEDBACK_CACULATE_READ_LEVEL_TO_LRU	(FALSE)
#define RETRY_HB_FEEDBACK_NODE_NUM						(4)
#define HB_RETRY_XLC_RRT_STEP_NUM     					(25 + 37 + 1) 	//NowMax: 25 Steps ValleyTrack  + 30 Steps ACRR
#define HB_RETRY_SLC_RRT_STEP_NUM						(8 + 1)

#elif((PS5017_EN) && (FLASH_B37R_TLC == FW_CATEGORY_FLASH))
#define	RETRY_READ_HB_LRU_TABLE_EN						(TRUE)
#define RETRY_HB_LRU_BY_DIE								(TRUE)
#define RETRY_HB_LRU_IN_DBUF							(TRUE && RETRY_HB_LRU_BY_DIE)
#define	RETRY_HB_RRT_IN_DBUF							(TRUE)
#define	RETRY_INITIAL_READ_EN							(TRUE)
#define RETRY_HB_FEEDBACK_CACULATE_READ_LEVEL_TO_LRU	(FALSE)
#define RETRY_HB_FEEDBACK_NODE_NUM						(4)
#define HB_RETRY_XLC_RRT_STEP_NUM     					(81 + 1)
#define HB_RETRY_SLC_RRT_STEP_NUM						(8 + 1)

#elif((PS5017_EN) && (FLASH_SAMSUNG_V6_TLC == FW_CATEGORY_FLASH || FLASH_SAMSUNG_V6P_TLC == FW_CATEGORY_FLASH \
		|| FLASH_SAMSUNG_V7_TLC == FW_CATEGORY_FLASH || FLASH_SAMSUNG_V8_TLC == FW_CATEGORY_FLASH || FLASH_SAMSUNG_V5_TLC == FW_CATEGORY_FLASH)) //Samsung v7/v8 mst add--Reip
#define	RETRY_READ_HB_LRU_TABLE_EN						(TRUE)
#define RETRY_HB_LRU_BY_DIE								(TRUE)
#define RETRY_HB_LRU_IN_DBUF							(TRUE && RETRY_HB_LRU_BY_DIE)
#define	RETRY_HB_RRT_IN_DBUF							(TRUE)
#define	RETRY_INITIAL_READ_EN							(TRUE)
#define RETRY_HB_FEEDBACK_CACULATE_READ_LEVEL_TO_LRU	(FALSE)
#define RETRY_HB_FEEDBACK_NODE_NUM						(4)
#define HB_RETRY_XLC_RRT_STEP_NUM     					(33 + 1)
#define HB_RETRY_SLC_RRT_STEP_NUM						(33 + 1)

#elif((PS5017_EN) && (FLASH_INTEL_N38A_QLC == FW_CATEGORY_FLASH))
#define	RETRY_READ_HB_LRU_TABLE_EN						(TRUE)
#define RETRY_HB_LRU_BY_DIE								(TRUE)
#define RETRY_HB_LRU_IN_DBUF							(TRUE && RETRY_HB_LRU_BY_DIE)
#define	RETRY_HB_RRT_IN_DBUF							(TRUE)
#define	RETRY_INITIAL_READ_EN							(TRUE)
#define RETRY_HB_FEEDBACK_CACULATE_READ_LEVEL_TO_LRU	(FALSE)
#define RETRY_HB_FEEDBACK_NODE_NUM						(4)
#define HB_RETRY_XLC_RRT_STEP_NUM     					(15 + 1)
#define HB_RETRY_SLC_RRT_STEP_NUM						(15 + 1)

/*****************************
 *Not Support
 *****************************/
#else /* RETRY_HARDBIT_FOR_SDK_EN */
#endif /* RETRY_HARDBIT_FOR_SDK_EN */


#if (MST_MODE_EN/*(CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC)\
		||(CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_TLC)\
		||(CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_QLC)\
		||(CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_TLC)\
		||(CONFIG_FLASH_TYPE == FLASH_TYPE_YMTC_3D_QLC)\
		||(CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC)\
		||(CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC)\
		||(CONFIG_FLASH_TYPE == FLASH_TYPE_SAMSUNG_3D_TLC)*/)//ems mst add--karl//Duson Porting BICS5 Add //Reip Porting 3D-V7 QLC Add//zerio bics6 qlc add
#define	RETRY_INITIAL_READ_EN                       (TRUE)
#define	RETRY_READ_HB_SCRATCH_TABLE_EN				(FALSE)
#define	RETRY_READ_HB_LRU_GROUP						(FALSE)
#define	RETRY_READ_MICRON_HB_LRU_EN                 (FALSE)
#endif
//Feature Default Setting (All Off)
#ifndef RETRY_READ_HB_LRU_TABLE_EN
#define	RETRY_READ_HB_LRU_TABLE_EN						(FALSE)
#endif /* RETRY_READ_HB_LRU_TABLE_EN */
#ifndef RETRY_HB_LRU_TABLE_LIMIT_NODE_NUM_EN
#define	RETRY_HB_LRU_TABLE_LIMIT_NODE_NUM_EN			(FALSE)
#endif /* RETRY_HB_LRU_TABLE_LIMIT_NODE_NUM_EN */
#ifndef RETRY_HB_LRU_BY_DIE
// Only Enable RETRY_HB_LRU_BY_DIE will place in BTCM
#define	RETRY_HB_LRU_BY_DIE								(FALSE)
#endif /* RETRY_HB_LRU_BY_DIE */
#ifndef RETRY_HB_LRU_IN_DBUF
// Enable Both RETRY_HB_LRU_BY_DIE RETRY_HB_LRU_IN_DBUF will Use Static DBUF
#define	RETRY_HB_LRU_IN_DBUF							(FALSE && RETRY_HB_LRU_BY_DIE)
#endif /* RETRY_HB_LRU_IN_DBUF */
#ifndef RETRY_INITIAL_READ_EN
#define	RETRY_INITIAL_READ_EN							(FALSE)
#endif /* RETRY_INITIAL_READ_EN */
#ifndef RETRY_READ_HB_SCRATCH_TABLE_EN
#define	RETRY_READ_HB_SCRATCH_TABLE_EN					(FALSE)
#endif /* RETRY_READ_HB_SCRATCH_TABLE_EN */
#ifndef RETRY_HB_FEEDBACK_CACULATE_READ_LEVEL_TO_LRU
//Enable RETRY_HB_LRU_IN_DBUF if by die
#define	RETRY_HB_FEEDBACK_CACULATE_READ_LEVEL_TO_LRU	(FALSE)
#define RETRY_HB_FEEDBACK_NODE_NUM						(0)
#endif /* RETRY_HB_FEEDBACK_CACULATE_READ_LEVEL_TO_LRU */
#ifndef RETRY_HB_DYNAMIC_READ_RETRY_TABLE
//Enable RETRY_HB_LRU_IN_DBUF if by die
#define	RETRY_HB_DYNAMIC_READ_RETRY_TABLE				(FALSE)
#endif /* RETRY_HB_DYNAMIC_READ_RETRY_TABLE */
#ifndef RETRY_HB_FEEDBACK_NODE_SLC_FEEDBACK_EN
//Enable RETRY_HB_LRU_IN_DBUF if by die
#define	RETRY_HB_FEEDBACK_NODE_SLC_FEEDBACK_EN			(FALSE)
#endif /* RETRY_HB_FEEDBACK_NODE_SLC_FEEDBACK_EN */
#ifndef RETRY_HB_FEEDBACK_NODE_CHECK_VALID_EN// Dylan build V6 RDT (add HB flow)
#define	RETRY_HB_FEEDBACK_NODE_CHECK_VALID_EN			(RETRY_HB_FEEDBACK_CACULATE_READ_LEVEL_TO_LRU && (MICRON_FSP_EN || (FALSE == RETRY_HB_DYNAMIC_READ_RETRY_TABLE && FALSE == RETRY_HB_LRU_TABLE_LIMIT_NODE_NUM_EN)))
#endif /* RETRY_HB_FEEDBACK_NODE_CHECK_VALID_EN */


//Retry

#define CALLBACK_FUNC_IN_COMMON_CODEBANK	(FALSE)
#define RETRY_DEBUG_UART_EN					(FALSE)

#define RETRY_CHECK_ALL_ZERO_RESULT_EN		(TRUE && (!NES_EN) && (!NES_GEN2_EN) && (!BURNER_MODE_EN))
#define RETRY_ALL_BIN_EN					(FALSE)

#define RETRY__ENABLE_HB_FLOW				(TRUE)
#if (BURNER_MODE_EN)
#define RETRY_BURNER_DEBUG_SB				(FALSE)
#define RETRY__ENABLE_SB_FLOW				(TRUE && RETRY_BURNER_DEBUG_SB)
#else /*(BURNER_MODE_EN)*/
#define RETRY_BURNER_DEBUG_SB				(FALSE)
#define RETRY__ENABLE_SB_FLOW				(TRUE && (!RDT_MODE_EN))
#endif /*(BURNER_MODE_EN)*/
#define RETRY__ENABLE_RS_FLOW				(TRUE && (!BURNER_MODE_EN) && (!RDT_MODE_EN) && (!NCS_EN) && (RAIDECC_ENCODE_EN))    // NCS won't test RaidECC_Decode Disable for code over size

#define	HB_GEN_FAIL_EN						(FALSE)//Dylan verify HB Flow
#define INTO_SB_EN							(FALSE)
#define INTO_RS_EN							(FALSE)
#define INTO_SB_PERCENTAGE					(15)
#define INTO_RS_PERCENTAGE					(15)
#define RETRY_SKIP_SLC_INTO_SB				(INTO_SB_EN && (FALSE == RETRY_SB_SLC_SB_EN))	//Only for Phison Decoding Flow,To avoid SLC pages that SB not handling , RaidECC will also be skipped

#define	RETRY_FORCE_HB_TO_MTP_STALL			(FALSE)
#define	RETRY_STALL_CACHE_PROGRAM_UART		(FALSE)	//cop0?

//HB
#define HB_READ_OFFSET_VALUES_RESET_EN		(FALSE)
#define HB_DEBUG_UART_EN					((FALSE && (!BURNER_MODE_EN)))
#define HB_MAX_SET_FEATURE_FAIL_EXIT_EN		(FALSE) // TRUE : exit HB,  FALSE : read directly

#define RETRY_SB_ENABLE_DEBUG_MESSAGE		(FALSE)
#define RETRY_RS_ENABLE_DEBUG_MESSAGE		(FALSE)
#define RETRY_RAIDECC_DECODE_ENABLE_DECODE_FAIL_PAGE	(FALSE)

#if RETRY_HARDBIT_FOR_SDK_EN
#else /* RETRY_HARDBIT_FOR_SDK_EN */
#define HB_LRU_TABLE_DEBUG_UART						(FALSE && (RETRY_READ_HB_LRU_TABLE_EN))
#define RETRY_HB_LRU_ADD_FREE_BUF_TO_NON_SLC_NODE_NUM	(FALSE && RETRY_HB_LRU_IN_DBUF)
#define	HB_TRAPPING_SET_ENABLE						(TRUE)
#endif /* RETRY_HARDBIT_FOR_SDK_EN */

//SB

#define RETRY_MADE_SB__WITH_DEFAULT_LLR__RETRY_FAIL							(FALSE)
#define RETRY_MADE_SB__HB_WITH_OPT_LEVEL__RETRY_FAIL						(FALSE)
#define RETRY_MADE_SB__SB_WITH_OPT_LEVEL__RETRY_FAIL						(FALSE)
#define RETRY_MADE_SB__SB_WITH_OPT_LEVEL_AND_APT_LLR__RETRY_FAIL			(FALSE)
#define RETRY_MADE_SB__DSP2__RETRY_FAIL										(FALSE)
#define	RETRY_MADE_SB__SB_WITH_ADT_LLR_WITH_OPT_READ_LEVEL_FAIL				(FALSE)
#define RETRY_MADE_SB__SB_WITH_OPT_LEVEL__AFTER_RAIDECC__RETRY_FAIL			(FALSE)

#define	RETRY_MADE_SB__HB_WITH_COARSE_TUNING_LEVEL__RETRY_FAIL							(FALSE)
#define	RETRY_MADE_SB__SB_WITH_CORNER_CASE_FIX_LLR_WITH_COARSE_TUNING_LEVEL_RETRY_FAIL	(FALSE)
#define	RETRY_MADE_SB__SB_WITH_ADT_LLR_WITH_COARSE_TUNING_LEVEL__RETRY_FAIL				(FALSE)
#define	RETRY_MADE_SB__SB_WITH_CORNER_CASE_FIX_LLR_WITH_OPT_LEVEL_RETRY_FAIL			(FALSE)
#define	RETRY_MADE_SB__SB_WITH_ADT_LLR_WITH_OPT_LEVEL__RETRY_FAIL						(FALSE)
#define	RETRY_MADE_SB__SB_WITH_DEFAULT_LLR_WITH_OPT_READ_LEVEL_FAIL						(FALSE)

#define RETRY_SB_ENABLE_DEBUG_CMP_WITH_HB_DATA		(FALSE && RETRY__ENABLE_HB_FLOW)
#define SB_DUMP_DATA_DEBUG_EN						(FALSE)
#define RETRY_READ_SB__DIRECTLY_GET_CORRECT_DATA_FROM_ERROR_PAGE	       (FALSE)


#if RETRY_SOFTBITT_FOR_SDK_EN
#else/*RETRY_SOFTBITT_FOR_SDK_EN*/
#define	SB_TRAPPING_SET_ENABLE			(TRUE)
#endif/*RETRY_SOFTBITT_FOR_SDK_EN*/

//RS

//SBRAID  Can use this define to decide whether continuing to rescue Normal pages after Target page is rescued
#define	RETRY_SBRAID_CONTINUE_PROCESSING_OTHER_FAIL_PAGE		(TRUE)

//SBRAID Pre-Allocate buffer
#define	RETRY_SBRAID_PRE_ALLOCATE_BUF			(TRUE)

#define RETRY_ENABLE_GEN_FAIL_SOFTBITRAIDECC	(FALSE)
#define	GEN_NORMAL_FAIL_MAX_CNT					(2)
#define RETRY_TURBORAIN_DEBUG					(FALSE)

#define RETRY_READ_RS__ENABLE_GET_PARITY_DATA_FROM_ON_ENCODING_DATA		(TRUE)
#define RETRY_READ_RS__ENABLE_SB_DECODE_AFTER_RS						(FALSE || RETRY_SBRAID_EN)
#define RETRY_READ_RS__CHECKMODE										(FALSE)
#define RETRY_READ_RS__SKIP_DECODE_ERROR_FRAME							(TRUE && RETRY_READ_RS__CHECKMODE)
#define RETRY_READ_PSEUDO_FAIL_PAGE_TO_CMP_WITH_DATA_IN_RS_PBUF			(TRUE && RETRY_READ_RS__CHECKMODE)
#define RETRY_READ_RS__DIRECTLY_GET_CORRECT_DATA_FROM_ERROR_PAGE		(TRUE && (RETRY_READ_RS__CHECKMODE || (FALSE == RETRY__ENABLE_HB_FLOW)))
#define RETRY_READ_RS_CMP_DATA_IN_RS_PBUF_AND_TEMP_BUF					(FALSE && RETRY_READ_RS__CHECKMODE)

#endif /* _RETRY_SETUP_H_ */
