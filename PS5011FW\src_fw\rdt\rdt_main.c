/*
 * rdt_main.c
 *
 *  Created on: 2020骞�2鏈�6鏃�
 *      Author: user
 */

#include <string.h>
#include <stdlib.h>
#include "setup.h"
#include "hal/sys/api/clk/clk.h"
#include "rdt_api.h"
#include "burner/Burner.h"
#include "host_handler/hostevt_errhdl_api.h"
#include "host_handler/host_management_api.h"
/* ------ RDT_RECORD_SCAN_WINDOW_LOG ------ */
#include "vuc/VUC_ScanFlashWindowParameter.h"
#include "vuc/VUC_ReadScanFlashWindow.h"
/* ------------ RDT_RUN_ONLINE ------------ */
#include "vuc/VUC_EraseAll.h"
#include "host/VUC_handler.h"
#include "hal/nvme/nvme.h"

#if (RDT_MODE_EN)
#if (!RDT_RUN_ONLINE)
RDT_API_STRUCT gRdtApiStruct;
FPL_API_STRUCT gC1Cop0FplApiStruct;
U8 gubCheckUSBLinkForRDT = 0;
U8 ERL_Bypass_Record_HB_RETRY_INFO[16][16][4]; //[Global CE][DIE][Plane];MAX_Globa CE = 16, MAX DIE = 16,Plane=4
U32 gFlashTestStartTime;

void RDT_Main(void)
{
	//variable initial to 0
	//M_UART(RDT_TEST_,"\n\r RDT Check time: flow [RDT] START ");
	//M_UART(RDT_TEST_,"\n\r RDT Check time: flow [RDT] START ");
	//M_UART(RDT_TEST_,"\n\r sizeof(FLH_ENV_STRUCT_t) = %d", sizeof(FLH_ENV_STRUCT_t));
	memset((void *)(&gC1Cop0FplApiStruct), 0x00, sizeof(FPL_API_STRUCT));
	memset((void *)(&gRdtApiStruct), 0x00, sizeof(RDT_API_STRUCT));
	memset((void *)(&ERL_Bypass_Record_HB_RETRY_INFO), 0x00, sizeof(ERL_Bypass_Record_HB_RETRY_INFO));
	RDT_API_STRUCT_PTR rdt = &gRdtApiStruct;
	rdt->fpl = &gC1Cop0FplApiStruct;
	//M_UART(RDT_TEST_,"\nRDT_FORCE_FAST_TEST = %d", RDT_FORCE_FAST_TEST);
	//M_UART(RDT_TEST_,"\nClockGetIPClockMHz(NULL, CLOCK_IP_SYS) = %d", ClockGetIPClockMHz(NULL, CLOCK_IP_SYS));
	//M_UART(RDT_TEST_,"\nClockGetIPClockMHz(NULL, CLOCK_IP_CPU) = %d", ClockGetIPClockMHz(NULL, CLOCK_IP_CPU));

	//M_UART(RDT_TEST_,"\ngFlhEnv.ubTargetFlashClock = %b", gFlhEnv.ubTargetFlashClock);
	//M_UART(RDT_TEST_,"\ngFlhEnv.ubCurrentFlashClock = %b", gFlhEnv.ubCurrentFlashClock);
	//M_UART(RDT_TEST_,"\ngFlhEnv.ubCurrentInterface = %b", gFlhEnv.ubCurrentInterface);
	//M_UART(RDT_TEST_,"\ngFlhEnv.ubTargetInterface = %b", gFlhEnv.ubTargetInterface);
	//M_UART(RDT_TEST_,"\ngFlhEnv.ubDefaultInterface = %b", gFlhEnv.ubDefaultInterface);
	//M_UART(RDT_TEST_,"\nBURNER_STATIC_AREA_BASE = %x", BURNER_STATIC_AREA_BASE);
	//M_UART(RDT_TEST_,"\ngpIDPage->ubIDB_TargetFlashClock:%b", gpIDPage->ubIDB_TargetFlashClock);
	//M_UART(RDT_TEST_,"\n M_USB_GET_ENUM_SPD = %x", M_USB_GET_ENUM_SPD());
	M_UART(RDT_TEST_, "\ngFlhEnv.ubTargetFlashClock = %d", gFlhEnv.ubTargetFlashClock);
	M_UART(RDT_TEST_, "\ngFlhEnv.ubCurrentFlashClock = %d", gFlhEnv.ubCurrentFlashClock);
	rdt_api_basic_init(rdt);
	//link or not
	if ( (rdt_link_wait(rdt) == HAVE_LINK) ) {
		//rdt->next_state = PRL_STATE_WAIT_VUC;
		rdt->rdt_test_flow_index = RDT_TEST_FLOW_FINISH_INDEX;
	}
	else {
		if (!rdt_api_test_preparation(rdt)) {
			rdt->rdt_test_flow_index = RDT_TEST_FLOW_FINISH_INDEX; //stop test
		}
#if RDT_SCAN_WINDOW_TEST
		//rdt->next_state = PRL_STATE_WAIT_VUC;
		M_UART(RDT_TEST_, "win addr:%x", rdt->log[RDT_LOG_SCAN_WIN].log_block_addr[0]);
		M_UART(RDT_TEST_, "win block:%d", PCA_2_BLOCK(PCA_RULE(1), rdt->log[RDT_LOG_SCAN_WIN].log_block_addr[0]));
		M_UART(RDT_TEST_, "win plane:%d", PCA_2_PLANE(PCA_RULE(1), rdt->log[RDT_LOG_SCAN_WIN].log_block_addr[0]));
		U64 final_time = 60 * 1000 * 60 + rdt_api_rtt_get_timer_count();
		U16 scan_window_count = 0;
		gubLEDState = RDT_NAND_TEST;
		while (final_time > rdt_api_rtt_get_timer_count()) {
			scan_window_count = scan_window_count == 65535 ? 0 : scan_window_count;
			M_UART(RDT_TEST_, "\nscan window : %d", scan_window_count);
			rdt_api_ScanWindowFlow(rdt, FALSE);
		}
		rdt_api_ScanWindowFlow(rdt, TRUE);
		if (gubLEDState == RDT_NAND_TEST) {
			gubLEDState = RDT_TEST_FINISH;
		}
		return;
#endif
#if RDT_DEBUG
		rdt_flash_retry_cmd_verify(rdt);
#endif
	}
#if ENABLE_RDT_DELEGATE_CQ_IC_PATTERN
	M_UART(RDT_TEST_, "\ngbz_task.ubTestEnable = %d", gbz_task.ubTestEnable);
#endif
	U8 LED_ERROR_SET = 0;
	if (rdt->rdt_test_flow_index < RDT_TEST_FLOW_FINISH_INDEX) {
		if ( (gpIDPage->ubTotalCe > 0) && (gpIDPage->ubTotalCe != (gFlhEnv.ubCENumber * gFlhEnv.ubLUNperTarget)) ) {
			rdt->rdt_err = ERR_CE_NUM_COMPARE_FAIL;
			rdt_api_rml_log_add(rdt, RML_IMARK_RDT_TEST_START);
			rdt_api_write_log_to_flash(rdt, RDT_LOG_RDT_TESTMARK);
			gubLEDState = RDT_TEST_ERROR;
			rdt->rdt_test_flow_index = RDT_TEST_FLOW_FINISH_INDEX;//stop test
		}
	}

	while (rdt->rdt_test_flow_index < RDT_TEST_FLOW_FINISH_INDEX) {
		//rdt->current_state = rdt->next_state;
		U8 error_happened_force_to_save_log = 0;

		switch (rdt->rdt_test_flow[rdt->rdt_test_flow_index]) {
		case PRL_STATE_CHECK_TEMPERATURE:
			M_UART(RDT_TEST_, "\n RDT Check time: flow [CHECK_TEMPERATURE] Start");
			rdt_thermal_detect(rdt, 0, 0);
			if (rdt->rdt_err) {
				LED_ERROR_SET = 1;
				error_happened_force_to_save_log = 1;
			}
			M_UART(RDT_TEST_, "\n RDT Check time: flow [CHECK_TEMPERATURE] finish");
			break;

		case PRL_STATE_START_TEST:
			rdt_api_rml_log_add(rdt, RML_IMARK_RDT_TEST_START);
			rdt_api_write_log_to_flash(rdt, RDT_LOG_RDT_TESTMARK);
			rdt_api_prl_program(rdt, rdt->rdt_test_flow_index);//change to store flow index
			break;

		case PRL_STATE_NO_ACTION:
			//stop test flow
			if (!LED_ERROR_SET) {
				gubLEDState = RDT_TEST_FINISH;
			}
			//gubLEDState = RDT_TEST_FINISH;

#if !ENABLE_RDT_NO_LOG
#if ENABLE_PH
			rdt_api_choose_PH_blk(rdt);
#endif
			rdt_api_exchange_dbt_to_bbm(rdt);
#endif

			rdt->rdt_test_flow_index = RDT_TEST_FLOW_FINISH_INDEX;//stop test
			rdt_api_prl_program(rdt, rdt->rdt_test_flow_index);//change to store flow index

			rdt_api_rml_log_add(rdt, RML_IMARK_RDT_TEST_END);
			rdt_api_write_log_to_flash(rdt, RDT_LOG_RDT_TESTMARK);

			//rdt_api_erase_by_dbt(rdt);
			rdt_api_parse_all_log(rdt);
			M_UART(RDT_TEST_, "\n RDT Check time: flow [RDT] Finish");
			break;

		case PRL_STATE_SRAM_TEST:
			gubLEDState = RDT_SRAM_TEST;
			M_UART(RDT_TEST_, "\n RDT Check time: flow [SRAM_TEST] Start");
			if (!rdt_api_atcm_sram_test(rdt) ||  !rdt_api_sram_wr_test(rdt) ) {
				error_happened_force_to_save_log = 1;
			}
			M_UART(RDT_TEST_, "\n current core power %b \n", rdt_api_read_voltage_setting() >> COREPOWER_SEL_SHIFT);
			M_UART(RDT_TEST_, "\n RDT Check time: flow [SRAM_TEST] finish ");
			break;

		case PRL_STATE_TLC_FLASH_TEST:
			gubLEDState = RDT_NAND_TEST;
			M_UART(RDT_TEST_, "\n RDT Check time: flow [TLC_FLASH_TEST] Start");
			rdt_init_eot_setting(rdt, rdt->param.tlc_ecc_bit_threshold);
			if ( !rdt_api_general_flash_test(rdt, 0) ) {
				LED_ERROR_SET = 1;
				error_happened_force_to_save_log = 1;
			}
			M_UART(RDT_TEST_, "\n RDT Check time: flow [TLC_FLASH_TEST] finish");
			break;

		case PRL_STATE_SLC_FLASH_TEST:
			gubLEDState = RDT_NAND_TEST;
			M_UART(RDT_TEST_, "\n RDT Check time: flow [SLC_FLASH_TEST] Start ");
			gFlashTestStartTime = rdt_api_rtt_get_timer_count();
			rdt_init_eot_setting(rdt, rdt->param.slc_ecc_bit_threshold);
			if ( !rdt_api_general_flash_test(rdt, 1) ) {
				LED_ERROR_SET = 1;
				error_happened_force_to_save_log = 1;
			}
			M_UART(RDT_TEST_, "\n RDT Check time: flow [SLC_FLASH_TEST] finish ");
			break;

		case PRL_STATE_IC_TEST:
			gubLEDState = RDT_IC_TEST;
			M_UART(RDT_TEST_, "\n\r RDT Check time: flow [IC_TEST] Start ");
			if ( !rdt_api_ic_pattern_test(rdt) ) {
				error_happened_force_to_save_log = 1;
				break;
			}
			M_UART(RDT_TEST_, "\n current core power %b \n", rdt_api_read_voltage_setting() >> COREPOWER_SEL_SHIFT);
			M_UART(RDT_TEST_, "\n\r RDT Check time: flow [IC_TEST] finish ");
			break;

		case PRL_STATE_SCAN_WINDOW_1_TEST:
#if RDT_RECORD_SCAN_WINDOW_LOG
			M_UART(RDT_TEST_, "\n RDT Check time: flow [SCAN_WINDOW_1_TEST] Start");
			gubLEDState = RDT_NAND_TEST;
			rdt_api_rml_log_add(rdt, RML_IMARK_WIN_TEST_SLC_START);
			rdt_api_write_log_to_flash(rdt, RDT_LOG_RDT_TESTMARK);
			rdt_api_ScanWindowFlow(rdt, TRUE);
			rdt_api_rml_log_add(rdt, RML_IMARK_WIN_TEST_SLC_END);
			rdt_api_write_log_to_flash(rdt, RDT_LOG_RDT_TESTMARK);
			M_UART(RDT_TEST_, "\n RDT Check time: flow [SCAN_WINDOW_1_TEST] Finish");
#endif
			break;
		case PRL_STATE_SCAN_WINDOW_2_TEST:
#if RDT_RECORD_SCAN_WINDOW_LOG
			M_UART(RDT_TEST_, "\n RDT Check time: flow [SCAN_WINDOW_2_TEST] Start");
			gubLEDState = RDT_NAND_TEST;
			rdt_api_rml_log_add(rdt, RML_IMARK_WIN_TEST_TLC_START);
			rdt_api_write_log_to_flash(rdt, RDT_LOG_RDT_TESTMARK);
			rdt_api_ScanWindowFlow(rdt, TRUE);
			rdt_api_rml_log_add(rdt, RML_IMARK_WIN_TEST_TLC_END);
			rdt_api_write_log_to_flash(rdt, RDT_LOG_RDT_TESTMARK);
			M_UART(RDT_TEST_, "\n RDT Check time: flow [SCAN_WINDOW_2_TEST] Finish");
#endif
			break;

		case PRL_STATE_SLC_RDY_TIME_TEST:
#if (RDT_RDY_TIME_TEST)	//For TPROG, Reip
			M_UART(RDT_TEST_, "\n RDT Check time: flow [SLC_RDY_TIME_TEST] Start ");
			gubLEDState = RDT_NAND_TEST;
			rdt_api_rml_log_add(rdt, RML_IMARK_RDY_TEST_SLC_START);
			rdt_api_write_log_to_flash(rdt, RDT_LOG_RDT_TESTMARK);
			rdt_flash_rdy_time_test_process(rdt, TRUE);
			rdt_api_rml_log_add(rdt, RML_IMARK_RDY_TEST_SLC_END);
			rdt_api_write_log_to_flash(rdt, RDT_LOG_RDT_TESTMARK);
			M_UART(RDT_TEST_, "\n RDT Check time: flow [SLC_RDY_TIME_TEST] Finish ");
#endif
			break;

		case PRL_STATE_TLC_RDY_TIME_TEST:
#if (RDT_RDY_TIME_TEST)	//For TPROG, Reip
			M_UART(RDT_TEST_, "\n RDT Check time: flow [TLC_RDY_TIME_TEST] Start ");
			gubLEDState = RDT_NAND_TEST;
			rdt_api_rml_log_add(rdt, RML_IMARK_RDY_TEST_TLC_START);
			rdt_api_write_log_to_flash(rdt, RDT_LOG_RDT_TESTMARK);
			rdt_flash_rdy_time_test_process(rdt, FALSE);
			rdt_api_rml_log_add(rdt, RML_IMARK_RDY_TEST_TLC_END);
			rdt_api_write_log_to_flash(rdt, RDT_LOG_RDT_TESTMARK);
			M_UART(RDT_TEST_, "\n RDT Check time: flow [TLC_RDY_TIME_TEST] Finish ");
#endif
			break;

		case PRL_STATE_RETRY_VERIFY_TEST:
#if RDT_RETRY_CMD_VERIFY
			M_UART(RDT_TEST_, "\n RDT Check time: flow [RETRY_VERIFY_TEST] Start");
			gubLEDState = RDT_NAND_TEST;
			rdt_api_rml_log_add(rdt, RML_IMARK_RETRY_VERIFY_TEST_START);
			rdt_api_write_log_to_flash(rdt, RDT_LOG_RDT_TESTMARK);
			rdt_flash_retry_cmd_verify(rdt);
			rdt_api_rml_log_add(rdt, RML_IMARK_RETRY_VERIFY_TEST_END);
			rdt_api_write_log_to_flash(rdt, RDT_LOG_RDT_TESTMARK);
			M_UART(RDT_TEST_, "\n RDT Check time: flow [RETRY_VERIFY_TEST] Finish");
#endif
			break;
		case PRL_STATE_ENTER_LOW_POWER:
			rdt_enter_low_power_mode(rdt);
			break;

		case PRL_STATE_ENTER_NORMAL_POWER:
			rdt_enter_normal_power_mode(rdt);
			break;

		case PRL_STATE_SAVE_LOG:
			//Led status change
#if !ENABLE_RDT_NO_LOG
			rdt_api_save_log(rdt);
#endif
			break;

		default:
			break;
		}

		if (error_happened_force_to_save_log) {
			gubLEDState = RDT_TEST_ERROR;
			//error happened, force to save log
			rdt->rdt_test_flow[rdt->rdt_test_flow_index + 1] = PRL_STATE_SAVE_LOG;
			rdt->rdt_test_flow[rdt->rdt_test_flow_index + 2] = PRL_STATE_NO_ACTION;
		}
		rdt->rdt_test_flow_index += (rdt->rdt_test_flow_index < RDT_TEST_FLOW_FINISH_INDEX) ? 1 : 0; //avoid overflow
	}

	//M_UART(RDT_TEST_, "\ngoto Burner_Daemon");


	if ( (rdt->rdt_err != 0) || (rdt->err_bitmap.fields.rdt_init_flow_fail != 0) ) {
		M_UART(RDT_TEST_, "\nTest result = FAIL");
	}
	//else {
	//	M_UART(RDT_TEST_, "\nTest result = PASS");
	//}
	M_UART(RDT_TEST_, "\nRDT wait VUC\n");

#if (HOST_MODE == SATA)
	if (FALSE == M_SATA_CHECK_COMRST_D2H_SEND()) {
		gSATAHostEvt.btHRST = TRUE;
	}
	Burner_Daemon();
#elif (HOST_MODE == USB)
	while (1) {
		USBGetUSBProtocol();

		FTLSyncCmdHandler_USB(&gUSBCUrrentCMD, (U32)M_DB_GET_RD_CNT(DB_APU_CMD_CQ));

		if (FALSE == M_USB_CHK_HOST_EVENT()) {
			if (M_DB_GET_RD_CNT(DB_APU_TD_CQ)) {
				USBTDHandler();
			}
		}

		//warm reset handler (before preformat)
		if ((M_CHECK_HOST_EVENT() & ERROR_HANDLE_APU_WRITE_ERROR_BIT) || M_USB_CHK_HOST_EVENT() || M_USB_CHECK_HOST_EVENT_LPM() || M_USB_CHK_HOST_EVENT_L1()) {
			HostEventHandler();
			M_UART(USB_DEBUG_, "\nHostEventHandler()\n");
		}


		if (gHostManager.ubType && (HOST_STATE_FW_DOING != gHostManager.ubState)) {
			HostDelegateMgr();
			M_UART(USB_DEBUG_, "\bHostDelegateMgr()\n");
		}

	}
#endif
}
#endif
#endif /* RDT_MODE_EN */
