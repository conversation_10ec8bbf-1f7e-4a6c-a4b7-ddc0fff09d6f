/*
 * ftl_table.h
 */
#ifndef FTL_TABLE_H_
#define FTL_TABLE_H_

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "ftl/ftl_table_api.h"

/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */
#define FTL_TRIM_CONDITIONAL_BMP_MAX_PMD_NUM_IN_HMB		(16)

// for Mutex Lock gubST3GCMutexLock
#define TABLE_UPDATE_LOCK_BIT			BIT0
#define TABLE_GC_LOCK_BIT				BIT1
#define TABLE_UPDATE_PTE_LOG_DEFAULT	(0xFFFF)
#define TABLE_GC_GCPTE_VALID_PMD_NUM		(1)
#define TABLE_GC_UPDATE_PMD_DOING_JOB_NUM	(1)
#define LOG_PMD_NUM_IN_A_PMDBMP_REG			(5)
#define COP1_ST3_CR_PMD_TBL_BITMAP_NUM		(32)

#define LockMode				(0)
#define UnLockMode				(1)

#define DTLOG_PTE_SIZE			(1024)
#define DTLOG_PMD_SIZE			(1024)
#define DTLOG_PTE_SIZE_IN_HMB			(4096)
#define DTLOG_PMD_SIZE_IN_HMB			(4096)
#define DTLOG_ALIGN_U32B_SIZE        (32 / 8)
#define DTLOG_ALIGN_U32B_MASK        (DTLOG_ALIGN_U32B_SIZE - 1)
#define FTL_TABLE_ENTRY_ALIGN_32B_SIZE		(SIZE_32B / SIZE_4B)

#define MIN_CACHE_YOU_CAN_USE		(32)
#define DUMMY_LOG_VALUE   (0xFFFFFFFFFFFFFFFF)
#if (DEBUG_CORNER_TEST_66_EN)
#define THRESHOLD_START_TABLE_GC	(1)
#else
#define THRESHOLD_START_TABLE_GC	(0)
#endif
#define THRESHOLD_READY_TABLE_GC	(1)
#define P4K_CNT_FOR_PROG_PMDBMP	(1)
#define PMDBMP_SIZE	(PMD_BITMAP_SIZE)
#define MIN_NUM_FOR_UPDATE_PMD	(1)
#define PLANE_NUM_PROG_PMDBMP_RS	(2)
#define NEED_QUOTA_TYPE_TABLE_GC			(0)
#define NEED_QUOTA_TYPE_TABLE_UPDATE	(1)
#define RESERVE_CACHE_NUM_FOR_RETRY		(7)
#define TABLE_UPDATE_MAX_NUMBER_TABLE_PROGRAM_4K_IN_SHARE_TAG	(127)

//ST1
#define DSA_RELEASE_SLOT_SIZE	(1024)
#define DSA_RELEASE_SLOT_SIZE_LOG	(10)
#define DSA_RELEASE_SLOT_NUM		(8)
#define Default_PTELOG_PTR		(0xFFFF)
#define Default_GC_INSERT_VB	(0xFFFF)
#define DSA_RELEASE_BMP_DEFAULT	(0)
#define U8B_ALIGN_U32B_MASK		(32 / 8 - 1)

//Table GC
#define TABLE_GC_COPYBUF_4KNUM	(16)
#define TABLE_GC_LOG_4KNUM		(1)
#define TABLE_GC_PMDBMP_SIZE	(PMD_BITMAP_SIZE)
#define TABLE_GC_LOG_ENTRY		((TABLE_GC_LOG_4KNUM) * 4096 / 8)
#define TABLE_GC_MAX_VB_NUM		(2)
#define GCPTE		(0)
#define GCPMD		(1)

//Table Program Manager
#define TABLE_PROGRAM_SQ_CNT	(17 + COP0_UNLOCK_SQ_NUM)
#define TABLE_PROGRAM_CALLER_TABLE_UPDATE	(0)
#define TABLE_PROGRAM_CALLER_TABLE_GC		(1)
#define TABLE_PROGRAM_CALLER_FLUSH_PGD_VC	(2)

//Flush PGD & VC
#define INSERT_DSA	(0)
#define INSERT_GCSA	(1)
#define TABLE_GC	(2)

#define TABLE_FLUSH_THRESHOLD_SIZE_IN_4K_IN_HMB              (32)

#define TABLE_TRIM_COND_GATHRE_LOG_SIZE_1K_NUM	(2)

// Build Valid Count Table
#define BUILD_VC_SPECIAL_UNIT_LB_OFFSET		(4)
#define BUILD_VC_SPECIAL_UNIT_BMP_SIZE		(512)
#define BUILD_VC_PMD_BUF_LB_OFFSET			(5)
#define BUILD_VC_PTE_BUF_LB_OFFSET			(6)

/*
 * ---------------------------------------------------------------------------------------------------
 *  data types
 * ---------------------------------------------------------------------------------------------------
 */
typedef enum {
	TABLE_EVENT_QUEUE_NULL,
	TABLE_EVENT_QUEUE_PENDING,
	TABLE_EVENT_QUEUE_PAUSING,
	TABLE_EVENT_QUEUE_NUM,
} TableEventQueueTypeEnum_t;

typedef enum {
	TABLE_SCHEDULE_RESULT_RETURN,
	TABLE_SCHEDULE_RESULT_CONTINUE
} TableScheduleResultEnum_t;

#define INVALID_CHANGE_UNIT_INDEX	(0xFFFF) // 0xffff as invalid means table unit didn't changed

typedef struct {
	U8 ubMode;								// 1: PTE, 0: PMD
	U8 ubUpdatePMDPGDDone;					// for PTE: update PMD done, for PMD: update PGD done
	TableProgramStateEnum_t ubDoingState;	// current state of state machine

	U16 uwDTLogCnt;
	U16 uwDTLogResumeIndex;
	U16 uwChangeUnitIndex;		// 0xffff as invalid means table unit didn't changed
	U16 uwCop0TagId;						// Tag ID of COP0 for Program PTE/PMD

	// for debug
	U32	ulRound;
} FTLTableProgram;

typedef struct {
	U32 ulPTEPCA;	//Will be filled with 0xFFFFFFFF
#if (PS5017_EN || PS5021_EN)
	U32 ulPTEIndex	    : 20;
	U32 				: 12;
#else /* (PS5017_EN || PS5021_EN) */
	U32 ulPTEIndex		: 19;
	U32 				: 13;
#endif /* (PS5017_EN || PS5021_EN) */
} TrimCondLog_t ;

/*
 * ---------------------------------------------------------------------------------------------------
 *   macros
 * ---------------------------------------------------------------------------------------------------
 */
//========================== DEBUG_TABLE_UPDATE_FLOW ===========================
#if DEBUG_TABLE_UPDATE_FLOW==TRUE
// print PTE and PMD after programmed done of table update flow
#define M_DEBUG_INSERT_DSA_PROGRAM_DONE_DUMP(pTableProgram) do {\
	if ( PTE == pTableProgram->ubMode ) {\
		if ( guwProgramPTERoundBackup != pTableProgram->ulRound ) {\
			M_UART(TABLE_, "Table Program Done: PTE, DTCnt:%d, Round:%d\n",\
				pTableProgram->uwDTLogCnt, pTableProgram->ulRound );\
			guwProgramPTERoundBackup = pTableProgram->ulRound;\
		}\
	}\
	else {\
		if ( guwProgramPMDRoundBackup != pTableProgram->ulRound ) {\
			M_UART(TABLE_, "Table Program Done: PMD, DTCnt:%d, Round:%d\n",\
				pTableProgram->uwDTLogCnt, pTableProgram->ulRound );\
			guwProgramPMDRoundBackup = pTableProgram->ulRound;\
		}\
	}\
} while (0)

#else /* DEBUG_TABLE_UPDATE_FLOW */
#define M_DEBUG_INSERT_DSA_PROGRAM_PTE_DUMP(uwTagId)
#define M_DEBUG_INSERT_DSA_PROGRAM_PMD_DUMP(uwTagId)
#define M_DEBUG_INSERT_DSA_PROGRAM_DONE_DUMP(pTableProgram)
#endif /* DEBUG_TABLE_UPDATE_FLOW */

#define FTL_TABLE_UPDATE_SET_SEQ_PTE_INVALID_CID_IN_HMB_EN					(FALSE)

//========================== UART_TABLE_GC_FLOW ===========================
#if UART_TABLE_GC_FLOW==TRUE

// print PTE copied of Table GC
#define M_DEBUG_TABLE_GC_COPY_PTE_DUMP() do {\
	FTLTableGC *pTableGC = &gFTLState.TableGC;\
	M_UART(TABLE_, "[Table GC copy PTE] Read/ProgramIndex:%d/%d, CopyBoundary:%d\n", \
			pTableGC->uwCopyReadIndex, pTableGC->uwCopyProgramIndex, pTableGC->uwGCPTELogNum);\
} while (0)

// print PMD copied of Table GC
#define M_DEBUG_TABLE_GC_COPY_PMD_DUMP() do {\
	FTLTableGC *pTableGC = &gFTLState.TableGC;\
	M_UART(TABLE_, "[Table GC copy PMD] Read/ProgramIndex:%d/%d, CopyBoundary:%d\n", \
			pTableGC->uwCopyReadIndex, pTableGC->uwCopyProgramIndex, pTableGC->uwGCPTELogNum);\
} while (0)

// print VC of each source of Table GC
#define M_DEBUG_TABLE_GC_SOURCE_VC_DUMP() do {\
	U8 ubi;\
	FTLTableGC *pTableGC = &gFTLState.TableGC;\
	M_UART(TABLE_, "After Table GC:\n");\
	for (ubi = 0; ubi < pTableGC->ubGCVCZeroSourceNum; ubi++) {\
		M_UART(TABLE_, "Source: %X, VC: %X\n", pTableGC->uwGCVCZeroSource[ubi], gpulVC[pTableGC->uwGCVCZeroSource[ubi]].ulAll);\
	}\
	for (ubi = 0; ubi < pTableGC->ubGCSourceNum; ubi++) {\
		M_UART(TABLE_, "Source: %X, VC: %X\n", pTableGC->uwGCSource[ubi], gpulVC[pTableGC->uwGCSource[ubi]].ulAll);\
	}\
} while (0)

#else /* UART_TABLE_GC_FLOW */
#define M_DEBUG_TABLE_GC_COPY_PTE_DUMP()
#define M_DEBUG_TABLE_GC_COPY_PMD_DUMP()
#define M_DEBUG_TABLE_GC_SOURCE_VC_DUMP()
#endif /* UART_TABLE_GC_FLOW */
#endif // FTL_TABLE_H_
