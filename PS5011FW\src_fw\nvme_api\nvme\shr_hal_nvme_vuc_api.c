
/**************************************************************************/
/*                                                                        */
/*           Copyright (c) 2000-2017 by Phison Electronics Corp.          */
/*                                                                        */
/**************************************************************************/

/**************************************************************************/
/*                                                                        */
/*  HAL UART DEFINITION                                    RELEASE        */
/*                                                                        */
/*    shr_hal_nvme_vuc_api.c                                        GNU C         */
/*                                                           X.X          */
/*  AUTHOR                                                                */
/*                                                                        */
/*                                                                        */
/*  DESCRIPTION                                                           */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/*  RELEASE HISTORY                                                       */
/*                                                                        */
/*    DATE              NAME                      DESCRIPTION             */
/*                                                                        */
/*                                                                        */
/**************************************************************************/
//shr_hal_nvme_vuc_api.c

#define _SHR_HAL_NVME_VUC_API_C_

#include "nvme_api/nvme/shr_hal_nvme_api.h"
#include "nvme_api/nvme/shr_hal_nvme_vuc_api.h"

#include <stdio.h>
#include "common/mem.h"
#include "ftl/ftl_sync_cmd_handler.h"
#include "ftl/ftl_nrw_api.h"
#include "aom/aom_api.h"
#include "host/VUC_handler_api.h"
#include "host_handler/hostevt_errhdl_api.h"
#include "hal/cop0/cop0_api.h"
#include "hal/mr/mr_api.h"
#include "vuc/VUC_api.h"
#include "hal/dmac/dmac_pop_cmd.h"
#include "init/fw_init.h"
#include "testflow/sim_spor/sim_spor_api.h"
#include "retry/retry_api.h"

//NCS_SBRAID_
#include "table/vbmap/vbmap_api.h"

#if (VUC_MICRON_NICKS_EN)
static U32 gulMicronVUCBufferAddr = 0;
static U8 gubMicronVUCBufferFlag = 0;
#endif /*(VUC_MICRON_NICKS_EN)*/

AOM_VUC void VUCDirectRead(OPT_HCMD_PTR pCmd);

void nvme_vendor_power (OPT_HCMD_PTR pCmd)
{
	CTAG_t CTAG;
	U8 bStatus;

	CTAG = pCmd->hcmd.info.CTAG;

	//check SCT SC.............................................
	if (pCmd->state == INITDONE) {
		bStatus = NRWAdminVendorPowerHandler(pCmd->hcmd.info.nonuser.ulDW10);

		if (bStatus == TRUE) {
			pCmd->state = CMD_STATE_CMD_CPL;
		}
	}

	if (pCmd->state == CMD_STATE_CMD_CPL) {
		nvme_send_cq_completion (CTAG, GENERIC_CMD, SUCCESS_CPL, 0, 0, 0, (U32 *)&gErrLogData[0]); //get phase tag

		pCmd->state = CMD_STATE_FINISH;
	}
}


#if SUPPORT_VUC_VPG_EN

VPG_SAVE_INFO_STRUCT_T  VPG_FW_INFO;

void vpg_get_info_flow (VPG_INFO_STRUCT_PTR ulPhyAddr)
{
	//M_UART(NVME_API_,"\nvpg_get_info_flow");
	memcpy((void *)&ulPhyAddr->uwCFASig, (void *) "TB", 2); // TBD,will check with sony
	ulPhyAddr->uwVPGLevel = 0x0190;


	ulPhyAddr->uwPS0PowerSet =  VUC_PS0PowerSet;
	ulPhyAddr->uwPS1PowerSet =  VUC_PS1PowerSet;
	ulPhyAddr->uwPS2PowerSet =  VUC_PS2PowerSet;

	ulPhyAddr->uwMaxStream =  VUC_VPG_MAX_STREAM_NUBMER;
	ulPhyAddr->uwVpgStatus = VPG_FW_INFO.uwVpgStatus;

	ulPhyAddr->uwCurrentStream = VPG_FW_INFO.uwCurrentStream;
	ulPhyAddr->uwAUSize = VUC_VPG_AU_SIZE_SC;
	ulPhyAddr->ulGUSize = VUC_VPG_GU_SIZE_SC;
	ulPhyAddr->wuRUSize = VUC_VPG_RU_SIZE_SC;
	ulPhyAddr->wuOFS = VUC_VPG_OFS_AU;
	nvme_change_state_api (FW_PROCESS_DONE);
}

void vpg_start_rec_flow(OPT_HCMD_PTR pCmd)
{
	VPG_FW_INFO.uwVpgStatus = VPG_STATE_ENABLE;
	nvme_change_state_api (FW_PROCESS_DONE);

}
void vpg_terminate_flow(OPT_HCMD_PTR pCmd)
{
	U8 ubI;
	for (ubI = 0; ubI < VUC_VPG_STREAM_AU_MAX; ubI++) {
		if (VPG_FW_INFO.VPGStreamID[ubI].ubVpgStreamStatus == VPG_STREAM_STATE_FREE) {
			VPG_FW_INFO.VPGStreamID[ubI].ubOpenAUCount = 0;
			VPG_FW_INFO.VPGStreamID[ubI].uoStartLBA = 0;
			VPG_FW_INFO.VPGStreamID[ubI].ulWrittenLBA = 0;
			VPG_FW_INFO.VPGStreamID[ubI].ubVpgStreamStatus = VPG_STREAM_STATE_FREE;
			VPG_FW_INFO.uwCurrentStream--;
		}
	}
	VPG_FW_INFO.uwVpgStatus = VPG_STATE_TERMINATE;
	nvme_change_state_api (FW_PROCESS_DONE);
}

void vpg_open_stream_flow(U32 ulStartLBA, U32 ulAUCount)
{
	U8 ubI;
	for (ubI = 0; ubI < VUC_VPG_STREAM_AU_MAX; ubI++) { //search one free stream to enable
		if (VPG_FW_INFO.VPGStreamID[ubI].ubVpgStreamStatus == VPG_STREAM_STATE_FREE) {
			VPG_FW_INFO.VPGStreamID[ubI].ubOpenAUCount = ulAUCount;
			VPG_FW_INFO.VPGStreamID[ubI].uoStartLBA = ulStartLBA;
			VPG_FW_INFO.VPGStreamID[ubI].ulWrittenLBA = 0;
			VPG_FW_INFO.VPGStreamID[ubI].ubVpgStreamStatus = VPG_STREAM_STATE_OPEN;
			VPG_FW_INFO.uwCurrentStream++;
			break;
		}
	}
	//VPG_FW_INFO.uwVpgStatus = VPG_STATE_ENABLE;
	nvme_change_state_api (FW_PROCESS_DONE);
}

//Operation Code =0x4
void vpg_suspend_rec_flow(OPT_HCMD_PTR pCmd)
{
	VPG_FW_INFO.uwVpgStatus = VPG_STATE_SUSPEND;
	nvme_change_state_api (FW_PROCESS_DONE);
}
void vpg_resume_flow(OPT_HCMD_PTR pCmd)
{
	VPG_FW_INFO.uwVpgStatus = VPG_STATE_ENABLE;
	nvme_change_state_api (FW_PROCESS_DONE);
}

void vuc_vpg_open_stream_check_status (OPT_HCMD_PTR pCmd)
{
	P_HOST_GEOMETRY_PTR pHostPrtInfo = (P_HOST_GEOMETRY_PTR) gpHostPrtInfo;
	U32 ulStartLBA, ulEndLBA, ulAUCount, ulNSID, ulTempStartLBA, ulTempEndLBA;
	U8 ubI;
	U64 uoLbSize;

	ulNSID = pCmd->nvme_sqcmd.adm.nsid;

	//sony one LBA= 512byte
	//ulLBDataSize = pHostPrtInfo->pVarNsIdfy[ulNSID-1]->Lbaf[pHostPrtInfo->pVarNsIdfy[ulNSID]->CurrLbaf.btFlbas].btLbaDataSize;
	uoLbSize = pHostPrtInfo->pVarNsIdfy[ulNSID - 1]->uoLbSize;
	ulStartLBA = pCmd->nvme_sqcmd.adm.vuc_vpg.cdw11;
	ulAUCount = pCmd->nvme_sqcmd.adm.vuc_vpg.cdw12;

	// 1.check open stream range
	if (ulStartLBA < VUC_VPG_OFS_LBA) {
		nvme_admi_cmd_sc_msg (pCmd, GENERIC_CMD, INVALID_FIELD, NVME_ERR_LOC_DW11, 0);
	}
	else if ((ulAUCount > VUC_VPG_STREAM_AU_MAX ) || (ulStartLBA % VUC_VPG_AU_SIZE_SC)) {
		nvme_admi_cmd_sc_msg (pCmd, GENERIC_CMD, INVALID_FIELD, NVME_ERR_LOC_DW11, 0);
	}
	else  if (ulStartLBA >= uoLbSize) {
		nvme_admi_cmd_sc_msg (pCmd, GENERIC_CMD, LBA_OUT_RANGE, NVME_ERR_LOC_DW11, 0);
	}
	else if ((ulStartLBA + (ulAUCount * VUC_VPG_AU_SIZE_SC)) >= uoLbSize) {
		nvme_admi_cmd_sc_msg (pCmd, GENERIC_CMD, LBA_OUT_RANGE, NVME_ERR_LOC_DW11, 0);
	}
	else {
		// 2.overlapping check
		// if ((pCmd->cur_sct == GENERIC_CMD )&&( pCmd->cur_sc == SUCCESS_CPL)){
		ulEndLBA = (ulStartLBA + (ulAUCount * VUC_VPG_AU_SIZE_SC));
		for (ubI = 0; ubI < VUC_VPG_STREAM_AU_MAX; ubI++) { //search one free stream to enable
			if (VPG_FW_INFO.VPGStreamID[ubI].ubVpgStreamStatus == VPG_STREAM_STATE_OPEN) {
				//ulTempAUCount = VPG_FW_INFO.VPGStreamID[ubI].ubOpenAUCount;
				ulTempStartLBA = VPG_FW_INFO.VPGStreamID[ubI].uoStartLBA;
				ulTempEndLBA = (ulTempStartLBA + (VPG_FW_INFO.VPGStreamID[ubI].ubOpenAUCount * VUC_VPG_AU_SIZE_SC));
				// 1. |----|  2.  |----|      3.  |----|      4. |----|
				//      |--|     |--------|             |---|    |---|
				if (((ulStartLBA <= ulTempStartLBA) && (ulEndLBA >= ulTempEndLBA)) || ((ulStartLBA >= ulTempStartLBA) && (ulEndLBA <= ulTempEndLBA))
					|| ((ulTempStartLBA >= ulStartLBA) && (ulTempStartLBA <= ulEndLBA)) || ((ulTempEndLBA >= ulTempStartLBA) && ( ulTempEndLBA <= ulEndLBA))) {
					nvme_admi_cmd_sc_msg (pCmd, GENERIC_CMD, INVALID_FIELD, NVME_ERR_LOC_DW11, 0);
					break;
				}
			}
		}
	}
}
void vuc_vpg_check_status (OPT_HCMD_PTR pCmd)
{

	U32 ulSubOp ;

	ulSubOp = pCmd->nvme_sqcmd.adm.vuc_vpg.sub_op;

	switch (ulSubOp) {
	case VUC_VPG_OPC_GET_INFO: {
			nvme_prp_status_check (pCmd, SIZE_4KB);
			break;
		}
	case VUC_VPG_OPC_START_REC:
		break;
	case VUC_VPG_OPC_TERMINATE_REC:
		break;
	case VUC_VPG_OPC_SUSPEND_REC:
		break;
	case VUC_VPG_OPC_RESUME_REC:
		break;
	case VUC_VPG_OPC_OPEN_STREAM:
		// LBA not AU aligned
		{
			vuc_vpg_open_stream_check_status (pCmd);
		}
		break;
	default:
		nvme_admi_cmd_sc_msg (pCmd, GENERIC_CMD, INVALID_FIELD, NVME_ERR_LOC_DW10, 0);
	}

}



//Operation Code =0xF
void vpg_get_info_api (OPT_HCMD_PTR pCmd)
{
	//U32 ulPhyAddr;
	VPG_INFO_STRUCT_PTR ulPhyAddr;

	nvme_malloc_dbuf_api (pCmd, BC_4KB, WCH_RTYPE, BYTE_CMD) ;
	if (pCmd->state == WAIT_FW_PROCESS) {

		ulPhyAddr =  (VPG_INFO_STRUCT_PTR)(pCmd->cur_phyMemAddr);
		vpg_get_info_flow (ulPhyAddr);
		//pCmd->state = FW_PROCESS_DONE;
	}


	if (pCmd->state == FW_PROCESS_DONE) {
		pCmd->state = TRIGDMA;
	}

	if (pCmd->state == PROCESS_ALL_DONE) {

		pCmd->state = CMD_STATE_CMD_CPL;
	}

}




//Operation Code =0x1
void vpg_start_rec_api(OPT_HCMD_PTR pCmd)
{
	VPG_INFO_STRUCT_PTR vuc = (VPG_INFO_STRUCT_PTR)&VPG_FW_INFO ;
	if (pCmd->state == INITDONE) {
		if (vuc->uwVpgStatus != VPG_STATE_TERMINATE) {
			nvme_admi_cmd_sc_msg (pCmd, GENERIC_CMD, INVALID_FIELD, NVME_ERR_NOT_SPECIFIC, NVME_ERR_NOT_SPECIFIC);
			pCmd->state = CMD_STATE_CMD_CPL;
		}
		else {
			pCmd->state = WAIT_FW_PROCESS;
		}
	}
	if (pCmd->state == WAIT_FW_PROCESS) {
		vpg_start_rec_flow (pCmd);
	}

	if (pCmd->state == FW_PROCESS_DONE) {
		pCmd->state = CMD_STATE_CMD_CPL;
	}

}

//Operation Code =0x2
void vpg_terminate_rec_api(OPT_HCMD_PTR pCmd)
{

	VPG_INFO_STRUCT_PTR vuc = (VPG_INFO_STRUCT_PTR)&VPG_FW_INFO ;
	if (pCmd->state == INITDONE) {
		if (vuc->uwVpgStatus != VPG_STATE_ENABLE) {
			nvme_admi_cmd_sc_msg (pCmd, GENERIC_CMD, INVALID_FIELD, NVME_ERR_NOT_SPECIFIC, NVME_ERR_NOT_SPECIFIC);
			pCmd->state = CMD_STATE_CMD_CPL;
		}
		else {
			pCmd->state = WAIT_FW_PROCESS;
		}
	}
	if (pCmd->state == WAIT_FW_PROCESS) {
		vpg_terminate_flow (pCmd);

	}

	if (pCmd->state == FW_PROCESS_DONE) {
		pCmd->state = CMD_STATE_CMD_CPL;
	}
}

//Operation Code =0x3
void vpg_open_stream_api(OPT_HCMD_PTR pCmd)
{
	VPG_INFO_STRUCT_PTR vuc = (VPG_INFO_STRUCT_PTR)&VPG_FW_INFO ;
	U8 StreamFull = 1, ubI;
	U32 ulStartLBA, ulAUCount ;

	if (pCmd->state == INITDONE) {
		//VPG open stream allocate specified AU for VPG recording
		// fill fw revision from idpage
		for (ubI = 0; ubI < VUC_VPG_STREAM_AU_MAX; ubI++) {
			if (vuc->VPGStreamID[ubI].ubVpgStreamStatus == VPG_STREAM_STATE_FREE) {
				StreamFull = 0;
				break;
			}
		}

		if (StreamFull) {
			nvme_admi_cmd_sc_msg (pCmd, GENERIC_CMD, INVALID_FIELD, NVME_ERR_NOT_SPECIFIC, NVME_ERR_NOT_SPECIFIC);
			pCmd->state = CMD_STATE_CMD_CPL;
		}
		else {
			pCmd->state = WAIT_FW_PROCESS;
		}
	}

	if (pCmd->state == WAIT_FW_PROCESS) {
		ulStartLBA = pCmd->nvme_sqcmd.adm.vuc_vpg.cdw11;
		ulAUCount = pCmd->nvme_sqcmd.adm.vuc_vpg.cdw12;
		vpg_open_stream_flow (ulStartLBA, ulAUCount);
	}

	if (pCmd->state == FW_PROCESS_DONE) {
		pCmd->state = CMD_STATE_CMD_CPL;
	}

}
//Operation Code =0x4
void vpg_suspend_rec_api(OPT_HCMD_PTR pCmd)
{
	VPG_INFO_STRUCT_PTR vuc = (VPG_INFO_STRUCT_PTR)&VPG_FW_INFO ;
	if (pCmd->state == INITDONE) {
		if (vuc->uwVpgStatus != VPG_STATE_ENABLE) {
			nvme_admi_cmd_sc_msg (pCmd, GENERIC_CMD, INVALID_FIELD, NVME_ERR_NOT_SPECIFIC, NVME_ERR_NOT_SPECIFIC);
			pCmd->state = CMD_STATE_CMD_CPL;
		}
		else {
			pCmd->state = WAIT_FW_PROCESS;
		}
	}
	if (pCmd->state == WAIT_FW_PROCESS) {
		vpg_suspend_rec_flow (pCmd);
	}

	if (pCmd->state == FW_PROCESS_DONE) {
		pCmd->state = CMD_STATE_CMD_CPL;
	}
}

//Operation Code =0x5
void vpg_resume_rec_api(OPT_HCMD_PTR pCmd)
{

	VPG_INFO_STRUCT_PTR vuc = (VPG_INFO_STRUCT_PTR)&VPG_FW_INFO ;

	if (pCmd->state == INITDONE) {
		if (vuc->uwVpgStatus != VPG_STATE_SUSPEND) {
			nvme_admi_cmd_sc_msg (pCmd, GENERIC_CMD, INVALID_FIELD, NVME_ERR_NOT_SPECIFIC, NVME_ERR_NOT_SPECIFIC);
			pCmd->state = CMD_STATE_CMD_CPL;
		}
		else {
			pCmd->state = WAIT_FW_PROCESS;
		}
	}
	if (pCmd->state == WAIT_FW_PROCESS) {
		vpg_resume_flow (pCmd);
	}

	if (pCmd->state == FW_PROCESS_DONE) {
		pCmd->state = CMD_STATE_CMD_CPL;
	}
}

void nvme_vuc_vpg (OPT_HCMD_PTR pCmd)
{
	//P_HOST_GEOMETRY_PTR pHostPrtInfo = (P_HOST_GEOMETRY_PTR) gpHostPrtInfo;
	U32 ulSubOp ;
	ulSubOp = pCmd->nvme_sqcmd.adm.vuc_vpg.sub_op;
	//M_UART(NVME_API_,"\nnvme_vuc_vpg uoLPOUL=%X", ulSubOp);
	if (pCmd->state == INITDONE) {
		vuc_vpg_check_status (pCmd);
		if ( ( pCmd->cur_sct != GENERIC_CMD) || ( pCmd->cur_sc != SUCCESS_CPL) ) {
			pCmd->state = CMD_STATE_CMD_CPL;
		}
		else {
			pCmd->state = INITDONE;
		}
	}

	if (pCmd->state < CMD_STATE_CMD_CPL) {

		switch (ulSubOp) {
		case VUC_VPG_OPC_GET_INFO:
			vpg_get_info_api (pCmd);
			break;

		case VUC_VPG_OPC_START_REC:
			vpg_start_rec_api (pCmd);
			break;

		case VUC_VPG_OPC_TERMINATE_REC:
			vpg_terminate_rec_api (pCmd);
			break;

		case VUC_VPG_OPC_OPEN_STREAM:
			vpg_open_stream_api (pCmd);
			break;

		case VUC_VPG_OPC_SUSPEND_REC:
			vpg_suspend_rec_api (pCmd);
			break;

		case VUC_VPG_OPC_RESUME_REC:
			vpg_resume_rec_api (pCmd);
			break;
		default:
			break;
		}
	}

	if (pCmd->state == CMD_STATE_CMD_CPL) {
		if ( ( pCmd->cur_sct != GENERIC_CMD) || ( pCmd->cur_sc != SUCCESS_CPL) ) {
			nvme_lid_err_log_fill_info_api (pCmd->cmd_err_byte,  pCmd->cmd_err_bit, 0, 0, &gErrLogData[0]);
		}
		nvme_send_cq_completion (pCmd->hcmd.info.CTAG,  pCmd->cur_sct,  pCmd->cur_sc, 0, 0, 0, (U32 *)&gErrLogData[0]); //get phase tag
		pCmd->state = CMD_STATE_FINISH;
	}
}

#endif

AOM_NRW void nvme_vuc_identify_check_status (OPT_HCMD_PTR pCmd)
{
	U8  ubCNS;
	U32 ulNSID;
	U16 ulCNTID;

	ulNSID = pCmd->nvme_sqcmd.raw_u8_data.by[53];
	ubCNS = pCmd->nvme_sqcmd.raw_u8_data.by[52];
	ulCNTID = ((pCmd->nvme_sqcmd.raw_data.dw[13] >> 16) & 0xFFFF);
	if ((ubCNS > CNS_CIDL_ALL_NM) || ((ubCNS > CNS_NSDESC) && (ubCNS < CNS_NSL_NM))) {
		nvme_admi_cmd_sc_msg (pCmd, GENERIC_CMD, INVALID_FIELD, NVME_ERR_LOC_DW10, 0);
	}//check Note1:NVMe 1.3 P113 Mandataory  for controllers that support on Namespae Management enable
	/*else if ( ((ulCNTID != 0) || (ubRSV != 0)) && (((ubCNS >= CNS_NDS) && (ubCNS <= CNS_NSL)) || (ubCNS == CNS_NSL_NM) || (ubCNS == CNS_NDS_NM))) {
		nvme_admi_cmd_sc_msg (pCmd, GENERIC_CMD, INVALID_FIELD, NVME_ERR_LOC_DW10 + NVME_DW_BYTE_OFFSET_2, 0);
	}*//* peter */ //CNS=0 (not support NS Management and NSID=0xFFFFFFFF shall fail)
#if (SUPPORT_NS_MANAGE_EN==0)
	else if (((ubCNS == CNS_NSL_NM) || (ubCNS == CNS_NDS_NM) || (ubCNS == CNS_CIDL_ATT_NM) || (ubCNS == CNS_CIDL_ALL_NM) )) {
		//else if (((ubCNS == CNS_NSL_NM) || (ubCNS == CNS_NDS_NM) || (ubCNS == CNS_CIDL_ATT_NM) || (ubCNS == CNS_CIDL_ALL_NM) ) && (SUPPORT_NS_MANAGE_EN == 0)) {
		nvme_admi_cmd_sc_msg (pCmd, GENERIC_CMD, INVALID_FIELD, NVME_ERR_LOC_DW10, 0);
	} //CNTID check // NVMe 1.3 P114 : cntid "should" be cleared to 0h for Identify operations with a CNS value of 00h, 01h, 02h, 10h, and 11h
	//else if ( ((ubCNS == CNS_NDS) && (ulNSID == NVME_ALL_NS)) && (SUPPORT_NS_MANAGE_EN == 0) ) {
	else if  ((ubCNS == CNS_NDS) && (ulNSID == NVME_ALL_NS)) {
		nvme_admi_cmd_sc_msg (pCmd, GENERIC_CMD, INVALID_NS, NVME_ERR_LOC_NAMESPACE, 0);
	} //CNS=0 (check invaild NSID or NS support and NSID != 0xFFFFFFFF // consider max 8 NS and 0xFFFFFFFF
#endif
	else if ( (ubCNS == CNS_NDS) && ((ulNSID == NVME_NS_NONE) || ( (ulNSID > MAX_NS_NUMBER) && (ulNSID != NVME_ALL_NS))))  {
		nvme_admi_cmd_sc_msg (pCmd, GENERIC_CMD, INVALID_NS, NVME_ERR_LOC_NAMESPACE, 0);
	} //CNS=1 (invalid NSID)
	else if ((ubCNS == CNS_CDS) && (ulNSID != NVME_NS_NONE)) {
		nvme_admi_cmd_sc_msg (pCmd, GENERIC_CMD, INVALID_NS, NVME_ERR_LOC_NAMESPACE, 0);
	}//CNS=2 or CNS=0x10
	else if (((ubCNS == CNS_NSL) || (ubCNS == CNS_NSL_NM)) && (ulNSID == 0xFFFFFFFE || ulNSID == NVME_ALL_NS)  ) {
		nvme_admi_cmd_sc_msg (pCmd, GENERIC_CMD, GENERIC_CMD, NVME_ERR_LOC_NAMESPACE, 0);
	}//CNS=3 //only support 1.3 later
	else if ( (ubCNS == CNS_NSDESC) && (HAL_VS_GET_VER_API() <= IDFY_CTRL_VER_1_2_MNR)) {
		nvme_admi_cmd_sc_msg (pCmd, GENERIC_CMD, INVALID_FIELD, NVME_ERR_LOC_DW10, 0);
	}//CNS=3 register invalid NSID
	else if ( (ubCNS == CNS_NSDESC) && ((ulNSID == NVME_NS_NONE) || ( (ulNSID > MAX_NS_NUMBER) && (ulNSID != NVME_ALL_NS))))  {
		nvme_admi_cmd_sc_msg (pCmd, GENERIC_CMD, INVALID_NS, NVME_ERR_LOC_NAMESPACE, 0);
	}//CNS=0x11 invalid NS all fail
	else if ((ubCNS == CNS_NDS_NM) && ((ulNSID == NVME_NS_NONE) || (ulNSID > MAX_NS_NUMBER)) ) {
		nvme_admi_cmd_sc_msg (pCmd, GENERIC_CMD, INVALID_NS, NVME_ERR_LOC_NAMESPACE, 0);
	}//CNS=0x12 invalid NS all fail
	else if ((ubCNS == CNS_CIDL_ATT_NM) && ((ulNSID == NVME_NS_NONE) || (ulNSID > MAX_NS_NUMBER)) ) {
		nvme_admi_cmd_sc_msg (pCmd, GENERIC_CMD, INVALID_NS, NVME_ERR_LOC_NAMESPACE, 0);
	}

}

AOM_NRW void nvme_vuc_identify (OPT_HCMD_PTR pCmd)
{
	U32 ulPhyAddr;
	U32 ulNSID, ulDw10;

	//check SCT SC.............................................
	if (pCmd->state == INITDONE) {
		nvme_vuc_identify_check_status (pCmd);

		if ( (pCmd->cur_sct != GENERIC_CMD) || (pCmd->cur_sc != SUCCESS_CPL) ) {
			pCmd->state = PROCESS_ALL_DONE;
		}
	}

	//--------------------------------------

	nvme_malloc_dbuf_api (pCmd, BC_4KB, WCH_RTYPE, BYTE_CMD) ;

	if (pCmd->state == WAIT_FW_PROCESS) {
		ulPhyAddr =  (pCmd->cur_phyMemAddr);
		ulNSID = pCmd->nvme_sqcmd.raw_u8_data.by[53];
		ulDw10 = pCmd->nvme_sqcmd.raw_data.dw[13];

		nvme_identify_fill_api (ulDw10, ulNSID, ulPhyAddr);
		pCmd->state = FW_PROCESS_DONE;
	}

	if (pCmd->state == FW_PROCESS_DONE) {
		pCmd->state = TRIGDMA;
	}


	if (pCmd->state == PROCESS_ALL_DONE) {
		if (gVUCVar.ubSecspKeyValid == 0) {
			pCmd->state = CMD_STATE_CMD_CPL;
		}
		else {
			return;
		}
	}

	if (pCmd->state == CMD_STATE_CMD_CPL) {
		if ( (pCmd->cur_sct != GENERIC_CMD) || (pCmd->cur_sc != SUCCESS_CPL) ) {
			nvme_lid_err_log_fill_info_api (pCmd->cmd_err_byte, pCmd->cmd_err_bit, 0, 0, &gErrLogData[0]);
		}
		nvme_send_cq_completion (pCmd->hcmd.info.CTAG, pCmd->cur_sct, pCmd->cur_sc, 0, 0, 0, (U32 *)&gErrLogData[0]); //get phase tag
		pCmd->state = CMD_STATE_FINISH;
	}
}

static void nvme_pass_through(OPT_HCMD_PTR pCmd)
{
	U8 ubSubFeature;

	/* byte[49] is Sub Feature */
	ubSubFeature = (U8) ((pCmd->nvme_sqcmd.raw_data.dw[12] >> 8) & 0xFF);

	M_UART(NVME_API_, "\nNVME_PASS: %d", ubSubFeature);
	switch (ubSubFeature) {
	case GET_LOG:
		nvme_get_log(pCmd);
		break;
	case IDENTIFY:
		nvme_vuc_identify(pCmd);
		break;
	case SET_FEATURE:
		nvme_set_feature(pCmd);
		break;
	case GET_FEATURE:
		nvme_get_feature(pCmd);
		break;
	case FW_COMMIT:
		nvme_fw_commit(pCmd);
		break;
	case FW_DL:
		nvme_fw_img_download(pCmd);
		break;
	case FORMAT_NVME:
		nvme_format(pCmd);
		break;
	default:
		break;
	}
}

AOM_VUC U8 nvme_vuc_support_cmd(OPT_HCMD_PTR pCmd)
{
	U8 ubCmdSupport = FALSE;
	U8 ubFeature, ubSubFeature;

	/* byte[48] is Feature */
	ubFeature = (U8) (pCmd->nvme_sqcmd.raw_data.dw[12] & 0xFF);

	/* byte[49] is Sub Feature */
	ubSubFeature = (U8) ((pCmd->nvme_sqcmd.raw_data.dw[12] >> 8) & 0xFF);

	if (VUC_PROTECT_EN) {
		if ((VUC_PROTECT_VERIFY_STATE_PASS != gVUCVar.VUCProtect.ubProtectState) && (FAIL == VUCProtectPassList(ubFeature))) {
			return ubCmdSupport;
		}
	}

	switch (ubFeature) {

	/*************** No-Data VUC ***************/
	case VUC_AP_KEY:
	case VUC_ISP_JUMP:
	case VUC_CUT_EFUSE:
	case VUC_SET_SPECIFIC_GLOBAL_VALUE:
	case VUC_ERASE_PCA:
	case VUC_CLEAR_SMART:
	case VUC_DLMC_PREFORMAT:
	case VUC_MAKE_ERROR:
		if (!VUC_PROTECT_EN) {
			ubCmdSupport = (VENDOR_NODATA == pCmd->nvme_sqcmd.vendor.opcode);
		}
		else {
			ubCmdSupport = (VENDOR_NODATA == pCmd->nvme_sqcmd.vendor.opcode || VENDOR_ENCRYPT_NO_DATA == pCmd->nvme_sqcmd.vendor.opcode);
		}
		break;
	case VUC_SEND_HANDSHAKE_REQUEST:
		if (VUC_PROTECT_EN) {
			ubCmdSupport = TRUE;
		}
		break;
	case VUC_DISABLE_VUC:
		if (VUC_PROTECT_EN) {
			ubCmdSupport = TRUE;
		}
		break;
	case VUC_ERASE_BLOCK:
		break;
	case VUC_DO_PCIE_EYE_FLOW: // 0xEE
		// E13 only support eye open (eye width)
		if (VUC_DO_PCIE_EYE_OPEN == ubSubFeature) {
			ubCmdSupport = TRUE;
		}
		break;
	/*************** Data-In VUC ***************/
	case VUC_WRITE_SRAM:
	case VUC_WRITE_REG:
	case VUC_WRITE_INFO:
	case VUC_WRITE_PH:
	case VUC_PROG_PAGE:
	case VUC_PROG_PCA:
	case VUC_CACHE_PROG:
	case VUC_SET_SMART_VALUE:
#if (TCG_EN && !BURNER_MODE_EN && !RDT_MODE_EN && !LPM3_LOADER)
	case VUC_TCG_PSID:
#endif /* (TCG_EN && !BURNER_MODE_EN && !RDT_MODE_EN && !LPM3_LOADER) */

		if (!VUC_PROTECT_EN) {
			ubCmdSupport = (VENDOR_WRITEDATA == pCmd->nvme_sqcmd.vendor.opcode);
		}
		else {
			ubCmdSupport = (VENDOR_WRITEDATA == pCmd->nvme_sqcmd.vendor.opcode || VENDOR_ENCRYPT_WRITE_DATA == pCmd->nvme_sqcmd.vendor.opcode);
		}
		break;
	case VUC_KINGSTON_ANTI_FAKE_DATA:
		if (KINGSTON_EN) {
			if (!VUC_PROTECT_EN) {
				ubCmdSupport = ( (VENDOR_WRITEDATA == pCmd->nvme_sqcmd.vendor.opcode) || (VENDOR_READDATA == pCmd->nvme_sqcmd.vendor.opcode) );
			}
			else {
				ubCmdSupport = ( (VENDOR_WRITEDATA == pCmd->nvme_sqcmd.vendor.opcode) || (VENDOR_ENCRYPT_WRITE_DATA == pCmd->nvme_sqcmd.vendor.opcode) || (VENDOR_READDATA == pCmd->nvme_sqcmd.vendor.opcode) || (VENDOR_ENCRYPT_READ_DATA == pCmd->nvme_sqcmd.vendor.opcode));
			}
		}
		else {
			ubCmdSupport = FALSE;
		}
		break;
	case VUC_FW_FEATURE_CTRL:
		ubCmdSupport = TRUE;
		break;
	case VUC_PREFORMAT:
	case VUC_NAND_VERIFY_WRITE:
	case VUC_NAND_VERIFY_TRIGGER:
	case VUC_SCAN_FLASH_SETTING:
	case VUC_SET_SCAN_WINDOW_PARAMETER:
		break;

	case VUC_SEND_ENCRYPTION_DATA:
		if (VUC_PROTECT_EN) {
			ubCmdSupport = TRUE;
		}

		break;

	/*************** Data-Out VUC ***************/
	case VUC_READ_SYS_INFO:
	case VUC_DUMP_TABLE:
	case VUC_GET_BLOCK_STATUS:
	case VUC_GET_SMART:
	case VUC_GET_FLASH_INFO:
	case VUC_GET_STATUS:
	case VUC_READ_SRAM:
	case VUC_READ_REG:
	case VUC_READ_INFO:
	case VUC_READ_PH:
	case VUC_READ_PAGE:
	case VUC_READ_PCA:
	case VUC_CACHE_READ:
	case VUC_DETECT_PERIPHERAL:
	case VUC_GET_DATA_SIZE:
	case VUC_FLASH_FEATURE_CONFIGURE:
	case VUC_PCA_TRANSLATE:
	case VUC_GET_TRIM_TABLE:
	case VUC_GET_PCIE_EYE_INFO: // 0xEF
	case VUC_DETECT_TXOPEN_RXSHORT:
		if (!VUC_PROTECT_EN) {
			ubCmdSupport = (VENDOR_READDATA == pCmd->nvme_sqcmd.vendor.opcode);
		}
		else {
			ubCmdSupport = (VENDOR_READDATA == pCmd->nvme_sqcmd.vendor.opcode || VENDOR_ENCRYPT_READ_DATA == pCmd->nvme_sqcmd.vendor.opcode);
		}
		break;
	case VUC_DUMP_PRAM:
	case VUC_DUMP_CODE:
		break;

	/*************** VUC SECURITY PASS THROUGH ***************/
	case VUC_SECURITY_PASS_THROUGH: {
			switch (ubSubFeature) {
			case IDENTIFY:
			case GET_LOG:
			case SET_FEATURE:
			case GET_FEATURE:
			case FW_COMMIT:
			case FW_DL:
			case FORMAT_NVME:
				ubCmdSupport = TRUE;
				break;
			default:
				break;
			}
		}
		break;

	case VUC_RECEIEVE_ENCRYPTION_DATA:
		if (VUC_PROTECT_EN) {
			ubCmdSupport = TRUE;
		}
		break;

	default:
		break;
	}
	return ubCmdSupport;
}

// handle vendor command
void nvme_vuc_dispatch(OPT_HCMD_PTR pCmd)
{
	U8 ubFeature, ubSubFeature;
	U32 ulLength;
	U32 ulBufPara;
	U8 ubRecordCmdFlag = FALSE;
	SECURE_LOG_T ulSecureLog = {0};

	gLastCmd.ubError_type = gVUCVar.ubVucCmdErrStatus;

	/*	Initialize Variable */
	if (INITDONE == pCmd->state) {
		gVUCVar.ubVucCmdErrStatus = 0;

		if (VUC_ADMIN_ENCRYPT_NO_DATA == pCmd->hcmd.info.ubOPCode || VUC_ADMIN_ENCRYPT_READ_DATA == pCmd->hcmd.info.ubOPCode || VUC_ADMIN_ENCRYPT_WRITE_DATA == pCmd->hcmd.info.ubOPCode) {
			// Release ST3C cache
			BufferAdjustST3CCache_AOM_TABLE(BIT_BUFFER_FLOW_VUC_READ_WRITE, GET_BUFFER);

			// Allocate buffer
			BufferAllocateFWLBPBLink(FWLB_VUC_READ_WRITE_BIT, ALLOCATE_NO_USE_CTAG, ALLOCATE_NO_USE_LCA);

			ulBufPara = M_LB_TO_ADDR(LB_ID_FW, gFWLBMgr.Type[FWLB_VUC_READ_WRITE].uwLBOffset);

			VUCProtectFWDecryptCommand(ulBufPara, (VUC_OPT_HCMD_PTR)pCmd);

			// Release buffer
			BufferFreeFWLBPBLink(FWLB_VUC_READ_WRITE_BIT);

			// Return buffer esource to ST3C
			BufferAdjustST3CCache_AOM_TABLE(BIT_BUFFER_FLOW_VUC_READ_WRITE, RETURN_BUFFER);
		}
		if (  (VENDOR_NODATA == pCmd->nvme_sqcmd.vendor.opcode || VENDOR_ENCRYPT_NO_DATA == pCmd->nvme_sqcmd.vendor.opcode) && VUC_ISP_JUMP == ((U8)(pCmd->nvme_sqcmd.raw_data.dw[12] & 0xFF)) ) {
			nvme_set_cmd_sync_mode(pCmd);
		}
	}

	/* assign Length (Unit is Byte) */
	ulLength = DEF_4B * ((U32)pCmd->nvme_sqcmd.raw_data.dw[10]);

	/* byte[48] is Feature */
	ubFeature = (U8) (pCmd->nvme_sqcmd.raw_data.dw[12] & 0xFF);

	/* byte[49] is Sub Feature */
	ubSubFeature = (U8) ((pCmd->nvme_sqcmd.raw_data.dw[12] >> 8) & 0xFF);

	if (!nvme_vuc_support_cmd(pCmd)) {
		ulSecureLog.VUCLog.ubHeader = UNAUTHORIZED_ACCESS_OF_VUC;
		ulSecureLog.VUCLog.ubFeature = ubFeature;
		ulSecureLog.VUCLog.uwTimeStamp = (U16)((guoFWStartTime + (guoOperationTime / MILLISECOND_PER_SECOND)) / VUC_ONE_HOUR_TO_SECONDS);
		nvme_unsecure_log_insert_api(ulSecureLog.ulAll);

		if ((pCmd->hcmd.info.ubOPCode >= VENDOR_NODATA && pCmd->hcmd.info.ubOPCode <= VENDOR_READDATA) || (pCmd->hcmd.info.ubOPCode >= VENDOR_ENCRYPT_NO_DATA && pCmd->hcmd.info.ubOPCode <= VENDOR_ENCRYPT_READ_DATA)) {
			nvme_lid_err_log_fill_info_api (NVME_ERR_LOC_OPCODE, 0x0, 0, 0, &gErrLogData[0]);
			nvme_send_cq_completion (pCmd->hcmd.info.CTAG, GENERIC_CMD, INVALID_FIELD, 0, 0, 0, (U32 *)&gErrLogData[0]); //get phase tag
			pCmd->state = CMD_STATE_FINISH;
		}
		else {
			pCmd->cur_sct = GENERIC_CMD;
			pCmd->cur_sc = INVALID_FIELD;
			pCmd->state = PROCESS_ALL_DONE;
		}
		return;
	}
	else {
		/* Check AP Key Enable */
		if ((gVUCVar.ubApKeyValid == 0) && (ubFeature != VUC_AP_KEY)) {
			pCmd->state = CMD_ERROR;
			gVUCVar.ubVucCmdErrStatus = HOST_ERROR_APKEY_INVALID;
		}
		else {
			if (ubFeature == VUC_SECURITY_PASS_THROUGH) {
				nvme_pass_through(pCmd);
			}
			else {
				if (VENDOR_WRITEDATA == pCmd->nvme_sqcmd.vendor.opcode || VENDOR_ENCRYPT_WRITE_DATA == pCmd->nvme_sqcmd.vendor.opcode) {
					nvme_malloc_dbuf_api (pCmd, ulLength, WCH_WTYPE, BYTE_CMD);
				}
				else if (VENDOR_READDATA == pCmd->nvme_sqcmd.vendor.opcode || VENDOR_ENCRYPT_READ_DATA == pCmd->nvme_sqcmd.vendor.opcode) {
					nvme_malloc_dbuf_api (pCmd, ulLength, WCH_RTYPE, BYTE_CMD) ;
				}
				else {
					pCmd->state = WAIT_FW_PROCESS;
				}

				if ((VENDOR_WRITEDATA == pCmd->nvme_sqcmd.vendor.opcode) || (VENDOR_READDATA == pCmd->nvme_sqcmd.vendor.opcode) || (VENDOR_ENCRYPT_WRITE_DATA == pCmd->nvme_sqcmd.vendor.opcode) || (VENDOR_ENCRYPT_READ_DATA == pCmd->nvme_sqcmd.vendor.opcode )) {
					gulVUCBufAddr = (U32)nvme_cur_HCMD.cur_phyMemAddr;
				}

				if (pCmd->state == WAIT_FW_PROCESS) {
					switch (ubFeature) {

					/*************** No-Data VUC ***************/
					case VUC_AP_KEY:
						VUC_ApKey((VUC_OPT_HCMD_PTR) pCmd);
						pCmd->state = PROCESS_ALL_DONE;
						break;
					case VUC_ISP_JUMP:
						VUC_IspJump((VUC_OPT_HCMD_PTR) pCmd);
						pCmd->state = PROCESS_ALL_DONE;
						break;
					case VUC_CUT_EFUSE:
						VUC_CutEFuse((VUC_OPT_HCMD_PTR) pCmd);
						pCmd->state = PROCESS_ALL_DONE;
						break;
					case VUC_SET_SPECIFIC_GLOBAL_VALUE:
						VUC_SetSpecificGlobalValue((VUC_OPT_HCMD_PTR) pCmd);
						pCmd->state = PROCESS_ALL_DONE;
						break;
					case VUC_ERASE_BLOCK:
						break;
					case VUC_ERASE_PCA:
						VUC_EraseFlash((VUC_OPT_HCMD_PTR) pCmd);
						pCmd->state = PROCESS_ALL_DONE;
						break;
					case VUC_CLEAR_SMART:
						VUC_CleanSMART((VUC_OPT_HCMD_PTR) pCmd);
						pCmd->state = PROCESS_ALL_DONE;
						break;
					case VUC_MAKE_ERROR:
						VUC_MakeError((VUC_OPT_HCMD_PTR) pCmd);
						pCmd->state = PROCESS_ALL_DONE;
						break;
					case VUC_DLMC_PREFORMAT:
						//gDLMC.ubDLMCPreformat = TRUE;
						gpVT->DLMC.B.btDLMCPreformat = TRUE;
						pCmd->state = PROCESS_ALL_DONE;
						break;
					case VUC_SEND_HANDSHAKE_REQUEST:       // 0xC4
						ubRecordCmdFlag = TRUE;
						if (VUC_PROTECT_EN) {
							VUCProtectSendHandshakeRequest((VUC_OPT_HCMD_PTR) pCmd);
						}
						pCmd->state = PROCESS_ALL_DONE;
						break;
					case VUC_DISABLE_VUC:                  // 0xC7
						if (VUC_PROTECT_EN) {
							VUCProtectDisable((VUC_OPT_HCMD_PTR) pCmd);
						}
						pCmd->state = PROCESS_ALL_DONE;
						break;
					case VUC_DO_PCIE_EYE_FLOW: // 0xEE
#if (PS5021_EN && E21_HOST_CHECK)
						pCmd->state = CMD_ERROR;
#else /*(PS5021_EN)*/
						VUC_DoPCIEEyeFlow((VUC_OPT_HCMD_PTR) pCmd);
#endif /*(PS5021_EN)*/
						break;
					/*************** Data-In VUC ***************/
					case VUC_PREFORMAT:
						break;
					case VUC_WRITE_SRAM:
						VUC_WriteSram((VUC_OPT_HCMD_PTR) pCmd);
						pCmd->state = FW_PROCESS_DONE;
						break;
					case VUC_WRITE_REG:
						VUC_WriteReg((VUC_OPT_HCMD_PTR) pCmd);
						pCmd->state = FW_PROCESS_DONE;
						break;
					case VUC_WRITE_INFO:
						VUC_WriteInfo((VUC_OPT_HCMD_PTR) pCmd);
						break;
					case VUC_WRITE_PH:
						VUCWritePH((VUC_OPT_HCMD_PTR) pCmd);
						break;
#if (KINGSTON_EN)
					case VUC_KINGSTON_ANTI_FAKE_DATA:
						VUCKingstonAntiFakeData((VUC_OPT_HCMD_PTR) pCmd);
						pCmd->state = FW_PROCESS_DONE;
						break;
#endif /* (KINGSTON_EN) */
					case VUC_PROG_PAGE:
						VUC_ProgPage((VUC_OPT_HCMD_PTR) pCmd);
						pCmd->state = FW_PROCESS_DONE;
						break;
					case VUC_PROG_PCA:
						VUC_ProgPCA((VUC_OPT_HCMD_PTR) pCmd);
						pCmd->state = FW_PROCESS_DONE;
						break;
					case VUC_CACHE_PROG:
						VUC_CacheWrite((VUC_OPT_HCMD_PTR) pCmd);
						pCmd->state = FW_PROCESS_DONE;
						break;
					case VUC_NAND_VERIFY_WRITE:
						break;
					case VUC_NAND_VERIFY_TRIGGER:
						break;
					case VUC_SCAN_FLASH_SETTING:
						break;
					case VUC_SET_SCAN_WINDOW_PARAMETER:
						break;
					case VUC_SET_SMART_VALUE:
						VUCSetSMARTValue((VUC_OPT_HCMD_PTR) pCmd);
						pCmd->state = FW_PROCESS_DONE;
						break;
					case VUC_SET_SMART_UPDATE_RATIO:
						VUCSetSMARTUpdateRatio((VUC_OPT_HCMD_PTR) pCmd);
						pCmd->state = FW_PROCESS_DONE;
						break;
					case VUC_SEND_ENCRYPTION_DATA:
						ubRecordCmdFlag = TRUE;
						if (VUC_PROTECT_EN) {
							VUCProtectSendEncryptionData((VUC_OPT_HCMD_PTR) pCmd);
						}
						pCmd->state = FW_PROCESS_DONE;
						break;
					case VUC_TCG_PSID:  // FW only supports Data-In sub-feature.
#if (TCG_EN && !BURNER_MODE_EN && !RDT_MODE_EN && !LPM3_LOADER)
						VUCTcgPsid((VUC_OPT_HCMD_PTR) pCmd);
#endif /* (TCG_EN && !BURNER_MODE_EN && !RDT_MODE_EN && !LPM3_LOADER) */
						break;
					/*************** Data-Out VUC ***************/
					case VUC_READ_SYS_INFO:
						VUC_ReadSysInfo((VUC_OPT_HCMD_PTR) pCmd);
						pCmd->state = FW_PROCESS_DONE;
						break;
					case VUC_DUMP_TABLE:
						VUC_DumpTable((VUC_OPT_HCMD_PTR) pCmd);
						pCmd->state = FW_PROCESS_DONE;
						break;
					case VUC_PCA_TRANSLATE:
						VUC_PCATranslate((VUC_OPT_HCMD_PTR) pCmd);
						pCmd->state = FW_PROCESS_DONE;
						break;
					case VUC_FW_FEATURE_CTRL:
						VUC_FwFeatureControl((VUC_OPT_HCMD_PTR) pCmd);
						break;
					case VUC_GET_BLOCK_STATUS:
						VUC_ListBadBlock((VUC_OPT_HCMD_PTR) pCmd);
						pCmd->state = FW_PROCESS_DONE;
						break;
					case VUC_GET_SMART:
						VUC_GetSMARTInfo((VUC_OPT_HCMD_PTR) pCmd);
						pCmd->state = FW_PROCESS_DONE;
						break;
					case VUC_GET_FLASH_INFO:
						VUC_GetFlashInfo((VUC_OPT_HCMD_PTR) pCmd);
						pCmd->state = FW_PROCESS_DONE;
						break;
					case VUC_GET_TRIM_TABLE:
						VUC_GetTrimTable((VUC_OPT_HCMD_PTR) pCmd);
						break;
					case VUC_DUMP_PRAM:
						break;
					case VUC_READ_SRAM:
						VUC_ReadSram((VUC_OPT_HCMD_PTR) pCmd);
						pCmd->state = FW_PROCESS_DONE;
						break;
					case VUC_READ_REG:
						VUC_ReadReg((VUC_OPT_HCMD_PTR) pCmd);
						pCmd->state = FW_PROCESS_DONE;
						break;
					case VUC_DUMP_CODE:
						break;
					case VUC_READ_INFO:
						VUC_DirectReadInfo((VUC_OPT_HCMD_PTR) pCmd);
						pCmd->state = FW_PROCESS_DONE;
						break;
					case VUC_READ_PH:
						VUCReadPH((VUC_OPT_HCMD_PTR) pCmd);
						break;
					case VUC_READ_PAGE:
						if (WAIT_FW_PROCESS == pCmd->state) {
							if (NCS_EN) {
								VUCDirectRead(pCmd);
							}
							else {
								VUC_ReadPage((VUC_OPT_HCMD_PTR)pCmd);
								pCmd->state = FW_PROCESS_DONE;
							}
						}
						break;
					case VUC_READ_PCA:
						if (WAIT_FW_PROCESS == pCmd->state) {
							VUC_ReadPca((VUC_OPT_HCMD_PTR) pCmd);
						}
						pCmd->state = FW_PROCESS_DONE;
						break;
					case VUC_CACHE_READ:
						if (WAIT_FW_PROCESS == pCmd->state) {
							VUC_CacheRead((VUC_OPT_HCMD_PTR) pCmd);
						}
						break;
					case VUC_FLASH_FEATURE_CONFIGURE:
						VUC_FlashFeatureConfigure((VUC_OPT_HCMD_PTR) pCmd);
						pCmd->state = FW_PROCESS_DONE;
						break;
					case VUC_GET_STATUS:
						VUC_GetStatus((VUC_OPT_HCMD_PTR)pCmd);
						pCmd->state = FW_PROCESS_DONE;
						break;
					case VUC_GET_DATA_SIZE:
						VUC_GetVucDataSize((VUC_OPT_HCMD_PTR) pCmd);
						pCmd->state = FW_PROCESS_DONE;
						break;
					case VUC_RECEIEVE_ENCRYPTION_DATA:            // 0xC5
						if (VUC_PROTECT_EN) {
							VUCProtectReceiveEncryptionData((VUC_OPT_HCMD_PTR) pCmd);
						}
						pCmd->state = FW_PROCESS_DONE;
						break;
					case VUC_GET_PCIE_EYE_INFO: // 0xEF
#if (PS5021_EN && E21_HOST_CHECK)
						pCmd->state = CMD_ERROR;
#else /*(PS5021_EN)*/
						VUC_GetPCIEEyeInfo((VUC_OPT_HCMD_PTR) pCmd);
#endif /*(PS5021_EN)*/
						break;
					case VUC_DETECT_PERIPHERAL:
						VUC_DetectPeripheral((VUC_OPT_HCMD_PTR) pCmd);
						pCmd->state = FW_PROCESS_DONE;
						break;
#if (NVME == HOST_MODE)
					case VUC_DETECT_TXOPEN_RXSHORT:
						VUCDetectTxRx((VUC_OPT_HCMD_PTR) pCmd);
						pCmd->state = FW_PROCESS_DONE;
						break;
#endif /*(NVME == HOST_MODE)*/
					default:
						break;
					}

					if (ubRecordCmdFlag) {
						ubRecordCmdFlag = FALSE;
						gLastCmd.ubOP 			= pCmd->hcmd.info.ubOPCode;
						gLastCmd.ubFeature		= ubFeature;
						gLastCmd.ubSubFeature	= ubSubFeature;
					}
				}
			}
		}
	}

	if (CMD_ERROR == pCmd->state) {
		M_UART(NVME_, "\nCmd Err (CQ)");
		ulSecureLog.VUCLog.ubHeader = UNAUTHORIZED_ACCESS_OF_VUC;
		ulSecureLog.VUCLog.ubFeature = ubFeature;
		ulSecureLog.VUCLog.uwTimeStamp = (U16)((guoFWStartTime + (guoOperationTime / MILLISECOND_PER_SECOND)) / VUC_ONE_HOUR_TO_SECONDS);
		pCmd->cur_sct = SCT_ERR_VUC;
		pCmd->cur_sc = VUC_ERROR_STATUS_CODE;
		pCmd->trigdma_err = TRUE;

		if (HOST_ERROR_APKEY_INVALID == gVUCVar.ubVucCmdErrStatus) {
			pCmd->state = PROCESS_ALL_DONE;
		}
		else {
			if (VENDOR_WRITEDATA == pCmd->nvme_sqcmd.vendor.opcode || VENDOR_ENCRYPT_WRITE_DATA == pCmd->nvme_sqcmd.vendor.opcode) {
				pCmd->state = FREERESOURCE;
			}
			else if (VENDOR_READDATA == pCmd->nvme_sqcmd.vendor.opcode || VENDOR_ENCRYPT_READ_DATA == pCmd->nvme_sqcmd.vendor.opcode) {
				pCmd->state = PROCESS_4K_DONE;//avoid to do free 2 times BMU buffer(APU,FW) cause BMU abnormal on read command orientation
			}
			else {
				pCmd->state = PROCESS_ALL_DONE;
			}
		}
	}

	if (PROCESS_ALL_DONE == pCmd->state) {
		if (( (GENERIC_CMD == pCmd->cur_sct) && (SUCCESS_CPL == pCmd->cur_sc) ) && (VUC_WRITE_INFO == ubFeature)) { //Reduce storage host info. times
			nvme_set_save_host_info_api(1); //set host info enable
			pCmd->state = WAIT_FW_SAVE_INFO;
		}
		else if (VUC_AP_KEY != ubFeature) {
			gVUCVar.ubApKeyValid = 0;
		}
	}

	if (FW_SAVE_INFO_DONE == pCmd->state) {
		if (VUC_WRITE_INFO == ubFeature) {
			gVUCVar.ubApKeyValid = 0;
		}
		pCmd->state = CMD_STATE_CMD_CPL;
	}

	if (CMD_STATE_FINISH == pCmd->state) {
		if (VUC_SECURITY_PASS_THROUGH == ubFeature) {
			gVUCVar.ubApKeyValid = 0;
		}
	}

	// handle and return completion of NVME vendor commmand
	if ((pCmd->hcmd.info.ubOPCode >= VENDOR_NODATA && pCmd->hcmd.info.ubOPCode <= VENDOR_READDATA) || (pCmd->hcmd.info.ubOPCode >= VENDOR_ENCRYPT_NO_DATA && pCmd->hcmd.info.ubOPCode <= VENDOR_ENCRYPT_READ_DATA)) {
		if (pCmd->state == FW_PROCESS_DONE) {
			if (pCmd->hcmd.info.ubOPCode == VENDOR_WRITEDATA || pCmd->hcmd.info.ubOPCode == VENDOR_ENCRYPT_WRITE_DATA) {
				pCmd->state = FREERESOURCE;
			}
			else if (pCmd->hcmd.info.ubOPCode == VENDOR_READDATA || pCmd->hcmd.info.ubOPCode == VENDOR_ENCRYPT_READ_DATA) {
				pCmd->state = TRIGDMA;
			}
		}

		if (pCmd->state == PROCESS_ALL_DONE) {
			if (0 == ulSecureLog.VUCLog.ubHeader) {
				ulSecureLog.VUCLog.ubHeader = AUTHORIZED_ACCESS_OF_VUC;
				ulSecureLog.VUCLog.ubFeature = ubFeature;
				ulSecureLog.VUCLog.uwTimeStamp = (U16)((guoFWStartTime + (guoOperationTime / MILLISECOND_PER_SECOND)) / VUC_ONE_HOUR_TO_SECONDS);
			}
			nvme_unsecure_log_insert_api(ulSecureLog.ulAll);
			pCmd->state = CMD_STATE_CMD_CPL;
		}

		if (pCmd->state >= CMD_STATE_CMD_CPL) {
			if (ubFeature != VUC_SECURITY_PASS_THROUGH) {
				if ((GENERIC_CMD != pCmd->cur_sct) || (SUCCESS_CPL != pCmd->cur_sc)) {
					nvme_lid_err_log_fill_info_api (pCmd->cmd_err_byte, pCmd->cmd_err_bit, 0, 0, &gErrLogData[0]);
				}
				nvme_send_cq_completion (pCmd->hcmd.info.CTAG, pCmd->cur_sct, pCmd->cur_sc, 0, 0, 0, (U32 *)&gErrLogData[0]); //get phase tag
			}
			//pCmd->state = WAIT_FW_SAVE_INFO;
			//pCmd->valid = 0;
			pCmd->state = CMD_STATE_FINISH;
		}
	}
}

// handle Kingston VUC command
void nvme_kingston_vuc_dispatch(OPT_HCMD_PTR pCmd)
{
	U32 ulWLength = DEF_512B;
	U32 ulRLength = DEF_KB(1);
	U8 ubSubFeatureID;
	U8 ubCMDChecksum = 0;
	U8 ubDWord12[4];
	U8 ubDWord13[4];
	U8 ubi;

	gLastCmd.ubError_type = gVUCVar.ubVucCmdErrStatus;

	/*	Initialize Variable */
	if (INITDONE == pCmd->state) {
		gVUCVar.ubVucCmdErrStatus = 0;
	}

	for (ubi = 0; ubi < 4; ubi++) {
		ubDWord12[ubi] = (U8) ((pCmd->nvme_sqcmd.raw_data.dw[12] >> (8 * ubi)) & 0xFF);
	}
	for (ubi = 0; ubi < 4; ubi++) {
		ubDWord13[ubi] = (U8) ((pCmd->nvme_sqcmd.raw_data.dw[13] >> (8 * ubi)) & 0xFF);
	}

	/* DW12,byte[0](byte[0]) is Sub Feature ID */
	ubSubFeatureID = ubDWord12[0];

	if (VENDOR_KINGSTON_VERIFY_ANTIFAKE_WRITE_DATA == pCmd->nvme_sqcmd.vendor.opcode) {
		nvme_malloc_dbuf_api (pCmd, ulWLength, WCH_WTYPE, BYTE_CMD);
	}
	else if (VENDOR_KINGSTON_VERIFY_ANTIFAKE_READ_DATA == pCmd->nvme_sqcmd.vendor.opcode) {
		nvme_malloc_dbuf_api (pCmd, ulRLength, WCH_RTYPE, BYTE_CMD) ;
	}
	else {
		pCmd->state = WAIT_FW_PROCESS;
	}

	if ( WAIT_FW_PROCESS == pCmd->state ) {
		if (VENDOR_KINGSTON_VERIFY_ANTIFAKE_NO_DATA == pCmd->nvme_sqcmd.vendor.opcode) {
			ubCMDChecksum = VUCKingstonVerifyAntiFakeChecksum((VUC_OPT_HCMD_PTR) pCmd, ubDWord12, ubDWord13);
			if (ubCMDChecksum) {
				pCmd->state = CMD_ERROR;
			}
			else {
				pCmd->state = PROCESS_ALL_DONE;
			}
		}
		else if (VENDOR_KINGSTON_VERIFY_ANTIFAKE_WRITE_DATA == pCmd->nvme_sqcmd.vendor.opcode) {
			ubCMDChecksum = VUCKingstonVerifyAntiFakeChecksum((VUC_OPT_HCMD_PTR) pCmd, ubDWord12, ubDWord13);
			if (ubCMDChecksum) {
				pCmd->state = CMD_ERROR;
			}
			else {
				if (gST3CRAMDist.ulDoingFlow & BIT_BUFFER_FLOW_VUC_KINGSTON) {
					M_UART(NVME_, "\nVerify_Antifake_Write re-allocate 4K buffer!\n");
					VUCReleaseKingston4KBuf();
				}
				// Get ST3C cache
				BufferAdjustST3CCache_AOM_TABLE(BIT_BUFFER_FLOW_VUC_KINGSTON, GET_BUFFER);

				// Allocate 4K buffer for K VUC
				BufferAllocateFWLBPBLink(FWLB_VUC_KINGSTON_BIT, ALLOCATE_NO_USE_CTAG, ALLOCATE_NO_USE_LCA);

				VUCKingstonVerifyAntiFakeWrite(pCmd->cur_phyMemAddr);
				pCmd->state = FW_PROCESS_DONE;
			}

		}
		else if (VENDOR_KINGSTON_VERIFY_ANTIFAKE_READ_DATA == pCmd->nvme_sqcmd.vendor.opcode) {
			ubCMDChecksum = VUCKingstonVerifyAntiFakeChecksum((VUC_OPT_HCMD_PTR) pCmd, ubDWord12, ubDWord13);
			if (ubCMDChecksum) {
				pCmd->state = CMD_ERROR;
			}
			else {
				if (VUCKingstonVerifyAntiFakeRead(pCmd->cur_phyMemAddr)) {
					pCmd->state = CMD_ERROR;
				}
				else {
					pCmd->state = FW_PROCESS_DONE;
				}

				if ((gST3CRAMDist.ulDoingFlow & BIT_BUFFER_FLOW_VUC_KINGSTON)) {
					//Release Buffer
					VUCReleaseKingston4KBuf();
					M_UART(NVME_, "\nAnti-Fake_Read(0xE6) done, release Kingston4KBuffer\n");
				}
			}
		}
		else {
			pCmd->state = CMD_ERROR;
			gVUCVar.ubVucCmdErrStatus = HOST_ERROR_CMD_FAIL;
		}
	}

	if (CMD_ERROR == pCmd->state) {
		M_UART(NVME_, "\nCmd Err (CQ)");
		pCmd->state = PROCESS_ALL_DONE;
		pCmd->cur_sct = SCT_ERR_VUC;
		pCmd->cur_sc = VUC_ERROR_STATUS_CODE;
	}

	// handle and return completion of NVME vendor commmand
	if (( VENDOR_KINGSTON_VERIFY_ANTIFAKE_NO_DATA <= pCmd->hcmd.info.ubOPCode)  && (VENDOR_KINGSTON_VERIFY_ANTIFAKE_READ_DATA >= pCmd->hcmd.info.ubOPCode )) {
		if (FW_PROCESS_DONE == pCmd->state ) {
			if (VENDOR_KINGSTON_VERIFY_ANTIFAKE_WRITE_DATA == pCmd->hcmd.info.ubOPCode) {
				pCmd->state = FREERESOURCE;
			}
			else if (VENDOR_KINGSTON_VERIFY_ANTIFAKE_READ_DATA == pCmd->hcmd.info.ubOPCode) {
				pCmd->state = TRIGDMA;
			}
		}

		if (PROCESS_ALL_DONE == pCmd->state ) {
			pCmd->state = CMD_STATE_CMD_CPL;
		}

		if (CMD_STATE_CMD_CPL <= pCmd->state) {
			nvme_send_cq_completion (pCmd->hcmd.info.CTAG, pCmd->cur_sct, pCmd->cur_sc, 0, 0, 0, (U32 *)&gErrLogData[0]); //get phase tag
			pCmd->state = CMD_STATE_FINISH;
		}
	}
}//end of "nvme_K_vuc_dispatch()"

#if (VUC_MICRON_NICKS_EN)
void nvme_micron_vuc_dispatch(OPT_HCMD_PTR pCmd)
{
	U8 ubInputHeaderFormatVersion = 0;
	U8 ubInputDataFormatVersion = 0;
	U16 uwCmdClass = 0;
	U16 uwCmdCode = 0;

	if (INITDONE == pCmd->state) {
		gVUCVar.ubVucCmdErrStatus = 0;
	}

	U32 ulLength = DEF_4B * ((U32)pCmd->nvme_sqcmd.raw_data.dw[10]);

	if ( VENDOR_MICRON_VS_INPUT == pCmd->hcmd.info.ubOPCode ) {
		nvme_malloc_dbuf_api (pCmd, ulLength, WCH_WTYPE, BYTE_CMD);
		M_UART(VUC_MICRON_, "\nAllocate buffer by input");
	}
	else if ( VENDOR_MICRON_VS_OUTPUT == pCmd->hcmd.info.ubOPCode ) {
		nvme_malloc_dbuf_api (pCmd, ulLength, WCH_RTYPE, BYTE_CMD) ;
		M_UART(VUC_MICRON_, "\nAllocate buffer by output");
	}
	else {
		pCmd->state = WAIT_FW_PROCESS;
	}

	if ( WAIT_FW_PROCESS == pCmd->state ) {

		// Wait for FW idle
		if ( VENDOR_MICRON_VS_INPUT == pCmd->hcmd.info.ubOPCode) {
			if (0 == (pCmd->done_LCA - 1)) {
				M_UART(VUC_MICRON_, "\n pCmd->done_LCA:0");
				if (FALSE == BufferAllocateBuf(BUF_ALLOCATE_MODE_COPY_BUFFER, BUF_USING_STATE_MICRON_VUC)) {
					M_UART(VUC_MICRON_, "\n Fail allocate 96k buffer");
				}
				else {
					gulResponseBufferAddr = 0;
					gulGNPTLastCmdOffset = 0;
					gulGNPTResponsePayloadSize = 0;

					gubMicronVUCBufferFlag = 1;
					M_UART(VUC_MICRON_, "\n Success allocate 96k buffer");

					BufferAdjustST3CCache_AOM_TABLE(BIT_BUFFER_FLOW_VUC_READ_WRITE, GET_BUFFER);
					BufferAllocateFWLBPBLink(FWLB_VUC_READ_WRITE_BIT, ALLOCATE_NO_USE_CTAG, ALLOCATE_NO_USE_LCA);
					gulMicronVUCBufferAddr = M_LB_TO_ADDR(LB_ID_FW, gFWLBMgr.Type[FWLB_VUC_READ_WRITE].uwLBOffset);
					M_UART(VUC_MICRON_, "\n Success allocate 20k buffer");
				}
			}
			else {
				M_UART(VUC_MICRON_, "\n pCmd->done_LCA:%x", pCmd->done_LCA);
			}
		}

		// Do Micron Cmd
		if (gubMicronVUCBufferFlag) {
			while ((TRUE == M_COP0_GET_D2H_PATH_BUSY()) || (FALSE == MRCheckCOP0DecoderIdle()) || (FALSE == MRCheckCOP0SlaveIdle())) {
				FWCop0Waiting();
			}

			if ( VENDOR_MICRON_VS_INPUT == pCmd->hcmd.info.ubOPCode) {
				DMACParam_t DMACParam;
				DMACParam.ulSourceAddr = pCmd->cur_phyMemAddr;
				DMACParam.ulDestAddr = gulMicronVUCBufferAddr + (pCmd->done_LCA - 1) * BC_4KB;
				DMACParam.ul32ByteNum = SIZE_IN_32B(BC_4KB) ;
				gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
				DMACCopy(DMAC_MODE_NORMAL_QUEUE, &DMACParam, gulDMACDirectWaitDone_Callback, 0);
				while (FALSE == gDMACDirectWaitCQInfo.ubDoneFlag) {
					DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
				}

				if (pCmd->done_LCA == pCmd->total_LCA) {

					VUCMicronParsingInputHeader(gulMicronVUCBufferAddr, &ubInputHeaderFormatVersion, &ubInputDataFormatVersion, &uwCmdClass, &uwCmdCode);
					gubMicronLastCmdCode = uwCmdCode;
					gubMicronLastCmdClass = uwCmdClass;
					if (VUC_MICRON_NAND_VS_COMMANDS == uwCmdClass) {
						switch (uwCmdCode) {
#if (VT_SWEEP_EN)
						case VUC_MICRON_GET_VT_SWEEP:
							gulResponseBufferAddr = M_LB_TO_ADDR(LB_ID_FW, gFWLBMgr.Type[FWLB_COPYBUFFER].uwLBOffset);
							gpVTDBUF->WriteProtect.btMicronNandMode = TRUE;
							VUCMicronGetVTSweep(pCmd->cur_phyMemAddr);
							pCmd->state = FW_PROCESS_DONE;
							break;
#endif /*VT_SWEEP_EN*/
#if (VUC_MICRON_NAND_VS_COMMANDS_EN)
#if (IM_N28)
						case VUC_MICRON_GET_RAW_BIT_ERROR_COUNT:
							gulResponseBufferAddr = M_LB_TO_ADDR(LB_ID_FW, gFWLBMgr.Type[FWLB_COPYBUFFER].uwLBOffset);
							VUCMicronGetRawBitErrorCnt(gulMicronVUCBufferAddr, gulResponseBufferAddr); //VUC cmd for N28
							pCmd->state = FW_PROCESS_DONE;
							break;
						case VUC_MICRON_GET_DEFECT_LIST:
							gulResponseBufferAddr = M_LB_TO_ADDR(LB_ID_FW, gFWLBMgr.Type[FWLB_COPYBUFFER].uwLBOffset);
							VUCMicronGetDefectList(gulMicronVUCBufferAddr, gulResponseBufferAddr);
							pCmd->state = FW_PROCESS_DONE;
							break;
						case VUC_MICRON_GET_NAND_BLOCK_ERASE_COUNT:
							gulResponseBufferAddr = M_LB_TO_ADDR(LB_ID_FW, gFWLBMgr.Type[FWLB_COPYBUFFER].uwLBOffset);
							VUCMicronGetEraseCount(gulResponseBufferAddr);
							pCmd->state = FW_PROCESS_DONE;
							break;
#endif /*(IM_N28)*/
						case VUC_MICRON_GET_BLOCK_NAND_MODE:
							gulResponseBufferAddr = M_LB_TO_ADDR(LB_ID_FW, gFWLBMgr.Type[FWLB_COPYBUFFER].uwLBOffset);
							VUCMicronGetBlkNandMode(gulMicronVUCBufferAddr, gulResponseBufferAddr);
							pCmd->state = FW_PROCESS_DONE;
							break;
						case VUC_MICRON_GET_MLBI:
							gulResponseBufferAddr = M_LB_TO_ADDR(LB_ID_FW, gFWLBMgr.Type[FWLB_COPYBUFFER].uwLBOffset);
							VUCMicronGetMLBi(gulMicronVUCBufferAddr, gulResponseBufferAddr);
							pCmd->state = FW_PROCESS_DONE;
							break;
						case VUC_MICRON_SET_MLBI:
							gulResponseBufferAddr = M_LB_TO_ADDR(LB_ID_FW, gFWLBMgr.Type[FWLB_COPYBUFFER].uwLBOffset);
							VUCMicronSetMLBi(gulMicronVUCBufferAddr, gulResponseBufferAddr);
							pCmd->state = FW_PROCESS_DONE;
							break;
#endif /*(VUC_MICRON_NAND_VS_COMMANDS_EN)*/
						case VUC_MICRON_GNPT:
							M_UART(VUC_MICRON_, "\n Micron GNPT");
							VUCMicronGNPTMangement(gulMicronVUCBufferAddr);
							pCmd->state = FW_PROCESS_DONE;
							break;
						case VUC_MICRON_GET_ERROR_RECOVERY_STATISTICS:
							gulResponseBufferAddr = M_LB_TO_ADDR(LB_ID_FW, gFWLBMgr.Type[FWLB_COPYBUFFER].uwLBOffset);
							VUCMicronGetNANDErrorRecoveryStatistics(gulResponseBufferAddr);
							pCmd->state = FW_PROCESS_DONE;
							break;
						case VUC_MICRON_GET_BEC:
							gulResponseBufferAddr = M_LB_TO_ADDR(LB_ID_FW, gFWLBMgr.Type[FWLB_COPYBUFFER].uwLBOffset);
							VUCMicronGetBEC(gulResponseBufferAddr);
							pCmd->state = FW_PROCESS_DONE;
							break;
#if (UNIFIED_LOG_EN)
						case VUC_MICRON_GET_UNIFIED_EVENT_LOG:
							gulResponseBufferAddr = M_LB_TO_ADDR(LB_ID_FW, gFWLBMgr.Type[FWLB_COPYBUFFER].uwLBOffset);
							VUCMicronGetUnifiedLog(gulResponseBufferAddr);
							pCmd->state = FW_PROCESS_DONE;
#endif
						default:
							M_UART(VUC_MICRON_, "\nInvalid CmdCode");
							pCmd->state = FW_PROCESS_DONE;
							break;
						}
					}
					else if (VUC_MICRON_COMMON_VS_COMMANDS == uwCmdClass) {
						switch (uwCmdCode) {
#if (VUC_MICRON_COMMON_VS_COMMANDS_EN)
						case VUC_MICRON_CLEAR_EVENT_LOG:
							// Release Copy buffer in order to do Trim
							BufferFreeBuf_AOM_TABLE_UPDATE(BUF_ALLOCATE_MODE_COPY_BUFFER, BUF_USING_STATE_MICRON_VUC);
							VUCMicronClearEventLog(gulMicronVUCBufferAddr, pCmd->cur_phyMemAddr);
							pCmd->state = FW_PROCESS_DONE;
							break;
						case VUC_MICRON_GET_DRIVE_CONFIGURE:
							gulResponseBufferAddr = M_LB_TO_ADDR(LB_ID_FW, gFWLBMgr.Type[FWLB_COPYBUFFER].uwLBOffset);
							VUCMicronGetDeviceConfigData(gulResponseBufferAddr);
							pCmd->state = FW_PROCESS_DONE;
							break;
						case VUC_MICRON_GET_ALL_TEMPERATURE:
							gulResponseBufferAddr = M_LB_TO_ADDR(LB_ID_FW, gFWLBMgr.Type[FWLB_COPYBUFFER].uwLBOffset);
							VUCMicronGetTemperatureSensor(gulResponseBufferAddr);
							pCmd->state = FW_PROCESS_DONE;
							break;
#endif /*(VUC_MICRON_COMMON_VS_COMMANDS_EN)*/
						case VUC_MICRON_GET_L2P:
							gulResponseBufferAddr = M_LB_TO_ADDR(LB_ID_FW, gFWLBMgr.Type[FWLB_COPYBUFFER].uwLBOffset);
							VUCMicronLogicalToPhysicalAddress((GetLogicaltoPhysicalAddressInputData_t *) (gulMicronVUCBufferAddr + VUC_MICRON_LOGICAL_TO_PHYSICAL_ADDRESS_HEADER_LENGTH), gulResponseBufferAddr);
							pCmd->state = FW_PROCESS_DONE;
							break;
						case VUC_MICRON_GET_P2L:
							gulResponseBufferAddr = M_LB_TO_ADDR(LB_ID_FW, gFWLBMgr.Type[FWLB_COPYBUFFER].uwLBOffset);
							VUCMicronPhysicaltoLogicalAddress(gulMicronVUCBufferAddr, gulResponseBufferAddr);
							pCmd->state = FW_PROCESS_DONE;
							break;

						default:
							M_UART(VUC_MICRON_, "\nInvalid CmdCode");
							pCmd->state = FW_PROCESS_DONE;
							break;
						}

					}
					else {
						pCmd->state = FW_PROCESS_DONE;

					}
				}
				else {
					//Wait for receiving all 4K data
					pCmd->state = FW_PROCESS_DONE;
				}
			}

			else if (VENDOR_MICRON_VS_OUTPUT == pCmd->hcmd.info.ubOPCode) {
				M_UART(VUC_MICRON_, "\nVUC Output!");
				VUCMicronResponse(pCmd);
			}

		}

	}

	if (CMD_ERROR == pCmd->state) {
		pCmd->state = PROCESS_ALL_DONE;
		pCmd->cur_sct = SCT_ERR_VUC;
		pCmd->cur_sc = VUC_ERROR_STATUS_CODE;
	}

	if (( VENDOR_MICRON_VS_INPUT == pCmd->hcmd.info.ubOPCode) || (VENDOR_MICRON_VS_OUTPUT == pCmd->hcmd.info.ubOPCode )) {
		if (FW_PROCESS_DONE == pCmd->state ) {
			if (VENDOR_MICRON_VS_INPUT == pCmd->hcmd.info.ubOPCode) {
				pCmd->state = FREERESOURCE;
			}
			else if (VENDOR_MICRON_VS_OUTPUT == pCmd->hcmd.info.ubOPCode) {
				pCmd->state = TRIGDMA;
			}
		}

		if (PROCESS_ALL_DONE == pCmd->state ) {
			pCmd->state = CMD_STATE_CMD_CPL;
		}

		if (CMD_STATE_CMD_CPL <= pCmd->state) {
			nvme_send_cq_completion (pCmd->hcmd.info.CTAG, pCmd->cur_sct, pCmd->cur_sc, 0, 0, 0, (U32 *)&gErrLogData[0]); //get phase tag
			pCmd->state = CMD_STATE_FINISH;

			if ((VENDOR_MICRON_VS_OUTPUT == pCmd->hcmd.info.ubOPCode) && ( VUC_MICRON_RESPONSE_STATUS_NO_ERROR == guwVUCMicronResponseStatusCode) && (!gubMicronVUCCMDNotDone)) {
				if (!((VUC_MICRON_CLEAR_EVENT_LOG == gubMicronLastCmdCode) && ( VUC_MICRON_COMMON_VS_COMMANDS == gubMicronLastCmdClass))) {
					BufferFreeBuf_AOM_TABLE_UPDATE(BUF_ALLOCATE_MODE_COPY_BUFFER, BUF_USING_STATE_MICRON_VUC);
				}
				M_UART(VUC_MICRON_, "\n Success free 96k buffer");
				BufferFreeFWLBPBLink(FWLB_VUC_READ_WRITE_BIT);
				BufferAdjustST3CCache_AOM_TABLE(BIT_BUFFER_FLOW_VUC_READ_WRITE, RETURN_BUFFER);
				M_UART(VUC_MICRON_, "\n Success free 20k buffer");
				gpVTDBUF->WriteProtect.btMicronNandMode = FALSE;
				if (0 == gpVTDBUF->WriteProtect.ubAll) {
					gubFTLWriteProtectHandle.btFinalVTProgramDone = FALSE;
					gubFTLWriteProtectHandle.btFinalDriveLogProgramDone = FALSE;
					gubFTLWriteProtectHandle.ubNVMEWriteProtectStandbyDone = FALSE;
				}
				gubMicronVUCBufferFlag = 0;
				gulVUCMicronVUCOffset = 0;
				//UartPrintf("\nVUC done Free all buffer");
			}
		}
	}

}
#endif /*(VUC_MICRON_NICKS_EN)*/

U8 NVMEVUCCheckWriteProtect(OPT_HCMD_PTR pCmd)
{
	U8 ubCmdAbort = TRUE;
	U8 ubOPCode = gVUCSecuritySPSQ64BVariable.uldw[0];
	U8 ubFeature = (U8) (gVUCSecuritySPSQ64BVariable.uldw[12] & 0xFF);
	U8 ubSubFeature = (U8) ((gVUCSecuritySPSQ64BVariable.uldw[12] >> 8) & 0xFF);
	switch (ubOPCode) {
	case VENDOR_NODATA:
	case VENDOR_ENCRYPT_NO_DATA:
		if ((VUC_SECURITY_PASS_THROUGH == ubFeature) && ((FORMAT_NVME == ubSubFeature) || (SANITIZE == ubSubFeature))) {
			ubCmdAbort = TRUE;
			gVUCVar.ubApKeyValid = 0;
			gVUCVar.ubSecspKeyValid = 0;
			gVUCVar.VUCSkipLPMBMP.btVUCSecurity = 0;
		}
		else {
			ubCmdAbort = FALSE;
		}
		break;
	case VENDOR_WRITEDATA:
	case VENDOR_ENCRYPT_WRITE_DATA:
		if ((VUC_SECURITY_PASS_THROUGH == ubFeature) || (VUC_SEND_ENCRYPTION_DATA == ubFeature)) {
			ubCmdAbort = FALSE;
		}
		else {
			gVUCVar.ubApKeyValid = 0;
			gVUCVar.ubSecspKeyValid = 0;
			gVUCVar.VUCSkipLPMBMP.btVUCSecurity = 0;
		}
		break;
	default:
		break;
	}
	return ubCmdAbort;
}

void nvme_vucsp_record_sq(OPT_HCMD_PTR_t pCmd)
{
	U8 ubi;
	// Record vendor command
	for (ubi = 0; ubi < 16; ubi++) {
		gVUCSecuritySPSQ64BVariable.uldw[ubi] = ((volatile U32 *)pCmd->cur_phyMemAddr)[ubi];
		M_UART(NVME_, "\nSendSQ dw[%d] = %x", ubi, gVUCSecuritySPSQ64BVariable.uldw[ubi]);
	}
}

void nvme_vucsp_get_sq(OPT_HCMD_PTR_t pCmd)
{
	U8 ubi;
	U32 ulBufPara;
	pCmd->nvme_sqcmd.raw_data.dw[0] = gVUCSecuritySPSQ64BVariable.uldw[0];
	for (ubi = 10; ubi < 16; ubi++) {
		pCmd->nvme_sqcmd.raw_data.dw[ubi] = gVUCSecuritySPSQ64BVariable.uldw[ubi];
	}

	if (VUC_ADMIN_ENCRYPT_NO_DATA == pCmd->nvme_sqcmd.raw_data.dw[0] || VUC_ADMIN_ENCRYPT_READ_DATA == pCmd->nvme_sqcmd.raw_data.dw[0] || VUC_ADMIN_ENCRYPT_WRITE_DATA == pCmd->nvme_sqcmd.raw_data.dw[0]) {
		// Release ST3C cache
		BufferAdjustST3CCache_AOM_TABLE(BIT_BUFFER_FLOW_VUC_READ_WRITE, GET_BUFFER);

		// Allocate buffer
		BufferAllocateFWLBPBLink(FWLB_VUC_READ_WRITE_BIT, ALLOCATE_NO_USE_CTAG, ALLOCATE_NO_USE_LCA);

		ulBufPara = M_LB_TO_ADDR(LB_ID_FW, gFWLBMgr.Type[FWLB_VUC_READ_WRITE].uwLBOffset);

		VUCProtectFWDecryptCommand(ulBufPara, (VUC_OPT_HCMD_PTR)pCmd);

		// Release buffer
		BufferFreeFWLBPBLink(FWLB_VUC_READ_WRITE_BIT);

		// Return buffer esource to ST3C
		BufferAdjustST3CCache_AOM_TABLE(BIT_BUFFER_FLOW_VUC_READ_WRITE, RETURN_BUFFER);
	}
}

AOM_VUC void nvme_vucsp_get_log(OPT_HCMD_PTR_t pCmd)
{
	pCmd->nvme_sqcmd.adm.getlog.logid = (U8)gVUCSecuritySPSQ64BVariable.uldw[0];
	pCmd->nvme_sqcmd.raw_data.dw[12] = 0;
	pCmd->nvme_sqcmd.raw_data.dw[13] = 0;
	nvme_getlog_fill_api (pCmd, pCmd->cur_phyMemAddr);
}

AOM_VUC void nvme_vucsp_identify(OPT_HCMD_PTR_t pCmd)
{
	U32 ulDw10 = 0;
	U32 ulNSID = 0;
	ulDw10 = (U8)gVUCSecuritySPSQ64BVariable.uldw[0];

	ulNSID = (0 == ulDw10);

	nvme_identify_fill_api (ulDw10, ulNSID, pCmd->cur_phyMemAddr);
}

void nvme_vucsp_get_cq(OPT_HCMD_PTR_t pCmd)
{
	U8 ubi;
	/*	Write 16B CQ To Buffer */
	for (ubi = 0; ubi < 4; ubi++) {
		((volatile U32 *)pCmd->cur_phyMemAddr)[ubi] = gVUCSecuritySPCQ16BVariable.uldw[ubi];
	}
}

void nvme_vucsp_fetch_cq(OPT_HCMD_PTR_t pCmd)
{
	NVMECPL_t cpl;

	cpl.ulDW[0] = 0;
	cpl.ulDW[1] = 0;

	gVUCVar.ubVucCmdErrStatus = 0;

	cpl.CTAG = pCmd->hcmd.info.CTAG;
	cpl.ulDW[0] = (U32) cpl.CTAG;
	cpl.ulDW[1] = (U32) 0;

	if (CMD_ERROR == pCmd->state) {
		M_UART(NVME_, "\nCmd Err (CQ)");
		cpl.ubSCT = SCT_ERR_VUC;
		cpl.ubSC = VUC_ERROR_STATUS_CODE;
	}

	/*	Record 16B CQ */
	gVUCSecuritySPCQ16BVariable.uldw[0] = 0;
	gVUCSecuritySPCQ16BVariable.uldw[1] = 0;
	gVUCSecuritySPCQ16BVariable.uldw[2] = cpl.ulDW[1];
	gVUCSecuritySPCQ16BVariable.uldw[3] = cpl.ulDW[0];
}

void VUCDirectRead(OPT_HCMD_PTR pCmd)
{
	COP0Status_t eCOP0Status;
	cmd_table_t uoCallbackInfo = {0};
	COP0ReadSQPara_t ReadSQPara = {{0}};
	BUF_TYPE_t ulBufPara = {0};
	COP0_Attr_t ulCOP0AttributeBackup;  // backup cop0 attr
	FWSetPara_t ulFWSet[FRAMES_PER_PAGE] = {{0}};

	U32 ulPCA;
	U8 ubBank, ubCh, ubQ, ubPlane, ubALU, ubFrameIdx, ubLMU = 0, ubLUN = 0, ubNCSHaveEnteredRetryFlag = 0;
	U16 uwPage, uwBlock;
	// indicate if direct read comes from NCS verification flow
	U8 ubNCSVerifyFlag = pCmd->nvme_sqcmd.raw_u8_data.by[60];
	ubALU = pCmd->nvme_sqcmd.raw_u8_data.by[49];
	uwPage = pCmd->nvme_sqcmd.raw_u16_data.wd[27];
	uwBlock = pCmd->nvme_sqcmd.raw_u16_data.wd[28];
	ubQ = pCmd->nvme_sqcmd.raw_u8_data.by[59];
	ubFrameIdx = pCmd->nvme_sqcmd.raw_u8_data.by[40];
	ubLMU = pCmd->nvme_sqcmd.raw_u8_data.by[58];
	ubCh = M_MOD(ubQ, gubPlanesPerBurst);
	ubBank = M_DIV(ubQ, gubPlanesPerBurst);
	ubPlane = uwBlock & gubBurstsPerBankMask;
	uwBlock = uwBlock >> gubBurstsPerBankLog;
	ubLUN = uwBlock >> gPCARule_Block.ubBit_No;	//Parse LUN (Die) Index

	ulPCA = 0;
	ulPCA |= (ubFrameIdx & gPCARule_Entry.ulMask) << gPCARule_Entry.ubShift[ubALU];		// Frame(Entry)
	ulPCA |= (ubPlane & gPCARule_Plane.ulMask) << gPCARule_Plane.ubShift[ubALU];		// Plane
	ulPCA |= (ubLMU & gPCARule_LMU.ulMask) << gPCARule_LMU.ubShift[ubALU];
	ulPCA |= ((ubCh + (ubBank << gPCARule_Channel.ubBit_No)) & gPCARule_G_CE.ulMask) << gPCARule_G_CE.ubShift[ubALU];
	ulPCA |= (GET_FPAGE(uwPage) & gPCARule_Page.ulMask) << gPCARule_Page.ubShift[ubALU]; 	// Page
	ulPCA |= (uwBlock & gPCARule_Block.ulMask) << gPCARule_Block.ubShift[ubALU];		// Block
	ulPCA |= (ubLUN & gPCARule_LUN.ulMask) << gPCARule_LUN.ubShift[ubALU];			// LUN

	// NCS directly read Pln 1, Blk 0, Pg 0 in all CH to detect CE
	// Change LDPC mode to 7 (ID page) to avoid UNC INT
	if (NCS_EN) {
		//for Micron Open/Close page flow
		gubNCSTLCOpenFlow = pCmd->nvme_sqcmd.raw_u8_data.by[61] & BIT0;
		gubNCSQLC2ndPassFlow = pCmd->nvme_sqcmd.raw_u8_data.by[61] & BIT2;
		if ((0 == uwBlock) && (0 == uwPage)) { /* parasoft-suppress BD-PB-CC "get from HW"*/
			FlaSwitchLDPCHandler(SET_ECC_MODE_7);
		}
#if (RETRY_MICRON_NICKS)
		gubNCSAssignACRR = pCmd->nvme_sqcmd.raw_u8_data.by[62] & 7;
#endif /*(RETRY_MICRON_NICKS)*/
	}

	// NCS doesn't need conversion, enable bypass
	if (NCS_EN) {
		ulCOP0AttributeBackup.ulAll = M_COP0_GET_ATTR(COP0_MT_ATTR1);
		M_COP0_GET_ATTR(COP0_MT_ATTR1) &= CLR_ATTR_LCA_COMPARE_DIS;
		if (ubNCSVerifyFlag) {

			//WorkaRound, Clear over errbit threshold INT
			M_FIP_EOT_INTERRUPT_DIS(); /* ECC Over Bits */

			M_COP0_GET_ATTR(COP0_MT_ATTR1) |= SET_ATTR_CONV_BYPASS_EN;
#if (RETRY_INITIAL_READ_EN)
			if (FALSE == gubNeedSendNCSFlag) {
				RetryResetLRUTable();
			}
#endif /*(RETRY_INITIAL_READ_EN)*/
			gubNeedSendNCSFlag = TRUE;
			if (NCS_SBRAID_EN) {
				//Modify VBRMP for later RAID flow , for by pass original remap to get right RAID pattern
				gpuwVBRMP[uwBlock].B.ToRUT_Unit = uwBlock;
			}
		}
		//Wait COP0 Idle
		while ((TRUE == M_COP0_GET_D2H_PATH_BUSY()) || (FALSE == MRCheckCOP0DecoderIdle()) || (FALSE == MRCheckCOP0SlaveIdle())) {
			FWCop0Waiting();
		}
	}

	COP0API_FillCOP0ReadSQUserData0Para(COP0_R_COPYUNIT_READ, &ReadSQPara);
	ReadSQPara.UserData0.btSLCMode = ubALU ? TRUE : FALSE;
	ReadSQPara.UserData0.btSLCSetMethod = COP0_TIE_D1SLC_MODE_FROM_TIEIN;
	ReadSQPara.UserData0.TagID = TAG_ID_ALLOCATE;
	ReadSQPara.UserData0.btReadTieOutMethod = TRUE;
	ReadSQPara.ulPCA.ulAll = ulPCA;
	ReadSQPara.UserData0.btRUTBps = TRUE;
	ReadSQPara.UserData0.btVBRMPBps = TRUE;
	ReadSQPara.UserData0.AttrMTTemplate = COP0_MT_TEMP_BUF_LBPB_READ;
	ReadSQPara.UserData0.L4KNum = 0;
	ReadSQPara.UserData0.btSerial = FALSE;
	ReadSQPara.BufVld.ubBufType = BUF_TYPE_C;
	ReadSQPara.BufVld.pulBufInfoPtr = &ulBufPara;
	ReadSQPara.pulFWSetPtr = ulFWSet;
#if (NES_GEN2_EN&&(!NCS_EN))
	ReadSQPara.UserData0.btSkipError = TRUE;
#endif /*NES_GEN2_EN*/
	// Buffer addess with serial setting
	ulBufPara.C.ubLBID = pCmd->cur_LBID;
	ulBufPara.C.uwLBOffset = pCmd->cur_LBOFFSET;
#if (PS5021_EN)
	ulFWSet[0].ulFWSet = DBUF_GC_BACKUP_COPY_UNIT_IDX;
#else /* (PS5021_EN) */
	ulFWSet[0].ulFWSet = GC_BACKUP_COPY_UNIT_OFF;
#endif /* (PS5021_EN) */
	ulFWSet[0].ubZInfo = MAX_ZINFO;

	eCOP0Status = COP0API_SendReadSQ(&ReadSQPara, &uoCallbackInfo);
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, TRUE == eCOP0Status.btSendCmdSuccess);

	while (0 != gpuoCmdTableMgr[COP0_CMD_TABLE_ID][ReadSQPara.UserData0.TagID].ulData.ulFLHInfo.uwCnt) {
		if (NCS_EN) {
			COP0DelegateCmd();
			FWErrRecorder();
			if (gpRetry->ubCnt) {
				ubNCSHaveEnteredRetryFlag = TRUE;
				Retry_Job_Handling();
			}
		}
		else {
			FWCop0Waiting();
		}
	}

	/*
	 * NCS verification
	 * we need re-write data back to NCS by flash VUC.
	 */
	if (NCS_EN) {
		if (ubNCSVerifyFlag) {
			// Get old and new MT index, Notice: should get MT_IDX before any CodeBank switch
			U8 ubOriginMTIdx = M_GET_LAST_GLOBAL_TRIGGER_MT_IDX();

#if FIP_DPS_FEATURE_EN
			//check Byte62 for NCS DPS verify, If Byte62 is BIT0 is TRUE, do DPS verify
			if (pCmd->nvme_sqcmd.raw_u8_data.by[62] & BIT0) {
				U8 ubNewMTIdx = FlaGetFreeMTIndex();  // new MT used to send data back to NCS

				FIPReduceProgramTime();

				// Send data and ECC info to NCS
				FIPVUCSendNCSInfo(ubNewMTIdx, (FlhMT_t *)M_MT_ADDR(ubOriginMTIdx), NCS_WRITE_DPS_SETTING_INFO, 0, 0, 0);
				// Return retry MT
				FlaAddFreeMTIndex(ubNewMTIdx);
			}
#endif /*FIP_DPS_FEATURE_EN*/

			/*
			 * Only need to re-write data when no error case in this step.
			 * Leave failure case to retry job (which includes trapping set).
			 */
			if (!ubNCSHaveEnteredRetryFlag) {
				U8 ubNewMTIdx = FlaGetFreeMTIndex();  // new MT used to send data back to NCS
				// Get ECC count one before trig any MT
				guwDMACntOneValue = M_FIP_GET_ECC_INF(M_GET_LAST_GLOBAL_TRIGGER_QUEUE_IDX() % gFlhEnv.ubChannelExistNum);
				// Dump trapping set setting
				ReadRetryDumpHWSetting(ubLMU, 0xD0, 0, 0, 1);
				// Send data and ECC info to NCS
#if (PS5021_EN)
				FIPVUCSendNCSInfo(ubNewMTIdx, (FlhMT_t *)M_MT_ADDR(ubOriginMTIdx), NCS_WRITE_FRAME_INFO, M_LB_TO_ADDR(pCmd->cur_LBID, pCmd->cur_LBOFFSET), DBUF_GC_BACKUP_COPY_UNIT_IDX, 0);
#else /* (PS5021_EN) */
				FIPVUCSendNCSInfo(ubNewMTIdx, (FlhMT_t *)M_MT_ADDR(ubOriginMTIdx), NCS_WRITE_FRAME_INFO, M_LB_TO_ADDR(pCmd->cur_LBID, pCmd->cur_LBOFFSET), GC_BACKUP_COPY_UNIT_OFF, 0);
#endif /* (PS5021_EN) */
				FIPVUCSendNCSInfo(ubNewMTIdx, (FlhMT_t *)M_MT_ADDR(ubOriginMTIdx), NCS_WRITE_ERROR_BIT_INFO, 0, 0, 0);
				FIPVUCSendNCSInfo(ubNewMTIdx, (FlhMT_t *)M_MT_ADDR(ubOriginMTIdx), NCS_WRITE_HW_SETTING_INFO, 0, 0, 0);
				// Return retry MT
				FlaAddFreeMTIndex(ubNewMTIdx);
			}
		}
		// Restore cop0 attr back
		M_COP0_GET_ATTR(COP0_MT_ATTR1) = ulCOP0AttributeBackup.ulAll;
	}

	// Change LDPC mode back to 2
	if (NCS_EN) {
		if ((0 == uwBlock) && (0 == uwPage)) { /* parasoft-suppress BD-PB-CC "get from HW"*/
			FlaSwitchLDPCHandler(gFlhEnv.ubDefaultLDPCMode);
		}
	}
	// NCS Micron need clear param from VUC
	if (NCS_EN) {
		if (gubNCSTLCOpenFlow) {
			gubNCSTLCOpenFlow = FALSE;
		}
		if (gubNCSQLC2ndPassFlow) {
			gubNCSQLC2ndPassFlow = FALSE;
		}
	}
	pCmd->state = FW_PROCESS_DONE;
}

void nvme_sec_send_vendor_api(OPT_HCMD_PTR pCmd)
{
	U16 uwSpsp;
	U32 ulBufAddr;
	U8 ubFinish;

	uwSpsp = pCmd->nvme_sqcmd.adm.security_rw.spsp;

	switch (uwSpsp) {
	case SPSP_IDENTIFY:
	case SPSP_GET_LOG:
		nvme_vucsp_record_sq(pCmd);
		pCmd->state = FW_PROCESS_DONE;
		break;
	case SPSP_SEND_SQ:
		nvme_vucsp_record_sq(pCmd);
		gVUCVar.ubSecspKeyValid = 1;
		gVUCVar.VUCSkipLPMBMP.btVUCSecurity = 1;
		pCmd->state = FW_PROCESS_DONE;
		break;

	case SPSP_VENDOR_SPOR:
		if (SIM_SPOR_EN) {
			ulBufAddr = M_LB_TO_ADDR(pCmd->cur_LBID, pCmd->cur_LBOFFSET);

			M_UART(SIM_SPOR_INFO_, "send LBID %x Offset %x\n", pCmd->cur_LBID, pCmd->cur_LBOFFSET);

			ubFinish = SimSPORSecurityVendorPowerHandler(TRUE, ulBufAddr);

			if (TRUE == ubFinish) {
				pCmd->state = FW_PROCESS_DONE;      // 4K buffer only
			}
		}
		break;

	default:
		nvme_mfree_hal_api (pCmd, LCAC_1C);
		pCmd->cur_sct = SCT_ERR_VUC;
		pCmd->cur_sc = VUC_ERROR_STATUS_CODE_UNEXPECTED;
		pCmd->state = CMD_STATE_CMD_CPL;
		break;
	}

}

void nvme_sec_receive_vendor_api(OPT_HCMD_PTR pCmd)
{
	U16 uwSpsp;
	U8 ubFinish;
	U32 ulBufAddr;

	uwSpsp = pCmd->nvme_sqcmd.adm.security_rw.spsp;

	switch (uwSpsp) {
	case SPSP_IDENTIFY:
		nvme_vucsp_identify(pCmd);
		pCmd->state = FW_PROCESS_DONE;
		break;
	case SPSP_GET_LOG:
		nvme_vucsp_get_log(pCmd);
		pCmd->state = FW_PROCESS_DONE;
		break;
	case SPSP_GET_CQ:
		nvme_vucsp_get_cq(pCmd);
		gVUCVar.VUCSkipLPMBMP.btVUCSecurity = 0;
		if (gVUCVar.ubVucAPKeyShutdown) {
			M_HOST_EVENT_SET_FLAG_IN_ISR(HOST_EVENT_NORMAL_SHUT_DOWN_BIT);
			gVUCVar.ubVucAPKeyShutdown = 0;
		}
		pCmd->state = FW_PROCESS_DONE;
		break;

	case SPSP_VENDOR_SPOR:
		if (SIM_SPOR_EN) {
			ulBufAddr = M_LB_TO_ADDR(pCmd->cur_LBID, pCmd->cur_LBOFFSET);

			M_UART(SYNC_CMD_, "send LBID %x Offset %x\n", pCmd->cur_LBID, pCmd->cur_LBOFFSET);

			ubFinish = SimSPORSecurityVendorPowerHandler(FALSE, ulBufAddr);

			if (ubFinish == TRUE) {
				pCmd->state = FW_PROCESS_DONE;      // 4K buffer only
			}
		}
		break;

	default:
		nvme_mfree_hal_api (pCmd, LCAC_1C);
		pCmd->cur_sct = SCT_ERR_VUC;
		pCmd->cur_sc = VUC_ERROR_STATUS_CODE_UNEXPECTED;
		pCmd->state = CMD_STATE_CMD_CPL;
		break;
	}

}
