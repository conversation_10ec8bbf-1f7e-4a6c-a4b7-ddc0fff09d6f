/**************************************************************************/
/*                                                                        */
/*           Copyright (c) 2000-2017 by Phison Electronics Corp.          */
/*                                                                        */
/**************************************************************************/

/**************************************************************************/
/*                                                                        */
/*  HAL UART DEFINITION                                    RELEASE        */
/*                                                                        */
/*    shr_hal_d2h_reg.h                                  ARM Compiler     */
/*                                                           X.X          */
/*  AUTHOR                                                                */
/*                                                                        */
/*                                                                        */
/*  DESCRIPTION                                                           */
/*                                                                        */
/*    This file contains hal doorbell definitions for this system.        */
/*                                                                        */
/*                                                                        */
/*  RELEASE HISTORY                                                       */
/*                                                                        */
/*    DATE              NAME                      DESCRIPTION             */
/*                                                                        */
/*  26-04-2017                                                            */
/*                                                                        */
/**************************************************************************/

#ifndef _SHR_HAL_D2H_REG_H_
#define _SHR_HAL_D2H_REG_H_

#include "common/typedef.h"
#include "common/symbol.h"
#include "common/math_op.h"


#define D2H_REGISTER_ADDRESS        (D2H_REG_ADDRESS)
#define SEC_REGISTER_ADDRESS   (SECURITY_REG_ADDRESS)
#define MR_REGISTER_ADDRESS   (MR_REG_ADDRESS)
#define SYSTEM_PD1_REGISTER_ADDRESS (SYSTEM_PD1_REG_ADDRESS)

#define R8_D2H                       ((REG8  *)D2H_REGISTER_ADDRESS)
#define R16_D2H                      ((REG16 *)D2H_REGISTER_ADDRESS)
#define R32_D2H                      ((REG32 *)D2H_REGISTER_ADDRESS)
#define R64_D2H                      ((REG64 *)D2H_REGISTER_ADDRESS)

/*================= Function Control and Status Registers ================*/

#define D2HL_CR_RLEN_W32             (0x00 >> 2)
#define D2HL_CR_RADDR32_W32          (0x40 >> 2)

#define D2HLL_CR_RADDR64_W64         (0x80 >> 3)
#define D2HL_CR_RADDR64_L_0x80       (0x80 >> 2)
#define D2HL_CR_RADDR64_H_0x84       (0x84 >> 2)


#define D2HL_RMAP_DLEN_0x100         (0x100 >> 2)
#define D2HL_RMAP_DADDR32_0x104      (0x104 >> 2)
#define D2HLL_RMAP_HADDR_0x108       (0x108 >> 3)
#define D2HL_RMAP_HADDR_L_0x108      (0x108 >> 2)
#define D2HL_RMAP_HADDR_H_0x10C      (0x10C >> 2)



#define D2HB_CR_ERR_INFO_0x200       (0x200 >> 2)
#define D2H_CPU_WRITE_RANGE_ERROR    BIT0
#define D2H_CPU_READ_RANGE_ERROR     BIT1
#define D2H_AES_WRITE_RANGE_ERROR    BIT2
#define D2H_AES_READ_RANGE_ERROR     BIT3
#define D2H_AXI_RDATA_ERROR          BIT4
#define D2H_AXI_BRESP_ERROR          BIT5
#define D2H_AXI_RRESP_ERROR          BIT6
#define D2H_AXI_TIMEOUT              BIT7
#define D2H_AXI_PERST_N              BIT8
#define D2H_AXI_D3_BLK               BIT9
#define	D2H_INT_EN_SHIFT			(16)
#define	D2H_INT_EN_MASK				(BIT_MASK(10))
#define D2H_INT_CPU_WRITE_RANGE      BIT16
#define D2H_INT_CPU_READ_RANGE       BIT17
#define D2H_INT_AES_WRITE_RANGE      BIT18
#define D2H_INT_AES_READ_RANGE       BIT19
#define D2H_INT_AXI_RDATA            BIT20
#define D2H_INT_AXI_BRESP            BIT21
#define D2H_INT_AXI_RRESP            BIT22
#define D2H_INT_AXI_TIMEOUT          BIT23
#define D2H_INT_AXI_PERST_N          BIT24
#define D2H_INT_AXI_D3_BLK           BIT25
#define	D2H_INT_FLAG_SHIFT			(0)
#define	D2H_INT_FLAG_MASK			(BIT_MASK(10))

#define D2H_LOG_CPU_WRITE_OUT_OF_RANGE   1
#define D2H_LOG_CPU_READ_OUT_OF_RANGE    2
#define D2H_LOG_AES_WRITE_OUT_OF_RANGE   3
#define D2H_LOG_AES_READ_OUT_OF_RANGE    4
#define D2H_LOG_AES_READ_PARITY_ERROR    5
#define R32_D2H_CR_ERR_INFO          D2HB_CR_ERR_INFO_0x200 //@Notice: Maybe add a new .h


#define D2HL_CR_ERR_INS_0x204        (0x204 >> 2)
#define EI_AXIH_RPTY                 BIT0
#define EI_AXIH_WPTY                 BIT1
#define EI_AXIH_BRESP                BIT2
#define EI_AXIH_RRESP                BIT3

#define D2HL_CR_AUTO_RESP_0x210      (0x210 >> 2)
#define D2H_INT_PERST_EN             BIT0
#define D2H_INT_AUTO_D3_BLK_EN       BIT1

#define D2HL_CR_BUS_STATUS_0x214      (0x214 >> 2)
#define D2H_IDLE_ST             	 BIT0

#define D2HL_CR_ERR_INFO_ADDR        (0x20C >> 2)
#define D2HL_CR_TIMEOUT_VALUE        (0x218 >> 2)
#define D2HH_CR_TIMEOUT_VALUE        (0x21C >> 2)



// seems we do not have AES test code, add AES part in D2H reg.h (this block should be removed)
#define r32_AES                      ((REG32 *)SEC_REGISTER_ADDRESS)
#define AES_OTF_FUNC                 (0x400 >> 2)
#define AES_ERR_INST                 (0x410 >> 2)
#define AES_BYPASS                   BIT0
#define AES_CRC_TO_MR                BIT26
#define r32_MROH                       ((REG32 *)MR_REGISTER_ADDRESS)
#define MR_SQ_FLUSH_L                (0x3F0 >> 2)
#define MR_SQ_FLUSH_H                (0x3F4 >> 2)
#define BMU_GET_MR_SLAVE_Q_EMT(MRId) (((volatile U32*)(MR_REGISTER_ADDRESS + (MRId)*8 + 4))[0] | BIT29)
#define SYS_MISC_OFFSET               0x1000
#define SYS_MISC_ADDRESS              (SYSTEM_PD1_REGISTER_ADDRESS + SYS_MISC_OFFSET)
#define SYS_FW_DCC_EN                 (0x8 >> 2)
#define r32_DCC                       ((REG32 *)SYS_MISC_ADDRESS)
#define PLDAL_PEX_DEV2_0xC4           (0xC4 >> 2)

/*============== END: NFE Control and Status Registers ===================*/


#endif /* _SHR_HAL_D2H_REG_H_ */
