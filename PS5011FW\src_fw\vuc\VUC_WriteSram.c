#include "hal/pic/uart/uart_api.h"
#include "nvme_api/nvme/shr_hal_nvme.h"
#include "vuc/VUC_WriteSram.h"
#include "host/VUC_handler.h"

void VUC_WriteSram(VUC_OPT_HCMD_PTR_t pCmd)
{
	M_UART(VUC_, "\nVUC_WRITE_SRAM");
#if (BURNER_MODE_EN || RDT_MODE_EN)
	{
		memcpy((void *)(pCmd->vuc_sqcmd.vendor.ReadWriteSram.ulRamAddr), (void *)gulVUCBufAddr, DEF_4B * pCmd->vuc_sqcmd.vendor.ReadWriteSram.ulLength);
	}
#else
	{
		memcpy((void *)(pCmd->vuc_sqcmd.vendor.ReadWriteSram.ulRamAddr), (void *)gulVUCBufAddr, DEF_4B);
	}
#endif

	M_UART(VUC_, "\nBuf[%l] to Des[%l] Len %l", gulVUCBufAddr, (pCmd->vuc_sqcmd.vendor.ReadWriteSram.ulRamAddr), DEF_4B * pCmd->vuc_sqcmd.vendor.ReadWriteSram.ulLength);
}
