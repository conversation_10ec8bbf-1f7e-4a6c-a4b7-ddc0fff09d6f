#include "i2cm_api.h"
#include "i2cm_device_api.h"
#include "tt/TT_api.h"
#include "table/initinfo_vt/initinfo_vt.h"
#include "hal/sys/api/angc/angc_api.h"
#include "hal/sys/api/pmu/pmu_api.h"
#include "hal/sys/api/padc/padc_api.h"
#include "hal/sys/api/mux/mux_api.h"
#include "drive_log/drive_log_api.h"
#include "lpm/lpm_api.h"
#if (RDT_MODE_EN)
#if (PS5017_EN)
#include "rdt/rdt_api.h"
#include "vuc/VUC_MicronGetTemperature.h"
#else
#include "rdt/rdt.h"
#include "rdt/rdt_temperature.h"
#endif
#endif /* (RDT_MODE_EN) */
#include "init/fw_init.h"//E17, for gMainJumpManager.btCheckToBurnerFlag

void I2CMPowerOffIC(void)
{
	U64 uoStartTriggerTime;
	U8 ubGroupOffset;

	ubGroupOffset = M_I2CM_GROUP_OFFSET(I2C_GROUP_1);
	M_I2CM_GROUP_SLAVE_ADDR(I2C_GROUP_1, I2C_PS6103_SLAVE_ADDR);

	// set 0x11 register to disable ch1, ch2, LDO
	M_I2CM_GROUP_OPERATION_REG(I2C_GROUP_1, 0, I2C_PS6103_CH_ENABLE_ADDR);
	M_I2CM_GROUP_OPERATION_REG(I2C_GROUP_1, 1, 0);


	M_I2CM_GROUP_CTRL(I2C_GROUP_1, 0, 0, 2); //(GroupId, ModeSelect, DataCnt, OperationCnt)

	// trigger I2C master
	uoStartTriggerTime = M_RTT_GET_CNT_MILLISECOND(RTT0);
	M_I2CM_SET_START_BIT(ubGroupOffset);

	// wait slave response
	while (M_I2CM_GET_START_BIT(ubGroupOffset)) {
		if ( (M_RTT_GET_CNT_MILLISECOND(RTT0) - uoStartTriggerTime) > I2C_TRIGGER_TIMEOUT_MILLISECOND) {
			M_FW_ASSERT(ASSERT_HAL_OTHER_0x06E0, FALSE);
		}
	}

}

void I2CMInitThermalSensor(U8 ubSlaveAddress)
{
#if (PS5017_EN)
	mux_i2cm_scl_and_sda(); //TS value would be wrong due to GPIO initial setting before LPM, so switch mux to I2C after wake up.
#endif /* (PS5017_EN) */
	if (gTT.ubOthers.B.btEnableFlashTemperature) {
		I2CMTriggerReadThermalSensor();
	}
	else {

		if (I2C_DETECT_FAIL != THERMAL_SENSOR) {
			U8 ubGroupOffset;
			ubGroupOffset = M_I2CM_GROUP_OFFSET(I2C_GROUP_1);

			while ( M_RTT_GET_CNT_MILLISECOND(RTT0) < I2C_THERMAL_START_DELAY_TIME_MILLISECOND);

			RTTSetSelfTimeout( &gTT.ThermalSensorTimer, I2C_THERMAL_SENSOR_TIMEOUT);
			while (M_I2CM_GET_START_BIT(ubGroupOffset)) {
				if ((RTTCheckSelfTimeout(&gTT.ThermalSensorTimer)) || (gLPM.InfoBlkSetting.B.btPMICTimeOut)) {
					gTT.ubI2CSlaveAddr = I2C_DETECT_FAIL;
#if ((!BURNER_MODE_EN) && (!RDT_MODE_EN))
					if ((!PS5017_EN) || (FALSE == gMainJumpManager.btCheckToBurnerFlag)) {
						if (DRIVE_LOG_EN) {
							TTSaveDriveLog(TT_DRIVE_LOG_SENSOR_ERROR, 0, 0, 0, 2, DRIVE_LOG_ADD_FORCE_SAVE_FLASH_LATER_MODE);
						}
					}

#endif /* ((!BURNER_MODE_EN) && (!RDT_MODE_EN)) */
					return;
				}
			}

			//init  extern thermal sensor
			R16_I2CM[R16_I2CM_BUF0_REG] = 0;
			M_I2CM_GROUP_SLAVE_ADDR(I2C_GROUP_1, ubSlaveAddress);
			M_I2CM_GROUP_OPERATION_REG(I2C_GROUP_1, 0, 0x00);
			M_I2CM_GROUP_CTRL(I2C_GROUP_1, GP_DIR_BIT, 2, 1); //(GroupId, ModeSelect, DataCnt, OperationCnt)
#if ((!PS5017_EN) || (FALSE == FAE_TT_EN))
			I2CMTriggerReadThermalSensor();
#endif /* ((!PS5017_EN) || (FALSE == FAE_TT_EN)) */
		}
#if (FAE_TT_EN && PS5017_EN)
		I2CMTriggerReadThermalSensor();
#endif /* (FAE_TT_EN && PS5017_EN) */
	}
}

void I2CMTriggerReadThermalSensor(void)
{
	if (gTT.ubOthers.B.btEnableFlashTemperature) {
#if ((!BURNER_MODE_EN) && (!RDT_MODE_EN))
		if ((!PS5017_EN) || (FALSE == gMainJumpManager.btCheckToBurnerFlag)) {
			TTTriggerFlashTemperature();
		}

#endif /* ((!BURNER_MODE_EN) && (!RDT_MODE_EN)) */
	}
	else {
		if (I2C_DETECT_FAIL != THERMAL_SENSOR) {
			U8 ubGroupOffset;
			ubGroupOffset = M_I2CM_GROUP_OFFSET(I2C_GROUP_1);
			// trigger I2C master
			M_I2CM_SET_START_BIT(ubGroupOffset);
			RTTSetSelfTimeout( &gTT.ThermalSensorTimer, I2C_THERMAL_SENSOR_TIMEOUT);
		}
#if ((!BURNER_MODE_EN) && FAE_TT_EN && PS5017_EN) //[BC_M]
		if (FALSE == gMainJumpManager.btCheckToBurnerFlag) {
			TTTriggerFlashTemperature();
		}
#endif

	}
}

U8 I2CMThermalSensorGetTemperature(TTTemperatureCelsius_t *pubTemp)
{
#if RDT_MODE_EN && RDT_THERMAL_DETECT
	//rdt got flash temperature by cop0 in flash test step
	if (gRdtApiStruct.rdt_got_flash_temperature_by_cop0) {
		pubTemp->B.btSign = 0;
		pubTemp->B.Degree = gRdtApiStruct.rdt_got_flash_temperature_by_cop0;
		gRdtApiStruct.rdt_got_flash_temperature_by_cop0 = 0;
		//UartPrintf("\n[TT] pubTemp = %x \n", pubTemp->B.Degree );
	}
#elif(0)//
	U16   uwTemp;
	U16 uwKelvinTemperature;
	TTTemperatureCelsius_t ubTemperatureOffset; // only for on-die or external sensor.
	U8 ubGroupOffset;
	U8 ubFormulaUpperOffset = gTT.ubFormulaUpperOffset;
	U8 ubFormulaLowerOffset = gTT.ubFormulaLowerOffset;
	U8 ubFormulaConstant = gTT.ubFormulaConstant;
#if ((!BURNER_MODE_EN) && FAE_TT_EN)
	TTTemperatureCelsius_t ubCurrentThermalSensorTemp;
#endif /*((!BURNER_MODE_EN) && FAE_TT_EN)*/

	if (gTT.ubOthers.B.btEnableFlashTemperature) {
#if (RDT_MODE_EN)
		RDT_API_STRUCT_PTR rdt = &gRdtApiStruct;

		ubTemperatureOffset.ubAll = 0;
		pubTemp->ubAll =  rdt->max_flash_temperature.ubAll; //max CE temperature
#elif (!BURNER_MODE_EN)
		if ( TTGetFlashTemperature(pubTemp)) {
			return FAIL;
		}
#endif /* (RDT_MODE_EN) */
	}
	else {
		ubTemperatureOffset.ubAll = gTT.ubTemperatureOffsetExternalToFlash.ubAll;
		ubGroupOffset = M_I2CM_GROUP_OFFSET(I2C_GROUP_1);

		if (I2C_DETECT_FAIL == THERMAL_SENSOR) {
			if (M_ANGC_GET_TCODE_SIGN()) {
				pubTemp->B.btSign = TRUE;
			}
			else {
				pubTemp->B.btSign = FALSE;
			}
			pubTemp->B.Degree = M_ANGC_GET_TEMPERATURE_SENSOR_CELSIUS();
			// FLH Tc = ((((Constant-upper_offset)+lower_offset)/Constant)*CTL Tj)-lower_offset
			if (pubTemp->B.btSign) {
				pubTemp->B.Degree = (((((ubFormulaConstant - ubFormulaUpperOffset) + ubFormulaLowerOffset) * (pubTemp->B.Degree)) / ubFormulaConstant) + ubFormulaLowerOffset);
			}
			else {
				pubTemp->ubAll = (((((ubFormulaConstant - ubFormulaUpperOffset) + ubFormulaLowerOffset) * (pubTemp->B.Degree)) / ubFormulaConstant) - ubFormulaLowerOffset);
				if (pubTemp->B.btSign) {
					pubTemp->B.Degree = ~(pubTemp->B.Degree) + 1;
				}
			}
		}
		else {
			// wait slave response
			if (M_I2CM_GET_START_BIT(ubGroupOffset)) {
				if ((RTTCheckSelfTimeout(&gTT.ThermalSensorTimer)) || (gLPM.InfoBlkSetting.B.btPMICTimeOut)) {
					gTT.ubI2CSlaveAddr = I2C_DETECT_FAIL;
#if ((!BURNER_MODE_EN) && (!RDT_MODE_EN))
					if (DRIVE_LOG_EN) {
						TTSaveDriveLog(TT_DRIVE_LOG_SENSOR_ERROR, 0, 0, 0, 0, DRIVE_LOG_ADD_FORCE_SAVE_FLASH_LATER_MODE);
					}
#endif /* ((!BURNER_MODE_EN) && (!RDT_MODE_EN)) */
				}
				return FAIL;
			}

			if ( (R32_I2CM[R32_I2CM_GP1_NACK] & GP_ANACK_BIT) || (R32_I2CM[R32_I2CM_GP1_NACK] & GP_DNACK_BIT)) {
				// there is a something wrong with the thermal sensor.
				gTT.ubI2CSlaveAddr = I2C_DETECT_FAIL;
#if ((!BURNER_MODE_EN) && (!RDT_MODE_EN))
				if (DRIVE_LOG_EN) {
					TTSaveDriveLog(TT_DRIVE_LOG_SENSOR_ERROR, 0, 0, 0, 1, DRIVE_LOG_ADD_FORCE_SAVE_FLASH_LATER_MODE);
				}
#endif /* ((!BURNER_MODE_EN) && (!RDT_MODE_EN)) */
				return FAIL;
			}

			if (I2C_THERMAL_TMP102 == THERMAL_SENSOR ) {
				uwTemp = ((R8_I2CM[R8_I2CM_BUF0_REG] << I2C_THERMAL_TMP102_BYTE0_LEFT_OFFSET) | (R8_I2CM[R8_I2CM_BUF0_REG + 1] >> I2C_THERMAL_TMP102_BYTE1_RIGHT_OFFSET));
				uwTemp /= I2C_THERMAL_TMP102_TEMPERATURE_RESOLUTION;
				pubTemp->ubAll =  uwTemp;
			}
			else {
				pubTemp->ubAll = (R8_I2CM[R8_I2CM_BUF0_REG]);
			}
			if (pubTemp->B.btSign) {
				pubTemp->B.Degree = (~pubTemp->B.Degree);
			}
#if ((!BURNER_MODE_EN) && FAE_TT_EN)
			ubCurrentThermalSensorTemp.ubAll = pubTemp->ubAll;
#endif /*((!BURNER_MODE_EN) && FAE_TT_EN)*/

			/* update offset */
			if (ubTemperatureOffset.B.btSign == pubTemp->B.btSign) {
				pubTemp->B.Degree += ubTemperatureOffset.B.Degree;
			}
			else {
				if (ubTemperatureOffset.B.Degree > pubTemp->B.Degree) {
					pubTemp->B.Degree = ubTemperatureOffset.B.Degree - pubTemp->B.Degree;
					pubTemp->B.btSign = ubTemperatureOffset.B.btSign; /* change sign */
				}
				else {
					pubTemp->B.Degree -= ubTemperatureOffset.B.Degree;
				}
			}
		}
	}


	// update Max/Min temperature.
	if ( pubTemp->B.btSign ) {
		uwKelvinTemperature = TT_KELVIN - pubTemp->B.Degree;
	}
	else {
		uwKelvinTemperature = TT_KELVIN + pubTemp->B.Degree;
	}

	if (M_CELSIUS_TO_KELVIN(gpVTDBUF->TT.ubMinTemperature) > uwKelvinTemperature) {
		gpVTDBUF->TT.ubMinTemperature.ubAll = pubTemp->ubAll;
	}

	if (M_CELSIUS_TO_KELVIN(gpVTDBUF->TT.ubMaxTemperature) < uwKelvinTemperature) {
		gpVTDBUF->TT.ubMaxTemperature.ubAll = pubTemp->ubAll;
	}
#if ((!BURNER_MODE_EN) && FAE_TT_EN)
	if ( TTGetFlashTemperature(pubTemp)) {
		return FAIL;
	}
	if (M_TT_CHECK_READ_TEMPERATURE_DONE()) {
		if (QFN88 == gubASICType) {
			mux_gpio_10_uart_tx_0();
		}
		M_UART(TT_FAE_, "\n%s%d", M_ANGC_GET_TCODE_SIGN() ? " -" : " ", M_ANGC_GET_TEMPERATURE_SENSOR_CELSIUS()); //1st: CTRL
		M_UART(TT_FAE_, "%s%d", (ubCurrentThermalSensorTemp.B.btSign) ? " -" : " ", ubCurrentThermalSensorTemp.B.Degree); //2nd : TS
		M_UART(TT_FAE_, "%s%d", (pubTemp->B.btSign) ? " -" : " ", pubTemp->B.Degree); //3rd : Flash Max
		M_UART(TT_FAE_, " %d %d %d", gpVTDBUF->TTBackup.uwMTQDelay[TT_DELAY_SRC_TT], gpVTDBUF->TTBackup.uwReadDelay[TT_DELAY_SRC_TT], gpVTDBUF->TTBackup.uwWriteDelay[TT_DELAY_SRC_TT]);
		M_UART(TT_FAE_, ",%d ", gpVT->TT.TMTState);
		if (QFN88 == gubASICType) {
			M_RTT_IDLE_MS(30); //Give Uart enough time to print before switching mux
			mux_i2cm_scl_and_sda();
		}
	}
#endif /*((!BURNER_MODE_EN) && FAE_TT_EN)*/
#endif //RDT_MODE_EN
	return PASS;
}

//only trigger one time in LPMInit()
void I2CMSetPMICLowVoltage(void)
{
	U64 uoStartTriggerTime;
	U8 ubGroupOffset;

#if (USB == HOST_MODE)
	R8_SYS0_PADC[R8_SYS0_PAD_GPIO_10] &= (CR_GPIO_EN_BIT | XGPIO_I_BIT | CR_GPIO_PD_75K_BIT | CR_GPIO_PD_5P5K_BIT);
	R8_SYS0_PADC[R8_SYS0_PAD_GPIO_10] |= (CR_GPIO_PU_5P5K_BIT);
	R8_SYS0_PADC[R8_SYS0_PAD_GPIO_11] &= (CR_GPIO_EN_BIT | XGPIO_I_BIT | CR_GPIO_PD_75K_BIT | CR_GPIO_PD_5P5K_BIT);
	R8_SYS0_PADC[R8_SYS0_PAD_GPIO_11] |= (CR_GPIO_PU_5P5K_BIT);
#endif/* (USB == HOST_MODE) */

	ubGroupOffset = M_I2CM_GROUP_OFFSET(I2C_GROUP_1);
	uoStartTriggerTime = M_RTT_GET_CNT_MILLISECOND(RTT0);
	//Wait former I2C command complete
	while (M_I2CM_GET_START_BIT(ubGroupOffset)) {
		if ( (M_RTT_GET_CNT_MILLISECOND(RTT0) - uoStartTriggerTime) > I2C_TRIGGER_TIMEOUT_MILLISECOND) {
			gLPM.InfoBlkSetting.B.btPMICSupport = FALSE;
			gLPM.InfoBlkSetting.B.btPMICTimeOut = TRUE;
			M_PMU_EXTERNAL_POWER_IC_DIS();
			return;
		}
	}

	M_I2CM_GROUP_SLAVE_ADDR(I2C_GROUP_1, I2C_PS6103_SLAVE_ADDR);

	// set 0x11 register bit 7 to disable ch1
	M_I2CM_GROUP_OPERATION_REG(I2C_GROUP_1, 0, I2C_PS6103_CH2_SELECT_REGISTER_GPIO);
#if (USB == HOST_MODE)
	M_I2CM_GROUP_OPERATION_REG(I2C_GROUP_1, 1, I2C_PS6103_CH2_GPIO_VID_SETTING_0875V);
#else/* (USB == HOST_MODE) */
	M_I2CM_GROUP_OPERATION_REG(I2C_GROUP_1, 1, I2C_PS6103_CH2_GPIO_VID_SETTING_0825V);
#endif/* (USB == HOST_MODE) */

	M_I2CM_GROUP_CTRL(I2C_GROUP_1, 0, 0, 2); //(GroupId, ModeSelect, DataCnt, OperationCnt)

	// trigger I2C master
	uoStartTriggerTime = M_RTT_GET_CNT_MILLISECOND(RTT0);
	M_I2CM_SET_START_BIT(I2C_GROUP_1);

	// wait slave response
	while (M_I2CM_GET_START_BIT(I2C_GROUP_1)) {
		if ( (M_RTT_GET_CNT_MILLISECOND(RTT0) - uoStartTriggerTime) > I2C_TRIGGER_TIMEOUT_MILLISECOND) {
			gLPM.InfoBlkSetting.B.btPMICSupport = FALSE;
			gLPM.InfoBlkSetting.B.btPMICTimeOut = TRUE;
			M_PMU_EXTERNAL_POWER_IC_DIS();
			return;
		}
	}

#if (USB == HOST_MODE)
	//Set normal operation voltage = 0.925V
	M_I2CM_GROUP_SLAVE_ADDR(I2C_GROUP_1, I2C_PS6103_SLAVE_ADDR);
	M_I2CM_GROUP_OPERATION_REG(I2C_GROUP_1, 0, I2C_PS6103_CH2_SELECT_REGISTER);
	M_I2CM_GROUP_OPERATION_REG(I2C_GROUP_1, 1, I2C_PS6103_CH2_VID_SETTING_0925V);

	M_I2CM_GROUP_CTRL(I2C_GROUP_1, 0, 0, 2); //(GroupId, ModeSelect, DataCnt, OperationCnt)

	// trigger I2C master
	uoStartTriggerTime = M_RTT_GET_CNT_MILLISECOND(RTT0);
	M_I2CM_SET_START_BIT(I2C_GROUP_1);

	// wait slave response
	while (M_I2CM_GET_START_BIT(I2C_GROUP_1)) {
		if ( (M_RTT_GET_CNT_MILLISECOND(RTT0) - uoStartTriggerTime) > I2C_TRIGGER_TIMEOUT_MILLISECOND) {
			gLPM.InfoBlkSetting.B.btPMICSupport = FALSE;
			gLPM.InfoBlkSetting.B.btPMICTimeOut = TRUE;
			M_PMU_EXTERNAL_POWER_IC_DIS();
			return;
		}
	}
#endif/* (USB == HOST_MODE) */
}


void I2CMSetPMICCommandFormatForFlashIO(void)
{
	U64 uoStartTriggerTime;
	U8 ubGroupOffset;

	ubGroupOffset = M_I2CM_GROUP_OFFSET(I2C_GROUP_1);
	uoStartTriggerTime = M_RTT_GET_CNT_MILLISECOND(RTT0);

	//Wait former I2C command from TT complete
	while (M_I2CM_GET_START_BIT(ubGroupOffset)) {
		if ( (M_RTT_GET_CNT_MILLISECOND(RTT0) - uoStartTriggerTime) > I2C_TRIGGER_TIMEOUT_MILLISECOND) {
			gLPM.InfoBlkSetting.B.btPMICSupport = FALSE;
			gLPM.InfoBlkSetting.B.btPMICTimeOut = TRUE;
			M_PMU_EXTERNAL_POWER_IC_DIS();
			M_UART(DEBUG_, "I2C OT\n");
			return;
		}
	}

	//FW only write the folowing command format into register, then PMU will trigger it during Hw flow.
	//====================================================================
	// Group 1 - flash IO power off (I2C mode, disable PMIC CH1, PS6103 Register index:11h, bit 7 set to 0)
	//====================================================================
	M_I2CM_GROUP_SLAVE_ADDR(I2C_GROUP_1, I2C_PS6103_SLAVE_ADDR);

	// set 0x11 register bit 7 to disable ch1
	M_I2CM_GROUP_OPERATION_REG(I2C_GROUP_1, 0, I2C_PS6103_CH_ENABLE_ADDR);
	M_I2CM_GROUP_OPERATION_REG(I2C_GROUP_1, 1, I2C_PS6103_CH1_DISABLE);

	M_I2CM_GROUP_CTRL(I2C_GROUP_1, 0, 0, 2);	//(GroupId, ModeSelect, DataCnt, OperationCnt)

	//====================================================================
	// Group 4 - flash IO power on (I2C mode, enable PMIC CH1, PS6103 Register index:11h, bit 7 set to 1)
	//====================================================================
	M_I2CM_GROUP_SLAVE_ADDR(I2C_GROUP_4, I2C_PS6103_SLAVE_ADDR);

	M_I2CM_GROUP_OPERATION_REG(I2C_GROUP_4, 0, I2C_PS6103_CH_ENABLE_ADDR);
	M_I2CM_GROUP_OPERATION_REG(I2C_GROUP_4, 1, I2C_PS6103_CH1_ENABLE);

	M_I2CM_GROUP_CTRL(I2C_GROUP_4, 0, 0, 2); //(GroupId, ModeSelect, DataCnt, OperationCnt)
}

void I2CMSetPMICCommandFormatForLowVoltage(void)
{
	U64 uoStartTriggerTime;
	U8 ubGroupOffset;

	ubGroupOffset = M_I2CM_GROUP_OFFSET(I2C_GROUP_1);
	uoStartTriggerTime = M_RTT_GET_CNT_MILLISECOND(RTT0);

	//Wait former I2C command from TT complete
	while (M_I2CM_GET_START_BIT(ubGroupOffset)) {
		if (((M_RTT_GET_CNT_MILLISECOND(RTT0) - uoStartTriggerTime) > I2C_TRIGGER_TIMEOUT_MILLISECOND)) {
			gLPM.InfoBlkSetting.B.btPMICSupport = FALSE;
			gLPM.InfoBlkSetting.B.btPMICTimeOut = TRUE;
			M_PMU_EXTERNAL_POWER_IC_DIS();
			M_UART(DEBUG_, "I2C OT\n");
			return;
		}
	}
	//FW only write the folowing command format into register, then PMU will trigger it during Hw flow.
	//====================================================================
	// Group 2 - Set CH2 to GPIO Mode (PS6103 Register index:06h, bit 7 set to 1, control GPIO_LV)
	//====================================================================
	M_I2CM_GROUP_SLAVE_ADDR(I2C_GROUP_2, I2C_PS6103_SLAVE_ADDR);

	// set 0x06 register bit 7 to enable GPIO mode
	M_I2CM_GROUP_OPERATION_REG(I2C_GROUP_2, 0, I2C_PS6103_CH1_SELECT_REGISTER_GPIO);
	if (M_GET_VUC_POWER_MODE()) {
		M_I2CM_GROUP_OPERATION_REG(I2C_GROUP_2, 1, I2C_PS6103_12V_GPIO_ENABLE);
	}
	else {
		M_I2CM_GROUP_OPERATION_REG(I2C_GROUP_2, 1, I2C_PS6103_18V_GPIO_ENABLE);
	}

	M_I2CM_GROUP_CTRL(I2C_GROUP_2, 0, 0, 2); //(GroupId, ModeSelect, DataCnt, OperationCnt)

	//====================================================================
	// Group 3 - Set CH2 to I2C Mode (PS6103 Register index:06h, bit 7 set to 0, control GPIO_LV)
	//====================================================================
	M_I2CM_GROUP_SLAVE_ADDR(I2C_GROUP_3, I2C_PS6103_SLAVE_ADDR);

	// set 0x06 register bit 7 to disable GPIO mode
	M_I2CM_GROUP_OPERATION_REG(I2C_GROUP_3, 0, I2C_PS6103_CH1_SELECT_REGISTER_GPIO);
	if (M_GET_VUC_POWER_MODE()) {
		M_I2CM_GROUP_OPERATION_REG(I2C_GROUP_3, 1, I2C_PS6103_12V_GPIO_DISABLE);
	}
	else {
		M_I2CM_GROUP_OPERATION_REG(I2C_GROUP_3, 1, I2C_PS6103_18V_GPIO_DISABLE);
	}

	M_I2CM_GROUP_CTRL(I2C_GROUP_3, 0, 0, 2); //(GroupId, ModeSelect, DataCnt, OperationCnt)
}

#if PS5021_EN
void I2CMSetPMICValue(U8 ubRegister, U8 ubValue)
{
	RTTSelfTimeoutCondition_t RttTimer;
	U8 ubGroupOffset;

	ubGroupOffset = M_I2CM_GROUP_OFFSET(I2C_GROUP_1);
	//Wait former I2C command complete
	RTTSetSelfTimeout(&RttTimer, I2C_TRIGGER_TIMEOUT_MILLISECOND * 1000);
	while (M_I2CM_GET_START_BIT(ubGroupOffset)) {
		if (TRUE == RTTCheckSelfTimeout(&RttTimer)) {
			return;
		}
	}

	M_I2CM_GROUP_SLAVE_ADDR(I2C_GROUP_1, I2C_PS6121_SLAVE_ADDR);

	M_I2CM_GROUP_OPERATION_REG(I2C_GROUP_1, 0, ubRegister);
	M_I2CM_GROUP_OPERATION_REG(I2C_GROUP_1, 1, ubValue);

	M_I2CM_GROUP_CTRL(I2C_GROUP_1, 0, 0, 2); //(GroupId, ModeSelect, DataCnt, OperationCnt)

	// trigger I2C master
	M_I2CM_SET_START_BIT(I2C_GROUP_1);

	// wait slave response
	RTTSetSelfTimeout(&RttTimer, I2C_TRIGGER_TIMEOUT_MILLISECOND * 1000);
	while (M_I2CM_GET_START_BIT(I2C_GROUP_1)) {
		if (TRUE == RTTCheckSelfTimeout(&RttTimer)) {
			return;
		}
	}
}

void I2CMGetPMICValue(U8 ubRegister, volatile U8 *pubValue)
{
	RTTSelfTimeoutCondition_t RttTimer;
	U8 ubGroupOffset;

	*pubValue = 0xFF;

	ubGroupOffset = M_I2CM_GROUP_OFFSET(I2C_GROUP_1);
	//Wait former I2C command complete
	RTTSetSelfTimeout(&RttTimer, I2C_TRIGGER_TIMEOUT_MILLISECOND * 1000);
	while (M_I2CM_GET_START_BIT(ubGroupOffset)) {
		if (TRUE == RTTCheckSelfTimeout(&RttTimer)) {
			return;
		}
	}

	M_I2CM_SET_BUF0(0);
	M_I2CM_GROUP_SLAVE_ADDR(I2C_GROUP_1, I2C_PS6121_SLAVE_ADDR);
	M_I2CM_GROUP_OPERATION_REG(I2C_GROUP_1, 0, ubRegister);

	M_I2CM_GROUP_CTRL(I2C_GROUP_1, GP_DIR_BIT, 1, 1); //(GroupId, ModeSelect, DataCnt, OperationCnt)

	// trigger I2C master
	M_I2CM_SET_START_BIT(I2C_GROUP_1);

	// wait slave response
	RTTSetSelfTimeout(&RttTimer, I2C_TRIGGER_TIMEOUT_MILLISECOND * 1000);
	while (M_I2CM_GET_START_BIT(I2C_GROUP_1)) {
		if (TRUE == RTTCheckSelfTimeout(&RttTimer)) {
			return;
		}
	}

	*pubValue = (U8)(M_I2CM_GET_BUF0());
}

void I2CMPmicAdjustWorkaround(void)
{
	U8 ubCH3TempVal = 0;
	U8 ubLDO1TrimValue = 0;

	M_SET_MUX_FW_OPTION_1();
	mux_i2cm_scl_and_sda();

	//Decrease CH3 FIO to 1.2V if pmic register 0x55 trim value is between 15 -35
	I2CMGetPMICValue(I2C_PS6121_CH3_TRIM_VALUE_REG, &ubCH3TempVal);
	if (M_I2CM_CHECK_CH3_TRIM_FIELD_1350V(ubCH3TempVal)) {
		I2CMSetPMICValue(I2C_PS6121_CH3_SELECT_REGISTER_GPIO, I2C_PS6121_CH3_GPIO_VID_SETTING_1050V);
	}

	//Increase LDO1 to 1.8V if pmic register 0x5C trim value is between 30 -50
	I2CMGetPMICValue(I2C_PS6121_LDO1_TRIM_VALUE_REG, &ubLDO1TrimValue);
	if (M_I2CM_CHECK_LDO1_TRIM_FIELD_1600V(ubLDO1TrimValue)) {
		I2CMSetPMICValue(I2C_PS6121_LDO1_SEL_REG, I2C_PS6121_LDO1_GPIO_VID_SETTING_2000V);
	}
}
#endif /* PS5021_EN */

