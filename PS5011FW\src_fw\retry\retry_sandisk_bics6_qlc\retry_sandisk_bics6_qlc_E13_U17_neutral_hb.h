#ifndef _RETRY_SANDISK_BISC5_TLC_E13_NEUTRAL_HB_H_
#define _RETRY_SANDISK_BISC5_TLC_E13_NEUTRAL_HB_H_
/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */

#include "env.h"

#if (((PS5013_EN) || (PS5017_EN)) && (FW_CATEGORY_FLASH == FLASH_SANDISK_BICS6_QLC) && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER))
/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */

#define HBIT_RETRY_SANDISK_BICS6_QLC_1024G_STEP_NUM			(49 + 1)
#define HBIT_RETRY_SANDISK_BICS6_SLC_1024G_STEP_NUM			(49 + 1)

/*
 * ---------------------------------------------------------------------------------------------------
 *  enum
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */

#endif /* ((PS5013_EN) && (FW_CATEGORY_FLASH == FLASH_SANDISK_BICS6_QLC) && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER)) */
#endif /* _RETRY_SANDISK_BISC5_TLC_E13_NEUTRAL_HB_H_ */
