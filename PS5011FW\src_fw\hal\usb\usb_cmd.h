#ifndef _USB_CMD_H_
#define _USB_CMD_H_

#if (USB == HOST_MODE)

// a
#include "aom/aom_api.h"
// i
#include "init/fw_init.h"
#include "trim/ftl_trim_api.h"
/*=====================================================================================
                                  Variable Definition
=====================================================================================*/
/* Cmd Spec Var */

/* SetFeature SubCommand */

/* Security Command */

/*=====================================================================================
                                 Structure Definition
=====================================================================================*/
typedef struct {
	//Mapping to B1 to B10 of scsicmd of APUCQ_t
	//CDB BYTE 1
	U8 Res1				: 1;
	U8 Ptotocol			: 4;
	U8 MultipleCount	: 3;
	//CDB BYTE 2
	U8 TLen				: 2;
	U8 ByteBlock		: 1;
	U8 TDir				: 1;
	U8 Res2				: 1;
	U8 CkCond			: 1; //Check Condition
	U8 OffLine			: 2;
	//CDB BYTE 3
	U8 Features;
	//CDB BYTE 4
	U8 SectorCount;
	//CDB BYTE 5
	U8 LBA_L;
	//CDB BYTE 6
	U8 LBA_M;
	//CDB BYTE 7
	U8 LBA_H;
	//CDB BYTE 8
	U8 Device;
	//CDB BYTE 9
	U8 Command;
	//CDB BYTE 10
	U8 Res3;
} ATA_PASS_THROUGH_CMD_t, *pATA_PASS_THROUGH_CMD_t;

typedef struct {
	//Mapping to B1 to B10 of scsicmd of APUCQ_t
	//CDB BYTE 1
	U8 btAnchor			: 1;
	U8 btRes1			: 7;
	//CDB BYTE 2~5
	U8 bRes1[4];
	//CDB BYTE 6
	U8 btGroupNumber	: 5;
	U8 btRes2			: 3;
	//CDB BYTE 7
	U8 bParameterListLen_H;
	//CDB BYTE 8
	U8 bParameterListLen_L;
	//CDB BYTE 9
	U8 bControl;
	//CDB BYTE 10
	U8 bRes2;
} UNMAP_CMD_t, *pUNMAP_CMD_t;


typedef struct {
	U64	uoUnmapLBA;
	U32	ulUnmapLen;
	U8	bReserve[4];
} UnmapBlockDescriptor_t;

typedef struct {
	U8 ubUnmapDataLen_H;			//Unit: BYTES
	U8 uwUnmapDataLen_L;
	U8 ubUnmapBlockDescriptorLen_H;	//Unit: BYTES
	U8 ubUnmapBlockDescriptorLen_L;
	U8 bReserve[4];
} UnmapParameterList_t;

typedef struct {
	//Mapping to B1 to B5 of scsicmd of APUCQ_t
	//CDB BYTE 1
	U8 sp		: 1;
	U8 btRes1	: 3;
	U8 pf		: 1;
	U8 btRes2	: 3;
	//CDB BYTE 2~3
	U8 bRes1[2];
	//CDB BYTE 4
	U8 bParameterListLen;
	//CDB BYTE 5
	U8 bControl;
} MODE_SELECT_6_CMD_t, *pMODE_SELECT_6_CMD_t;

typedef struct {
	//Mapping to BYTE1 to BYTE9 of scsicmd of APUCQ_t
	//CDB BYTE 1
	U8 sp		: 1;
	U8 btRes1	: 3;
	U8 pf		: 1;
	U8 btRes2	: 3;
	//CDB BYTE 2~6
	U8 bRes1[5];
	//CDB BYTE 7~8
	U8 bParameterListLen_H;
	U8 bParameterListLen_L;
	//CDB BYTE 9
	U8 bControl;
} MODE_SELECT_10_CMD_t, *pMODE_SELECT_10_CMD_t;

typedef struct {
	U8 ubModeDataLen;
	U8 ubMediumType;
	U8 ubDeviceSpecificParameter;
	U8 ubBlkDescriptorLen;
} ModeParameterHeader_6_t;

typedef struct {
	U8 ubModeDataLen_H;
	U8 ubModeDataLen_L;
	U8 ubMediumType;
	U8 ubDeviceSpecificParameter;
	U8 btLongLBA	: 1;
	U8 btReserve1	: 7;
	U8 ubReserve2;
	U8 ubBlkDescriptorLen_H;
	U8 ubBlkDescriptorLen_L;
} ModeParameterHeader_10_t;

typedef union {

	U32 ulBlkDescriptor[2];
	struct {
		U32 ubDensityCode	: 8;
		U32 ubNumOfBlks_H	: 8;
		U32 ubNumOfBlks_M	: 8;
		U32 ubNumOfBlks_L	: 8;
		U32 ubReserve		: 8;
		U32 ubBlkLen_H		: 8;
		U32 ubBlkLen_M		: 8;
		U32 ubBlkLen_L		: 8;
	} GeneralModeParaBlkDescriptor;

} ModeParameterBlkDescriptor_t;

typedef union {
	U8 ubModePage[64]; // Predefined size of reserved for needed maximum size of mode page

	// The struct defined from each mode page, reference from SCSI Block Commands (SBC)
	struct {
		// BYTE 0
		U8 btPageCode						: 6;
		U8 btSPF							: 1;
		U8 btPS								: 1;
		// BYTE 1
		U8 ubPageLen;
		// BYTE 2
		U8 btRCD							: 1; // read cache disable
		U8 btMF								: 1; // multiplication factor
		U8 btWCE							: 1; // writeback cache enable
		U8 btSIZE							: 1; // size enable
		U8 btDISC							: 1; // discontinuity
		U8 btCAP							: 1; // caching analysis permitted
		U8 btABPF							: 1; // abort pre-fetch
		U8 btIC								: 1; // initiator control
		// BYTE 3
		U8 btWriteRetentionPriority			: 4;
		U8 btDemandReadRetentionPriority	: 4;
		// BYTE 4 ~ 5
		U16 uwDisPrefetchTransferLen;
		// BYTE 6 ~ 7
		U16 uwMinPrefetch;
		// BYTE 8 ~ 9
		U16 uwMaxPrefetch;
		// BYTE 10 ~ 11
		U16 uwMaxPrefetchCeiling;
		// BYTE 12
		U8 btNV_DIS							: 1; // non-volatile cache
		U8 btReserve						: 2;
		U8 btVendorSpecific					: 2;
		U8 btDRA							: 1; // disable read-ahead
		U8 btLBCSS							: 1; // logical block cache segment size
		U8 btFSW							: 1; // force sequential write
		// BYTE 13
		U8 ubNumOfCacheSegments;
		// BYTE 14 ~ 15
		U16 uwCacheSegmentSize;
		// BYTE 16
		U8 ubReserve1;
		// BYTE 17 ~ 19
		U8 Obsolete[3];
		// BYTE 20~63
		U8 ubReserve2[44];
	} CachingModePage;
} ModeParameterModePage_t;

typedef union {
	ATA_PASS_THROUGH_CMD_t stATAPassThroughCmd;
	UNMAP_CMD_t stUnmapCmd;
} SCSI_CDB_t, *pSCSI_CDB_t;

typedef union {
	U32	ulDW[4];
	struct {
		// DW0
		U8 ubIUID;
		U8 ubRsv0;
		U8 ubTag_H;
		U8 ubTag_L;
		// DW1
		U8 ubTaskMgnFunc;
		U8 ubRsv1;
		U8 ubTagOfTaskToBeMgn_H;
		U8 ubTagOfTaskToBeMgn_L;
		// DW2
		U8 ubLUN[7];
		U8 ubLUN_LSB;
	} CDB;
} TMIU_SCSI_CDB_t, *pTMIU_SCSI_CDB_t;

/*=====================================================================================
                                    Macro Definition
=====================================================================================*/
/* Setup Command */
#define GETDESCRIPTOR							(0x06U)
/* Setup Command Value */
#define DEVICE									(0x01)
#define CONFIGURATION							(0x02)
#define STRING									(0x03)
#define INTERFACE								(0x04)
#define ENDPOINT								(0x05)
#define DEVICE_QUALIFIER						(0x06)
#define OTHER_SPEED_CONFIGURATION				(0x07)
#define INTERFACE_POWER							(0x08)
#define BOS_ID									(0x0F)
#define REPORT_DESC								(0x22)
#define LANGUAGE_ID								(0x00)
/* Setup Command Length*/
#define DEVICE_LENGTH							(0x12)

#define CONFIG_LENGTH_UASP						(0x79)
#define CONFIG_LENGTH20_UASP					(0x55)

#define CONFIG_LENGTH_BOT						(0x2C)
#define CONFIG_LENGTH20_BOT						(0x20)

#define STRING_LENGTH0							(4U)
#define STRING_LENGTH1							(34U)
#define STRING_LENGTH2							(26U)
#define STRING_LENGTH3							(34U)
#define DEVICE_QUALIFIER_LENGTH					(0x0A)
#define OTHER_SPEED_CONFIGURATION_LENGTH		(0x20)	/*0x27U*/
#define SERIAL_LENGTH							(STRING_LENGTH3)
#define BOS_LENGTH								(0x2A)

#define OTHER_SPEED_CONFIG_LENGTH				(0x20U)	/*0x27U*/

/* Bulk Command */
#define TEST_UNIT_READY							(0x00U)
#define REQUEST_SENSE							(0x03U)
#define PHISON_VENDOR							(0x06U)
#define USB_SEC_PROTOCOL_VENDOR					(0xFEU)
#define USB_SPSP_SEND_SQ						(0xC0U)
#define USB_SPSP_WRITE_DATA						(0xC1U)
#define USB_SPSP_READ_DATA						(0xC2U)
#define USB_SPSP_GET_CQ							(0xC3U)
#define PHISON_VENDOR_LINUX						(0xC6U)
#define INQUIRY									(0x12U)
#define Send_Diagnostic							(0x1DU)
#define Format_Unit								(0x04U)
#define MODE_SELECT_6							(0x15U)
#define MODE_SELECT_10							(0x55U)
#define MODE_SENSE_6							(0x1AU)
#define START_STOP								(0x1BU)
#define PREVENT_ALLOW							(0x1EU)
#define READ_FORMAT_CAPACITIES					(0x23U)
#define READ_CAPACITY							(0x25U)
#define SYNC_READ								(0x28U)
#define SYNC_WRITE								(0x2AU)
#define Seek_10 								(0x2BU)
#define DO_SEEK									(0x2FU)
#define WriteAndVerify							(0x2EU)
#define READ_6									(0x08U)
#define WRITE_6									(0x0AU)
#define READ_10									(0x28U)
#define WRITE_10								(0x2AU)
#define READ_12									(0xA8U)
#define WRITE_12								(0xAAU)
#define READ_16									(0x88U)
#define WRITE_16								(0x8AU)
#define SYNC_CACHE_10							(0x35U)
#define UNMAP									(0x42U)
#define MODE_SENSE_10							(0x5AU)
#define SYNC_CACHE_16							(0x91U)
#define READ_CAPACITY_16						(0x9EU)
#define REPORT_LUN								(0xA0U)
#define ATA_PASS_THROUGH_12						(0xA1U)
#define SECURITY_PROTOCOL_IN					(0xA2U)
#define MANAGEMENT_PROTOCOL_IN					(0xA3U)
#define USB_DIRECT_READ							(0xC0U)
#define VUC_FOR_CRYSTAL							(0xE6U)
#define VS_COMMAND								(0xF7U)
#define VS_GET_ErrorRecoveryStatistics			(0x0FU)
#define VS_Get_Temperature						(0x11U)
#define VS_L2P          						(0x0DU)
#define VS_P2L		            				(0x0EU)
#define VS_GET_ReadDisturbCountLimit			(0x28U)
#define VS_GET_VT_SWEEP							(0x2AU)
#define VS_GET_BEC								(0x38U)
#define VS_GET_UnifiedEventLog					(0x3BU)
#define VS_GET_UnifiedEventLogThreshold			(0x3CU)
#define VS_SET_UnifiedEventLogThreshold			(0x3DU)
#define VS_GET_MAG_INFORMATION					(0x3EU)
#define VS_GNPT									(0x16U)
#define VS_COMMAND_CLASS_04						(0x04U)
#define VS_COMMAND_CLASS_02						(0x02U)
#define VS_GET_MLBi		            			(0x19U)// for test
#define VS_SET_MLBi     						(0x18U)// for test
#define VS_GET_Nand_Block_Mode  				(0x03U)// for test
#define FEATURE_CONFIGURE						(0x16U)
#define GET_FEATURE								(0x00U)
#define SET_FEATURE								(0x02U)
#define GET_AC_TIMING							(0x09U)
#define GET_NAND_CONFIG							(0x0AU)
#define GET_ALL_FLASH_STATUS					(0x0BU)

/* Bulk Packet Length */
#define FS_BULK_PACKET_LEN						(0x40U)
#define HS_BULK_PACKET_LEN						(0x200U)
#define SS_BULK_PACKET_LEN						(0x400U)
#define SSP_BULK_PACKET_LEN						(0x400U)

/*ATAPassThrough12*/
#define IDENTIFY_DEVICE							(0xEC)
#define SMART_COMMAND							(0xB0)

/* SMART SubCommand */
#define SMART_READ_DATA							(0xD0) // Return the Device SMART data to the host (see ATA8-ACS-3)
#define SMART_READ_THRESHOLD					(0xD1) // Return the Device attribute thresholds to the host (see ATA-3)
#define SMART_EN_DIS_ATTRIBUTE_AUTOSAVE			(0xD2) // Enable or disables the attribute autosave feature of the Device. (see ATA8-ACS-3)
#define SMART_SAVE_ATTRIBUTE_VALUE				(0xD3) // Cause the Device to immediately save attribute values regardless of the state of the attribute autosave timer (see ATA-6)
#define SMART_EXECUTE_OFFLINE_IMMEDIATE			(0xD4) // Cause the Device to initiate the set of activities that collect SMART data... (see ATA8-ACS-3)
#define SMART_READ_LOG							(0xD5) // Return the specified log to the host (see 7.46.2)
#define SMART_WRITE_LOG							(0xD6) // Cause the Device to write the specified number of log pages to the specified log (see 7.46.4)
#define SMART_ENABLE_OPERATION					(0xD8) // Enable access to all available SMART capabilities within the Device (see ATA8-ACS-2)
#define SMART_DISABLE_OPERATION					(0xD9) // Disable all SMART operations (see ATA8-ACS-2)
#define SMART_RETURN_STATUS						(0xDA) // Cause the Device to communicate the reliability status of the device to the host (see 7.46.3)
#define SMART_EN_DIS_ATTRIBUTE_AUTO_OFFLINE		(0xDB) // (see S11)

/* Inquiry Page Code*/
#define INQUIRY_PAGECODE_0x00					(0x00)
#define INQUIRY_PAGECODE_0x80					(0x80)
#define INQUIRY_PAGECODE_0x83					(0x83)
#define INQUIRY_PAGECODE_0xB0					(0xB0)
#define INQUIRY_PAGECODE_0xB2					(0xB2)
#define USB_SCSI_INQUIRY_VERSION_DESCRIPTOR_1	(0xC004)
#define USB_SCSI_INQUIRY_VERSION_DESCRIPTOR_2	(0x6004)
#define USB_SCSI_INQUIRY_VERSION_DESCRIPTOR_3	(0x4717)

/* Request Sense */
#define REQUEST_SENSE_DESCRIPTION_FORMAT_BIT	(0x01)

/* Mode Page Codes Related*/
#define USB_SCSI_MODE_PAGE_CODE_ERROR_RECOVERY		(0x01) //Read-Write error recovery
#define USB_SCSI_MODE_PAGE_CODE_FORMAT_DEVICE		(0x03) //Format device
#define USB_SCSI_MODE_PAGE_CODE_RIGID_DISK_GEOMETRY	(0x04) //Rigid Disk Geometry
#define USB_SCSI_MODE_PAGE_CODE_CACHING				(0x08) //The Caching Mode page for MODE SENSE/MODE SELECT defines the parameters that affect the use of the cache
#define USB_SCSI_MODE_PAGE_CODE_CACHING_LEN			(0x12)
#define USB_SCSI_MODE_PAGE_CODE_POWER_CONDITION		(0x1A) //Power condition
#define USB_SCSI_MODE_PAGE_CODE_RESTRICTED_1C		(0x1C) //SubPageCode 00, Informational Exceptions Control
#define USB_SCSI_MODE_PAGE_CODE_ALL_PAGE			(0x3F) //Return all pages and subpages
#define USB_SCSI_MODE_PAGE_MASK_DBD					(0x08)
#define USB_SCSI_MODE_PAGE_MASK_PAGE_CODE			(0x3F)
#define USB_SCSI_MODE_PAGE_MASK_PAGE_CONTROL		(0xC0)
#define USB_SCSI_MODE_PAGE_MASK_SPF					(0x40)

/* Read Capacity Related */
#define USB_SCSI_READ_CAPACITY_SERVICE_ACTION		(0x10)
#define USB_SCSI_READ_CAPACITY_MASK_SERVICE_ACTION	(0x1F)

/* Unmap Command setup value */
#define MAXIMUM_UNMAP_BLOCK_DESCRIPTOR_COUNT (TRIM_CMD_RANGE_NUM_MAX - 1)

/* extern global variables */
extern ModeParameterModePage_t gDefaultCachingModePage;
extern ModeParameterModePage_t gCurrentCachingModePage;

#if (USB_WORKAROUND_EOB_TWICE_EN)
#define CMD_DATA_BYTESIN4K 4096
#endif /* (USB_WORKAROUND_EOB_TWICE_EN) */
/*=====================================================================================
                                  Function Declaretion
=====================================================================================*/
/* usb_cmd.c */
U8 USBCheckIOR(PAPUCQ_t pAPUCQ);
AOM_USB U8 USBCheckWriteProtect(void);

AOM_USB U8 USBCheckReadCmd(PAPUCQ_t pAPUCQ);
AOM_USB U8 USBCheckWriteCmd(PAPUCQ_t pAPUCQ);
AOM_USB U8 USBCheckWriteTypeCmd(PAPUCQ_t pAPUCQ);
AOM_USB U64 USBCalculateStartLBA(PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBCalculateRemainPacketFromCBW(PUSBCURRCMD_t pCurrentCMD);

void USBFillCurrCmdInfo(PUSBCURRCMD_t pCurrentCMD, PAPUCQ_t pAPUCQ);

AOM_USB void USBCmdErrHandler(PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBFillAutoResponseCMD_Default(void);
void USBFillAutoResponseCMD_Config(void);
/* usb_setup_cmd.c */
AOM_USB void USBSetupCmdHandler(PUSBCURRCMD_t pCurrentCMD);

AOM_USB void USBSetupStandardCmd(PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBSetupClassCmd(PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBSetupVendorCmd(PUSBCURRCMD_t pCurrentCMD);

/* usb_bulk_cmd.c*/
AOM_USB void USBGetBulkCmdHandler(PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBCmdDispatcher(PUSBCURRCMD_t pCurrentCMD);
#if (USB_WORKAROUND_EOB_TWICE_EN)
void USBCheckUSBACPLWA(void);
#endif /* (USB_WORKAROUND_EOB_TWICE_EN) */
void USBCheckAckRetryWA(U32 ulDataXferByteCnt);
void USBWaitReadIdle(void);
U8 USBCmdCheckSaveStateDis(void);

/* Fill buffer */
AOM_USB void USBFillModeSense10(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBFillDataFromSourceAddr(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBFillReadCapacity(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBFillReportLun(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBFillRequestSense(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBFillModeSense6(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBFillIdentifyDeviceData(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBFillSMARTReadThreshold(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBFillSMARTReadData(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBFillReadFormatCapacities(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBFillReadCapacity16(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBFillSecurityProtocolIn(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBFillBulkGetLun(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD);
AOM_USB	void USBHandleUnmap(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBHandleSyncCache(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBCmdDirectRead(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD);

AOM_USB void USBCopyASMediaSMARTVendorCMD(PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBFillIdentifyForNVME_ASMedia(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBFillSMARTInfoForNVME_ASMedia(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBFillSMARTAttributeData(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBFillSMARTInfoForNVME(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD);

/* USB Command*/
#if (!BURNER_MODE_EN)
AOM_USB void USBCmdVendorReadInfo(PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBCmdVendorUpdateInfo(PUSBCURRCMD_t pCurrentCMD);
#endif /* (!BURNER_MODE_EN) */
AOM_USB void USBCmdVendorChangeSpeed(PUSBCURRCMD_t pCurrentCMD);
AOM_VUC void USBCmdVendorGetMAGInformation(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBCmdVendorGetErrorRecoveryStatistics(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBCmdVendorGetReadDisturbCountLimit(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBCmdVendorGetVtSweep(U32 ulPayloadAddr, PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBCmdVendorGetBEC(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBCmdVendorGetUnifiedEventLog(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBCmdVendorGetUnifiedEventLogThreshold(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBCmdVendorSetUnifiedEventLogThreshold(PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBCmdVendorGetMLBi(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD); // for test
AOM_USB void USBCmdVendorSetMLBi(PUSBCURRCMD_t pCurrentCMD); // for test
AOM_USB void USBCmdVendorGetNandBlockMode(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD); // for test
AOM_USB void USBCmdVendorGetTemperature(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBCmdVendorL2P(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBCmdVendorP2L(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBCmdVendorGNPT(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBCmdVendorCrucialVSCommand(PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBCmdVendorGetFlashID(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBCmdVendorFeatureConfigure(PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBCmdVendorSetFeature(PUSBCURRCMD_t pCurrentCMD);
AOM_VUC_3 void USBCmdVendorGetFeature(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBCmdInquiry(PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBCmdReadCapacity(PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBCmdReadCapacity16(PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBCmdSyncCache(PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBCmdInvalidCmd(PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBCmdTestUnitReady(PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBCmdReadFormatCapacities(PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBCmdReportLun(PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBCmdRequestSense(PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBCmdModeSense6(PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBCmdUnmap(PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBCmdATAPassThrough12(PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBCmdSecurityProtocolIn(PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBCmdManagementProtocolIn(PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBCmdPreventAllow(PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBCmdStartStop(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBCmdReadWriteZeroLength(PUSBCURRCMD_t pCurrentCMD);

AOM_VUC void USBCmdVendorGetACTiming(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD);
AOM_VUC void GetACTimingCheck();
AOM_USB2 void USBCmdVendorGetNandConfig(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD);

AOM_USB void USBSetStatus(PUSBCURRCMD_t pCurrentCMD, U8 bRespHOST, enumUSBSenseKey	enumSenseKey, U8 ubASC, U8 ubASQ, U8 ubCmdStatus, U8 btRespCode);
AOM_USB void USBCmdXferCPL2APU(PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBMediaOK(PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBEP1Stall(PUSBCURRCMD_t pCurrentCMD);

AOM_USB U8 USBCalCheckSum(U32 ulBufAddr, U32 ulBufSize);
AOM_USB void USBCmdModeSense10(PUSBCURRCMD_t pCurrentCMD);
AOM_USB void USBTMIUHandler(PUSBCURRCMD_t pCurrentCMD);

AOM_USB void USBWaitStatusXferDone(U32 ulTDbitmap);
void USBWaitCplComplete(void);
#if (!BURNER_MODE_EN)
void USBPollingLUNReset(void);
U8 USBCheckLunReset(void);
#endif /* (!BURNER_MODE_EN) */
AOM_USB void USBCmdContentPatch(void);

/* Special Vendor Feature */

// Temp

#endif /* (USB == HOST_MODE) */

#endif /* _USB_CMD_H_ */

