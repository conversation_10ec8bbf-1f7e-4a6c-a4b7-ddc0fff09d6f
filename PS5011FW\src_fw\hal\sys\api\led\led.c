#include "led_api.h"
#include "hal/sys/api/mux/mux_api.h"

LEDModule_t gLED;

void LEDSetLEDPattern(U8 ubLEDLightMode, U8 ubLEDWaveMode)
{
#if PS5017_EN
	U16 uwLEDLowTime = gLED.uwONTime / LED_LOW_LT_UNIT;
	M_MUX_CTRL_BY_APU();
	M_LED_DISABLE();
	M_LED_SEL_LED_MODE(ubLEDLightMode);
	M_LED_SEL_LED_WAVE_MODE(ubLEDWaveMode);
	M_LED_SEL_LOW_LT_DEFINE(uwLEDLowTime);
#elif PS5021_EN /* PS5017_EN */
	M_MUX_CTRL_BY_LED_CTRL2_9();
	M_LED_DISABLE();
#else /* PS5017_EN */
	U16 uwLEDLowTime = gLED.uwONTime / LED_LOW_LT_UNIT;
	M_MUX_CTRL_BY_LED();
	M_LED_DISABLE();
	M_LED_SEL_LED_MODE(ubLEDLightMode);
	M_LED_SEL_LED_WAVE_MODE(ubLEDWaveMode);
	M_LED_SEL_LOW_LT_DEFINE(uwLEDLowTime);
#endif /* PS5017_EN */
}
