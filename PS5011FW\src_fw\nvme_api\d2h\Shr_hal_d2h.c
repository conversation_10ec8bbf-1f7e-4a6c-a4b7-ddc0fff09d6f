/*********************************************************************
 *                                                                   *
 *        Copyright (c) 2000-2017 by Phison Electronics Corp.        *
 *                                                                   *
 *********************************************************************
 *                                                                   *
 *  FILE : shr_hal_d2h.c                   PROJECT : 5011 model      *
 *                                                                   *
 *  AUTHOR :                               DATE : 2017-04-27         *
 *                                                                   *
 *  DESCRIPTION                                                      *
 *                                                                   *
 *    This file implements doorbell.                                 *
 *                                                                   *
 *  VERSION                                                          *
 *                                                                   *
 *    CHANGE                         NAME               DATE         *
 *                                                                   *
 *********************************************************************/

#define _SHR_HAL_D2H_C_

// goodid
//#include "hal\db\shr_hal_db_reg.h"
//#include "hal\db\shr_hal_db.h"

//#include "nvme_api\db\shr_hal_db_reg.h"//merge@@
#include "nvme_api/db/shr_hal_db.h"//merge@@
#include "nvme_api/d2h/shr_hal_d2h.h"

// goodid
#if 0 //merge@@
#include "misc/shr_def.h"
#else
#include "common/symbol.h"
#include "common/math_op.h"
#endif


#if 0 //merge@@
#include "nvme_api/misc/shr_debug.h"
#else
//#include "debug/debug.h"
#include "nvme_api/misc/shr_hal_debug.h"
#endif
// goodid
//#include "hal\d2h\shr_hal_d2h_reg.h"
//#include "hal\d2h\shr_hal_d2h.h"
//#include "hal\nvme\shr_hal_nvme.h"
//#include "hal\pcie\shr_hal_pcie.h"
#include "nvme_api/d2h/shr_hal_d2h_reg.h"
#include "nvme_api/d2h/shr_hal_d2h.h"
//#include "hal\nvme\shr_hal_nvme.h"
#include "nvme_api/nvme/shr_hal_nvme.h"
//#include "hal\pcie\shr_hal_pcie.h"

// goodid
#include "nvme_api/system/shr_hal_sys_pmu_reg.h"
//#include "nvme_api\pcie\shr_hal_pcie_reg.h"
#include "nvme_api/pcie/shr_hal_pcie.h"//merge@@
//#include "nvme_api/bmu/shr_hal_bmu_lib.h"//merge@@

// goodid
#if 0 //merge
#include "nvme_api/pic/shr_hal_pic_uartt.h"
#else
//#include "hal/pic/uart/uart_api.h"
#include "nvme_api/pic/shr_hal_pic_uartt.h"
#endif

#include "trim/ftl_trim_api.h"
#include "ftl/ftl_nrw_api.h"
#include "hal/dmac/dmac_pop_cmd.h"

#define _SHR_HAL_D2H_C_

// fixme:  not a thread safe mechanism, should avoid global variable
// static HMB_HANDLE gHmbHandle = {NULL, NULL, NULL}; => moved to Shr_hal_nvme_other.h
// fixme: temporary use gpHostPrtInfo due to device memory located in global data area
#include "nvme_api/nvme/shr_hal_nvme_other.h" // for reference structure: nvme_prt_host_geometry_struct
#include "nvme_api/nvme/shr_hal_nvme_api.h"   // for reference global variable gpHostPrtInfo


void d2h_AES_PATH_init(U8 bank_id, U32 length, U32 localAddr, U64 hostAddr)
{
	R32_D2H[D2HL_CR_RLEN_W32 + bank_id] = length;
	R32_D2H[D2HL_CR_RADDR32_W32 + bank_id] = localAddr;
	R64_D2H[D2HLL_CR_RADDR64_W64 + bank_id] = hostAddr;
}

void d2h_RMP_PATH_init(U32 length, U32 localAddr, U64 hostAddr)
{
	R32_D2H[D2HL_RMAP_DLEN_0x100] = length;
	R32_D2H[D2HL_RMAP_DADDR32_0x104] = localAddr;
	R64_D2H[D2HLL_RMAP_HADDR_0x108] = hostAddr;
}

void d2h_Backup_Reg_for_RMP(U32 *pulRmpDataLength, U32 *pulDataAddr, U64 *puoHostAddr)
{
	*pulRmpDataLength = R32_D2H[D2HL_RMAP_DLEN_0x100];
	*pulDataAddr = R32_D2H[D2HL_RMAP_DADDR32_0x104];
	*puoHostAddr = R64_D2H[D2HLL_RMAP_HADDR_0x108];
}

void d2h_Restore_Reg_for_RMP(U32 *pulRmpDataLength, U32 *pulDataAddr, U64 *puoHostAddr)
{
	R32_D2H[D2HL_RMAP_DLEN_0x100] = *pulRmpDataLength;
	R32_D2H[D2HL_RMAP_DADDR32_0x104] = *pulDataAddr;
	R64_D2H[D2HLL_RMAP_HADDR_0x108] = *puoHostAddr;
}

void d2h_AES_PATH_get(U8 bank_id, U32 *length, U32 *localAddr, U64 *hostAddr)
{
	*length = R32_D2H[D2HL_CR_RLEN_W32 + bank_id];
	*localAddr = R32_D2H[D2HL_CR_RADDR32_W32 + bank_id];
	*hostAddr = R64_D2H[D2HLL_CR_RADDR64_W64 + bank_id];
}

void d2h_RMP_PATH_get(U32 *length, U32 *localAddr, U64 *hostAddr)
{
	*length = R32_D2H[D2HL_RMAP_DLEN_0x100];
	*localAddr = R32_D2H[D2HL_RMAP_DADDR32_0x104];
	*hostAddr = R64_D2H[D2HLL_RMAP_HADDR_0x108];
}

void d2h_auto_resp_set(U32 setting)
{
	// fixme: chip id = 5011
	setting = setting & 0x3;
	R32_D2H[D2HL_CR_AUTO_RESP_0x210] = setting;
}


void d2h_Int_set(U32 setting)
{
	// fixme: chip id = 5011
	setting = setting & 0x3FF0000;
	R32_D2H[D2HB_CR_ERR_INFO_0x200] = setting;
}

void d2h_Err_insert(U32 setting)
{
	// fixme: chip id = 5011
	setting = setting & 0xF;
	R32_D2H[D2HL_CR_ERR_INS_0x204] = setting;
}


void d2h_int_handler()
{
	volatile U32 status;
	P_HOST_GEOMETRY_PTR pHostPrtInfo = (P_HOST_GEOMETRY_PTR) gpHostPrtInfo;
	HMB_HANDLE *handle = pHostPrtInfo->pHmbHandle;
	HMB_GLOBAL_DATA *data = handle->data;

	status = R32_D2H[D2HB_CR_ERR_INFO_0x200];

	if (status & (D2H_AXI_RDATA_ERROR | D2H_AXI_BRESP_ERROR | D2H_AXI_RRESP_ERROR)) {
		d2h_Err_insert(0);
	}

	// there may not be any higer priority interrupt to enter this function
	// overwrite last interrupt result
	if (data != NULL) {
		data->intInfo.count++;
		data->intInfo.status = status;
		data->intInfo.address = R32_D2H[D2HL_CR_ERR_INFO_ADDR];
	}

	// clear  interrupt
	R32_D2H[D2HB_CR_ERR_INFO_0x200] = status;
#if !VS_SIM_EN
	__asm("DSB");
#endif

#if 0
	// make sure interrupt is cleared (test purpose)
	status = R32_D2H[D2HB_CR_ERR_INFO_0x200];
	while ((status & 0x3FF) != 0) {
		status = R32_D2H[D2HB_CR_ERR_INFO_0x200];
	}
#endif
}

AOM_HMB void _HMB_entry_swap(HMB_LIST_ENTRY *A, HMB_LIST_ENTRY *B)
{
	HMB_LIST_ENTRY temp;

	memcpy(&temp, A, sizeof(HMB_LIST_ENTRY));
	memcpy(A, B, sizeof(HMB_LIST_ENTRY));
	memcpy(B, &temp, sizeof(HMB_LIST_ENTRY));
}

AOM_HMB void _HMB_bubble_sort(HMB_LIST *list)
{
	U32 i, j, flag = 1;

	for (i = 0; i < (list->size - 1) && flag; i++) {
		flag = 0;
		for (j = 0; j < list->size - i - 1; j++) {
			if (list->entry[j + 1].freeBytes > list->entry[j].freeBytes) {
				// swap
				_HMB_entry_swap(&(list->entry[j + 1]), &(list->entry[j]));
				flag = 1;
			}
		}
	}
}

U32 d2h_get_start_address(U32 type)
{
	switch (type) {
	case D2H_Z1: {
			return D2H_Z1_RAM_ADDRESS;
			break;
		}
	case D2H_Z2: {
			return D2H_Z2_RAM_ADDRESS;
			break;
		}
	case D2H_REMAP: {
			return DUMMY_REMAP_ADDRESS;
			break;
		}
	default : {
			return NULL;
		}
	}

	return NULL;
}

/*
* Create HMB list from HMB command
* Sort HMB entry by length desc and apply to HMB list in sequence
* HMB list contained memory block ordered from large to small
*/

AOM_NRW_2 void d2h_create_HMB_list(U32 ulHMDLEC, U64 ullHMDL, U32 ulMPS, HMB_LIST *freeMemList)
{
	U32 ulNum = 0;
	U32 ulEntryNum, ulBSIZE, ulNewEntryNum;
	U64 ullBADD;
	volatile U64 *ulpGetDataPtr = NULL;
	DMACParam_t DMACParam = {{0}};
	U64 uoTotalSize, uoMemSetSizeFor32ByteAlignment;

	d2h_RMP_PATH_init (BC_16MB, DUMMY_REMAP_ADDRESS, ullHMDL);
	ulpGetDataPtr = (U64 *) DUMMY_REMAP_ADDRESS;

	// limit HMB entry to MAX_HMB_ENTRY_FROM_HOST
	if (ulHMDLEC > MAX_HMB_ENTRY_FROM_HOST) {
		ulEntryNum = MAX_HMB_ENTRY_FROM_HOST;
	}
	else {
		ulEntryNum = ulHMDLEC;
	}

	// allocate buffer
	TrimRangeNVMERaw_t *pulTempBuffer1 = (TrimRangeNVMERaw_t *)M_PB_TO_ADDR(nvme_cur_HCMD.cur_phyMemAddr);
	TrimRangeNVMERaw_t *pulTempBuffer2 = (TrimRangeNVMERaw_t *)(M_PB_TO_ADDR(nvme_cur_HCMD.cur_phyMemAddr) + SIZE_2KB);

	// assign HMB entry to HMB list
	freeMemList->size = ulEntryNum;
	for (ulNum = 0; ulNum < ulEntryNum; ulNum++) {
		ullBADD = *(ulpGetDataPtr + (ulNum * 2));
		ulBSIZE = (U32) * (ulpGetDataPtr + (ulNum * 2) + 1);
		pulTempBuffer1[ulNum].ulAttribute = 0;
		pulTempBuffer1[ulNum].uoStartLBA = ullBADD;
		pulTempBuffer1[ulNum].ulLength = ulBSIZE * ulMPS;
	}

	//==============================================================
	//	Before sort, check 32B-alignment
	//	If not align 32B, need to set 1s (Sort by ascending order)
	//==============================================================
	uoTotalSize = ulEntryNum * sizeof(TrimRangeNVMERaw_t);
	uoMemSetSizeFor32ByteAlignment = DELTA_TO_ALIGN_32B(uoTotalSize);

	if (0 != uoMemSetSizeFor32ByteAlignment) {
		memset((void *)((U32)pulTempBuffer1 + uoTotalSize), 0xFF, uoMemSetSizeFor32ByteAlignment);
	}

	//==================================================
	//  Sort (Result is in Source Buffer)
	//  "pulTempBuffer1" : Source Buffer
	//  "pulTempBuffer2"   : Temp Buffer
	//==================================================
	DMACParam.DMACSort.ulSourceAddr = (U32)pulTempBuffer1;
	DMACParam.DMACSort.ulDestAddr = (U32)pulTempBuffer2;
	DMACParam.DMACSort.ul32ByteNum = SIZE_IN_32B_CEILING(uoTotalSize);
	DMACParam.DMACSort.ubOrder = DMAC_SORT_ORDER_ASCEND;
	DMACParam.DMACSort.ubEntrySize = DMAC_SORT_ENTRY_SIZE_16B;
	DMACAPISortBit(&DMACParam, (U32)gulDMACDirectWaitDone_Callback, HMB_RANGE_START_LBA_OFFSET, HMB_RANGE_START_LBA_BIT_NUM);

	gDMACDirectWaitCQInfo.ubDoneFlag = FALSE;
	while (!gDMACDirectWaitCQInfo.ubDoneFlag) {
		DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
	}

	//===============================================================
	//  Merge HMB Ranges
	//===============================================================
	ulNewEntryNum = 1;

	for (ulNum = 1; ulNum < ulEntryNum; ulNum++) {
		if (pulTempBuffer1[ulNum].uoStartLBA <= (pulTempBuffer1[ulNewEntryNum - 1].uoStartLBA + pulTempBuffer1[ulNewEntryNum - 1].ulLength)) {
			if ( (pulTempBuffer1[ulNum].uoStartLBA + pulTempBuffer1[ulNum].ulLength) > pulTempBuffer1[ulNewEntryNum - 1].uoStartLBA + pulTempBuffer1[ulNewEntryNum - 1].ulLength) {
				pulTempBuffer1[ulNewEntryNum - 1].ulLength = pulTempBuffer1[ulNum].uoStartLBA + pulTempBuffer1[ulNum].ulLength - pulTempBuffer1[ulNewEntryNum - 1].uoStartLBA;
			}
		}
		else {
			pulTempBuffer1[ulNewEntryNum].uoStartLBA = pulTempBuffer1[ulNum].uoStartLBA;
			pulTempBuffer1[ulNewEntryNum].ulLength = pulTempBuffer1[ulNum].ulLength;
			++ulNewEntryNum;
		}
	}

	//Restore Format
	for (ulNum = 0; ulNum < ulNewEntryNum; ++ulNum) {
		freeMemList->entry[ulNum].freeBytes = pulTempBuffer1[ulNum].ulLength;
		freeMemList->entry[ulNum].startAddress = pulTempBuffer1[ulNum].uoStartLBA;
		freeMemList->entry[ulNum].endAddress = pulTempBuffer1[ulNum].uoStartLBA + freeMemList->entry[ulNum].freeBytes;
	}
}

U32 d2h_align_unit(U32 type, U32 memSide)
{
	// chip id = 5011
	if (type == D2H_Z1) {
		if (memSide == D2H_HOST) {
			return D2H_AES_Z1_HOSTLEN_ALIGN;
		}
		else if (memSide == D2H_DEVICE) {
			return D2H_AES_Z1_LOCALLEN_ALIGN;
		}
	}
	else if (type == D2H_Z2) {
		if (memSide == D2H_HOST) {
			return D2H_AES_Z2_HOSTLEN_ALIGN;
		}
		else if (memSide == D2H_DEVICE) {
			return D2H_AES_Z2_LOCALLEN_ALIGN;
		}
	}
	else if (type == D2H_REMAP) {
		if (memSide == D2H_HOST) {
			return D2H_REMP_HOSTADDR_ALIGN;
		}
		else if (memSide == D2H_DEVICE) {
			return D2H_REMP_HOSTADDR_ALIGN;
		}
	}
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
	return 0;

}

/*
* check whether input length + protect info align HW spec
* return zero if aligned, otherwise, return not zero
*/

AOM_HMB U32 d2h_host_length_align(U32 type, U64 length)
{
	// if chip id is PS5011
	if (type == D2H_Z1) {
		return length % d2h_align_unit(D2H_Z1, D2H_HOST);
	}
	else if (type == D2H_Z2) {
		return length % d2h_align_unit(D2H_Z2, D2H_HOST);
	}
	else if (type == D2H_REMAP) {
		return D2H_REMP_HOSTADDR_ALIGN;
	}
	else {
		return 1;
	}
}

/*
* check whether input length info align HW spec
* return zero if aligned, otherwise, return not zero
*/
AOM_HMB U32 d2h_local_length_align(U32 type, U64 length)
{
	// if chip id is PS5011
	if (type == D2H_Z1) {
		return length % d2h_align_unit(D2H_Z1, D2H_DEVICE);
	}
	else if (type == D2H_Z2) {
		return length % d2h_align_unit(D2H_Z2, D2H_DEVICE);
	}
	else if (type == D2H_REMAP) {
		return length % d2h_align_unit(D2H_REMAP, D2H_DEVICE);
	}
	else {
		return 1;
	}
}

/*
*  withinRange: return whether address located within an occupied block
*  if address laid within an occupied block, return the block entry, and set withinRange = 1
*  otherwise return the block entry of smallest occupied address entry, set withinRange = 0
*  if address is larger than every occupied block, return NULL (withinRange = 0)
*/
HMB_MEMORY_MAP_ENTRY *
AOM_HMB _get_occupied_entry(U32 listIndex, U64 address, U32 *withinRange, HMB_MEMORY_MAP *memMap)
{
	HMB_MEMORY_MAP_ENTRY *mapEntry = NULL, *retEntry = NULL;
	U32 i;

	// gD2HMemMap take zero as empty address
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (address != 0));

	for (i = 0; i < MAX_HMB_ENTEY_SUPPORT; i++) {
		mapEntry = &memMap->entry[i];
		if (mapEntry->type != D2H_EMPTY) {
			if (listIndex == mapEntry->index) {
				// address lay within occupied block
				if (address >= mapEntry->address &&
					address < mapEntry->address +  mapEntry->length) {
					*withinRange = 1;
					return mapEntry;
				}

				//record the smallest entry address
				if (mapEntry->address >= address) {
					// retEntry point to the smallest address
					if (retEntry == NULL) {
						retEntry = mapEntry;
					}
					else {
						if (retEntry->address < mapEntry->address) {
							retEntry = mapEntry;
						}
					}
				}
			}
		}
	}

	*withinRange = 0;
	return retEntry;

}
AOM_HMB void _delete_map(U32 mapIndex, HMB_MEMORY_MAP *memMap)
{
	HMB_MEMORY_MAP_ENTRY *mapEntry = NULL;

	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (mapIndex < REMAP_MAP_INDEX));

	mapEntry = &memMap->entry[mapIndex];
	memset((void *)mapEntry, 0x00, sizeof(HMB_MEMORY_MAP_ENTRY));

	memMap->freeEntry++;
}

AOM_HMB void __insert_map(U32 mapIndex, U32 type, U32 listIndex, U64 address, U64 length, HMB_MEMORY_MAP *memMap)
{
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (mapIndex < REMAP_MAP_INDEX));

	memMap->entry[mapIndex].type = type;		/* parasoft-suppress BD-PB-ARRAY "check by assert"*/
	memMap->entry[mapIndex].index = listIndex;
	memMap->entry[mapIndex].address = address;
	memMap->entry[mapIndex].length = length;
}

AOM_HMB void _insert_map(U32 type, U32 listIndex, U64 address, U64 length, HMB_MEMORY_MAP *memMap)
{
	HMB_MEMORY_MAP_ENTRY *mapEntry = NULL;
	U32 i;

	if (memMap->freeEntry <= 0) {
		return;
	}

	// remap is reserved at the last entry
	if (type == D2H_REMAP) {
		mapEntry = &memMap->entry[REMAP_MAP_INDEX - 1];
		if (mapEntry->type == D2H_EMPTY) {
			__insert_map(REMAP_MAP_INDEX - 1, type, listIndex, address, length, memMap);
			memMap->freeEntry--;
		}
	}
	else {
		// search a empty entry to insert
		for (i = 0; i < MAX_HMB_ENTEY_SUPPORT; i++) {
			mapEntry = &memMap->entry[i];
			if (mapEntry->type == D2H_EMPTY) {
				__insert_map(i, type, listIndex, address, length, memMap);
				memMap->freeEntry--;
				break;
			}
		}
	}

	return;
}

/*
* write memory map to D2H hardware
*/
void d2h_flush_map(HMB_MEMORY_MAP *memMap)
{
	//HMB_MEMORY_MAP *memMap = &gD2HMemMap;
	HMB_MEMORY_MAP_ENTRY *mapEntry = NULL;
	U32 i, j, index;
	U64 baseAddress, offset;

	// clear previous setting
	for (i = 0; i < MAX_HMB_ENTEY_SUPPORT; i++) {
		d2h_AES_PATH_init((U8)i, 0x0, 0x0, 0x0);
	}
	d2h_RMP_PATH_init(0x0, 0x0, 0x0);

	// write mem map to HW (zone1, zone2)
	index = 0;
	for (j = D2H_Z1; j <= D2H_Z2; j++) {
		baseAddress = d2h_get_start_address(j);
		offset = 0;
		for (i = 0; i < MAX_HMB_ENTEY_SUPPORT; i++) {
			mapEntry = &memMap->entry[i];
			if (mapEntry->type == j) {
				d2h_AES_PATH_init((U8)(index++), mapEntry->length, baseAddress + offset, mapEntry->address);
				offset += mapEntry->length;
			}
		}
	}

	// write mem map to HW (remap)
	mapEntry = &memMap->entry[REMAP_MAP_INDEX - 1];
	if (mapEntry->type == D2H_REMAP) {
		baseAddress = d2h_get_start_address(D2H_REMAP);
		d2h_RMP_PATH_init(mapEntry->length, baseAddress, mapEntry->address);
	}
}

/*
* memory allocation from a single entry in gFreeMemList
* A single entry in gFreeMemList may contained many block of free continuous memory separated by previous allocated memory
*
* entry1 in HMB_LIST
* ---------
* |              |
* |free1      |
* |              |
* |--------|
* |              |
* |Occupied |
* |--------|
* |              |
* |free2      |
* |--------|
*
* this function allocate only one block (the first block) of free memory at a time, and fill the allocated info into gD2HMemMap.
* If expected allcate length larger than a single free block length, the allocated size will be return to caller
* caller is responsiable to take care return length by either call this function again to retreve next free block, or to get memory from next entry
* The function return value should be the allocated length
* note: expected_length should be "desire size + protect bytes reserved for AES"
*/

AOM_HMB U64 __HMB_malloc(U32 listIndex, U32 type, U64 expected_length, HMB_LIST *freeMemList, HMB_MEMORY_MAP *memMap)
{
	HMB_LIST_ENTRY *listEntry = NULL;
	HMB_MEMORY_MAP_ENTRY *mapEntry = NULL;
	volatile U64 expectAddress, freePosition, allocLen;
	U32 alignUint, withinRange;

	// index overflow
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (listIndex <= freeMemList->size));

	alignUint = d2h_align_unit(type, D2H_HOST);

	// length not aligned
	if (d2h_host_length_align(type, expected_length) != 0) {
		return 0;
	}

	if (memMap->freeEntry <= 0) {
		return 0;
	}

	listEntry = &(freeMemList->entry[listIndex]);

	// try to allocate from the beginning position
	expectAddress = listEntry->startAddress;

	while (1) {
		// adjust expected address align to next 16 Bytes (D2H SPEC MODIFICATION SVN_3528)
		// fix me: host address alignment should associated with D2H_AES_Z1_HOSTADDR_ALIGN / D2H_AES_Z2_HOSTADDR_ALIGN
		if (expectAddress & 0xF) {
			expectAddress += (~expectAddress & 0xF) + 1;
		}
		if (expectAddress >= listEntry->endAddress) {
			return 0;
		}

		// find the largest continuous block
		mapEntry = _get_occupied_entry(listIndex, expectAddress, &withinRange, memMap);
		// threre is no occupied block behind expectAddress
		if (mapEntry == NULL) {
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (listEntry->endAddress >= D2H_HW_RESERVED_LENGTH));
			freePosition = listEntry->endAddress - D2H_HW_RESERVED_LENGTH;
		}
		else {
			// expectAddress located in an occupied block
			if (withinRange) {
				expectAddress = mapEntry->address + mapEntry->length;
				if (type == D2H_Z2) {
					//expectAddress += D2H_HW_RESERVED_LENGTH;
				}
				// reach block end
				if (expectAddress >= listEntry->endAddress) {
					return 0;
				}
				else {
					continue;
				}
			}
			else {
				// expectAddress is free to use until next occupied block begin
				M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (mapEntry->address >= D2H_HW_RESERVED_LENGTH));
				freePosition = mapEntry->address - D2H_HW_RESERVED_LENGTH;
			}
		}

		// align allocate length to alignUint
		if (expectAddress + expected_length <= freePosition) {
			allocLen = expected_length;
		}
		else {
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (freePosition >= expectAddress));
			allocLen = ((freePosition - expectAddress) / alignUint) * alignUint; /* parasoft-suppress BD-PB-ZERO "check by assert"*/
		}


		if (allocLen > 0) {
			// save allocate info to gD2HMemMap and return
			_insert_map(type, listIndex, expectAddress, allocLen, memMap);
			return allocLen;
		}
		else {
			if (mapEntry == NULL) {
				return 0;
			}
			expectAddress = mapEntry->address + mapEntry->length;
		}
	}

	// should never reach here
	return 0xFFFFFFFFFFFFFFFF;

}

AOM_HMB U32 _HMB_malloc_remap(U32 type, U64 expected_length, HMB_LIST *freeMemList, HMB_MEMORY_MAP *memMap)
{
	HMB_LIST_ENTRY *entry = NULL;
	HMB_MEMORY_MAP_ENTRY *mapEntry = NULL;
	U32 maxAllocLength;

	// for simpilicity sake, allocate from index 0 for remap
	entry = &freeMemList->entry[0];
	if (entry->freeBytes <= d2h_align_unit(type, D2H_HOST)) {
		return NULL;
	}

	if (entry->freeBytes > D2H_REMAP_MAX_SIZE) {
		maxAllocLength = D2H_REMAP_MAX_SIZE;
	}
	else {
		maxAllocLength = entry->freeBytes;
	}

	if (expected_length < maxAllocLength) {
		maxAllocLength = expected_length;
	}

	mapEntry = &memMap->entry[REMAP_MAP_INDEX - 1];
	mapEntry->type = D2H_REMAP;
	mapEntry->length = maxAllocLength;
	mapEntry->address = entry->startAddress;
	d2h_RMP_PATH_init(mapEntry->length, DUMMY_REMAP_ADDRESS, mapEntry->address);

	// return NULL if we cannot allocate expected length succesfully, but we still allocated best effort
	if (maxAllocLength != expected_length) {
		return NULL;
	}
	else {
		return d2h_get_start_address(type);
	}
}

AOM_HMB U32 _HMB_malloc_light(U32 type, U64 expected_length, HMB_LIST *freeMemList, HMB_MEMORY_MAP *memMap)
{
	HMB_LIST_ENTRY *listEntry = NULL;
	U32 listIndex = 0, allocLen = 0, alignUint = 0;

	alignUint = d2h_align_unit(type, D2H_HOST);

	while (expected_length > 0) {

		if (listIndex >= freeMemList->size) {
			return NULL;
		}

		listEntry = &(freeMemList->entry[listIndex]);
		allocLen = (listEntry->freeBytes / alignUint) * alignUint; /* parasoft-suppress BD-PB-ZERO "check by assert"*/
		if (expected_length < allocLen) {
			allocLen = expected_length;
			expected_length = 0;
		}
		else {
			expected_length -= allocLen;
		}
		M_UART(D2H_, "expect %L, alloc %x\n", expected_length, allocLen);
		_insert_map(type, listIndex, listEntry->startAddress, allocLen, memMap);
		listIndex++;
	}

	// write memory map to D2H hardware
	d2h_flush_map(memMap);
	return d2h_get_start_address(type);
}


AOM_HMB U32 _HMB_malloc(U32 type, U64 expected_length, HMB_LIST *freeMemList, HMB_MEMORY_MAP *memMap)
{
	U64 allocLen;
	U32 i = 0;

	i = 0;
	while (expected_length > 0) {
		allocLen = __HMB_malloc(i, type, expected_length, freeMemList, memMap);
		if (allocLen == 0) {
			i++;
		}
		else {
			expected_length = expected_length - allocLen;
		}

		if (i >= freeMemList->size) {
			break;
		}
		if (memMap->freeEntry <= 0) {
			break;
		}
	}

	// allocation failed
	if (expected_length > 0) {
		//fixme: delete allocated map entry?
		d2h_flush_map(memMap); // we still flush map though, become best effor size
		return d2h_get_start_address(type);
	}
	else {
		// write current setting to HW
		d2h_flush_map(memMap);
		return d2h_get_start_address(type);
	}

	return NULL;

}

U32 HMB_malloc(HMB_HANDLE *handle, U32 type, U64 expected_length)
{
	HMB_LIST *freeMemList = NULL;
	HMB_MEMORY_MAP *memMap = NULL;
	U64 hostLen;

	if (NULL == handle) {
		return 0;
	}

	freeMemList = handle->pFreeMemList;
	memMap = handle->pD2HMemMap;

	if (type == D2H_REMAP) {
		return _HMB_malloc_remap(type, expected_length, freeMemList, memMap);
	}

	if (memMap->freeEntry <= 0) {
		M_UART(D2H_, "[D2H] Out of map entry\n");
		return 0;
	}

	if (d2h_local_length_align(type, expected_length) != 0) {
		M_UART(D2H_, "[D2H] Length alignment error\n");
		return 0;
	}

	// 5011 AES module need extra 2 bytes digest information located rigth after each 32byte/512byte data
	hostLen = (expected_length / d2h_align_unit(type, D2H_DEVICE)) * d2h_align_unit(type, D2H_HOST);

#if HMB_LIGHT
	HMB_free(handle, D2H_Z1);
	HMB_free(handle, D2H_Z2);
	return _HMB_malloc_light(type, hostLen, freeMemList, memMap);
#else
	return _HMB_malloc(type, hostLen, freeMemList, memMap);
#endif

}

void HMB_free(HMB_HANDLE *handle, U32 type)
{
	HMB_MEMORY_MAP *memMap = NULL;
	HMB_MEMORY_MAP_ENTRY *mapEntry = NULL;
	U32 i;

	if (NULL == handle) {
		return;
	}

	memMap = handle->pD2HMemMap;

	if (type >= D2H_TYPE_MAX) {
		return;
	}

	for (i = 0; i < MAX_HMB_ENTEY_SUPPORT; i++) {
		mapEntry = &memMap->entry[i];
		if (mapEntry->type == type) {
			_delete_map(i, memMap);
		}
	}

	// write current setting to HW
	d2h_flush_map(memMap);
}

AOM_NRW_2 U64 _HMB_get_list_free_count(U32 listIndex, U32 type, HMB_LIST *freeMemList, HMB_MEMORY_MAP *memMap)
{
	HMB_LIST_ENTRY *listEntry = NULL;
	HMB_MEMORY_MAP_ENTRY *mapEntry = NULL;
	volatile U64 expectAddress, freePosition, freeCount = 0;
	U32 alignUintHost, alignUintDevice, withinRange;

	// index overflow
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (listIndex <= freeMemList->size));

	alignUintHost = d2h_align_unit(type, D2H_HOST);
	alignUintDevice = d2h_align_unit(type, D2H_DEVICE);

	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (alignUintHost != 0));

	listEntry = &(freeMemList->entry[listIndex]);

	// calculate from the beginning position
	expectAddress = listEntry->startAddress;

	while (1) {
		// adjust expected address align to next 16 Bytes (D2H SPEC MODIFICATION SVN_3528)
		if (expectAddress & 0xF) {
			expectAddress += (~expectAddress & 0xF) + 1;
		}
		if (expectAddress >= listEntry->endAddress) {
			return freeCount;
		}

		// find the largest continuous block
		mapEntry = _get_occupied_entry(listIndex, expectAddress, &withinRange, memMap);
		// threre is no occupied block behind expectAddress
		if (mapEntry == NULL) {
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (listEntry->endAddress >= D2H_HW_RESERVED_LENGTH));
			freePosition = listEntry->endAddress - D2H_HW_RESERVED_LENGTH;
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (freePosition >= expectAddress));
			freeCount += ((freePosition - expectAddress) / alignUintHost) * alignUintDevice; /* parasoft-suppress BD-PB-ZERO "check by assert"*/
			return freeCount;

		}
		else {
			// expectAddress located in an occupied block
			if (withinRange) {
				expectAddress = mapEntry->address + mapEntry->length;
				// reach block end
				if (expectAddress >= listEntry->endAddress) {
					return freeCount;
				}
			}
			else {
				// expectAddress is free to use until next occupied block begin
				M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (mapEntry->address >= D2H_HW_RESERVED_LENGTH));
				freePosition = mapEntry->address - D2H_HW_RESERVED_LENGTH;
				M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (freePosition >= expectAddress));
				freeCount += ((freePosition - expectAddress) / alignUintHost) * alignUintDevice; /* parasoft-suppress BD-PB-ZERO "check by assert"*/
				expectAddress = mapEntry->address + mapEntry->length;
			}
		}
	}

	// should never reach here
	return 0x0;

}

AOM_NRW_2 U32 HMB_local_max_length(U32 type)
{
	// if chip id is 5011
	if (type == D2H_Z1) {
		return D2H_Z1_MAX_SIZE;
	}
	else if (type == D2H_Z2) {
		return D2H_Z2_MAX_SIZE;
	}
	else if (type == D2H_REMAP) {
		return D2H_REMAP_MAX_SIZE;
	}
	else {
		return 0;
	}
}

/*
* return free memory count in bytes for device (FW)
*/
U64 HMB_get_free_count(HMB_HANDLE *handle, U32 type)
{
	HMB_LIST *freeMemList = NULL;
	HMB_LIST_ENTRY *listEntry = NULL;
	HMB_MEMORY_MAP *memMap = NULL;
	U64 freeCount = 0; // in bytes
	U32 i = 0;
	U32 maxSize;
	U32 listSize;

	if (NULL == handle) {
		return 0;
	}
	freeMemList = handle->pFreeMemList;
	listSize = freeMemList->size;
	if (type == D2H_EMPTY || type >= D2H_TYPE_MAX) {
		return 0;
	}

	if (type == D2H_REMAP) {
		// for simpilicity sake, always use index 0
		listEntry = &freeMemList->entry[0];
		if (listEntry->freeBytes > D2H_REMAP_MAX_SIZE) {
			return D2H_REMAP_MAX_SIZE;
		}
		return listEntry->freeBytes;
	}

	memMap = handle->pD2HMemMap;
	maxSize = HMB_local_max_length(type);

	if (listSize > MAX_HMB_ENTEY_SUPPORT) {
		listSize = MAX_HMB_ENTEY_SUPPORT;
	}

#if HMB_LIGHT
	for (i = 0; i < listSize; i++) {
		listEntry = &freeMemList->entry[i];
		freeCount += (listEntry->freeBytes / (U64)d2h_align_unit(type, D2H_HOST)) * (U64)d2h_align_unit(type, D2H_DEVICE);
		// uart_printf("\n freeCount %L freeBytes %L \n", freeCount, listEntry->freeBytes);
	}
#else
	for (i = 0; i < listSize; i++) {
		freeCount += _HMB_get_list_free_count(i, type, freeMemList, memMap);
	}
#endif

	if (freeCount > (U64)maxSize) {
		return (U64)maxSize;
	}

	return freeCount;
}

U64 HMB_get_allocate_size(HMB_HANDLE *handle, U32 type)
{
	HMB_MEMORY_MAP *memMap = NULL;
	HMB_MEMORY_MAP_ENTRY *mapEntry = NULL;
	U64 size = 0; // in bytes
	U32 i = 0, alignUintHost, alignUintDevice;

	if (handle == NULL) {
		return 0;
	}

	if (type == D2H_EMPTY || type >= D2H_TYPE_MAX) {
		return 0;
	}

	alignUintHost = d2h_align_unit(type, D2H_HOST);
	alignUintDevice = d2h_align_unit(type, D2H_DEVICE);

	memMap = handle->pD2HMemMap;
	for (i = 0; i < REMAP_MAP_INDEX; i++) {
		mapEntry = &memMap->entry[i];
		if (mapEntry->type == type) {
			size += (mapEntry->length / alignUintHost) * alignUintDevice; /* parasoft-suppress BD-PB-ZERO "check by assert"*/
		}
	}

	return size;
}

// Ava's current code base, all required device memory temporary located in global data
#if 0
U32 _d2h_setup(HMB_HANDLE *handle)
{
	BMU_CMD_SQ bmuSQ;
	BMU_CMD_CQ bmuWcq;
	U32 addr;

	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (handle != 0));

	// structure total over BMU_ALLOCATE_UNIT (4KB)
	if (sizeof(HMB_MEMORY_MAP) + sizeof(HMB_LIST) +
		sizeof(HMB_GLOBAL_DATA) >= BMU_ALLOCATE_UNIT) {
		// allocate more from BMU or shrink the HMB_LIST size supported
		return 0;
	}

	// allocated before, this is the second HMB command
	if (handle->pFreeMemList != NULL) {
		// fixme: should handle HMB disable here
		return 1;
	}

	// allocate BMU_ALLOCATE_UNIT (4KB) to store D2H necessary structure
	memset(&bmuSQ, 0, sizeof(BMU_CMD_SQ));
	bmuSQ.param.alloc.opcode = BMU_OPCODE_ALLOC_PB;           // 1RCQ map to 1-4KB PB
	bmuSQ.param.alloc.lb_id = NONUSER_CMD_LBID;
	bmuSQ.param.alloc.size = 1;
	bmuSQ.param.alloc.af = 0; // auto free
	bmuSQ.param.alloc.lca = 0;
	bmuSQ.param.alloc.ctag = 0;
	bmu_push_blocking_sq(&bmuSQ);

	if (bmu_pop_cq(&bmuWcq) == PASS) {
		if (bmuWcq.param.validate.result != BMU_RESP_STATUS_SUCCESS) {
			return 0;
		}
		addr = (U32)(DBUF_PB_RAM_ADDRESS + (bmuWcq.param.alloc.pb_addr * BMU_ALLOCATE_UNIT));

		handle->data = (HMB_GLOBAL_DATA *)addr;
		//M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, ((&handle->data->reservedBuffer[0] & 0x1F) != 0); // reservedBuffer must be 32 bytes alignment for DMAC
		handle->pFreeMemList = (HMB_LIST *)(addr + sizeof(HMB_GLOBAL_DATA));
		handle->pD2HMemMap = (HMB_MEMORY_MAP *)(addr + sizeof(HMB_GLOBAL_DATA) + sizeof(HMB_LIST));
		memcpy(&handle->data->bmuWcq, &bmuWcq, sizeof(BMU_CMD_CQ));
		return 1;
	}
	else {
		return 0;
	}

}

U32 _d2h_free(HMB_HANDLE *handle)
{
	BMU_CMD_CQ *bmuWcq;

	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (handle != 0));

	bmuWcq = &handle->data->bmuWcq;

	if (bmu_basic_free(bmuWcq, bmuWcq->param.alloc.lb_id,
			bmuWcq->param.alloc.lb_ofst, 1)) {
		uart_printf("_d2h_free Failed\n");
		return 0;
	}
	else {
		return 1;
	}
}
#endif

HMB_HANDLE *d2h_get_HMB_command(U32 ulHMDLEC, U64 ullHMDL, U32 ulMPS)
{
	U32 i;
	HMB_HANDLE *handle = NULL;
	P_HOST_GEOMETRY_PTR pHostPrtInfo = (P_HOST_GEOMETRY_PTR) gpHostPrtInfo;

	if (pHostPrtInfo == NULL) {
		return NULL;
	}

	handle = pHostPrtInfo->pHmbHandle;

	// Ava's current code base, all required device memory temporary located in global data
#if 0
	if (_d2h_setup(handle) == 0) {
		uart_printf("D2H BMU allocation failed\n");
		return NULL;
	}
#endif

	// skip NULL pointer checking from here and code flow after this line
	// zero gD2HMemMap
	memset((void *)handle->pD2HMemMap, 0x00, sizeof(HMB_MEMORY_MAP));

	// zero gFreeMemList
	memset((void *)handle->pFreeMemList, 0x00, sizeof(HMB_LIST));
	handle->pD2HMemMap->freeEntry = MAX_HMB_ENTEY_SUPPORT; // free entry should not take remap into account

	// zero global data
	memset((void *)handle->data, 0x00, sizeof(HMB_GLOBAL_DATA));

	//create HMB_LIST from NVMe HMB command
	d2h_create_HMB_list(ulHMDLEC, ullHMDL, ulMPS, handle->pFreeMemList);

	// print freeMemList for debug purpose
	for (i = 0; i < handle->pFreeMemList->size; i++) {
		M_UART(D2H_, "\n[%l] len = %L, addr = %L", i, handle->pFreeMemList->entry[i].freeBytes, handle->pFreeMemList->entry[i].startAddress);
	}

	return handle;
}

HMB_HANDLE *init_HMB(void)
{
	P_HOST_GEOMETRY_PTR pHostPrtInfo = (P_HOST_GEOMETRY_PTR) gpHostPrtInfo;

	if (pHostPrtInfo == NULL) {
		return NULL;
	}

	if (pHostPrtInfo->pHmbHandle == NULL) {
		return NULL;
	}

	if (pHostPrtInfo->pHmbHandle->pFreeMemList == NULL) {
		return NULL;
	}

	return pHostPrtInfo->pHmbHandle;
}
