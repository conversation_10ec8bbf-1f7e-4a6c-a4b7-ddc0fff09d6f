#ifndef _VUC_GETFLASHINFO_H_
#define _VUC_GETFLASHINFO_H_
#include "aom/aom_api.h"

/* vendor_get_flash_id */
#define VUC_GET_ALL_FLASH_ID 		 	(0)
#define VUC_GET_SPECIFIC_FLASH_ID		(1)
#define VUC_GET_PARAMETER_TABLE			(2)
#define	VUC_GET_FLASH_READ_RETRY_TABLE	(3)
#define	VUC_GET_UNIQUE_ID				(4)
#define	VUC_GET_BLOCK_NUMBER			(5)
#define	VUC_GET_SOFT_BIT_TABLE			(6)
#define VUC_ASSIGN_READ_RETRY_TABLE			(7)
#define VUC_GET_MAX_BAD_PER_PLANE		(8)

/* Get max bad block number option */
#define VUC_GET_MAX_BAD_OP_PERCENT			(3)
#define VUC_GET_MAX_BAD_OVER_PROVISION		(100 - VUC_GET_MAX_BAD_OP_PERCENT)
#define VUC_GET_MAX_BAD_NUMBER_PER_PLANE	(0)
#define VUC_GET_MAX_BAD_NUMBER_PER_DIE 		(1)
#define VUC_BYTE_TO_KB_LOG					(10)
#define VUC_KB_TO_MB_LOG					(10)

#define VUC_GET_TRIM_TABLE_SUBFEATURE		(0x00)
#define VUC_GET_DPS_TABLE_SUBFEATURE			(0x01)

#if (USB == HOST_MODE) && (!RDT_MODE_EN)
AOM_VUC void VUC_GetFlashID(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD);
#else /* (USB == HOST_MODE) */
AOM_VUC void VUC_GetFlashInfo(VUC_OPT_HCMD_PTR_t pCmd);
AOM_VUC void VUC_GetFlashID(VUC_OPT_HCMD_PTR_t pCmd);
#endif /* (USB == HOST_MODE) */
AOM_NRW_2 void VUC_GetTrimTable(VUC_OPT_HCMD_PTR_t pCmd);
AOM_VUC void VUCDownloadRetryTable(VUC_OPT_HCMD_PTR_t pCmd);
AOM_VUC void VUC_GetDPSTable(VUC_OPT_HCMD_PTR_t pCmd);
#if (USB == HOST_MODE) && (!RDT_MODE_EN)
AOM_VUC void VUCGetMaxBlockPerPlane(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD);
AOM_VUC void VUC_GetFlashStatus(U32 ulBufAddr, PUSBCURRCMD_t pCurrentCMD);
#endif /* (USB == HOST_MODE) */
#endif /* _VUC_GETFLASHINFO_H_ */
