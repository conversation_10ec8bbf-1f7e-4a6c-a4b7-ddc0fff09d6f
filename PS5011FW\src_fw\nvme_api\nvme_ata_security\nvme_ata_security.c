/****************************************************************************
*
*  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
*  All rights reserved
*
*  The content of this document is confidential and shall be applied
*  subject to the terms and conditions of the license agreement and
*  other applicable laws. Any unauthorized access, use or disclosure
*  of this document is strictly prohibited and may be punishable
*  under laws.
*
*  nvme_ata_security.c
*
*
*
****************************************************************************/

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "nvme_api/nvme/shr_hal_nvme_api.h"
#include "nvme_api/nvme_ata_security/nvme_ata_security.h"
#include "tcg/tcg_api.h"
#include "ftl/ftl_preread_api.h"
#include "ftl/ftl_nrw_api.h"
#include "ftl/ftl_nrw.h"

NVMeATASecurity_t gNVMeATASecurity;
/*
 * ---------------------------------------------------------------------------------------------------
 *   private prototypes
 * ---------------------------------------------------------------------------------------------------
 */
#if (NVME_ATA_SECURITY_SUPPORT && !BURNER_MODE_EN && !LPM3_LOADER)
AOM_NVME_ATA_SECURITY void static NVMeATASecurityAllocateBuf(void);
AOM_NVME_ATA_SECURITY void static NVMeATASecurityFreeBuf(void);
AOM_NVME_ATA_SECURITY U32 static NVMeATASecurityCheckPassword(NVME_ATA_SECURITY_PTR pNVMeATASecurity, U8 *pubPassword, U8 ubMSTRPW);
AOM_NVME_ATA_SECURITY void static NVMeATASecuritySetPassword(NVME_ATA_SECURITY_PTR pNVMeATASecurity, OPT_HCMD_PTR pCmd);
AOM_NVME_ATA_SECURITY U8 static NVMeATASecurityUnlock(NVME_ATA_SECURITY_PTR pNVMeATASecurity, OPT_HCMD_PTR pCmd);
AOM_NVME_ATA_SECURITY void static NVMeATASecurityErasePrepare(NVME_ATA_SECURITY_PTR pNVMeATASecurity);
AOM_NVME_ATA_SECURITY U8 static NVMeATASecurityErase(NVME_ATA_SECURITY_PTR pNVMeATASecurity, OPT_HCMD_PTR pCmd);
AOM_NVME_ATA_SECURITY void static NVMeATASecurityFreezeLock(NVME_ATA_SECURITY_PTR pNVMeATASecurity);
AOM_NVME_ATA_SECURITY U8 static NVMeATASecurityDisPassword(NVME_ATA_SECURITY_PTR pNVMeATASecurity, OPT_HCMD_PTR pCmd);
AOM_NVME_ATA_SECURITY U8 static NVMeATASecuritySendHandle(NVME_ATA_SECURITY_PTR pNVMeATASecurity, OPT_HCMD_PTR pCmd);
#endif /* (NVME_ATA_SECURITY_SUPPORT && !BURNER_MODE_EN && !LPM3_LOADER) */
/*
 * ---------------------------------------------------------------------------------------------------
 *   private
 * ---------------------------------------------------------------------------------------------------
 */

#if (NVME_ATA_SECURITY_SUPPORT && !BURNER_MODE_EN && !LPM3_LOADER)
void NVMeATASecurityAllocateBuf(void)
{
	M_UART(NVME_ATA_SECURITY_, "[NVMe ATA Security] AllocateBuf\n");
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (FALSE == gNVMeATASecurity.btBufExist));

	switch (gNVMeATASecurity.NVMeATASecurityAllocateBufState) {
	case NVME_ATA_SECURITY_ALLOCATE_BUF_STATE_INIT:
		gNVMeATASecurity.NVMeATASecurityAllocateBufState = NVME_ATA_SECURITY_ALLOCATE_BUF_STATE_STOP_PREREAD;
	// no break;
	case NVME_ATA_SECURITY_ALLOCATE_BUF_STATE_STOP_PREREAD:
		if (TRUE == PREREAD_EN) {
			if (TRUE == gubEnterFTLTask) {
				FTLSetPreReadStop();
				gPreReadInfo.btPreReadEn = FALSE;
			}
		}
		gNVMeATASecurity.NVMeATASecurityAllocateBufState = NVME_ATA_SECURITY_ALLOCATE_BUF_STATE_ADJUST_LOW_LIMIT;
	// no break;
	case NVME_ATA_SECURITY_ALLOCATE_BUF_STATE_ADJUST_LOW_LIMIT: {
			BufLBLowLimit_t LBLowLimit = {0};
			BMUCmdResult_t BMUCmdResult;

			if (TRUE == PREREAD_EN) {
				if ((STATE_PREREAD_IDLE != gPreReadInfo.ubMainState) && (TRUE == gubEnterFTLTask)) {
					break;
				}
			}

			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (FALSE == gDLMC.uwOthers.B.btAdjustTableLowLimit) && (FALSE == gDLMC.uwOthers.B.btAdjustDataLowLimit));
			gNVMeATASecurity.btRLBLowLimitAdjust = TRUE;
			BufCalculateLowLimit(&LBLowLimit, BUF_NVME_ATA_SECUIRTY_LOW_LIMIT_BITMAP);

			// Adjust to higher first, then adjust to lower
			BMUAPICmdUpdateLL(BMU_CMD_NEED_CQ, LB_ID_FW, LBLowLimit.uwFW, &BMUCmdResult);
			BMUAPICmdUpdateLL(BMU_CMD_NEED_CQ, LB_ID_READ, LBLowLimit.uwRead, &BMUCmdResult);
			gNVMeATASecurity.NVMeATASecurityAllocateBufState = NVME_ATA_SECURITY_ALLOCATE_BUF_STATE_WAIT_ADJUST_LOW_LIMIT;
		}
	// no break;
	case NVME_ATA_SECURITY_ALLOCATE_BUF_STATE_WAIT_ADJUST_LOW_LIMIT:
		if (M_BMU_CHECK_REPLENISH()) {
			break;
		}
		gNVMeATASecurity.NVMeATASecurityAllocateBufState = NVME_ATA_SECURITY_ALLOCATE_BUF_STATE_ALLOCATE;
	// no break;
	case NVME_ATA_SECURITY_ALLOCATE_BUF_STATE_ALLOCATE:
		BufferAllocateFWLBPBLink(FWLB_NVME_ATA_SECURITY_BIT, ALLOCATE_NO_USE_CTAG, ALLOCATE_NO_USE_LCA);
		gNVMeATASecurity.NVMeATASecurityAllocateBufState = NVME_ATA_SECURITY_ALLOCATE_BUF_STATE_SETUP_BUFFER;
	// no break;
	case NVME_ATA_SECURITY_ALLOCATE_BUF_STATE_SETUP_BUFFER:
		gNVMeATASecurity.ulSHABufAddr = M_LB_TO_ADDR(LB_ID_FW, (gFWLBMgr.Type[FWLB_NVME_ATA_SECURITY].uwLBOffset & LB_LENGTH_MASK));
		gNVMeATASecurity.btBufExist = TRUE;
		gNVMeATASecurity.NVMeATASecurityAllocateBufState = NVME_ATA_SECURITY_ALLOCATE_BUF_STATE_DONE;
	// no break;
	case NVME_ATA_SECURITY_ALLOCATE_BUF_STATE_DONE:
		gNVMeATASecurity.NVMeATASecurityAllocateBufState = NVME_ATA_SECURITY_ALLOCATE_BUF_STATE_INIT;
		break;
	default:
		break;
	}
}

void NVMeATASecurityFreeBuf(void)
{
	M_UART(NVME_ATA_SECURITY_, "[NVMe ATA Security] FreeBuf\n");
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, TRUE == (gNVMeATASecurity.btBufExist));

	switch (gNVMeATASecurity.NVMeATASecurityFreeBufState) {
	case NVME_ATA_SECURITY_FREE_BUF_STATE_INIT:
		gNVMeATASecurity.NVMeATASecurityFreeBufState = NVME_ATA_SECURITY_FREE_BUF_STATE_FREE;
	// no break;
	case NVME_ATA_SECURITY_FREE_BUF_STATE_FREE:
		BufferFreeFWLBPBLink(FWLB_NVME_ATA_SECURITY_BIT);
		gNVMeATASecurity.btBufExist = FALSE;
		gNVMeATASecurity.NVMeATASecurityFreeBufState = NVME_ATA_SECURITY_FREE_BUF_STATE_ADJUST_LOW_LIMIT;
	// no break;
	case NVME_ATA_SECURITY_FREE_BUF_STATE_ADJUST_LOW_LIMIT: {
			BufLBLowLimit_t LBLowLimit = {0};
			BMUCmdResult_t BMUCmdResult;

			gNVMeATASecurity.btRLBLowLimitAdjust = FALSE;
			BufCalculateLowLimit(&LBLowLimit, BUF_NVME_ATA_SECUIRTY_LOW_LIMIT_BITMAP);

			// Adjust to higher first, then adjust to lower
			BMUAPICmdUpdateLL(BMU_CMD_NEED_CQ, LB_ID_READ, LBLowLimit.uwRead, &BMUCmdResult);
			BMUAPICmdUpdateLL(BMU_CMD_NEED_CQ, LB_ID_FW, LBLowLimit.uwFW, &BMUCmdResult);

			if (TRUE == PREREAD_EN) {
				if (TRUE == gubEnterFTLTask) {
					gPreReadInfo.btPreReadEn = TRUE;
				}
			}
		}
		gNVMeATASecurity.NVMeATASecurityFreeBufState = NVME_ATA_SECURITY_FREE_BUF_STATE_DONE;
	//no break;
	case NVME_ATA_SECURITY_FREE_BUF_STATE_DONE:
		gNVMeATASecurity.NVMeATASecurityFreeBufState = NVME_ATA_SECURITY_FREE_BUF_STATE_INIT;
		break;
	default:
		break;
	}
}

U32 NVMeATASecurityCheckPassword(NVME_ATA_SECURITY_PTR pNVMeATASecurity, U8 *pubPassword, U8 ubMSTRPW)
{
	M_UART(NVME_ATA_SECURITY_, "[NVMe ATA Security] Compare Password who: %d\n", ubMSTRPW);
	NVMeATASecurityComparePasswordResult_t ulCompareResult = NVME_ATA_SECURITY_NOT_COMPARE;

	memcpy((void *)gNVMeATASecurity.ulSHABufAddr, (void *)pubPassword, NVME_ATA_SECURITY_MAX_PASSWORD_LENGTH);
	SecuritySHAExecute(gNVMeATASecurity.ulSHABufAddr, NVME_ATA_SECURITY_MAX_PASSWORD_LENGTH, gNVMeATASecurity.ulSHABufAddr, SECURITY_SHA_FIRST_ROUND_BIT | SECURITY_SHA_FINAL_ROUND_BIT, SECURITY_MODE_SELECT_SHA_256, SECURITY_SHA_AXI_PAC32_DIS);

	if (TRUE == ubMSTRPW) {
		if (TRUE == pNVMeATASecurity->Config.btMasterPasswordValid) {
			ulCompareResult = ((0 == memcmp((void *)gNVMeATASecurity.ulSHABufAddr, (void *)&pNVMeATASecurity->aubMasterPasswordDigest[0], NVME_ATA_SECURITY_MAX_PASSWORD_LENGTH)) ? NVME_ATA_SECURITY_MASTER_PASSWORD_CORRECT : NVME_ATA_SECURITY_MASTER_PASSWORD_INCORRECT);
		}
		else {
			ulCompareResult = NVME_ATA_SECURITY_MASTER_PASSWORD_INCORRECT;
		}
	}
	else {
		if (TRUE == pNVMeATASecurity->Status.btEn) {
			ulCompareResult = ((0 == memcmp((void *)gNVMeATASecurity.ulSHABufAddr,  (void *)&pNVMeATASecurity->aubUserPasswordDigest[0], NVME_ATA_SECURITY_MAX_PASSWORD_LENGTH)) ? NVME_ATA_SECURITY_USER_PASSWORD_CORRECT : NVME_ATA_SECURITY_USER_PASSWORD_INCORRECT);
		}
		else {
			ulCompareResult = NVME_ATA_SECURITY_NOT_ENABLE_ABORT_CMD;
		}
	}
	M_UART(NVME_ATA_SECURITY_, "[NVMe ATA Security] CompareResult: %d\n", ulCompareResult);
	return ulCompareResult;
}

void NVMeATASecuritySetPassword(NVME_ATA_SECURITY_PTR pNVMeATASecurity, OPT_HCMD_PTR pCmd)
{
	M_UART(NVME_ATA_SECURITY_, "[NVMe ATA Security] SetPassword\n");
	NVMeATASecurityParameterSetPassword_t *pSetPassword;

	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (TRUE == gNVMeATASecurity.btBufExist));

	pSetPassword = (NVMeATASecurityParameterSetPassword_t *)(pCmd->cur_phyMemAddr);

	memcpy((void *)gNVMeATASecurity.ulSHABufAddr, (void *)&pSetPassword->aubPassword[0], NVME_ATA_SECURITY_MAX_PASSWORD_LENGTH);
	SecuritySHAExecute(gNVMeATASecurity.ulSHABufAddr, NVME_ATA_SECURITY_MAX_PASSWORD_LENGTH, gNVMeATASecurity.ulSHABufAddr, SECURITY_SHA_FIRST_ROUND_BIT | SECURITY_SHA_FINAL_ROUND_BIT, SECURITY_MODE_SELECT_SHA_256, SECURITY_SHA_AXI_PAC32_DIS);
	if (TRUE == pSetPassword->btMSTRPW) {
		memcpy((void *)&pNVMeATASecurity->aubMasterPasswordDigest[0], (void *)gNVMeATASecurity.ulSHABufAddr, SECURITY_SHA_256_OUTPUT_LENGTH);
		U16 uwMasterID = (((U16)pSetPassword->ubMasterIDHigh << 8) | (pSetPassword->ubMasterIDLow));
		if ((NVME_ATA_SECURITY_MASTER_PASSWORD_ID_NOT_SUPPORT_DEFAULT_1 == uwMasterID)
			|| (NVME_ATA_SECURITY_MASTER_PASSWORD_ID_NOT_SUPPORT_DEFAULT_2 == uwMasterID)) {
			// do nothing
		}
		else {
			pNVMeATASecurity->uwMasterPasswordID = uwMasterID;
		}
		pNVMeATASecurity->Config.btMasterPasswordValid = TRUE;
	}
	else {
		memcpy((void *)&pNVMeATASecurity->aubUserPasswordDigest[0], (void *)gNVMeATASecurity.ulSHABufAddr, SECURITY_SHA_256_OUTPUT_LENGTH);
		pNVMeATASecurity->Status.btEn = TRUE;
		pNVMeATASecurity->Config.btMasterPasswordCapability = (TRUE == pSetPassword->btMAXLVL) ? NVME_ATA_SECURITY_MASTER_PASSWORD_CAPABILITY_MAX : NVME_ATA_SECURITY_MASTER_PASSWORD_CAPABILITY_HIGH;
	}
}

U8 NVMeATASecurityUnlock(NVME_ATA_SECURITY_PTR pNVMeATASecurity, OPT_HCMD_PTR pCmd)
{
	M_UART(NVME_ATA_SECURITY_, "[NVMe ATA Security] Unlock\n");
	if (TRUE == pNVMeATASecurity->Status.btExpired) {
		return FALSE;
	}

	U8 ubStatus = TRUE;
	U32 ulCompareResult;
	NVMeATASecurityParameterUnlock_t *pUnlock;

	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (TRUE == gNVMeATASecurity.btBufExist));

	pUnlock = (NVMeATASecurityParameterUnlock_t *)(pCmd->cur_phyMemAddr);
	ulCompareResult = NVMeATASecurityCheckPassword(pNVMeATASecurity, &pUnlock->aubPassword[0], pUnlock->btMSTRPW);

	switch (ulCompareResult) {
	case NVME_ATA_SECURITY_MASTER_PASSWORD_CORRECT:
		if (TRUE == pNVMeATASecurity->Status.btEn) {
			if (NVME_ATA_SECURITY_MASTER_PASSWORD_CAPABILITY_MAX == pNVMeATASecurity->Config.btMasterPasswordCapability) {
				if (TRUE == pNVMeATASecurity->Status.btLocked) { // only in SEC4 can decrement
					pNVMeATASecurity->ubPasswordAttemptCnt--;
				}
				ubStatus = FALSE;
			}
			else if (TRUE == pNVMeATASecurity->Status.btLocked) {
				pNVMeATASecurity->Status.btLocked = FALSE;
				M_UART(NVME_ATA_SECURITY_, "[NVMe ATA Security] Unlock\n");
				M_NVME_ATA_SECURITY_CLEAR_LOCK();
			}
		}
		break;
	case NVME_ATA_SECURITY_USER_PASSWORD_CORRECT:
		if (TRUE == pNVMeATASecurity->Status.btLocked) {
			pNVMeATASecurity->Status.btLocked = FALSE;
			M_UART(NVME_ATA_SECURITY_, "[NVMe ATA Security] Unlock\n");
			M_NVME_ATA_SECURITY_CLEAR_LOCK();
		}
		break;
	case NVME_ATA_SECURITY_MASTER_PASSWORD_INCORRECT:
	case NVME_ATA_SECURITY_USER_PASSWORD_INCORRECT:
		ubStatus = FALSE;
		if (TRUE == pNVMeATASecurity->Status.btLocked) { // only in SEC4 can decrement
			pNVMeATASecurity->ubPasswordAttemptCnt--;
		}
		break;
	case NVME_ATA_SECURITY_NOT_ENABLE_ABORT_CMD:
		ubStatus = FALSE;
		break;
	default:
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
		break;
	}

	if (0 == pNVMeATASecurity->ubPasswordAttemptCnt) {
		pNVMeATASecurity->Status.btExpired = TRUE;
	}

	return ubStatus;
}

void NVMeATASecurityErasePrepare(NVME_ATA_SECURITY_PTR pNVMeATASecurity)
{
	M_UART(NVME_ATA_SECURITY_, "[NVMe ATA Security] Erase Prepare\n");
	U64 uoRCmdCnt, uoWCmdCnt;
	NLOG_GET_RCMD_CNT(uoRCmdCnt);
	NLOG_GET_WCMD_CNT(uoWCmdCnt);
	pNVMeATASecurity->uoRecordIOCnt = uoRCmdCnt + uoWCmdCnt;
}

U8 NVMeATASecurityErase(NVME_ATA_SECURITY_PTR pNVMeATASecurity, OPT_HCMD_PTR pCmd)
{
	M_UART(NVME_ATA_SECURITY_, "[NVMe ATA Security] Erase\n");
	U64 uoRCmdCnt, uoWCmdCnt;
	NLOG_GET_RCMD_CNT(uoRCmdCnt);
	NLOG_GET_WCMD_CNT(uoWCmdCnt);

	if ((TRUE == pNVMeATASecurity->Status.btExpired)
		|| ((uoRCmdCnt + uoWCmdCnt) != pNVMeATASecurity->uoRecordIOCnt)
		|| (FALSE == pNVMeATASecurity->Config.btLastCmdIsErasePrepare)) {
		return FALSE;
	}

	U8 ubStatus = TRUE;
	U8 ubEraseMode = 0;
	U32 ulCompareResult;
	NVMeATASecurityParameterErase_t *pErase;

	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (TRUE == gNVMeATASecurity.btBufExist));

	pErase = (NVMeATASecurityParameterErase_t *)(pCmd->cur_phyMemAddr);
	ulCompareResult = NVMeATASecurityCheckPassword(pNVMeATASecurity, &pErase->aubPassword[0], pErase->btMSTRPW);

	switch (ulCompareResult) {
	case NVME_ATA_SECURITY_MASTER_PASSWORD_CORRECT:
	case NVME_ATA_SECURITY_USER_PASSWORD_CORRECT:
		if ((FALSE == pNVMeATASecurity->Status.btEnhanceEraseSupport) && (TRUE == pErase->btEN_ER)) {
			ubStatus = FALSE;
		}
		else {
			if ((TRUE == pNVMeATASecurity->Status.btEnhanceEraseSupport) && (TRUE == pErase->btEN_ER)) {
				ubEraseMode = SES_USER_DATA_ERASE;
			}
			else { // Normal Mode
				ubEraseMode = SES_NO_ERASE;
			}
		}
		break;
	case NVME_ATA_SECURITY_MASTER_PASSWORD_INCORRECT:
	case NVME_ATA_SECURITY_USER_PASSWORD_INCORRECT:
	case NVME_ATA_SECURITY_NOT_ENABLE_ABORT_CMD:
		ubStatus = FALSE;
		break;
	default:
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
		break;
	}

	if (TRUE == ubStatus) {
		NRWFormatNVMSetup(BIT_MASK(MAX_NS_NUMBER), TRUE, ubEraseMode);
		gNVMeATASecurity.btNeedErase = TRUE;
	}

	return ubStatus;
}

void NVMeATASecurityFreezeLock(NVME_ATA_SECURITY_PTR pNVMeATASecurity)
{
	M_UART(NVME_ATA_SECURITY_, "[NVMe ATA Security] Frozen\n");
	pNVMeATASecurity->Status.btFrozen = TRUE;
}

U8 NVMeATASecurityDisPassword(NVME_ATA_SECURITY_PTR pNVMeATASecurity, OPT_HCMD_PTR pCmd)
{
	M_UART(NVME_ATA_SECURITY_, "[NVMe ATA Security] DisPassword\n");
	U8 ubStatus = TRUE;
	U32 ulCompareResult;

	NVMeATASecurityParameterDisPassword_t *pDisablePassword;

	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (TRUE == gNVMeATASecurity.btBufExist));

	pDisablePassword = (NVMeATASecurityParameterDisPassword_t *)(pCmd->cur_phyMemAddr);
	ulCompareResult = NVMeATASecurityCheckPassword(pNVMeATASecurity, &pDisablePassword->aubPassword[0], pDisablePassword->btMSTRPW);

	switch (ulCompareResult) {
	case NVME_ATA_SECURITY_MASTER_PASSWORD_CORRECT:
		if (TRUE == pNVMeATASecurity->Status.btEn) {
			if (NVME_ATA_SECURITY_MASTER_PASSWORD_CAPABILITY_MAX == pNVMeATASecurity->Config.btMasterPasswordCapability) {
				ubStatus = FALSE;
			}
		}
		break;
	case NVME_ATA_SECURITY_USER_PASSWORD_CORRECT:
		break;
	case NVME_ATA_SECURITY_MASTER_PASSWORD_INCORRECT:
	case NVME_ATA_SECURITY_USER_PASSWORD_INCORRECT:
	case NVME_ATA_SECURITY_NOT_ENABLE_ABORT_CMD:
		ubStatus = FALSE;
		break;
	default:
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
		break;
	}

	if (TRUE == ubStatus) {
		pNVMeATASecurity->Status.btEn = FALSE;
		pNVMeATASecurity->Config.btMasterPasswordCapability = NVME_ATA_SECURITY_MASTER_PASSWORD_CAPABILITY_HIGH;
	}

	return ubStatus;
}

U8 NVMeATASecuritySendHandle(NVME_ATA_SECURITY_PTR pNVMeATASecurity, OPT_HCMD_PTR pCmd)
{
	M_UART(NVME_ATA_SECURITY_, "[NVMe ATA Security] Send CMD handle\n");
	U8 ubStatus = TRUE;
	switch (pCmd->nvme_sqcmd.adm.security_rw.spsp) {
	case NVME_ATA_SECURITY_CMD_SPSP_SET_PASSWORD:
		NVMeATASecuritySetPassword(pNVMeATASecurity, pCmd);
		break;
	case NVME_ATA_SECURITY_CMD_SPSP_GET_UNLOCK:
		ubStatus = NVMeATASecurityUnlock(pNVMeATASecurity, pCmd);
		break;
	case NVME_ATA_SECURITY_CMD_SPSP_GET_ERASE_PREPARE:
		NVMeATASecurityErasePrepare(pNVMeATASecurity);
		gNVMeATASecurity.btNeedSaveHostTable = FALSE;
		break;
	case NVME_ATA_SECURITY_CMD_SPSP_GET_ERASE:
		ubStatus = NVMeATASecurityErase(pNVMeATASecurity, pCmd);
		break;
	case NVME_ATA_SECURITY_CMD_SPSP_GET_FREEZE_LOCK:
		NVMeATASecurityFreezeLock(pNVMeATASecurity);
		break;
	case NVME_ATA_SECURITY_CMD_SPSP_GET_DISABLE_PASSWORD:
		ubStatus = NVMeATASecurityDisPassword(pNVMeATASecurity, pCmd);
		break;
	default:
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
		break;
	}
	return ubStatus;
}
#endif /* (NVME_ATA_SECURITY_SUPPORT && !BURNER_MODE_EN && !LPM3_LOADER) */
/*
 * ---------------------------------------------------------------------------------------------------
 *   public
 * ---------------------------------------------------------------------------------------------------
 */
void NVMeATASecurityInit(U8 ubMode)
{
#if (!E21_TODO)
	M_UART(NVME_ATA_SECURITY_, "[NVMe ATA Security] Init Mode: %d\n", ubMode);
	NVME_ATA_SECURITY_PTR pNVMeATASecurity = gpHostPrtInfo->pNVMeATASecurity;
	if ((NVME_ATA_SECURITY_INIT_MODE_PREFORMAT == ubMode) || (NVME_ATA_SECURITY_INIT_MODE_DLMC == ubMode)) {
		pNVMeATASecurity->Status.ubAll = 0;
		pNVMeATASecurity->Config.ubAll = 0;
		pNVMeATASecurity->Status.btSupport = NVME_ATA_SECURITY_SUPPORT;
		pNVMeATASecurity->Status.btEnhanceEraseSupport = NVME_ATA_ENHANCE_ERASE_SUPPORT;
		pNVMeATASecurity->ubPasswordAttemptCnt = NVME_ATA_SECURITY_PASSWORD_MAX_ATTEMPT_CNT;
		pNVMeATASecurity->uwMasterPasswordID = NVME_ATA_SECURITY_DEFAULT_MASTER_PASSWORD_ID;
		memset((void *)pNVMeATASecurity->aubMasterPasswordDigest, 0x00, SECURITY_SHA_256_OUTPUT_LENGTH);
		memset((void *)pNVMeATASecurity->aubUserPasswordDigest, 0x00, SECURITY_SHA_256_OUTPUT_LENGTH);
	}
#if (!BURNER_MODE_EN && !LPM3_LOADER)
	else if ((NVME_ATA_SECURITY_INIT_MODE_POWER_CYCLE == ubMode) || (NVME_ATA_SECURITY_INIT_MODE_HRST == ubMode)) {
		pNVMeATASecurity->Status.btExpired = FALSE;
		pNVMeATASecurity->Status.btFrozen = FALSE;
		pNVMeATASecurity->Status.btLocked = FALSE;

		if (TRUE == pNVMeATASecurity->Status.btEn) {
			pNVMeATASecurity->Status.btLocked = TRUE;
			M_NVME_ATA_SECURITY_SET_LOCK();
		}

		pNVMeATASecurity->Config.btLastCmdIsErasePrepare = FALSE;
		pNVMeATASecurity->Config.btHRST = FALSE;
		pNVMeATASecurity->ubPasswordAttemptCnt = NVME_ATA_SECURITY_PASSWORD_MAX_ATTEMPT_CNT;

		gNVMeATASecurity.btBufExist = FALSE;
		gNVMeATASecurity.btNeedErase = FALSE;
		gNVMeATASecurity.btNeedSaveHostTable = FALSE;
		gNVMeATASecurity.btRLBLowLimitAdjust = FALSE;
		gNVMeATASecurity.NVMeATASecurityAllocateBufState = NVME_ATA_SECURITY_ALLOCATE_BUF_STATE_INIT;
		gNVMeATASecurity.NVMeATASecurityFreeBufState = NVME_ATA_SECURITY_FREE_BUF_STATE_INIT;
		gNVMeATASecurity.NVMeATASecuritySendState = NVME_ATA_SECURITY_SEND_STATE_INIT;
	}
	else if (NVME_ATA_SECURITY_INIT_MODE_LPM3 == ubMode) {
		if (TRUE == pNVMeATASecurity->Status.btLocked) {
			M_NVME_ATA_SECURITY_SET_LOCK();
		}
	}
#endif /* (!BURNER_MODE_EN && !LPM3_LOADER) */
#endif /*(!E21_TODO)*/
}

#if (NVME_ATA_SECURITY_SUPPORT && !BURNER_MODE_EN && !LPM3_LOADER)
U8 NVMeATASecurityReceiveCheckStatus(OPT_HCMD_PTR pCmd)
{
	M_UART(NVME_ATA_SECURITY_, "[NVMe ATA Security] Recv check status\n");
	U8 ubStatusCorrect = FALSE;

	if (NVME_ATA_SECURITY_CMD_SPSP_GET_STATUS != pCmd->nvme_sqcmd.adm.security_rw.spsp) {
		nvme_admi_cmd_sc_msg (pCmd, GENERIC_CMD, INVALID_FIELD, 0xFF, 0xFF);
	}
	else if ((pCmd->nvme_sqcmd.adm.security_rw.tl < NVME_ATA_SECURITY_GET_STATUS_TRANSFER_LENGTH) || (pCmd->nvme_sqcmd.adm.security_rw.tl > SIZE_4KB)) {
		nvme_admi_cmd_sc_msg (pCmd, GENERIC_CMD, INVALID_FIELD, 0xFF, 0xFF);
	}
	else {
		ubStatusCorrect = TRUE;
	}
	return ubStatusCorrect;
}

U8 NVMeATASecuritySendCheckStatus(OPT_HCMD_PTR pCmd)
{
	M_UART(NVME_ATA_SECURITY_, "[NVMe ATA Security] Send check status\n");
	NVME_ATA_SECURITY_PTR pNVMeATASecurity = gpHostPrtInfo->pNVMeATASecurity;

	/* check write protect, follow E19 */
	if (0 != gpVTDBUF->WriteProtect.ubAll) {
		if ((NVME_ATA_SECURITY_CMD_SPSP_SET_PASSWORD == pCmd->nvme_sqcmd.adm.security_rw.spsp) ||
			(NVME_ATA_SECURITY_CMD_SPSP_GET_ERASE_PREPARE == pCmd->nvme_sqcmd.adm.security_rw.spsp) ||
			(NVME_ATA_SECURITY_CMD_SPSP_GET_ERASE == pCmd->nvme_sqcmd.adm.security_rw.spsp) ||
			(NVME_ATA_SECURITY_CMD_SPSP_GET_DISABLE_PASSWORD == pCmd->nvme_sqcmd.adm.security_rw.spsp)) {
			nvme_admi_cmd_sc_msg (pCmd, INTEGRITY_ERR, ACCESS_DENIED, 0xFF, 0xFF);
			return FALSE;
		}
	}

	/* check lock */
	if (TRUE == pNVMeATASecurity->Status.btLocked) {	/* Locked */
		if ((NVME_ATA_SECURITY_CMD_SPSP_SET_PASSWORD == pCmd->nvme_sqcmd.adm.security_rw.spsp)
			|| (NVME_ATA_SECURITY_CMD_SPSP_GET_FREEZE_LOCK == pCmd->nvme_sqcmd.adm.security_rw.spsp)
			|| (NVME_ATA_SECURITY_CMD_SPSP_GET_DISABLE_PASSWORD == pCmd->nvme_sqcmd.adm.security_rw.spsp)) {
			nvme_admi_cmd_sc_msg (pCmd, INTEGRITY_ERR, ACCESS_DENIED, 0xFF, 0xFF);
			return FALSE;
		}
	}
	else if (TRUE == pNVMeATASecurity->Status.btFrozen) { /* frozen */
		if ((NVME_ATA_SECURITY_CMD_SPSP_SET_PASSWORD == pCmd->nvme_sqcmd.adm.security_rw.spsp)
			|| (NVME_ATA_SECURITY_CMD_SPSP_GET_UNLOCK == pCmd->nvme_sqcmd.adm.security_rw.spsp)
			|| (NVME_ATA_SECURITY_CMD_SPSP_GET_ERASE_PREPARE == pCmd->nvme_sqcmd.adm.security_rw.spsp)
			|| (NVME_ATA_SECURITY_CMD_SPSP_GET_ERASE == pCmd->nvme_sqcmd.adm.security_rw.spsp)
			|| (NVME_ATA_SECURITY_CMD_SPSP_GET_DISABLE_PASSWORD == pCmd->nvme_sqcmd.adm.security_rw.spsp)) {
			nvme_admi_cmd_sc_msg (pCmd, INTEGRITY_ERR, ACCESS_DENIED, 0xFF, 0xFF);
			return FALSE;
		}
	}

	/* check length */
	if ((NVME_ATA_SECURITY_CMD_SPSP_SET_PASSWORD == pCmd->nvme_sqcmd.adm.security_rw.spsp)
		|| (NVME_ATA_SECURITY_CMD_SPSP_GET_UNLOCK == pCmd->nvme_sqcmd.adm.security_rw.spsp)
		|| (NVME_ATA_SECURITY_CMD_SPSP_GET_ERASE == pCmd->nvme_sqcmd.adm.security_rw.spsp)
		|| (NVME_ATA_SECURITY_CMD_SPSP_GET_DISABLE_PASSWORD == pCmd->nvme_sqcmd.adm.security_rw.spsp)) {
		if ((pCmd->nvme_sqcmd.adm.security_rw.tl < NVME_ATA_SECURITY_SEND_PAYLOAD_EXIST_TRANSFER_LENGTH) || (pCmd->nvme_sqcmd.adm.security_rw.tl > SIZE_4KB)) {
			nvme_admi_cmd_sc_msg (pCmd, GENERIC_CMD, INVALID_FIELD, 0xFF, 0xFF);
			return FALSE;
		}
	}
	else if ((NVME_ATA_SECURITY_CMD_SPSP_GET_ERASE_PREPARE == pCmd->nvme_sqcmd.adm.security_rw.spsp)
		|| (NVME_ATA_SECURITY_CMD_SPSP_GET_FREEZE_LOCK == pCmd->nvme_sqcmd.adm.security_rw.spsp)) {
		if (NVME_ATA_SECURITY_SEND_NO_PAYLOAD_TRANSFER_LENGTH != pCmd->nvme_sqcmd.adm.security_rw.tl) {
			nvme_admi_cmd_sc_msg (pCmd, GENERIC_CMD, INVALID_FIELD, 0xFF, 0xFF);
			return FALSE;
		}
	}
	else {
		nvme_admi_cmd_sc_msg (pCmd, GENERIC_CMD, INVALID_FIELD, 0xFF, 0xFF);
		return FALSE;
	}
	return TRUE;
}

void NVMeATASecurityGetStatus(OPT_HCMD_PTR pCmd)
{
	M_UART(NVME_ATA_SECURITY_, "[NVMe ATA Security] Receive get status\n");
	NVME_ATA_SECURITY_PTR pNVMeATASecurity = gpHostPrtInfo->pNVMeATASecurity;
	NVMeATASecurityStatus_t NVMeATASecurityStatus = {0};
	U16 uwEnhanceEraseTime = 0;
	uwEnhanceEraseTime = (U16)((guoDiskInfo.ulDiskSizeInMB / FTL_128GB_TO_MB) * NVME_ATA_SECURITY_ENHANCE_ERASE_TIME_128GB_IN_SECOND);

	NVMeATASecurityStatus.ubParameterListLength = NVME_ATA_SECURITY_GET_STATUS_PARAMETER_LIST_LENGTH;
	NVMeATASecurityStatus.ubNormalEraseTimeHigh = (NVME_ATA_SECURITY_NORMAL_ERASE_TIME_IN_SECOND >> 8) & BIT_MASK(8);
	NVMeATASecurityStatus.ubNormalEraseTimeLow = NVME_ATA_SECURITY_NORMAL_ERASE_TIME_IN_SECOND & BIT_MASK(8);
	NVMeATASecurityStatus.ubEnhanceEraseTimeHigh = (uwEnhanceEraseTime >> 8) & BIT_MASK(8);
	NVMeATASecurityStatus.ubEnhanceEraseTimeLow = uwEnhanceEraseTime & BIT_MASK(8);
	NVMeATASecurityStatus.uwMasterPasswordIDHigh = (pNVMeATASecurity->uwMasterPasswordID >> 8) & BIT_MASK(8);
	NVMeATASecurityStatus.uwMasterPasswordIDLow = pNVMeATASecurity->uwMasterPasswordID & BIT_MASK(8);
	NVMeATASecurityStatus.btMaxSet = (TRUE == pNVMeATASecurity->Status.btEn) ? pNVMeATASecurity->Config.btMasterPasswordCapability : FALSE;
	NVMeATASecurityStatus.Status.ubAll = pNVMeATASecurity->Status.ubAll;

	memset((void *)pCmd->cur_phyMemAddr, 0x00, pCmd->nvme_sqcmd.adm.security_rw.tl);
	memcpy((void *)pCmd->cur_phyMemAddr, (void *)&NVMeATASecurityStatus, sizeof(NVMeATASecurityStatus_t));
	pCmd->state = FW_PROCESS_DONE;
}

void NVMeATASecuritySend(OPT_HCMD_PTR pCmd)
{
	M_UART(NVME_ATA_SECURITY_, "[NVMe ATA Security] Send\n");
	NVME_ATA_SECURITY_PTR pNVMeATASecurity = gpHostPrtInfo->pNVMeATASecurity;
	U32 ulTransferLength = pCmd->nvme_sqcmd.adm.security_rw.tl;
	U8 ubHandleStatus = TRUE, ubContinue;

	do {
		ubContinue = FALSE;
		switch (gNVMeATASecurity.NVMeATASecuritySendState) {
		case NVME_ATA_SECURITY_SEND_STATE_INIT:
			gNVMeATASecurity.btNeedSaveHostTable = TRUE;
			gNVMeATASecurity.NVMeATASecuritySendState = NVME_ATA_SECURITY_SEND_STATE_ALLOCATE_BUF;
		//no break;
		case NVME_ATA_SECURITY_SEND_STATE_ALLOCATE_BUF:
			NVMeATASecurityAllocateBuf();
			if (NVME_ATA_SECURITY_ALLOCATE_BUF_STATE_INIT == gNVMeATASecurity.NVMeATASecurityAllocateBufState) {
				gNVMeATASecurity.NVMeATASecuritySendState = NVME_ATA_SECURITY_SEND_STATE_HANDLE;
			}
			else {
				break;
			}
		//no break;
		case NVME_ATA_SECURITY_SEND_STATE_HANDLE:
			ubHandleStatus = NVMeATASecuritySendHandle(pNVMeATASecurity, pCmd);
			if (TRUE == gNVMeATASecurity.btNeedErase) {
				gNVMeATASecurity.NVMeATASecuritySendState = NVME_ATA_SECURITY_SEND_STATE_ERASE_HANDLE;
			}
			else {
				gNVMeATASecurity.NVMeATASecuritySendState = NVME_ATA_SECURITY_SEND_STATE_FREE_BUF;
				ubContinue = TRUE;
				break;
			}
		//no break;
		case NVME_ATA_SECURITY_SEND_STATE_ERASE_HANDLE:
			NRWFormatNVM(FALSE);
			if (FORMAT_NVM_INIT == gNRWCmdState.FormatNVM.ubState) {
				pNVMeATASecurity->Status.btEn = FALSE;
				pNVMeATASecurity->Status.btLocked = FALSE;
				pNVMeATASecurity->Config.btMasterPasswordCapability = NVME_ATA_SECURITY_MASTER_PASSWORD_CAPABILITY_HIGH;
				gNVMeATASecurity.NVMeATASecuritySendState = NVME_ATA_SECURITY_SEND_STATE_FREE_BUF;
				gNVMeATASecurity.btNeedErase = FALSE;
				M_UART(NVME_ATA_SECURITY_, "[NVMe ATA Security] Erase Done\n");
				M_NVME_ATA_SECURITY_CLEAR_LOCK();
			}
			else {
				break;
			}
		//no break;
		case NVME_ATA_SECURITY_SEND_STATE_FREE_BUF:
			NVMeATASecurityFreeBuf();
			if (NVME_ATA_SECURITY_FREE_BUF_STATE_INIT == gNVMeATASecurity.NVMeATASecurityFreeBufState) {
				gNVMeATASecurity.NVMeATASecuritySendState = NVME_ATA_SECURITY_SEND_STATE_DONE;
			}
			else {
				break;
			}
		//no break;
		case NVME_ATA_SECURITY_SEND_STATE_DONE:
			break;
		default:
			break;
		}
	} while (TRUE == ubContinue);


	if (NVME_ATA_SECURITY_SEND_STATE_DONE == gNVMeATASecurity.NVMeATASecuritySendState) {
		if (FALSE == ubHandleStatus) {
			nvme_admi_cmd_sc_msg (pCmd, INTEGRITY_ERR, ACCESS_DENIED, 0xFF, 0xFF);
		}
		gNVMeATASecurity.NVMeATASecuritySendState = NVME_ATA_SECURITY_SEND_STATE_INIT;
		if (0 == ulTransferLength) {
			pCmd->state = PROCESS_ALL_DONE;
		}
		else {
			pCmd->state = FW_PROCESS_DONE;
		}
	}
}

U8 NVMeATASecurityAbortCmd(OPT_HCMD_PTR pCmd)
{
	M_UART(NVME_ATA_SECURITY_, "[NVMe ATA Security] Check Cmd Abort, ADM:%d, OPCode: %b\n", pCmd->hcmd.info.ubADM, pCmd->hcmd.info.ubOPCode);
	NVME_ATA_SECURITY_PTR pNVMeATASecurity = gpHostPrtInfo->pNVMeATASecurity;
	U8 ubIsAbort = FALSE;
	// SEC4
	if ((INITDONE == pCmd->state) && (TRUE == pNVMeATASecurity->Status.btLocked)) {
		if (pCmd->hcmd.info.ubADM) {
			switch (pCmd->hcmd.info.ubOPCode) {
			/*
			case DELETE_SQ: 		// 0x00
			case CREATE_SQ: 		// 0x01
			case GET_LOG:   		// 0x02
			case DELETE_CQ:			// 0x04
			case CREATE_CQ:			// 0x05
			case IDENTIFY:			// 0x06
			case ABORT:				// 0x08
			case SET_FEATURE:		// 0x09
			case GET_FEATURE:		// 0x0A
			case ASYNC_REQ:			// 0x0C
			case FW_COMMIT:			// 0x10
			case FW_DL:				// 0x11
			case DEV_SELF_TEST:		// 0x14
			case NS_MGMT:			// 0x0D
			case NS_ATT:			// 0x15
			case KEEP_ALIVE:		// 0x18
			case DIR_SEND:			// 0x19
			case DIR_RECEIVE:		// 0x1A
			case OVER_FABRICS:		// 0x7F
			case FORMAT_NVME:		// 0x80
			case SECRUITY_SEND: 	// 0x81
			case SECURITY_RECEIVE: 	// 0x82
				break;
			*/
			case SANITIZE:			// 0x84
				ubIsAbort = TRUE;
				break;
			default:
				break;
			}
		}
		else { // for IO command will all abort
			ubIsAbort = TRUE;
		}
	}
	// SEC2, SEC6
	else if ((INITDONE == pCmd->state) && (TRUE == pNVMeATASecurity->Status.btFrozen)) {
		if (pCmd->hcmd.info.ubADM) {
			switch (pCmd->hcmd.info.ubOPCode) {
			/*
			case DELETE_SQ: 		// 0x00
			case CREATE_SQ: 		// 0x01
			case GET_LOG:   		// 0x02
			case DELETE_CQ:			// 0x04
			case CREATE_CQ:			// 0x05
			case IDENTIFY:			// 0x06
			case ABORT:				// 0x08
			case SET_FEATURE:		// 0x09
			case GET_FEATURE:		// 0x0A
			case ASYNC_REQ:			// 0x0C
			case FW_COMMIT:			// 0x10
			case FW_DL:				// 0x11
			case DEV_SELF_TEST:		// 0x14
			case KEEP_ALIVE:		// 0x18
			case DIR_SEND:			// 0x19
			case DIR_RECEIVE:		// 0x1A
			case OVER_FABRICS:		// 0x7F
			case SECRUITY_SEND: 	// 0x81
			case SECURITY_RECEIVE: 	// 0x82
			case SANITIZE:			// 0x84
			case NS_MGMT:			// 0x0D
			case NS_ATT:			// 0x15
				break;
			*/
			case FORMAT_NVME:		// 0x80
				ubIsAbort = TRUE;
				break;
			default:
				break;
			}
		}
		else {	// for IO command will all not abort
		}
	}
	// SEC1, SEC3, SEC5
	else {
	}

	if (ubIsAbort == TRUE) {
		nvme_admi_cmd_sc_msg (pCmd, INTEGRITY_ERR, ACCESS_DENIED, 0xFF, 0xFF);
	}
	return ubIsAbort;
}
#endif /* (NVME_ATA_SECURITY_SUPPORT && !BURNER_MODE_EN && !LPM3_LOADER) */
