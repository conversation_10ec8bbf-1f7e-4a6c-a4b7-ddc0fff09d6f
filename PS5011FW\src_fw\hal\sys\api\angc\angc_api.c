#include "angc_api.h"

void TSInitBegin(void)
{
#if PS5017_EN
	//Enable CHOP
	M_ANGC_ENABLE_CHOP();

	//Enable INTCK
	M_ANGC_ENABLE_INTCLK();

	M_ANGC_ENABLE_DEC(); //[bc_m]20200910
#endif /* PS5017_EN */
	//Enable TS
	M_ANGC_ENABLE_TS();

	//Release Reset
	M_ANGC_RELEASE_RESET_TS();
}

void TSInit(void)
{
	//Polling RDY
	while (!M_ANGC_CHK_TCODE_RDY());
}

void OSC1Init(void)
{
	// Set for OSC1 98.5%
	M_ANGC_SET_OSC1_CFG(0x12);// 2'b010010
}

void OSC2Init(void)
{
	// Set for OSC2 98.5%
	M_ANGC_SET_OSC2_CFG(0x01);// 2'b000001
}


