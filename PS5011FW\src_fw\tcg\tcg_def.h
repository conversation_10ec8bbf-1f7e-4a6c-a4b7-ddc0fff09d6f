#ifndef _TCG_DEF_H_
#define _TCG_DEF_H_
#include "tcg_conf.h"

#define TCG_PREFORMAT_BUFFER_SIZE (2+ OPAL_VT_FLASH_CNT+ OPAL_SP_TABLE_FLASH_CNT) //Variables Manager + DRBG Buffer + 3 TcgVT + 3 AdminTable + 9 LockingTable 
#define TCG_PREFORMAT_DRBG_BUFFER_OFFSET (1)
#define TCG_PREFORMAT_FLASH_INDEX_BUFFER_OFFSET (2)

#define TCG_WRITE_PROTECT_BUFFER_SIZE (2+ OPAL_VT_FLASH_CNT+ OPAL_SP_TABLE_FLASH_CNT) //Variables Manager + DRBG Buffer + 3 TcgVT + 3 AdminTable + 9 LockingTable 
#define TCG_WRITE_PROTECT_DRBG_BUFFER_OFFSET (1)
#define TCG_WRITE_PROTECT_FLASH_INDEX_BUFFER_OFFSET (2)

//Because sector0 ubIPB_TCG_SSC SSCMode only has two bit, we need to change info block structure if we run out of four TCG SSCmode
#define TCG_SSC_OPAL			(0)
#define TCG_SSC_PYRITE_1		(1)
#define TCG_SSC_PYRITE_2		(2)
#define TCG_SSC_RSV				(3)

#define TCG_SP_TABLE_SYNC_BIT_MAX_IDX (15)
#define TCG_TIMEOUT_RECORDING_TIME   (2 * MILLISECOND_PER_SECOND)  // 2 second
#define TCG_DRIVELOG_NO_PAYLOAD      (0)

#define TCG_MSID_DEFAULT_PIN {'p', 'h', 'i', 's', 'o', 'n'}
#define TCG_MSID_DEFAULT_PIN_LENGTH (6)
#define TCG_PIN_MAX_LENGTH (32)
#define TCG_NRW_BUFFER_NUM	(2) // SATA only
#define TCG_VARIABLES_MANAGER_LB_OFFSET (0)
#define TCG_BUFFER_MANAGER_LB_OFFSET (1)
#define TCG_VT_LB_OFFSET (2)
#define TCG_PAYLOAD_BUFFER_NUM	(4)
#define TCG_GET_COM_ID_COM_ID			(0x0)
#define TCG_LEVEL_0_DISCOVERY_COM_ID	(0x01)
#define TCG_TPER_RESET_COM_ID			(0x04)
#define TCG_BLOCK_SID_COM_ID			(0x05)
#define TCG_TABLE_TABLE_COLUMN_NUM		(15)
#define TCG_SP_INFO_TABLE_COLUMN_NUM		(7)
#define TCG_SP_TEMPLATE_TABLE_COLUMN_NUM		(4)
#define TCG_METHOD_TABLE_COLUMN_NUM			(4)
#define TCG_ACE_TABLE_COLUMN_NUM			(5)
#define TCG_AUTHORITY_TABLE_COLUMN_NUM		(19)
#define TCG_CPIN_TABLE_COLUMN_NUM	(8)
#define TCG_TPER_INFO_TABLE_COLUMN_NUM	(9)
#define TCG_TEMPLATE_TABLE_COLUMN_NUM	(5)
#define TCG_SP_TABLE_COLUMN_NUM	(8)
#define TCG_DATA_REMOVAL_MECHANISM_TABLE_COLUMN_NUM (2)
#define TCG_SECRET_PROTECT_TABLE_COLUMN_NUM	(4)
#define TCG_LOCKING_INFO_TABLE_COLUMN_NUM	(11)
#define TCG_LOCKING_INFO_TABLE_COLUMN_NUM_SINGLE_USER_MODE	(13)
#define TCG_LOCKING_TABLE_COLUMN_NUM (20)
#define TCG_MBR_CONTROL_TABLE_COLUMN_NUM (4)
#define TCG_MBR_TABLE_COLUMN_NUM (1)
#define TCG_KEY_AES_256_TABLE_COLUMN_NUM (5)
#define TCG_DATASTORE_TABLE_COLUMN_NUM (1)
#define TCG_OBJECT_TABLE_WRITE_GRANULARITY	(0)
#define TCG_OBJECT_TABLE_READ_GRANULARITY	(0)
#define TCG_TPER_INFO_TABLE_PROTOCOL_VERSION		(1)
#define TCG_SECRET_PROTECT_TABLE_PROTECT_MECHANISM 	(1)
#define TCG_END_SESSION_SECURITY_RECEIVE_PAYLOAD_LENGTH	(4)
#define TCG_START_TRANSACTION_SECURITY_RECEIVE_PAYLOAD_LENGTH	(2)
#define TCG_END_TRANSACTION_SECURITY_RECEIVE_PAYLOAD_LENGTH		(2)
#define TCG_SYNC_SESSION_SECURITY_RECEIVE_PAYLOAD_LENGTH		(37)
#define TCG_SIMPLE_FILL_BUFFER_SECURITY_RECEIVE_PAYLOAD_LENGTH (8)
#define TCG_AUTHENTICATE_SECURITY_RECEIVE_PAYLOAD_LENGTH (8)
#define TCG_PROPERTY_SECURITY_RECEIVE_PAYLOAD_LENGTH (300)
#define TCG_PROPERTY_WITH_PARAMETER_SECURITY_RECEIVE_PAYLOAD_LENGTH (436)
#define TCG_MAX_NAME_SIZE (32)
#define TCG_MAX_AUTORITY_IN_ACE_ELEMENT (20)
#define TCG_MAX_AVAILABLE_COLUMN_NUM_IN_ACE_TABLE_ELEMENT	(11)
#define TCG_DIGEST_SIZE_OF_SHA_512	(64) //(512/8)
#define TCG_SECURITY_KEY_SIZE	(SIZE_64B)
#define TCG_MAX_RESET_TYPE_NUM	(2)
#define TCG_GUDID_SIZE			(12)
#define TCG_KEY_WRAP_PLAINTEXT_SIZE 	(32)
#define TCG_KEY_WRAP_IV_SIZE 			(8)
#define TCG_KEY_WRAP_CIPHERTEXT_SIZE 	(40)
#define TCG_DEK_SIZE 					(64)
#define TCG_EDEK_SIZE 					((TCG_DEK_SIZE / TCG_KEY_WRAP_PLAINTEXT_SIZE) * TCG_KEY_WRAP_CIPHERTEXT_SIZE)
#define TCG_KEK_SIZE 					(32)
#define TCG_EKEK_SIZE 					((TCG_KEK_SIZE / TCG_KEY_WRAP_PLAINTEXT_SIZE) * TCG_KEY_WRAP_CIPHERTEXT_SIZE)
#define TCG_EPASSWORD_SIZE 				(TCG_KEY_WRAP_CIPHERTEXT_SIZE)
#define TCG_INITIAL_VALUE_SIZE_OF_KEK	(16)
#define TCG_MAX_HOST_CHALLENGE_LENGTH	(64)
#define TCG_MAX_ACL_NUM_IN_ACL_LIST	(4)
#define TCG_MAX_SET_PARAMETER_RESET_TYPE_NUM	(2)
#define TCG_MAX_SET_PARAMETER_OBJECT_TABLE_PARAMETER_COLUMN_NUM	(15)
#define TCG_SECURITY_SRC_BUF_SIZE	(128)
#define TCG_SECURITY_DST_BUF_SIZE	(128)
#define TCG_INTEGRITY_CHECK_LOAD   (0)
#define TCG_INTEGRITY_CHECK_SAVE   (1)
#define TCG_DONT_CARE_LOCK_ON_RESET_BMP 	(1)
#define TCG_CARE_LOCK_ON_RESET_BMP 			(0)
#define TCG_WRONG_PIN						(0)
#define TCG_CORRECT_PIN						(1)
#define TCG_DONT_CARE_EKEK_IDX				(0xFF)
#define TCG_KEY_DERIVATION_FUNCTION_INITIAL_COUNTER_VALUE (1)
#define TCG_PBKDF2_ITERATION_NUM 	(1000)
#define TCG_SET_BUFFER_HEADER_GERNERAL_CASE 			(0)
#define TCG_SET_BUFFER_HEADER_NON_GERNERAL_FAIL_CASE 	(BIT0)
#define TCG_SET_BUFFER_HEADER_ZERO_SESSION_ID_CASE 		(BIT1)
#define TCG_SET_BUFFER_HEADER_ABORT_SESSION 			(BIT2)

#define TCG_VARIABLE_TABLE_RESERVED_SIZE	(1744)

#define TCG_NO_INTEGRITY_CHECK_SIZE									(SIZE_64B)
#define TCG_NO_INTEGRITY_CHECK_RESERVED_SIZE						(24)
#define TCG_TABLE_TAIL_RESERVED_SIZE 								(8)
#define TCG_VARIABLE_TABLE_NONVOLATILE_SIZE							(SIZE_256B)
#define TCG_VARIABLE_TABLE_NONVOLATILE_RESERVED_SIZE				(56)
#define TCG_VARIABLE_TABLE_NONVOLATILE_VARIABLE_SIZE				((TCG_VARIABLE_TABLE_NONVOLATILE_SIZE - TCG_NO_INTEGRITY_CHECK_SIZE) / 2)
#define TCG_ADMIN_PARTITAL_1_SECURITY_VERSION_INFO_RESERVED_SIZE	(5)
#define TCG_4B_ALIGNMENT_3B_RESERVED								(3)

#define TCG_ADMIN_PARTITAL_1_RESERVED_SIZE 	(832)
#define TCG_ADMIN_PARTITAL_2_RESERVED_SIZE 	(1796)
#define TCG_ADMIN_PARTITAL_3_RESERVED_SIZE 	(2272)
#define TCG_LOCKING_PART_1_RESERVED_SIZE 	(4032)
#define TCG_LOCKING_PART_2_RESERVED_SIZE 	(2840)
#define TCG_LOCKING_PART_3_RESERVED_SIZE 	(1056)

#define TCG_PBKDF2_SALT_SIZE	(32)
#define TCG_RESERVED_SIZE_FOR_TRIM_RANGE_ALIGN_32	(32)
#define TCG_MAX_DEK_CHANGED_TRIM_RANGE_NUM (2 * TCG_MAX_LOCKING_SP_LOCKING_TABLE_LENGTH + 1)
#define TCG_MAX_RANGE_CHANGED_TRIM_RANGE_NUM (2 * TCG_MAX_LOCKING_SP_LOCKING_TABLE_LENGTH)
#define TCG_PBKDF2_OUTPUT_SZIE	(32)
#define TCG_MAX_EKEK_NUM (TCG_MAX_LOCKING_SP_C_PIN_TABLE_LENGTH + 1)			// 1 for SID EKEK
#define TCG_MAX_WRAPPED_PASSWORD_NUM (TCG_MAX_LOCKING_SP_C_PIN_TABLE_LENGTH + 1 + 1 + 1)	// 1 for SID, 1 for Admin1 in AdminSP(Opal), 1 for Psid 
#define TCG_ACE_TABLE_ELEMENT_PER_4K	(36)
#define TCG_PBKDF2_OUTPUT_LENGTH		(32)
#define TCG_PBKDF2_OUTPUT_DWORD_LENGTH		(8)
#define TCG_KEY_WRAP_RESULT_OFFSET			(8)

#define OPAL_SP_TABLE_FLASH_CNT		(12)
#define OPAL_VT_FLASH_CNT			(3)
#define OPAL_TOTAL_TABLE_FLASH_CNT		(2 * OPAL_SP_TABLE_FLASH_CNT + OPAL_VT_FLASH_CNT)	//1 origin + 1 spare

#define OPAL_MBR_TABLE_SIZE			((TRUE == TCG_EN)? (0x8000000):(0)) //128MB
#define OPAL_DATASTORE_SIZE			((TRUE == TCG_EN)? (0xA00000):(0)) //10M

#define OPAL_REVERT_MBR		(0)
#define OPAL_REVERT_DATASTORE	(1)
#define TCG_REVERTSP_KEEP_DATA_PARAMETER (0x060000)

#define OPAL_WRITE_LIFECYCLE				(0)
#define OPAL_READ_LIFECYCLE				(1)
#define OPAL_READ_CONDITIONAL_WRITE_LIFECYCLE		(2)

#define OPAL_TRIM_MODE_ALL_USER_DATA			(1)
#define OPAL_TRIM_MODE_ALL_USER_DATA_BUT_GLOBAL_RANGE (2)
#define OPAL_TRIM_MODE_DEK_CHANGED	(0)


#define OPAL_SYNC_SP_TABLE_SYNC_SPARE		(0)
#define OPAL_SYNC_SP_TABLE_RECOVER_ORIGIN	(1)

#define OPAL_NULL_TARGET_ADDR	(0)
#define OPAL_FORCE_SAVE_NULL_TARGET_ADDR	(1)

#define OPAL_DO_NOT_KEEP_GLOBAL_RANGE_KEY (FALSE)

#define TCG_SET_RANGE_AND_KEY (TRUE)
#define TCG_SET_RANGE_ONLY    (FALSE)
#define OPAL_RANGE		(TRUE)
#define OPAL_NOT_RANGE	(FALSE)
#define OPAL_DONTCARE	(0xff)

#define TCG_PAYLOAD_BUFFER_SIZE	(0x4000) // 16KB

#define TCG_TPER_COMID 			(0x000007FE)

#define TCG_LOCKING_RANGE_ALIGNMENT_REQUIRED	(TRUE)
#define TCG_OPAL_ALIGNMENT_GRANULARITY  (8)
#define TCG_OPAL_LOWEST_ALIGNED_LBA     (0)

#define TCG_LOCKING_INFO_POLICY (RangeLengthUserSettingPolicy)
#define TCG_INVALID_TABLE_INDEX (0xFFFFFFFF)


#define M_TCG_INVERSE_LONG(x)         ((((U8)(x))<<24) | (((U8)((x)>>8))<<16) | (((U8)((x)>>16))<<8) | ((U8)((x)>>24)))
#define M_TCG_INVERSE_WORD(x)         ((((U8)(x))<<8) | ((U8)((x)>>8)))

#define TCG_COMPACKET_HEADER_LENGTH           (20)
#define TCG_PACKET_HEADER_LENGTH              (24)
#define TCG_SUBPACKET_HEADER_LENGTH           (12)
#define TCG_PAYLOAD_HEADER_LENGTH        (56) // TCG_COMPACKET_HEADER_LENGTH + TCG_PACKET_HEADER_LENGTH + TCG_SUBPACKET_HEADER_LENGTH
#define TCG_PAYLOAD_START_OFFSET    (TCG_PAYLOAD_HEADER_LENGTH) // Buffer Payload start address offset

#define TCG_MAX_TOKEN_LENGTH            (512)

#define TCG_OPAL_AES_KEY_NUM				(16)
#define TCG_OPAL_LOCKING_SP_ADMIN_NUM		(4)
#define TCG_OPAL_LOCKING_SP_USER_NUM		(TCG_OPAL_AES_KEY_NUM)
#define TCG_OPAL_LOCKING_SP_DATASTORE_NUM	(TCG_OPAL_LOCKING_SP_USER_NUM)
#define TCG_PYRITE_AES_KEY_NUM				(1)
#define TCG_PYRITE_LOCKING_SP_ADMIN_NUM		(1)
#define TCG_PYRITE_LOCKING_SP_USER_NUM		(2)
#define TCG_PYRITE_LOCKING_SP_DATASTORE_NUM	(1)

#define TCG_MAX_AES_KEY_NUM					(TCG_OPAL_AES_KEY_NUM)
#define TCG_MAX_LOCKING_SP_ADMIN_NUM		(TCG_OPAL_LOCKING_SP_ADMIN_NUM)
#define TCG_MAX_LOCKING_SP_USER_NUM			(TCG_OPAL_LOCKING_SP_USER_NUM)
#define TCG_MAX_LOCKING_SP_DATASTORE_NUM	(TCG_OPAL_LOCKING_SP_DATASTORE_NUM)


#define TCG_PASSWORD_TRY_LIMIT       (3)
#define TCG_SESSION_TIMEOUT_NO_LIMIT 	(0)
#define TCG_SESSION_TIMEOUT_TIME     	(TCG_SESSION_TIMEOUT_NO_LIMIT)

#define TCG_ADMINSP_TABLE_BYTE_SIZE     (0x10000)
#define TCG_LOCKINGSP_TABLE_BYTE_SIZE   (0x20000)
#define TCG_TABLE_SECTOR_CNT            ((TCG_ADMINSP_TABLE_BYTE_SIZE + TCG_LOCKINGSP_TABLE_BYTE_SIZE)/512)

#define TCG_READ_WRITE_CROSSING_RANGE_EN (FALSE)

#define TCG_INIT_CPIN_SID_INDICATOR			(0)
#define TCG_TPER_REVERT_CPIN_SID_BEHAVIOR	(0)

#define TCG_MBR_TABLE_SIZE							(0x8000000)
#define TCG_MBR_MANDATORY_WRITE_GRANULARITY_VALUE	(1)
#define TCG_MBR_RECOMMENDED_ACCESS_GRANULARITY		(0x1000)

#define TCG_DATASTORE_TABLE_SIZE_ALL					(0x00A00000)
#define TCG_DATASTORE_TABLE_SIZE_ALIGN					(0x1000)
#define TCG_DATASTORE_MANDATORY_WRITE_GRANULARITY_VALUE	(1)
#define TCG_DATASTORE_RECOMMENDED_ACCESS_GRANULARITY	(0x1000)

#define TCG_LEVEL_0_DISCOVERY_HEADER_PARAMETER_LENGTH	(44)
#define TCG_LEVEL_0_DISCOVERY_HEADER_LENGTH				(48)
#define TCG_LEVEL_0_DISCOVERY_TPER_FEATURE_LENGTH		(16)
#define TCG_LEVEL_0_DISCOVERY_LOCKING_FEATURE_LENGTH	(16)

#define TCG_OPAL_LEVEL_0_DISCOVERY_GEOMETRY_REPORTING_FEATURE_LENGTH 	(32)
#define TCG_PYRITE_2_LEVEL_0_DISCOVERY_REMOVAL_MECHANISM_FEATURE_LENGTH	(36)
#define TCG_LEVEL_0_DISCOVERY_SSC_FEATURE_LENGTH						(20)
#define TCG_LEVEL_0_DISCOVERY_SINGLE_USER_MODE_FEATURE_LENGTH       	(16)
#define TCG_LEVEL_0_DISCOVERY_ADDITIONAL_DATASTORE_FEATURE_LENGTH       (16)
#define TCG_LEVEL_0_DISCOVERY_BLOCK_SID_FEATURE_LENGTH        			(16)

#define TCG_OPAL_ADMIN_SP_TABLE_TABLE_LENGTH		(11)
#define TCG_PYRITE_1_ADMIN_SP_TABLE_TABLE_LENGTH	(11)
#define TCG_PYRITE_2_ADMIN_SP_TABLE_TABLE_LENGTH	(12)
#define TCG_MAX_ADMIN_SP_TABLE_TABLE_LENGTH			(TCG_PYRITE_2_ADMIN_SP_TABLE_TABLE_LENGTH)

#define TCG_OPAL_LOCKING_SP_TABLE_TABLE_ADDITIONAL_DATASTORE_LENGTH (TCG_OPAL_LOCKING_SP_USER_NUM - 1)

#define TCG_PYRITE_LOCKING_SP_TABLE_TABLE_LENGTH 	(13)
#define TCG_OPAL_LOCKING_SP_TABLE_TABLE_LENGTH  	(15 + TCG_OPAL_LOCKING_SP_TABLE_TABLE_ADDITIONAL_DATASTORE_LENGTH)
#define TCG_MAX_LOCKING_SP_TABLE_TABLE_LENGTH		(TCG_OPAL_LOCKING_SP_TABLE_TABLE_LENGTH)

#define TCG_ADMIN_SP_SP_INFO_TABLE_LENGTH     (1)
#define TCG_LOCKING_SP_SP_INFO_TABLE_LENGTH   (1)

#define TCG_ADMIN_SP_SP_TEMPLATE_TABLE_LENGTH     (2)
#define TCG_LOCKING_SP_SP_TEMPLATE_TABLE_LENGTH   (2)

#define TCG_ADMIN_SP_METHOD_TABLE_LENGTH      (8)

#define TCG_OPAL_LOCKING_SP_METHOD_TABLE_ADDITIONAL_DATASTORE_LENGTH  (2)

#define TCG_PYRITE_LOCKING_SP_METHOD_TABLE_LENGTH 	(7)
#define TCG_OPAL_LOCKING_SP_METHOD_TABLE_LENGTH		(8 + TCG_OPAL_LOCKING_SP_METHOD_TABLE_ADDITIONAL_DATASTORE_LENGTH)
#define TCG_MAX_LOCKING_SP_METHOD_TABLE_LENGTH		(TCG_OPAL_LOCKING_SP_METHOD_TABLE_LENGTH)

#define TCG_PYRITE_ADMIN_SP_ACE_TABLE_BASE_ACE_LENGTH 				(1)
#define TCG_PYRITE_ADMIN_SP_ACE_TABLE_AUTHORITY_LENGTH 				(0)
#define TCG_PYRITE_ADMIN_SP_ACE_TABLE_CPIN_LENGTH 					(3)
#define TCG_PYRITE_ADMIN_SP_ACE_TABLE_REMOVAL_MECHANISM_LENGTH 		(1)
#define TCG_PYRITE_1_ADMIN_SP_ACE_TABLE_REMOVAL_MECHANISM_LENGTH 	(0)
#define TCG_PYRITE_2_ADMIN_SP_ACE_TABLE_REMOVAL_MECHANISM_LENGTH 	(1)
#define TCG_OPAL_ADMIN_SP_ACE_TABLE_BASE_ACE_LENGTH					(2)
#define TCG_OPAL_ADMIN_SP_ACE_TABLE_AUTHORITY_LENGTH 				(1)
#define TCG_OPAL_ADMIN_SP_ACE_TABLE_CPIN_LENGTH						(4)
#define TCG_OPAL_ADMIN_SP_ACE_TABLE_REMOVAL_MECHANISM_LENGTH 		(0)

#define TCG_ADMIN_SP_ACE_TABLE_TPER_INFO_LENGTH            (1)
#define TCG_ADMIN_SP_ACE_TABLE_SP_LENGTH                   (1)

#define TCG_ADMIN_SP_ACE_TABLE_PSID_LENGTH						(2)
#define TCG_PYRITE_1_ADMIN_SP_ACE_TABLE_INCLUDE_PSID_LENGTH 	(TCG_PYRITE_ADMIN_SP_ACE_TABLE_BASE_ACE_LENGTH + TCG_PYRITE_ADMIN_SP_ACE_TABLE_AUTHORITY_LENGTH + TCG_PYRITE_ADMIN_SP_ACE_TABLE_CPIN_LENGTH + TCG_PYRITE_1_ADMIN_SP_ACE_TABLE_REMOVAL_MECHANISM_LENGTH + TCG_ADMIN_SP_ACE_TABLE_TPER_INFO_LENGTH + TCG_ADMIN_SP_ACE_TABLE_SP_LENGTH + TCG_ADMIN_SP_ACE_TABLE_PSID_LENGTH)
#define TCG_PYRITE_2_ADMIN_SP_ACE_TABLE_INCLUDE_PSID_LENGTH 	(TCG_PYRITE_ADMIN_SP_ACE_TABLE_BASE_ACE_LENGTH + TCG_PYRITE_ADMIN_SP_ACE_TABLE_AUTHORITY_LENGTH + TCG_PYRITE_ADMIN_SP_ACE_TABLE_CPIN_LENGTH + TCG_PYRITE_2_ADMIN_SP_ACE_TABLE_REMOVAL_MECHANISM_LENGTH + TCG_ADMIN_SP_ACE_TABLE_TPER_INFO_LENGTH + TCG_ADMIN_SP_ACE_TABLE_SP_LENGTH + TCG_ADMIN_SP_ACE_TABLE_PSID_LENGTH)
#define TCG_OPAL_ADMIN_SP_ACE_TABLE_INCLUDE_PSID_LENGTH 		(TCG_OPAL_ADMIN_SP_ACE_TABLE_BASE_ACE_LENGTH + TCG_OPAL_ADMIN_SP_ACE_TABLE_AUTHORITY_LENGTH + TCG_OPAL_ADMIN_SP_ACE_TABLE_CPIN_LENGTH + TCG_OPAL_ADMIN_SP_ACE_TABLE_REMOVAL_MECHANISM_LENGTH + TCG_ADMIN_SP_ACE_TABLE_TPER_INFO_LENGTH + TCG_ADMIN_SP_ACE_TABLE_SP_LENGTH + TCG_ADMIN_SP_ACE_TABLE_PSID_LENGTH)
#define TCG_MAX_ADMIN_SP_ACE_TABLE_LENGTH 						(TCG_OPAL_ADMIN_SP_ACE_TABLE_INCLUDE_PSID_LENGTH)

#define TCG_PYRITE_LOCKING_SP_ACE_TABLE_BASE_ACE_LENGTH 	(2)
#define TCG_PYRITE_LOCKING_SP_ACE_TABLE_ACE_LENGTH			(2)
#define TCG_PYRITE_LOCKING_SP_ACE_TABLE_AUTORITY_LENGTH		(2)
#define TCG_OPAL_LOCKING_SP_ACE_TABLE_BASE_ACE_LENGTH		(4)
#define TCG_OPAL_LOCKING_SP_ACE_TABLE_ACE_LENGTH			(2)
#define TCG_OPAL_LOCKING_SP_ACE_TABLE_AUTORITY_LENGTH		(2 + TCG_OPAL_LOCKING_SP_USER_NUM)

#define TCG_PYRITE_LOCKING_SP_ACE_TABLE_C_PIN_LENGTH 	(2 + TCG_PYRITE_LOCKING_SP_USER_NUM)
#define TCG_OPAL_LOCKING_SP_ACE_TABLE_C_PIN_LENGTH 		((2 + TCG_OPAL_LOCKING_SP_USER_NUM)*2)

#define TCG_PYRITE_LOCKING_SP_ACE_TABLE_KEY_AES_LENGTH			(0)
#define TCG_PYRITE_LOCKING_SP_ACE_TABLE_KEY_AES_256_LENGTH		(0)
#define TCG_PYRITE_LOCKING_SP_ACE_TABLE_LOCKING_LENGTH			(4)
#define TCG_OPAL_LOCKING_SP_ACE_TABLE_KEY_AES_LENGTH         	(1)
#define TCG_OPAL_LOCKING_SP_ACE_TABLE_KEY_AES_256_LENGTH     	(TCG_OPAL_LOCKING_SP_USER_NUM)
#define TCG_OPAL_LOCKING_SP_ACE_TABLE_LOCKING_LENGTH       		(TCG_OPAL_LOCKING_SP_USER_NUM + TCG_OPAL_LOCKING_SP_USER_NUM + TCG_OPAL_LOCKING_SP_USER_NUM + 2)

#define TCG_LOCKING_SP_ACE_TABLE_MBR_CONTROL_LENGTH   (2)

#define TCG_PYRITE_LOCKING_SP_ACE_TABLE_DATASTORE_LENGTH 	(2)
#define TCG_OPAL_LOCKING_SP_ACE_TABLE_DATASTORE_LENGTH 		(2 * TCG_OPAL_LOCKING_SP_USER_NUM)

#define TCG_PYRITE_LOCKING_SP_ACE_TABLE_LENGTH 	(TCG_PYRITE_LOCKING_SP_ACE_TABLE_BASE_ACE_LENGTH + TCG_PYRITE_LOCKING_SP_ACE_TABLE_ACE_LENGTH + TCG_PYRITE_LOCKING_SP_ACE_TABLE_AUTORITY_LENGTH + TCG_PYRITE_LOCKING_SP_ACE_TABLE_C_PIN_LENGTH + TCG_PYRITE_LOCKING_SP_ACE_TABLE_KEY_AES_LENGTH + TCG_PYRITE_LOCKING_SP_ACE_TABLE_KEY_AES_256_LENGTH + TCG_PYRITE_LOCKING_SP_ACE_TABLE_LOCKING_LENGTH + TCG_LOCKING_SP_ACE_TABLE_MBR_CONTROL_LENGTH + TCG_PYRITE_LOCKING_SP_ACE_TABLE_DATASTORE_LENGTH)
#define TCG_OPAL_LOCKING_SP_ACE_TABLE_LENGTH 	(TCG_OPAL_LOCKING_SP_ACE_TABLE_BASE_ACE_LENGTH + TCG_OPAL_LOCKING_SP_ACE_TABLE_ACE_LENGTH + TCG_OPAL_LOCKING_SP_ACE_TABLE_AUTORITY_LENGTH + TCG_OPAL_LOCKING_SP_ACE_TABLE_C_PIN_LENGTH + TCG_OPAL_LOCKING_SP_ACE_TABLE_KEY_AES_LENGTH + TCG_OPAL_LOCKING_SP_ACE_TABLE_KEY_AES_256_LENGTH + TCG_OPAL_LOCKING_SP_ACE_TABLE_LOCKING_LENGTH + TCG_LOCKING_SP_ACE_TABLE_MBR_CONTROL_LENGTH + TCG_OPAL_LOCKING_SP_ACE_TABLE_DATASTORE_LENGTH)

#define TCG_PYRITE_LOCKING_SP_ACE_TABLE_FEATURE_LENGTH 			(TCG_PYRITE_LOCKING_SP_ACE_TABLE_LENGTH)
#define TCG_OPAL_LOCKING_SP_SINGLE_USER_MODE_ACE_TABLE_LENGTH 	(TCG_OPAL_LOCKING_SP_ACE_TABLE_LENGTH + (3 + ((TCG_OPAL_LOCKING_SP_USER_NUM -  1) * 3) + 1))
#define TCG_OPAL_LOCKING_SP_ACE_TABLE_FEATURE_LENGTH			(TCG_OPAL_LOCKING_SP_SINGLE_USER_MODE_ACE_TABLE_LENGTH)

#define TCG_ADMIN_SP_AUTHORITY_TABLE_PSID_LENGTH      				(1)
#define TCG_PYRITE_ADMIN_SP_AUTHORITY_TABLE_INCLUDE_PSID_LENGTH 	(4)
#define TCG_OPAL_ADMIN_SP_AUTHORITY_TABLE_INCLUDE_PSID_LENGTH 		(6)
#define TCG_MAX_ADMIN_SP_AUTHORITY_TABLE_LENGTH 					(TCG_OPAL_ADMIN_SP_AUTHORITY_TABLE_INCLUDE_PSID_LENGTH)

#define TCG_PYRITE_LOCKING_SP_AUTHORITY_TABLE_LENGTH 				(3 + TCG_PYRITE_LOCKING_SP_ADMIN_NUM + TCG_PYRITE_LOCKING_SP_USER_NUM)
#define TCG_OPAL_LOCKING_SP_AUTHORITY_TABLE_LENGTH 					(3 + TCG_OPAL_LOCKING_SP_ADMIN_NUM + TCG_OPAL_LOCKING_SP_USER_NUM)
#define TCG_MAX_LOCKING_SP_AUTHORITY_TABLE_LENGTH 					(TCG_OPAL_LOCKING_SP_AUTHORITY_TABLE_LENGTH)

#define TCG_ADMIN_SP_C_PIN_TABLE_PSID_LENGTH          			(1)
#define TCG_PYRITE_ADMIN_SP_C_PIN_TABLE_INCLUDE_PSID_LENGTH 	(3)
#define TCG_OPAL_ADMIN_SP_C_PIN_TABLE_INCLUDE_PSID_LENGTH 		(4)
#define TCG_MAX_ADMIN_SP_C_PIN_TABLE_LENGTH 					(TCG_OPAL_ADMIN_SP_C_PIN_TABLE_INCLUDE_PSID_LENGTH)

#define TCG_PYRITE_LOCKING_SP_C_PIN_TABLE_LENGTH 		(TCG_PYRITE_LOCKING_SP_ADMIN_NUM + TCG_PYRITE_LOCKING_SP_USER_NUM)
#define TCG_OPAL_LOCKING_SP_C_PIN_TABLE_LENGTH			(TCG_OPAL_LOCKING_SP_ADMIN_NUM + TCG_OPAL_LOCKING_SP_USER_NUM)
#define TCG_MAX_LOCKING_SP_C_PIN_TABLE_LENGTH 			(TCG_OPAL_LOCKING_SP_C_PIN_TABLE_LENGTH)

#define TCG_LOCKING_SP_TPER_INFO_TABLE_LENGTH           (1)
#define TCG_ADMIN_SP_TEMPLATE_TABLE_LENGTH            	(3)
#define TCG_ADMIN_SP_SP_TABLE_LENGTH                  	(2)
#define TCG_ADMIN_SP_DATA_REMOVAL_MECHANISM_LENGTH 		(1)
#define TCG_LOCKING_SP_SECRET_PROTECT_TABLE_LENGTH    	(1)
#define TCG_LOCKING_SP_LOCKING_INFO_TABLE_LENGTH      	(1)

#define TCG_PYRITE_LOCKING_SP_LOCKING_TABLE_LENGTH 	(1)
#define TCG_OPAL_LOCKING_SP_LOCKING_TABLE_LENGTH 	(TCG_OPAL_LOCKING_SP_USER_NUM)
#define TCG_MAX_LOCKING_SP_LOCKING_TABLE_LENGTH 	(TCG_OPAL_LOCKING_SP_LOCKING_TABLE_LENGTH)

#define TCG_LOCKING_SP_MBR_CONTROL_TABLE_LENGTH       (1)

#define TCG_OPAL_LOCKING_SP_KEY_AES_256_TABLE_LENGTH 		(TCG_OPAL_LOCKING_SP_USER_NUM)
#define TCG_MAX_LOCKING_SP_KEY_AES_256_TABLE_LENGTH 		(TCG_OPAL_LOCKING_SP_KEY_AES_256_TABLE_LENGTH)

#define TCG_PROPERTY_METHOD_EACH_PARAMETER_MAX_SIZE                      	(32)
#define TCG_PROPERTY_METHOD_MAX_COMAPACKET_SIZE_STRING_LENGTH             	(16)
#define TCG_PROPERTY_METHOD_MAX_RESPPONSE_COMAPACKET_SIZE_STRING_LENGTH     (24)
#define TCG_PROPERTY_METHOD_MAX_PACKET_SIZE_STRING_LENGTH                 	(13)
#define TCG_PROPERTY_METHOD_MAX_SUBPACKET_SIZE_STRING_LENGTH                (13)
#define TCG_PROPERTY_METHOD_MAX_INDEX_TOKEN_SIZE_STRING_LENGTH              (15)
#define TCG_PROPERTY_METHOD_MAX_PACKETS_STRING_LENGTH                     	(10)
#define TCG_PROPERTY_METHOD_MAX_METHODS_STRING_LENGTH                     	(10)
#define TCG_PROPERTY_METHOD_MAX_SESSIONS_STRING_LENGTH                    	(11)
#define TCG_PROPERTY_METHOD_MAX_AUTHENTICATIONS_STRING_LENGTH             	(18)
#define TCG_PROPERTY_METHOD_MAX_TRANSACTION_LIMIT_STRING_LENGTH           	(19)
#define TCG_PROPERTY_METHOD_DEF_SESSION_TIMEOUT_STRING_LENGTH             	(17)

#define TCG_512B_BYTE_SHIFT	(9)
#define TCG_4KB_BYTE_SHIFT	(12)
#define TCG_DMAC_TRIM_MERGE_ADDR_ALIGN	(32)
#define TCG_ASCII_0 (48)
#define TCG_RESPONESE_TO_COME_NO_RESPONSE_AVAILABLE_OUTSTANDING_DATA (1)
#define TCG_OVERWRITE_ERASE_TIME_128GB_IN_SECOND (1080) //unit: second
#define TCG_BLOCK_ERASE_TIME_128GB_IN_SECOND (20) //unit: second
#define TCG_UNMAP_TIME_DIVIDE_2_IN_SECOND (5) //unit: second

#define TCG_ADMIN_SP_CPIN_SID_TABLE_IDX						(0X00)
#define TCG_ADMIN_SP_CPIN_MSID_TABLE_IDX					(0X01)
#define TCG_ADMIN_SP_SP_LOCKING_TABLE_IDX					(0X01)
#define TCG_ADMIN_SP_TPERINFO_TABLE_IDX						(0X00)
#define TCG_LOCKING_SP_LOCKING_GLOBAL_RANGE_TABLE_IDX		(0X00)
#define TCG_LOCKING_SP_CPIN_ADMIN1_TABLE_IDX				(0X00)
#define TCG_LOCKING_SP_TABLE_ACE_TABLE_IDX					(0X05)
#define TCG_LOCKING_SP_MBRCONTROL_TABLE_IDX					(0X00)

#define TCG_OPAL_ADMIN_SP_CPIN_PSID_TABLE_IDX 				(0x03)
#define TCG_OPAL_LOCKING_SP_TABLE_MBR_TABLE_IDX 			(0X0C)
#define TCG_OPAL_LOCKING_SP_TABLE_DATASTORE_TABLE_IDX		(0X0E)
#define TCG_PYRITE_ADMIN_SP_CPIN_PSID_TABLE_IDX 			(0x02)
#define TCG_PYRITE_LOCKING_SP_TABLE_MBR_TABLE_IDX			(0X0B)
#define TCG_PYRITE_LOCKING_SP_TABLE_DATASTORE_TABLE_IDX		(0x0C)

// Security version related def
#define TCG_UPDATE_TABLE_ORIGIN_TABLE_VALID (0)
#define TCG_UPDATE_TABLE_SPARE_TABLE_VALID	(1)
#define TCG_SECURITE_VERSION_SRC_LB_OFFSET (1)
#define TCG_SECURITE_VERSION_DST_LB_OFFSET (2)
// --- Security version 3 def ----
#define TCG_VARIABLE_TABLE_RESERVED_SIZE_VERSION_3	(2020)
#define TCG_AES_KEY_NUM_VERSION_3					(16)

//BufferIndex
typedef enum TcgFlashTableIndex {
	TcgFlashTableVT1Index,
	TcgFlashTableVT2Index,
	TcgFlashTableVT3Index,

	TcgFlashTableOriginAdmin1Index,
	TcgFlashTableOriginAdmin2Index,
	TcgFlashTableOriginAdmin3Index,

	TcgFlashTableOriginLocking1Index,
	TcgFlashTableOriginLocking2Index,
	TcgFlashTableOriginLocking3Index,
	TcgFlashTableOriginLocking4Index,
	TcgFlashTableOriginLocking5Index,
	TcgFlashTableOriginLocking6Index,
	TcgFlashTableOriginLocking7Index,
	TcgFlashTableOriginLocking8Index,
	TcgFlashTableOriginLocking9Index,

	TcgFlashTableSpareAdmin1Index,
	TcgFlashTableSpareAdmin2Index,
	TcgFlashTableSpareAdmin3Index,

	TcgFlashTableSpareLocking1Index,
	TcgFlashTableSpareLocking2Index,
	TcgFlashTableSpareLocking3Index,
	TcgFlashTableSpareLocking4Index,
	TcgFlashTableSpareLocking5Index,
	TcgFlashTableSpareLocking6Index,
	TcgFlashTableSpareLocking7Index,
	TcgFlashTableSpareLocking8Index,
	TcgFlashTableSpareLocking9Index,

	TcgFlashTableInvalidIndex,
} TcgFlashTableIndex_t;

typedef enum TcgTableKind {
	TcgTableDefaultKind,	// 0
	TcgTableObjectKind,	// 1
	TcgTableByteKind,		// 2
} TcgTableKind_t;

typedef enum TcgBooleanAce {
	TcgAndBoolean, // 0
	TcgOrBoolean, // 1
	TcgNotBoolean // 2
} TcgBooleanAce_t;

typedef enum TcgAuthorityOperation {
	TcgAuthorityNoneOperation,
	TcgAuthorityPasswordOperation,
	TcgAuthoritySignOperation,
	TcgAuthorityExchangeOperation,
	TcgAuthoritySymkOperation,
	TcgAuthorityHmacOperation,
	TcgAuthorityTperSignOperation,
	TcgAuthorityTperExchangeOperation
} TcgAuthorityOperation_t;

typedef enum TcgEncryptSupport {
	TcgNoneEncryptSupport,
	TcgMediaEncryptSupport
} TcgEncryptSupport_t;

typedef enum TcgReset {
	TcgPowerCycleReset,// 0
	TcgHardwareReset,// 1
	TcgHotPlugReset,// 2
	TcgProgramaticReset// 3
} TcgReset_t;

typedef enum TcgSymmetricMediaEncrypt {
	TcgECBSymmetricMediaEncrypt,
	TcgCBCSymmetricMediaEncrypt,
	TcgCFBSymmetricMediaEncrypt,
	TcgOFBSymmetricMediaEncrypt,
	TcgGCMSymmetricMediaEncrypt,
	TcgCTRSymmetricMediaEncrypt,
	TcgCCMSymmetricMediaEncrypt,
	TcgXTSSymmetricMediaEncrypt,
	TcgLRWSymmetricMediaEncrypt,
	TcgEMESymmetricMediaEncrypt,
	TcgCMCSymmetricMediaEncrypt,
	TcgXEXSymmetricMediaEncrypt,
	TcgSymmetricMediaEncryption = 23
} TcgSymmetricMediaEncrypt_t;

typedef enum RangeLengthSettingPolicy {	//single user mode
	RangeLengthUserSettingPolicy,
	RangeLengthAdminSettingPolicy
} RangeLengthSettingPolicy_t;

typedef enum TcgComIdState {
	TcgInvalidComIdState,
	TcgInactiveComIdState,
	TcgIssuedComIdState,
	TcgAssociatedComIdState
} TcgComIdState_t ;

typedef enum TcgIfState {
	TcgIfSendStayState    = 0x00,
	TcgIfSendState         = 0x01,
	TcgIfRecvState         = 0x02,
	TcgIfRecvStayState    = 0x03
} TcgIfState_t;

typedef enum TcgTokenType {
	TcgTokenUidType,
	TcgTokenSimpleType,
	TcgTokenSequenceType,
	TcgTokenDataType
} TcgTokenType_t;

typedef enum TcgSimpleTokenType {
	TcgSimpleTokenTinyType,
	TcgSimpleTokenShortType,
	TcgSimpleTokenMediumType,
	TcgSimpleTokenLongType
} TcgSimpleTokenType_t;

typedef enum TcgTokenSign {
	TcgTokenUnsign,
	TcgTokenSign,
} TcgTokenSign_t;

typedef enum TcgTokenDataType {
	TcgTokenDataIntegerType,
	TcgTokenDataByteType
} TcgTokenDataType_t;

typedef enum TcgListToken {
	TcgPadToken               = 0x00,
	TcgStartListToken        = 0xF0,
	TcgEndListToken          = 0xF1,
	TcgStartNameToken        = 0xF2,
	TcgEndNameToken          = 0xF3,
	TcgReservedF4Token            = 0xF4,
	TcgReservedF5Token            = 0xF5,
	TcgReservedF6Token            = 0xF6,
	TcgReservedF7Token            = 0xF7,
	TcgCallToken        = 0xF8,
	TcgEndOfDataToken       = 0xF9,
	TcgEndOfSessionToken    = 0xFA,
	TcgStartTransactionToken = 0xFB,
	TcgEndTransactionToken   = 0xFC,
	TcgEmptyAtomsToken       = 0xFF
} TcgListToken_t;

typedef enum TcgSessionManagementMethodUidL {
	TcgSessionManagementUidH               = 0x00000000,
	TcgSessionManagementUidL               = 0x000000FF,
	TcgSessionManagementPropertiesMethodUidL    = 0x0000FF01,
	TcgSessionManagementStartSessionMethodUidL = 0x0000FF02,
	TcgSessionManagementSyncSessionMethodUidL  = 0x0000FF03,
	TcgSessionManagementCloseSessionMethodUidL = 0x0000FF06,
} TcgSessionManagementMethodUidL_t;

typedef enum TcgSpTemplateBaseUidL {
	TcgSpTemplateBaseUidL      = 0x00000001,
	TcgSpTemplateAdminUidL     = 0x00000002,
	TcgSpTemplateLockingUidL   = 0x00000002
} TcgSpTemplateUidL_t;

typedef enum TcgSpInfoUidL {
	TcgSpInfoUidL      = 0x00000001,
} TcgSpInfoUidL_t;

typedef enum TcgTableUidL {
	TcgTableTableUidL           = 0x00000001,
	TcgTableSpInfoUidL         = 0x00000002,
	TcgTableSpTemplateUidL    = 0x00000003,
	TcgTableMethodUidL       = 0x00000006,
	TcgTableAclUidL  = 0x00000007,
	TcgTableAceUidL             = 0x00000008,
	TcgTableAuthorityUidL       = 0x00000009,
	TcgTableCPinUidL           = 0x0000000B,
	TcgTableSecretProtectUidL  = 0x0000001D,
	TcgTableTperInfoUidL       = 0x00000201,
	TcgTableTemplateUidL        = 0x00000204,
	TcgTableSpUidL              = 0x00000205,
	TcgTableLockingInfoUidL    = 0x00000801,
	TcgTableLockingUidL         = 0x00000802,
	TcgTableMBRControlUidL     = 0x00000803,
	TcgTableMBRUidL             = 0x00000804,
	TcgTableKeyAes128UidL       = 0x00000805,
	TcgTableKeyAes256UidL       = 0x00000806,
	TcgTableDataStoreUidL      = 0x00001001,
	// ref. ADD DATASTORE
	TcgTableDataStore2UidL,
	TcgTableDataStore3UidL,
	TcgTableDataStore4UidL,
	TcgTableDataStore5UidL,
	TcgTableDataStore6UidL,
	TcgTableDataStore7UidL,
	TcgTableDataStore8UidL,
	TcgTableDataStore9UidL,
	TcgTableDataStore10UidL,
	TcgTableDataStore11UidL,
	TcgTableDataStore12UidL,
	TcgTableDataStore13UidL,
	TcgTableDataStore14UidL,
	TcgTableDataStore15UidL,
	TcgTableDataStore16UidL,

	TcgTableDataRemovalMechanismUidL = 0x00001101,
} TcgTableUidL_t;

typedef enum TcgThisSpUid {
	TcgThisSpUidH	= 0x00000000,
	TcgThisSpUidL	= 0x00000001,
} TcgThisSpUid_t;

typedef enum TcgMethodUidL {
	TcgMethodNextUidL        = 0x00000008,
	TcgMethodGetAclUidL     = 0x0000000D,
	TcgMethodGenKeyUidL     = 0X00000010,
	TcgMethodRevertSpUidL   = 0X00000011,
	TcgMethodGetUidL         = 0x00000016,
	TcgMethodSetUidL         = 0x00000017,
	TcgMethodAuthenticateUidL = 0x0000001C,
	TcgMethodRevertUidL      = 0x00000202,
	TcgMethodActivateUidL    = 0x00000203,
	TcgMethodRandomUidL      = 0x00000601,
	TcgMethodReactivateUidL  = 0x00000801,
	TcgMethodEraseUidL       = 0x00000803,

	TcgMethodNullUidL		= 0xffffffff,
} TcgMethodUidL_t;

typedef enum TcgAceUidL {
	// Base ACEs
	TcgAceAnybodyUidL                                   = 0x00000001,
	TcgAceAdminUidL                                     = 0x00000002,
	TcgAceAnybodyGetCommonNameUidL                    = 0x00000003,
	TcgAceAnybodySetCommonNameUidL                     = 0x00000004,

	// C_PIN
	TcgAceCPinSidGetNoPinUidL                      = 0x00008C02,
	TcgAceCPinSidSetPinUidL                         = 0x00008C03,
	TcgAceCPinMsidGetPinUidL                        = 0x00008C04,

	// SP - ref. Psid
	TcgAceSpPsidUidL                                   = 0x000100E0,

	// C_PIN - ref. Psid
	TcgAceCPinGetPsidNoPinUidL                      = 0x000100E1,

	// Authority
	TcgAceSetEnabledUidL                               = 0x00030001,

	// SP
	TcgAceSpSidUidL                                    = 0x00030002,

	// TPerInfo
	TcgAceTperInfoSetProgrammaticResetEnableUidL    = 0x00030003,

	// ACE
	TcgAceAceGetAllUidL                               = 0x00038000,
	TcgAceAceSetBooleanExpressionUidL                 = 0x00038001,

	// Authority
	TcgAceAuthorityGetAllUidL                         = 0x00039000,
	TcgAceAuthoritySetEnabledUidL                     = 0x00039001,

	// C_PIN
	TcgAceCPinAdminsGetAllNoPinUidL               = 0x0003A000,
	TcgAceCPinAdminsSetPinUidL                      = 0x0003A001,

	// C_PIN - vendor
	TcgAceCPinAdminsGetAllNoPinVendorUidL       = 0x0003A700,	//[To be modified] Undefine in Spec, Should be removed
	TcgAceCPinAdminsSetPinVendorUidL = 0x0003A701,				//[To be modified] Undefine in Spec, Should be removed

	// C_PIN
	TcgAceCPinUser1SetPinUidL                       = 0x0003A801,
	TcgAceCPinUser2SetPinUidL,
	TcgAceCPinUser3SetPinUidL,
	TcgAceCPinUser4SetPinUidL,
	TcgAceCPinUser5SetPinUidL,
	TcgAceCPinUser6SetPinUidL,
	TcgAceCPinUser7SetPinUidL,
	TcgAceCPinUser8SetPinUidL,
	TcgAceCPinUser9SetPinUidL,
	TcgAceCPinUser10SetPinUidL,
	TcgAceCPinUser11SetPinUidL,
	TcgAceCPinUser12SetPinUidL,
	TcgAceCPinUser13SetPinUidL,
	TcgAceCPinUser14SetPinUidL,
	TcgAceCPinUser15SetPinUidL,
	TcgAceCPinUser16SetPinUidL,

	// C_PIN - vendor
	TcgAceCPinUser1SetPinVendorUidL	= 0x0003AF01,//[To be modified] Undefine in Spec, Should be removed
	TcgAceCPinUser2SetPinVendorUidL,//[To be modified] Undefine in Spec, Should be removed
	TcgAceCPinUser3SetPinVendorUidL,//[To be modified] Undefine in Spec, Should be removed
	TcgAceCPinUser4SetPinVendorUidL,//[To be modified] Undefine in Spec, Should be removed
	TcgAceCPinUser5SetPinVendorUidL,//[To be modified] Undefine in Spec, Should be removed
	TcgAceCPinUser6SetPinVendorUidL,//[To be modified] Undefine in Spec, Should be removed
	TcgAceCPinUser7SetPinVendorUidL,//[To be modified] Undefine in Spec, Should be removed
	TcgAceCPinUser8SetPinVendorUidL,//[To be modified] Undefine in Spec, Should be removed
	TcgAceCPinUser9SetPinVendorUidL,//[To be modified] Undefine in Spec, Should be removed
	TcgAceCPinUser10SetPinVendorUidL,//[To be modified] Undefine in Spec, Should be removed
	TcgAceCPinUser11SetPinVendorUidL,//[To be modified] Undefine in Spec, Should be removed
	TcgAceCPinUser12SetPinVendorUidL,//[To be modified] Undefine in Spec, Should be removed
	TcgAceCPinUser13SetPinVendorUidL,//[To be modified] Undefine in Spec, Should be removed
	TcgAceCPinUser14SetPinVendorUidL,//[To be modified] Undefine in Spec, Should be removed
	TcgAceCPinUser15SetPinVendorUidL,//[To be modified] Undefine in Spec, Should be removed
	TcgAceCPinUser16SetPinVendorUidL,//[To be modified] Undefine in Spec, Should be removed

	// K_AES_256
	TcgAceKeyAes256GlobalRangeGenKeyUidL              = 0x0003B800,
	TcgAceKeyAes256Range1GenKeyUidL                   = 0x0003B801,
	TcgAceKeyAes256Range2GenKeyUidL,
	TcgAceKeyAes256Range3GenKeyUidL,
	TcgAceKeyAes256Range4GenKeyUidL,
	TcgAceKeyAes256Range5GenKeyUidL,
	TcgAceKeyAes256Range6GenKeyUidL,
	TcgAceKeyAes256Range7GenKeyUidL,
	TcgAceKeyAes256Range8GenKeyUidL,
	TcgAceKeyAes256Range9GenKeyUidL,
	TcgAceKeyAes256Range10GenKeyUidL,
	TcgAceKeyAes256Range11GenKeyUidL,
	TcgAceKeyAes256Range12GenKeyUidL,
	TcgAceKeyAes256Range13GenKeyUidL,
	TcgAceKeyAes256Range14GenKeyUidL,
	TcgAceKeyAes256Range15GenKeyUidL,

	// K_AES
	TcgAceKeyAesModeUidL                                = 0x0003BFFF,

	// Locking
	TcgAceLockingGlobalRangeGetRangeStartToActiveKeyUidL = 0x0003D000,
	TcgAceLockingRange1GetRangeStartToActiveKeyUidL      = 0x0003D001,
	TcgAceLockingRange2GetRangeStartToActiveKeyUidL,
	TcgAceLockingRange3GetRangeStartToActiveKeyUidL,
	TcgAceLockingRange4GetRangeStartToActiveKeyUidL,
	TcgAceLockingRange5GetRangeStartToActiveKeyUidL,
	TcgAceLockingRange6GetRangeStartToActiveKeyUidL,
	TcgAceLockingRange7GetRangeStartToActiveKeyUidL,
	TcgAceLockingRange8GetRangeStartToActiveKeyUidL,
	TcgAceLockingRange9GetRangeStartToActiveKeyUidL,
	TcgAceLockingRange10GetRangeStartToActiveKeyUidL,
	TcgAceLockingRange11GetRangeStartToActiveKeyUidL,
	TcgAceLockingRange12GetRangeStartToActiveKeyUidL,
	TcgAceLockingRange13GetRangeStartToActiveKeyUidL,
	TcgAceLockingRange14GetRangeStartToActiveKeyUidL,
	TcgAceLockingRange15GetRangeStartToActiveKeyUidL,

	TcgAceLockingGlobalRangeSetReadLockedUidL              = 0x0003E000,
	TcgAceLockingRange1SetReadLockedUidL                   = 0x0003E001,
	TcgAceLockingRange2SetReadLockedUidL,
	TcgAceLockingRange3SetReadLockedUidL,
	TcgAceLockingRange4SetReadLockedUidL,
	TcgAceLockingRange5SetReadLockedUidL,
	TcgAceLockingRange6SetReadLockedUidL,
	TcgAceLockingRange7SetReadLockedUidL,
	TcgAceLockingRange8SetReadLockedUidL,
	TcgAceLockingRange9SetReadLockedUidL,
	TcgAceLockingRange10SetReadLockedUidL,
	TcgAceLockingRange11SetReadLockedUidL,
	TcgAceLockingRange12SetReadLockedUidL,
	TcgAceLockingRange13SetReadLockedUidL,
	TcgAceLockingRange14SetReadLockedUidL,
	TcgAceLockingRange15SetReadLockedUidL,

	TcgAceLockingGlobalRangeSetWriteLockedUidL              = 0x0003E800,
	TcgAceLockingRange1SetWriteLockedUidL                   = 0x0003E801,
	TcgAceLockingRange2SetWriteLockedUidL,
	TcgAceLockingRange3SetWriteLockedUidL,
	TcgAceLockingRange4SetWriteLockedUidL,
	TcgAceLockingRange5SetWriteLockedUidL,
	TcgAceLockingRange6SetWriteLockedUidL,
	TcgAceLockingRange7SetWriteLockedUidL,
	TcgAceLockingRange8SetWriteLockedUidL,
	TcgAceLockingRange9SetWriteLockedUidL,
	TcgAceLockingRange10SetWriteLockedUidL,
	TcgAceLockingRange11SetWriteLockedUidL,
	TcgAceLockingRange12SetWriteLockedUidL,
	TcgAceLockingRange13SetWriteLockedUidL,
	TcgAceLockingRange14SetWriteLockedUidL,
	TcgAceLockingRange15SetWriteLockedUidL,

	TcgAceLockingGlobalRangeAdminsSetUidL                = 0x0003F000,
	TcgAceLockingAdminsRangeStartToLockOnResetUidL            = 0x0003F001,

	// MBRControl
	TcgAceMBRControlAdminsSetUidL                     = 0x0003F800,
	TcgAceMBRControlSetDoneToDoneOnResetUidL                  = 0x0003F801,

	// DataStore
	TcgAceDataStoreGetAllUidL                         = 0x0003FC00,
	TcgAceDataStoreSetAllUidL                         = 0x0003FC01,

	// DataStore - ref. ADD DATASTORE
	TcgAceDataStore2GetAllUidL                        = 0x0003FC02,
	TcgAceDataStore2SetAllUidL                        = 0x0003FC03,
	TcgAceDataStore3GetAllUidL,
	TcgAceDataStore3SetAllUidL,
	TcgAceDataStore4GetAllUidL,
	TcgAceDataStore4SetAllUidL,
	TcgAceDataStore5GetAllUidL,
	TcgAceDataStore5SetAllUidL,
	TcgAceDataStore6GetAllUidL,
	TcgAceDataStore6SetAllUidL,
	TcgAceDataStore7GetAllUidL,
	TcgAceDataStore7SetAllUidL,
	TcgAceDataStore8GetAllUidL,
	TcgAceDataStore8SetAllUidL,
	TcgAceDataStore9GetAllUidL,
	TcgAceDataStore9SetAllUidL,
	TcgAceDataStore10GetAllUidL,
	TcgAceDataStore10SetAllUidL,
	TcgAceDataStore11GetAllUidL,
	TcgAceDataStore11SetAllUidL,
	TcgAceDataStore12GetAllUidL,
	TcgAceDataStore12SetAllUidL,
	TcgAceDataStore13GetAllUidL,
	TcgAceDataStore13SetAllUidL,
	TcgAceDataStore14GetAllUidL,
	TcgAceDataStore14SetAllUidL,
	TcgAceDataStore15GetAllUidL,
	TcgAceDataStore15SetAllUidL,
	TcgAceDataStore16GetAllUidL,
	TcgAceDataStore16SetAllUidL,

	// ref. single user mode
	TcgAceLockingGlobalRangeSetReadLockEnabledToLockOnResetUidL  = 0x00040000,
	TcgAceLockingRange1SetReadLockEnabledToLockOnResetUidL       = 0x00040001,
	TcgAceLockingRange2SetReadLockEnabledToLockOnResetUidL,
	TcgAceLockingRange3SetReadLockEnabledToLockOnResetUidL,
	TcgAceLockingRange4SetReadLockEnabledToLockOnResetUidL,
	TcgAceLockingRange5SetReadLockEnabledToLockOnResetUidL,
	TcgAceLockingRange6SetReadLockEnabledToLockOnResetUidL,
	TcgAceLockingRange7SetReadLockEnabledToLockOnResetUidL,
	TcgAceLockingRange8SetReadLockEnabledToLockOnResetUidL,
	TcgAceLockingRange9SetReadLockEnabledToLockOnResetUidL,
	TcgAceLockingRange10SetReadLockEnabledToLockOnResetUidL,
	TcgAceLockingRange11SetReadLockEnabledToLockOnResetUidL,
	TcgAceLockingRange12SetReadLockEnabledToLockOnResetUidL,
	TcgAceLockingRange13SetReadLockEnabledToLockOnResetUidL,
	TcgAceLockingRange14SetReadLockEnabledToLockOnResetUidL,
	TcgAceLockingRange15SetReadLockEnabledToLockOnResetUidL,

	TcgAceLockingRange1SetRangeStartToRangeLengthUidL    = 0x00041001,
	TcgAceLockingRange2SetRangeStartToRangeLengthUidL,
	TcgAceLockingRange3SetRangeStartToRangeLengthUidL,
	TcgAceLockingRange4SetRangeStartToRangeLengthUidL,
	TcgAceLockingRange5SetRangeStartToRangeLengthUidL,
	TcgAceLockingRange6SetRangeStartToRangeLengthUidL,
	TcgAceLockingRange7SetRangeStartToRangeLengthUidL,
	TcgAceLockingRange8SetRangeStartToRangeLengthUidL,
	TcgAceLockingRange9SetRangeStartToRangeLengthUidL,
	TcgAceLockingRange10SetRangeStartToRangeLengthUidL,
	TcgAceLockingRange11SetRangeStartToRangeLengthUidL,
	TcgAceLockingRange12SetRangeStartToRangeLengthUidL,
	TcgAceLockingRange13SetRangeStartToRangeLengthUidL,
	TcgAceLockingRange14SetRangeStartToRangeLengthUidL,
	TcgAceLockingRange15SetRangeStartToRangeLengthUidL,

	TcgAceCPinAnybodyGetNoPinUidL                       = 0x00042000,
	TcgAceSpReactivateAdminUidL                           = 0x00042001,
	TcgAceLockingGlobalRangeEraseUidL                     = 0x00043000,
	TcgAceLockingRange1EraseUidL                          = 0x00043001,
	TcgAceLockingRange2EraseUidL,
	TcgAceLockingRange3EraseUidL,
	TcgAceLockingRange4EraseUidL,
	TcgAceLockingRange5EraseUidL,
	TcgAceLockingRange6EraseUidL,
	TcgAceLockingRange7EraseUidL,
	TcgAceLockingRange8EraseUidL,
	TcgAceLockingRange9EraseUidL,
	TcgAceLockingRange10EraseUidL,
	TcgAceLockingRange11EraseUidL,
	TcgAceLockingRange12EraseUidL,
	TcgAceLockingRange13EraseUidL,
	TcgAceLockingRange14EraseUidL,
	TcgAceLockingRange15EraseUidL,

	// Authority
	TcgAceUser1SetCommonNameUidL                       = 0x00044001,
	TcgAceUser2SetCommonNameUidL,
	TcgAceUser3SetCommonNameUidL,
	TcgAceUser4SetCommonNameUidL,
	TcgAceUser5SetCommonNameUidL,
	TcgAceUser6SetCommonNameUidL,
	TcgAceUser7SetCommonNameUidL,
	TcgAceUser8SetCommonNameUidL,
	TcgAceUser9SetCommonNameUidL,
	TcgAceUser10SetCommonNameUidL,
	TcgAceUser11SetCommonNameUidL,
	TcgAceUser12SetCommonNameUidL,
	TcgAceUser13SetCommonNameUidL,
	TcgAceUser14SetCommonNameUidL,
	TcgAceUser15SetCommonNameUidL,
	TcgAceUser16SetCommonNameUidL,

	TcgAceDataRemovalMechanismSetActiveDataRemovalMechanismUidL = 0x00050001,

} TcgAceUidL_t;

typedef enum TcgAuthorityUidL {
	TcgAuthorityAnybodyUidL         = 0x00000001,
	TcgAuthorityAdminsUidL          = 0x00000002,
	TcgAuthorityMakersUidL          = 0x00000003,
	TcgAuthoritySidUidL             = 0x00000006,
	TcgAdminSpAuthorityAdmin1UidL    = 0x00000201,

	TcgLockingSpAuthorityAdmin1UidL  = 0x00010001,
	TcgLockingSpAuthorityAdmin2UidL,
	TcgLockingSpAuthorityAdmin3UidL,
	TcgLockingSpAuthorityAdmin4UidL,

	// ref. Psid
	TcgAuthorityPsidUidL            = 0x0001FF01,

	TcgLockingSpAuthorityUsersUidL   = 0x00030000,
	TcgLockingSpAuthorityUser1UidL   = 0x00030001,
	TcgLockingSpAuthorityUser2UidL,
	TcgLockingSpAuthorityUser3UidL,
	TcgLockingSpAuthorityUser4UidL,
	TcgLockingSpAuthorityUser5UidL,
	TcgLockingSpAuthorityUser6UidL,
	TcgLockingSpAuthorityUser7UidL,
	TcgLockingSpAuthorityUser8UidL,
	TcgLockingSpAuthorityUser9UidL,
	TcgLockingSpAuthorityUser10UidL,
	TcgLockingSpAuthorityUser11UidL,
	TcgLockingSpAuthorityUser12UidL,
	TcgLockingSpAuthorityUser13UidL,
	TcgLockingSpAuthorityUser14UidL,
	TcgLockingSpAuthorityUser15UidL,
	TcgLockingSpAuthorityUser16UidL,

	TcgAuthorityACENullUidL         = 0x0FFFFFFF, // @JIRA_E13-564, Only for ACElement use, because ACElement's UidL is 28 bit
	TcgAuthorityNullUidL            = 0xFFFFFFFF
} TcgAuthorityUidL_t;

typedef enum TcgCPinUidL {
	TcgAdminSpCPinSidUidL       = 0x00000001,
	TcgAdminSpCPinMsidUidL      = 0x00008402,
	TcgAdminSpCPinAdmin1UidL    = 0x00000201,

	TcgLockingSpCPinAdmin1UidL  = 0x00010001,
	TcgLockingSpCPinAdmin2UidL,
	TcgLockingSpCPinAdmin3UidL,
	TcgLockingSpCPinAdmin4UidL,

	TcgCPinPsidUidL            = 0x0001FF01,

	TcgLockingSpCPinUser1UidL   = 0x00030001,
	TcgLockingSpCPinUser2UidL,
	TcgLockingSpCPinUser3UidL,
	TcgLockingSpCPinUser4UidL,
	TcgLockingSpCPinUser5UidL,
	TcgLockingSpCPinUser6UidL,
	TcgLockingSpCPinUser7UidL,
	TcgLockingSpCPinUser8UidL,
	TcgLockingSpCPinUser9UidL,
	TcgLockingSpCPinUser10UidL,
	TcgLockingSpCPinUser11UidL,
	TcgLockingSpCPinUser12UidL,
	TcgLockingSpCPinUser13UidL,
	TcgLockingSpCPinUser14UidL,
	TcgLockingSpCPinUser15UidL,
	TcgLockingSpCPinUser16UidL,

	TcgCPinNullUidL            = 0xFFFFFFFF
} TcgCPinUidL_t;

typedef enum TcgSecretProtectUidL {
	TcgSecretProtectKeyAes256UidL = 0x0000001E,
} TcgSecretProtectUidL_t;

typedef enum TcgLockingInfoUidL {
	TcgLockingInfoUidL = 0x00000001,
} TcgLockingInfoUidL_t;

typedef enum TcgLockingUidL {
	TcgLockingGlobalRangeUidL   = 0x00000001,
	TcgLockingRange1UidL        = 0x00030001,
	TcgLockingRange2UidL,
	TcgLockingRange3UidL,
	TcgLockingRange4UidL,
	TcgLockingRange5UidL,
	TcgLockingRange6UidL,
	TcgLockingRange7UidL,
	TcgLockingRange8UidL,
	TcgLockingRange9UidL,
	TcgLockingRange10UidL,
	TcgLockingRange11UidL,
	TcgLockingRange12UidL,
	TcgLockingRange13UidL,
	TcgLockingRange14UidL,
	TcgLockingRange15UidL,
} TcgLockingUidL_t;

typedef enum TcgMBRControlUidL {
	TcgMBRControlUidL = 0x00000001
} TcgMBRControlUidL_t;

typedef enum TcgKeyAes256UidL {
	TcgKeyAes256GlobalRangeUidL = 0x00000001,
	TcgKeyAes256Range1UidL      = 0x00030001,
	TcgKeyAes256Range2UidL,
	TcgKeyAes256Range3UidL,
	TcgKeyAes256Range4UidL,
	TcgKeyAes256Range5UidL,
	TcgKeyAes256Range6UidL,
	TcgKeyAes256Range7UidL,
	TcgKeyAes256Range8UidL,
	TcgKeyAes256Range9UidL,
	TcgKeyAes256Range10UidL,
	TcgKeyAes256Range11UidL,
	TcgKeyAes256Range12UidL,
	TcgKeyAes256Range13UidL,
	TcgKeyAes256Range14UidL,
	TcgKeyAes256Range15UidL,
} TcgKeyAes256UidL_t;



typedef enum TcgTperInfoUidL {
	TcgTperInfoUidL = 0x00030001
} TcgTperInfoUidL_t;

typedef enum TcgTemplateUidL {
	TcgTemplateBaseUidL         = 0x00000001,
	TcgTemplateAdminUidL        = 0x00000002,
	TcgTemplateLockingUidL      = 0x00000006
} TcgTemplateUidL_t;


typedef enum TcgLifeCycle {
	TcgIssuedLifeCycle,
	TcgIssuedDisabledLifeCycle,
	TcgIssuedFrozenLifeCycle,
	TcgIssuedDisabledFrozenLifeCycle,
	TcgIssuedFailedLifeCycle,
	TcgManufacturedInactiveLifeCycle = 8,	//OPAL SSC V2.01
	TcgManufacturedLifeCycle,
	TcgManufacturedDisabledLifeCycle,
	TcgManufacturedFrozenLifeCycle,
	TcgManufacturedDisablesFrozenLifeCycle,
	TcgManufacturedFailedLifeCycle
} TcgLifeCycle_t;

typedef enum TcgDataRemovalMechanism {
	TcgDataRemovalMechanismOverwriteDataErase = 0x00,
	TcgDataRemovalMechanismBlockErase,
	TcgDataRemovalMechanismCryptoErase,
	TcgDataRemovalMechanismUnmap,
	TcgDataRemovalMechanismResetWritePointers,
	TcgDataRemovalMechanismVendorSpecificErase,
} TcgDataRemovalMechanism_t;

typedef enum TcgSpUidL {
	TcgSpAdminUidL              = 0x00000001,
	TcgSpLockingUidL            = 0x00000002,
	TcgSpNullUidL               = 0xFFFFFFFF
} TcgSpUidL_t;


typedef enum TcgAceTableColumn {
	TcgAceTableUidColumn,
	TcgAceTableNameColumn,
	TcgAceTableCommonNameColumn,
	TcgAceTableBooleanExpressionColumn,
	TcgAceTableColumnsColumn
} TcgAceTableColumn_t;

typedef enum TcgAuthorityTableColumn {
	TcgAuthorityTableUidColumn,
	TcgAuthorityTableNameColumn,
	TcgAuthorityTableCommonNameColumn,
	TcgAuthorityTableIsClassColumn,
	TcgAuthorityTableClassColumn,
	TcgAuthorityTableEnableColumn,
	TcgAuthorityTableSecureColumn,
	TcgAuthorityTableHasHandSignColumn,
	TcgAuthorityTablePresetCertificateColumn,
	TcgAuthorityTableOperationColumn,
	TcgAuthorityTableCredentialColumn,
	TcgAuthorityTableResponsesignColumn,
	TcgAuthorityTableResponseexchColumn,
	TcgAuthorityTableClockStartColumn,
	TcgAuthorityTableClockEndColumn,
	TcgAuthorityTableLimitColumn,
	TcgAuthorityUsesColumn,
	TcgAuthorityLogColumn,
	TcgAuthorityLogToColumn
} TcgAuthorityTableColumn_t;

typedef enum TcgCPinColumn {
	TcgCPinUidColumn,
	TcgCPinNameColumn,
	TcgCPinCommonNameColumn,
	TcgCPinPinColumn,
	TcgCPinCharSetColumn,
	TcgCPinTryLimitColumn,
	TcgCPinTriesColumn,
	TcgCPinPersistenceColumn
} TcgCPinColumn_t;

typedef enum TcgDataRemovalMechanismColumn {
	TcgDataRemovalMechanismUidColumn,
	TcgDataRemovalMechanismActiveDataRemovalMechanismColumn,
} TcgDataRemovalMechanismColumn_t;

typedef enum TcgTperInfoColumn {
	TcgTperInfoProgrammaticResetEnableColumn = 0x08
} TcgTperInfoColumn_t;

typedef enum TcgKeyAesColumn {
	TcgKeyAesUidColumn,
	TcgKeyAesNameColumn,
	TcgKeyAesCommonNameColumn,
	TcgKeyAesKeyColumn,
	TcgKeyAesModeColumn
} TcgKeyAesColumn_t;

typedef enum TcgLockingColumn {
	TcgLockingUidColumn,
	TcgLockingNameColumn,
	TcgLockingCommonNameColumn,
	TcgLockingRangeStartColumn,
	TcgLockingRangeLengthColumn,
	TcgLockingReadLockEnabledColumn,
	TcgLockingWriteLockEnabledColumn,
	TcgLockingReadLockedColumn,
	TcgLockingWriteLockedColumn,
	TcgLockingLockOnResetColumn,
	TcgLockingActiveKeyColumn,
	TcgLockingNextKeyColumn,
	TcgLockingReencryptStateColumn,
	TcgLockingReencryptRequestColumn,
	TcgLockingAdvancedKeyModeColumn,
	TcgLockingVerifyModeColumn,
	TcgLockingContonResetColumn,
	TcgLockingLastReencryptLBAColumn,
} TcgLockingColumn_t;

typedef enum TcgMBRControlColumn {
	TcgMBRControlUidColumn,
	TcgMBRControlEnableColumn,
	TcgMBRControlDoneColumn,
	TcgMBRControlDoneOnResetColumn
} TcgMBRControlColumn_t;

typedef enum TcgSecretProtectColumn {
	TcgSecretProtectProtectMechanismColumn = 0x03
} TcgSecretProtectColumn_t;

typedef enum TcgReturn {
	TcgReturnSuccess,
	TcgReturnNoFurtherData,
	TcgReturnEmptyList,
} TcgReturn_t;

typedef enum TcgStatusCode {
	TcgStatusSuccess,
	TcgStatusNotAuthorized,
	TcgStatusObsolete0,
	TcgStatusSpBusy,
	TcgStatusSpFailed,
	TcgStatusSpDisabled,
	TcgStatusSpFrozen,
	TcgStatusNoSessionAvailable,
	TcgStatusUniquenessConflict,
	TcgStatusInsufficientSpace,
	TcgStatusInsufficientRows,
	TcgStatusInvalidMethod,
	TcgStatusInvalidParameter,
	TcgStatusObsolete1,
	TcgStatusObsolete2,
	TcgStatusTperMalfunction,
	TcgStatusTransationFailure,
	TcgStatusResponceOverflow,
	TcgStatusAuthorityLockedOut,
	TcgStatusFail = 0x3F
} TcgStatusCode_t;

typedef enum TcgEncodeString {
	TcgEncodeStartNameString,
	TcgEncodeStartValueString
} TcgEncodeString_t;

typedef enum TcgDataRemovalMechanismUidL {
	TcgDataRemovalMechanismUidL = 0x00000001,
} TcgDataRemovalMechanismUidL_t;

typedef enum TcgSessionManagementFail {
	TcgSessionManagementNoFail,
	TcgSessionManagementInvalidParameterFail,
	TcgSessionManagementNotAuthorizedFail,
	TcgSessionManagementUnknownFail
} TcgSessionManagementFail_t;

typedef enum TcgStartSessionOptionalParameter {
	TcgStartSessionHostChallengeParameter,
	TcgStartSessionHostExchangeAuthorityParameter,
	TcgStartSessionExchangeCertificationParameter,
	TcgStartSessionHostSigningAuthorityParameter,
	TcgStartSessionHostSigningCertificationParameter,
	TcgStartSessionSessionTimeoutParameter,
	TcgStartSessionInitialCreditParameter,
	TcgStartSessionSignedHashParameter
} TcgStartSessionOptionalParameter_t;

typedef enum TcgCellBlock {
	TcgCellBlockTable,
	TcgCellBlockStartRow,
	TcgCellBlockEndRow,
	TcgCellBlockStartColumn,
	TcgCellBlockEndColumn
} TcgCellBlock_t;

typedef enum TcgSetParameter {
	TcgSetWhereParameter,
	TcgSetValueParameter
} TcgSetParameter_t;

typedef enum TcgHostStatusCode {
	TcgHostStatusSuccess = 0x00,
	TcgHostStatusInvalidField = 0x02,
	TcgHostStatusInvalidFieldAndDoNotRetry = 0x03,	//Not in NVME SPEC
	TcgHostStatusCommandSequenceErrorAndDoNotRetry = 0x04,//Not in NVME SPEC
} TcgHostStatusCode_t;

typedef enum TcgBlockedSIDstate {
	TcgSIDNotBlocked,
	TcgSIDBlocked
} TcgBlockedSIDstate_t;

typedef enum TcgIfReceiveCompacketField {
	TcgResponsetoComeNoResponseAvailable = 0x00,
	TcgAllResponseReturnedNoFurtherData = 0x05
} TcgIfReceiveCompacketField_t;

typedef enum TcgMakeSureKeyRangeProperMode {
	TcgMakeSureKeyRangeProperModeGenRangeKeyPath,
	TcgMakeSureKeyRangeProperModeClearRangeKeyPath,
	TcgMakeSureKeyRangeProperModeCheckLockingTable,
} TcgMakeSureKeyRangeProperMode_t;

typedef enum TcgEncryptKekandPasswordWhenInitMode {
	TcgEncryptKekandPasswordWhenInitModeInit,
	TcgEncryptKekandPasswordWhenInitModeRevertAdminSp,
	TcgEncryptKekandPasswordWhenInitModeRevertLockingSp,
	TcgEncryptKekandPasswordWhenInitModeRevertSp,
	TcgEncryptKekandPasswordWhenInitModeReactivate,
	TcgEncryptKekandPasswordWhenInitModeErase,
} TcgEncryptKekandPasswordWhenInitMode_t;

typedef enum TcgEncryptKeyDataMode {
	TcgEncryptKeyDataModeDek,
	TcgEncryptKeyDataModeKek,
	TcgEncryptKeyDataModePassword,
	TcgEncryptKeyDataModePSIDPassword,
} TcgEncryptKeyDataMode_t;

typedef enum TcgDecryptKeyDataMode {
	TcgDecryptKeyDataModeEdek,
	TcgDecryptKeyDataModeEkek,
	TcgDecryptKeyDataModeEpassword,
	TcgDecryptKeyDataModePSIDEpassword,
} TcgDecryptKeyDataMode_t;

typedef enum TcgInitLockingSpMode {
	TcgInitLockingSpModeInit,
	TcgInitLockingSpModeRevert,
	TcgInitLockingSpModeRevertSp,
	TcgInitLockingSpModeReactivate,
} TcgInitLockingSpMode_t;

typedef enum TcgEventDrivelogReason {
	TcgInitDoneLog,
	TcgTimeoutLog,
	TcgEventLog,
	TcgErrorLog,
} TcgEventDrivelogReason_t;

typedef enum TcgSecurityVersionUpdate {
	TcgSecurityVersion0to1 = 0x00, // @JIRA_E13-564
	TcgSecurityVersion1to2 = 0x01, // @JIRA_E13-1064
	TcgSecurityVersion2to3 = 0x02, // @JIRA_E13-1221
	TcgSecurityVersion3to4 = 0x03, // @JIRA_E13-2145
	TcgSecurityVersion4to5 = 0x04, // @JIRA_E13-6639
	TcgSecurityVersion5to6 = 0x05, // @JIRA_E13-7899
} TcgSecurityVersionUpdate_t;

#endif /* _TCG_DEF_H_ */
