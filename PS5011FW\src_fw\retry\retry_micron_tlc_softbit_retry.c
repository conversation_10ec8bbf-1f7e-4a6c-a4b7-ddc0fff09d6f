/****************************************************************************/
//
//  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
//  All rights reserved
//
//  The content of this document is confidential and shall be applied
//  subject to the terms and conditions of the license agreement and
//  other applicable laws. Any unauthorized access, use or disclosure
//  of this document is strictly prohibited and may be punishable
//  under laws.
//
//  retry_micron_tlc_softbit_retry.c
//
//
//
/****************************************************************************/

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "err_hdl_fpl_softbit_retry.h"
#if (((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_MLC)) && (FALSE == RETRY_MICRON_NICKS_ALL_PAGE_TYPE_SB_SUPPORT) && (FALSE == IM_B47R) && (FALSE == IM_B37R))
#include "debug/debug_setup.h"
#include "err_hdl_fpl_sb_retry_api.h"
#include <string.h>
#include "retry.h"
#include "hal/fip/fpu.h"
#include "retry.h"
#include "retry_api.h"
#include "err_hdl_fpl_softbit_retry_table.h"
#include "err_hdl_fpl_RS.h"
#include "hal/db/db_reg.h"
#include "hal/db/db_api.h"
#include "hal/fip/fip_reg.h"
#include "hal/fip/fip_api.h"
#include "debug/debug.h"
#include "hal/dmac/dmac_api.h"
#include "hal/dmac/dmac_pop_cmd.h"
#include "nvme_api/nvme/shr_hal_nvme_vuc_api.h"
#include "retry_hb.h"
#include "err_hdl_fpl_SoftbitRaid.h"

#include "hal/pic/uart/uart_api.h"
#include "retry/stall.h"
#include "retry/patch_cmd.h"
#include "hal/fip/fip.h"
#include "table/vbmap/vbmap_api.h"

/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */

#define SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX	(4)
#define SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH		(4)
#define SB_MICRON_SET_FEATURE_FPU_DATA_P1			(SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX)
#define SB_MICRON_SET_FEATURE_FPU_DATA_P2			(SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (1 << 1))
#define SB_MICRON_SET_FEATURE_FPU_DATA_P3			(SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (2 << 1))
#define SB_MICRON_SET_FEATURE_FPU_DATA_P4			(SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (3 << 1))
#define SB_MICRON_SET_FEATURE_FPU_END_IDX			(SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH << 1))

#define SB_MICRON_GET_FEATURE_FPU_END_IDX			(3)

#define SB_MICRON_CHECK_FEATURE_FPU_START_IDX		(2)
#define SB_MICRON_DETERMING_COARSE_TUNING_CHECK_FEATURE_FPU_PARAM_NUM		(1)

#define SB_MICRON_HB_ARC_MAX_ROUND_CNT				(3)

#define SB_MICRON_DECODE_MODE_LOWER				(2)
#define SB_MICRON_DECODE_MODE_UPPER					(3)
#define SB_MICRON_DECODE_MODE_EXTRA					(4)

#define SB_MICRON_DELTA_TABLE_MINUS_3_SHIFT			(0)
#define SB_MICRON_DELTA_TABLE_MINUS_2_SHIFT			(1)
#define SB_MICRON_DELTA_TABLE_MINUS_1_SHIFT			(2)
#define SB_MICRON_DELTA_TABLE_ORIGIN_SHIFT			(3)
#define SB_MICRON_DELTA_TABLE_PLUS_1_SHIFT			(4)
#define SB_MICRON_DELTA_TABLE_PLUS_2_SHIFT			(5)
#define SB_MICRON_DELTA_TABLE_PLUS_3_SHIFT			(6)
#define SB_MICRON_RETRY_VTH_LENGTH					(7)

#define SB_MICRON_DELTA_TABLE_BIOS_0			(0)
#define SB_MICRON_DELTA_TABLE_BIOS_1			(1)
#define SB_MICRON_DELTA_TABLE_BIOS_2			(2)
#define SB_MICRON_DELTA_TABLE_BIOS_3			(3)

#define SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B1H_INDEX_0		(0)
#define SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B3H_INDEX_0		(1)
#define SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B1H_INDEX_1		(2)
#define SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B4H_INDEX		(3)
#define SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B2H_INDEX_0		(4)
#define SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B3H_INDEX_1		(5)
#define SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B2H_INDEX_1		(6)

#define SB_MICRON_NO_NEED_SET_SECOND_FEATURE_ADDRESS			(0)
#define SB_MICRON_NEED_SET_SECOND_FEATURE_ADDRESS				(1)
#define SB_MICRON_SETTING_SECOND_FEATURE_ADDRESS				(2)

#define SB_MICRON_DEFAULT_LLR_TABLE_START_IDX		(0)

#define SB_MICRON_SET_TARGET_PAGE						(0)
#define SB_MICRON_SET_SHARE_PAGE_WITH_ARC_RESULT	(1)
#define SB_MICRON_NEXT_WORDLINE_Y_SHIFT_B27B		(1)
#define SB_MICRON_NEXT_WORDLINE_Y_SHIFT_B27A		(1)

#define SB_MICRON_NEXT_WORDLINE_Y_SHIFT				(3)

#define SB_MICRON_SPECIFIC_NOT_USING					(0)
#define SB_MICRON_SPECIFIC_RETRY_TABLE				(1)
#define SB_MICRON_SPECIFIC_READ_OFFSET				(2)

#if(TRUE == RETRY_MICRON_NICKS)
#define SB_MICRON_SPECIFIC_RETRY_TABLE_MAX_CNT		(3)		//by decoding flow
#else	/*(TRUE == RETRY_MICRON_NICKS)*/
#define SB_MICRON_SPECIFIC_RETRY_TABLE_MAX_CNT		(4)		//by decoding flow
#endif	/*(TRUE == RETRY_MICRON_NICKS)*/
#define SB_MICRON_SPECIFIC_READ_OFFSET_MAX_CNT		(2)		//by decoding flow

#define SB_MICRON_FEATURE_PARAM_P1_INDEX			(0)
#define SB_MICRON_FEATURE_PARAM_P2_INDEX			(1)
#define SB_MICRON_FEATURE_PARAM_P3_INDEX			(2)
#define SB_MICRON_FEATURE_PARAM_P4_INDEX			(3)
#define SB_MICRON_FEATURE_PARAM_ARC_RESULT			(SB_MICRON_FEATURE_PARAM_P3_INDEX)


/*
 * ---------------------------------------------------------------------------------------------------
 *  data types
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *   extern global variables
 * ---------------------------------------------------------------------------------------------------
 */
RETRY_SB_TASK_STRUCT gSBTask;

/*
 * ---------------------------------------------------------------------------------------------------
 *   static global variables
 * ---------------------------------------------------------------------------------------------------
 */
#if !BURNER_MODE_EN
#if(RETRY_SOFTBITT_FOR_SDK_EN==FALSE)
static SoftBitLLRTable_t gSoftBitDSPLLRTable[4];
//for NCS
static SoftBitLLRTable_t gSoftBitDSPADTLLRTable;
#else /* (RETRY_SOFTBITT_FOR_SDK_EN==FALSE) */

#endif /* (RETRY_SOFTBITT_FOR_SDK_EN==FALSE) */

/*
 * ---------------------------------------------------------------------------------------------------
 *   private prototypes
 * ---------------------------------------------------------------------------------------------------
 */
#if(RETRY_SOFTBITT_FOR_SDK_EN==FALSE)
AOM_RETRY_SB static U8 RetrySBInit(void);
AOM_RETRY_SB static void RetrySBDetermineCoarseTuningLevel(void);
AOM_RETRY_SB static void RetrySBSwitchStateToSetDefaultFeatureReset(void);
AOM_RETRY_SB static void RetrySBSwitchToSetDefaultFeatureThenDoBackupSBFlow(U8 ubBackUpedState, U8 ubBackUpedSubstate);
AOM_RETRY_SB static void RetrySBMTTriggerRetry(U8 ubClearFlag);
AOM_RETRY_SB static void RetrySBInitDSPwithDefaultLLR(U8 ubPageType);
AOM_RETRY_SB static void RetrySBFlashSetECCParam(U32 *data, U32 len, U32 load_target, U8 ubMode);
AOM_RETRY_SB static U8 RetrySBPrepareFreeMT(MT_Callback_Func_t PassCallback, MT_Callback_Func_t FailCallback);
AOM_RETRY_SB static void RetrySBSelecMTFPUTrigger(U16 uwFpu_offset, U32 ulFSA, U8 flh_type_legacy, U8 ubMTIndex, U8 ubMode);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBDetermingCoarseTuningLevelPass_Callback(U8 ubMTIndex);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBDetermingCoarseTuningLevelFail_Callback(U8 ubMTIndex);
AOM_RETRY_SB static void RetrySBReadFlow(void);
AOM_RETRY_SB static void RetrySBFlashLoadECCParam(U32 load_target);
AOM_RETRY_SB static void RetrySBDecodeFlow (void);
AOM_RETRY_SB static void RetrySBModifyReadRetryAddCoarseTuningLevel(void);
AOM_RETRY_SB static void RetrySBSelectDMAMTFPUTrigger( U16 uwFpu_offset, U32 ulFSA, U8 ubMTIndex);
AOM_RETRY_SB static void RetrySBDumpLLRTable(void);
AOM_RETRY_SB static U32 RetrySBFlashGetCurrentLDPCFrameSize(void);

AOM_FLH_ERR_SB_CALLBACK static void RetrySBTriggerCorrectFail_Callback(U8 ubMTIndex);
AOM_RETRY_SB static void RetrySBHBSetFeatureRetryTable(U8 ubTableIdx);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBHBSetFeatureRetryTablePass_Callback(U8 ubMTIndex);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBHBSetFeatureRetryTableFail_Callback(U8 ubMTIndex);
AOM_RETRY_SB static void RetrySBHBSetFeatureARC(U8 ubCalibration, U8 ubPersistence);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBHBSetFeatureARCPass_Callback(U8 ubMTIndex);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBHBSetFeatureARCFail_Callback(U8 ubMTIndex);
AOM_RETRY_SB static U8 RetrySBHBReadwithARC(void);
AOM_RETRY_SB static U8 RetrySBHBReadwithRetryTableOrReadOffset(void);
AOM_RETRY_SB static void RetrySBCaculateShareLowerPage(void);
AOM_RETRY_SB static void RetrySBSetReadLevelOffset(U8 *ubReadOffsetTable);
AOM_RETRY_SB static void RetrySBGetReadLevelOffsetFromARC(void);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBSetReadLevelOffsetPass_Callback(U8 ubMTIndex);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBSetReadLevelOffsetFail_Callback(U8 ubMTIndex);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBGetReadLevelOffsetFromARCPass_Callback(U8 ubMTIndex);
AOM_RETRY_SB static void RetrySBReadHB( U8 page_type, U8 dsp_en, U32 ulFSA, U8 ldpc_frame_idx, U8 src_ibuf, U8 dst_ibuf, U8 ibuf_frame_sel);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBReadHBPass_Callback(U8 ubMTIndex);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBReadHBFail_Callback(U8 ubMTIndex);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBTriggerCorrectPass_Callback(U8 ubMTIndex);
AOM_RETRY_SB static void RetrySBReadSB1( U8 ubPageType, U8 *pubReadRetryTable, U8 ldpc_frame_idx);
AOM_RETRY_SB static void RetrySBReadSB2( U8 ubPageType, U8 *pubReadRetryTable, U8 ldpc_frame_idx);
AOM_RETRY_SB static void RetrySBReadSB3( U8 ubPage_type, U8 ubDSPEable, U8 *pubReadRetryTable, U8 ldpc_frame_idx);
AOM_RETRY_SB static void RetrySBReadSB4( U8 ubPage_type, U8 ubDSPEable, U8 *pubReadRetryTable, U8 ldpc_frame_idx);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBReadSB1Pass_Callback(U8 ubMTIndex);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBReadSB1Fail_Callback(U8 ubMTIndex);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBReadSB2Pass_Callback(U8 ubMTIndex);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBReadSB2Fail_Callback(U8 ubMTIndex);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBReadSB3Pass_Callback(U8 ubMTIndex);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBReadSB3Fail_Callback(U8 ubMTIndex);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBReadSB4Pass_Callback(U8 ubMTIndex);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBReadSB4Fail_Callback(U8 ubMTIndex);
AOM_RETRY_SB static void RetrySBClearSBOffsetFeatures( U8 ubPageType, U8 *pubReadRetryTable);
AOM_RETRY_SB static void RetrySBCorrectMTTrigger( U8 hb_ibuf_ptr, U8 sb_ibuf_ptr, U8 sel_2k, U8 dsp_en, U8 dsp_4k_en, U8 ubdecode_mode, U8 scale_mode, U8 flow_mode, U8 hb_sb, U8 ubMTIndex);
AOM_RETRY_SB static void RetrySBSetFeatureByReadRetryDeltaTableOffset( U8 ubPageType, U8 *pubReadRetryTable, U8 ubBiosValue);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBSetReadRetryDeltaPass_Callback(U8 ubMTIndex);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBSetReadRetryDeltaFail_Callback(U8 ubMTIndex);
AOM_RETRY_SB static void RetrySBDSP2 (void);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBDSP2Fail_Callback(U8 ubMTIndex);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBTriggerCorrectAdaptLLRPass_Callback(U8 ubMTIndex);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBTriggerCorrectAdaptLLRFail_Callback(U8 ubMTIndex);
AOM_RETRY_SB static U8 RetrySBCaculateNextWordLineSharePage(void);
AOM_RETRY_SB static U8 RetrySBRecordNextWLPagesFSA(U8 ubX, U8 ubY, U8 ubSharedPageIdx);
AOM_RETRY_SB void RetrySBupdate_dsp_with_DSP2_llr(U8 ubPageType);
AOM_RETRY_SB static void RetrySBReleaseFinishMT(void);
AOM_RETRY_SB static void RetrySBSetDefaultFeatureAndCheck(void);
AOM_RETRY_SB static void RetrySBResetFlash(void);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBResetFlashPass_Callback(U8 ubMTIndex);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBResetFlashFail_Callback(U8 ubMTIndex);
AOM_RETRY_SB static void RetrySBFlashRetoreLLR(U8 lmu_sel, U8 llr_idx);
AOM_RETRY_SB static U16 RetrySBInvertCoord( U8 ubx, U16 uwY);
AOM_RETRY_SB static void RetrySBFPUTrigger(U16 uwFpu_offset, U8 int_en, U16 vct);
AOM_RETRY_SB static void RetrySBBackupRestore(U64 dram_addr, U32 iram_addr, U8 ibf_ptr, U8 direction, U8 mode, U8 bch_mode, U8 all, U8 frm_msk);
AOM_RETRY_SB static void RetrySBBackupCorrect2k(U8 src_ibf, U8 ldpc_frame_idx, U32 ulbackup_addr, U32 ultmp_addr, U32 Iram_addr, U32 TempIram_addr, U8 ubMode);
AOM_RETRY_SB static void RetrySBBackupRestoreIBUF(U8 ubMTIndex);
AOM_RETRY_SB static U8 RetrySBget_dsp_rank(U8 rank_idx, U8 ubMode8Value);
AOM_RETRY_SB static U8 RetrySBAddAndCheckOverBoundary(U64 uoOperandA, U64 uoOperandB, U64 *puoResult, RetrySBDataBitLength_t BitLength);
AOM_RETRY_SB static void RetrySBProgNCSCMD( U16 uwFpu_offset, U8 io_type, U8 ubMTIndex, U8 ubMode);

#if RETRY_HARDBIT_FOR_SDK_EN
#else /* RETRY_HARDBIT_FOR_SDK_EN */
AOM_RETRY_SB static void RetrySBTrappingSetFlowSetup (U8 ubEnableTrappingSet);
#endif /* RETRY_HARDBIT_FOR_SDK_EN */

#else /* (RETRY_SOFTBITT_FOR_SDK_EN==FALSE) */

#endif /* (RETRY_SOFTBITT_FOR_SDK_EN==FALSE) */

/*
 * ---------------------------------------------------------------------------------------------------
 *   private
 * ---------------------------------------------------------------------------------------------------
 */
#if RETRY_SOFTBITT_FOR_SDK_EN
#else /* RETRY_HARDBIT_FOR_SDK_EN */

U8 RetrySBAddAndCheckOverBoundary(U64 uoOperandA, U64 uoOperandB, U64 *puoResult, RetrySBDataBitLength_t BitLength)
{
	/*
	 * U8 Example
	 *
	 * Negative Over Boundary : Negative + Negative
	 * uoSignBit = 0xFF & 0x80 & BIT7 = BIT7
	 * 1)  0xFF + 0x80 = 0x17F, BIT7 Missing
	 * 2)  0x80 + 0x80 = 0x100, BIT7 Missing
	 *
	 * Positive Over Boundary : Positive + Positive
	 * uoSignBit = 0x7F & 0x01 & BIT7 = 0
	 * 1)  0x7F + 0x01 = 0x80, BIT7 Set
	 * 2)  0x7F + 0x7F = 0xFE, BIT7 Set
	 */
	U64 uoSignBit;

	(*puoResult) = (uoOperandA + uoOperandB) & BIT_MASK(BitLength);

	// A, B have different sign bit, must be safe
	if ((uoOperandA ^ uoOperandB) & BIT(BitLength - 1)) {
		return RETRY_SB_NO_OVER_BOUNDARY;
	}

	uoSignBit = uoOperandA & uoOperandB & BIT(BitLength - 1);

	// If sign bit of result has "NOT" changed
	if (0 == (((*puoResult) & BIT(BitLength - 1)) ^ uoSignBit)) {
		return RETRY_SB_NO_OVER_BOUNDARY;
	}

	if (uoSignBit) {
		(*puoResult) = BIT(BitLength - 1);  // Set to minimum negative value
		return RETRY_SB_OVER_NEGATIVE_BOUNDARY;
	}
	else {
		(*puoResult) = BIT_MASK(BitLength - 1);  // Set to maximum positive value
		return RETRY_SB_OVER_POSITIVE_BOUNDARY;
	}
}

void RetrySBSwitchStateToSetDefaultFeatureReset(void)
{
	//This should be the only Function that set "gSBTask.ubBackUpedState = RETRY_SB_STATE_SB_RESET_FLH"
	if (RETRY_SB_STATE_SB_RESET_FLH == gSBTask.ubBackUpedState) {		//when ubBackUpedState == RETRY_SB_STATE_SB_RESET_FLH mean`s Set Default Feature is Fail, Need  END SB flow directly
		//Reset FLH , Leave SB retry
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
		gSBTask.ubState = RETRY_SB_STATE_SB_RESET_FLH;
		gSBTask.ubBackUpedState = RETRY_SB_STATE_DONE;
	}
	else {
		gSBTask.ubState = RETRY_SB_STATE_SET_DEFAULT_FEATURE_AND_CHK;
		gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_INIT;
		gSBTask.ubBackUpedState = RETRY_SB_STATE_SB_RESET_FLH;
		gSBTask.ubBackUpedSubstate = RETRY_SB_SUBSTATE_NULL;
	}
}

void RetrySBSwitchToSetDefaultFeatureThenDoBackupSBFlow(U8 ubBackUpedState, U8 ubBackUpedSubstate)
{
	gSBTask.ubState = RETRY_SB_STATE_SET_DEFAULT_FEATURE_AND_CHK;
	gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_INIT;
	gSBTask.ubBackUpedState = ubBackUpedState;
	gSBTask.ubBackUpedSubstate = ubBackUpedSubstate;
}

void RetrySBMTTriggerRetry(U8 ubClearFlag)
{
	if (gSBTask.ubMTFailCnt < RETRY_SB_MT_FAIL_RETRY_CNT_MAX) {
		gSBTask.ubMTFailCnt++;
	}
	else {
		gSBTask.ubState = RETRY_SB_STATE_SB_RESET_FLH;
		/*
			Normal flow MT Fail(Case A/B)	->	RETRY_SB_STATE_SB_RESET_FLH, BackupState = RETRY_SB_STATE_TERMINATE_FAIL, terminate Case A/B after Reset
			Normal flow MT Fail(Case C)		->	RETRY_SB_STATE_SB_RESET_FLH, BackupState = RETRY_SB_STATE_CHECK_NEXT_STATE, terminate Case C and move to next 2K_Frame after Reset

			SetDefaultFeature MT Fail when leaving SB flow		->	RETRY_SB_STATE_SB_RESET_FLH, BackupState = RETRY_SB_STATE_DONE, Leave SB after Reset

			Reset MT Fail		->	RETRY_SB_STATE_DONE, Leave SB directly
		*/
		if ((RETRY_SB_STATE_DONE == gSBTask.ubBackUpedState)
			|| (RETRY_SB_STATE_INIT == gSBTask.ubBackUpedState)) {
			gSBTask.ubState = RETRY_SB_STATE_DONE;
		}
		else {
			if (RETRY_SB_STATE_SB_RESET_FLH == gSBTask.ubBackUpedState) {		//SetDefaultFeature MT Fail when leaving SB flow
				gSBTask.ubBackUpedState = RETRY_SB_STATE_DONE;
			}
			else {															// Normal flow MT Fail
				if (RETRY_SB_STATE_CHECK_NEXT_STATE != gSBTask.ubBackUpedState) {
					gSBTask.ubBackUpedState = RETRY_SB_STATE_TERMINATE_FAIL;
				}
			}
		}
		if (TRUE == ubClearFlag) {
			//Clear Flag for second FA
			gSBTask.ubSecondFeatureAddr = SB_MICRON_NO_NEED_SET_SECOND_FEATURE_ADDRESS;
		}
	}
}

void RetrySBInitDSPwithDefaultLLR(U8 ubPageType)
{
	R32_FCON[ R32_FCON_LDPC_CFG ] &= ~((PAGE_SELECT_MASK) << PAGE_SELECT_SHIFT);
	R32_FCON[ R32_FCON_LDPC_CFG ] |=  (( ubPageType & PAGE_SELECT_MASK) << PAGE_SELECT_SHIFT);

	RetrySBFlashSetECCParam(&sb_llr_table[0][ubPageType][0], DEFAULT_LLR_TABLE_LENGTH, LLR_TABLE0_LOAD_BIT, NORMAL_MODE);

	/* *Init DSP */
	RetrySBFlashSetECCParam(&sb_dsp_graycode[ubPageType][0], DSP_GRAYCODE_LENGTH, DSP_GRAY_CODE_LOAD_BIT, NORMAL_MODE);
	switch (gpOtherInfo->ubProcess) {
	case RETRY_MICRON_FLASH_PROCESS_B16A:
	case RETRY_MICRON_FLASH_PROCESS_B17A:
	case RETRY_MICRON_FLASH_PROCESS_B27A:
	case RETRY_MICRON_FLASH_PROCESS_B27B:
	case RETRY_MICRON_FLASH_PROCESS_N18A:
		RetrySBFlashSetECCParam(&sb_dsp_lut[ubPageType][0], DSP_LUT_LENGTH, DSP_LUT_LOAD_BIT, NORMAL_MODE);
		break;
	default:
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
		RetrySBSwitchStateToSetDefaultFeatureReset();
	}
	RetrySBFlashSetECCParam(&sb_dsp_param[ubPageType][0], DSP_PARAM_LENGTH, DSP_PARAM_LOAD_BIT, NORMAL_MODE);
}

void RetrySBFlashSetECCParam(U32 *data, U32 len, U32 load_target, U8 ubMode)
{
	U32 idx;

	for (idx = 0; idx < len; idx++) {
		/* Select LLR table */
		R32_FCON[R32_FCON_ECC_PARAM_CFG] &= (~(ECC_PARAM_SEL_MASK << ECC_PARAM_SEL_SHIFT ));
		R32_FCON[R32_FCON_ECC_PARAM_CFG] |= (idx << ECC_PARAM_SEL_SHIFT);

		/* Fill LLR data */
		if (ubMode == NORMAL_MODE) {
			R32_FCON[R32_FCON_ECC_PARAM] = data[idx];
		}
		else {
			R32_FCON[R32_FCON_ECC_PARAM] = data[len - 1 - idx];
		}
	}

	RetrySBFlashLoadECCParam(load_target);
}

U8 RetrySBInit()
{
	U8 ubch, ublun, ubIdx, ubBank;
	U8 ubalu = 0;
	U8 ubi = 0;

	/*
	 * should given outside :
	 *
	 * pre-function : Retry_Create_SB_Task()
	 * post-function : Retry_Clear_SB_Task()
	 *
	 */

	///copy delta table :
	switch (gpOtherInfo->ubProcess) {
	case RETRY_MICRON_FLASH_PROCESS_B16A:
	case RETRY_MICRON_FLASH_PROCESS_B17A:
	case RETRY_MICRON_FLASH_PROCESS_B27A:
	case RETRY_MICRON_FLASH_PROCESS_B27B:
	case RETRY_MICRON_FLASH_PROCESS_N18A:
		gSBTask.ubllr_index_MAX = SB_MICRON_MAX_GENERAL_LLR_TABLE_CNT;
		memcpy((void *)default_delta_table, (void *)sb_delta_table, sizeof(sb_delta_table));
		break;
	default:
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
		RetrySBSwitchStateToSetDefaultFeatureReset();
	}

	gSBTask.ulCount_one[0] = 0;
	gSBTask.ulCount_one[1] = 0;
	gSBTask.ulCount_one[2] = 0;

	//Micron SB Todo
	//FSA translation
	ubalu = gSBTask.MTTemplate.dma.ALUSelect;
	ubch = (gSBTask.MTTemplate.dma.uliFSA0_1 >> gpOtherInfo->pRuleSet->pChannel->ubShift[ubalu]) & gpOtherInfo->pRuleSet->pChannel->ulMask;
	ublun = ((gSBTask.MTTemplate.dma.uliFSA0_1 >> gpOtherInfo->pRuleSet->pLun->ubShift[ubalu]) & gpOtherInfo->pRuleSet->pLun->ulMask) << gpOtherInfo->pRuleSet->pDie_IL->ubBit_No;
	ublun |= (gSBTask.MTTemplate.dma.uliFSA0_1 >> gpOtherInfo->pRuleSet->pDie_IL->ubShift[ubalu]) & gpOtherInfo->pRuleSet->pDie_IL->ulMask;
	ubBank = (gSBTask.MTTemplate.dma.uliFSA0_1 >> gpOtherInfo->pRuleSet->pBank->ubShift[ubalu]) & gpOtherInfo->pRuleSet->pBank->ulMask;

	gSBTask.ubChannel = ubch;
	gSBTask.ublun = ublun;
	gSBTask.ubBank = ubBank;
	gSBTask.ubCurr_frm_idx = 0;
	gSBTask.ubSBDecodeFlowLoopBreakFlag = 0;

	/// now only use ErrFram, so LDPC frm set 0xFFFF to bypass
	if (RETRY_READ_SB__DIRECTLY_GET_CORRECT_DATA_FROM_ERROR_PAGE) {
		gSBTask.uwLDPCCurrentErrMap = 0;
		for (ubIdx = 0 ; ubIdx < gub4kEntrysPerPlane ; ubIdx++) {
			if (gSBTask.ubErr_frm & (BIT0 << ubIdx)) {
				gSBTask.uwLDPCCurrentErrMap |=  ((BIT0 | BIT1) << (ubIdx << 1) );
			}
		}
	}
	else {
		gSBTask.uwLDPCCurrentErrMap = 0xFFFF;
	}

	/* enable dsp engine */
	M_FIP_CLEAR_LDPC_DSP_EN();
	M_FIP_SET_LDPC_DSP_EN();
	/* set page select */
	M_FIP_SET_LDPC_CONFIG_PAGE_SELECT(gSBTask.ubPageType);
	/* determine decode mode */

	/* fill table which need to be used*/
	RetrySBInitDSPwithDefaultLLR(gSBTask.ubPageType);

	/* enable dsp auto upload llr table after dsp calculate */
	M_FIP_SET_DSP_UPDATE_LLR_TABLE_EN();
#if RETRY_HARDBIT_FOR_SDK_EN
#else /* RETRY_HARDBIT_FOR_SDK_EN */
	/// trapping set cnt = 0
	gSBTask.ubTrappingSetCnt = 0;
#endif /* RETRY_HARDBIT_FOR_SDK_EN */
	gSBTask.ubARCRoundCnt = 0;
	gSBTask.ubSecondFeatureAddr = 0;
	gSBTask.ubTempPageType = 0;
	for (ubi = 0 ; ubi < 2 ; ubi++) {
		gSBTask.NextWordLineSharePage[ubi].ubSharePageType = 0;
		gSBTask.NextWordLineSharePage[ubi].ulSharePageFSA = 0;
	}
	gSBTask.ubSpecificMode = SB_MICRON_SPECIFIC_NOT_USING;
	gSBTask.ubSpecificRetryTable = 0;
	gSBTask.ubSpecificRetryTableRound = 0;
	gSBTask.ubSpecificReadOffsetRound = 0;
#if(TRUE == RETRY_MICRON_NICKS)
	gSBTask.ubSBReadDecodeWithARC = 0;
	gSBTask.ubErrorRecoverySBStep = 0;
	gSBTask.ubBlkRefresh = 0;
#endif	/*(TRUE == RETRY_MICRON_NICKS)*/
	gSBTask.ubDeltaTablePassReadLevelOffset = 0;
	gSBTask.ubDeltaTableCoarseTuningReadLevelOffset = 0;
	gSBTask.ubCoarseTuningReadLevel = 0;
	gSBTask.ul16KCntOneInSBCoarseTuning	= 0;
	return TRUE;
}

void RetrySBDetermineCoarseTuningLevel(void)
{
	U16 *puwfpu_ptr = NULL;
	U8 ubfpu_idx, ubIndex, ubSign, ubNonZeroSignal;
	U16 uwFpu_offset;
	U8 ubMTIndex;
	U32 ulDeltaN;
	//S8 sbDeltaTableValue1;
	//S16 swDeltaTableValue1;
	U8 ubTriggerMTLowClockMode = FALSE;
	U8 ubi = 0;
	U8 ubCoarseTuningShiftOffset = 0;
	//U8 ubCheckFeatureParamNum = 0;
	//NCS Debug
	U16 *puwNCSDebug = NULL;
	U32 *pulNCSDebug = NULL;
	U32 ulCntOneResultTemp;

	U64 uoAddResult = 0;

	switch (gSBTask.ubSubstate) {
	case RETRY_SB_SUBSTATE_DET_COARSE_LVL__INIT:
		if (gSBTask.ubChannel  >= 4) {
			RetrySBSwitchStateToSetDefaultFeatureReset();
			break;
		}

		//clear to default value
		for (ubfpu_idx = 0; ubfpu_idx < (sizeof (delta_table) / 7); ubfpu_idx++) {
			default_delta_table[3][ubfpu_idx] = 0;
		}

		//Micron SB Todo
		//Find Lower page for Upper/Extra Pages in the share page group
		RetrySBCaculateShareLowerPage();

		gubCnt1Result[RETRY_SB_LOWER_PAGE][CNT1_RESULT_ARRAY_PARM_CNT] = 0;
		gubCnt1Result[RETRY_SB_LOWER_PAGE][CNT1_RESULT_ARRAY_FRAME_CNT] = 0;
		gubCnt1Result[RETRY_SB_LOWER_PAGE][CNT1_RESULT_ARRAY_ROUND_CNT] = 0;
		gubCnt1Result[RETRY_SB_LOWER_PAGE][CNT1_RESULT_SKIP_BIT_CNT] = 0;

		gSBTask.ubSubstate = RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_DISABLE_ARC;
	/* no break */

	case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_DISABLE_ARC:
	case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_SET_DEFAULT_RETRY_TABLE:
	case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_SET_READ_LEVEL:
		ubTriggerMTLowClockMode = TRUE;
		uwFpu_offset = FPU_PTR_OFFSET(fpu_set_feature);		//set_feature
		puwfpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);
		/// reset fpu entry
		for (ubi = 0 ; ubi < SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH ; ubi++) {
			if (0 == ubi) {
				puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (ubi << 1)] = FPU_DAT_W_FIR_D(0x00);
			}
			else if ((SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH - 1) == ubi) {
				puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (ubi << 1)] = FPU_DAT_W_LAST_D(0x00);
			}
			else {
				puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (ubi << 1)] = FPU_DAT_W_MID_D(0x00);
			}
			puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (ubi << 1) + 1] = FPU_DAT_W(0x00);
		}
		puwfpu_ptr[0] = FPU_CMD(0xD5);
		puwfpu_ptr[1] = FPU_ADR_1B(gSBTask.ublun);
		if (FIP_FLASH_CLOCK_400MHZ == gFlhEnv.ubTargetFlashClock) {
			// flh clk 400
			puwfpu_ptr[3] = FPU_DLY(0x15);		// Decimal 21
		}
		else if (FIP_FLASH_CLOCK_333MHZ == gFlhEnv.ubTargetFlashClock) {
			// flh clk 333
			puwfpu_ptr[3] = FPU_DLY(0xF);		// Decimal 15
		}
		else {
			puwfpu_ptr[3] = FPU_DLY(0x10);		// Decimal 16
		}

		switch (gSBTask.ubSubstate) {
		case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_DISABLE_ARC:
			puwfpu_ptr[2] = FPU_ADR_1B(0x96);				//FA 96h for ARC feature
			break;
		case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_SET_DEFAULT_RETRY_TABLE:
			puwfpu_ptr[2] = FPU_ADR_1B(0x89);
			break;
		case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_SET_READ_LEVEL:
			puwfpu_ptr[2] = FPU_ADR_1B(0xA8);					//only need set Lower page read offset
			//Upper/Extra page use share Lower page to detrmine coarse tuning level
			puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P1] = FPU_DAT_W_FIR_D(default_delta_table[3][3]);
			puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P1 + 1] = FPU_DAT_W(default_delta_table[3][3]);
			break;
		default:
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
			break;
		}
		//Micron SB Todo
		puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_END_IDX] = FPU_END;
		break;

	case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_GET_ARC:
	case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_GET_RETRY_TABLE:
	case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_GET_READ_LEVEL:
		ubTriggerMTLowClockMode = TRUE;
		uwFpu_offset = FPU_PTR_OFFSET(fpu_get_feature);		//get_feature
		puwfpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);

		puwfpu_ptr[0] = FPU_CMD(0xD4);
		puwfpu_ptr[1] = FPU_ADR_1B(gSBTask.ublun);

		switch (gSBTask.ubSubstate) {
		case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_GET_ARC:
			puwfpu_ptr[2] = FPU_ADR_1B(0x96);
			break;
		case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_GET_RETRY_TABLE:
			puwfpu_ptr[2] = FPU_ADR_1B(0x89);
			break;
		case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_GET_READ_LEVEL:
			puwfpu_ptr[2] = FPU_ADR_1B(0xA8);
			break;
		default:
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
			break;
		}
		puwfpu_ptr[SB_MICRON_GET_FEATURE_FPU_END_IDX] = FPU_END;
		break;

	case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_CHECK_ARC:
	case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_CHECK_RETRY_TABLE:
	case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_CHECK_READ_LEVEL:
		ubTriggerMTLowClockMode = TRUE;
		uwFpu_offset = FPU_PTR_OFFSET(fpu_read_and_compare_feature_data);		//check_feature
		puwfpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);

		puwfpu_ptr[0] = FPU_CMD(0x00);
		puwfpu_ptr[1] = FPU_DLY(0x10);

		for (ubi = 0; ubi < PARAMETER_NUM_PER_FPU; ubi++) {		//Clear <P1><P2><P3><P4> compare mask
			puwfpu_ptr[SB_MICRON_CHECK_FEATURE_FPU_START_IDX + (ubi << 1)] =  FPU_DAT_R_CMP(0x00);
			puwfpu_ptr[(SB_MICRON_CHECK_FEATURE_FPU_START_IDX + 1) + (ubi << 1)] = FPU_DAT_R_MASK(0x00);
		}

		switch (gSBTask.ubSubstate) {
		case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_CHECK_ARC:
		case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_CHECK_RETRY_TABLE:
			//Compare <P1>
			puwfpu_ptr[SB_MICRON_CHECK_FEATURE_FPU_START_IDX] =  FPU_DAT_R_CMP(0x00);
			puwfpu_ptr[(SB_MICRON_CHECK_FEATURE_FPU_START_IDX + 1)] = FPU_DAT_R_MASK(0xFF);
			break;
		case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_CHECK_READ_LEVEL:
			//Compare <P1>
			puwfpu_ptr[SB_MICRON_CHECK_FEATURE_FPU_START_IDX] = FPU_DAT_R_CMP(default_delta_table[3][3]);
			puwfpu_ptr[(SB_MICRON_CHECK_FEATURE_FPU_START_IDX + 1)] = FPU_DAT_R_MASK(0xFF);
			break;
		default:
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
			break;
		}
		break;

	case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_READ_CNTONE_OP:
		// enable count one function
		M_FIP_CHANNEL_COUNTONE_EN(gSBTask.ubChannel);

		//========== SB retry FPU form ===============
		//Micron SB Todo
		//fpu_micron_read
		uwFpu_offset = FPU_PTR_TLC_1P_READ_CMD;
		puwfpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);
		//Micron SB Todo
		//Count one caculate
		for (ubIndex = gubCnt1Result[RETRY_SB_LOWER_PAGE][CNT1_RESULT_ARRAY_PARM_CNT] ; ubIndex < gubCnt1Result[RETRY_SB_LOWER_PAGE][CNT1_RESULT_ARRAY_PARM_LENGTH]; ubIndex++) {
			if (gubCnt1Result[RETRY_SB_LOWER_PAGE][CNT1_RESULT_SKIP_BIT_CNT] & BIT(ubIndex)) {
				gubCnt1Result[RETRY_SB_LOWER_PAGE][CNT1_RESULT_ARRAY_PARM_CNT]++;
			}
			else {
				ubIndex = gubCnt1Result[RETRY_SB_LOWER_PAGE][CNT1_RESULT_ARRAY_PARM_LENGTH];
			}
		}

		if (gubCnt1Result[RETRY_SB_LOWER_PAGE][CNT1_RESULT_ARRAY_PARM_CNT] == gubCnt1Result[RETRY_SB_LOWER_PAGE][CNT1_RESULT_ARRAY_PARM_LENGTH]) {
			gubCnt1Result[RETRY_SB_LOWER_PAGE][CNT1_RESULT_ARRAY_PARM_CNT]  = 0;
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_CHK_COARSE_TABLE;
			break;
		}

		//Micron SB Todo
		//Count1 read fpu sequence
		ubMTIndex = RetrySBPrepareFreeMT(RetrySBDetermingCoarseTuningLevelPass_Callback, RetrySBDetermingCoarseTuningLevelFail_Callback);
		RetrySBSelecMTFPUTrigger( uwFpu_offset, (U32)( (gSBTask.ulShareLowerFSA_Align) + (gSBTask.ubCurr_frm_idx) ), 0, ubMTIndex, SWITCH_CLK__NORMAL_MODE);

		if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
			gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
		}
		else {
			gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
			gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
		}
		break;

	case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_READ_CNTONE_DMA:
		//Micron SB Todo
		//fpu_micron_raw_dma
		uwFpu_offset = FPU_PTR_OFFSET(fpu_raw_dma_read);
		puwfpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);
		puwfpu_ptr[7] = FPU_DMA_R;
		ubMTIndex = RetrySBPrepareFreeMT(RetrySBDetermingCoarseTuningLevelPass_Callback, RetrySBDetermingCoarseTuningLevelFail_Callback);
		RetrySBSelectDMAMTFPUTrigger( uwFpu_offset, (U32)( (gSBTask.ulShareLowerFSA_Align) + (gSBTask.ubCurr_frm_idx) ), ubMTIndex);
		if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
			gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
		}
		else {
			gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
			gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
		}
		break;
	case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_CHK_COARSE_TABLE:
		ubNonZeroSignal = FALSE;
		for (ubIndex = 0 ; ubIndex <  gubCnt1Result[RETRY_SB_LOWER_PAGE][CNT1_RESULT_ARRAY_PARM_LENGTH]; ubIndex++) {

			if (gubCnt1Result[RETRY_SB_LOWER_PAGE][CNT1_RESULT_SKIP_BIT_CNT] & BIT(ubIndex)) {
				continue;
			}

			if (ID_INTEL == gpOtherInfo->ubMakerCode) {
				// calculate DeltaN
				ulDeltaN = gSBTask.ulCount_one[ubIndex] - (RETRY_SB_COARSE_TABLE_MAGIC_NUMBER * RETRY_SB_COARSE_TABLE_MAGIC_NUMBER_MULTIPLIER);	//gSBTask.ulCount_one[ubIndex] init value is 0, but will be update by Callback functions (RetrySBDetermingCoarseTuningLevelPass_Callback, RetrySBDetermingCoarseTuningLevelFail_Callback)
			}
			else {
				// calculate DeltaN
				ulDeltaN = RETRY_SB_COARSE_TABLE_MAGIC_NUMBER * RETRY_SB_COARSE_TABLE_MAGIC_NUMBER_MULTIPLIER - gSBTask.ulCount_one[ubIndex];	//gSBTask.ulCount_one[ubIndex] init value is 0, but will be update by Callback functions (RetrySBDetermingCoarseTuningLevelPass_Callback, RetrySBDetermingCoarseTuningLevelFail_Callback)
			}

			//SB MICRON Check Point 0729
			M_UART(ERROR_SOFTBIT_, "\n ulDeltaN_A %x ", ulDeltaN);

			/// check condition
			if (ulDeltaN & BIT(31)) {
				ubSign = 1;
				ulDeltaN = BIT(31) - (ulDeltaN & BIT_MASK(31));
			}
			else {
				ubSign = 0;
			}

			ulCntOneResultTemp = gSBTask.ulCount_one[ubIndex];

			//Record Countone Result for DriveLog Debug Info
			if (0 == ubIndex) {	//Only Record First Time Count One for origin UECC situation
				gSBTask.ul16KCntOneInSBCoarseTuning = ulCntOneResultTemp;
			}

			//SB MICRON Check Point 0729
			M_UART(ERROR_SOFTBIT_, "\n ulDeltaN_B %x ", ulDeltaN);
			M_UART(ERROR_SOFTBIT_, "\n ulCntOneResultTemp: %x, gSBTask.ulCount_one[ubIndex], %d, %x ", ulCntOneResultTemp, ubIndex, gSBTask.ulCount_one[ubIndex]);
			M_UART(ERROR_SOFTBIT_, "\n gubCnt1Result[RETRY_SB_LOWER_PAGE][ubIndex] :%x", gubCnt1Result[RETRY_SB_LOWER_PAGE][ubIndex]);
			M_UART(ERROR_SOFTBIT_, "\n guwCoarse_Table %x ", (guwCoarse_Table[ gubCnt1Result[RETRY_SB_LOWER_PAGE][ubIndex] ][COARSE_LEVEL_THRESHOLD_1_IDX]));

			if (ID_INTEL == gpOtherInfo->ubMakerCode) {	//for Intel(UNIC) Flash
				// Intel(UNIC) B16A
				//Caclulate Absolute value Vth_Shift of, record sign of result
				U64 CountOneDeltaNTemp = (U64)ulDeltaN;	//Need U64 to Calculate Countone, Delat_N * Coefficient(shift for no float) will over U32

				if (0 == ubSign) {		//Positive
					ubSign = 1;
					if (ulDeltaN >= guwCount1DiffDeltaNThreshold[RETRY_SB_COUNTONE_DIFF_DELTA_N_THRESHOLD_POSITIVE_IDX_0]) {
						CountOneDeltaNTemp = (CountOneDeltaNTemp * gulCoarseTuningCoefficient[RETRY_SB_UNIC_COUNTONE_CASE_0]) + gulCoarseTuningConstant[RETRY_SB_UNIC_COUNTONE_CASE_0];
					}
					else if (( ulDeltaN < guwCount1DiffDeltaNThreshold[RETRY_SB_COUNTONE_DIFF_DELTA_N_THRESHOLD_POSITIVE_IDX_0])
						&& (ulDeltaN >= guwCount1DiffDeltaNThreshold[RETRY_SB_COUNTONE_DIFF_DELTA_N_THRESHOLD_POSITIVE_IDX_1])) {
						CountOneDeltaNTemp = (CountOneDeltaNTemp * gulCoarseTuningCoefficient[RETRY_SB_UNIC_COUNTONE_CASE_1]) + gulCoarseTuningConstant[RETRY_SB_UNIC_COUNTONE_CASE_1];
					}
					else {
						CountOneDeltaNTemp = (CountOneDeltaNTemp * gulCoarseTuningCoefficient[RETRY_SB_UNIC_COUNTONE_CASE_2]);
						if (CountOneDeltaNTemp >= gulCoarseTuningConstant[RETRY_SB_UNIC_COUNTONE_CASE_2]) {
							CountOneDeltaNTemp = CountOneDeltaNTemp - gulCoarseTuningConstant[RETRY_SB_UNIC_COUNTONE_CASE_2];
						}
						else {
							CountOneDeltaNTemp = gulCoarseTuningConstant[RETRY_SB_UNIC_COUNTONE_CASE_2] - CountOneDeltaNTemp;
							ubSign = 0;
						}
					}
				}
				else {	//Negative
					ubSign = 0;
					if (ulDeltaN > guwCount1DiffDeltaNThreshold[RETRY_SB_COUNTONE_DIFF_DELTA_N_THRESHOLD_NEGATIVE_IDX_0]) {
						CountOneDeltaNTemp = (CountOneDeltaNTemp * gulCoarseTuningCoefficient[RETRY_SB_UNIC_COUNTONE_CASE_4]) + gulCoarseTuningConstant[RETRY_SB_UNIC_COUNTONE_CASE_4];
					}
					else if ((ulDeltaN < guwCount1DiffDeltaNThreshold[RETRY_SB_COUNTONE_DIFF_DELTA_N_THRESHOLD_NEGATIVE_IDX_0])
						&& (ulDeltaN >= guwCount1DiffDeltaNThreshold[RETRY_SB_COUNTONE_DIFF_DELTA_N_THRESHOLD_NEGATIVE_IDX_1])) {
						CountOneDeltaNTemp = (CountOneDeltaNTemp * gulCoarseTuningCoefficient[RETRY_SB_UNIC_COUNTONE_CASE_3]) + gulCoarseTuningConstant[RETRY_SB_UNIC_COUNTONE_CASE_3];
					}
					else {
						CountOneDeltaNTemp = (CountOneDeltaNTemp * gulCoarseTuningCoefficient[RETRY_SB_UNIC_COUNTONE_CASE_2]) + gulCoarseTuningConstant[RETRY_SB_UNIC_COUNTONE_CASE_2];
					}
				}

				//Recovery Shifting for floating value
				gSBTask.ulCount_one[ubIndex] = (U32)(CountOneDeltaNTemp / RETRY_SB_COARSE_TUNING_COFFICEINT_CONSTANT_FLOAT_SHIFT);
			}
			else {	// Micron
				if (ulDeltaN <= (U32)(guwCoarse_Table[ gubCnt1Result[RETRY_SB_LOWER_PAGE][ubIndex] ][COARSE_LEVEL_THRESHOLD_1_IDX])) {
					/// Here result should be Vth_Shift, to reduce memory using so use ulCount_one[]
					gSBTask.ulCount_one[ubIndex]  =  ulDeltaN / guwCoarse_Table[ gubCnt1Result[RETRY_SB_LOWER_PAGE][ubIndex] ][COARSE_LEVEL_DIVISOR_1_IDX];
				}
				else if (   (ulDeltaN > (U32)(guwCoarse_Table[ gubCnt1Result[RETRY_SB_LOWER_PAGE][ubIndex] ][COARSE_LEVEL_THRESHOLD_1_IDX]))   /* parasoft-suppress BD-PB-CC "This line won`t be always true. ulDeltaN can be small when Callback functions (RetrySBDetermingCoarseTuningLevelPass_Callback, RetrySBDetermingCoarseTuningLevelFail_Callback) update gSBTask.ulCount_one[ubIndex]"*/        \
					&&                                                                                                                                                                            \
					(ulDeltaN <= (U32)(guwCoarse_Table[ gubCnt1Result[RETRY_SB_LOWER_PAGE][ubIndex] ][COARSE_LEVEL_THRESHOLD_2_IDX]))         \
				)                                                                                                                                                                                    \
				{
					/// Here result should be Vth_Shift, to reduce memory using so use ulCount_one[]
					gSBTask.ulCount_one[ubIndex]  =  ulDeltaN / guwCoarse_Table[ gubCnt1Result[RETRY_SB_LOWER_PAGE][ubIndex] ][COARSE_LEVEL_DIVISOR_2_IDX];
				}
				else if (ulDeltaN > (U32)(guwCoarse_Table[ gubCnt1Result[RETRY_SB_LOWER_PAGE][ubIndex] ][COARSE_LEVEL_THRESHOLD_2_IDX])) {			/* parasoft-suppress BD-PB-CC "This line won`t be always true. ulDeltaN can be small when Callback functions (RetrySBDetermingCoarseTuningLevelPass_Callback, RetrySBDetermingCoarseTuningLevelFail_Callback) update gSBTask.ulCount_one[ubIndex]"*/
					/// Here result should be Vth_Shift, to reduce memory using so use ulCount_one[]
					gSBTask.ulCount_one[ubIndex]  =  ulDeltaN / guwCoarse_Table[ gubCnt1Result[RETRY_SB_LOWER_PAGE][ubIndex] ][COARSE_LEVEL_DIVISOR_3_IDX];
				}
			}

			//Recovery Sign bit
			if (ubSign) {
				gSBTask.ulCount_one[ubIndex] = BIT(31) - (gSBTask.ulCount_one[ubIndex] & BIT_MASK(31));
			}

			if ((gSBTask.ulCount_one[ubIndex] & 0xFF) == 0) {
				gubCnt1Result[RETRY_SB_LOWER_PAGE][CNT1_RESULT_SKIP_BIT_CNT] |= BIT(ubIndex);
			}
			else {
				ubNonZeroSignal = TRUE;
			}

			//SB MICRON Check Point 0729
			M_UART(ERROR_SOFTBIT_, "\n gSBTask.ulCount_one[ubIndex](Vth_shift) %x, ubNonZeroSignal_A %d ", gSBTask.ulCount_one[ubIndex], ubNonZeroSignal);

			////
			if (NCS_EN) {
				uwFpu_offset = ( FPU_NCS_OFF );
				puwfpu_ptr = (U16 *)(IRAM_BASE + FPU_NCS_OFF);

				//R0~R2
				if ((gubCnt1Result[RETRY_SB_LOWER_PAGE][CNT1_RESULT_ARRAY_ROUND_CNT]) == 3) {
					puwfpu_ptr[4] = FPU_ADR_1B(0x57);
				}
				else if ((gubCnt1Result[RETRY_SB_LOWER_PAGE][CNT1_RESULT_ARRAY_ROUND_CNT]) == 4) {
					puwfpu_ptr[4] = FPU_ADR_1B(0x67);
				}
				else {
					puwfpu_ptr[4] = FPU_ADR_1B(0x50 + ((gubCnt1Result[RETRY_SB_LOWER_PAGE][CNT1_RESULT_ARRAY_ROUND_CNT]) * 0x10));
				}
				puwfpu_ptr[5] = FPU_ADR_1B(0x06);
				puwfpu_ptr[6] = FPU_ADR_1B(0x00);

				/// cnt 1 value
				pulNCSDebug = (U32 *)gulVUCAddr;
				*pulNCSDebug = ulCntOneResultTemp;

				/// vth tracking
				puwNCSDebug = (U16 *)(gulVUCAddr + 4);
				*puwNCSDebug = gSBTask.ulCount_one[ubIndex];

				ubMTIndex = RetrySBPrepareFreeMT(NULL, NULL);
				RetrySBProgNCSCMD( uwFpu_offset, 0, ubMTIndex, RETRY_SB_INSERT_NEW_MT_MODE);
			}

			//Micron SB Todo
			//delta table format, Vth shift -> Vth
			RetrySBAddAndCheckOverBoundary(
				(U64)default_delta_table[3][gubCnt1Result[RETRY_SB_LOWER_PAGE][ubIndex]],
				(U64)gSBTask.ulCount_one[ubIndex],
				&uoAddResult,
				RETRY_SB_U8_BIT_LENGTH
			);
			gSBTask.ulCount_one[ubIndex] = (U8)uoAddResult;

			//SB MICRON Check Point 0729
			M_UART(ERROR_SOFTBIT_, "\n gSBTask.ulCount_one[ubIndex](Vth) %x, default_delta_table[3][gubCnt1Result[RETRY_SB_LOWER_PAGE][ubIndex]] %x ", gSBTask.ulCount_one[ubIndex], default_delta_table[3][gubCnt1Result[RETRY_SB_LOWER_PAGE][ubIndex]]);
		}

		//SB MICRON Check Point 0729
		M_UART(ERROR_SOFTBIT_, "\n gubCnt1Result[RETRY_SB_LOWER_PAGE][CNT1_RESULT_ARRAY_ROUND_CNT] %d ", gubCnt1Result[RETRY_SB_LOWER_PAGE][CNT1_RESULT_ARRAY_ROUND_CNT]);


		if (ubNonZeroSignal) {
			///   refresh it
			for (ubIndex = 0 ; ubIndex <  gubCnt1Result[RETRY_SB_LOWER_PAGE][CNT1_RESULT_ARRAY_PARM_LENGTH]; ubIndex++) {
				if ((gubCnt1Result[RETRY_SB_LOWER_PAGE][CNT1_RESULT_SKIP_BIT_CNT] & BIT(ubIndex)) == 0) {
					//Micron SB Todo
					//delta table format, Vth shift
					default_delta_table[3][gubCnt1Result[RETRY_SB_LOWER_PAGE][ubIndex]]  =  gSBTask.ulCount_one[ubIndex] ;
				}
			}

			/// check ROUND CNT
			gubCnt1Result[RETRY_SB_LOWER_PAGE][CNT1_RESULT_ARRAY_ROUND_CNT]++;
#if (TRUE == (IM_B27B || IM_B27A))
			if (gubCnt1Result[RETRY_SB_LOWER_PAGE][CNT1_RESULT_ARRAY_ROUND_CNT] < COARSE_TABLE_CHECK_ROUND_MAX_TIMES_B27B) {
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_SET_READ_LEVEL;
				break;
			}
#else
			if (gubCnt1Result[RETRY_SB_LOWER_PAGE][CNT1_RESULT_ARRAY_ROUND_CNT] < COARSE_TABLE_CHECK_ROUND_MAX_TIMES) {
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_SET_READ_LEVEL;
				break;
			}
#endif
		}

		// program counter should  be here after all done.

		//Micron SB Todo
		//delta table format, Vth shift
		ubCoarseTuningShiftOffset = default_delta_table[3][3];

		// For DriveLog Debug Info, Record delta Table Coarse Tuning Offset
		gSBTask.ubDeltaTableCoarseTuningReadLevelOffset = ubCoarseTuningShiftOffset;

		//SB MICRON Check Point 0806
		M_UART(ERROR_SOFTBIT_, "\n default_delta_table[3][3] = %x", default_delta_table[3][3]);
		for (ubIndex = 0 ; ubIndex < 7; ubIndex++) {
			Offset_delta_table[ubIndex] = ubCoarseTuningShiftOffset;		//Update Lower/Upper/eXtra Read offset from result caculate by Lower page
			default_delta_table[3][ubIndex] = 0;
			//SB MICRON Check Point 0806
			M_UART(ERROR_SOFTBIT_, "\n Offset_delta_table[%d] = %x", ubIndex, Offset_delta_table[ubIndex]);
		}

		/// count case A or caseB
		ubNonZeroSignal = FALSE;
		if (0 != Offset_delta_table[3]) {
			ubNonZeroSignal = TRUE;
		}
		//Micron count case A or caseB
		if (ubNonZeroSignal) {
			gSBTask.ubCaseABCntDefault = SOFTBIT_RETRY_CASE_A;
		}
		else {
			gSBTask.ubCaseABCntDefault = SOFTBIT_RETRY_CASE_B;
		}

		//SB MICRON Check Point 0729
		M_UART(ERROR_SOFTBIT_, "\n gSBTask.ubCaseABCntDefault %d ", gSBTask.ubCaseABCntDefault);

		ubNonZeroSignal = FALSE;
		gubCnt1Result[RETRY_SB_LOWER_PAGE][CNT1_RESULT_ARRAY_ROUND_CNT] = 0;

		//Reset Curr_frm_idx
		gSBTask.ubCurr_frm_idx = 0;
		gSBTask.ubState = RETRY_SB_STATE_SCAN_ERR_FRAME;
		break;

	case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_IDLE_AND_WAIT:
		break;
	}
	if (TRUE == ubTriggerMTLowClockMode) {
		ubMTIndex = RetrySBPrepareFreeMT(RetrySBDetermingCoarseTuningLevelPass_Callback, RetrySBDetermingCoarseTuningLevelFail_Callback);
		RetrySBSelecMTFPUTrigger(uwFpu_offset, (U32)( (gSBTask.ulShareLowerFSA_Align) + (gSBTask.ubCurr_frm_idx) ), 0, ubMTIndex, SWITCH_CLK__LOW_CLK_MODE);

		if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
			gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
		}
		else {
			gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
			gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
		}
	}
}

U8 RetrySBPrepareFreeMT(MT_Callback_Func_t PassCallback, MT_Callback_Func_t FailCallback)
{
	U8 ubMTIndex = FlaGetFreeMTIndex();
	gpRetry->TrigMTList.MTCb[gpRetry->TrigMTList.ubTail].ubMTIndex = ubMTIndex;
	gpRetry->TrigMTList.MTCb[gpRetry->TrigMTList.ubTail].PassCallback = PassCallback;
	gpRetry->TrigMTList.MTCb[gpRetry->TrigMTList.ubTail].FailCallback = FailCallback;
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, gpRetry->TrigMTList.ubTrigCnt != RETRY_MAX_TRIG_MT_NUM);
	gpRetry->TrigMTList.ubTrigCnt++;
	gpRetry->TrigMTList.ubTail = (gpRetry->TrigMTList.ubTail + 1) & RETRY_MAX_TRIG_MT_NUM_MASK;
	return ubMTIndex;
}

void RetrySBSelecMTFPUTrigger(U16 uwFpu_offset, U32 ulFSA, U8 flh_type_legacy, U8 ubMTIndex, U8 ubMode)
{
	FlhMT_t *mtp = NULL;
	MTCfg_t uoMTCfg;
	U8 ubALU;

	ubALU = gSBTask.MTTemplate.dma.ALUSelect;
	mtp = (FlhMT_t *)M_MT_ADDR(ubMTIndex);

	///clear trig struct
	memset((void *) & (gSBTask.MT), 0, sizeof(FlhMT_t));

	/// copy mtp tempelete here
#if (PS5013_EN)
	gSBTask.MT.cmd.btFPUEn = 0;
#endif /* (PS5013_EN) */
	gSBTask.MT.cmd.btBusy = 1;

	gSBTask.MT.cmd.btConversionBypass = 0;
	gSBTask.MT.cmd.ALUSelect = gSBTask.MTTemplate.dma.ALUSelect;
	gSBTask.MT.cmd.btDisableUDMA = 1;
#if (PS5017_EN || PS5013_EN)
	gSBTask.MT.cmd.btiFSAEn = 1;
#endif /* (PS5017_EN || PS5013_EN) */

	gSBTask.MT.cmd.btAllowSwitch = 0;
	gSBTask.MT.dma.ADGSelectPointer = 0;
	gSBTask.MT.cmd.btIoType = gSBTask.MTTemplate.dma.btIoType;
	//Micron SB Todo
	//CLK rate
	if ((ubMode == SWITCH_CLK__LOW_CLK_MODE) && (ASIC)) {
		gSBTask.MT.cmd.FCLK_DIV = FIP_MT_CLK_DIV_MAX;
#if (PS5013_EN)
		gSBTask.MT.cmd.btFCLK_DIV_EN = ENABLE;
#endif /* (PS5013_EN) */
	}
	else {
		gSBTask.MT.cmd.FCLK_DIV = (U8)(gSBTask.MTTemplate.dma.FCLK_DIV);
#if (PS5013_EN)
		gSBTask.MT.cmd.btFCLK_DIV_EN = (U8)(gSBTask.MTTemplate.dma.btFCLK_DIV_EN);
#endif /* (PS5013_EN) */
	}
	gSBTask.MT.cmd.FlashType = gSBTask.MTTemplate.dma.FlashType;


	gSBTask.MT.cmd.btBMUAllocateEn = 0;
	gSBTask.MT.cmd.btInterruptVectorEn = 1;

	gSBTask.MT.cmd.uwFPUPtr  = uwFpu_offset;
	gSBTask.MT.cmd.btUpdPollingSequence = 1;
	//Micron SB Todo
	//Polling sequence
	U8 ubTempPolSeqSel = ((FPU_PTR_OFFSET(fpu_entry_reset_fc) == uwFpu_offset) || (FPU_PTR_OFFSET(fpu_entry_nop) == uwFpu_offset)) ? (POL_SEQ_FPU_ENTRY_READ_STATUS_LUN0_BUSY_20 + gSBTask.ubResetDie) : (POL_SEQ_FPU_ENTRY_READ_STATUS_LUN0_BUSY_20 + gSBTask.ublun);
	M_FIP_SET_POL_SEQUENCE_SELECT(gSBTask.MT.cmd.POL_SEQ_SEL, ubTempPolSeqSel);

	gSBTask.MT.dma.uliFSA0_1 = ulFSA;
	///// do global trigger

	//set MTQ_ID
	gSBTask.MT.cmd.btCESelectMode = 1;
	gSBTask.MT.cmd.ubCEValue = gSBTask.MTTemplate.cmd.ubCEValue;
	gSBTask.MT.cmd.NormalTargetCPU = MT_TARGET_CPU0;
	gSBTask.MT.cmd.ErrorTargetCPU = MT_TARGET_CPU0;

	memcpy((void *)mtp, (void *) & (gSBTask.MT), sizeof(FlhMT_t));

	// Global Trigger MT
	uoMTCfg.uoAll = 0;
	uoMTCfg.bits_recc.ubQUE_IDX = gSBTask.MTTemplate.cmd.ubCEValue;
	uoMTCfg.bits_recc.ubMT_IDX = ubMTIndex;
	uoMTCfg.bits_recc.btQOS = gSBTask.ubUseQorOrNot;
	uoMTCfg.u32.ulMT_CFG1 = 0;
	FlaGlobalTrigger(&uoMTCfg);
}

void RetrySBDetermingCoarseTuningLevelPass_Callback(U8 ubMTIndex)
{
	gSBTask.ubMTFailCnt = 0;
	gSBTask.ubState =  gSBTask.ubPrevState;
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
	gSBTask.ubState = RETRY_SB_STATE_DET_COARSE_TUNE_LEVEL;


	switch (gSBTask.ubSubstate) {
	case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_DISABLE_ARC:
		gSBTask.ubSubstate = RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_GET_ARC;
		break;
	case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_GET_ARC:
		gSBTask.ubSubstate = RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_CHECK_ARC;
		break;
	case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_CHECK_ARC:
		gSBTask.ubSubstate = RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_SET_DEFAULT_RETRY_TABLE;
		break;
	case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_SET_DEFAULT_RETRY_TABLE:
		gSBTask.ubSubstate = RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_GET_RETRY_TABLE;
		break;
	case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_GET_RETRY_TABLE:
		gSBTask.ubSubstate = RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_CHECK_RETRY_TABLE;
		break;
	case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_CHECK_RETRY_TABLE:
		gSBTask.ubSubstate = RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_SET_READ_LEVEL;
		break;
	case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_SET_READ_LEVEL:
		gSBTask.ubSubstate = RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_GET_READ_LEVEL;
		break;
	case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_GET_READ_LEVEL:
		gSBTask.ubSubstate = RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_CHECK_READ_LEVEL;
		break;
	case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_CHECK_READ_LEVEL:
		gSBTask.ubSubstate = RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_READ_CNTONE_OP;
		//Init Value
		gSBTask.ulCount_one[0] = 0;
		gSBTask.ulCount_one[1] = 0;
		gSBTask.ulCount_one[2] = 0;
		gSBTask.ubCurr_frm_idx = 0;
		break;

	case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_READ_CNTONE_OP:
		gSBTask.ubSubstate = RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_READ_CNTONE_DMA;
		break;
	case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_READ_CNTONE_DMA:
		gSBTask.ulCount_one[gubCnt1Result[RETRY_SB_LOWER_PAGE][CNT1_RESULT_ARRAY_PARM_CNT]] += M_FIP_CHANNEL_GET_COUNTONE_RESULT(gSBTask.ubChannel);

		gubCnt1Result[RETRY_SB_LOWER_PAGE][CNT1_RESULT_ARRAY_FRAME_CNT] ++;
		gSBTask.ubCurr_frm_idx++;
		if (gubCnt1Result[RETRY_SB_LOWER_PAGE][CNT1_RESULT_ARRAY_FRAME_CNT] == gub4kEntrysPerPlane) {
			gubCnt1Result[RETRY_SB_LOWER_PAGE][CNT1_RESULT_ARRAY_FRAME_CNT] = 0;
			gSBTask.ubCurr_frm_idx = 0;
			gubCnt1Result[RETRY_SB_LOWER_PAGE][CNT1_RESULT_ARRAY_PARM_CNT] ++;
		}
		if (gubCnt1Result[RETRY_SB_LOWER_PAGE][CNT1_RESULT_ARRAY_PARM_CNT] == gubCnt1Result[RETRY_SB_LOWER_PAGE][CNT1_RESULT_ARRAY_PARM_LENGTH]) {
			gubCnt1Result[RETRY_SB_LOWER_PAGE][CNT1_RESULT_ARRAY_PARM_CNT]  = 0;
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_CHK_COARSE_TABLE;
		}
		else {
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_READ_CNTONE_OP;
		}
		break;
	}
}

void RetrySBDetermingCoarseTuningLevelFail_Callback(U8 ubMTIndex)
{
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
	gSBTask.ubState =  gSBTask.ubPrevState;
	switch (gSBTask.ubSubstate) {
	case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_DISABLE_ARC:
	case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_SET_DEFAULT_RETRY_TABLE:
	case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_SET_READ_LEVEL:
	case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_GET_ARC:
	case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_GET_RETRY_TABLE:
	case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_GET_READ_LEVEL:
	case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_READ_CNTONE_OP:
		RetrySBMTTriggerRetry(FALSE);
		break;

	case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_CHECK_ARC:
	case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_CHECK_RETRY_TABLE:
	case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_CHECK_READ_LEVEL:
		if (gSBTask.ubMTCheckFeatureFailCnt < RETRY_SB_MT_FAIL_RETRY_CNT_MAX) {
			gSBTask.ubMTCheckFeatureFailCnt++;
			gSBTask.ubState = RETRY_SB_STATE_DET_COARSE_TUNE_LEVEL;
			switch (gSBTask.ubSubstate) {
			case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_CHECK_ARC:
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_DISABLE_ARC;
				break;
			case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_CHECK_RETRY_TABLE:
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_SET_DEFAULT_RETRY_TABLE;
				break;
			case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_CHECK_READ_LEVEL:
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_SET_READ_LEVEL;
				break;
			}
		}
		else {
			gSBTask.ubMTCheckFeatureFailCnt = 0;
			gpVTDBUF->Retry.ulSetFeatureFailCnt++;
#if (!RELEASED_FW)
			DebugError();
#endif
			//If Set Get CHK feature fail, try to set to default feature, then Reset FLH
			RetrySBSwitchStateToSetDefaultFeatureReset();
		}
		break;
	case RETRY_SB_SUBSTATE_DETERMING_COARSE_TUNING_LEVEL_READ_CNTONE_DMA:

		// enable count one function
		RetrySBDetermingCoarseTuningLevelPass_Callback(ubMTIndex);
		break;
	}

}

void RetrySBReadFlow(void)
{

	switch (gSBTask.ubSubstate) {
	case RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_INIT:
		//Micron SB Todo
		M_FIP_SET_LDPC_CONFIG_PAGE_SELECT(gSBTask.ubPageType);
		switch (gSBTask.ubPageType) {
		case RETRY_SB_LOWER_PAGE:
			gSBTask.ubdecode_mode = SB_MICRON_DECODE_MODE_LOWER;
			break;
		case RETRY_SB_UPPER_PAGE:
			gSBTask.ubdecode_mode = SB_MICRON_DECODE_MODE_UPPER;
			break;
		case RETRY_SB_EXTRA_PAGE:
			gSBTask.ubdecode_mode = SB_MICRON_DECODE_MODE_EXTRA;
			break;
		default:
			//LOG_PRINTF("Invalid page type.\n");
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
			RetrySBSwitchStateToSetDefaultFeatureReset();
			return;
		}

		switch (gSBTask.ubSubstate) {
		case RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_INIT:
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_SET_ARC_CALIBRATION_PERSISTENCE;
			gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_SET_FEATURE;
			break;
		default:
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
			RetrySBSwitchStateToSetDefaultFeatureReset();
			return;
		}

	/* no break */
	case RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_SET_ARC_CALIBRATION_PERSISTENCE:
#if(TRUE == RETRY_MICRON_NICKS)
		if (TRUE == gSBTask.ubSBReadDecodeWithARC) {
			RetrySBHBSetFeatureARC(TRUE, FALSE);
		}
		else {
			RetrySBHBSetFeatureARC(FALSE, FALSE);
		}
#else	/*(TRUE == RETRY_MICRON_NICKS)*/
		RetrySBHBSetFeatureARC(FALSE, TRUE);
#endif	/*(TRUE == RETRY_MICRON_NICKS)*/
		break;
	case RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_HB:
		RetrySBReadHB(  gSBTask.ubPageType, TRUE, (U32)( (gSBTask.ulFSA_Align) + (gSBTask.ubCurr_frm_idx) ), gSBTask.ubCurrentLDPCFrameIdx, 1, 0, 0);
		break;
	case RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_SB1:
		RetrySBReadSB1(   gSBTask.ubPageType, (U8 *)delta_table, gSBTask.ubCurrentLDPCFrameIdx);
		break;
	case RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_SB2:
		RetrySBReadSB2(  gSBTask.ubPageType, (U8 *)delta_table, gSBTask.ubCurrentLDPCFrameIdx);
		break;
	case RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_SB3:
		RetrySBReadSB3( gSBTask.ubPageType, DISABLE, (U8 *)delta_table, gSBTask.ubCurrentLDPCFrameIdx);
		break;
	case RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_SB4:
		RetrySBReadSB4( gSBTask.ubPageType, DISABLE, (U8 *)delta_table, gSBTask.ubCurrentLDPCFrameIdx);
		break;
	case RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_IDLE_OR_WAIT:
		break;
	}
}

void RetrySBFlashLoadECCParam(U32 load_target)
{
	/* Enable ECC clock for set configuration into ECC engine */
	M_FIP_ECC_CLOCK_GATING_DIS();

	/* Trigger load and check HW ready */
	R32_FCON[R32_FCON_ECC_PARAM_CFG] |= load_target;
	while (R32_FCON[R32_FCON_ECC_PARAM_CFG] & load_target);

	/* Disable ECC clock */
	M_FIP_ECC_CLOCK_GATING_EN();
}

U32 RetrySBFlashGetCurrentLDPCFrameSize(void)
{
	return ldpc_frame_size[M_GET_ECC_MODE()];
}

void RetrySBDumpLLRTable(void)
{
	U8	ubLLRTable4ByteIdx = 0;
	U32 ulTemp = 0;
	////Get LLR table ////
	for (ubLLRTable4ByteIdx = 0 ; ubLLRTable4ByteIdx < SBIT_DSP_LLR_TABLE_SIZE_IN_4_BYTES ; ubLLRTable4ByteIdx++) {
		R32_FCON[R32_FCON_ECC_PARAM_CFG] &= ~(DSP_LLR_TABLE_SEL_MASK << DSP_LLR_TABLE_SEL_SHIFT);
		R32_FCON[R32_FCON_ECC_PARAM_CFG] |= (ubLLRTable4ByteIdx << DSP_LLR_TABLE_SEL_SHIFT);
		ulTemp = R32_FCON[R32_FCON_DSP_LLR_TABLE];
		gSoftBitDSPADTLLRTable.LLRTable.AccessBy32bits.ulLLR32bits[ubLLRTable4ByteIdx] = ulTemp;

		//SB MICRON Check Point 0805
		M_UART(ERROR_SOFTBIT_, "\n LLR_Dump %x", ulTemp);
	}
	//SB MICRON Check Point 0805
	M_UART(ERROR_SOFTBIT_, "\n ");
}

void RetrySBDecodeFlow (void)
{
	U8 ubMTIndex;

	switch (gSBTask.ubSubstate) {
	case RETRY_SB_SUBSTATE_SB_DECODE_WITH_DEFAULT_LLR_INIT:
	case RETRY_SB_SUBSTATE_SB_DECODE_WITH_ADT_LLR_INIT:
		switch (gSBTask.ubPageType) {
		case RETRY_SB_LOWER_PAGE:
			gSBTask.ubdecode_mode = SB_MICRON_DECODE_MODE_LOWER;
			break;
		case RETRY_SB_UPPER_PAGE:
			gSBTask.ubdecode_mode = SB_MICRON_DECODE_MODE_UPPER;
			break;
		case RETRY_SB_EXTRA_PAGE:
			gSBTask.ubdecode_mode = SB_MICRON_DECODE_MODE_EXTRA;
			break;
		default:
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
			RetrySBSwitchStateToSetDefaultFeatureReset();
			return;
		}

		switch (gSBTask.ubSubstate) {

		case RETRY_SB_SUBSTATE_SB_DECODE_WITH_DEFAULT_LLR_INIT:
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_DECODE_WITH_DEFAULT_LLR_SB_CORR;
			break;
		case RETRY_SB_SUBSTATE_SB_DECODE_WITH_ADT_LLR_INIT:
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_DECODE_WITH_ADT_LLR_SB_CORR;
			if (NCS_EN) {
				//SB MICRON Check Point 0805
				M_UART(ERROR_SOFTBIT_, "\n ADT LLR_Dump ");

				RetrySBDumpLLRTable();
			}
			break;
		default:
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
			RetrySBSwitchStateToSetDefaultFeatureReset();
			return;
		}

		break;
	case RETRY_SB_SUBSTATE_SB_DECODE_WITH_DEFAULT_LLR_SB_CORR:
	case RETRY_SB_SUBSTATE_SB_DECODE_WITH_ADT_LLR_SB_CORR:
		if (NCS_EN) {
			ReadRetryDumpHWSetting(gSBTask.ubPageType, 0xD2, gSBTask.ubdecode_mode, 0, 0);
		}
		// fpu SB correct of 2K
		ubMTIndex = RetrySBPrepareFreeMT(RetrySBTriggerCorrectPass_Callback, RetrySBTriggerCorrectFail_Callback);
		RetrySBCorrectMTTrigger( 0, 0, gSBTask.ubCurrentLDPCFrameIdx, 0, 0, gSBTask.ubdecode_mode, 1, 0, 1, ubMTIndex);
		if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
			gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
		}
		else {
			gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
			gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
		}
		break;

	}
	return;
}

void RetrySBTriggerCorrectFail_Callback(U8 ubMTIndex)
{
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
	gSBTask.ubState = gSBTask.ubPrevState;
	switch (gSBTask.ubSubstate) {
	//Micron SB Todo
	case RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_HB_CORR_2K:
		gSBTask.ubSubstate =  RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_HB_CORR_2K;
		break;
	case RETRY_SB_SUBSTATE_SB_DECODE_WITH_DEFAULT_LLR_SB_CORR:
		gSBTask.ubSubstate =  RETRY_SB_SUBSTATE_SB_DECODE_WITH_DEFAULT_LLR_SB_CORR;
		break;
	case RETRY_SB_SUBSTATE_SB_DECODE_WITH_ADT_LLR_SB_CORR:
		gSBTask.ubSubstate =  RETRY_SB_SUBSTATE_SB_DECODE_WITH_ADT_LLR_SB_CORR;
		break;
	case RETRY_SB_SUBSTATE_SB_DSP2_SB_CORR:
		gSBTask.ubSubstate =  RETRY_SB_SUBSTATE_SB_DSP2_SB_CORR;
		break;
	case RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_WITH_SPECIFIC_RETRY_TABLE_READ_OFFSET_HB_CORR_2K:
		gSBTask.ubSubstate =  RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_WITH_SPECIFIC_RETRY_TABLE_READ_OFFSET_HB_CORR_2K;
		break;
	}
}

void RetrySBModifyReadRetryAddCoarseTuningLevel(void)
{
	//U8 ubidx;
	U8 ubidx2;
	U64 uoAddResult = 0;
	if ((SOFTBIT_RETRY_CASE_B == gSBTask.ubCaseABCntDefault) && (SOFTBIT_RETRY_CASE_A == gSBTask.ubCaseABCnt)) {
		gSBTask.ubCaseABCnt = SOFTBIT_RETRY_CASE_B;
	}
	/// reset delta_table
	memcpy((void *)delta_table, (void *)default_delta_table, sizeof(delta_table));
	if (SOFTBIT_RETRY_CASE_A == gSBTask.ubCaseABCnt) {
		/// update delta_table			//MICRON, only Read offset, no need update +-delta
		for (ubidx2 = 0;  ubidx2 < 7;  ubidx2++) {
			RetrySBAddAndCheckOverBoundary(delta_table[3][ubidx2], Offset_delta_table[ubidx2], &uoAddResult, RETRY_SB_U8_BIT_LENGTH);
			delta_table[3][ubidx2] = (U8)uoAddResult;

			//SB MICRON Check Point 0806
			M_UART(ERROR_SOFTBIT_, "\n delta_table[3][%d]= %x, Offset_delta_table[%d] = %x", ubidx2, delta_table[3][ubidx2], ubidx2, Offset_delta_table[ubidx2]);
		}
		// For DriveLog Debug Info, Record Coarse Tuning Result
		gSBTask.ubCoarseTuningReadLevel = delta_table[3][3];
	}
	else if (SOFTBIT_RETRY_CASE_B == gSBTask.ubCaseABCnt) {
		for (ubidx2 = 0;  ubidx2 < 7;  ubidx2++) {
			delta_table[3][ubidx2] = 0;
		}
	}
	else {	//CASE_C,
		if (SB_MICRON_SPECIFIC_RETRY_TABLE == gSBTask.ubSpecificMode) {
			gSBTask.ubSpecificRetryTable = gubSpecificRetryTable[gSBTask.ubSpecificRetryTableRound];
			for (ubidx2 = 0;  ubidx2 < 7;  ubidx2++) {
				delta_table[3][ubidx2] = 0;
			}

			//SB MICRON Check Point 0802
			M_UART(ERROR_SOFTBIT_, "\n SpecificTable:%x", gSBTask.ubSpecificRetryTable);
		}
		else {	//SB_MICRON_SPECIFIC_READ_OFFSET
			for (ubidx2 = 0;  ubidx2 < 7;  ubidx2++) {
				delta_table[3][ubidx2] = gubSpecificReadOffset[gSBTask.ubSpecificReadOffsetRound];
			}

			//SB MICRON Check Point 0802
			M_UART(ERROR_SOFTBIT_, "\n SpecificOffset:%x", delta_table[3][0]);		//Should be the same in a row
		}
	}
}

void RetrySBHBSetFeatureRetryTable(U8 ubTableIdx)
{
	/*Calibration ON: do Read by Calibrate Read offset and save Read offset result to FA:A0h~ACh <P3>*/
	/*Persistence ON: do Read by Read offset on FA:A0h~ACh <P3>*/

	U16 *puwfpu_ptr = NULL;
	U16 uwFpu_offset;
	U8 ubMTIndex = 0;
	U8 ubi = 0;
	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_SETTING_RETRY_TABLE_SET_FEATURE:
		//Micron SB Todo
		//Micron Set feature
		uwFpu_offset = FPU_PTR_OFFSET(fpu_set_feature);		//set_feature
		puwfpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);
		/// reset fpu entry
		for (ubi = 0 ; ubi < SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH ; ubi++) {
			if (0 == ubi) {
				puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (ubi << 1)] = FPU_DAT_W_FIR_D(0x00);
			}
			else if ((SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH - 1) == ubi) {
				puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (ubi << 1)] = FPU_DAT_W_LAST_D(0x00);
			}
			else {
				puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (ubi << 1)] = FPU_DAT_W_MID_D(0x00);
			}
			puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (ubi << 1) + 1] = FPU_DAT_W(0x00);
		}
		puwfpu_ptr[0] = FPU_CMD(0xD5);
		puwfpu_ptr[1] = FPU_ADR_1B(gSBTask.ublun);
		puwfpu_ptr[2] = FPU_ADR_1B(0x89);				//FA 89h for Read Retry Table
		if (FIP_FLASH_CLOCK_400MHZ == gFlhEnv.ubTargetFlashClock) {
			// flh clk 400
			puwfpu_ptr[3] = FPU_DLY(0x15);		// Decimal 21
		}
		else if (FIP_FLASH_CLOCK_333MHZ == gFlhEnv.ubTargetFlashClock) {
			// flh clk 333
			puwfpu_ptr[3] = FPU_DLY(0xF);		// Decimal 15
		}
		else {
			puwfpu_ptr[3] = FPU_DLY(0x10);		// Decimal 16
		}

		puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P1] = FPU_DAT_W_FIR_D(ubTableIdx);
		puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P1 + 1] = FPU_DAT_W(ubTableIdx);
		puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_END_IDX] = FPU_END;
		break;
	case RETRY_SB_3RD_STATE_SETTING_RETRY_TABLE_GET_FEATURE:
		uwFpu_offset = FPU_PTR_OFFSET(fpu_get_feature);		//get_feature
		puwfpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);

		puwfpu_ptr[0] = FPU_CMD(0xD4);
		puwfpu_ptr[1] = FPU_ADR_1B(gSBTask.ublun);
		puwfpu_ptr[2] = FPU_ADR_1B(0x89);

		puwfpu_ptr[SB_MICRON_GET_FEATURE_FPU_END_IDX] = FPU_END;
		break;
	case RETRY_SB_3RD_STATE_SETTING_RETRY_TABLE_CHECK_FEATURE:
		uwFpu_offset = FPU_PTR_OFFSET(fpu_read_and_compare_feature_data);		//cmp_feature_data
		puwfpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);

		puwfpu_ptr[0] = FPU_CMD(0x00);
		puwfpu_ptr[1] = FPU_DLY(0x10);

		//Clear <P1><P2><P3><P3> Compare mask
		for (ubi = 0; ubi < PARAMETER_NUM_PER_FPU; ubi++) {
			puwfpu_ptr[SB_MICRON_CHECK_FEATURE_FPU_START_IDX + (ubi << 1)] =  FPU_DAT_R_CMP(0x00);
			puwfpu_ptr[(SB_MICRON_CHECK_FEATURE_FPU_START_IDX + 1) + (ubi << 1)] = FPU_DAT_R_MASK(0x00);
		}
		//Compare <P1>
		puwfpu_ptr[SB_MICRON_CHECK_FEATURE_FPU_START_IDX + (SB_MICRON_FEATURE_PARAM_P1_INDEX << 1)] =  FPU_DAT_R_CMP(ubTableIdx);
		puwfpu_ptr[(SB_MICRON_CHECK_FEATURE_FPU_START_IDX + 1) + (SB_MICRON_FEATURE_PARAM_P1_INDEX << 1)] = FPU_DAT_R_MASK(0xFF);

		break;
	}
	if (NES_EN || NCS_EN) {
		ubMTIndex = RetrySBPrepareFreeMT(RetrySBHBSetFeatureRetryTablePass_Callback, RetrySBHBSetFeatureRetryTablePass_Callback);
	}
	else {
		ubMTIndex = RetrySBPrepareFreeMT(RetrySBHBSetFeatureRetryTablePass_Callback, RetrySBHBSetFeatureRetryTableFail_Callback);
	}

	RetrySBSelecMTFPUTrigger(uwFpu_offset, (U32)( (gSBTask.ulFSA_Align) + (gSBTask.ubCurr_frm_idx) ), 0, ubMTIndex, SWITCH_CLK__LOW_CLK_MODE);
	if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
		gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
	}
	else {
		gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
		gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
	}
}

void RetrySBHBSetFeatureRetryTablePass_Callback(U8 ubMTIndex)
{
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
	gSBTask.ubMTFailCnt = 0;
	gSBTask.ubState =  gSBTask.ubPrevState;
	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_SETTING_RETRY_TABLE_SET_FEATURE:
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SETTING_RETRY_TABLE_GET_FEATURE;
		break;
	case RETRY_SB_3RD_STATE_SETTING_RETRY_TABLE_GET_FEATURE:
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SETTING_RETRY_TABLE_CHECK_FEATURE;
		break;
	case RETRY_SB_3RD_STATE_SETTING_RETRY_TABLE_CHECK_FEATURE:
		switch (gSBTask.ubSubstate) {
		case RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_WITH_SPECIFIC_RETRY_TABLE_READ_OFFSET_SET_FEATURE:
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_WITH_SPECIFIC_RETRY_TABLE_READ_OFFSET_HB_READ;
			gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_HB_READ_INIT;
			break;

		case RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_RETRY_TABLE:

			//SB MICRON Check Point 0802
			M_UART(ERROR_SOFTBIT_, "\nSB BackUpedState:%d", gSBTask.ubBackUpedState);
			M_UART(ERROR_SOFTBIT_, "\n     ubBackUpedSubstate:%d", gSBTask.ubBackUpedSubstate);

			gSBTask.ubState = gSBTask.ubBackUpedState;
			gSBTask.ubSubstate = gSBTask.ubBackUpedSubstate;
			if (RETRY_SB_STATE_SB_RESET_FLH == gSBTask.ubBackUpedState) {		//after Set Default feature and next State is Reset flash , This is the END of SB flow
				gSBTask.ubState =  RETRY_SB_STATE_SB_RESET_FLH;
				gSBTask.ubBackUpedState = RETRY_SB_STATE_DONE;
			}
			break;
		}
		break;
	}

}

void RetrySBHBSetFeatureRetryTableFail_Callback(U8 ubMTIndex)
{
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
	gSBTask.ubState =  gSBTask.ubPrevState;
	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_SETTING_RETRY_TABLE_SET_FEATURE:
	case RETRY_SB_3RD_STATE_SETTING_RETRY_TABLE_GET_FEATURE:
		RetrySBMTTriggerRetry(FALSE);
		break;

	case RETRY_SB_3RD_STATE_SETTING_RETRY_TABLE_CHECK_FEATURE:
		if (gSBTask.ubMTCheckFeatureFailCnt < RETRY_SB_MT_FAIL_RETRY_CNT_MAX) {
			gSBTask.ubMTCheckFeatureFailCnt++;
			gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SETTING_RETRY_TABLE_SET_FEATURE;
		}
		else {
			gSBTask.ubMTCheckFeatureFailCnt = 0;
			gpVTDBUF->Retry.ulSetFeatureFailCnt++;
#if (!RELEASED_FW)
			DebugError();
#endif
			//If Set Get CHK feature fail, try to set to default feature, then Reset FLH
			RetrySBSwitchStateToSetDefaultFeatureReset();
		}
		break;
	}
}


void RetrySBHBSetFeatureARC(U8 ubCalibration, U8 ubPersistence)
{
	/*Calibration ON: do Read by Calibrate Read offset and save Read offset result to FA:A0h~ACh <P3>*/
	/*Persistence ON: do Read by Read offset on FA:A0h~ACh <P3>*/

	U16 *puwfpu_ptr = NULL;
	U16 uwFpu_offset;
	U8 ubMTIndex = 0;
	U8 ubi = 0;
	U8 ubFPUData = ((ubPersistence << 1) + ubCalibration);

	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_SET_FEATURE:
		//Micron SB Todo
		//Micron Set feature
		uwFpu_offset = FPU_PTR_OFFSET(fpu_set_feature);		//set_feature
		puwfpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);
		/// reset fpu entry
		for (ubi = 0 ; ubi < SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH ; ubi++) {
			if (0 == ubi) {
				puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (ubi << 1)] = FPU_DAT_W_FIR_D(0x00);
			}
			else if ((SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH - 1) == ubi) {
				puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (ubi << 1)] = FPU_DAT_W_LAST_D(0x00);
			}
			else {
				puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (ubi << 1)] = FPU_DAT_W_MID_D(0x00);
			}
			puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (ubi << 1) + 1] = FPU_DAT_W(0x00);
		}
		puwfpu_ptr[0] = FPU_CMD(0xD5);
		puwfpu_ptr[1] = FPU_ADR_1B(gSBTask.ublun);
		puwfpu_ptr[2] = FPU_ADR_1B(0x96);				//FA 96h for ARC feature
		if (FIP_FLASH_CLOCK_400MHZ == gFlhEnv.ubTargetFlashClock) {
			// flh clk 400
			puwfpu_ptr[3] = FPU_DLY(0x15);		// Decimal 21
		}
		else if (FIP_FLASH_CLOCK_333MHZ == gFlhEnv.ubTargetFlashClock) {
			// flh clk 333
			puwfpu_ptr[3] = FPU_DLY(0xF);		// Decimal 15
		}
		else {
			puwfpu_ptr[3] = FPU_DLY(0x10);		// Decimal 16
		}

		puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P1] = FPU_DAT_W_FIR_D(ubFPUData);
		puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P1 + 1] = FPU_DAT_W(ubFPUData);
		puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_END_IDX] = FPU_END;

		break;
	case RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_GET_FEATURE:
		uwFpu_offset = FPU_PTR_OFFSET(fpu_get_feature);		//get_feature
		puwfpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);

		puwfpu_ptr[0] = FPU_CMD(0xD4);
		puwfpu_ptr[1] = FPU_ADR_1B(gSBTask.ublun);
		puwfpu_ptr[2] = FPU_ADR_1B(0x96);

		puwfpu_ptr[SB_MICRON_GET_FEATURE_FPU_END_IDX] = FPU_END;

		break;
	case RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_CHECK_FEATURE:
		uwFpu_offset = FPU_PTR_OFFSET(fpu_read_and_compare_feature_data);		//set_feature
		puwfpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);

		puwfpu_ptr[0] = FPU_CMD(0x00);
		puwfpu_ptr[1] = FPU_DLY(0x10);

		//Clear <P1><P2><P3><P3> Compare mask
		for (ubi = 0; ubi < PARAMETER_NUM_PER_FPU; ubi++) {
			puwfpu_ptr[SB_MICRON_CHECK_FEATURE_FPU_START_IDX + (ubi << 1)] =  FPU_DAT_R_CMP(0x00);
			puwfpu_ptr[(SB_MICRON_CHECK_FEATURE_FPU_START_IDX + 1) + (ubi << 1)] = FPU_DAT_R_MASK(0x00);
		}
		//Compare <P1>
		puwfpu_ptr[SB_MICRON_CHECK_FEATURE_FPU_START_IDX + (SB_MICRON_FEATURE_PARAM_P1_INDEX << 1)] =  FPU_DAT_R_CMP(ubFPUData);
		puwfpu_ptr[(SB_MICRON_CHECK_FEATURE_FPU_START_IDX + 1) + (SB_MICRON_FEATURE_PARAM_P1_INDEX << 1)] = FPU_DAT_R_MASK(0xFF);

		break;
	}
	if (NES_EN || NCS_EN) {
		ubMTIndex = RetrySBPrepareFreeMT(RetrySBHBSetFeatureARCPass_Callback, RetrySBHBSetFeatureARCPass_Callback);
	}
	else {
		ubMTIndex = RetrySBPrepareFreeMT(RetrySBHBSetFeatureARCPass_Callback, RetrySBHBSetFeatureARCFail_Callback);
	}
	RetrySBSelecMTFPUTrigger(uwFpu_offset, (U32)( (gSBTask.ulFSA_Align) + (gSBTask.ubCurr_frm_idx) ), 0, ubMTIndex, SWITCH_CLK__LOW_CLK_MODE);
	if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
		gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
	}
	else {
		gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
		gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
	}
}

void RetrySBHBSetFeatureARCPass_Callback(U8 ubMTIndex)
{
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
	gSBTask.ubMTFailCnt = 0;
	gSBTask.ubState =  gSBTask.ubPrevState;
	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_SET_FEATURE:
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_GET_FEATURE;
		break;
	case RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_GET_FEATURE:
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_CHECK_FEATURE;
		break;
	case RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_CHECK_FEATURE:
		switch (gSBTask.ubSubstate) {
		case RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_ENABLE_AUTO_READ_CALIBRATION:
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_HB_READ;
			gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_HB_READ_INIT;
			break;
		case RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_SET_ARC_CALIBRATION_PERSISTENCE:
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_HB;
			gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_HB_READ_INIT;
			break;
		case RETRY_SB_SUBSTATE_SB_DSP2_ENABLE_ARC_DISABLE_PERSISTENCE:
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_DSP2_READ_NEXT_WORDLINE_SHARE_PAGE_I;
			gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_HB_READ_INIT;
			break;
		case RETRY_SB_SUBSTATE_SB_DSP2_DISABLE_ARC_ENABLE_PERSISTENCE:
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_DSP2_READ_LEVEL_HB;
			gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_HB_READ_INIT;
			break;
		case RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_WITH_SPECIFIC_RETRY_TABLE_READ_OFFSET_ENABLE_AUTO_READ_CALIBRATION:
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_WITH_SPECIFIC_RETRY_TABLE_READ_OFFSET_SET_FEATURE;
			if (SB_MICRON_SPECIFIC_RETRY_TABLE == gSBTask.ubSpecificMode) {
				gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SETTING_RETRY_TABLE_SET_FEATURE;
			}
			else {	//	SB_MICRON_SPECIFIC_READ_OFFSET
				gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SET_READ_LEVEL_OFFSET_INIT;
			}
			break;
		case RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_ARC:					//To next Substate
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_READ_OFFSET_LOWER;
			gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SET_READ_LEVEL_OFFSET_INIT;
			break;
		}
		break;
	}

}
void RetrySBHBSetFeatureARCFail_Callback(U8 ubMTIndex)
{
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
	gSBTask.ubState =  gSBTask.ubPrevState;
	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_SET_FEATURE:
	case RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_GET_FEATURE:
		RetrySBMTTriggerRetry(FALSE);
		break;

	case RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_CHECK_FEATURE:
		if (gSBTask.ubMTCheckFeatureFailCnt < RETRY_SB_MT_FAIL_RETRY_CNT_MAX) {
			gSBTask.ubMTCheckFeatureFailCnt++;
			gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_SET_FEATURE;
		}
		else {
			gSBTask.ubMTCheckFeatureFailCnt = 0;
			gpVTDBUF->Retry.ulSetFeatureFailCnt++;
#if (!RELEASED_FW)
			DebugError();
#endif
			//If Set Get CHK feature fail, try to set to default feature, then Reset FLH
			RetrySBSwitchStateToSetDefaultFeatureReset();
		}
		break;
	}
}
U8 RetrySBHBReadwithARC(void)
{
	U8 ubSBCorrect = 0;
	U8 ubMTIndex;

	switch (gSBTask.ubSubstate) {
	case RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_INIT:
		gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_SET_READ_LEVEL_OFFSET;
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SET_READ_LEVEL_OFFSET_INIT;
		gSBTask.ubARCRoundCnt = 0;
		gSBTask.ubTempPageType = gSBTask.ubPageType;
	/* no break */
	case RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_SET_READ_LEVEL_OFFSET:
		RetrySBSetReadLevelOffset((U8 *)delta_table);
		break;

	case  RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_ENABLE_AUTO_READ_CALIBRATION:				// do 3 times ARC, First with Calibration ON Persistence OFF,  then Calibration ON Persistence ON
		if (0 == gSBTask.ubARCRoundCnt) {
			RetrySBHBSetFeatureARC(TRUE, FALSE);
		}
		else if (1 == gSBTask.ubARCRoundCnt) {		//From second round, Start using ARC_ON, Persistence_ON
			RetrySBHBSetFeatureARC(TRUE, TRUE);
		}
		else {	//After second round, no need to Set ARC Feature
			gSBTask.ubSubstate =  RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_HB_READ;
			gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_HB_READ_INIT;
		}
		break;


	case RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_HB_READ:
		RetrySBReadHB(  gSBTask.ubPageType, TRUE, (U32)( (gSBTask.ulFSA_Align) + (gSBTask.ubCurr_frm_idx) ), gSBTask.ubCurrentLDPCFrameIdx, 1, 0, 0);
		break;

	case  RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_HB_CORR_2K:
		//Micron SB Todo, NCS

		if (NCS_EN) {
			ReadRetryDumpHWSetting(gSBTask.ubPageType, 0xD0, 0, 0, 0);
		}

		// fpu HB correct of 2K
		ubMTIndex = RetrySBPrepareFreeMT(RetrySBTriggerCorrectPass_Callback, RetrySBTriggerCorrectFail_Callback);
		RetrySBCorrectMTTrigger( 0, 0, gSBTask.ubCurrentLDPCFrameIdx, 0, 0, 0, 0, 0, 0, ubMTIndex);    // scale mode=0, decode mode=0
		if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
			gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
		}
		else {
			gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
			gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
		}
		break;
	}

	return ubSBCorrect;
}

U8 RetrySBHBReadwithRetryTableOrReadOffset(void)
{
	U8 ubSBCorrect = 0;
	U8 ubMTIndex;

	switch (gSBTask.ubSubstate) {
	case RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_WITH_SPECIFIC_RETRY_TABLE_READ_OFFSET_INIT:
		gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_WITH_SPECIFIC_RETRY_TABLE_READ_OFFSET_ENABLE_AUTO_READ_CALIBRATION;
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_SET_FEATURE;

		gSBTask.ubARCRoundCnt = 0;
		gSBTask.ubTempPageType = gSBTask.ubPageType;
	/* no break */
	case  RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_WITH_SPECIFIC_RETRY_TABLE_READ_OFFSET_ENABLE_AUTO_READ_CALIBRATION:				// do  ARC, with Calibration ON Persistence OFF
		RetrySBHBSetFeatureARC(TRUE, FALSE);																				// for NCS convinience, Set ARC first then set (RR table/RR offset)
		break;

	case RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_WITH_SPECIFIC_RETRY_TABLE_READ_OFFSET_SET_FEATURE:
		if (SB_MICRON_SPECIFIC_RETRY_TABLE == gSBTask.ubSpecificMode) {
			RetrySBHBSetFeatureRetryTable(gSBTask.ubSpecificRetryTable);		//Set Specific Retry Table
		}
		else {	//	SB_MICRON_SPECIFIC_READ_OFFSET
			RetrySBSetReadLevelOffset((U8 *)delta_table);
		}
		break;

	case RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_WITH_SPECIFIC_RETRY_TABLE_READ_OFFSET_HB_READ:
		RetrySBReadHB(  gSBTask.ubPageType, TRUE, (U32)( (gSBTask.ulFSA_Align) + (gSBTask.ubCurr_frm_idx) ), gSBTask.ubCurrentLDPCFrameIdx, 1, 0, 0);
		break;

	case  RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_WITH_SPECIFIC_RETRY_TABLE_READ_OFFSET_HB_CORR_2K:
		//Micron SB Todo, NCS

		if (NCS_EN) {
			ReadRetryDumpHWSetting(gSBTask.ubPageType, 0xD0, 0, 0, 0);
		}

		// fpu HB correct of 2K
		ubMTIndex = RetrySBPrepareFreeMT(RetrySBTriggerCorrectPass_Callback, RetrySBTriggerCorrectFail_Callback);
		RetrySBCorrectMTTrigger( 0, 0, gSBTask.ubCurrentLDPCFrameIdx, 0, 0, 0, 0, 0, 0, ubMTIndex);    // scale mode=0, decode mode=0
		if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
			gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
		}
		else {
			gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
			gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
		}
		break;
	}

	return ubSBCorrect;
}

void RetrySBCaculateShareLowerPage(void)
{
	U8 ubX = 0, ubTempX = 0;
	U16 uwPage = 0, uwY = 0;

	gSBTask.ulShareLowerFSA_Align = gSBTask.ulFSA_Align;
	gSBTask.ulShareUpperFSA_Align = gSBTask.ulFSA_Align;
	//Micron SB Todo
	uwPage = (gSBTask.ulFSA_Align >> gPCARule_Page.ubShift[COP0_PCA_RULE_0]) & gPCARule_Page.ulMask;
	ubX = FTLGetCoord(uwPage, IM_GETCOORD_X_VAL);
	uwY = FTLGetCoord(uwPage, IM_GETCOORD_Y_VAL);

	ubTempX = ubX;
	//SB MICRON Check Point 0806
	M_UART(ERROR_SOFTBIT_, "\n ubX:%d, ubY:%d", ubX, uwY);
	if (RETRY_SB_LOWER_PAGE != gSBTask.ubPageType) {	//Lower page need to caculate Lower share page, use PCA_Align directly
		//TODO B47R
		switch (gFlhEnv.ubFlashID[4]) {
		case 0xA1:	//B16A
		case 0xA6:  //B17A
		case 0xA2:	// B27A
			ubTempX = ubX - gSBTask.ubPageType;
			break;
		case 0xE6:  //B27B
			ubTempX = ubX + gSBTask.ubPageType;
			break;

		default:
			//MICRON_SB_ASSERT
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
			break;
		}

		//SB MICRON Check Point 0806
		M_UART(ERROR_SOFTBIT_, "\n ubTempX:%d, PageType:%d", ubTempX, gSBTask.ubPageType);

		//Caculate VCA of Share lower page from X Y
		uwPage = RetrySBInvertCoord(ubTempX, uwY);
		//Update Target FSA uwPage element to gen FSA of Share Lower page
		//PCA < -> FSA use same format, the different should only be value of "Block"
		gSBTask.ulShareLowerFSA_Align &= ~(gPCARule_Page.ulMask << gPCARule_Page.ubShift[COP0_PCA_RULE_0]);
		gSBTask.ulShareLowerFSA_Align |= (uwPage << gPCARule_Page.ubShift[COP0_PCA_RULE_0]);
	}
	if (RETRY_SB_EXTRA_PAGE == gSBTask.ubPageType) {	//Only eXtra page need to caculate Upper share page
		//TODO B47R
		switch (gFlhEnv.ubFlashID[4]) {
		case 0xA1:	//B16A
		case 0xA6:  //B17A
		case 0xA2:	//B27A
			ubTempX = ubX - 1;
			break;
		case 0xE6:  //B27B
			ubTempX = ubX + 1;
			break;
		default:
			//MICRON_SB_ASSERT
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
			break;
		}

		//SB MICRON Check Point 0806
		M_UART(ERROR_SOFTBIT_, "\n ubTempX:%d, PageType:%d", ubTempX, gSBTask.ubPageType);

		//Caculate VCA of Share lower page from X Y
		uwPage = RetrySBInvertCoord(ubTempX, uwY);
		//Update Target FSA uwPage element to gen FSA of Share Lower page
		//PCA < -> FSA use same format, the different should only be value of "Block"
		gSBTask.ulShareUpperFSA_Align &= ~(gPCARule_Page.ulMask << gPCARule_Page.ubShift[COP0_PCA_RULE_0]);
		gSBTask.ulShareUpperFSA_Align |= (uwPage << gPCARule_Page.ubShift[COP0_PCA_RULE_0]);
	}

}

void RetrySBSelectDMAMTFPUTrigger( U16 uwFpu_offset, U32 ulFSA, U8 ubMTIndex)
{

	FlhMT_t *mtp;
	MTCfg_t uoMTCfg;
	U8 ubIdx;
	L4KTable16B_t *pul4k_ptr;

	////------------------------------------------------------------
	mtp = (FlhMT_t *)M_MT_ADDR(ubMTIndex);

	////------------------------------------------------------------
	pul4k_ptr = (L4KTable16B_t *)((U32) gSBTask.ultmp_SpareAddr);

	memcpy((void *) & (gSBTask.MT), (void *) & (gSBTask.MTTemplate), sizeof(FlhMT_t));


	gSBTask.MT.dma.btForce_R_Fail = 0;

	gSBTask.MT.dma.uliFSA0_1 = ulFSA;

	gSBTask.MT.dma.btBMUAllocateEn = 0;
	gSBTask.MT.dma.uwFPUPtr  = uwFpu_offset;
	gSBTask.MT.cmd.btBusy = 1;
	gSBTask.MT.cmd.btUpdPollingSequence = 1;
	M_FIP_SET_POL_SEQUENCE_SELECT(gSBTask.MT.cmd.POL_SEQ_SEL, (POL_SEQ_FPU_ENTRY_READ_STATUS_LUN0_BUSY_20 + gSBTask.ublun));

	gSBTask.MT.dma.btConversionBypass = 1;
	gSBTask.MT.dma.btGC = 0;

	gSBTask.MT.dma.btLDPCCorrectEn = 0;
	gSBTask.MT.dma.btCRCCheckDis = 1;
	gSBTask.MT.dma.btDisableUDMA = 1;

	//Micron SB Todo
	if ( (gSBTask.ubThirdstate == RETRY_SB_3RD_STATE_HB_READ_DIRECT_READ_DMA)
		|| (gSBTask.ubThirdstate == RETRY_SB_3RD_STATE_SB_READ_SB1_PLUS_MINUS_2_DELTA_DMA)
		|| (gSBTask.ubThirdstate == RETRY_SB_3RD_STATE_SB_READ_SB2_PLUS_MINUS_3_DELTA_DMA)
		|| (gSBTask.ubThirdstate == RETRY_SB_3RD_STATE_SB_READ_SB2_PLUS_MINUS_1_DELTA_DMA_WITH_XNOR)
		|| (gSBTask.ubThirdstate == RETRY_SB_3RD_STATE_SB_READ_SB3_DMA)
		|| (gSBTask.ubThirdstate == RETRY_SB_3RD_STATE_SB_READ_SB4_DMA)
	) {
		gSBTask.MT.dma.ADGSelectPointer = gSBTask.ubCurrentLDPCFrameIdx;
	}
	else {
		gSBTask.MT.dma.ADGSelectPointer = 0;
	}

#if (PS5017_EN || PS5021_EN)
	gSBTask.MT.dma.btFpuPCAEn = 1;
#endif /*(PS5017_EN || PS5021_EN)*/
	gSBTask.MT.dma.btAllowSwitch = 0;
	gSBTask.MT.dma.L4KNum = 1;
	gSBTask.MT.dma.FrameNum = 1;
	gSBTask.MT.dma.btZipEn = 0;
	gSBTask.MT.dma.btCompareEn = 0;
	gSBTask.MT.dma.btBufferMode = 0;
	gSBTask.MT.dma.L4KSparePtr = ( gSBTask.ultmp_SpareAddr) & 0xFFFF;


	ubIdx = gSBTask.ubCurr_frm_idx;
	pul4k_ptr = (L4KTable16B_t *)((U32)(gSBTask.ultmp_SpareAddr));
	pul4k_ptr->BitMap.Read.FW = 0;
	pul4k_ptr->BitMap.Read.Zinfo = MAX_ZINFO;
	pul4k_ptr->BitMap.Read.ubBufferValid = 0xFF;
#if (PS5017_EN || PS5013_EN)
	pul4k_ptr->BitMap.Read.uwL4kNextPtr = 0xFFFF;
#endif /* (PS5017_EN || PS5013_EN) */
	pul4k_ptr->BitMap.Read.BADR = ((U32)gSBTask.ultmp_addr) >> SECTOR_SIZE_SHIFT;
	///// do global trigger

	//set MTQ_ID
	gSBTask.MT.cmd.btCESelectMode = 1;
	gSBTask.MT.cmd.ubCEValue = gSBTask.MTTemplate.cmd.ubCEValue;
	gSBTask.MT.cmd.NormalTargetCPU = MT_TARGET_CPU0;
	gSBTask.MT.cmd.ErrorTargetCPU = MT_TARGET_CPU0;

	memcpy((void *)mtp, (void *) & (gSBTask.MT), sizeof(FlhMT_t));

	// Global Trigger MT
	uoMTCfg.uoAll = 0;
	uoMTCfg.bits_recc.ubQUE_IDX = gSBTask.MTTemplate.cmd.ubCEValue;
	uoMTCfg.bits_recc.ubMT_IDX = ubMTIndex;
	uoMTCfg.bits_recc.btQOS =  gSBTask.ubUseQorOrNot;
	uoMTCfg.u32.ulMT_CFG1 = 0;
	FlaGlobalTrigger(&uoMTCfg);
}

void RetrySBSetReadLevelOffset(U8 *ubReadOffsetTable)
{
	U16 *puwfpu_ptr = NULL;
	U8 ubMTIndex = 0;
	U16 uwFpu_offset = 0;
	U8 ubSetFeatureRoundCnt = 0;
	U8 ubSetFeatureAddress = 0;
	U8 ubDeltaTableIndex = 0;
	U8 ubi = 0;

	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_SET_READ_LEVEL_OFFSET_INIT:
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SET_READ_LEVEL_OFFSET_I;
	/* no break */
	case RETRY_SB_3RD_STATE_SET_READ_LEVEL_OFFSET_I:
	case RETRY_SB_3RD_STATE_SET_READ_LEVEL_OFFSET_II:
	case RETRY_SB_3RD_STATE_SET_READ_LEVEL_OFFSET_III:
	case RETRY_SB_3RD_STATE_SET_READ_LEVEL_OFFSET_IV:
		ubSetFeatureRoundCnt = gSBTask.ubThirdstate - RETRY_SB_3RD_STATE_SET_READ_LEVEL_OFFSET_I;
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, ubSetFeatureRoundCnt < SB_MICRON_MAX_READ_OFFSET_FEATURE_NUM);

		ubSetFeatureAddress = gubMicronReadOffsetFeatureAddress[gSBTask.ubTempPageType][ubSetFeatureRoundCnt];
		ubDeltaTableIndex = gubMircronPageTypesReadOffsetIndexOnDeltaTable[gSBTask.ubTempPageType][ubSetFeatureRoundCnt];

		//SB MICRON Check Point 0806
		M_UART(ERROR_SOFTBIT_, "\n PageType:%d, ubDeltaTableIndex = %d", gSBTask.ubTempPageType, ubDeltaTableIndex);

		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, 0xFF != ubSetFeatureAddress);
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, 0xFF != ubDeltaTableIndex);

		uwFpu_offset = FPU_PTR_OFFSET(fpu_set_feature);
		puwfpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);
		/// reset fpu entry
		for (ubi = 0 ; ubi < SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH ; ubi++) {
			if (0 == ubi) {
				puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (ubi << 1)] = FPU_DAT_W_FIR_D(0x00);
			}
			else if ((SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH - 1) == ubi) {
				puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (ubi << 1)] = FPU_DAT_W_LAST_D(0x00);
			}
			else {
				puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (ubi << 1)] = FPU_DAT_W_MID_D(0x00);
			}
			puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (ubi << 1) + 1] = FPU_DAT_W(0x00);
		}
		puwfpu_ptr[0] = FPU_CMD(0xD5);				//set_feature
		puwfpu_ptr[1] = FPU_ADR_1B(gSBTask.ublun);
		if (FIP_FLASH_CLOCK_400MHZ == gFlhEnv.ubTargetFlashClock) {
			// flh clk 400
			puwfpu_ptr[3] = FPU_DLY(0x15);		// Decimal 21
		}
		else if (FIP_FLASH_CLOCK_333MHZ == gFlhEnv.ubTargetFlashClock) {
			// flh clk 333
			puwfpu_ptr[3] = FPU_DLY(0xF);		// Decimal 15
		}
		else {
			puwfpu_ptr[3] = FPU_DLY(0x10);		// Decimal 16
		}

		puwfpu_ptr[2] = FPU_ADR_1B(ubSetFeatureAddress);

		puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P1] = FPU_DAT_W_FIR_D(ubReadOffsetTable[SB_MICRON_DELTA_TABLE_ORIGIN_SHIFT * SB_MICRON_RETRY_VTH_LENGTH + ubDeltaTableIndex]);
		puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P1 + 1] = FPU_DAT_W(ubReadOffsetTable[SB_MICRON_DELTA_TABLE_ORIGIN_SHIFT * SB_MICRON_RETRY_VTH_LENGTH + ubDeltaTableIndex]);
		//Micron SB Todo
		puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_END_IDX] = FPU_END;

		break;
	case RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_I:
	case RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_II:
	case RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_III:
	case RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_IV:
		ubSetFeatureRoundCnt = gSBTask.ubThirdstate - RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_I;
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, ubSetFeatureRoundCnt < SB_MICRON_MAX_READ_OFFSET_FEATURE_NUM);

		ubSetFeatureAddress = gubMicronReadOffsetFeatureAddress[gSBTask.ubTempPageType][ubSetFeatureRoundCnt];
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, 0xFF != ubSetFeatureAddress);

		uwFpu_offset = FPU_PTR_OFFSET(fpu_get_feature);
		puwfpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);

		puwfpu_ptr[0] = FPU_CMD(0xD4);				//get_feature
		puwfpu_ptr[1] = FPU_ADR_1B(gSBTask.ublun);

		puwfpu_ptr[2] = FPU_ADR_1B(ubSetFeatureAddress);

		puwfpu_ptr[SB_MICRON_GET_FEATURE_FPU_END_IDX] = FPU_END;
		break;

	case RETRY_SB_3RD_STATE_CHECK_READ_LEVEL_OFFSET_I:
	case RETRY_SB_3RD_STATE_CHECK_READ_LEVEL_OFFSET_II:
	case RETRY_SB_3RD_STATE_CHECK_READ_LEVEL_OFFSET_III:
	case RETRY_SB_3RD_STATE_CHECK_READ_LEVEL_OFFSET_IV:
		ubSetFeatureRoundCnt = gSBTask.ubThirdstate - RETRY_SB_3RD_STATE_CHECK_READ_LEVEL_OFFSET_I;
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, ubSetFeatureRoundCnt < SB_MICRON_MAX_READ_OFFSET_FEATURE_NUM);

		ubDeltaTableIndex = gubMircronPageTypesReadOffsetIndexOnDeltaTable[gSBTask.ubTempPageType][ubSetFeatureRoundCnt];
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, 0xFF != ubDeltaTableIndex);

		uwFpu_offset = FPU_PTR_OFFSET(fpu_read_and_compare_feature_data);
		puwfpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);

		puwfpu_ptr[0] = FPU_CMD(0x00);		//check_feature
		puwfpu_ptr[1] = FPU_DLY(0x10);

		//Clear <P1><P2><P3><P3> Compare mask
		for (ubi = 0; ubi < PARAMETER_NUM_PER_FPU; ubi++) {
			puwfpu_ptr[SB_MICRON_CHECK_FEATURE_FPU_START_IDX + (ubi << 1)] =  FPU_DAT_R_CMP(0x00);
			puwfpu_ptr[(SB_MICRON_CHECK_FEATURE_FPU_START_IDX + 1) + (ubi << 1)] = FPU_DAT_R_MASK(0x00);
		}

		//Compare <P1>
		puwfpu_ptr[SB_MICRON_CHECK_FEATURE_FPU_START_IDX] = FPU_DAT_R_CMP(ubReadOffsetTable[SB_MICRON_DELTA_TABLE_ORIGIN_SHIFT * SB_MICRON_RETRY_VTH_LENGTH + ubDeltaTableIndex]);
		puwfpu_ptr[(SB_MICRON_CHECK_FEATURE_FPU_START_IDX + 1)] = FPU_DAT_R_MASK(0xFF);
		break;
	}
	if (NES_EN || NCS_EN) {
		ubMTIndex = RetrySBPrepareFreeMT(RetrySBSetReadLevelOffsetPass_Callback, RetrySBSetReadLevelOffsetPass_Callback);
	}
	else {
		ubMTIndex = RetrySBPrepareFreeMT(RetrySBSetReadLevelOffsetPass_Callback, RetrySBSetReadLevelOffsetFail_Callback);
	}
	RetrySBSelecMTFPUTrigger(uwFpu_offset, (U32)( (gSBTask.ulFSA_Align) + (gSBTask.ubCurr_frm_idx) ), 0, ubMTIndex, SWITCH_CLK__LOW_CLK_MODE);

	if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
		gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
	}
	else {
		gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
		gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
	}
}


void RetrySBGetReadLevelOffsetFromARC(void)
{
	U8 ubSetFeatureRoundCnt = 0;
	U8 ubSetFeatureAddress = 0;
	U8 ubDeltaTableIndex = 0;
	U8 ubDeltaTableTempIndex = 0;

	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_FROM_ARC_INIT:
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_FROM_ARC_I;
	/* no break */
	case RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_FROM_ARC_I:
	case RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_FROM_ARC_II:
	case RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_FROM_ARC_III:
	case RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_FROM_ARC_IV:
		ubSetFeatureRoundCnt = gSBTask.ubThirdstate - RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_FROM_ARC_I;
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, ubSetFeatureRoundCnt < SB_MICRON_MAX_READ_OFFSET_FEATURE_NUM);

		ubSetFeatureAddress = gubMicronReadOffsetFeatureAddress[gSBTask.ubPageType][ubSetFeatureRoundCnt];
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, 0xFF != ubSetFeatureAddress);

		//Micron SB Todo
		//Wait FIP_CH_FPU_TRIGGER to 0x04, only in SB (other Queue empty)
		while (SRQ_SVL_BIT != (M_FIP_GET_FPU_TRIG(gSBTask.ubChannel) & (MT_BSY_BIT | ANY_BSY_BIT | SRQ_SVL_BIT | SIGN_OFF_BUSY_BIT | FPU_TRIGGER_BIT)));
		FlaGetFeature(gSBTask.ubChannel, gSBTask.ubBank, ubSetFeatureAddress, &gSBTask.ubGetFeatureResult[0], FIP_NO_USE_PARAMETER, FIP_NO_USE_PARAMETER, FALSE, FIP_NO_USE_PARAMETER, FIP_NO_USE_PARAMETER);

		//Get Feature(N) done -> Update ARC_READ_LEVEL_Table(N)
		gSBTask.ubThirdstate = ubSetFeatureRoundCnt  +  RETRY_SB_3RD_STATE_UPDATE_ARC_READ_LEVEL_OFFSET_TABLE_I;
	/* no break */
	case RETRY_SB_3RD_STATE_UPDATE_ARC_READ_LEVEL_OFFSET_TABLE_I:
	case RETRY_SB_3RD_STATE_UPDATE_ARC_READ_LEVEL_OFFSET_TABLE_II:
	case RETRY_SB_3RD_STATE_UPDATE_ARC_READ_LEVEL_OFFSET_TABLE_III:
	case RETRY_SB_3RD_STATE_UPDATE_ARC_READ_LEVEL_OFFSET_TABLE_IV:
		//Micron SB Todo
		ubSetFeatureRoundCnt = gSBTask.ubThirdstate - RETRY_SB_3RD_STATE_UPDATE_ARC_READ_LEVEL_OFFSET_TABLE_I;
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, ubSetFeatureRoundCnt < SB_MICRON_MAX_READ_OFFSET_FEATURE_NUM);

		ubDeltaTableIndex = gubMircronPageTypesReadOffsetIndexOnDeltaTable[gSBTask.ubPageType][ubSetFeatureRoundCnt];
		//Micron SB Todo
		gubARCResultReadOffsetTable[SB_MICRON_DELTA_TABLE_ORIGIN_SHIFT][ubDeltaTableIndex] = gSBTask.ubGetFeatureResult[SB_MICRON_FEATURE_PARAM_ARC_RESULT];

		if (((ubSetFeatureRoundCnt + 1) == gubMicronReadOffsetFeatureAddressNumber[gSBTask.ubPageType])) {	//Different PageType End in corresbounding RouncCnt
			//Switch to Next SB State
			switch (gSBTask.ubSubstate) {
			case RETRY_SB_SUBSTATE_SB_DSP2_GET_FEATURE_OF_ARC_RESULT:
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_DSP2_SET_READ_LEVEL_FOR_NEXT_WORDLINE_I;
				gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SET_READ_LEVEL_OFFSET_INIT;
				switch (gSBTask.ubPageType) {
				case RETRY_SB_LOWER_PAGE:
					ubDeltaTableIndex = gubMircronPageTypesReadOffsetIndexOnDeltaTable[gSBTask.ubPageType][0];
					//Upper	//FA = A6h, AAh
					ubDeltaTableTempIndex = gubMircronPageTypesReadOffsetIndexOnDeltaTable[RETRY_SB_UPPER_PAGE][0];
					gubARCResultReadOffsetTable[SB_MICRON_DELTA_TABLE_ORIGIN_SHIFT][ubDeltaTableTempIndex] = gubARCResultReadOffsetTable[SB_MICRON_DELTA_TABLE_ORIGIN_SHIFT][ubDeltaTableIndex];
					ubDeltaTableTempIndex = gubMircronPageTypesReadOffsetIndexOnDeltaTable[RETRY_SB_UPPER_PAGE][1];
					gubARCResultReadOffsetTable[SB_MICRON_DELTA_TABLE_ORIGIN_SHIFT][ubDeltaTableTempIndex] = gubARCResultReadOffsetTable[SB_MICRON_DELTA_TABLE_ORIGIN_SHIFT][ubDeltaTableIndex];
					//eXta	//FA = A5h, A7h, A9h, ABh
					ubDeltaTableTempIndex = gubMircronPageTypesReadOffsetIndexOnDeltaTable[RETRY_SB_EXTRA_PAGE][0];
					gubARCResultReadOffsetTable[SB_MICRON_DELTA_TABLE_ORIGIN_SHIFT][ubDeltaTableTempIndex] = gubARCResultReadOffsetTable[SB_MICRON_DELTA_TABLE_ORIGIN_SHIFT][ubDeltaTableIndex];
					ubDeltaTableTempIndex = gubMircronPageTypesReadOffsetIndexOnDeltaTable[RETRY_SB_EXTRA_PAGE][1];
					gubARCResultReadOffsetTable[SB_MICRON_DELTA_TABLE_ORIGIN_SHIFT][ubDeltaTableTempIndex] = gubARCResultReadOffsetTable[SB_MICRON_DELTA_TABLE_ORIGIN_SHIFT][ubDeltaTableIndex];
					ubDeltaTableTempIndex = gubMircronPageTypesReadOffsetIndexOnDeltaTable[RETRY_SB_EXTRA_PAGE][2];
					gubARCResultReadOffsetTable[SB_MICRON_DELTA_TABLE_ORIGIN_SHIFT][ubDeltaTableTempIndex] = gubARCResultReadOffsetTable[SB_MICRON_DELTA_TABLE_ORIGIN_SHIFT][ubDeltaTableIndex];
					ubDeltaTableTempIndex = gubMircronPageTypesReadOffsetIndexOnDeltaTable[RETRY_SB_EXTRA_PAGE][3];
					gubARCResultReadOffsetTable[SB_MICRON_DELTA_TABLE_ORIGIN_SHIFT][ubDeltaTableTempIndex] = gubARCResultReadOffsetTable[SB_MICRON_DELTA_TABLE_ORIGIN_SHIFT][ubDeltaTableIndex];

					gSBTask.NextWordLineSharePage[1].ubSharePageType = RETRY_SB_UPPER_PAGE;
					gSBTask.NextWordLineSharePage[2].ubSharePageType = RETRY_SB_EXTRA_PAGE;
					break;

				case RETRY_SB_UPPER_PAGE:
					ubDeltaTableIndex = gubMircronPageTypesReadOffsetIndexOnDeltaTable[gSBTask.ubPageType][0];
					//eXtra	//FA = A5h, A7h
					ubDeltaTableTempIndex = gubMircronPageTypesReadOffsetIndexOnDeltaTable[RETRY_SB_EXTRA_PAGE][0];
					gubARCResultReadOffsetTable[SB_MICRON_DELTA_TABLE_ORIGIN_SHIFT][ubDeltaTableTempIndex] = gubARCResultReadOffsetTable[SB_MICRON_DELTA_TABLE_ORIGIN_SHIFT][ubDeltaTableIndex];
					ubDeltaTableTempIndex = gubMircronPageTypesReadOffsetIndexOnDeltaTable[RETRY_SB_EXTRA_PAGE][1];
					gubARCResultReadOffsetTable[SB_MICRON_DELTA_TABLE_ORIGIN_SHIFT][ubDeltaTableTempIndex] = gubARCResultReadOffsetTable[SB_MICRON_DELTA_TABLE_ORIGIN_SHIFT][ubDeltaTableIndex];

					ubDeltaTableIndex = gubMircronPageTypesReadOffsetIndexOnDeltaTable[gSBTask.ubPageType][1];
					//Lower //FA = A8h
					ubDeltaTableTempIndex = gubMircronPageTypesReadOffsetIndexOnDeltaTable[RETRY_SB_LOWER_PAGE][0];
					gubARCResultReadOffsetTable[SB_MICRON_DELTA_TABLE_ORIGIN_SHIFT][ubDeltaTableTempIndex] = gubARCResultReadOffsetTable[SB_MICRON_DELTA_TABLE_ORIGIN_SHIFT][ubDeltaTableIndex];
					//eXtra	//FA = A9h, ABh
					ubDeltaTableTempIndex = gubMircronPageTypesReadOffsetIndexOnDeltaTable[RETRY_SB_EXTRA_PAGE][2];
					gubARCResultReadOffsetTable[SB_MICRON_DELTA_TABLE_ORIGIN_SHIFT][ubDeltaTableTempIndex] = gubARCResultReadOffsetTable[SB_MICRON_DELTA_TABLE_ORIGIN_SHIFT][ubDeltaTableIndex];
					ubDeltaTableTempIndex = gubMircronPageTypesReadOffsetIndexOnDeltaTable[RETRY_SB_EXTRA_PAGE][3];
					gubARCResultReadOffsetTable[SB_MICRON_DELTA_TABLE_ORIGIN_SHIFT][ubDeltaTableTempIndex] = gubARCResultReadOffsetTable[SB_MICRON_DELTA_TABLE_ORIGIN_SHIFT][ubDeltaTableIndex];

					gSBTask.NextWordLineSharePage[1].ubSharePageType = RETRY_SB_LOWER_PAGE;
					gSBTask.NextWordLineSharePage[2].ubSharePageType = RETRY_SB_EXTRA_PAGE;
					break;

				case RETRY_SB_EXTRA_PAGE:
					ubDeltaTableIndex = gubMircronPageTypesReadOffsetIndexOnDeltaTable[gSBTask.ubPageType][1];	//Start from A7h
					//Upper	//FA = A6h
					ubDeltaTableTempIndex = gubMircronPageTypesReadOffsetIndexOnDeltaTable[RETRY_SB_UPPER_PAGE][0];
					gubARCResultReadOffsetTable[SB_MICRON_DELTA_TABLE_ORIGIN_SHIFT][ubDeltaTableTempIndex] = gubARCResultReadOffsetTable[SB_MICRON_DELTA_TABLE_ORIGIN_SHIFT][ubDeltaTableIndex];

					ubDeltaTableIndex = gubMircronPageTypesReadOffsetIndexOnDeltaTable[gSBTask.ubPageType][2];
					//Lower //FA = A8h
					ubDeltaTableTempIndex = gubMircronPageTypesReadOffsetIndexOnDeltaTable[RETRY_SB_LOWER_PAGE][0];
					gubARCResultReadOffsetTable[SB_MICRON_DELTA_TABLE_ORIGIN_SHIFT][ubDeltaTableTempIndex] = gubARCResultReadOffsetTable[SB_MICRON_DELTA_TABLE_ORIGIN_SHIFT][ubDeltaTableIndex];

					ubDeltaTableIndex = gubMircronPageTypesReadOffsetIndexOnDeltaTable[gSBTask.ubPageType][3];
					//Upper	//FA = A6h
					ubDeltaTableTempIndex = gubMircronPageTypesReadOffsetIndexOnDeltaTable[RETRY_SB_UPPER_PAGE][1];
					gubARCResultReadOffsetTable[SB_MICRON_DELTA_TABLE_ORIGIN_SHIFT][ubDeltaTableTempIndex] = gubARCResultReadOffsetTable[SB_MICRON_DELTA_TABLE_ORIGIN_SHIFT][ubDeltaTableIndex];

					gSBTask.NextWordLineSharePage[1].ubSharePageType = RETRY_SB_LOWER_PAGE;
					gSBTask.NextWordLineSharePage[2].ubSharePageType = RETRY_SB_UPPER_PAGE;
					break;
				}
				break;
			}
		}
		else {
			//Continue setting next FA of read level offset
			gSBTask.ubThirdstate = (ubSetFeatureRoundCnt + 1) + RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_FROM_ARC_I;
		}

		break;
	}

}


void RetrySBSetReadLevelOffsetPass_Callback(U8 ubMTIndex)
{
	U8 ubSetFeatureRoundCnt = 0;
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
	gSBTask.ubMTFailCnt = 0;
	gSBTask.ubState = gSBTask.ubPrevState;

	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_SET_READ_LEVEL_OFFSET_I:
	case RETRY_SB_3RD_STATE_SET_READ_LEVEL_OFFSET_II:
	case RETRY_SB_3RD_STATE_SET_READ_LEVEL_OFFSET_III:
	case RETRY_SB_3RD_STATE_SET_READ_LEVEL_OFFSET_IV:
		ubSetFeatureRoundCnt = gSBTask.ubThirdstate - RETRY_SB_3RD_STATE_SET_READ_LEVEL_OFFSET_I;
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, ubSetFeatureRoundCnt < SB_MICRON_MAX_READ_OFFSET_FEATURE_NUM);
		//Set Feature(N) done -> Get Feature(N)
		gSBTask.ubThirdstate = ubSetFeatureRoundCnt  +  RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_I;
		break;

	case RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_I:
	case RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_II:
	case RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_III:
	case RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_IV:
		ubSetFeatureRoundCnt = gSBTask.ubThirdstate - RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_I;
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, ubSetFeatureRoundCnt < SB_MICRON_MAX_READ_OFFSET_FEATURE_NUM);
		//Set Feature(N) done -> Get Feature(N)
		gSBTask.ubThirdstate = ubSetFeatureRoundCnt  +  RETRY_SB_3RD_STATE_CHECK_READ_LEVEL_OFFSET_I;
		break;

	case RETRY_SB_3RD_STATE_CHECK_READ_LEVEL_OFFSET_I:
	case RETRY_SB_3RD_STATE_CHECK_READ_LEVEL_OFFSET_II:
	case RETRY_SB_3RD_STATE_CHECK_READ_LEVEL_OFFSET_III:
	case RETRY_SB_3RD_STATE_CHECK_READ_LEVEL_OFFSET_IV:
		ubSetFeatureRoundCnt = gSBTask.ubThirdstate - RETRY_SB_3RD_STATE_CHECK_READ_LEVEL_OFFSET_I;
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, ubSetFeatureRoundCnt < SB_MICRON_MAX_READ_OFFSET_FEATURE_NUM);
		if (((ubSetFeatureRoundCnt + 1) == gubMicronReadOffsetFeatureAddressNumber[gSBTask.ubTempPageType])) {	//Different PageType End in corresbounding RouncCnt
			//Switch to Next SB State
			switch (gSBTask.ubSubstate) {
			case RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_SET_READ_LEVEL_OFFSET:
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_ENABLE_AUTO_READ_CALIBRATION;
				gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_SET_FEATURE;
				gSBTask.ubARCRoundCnt = 0;
				break;
			case RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_WITH_SPECIFIC_RETRY_TABLE_READ_OFFSET_SET_FEATURE:
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_WITH_SPECIFIC_RETRY_TABLE_READ_OFFSET_HB_READ;
				gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_HB_READ_INIT;
				break;
			case RETRY_SB_SUBSTATE_SB_DSP2_SET_READ_LEVEL_FOR_NEXT_WORDLINE_I:
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_DSP2_SET_READ_LEVEL_FOR_NEXT_WORDLINE_II;
				gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SET_READ_LEVEL_OFFSET_INIT;
				break;
			case RETRY_SB_SUBSTATE_SB_DSP2_SET_READ_LEVEL_FOR_NEXT_WORDLINE_II:
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_DSP2_ENABLE_ARC_DISABLE_PERSISTENCE;
				gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_SET_FEATURE;
				break;
			case RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_READ_OFFSET_LOWER:
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_READ_OFFSET_UPPER;
				gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SET_READ_LEVEL_OFFSET_INIT;
				break;
			case RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_READ_OFFSET_UPPER:
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_READ_OFFSET_EXTRA;
				gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SET_READ_LEVEL_OFFSET_INIT;
				break;
			case RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_READ_OFFSET_EXTRA:
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_SB_READ_OFFSET;
				gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SB_CLEAR_SB_OFFSET_INIT;
				break;
			}
		}
		else {
			//Continue setting next FA of read level offset
			gSBTask.ubThirdstate = (ubSetFeatureRoundCnt + 1) + RETRY_SB_3RD_STATE_SET_READ_LEVEL_OFFSET_I;
		}
		break;
	}
}

void RetrySBSetReadLevelOffsetFail_Callback(U8 ubMTIndex)
{
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
	gSBTask.ubState = gSBTask.ubPrevState;

	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_SET_READ_LEVEL_OFFSET_I:
	case RETRY_SB_3RD_STATE_SET_READ_LEVEL_OFFSET_II:
	case RETRY_SB_3RD_STATE_SET_READ_LEVEL_OFFSET_III:
	case RETRY_SB_3RD_STATE_SET_READ_LEVEL_OFFSET_IV:

	case RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_I:
	case RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_II:
	case RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_III:
	case RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_IV:
		RetrySBMTTriggerRetry(FALSE);
		break;


	case RETRY_SB_3RD_STATE_CHECK_READ_LEVEL_OFFSET_I:
	case RETRY_SB_3RD_STATE_CHECK_READ_LEVEL_OFFSET_II:
	case RETRY_SB_3RD_STATE_CHECK_READ_LEVEL_OFFSET_III:
	case RETRY_SB_3RD_STATE_CHECK_READ_LEVEL_OFFSET_IV:
		if (gSBTask.ubMTCheckFeatureFailCnt < RETRY_SB_MT_FAIL_RETRY_CNT_MAX) {
			gSBTask.ubMTCheckFeatureFailCnt++;
			switch (gSBTask.ubThirdstate) {
			case RETRY_SB_3RD_STATE_CHECK_READ_LEVEL_OFFSET_I:
				gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SET_READ_LEVEL_OFFSET_I;
				break;
			case RETRY_SB_3RD_STATE_CHECK_READ_LEVEL_OFFSET_II:
				gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SET_READ_LEVEL_OFFSET_II;
				break;
			case RETRY_SB_3RD_STATE_CHECK_READ_LEVEL_OFFSET_III:
				gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SET_READ_LEVEL_OFFSET_III;
				break;
			case RETRY_SB_3RD_STATE_CHECK_READ_LEVEL_OFFSET_IV:
				gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SET_READ_LEVEL_OFFSET_IV;
				break;
			}
		}
		else {
			gSBTask.ubMTCheckFeatureFailCnt = 0;
			gpVTDBUF->Retry.ulSetFeatureFailCnt++;
#if (!RELEASED_FW)
			DebugError();
#endif
			//If Set Get CHK feature fail, try to set to default feature, then Reset FLH
			RetrySBSwitchStateToSetDefaultFeatureReset();
		}
		break;
	}
}


void RetrySBGetReadLevelOffsetFromARCPass_Callback(U8 ubMTIndex)
{
	U8 ubSetFeatureRoundCnt = 0;
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
	gSBTask.ubMTFailCnt = 0;
	gSBTask.ubState = gSBTask.ubPrevState;

	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_FROM_ARC_I:
	case RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_FROM_ARC_II:
	case RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_FROM_ARC_III:
	case RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_FROM_ARC_IV:
		ubSetFeatureRoundCnt = gSBTask.ubThirdstate - RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_FROM_ARC_I;
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, ubSetFeatureRoundCnt < SB_MICRON_MAX_READ_OFFSET_FEATURE_NUM);
		//Get Feature(N) done -> Update ARC_READ_LEVEL_Table(N)
		gSBTask.ubSubstate = ubSetFeatureRoundCnt  +  RETRY_SB_3RD_STATE_UPDATE_ARC_READ_LEVEL_OFFSET_TABLE_I;
		break;
	}
}

void RetrySBReadHB( U8 page_type, U8 dsp_en, U32 ulFSA, U8 ldpc_frame_idx, U8 src_ibuf, U8 dst_ibuf, U8 ibuf_frame_sel)
{
	U16 *fpu_ptr = NULL;

	U8 ubMTIndex;
	U16 uwFpu_offset;

	/*RAW_LOGIC = 2 (NOP), src_ibuf and dst_ibuf are NOT allowed to assign the identical value, By FIP Spec*/
	if (src_ibuf == dst_ibuf) {
		src_ibuf = (dst_ibuf + 1) % 4;		// 4 for IBUF Number
	}

	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_HB_READ_INIT:
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_HB_READ_DIRECT_READ_OP;
		gSBTask.ubDsp_en = dsp_en;
	/* no break */
	case RETRY_SB_3RD_STATE_HB_READ_DIRECT_READ_OP:
		uwFpu_offset = FPU_PTR_TLC_1P_READ_CMD;
		ubMTIndex = RetrySBPrepareFreeMT(RetrySBReadHBPass_Callback, RetrySBReadHBFail_Callback);
		RetrySBSelecMTFPUTrigger( uwFpu_offset, ulFSA, 0, ubMTIndex, SWITCH_CLK__NORMAL_MODE);
		if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
			gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
		}
		else {
			gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
			gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
		}
		break;

	case RETRY_SB_3RD_STATE_HB_READ_DIRECT_READ_DMA:
		uwFpu_offset = FPU_PTR_OFFSET(fpu_raw_dma_read);
		fpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);
		fpu_ptr[7] =  FPU_DMA_R_RAW((ibuf_frame_sel << RAW_MODE_OFFSET) | (2 << RAW_LOGIC_OFFSET) | (src_ibuf << RAW_SRC_PTR_OFFSET)
				| (dst_ibuf << RAW_DST_PTR_OFFSET));

		ubMTIndex = RetrySBPrepareFreeMT(RetrySBReadHBPass_Callback, RetrySBReadHBFail_Callback);
		RetrySBSelectDMAMTFPUTrigger( uwFpu_offset, ulFSA, ubMTIndex);
		if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
			gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
		}
		else {
			gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
			gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
		}
		break;
	}
}


void RetrySBReadHBPass_Callback(U8 ubMTIndex)
{
	gSBTask.ubMTFailCnt = 0;
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
	gSBTask.ubState =  gSBTask.ubPrevState;

	switch (gSBTask.ubThirdstate) {

	case RETRY_SB_3RD_STATE_HB_READ_DIRECT_READ_OP:
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_HB_READ_DIRECT_READ_DMA;
		break;

	case  RETRY_SB_3RD_STATE_HB_READ_DIRECT_READ_DMA:
		if ( gSBTask.ubDsp_en) {
			switch (gSBTask.ubSubstate) {
			case RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_HB_READ:
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_HB_CORR_2K;
				gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_NULL;
				break;
			case RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_WITH_SPECIFIC_RETRY_TABLE_READ_OFFSET_HB_READ:
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_WITH_SPECIFIC_RETRY_TABLE_READ_OFFSET_HB_CORR_2K;
				gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_NULL;
				break;
			case RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_HB:
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_SB1;
				gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SB_READ_SB1_INIT;
				break;
			case RETRY_SB_SUBSTATE_SB_DSP2_READ_TARGET_PAGE:
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_DSP2_GET_FEATURE_OF_ARC_RESULT;
				gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_GET_READ_LEVEL_OFFSET_FROM_ARC_INIT;
				break;
			case RETRY_SB_SUBSTATE_SB_DSP2_READ_NEXT_WORDLINE_SHARE_PAGE_I:
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_DSP2_READ_NEXT_WORDLINE_SHARE_PAGE_II;
				gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_HB_READ_INIT;
				break;
			case RETRY_SB_SUBSTATE_SB_DSP2_READ_NEXT_WORDLINE_SHARE_PAGE_II:
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_DSP2_GEN_SB6_SB7;
				gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_NULL;
				break;
			case RETRY_SB_SUBSTATE_SB_DSP2_READ_LEVEL_HB:
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_DSP2_READ_LEVEL_SB1;
				gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SB_READ_SB1_INIT;
				break;
			}
		}
		else {
			//Micron SB Todo , for DSP2
			switch (gSBTask.ubSubstate) {

			}
			if (gSBTask.ubState == RETRY_SB_STATE_READ_GOOD_2K_TO_BUF) {
				RetrySBBackupCorrect2k(0, gSBTask.ubCurrentLDPCFrameIdx,            \
					gSBTask.ulbackup_addr, gSBTask.ultmp_addr,   \
					gSBTask.ulSpareAddr, gSBTask.ultmp_SpareAddr, RETRY_SB_BACKUP_RESTORE_TO_DRAMIRAM);
				gSBTask.ubState = RETRY_SB_STATE_CHECK_NEXT_STATE;
			}
		}
		break;
	}
}


void RetrySBReadHBFail_Callback(U8 ubMTIndex)
{
	gSBTask.ubState =  gSBTask.ubPrevState;
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_HB_READ_DIRECT_READ_OP:
		RetrySBMTTriggerRetry(FALSE);
		break;
	case RETRY_SB_3RD_STATE_HB_READ_DIRECT_READ_DMA:
		RetrySBReadHBPass_Callback(ubMTIndex);
		break;
	}
}


void RetrySBTriggerCorrectPass_Callback(U8 ubMTIndex)
{
	///NCS Debug
	U16 *puwfpu_ptr = NULL;
	U16 *puwNCSDebug = NULL;
	U32 *pulNCSDebug = NULL;
	U8  *pubNCSDebug = NULL;
	U16 uwFailCnt;
	U8 ubIdx;
	// NCS Debug
	U8 ubNCSCheckRound;
	U8 ubNCSCheckTableIdx;
	U8 ubTempState = 0;
	U8 ubllr_indexTemp = 0;

	ubTempState = gSBTask.ubSubstate;

	gSBTask.ubMTFailCnt = 0;
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
	gSBTask.ubState =  gSBTask.ubPrevState;
	///while ((R32_FCON[R32_FCON_LDPC_SBC] & ((BIT0 << ( gSBTask.ubChannel + SBC_FINISH_SHIFT)) | SBC_BSY_MASK)) != (BIT0 << ( gSBTask.ubChannel + SBC_FINISH_SHIFT)));
	while ((R32_FCON[R32_FCON_LDPC_SBC] & ((SBC_BSY_MASK) << SBC_BSY_SHIFT))  );

	//Debug Info
	gSBTask.ubTerminateState = gSBTask.ubState;
	gSBTask.ubTerminateSubstate = gSBTask.ubSubstate;

	switch (gSBTask.ubSubstate) {

	case RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_HB_CORR_2K:
		gSBTask.ubState =  RETRY_SB_STATE_HB_WITH_AUTO_READ_CALIBRATION;
		gSBTask.ubSubstate =  RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_HB_CORR_2K;
		break;

	case RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_WITH_SPECIFIC_RETRY_TABLE_READ_OFFSET_HB_CORR_2K:
		gSBTask.ubState =  RETRY_SB_STATE_HB_WITH_AUTO_READ_CALIBRATION;
		gSBTask.ubSubstate =  RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_WITH_SPECIFIC_RETRY_TABLE_READ_OFFSET_HB_CORR_2K;
		break;

	case RETRY_SB_SUBSTATE_SB_DECODE_WITH_DEFAULT_LLR_SB_CORR:
		gSBTask.ubState =  RETRY_SB_STATE_SB_DECODE_WITH_DEFAULT_LLR;
		gSBTask.ubSubstate =  RETRY_SB_SUBSTATE_SB_DECODE_WITH_DEFAULT_LLR_SB_CORR;
		if (NCS_EN) {
			ubllr_indexTemp = gSBTask.ubllr_index;
		}
		break;
	case RETRY_SB_SUBSTATE_SB_DECODE_WITH_ADT_LLR_SB_CORR:
		gSBTask.ubState =  RETRY_SB_STATE_SB_DECODE_WITH_ADT_LLR;
		gSBTask.ubSubstate =  RETRY_SB_SUBSTATE_SB_DECODE_WITH_ADT_LLR_SB_CORR;
		break;
	case RETRY_SB_SUBSTATE_SB_DSP2_SB_CORR:
		gSBTask.ubState = RETRY_SB_STATE_SB_DSP2;
		gSBTask.ubSubstate =  RETRY_SB_SUBSTATE_SB_DSP2_SB_CORR;
		break;
	default:
		break;
	}

#if(TRUE == RETRY_MICRON_NICKS)
	switch (gSBTask.ubSubstate) {		//Update Error Recovery Step with Most  Energy (largest step)
	case RETRY_SB_SUBSTATE_SB_DECODE_WITH_DEFAULT_LLR_SB_CORR:
	case RETRY_SB_SUBSTATE_SB_DECODE_WITH_ADT_LLR_SB_CORR:
		if (TRUE == gSBTask.ubSBReadDecodeWithARC) {
			if (RETRY_SB_MICRON_ERROR_RECOVERY_READ_RETRY_ARC_SB_STEP > gSBTask.ubErrorRecoverySBStep) {		// ARC + SB
				gSBTask.ubErrorRecoverySBStep = RETRY_SB_MICRON_ERROR_RECOVERY_READ_RETRY_ARC_SB_STEP;
			}
		}
		else {
			if (RETRY_SB_MICRON_ERROR_RECOVERY_READ_RETRY_SB_STEP > gSBTask.ubErrorRecoverySBStep) {		// SB
				gSBTask.ubErrorRecoverySBStep = RETRY_SB_MICRON_ERROR_RECOVERY_READ_RETRY_SB_STEP;
			}
		}
		break;
	case RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_WITH_SPECIFIC_RETRY_TABLE_READ_OFFSET_HB_CORR_2K:
		if ((RETRY_SB_MICRON_ERROR_RECOVERY_READ_RETRY_ARC_STEP + gSBTask.ubSpecificRetryTableRound) > gSBTask.ubErrorRecoverySBStep) {	//ARC + RR0, RR5, RR6
			gSBTask.ubErrorRecoverySBStep = (RETRY_SB_MICRON_ERROR_RECOVERY_READ_RETRY_ARC_STEP + gSBTask.ubSpecificRetryTableRound);
		}
		break;
	default:
		break;
	}
#endif	/*(TRUE == RETRY_MICRON_NICKS)*/

	uwFailCnt = ((R32_FCON[R32_FCON_LDPC_SBC] & SBC_BFC_CNT_MASK) >> SBC_BFC_CNT_SHIFT);

	if (R32_FCON[R32_FCON_LDPC_SBC] & SBC_SUCCEED_BIT) {
		if (SB_DUMP_DATA_DEBUG_EN) {

		}
		R32_FCON[R32_FCON_LDPC_SBC] = 0;

		// backup correctable 2k
		RetrySBBackupCorrect2k(3, gSBTask.ubCurrentLDPCFrameIdx,            \
			gSBTask.ulbackup_addr, gSBTask.ultmp_addr, 				\
			gSBTask.ulSpareAddr, gSBTask.ultmp_SpareAddr, RETRY_SB_BACKUP_RESTORE_TO_DRAMIRAM);

		gSBTask.ubState = RETRY_SB_STATE_TERMINATE_PASS;
	}
	else {
		switch (gSBTask.ubSubstate) {
		case RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_HB_CORR_2K:
#if RETRY_SOFTBITT_FOR_SDK_EN
			gSBTask.ubState = RETRY_SB_STATE_HB_WITH_AUTO_READ_CALIBRATION;
			gSBTask.ubSubstate =  RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_INIT;
#else /* RETRY_SOFTBITT_FOR_SDK_EN */
			gSBTask.ubTrappingSetCnt = 0;
			if ((SB_ARC_MAX_ROUND_CNT - 1) == gSBTask.ubARCRoundCnt) {
				gSBTask.ubARCRoundCnt = 0;
				gSBTask.ubState = RETRY_SB_STATE_SB_READ_WITH_OPTIMAL_READ_LEVEL;
				gSBTask.ubSubstate =  RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_INIT;
			}
			else {
				gSBTask.ubARCRoundCnt++;
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_ENABLE_AUTO_READ_CALIBRATION;
				gSBTask.ubThirdstate =  RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_SET_FEATURE;
			}
#endif /* RETRY_HARDBIT_FOR_SDK_EN */
			if (SB_DUMP_DATA_DEBUG_EN) {
				RetrySBBackupCorrect2k(3, gSBTask.ubCurrentLDPCFrameIdx,            \
					gSBTask.ulbackup_addr, (U32)(gulVUCAddr),
					gSBTask.ulSpareAddr, gSBTask.ultmp_SpareAddr, RETRY_SB_BACKUP_RESTORE_TO_ONLYDRAM_AND_KEEP);
			}
			break;

		case RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_WITH_SPECIFIC_RETRY_TABLE_READ_OFFSET_HB_CORR_2K:
#if(TRUE == RETRY_MICRON_NICKS)
			gSBTask.ubState = RETRY_SB_STATE_SELECT_READ_RETRY_TABLE;
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_NULL;
			gSBTask.ubSpecificRetryTableRound ++;
			//SB MICRON Check Point 0802
			M_UART(ERROR_SOFTBIT_, "\n SpecificRetryTableRound:%d", gSBTask.ubSpecificRetryTableRound );
			if (SB_MICRON_SPECIFIC_RETRY_TABLE_MAX_CNT == gSBTask.ubSpecificRetryTableRound) {
				gSBTask.ubSpecificRetryTableRound = 0;
				//Should set Default Feature between Specific RR & Specific Read offset
				RetrySBSwitchToSetDefaultFeatureThenDoBackupSBFlow(RETRY_SB_STATE_SB_READ_WITH_READ_RETRY_TABLE_WITH_ARC, RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_INIT);
			}
			break;
#else /*(TRUE == RETRY_MICRON_NICKS)*/
			gSBTask.ubState =  RETRY_SB_STATE_SB_READ_WITH_OPTIMAL_READ_LEVEL;
			gSBTask.ubSubstate =  RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_INIT;
			break;
#endif	/*(TRUE == RETRY_MICRON_NICKS)*/
		case RETRY_SB_SUBSTATE_SB_DECODE_WITH_DEFAULT_LLR_SB_CORR:
			gSBTask.ubState = RETRY_SB_STATE_LOAD_DEFAULT_LLR_TABLE;
			gSBTask.ubSubstate =  RETRY_SB_SUBSTATE_NULL;
			gSBTask.ubllr_index++;

			if (NCS_EN) {
				for (ubIdx = 0 ; ubIdx < 3 ; ubIdx++) {
					RetrySBBackupCorrect2k(ubIdx, gSBTask.ubCurrentLDPCFrameIdx,            \
						gSBTask.ulbackup_addr, (U32)(gulVUCAddr +  BC_8KB + (BC_4KB + BC_1KB)*ubIdx),
						gSBTask.ulSpareAddr, gSBTask.ultmp_SpareAddr, RETRY_SB_BACKUP_RESTORE_TO_ONLYDRAM_AND_KEEP);
					if (RETRY_SB_ENABLE_DEBUG_MESSAGE) {
						//UartPrintf("\n F_VUCAddr %x", (U32)(gulVUCAddr +  BC_8KB + (BC_4KB + BC_1KB)*ubIdx));
					}
				}
			}
			break;

		case RETRY_SB_SUBSTATE_SB_DECODE_WITH_ADT_LLR_SB_CORR:
#if(TRUE == RETRY_MICRON_NICKS)
			if (TRUE == gSBTask.ubSBReadDecodeWithARC) {
				gSBTask.ubState = RETRY_SB_STATE_TERMINATE_FAIL;
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_NULL;
			}
			else {
				RetrySBSwitchToSetDefaultFeatureThenDoBackupSBFlow(RETRY_SB_STATE_SELECT_READ_RETRY_TABLE, RETRY_SB_SUBSTATE_NULL);
			}
			break;
#else /*(TRUE == RETRY_MICRON_NICKS)*/
			if ( SB_MICRON_SPECIFIC_RETRY_TABLE == gSBTask.ubSpecificMode) {
				gSBTask.ubState = RETRY_SB_STATE_MODIFY_RR_ADD_COARSE_TUNING_LEVEL;
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_NULL;
				gSBTask.ubSpecificRetryTableRound ++;
				//SB MICRON Check Point 0802
				M_UART(ERROR_SOFTBIT_, "\n SpecificRetryTableRound:%d", gSBTask.ubSpecificRetryTableRound );
				if (SB_MICRON_SPECIFIC_RETRY_TABLE_MAX_CNT == gSBTask.ubSpecificRetryTableRound) {
					gSBTask.ubSpecificRetryTableRound = 0;
					gSBTask.ubSpecificMode = SB_MICRON_SPECIFIC_READ_OFFSET;
					//Should set Default Feature between Specific RR & Specific Read offset
					RetrySBSwitchToSetDefaultFeatureThenDoBackupSBFlow(RETRY_SB_STATE_MODIFY_RR_ADD_COARSE_TUNING_LEVEL, RETRY_SB_SUBSTATE_NULL);
				}
			}
			else if ( SB_MICRON_SPECIFIC_READ_OFFSET == gSBTask.ubSpecificMode) {
				gSBTask.ubState = RETRY_SB_STATE_MODIFY_RR_ADD_COARSE_TUNING_LEVEL;
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_NULL;
				gSBTask.ubSpecificReadOffsetRound ++;
				//SB MICRON Check Point 0802
				M_UART(ERROR_SOFTBIT_, "\n SpecificReadOffsetRound:%d", gSBTask.ubSpecificReadOffsetRound );
				if (SB_MICRON_SPECIFIC_READ_OFFSET_MAX_CNT == gSBTask.ubSpecificReadOffsetRound) {
					gSBTask.ubSpecificReadOffsetRound = 0;
					gSBTask.ubState = RETRY_SB_STATE_TERMINATE_FAIL;
				}
			}
			else {	//SB_MICRON_SPECIFIC_NOT_USING	,Case A/B
				gSBTask.ubState =  RETRY_SB_STATE_SB_DSP2;
				gSBTask.ubSubstate =  RETRY_SB_SUBSTATE_SB_DSP2_INIT;
			}
			break;
#endif /*(TRUE == RETRY_MICRON_NICKS)*/
		case RETRY_SB_SUBSTATE_SB_DSP2_SB_CORR:
			gSBTask.ubState = RETRY_SB_STATE_TERMINATE_FAIL;
			break;

		default:
			break;
		}
	}

	//Micron SB Todo, NCS

	if (NCS_EN) {		//Backup IBUF to DRAM IRAM and keep for NCS,  backup gSBTask.ultmp_SpareAddr to gL4kSpareBackup for later use, because RetrySBProgNCSCMD will make gSBTask.ultmp_SpareAddr dirty
		// backup correctable 2k
		RetrySBBackupCorrect2k(3, gSBTask.ubCurrentLDPCFrameIdx,            \
			gSBTask.ulbackup_addr, gSBTask.ultmp_addr,
			gSBTask.ulSpareAddr, gSBTask.ultmp_SpareAddr, RETRY_SB_BACKUP_RESTORE_TO_DRAMIRAM_AND_KEEP);

		//SB MICRON Check Point 1007
		memcpy(&gL4kSpareBackup, (U32 *)((U32)(gSBTask.ultmp_SpareAddr)), BC_16B);
		//SB MICRON Check Point 0730
		M_UART(ERROR_SOFTBIT_, "\n gL4kSpareBackup");
		M_UART(ERROR_SOFTBIT_, "\n %x", gL4kSpareBackup.BitMap.ulong.ulL0);
		M_UART(ERROR_SOFTBIT_, "\n %x", gL4kSpareBackup.BitMap.ulong.ulL1);
		M_UART(ERROR_SOFTBIT_, "\n %x", gL4kSpareBackup.BitMap.ulong.ulL2);
		M_UART(ERROR_SOFTBIT_, "\n %x \n", gL4kSpareBackup.BitMap.ulong.ulL3);

		if (0 == gSBTask.ubCurrentLDPCFrameIdx) {
			memcpy(&gL4kSpareBackup, (U32 *)((U32)(gSBTask.ultmp_SpareAddr)), BC_8B);
		}
		else {
			memcpy(&gL4kSpareBackup, (U32 *)((U32)(gSBTask.ultmp_SpareAddr + BC_8B)), BC_8B);
		}
	}



	if (NCS_EN) {	// 0x10 for HB , Iteration, INI_PTYCHSUM 		//0x11 for SB, Iteration, LLR tables
		if (ubTempState == RETRY_SB_SUBSTATE_SB_DSP2_SB_CORR) {
			ubNCSCheckRound = 4;
		}
		else {
			ubNCSCheckRound = 1;
		}
		for (ubNCSCheckTableIdx = 0; ubNCSCheckTableIdx < ubNCSCheckRound ; ubNCSCheckTableIdx++) {
			puwfpu_ptr = (U16 *)(IRAM_BASE + FPU_NCS_OFF);

			//R0~R2
			switch (ubTempState) {
			case RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_HB_CORR_2K:
			case RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_WITH_SPECIFIC_RETRY_TABLE_READ_OFFSET_HB_CORR_2K:
				puwfpu_ptr[4] = FPU_ADR_1B(0x10);
#if RETRY_SOFTBITT_FOR_SDK_EN
				puwfpu_ptr[5] = FPU_ADR_1B(0x04);
#else /* RETRY_SOFTBITT_FOR_SDK_EN */
				puwfpu_ptr[5] = FPU_ADR_1B(0x04);
#endif
				puwfpu_ptr[6] = FPU_ADR_1B(0x00);
				break;
			case RETRY_SB_SUBSTATE_SB_DECODE_WITH_DEFAULT_LLR_SB_CORR:
			case RETRY_SB_SUBSTATE_SB_DECODE_WITH_ADT_LLR_SB_CORR:
			case RETRY_SB_SUBSTATE_SB_DSP2_SB_CORR:
				puwfpu_ptr[4] = FPU_ADR_1B(0x11);
				puwfpu_ptr[5] = FPU_ADR_1B(0x2C);		//44		//64
				puwfpu_ptr[6] = FPU_ADR_1B(0x00);
				break;

			default:
				M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
				RetrySBSwitchStateToSetDefaultFeatureReset();
				return;
			}

			puwNCSDebug = (U16 *)gulVUCAddr;
			//// Update Error bits value to gulVUCAddr[0]_L(2Byte)
			*puwNCSDebug = uwFailCnt;
			puwNCSDebug++;

			/// Update Iteration to gulVUCAddr[0]_H(2Byte)
			switch (ubTempState) {

			case RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_HB_CORR_2K:
			case RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_WITH_SPECIFIC_RETRY_TABLE_READ_OFFSET_HB_CORR_2K:
				*puwNCSDebug = (U16)(R32_FCON[R32_FCON_LDPC_SBC2] & SBC_ITER_STOP_SHIFT_MASK);
				break;

			case RETRY_SB_SUBSTATE_SB_DSP2_SB_CORR:
			case RETRY_SB_SUBSTATE_SB_DECODE_WITH_ADT_LLR_SB_CORR:
				*puwNCSDebug = 0;
				break;

			case RETRY_SB_SUBSTATE_SB_DECODE_WITH_DEFAULT_LLR_SB_CORR:
				*puwNCSDebug = (U16)(ubllr_indexTemp) + 1;		//NCS (Decoding flow)   LLR number start from 1 , not 0
				break;
			default:
				M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
				RetrySBSwitchStateToSetDefaultFeatureReset();
				return;
			}

			puwNCSDebug++;

			/// HB: Update INI_PTYCHSUM, SB:Update LLR table
			switch (ubTempState) {
			case RETRY_SB_SUBSTATE_SB_DECODE_WITH_ADT_LLR_SB_CORR:
				/// distribution
				pulNCSDebug = (U32 *)puwNCSDebug;

				for (ubIdx = 0; ubIdx < 10; ++ubIdx) {
					//SB MICRON Check Point 0802
					M_UART(ERROR_SOFTBIT_, "\n ADT LLR: %x", gSoftBitDSPADTLLRTable.LLRTable.AccessBy32bits.ulLLR32bits[ubIdx]);
					*pulNCSDebug  = gSoftBitDSPADTLLRTable.LLRTable.AccessBy32bits.ulLLR32bits[ubIdx];
					pulNCSDebug++;
				}
				//SB MICRON Check Point 0802
				M_UART(ERROR_SOFTBIT_, "\n ");
				break;

			case RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_HB_CORR_2K:
			case RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_WITH_SPECIFIC_RETRY_TABLE_READ_OFFSET_HB_CORR_2K:
				break;

			case RETRY_SB_SUBSTATE_SB_DECODE_WITH_DEFAULT_LLR_SB_CORR:
			case RETRY_SB_SUBSTATE_SB_DSP2_SB_CORR:
				/// distribution
				pulNCSDebug = (U32 *)puwNCSDebug;
				if (RETRY_SB_SUBSTATE_SB_DSP2_SB_CORR == ubTempState) {

					//SB MICRON Check Point 0731
					M_UART(ERROR_SOFTBIT_, "\n DSP2_LLR");

					////Get LLR table from temp LLR table variable////
					for (ubIdx = 0 ; ubIdx < SBIT_DSP_LLR_TABLE_SIZE_IN_4_BYTES ; ubIdx++) {
						*pulNCSDebug = gSoftBitDSPLLRTable[ubNCSCheckTableIdx].LLRTable.AccessBy32bits.ulLLR32bits[ubIdx];

						//SB MICRON Check Point 0731
						M_UART(ERROR_SOFTBIT_, "\n pulNCSDebug_sb_llr_table:%x", *pulNCSDebug);

						pulNCSDebug++;
					}
					//SB MICRON Check Point 0731
					M_UART(ERROR_SOFTBIT_, "\n ");
					/////////////////////////////////
				}
				else {
					//SB MICRON Check Point 0730
					M_UART(ERROR_SOFTBIT_, "\n ubllr_indexTemp:%d, %d", ubllr_indexTemp, gSBTask.ubPageType);

					for (ubIdx = 0; ubIdx < DEFAULT_LLR_TABLE_LENGTH; ubIdx++) {
						*pulNCSDebug  = sb_llr_table[ubllr_indexTemp][gSBTask.ubPageType][ubIdx];

						//SB MICRON Check Point 0730
						M_UART(ERROR_SOFTBIT_, "\n pulNCSDebug_sb_llr_table:%x", *pulNCSDebug);
						pulNCSDebug++;
					}

					//SB MICRON Check Point 0730
					M_UART(ERROR_SOFTBIT_, "\n ");
				}
				break;

			default:
				M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
				RetrySBSwitchStateToSetDefaultFeatureReset();
				return;
			}

			//ubMTIndex = Retry_PrepareFreeMT_SB(NULL, NULL);
			RetrySBProgNCSCMD( (FPU_NCS_OFF), 0, ubMTIndex, RETRY_SB_INSERT_USED_MT_MODE);
		}
	}

	//Micron SB Todo, NCS

	if (NCS_EN) {	//Return HW setting
		puwfpu_ptr = (U16 *)(IRAM_BASE + FPU_NCS_OFF);

		//R0~R2
		RetryHWSetting_t *pRetryHWSettingTable = (RetryHWSetting_t *)(gulHWSettingAddr);
		puwfpu_ptr[4] = FPU_ADR_1B(pRetryHWSettingTable->ubHWRegSel); // R0 : 0xD0 Normal HardBit HW REG; 0xD1 Trapping Set Flow HardBit HW REG; 0xD2 DSP1 HW REG; 0xD3 DSP2 HW REG
		puwfpu_ptr[5] = FPU_ADR_1B(0xB9); // R1+R2: 441 Byte
		puwfpu_ptr[6] = FPU_ADR_1B(0x01);

		// Fill HWSetting to VUC Address by ReadRetryDumpHWSetting()
		memcpy((void *)gulVUCAddr, (void *)gulHWSettingAddr, 441);

		//ubMTIndex = Retry_PrepareFreeMT_SB(NULL, NULL);
		RetrySBProgNCSCMD( (FPU_NCS_OFF), 0, ubMTIndex, RETRY_SB_INSERT_USED_MT_MODE);
	}

	if (NCS_EN) {	//Return decode data to NCS
		puwfpu_ptr = (U16 *)(IRAM_BASE + FPU_NCS_OFF);

		//R0~R2
		switch (ubTempState) {
		case RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_HB_CORR_2K:
		case RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_WITH_SPECIFIC_RETRY_TABLE_READ_OFFSET_HB_CORR_2K:
			puwfpu_ptr[4] = FPU_ADR_1B(0x00);
			break;

		case RETRY_SB_SUBSTATE_SB_DECODE_WITH_ADT_LLR_SB_CORR:
			puwfpu_ptr[4] = FPU_ADR_1B(0x40);
			break;

		case RETRY_SB_SUBSTATE_SB_DECODE_WITH_DEFAULT_LLR_SB_CORR:
		case RETRY_SB_SUBSTATE_SB_DSP2_SB_CORR:
			puwfpu_ptr[4] = FPU_ADR_1B(0x30);
			break;

		default:
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
			RetrySBSwitchStateToSetDefaultFeatureReset();
			return;

		}

		puwfpu_ptr[5] = FPU_ADR_1B(0x08);
		puwfpu_ptr[6] = FPU_ADR_1B(0x08);
		//SBRAID Check Point 0409
		if (DEBUG_SBRAID_EN) {
			if (RETRY_SB_SUBSTATE_SB_DSP2_SB_CORR == ubTempState) {
				if (gpRS_Task.ubState == RAIDECC_RETRY_DECODE_NORMAL_PAGE_HBSB_RETRY) {
					//Printf 16Byte spare
					M_UART(RETRY_SBRAID_, "\n SB Normal Spare_2:");
					M_UART(RETRY_SBRAID_, "\n IRAM_GC :%x", * ((U32 *)(( IRAM_BASE + GC_BACKUP_RETRY_OFF)  + 0 * L4K_SIZE)));
					M_UART(RETRY_SBRAID_, "\n RR_Spare :%x", * ((U32 *)(gSBTask.ulSpareAddr)));
					M_UART(RETRY_SBRAID_, "\n tmp_Spare :%x \n", * ((U32 *)(gSBTask.ultmp_SpareAddr + 0 * L4K_SIZE)));
				}
			}

		}
		memcpy((U32 *)(gulVUCAddr), (U32 *)(gSBTask.ultmp_addr + (gSBTask.ubCurrentLDPCFrameIdx * BC_2KB)), BC_2KB);

		//SB MICRON Check Point 0730
		M_UART(ERROR_SOFTBIT_, "\n gulVUCAddr:%x, gSBTask.ultmp_addr:%x, gSBTask.ubCurrentLDPCFrameIdx:%d Decode Data:", gulVUCAddr, gSBTask.ultmp_addr, gSBTask.ubCurrentLDPCFrameIdx);
		for (U8 ubj = 0 ; ubj < 4 ; ubj++) {
			M_UART(ERROR_SOFTBIT_, "\n tmp %x", * ((U32 *)(gSBTask.ultmp_addr + (gSBTask.ubCurrentLDPCFrameIdx * BC_2KB) + ubj * BC_4B)));
			M_UART(ERROR_SOFTBIT_, "\n Vuc %x", * ((U32 *)(gulVUCAddr + ubj * BC_4B)));
		}
		M_UART(ERROR_SOFTBIT_, "\n ");

		//ubMTIndex = Retry_PrepareFreeMT_SB(NULL, NULL);
		RetrySBProgNCSCMD( (FPU_NCS_OFF), 0, ubMTIndex, RETRY_SB_INSERT_USED_MT_MODE_AND_WRITE_SPARE);
	}

	//////////////////////////////
	////Return Distribution to NCS////
	//////////////////////////////

	if (NCS_EN) {
		switch (ubTempState) {
		///	 ADT LLR need to return distribution to NCS
		case RETRY_SB_SUBSTATE_SB_DECODE_WITH_ADT_LLR_SB_CORR:
			puwfpu_ptr = (U16 *)(IRAM_BASE + FPU_NCS_OFF);

			//R0~R2
			puwfpu_ptr[4] = FPU_ADR_1B(0x20);
			puwfpu_ptr[5] = FPU_ADR_1B(0x84);
			puwfpu_ptr[6] = FPU_ADR_1B(0x00);

			/// cnt 1 value
			pubNCSDebug = (U8 *)gulVUCAddr;
			for (ubIdx = 0; ubIdx < 4; ++ubIdx) {
				// for toshiba tlc middle distribution, first distribution is start from offset 1 not 0
				*pubNCSDebug  =  RetrySBget_dsp_rank(ubIdx, DISABLE);
				pubNCSDebug++;
			}

			/// distribution
			pulNCSDebug = (U32 *)pubNCSDebug;
			for (ubIdx = 0; ubIdx < 32; ++ubIdx) {
				R32_FCON[R32_FCON_ECC_PARAM_CFG] &= ~(DSP_DISTRIBUTION_SEL_MASK << DSP_DISTRIBUTION_SEL_SHIFT);
				R32_FCON[R32_FCON_ECC_PARAM_CFG] |= (ubIdx << DSP_DISTRIBUTION_SEL_SHIFT);

				*pulNCSDebug  = R32_FCON[R32_FCON_DSP_DISTRIBUTION];
				pulNCSDebug++;
			}

			//ubMTIndex = Retry_PrepareFreeMT_SB(NULL, NULL);
			RetrySBProgNCSCMD( (FPU_NCS_OFF), 0, ubMTIndex, RETRY_SB_INSERT_USED_MT_MODE);
			break;
		default:
			break;
		}
	}

	////When NCS, Restore IBUF for next FIX default(corner case) LLR table (flows that no SB_READ between SB_DECODE)////
	if (NCS_EN) {
		if (RETRY_SB_SUBSTATE_SB_DECODE_WITH_DEFAULT_LLR_SB_CORR == ubTempState) {
			if (RETRY_SB_STATE_LOAD_DEFAULT_LLR_TABLE == gSBTask.ubState) {
				RetrySBBackupRestoreIBUF(ubMTIndex);
			}
		}
	}
}

void RetrySBReadSB1( U8 ubPageType, U8 *pubReadRetryTable, U8 ldpc_frame_idx)
{
#if (!BURNER_MODE_EN) && (!RDT_MODE_EN) && ( !LPM3_LOADER)
	U16 *fpu_ptr;
	U16 uwFpu_offset;
	U8 ubMTIndex;

	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_SB_READ_SB1_INIT:
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SB_READ_SB1_PLUS_MINUS_2_DELTA_SET_FEATURE;
		gSBTask.ubFourthstate = RETRY_SB_4TH_STATE_SB_READ_LEVEL_BY_DELTA_TABLE_SET_FEATURE;
		if (RETRY_SB_EXTRA_PAGE == ubPageType) {
			gSBTask.ubSecondFeatureAddr = SB_MICRON_NEED_SET_SECOND_FEATURE_ADDRESS;
		}

		//SB MICRON Check Point 0730
		M_UART(ERROR_SOFTBIT_, "\n SB1 gSBTask.ubSecondFeatureAddr :%d", gSBTask.ubSecondFeatureAddr);

	/* no break */
	case RETRY_SB_3RD_STATE_SB_READ_SB1_PLUS_MINUS_2_DELTA_SET_FEATURE:
		//+-2 delta
		//May Need set 2 FA by ubSecondFeatureAddr flag
		RetrySBSetFeatureByReadRetryDeltaTableOffset( ubPageType, pubReadRetryTable, SB_MICRON_DELTA_TABLE_BIOS_2);
		break;
	case RETRY_SB_3RD_STATE_SB_READ_SB1_PLUS_MINUS_2_DELTA_READ:
		uwFpu_offset = FPU_PTR_OFFSET(fpu_micron_sb_read);
		ubMTIndex = RetrySBPrepareFreeMT(RetrySBReadSB1Pass_Callback, RetrySBReadSB1Fail_Callback);
		RetrySBSelecMTFPUTrigger( uwFpu_offset, (U32)( (gSBTask.ulFSA_Align) + (gSBTask.ubCurr_frm_idx) ), 0, ubMTIndex, SWITCH_CLK__NORMAL_MODE);
		if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
			gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
		}
		else {
			gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
			gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
		}
		break;
	case RETRY_SB_3RD_STATE_SB_READ_SB1_PLUS_MINUS_2_DELTA_DMA:
		uwFpu_offset = FPU_PTR_OFFSET(fpu_raw_dma_read);
		fpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);
		fpu_ptr[7] =  FPU_DMA_R_RAW((1 << RAW_MODE_OFFSET) | (2 << RAW_LOGIC_OFFSET) | (1 << RAW_SRC_PTR_OFFSET) | (0 << RAW_DST_PTR_OFFSET));
		ubMTIndex = RetrySBPrepareFreeMT(RetrySBReadSB1Pass_Callback, RetrySBReadSB1Fail_Callback);
		RetrySBSelectDMAMTFPUTrigger( uwFpu_offset, (U32)( (gSBTask.ulFSA_Align) + (gSBTask.ubCurr_frm_idx) ), ubMTIndex);
		if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
			gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
		}
		else {
			gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
			gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
		}
		break;

	}
#endif /* (!BURNER_MODE_EN) && (!RDT_MODE_EN) && ( !LPM3_LOADER) */
}

void RetrySBReadSB2( U8 ubPageType, U8 *pubReadRetryTable, U8 ldpc_frame_idx)
{
#if (!BURNER_MODE_EN) && (!RDT_MODE_EN) && ( !LPM3_LOADER)
	U16 *fpu_ptr;
	U16 uwFpu_offset;
	U8 ubMTIndex;

	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_SB_READ_SB2_INIT:
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SB_READ_SB2_PLUS_MINUS_3_DELTA_SET_FEATURE;
		gSBTask.ubFourthstate = RETRY_SB_4TH_STATE_SB_READ_LEVEL_BY_DELTA_TABLE_SET_FEATURE;
		if (RETRY_SB_EXTRA_PAGE == ubPageType) {
			gSBTask.ubSecondFeatureAddr = SB_MICRON_NEED_SET_SECOND_FEATURE_ADDRESS;
		}

		//SB MICRON Check Point 0730
		M_UART(ERROR_SOFTBIT_, "\n SB2 gSBTask.ubSecondFeatureAddr :%d", gSBTask.ubSecondFeatureAddr);
	/* no break */
	case RETRY_SB_3RD_STATE_SB_READ_SB2_PLUS_MINUS_3_DELTA_SET_FEATURE:
		//+-3 delta
		//May Need set 2 FA by ubSecondFeatureAddr flag
		RetrySBSetFeatureByReadRetryDeltaTableOffset( ubPageType, pubReadRetryTable, SB_MICRON_DELTA_TABLE_BIOS_3);
		break;
	case RETRY_SB_3RD_STATE_SB_READ_SB2_PLUS_MINUS_3_DELTA_READ:
		uwFpu_offset = FPU_PTR_OFFSET(fpu_micron_sb_read);
		ubMTIndex = RetrySBPrepareFreeMT(RetrySBReadSB2Pass_Callback, RetrySBReadSB2Fail_Callback);
		RetrySBSelecMTFPUTrigger( uwFpu_offset, (U32)( (gSBTask.ulFSA_Align) + (gSBTask.ubCurr_frm_idx) ), 0, ubMTIndex, SWITCH_CLK__NORMAL_MODE);
		if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
			gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
		}
		else {
			gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
			gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
		}
		break;
	case RETRY_SB_3RD_STATE_SB_READ_SB2_PLUS_MINUS_3_DELTA_DMA:
		uwFpu_offset = FPU_PTR_OFFSET(fpu_raw_dma_read);
		fpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);
		fpu_ptr[7] =  FPU_DMA_R_RAW((0 << RAW_MODE_OFFSET) | (2 << RAW_LOGIC_OFFSET) | (3 << RAW_SRC_PTR_OFFSET) | (2 << RAW_DST_PTR_OFFSET));		//SB2 temp set dest to IBUF 2, 2K Frame 0
		ubMTIndex = RetrySBPrepareFreeMT(RetrySBReadSB2Pass_Callback, RetrySBReadSB2Fail_Callback);
		RetrySBSelectDMAMTFPUTrigger( uwFpu_offset, (U32)( (gSBTask.ulFSA_Align) + (gSBTask.ubCurr_frm_idx) ), ubMTIndex);
		if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
			gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
		}
		else {
			gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
			gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
		}
		break;
	case RETRY_SB_3RD_STATE_SB_READ_SB2_INIT_2:
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SB_READ_SB2_PLUS_MINUS_1_DELTA_SET_FEATURE;
		gSBTask.ubFourthstate = RETRY_SB_4TH_STATE_SB_READ_LEVEL_BY_DELTA_TABLE_SET_FEATURE;
		if (RETRY_SB_EXTRA_PAGE == ubPageType) {
			gSBTask.ubSecondFeatureAddr = SB_MICRON_NEED_SET_SECOND_FEATURE_ADDRESS;
		}

		//SB MICRON Check Point 0730
		M_UART(ERROR_SOFTBIT_, "\n SB2_2 gSBTask.ubSecondFeatureAddr :%d", gSBTask.ubSecondFeatureAddr);
	/*No break;*/
	case RETRY_SB_3RD_STATE_SB_READ_SB2_PLUS_MINUS_1_DELTA_SET_FEATURE:
		//+-1 delta
		//May Need set 2 FA by ubSecondFeatureAddr flag
		RetrySBSetFeatureByReadRetryDeltaTableOffset( ubPageType, pubReadRetryTable, SB_MICRON_DELTA_TABLE_BIOS_1);
		break;
	case RETRY_SB_3RD_STATE_SB_READ_SB2_PLUS_MINUS_1_DELTA_READ:
		uwFpu_offset = FPU_PTR_OFFSET(fpu_micron_sb_read);
		ubMTIndex = RetrySBPrepareFreeMT(RetrySBReadSB2Pass_Callback, RetrySBReadSB2Fail_Callback);
		RetrySBSelecMTFPUTrigger( uwFpu_offset, (U32)( (gSBTask.ulFSA_Align) + (gSBTask.ubCurr_frm_idx) ), 0, ubMTIndex, SWITCH_CLK__NORMAL_MODE);
		if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
			gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
		}
		else {
			gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
			gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
		}
		break;
	case RETRY_SB_3RD_STATE_SB_READ_SB2_PLUS_MINUS_1_DELTA_DMA_WITH_XNOR:
		uwFpu_offset = FPU_PTR_OFFSET(fpu_raw_dma_read);
		fpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);
		fpu_ptr[7] =  FPU_DMA_R_RAW((0 << RAW_MODE_OFFSET) | (1 << RAW_LOGIC_OFFSET) | (2 << RAW_SRC_PTR_OFFSET) | (1 << RAW_DST_PTR_OFFSET));	//SB2 XNOR  SB2_temp ( IBUF 2, 2K Frame 0) to IBUF 1, 2K Frame 0
		ubMTIndex = RetrySBPrepareFreeMT(RetrySBReadSB2Pass_Callback, RetrySBReadSB2Fail_Callback);
		RetrySBSelectDMAMTFPUTrigger( uwFpu_offset, (U32)( (gSBTask.ulFSA_Align) + (gSBTask.ubCurr_frm_idx) ), ubMTIndex);
		if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
			gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
		}
		else {
			gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
			gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
		}
		break;
	}
#endif /* (!BURNER_MODE_EN) && (!RDT_MODE_EN) && ( !LPM3_LOADER) */
}

void RetrySBReadSB3( U8 ubPage_type, U8 ubDSPEable, U8 *pubReadRetryTable, U8 ldpc_frame_idx)
{
	U16 *fpu_ptr = NULL;

	U8 ubMTIndex;
	U16 uwFpu_offset;

	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_SB_READ_SB3_INIT:
		if (RETRY_SB_LOWER_PAGE == ubPage_type) {
#if (RETRY_MICRON_NICKS)
			switch (gSBTask.ubState) {
			case RETRY_SB_STATE_SB_READ_WITH_READ_RETRY_TABLE:
			case RETRY_SB_STATE_SB_READ_WITH_READ_RETRY_TABLE_WITH_ARC:
				gSBTask.ubState = RETRY_SB_STATE_LOAD_DEFAULT_LLR_TABLE;
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_NULL;
				break;
			}
			return;
#else 	/*(RETRY_MICRON_NICKS)*/
			switch (gSBTask.ubState) {
			case RETRY_SB_STATE_SB_READ_WITH_OPTIMAL_READ_LEVEL:
				gSBTask.ubState = RETRY_SB_STATE_LOAD_DEFAULT_LLR_TABLE;
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_NULL;
				break;
			case RETRY_SB_STATE_SB_DSP2:
				gSBTask.ubState = RETRY_SB_STATE_SB_DSP2;
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_DSP2_SB_TRIG_SBC_FOR_ADT_LLR;
				gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_NULL;
				break;
			}
			return;
#endif	/*(RETRY_MICRON_NICKS)*/
		}
		else {
			gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SB_READ_SB3_READ;
		}
	/* no break */
	case RETRY_SB_3RD_STATE_SB_READ_SB3_READ:
		uwFpu_offset = FPU_PTR_TLC_1P_READ_CMD;
		ubMTIndex = RetrySBPrepareFreeMT(RetrySBReadSB3Pass_Callback, RetrySBReadSB3Fail_Callback);
		RetrySBSelecMTFPUTrigger( uwFpu_offset, (U32)( (gSBTask.ulShareLowerFSA_Align) + (gSBTask.ubCurr_frm_idx) ), 0, ubMTIndex, SWITCH_CLK__NORMAL_MODE);
		if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
			gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
		}
		else {
			gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
			gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
		}
		break;

	case RETRY_SB_3RD_STATE_SB_READ_SB3_DMA:
		uwFpu_offset = FPU_PTR_OFFSET(fpu_raw_dma_read);
		fpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);
		fpu_ptr[7] =  FPU_DMA_R_RAW((1 << RAW_MODE_OFFSET) | (2 << RAW_LOGIC_OFFSET) | (2 << RAW_SRC_PTR_OFFSET) | (1 << RAW_DST_PTR_OFFSET)); //SB3 Store in IBUF 1, 2K Frame 1

		ubMTIndex = RetrySBPrepareFreeMT(RetrySBReadSB3Pass_Callback, RetrySBReadSB3Fail_Callback);
		RetrySBSelectDMAMTFPUTrigger( uwFpu_offset, (U32)( (gSBTask.ulShareLowerFSA_Align) + (gSBTask.ubCurr_frm_idx) ), ubMTIndex);	//SB3 will read Share Lower page
		if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
			gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
		}
		else {
			gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
			gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
		}
		break;
	}
}

void RetrySBReadSB4( U8 ubPage_type, U8 ubDSPEable, U8 *pubReadRetryTable, U8 ldpc_frame_idx)
{
	U16 *fpu_ptr = NULL;

	U8 ubMTIndex;
	U16 uwFpu_offset;

	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_SB_READ_SB4_INIT:
		if (RETRY_SB_UPPER_PAGE == ubPage_type) {
#if (RETRY_MICRON_NICKS)
			switch (gSBTask.ubState) {
			case RETRY_SB_STATE_SB_READ_WITH_READ_RETRY_TABLE:
			case RETRY_SB_STATE_SB_READ_WITH_READ_RETRY_TABLE_WITH_ARC:
				gSBTask.ubState = RETRY_SB_STATE_LOAD_DEFAULT_LLR_TABLE;
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_NULL;
				break;
			}
			return;
#else 	/*(RETRY_MICRON_NICKS)*/
			switch (gSBTask.ubState) {
			case RETRY_SB_STATE_SB_READ_WITH_OPTIMAL_READ_LEVEL:
				gSBTask.ubState = RETRY_SB_STATE_LOAD_DEFAULT_LLR_TABLE;
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_NULL;
				break;
			case RETRY_SB_STATE_SB_DSP2:
				gSBTask.ubState = RETRY_SB_STATE_SB_DSP2;
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_DSP2_SB_TRIG_SBC_FOR_ADT_LLR;
				gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_NULL;
				break;
			}
			return;
#endif	/*(RETRY_MICRON_NICKS)*/
		}
		else {
			gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SB_READ_SB4_READ;
		}
	/* no break */
	case RETRY_SB_3RD_STATE_SB_READ_SB4_READ:
		uwFpu_offset = FPU_PTR_TLC_1P_READ_CMD;
		ubMTIndex = RetrySBPrepareFreeMT(RetrySBReadSB4Pass_Callback, RetrySBReadSB4Fail_Callback);
		RetrySBSelecMTFPUTrigger( uwFpu_offset, (U32)( (gSBTask.ulShareUpperFSA_Align) + (gSBTask.ubCurr_frm_idx) ), 0, ubMTIndex, SWITCH_CLK__NORMAL_MODE);
		if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
			gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
		}
		else {
			gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
			gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
		}
		break;

	case RETRY_SB_3RD_STATE_SB_READ_SB4_DMA:
		uwFpu_offset = FPU_PTR_OFFSET(fpu_raw_dma_read);
		fpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);
		fpu_ptr[7] =  FPU_DMA_R_RAW((0 << RAW_MODE_OFFSET) | (2 << RAW_LOGIC_OFFSET) | (3 << RAW_SRC_PTR_OFFSET) | (2 << RAW_DST_PTR_OFFSET)); //SB3 Store in IBUF 1, 2K Frame 1

		ubMTIndex = RetrySBPrepareFreeMT(RetrySBReadSB4Pass_Callback, RetrySBReadSB4Fail_Callback);
		RetrySBSelectDMAMTFPUTrigger( uwFpu_offset, (U32)( (gSBTask.ulShareUpperFSA_Align) + (gSBTask.ubCurr_frm_idx) ), ubMTIndex);	//SB3 will read Share Lower page
		if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
			gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
		}
		else {
			gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
			gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
		}
		break;
	}
}

void RetrySBReadSB1Pass_Callback(U8 ubMTIndex)
{
	gSBTask.ubMTFailCnt = 0;
	gSBTask.ubState =  gSBTask.ubPrevState;
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;

	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_SB_READ_SB1_PLUS_MINUS_2_DELTA_READ:
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SB_READ_SB1_PLUS_MINUS_2_DELTA_DMA;
		break;
	case RETRY_SB_3RD_STATE_SB_READ_SB1_PLUS_MINUS_2_DELTA_DMA:
		switch (gSBTask.ubSubstate) {
		case RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_SB1:
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_SB2;
			gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SB_READ_SB2_INIT;
			break;
		case RETRY_SB_SUBSTATE_SB_DSP2_READ_LEVEL_SB1:
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_DSP2_READ_LEVEL_SB2;
			gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SB_READ_SB2_INIT;
			break;
		}
		break;
	}
}


void RetrySBReadSB1Fail_Callback(U8 ubMTIndex)
{
	gSBTask.ubState =  gSBTask.ubPrevState;
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;

	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_SB_READ_SB1_PLUS_MINUS_2_DELTA_READ:
		RetrySBMTTriggerRetry(FALSE);
		break;
	case RETRY_SB_3RD_STATE_SB_READ_SB1_PLUS_MINUS_2_DELTA_DMA:
		RetrySBReadSB1Pass_Callback(ubMTIndex);
		break;
	}
}


void RetrySBReadSB2Pass_Callback(U8 ubMTIndex)
{
	gSBTask.ubMTFailCnt = 0;
	gSBTask.ubState =  gSBTask.ubPrevState;
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;

	switch (gSBTask.ubThirdstate) {


	case RETRY_SB_3RD_STATE_SB_READ_SB2_PLUS_MINUS_3_DELTA_READ:
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SB_READ_SB2_PLUS_MINUS_3_DELTA_DMA;
		break;
	case RETRY_SB_3RD_STATE_SB_READ_SB2_PLUS_MINUS_3_DELTA_DMA:
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SB_READ_SB2_INIT_2;
		break;
	case RETRY_SB_3RD_STATE_SB_READ_SB2_PLUS_MINUS_1_DELTA_READ:
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SB_READ_SB2_PLUS_MINUS_1_DELTA_DMA_WITH_XNOR;
		break;
	case RETRY_SB_3RD_STATE_SB_READ_SB2_PLUS_MINUS_1_DELTA_DMA_WITH_XNOR:
		switch (gSBTask.ubSubstate) {
		case RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_SB2:
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_SB3;
			gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SB_READ_SB3_INIT;
			break;
		case RETRY_SB_SUBSTATE_SB_DSP2_READ_LEVEL_SB2:
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_DSP2_READ_LEVEL_SB3;
			gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SB_READ_SB3_INIT;
			break;
		}
		break;
	}
}

void RetrySBReadSB2Fail_Callback(U8 ubMTIndex)
{
	gSBTask.ubState =  gSBTask.ubPrevState;
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;

	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_SB_READ_SB2_PLUS_MINUS_3_DELTA_READ:
	case RETRY_SB_3RD_STATE_SB_READ_SB2_PLUS_MINUS_1_DELTA_READ:
		RetrySBMTTriggerRetry(FALSE);
		break;
	case RETRY_SB_3RD_STATE_SB_READ_SB2_PLUS_MINUS_3_DELTA_DMA:
	case RETRY_SB_3RD_STATE_SB_READ_SB2_PLUS_MINUS_1_DELTA_DMA_WITH_XNOR:
		RetrySBReadSB2Pass_Callback(ubMTIndex);
		break;
	}
}

void RetrySBReadSB3Pass_Callback(U8 ubMTIndex)
{
	gSBTask.ubMTFailCnt = 0;
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
	gSBTask.ubState =  gSBTask.ubPrevState;

	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_SB_READ_SB3_READ:
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SB_READ_SB3_DMA;
		break;

	case  RETRY_SB_3RD_STATE_SB_READ_SB3_DMA:
		switch (gSBTask.ubSubstate) {
		case RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_SB3:
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_SB4;
			gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SB_READ_SB4_INIT;
			break;
		case RETRY_SB_SUBSTATE_SB_DSP2_READ_LEVEL_SB3:
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_DSP2_READ_LEVEL_SB4;
			gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SB_READ_SB4_INIT;
			break;
		}
		break;
	}
}

void RetrySBReadSB3Fail_Callback(U8 ubMTIndex)
{
	gSBTask.ubState =  gSBTask.ubPrevState;
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;

	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_SB_READ_SB3_READ:
		RetrySBMTTriggerRetry(FALSE);
		break;
	case RETRY_SB_3RD_STATE_SB_READ_SB3_DMA:
		RetrySBReadSB3Pass_Callback(ubMTIndex);
		break;
	}
}

void RetrySBReadSB4Pass_Callback(U8 ubMTIndex)
{
	gSBTask.ubMTFailCnt = 0;
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
	gSBTask.ubState =  gSBTask.ubPrevState;

	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_SB_READ_SB4_READ:
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SB_READ_SB4_DMA;
		break;
	case RETRY_SB_3RD_STATE_SB_READ_SB4_DMA:			//End of SB Read , Check to Next gSBTask.ubState
#if (RETRY_MICRON_NICKS)
		switch (gSBTask.ubState) {
		case RETRY_SB_STATE_SB_READ_WITH_READ_RETRY_TABLE:
		case RETRY_SB_STATE_SB_READ_WITH_READ_RETRY_TABLE_WITH_ARC:
			gSBTask.ubState = RETRY_SB_STATE_LOAD_DEFAULT_LLR_TABLE;
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_NULL;
			break;
		}
		break;
#else 	/*(RETRY_MICRON_NICKS)*/
		switch (gSBTask.ubSubstate) {
		case RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_SB4:
			gSBTask.ubState = RETRY_SB_STATE_LOAD_DEFAULT_LLR_TABLE;
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_NULL;
			break;
		case RETRY_SB_SUBSTATE_SB_DSP2_READ_LEVEL_SB4:
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_DSP2_SB_TRIG_SBC_FOR_ADT_LLR;
			gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_NULL;
			break;
		}
		break;
#endif	/*(RETRY_MICRON_NICKS)*/
	}
}

void RetrySBReadSB4Fail_Callback(U8 ubMTIndex)
{
	gSBTask.ubState =  gSBTask.ubPrevState;
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;

	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_SB_READ_SB4_READ:
		RetrySBMTTriggerRetry(FALSE);
		break;
	case RETRY_SB_3RD_STATE_SB_READ_SB4_DMA:
		RetrySBReadSB4Pass_Callback(ubMTIndex);
		break;
	}
}

void RetrySBClearSBOffsetFeatures( U8 ubPageType, U8 *pubReadRetryTable)
{
	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_SB_CLEAR_SB_OFFSET_INIT:
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SB_CLEAR_SB_OFFSET_PLUS_MINUS_0_DELTA_SET_FEATURE;
		gSBTask.ubFourthstate = RETRY_SB_4TH_STATE_SB_READ_LEVEL_BY_DELTA_TABLE_SET_FEATURE;
		if (RETRY_SB_EXTRA_PAGE == ubPageType) {
			gSBTask.ubSecondFeatureAddr = SB_MICRON_NEED_SET_SECOND_FEATURE_ADDRESS;
		}
	/* no break */
	case RETRY_SB_3RD_STATE_SB_CLEAR_SB_OFFSET_PLUS_MINUS_0_DELTA_SET_FEATURE:
		//Set B4 or B3 or B1/B2 by +-0 offset
		//May Need set 2 FA by ubSecondFeatureAddr flag
		RetrySBSetFeatureByReadRetryDeltaTableOffset( ubPageType, pubReadRetryTable, SB_MICRON_DELTA_TABLE_BIOS_0);
		break;
	}
}



void RetrySBCorrectMTTrigger( U8 dsp2_en, U8 sb_ibuf_ptr, U8 sel_2k, U8 dsp_en, U8 dsp_4k_en, U8 ubdecode_mode, U8 scale_mode, U8 flow_mode, U8 hb_sb, U8 ubMTIndex)
{
	U16 *fpu_ptr = NULL;
	FlhMT_t *mtp = NULL;
	MTCfg_t uoMTCfg;
	U16 uwFpu_offset;

	// hb_sb = 1 => SB correct
	// hb_sb = 0 => HB correct

	// set flow mode
	R32_FCON[R32_FCON_LDPC_CFG] &= ~(FLOW_MODE_MASK << FLOW_MODE_SHIFT);
	R32_FCON[R32_FCON_LDPC_CFG] |= (flow_mode << FLOW_MODE_SHIFT);

	///+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

	mtp = (FlhMT_t *)M_MT_ADDR(ubMTIndex);

	/// copy mtp tempelete here
	uwFpu_offset = FPU_PTR_OFFSET(fpu_sbc);
	fpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);
	fpu_ptr[0] = FPU_SBC((dsp_4k_en << 9) | (sel_2k << 8) | (dsp_en << 6) | (dsp2_en << 3) | (sb_ibuf_ptr << 0)) | (hb_sb << 7);

	memcpy((void *) & (gSBTask.MT), (void *) & (gSBTask.MTTemplate), sizeof(FlhMT_t));

	/// given ce_value, flh_type, io_type
	gSBTask.MT.dma.uwFPUPtr  = uwFpu_offset;

	gSBTask.MT.dma.btLDPCCorrectEn = 1;
	gSBTask.MT.dma.btInterruptVectorEn = 1;
	gSBTask.MT.dma.btScaleMode = scale_mode;
	gSBTask.MT.dma.DecodeMode = ubdecode_mode;
	gSBTask.MT.dma.btBCHBps = 1;
	gSBTask.MT.cmd.btMTPFormat = 1;
#if (PS5017_EN || PS5013_EN)
	gSBTask.MT.dma.btiFSAEn = 1;
#endif /* (PS5017_EN || PS5013_EN) */
	gSBTask.MT.dma.ADGSelectPointer = 0;

	gSBTask.MT.cmd.uliFSA0_1    = (U32)( (gSBTask.ulFSA_Align) + (gSBTask.ubCurr_frm_idx) );


	///// do global trigger
	//set MTQ_ID
	gSBTask.MT.cmd.btCESelectMode = 1;
	gSBTask.MT.cmd.ubCEValue = gSBTask.MTTemplate.cmd.ubCEValue;
	gSBTask.MT.cmd.NormalTargetCPU = MT_TARGET_CPU0;
	gSBTask.MT.cmd.ErrorTargetCPU = MT_TARGET_CPU0;

	memcpy((void *)mtp, (void *) & (gSBTask.MT), sizeof(FlhMT_t));

	// Global Trigger MT
	uoMTCfg.uoAll = 0;
	uoMTCfg.bits_recc.ubQUE_IDX = gSBTask.MTTemplate.cmd.ubCEValue;
	uoMTCfg.bits_recc.ubMT_IDX = ubMTIndex;
	uoMTCfg.bits_recc.btQOS = gSBTask.ubUseQorOrNot;
	uoMTCfg.u32.ulMT_CFG1 = 0;
	FlaGlobalTrigger(&uoMTCfg);

}


void RetrySBSetFeatureByReadRetryDeltaTableOffset( U8 ubPageType, U8 *pubReadRetryTable, U8 ubBiosValue)
{
	U8 ubMTIndex;
	U16 uwFpu_offset = FPU_PTR_OFFSET(fpu_entry_nop);
	U16 *puwfpu_ptr;
	U8 ubi = 0;
	U8 ubCheckFeatureParamNum = 0;
	U8 ubFeatureAddress = 0;

	U8 ubMinusShiftIndex = SB_MICRON_DELTA_TABLE_ORIGIN_SHIFT - ubBiosValue;
	U8 ubPlusShiftIndex = ubBiosValue + SB_MICRON_DELTA_TABLE_ORIGIN_SHIFT;
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, SB_MICRON_DELTA_TABLE_ORIGIN_SHIFT >= ubBiosValue);

	switch (ubPageType) {
	case RETRY_SB_LOWER_PAGE:
		ubFeatureAddress = 0xB4;
		break;
	case RETRY_SB_UPPER_PAGE:
		ubFeatureAddress = 0xB3;
		break;
	case RETRY_SB_EXTRA_PAGE:
		if (SB_MICRON_SETTING_SECOND_FEATURE_ADDRESS == gSBTask.ubSecondFeatureAddr) {
			ubFeatureAddress = 0xB2;
		}
		else {		//should be SB_MICRON_NEED_SET_SECOND_FEATURE_ADDRESS
			ubFeatureAddress = 0xB1;
		}
		break;
	}

	switch (gSBTask.ubFourthstate) {
	case RETRY_SB_4TH_STATE_SB_READ_LEVEL_BY_DELTA_TABLE_SET_FEATURE:
		uwFpu_offset = FPU_PTR_OFFSET(fpu_set_feature);
		puwfpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);
		/// reset fpu entry
		for (ubi = 0 ; ubi < SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH ; ubi++) {
			if (0 == ubi) {
				puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (ubi << 1)] = FPU_DAT_W_FIR_D(0x00);
			}
			else if ((SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH - 1) == ubi) {
				puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (ubi << 1)] = FPU_DAT_W_LAST_D(0x00);
			}
			else {
				puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (ubi << 1)] = FPU_DAT_W_MID_D(0x00);
			}
			puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (ubi << 1) + 1] = FPU_DAT_W(0x00);
		}
		puwfpu_ptr[0] = FPU_CMD(0xD5);				//set_feature
		puwfpu_ptr[1] = FPU_ADR_1B(gSBTask.ublun);
		if (FIP_FLASH_CLOCK_400MHZ == gFlhEnv.ubTargetFlashClock) {
			// flh clk 400
			puwfpu_ptr[3] = FPU_DLY(0x15);		// Decimal 21
		}
		else if (FIP_FLASH_CLOCK_333MHZ == gFlhEnv.ubTargetFlashClock) {
			// flh clk 333
			puwfpu_ptr[3] = FPU_DLY(0xF);		// Decimal 15
		}
		else {
			puwfpu_ptr[3] = FPU_DLY(0x10);		// Decimal 16
		}

		puwfpu_ptr[2] = FPU_ADR_1B(ubFeatureAddress);

		switch (ubFeatureAddress) {
		case 0xB4:		//Lower page, set <P1>, <P2>
			puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P1] = FPU_DAT_W_FIR_D(pubReadRetryTable[ubMinusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B4H_INDEX]);
			puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P1 + 1] = FPU_DAT_W(pubReadRetryTable[ubMinusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B4H_INDEX]);

			puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P2] = FPU_DAT_W_MID_D(pubReadRetryTable[ubPlusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B4H_INDEX]);
			puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P2 + 1] = FPU_DAT_W(pubReadRetryTable[ubPlusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B4H_INDEX]);
			break;
		case 0xB3:		//Upper page, set <P1>, <P2>, <P3>, <P4>
			puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P1] = FPU_DAT_W_FIR_D(pubReadRetryTable[ubMinusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B3H_INDEX_0]);
			puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P1 + 1] = FPU_DAT_W(pubReadRetryTable[ubMinusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B3H_INDEX_0]);

			puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P2] = FPU_DAT_W_MID_D(pubReadRetryTable[ubPlusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B3H_INDEX_1]);
			puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P2 + 1] = FPU_DAT_W(pubReadRetryTable[ubPlusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B3H_INDEX_1]);

			puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P3] = FPU_DAT_W_MID_D(pubReadRetryTable[ubMinusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B3H_INDEX_0]);
			puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P3 + 1] = FPU_DAT_W(pubReadRetryTable[ubMinusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B3H_INDEX_0]);

			puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P4] = FPU_DAT_W_LAST_D(pubReadRetryTable[ubPlusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B3H_INDEX_1]);
			puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P4 + 1] = FPU_DAT_W(pubReadRetryTable[ubPlusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B3H_INDEX_1]);
			break;
		case 0xB2:		//eXtra page, set <P1>, <P2>, <P3>, <P4>
			puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P1] = FPU_DAT_W_FIR_D(pubReadRetryTable[ubMinusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B2H_INDEX_0]);
			puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P1 + 1] = FPU_DAT_W(pubReadRetryTable[ubMinusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B2H_INDEX_0]);

			puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P2] = FPU_DAT_W_MID_D(pubReadRetryTable[ubPlusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B2H_INDEX_1]);
			puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P2 + 1] = FPU_DAT_W(pubReadRetryTable[ubPlusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B2H_INDEX_1]);

			puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P3] = FPU_DAT_W_MID_D(pubReadRetryTable[ubMinusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B2H_INDEX_0]);
			puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P3 + 1] = FPU_DAT_W(pubReadRetryTable[ubMinusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B2H_INDEX_0]);

			puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P4] = FPU_DAT_W_LAST_D(pubReadRetryTable[ubPlusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B2H_INDEX_1]);
			puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P4 + 1] = FPU_DAT_W(pubReadRetryTable[ubPlusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B2H_INDEX_1]);
			break;
		case 0xB1:		//eXtra page, set <P1>, <P2>, <P3>, <P4>
			puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P1] = FPU_DAT_W_FIR_D(pubReadRetryTable[ubMinusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B1H_INDEX_0]);
			puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P1 + 1] = FPU_DAT_W(pubReadRetryTable[ubMinusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B1H_INDEX_0]);

			puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P2] = FPU_DAT_W_MID_D(pubReadRetryTable[ubPlusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B1H_INDEX_1]);
			puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P2 + 1] = FPU_DAT_W(pubReadRetryTable[ubPlusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B1H_INDEX_1]);

			puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P3] = FPU_DAT_W_MID_D(pubReadRetryTable[ubMinusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B1H_INDEX_0]);
			puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P3 + 1] = FPU_DAT_W(pubReadRetryTable[ubMinusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B1H_INDEX_0]);

			puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P4] = FPU_DAT_W_LAST_D(pubReadRetryTable[ubPlusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B1H_INDEX_1]);
			puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P4 + 1] = FPU_DAT_W(pubReadRetryTable[ubPlusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B1H_INDEX_1]);
			break;
		}

		//Micron SB Todo
		puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_END_IDX] = FPU_END;

		break;
	case RETRY_SB_4TH_STATE_SB_READ_LEVEL_BY_DELTA_TABLE_GET_FEATURE:
		uwFpu_offset = FPU_PTR_OFFSET(fpu_get_feature);
		puwfpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);

		puwfpu_ptr[0] = FPU_CMD(0xD4);				//get_feature
		puwfpu_ptr[1] = FPU_ADR_1B(gSBTask.ublun);

		puwfpu_ptr[2] = FPU_ADR_1B(ubFeatureAddress);

		puwfpu_ptr[SB_MICRON_GET_FEATURE_FPU_END_IDX] = FPU_END;
		break;

	case RETRY_SB_4TH_STATE_SB_READ_LEVEL_BY_DELTA_TABLE_CHECK_FEATURE:
		uwFpu_offset = FPU_PTR_OFFSET(fpu_read_and_compare_feature_data);
		puwfpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);

		puwfpu_ptr[0] = FPU_CMD(0x00);		//check_feature
		puwfpu_ptr[1] = FPU_DLY(0x10);

		//Clear <P1><P2><P3><P3> Compare mask
		for (ubi = 0; ubi < PARAMETER_NUM_PER_FPU; ubi++) {
			puwfpu_ptr[SB_MICRON_CHECK_FEATURE_FPU_START_IDX + (ubi << 1)] =  FPU_DAT_R_CMP(0x00);
			puwfpu_ptr[(SB_MICRON_CHECK_FEATURE_FPU_START_IDX + 1) + (ubi << 1)] = FPU_DAT_R_MASK(0x00);
		}
		if (0xB4 == ubFeatureAddress) {
			ubCheckFeatureParamNum = 2;
		}
		else {
			ubCheckFeatureParamNum = PARAMETER_NUM_PER_FPU;
		}
		for (ubi = 0; ubi < ubCheckFeatureParamNum; ubi++) {		//Enable Param that need compare
			puwfpu_ptr[(SB_MICRON_CHECK_FEATURE_FPU_START_IDX + 1) + (ubi << 1)] = FPU_DAT_R_MASK(0xFF);
		}

		switch (ubFeatureAddress) {
		case 0xB4:		//Lower page, set <P1>, <P2>
			puwfpu_ptr[SB_MICRON_CHECK_FEATURE_FPU_START_IDX + (SB_MICRON_FEATURE_PARAM_P1_INDEX << 1)] = FPU_DAT_R_CMP(pubReadRetryTable[ubMinusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B4H_INDEX]);
			puwfpu_ptr[SB_MICRON_CHECK_FEATURE_FPU_START_IDX + (SB_MICRON_FEATURE_PARAM_P2_INDEX << 1)] = FPU_DAT_R_CMP(pubReadRetryTable[ubPlusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B4H_INDEX]);
			break;
		case 0xB3:		//Upper page, set <P1>, <P2>, <P3>, <P4>
			puwfpu_ptr[SB_MICRON_CHECK_FEATURE_FPU_START_IDX + (SB_MICRON_FEATURE_PARAM_P1_INDEX << 1)] = FPU_DAT_R_CMP(pubReadRetryTable[ubMinusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B3H_INDEX_0]);
			puwfpu_ptr[SB_MICRON_CHECK_FEATURE_FPU_START_IDX + (SB_MICRON_FEATURE_PARAM_P2_INDEX << 1)] = FPU_DAT_R_CMP(pubReadRetryTable[ubPlusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B3H_INDEX_1]);
			puwfpu_ptr[SB_MICRON_CHECK_FEATURE_FPU_START_IDX + (SB_MICRON_FEATURE_PARAM_P3_INDEX << 1)] = FPU_DAT_R_CMP(pubReadRetryTable[ubMinusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B3H_INDEX_0]);
			puwfpu_ptr[SB_MICRON_CHECK_FEATURE_FPU_START_IDX + (SB_MICRON_FEATURE_PARAM_P4_INDEX << 1)] = FPU_DAT_R_CMP(pubReadRetryTable[ubPlusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B3H_INDEX_1]);
		case 0xB2:		//eXtra page, set <P1>, <P2>, <P3>, <P4>
			puwfpu_ptr[SB_MICRON_CHECK_FEATURE_FPU_START_IDX + (SB_MICRON_FEATURE_PARAM_P1_INDEX << 1)] = FPU_DAT_R_CMP(pubReadRetryTable[ubMinusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B2H_INDEX_0]);
			puwfpu_ptr[SB_MICRON_CHECK_FEATURE_FPU_START_IDX + (SB_MICRON_FEATURE_PARAM_P2_INDEX << 1)] = FPU_DAT_R_CMP(pubReadRetryTable[ubPlusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B2H_INDEX_1]);
			puwfpu_ptr[SB_MICRON_CHECK_FEATURE_FPU_START_IDX + (SB_MICRON_FEATURE_PARAM_P3_INDEX << 1)] = FPU_DAT_R_CMP(pubReadRetryTable[ubMinusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B2H_INDEX_0]);
			puwfpu_ptr[SB_MICRON_CHECK_FEATURE_FPU_START_IDX + (SB_MICRON_FEATURE_PARAM_P4_INDEX << 1)] = FPU_DAT_R_CMP(pubReadRetryTable[ubPlusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B2H_INDEX_1]);
			break;
		case 0xB1:		//eXtra page, set <P1>, <P2>, <P3>, <P4>
			puwfpu_ptr[SB_MICRON_CHECK_FEATURE_FPU_START_IDX + (SB_MICRON_FEATURE_PARAM_P1_INDEX << 1)] = FPU_DAT_R_CMP(pubReadRetryTable[ubMinusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B1H_INDEX_0]);
			puwfpu_ptr[SB_MICRON_CHECK_FEATURE_FPU_START_IDX + (SB_MICRON_FEATURE_PARAM_P2_INDEX << 1)] = FPU_DAT_R_CMP(pubReadRetryTable[ubPlusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B1H_INDEX_1]);
			puwfpu_ptr[SB_MICRON_CHECK_FEATURE_FPU_START_IDX + (SB_MICRON_FEATURE_PARAM_P3_INDEX << 1)] = FPU_DAT_R_CMP(pubReadRetryTable[ubMinusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B1H_INDEX_0]);
			puwfpu_ptr[SB_MICRON_CHECK_FEATURE_FPU_START_IDX + (SB_MICRON_FEATURE_PARAM_P4_INDEX << 1)] = FPU_DAT_R_CMP(pubReadRetryTable[ubPlusShiftIndex * SB_MICRON_RETRY_VTH_LENGTH + SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_B1H_INDEX_1]);
			break;
		}
		break;
	}
	if (NES_EN || NCS_EN) {
		ubMTIndex = RetrySBPrepareFreeMT(RetrySBSetReadRetryDeltaPass_Callback, RetrySBSetReadRetryDeltaPass_Callback);
	}
	else {
		ubMTIndex = RetrySBPrepareFreeMT(RetrySBSetReadRetryDeltaPass_Callback, RetrySBSetReadRetryDeltaFail_Callback);
	}
	RetrySBSelecMTFPUTrigger(uwFpu_offset, (U32)( (gSBTask.ulFSA_Align) + (gSBTask.ubCurr_frm_idx) ), 0, ubMTIndex, SWITCH_CLK__LOW_CLK_MODE);

	if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
		gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
	}
	else {
		gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
		gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
	}
}

void RetrySBSetReadRetryDeltaPass_Callback(U8 ubMTIndex)
{
	gSBTask.ubMTFailCnt = 0;
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
	gSBTask.ubState =  gSBTask.ubPrevState;

	switch (gSBTask.ubFourthstate) {

	case RETRY_SB_4TH_STATE_SB_READ_LEVEL_BY_DELTA_TABLE_SET_FEATURE:
		gSBTask.ubFourthstate = RETRY_SB_4TH_STATE_SB_READ_LEVEL_BY_DELTA_TABLE_GET_FEATURE;
		break;
	case RETRY_SB_4TH_STATE_SB_READ_LEVEL_BY_DELTA_TABLE_GET_FEATURE:
		gSBTask.ubFourthstate = RETRY_SB_4TH_STATE_SB_READ_LEVEL_BY_DELTA_TABLE_CHECK_FEATURE;
		break;
	case RETRY_SB_4TH_STATE_SB_READ_LEVEL_BY_DELTA_TABLE_CHECK_FEATURE:
		gSBTask.ubFourthstate = RETRY_SB_4TH_STATE_SB_READ_LEVEL_BY_DELTA_TABLE_SET_FEATURE;

		//SB MICRON Check Point 0730
		M_UART(ERROR_SOFTBIT_, "\n CB gSBTask.ubSecondFeatureAddr :%d", gSBTask.ubSecondFeatureAddr);

		if (SB_MICRON_NEED_SET_SECOND_FEATURE_ADDRESS == gSBTask.ubSecondFeatureAddr) {	//check Flag for Second FA to set, If TRUE do Set feature again with second FA
			gSBTask.ubSecondFeatureAddr = SB_MICRON_SETTING_SECOND_FEATURE_ADDRESS;		//Update Flag for Second FA
		}
		else {
			if (SB_MICRON_SETTING_SECOND_FEATURE_ADDRESS == gSBTask.ubSecondFeatureAddr) {	//After Second FA has been set, Clear flag
				gSBTask.ubSecondFeatureAddr = SB_MICRON_NO_NEED_SET_SECOND_FEATURE_ADDRESS;
			}
			switch (gSBTask.ubThirdstate) {
			//SB1
			case RETRY_SB_3RD_STATE_SB_READ_SB1_PLUS_MINUS_2_DELTA_SET_FEATURE:
				gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SB_READ_SB1_PLUS_MINUS_2_DELTA_READ;
				break;
			//SB2
			case RETRY_SB_3RD_STATE_SB_READ_SB2_PLUS_MINUS_3_DELTA_SET_FEATURE:
				gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SB_READ_SB2_PLUS_MINUS_3_DELTA_READ;
				break;
			case RETRY_SB_3RD_STATE_SB_READ_SB2_PLUS_MINUS_1_DELTA_SET_FEATURE:
				gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SB_READ_SB2_PLUS_MINUS_1_DELTA_READ;
				break;
			//Clear SB offset
			case RETRY_SB_3RD_STATE_SB_CLEAR_SB_OFFSET_PLUS_MINUS_0_DELTA_SET_FEATURE:
				//To next SubState
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_RETRY_TABLE;
				gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SETTING_RETRY_TABLE_SET_FEATURE;
				break;
			}
		}
		break;
	}
}

void RetrySBSetReadRetryDeltaFail_Callback(U8 ubMTIndex)
{
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
	gSBTask.ubState =  gSBTask.ubPrevState;

	switch (gSBTask.ubFourthstate) {

	case RETRY_SB_4TH_STATE_SB_READ_LEVEL_BY_DELTA_TABLE_SET_FEATURE:
	case RETRY_SB_4TH_STATE_SB_READ_LEVEL_BY_DELTA_TABLE_GET_FEATURE:
		RetrySBMTTriggerRetry(TRUE);
		break;
	case RETRY_SB_4TH_STATE_SB_READ_LEVEL_BY_DELTA_TABLE_CHECK_FEATURE:
		if (gSBTask.ubMTCheckFeatureFailCnt < RETRY_SB_MT_FAIL_RETRY_CNT_MAX) {
			gSBTask.ubMTCheckFeatureFailCnt++;
			gSBTask.ubFourthstate = RETRY_SB_4TH_STATE_SB_READ_LEVEL_BY_DELTA_TABLE_SET_FEATURE;
		}
		else {
			gSBTask.ubMTCheckFeatureFailCnt = 0;
			gpVTDBUF->Retry.ulSetFeatureFailCnt++;
#if (!RELEASED_FW)
			DebugError();
#endif
			//If Set Get CHK feature fail, try to set to default feature, then Reset FLH
			RetrySBSwitchStateToSetDefaultFeatureReset();
			//Clear Flag for second FA
			gSBTask.ubSecondFeatureAddr = SB_MICRON_NO_NEED_SET_SECOND_FEATURE_ADDRESS;
		}
		break;
	}
}

void RetrySBDSP2 (void)
{
	U8 ubMTIndex;
	U8 ubPageType = 0;
	U32	ulFSA = 0;
	U8 ubNextWordLineExist = TRUE;

	switch (gSBTask.ubSubstate) {
	case RETRY_SB_SUBSTATE_SB_DSP2_INIT:
		switch (gSBTask.ubPageType) {
		case RETRY_SB_LOWER_PAGE:
			gSBTask.ubdecode_mode = SB_MICRON_DECODE_MODE_LOWER;
			break;
		case RETRY_SB_UPPER_PAGE:
			gSBTask.ubdecode_mode = SB_MICRON_DECODE_MODE_UPPER;
			break;
		case RETRY_SB_EXTRA_PAGE:
			gSBTask.ubdecode_mode = SB_MICRON_DECODE_MODE_EXTRA;
			break;
		default:
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
			RetrySBSwitchStateToSetDefaultFeatureReset();
			return;
		}
		//Caculate Next WL Share pages
		ubNextWordLineExist = RetrySBCaculateNextWordLineSharePage();
		if (FALSE == ubNextWordLineExist) {		//When Next WL is not exist(out of range)
			gSBTask.ubState = RETRY_SB_STATE_TERMINATE_FAIL;
			return;
		}
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_HB_READ_INIT;
		gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_DSP2_READ_TARGET_PAGE;

	/* no break */
	case RETRY_SB_SUBSTATE_SB_DSP2_READ_TARGET_PAGE:
		ubPageType = gSBTask.ubPageType;
		ulFSA = (U32)( (gSBTask.NextWordLineSharePage[0].ulSharePageFSA) + (gSBTask.ubCurr_frm_idx) );
		switch (ubPageType) {
		case RETRY_SB_LOWER_PAGE:
			RetrySBReadHB( ubPageType, TRUE, ulFSA, gSBTask.ubCurrentLDPCFrameIdx, 1, 0, 0);		//Lower, Next WL Share page to IBUF 0 top 2k
			break;
		case RETRY_SB_UPPER_PAGE:
			RetrySBReadHB( ubPageType, TRUE, ulFSA, gSBTask.ubCurrentLDPCFrameIdx, 1, 0, 1);		//Upper, Next WL Share page to IBUF 0 bottom 2k
			break;
		case RETRY_SB_EXTRA_PAGE:
			RetrySBReadHB( ubPageType, TRUE, ulFSA, gSBTask.ubCurrentLDPCFrameIdx, 2, 1, 0);		//eXtra, Next WL Share page to IBUF 0 bottom 2k
			break;
		}
		break;
	case RETRY_SB_SUBSTATE_SB_DSP2_GET_FEATURE_OF_ARC_RESULT:
		RetrySBGetReadLevelOffsetFromARC();
		break;
	case RETRY_SB_SUBSTATE_SB_DSP2_SET_READ_LEVEL_FOR_NEXT_WORDLINE_I:
		gSBTask.ubTempPageType = gSBTask.NextWordLineSharePage[1].ubSharePageType;
		RetrySBSetReadLevelOffset((U8 *)gubARCResultReadOffsetTable);
		break;
	case RETRY_SB_SUBSTATE_SB_DSP2_SET_READ_LEVEL_FOR_NEXT_WORDLINE_II:
		gSBTask.ubTempPageType = gSBTask.NextWordLineSharePage[2].ubSharePageType;
		RetrySBSetReadLevelOffset((U8 *)gubARCResultReadOffsetTable);
		break;
	case RETRY_SB_SUBSTATE_SB_DSP2_ENABLE_ARC_DISABLE_PERSISTENCE:
		RetrySBHBSetFeatureARC(TRUE, FALSE);
		break;
	case RETRY_SB_SUBSTATE_SB_DSP2_READ_NEXT_WORDLINE_SHARE_PAGE_I:
		//Micron SB Todo, IBUF ? 2K Frame ?
		ubPageType = gSBTask.NextWordLineSharePage[1].ubSharePageType;
		ulFSA = gSBTask.NextWordLineSharePage[1].ulSharePageFSA;
		switch (ubPageType) {
		case RETRY_SB_LOWER_PAGE:
			RetrySBReadHB( ubPageType, TRUE, ulFSA, gSBTask.ubCurrentLDPCFrameIdx, 1, 0, 0);		//Lower, Next WL Share page to IBUF 0 top 2k
			break;
		case RETRY_SB_UPPER_PAGE:
			RetrySBReadHB( ubPageType, TRUE, ulFSA, gSBTask.ubCurrentLDPCFrameIdx, 1, 0, 1);		//Upper, Next WL Share page to IBUF 0 bottom 2k
			break;
		}
		break;
	case RETRY_SB_SUBSTATE_SB_DSP2_READ_NEXT_WORDLINE_SHARE_PAGE_II:
		//Micron SB Todo, IBUF ? 2K Frame ?
		ubPageType = gSBTask.NextWordLineSharePage[2].ubSharePageType;
		ulFSA = gSBTask.NextWordLineSharePage[2].ulSharePageFSA;
		switch (ubPageType) {
		case RETRY_SB_UPPER_PAGE:
			RetrySBReadHB( ubPageType, TRUE, ulFSA, gSBTask.ubCurrentLDPCFrameIdx, 1, 0, 1);		//Upper, Next WL Share page to IBUF 0 top 2k
			break;
		case RETRY_SB_EXTRA_PAGE:
			RetrySBReadHB( ubPageType, TRUE, ulFSA, gSBTask.ubCurrentLDPCFrameIdx, 2, 1, 0);		//eXtra, Next WL Share page to IBUF 0 bottom 2k
			break;
		}
		break;
	case RETRY_SB_SUBSTATE_SB_DSP2_GEN_SB6_SB7:
		//Micron SB Todo

		//Disable DSP_EN when trigger DSP2
		M_FIP_CLEAR_LDPC_DSP_EN();

		// set group table
		M_FIP_SET_DSP2_GROUP_TABLE(gulGroupTable);		// SB6/SB7 Group Table from HDF_PS5013 Decoding_FLow_and_Error_Handling_Flow for BiCS4 TLC 20181127_(Ver 0.1)_SN00000002

		// trigger dsp2 engine to generate SB6/SB7
		//1. need to wait DSP_Trigger to 0 before trigger DSP2 for each channel.
		//2. trigger DSP2 for SB6 SB7
		M_FIP_WAIT_DSP_TRIGGER_DSP2_GENERATE_SB6_SB7(gSBTask.ubChannel);

		gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_DSP2_DISABLE_ARC_ENABLE_PERSISTENCE;
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_SET_FEATURE;

	/* no break */

	case RETRY_SB_SUBSTATE_SB_DSP2_DISABLE_ARC_ENABLE_PERSISTENCE:
		RetrySBHBSetFeatureARC(FALSE, TRUE);
		break;
	case RETRY_SB_SUBSTATE_SB_DSP2_READ_LEVEL_HB:
		RetrySBReadHB(  gSBTask.ubPageType, TRUE, (U32)( (gSBTask.ulFSA_Align) + (gSBTask.ubCurr_frm_idx) ), gSBTask.ubCurrentLDPCFrameIdx, 1, 0, 0);
		break;
	case RETRY_SB_SUBSTATE_SB_DSP2_READ_LEVEL_SB1:
		RetrySBReadSB1(   gSBTask.ubPageType, (U8 *)delta_table, gSBTask.ubCurrentLDPCFrameIdx);
		break;
	case RETRY_SB_SUBSTATE_SB_DSP2_READ_LEVEL_SB2:
		RetrySBReadSB2(  gSBTask.ubPageType, (U8 *)delta_table, gSBTask.ubCurrentLDPCFrameIdx);
		break;
	case RETRY_SB_SUBSTATE_SB_DSP2_READ_LEVEL_SB3:
		RetrySBReadSB3( gSBTask.ubPageType, DISABLE, (U8 *)delta_table, gSBTask.ubCurrentLDPCFrameIdx);
		break;
	case RETRY_SB_SUBSTATE_SB_DSP2_READ_LEVEL_SB4:
		RetrySBReadSB4( gSBTask.ubPageType, DISABLE, (U8 *)delta_table, gSBTask.ubCurrentLDPCFrameIdx);
		break;

	case RETRY_SB_SUBSTATE_SB_DSP2_SB_TRIG_SBC_FOR_ADT_LLR:
		//Enable DSP_EN before Trigger DSP
		M_FIP_SET_LDPC_DSP_EN();

		ubMTIndex = RetrySBPrepareFreeMT(RetrySBTriggerCorrectAdaptLLRPass_Callback, RetrySBTriggerCorrectAdaptLLRFail_Callback);
		RetrySBCorrectMTTrigger( 0, 0, 0, TRUE, 0, gSBTask.ubdecode_mode, 1, 0, FALSE, ubMTIndex);
		// check sb result
		if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
			gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
		}
		else {
			gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
			gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
		}
		break;
	case RETRY_SB_SUBSTATE_SB_DSP2_INT_SB_CORR:
		gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_DSP2_SB_CORR;

		//Disable DSP_EN before Trigger DSP2 Decode
		M_FIP_CLEAR_LDPC_DSP_EN();

	/* no break */

	case RETRY_SB_SUBSTATE_SB_DSP2_SB_CORR:
		/* Reset SB finish status */ // not sure
		R32_FCON[R32_FCON_LDPC_SBC] = 0;
		// set dsp2 llr table
		//Retry_SB_set_dsp2_llr(gSBTask.ubPageType);
		RetrySBupdate_dsp_with_DSP2_llr(gSBTask.ubPageType);
		if (RETRY_SB_STATE_SET_DEFAULT_FEATURE_AND_CHK == gSBTask.ubState) {
			break;
		}
		//Micron SB Todo, NCS
		if (NCS_EN) {
			ReadRetryDumpHWSetting(gSBTask.ubPageType, 0xD3, gSBTask.ubdecode_mode, 2, 0);
		}
		// fpu SB correct of 2K
		ubMTIndex = RetrySBPrepareFreeMT(RetrySBTriggerCorrectPass_Callback, RetrySBDSP2Fail_Callback);
		RetrySBCorrectMTTrigger(0, 0, gSBTask.ubCurrentLDPCFrameIdx, 0, 0, gSBTask.ubdecode_mode, 1, 2, 1, ubMTIndex);   //set flow mode = 2
		// check sb result
		if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
			gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
		}
		else {
			gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
			gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
		}
		break;
	}
	return;
}
void RetrySBDSP2Fail_Callback(U8 ubMTIndex)
{
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
	gSBTask.ubState = RETRY_SB_STATE_SB_DSP2;
	gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_DSP2_SB_CORR;
}
void RetrySBTriggerCorrectAdaptLLRPass_Callback(U8 ubMTIndex)
{
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
	gSBTask.ubMTFailCnt = 0;
	gSBTask.ubState =  gSBTask.ubPrevState;
	while ((R32_FCON[R32_FCON_LDPC_SBC] & ((SBC_BSY_MASK) << SBC_BSY_SHIFT))  );

	switch (gSBTask.ubSubstate) {
	case RETRY_SB_SUBSTATE_SB_DSP2_SB_TRIG_SBC_FOR_ADT_LLR:
		gSBTask.ubState = RETRY_SB_STATE_SB_DSP2;
		gSBTask.ubSubstate =  RETRY_SB_SUBSTATE_SB_DSP2_INT_SB_CORR;
		break;

	default:
		break;
	}

	if (R32_FCON[R32_FCON_LDPC_SBC] & SBC_SUCCEED_BIT) {
		if (SB_DUMP_DATA_DEBUG_EN) {
		}

		R32_FCON[R32_FCON_LDPC_SBC] = 0;
	}

	//Load ADT LLR from SBC result
	RetrySBFlashLoadECCParam(LLR_TABLE0_LOAD_BIT);

}

void RetrySBTriggerCorrectAdaptLLRFail_Callback(U8 ubMTIndex)
{
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
	switch (gSBTask.ubSubstate) {
	case RETRY_SB_SUBSTATE_SB_DSP2_SB_TRIG_SBC_FOR_ADT_LLR:
		gSBTask.ubState = RETRY_SB_STATE_SB_DSP2;
		gSBTask.ubSubstate =  RETRY_SB_SUBSTATE_SB_DSP2_SB_TRIG_SBC_FOR_ADT_LLR;
		break;
	default:
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
		RetrySBSwitchStateToSetDefaultFeatureReset();
		return;
	}
}

U8 RetrySBCaculateNextWordLineSharePage(void)
{
	U8 ubX = 0, ubTempX = 0;
	U16 uwPage = 0, uwY = 0, uwTempY = 0;
	U8 ubNextWordLineExist = TRUE;

	gSBTask.NextWordLineSharePage[0].ulSharePageFSA = gSBTask.ulFSA_Align;
	gSBTask.NextWordLineSharePage[1].ulSharePageFSA = gSBTask.ulFSA_Align;
	gSBTask.NextWordLineSharePage[2].ulSharePageFSA = gSBTask.ulFSA_Align;
	//Micron SB Todo
	uwPage = (gSBTask.ulFSA_Align >> gPCARule_Page.ubShift[COP0_PCA_RULE_0]) & gPCARule_Page.ulMask;
	ubX = FTLGetCoord(uwPage, IM_GETCOORD_X_VAL);
	uwY = FTLGetCoord(uwPage, IM_GETCOORD_Y_VAL);
	ubTempX = ubX;
	uwTempY = uwY;
	/*
		switch (gSBTask.ubPageType) {
		case RETRY_SB_LOWER_PAGE:
			switch (gFlhEnv.ubFlashID[4]) {
			case 0xA1:	//B16A
			case 0xA6:  //B17A
				uwTempY += SB_MICRON_NEXT_WORDLINE_Y_SHIFT;		//Next WL

				//Caculate VCA of Share lower page from X Y
				uwPage = RetrySBInvertCoord(ubTempX, uwTempY);
			if (IM_B17_AXIS_X_NULL_VALUE == uwPage) {
				ubNextWordLineExist = FALSE;
			}
				//Update Target FSA uwPage element to gen FSA of Share Lower page
				//PCA < -> FSA use same format, the different should only be value of "Block"
				gSBTask.NextWordLineSharePage[0].ulSharePageFSA &= ~(gPCARule_Page.ulMask << gPCARule_Page.ubShift[COP0_PCA_RULE_0]);
				gSBTask.NextWordLineSharePage[0].ulSharePageFSA |= (uwPage << gPCARule_Page.ubShift[COP0_PCA_RULE_0]);

				ubTempX ++;											//Upper page
				break;
			default:
				//MICRON_SB_ASSERT
				M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
				break;
			}
			//Caculate VCA of Share lower page from X Y
			uwPage = RetrySBInvertCoord(ubTempX, uwTempY);
			if (IM_B17_AXIS_X_NULL_VALUE == uwPage) {
				ubNextWordLineExist = FALSE;
			}
			//Update Target FSA uwPage element to gen FSA of Share Lower page
			//PCA < -> FSA use same format, the different should only be value of "Block"
			gSBTask.NextWordLineSharePage[1].ulSharePageFSA &= ~(gPCARule_Page.ulMask << gPCARule_Page.ubShift[COP0_PCA_RULE_0]);
			gSBTask.NextWordLineSharePage[1].ulSharePageFSA |= (uwPage << gPCARule_Page.ubShift[COP0_PCA_RULE_0]);

			ubTempX++;													//eXtra page
			uwPage = RetrySBInvertCoord(ubTempX, uwTempY);
			if (IM_B17_AXIS_X_NULL_VALUE == uwPage) {
				ubNextWordLineExist = FALSE;
			}
			//Update Target FSA uwPage element to gen FSA of Share Lower page
			//PCA < -> FSA use same format, the different should only be value of "Block"
			gSBTask.NextWordLineSharePage[2].ulSharePageFSA &= ~(gPCARule_Page.ulMask << gPCARule_Page.ubShift[COP0_PCA_RULE_0]);
			gSBTask.NextWordLineSharePage[2].ulSharePageFSA |= (uwPage << gPCARule_Page.ubShift[COP0_PCA_RULE_0]);
			break;

		case RETRY_SB_UPPER_PAGE:
			switch (gFlhEnv.ubFlashID[4]) {
			case 0xA1:	//B16A
			case 0xA6:  //B17A
				uwTempY += SB_MICRON_NEXT_WORDLINE_Y_SHIFT;		//Next WL

				//Caculate VCA of Share lower page from X Y
				uwPage = RetrySBInvertCoord(ubTempX, uwTempY);
			if (IM_B17_AXIS_X_NULL_VALUE == uwPage) {
				ubNextWordLineExist = FALSE;
			}
				//Update Target FSA uwPage element to gen FSA of Share Lower page
				//PCA < -> FSA use same format, the different should only be value of "Block"
				gSBTask.NextWordLineSharePage[0].ulSharePageFSA &= ~(gPCARule_Page.ulMask << gPCARule_Page.ubShift[COP0_PCA_RULE_0]);
				gSBTask.NextWordLineSharePage[0].ulSharePageFSA |= (uwPage << gPCARule_Page.ubShift[COP0_PCA_RULE_0]);

				ubTempX --;											//Lower page
				break;
			default:
				//MICRON_SB_ASSERT
				M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
				break;
			}
			//Caculate VCA of Share lower page from X Y
			uwPage = RetrySBInvertCoord(ubTempX, uwTempY);
			if (IM_B17_AXIS_X_NULL_VALUE == uwPage) {
				ubNextWordLineExist = FALSE;
			}
			//Update Target FSA uwPage element to gen FSA of Share Lower page
			//PCA < -> FSA use same format, the different should only be value of "Block"
			gSBTask.NextWordLineSharePage[1].ulSharePageFSA &= ~(gPCARule_Page.ulMask << gPCARule_Page.ubShift[COP0_PCA_RULE_0]);
			gSBTask.NextWordLineSharePage[1].ulSharePageFSA |= (uwPage << gPCARule_Page.ubShift[COP0_PCA_RULE_0]);

			ubTempX = ubX + 1;											//eXtra page
			uwPage = RetrySBInvertCoord(ubTempX, uwTempY);
			if (IM_B17_AXIS_X_NULL_VALUE == uwPage) {
				ubNextWordLineExist = FALSE;
			}
			//Update Target FSA uwPage element to gen FSA of Share Lower page
			//PCA < -> FSA use same format, the different should only be value of "Block"
			gSBTask.NextWordLineSharePage[2].ulSharePageFSA &= ~(gPCARule_Page.ulMask << gPCARule_Page.ubShift[COP0_PCA_RULE_0]);
			gSBTask.NextWordLineSharePage[2].ulSharePageFSA |= (uwPage << gPCARule_Page.ubShift[COP0_PCA_RULE_0]);
			break;

		case RETRY_SB_EXTRA_PAGE:
			switch (gFlhEnv.ubFlashID[4]) {
			case 0xA1:	//B16A
			case 0xA6:  //B17A
				uwTempY += SB_MICRON_NEXT_WORDLINE_Y_SHIFT;		//Next WL

				//Caculate VCA of Share lower page from X Y
				uwPage = RetrySBInvertCoord(ubTempX, uwTempY);
			if (IM_B17_AXIS_X_NULL_VALUE == uwPage) {
				ubNextWordLineExist = FALSE;
			}
				//Update Target FSA uwPage element to gen FSA of Share Lower page
				//PCA < -> FSA use same format, the different should only be value of "Block"
				gSBTask.NextWordLineSharePage[0].ulSharePageFSA &= ~(gPCARule_Page.ulMask << gPCARule_Page.ubShift[COP0_PCA_RULE_0]);
				gSBTask.NextWordLineSharePage[0].ulSharePageFSA |= (uwPage << gPCARule_Page.ubShift[COP0_PCA_RULE_0]);

				ubTempX = ubX - 2;									//Lower page
				break;
			default:
				//MICRON_SB_ASSERT
				M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
				break;
			}
			//Caculate VCA of Share lower page from X Y
			uwPage = RetrySBInvertCoord(ubTempX, uwTempY);
			if (IM_B17_AXIS_X_NULL_VALUE == uwPage) {
				ubNextWordLineExist = FALSE;
			}
			//Update Target FSA uwPage element to gen FSA of Share Lower page
			//PCA < -> FSA use same format, the different should only be value of "Block"
			gSBTask.NextWordLineSharePage[1].ulSharePageFSA &= ~(gPCARule_Page.ulMask << gPCARule_Page.ubShift[COP0_PCA_RULE_0]);
			gSBTask.NextWordLineSharePage[1].ulSharePageFSA |= (uwPage << gPCARule_Page.ubShift[COP0_PCA_RULE_0]);

			ubTempX = ubX - 1;											//Upper page
			uwPage = RetrySBInvertCoord(ubTempX, uwTempY);
			if (IM_B17_AXIS_X_NULL_VALUE == uwPage) {
				ubNextWordLineExist = FALSE;
			}
			//Update Target FSA uwPage element to gen FSA of Share Lower page
			//PCA < -> FSA use same format, the different should only be value of "Block"
			gSBTask.NextWordLineSharePage[2].ulSharePageFSA &= ~(gPCARule_Page.ulMask << gPCARule_Page.ubShift[COP0_PCA_RULE_0]);
			gSBTask.NextWordLineSharePage[2].ulSharePageFSA |= (uwPage << gPCARule_Page.ubShift[COP0_PCA_RULE_0]);
			break;

		}
	*/
	//TODO B47R
	switch (gFlhEnv.ubFlashID[4]) {
	case 0xA1:	//B16A
	case 0xA6:  //B17A
	case 0xA2: //B27A
#if IM_B27A
		uwTempY += SB_MICRON_NEXT_WORDLINE_Y_SHIFT_B27A;
#else
		uwTempY += SB_MICRON_NEXT_WORDLINE_Y_SHIFT; 	//Next WL
#endif

		switch (gSBTask.ubPageType) {
		case RETRY_SB_LOWER_PAGE:
			ubNextWordLineExist &= RetrySBRecordNextWLPagesFSA(ubTempX, uwTempY, 0);

			ubTempX ++;																	//Upper page
			ubNextWordLineExist &= RetrySBRecordNextWLPagesFSA(ubTempX, uwTempY, 1);

			ubTempX++;																	//eXtra page
			ubNextWordLineExist &= RetrySBRecordNextWLPagesFSA(ubTempX, uwTempY, 2);
			break;

		case RETRY_SB_UPPER_PAGE:
			ubNextWordLineExist &= RetrySBRecordNextWLPagesFSA(ubTempX, uwTempY, 0);

			ubTempX --;																	//Lower page
			ubNextWordLineExist &= RetrySBRecordNextWLPagesFSA(ubTempX, uwTempY, 1);

			ubTempX = ubX + 1;															//eXtra page
			ubNextWordLineExist &= RetrySBRecordNextWLPagesFSA(ubTempX, uwTempY, 2);
			break;

		case RETRY_SB_EXTRA_PAGE:
			ubNextWordLineExist &= RetrySBRecordNextWLPagesFSA(ubTempX, uwTempY, 0);

			ubTempX = ubX - 2;															//Lower page
			ubNextWordLineExist &= RetrySBRecordNextWLPagesFSA(ubTempX, uwTempY, 1);

			ubTempX = ubX - 1;															//Upper page
			ubNextWordLineExist &= RetrySBRecordNextWLPagesFSA(ubTempX, uwTempY, 2);
			break;
		}
		break;
	case 0xE6:  //B27B

		uwTempY += SB_MICRON_NEXT_WORDLINE_Y_SHIFT_B27B;		//Next WL
		switch (gSBTask.ubPageType) {
		case RETRY_SB_LOWER_PAGE:
			ubNextWordLineExist &= RetrySBRecordNextWLPagesFSA(ubTempX, uwTempY, 0);

			ubTempX--;																	//Upper page
			ubNextWordLineExist &= RetrySBRecordNextWLPagesFSA(ubTempX, uwTempY, 1);

			ubTempX--;																	//eXtra page
			ubNextWordLineExist &= RetrySBRecordNextWLPagesFSA(ubTempX, uwTempY, 2);
			break;

		case RETRY_SB_UPPER_PAGE:
			ubNextWordLineExist &= RetrySBRecordNextWLPagesFSA(ubTempX, uwTempY, 0);

			ubTempX++;																	//Lower page
			ubNextWordLineExist &= RetrySBRecordNextWLPagesFSA(ubTempX, uwTempY, 1);

			ubTempX = ubX - 1;															//eXtra page
			ubNextWordLineExist &= RetrySBRecordNextWLPagesFSA(ubTempX, uwTempY, 2);
			break;

		case RETRY_SB_EXTRA_PAGE:
			ubNextWordLineExist &= RetrySBRecordNextWLPagesFSA(ubTempX, uwTempY, 0);

			ubTempX = ubX + 2;															//Lower page
			ubNextWordLineExist &= RetrySBRecordNextWLPagesFSA(ubTempX, uwTempY, 1);

			ubTempX = ubX + 1;															//Upper page
			ubNextWordLineExist &= RetrySBRecordNextWLPagesFSA(ubTempX, uwTempY, 2);
			break;
		}

		break;
	default:
		//MICRON_SB_ASSERT
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
		break;
	}
	return ubNextWordLineExist;
}
U8 RetrySBRecordNextWLPagesFSA(U8 ubX, U8 ubY, U8 ubSharedPageIdx)
{
	U16 uwPage = 0;
	//Caculate VCA of Share lower page from X Y
	uwPage = RetrySBInvertCoord(ubX, ubY);
	if (IM_B17_AXIS_X_NULL_VALUE == uwPage) {
		return FALSE;
	}
	//Update Target FSA uwPage element to gen FSA of Share Lower page
	//PCA < -> FSA use same format, the different should only be value of "Block"
	gSBTask.NextWordLineSharePage[ubSharedPageIdx].ulSharePageFSA &= ~(gPCARule_Page.ulMask << gPCARule_Page.ubShift[COP0_PCA_RULE_0]);
	gSBTask.NextWordLineSharePage[ubSharedPageIdx].ulSharePageFSA |= (uwPage << gPCARule_Page.ubShift[COP0_PCA_RULE_0]);
	return TRUE;
}
void RetrySBupdate_dsp_with_DSP2_llr(U8 ubPageType)
{
	U8 ubLLRTable4ByteIdx = 0;
	U8 ubLLRTable5BitsIdx = 0;
	U8 ubTemp5bits = 0;
	U8 ubOperation = 0;
	U8 ubTableIdx = 0;

	U64 uoTemp64bits = 0;
	//NCS_Debug
	U8 ubDebugFlag = TRUE;
	LLRTable40BitsAccess_t LLRTableBitsAccess;


	for (ubTableIdx = 0 ; ubTableIdx < 4 ; ubTableIdx++) {

		if (3 == ubTableIdx) {	//DSP2_LLR_Idx_3 (SB6 = 1, SB7 = 1) should be all zero
			memset(&gSoftBitDSPLLRTable[ubTableIdx], 0, sizeof(SoftBitLLRTable_t));
		}
		else {
			////Get LLR table from FIP_DSP to temp variable////
			for (ubLLRTable4ByteIdx = 0 ; ubLLRTable4ByteIdx < SBIT_DSP_LLR_TABLE_SIZE_IN_4_BYTES ; ubLLRTable4ByteIdx++) {
				R32_FCON[R32_FCON_ECC_PARAM_CFG] &= ~(DSP_LLR_TABLE_SEL_MASK << DSP_LLR_TABLE_SEL_SHIFT);
				R32_FCON[R32_FCON_ECC_PARAM_CFG] |= (ubLLRTable4ByteIdx << DSP_LLR_TABLE_SEL_SHIFT);
				gSoftBitDSPLLRTable[ubTableIdx].LLRTable.AccessBy32bits.ulLLR32bits[ubLLRTable4ByteIdx]  = R32_FCON[R32_FCON_DSP_LLR_TABLE];
				if (RETRY_SB_ENABLE_DEBUG_MESSAGE) {
					if (ubDebugFlag) {
						//UartPrintf("LL_T %x \n", SoftBitDSPLLRTable[ubTableIdx].LLRTable.AccessBy32bits.ulLLR32bits[ubLLRTable4ByteIdx]);
					}
				}
			}
			ubDebugFlag = FALSE;
		}
	}

	for (ubTableIdx = 0 ; ubTableIdx < 4 ; ubTableIdx++) {
		if (RETRY_SB_ENABLE_DEBUG_MESSAGE) {
			//UartPrintf("\n //TabIdx %d // \n ", ubTableIdx);
		}
		if (3 == ubTableIdx) {
			//DSP2_LLR_Idx_3 (SB6 = 1, SB7 = 1) should be all zero
		}
		else {
			////Update LLR table in FIP by DSP2 LLR table////
			for (ubLLRTable5BitsIdx = 0 ; ubLLRTable5BitsIdx < SBIT_DSP_LLR_TABLE_SIZE_IN_5_BITS ; ubLLRTable5BitsIdx++) {
				//Check Operation(+1/-1/0) from DSP2 LLR table

				ubOperation = ((sb_dsp2_llr_table[ubPageType][ubTableIdx][ubLLRTable5BitsIdx >> SBIT_DSP_LLR_TABLE_5_BITS_TO_OPRATION_IDX_LOG] >> ((ubLLRTable5BitsIdx & BIT_MASK(SBIT_DSP_LLR_TABLE_5_BITS_TO_OPRATION_IDX_LOG)) * SBIT_DSP_LLR_TABLE_BITS_PER_OPRATION)) & BIT_MASK(SBIT_DSP_LLR_TABLE_BITS_PER_OPRATION));

				//Access 40bits by LLRTable40BitsAccess_t structure
				LLRTableBitsAccess.LLRTable.LLR40Bits = (gSoftBitDSPLLRTable[ubTableIdx].LLRTable.LLR40Bits[ubLLRTable5BitsIdx >> SBIT_DSP_LLR_TABLE_5_BITS_TO_40_BITS_IDX_LOG]);
				//Do bits shift, mask for 5bits from LLRTable40BitsAccess_t by 64bits access
				ubTemp5bits = ( LLRTableBitsAccess.LLRTable.uoAll >> (SBIT_DSP_LLR_TABLE_BITS_PER_HW_ACCESS * (ubLLRTable5BitsIdx & BIT_MASK(SBIT_DSP_LLR_TABLE_5_BITS_TO_40_BITS_IDX_LOG)))) & BIT_MASK(SBIT_DSP_LLR_TABLE_BITS_PER_HW_ACCESS);

				if (RETRY_SB_ENABLE_DEBUG_MESSAGE) {
					//UartPrintf("40b %L  ", LLRTableBitsAccess.LLRTable.uoAll);
				}

				if (RETRY_SB_ENABLE_DEBUG_MESSAGE) {
					//UartPrintf("Idx %d OP %d 5b %x ", ubLLRTable5BitsIdx, ubOperation, ubTemp5bits);
				}


				if (0x3 == ubOperation) {	//-1
					ubTemp5bits = ((ubTemp5bits + 0x1F) & BIT_MASK(SBIT_DSP_LLR_TABLE_BITS_PER_HW_ACCESS));
				}
				else if (0x0 == ubOperation) {	//0
					//do nothing
				}
				else if (0x1 == ubOperation) {	//+1
					ubTemp5bits = ((ubTemp5bits + 0x01) & BIT_MASK(SBIT_DSP_LLR_TABLE_BITS_PER_HW_ACCESS));
				}
				else {						//Not define
					M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
					RetrySBSwitchStateToSetDefaultFeatureReset();
				}

				if (RETRY_SB_ENABLE_DEBUG_MESSAGE) {
					//UartPrintf("5b_o %x ", ubTemp5bits);
				}

				//Clean 5 bits that we want update
				uoTemp64bits = (BIT_MASK64(SBIT_DSP_LLR_TABLE_BITS_PER_HW_ACCESS) << (SBIT_DSP_LLR_TABLE_BITS_PER_HW_ACCESS * (ubLLRTable5BitsIdx & BIT_MASK(SBIT_DSP_LLR_TABLE_5_BITS_TO_40_BITS_IDX_LOG))));
				uoTemp64bits = ~(uoTemp64bits);
				LLRTableBitsAccess.LLRTable.uoAll &= uoTemp64bits;

				//Set 5 bits result
				uoTemp64bits = ubTemp5bits;
				uoTemp64bits =  (uoTemp64bits << (SBIT_DSP_LLR_TABLE_BITS_PER_HW_ACCESS * (ubLLRTable5BitsIdx & BIT_MASK(SBIT_DSP_LLR_TABLE_5_BITS_TO_40_BITS_IDX_LOG))));
				LLRTableBitsAccess.LLRTable.uoAll |= uoTemp64bits;

				gSoftBitDSPLLRTable[ubTableIdx].LLRTable.LLR40Bits[ubLLRTable5BitsIdx >> SBIT_DSP_LLR_TABLE_5_BITS_TO_40_BITS_IDX_LOG] = LLRTableBitsAccess.LLRTable.LLR40Bits;
				if (RETRY_SB_ENABLE_DEBUG_MESSAGE) {
					//UartPrintf("40b_o %L \n", LLRTableBitsAccess.LLRTable.uoAll);
				}
			}
			if (RETRY_SB_ENABLE_DEBUG_MESSAGE) {
				////Get LLR table from FIP_DSP to temp variable////
				for (ubLLRTable4ByteIdx = 0 ; ubLLRTable4ByteIdx < SBIT_DSP_LLR_TABLE_SIZE_IN_4_BYTES ; ubLLRTable4ByteIdx++) {
					if (RETRY_SB_ENABLE_DEBUG_MESSAGE) {
						//UartPrintf("LL_T_o %x \n", SoftBitDSPLLRTable[ubTableIdx].LLRTable.AccessBy32bits.ulLLR32bits[ubLLRTable4ByteIdx]);
					}
				}

			}
		}
		////Set LLR table from temp variable to FIP_DSP////
		RetrySBFlashSetECCParam(&gSoftBitDSPLLRTable[ubTableIdx].LLRTable.AccessBy32bits.ulLLR32bits[0], SBIT_DSP_LLR_TABLE_SIZE_IN_4_BYTES, (LLR_TABLE0_LOAD_BIT << ubTableIdx), NORMAL_MODE);
	}

}

void RetrySBReleaseFinishMT(void)
{
	FlaAddFreeMTIndex(gpRetry->TrigMTList.MTCb[gpRetry->TrigMTList.ubHead].ubMTIndex);
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, gpRetry->TrigMTList.ubTrigCnt);
	gpRetry->TrigMTList.ubTrigCnt--;
	gpRetry->TrigMTList.ubHead = (gpRetry->TrigMTList.ubHead + 1) & RETRY_MAX_TRIG_MT_NUM_MASK;
}

void RetrySBSetDefaultFeatureAndCheck(void)
{
	switch (gSBTask.ubSubstate) {
	case RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_INIT:
		gSBTask.ubMTFailCnt = 0;
		gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_ARC;
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_SET_FEATURE;
		//Enable DSP_EN for Default setting
		M_FIP_SET_LDPC_DSP_EN();
	// no break
	case RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_ARC:
		RetrySBHBSetFeatureARC(FALSE, FALSE);
		break;
	case RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_READ_OFFSET_LOWER:
	case RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_READ_OFFSET_UPPER:
	case RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_READ_OFFSET_EXTRA:
		gSBTask.ubTempPageType = gSBTask.ubSubstate - RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_READ_OFFSET_LOWER;
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, gSBTask.ubTempPageType <= RETRY_SB_EXTRA_PAGE);
		RetrySBSetReadLevelOffset((U8 *)default_delta_table);
		break;

	case RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_SB_READ_OFFSET:
		RetrySBClearSBOffsetFeatures(gSBTask.ubPageType, (U8 *)default_delta_table);
		break;

	case RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_RETRY_TABLE:
		RetrySBHBSetFeatureRetryTable(0x00);		//Set Default Retry Table
		break;
	}
}
void RetrySBResetFlash(void)
{
	U16 uwFpu_offset;
	U8 ubMTIndex;
	uwFpu_offset = (0 == gSBTask.ubResetDie) ? FPU_PTR_OFFSET(fpu_entry_reset_fc) : FPU_PTR_OFFSET(fpu_entry_nop);
	ubMTIndex = RetrySBPrepareFreeMT(RetrySBResetFlashPass_Callback, RetrySBResetFlashFail_Callback);
	RetrySBSelecMTFPUTrigger( uwFpu_offset, 0, 0, ubMTIndex, SWITCH_CLK__NORMAL_MODE);
	if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
		gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
	}
	else {
		gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
		gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
	}
}

void RetrySBResetFlashPass_Callback(U8 ubMTIndex)
{
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
	gSBTask.ubMTFailCnt = 0;
	if ((gubDieNumber - 1) == gSBTask.ubResetDie) {
		gSBTask.ubState = gSBTask.ubBackUpedState;
		gSBTask.ubBackUpedState = RETRY_SB_STATE_BACKIPSTATE_NON_STATE;
		gSBTask.ubResetDie = 0;
	}
	else {
		gSBTask.ubResetDie++;
		gSBTask.ubState = RETRY_SB_STATE_SB_RESET_FLH;
	}
}

void RetrySBResetFlashFail_Callback(U8 ubMTIndex)
{
	gSBTask.ubState = gSBTask.ubPrevState;
	// REAL problem.....

	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);

	RetrySBMTTriggerRetry(FALSE);
}

void RetrySBFlashRetoreLLR(U8 lmu_sel, U8 llr_idx)
{
	U32 idx;

	if (llr_idx > LLR_TBL_CNT) {
		return;
	}

	R32_FCON[R32_FCON_LDPC_CFG] &=  ~( (PAGE_SELECT_MASK) << PAGE_SELECT_SHIFT );

	switch (lmu_sel) {
	case 0:
		R32_FCON[R32_FCON_LDPC_CFG] |=  (( 0 & PAGE_SELECT_MASK) << PAGE_SELECT_SHIFT);
		break;
	case 1:
		R32_FCON[R32_FCON_LDPC_CFG] |=  (( 1 & PAGE_SELECT_MASK) << PAGE_SELECT_SHIFT);
		break;
	case 2:
		R32_FCON[R32_FCON_LDPC_CFG] |=  (( 2 & PAGE_SELECT_MASK) << PAGE_SELECT_SHIFT);
		break;
	}

	for (idx = 0; idx < 10; idx++) {

		/* Select LLR table */
		R32_FCON[R32_FCON_ECC_PARAM_CFG] &= (~(ECC_PARAM_SEL_MASK << ECC_PARAM_SEL_SHIFT ));
		R32_FCON[R32_FCON_ECC_PARAM_CFG] |= (idx << ECC_PARAM_SEL_SHIFT);

		/* Fill LLR data */
		R32_FCON[R32_FCON_ECC_PARAM] = sb_llr_table[llr_idx][lmu_sel][idx];

		//SB MICRON Check Point 0730
		M_UART(ERROR_SOFTBIT_, "\n Def_LLR Idx %d ,%x", llr_idx, sb_llr_table[llr_idx][lmu_sel][idx]);

	}


	RetrySBFlashLoadECCParam(LLR_TABLE0_LOAD_BIT);

}

U16 RetrySBInvertCoord( U8 ubx, U16 uwY)
{
	//Micron SB Todo
	U16 uwSuperpageIdx = 0;
#if (IM_B27A || IM_B27B)
	U16 uwWordLine = uwY;
	U8 ubCase = 0xFF;
	U16 ubWordLineBase = 0xFF;
#endif

	if (MICRON_FSP_EN) {
		//TODO B47R
		switch (gFlhEnv.ubFlashID[4]) {

#if(IM_B17)
		case 0xA1:       //B16A
		case 0xA6:  //B17A
			if ((uwY) < IM_B17_AXIS_X_SECTION_1) {
				if ((ubx) % 3) {
					uwSuperpageIdx = IM_B17_AXIS_X_NULL_VALUE;
				}
				else {
					uwSuperpageIdx = ((uwY * 4) + (ubx / 3));
				}
				M_UART(ERROR_SOFTBIT_, "\n Sec_1");
			}
			else if ((uwY) < IM_B17_AXIS_X_SECTION_2) {
				if (2 == ((ubx) % 3)) {
					uwSuperpageIdx = IM_B17_AXIS_X_NULL_VALUE;
				}
				else {
					uwSuperpageIdx = (((uwY - IM_B17_AXIS_X_SECTION_1) * 8) + ((ubx / 3) + (ubx % 3)) + IM_B17_SECTION_1);
				}
				M_UART(ERROR_SOFTBIT_, "\n Sec_2");
			}
			else if ((uwY) < IM_B17_AXIS_X_SECTION_4) {
				if ((ubx) % 3) {
					uwSuperpageIdx = ( ((uwY - IM_B17_AXIS_X_SECTION_2) * 12) + ( ((ubx / 3) * 3) + ( 2 - (ubx % 3)) ) + IM_B17_SECTION_3 );
					M_UART(ERROR_SOFTBIT_, "\n Sec_4");
				}
				else {
					if ((uwY) < IM_B17_AXIS_X_SECTION_3) {
						uwSuperpageIdx = (((uwY - IM_B17_AXIS_X_SECTION_2) * 4) + (ubx / 3) + IM_B17_SECTION_2) ;
						M_UART(ERROR_SOFTBIT_, "\n Sec_3");
					}
					else {
						uwSuperpageIdx = (((uwY - IM_B17_AXIS_X_SECTION_3) * 12) + ((ubx / 3) * 3) + 62) ;
						M_UART(ERROR_SOFTBIT_, "\n Sec_4");
					}
				}

			}
			else if ((uwY) < IM_B17_AXIS_X_SECTION_5) {  //189
				if ((ubx) % 3) {
					uwSuperpageIdx = ( ((uwY - IM_B17_AXIS_X_SECTION_4) * 16) + ( ((ubx / 3) * 3) + ( 2 - (ubx % 3)) ) + IM_B17_SECTION_4 );
				}
				else {
					uwSuperpageIdx = (((uwY - IM_B17_AXIS_X_SECTION_3) * 12) + ((ubx / 3) * 3) + 62) ;
				}
				M_UART(ERROR_SOFTBIT_, "\n Sec_5");
			}
			else if ((uwY) < IM_B17_AXIS_X_SECTION_6) {  //192
				if ((ubx) % 3) {
					uwSuperpageIdx = ( ((uwY - IM_B17_AXIS_X_SECTION_5) * 8) + ( ((ubx / 3) * 2) + ( 2 - (ubx % 3)) ) + 2268 );
				}
				else {
					uwSuperpageIdx = (((uwY - IM_B17_AXIS_X_SECTION_3) * 12) + ((ubx / 3) * 3) + 62) ;
				}
				M_UART(ERROR_SOFTBIT_, "\n Sec_6");
			}
			else if ((uwY) < IM_B17_AXIS_X_SECTION_7) {  //195
				if ( 2 == ((ubx) % 3)) {
					uwSuperpageIdx = IM_B17_AXIS_X_NULL_VALUE;
				}
				else {
					uwSuperpageIdx = ( ((uwY - IM_B17_AXIS_X_SECTION_6) * 16) + ( ((ubx / 3) * 4) + (ubx % 3) ) + 2268 );
				}
				M_UART(ERROR_SOFTBIT_, "\n Sec_7");
			}
			else {
				if ((ubx) % 3) {
					uwSuperpageIdx = IM_B17_AXIS_X_NULL_VALUE;
				}
				else {
					uwSuperpageIdx = ((uwY * 4) + (ubx / 3) + 2292);
				}
				M_UART(ERROR_SOFTBIT_, "\n Sec_8");
			}
			break; // break Case B17A

#elif (IM_B27A)
		case 0xA2:	//B27A
			if (0 == uwWordLine) {	//SLC
				uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_0_PAGE_BASE;
				ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_0;
				ubWordLineBase = uwWordLine;
				if (0 != (ubx % 3)) {	//Upper, Xtra Page
					uwSuperpageIdx = IM_B27A_AXIS_INVALID_PAGE_IDX;
				}
			}
			else if (1 == uwWordLine) { //MLC
				uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_1_PAGE_BASE;
				ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_1;
				if (2 == (ubx % 3)) {	//Xtra Page
					uwSuperpageIdx = IM_B27A_AXIS_INVALID_PAGE_IDX;
				}
			}
			else if ((2 == uwWordLine)	|| (3 == uwWordLine)) {
				if (0 != (ubx % 3)) {	//Upper, Xtra Page
					uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_2_3_UPPER_EXTRA_PAGE_BASE;
					ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_2;
					ubWordLineBase = IM_B27A_AXIS_WORDLINE_2_3_WORDLINE_BASE;
				}
				else {	//Lower Page
					uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_2_3_LOWER_PAGE_BASE;
					ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_0;
					ubWordLineBase = IM_B27A_AXIS_WORDLINE_2_3_WORDLINE_BASE;
				}
			}
			else if ((4 <= uwWordLine) && (44 >= uwWordLine)) {
				uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_4_44_PAGE_BASE;
				ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_2;
				ubWordLineBase = IM_B27A_AXIS_WORDLINE_4_46_WORDLINE_BASE;
			}
			else if (45 == uwWordLine) {
				if (0 != (ubx % 3)) {	//Upper, Xtra Page
					uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_45_UPPER_EXTRA_PAGE_BASE;
					ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_3;
				}
				else {	//Lower Page
					uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_45_46_LOWER_PAGE_BASE;
					ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_2;
					ubWordLineBase = IM_B27A_AXIS_WORDLINE_4_46_WORDLINE_BASE;
				}
			}
			else if (46 == uwWordLine) {
				if (0 != (ubx % 3)) {	//Upper, Xtra Page
					uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_46_UPPER_EXTRA_PAGE_BASE;
					ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_4;
				}
				else {	//Lower Page
					uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_45_46_LOWER_PAGE_BASE;
					ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_2;
					ubWordLineBase = IM_B27A_AXIS_WORDLINE_4_46_WORDLINE_BASE;
				}
			}
			else if (47 == uwWordLine) {
				uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_47_PAGE_BASE;
				ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_5;
				if (2 == (ubx % 3)) {	//Xtra Page
					uwSuperpageIdx = IM_B27A_AXIS_INVALID_PAGE_IDX;
				}
			}
			else if (48 == uwWordLine) {
				if (0 != (ubx % 3)) {	//Upper, Xtra Page
					uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_48_UPPER_EXTRA_PAGE_BASE;
					ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_4;
				}
				else {	//Lower Page
					uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_48_LOWER_PAGE_BASE;
					ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_3;
				}
			}
			else if (49 == uwWordLine) {
				uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_49_PAGE_BASE;
				ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_5;
				if (2 == (ubx % 3)) {	//Xtra Page
					uwSuperpageIdx = IM_B27A_AXIS_INVALID_PAGE_IDX;
				}
			}
			else if (50 == uwWordLine) {
				if (0 !=  (ubx % 3)) {	//Upper, Xtra Page
					uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_50_UPPER_EXTRA_PAGE_BASE;
					ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_2;
					ubWordLineBase = uwWordLine;
				}
				else {	//Lower Page
					uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_50_LOWER_PAGE_BASE;
					ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_0;
					ubWordLineBase = uwWordLine;
				}
			}
			else if (51 == uwWordLine) {
				uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_51_PAGE_BASE;
				ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_1;
				if (2 == (ubx % 3)) {	//Xtra Page
					uwSuperpageIdx = IM_B27A_AXIS_INVALID_PAGE_IDX;
				}
			}
			else if (52 == uwWordLine) {
				if (0 != (ubx % 3)) {	//Upper, Xtra Page
					uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_52_UPPER_EXTRA_PAGE_BASE;
					ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_2;
					ubWordLineBase = IM_B27A_AXIS_WORDLINE_52_96_WORDLINE_BASE;
				}
				else {	//Lower Page
					uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_52_LOWER_PAGE_BASE;
					ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_0;
					ubWordLineBase = uwWordLine;
				}
			}
			else if (53 == uwWordLine) {
				if (0 != (ubx % 3)) {	//Upper, Xtra Page
					uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_53_UPPER_EXTRA_PAGE_BASE;
					ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_2;
					ubWordLineBase = IM_B27A_AXIS_WORDLINE_52_96_WORDLINE_BASE;
				}
				else {	//Lower Page
					uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_53_LOWER_PAGE_BASE;
					ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_3;
				}
			}
			else if ((54 <= uwWordLine) && (94 >= uwWordLine)) {
				uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_54_94_PAGE_BASE;
				ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_2;
				ubWordLineBase = IM_B27A_AXIS_WORDLINE_52_96_WORDLINE_BASE;
			}
			else if (95 == uwWordLine) {
				if (0 != (ubx % 3)) {	//Upper, Xtra Page
					uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_95_UPPER_EXTRA_PAGE_BASE;
					ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_3;
				}
				else {	//Lower Page
					uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_95_96_LOWER_PAGE_BASE;
					ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_2;
					ubWordLineBase = IM_B27A_AXIS_WORDLINE_52_96_WORDLINE_BASE;
				}
			}
			else if (96 == uwWordLine) {
				if (0 != (ubx % 3)) {	//Upper, Xtra Page
					uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_96_UPPER_EXTRA_PAGE_BASE;
					ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_4;
				}
				else {	//Lower Page
					uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_95_96_LOWER_PAGE_BASE;
					ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_2;
					ubWordLineBase = IM_B27A_AXIS_WORDLINE_52_96_WORDLINE_BASE;
				}
			}
			else if (97 == uwWordLine) {
				uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_97_PAGE_BASE;
				ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_5;
				if (2 == (ubx % 3)) {	//Xtra Page
					uwSuperpageIdx = IM_B27A_AXIS_INVALID_PAGE_IDX;
				}
			}
			else if (98 == uwWordLine) {
				uwSuperpageIdx = IM_B27A_AXIS_WORDLINE_98_PAGE_BASE;
				ubCase = IM_B27A_AXIS_X_Y_TO_PAGE_CASE_3;
				if (0 != (ubx % 3)) {	//Upper, Xtra Page
					uwSuperpageIdx = IM_B27A_AXIS_INVALID_PAGE_IDX;
				}
			}
			else {
				M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
			}
			if (IM_B27A_AXIS_INVALID_PAGE_IDX == uwSuperpageIdx) {
				break;
			}
			switch (ubCase) {
			case IM_B27A_AXIS_X_Y_TO_PAGE_CASE_0:
				uwSuperpageIdx += ((uwWordLine - ubWordLineBase) * 18  + (ubx / 3));
				break;
			case IM_B27A_AXIS_X_Y_TO_PAGE_CASE_1:
				uwSuperpageIdx += ((ubx / 3) * 2 + (ubx % 3));
				break;
			case IM_B27A_AXIS_X_Y_TO_PAGE_CASE_2:
				uwSuperpageIdx += (((uwWordLine - ubWordLineBase) - ((0 == (ubx % 3)) ? 2 : 0)) * 54 + ((ubx / 3) * 3) + (2 - (ubx % 3)));
				break;
			case IM_B27A_AXIS_X_Y_TO_PAGE_CASE_3:
				uwSuperpageIdx += ((ubx / 3) * 3 + (2 - ubx % 3));
				break;
			case IM_B27A_AXIS_X_Y_TO_PAGE_CASE_4:
				uwSuperpageIdx += (((ubx / 3) * 2) + (2 - (ubx % 3)));
				break;
			case IM_B27A_AXIS_X_Y_TO_PAGE_CASE_5:
				uwSuperpageIdx += ((ubx / 3) * 2 + (ubx % 3));
				break;

			default:
				M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
				break;
			}
			break; // break Case B27A
#elif IM_B27B

		case 0xE6:  //B27B
			if (0 == uwWordLine) {
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_0_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_0;
				ubWordLineBase = uwWordLine;
				if (2 > (ubx % 3)) {	//Upper, Xtra Page
					uwSuperpageIdx = IM_B27B_AXIS_INVALID_PAGE_IDX;
				}
			}
			else if (1 == uwWordLine) {
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_1_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_1;
				if (0 == (ubx % 3)) {	//Xtra Page
					uwSuperpageIdx = IM_B27B_AXIS_INVALID_PAGE_IDX;
				}
			}
			else if ((2 == uwWordLine)	|| (3 == uwWordLine)) {
				if (2 > (ubx % 3)) {	//Upper, Xtra Page
					uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_2_3_UPPER_EXTRA_PAGE_BASE;
					ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_2;
					ubWordLineBase = IM_B27B_AXIS_WORDLINE_2_3_WORDLINE_BASE;
				}
				else {	//Lower Page
					uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_2_3_LOWER_PAGE_BASE;
					ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_0;
					ubWordLineBase = IM_B27B_AXIS_WORDLINE_2_3_WORDLINE_BASE;
				}
			}
			else if ((4 <= uwWordLine) && (44 >= uwWordLine)) {
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_4_44_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_2;
				ubWordLineBase = IM_B27B_AXIS_WORDLINE_4_46_WORDLINE_BASE;
			}
			else if (45 == uwWordLine) {
				if (2 > (ubx % 3)) {	//Upper, Xtra Page
					uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_45_UPPER_EXTRA_PAGE_BASE;
					ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_3;
				}
				else {	//Lower Page
					uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_45_46_LOWER_PAGE_BASE;
					ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_2;
					ubWordLineBase = IM_B27B_AXIS_WORDLINE_4_46_WORDLINE_BASE;
				}
			}
			else if (46 == uwWordLine) {
				if (2 > (ubx % 3)) {	//Upper, Xtra Page
					uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_46_UPPER_EXTRA_PAGE_BASE;
					ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_4;
				}
				else {	//Lower Page
					uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_45_46_LOWER_PAGE_BASE;
					ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_2;
					ubWordLineBase = IM_B27B_AXIS_WORDLINE_4_46_WORDLINE_BASE;
				}
			}
			else if (47 == uwWordLine) {
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_47_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_5;
				if (0 == (ubx % 3)) {	//Xtra Page
					uwSuperpageIdx = IM_B27B_AXIS_INVALID_PAGE_IDX;
				}
			}
			else if (48 == uwWordLine) {
				if (2 > (ubx % 3)) {	//Upper, Xtra Page
					uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_48_UPPER_EXTRA_PAGE_BASE;
					ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_3;
				}
				else {	//Lower Page
					uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_48_LOWER_PAGE_BASE;
					ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_4;
				}
			}
			else if (49 == uwWordLine) {
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_49_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_5;
				if (0 == (ubx % 3)) {	//Xtra Page
					uwSuperpageIdx = IM_B27B_AXIS_INVALID_PAGE_IDX;
				}
			}
			else if (50 == uwWordLine) {
				if (2 > (ubx % 3)) {	//Upper, Xtra Page
					uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_50_UPPER_EXTRA_PAGE_BASE;
					ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_2;
					ubWordLineBase = uwWordLine;
				}
				else {	//Lower Page
					uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_50_LOWER_PAGE_BASE;
					ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_0;
					ubWordLineBase = uwWordLine;
				}
			}
			else if (51 == uwWordLine) {
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_51_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_1;
				if (0 == (ubx % 3)) {	//Xtra Page
					uwSuperpageIdx = IM_B27B_AXIS_INVALID_PAGE_IDX;
				}
			}
			else if (52 == uwWordLine) {
				if (2 > (ubx % 3)) {	//Upper, Xtra Page
					uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_52_UPPER_EXTRA_PAGE_BASE;
					ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_2;
					ubWordLineBase = IM_B27B_AXIS_WORDLINE_52_96_WORDLINE_BASE;
				}
				else {	//Lower Page
					uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_52_LOWER_PAGE_BASE;
					ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_0;
					ubWordLineBase = uwWordLine;
				}
			}
			else if (53 == uwWordLine) {
				if (2 > (ubx % 3)) {	//Upper, Xtra Page
					uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_53_UPPER_EXTRA_PAGE_BASE;
					ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_2;
					ubWordLineBase = IM_B27B_AXIS_WORDLINE_52_96_WORDLINE_BASE;
				}
				else {	//Lower Page
					uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_53_LOWER_PAGE_BASE;
					ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_4;
				}
			}
			else if ((54 <= uwWordLine) && (94 >= uwWordLine)) {
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_54_94_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_2;
				ubWordLineBase = IM_B27B_AXIS_WORDLINE_52_96_WORDLINE_BASE;
			}
			else if (95 == uwWordLine) {
				if (2 > (ubx % 3)) {	//Upper, Xtra Page
					uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_95_UPPER_EXTRA_PAGE_BASE;
					ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_3;
				}
				else {	//Lower Page
					uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_95_96_LOWER_PAGE_BASE;
					ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_2;
					ubWordLineBase = IM_B27B_AXIS_WORDLINE_52_96_WORDLINE_BASE;
				}
			}
			else if (96 == uwWordLine) {
				if (2 > (ubx % 3)) {	//Upper, Xtra Page
					uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_96_UPPER_EXTRA_PAGE_BASE;
					ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_4;
				}
				else {	//Lower Page
					uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_95_96_LOWER_PAGE_BASE;
					ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_2;
					ubWordLineBase = IM_B27B_AXIS_WORDLINE_52_96_WORDLINE_BASE;
				}
			}
			else if (97 == uwWordLine) {
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_97_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_5;
				if (0 == (ubx % 3)) {	//Xtra Page
					uwSuperpageIdx = IM_B27B_AXIS_INVALID_PAGE_IDX;
				}
			}
			else if (98 == uwWordLine) {
				uwSuperpageIdx = IM_B27B_AXIS_WORDLINE_98_PAGE_BASE;
				ubCase = IM_B27B_AXIS_X_Y_TO_PAGE_CASE_4;
				if (2 > (ubx % 3)) {	//Upper, Xtra Page
					uwSuperpageIdx = IM_B27B_AXIS_INVALID_PAGE_IDX;
				}
			}
			else {
				M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
			}
			if (IM_B27B_AXIS_INVALID_PAGE_IDX == uwSuperpageIdx) {
				break;
			}
			switch (ubCase) {
			case IM_B27B_AXIS_X_Y_TO_PAGE_CASE_0:
				uwSuperpageIdx += ((uwWordLine - ubWordLineBase) * 12  + (ubx / 3));
				break;
			case IM_B27B_AXIS_X_Y_TO_PAGE_CASE_1:
				uwSuperpageIdx += ((ubx / 3) * 2 + (2 - (ubx % 3)));
				break;
			case IM_B27B_AXIS_X_Y_TO_PAGE_CASE_2:
				uwSuperpageIdx += (((uwWordLine - ubWordLineBase) - ((2 == (ubx % 3)) ? 2 : 0)) * 36  + ubx);
				break;
			case IM_B27B_AXIS_X_Y_TO_PAGE_CASE_3:
				uwSuperpageIdx += ((ubx / 3) * 4 + (ubx % 3));
				break;
			case IM_B27B_AXIS_X_Y_TO_PAGE_CASE_4:
				uwSuperpageIdx += ubx;
				break;
			case IM_B27B_AXIS_X_Y_TO_PAGE_CASE_5:
				uwSuperpageIdx += ((ubx / 3) * 4 + (2 - (ubx % 3)));
				break;
			default:
				M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
				break;
			}
			break; // break Case B27A/B27B
#endif
		case 0xAA:  //N18
			// TBD
			break; // break Case N18
		}
	}
	return uwSuperpageIdx;
}

void RetrySBFPUTrigger(U16 uwFpu_offset, U8 int_en, U16 vct)
{
	R32_FCTL_CH[gSBTask.ubChannel][R32_FCTL_INT_VCT] &= CLR_NORMAL_CPU;
	R32_FCTL_CH[gSBTask.ubChannel][R32_FCTL_INT_VCT] |= (1 << NORMAL_CPU_SHIFT);

	R32_FCTL_CH[gSBTask.ubChannel][R32_FCTL_INT_VCT] &= CLR_ERROR_CPU;
	R32_FCTL_CH[gSBTask.ubChannel][R32_FCTL_INT_VCT] |= (1 << ERROR_CPU_SHIFT);

	R32_FCTL_CH[gSBTask.ubChannel][R32_FCTL_INT_CFG] &= (~INT_VEC_EN_BIT);

	if (uwFpu_offset != 0xFFFF) {
		R32_FCTL_CH[gSBTask.ubChannel][R32_FCTL_FPU_ENTRY] &= ~FPU_ADDR_BASE_SHIFT_MASK;
		R32_FCTL_CH[gSBTask.ubChannel][R32_FCTL_FPU_ENTRY] |= uwFpu_offset;
	}

	M_FIP_TRIG_FPU(gSBTask.ubChannel);
	while (M_FIP_CHK_FPU_BUSY(gSBTask.ubChannel));
}

void RetrySBBackupRestore( U64 dram_addr, U32 iram_addr, U8 ibf_ptr, U8 direction, U8 mode, U8 bch_mode, U8 all, U8 frm_msk)
{
	/*
	    direction --> 1: restore frame to IBF
	                  0: backup frame to DRAM/IRAM
	         mode --> 1: transfer DATA/Spare to/from DRAM/IRAM
	                  0: transfer ECC frame to/from DRAM
	*/
	U16 *fpu_ptr = NULL;
	U16 uwFpu_offset;
	//Wait FIP_CH_FPU_TRIGGER to 0x04, only in SB (other Queue empty)
	while (SRQ_SVL_BIT != (M_FIP_GET_FPU_TRIG(gSBTask.ubChannel) & (MT_BSY_BIT | ANY_BSY_BIT | SRQ_SVL_BIT | SIGN_OFF_BUSY_BIT | FPU_TRIGGER_BIT)));

	M_FIP_SET_RAW_DMA_ADR(gSBTask.ubChannel, dram_addr);
#if (PS5013_EN)
	M_FIP_SET_RAW_DMA_IRAM_ADR(gSBTask.ubChannel, iram_addr);
#else /* (PS5013_EN) */
	U32 iram_off;  //E19
	iram_off = (iram_addr - IRAM_BASE);
	M_FIP_SET_RAW_DMA_IRAM_ADR(gSBTask.ubChannel, iram_off);
#endif /* (PS5013_EN) */
	/// fpu_ptr = (U16*) (IRAM_BASE + FPU_ENTRY_DUMP_IBUF_TEST);

	uwFpu_offset = FPU_PTR_OFFSET(fpu_backup_restore_ibuf);
	fpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);

	*fpu_ptr = FPU_BR((frm_msk << 8) | (all << 6) | (bch_mode << 5) | (ibf_ptr << 2) | (direction << 1) | mode);
	fpu_ptr++;

	*fpu_ptr = FPU_END;

	RetrySBFPUTrigger( uwFpu_offset, FALSE, 0xFFFF);
#if (PS5017_EN || PS5021_EN)
	while (SRQ_SVL_BIT != (M_FIP_GET_FPU_TRIG(gSBTask.ubChannel) & (MT_BSY_BIT | ANY_BSY_BIT | SRQ_SVL_BIT | SIGN_OFF_BUSY_BIT | FPU_TRIGGER_BIT)));
#endif /* (PS5017_EN || PS5021_EN) */
}

void RetrySBBackupCorrect2k(U8 src_ibf, U8 ldpc_frame_idx, U32 ulbackup_addr, U32 ultmp_addr, U32 Iram_addr, U32 TempIram_addr, U8 ubMode)
{
	U32 ecc_size;
	DMACParam_t DMACParam;

	ecc_size = (RetrySBFlashGetCurrentLDPCFrameSize() - 2048 - 8);

	// setup ldpc frame size
	M_FIP_CLEAR_DRAM_MULTI(gSBTask.ubChannel);
	// Clean BR_W_LEN then set backup_restore length
	M_FIP_SET_BACK_RESTORE_LENGTH(gSBTask.ubChannel, ((((RetrySBFlashGetCurrentLDPCFrameSize() * 2) + 16) / 16) - 1));
	// backup data from channel ibf to DBUF_BASE
	if ((RETRY_SB_BACKUP_RESTORE_TO_DRAMIRAM == ubMode)
		|| (RETRY_SB_BACKUP_RESTORE_TO_DRAMIRAM_AND_KEEP == ubMode)) {
		//Retry_SB_backup_restore(ultmp_addr, TempIram_addr, src_ibf, 0, ubMode, 0, 0, 0);
		RetrySBBackupRestore(ultmp_addr, TempIram_addr, src_ibf, 0, 1, 0, 0, 0);
	}
	else if (ubMode == RETRY_SB_BACKUP_RESTORE_FROM_ONLYDRAM) {
		RetrySBBackupRestore(ultmp_addr, TempIram_addr, src_ibf, 1, 0, 0, 0, 0);
	}
	else {
		//Retry_SB_backup_restore(ultmp_addr, TempIram_addr, src_ibf, 0, ubMode, 0, 0, 0);
		RetrySBBackupRestore(ultmp_addr, TempIram_addr, src_ibf, 0, 0, 0, 0, 0);
	}

	/* For PS5011, data size in ibf format is following :

	      byte 15    byte 0
	      ------------------
	     |     2K data 0   |
	      -----------------
	     |     2K data 1   |
	      -----------------
	     | spr 1  |  spr 0 |
	      -----------------
	     |    x   |  CRC 0 |
	      -----------------
	     |      LDPC 0     |
	      -----------------
	     |    x   |  CRC 1 |
	      -----------------
	     |      LDPC 1     |
	      -----------------    */
	if (ubMode == RETRY_SB_BACKUP_RESTORE_TO_DRAMIRAM) {
		if (ldpc_frame_idx == 0) {
			// first 2k
			if (RETRY_SB_ENABLE_DEBUG_CMP_WITH_HB_DATA) {
				if ((gHBTask.ubHBFailFrameMap & BIT(gSBTask.ubCurr_frm_idx))) {

				}
				else {
					for (ecc_size = 0 ; ecc_size < BC_2KB ; ecc_size++) {
						if (  *((U8 *)ulbackup_addr + ecc_size) != *((U8 *)ultmp_addr + ecc_size) ) {
							M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
						}
					}
					for (ecc_size = 0 ; ecc_size < BC_8B ; ecc_size++) {
						if (  *((U8 *)Iram_addr + ecc_size) != *((U8 *)TempIram_addr + ecc_size) ) {
							M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
						}
					}
				}
			}
			/* copy first 2k data */
			DMACParam.ulSourceAddr = ultmp_addr;
			DMACParam.ulDestAddr = ulbackup_addr;
			DMACParam.ul32ByteNum = SIZE_IN_32B(BC_2KB);
			gpRetry->ubWaitDMACDoneFlag = TRUE;

			DMACCopy(DMAC_MODE_NORMAL_QUEUE, &DMACParam, gulReadRetryDMACDone_CallBack, 0);
			while (gpRetry->ubWaitDMACDoneFlag == TRUE) {
				DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
			}

			/* copy spr 0 */
			memcpy((U32 *)(Iram_addr), (U32 *)(TempIram_addr), BC_8B );

		}
		else {      // second 2k

			//        LOG_PRINTF("[Backup] 2nd correctable 2k to DBUF addr 0x%x\n", ulbackup_addr);
			if (RETRY_SB_ENABLE_DEBUG_CMP_WITH_HB_DATA) {
				if ((gHBTask.ubHBFailFrameMap & BIT(gSBTask.ubCurr_frm_idx))) {

				}
				else {
					for (ecc_size = 0 ; ecc_size < BC_2KB ; ecc_size++) {
						if (  *((U8 *)ulbackup_addr + BC_2KB + ecc_size) != *((U8 *)ultmp_addr + BC_2KB + ecc_size) ) {
							M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
						}
					}
					for (ecc_size = 0 ; ecc_size < BC_8B ; ecc_size++) {
						if (  *((U8 *)Iram_addr + BC_8B + ecc_size) != *((U8 *)TempIram_addr + BC_8B + ecc_size) ) {
							M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
						}
					}
				}
			}
			/* copy second 2k data */
			///memcpy((U32 *)(ulbackup_addr + BC_2KB), (U32 *)(ultmp_addr + BC_2KB), BC_2KB);
			DMACParam.ulSourceAddr = (ultmp_addr + BC_2KB);
			DMACParam.ulDestAddr = (ulbackup_addr + BC_2KB);
			DMACParam.ul32ByteNum = SIZE_IN_32B(BC_2KB);
			gpRetry->ubWaitDMACDoneFlag = TRUE;

			DMACCopy(DMAC_MODE_NORMAL_QUEUE, &DMACParam, gulReadRetryDMACDone_CallBack, 0);
			while (gpRetry->ubWaitDMACDoneFlag == TRUE) {
				DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
			}
			/* copy spr 1 */
			memcpy((U32 *)(Iram_addr + BC_8B ), (U32 *)(TempIram_addr + BC_8B), BC_8B);
		}
	}
	else if (ubMode == RETRY_SB_BACKUP_RESTORE_TO_ONLYDRAM) {
		if (ldpc_frame_idx == 0) {
			// first 2k

			//        LOG_PRINTF("[Backup] 1st correctable 2k to DBUF addr 0x%x\n", ulbackup_addr);

			/* copy first 2k data */
			///memcpy((U32 *)ulbackup_addr, (U32 *)ultmp_addr, BC_2KB);
			DMACParam.ulSourceAddr = (ultmp_addr);
			DMACParam.ulDestAddr = (ulbackup_addr);
			DMACParam.ul32ByteNum = SIZE_IN_32B(BC_2KB);
			gpRetry->ubWaitDMACDoneFlag = TRUE;

			DMACCopy(DMAC_MODE_NORMAL_QUEUE, &DMACParam, gulReadRetryDMACDone_CallBack, 0);
			while (gpRetry->ubWaitDMACDoneFlag == TRUE) {
				DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
			}

			/* copy spr 0 */
			memcpy((U32 *)(ulbackup_addr + BC_4KB), (U32 *)(ultmp_addr + BC_4KB), BC_8B);
#if 1
			/* copy crc 0 */
			memcpy((U32 *)(ulbackup_addr + BC_4KB + BC_16B), (U32 *)(ultmp_addr + BC_4KB + BC_16B), BC_8B);

			/* copy ldpc 0 */
			memcpy((U32 *)(ulbackup_addr + BC_4KB + BC_16B + BC_16B), (U32 *)(ultmp_addr + BC_4KB + BC_16B + BC_16B), ecc_size);
#endif

		}
		else {      // second 2k

			//        LOG_PRINTF("[Backup] 2nd correctable 2k to DBUF addr 0x%x\n", ulbackup_addr);

			/* copy second 2k data */
			///memcpy((U32 *)(ulbackup_addr + BC_2KB), (U32 *)(ultmp_addr + BC_2KB), BC_2KB);
			DMACParam.ulSourceAddr = (ultmp_addr + BC_2KB);
			DMACParam.ulDestAddr = (ulbackup_addr + BC_2KB);
			DMACParam.ul32ByteNum = SIZE_IN_32B(BC_2KB);
			gpRetry->ubWaitDMACDoneFlag = TRUE;

			DMACCopy(DMAC_MODE_NORMAL_QUEUE, &DMACParam, gulReadRetryDMACDone_CallBack, 0);
			while (gpRetry->ubWaitDMACDoneFlag == TRUE) {
				DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
			}

			/* copy spr 1 */
			memcpy((U32 *)(ulbackup_addr + BC_4KB + BC_8B), (U32 *)(ultmp_addr + BC_4KB + BC_8B), BC_8B);

#if 1
			/* copy crc 1 */
			memcpy((U32 *)(ulbackup_addr + BC_4KB + SIZE_32B + ecc_size), (U32 *)(ultmp_addr + BC_4KB + SIZE_32B + ecc_size), BC_8B);

			/* copy ldpc 1 */
			memcpy((U32 *)(ulbackup_addr + BC_4KB + SIZE_32B + ecc_size + BC_16B), (U32 *)(ultmp_addr + BC_4KB + SIZE_32B + ecc_size + BC_16B), ecc_size);
#endif
		}
	}
	else if (RETRY_SB_BACKUP_RESTORE_TO_ONLYDRAM_AND_KEEP == ubMode) {

	}
	else if (RETRY_SB_BACKUP_RESTORE_TO_DRAMIRAM_AND_KEEP == ubMode) {

	}
	else {
		M_UART(ERROR_SOFTBIT_, "\n Done!");
	}
}

void RetrySBBackupRestoreIBUF(U8 ubMTIndex)
{
	FlhMT_t MT;
	MTCfg_t uoMTCfg;
	U8 ubIBFPtr;
	U16 uwFPUPtr;
	U16 *puwFPU = NULL;
	FlhIntInfo8Bit_t ubFlhMsg;


	R32_FALL[R32_FCTL_BACK_RESTORE] |= DRAM_MULTI_EN;
#if (PS5013_EN)
	U8 ubi;
	for (ubi = 0; ubi  < gub4kEntrysPerPlane; ubi ++) {
		/*
		 * FCTL_BACK_RESTORE[18](DRAM_MULTI_EN) 		 �]1�|�Ұ�Back-up and restore�䴩4��buffer address�i�H�]�w
		 *	FCTL_BACK_RESTORE[17:16](DRAM_ADDR_SEL) ��ܭn�]�w�ĴX��DRAM_ADDR(�粒��g FCTL_RAW_DMA_ADR)�`�@��4�ӥi�H�]�w
		 */
		R32_FALL[R32_FCTL_BACK_RESTORE] &= ~(DRAM_ADDR_SEL_SHIFT_MASK);
		R32_FALL[R32_FCTL_BACK_RESTORE] |= M_SET_DRAM_ADDR(ubi);
		R32_FALL[R32_FCTL_RAW_DMA_ADR] = (gulVUCAddr +  BC_8KB + (BC_4KB + BC_1KB) * ubi);
	}
	R32_FALL[R32_FCTL_BACK_RESTORE] &= ~BACK_RESTORE_IADDR_MASK;
	R32_FALL[R32_FCTL_BACK_RESTORE] |= (0 & BACK_RESTORE_IADDR_MASK);
#endif /* (PS5013_EN) */

	gMTMgr.ubMTDoneMsg[(ubMTIndex) - MT_RETRY_START_INDEX].ubAll = 0;

	uwFPUPtr = FPU_PTR_OFFSET(fpu_backup_restore_ibuf);
	puwFPU = (U16 *)(IRAM_BASE + uwFPUPtr);

	// Fill in backup restore FPU
	for (ubIBFPtr = 0; ubIBFPtr < gub4kEntrysPerPlane ; ubIBFPtr++) {

		*puwFPU = FPU_BR((0 << 8) | (ALL_RESTORE_BY_PTR << 6) | (BCH_MODE_NO_ECC << 5) | (ubIBFPtr << 2) | (DIRECTION_TO_IBF << 1) | MODE_DBUF_ONLY);
		__asm ("DSB"); // Make sure the ordering of IRAM and FIP register
		puwFPU++;
	}
	*puwFPU = FPU_END;

	///Clear MT structure
	memset((void *)&MT, 0, MT_SIZE);

	MT.cmd.btConversionBypass = TRUE;
	MT.cmd.ALUSelect = gSBTask.MTTemplate.dma.ALUSelect;
	MT.cmd.btDisableUDMA = TRUE;
#if (PS5017_EN || PS5013_EN)
	MT.cmd.btiFSAEn = TRUE;
#endif /* (PS5017_EN || PS5013_EN) */

	MT.cmd.btIoType = gSBTask.MTTemplate.dma.btIoType;
	MT.cmd.FCLK_DIV = gSBTask.MTTemplate.dma.FCLK_DIV;
#if (PS5013_EN)
	MT.cmd.btFCLK_DIV_EN = gSBTask.MTTemplate.dma.btFCLK_DIV_EN;
#endif /* (PS5013_EN) */
	MT.cmd.FlashType = gSBTask.MTTemplate.dma.FlashType;

	MT.cmd.btInterruptVectorEn = TRUE;

	MT.cmd.uwFPUPtr  = uwFPUPtr;
	MT.cmd.btUpdPollingSequence	= TRUE;

	MT.cmd.uliFSA0_1 = gSBTask.MTTemplate.cmd.uliFSA0_1;
	MT.cmd.ubiFSA0_h = gSBTask.MTTemplate.cmd.ubiFSA0_h;

	MT.cmd.btCESelectMode = 1;
	MT.cmd.ubCEValue = gSBTask.MTTemplate.dma.ubCEValue;

	MT.cmd.NormalTargetCPU = MT_TARGET_CPU0;
	MT.cmd.ErrorTargetCPU = MT_TARGET_CPU0;
#if (PS5017_EN || PS5021_EN)
	MT.cmd.uliFSA0_1 = (gulVUCAddr +  BC_8KB);
	MT.cmd.uliFSA1_1 = (gulVUCAddr +  BC_8KB + (BC_4KB + BC_1KB) * 1);
	MT.cmd.uliFSA2_1 = (gulVUCAddr +  BC_8KB + (BC_4KB + BC_1KB) * 2);
	MT.cmd.uliFSA3_1 = (gulVUCAddr +  BC_8KB + (BC_4KB + BC_1KB) * 3);
#endif /* (PS5017_EN || PS5021_EN) */
#if (FIP_VIRTUAL_ADDRESS_WORKAROUND_EN)
	M_FIP_SET_POL_SEQUENCE_SELECT(MT.cmd.POL_SEQ_SEL, (M_FIP_GET_FSA_DIE_NUMBER(MT.cmd.uliFSA0_1, MT.cmd.ALUSelect) ? POL_SEQ_FPU_ENTRY_READ_STATUS_BUSY_40_DIE1 : POL_SEQ_FPU_ENTRY_READ_STATUS_BUSY_40));
#else /* (FIP_VIRTUAL_ADDRESS_WORKAROUND_EN) */
	M_FIP_SET_POL_SEQUENCE_SELECT(MT.cmd.POL_SEQ_SEL, POL_SEQ_FPU_ENTRY_READ_STATUS_BUSY_40);
#endif /* (FIP_VIRTUAL_ADDRESS_WORKAROUND_EN) */

	// Fill MT content to IRAM
	memcpy((void *)M_MT_ADDR(ubMTIndex), &MT, MT_SIZE);

	uoMTCfg.uoAll = 0;
	uoMTCfg.bits_recc.ubMT_IDX = (ubMTIndex & MT_IDX_MASK);
	uoMTCfg.bits_recc.ubQUE_IDX = MT.cmd.ubCEValue;
	uoMTCfg.bits_recc.btMT_HRD_LCK = TRUE;
	FlaGlobalTrigger(&uoMTCfg);

	///--------
	do {
		FIPDelegateCmd();
		ubFlhMsg = gMTMgr.ubMTDoneMsg[(gpRetry->TrigMTList.MTCb[gpRetry->TrigMTList.ubHead].ubMTIndex) - MT_RETRY_START_INDEX];
	} while (ubFlhMsg.ubAll == 0);

	gMTMgr.ubMTDoneMsg[(gpRetry->TrigMTList.MTCb[gpRetry->TrigMTList.ubHead].ubMTIndex) - MT_RETRY_START_INDEX].ubAll = 0;
}

U8 RetrySBget_dsp_rank(U8 rank_idx, U8 ubMode8Value)
{
	U64 dsp_center;

	//dsp_center = R32_FCON[R32_FCON_DSP_CENTER_0] | (R32_FCON[R32_FCON_DSP_CENTER_1] << 32);
	dsp_center = (R32_FCON[R32_FCON_DSP_CENTER_1]);
	dsp_center = dsp_center << 32;
	dsp_center &= 0xFFFFFFFF00000000;
	dsp_center = (dsp_center | R32_FCON[R32_FCON_DSP_CENTER_0]);


	// rank index 7: bit[60:56]
	// rank index 6: bit[52:48]
	// rank index 5: bit[44:40]
	// rank index 4: bit[36:32]
	// rank index 3: bit[28:24]
	// rank index 2: bit[20:16]
	// rank index 1: bit[12:8]
	// rank index 0: bit[4:0]
	if (ubMode8Value) {
		return ((dsp_center >> (rank_idx * 8)) & 0x1F) % 8;
	}
	else {
		return ((dsp_center >> (rank_idx * 8)) & 0x1F);
	}
}

void RetrySBProgNCSCMD( U16 uwFpu_offset, U8 io_type, U8 ubMTIndex, U8 ubMode)
{

	FlhMT_t *mtp = NULL;
	MTCfg_t uoMTCfg;
	L4KTable16B_t *pul4k_ptr = NULL;
	U8 ubFrameIndex;
	FlhIntInfo8Bit_t ubFlhMsg;
	////------------------------------------------------------------
	mtp = (FlhMT_t *)M_MT_ADDR(ubMTIndex);

	////------------------------------------------------------------
	pul4k_ptr = (L4KTable16B_t *)((U32) gSBTask.ultmp_SpareAddr);

	memcpy((void *) & (gSBTask.MT), (void *) & (gSBTask.MTTemplate), sizeof(FlhMT_t));

	if ((ubMode == RETRY_SB_INSERT_USED_MT_MODE) || (ubMode == RETRY_SB_INSERT_USED_MT_MODE_AND_WRITE_SPARE)) {
		gMTMgr.ubMTDoneMsg[(ubMTIndex) - MT_RETRY_START_INDEX].ubAll = 0;
	}


	gSBTask.MT.dma.btForce_R_Fail = 0;
	gSBTask.MT.dma.uliFSA0_1 = 0;
	gSBTask.MT.dma.btBMUAllocateEn = 0;
	gSBTask.MT.dma.uwFPUPtr  = uwFpu_offset;
	gSBTask.MT.cmd.btBusy = TRUE;
	gSBTask.MT.cmd.btUpdPollingSequence = 1;
	M_FIP_SET_POL_SEQUENCE_SELECT(gSBTask.MT.cmd.POL_SEQ_SEL, (POL_SEQ_FPU_ENTRY_READ_STATUS_LUN0_BUSY_20 + gSBTask.ublun));

	gSBTask.MT.dma.btConversionBypass = 1;
	gSBTask.MT.dma.btGC = 0;

	gSBTask.MT.dma.btLDPCCorrectEn = 0;
	gSBTask.MT.dma.btCRCCheckDis = 1;
	gSBTask.MT.dma.btDisableUDMA = 1;
	gSBTask.MT.dma.ADGSelectPointer = 0;



	gSBTask.MT.dma.btAllowSwitch = 0;
	gSBTask.MT.dma.L4KNum = 4;
	gSBTask.MT.dma.FrameNum = 4;
	gSBTask.MT.dma.btZipEn = 0;
	gSBTask.MT.dma.btCompareEn = 0;
	gSBTask.MT.dma.btBufferMode = 0;
	gSBTask.MT.dma.L4KSparePtr = ( gSBTask.ultmp_SpareAddr) & 0xFFFF;

	for (ubFrameIndex = 0; ubFrameIndex < FRAMES_PER_PAGE; ubFrameIndex++) {
		pul4k_ptr = (L4KTable16B_t *)((U32)(gSBTask.ultmp_SpareAddr + ubFrameIndex * L4K_SIZE));
		pul4k_ptr->BitMap.Read.FW = 0;
		pul4k_ptr->BitMap.Read.Zinfo = MAX_ZINFO;
		pul4k_ptr->BitMap.Read.ubBufferValid = 0xFF;
#if (PS5017_EN || PS5013_EN)
		pul4k_ptr->BitMap.Read.uwL4kNextPtr = 0xFFFF;
#endif /* (PS5017_EN || PS5013_EN) */
		pul4k_ptr->BitMap.Read.BADR = ((U32)(gulVUCAddr + ubFrameIndex * BC_4KB)) >> SECTOR_SIZE_SHIFT;

		if (ubMode == RETRY_SB_INSERT_USED_MT_MODE_AND_WRITE_SPARE) {
			if (ubFrameIndex == 0) {
				memcpy((U32 *)((U32)(gSBTask.ultmp_SpareAddr + ubFrameIndex * L4K_SIZE)), &gL4kSpareBackup, BC_8B);
			}
		}
	}

	///// do global trigger

	//set MTQ_ID
	gSBTask.MT.cmd.btCESelectMode = 1;
	gSBTask.MT.cmd.ubCEValue = gSBTask.MTTemplate.cmd.ubCEValue;
	gSBTask.MT.cmd.NormalTargetCPU = MT_TARGET_CPU0;
	gSBTask.MT.cmd.ErrorTargetCPU = MT_TARGET_CPU0;

	memcpy((void *)mtp, (void *) & (gSBTask.MT), sizeof(FlhMT_t));

	// Global Trigger MT
	uoMTCfg.uoAll = 0;
	uoMTCfg.bits_recc.ubQUE_IDX = gSBTask.MTTemplate.cmd.ubCEValue;
	uoMTCfg.bits_recc.ubMT_IDX = ubMTIndex;
	uoMTCfg.bits_recc.btQOS =  gSBTask.ubUseQorOrNot;
	uoMTCfg.u32.ulMT_CFG1 = 0;
	FlaGlobalTrigger(&uoMTCfg);


	///Directly wait till done
	if (ubMode == RETRY_SB_INSERT_NEW_MT_MODE) {
		while (gpRetry->TrigMTList.ubTrigCnt) {
			FIPDelegateCmd();
			ubFlhMsg = gMTMgr.ubMTDoneMsg[(gpRetry->TrigMTList.MTCb[gpRetry->TrigMTList.ubHead].ubMTIndex) - MT_RETRY_START_INDEX];

			if (ubFlhMsg.ubAll == 0) {
				continue; // MT not finish, break
			}

			RetrySBReleaseFinishMT();
		}
	}
	else {
		do {
			FIPDelegateCmd();
			ubFlhMsg = gMTMgr.ubMTDoneMsg[(gpRetry->TrigMTList.MTCb[gpRetry->TrigMTList.ubHead].ubMTIndex) - MT_RETRY_START_INDEX];
		} while (ubFlhMsg.ubAll == 0);

	}
}

void RetrySBTrappingSetFlowSetup (U8 ubEnableTrappingSet)
{
	U8 ubi;
	M_HB_DEBUG_UART(" TrappingSet");
	if (ENABLE == ubEnableTrappingSet) {
		gulTrappingSetParameterSB[HB_TRAPPING_SET_PARAMETER_EN_OFFSET] |= SET_BIT20; // Set bit 20 to enable trapping set parameter
		M_HB_DEBUG_UART("Enable\n");
	}
	else {
		gulTrappingSetParameterSB[HB_TRAPPING_SET_PARAMETER_EN_OFFSET] &= CLR_BIT20; // Clear bit20 to disable trapping set parameter
		M_HB_DEBUG_UART("Disable\n");
	}
	for (ubi = 0; ubi < HB_TRAPPING_SET_PARAMETER_NUM; ubi++) {
		M_FIP_SET_ECC_PARAM(ubi, gulTrappingSetParameterSB[ubi]);
	}
	M_FIP_LOAD_ECC_PARAM(BF_PARAM_LOAD_BIT);
}
#endif /* RETRY_HARDBIT_FOR_SDK_EN */

#if(RETRY_SOFTBITT_FOR_SDK_EN==FALSE)

void RetrySBSetLLR(void *buf, U32 page_id)
{
	R32_FCON[R32_FCON_LDPC_CFG] &= ~PAGE_SELECT_SHIFT_MASK;
	R32_FCON[R32_FCON_LDPC_CFG] |= (page_id << PAGE_SELECT_SHIFT);
	memcpy((void *)&R32_FCON[R32_FCON_DSP_LLR_TABLE], (void *)buf, SBIT_LLR_TABLE_SIZE);
}

void RetrySBGetLLR(void *buf, U32 page_id)
{
	R32_FCON[R32_FCON_LDPC_CFG] &= ~PAGE_SELECT_SHIFT_MASK;
	R32_FCON[R32_FCON_LDPC_CFG] |= (page_id << PAGE_SELECT_SHIFT);
	memcpy((void *)buf, (void *)&R32_FCON[R32_FCON_DSP_LLR_TABLE], SBIT_LLR_TABLE_SIZE);
}

#endif /* (RETRY_SOFTBITT_FOR_SDK_EN==FALSE) */

/*
 * ---------------------------------------------------------------------------------------------------
 *   public
 * ---------------------------------------------------------------------------------------------------
 */

#if(TRUE == RETRY_MICRON_NICKS)
/*
 *  SOFTBIT RETRY DECODING MAIN FUNCTION
 *  =========================
 *
 *  NOTE :
 *
 *  If user want to stay inside SB decoding for longer period
 *
 *  It can be done by
 *  1. Setting CALLBACK_FUNC_IN_COMMON_CODEBANK = TRUE to build loop
 *  2. Setting gSBTask.ubSBDecodeFlowLoopBreakFlag = TRUE
 *     for breakpoint
 *
 *	and it can be canceled by :
 *	1. Setting CALLBACK_FUNC_IN_COMMON_CODEBANK = FALSE
 *
 *		Then Program Counter will leave this decoding flow
 *		EVERY TIME after triggering MT & changing state
 *
 * Main function for Micron Nicks project
 */
void SBRetryMain(void)
{
#if(RETRY_SOFTBITT_FOR_SDK_EN==FALSE)
	U8 ubMTIndex;
	FlhIntInfo8Bit_t ubFlhMsg;
	MT_Callback_Func_t callback;
	U8 ubStep = 0;

	do {
		//SB MICRON Check Point 0722
		M_UART(ERROR_SOFTBIT_, "\nSB STATE:%d", gSBTask.ubState);
		M_UART(ERROR_SOFTBIT_, "\n    subSTATE:%d", gSBTask.ubSubstate);
		M_UART(ERROR_SOFTBIT_, "\n        ThirdSTATE:%d", gSBTask.ubThirdstate);
		M_UART(ERROR_SOFTBIT_, "\n            FourthSTATE:%d", gSBTask.ubFourthstate);

		switch (gSBTask.ubState) {

		case RETRY_SB_STATE_INIT:
			gSBTask.ubPrevState = RETRY_SB_STATE_INIT;

			/// init variable
			RetrySBInit();

			// Break when Assert happened in RetrySBInit
			if (RETRY_SB_STATE_SET_DEFAULT_FEATURE_AND_CHK == gSBTask.ubState) {
				break;
			}

			//SB MICRON Check Point 0806
			M_UART(ERROR_SOFTBIT_, "\n SB Skip??, ALU:%x , WLType: %d, WLStBypass: %d \n", gSBTask.MTTemplate.dma.ALUSelect, gSBTask.ubWLType, gSBTask.MTTemplate.dma.ulUserDefineReadDma.btWLStatusBypass);

			//Micron SB Todo
			if ( gSBTask.MTTemplate.dma.ALUSelect & ALU_RULE_SLC_BIT) {
				//			if ( (gSBTask.MTTemplate.dma.ALUSelect & ALU_RULE_SLC_BIT) || (gpOtherInfo->pRuleSet->pLMU->ubBit_No == 0) || (gpuwVBRMP[uwErrorPageUnitIndex].B.btIsSLCMode)) {
				M_UART(ERROR_SOFTBIT_, "\n SLC, skip Soft-bit decode");
				gSBTask.ubState =  RETRY_SB_STATE_SB_RESET_FLH;
				gSBTask.ubBackUpedState = RETRY_SB_STATE_DONE;
				gpRetry->RetryJobList[gpRetry->ubHead].ubGenFailInRR = FALSE;
				break;
			}
			else if ((SB_WORD_LINE_SLC == gSBTask.ubWLType) || (SB_WORD_LINE_MLC == gSBTask.ubWLType) //SLC, MLC, Open Page skip SB flow
				|| ((TRUE == gSBTask.MTTemplate.dma.ulUserDefineReadDma.btWLStatusBypass) && (FALSE == gpVT->FTL.btSPORDoing))) {
				M_UART(ERROR_SOFTBIT_, "\n skip SLC_MLC, Open page \n");
				gSBTask.ubState =  RETRY_SB_STATE_SB_RESET_FLH;
				gSBTask.ubBackUpedState = RETRY_SB_STATE_DONE;
				gpRetry->RetryJobList[gpRetry->ubHead].ubGenFailInRR = FALSE;
			}
			else {
				gSBTask.ubState = RETRY_SB_STATE_SCAN_ERR_FRAME;
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_INIT;
				//Find Lower page for Upper/Extra Pages in the share page group
				RetrySBCaculateShareLowerPage();
				//Enable DSP_EN for later ADT LLR
				M_FIP_SET_LDPC_DSP_EN();

				if (NCS_EN) {
					if (TRUE == gubNCSTLCOpenFlow) {
						gSBTask.ubState =  RETRY_SB_STATE_SB_RESET_FLH;
						gSBTask.ubBackUpedState = RETRY_SB_STATE_DONE;
						gpRetry->RetryJobList[gpRetry->ubHead].ubGenFailInRR = FALSE;
					}
				}
			}
			break;

		case RETRY_SB_STATE_SCAN_ERR_FRAME:
			gSBTask.ubPrevState = RETRY_SB_STATE_SCAN_ERR_FRAME;
			/// scan fpl_err_hdl->hb_retry->page.job->err_frm (gSBTask.ubErr_frm) to check which err_frm needs to be retryed
			gSBTask.ubCurrentLDPCFrameIdx = 0;
			gSBTask.ubErrorRecoverySBStep = 0;		//Reset Record Error Recovery Step to 0
			for ( ; gSBTask.ubCurr_frm_idx < gub4kEntrysPerPlane; gSBTask.ubCurr_frm_idx++) {
				if ( (BIT0 << gSBTask.ubCurr_frm_idx) & gSBTask.ubErr_frm ) {

					/// refresh addr
					gSBTask.ulbackup_addr = gSBTask.ulbackup_addr_base[gSBTask.ubCurr_frm_idx];///(0x1000 * gSBTask.ubCurr_frm_idx);
					gSBTask.ulSpareAddr = gSBTask.ulSpareAddr_base [gSBTask.ubCurr_frm_idx];//(gSBTask.ubCurr_frm_idx * 16);

					/// change state
					gSBTask.ubState = RETRY_SB_STATE_CHECK_ERR_LDPC_FRM;
					break;
				}
			}

			if ( gSBTask.ubCurr_frm_idx == gub4kEntrysPerPlane ) {	//All error frame been processed, leave SB flow
				RetrySBSwitchStateToSetDefaultFeatureReset();
			}
			break;
		case RETRY_SB_STATE_CHECK_ERR_LDPC_FRM:
			gSBTask.ubPrevState =  RETRY_SB_STATE_CHECK_ERR_LDPC_FRM;
			/// scan gpRetry->RetryJobList[gpRetry->ubHead].uwLDPCCurrentErrMap and give value to gSBTask->curr_ldpc_frm_idx
			/// *******************need to manually do  gSBTask->curr_ldpc_frm_idx++ after each SB retry done****************
			if ( gSBTask.ubCurrentLDPCFrameIdx == LDPC_FRAME_MAX_CNT) {
				/// this physical frame is done, change state
				if ( (gSBTask.uwLDPCCurrentErrMap & (     (BIT0 | BIT1) << ((gSBTask.ubCurr_frm_idx) << 1) )  ) == 0) {
					gSBTask.ubErr_frm &= ~(BIT0 << (gSBTask.ubCurr_frm_idx));
				}
				gSBTask.ubCurrentLDPCFrameIdx = 0;
				gSBTask.ubState = RETRY_SB_STATE_SCAN_ERR_FRAME;
				break;
			}

			if ( ( (gSBTask.uwLDPCCurrentErrMap) & ( BIT((gSBTask.ubCurr_frm_idx << 1) + gSBTask.ubCurrentLDPCFrameIdx) )  ) == 0) {
				if (gSBTask.ubIsAfterRaidECC > RETRY_SB_TASK_IS_RETRY_TASK_CASE) {
					/// do nothing
				}
				else {
					if (RETRY_READ_SB__DIRECTLY_GET_CORRECT_DATA_FROM_ERROR_PAGE) {
						/// read good 2K to buffer
						gSBTask.ubState = RETRY_SB_STATE_READ_GOOD_2K_TO_BUF;
						gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_HB_READ_INIT;
					}
				}
			}
			else {
				if (gSBTask.ubIsAfterRaidECC == RETRY_SB_TASK_IS_ERR_PAGE_AFTER_RAIDECC_CASE) {
					gSBTask.ubState = RETRY_SB_STATE_SB_WITH_OPT_READ_LEVEL__AFTER_RAIDECC;
					gSBTask.ubSubstate = RETRY_SB_SUBSTATE_WITH_OPT_READ_LEVEL__AFTER_RAIDECC__INIT;
				}
				else {
					gSBTask.ubState = RETRY_SB_STATE_SB_READ_WITH_READ_RETRY_TABLE;
					gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_INIT;
				}
			}
			break;

		case RETRY_SB_STATE_SB_READ_WITH_READ_RETRY_TABLE:
			gSBTask.ubPrevState = RETRY_SB_STATE_SB_READ_WITH_READ_RETRY_TABLE;
			RetrySBReadFlow();
			gSBTask.ubllr_index = SB_MICRON_DEFAULT_LLR_TABLE_START_IDX;
			break;

		case RETRY_SB_STATE_LOAD_DEFAULT_LLR_TABLE:
			gSBTask.ubPrevState =  RETRY_SB_STATE_LOAD_DEFAULT_LLR_TABLE;
			if (gSBTask.ubllr_index == gSBTask.ubllr_index_MAX ) {
				gSBTask.ubllr_index = 0;
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_DECODE_WITH_ADT_LLR_INIT;
				gSBTask.ubState = RETRY_SB_STATE_SB_DECODE_WITH_ADT_LLR;
				//Load ADT LLR from Decode result
				RetrySBFlashLoadECCParam(LLR_TABLE0_LOAD_BIT);
				break;
			}
			RetrySBFlashRetoreLLR(gSBTask.ubPageType, gSBTask.ubllr_index); // set default llr table
			gSBTask.ubState = RETRY_SB_STATE_SB_DECODE_WITH_DEFAULT_LLR;
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_DECODE_WITH_DEFAULT_LLR_INIT;
			break;

		case RETRY_SB_STATE_SB_DECODE_WITH_DEFAULT_LLR:
			gSBTask.ubPrevState = RETRY_SB_STATE_SB_DECODE_WITH_DEFAULT_LLR;
			RetrySBDecodeFlow();
			break;

		case RETRY_SB_STATE_SB_DECODE_WITH_ADT_LLR:
			gSBTask.ubPrevState = RETRY_SB_STATE_SB_DECODE_WITH_ADT_LLR;
			RetrySBDecodeFlow();
			break;

		case  RETRY_SB_STATE_SELECT_READ_RETRY_TABLE:
			gSBTask.ubPrevState =  RETRY_SB_STATE_SELECT_READ_RETRY_TABLE;

			gSBTask.ubSpecificRetryTable = gubSpecificRetryTable[gSBTask.ubSpecificRetryTableRound];

			gSBTask.ubState =  RETRY_SB_STATE_HB_WITH_AUTO_READ_CALIBRATION;
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_WITH_SPECIFIC_RETRY_TABLE_READ_OFFSET_INIT;


			break;

		case RETRY_SB_STATE_HB_WITH_AUTO_READ_CALIBRATION:
			gSBTask.ubPrevState = RETRY_SB_STATE_HB_WITH_AUTO_READ_CALIBRATION;

			//Micron Nick use Specific mode to do RRX + AutoCal
			gSBTask.ubSpecificMode = SB_MICRON_SPECIFIC_RETRY_TABLE;
			RetrySBHBReadwithRetryTableOrReadOffset();
			break;

		case RETRY_SB_STATE_SB_READ_WITH_READ_RETRY_TABLE_WITH_ARC:
			gSBTask.ubPrevState = RETRY_SB_STATE_SB_READ_WITH_READ_RETRY_TABLE;
			gSBTask.ubSBReadDecodeWithARC = TRUE;
			RetrySBReadFlow();
			gSBTask.ubllr_index = SB_MICRON_DEFAULT_LLR_TABLE_START_IDX;
			break;


		case RETRY_SB_STATE_TERMINATE_PASS:
			//SBRAID Debug 1115
			/*
			if (DEBUG_SOFTBITRAIDECC_UART) {
				UartPrintf("\n [SB]  PCA:%x \n", (U32)( (gSBTask.ulFSA_Align) + (gSBTask.ubCurr_frm_idx) ));
			}
			*/
			gSBTask.uwLDPCCurrentErrMap &= ~(     (BIT(gSBTask.ubCurrentLDPCFrameIdx)) << ((gSBTask.ubCurr_frm_idx) << 1)       );
			gSBTask.ubCaseABCnt = 0;

			//Update Error Recovery Step Cnt when both LDPC Frame been Decode Pass
			if (((     (BIT0 | BIT1) << ((gSBTask.ubCurr_frm_idx) << 1)       ) & gSBTask.uwLDPCCurrentErrMap) == 0) {
				M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (0 != gSBTask.ubErrorRecoverySBStep));
			}

		/* no break */
		case RETRY_SB_STATE_CHECK_NEXT_STATE:
			//Micron SB Fix 1004
			gSBTask.ubSpecificRetryTableRound = 0;
			gSBTask.ubSBReadDecodeWithARC = FALSE;

			/// if collected 4K, use FPU COR to send it out.
			gSBTask.ubCurrentLDPCFrameIdx++;
			RetrySBSwitchToSetDefaultFeatureThenDoBackupSBFlow(RETRY_SB_STATE_CHECK_ERR_LDPC_FRM, RETRY_SB_SUBSTATE_NULL);
			if (gSBTask.ubCurrentLDPCFrameIdx == LDPC_FRAME_MAX_CNT) {
				if ( (gSBTask.uwLDPCCurrentErrMap & (     (BIT0 | BIT1) << ((gSBTask.ubCurr_frm_idx) << 1) )  ) == 0) {
					gSBTask.ubErr_frm &= ~(BIT0 << (gSBTask.ubCurr_frm_idx));
				}
				gSBTask.ubCurrentLDPCFrameIdx = 0;
				gSBTask.ubCurr_frm_idx++;
				RetrySBSwitchToSetDefaultFeatureThenDoBackupSBFlow(RETRY_SB_STATE_SCAN_ERR_FRAME, RETRY_SB_SUBSTATE_NULL);
				//Update Error Recovery Step Trigger Cnt when both LDPC Frame been accessed
				M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (0 != gSBTask.ubErrorRecoverySBStep));

				if (RETRY_SB_MICRON_ERROR_RECOVERY_READ_RETRY_ARC_STEP <= gSBTask.ubErrorRecoverySBStep) {		//Micron Nicks flow need refresh block when hit step11
					gSBTask.ubBlkRefresh = TRUE;
				}
			}

			break;

		case RETRY_SB_STATE_TERMINATE_FAIL:
			//Micron SB Todo
			//Reset ARC/SBSBR/DECODE_MODE
			gSBTask.ubCaseABCnt = SOFTBIT_RETRY_CASE_A;
			gSBTask.ubState =  RETRY_SB_STATE_CHECK_NEXT_STATE;
			break;

		case RETRY_SB_STATE_RECEIVE_CQ:
			if (FALSE == FIPIsStopAllocateEventSet(FIP_STOP_ALLOCATE_EVENT_SB_WAIT_MT_DONE)) {
				FIPSetStopAllocateBuf(FIP_STOP_ALLOCATE_EVENT_SB_WAIT_MT_DONE);
			}
			FIPDelegateCmd();
			while (gpRetry->TrigMTList.ubTrigCnt) {
				ubMTIndex = gpRetry->TrigMTList.MTCb[gpRetry->TrigMTList.ubHead].ubMTIndex;

				ubFlhMsg = gMTMgr.ubMTDoneMsg[ubMTIndex - MT_RETRY_START_INDEX];

				if (ubFlhMsg.ubAll == 0) {
					break; // MT not finish, break
				}

				gpRetry->RttTimer.uoStartRTT = 0;
				gpRetry->RttTimer.uoEndRTT   = 0;

				if ((ubFlhMsg.btMTStop) || (ubFlhMsg.btErasePage)) {
					//SB MICRON Check Point 0724
					M_UART(ERROR_SOFTBIT_, "\n FailCallBack!!");
					callback = (MT_Callback_Func_t)gpRetry->TrigMTList.MTCb[gpRetry->TrigMTList.ubHead].FailCallback;
				}
				else {
					callback = (MT_Callback_Func_t)gpRetry->TrigMTList.MTCb[gpRetry->TrigMTList.ubHead].PassCallback;
				}
				if (callback != NULL) {
					callback(ubMTIndex);
				}

				RetrySBReleaseFinishMT();
				if (0 == gpRetry->TrigMTList.ubTrigCnt) {
					FIPClearStopAllocateBuf(FIP_STOP_ALLOCATE_EVENT_SB_WAIT_MT_DONE);
				}
			}
			break;
		case RETRY_SB_STATE_SET_DEFAULT_FEATURE_AND_CHK:
			gSBTask.ubPrevState =  RETRY_SB_STATE_SET_DEFAULT_FEATURE_AND_CHK;
			RetrySBSetDefaultFeatureAndCheck();
			break;
		case RETRY_SB_STATE_SB_RESET_FLH:
			gSBTask.ubPrevState =  RETRY_SB_STATE_SB_RESET_FLH;
			RetrySBResetFlash();
			break;
		case RETRY_SB_STATE_DONE:
			gSBTask.ubSBDecodeFlowLoopBreakFlag = 1;
			// For DriveLog Debug Info, Record delta Table Offset
			gSBTask.ubDeltaTablePassReadLevelOffset = delta_table[3][3];
			break;
		}

		if ((CALLBACK_FUNC_IN_COMMON_CODEBANK == FALSE) && (gSBTask.ubSBDecodeFlowLoopBreakFlag)) {
			break;
		}
	} while ( (CALLBACK_FUNC_IN_COMMON_CODEBANK == FALSE)  );
#else  /* (RETRY_SOFTBITT_FOR_SDK_EN==FALSE) */
	gSBTask.ubErr_frm = gpRetry->RetryJobList[gpRetry->ubHead].ubErrorP4KBMP;
	gSBTask.ubState = RETRY_SB_STATE_DONE;

	return;
#endif  /* (RETRY__KEEP_SOFTBIT_FLOW_CODE_EN==TRUE) */
}

#else /*(TRUE == RETRY_MICRON_NICKS)*/
/*
 *  SOFTBIT RETRY DECODING MAIN FUNCTION
 *  =========================
 *
 *  NOTE :
 *
 *  If user want to stay inside SB decoding for longer period
 *
 *  It can be done by
 *  1. Setting CALLBACK_FUNC_IN_COMMON_CODEBANK = TRUE to build loop
 *  2. Setting gSBTask.ubSBDecodeFlowLoopBreakFlag = TRUE
 *     for breakpoint
 *
 *	and it can be canceled by :
 *	1. Setting CALLBACK_FUNC_IN_COMMON_CODEBANK = FALSE
 *
 *		Then Program Counter will leave this decoding flow
 *		EVERY TIME after triggering MT & changing state
 *
 */

void SBRetryMain(void)
{
#if(RETRY_SOFTBITT_FOR_SDK_EN==FALSE)
	U8 ubMTIndex;
	FlhIntInfo8Bit_t ubFlhMsg;
	MT_Callback_Func_t callback;

	do {
		//SB MICRON Check Point 0722
		M_UART(ERROR_SOFTBIT_, "\nSB STATE:%d", gSBTask.ubState);
		M_UART(ERROR_SOFTBIT_, "\n    subSTATE:%d", gSBTask.ubSubstate);
		M_UART(ERROR_SOFTBIT_, "\n        ThirdSTATE:%d", gSBTask.ubThirdstate);
		M_UART(ERROR_SOFTBIT_, "\n            FourthSTATE:%d", gSBTask.ubFourthstate);

		switch (gSBTask.ubState) {

		case RETRY_SB_STATE_INIT:
			gSBTask.ubPrevState = RETRY_SB_STATE_INIT;

			/// init variable
			RetrySBInit();

			// Break when Assert happened in RetrySBInit
			if (RETRY_SB_STATE_SET_DEFAULT_FEATURE_AND_CHK == gSBTask.ubState) {
				break;
			}

			//SB MICRON Check Point 0806
			M_UART(ERROR_SOFTBIT_, "\n SB Skip??, ALU:%x , WLType: %d, WLStBypass: %d \n", gSBTask.MTTemplate.dma.ALUSelect, gSBTask.ubWLType, gSBTask.MTTemplate.dma.ulUserDefineReadDma.btWLStatusBypass);

			//Micron SB Todo
			if ( gSBTask.MTTemplate.dma.ALUSelect & ALU_RULE_SLC_BIT) {
				//			if ( (gSBTask.MTTemplate.dma.ALUSelect & ALU_RULE_SLC_BIT) || (gpOtherInfo->pRuleSet->pLMU->ubBit_No == 0) || (gpuwVBRMP[uwErrorPageUnitIndex].B.btIsSLCMode)) {
				M_UART(ERROR_SOFTBIT_, "\n SLC, skip Soft-bit decode");
				gSBTask.ubState =  RETRY_SB_STATE_SB_RESET_FLH;
				gSBTask.ubBackUpedState = RETRY_SB_STATE_DONE;
				gpRetry->RetryJobList[gpRetry->ubHead].ubGenFailInRR = FALSE;
				break;
			}
			else if ((SB_WORD_LINE_SLC == gSBTask.ubWLType) || (SB_WORD_LINE_MLC == gSBTask.ubWLType) //SLC, MLC, Open Page skip SB flow
				|| ((TRUE == gSBTask.MTTemplate.dma.ulUserDefineReadDma.btWLStatusBypass) && (FALSE == gpVT->FTL.btSPORDoing))) {
				M_UART(ERROR_SOFTBIT_, "\n skip SLC_MLC, Open page \n");
				gSBTask.ubState =  RETRY_SB_STATE_SB_RESET_FLH;
				gSBTask.ubBackUpedState = RETRY_SB_STATE_DONE;
				gpRetry->RetryJobList[gpRetry->ubHead].ubGenFailInRR = FALSE;
			}
			else {
				gSBTask.ubState = RETRY_SB_STATE_DET_COARSE_TUNE_LEVEL;
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_DET_COARSE_LVL__INIT;
				if (NCS_EN) {
					if (TRUE == gubNCSTLCOpenFlow) {
						gSBTask.ubState =  RETRY_SB_STATE_SB_RESET_FLH;
						gSBTask.ubBackUpedState = RETRY_SB_STATE_DONE;
						gpRetry->RetryJobList[gpRetry->ubHead].ubGenFailInRR = FALSE;
					}
				}
			}
			break;
		case RETRY_SB_STATE_DET_COARSE_TUNE_LEVEL :
			gSBTask.ubPrevState =  RETRY_SB_STATE_DET_COARSE_TUNE_LEVEL;
			/// for each 4K
			RetrySBDetermineCoarseTuningLevel();
			break;
		case RETRY_SB_STATE_SCAN_ERR_FRAME:
			gSBTask.ubPrevState = RETRY_SB_STATE_SCAN_ERR_FRAME;
			/// scan fpl_err_hdl->hb_retry->page.job->err_frm (gSBTask.ubErr_frm) to check which err_frm needs to be retryed
			gSBTask.ubCurrentLDPCFrameIdx = 0;
			for ( ; gSBTask.ubCurr_frm_idx < gub4kEntrysPerPlane; gSBTask.ubCurr_frm_idx++) {
				if ( (BIT0 << gSBTask.ubCurr_frm_idx) & gSBTask.ubErr_frm ) {

					/// refresh addr
					gSBTask.ulbackup_addr = gSBTask.ulbackup_addr_base[gSBTask.ubCurr_frm_idx];///(0x1000 * gSBTask.ubCurr_frm_idx);
					gSBTask.ulSpareAddr = gSBTask.ulSpareAddr_base [gSBTask.ubCurr_frm_idx];//(gSBTask.ubCurr_frm_idx * 16);

					/// change state
					gSBTask.ubState = RETRY_SB_STATE_CHECK_ERR_LDPC_FRM;
					break;
				}
			}

			if ( gSBTask.ubCurr_frm_idx == gub4kEntrysPerPlane ) {	//All error frame been processed, leave SB flow
				RetrySBSwitchStateToSetDefaultFeatureReset();
			}
			break;
		case RETRY_SB_STATE_CHECK_ERR_LDPC_FRM:
			gSBTask.ubPrevState =  RETRY_SB_STATE_CHECK_ERR_LDPC_FRM;
			/// scan gpRetry->RetryJobList[gpRetry->ubHead].uwLDPCCurrentErrMap and give value to gSBTask->curr_ldpc_frm_idx
			/// *******************need to manually do  gSBTask->curr_ldpc_frm_idx++ after each SB retry done****************
			if ( gSBTask.ubCurrentLDPCFrameIdx == LDPC_FRAME_MAX_CNT) {
				/// this physical frame is done, change state
				if ( (gSBTask.uwLDPCCurrentErrMap & (     (BIT0 | BIT1) << ((gSBTask.ubCurr_frm_idx) << 1) )  ) == 0) {
					gSBTask.ubErr_frm &= ~(BIT0 << (gSBTask.ubCurr_frm_idx));
				}
				gSBTask.ubCurrentLDPCFrameIdx = 0;
				gSBTask.ubState = RETRY_SB_STATE_SCAN_ERR_FRAME;
				break;
			}

			if ( ( (gSBTask.uwLDPCCurrentErrMap) & ( BIT((gSBTask.ubCurr_frm_idx << 1) + gSBTask.ubCurrentLDPCFrameIdx) )  ) == 0) {
				if (gSBTask.ubIsAfterRaidECC > RETRY_SB_TASK_IS_RETRY_TASK_CASE) {
					/// do nothing
				}
				else {
					if (RETRY_READ_SB__DIRECTLY_GET_CORRECT_DATA_FROM_ERROR_PAGE) {
						/// read good 2K to buffer
						gSBTask.ubState = RETRY_SB_STATE_READ_GOOD_2K_TO_BUF;
						gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_HB_READ_INIT;
					}
				}
			}
			else {
				if (gSBTask.ubIsAfterRaidECC == RETRY_SB_TASK_IS_ERR_PAGE_AFTER_RAIDECC_CASE) {
					gSBTask.ubState = RETRY_SB_STATE_SB_WITH_OPT_READ_LEVEL__AFTER_RAIDECC;
					gSBTask.ubSubstate = RETRY_SB_SUBSTATE_WITH_OPT_READ_LEVEL__AFTER_RAIDECC__INIT;
				}
				else {
					gSBTask.ubState = RETRY_SB_STATE_MODIFY_RR_ADD_COARSE_TUNING_LEVEL;
				}
			}
			break;
		case  RETRY_SB_STATE_MODIFY_RR_ADD_COARSE_TUNING_LEVEL:
			gSBTask.ubPrevState =  RETRY_SB_STATE_MODIFY_RR_ADD_COARSE_TUNING_LEVEL;
			/// for each 2K
			RetrySBModifyReadRetryAddCoarseTuningLevel();  // add coarse tuning level

			//Enable DSP_EN for later ADT LLR
			M_FIP_SET_LDPC_DSP_EN();

			gSBTask.ubState =  RETRY_SB_STATE_SB_RESET_FLH;
			if (SOFTBIT_RETRY_CASE_C != gSBTask.ubCaseABCnt) {	// Case A/B
				gSBTask.ubBackUpedState = RETRY_SB_STATE_HB_WITH_AUTO_READ_CALIBRATION;
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_INIT;
			}
			else {	// Case C
				gSBTask.ubBackUpedState = RETRY_SB_STATE_HB_WITH_AUTO_READ_CALIBRATION;
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_WITH_SPECIFIC_RETRY_TABLE_READ_OFFSET_INIT;
			}

			break;

		case RETRY_SB_STATE_HB_WITH_AUTO_READ_CALIBRATION:
			gSBTask.ubPrevState = RETRY_SB_STATE_HB_WITH_AUTO_READ_CALIBRATION;
			if (SOFTBIT_RETRY_CASE_C != gSBTask.ubCaseABCnt) {	// Case A/B
				RetrySBHBReadwithARC();
			}
			else {	//Specific retry table/read offset, Case C
				RetrySBHBReadwithRetryTableOrReadOffset();
			}
			break;

		case RETRY_SB_STATE_SB_READ_WITH_OPTIMAL_READ_LEVEL:
			gSBTask.ubPrevState = RETRY_SB_STATE_SB_READ_WITH_OPTIMAL_READ_LEVEL;
			RetrySBReadFlow();
			gSBTask.ubllr_index = SB_MICRON_DEFAULT_LLR_TABLE_START_IDX;
			break;

		case RETRY_SB_STATE_LOAD_DEFAULT_LLR_TABLE:
			gSBTask.ubPrevState =  RETRY_SB_STATE_LOAD_DEFAULT_LLR_TABLE;
			if (gSBTask.ubllr_index == gSBTask.ubllr_index_MAX ) {
				gSBTask.ubllr_index = 0;
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_DECODE_WITH_ADT_LLR_INIT;
				gSBTask.ubState = RETRY_SB_STATE_SB_DECODE_WITH_ADT_LLR;
				//Load ADT LLR from Decode result
				RetrySBFlashLoadECCParam(LLR_TABLE0_LOAD_BIT);
				break;
			}
			RetrySBFlashRetoreLLR(gSBTask.ubPageType, gSBTask.ubllr_index); // set default llr table
			gSBTask.ubState = RETRY_SB_STATE_SB_DECODE_WITH_DEFAULT_LLR;
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_DECODE_WITH_DEFAULT_LLR_INIT;
			break;

		case RETRY_SB_STATE_SB_DECODE_WITH_DEFAULT_LLR:
			gSBTask.ubPrevState = RETRY_SB_STATE_SB_DECODE_WITH_DEFAULT_LLR;
			RetrySBDecodeFlow();
			break;

		case RETRY_SB_STATE_SB_DECODE_WITH_ADT_LLR:
			gSBTask.ubPrevState = RETRY_SB_STATE_SB_DECODE_WITH_ADT_LLR;
			RetrySBDecodeFlow();
			break;

		case RETRY_SB_STATE_SB_DSP2:
			gSBTask.ubPrevState =  RETRY_SB_STATE_SB_DSP2;
			RetrySBDSP2();
			break;

		case RETRY_SB_STATE_TERMINATE_PASS:
			//SBRAID Debug 1115
			/*
			if (DEBUG_SOFTBITRAIDECC_UART) {
				UartPrintf("\n [SB]  PCA:%x \n", (U32)( (gSBTask.ulFSA_Align) + (gSBTask.ubCurr_frm_idx) ));
			}
			*/
			gSBTask.uwLDPCCurrentErrMap &= ~(     (BIT(gSBTask.ubCurrentLDPCFrameIdx)) << ((gSBTask.ubCurr_frm_idx) << 1)       );
			gSBTask.ubCaseABCnt = 0;
		/* no break */
		case RETRY_SB_STATE_CHECK_NEXT_STATE:
			if (NCS_EN) {
				gSBTask.uwLDPCCurrentErrMap = 0;
				gSBTask.ubErr_frm = 0;
				gSBTask.ubCurr_frm_idx = 0;
				RetrySBSwitchStateToSetDefaultFeatureReset();
				break;
			}
			/// if collected 4K, use FPU COR to send it out.
			gSBTask.ubCurrentLDPCFrameIdx++;
			gSBTask.ubState = RETRY_SB_STATE_CHECK_ERR_LDPC_FRM;

			if (gSBTask.ubCurrentLDPCFrameIdx == LDPC_FRAME_MAX_CNT) {
				if ( (gSBTask.uwLDPCCurrentErrMap & (     (BIT0 | BIT1) << ((gSBTask.ubCurr_frm_idx) << 1) )  ) == 0) {
					gSBTask.ubErr_frm &= ~(BIT0 << (gSBTask.ubCurr_frm_idx));
				}
				gSBTask.ubCurrentLDPCFrameIdx = 0;
				gSBTask.ubCurr_frm_idx++;
				gSBTask.ubState = RETRY_SB_STATE_SCAN_ERR_FRAME;
			}

			break;

		case RETRY_SB_STATE_TERMINATE_FAIL:
			//Micron SB Todo
			//Reset ARC/SBSBR/DECODE_MODE

			//SB MICRON Check Point 0802
			M_UART(ERROR_SOFTBIT_, "\n CaseABC:%d \n", gSBTask.ubCaseABCnt);

			if (SOFTBIT_RETRY_CASE_A == gSBTask.ubCaseABCnt) {
				gSBTask.ubCaseABCnt = SOFTBIT_RETRY_CASE_B;
				RetrySBSwitchToSetDefaultFeatureThenDoBackupSBFlow(RETRY_SB_STATE_MODIFY_RR_ADD_COARSE_TUNING_LEVEL, RETRY_SB_SUBSTATE_NULL);
			}
			else if (SOFTBIT_RETRY_CASE_B == gSBTask.ubCaseABCnt) {
				gSBTask.ubCaseABCnt = SOFTBIT_RETRY_CASE_C;
				gSBTask.ubSpecificMode = SB_MICRON_SPECIFIC_RETRY_TABLE;
				RetrySBSwitchToSetDefaultFeatureThenDoBackupSBFlow(RETRY_SB_STATE_MODIFY_RR_ADD_COARSE_TUNING_LEVEL, RETRY_SB_SUBSTATE_NULL);
			}
			else {
				gSBTask.ubCaseABCnt = SOFTBIT_RETRY_CASE_A;
				RetrySBSwitchToSetDefaultFeatureThenDoBackupSBFlow(RETRY_SB_STATE_CHECK_NEXT_STATE, RETRY_SB_SUBSTATE_NULL);
			}
			break;

		case RETRY_SB_STATE_RECEIVE_CQ:
			if (FALSE == FIPIsStopAllocateEventSet(FIP_STOP_ALLOCATE_EVENT_SB_WAIT_MT_DONE)) {
				FIPSetStopAllocateBuf(FIP_STOP_ALLOCATE_EVENT_SB_WAIT_MT_DONE);
			}
			FIPDelegateCmd();
			while (gpRetry->TrigMTList.ubTrigCnt) {
				ubMTIndex = gpRetry->TrigMTList.MTCb[gpRetry->TrigMTList.ubHead].ubMTIndex;

				ubFlhMsg = gMTMgr.ubMTDoneMsg[ubMTIndex - MT_RETRY_START_INDEX];

				if (ubFlhMsg.ubAll == 0) {
					break; // MT not finish, break
				}

				gpRetry->RttTimer.uoStartRTT = 0;
				gpRetry->RttTimer.uoEndRTT   = 0;

				if ((ubFlhMsg.btMTStop) || (ubFlhMsg.btErasePage)) {
					//SB MICRON Check Point 0724
					M_UART(ERROR_SOFTBIT_, "\n FailCallBack!!");
					callback = (MT_Callback_Func_t)gpRetry->TrigMTList.MTCb[gpRetry->TrigMTList.ubHead].FailCallback;
				}
				else {
					callback = (MT_Callback_Func_t)gpRetry->TrigMTList.MTCb[gpRetry->TrigMTList.ubHead].PassCallback;
				}
				if (callback != NULL) {
					callback(ubMTIndex);
				}

				RetrySBReleaseFinishMT();
				if (0 == gpRetry->TrigMTList.ubTrigCnt) {
					FIPClearStopAllocateBuf(FIP_STOP_ALLOCATE_EVENT_SB_WAIT_MT_DONE);
				}
			}
			break;
		case RETRY_SB_STATE_SET_DEFAULT_FEATURE_AND_CHK:
			gSBTask.ubPrevState =  RETRY_SB_STATE_SET_DEFAULT_FEATURE_AND_CHK;
			RetrySBSetDefaultFeatureAndCheck();
			break;
		case RETRY_SB_STATE_SB_RESET_FLH:
			gSBTask.ubPrevState =  RETRY_SB_STATE_SB_RESET_FLH;
			RetrySBResetFlash();
			break;
		case RETRY_SB_STATE_DONE:
			gSBTask.ubSBDecodeFlowLoopBreakFlag = 1;
			// For DriveLog Debug Info, Record delta Table Offset
			gSBTask.ubDeltaTablePassReadLevelOffset = delta_table[3][3];
			break;
		}

		if ((CALLBACK_FUNC_IN_COMMON_CODEBANK == FALSE) && (gSBTask.ubSBDecodeFlowLoopBreakFlag)) {
			break;
		}
	} while ( (CALLBACK_FUNC_IN_COMMON_CODEBANK == FALSE)  );
#else  /* (RETRY_SOFTBITT_FOR_SDK_EN==FALSE) */
	gSBTask.ubErr_frm = gpRetry->RetryJobList[gpRetry->ubHead].ubErrorP4KBMP;
	gSBTask.ubState = RETRY_SB_STATE_DONE;

	return;
#endif  /* (RETRY__KEEP_SOFTBIT_FLOW_CODE_EN==TRUE) */
}
#endif /*(TRUE == RETRY_MICRON_NICKS)*/

#endif /*!BURNER_MODE_EN*/
#endif /*((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_MLC))*/


