/****************************************************************************
*
*  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
*  All rights reserved
*
*  The content of this document is confidential and shall be applied
*  subject to the terms and conditions of the license agreement and
*  other applicable laws. Any unauthorized access, use or disclosure
*  of this document is strictly prohibited and may be punishable
*  under laws.
*
*   Read_disturb_api.h
*
*
*
****************************************************************************/
#ifndef _READ_DISTURB_API_H_
#define _READ_DISTURB_API_H_
/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */
#define	READ_DISTURB_THRESHOLD_SHIFT		(12)
#define READ_DISTURB_EN_INFO_BLOCK_BIT (BIT0)
#define	READ_DISTURB_MAX_ERASE_CNT_LEVEL_NUM		(10)
#define READ_DISTURB_DEFAULT_SLC_READ_VERIFY_THRESHOLD_RAGNE_0      (1000)
#define READ_DISTURB_DEFAULT_TLC_READ_VERIFY_THRESHOLD_RAGNE_0      (1000)
#define READ_DISTURB_DEFAULT_SLC_FORCE_SWAP_THRESHOLD_RAGNE_0      (4000)
#define READ_DISTURB_DEFAULT_TLC_FORCE_SWAP_THRESHOLD_RAGNE_0      (2000)
#define READ_DISTURB_DEFAULT_SLC_READ_VERIFY_THRESHOLD_RAGNE_1      (600)
#define READ_DISTURB_DEFAULT_ERASE_CNT_RANGE            (1000)
#define READ_DISTURB_DEFAULT_READ_VERIFY_THRESHOLD_SUBTRACT      (50)
#define	READ_DISTURB_DEFAULT_ERASE_CNT_LEVEL_NUM		(4)
#define READ_DISTURB_DEFAULT_INVALID_ERASE_CNT      (0xFFFF)

#define READ_DISTURB_EXECUTION_TIME_0				(50000)//ms
#define READ_DISTURB_EXECUTION_TIME_1				(60000)//ms
#define READ_DISTURB_XFER_DATA_OUT_SQ_THRESHOLD		(250)
#define READ_DISTURB_XFER_DATA_OUT_SQ_CRITICAL_THRESHOLD	(1)
#if (READ_DISTURB_PRDH_EN)
// To Be discuss , Use Frey2 window size
// SLC/QLC at least 550MB/90MB trigger a scan event (Min EraseCnt
// SLC/QLC at least 103MB/21MB trigger a scan event (Max EraseCnt
// Temp use SLC 550 , 2500 MB/s -> 0.2s
#define READ_DISTURB_CHECK_TIME     (100)
#else /* (READ_DISTURB_PRDH_EN) */
#define READ_DISTURB_CHECK_TIME     (1000)
#endif /* (READ_DISTURB_PRDH_EN) */

/*
 * ---------------------------------------------------------------------------------------------------
 *  enum
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *  data types
 * ---------------------------------------------------------------------------------------------------
 */
typedef struct {
	U16 uwTLCReadVerifyReadCntThreshold[READ_DISTURB_MAX_ERASE_CNT_LEVEL_NUM];
	U16 uwSLCReadVerifyReadCntThreshold[READ_DISTURB_MAX_ERASE_CNT_LEVEL_NUM];
	U16 uwTLCForceCopyReadCntThreshold[READ_DISTURB_MAX_ERASE_CNT_LEVEL_NUM];
	U16 uwSLCForceCopyReadCntThreshold[READ_DISTURB_MAX_ERASE_CNT_LEVEL_NUM];
	U16 uwTLCEraseCntRange[READ_DISTURB_MAX_ERASE_CNT_LEVEL_NUM];
	U16 uwSLCEraseCntRange[READ_DISTURB_MAX_ERASE_CNT_LEVEL_NUM];
} ReadDisturbThreshold_t;

typedef struct {
	U64 uoStartTime;
	ReadDisturbThreshold_t Threshold;
	U32 ulSentinelPlaneIdx;
	U16 uwMaxReadCntUnit;
	U16 uwReadDisturbReadCntSeqReadWeightingFactor;
	U8 ubState;
	U8 btDoing 	: 1;
	U8 btKeepSearchUnit : 1;
	U8  		: 6;
} ReadDisturb_t;

typedef struct {
	U16 uwXferDataOutSendSQNum;
	U16 uwXferDataOutSQUpperBound;
	U16 uwReadDisturbVerifySQNum;
	U16 uwReserve;
} ReadDisturbThrottle_t;

/*
 * ---------------------------------------------------------------------------------------------------
 *   macros
 * ---------------------------------------------------------------------------------------------------
 */
#define M_READ_DISTURB_CALCULATE_THRESHOLD(x)	((((U32)x)*1000) >> READ_DISTURB_THRESHOLD_SHIFT)
#define M_READ_DISTURB_DEFAULT_FORCE_COPY_THRESHOLD(x)      (x*2)

/*
 * ---------------------------------------------------------------------------------------------------
 *   extern global variables
 * ---------------------------------------------------------------------------------------------------
 */
extern ReadDisturb_t gReadDisturb;
extern ReadDisturbThrottle_t gReadDisturbThrottle;

/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */
U8 ReadDisturbNeedThrottleXferDataOut(void);
#if (READ_DISTURB_PRDH_EN)
FW_BTCM0_SECTION void ReadDisturb(void);
#else /* (READ_DISTURB_PRDH_EN) */
AOM_FTL_EXT void ReadDisturb(void);
#endif /* (READ_DISTURB_PRDH_EN) */

#endif //_READ_DISTURB_API_H_
