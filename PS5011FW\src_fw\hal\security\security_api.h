
/****************************************************************************
*
*  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
*  All rights reserved
*
*  The content of this document is confidential and shall be applied
*  subject to the terms and conditions of the license agreement and
*  other applicable laws. Any unauthorized access, use or disclosure
*  of this document is strictly prohibited and may be punishable
*  under laws.
*
*  security_api.h
*
*
*
****************************************************************************/

#ifndef _SECURITY_API_H_
#define _SECURITY_API_H_

/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "typedef.h"
#include "aom/aom_api.h"
#include "hal/security/security_reg.h"

/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */
#if (!PS5017_EN) //To be modify for E21
#define SECURITY_AES_RANGE_ADDR (SECURITY_REG_BASE + SECURITY_AES_RANGE_OFFSET)
#define SECURITY_AES_RANGE_SIZE (0x200)
#define SECURITY_AES_RANGE_NUM  (24)
#endif /* (!PS5017_EN) */
#define	SECURITY_AES_KEY_LENGTH	(64)

#define SECURITY_SHA_AXI_PAC32_EN (ENABLE)
#define SECURITY_SHA_AXI_PAC32_DIS (DISABLE)

#define SECURITY_SHA_FIRST_ROUND_BIT (BIT0)
#define SECURITY_SHA_FINAL_ROUND_BIT (BIT1)
#define SECURITY_SHA_MIDDLE_ROUND	(0)

#define SECURITY_SHA_160_OUTPUT_LENGTH (0x14)
#define SECURITY_SHA_256_OUTPUT_LENGTH (0x20)
#define SECURITY_SHA_512_OUTPUT_LENGTH (0x40)

#define SECURITY_MODE_SELECT_SHA_160	(SECURITY_SHA_160_OUTPUT_LENGTH)
#define SECURITY_MODE_SELECT_SHA_256	(SECURITY_SHA_256_OUTPUT_LENGTH)
#define SECURITY_MODE_SELECT_SHA_512	(SECURITY_SHA_512_OUTPUT_LENGTH)
#define SECURITY_MODE_SELECT_SM3		(0x60)

#define SECURITY_AES_RANGE_ALL_RANGE_WRITE_UNLOCK	((U64)0)
#define SECURITY_AES_RANGE_ALL_RANGE_READ_UNLOCK	((U64)0)
#define SECURITY_DEFAULT_NSID                       (0)
#define SECURITY_MBR_END_LCA_DEFAULT_VALUE          ((U32)0)
#define SECURITY_MBR_END_LBA_DEFAULT_VALUE          ((U64)0)
#define SECURITY_AES_RANGE_DEFAULT_KEY_IDX    (0)

#if (PS5013_EN)
#define SECURITY_AES_RANGE_LBA_DEFAULT_VALUE  ((U64)0x7FFFFFFFF)
#define SECURITY_AES_RANGE_INVALID_KEY_IDX    (0x00)
#else /* (PS5013_EN) */
#define SECURITY_AES_RANGE_LBA_DEFAULT_VALUE  ((U64)0)
#define SECURITY_AES_RANGE_INVALID_KEY_IDX    (0x7F)
#endif /* (PS5013_EN) */

#define SECURITY_GLOBAL_RANGE_KEY_IDX         (0)
#define SECURITY_OFFLINE_KEY_INDEX (16)
#define SECURITY_DEVICE_TO_HOST_KEY_IDX (17)
#define SECURITY_GENERATE_DECRYPTION_KEY (TRUE)

#define SECURITY_WAIT_D2H_IDLE_TIMEOUT_TIME   (1 * MILLISECOND_PER_SECOND) // 1 second
#define SECURITY_DRIVELOG_WAIT_D2H_IDLE_START (0)
#define SECURITY_DRIVELOG_WAIT_D2H_IDLE_END   (1)
#define SECURITY_DRIVELOG_SET_KEY             (2)

#if (PS5017_EN)
// Fixed key for TCG_OFF & AES_ON
#define SECURITY_DEFAULT_DATA_KEY {0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A,\
	                               0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A,\
                                   0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A,\
                                   0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A,\
                                   0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A,\
                                   0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A,\
                                   0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A,\
                                   0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A}
#endif /*(PS5017_EN)*/
/*
 * ---------------------------------------------------------------------------------------------------
 *  data types
 * ---------------------------------------------------------------------------------------------------
 */
typedef struct {
	U8 ubMode;			// 1	AES chaining Mode.
	U8 ubAESKeyMode;		// 1	AES Key length bits
	U8 ubKeyIdx;		// 1	AES Key table index
	U8 EncryptOrDecrypt  : 4;	//  	Encypt or Decrypt
	U8 SetKeyEn : 4;	//  	Enable set key
	U32 *pulKey;		// 4	AES Key
	U32 *pulInitVector;	// 4	Initial vector
	U32 ulSrcAddr;		// 4	Source address
	U32 ulTargetAddr;	// 4 	Target address
	U32 ulDataSize;		// 4 	Data size
	U64 uoLBA;			// 8	LBA value
} SecurityAESParameter_t;

/*
 * ---------------------------------------------------------------------------------------------------
 *   macros
 * ---------------------------------------------------------------------------------------------------
 */
#define M_SECURITY_AES_EN()							((TCG_EN && M_TCG_AES_EN()) || TCG_OFF_AES_EN)
#define M_SECURITY_INVERSE_LONG(X)					((((U8)(X))<<24) | (((U8)((X)>>8))<<16) | (((U8)((X)>>16))<<8) | ((U8)((X)>>24)))
#define M_SECURITY_SET_ON_THE_FLY_KEY_IDX(Idx)		do { \
											R32_SECURITY_AES_OTF[R32_SECURITY_AES_OTF_OPERATION] &=(~ (AES_OTF_KEY_IDX_MASK << AES_OTF_KEY_IDX_SHIFT)); \
											R32_SECURITY_AES_OTF[R32_SECURITY_AES_OTF_OPERATION] |= (((Idx) & AES_OTF_KEY_IDX_MASK) << AES_OTF_KEY_IDX_SHIFT);\
										} while (0)
#define M_SECURITY_SET_AES_ON_THE_FLY_KEY_TO_REG()	do { \
												R32_SECURITY_AES_OTF[R32_SECURITY_AES_OTF_OPERATION] &=(~ (AES_OTF_KEY_CHG_DIR_MASK << AES_OTF_KEY_CHG_DIR_SHIFT)); \
												R32_SECURITY_AES_OTF[R32_SECURITY_AES_OTF_OPERATION] |= (SECURITY_AES_ON_THE_FLY_KEY_CHANGE_DIRECTION_TABLE_TO_REGISTER & AES_OTF_KEY_CHG_DIR_MASK) << AES_OTF_KEY_CHG_DIR_SHIFT;\
											} while (0)
#define M_SECURITY_WAIT_AES_ON_THE_FLY_KEY_CHANGE_FINISH()	while(R8_SECURITY_AES_OTF[R8_SECURITY_AES_OTF_KEY_CHG] & (AES_OTF_KEY_CHG_MASK<<AES_OTF_KEY_CHG_SHIFT))

#if (PS5013_EN)
//nsid 0~7
#define M_SECURITY_SET_AES_RANGE_GLOBAL_KEY_IDX(NSID, KeyIdx) 	do{\
									R8_SECURITY_AES_RGN[R8_SECURITY_AES_RGN_GLB_KIDX + (NSID)*R8_SECURITY_AES_RGN_GLB_KIDX_OFFSET] &=(~(AES_RGN_GLB_KIDX_MASK<<AES_RGN_GLB_KIDX_SHIFT));\
									R8_SECURITY_AES_RGN[R8_SECURITY_AES_RGN_GLB_KIDX + (NSID)*R8_SECURITY_AES_RGN_GLB_KIDX_OFFSET] |= (((KeyIdx)&AES_RGN_GLB_KIDX_MASK)<<AES_RGN_GLB_KIDX_SHIFT);\
											}while(0)
#define M_SECURITY_GET_AES_RANGE_GLOBAL_KEY_IDX(NSID) 		((R8_SECURITY_AES_RGN[R8_SECURITY_AES_RGN_GLB_KIDX + (NSID)*R8_SECURITY_AES_RGN_GLB_KIDX_OFFSET]>>AES_RGN_GLB_KIDX_SHIFT) & AES_RGN_GLB_KIDX_MASK)
#define M_SECURITY_SET_AES_RANGE_START_LCA(RangeIdx, LCA)	(R32_SECURITY_AES_RGN[R32_SECURITY_AES_RGN_SLCA + (RangeIdx)*R32_SECURITY_AES_RGN_SLCA_OFFSET] = (U32)(LCA))
#define M_SECURITY_GET_AES_RANGE_START_LCA(RangeIdx)		(R32_SECURITY_AES_RGN[R32_SECURITY_AES_RGN_SLCA + (RangeIdx)*R32_SECURITY_AES_RGN_SLCA_OFFSET])
#define M_SECURITY_SET_AES_RANGE_KEY_IDX(RangeIdx, KeyIdx)			do{\
									R32_SECURITY_AES_RGN[R32_SECURITY_AES_RGN_KIDX + (RangeIdx)*R32_SECURITY_AES_RGN_KIDX_OFFSET] &=(~(AES_RGN_KIDX_MASK<<AES_RGN_KIDX_SHIFT));\
									R32_SECURITY_AES_RGN[R32_SECURITY_AES_RGN_KIDX + (RangeIdx)*R32_SECURITY_AES_RGN_KIDX_OFFSET] |= (((KeyIdx)&AES_RGN_KIDX_MASK)<<AES_RGN_KIDX_SHIFT);\
											}while(0)
#define M_SECURITY_GET_AES_RANGE_KEY_IDX(RangeIdx)		((R32_SECURITY_AES_RGN[R32_SECURITY_AES_RGN_KIDX + (RangeIdx)*R32_SECURITY_AES_RGN_KIDX_OFFSET]>>AES_RGN_KIDX_SHIFT)&AES_RGN_KIDX_MASK)
#define M_SECURITY_SET_AES_RANGE_END_LCA(RangeIdx, LCA)	(R32_SECURITY_AES_RGN[R32_SECURITY_AES_RGN_ELCA + (RangeIdx)*R32_SECURITY_AES_RGN_ELCA_OFFSET] = (U32)(LCA))
#define M_SECURITY_GET_AES_RANGE_END_LCA(RangeIdx)		(R32_SECURITY_AES_RGN[R32_SECURITY_AES_RGN_ELCA + (RangeIdx)*R32_SECURITY_AES_RGN_ELCA_OFFSET])
#define M_SECURITY_SET_AES_RANGE_MBR_END_LCA(NSID, LCA)			do{\
									R32_SECURITY_AES_RGN[R32_SECURITY_AES_RGN_MBR_ELCA + (NSID)*R32_SECURITY_AES_RGN_MBR_ELCA_OFFSET] &=(~(AES_RGN_MBR_ELCA_MASK<<AES_RGN_MBR_ELCA_SHIFT));\
									R32_SECURITY_AES_RGN[R32_SECURITY_AES_RGN_MBR_ELCA + (NSID)*R32_SECURITY_AES_RGN_MBR_ELCA_OFFSET] |= (((LCA)&AES_RGN_MBR_ELCA_MASK)<<AES_RGN_MBR_ELCA_SHIFT);\
											}while(0)
#define M_SECURITY_GET_AES_RANGE_MBR_END_LCA(NSID)		((R32_SECURITY_AES_RGN[R32_SECURITY_AES_RGN_MBR_ELCA + (NSID)*R32_SECURITY_AES_RGN_MBR_ELCA_OFFSET]>>AES_RGN_MBR_ELCA_SHIFT)&AES_RGN_MBR_ELCA_MASK)
#define M_SECURITY_GET_WRITE_LOCK()						(R32_SECURITY_AES_RGN[R32_SECURITY_AES_RGN_WLOCK])
#define M_SECURITY_GET_READ_LOCK()						(R32_SECURITY_AES_RGN[R32_SECURITY_AES_RGN_RLOCK])
#define M_SECURITY_GET_GLOBAL_RANGE_WRITE_LOCK(NSID)	((R32_SECURITY_AES_RGN[R32_SECURITY_AES_RGN_WLOCK] >> (SECURITY_GLOBAL_RANGE_WRITE_LOCK_OFFSET + (NSID))) & BIT_MASK(1))
#define M_SECURITY_GET_GLOBAL_RANGE_READ_LOCK(NSID)		((R32_SECURITY_AES_RGN[R32_SECURITY_AES_RGN_RLOCK] >> (SECURITY_GLOBAL_RANGE_READ_LOCK_OFFSET + (NSID))) & BIT_MASK(1))

#define M_SECURITY_SET_GLOBAL_RANGE_WRITE_LOCK(NSID, X)			do{\
									(R32_SECURITY_AES_RGN[R32_SECURITY_AES_RGN_WLOCK] &= (~(BIT_MASK(1) << (SECURITY_GLOBAL_RANGE_WRITE_LOCK_OFFSET + (NSID)))));\
									(R32_SECURITY_AES_RGN[R32_SECURITY_AES_RGN_WLOCK] |= (((X) & BIT_MASK(1)) << (SECURITY_GLOBAL_RANGE_WRITE_LOCK_OFFSET + (NSID))));\
											}while(0)

#define M_SECURITY_SET_GLOBAL_RANGE_READ_LOCK(NSID, X)			do{\
									(R32_SECURITY_AES_RGN[R32_SECURITY_AES_RGN_RLOCK] &= (~(BIT_MASK(1) << (SECURITY_GLOBAL_RANGE_READ_LOCK_OFFSET + (NSID)))));\
									(R32_SECURITY_AES_RGN[R32_SECURITY_AES_RGN_RLOCK] |= (((X) & BIT_MASK(1)) << (SECURITY_GLOBAL_RANGE_READ_LOCK_OFFSET + (NSID))));\
											}while(0)
#define M_SECURITY_SET_AES_RANGE_WRITE_LOCK(Idx, X)			do{\
									(R32_SECURITY_AES_RGN[R32_SECURITY_AES_RGN_WLOCK] &= (~(BIT_MASK(1) << (Idx))));\
									(R32_SECURITY_AES_RGN[R32_SECURITY_AES_RGN_WLOCK] |= (((X) & BIT_MASK(1)) << (Idx)));\
											}while(0)

#define M_SECURITY_SET_AES_RANGE_READ_LOCK(Idx, X)			do{\
									(R32_SECURITY_AES_RGN[R32_SECURITY_AES_RGN_RLOCK] &= (~(BIT_MASK(1) << (Idx))));\
									(R32_SECURITY_AES_RGN[R32_SECURITY_AES_RGN_RLOCK] |= (((X) & BIT_MASK(1)) << (Idx)));\
											}while(0)

#define M_SECURITY_GET_RANGE_WRITE_LOCK(RangeIdx)		((R32_SECURITY_AES_RGN[R32_SECURITY_AES_RGN_WLOCK] >> (RangeIdx)) & BIT_MASK(1))
#define M_SECURITY_GET_RANGE_READ_LOCK(RangeIdx)		((R32_SECURITY_AES_RGN[R32_SECURITY_AES_RGN_RLOCK] >> (RangeIdx)) & BIT_MASK(1))
#define M_SECURITY_SET_AES_RANGE_MBR_EN(NSID, Value)			do{\
									R8_SECURITY_AES_RGN[R8_SECURITY_AES_RGN_MBR_EN] &= (~(BIT_MASK(1) <<(NSID)));\
									R8_SECURITY_AES_RGN[R8_SECURITY_AES_RGN_MBR_EN] |= (((Value) & BIT_MASK(1)) <<(NSID));\
														}while(0)
#define M_SECURITY_GET_AES_RANGE_MBR_EN(NSID)			((R8_SECURITY_AES_RGN[R8_SECURITY_AES_RGN_MBR_EN]>>(NSID))& BIT_MASK(1))

#define M_SECURITY_SET_ALL_RANGE_READ_UNLOCK()	(R32_SECURITY_AES_RGN[R32_SECURITY_AES_RGN_RLOCK] = 0)
#define M_SECURITY_SET_ALL_RANGE_WRITE_UNLOCK()	(R32_SECURITY_AES_RGN[R32_SECURITY_AES_RGN_WLOCK] = 0)
#endif /*(PS5013_EN)*/
#define M_SECURITY_SET_AES_BYPASS() do{\
										R32_SECURITY_AES_OTF[R32_SECURITY_AES_OTF_OPERATION] |= ((SECURITY_AES_ON_THE_FLY_BYPASS &AES_OTF_BYPASS_MASK) << AES_OTF_BYPASS_SHIFT);\
										} while (0)
#define M_SECUIRTY_GET_AES_BYPASS()	(((R32_SECURITY_AES_OTF[R32_SECURITY_AES_OTF_OPERATION])>>AES_OTF_BYPASS_SHIFT)&AES_OTF_BYPASS_MASK)
#define M_SECURITY_CLEAR_AES_BYPASS() do{\
										R32_SECURITY_AES_OTF[R32_SECURITY_AES_OTF_OPERATION] &= ((~AES_OTF_BYPASS_MASK) << AES_OTF_BYPASS_SHIFT);\
										} while (0)
#define M_SECURITY_RESET_AES_OFFLINE_FUNCTION() do{\
                                        R32_SECURITY_AES_OFL[R32_SECURITY_AES_OFL_FUNC] = SECURITY_AES_OFFLINE_FUNCTION_DEFAULT_VALUE;\
                                        } while (0)
#define M_SECURITY_RESET_AES_ON_THE_FLY_OPERATION() do{\
                                        R32_SECURITY_AES_OTF[R32_SECURITY_AES_OTF_OPERATION] = SECURITY_AES_ON_THE_FLY_OPERATION_DEFAULT_VALUE;\
                                        } while (0)
#define M_SECURITY_CHECK_SECURITY_IP_BUSY()	(((R32_SECURITY_AES_OFL[R32_SECURITY_AES_OFL_CTRL] & (AES_OFL_START_MASK << AES_OFL_START_SHIFT))) || \
											((R32_SECURITY_SHA[R32_SECURITY_SHA_CTRL] & (SHA_START_MASK << SHA_START_SHIFT))) ||\
											((R32_PKER[R32_PKE_STA] & CMDQ_OP_BUSY_BIT)))
#define M_SECURITY_SET_AES_ON_THE_FLY_D2H_KEY_MODE(X)	do { \
											R32_SECURITY_AES_OTF[R32_SECURITY_AES_OTF_FUNC] &= (~(AES_OTF_D2H_KEY_MODE_256_MASK << AES_OTF_D2H_KEY_MODE_256_SHIFT)); \
											R32_SECURITY_AES_OTF[R32_SECURITY_AES_OTF_FUNC] |= (((X) & AES_OTF_D2H_KEY_MODE_256_MASK) << AES_OTF_D2H_KEY_MODE_256_SHIFT); \
										} while (0)
/*
 * ---------------------------------------------------------------------------------------------------
 *   extern global variables
 * ---------------------------------------------------------------------------------------------------
 */
extern const U8 gaubSecurityDLMCHashKey[64];

/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */
AOM_SECURITY void *SecReverseCopy(void *pDst, void *pSrc, U32 ulNum);
AOM_INIT void SecurityRamInit(void);
AOM_DLMC U8 SecurityDeScramble(U32 ulSrcAddr, U32 ulByteCnt);
AOM_TCG_COMMON void SecuritySHAExecute(U32 ulSrcAddr, U32 ulSrcSize, U32 ulDstAddr, U8 ubSHAMode, U8 ubSHAType, U8 ubAxiPac32);
AOM_SECURITY void SecurityAESOfflineExecute(SecurityAESParameter_t *pAESParameter);
AOM_SECURITY void SecurityAESOfflineSecurityKeyProtect4KBuf(U8 ubAESDirection,  U32 ulAddr, U32 *pulKeyAddr);
AOM_TCG_COMMON void SecurityHMACExecuteFirstStep(U8 ubSHAType, volatile U8 *pubKey, U32 ulKeyLength, U32 ulSrcAddr, U32 ulTargetAddr);
AOM_TCG_COMMON void SecurityHMACExecuteSecondStep(U8 ubSHAType, volatile U8 *pubInput, U32 ulInputSize, U32 ulTargetAddr, U8 ubEnFinalRound);
AOM_TCG_COMMON void SecurityHMACExecuteThirdStep(U8 ubSHAType, U32 ulKeyLength, volatile U8 *pubOutput);
AOM_TCG_COMMON void SecurityPBKDF2HMAC256WithXOR(U8 *pubInput, U32 ulInputLength, U8 ubNeedXOR, U8 *pubT);
AOM_SECURITY void SecurityAESKeyWrap(U8 ubKeyMode, U32 *pulKeyEncryptionKey, U8 ubKeyWrapKeyIdx, const U32 *pulPlain, U32 *pulCipher, U8 ubKeyWrapN4, U32 *pulKeyWrapInitVector, U8 ubEncryptionDecryption);
AOM_SECURITY void SecurityAESSetKeyTableAndKeyRegister(U8 ubIdx, U8 ubIsGenerateDecryptionKey);
AOM_SECURITY void SecurityAESSetKeyTable(U8 ubIdx, U8 ubChainingMode, U8 ubAESKeyType, U32 *pulKey, U64 uoLBA);
AOM_SECURITY void SecurityWaitD2HPathIdle(void);
AOM_TCG_COMMON void SecurityAddDrivelog(U8 ubReason, U64 uoTimeStamp);
AOM_INIT void SecuritySetDataKey(U8 ubKeyIdx, U8 *pubDek);

INLINE void SecuritySetAESBypass(U8 ubTcgAESEn)
{
	if (TRUE == (((TRUE == ubTcgAESEn) || D2H_AES_EN) && (!BURNER_MODE_EN) && (!LPM3_LOADER))) {
		if ((FALSE == NO_HOST_BY_V7_EN) && (FALSE == FPGA_RTL_2CH)) {
			M_SECURITY_CLEAR_AES_BYPASS();
		}
		else {
			M_SECURITY_SET_AES_BYPASS();
		}
	}
	else {
		M_SECURITY_SET_AES_BYPASS();
	}
}
#endif /* _SECURITY_API_H_ */
