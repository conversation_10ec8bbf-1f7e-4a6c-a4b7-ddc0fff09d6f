#ifndef _VUC_ISPREADFLASH_H_
#define _VUC_ISPREADFLASH_H_

void VUC_ISPReadFlash(VUC_OPT_HCMD_PTR_t pCmd);
U8 VendorReadFlash(U16 uwFWSlotBitMap, U8 ubActSlotIdx, U32 *pulCodeSize);
#if (USB == HOST_MODE)
AOM_VUC U8 VUCReadPadPage(FlashAccessInfo_t *pFlashInfo);
AOM_VUC void VUC_ReadSinglePage(U32 ulPhysicalAddr, U32 ulPCA, U8 COP0Par, U8 ubReadFrameNumber, U8 ubReadMode);
#endif /* (USB == HOST_MODE) */
AOM_VUC U8 VUCReadIdpage(FlashAccessInfo_t *pFlashInfo);
#if (PS5017_EN)
AOM_VUC U8 VUCReadPadpage(FlashAccessInfo_t *pFlashInfo);
#endif /* (PS5017_EN) */
#endif /* _VUC_ISPREADFLASH_H_ */
