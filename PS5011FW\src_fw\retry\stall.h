
#ifndef _STALL_H_
#define _STALL_H_

#include "hal/fip/fip_api.h"

//Debug
#define MTP_RS_PROGRAME_UART_EN		FALSE

#define		MTP_STALL_DEBUG												TRUE

#define	STALL_CHECK_NO_MT_RESOURCE_CNT 	64
#define   MTP_WAIT_STALL_LOOP_MAX_CNT                                          64

#define	ARM_STALL_EN							FALSE
#define	PATCH_LITE_SUPPORT_IOR_EN				TRUE

//Andes chat room IRAM Address offset
#define MTP_STALL_ANDES_CHAT_ROOM_OFFSET	(0x1AE80)

#define STALL_REG_BM_SIZE		8
#define ARM_STALL_BM_SIZE		8
#define ANDES_STALL_BY_ARM_BM	8
#define ANDES_STOP_FORM_MT_REQUEST	8

#define M_GET_STALL_REG_BM_ADDRESS	((U32)(COP0_RAM_ADDRESS + MTP_STALL_ANDES_CHAT_ROOM_OFFSET))
#define M_GET_ARM_STALL_BM_ADDRESS	((U32)(M_GET_STALL_REG_BM_ADDRESS + STALL_REG_BM_SIZE))
#define M_GET_ANDES_STALL_BY_ARM_BM_ADDRESS	((U32)(M_GET_ARM_STALL_BM_ADDRESS + ARM_STALL_BM_SIZE))
#define M_GET_ANDES_STOP_FORM_MT_REQUEST_BM_ADDRESS	((U32)(M_GET_ANDES_STALL_BY_ARM_BM_ADDRESS + ANDES_STALL_BY_ARM_BM))

#define READ_NOT_FOUND		0
#define READ_MTQ_HEAD			1
#define READ_AFTER_WRITE		2

#define READ_NOT_FOUND_MTP	0
#define STALL_BIT_NOT_FOUND	0
#define STALL_MTP_DIRECTLY		1
#define SET_MTD_STALL_BIT		2
#define STALL_BIT_CLEAR			3




#define CHECK_STALL_DONE				0
#define FIP_BUSY							1
#define NEED_WAIT_MT					2
#define FIP_INT							3

#define MTP_MTQ_NOT_EMPTY			4
#define CURRENT_Q_STALL_DONE			5
#define CURRENT_Q_NOT_STALL 			6
#define CURRENT_QUEUE_ABANDON_WAIT_STALL 			7

#define INVALID_MTP_INDEX		0xFF

#define MTP_SET_STALL_BIT		0
#define MTP_CLEAN_STALL_BIT	1


#define Q_ARB_INIT_VALUE		0x3
#define Q_ARB_ALL_Q_SAME_PRIORITY_VALUE		0x1F  //  <=  this value will be higher priority  , 0x1F  all queue be same priority


#define MTP_STALL				TRUE
#define MTP_NOT_STALL			FALSE

#define IOR_GROUP_NO_MATCH	0
#define IOR_MTQ_EMPTY			1
#define IOR_GROUP_MATCH		2

//Debug info
#define	INIT_VALUE				0
#define	ANDES_REQUEST_STALL		1
#define	STALL_BY_ARM			2
#define	STALL_BY_STALL_BIT				3
#define	STALL_BY_ERROR_CQ_STALL_BIT	4
#define	STALL_BY_ERROR_CQ			5
#define	FORCE_STALL_Q_STALL_BIT			6
#define	FORCE_STALL_Q_ARM				7
#define	FORCE_STALL_Direct_Handle_Q_ARM		8

#define	INIT_VALUE				0
#define	STALL_CNT_UPDATE		1
#define	IOR_PATCH			2
#define	OPT_PATCH				3
#define	RE_TRIGGER_MT		4

#define	NOT_FOUND_INIT				0
#define	PATCHLITE_FOUND		1
#define	PATCHLITE_NOT_FOUND			2

//Record Stall/Destall Last modified Queues
#define	MTP_STALL_MAX_RECORD_MODIFY_NUM	4

typedef enum MTPStallEventEnum {
	//MTP_Stall Debug Use, Should be Sync with RetryModeEnum
	MTP_STALL_REQUEST_EVENT_NOT_DEFINE_OR_IDLE = 0,
	MTP_STALL_REQUEST_EVENT_HB_STATE,
	MTP_STALL_REQUEST_EVENT_SINGLE_QUEUE,
	MTP_STALL_REQUEST_EVENT_SB_STATE,
	MTP_STALL_REQUEST_EVENT_ZQC_CHANNEL_STATE,
	MTP_STALL_REQUEST_EVENT_CODE_BANK,
	MTP_STALL_REQUEST_EVENT_RS_STATE,
	MTP_STALL_REQUEST_EVENT_BFEA_CHANNEL_STATE,
	//Above Should be Sync with RetryModeEnum

	//aditional for other Stall/Destall request
	MTP_STALL_REQUEST_EVENT_PROGRAM_FAIL,
	MTP_STALL_REQUEST_EVENT_RMA_LOG,
	MTP_STALL_REQUEST_EVENT_GEN_PROGRAM_FAIL,

	MTP_STALL_REQUEST_EVENT_ERROR_RECORDER_BUFFER_ERROR,
	MTP_STALL_REQUEST_EVENT_ERROR_RECORDER,
	MTP_STALL_REQUEST_EVENT_TRIGGER_CORRECT_BUFFER_ERROR,
	MTP_STALL_REQUEST_EVENT_READ_RETRY_PATCH_DONE,
	MTP_STALL_REQUEST_EVENT_OPENBLOCKRAID_CHECK_MTQ
} MTPStallEventEnum_t;

//Record MTP stall modified history
typedef union {
	U8 ubAll;
	struct {
		U8 StallCnt:		3;
		U8 StallEvent:	5;
	} StallInfo;
} MTPStallModifyRecord_t;

typedef struct {
	U8 gubMTPStallCntPerQueue[MAX_CHANNEL * MAX_CE_PER_CHANNEL];
	U8 gubMTPStallCntPerQueueQos[MAX_CHANNEL * MAX_CE_PER_CHANNEL];
	U32 gulMTPStallQueueSend_BM;	//Use to record Q that send Stall request, also use for avoid re++ stall cnt(by clean when ++)
	U8 gubForBackupStallCnt[MAX_CHANNEL * MAX_CE_PER_CHANNEL];
	U8 gubMTPStallRoundCnt[MAX_CHANNEL * MAX_CE_PER_CHANNEL];
	U8 gubMTPStallBitList[MAX_CHANNEL * MAX_CE_PER_CHANNEL];
	U8 ubErrJobCntPerQueue[MAX_CHANNEL * MAX_CE_PER_CHANNEL];
	U8 ubMTIndexBM[(MT_RETRY_NUM >> BITS_PER_BYTE_LOG)];
	//Round Cnt for checking MT_IRAM resource not enough
	U8 ubMTIRAMResourceCheckRoundCnt[MAX_CHANNEL * MAX_CE_PER_CHANNEL];
	U32 gulStallJobNeedPatch_BM;
	U32 gulProgramFailUsing_BM;

	//Debug Info
	U8 gubMTPStallQueueState[MAX_CHANNEL * MAX_CE_PER_CHANNEL];
	U8 gubMTPPatchState[MAX_CHANNEL * MAX_CE_PER_CHANNEL];
	U8 gubPatchLiteState[MAX_CHANNEL * MAX_CE_PER_CHANNEL];
	U8 ubMTDbyPatch;
	MTPStallModifyRecord_t ubSetStallModify[MAX_CHANNEL * MAX_CE_PER_CHANNEL][MTP_STALL_MAX_RECORD_MODIFY_NUM];
	MTPStallModifyRecord_t ubDestallModify[MAX_CHANNEL * MAX_CE_PER_CHANNEL][MTP_STALL_MAX_RECORD_MODIFY_NUM];
	U32 ulStopFormMTEventCnt;
	U32 gulMTPCacheProgCnt;
#if (MICRON_FSP_EN)
	U8 ubDieNeedPatch_BM[MAX_CHANNEL * MAX_CE_PER_CHANNEL];
#endif /* (MICRON_FSP_EN) */
	U32 ulForceReadPatchBMP;

	U16 uwProgFailCnt;
	U16 uwOtherFailCnt;

} MTP_STALL_MGR_t;

extern MTP_STALL_MGR_t gMTPStallMgr;


//Set Stall_Req_BM
#define M_SET_ARM_STALL_REQ_BM(ubQueueIdx)do{ \
	gpComm2Andes_Info->uoARM_Stall_Req_BM |= BIT64(ubQueueIdx); \
	__asm("DSB"); \
}while(0)

#define M_SET_ARM_STALL_REQ_BM_ALL(uoValue)do{ \
	gpComm2Andes_Info->uoARM_Stall_Req_BM = uoValue; \
	__asm("DSB"); \
} while (0)

//Clean Andes_Stop_FormMT_BM
#define M_CLEAN_ANDES_STOP_FORMMT_BM(ubQueueIdx)do{ \
	gpComm2Andes_Info->uoAndes_Stop_FormMT_BM &= ~ BIT64(ubQueueIdx); \
	__asm("DSB"); \
} while (0)


//Clean Andes_Req_ARM_Directly_Stall_BM
#define M_CLEAN_ANDES_REQ_ARM_DIRECTLY_STALL_BM_ALL()do{ \
	gpComm2Andes_Info->uoAndes_Req_ARM_Directly_Stall_BM = 0; \
	__asm("DSB"); \
} while (0)

#define M_SET_ARM_STOP_FORMMT_REQ_BM(ubQueueIdx)do{ \
	gpComm2Andes_Info->uoARM_Stop_FormMT_Req_BM |= BIT64(ubQueueIdx); \
	__asm("DSB"); \
}while(0)


#if ARM_STALL_EN
AOM_RETRY U8 MTP_Stall(U8 ubMode, U8 ubChannel, U8 ubBank);
AOM_RETRY U8 MTP_Check_Stall(U8 ubMode, U8 ubChannel, U8 ubBank);
AOM_RETRY U8 MTP_CheckAndStall_From_Non_Exec_MT(U8 ubChannel, U8 ubBank, U8 *ubQosPtr);
AOM_RETRY U8 Check_Read_not_Found_From_MTP(U8 ubIndexChannel, U8 ubIndex, U8 ubQos);
AOM_RETRY void Clean_MTD_Stall_Bits(U8 ubIndexChannel, U8 ubIndex, U8 ubQos);
AOM_RETRY void MTP_Stall_Clear_Wait_Cnt_Bmp(U8 ubMode, U8 ubChannel, U8 ubBank);
#endif /* ARM_STALL_EN */

////////////////////////
////MTP Stall by Andes////
///////////////////////
U8 MTP_Stall_Andes(U8 ubMode, U8 ubChannel, U8 ubBank);
U8 MTP_Check_Stall_Andes(U8 ubMode, U8 ubChannel, U8 ubBank);
void MTP_Stall_WaitAndesFlag(U8 ubMode, U8 ubChannel, U8 ubBank);
U8 MTP_Stall_Force_Q_Stall(U8 ubChannel, U8 ubBank, U8 ubQos, U8 ubMode);
U8 MTP_Stall_Check_NonExecMTGroup(U8 ubChannel, U8 ubBank, U8 ubQOS);
U8 CheckMTQStuck(U8 ubIdxChannel, U8 ubIdx, U8 ubQos, U8 ubInMTPStallFlow, U8 ubMode);
U8 MTPStallCheckMTQStuck(U8 ubChannel, U8 ubBank);

U8 Check_Stall_Condition_MTP(volatile MtpMT_t *puMTQ);

U8 MTP_CheckAndStall_From_MTP(U8 ubChannel, U8 ubBank, U8 ubQos, U8 ubMode);
U8 MTP_Destall_Patch_Request(U8 ubChannel, U8 ubBank, U8 ubStallRequestEvent);
AOM_PATCH_CMD void MTP_SetPatchCMDParam(U8 ubMode, U8 ubChannel, U8 ubBank, U8 ubBypassPatch);
void MTP_SetPatchLite(U8 ubMode, U8 ubChannel, U8 ubBank);

void MTP_Stall_Queue_Range(U8 ubMode, U8 ubChannel, U8 ubBank, U8 *ubStartChannel, U8 *ubStartBank, U8 *ubChannelNumber, U8 ubSetPatch);
void MTPStallGenerateFailJob(U8 ubChannel, U8 ubBank, U8 ubCmd);
#if PS5017_EN
void MTPStallDirectHandleQueueCheckCQ(U8 ubMTIdx, U8 ubCmd, U8 ubChannel, U8 ubBank);
void MTPStallForceDirectHandleQueueStable(U8 ubGlobalQueueIdx, U8 ubQosOrNot);
#endif
void MTPStallClearAndesStopFormMTBMP(U8 ubGlobalQueueIdx);
void MTPStallWaitRangeQueueIdle(U8 ubStartChannel, U8 ubStartBank, U8 ubChannelNumber, U8 ubMode, U8 ubAbandon);
U8 MTP_Stall_Check_Q_Idle(U8 ubQueueIdx, U8 ubQos);

void MTP_Stall_Polling_True_Ready(U8 ubMode, U8 ubChannel, U8 ubBank, U8 ubAbandon);
void MTP_Stall_Polling_True_Ready_Check_CQ(void);

void StallResetPatchToInit(U8 ubQueueIdx);

//For "Queue Stuck & Program Fail" case testing
U8 MTP_Stall_Force_Queue_Stuck (U8 ubIndexChannel, U8 ubIndex, U8 ubQosQorNot);


#endif
