#include "typedef.h"
#include "fw_common.h"
#include "hal/cop0/cop0_api.h"

U8 FTLCommonLibraryWordLineInfo(U8 ubY, FTLWordLineInfoGetModeEnum_t ubMode)
{
	U8 ubResult = 0;
#if IM_N28
	FTLWordLinePageNumberEnum_t ubWordLinePageNum;
	FTLWordLineTypeEnum_t ubWordLineType;
	FTLTrimRegNumEnum_t ubWordLineTrimRegNum;

	switch (ubY) {
	case 0:
	case 98:
		// SLC WL
		ubWordLinePageNum = SLC_WL_PAGE_NUM;
		ubWordLineTrimRegNum = SLC_WL_TRIM_REG_NUM;
		ubWordLineType = SLC_WL;
		break;
	case 1:
	case 47:
	case 49:
	case 51:
	case 53:
	case 97:
		// TLC WL
		ubWordLinePageNum = TLC_WL_PAGE_NUM;
		ubWordLineTrimRegNum = TLC_WL_TRIM_REG_NUM;
		ubWordLineType = TLC_WL;
		break;
	default:
		// QLC WL
		ubWordLinePageNum = QLC_WL_PAGE_NUM;
		ubWordLineTrimRegNum = QLC_WL_TRIM_REG_NUM;
		ubWordLineType = QLC_WL;
		break;
	}

	switch (ubMode) {
	case WL_PAGE_NUM:
		ubResult = ubWordLinePageNum;
		break;
	case WL_TYPE:
		ubResult = ubWordLineType;
		break;
	case WL_TRIM_REG_NUM:
		ubResult = ubWordLineTrimRegNum;
		break;
	default:
		M_FW_CRITICAL_ASSERT(ASSERT_FTL_0x0413, FALSE);
		break;
	}
#elif (IM_B47R)
	FTLWordLinePageNumberEnum_t ubWordLinePageNum;
	FTLWordLineTypeEnum_t ubWordLineType;
	FTLTrimRegNumEnum_t ubWordLineTrimRegNum;
	//B47R only need trim TLC WL
	switch (ubY) {
	case 2:
	case 23:
	case 64:
	case 86:
	case 91:
	case 112:
	case 153:
	case 175:
		// TLC WL
		ubWordLinePageNum = TLC_WL_PAGE_NUM;
		ubWordLineTrimRegNum = TLC_WL_TRIM_REG_NUM;
		ubWordLineType = TLC_WL;
		break;
	default:
		M_FW_CRITICAL_ASSERT(ASSERT_FTL_0x0414, FALSE);
		break;
	}

	switch (ubMode) {
	case WL_PAGE_NUM:
		ubResult = ubWordLinePageNum;
		break;
	case WL_TYPE:
		ubResult = ubWordLineType;
		break;
	case WL_TRIM_REG_NUM:
		ubResult = ubWordLineTrimRegNum;
		break;
	default:
		M_FW_CRITICAL_ASSERT(ASSERT_FTL_0x0415, FALSE);
		break;
	}

	return ubResult;
#elif (IM_N48R)
	FTLWordLinePageNumberEnum_t ubWordLinePageNum;
	FTLWordLineTypeEnum_t ubWordLineType;
	FTLTrimRegNumEnum_t ubWordLineTrimRegNum;
	//N48R only need trim QLC WL
	switch (ubY) {
	case 0:
	case 88:
	case 89:
	case 177:
		// MLC WL
		ubWordLinePageNum = MLC_WL_PAGE_NUM;
		ubWordLineTrimRegNum = MLC_WL_TRIM_REG_NUM;
		ubWordLineType = MLC_WL;
		M_FW_CRITICAL_ASSERT(ASSERT_FTL_0x0416, FALSE);
		break;
	default:
		// QLC WL
		ubWordLinePageNum = QLC_WL_PAGE_NUM;
		ubWordLineTrimRegNum = QLC_WL_TRIM_REG_NUM;
		ubWordLineType = QLC_WL;
		break;
	}

	switch (ubMode) {
	case WL_PAGE_NUM:
		ubResult = ubWordLinePageNum;
		break;
	case WL_TYPE:
		ubResult = ubWordLineType;
		break;
	case WL_TRIM_REG_NUM:
		ubResult = ubWordLineTrimRegNum;
		break;
	default:
		M_FW_CRITICAL_ASSERT(ASSERT_FTL_0x0417, FALSE);
		break;
	}

	return ubResult;

#else
	M_FW_CRITICAL_ASSERT(ASSERT_FTL_0x0418, FALSE);
#endif /* IM_N28 */
	return ubResult;
}

U16 FTLCommonLibraryGetSuperPageIdxFromFSA(U32 ulFSA, U8 ubALUSelect)
{
	U16 uwSuperPageIdx = (ulFSA >> gPCARule_Page.ubShift[ubALUSelect]) & gPCARule_Page.ulMask;
#if (!MICRON_FSP_EN)
	if (!M_CHECK_COP0_PCA_IS_SLC(ubALUSelect)) {
		U8 ubLMUNumber = ((ulFSA >> gPCARule_LMU.ubShift[ubALUSelect]) & gPCARule_LMU.ulMask);
		uwSuperPageIdx = (uwSuperPageIdx * gubLMUNumber) + ubLMUNumber;
	}
#endif /* (!MICRON_FSP_EN)*/
	return uwSuperPageIdx;
}
#if 0
U16 FTLCommonLibraryGetSuperPageIdxFromVCA(U32 ulVCA, U8 ubIsSLCMode)
{
	U16 uwSuperPageIdx = 0;
	U32 ulUnitPtr = ( (ulVCA >> gub4kEntrysPerPlaneLog) & gulPlanesPerUnitAlignMask) ;
	if (ubIsSLCMode) {
		uwSuperPageIdx = (ulUnitPtr / gubPlanesPerSuperPage);
	}
	else {
#if (MICRON_FSP_EN)
		U8 ubPageOffset = 0;
		FTLPlaneInx2Physical(ulUnitPtr, &(uwSuperPageIdx), &(ubPageOffset));
#else /* (MICRON_FSP_EN) */
		U16 uwWordlineSize = (gubPlanesPerSuperPage * gubLMUNumber);
		uwSuperPageIdx = (((ulUnitPtr) / (uwWordlineSize)) * gubLMUNumber ) +
			((((ulUnitPtr) % (gubBurstsPerBank * gubLMUNumber)) / gubBurstsPerBank));
#endif
	}

	return uwSuperPageIdx;
}
#else
U16 FTLCommonLibraryGetSuperPageIdxFromVCA(U32 ulVCA, U8 ubIsSLCMode)
{
	U16 uwSuperPageIdx = 0;
	U32 ulUnitPtr = ( (ulVCA >> gub4kEntrysPerPlaneLog) & gulPlanesPerUnitAlignMask) ;
	uwSuperPageIdx = (ulUnitPtr / gubPlanesPerSuperPage);

	return uwSuperPageIdx;
}
#endif
