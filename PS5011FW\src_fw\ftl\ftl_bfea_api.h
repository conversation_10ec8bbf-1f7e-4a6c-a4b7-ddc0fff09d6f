
#ifndef _FTL_BFEA_API_H_
#define _FTL_BFEA_API_H_

#include "ftl/ftl_bfea.h"
#include "common/typedef.h"

enum BFEA_BIN_LIST {
	BFEA_BIN0,
	BFEA_BIN1,
	BFEA_BIN2,
	B<PERSON><PERSON>_BIN3,
	BFEA_BIN4,
	B<PERSON><PERSON>_BIN5,
	BFEA_BIN6,
	BFEA_BIN7,
	BFEA_BIN_NUM,
	BFEA_BIN_NONE = 0xFF
};

#if BFEA_EN

#define BFEA_TABLE_SIZE_4K_NUM	(1)		// The Size of BFEATable is 4K (BFEATable+Varibles)
#define BFEA_TABLE_PLANE_NUM	(1)		// The Size of BFEATable is 4K (BFEATable+Varibles) ,4K(Frame) in one 16K(Plane) is enough

#define BFEA_CHECK_MARK			(0x8299BFEA)

#define M_BFEA_SWAP_VBRMP_REBIND_BF(uwUnit)		do{\
													U16 VBRMPValue = gpuwVBRMP[uwUnit].uwAll;\
													if (!(VBRMPValue & 0xC000)) {\
														BFEAAddFreeUnitRemoveBF(uwUnit);\
														BFEAEndProramUnitBindBF(uwUnit);\
													}\
												}while(0)

enum BFEAFWGetBinMode {
	BFEA_GET_BIN_NORMAL_READ,
	BFEA_GET_BIN_READ_DISTURB,
};

enum BFEA_TEMP_TYPE {
	BFEA_TEMP_0_40,
	BFEA_TEMP_30_60,
	BFEA_TEMP_50_90,
	BFEA_TEMP_TYPE_NUM
};

enum BFEA_BFSCAN_STATE {
	BFSCAN_WAIT_TRIG_SCAN,
	BFSCAN_INIT,
	BFSCAN_GET_SCAN_BIN,
	BFSCAN_GET_OLDEST_BF_IN_TARGET_BIN,
	BFSCAN_GET_MEASURE_UNIT_WLG2_XPPAGE,
	BFSCAN_TRIG_vRLC_MEASURE,
	BFSCAN_CHK_vRLC_ROUND_AND_TABLE_UPDATE,
	BFSCAN_SAVE_SCAN_RESULT,
	BFSCAN_WAIT_SAVE_SCAN_RESULT_DONE,
	BFSCAN_COMBINE_TRIG,
	BFSCAN_FORCE_RESCAN,
	BFSCAN_SCAN_DONE
};

enum BFEA_QUICKSYNC_STATE {
	QUICKSYNC_INIT = 0,
	QUICKSYNC_GET_SYNC_END_BF = 1,
	QUICKSYNC_GET_SYNC_START_BF = 2,
	QUICKSYNC_READ = 3,
	QUICKSYNC_FORCE_GET_SYNC_END_BF = 4,
	QUICKSYNC_DONE = 5,
};

enum BFEA_SMARTRESYNC_STATE {
	RESYNC_INIT,
	RESYNC_GET_SYNC_END_BF,
	RESYNC_GET_SYNC_START_BF,
	RESYNC_BF_MEASURE,
	RESYNC_FORCE_COMBINE_BIN7,
	RESYNC_UPDATE_TABLE,
	RESYNC_FORCE_GET_SYNC_END_BF,
	RESYNC_DONE
};

typedef enum BFEADBUFStateEnum {
	BFEA_DBUF_NO_STATE = 0,
	BFEA_DBUF_HAVE_STATE
} BFEADBUFStateEnum_t;

typedef struct {
	U8 ubBFET; //Block Family Elapsed Time, Unit:Miniute
	U8 ubBFTD; //Block Family Temperature Differencce, Unit:Celsius Degree
	U8 ubBFTMT; //Block Family Temperature Measurement Time, Unit:Second
	U8 ubBFMP[3]; //Bin Measurement Period 0~40 degree, Unit:Miniute
	U8 ubForceCombineTh; //Must Combine to Not Run Out of BFs, Unit:Number of BF
	U8 ubLCombineTh; //The Starting Point to Allow Combining, Unit:DACs
} BFEAMConfig_t;

typedef union {
	U32 ulReadRetryBin[BFEA_MAX_DIE_NUM_PER_CHANNEL];
	U8 ubReadRetryBin[BFEA_MAX_DIE_NUM];
} BFEAFamilyTableEntry_t;

typedef union {
	U16 uwAll;
	struct {
		U8 ubYoungerBF;
		U8 ubOlderBF;
	};
} BFEAActiveBFEntry_t;

typedef union {
	U16 uwAll;
	struct {
#if(BFEA_UNIT_LINKED_LIST)
		U16 NextUnit	: 10;
		U16 ubBF		: 6;
#else/*(BFEA_UNIT_LINKED_LIST)*/
		TTTemperatureCelsius_t Temp;//Record Programming Highest Temp of Die
		U8 ubBF;
#endif/*(BFEA_UNIT_LINKED_LIST)*/
	} Entry;
} BFEASuperBlockTableEntry_t;

typedef struct {
	S8 sbScanAverageMeasureOffset[BFEA_BF_SCAN_MAX_OLDEST_BF_NUM][BFEA_MAX_DIE_NUM];		// 64

	U8 ubScanBinOldestBF[BFEA_BF_SCAN_MAX_OLDEST_BF_NUM];									// 2
	U8 ubMinBinInOldestBF[BFEA_BF_SCAN_MAX_OLDEST_BF_NUM];									// 2
	U8 ubState;																				// 1
	U8 ubMeasurePeriod;//																	// 1
	U8 ubScanBin;																			// 1
	U8 ubFoundOldestBFCnt;																	// 1

	U8 ubScanMaxLeeDistance;																// 1
	U8 ubScanBFListIdx;																		// 1
	U8 ubReserve[6];																		// 6
} BFEABFScan_t;

typedef struct {
	U16 uwUnitList[BFEA_BF_SCAN_UNIT_NUM];												// 6
	U16 uwPageList[BFEA_BF_SCAN_UNIT_NUM][BFEA_BF_SCAN_PAGE_NUM];						// 18
	U64 uoPreviousBFMeasureTime;														// 8
	S16 swTotalMeasureOffset;															// 2
	U8 ubUnitNum;																		// 1
	U8 ubTrimLoopCnt;																	// 1
	U8 ubPageListIdx;																	// 1
	U8 ubUnitListIdx;																	// 1
	U8 ubDieIdx;																		// 1
	U8 ubNeedFirstRead;																	// 1
} BFEAMeasure_t;

typedef union {
	U8 ubAll;
	struct {
		U8 btQuickSyncChkNeedNewBF: 1;
		U8 btForceCombineTrigBFScan: 1;
		U8 btQuickSyncDone: 1;
		U8 btSmartReSyncDone: 1;
		U8 SaveBFEATableRequest: 3;
		U8 ubReserve: 1;
	} bits;
} BEFAEvent_t;

typedef struct {
	U8 ubBFPool[BFEA_BF_TOTAL_NUM];
	//-------------------------------
	U8 ubBFPoolHead;
	U8 ubBFPoolTail;
	U8 ubYoungestBF;
	U8 ubOldestBF;
	//-------------------------------
	U16 uwCurrentBFBindUnitCnt;
	U8 ubUsingEmergencyBF;
	U8 ubReserve1;
	U32 ulAPLFreezeBFCreateCnt;
	U32 ulForceBFCombineCnt;
	//-------------------------------
	U8 ubUsedBFCnt;
	U8 ubBinHeadBF[BFEA_BIN_NUM];//Each Bin Ptr's Youngest BF Idx
	U8 ubReserve[7];
} BlockFamilyMgr_t;

typedef struct {
	U64 uoStartTimer;								// 8
	U32 ulRoundStartTime;							// 4
	U8 ubBinHeadBF[BFEA_BIN_NUM];					// 8
	U8 ubExpectHeadBF[BFEA_BIN_NUM];				// 8
	U8 ubState;										// 1
	U8 ubCheckBin;									// 1
	U8 ubFillBinDone;								// 1
	U8 ubCopyStartBF;								// 1
	U8 ubCopyEndBF;									// 1
	U8 ubCheckBinStartBF;								// 1
	U8 ubCheckBinEndBF;								// 1
	U8 ubMeasureBF;									// 1
	U8 ubFindBin;									// 1
	U8 ubCopyShift;									// 1
	U8 ubReserve[2];								// 2
} BFEAQuickSync_t;

typedef struct {
	U64 uoStartTimer;							// 8
	U64 uoBFScanDoneBMP;						// 8
	U8 ubExpectHeadBF[BFEA_BIN_NUM];			// 8
	U8 ubState;									// 1
	U8 ubChkBin;								// 1
	U8 ubMeasureBF;								// 1
	U8 ubDieIdx;								// 1
	U8 ubBFMinBin;								// 1
	U8 ubChkBinStartBF;							// 1
	U8 ubChkBinEndBF;							// 1
	U8 ubMoveBinDieCnt;							// 1
} BFEASmartReSync_t;

typedef struct {
	BFEABFScan_t BFScan;//VM				//	80
	BFEASmartReSync_t SmartReSync;			//	32
	BFEAQuickSync_t QuickSync;				//	40
	BFEAMeasure_t Measure;					//	40
	U32 ulBFStartTime;//VM						4
	U32 ulLastEnableBFScanTime;//VM				4
	U32 ulLastTempMeasureTime;//VM				4
	U32 ulEnterLPMTime;						//	4
	TTTemperatureCelsius_t ubBFHighTemp;//VM	1
	TTTemperatureCelsius_t ubBFLowTemp;//VM		1
	BEFAEvent_t ubEvent;//VM					1
	U8 ubReserve[5];						//  	5
} BEFAMgr_t;

typedef struct {
	U16 uwBFHeadUnit[BFEA_BF_TOTAL_NUM];		// 2 * 64 = 128 B
	U16 uwBFTailUnit[BFEA_BF_TOTAL_NUM];		// 2 * 64 = 128 B
	U16 uwBFBindingUnitCnt[BFEA_BF_TOTAL_NUM];	// 2 * 64 = 128 B
	U16 uwOldestUnit;							// 2
	U16 uwYoungestUnit;							// 2
	U16 uwTotalBindingUnitCnt;					// 2
} UnitListMgr_t;

typedef struct {
	BFEAFamilyTableEntry_t FamilyTable[BFEA_BF_TOTAL_NUM];			// 32 *  64 = 2048 B
	BFEAActiveBFEntry_t ActiveBFTable[BFEA_BF_TOTAL_NUM];			// 2  *  64 =   128 B			//Point to next older BF Idx
	BFEASuperBlockTableEntry_t SuperBlockTable[BFEA_MAX_UNIT_NUM];	// 2  * 556 = 1112 B
	BlockFamilyMgr_t BF;											//               96 B
	BEFAMgr_t BFEAMgr;												//              216 B
#if BFEA_UNIT_LINKED_LIST
	UnitListMgr_t UnitListMgr;										//		 390 B
	U8 ubReserved[102];
#else /*BFEA_UNIT_LINKED_LIST*/
	U8 ubReserved[492];
#endif /*BFEA_UNIT_LINKED_LIST*/
	U32 ulCheckMark;												//			4B
} BFEANVM_t;

TYPE_SIZE_CHECK(BlockFamilyMgr_t, 96 * SIZE_1B);
TYPE_SIZE_CHECK(BEFAMgr_t, 216 * SIZE_1B);
TYPE_SIZE_CHECK(BFEANVM_t, SIZE_4KB);

extern BFEAMConfig_t gBFEAMConfig;
extern BEFAMgr_t *gpBFEAMgr;
extern BFEANVM_t *gpBFEANVM;//NVM:Non volatile memory

extern U32 gulBFEABFElapsedTime;
//extern U32 gulBFEABFExtensionTime;
extern U32 gulBFEABFTempMeasureTime;
extern U32 gulBFEABFMeasurePeriod[BFEA_TEMP_TYPE_NUM];
//extern U32 gulBFEARefCalStartMfg;
//extern U32 gulBFEARefCalEndMfg;
//extern U32 gulBFEARefCalStartTime;
//extern U32 gulBFEARefCalEndTime;
extern U16 gulBFEADebugUnit;
extern U8 gubBFEACurrentTempType; // 0 : 0-40 1: 30-60 2: 50-90
extern U8 gubBFEASampleMod[BFEA_MAX_SAMPLE_MOD];
extern U8 gubBFEATableLoadError;
extern S8 gsbBFEABinBoundary[BFEA_BIN_NUM];
extern U8 gubBFEAACRRMapping[BFEA_BIN_NUM];

AOM_BFEA void BFEAInit(void);
void BFEADelegate(void);
U8 BFEAFWPCAGetBin(U32 ulFWPCA, U8 ubMode);
AOM_BFEA void BFEAStartProramUnitBindBF(U16 uwUnit);
AOM_BFEA void BFEAEndProramUnitBindBF(U16 uwUnit);
#if (RELEASED_FW)
void BFEAAddFreeUnitRemoveBF(U16 uwUnit);
#else
AOM_BFEA void BFEAAddFreeUnitRemoveBF(U16 uwUnit);
#endif
AOM_BFEA U8 BFEATestBECBinSearch(U16 uwUnit, U8 ubSelectDie, U8 ubMinBin, BFEANVM_t *pBFEANVM);
AOM_BFEA void BFEAMConfigParser(void);
AOM_BFEA void BFEAPreformatProgramTable(void);
AOM_INIT void BFEAGetBinSetConfigFromInfoBlk(void);
void BFEACheckCurrentTemp(void);
#if(!BFEA_UNIT_LINKED_LIST)
TTTemperatureCelsius_t BFEAGetSuperBlockHighestTemp(U16 uwUnit);
#endif/*(!BFEA_UNIT_LINKED_LIST)*/
#endif/*BFEA_EN*/
#endif/*_FTL_BFEA_API_H_*/



