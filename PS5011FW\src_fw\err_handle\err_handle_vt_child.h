#ifndef _ERR_HANDLE_VT_CHILD_H_
#define _ERR_HANDLE_VT_CHILD_H_

#include "fw_vardef.h"
#include "aom/aom_api.h"
#include "err_handle/err_handle_api.h"

//==============================================================================
// Definition
//==============================================================================
//switch
#define ERR_HDL_VT_COPY_DEBUG_VERIFY_EN					(FALSE)
#define ERR_HDL_VT_DEBUG_CHECK_RUT						(FALSE)
#define ERR_HDL_VT_DEBUG_PRINT_RUT						(FALSE)

#define ERR_HDL_VT_CHILD_DEBUG_FORCE_SAVE_INITINFO_EN		(FALSE)
#define ERR_HDL_VT_CHILD_DEBUG_CHECK_SYS_USER_CNT			(FALSE)

// other
#define ERR_HDL_VT_CHILD_COPY_VT_NUM	(16)
#define ERROR_HANDLE_VT_MAX_VT_UNIT		(4)

//mode
#define ERROR_HANDLE_MODE_SEARCH_UNIT_SINGLE_PHASE			(0)
#define ERROR_HANDLE_MODE_SEARCH_UNIT_ALL_PHASE			(1)
#define ERROR_HANDLE_MODE_SEARCH_BLOCK_SINGLE_PHASE		(2)
#define ERROR_HANDLE_MODE_SEARCH_BLOCK_ALL_PHASE			(3)
#define ERROR_HANDLE_MODE_SEARCH_ALL_VT_MOTHER_UNIT_ALL_PHASE	(4)

#define MODE_REPAIR_BLOCK_OLD			(0)
#define MODE_REPAIR_BLOCK_NEW			(1)

#define MODE_VT_CHILD					(0)
#define MODE_VT_MOTHER					(1)

#define ERROR_HANDLE_MODE_PENDING_HANDLE				(0)
#define ERROR_HANDLE_MODE_IMMEDIATELY_HANDLE			(1)
#define ERROR_HANDLE_MODE_NOT_HANDLE					(2)

//gen fail
#define RAND_GEN_R_FAIL_VT_CHILD_COPY		(TRUE & (!BURNER_MODE_EN))
#define RAND_GEN_P_FAIL_VT_CHILD_COPY		(TRUE & (!BURNER_MODE_EN))
#define RAND_GEN_P_FAIL_VT_CHILD_1ST		(TRUE & (!BURNER_MODE_EN))
#define RAND_GEN_P_FAIL_VT_CHILD_2ND		(TRUE & (!BURNER_MODE_EN))
#define RAND_GEN_P_FAIL_VT_CHILD_MOTHER		(TRUE & (!BURNER_MODE_EN))//program vt mother fail (in VT Child Error Handle)

#define RAND_GEN_R_FAIL_VT_MOTHER_COPY		(TRUE & (!BURNER_MODE_EN))
#define RAND_GEN_P_FAIL_VT_MOTHER_COPY		(TRUE & (!BURNER_MODE_EN))
#define RAND_GEN_P_FAIL_VT_MOTHER_MOTHER	(TRUE & (!BURNER_MODE_EN))//program vt mother fail (in VT Mother Error Handle)

//==============================================================================
// Structures
//==============================================================================
typedef enum ErrHandleVTChildStateEnum {
	ERR_HANDLE_VT_CHILD_INIT 						= 0x01,
	ERR_HANDLE_VT_CHILD_ALLOCATE_RESOURCE			= 0x02,
	ERR_HANDLE_VT_CHILD_LOAD_RUTST_N_GEN_DBT_LOG	= 0x03,
	ERR_HANDLE_VT_CHILD_GET_FREE_UNIT				= 0x04,
	ERR_HANDLE_VT_CHILD_ERASE_UNIT					= 0x05,
	ERR_HANDLE_VT_CHILD_WAIT_ERASE_DONE				= 0x06,
	ERR_HANDLE_VT_CHILD_REPAIR_UNIT					= 0x07,
	ERR_HANDLE_VT_CHILD_COPY_UNIT					= 0x08,
	ERR_HANDLE_VT_CHILD_RESTRUCT_RUT				= 0x09,
	ERR_HANDLE_VT_CHILD_CLEAR_ERROR_LOG				= 0x0A,
	ERR_HANDLE_VT_CHILD_UPDATE_VT					= 0x0B,
	ERR_HANDLE_VT_CHILD_SAVE_VBRMP					= 0x0C,
	ERR_HANDLE_VT_CHILD_SEARCH_LATER_ERROR_LOG		= 0x0D,
	ERR_HANDLE_VT_CHILD_HANDLE_VT_MOTHER_FAIL		= 0x0E,
	ERR_HANDLE_VT_CHILD_PROGRAM_VT_MOTHER			= 0x0F,
	ERR_HANDLE_VT_CHILD_PROGRAM_VT_CHILD_1ST		= 0x10,
	ERR_HANDLE_VT_CHILD_PROGRAM_VT_CHILD_2ND		= 0x11,
	ERR_HANDLE_VT_CHILD_SYSTEM_UPDATE				= 0x12,
	ERR_HANDLE_VT_CHILD_ADD_FREE_UNIT				= 0x13,
	ERR_HANDLE_VT_CHILD_FREE_RESOURCE				= 0x14,
	ERR_HANDLE_VT_CHILD_RESUME_FTLHANDLE			= 0x15,
	ERR_HANDLE_VT_CHILD_FINISH						= 0x16,
} ErrHandleVTChildState_t;

typedef enum ErrHandleVTMotherStateEnum {
	ERR_HDL_VT_MOTHER_INIT 							= 0x01,
	ERR_HDL_VT_MOTHER_ALLOCATE_RESOURCE				= 0x02,
	ERR_HDL_VT_MOTHER_GET_FREE_NEW_BLOCK			= 0x03,
	ERR_HDL_VT_MOTHER_ERASE_NEW_BLOCK				= 0x04,
	ERR_HDL_VT_MOTHER_WAIT_ERASE_DONE				= 0x05,
	ERR_HDL_VT_MOTHER_REPAIR_NEW_BLOCK				= 0x06,
	ERR_HDL_VT_MOTHER_COPY_BLOCK					= 0x07,
	ERR_HDL_VT_MOTHER_REPAIR_OLD_BLOCK				= 0x08,
	ERR_HDL_VT_MOTHER_SAVE_VBRMP					= 0x09,
	ERR_HDL_VT_MOTHER_SEARCH_LATER_ERROR_LOG		= 0x0A,
	ERR_HDL_VT_MOTHER_UPDATE_VT						= 0x0B,
	ERR_HDL_VT_MOTHER_PROGRAM_VT_MOTHER				= 0x0C,
	ERR_HDL_VT_MOTHER_SYSTEM_UPDATE					= 0x0D,
	ERR_HDL_VT_MOTHER_FREE_RESOURCE					= 0x0E,
	ERR_HDL_VT_MOTHER_RESUME_FTLHANDLE				= 0x0F,
	ERR_HDL_VT_MOTHER_FINISH						= 0x10,
} ErrHandleVTMotherState_t;

typedef enum ErrHandleVTCopyStateEnum {
	ERR_HANDLE_VT_COPY_INIT 			= 0x01,
	ERR_HANDLE_VT_COPY_ALLOC_COPY_BUF 	= 0x02,
	ERR_HANDLE_VT_COPY_READ_SOURCE 		= 0x03,
	ERR_HANDLE_VT_COPY_PROG_TARGET		= 0x04,
	ERR_HANDLE_VT_COPY_VERIFY 			= 0x05,
	ERR_HANDLE_VT_COPY_FREE_COPY_BUF	= 0x06,
	ERR_HANDLE_VT_COPY_FINISH			= 0x07,
} ErrHandleVTCopyState_t;

typedef enum ErrorHandleVTRestoreModeEnum {
	ERROR_HANDLE_RESTORE_ALL_BLK = 0x00,
	ERROR_HANDLE_RESTORE_SPECIFIC_BLK = 0x01,
} ErrorHandleVTRestoreMode_t;

typedef struct {
	U8 ubState;
	U16 uwSourceUnit;
	U16 uwTargetUnit;
	U32 ulSourcePlaneIndex;
	U32 ulTargetPlaneIdx;
	U32 ulCopiedPlaneCnt;
	U32 ulNeedCopyPlaneNum;
	U32 ulSuccessReadPlaneIndex;
	U8 btStateDoing				: 1;
	U8 btCopyAbort				: 1;
	U8 							: 6;
} ErrHdlCopyVTChild_t;

typedef struct {
	U8 ubState;
	U16 uwSourceUnit;
	U16 uwTargetUnit;
	U8 ubSourceBlockIndex;
	U8 ubTargetBlockIndex;
	U16 uwSourceBlockPlaneIndex;
	U16 uwTargetBlockPlaneIndex;
	U16 uwSuccessReadBlockIndex;
	U16 uwSuccessReadBlockPlaneIndex;
	U8 btStateDoing				: 1;
	U8 btCopyAbort				: 1;
	U8 							: 6;
} ErrHdlCopyVTMother_t;

typedef struct {
	ErrLog_t ulErrorLog;
	U8 ubErrorPlaneBankBMP[RUT_MAX_PLANEBANK / BITS_PER_BYTE];
} ErrorHandleVTPendingError_t;

//==============================================================================
// Function API
//==============================================================================
// VT COMMON
AOM_ERROR_HANDLE2 void ErrHandleVTCheckSysUserCnt_Debug(U8 ubMode);
AOM_ERROR_HANDLE2 void ErrHandleVTBackupSysUserCnt_Debug(U8 ubMode);
AOM_ERROR_HANDLE2 U8 ErrHandleVTCopyReadSource(U8 ubMode);
AOM_ERROR_HANDLE2 U8 ErrHandleVTCopyProgTarget(U8 ubMode);
AOM_ERROR_HANDLE2 U16 ErrHandleVTSearchFailErrorLog(U8 ubMode, U16 uwTargetUnit, U8 ubPlaneBank, U8 ubFailPhase);
AOM_ERROR_HANDLE2 void ErrHandleVTRead4K(PCA_t ulDataPCA, U32 ulBufAddr, U32 ulTieInLCA);
AOM_ERROR_HANDLE2 void ErrHandleVTReadSinglePlane(U32 *pulLCA, U32 *pul4KBufferAddr, PCA_t ulPCA, U8 ubNeedWaitDone);
AOM_ERROR_HANDLE2 void ErrHandleVTProgramSinglePlane(U32 *pulLCA, U32 *pul4KBufferAddr, PCA_t ulPCA);
AOM_ERROR_HANDLE2 void ErrHandleVTEraseResumeOldVTMother(void);
AOM_ERROR_HANDLE2 void ErrHandleVTAllocateResource(U8 ubOwner);
AOM_ERROR_HANDLE2 void ErrHandleVTAllocateCopyBuffer(void);
AOM_ERROR_HANDLE2 void ErrHandleVTFreeResource(U8 ubOwner);
AOM_ERROR_HANDLE2 void ErrHandleVTFreeCopyBuffer(void);
AOM_ERROR_HANDLE2 void ErrHandleVTCopyVerify(U8 ubMode);
AOM_ERROR_HANDLE2 void ErrHandleVTClearStatus(U8 ubMode);
AOM_ERROR_HANDLE2 void ErrHandleVTUpdateVT(U8 ubMode);
AOM_ERROR_HANDLE2 void ErrHandleVTProgramVTMother(U8 ubMode);
AOM_ERROR_HANDLE2 void ErrHandleVTProgramVTChild(void);

// VT CHILD
AOM_ERROR_HANDLE2 U8 ErrHandleVTChildCopyUnit(void);
AOM_ERROR_HANDLE2 void ErrHandleVTChildRestructRUT(void);
AOM_ERROR_HANDLE2 void ErrHandleVTChildGetErrorLog(U16 uwLogOffset);
AOM_ERROR_HANDLE2 void ErrHandleVTChildCopyUnitInit(void);
AOM_ERROR_HANDLE2 void ErrHandleVTChild(U16 uwErrorLogOffset);

// VT MOTHER
AOM_ERROR_HANDLE2 U8 ErrHandleVTMotherCheckNGetFailInfo(U8 ubFailPhase);
AOM_ERROR_HANDLE2 U8 ErrHandleVTMotherCopyBlock(void);
AOM_ERROR_HANDLE2 void ErrHandleVTMotherCopyInit(void);
AOM_ERROR_HANDLE2 void ErrHandleVTMother(U16 uwErrLogOffset);
AOM_ERROR_HANDLE2 void ErrorHandleVTMotherRestorePendingError(ErrorHandleVTRestoreMode_t ubRestoreMode, U8 ubRestoreVTUnitIdx, U8 ubRestoreVTBlk);
AOM_ERROR_HANDLE2 U8 ErrorHandleIsThisVTBlkGotPendingEvent(U8 ubCheckVTUnitIdx, U8 ubCheckVTBlk);

//==============================================================================
// Variable
//==============================================================================

#endif /* _ERR_COPY_API_H_ */
