#ifndef _VUC_DETECTPERIPHERAL_H_
#define _VUC_DETECTPERIPHERAL_H_
#include "symbol.h"
#include "aom/aom_api.h"
#include "host/VUC_handler.h"

#define TML_DATA_SHIFT					(4)
#define VUC_PERIPHERAL_PMIC6117_ID      (0x05)
#define VUC_PERIPHERAL_LOADSWITCH_ID	(0x02)
#define VUC_PERIPHERAL_TML_ID			(0x01)
#define VUC_PERIPHERAL_PMIC_ID			(0x00)
#define TML_RES_DIVIDEN					(625)
#define TML_RES_DIVISOR					(10000)
#define VUC_DETECT_PERIPHERAL_DETECT_FAIL		(0xFF)
#define VUC_DETECT_PERIPHERAL_DETECTED			(TRUE)
#define VUC_DETECT_PERIPHERAL_READ 	(0)
#define VUC_DETECT_PERIPHERAL_WRITE 	(1)

#if (HOST_MODE == NVME)
AOM_VUC void VUC_DetectPeripheral(VUC_OPT_HCMD_PTR_t pCmd);
#else
AOM_VUC_3 void VUC_DetectPeripheral(VUC_OPT_HCMD_PTR_t pCmd);
#endif
#if (HOST_MODE == SATA)
AOM_INIT void SATAVUCDetectLoadSwitch (VUC_OPT_HCMD_PTR_t pCmd, U32 ulReturnBuf);
#endif /* (HOST_MODE == SATA) */
#endif /* _VUC_DETECTPERIPHERAL_H_ */
