#include "burner/Burner_api.h"
#include "spare.h"
#include "ftl/ftl_api.h"
#include "hal/pic/uart/uart_api.h"
#include "init/fw_init.h"
#include "nvme_api/nvme/shr_hal_nvme.h"
#include "table/sys_area/sys_area_api.h"
#include "vuc/VUC_ScanPH.h"

PH_t gPH;
#if (PHBLOCK_EN)
void VUCScanPH(U32 ulDstAddr)
{
	U8 ubBlkCnt;
	U16 uwLastWritePage[PH_NUM] = {0};
	U16 uwMPLogPage[PH_NUM] = {VUC_PH_NO_LOG_FOUND, VUC_PH_NO_LOG_FOUND};
	U16 uwRDTLogPage[PH_NUM] = {VUC_PH_NO_LOG_FOUND, VUC_PH_NO_LOG_FOUND};
	U16 uwECLogPage[PH_NUM] = {VUC_PH_NO_LOG_FOUND, VUC_PH_NO_LOG_FOUND};
#if VRLC_EN
	U16 uwVRLCLogPage[PH_NUM] = {VUC_PH_NO_LOG_FOUND, VUC_PH_NO_LOG_FOUND};
#endif /* VRLC_EN */
	U16 uwPageInBlkPtr;
	U8 ubCEPtr, ubChannelPtr, ubBlkPtr;
	U8 ubNeedErase = 0;
	U8 ubNeedEraseTarget;
	U8 ubPCARuleMode;
	PCA_t ulFWPCA;
	U32 ulLocalPCA;
	U8 ubLocalLPCRC = 0;
	FlashAccessInfo_t FlashInfo = {0};
	M_INIT_SCAN_SET_FIP_NON_STOP();
	if (PH_UART_EN) {
		M_UART(VUC_, "\n [PHInfo] ScanProductHistory ");
	}

	gubLeavePreformatFlag = 0;
	for (ubBlkCnt = 0; ubBlkCnt < PH_NUM; ubBlkCnt++) {
		ubBlkPtr = gPH.PHBlk[ubBlkCnt].Info.ubBlockOrder;
		ubCEPtr = gPH.PHBlk[ubBlkCnt].Info.CEperCH;
		ubChannelPtr = gPH.PHBlk[ubBlkCnt].Info.Channel;

		uwPageInBlkPtr = 0;

		while (1) {
			ubPCARuleMode = (gFlhEnv.ulFlashDefaultType.BitMap.CellType == FLH_SLC) ? COP0_PCA_RULE_0 : COP0_PCA_RULE_2;
			ulLocalPCA = M_GET_PCA(0, M_DIV(ubBlkPtr, gubBurstsPerBank), uwPageInBlkPtr, ubCEPtr, ubChannelPtr, M_MOD(ubBlkPtr, gubBurstsPerBank), 0, ubPCARuleMode);
			M_FWPCA_SET(ulFWPCA.ulAll, ulLocalPCA, gPCAInfo.ubMaxZByte, 0, ubLocalLPCRC);

			FTLCOP0ReadN4kEntry_AOM_INIT_2((FTLCOP0ReadN4kEntryMode_t) {
				R_GETLCA_FROM_L4KTABLE, COP0_R_SYSTEM_INITIAL_SCAN, 1, NULL
			}, ulFWPCA, &ulDstAddr);

			if (SPARE_LCA_PH_MP_LOG == gulTieoutLCA[TIEOUT_SINGLE_4K_LCA_OFFSET]) {
				uwMPLogPage[ubBlkCnt] = uwPageInBlkPtr;
			}
			else if (SPARE_LCA_PH_RDT_LOG == gulTieoutLCA[TIEOUT_SINGLE_4K_LCA_OFFSET]) {
				uwRDTLogPage[ubBlkCnt] = uwPageInBlkPtr;
			}
			else if (SPARE_LCA_PH_EC_LOG == gulTieoutLCA[TIEOUT_SINGLE_4K_LCA_OFFSET]) {
				uwECLogPage[ubBlkCnt] = uwPageInBlkPtr;
			}
#if VRLC_EN
			else if (SPARE_LCA_PH_VRLC_LOG == gulTieoutLCA[TIEOUT_SINGLE_4K_LCA_OFFSET]) {
				uwVRLCLogPage[ubBlkCnt] = uwPageInBlkPtr;
			}
#endif /* VRLC_EN */
			else if (gubIsErasePage) {
				// empty
				if (PH_UART_EN) {
					M_UART(VUC_, "\n [PHInfo] ActiveBlk:%d Page:%d Empty", ubBlkCnt, uwPageInBlkPtr);
				}
				uwLastWritePage[ubBlkCnt] = uwPageInBlkPtr;
				break;
			}

			uwPageInBlkPtr++;

			if (uwPageInBlkPtr == (gulFastPagePlanesPerUnit / gubPlanesPerSuperPage)) {
				uwLastWritePage[ubBlkCnt] = uwPageInBlkPtr;
				break;
			}
		}
	}

	if (uwLastWritePage[1] == 0) {
		gPH.Info.B.btActiveBlk = 0;
	}
	else if (uwLastWritePage[0] == 0) {
		gPH.Info.B.btActiveBlk = 1;
	}
	else {
		gPH.Info.B.btActiveBlk = (uwLastWritePage[0] < uwLastWritePage[1]) ? 0 : 1;
		ubNeedErase = 1;
	}

	gPH.uwLastPage = uwLastWritePage[gPH.Info.B.btActiveBlk];
	gPH.uwMPLogPage = uwMPLogPage[gPH.Info.B.btActiveBlk];
	gPH.uwRDTLogPage = uwRDTLogPage[gPH.Info.B.btActiveBlk];
	gPH.uwECLogPage = uwECLogPage[gPH.Info.B.btActiveBlk];
#if VRLC_EN
	gPH.uwVRLCLogPage = uwVRLCLogPage[gPH.Info.B.btActiveBlk];
#endif /* VRLC_EN */
	gPH.Info.B.btScannedDone = TRUE;

	if (PH_UART_EN) {
		M_UART(VUC_, "\n [PHInfo] ActiveBlk:%d LastPage:%d ", gPH.Info.B.btActiveBlk, gPH.uwLastPage);
		M_UART(VUC_, "\n [PHInfo] MPLogPage:%d RDTLogPage:%d ", gPH.uwMPLogPage, gPH.uwRDTLogPage);
#if VRLC_EN
		M_UART(VRLC_DEBUG_, "\n [PHInfo] ECLogPage:%d VRLCLogPage:%d ", gPH.uwECLogPage, gPH.uwVRLCLogPage);
#endif /* VRLC_EN */
	}

	if (ubNeedErase) {
		ubNeedEraseTarget = gPH.Info.B.btActiveBlk ^ 0x1;
		ubBlkPtr = gPH.PHBlk[ubNeedEraseTarget].Info.ubBlockOrder;
		FlashInfo.ulBlock = ubBlkPtr / (PLANE_BANK_NUM);
		FlashInfo.ubChannel = gPH.PHBlk[ubNeedEraseTarget].Info.Channel;
		FlashInfo.ubFlashCE = gPH.PHBlk[ubNeedEraseTarget].Info.CEperCH;
		FlashInfo.ubLUN = 0;
		FlashInfo.ubPlane = ubBlkPtr % PLANE_BANK_NUM;
		// Erase Block
		BurnerEraseBLK(gFlhEnv.ubSLCMethod, MODE_D3, (FlashAccessInfo_t *)&FlashInfo, 0);
		BurnerWaitCQDone(guwBurnerBGTag);
	}
	gubLeavePreformatFlag = 1;

	M_INIT_SCAN_RESTORE_FIP_STOP();
}
#endif