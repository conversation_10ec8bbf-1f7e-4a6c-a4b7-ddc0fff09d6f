#ifndef _E21_RAIDECC_REG_H_
#define _E21_RAIDECC_REG_H_

#include "typedef.h"
#include "mem.h"
#include "symbol.h"

#define RAIDECC_REG_BASE					(BVCI_RAIDECC_REG_ADDRESS)
#define RAIDECC_BPT1_OFFSET					(BVCI_RAIDECC_BPT1_RAM_ADDRESS)
#define RAIDECC_INTERNAL_BUFFER_BASE		(BVCI_RAIDECC_INTERNAL_BUFFER_ADDRESS_BASE)

#define R8_RAIDECC							((volatile U8 *) RAIDECC_REG_BASE)
#define R16_RAIDECC							((volatile U16 *) RAIDECC_REG_BASE)
#define R32_RAIDECC							((volatile U32 *) RAIDECC_REG_BASE)
#define R64_RAIDECC							((volatile U64 *) RAIDECC_REG_BASE)

#define R8_RAIDECC_BPT1						((volatile U8 *) RAIDECC_BPT1_OFFSET)
#define R16_RAIDECC_BPT1					((volatile U16 *) RAIDECC_BPT1_OFFSET)
#define R32_RAIDECC_BPT1					((volatile U32 *) RAIDECC_BPT1_OFFSET)
#define R64_RAIDECC_BPT1					((volatile U64 *) RAIDECC_BPT1_OFFSET)

#define R32_RAIDECC_MODE_0					(0x0000 >> 2)
#define 	CPU_SEL_SHIFT						    (24)
#define		CPU_SEL_MASK						    (BIT_MASK(3))
#define 	RAIDECC_BMU_DEC_COR_SPR_SHIFT			(23)
#define 	RAIDECC_BMU_DEC_COR_SPR_MASK			(BIT_MASK(1))
#define		RAIDECC_COR_NORMAL_MODE_SHIFT		    (20)
#define	    RAIDECC_COR_NORMAL_MODE_MASK			(BIT_MASK(1))
#define	    RAIDECC_BPT_SEL_SHIFT					(16)
#define	    RAIDECC_BPT_SEL_MASK					(BIT_MASK(1))
#define	    RAIDECC_BMU_PRE_LOAD_EN_SHIFT			(14)
#define	    RAIDECC_BMU_PRE_LOAD_EN_MASK			(BIT_MASK(1))
#define 		SET_RAIDECC_BMU_PRE_LOAD_ENABLE     (0x1 << RAIDECC_BMU_PRE_LOAD_EN_SHIFT)
#define		RAIDECC_BMU_FORCE_LOAD_EN_SHIFT			(13)
#define		RAIDECC_BMU_FORCE_LOAD_EN_MASK			(BIT_MASK(1))
#define		RAIDECC_BMU_FW_FREE_SHIFT				(12)
#define		RAIDECC_BMU_FW_FREE_MASK				(BIT_MASK(1))
#define		RAIDECC_BMU_EN_SHIFT					(11)
#define		RAIDECC_BMU_EN_MASK						(BIT_MASK(1))
#define		RAIDECC_32B_EN_SHIFT					(10)
#define		RAIDECC_32B_EN_MASK						(BIT_MASK(1))
#define		RAIDECC_PAGESIZE_SHIFT					(8)
#define		RAIDECC_PAGESIZE_MASK					(BIT_MASK(2))
#define			SET_RAIDECC_4KB_PAGE             	(0x0 << RAIDECC_PAGESIZE_SHIFT)
#define 		SET_RAIDECC_8KB_PAGE             	(0x1 << RAIDECC_PAGESIZE_SHIFT)
#define 		SET_RAIDECC_12KB_PAGE            	(0x2 << RAIDECC_PAGESIZE_SHIFT)
#define 		SET_RAIDECC_16KB_PAGE            	(0x3 << RAIDECC_PAGESIZE_SHIFT)
#define		RAIDECC_PEC_CHK_EN_SHIFT				(7)
#define		RAIDECC_PEC_CHK_EN_MASK					(BIT_MASK(1))
#define		RAIDECC_TAG_CHK_VLD_AUTO_CLR_SHIFT		(6)
#define		RAIDECC_TAG_CHK_VLD_AUTO_CLR_MASK		(BIT_MASK(1))
#define		RAIDECC_PBUF_INIT_SHIFT					(2)
#define		RAIDECC_PBUF_INIT_MASK					(BIT_MASK(1))
#define		RAIDECC_PBUF_LOAD_SHIFT					(1)
#define		RAIDECC_PBUF_LOAD_MASK					(BIT_MASK(1))



#define R32_RAIDECC_EX_PB_SEL				(0x0094 >> 2)
#define	    RAIDECC_IN_PB_VLD_SHIFT             (26)
#define	    RAIDECC_IN_PB_VLD_MASK				(BIT_MASK(1))
#define	    RAIDECC_IN_PB_NUM_SHIFT				(24)
#define	    RAIDECC_IN_PB_NUM_MASK				(BIT_MASK(2))
#define	    RAIDECC_EX_PB_NUM_SHIFT				(16)
#define	    RAIDECC_EX_PB_NUM_MASK				(BIT_MASK(5))
#define	    RAIDECC_EX_PB_TAG_SHIFT				(0)
#define	    RAIDECC_EX_PB_TAG_MASK				(BIT_MASK(6))

#define R32_RAIDECC_ERR_INT_INFO			(0x00B8 >> 2)
#define 	PARITY_ERR_SEL_SHIFT			(30)
#define 	PARITY_ERR_SEL_MASK				(BIT_MASK(2))
#define 	PARITY_ERR_ADDR_SHIFT			(20)
#define 	PARITY_ERR_ADDR_MASK			(BIT_MASK(10))
#define 	ERR_FLAG_SHIFT					(0)
#define 	ERR_FLAG_MASK					(BIT_MASK(9))
#define 	RRESEP_ERR_FLAG					(BIT8)
#define 	BRESEP_ERR_FLAG					(BIT7)
#define 	BPT_PARITY_ERR_FLAG				(BIT6)
#define 	TAG_PARITY_ERR_FLAG				(BIT5)
#define 	SPR_PARITY_ERR_FLAG				(BIT4)
#define 	PBUF6_PARITY_ERR_FLAG			(BIT3)
#define 	PBUF4_PARITY_ERR_FLAG			(BIT2)
#define 	PBUF2_PARITY_ERR_FLAG			(BIT1)
#define 	PBUF0_PARITY_ERR_FLAG			(BIT0)

#define R32_RAIDECC_SWAP_DBASE				(0x00C0 >> 2)

#define R32_RAIDECC_SWAP_SBASE				(0x00C4 >> 2)

#define R32_RAIDECC_SWAP_DBASE_AND_SWAP_SBASE_EXT	(0x00C8 >> 2)
#define 	SWAP_SBASE_33_32_SHIFT					(8)
#define 	SWAP_SBASE_33_32_MASK					(BIT_MASK(2))
#define 	SWAP_DBASE_33_32_SHIFT					(0)
#define 	SWAP_DBASE_33_32_MASK					(BIT_MASK(2))

#define R32_RAIDECC_PBUF_SRAM_TEST_0		(0x00CC >> 2)
#define 	GOAL_DATA_SHIFT					(16)
#define 	GOAL_DATA_MASK					(BIT_MASK(16))
#define 	PBUF_SRAM_ERROR_FLAG_SHIFT		(8)
#define 	PBUF_SRAM_ERROR_FLAG_MASK		(BIT_MASK(4))
#define 	CHK_EN_SHIFT					(5)
#define 	CHK_EN_MASK						(BIT_MASK(1))
#define 		SET_CHK_EN					(SET_BIT5)
#define 	PBUF_SRAM_CHK_MODE_SHIFT		(4)
#define 	PBUF_SRAM_CHK_MODE_MSAK			(BIT_MASK(1))
#define 	PBUF_SRAM_CHK_TRIG_SHIFT		(0)
#define 	PBUF_SRAM_CHK_TRIG_MASK			(BIT_MASK(1))

#define R32_RAIDECC_PBUF_SRAM_TEST_1		(0x00D0 >> 2)
#define 	PBUF_SRAM_ERROR_ADDR_SHIFT		(16)
#define 	PBUF_SRAM_ERROR_ADDR_MASK		(BIT_MASK(9))
#define 	TEST_PBUF_BYTE_SEL_SHIFT		(8)
#define 	TEST_PBUF_BYTE_SEL_MASK			(BIT_MASK(4))
#define 	TEST_PBUF_SEL_SHIFT				(0)
#define 	TEST_PBUF_SEL_MASK				(BIT_MASK(4))

#define R32_RAIDECC_PBUF_SRAM_TEST_2		(0x00D4 >> 2)
#define 	PBUF_SRAM_INSERT_ERR_ADDR_SHIFT	(16)
#define 	PBUF_SRAM_INSERT_ERR_ADDR_MASK	(BIT_MASK(11))
#define 	PBUF_SRAM_INSERT_ERROR_SHIFT	(8)
#define 	PBUF_SRAM_INSERT_ERROR_MASK		(BIT_MASK(8))

#define R32_RAIDECC_PBUF_SRAM_TEST_3		(0x00D8 >> 2)
#define 	PBUF_TEST_LOOP_SHIFT			(0)
#define 	PBUF_TEST_LOOP_MASL				(BIT_MASK(32))

#define R32_RAIDECC_TAG_SEL_1				(0x00DC >> 2)
#define 	RAIDECC_TAG_SEL_1_SHIIFT		(0)
#define 	RAIDECC_TAG_SEL_1_MASK			(BIT_MASK(9))

#define R32_RAIDECC_TAG_RD_CONFIG_1				(0x00E0 >> 2)
#define 	RAIDECC_TAG_PEC_1_SHIFT				(24)
#define 	RAIDECC_TAG_PEC_1_MASK				(BIT_MASK(8))
#define 	RAIDECC_TAG_PRC_1_SHIFT				(16)
#define 	RAIDECC_TAG_PRC_1_MASK				(BIT_MASK(8))
#define 	RAIDECC_TAG_VALID_1_SHIFT			(11)
#define 	RAIDECC_TAG_VALID_1_MASK			(BIT_MASK(1))
#define 	RAIDECC_TAG_PRF_1_SHIFT				(10)
#define 	RAIDECC_TAG_PRF_1_MASK				(BIT_MASK(1))
#define 	SET_RAIDECC_TAG_VALID_BUSY_SHIFT	(9)
#define 	SET_RAIDECC_TAG_VALID_BUSY_MASK		(BIT_MASK(1))
#define 	RAIDECC_TAG_SEL_1_SHIFT				(0)
#define 	RAIDECC_TAG_SEL_1_MASK				(BIT_MASK(9))

#define RAIDECC_PBUF_SRAM_TEST_4				(0x00E4 >> 2)
#define 	PBUF_TEST_LAT_DATA_SHIFT			(0)
#define 	PBUF_TEST_LAT_DATA_MASK				(32)

#endif /* _E21_RAIDECC_REG_H_ */
