/**************************************************************************/
/*                                                                        */
/*           Copyright (c) 2000-2017 by Phison Electronics Corp.          */
/*                                                                        */
/**************************************************************************/

/**************************************************************************/
/*                                                                        */
/*  HAL UART DEFINITION                                    RELEASE        */
/*                                                                        */
/*    shr_hal_nvme_cmgr_reg.h                               GNU C         */
/*                                                           X.X          */
/*  AUTHOR                                                                */
/*                                                                        */
/*                                                                        */
/*  DESCRIPTION                                                           */
/*                                                                        */
/*                                                                        */
/*                                                                        */
/*  RELEASE HISTORY                                                       */
/*                                                                        */
/*    DATE              NAME                      DESCRIPTION             */
/*                                                                        */
/*                                                                        */
/**************************************************************************/
#if (NVME == HOST_MODE)
#ifndef _SHR_HAL_NVME_CMGR_REG_H_
#define _SHR_HAL_NVME_CMGR_REG_H_

#if 0 //merge@@
#include "misc/shr_def.h"
#define NVME_CMGR_REGISTER_ADDRESS        (NVME_CMGR_REGISTER_ADDRESS)
#else
#include "common/symbol.h"
#include "common/math_op.h"
#include "hal/sys/api/clk/clk_api.h"
#if (PS5021_EN)
#include "hal/nvme/nvme_cmgr_reg_5021.h"
#else /* (PS5021_EN) */
#include "hal/nvme/nvme_cmgr_reg_5013.h"
#endif /* (PS5021_EN) */
#define NVME_CMGR_REGISTER_ADDRESS        (NVME_REG_ADDRESS)
#endif

#if (PS5021_EN)
#define R8_NVME_CMGR_REG					((REG8 *)NVME_CMGR_REGISTER_ADDRESS) // 0xF8002000
#define R16_NVME_CMGR_REG					((REG16 *)NVME_CMGR_REGISTER_ADDRESS)
#define R32_NVME_CMGR_REG					((REG32 *)NVME_CMGR_REGISTER_ADDRESS)
#define R64_NVME_CMGR_REG					((REG64 *)NVME_CMGR_REGISTER_ADDRESS)

#define R8_NVME_CMGR_RAM					((REG8 *)NVME_CMGR_RAM_ADDRESS) // 0x40200000
#define R16_NVME_CMGR_RAM					((REG16 *)NVME_CMGR_RAM_ADDRESS)
#define R32_NVME_CMGR_RAM					((REG32 *)NVME_CMGR_RAM_ADDRESS)
#define R64_NVME_CMGR_RAM					((REG64 *)NVME_CMGR_RAM_ADDRESS)
#endif /*PS5021_EN*/

#define r8_CMGR                             ((REG8 *)NVME_CMGR_REGISTER_ADDRESS) // 0x00CC2000
#define r16_CMGR                            ((REG16 *)NVME_CMGR_REGISTER_ADDRESS)
#define r32_CMGR                            ((REG32 *)NVME_CMGR_REGISTER_ADDRESS)
#define r64_CMGR                            ((REG64 *)NVME_CMGR_REGISTER_ADDRESS)


/*=========================  CMGR interrupt  ================================*/
#define     CMGR_INT_EN_ALL              (0xFFFFFFFF)
#define     NVME_ENABLE_CMGR_INT()       (r32_CMGR[R32_NVME_CMGR_INT_EN] = (CMGR_INT_EN_ALL & (~CMGR_LOCK)))

#define     NVME_CHK_CMGR_LOCK_INT()            (r32_CMGR[R32_NVME_CMGR_INT] & CMGR_LOCK)
#define     NVME_CHK_CMGR_IDLE_INT()            (r32_CMGR[R32_NVME_CMGR_INT] & CMGR_IDLE_INT)
#define     NVME_CHK_CMGR_R_CMD_ERR_INT()       (r32_CMGR[R32_NVME_CMGR_INT] & APU_R_CMD_ERR)
#define     NVME_CHK_CMGR_W_CMD_ERR_INT()       (r32_CMGR[R32_NVME_CMGR_INT] & APU_W_CMD_ERR)
#define     NVME_CHK_CMGR_R_UNC_ERR_INT()       (r32_CMGR[R32_NVME_CMGR_INT] & APU_R_UNC_ERR)
#define     NVME_CHK_CMGR_PNSRAM_PERR_INT()     (r32_CMGR[R32_NVME_CMGR_INT] & PNSRAM_PERR)
#define     NVME_CHK_CMGR_CTSRAM_PERR_INT()     (r32_CMGR[R32_NVME_CMGR_INT] & CTSRAM_PERR)
#define     NVME_CHK_CMGR_GPSRAM_PERR_INT()     (r32_CMGR[R32_NVME_CMGR_INT] & GPSRAM_PERR)
#define     NVME_CHK_NLOG_BUSY_OV_INT()         (r32_CMGR[R32_NVME_CMGR_INT] & NLOG_BUSY_OV)
#define     NVME_CHK_NLOG_RDAT_OV_INT()         (r32_CMGR[R32_NVME_CMGR_INT] & NLOG_RDAT_OV)
#define     NVME_CHK_NLOG_RCMD_OV_INT()         (r32_CMGR[R32_NVME_CMGR_INT] & NLOG_RCMD_OV)
#define     NVME_CHK_NLOG_WDAT_OV_INT()         (r32_CMGR[R32_NVME_CMGR_INT] & NLOG_WDAT_OV)
#define     NVME_CHK_NLOG_WCMD_OV_INT()         (r32_CMGR[R32_NVME_CMGR_INT] & NLOG_WCMD_OV)
#if (PS5021_EN)
#define     NVME_CHK_CMGR_CVLD_ERR()         	(r32_CMGR[R32_NVME_CMGR_INT] & CLR_CVLD_ERR)
#endif /* PS5021_EN */

#define     NVME_CLR_CMGR_LOCK_INT()            (r32_CMGR[R32_NVME_CMGR_INT] = CMGR_LOCK)
#define     NVME_CLR_CMGR_IDLE_INT()            (r32_CMGR[R32_NVME_CMGR_INT] = CMGR_IDLE_INT)
#define     NVME_CLR_CMGR_RERR_INT()            (r32_CMGR[R32_NVME_CMGR_INT] = APU_R_CMD_ERR)
#define     NVME_CLR_CMGR_WERR_INT()            (r32_CMGR[R32_NVME_CMGR_INT] = APU_W_CMD_ERR)
#define     NVME_CLR_CMGR_R_UNC_ERR_INT()       (r32_CMGR[R32_NVME_CMGR_INT] = APU_R_UNC_ERR)
#define     NVME_CLR_CMGR_PNSRAM_PERR_INT()     (r32_CMGR[R32_NVME_CMGR_INT] = PNSRAM_PERR)
#define     NVME_CLR_CMGR_CTSRAM_PERR_INT()     (r32_CMGR[R32_NVME_CMGR_INT] = CTSRAM_PERR)
#define     NVME_CLR_CMGR_GPSRAM_PERR_INT()     (r32_CMGR[R32_NVME_CMGR_INT] = GPSRAM_PERR)
#define     NVME_CLR_NLOG_BUSY_OV_INT()         (r32_CMGR[R32_NVME_CMGR_INT] = NLOG_BUSY_OV)
#define     NVME_CLR_NLOG_RDAT_OV_INT()         (r32_CMGR[R32_NVME_CMGR_INT] = NLOG_RDAT_OV)
#define     NVME_CLR_NLOG_RCMD_OV_INT()         (r32_CMGR[R32_NVME_CMGR_INT] = NLOG_RCMD_OV)
#define     NVME_CLR_NLOG_WDAT_OV_INT()         (r32_CMGR[R32_NVME_CMGR_INT] = NLOG_WDAT_OV)
#define     NVME_CLR_NLOG_WCMD_OV_INT()         (r32_CMGR[R32_NVME_CMGR_INT] = NLOG_WCMD_OV)
#if (PS5021_EN)
#define     NVME_CLR_CMGR_CVLD_ERR()         	(r32_CMGR[R32_NVME_CMGR_INT] = CLR_CVLD_ERR)
#endif /* PS5021_EN */

/*=========================  CMGR Control  ================================*/

#if (PS5021_EN)
#define     NVME_SET_CMGR_CTAG_EOFST(x)         	(r32_CMGR[R32_NVME_CMGR_CTRL_0] = (x & CMGR_CTAG_EOFST))
#endif /* PS5021_EN */

#define 	AUTO_LOCK_EN                    	(CMGR_AUTO_LOCK_EN_BIT)
#define 	AES_CHK_EN							(CMGR_AES_CHK_EN_BIT)
#define 	CID_SCAN_EN							(CMGR_CID_SCAN_EN_BIT)
#define 	IOR_ACPL_EN							(CMGR_IOR_ACPL_EN_BIT)
#define 	SRAM_AUTO_LS						(CMGR_SRAM_AUTO_LS_EN_BIT)
#define 	DB_ACTIVE							(CMGR_DB_ACTIVE_BIT)
#define 	IOR_NON_PNSRAM						(CMGR_IOR_SYNC_BIT)
#define   SET_DB_ACTIVE()                      	(r8_CMGR[R8_NVME_CMGR_CTRL]|= CMGR_DB_ACTIVE_BIT)
#define   CLR_DB_ACTIVE()                      	(r8_CMGR[R8_NVME_CMGR_CTRL]&= ~(CMGR_DB_ACTIVE_BIT))
#define   SET_AES_ACTIVE()                     	(r8_CMGR[R8_NVME_CMGR_CTRL]|=CMGR_AES_CHK_EN_BIT)
#define   CLR_AES_ACTIVE()                     	(r8_CMGR[R8_NVME_CMGR_CTRL]&=~(CMGR_AES_CHK_EN_BIT))
#define   CMGR_SET_IOR_SYNC()  	               	(r8_CMGR[R8_NVME_CMGR_CTRL] |= CMGR_IOR_SYNC_BIT)
#define   CMGR_CLR_IOR_SYNC()  	               	(r8_CMGR[R8_NVME_CMGR_CTRL] &= ~(CMGR_IOR_SYNC_BIT))
#define	  CMGR_SET_SRAM_AUTO_LS()				(r8_CMGR[R8_NVME_CMGR_CTRL] |= (CMGR_SRAM_AUTO_LS_EN_BIT))
#define	  CMGR_CLR_SRAM_AUTO_LS()				(r8_CMGR[R8_NVME_CMGR_CTRL] &= (~CMGR_SRAM_AUTO_LS_EN_BIT))
#define	  HAL_SET_NVME_CMGR_CTRL(x)			   	(r8_CMGR[R8_NVME_CMGR_CTRL]|= (x))
#define	  HAL_CLR_NVME_CMGR_CTRL(x)				(r8_CMGR[R8_NVME_CMGR_CTRL]&= ~(x))

#define 	R_DMA_ERR_LOCK_EN              		(APU_R_CMD_ERR_LOCK_EN_BIT)
#define 	W_DMA_ERR_LOCK_EN               	(APU_W_CMD_ERR_LOCK_EN_BIT)
#define 	R_UNC_LOCK_EN                   	(APU_R_UNC_LOCK_EN_BIT)
#define 	SPAR_CHK_EN                     	(APU_SPAR_CHK_EN_BIT)
#define 	PAR_CHK_EN                      	(APU_PAR_CHK_EN_BIT)
#define 	APU_DRU_ERR_INJ                    	(APU_DRU_ERR_INJ_BIT)
#define 	APU_DWU_ERR_INJ                    	(APU_DWU_ERR_INJ_BIT)
#define	HAL_SET_NVME_CMGR_ERR_CTRL(x)		(r32_CMGR[R32_NVME_CMGR_ERR_CTRL] |= (x))

#define		CMGR_SET_DLY_WR_TMR(x)			(r16_CMGR[R16_NVME_CMGR_APU_DLY_WR_TMR] = x)
#define		M_NVME_CMD_MANAGER_GET_DELAY_WRITE_TIMER()			(r16_CMGR[R16_NVME_CMGR_APU_DLY_WR_TMR] )

#define		CMGR_SET_DLY_RD_TMR(x)			(r16_CMGR[R16_NVME_CMGR_APU_DLY_RD_TMR] = x)
#define		M_NVME_CMD_MANAGER_GET_DELAY_READ_TIMER()			(r16_CMGR[R16_NVME_CMGR_APU_DLY_RD_TMR])

#define   CMGR_SET_DLY_UNIT(N)                 (r16_CMGR[R16_NVME_CMGR_APU_DLY_CTRL] = N)
#if PS5021_EN
#define	  NVME_CMD_MANAGER_DELAY_UNIT			(ClockGetSysClkMHz() / 2)	// MHz/4:500ns,  MHz/2:1us,  (MHz/2)*10:10us
#else /* PS5021_EN */
#define	  NVME_CMD_MANAGER_DELAY_UNIT			(ClockGetIPClockMHz(CLOCK_CENTER_0, CLOCK_IP_0) / 4)	// MHz/4:500ns,  MHz/2:1us,  (MHz/2)*10:10us
#endif /* PS5021_EN */

#define		CMGR_SET_WR_DLY_EN()				(r8_CMGR[R8_NVME_CMGR_APU_DLY_CTRL] |= APU_DLY_WR_EN_BIT)
#define		CMGR_CLR_WR_DLY_EN()				(r8_CMGR[R8_NVME_CMGR_APU_DLY_CTRL] &= ~APU_DLY_WR_EN_BIT)
#define		CMGR_SET_RD_DLY_EN()				(r8_CMGR[R8_NVME_CMGR_APU_DLY_CTRL] |= APU_DLY_RD_EN_BIT)
#define		CMGR_CLR_RD_DLY_EN()				(r8_CMGR[R8_NVME_CMGR_APU_DLY_CTRL] &= ~APU_DLY_RD_EN_BIT)

#define 	CMGR_SET_MDTS(x)					(r8_CMGR[R8_NVME_CMGR_MDTS] = (x))
#define 	CMGR_GET_MDTS()						(r8_CMGR[R8_NVME_CMGR_MDTS])

#if (PS5021_EN)
#define CMGR_SET_MPS(FID, val)          			(R32_NVME_CMGR_REG[R32_NVME_CMGR_CFG0 + (FID / 8)] |= (((U32)val & BITMSK(4,0)) << (4 * (FID % 8))))
#define CMGR_CLR_MPS(FID)                         (R32_NVME_CMGR_REG[R32_NVME_CMGR_CFG0 + (FID / 8)] &= (~(((U32)BITMSK(4,0)) << (4 * (FID % 8)))))
#else /* (PS5021_EN) */
#define 	CMGR_SET_MPS(x)					(r8_CMGR[R8_NVME_CMGR_MPS] = (x))
#endif /* (PS5021_EN) */

#define NLOG_SET_MS_UNIT(x)				(r32_CMGR[R32_NVME_CMGR_NLOG_MS_UNIT]= x)

#define 	CMGR_CLR_IDLE_TMR() 				(r32_CMGR[R32_NVME_CMGR_IDLE_TMR] = 0)
#define 	CMGR_GET_IDLE_TMR() 				(r32_CMGR[R32_NVME_CMGR_IDLE_TMR])

#define   CMGR_SET_IDLE_TMR_THR(N)             (r32_CMGR[R32_NVME_CMGR_IDLE_TMR_THR] = N)

#define 	CMGR_SET_IDLE_TMR_EN()            do {\
	CPUARMSetCriticalRegisterBit((r32_CMGR + R32_NVME_CMGR_IDLE_TMR_CTRL), CMGR_IDLE_TMR_EN_BIT);\
} while(0)
#define 	CMGR_CLR_IDLE_TMR_EN()            do {\
	CPUARMClearCriticalRegisterBit((r32_CMGR + R32_NVME_CMGR_IDLE_TMR_CTRL), CMGR_IDLE_TMR_EN_BIT);\
} while(0)

#if (PS5021_EN)
#define NLOG_GET_SYS_CLOCK_CNT(FID,x)		(x=r32_CMGR[(R32_NVME_CMGR_NLOG_SYS_CNT + FID * R64_NVME_CMGR_NLOG_ENTRY_SIZE)])
#define NLOG_SET_SYS_CLOCK_CNT(FID,x)		(r32_CMGR[(R32_NVME_CMGR_NLOG_SYS_CNT + FID * R64_NVME_CMGR_NLOG_ENTRY_SIZE)]= x)
#else /* (PS5021_EN) */
#define NLOG_GET_SYS_CLOCK_CNT(x)		(x=r32_CMGR[R32_NVME_CMGR_NLOG_SYS_CNT])
#define NLOG_SET_SYS_CLOCK_CNT(x)		(r32_CMGR[R32_NVME_CMGR_NLOG_SYS_CNT]= x)
#endif /* (PS5021_EN) */

#if (PS5021_EN)
// F800_2084h
#define CMGR_SET_FW_DEL_CMD_REQ()       		(r32_CMGR[R32_NVME_CMGR_FW_DEL_CMD] |= CMGR_FW_DEL_CMD_REQ)
#define CMGR_CHECK_FW_DEL_CMD_REQ()       		(r32_CMGR[R32_NVME_CMGR_FW_DEL_CMD] & CMGR_FW_DEL_CMD_REQ)
#define CMGR_CHECK_PROC_CMD_ACT()    			(r32_CMGR[R32_NVME_CMGR_FW_DEL_CMD] & CMGR_PROC_CMD_ACT)
#define CMGR_CHECK_FCF_INFO_VALID()         		(r32_CMGR[R32_NVME_CMGR_FW_DEL_CMD] & CMGR_FCF_INFO_VLD)
#define CMGR_CLR_VALID_CTAG(x)				(r32_CMGR[R32_NVME_CMGR_FW_CLR_CTAG] = ((x) & CMGR_FW_CLR_CVLD_CTAG_MASK))
#define CMGR_GET_FW_DEL_CMD_CTAG()      	((r32_CMGR[R32_NVME_CMGR_FW_DEL_CMD] & CMGR_FW_DEL_CMD_CTAG) >> CMGR_FW_DEL_CMD_CTAG_SHIFT)
// F800_2088h
#define	CMGR_CHK_CLR_CVLD_REQ()			(r32_CMGR[R32_NVME_CMGR_FW_CLR_CTAG] & CMGR_FW_CLR_CVLD_REQ_BIT)
#define	CLR_CVLD_REG_ADDR                 (r32_CMGR[R32_NVME_CMGR_FW_CLR_CTAG])

#else /* (PS5021_EN) */
#define CMGR_CLR_VALID_CTAG(x)				(r8_CMGR[R8_NVME_CMGR_FW_CLR_CVLD_CTAG] = ((x) & CMGR_FW_CLR_CVLD_CTAG_MASK))
#define	CMGR_CHK_CLR_CVLD_REQ()				(r8_CMGR[R8_NVME_CMGR_FW_CLR_CVLD_REG] & CMGR_FW_CLR_CVLD_REQ_BIT)
#define CLR_CVLD_REG_ADDR                 (r8_CMGR[R8_NVME_CMGR_FW_CLR_CVLD_REG])
#endif /* (PS5021_EN) */
#define CMGR_WAIT_REG(x)                    WAIT_REG(CLR_CVLD_REG_ADDR, CMGR_FW_CLR_CVLD_REQ_BIT,x)

#define		CMGR_SET_APU_RST()		  (r32_CMGR[R32_NVME_CMGR_RST]|= CMGR_APU_RST_BIT)
#define		CMGR_CLR_APU_RST()		  (r32_CMGR[R32_NVME_CMGR_RST]&= (~CMGR_APU_RST_BIT))
#define 	CMGR_SET_APU_PAUSE()      (r32_CMGR[R32_NVME_CMGR_RST]|= CMGR_APU_PAUSE_BIT)
#define 	CMGR_CLR_APU_PAUSE()      (r32_CMGR[R32_NVME_CMGR_RST] &= (~CMGR_APU_PAUSE_BIT))
#define		CMGR_SET_APU_LSI_RST()	  (r32_CMGR[R32_NVME_CMGR_RST]|= CMGR_APU_LSI_RST_BIT)
#define		CMGR_CLR_APU_LSI_RST()	  (r32_CMGR[R32_NVME_CMGR_RST]&= (~CMGR_APU_LSI_RST_BIT))
#define		CMGR_CHK_APU_LSI_RST()	  (r32_CMGR[R32_NVME_CMGR_RST] & (CMGR_APU_LSI_RST_BIT))

#if (PS5021_EN)
#define   CMGR_GET_NVME_QCNT()          	(r16_CMGR[R16_NVME_CMGR_NVME_QCNT])
#define   M_CMGR_GET_NVME_QCNT_FULL()          	(r16_CMGR[R16_NVME_CMGR_NVME_QCNT] == 0x200)
#else /* (PS5021_EN) */
#define   CMGR_GET_NVME_QCNT()          	(r8_CMGR[R8_NVME_CMGR_NVME_QCNT])
#define   M_CMGR_GET_NVME_QCNT_FULL()          	(r8_CMGR[R8_NVME_CMGR_NVME_QCNT] == 0x80)
#endif /* (PS5021_EN) */

#if (PS5021_EN)
#define   CMGR_GET_APU_UNC_CTAG()   ((r32_CMGR[R32_NVME_CMGR_APU_UNC_CTAG] & APU_UNC_CTAG) >> APU_UNC_CTAG_SHIFT)
#define	  CMGR_GET_APU_UNC_LBA()	(r64_CMGR[R64_NVME_CMGR_APU_UNC_LBA] & APU_UNC_LBA_MASK)
#define   CMGR_GET_APU_UNC_NSID()   ((r32_CMGR[R32_NVME_CMGR_APU_UNC_NSID] & APU_UNC_NSID) >> APU_UNC_NSID_SHIFT)
#else /* (PS5021_EN) */
#define   CMGR_GET_APU_UNC_CTAG()   (r8_CMGR[R8_APU_UNC_CTAG])
#define   CMGR_GET_APU_UNC_LBA()      (r32_CMGR[R32_NVME_CMGR_APU_UNC_LBA])
#define   CMGR_GET_APU_UNC_NSID()      (r32_CMGR[R32_NVME_CMGR_APU_UNC_NSID])
#endif /* (PS5021_EN) */

// #define   RD_ERR_INFO_W32                     (0x00000070 >> 2)
#define 	RD_UNC_ERR                      	(RLS_UNC_ERR_BIT)
#define   CMGR_GET_RD_ERR_INFO()				(r32_CMGR[R32_NVME_CMGR_RERR_INFO])
#define	  CMGR_GET_RD_ERR_CTAG()				(r32_CMGR[R32_NVME_CMGR_RERR_INFO] & RLS_ERR_CTAG)
#define   CMGR_CHK_RD_APU_PAR_ERRR()          	(r32_CMGR[R32_NVME_CMGR_RERR_INFO] & RLS_APU_PAR_ERR_BIT)
#define   CMGR_CHK_RD_PRP_ERR()               	(r32_CMGR[R32_NVME_CMGR_RERR_INFO] & RLS_PRP_ERR_BIT)
#define   CMGR_CHK_RD_PCIE_AXI_ERR()          	(r32_CMGR[R32_NVME_CMGR_RERR_INFO] & RLS_PCIE_AXI_ERR_BIT)
#define   CMGR_CHK_RD_SYS_AXI_ERRR()          	(r32_CMGR[R32_NVME_CMGR_RERR_INFO] & RLS_SYS_AXI_ERR_BIT)
#define   CMGR_CHK_RD_E3D_ERR()          	    (r32_CMGR[R32_NVME_CMGR_RERR_INFO] & RLS_E3D_ERR_BIT)
#define   CMGR_CHK_RD_UNC_ERR()               	(r32_CMGR[R32_NVME_CMGR_RERR_INFO] & RLS_UNC_ERR_BIT)

#define   CMGR_GET_WR_ERR_INFO()              	(r32_CMGR[R32_NVME_CMGR_WERR_INFO])
#define   CMGR_GET_WR_ERR_CTAG()              	(r32_CMGR[R32_NVME_CMGR_WERR_INFO] & WLS_ERR_CTAG)
#define   CMGR_CHK_WR_PRP_ERR()                 (r32_CMGR[R32_NVME_CMGR_WERR_INFO] & WLS_PRP_ERR_BIT)
#define   CMGR_CHK_WLS_PCIE_PAR_ERR()           (r32_CMGR[R32_NVME_CMGR_WERR_INFO] & WLS_PCIE_PAR_ERR_BIT)
#define   CMGR_CHK_WLS_PCI_ERR()                (r32_CMGR[R32_NVME_CMGR_WERR_INFO] & WLS_PCI_ERR_BIT)

/*
#define CMGR_FIFO_VLD                                (0x620)//- Indicate the command fifo valid.
#define     APU_CHK_CMD_FIFO_VLD(N)                      (r8_APU[APUB_CMD_FIFO_VLD+(((U32)N) >> 3)] & BIT(N % 8))
#define     APU_GET_CMD_FIFO_VLD(N)                      (r32_APU[(APUB_CMD_FIFO_VLD >> 2)+((U32)N)])//- Indicate the command fifo valid.
#define     APU_CMD_FIFO_VLD_NUM                         (8)//256/32
*/

#define	   CMGR_GET_FCF_CMD_DWx(x)	  (r32_CMGR[R32_NVME_CMGR_FCF_CMD_DW0 + (x)])
#define    CMGR_GET_FCF_CMD_DW0()     (r32_CMGR[R32_NVME_CMGR_FCF_CMD_DW0])
#define    CMGR_GET_FCF_CMD_DW1()     (r32_CMGR[R32_NVME_CMGR_FCF_CMD_DW1])
#define    CMGR_GET_FCF_CMD_DW2()     (r32_CMGR[R32_NVME_CMGR_FCF_CMD_DW2])
#define    CMGR_GET_FCF_CMD_DW3()     (r32_CMGR[R32_NVME_CMGR_FCF_CMD_DW3])
#define    CMGR_GET_FCF_CMD_DW4()     (r32_CMGR[R32_NVME_CMGR_FCF_CMD_DW4])
#define    CMGR_GET_FCF_CMD_DW5()     (r32_CMGR[R32_NVME_CMGR_FCF_CMD_DW5])
#define    CMGR_GET_FCF_CMD_DW6()     (r32_CMGR[R32_NVME_CMGR_FCF_CMD_DW6])
#define    CMGR_GET_FCF_CMD_DW7()     (r32_CMGR[R32_NVME_CMGR_FCF_CMD_DW7])
#define    CMGR_GET_FCF_CMD_DW8()     (r32_CMGR[R32_NVME_CMGR_FCF_CMD_DW8])
#define    CMGR_GET_FCF_CMD_DW9()     (r32_CMGR[R32_NVME_CMGR_FCF_CMD_DW9])
#define    CMGR_GET_FCF_CMD_DW10()    (r32_CMGR[R32_NVME_CMGR_FCF_CMD_DW10])
#define    CMGR_GET_FCF_CMD_DW11()    (r32_CMGR[R32_NVME_CMGR_FCF_CMD_DW11])
#define    CMGR_GET_FCF_CMD_DW12()    (r32_CMGR[R32_NVME_CMGR_FCF_CMD_DW12])
#define    CMGR_GET_FCF_CMD_DW13()    (r32_CMGR[R32_NVME_CMGR_FCF_CMD_DW13])
#define    CMGR_GET_FCF_CMD_DW14()    (r32_CMGR[R32_NVME_CMGR_FCF_CMD_DW14])
#define    CMGR_GET_FCF_CMD_DW15()    (r32_CMGR[R32_NVME_CMGR_FCF_CMD_DW15])

#define 	FCF_GET_SQID()                  	(r32_CMGR[R32_NVME_CMGR_FCF_ERR_INFO] & CMGR_FCF_SQID)
#if (PS5021_EN)
#define		FCF_GET_FID							((r32_CMGR[R32_NVME_CMGR_FCF_ERR_INFO] & CMGR_FCF_FID) >> CMGR_FCF_FID_SHIFT)
#endif /* PS5021_EN */
#define 	FCF_GET_ERR_CODE()					(r32_CMGR[R32_NVME_CMGR_FCF_ERR_INFO] & BITMSK(16,16))
#define 	FCF_GET_CMD_ERR_INFO()				(r32_CMGR[R32_NVME_CMGR_FCF_ERR_INFO]);


#define 	PRP2_ERR					 CMD_PRP2_ERR_BIT
#define 	PRP1_ERR                     CMD_PRP1_ERR_BIT
#if (PS5021_EN)
#define		LOCK_ERR					 CMD_LOCK_ERR_BIT
#else /* (PS5021_EN) */
#define	 	STRID_ERR                    CMD_STRID_ERR_BIT
#endif /* (PS5021_EN) */
#define	 	LBA_ERR                      CMD_LBA_ERR_BIT
#define	 	WP_ERR                       CMD_WP_ERR_BIT
#define	 	DSM_ERR                      CMD_DSM_ERR_BIT
#define	 	PSDT_ERR                     CMD_PSDT_ERR_BIT
#define	 	FUSE_ERR                     CMD_FUSE_ERR_BIT
#define	 	MDTS_ERR                     CMD_MDTS_ERR_BIT
#define	 	AES_ERR                      CMD_AES_ERR_BIT
#define	 	CID_ERR                      CMD_CID_ERR_BIT
#define	 	NS_ERR                       CMD_NS_ERR_BIT
#define    	PRP_FETCH_AXI_ERR            PRP_FETCH_AXI_ERR_BIT
#define    	PRP_INVALD_OFFSET_ERR        PRP_INVALD_OFFSET_ERR_BIT
#define    	FCF_FETCH_ERR                CMD_NL_ERR_BIT
#define	  	DTYPE_ERR                    CMD_DTYPE_ERR_BIT

#if (PS5021_EN)
#define CMGR_GET_FCF_FID()                  ((r32_CMGR[R32_NVME_CMGR_FCF_ERR_INFO] & CMGR_FCF_FID) >> CMGR_FCF_FID_SHIFT)
#endif /* (PS5021_EN) */

/*================================= Write DMA temporary PRP ================================*/
// #define WCH_PRP_OFFSET                      (0x800)         // total 0 ~ 31 entry
// #define WCH_PRP_ENTRY_W64                   ((REG64 *)(NVME_CMGR_REGISTER_ADDRESS+WCH_PRP_OFFSET))

/*================================= Namespace Information ================================*/

//typedef volatile struct ns_info             * NSINFO_PTR;	  // total 0 ~ 7 NS Block
//#define gpNSinfo                            ((NSINFO_PTR)(NVME_CMGR_REGISTER_ADDRESS+NS_INFO_OFFSET))

#define NS_INFO_OFFSET                      (0x900)
/*
typedef struct ns_info          NVMENSINFO, *PNVMENSINFO;

struct ns_info {

	// SQ Descriptor
	union {

		U32 ulDW[2];

		struct __attribute__((packed)){

			U64 ulNSSize	 :40;
			U32 ubNSID   	 : 4;	 // 1-based
			U32 ubNS_LBAF 	 : 1;
			U32 ubNS_WP 	 : 1;
			U32 ubNS_ACT 	 : 1;
			U32 ubResv  	 : 1;
			U8  ubStrId_En      ;
			U8  ubResv1  	    ;
		} elem ;

	} NS_DESP;

};
*/

#if (PS5021_EN)
#define NS_CLR_NS_INFO_L(NSID)              (R64_NVME_CMGR_RAM[R64_NVME_CMGR_GPSRAM_NS_INFO_L + ((NSID-1) * QW_OFFSET_10H)] = 0)
#define NS_CLR_NS_INFO_H(NSID)              (R64_NVME_CMGR_RAM[R64_NVME_CMGR_GPSRAM_NS_INFO_H + ((NSID-1) * QW_OFFSET_10H)] = 0)
#define NS_CLR_NS_INFO(NSID)                do { \
												NS_CLR_NS_INFO_L(NSID); \
												NS_CLR_NS_INFO_H(NSID); \
											} while(0)
#define NS_SET_SIZE(NSID,x)                 (R64_NVME_CMGR_RAM[R64_NVME_CMGR_GPSRAM_NS_INFO_L + ((NSID-1) * QW_OFFSET_10H)] |= (((U64)x) & GPSRAM_NS_SIZE))
#define NS_SET_AFID(NSID,FID)               (R64_NVME_CMGR_RAM[R64_NVME_CMGR_GPSRAM_NS_INFO_L + ((NSID-1) * QW_OFFSET_10H)] |= ((U64)(( (1 << FID) & GPSRAM_NS_AFID_MASK) << GPSRAM_NS_AFID_SHIFT)))
#define NS_SET_NSID(NSID,x)                 (R64_NVME_CMGR_RAM[R64_NVME_CMGR_GPSRAM_NS_INFO_H + ((NSID-1) * QW_OFFSET_10H)] |= (((U64)x) & GPSRAM_NSID))
#define NS_SET_LBAF4K(NSID)                 (R64_NVME_CMGR_RAM[R64_NVME_CMGR_GPSRAM_NS_INFO_H + ((NSID-1) * QW_OFFSET_10H)] |= GPSRAM_NS_LBAF_BIT)
#define NS_SET_LBAF512(NSID)                (R64_NVME_CMGR_RAM[R64_NVME_CMGR_GPSRAM_NS_INFO_H + ((NSID-1) * QW_OFFSET_10H)]  &= (~GPSRAM_NS_LBAF_BIT))
#define NS_SET_WP(NSID)   	                (R64_NVME_CMGR_RAM[R64_NVME_CMGR_GPSRAM_NS_INFO_H + ((NSID-1) * QW_OFFSET_10H)] |= GPSRAM_NS_WP_BIT)
#define NS_CLR_WP(NSID)     	            (R64_NVME_CMGR_RAM[R64_NVME_CMGR_GPSRAM_NS_INFO_H + ((NSID-1) * QW_OFFSET_10H)] &= (~GPSRAM_NS_WP_BIT))
#define NS_SET_ACT(NSID)                    (R64_NVME_CMGR_RAM[R64_NVME_CMGR_GPSRAM_NS_INFO_H + ((NSID-1) * QW_OFFSET_10H)] |= GPSRAM_NS_ACT_BIT)
#define NS_CLR_ACT(NSID)                    (R64_NVME_CMGR_RAM[R64_NVME_CMGR_GPSRAM_NS_INFO_H + ((NSID-1) * QW_OFFSET_10H)] &= (~GPSRAM_NS_ACT_BIT))

#define NS_GET_SIZE(NSID,x)                 ( (x) = ((R64_NVME_CMGR_RAM[R64_NVME_CMGR_GPSRAM_NS_INFO_L + ((NSID-1) * QW_OFFSET_10H)] & GPSRAM_NS_SIZE)))
#define NS_GET_LBAF(NSID,x)                 ( (x) = ((R64_NVME_CMGR_RAM[R64_NVME_CMGR_GPSRAM_NS_INFO_H + ((NSID-1) * QW_OFFSET_10H)] & GPSRAM_NS_LBAF_BIT) >> GPSRAM_NS_LBAF_SHIFT))
#define NS_GET_NSID(NSID,x)                 ( (x) = ((R64_NVME_CMGR_RAM[R64_NVME_CMGR_GPSRAM_NS_INFO_H + ((NSID-1) * QW_OFFSET_10H)] & GPSRAM_NSID)))
#define NS_GET_WP(NSID,x)              	    ( (x) = ((R64_NVME_CMGR_RAM[R64_NVME_CMGR_GPSRAM_NS_INFO_H + ((NSID-1) * QW_OFFSET_10H)] & GPSRAM_NS_WP_BIT) >> GPSRAM_NS_WP_SHIFT))
#define NS_CHK_ACT(NSID)                    (R64_NVME_CMGR_RAM[R64_NVME_CMGR_GPSRAM_NS_INFO_H + ((NSID-1) * QW_OFFSET_10H)] & GPSRAM_NS_ACT_BIT)
#define NS_CHK_LBAF(NSID)                   (R64_NVME_CMGR_RAM[R64_NVME_CMGR_GPSRAM_NS_INFO_H + ((NSID-1) * QW_OFFSET_10H)] & GPSRAM_NS_LBAF_BIT)
#else /* (PS5021_EN) */
#define NS_CLR_NS_INFO(NSID)            	(r64_CMGR[R64_NVME_CMGR_GPSRAM_NS_INFO + (NVME_CMGR_GPSRAM_NS_INFO_QW_SIZE * (NSID-1))] = 0)
#define NS_SET_SIZE(NSID,x)              	(r64_CMGR[R64_NVME_CMGR_GPSRAM_NS_INFO + (NVME_CMGR_GPSRAM_NS_INFO_QW_SIZE * (NSID-1))] |=  ((x) & GPSRAM_NS_SIZE_MASK))
#define NS_SET_NSID(NSID,x)              	(r32_CMGR[R32_NVME_CMGR_GPSRAM_NS_INFO_DW1 + (NVME_CMGR_GPSRAM_NS_INFO_DW_SIZE * (NSID-1))] |= ( ((x) & GPSRAM_NSID_MASK) << GPSRAM_NSID_SHIFT ) )
#define NS_SET_LBAF4K(NSID)              	(r32_CMGR[R32_NVME_CMGR_GPSRAM_NS_INFO_DW1 + (NVME_CMGR_GPSRAM_NS_INFO_DW_SIZE * (NSID-1))] |= GPSRAM_NS_LBAF_BIT)
#define NS_SET_LBAF512(NSID)             	(r32_CMGR[R32_NVME_CMGR_GPSRAM_NS_INFO_DW1 + (NVME_CMGR_GPSRAM_NS_INFO_DW_SIZE * (NSID-1))] &= ~GPSRAM_NS_LBAF_BIT)
#define NS_SET_WP(NSID)   	             	(r32_CMGR[R32_NVME_CMGR_GPSRAM_NS_INFO_DW1 + (NVME_CMGR_GPSRAM_NS_INFO_DW_SIZE * (NSID-1))] |=  GPSRAM_NW_WP_BIT)
#define NS_CLR_WP(NSID)     	           	(r32_CMGR[R32_NVME_CMGR_GPSRAM_NS_INFO_DW1 + (NVME_CMGR_GPSRAM_NS_INFO_DW_SIZE * (NSID-1))] &= ~GPSRAM_NW_WP_BIT)
#define NS_SET_ACT(NSID)                	(r32_CMGR[R32_NVME_CMGR_GPSRAM_NS_INFO_DW1 + (NVME_CMGR_GPSRAM_NS_INFO_DW_SIZE * (NSID-1))] |= GPSRAM_NS_ACT_BIT)
#define NS_CLR_ACT(NSID)                	(r32_CMGR[R32_NVME_CMGR_GPSRAM_NS_INFO_DW1 + (NVME_CMGR_GPSRAM_NS_INFO_DW_SIZE * (NSID-1))] &= ~GPSRAM_NS_ACT_BIT)

#define NS_CLR_STRID(NSID)               	(r32_CMGR[R32_NVME_CMGR_GPSRAM_NS_INFO_DW1 + (NVME_CMGR_GPSRAM_NS_INFO_DW_SIZE * (NSID-1))] &= ~( (GPSRAM_NS_STRID_EN_MASK << GPSRAM_NS_STRID_EN_SHIFT) ))
#define NS_SET_STRID(NSID,x)         		(r32_CMGR[R32_NVME_CMGR_GPSRAM_NS_INFO_DW1 + (NVME_CMGR_GPSRAM_NS_INFO_DW_SIZE * (NSID-1))] |= ( ((x) & GPSRAM_NS_STRID_EN_MASK)) << GPSRAM_NS_STRID_EN_SHIFT ) )
#define NS_GET_SIZE(NSID,x)              	( (x) = (r64_CMGR[R64_NVME_CMGR_GPSRAM_NS_INFO + (NVME_CMGR_GPSRAM_NS_INFO_QW_SIZE * (NSID-1))] & GPSRAM_NS_SIZE_MASK) )
#define NS_GET_LBAF(NSID,x)              	( (x) = ((r32_CMGR[R32_NVME_CMGR_GPSRAM_NS_INFO_DW1 + (NVME_CMGR_GPSRAM_NS_INFO_DW_SIZE * (NSID-1))] & GPSRAM_NS_LBAF_BIT) >> GPSRAM_NS_LBAF_SHIFT)  )
#define NS_GET_NSID(NSID,x)              	( (x) = ((r32_CMGR[R32_NVME_CMGR_GPSRAM_NS_INFO_DW1 + (NVME_CMGR_GPSRAM_NS_INFO_DW_SIZE * (NSID-1))] & GPSRAM_NSID ) >> GPSRAM_NSID_SHIFT)  )
#define NS_GET_WP(NSID,x)              		( (x) = ((r32_CMGR[R32_NVME_CMGR_GPSRAM_NS_INFO_DW1 + (NVME_CMGR_GPSRAM_NS_INFO_DW_SIZE * (NSID-1))] & GPSRAM_NW_WP_BIT) >> GPSRAM_NW_WP_SHIFT)  )
#define NS_GET_STRID(NSID,x)            	( (x) = ((r32_CMGR[R32_NVME_CMGR_GPSRAM_NS_INFO_DW1 + (NVME_CMGR_GPSRAM_NS_INFO_DW_SIZE * (NSID-1))] & GPSRAM_NS_STRID_EN ) >> GPSRAM_NS_STRID_EN_SHIFT)  )
#define NS_CHK_ACT(NSID)                	(r32_CMGR[R32_NVME_CMGR_GPSRAM_NS_INFO_DW1 + (NVME_CMGR_GPSRAM_NS_INFO_DW_SIZE * (NSID-1))] & GPSRAM_NS_ACT_BIT)
#define NS_CHK_LBAF(NSID)               	(r32_CMGR[R32_NVME_CMGR_GPSRAM_NS_INFO_DW1 + (NVME_CMGR_GPSRAM_NS_INFO_DW_SIZE * (NSID-1))] & GPSRAM_NS_LBAF_BIT)
#endif /* (PS5021_EN) */

/*
#define NL_CC                               (0x00000090 >> 2)
#define 	CC_EN                           	BIT0
#define 	NLCC_GET_CSS()						((r32_NVME[NL_CC] & BITMSK(3,4)) >> 4 )
#define 	NLCC_GET_MPS()						((r32_NVME[NL_CC] & BITMSK(4,7)) >> 7 )
#define 	NLCC_GET_AMS()						((r32_NVME[NL_CC] & BITMSK(3,11)) >> 11 )
#define 	NLCC_GET_SHN()						((r32_NVME[NL_CC] & BITMSK(2,14)) >> 14 )
#define 	NLCC_GET_IOSQES()					((r32_NVME[NL_CC] & BITMSK(4,16)) >> 16 )
#define 	NLCC_GET_IOCQES()					((r32_NVME[NL_CC] & BITMSK(4,20)) >> 20 )
*/

/*================================= SMART/HEALTH Information LOG ================================*/
#if (PS5021_EN)
#define NLOG_GET_RCMD_CNT(FID,x)	                {(R64_NVME_CMGR_RAM[(R64_NVME_CMGR_NLOG_RCMD_CNT + FID * R64_NVME_CMGR_NLOG_ENTRY_SIZE)]); (x=R64_NVME_CMGR_RAM[(R64_NVME_CMGR_NLOG_RCMD_CNT + FID * R64_NVME_CMGR_NLOG_ENTRY_SIZE)]);}
#define NLOG_GET_RDAT_CNT(FID,x)	                {(R64_NVME_CMGR_RAM[(R64_NVME_CMGR_NLOG_RDAT_CNT + FID * R64_NVME_CMGR_NLOG_ENTRY_SIZE)]); (x=R64_NVME_CMGR_RAM[(R64_NVME_CMGR_NLOG_RDAT_CNT + FID * R64_NVME_CMGR_NLOG_ENTRY_SIZE)]);}
#define NLOG_GET_WCMD_CNT(FID,x)	                {(R64_NVME_CMGR_RAM[(R64_NVME_CMGR_NLOG_WCMD_CNT + FID * R64_NVME_CMGR_NLOG_ENTRY_SIZE)]); (x=R64_NVME_CMGR_RAM[(R64_NVME_CMGR_NLOG_WCMD_CNT + FID * R64_NVME_CMGR_NLOG_ENTRY_SIZE)]);}
#define NLOG_GET_WDAT_CNT(FID,x)	                {(R64_NVME_CMGR_RAM[(R64_NVME_CMGR_NLOG_WDAT_CNT + FID * R64_NVME_CMGR_NLOG_ENTRY_SIZE)]); (x=R64_NVME_CMGR_RAM[(R64_NVME_CMGR_NLOG_WDAT_CNT + FID * R64_NVME_CMGR_NLOG_ENTRY_SIZE)]);}
#define NLOG_GET_BUSY_MS(FID,x)	                    {(R64_NVME_CMGR_RAM[(R64_NVME_CMGR_NLOG_BUSY_TIME_MS + FID * R64_NVME_CMGR_NLOG_ENTRY_SIZE)]); (x=R64_NVME_CMGR_RAM[(R64_NVME_CMGR_NLOG_BUSY_TIME_MS + FID * R64_NVME_CMGR_NLOG_ENTRY_SIZE)]);}
#define NLOG_GET_BUSY_MIN(FID,x)	                {(R64_NVME_CMGR_RAM[(R64_NVME_CMGR_NLOG_BUSY_TIME_MIN + FID * R64_NVME_CMGR_NLOG_ENTRY_SIZE)]); (x=R64_NVME_CMGR_RAM[(R64_NVME_CMGR_NLOG_BUSY_TIME_MIN + FID * R64_NVME_CMGR_NLOG_ENTRY_SIZE)]);}
#define NLOG_SET_RCMD_CNT(FID,x)			        (R64_NVME_CMGR_RAM[(R64_NVME_CMGR_NLOG_RCMD_CNT + FID * R64_NVME_CMGR_NLOG_ENTRY_SIZE)]= x)
#define NLOG_SET_RDAT_CNT(FID,x)			        (R64_NVME_CMGR_RAM[(R64_NVME_CMGR_NLOG_RDAT_CNT + FID * R64_NVME_CMGR_NLOG_ENTRY_SIZE)]= x)
#define NLOG_SET_WCMD_CNT(FID,x)			        (R64_NVME_CMGR_RAM[(R64_NVME_CMGR_NLOG_WCMD_CNT + FID * R64_NVME_CMGR_NLOG_ENTRY_SIZE)]= x)
#define NLOG_SET_WDAT_CNT(FID,x)			        (R64_NVME_CMGR_RAM[(R64_NVME_CMGR_NLOG_WDAT_CNT + FID * R64_NVME_CMGR_NLOG_ENTRY_SIZE)]= x)
#define NLOG_SET_BUSY_MS(FID,x)			            (R64_NVME_CMGR_RAM[(R64_NVME_CMGR_NLOG_BUSY_TIME_MS + FID * R64_NVME_CMGR_NLOG_ENTRY_SIZE)]= x)
#define NLOG_SET_BUSY_MIN(FID,x)			        (R64_NVME_CMGR_RAM[(R64_NVME_CMGR_NLOG_BUSY_TIME_MIN + FID * R64_NVME_CMGR_NLOG_ENTRY_SIZE)]= x)
#else /* PS5021_EN */
#define 	NLOG_GET_RCMD_CNT(x)				{(r64_CMGR[R64_NVME_CMGR_NLOG_RCMD_CNT]); (x=r64_CMGR[R64_NVME_CMGR_FW_GPSRAM_QW_RDATA]);}
#define 	NLOG_GET_RDAT_CNT(x)				{(r64_CMGR[R64_NVME_CMGR_NLOG_RDAT_CNT]); (x=r64_CMGR[R64_NVME_CMGR_FW_GPSRAM_QW_RDATA]);}
#define 	NLOG_GET_WCMD_CNT(x)				{(r64_CMGR[R64_NVME_CMGR_NLOG_WCMD_CNT]); (x=r64_CMGR[R64_NVME_CMGR_FW_GPSRAM_QW_RDATA]);}
#define 	NLOG_GET_WDAT_CNT(x)				{(r64_CMGR[R64_NVME_CMGR_NLOG_WDAT_CNT]); (x=r64_CMGR[R64_NVME_CMGR_FW_GPSRAM_QW_RDATA]);}
#define 	NLOG_GET_BUSY_MS(x)					{(r64_CMGR[R64_NVME_CMGR_NLOG_BUSY_MS]); (x=r64_CMGR[R64_NVME_CMGR_FW_GPSRAM_QW_RDATA]);}
#define 	NLOG_GET_BUSY_MIN(x)				{(r64_CMGR[R64_NVME_CMGR_NLOG_BUSY_MIN]); x=(r64_CMGR[R64_NVME_CMGR_FW_GPSRAM_QW_RDATA]);}
#define 	NLOG_SET_RCMD_CNT(x)				(r64_CMGR[R64_NVME_CMGR_NLOG_RCMD_CNT]= x)
#define 	NLOG_SET_RDAT_CNT(x)				(r64_CMGR[R64_NVME_CMGR_NLOG_RDAT_CNT]= x)
#define 	NLOG_SET_WCMD_CNT(x)				(r64_CMGR[R64_NVME_CMGR_NLOG_WCMD_CNT]= x)
#define 	NLOG_SET_WDAT_CNT(x)				(r64_CMGR[R64_NVME_CMGR_NLOG_WDAT_CNT]= x)
#define 	NLOG_SET_BUSY_MS(x)					(r64_CMGR[R64_NVME_CMGR_NLOG_BUSY_MS]= x)
#define 	NLOG_SET_BUSY_MIN(x)				(r64_CMGR[R64_NVME_CMGR_NLOG_BUSY_MIN]= x)
#endif /* PS5021_EN */

/*================================= PRP Entry for read Command ================================*/
// #define RCH_PRP_OFFSET                      (0x1000)         // total 0 ~ 511 entry
// #define RCH_PRP_ENTRY_W64                   ((REG64 *)(NVME_CMGR_REGISTER_ADDRESS + RCH_PRP_OFFSET))

/*================================= CMGR Command Table SRAM ================================*/
#define	NS_LBAF512		(LBAF_512)
#define	NS_LBAF4K		(LBAF_4K)

#define CTSRAM_QW0                          (R64_NVME_CMGR_CTSRAM_CMD_INFO)
#define CTSRAM_QW1                          (R64_NVME_CMGR_CTSRAM_CMD_STS)
#define CTSRAM_QW2                          (R64_NVME_CMGR_CTSRAM_RLBN)
#define CTSRAM_QW3                          (R64_NVME_CMGR_CTSRAM_PRP1)
#define CTSRAM_QW4                          (R64_NVME_CMGR_CTSRAM_PRP2)
#define CTSRAM_QW5                          (R64_NVME_CMGR_CTSRAM_SLBA)
#define CTSRAM_QW6                          (R64_NVME_CMGR_CTSRAM_CMD_SPC)
#define CTSRAM_QW7                          (R64_NVME_CMGR_CTSRAM_QW7)

#if (PS5021_EN)
#define 	CTSRAM_GET_CTSRAM_DW1(CTAG) 		(R32_NVME_CMGR_RAM[CTSRAM_DW1 + (CTSRAM_CMD_OFFSET_DW * (CTAG))])
#define 	CTSRAM_GET_CTSRAM_DW6(CTAG) 		(R32_NVME_CMGR_RAM[CTSRAM_DW6 + (CTSRAM_CMD_OFFSET_DW * (CTAG))])
#define 	CTSRAM_GET_CTSRAM_DW7(CTAG) 		(R32_NVME_CMGR_RAM[CTSRAM_DW7 + (CTSRAM_CMD_OFFSET_DW * (CTAG))])
#define 	CTSRAM_GET_CTSRAM_DW8(CTAG) 		(R32_NVME_CMGR_RAM[CTSRAM_DW8 + (CTSRAM_CMD_OFFSET_DW * (CTAG))])
#define 	CTSRAM_GET_CTSRAM_DW9(CTAG) 		(R32_NVME_CMGR_RAM[CTSRAM_DW9 + (CTSRAM_CMD_OFFSET_DW * (CTAG))])
#define 	CTSRAM_GET_CTSRAM_DW10(CTAG) 		(R32_NVME_CMGR_RAM[CTSRAM_DW10 + (CTSRAM_CMD_OFFSET_DW * (CTAG))])
#define 	CTSRAM_GET_CTSRAM_DW11(CTAG) 		(R32_NVME_CMGR_RAM[CTSRAM_DW11 + (CTSRAM_CMD_OFFSET_DW * (CTAG))])
#define 	CTSRAM_GET_CTSRAM_DW12(CTAG) 		(R32_NVME_CMGR_RAM[CTSRAM_DW12 + (CTSRAM_CMD_OFFSET_DW * (CTAG))])
#define 	CTSRAM_GET_CTSRAM_DW13(CTAG) 		(R32_NVME_CMGR_RAM[CTSRAM_DW13 + (CTSRAM_CMD_OFFSET_DW * (CTAG))])
#define 	CTSRAM_GET_CTSRAM_DW14(CTAG) 		(R32_NVME_CMGR_RAM[CTSRAM_DW14 + (CTSRAM_CMD_OFFSET_DW * (CTAG))])
#define 	CTSRAM_GET_CTSRAM_DW15(CTAG) 		(R32_NVME_CMGR_RAM[CTSRAM_DW15 + (CTSRAM_CMD_OFFSET_DW * (CTAG))])
#else /* (PS5021_EN) */
#define 	CTSRAM_GET_CTSRAM_DW1(CTAG) 		(r32_CMGR[CTSRAM_DW1 + (CTSRAM_CMD_OFFSET_DW * (CTAG))])
#define 	CTSRAM_GET_CTSRAM_DW6(CTAG) 		(r32_CMGR[CTSRAM_DW6 + (CTSRAM_CMD_OFFSET_DW * (CTAG))])
#define 	CTSRAM_GET_CTSRAM_DW7(CTAG) 		(r32_CMGR[CTSRAM_DW7 + (CTSRAM_CMD_OFFSET_DW * (CTAG))])
#define 	CTSRAM_GET_CTSRAM_DW8(CTAG) 		(r32_CMGR[CTSRAM_DW8 + (CTSRAM_CMD_OFFSET_DW * (CTAG))])
#define 	CTSRAM_GET_CTSRAM_DW9(CTAG) 		(r32_CMGR[CTSRAM_DW9 + (CTSRAM_CMD_OFFSET_DW * (CTAG))])
#define 	CTSRAM_GET_CTSRAM_DW10(CTAG) 		(r32_CMGR[CTSRAM_DW10 + (CTSRAM_CMD_OFFSET_DW * (CTAG))])
#define 	CTSRAM_GET_CTSRAM_DW11(CTAG) 		(r32_CMGR[CTSRAM_DW11 + (CTSRAM_CMD_OFFSET_DW * (CTAG))])
#define 	CTSRAM_GET_CTSRAM_DW12(CTAG) 		(r32_CMGR[CTSRAM_DW12 + (CTSRAM_CMD_OFFSET_DW * (CTAG))])
#define 	CTSRAM_GET_CTSRAM_DW13(CTAG) 		(r32_CMGR[CTSRAM_DW13 + (CTSRAM_CMD_OFFSET_DW * (CTAG))])
#define 	CTSRAM_GET_CTSRAM_DW14(CTAG) 		(r32_CMGR[CTSRAM_DW14 + (CTSRAM_CMD_OFFSET_DW * (CTAG))])
#define 	CTSRAM_GET_CTSRAM_DW15(CTAG) 		(r32_CMGR[CTSRAM_DW15 + (CTSRAM_CMD_OFFSET_DW * (CTAG))])
#endif /* (PS5021_EN) */

#if (PS5021_EN)
#define 	CTSRAM_GET_CMD_INFO(CTAG)                  (R64_NVME_CMGR_RAM[CTSRAM_QW0 + (CTSRAM_CMD_OFFSET_QW * (CTAG))])
#define 	CTSRAM_GET_CMD_STS(CTAG)                   (R64_NVME_CMGR_RAM[CTSRAM_QW1 + (CTSRAM_CMD_OFFSET_QW * (CTAG))])
#define 	CTSRAM_GET_RLBN(CTAG)                      (R64_NVME_CMGR_RAM[CTSRAM_QW2 + (CTSRAM_CMD_OFFSET_QW * (CTAG))])
#define 	CTSRAM_GET_PRP1(CTAG)                      (R64_NVME_CMGR_RAM[CTSRAM_QW3 + (CTSRAM_CMD_OFFSET_QW * (CTAG))])
#define 	CTSRAM_GET_PRP2(CTAG)                      (R64_NVME_CMGR_RAM[CTSRAM_QW4 + (CTSRAM_CMD_OFFSET_QW * (CTAG))])
#define 	CTSRAM_GET_SLBA(CTAG)                      (R64_NVME_CMGR_RAM[CTSRAM_QW5 + (CTSRAM_CMD_OFFSET_QW * (CTAG))])
#define 	CTSRAM_GET_CMD_SPC(CTAG)                   (R64_NVME_CMGR_RAM[CTSRAM_QW6 + (CTSRAM_CMD_OFFSET_QW * (CTAG))])

#define 	CTSRAM_GET_OPC(CTAG)            	            (R32_NVME_CMGR_RAM[CTSRAM_DW0 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_OPC)
#define 	CTSRAM_GET_FUSE(CTAG)            	            ((R32_NVME_CMGR_RAM[CTSRAM_DW0 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_FUSE) >> CTSRAM_FUSE_SHIFT )
#define 	CTSRAM_GET_PSDT(CTAG)            	            ((R32_NVME_CMGR_RAM[CTSRAM_DW0 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_PSDT) >> CTSRAM_PSDT_SHIFT )
#define 	CTSRAM_GET_CID(CTAG)            	            ((R32_NVME_CMGR_RAM[CTSRAM_DW0 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_CID) >> CTSRAM_CID_SHIFT )

#define 	CTSRAM_GET_NSID(CTAG)            	            (R32_NVME_CMGR_RAM[CTSRAM_DW1 + (CTSRAM_CMD_OFFSET_DW * (CTAG))])
#define 	CTSRAM_GET_FID(CTAG)                    ((R64_NVME_CMGR_RAM[R64_NVME_CMGR_CTSRAM_CMD_STS + (QW_OFFSET_40H * (CTAG))] & CTSRAM_FID) >> CTSRAM_FID_SHIFT )

#define 	CTSRAM_CHK_ADMIN(CTAG)            	       (R32_NVME_CMGR_RAM[CTSRAM_DW2 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_ADM_BIT)
#define 	CTSRAM_GET_ADMIN(CTAG)            	       ((R32_NVME_CMGR_RAM[CTSRAM_DW2 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_ADM_BIT) == CTSRAM_ADM_BIT)
#define 	CTSRAM_GET_LBAFORMAT(CTAG)                 ((R32_NVME_CMGR_RAM[CTSRAM_DW2 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_LBAF_BIT)>>CTSRAM_LBAF_SHIFT)
#define     CTSRAM_GET_VALID(CTAG)                      (R32_NVME_CMGR_RAM[CTSRAM_DW2 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_VLD_BIT)
#define     CTSRAM_GET_CMDSTS(CTAG)                 ((R32_NVME_CMGR_RAM[CTSRAM_DW2 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_CMD_STS) >> CTSRAM_CMD_STS_SHIFT)
#define     CTSRAM_GET_PT(CTAG)                          ((R32_NVME_CMGR_RAM[CTSRAM_DW2 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_PT_BIT) >> CTSRAM_PT_SHIFT )
#define     CTSRAM_GET_ERR(CTAG)                        ((R32_NVME_CMGR_RAM[CTSRAM_DW2 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_ERR_BIT) >> CTSRAM_ERR_SHIFT )
#define     CTSRAM_GET_CMDTYP(CTAG)                 ((R32_NVME_CMGR_RAM[CTSRAM_DW2 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_CMD_TYPE ) >> CTSRAM_CMD_TYPE_SHIFT )
#define     CTSRAM_GET_SSD_NSID(CTAG)                 ((R32_NVME_CMGR_RAM[CTSRAM_DW2 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_SSD_NSID) >> CTSRAM_SSD_NSID_SHIFT )

#define 	CTSRAM_GET_SQID(CTAG)            	            ((R32_NVME_CMGR_RAM[CTSRAM_DW3 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_SQID) >> CTSRAM_SQID_SHIFT )

#define     CTSRAM_GET_DW2(x)                	            (R32_NVME_CMGR_RAM[R32_NVME_CMGR_CTSRAM_CMD_STS + (x * DW_OFFSET_40H)]& 0xFFFFFFFE)
#define     CTSRAM_SET_DW2(x,y)              	            (R32_NVME_CMGR_RAM[R32_NVME_CMGR_CTSRAM_CMD_STS + (x * DW_OFFSET_40H)] = y)


#define 	CTSRAM_GET_Z0_RLBN(CTAG)                    (R32_NVME_CMGR_RAM[CTSRAM_DW4 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_Z0_RLBN)
#define 	CTSRAM_SET_Z0_RLBN(CTAG,x)                  (R32_NVME_CMGR_RAM[CTSRAM_DW4 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] = (x & CTSRAM_Z0_RLBN))

#define     CTSRAM_GET_NLB(CTAG)                        (R32_NVME_CMGR_RAM[CTSRAM_DW12 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_NLB)
#define     CTSRAM_GET_DTYPE(CTAG)             	    ((R32_NVME_CMGR_RAM[CTSRAM_DW12 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_DTYPE) >> CTSRAM_DTYPE_SHIFT)
#define     CTSRAM_GET_PRINFO(CTAG)                  ((R32_NVME_CMGR_RAM[CTSRAM_DW12 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_PRINFO) >> CTSRAM_PRINFO_SHIFT)
#define     CTSRAM_GET_FUA(CTAG)                        ((R32_NVME_CMGR_RAM[CTSRAM_DW12 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_FUA_BIT )>> CTSRAM_FUA_SHIFT )
#define     CTSRAM_GET_LR(CTAG)                          ((R32_NVME_CMGR_RAM[CTSRAM_DW12 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_LR_BIT )>> CTSRAM_LR_SHIFT )

#define     CTSRAM_GET_DSM(CTAG)              	    (R32_NVME_CMGR_RAM[CTSRAM_DW13 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_DSM)
#define     CTSRAM_GET_DSPEC(CTAG)             	    ((R32_NVME_CMGR_RAM[CTSRAM_DW13 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_DSPEC) >> CTSRAM_DSPEC_SHIFT)
#else /* (PS5021_EN) */
#define 	CTSRAM_GET_CMD_INFO(CTAG)                 (r64_CMGR[CTSRAM_QW0 + (CTSRAM_CMD_OFFSET_QW * (CTAG))])
#define 	CTSRAM_GET_CMD_STS(CTAG)                  (r64_CMGR[CTSRAM_QW1 + (CTSRAM_CMD_OFFSET_QW * (CTAG))])
#define 	CTSRAM_GET_RLBN(CTAG)                     (r64_CMGR[CTSRAM_QW2 + (CTSRAM_CMD_OFFSET_QW * (CTAG))])
#define 	CTSRAM_GET_PRP1(CTAG)                     (r64_CMGR[CTSRAM_QW3 + (CTSRAM_CMD_OFFSET_QW * (CTAG))])
#define 	CTSRAM_GET_PRP2(CTAG)                     (r64_CMGR[CTSRAM_QW4 + (CTSRAM_CMD_OFFSET_QW * (CTAG))])
#define 	CTSRAM_GET_SLBA(CTAG)                     (r64_CMGR[CTSRAM_QW5 + (CTSRAM_CMD_OFFSET_QW * (CTAG))])
#define 	CTSRAM_GET_CMD_SPC(CTAG)                  (r64_CMGR[CTSRAM_QW6 + (CTSRAM_CMD_OFFSET_QW * (CTAG))])

#define 	CTSRAM_GET_OPC(CTAG)            	        (r32_CMGR[CTSRAM_DW0 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_OPC)
#define 	CTSRAM_GET_FUSE(CTAG)            	        ((r32_CMGR[CTSRAM_DW0 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_FUSE) >> CTSRAM_FUSE_SHIFT )
#define 	CTSRAM_GET_PSDT(CTAG)            	        ((r32_CMGR[CTSRAM_DW0 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_PSDT) >> CTSRAM_PSDT_SHIFT )
#define 	CTSRAM_GET_CID(CTAG)            	        ((r32_CMGR[CTSRAM_DW0 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_CID) >> CTSRAM_CID_SHIFT )

#define 	CTSRAM_GET_NSID(CTAG)            	            (r32_CMGR[CTSRAM_DW1 + (CTSRAM_CMD_OFFSET_DW * (CTAG))])

#define 	CTSRAM_CHK_ADMIN(CTAG)            	        (r32_CMGR[CTSRAM_DW2 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_ADM_BIT)
#define 	CTSRAM_GET_ADMIN(CTAG)            	        ((r32_CMGR[CTSRAM_DW2 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_ADM_BIT) == CTSRAM_ADM_BIT)
#define 	CTSRAM_GET_LBAFORMAT(CTAG)              	((r32_CMGR[CTSRAM_DW2 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_LBAF_BIT)>>CTSRAM_LBAF_SHIFT)
#define     CTSRAM_GET_VALID(CTAG)                      (r32_CMGR[CTSRAM_DW2 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_VLD_BIT)
#define     CTSRAM_GET_CMDSTS(CTAG)                 ((r32_CMGR[CTSRAM_DW2 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_CMD_STS) >> CTSRAM_CMD_STS_SHIFT )
#define     CTSRAM_GET_PT(CTAG)                          ((r32_CMGR[CTSRAM_DW2 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_PT_BIT) >> CTSRAM_PT_SHIFT )
#define     CTSRAM_GET_ERR(CTAG)                        ((r32_CMGR[CTSRAM_DW2 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_ERR_BIT) >> CTSRAM_ERR_SHIFT )
#define     CTSRAM_GET_CMDTYP(CTAG)                 ((r32_CMGR[CTSRAM_DW2 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_CMD_TYPE) >> CTSRAM_CMD_TYPE_SHIFT )
#define     CTSRAM_GET_SSD_NSID(CTAG)                 ((r32_CMGR[CTSRAM_DW2 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_SSD_NSID) >> CTSRAM_SSD_NSID_SHIFT )

#define 	CTSRAM_GET_SQID(CTAG)            	            ((r32_CMGR[CTSRAM_DW3 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_SQID) >> CTSRAM_SQID_SHIFT )

#define 	CTSRAM_GET_Z0_RLBN(CTAG)                    (r32_CMGR[CTSRAM_DW4 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_Z0_RLBN)
#define 	CTSRAM_SET_Z0_RLBN(CTAG,x)                  (r32_CMGR[CTSRAM_DW4 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] = (x & CTSRAM_Z0_RLBN))

#define     CTSRAM_GET_NLB(CTAG)                        (r32_CMGR[CTSRAM_DW12 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_NLB)
#define     CTSRAM_GET_DTYPE(CTAG)             	    ((r32_CMGR[CTSRAM_DW12 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_DTYPE) >> CTSRAM_DTYPE_SHIFT)
#define     CTSRAM_GET_PRINFO(CTAG)                  ((r32_CMGR[CTSRAM_DW12 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_PRINFO) >> CTSRAM_PRINFO_SHIFT)
#define     CTSRAM_GET_FUA(CTAG)                        ((r32_CMGR[CTSRAM_DW12 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_FUA_BIT )>> CTSRAM_FUA_SHIFT )
#define     CTSRAM_GET_LR(CTAG)                          ((r32_CMGR[CTSRAM_DW12 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_LR_BIT )>> CTSRAM_LR_SHIFT )

#define     CTSRAM_GET_DSM(CTAG)              	    (r32_CMGR[CTSRAM_DW13 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_DSM)
#define     CTSRAM_GET_DSPEC(CTAG)             	    ((r32_CMGR[CTSRAM_DW13 + (CTSRAM_CMD_OFFSET_DW * (CTAG))] & CTSRAM_DSPEC) >> CTSRAM_DSPEC_SHIFT)
#endif /* (PS5021_EN) */



#if (PS5021_EN)
#define	CMGR_GET_LCA_MGR_DMA_IDLE()			(R32_NVME_CMGR_REG[R32_NVME_CMGR_LCA_MGR_DB_PORT] & DMA_IDLE_BIT)
#define CMGR_CHECK_DMA_STATE_IDLE()       ((U32)R32_NVME_CMGR_REG[R32_NVME_CMGR_LCA_MGR_DB_PORT2] & DMAT_STATE)//wait dma state idle
#define	CMGR_GET_CFMGR_IDLE()				(R32_NVME_CMGR_REG[R32_NVME_CMGR_CFMGR_DB_PORT] & CFMGR_IDLE_BIT)
#define	CMGR_GET_CFMGR_REMPTY()				(R32_NVME_CMGR_REG[R32_NVME_CMGR_CFMGR_DB_PORT] & CFMGR_REMPTY_BIT)

#else /* (PS5021_EN) */
#define		CMGR_GET_LCA_MGR_DMA_IDLE()				(r32_CMGR[R32_NVME_CMGR_LCA_MGR_DB_PORT] & DMA_IDLE_BIT)
#define		CMGR_CHECK_DMA()						(r32_CMGR[R32_NVME_CMGR_LCA_MGR_DB_PORT] & DMA_CNT)
// #define		CMGR_CHECK_READ_WRITE_DMA_CNT()			(r32_CMGR[LCA_MGR_DB_PORT_W32] & DMA_CNT_MASK)
#define     M_CMGR_WRITE_PRP_HANG()                  ((r32_CMGR[R32_NVME_CMGR_LCA_MGR_DB_PORT] & CMGR_WRITE_PRP_DEBUG1)&&( 0 == (r32_CMGR[R32_NVME_CMGR_LCA_MGR_DB_PORT] & CMGR_WRITE_PRP_DEBUG2)))

#define 	M_CMGR_READ_DMA_UPDATE_CMD_TABLE_IDLE()        (r32_CMGR[R32_NVME_CMGR_DEBUG_PORT_0X120] & READ_DMA_UPDATE_CMD_TABLE_IDLE_BIT)

#define     M_CMGR_READ_PRP_HANG()                  (r32_CMGR[R32_NVME_CMGR_LCA_MGR_DB_PORT3] & CMGR_READ_PRP_DEBUG1_BIT)

#define		CMGR_GET_CFMGR_IDLE()					(r32_CMGR[R32_NVME_CMGR_CFMGR_DB_PORT] & CFMGR_IDLE_BIT)

#define		CMGR_PARING_CMD_IDLE()					(r32_CMGR[R32_NVME_CMGR_CFMGR_DB_PORT2] & CMGR_PARSING_CMD_IDLE_BIT)

#define     M_CMGR_PNSRAM_FULL()                	(r32_CMGR[R32_NVME_CMGR_MGR_DB_PORT] & CMGR_PNSRAM_FULL_BIT)
#endif /* (PS5021_EN) */

/*====================== END: CMGR register =============================*/

#endif
#endif /* (NVME==HOST_MODE) */

