/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "hal/fip/fip_api.h"
#include "hal/cop0/cop0_api.h"
#include "media_scan/media_scan_api.h"
#include "table/sys_block/sys_block_api.h"
#include "drive_log/drive_log_api.h"
#include "retry/stall.h"
#include "retry/patch_cmd.h"
#include "hal/fip/fpu.h"
#include "buffer/buffer.h"
#include "init/fw_init.h"

#if VRLC_EN
#include "VRLC/VRLC_api.h"
#endif /* VRLC_EN */
#include "vuc/VUC_MicronGetUnifiedEventLog.h"


/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *  global Variable
 * ---------------------------------------------------------------------------------------------------
 */

extern Unit_Temperature_t *gUnitTemperature;
/*
 * ---------------------------------------------------------------------------------------------------
 *  enum
 * ---------------------------------------------------------------------------------------------------
 */

/*
* ---------------------------------------------------------------------------------------------------
*   private prototypes
* ---------------------------------------------------------------------------------------------------
*/
#if (RECORD_FLASH_TEMPERATURE_EN)
U8 MediaScanLevelToTemperature(U8 ubLevel)
{
	U8 ubTemperatureArray[16] = {0, 5, 9, 14, 18, 23, 28, 32, 37, 41, 46, 51, 55, 60, 65, 70};
	return ubTemperatureArray[ubLevel];
}

U8 MediaScanTemperatureToLevel(TTTemperatureCelsius_t TTFlashTemperature)
{
	U8 ubTemperatureArray[16] = {0, 5, 9, 14, 18, 23, 28, 32, 37, 41, 46, 51, 55, 60, 65, 70};
	U8 ubTemperatureLevel;
	if (TTFlashTemperature.B.btSign) {
		return 0;
	}
	for (ubTemperatureLevel = 0; ubTemperatureLevel < 16; ubTemperatureLevel++) {
		if (ubTemperatureArray[ubTemperatureLevel] > TTFlashTemperature.B.Degree) {
			break;
		}
	}
	return ubTemperatureLevel - 1;
}

void MediaScanRecordUnitTemperature(Unit_t uwUnit)
{
	if (uwUnit.B.uwUnit & BIT_MASK(1)) {
		gUnitTemperature->Unit[((uwUnit.B.uwUnit) >> 1)].ubOddUnit = MediaScanTemperatureToLevel(gTT.ubCurrentTemperatureExternal);
	}
	else {
		gUnitTemperature->Unit[((uwUnit.B.uwUnit) >> 1)].ubEvenUnit = MediaScanTemperatureToLevel(gTT.ubCurrentTemperatureExternal);
	}
}

void MediaScanRecordPhysicalBlkTemperature(U8 ubSystemAreaType, U8 ubChannel, U8 ubBlkIndex)
{
	U8 ubFlashTemperature = MediaScanTemperatureToLevel(gTT.ubCurrentTemperatureExternal);

	switch (ubSystemAreaType) {
	case PHYSICAL_BLK_SYSTEM:
		if (ubBlkIndex & BIT_MASK(1)) {
			gUnitTemperature->SystemBlk[ubBlkIndex >> 1].ubOddUnit = ubFlashTemperature;
		}
		else {
			gUnitTemperature->SystemBlk[ubBlkIndex >> 1].ubEvenUnit = ubFlashTemperature;
		}
		break;
	case PHYSICAL_BLK_DBT:
		if (ubBlkIndex & BIT_MASK(1)) {
			gUnitTemperature->DBTblk[ubBlkIndex >> 1].ubOddUnit = ubFlashTemperature;
		}
		else {
			gUnitTemperature->DBTblk[ubBlkIndex >> 1].ubEvenUnit = ubFlashTemperature;
		}
		break;
	case PHYSICAL_BLK_CODE:
		if (ubBlkIndex & BIT_MASK(1)) {
			gUnitTemperature->CodeBlk[(ubChannel * 2 + ubBlkIndex) >> 1].ubOddUnit = ubFlashTemperature;
		}
		else {
			gUnitTemperature->CodeBlk[(ubChannel * 2 + ubBlkIndex) >> 1].ubEvenUnit = ubFlashTemperature;
		}
		break;
	case PHYSICAL_BLK_BANKING:
		if (ubBlkIndex & BIT_MASK(1)) {
			gUnitTemperature->BankingBlk[(ubChannel * 2 + ubBlkIndex) >> 1].ubOddUnit = ubFlashTemperature;
		}
		else {
			gUnitTemperature->BankingBlk[(ubChannel * 2 + ubBlkIndex) >> 1].ubEvenUnit = ubFlashTemperature;
		}
		break;
	case PHYSICAL_BLK_VTMOTHER:
		gUnitTemperature->VTMother = ubFlashTemperature;
		break;
	default:
		break;
	}
}

U8 MediaScanGetUnitTemperature(U16 uwUnit)
{
	if (uwUnit & BIT_MASK(1)) {
		return MediaScanLevelToTemperature(gUnitTemperature->Unit[(uwUnit) >> 1].ubOddUnit);
	}
	else {
		return MediaScanLevelToTemperature(gUnitTemperature->Unit[(uwUnit) >> 1].ubEvenUnit);
	}
}

U8 MediaScanGetPhysicalBlkTemperature(U8 ubSystemAreaType, SystemAreaBlock_t ubSystemAreaBlock)
{
	U8 ubi, ubChannel;
	switch (ubSystemAreaType) {
	case PHYSICAL_BLK_SYSTEM:
		for (ubi = 0 ; ubi < SYSTEM_AREA_SYSTEM_BLK_NUM ; ubi++) {
			if (gSystemArea.SystemBlock[ubi].uwAll == ubSystemAreaBlock.uwAll) {
				if (ubi & BIT_MASK(1)) {
					return MediaScanLevelToTemperature(gUnitTemperature->SystemBlk[ubi >> 1].ubOddUnit);
				}
				else {
					return MediaScanLevelToTemperature(gUnitTemperature->SystemBlk[ubi >> 1].ubEvenUnit);
				}
			}
		}
		break;
	case PHYSICAL_BLK_DBT:
		for (ubi = 0 ; ubi < SYSTEM_AREA_DBT_BLK_NUM ; ubi++) {
			if (gSystemArea.DBTBlock[ubi].uwAll == ubSystemAreaBlock.uwAll) {
				if (ubi & BIT_MASK(1)) {
					return MediaScanLevelToTemperature(gUnitTemperature->DBTblk[ubi >> 1].ubOddUnit);
				}
				else {
					return MediaScanLevelToTemperature(gUnitTemperature->DBTblk[ubi >> 1].ubEvenUnit);
				}
			}
		}
		break;
	case PHYSICAL_BLK_CODE:
		for (ubChannel = 0; ubChannel < E17_ALLIGN_E13_MAX_CH; ubChannel++) {
			for (ubi = 0; ubi < CODE_BLK_NUM_PER_CH; ubi++) {
				if (gSystemArea.CodeBlock[ubChannel][ubi].uwAll == ubSystemAreaBlock.uwAll) {
					if (ubi & BIT_MASK(1)) {
						return MediaScanLevelToTemperature(gUnitTemperature->CodeBlk[ubi >> 1].ubOddUnit);
					}
					else {
						return MediaScanLevelToTemperature(gUnitTemperature->CodeBlk[ubi >> 1].ubEvenUnit);
					}
				}
			}
		}
		break;
	case PHYSICAL_BLK_BANKING:
		for (ubChannel = 0; ubChannel < E17_ALLIGN_E13_MAX_CH; ubChannel++) {
			for (ubi = 0; ubi < CODE_BLK_NUM_PER_CH; ubi++) {
				if (gSystemArea.BankingBlk[ubChannel][ubi].uwAll == ubSystemAreaBlock.uwAll) {
					if (ubi & BIT_MASK(1)) {
						return MediaScanLevelToTemperature(gUnitTemperature->BankingBlk[ubi >> 1].ubOddUnit);
					}
					else {
						return MediaScanLevelToTemperature(gUnitTemperature->BankingBlk[ubi >> 1].ubEvenUnit);
					}
				}
			}
		}
		break;
	case PHYSICAL_BLK_VTMOTHER:
		return MediaScanLevelToTemperature(gUnitTemperature->VTMother);
		break;
	default:
		return 0;
	}
	return 0;

}
#endif /* (RECORD_FLASH_TEMPERATURE_EN) */
#if ((RECORD_FLASH_TEMPERATURE_EN) && (!BURNER_MODE_EN))
void MediaScanGetFlashTemperature()
{
	U8 ubStatus = FAIL;
	while (ubStatus) {
		ubStatus = TTGetFlashTemperature(&gTT.ubCurrentTemperatureExternal);
	}
	gUnitTemperature->InitFlag = FALSE;
	U8	ubResult = 0;
	BMUCmdResult_t BMUCmdFreePBResult = {{0}};
	do {
		ubResult = BMUAPICmdFreePB(BMU_CMD_NEED_CQ, RECORD_FLASH_TEMPERATURE_BUF_OFFSET,
				BMU_FREE_NOT_NEED_CHECK_DF_FLAG, BMU_ALLOCATE_NOT_UPDATE_QUOTA, 0, &BMUCmdFreePBResult);
	} while (BMU_CMD_STATUS_FAIL == ubResult);
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, BMU_CMD_STATUS_SUCCESS == BMUCmdFreePBResult.BMUFreePBRst.ubResult);
}
#endif /* ((RECORD_FLASH_TEMPERATURE_EN) && (!BURNER_MODE_EN)) */

#if ((MEDIA_SCAN_EN) && (!BURNER_MODE_EN) && (!RDT_MODE_EN) && (IM_N48R))
U16 MediaScanPreconditionSetMLBiFPU(U16 uwTrimAddr, U8 ubDie)
{
	//U8 ubi;
	U8 ubSetFeatureData[PARAMETER_NUM_PER_FPU] = {0};
	U16 uwFPUPtr = FPU_PTR_OFFSET(fpu_micron_set_mlbi_cmd);
	U16 *puwFPU = (U16 *)(IRAM_BASE + uwFPUPtr);

	// Copy set feature from VRLC Read Level
	ubSetFeatureData[0] = gMediaScanManager.ReadSamplingOffset.ubReadLevel;
	// Set feature cmd
	puwFPU[0] = FPU_CMD(0xEB);
	// 1st feature address (LUN address)
	puwFPU[1] = FPU_ADR_1B((uwTrimAddr & BIT_MASK(8)));

	puwFPU[2] = FPU_ADR_1B(((uwTrimAddr >> 8) & BIT_MASK(8)));  // USE HB RETRY ADDr TEST VRLC FLOW
	//puwFPU[3] = FPU_ADR_1B(((gVRLCTask.uwTrimReg >> 8) & BIT_MASK(8))); // MLBi Command format ?
	puwFPU[3] = FPU_ADR_1B(ubDie); //LUN

	// Delay
	if (FIP_FLASH_CLOCK_400MHZ == gFlhEnv.ubTargetFlashClock) {
		// flh clk 400
		puwFPU[4] = FPU_DLY(0x15);		// Decimal 21
	}
	else if (FIP_FLASH_CLOCK_333MHZ == gFlhEnv.ubTargetFlashClock) {
		// flh clk 333
		puwFPU[4] = FPU_DLY(0xF);		// Decimal 15
	}
	else {
		puwFPU[4] = FPU_DLY(0x10);		// Decimal 16
	}

	puwFPU[5] = FPU_DAT_W_1B(ubSetFeatureData[0]);
	puwFPU[6] = FPU_END;

	return uwFPUPtr;
}

U16 MediaScanPreconditionGetMLBiFPU(U8 ubQLC, U16 uwTrimAddr, U8 ubDie)
{
#if RETRY_HARDBIT_FOR_SDK_EN
	return 0;
#else /* RETRY_HARDBIT_FOR_SDK_EN */
	// Get FPU PTR
	U16 uwFPUPtr = FPU_PTR_OFFSET(fpu_micron_get_mlbi_cmd);
	U16 *puwFPU = (U16 *)(IRAM_BASE + uwFPUPtr);

	// Set feature cmd, already fill FPU[0] in init state
	puwFPU[0] = FPU_CMD(0xEA);
	// 1st feature address (LUN address)
	puwFPU[1] = FPU_ADR_1B((uwTrimAddr & BIT_MASK(8)));
	puwFPU[2] = FPU_ADR_1B(((uwTrimAddr >> 8) & ((ubQLC) ? BIT_MASK(8) : BIT_MASK(2))));  // USE HB RETRY ADDr TEST VRLC FLOW
	puwFPU[3] = FPU_ADR_1B(ubDie); // LUN
	puwFPU[4] = FPU_END;

	return uwFPUPtr;
#endif /* RETRY_HARDBIT_FOR_SDK_EN */
}
#if 0
U16 MediaScanPreconditionCheckFeatureFPU(void)
{
	U8 ubCompareFeatureData[PARAMETER_NUM_PER_FPU] = {0};
	U8 ubi;
	// Get FPU PTR
	U16 uwFPUPtr = FPU_PTR_OFFSET(fpu_read_and_compare_feature_data);
	U16 *puwFPU = (U16 *)(IRAM_BASE + uwFPUPtr);

	// Copy set feature from VRLC
	ubCompareFeatureData[0] = gMediaScanManager.RSO.ubReadLevel;

	for (ubi = 0; ubi < 4; ubi++) {
		puwFPU[2 + (ubi << 1)] = FPU_DAT_R_CMP(ubCompareFeatureData[ubi]);
		puwFPU[3 + (ubi << 1)] = (0 == ubi) ? FPU_DAT_R_MASK(0xFF) : FPU_DAT_R_MASK(0); //Just compare P1 value
	}
	return uwFPUPtr;
}
#endif

void MediaScanStallOrUnstallChannel(RetryJob_t *pRetryJob, U8 ubMode)
{
	U8 ubCE = 0;
	if (VALLEY_HEALTH_CHECK_STALL_CHANNEL == ubMode) {
		//stall CH
		FIPSetStopAllocateBuf(FIP_STOP_ALLOCATE_EVENT_SB_WAIT_PATCH_DONE); //Enable Stop QRY Busy, prevent buffer deadlock

		U8 ubIdx = 0 ;
		if (IOR_EN) {
			for (ubIdx = 0 ; ubIdx < gFlhEnv.ubCENumberInCh[pRetryJob->ubChannel] ; ubIdx++) {
				if (gPatchCmdMgr.ulNeedPatchQueueBMP & BIT(M_GLOBAL_Q_IDX(pRetryJob->ubChannel, ubIdx, BIT(gPCARule_Channel.ubBit_No)))) {
					StallResetPatchToInit(M_GLOBAL_Q_IDX(pRetryJob->ubChannel, ubIdx, BIT(gPCARule_Channel.ubBit_No)));
				}
			}
		}
		else {
			for (ubIdx = 0 ; ubIdx < gFlhEnv.ubCENumberInCh[pRetryJob->ubChannel] ; ubIdx++) {
				while (gPatchCmdMgr.ulNeedPatchQueueBMP & BIT(M_GLOBAL_Q_IDX(pRetryJob->ubChannel, ubIdx, BIT(gPCARule_Channel.ubBit_No)))) {
					PatchCmdDetect();
					FIPDelegateCmd();
					FWErrRecorder();
				}
			}
		}
		for (ubCE = 0; ubCE < gFlhEnv.ubCENumberInCh[pRetryJob->ubChannel]; ubCE++) {
			MTP_Stall_Andes(SINGLE_QUEUE, pRetryJob->ubChannel, ubCE);
			MTP_Stall_WaitAndesFlag(SINGLE_QUEUE, pRetryJob->ubChannel, ubCE);
			while (MTP_Check_Stall_Andes(SINGLE_QUEUE, pRetryJob->ubChannel, ubCE) != CHECK_STALL_DONE);	//Wait request Q range stall done
		}

		FIPClearStopAllocateBuf(FIP_STOP_ALLOCATE_EVENT_SB_WAIT_PATCH_DONE); // Disable Stop QRY Busy
	}
	else if (VALLEY_HEALTH_CHECK_UNSTALL_CHANNEL == ubMode) {
		//unstall CH
		for (ubCE = 0; ubCE < gFlhEnv.ubCENumberInCh[pRetryJob->ubChannel]; ubCE++) {
			MTP_SetPatchLite(SINGLE_QUEUE, pRetryJob->ubChannel, ubCE);
		}
	}
}

void MediaScanReadDataPushCmdMT(U8 ubQLC, U8 ubMode, U16 uwTrimAddr)
{
	FlhMT_t MT;
	MTCfg_t uoMTCfg;
	U8 ubMTIdx, ubDieNum;
	U16 uwSLCFPUPtrBin2, uwTLCFPUPtrBin0, uwTLCFPUPtrBin2, uwTLCFPUPtrBin3;
	U16 *puwSLCFPUBin2, *puwTLCFPUBin0, *puwTLCFPUBin2, *puwTLCFPUBin3;

	RetryJob_t *pCurrentRetryJob = &gpRetry->RetryJobList[gpRetry->ubHead];

	//cmd MT
	ubMTIdx = FlaGetFreeMTIndex();
	memcpy((void *)&MT, (void *)(pCurrentRetryJob->pErrorMT), MT_SIZE);

	MT.cmd.btUpdPollingSequence = TRUE;
	ubDieNum = M_FIP_GET_FSA_DIE_NUMBER(MT.cmd.uliFSA0_1, MT.cmd.ALUSelect);
#if (FIP_VIRTUAL_ADDRESS_WORKAROUND_EN)
	M_FIP_SET_POL_SEQUENCE_SELECT(MT.cmd.POL_SEQ_SEL, (ubDieNum ? POL_SEQ_FPU_ENTRY_READ_STATUS_BUSY_20_DIE1 : POL_SEQ_FPU_ENTRY_READ_STATUS_BUSY_20));
#else /* (FIP_VIRTUAL_ADDRESS_WORKAROUND_EN) */
	M_FIP_SET_POL_SEQUENCE_SELECT(MT.cmd.POL_SEQ_SEL, POL_SEQ_FPU_ENTRY_READ_STATUS_BUSY_20); // Pol true ready
#endif /* (FIP_VIRTUAL_ADDRESS_WORKAROUND_EN) */
	MT.cmd.btBusy = TRUE; // Using autopol
	MT.cmd.btiFSAEn = TRUE;

	MT.cmd.btMTPFormat = 0;
	MT.cmd.NormalTargetCPU = MT_TARGET_CPU0;
	MT.cmd.ErrorTargetCPU = MT_TARGET_CPU0;
	MT.cmd.btDisableUDMA = TRUE;

	if (FALSE == ubQLC) { //SLC, MLC
#if (PS5017_EN)
		uwSLCFPUPtrBin2 = FPU_PTR_OFFSET(fpu_entry_tlc_1p_30_read_bin1);
#else
		uwSLCFPUPtrBin2 = FPU_PTR_OFFSET(fpu_entry_tlc_1p_30_read_bin2);
#endif
		puwSLCFPUBin2 = (U16 *)(IRAM_BASE + uwSLCFPUPtrBin2);
		if (MEDIASCAN_READ_CMD == ubMode) {
#if (PS5017_EN)
			MT.cmd.uwFPUPtr = (MEDIA_SCAN_EVENT_NORMAL_QLC_DATA_UNIT == gMediaScanManager.ubEventIdx) ? FPU_PTR_OFFSET(fpu_entry_tlc_1p_30_read) : FPU_PTR_OFFSET(fpu_entry_slc_1p_30_read);
#else
			MT.cmd.uwFPUPtr = (MEDIA_SCAN_EVENT_NORMAL_QLC_DATA_UNIT == gMediaScanManager.ubEventIdx) ? FPU_PTR_OFFSET(fpu_entry_tlc_1p_30_read_bin0) : FPU_PTR_OFFSET(fpu_entry_slc_1p_30_read_bin0);
#endif
		}
		else if (MEDIASCAN_READ_PREFIX_CMD == ubMode) {
			if (MEDIASCAN_SLC_DISABLE_AUTO_READ_CALIBRATION == gMediaScanManager.ubSLCVHCState) { //2Eh+00h-30h
#if (PS5017_EN)
				MT.cmd.uwFPUPtr = (MEDIA_SCAN_EVENT_NORMAL_QLC_DATA_UNIT == gMediaScanManager.ubEventIdx) ? FPU_PTR_OFFSET(fpu_entry_tlc_1p_30_read_bin1) : FPU_PTR_OFFSET(fpu_entry_slc_1p_30_read_bin1);
#else
				MT.cmd.uwFPUPtr = (MEDIA_SCAN_EVENT_NORMAL_QLC_DATA_UNIT == gMediaScanManager.ubEventIdx) ? FPU_PTR_OFFSET(fpu_entry_tlc_1p_30_read_bin2) : FPU_PTR_OFFSET(fpu_entry_slc_1p_30_read_bin2);
#endif
				puwSLCFPUBin2[1] = FPU_ADR_1B((gMediaScanManager.ulFirstCalibrationOffset) & (BIT_MASK(8)));
				puwSLCFPUBin2[2] = FPU_ADR_1B(gMediaScanManager.ubCombineCalibrationOffset);
				puwSLCFPUBin2[3] = FPU_ADR_1B(0x00);
				puwSLCFPUBin2[4] = FPU_ADR_1B(0x00);
			}
		}
		else if (MEDIASCAN_SET_TRIM_CMD == ubMode) {
			MT.cmd.uwFPUPtr = MediaScanPreconditionSetMLBiFPU(uwTrimAddr, ubDieNum);
		}
#if MEDIA_SCAN_DEBUG_CHECK_READ_LEVEL_BY_MT_EN
		else if (MEDIASCAN_GET_TRIM_CMD == ubMode) {
			MT.cmd.uwFPUPtr = MediaScanPreconditionGetMLBiFPU(ubQLC, uwTrimAddr, ubDieNum);
		}
#endif /* MEDIA_SCAN_DEBUG_CHECK_READ_LEVEL_BY_MT_EN */
	}
	else {
#if (PS5017_EN)
		uwTLCFPUPtrBin0 = FPU_PTR_OFFSET(fpu_entry_tlc_1p_30_read);
		uwTLCFPUPtrBin2 = FPU_PTR_OFFSET(fpu_entry_tlc_1p_30_read_bin1);
		uwTLCFPUPtrBin3 = FPU_PTR_OFFSET(fpu_entry_tlc_1p_30_read_bin2);
#else
		uwTLCFPUPtrBin0 = FPU_PTR_OFFSET(fpu_entry_tlc_1p_30_read_bin0);
		uwTLCFPUPtrBin2 = FPU_PTR_OFFSET(fpu_entry_tlc_1p_30_read_bin2);
		uwTLCFPUPtrBin3 = FPU_PTR_OFFSET(fpu_entry_tlc_1p_30_read_bin3);
#endif
		puwTLCFPUBin0 = (U16 *)(IRAM_BASE + uwTLCFPUPtrBin0);
		puwTLCFPUBin2 = (U16 *)(IRAM_BASE + uwTLCFPUPtrBin2);
		puwTLCFPUBin3 = (U16 *)(IRAM_BASE + uwTLCFPUPtrBin3);
		if (MEDIASCAN_READ_CMD == ubMode) {
#if (PS5017_EN)
			MT.cmd.uwFPUPtr = FPU_PTR_OFFSET(fpu_entry_tlc_1p_30_read);
#else
			MT.cmd.uwFPUPtr = FPU_PTR_OFFSET(fpu_entry_tlc_1p_30_read_bin0);
#endif
			if (MEDIASCAN_QLC_READ_SAMPLING_OFFSET == gMediaScanManager.ubQLCVHCState) {
#if ((PS5017_EN) || (PS5021_EN))
#if (VIRTUAL_ADDRESS_EN)
				// Virtual Address Setting
				if (FIP_INVALID_VIRTUAL_ADDR != DISABLE_COARSE_FINE_ADR_VALUE) {
					MT.cmd.VA0 = DISABLE_COARSE_FINE_ADR_VALUE;
					MT.cmd.VAMode = 1;
				}
#endif /* (VIRTUAL_ADDRESS_EN) */
#else /* ((PS5017_EN) || (PS5021_EN)) */
				puwTLCFPUBin3[3] = FPU_ADR_1B(DISABLE_COARSE_FINE_ADR_VALUE);
#endif /* ((PS5017_EN) || (PS5021_EN)) */
			}
		}
		else if (MEDIASCAN_READ_PREFIX_CMD == ubMode) {
			switch (gMediaScanManager.ubQLCVHCState) {
			case MEDIASCAN_QLC_FINE_CALIBRATION: //2Eh+00h-30h
#if (PS5017_EN)
				MT.cmd.uwFPUPtr = FPU_PTR_OFFSET(fpu_entry_tlc_1p_30_read_bin1);
#else
				MT.cmd.uwFPUPtr = FPU_PTR_OFFSET(fpu_entry_tlc_1p_30_read_bin2);
#endif
				puwTLCFPUBin2[1] = FPU_ADR_1B((gMediaScanManager.ulFirstCalibrationOffset) & (BIT_MASK(8)));
				puwTLCFPUBin2[2] = FPU_ADR_1B((gMediaScanManager.ulFirstCalibrationOffset >> 8) & (BIT_MASK(8)));
				puwTLCFPUBin2[3] = FPU_ADR_1B((gMediaScanManager.ulFirstCalibrationOffset >> 16) & (BIT_MASK(8)));
				puwTLCFPUBin2[4] = FPU_ADR_1B((gMediaScanManager.ulFirstCalibrationOffset >> 24) & (BIT_MASK(8)));
				puwTLCFPUBin3[3] = FPU_ADR_1B(0x00);
				break;
			case MEDIASCAN_QLC_DISABLE_CALIBRATION: //2Eh+00h-30h
#if (PS5017_EN)
				MT.cmd.uwFPUPtr = FPU_PTR_OFFSET(fpu_entry_tlc_1p_30_read_bin1);
#else
				MT.cmd.uwFPUPtr = FPU_PTR_OFFSET(fpu_entry_tlc_1p_30_read_bin2);
#endif
				puwTLCFPUBin2[1] = FPU_ADR_1B((((gMediaScanManager.ulFirstCalibrationOffset) & (BIT_MASK(8))) + ((gMediaScanManager.ulSecondCalibrationOffset) & (BIT_MASK(8)))) & BIT_MASK(8));
				puwTLCFPUBin2[2] = FPU_ADR_1B((((gMediaScanManager.ulFirstCalibrationOffset >> 8) & (BIT_MASK(8))) + ((gMediaScanManager.ulSecondCalibrationOffset >> 8) & (BIT_MASK(8)))) & BIT_MASK(8));
				puwTLCFPUBin2[3] = FPU_ADR_1B((((gMediaScanManager.ulFirstCalibrationOffset >> 16) & (BIT_MASK(8))) + ((gMediaScanManager.ulSecondCalibrationOffset >> 16) & (BIT_MASK(8)))) & BIT_MASK(8));
				puwTLCFPUBin2[4] = FPU_ADR_1B((((gMediaScanManager.ulFirstCalibrationOffset >> 24) & (BIT_MASK(8))) + ((gMediaScanManager.ulSecondCalibrationOffset >> 24) & (BIT_MASK(8)))) & BIT_MASK(8));
				puwTLCFPUBin3[3] = FPU_ADR_1B(DISABLE_COARSE_FINE_ADR_VALUE);
				break;
			}
		}
		else if (MEDIASCAN_SET_TRIM_CMD == ubMode) {
			//MT.cmd.FCLK_DIV = 0x3F; // decrease clk rate
			//MT.cmd.btFCLK_DIV_EN = TRUE;
			MT.cmd.uwFPUPtr = MediaScanPreconditionSetMLBiFPU(uwTrimAddr, ubDieNum);
		}
#if MEDIA_SCAN_DEBUG_CHECK_READ_LEVEL_BY_MT_EN
		else if (MEDIASCAN_GET_TRIM_CMD == ubMode) {
			//MT.cmd.FCLK_DIV = 0x3F; // decrease clk rate
			//MT.cmd.btFCLK_DIV_EN = TRUE;
			MT.cmd.uwFPUPtr = MediaScanPreconditionGetMLBiFPU(ubQLC, uwTrimAddr, ubDieNum);
		}
#endif /* MEDIA_SCAN_DEBUG_CHECK_READ_LEVEL_BY_MT_EN */
	}
	memcpy((void *)M_MT_ADDR(ubMTIdx), &MT, MT_SIZE);

	uoMTCfg.uoAll = 0;
	uoMTCfg.bits_recc.ubQUE_IDX = MT.dma.ubCEValue;
	uoMTCfg.bits_recc.ubMT_IDX = ubMTIdx;
	FlaGlobalTrigger(&uoMTCfg);

	while (1) {
		if (gMTMgr.ubMTDoneMsg[ubMTIdx - MT_RETRY_START_INDEX].btAllDone) {
			FlaAddFreeMTIndex(ubMTIdx);
			break;
		}
		FIPDelegateCmd();
		FWErrRecorder();
	}
}

void MediaScanReadDataPushDMAMT(U8 ubQLC, U8 ubMode, U16 uwTrimAddr)
{
	FlhMT_t MT;
	L4KTable16B_t *L4kPtr = NULL;
	MTCfg_t uoMTCfg;
	U8 ubMTIdx, ubFrameIndex, ubi;
	U16 uwTLCFPUPtr;
	U16 *puwTLCFPU;

	RetryJob_t *pCurrentRetryJob = &gpRetry->RetryJobList[gpRetry->ubHead];

	// DMA MT
	ubMTIdx = FlaGetFreeMTIndex();
	memcpy((void *)&MT, (void *)(pCurrentRetryJob->pErrorMT), MT_SIZE);
	gMTMgr.ubRecordExtInfoMT = ubMTIdx; // for FIPDelegate record BEC when EOT

	MT.dma.btBufferMode = 0;	//0:physical addr.
	MT.dma.btZipEn = FALSE;
	MT.dma.btConversionBypass = TRUE;
	MT.dma.btCRCCheckDis = TRUE;
	MT.dma.btCompareEn = FALSE;
	MT.dma.btGC = FALSE;
#if (PS5017_EN)
	MT.dma.btFpuPCAEn = TRUE;
	MT.dma.EOTChkEn = TRUE;
#endif /* (PS5017_EN) */
	MT.dma.btForce_R_Fail = FALSE;
	MT.dma.btMTPFormat = 1;
	MT.dma.NorTarCPU = MT_TARGET_CPU0;
	MT.dma.ErrTarCPU = MT_TARGET_CPU0;
	MT.dma.btBMUAllocateEn = FALSE;
	MT.dma.btDisableUDMA = TRUE;
	MT.dma.L4KSparePtr = SPR_RETRY_OFF;

	if (MEDIASCAN_GET_EC == ubMode) {	//SLC/MLC RSO, QLC RSO
		MT.dma.btLDPCCorrectEn = TRUE;
		MT.dma.FrameNum = 4;
		for (ubFrameIndex = 0; ubFrameIndex < FRAMES_PER_PAGE; ubFrameIndex++) {
			L4kPtr = (L4KTable16B_t *)(IRAM_BASE + SPR_RETRY_OFF + ubFrameIndex * L4K_SIZE);
			L4kPtr->BitMap.Read.ubBufferValid = 0;
			L4kPtr->BitMap.Read.Zinfo = MAX_ZINFO;
		}
	}
	else {
		if (MEDIASCAN_GET_READ_OFFSET == ubMode) { 	//SLC/MLC Get read level
#if (PS5017_EN)
			uwTLCFPUPtr = FPU_PTR_OFFSET(fpu_entry_tlc_1p_20_read_bin1);
#else
			uwTLCFPUPtr = FPU_PTR_OFFSET(fpu_entry_tlc_1p_20_read_bin2);
#endif
			puwTLCFPU = (U16 *)(IRAM_BASE + uwTLCFPUPtr);
			puwTLCFPU[4] = FPU_DLY(M_FPU_DELAY_COUNT(600, gMediaScanManager.uwFIPClock));
#if (PS5017_EN)
			MT.dma.uwFPUPtr = FPU_PTR_OFFSET(fpu_entry_tlc_1p_20_read_bin1);
#else
			MT.dma.uwFPUPtr = FPU_PTR_OFFSET(fpu_entry_tlc_1p_20_read_bin2);
#endif
		}
#if MEDIA_SCAN_DEBUG_CHECK_READ_LEVEL_BY_MT_EN
		else if (MEDIASCAN_GET_READ_LEVEL == ubMode) { 	//QLC get ofst
			MT.dma.uwFPUPtr = FPU_PTR_OFFSET(fpu_entry_getfeature_dma); // read data no ADR_GEN
		}
#endif /* MEDIA_SCAN_DEBUG_CHECK_READ_LEVEL_BY_MT_EN */
#if MEDIA_SCAN_DEBUG_EN
		for (ubi = 0; ubi < gpRetry->ubCnt; ubi++) {
			UartPrintf("\n\t[Retry_ET]\t%d\t%d", ubi, gpRetry->RetryJobList[gpRetry->ubHead + ubi].ubErrorType);
		}
#endif /* MEDIA_SCAN_DEBUG_EN */

		// Allocate retry buffer
		if (gpRetry->ubAllocatedRetryBufferFrame == 0) {
			BufferAllocateFWLBPBLink(FWLB_READ_RETRY_BIT, ALLOCATE_NO_USE_CTAG, ALLOCATE_NO_USE_LCA);
			gpRetry->ubAllocatedRetryBufferFrame += guwFWFlowLBAmount[FWLB_READ_RETRY];
		}
		for (ubi = 0; ubi  < FRAMES_PER_PAGE; ubi ++) {
			gpRetry->ulBufAddr[ubi] = M_LB_TO_ADDR(LB_ID_FW, gFWLBMgr.Type[FWLB_READ_RETRY].uwLBOffset + ubi);
		}

		MT.dma.btLDPCCorrectEn = FALSE;
		MT.dma.FrameNum = 1;
		L4kPtr = (L4KTable16B_t *)(IRAM_BASE + SPR_RETRY_OFF);
		L4kPtr->BitMap.Read.ubBufferValid = 0x01; //1 sector
		L4kPtr->BitMap.Read.Zinfo = MAX_ZINFO;
		L4kPtr->BitMap.Read.PCA = 0;
		L4kPtr->BitMap.Read.BADR = M_LB_TO_ADDR(LB_ID_FW, gFWLBMgr.Type[FWLB_READ_RETRY].uwLBOffset) >> SECTOR_SIZE_SHIFT;	//align 512
	}
	memcpy((void *)M_MT_ADDR(ubMTIdx), &MT, MT_SIZE);

	uoMTCfg.uoAll = 0;
	uoMTCfg.bits_recc.ubQUE_IDX = MT.dma.ubCEValue;
	uoMTCfg.bits_recc.ubMT_IDX = ubMTIdx;
	FlaGlobalTrigger(&uoMTCfg);

	while (1) {
		if (gMTMgr.ubMTDoneMsg[ubMTIdx - MT_RETRY_START_INDEX].btAllDone) {
			FlaAddFreeMTIndex(ubMTIdx);
			//get result
			if (MEDIASCAN_GET_READ_OFFSET == ubMode) {
				if (ubQLC) {
					//Readout Valley Track Indicator offset 2 (CBC: bit0-2)
					U8 ubCBC;
					U8 *ubCBCReadOutAddr = (U8 *)(M_LB_TO_ADDR(LB_ID_FW, gFWLBMgr.Type[FWLB_READ_RETRY].uwLBOffset) + 0x2);
					ubCBC = ubCBCReadOutAddr[0];
					ubCBC &= BIT_MASK(3);
					gMediaScanManager.btIsCBC7 = (ubCBC == 7) ? TRUE : FALSE;

					//Readout 07-E0 offset 68,70,72,74(toggle needs 2 byte for 1 data)
					U8 ubReadOutCnt;
					U8 ubReadOutOffset[4];
					U8 *ubReadOutAddr = (U8 *)(M_LB_TO_ADDR(LB_ID_FW, gFWLBMgr.Type[FWLB_READ_RETRY].uwLBOffset) + 0x44);
					for (ubReadOutCnt = 0; ubReadOutCnt < 4; ubReadOutCnt++) {
						ubReadOutOffset[ubReadOutCnt] = ubReadOutAddr[(ubReadOutCnt << 1)];
#if MEDIA_SCAN_DEBUG_EN
						UartPrintf("\n\tubReadOutOffset: %b", ubReadOutOffset[ubReadOutCnt]);
						UartPrintf("\n\tubReadOutAddr[%d]: %b", ubReadOutCnt, &ubReadOutAddr[(ubReadOutCnt << 1)]);
#endif /* MEDIA_SCAN_DEBUG_EN */
					}
					if (MEDIASCAN_QLC_GET_READ_OFFSET == gMediaScanManager.ubQLCVHCState) {
						gMediaScanManager.ulFirstCalibrationOffset = (ubReadOutOffset[0] | (ubReadOutOffset[1] << 8) | (ubReadOutOffset[2] << 16) | (ubReadOutOffset[3] << 24));
					}
					else if (MEDIASCAN_QLC_GET_FINE_READ_OFFSET == gMediaScanManager.ubQLCVHCState) {
						gMediaScanManager.ulSecondCalibrationOffset = (ubReadOutOffset[0] | (ubReadOutOffset[1] << 8) | (ubReadOutOffset[2] << 16) | (ubReadOutOffset[3] << 24));
					}

#if MEDIA_SCAN_DEBUG_EN
					UartPrintf("\n\tCBC:\t%d", ubCBC);
					//check all readout buf
					ValleyTrackReadout38B_t *Readoutbuffer = (ValleyTrackReadout38B_t *)(M_LB_TO_ADDR(LB_ID_FW, gFWLBMgr.Type[FWLB_READ_RETRY].uwLBOffset));
					UartPrintf("\n\t%b\t%b", Readoutbuffer->ubReserve0, Readoutbuffer->ubIndicator);
					UartPrintf("\n");
					for (ubReadOutCnt = 0; ubReadOutCnt < 32; ubReadOutCnt++) {
						UartPrintf("%b", Readoutbuffer->ubReserve1[ubReadOutCnt]);
					}
					UartPrintf("\n");
					for (ubReadOutCnt = 0; ubReadOutCnt < 4; ubReadOutCnt++) {
						UartPrintf("%b", Readoutbuffer->ubCalibrationOffset[ubReadOutCnt]);
					}

					ValleyTrackReadout38B_t *Readoutbuffer2 = (ValleyTrackReadout38B_t *)(M_LB_TO_ADDR(LB_ID_FW, gFWLBMgr.Type[FWLB_READ_RETRY].uwLBOffset) + 0x26);
					UartPrintf("\n\t%b\t%b", Readoutbuffer2->ubReserve0, Readoutbuffer2->ubIndicator);
					UartPrintf("\n");
					for (ubReadOutCnt = 0; ubReadOutCnt < 32; ubReadOutCnt++) {
						UartPrintf("%b", Readoutbuffer2->ubReserve1[ubReadOutCnt]);
					}
					UartPrintf("\n");
					for (ubReadOutCnt = 0; ubReadOutCnt < 4; ubReadOutCnt++) {
						UartPrintf("%b", Readoutbuffer2->ubCalibrationOffset[ubReadOutCnt]);
					}
#endif /* MEDIA_SCAN_DEBUG_EN */
					//release buffer
					if ((gpRetry->ubCnt == 1) && (gFWLBMgr.Type[FWLB_READ_RETRY].uwState == BUFFER_SUCCESS)) {
						BufferFreeFWLBPBLink(FWLB_READ_RETRY_BIT);
						gpRetry->ubAllocatedRetryBufferFrame -= FWLB_READ_RETRY_SIZE_IN_4K;
					}
				}
			}
			else if (MEDIASCAN_GET_EC == ubMode) {
				U32 ulInterruptInfo = gMTMgr.ulINTInfo[ubMTIdx - MT_RETRY_START_INDEX];
				if (ulInterruptInfo & UNCORR_UPD_BIT) {
					gMediaScanManager.btUNC = TRUE;
				}
			}
#if MEDIA_SCAN_DEBUG_CHECK_READ_LEVEL_BY_MT_EN
			else if (MEDIASCAN_GET_READ_LEVEL == ubMode) {
				GetTrimReadout4B_t *Readoutbuffer = (GetTrimReadout4B_t *)(M_LB_TO_ADDR(LB_ID_FW, gFWLBMgr.Type[FWLB_READ_RETRY].uwLBOffset));
				if ((!ubQLC) && (MEDIASCAN_SLC_READ_SAMPLING_OFFSET == gMediaScanManager.ubSLCVHCState)) {
					UartPrintf("\n\tGet MT:\t%d\t%l\t%b\n", gMediaScanManager.ReadSamplingOffset.uwTrimCnt, uwTrimAddr, Readoutbuffer->ubReadOffset);
				}
				else if ((ubQLC) && (MEDIASCAN_QLC_READ_SAMPLING_OFFSET == gMediaScanManager.ubQLCVHCState)) {
					UartPrintf("\n\tGet MT:\t%l\t%b\n", uwTrimAddr, Readoutbuffer->ubReadOffset);
				}
				else if ((!ubQLC) && ((MEDIASCAN_SLC_TEST_START == gMediaScanManager.ubSLCVHCState) || (MEDIASCAN_SLC_TEST_END == gMediaScanManager.ubSLCVHCState))) {
					UartPrintf("\n\tGet MT:\t%d\t%l\t%b\n", gMediaScanManager.ReadSamplingOffset.uwTrimCnt, uwTrimAddr, Readoutbuffer->ubReadOffset);
				}
				else if ((ubQLC) && ((MEDIASCAN_QLC_TEST_START == gMediaScanManager.ubQLCVHCState) || (MEDIASCAN_QLC_TEST_END == gMediaScanManager.ubQLCVHCState))) {
					UartPrintf("\n\tGet MT:\t%l\t%b\n", uwTrimAddr, Readoutbuffer->ubReadOffset);
				}

				//release buffer
				if ((gpRetry->ubCnt == 1) && (gFWLBMgr.Type[FWLB_READ_RETRY].uwState == BUFFER_SUCCESS)) {
					BufferFreeFWLBPBLink(FWLB_READ_RETRY_BIT);
					gpRetry->ubAllocatedRetryBufferFrame -= FWLB_READ_RETRY_SIZE_IN_4K;
				}
			}
#endif /* MEDIA_SCAN_DEBUG_CHECK_READ_LEVEL_BY_MT_EN */
			break;
		}
		FIPDelegateCmd();
		FWErrRecorder();
	}
}

void MediaScanReadSamplingOffset(U8 ubQLC, RetryJob_t *pCurrentRetryJob)
{
	U16 uwSLCTrimAddr[2];
	U16 uwQLCTrimAddr;
#if (!MEDIA_SCAN_DEBUG_EN)
	if (ubQLC) {
		MediaScanStallOrUnstallChannel(pCurrentRetryJob, VALLEY_HEALTH_CHECK_STALL_CHANNEL);
	}
	//setup trim address
	U8 ubDoingWordLineLTrimIdx = 0; //QLC:0~14, TLC:0~6, SLC:0
	U8 ubDoingYCoordIdx = 0;  //WLG: 0 ~ 7 get Coor-Y from array

	if (ubQLC) {
		ubDoingYCoordIdx = FTLPhysicalWordLineGetCoordForNonSLC(0, gMediaScanManager.ubVictimWordline, IM_PHYSICAL_WORDLINE_GETCOORD_WORDLINE_GROUP);
		ubDoingWordLineLTrimIdx = (gMediaScanManager.uwScanIdx[gMediaScanManager.ubEventIdx] == FTLPhysicalWordLineGetCoordForNonSLC(0, gMediaScanManager.ubVictimWordline, IM_PHYSICAL_WORDLINE_GETCOORD_PHYSICAL_PAGE)) ? 0x0 : 0xe;	//LP or TP
		uwQLCTrimAddr = M_VRLC_GET_TRIMREG_FROM_WLG(ubDoingYCoordIdx, ubDoingWordLineLTrimIdx);
	}
	gMediaScanManager.ReadSamplingOffset.uwTest = 0;
#endif /* (!MEDIA_SCAN_DEBUG_EN) */

	//get DAC center offset
	U8 ubFeatureData[4] = {0};
	if (FALSE == ubQLC) { //SLC, MLC: need to get RDT result(VRLC not support)
		gMediaScanManager.ReadSamplingOffset.uwTest++;
		for (gMediaScanManager.ReadSamplingOffset.uwTrimCnt = 0; gMediaScanManager.ReadSamplingOffset.uwTrimCnt < 2; gMediaScanManager.ReadSamplingOffset.uwTrimCnt++) { //get high bit/low bit of read level
			if (MEDIA_SCAN_EVENT_NORMAL_QLC_DATA_UNIT == gMediaScanManager.ubEventIdx) {	//MLC UP
				uwSLCTrimAddr[gMediaScanManager.ReadSamplingOffset.uwTrimCnt] = (0 == gMediaScanManager.ReadSamplingOffset.uwTrimCnt) ? 0x1C0 : 0x1C1;
			}
			else {	//SLC
				if (0 == gMediaScanManager.ReadSamplingOffset.uwTrimCnt) { //1st time
					uwSLCTrimAddr[gMediaScanManager.ReadSamplingOffset.uwTrimCnt] = FTLPhysicalWordLineGetCoordForSLC(0, gMediaScanManager.ubVictimWordline, IM_PHYSICAL_WORDLINE_GETCOORD_IS_EDGE_WORDLINE) ? 0x1D4 : 0x1DE;
				}
				else {	//2nd time
					uwSLCTrimAddr[gMediaScanManager.ReadSamplingOffset.uwTrimCnt] = FTLPhysicalWordLineGetCoordForSLC(0, gMediaScanManager.ubVictimWordline, IM_PHYSICAL_WORDLINE_GETCOORD_IS_EDGE_WORDLINE) ? 0x1D5 : 0x1DF;
				}
			}

			memset(ubFeatureData, 0, sizeof(ubFeatureData));
			FIPGetMLBiForMediaScan(pCurrentRetryJob->ubChannel, pCurrentRetryJob->ubBank, uwSLCTrimAddr[gMediaScanManager.ReadSamplingOffset.uwTrimCnt], ubFeatureData);
			if (0 == gMediaScanManager.ReadSamplingOffset.uwTrimCnt) {	//1st time
				gMediaScanManager.ReadSamplingOffset.uwDACCenterOffset = ubFeatureData[0];
			}
			else {	//2nd time
				gMediaScanManager.ReadSamplingOffset.uwDACCenterOffset |= ((ubFeatureData[0] & BIT_MASK(2)) << 8);
			}
#if MEDIA_SCAN_DEBUG_EN
			UartPrintf("\n\tSLC/MLC Read level:\tTrimCnt\tTrimAddr\tReadLevel");
			UartPrintf("\n\tGet PIO:\t%d\t%l\t%b", gMediaScanManager.ReadSamplingOffset.uwTrimCnt, uwSLCTrimAddr[gMediaScanManager.ReadSamplingOffset.uwTrimCnt], ubFeatureData[0]);
#if MEDIA_SCAN_DEBUG_CHECK_READ_LEVEL_BY_MT_EN
			if (ubFeatureData[0] != 0xFF) {
				MediaScanReadDataPushCmdMT(ubQLC, MEDIASCAN_GET_TRIM_CMD, uwSLCTrimAddr[gMediaScanManager.ReadSamplingOffset.uwTrimCnt]); //get trim CMD
				MediaScanReadDataPushDMAMT(ubQLC, MEDIASCAN_GET_READ_LEVEL, uwSLCTrimAddr[gMediaScanManager.ReadSamplingOffset.uwTrimCnt]); //need buf
			}
#endif /* MEDIA_SCAN_DEBUG_CHECK_READ_LEVEL_BY_MT_EN */
#endif /* MEDIA_SCAN_DEBUG_EN */
		}
	}
	else { //QLC (LP: Lv1, TP:Lv15)
		//N48 VRLC Trim index: 120 (only QLC)
		gMediaScanManager.ReadSamplingOffset.uwTest++;
		memset(ubFeatureData, 0, sizeof(ubFeatureData));
		FIPGetMLBiForMediaScan(pCurrentRetryJob->ubChannel, pCurrentRetryJob->ubBank, uwQLCTrimAddr, ubFeatureData);
		gMediaScanManager.ReadSamplingOffset.uwDACCenterOffset =  ubFeatureData[0];
#if MEDIA_SCAN_DEBUG_EN
		UartPrintf("\n\tQLC Read level:\tTrimAddr\tReadLevel");
		UartPrintf("\n\tGet PIO:\t%l\t%b", uwQLCTrimAddr, ubFeatureData[0]);
#if MEDIA_SCAN_DEBUG_CHECK_READ_LEVEL_BY_MT_EN
		if (ubFeatureData[0] != 0xFF) {
			MediaScanReadDataPushCmdMT(ubQLC, MEDIASCAN_GET_TRIM_CMD, uwQLCTrimAddr); //get trim CMD
			MediaScanReadDataPushDMAMT(ubQLC, MEDIASCAN_GET_READ_LEVEL, uwQLCTrimAddr); //need buf
		}
#endif /* MEDIA_SCAN_DEBUG_CHECK_READ_LEVEL_BY_MT_EN */
#endif /* MEDIA_SCAN_DEBUG_EN */

		if (LOWER == (gMediaScanManager.uwScanIdx[gMediaScanManager.ubEventIdx] % PAGE_NUM)) {	//LP: lv1
			gMediaScanManager.ubCombineCalibrationOffset = (U8)((gMediaScanManager.ulFirstCalibrationOffset) & (BIT_MASK(8))) + (U8)((gMediaScanManager.ulSecondCalibrationOffset) & (BIT_MASK(8)));
		}
		else {	//TP: lv15
			gMediaScanManager.ubCombineCalibrationOffset = (U8)((gMediaScanManager.ulFirstCalibrationOffset >> 24) & (BIT_MASK(8))) + (U8)((gMediaScanManager.ulSecondCalibrationOffset >> 24) & (BIT_MASK(8)));
		}
	}

	//dummy read
	MediaScanReadDataPushCmdMT(ubQLC, MEDIASCAN_READ_CMD, 0); //Read CMD
#if MEDIA_SCAN_DEBUG_DATA_FOR_MICRON_UART_EN
	UartPrintf("\n\n\tFirst, Second, Combine:\t%l\t%l\t%b", gMediaScanManager.ulFirstCalibrationOffset, gMediaScanManager.ulSecondCalibrationOffset, gMediaScanManager.ubCombineCalibrationOffset);
#endif /* MEDIA_SCAN_DEBUG_DATA_FOR_MICRON_UART_EN */
#if MEDIA_SCAN_DEBUG_EN
	UartPrintf("\n\n\n\tDAC center offset:\t%l", gMediaScanManager.ReadSamplingOffset.uwDACCenterOffset);
	UartPrintf("\n\tFirst, Second, Combine:\t%l\t%l\t%b", gMediaScanManager.ulFirstCalibrationOffset, gMediaScanManager.ulSecondCalibrationOffset, gMediaScanManager.ubCombineCalibrationOffset);

	if (ubQLC) {
		gMediaScanManager.ReadSamplingOffset.uwTest++;
		memset(ubFeatureData, 0, sizeof(ubFeatureData));
		FIPGetMLBiForMediaScan(pCurrentRetryJob->ubChannel, pCurrentRetryJob->ubBank, uwQLCTrimAddr, ubFeatureData);
		UartPrintf("\n\tQLC Read level:\tTrimAddr\tReadLevel");
		UartPrintf("\n\tGet PIO:\t%l\t%b", uwQLCTrimAddr, ubFeatureData[0]);
#if MEDIA_SCAN_DEBUG_CHECK_READ_LEVEL_BY_MT_EN
		if (ubFeatureData[0] != 0xFF) {
			MediaScanReadDataPushCmdMT(ubQLC, MEDIASCAN_GET_TRIM_CMD, uwQLCTrimAddr); //get trim CMD
			MediaScanReadDataPushDMAMT(ubQLC, MEDIASCAN_GET_READ_LEVEL, uwQLCTrimAddr); //need buf
		}
#endif /* MEDIA_SCAN_DEBUG_CHECK_READ_LEVEL_BY_MT_EN */
	}
#endif /* MEDIA_SCAN_DEBUG_EN */
	gMediaScanManager.ReadSamplingOffset.uwDoingReadSamplingOffset = TRUE;

	//Left, Right, Center
	for (U8 ubRSOReadCnt = 0; ubRSOReadCnt < VRLC_NUM; ubRSOReadCnt++) {
		U8 ubAGCOffset;
		if (ubQLC) {
			ubAGCOffset = (LOWER == (gMediaScanManager.uwScanIdx[gMediaScanManager.ubEventIdx] % PAGE_NUM)) ? gubMediaScanAGCOffsetArray[1] : gubMediaScanAGCOffsetArray[2];	//LP or TP

			switch (ubRSOReadCnt) {
			case VRLC_LEFT:
				gMediaScanManager.ReadSamplingOffset.ubReadLevel = ((gMediaScanManager.ReadSamplingOffset.uwDACCenterOffset - ubAGCOffset) & BIT_MASK(8));
				break;
			case VRLC_RIGHT:
				gMediaScanManager.ReadSamplingOffset.ubReadLevel = ((gMediaScanManager.ReadSamplingOffset.uwDACCenterOffset + ubAGCOffset) & BIT_MASK(8));
				break;
			case VRLC_CENTER:
				gMediaScanManager.ReadSamplingOffset.ubReadLevel = (gMediaScanManager.ReadSamplingOffset.uwDACCenterOffset & BIT_MASK(8));
				break;
			}
			MediaScanReadDataPushCmdMT(ubQLC, MEDIASCAN_SET_TRIM_CMD, uwQLCTrimAddr); //set trim CMD
#if MEDIA_SCAN_DEBUG_EN
			UartPrintf("\n\tQLC Read level:\tTrimAddr\tReadLevel");
			UartPrintf("\n\tSet MT:\t%l\t%b", uwQLCTrimAddr, gMediaScanManager.ReadSamplingOffset.ubReadLevel);
			//MediaScanReadDataPushCmdMT(ubQLC, MEDIASCAN_GET_TRIM_CMD, uwQLCTrimAddr); //get trim CMD
			//MediaScanReadDataPushDMAMT(ubQLC, MEDIASCAN_GET_READ_LEVEL, uwQLCTrimAddr); //need buf

			gMediaScanManager.ReadSamplingOffset.uwTest++;
			memset(ubFeatureData, 0, sizeof(ubFeatureData));
			FIPGetMLBiForMediaScan(pCurrentRetryJob->ubChannel, pCurrentRetryJob->ubBank, uwQLCTrimAddr, ubFeatureData);
			UartPrintf("\n\tGet PIO:\t%l\t%b", uwQLCTrimAddr, ubFeatureData[0]);
#if MEDIA_SCAN_DEBUG_CHECK_READ_LEVEL_BY_MT_EN
			if (ubFeatureData[0] != 0xFF) {
				MediaScanReadDataPushCmdMT(ubQLC, MEDIASCAN_GET_TRIM_CMD, uwQLCTrimAddr); //get trim CMD
				MediaScanReadDataPushDMAMT(ubQLC, MEDIASCAN_GET_READ_LEVEL, uwQLCTrimAddr); //need buf
			}
#endif /* MEDIA_SCAN_DEBUG_CHECK_READ_LEVEL_BY_MT_EN */
#endif /* MEDIA_SCAN_DEBUG_EN */
		}
		else {	//SLC, MLC
			ubAGCOffset = gubMediaScanAGCOffsetArray[0];
			gMediaScanManager.ReadSamplingOffset.uwTrimCnt = 0;
			gMediaScanManager.ReadSamplingOffset.uwTest++;
			for (gMediaScanManager.ReadSamplingOffset.uwTrimCnt = 0;  gMediaScanManager.ReadSamplingOffset.uwTrimCnt < 2; gMediaScanManager.ReadSamplingOffset.uwTrimCnt++) {
				switch (ubRSOReadCnt) {
				case VRLC_LEFT:
					gMediaScanManager.ReadSamplingOffset.ubReadLevel = (0 == gMediaScanManager.ReadSamplingOffset.uwTrimCnt) ? ((gMediaScanManager.ReadSamplingOffset.uwDACCenterOffset - ubAGCOffset) & BIT_MASK(8)) : (((gMediaScanManager.ReadSamplingOffset.uwDACCenterOffset - ubAGCOffset) >> 8) & BIT_MASK(2));
					break;
				case VRLC_RIGHT:
					gMediaScanManager.ReadSamplingOffset.ubReadLevel = (0 == gMediaScanManager.ReadSamplingOffset.uwTrimCnt) ? ((gMediaScanManager.ReadSamplingOffset.uwDACCenterOffset + ubAGCOffset) & BIT_MASK(8)) : (((gMediaScanManager.ReadSamplingOffset.uwDACCenterOffset + ubAGCOffset) >> 8) & BIT_MASK(2));
					break;
				case VRLC_CENTER:
					gMediaScanManager.ReadSamplingOffset.ubReadLevel = (0 == gMediaScanManager.ReadSamplingOffset.uwTrimCnt) ? (gMediaScanManager.ReadSamplingOffset.uwDACCenterOffset & BIT_MASK(8)) : ((gMediaScanManager.ReadSamplingOffset.uwDACCenterOffset >> 8) & BIT_MASK(2));
					break;
				}
				MediaScanReadDataPushCmdMT(ubQLC, MEDIASCAN_SET_TRIM_CMD, uwSLCTrimAddr[gMediaScanManager.ReadSamplingOffset.uwTrimCnt]); //set trim CMD
#if MEDIA_SCAN_DEBUG_EN
				UartPrintf("\n\tSLC/MLC Read level:\tTrimCnt\tTrimAddr\tReadLevel");
				UartPrintf("\n\tSet MT:\t%d\t%l\t%b", gMediaScanManager.ReadSamplingOffset.uwTrimCnt, uwSLCTrimAddr[gMediaScanManager.ReadSamplingOffset.uwTrimCnt], gMediaScanManager.ReadSamplingOffset.ubReadLevel);
				//MediaScanReadDataPushCmdMT(ubQLC, MEDIASCAN_GET_TRIM_CMD, uwSLCTrimAddr[gMediaScanManager.RSO.uwTrimCount]); //get trim CMD
				//MediaScanReadDataPushDMAMT(ubQLC, MEDIASCAN_GET_READ_LEVEL, uwSLCTrimAddr[gMediaScanManager.RSO.uwTrimCount]); //need buf

				memset(ubFeatureData, 0, sizeof(ubFeatureData));
				FIPGetMLBiForMediaScan(pCurrentRetryJob->ubChannel, pCurrentRetryJob->ubBank, uwSLCTrimAddr[gMediaScanManager.ReadSamplingOffset.uwTrimCnt], ubFeatureData);
				UartPrintf("\n\tGet PIO:\t%d\t%l\t%b", gMediaScanManager.ReadSamplingOffset.uwTrimCnt, uwSLCTrimAddr[gMediaScanManager.ReadSamplingOffset.uwTrimCnt], ubFeatureData[0]);
#if MEDIA_SCAN_DEBUG_CHECK_READ_LEVEL_BY_MT_EN
				if (ubFeatureData[0] != 0xFF) {
					MediaScanReadDataPushCmdMT(ubQLC, MEDIASCAN_GET_TRIM_CMD, uwSLCTrimAddr[gMediaScanManager.ReadSamplingOffset.uwTrimCnt]); //get trim CMD
					MediaScanReadDataPushDMAMT(ubQLC, MEDIASCAN_GET_READ_LEVEL, uwSLCTrimAddr[gMediaScanManager.ReadSamplingOffset.uwTrimCnt]); //need buf
				}
#endif /* MEDIA_SCAN_DEBUG_CHECK_READ_LEVEL_BY_MT_EN */
#endif /* MEDIA_SCAN_DEBUG_EN */
			}
		}

		MediaScanReadDataPushCmdMT(ubQLC, MEDIASCAN_READ_CMD, 0); //Read CMD
		MediaScanReadDataPushDMAMT(ubQLC, MEDIASCAN_GET_EC, 0); //Get EC

		if ((gMediaScanManager.btUNC) || (M_FIP_GET_ECC_INF(pCurrentRetryJob->ubChannel) > (MEDIA_SCAN_READ_BIT_ERROR_COUNT_UNC_LIMIT >> 3))) {
			gMediaScanManager.ReadSamplingOffset.uwErrorCountPerCodeWord[ubRSOReadCnt] = (MEDIA_SCAN_READ_BIT_ERROR_COUNT_UNC_LIMIT >> 3);
		}
		else {
			gMediaScanManager.ReadSamplingOffset.uwErrorCountPerCodeWord[ubRSOReadCnt] = M_FIP_GET_ECC_INF(pCurrentRetryJob->ubChannel);
		}
#if MEDIA_SCAN_DEBUG_EN
		UartPrintf("\n\tEC[%d]:\t%l\t%l", ubRSOReadCnt, gMediaScanManager.ReadSamplingOffset.uwErrorCountPerCodeWord[ubRSOReadCnt], gMTMgr.uwECCErrorBitNum);
#endif /* MEDIA_SCAN_DEBUG_EN */
	}
	gMediaScanManager.ReadSamplingOffset.uwDoingReadSamplingOffset = FALSE;
}
void MediaScanValleyHealthCheckForQLCWordLine(RetryJob_t *pRetryJob)
{
	U8 ubFeatureData[4] = {0};

	//Read Verify finish: Read with 7th addr. Cycle = 10h (default)
	switch (gMediaScanManager.ubQLCVHCState) {
#if MEDIA_SCAN_DEBUG_EN
	case MEDIASCAN_QLC_TEST_START:
		UartPrintf("\n\n\n\tQLC default EC:\t%l\t%l", (gMediaScanManager.EOT.uwCurrentErrBitNum << 3), gMediaScanManager.EOT.uwCurrentErrBitNum);
		MediaScanStallOrUnstallChannel(pRetryJob, VALLEY_HEALTH_CHECK_STALL_CHANNEL);
#if MEDIA_SCAN_DEBUG_CHECK_READ_OFFSET_PREFIX_EN
		gMediaScanManager.ReadSamplingOffset.ubFeatureAddr = 0xC1;
		for (U8 ubTrimTestCnt = 0; ubTrimTestCnt < 0xF; ubTrimTestCnt++) {
			memset(ubFeatureData, 0, sizeof(ubFeatureData));
			FIPGetFeatureForMediaScan(pRetryJob->ubChannel, pRetryJob->ubBank, gMediaScanManager.ReadSamplingOffset.ubFeatureAddr, ubFeatureData);
			UartPrintf("\n\tQLC Read level:\t%d\t%d\t%b\t%b\t%b\t%b", gMediaScanManager.ubQLCVHCState, ubTrimTestCnt, ubFeatureData[0], ubFeatureData[1], ubFeatureData[2], ubFeatureData[3]);
			gMediaScanManager.ReadSamplingOffset.ubFeatureAddr += 1;
		}
#endif /* MEDIA_SCAN_DEBUG_CHECK_READ_OFFSET_PREFIX_EN */
		U8 ubDoingWordLineLTrimIdx = 0; //QLC:0~14, TLC:0~6, SLC:0
		U8 ubDoingYCoordIdx = 0;  //WLG: 0 ~ 7 get Coor-Y from array
		U8 uwQLCTrimAddr;
		ubDoingYCoordIdx = FTLPhysicalWordLineGetCoordForNonSLC(0, gMediaScanManager.ubVictimWordline, IM_PHYSICAL_WORDLINE_GETCOORD_WORDLINE_GROUP);
		ubDoingWordLineLTrimIdx = (gMediaScanManager.uwScanIdx[gMediaScanManager.ubEventIdx] == FTLPhysicalWordLineGetCoordForNonSLC(0, gMediaScanManager.ubVictimWordline, IM_PHYSICAL_WORDLINE_GETCOORD_PHYSICAL_PAGE)) ? 0x0 : 0xe;	//LP or TP
		uwQLCTrimAddr = M_VRLC_GET_TRIMREG_FROM_WLG(ubDoingYCoordIdx, ubDoingWordLineLTrimIdx);
		gMediaScanManager.ReadSamplingOffset.uwTest = 0;

		UartPrintf("\n\n\n\tTest Start:");
		memset(ubFeatureData, 0, sizeof(ubFeatureData));
		FIPGetMLBiForMediaScan(pRetryJob->ubChannel, pRetryJob->ubBank, uwQLCTrimAddr, ubFeatureData);
		UartPrintf("\n\tQLC Read level:\tTrimAddr\tReadLevel");
		UartPrintf("\n\tGet PIO:\t%l\t%b", uwQLCTrimAddr, ubFeatureData[0]);
#if MEDIA_SCAN_DEBUG_CHECK_READ_LEVEL_BY_MT_EN
		if (ubFeatureData[0] != 0xFF) {
			MediaScanReadDataPushCmdMT(TRUE, MEDIASCAN_GET_TRIM_CMD, uwQLCTrimAddr); //get trim CMD
			MediaScanReadDataPushDMAMT(TRUE, MEDIASCAN_GET_READ_LEVEL, uwQLCTrimAddr); //need buf
		}
#endif /* MEDIA_SCAN_DEBUG_CHECK_READ_LEVEL_BY_MT_EN */
		gMediaScanManager.ubQLCVHCState = MEDIASCAN_QLC_GET_READ_OFFSET;
#endif /* MEDIA_SCAN_DEBUG_EN */
	case MEDIASCAN_QLC_GET_READ_OFFSET:
		MediaScanReadDataPushDMAMT(TRUE, MEDIASCAN_GET_READ_OFFSET, 0); //07h-E0h
		if (gMediaScanManager.btIsCBC7) {	//If CBC =7, do not need to do VHC
			gMediaScanManager.ubQLCVHCState = 0; //Reset state for each page
			break;
		}
		else {
			gMediaScanManager.ubQLCVHCState = MEDIASCAN_QLC_FINE_CALIBRATION;
		}

	case MEDIASCAN_QLC_FINE_CALIBRATION:
		MediaScanReadDataPushCmdMT(TRUE, MEDIASCAN_READ_PREFIX_CMD, 0); //2Eh + 00h-30h + 00h(7th addr.)
		gMediaScanManager.ubQLCVHCState = MEDIASCAN_QLC_GET_FINE_READ_OFFSET;

	case MEDIASCAN_QLC_GET_FINE_READ_OFFSET:
		MediaScanReadDataPushDMAMT(TRUE, MEDIASCAN_GET_READ_OFFSET, 0); //07h-E0h
		gMediaScanManager.ubQLCVHCState = MEDIASCAN_QLC_DISABLE_CALIBRATION;

	case MEDIASCAN_QLC_DISABLE_CALIBRATION:
		MediaScanReadDataPushCmdMT(TRUE, MEDIASCAN_READ_PREFIX_CMD, 0); //2Eh + 00h-30h + 60h(7th addr.)
#if MEDIA_SCAN_DEBUG_CHECK_READ_OFFSET_PREFIX_EN
		gMediaScanManager.ReadSamplingOffset.ubFeatureAddr = 0xC1;
		for (U8 ubTrimTestCnt = 0; ubTrimTestCnt < 0xF; ubTrimTestCnt++) {
			memset(ubFeatureData, 0, sizeof(ubFeatureData));
			FIPGetFeatureForMediaScan(pRetryJob->ubChannel, pRetryJob->ubBank, gMediaScanManager.ReadSamplingOffset.ubFeatureAddr, ubFeatureData);
			UartPrintf("\n\tQLC Read level:\t%d\t%d\t%b\t%b\t%b\t%b", gMediaScanManager.ubQLCVHCState, ubTrimTestCnt, ubFeatureData[0], ubFeatureData[1], ubFeatureData[2], ubFeatureData[3]);
			gMediaScanManager.ReadSamplingOffset.ubFeatureAddr += 1;
		}
#endif /* MEDIA_SCAN_DEBUG_CHECK_READ_OFFSET_PREFIX_EN */
		gMediaScanManager.ubQLCVHCState = MEDIASCAN_QLC_READ_SAMPLING_OFFSET;

	case MEDIASCAN_QLC_READ_SAMPLING_OFFSET:
		MediaScanReadSamplingOffset(TRUE, pRetryJob); //RSO Read (LP: Lv1, TP: Lv15)
		gMediaScanManager.ubQLCVHCState = MEDIASCAN_QLC_CLEAR_PREFIX_READ;

	case MEDIASCAN_QLC_CLEAR_PREFIX_READ:
		memset(ubFeatureData, 0, sizeof(ubFeatureData));
		FIPSetFeatureForMediaScan(pRetryJob->ubChannel, pRetryJob->ubBank, CLEAR_READ_PREFIX_ADDRESS, ubFeatureData); //set feature FA 96h = C0h
#if MEDIA_SCAN_DEBUG_EN
		gMediaScanManager.ubQLCVHCState = MEDIASCAN_QLC_TEST_END;

	case MEDIASCAN_QLC_TEST_END:
		gMediaScanManager.ReadSamplingOffset.uwTest++;
		memset(ubFeatureData, 0, sizeof(ubFeatureData));
		FIPGetMLBiForMediaScan(pRetryJob->ubChannel, pRetryJob->ubBank, uwQLCTrimAddr, ubFeatureData);
		UartPrintf("\n\tQLC Read level:\tTrimAddr\tReadLevel");
		UartPrintf("\n\tGet PIO:\t%l\t%b", uwQLCTrimAddr, ubFeatureData[0]);
#if MEDIA_SCAN_DEBUG_CHECK_READ_LEVEL_BY_MT_EN
		if (ubFeatureData[0] != 0xFF) {
			MediaScanReadDataPushCmdMT(TRUE, MEDIASCAN_GET_TRIM_CMD, uwQLCTrimAddr); //get trim CMD
			MediaScanReadDataPushDMAMT(TRUE, MEDIASCAN_GET_READ_LEVEL, uwQLCTrimAddr); //need buf
		}
#endif /* MEDIA_SCAN_DEBUG_CHECK_READ_LEVEL_BY_MT_EN */
#if MEDIA_SCAN_DEBUG_CHECK_READ_OFFSET_PREFIX_EN
		gMediaScanManager.ReadSamplingOffset.ubFeatureAddr = 0xC1;
		for (U8 ubTrimTestCnt = 0; ubTrimTestCnt < 0xF; ubTrimTestCnt++) {
			memset(ubFeatureData, 0, sizeof(ubFeatureData));
			FIPGetFeatureForMediaScan(pRetryJob->ubChannel, pRetryJob->ubBank, gMediaScanManager.ReadSamplingOffset.ubFeatureAddr, ubFeatureData);
			UartPrintf("\n\tQLC Read level:\t%d\t%d\t%b\t%b\t%b\t%b", gMediaScanManager.ubQLCVHCState, ubTrimTestCnt, ubFeatureData[0], ubFeatureData[1], ubFeatureData[2], ubFeatureData[3]);
			gMediaScanManager.ReadSamplingOffset.ubFeatureAddr += 1;
		}
#endif /* MEDIA_SCAN_DEBUG_CHECK_READ_OFFSET_PREFIX_EN */
#endif /* MEDIA_SCAN_DEBUG_EN */
		MediaScanStallOrUnstallChannel(pRetryJob, VALLEY_HEALTH_CHECK_UNSTALL_CHANNEL);
		//Reset state for each page
		gMediaScanManager.ubQLCVHCState = 0;	//MEDIASCAN_QLC_TEST_START or MEDIASCAN_QLC_GET_READ_OFFSET //MEDIASCAN_QLC_PAGE_SCAN_FLOW_FINISH;
		break;
	}
}

void MediaScanValleyHealthCheckForSLCAndMLCWordLine(RetryJob_t *pRetryJob)
{
	U8 ubFeatureData[4] = {0};

	//Read Verify finish: Read with 7th addr. Cycle = 00h (default)
	switch (gMediaScanManager.ubSLCVHCState) {
#if MEDIA_SCAN_DEBUG_EN
	case MEDIASCAN_SLC_TEST_START:
		UartPrintf("\n\n\n\tSLC/MLC default EC:\t%l\t%l", (gMediaScanManager.EOT.uwCurrentErrBitNum << 3), gMediaScanManager.EOT.uwCurrentErrBitNum);
#else /* MEDIA_SCAN_DEBUG_EN */
	case MEDIASCAN_SLC_AUTO_READ_CALIBRATION: //ARC
#endif /* MEDIA_SCAN_DEBUG_EN */
		MediaScanStallOrUnstallChannel(pRetryJob, VALLEY_HEALTH_CHECK_STALL_CHANNEL);
#if MEDIA_SCAN_DEBUG_EN
#if MEDIA_SCAN_DEBUG_CHECK_READ_OFFSET_PREFIX_EN
		gMediaScanManager.ReadSamplingOffset.ubFeatureAdd = 0xA0;
		for (U8 ubTrimTestCnt = 0; ubTrimTestCnt < 0x3; ubTrimTestCnt++) {
			memset(ubFeatureData, 0, sizeof(ubFeatureData));
			FIPGetFeatureForMediaScan(pRetryJob->ubChannel, pRetryJob->ubBank, gMediaScanManager.ReadSamplingOffset.ubFeatureAddr, ubFeatureData);
			UartPrintf("\n\tMLC Read level:\t%d\t%d\t%b\t%b\t%b\t%b", gMediaScanManager.ubSLCVHCState, ubTrimTestCnt, ubFeatureData[0], ubFeatureData[1], ubFeatureData[2], ubFeatureData[3]);
			gMediaScanManager.ReadSamplingOffset.ubFeatureAddr += 1;
		}
#endif /* MEDIA_SCAN_DEBUG_CHECK_READ_OFFSET_PREFIX_EN */
		U16 uwSLCTrimAddr[2];
		gMediaScanManager.ReadSamplingOffset.uwTest = 0;
		UartPrintf("\n\n\n\tTest Start:");
		for (gMediaScanManager.ReadSamplingOffset.uwTrimCnt = 0; gMediaScanManager.ReadSamplingOffset.uwTrimCnt < 2; gMediaScanManager.ReadSamplingOffset.uwTrimCnt++) { //get high bit/low bit of read level
			if (MEDIA_SCAN_EVENT_NORMAL_QLC_DATA_UNIT == gMediaScanManager.ubEventIdx) {	//MLC UP
				uwSLCTrimAddr[gMediaScanManager.ReadSamplingOffset.uwTrimCnt] = (0 == gMediaScanManager.ReadSamplingOffset.uwTrimCnt) ? 0x1C0 : 0x1C1;
			}
			else {	//SLC
				if (0 == gMediaScanManager.ReadSamplingOffset.uwTrimCnt) {	//1st time
					uwSLCTrimAddr[gMediaScanManager.ReadSamplingOffset.uwTrimCnt] = FTLPhysicalWordLineGetCoordForSLC(0, gMediaScanManager.ubVictimWordline, IM_PHYSICAL_WORDLINE_GETCOORD_IS_EDGE_WORDLINE) ? 0x1D4 : 0x1DE;
				}
				else {	//2nd time
					uwSLCTrimAddr[gMediaScanManager.ReadSamplingOffset.uwTrimCnt] = FTLPhysicalWordLineGetCoordForSLC(0, gMediaScanManager.ubVictimWordline, IM_PHYSICAL_WORDLINE_GETCOORD_IS_EDGE_WORDLINE) ? 0x1D5 : 0x1DF;
				}
			}
			memset(ubFeatureData, 0, sizeof(ubFeatureData));
			FIPGetMLBiForMediaScan(pRetryJob->ubChannel, pRetryJob->ubBank, uwSLCTrimAddr[gMediaScanManager.ReadSamplingOffset.uwTrimCnt], ubFeatureData);
			UartPrintf("\n\tSLC/MLC Read level:\tTrimCnt\tTrimAddr\tReadLevel");
			UartPrintf("\n\tGet PIO:\t%d\t%l\t%b", gMediaScanManager.ReadSamplingOffset.uwTrimCnt, uwSLCTrimAddr[gMediaScanManager.ReadSamplingOffset.uwTrimCnt], ubFeatureData[0]);
#if MEDIA_SCAN_DEBUG_CHECK_READ_LEVEL_BY_MT_EN
			if (ubFeatureData[0] != 0xFF) {
				MediaScanReadDataPushCmdMT(FALSE, MEDIASCAN_GET_TRIM_CMD, uwSLCTrimAddr[gMediaScanManager.ReadSamplingOffset.uwTrimCnt]); //get trim CMD
				MediaScanReadDataPushDMAMT(FALSE, MEDIASCAN_GET_READ_LEVEL, uwSLCTrimAddr[gMediaScanManager.ReadSamplingOffset.uwTrimCnt]); //need buf
			}
#endif /* MEDIA_SCAN_DEBUG_CHECK_READ_LEVEL_BY_MT_EN */
		}
		gMediaScanManager.ubSLCVHCState = MEDIASCAN_SLC_AUTO_READ_CALIBRATION;

	case MEDIASCAN_SLC_AUTO_READ_CALIBRATION: //ARC
#endif /* MEDIA_SCAN_DEBUG_EN */
		FIPGetFeatureForMediaScan(pRetryJob->ubChannel, pRetryJob->ubBank, VALLEY_TRACK_FEATURE_ADDRESS, ubFeatureData, FIP_NO_USE_PARAMETER, FIP_NO_USE_PARAMETER, FALSE, FIP_NO_USE_PARAMETER, FIP_NO_USE_PARAMETER);
		ubFeatureData[0] = 0x05;
		FIPSetFeatureForMediaScan(pRetryJob->ubChannel, pRetryJob->ubBank, VALLEY_TRACK_FEATURE_ADDRESS, ubFeatureData); //set feature FA 96h = 05h
		MediaScanReadDataPushCmdMT(FALSE, MEDIASCAN_READ_CMD, 0); //00h-30h
		gMediaScanManager.ubSLCVHCState = MEDIASCAN_SLC_GET_READ_OFFSET;

	case MEDIASCAN_SLC_GET_READ_OFFSET:
		memset(ubFeatureData, 0, sizeof(ubFeatureData));
		if (MEDIA_SCAN_EVENT_NORMAL_QLC_DATA_UNIT == gMediaScanManager.ubEventIdx) { //MLC
			FIPGetFeatureForMediaScan(pRetryJob->ubChannel, pRetryJob->ubBank, MLC_READ_OFFSET_LEVEL_1_FEATURE_ADDRESS, ubFeatureData, FIP_NO_USE_PARAMETER, FIP_NO_USE_PARAMETER, FALSE, FIP_NO_USE_PARAMETER, FIP_NO_USE_PARAMETER); //MLC get feature FA A0h(Lv1)
			gMediaScanManager.ulFirstCalibrationOffset = (U32) (ubFeatureData[0] + ubFeatureData[2]);
			memset(ubFeatureData, 0, sizeof(ubFeatureData));
			FIPGetFeatureForMediaScan(pRetryJob->ubChannel, pRetryJob->ubBank, MLC_READ_OFFSET_LEVEL_3_FEATURE_ADDRESS, ubFeatureData, FIP_NO_USE_PARAMETER, FIP_NO_USE_PARAMETER, FALSE, FIP_NO_USE_PARAMETER, FIP_NO_USE_PARAMETER); //MLC get feature FA A2h(Lv3)
		}
		else {	//SLC
			FIPGetFeatureForMediaScan(pRetryJob->ubChannel, pRetryJob->ubBank, SLC_READ_OFFSET_FEATURE_ADDRESS, ubFeatureData, FIP_NO_USE_PARAMETER, FIP_NO_USE_PARAMETER, FALSE, FIP_NO_USE_PARAMETER, FIP_NO_USE_PARAMETER); //SLC get feature FA A4h
		}
		gMediaScanManager.ubCombineCalibrationOffset = ubFeatureData[0] + ubFeatureData[2];
		gMediaScanManager.ubSLCVHCState = MEDIASCAN_SLC_DISABLE_AUTO_READ_CALIBRATION;

	case MEDIASCAN_SLC_DISABLE_AUTO_READ_CALIBRATION:
		memset(ubFeatureData, 0, sizeof(ubFeatureData));
		FIPGetFeatureForMediaScan(pRetryJob->ubChannel, pRetryJob->ubBank, VALLEY_TRACK_FEATURE_ADDRESS, ubFeatureData, FIP_NO_USE_PARAMETER, FIP_NO_USE_PARAMETER, FALSE, FIP_NO_USE_PARAMETER, FIP_NO_USE_PARAMETER);
		ubFeatureData[0] = 0xC0;
		FIPSetFeatureForMediaScan(pRetryJob->ubChannel, pRetryJob->ubBank, VALLEY_TRACK_FEATURE_ADDRESS, ubFeatureData); //set feature FA 96h = C0h

		MediaScanReadDataPushCmdMT(FALSE, MEDIASCAN_READ_PREFIX_CMD, 0);	//2Eh + 00h-30h
#if MEDIA_SCAN_DEBUG_CHECK_READ_OFFSET_PREFIX_EN	//check read level of 2Eh in feature addr.
		gMediaScanManager.ReadSamplingOffset.ubFeatureAddr = 0xA0;
		for (U8 ubTrimTestCnt = 0; ubTrimTestCnt < 0x3; ubTrimTestCnt++) {
			memset(ubFeatureData, 0, sizeof(ubFeatureData));
			FIPGetFeatureForMediaScan(pRetryJob->ubChannel, pRetryJob->ubBank, gMediaScanManager.ReadSamplingOffset.ubFeatureAddr, ubFeatureData);
			UartPrintf("\n\tMLC Read level:\t%d\t%d\t%b\t%b\t%b\t%b", gMediaScanManager.ubSLCVHCState, ubTrimTestCnt, ubFeatureData[0], ubFeatureData[1], ubFeatureData[2], ubFeatureData[3]);
			gMediaScanManager.ReadSamplingOffset.ubFeatureAddr += 1;
		}
#endif /* MEDIA_SCAN_DEBUG_CHECK_READ_OFFSET_PREFIX_EN */
		gMediaScanManager.ubSLCVHCState = MEDIASCAN_SLC_READ_SAMPLING_OFFSET;

	case MEDIASCAN_SLC_READ_SAMPLING_OFFSET:
		MediaScanReadSamplingOffset(FALSE, pRetryJob); //RSO Read (SLC: Lv1, MLC UP: Lv3)
		gMediaScanManager.ubSLCVHCState = MEDIASCAN_SLC_CLEAR_PREFIX_READ;

	case MEDIASCAN_SLC_CLEAR_PREFIX_READ:
		memset(ubFeatureData, 0, sizeof(ubFeatureData));
		FIPSetFeatureForMediaScan(pRetryJob->ubChannel, pRetryJob->ubBank, CLEAR_READ_PREFIX_ADDRESS, ubFeatureData); //set feature FA 96h = C0h
#if MEDIA_SCAN_DEBUG_EN
		gMediaScanManager.ubSLCVHCState = MEDIASCAN_SLC_TEST_END;

	case MEDIASCAN_SLC_TEST_END:
		gMediaScanManager.ReadSamplingOffset.uwTrimCnt = 0;
		gMediaScanManager.ReadSamplingOffset.uwTest++;
		for (gMediaScanManager.ReadSamplingOffset.uwTrimCnt = 0; gMediaScanManager.ReadSamplingOffset.uwTrimCnt < 2; gMediaScanManager.ReadSamplingOffset.uwTrimCnt++) { //get high bit/low bit of read level
			memset(ubFeatureData, 0, sizeof(ubFeatureData));
			FIPGetMLBiForMediaScan(pRetryJob->ubChannel, pRetryJob->ubBank, uwSLCTrimAddr[gMediaScanManager.ReadSamplingOffset.uwTrimCnt], ubFeatureData);
			UartPrintf("\n\tSLC/MLC Read level:\tTrimCnt\tTrimAddr\tReadLevel");
			UartPrintf("\n\tGet PIO:\t%d\t%l\t%b", gMediaScanManager.ReadSamplingOffset.uwTrimCnt, uwSLCTrimAddr[gMediaScanManager.ReadSamplingOffset.uwTrimCnt], ubFeatureData[0]);
#if MEDIA_SCAN_DEBUG_CHECK_READ_LEVEL_BY_MT_EN
			if (ubFeatureData[0] != 0xFF) {
				MediaScanReadDataPushCmdMT(FALSE, MEDIASCAN_GET_TRIM_CMD, uwSLCTrimAddr[gMediaScanManager.ReadSamplingOffset.uwTrimCnt]);  //get trim CMD
				MediaScanReadDataPushDMAMT(FALSE, MEDIASCAN_GET_READ_LEVEL, uwSLCTrimAddr[gMediaScanManager.ReadSamplingOffset.uwTrimCnt]);  //need buf
			}
#endif /* MEDIA_SCAN_DEBUG_CHECK_READ_LEVEL_BY_MT_EN */
		}
#if MEDIA_SCAN_DEBUG_CHECK_READ_OFFSET_PREFIX_EN
		gMediaScanManager.ReadSamplingOffset.ubFeatureAddr = 0xA0;
		for (U8 ubTrimTestCnt = 0; ubTrimTestCnt < 0x3; ubTrimTestCnt++) {
			memset(ubFeatureData, 0, sizeof(ubFeatureData));
			FIPGetFeatureForMediaScan(pRetryJob->ubChannel, pRetryJob->ubBank, gMediaScanManager.ReadSamplingOffset.ubFeatureAddr, ubFeatureData);
			UartPrintf("\n\tMLC Read level:\t%d\t%d\t%b\t%b\t%b\t%b", gMediaScanManager.ubSLCVHCState, ubTrimTestCnt, ubFeatureData[0], ubFeatureData[1], ubFeatureData[2], ubFeatureData[3]);
			gMediaScanManager.ReadSamplingOffset.ubFeatureAddr += 1;
		}
#endif /* MEDIA_SCAN_DEBUG_CHECK_READ_OFFSET_PREFIX_EN */
#endif /* MEDIA_SCAN_DEBUG_EN */
		MediaScanStallOrUnstallChannel(pRetryJob, VALLEY_HEALTH_CHECK_UNSTALL_CHANNEL);
		//Reset state for each page
		gMediaScanManager.ubSLCVHCState = 0;	//MEDIASCAN_SLC_TEST_START or MEDIASCAN_SLC_AUTO_READ_CALIBRATION//MEDIASCAN_SLC_PAGE_SCAN_FLOW_FINISH;
		break;
	}
}

U8 MediaScanValleyHealthCheckMain(RetryJob_t *pRetryJob)
{
	U8 ubCreateErrLog = FALSE;
	U8 ubNeedUNEL = TRUE;
	U16 uwValleyDiffECThershold, uwValleyOffsetThershold, uwValleyCenterECThershold;
	U8 ubOffsel, ubTemperatureThershold, ubWordLineGroup;

	gMediaScanManager.btVHCFlag = TRUE;
	gpVTDBUF->MediaScan.uwValleyHealthCheckCnt[gMediaScanManager.ubVictimScanGroup]++;
	if ((MEDIA_SCAN_EVENT_NORMAL_QLC_DATA_UNIT == gMediaScanManager.ubEventIdx) && (QLC_WL == FTLPhysicalWordLineGetCoordForNonSLC(0, gMediaScanManager.ubVictimWordline, IM_PHYSICAL_WORDLINE_GETCOORD_WORDLINE_TYPE))) {
		MediaScanValleyHealthCheckForQLCWordLine(pRetryJob);

		//setting VHC threshold and offsel for UNEL
		ubWordLineGroup = FTLPhysicalWordLineGetCoordForNonSLC(0, gMediaScanManager.ubVictimWordline, IM_PHYSICAL_WORDLINE_GETCOORD_WORDLINE_GROUP);
		if (LOWER == (gMediaScanManager.uwScanIdx[gMediaScanManager.ubEventIdx] % PAGE_NUM))  { //Lower Page
			uwValleyCenterECThershold = guwCenterErrorCountThresholdPerCodeWord[ubWordLineGroup][0];
			uwValleyDiffECThershold = guwDiffErrorCountThresholdPerCodeWord[ubWordLineGroup][0];
			uwValleyOffsetThershold = MEDIA_SCAN_VALLEY_OFFSET_THRESHOLD_FOR_SLC_AND_MLC_AND_QLC_LP;
			ubOffsel = gubMediaScanAGCOffsetArray[1];	//for UNEL
		}
		else {	//Top Page
			uwValleyCenterECThershold = guwCenterErrorCountThresholdPerCodeWord[ubWordLineGroup][1];
			uwValleyDiffECThershold = guwDiffErrorCountThresholdPerCodeWord[ubWordLineGroup][1];
			uwValleyOffsetThershold = MEDIA_SCAN_VALLEY_OFFSET_THRESHOLD_FOR_QLC_TOP_PAGE;
			ubOffsel = gubMediaScanAGCOffsetArray[2];	//for UNEL
		}
		ubTemperatureThershold = MEDIA_SCAN_TEMPERATURE_DELTA_THRESHOLD_FOR_MLC_AND_QLC;
	}
	else { //SLC, MLC WL
		MediaScanValleyHealthCheckForSLCAndMLCWordLine(pRetryJob);
		//setting VHC threshold
		if (MEDIA_SCAN_EVENT_NORMAL_QLC_DATA_UNIT == gMediaScanManager.ubEventIdx) {	//MLC
			ubWordLineGroup = FTLPhysicalWordLineGetCoordForNonSLC(0, gMediaScanManager.ubVictimWordline, IM_PHYSICAL_WORDLINE_GETCOORD_WORDLINE_GROUP);
			uwValleyCenterECThershold = MEDIA_SCAN_CENTER_ERROR_COUNT_THRESHOLD_FOR_MLC_PER_CODEWORD;
			uwValleyDiffECThershold = MEDIA_SCAN_DIFF_ERROR_COUNT_THRESHOLD_FOR_MLC_PER_CODEWORD;
			ubTemperatureThershold = ((0 == FTLPhysicalWordLineGetCoordForNonSLC(0, gMediaScanManager.ubVictimWordline, IM_PHYSICAL_WORDLINE_GETCOORD_WORDLINE_GROUP)) || (7 == FTLPhysicalWordLineGetCoordForNonSLC(0, gMediaScanManager.ubVictimWordline, IM_PHYSICAL_WORDLINE_GETCOORD_WORDLINE_GROUP))) ? MEDIA_SCAN_TEMPERATURE_DELTA_THRESHOLD_FOR_SLC_AND_MLC : MEDIA_SCAN_TEMPERATURE_DELTA_THRESHOLD_FOR_MLC_AND_QLC;
			uwValleyOffsetThershold = MEDIA_SCAN_VALLEY_OFFSET_THRESHOLD_FOR_SLC_AND_MLC_AND_QLC_LP;
		}
		else {	//SLC
			ubWordLineGroup = FTLPhysicalWordLineGetCoordForSLC(0, gMediaScanManager.ubVictimWordline, IM_PHYSICAL_WORDLINE_GETCOORD_WORDLINE_GROUP);
			uwValleyCenterECThershold = MEDIA_SCAN_CENTER_ERROR_COUNT_THRESHOLD_FOR_SLC_PER_CODEWORD;
			uwValleyDiffECThershold = MEDIA_SCAN_DIFF_ERROR_COUNT_THRESHOLD_FOR_SLC_PER_CODEWORD;
			ubTemperatureThershold = MEDIA_SCAN_TEMPERATURE_DELTA_THRESHOLD_FOR_SLC_AND_MLC;
			uwValleyOffsetThershold = MEDIA_SCAN_VALLEY_OFFSET_THRESHOLD_FOR_SLC_AND_MLC_AND_QLC_LP;
		}
		ubOffsel = gubMediaScanAGCOffsetArray[0];	//for UNEL
	}


	//Media scan DiffEC is different from VRLC DiffEC.
	U8 ubIdealOffset, ubMode, ubReadTemperature, ubTemperatureDelta;
	U8 ubProgramTemperature = 0;

	ubReadTemperature = (gTT.ubCurrentTemperatureExternal.B.btSign) ? 0 : gTT.ubCurrentTemperatureExternal.B.Degree; //Get temp from FLH. Only record temp higher than 0(K) //TTGetThermalSensorTemperature(): Get temp from sensor.
	ubMode = NON_PHYSICAL_BLK;
	if (MEDIA_SCAN_EVENT_PARTIAL_VT_MOTHER == gMediaScanManager.ubEventIdx) {
		ubMode = PHYSICAL_BLK_VTMOTHER;
	}
	else if (MEDIA_SCAN_EVENT_NORMAL_SLC_FW == gMediaScanManager.ubEventIdx) {
		if ((MEDIA_SCAN_SLC_EVENT_SYSTEM_BLK_0 == gMediaScanManager.ubCurrentSLCNonDataEventIdx) || (MEDIA_SCAN_SLC_EVENT_SYSTEM_BLK_1 == gMediaScanManager.ubCurrentSLCNonDataEventIdx)) {
			ubMode = PHYSICAL_BLK_SYSTEM;
		}
		else if ((MEDIA_SCAN_SLC_EVENT_DBT_BLK_0 == gMediaScanManager.ubCurrentSLCNonDataEventIdx) || (MEDIA_SCAN_SLC_EVENT_DBT_BLK_1 == gMediaScanManager.ubCurrentSLCNonDataEventIdx)) {
			ubMode = PHYSICAL_BLK_DBT;
		}
#if (PS5017_EN)
		else if ((MEDIA_SCAN_SLC_EVENT_CODE_BLK_CH0_0 >= gMediaScanManager.ubCurrentSLCNonDataEventIdx) || (MEDIA_SCAN_SLC_EVENT_CODE_BLK_CH1_1 <= gMediaScanManager.ubCurrentSLCNonDataEventIdx)) {
			ubMode = PHYSICAL_BLK_CODE;
		}
		else if ((MEDIA_SCAN_SLC_EVENT_BANKING_BLK_CH0_0 >= gMediaScanManager.ubCurrentSLCNonDataEventIdx) || (MEDIA_SCAN_SLC_EVENT_BANKING_BLK_CH1_1 <= gMediaScanManager.ubCurrentSLCNonDataEventIdx)) {
			ubMode = PHYSICAL_BLK_BANKING;
		}

#else /*(PS5017_EN)*/
		else if ((MEDIA_SCAN_SLC_EVENT_CODE_BLK_CH0_0 >= gMediaScanManager.ubCurrentSLCNonDataEventIdx) || (MEDIA_SCAN_SLC_EVENT_CODE_BLK_CH3_1 <= gMediaScanManager.ubCurrentSLCNonDataEventIdx)) {
			ubMode = PHYSICAL_BLK_CODE;
		}
		else if ((MEDIA_SCAN_SLC_EVENT_BANKING_BLK_CH0_0 >= gMediaScanManager.ubCurrentSLCNonDataEventIdx) || (MEDIA_SCAN_SLC_EVENT_BANKING_BLK_CH3_1 <= gMediaScanManager.ubCurrentSLCNonDataEventIdx)) {
			ubMode = PHYSICAL_BLK_BANKING;
		}
#endif /*(PS5017_EN)*/
	}
#if RECORD_FLASH_TEMPERATURE_EN
	ubProgramTemperature = (NON_PHYSICAL_BLK == ubMode) ? MediaScanGetUnitTemperature(gMediaScanManager.uwVictimUnit) : MediaScanGetPhysicalBlkTemperature(ubMode, gMediaScanManager.uwCurrentSLCNonDataBlk[gMediaScanManager.ubCurrentSLCNonDataEventIdx]);
#endif	/* RECORD_FLASH_TEMPERATURE_EN */

	ubTemperatureDelta = (ubReadTemperature > ubProgramTemperature) ? (ubReadTemperature - ubProgramTemperature) : (ubProgramTemperature - ubReadTemperature);
	if (gMediaScanManager.btIsCBC7) {
		ubCreateErrLog = TRUE;
	}
	else {
		if (((gMediaScanManager.ReadSamplingOffset.uwErrorCountPerCodeWord[0] + gMediaScanManager.ReadSamplingOffset.uwErrorCountPerCodeWord[1]) >> 1) > uwValleyDiffECThershold) {
			ubCreateErrLog = TRUE;
		}
		else {
			ubIdealOffset = gMediaScanManager.ubCombineCalibrationOffset;	//without DAC center offset
			ubIdealOffset = (ubIdealOffset > 0x7F) ? (0xFF - ubIdealOffset) : ubIdealOffset;	//7Fh: 1270mv, 80h: -1280mv
			if ((ubIdealOffset > uwValleyOffsetThershold) || (gMediaScanManager.ReadSamplingOffset.uwErrorCountPerCodeWord[2] > uwValleyCenterECThershold)) {
				if (ubTemperatureDelta < ubTemperatureThershold) {
					ubCreateErrLog = TRUE;
				}
			}
			else {
				ubNeedUNEL = FALSE;
			}
		}
	}

#if ((UNIFIED_LOG_EN || MEDIA_SCAN_DEBUG_UNIFIED_LOG_UART_EN) && IM_N48R)
	U8 ubFlashPlane, ubRuleIndex;
	UNIFIED_LOG_INFO_T NewUnifiedLogInfo = {0};
	U32 ulFSA = pRetryJob->pErrorMT->dma.uliFSA0_1;
	VECTRO_TABLE_STRUCT_PTR pVectorTable = (VECTRO_TABLE_STRUCT_PTR)M_COP0_GET_VECTOR_TABLE_ADDR(pRetryJob->ubPresentMTIdx);
	U8 ubCodeword = 0;
	U8 ubFailBitCnt = 0;
	U8 ubCurrentTempC = 0, ubUNELTemperatureDelta = 0;
	U16 uwUnit, uwUnitEC, uwMSErrCode;

	M_PCA_RULE_IDX(ubRuleIndex, pVectorTable->uldat.btSLC, pVectorTable->uldat.btD1);
	M_PCA_GET_INFO(NewUnifiedLogInfo.ubCH, ulFSA, gPCARule_Channel, ubRuleIndex);
	M_PCA_GET_INFO(NewUnifiedLogInfo.ubCE, ulFSA, gPCARule_Bank, ubRuleIndex);
	M_PCA_GET_INFO(NewUnifiedLogInfo.ubLUN, ulFSA, gPCARule_LUN, ubRuleIndex);
	M_PCA_GET_INFO(uwUnit, ulFSA, gPCARule_Block, ubRuleIndex);
	M_PCA_GET_INFO(ubFlashPlane, ulFSA, gPCARule_Plane, ubRuleIndex);
	NewUnifiedLogInfo.uwBlock = (uwUnit << gubBurstsPerBankLog) + ubFlashPlane;
	M_PCA_GET_INFO(NewUnifiedLogInfo.uwPage, ulFSA, gPCARule_Page, ubRuleIndex);
	M_PCA_GET_INFO(NewUnifiedLogInfo.uwUnit, pRetryJob->ulErrorPCA, gPCARule_Unit, ubRuleIndex);
	NewUnifiedLogInfo.ubPageType = FTLGetCoord(NewUnifiedLogInfo.uwPage, IM_GETCOORD_X_VAL) % gubLMUNumber + 1;
	NewUnifiedLogInfo.ubBlkType = (gpuwVBRMP[NewUnifiedLogInfo.uwUnit].B.btIsSLCMode ? 1 : 4) + (gpulVC[NewUnifiedLogInfo.uwUnit].B.btStatic ? 0 : 4);

	for (U8 ubi = 0; ubi < 4; ++ubi) {
		if (((pRetryJob->ubUnifiedLogErrorBMP) & BIT(2 * ubi)) || ((pRetryJob->ubUnifiedLogErrorBMP) & BIT(2 * ubi + 1))) {
			ubCodeword++;
		}
	}

	ubUNELTemperatureDelta = ubReadTemperature - ubProgramTemperature;
	ubCurrentTempC = (gTT.ubCurrentTemperatureExternal.B.btSign) ? ((~gTT.ubCurrentTemperatureExternal.B.Degree) + 1) : gTT.ubCurrentTemperatureExternal.B.Degree;
	uwUnitEC = ((MEDIA_SCAN_EVENT_NORMAL_SLC_FW == gMediaScanManager.ubEventIdx) && (gMediaScanManager.ubCurrentSLCNonDataEventIdx != MEDIA_SCAN_SLC_EVENT_TABLE)) ? 0 : (gpulEC[gMediaScanManager.uwVictimUnit].B.uwEraseCnt);
#if MEDIA_SCAN_DEBUG_DATA_FOR_MICRON_UART_EN
	UartPrintf("\n\tRtemp:\t%l\tPtemp:\t%l\tDtemp:\t%l\tUDtemp:\t%l", ubReadTemperature, ubProgramTemperature, ubTemperatureDelta, ubUNELTemperatureDelta);
#endif /* MEDIA_SCAN_DEBUG_DATA_FOR_MICRON_UART_EN */
#if(UNIFIED_LOG_EN && IM_N48R)
	uwMSErrCode = (ubCreateErrLog) ? UNIFIED_LOG_FOLD_VHC : UNIFIED_LOG_FOLD_FORGIVEN;
	if (TRUE == ubNeedUNEL) {
		UnifiedLogMSFoldEventAdd(NewUnifiedLogInfo, uwMSErrCode, gMediaScanManager.ubCombineCalibrationOffset, ubUNELTemperatureDelta, uwUnitEC, ubCodeword);
		UnifiedLogMSFoldStatsEventAdd(NewUnifiedLogInfo, (gMediaScanManager.EOT.uwCurrentErrBitNum << 3), (gMediaScanManager.ReadSamplingOffset.uwErrorCountPerCodeWord[2] << 3), (gMediaScanManager.ReadSamplingOffset.uwErrorCountPerCodeWord[0] << 3), (gMediaScanManager.ReadSamplingOffset.uwErrorCountPerCodeWord[1] << 3), gMediaScanManager.ReadSamplingOffset.uwDACCenterOffset, ubOffsel, ubFailBitCnt);
	}

#endif /* UNIFIED_LOG_EN && IM_N48R */
	if ((MEDIA_SCAN_DEBUG_UNIFIED_LOG_UART_EN) && (TRUE == ubNeedUNEL)) {
		uwMSErrCode = (ubCreateErrLog) ? UNIFIED_LOG_FOLD_VHC : UNIFIED_LOG_FOLD_FORGIVEN;
		UartPrintf("\n\n\t[VHC]\tEvent\tErrC\tCH\tCE\tLUN\tCW\tBlk\tPg\tSB\tROOffset\tPgTp\tBlkTp\tPEC\tBV\tRWTmpD\ttempC");
		UartPrintf("\tEvent\tFEC\tFCEC\tFLEC\tFREC\tDACC\tSB\tOffSel\tFBCnt\ttempC");
		UartPrintf("\tWL\tWGR\tPg\tBlk\tDiffEC");

		UartPrintf("\n\t[VHC]\t%l\t%l\t%b\t%b\t%b\t%b\t%l", UNIFIED_LOG_ID_MS_FOLD, uwMSErrCode, NewUnifiedLogInfo.ubCH, NewUnifiedLogInfo.ubCE, NewUnifiedLogInfo.ubLUN, ubCodeword, NewUnifiedLogInfo.uwBlock);
		UartPrintf("\t%l\t%l\t%b\t%b\t%b\t%l", NewUnifiedLogInfo.uwPage, NewUnifiedLogInfo.uwUnit, gMediaScanManager.ubCombineCalibrationOffset, NewUnifiedLogInfo.ubPageType, NewUnifiedLogInfo.ubBlkType, uwUnitEC);
		UartPrintf("\t%L\t%b\t%b", (gpComm2Andes_Info->uoTotalNandWrite / gubPlanesPerSuperPage), ubUNELTemperatureDelta, ubCurrentTempC);

		UartPrintf("\t%l\t%l\t%l\t%l\t%l\t%l\t%l\t%b\t%b\t%b", UNIFIED_LOG_ID_MS_FOLD_STATS, (gMediaScanManager.EOT.uwCurrentErrBitNum << 3), (gMediaScanManager.ReadSamplingOffset.uwErrorCountPerCodeWord[2] << 3),  (gMediaScanManager.ReadSamplingOffset.uwErrorCountPerCodeWord[0] << 3), (gMediaScanManager.ReadSamplingOffset.uwErrorCountPerCodeWord[1] << 3), gMediaScanManager.ReadSamplingOffset.uwDACCenterOffset, NewUnifiedLogInfo.uwUnit, ubOffsel, ubFailBitCnt, ubCurrentTempC);
		UartPrintf("\t%d\t%d\t%d\t%d\t%d\n", gMediaScanManager.ubVictimWordline, ubWordLineGroup, NewUnifiedLogInfo.uwPage, NewUnifiedLogInfo.uwBlock, (((gMediaScanManager.ReadSamplingOffset.uwErrorCountPerCodeWord[0] << 3) + (gMediaScanManager.ReadSamplingOffset.uwErrorCountPerCodeWord[1] << 3)) >> 1));
		//UartPrintf("\n\n\tFoldEvent\n\tEvent:\t%l\n\tErrC:\t%l\n\tCH:\t%b\n\tCE:\t%b\n\tLUN:\t%b\n\tCW:\t%b\n\tBlk:\t%l", UNIFIED_LOG_ID_MS_FOLD, uwMSErrCode, NewUnifiedLogInfo.ubCH, NewUnifiedLogInfo.ubCE, NewUnifiedLogInfo.ubLUN, ubCodeword, NewUnifiedLogInfo.uwBlock);
		//UartPrintf("\n\tPgNum:\t%l\n\tSB:\t%l\n\tROOffset:\t%b\n\tPgTp:\t%b\n\tBlkTp:\t%b\n\tPEC:\t%l", NewUnifiedLogInfo.uwPage, NewUnifiedLogInfo.uwUnit, gMediaScanManager.ubCombineCalibrationOffset, NewUnifiedLogInfo.ubPageType, NewUnifiedLogInfo.ubBlkType, uwUnitEC);
		//UartPrintf("\n\tBV:\t%L\n\tRWTmpD:\t%b\n\ttempC:\t%b", (gpComm2Andes_Info->uoTotalNandWrite / gubPlanesPerSuperPage), ubUNELTemperatureDelta, ubCurrentTempC);

		//UartPrintf("\n\n\tFoldStatsEvent\n\tEvent:\t%l\n\tFEC:\t%l\n\tFCEC:\t%l\n\tFLEC:\t%l\n\tFREC:\t%l\n\tDACC:\t%l\n\tSB:\t%l\n\tOffSel:\t%b\n\tFBCnt:\t%b\n\ttempC:\t%b\n", UNIFIED_LOG_ID_MS_FOLD_STATS, (gMediaScanManager.EOT.uwCurrentErrBitNum << 3), gMediaScanManager.ReadSamplingOffset.uwErrorCount[2],  gMediaScanManager.ReadSamplingOffset.uwErrorCount[0], gMediaScanManager.ReadSamplingOffset.uwErrorCount[1], gMediaScanManager.ReadSamplingOffset.uwDACCenterOffset, NewUnifiedLogInfo.uwUnit, ubOffsel, ubFailBitCnt, ubCurrentTempC);
	}
#endif /* ((UNIFIED_LOG_EN || MEDIA_SCAN_DEBUG_UNIFIED_LOG_UART_EN) && IM_N48R) */
	gpVTDBUF->MediaScan.uwNeedFoldingCnt[gMediaScanManager.ubVictimScanGroup] = (ubCreateErrLog) ? (gpVTDBUF->MediaScan.uwNeedFoldingCnt[gMediaScanManager.ubVictimScanGroup] + 1) : gpVTDBUF->MediaScan.uwNeedFoldingCnt[gMediaScanManager.ubVictimScanGroup];
	return ubCreateErrLog;
}
#endif /* ((MEDIA_SCAN_EN) && (!BURNER_MODE_EN) && (!RDT_MODE_EN) && (IM_N48R)) */
