#ifndef _ERROR_HANDLE_CODE_API_H_
#define _ERROR_HANDLE_CODE_API_H_

#include "typedef.h"
#include "symbol.h"

//==============================================================================
// Definition
//==============================================================================
#define ERROR_HANDLE_CODE_BLOCK_CODESIGN_HEADER_PAGE_CNT	(1)
#define ERROR_HANDLE_CODE_BLOCK_SIGNATURE_PAGE_CNT	(1)
#define ERROR_HANDLE_CODE_BLOCK_CODEPOINT_PAGE_CNT	(1)
#define ERROR_HANDLE_CODE_BLOCK_PAD_PAGE_CNT	(1)

#define ERROR_HANDLE_CODE_BLOCK_ID_PAGE_PAGEIDX (0)

#define ERROR_HANDLE_CODE_BLOCK_MAKE_ERROR_HAPPENED (BIT0)
#define ERROR_HANDLE_CODE_BLOCK_MAKE_ERROR_NOW	    (BIT1)

#define ERROR_HANDLE_CODE_BLOCK_DEFAULT_CE			    (0x00)

#define ERROR_HANDLE_CODE_BANK_BLK_CODE_START_PAGE    (1)

#define ERROR_HANDLE_CODE_PROGRAM_BANKING_BLK_START_PAGE_2_MODE	(0)
#define ERROR_HANDLE_CODE_PROGRAM_BANKING_BLK_START_PAGE_1_MODE	(1)
#define ERROR_HANDLE_CODE_PROGRAM_CODE_BLK_MODE	(2)
#define ERROR_HANDLE_CODE_PROGRAM_TEMP_BANK_BLK_ALL_CHANNEL_MODE	(3)
#define ERROR_HANDLE_CODE_PROGRAM_PAD_PAGE_MODE	(4)

#define ERROR_HANDLE_CODE_BANKING_BLK_ERROR	(0)
#define ERROR_HANDLE_CODE_BLK_ERROR_HANDLE_SPOR (1)
#define ERROR_HANDLE_DLMC_SPOR (2)

#define ERROR_HANDLE_CODE_MAX_FW_SLOT	(0x03)

#if BOOTLOADER_EN
#define	ERROR_HANDLE_CODE_BOOTLOADER_ATCM_LOCATION	0
#define	ERROR_HANDLE_CODE_BOOTLOADER_BTCM_LOCATION	1
#define ERROR_HANDLE_CODE_BOOTLOADER_MAX_SECTION	2
#endif /* BOOTLOADER_EN */
//==============================================================================
// Structures
//==============================================================================
typedef enum ErrorHandleCodeBlockStateEnum {
	CODE_BLOCK_HANDLE_IDLE = 0,
	CODE_BLOCK_HANDLE_START,
	CODE_BLOCK_HANDLE_FREE_QUEUE,
	CODE_BLOCK_HANDLE_SET_VARIABLE,
	CODE_BLOCK_HANDLE_COPY_REVISION_0,
	CODE_BLOCK_HANDLE_UPDATE_SYSTEM_0,
	CODE_BLOCK_HANDLE_COPY_REVISION_1,
	CODE_BLOCK_HANDLE_UPDATE_SYSTEM_1,
	CODE_BLOCK_UPDATE_DONE,
	CODE_BLOCK_UPDATE_FAIL
} ErrorHandleCodeBlockStateEnum_t;

typedef struct {
	U16 uwStartPageIdx; // Record  start page index of current section
	U16 uwPageCnt;		  // Record  page count of current section
} ErrorHandleCodeSectionInfo_t; //16Byte

typedef struct {
	ErrorHandleCodeBlockStateEnum_t ubState;
	U8 ubHandleCodeReason;
	U8 ubActiveSlotIdx;
	U8 ubRevisionNeedProgramBMP;
	U8 ubProgramErrorBMP;
	U8 ubMakeProgramErrorBMP;
	U8 ubMode;
	U8 ubNeedGetSystemFreeQueueBMP[CODE_BLK_NUM_PER_CH];
	U8 ubSkipCodeBankingErrorHandle;
	U8 ubCodeBlkMarkBad;
	U8 ubBankingBlkMarkBadBMP;
	U8 ubValidSlotNum;
	U8 ubValidSlotID[ERROR_HANDLE_CODE_MAX_FW_SLOT];
	U16 uwCodePtrPageIdx;
	U16 uwPadPageIdx;
	ErrorHandleCodeSectionInfo_t ulFWSection[ERROR_HANDLE_CODE_MAX_FW_SLOT][CODE_MAX_SECTION];
#if BOOTLOADER_EN
	ErrorHandleCodeSectionInfo_t ulBootLoaderSection[ERROR_HANDLE_CODE_MAX_FW_SLOT][ERROR_HANDLE_CODE_BOOTLOADER_MAX_SECTION];
#endif /* BOOTLOADER_EN */
} ErrorHandleCodeBlock_t;

typedef union {
	U8 ubAll;

	struct {
		U8 btReadFail			: 1;
		U8 btProgramFail			: 1;
		U8 				: 6;
	} BMP;
} ErrorHandleCodeBlockError_t;

#if BOOTLOADER_EN
typedef enum {
	ERROR_HANDLE_CODE_L4K_DEFAULT_STATE = 0,
	ERROR_HANDLE_CODE_L4K_ID_PAGE,
	ERROR_HANDLE_CODE_L4K_BOOTLOADER_CODE_SIGN_HEADER,
	ERROR_HANDLE_CODE_L4K_BOOTLOADER_CODE_SECTION,
	ERROR_HANDLE_CODE_L4K_BOOTLOADER_SIGNATURE,
	ERROR_HANDLE_CODE_L4K_FW_CODE_SIGN_HEADER,
	ERROR_HANDLE_CODE_L4K_FW_CODE_SECTION,
	ERROR_HANDLE_CODE_L4K_FW_SIGNATURE,
	ERROR_HANDLE_CODE_L4K_CODE_POINTER,
	ERROR_HANDLE_CODE_L4K_DUMMY
} ErrorHandleCodeBlockFillL4kAndCRCInfoState_t;
#endif /* BOOTLOADER_EN */
//==============================================================================
// Variables
//==============================================================================
extern ErrorHandleCodeBlock_t gErrorHandleCodeBlk;
//==============================================================================
// Function API
//==============================================================================
AOM_ERROR_HANDLE_CODE void ErrorHandleCodeBlockHandle(U8 ubPBMode, U8 ubHandleLogType);
AOM_ERROR_HANDLE_CODE U16 ErrorHandleGetCodeBlockErrorLog(U8 ubHandleLogType);
AOM_ERROR_HANDLE_CODE U8 ErrorHandleGetCodeBlockErrorMode(U16 uwErrorLogOffset );
AOM_ERROR_HANDLE_CODE ErrorHandleCodeBlockError_t ErrorHandleCodeBlockCopy(U8 ubCodeIdxInChannel, U8 ubErrorChannel, U8 ubBlkProgramMode);
AOM_ERROR_HANDLE_CODE SystemAreaBlockStatus_t ErrorHandleCodeBlockReadPlane(U32 ulBufferAddr, U16 uwPlaneIdx, U8 ubDataMode, U8 ubCodeBlkProgramMode );
AOM_ERROR_HANDLE_CODE void ErrorHandleCodeSetRightCodeBlkToFWSlotInfo(U32 ulBufferAddr);

#endif /* _ERR_HANDLE_API_H_ */
