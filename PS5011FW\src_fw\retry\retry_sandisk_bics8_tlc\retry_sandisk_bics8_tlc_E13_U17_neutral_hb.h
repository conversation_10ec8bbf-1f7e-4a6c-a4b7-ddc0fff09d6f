#ifndef _RETRY_SANDISK_BISC8_TLC_E13_NEUTRAL_HB_H_
#define _RETRY_SANDISK_BISC8_TLC_E13_NEUTRAL_HB_H_
/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */

#include "env.h"

#if (((PS5013_EN) || (PS5017_EN)) && ((FW_CATEGORY_FLASH == FLASH_SANDISK_BICS8_TLC)||(FW_CATEGORY_FLASH == FLASH_SANDISK_BICS6_TLC))  && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER))//BICS8 Add
/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */

#define HBIT_RETRY_SANDISK_BICS8_TLC_512G_STEP_NUM			(63 + 1)
#define HBIT_RETRY_SANDISK_BICS8_SLC_512G_STEP_NUM			(26 + 1)
#define HBIT_RETRY_SANDISK_BICS8_TLC_1024G_STEP_NUM			(45 + 1)
#define HBIT_RETRY_SANDISK_BICS8_SLC_1024G_STEP_NUM			(26 + 1)

/*
 * ---------------------------------------------------------------------------------------------------
 *  enum
 * ---------------------------------------------------------------------------------------------------
 */

/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */

#endif /* ((PS5013_EN) && (FW_CATEGORY_FLASH == FLASH_SANDISK_BICS8_TLC) && (CUSTOMER_MAINSTREAM == FW_CATEGORY_CUSTOMER)) */
#endif /* _RETRY_SANDISK_BISC8_TLC_E13_NEUTRAL_HB_H_ */
