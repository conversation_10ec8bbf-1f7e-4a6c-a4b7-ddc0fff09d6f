#ifndef _MATH_OP_
#define _MATH_OP_
#include "typedef.h"
#include "setup.h"

#if VS_SIM_EN
#include <intrin.h>
#else /* VS_SIM_EN */
#include <arm_acle.h>
#endif /* VS_SIM_EN */

#define MIN(a, b) (((a) < (b)) ? (a) : (b))
#define MAX(a, b) (((a) > (b)) ? (a) : (b))

#define FLOOR(VALUE, BASE)		((VALUE) / (BASE))
#define FLOOR_POW2(VALUE, BASE)		((VALUE) & ~((BASE) - 1))

#define CEILING(VALUE, BASE)		((((VALUE) + ((BASE) - 1)) / (BASE)) * (BASE))
#define CEILING_DIV(VALUE, BASE)	((((VALUE) + ((BASE) - 1)) / (BASE)))
#define CEILING_POW2(VALUE, BASE)	(((VALUE) + ((BASE) - 1)) & ~((BASE) - 1))
#define SIZE_IN_32B(SIZE)			((SIZE) / SIZE_32B)
#define SIZE_IN_32B_CEILING(SIZE)	(((SIZE) + (SIZE_32B - 1)) / SIZE_32B)
#define SIZE_IN_32B_FLOORING(SIZE)	((SIZE) / SIZE_32B)
#define ADDR_ALIGN_32B(ADDR)		(ADDR / 32 * 32)
#define DELTA_TO_ALIGN_32B(SIZE)	(((SIZE) % SIZE_32B) ? ((SIZE_32B) - ((SIZE) % SIZE_32B)) : 0)

#define M_SMSK(X,Y,Z)				((U32)(((X) << (Y)) & Z))
#define BITMSK(LENGTH, OFFSET)		((~((~0ULL)<<(LENGTH)))<<(OFFSET))
#define WAIT_REG(REG, MASK, VAL) while (((REG) & (MASK)) != (VAL)){}

#define HIGH_32_BIT(VAL)					((VAL) >> 32)
#define HIGH_8_BIT(VAL)					((VAL) >> 8)

#define M_BYYE_SIGN_MAGNITUDE_TO_TWO_COMPLEMENT(x) (((U8)x & 0x80) ? ((~((U8)x & 0x7F)) + (U8)1) : (U8)x)
#define M_BYYE_TWO_COMPLEMENT_TO_SIGN_MAGNITUDE(x) (((U8)x & 0x80) ? (~((U8)x-1) | 0x80) : (U8)x)

#define BIT_TO_BYTE_SHIFT (3)
#define BIT_TO_BYTE_MASK ((BIT0 << BIT_TO_BYTE_SHIFT) - 1)

#if VS_SIM_EN
#define CoutingLeadingZeros(VAL)	__lzcnt(VAL)
#else /* VS_SIM_EN */
#define CoutingLeadingZeros(VAL)	__clz(VAL)
#endif /* VS_SIM_EN */

//*******************************************
//			ODD_CE
//*******************************************
#if (ODD_CE_EN)
#define M_MOD(A,B)    ((A) % (B))
#define M_MUL(A,B)    ((A) * (B))
#define M_DIV(A,B)    ((A) / (B))
#else /* ODD_CE_EN */
#define M_MOD(A,B)    ((A) & (B ## Mask))
#define M_MUL(A,B)    ((A) << (B ## Log))
#define M_DIV(A,B)    ((A) >> (B ## Log))
#endif /* ODD_CE_EN */

#define M_DIV_CEILING(A, B)    (((A)+(B)-1)/(B))
INLINE U32 CoutingTrailingZeros(U32 ulValue)
{
	U32 ulCount = CoutingLeadingZeros(ulValue & ((~ulValue) + 1));
	return ulValue ? 31 - ulCount : ulCount;
}

INLINE U32 CalculateCRC24(U32 ulSrcAdr, U64 uoLBA)
{
	U32 uli, ulj;
	U32 ulDin;
	U32 ulCRCOut = 0x123456;
	for (uli = 0; uli < 6; uli++) {
		//cppcheck-suppress shiftTooManyBits; "calculate CRC, this is what we want"
		ulDin = (U32) ((uoLBA >> (uli * 24)) & 0xFFFFFF);
		for (ulj = 0; ulj < 24; ulj++) {
			if (((ulCRCOut & 0x800000) >> 23) ^ (ulDin & 0x000001)) {
				ulCRCOut <<= 1;
				ulDin >>= 1;
				ulCRCOut ^= 0x864CFB;
			}
			else {
				ulCRCOut <<= 1;
				ulDin >>= 1;
			}
			if (uli == 5 && ulj == 7) {
				break;
			}
		}
	}
	//ulCRCOut&=0xFFFFFF;
	for (uli = 0; uli < (4096 / 3) + 1; uli++) {
		ulDin = ((U32 *) (ulSrcAdr + (uli * 3)))[0];
		for (ulj = 0; ulj < 24; ulj++) {
			if (((ulCRCOut & 0x800000) >> 23) ^ (ulDin & 0x000001)) {
				ulCRCOut <<= 1;
				ulDin >>= 1;
				ulCRCOut ^= 0x864CFB;
			}
			else {
				ulCRCOut <<= 1;
				ulDin >>= 1;
			}
			if (uli * 24 + ulj == 32767) {
				break;
			}
		}
	}
	return (ulCRCOut & 0xFFFFFF);
}

INLINE U8 CalulateFloorLog2(U32 ulValue)
{
	return (ulValue <= 1) ? 0 : (U8)(31 - CoutingLeadingZeros(ulValue));
}

INLINE U8 CalulateCeilingLog2(U32 ulValue)
{
	return (ulValue <= 1) ? 0 : (U8)(32 - CoutingLeadingZeros(ulValue - 1));
}

INLINE U8 Log2Cal(U32 ulData)
{
#if VS_SIM_EN
	return (U8)((ulData > 1) ? (32 - __lzcnt(ulData - 1)) : 0);
#else /* VS_SIM_EN */
	return (U8)((ulData > 1) ? (32 - __clz(ulData - 1)) : 0);
#endif /* VS_SIM_EN */
}

INLINE U8 CntingBits(U32 ulCntValue)
{
	U8 ubCnt = 0;
	while (ulCntValue) {
		++ubCnt;
		ulCntValue &= (ulCntValue - 1);
	}
	return ubCnt;
}

INLINE U16 CalulateGreatestCommonDivisor(U16 uwNum1, U16 uwNum2)
{
	while ((uwNum1 > 0) && (uwNum2 > 0)) {
		if (uwNum1 > uwNum2) {
			uwNum1 = uwNum1 % uwNum2;
		}
		else {
			uwNum2 = uwNum2 % uwNum1;
		}
	}
	return (uwNum1 == 0) ? uwNum2 : uwNum1;
}
#endif/*_MATH_OP_*/
