
#include "err_hdl_fpl_softbit_retry.h"
#if (((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC) && (TRUE == IM_N48R)) /*|| (((CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_HYNIX_3D_QLC)) && ((TRUE == IM_V6)||(TRUE == IM_V7)||(TRUE == IM_V8)||(TRUE == IM_V5))) || ((CONFIG_FLASH_TYPE == FLASH_TYPE_BICSX_3D_TLC) && ((TRUE == IM_BICS5) || (TRUE == IM_BICS6) || (TRUE == IM_BICS8)))*/)//Duson Porting BICS5 Add //Reip Porting 3D-V7 QLC Add//Jeffrey Porting 3D V8 TLC Add//zerio BICS8 Add
//Dylan for V6 RDT porting,for Error: L6218E: Undefined symbol gSBTask (referred from read_retry.o).
#include "debug/debug_setup.h"
#include "err_hdl_fpl_sb_retry_api.h"
#include <string.h>
#include "hal/fip/fpu.h"
#include "retry_hb.h"
#include "err_hdl_fpl_softbit_retry_table.h"
#include "err_hdl_fpl_RS.h"
#include "hal/db/db_reg.h"
#include "hal/db/db_api.h"
#include "hal/fip/fip_reg.h"
#include "hal/fip/fip_api.h"
#include "debug/debug.h"
#include "hal/dmac/dmac_api.h"
#include "hal/dmac/dmac_pop_cmd.h"
#include "nvme_api/nvme/shr_hal_nvme_vuc_api.h"
#include "err_hdl_fpl_SoftbitRaid.h"
#include "err_hdl_fpl_TurboRain.h"

#include "hal/pic/uart/uart_api.h"
#include "retry/stall.h"
#include "retry/patch_cmd.h"
#include "hal/fip/fip.h"

#define SB_MICRON_NO_NEED_SET_SECOND_FEATURE_ADDRESS			(0)
#define SB_MICRON_NEED_SET_SECOND_FEATURE_ADDRESS				(1)
#define SB_MICRON_SETTING_SECOND_FEATURE_ADDRESS				(2)

#define SB_MICRON_DEFAULT_LLR_TABLE_START_IDX		(0)

//For Micron QLC
#define SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_QLC_LOWER_BCH_INDEX		(0 * SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH)
#define SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_QLC_LOWER_BAH_INDEX		(1 * SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH)
#define SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_QLC_LOWER_B9H_INDEX		(2 * SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH)
#define SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_QLC_LOWER_BEH_INDEX		(3 * SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH)
#define SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_QLC_UPPER_BCH_INDEX		(4 * SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH)
#define SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_QLC_UPPER_BDH_INDEX		(5 * SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH)
#define SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_QLC_UPPER_BEH_INDEX		(6 * SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH)
#define SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_QLC_UPPER_BFH_INDEX		(7 * SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH)
#define SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_QLC_XTRA_B9H_INDEX		(8 * SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH)
#define SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_QLC_XTRA_B8H_INDEX		(9 * SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH)
#define SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_QLC_XTRA_BBH_INDEX		(10 * SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH)
#define SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_QLC_TOP_BDH_INDEX			(11 * SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH)
#define SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_QLC_TOP_BBH_INDEX			(12 * SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH)
#define SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_QLC_TOP_BAH_INDEX			(13 * SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH)
#define SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_QLC_TOP_BFH_INDEX			(14 * SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH)
#define SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_SLC_E1H_INDEX				(15 * SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH)
#define SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_MLC_LOWER_E2H_INDEX		(16 * SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH)
#define SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_MLC_UPPER_E1H_INDEX		(17 * SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH)
#define SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_MLC_UPPER_E2H_INDEX		(18 * SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH)

#define SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_LENGTH (19*SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH)
#define SB_MICRON_SET_FEATURE_P1_DATA_INDEX			(0)
#define SB_MICRON_SET_FEATURE_P2_DATA_INDEX			(1)
#define SB_MICRON_SET_FEATURE_P3_DATA_INDEX			(2)
#define SB_MICRON_SET_FEATURE_P4_DATA_INDEX			(3)

//The Index is Mapping to delta_table Structure with 1H2S_SB1, 1H2S_SB2, 1H1S_SB1
#define SB_MICRON_DELTA_TABLE_DEFAULT			(0)
#define SB_MICRON_DELTA_TABLE_1H2S_SOFTBIT_1			(1)
#define SB_MICRON_DELTA_TABLE_1H2S_SOFTBIT_2			(2)
#define SB_MICRON_DELTA_TABLE_1H1S_SOFTBIT_1			(3)
#define SB_MICRON_DELTA_TABLE_SOFTBIT_NUM		(4)

#define RETRY_SB_MICRON_SLC_MLC_INIT_STAGE	(RETRY_SB_MICRON_STAGE_3)
#define RETRY_SB_MICRON_QLC_INIT_STAGE	(RETRY_SB_MICRON_STAGE_1)

#define RETRY_SB_MICRON_SLC_MLC_INIT_STEP	(RETRY_SB_MICRON_SLC_MLC_STEP_MIN1SW_PRFIX_RR_ARC1)
#define RETRY_SB_MICRON_QLC_INIT_STEP	(RETRY_SB_MICRON_STEP_NORMAL_READ)

#define M_RETRY_SB_CHECK_IF_SLC_MLC_PAGETYPES() 	((gSBTask.ubPageType <= RETRY_SB_MICRON_MLC_UPPER_PAGE) ? TRUE : FALSE)
#define M_RETRY_SB_GET_CURRENT_PRFIX_RR_IDX_SKIP_RR0()	 (gSBTask.ubCurrentStep - RETRY_SB_MICRON_STEP_PREFIX_RR1_FINE_CALIB_PERSISTENT_FINE_CALIB + 1)	// Plus 1 for skip RR0
#define M_RETRY_SB_STEP_TO_PRFIX_RR_IDX_SKIP_RR0(ubStep_M)	((RETRY_SB_MICRON_STEP_PERSISTENT_FINE_CALIB == ubStep_M) ? 0 : (ubStep_M - RETRY_SB_MICRON_STEP_PREFIX_RR1_FINE_CALIB_PERSISTENT_FINE_CALIB + 1))	//If is Step1.1, PrefixRRIdx should be 0, Others Plus 1 for skip RR0)

#define INVALID_RETRY_SB_STEP_INDEX	(0xFF)
#define INVALID_RETRY_SB_SW_VALUE		(0xFFFF)

//TODO N48R Decoding Flow 0707
#define PERSISTENT_FINE_CALIBRATION (0)
#define CBC (0)

#define RETRY_SB_ORIGIN_EN	(FALSE)



RETRY_SB_TASK_STRUCT gSBTask;
SoftBitMinSWInfo_t gSBMinSWInfo;
ReadOffset4Param_t gRetrySBFineCalibReadOffset;

U8 gubSBMicronPreFixRRTable[RETRY_SB_MICRON_READ_VOLTAGE_OFFSETS][RETRY_SB_MICRON_PREFIX_READ_RETRY_NUM] =  {
	//RR0 RR1 RR2 RR3 RR4 RR5 RR6 RR7 RR8
	{0x00, 0x1E, 0x3C, 0x5A, 0xF2, 0xE4, 0xD6, 0xC8, 0xBA}, //rSLC
	{0x00, 0x00, 0xF5, 0xED, 0xFB, 0xF6, 0xF1, 0xEC, 0xE7}, //rL2_2bps
	{0x00, 0x00, 0xFC, 0xF9, 0xFD, 0xFA, 0xF7, 0xF4, 0xF1}, //rL1_2bps
	{0x00, 0x00, 0xEB, 0xE2, 0xF8, 0xF0, 0xE8, 0xE0, 0xD8}, //rL3_2bps
	{0x00, 0x0C, 0x18, 0x0E, 0x00, 0x00, 0xF0, 0x00, 0x0C}, //rL1_4bps
	{0x00, 0x03, 0x01, 0x00, 0xFF, 0xFE, 0xFD, 0xFC, 0x06}, //rL4_4bps
	{0x00, 0x01, 0xFC, 0xF9, 0xFD, 0xFA, 0xF7, 0xF4, 0x06}, //rL6_4bps
	{0x00, 0xFE, 0xF2, 0xEC, 0xF9, 0xF2, 0xEB, 0xE4, 0x04}, //rL11_4bps
	{0x00, 0x04, 0x04, 0x02, 0x00, 0x00, 0x00, 0x00, 0x05}, //rL3_4bps
	{0x00, 0x00, 0xFB, 0xF6, 0xFD, 0xFA, 0xF7, 0xF4, 0x06}, //rL7_4bps
	{0x00, 0xFF, 0xF6, 0xF1, 0xFB, 0xF6, 0xF1, 0xEC, 0x05}, //rL9_4bps
	{0x00, 0xFD, 0xEB, 0xE4, 0xF8, 0xF0, 0xE8, 0xE0, 0x03}, //rL13_4bps
	{0x00, 0x06, 0x09, 0x08, 0x00, 0x00, 0x00, 0x00, 0x04}, //rL2_4bps
	{0x00, 0xFF, 0xF9, 0xF4, 0xFC, 0xF8, 0xF4, 0xF0, 0x06}, //rL8_4bps
	{0x00, 0xFD, 0xE7, 0xE0, 0xF7, 0xEE, 0xE5, 0xDC, 0x02}, //rL14_4bps
	{0x00, 0x02, 0xFE, 0xFB, 0xFE, 0xFC, 0xFA, 0xF8, 0x06}, //rL5_4bps
	{0x00, 0xFF, 0xF5, 0xEE, 0xFA, 0xF4, 0xEE, 0xE8, 0x04}, //rL10_4bps
	{0x00, 0xFE, 0xEF, 0xE8, 0xF9, 0xF2, 0xEB, 0xE4, 0x04}, //rL12_4bps
	{0x00, 0xFC, 0xE3, 0xDA, 0xF5, 0xEA, 0xDF, 0xD4, 0x02} //rL15_4bps
};

U16 guwSBMicronSetMLBiAddrTable[RETRY_SB_MICRON_SET_MLBI_NUM] = {
	0x0218, 0x01EA, 0x0AE7, 0x00C0, 0x00C1, 0x00C2,
	0x00C3, 0x00C4, 0x00C5, 0x00C6, 0x00C7, 0x00C8,
	0x00C9, 0x00CA, 0x00CB, 0x00CC, 0x00CD, 0x00CE
};

U8 gubSBMicronDefaultMLBiValueTable[RETRY_SB_MICRON_SET_MLBI_NUM] = {

};
/*
 * ---------------------------------------------------------------------------------------------------
 *   static global variables
 * ---------------------------------------------------------------------------------------------------
 */
#if (RETRY__ENABLE_SB_FLOW)

#if(RETRY_SOFTBITT_FOR_SDK_EN==FALSE)
AOM_FLH_ERR_SB_CALLBACK static void RetrySBClearRROffsetPass_Callback(U8 ubMTIndex);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBClearRROffsetFail_Callback(U8 ubMTIndex);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBHBSetFeatureCRRPass_Callback(U8 ubMTIndex);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBHBSetFeatureCRFail_Callback(U8 ubMTIndex);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBHBSetFeatureARCPass_Callback(U8 ubMTIndex);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBHBSetFeatureARCFail_Callback(U8 ubMTIndex);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBTriggerCorrectFail_Callback(U8 ubMTIndex);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBTriggerCorrectPass_Callback(U8 ubMTIndex);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBHBMTLDPCDecodeFail_Callback(U8 ubMTIndex);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBHBMTLDPCDecodePass_Callback(U8 ubMTIndex);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBSetReadRetryDeltaPass_Callback(U8 ubMTIndex);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBSetReadRetryDeltaFail_Callback(U8 ubMTIndex);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBReadHBPass_Callback(U8 ubMTIndex);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBReadHBFail_Callback(U8 ubMTIndex);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBResetFlashPass_Callback(U8 ubMTIndex);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBResetFlashFail_Callback(U8 ubMTIndex);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBReadSB1Pass_Callback(U8 ubMTIndex);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBReadSB1Fail_Callback(U8 ubMTIndex);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBReadSB2Pass_Callback(U8 ubMTIndex);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBReadSB2Fail_Callback(U8 ubMTIndex);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBHBSetFeatureCRPass_Callback(U8 ubMTIndex);

AOM_FLH_ERR_SB_CALLBACK static void RetrySBReadHB4KDMAPass_Callback(U8 ubMTIdx);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBReadHB4KDMAFail_Callback(U8 ubMTIdx);
AOM_FLH_ERR_SB_CALLBACK static void RetrySBHBReadCMDOnlyPass_Callback(U8 ubMTIdx);

AOM_FLH_ERR_SB_CALLBACK static void RetrySBReadHBMTFail_Callback(U8 ubMTIndex);

AOM_RETRY_SB static void RetrySBResetFlash(void);
AOM_RETRY_SB static void RetrySBSelectDMAMTFPUTrigger( U16 uwFpu_offset, U32 ulFSA, U8 ubMTIndex);
AOM_RETRY_SB static void RetrySBCorrectMTTrigger( U8 hb_ibuf_ptr, U8 sb_ibuf_ptr, U8 sel_2k, U8 dsp_en, U8 dsp_4k_en, U8 ubdecode_mode, U8 scale_mode, U8 flow_mode, U8 hb_sb, U8 ubMTIndex);
AOM_RETRY_SB static U8 RetrySBPrepareFreeMT(MT_Callback_Func_t PassCallback, MT_Callback_Func_t FailCallback);
AOM_RETRY_SB static void RetrySBReleaseFinishMT(void);
AOM_RETRY_SB static U8 RetrySBInit(void);
AOM_RETRY_SB static void RetrySBInitStageStep(void);
AOM_RETRY_SB static void RetrySBInitMinSWInfoAndRecordReadOffset (void);
AOM_RETRY_SB static void RetrySBSwitchStateToSetDefaultFeatureReset(void);
AOM_RETRY_SB static void RetrySBFlashLoadECCParam(U32 load_target);
AOM_RETRY_SB static void RetrySBFlashSetECCParam(U32 *data, U32 len, U32 load_target, U8 ubMode);
#if (RETRY_SB_ORIGIN_EN)
AOM_RETRY_SB static void RetrySBReadFlow(void);
#endif /*(RETRY_SB_ORIGIN_EN)*/
AOM_RETRY_SB static void RetrySBTurboRainInit(void);
AOM_RETRY_SB static void RetrySBReadFlowNew(void);
AOM_RETRY_SB static void RetrySBDecodeFlowNew(void);
#if (RETRY_SB_ORIGIN_EN)
AOM_RETRY_SB static U8 RetrySBHBReadwithARC(void);
#endif /*(RETRY_SB_ORIGIN_EN)*/
AOM_RETRY_SB static void RetrySBHBRAWReadSBCDecode(void);
AOM_RETRY_SB static void RetrySBSetCR(U8 ubIsCR, U8 ubIsEnhanced);
AOM_RETRY_SB static void RetrySBClearRROffset();
AOM_RETRY_SB static void RetrySBGetCBC(void);
AOM_RETRY_SB static void RetrySBSet7thAddress(U8 ubMode);
AOM_RETRY_SB static void RetrySBSetFPUPrefixRRWithPrefixRRTable(U8 ubCurrnetPrefixRRIdx, ReadOffset4Param_t Offset);
AOM_RETRY_SB static void RetrySBSetFPUPrefixRR(U8 ubPrefixRRParam0, U8 ubPrefixRRParam1, U8 ubPrefixRRParam2, U8 ubPrefixRRParam3);
AOM_RETRY_SB static void RetrySBHB4KReadMTDecode(void);
AOM_RETRY_SB static void RetrySBUpdateStageStep(void);
AOM_RETRY_SB static void RetrySBHBSetFeatureARC(U8 ubCalibration, U8 ubPersistence);
AOM_RETRY_SB static void RetrySBMTTriggerRetry(U8 ubClearFlag);
AOM_RETRY_SB static void RetrySBSelecMTFPUTrigger(U16 uwFpu_offset, U32 ulFSA, U8 flh_type_legacy, U8 ubMTIndex, U8 ubMode);
AOM_RETRY_SB static void RetrySBFlashRetoreLLR(U8 lmu_sel, U8 llr_idx);
AOM_RETRY_SB static void RetrySBBackupCorrect2k(U8 src_ibf, U8 ldpc_frame_idx, U32 ulbackup_addr, U32 ultmp_addr, U32 Iram_addr, U32 TempIram_addr, U8 ubMode);
AOM_RETRY_SB static U32 RetrySBFlashGetCurrentLDPCFrameSize(void);
AOM_RETRY_SB static void RetrySBBackupRestore(U64 dram_addr, U32 iram_addr, U8 ibf_ptr, U8 direction, U8 mode, U8 bch_mode, U8 all, U8 frm_msk);
AOM_RETRY_SB static void RetrySBSwitchStatebyStageStep(U8 ubNeedCleanPreviousStageStepSettings, U8 ubStageNeedtoClean, U8 ubStepNeedtoClean );
AOM_RETRY_SB static void RetrySBSwitchPostConditionThenDoBackupSBFlow(U8 ubBackUpedState, U8 ubBackUpedSubstate, U8 ubStageNeedtoClean, U8 ubStepNeedtoClean);
AOM_RETRY_SB static void RetrySBSwitchToSetDefaultFeatureThenDoBackupSBFlow(U8 ubBackUpedState, U8 ubBackUpedSubstate);
AOM_RETRY_SB static void RetrySBFPUTrigger(U16 uwFpu_offset, U8 int_en, U16 vct);
#if (RETRY_SB_ORIGIN_EN)
AOM_RETRY_SB static void RetrySBDecodeFlow (void);
#endif /*(RETRY_SB_ORIGIN_EN)*/
AOM_RETRY_SB static void RetrySBClearSBOffsetFeatures( U8 ubPageType, U8 *pubReadRetryTable);
AOM_RETRY_SB static void RetrySBSetFeatureByReadRetryDeltaTableOffset( U8 ubPageType, U8 *pubReadRetryTable, U8 ubBiosValue);
AOM_RETRY_SB static void RetrySBReadHB( U8 page_type, U8 dsp_en, U32 ulFSA, U8 ldpc_frame_idx, U8 src_ibuf, U8 dst_ibuf, U8 ibuf_frame_sel);
AOM_RETRY_SB static void RetrySBReadSB1( U8 ubPageType, U8 *pubReadRetryTable, U8 ldpc_frame_idx);
AOM_RETRY_SB static void RetrySBReadSB2( U8 ubPageType, U8 *pubReadRetryTable, U8 ldpc_frame_idx);
AOM_RETRY_SB void RetrySBSetDefaultFeatureAndCheck(void);
AOM_RETRY_SB static void  RetrySBFlaSetMLBi(U32 uliFSA, U16 uwAddr, U8 pubFeature);
AOM_RETRY_SB static void  RetrySBFlaGetMLBi(U32 uliFSA, U16 uwAddr, U8 *pubFeature);
AOM_RETRY_SB static void RetrySBSetEnhanceCRTrim(void);
#if (ERROR_HANDLE_VALIDATION)
AOM_RETRY_SB static U16 RetrySBGetTrimValue(U32 ulWLgroup, U8 ubPageType, U8 ubIdx);
AOM_RETRY_SB static void RetrySBGetPrefixRR(U8 ubCurrnetPrefixRRIdx, U8 *pubPrefixRRValue);
AOM_RETRY_SB static U32 RetrySBGetTotalReadOffset(U8 ubStage, U8 ubStep, U32 ulValleyTrackReadOffset);
#endif /* (ERROR_HANDLE_VALIDATION) */

#if (ERROR_HANDLE_VALIDATION)
void RetrySBGetPrefixRR(U8 ubCurrnetPrefixRRIdx, U8 *pubPrefixRRValue)
{
	//U32 ulResult = 0;
	if (RETRY_SB_MICRON_QLC_LOWER_PAGE == gSBTask.ubPageType) {
		pubPrefixRRValue[0] = gubSBMicronPreFixRRTable[4][ubCurrnetPrefixRRIdx] & 0xff;
		pubPrefixRRValue[1] = gubSBMicronPreFixRRTable[5][ubCurrnetPrefixRRIdx] & 0xff;
		pubPrefixRRValue[2] = gubSBMicronPreFixRRTable[6][ubCurrnetPrefixRRIdx] & 0xff;
		pubPrefixRRValue[3] = gubSBMicronPreFixRRTable[7][ubCurrnetPrefixRRIdx] & 0xff;
	}
	else if (RETRY_SB_MICRON_QLC_UPPER_PAGE == gSBTask.ubPageType) {
		pubPrefixRRValue[0] = gubSBMicronPreFixRRTable[8][ubCurrnetPrefixRRIdx] & 0xff;
		pubPrefixRRValue[1] = gubSBMicronPreFixRRTable[9][ubCurrnetPrefixRRIdx] & 0xff;
		pubPrefixRRValue[2] = gubSBMicronPreFixRRTable[10][ubCurrnetPrefixRRIdx] & 0xff;
		pubPrefixRRValue[3] = gubSBMicronPreFixRRTable[11][ubCurrnetPrefixRRIdx] & 0xff;
	}
	else if (RETRY_SB_MICRON_QLC_EXTRA_PAGE == gSBTask.ubPageType) {
		pubPrefixRRValue[0] = gubSBMicronPreFixRRTable[12][ubCurrnetPrefixRRIdx] & 0xff;
		pubPrefixRRValue[1] = gubSBMicronPreFixRRTable[13][ubCurrnetPrefixRRIdx] & 0xff;
		pubPrefixRRValue[2] = gubSBMicronPreFixRRTable[14][ubCurrnetPrefixRRIdx] & 0xff;
		pubPrefixRRValue[3] = 0;
	}
	else {	//RETRY_SB_MICRON_QLC_TOP_PAGE
		pubPrefixRRValue[0] = gubSBMicronPreFixRRTable[15][ubCurrnetPrefixRRIdx] & 0xff;
		pubPrefixRRValue[1] = gubSBMicronPreFixRRTable[16][ubCurrnetPrefixRRIdx] & 0xff;
		pubPrefixRRValue[2] = gubSBMicronPreFixRRTable[17][ubCurrnetPrefixRRIdx] & 0xff;
		pubPrefixRRValue[3] = gubSBMicronPreFixRRTable[18][ubCurrnetPrefixRRIdx] & 0xff;
	}
}
U16 RetrySBGetTrimValue(U32 ulWLgroup, U8 ubPageType, U8 ubIdx)
{
	U16 uwbase = 0x40;
	U16 uwgrp = 0x10;
	U16 uwResult = 0;

	switch (ubPageType) {
	case RETRY_SB_MICRON_QLC_LOWER_PAGE:
		switch (ubIdx) {
		case 0:
			uwResult = (uwbase + (uwgrp * (ulWLgroup)) + 0);
			break;
		case 1:
			uwResult = (uwbase + (uwgrp * (ulWLgroup)) + 3);
			break;
		case 2:
			uwResult = (uwbase + (uwgrp * (ulWLgroup)) + 5);
			break;
		case 3:
			uwResult = (uwbase + (uwgrp * (ulWLgroup)) + 10);
			break;
		default:
			break;
		}
		break;
	case RETRY_SB_MICRON_QLC_UPPER_PAGE:
		switch (ubIdx) {
		case 0:
			uwResult = (uwbase + (uwgrp * (ulWLgroup)) + 2);
			break;
		case 1:
			uwResult = (uwbase + (uwgrp * (ulWLgroup)) + 6);
			break;
		case 2:
			uwResult = (uwbase + (uwgrp * (ulWLgroup)) + 8);
			break;
		case 3:
			uwResult = (uwbase + (uwgrp * (ulWLgroup)) + 12);
			break;
		default:
			break;
		}
		break;
	case RETRY_SB_MICRON_QLC_EXTRA_PAGE:
		switch (ubIdx) {
		case 0:
			uwResult = (uwbase + (uwgrp * (ulWLgroup)) + 1);
			break;
		case 1:
			uwResult = (uwbase + (uwgrp * (ulWLgroup)) + 7);
			break;
		case 2:
			uwResult = (uwbase + (uwgrp * (ulWLgroup)) + 13);
			break;
		default:
			break;
		}
		break;
	case RETRY_SB_MICRON_QLC_TOP_PAGE:
		switch (ubIdx) {
		case 0:
			uwResult = (uwbase + (uwgrp * (ulWLgroup)) + 4);
			break;
		case 1:
			uwResult = (uwbase + (uwgrp * (ulWLgroup)) + 9);
			break;
		case 2:
			uwResult = (uwbase + (uwgrp * (ulWLgroup)) + 11);
			break;
		case 3:
			uwResult = (uwbase + (uwgrp * (ulWLgroup)) + 14);
			break;
		default:
			break;
		}
		break;
	default:
		break;
	}
	return uwResult;
}
U32 RetrySBGetTotalReadOffset(U8 ubStage, U8 ubStep, U32 ulValleyTrackReadOffset)
{
	U8 ubSBMinSWidx = 0;
	U8 ubidx = 0;
	U8 ubCurrnetPrefixRRIdx = 0;
	U8 ubQLCTrimData[4] = {0};
	U8 ubTrimIdx = 0;
	U8 ubPrefixValue[4] = {0};
	U8 ubReadOffset[4] = {0};
	U8 ubTotalValue[4] = {0};
	U16 uwQLCTrimAddr[4] = {0};
	U32 ulTotalReadOffset = 0;

	//Trim value
	for (ubTrimIdx = 0; ubTrimIdx < 4; ubTrimIdx++) {
		uwQLCTrimAddr[ubTrimIdx] = RetrySBGetTrimValue(gSBTask.uwWLgroup, gSBTask.ubPageType, ubTrimIdx);
		RetrySBFlaGetMLBi(gSBTask.ulFSA_ori, uwQLCTrimAddr[ubTrimIdx], &ubQLCTrimData[ubTrimIdx]);
	}

	switch (ubStage) {
	case RETRY_SB_MICRON_STAGE_1:
	case RETRY_SB_MICRON_STAGE_4:
		//Prefix RR read offset
		ubCurrnetPrefixRRIdx = M_RETRY_SB_STEP_TO_PRFIX_RR_IDX_SKIP_RR0(gSBTask.ubCurrentStep);
		RetrySBGetPrefixRR(ubCurrnetPrefixRRIdx, &ubPrefixValue[0]);

		//Valley Track read offset
		ubReadOffset[3] = ((ulValleyTrackReadOffset >> 24) & 0xFF);
		ubReadOffset[2] = ((ulValleyTrackReadOffset >> 16) & 0xFF);
		ubReadOffset[1] = ((ulValleyTrackReadOffset >> 8) & 0xFF);
		ubReadOffset[0] = ((ulValleyTrackReadOffset) & 0xFF);

		for (ubidx = 0; ubidx < 4; ubidx++) {
			ubTotalValue[ubidx] = ubPrefixValue[ubidx] + ubQLCTrimData[ubidx] + ubReadOffset[ubidx];
		}
		ulTotalReadOffset = (((ubTotalValue[3] & 0xFF) << 24) | ((ubTotalValue[2] & 0xFF) << 16) | ((ubTotalValue[1] & 0xFF) << 8) | (ubTotalValue[0] & 0xFF));
		break;

	case RETRY_SB_MICRON_STAGE_5:
		if (ubStep == RETRY_SB_MICRON_STEP_MIN1SW_PRFIX_RR_FINE_CALIB_PERSISTENT_FINE_CALIB) {
			ubSBMinSWidx = 0;
		}
		else if (ubStep == RETRY_SB_MICRON_STEP_MIN2SW_PRFIX_RR_FINE_CALIB_PERSISTENT_FINE_CALIB) {
			ubSBMinSWidx = 1;
		}
		else {
			ubSBMinSWidx = 2;
		}
		//Min SW Prefix RR read offset
		for (ubidx = 0; ubidx < 4; ubidx++) {
			ubPrefixValue[ubidx] = gSBMinSWInfo.SWReadOffset[ubSBMinSWidx].ReadOffset4Param.Param.ubParam[ubidx];
		}
		//Valley Track read offset
		ubReadOffset[3] = ((ulValleyTrackReadOffset >> 24) & 0xFF);
		ubReadOffset[2] = ((ulValleyTrackReadOffset >> 16) & 0xFF);
		ubReadOffset[1] = ((ulValleyTrackReadOffset >> 8) & 0xFF);
		ubReadOffset[0] = (ulValleyTrackReadOffset & 0xFF);

		for (ubidx = 0; ubidx < 4; ubidx++) {
			ubTotalValue[ubidx] = ubPrefixValue[ubidx] + ubQLCTrimData[ubidx] + ubReadOffset[ubidx];
		}
		ulTotalReadOffset = (((ubTotalValue[3] & 0xFF) << 24) | ((ubTotalValue[2] & 0xFF) << 16) | ((ubTotalValue[1] & 0xFF) << 8) | (ubTotalValue[0] & 0xFF));
		break;
	case RETRY_SB_MICRON_STAGE_6:
		//Min1 SW Prefix RR read offset
		for (ubidx = 0; ubidx < 4; ubidx++) {
			ubPrefixValue[ubidx] = gSBMinSWInfo.SWReadOffset[0].ReadOffset4Param.Param.ubParam[ubidx];
		}

		ubReadOffset[3] = ((ulValleyTrackReadOffset >> 24) & 0xFF);
		ubReadOffset[2] = ((ulValleyTrackReadOffset >> 16) & 0xFF);
		ubReadOffset[1] = ((ulValleyTrackReadOffset >> 8) & 0xFF);
		ubReadOffset[0] = ((ulValleyTrackReadOffset) & 0xFF);

		for (ubidx = 0; ubidx < 4; ubidx++) {
			ubTotalValue[ubidx] = ubPrefixValue[ubidx] + ubQLCTrimData[ubidx] + ubReadOffset[ubidx];
		}
		ulTotalReadOffset = (((ubTotalValue[3] & 0xFF) << 24) | ((ubTotalValue[2] & 0xFF) << 16) | ((ubTotalValue[1] & 0xFF) << 8) | (ubTotalValue[0] & 0xFF));
		break;
	default:
		break;
	}
	return ulTotalReadOffset;

}
#endif /* (ERROR_HANDLE_VALIDATION) */
void RetrySBSetEnhanceCRTrim(void)
{
	U8 ubi;
	for (ubi = 0; ubi < RETRY_SB_MICRON_SET_MLBI_NUM ; ubi++) {
		RetrySBFlaGetMLBi(gSBTask.ulFSA_ori, guwSBMicronSetMLBiAddrTable[ubi], &gubSBMicronDefaultMLBiValueTable[ubi]);
		if (ubi == 0) {
			RetrySBFlaSetMLBi(gSBTask.ulFSA_ori, guwSBMicronSetMLBiAddrTable[ubi], 0x23);
		}
		else if (ubi == 1) {
			RetrySBFlaSetMLBi(gSBTask.ulFSA_ori, guwSBMicronSetMLBiAddrTable[ubi], 0x64);
		}
		else if (ubi == 2) {
			RetrySBFlaSetMLBi(gSBTask.ulFSA_ori, guwSBMicronSetMLBiAddrTable[ubi], 0x00);
		}
		else {
			RetrySBFlaSetMLBi(gSBTask.ulFSA_ori, guwSBMicronSetMLBiAddrTable[ubi], gubSBMicronDefaultMLBiValueTable[ubi] - 0x15);
		}
	}
}
void  RetrySBFlaSetMLBi(U32 uliFSA, U16 uwAddr, U8 pubFeature)
{
	U8 ubChannel, ubCE;
	ubChannel = (uliFSA >> gPCARule_Channel.ubShift[0]) & gPCARule_Channel.ulMask;
	ubCE = (uliFSA >> gPCARule_Bank.ubShift[0]) & gPCARule_Bank.ulMask;
	REG32 *pFlaReg = R32_FCTL_CH[ubChannel];

	FIPWaitFIPInternalReady( ubChannel, GERNAL_TIMEOUT_THRESHOLD);

	FlaCEControl(ubChannel, ubCE, ENABLE);

	pFlaReg[R32_FCTL_PIO_CMD] = 0xEB;
	pFlaReg[R32_FCTL_PIO_ADR] = (uwAddr & BIT_MASK(8));
	pFlaReg[R32_FCTL_PIO_ADR] = ((uwAddr >> 8) & BIT_MASK(8));
	pFlaReg[R32_FCTL_PIO_ADR] = 0;

	__asm("DSB");
	IdlePC(200);
	pFlaReg[R32_FCTL_HS_MODE] |= SET_DAT_DEFAULT;
	__asm("DSB");
	pFlaReg[R32_FCTL_PIO_DAT] = ((pubFeature << 8) | pubFeature);
	__asm("DSB");


	FlaWaitFlashReady(GERNAL_TIMEOUT_THRESHOLD);

	FlaCEControl(ubChannel, ubCE, DISABLE);

}

void  RetrySBFlaGetMLBi(U32 uliFSA, U16 uwAddr, U8 *pubFeature)
{
	U8 ubChannel, ubCE;
	ubChannel = (uliFSA >> gPCARule_Channel.ubShift[0]) & gPCARule_Channel.ulMask;
	ubCE = (uliFSA >> gPCARule_Bank.ubShift[0]) & gPCARule_Bank.ulMask;
	REG32 *pFlaReg = R32_FCTL_CH[ubChannel];

	FIPWaitFIPInternalReady( ubChannel, GERNAL_TIMEOUT_THRESHOLD);

	FlaCEControl(ubChannel, ubCE, ENABLE);

	pFlaReg[R32_FCTL_PIO_CMD] = 0xEA;
	pFlaReg[R32_FCTL_PIO_ADR] = (uwAddr & BIT_MASK(8));
	pFlaReg[R32_FCTL_PIO_ADR] = ((uwAddr >> 8) & BIT_MASK(8));
	pFlaReg[R32_FCTL_PIO_ADR] = 0;
	__asm("DSB");

	FlaWaitFlashReady(GERNAL_TIMEOUT_THRESHOLD);

	pFlaReg[R32_FCTL_HS_MODE] |= SET_DAT_DEFAULT;
	__asm("DSB");

	*pubFeature = (U8)pFlaReg[R32_FCTL_PIO_DAT];
	FlaCEControl(ubChannel, ubCE, DISABLE);
}


#else /*(RETRY_SOFTBITT_FOR_SDK_EN==FALSE)*/
#endif /*(RETRY_SOFTBITT_FOR_SDK_EN==FALSE)*/

#if(TRUE == RETRY_MICRON_NICKS)

void RetrySBResetFlash(void)
{
	U16 uwFpu_offset;
	U8 ubMTIndex;
	uwFpu_offset = guwFPUEntryResetFAh[gSBTask.ublun];
	ubMTIndex = RetrySBPrepareFreeMT(RetrySBResetFlashPass_Callback, RetrySBResetFlashFail_Callback);
	RetrySBSelecMTFPUTrigger( uwFpu_offset, gSBTask.MTTemplate.dma.uliFSA0_1, 0, ubMTIndex, SWITCH_CLK__NORMAL_MODE);
	if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
		gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
	}
	else {
		gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
		gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
	}
}

U32 RetrySBFlashGetCurrentLDPCFrameSize(void)
{
	return ldpc_frame_size[M_GET_ECC_MODE()];
}

void RetrySBMTTriggerRetry(U8 ubClearFlag)
{
	if (gSBTask.ubMTFailCnt < RETRY_SB_MT_FAIL_RETRY_CNT_MAX) {
		gSBTask.ubMTFailCnt++;
	}
	else {
		gSBTask.ubState = RETRY_SB_STATE_SB_RESET_FLH;
		/*
			Normal flow MT Fail(Case A/B)	->	RETRY_SB_STATE_SB_RESET_FLH, BackupState = RETRY_SB_STATE_TERMINATE_FAIL, terminate Case A/B after Reset
			Normal flow MT Fail(Case C)		->	RETRY_SB_STATE_SB_RESET_FLH, BackupState = RETRY_SB_STATE_CHECK_NEXT_STATE, terminate Case C and move to next 2K_Frame after Reset

			SetDefaultFeature MT Fail when leaving SB flow		->	RETRY_SB_STATE_SB_RESET_FLH, BackupState = RETRY_SB_STATE_DONE, Leave SB after Reset

			Reset MT Fail		->	RETRY_SB_STATE_DONE, Leave SB directly
		*/
		if ((RETRY_SB_STATE_DONE == gSBTask.ubBackUpedState)
			|| (RETRY_SB_STATE_INIT == gSBTask.ubBackUpedState)) {
			gSBTask.ubState = RETRY_SB_STATE_DONE;
		}
		else {
			if (RETRY_SB_STATE_SB_RESET_FLH == gSBTask.ubBackUpedState) {		//SetDefaultFeature MT Fail when leaving SB flow
				gSBTask.ubBackUpedState = RETRY_SB_STATE_DONE;
			}
			else {															// Normal flow MT Fail
				if (RETRY_SB_STATE_CHECK_NEXT_STATE != gSBTask.ubBackUpedState) {
					gSBTask.ubBackUpedState = RETRY_SB_STATE_TERMINATE_FAIL;
				}
			}
		}
		if (TRUE == ubClearFlag) {
			//Clear Flag for second FA
			gSBTask.ubTotalFeatureAddr = SB_MICRON_NO_NEED_SET_SECOND_FEATURE_ADDRESS;
			gSBTask.ubCurrentFeatureAddr = SB_MICRON_NO_NEED_SET_SECOND_FEATURE_ADDRESS;
		}
	}
}

void RetrySBReadSB1Pass_Callback(U8 ubMTIndex)
{
	gSBTask.ubMTFailCnt = 0;
	gSBTask.ubState =  gSBTask.ubPrevState;
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;

	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_SB_READ_SB1_DELTA_READ:
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SB_READ_SB1_DELTA_DMA;
		break;
	case RETRY_SB_3RD_STATE_SB_READ_SB1_DELTA_DMA:
#if (RETRY_SB_ORIGIN_EN)
		gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_SB2;
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SB_READ_SB2_INIT;
#else /*(RETRY_SB_ORIGIN_EN)*/
		if (RETRY_MICRON_TABLR_PARAMETER_1H1S == gSBTask.ubSBRead1H1Sor1H2S) {
			gSBTask.ubState = RETRY_SB_STATE_LOAD_DEFAULT_LLR;
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_NULL;
		}
		else {
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_READ_SB2;
			gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SB_READ_SB2_INIT;
		}
#endif /*(RETRY_SB_ORIGIN_EN)*/
		break;
	}
}

void RetrySBReadSB1Fail_Callback(U8 ubMTIndex)
{
	gSBTask.ubState =  gSBTask.ubPrevState;
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;

	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_SB_READ_SB1_DELTA_READ:
		RetrySBMTTriggerRetry(FALSE);
		break;
	case RETRY_SB_3RD_STATE_SB_READ_SB1_DELTA_DMA:
		RetrySBReadSB1Pass_Callback(ubMTIndex);
		break;
	}
}

void RetrySBReadSB2Pass_Callback(U8 ubMTIndex)
{
	gSBTask.ubMTFailCnt = 0;
	gSBTask.ubState =  gSBTask.ubPrevState;
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;

	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_SB_READ_SB2_DELTA_READ:
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SB_READ_SB2_DELTA_DMA;
		break;
	case RETRY_SB_3RD_STATE_SB_READ_SB2_DELTA_DMA:
		if (gSBTask.ubIsFromTurboRain) {
			// No need to decode
			gSBTask.ubState = RETRY_SB_STATE_SET_DEFAULT_FEATURE_AND_CHK;
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_INIT;
		}
		else {
			gSBTask.ubState = RETRY_SB_STATE_LOAD_DEFAULT_LLR;
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_NULL;
		}
		break;
	}
}

void RetrySBReadSB2Fail_Callback(U8 ubMTIndex)
{
	gSBTask.ubState =  gSBTask.ubPrevState;
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;

	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_SB_READ_SB2_DELTA_READ:
		RetrySBMTTriggerRetry(FALSE);
		break;
	case RETRY_SB_3RD_STATE_SB_READ_SB2_DELTA_DMA:
		RetrySBReadSB2Pass_Callback(ubMTIndex);
		break;
	}
}

void RetrySBResetFlashPass_Callback(U8 ubMTIndex)
{
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
	gSBTask.ubMTFailCnt = 0;
	gSBTask.ubState = gSBTask.ubBackUpedState;
	gSBTask.ubBackUpedState = RETRY_SB_STATE_BACKIPSTATE_NON_STATE;
}

void RetrySBResetFlashFail_Callback(U8 ubMTIndex)
{
	gSBTask.ubState = gSBTask.ubPrevState;
	// REAL problem.....

	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);

	RetrySBMTTriggerRetry(FALSE);
}

void RetrySBReadHBFail_Callback(U8 ubMTIndex)
{
	gSBTask.ubState =  gSBTask.ubPrevState;
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_HB_READ_DIRECT_READ_OP:
		RetrySBMTTriggerRetry(FALSE);
		break;
	case RETRY_SB_3RD_STATE_HB_READ_DIRECT_READ_DMA:
		RetrySBReadHBPass_Callback(ubMTIndex);
		break;
	}
}

void RetrySBReadHBPass_Callback(U8 ubMTIndex)
{
	gSBTask.ubMTFailCnt = 0;
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
	gSBTask.ubState =  gSBTask.ubPrevState;

	switch (gSBTask.ubThirdstate) {

	case RETRY_SB_3RD_STATE_HB_READ_DIRECT_READ_OP:
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_HB_READ_DIRECT_READ_DMA;
		break;

	case  RETRY_SB_3RD_STATE_HB_READ_DIRECT_READ_DMA:
		switch (gSBTask.ubSubstate) {
#if (RETRY_SB_ORIGIN_EN)
		case RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_HB_READ:
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_HB_CORR_2K;
			gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_NULL;
			break;
		case RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_HB:
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_SB1;
			gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SB_READ_SB1_INIT;
			break;
#else /*(RETRY_SB_ORIGIN_EN)*/
		case RETRY_SB_SUBSTATE_HB_RAW_READ:
			if (RETRY_SB_MICRON_STEP_NORMAL_READ == gSBTask.ubCurrentStep) {
				RetrySBGetCBC();
				// Just read, no need decoding
				RetrySBUpdateStageStep();
				RetrySBSwitchStatebyStageStep(TRUE, gSBTask.ubCurrentStage, gSBTask.ubCurrentStep);
			}
			else if (((gSBTask.ubSBSBRSecondRound == 0) && (RETRY_SB_MICRON_STEP_EXPRESS_SBSBR_1_2 == gSBTask.ubCurrentStep))) {
				gSBTask.ubSBSBRSecondRound = 1;
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_RAW_READ_INIT;
			}
			else {
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_RAW_READ_SBC_HB_CORR_2K;
				gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_NULL;
			}
			break;
		case RETRY_SB_SUBSTATE_SB_READ_HB:
			if (gSBTask.ubIsFromTurboRain) {
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_READ_HB;
				gSBTask.ubThirdstate = RETRY_SB_3RD_HB_READ_DIRECT_READ_FOR_TURBO_RAIN;
			}
			else {
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_READ_SB1;
				gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SB_READ_SB1_INIT;
			}
			break;

#endif /*(RETRY_SB_ORIGIN_EN)*/
		default:
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
			break;
		}
		break;
	case RETRY_SB_3RD_HB_READ_DIRECT_READ_FOR_TURBO_RAIN:
		gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_READ_SB1;
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SB_READ_SB1_INIT;

		break;
	default:
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
		break;
	}
}

void RetrySBReadHBMTFail_Callback(U8 ubMTIndex)
{
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
	RetrySBSwitchStateToSetDefaultFeatureReset();
}

void RetrySBClearRROffsetPass_Callback(U8 ubMTIndex)
{
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
	gSBTask.ubMTFailCnt = 0;
	gSBTask.ubState =  gSBTask.ubPrevState;
	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_CLEAR_RR_OFFSET_SET_FEATURE:
		gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SET_DEFAULT_CORRECTIVE_READ_SETTING;
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SETTING_CORRECTIVE_READ_SET_FEATURE;
		break;
	}

}

void RetrySBClearRROffsetFail_Callback(U8 ubMTIndex)
{
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
	gSBTask.ubState =  gSBTask.ubPrevState;
	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_CLEAR_RR_OFFSET_SET_FEATURE:
		RetrySBMTTriggerRetry(FALSE);
		break;
	}
}

void RetrySBHBSetFeatureCRFail_Callback(U8 ubMTIndex)
{
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
	gSBTask.ubState =  gSBTask.ubPrevState;
	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_SETTING_CORRECTIVE_READ_SET_FEATURE:
	case RETRY_SB_3RD_STATE_SETTING_CORRECTIVE_READ_GET_FEATURE:
		RetrySBMTTriggerRetry(FALSE);
		break;

	case RETRY_SB_3RD_STATE_SETTING_CORRECTIVE_READ_CHECK_FEATURE:
		if (gSBTask.ubMTCheckFeatureFailCnt < RETRY_SB_MT_FAIL_RETRY_CNT_MAX) {
			gSBTask.ubMTCheckFeatureFailCnt++;
			gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SETTING_CORRECTIVE_READ_SET_FEATURE;
		}
		else {
			gSBTask.ubMTCheckFeatureFailCnt = 0;
			gpVTDBUF->Retry.ulSetFeatureFailCnt++;
#if (!RELEASED_FW)
			DebugError();
#endif
			//If Set Get CHK feature fail, try to set to default feature, then Reset FLH
			RetrySBSwitchStateToSetDefaultFeatureReset();
		}
		break;
	}
}

void RetrySBHBSetFeatureCRPass_Callback(U8 ubMTIndex)
{
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
	gSBTask.ubMTFailCnt = 0;
	gSBTask.ubState =  gSBTask.ubPrevState;
	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_SETTING_CORRECTIVE_READ_SET_FEATURE:
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SETTING_CORRECTIVE_READ_GET_FEATURE;
		break;
	case RETRY_SB_3RD_STATE_SETTING_CORRECTIVE_READ_GET_FEATURE:
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SETTING_CORRECTIVE_READ_CHECK_FEATURE;
		break;
	case RETRY_SB_3RD_STATE_SETTING_CORRECTIVE_READ_CHECK_FEATURE:
		switch (gSBTask.ubSubstate) {
		case RETRY_SB_SUBSTATE_SB_READ_SET_CR_2B_4B_ENHANCE_CR:
			if (gSBTask.ubCurrentStage == RETRY_SB_MICRON_STAGE_6) {
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_READ_HB;
				gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_HB_READ_INIT;
			}
			else {
				RetrySBUpdateStageStep();
				RetrySBSwitchStatebyStageStep(TRUE, gSBTask.ubCurrentStage, gSBTask.ubCurrentStep);
			}
			break;
		case RETRY_SB_SUBSTATE_HB_READ_SET_CR_2B_4B_ENHANCE_CR:
			if ((RETRY_SB_MICRON_STEP_MIN1SW_PRFIX_RR_FINE_CALIB_PERSISTENT_FINE_CALIB == gSBTask.ubCurrentStep) || (RETRY_SB_MICRON_STEP_MIN2SW_PRFIX_RR_FINE_CALIB_PERSISTENT_FINE_CALIB == gSBTask.ubCurrentStep) || (RETRY_SB_MICRON_STEP_MIN3SW_PRFIX_RR_FINE_CALIB_PERSISTENT_FINE_CALIB == gSBTask.ubCurrentStep)) {
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_4K_FRAME_READ_OP_ONLY;
				gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_NULL;
			}
			else {
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_4K_FRAME_READ_OP_DMA_DECODE;
				gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_NULL;
			}
			break;
		case RETRY_SB_SUBSTATE_SET_DEFAULT_CORRECTIVE_READ_SETTING:
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_SB_READ_OFFSET;
			gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SB_CLEAR_SB_OFFSET_INIT;
			break;
		case RETRY_SB_SUBSTATE_HB_RAW_READ_SET_4b_CR_MIN1SW_READ_OFFSET:
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_RAW_READ_SET_7TH_ADDRESS;
			break;
		}
		break;
	}

}


void RetrySBHBSetFeatureARCFail_Callback(U8 ubMTIndex)
{
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
	gSBTask.ubState =  gSBTask.ubPrevState;
	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_SET_FEATURE:
	case RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_GET_FEATURE:
		RetrySBMTTriggerRetry(FALSE);
		break;

	case RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_CHECK_FEATURE:
		if (gSBTask.ubMTCheckFeatureFailCnt < RETRY_SB_MT_FAIL_RETRY_CNT_MAX) {
			gSBTask.ubMTCheckFeatureFailCnt++;
			gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_SET_FEATURE;
		}
		else {
			gSBTask.ubMTCheckFeatureFailCnt = 0;
			gpVTDBUF->Retry.ulSetFeatureFailCnt++;
#if (!RELEASED_FW)
			DebugError();
#endif
			//If Set Get CHK feature fail, try to set to default feature, then Reset FLH
			RetrySBSwitchStateToSetDefaultFeatureReset();
		}
		break;
	}
}

void RetrySBHBSetFeatureARCPass_Callback(U8 ubMTIndex)
{
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
	gSBTask.ubMTFailCnt = 0;
	gSBTask.ubState =  gSBTask.ubPrevState;
	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_SET_FEATURE:
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_GET_FEATURE;
		break;
	case RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_GET_FEATURE:
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_CHECK_FEATURE;
		break;
	case RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_CHECK_FEATURE:
		switch (gSBTask.ubSubstate) {
		case RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_SET_ARC_CALIBRATION_PERSISTENCE:
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_HB;
			gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_HB_READ_INIT;
			break;
		case RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_ENABLE_AUTO_READ_CALIBRATION:
			gSBTask.ubSubstate =  RETRY_SB_SUBSTATE_HB_RAW_READ;
			gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_HB_READ_INIT;
			break;
		case RETRY_SB_SUBSTATE_SB_READ_SET_ARC_CALIBRATION_PERSISTENCE:
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_READ_HB;
			gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_HB_READ_INIT;
			break;
		case RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_ARC:					//To next Substate
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_SB_RR_OFFSET;
			gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_CLEAR_RR_OFFSET_SET_FEATURE;
			break;
		}
		break;
	}

}

void RetrySBSwitchStatebyStageStep(U8 ubNeedCleanPreviousStageStepSettings, U8 ubStageNeedtoClean, U8 ubStepNeedtoClean )
{
	if (M_RETRY_SB_CHECK_IF_SLC_MLC_PAGETYPES()) {

		if (RETRY_SB_MICRON_SLC_MLC_STEP_NUM == gSBTask.ubCurrentStep) {
			gSBTask.ubState = RETRY_SB_STATE_TERMINATE_FAIL;
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_NULL;
		}
		else {
			switch (gSBTask.ubCurrentStage) {
			case RETRY_SB_MICRON_STAGE_3:
				gSBTask.ubState = RETRY_SB_STATE_HB_2K_RAW_READ_DECODE;
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_RAW_READ_INIT;
				break;
			case RETRY_SB_MICRON_STAGE_4:
				if (RETRY_SB_MICRON_SLC_MLC_STEP_NUM == gSBTask.ubCurrentStep) {
					gSBTask.ubState = RETRY_SB_STATE_TERMINATE_FAIL;
					gSBTask.ubSubstate = RETRY_SB_SUBSTATE_NULL;
				}
				else {
					gSBTask.ubState = RETRY_SB_STATE_SB_READ;
					gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_READ_INIT;
				}
				break;
			default:
				M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
				break;
			}
			if (TRUE == ubNeedCleanPreviousStageStepSettings) {
				//Backup Stage/Step and Set PostCondition to Clean Setting of Previous Stage/Step
				RetrySBSwitchPostConditionThenDoBackupSBFlow(gSBTask.ubState, gSBTask.ubSubstate, ubStageNeedtoClean, ubStepNeedtoClean);
			}
		}
	}
	else {
		if (RETRY_SB_MICRON_STEP_NUM == gSBTask.ubCurrentStep) {
			gSBTask.ubState = RETRY_SB_STATE_TERMINATE_FAIL;
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_NULL;
		}
		else {
			switch (gSBTask.ubCurrentStage) {
			case RETRY_SB_MICRON_STAGE_1:
				switch (gSBTask.ubCurrentStep) {
				case RETRY_SB_MICRON_STEP_NORMAL_READ: // Should not be this case
				case RETRY_SB_MICRON_STEP_PERSISTENT_FINE_CALIB:
					gSBTask.ubState = RETRY_SB_STATE_HB_4K_READ_MT_DECODE;
					gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_4K_FRAME_READ_INIT;
					break;
				case RETRY_SB_MICRON_STEP_EXPRESS_SBSBR_1_2:
					gSBTask.ubState = RETRY_SB_STATE_HB_2K_RAW_READ_DECODE;
					gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_RAW_READ_INIT;
					break;
				default:
					break;
				}
				break;
			case RETRY_SB_MICRON_STAGE_2:
				switch (gSBTask.ubCurrentStep) {
				case RETRY_SB_MICRON_STEP_CURCBC_P1_FINE_CALIB:
				case RETRY_SB_MICRON_STEP_CURCBC_N1_FINE_CALIB:
				case RETRY_SB_MICRON_STEP_CURCBC_P2_FINE_CALIB:
				case RETRY_SB_MICRON_STEP_CURCBC_N2_FINE_CALIB:
				case RETRY_SB_MICRON_STEP_CURCBC_P3_FINE_CALIB:
				case RETRY_SB_MICRON_STEP_EXPRESS_SBSBR_2_2:
				case RETRY_SB_MICRON_STEP_EXPRESS_SBSBR_2_4:
					gSBTask.ubState = RETRY_SB_STATE_HB_2K_RAW_READ_DECODE;
					gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_RAW_READ_INIT;
					break;
				default:
					break;
				}
				break;
			case RETRY_SB_MICRON_STAGE_4:
				gSBTask.ubState = RETRY_SB_STATE_HB_4K_READ_MT_DECODE;
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_4K_FRAME_READ_INIT;
				break;
			case RETRY_SB_MICRON_STAGE_3:
				gSBTask.ubState = RETRY_SB_STATE_CR_TRIM_ENHANCE_TRIM;
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_READ_SET_CR_2B_4B_ENHANCE_CR;
				gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SETTING_CORRECTIVE_READ_SET_FEATURE;
				break;
			case RETRY_SB_MICRON_STAGE_5:
				if (gSBTask.ubCurrentStep == RETRY_SB_MICRON_STEP_RESOTRE_POR_TRIM) {
					gSBTask.ubState = RETRY_SB_STATE_RESTORE_POR_TRIM;
				}
				else {
					gSBTask.ubState = RETRY_SB_STATE_HB_4K_READ_MT_DECODE;
					gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_4K_FRAME_READ_INIT;
				}
				break;
			case RETRY_SB_MICRON_STAGE_6:
				switch (gSBTask.ubCurrentStep) {
				case RETRY_SB_MICRON_STEP_MIN1SW_PRFIX_RR_CR_4b:
					gSBTask.ubState = RETRY_SB_STATE_HB_2K_RAW_READ_DECODE;
					gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_RAW_READ_INIT;
					break;
				case RETRY_SB_MICRON_STEP_MIN1SW_PRFIX_RR_SBSBR1_CR_2b:
				case RETRY_SB_MICRON_STEP_MIN1SW_PRFIX_RR_SBSBR1_CR_4b:
				case RETRY_SB_MICRON_STEP_MIN1SW_PRFIX_RR_SBSBR2_CR_4b:
					gSBTask.ubState = RETRY_SB_STATE_SB_READ;
					gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_READ_INIT;
					break;
				default:
					break;
				}
				break;
			default:
				M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
				break;
			}
			if (TRUE == ubNeedCleanPreviousStageStepSettings) {
				//Backup Stage/Step and Set PostCondition to Clean Setting of Previous Stage/Step
				RetrySBSwitchPostConditionThenDoBackupSBFlow(gSBTask.ubState, gSBTask.ubSubstate, ubStageNeedtoClean, ubStepNeedtoClean);
			}
		}
	}
}
void RetrySBTriggerCorrectFail_Callback(U8 ubMTIndex)
{
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
	gSBTask.ubState = gSBTask.ubPrevState;
	switch (gSBTask.ubSubstate) {
	//Micron SB Todo
	case RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_HB_CORR_2K:
		gSBTask.ubSubstate =  RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_HB_CORR_2K;
		break;
	case RETRY_SB_SUBSTATE_SB_DECODE_WITH_DEFAULT_LLR_SB_CORR:
		gSBTask.ubSubstate =  RETRY_SB_SUBSTATE_SB_DECODE_WITH_DEFAULT_LLR_SB_CORR;
		break;
	case RETRY_SB_SUBSTATE_HB_RAW_READ_SBC_HB_CORR_2K:
		gSBTask.ubSubstate =  RETRY_SB_SUBSTATE_HB_RAW_READ_SBC_HB_CORR_2K;
		break;
	default:
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
		break;
	}
}

void RetrySBTriggerCorrectPass_Callback(U8 ubMTIndex)
{

	U16 uwFailCnt;

	U8 ubTempState = 0;
#if (RETRY_SB_ORIGIN_EN)
	U8 ubIdx;
	U8 ubllr_indexTemp = 0;
#endif /*(RETRY_SB_ORIGIN_EN)*/

	U8 ubTempStage = 0;
	U8 ubTempStep = 0;

#if (ERROR_HANDLE_VALIDATION)
	U32 ulTotalReadOffset = 0;
	U32 ulReadOffsetWith4Params = 0;
#endif /* (ERROR_HANDLE_VALIDATION) */

	ubTempState = gSBTask.ubSubstate;

	ubTempStage = gSBTask.ubCurrentStage;
	ubTempStep = gSBTask.ubCurrentStep;

	gSBTask.ubMTFailCnt = 0;
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
	gSBTask.ubState =  gSBTask.ubPrevState;
	///while ((R32_FCON[R32_FCON_LDPC_SBC] & ((BIT0 << ( gSBTask.ubChannel + SBC_FINISH_SHIFT)) | SBC_BSY_MASK)) != (BIT0 << ( gSBTask.ubChannel + SBC_FINISH_SHIFT)));
	while ((R32_FCON[R32_FCON_LDPC_SBC] & ((SBC_BSY_MASK) << SBC_BSY_SHIFT))  );

	//Debug Info
	gSBTask.ubTerminateState = gSBTask.ubState;
	gSBTask.ubTerminateSubstate = gSBTask.ubSubstate;

	switch (gSBTask.ubSubstate) {
#if (RETRY_SB_ORIGIN_EN)
	case RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_HB_CORR_2K:
		gSBTask.ubState =  RETRY_SB_STATE_HB_WITH_AUTO_READ_CALIBRATION;
		gSBTask.ubSubstate =  RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_HB_CORR_2K;
		gSBTask.ubErrorRecoverySBStep = RETRY_SB_MICRON_ERROR_RECOVERY_READ_RETRY_ARC_STEP + gSBTask.ubARCRoundCnt;
		break;

	case RETRY_SB_SUBSTATE_SB_DECODE_WITH_DEFAULT_LLR_SB_CORR:
		gSBTask.ubState =  RETRY_SB_STATE_SB_DECODE_WITH_DEFAULT_LLR;
		gSBTask.ubSubstate =  RETRY_SB_SUBSTATE_SB_DECODE_WITH_DEFAULT_LLR_SB_CORR;
		gSBTask.ubErrorRecoverySBStep = RETRY_SB_MICRON_ERROR_RECOVERY_READ_RETRY_ARC_SB_STEP;
		if (NCS_EN && gubNeedSendNCSFlag) {
			ubllr_indexTemp = gSBTask.ubllr_index;
		}
		break;
#endif /*(RETRY_SB_ORIGIN_EN)*/
	case RETRY_SB_SUBSTATE_HB_RAW_READ_SBC_HB_CORR_2K:
		gSBTask.ubState =  RETRY_SB_STATE_HB_2K_RAW_READ_DECODE;
		gSBTask.ubSubstate =  RETRY_SB_SUBSTATE_HB_RAW_READ_SBC_HB_CORR_2K;
		gSBTask.ubErrorRecoverySBStep = gSBTask.ubCurrentStep;
		break;

	case RETRY_SB_SUBSTATE_SB_DECODE_SB_CORR:
		gSBTask.ubState =  RETRY_SB_STATE_SB_DECODE;
		gSBTask.ubSubstate =  RETRY_SB_SUBSTATE_SB_DECODE_SB_CORR;
		gSBTask.ubErrorRecoverySBStep = gSBTask.ubCurrentStep;
		break;

	default:
		break;
	}



	uwFailCnt = ((R32_FCON[R32_FCON_LDPC_SBC] & SBC_BFC_CNT_MASK) >> SBC_BFC_CNT_SHIFT);
#if (RETRY_TURBORAIN_DEBUG)
	if (((gpRetry->ubIsQLCPageFlag && (gpRetry->ubGenNormalFailCnt > GEN_NORMAL_FAIL_MAX_CNT)) || (gpRetry->ubIsQLCPageFlag == FALSE)) && (R32_FCON[R32_FCON_LDPC_SBC] & SBC_SUCCEED_BIT))
#else /*RETRY_TURBORAIN_DEBUG*/
	if (R32_FCON[R32_FCON_LDPC_SBC] & SBC_SUCCEED_BIT)
#endif
	{
		if (SB_DUMP_DATA_DEBUG_EN) {

		}
		R32_FCON[R32_FCON_LDPC_SBC] = 0;
		//UartPrintf("\nSB Pass QLC : %d, FailCnt : %d ", gpRetry->ubIsQLCPageFlag, gpRetry->ubGenNormalFailCnt);
		// backup correctable 2k
		RetrySBBackupCorrect2k(3, gSBTask.ubCurrentLDPCFrameIdx,            \
			gSBTask.ulbackup_addr, gSBTask.ultmp_addr, 				\
			gSBTask.ulSpareAddr, gSBTask.ultmp_SpareAddr, RETRY_SB_BACKUP_RESTORE_TO_DRAMIRAM);

		gSBTask.ubState = RETRY_SB_STATE_TERMINATE_PASS;
		RetrySBSwitchPostConditionThenDoBackupSBFlow(gSBTask.ubState, gSBTask.ubSubstate, ubTempStage, ubTempStep);
#if (ERROR_HANDLE_VALIDATION)
		M_UART(ERROR_HANDLE_VALIDATION_, "\n[EH] SB Pass Step:%d\n", ubTempStep);
#endif /* (ERROR_HANDLE_VALIDATION) */
	}
	else {
#if (ERROR_HANDLE_VALIDATION)
		if (gSBTask.ubCurrentStep == RETRY_SB_MICRON_STEP_MIN1SW_PRFIX_RR_CR_4b) {
			ulReadOffsetWith4Params = (U32)(FIPValleyTrackReadOut(gSBTask.ulFSA_ori, FIP_VALLEY_TRACK_READOUT_MODE_READ_OFFSET));

			ulTotalReadOffset = RetrySBGetTotalReadOffset(ubTempStage, ubTempStep, ulReadOffsetWith4Params);
			M_UART(ERROR_HANDLE_VALIDATION_, "\n[EH] (%d) min SW: %d, Readoffset: %x", gSBTask.ubCurrentStep, gSBMinSWInfo.uwMinSWValue[0], ulTotalReadOffset);
		}
#endif /* (ERROR_HANDLE_VALIDATION) */

		RetrySBUpdateStageStep();
		//UartPrintf("\nSB Fail QLC : %d, FailCnt : %d ", gpRetry->ubIsQLCPageFlag, gpRetry->ubGenNormalFailCnt);
#if (RETRY_SB_ORIGIN_EN)
		switch (gSBTask.ubSubstate) {
		case RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_HB_CORR_2K:
#if RETRY_SOFTBITT_FOR_SDK_EN
			gSBTask.ubState = RETRY_SB_STATE_HB_WITH_AUTO_READ_CALIBRATION;
			gSBTask.ubSubstate =  RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_INIT;
#else /* RETRY_SOFTBITT_FOR_SDK_EN */
			gSBTask.ubTrappingSetCnt = 0;
			if ((SB_ARC_MAX_ROUND_CNT - 1) == gSBTask.ubARCRoundCnt) {
				gSBTask.ubARCRoundCnt = 0;
				gSBTask.ubState = RETRY_SB_STATE_SB_READ_WITH_READ_RETRY_TABLE_WITH_ARC;
				gSBTask.ubSubstate =  RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_INIT;
			}
			else {
				gSBTask.ubARCRoundCnt++;
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_ENABLE_AUTO_READ_CALIBRATION;
				gSBTask.ubThirdstate =  RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_SET_FEATURE;
			}
#endif /* RETRY_HARDBIT_FOR_SDK_EN */
			if (SB_DUMP_DATA_DEBUG_EN) {
				RetrySBBackupCorrect2k(3, gSBTask.ubCurrentLDPCFrameIdx,            \
					gSBTask.ulbackup_addr, (U32)(gulVUCAddr),
					gSBTask.ulSpareAddr, gSBTask.ultmp_SpareAddr, RETRY_SB_BACKUP_RESTORE_TO_ONLYDRAM_AND_KEEP);
			}
			break;

		case RETRY_SB_SUBSTATE_SB_DECODE_WITH_DEFAULT_LLR_SB_CORR:
			//gSBTask.ubState = RETRY_SB_STATE_LOAD_DEFAULT_LLR_TABLE;
			//gSBTask.ubSubstate =  RETRY_SB_SUBSTATE_NULL;
			//gSBTask.ubllr_index++;
			gSBTask.ubState = RETRY_SB_STATE_TERMINATE_FAIL;
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_NULL;
			if (NCS_EN && gubNeedSendNCSFlag) {
				for (ubIdx = 0 ; ubIdx < 3 ; ubIdx++) {
					RetrySBBackupCorrect2k(ubIdx, gSBTask.ubCurrentLDPCFrameIdx,            \
						gSBTask.ulbackup_addr, (U32)(gulVUCAddr +  BC_8KB + (BC_4KB + BC_1KB)*ubIdx),
						gSBTask.ulSpareAddr, gSBTask.ultmp_SpareAddr, RETRY_SB_BACKUP_RESTORE_TO_ONLYDRAM_AND_KEEP);
					if (RETRY_SB_ENABLE_DEBUG_MESSAGE) {
						//UartPrintf("\n F_VUCAddr %x", (U32)(gulVUCAddr +  BC_8KB + (BC_4KB + BC_1KB)*ubIdx));
					}
				}
			}
			break;
		default:
			break;
		}
#endif /*(RETRY_SB_ORIGIN_EN)*/


		RetrySBSwitchStatebyStageStep(TRUE, ubTempStage, ubTempStep);

	}
}


void RetrySBReadHB4KDMAPass_Callback(U8 ubMTIdx)
{
	U8 ubTempState = 0;
	U8 ubTempStage = 0;
	U8 ubTempStep = 0;

	FlhMT_t *pMT = (FlhMT_t *)M_MT_ADDR(ubMTIdx);
	U8 ubFrameIdx = 0;
	U32 ulDMADecodeResultDataAddr = 0;
	U32 ulRetryResultDataBy4KAddr = 0;
	U32 ulTempIRAMAddr = 0;
	U32 ulRetryResultSpareBy4KIRAMAddr = 0;
	DMACParam_t DMACParam;

	ubTempState = gSBTask.ubSubstate;
	ubTempStage = gSBTask.ubCurrentStage;
	ubTempStep = gSBTask.ubCurrentStep;

	gSBTask.ubMTFailCnt = 0;
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
	gSBTask.ubState =  gSBTask.ubPrevState;

	//Debug Info
	gSBTask.ubTerminateState = gSBTask.ubState;
	gSBTask.ubTerminateSubstate = gSBTask.ubSubstate;

	switch (gSBTask.ubSubstate) {
	case RETRY_SB_SUBSTATE_HB_4K_FRAME_READ_OP_DMA_DECODE:
		gSBTask.ubState =  RETRY_SB_STATE_HB_4K_READ_MT_DECODE;
		gSBTask.ubSubstate =  RETRY_SB_SUBSTATE_HB_4K_FRAME_READ_OP_DMA_DECODE;

	default:
		break;
	}

	gSBTask.ubErrorRecoverySBStep = gSBTask.ubCurrentStep;

	//TODO N48R Decoding Flow 0707

	/*Copy Read DMA Decode Result to Retry Buffer / Retry GCBackup IRAM*/
	//Should DMA only 1 4K_Frame
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (1 == pMT->dma.FrameNum));
	ubFrameIdx = (pMT->dma.uliFSA0_1 & gpOtherInfo->pRuleSet->pEntry->ulMask);
	// DMA from Retry 2nd to RetryBuffer(1st) of the Current 4K_Frame
	ulDMADecodeResultDataAddr = gSBTask.ultmp_addr;						// Retry 2nd Buffer
	ulRetryResultDataBy4KAddr = gSBTask.ulbackup_addr;						// Retry Buffer
#if (PS5013_EN)
	ulTempIRAMAddr = gSBTask.ultmp_SpareAddr + ubFrameIdx * L4K_SIZE;		//by Using btGC, BackupP4K, 	(IRAM_BASE + TEMP_SPR_FOR_SB_RS_OFF + 64)
#elif (PS5017_EN)
	ulTempIRAMAddr = IRAM_BASE + GC_BACKUP_OFF + (ERR_HDL_MAX_L4K_PER_PAGE * SPARE_SIZE * ubMTIdx);
#endif
	ulRetryResultSpareBy4KIRAMAddr = gSBTask.ulSpareAddr;					// IRAM_BASE + GC_BACKUP_RETRY_OFF + ubFrameIndex * L4K_SIZE

	/* copy first/second 2k data  by gSBTask.ubCurrentLDPCFrameIdx*/
	DMACParam.ulSourceAddr = (ulDMADecodeResultDataAddr + gSBTask.ubCurrentLDPCFrameIdx * BC_2KB);
	DMACParam.ulDestAddr = (ulRetryResultDataBy4KAddr + gSBTask.ubCurrentLDPCFrameIdx * BC_2KB);
	DMACParam.ul32ByteNum = SIZE_IN_32B(BC_2KB);
	gpRetry->ubWaitDMACDoneFlag = TRUE;

	DMACCopy(DMAC_MODE_NORMAL_QUEUE, &DMACParam, gulReadRetryDMACDone_CallBack, 0);
	while (gpRetry->ubWaitDMACDoneFlag == TRUE) {
		DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
	}
	/* copy first/second spare data by gSBTask.ubCurrentLDPCFrameIdx */
	memcpy((U32 *)(ulRetryResultSpareBy4KIRAMAddr + gSBTask.ubCurrentLDPCFrameIdx * BC_8B ), (U32 *)(ulTempIRAMAddr + gSBTask.ubCurrentLDPCFrameIdx * BC_8B), BC_8B);

	gSBTask.ubState = RETRY_SB_STATE_TERMINATE_PASS;
#if (ERROR_HANDLE_VALIDATION)
	M_UART(ERROR_HANDLE_VALIDATION_, "\n[EH] SB Pass Step:%d\n", ubTempStep);
#endif /* (ERROR_HANDLE_VALIDATION) */

	RetrySBSwitchPostConditionThenDoBackupSBFlow(gSBTask.ubState, gSBTask.ubSubstate, ubTempStage, ubTempStep);

}


void RetrySBReadHB4KDMAFail_Callback(U8 ubMTIdx)
{
	U8 ubTempState = 0;


	U8 ubTempStage = 0;
	U8 ubTempStep = 0;

	U8 ubErrorP4KBMP = 0;
	U8 ub1stErrFrameIdx = 0;

	U8 uwtotalSW = 0;
	U8 ubSWNum = 0;
	U8 ubReplaceIdx = 0;

	U32 ulReadOffsetWith4Params = 0;

	U8 ubNeedCleanPreviousStageStepSettings = TRUE;
#if (ERROR_HANDLE_VALIDATION)
	U32 ulTotalReadOffset = 0;
#endif /* (ERROR_HANDLE_VALIDATION) */
	ubTempState = gSBTask.ubSubstate;
	ubTempStage = gSBTask.ubCurrentStage;
	ubTempStep = gSBTask.ubCurrentStep;

	gSBTask.ubMTFailCnt = 0;
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
	gSBTask.ubState =  gSBTask.ubPrevState;

	//Debug Info
	gSBTask.ubTerminateState = gSBTask.ubState;
	gSBTask.ubTerminateSubstate = gSBTask.ubSubstate;

	switch (gSBTask.ubSubstate) {
	case RETRY_SB_SUBSTATE_HB_4K_FRAME_READ_OP_DMA_DECODE:
		gSBTask.ubState =  RETRY_SB_STATE_HB_4K_READ_MT_DECODE;
		gSBTask.ubSubstate =  RETRY_SB_SUBSTATE_HB_4K_FRAME_READ_OP_DMA_DECODE;

	default:
		break;
	}

	//Find Error FrameIdx of this DMA Decode MT
	ubErrorP4KBMP = FIPCalculateP4KErrorFrameMap(ubMTIdx);

	for (ub1stErrFrameIdx = 0 ; ub1stErrFrameIdx < HB_MAX_4K_FRAME_NUM; ub1stErrFrameIdx++) {
		if (ubErrorP4KBMP & BIT(ub1stErrFrameIdx)) {
			break;
		}
	}

	//TODO N48R Decoding Flow 0707
	// What if Erase Page Interrupt Case?


	// Get SW and Recode Read Offset
	// Calculate SW

	//Only do HB Read & Decode 1 Frame(4K)

	if (INIT_PTYCHSUM_NUM != gMTMgr.uwPTYCHSUM[ub1stErrFrameIdx]) {
		uwtotalSW += gMTMgr.uwPTYCHSUM[ub1stErrFrameIdx];
	}

	switch (ubTempStage) {
	case RETRY_SB_MICRON_STAGE_1:
	case RETRY_SB_MICRON_STAGE_4:
		ulReadOffsetWith4Params = (U32)(FIPValleyTrackReadOut(gSBTask.ulFSA_ori, FIP_VALLEY_TRACK_READOUT_MODE_READ_OFFSET));
		for (ubSWNum = 0; ubSWNum < RETRY_SB_FLOW_HB_MICRON_MIN_SW_NUM ; ubSWNum++) {
			if (INVALID_RETRY_SB_STEP_INDEX == gSBMinSWInfo.ubMinSWStep[ubSWNum]) {
				// Update new value to array
				gSBMinSWInfo.ubMinSWStep[ubSWNum] = ubTempStep;
				gSBMinSWInfo.uwMinSWValue[ubSWNum] = uwtotalSW;
				gSBMinSWInfo.SWReadOffset[ubSWNum].ReadOffset4Param.ulAll = ulReadOffsetWith4Params;
				break;
			}
			else {
				// Sorting the Minimun_SW is Min_SWs is at Index_0
				// If current total SW < Min_SW in Table, replace
				if (gSBMinSWInfo.uwMinSWValue[ubSWNum] > uwtotalSW) {
					for (ubReplaceIdx = RETRY_SB_FLOW_HB_MICRON_MIN_SW_NUM - 1; ubReplaceIdx > ubSWNum; ubReplaceIdx--) {
						gSBMinSWInfo.uwMinSWValue[ubReplaceIdx] = gSBMinSWInfo.uwMinSWValue[ubReplaceIdx - 1];
						gSBMinSWInfo.ubMinSWStep[ubReplaceIdx] = gSBMinSWInfo.ubMinSWStep[ubReplaceIdx - 1];
						gSBMinSWInfo.SWReadOffset[ubReplaceIdx] = gSBMinSWInfo.SWReadOffset[ubReplaceIdx - 1];
					}
					gSBMinSWInfo.uwMinSWValue[ubSWNum] = uwtotalSW;
					gSBMinSWInfo.ubMinSWStep[ubSWNum] = ubTempStep;
					gSBMinSWInfo.SWReadOffset[ubSWNum].ReadOffset4Param.ulAll = ulReadOffsetWith4Params;
					break;
				}
			}
		}
#if (ERROR_HANDLE_VALIDATION)
		ulTotalReadOffset = RetrySBGetTotalReadOffset(ubTempStage, ubTempStep, ulReadOffsetWith4Params);
		M_UART(ERROR_HANDLE_VALIDATION_, "\n[EH] (%d) min SW: %d, current SW: %d, Readoffset: %x", gSBTask.ubCurrentStep, gSBMinSWInfo.uwMinSWValue[0], uwtotalSW, ulTotalReadOffset);
#endif /* ERROR_HANDLE_VALIDATION */
		break;
	case RETRY_SB_MICRON_STAGE_5:

		switch (ubTempStep) {
		case RETRY_SB_MICRON_STEP_MIN1SW_PRFIX_RR_FINE_CALIB_PERSISTENT_FINE_CALIB:
		case RETRY_SB_MICRON_STEP_MIN2SW_PRFIX_RR_FINE_CALIB_PERSISTENT_FINE_CALIB:
		case RETRY_SB_MICRON_STEP_MIN3SW_PRFIX_RR_FINE_CALIB_PERSISTENT_FINE_CALIB:
			ulReadOffsetWith4Params = (U32)(FIPValleyTrackReadOut(gSBTask.ulFSA_ori, FIP_VALLEY_TRACK_READOUT_MODE_READ_OFFSET));
			//Update Record Read Offset from ValleyTrack when each time in 5.Xa Step
			gRetrySBFineCalibReadOffset.ReadOffset4Param.ulAll = ulReadOffsetWith4Params;
#if (ERROR_HANDLE_VALIDATION)
			ulTotalReadOffset = RetrySBGetTotalReadOffset(ubTempStage, ubTempStep, ulReadOffsetWith4Params);
			M_UART(ERROR_HANDLE_VALIDATION_, "\n[EH] (%d) min SW: %d, current SW: %d, Readoffset: %x", gSBTask.ubCurrentStep,  gSBMinSWInfo.uwMinSWValue[0], uwtotalSW, ulTotalReadOffset);
#endif /* (ERROR_HANDLE_VALIDATION) */

			//For Step 5.Xa -> 5.Xb, No need to Clean Settings
			ubNeedCleanPreviousStageStepSettings = FALSE;
			break;
		case RETRY_SB_MICRON_STEP_MIN1SW_PRFIX_RR_CALIBRATED_OFFSET_CR_2B:
		case RETRY_SB_MICRON_STEP_MIN2SW_PRFIX_RR_CALIBRATED_OFFSET_CR_2B:
		case RETRY_SB_MICRON_STEP_MIN3SW_PRFIX_RR_CALIBRATED_OFFSET_CR_2B:
			//Record SW & Read Offset when SW is Minimum
			if ((INVALID_RETRY_SB_SW_VALUE == gSBMinSWInfo.uwMinSWValueForStage5_6) || (gSBMinSWInfo.uwMinSWValueForStage5_6 > uwtotalSW)) {
				// Update new value to array, when empty
				// If current SW < Record SW, replace
				gSBMinSWInfo.ubMinSWIdxForStage5_6 = gSBMinSWInfo.ubCurrentUsingMinSWIdx;
				gSBMinSWInfo.uwMinSWValueForStage5_6 = uwtotalSW;
				gSBMinSWInfo.MinSWReadOffset.ReadOffset4Param.ulAll = gRetrySBFineCalibReadOffset.ReadOffset4Param.ulAll;
			}
#if (ERROR_HANDLE_VALIDATION)
			M_UART(ERROR_HANDLE_VALIDATION_, "\n[EH] (%d) min SW: %d, current SW: %d, stage5_6 SW: %d", gSBTask.ubCurrentStep, gSBMinSWInfo.uwMinSWValue[0], uwtotalSW, gSBMinSWInfo.uwMinSWValueForStage5_6);
#endif /* (ERROR_HANDLE_VALIDATION) */
			break;
		default:
			break;
		}
		break;
	default:
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
		break;
	}

	RetrySBUpdateStageStep();

	RetrySBSwitchStatebyStageStep(ubNeedCleanPreviousStageStepSettings, ubTempStage, ubTempStep);

}


void RetrySBHBReadCMDOnlyPass_Callback(U8 ubMTIdx)
{
	gSBTask.ubMTFailCnt = 0;
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
	gSBTask.ubState =  gSBTask.ubPrevState;

	gSBTask.ubSubstate =	RETRY_SB_SUBSTATE_HB_4K_FRAME_READ_OP_DMA_DECODE;
}


void RetrySBSetReadRetryDeltaFail_Callback(U8 ubMTIndex)
{
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
	gSBTask.ubState =  gSBTask.ubPrevState;

	switch (gSBTask.ubFourthstate) {

	case RETRY_SB_4TH_STATE_SB_READ_LEVEL_BY_DELTA_TABLE_SET_FEATURE:
	case RETRY_SB_4TH_STATE_SB_READ_LEVEL_BY_DELTA_TABLE_GET_FEATURE:
		RetrySBMTTriggerRetry(TRUE);
		break;
	case RETRY_SB_4TH_STATE_SB_READ_LEVEL_BY_DELTA_TABLE_CHECK_FEATURE:
		if (gSBTask.ubMTCheckFeatureFailCnt < RETRY_SB_MT_FAIL_RETRY_CNT_MAX) {
			gSBTask.ubMTCheckFeatureFailCnt++;
			gSBTask.ubFourthstate = RETRY_SB_4TH_STATE_SB_READ_LEVEL_BY_DELTA_TABLE_SET_FEATURE;
		}
		else {
			gSBTask.ubMTCheckFeatureFailCnt = 0;
			gpVTDBUF->Retry.ulSetFeatureFailCnt++;
#if (!RELEASED_FW)
			DebugError();
#endif
			//If Set Get CHK feature fail, try to set to default feature, then Reset FLH
			RetrySBSwitchStateToSetDefaultFeatureReset();
			//Clear Flag for second FA
			gSBTask.ubCurrentFeatureAddr = SB_MICRON_NO_NEED_SET_SECOND_FEATURE_ADDRESS;
			gSBTask.ubTotalFeatureAddr = SB_MICRON_NO_NEED_SET_SECOND_FEATURE_ADDRESS;
		}
		break;
	}
}

void RetrySBSetReadRetryDeltaPass_Callback(U8 ubMTIndex)
{
	gSBTask.ubMTFailCnt = 0;
	gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
	gSBTask.ubState =  gSBTask.ubPrevState;

	switch (gSBTask.ubFourthstate) {
	case RETRY_SB_4TH_STATE_SB_READ_LEVEL_BY_DELTA_TABLE_SET_FEATURE:
		gSBTask.ubFourthstate = RETRY_SB_4TH_STATE_SB_READ_LEVEL_BY_DELTA_TABLE_GET_FEATURE;
		break;
	case RETRY_SB_4TH_STATE_SB_READ_LEVEL_BY_DELTA_TABLE_GET_FEATURE:
		gSBTask.ubFourthstate = RETRY_SB_4TH_STATE_SB_READ_LEVEL_BY_DELTA_TABLE_CHECK_FEATURE;
		break;
	case RETRY_SB_4TH_STATE_SB_READ_LEVEL_BY_DELTA_TABLE_CHECK_FEATURE:
		gSBTask.ubFourthstate = RETRY_SB_4TH_STATE_SB_READ_LEVEL_BY_DELTA_TABLE_SET_FEATURE;

		//SB MICRON Check Point 0730
		M_UART(ERROR_SOFTBIT_, "\n CB gSBTask.ubSecondFeatureAddr :%d", gSBTask.ubCurrentFeatureAddr);

		if ((gSBTask.ubTotalFeatureAddr - 1) != gSBTask.ubCurrentFeatureAddr) {
			gSBTask.ubCurrentFeatureAddr ++;
		}
		else {
			gSBTask.ubCurrentFeatureAddr = SB_MICRON_NO_NEED_SET_SECOND_FEATURE_ADDRESS;
			gSBTask.ubTotalFeatureAddr = SB_MICRON_NO_NEED_SET_SECOND_FEATURE_ADDRESS;
			switch (gSBTask.ubThirdstate) {
			//SB1
			case RETRY_SB_3RD_STATE_SB_READ_SB1_DELTA_SET_FEATURE:
				gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SB_READ_SB1_DELTA_READ;
				break;
			//SB2
			case RETRY_SB_3RD_STATE_SB_READ_SB2_DELTA_SET_FEATURE:
				gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SB_READ_SB2_DELTA_READ;
				break;
			//Clear SB offset
			case RETRY_SB_3RD_STATE_SB_CLEAR_SB_OFFSET_DELTA_SET_FEATURE:
				gSBTask.ubState = gSBTask.ubBackUpedState;
				gSBTask.ubSubstate = gSBTask.ubBackUpedSubstate;
				if ((RETRY_SB_STATE_SB_RESET_FLH == gSBTask.ubBackUpedState) || gSBTask.ubIsFromTurboRain) {		//after Set Default feature and next State is Reset flash, In Micron SB Support All PageType need to Reset WLBS before RESET_FLH
					gSBTask.ubState =  RETRY_SB_STATE_SB_RESET_FLH;
					gSBTask.ubBackUpedState = RETRY_SB_STATE_DONE;
				}
				break;
			}
		}
		break;
	}
}

void RetrySBReadHB( U8 page_type, U8 dsp_en, U32 ulFSA, U8 ldpc_frame_idx, U8 src_ibuf, U8 dst_ibuf, U8 ibuf_frame_sel)
{
	U16 *fpu_ptr = NULL;
	U8 ubMTIndex;
	U16 uwFpu_offset;
	U8 ubPrefixOffset;
	U8 ubCurrnetPrefixRRIdx;
	//U8 ubi = 0;

	/*RAW_LOGIC = 2 (NOP), src_ibuf and dst_ibuf are NOT allowed to assign the identical value, By FIP Spec*/
	if (src_ibuf == dst_ibuf) {
		src_ibuf = (dst_ibuf + 1) % 4;		// 4 for IBUF Number
	}

	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_HB_READ_INIT:
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_HB_READ_DIRECT_READ_OP;
		gSBTask.ubDsp_en = dsp_en;
	/* no break */
	case RETRY_SB_3RD_STATE_HB_READ_DIRECT_READ_OP:
		if (M_RETRY_SB_CHECK_IF_SLC_MLC_PAGETYPES()) {
			if (RETRY_SB_MICRON_SLC_PAGE == gSBTask.ubPageType) {
				uwFpu_offset = FPU_PTR_SLC_RETRY_READ_ACRR_CMD(0);
				ubPrefixOffset =  1;
			}
			else {
				uwFpu_offset = FPU_PTR_TLC_RETRY_READ_ACRR_CMD(0);
				ubPrefixOffset =  0;
			}
			if (RETRY_SB_MICRON_SLC_PAGE == gSBTask.ubPageType) {
				RetrySBSetFPUPrefixRR(gubSBMicronPreFixRRTable[0][gHBParamMgr.ubMinSWIdx[0]], 0, 0, 0);
			}
			else if (RETRY_SB_MICRON_MLC_LOWER_PAGE == gSBTask.ubPageType) { //MLC Lower A1
				RetrySBSetFPUPrefixRR(gubSBMicronPreFixRRTable[1][gHBParamMgr.ubMinSWIdx[0]], 0, 0, 0);
			}
			else if (RETRY_SB_MICRON_MLC_UPPER_PAGE == gSBTask.ubPageType) { //MLC Upper A0 & A2
				RetrySBSetFPUPrefixRR(gubSBMicronPreFixRRTable[2][gHBParamMgr.ubMinSWIdx[0]], gubSBMicronPreFixRRTable[3][gHBParamMgr.ubMinSWIdx[0]], 0, 0);
			}
		}
		else {
			uwFpu_offset = FPU_PTR_TLC_RETRY_READ_ACRR_CMD(gHBParamMgr.ubCurACRR);
			ubPrefixOffset = 0;
			if (gSBTask.ubCurrentStage == RETRY_SB_MICRON_STAGE_6) {
				ubCurrnetPrefixRRIdx = M_RETRY_SB_STEP_TO_PRFIX_RR_IDX_SKIP_RR0(gSBMinSWInfo.ubMinSWStep[gSBMinSWInfo.ubMinSWIdxForStage5_6]);
				RetrySBSetFPUPrefixRRWithPrefixRRTable(ubCurrnetPrefixRRIdx, gSBMinSWInfo.MinSWReadOffset);
			}
			else {
				RetrySBSetFPUPrefixRR(0, 0, 0, 0);
			}
		}
		fpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);
#if (PS5013_EN)
		fpu_ptr[8 + ubPrefixOffset] = VA_ADR_1B(gSBTask.ub7thAddr);
#endif

		ubMTIndex = RetrySBPrepareFreeMT(RetrySBReadHBPass_Callback, RetrySBReadHBFail_Callback);
		RetrySBSelecMTFPUTrigger( uwFpu_offset, ulFSA, 0, ubMTIndex, SWITCH_CLK__NORMAL_MODE);

		if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
			gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
		}
		else {
			gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
			gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
		}
		break;

	case RETRY_SB_3RD_STATE_HB_READ_DIRECT_READ_DMA:
		uwFpu_offset = FPU_PTR_OFFSET(fpu_raw_dma_read);
		fpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);
		fpu_ptr[7] =  FPU_DMA_R_RAW((ibuf_frame_sel << RAW_MODE_OFFSET) | (2 << RAW_LOGIC_OFFSET) | (src_ibuf << RAW_SRC_PTR_OFFSET)
				| (dst_ibuf << RAW_DST_PTR_OFFSET));

		ubMTIndex = RetrySBPrepareFreeMT(RetrySBReadHBPass_Callback, RetrySBReadHBFail_Callback);
		RetrySBSelectDMAMTFPUTrigger( uwFpu_offset, ulFSA, ubMTIndex);
		if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
			gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
		}
		else {
			gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
			gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
		}
		break;
	case RETRY_SB_3RD_HB_READ_DIRECT_READ_FOR_TURBO_RAIN:
		if (gSBTask.ubIsFromTurboRain) {
			RetrySBBackupRestore(gSBTask.ultmp_addr + BC_4KB * 2, gSBTask.ultmp_SpareAddr + 2 * BC_16B, 2, 1, 1, 0, 0, 0); //Restore into SB5
		}
		uwFpu_offset = FPU_PTR_OFFSET(fpu_raw_dma_read);
		fpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);
		// To be confirm
		fpu_ptr[7] =  FPU_DMA_R_RAW((1 << RAW_MODE_OFFSET) | (0 << RAW_LOGIC_OFFSET) | (2 << RAW_SRC_PTR_OFFSET)
				| (1 << RAW_DST_PTR_OFFSET));

		ubMTIndex = RetrySBPrepareFreeMT(RetrySBReadHBPass_Callback, RetrySBReadHBFail_Callback);
		RetrySBSelectDMAMTFPUTrigger( uwFpu_offset, ulFSA, ubMTIndex);
		if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
			gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
		}
		else {
			gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
			gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
		}
		break;
	}
}

void RetrySBReadSB1( U8 ubPageType, U8 *pubReadRetryTable, U8 ldpc_frame_idx)
{
	U16 *fpu_ptr;
	U16 uwFpu_offset;
	U8 ubMTIndex;

	U8 ubFPUACRRIdx = 0;
	U16	uwFPUACRRIValue = 0;

	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_SB_READ_SB1_INIT:
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SB_READ_SB1_DELTA_SET_FEATURE;
		gSBTask.ubFourthstate = RETRY_SB_4TH_STATE_SB_READ_LEVEL_BY_DELTA_TABLE_SET_FEATURE;
	/* no break */
	case RETRY_SB_3RD_STATE_SB_READ_SB1_DELTA_SET_FEATURE:
		//May Need set 2 FA by ubSecondFeatureAddr flag

		if (RETRY_MICRON_TABLR_PARAMETER_1H1S == gSBTask.ubSBRead1H1Sor1H2S) {
			RetrySBSetFeatureByReadRetryDeltaTableOffset( ubPageType, pubReadRetryTable, SB_MICRON_DELTA_TABLE_1H1S_SOFTBIT_1);
		}
		else {
			RetrySBSetFeatureByReadRetryDeltaTableOffset( ubPageType, pubReadRetryTable, SB_MICRON_DELTA_TABLE_1H2S_SOFTBIT_1);
		}

		break;
	case RETRY_SB_3RD_STATE_SB_READ_SB1_DELTA_READ:
		if (gSBTask.MTTemplate.dma.ALUSelect & ALU_RULE_SLC_BIT) { // BIT1 = SLC
			uwFpu_offset = FPU_PTR_OFFSET(fpu_slc_micron_sb_read);
			ubFPUACRRIdx = 4;
			uwFPUACRRIValue = VA_ADR_1B(0x00);
		}
		else {
			uwFpu_offset = FPU_PTR_OFFSET(fpu_tlc_micron_sb_read);
			ubFPUACRRIdx = 3;
			uwFPUACRRIValue = VA_ADR_1B(gSBTask.ub7thAddr);
		}

		//Micron  Nicks, SBSBR with ACRR
		fpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);

#if (PS5013_EN)
		//Update ACRR Value for SBSBR
		fpu_ptr[ubFPUACRRIdx] = uwFPUACRRIValue;
#endif

		ubMTIndex = RetrySBPrepareFreeMT(RetrySBReadSB1Pass_Callback, RetrySBReadSB1Fail_Callback);
		RetrySBSelecMTFPUTrigger( uwFpu_offset, (U32)( (gSBTask.ulFSA_Align) + (gSBTask.ubCurr_frm_idx) ), 0, ubMTIndex, SWITCH_CLK__NORMAL_MODE);
		if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
			gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
		}
		else {
			gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
			gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
		}
		break;
	case RETRY_SB_3RD_STATE_SB_READ_SB1_DELTA_DMA:
		uwFpu_offset = FPU_PTR_OFFSET(fpu_raw_dma_read);
		fpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);
		fpu_ptr[7] =  FPU_DMA_R_RAW((1 << RAW_MODE_OFFSET) | (2 << RAW_LOGIC_OFFSET) | (1 << RAW_SRC_PTR_OFFSET) | (0 << RAW_DST_PTR_OFFSET));
		ubMTIndex = RetrySBPrepareFreeMT(RetrySBReadSB1Pass_Callback, RetrySBReadSB1Fail_Callback);
		RetrySBSelectDMAMTFPUTrigger( uwFpu_offset, (U32)( (gSBTask.ulFSA_Align) + (gSBTask.ubCurr_frm_idx) ), ubMTIndex);
		if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
			gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
		}
		else {
			gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
			gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
		}
		break;
	}
}

void RetrySBReadSB2( U8 ubPageType, U8 *pubReadRetryTable, U8 ldpc_frame_idx)
{
	U16 *fpu_ptr;
	U16 uwFpu_offset;
	U8 ubMTIndex;

	U8 ubFPUACRRIdx = 0;
	U16	uwFPUACRRIValue = 0;

	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_SB_READ_SB2_INIT:
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SB_READ_SB2_DELTA_SET_FEATURE;
		gSBTask.ubFourthstate = RETRY_SB_4TH_STATE_SB_READ_LEVEL_BY_DELTA_TABLE_SET_FEATURE;
		if (RETRY_SB_MICRON_MLC_UPPER_PAGE == ubPageType) {
			gSBTask.ubTotalFeatureAddr = 2;
		}
		else if (RETRY_SB_MICRON_QLC_LOWER_PAGE == ubPageType) {
			gSBTask.ubTotalFeatureAddr = 4;
		}
		else if (RETRY_SB_MICRON_QLC_UPPER_PAGE == ubPageType) {
			gSBTask.ubTotalFeatureAddr = 4;
		}
		else if (RETRY_SB_MICRON_QLC_EXTRA_PAGE == ubPageType) {
			gSBTask.ubTotalFeatureAddr = 3;
		}
		else if (RETRY_SB_MICRON_QLC_TOP_PAGE == ubPageType) {
			gSBTask.ubTotalFeatureAddr = 4;
		}

	/* no break */
	case RETRY_SB_3RD_STATE_SB_READ_SB2_DELTA_SET_FEATURE:
		//May Need set 2 FA by ubSecondFeatureAddr flag
		RetrySBSetFeatureByReadRetryDeltaTableOffset( ubPageType, pubReadRetryTable, SB_MICRON_DELTA_TABLE_1H2S_SOFTBIT_2);
		break;
	case RETRY_SB_3RD_STATE_SB_READ_SB2_DELTA_READ:
		if (gSBTask.MTTemplate.dma.ALUSelect & ALU_RULE_SLC_BIT) { // BIT1 = SLC
			uwFpu_offset = FPU_PTR_OFFSET(fpu_slc_micron_sb_read);
			ubFPUACRRIdx = 4;
			uwFPUACRRIValue = VA_ADR_1B(0x00);
		}
		else {
			uwFpu_offset = FPU_PTR_OFFSET(fpu_tlc_micron_sb_read);
			ubFPUACRRIdx = 3;
			uwFPUACRRIValue = VA_ADR_1B(gSBTask.ub7thAddr);
		}

		//Micron  Nicks, SBSBR with ACRR
		fpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);

#if (PS5013_EN)
		//Update ACRR Value for SBSBR
		fpu_ptr[ubFPUACRRIdx] = uwFPUACRRIValue;
#endif

		ubMTIndex = RetrySBPrepareFreeMT(RetrySBReadSB2Pass_Callback, RetrySBReadSB2Fail_Callback);
		RetrySBSelecMTFPUTrigger( uwFpu_offset, (U32)( (gSBTask.ulFSA_Align) + (gSBTask.ubCurr_frm_idx) ), 0, ubMTIndex, SWITCH_CLK__NORMAL_MODE);
		if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
			gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
		}
		else {
			gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
			gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
		}
		break;
	case RETRY_SB_3RD_STATE_SB_READ_SB2_DELTA_DMA:
		uwFpu_offset = FPU_PTR_OFFSET(fpu_raw_dma_read);
		fpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);
		fpu_ptr[7] =  FPU_DMA_R_RAW((0 << RAW_MODE_OFFSET) | (2 << RAW_LOGIC_OFFSET) | (2 << RAW_SRC_PTR_OFFSET) | (1 << RAW_DST_PTR_OFFSET));
		ubMTIndex = RetrySBPrepareFreeMT(RetrySBReadSB2Pass_Callback, RetrySBReadSB2Fail_Callback);
		RetrySBSelectDMAMTFPUTrigger( uwFpu_offset, (U32)( (gSBTask.ulFSA_Align) + (gSBTask.ubCurr_frm_idx) ), ubMTIndex);
		if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
			gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
		}
		else {
			gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
			gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
		}
		break;
	}
}

void RetrySBSelectDMAMTFPUTrigger( U16 uwFpu_offset, U32 ulFSA, U8 ubMTIndex)
{

	FlhMT_t *mtp;
	MTCfg_t uoMTCfg;
	U8 ubIdx;
	L4KTable16B_t *pul4k_ptr;

	////------------------------------------------------------------
	mtp = (FlhMT_t *)M_MT_ADDR(ubMTIndex);

	////------------------------------------------------------------
	pul4k_ptr = (L4KTable16B_t *)((U32) gSBTask.ultmp_SpareAddr);

	memcpy((void *) & (gSBTask.MT), (void *) & (gSBTask.MTTemplate), sizeof(FlhMT_t));


	gSBTask.MT.dma.btForce_R_Fail = 0;

	gSBTask.MT.dma.uliFSA0_1 = ulFSA;

	gSBTask.MT.dma.btBMUAllocateEn = 0;
	gSBTask.MT.dma.uwFPUPtr  = uwFpu_offset;
	gSBTask.MT.cmd.btBusy = 1;
	gSBTask.MT.cmd.btUpdPollingSequence = 1;
	gSBTask.MT.cmd.POL_SEQ_SEL = (POL_SEQ_FPU_ENTRY_READ_STATUS_LUN0_BUSY_20 + gSBTask.ublun);

	gSBTask.MT.dma.btConversionBypass = 1;
	gSBTask.MT.dma.btGC = 0;

	gSBTask.MT.dma.btLDPCCorrectEn = 0;
	gSBTask.MT.dma.btCRCCheckDis = 1;
	gSBTask.MT.dma.btDisableUDMA = 1;
#if (PS5017_EN)
	gSBTask.MT.dma.btFpuPCAEn = 1;
#endif /* (PS5017_EN) */
	//Micron SB Todo
	if ( (gSBTask.ubThirdstate == RETRY_SB_3RD_STATE_HB_READ_DIRECT_READ_DMA)
		|| (gSBTask.ubThirdstate == RETRY_SB_3RD_STATE_SB_READ_SB1_DELTA_DMA)
		|| (gSBTask.ubThirdstate == RETRY_SB_3RD_STATE_SB_READ_SB2_DELTA_DMA)
		|| (gSBTask.ubThirdstate == RETRY_SB_3RD_STATE_SB_READ_SB2_PLUS_MINUS_1_DELTA_DMA_WITH_XNOR)
		|| (gSBTask.ubThirdstate == RETRY_SB_3RD_STATE_SB_READ_SB3_DMA)
		|| (gSBTask.ubThirdstate == RETRY_SB_3RD_STATE_SB_READ_SB4_DMA)
		|| (gSBTask.ubThirdstate == RETRY_SB_3RD_HB_READ_DIRECT_READ_FOR_TURBO_RAIN)
	) {
		gSBTask.MT.dma.ADGSelectPointer = gSBTask.ubCurrentLDPCFrameIdx;
	}
	else {
		gSBTask.MT.dma.ADGSelectPointer = 0;
	}

	gSBTask.MT.dma.btAllowSwitch = 0;
	gSBTask.MT.dma.L4KNum = 1;
	gSBTask.MT.dma.FrameNum = 1;
	gSBTask.MT.dma.btZipEn = 0;
	gSBTask.MT.dma.btCompareEn = 0;
	gSBTask.MT.dma.btBufferMode = 0;
	gSBTask.MT.dma.L4KSparePtr = ( gSBTask.ultmp_SpareAddr) & 0xFFFF;


	ubIdx = gSBTask.ubCurr_frm_idx;
	pul4k_ptr = (L4KTable16B_t *)((U32)(gSBTask.ultmp_SpareAddr));
	pul4k_ptr->BitMap.Read.FW = 0;
	pul4k_ptr->BitMap.Read.Zinfo = MAX_ZINFO;
	pul4k_ptr->BitMap.Read.ubBufferValid = 0xFF;
	pul4k_ptr->BitMap.Read.uwL4kNextPtr = 0xFFFF;
	pul4k_ptr->BitMap.Read.BADR = ((U32)gSBTask.ultmp_addr) >> SECTOR_SIZE_SHIFT;
	///// do global trigger

	//set MTQ_ID
	gSBTask.MT.cmd.btCESelectMode = 1;
	gSBTask.MT.cmd.ubCEValue = gSBTask.MTTemplate.cmd.ubCEValue;
	gSBTask.MT.cmd.NormalTargetCPU = MT_TARGET_CPU0;
	gSBTask.MT.cmd.ErrorTargetCPU = MT_TARGET_CPU0;

	memcpy((void *)mtp, (void *) & (gSBTask.MT), sizeof(FlhMT_t));

	// Global Trigger MT
	uoMTCfg.uoAll = 0;
	uoMTCfg.bits_recc.ubQUE_IDX = gSBTask.MTTemplate.cmd.ubCEValue;
	uoMTCfg.bits_recc.ubMT_IDX = ubMTIndex;
	uoMTCfg.bits_recc.btQOS =  gSBTask.ubUseQorOrNot;
	uoMTCfg.u32.ulMT_CFG1 = 0;
	FlaGlobalTrigger(&uoMTCfg);
}

void RetrySBFPUTrigger(U16 uwFpu_offset, U8 int_en, U16 vct)
{
	R32_FCTL_CH[gSBTask.ubChannel][R32_FCTL_INT_VCT] &= CLR_NORMAL_CPU;
	R32_FCTL_CH[gSBTask.ubChannel][R32_FCTL_INT_VCT] |= (1 << NORMAL_CPU_SHIFT);

	R32_FCTL_CH[gSBTask.ubChannel][R32_FCTL_INT_VCT] &= CLR_ERROR_CPU;
	R32_FCTL_CH[gSBTask.ubChannel][R32_FCTL_INT_VCT] |= (1 << ERROR_CPU_SHIFT);

	R32_FCTL_CH[gSBTask.ubChannel][R32_FCTL_INT_CFG] &= (~INT_VEC_EN_BIT);

	if (uwFpu_offset != 0xFFFF) {
		R32_FCTL_CH[gSBTask.ubChannel][R32_FCTL_FPU_ENTRY] &= ~FPU_ADDR_BASE_SHIFT_MASK;
		R32_FCTL_CH[gSBTask.ubChannel][R32_FCTL_FPU_ENTRY] |= uwFpu_offset;
	}

	M_FIP_TRIG_FPU(gSBTask.ubChannel);
	while (M_FIP_CHK_FPU_BUSY(gSBTask.ubChannel));
}

void RetrySBSetFeatureByReadRetryDeltaTableOffset( U8 ubPageType, U8 *pubReadRetryTable, U8 ubSBIdx)
{
	U8 ubMTIndex;
	U16 uwFpu_offset = FPU_PTR_OFFSET(fpu_entry_nop);
	U16 *puwfpu_ptr;
	U8 ubi = 0;
	U8 ubCheckFeatureParamBMP = 0;
	U8 ubFeatureAddress = 0;
	U8 ubFeatureAddressReadRetryTableOffSet = 0;

	switch (ubPageType) {
	case RETRY_SB_MICRON_SLC_PAGE:
		gSBTask.ubTotalFeatureAddr = 1;
		ubFeatureAddress = 0xE1;
		ubFeatureAddressReadRetryTableOffSet = SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_SLC_E1H_INDEX;
		break;
	case RETRY_SB_MICRON_MLC_LOWER_PAGE:
		gSBTask.ubTotalFeatureAddr = 1;
		ubFeatureAddress = 0xE2;
		ubFeatureAddressReadRetryTableOffSet = SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_MLC_LOWER_E2H_INDEX;
		break;
	case RETRY_SB_MICRON_MLC_UPPER_PAGE:
		gSBTask.ubTotalFeatureAddr = 2;
		if (gSBTask.ubCurrentFeatureAddr == 0) {
			ubFeatureAddress = 0xE1;
			ubFeatureAddressReadRetryTableOffSet = SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_MLC_UPPER_E1H_INDEX;
		}
		else if (gSBTask.ubCurrentFeatureAddr == 1) {
			ubFeatureAddress = 0xE2;
			ubFeatureAddressReadRetryTableOffSet = SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_MLC_UPPER_E2H_INDEX;
		}
		break;
	case RETRY_SB_MICRON_QLC_LOWER_PAGE:
		gSBTask.ubTotalFeatureAddr = 4;
		if (gSBTask.ubCurrentFeatureAddr == 0) {
			ubFeatureAddress = 0xBC;
			ubFeatureAddressReadRetryTableOffSet = SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_QLC_LOWER_BCH_INDEX;
		}
		else if (gSBTask.ubCurrentFeatureAddr == 1) {
			ubFeatureAddress = 0xBA;
			ubFeatureAddressReadRetryTableOffSet = SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_QLC_LOWER_BAH_INDEX;
		}
		else if (gSBTask.ubCurrentFeatureAddr == 2) {
			ubFeatureAddress = 0xB9;
			ubFeatureAddressReadRetryTableOffSet = SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_QLC_LOWER_B9H_INDEX;
		}
		else if (gSBTask.ubCurrentFeatureAddr == 3) {
			ubFeatureAddress = 0xBE;
			ubFeatureAddressReadRetryTableOffSet = SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_QLC_LOWER_BEH_INDEX;
		}
		break;
	case RETRY_SB_MICRON_QLC_UPPER_PAGE:
		gSBTask.ubTotalFeatureAddr = 4;
		if (gSBTask.ubCurrentFeatureAddr == 0) {
			ubFeatureAddress = 0xBC;
			ubFeatureAddressReadRetryTableOffSet = SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_QLC_UPPER_BCH_INDEX;
		}
		else if (gSBTask.ubCurrentFeatureAddr == 1) {
			ubFeatureAddress = 0xBD;
			ubFeatureAddressReadRetryTableOffSet = SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_QLC_UPPER_BDH_INDEX;
		}
		else if (gSBTask.ubCurrentFeatureAddr == 2) {
			ubFeatureAddress = 0xBE;
			ubFeatureAddressReadRetryTableOffSet = SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_QLC_UPPER_BEH_INDEX;
		}
		else if (gSBTask.ubCurrentFeatureAddr == 3) {
			ubFeatureAddress = 0xBF;
			ubFeatureAddressReadRetryTableOffSet = SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_QLC_UPPER_BFH_INDEX;
		}
		break;
	case RETRY_SB_MICRON_QLC_EXTRA_PAGE:
		gSBTask.ubTotalFeatureAddr = 3;
		if (gSBTask.ubCurrentFeatureAddr == 0) {
			ubFeatureAddress = 0xB9;
			ubFeatureAddressReadRetryTableOffSet = SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_QLC_XTRA_B9H_INDEX;
		}
		else if (gSBTask.ubCurrentFeatureAddr == 1) {
			ubFeatureAddress = 0xB8;
			ubFeatureAddressReadRetryTableOffSet = SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_QLC_XTRA_B8H_INDEX;
		}
		else if (gSBTask.ubCurrentFeatureAddr == 2) {
			ubFeatureAddress = 0xBB;
			ubFeatureAddressReadRetryTableOffSet = SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_QLC_XTRA_BBH_INDEX;
		}
		break;
	case RETRY_SB_MICRON_QLC_TOP_PAGE:
		gSBTask.ubTotalFeatureAddr = 4;
		if (gSBTask.ubCurrentFeatureAddr == 0) {
			ubFeatureAddress = 0xBD;
			ubFeatureAddressReadRetryTableOffSet = SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_QLC_TOP_BDH_INDEX;
		}
		else if (gSBTask.ubCurrentFeatureAddr == 1) {
			ubFeatureAddress = 0xBB;
			ubFeatureAddressReadRetryTableOffSet = SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_QLC_TOP_BBH_INDEX;
		}
		else if (gSBTask.ubCurrentFeatureAddr == 2) {
			ubFeatureAddress = 0xBA;
			ubFeatureAddressReadRetryTableOffSet = SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_QLC_TOP_BAH_INDEX;
		}
		else if (gSBTask.ubCurrentFeatureAddr == 3) {
			ubFeatureAddress = 0xBF;
			ubFeatureAddressReadRetryTableOffSet = SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_QLC_TOP_BFH_INDEX;
		}
		break;
	}

	switch (gSBTask.ubFourthstate) {
	case RETRY_SB_4TH_STATE_SB_READ_LEVEL_BY_DELTA_TABLE_SET_FEATURE:
		uwFpu_offset = FPU_PTR_OFFSET(fpu_set_feature);
		puwfpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);
		/// reset fpu entry
		for (ubi = 0 ; ubi < SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH ; ubi++) {
			if (0 == ubi) {
				puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (ubi << 1)] = FPU_DAT_W_FIR_D(0x00);
			}
			else if ((SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH - 1) == ubi) {
				puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (ubi << 1)] = FPU_DAT_W_LAST_D(0x00);
			}
			else {
				puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (ubi << 1)] = FPU_DAT_W_MID_D(0x00);
			}
			puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (ubi << 1) + 1] = FPU_DAT_W(0x00);
		}
		puwfpu_ptr[0] = FPU_CMD(0xD5);				//set_feature
		puwfpu_ptr[1] = FPU_ADR_1B(gSBTask.ublun);
		if (FIP_FLASH_CLOCK_400MHZ == gFlhEnv.ubTargetFlashClock) {
			// flh clk 400
			puwfpu_ptr[3] = FPU_DLY(0x15);		// Decimal 21
		}
		else if (FIP_FLASH_CLOCK_333MHZ == gFlhEnv.ubTargetFlashClock) {
			// flh clk 333
			puwfpu_ptr[3] = FPU_DLY(0xF);		// Decimal 15
		}
		else {
			puwfpu_ptr[3] = FPU_DLY(0x10);		// Decimal 16
		}

		puwfpu_ptr[2] = FPU_ADR_1B(ubFeatureAddress);

		// set <P1>, <P2>, <P3>, <P4>
		puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P1] = FPU_DAT_W_FIR_D(pubReadRetryTable[ubSBIdx * SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_LENGTH + ubFeatureAddressReadRetryTableOffSet + SB_MICRON_SET_FEATURE_P1_DATA_INDEX]);
		puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P1 + 1] = FPU_DAT_W(pubReadRetryTable[ubSBIdx * SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_LENGTH + ubFeatureAddressReadRetryTableOffSet + SB_MICRON_SET_FEATURE_P1_DATA_INDEX]);

		puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P2] = FPU_DAT_W_MID_D(pubReadRetryTable[ubSBIdx * SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_LENGTH + ubFeatureAddressReadRetryTableOffSet + SB_MICRON_SET_FEATURE_P2_DATA_INDEX]);
		puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P2 + 1] = FPU_DAT_W(pubReadRetryTable[ubSBIdx * SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_LENGTH + ubFeatureAddressReadRetryTableOffSet + SB_MICRON_SET_FEATURE_P2_DATA_INDEX]);

		puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P3] = FPU_DAT_W_MID_D(pubReadRetryTable[ubSBIdx * SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_LENGTH + ubFeatureAddressReadRetryTableOffSet + SB_MICRON_SET_FEATURE_P3_DATA_INDEX]);
		puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P3 + 1] = FPU_DAT_W(pubReadRetryTable[ubSBIdx * SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_LENGTH + ubFeatureAddressReadRetryTableOffSet + SB_MICRON_SET_FEATURE_P3_DATA_INDEX]);

		puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P4] = FPU_DAT_W_LAST_D(pubReadRetryTable[ubSBIdx * SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_LENGTH + ubFeatureAddressReadRetryTableOffSet + SB_MICRON_SET_FEATURE_P4_DATA_INDEX]);
		puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P4 + 1] = FPU_DAT_W(pubReadRetryTable[ubSBIdx * SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_LENGTH + ubFeatureAddressReadRetryTableOffSet + SB_MICRON_SET_FEATURE_P4_DATA_INDEX]);

		//Micron SB Todo
		puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_END_IDX] = FPU_END;

		break;
	case RETRY_SB_4TH_STATE_SB_READ_LEVEL_BY_DELTA_TABLE_GET_FEATURE:
		uwFpu_offset = FPU_PTR_OFFSET(fpu_get_feature);
		puwfpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);
		puwfpu_ptr[0] = FPU_CMD(0xD4);				//get_feature
		puwfpu_ptr[1] = FPU_ADR_1B(gSBTask.ublun);
		puwfpu_ptr[2] = FPU_ADR_1B(ubFeatureAddress);
		puwfpu_ptr[SB_MICRON_GET_FEATURE_FPU_END_IDX] = FPU_END;
		break;

	case RETRY_SB_4TH_STATE_SB_READ_LEVEL_BY_DELTA_TABLE_CHECK_FEATURE:
		uwFpu_offset = FPU_PTR_OFFSET(fpu_read_and_compare_feature_data);
		puwfpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);

		puwfpu_ptr[0] = FPU_CMD(0x00);		//check_feature
		puwfpu_ptr[1] = FPU_DLY(0x10);

		//Clear <P1><P2><P3><P3> Compare mask
		for (ubi = 0; ubi < PARAMETER_NUM_PER_FPU; ubi++) {
			puwfpu_ptr[SB_MICRON_CHECK_FEATURE_FPU_START_IDX + (ubi << 1)] =  FPU_DAT_R_CMP(0x00);
			puwfpu_ptr[(SB_MICRON_CHECK_FEATURE_FPU_START_IDX + 1) + (ubi << 1)] = FPU_DAT_R_MASK(0x00);
		}
		ubCheckFeatureParamBMP = (BIT_MASK(PARAMETER_NUM_PER_FPU));
		for (ubi = 0; ubi < PARAMETER_NUM_PER_FPU; ubi++) {		//Enable Param that need compare
			if (ubCheckFeatureParamBMP & (BIT0 << ubi)) {
				puwfpu_ptr[(SB_MICRON_CHECK_FEATURE_FPU_START_IDX + 1) + (ubi << 1)] = FPU_DAT_R_MASK(0xFF);
			}
		}

		puwfpu_ptr[SB_MICRON_CHECK_FEATURE_FPU_START_IDX + (SB_MICRON_FEATURE_PARAM_P1_INDEX << 1)] = FPU_DAT_R_CMP(pubReadRetryTable[ubSBIdx * SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_LENGTH + ubFeatureAddressReadRetryTableOffSet + SB_MICRON_SET_FEATURE_P1_DATA_INDEX]);
		puwfpu_ptr[SB_MICRON_CHECK_FEATURE_FPU_START_IDX + (SB_MICRON_FEATURE_PARAM_P2_INDEX << 1)] = FPU_DAT_R_CMP(pubReadRetryTable[ubSBIdx * SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_LENGTH + ubFeatureAddressReadRetryTableOffSet + SB_MICRON_SET_FEATURE_P2_DATA_INDEX]);
		puwfpu_ptr[SB_MICRON_CHECK_FEATURE_FPU_START_IDX + (SB_MICRON_FEATURE_PARAM_P3_INDEX << 1)] = FPU_DAT_R_CMP(pubReadRetryTable[ubSBIdx * SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_LENGTH + ubFeatureAddressReadRetryTableOffSet + SB_MICRON_SET_FEATURE_P3_DATA_INDEX]);
		puwfpu_ptr[SB_MICRON_CHECK_FEATURE_FPU_START_IDX + (SB_MICRON_FEATURE_PARAM_P4_INDEX << 1)] = FPU_DAT_R_CMP(pubReadRetryTable[ubSBIdx * SB_MICRON_DELTA_TABLE_FEATURE_ADDRESS_LENGTH + ubFeatureAddressReadRetryTableOffSet + SB_MICRON_SET_FEATURE_P4_DATA_INDEX]);
		break;
	}

	if (NES_EN || NCS_EN) {
		ubMTIndex = RetrySBPrepareFreeMT(RetrySBSetReadRetryDeltaPass_Callback, RetrySBSetReadRetryDeltaPass_Callback);
	}
	else {
		ubMTIndex = RetrySBPrepareFreeMT(RetrySBSetReadRetryDeltaPass_Callback, RetrySBSetReadRetryDeltaFail_Callback);
	}
	RetrySBSelecMTFPUTrigger(uwFpu_offset, (U32)( (gSBTask.ulFSA_Align) + (gSBTask.ubCurr_frm_idx) ), 0, ubMTIndex, SWITCH_CLK__LOW_CLK_MODE);

	if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
		gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
	}
	else {
		gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
		gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
	}
}

void RetrySBClearSBOffsetFeatures( U8 ubPageType, U8 *pubReadRetryTable)
{
	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_SB_CLEAR_SB_OFFSET_INIT:
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SB_CLEAR_SB_OFFSET_DELTA_SET_FEATURE;
		gSBTask.ubFourthstate = RETRY_SB_4TH_STATE_SB_READ_LEVEL_BY_DELTA_TABLE_SET_FEATURE;
		if (RETRY_SB_MICRON_MLC_UPPER_PAGE == ubPageType) {
			gSBTask.ubTotalFeatureAddr = 2;
		}
		else if (RETRY_SB_MICRON_QLC_LOWER_PAGE == ubPageType) {
			gSBTask.ubTotalFeatureAddr = 4;
		}
		else if (RETRY_SB_MICRON_QLC_UPPER_PAGE == ubPageType) {
			gSBTask.ubTotalFeatureAddr = 4;
		}
		else if (RETRY_SB_MICRON_QLC_EXTRA_PAGE == ubPageType) {
			gSBTask.ubTotalFeatureAddr = 3;
		}
		else if (RETRY_SB_MICRON_QLC_TOP_PAGE == ubPageType) {
			gSBTask.ubTotalFeatureAddr = 4;
		}
	/* no break */
	case RETRY_SB_3RD_STATE_SB_CLEAR_SB_OFFSET_DELTA_SET_FEATURE:
		RetrySBSetFeatureByReadRetryDeltaTableOffset( ubPageType, pubReadRetryTable, SB_MICRON_DELTA_TABLE_DEFAULT);
		break;
	}
}

void RetrySBSetDefaultFeatureAndCheck(void)
{
	U8 ubi = 0;
	switch (gSBTask.ubSubstate) {
	case RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_INIT:
		gSBTask.ubMTFailCnt = 0;
		gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_ARC;
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_SET_FEATURE;
		break;

	case RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_ARC:
		RetrySBHBSetFeatureARC(FALSE, FALSE);
		break;

	case RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_SB_RR_OFFSET:
		RetrySBClearRROffset();
		break;

	case RETRY_SB_SUBSTATE_SET_DEFAULT_CORRECTIVE_READ_SETTING:
		if ((gSBTask.ubThirdstate == RETRY_SB_3RD_STATE_SETTING_CORRECTIVE_READ_SET_FEATURE) && (gSBTask.ubIsEnhance)) {
			for (ubi = 0; ubi < RETRY_SB_MICRON_SET_MLBI_NUM ; ubi++) {
				RetrySBFlaSetMLBi(gSBTask.ulFSA_ori, guwSBMicronSetMLBiAddrTable[ubi], gubSBMicronDefaultMLBiValueTable[ubi]);
			}
		}
		RetrySBSetCR(RETRY_SB_NOT_CORRECTIVE_READ, FALSE);
		break;

	case RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_SB_READ_OFFSET:
		RetrySBClearSBOffsetFeatures(gSBTask.ubPageType, (U8 *)delta_table);
		break;
	}
}

void RetrySBBackupRestore( U64 dram_addr, U32 iram_addr, U8 ibf_ptr, U8 direction, U8 mode, U8 bch_mode, U8 all, U8 frm_msk)
{
	/*
	    direction --> 1: restore frame to IBF
	                  0: backup frame to DRAM/IRAM
	         mode --> 1: transfer DATA/Spare to/from DRAM/IRAM
	                  0: transfer ECC frame to/from DRAM
	*/
	// setup ldpc frame size
	M_FIP_CLEAR_DRAM_MULTI(gSBTask.ubChannel);
	// Clean BR_W_LEN then set backup_restore length
	M_FIP_SET_BACK_RESTORE_LENGTH(gSBTask.ubChannel, ((((RetrySBFlashGetCurrentLDPCFrameSize() * 2) + 16) / 16) - 1));
	// backup data from channel ibf to DBUF_BASE
	U16 *fpu_ptr = NULL;
	U16 uwFpu_offset;
	//Wait FIP_CH_FPU_TRIGGER to 0x04, only in SB (other Queue empty)

	while (SRQ_SVL_BIT != (M_FIP_GET_FPU_TRIG(gSBTask.ubChannel) & (MT_BSY_BIT | ANY_BSY_BIT | SRQ_SVL_BIT | SIGN_OFF_BUSY_BIT | FPU_TRIGGER_BIT)));
#if (!PS5017_EN)
	M_FIP_SET_RAW_DMA_ADR(gSBTask.ubChannel, dram_addr);
	M_FIP_SET_RAW_DMA_IRAM_ADR(gSBTask.ubChannel, iram_addr);
#endif /* (!PS5017_EN) */
	/// fpu_ptr = (U16*) (IRAM_BASE + FPU_ENTRY_DUMP_IBUF_TEST);
#if (PS5017_EN)
	U32 iram_off;  //E19
	iram_off = (iram_addr - IRAM_BASE);
	R32_FCTL_CH[gSBTask.ubChannel][R32_FCTL_IFSA0] = dram_addr;
	R32_FCTL_CH[gSBTask.ubChannel][R32_FCTL_IFSA_HIGH] = 0;
	R8_FCTL_CH[gSBTask.ubChannel][R8_FCTL_IN_FSA0] = iram_off & IN_FSA0_MASK;
	R8_FCTL_CH[gSBTask.ubChannel][R8_FCTL_IN_FSA1] = (iram_off >> 8) & IN_FSA1_MASK;
#endif /*(PS5017_EN)*/

	uwFpu_offset = FPU_PTR_OFFSET(fpu_backup_restore_ibuf);
	fpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);

	*fpu_ptr = FPU_BR((frm_msk << 8) | (all << 6) | (bch_mode << 5) | (ibf_ptr << 2) | (direction << 1) | mode);
	fpu_ptr++;

	*fpu_ptr = FPU_END;

	RetrySBFPUTrigger( uwFpu_offset, FALSE, 0xFFFF);
	while (SRQ_SVL_BIT != (M_FIP_GET_FPU_TRIG(gSBTask.ubChannel) & (MT_BSY_BIT | ANY_BSY_BIT | SRQ_SVL_BIT | SIGN_OFF_BUSY_BIT | FPU_TRIGGER_BIT)));
}

void RetrySBBackupCorrect2k(U8 src_ibf, U8 ldpc_frame_idx, U32 ulbackup_addr, U32 ultmp_addr, U32 Iram_addr, U32 TempIram_addr, U8 ubMode)
{
	U32 ecc_size;
	DMACParam_t DMACParam;

	ecc_size = (RetrySBFlashGetCurrentLDPCFrameSize() - 2048 - 8);

	if ((RETRY_SB_BACKUP_RESTORE_TO_DRAMIRAM == ubMode)
		|| (RETRY_SB_BACKUP_RESTORE_TO_DRAMIRAM_AND_KEEP == ubMode)) {
		//Retry_SB_backup_restore(ultmp_addr, TempIram_addr, src_ibf, 0, ubMode, 0, 0, 0);
		RetrySBBackupRestore(ultmp_addr, TempIram_addr, src_ibf, 0, 1, 0, 0, 0);
	}
	else if (ubMode == RETRY_SB_BACKUP_RESTORE_FROM_ONLYDRAM) {
		RetrySBBackupRestore(ultmp_addr, TempIram_addr, src_ibf, 1, 0, 0, 0, 0);
	}
	else {
		//Retry_SB_backup_restore(ultmp_addr, TempIram_addr, src_ibf, 0, ubMode, 0, 0, 0);
		RetrySBBackupRestore(ultmp_addr, TempIram_addr, src_ibf, 0, 0, 0, 0, 0);
	}

	/* For PS5011, data size in ibf format is following :

	      byte 15    byte 0
	      ------------------
	     |     2K data 0   |
	      -----------------
	     |     2K data 1   |
	      -----------------
	     | spr 1  |  spr 0 |
	      -----------------
	     |    x   |  CRC 0 |
	      -----------------
	     |      LDPC 0     |
	      -----------------
	     |    x   |  CRC 1 |
	      -----------------
	     |      LDPC 1     |
	      -----------------    */
	if (ubMode == RETRY_SB_BACKUP_RESTORE_TO_DRAMIRAM) {
		if (ldpc_frame_idx == 0) {
			// first 2k
			if (RETRY_SB_ENABLE_DEBUG_CMP_WITH_HB_DATA) {
				if ((gHBTask.ubHBFailFrameMap & BIT(gSBTask.ubCurr_frm_idx))) {

				}
				else {
					for (ecc_size = 0 ; ecc_size < BC_2KB ; ecc_size++) {
						if (  *((U8 *)ulbackup_addr + ecc_size) != *((U8 *)ultmp_addr + ecc_size) ) {
							M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
						}
					}
					for (ecc_size = 0 ; ecc_size < BC_8B ; ecc_size++) {
						if (  *((U8 *)Iram_addr + ecc_size) != *((U8 *)TempIram_addr + ecc_size) ) {
							M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
						}
					}
				}
			}
			/* copy first 2k data */
			DMACParam.ulSourceAddr = ultmp_addr;
			DMACParam.ulDestAddr = ulbackup_addr;
			DMACParam.ul32ByteNum = SIZE_IN_32B(BC_2KB);
			gpRetry->ubWaitDMACDoneFlag = TRUE;

			DMACCopy(DMAC_MODE_NORMAL_QUEUE, &DMACParam, gulReadRetryDMACDone_CallBack, 0);
			while (gpRetry->ubWaitDMACDoneFlag == TRUE) {
				DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
			}

			/* copy spr 0 */
			memcpy((U32 *)(Iram_addr), (U32 *)(TempIram_addr), BC_8B );

		}
		else {      // second 2k

			//        LOG_PRINTF("[Backup] 2nd correctable 2k to DBUF addr 0x%x\n", ulbackup_addr);
			if (RETRY_SB_ENABLE_DEBUG_CMP_WITH_HB_DATA) {
				if ((gHBTask.ubHBFailFrameMap & BIT(gSBTask.ubCurr_frm_idx))) {

				}
				else {
					for (ecc_size = 0 ; ecc_size < BC_2KB ; ecc_size++) {
						if (  *((U8 *)ulbackup_addr + BC_2KB + ecc_size) != *((U8 *)ultmp_addr + BC_2KB + ecc_size) ) {
							M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
						}
					}
					for (ecc_size = 0 ; ecc_size < BC_8B ; ecc_size++) {
						if (  *((U8 *)Iram_addr + BC_8B + ecc_size) != *((U8 *)TempIram_addr + BC_8B + ecc_size) ) {
							M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
						}
					}
				}
			}
			/* copy second 2k data */
			///memcpy((U32 *)(ulbackup_addr + BC_2KB), (U32 *)(ultmp_addr + BC_2KB), BC_2KB);
			DMACParam.ulSourceAddr = ((gSBTask.ubNeedToGetSB0Flag) ? ultmp_addr : (ultmp_addr + BC_2KB));
			DMACParam.ulDestAddr = (ulbackup_addr + BC_2KB);
			DMACParam.ul32ByteNum = SIZE_IN_32B(BC_2KB);
			gpRetry->ubWaitDMACDoneFlag = TRUE;

			DMACCopy(DMAC_MODE_NORMAL_QUEUE, &DMACParam, gulReadRetryDMACDone_CallBack, 0);
			while (gpRetry->ubWaitDMACDoneFlag == TRUE) {
				DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
			}
			/* copy spr 1 */
			if (gSBTask.ubNeedToGetSB0Flag) {
				/*for (ubx = 0 ; ubx < 4 ; ubx++) {
					UartPrintf("\nubx:%d, TempIram_addr: %x", ubx, *((U32 *)(TempIram_addr + ubx * BC_4B)));
				}*/
				memcpy((U32 *)(Iram_addr + BC_8B ), (U32 *)(TempIram_addr), BC_8B);
			}
			else {
				memcpy((U32 *)(Iram_addr + BC_8B ), (U32 *)(TempIram_addr + BC_8B), BC_8B);
			}
		}
	}
	else if (ubMode == RETRY_SB_BACKUP_RESTORE_TO_ONLYDRAM) {
		if (ldpc_frame_idx == 0) {
			// first 2k

			//        LOG_PRINTF("[Backup] 1st correctable 2k to DBUF addr 0x%x\n", ulbackup_addr);

			/* copy first 2k data */
			///memcpy((U32 *)ulbackup_addr, (U32 *)ultmp_addr, BC_2KB);
			DMACParam.ulSourceAddr = (ultmp_addr);
			DMACParam.ulDestAddr = (ulbackup_addr);
			DMACParam.ul32ByteNum = SIZE_IN_32B(BC_2KB);
			gpRetry->ubWaitDMACDoneFlag = TRUE;

			DMACCopy(DMAC_MODE_NORMAL_QUEUE, &DMACParam, gulReadRetryDMACDone_CallBack, 0);
			while (gpRetry->ubWaitDMACDoneFlag == TRUE) {
				DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
			}

			/* copy spr 0 */
			memcpy((U32 *)(ulbackup_addr + BC_4KB), (U32 *)(ultmp_addr + BC_4KB), BC_8B);
#if 1
			/* copy crc 0 */
			memcpy((U32 *)(ulbackup_addr + BC_4KB + BC_16B), (U32 *)(ultmp_addr + BC_4KB + BC_16B), BC_8B);

			/* copy ldpc 0 */
			memcpy((U32 *)(ulbackup_addr + BC_4KB + BC_16B + BC_16B), (U32 *)(ultmp_addr + BC_4KB + BC_16B + BC_16B), ecc_size);
#endif

		}
		else {      // second 2k

			//        LOG_PRINTF("[Backup] 2nd correctable 2k to DBUF addr 0x%x\n", ulbackup_addr);

			/* copy second 2k data */
			///memcpy((U32 *)(ulbackup_addr + BC_2KB), (U32 *)(ultmp_addr + BC_2KB), BC_2KB);
			DMACParam.ulSourceAddr = (ultmp_addr + BC_2KB);
			DMACParam.ulDestAddr = (ulbackup_addr + BC_2KB);
			DMACParam.ul32ByteNum = SIZE_IN_32B(BC_2KB);
			gpRetry->ubWaitDMACDoneFlag = TRUE;

			DMACCopy(DMAC_MODE_NORMAL_QUEUE, &DMACParam, gulReadRetryDMACDone_CallBack, 0);
			while (gpRetry->ubWaitDMACDoneFlag == TRUE) {
				DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
			}

			/* copy spr 1 */
			memcpy((U32 *)(ulbackup_addr + BC_4KB + BC_8B), (U32 *)(ultmp_addr + BC_4KB + BC_8B), BC_8B);

#if 1
			/* copy crc 1 */
			memcpy((U32 *)(ulbackup_addr + BC_4KB + SIZE_32B + ecc_size), (U32 *)(ultmp_addr + BC_4KB + SIZE_32B + ecc_size), BC_8B);

			/* copy ldpc 1 */
			memcpy((U32 *)(ulbackup_addr + BC_4KB + SIZE_32B + ecc_size + BC_16B), (U32 *)(ultmp_addr + BC_4KB + SIZE_32B + ecc_size + BC_16B), ecc_size);
#endif
		}
	}
	else if (RETRY_SB_BACKUP_RESTORE_TO_ONLYDRAM_AND_KEEP == ubMode) {

	}
	else if (RETRY_SB_BACKUP_RESTORE_TO_DRAMIRAM_AND_KEEP == ubMode) {

	}
	else {
		M_UART(ERROR_SOFTBIT_, "\n Done!");
	}
}

void RetrySBFlashRetoreLLR(U8 lmu_sel, U8 llr_idx)
{
	U32 idx;
	if (llr_idx > LLR_TBL_CNT) {
		return;
	}
	R32_FCON[R32_FCON_LDPC_CFG] &=  ~( (PAGE_SELECT_MASK) << PAGE_SELECT_SHIFT );

	switch (lmu_sel) {
	case 0:
		R32_FCON[R32_FCON_LDPC_CFG] |=  (( 0 & PAGE_SELECT_MASK) << PAGE_SELECT_SHIFT);
		break;
	case 1:
		R32_FCON[R32_FCON_LDPC_CFG] |=  (( 1 & PAGE_SELECT_MASK) << PAGE_SELECT_SHIFT);
		break;
	case 2:
		R32_FCON[R32_FCON_LDPC_CFG] |=  (( 2 & PAGE_SELECT_MASK) << PAGE_SELECT_SHIFT);
		break;
	case 3:
		R32_FCON[R32_FCON_LDPC_CFG] |=  (( 3 & PAGE_SELECT_MASK) << PAGE_SELECT_SHIFT);
		break;
	}

	for (idx = 0; idx < 10; idx++) {

		/* Select LLR table */
		R32_FCON[R32_FCON_ECC_PARAM_CFG] &= (~(ECC_PARAM_SEL_MASK << ECC_PARAM_SEL_SHIFT ));
		R32_FCON[R32_FCON_ECC_PARAM_CFG] |= (idx << ECC_PARAM_SEL_SHIFT);

		/* Fill LLR data */
		R32_FCON[R32_FCON_ECC_PARAM] = sb_llr_table[llr_idx][idx];

		//SB MICRON Check Point 0730
		M_UART(ERROR_SOFTBIT_, "\n Def_LLR Idx %d ,%x", llr_idx, sb_llr_table[llr_idx][idx]);

	}

	RetrySBFlashLoadECCParam(LLR_TABLE0_LOAD_BIT);
}

U8 RetrySBPrepareFreeMT(MT_Callback_Func_t PassCallback, MT_Callback_Func_t FailCallback)
{
	U8 ubMTIndex = FlaGetFreeMTIndex();
	gpRetry->TrigMTList.MTCb[gpRetry->TrigMTList.ubTail].ubMTIndex = ubMTIndex;
	gpRetry->TrigMTList.MTCb[gpRetry->TrigMTList.ubTail].PassCallback = PassCallback;
	gpRetry->TrigMTList.MTCb[gpRetry->TrigMTList.ubTail].FailCallback = FailCallback;
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, gpRetry->TrigMTList.ubTrigCnt != RETRY_MAX_TRIG_MT_NUM);
	gpRetry->TrigMTList.ubTrigCnt++;
	gpRetry->TrigMTList.ubTail = (gpRetry->TrigMTList.ubTail + 1) & RETRY_MAX_TRIG_MT_NUM_MASK;
	return ubMTIndex;
}

void RetrySBReleaseFinishMT(void)
{
	FlaAddFreeMTIndex(gpRetry->TrigMTList.MTCb[gpRetry->TrigMTList.ubHead].ubMTIndex);
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, gpRetry->TrigMTList.ubTrigCnt);
	gpRetry->TrigMTList.ubTrigCnt--;
	gpRetry->TrigMTList.ubHead = (gpRetry->TrigMTList.ubHead + 1) & RETRY_MAX_TRIG_MT_NUM_MASK;
}

void RetrySBCorrectMTTrigger( U8 dsp2_en, U8 sb_ibuf_ptr, U8 sel_2k, U8 dsp_en, U8 dsp_4k_en, U8 ubdecode_mode, U8 scale_mode, U8 flow_mode, U8 hb_sb, U8 ubMTIndex)
{
	U16 *fpu_ptr = NULL;
	FlhMT_t *mtp = NULL;
	MTCfg_t uoMTCfg;
	U16 uwFpu_offset;

	// hb_sb = 1 => SB correct
	// hb_sb = 0 => HB correct

	// set flow mode
	R32_FCON[R32_FCON_LDPC_CFG] &= ~(FLOW_MODE_MASK << FLOW_MODE_SHIFT);
	R32_FCON[R32_FCON_LDPC_CFG] |= (flow_mode << FLOW_MODE_SHIFT);

	///+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

	mtp = (FlhMT_t *)M_MT_ADDR(ubMTIndex);

	/// copy mtp tempelete here
	uwFpu_offset = FPU_PTR_OFFSET(fpu_sbc);
	fpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);
	fpu_ptr[0] = FPU_SBC((dsp_4k_en << 9) | (sel_2k << 8) | (dsp_en << 6) | (dsp2_en << 3) | (sb_ibuf_ptr << 0)) | (hb_sb << 7);

	memcpy((void *) & (gSBTask.MT), (void *) & (gSBTask.MTTemplate), sizeof(FlhMT_t));

	/// given ce_value, flh_type, io_type
	gSBTask.MT.dma.uwFPUPtr  = uwFpu_offset;

	gSBTask.MT.dma.btLDPCCorrectEn = 1;
	gSBTask.MT.dma.btInterruptVectorEn = 1;
	gSBTask.MT.dma.btScaleMode = scale_mode;
	gSBTask.MT.dma.DecodeMode = ubdecode_mode;
	gSBTask.MT.dma.btBCHBps = 1;
	gSBTask.MT.cmd.btMTPFormat = 1;

	gSBTask.MT.dma.btiFSAEn = 1;
	gSBTask.MT.dma.ADGSelectPointer = 0;

	gSBTask.MT.cmd.uliFSA0_1    = (U32)( (gSBTask.ulFSA_Align) + (gSBTask.ubCurr_frm_idx) );
#if (PS5017_EN)
	gSBTask.MT.dma.btFpuPCAEn = 1;
#endif /* (PS5017_EN) */
	///// do global trigger
	//set MTQ_ID
	gSBTask.MT.cmd.btCESelectMode = 1;
	gSBTask.MT.cmd.ubCEValue = gSBTask.MTTemplate.cmd.ubCEValue;
	gSBTask.MT.cmd.NormalTargetCPU = MT_TARGET_CPU0;
	gSBTask.MT.cmd.ErrorTargetCPU = MT_TARGET_CPU0;

	memcpy((void *)mtp, (void *) & (gSBTask.MT), sizeof(FlhMT_t));

	// Global Trigger MT
	uoMTCfg.uoAll = 0;
	uoMTCfg.bits_recc.ubQUE_IDX = gSBTask.MTTemplate.cmd.ubCEValue;
	uoMTCfg.bits_recc.ubMT_IDX = ubMTIndex;
	uoMTCfg.bits_recc.btQOS = gSBTask.ubUseQorOrNot;
	uoMTCfg.u32.ulMT_CFG1 = 0;
	FlaGlobalTrigger(&uoMTCfg);

}

void RetrySBSelecMTFPUTrigger(U16 uwFpu_offset, U32 ulFSA, U8 flh_type_legacy, U8 ubMTIndex, U8 ubMode)
{
	FlhMT_t *mtp = NULL;
	MTCfg_t uoMTCfg;
	U8 ubALU;

	ubALU = gSBTask.MTTemplate.dma.ALUSelect;
	mtp = (FlhMT_t *)M_MT_ADDR(ubMTIndex);

	///clear trig struct
	memset((void *) & (gSBTask.MT), 0, sizeof(FlhMT_t));

	/// copy mtp tempelete here
#if (!PS5017_EN)
	gSBTask.MT.cmd.btFPUEn = 0;
#endif /* (!PS5017_EN) */
	gSBTask.MT.cmd.btBusy = 1;

	gSBTask.MT.cmd.btConversionBypass = 0;
	gSBTask.MT.cmd.ALUSelect = gSBTask.MTTemplate.dma.ALUSelect;
	gSBTask.MT.cmd.btDisableUDMA = 1;
	gSBTask.MT.cmd.btiFSAEn = 1;

	gSBTask.MT.cmd.btAllowSwitch = 0;
	gSBTask.MT.dma.ADGSelectPointer = 0;
	gSBTask.MT.cmd.btIoType = gSBTask.MTTemplate.dma.btIoType;
	//Micron SB Todo
	//CLK rate
	if ((ubMode == SWITCH_CLK__LOW_CLK_MODE) && (ASIC)) {
		gSBTask.MT.cmd.FCLK_DIV = FIP_MT_CLK_DIV_MAX;
#if (!PS5017_EN)
		gSBTask.MT.cmd.btFCLK_DIV_EN = ENABLE;
#endif /* (!PS5017_EN) */
	}
	else {
		gSBTask.MT.cmd.FCLK_DIV = (U8)(gSBTask.MTTemplate.dma.FCLK_DIV);
#if (!PS5017_EN)
		gSBTask.MT.cmd.btFCLK_DIV_EN = (U8)(gSBTask.MTTemplate.dma.btFCLK_DIV_EN);
#endif /* (!PS5017_EN) */
	}
	gSBTask.MT.cmd.FlashType = gSBTask.MTTemplate.dma.FlashType;

#if (FIP_SUPPORT_MT_VIRTUAL_ADDRESS_EN)
	if ((uwFpu_offset == FPU_PTR_SLC_RETRY_READ_ACRR_CMD(0)) ||
		(uwFpu_offset == FPU_PTR_TLC_RETRY_READ_ACRR_CMD(0)) ||
		(uwFpu_offset == FPU_PTR_OFFSET(fpu_slc_micron_sb_read)) ||
		(uwFpu_offset == FPU_PTR_OFFSET(fpu_tlc_micron_sb_read))) {

		gSBTask.MT.cmd.VAMode = 1;
		gSBTask.MT.cmd.VA0 = gSBTask.ub7thAddr;
	}
#endif /* (FIP_SUPPORT_MT_VIRTUAL_ADDRESS_EN) */
	gSBTask.MT.cmd.btBMUAllocateEn = 0;
	gSBTask.MT.cmd.btInterruptVectorEn = 1;

	gSBTask.MT.cmd.uwFPUPtr  = uwFpu_offset;
	gSBTask.MT.cmd.btUpdPollingSequence = 1;
	//Micron SB Todo
	//Polling sequence
	gSBTask.MT.cmd.POL_SEQ_SEL = (((FPU_PTR_OFFSET(fpu_entry_reset_fc) == uwFpu_offset) || (FPU_PTR_OFFSET(fpu_entry_nop) == uwFpu_offset))) ? (POL_SEQ_FPU_ENTRY_READ_STATUS_LUN0_BUSY_20 + gSBTask.ubResetDie) : (POL_SEQ_FPU_ENTRY_READ_STATUS_LUN0_BUSY_20 + gSBTask.ublun);

	gSBTask.MT.dma.uliFSA0_1 = ulFSA;
#if (PS5017_EN)
	gSBTask.MT.dma.btFpuPCAEn = 1;
#endif /* (PS5017_EN) */
	///// do global trigger

	//set MTQ_ID
	gSBTask.MT.cmd.btCESelectMode = 1;
	gSBTask.MT.cmd.ubCEValue = gSBTask.MTTemplate.cmd.ubCEValue;
	gSBTask.MT.cmd.NormalTargetCPU = MT_TARGET_CPU0;
	gSBTask.MT.cmd.ErrorTargetCPU = MT_TARGET_CPU0;

	memcpy((void *)mtp, (void *) & (gSBTask.MT), sizeof(FlhMT_t));

	// Global Trigger MT
	uoMTCfg.uoAll = 0;
	uoMTCfg.bits_recc.ubQUE_IDX = gSBTask.MTTemplate.cmd.ubCEValue;
	uoMTCfg.bits_recc.ubMT_IDX = ubMTIndex;
	uoMTCfg.bits_recc.btQOS = gSBTask.ubUseQorOrNot;
	uoMTCfg.u32.ulMT_CFG1 = 0;
	FlaGlobalTrigger(&uoMTCfg);
}

void RetrySBSwitchStateToSetDefaultFeatureReset(void)
{
	//This should be the only Function that set "gSBTask.ubBackUpedState = RETRY_SB_STATE_SB_RESET_FLH"
	if (RETRY_SB_STATE_SB_RESET_FLH == gSBTask.ubBackUpedState) {		//when ubBackUpedState == RETRY_SB_STATE_SB_RESET_FLH mean`s Set Default Feature is Fail, Need  END SB flow directly
		//Reset FLH , Leave SB retry
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
		gSBTask.ubState = RETRY_SB_STATE_SB_RESET_FLH;
		gSBTask.ubBackUpedState = RETRY_SB_STATE_DONE;
	}
	else {
		gSBTask.ubState = RETRY_SB_STATE_SET_DEFAULT_FEATURE_AND_CHK;
		gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_INIT;
		gSBTask.ubBackUpedState = RETRY_SB_STATE_SB_RESET_FLH;
		gSBTask.ubBackUpedSubstate = RETRY_SB_SUBSTATE_NULL;
	}
}

void RetrySBFlashLoadECCParam(U32 load_target)
{
	/* Enable ECC clock for set configuration into ECC engine */
	M_FIP_ECC_CLOCK_GATING_DIS();

	/* Trigger load and check HW ready */
	R32_FCON[R32_FCON_ECC_PARAM_CFG] |= load_target;
	while (R32_FCON[R32_FCON_ECC_PARAM_CFG] & load_target);

	/* Disable ECC clock */
	M_FIP_ECC_CLOCK_GATING_EN();
}

void RetrySBFlashSetECCParam(U32 *data, U32 len, U32 load_target, U8 ubMode)
{
	U32 idx;

	for (idx = 0; idx < len; idx++) {
		/* Select LLR table */
		R32_FCON[R32_FCON_ECC_PARAM_CFG] &= (~(ECC_PARAM_SEL_MASK << ECC_PARAM_SEL_SHIFT ));
		R32_FCON[R32_FCON_ECC_PARAM_CFG] |= (idx << ECC_PARAM_SEL_SHIFT);

		/* Fill LLR data */
		if (ubMode == NORMAL_MODE) {
			R32_FCON[R32_FCON_ECC_PARAM] = data[idx];
		}
		else {
			R32_FCON[R32_FCON_ECC_PARAM] = data[len - 1 - idx];
		}
	}

	RetrySBFlashLoadECCParam(load_target);
}

void RetrySBHBSetFeatureARC(U8 ubCalibration, U8 ubPersistence)
{
	/*Calibration ON: do Read by Calibrate Read offset and save Read offset result to FA:A0h~ACh <P3>*/
	/*Persistence ON: do Read by Read offset on FA:A0h~ACh <P3>*/

	U16 *puwfpu_ptr = NULL;
	U16 uwFpu_offset;
	U8 ubMTIndex = 0;
	U8 ubi = 0;
	U8 ubFPUData = ((ubPersistence << 1) + ubCalibration);
#ifdef VIRTUAL_ADDRESS_EN
	ubFPUData |=  0x04;

	if ((gSBTask.ubSubstate == RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_ARC) && VALLEY_TRACK_EN) {
		ubFPUData |= 0x08; // enable Valley Track Bit3
	}

#endif
	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_SET_FEATURE:
		//Micron SB Todo
		//Micron Set feature
		uwFpu_offset = FPU_PTR_OFFSET(fpu_set_feature);		//set_feature
		puwfpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);
		/// reset fpu entry
		for (ubi = 0 ; ubi < SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH ; ubi++) {
			if (0 == ubi) {
				puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (ubi << 1)] = FPU_DAT_W_FIR_D(0x00);
			}
			else if ((SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH - 1) == ubi) {
				puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (ubi << 1)] = FPU_DAT_W_LAST_D(0x00);
			}
			else {
				puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (ubi << 1)] = FPU_DAT_W_MID_D(0x00);
			}
			puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (ubi << 1) + 1] = FPU_DAT_W(0x00);
		}
		puwfpu_ptr[0] = FPU_CMD(0xD5);
		puwfpu_ptr[1] = FPU_ADR_1B(gSBTask.ublun);
		puwfpu_ptr[2] = FPU_ADR_1B(0x96);				//FA 96h for ARC feature
		if (FIP_FLASH_CLOCK_400MHZ == gFlhEnv.ubTargetFlashClock) {
			// flh clk 400
			puwfpu_ptr[3] = FPU_DLY(0x15);		// Decimal 21
		}
		else if (FIP_FLASH_CLOCK_333MHZ == gFlhEnv.ubTargetFlashClock) {
			// flh clk 333
			puwfpu_ptr[3] = FPU_DLY(0xF);		// Decimal 15
		}
		else {
			puwfpu_ptr[3] = FPU_DLY(0x10);		// Decimal 16
		}

		puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P1] = FPU_DAT_W_FIR_D(ubFPUData);
		puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P1 + 1] = FPU_DAT_W(ubFPUData);
		puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_END_IDX] = FPU_END;

		break;
	case RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_GET_FEATURE:
		uwFpu_offset = FPU_PTR_OFFSET(fpu_get_feature);		//get_feature
		puwfpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);

		puwfpu_ptr[0] = FPU_CMD(0xD4);
		puwfpu_ptr[1] = FPU_ADR_1B(gSBTask.ublun);
		puwfpu_ptr[2] = FPU_ADR_1B(0x96);

		puwfpu_ptr[SB_MICRON_GET_FEATURE_FPU_END_IDX] = FPU_END;

		break;
	case RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_CHECK_FEATURE:
		uwFpu_offset = FPU_PTR_OFFSET(fpu_read_and_compare_feature_data);		//set_feature
		puwfpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);

		puwfpu_ptr[0] = FPU_CMD(0x00);
		puwfpu_ptr[1] = FPU_DLY(0x10);

		//Clear <P1><P2><P3><P3> Compare mask
		for (ubi = 0; ubi < PARAMETER_NUM_PER_FPU; ubi++) {
			puwfpu_ptr[SB_MICRON_CHECK_FEATURE_FPU_START_IDX + (ubi << 1)] =  FPU_DAT_R_CMP(0x00);
			puwfpu_ptr[(SB_MICRON_CHECK_FEATURE_FPU_START_IDX + 1) + (ubi << 1)] = FPU_DAT_R_MASK(0x00);
		}
		//Compare <P1>
		puwfpu_ptr[SB_MICRON_CHECK_FEATURE_FPU_START_IDX + (SB_MICRON_FEATURE_PARAM_P1_INDEX << 1)] =  FPU_DAT_R_CMP(ubFPUData);
		puwfpu_ptr[(SB_MICRON_CHECK_FEATURE_FPU_START_IDX + 1) + (SB_MICRON_FEATURE_PARAM_P1_INDEX << 1)] = FPU_DAT_R_MASK(0xFF);

		break;
	}
	if (NES_EN || NCS_EN) {
		ubMTIndex = RetrySBPrepareFreeMT(RetrySBHBSetFeatureARCPass_Callback, RetrySBHBSetFeatureARCPass_Callback);
	}
	else {
		ubMTIndex = RetrySBPrepareFreeMT(RetrySBHBSetFeatureARCPass_Callback, RetrySBHBSetFeatureARCFail_Callback);
	}
	RetrySBSelecMTFPUTrigger(uwFpu_offset, (U32)( (gSBTask.ulFSA_Align) + (gSBTask.ubCurr_frm_idx) ), 0, ubMTIndex, SWITCH_CLK__LOW_CLK_MODE);
	if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
		gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
	}
	else {
		gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
		gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
	}
}
#if (RETRY_SB_ORIGIN_EN)
void RetrySBReadFlow(void)
{
	switch (gSBTask.ubSubstate) {
	case RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_INIT:
		gSBTask.ubdecode_mode = 2;
		M_FIP_SET_LDPC_CONFIG_PAGE_SELECT(gSBTask.ubdecode_mode);
		gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_SET_ARC_CALIBRATION_PERSISTENCE;
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_SET_FEATURE;

	/* no break */
	case RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_SET_ARC_CALIBRATION_PERSISTENCE:
		if (TRUE == gSBTask.ubSBReadDecodeWithARC) {
			RetrySBHBSetFeatureARC(FALSE, TRUE);
		}
		else {
			RetrySBHBSetFeatureARC(FALSE, FALSE);
		}
		break;
	case RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_HB:
		RetrySBReadHB(  gSBTask.ubPageType, TRUE, (U32)( (gSBTask.ulFSA_Align) + (gSBTask.ubCurr_frm_idx) ), gSBTask.ubCurrentLDPCFrameIdx, 1, 0, 0);
		break;
	case RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_SB1:
		RetrySBReadSB1(   gSBTask.ubPageType, (U8 *)delta_table, gSBTask.ubCurrentLDPCFrameIdx);
		break;
	case RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_SB2:
		RetrySBReadSB2(  gSBTask.ubPageType, (U8 *)delta_table, gSBTask.ubCurrentLDPCFrameIdx);
		break;
	case RETRY_SB_SUBSTATE_SB_READ_WITH_OPTIMAL_READ_LEVEL_IDLE_OR_WAIT:
		break;
	}
}
#endif /*(RETRY_SB_ORIGIN_EN)*/
void RetrySBTurboRainInit(void)
{
	DMACParam_t DMACParam;
	U8 ubch;
	gSBTask.ubPageType = gTurboRainTask.ubPageType;
	gSBTask.ulFSA_Align = ( gpRetry->RetryJobList[gpRetry->ubHead].pErrorMT->dma.uliFSA0_1 >> gPCARule_Entry.ubBit_No) << gPCARule_Entry.ubBit_No;
	gSBTask.ubCurr_frm_idx = gTurboRainTask.ubCurr_frm_idx;
	gSBTask.ubCurrentLDPCFrameIdx = gTurboRainTask.ubCurrentLDPCFrameIdx;
	ubch = (gSBTask.ulFSA_Align >> gpOtherInfo->pRuleSet->pChannel->ubShift[0]) & gpOtherInfo->pRuleSet->pChannel->ulMask;
	gSBTask.ubChannel = ubch;
#if (RETRY_TURBORAIN_DEBUG)
	UartPrintf("\n[Create_SB_Task]Frame:%d, LDPCFrame:%d", gSBTask.ubCurr_frm_idx, gSBTask.ubCurrentLDPCFrameIdx);
#endif

	//gSBTask.ubIsFromTurboRain = 1;
	gSBTask.ubCurrentStage = RETRY_SB_MICRON_STAGE_6;
	gSBTask.ubCurrentStep = RETRY_SB_MICRON_STEP_MIN1SW_PRFIX_RR_CR_4b;

	memcpy((void *)&gSBTask.MTTemplate, (void *)gpRetry->RetryJobList[gpRetry->ubHead].pErrorMT, MT_SIZE);
	gSBTask.ubPrevState = RETRY_SB_STATE_INIT;
	gSBTask.ubState = RETRY_SB_STATE_SB_READ;
	gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_READ_INIT;
	////////Input ExtS to SB temp Buffer
	if (gSBTask.ubCurrentLDPCFrameIdx == 0) {
		DMACParam.ulSourceAddr = gTurboRainTask.ulbackup_addr;
		DMACParam.ulDestAddr = (gSBTask.ultmp_addr + BC_4KB * 2 + BC_2KB);
		DMACParam.ul32ByteNum = SIZE_IN_32B(BC_2KB);
		gpRetry->ubWaitDMACDoneFlag = TRUE;

		DMACCopy(DMAC_MODE_NORMAL_QUEUE, &DMACParam, gulReadRetryDMACDone_CallBack, 0);
		while (gpRetry->ubWaitDMACDoneFlag == TRUE) {
			DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
		}

		//copy spr 0
		memcpy((U32 *)(gSBTask.ultmp_SpareAddr + 2 * BC_16B + BC_8B), (U32 *)(gTurboRainTask.ulSpareAddr), BC_8B );
#if (RETRY_TURBORAIN_DEBUG)
		/*UartPrintf("\n[SB5]Data 0 :%L", *((U64 *)(gSBTask.ultmp_addr + BC_4KB * 2 + BC_2KB)));
		UartPrintf("\n[SB5]Spare 0 :%L", *((U64 *)(gSBTask.ultmp_SpareAddr + 2 * BC_16B + BC_8B)));*/
#endif
	}
	else {
		DMACParam.ulSourceAddr = (gTurboRainTask.ulbackup_addr + BC_2KB);
		DMACParam.ulDestAddr = (gSBTask.ultmp_addr + BC_4KB * 2 + BC_2KB);
		DMACParam.ul32ByteNum = SIZE_IN_32B(BC_2KB);
		gpRetry->ubWaitDMACDoneFlag = TRUE;

		DMACCopy(DMAC_MODE_NORMAL_QUEUE, &DMACParam, gulReadRetryDMACDone_CallBack, 0);
		while (gpRetry->ubWaitDMACDoneFlag == TRUE) {
			DMACDelegateCmd(DMAC_MODE_NORMAL_QUEUE);
		}

		// copy spr 1
		memcpy((U32 *)(gSBTask.ultmp_SpareAddr + 2 * BC_16B + BC_8B), (U32 *)(gTurboRainTask.ulSpareAddr + BC_8B), BC_8B );
	}
}
void RetrySBReadFlowNew(void)
{
	switch (gSBTask.ubSubstate) {
	case RETRY_SB_SUBSTATE_SB_READ_INIT:
		gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_READ_HB;
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_HB_READ_INIT;

		if (M_RETRY_SB_CHECK_IF_SLC_MLC_PAGETYPES()) {
			switch (gSBTask.ubCurrentStage) {
			case RETRY_SB_MICRON_STAGE_4:
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_READ_SET_ARC_CALIBRATION_PERSISTENCE;
				gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_SET_FEATURE;
				gSBTask.ubSBReadDecodeWithARC = TRUE;

				switch (gSBTask.ubCurrentStep) {
				case RETRY_SB_MICRON_SLC_MLC_STEP_PREFIX_RR_ARC2_SBSBR1:
					gSBTask.ubSBRead1H1Sor1H2S = RETRY_MICRON_TABLR_PARAMETER_1H1S;
					gSBTask.ubdecode_mode = 1;
					break;
				case RETRY_SB_MICRON_SLC_MLC_STEP_PREFIX_RR_ARC2_SBSBR2:
					gSBTask.ubSBRead1H1Sor1H2S = RETRY_MICRON_TABLR_PARAMETER_1H2S;
					gSBTask.ubdecode_mode = 2;
					break;

				default:
					M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
					break;
				}
				break;
			default:
				M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
				break;
			}
		}
		else {
			switch (gSBTask.ubCurrentStage) {
			case RETRY_SB_MICRON_STAGE_6:
				switch (gSBTask.ubCurrentStep) {
				case RETRY_SB_MICRON_STEP_MIN1SW_PRFIX_RR_SBSBR1_CR_2b:
					gSBTask.ubCRSetting = RETRY_SB_2BIT_CORRECTIVE_READ;
					gSBTask.ubSBRead1H1Sor1H2S = RETRY_MICRON_TABLR_PARAMETER_1H1S;
					gSBTask.ubdecode_mode = 1;
					break;
				case RETRY_SB_MICRON_STEP_MIN1SW_PRFIX_RR_SBSBR1_CR_4b:
					gSBTask.ubCRSetting = RETRY_SB_4BIT_CORRECTIVE_READ;
					gSBTask.ubSBRead1H1Sor1H2S = RETRY_MICRON_TABLR_PARAMETER_1H1S;
					gSBTask.ubdecode_mode = 1;
					break;
				case RETRY_SB_MICRON_STEP_MIN1SW_PRFIX_RR_SBSBR2_CR_4b:
					gSBTask.ubCRSetting = RETRY_SB_4BIT_CORRECTIVE_READ;
					gSBTask.ubSBRead1H1Sor1H2S = RETRY_MICRON_TABLR_PARAMETER_1H2S;
					gSBTask.ubdecode_mode = 2;
					break;
				case RETRY_SB_MICRON_STEP_MIN1SW_PRFIX_RR_CR_4b: // only turbo rain
					gSBTask.ubCRSetting = RETRY_SB_4BIT_CORRECTIVE_READ;
					gSBTask.ubSBRead1H1Sor1H2S = RETRY_MICRON_TABLR_PARAMETER_1H2S;
					break;

				default:
					M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
					break;
				}
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_RAW_READ_SET_7TH_ADDRESS;
				break;
			default:
				M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
				break;
			}
		}
		break;

	/****Pre-conditional Settings---->>>>****/

	case RETRY_SB_SUBSTATE_SB_RAW_READ_SET_7TH_ADDRESS:
		RetrySBSet7thAddress(gSBTask.ubCurrentStep);

		if (gSBTask.ubCurrentStep == RETRY_SB_MICRON_STEP_MIN1SW_PRFIX_RR_SBSBR1_CR_2b) {
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_READ_SET_CR_2B_4B_ENHANCE_CR;
			gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SETTING_CORRECTIVE_READ_SET_FEATURE;
		}
		else if ((gSBTask.ubCurrentStep == RETRY_SB_MICRON_STEP_MIN1SW_PRFIX_RR_SBSBR1_CR_4b) || (gSBTask.ubCurrentStep == RETRY_SB_MICRON_STEP_MIN1SW_PRFIX_RR_SBSBR2_CR_4b)) {
			// 6-1 set CR already
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_READ_SB1;
			gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SB_READ_SB1_INIT;
		}
		else if (gSBTask.ubCurrentStep == RETRY_SB_MICRON_STEP_MIN1SW_PRFIX_RR_CR_4b) { // only turbo rain
			gSBTask.ubSubstate  = RETRY_SB_SUBSTATE_SB_READ_SET_CR_2B_4B_ENHANCE_CR;
			gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SETTING_CORRECTIVE_READ_SET_FEATURE;
		}
		break;
	case RETRY_SB_SUBSTATE_SB_READ_SET_ARC_CALIBRATION_PERSISTENCE:
		if (TRUE == gSBTask.ubSBReadDecodeWithARC) {
			RetrySBHBSetFeatureARC(FALSE, TRUE);
		}
		else {
			RetrySBHBSetFeatureARC(FALSE, FALSE);
		}
		break;

	case RETRY_SB_SUBSTATE_SB_READ_SET_CR_2B_4B_ENHANCE_CR:
		if ((gSBTask.ubThirdstate == RETRY_SB_3RD_STATE_SETTING_CORRECTIVE_READ_SET_FEATURE) && gSBTask.ubIsFromTurboRain && gSBTask.ubIsEnhance) {
			RetrySBSetEnhanceCRTrim();
		}
		RetrySBSetCR(gSBTask.ubCRSetting, gSBTask.ubIsEnhance);
		break;

	/****<<<<----Pre-conditional Settings****/

	case RETRY_SB_SUBSTATE_SB_READ_HB:
		RetrySBReadHB(  gSBTask.ubPageType, TRUE, (U32)( (gSBTask.ulFSA_Align) + (gSBTask.ubCurr_frm_idx) ), gSBTask.ubCurrentLDPCFrameIdx, 1, 0, 0);
		break;
	case RETRY_SB_SUBSTATE_SB_READ_SB1:
		RetrySBReadSB1(   gSBTask.ubPageType, (U8 *)delta_table, gSBTask.ubCurrentLDPCFrameIdx);
		break;
	case RETRY_SB_SUBSTATE_SB_READ_SB2:
		RetrySBReadSB2(  gSBTask.ubPageType, (U8 *)delta_table, gSBTask.ubCurrentLDPCFrameIdx);
		break;
	case RETRY_SB_SUBSTATE_SB_READ_IDLE_OR_WAIT:
		break;
	}
}
#if (RETRY_SB_ORIGIN_EN)
void RetrySBDecodeFlow (void)
{
	U8 ubMTIndex;

	switch (gSBTask.ubSubstate) {
	case RETRY_SB_SUBSTATE_SB_DECODE_WITH_DEFAULT_LLR_INIT:
		gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_DECODE_WITH_DEFAULT_LLR_SB_CORR;
	/* no break */
	case RETRY_SB_SUBSTATE_SB_DECODE_WITH_DEFAULT_LLR_SB_CORR:
		if (NCS_EN && gubNeedSendNCSFlag) {
			ReadRetryDumpHWSetting(gSBTask.ubDSPEnginePageType, 0xD2, gSBTask.ubdecode_mode, 0, 0);
		}
		// fpu SB correct of 2K
		ubMTIndex = RetrySBPrepareFreeMT(RetrySBTriggerCorrectPass_Callback, RetrySBTriggerCorrectFail_Callback);
		RetrySBCorrectMTTrigger( 0, 0, gSBTask.ubCurrentLDPCFrameIdx, 0, 0, gSBTask.ubdecode_mode, 1, 0, 1, ubMTIndex);
		if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
			gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
		}
		else {
			gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
			gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
		}
		break;
	}
}
#endif /*(RETRY_SB_ORIGIN_EN)*/
void RetrySBDecodeFlowNew(void)
{
	U8 ubMTIndex;

	switch (gSBTask.ubSubstate) {
	case RETRY_SB_SUBSTATE_SB_DECODE_INIT:
		gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_DECODE_SB_CORR;
	/* no break */
	case RETRY_SB_SUBSTATE_SB_DECODE_SB_CORR:
		if (NCS_EN && gubNeedSendNCSFlag) {
			ReadRetryDumpHWSetting(gSBTask.ubDSPEnginePageType, 0xD2, gSBTask.ubdecode_mode, 0, 0);
		}
		// fpu SB correct of 2K
		ubMTIndex = RetrySBPrepareFreeMT(RetrySBTriggerCorrectPass_Callback, RetrySBTriggerCorrectFail_Callback);
		RetrySBCorrectMTTrigger( 0, 0, gSBTask.ubCurrentLDPCFrameIdx, 0, 0, gSBTask.ubdecode_mode, 1, 0, 1, ubMTIndex);
		if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
			gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
		}
		else {
			gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
			gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
		}
		break;
	}
}

void RetrySBSwitchPostConditionThenDoBackupSBFlow(U8 ubBackUpedState, U8 ubBackUpedSubstate, U8 ubStageNeedtoClean, U8 ubStepNeedtoClean)
{
	gSBTask.ubState = RETRY_SB_STATE_POSTCONDITION_FOR_STEPS;
	gSBTask.ubBackUpedState = ubBackUpedState;
	gSBTask.ubBackUpedSubstate = ubBackUpedSubstate;
	gSBTask.ubPreviousStage = ubStageNeedtoClean;
	gSBTask.ubPreviousStep = ubStepNeedtoClean;
}

void RetrySBSwitchToSetDefaultFeatureThenDoBackupSBFlow(U8 ubBackUpedState, U8 ubBackUpedSubstate)
{
	gSBTask.ubState = RETRY_SB_STATE_SET_DEFAULT_FEATURE_AND_CHK;
	gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SET_DEFAULT_FEATURE_INIT;
	gSBTask.ubBackUpedState = ubBackUpedState;
	gSBTask.ubBackUpedSubstate = ubBackUpedSubstate;
}
#if (RETRY_SB_ORIGIN_EN)
U8 RetrySBHBReadwithARC(void)
{
	U8 ubSBCorrect = 0;
	U8 ubMTIndex;

	switch (gSBTask.ubSubstate) {
	case RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_INIT:
		gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_ENABLE_AUTO_READ_CALIBRATION;
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_SET_FEATURE;
		gSBTask.ubARCRoundCnt = 0;
		gSBTask.ubTempPageType = gSBTask.ubPageType;
	/* no break */

	case  RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_ENABLE_AUTO_READ_CALIBRATION:				// do 3 times ARC, First with Calibration ON Persistence OFF,  then Calibration ON Persistence ON
		if (0 == gSBTask.ubARCRoundCnt) {
			RetrySBHBSetFeatureARC(TRUE, FALSE);
		}
		else if (1 == gSBTask.ubARCRoundCnt) {		//From second round, Start using ARC_ON, Persistence_ON
			RetrySBHBSetFeatureARC(TRUE, TRUE);
		}
		else {	//After second round, no need to Set ARC Feature
			gSBTask.ubSubstate =  RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_HB_READ;
			gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_HB_READ_INIT;
		}
		break;

	case RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_HB_READ:
		RetrySBReadHB(  gSBTask.ubPageType, FALSE, (U32)( (gSBTask.ulFSA_Align) + (gSBTask.ubCurr_frm_idx) ), gSBTask.ubCurrentLDPCFrameIdx, 1, 0, 0);
		break;

	case  RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_HB_CORR_2K:

		if (NCS_EN) {
			ReadRetryDumpHWSetting(0, 0xD0, 0, 0, 0);
		}
		// fpu HB correct of 2K
		ubMTIndex = RetrySBPrepareFreeMT(RetrySBTriggerCorrectPass_Callback, RetrySBTriggerCorrectFail_Callback);
		RetrySBCorrectMTTrigger( 0, 0, gSBTask.ubCurrentLDPCFrameIdx, 0, 0, 0, 0, 0, 0, ubMTIndex);    // scale mode=0, decode mode=0
		if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
			gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
		}
		else {
			gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
			gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
		}
		break;
	}

	return ubSBCorrect;
}
#endif /*(RETRY_SB_ORIGIN_EN)*/
void RetrySBHBRAWReadSBCDecode(void)
{
	U8 ubMTIndex;

	switch (gSBTask.ubSubstate) {
	case RETRY_SB_SUBSTATE_HB_RAW_READ_INIT:
		gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_RAW_READ;
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_HB_READ_INIT;
		gSBTask.ubTempPageType = gSBTask.ubPageType;

		if (M_RETRY_SB_CHECK_IF_SLC_MLC_PAGETYPES()) {
			switch (gSBTask.ubCurrentStage) {
			case RETRY_SB_MICRON_STAGE_3:
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_RAW_READ_SET_7TH_ADDRESS;
				break;
			default:
				M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
				break;
			}
		}
		else {
			switch (gSBTask.ubCurrentStage) {
			case RETRY_SB_MICRON_STAGE_1:
			case RETRY_SB_MICRON_STAGE_2:
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_RAW_READ_SET_7TH_ADDRESS;
				break;
			case RETRY_SB_MICRON_STAGE_6:
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_RAW_READ_SET_4b_CR_MIN1SW_READ_OFFSET;
				gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SETTING_CORRECTIVE_READ_SET_FEATURE;
				gSBTask.ubCRSetting = RETRY_SB_4BIT_CORRECTIVE_READ;
				break;
			default:
				M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
				break;
			}
		}

		break;

	/****Pre-conditional Settings---->>>>****/

	case  RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_ENABLE_AUTO_READ_CALIBRATION:				// do 3 times ARC, First with Calibration ON Persistence OFF,  then Calibration ON Persistence ON
		if (gSBTask.ubCurrentStep == RETRY_SB_MICRON_SLC_MLC_STEP_MIN1SW_PRFIX_RR_ARC1) {
			RetrySBHBSetFeatureARC(TRUE, FALSE);
		}
		else if (gSBTask.ubCurrentStep == RETRY_SB_MICRON_SLC_MLC_STEP_MIN1SW_PRFIX_RR_ARC3_0) {		//From second round, Start using ARC_ON, Persistence_ON
			RetrySBHBSetFeatureARC(TRUE, TRUE);
		}
		else {	//After second round, no need to Set ARC Feature
			gSBTask.ubSubstate =  RETRY_SB_SUBSTATE_HB_RAW_READ;
			gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_HB_READ_INIT;
		}
		break;

	case RETRY_SB_SUBSTATE_HB_RAW_READ_SET_7TH_ADDRESS:
		RetrySBSet7thAddress(gSBTask.ubCurrentStep);
		if (INVALID_INDEX == gSBTask.ub7thAddr) {
			// Invalid CBC, Skip Step
			RetrySBUpdateStageStep();
			RetrySBSwitchStatebyStageStep(TRUE, gSBTask.ubCurrentStage, gSBTask.ubCurrentStep);

			if ((RETRY_SB_MICRON_STEP_EXPRESS_SBSBR_2_2 == gSBTask.ubCurrentStep) || (RETRY_SB_MICRON_STEP_EXPRESS_SBSBR_2_4 == gSBTask.ubCurrentStep)) {
				//2.1 & 2.3 is Invalid, So 2.2 & 2.4 can be skipped
				RetrySBUpdateStageStep();
				RetrySBSwitchStatebyStageStep(TRUE, gSBTask.ubCurrentStage, gSBTask.ubCurrentStep);
			}
		}
		else {
			if (M_RETRY_SB_CHECK_IF_SLC_MLC_PAGETYPES()) {
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_ENABLE_AUTO_READ_CALIBRATION;
				gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SETTING_AUTO_READ_CALIBRATION_SET_FEATURE;
			}
			else {
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_RAW_READ;
				gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_HB_READ_INIT;
			}
		}
		break;

	case RETRY_SB_SUBSTATE_HB_RAW_READ_SET_4b_CR_MIN1SW_READ_OFFSET:
		RetrySBSetCR(gSBTask.ubCRSetting, gSBTask.ubIsEnhance);
		break;

	/****<<<<----Pre-conditional Settings****/

	case RETRY_SB_SUBSTATE_HB_RAW_READ:
		if ((gSBTask.ubSBSBRSecondRound) || (gSBTask.ubCurrentStep == RETRY_SB_MICRON_STEP_EXPRESS_SBSBR_2_2) || (gSBTask.ubCurrentStep == RETRY_SB_MICRON_STEP_EXPRESS_SBSBR_2_4) ) {
			RetrySBReadHB(  gSBTask.ubPageType, FALSE, (U32)( (gSBTask.ulFSA_Align) + (gSBTask.ubCurr_frm_idx) ), gSBTask.ubCurrentLDPCFrameIdx, 1, 0, 1);
		}
		else {
			RetrySBReadHB(  gSBTask.ubPageType, FALSE, (U32)( (gSBTask.ulFSA_Align) + (gSBTask.ubCurr_frm_idx) ), gSBTask.ubCurrentLDPCFrameIdx, 1, 0, 0);
		}
		break;

	case RETRY_SB_SUBSTATE_HB_RAW_READ_SBC_HB_CORR_2K:
		if (NCS_EN) {
			ReadRetryDumpHWSetting(0, 0xD0, 0, 0, 0);
		}
		// fpu HB correct of 2K
		ubMTIndex = RetrySBPrepareFreeMT(RetrySBTriggerCorrectPass_Callback, RetrySBTriggerCorrectFail_Callback);
		if ((gSBTask.ubSBSBRSecondRound) || (gSBTask.ubCurrentStep == RETRY_SB_MICRON_STEP_EXPRESS_SBSBR_2_2) || (gSBTask.ubCurrentStep == RETRY_SB_MICRON_STEP_EXPRESS_SBSBR_2_4) ) {
			gSBTask.ubSBSBRSecondRound = 0;
			RetrySBFlashSetECCParam(&sb_llr_table[RETRY_MICRON_TABLR_PARAMETER_1H1S][0], DEFAULT_LLR_TABLE_LENGTH, LLR_TABLE0_LOAD_BIT, NORMAL_MODE);
			RetrySBCorrectMTTrigger( 0, 0, gSBTask.ubCurrentLDPCFrameIdx, 0, 0, 1, 1, 0, 1, ubMTIndex);    // 1H1S
		}
		else {
			RetrySBCorrectMTTrigger( 0, 0, gSBTask.ubCurrentLDPCFrameIdx, 0, 0, 0, 0, 0, 0, ubMTIndex);    // scale mode=0, decode mode=0
		}
		if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
			gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
		}
		else {
			gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
			gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
		}
		break;
	}

	return;
}
void RetrySBClearRROffset()
{
	U16 *puwfpu_ptr = NULL;
	U16 uwFpu_offset;
	U8 ubMTIndex = 0;
	U8 ubi = 0;
	U8 ubFPUData = 1;

	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_CLEAR_RR_OFFSET_SET_FEATURE:
		//Micron SB Todo
		//Micron Set feature
		uwFpu_offset = FPU_PTR_OFFSET(fpu_set_feature);		//set_feature
		puwfpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);
		/// reset fpu entry
		for (ubi = 0 ; ubi < SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH ; ubi++) {
			if (0 == ubi) {
				puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (ubi << 1)] = FPU_DAT_W_FIR_D(0x00);
			}
			else if ((SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH - 1) == ubi) {
				puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (ubi << 1)] = FPU_DAT_W_LAST_D(0x00);
			}
			else {
				puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (ubi << 1)] = FPU_DAT_W_MID_D(0x00);
			}
			puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (ubi << 1) + 1] = FPU_DAT_W(0x00);
		}
		puwfpu_ptr[0] = FPU_CMD(0xD5);
		puwfpu_ptr[1] = FPU_ADR_1B(gSBTask.ublun);
		puwfpu_ptr[2] = FPU_ADR_1B(0x86);				//FA 96h for ARC feature
		if (FIP_FLASH_CLOCK_400MHZ == gFlhEnv.ubTargetFlashClock) {
			// flh clk 400
			puwfpu_ptr[3] = FPU_DLY(0x15);		// Decimal 21
		}
		else if (FIP_FLASH_CLOCK_333MHZ == gFlhEnv.ubTargetFlashClock) {
			// flh clk 333
			puwfpu_ptr[3] = FPU_DLY(0xF);		// Decimal 15
		}
		else {
			puwfpu_ptr[3] = FPU_DLY(0x10);		// Decimal 16
		}

		puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P1] = FPU_DAT_W_FIR_D(ubFPUData);
		puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P1 + 1] = FPU_DAT_W(ubFPUData);
		puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_END_IDX] = FPU_END;

		break;
	}
	if (NES_EN || NCS_EN) {
		ubMTIndex = RetrySBPrepareFreeMT(RetrySBClearRROffsetPass_Callback, RetrySBClearRROffsetPass_Callback);
	}
	else {
		ubMTIndex = RetrySBPrepareFreeMT(RetrySBClearRROffsetPass_Callback, RetrySBClearRROffsetFail_Callback);
	}

	RetrySBSelecMTFPUTrigger(uwFpu_offset, (U32)( (gSBTask.ulFSA_Align) + (gSBTask.ubCurr_frm_idx) ), 0, ubMTIndex, SWITCH_CLK__LOW_CLK_MODE);
	if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
		gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
	}
	else {
		gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
		gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
	}
}
void RetrySBSetCR(U8 ubIsCR, U8 ubIsEnhanced)
{

	U16 *puwfpu_ptr = NULL;
	U16 uwFpu_offset;
	U8 ubMTIndex = 0;
	U8 ubi = 0;
	U8 ubFPUData = ((ubIsEnhanced << 2) + ubIsCR);

	switch (gSBTask.ubThirdstate) {
	case RETRY_SB_3RD_STATE_SETTING_CORRECTIVE_READ_SET_FEATURE:
		//Micron SB Todo
		//Micron Set feature
		uwFpu_offset = FPU_PTR_OFFSET(fpu_set_feature);		//set_feature
		puwfpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);
		/// reset fpu entry
		for (ubi = 0 ; ubi < SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH ; ubi++) {
			if (0 == ubi) {
				puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (ubi << 1)] = FPU_DAT_W_FIR_D(0x00);
			}
			else if ((SB_MICRON_SET_FEATURE_FPU_DATA_LENGTH - 1) == ubi) {
				puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (ubi << 1)] = FPU_DAT_W_LAST_D(0x00);
			}
			else {
				puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (ubi << 1)] = FPU_DAT_W_MID_D(0x00);
			}
			puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_START_IDX + (ubi << 1) + 1] = FPU_DAT_W(0x00);
		}
		puwfpu_ptr[0] = FPU_CMD(0xD5);
		puwfpu_ptr[1] = FPU_ADR_1B(gSBTask.ublun);
		puwfpu_ptr[2] = FPU_ADR_1B(0x93);				//FA 96h for ARC feature
		if (FIP_FLASH_CLOCK_400MHZ == gFlhEnv.ubTargetFlashClock) {
			// flh clk 400
			puwfpu_ptr[3] = FPU_DLY(0x15);		// Decimal 21
		}
		else if (FIP_FLASH_CLOCK_333MHZ == gFlhEnv.ubTargetFlashClock) {
			// flh clk 333
			puwfpu_ptr[3] = FPU_DLY(0xF);		// Decimal 15
		}
		else {
			puwfpu_ptr[3] = FPU_DLY(0x10);		// Decimal 16
		}

		puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P1] = FPU_DAT_W_FIR_D(ubFPUData);
		puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_DATA_P1 + 1] = FPU_DAT_W(ubFPUData);
		puwfpu_ptr[SB_MICRON_SET_FEATURE_FPU_END_IDX] = FPU_END;

		break;
	case RETRY_SB_3RD_STATE_SETTING_CORRECTIVE_READ_GET_FEATURE:
		uwFpu_offset = FPU_PTR_OFFSET(fpu_get_feature);		//get_feature
		puwfpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);

		puwfpu_ptr[0] = FPU_CMD(0xD4);
		puwfpu_ptr[1] = FPU_ADR_1B(gSBTask.ublun);
		puwfpu_ptr[2] = FPU_ADR_1B(0x93);

		puwfpu_ptr[SB_MICRON_GET_FEATURE_FPU_END_IDX] = FPU_END;

		break;
	case RETRY_SB_3RD_STATE_SETTING_CORRECTIVE_READ_CHECK_FEATURE:
		uwFpu_offset = FPU_PTR_OFFSET(fpu_read_and_compare_feature_data);		//set_feature
		puwfpu_ptr = (U16 *)(IRAM_BASE + uwFpu_offset);

		puwfpu_ptr[0] = FPU_CMD(0x00);
		puwfpu_ptr[1] = FPU_DLY(0x10);

		//Clear <P1><P2><P3><P3> Compare mask
		for (ubi = 0; ubi < PARAMETER_NUM_PER_FPU; ubi++) {
			puwfpu_ptr[SB_MICRON_CHECK_FEATURE_FPU_START_IDX + (ubi << 1)] =  FPU_DAT_R_CMP(0x00);
			puwfpu_ptr[(SB_MICRON_CHECK_FEATURE_FPU_START_IDX + 1) + (ubi << 1)] = FPU_DAT_R_MASK(0x00);
		}
		//Compare <P1>
		puwfpu_ptr[SB_MICRON_CHECK_FEATURE_FPU_START_IDX + (SB_MICRON_FEATURE_PARAM_P1_INDEX << 1)] =  FPU_DAT_R_CMP(ubFPUData);
		puwfpu_ptr[(SB_MICRON_CHECK_FEATURE_FPU_START_IDX + 1) + (SB_MICRON_FEATURE_PARAM_P1_INDEX << 1)] = FPU_DAT_R_MASK(0xFF);

		break;
	}
	if (NES_EN || NCS_EN) {
		ubMTIndex = RetrySBPrepareFreeMT(RetrySBHBSetFeatureCRPass_Callback, RetrySBHBSetFeatureCRPass_Callback);
	}
	else {
		ubMTIndex = RetrySBPrepareFreeMT(RetrySBHBSetFeatureCRPass_Callback, RetrySBHBSetFeatureCRFail_Callback);
	}

	RetrySBSelecMTFPUTrigger(uwFpu_offset, (U32)( (gSBTask.ulFSA_Align) + (gSBTask.ubCurr_frm_idx) ), 0, ubMTIndex, SWITCH_CLK__LOW_CLK_MODE);
	if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
		gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
	}
	else {
		gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
		gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
	}
}
void RetrySBGetCBC()
{
	gSBTask.ubCBCValue = (U8)(FIPValleyTrackReadOut(gSBTask.ulFSA_ori, FIP_VALLEY_TRACK_READOUT_MODE_CBC));
}

void RetrySBSet7thAddress(U8 ubStep)
{
	if (M_RETRY_SB_CHECK_IF_SLC_MLC_PAGETYPES()) {
		gSBTask.ub7thAddr = 0x00;
	}
	else {
		switch (ubStep) {
		case RETRY_SB_MICRON_STEP_NORMAL_READ:
			gSBTask.ub7thAddr = 0x10;
			break;
		case RETRY_SB_MICRON_STEP_PERSISTENT_FINE_CALIB:
			gSBTask.ub7thAddr = 0x20;
			break;
		case RETRY_SB_MICRON_STEP_EXPRESS_SBSBR_1_2:
			if (gSBTask.ubSBSBRSecondRound) {
				gSBTask.ub7thAddr = 0x50;
			}
			else {
				gSBTask.ub7thAddr = 0x20;
			}
			break;
		case RETRY_SB_MICRON_STEP_EXPRESS_SBSBR_2_2:
		case RETRY_SB_MICRON_STEP_EXPRESS_SBSBR_2_4:
			gSBTask.ub7thAddr = 0x50;// need check : Frey2_N48R_Read_Error_Recovery_rev0.6.pdf = 0x50 , but Frey2_N48_feature - phison_20200507 = 0x40
			break;
		case RETRY_SB_MICRON_STEP_CURCBC_P1_FINE_CALIB:
			if (gSBTask.ubCBCValue == 7) {
				gSBTask.ub7thAddr = INVALID_INDEX;
			}
			else {
				gSBTask.ub7thAddr = (0x88 | (gSBTask.ubCBCValue + 1));
			}
			break;
		case RETRY_SB_MICRON_STEP_CURCBC_N1_FINE_CALIB:
			if (gSBTask.ubCBCValue == 0) {
				gSBTask.ub7thAddr = INVALID_INDEX;
			}
			else {
				gSBTask.ub7thAddr = (0x88 | (gSBTask.ubCBCValue - 1));
			}
			break;
		case RETRY_SB_MICRON_STEP_CURCBC_P2_FINE_CALIB:
			if (gSBTask.ubCBCValue >= 6) {
				gSBTask.ub7thAddr = INVALID_INDEX;
			}
			else {
				gSBTask.ub7thAddr = (0x88 | (gSBTask.ubCBCValue + 2));
			}
			break;
		case RETRY_SB_MICRON_STEP_CURCBC_N2_FINE_CALIB:
			if (gSBTask.ubCBCValue <= 1) {
				gSBTask.ub7thAddr = INVALID_INDEX;
			}
			else {
				gSBTask.ub7thAddr = (0x88 | (gSBTask.ubCBCValue - 2));
			}
			break;
		case RETRY_SB_MICRON_STEP_CURCBC_P3_FINE_CALIB:
			if (gSBTask.ubCBCValue >= 5) {
				gSBTask.ub7thAddr = INVALID_INDEX;
			}
			else {
				gSBTask.ub7thAddr = (0x88 | (gSBTask.ubCBCValue + 3));
			}
			break;

		//Stage3 Steps
		case RETRY_SB_MICRON_STEP_PREFIX_RR1_FINE_CALIB_PERSISTENT_FINE_CALIB:
		case RETRY_SB_MICRON_STEP_PREFIX_RR2_FINE_CALIB_PERSISTENT_FINE_CALIB:
		case RETRY_SB_MICRON_STEP_PREFIX_RR3_FINE_CALIB_PERSISTENT_FINE_CALIB:
		case RETRY_SB_MICRON_STEP_PREFIX_RR4_FINE_CALIB_PERSISTENT_FINE_CALIB:
		case RETRY_SB_MICRON_STEP_PREFIX_RR5_FINE_CALIB_PERSISTENT_FINE_CALIB:
		case RETRY_SB_MICRON_STEP_PREFIX_RR6_FINE_CALIB_PERSISTENT_FINE_CALIB:
		case RETRY_SB_MICRON_STEP_PREFIX_RR7_FINE_CALIB_PERSISTENT_FINE_CALIB:
		case RETRY_SB_MICRON_STEP_PREFIX_RR8_FINE_CALIB_PERSISTENT_FINE_CALIB:
		//Stage5 Steps
		case RETRY_SB_MICRON_STEP_MIN1SW_PRFIX_RR_FINE_CALIB_PERSISTENT_FINE_CALIB:
		case RETRY_SB_MICRON_STEP_MIN2SW_PRFIX_RR_FINE_CALIB_PERSISTENT_FINE_CALIB:
		case RETRY_SB_MICRON_STEP_MIN3SW_PRFIX_RR_FINE_CALIB_PERSISTENT_FINE_CALIB:
			if (RETRY_SB_SUBSTATE_HB_4K_FRAME_READ_OP_ONLY == gSBTask.ubSubstate) {
				gSBTask.ub7thAddr = 0x00;		//Fine Calib
			}
			else {	//RETRY_SB_SUBSTATE_HB_4K_FRAME_READ_OP_DMA_DECODE == gSBTask.ubSubstate
				gSBTask.ub7thAddr = 0x20;		//Persistent Fin Calib
			}
			break;
		case RETRY_SB_MICRON_STEP_MIN1SW_PRFIX_RR_CALIBRATED_OFFSET_CR_2B:
		case RETRY_SB_MICRON_STEP_MIN2SW_PRFIX_RR_CALIBRATED_OFFSET_CR_2B:
		case RETRY_SB_MICRON_STEP_MIN3SW_PRFIX_RR_CALIBRATED_OFFSET_CR_2B:
		case RETRY_SB_MICRON_STEP_MIN1SW_PRFIX_RR_SBSBR1_CR_2b:
		case RETRY_SB_MICRON_STEP_MIN1SW_PRFIX_RR_CR_4b:
		case RETRY_SB_MICRON_STEP_MIN1SW_PRFIX_RR_SBSBR1_CR_4b:
		case RETRY_SB_MICRON_STEP_MIN1SW_PRFIX_RR_SBSBR2_CR_4b:
			gSBTask.ub7thAddr = 0x60;
			break;
		default:
			break;
		}
	}
	return;
}

/*
void RetrySBSetFPUPrefixRRWithPrefixRRTable(U8 ubCurrnetPrefixRRIdx)
{
	U16 uwFPUOffset;
	U16 *puwFPU = NULL;
	U8 ubPrefixOffset;

		if (gSBTask.MTTemplate.dma.ALUSelect & ALU_RULE_SLC_BIT) { // BIT1 = SLC
			if (S17_EN) {
				gHBParamMgr.ubCurACRR = 0;
			}
			uwFPUOffset = FPU_PTR_SLC_RETRY_READ_ACRR_CMD(0);
			ubPrefixOffset = 1;
		}
		else {
			uwFPUOffset = FPU_PTR_TLC_RETRY_READ_ACRR_CMD(gHBParamMgr.ubCurACRR);
			ubPrefixOffset = 0;
		}

		puwFPU = (U16 *)(IRAM_BASE + uwFPUOffset);

		if (RETRY_SB_MICRON_SLC_PAGE == gSBTask.ubPageType) { //SLC A4 //SLC Unit, SLC WordLine
			puwFPU[1 + ubPrefixOffset] = FPU_ADR_1B(gubSBMicronPreFixRRTable[0][ubCurrnetPrefixRRIdx]);
			puwFPU[2 + ubPrefixOffset] = FPU_ADR_1B(0);
			puwFPU[3 + ubPrefixOffset] = FPU_ADR_1B(0);
			puwFPU[4 + ubPrefixOffset] = FPU_ADR_1B(0);
		// For Micron N48R not expect SLC Page in this Stage
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
		}
		else if (RETRY_SB_MICRON_MLC_LOWER_PAGE == gSBTask.ubPageType) { //MLC Lower A1
			puwFPU[1 + ubPrefixOffset] = FPU_ADR_1B(gubSBMicronPreFixRRTable[1][ubCurrnetPrefixRRIdx]);
			puwFPU[2 + ubPrefixOffset] = FPU_ADR_1B(0);
			puwFPU[3 + ubPrefixOffset] = FPU_ADR_1B(0);
			puwFPU[4 + ubPrefixOffset] = FPU_ADR_1B(0);
		// For Micron N48R not expect MLC Page in this Stage
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
		}
		else if (RETRY_SB_MICRON_MLC_UPPER_PAGE == gSBTask.ubPageType) { //MLC Upper A0 & A2
			puwFPU[1 + ubPrefixOffset] = FPU_ADR_1B(gubSBMicronPreFixRRTable[2][ubCurrnetPrefixRRIdx]);
			puwFPU[2 + ubPrefixOffset] = FPU_ADR_1B(gubSBMicronPreFixRRTable[3][ubCurrnetPrefixRRIdx]);
			puwFPU[3 + ubPrefixOffset] = FPU_ADR_1B(0);
			puwFPU[4 + ubPrefixOffset] = FPU_ADR_1B(0);
		// For Micron N48R not expect MLC Page in this Stage
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
		}
		else if (RETRY_SB_MICRON_QLC_LOWER_PAGE == gSBTask.ubPageType) {
			puwFPU[1 + ubPrefixOffset] = FPU_ADR_1B(gubSBMicronPreFixRRTable[4][ubCurrnetPrefixRRIdx]);
			puwFPU[2 + ubPrefixOffset] = FPU_ADR_1B(gubSBMicronPreFixRRTable[5][ubCurrnetPrefixRRIdx]);
			puwFPU[3 + ubPrefixOffset] = FPU_ADR_1B(gubSBMicronPreFixRRTable[6][ubCurrnetPrefixRRIdx]);
			puwFPU[4 + ubPrefixOffset] = FPU_ADR_1B(gubSBMicronPreFixRRTable[7][ubCurrnetPrefixRRIdx]);
		}
		else if (RETRY_SB_MICRON_QLC_UPPER_PAGE == gSBTask.ubPageType) {
			puwFPU[1 + ubPrefixOffset] = FPU_ADR_1B(gubSBMicronPreFixRRTable[8][ubCurrnetPrefixRRIdx]);
			puwFPU[2 + ubPrefixOffset] = FPU_ADR_1B(gubSBMicronPreFixRRTable[9][ubCurrnetPrefixRRIdx]);
			puwFPU[3 + ubPrefixOffset] = FPU_ADR_1B(gubSBMicronPreFixRRTable[10][ubCurrnetPrefixRRIdx]);
			puwFPU[4 + ubPrefixOffset] = FPU_ADR_1B(gubSBMicronPreFixRRTable[11][ubCurrnetPrefixRRIdx]);
		}
		else if (RETRY_SB_MICRON_QLC_EXTRA_PAGE == gSBTask.ubPageType) {
			puwFPU[1 + ubPrefixOffset] = FPU_ADR_1B(gubSBMicronPreFixRRTable[12][ubCurrnetPrefixRRIdx]);
			puwFPU[2 + ubPrefixOffset] = FPU_ADR_1B(gubSBMicronPreFixRRTable[13][ubCurrnetPrefixRRIdx]);
			puwFPU[3 + ubPrefixOffset] = FPU_ADR_1B(gubSBMicronPreFixRRTable[14][ubCurrnetPrefixRRIdx]);
			puwFPU[4 + ubPrefixOffset] = FPU_ADR_1B(0);
		}
		else {	//RETRY_SB_MICRON_QLC_TOP_PAGE
			puwFPU[1 + ubPrefixOffset] = FPU_ADR_1B(gubSBMicronPreFixRRTable[15][ubCurrnetPrefixRRIdx]);
			puwFPU[2 + ubPrefixOffset] = FPU_ADR_1B(gubSBMicronPreFixRRTable[16][ubCurrnetPrefixRRIdx]);
			puwFPU[3 + ubPrefixOffset] = FPU_ADR_1B(gubSBMicronPreFixRRTable[17][ubCurrnetPrefixRRIdx]);
			puwFPU[4 + ubPrefixOffset] = FPU_ADR_1B(gubSBMicronPreFixRRTable[18][ubCurrnetPrefixRRIdx]);
		}
}
*/
void RetrySBSetFPUPrefixRRWithPrefixRRTable(U8 ubCurrnetPrefixRRIdx, ReadOffset4Param_t Offset)
{
	if (RETRY_SB_MICRON_SLC_PAGE == gSBTask.ubPageType) { //SLC A4 //SLC Unit, SLC WordLine
		RetrySBSetFPUPrefixRR(
			gubSBMicronPreFixRRTable[0][ubCurrnetPrefixRRIdx],
			0,
			0,
			0);
		// For Micron N48R not expect SLC Page in this Stage
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
	}
	else if (RETRY_SB_MICRON_MLC_LOWER_PAGE == gSBTask.ubPageType) { //MLC Lower A1
		RetrySBSetFPUPrefixRR(
			gubSBMicronPreFixRRTable[1][ubCurrnetPrefixRRIdx],
			0,
			0,
			0);
		// For Micron N48R not expect MLC Page in this Stage
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
	}
	else if (RETRY_SB_MICRON_MLC_UPPER_PAGE == gSBTask.ubPageType) { //MLC Upper A0 & A2
		RetrySBSetFPUPrefixRR(
			gubSBMicronPreFixRRTable[2][ubCurrnetPrefixRRIdx],
			gubSBMicronPreFixRRTable[3][ubCurrnetPrefixRRIdx],
			0,
			0);
		// For Micron N48R not expect MLC Page in this Stage
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
	}
	else if (RETRY_SB_MICRON_QLC_LOWER_PAGE == gSBTask.ubPageType) {
		RetrySBSetFPUPrefixRR(
			gubSBMicronPreFixRRTable[4][ubCurrnetPrefixRRIdx] + Offset.ReadOffset4Param.Param.ubParam[0],
			gubSBMicronPreFixRRTable[5][ubCurrnetPrefixRRIdx] + Offset.ReadOffset4Param.Param.ubParam[1],
			gubSBMicronPreFixRRTable[6][ubCurrnetPrefixRRIdx] + Offset.ReadOffset4Param.Param.ubParam[2],
			gubSBMicronPreFixRRTable[7][ubCurrnetPrefixRRIdx] + Offset.ReadOffset4Param.Param.ubParam[3]);
	}
	else if (RETRY_SB_MICRON_QLC_UPPER_PAGE == gSBTask.ubPageType) {
		RetrySBSetFPUPrefixRR(
			gubSBMicronPreFixRRTable[8][ubCurrnetPrefixRRIdx] + Offset.ReadOffset4Param.Param.ubParam[0],
			gubSBMicronPreFixRRTable[9][ubCurrnetPrefixRRIdx] + Offset.ReadOffset4Param.Param.ubParam[1],
			gubSBMicronPreFixRRTable[10][ubCurrnetPrefixRRIdx] + Offset.ReadOffset4Param.Param.ubParam[2],
			gubSBMicronPreFixRRTable[11][ubCurrnetPrefixRRIdx] + Offset.ReadOffset4Param.Param.ubParam[3]);
	}
	else if (RETRY_SB_MICRON_QLC_EXTRA_PAGE == gSBTask.ubPageType) {
		RetrySBSetFPUPrefixRR(
			gubSBMicronPreFixRRTable[12][ubCurrnetPrefixRRIdx] + Offset.ReadOffset4Param.Param.ubParam[0],
			gubSBMicronPreFixRRTable[13][ubCurrnetPrefixRRIdx] + Offset.ReadOffset4Param.Param.ubParam[1],
			gubSBMicronPreFixRRTable[14][ubCurrnetPrefixRRIdx] + Offset.ReadOffset4Param.Param.ubParam[2],
			0);
	}
	else {	//RETRY_SB_MICRON_QLC_TOP_PAGE
		RetrySBSetFPUPrefixRR(
			gubSBMicronPreFixRRTable[15][ubCurrnetPrefixRRIdx] + Offset.ReadOffset4Param.Param.ubParam[0],
			gubSBMicronPreFixRRTable[16][ubCurrnetPrefixRRIdx] + Offset.ReadOffset4Param.Param.ubParam[1],
			gubSBMicronPreFixRRTable[17][ubCurrnetPrefixRRIdx] + Offset.ReadOffset4Param.Param.ubParam[2],
			gubSBMicronPreFixRRTable[18][ubCurrnetPrefixRRIdx] + Offset.ReadOffset4Param.Param.ubParam[3]);
	}
}

void RetrySBSetFPUPrefixRR(U8 ubPrefixRRParam0, U8 ubPrefixRRParam1, U8 ubPrefixRRParam2, U8 ubPrefixRRParam3)
{
	U16 uwFPUOffset;
	U16 *puwFPU = NULL;
	U8 ubPrefixOffset;

	if (gSBTask.MTTemplate.dma.ALUSelect & ALU_RULE_SLC_BIT) { // BIT1 = SLC
		uwFPUOffset = FPU_PTR_SLC_RETRY_READ_ACRR_CMD(0);
		ubPrefixOffset = 1;
	}
	else {
		uwFPUOffset = FPU_PTR_TLC_RETRY_READ_ACRR_CMD(gHBParamMgr.ubCurACRR);
		ubPrefixOffset = 0;
	}

	puwFPU = (U16 *)(IRAM_BASE + uwFPUOffset);

	puwFPU[1 + ubPrefixOffset] = FPU_ADR_1B(ubPrefixRRParam0);
	puwFPU[2 + ubPrefixOffset] = FPU_ADR_1B(ubPrefixRRParam1);
	puwFPU[3 + ubPrefixOffset] = FPU_ADR_1B(ubPrefixRRParam2);
	puwFPU[4 + ubPrefixOffset] = FPU_ADR_1B(ubPrefixRRParam3);

	return;
}



void RetrySBHB4KReadMTDecode()
{
	U8 ubMTIdx;

	U16 uwFPUOffset;
	U16 *puwFPU = NULL;
	U8 ubPrefixOffset;

	FlhMT_t MT;
	U8 ubCurrnetPrefixRRIdx = 0;

	L4KTable16B_t *L4kPtr = NULL;
	MTCfg_t uoMTCfg;
	RetryPCARuleSet_t *pRuleSet = gpOtherInfo->pRuleSet;
	U8 ubFrameIndex = 0;
	U8 ubDMAFrameNum = 0;
	U8 ubBufferValid = 0;

	switch (gSBTask.ubSubstate) {
	case RETRY_SB_SUBSTATE_HB_4K_FRAME_READ_INIT:
		gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_4K_FRAME_READ_SET_PREFIX_READ_RETRY;
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_NULL;
		gSBTask.ubTempPageType = gSBTask.ubPageType;

		if (M_RETRY_SB_CHECK_IF_SLC_MLC_PAGETYPES()) {
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
		}
		else {
			switch (gSBTask.ubCurrentStage) {
			case RETRY_SB_MICRON_STAGE_1:
			case RETRY_SB_MICRON_STAGE_4:
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_4K_FRAME_READ_SET_PREFIX_READ_RETRY;
				break;
			case RETRY_SB_MICRON_STAGE_5:
				gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_4K_FRAME_READ_SET_PREFIX_READ_RETRY_BY_MIN_SW;
				switch (gSBTask.ubCurrentStep) {
				case RETRY_SB_MICRON_STEP_MIN1SW_PRFIX_RR_FINE_CALIB_PERSISTENT_FINE_CALIB:
					gSBMinSWInfo.ubCurrentUsingMinSWIdx = 0;
					break;
				case RETRY_SB_MICRON_STEP_MIN2SW_PRFIX_RR_FINE_CALIB_PERSISTENT_FINE_CALIB:
					gSBMinSWInfo.ubCurrentUsingMinSWIdx = 1;
					break;
				case RETRY_SB_MICRON_STEP_MIN3SW_PRFIX_RR_FINE_CALIB_PERSISTENT_FINE_CALIB:
					gSBMinSWInfo.ubCurrentUsingMinSWIdx = 2;
					break;
				case RETRY_SB_MICRON_STEP_MIN1SW_PRFIX_RR_CALIBRATED_OFFSET_CR_2B:
				case RETRY_SB_MICRON_STEP_MIN2SW_PRFIX_RR_CALIBRATED_OFFSET_CR_2B:
				case RETRY_SB_MICRON_STEP_MIN3SW_PRFIX_RR_CALIBRATED_OFFSET_CR_2B:
					gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_4K_FRAME_READ_SET_PREFIX_READ_RETRY_BY_MIN_SW_VALLY_TRACK;
					break;


				default:
					break;
				}
				break;
			default:
				M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
				break;
			}
		}

		break;

	/****Pre-conditional Settings---->>>>****/

	case RETRY_SB_SUBSTATE_HB_4K_FRAME_READ_SET_PREFIX_READ_RETRY:

		if (gSBTask.ubCurrentStage == RETRY_SB_MICRON_STAGE_1) {
			ubCurrnetPrefixRRIdx = 0;
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_4K_FRAME_READ_OP_DMA_DECODE;
		}
		else {
			ubCurrnetPrefixRRIdx = M_RETRY_SB_GET_CURRENT_PRFIX_RR_IDX_SKIP_RR0();
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_4K_FRAME_READ_OP_ONLY;
		}
		ReadOffset4Param_t tmp = {};
		RetrySBSetFPUPrefixRRWithPrefixRRTable(ubCurrnetPrefixRRIdx, tmp);

		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_NULL;

		break;

	case RETRY_SB_SUBSTATE_HB_4K_FRAME_READ_SET_PREFIX_READ_RETRY_BY_MIN_SW:
		ubCurrnetPrefixRRIdx = M_RETRY_SB_STEP_TO_PRFIX_RR_IDX_SKIP_RR0(gSBMinSWInfo.ubMinSWStep[gSBMinSWInfo.ubCurrentUsingMinSWIdx]);
		RetrySBSetFPUPrefixRRWithPrefixRRTable(ubCurrnetPrefixRRIdx, gSBMinSWInfo.SWReadOffset[gSBMinSWInfo.ubCurrentUsingMinSWIdx]);

		gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_READ_SET_CR_2B_4B_ENHANCE_CR;
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SETTING_CORRECTIVE_READ_SET_FEATURE;

		break;

	case RETRY_SB_SUBSTATE_HB_4K_FRAME_READ_SET_PREFIX_READ_RETRY_BY_MIN_SW_VALLY_TRACK:
		ubCurrnetPrefixRRIdx = M_RETRY_SB_STEP_TO_PRFIX_RR_IDX_SKIP_RR0(gSBMinSWInfo.ubMinSWStep[gSBMinSWInfo.ubCurrentUsingMinSWIdx]);
		RetrySBSetFPUPrefixRRWithPrefixRRTable(ubCurrnetPrefixRRIdx, gRetrySBFineCalibReadOffset);

		gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_READ_SET_CR_2B_4B_ENHANCE_CR;
		gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_SETTING_CORRECTIVE_READ_SET_FEATURE;
		break;


	case RETRY_SB_SUBSTATE_HB_READ_SET_CR_2B_4B_ENHANCE_CR:
		// Step 5-1a no need to restore
		if ((RETRY_SB_MICRON_STEP_MIN2SW_PRFIX_RR_FINE_CALIB_PERSISTENT_FINE_CALIB == gSBTask.ubCurrentStep) || (RETRY_SB_MICRON_STEP_MIN3SW_PRFIX_RR_FINE_CALIB_PERSISTENT_FINE_CALIB == gSBTask.ubCurrentStep)) {
			RetrySBSetCR(RETRY_SB_NOT_CORRECTIVE_READ, FALSE);
		}
		else if ((RETRY_SB_MICRON_STEP_MIN1SW_PRFIX_RR_CALIBRATED_OFFSET_CR_2B == gSBTask.ubCurrentStep) || (RETRY_SB_MICRON_STEP_MIN2SW_PRFIX_RR_CALIBRATED_OFFSET_CR_2B == gSBTask.ubCurrentStep) || (RETRY_SB_MICRON_STEP_MIN3SW_PRFIX_RR_CALIBRATED_OFFSET_CR_2B == gSBTask.ubCurrentStep)) {
			RetrySBSetCR(RETRY_SB_2BIT_CORRECTIVE_READ, gSBTask.ubIsEnhance);
		}
		else if ((RETRY_SB_MICRON_STEP_MIN1SW_PRFIX_RR_FINE_CALIB_PERSISTENT_FINE_CALIB == gSBTask.ubCurrentStep)) {
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_4K_FRAME_READ_OP_ONLY;
			gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_NULL;
		}
		break;
	case RETRY_SB_SUBSTATE_HB_4K_FRAME_READ_OP_ONLY:

		if (gSBTask.MTTemplate.dma.ALUSelect & ALU_RULE_SLC_BIT) { // BIT1 = SLC
			uwFPUOffset = FPU_PTR_SLC_RETRY_READ_ACRR_CMD(0);
			ubPrefixOffset = 1;
		}
		else {
			uwFPUOffset = FPU_PTR_TLC_RETRY_READ_ACRR_CMD(gHBParamMgr.ubCurACRR);
			ubPrefixOffset = 0;
		}
		puwFPU = (U16 *)(IRAM_BASE + uwFPUOffset);

		//TODO N48R Decoding Flow 0707

		//Decide FineCalib/PersistentFineCalib
		RetrySBSet7thAddress(gSBTask.ubCurrentStep);
		if (INVALID_INDEX == gSBTask.ub7thAddr) {
			RetrySBUpdateStageStep();
			RetrySBSwitchStatebyStageStep(TRUE, gSBTask.ubCurrentStage, gSBTask.ubCurrentStep);
			break;
		}

#if (PS5013_EN)
		puwFPU[8] = VA_ADR_1B(gSBTask.ub7thAddr);
#endif

		ubMTIdx = RetrySBPrepareFreeMT(RetrySBHBReadCMDOnlyPass_Callback, RetrySBReadHBMTFail_Callback);
		RetrySBSelecMTFPUTrigger( uwFPUOffset, (U32)( (gSBTask.ulFSA_Align) + (gSBTask.ubCurr_frm_idx) ), 0, ubMTIdx, SWITCH_CLK__NORMAL_MODE);

		if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
			gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
		}
		else {
			gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
			gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
		}
		break;


	/****<<<<----Pre-conditional Settings****/

	case RETRY_SB_SUBSTATE_HB_4K_FRAME_READ_OP_DMA_DECODE:

		//RetrySBReadHB(  gSBTask.ubPageType, FALSE, (U32)( (gSBTask.ulFSA_Align) + (gSBTask.ubCurr_frm_idx) ), gSBTask.ubCurrentLDPCFrameIdx, 1, 0, 0);

		if (gSBTask.MTTemplate.dma.ALUSelect & ALU_RULE_SLC_BIT) { // BIT1 = SLC
			uwFPUOffset = FPU_PTR_SLC_RETRY_READ_ACRR_CMD(0);
			ubPrefixOffset = 1;
		}
		else {
			uwFPUOffset = FPU_PTR_TLC_RETRY_READ_ACRR_CMD(gHBParamMgr.ubCurACRR);
			ubPrefixOffset = 0;
		}
		puwFPU = (U16 *)(IRAM_BASE + uwFPUOffset);

		//TODO N48R Decoding Flow 0707

		//Decide FineCalib/PersistentFineCalib
		RetrySBSet7thAddress(gSBTask.ubCurrentStep);
		if (INVALID_INDEX == gSBTask.ub7thAddr) {
			RetrySBUpdateStageStep();
			RetrySBSwitchStatebyStageStep(TRUE, gSBTask.ubCurrentStage, gSBTask.ubCurrentStep);
			break;
		}

#if (PS5013_EN)
		puwFPU[8] = VA_ADR_1B(gSBTask.ub7thAddr);
#endif

		//TODO N48R Decoding Flow 0707

		ubMTIdx = RetrySBPrepareFreeMT(NULL, RetrySBReadHBMTFail_Callback);
		RetrySBSelecMTFPUTrigger( uwFPUOffset, (U32)( (gSBTask.ulFSA_Align) + (gSBTask.ubCurr_frm_idx) ), 0, ubMTIdx, SWITCH_CLK__NORMAL_MODE);

		//HB DMA MT Start
		/****************************************/

#if (RETRY_TURBORAIN_DEBUG)
		ubMTIdx = Retry_PrepareFreeMT(RetrySBReadHB4KDMAFail_Callback, RetrySBReadHB4KDMAFail_Callback);
#else
		ubMTIdx = Retry_PrepareFreeMT(RetrySBReadHB4KDMAPass_Callback, RetrySBReadHB4KDMAFail_Callback);
#endif
		gMTMgr.ubRecordExtInfoMT = ubMTIdx;
		memcpy((void *)&MT, (void *)&gSBTask.MTTemplate, MT_SIZE);

		MT.dma.btBufferMode = 0;
		MT.dma.btZipEn = FALSE;
		MT.dma.btConversionBypass = TRUE;
		MT.dma.btCRCCheckDis = TRUE;
		MT.dma.btLDPCCorrectEn = TRUE;
		MT.dma.btCompareEn = FALSE;
		MT.dma.btGC = TRUE;
#if (PS5017_EN)
		MT.dma.btFpuPCAEn = TRUE;
#endif /* (PS5017_EN) */
		MT.dma.btForce_R_Fail = FALSE;
		MT.dma.btMTPFormat = 1;
		MT.dma.NorTarCPU = MT_TARGET_CPU0;
		MT.dma.ErrTarCPU = MT_TARGET_CPU0;
		MT.dma.btBMUAllocateEn = FALSE;
		MT.dma.btDisableUDMA = TRUE;
		MT.dma.btForce_R_Fail = FALSE;

		MT.dma.uliFSA0_1 = (U32)( (gSBTask.ulFSA_Align) + (gSBTask.ubCurr_frm_idx) );

		for (ubFrameIndex = 0; ubFrameIndex < FRAMES_PER_PAGE; ubFrameIndex++) {
			// Fill retry buffer address, spare offset for GC backup, and buffer valid
			L4kPtr = (L4KTable16B_t *)(IRAM_BASE + SPR_RETRY_OFF + ubFrameIndex * L4K_SIZE);


			//if (gHBTask.ubHBRetryFrameMap & BIT(ubFrameIndex)) {
			if (ubFrameIndex == gSBTask.ubCurr_frm_idx) {
				// DMA remain error frame
				ubBufferValid = BIT_MASK(SECTORS_PER_4K);
				//L4kPtr->BitMap.Read.FW = gHBTask.ulSprOffset[ubFrameIndex];
				L4kPtr->BitMap.Read.FW = (TEMP_SPR_FOR_SB_RS_OFF + 64) + ubFrameIndex * L4K_SIZE;   //Using SB Retry GC Backup Retry IRAM address, Prevent 2nd LDPC_Frame overlap 1st LDPC_Frame Result
				ubDMAFrameNum++;
			}
			else {
				ubBufferValid = 0;
				/*
				if (ubDMAFrameNum && (gHBTask.ubHBRetryFrameMap >> ubFrameIndex)) {
					ubDMAFrameNum++;
				}
				*/
			}

			L4kPtr->BitMap.Read.ubBufferValid = ubBufferValid;
			if (ubBufferValid) {
				//L4kPtr->BitMap.Read.FW = gHBTask.ulSprOffset[ubFrameIndex];
				L4kPtr->BitMap.Read.FW = (TEMP_SPR_FOR_SB_RS_OFF + 64) + ubFrameIndex * L4K_SIZE;   //Using SB Retry GC Backup Retry IRAM address, Prevent 2nd LDPC_Frame overlap 1st LDPC_Frame Result
			}
			else {
				// No need to do DMA, here assign FW set unused IRAM offset, prevent pass spare data to be overwritte
#if (PS5017_EN)
				L4kPtr->BitMap.Read.FW = DBUF_GC_BACKUP_RETRY_SKIP_DMA_IDX + ubFrameIndex;
#else /* (PS5017_EN) */
				L4kPtr->BitMap.Read.FW = GC_BACKUP_RETRY_SKIP_DMA_OFF + ubFrameIndex * L4K_SIZE;
#endif /* (PS5017_EN) */
			}
			L4kPtr->BitMap.Read.BADR = gSBTask.ultmp_addr >> SECTOR_SIZE_SHIFT;		//Only 4K each time, Use Retry 2nd Buffer
			L4kPtr->BitMap.Read.Zinfo = MAX_ZINFO;

		}
		MT.dma.FrameNum = 1;			// Each Time only do 4K (1 Frame) Read

		MT.dma.L4KSparePtr = SPR_RETRY_OFF + (MT.dma.uliFSA0_1 & pRuleSet->pEntry->ulMask) * L4K_SIZE;

		// Fill MT content to IRAM
		memcpy((void *)M_MT_ADDR(ubMTIdx), &MT, MT_SIZE);

		// Global Trigger MT
		uoMTCfg.uoAll = 0;
		uoMTCfg.bits_recc.ubQUE_IDX = gSBTask.MTTemplate.dma.ubCEValue;
		uoMTCfg.bits_recc.ubMT_IDX = ubMTIdx;
		uoMTCfg.bits_recc.btQOS = FALSE;
		uoMTCfg.u32.ulMT_CFG1 = 0;
		FlaGlobalTrigger(&uoMTCfg);

		//HB DMA MT End
		/***************************************/

		if (CALLBACK_FUNC_IN_COMMON_CODEBANK) {
			gpRetry->ubRetryState = FLH_RETRY__WAITING_SB_MT_BUSY;
		}
		else {
			gpRetry->ubRetryState = FLH_RETRY__HANDLING_SB_RETRY;
			gSBTask.ubState = RETRY_SB_STATE_RECEIVE_CQ;
		}

		break;
	}

	return;
}

void RetrySBPostconditionForSteps ()
{
	//TODO N48R Decoding Flow 0707
	gSBTask.ubState = gSBTask.ubBackUpedState;
	gSBTask.ubSubstate = gSBTask.ubBackUpedSubstate;
}

void RetrySBUpdateStageStep()
{
	if (M_RETRY_SB_CHECK_IF_SLC_MLC_PAGETYPES()) {
		if (gSBTask.ubCurrentStep < RETRY_SB_MICRON_SLC_MLC_STEP_NUM) {
			switch (gSBTask.ubCurrentStep) {
			case RETRY_SB_MICRON_SLC_MLC_STEP_MIN1SW_PRFIX_RR_ARC3_2:
				gSBTask.ubCurrentStage++;
				break;
			default:
				break;
			}
			gSBTask.ubCurrentStep++;
		}
		else {
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
		}

		if (gSBTask.ubCurrentStep != RETRY_SB_MICRON_SLC_MLC_STEP_NUM) {
			if (gSBTask.ubCurrentLDPCFrameIdx == 1) {
				++gpMicronMAGERS->ulMicronSLCStepTriggerCnt[gSBTask.ubCurrentStep];
			}
		}
	}
	else {
		if (gSBTask.ubCurrentStep < RETRY_SB_MICRON_STEP_NUM) {
			switch (gSBTask.ubCurrentStep) {
			case RETRY_SB_MICRON_STEP_EXPRESS_SBSBR_1_2:
			case RETRY_SB_MICRON_STEP_CURCBC_P3_FINE_CALIB:
			case RETRY_SB_MICRON_STEP_PREFIX_RR8_FINE_CALIB_PERSISTENT_FINE_CALIB:
			case RETRY_SB_MICRON_STEP_ENHANCED_TRIM_APPLICATION:
			case RETRY_SB_MICRON_STEP_RESOTRE_POR_TRIM:
				gSBTask.ubCurrentStage++;
				break;
			default:
				break;
			}
			gSBTask.ubCurrentStep++;
		}
		else {
			M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
		}

		if ((gSBTask.ubCurrentStep != RETRY_SB_MICRON_STEP_NUM) && (gSBTask.ubCurrentStep != RETRY_SB_MICRON_STEP_CRTRIM_SELECTION) && (gSBTask.ubCurrentStep != RETRY_SB_MICRON_STEP_ENHANCED_TRIM_APPLICATION) && (gSBTask.ubCurrentStep != RETRY_SB_MICRON_STEP_RESOTRE_POR_TRIM)
			&& (gSBTask.ubCurrentStep != RETRY_SB_MICRON_STEP_MIN1SW_PRFIX_RR_CALIBRATED_OFFSET_CR_2B) && (gSBTask.ubCurrentStep != RETRY_SB_MICRON_STEP_MIN2SW_PRFIX_RR_CALIBRATED_OFFSET_CR_2B) && (gSBTask.ubCurrentStep != RETRY_SB_MICRON_STEP_MIN3SW_PRFIX_RR_CALIBRATED_OFFSET_CR_2B)) {
			if (gSBTask.ubCurrentLDPCFrameIdx == 1) {
				if (gSBTask.ubCurrentStep <= RETRY_SB_MICRON_STEP_CURCBC_P3_FINE_CALIB) {
					++gpMicronMAGERS->ulMicronQLCStepTriggerCnt[gSBTask.ublun * MAX_CE + gSBTask.MTTemplate.dma.ubCEValue][gSBTask.ubCurrentStep - 1];
				}
				else if (gSBTask.ubCurrentStep <= RETRY_SB_MICRON_STEP_PREFIX_RR8_FINE_CALIB_PERSISTENT_FINE_CALIB) {
					++gpMicronMAGERS->ulMicronQLCStepTriggerCnt[gSBTask.ublun * MAX_CE + gSBTask.MTTemplate.dma.ubCEValue][gSBTask.ubCurrentStep - 3];
				}
				else if (RETRY_SB_MICRON_STEP_MIN1SW_PRFIX_RR_FINE_CALIB_PERSISTENT_FINE_CALIB == gSBTask.ubCurrentStep) {
					++gpMicronMAGERS->ulMicronQLCStepTriggerCnt[gSBTask.ublun * MAX_CE + gSBTask.MTTemplate.dma.ubCEValue][gSBTask.ubCurrentStep - 3];
				}
				else if (RETRY_SB_MICRON_STEP_MIN2SW_PRFIX_RR_FINE_CALIB_PERSISTENT_FINE_CALIB == gSBTask.ubCurrentStep) {
					++gpMicronMAGERS->ulMicronQLCStepTriggerCnt[gSBTask.ublun * MAX_CE + gSBTask.MTTemplate.dma.ubCEValue][gSBTask.ubCurrentStep - 4];
				}
				else if (RETRY_SB_MICRON_STEP_MIN3SW_PRFIX_RR_FINE_CALIB_PERSISTENT_FINE_CALIB == gSBTask.ubCurrentStep) {
					++gpMicronMAGERS->ulMicronQLCStepTriggerCnt[gSBTask.ublun * MAX_CE + gSBTask.MTTemplate.dma.ubCEValue][gSBTask.ubCurrentStep - 5];
				}
				else if (RETRY_SB_MICRON_STEP_MIN1SW_PRFIX_RR_SBSBR1_CR_2b <= gSBTask.ubCurrentStep) {
					++gpMicronMAGERS->ulMicronQLCStepTriggerCnt[gSBTask.ublun * MAX_CE + gSBTask.MTTemplate.dma.ubCEValue][gSBTask.ubCurrentStep - 7];
				}
			}
		}
	}
}


U8 RetrySBInit()
{
	U8 ubch, ublun, ubIdx, ubBank;
	U8 ubalu = 0;

	/*
	 * should given outside :
	 *
	 * pre-function : Retry_Create_SB_Task()
	 * post-function : Retry_Clear_SB_Task()
	 *
	 */

	///copy delta table :

	gSBTask.ubllr_index_MAX = SB_MICRON_MAX_GENERAL_LLR_TABLE_CNT;
	//memcpy((void *)default_delta_table, (void *)sb_delta_table, sizeof(sb_delta_table));

	gSBTask.ulCount_one[0] = 0;
	gSBTask.ulCount_one[1] = 0;
	gSBTask.ulCount_one[2] = 0;

#if (FALSE == RETRY_MICRON_NICKS)
	//clear Coarse Tuning State Value
	for (ubIdx = 0; ubIdx < RETRY_SB_READ_LEVEL_NUM; ubIdx++) {
		gubCoarseTuningStateValue[ubIdx] = 0;
	}
#endif /*(FALSE == RETRY_MICRON_NICKS)*/
	//Micron SB Todo
	//FSA translation
	ubalu = gSBTask.MTTemplate.dma.ALUSelect;
	ubch = (gSBTask.MTTemplate.dma.uliFSA0_1 >> gpOtherInfo->pRuleSet->pChannel->ubShift[ubalu]) & gpOtherInfo->pRuleSet->pChannel->ulMask;
	ublun = ((gSBTask.MTTemplate.dma.uliFSA0_1 >> gpOtherInfo->pRuleSet->pLun->ubShift[ubalu]) & gpOtherInfo->pRuleSet->pLun->ulMask) << gpOtherInfo->pRuleSet->pDie_IL->ubBit_No;
	ublun |= (gSBTask.MTTemplate.dma.uliFSA0_1 >> gpOtherInfo->pRuleSet->pDie_IL->ubShift[ubalu]) & gpOtherInfo->pRuleSet->pDie_IL->ulMask;
	ubBank = (gSBTask.MTTemplate.dma.uliFSA0_1 >> gpOtherInfo->pRuleSet->pBank->ubShift[ubalu]) & gpOtherInfo->pRuleSet->pBank->ulMask;

	gSBTask.ubChannel = ubch;
	gSBTask.ublun = ublun;
	gSBTask.ubBank = ubBank;
	gSBTask.ubCurr_frm_idx = 0;
	gSBTask.ubSBDecodeFlowLoopBreakFlag = 0;

	switch (gSBTask.ubPageType) {
	case RETRY_SB_MICRON_SLC_PAGE:
	case RETRY_SB_MICRON_MLC_LOWER_PAGE:
	case RETRY_SB_MICRON_QLC_LOWER_PAGE:
		gSBTask.ubDSPEnginePageType = RETRY_SB_DSP_ENGINE_LOWER_PAGE;
		break;
	case RETRY_SB_MICRON_MLC_UPPER_PAGE:
	case RETRY_SB_MICRON_QLC_UPPER_PAGE:
		gSBTask.ubDSPEnginePageType = RETRY_SB_DSP_ENGINE_UPPER_PAGE;
		break;
	case RETRY_SB_MICRON_QLC_EXTRA_PAGE:
		gSBTask.ubDSPEnginePageType = RETRY_SB_DSP_ENGINE_EXTRA_PAGE;
		break;
	case RETRY_SB_MICRON_QLC_TOP_PAGE:
		gSBTask.ubDSPEnginePageType = RETRY_SB_DSP_ENGINE_TOP_PAGE;
		break;
	default:
		M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, FALSE);
		RetrySBSwitchStateToSetDefaultFeatureReset();
	}

	M_FIP_SET_LDPC_CONFIG_PAGE_SELECT(gSBTask.ubDSPEnginePageType);

	/// now only use ErrFram, so LDPC frm set 0xFFFF to bypass
	if (RETRY_READ_SB__DIRECTLY_GET_CORRECT_DATA_FROM_ERROR_PAGE) {
		gSBTask.uwLDPCCurrentErrMap = 0;
		for (ubIdx = 0 ; ubIdx < gub4kEntrysPerPlane ; ubIdx++) {
			if (gSBTask.ubErr_frm & (BIT0 << ubIdx)) {
				gSBTask.uwLDPCCurrentErrMap |=  ((BIT0 | BIT1) << (ubIdx << 1) );
			}
		}
	}
	else {
		gSBTask.uwLDPCCurrentErrMap = 0xFFFF;
	}

#if (FALSE == RETRY_MICRON_NICKS)
	M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (sizeof(gubCnt1Result) == sizeof(gubRetrySBMicronCntOneInfo)));
	memcpy((void *)gubCnt1Result, (void *)gubRetrySBMicronCntOneInfo, sizeof(gubCnt1Result));

	M_FIP_CLEAR_LDPC_DSP_EN();

#else	/*(FALSE == RETRY_MICRON_NICKS)*/
	M_FIP_CLEAR_LDPC_DSP_EN();
#endif	/*(FALSE == RETRY_MICRON_NICKS)*/

	RetrySBFlashSetECCParam(&sb_llr_table[RETRY_MICRON_TABLR_PARAMETER_1H2S][0], DEFAULT_LLR_TABLE_LENGTH, LLR_TABLE0_LOAD_BIT, NORMAL_MODE);

#if RETRY_HARDBIT_FOR_SDK_EN
#else /* RETRY_HARDBIT_FOR_SDK_EN */
	/// trapping set cnt = 0
	gSBTask.ubTrappingSetCnt = 0;
#endif /* RETRY_HARDBIT_FOR_SDK_EN */
	gSBTask.ubARCRoundCnt = 0;
	gSBTask.ubTotalFeatureAddr = 0;
	gSBTask.ubCurrentFeatureAddr = 0;
	gSBTask.ubTempPageType = 0;
	gSBTask.ubSpecificMode = SB_MICRON_SPECIFIC_NOT_USING;
	gSBTask.ubSpecificRetryTable = 0;
	gSBTask.ubSpecificRetryTableRound = 0;
	gSBTask.ubSpecificReadOffsetRound = 0;
#if(TRUE == RETRY_MICRON_NICKS)
	gSBTask.ubSBReadDecodeWithARC = 0;
	gSBTask.ubErrorRecoverySBStep = 0;
	gSBTask.ubBlkRefresh = 0;
#endif	/*(TRUE == RETRY_MICRON_NICKS)*/
	gSBTask.ubDoCoarseTuningFlag = 0;

	gSBTask.ubCurrentStage = 0;
	gSBTask.ubCurrentStep = 0;

	gSBTask.ubPreviousStage = 0;
	gSBTask.ubPreviousStep = 0;

	gSBTask.ubSBRead1H1Sor1H2S = RETRY_MICRON_TABLR_PARAMETER_1H1S;

	RetrySBInitStageStep();

	return TRUE;
}

void RetrySBInitMinSWInfoAndRecordReadOffset()
{
	U8 ubi = 0;
	memset(&gSBMinSWInfo, 0, sizeof(gSBMinSWInfo));
	for (ubi = 0 ; ubi < RETRY_SB_FLOW_HB_MICRON_MIN_SW_NUM ; ubi++) {
		gSBMinSWInfo.ubMinSWStep[ubi] = INVALID_RETRY_SB_STEP_INDEX;
		gSBMinSWInfo.uwMinSWValue[ubi] = INVALID_RETRY_SB_SW_VALUE;
	}
	gSBMinSWInfo.uwMinSWValueForStage5_6 = INVALID_RETRY_SB_SW_VALUE;

	gRetrySBFineCalibReadOffset.ReadOffset4Param.ulAll = 0;
}

void RetrySBInitStageStep()
{
	if (M_RETRY_SB_CHECK_IF_SLC_MLC_PAGETYPES()) {	//SLC / MLC PageTypes
		gSBTask.ubCurrentStage = RETRY_SB_MICRON_SLC_MLC_INIT_STAGE;
		gSBTask.ubCurrentStep = RETRY_SB_MICRON_SLC_MLC_INIT_STEP;
		if (1 == gSBTask.ubCurrentLDPCFrameIdx) {
			++gpMicronMAGERS->ulMicronSLCStepTriggerCnt[gSBTask.ubCurrentStep];
		}
	}
	else {	//QLC PageTypes
		gSBTask.ubCurrentStage = RETRY_SB_MICRON_QLC_INIT_STAGE;
		gSBTask.ubCurrentStep = RETRY_SB_MICRON_QLC_INIT_STEP;
	}
	return;
}



/*
 *  SOFTBIT RETRY DECODING MAIN FUNCTION
 *  =========================
 *
 *  NOTE :
 *
 *  If user want to stay inside SB decoding for longer period
 *
 *  It can be done by
 *  1. Setting CALLBACK_FUNC_IN_COMMON_CODEBANK = TRUE to build loop
 *  2. Setting gSBTask.ubSBDecodeFlowLoopBreakFlag = TRUE
 *     for breakpoint
 *
 *	and it can be canceled by :
 *	1. Setting CALLBACK_FUNC_IN_COMMON_CODEBANK = FALSE
 *
 *		Then Program Counter will leave this decoding flow
 *		EVERY TIME after triggering MT & changing state
 *
 * Main function for Micron Nicks project
 */
void SBRetryMain(void)
{
#if (RETRY_SOFTBITT_FOR_SDK_EN==FALSE)
	U8 ubMTIndex;
	FlhIntInfo8Bit_t ubFlhMsg;
	MT_Callback_Func_t callback;
	U8 ubTemperature;

	do {
		//SB MICRON Check Point 0110
		M_UART(ERROR_SOFTBIT_, "\nSB STATE:%d", gSBTask.ubState);
		M_UART(ERROR_SOFTBIT_, "\n    subSTATE:%d", gSBTask.ubSubstate);
		M_UART(ERROR_SOFTBIT_, "\n        ThirdSTATE:%d", gSBTask.ubThirdstate);
		M_UART(ERROR_SOFTBIT_, "\n            FourthSTATE:%d", gSBTask.ubFourthstate);

		switch (gSBTask.ubState) {

		case RETRY_SB_STATE_INIT:
			gSBTask.ubPrevState = RETRY_SB_STATE_INIT;

			/// init variable
			if (gSBTask.ubIsFromTurboRain) {
				RetrySBTurboRainInit();
				break;
			}
			else {
				RetrySBInit();
			}

			// Break when Assert happened in RetrySBInit
			if (RETRY_SB_STATE_SET_DEFAULT_FEATURE_AND_CHK == gSBTask.ubState) {
				break;
			}

			//SB MICRON Check Point 0806
			M_UART(ERROR_SOFTBIT_, "\n SB Skip??, ALU:%x, WLStBypass: %d \n", gSBTask.MTTemplate.dma.ALUSelect, gSBTask.MTTemplate.dma.ulUserDefineReadDma.btWLStatusBypass);
			gSBTask.ubState = RETRY_SB_STATE_SCAN_ERR_FRAME;
			break;

		case RETRY_SB_STATE_SCAN_ERR_FRAME:
			gSBTask.ubPrevState = RETRY_SB_STATE_SCAN_ERR_FRAME;
			/// scan fpl_err_hdl->hb_retry->page.job->err_frm (gSBTask.ubErr_frm) to check which err_frm needs to be retryed
			gSBTask.ubCurrentLDPCFrameIdx = 0;
			gSBTask.ubErrorRecoverySBStep = 0;		//Reset Record Error Recovery Step to 0
			for ( ; gSBTask.ubCurr_frm_idx < gub4kEntrysPerPlane; gSBTask.ubCurr_frm_idx++) {
				if ( (BIT0 << gSBTask.ubCurr_frm_idx) & gSBTask.ubErr_frm ) {

					/// refresh addr
					gSBTask.ulbackup_addr = gSBTask.ulbackup_addr_base[gSBTask.ubCurr_frm_idx];///(0x1000 * gSBTask.ubCurr_frm_idx);
					gSBTask.ulSpareAddr = gSBTask.ulSpareAddr_base [gSBTask.ubCurr_frm_idx];//(gSBTask.ubCurr_frm_idx * 16);

					/// change state
					gSBTask.ubState = RETRY_SB_STATE_CHECK_ERR_LDPC_FRM;
					break;
				}
			}

			if ( gSBTask.ubCurr_frm_idx == gub4kEntrysPerPlane ) {	//All error frame been processed, leave SB flow
				RetrySBSwitchStateToSetDefaultFeatureReset();
			}
			break;
		case RETRY_SB_STATE_CHECK_ERR_LDPC_FRM:
			gSBTask.ubPrevState =  RETRY_SB_STATE_CHECK_ERR_LDPC_FRM;
			/// scan gpRetry->RetryJobList[gpRetry->ubHead].uwLDPCCurrentErrMap and give value to gSBTask->curr_ldpc_frm_idx
			/// *******************need to manually do  gSBTask->curr_ldpc_frm_idx++ after each SB retry done****************
			if ( gSBTask.ubCurrentLDPCFrameIdx == LDPC_FRAME_MAX_CNT) {
				/// this physical frame is done, change state
				if ( (gSBTask.uwLDPCCurrentErrMap & (     (BIT0 | BIT1) << ((gSBTask.ubCurr_frm_idx) << 1) )  ) == 0) {
					gSBTask.ubErr_frm &= ~(BIT0 << (gSBTask.ubCurr_frm_idx));
				}
				gSBTask.ubCurrentLDPCFrameIdx = 0;
				gSBTask.ubState = RETRY_SB_STATE_SCAN_ERR_FRAME;
				break;
			}

			if ( ( (gSBTask.uwLDPCCurrentErrMap) & ( BIT((gSBTask.ubCurr_frm_idx << 1) + gSBTask.ubCurrentLDPCFrameIdx) )  ) == 0) {
				if (gSBTask.ubIsAfterRaidECC > RETRY_SB_TASK_IS_RETRY_TASK_CASE) {
					/// do nothing
				}
				else {
					if (RETRY_READ_SB__DIRECTLY_GET_CORRECT_DATA_FROM_ERROR_PAGE) {
						/// read good 2K to buffer
						gSBTask.ubState = RETRY_SB_STATE_READ_GOOD_2K_TO_BUF;
						gSBTask.ubThirdstate = RETRY_SB_3RD_STATE_HB_READ_INIT;
					}
				}
			}
			else {
				if (gSBTask.ubIsAfterRaidECC == RETRY_SB_TASK_IS_ERR_PAGE_AFTER_RAIDECC_CASE) {
					gSBTask.ubState = RETRY_SB_STATE_SB_WITH_OPT_READ_LEVEL__AFTER_RAIDECC;
					gSBTask.ubSubstate = RETRY_SB_SUBSTATE_WITH_OPT_READ_LEVEL__AFTER_RAIDECC__INIT;
				}
				else {
					RetrySBInitStageStep();

					RetrySBInitMinSWInfoAndRecordReadOffset();

#if (RETRY_SB_ORIGIN_EN)
					if (M_RETRY_SB_CHECK_IF_SLC_MLC_PAGETYPES()) {
						gSBTask.ubState = RETRY_SB_STATE_HB_WITH_AUTO_READ_CALIBRATION;
						gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_READ_WITH_ARC_INIT;
					}
#else /*(RETRY_SB_ORIGIN_EN)*/
					gSBTask.ubState = RETRY_SB_STATE_HB_2K_RAW_READ_DECODE;
					gSBTask.ubSubstate = RETRY_SB_SUBSTATE_HB_RAW_READ_INIT;
#endif /*(RETRY_SB_ORIGIN_EN)*/


				}
			}
			break;
#if (RETRY_SB_ORIGIN_EN)
		/***********SLC MLC Origin Flow--->>>>********/
		case RETRY_SB_STATE_HB_WITH_AUTO_READ_CALIBRATION:
			gSBTask.ubPrevState = RETRY_SB_STATE_HB_WITH_AUTO_READ_CALIBRATION;
			RetrySBHBReadwithARC();
			break;

		case RETRY_SB_STATE_SB_READ_WITH_READ_RETRY_TABLE_WITH_ARC:
			gSBTask.ubPrevState = RETRY_SB_STATE_SB_READ_WITH_READ_RETRY_TABLE_WITH_ARC;
			gSBTask.ubSBReadDecodeWithARC = TRUE;
			RetrySBReadFlow();
			gSBTask.ubllr_index = SB_MICRON_DEFAULT_LLR_TABLE_START_IDX;
			break;

		case RETRY_SB_STATE_LOAD_DEFAULT_LLR_TABLE:
			gSBTask.ubPrevState =  RETRY_SB_STATE_LOAD_DEFAULT_LLR_TABLE;
			RetrySBFlashRetoreLLR(gSBTask.ubDSPEnginePageType, RETRY_MICRON_TABLR_PARAMETER_1H2S); // set default llr table
			gSBTask.ubState = RETRY_SB_STATE_SB_DECODE_WITH_DEFAULT_LLR;
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_DECODE_WITH_DEFAULT_LLR_INIT;
			break;

		case RETRY_SB_STATE_SB_DECODE_WITH_DEFAULT_LLR:
			gSBTask.ubPrevState = RETRY_SB_STATE_SB_DECODE_WITH_DEFAULT_LLR;
			RetrySBDecodeFlow();
			break;
			/*******<<<<----SLC MLC Origin Flow************/
#else /*(RETRY_SB_ORIGIN_EN)*/
		case RETRY_SB_STATE_HB_2K_RAW_READ_DECODE:
			gSBTask.ubPrevState = RETRY_SB_STATE_HB_2K_RAW_READ_DECODE;
			RetrySBHBRAWReadSBCDecode();
			break;

		case RETRY_SB_STATE_SB_READ:
			gSBTask.ubPrevState = RETRY_SB_STATE_SB_READ;
			RetrySBReadFlowNew();
			break;

		case RETRY_SB_STATE_LOAD_DEFAULT_LLR:
			gSBTask.ubPrevState =  RETRY_SB_STATE_LOAD_DEFAULT_LLR;
			//Set LLR with 1H1S 1H2S
			RetrySBFlashRetoreLLR(gSBTask.ubDSPEnginePageType, gSBTask.ubSBRead1H1Sor1H2S); // set default llr table
			gSBTask.ubState = RETRY_SB_STATE_SB_DECODE;
			gSBTask.ubSubstate = RETRY_SB_SUBSTATE_SB_DECODE_INIT;
			break;

		case RETRY_SB_STATE_SB_DECODE:
			gSBTask.ubPrevState = RETRY_SB_STATE_SB_DECODE;
			RetrySBDecodeFlowNew();
			break;

		case RETRY_SB_STATE_HB_4K_READ_MT_DECODE:
			gSBTask.ubPrevState = RETRY_SB_STATE_HB_4K_READ_MT_DECODE;
			RetrySBHB4KReadMTDecode();
			break;

		case RETRY_SB_STATE_CR_TRIM_ENHANCE_TRIM:
			if (gSBTask.ubThirdstate == RETRY_SB_3RD_STATE_SETTING_CORRECTIVE_READ_SET_FEATURE) {
				gSBTask.ubPrevState = RETRY_SB_STATE_CR_TRIM_ENHANCE_TRIM;
				gSBTask.ubCurrentStep = RETRY_SB_MICRON_STEP_ENHANCED_TRIM_APPLICATION;
				ubTemperature = FIPMicronGetTemperature(gSBTask.ulFSA_ori);
				if (ubTemperature < RETRY_SB_ENHANCE_CORRECTIVE_READ_THRESHOLD) {
					gSBTask.ubIsEnhance = TRUE;
					RetrySBSetEnhanceCRTrim();
				}
				else {
					gSBTask.ubIsEnhance = FALSE;
				}
			}
			RetrySBUpdateStageStep();
			RetrySBSwitchStatebyStageStep(FALSE, gSBTask.ubCurrentStage, gSBTask.ubCurrentStep);
			break;
		case RETRY_SB_STATE_RESTORE_POR_TRIM:
			//TODO N48R Decoding Flow 0707

			RetrySBUpdateStageStep();
			RetrySBSwitchStatebyStageStep(FALSE, RETRY_SB_MICRON_STAGE_NUM, RETRY_SB_MICRON_SLC_MLC_STEP_NUM);
			break;

		case RETRY_SB_STATE_POSTCONDITION_FOR_STEPS:
			//TODO N48R Decoding Flow 0707
			RetrySBPostconditionForSteps();
			break;


#endif /*(RETRY_SB_ORIGIN_EN)*/

		case RETRY_SB_STATE_TERMINATE_PASS:
			//SBRAID Debug 1115
			/*
			if (DEBUG_SOFTBITRAIDECC_UART) {
				UartPrintf("\n [SB]  PCA:%x \n", (U32)( (gSBTask.ulFSA_Align) + (gSBTask.ubCurr_frm_idx) ));
			}
			*/
			gSBTask.uwLDPCCurrentErrMap &= ~(     (BIT(gSBTask.ubCurrentLDPCFrameIdx)) << ((gSBTask.ubCurr_frm_idx) << 1)       );

			//Update Error Recovery Step Cnt when both LDPC Frame been Decode Pass
			if (((     (BIT0 | BIT1) << ((gSBTask.ubCurr_frm_idx) << 1)       ) & gSBTask.uwLDPCCurrentErrMap) == 0) {
				M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (0 != gSBTask.ubErrorRecoverySBStep));
				//M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (RETRY_MICRON_ERROR_RECOVERY_STEP_NUM > gSBTask.ubErrorRecoverySBStep));
			}

		/* no break */
		case RETRY_SB_STATE_CHECK_NEXT_STATE:
			//Micron SB Fix 1004
			gSBTask.ubSpecificRetryTableRound = 0;
			gSBTask.ubSBReadDecodeWithARC = FALSE;

			/// if collected 4K, use FPU COR to send it out.
			gSBTask.ubCurrentLDPCFrameIdx++;
			RetrySBSwitchToSetDefaultFeatureThenDoBackupSBFlow(RETRY_SB_STATE_CHECK_ERR_LDPC_FRM, RETRY_SB_SUBSTATE_NULL);
			if (gSBTask.ubCurrentLDPCFrameIdx == LDPC_FRAME_MAX_CNT) {
				if ( (gSBTask.uwLDPCCurrentErrMap & (     (BIT0 | BIT1) << ((gSBTask.ubCurr_frm_idx) << 1) )  ) == 0) {
					gSBTask.ubErr_frm &= ~(BIT0 << (gSBTask.ubCurr_frm_idx));
				}
				gSBTask.ubCurrentLDPCFrameIdx = 0;
				gSBTask.ubCurr_frm_idx++;
				RetrySBSwitchToSetDefaultFeatureThenDoBackupSBFlow(RETRY_SB_STATE_SCAN_ERR_FRAME, RETRY_SB_SUBSTATE_NULL);
				//Update Error Recovery Step Trigger Cnt when both LDPC Frame been accessed
				M_FW_ASSERT(ASSERT_NO_DEFINE_0x0FFF, (0 != gSBTask.ubErrorRecoverySBStep));

				if (M_RETRY_SB_CHECK_IF_SLC_MLC_PAGETYPES()) {
					if (RETRY_SB_MICRON_SLC_MLC_STEP_MIN1SW_PRFIX_RR_ARC1 <= gSBTask.ubErrorRecoverySBStep) {
						gSBTask.ubBlkRefresh = TRUE;
					}
				}
				else {
					if (RETRY_SB_MICRON_STEP_MIN1SW_PRFIX_RR_SBSBR1_CR_2b <= gSBTask.ubErrorRecoverySBStep) {
						gSBTask.ubBlkRefresh = TRUE;
					}
				}
				if ( gSBTask.ubCurr_frm_idx == gub4kEntrysPerPlane ) {	//All error frame been processed, leave SB flow
					//Clear 4K_Frame Index
					gSBTask.ubCurr_frm_idx = 0;
					RetrySBSwitchStateToSetDefaultFeatureReset();
				}
			}

			break;

		case RETRY_SB_STATE_TERMINATE_FAIL:
#if (RETRY_TURBO_RAIN_EN)
			gSBTask.ubNeedToGetSB0Flag = TRUE;
			RetrySBBackupCorrect2k(0, gSBTask.ubCurrentLDPCFrameIdx,			\
				gSBTask.ulbackup_addr, gSBTask.ultmp_addr,				\
				gSBTask.ulSpareAddr, gSBTask.ultmp_SpareAddr, RETRY_SB_BACKUP_RESTORE_TO_DRAMIRAM);
			gSBTask.ubNeedToGetSB0Flag = FALSE;
#endif /*RETRY_TURBO_RAIN_EN*/
			gSBTask.ubState =  RETRY_SB_STATE_CHECK_NEXT_STATE;
			break;

		case RETRY_SB_STATE_RECEIVE_CQ:
			if (FALSE == FIPIsStopAllocateEventSet(FIP_STOP_ALLOCATE_EVENT_SB_WAIT_MT_DONE)) {
				FIPSetStopAllocateBuf(FIP_STOP_ALLOCATE_EVENT_SB_WAIT_MT_DONE);
			}
			FIPDelegateCmd();
			while (gpRetry->TrigMTList.ubTrigCnt) {
				ubMTIndex = gpRetry->TrigMTList.MTCb[gpRetry->TrigMTList.ubHead].ubMTIndex;

				ubFlhMsg = gMTMgr.ubMTDoneMsg[ubMTIndex - MT_RETRY_START_INDEX];

				if (ubFlhMsg.ubAll == 0) {
					break; // MT not finish, break
				}

				gpRetry->RttTimer.uoStartRTT = 0;
				gpRetry->RttTimer.uoEndRTT   = 0;

				if ((ubFlhMsg.btMTStop) || (ubFlhMsg.btErasePage)) {
					//SB MICRON Check Point 0724
					M_UART(ERROR_SOFTBIT_, "\n FailCallBack!!");
					callback = (MT_Callback_Func_t)gpRetry->TrigMTList.MTCb[gpRetry->TrigMTList.ubHead].FailCallback;
				}
				else {
					callback = (MT_Callback_Func_t)gpRetry->TrigMTList.MTCb[gpRetry->TrigMTList.ubHead].PassCallback;
				}
				if (callback != NULL) {
					callback(ubMTIndex);
				}

				RetrySBReleaseFinishMT();
				if (0 == gpRetry->TrigMTList.ubTrigCnt) {
					FIPClearStopAllocateBuf(FIP_STOP_ALLOCATE_EVENT_SB_WAIT_MT_DONE);
				}
			}
			break;
		case RETRY_SB_STATE_SET_DEFAULT_FEATURE_AND_CHK:
			gSBTask.ubPrevState =  RETRY_SB_STATE_SET_DEFAULT_FEATURE_AND_CHK;
			RetrySBSetDefaultFeatureAndCheck();
			break;

		case RETRY_SB_STATE_SB_RESET_FLH:
			gSBTask.ubPrevState =  RETRY_SB_STATE_SB_RESET_FLH;
			if (gSBTask.ubIsFromTurboRain) {
				////backup ibuf0
				RetrySBBackupCorrect2k(0, gSBTask.ubCurrentLDPCFrameIdx,			\
					gSBTask.ultmp_addr, gSBTask.ultmp_addr,				\
					gSBTask.ultmp_SpareAddr, gSBTask.ultmp_SpareAddr, RETRY_SB_BACKUP_RESTORE_TO_ONLYDRAM);
				////backup ibuf1
				RetrySBBackupCorrect2k(1, gSBTask.ubCurrentLDPCFrameIdx,			\
					gSBTask.ultmp_addr + BC_4KB + BC_1KB, gSBTask.ultmp_addr + BC_4KB + BC_1KB,				\
					gSBTask.ultmp_SpareAddr, gSBTask.ultmp_SpareAddr, RETRY_SB_BACKUP_RESTORE_TO_ONLYDRAM);
			}
			RetrySBResetFlash();
			break;
		case RETRY_SB_STATE_DONE:
			gSBTask.ubSBDecodeFlowLoopBreakFlag = 1;
			break;
		}

		if ((CALLBACK_FUNC_IN_COMMON_CODEBANK == FALSE) && (gSBTask.ubSBDecodeFlowLoopBreakFlag)) {
			break;
		}
	} while ( (CALLBACK_FUNC_IN_COMMON_CODEBANK == FALSE)  );
#else  /* (RETRY_SOFTBITT_FOR_SDK_EN==FALSE) */
	gSBTask.ubErr_frm = gpRetry->RetryJobList[gpRetry->ubHead].ubErrorP4KBMP;
	gSBTask.ubState = RETRY_SB_STATE_DONE;

	return;
#endif  /* (RETRY__KEEP_SOFTBIT_FLOW_CODE_EN==TRUE) */
}
#else /*(TRUE == RETRY_MICRON_NICKS)*/
void SBRetryMain(void)
{
}
#endif /*(TRUE == RETRY_MICRON_NICKS)*/
#endif /* (RETRY__ENABLE_SB_FLOW) */
#endif /*(((CONFIG_FLASH_TYPE == FLASH_TYPE_MICRON_3D_QLC)) && ((TRUE == RETRY_MICRON_NICKS_ALL_PAGE_TYPE_SB_SUPPORT) && (TRUE == IM_N48R)))*/
