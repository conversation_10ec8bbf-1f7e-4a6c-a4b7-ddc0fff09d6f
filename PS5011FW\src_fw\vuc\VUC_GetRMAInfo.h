#ifndef _VUC_GETRMAINFO_H_
#define _VUC_GETRMAINFO_H_

#include "aom/aom_api.h"
#include "hal/fip/fip_api.h"

#define VUC_READ_RMA_INFO_SIZE_IN_4K		(4)
#define VUC_RMA_INFO_HEADER_SIZE			(5)
#define VUC_RMA_INFO_IC_TYPE_SIZE			(10)
#define VUC_RMA_INFO_CONTROLL_LOT_NUMNER	(8)
#define VUC_RMA_INFO_WORKING_ORDER_NUM		(16)
#define VUC_RMA_INFO_IC_VERSION_HARDWARE_INTERATION_SHIFT	(4)

#pragma pack(push, 1)
typedef struct {
	U8 ubRMAInfoHeader[VUC_RMA_INFO_HEADER_SIZE];
	U8 ubRMADebugInfoVersion;
	U8 ubReserve1[506];
	U8 ubFWName[FW_REVISION_LENGTH];
	U8 ubICType[VUC_RMA_INFO_IC_TYPE_SIZE];
	U64 uoUserCapacity;
	U8 ubCENum;
	U8 ubBitPerCell;
	U8 ubPageSize;
	U8 ubFlashID[READ_ID_DATA_NUM];
	U8 ubCHNum;
	U32 ulDDRSize;
	U8 ubModelName[MODEL_NUM_LENGTH];
	U8 ubSerialNum[SERIAL_NUM_LENGTH];
	U16 uwFormFactor;
	U8 ubSmartCustomer;
	U8 ubBuildFATTable;
	U64 uoTotalEarlyBadBlkCnt;
	U64 uoTotalLaterBadBlkCnt;
	U64 uoECCCnt;
	U32 ulCRCCnt;
	U32 ulAverageEC;
	U32 ulMaxEC;
	U8 ubWriteProtect;
	U8 ubIndustrialSpec;
	U8 ubFlashProcessASCII1;
	U8 ubFlashProcessASCII2;
	U8 ubFlashProcessNumeric;
	U8 ubFlashProcessASCII3;
	U8 ubPlaneNum;
	U8 ubDieNumPerCE;
	U16 uwBlkPerCE;
	U16 uwBlkSize;
	U8 ubControllerLotNum[VUC_RMA_INFO_CONTROLL_LOT_NUMNER];
	U16 uwAssertNum;
	U8 ubVTFailLogPtr;
	U64 uoTotalD1EC;
	U64 uoTotalD2D3EC;
	U32 ulProgramFailD1Cnt;
	U32 ulProgramFailD2D3Cnt;
	U64 uoMarkBadCnt;
	U64 uoPowerOnTimeMinute;
	U64 uoPowerCycleCnt;
	U64 uoFWUpdateCnt;
	U32 ulWriteCRCCnt;
	U32 ulReadCRCCnt;
	U32 ulDDRErrorLogCnt;
	U8 ubLBADataSize;
	U64 uoNampeSpaceSize;
	U64 uoDeviceCapacity;
	U64 uoNandWriteSector;
	U32 ulReadLaterBadBlockCnt;
	U32 ulProgramLaterBadBlockCnt;
	U32 ulEraseLaterBadBlockCnt;
	U32 ulReadRetry;
	U32 ulCopyECC;
	U8 ubHostRWCRCCnt;
	U8 ubHostAXIError;
	U32 ulHostReadECCCnt;
	U32 ulPreAverageEC;
	U8 ubICVersion;
	U8 ubWorkingOrder[VUC_RMA_INFO_WORKING_ORDER_NUM];
	U32 ulSBRetryCnt;
	U32 ulRAIDECCRecoverySuccessCnt;
	U32 ulRAIDECCRecoveryFailCnt;
	U32 ulCERAIDECCCnt[MAX_PHYSICAL_CE_NUM];
	U32 ulCESBRetryCnt[MAX_PHYSICAL_CE_NUM];
	U32 ulCEHBRetryCnt[MAX_PHYSICAL_CE_NUM];
	U8 ubBankNum;
	U8 ubReserve2[318];
	U16 uwRemainSpareUnit;
	U32 ulHostAXIErrorPersistentCnt;
	U8 ubPCIEAXIErrCnt;
	U8 ubProgramE3DErrorCnt;
	U8 ubProgramENCCnt;
	U64 uoFIPTimeoutCnt;
	U32 ulPreviousMaxEC;
	U64 uoCEBMP;
	U64 uoFailCEBMP;
	U8 ubReserve3[2523];

}
RMA_Info_t;
#pragma pack(pop)

AOM_VUC void VUCGetRMAInfo(VUC_OPT_HCMD_PTR_t pCmd);

#endif /* _VUC_GETRMAINFO_H_ */
