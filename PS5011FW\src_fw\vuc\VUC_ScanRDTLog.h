#ifndef _VUC_SCANRDTLOG_H_
#define _VUC_SCANRDTLOG_H_

//==============================================================================
// RDT Log Configure Define
//==============================================================================
#define RDT_BLOCK_NUM_PER_LOG	(2)
#define	RDT_LOG_BLOCK0			(0)
#define	RDT_LOG_BLOCK1			(1)
#define MAX_SCAN_RDT_BLOCK_NUM	(256 / (gubCENumber * gubBurstsPerBank))

typedef struct {
	PCA_t ulRDTLog_PCA[RDT_BLOCK_NUM_PER_LOG];
	U16 uwRDTLog_Pages[RDT_BLOCK_NUM_PER_LOG];
} RDTLogInfo_t;

enum {
	RDT_LOG_DBT,
	RDT_LOG_ERROR_RECORD,				//ERL
	RDT_LOG_TEMPERATURE_DIVERSITY,		//TDL
	RDT_LOG_RDT_TESTMARK,				//RML
	RDT_LOG_FLASH_TESTMARK,				//FML
	RDT_LOG_POWER_RESUME,				//PRL
	RDT_LOG_ERROR_RECORD_2,				//ERL2
#if (RDT_MODE_EN || RDT_BURNER_MODE_EN)
	RDT_LOG_SAMPLE_BLOCK_TEST_LOG,      //STL
	RDT_LOG_TP_TESTMARK,                //TML  (E19 used)
	RDT_LOG_MICRON_RECORD_BEC_LOG,      //BEC
	RDT_LOG_SCAN_WIN,                   //SCANW
	RDT_LOG_VRLC,                       //VRLC
#endif /* (RDT_MODE_EN || RDT_BURNER_MODE_EN) */
#if RDT_RECORD_ECC_DISTRIBUTION
	RDT_LOG_FWVRLC, 					//AP Use
	RDT_LOG_ECC_DISTRIBUTION_LOG,		//EDL
	RDT_LOG_ECC_DISTRIBUTION_LOG2,		//EDL
	RDT_LOG_ECC_DISTRIBUTION_LOG3,		//EDL zerio add
	RDT_LOG_ECC_DISTRIBUTION_LOG4,		//EDL
	RDT_LOG_ECC_DISTRIBUTION_LOG5,		//EDL
	RDT_LOG_ECC_DISTRIBUTION_LOG6,		//EDL
	RDT_LOG_ECC_DISTRIBUTION_LOG7,		//EDL
	RDT_LOG_ECC_DISTRIBUTION_LOG8,		//EDL
#endif
#if (RDT_RUN_ONLINE)
	RDT_LOG_MP_PH_LOG,
#endif
	RDT_LOG_NUM
};

extern RDTLogInfo_t gRDTLog[RDT_LOG_NUM];

void VUC_ScanRDTLog(VUC_OPT_HCMD_PTR_t pCmd);

#endif /* _VUC_SCANRDTLOG_H_ */
