/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "rram_api.h"
#include "hal/sys/api/misc/misc_api.h"
#include "hal/sys/api/pmu/pmu_api.h"

/*
 * ---------------------------------------------------------------------------------------------------
 *   public
 * ---------------------------------------------------------------------------------------------------
 */
void ResumeRamInit(void)
{
	//-------------------------------------------------------------------------
	//154000F0000// set pmu_as_wake_msk [16] ,rcosc_as_wake_msk [17]
	//***********// set pmu_as_wake_msk [16] ,rcosc_as_wake_msk [17]
	//-------
	//11F00800304// load vdt pd
	//17700FEBFFC// clear xx_pd
	//***********// store to vdt pd
	//-------
	//0800000046C// jump direct to ...
	//-------------------------------------------------------------------------

	// work around for FIO power drop
	M_MISC_SET_RRAM_BANK(3);
	__asm("DSB");

	R32_SYS0_RRAM[(0x00) >> 2] = 0x00800520;	// Load PMU_PG_CTRL
	R32_SYS0_RRAM[(0x04) >> 2] = 0x00000200;	// set cr_lv_msk_pre [9]
	R32_SYS0_RRAM[(0x08) >> 2] = 0x00800520;	// store to Load PMU_PG_CTRL

	R32_SYS0_RRAM[(0x0C) >> 2] = 0x00000469;	// jump direct to ...


	__asm("DSB");
	M_MISC_SET_RRAM_BANK(7);
	__asm("DSB");

	R8_SYS0_RRAM[0x00] = 0x1F;	// load Load PMU_PG_CTRL
	R8_SYS0_RRAM[0x04] = 0x67;	// set cr_lv_msk_pre [9]
	R8_SYS0_RRAM[0x08] = 0x27;	// store to Load PMU_PG_CTRL

	R8_SYS0_RRAM[0x0C] = 0x80;	// jump direct to ...

	R8_SYS_PMU[0x0E] = 0xC0;	// set LPM3 WU address ==0x80C0
	R8_SYS_PMU[0x0F] = 0x80;	// set LPM3 WU address ==0x80C0
#if E21_TODO
#else/* E21_TODO*/
	// work around for FIO power drop
	M_MISC_SET_RRAM_BANK(0);
	__asm("DSB");

	R32_SYS0_RRAM[(0x00) >> 2] = 0x00800520;	// set CR_FLH_PG (bit3)
	R32_SYS0_RRAM[(0x04) >> 2] = 0x00000008;	// set CR_FLH_PG (bit3)
	R32_SYS0_RRAM[(0x08) >> 2] = 0x00800520;	// set CR_FLH_PG (bit3)

	R32_SYS0_RRAM[(0x0C) >> 2] = 0x00800118;	// set CR_GPIO_OE[9] (bit10)
	R32_SYS0_RRAM[(0x10) >> 2] = 0x00000400;	// set CR_GPIO_OE[9] (bit10)
	R32_SYS0_RRAM[(0x14) >> 2] = 0x00800118;	// set CR_GPIO_OE[9] (bit10)

	R32_SYS0_RRAM[(0x18) >> 2] = PMU_SHUTDOWN_FLASH_POWER_DELAY_50NS;	// loop, 1T = 50ns

	R32_SYS0_RRAM[(0x1C) >> 2] = 0x00800118;	// clear CR_GPIO_OE[9] (bit10)
	R32_SYS0_RRAM[(0x20) >> 2] = 0x0000FB00;	// clear CR_GPIO_OE[9] (bit10)
	R32_SYS0_RRAM[(0x24) >> 2] = 0x00800118;	// clear CR_GPIO_OE[9] (bit10)

	R32_SYS0_RRAM[(0x28) >> 2] = PMU_POWER_ON_FLASH_POWER_DELAY_50NS;	// loop, 1T = 50ns

	R32_SYS0_RRAM[(0x2C) >> 2] = 0x00800520;	// clear CR_FLH_PG (bit3)
	R32_SYS0_RRAM[(0x30) >> 2] = 0x000000F7;	// clear CR_FLH_PG (bit3)
	R32_SYS0_RRAM[(0x34) >> 2] = 0x00800520;	// clear CR_FLH_PG (bit3)

	R32_SYS0_RRAM[(0x38) >> 2] = 0x00000000;	// return

	__asm("DSB");
	M_MISC_SET_RRAM_BANK(4);
	__asm("DSB");

	R8_SYS0_RRAM[0x00] = 0x1F;	// set CR_FLH_PG (bit3)
	R8_SYS0_RRAM[0x04] = 0x61;	// set CR_FLH_PG (bit3)
	R8_SYS0_RRAM[0x08] = 0x2F;	// set CR_FLH_PG (bit3)

	R8_SYS0_RRAM[0x0C] = 0x1F;	// set CR_GPIO_OE[9] (bit10)
	R8_SYS0_RRAM[0x10] = 0x6F;	// set CR_GPIO_OE[9] (bit10)
	R8_SYS0_RRAM[0x14] = 0x2F;	// set CR_GPIO_OE[9] (bit10)

	R8_SYS0_RRAM[0x18] = 0xE0;	// loop, 1T = 50ns

	R8_SYS0_RRAM[0x1C] = 0x1F;	// clear CR_GPIO_OE[9] (bit10)
	R8_SYS0_RRAM[0x20] = 0x7F;	// clear CR_GPIO_OE[9] (bit10)
	R8_SYS0_RRAM[0x24] = 0x2F;	// clear CR_GPIO_OE[9] (bit10)

	R8_SYS0_RRAM[0x28] = 0xE0;	// loop, 1T = 50ns

	R8_SYS0_RRAM[0x2C] = 0x1F;	// clear CR_FLH_PG (bit3)
	R8_SYS0_RRAM[0x30] = 0x71;	// clear CR_FLH_PG (bit3)
	R8_SYS0_RRAM[0x34] = 0x2F;	// clear CR_FLH_PG (bit3)

	R8_SYS0_RRAM[0x38] = 0xF0;	// return

	R8_SYS_PMU[0x5A] = 0x01;	// cr_rramboot_en
#endif /* E21_TODO */
}
