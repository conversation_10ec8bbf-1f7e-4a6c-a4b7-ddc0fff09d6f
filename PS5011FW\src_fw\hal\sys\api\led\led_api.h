#ifndef	_LED_API_H_
#define	_LED_API_H_

#include "hal/sys/reg/sys_pd1_reg.h"
#include "aom/aom_api.h"

#define			LED_DEFAULT							(0)
#define			LED_LOW_LT_UNIT						(100)
#define			LED_UNIT						    (10)
#define			LED_LIGHT_OTHER						(0)
#define	 		LED_SQUARE_WAVE_FAST_6				(6)
#define			LED_MODE_SEL_L32					(5)
#define			LED_ON_TIME_DEFAULT					(200)
#define			LED_OFF_TIME_DEFAULT				(300)
#if PS5021_EN
#define 		M_LED_ENABLE()                		(R32_SYS0_PAD_CTRL[R32_SYS0_PAD_PD0_CTRL2] |= SR_LED_PAD_EN_BIT)
#define 		M_LED_DISABLE()                		(R32_SYS0_PAD_CTRL[R32_SYS0_PAD_PD0_CTRL2] &= ~SR_LED_PAD_EN_BIT)
#define 		M_LED_GET_STATUS()					(R32_SYS0_PAD_CTRL[R32_SYS0_PAD_PD0_CTRL2] & SR_LED_PAD_EN_BIT)
#define			M_LED_SEL_LED_MODE(MODE)			(NULL)
#define			M_LED_SEL_LED_WAVE_MODE(MODE)		(NULL)
#define			M_LED_SEL_LOW_LT_DEFINE(L_TIME)		(NULL)
#else/* PS5021_EN */
#define			M_LED_DISABLE()						R32_SYS1_LED[R32_SYS1_SYS_LED_CFG] &= ~(LED_TRIG_BIT)
#define			M_LED_ENABLE()						R32_SYS1_LED[R32_SYS1_SYS_LED_CFG] |= LED_TRIG_BIT
#define 		M_LED_GET_STATUS()					((R32_SYS1_LED[R32_SYS1_SYS_LED_CFG] & LED_TRIG_BIT) >> LED_TRIG_SHIFT)
#define			M_LED_SEL_LED_MODE(MODE)			{R32_SYS1_LED[R32_SYS1_SYS_LED_CFG] &= ~MODE_SEL_MASK;\
                                                    R32_SYS1_LED[R32_SYS1_SYS_LED_CFG] |= (((MODE) & MODE_SEL_MASK) << MODE_SEL_SHIFT);}
#define			M_LED_SEL_LED_WAVE_MODE(MODE)		{R32_SYS1_LED[R32_SYS1_SYS_LED_CFG] &= ~(LED_WAVE_MODE_MASK << LED_WAVE_MODE_SHIFT);\
                                                    R32_SYS1_LED[R32_SYS1_SYS_LED_CFG] |= (((MODE) & LED_WAVE_MODE_MASK) << LED_WAVE_MODE_SHIFT);}
#define			M_LED_SEL_LOW_LT_DEFINE(L_TIME)		{R32_SYS1_LED[R32_SYS1_SYS_LED_CFG] &= ~(LOW_LT_DEFINE_MASK << LOW_LT_DEFINE_SHIFT);\
                                                    R32_SYS1_LED[R32_SYS1_SYS_LED_CFG] |= (((L_TIME) & LOW_LT_DEFINE_MASK) << LOW_LT_DEFINE_SHIFT);}
#endif/* PS5021_EN */

typedef struct {
	U16 uwONTime;
	U16 uwOFFTime;
} LEDModule_t;

extern LEDModule_t gLED;

AOM_INIT_2 void LEDSetLEDPattern(U8 ubLEDLightMode, U8 ubLEDWaveMode);

#endif /* _LED_API_H_ */
