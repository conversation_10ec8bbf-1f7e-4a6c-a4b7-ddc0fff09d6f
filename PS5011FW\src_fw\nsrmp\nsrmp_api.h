#ifndef _NSRMP_API_H_
#define _NSRMP_API_H_

#include "nsrmp/hal_nsrmp.h"
#include "typedef.h"

#ifdef EXTERN
#undef EXTERN
#endif

#ifdef _NSRMP_API_C_
#define EXTERN
#else
#define EXTERN  extern
#endif

#define NS_SIZE_4GB   (0x100000000)

typedef struct ns_oft_tbl_struct NS_OFT_TBL_STRUCT, *NS_OFT_TBL_STRUCT_PTR;
struct ns_oft_tbl_struct {
	U16 valid;
	U16 start;
	U16 len;
};

typedef struct nsrmp_api_struct NSRMP_API_STRUCT, *NSRMP_API_STRUCT_PTR;
struct nsrmp_api_struct {
	U8 ns_pool_free_idx;
	NS_OFT_TBL_STRUCT ns_oft_tbl[NS_NUM];
};

#if (HOST_MODE == NVME)
EXTERN NSRMP_API_STRUCT gNsrmpApiStruct;
#endif
EXTERN void nsrmp_api_ns_tables_init(NSRMP_API_STRUCT_PTR p_nsrmp_api);
EXTERN void nsrmp_api_init(NSRMP_API_STRUCT_PTR p_nsrmp_api);
EXTERN void nsrmp_api_init_ns(NSRMP_API_STRUCT_PTR p_nsrmp_api);
EXTERN void nsrmp_api_set_ns(NSRMP_API_STRUCT_PTR p_nsrmp_api, U8 ub_nsid, U16 uw_4gb_unit);
EXTERN void nsrmp_api_del_ns(NSRMP_API_STRUCT_PTR p_nsrmp_api, U8 ub_nsid);



#undef EXTERN

#endif
