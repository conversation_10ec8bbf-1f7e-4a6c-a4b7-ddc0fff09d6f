#ifndef _FTL_XFER_DATA_IN_H_
#define _FTL_XFER_DATA_IN_H_

/***********************
	Include Files
************************/
#include <stdlib.h>
#include "setup.h"
#include "typedef.h"
#include "spare.h"
#include "debug/debug.h"
#include "debug/debug_setup.h"

#include "hal/sys/api/misc/misc_api.h"
#include "hal/apu/apu_api.h"
#include "hal/bmu/bmu_api.h"
#include "hal/cpu/cpu_api.h"
#include "hal/cop0/cop0.h"
#include "hal/cop0/cop0_cmd.h"
#include "hal/cop0/cop0_tiein.h"
#include "hal/cop0/cop0_tieout.h"
#include "hal/cop0/cop0_pop_cmd.h"
#include "hal/cop0/cop0_api.h"
#include "hal/cop1/cop1_inline.h"
#include "hal/cop1/cop1_api.h"
#include "hal/db/db_api.h"
#include "hal/dmac/dmac_pop_cmd.h"
#include "hal/fip/fip_api.h"
#include "hal/pic/uart/uart_api.h"
#include "hal/xzip/xzip_api.h"
#include "hal/xzip/xzip_reg.h"
#include "hal/sata/sata_api.h"
#include "host/host.h"

#include "ftl/ftl.h"
#include "ftl/ftl_api.h"
#include "ftl/ftl_barrier.h"
#include "ftl/ftl_gc.h"
#include "ftl/ftl_journal.h"
#include "ftl/ftl_load_alignment.h"
#include "ftl/ftl_nrw.h"
#include "ftl/ftl_seq_table_api.h"
#include "ftl/ftl_xzip_api.h"
#include "ftl/ftl_xcut_api.h"
#include "ftl/ftl_xfer_data_in_api.h"
#include "ftl/ftl_sustain_api.h"

#include "trim/ftl_trim_api.h"

#include "lpm/lpm_api.h"

#include "table/initinfo_vt/initinfo_vt.h"

#include "rs/rsmap.h"

#include "testflow/sim_spor/sim_spor_api.h"
#include "log/log_api.h"

#include "err_handle/err_handle_api.h"
#include "cpu/cpu_cache_api.h"
#include "math_op.h"
#include "drive_log/drive_log_api.h"
#include "media_scan/media_scan_api.h"


/***************************
	Private Define & Enum
****************************/
#define XFER_DATA_IN_FLOW_CNT_EN (FALSE)
#define FILL_DUMMY_IN_FRAME_FAIL (0xFF)   //invalid value for return ubRemainSectorInFrame
#define XFER_IN_FILL_DUMMY_BY_FRAME_THRESHOLD	(3)
#define XFER_IN_XZIP_INSERT_COP1_FORCE_CLEAR_CNT_THRESHOLD (0x800)
#define XFER_IN_XZIP_HIT_MAX_TO_AVERAGE_FACTOR (16)

#define M_GET_WCQ(SQ_CNT) ((WriteCQInfo_t *)M_DB_GET_QBODY_PTR( DB_BMU_WR_CQ, SQ_CNT))

#define M_POP_WCQ()	do{\
						M_DB_TRIGGER_READ_CNT(DB_BMU_WR_CQ, 1);\
					}while(0)

typedef enum {
	PROGRAM_TYPE_INIT,
	PROGRAM_NORMAL_DATA,
	PROGRAM_JOURNAL,
} ProgramTypeEnum_t;


typedef enum {
	MODE_NORMAL_DATA,
	MODE_NORMAL_JOURNAL_DATA,
	MODE_DUMMY_DATA
} InsertCOP0ModeEnum_t;

#endif /* _FTL_XFER_DATA_IN_H_ */
