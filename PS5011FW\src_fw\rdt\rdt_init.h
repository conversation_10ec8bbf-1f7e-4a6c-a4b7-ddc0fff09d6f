/*
 * rdt_init.h
 *
 *  Created on: 2020Äê2ÔÂ14ÈÕ
 *      Author: user
 */

#ifndef SRC_FW_RDT_RDT_INIT_H_
#define SRC_FW_RDT_RDT_INIT_H_

#include "typedef.h"
#define MAX_SYSTEM_BLOCK_COPY               (2)
#define MAX_SYSTEM_BLOCK                    (MAX_SYSTEM_BLOCK_COPY * 2) // 2ROOT 2BBT

#define RDT_CONTROLLER_FATAL_TMPERATURE_THRESHOLD (120)

#define MAX_CODE_BLOCK_COPY                 (8)

#define MAX_SYS_BLOCK_COPY                  (2)

#define MAX_PRODUCT_HISTORY_COPY			(2)

#define MAX_LOG_BLOCK_NUM                   (2)
#define MAX_BACKUP_LOG_BLOCK_NUM            (2)

#define PH_VALID_UNIT                       (50)

#define HAVE_LINK                    		(0x00)
#define NO_LINK_W_LPM                		(0x01)
#define NO_LINK_WO_LPM               		(0x02)
#define HAVE_INITIAL_FAIL					(0x03)

#define RDT_TEST_FLOW_FINISH_INDEX          (0xFF)

//for flash only no record
#define ENABLE_RDT_NO_LOG					(FALSE)

//for FPGA LED
#define ENABLE_FPGA_LED_DISPLAY				(FALSE)

//Micron Record Bec Histogram Request
#define BEC_HISTOGRAM_LIMIT_MAX     (256)
#define BLK_NUM_OF_CONTINUE_READ	(8)
#define MAX_NUMBER_OF_DIE_PER_CE    (4)

//config test pattern enable or not
#define ENABLE_RDT_SRAM_DBUF_DMAC_COPY_PATTERN 	(TRUE && !ICE_MODE_EN)
#define ENABLE_RDT_SRAM_DMAC_TEST 				(TRUE)
#define ENABLE_RDT_SRAM_IRAM_TEST_PATTERN 		(TRUE)
#define ENABLE_RDT_SRAM_RS_ENC_TEST 			(TRUE)
#define ENABLE_RDT_SRAM_RS_BPT_RAM_TEST_PATTERN (TRUE)
#define ENABLE_RDT_BMU_TEST_PATTERN				(TRUE)
#define ENABLE_RDT_DELEGATE_CQ_IC_PATTERN		(FALSE)
#define ENABLE_RDT_SRAM_ZIP_TEST 				(TRUE)
#define ENABLE_RDT_COP1_TEST					(TRUE)
#define ENABLE_RDT_XZIP_TEST 					(TRUE)
#define ENABLE_RDT_SEC_TEST 					(TRUE)

#define ENABLE_RDT_SRAM_OPTS_TEST_PATTERN		(FALSE)

#define ENABLE_DMAC_CRC							(FALSE)

#define RDT_TT_LOG              				(1)

#if RDT_TT_LOG
#define MP_COMPATIBILITY_RDT_TT_LOG  (BIT0)
#define RDT_TT_LOG_LENGTH                    (TT_TMT_RESUME + 6)  //  u0 show parsing how many u32, u1-u8 show tt state, u9-u12 shows tt state time.
#define MP_COMPATIBILITY_RDT_VUC_DUMP_TABLE  (BIT1)
#define MP_COMPATIBILITY_RDT_PREST_TEST      (BIT3)
#define MP_COMPATIBILITY_RDT_SCANWIN         (BIT4)
#endif

#define RDT_BGASSD_GET_INFO                 (0)
#define BGASSD_SOCKET_ID_LENGTH             (16)
#define RDT_TEST_FLOW_MAX                   (40)
#define IO_EXPANDER_Device_Address          (0x20) //IO_EXPANDER
#define POWER_ID_REGISTER                   (0x00)
#define EEPROM_Device_Address_1             (0x50) //EEPROM
#define EEPROM_Device_Address_2             (0x51) //EEPROM
#define EEPROM_Device_Address_3             (0x52) //EEPROM
#define EEPROM_Device_Address_4             (0x53) //EEPROM
#define SOCKET_ID_REGISTER                  (0x00)


#define CONFIG_GPIO_DEBUG_ENABLE				(FALSE)
#define ENABLE_RDT_ERASE_PAGE_CHECK				(FALSE)
#define CONFIG_RDT_RRT_CHECK					(1)

//temperature config
#define NEGATIVE_TEMP       (0)

#define RDT_RECORD_TEMPERATURE (1)

#define RDT_THERMAL_DETECT  (1)

#define RDT_NEW_THERMAL_DETECT_FLOW (1) //modify thermal detect method from SSDRDT-2179 @20211206

#define RDT_BGASSD_PMIC_CONTROL  (FALSE) //Use GPIO7,8 to control BGASSD external PMIC
#define COREPOWER_REGISTER       (0x04) //CH2 VID register
#define COREPOWER_SEL_SHIFT      (2)

#define RDT_ERASEPAGE            (TRUE)
#define ERASEPAGE_COUNT_ZERO     (20)


// RDT_RECORD_TEMPERATURE
#define MAX_THERMAL_SENSOR  (2) //Internal(CTRL) + External thermal sensor
#define MAX_TEMP_RECORD     (MAX_THERMAL_SENSOR + 64) //for mp parsing based on E12 64CE // E13 actually 32CE MAX_PHY_CE_NUM

// RDT_THERMAL_DETECT
#define PRE_DETECT_POLLING_TIME         (10)                                                    // polling /10 sec
#define PRE_DETECT_FAIL_MINUTE          (10)                                                    // record 10(min) temperature
#define PRE_DETECT_RECORD_TIMES         (60 / PRE_DETECT_POLLING_TIME)                          // X times = 1 min
#define PRE_DETECT_FAIL_TIMES           (60 * PRE_DETECT_FAIL_MINUTE / PRE_DETECT_POLLING_TIME) // 10 min timeout fail
#define PRE_DETECT_FORCE_TIMES          (60 * 5 / PRE_DETECT_POLLING_TIME)                      // 5 min timeout to set criteria

#define DETECT_POLLING_INTERVAL_SEC     (1)
#define DETECT_RECORD_INTERVAL_SEC      (60)

#if RDT_RECORD_ECC_DISTRIBUTION
typedef struct nand_team_rdt_err_dis_log_struct	EDL_LOG_STRUCT, *EDL_LOG_STRUCT_PTR;
struct nand_team_rdt_err_dis_log_struct {
	U8 slc_mode;				 // Byte 0
	U8 reserved0;                // Byte 1
	U8 flash_ce;				 // Byte 2
	U8 flash_channel;			 // Byte 3
	U8 flash_die;				 // Byte 4
	U8 flash_plane;				 // Byte 5
	U16 flash_block;			 // Byte [6:7]
	U16 flash_page_max_ecc;      // Byte [8:9]
	union {
		struct {
			U16 Error_Distribution[256];
			U8 error_page_bit_map[488];//WL0~3903
		} HB;//Only HB retry
		struct {
			U16 Error_Distribution[310];
			U8 error_page_bit_map[380];//WL0~3039
		} SB0;//SB retry + LDPC mode 0
		struct {
			U16 Error_Distribution[290];
			U8 error_page_bit_map[420];//WL0~3359
		} SB1;//SB retry + LDPC mode 1
		struct {
			U16 Error_Distribution[270];
			U8 error_page_bit_map[460];//WL0~3679
		} SB2;//SB retry + LDPC mode 2
		struct {
			U16 Error_Distribution[256];
			U8 error_page_bit_map[488];//WL0~3903
		} SB3_4_5_6;//SB retry + LDPC mode 3/4/5/6
		struct {
			U16 Error_Distribution[380];
			U8 error_page_bit_map[240];//WL0~1919
		} SB7;//SB retry + LDPC mode 7
		struct {
			U16 Error_Distribution[360];
			U8 error_page_bit_map[280];//WL0~2239
		} SB8;//SB retry + LDPC mode 7

		struct {
			U16 RDY_Distribution[500];
		} RB;//R/B Busy time distribution ( PageCnt/(x us) )

	};//total 1000bytes
	U16 error_had_happened : 1;	 // Byte [1010:1011]
	U16 error_page_num : 15; // Byte [1010:1011]
	U8 ldpc_enable_SB : 1; //0->not use softbit retry, 1->using softbit retry
	U8 ldpc_mode : 7;//LDPC mode 0~8
	U8 test_loop;
	U8 test_cycle;
	U8 reserved1[9];
};
#endif

#if (HOST_MODE == NVME)
#define HOST_IF_IS_NVME						(1)
#else
#define HOST_IF_IS_NVME						(0)	//for AHCI
#endif

#define ENABLE_FIND_BLOCK_FAST				(0)

#define ENABLE_CHECK_THERMAL				(0)


enum RDT_INIT_FLOW_FAIL {
	RDT_NO_ERROR = 0,						//0
	NO_BBM_FAIL,							//1
	LOAD_BAD_TABLE_FAIL,					//2
	NO_ENOUGH_GOOD_BLOCK_FAIL,				//3
	EARLY_BAD_COUNT_NOT_MATCH_FAIL,			//4
	COP0_INT_FAIL,							//5
	SRAM_TEST_FAIL,							//6
	RS_TAG_INIT_FAIL,						//7
	NO_CE_EXIST,							//8
	POWERON_DBUF_PARITY_ERROR,				//9
	LCA_COMPARE_FAIL,						//10
};

typedef struct rdt_param_struct     RDT_PARAM_STRUCT,   *RDT_PARAM_STRUCT_PTR;
struct rdt_param_struct {
	U8 rdt_info_header[3]; // Bytes [0:2]
	U8 reserved_3; // Bytes [3:3]
	U32 host_link_time; // Bytes [4:7]
	struct {
		U8 skip_pto_detection : 1;
		U8 skip_last_erase : 1;
		U8 reserved	: 6;
	} rdt_op_option; // Bytes [8:8]
	struct {
		U8 flash_tlc_test_enable : 1;
		U8 flash_slc_test_enable : 1;
		U8 flash_tlc_rdy_test_enable : 1;
		U8 flash_slc_rdy_test_enable : 1;
		U8 flash_retry_cmd_verify_test_enable : 1;
		U8 reserved	: 3;
	} flash_test_enable; // Bytes [9:9]
	U8 sram_test_enable; // Bytes [10:10]
	U8 dram_test_enable; // Bytes [11:11]
	U16 flash_slc_test_loop; // Bytes [12:13]
	U16 flash_slc_test_cycle; // Bytes [14:15]
	U16 flash_tlc_test_loop; // Bytes [16:17]
	U16 flash_tlc_test_cycle; // Bytes [18:19]
	U16 sram_test_loop; // Bytes [20:21]
	U16 sram_test_cycle; // Bytes [22:23]
	U16 dram_test_loop; // Bytes [24:25]
	U16 dram_test_cycle; // Bytes [26:27]
	U8 flash_tlc_skip_program_status_check_loop; // Bytes [28:28]
	U8 flash_slc_skip_program_status_check_loop; // Bytes [29:29]
#if RDT_RECORD_ECC_DISTRIBUTION
	struct {
		U16 record_edl_enable : 1;
		U16 record_edl_by_1P_read : 1;
		U16 single_ce_test_enable : 1;
		U16 reserved : 13;
	} rdt_record_ecc_distribution;
#else
	U8 reserved_30[2]; // Bytes [30:31]
#endif
	union {
		struct {
			U8 fast_flash_test : 1;
			U8 fast_ctl_test : 1;
			U8 skip_switch_flash_mode_test : 1;
			U8 skip_flash_retry_function_test : 1;
			U8 skip_flash_busy_time_test : 1;
			U8 fast_sram_test : 1;
			U8 skip_ic_pattern : 1;
			U8 skip_hv_pattern : 1;
		} rdt_fast_mode; // Bytes [32:32]
		U8 rdt_fast_mode_all;
	};
	U16 uwRDYTestUnitCnt;//Bytes [33:34]
	U8 reserved_35[4]; // Bytes [35:38]
	U8 UNC_NoRetry; //Bytes[39:39]
	U8 reserved_40[4]; // Bytes [40:43]
	U16 slc_ecc_bit_threshold; // Bytes [44:45]
	U8 reserved_46[4]; // Bytes [46:49]
	struct {
		U8 read_retry_enable        : 1;
		U8 reserved	: 7;
	} slc_read_error_option; // Bytes [50:50]
	U8 reserved_51[5]; // Bytes [51:55]
	U16 tlc_ecc_bit_threshold; // Bytes [56:57]
	U8 reserved_58[4]; // Bytes [58:61]
	struct {
		U8 read_retry_enable : 1;
		U8 reserved	: 7;
	} tlc_read_error_option; // Bytes [62:62]
	U8 micron_flash_tlc_dummy_loop; // Bytes [63:63]
	U8 micron_flash_slc_dummy_loop; // Bytes [64:64]
	U8 micron_bec_histogram_limit_lower; // Bytes [65:65]
	U8 micron_bec_histogram_limit_upper; // Bytes [66:66]
	U8 micron_info_ecc_threshold; // Bytes [67:67] (80) Num of bits from which start taking fomInfoData
	U8 micron_bec_histogram_enable; // Bytes [68:68]
	U8 over_rdy_detect_enable;	// Bytes [69:69]
	U16 uwSLCEraseRDYThrd;	// Bytes [70:71]
	U16 uwSLCProgRDYThrd;	// Bytes [72:73]
	U16 uwTLCEraseRDYThrd;	// Bytes [74:75]
	U16 uwTLCProgRDYThrd;	// Bytes [76:77]
	U8 reserved_78[47]; // Byte [78:124] //N48 add micron_bec_histogram_enable byte[68]
	U8 ubScanPageRegisterEccThres;//125
	U8 ubScanWindowEccThres;//126
	//127
	union {
		struct {
			U8 btScanWindow1ByPass : 1;
			U8 btScanWindow2ByPass : 1;
			U8 btScanWindowByPassRefWinResult : 1;
			U8 btScanWindowByPassDQTrain : 1;
			U8 btScanWindowReserved : 4;
		} ScanWindowOption; // Bytes [127:127]
		U8 ScanWindowOption_All;
	};

	U8 psid[32]; // Bytes [128:159]

#if RDT_RETRY_CMD_VERIFY
	U8 reserved_160[168];	// Bytes [160:331]
	U16 uwMaxECCBit;
	U32 ulCountOne[3];
	U32 ulReadResult    : 1;
	U32 ulRetry         : 1;
	U32 ulRetryCountOne : 30;
#else
	U8 reserved_160[186]; // Bytes [160:345]
#endif
	U16 tlc_special_test_cycle; // Bytes [346:347]
	U16 tlc_ecc_log_interval; // Bytes [348:349]
	U16 tlc_block_quantity; // Bytes [350:351]
	U16 tlc_start_select_block; // Bytes [352:353]
	U16 tlc_end_select_block; // Bytes [354:355]
	U8 tlc_ecc_threshold; // Bytes [356:356]
	U8 slc_ecc_threshold; // Bytes [357:357]
	U8 special_test_option; // Bytes [358:358] 0:do Random test, 1:Record_tProg, 2:do baking, 3:Sample Block Mark Bad, 4:log Max ECC, 5:PortID, 6:DPS ON, 7:OnlyCheckEarlyBad
	U8 tlc_special_test_wait_time; // Bytes [359:359]
	U8 slc_special_test_wait_time; // Bytes [360:360]
	U8 slc_special_test_cycle; // Bytes [361:361]
	U8 slc_ecc_log_interval; // Bytes [362:362]
	U8 enable_portID; // Bytes [363:363]
	U16 slc_block_quantity; // Bytes [364:365]
	U16 slc_start_select_block; // Bytes [366:367]
	U16 slc_end_select_block; // Bytes [368:369]
	U8 reserved_370[2]; // Bytes [370:371]
	U8 ambient_temperature; // Bytes [372:372]
	U8 reserved_373[7]; // Bytes [373:379]
	U8 hv_pattern_test_enable; // Bytes [380:380]
	U8 flash_tlc_read_verify_interval; // Bytes [381:381]
	U8 reserved_382; // Bytes [382:382]
	U8 flash_slc_read_verify_interval; // Bytes [383:383]
	U8 flash_partial_test_enable; // Bytes [384:384]
	U16 flash_test_start_unit; // Bytes [385:386]
	U16 flash_test_end_unit; // Bytes [387:388]
	U8 ctl_fatal_temperature; // Bytes [389:389]
	U8 erasepage_count_zero_thrd; // Bytes [390:390]
	U8 reserved_391[6]; // Bytes [391:396]
	U8 voltage_setting; // Bytes [397:397]
	U8 reserved_398[42]; // Bytes [398:439]
	struct {
		U8 keep_logs_into_erl_and_dbt  : 1;
		U8 check_result_and_modify_led : 1;
		U8 check_result_count_early    : 1;
		U8 enable_rdt_ending_led       : 1;
		U8 reserved                    : 4;
	} china_special_setting; // Bytes [440:440]
	U8 record_retry_pass; // Bytes [441:441]
	U16 check_result_bad_threshold; // Bytes [442:443]
	U16 check_result_erase_fail_threshold; // Bytes [444:445]
	U16 check_result_program_fail_threshold; // Bytes [446:447]
	U16 check_result_read_fail_threshold; // Bytes [448:449]
	U16 check_result_bad_per_plane_threshold; // Bytes [450:451]
	U16 check_result_bad_per_die_threshold; // Bytes [452:453]
	U8 retention_flow; // Bytes [454:454]
	U8 rdt_gpio_control_flow_cycle_count; // Bytes [455:455]
	U32 flash_test_timeout_sec; // Bytes [456:459]
	U16 check_temperature_timeout_sec; // Bytes [460:461]
	U8 reserved_462[50]; // Bytes [462:511]
} __attribute__((packed)); // Do not optimize


typedef struct rdt_system_block_struct RDT_SYSTEM_BLOCK_STRUCT, *RDT_SYSTEM_BLOCK_STRUCT_PTR;
struct rdt_system_block_struct {
	//U32 code_block_addr[MAX_CODE_BLOCK_COPY];
	U32 bbm_block_addr[MAX_SYSTEM_BLOCK_COPY];
	U32 code_pointer_block_addr[MAX_CODE_BLOCK_COPY]; //Each CH's CE0 contains 2 code blks
	U32 sys_block_addr[MAX_SYS_BLOCK_COPY];
	U32 ph_block_addr[MAX_PRODUCT_HISTORY_COPY]; //??
	//U8 code_block_cnt;
	U8 bbm_block_cnt;
	U8 code_pointer_block_cnt;
	U8 sys_block_cnt;
	U8 ph_block_cnt;
};


typedef struct rdt_saving_info_struct   RDT_SAVING_INFO_STRUCT, *RDT_SAVING_INFO_STRUCT_PTR;
struct rdt_saving_info_struct {
	//system info need
	U32 err_num_cnt;
	U32 wait_time_to_running; //second
	U8  sram_test_time; // minute
	U8  dram_test_time; // minute
	U16 tlc_test_time; // minute
	U16 tlc_max_ecc_bit;
	U16 slc_test_time; // minute
	U16 slc_max_ecc_bit;
	U16 max_bad_cnt_per_pln;
	U16 flash_early_bad_cnt;
	U16 flash_later_bad_cnt;
	//RDT_EACH_TEST_STATUS each_test_staus;
	U8  flash_mode; // 0: TLC  1: SLC
	U8  test_cycle;
	U8  test_loop;
	U8  die;
	U8  bank;
	U8  ch;
	U8  pln;
	U16 block;
	U16 page;
	U32 ulTotalTestUnits;
	U32 ulCountTestUnits;
	U16 uwPercentMask;
	U8 ubRDYAbnormalFlag;	//Reip
	U8 rev[17];
};

typedef struct rdt_base_struct              RDT_BASE_STRUCT, *RDT_BASE_STRUCT_PTR;
struct rdt_base_struct {
	//sram buffer
	U32 shr_buf_start;
	U32 shr_buf_end;

	//sram buffer for log
	U32 all_log_base;
	U32 all_log_size;

	//sram buffer for random block/page list
	U32 rand_block_list_base;
	U32 rand_block_list_size;
	U32 rand_page_list_base;
	U32 rand_page_list_size;

	// zip buffer
	U32 zip_buffer_base;
	U32 zip_buffer_size;

	// sec buffer
	U32 sec_buffer_base;
	U32 sec_buffer_size;

	//rs parity buffer buffer
	U32 rs_parity_buffer_base;
	U32 rs_parity_buffer_size;

	//temperature buffer RDT_RECORD_TEMPERATURE
	U32 temperature_base;
	U32 temperature_size;

	//sram buffer for rw buffer
	U32 sram_rw_buf_base;
	U32 sram_rw_buf_size;

	// sram buffer for error bit saving for each plane
	U32 ecc_for_plane_base;
	U32 ecc_for_plane_size;

	//sram buffer for error bit saving for each cop0 tag
	U32 ecc_for_callback_base;
	U32 ecc_for_callback_size;

	// dram buffer for rw buffer
	U32 dram_rw_buf_base;
	U32 dram_rw_buf_size;

	//dram buffer for temp DBT log saving(no use now)
	//U32 tmp_log_base;
	//U32 tmp_log_size;

};

typedef struct {
	U16 info;
	U32 interrupt;
	U64 db_msg;
	U32 time;
	U16 ctl_temperature;
	U8 rdt_state;
	U8 rdt_flash_test_loop;
	U8 rdt_flash_test_cycle;
} RDT_API_ASSERT_FAIL_LOG_STRUCT;

#if 0	//Carrie
typedef struct rdt_rtt_struct            RDT_RTT_STRUCT, *RDT_RTT_STRUCT_PTR;
struct rdt_rtt_struct {
	U32 *rtt_isr_fp;
	U8  timer_enable;
	U8  uart_link;

	//led
	U8  rdt_led_flow_state;
	U8  rdt_GPIO8_flash;
	U32 rdt_flash_cnt;
	U32 rdt_thermal_cnt;
};
#endif

typedef union rdt_error_bitmap  RDT_ERROR_BITMAP, *RDT_ERROR_BITMAP_PTR;
union rdt_error_bitmap {
	U32 u32;
	struct {
		U8 dbt_not_found				: 1;
		U8 erl_not_found				: 1;
		U8 tdl_not_found				: 1;
		U8 rml_not_fount				: 1;
		U8 ic_pattern_not_complete		: 1;
		U8 dram_test_not_complete		: 1;
		U8 sram_test_not_complete		: 1;
		U8 tlc_test_not_complete		: 1;

		U8 slc_test_not_complete		: 1;
		U8 find_not_enough_good_block	: 1;
		U8 rdt_init_flow_fail			: 4;
		U8 rsv : 2;

		U8 error_type_in_rml;
		U8 error_state_in_rml;

	} fields;
};

typedef struct rdt_buf_struct              RDT_BUF_STRUCT,          *RDT_BUF_STRUCT_PTR;
struct rdt_buf_struct {
	U32 data_buf_base;
	U32 data_buf_size;
	/*U32 spr_buf_base;
	U32 spr_buf_size;*/
};

typedef struct rdt_bbm_struct              RDT_BBM_STRUCT,          *RDT_BBM_STRUCT_PTR;
struct rdt_bbm_struct {
	U32 data_bbm_start_page;
	U32 data_bbm_size;
	U16 data_bbm_page_num;
};

typedef struct rdt_parity_struct           RDT_PARITY_STRUCT,       *RDT_PARITY_STRUCT_PTR;
struct rdt_parity_struct {
	U32 STS[32];
	U32 parity_error;
};


#if ENABLE_RDT_DELEGATE_CQ_IC_PATTERN

typedef struct busy_task BUSY_TASK, *BUSY_TASK_PTR;
struct busy_task {
	U8 ubTestResult;
	U8 ubTestEnable;
};

extern BUSY_TASK gbz_task;

#define BZ_TASK_INIT(ENABLE)	do { \
	        gbz_task.ubTestResult = 0;\
			gbz_task.ubTestEnable = ENABLE;\
}while(0)

#define BZ_GET_TEST_ENABLE()	( gbz_task.ubTestEnable )
#define BZ_GET_TEST_RESULT()	( gbz_task.ubTestResult )
#define BZ_SET_TEST_RESULT(x)	( gbz_task.ubTestResult = x )
#endif

#endif /* SRC_FW_RDT_RDT_INIT_H_ */
