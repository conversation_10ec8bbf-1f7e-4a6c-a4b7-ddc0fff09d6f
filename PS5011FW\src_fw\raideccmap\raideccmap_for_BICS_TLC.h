/****************************************************************************
*
*  Copyright (C) 2000-2019, Phison Electronics Corp., Inc.
*  All rights reserved
*
*  The content of this document is confidential and shall be applied
*  subject to the terms and conditions of the license agreement and
*  other applicable laws. Any unauthorized access, use or disclosure
*  of this document is strictly prohibited and may be punishable
*  under laws.
*
*  raideccmap_for_BICS_TLC.h
*
*
*
****************************************************************************/

#ifndef RAIDECCMAP_FOR_BICS_TLC_H_
#define RAIDECCMAP_FOR_BICS_TLC_H_
/*
 * ---------------------------------------------------------------------------------------------------
 *   header files
 * ---------------------------------------------------------------------------------------------------
 */
#include "common/math_op.h"
#include "fw_common.h"
#include "fw_vardef.h"
#include "typedef.h"

/*
 * ---------------------------------------------------------------------------------------------------
 *  definitions
 * ---------------------------------------------------------------------------------------------------
 */
//**********************
// Data Parity Ratio
//**********************
// Not a formal ratio definition
// "% gubPlanesPerSuperPage" only works under odd CE
#define RAIDECCMAP_DATA_PARITY_RATIO		(128 - (128 % gubPlanesPerSuperPage))  // 127 : 1

#define RAIDECCMAP_TABLE_MIN_PLANE_NUM_PER_GROUP	(8)
#define RAIDECCMAP_TALBE_DATA_PARITY_RATIO			((1 == gubCENumber) ? RAIDECCMAP_TABLE_MIN_PLANE_NUM_PER_GROUP : gubPlanesPerSuperPage)


//**********************
// Plane Protect
//**********************
#define RAIDECCMAP_PLANE_PROTECT_NUM_BY_FLASH		(MULTI_PLANE_PROTECTION ? (2) : (1))


//**********************
// Flash Related
//**********************
#define RAIDECCMAP_TAG_SHIFT_NUM_FOR_SLC_STRING   (1)
#define RAIDECCMAP_TAG_SHIFT_NUM_FOR_XLC_STRING   (3)

#define RAIDECCMAP_MAX_WINDOW_SIZE		(3)


//**********************
// Basic Tag Num
//**********************
#define RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM       (4)
#define RAIDECCMAP_1WL_PROTECTION_IN_XLC_PARITY_TAG_IDX_NUM       (12)

#define RAIDECCMAP_2WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM       (RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM * 2)  // 8
#define RAIDECCMAP_2WL_PROTECTION_IN_XLC_PARITY_TAG_IDX_NUM       (RAIDECCMAP_1WL_PROTECTION_IN_XLC_PARITY_TAG_IDX_NUM * 2)  // 24

#define RAIDECCMAP_SLC_WORDLINE_PROTECT_NUM						(1)
#define RAIDECCMAP_XLC_WORDLINE_PROTECT_NUM						(2)

// Required tag num without switch num
#define RAIDECCMAP_SLC_DATA_BASIC_TAG_NUM_BY_FLASH      (RAIDECCMAP_1WL_PROTECTION_IN_SLC_PARITY_TAG_IDX_NUM * RAIDECCMAP_SLC_WORDLINE_PROTECT_NUM * RAIDECCMAP_PLANE_PROTECT_NUM_BY_FLASH)  // 4 * 1 * 2
#define RAIDECCMAP_XLC_DATA_BASIC_TAG_NUM_BY_FLASH      (RAIDECCMAP_1WL_PROTECTION_IN_XLC_PARITY_TAG_IDX_NUM * RAIDECCMAP_XLC_WORDLINE_PROTECT_NUM * RAIDECCMAP_PLANE_PROTECT_NUM_BY_FLASH)  // 12 * 2 * 2
#define RAIDECCMAP_TABLE_DATA_BASIC_TAG_NUM_BY_FLASH	(1)


//**********************
// Switch Num
//**********************
#define RAIDECCMAP_SLC_DATA_TAG_SWITCH_NUM_BY_FLASH		(1)
#define RAIDECCMAP_XLC_DATA_TAG_SWITCH_NUM_BY_FLASH		(1)
#define RAIDECCMAP_TABLE_DATA_TAG_SWITCH_NUM_BY_FLASH	(2)


//**********************
// FW Tag Related
//**********************
// Directly defined in raideccmap_api.h


//**********************
// In RAM Tag Num Related (imply buffer usage)
//**********************
// BUFFER Keep
#define RAIDECCMAP_DECODE_GROUP_SIZE_BY_FLASH				(4)
// SLC and XLC GR share the same group, so separately define them
#define RAIDECCMAP_SLC_GR_IN_RAM_TAG_NUM_BY_FLASH			(RAIDECCMAP_SLC_DATA_BASIC_TAG_NUM_BY_FLASH)  // 2P 1WL => 2 * 4 = 8
#define RAIDECCMAP_XLC_GR_IN_RAM_TAG_NUM_BY_FLASH			(6)
#define RAIDECCMAP_GR_GROUP_SIZE_BY_FLASH					(MAX(RAIDECCMAP_SLC_GR_IN_RAM_TAG_NUM_BY_FLASH, RAIDECCMAP_XLC_GR_IN_RAM_TAG_NUM_BY_FLASH))
// RAIDECCMAP_GC_AND_COPY_UNIT_GROUP_SIZE_BY_FLASH should be the same as "RAM_GCGR_RAIDECC / FRAMES_PER_PAGE" in bmu_api.h
#define RAIDECCMAP_GC_AND_COPY_UNIT_GROUP_SIZE_BY_FLASH		(3 * 2)
#define RAIDECCMAP_TABLE_GROUP_SIZE_BY_FLASH				(1)
#define RAIDECCMAP_INITINFO_GROUP_SIZE_BY_FLASH				(1)
#define RAIDECCMAP_XLC_GR_IN_RAM_TAG_NUM_BY_FLASH				(6)  // Actually useless
// No need to concern decode part
#define RAIDECCMAP_TOTAL_IN_RAM_GROUP_SIZE_BY_FLASH			(RAIDECCMAP_GR_GROUP_SIZE_BY_FLASH + RAIDECCMAP_GC_AND_COPY_UNIT_GROUP_SIZE_BY_FLASH + RAIDECCMAP_TABLE_GROUP_SIZE_BY_FLASH + RAIDECCMAP_INITINFO_GROUP_SIZE_BY_FLASH)


//**********************
// Struct Size Related
//**********************
#define RAIDECCMAP_PARITYMAP_NUM				(353) //Duson Porting V7 RDT Modify

// 24
#define RAIDECCMAP_TAG_MGR_ENTRY_NUM	(\
	RAIDECCMAP_SLC_GR_IN_RAM_TAG_NUM_BY_FLASH * RAIDECCMAP_SLC_DATA_TAG_SWITCH_NUM_BY_FLASH +\
	RAIDECCMAP_XLC_GR_IN_RAM_TAG_NUM_BY_FLASH * RAIDECCMAP_XLC_DATA_TAG_SWITCH_NUM_BY_FLASH +\
	RAIDECCMAP_GC_AND_COPY_UNIT_GROUP_SIZE_BY_FLASH * RAIDECCMAP_XLC_DATA_TAG_SWITCH_NUM_BY_FLASH +\
	RAIDECCMAP_TABLE_GROUP_SIZE_BY_FLASH * RAIDECCMAP_TABLE_DATA_TAG_SWITCH_NUM_BY_FLASH +\
	RAIDECCMAP_INITINFO_GROUP_SIZE_BY_FLASH * RAIDECCMAP_TABLE_DATA_TAG_SWITCH_NUM_BY_FLASH\
)

#define RAIDECCMAP_OPEN_BLOCK_DESCRIPTION_NUM	(6)

/*
 * TODO:
 * Using RS define is not correct solution to evaluate number of parity.
 */
#if (D1_UNIT_EN)
#define RAIDECCMAP_SPOR_SCAN_GR_MAX_PARITY_NUM		(RAIDECCMAP_SLC_DATA_BASIC_TAG_NUM_BY_FLASH * RAIDECCMAP_SLC_DATA_TAG_SWITCH_NUM_BY_FLASH)
#else  /* (D1_UNIT_EN) */
#define RAIDECCMAP_SPOR_SCAN_GR_MAX_PARITY_NUM		(RAIDECCMAP_XLC_DATA_BASIC_TAG_NUM_BY_FLASH * RAIDECCMAP_XLC_DATA_TAG_SWITCH_NUM_BY_FLASH)
#endif  /* (D1_UNIT_EN) */

#define RAIDECCMAP_GR_VT_ARRAY_SIZE			(RAIDECCMAP_GR_GROUP_SIZE_BY_FLASH)
#define RAIDECCMAP_GC_VT_ARRAY_SIZE			(6)
#define RAIDECCMAP_TABLE_VT_ARRAY_SIZE		(RAIDECCMAP_TABLE_GROUP_SIZE_BY_FLASH)
#define RAIDECC_RESERVE_BYTE				(218)

/*
 * ---------------------------------------------------------------------------------------------------
 *  public prototypes
 * ---------------------------------------------------------------------------------------------------
 */

#endif /* _RAIDECCMAP_API_H_ */
