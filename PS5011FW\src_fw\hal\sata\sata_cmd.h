#ifndef _SATA_CMD_H_
#define _SATA_CMD_H_

#if (HOST_MODE == SATA)

// a
#include "aom/aom_api.h"
// i
#include "init/fw_init.h"

/*=====================================================================================
                                  Variable Definition
=====================================================================================*/
/* Cmd Spec Var */
#define DSM_ENTRY_CNT_PER_SECTOR                            (64)

/* SetFeature SubCommand */
#define ENABLE_WCACHE                                       (0x02) // Enable volatile write cache (see 7.43.3)
#define DISABLE_WCACHE                                      (0x82) // Disable volatile write cache (see 7.43.3)
#define SET_XFER_MODE                                       (0x03) // Set transfer mode (see 7.43.4)
#define     XFER_PIO_DEFAULT_MODE                               (0x00) // 0_0000b: PIO default mode or PIO default mode, disable IORDY
#define         XFER_PIO_DEFAULT_MODE_NORMAL                        (0x00) // 000b: PIO default mode
#define         XFER_PIO_DEFAULT_MODE_IORDY_DIS                     (0x01) // 001b: PIO default mode, disable IORDY
#define     XFER_PIO_FLOW_CONTROL_XFER_MODE                     (0x01) // 0_0001b: PIO flow control transfer mode
#define     XFER_MULTIWORD_DMA_MODE                             (0x04) // 0_0100b: Multiword DMA mode
#define     XFER_ULTRA_DMA_MODE                                 (0x08) // 0_1000b: Ultra DMA mode
#define ENABLE_APM                                          (0x05) // Enable the APM feature set (see 7.43.5)
#define DISABLE_APM                                         (0x85) // Disable volatile write cache (see 7.43.3)
#define ENABLE_ACTPM                                        (0x09) // Can't find Spec defintion, just copy from S11..
#define DISABLE_ACTPM                                       (0x89) // Can't find Spec defintion, just copy from S11..
#define ENABLE_SATA_FEATURE                                 (0x10) // Enable the APM feature set (see 7.43.5)
#define DISABLE_SATA_FEATURE                                (0x90) // Disable volatile write cache (see 7.43.3)
#define     SATA_FEATURE_AUTO_ACTIVATE                          (0x02) // 02h: DMA Setup FIS Auto-Activate optimization (see *********)
#define     SATA_FEATURE_DIPM_TRANSITION                        (0x03) // 03h: Device-initiated interface power state transitions (see *********)
#define     SATA_FEATURE_SSP                                    (0x06) // 06h: Software Settings Preservation (see *********)
#define     SATA_FEATURE_DEVICE_AP2S                            (0x07) // 06h: Device Automatic Partial to Slumber transitions (see *********)
#define     SATA_FEATURE_DEVSLP                                 (0x09) // 09h: Enable/Disable Device Sleep (see *********)
#define     SATA_FEATURE_PWDIS                                  (0x0B) // 0Bh: Enable/Disable Power Disable Feature (see **********)
#define DISABLE_RDLOOKAHEAD                                 (0x55) // Disable read look-ahead feature (see 7.43.12)
#define ENABLE_RDLOOKAHEAD                                  (0xAA) // Enable read look-ahead feature (see 7.43.12)
#define DISABLE_RTD                                         (0x66) // Disable reverting to power-on defaults (see 7.43.13)
#define ENABLE_RTD                                          (0xCC) // Enable reverting to power-on defaults (see 7.43.13)

/* DCO Command */
#define DCO_RESTORE                                         (0xC0)
#define DCO_FREEZE_LOCK                                     (0xC1)
#define DCO_IDENTIFY                                        (0xC2)
#define DCO_SET                                             (0xC3)
#define DCO_IDENTIFY_DMA                                    (0xC4)
#define DCO_SET_DMA                                         (0xC5)

/* Security Command */
#define SECURITY_SET_PASSWORD                               (0xF1) // Set the User password (see 4.20.3.2) or the Master password (see 4.20.3.3)
#define SECURITY_UNLOCK                                     (0xF2)
#define SECURITY_ERASE_PREPARE                              (0xF3)
#define SECURITY_ERASE_UNIT                                 (0xF4)
#define SECURITY_FREEZE_LOCK                                (0xF5)
#define SECURITY_DISABLE_PASSWORD                           (0xF6)

/* Sanitize SubCommand */
#define SANITIZE_STATUS_EXT                                 (0x0000)
#define SANITIZE_CRYPTO_SCRAMBLE_EXT                        (0x0011)
#define SANITIZE_BLOCK_ERASE_EXT                            (0x0012)
#define SANITIZE_OVERWRITE_EXT                              (0x0014)
#define SANITIZE_FREEZE_LOCK_EXT                            (0x0020)
#define SANITIZE_ANTIFREEZE_LOCK_EXT                        (0x0040)

/* AMAX SubCommand */
#define AMAX_GET_NATIVE_MAX_ADDRESS_EXT                     (0x0000)
#define AMAX_SET_ACCESSIBLE_MAX_ADDRESS_EXT                 (0x0001)
#define AMAX_FREEZE_ACCESSIBLE_MAX_ADDRESS_EXT              (0x0002)

/* Set Max SubCommand */
#define SET_MAX_SET_PASSWORD                                (0x01)
#define SET_MAX_LOCK                                        (0x02)
#define SET_MAX_UNLOCK                                      (0x03)
#define SET_MAX_FREEZE_LOCK                                 (0x04)
#define SET_MAX_SET_PASSWORD_DMA                            (0x05)
#define SET_MAX_UNLOCK_DMA                                  (0x06)

/* SMART SubCommand */
#define SMART_READ_DATA                                     (0xD0) // Return the Device SMART data to the host (see ATA8-ACS-3)
#define SMART_READ_THRESHOLD                                (0xD1) // Return the Device attribute thresholds to the host (see ATA-3)
#define SMART_EN_DIS_ATTRIBUTE_AUTOSAVE                     (0xD2) // Enable or disables the attribute autosave feature of the Device. (see ATA8-ACS-3)
#define SMART_SAVE_ATTRIBUTE_VALUE                          (0xD3) // Cause the Device to immediately save attribute values regardless of the state of the attribute autosave timer (see ATA-6)
#define SMART_EXECUTE_OFFLINE_IMMEDIATE                     (0xD4) // Cause the Device to initiate the set of activities that collect SMART data... (see ATA8-ACS-3)
#define SMART_READ_LOG                                      (0xD5) // Return the specified log to the host (see 7.46.2)
#define SMART_WRITE_LOG                                     (0xD6) // Cause the Device to write the specified number of log pages to the specified log (see 7.46.4)
#define SMART_ENABLE_OPERATION                              (0xD8) // Enable access to all available SMART capabilities within the Device (see ATA8-ACS-2)
#define SMART_DISABLE_OPERATION                             (0xD9) // Disable all SMART operations (see ATA8-ACS-2)
#define SMART_RETURN_STATUS                                 (0xDA) // Cause the Device to communicate the reliability status of the device to the host (see 7.46.3)
#define SMART_EN_DIS_ATTRIBUTE_AUTO_OFFLINE                 (0xDB) // (see S11)

/* SMART_EXECUTE_OFFLINE_IMMEDIATE SubCommand */
#define SMART_DST_EXECUTE_OFFLINE_ROUTINE                   (0x00)
#define SMART_DST_SHORT_SELFTEST_ROUTINE_OFF_LINE           (0x01)
#define SMART_DST_EXTENDED_SELFTEST_ROUTINE_OFF_LINE        (0x02)
#define SMART_DST_CONVEYANCE_SELFTEST_ROUTINE_OFF_LINE      (0x03)
#define SMART_DST_SELECTIVE_SELFTEST_ROUTINE_OFF_LINE       (0x04)
#define SMART_DST_ABORT_SELFTEST_ROUTINE                    (0x7F)
#define SMART_DST_SHORT_SELFTEST_ROUTINE_CAPTIVE            (0x81)
#define SMART_DST_EXTENDED_SELFTEST_ROUTINE_CAPTIVE         (0x82)
#define SMART_DST_CONVEYANCE_SELFTEST_ROUTINE_CAPTIVE       (0x83)
#define SMART_DST_SELECTIVE_SELFTEST_ROUTINE_CAPTIVE        (0x84)

/* Log Address & Page Size */
#define ADDR_GPL_LOG_DIRECTORY                              (0x00)
#define SIZE_GPL_LOG_DIRECTORY                              (1)
#define ADDR_SUMMARY_SMART_ERROR_LOG                        (0x01)
#define SIZE_SUMMARY_SMART_ERROR_LOG                        (1)
#define ADDR_COMPREHENSIVE_SMART_ERROR_LOG                  (0x02)
#define SIZE_COMPREHENSIVE_SMART_ERROR_LOG                  (51)
#define ADDR_EXT_COMPREHENSIVE_SMART_ERROR_LOG              (0x03)
#define SIZE_EXT_COMPREHENSIVE_SMART_ERROR_LOG              (64)
#define ADDR_DEVICE_STATISTICS_LOG                          (0x04)
#define SIZE_DEVICE_STATISTICS_LOG                          (8)
#define ADDR_SMART_SELFTEST_LOG                             (0x06)
#define SIZE_SMART_SELFTEST_LOG                             (1)
#define ADDR_EXT_SMART_SELFTEST_LOG                         (0x07)
#define SIZE_EXT_SMART_SELFTEST_LOG                         (1)
#define ADDR_SELECTIVE_SELFTEST_LOG                         (0x09)
#define SIZE_SELECTIVE_SELFTEST_LOG                         (1)
#define ADDR_NCQ_CMD_ERROR_LOG                              (0x10)
#define SIZE_NCQ_CMD_ERROR_LOG                              (1)
#define ADDR_SATA_PHY_EVT_CNT_LOG                           (0x11)
#define SIZE_SATA_PHY_EVT_CNT_LOG                           (1)
#define ADDR_IDENTIFY_DEVICE_DATA_LOG                       (0x30)
#define SIZE_IDENTIFY_DEVICE_DATA_LOG                       (9)
#define ADDR_CFA_LOG                                        (0x31)
#define SIZE_CFA_LOG                                        (4)
#define ADDR_HOST_SPECIFIC_LOG_MIN                          (0x80)
#define ADDR_HOST_SPECIFIC_LOG_MAX                          (0x9F)
#define SIZE_HOST_SPECIFIC_LOG                              (16)
#define NUM_HOST_SPECIFIC_LOG                               (ADDR_HOST_SPECIFIC_LOG_MAX - ADDR_HOST_SPECIFIC_LOG_MIN + 1) // 32

/* Trusted Computing feature set */
#define SATA_TRUSTED_NON_DATA                               (0x5B)
#define SATA_TRUSTED_RECEIVE                                (0x5C)
#define SATA_TRUSTED_RECEIVE_DMA                            (0x5D)
#define SATA_TRUSTED_SEND                                   (0x5E)
#define SATA_TRUSTED_SEND_DMA                               (0x5F)
#define SATA_SECURITY_PROTOCOL_INFO_LIST_LENGHT_IDX         (6)
#define SATA_SECURITY_PROTOCOL_INFO_LIST_CONTENT_IDX        (8)

/* Device Statistic Flags */
#define STATISTIC_DEVICE_STATISTIC_SUPPORT_BIT              (BIT7)
#define STATISTIC_VALID_VALUE_BIT                           (BIT6)
#define STATISTIC_NORMALIZED_STATISTIC_BIT                  (BIT5)
#define STATISTIC_SUPPORT_DSN_BIT                           (BIT4)
#define STATISTIC_MONITORED_CONDITION_MET_BIT               (BIT3)

/* HOST TABLE [ Addr: Offset (Size) ] */
// (Let write log addr: 09h & 80-9Fh allign 16K to avoid cross plane issue)
// 80h-9Fh: 0 ~ 511 ((0x9F - 0x80 + 1) * 16 = 32 * 16 = 512)
#define HOSTTABLE_HOST_SPECIFIC_LOG_OFFSET                  (0)
// 09h: 512 (1)
#define HOSTTABLE_SELECTIVE_SELFTEST_LOG_OFFSET             (HOSTTABLE_HOST_SPECIFIC_LOG_OFFSET                 + (NUM_HOST_SPECIFIC_LOG * SIZE_HOST_SPECIFIC_LOG)) // 0 + 512 = 512
// 07h: 513 (1)
#define HOSTTABLE_EXT_SMART_SELFTEST_LOG_OFFSET             (HOSTTABLE_SELECTIVE_SELFTEST_LOG_OFFSET            + SIZE_SELECTIVE_SELFTEST_LOG) // 512 + 1 = 513
// 06h: 514 (1)
#define HOSTTABLE_SMART_SELFTEST_LOG_OFFSET                 (HOSTTABLE_EXT_SMART_SELFTEST_LOG_OFFSET            + SIZE_EXT_SMART_SELFTEST_LOG) // 513 + 1 = 514
// 03h: 515 ~ 578 (64)
#define HOSTTABLE_EXT_COMPREHENSIVE_SMART_ERROR_LOG_OFFSET  (HOSTTABLE_SMART_SELFTEST_LOG_OFFSET                + SIZE_SMART_SELFTEST_LOG) // 514 + 1 = 515
// 02h: 579 ~ 629 (51)
#define HOSTTABLE_COMPREHENSIVE_SMART_ERROR_LOG_OFFSET      (HOSTTABLE_EXT_COMPREHENSIVE_SMART_ERROR_LOG_OFFSET + SIZE_EXT_COMPREHENSIVE_SMART_ERROR_LOG) // 515 + 64 = 579
// Total Host Table Offset
#define HOSTTABLE_TOTAL_SECTORCNT_NUM                       (HOSTTABLE_COMPREHENSIVE_SMART_ERROR_LOG_OFFSET     + SIZE_COMPREHENSIVE_SMART_ERROR_LOG) // 579 + 51 = 630

/*=====================================================================================
                                 Structure Definition
=====================================================================================*/
//security
typedef enum SATASecurityProtocol {
	SECURITY_PROTOCOL_INFO     = 0x00,
	SECURITY_PROTOCOL_TCG1     = 0x01,
	SECURITY_PROTOCOL_TCG2     = 0x02,
	SECURITY_PROTOCOL_IEEE1667 = 0xEE,
} SATASecurityProtocol_t;

typedef enum SATASecurityProtocolInfoSPSpecific {
	SECURITY_SUPPORTED_LIST = 0x0000,
	SECURITY_CERTIFICATE    = 0x0001,
} SATASecurityProtocolInfoSPSpecific_t;
/*=====================================================================================
                                    Macro Definition
=====================================================================================*/

/*=====================================================================================
                                  Function Declaretion
=====================================================================================*/
/* Sata_cmd.c */
AOM_SATA U8 CalculateCheckSum(U32 ulPbAddr, U32 ulBufSize);
AOM_SATA U8 CheckCheckSum(U32 ulBufAddr, U32 ulBufSize, U8 ubDataCheckSum);
AOM_SATA void CmdShouldNotBeSync(void);
AOM_SATA void CmdNotImplement(void);
AOM_SATA void SATACmdNOP(void);
AOM_SATA void FillIdentifyDeviceData(U32 ulBufAddr);
AOM_SATA void SATACmdIdentifyDev(void);
AOM_SATA void SATACmdSetFeature(void);
AOM_SATA void SATACmdSetMultipleMode(void);
AOM_SATA void SATACmdCheckPower(void);
AOM_SATA void SATACmdFlushCache(void);
AOM_SATA void SATACmdStandby(void);
AOM_SATA void SATACmdIdle(void);
AOM_SATA void SATACmdSleep(void);
AOM_SATA void SATACmdReadBuf(void);
AOM_SATA void SATACmdWriteBuf(void);
AOM_SATA void SATACmdWriteUNC(void);
AOM_SATA void SATACmdReadVerify(void);
AOM_SATA void SATACmdZeroExt(void);
AOM_SATA void SATACmdDataSetManagement(void);
AOM_SATA void SATACmdDLMC(void);
AOM_SATA void SATACmdSecurity(void);
AOM_SATA void SATACmdSanitize(void);
AOM_SATA void SATACmdTrustedNonData(void);
AOM_SATA void SATACmdTrustedReceive(void);
AOM_SATA void SATACmdTrustedSend(void);
AOM_SATA void SATACmdSyncRead(void);
AOM_SATA void SATACmdSyncReadSectorWithoutRetry(void);
AOM_SATA void SATACmdSyncWriteSectorWithoutRetry(void);
AOM_SATA_2 void SATACmdCFAWriteSector(void);
AOM_SATA_2 void SATACmdSMART(void);
AOM_SATA_2 void SATACmdReadLogExt(void);
AOM_SATA_2 void SATACmdWriteLogExt(void);
AOM_SATA_2 void SATACmdDCO(void);
AOM_SATA_2 void SATACmdDeviceDiagnostic(void);
AOM_SATA_2 void SATACmdRecalibrate(void);
AOM_SATA_2 void SATACmdSeek(void);
AOM_SATA_2 void SATACmdAMaxAddrConfig(void);
AOM_SATA_2 void SATACmdReadNativeMaxAddr(void);
AOM_SATA_2 void SATACmdSetMaxAddr(void);
AOM_SATA_2 void SATACmdInitDeviceParameter(void);

/* Sata_hpa.c */
AOM_SATA_2 void SATAInitResetHPA(U8 ubResetMode);
AOM_SATA_2 void SATAInsureHPAState(void);
AOM_SATA_2 void SATASetMaxSetPassword(U8 ubProtocol);
AOM_SATA_2 void SATASetMaxLock(void);
AOM_SATA_2 void SATASetMaxUnlock(U8 ubProtocol);
AOM_SATA_2 void SATASetMaxFreezeLock(void);
AOM_SATA_2 void SATASetMaxAddr(U8 ub48BitCmd);

/* Sata_dlmc.c */
AOM_SATA U8 SATADLMCContentCheck(U8 ubDLMCMode, U16 uwBufferOffset);
AOM_DLMC void SATADLMCFWCommit(U8 ubDLMCMode, U8 *ubSectorCntRegValue);

/* Sata_dco.c */
AOM_SATA_2 void SATADCORestore(void);
AOM_SATA_2 void SATADCOIdentify(SATADCOIDFYXferMode_t ubXferMode);
AOM_SATA_2 void SATADCOSet(SATADCOIDFYXferMode_t ubXferMode);

/* Sata_security.c */
AOM_SATA void SATASecurityInit(void);
AOM_SATA void SATACmdSecuritySetPassword(void);
AOM_SATA void SATACmdSecurityUnlock(void);
AOM_SATA void SATACmdSecurityEraseUnit(void);
AOM_SATA void SATACmdSecurityDisablePassword(void);

/* Sata_smart.c */
AOM_SATA_2 void SMARTReadSpecifiedData(U8 ubSubCommand);
AOM_SATA_2 void SMARTReturnStatus(void);
AOM_SATA_2 void ReadSpecifiedLogDispatcher(U8 ubLogAddr, U16 uwPageNum, U16 uwLogPageCnt);
AOM_SATA_2 void SMARTReadLog(void);
AOM_SATA_2 void WriteSpecifiedLogDispatcher(U8 ubWaitProgramData, U8 ubLogAddr, U16 uwPageNum, U16 uwLogPageCnt);
AOM_SATA_2 void SMARTWriteLog(void);
AOM_SATA_2 void SMARTExecuteOffLineImmediate(void);

/* sata_sanitize.c */
AOM_SATA void SATASanitizeErrorOutput(SATASanitizeDeviceErrorReason_t Reason);
AOM_SATA void SATASanitizeStatus(void);
AOM_SATA void SATASanitizeCryptoScramble(void);
AOM_SATA void SATASanitizeBlockErase(void);
AOM_SATA void SATASanitizeOverwrite(void);
AOM_SATA void SATASanitizeFreezeLock(void);
AOM_SATA void SATASanitizeAntiFreezeLock(void);

#endif /* (HOST_MODE == SATA) */

#endif /* _SATA_CMD_H_ */
