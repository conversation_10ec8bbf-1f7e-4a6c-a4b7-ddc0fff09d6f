
for /f "delims=" %%i in ('dir /b /a-d /s "*.bak"') do call del *.bak /f/q/s/a
for /f "delims=" %%i in ('dir /b /a-d /s "*.ncb"') do call del *.ncb /f/q/s/a
for /f "delims=" %%i in ('dir /b /a-d /s "*.suo"') do call del *.suo /f/q/s/a
for /f "delims=" %%i in ('dir /b /a-d /s "*.user"') do call del *.user /f/q/s/a
for /f "delims=" %%i in ('dir /b /a-d /s "*.sdf"') do call del *.sdf /f/q/s/a
for /f "delims=" %%i in ('dir /b /a-d /s "*.bsc"') do call del *.bsc /f/q/s/a
for /f "delims=" %%i in ('dir /b /a-d /s "*.obj"') do call del *.obj /f/q/s/a


pause