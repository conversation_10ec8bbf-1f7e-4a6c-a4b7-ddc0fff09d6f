/*

*/
#ifndef _FTL_SYNC_CMD_HANDLER_H_
#define _FTL_SYNC_CMD_HANDLER_H_

#include "typedef.h"
#include "symbol.h"
#include "fw_vardef.h"
#include "nvme_api/nvme/shr_hal_nvme.h"

#define ADMIN_CMD_VENDOR_POWER              (0xF4)

#define NVME_POWER_VENDOR_LOGID             (0xF0)
#define NVME_VENDOR_AER_TYPE                (0x07)

#define NUMBER_OF_POWER_STATES          (0x05)          // 1' base //From 5008
#define NVME_MAX_AER_NUM                    (4)
//==============================================================================
// Defined Values and Capabilities
//==============================================================================

typedef enum SyncWriteStateEnum {
	BYTE_SYNC_W_INIT,
	BYTE_SYNC_W_ALLOC_BMU,
	BYTE_SYNC_W_WAIT_ALLOC_BMU,  //Should not change the order of this one and next one
	BYTE_SYNC_W_ALLOC_BMU_DONE,
	BYTE_SYNC_W_WAIT_TD,
	BYTE_SYNC_W_TD_DONE,
	BYTE_SYNC_W_FINISH  //Trigger and insert to WCQ, and done
} SyncWriteStateEnum_t;

typedef enum NRWGetLogPageStateEnum {
	BYTE_NRW_GETLOGPAGE_INIT,
	BYTE_NRW_GETLOGPAGE_ALLOCATE_BUFFER,
	BYTE_NRW_GETLOGPAGE_ALLOCATE_BUFFER_WAIT,
	BYTE_NRW_GETLOGPAGE_PROCESS,
	BYTE_NRW_GETLOGPAGE_DMAC_COPY,
	BYTE_NRW_GETLOGPAGE_WAIT_DMAC_COPY,
	BYTE_NRW_GETLOGPAGE_FREE_BUFFER,
	BYTE_NRW_GETLOGPAGE_TRIG_APU_DB,
	BYTE_NRW_GETLOGPAGE_WAIT_TD_DONE,
	BYTE_NRW_GETLOGPAGE_TD_DONE,
	BYTE_NRW_GETLOGPAGE_FINISH
} NRWGetLogPageStateEnum_t;

typedef enum NRWIdentifyStateEnum {
	BYTE_NRW_IDENTIFY_INIT,
	BYTE_NRW_IDENTIFY_ALLOCATE_BUFFER,
	BYTE_NRW_IDENTIFY_ALLOCATE_BUFFER_WAIT,
	BYTE_NRW_IDENTIFY_PROCESS,
	BYTE_NRW_IDENTIFY_DMAC_COPY,
	BYTE_NRW_IDENTIFY_WAIT_DMAC_COPY,
	BYTE_NRW_IDENTIFY_FREE_BUFFER,
	BYTE_NRW_IDENTIFY_TRIG_APU_DB,
	BYTE_NRW_IDENTIFY_WAIT_TD_DONE,
	BYTE_NRW_IDENTIFY_TD_DONE,
	BYTE_NRW_IDENTIFY_FINISH
} NRWIdentifyStateEnum_t;

typedef enum NRWSetFeatureStateEnum {
	BYTE_NRW_SETFEATURE_INIT,
	BYTE_NRW_SETFEATURE_FINISH
} NRWSetFeatureStateEnum_t;

typedef enum PowerStateEnum {
	BYTE_POWER_STATE_INIT,
	BYTE_POWER_STATE_POWER_MANAGEMENT,
	BYTE_POWER_STATE_FINISH
} PowerStateEnum_t;



//==============================================================================
// Types and Structures
//==============================================================================

//==============================================================================
// Macro Functions
//==============================================================================
//#define M_IS_NRWIOCMD(pHcmd)  (pHcmd->info.ubADM == 0 && ((pHcmd->info.ubOPCode == NVME_FLUSH) || (pHcmd->info.ubOPCode == NVME_WRUNC) || (pHcmd->info.ubOPCode ==NVME_COMP) || (pHcmd->info.ubOPCode == NVME_WRZERO) || (TRIM_EN && pHcmd->info.ubOPCode == NVME_DATASET)))
#define M_IS_NRWIOCMD(pHcmd)  ((pHcmd->info.ubADM == 0) || (pHcmd->info.ubADM == 1 && (pHcmd->info.ubOPCode == FORMAT_NVME)) )
/*
#define M_CLEAR_CPL_INFO(CPL_PTR)  do{\
                                        CPL_PTR->ulDW[0] = 0; \
                                        CPL_PTR->ulDW[1] = 0; \
                                    }while(0);

#define M_CLEAR_WCH_INFO(WCH_PTR)  do{\
                                        WCH_PTR->ulDW[0] = 0; \
                                        WCH_PTR->ulDW[1] = 0; \
                                        WCH_PTR->ulDW[2] = 0; \
                                        WCH_PTR->ulDW[3] = 0; \
                                    }while(0);
*/

//==============================================================================
// Global Variables
//==============================================================================


//==============================================================================
//	Functions
//==============================================================================
extern void FTLProcessTD();
AOM_NRW_2 U8 NRWSendAERCpl(U8 ubType, U8 ubInfo, U8 ubIdentifier);
AOM_NRW_2 U8 NRWAdminVendorPowerHandler(U32 ulSPOREventEn);

AOM_NRW void FTLSyncCmdHandler_FTL(OPT_HCMD *pulCurrentCMD);
#if (NVME == HOST_MODE)
AOM_NRW void FTLSyncCmdHandler_NVME(OPT_HCMD *pulCurrentCMD, U8 btIsHostCmdInSPOR);
#elif (SATA == HOST_MODE) /* (NVME == HOST_MODE) */
void FTLSyncCmdHandler_SATA(void);
#else /* (NVME == HOST_MODE) */ // For USB
void FTLSyncCmdHandler_USB(USBCURRCMD_t *pCurrentCMD, U32 ulReadCount);
void USBClearACPL(void);
#endif /* (NVME == HOST_MODE) */
#endif /* _FTL_SYNC_CMD_HANDLER_H_ */
