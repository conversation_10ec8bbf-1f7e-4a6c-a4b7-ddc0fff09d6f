/**************************************************************************/
/*                                                                        */
/*           Copyright (c) 2000-2015 by Phison Electronics Corp.          */
/*                                                                        */
/**************************************************************************/

/**************************************************************************/
/*                                                                        */
/*  APPLICATION INTERFACE DEFINITION                       RELEASE        */
/*                                                                        */
/*    shr_hal_nvme_other.h                                  GNU C         */
/*                                                           X.X          */
/*  AUTHOR                                                                */
/*                                                                        */
/*                                                                        */
/*  DESCRIPTION                                                           */
/*                                                                        */
/*    This file defines the basic Application Interface (API) to the      */
/*    NVME library.                                                       */
/*                                                                        */
/*  RELEASE HISTORY                                                       */
/*                                                                        */
/*    DATE              NAME                      DESCRIPTION             */
/*                                                                        */
/*                                                                        */
/**************************************************************************/

#ifndef _SHR_NVME_OTHER_LIB_H_
#define _SHR_NVME_OTHER_LIB_H_

#include "nvme_api/nvme/shr_hal_nvme_feat.h"
#include "nvme_api/d2h/shr_hal_d2h.h"


#define DOWN_SIZE_EN (0)
//FW image max byte size(2M)
#define FW_IMAGE_MAX_BUF_BSIZE                     (2 * 1024 * 1024)
//FW image max dword size(2M)
#define FW_IMAGE_MAX_BUF_DSIZE                     (2 * 1024 * 256)

/*
 * Firmware Commit AC
 */

#define NVME_FW_STORE                            (0)
#define NVME_FW_STORE_ACTIVATE           (1)
#define NVME_FW_ACTIVATE_LATER           (2)
#define NVME_FW_ACTIVATE_NOW             (3)
#define NVME_BP_STORE                            (6)     // NVMe 1.3 add
#define NVME_BP_ACTIVATE                        (7)     // NVMe 1.3 add
/*
 * Firmware Commit result
 */
#define NVME_VALID_FW_IMAGE                  (0)
#define NVME_INVALID_FW_IMAGE               (1)
#define NVME_FW_ACTIVATE_PROHIB          (2)

/*
 * Firmware Commit slot
 */
#define NVME_CTRL_SELECT_SLOT                (0)
#define NVME_FW_SLOT1                              (1)
#define NVME_FW_SLOT2                               (2)

/*
 * Firmware Commit clear buffer
 */
#define NVME_RESET_DMLC_ACTIVE          (0)  //clear all image active parameter
#define NVME_CLEAR_DMLC_ACTION          (1)  //only clear have image after fw commit done or dmlc fail
// DW0 - abort Command Specific
#define NVME_ABORT_CMD_ABORTED              (0x00000000)
#define NVME_ABORT_CMD_NOT_ABORTED      (0x00000001)

//FORMAT
#define SES_NO_ERASE                          (0)
#define SES_USER_DATA_ERASE              (1)
#define SES_CRYPTOGRAPHIC_ERASE       (2)

#define PI_NOT_EN                  (0)
#define PI_TYPE1                    (1)
#define PI_TYPE2                    (2)
#define PI_TYPE3                    (3)

//#define MSET_EXTEND_LBA      (0)
//#define MSET_SEPARATE_BUF  (1)
#define MSET_SEPARATE_BUF  (0)
#define MSET_EXTEND_LBA      (1)



#define PIL_LAST_8BYTE  (0)
#define PIL_FIRST_8BYTE (1)



//SANITIZE
//most recent sanitize operation
#define NO_SANITIZE                           (0x00)
#define SANITIZE_SUCCESS                  (0x01)
#define SANITIZE_IN_PROGRESS          (0x02)
#define SANITIZE_FAILED                     (0x03)
//#define SANITIZE_CLR_NOTIFY             (0x04)
//sanitize action
#define SANACT_EXIT_FAILURE_MODE  (0x01)
#define SANACT_BLOCK_ERASE             (0x02)
#define SANACT_OVERWRITE                (0x03)
#define SANACT_CRYPTO_ERASE           (0x04)
//sanitize other
#define SANITIZE_VALID_INFO_SIZE     (20) //byte0~byte 19
#define SANITIZE_NO_SPROG_OR_DONE    (0xFFFF) // progress done or no progress
#define SANITIZE_NO_TIME_REPORT    (0xFFFFFFFF)


//security
typedef enum NvmeSecProtocol {
	SEC_PROTOCOL_INFO           = 0x00,
	SEC_PROTOCOL_TCG1           = 0x01,
	SEC_PROTOCOL_TCG2           = 0x02,
	SEC_PROTOCOL_IEEE1667     = 0xEE,
	SEC_PROTOCOL_ATA_SEC      = 0xEF,
	SEC_PROTOCOL_VENDOR       = 0xFE
} NvmeSecProtocol_t;

typedef enum NvmeSpSpecific {
	SPSP_INFO                        = 0x00,
	SPSP_IDENTIFY                    = 0x05,
	SPSP_GET_LOG                     = 0x06,
	SPSP_DSM                            = 0x09,
	SPSP_FW_IMG_DL                = 0x11,
	SPSP_DUMP_SYS_TABLE       = 0xB0,
	SPSP_VENDOR_SMART          = 0xB1,
	SPSP_SEND_SQ                     = 0xC0,
	SPSP_WRITE_DATA               = 0xC1,
	SPSP_READ_DATA                 = 0xC2,
	SPSP_GET_CQ                       = 0xC3,
	SPSP_READ_VB_DATA				= 0xC4,
	SPSP_VENDOR_SPOR                 = 0xD0,
} NvmeSpSpecific_t;


#pragma pack(1)

typedef struct nvme_fw_image_handler    FW_IMAGE_HANDLER_T, *FW_IMAGE_HANDLER_PTR;

struct nvme_fw_image_handler {
	U8 btHaveFwImg : 1;
	U8 btRsvd               : 7;
	U8 ubFwDlmcAcReset; //reset active
	U8 ubRsvd0;  //imediately active
	U8 ubRsvd1;
	U8 ubFWCurrentRevision[8]; //fw fill new firmware revision
};

typedef struct nvme_fw_slot_info_saved    FW_SLOT_LOG_SAVED_T, *FW_SLOT_LOG_SAVED_PTR;

struct nvme_fw_slot_info_saved {
	FW_SLOT_LOG_T        FwSlotInfoEntry;
	FW_IMAGE_HANDLER_T     FwImgDwInfo;
};



/*
 *  Data structure for SANITIZE command processing
 */
typedef struct nvme_sanitize_status    SANITIZE_LOG_STATUS_T, *SANITIZE_LOG_STATUS_PTR;


struct nvme_sanitize_status {
	union {
		U16 all;
		struct {
			U16 btSanitizeSts           : 3; //status of the most recent sanitize operation
			U16 btOverWriteDoneCnt : 5; //completed passes
			U16 btGlobalDataErased  : 1; // Global Data Erased
			U16 btRsvd               : 7;
		};
	};
};

typedef struct nvme_sanitize_dw10    SANITIZE_DW10_T, *SANITIZE_DW10_PTR;

struct nvme_sanitize_dw10 {
	union {
		U32 all;
		struct {
			U32 btSanitizeAction      : 3; // Sanitize Action
			U32 btAllowUnrestrictedSanitizeEx        : 1; // Allow Unrestricted Sanitize Exit
			U32 btOverWrPassCnt      : 4; // Overwrite Pass Count
			U32 btOverWrInvertPass       : 1; // Overwrite Invert Pattern Between Passes
			U32 btNodealloc   : 1; // No Deallocate After Sanitize
			U32 btRsvd        : 22;
		};
	};
};


typedef struct nvme_sanitize_handler    SANITIZE_LOG_HANDLER_T, * SANITIZE_LOG_HANDLER_PTR;

struct nvme_sanitize_handler {
	union {
		U32 all[2];
		struct {
			//U32 btStoreSanitize                 : 1;  // notify to store Sanitize Process Structure
			U32 btFwActivePending        : 1;  // FW COMMIT is successed, FW Activation is Pending
			U32 btLPMNotAllowed       : 1;  // prevent entering LPM when SANITIZE in Progress or Failure
			U32 btAbortNotAllowedCmd : 1; // Abort not allowed commands when SANITIZE in Progress/SANITIZE Failed
			U32 btRsvd                  : 29;

			U32 ulOverWrPattern;     // DW11: Overwrite Pattern
		};
	};
	U64 ulSmartWRCnt;     // smart write count when gde set 1
};

typedef struct nvme_sanitize_log_saved    SANITIZE_LOG_SAVED_T, * SANITIZE_LOG_SAVED_PTR;


struct nvme_sanitize_log_saved {
	U16                                uwSanitizeProg;      // Sanitize Progress
	SANITIZE_LOG_STATUS_T SanitizeStatus;      // Sanitize Status
	SANITIZE_DW10_T   SanitizeDw10;     // DW10: Sanitize Command DW10
	U32                     ulEstimatedTimeOverWr;     // Estimated Time for OverWrite
	U32                     ulEstimatedTimeBlkErase;     // Estimated Time for Block Erase
	U32                     ulEstimatedTimeCryptoErase;     // Estimated Time for Crypto Erase

	SANITIZE_LOG_HANDLER_T     SanitizeHandler;  // notify to store sanitize structure
	//U32                     ulRsvd;
};


/*
 *  Data structure for DATASET MANAGEMENT command processing
 */
typedef struct nvme_dataset_management_dw10    DATASET_MANAGEMENT_DW10_T, * DATASET_MANAGEMENT_DW10_PTR;

struct nvme_dataset_management_dw10 {
	U32   NR       : 8;	//Number of Ranges (ZERO-based value)
	U32   Reserved : 24;
};


typedef struct nvme_dataset_management_dw11    DATASET_MANAGEMENT_DW11_T, * DATASET_MANAGEMENT_DW11_PTR;

struct nvme_dataset_management_dw11 {
	U32   IDR		: 1; //Integral Dataset for Read
	U32   IDW		: 1; //Integral Dataset for Write
	U32   AD			: 1; //Attribute Deallocate
	U32   Reserved	: 29;
};


//====================host struct info====================================================

typedef struct nvme_host_geometry_struct    HOST_GEOMETRY_T, *HOST_GEOMETRY_PTR;


struct nvme_host_geometry_struct {
	IDFY_NS_VAR_INFO_T                                          gVarNsIdfy[MAX_NS_NUMBER];    // ns[0]:NS1, ns[1]:NS2, ... ns[7]:NS8
	IDFY_CTRL_VAR_INFO_T                                       gVarIdfy;           // For nvme identify ctrl var struct
	SMART_LOG_SAVED_T                                           gSmartLogSave;
	ERR_LOG_SAVED_T                                               gErrInfoLogEntry;
	FW_SLOT_LOG_SAVED_T                                       gFwSlotSaveInfo;
	NS_LIST_LOG_SAVED_T                                         gNsListLog;
	DEV_SELF_TEST_SAVED_T                                      gDevSelfTest;
	SANITIZE_LOG_SAVED_T                                       gSaniStatusLog;
	FEATURE_MGT_T                                                  gFeatStruct;       //currect/default/save
	HOST_PROC_INFO_T                                             gVarHostPro;
	HMB_HANDLE                                                gHMB_HANDLE;
	HMB_LIST                                                  gD2HFreeMemList;
	HMB_MEMORY_MAP                                            gD2HMemMap;
	HMB_GLOBAL_DATA                                           gD2HData;
	TELEMETRY_LOG_SAVED_T                                     gTelemetryLog;
	NGUID_T													  gNGUID[MAX_NS_NUMBER];
	ERR_LOG_ENTRY_T                                           gExtendErrLogEntry[EXTEND_ERROR_LOG_ENTRY_NUM];
	SECURE_LOG_SAVED_T                                        gSecureLog;
	FEATURE_SCP_T                                             gSCP[3];
	U8														  gubIsClearRedundant; // Flag to clear last redundant 2k in host table
	NVME_ATA_SECURITY_T										  gATASecurity;
#if (IM_N48R && PS5013_EN)
	U8														  ubReserved[314];
	U8														  gTTTemperatureData[BTCM0_GET_TEMPERATURE_SIZE]; // reserve last 2K for TT temperate data.
#endif /* (IM_N48R && PS5013_EN) */
} ;
TYPE_SIZE_CHECK_BELOW(HOST_GEOMETRY_T, SIZE_8KB);

//====================host info done===================================================================



//====================host prp info====================================================
typedef struct nvme_prt_host_geometry_struct    P_HOST_GEOMETRY_T, *P_HOST_GEOMETRY_PTR;

struct nvme_prt_host_geometry_struct {
	IDFY_NS_VAR_INFO_PTR                    pVarNsIdfy[MAX_NS_NUMBER];    // ns[0]:NS1, ns[1]:NS2, ... ns[7]:NS8
	IDFY_CTRL_VAR_INFO_PTR                 pVarIdfy[MAX_CTR_NUMBER];     // For nvme identify ctrl var struct
	SMART_LOG_SAVED_PTR                    pSmartLogSave;
	ERR_LOG_SAVED_PTR                        pErrInfoLogEntry;
	FW_SLOT_LOG_SAVED_PTR                pFwSlotSaveInfo;
	NS_LIST_LOG_SAVED_PTR                  pNsListLog;
	DEV_SELF_TEST_SAVED_PTR               pDevSelfTest;
	SANITIZE_LOG_SAVED_PTR         pSaniStatusLog;
	FEATURE_MGT_PTR                            pFeatStruct;       //currect/default/save
	HOST_PROC_INFO_PTR                      pVarHostPro; //LPMRESET should save or not
	HMB_HANDLE                             *pHmbHandle;
	TELEMETRY_LOG_SAVED_PTR                pTelemetryLog;
	NGUID_PTR								   pNGUID[MAX_NS_NUMBER];
	ERR_LOG_ENTRY_PTR                      pExtendErrorLogEntry;
	SECURE_LOG_SAVED_PTR                   pSecureLog;
	FEATURE_SCP_PTR                        pSCP[3];
	U8										*pubIsClearRedundant; // Flag to clear last redundant 2k in host table
	NVME_ATA_SECURITY_PTR 					pNVMeATASecurity;
	U8										*pTTTemperatureData;
};
//====================host prp done===================================================================
#pragma pack()
#endif /* _SHR_NVME_OTHER_LIB_H_ */
