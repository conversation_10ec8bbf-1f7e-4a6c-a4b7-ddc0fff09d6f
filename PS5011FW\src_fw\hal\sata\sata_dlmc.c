// f
#include "ftl/ftl_nrw.h"
#include "ftl/ftl_nrw_api.h"
#include "ftl/ftl_preread_api.h"
// l
#include "lpm/lpm_api.h"
// m
#include "hal/mr/mr_api.h"
// s
#include "hal/sata/sata_api.h"
#include "hal/sata/sata_cmd.h"

#if (HOST_MODE == SATA)

#if ((!BURNER_MODE_EN) && (!RDT_MODE_EN))

/***********************************************
** private prototypes
************************************************/

//trigger FW to change image
void SATADLMCChangeCode (void)
{
	M_UART(SATA_CMD_, "Trig DLMC\n");

	if (FALSE == gDLMC.ubDisablePreReadFlag) {
		M_UART(SATA_DEBUG_, "\nSPR");
		FTLSetPreReadStop();
		gPreReadInfo.btPreReadEn   = FALSE;
		gDLMC.ubDisablePreReadFlag = TRUE;
	}

	if (STATE_PREREAD_IDLE != gPreReadInfo.ubMainState) {
		M_UART(SATA_DEBUG_, "\nPS %d", gPreReadInfo.ubMainState);
		return;
	}

	LPMTermination();

	M_APU_SET_DISPATCH_FW_LOCK(APU_DISPATCH_FW_LOCK_LPM);

	if ((!M_APU_CHECK_CMD_DISPATCH_LOCK_STATE()  )
		|| (M_APU_GET_CMD_FIFO_ASYNC_Q_CNT())
		|| (!M_MR_CHK_NRW_PATH_EMPTY())
		|| M_DB_GET_RD_CNT(DB_APU_CMD_CQ)) {
		return;
	}

	gDLMC.ubFwDlmcAcTrig       = 0;
	gDLMC.ubDisablePreReadFlag = FALSE;

	//FW starting change FW
	M_UART(SATA_CMD_, "\nLPM Sleep to DLMC");
	M_UART(SATA_DEBUG_, " FPS4: %u\n",	gpVT->DLMC.B.btForcePS4);
	gpVT->DLMC.B.btLimitPS4  = FALSE;
	gpVT->DLMC.B.btForcePS4  = TRUE;
	gpVT->DLMC.B.btDLMCDoing = TRUE;
	gSystemAreaHandle.Info.ubGotoLPMForChangeCode = FALSE;

	LPMSleepToDLMC();

}

void SATADLMCFWCommit(U8 ubDLMCMode, U8 *ubSectorCntRegValue)
{
	U8 ubCommitStatus = NVME_VALID_FW_IMAGE;
	U8 ubDoCommitFunction = FALSE;

	gDLMC.ubFwDlmcAcTrig = FALSE;

	switch (ubDLMCMode) {
	case SATA_DLMC_SEGMENT_DOWNLOAD_ACTIVE_MODE:
	case SATA_DLMC_ONCE_DOWNLOAD_ACTIVE_MODE:
	case SATA_DLMC_SEGMENT_DOWNLOAD_NO_ACTIVE_MODE:
		ubDoCommitFunction = TRUE;
		break;
	default:
		break;
	}

	if (ubDoCommitFunction) {
		ubCommitStatus = NRWDLMCCommitEntry(NVME_FW_STORE, NVME_FW_SLOT1);
	}

	if (NVME_INVALID_FW_IMAGE == ubCommitStatus) {
		M_UART(SATA_CMD_, "\nError D I");
		gSATAVar.ubErrorCase = SATACMD_ERROR_CASE_0Dh;
	}
	else  if (NVME_FW_ACTIVATE_PROHIB == ubCommitStatus) {
		M_UART(SATA_CMD_, "\nError D P");
		gSATAVar.ubErrorCase = SATACMD_ERROR_CASE_0Eh;
	}
	else  if  (NVME_VALID_FW_IMAGE == ubCommitStatus) {
		gpVT->DLMC.B.btLimitPS4         = TRUE;
		gDLMC.uwOthers.B.btCommitIsDone = TRUE;
		if (SATA_DLMC_SEGMENT_DOWNLOAD_NO_ACTIVE_MODE != ubDLMCMode) {
			gDLMC.ubFwDlmcAcTrig          = TRUE;
			gpVT->DLMC.B.btDLMCActiveTrig = TRUE; // force LPM3
			gDLMC.ubHaveFWImage           = FALSE;
			M_SATA_SET_IS_NEED_SEND_D2H(SATA_DLMC_CHANGE_CODE_DO_NOT_SEND_D2H);
			*ubSectorCntRegValue = SATA_DLMC_SECTOR_CNT_REG_ACTIVE_TO_NEW_CODE;
		}
		else {
			*ubSectorCntRegValue = SATA_DLMC_SECTOR_CNT_REG_WAIT_ACTIVE_TO_NEW_CODE;
		}
		gpVT->DLMC.B.btDLMCDoing = TRUE;
		if (SATA_DLMC_DIRECT_ACTIVE_MODE != ubDLMCMode) {
			gpVTDBUF->FW.ulDLMCCnt++;
		}
	}
	NRWDLMCClearFW();
	M_UART(SATA_CMD_, "\nDLMC E");
}

U8 SATADLMCContentCheck(U8 ubDLMCMode, U16 uwBufferOffset)
{
	U8 ubErrorHappen = FALSE;
	U8 ubDLMCParameterReset = FALSE;

	if (SATA_DLMC_SEGMENT_DOWNLOAD_ACTIVE_MODE == ubDLMCMode) { //download segment untill all transfer done, then activate it
		if (SATA_DLMC_SEGMENT_DOWNLOAD_ACTIVE_MODE == gDLMC.ubSATALastDLMCMode) {
			if (DLMC_INIT != gDLMC.ubState) {
				if (0 == uwBufferOffset ) { //back to offset 0
					ubDLMCParameterReset = TRUE;
				}
				else if (uwBufferOffset != (gDLMC.ulFwOffset / DEF_512B)) {
					M_UART(SATA_CMD_, "\nDLMC Err 00 BO:%x FO:%x", uwBufferOffset, gDLMC.ulFwOffset);
					gSATAVar.ubErrorCase = SATACMD_ERROR_CASE_01h;
					ubErrorHappen        = TRUE;
					ubDLMCParameterReset = TRUE;
				}
			}
		}
		else {
			if (0 != uwBufferOffset) {
				gSATAVar.ubErrorCase = SATACMD_ERROR_CASE_02h;
				ubErrorHappen        = TRUE;
				ubDLMCParameterReset = TRUE;
			}
			else {
				ubDLMCParameterReset = TRUE;
			}
		}
	}
	else if (SATA_DLMC_ONCE_DOWNLOAD_ACTIVE_MODE == ubDLMCMode) { //download all, and activate it
		if (0 != uwBufferOffset) {
			gSATAVar.ubErrorCase = SATACMD_ERROR_CASE_03h;
			ubErrorHappen        = TRUE;
			ubDLMCParameterReset = TRUE;
		}
		else {
			ubDLMCParameterReset = TRUE;
		}
	}
	else if (SATA_DLMC_SEGMENT_DOWNLOAD_NO_ACTIVE_MODE == ubDLMCMode) { //download segment untill all transfer done, then activate it
		if (SATA_DLMC_SEGMENT_DOWNLOAD_NO_ACTIVE_MODE == gDLMC.ubSATALastDLMCMode) {
			if (DLMC_INIT != gDLMC.ubState) {
				if (0 == uwBufferOffset ) { //back to offset 0
					ubDLMCParameterReset = TRUE;
				}
				else if (uwBufferOffset != (gDLMC.ulFwOffset / DEF_512B)) {
					M_UART(SATA_CMD_, "\nDLMC Err 03 BO:%x FO:%x", uwBufferOffset, gDLMC.ulFwOffset);
					gSATAVar.ubErrorCase = SATACMD_ERROR_CASE_04h;
					ubErrorHappen        = TRUE;
					ubDLMCParameterReset = TRUE;
				}
			}
		}
		else {
			if (0 != uwBufferOffset) {
				gSATAVar.ubErrorCase = SATACMD_ERROR_CASE_05h;
				ubErrorHappen        = TRUE;
				ubDLMCParameterReset = TRUE;
			}
			else {
				ubDLMCParameterReset = TRUE;
			}
		}
	}
	else if (SATA_DLMC_DIRECT_ACTIVE_MODE == ubDLMCMode) { //activate 0x0E download
		if (FALSE == gDLMC.ubHaveFWImage) {
			gSATAVar.ubErrorCase = SATACMD_ERROR_CASE_06h;
			ubErrorHappen        = TRUE;
			ubDLMCParameterReset = TRUE;
		}
	}
	else {
		gSATAVar.ubErrorCase = SATACMD_ERROR_CASE_07h;
		ubErrorHappen        = TRUE;
		ubDLMCParameterReset = TRUE;
	}

	if (ubDLMCParameterReset) {
		NRWDLMCClearFW();
		gDLMC.ubHaveFWImage = FALSE;
	}

	gDLMC.ubSATALastDLMCMode = ubDLMCMode;

	return ubErrorHappen;

}

#endif /* ((!BURNER_MODE_EN) && (!RDT_MODE_EN)) */

#endif /* (HOST_MODE == SATA) */
