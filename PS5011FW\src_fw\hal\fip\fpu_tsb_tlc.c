#include "fpu.h"

#if ((CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_2D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_SANDISK_3D_TLC))
#define FPU_SECTION  __attribute__ ((section("IRAM_FPU")))

#if NES_GEN2_EN
U16 guwFPUContentForNCS[0x0d] = {
	FPU_CMD(0xC9), FPU_CMD(0x80), FPU_ADR_1B(0), FPU_ADR_1B(0), FPU_ADR_1B(0), FPU_ADR_1B(0), FPU_ADR_1B(0), FPU_ADR_1B(0), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x10), FPU_END,
};
#elif NES_GEN1_EN
U16 guwFPUContentForNCS[0x0C] = {
	FPU_CMD(0xC9), FPU_CMD(0x80), FPU_ADR_1B(0), FPU_ADR_1B(0), FPU_ADR_1B(0), FPU_ADR_1B(0), FPU_ADR_1B(0), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x10), FPU_END,
};
#endif  /* NES_GEN1_EN */
/*
 * Unused List:
 * fpu_reserved0
 * fpu_reserved1
 * fpu_reserved2
 * fpu_reserved3
 * fpu_reserved4
 */
FPU_ENTRY_LIST gFpuEntryList FPU_SECTION = {
	/*  fpu_version */
	{
		FPU_VER_L, FPU_VER_H,
	},
	/*  fpu_entry_nop */
	{
		FPU_NOP, FPU_END,
	},

	/*
	 *
	 * auto poll fpu section
	 *
	 */

	/*  fpu_entry_read_status_busy_40, Read Status for busy, FPU_DAT_R_CMP with Ext=1 Need Alignemnt  */
	{
		// 8 -23
		FPU_NOP, FPU_NOP, FPU_CMD(0x70), FPU_DLY(0x10), FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK(0x40), FPU_NOP, FPU_END,
	},
	/*  fpu_entry_read_status_busy_20, Read Status for busy, FPU_DAT_R_CMP with Ext=1 Need Alignemnt  */
	{
		// 24 - 39
		FPU_NOP, FPU_NOP, FPU_CMD(0x70), FPU_DLY(0x10), FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK(0x20),  FPU_NOP, FPU_END,
	},
	/*  fpu_entry_read_status_70_01_CURR_TLC_MODE, Read Status for fail, FPU_DAT_R_CMP with Ext=1 Need Alignemnt  */
	{
		// 40 - 55
		FPU_NOP, FPU_NOP, FPU_CMD(0x70), FPU_DLY(0x10),  FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK(0x01), FPU_NOP, FPU_END,
	},
	/*  fpu_entry_read_status_70_02_PREV_TLC_MODE, Read Status for fail, FPU_DAT_R_CMP with Ext=1 Need Alignemnt  */
	{
		// 56 -71
		FPU_NOP, FPU_NOP, FPU_CMD(0x70), FPU_DLY(0x10), FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK(0x02), FPU_NOP, FPU_END,
	},
	/*  fpu_entry_read_status_70_04_CURR_SLC_MODE, Read Status for fail, FPU_DAT_R_CMP with Ext=1 Need Alignemnt  */
	{
		// 72 - 87
		FPU_NOP, FPU_NOP, FPU_CMD(0x70), FPU_DLY(0x10),  FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK(0x04), FPU_NOP, FPU_END,
	},
	/*  fpu_entry_read_status_70_08_PREV_SLC_MODE, Read Status for fail, FPU_DAT_R_CMP with Ext=1 Need Alignemnt  */
	{
		// 88 - 103
		FPU_NOP, FPU_NOP, FPU_CMD(0x70), FPU_DLY(0x10),  FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK(0x08), FPU_NOP, FPU_END,
	},
	/*  fpu_entry_read_status_70_busy_20_CURR_TLC_MODE_01, Read Status for PFA, FPU_DAT_R_CMP with Ext=1 Need Alignemnt  */
	{
		// 104 - 119
		FPU_NOP, FPU_NOP, FPU_CMD(0x70), FPU_DLY(0x10), FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK_EXT(0x20), FPU_DAT_R_MASK(0x01), FPU_END,
	},
	/*  fpu_entry_read_status_70_busy_40_PREV_TLC_MODE_02, Read Status for PFA, FPU_DAT_R_CMP with Ext=1 Need Alignemnt  */
	{
		// 120 - 135
		FPU_NOP, FPU_NOP, FPU_CMD(0x70), FPU_DLY(0x10), FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK_EXT(0x40), FPU_DAT_R_MASK(0x02), FPU_END,
	},
	/*  fpu_entry_read_status_70_busy_20_CURR_SLC_MODE_04, Read Status for PFA, FPU_DAT_R_CMP with Ext=1 Need Alignemnt  */
	{
		// 136 - 151
		FPU_NOP, FPU_NOP, FPU_CMD(0x70), FPU_DLY(0x10),  FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK_EXT(0x20), FPU_DAT_R_MASK(0x04), FPU_END,
	},
	/*  fpu_entry_read_status_70_busy_40_PREV_SLC_MODE_08, Read Status for PFA, FPU_DAT_R_CMP with Ext=1 Need Alignemnt  */
	{
		// 152 - 167
		FPU_NOP, FPU_NOP, FPU_CMD(0x70), FPU_DLY(0x10), FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK_EXT(0x40), FPU_DAT_R_MASK(0x08), FPU_END,
	},
	/*  fpu_entry_read_status_70_busy_40_INTERMEDIATE_MULTIPLANE_PROGRAM, Read Status for PFA, FPU_DAT_R_CMP with Ext=1 Need Alignemnt  */
	{
		// 168 - 183
		FPU_NOP, FPU_NOP, FPU_CMD(0x70), FPU_DLY(0x10),  FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK_EXT(0x40), FPU_DAT_R_MASK(0x00), FPU_END,
	},
	/* fpu_entry_read_status_70_busy_20_CURR_SLC_TLC_MODE_05, Read Status for PFA, FPU_DAT_R_CMP with Ext=1 Need Alignemnt */
	{
		// 184 - 199
		FPU_NOP, FPU_NOP, FPU_CMD(0x70), FPU_DLY(0x10),  FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK_EXT(0x20), FPU_DAT_R_MASK(0x05), FPU_END
	},
	/*  fpu_entry_read_status_f1_busy_40, Read Status for busy, FPU_DAT_R_CMP with Ext=1 Need Alignemnt  */
	{
		// 200 - 215
		FPU_NOP, FPU_NOP, FPU_CMD(0xF1), FPU_DLY(0x10), FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK_EXT(0x40), FPU_DAT_R_MASK(0x0F), FPU_END,
	},
	/*  fpu_entry_read_status_f2_busy_40, Read Status for busy, FPU_DAT_R_CMP with Ext=1 Need Alignemnt  */
	{
		// 216 - 231
		FPU_NOP, FPU_NOP, FPU_CMD(0xF2), FPU_DLY(0x10),  FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK_EXT(0x40), FPU_DAT_R_MASK(0x0F), FPU_END,
	},
	/*  fpu_entry_read_status_f1_tlc_busy_20, Read Status for busy, FPU_DAT_R_CMP with Ext=1 Need Alignemnt  */
	{
		// 232 - 247
		FPU_NOP, FPU_NOP, FPU_CMD(0xF1), FPU_DLY(0x10),  FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK(0x01), FPU_NOP, FPU_END,
	},
	/*  fpu_entry_read_status_f2_tlc_busy_20, Read Status for busy, FPU_DAT_R_CMP with Ext=1 Need Alignemnt  */
	{
		// 248 - 263
		FPU_NOP, FPU_NOP, FPU_CMD(0xF2), FPU_DLY(0x10),  FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK(0x01), FPU_NOP, FPU_END,
	},
	/*  fpu_entry_read_status_f1_slc_busy_20, Read Status for busy, FPU_DAT_R_CMP with Ext=1 Need Alignemnt  */
	{
		// 264 - 279
		FPU_NOP, FPU_NOP, FPU_CMD(0xF1), FPU_DLY(0x10),  FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK(0x04), FPU_NOP, FPU_END,
	},
	/*  fpu_entry_read_status_f2_slc_busy_20, Read Status for busy, FPU_DAT_R_CMP with Ext=1 Need Alignemnt  */
	{
		// 280 - 295
		FPU_NOP, FPU_NOP, FPU_CMD(0xF2), FPU_DLY(0x10),  FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK(0x04), FPU_NOP, FPU_END,
	},
	/*  fpu_read_and_compare_feature_data, Reserve for Check Get Feature Data, FPU_DAT_R_CMP with Ext=1 Need Alignemnt  */
	{
		// 296 - 327
		0, 0, 0, 0, 0, 0, 0, 0,
		0, 0, 0, 0, 0, 0, 0, FPU_END
	},
	/*  fpu_entry_read_status_err_81, Read Status for Check Error, FPU_DAT_R_CMP with Ext=1 Need Alignemnt  */
	{
		// 328 -343
		FPU_NOP,  FPU_NOP, FPU_CMD(0x70), FPU_DLY(0x10), FPU_DAT_R_CMP(0x80), FPU_DAT_R_MASK(0x81),  FPU_NOP, FPU_END,
	},
	/*	fpu_entry_read_status_78_busy_40, Read Status for busy	*/
	{
		FPU_CMD(0x78), FPU_ADR(3), FPU_DLY(0x10), FPU_NOP, FPU_NOP, FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK(0x40), FPU_END,
	},
	/*	fpu_entry_read_status_78_busy_20, Read Status for busy	*/
	{
		FPU_CMD(0x78), FPU_ADR(3), FPU_DLY(0x10), FPU_NOP, FPU_NOP, FPU_DAT_R_CMP(0x00), FPU_DAT_R_MASK(0x20), FPU_END,
	},

	/**********************************************************
	** The Above Section is Used to Place Alignment FPU Sequence
	***********************************************************/

	/*
	 *
	 * error handle fpu section
	 *
	 */
	/*  fpu_entry_read, Page Read  */
	{
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_DLY(0x10), FPU_CMD(0x70), FPU_END,
	},
	/*  fpu_entry_dma_r_05_e0, Rand data out.  */
	{
		FPU_ADR_GEN, FPU_CMD(0x05), FPU_ADR(5), FPU_CMD(0xE0), FPU_DLY(0x60), FPU_DMA_R, FPU_END,
	},
	/*  fpu_reserved0, Rand data out.  */
	{
		FPU_NOP, FPU_NOP, FPU_NOP, FPU_NOP,
		FPU_NOP, FPU_NOP, FPU_NOP, FPU_NOP,
		FPU_NOP
	},

	/*  fpu_entry_tlc_read_lower  */
	{
		FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_NOP, FPU_END,
	},
	/*  fpu_entry_tlc_read_middle  */
	{
		FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_NOP, FPU_END,
	},
	/*  fpu_entry_tlc_read_upper  */
	{
		FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_NOP, FPU_END,
	},
	/*  fpu_entry_tlc_read_lower_relax_read_mode  */
	{
		FPU_ADR_GEN, FPU_CMD(0x26), FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_NOP, FPU_END,
	},
	/*  fpu_entry_tlc_read_middle_relax_read_mode  */
	{
		FPU_ADR_GEN, FPU_CMD(0x26), FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_NOP, FPU_END,
	},
	/*  fpu_entry_tlc_read_upper_relax_read_mode  */
	{
		FPU_ADR_GEN, FPU_CMD(0x26), FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_NOP, FPU_END,
	},
	/*  fpu_entry_rand_in, Rand Data In w/ fsa  */
	{
		FPU_ADR_GEN, FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_END,
	},
	/*  fpu_reserved3, Rand Data Out w/ fsa  */
	{
		FPU_NOP, FPU_NOP, FPU_NOP, FPU_NOP,
		FPU_NOP, FPU_NOP, FPU_NOP
	},
	/*  fpu_entry_cor_test  */
	{
		FPU_ADR_GEN, FPU_DMA_R_COR(0x00), FPU_DLY(0x00)/* dummy */, FPU_END,
	},
	/*  fpu_entry_dump_ibuf  */
	{
		FPU_BR(0x00), FPU_END,
	},
	/*  fpu_entry_dma_r_raw  */
	{
		FPU_ADR_GEN, FPU_CMD(0x05), FPU_ADR(5), FPU_CMD(0xE0),
		FPU_NOP, FPU_NOP, FPU_DLY(0x10), FPU_DMA_R_RAW(0x00),  // !!!  DON'T CHANGE THE POSITION OF "FPU_DMA_R_RAW(0x00)"  !!!
		FPU_END,
	},
	/*  fpu_entry_tsb_tlc_rr_lower  */
	{
		FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x3C), FPU_NOP, FPU_END,
	},
	/*  fpu_entry_tsb_tlc_rr_middle  */
	{
		FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x3C), FPU_NOP, FPU_END,
	},
	/*  fpu_entry_tsb_tlc_rr_upper  */
	{
		FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x3C), FPU_NOP, FPU_END,

	},
	/*  fpu_entry_tsb_tlc_rr_lower_relax_read_mode  */
	{
		FPU_ADR_GEN, FPU_CMD(0x26), FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x3C), FPU_NOP, FPU_END,
	},
	/*  fpu_entry_tsb_tlc_rr_middle_relax_read_mode  */
	{
		FPU_ADR_GEN, FPU_CMD(0x26), FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x3C), FPU_NOP, FPU_END,
	},
	/*  fpu_entry_tsb_tlc_rr_upper_relax_read_mode  */
	{
		FPU_ADR_GEN, FPU_CMD(0x26), FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x3C), FPU_NOP, FPU_END,

	},

	/*  fpu_entry_direct_slc_read  */
	{
		RD_SLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x60), FPU_POL_MASK(0xE0), FPU_DLY(0x60), FPU_CMD(0x05), FPU_ADR(5), FPU_CMD(0xE0), FPU_DLY(0x60), FPU_DMA_R, FPU_END,
	},
	/*  fpu_entry_direct_tlc_l_read  */
	{
		FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x60), FPU_POL_MASK(0xE0), FPU_DLY(0x60), FPU_CMD(0x05), FPU_ADR(5), FPU_CMD(0xE0), FPU_DLY(0x60), FPU_DMA_R, FPU_END,
	},
	/*  fpu_entry_direct_tlc_m_read  */
	{
		FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x60), FPU_POL_MASK(0xE0), FPU_DLY(0x60), FPU_CMD(0x05), FPU_ADR(5), FPU_CMD(0xE0), FPU_DLY(0x60), FPU_DMA_R, FPU_END,
	},
	/*  fpu_entry_direct_tlc_u_read  */
	{
		FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x60), FPU_POL_MASK(0xE0), FPU_DLY(0x60), FPU_CMD(0x05), FPU_ADR(5), FPU_CMD(0xE0), FPU_DLY(0x60), FPU_DMA_R, FPU_END,
	},

	/*  fpu_entry_direct_tlc_l_read_relax_read_mode  */
	{
		FPU_ADR_GEN, FPU_CMD(0x26), FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x60), FPU_POL_MASK(0xE0), FPU_DLY(0x60), FPU_CMD(0x05), FPU_ADR(5), FPU_CMD(0xE0), FPU_DLY(0x60), FPU_DMA_R, FPU_END,
	},
	/*  fpu_entry_direct_tlc_m_read_relax_read_mode  */
	{
		FPU_ADR_GEN, FPU_CMD(0x26), FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x60), FPU_POL_MASK(0xE0), FPU_DLY(0x60), FPU_CMD(0x05), FPU_ADR(5), FPU_CMD(0xE0), FPU_DLY(0x60), FPU_DMA_R, FPU_END,
	},
	/*  fpu_entry_direct_tlc_u_read_relax_read_mode  */
	{
		FPU_ADR_GEN, FPU_CMD(0x26), FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x60), FPU_POL_MASK(0xE0), FPU_DLY(0x60), FPU_CMD(0x05), FPU_ADR(5), FPU_CMD(0xE0), FPU_DLY(0x60), FPU_DMA_R, FPU_END,
	},
	/*
	 *
	 * normal fpu section
	 *
	 */

	/*  fpu_entry_reset_ff, Toggle/Lagacy reset  */
	{
		FPU_CMD(0xFF), FPU_DLY(0x10), FPU_END,
	},
	/*  fpu_entry_reset_fc, Onfi Reset.  */
	{
		FPU_CMD(0xFC), FPU_DLY(0x10), FPU_END,
	},
	/* fpu_entry_reset_fa, reset lun */
	{
		FPU_CMD_DQS(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xE0), FPU_DLY(0x10), FPU_ADR_GEN, FPU_CMD(0xFA), FPU_ADR(3), FPU_DLY(0x10), FPU_END, FPU_NOP, FPU_NOP, FPU_NOP,
	},

	/*
	 * Read command: slc / mlc mode + single plane mode.
	 */



	/*  fpu_entry_slc_read, A2 Page Read  */
	{
		RD_SLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_NOP, FPU_END,
	},
	/*
	 * Erase command: slc / mlc mode + plane mode.
	 */


	/*  fpu_entry_erase, Block Erase w/ fsa  */
	{
		ER_TLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD(0x60), FPU_ADR(3), FPU_CMD(0xD0), FPU_END,
	},
	/*  fpu_entry_slc_erase, A2 Block Erase w/ fsa  */
	{
		ER_SLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD(0x60), FPU_ADR(3), FPU_CMD(0xD0), FPU_END,
	},

	/*  fpu_entry_slc_60h_row_erase  */
	{
		ER_SLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD(0x60), FPU_ADR(3), FPU_END,
	},

	/*  fpu_entry_tlc_60h_row_erase  */
	{
		ER_TLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD(0x60), FPU_ADR(3), FPU_END,
	},

	/*  fpu_entry_2p_erase, 2-plane Block Erase w/ fsa  */
	{
		FPU_ADR_GEN, FPU_CMD(0x60), FPU_ADR(3), FPU_ADR_GEN, FPU_CMD(0x60), FPU_ADR(3), FPU_CMD(0xD0), FPU_DLY(0x10), FPU_END,
	},


	/*
	 * misc
	 */

	/*  fpu_entry_read_dma  */
	{
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_NOP, FPU_DMA_R, FPU_END,
	},
	/*  fpu_entry_dly_only  */
	{
		FPU_DLY(0x01), FPU_END,
	},

	/*  fpu_entry_erase_all_done  */
	{
		FPU_ADR_GEN, FPU_CMD(0x60), FPU_ADR(3), FPU_CMD(0xD0), FPU_END,
	},
	/*  fpu_entry_70 */
	{
		FPU_CMD(0x70), FPU_NOP, FPU_END,
	},

	/*  fpu_entry_prog_to_flash_cache, Page Program w/ fsa  */
	{
		FPU_ADR_GEN, FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_END,
	},

	/*  fpu_entry_read_from_flash_cache, Rand data out.  */
	{
		FPU_ADR_GEN, FPU_CMD(0x05), FPU_ADR(5), FPU_CMD(0xE0), FPU_DLY(0x60), FPU_DMA_R, FPU_END,
	},
	/*  fpu_entry_C85_A5_NoDMAW  */
	{
		FPU_ADR_GEN, FPU_CMD_DQS(0x85), FPU_ADR(ADDRESS_WRITE_NUM), FPU_DLY(0x10), FPU_END,
	},
	/*  fpu_entry_DMA_W_RAW  */
	{
		FPU_DMA_W_RAW(FPU_RAW_PROG_IBF), FPU_END,
	},
	/*  fpu_entry_DMA_R_RAW  */
	{
		FPU_DMA_R_RAW(FPU_RAW_DST_IBUF(0) | FPU_RAW_SRC_IBUF(1) | FPU_RAW_SET_MODE(2) | FPU_RAW_LOGIC_NOP), FPU_END,
	},
	/*  fpu_entry_C11  */
	{
		FPU_CMD_DQS(0x11), FPU_END,
	},
	/*  fpu_entry_C00_A5_C05_A2_CE0  */
	{
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(ADDRESS_WRITE_NUM), FPU_CMD(0x05), FPU_ADR(2), FPU_CMD(0xE0), FPU_END,
	},

	/*
	 * read/cache read
	 */

	/*
	 * Read command: slc / mlc mode + plane cache mode.
	 */
	/*  fpu_entry_slc_3F_read. */
	{
		//No FPU_END
		RD_SLC_PREFIX,
	},
	/*  fpu_entry_3F_read, */
	{
		FPU_CMD(0x3F), FPU_END,
	},

	/*  fpu_entry_mlc_2p_32_30_read  */
	{
		RD_MLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xE0), FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_END,

	},
	/*  fpu_entry_mlc_2p_32_31_read  */
	{
		RD_MLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xE0), FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_END,
	},

	/*  fpu_entry_mlc_4p_32_30_read  */
	{
		RD_MLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xE0), FPU_DLY(0x10),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xE0), FPU_DLY(0x10),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xE0), FPU_DLY(0x10),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_END,

	},
	/*  fpu_entry_mlc_4p_32_31_read  */
	{
		RD_MLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xE0), FPU_DLY(0x10),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xE0), FPU_DLY(0x10),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xE0), FPU_DLY(0x10),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_END,
	},

	/*  fpu_entry_slc_1p_30_fast_read  */
	{
		//No FPU_END
		TOSHIBA_FAST_READ_PREFIX,
	},
	/*  fpu_entry_slc_1p_30_read  */
	{
		RD_SLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_END,
	},
	/*  fpu_entry_slc_1p_31_fast_read  */
	{
		//No FPU_END
		TOSHIBA_FAST_READ_PREFIX,
	},
	/*  fpu_entry_slc_1p_31_read  */
	{
		RD_SLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_END,
	},
	/*  fpu_entry_slc_2p_32_30_read  */
	{
		RD_SLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_END,
	},
	/*  fpu_entry_slc_2p_32_31_read  */
	{
		RD_SLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_END,
	},
	/*  fpu_entry_slc_2p_32_F1_30_read  */
	{
		RD_SLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0xF1), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_END,
	},

	/*  fpu_entry_slc_2p_32_F2_30_read  */
	{
		RD_SLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0xF2), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_END,
	},
	/*  fpu_entry_slc_4p_32_30_read  */
	{
		RD_SLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_END,
	},

	/*  fpu_entry_slc_4p_32_31_read  */
	{
		RD_SLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_END,
	},

	/*	fpu_entry_slc_3p_32_30_read */
	{
		RD_SLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_END,
	},

	/* 	fpu_entry_slc_3p_32_31_read */
	{
		RD_SLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_END,
	},


	/*
	 * Read command: tlc mode + plane cache mode.
	 */

	/*  fpu_entry_tlc_1p_30_lower_pg_fast_read  */
	{
		//No FPU_END
		TOSHIBA_FAST_READ_PREFIX,
	},
	/*  fpu_entry_tlc_1p_30_lower_pg_read  */
	{
		FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_END,
	},
	/*  fpu_entry_tlc_1p_30_middle_pg_fast_read  */
	{
		//No FPU_END
		TOSHIBA_FAST_READ_PREFIX,
	},
	/*  fpu_entry_tlc_1p_30_middle_pg_read  */
	{
		FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_END,
	},
	/*  fpu_entry_tlc_1p_30_upper_pg_fast_read  */
	{
		//No FPU_END
		TOSHIBA_FAST_READ_PREFIX,
	},
	/*  fpu_entry_tlc_1p_30_upper_pg_read  */
	{
		FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_END,
	},
	/*  fpu_entry_tlc_1p_31_lower_pg_fast_read  */
	{
		//No FPU_END
		TOSHIBA_FAST_READ_PREFIX,
	},
	/*  fpu_entry_tlc_1p_31_lower_pg_read  */
	{
		FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_END,
	},
	/*  fpu_entry_tlc_1p_31_middle_pg_fast_read  */
	{
		//No FPU_END
		TOSHIBA_FAST_READ_PREFIX,
	},
	/*  fpu_entry_tlc_1p_31_middle_pg_read  */
	{
		FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_END,
	},
	/*  fpu_entry_tlc_1p_31_upper_pg_fast_read  */
	{
		//No FPU_END
		TOSHIBA_FAST_READ_PREFIX,
	},
	/*  fpu_entry_tlc_1p_31_upper_pg_read  */
	{
		FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_END,
	},

	/*  fpu_entry_tlc_2p_32_30_lower_pg_read  */
	{
		FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, // FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_END,
	},
	/*  fpu_entry_tlc_2p_32_30_middle_pg_read  */
	{
		FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_END,
	},
	/*  fpu_entry_tlc_2p_32_30_upper_pg_read  */
	{
		FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_END,
	},
	/*  fpu_entry_tlc_2p_32_31_lower_pg_read  */
	{
		FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_END,
	},
	/*  fpu_entry_tlc_2p_32_31_middle_pg_read  */
	{
		FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_END,
	},
	/*  fpu_entry_tlc_2p_32_31_upper_pg_read  */
	{
		FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_END,
	},
	/*  fpu_entry_tlc_2p_32_F1_30_lower_pg_read  */
	{
		FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0xF1), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_END,
	},
	/*  fpu_entry_tlc_2p_32_F1_30_middle_pg_read  */
	{
		FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0xF1), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_END,
	},
	/*  fpu_entry_tlc_2p_32_F1_30_upper_pg_read  */
	{
		FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0xF1), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_END,
	},
	/*  fpu_entry_tlc_2p_32_F2_30_lower_pg_read  */
	{
		FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0xF2), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_END,
	},
	/*  fpu_entry_tlc_2p_32_F2_30_middle_pg_read  */
	{
		FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0xF2), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_END,
	},
	/*  fpu_entry_tlc_2p_32_F2_30_upper_pg_read  */
	{
		FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0xF2), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_END,
	},
	/* fpu_entry_tlc_3p_32_30_lower_pg_read */
	{
		FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_END,
	},
	/*  fpu_entry_tlc_3p_32_30_middle_pg_read */
	{
		FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_END,
	},
	/*  fpu_entry_tlc_3p_32_30_upper_pg_read */
	{
		FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_END,
	},
	/* fpu_entry_tlc_3p_32_31_lower_pg_read */
	{
		FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_END,
	},
	/*  fpu_entry_tlc_3p_32_31_middle_pg_read */
	{
		FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_END,
	},
	/*  fpu_entry_tlc_3p_32_31_upper_pg_read */
	{
		FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_END,
	},
	/*  fpu_entry_tlc_4p_32_30_lower_pg_read */
	{
		FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_END,
	},
	/*  fpu_entry_tlc_4p_32_30_middle_pg_read */
	{
		FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_END,
	},
	/*  fpu_entry_tlc_4p_32_30_upper_pg_read */
	{
		FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_END,
	},
	/*  fpu_entry_tlc_4p_32_31_lower_pg_read */
	{
		FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_END,
	},
	/*  fpu_entry_tlc_4p_32_31_middle_pg_read */
	{
		FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_END,
	},
	/*  fpu_entry_tlc_4p_32_31_upper_pg_read */
	{
		FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x32), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_NOP, //FPU_DLY(0x40),
		FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_END,
	},

	/*
	 * end of common region (need by loader)
	 */

	/*  fpu_common_region_delimiter, used for checking common_region size <= 2KB  */
	{
		FPU_NOP, FPU_END,
	},


	/*
	 * Program command: slc / mlc mode.
	 */


	/*  fpu_entry_prog_mlc_80_11, Page Program w/ fsa  */
	{
		WR_MLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_NOP, FPU_END,
	},
	/*  fpu_entry_prog_mlc_80_15, Page Program w/ fsa  */
	{
		WR_MLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x15), FPU_END,
	},
	/*  fpu_entry_prog_slc_80_11, Page Program w/ fsa  */
	{
		WR_SLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_NOP, FPU_END,
	},
#if (COP0_BACKUP_P4K_WORKAROUND)
	/*  fpu_entry_prog_slc_80_11_gc, Page Program w/ fsa  */
	{
		WR_SLC_PREFIX, FPU_NOP, FPU_NOP, FPU_NOP, FPU_NOP,
		FPU_ADR_GEN, FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_DLY(0x40), FPU_END,
	},
#endif /*(COP0_BACKUP_P4K_WORKAROUND)*/
	/*  fpu_entry_prog_slc_80_15, Page Program w/ fsa  */
	{
		WR_SLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x15), FPU_END,
	},
#if (COP0_BACKUP_P4K_WORKAROUND)
	/*  fpu_entry_prog_slc_80_15_gc, Page Program w/ fsa  */
	{
		WR_SLC_PREFIX, FPU_NOP, FPU_NOP, FPU_NOP, FPU_NOP,
		FPU_ADR_GEN, FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x15), FPU_END,
	},
#endif /*(COP0_BACKUP_P4K_WORKAROUND)*/

	/*  fpu_entry_prog_slc_80_11_F1, Page Program w/ fsa  */
	{
		WR_SLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0xF1), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_END,
	},
	/*  fpu_entry_prog_slc_80_11_F2, Page Program w/ fsa  */
	{
		WR_SLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0xF2), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_END,
	},

	/*  fpu_entry_mlc_prog, Page Program w/ fsa  */
	{
		WR_MLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x10), FPU_END,
	},

	/*  fpu_entry_slc_prog, A2 Page Program w/ fsa  */
	{
		WR_SLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x10), FPU_END,
	},
#if (COP0_BACKUP_P4K_WORKAROUND)
	/*  fpu_entry_slc_prog_gc, A2 Page Program w/ fsa  */
	{
		WR_SLC_PREFIX, FPU_NOP, FPU_NOP, FPU_NOP, FPU_NOP,
		FPU_ADR_GEN, FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x10), FPU_END,
	},
#endif /*(COP0_BACKUP_P4K_WORKAROUND)*/
	// william need cache program
	{
		FPU_CMD(0xA2), FPU_CMD_DQS(0x85), FPU_DLY(0x10), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_NOP, FPU_END,	// FPU_PTR_CA2_C85_A5_DW
	},

	/*
	 * program command: tlc mode.
	 */
	/*  fpu_entry_first_upper_prog_10  */
	{
		FPU_ADR_GEN, FPU_CMD(0x09), FPU_CMD(0x03), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x10), FPU_END,
	},
	/*  fpu_entry_foggy_upper_prog_10  */
	{
		FPU_ADR_GEN, FPU_CMD(0x0D), FPU_CMD(0x03), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x10), FPU_END,
	},
	/*  fpu_entry_fine_upper_prog_10  */
	{
		FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x10), FPU_END,
	},

	/*  fpu_entry_first_lower_prog_1A  */
	{
		FPU_ADR_GEN, FPU_CMD(0x09), FPU_CMD(0x01), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD(0x1A), FPU_END,
	},
	/*  fpu_entry_first_middle_prog_1A  */
	{
		FPU_ADR_GEN, FPU_CMD(0x09), FPU_CMD(0x02), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD(0x1A), FPU_END,
	},
	/*  fpu_entry_foggy_lower_prog_1A  */
	{
		FPU_ADR_GEN, FPU_CMD(0x0D), FPU_CMD(0x01), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD(0x1A), FPU_END,
	},
	/*  fpu_entry_foggy_middle_prog_1A  */
	{
		FPU_ADR_GEN, FPU_CMD(0x0D), FPU_CMD(0x02), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD(0x1A), FPU_END,
	},
	/*  fpu_entry_fine_lower_prog_1A  */
	{
		FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD(0x1A), FPU_END,
	},
	/*  fpu_entry_fine_middle_prog_1A  */
	{
		FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD(0x1A), FPU_END,
	},

	/*  fpu_entry_first_lower_prog_11  */
	{
		FPU_ADR_GEN, FPU_CMD(0x09), FPU_CMD(0x01), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_NOP, FPU_END,
	},
	/*  fpu_entry_first_middle_prog_11  */
	{
		FPU_ADR_GEN, FPU_CMD(0x09), FPU_CMD(0x02), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_NOP, FPU_END,
	},
	/*  fpu_entry_first_upper_prog_11  */
	{
		FPU_ADR_GEN, FPU_CMD(0x09), FPU_CMD(0x03), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_NOP, FPU_END,
	},
	/*  fpu_entry_foggy_lower_prog_11  */
	{
		FPU_ADR_GEN, FPU_CMD(0x0D), FPU_CMD(0x01), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_NOP, FPU_END,
	},
	/*  fpu_entry_foggy_middle_prog_11  */
	{
		FPU_ADR_GEN, FPU_CMD(0x0D), FPU_CMD(0x02), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_NOP, FPU_END,
	},
	/*  fpu_entry_foggy_upper_prog_11  */
	{
		FPU_ADR_GEN, FPU_CMD(0x0D), FPU_CMD(0x03), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_NOP, FPU_END,
	},
	/*  fpu_entry_fine_lower_prog_11  */
	{
		FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_NOP, FPU_END,
	},
	/*  fpu_entry_fine_middle_prog_11  */
	{
		FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_NOP, FPU_END,
	},
	/*  fpu_entry_fine_upper_prog_11  */
	{
		FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_NOP, FPU_END,
	},


	/*  fpu_entry_first_upper_prog_15  */
	{
		FPU_ADR_GEN, FPU_CMD(0x09), FPU_CMD(0x03), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x15), FPU_END,
	},
	/*  fpu_entry_foggy_upper_prog_15  */
	{
		FPU_ADR_GEN, FPU_CMD(0x0D), FPU_CMD(0x03), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x15), FPU_END,
	},
	/*  fpu_entry_fine_upper_prog_15  */
	{
		FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x15), FPU_END,
	},

	/*  fpu_entry_first_lower_prog_11_F1  */
	{
		FPU_ADR_GEN, FPU_CMD(0x09), FPU_CMD(0x01), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0xF1), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_END,
	},
	/*  fpu_entry_first_middle_prog_11_F1  */
	{
		FPU_ADR_GEN, FPU_CMD(0x09), FPU_CMD(0x02), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0xF1), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_END,
	},
	/*  fpu_entry_first_upper_prog_11_F1  */
	{
		FPU_ADR_GEN, FPU_CMD(0x09), FPU_CMD(0x03), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0xF1), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_END,
	},
	/*  fpu_entry_foggy_lower_prog_11_F1  */
	{
		FPU_ADR_GEN, FPU_CMD(0x0D), FPU_CMD(0x01), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0xF1), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_END,
	},
	/*  fpu_entry_foggy_middle_prog_11_F1  */
	{
		FPU_ADR_GEN, FPU_CMD(0x0D), FPU_CMD(0x02), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0xF1), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_END,
	},
	/*  fpu_entry_foggy_upper_prog_11_F1  */
	{
		FPU_ADR_GEN, FPU_CMD(0x0D), FPU_CMD(0x03), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0xF1), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_END,
	},
	/*  fpu_entry_fine_lower_prog_11_F1  */
	{
		FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0xF1), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_END,
	},
	/*  fpu_entry_fine_middle_prog_11_F1  */
	{
		FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0xF1), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_END,
	},
	/*  fpu_entry_fine_upper_prog_11_F1  */
	{
		FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0xF1), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_END,
	},


	/*  fpu_entry_first_lower_prog_11_F2  */
	{
		FPU_ADR_GEN, FPU_CMD(0x09), FPU_CMD(0x01), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0xF2), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_END,
	},
	/*  fpu_entry_first_middle_prog_11_F2  */
	{
		FPU_ADR_GEN, FPU_CMD(0x09), FPU_CMD(0x02), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0xF2), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_END,
	},
	/*  fpu_entry_first_upper_prog_11_F2  */
	{
		FPU_ADR_GEN, FPU_CMD(0x09), FPU_CMD(0x03), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0xF2), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_END,
	},
	/*  fpu_entry_foggy_lower_prog_11_F2  */
	{
		FPU_ADR_GEN, FPU_CMD(0x0D), FPU_CMD(0x01), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0xF2), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_END,
	},
	/*  fpu_entry_foggy_middle_prog_11_F2  */
	{
		FPU_ADR_GEN, FPU_CMD(0x0D), FPU_CMD(0x02), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0xF2), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_END,
	},
	/*  fpu_entry_foggy_upper_prog_11_F2  */
	{
		FPU_ADR_GEN, FPU_CMD(0x0D), FPU_CMD(0x03), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0xF2), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_END,
	},
	/*  fpu_entry_fine_lower_prog_11_F2  */
	{
		FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0xF2), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_END,
	},
	/*  fpu_entry_fine_middle_prog_11_F2  */
	{
		FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0xF2), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_END,
	},
	/*  fpu_entry_fine_upper_prog_11_F2  */
	{
		FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0xF2), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_END,
	},


#if OPT_SUPPORT_INTERNAL_COPYBACK
	/*First  fpu_entry_first_lower_85_11_1a_copyback  */
	{
		/*FPU_ADR_GEN, FPU_CMD(0x09), FPU_CMD(0x01), FPU_CMD_DQS(0x85), FPU_ADR(5), FPU_DLY(0x10), FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xC0),
		FPU_ADR_GEN, FPU_CMD(0x09), FPU_CMD(0x01), FPU_CMD_DQS(0x85), FPU_ADR(5), FPU_DLY(0x10), FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xC0),*/
		FPU_ADR_GEN, FPU_CMD(0x09), FPU_CMD(0x01), FPU_CMD_DQS(0x85), FPU_ADR(5), FPU_DLY(0x10), FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xC0),
		FPU_ADR_GEN, FPU_CMD(0x09), FPU_CMD(0x01), FPU_CMD_DQS(0x85), FPU_ADR(5), FPU_DLY(0x10), FPU_CMD(0x1A), FPU_DLY(0x10), FPU_END,
	},
	/*  fpu_entry_first_middle_85_11_1a_copyback  */
	{
		/*FPU_ADR_GEN, FPU_CMD(0x09), FPU_CMD(0x02), FPU_CMD_DQS(0x85), FPU_ADR(5), FPU_DLY(0x10), FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xC0),
		FPU_ADR_GEN, FPU_CMD(0x09), FPU_CMD(0x02), FPU_CMD_DQS(0x85), FPU_ADR(5), FPU_DLY(0x10), FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xC0),*/
		FPU_ADR_GEN, FPU_CMD(0x09), FPU_CMD(0x02), FPU_CMD_DQS(0x85), FPU_ADR(5), FPU_DLY(0x10), FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xC0),
		FPU_ADR_GEN, FPU_CMD(0x09), FPU_CMD(0x02), FPU_CMD_DQS(0x85), FPU_ADR(5), FPU_DLY(0x10), FPU_CMD(0x1A), FPU_DLY(0x10), FPU_END,
	},
	/*  fpu_entry_first_upper_85_11_10_copyback  */
	{
		/*FPU_ADR_GEN, FPU_CMD(0x09), FPU_CMD(0x03), FPU_CMD_DQS(0x85), FPU_ADR(5), FPU_DLY(0x10), FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xC0),
		FPU_ADR_GEN, FPU_CMD(0x09), FPU_CMD(0x03), FPU_CMD_DQS(0x85), FPU_ADR(5), FPU_DLY(0x10), FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xC0),*/
		FPU_ADR_GEN, FPU_CMD(0x09), FPU_CMD(0x03), FPU_CMD_DQS(0x85), FPU_ADR(5), FPU_DLY(0x10), FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xC0),
		FPU_ADR_GEN, FPU_CMD(0x09), FPU_CMD(0x03), FPU_CMD_DQS(0x85), FPU_ADR(5), FPU_DLY(0x10), FPU_CMD_DQS(0x10), FPU_DLY(0x10), FPU_END,
	},
	/*Foggy  fpu_entry_foggy_lower_85_11_1a_copyback  */
	{
		/*FPU_ADR_GEN, FPU_CMD(0x0D), FPU_CMD(0x01), FPU_CMD_DQS(0x85), FPU_ADR(5), FPU_DLY(0x10), FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xC0),
		FPU_ADR_GEN, FPU_CMD(0x0D), FPU_CMD(0x01), FPU_CMD_DQS(0x85), FPU_ADR(5), FPU_DLY(0x10), FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xC0),*/
		FPU_ADR_GEN, FPU_CMD(0x0D), FPU_CMD(0x01), FPU_CMD_DQS(0x85), FPU_ADR(5), FPU_DLY(0x10), FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xC0),
		FPU_ADR_GEN, FPU_CMD(0x0D), FPU_CMD(0x01), FPU_CMD_DQS(0x85), FPU_ADR(5), FPU_DLY(0x10), FPU_CMD(0x1A), FPU_DLY(0x10), FPU_END,
	},
	/*  fpu_entry_foggy_middle_85_11_1a_copyback  */
	{
		/*FPU_ADR_GEN, FPU_CMD(0x0D), FPU_CMD(0x02), FPU_CMD_DQS(0x85), FPU_ADR(5), FPU_DLY(0x10), FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xC0),
		FPU_ADR_GEN, FPU_CMD(0x0D), FPU_CMD(0x02), FPU_CMD_DQS(0x85), FPU_ADR(5), FPU_DLY(0x10), FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xC0),*/
		FPU_ADR_GEN, FPU_CMD(0x0D), FPU_CMD(0x02), FPU_CMD_DQS(0x85), FPU_ADR(5), FPU_DLY(0x10), FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xC0),
		FPU_ADR_GEN, FPU_CMD(0x0D), FPU_CMD(0x02), FPU_CMD_DQS(0x85), FPU_ADR(5), FPU_DLY(0x10), FPU_CMD(0x1A), FPU_DLY(0x10), FPU_END,
	},
	/*  fpu_entry_foggy_upper_85_11_10_copyback  */
	{
		/*FPU_ADR_GEN, FPU_CMD(0x0D), FPU_CMD(0x03), FPU_CMD_DQS(0x85), FPU_ADR(5), FPU_DLY(0x10), FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xC0),
		FPU_ADR_GEN, FPU_CMD(0x0D), FPU_CMD(0x03), FPU_CMD_DQS(0x85), FPU_ADR(5), FPU_DLY(0x10), FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xC0),*/
		FPU_ADR_GEN, FPU_CMD(0x0D), FPU_CMD(0x03), FPU_CMD_DQS(0x85), FPU_ADR(5), FPU_DLY(0x10), FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xC0),
		FPU_ADR_GEN, FPU_CMD(0x0D), FPU_CMD(0x03), FPU_CMD_DQS(0x85), FPU_ADR(5), FPU_DLY(0x10), FPU_CMD_DQS(0x10), FPU_DLY(0x10), FPU_END,
	},
	/*Fine  fpu_entry_fine_lower_85_11_1a_copyback  */
	{
		/*FPU_ADR_GEN,              FPU_CMD(0x01), FPU_CMD_DQS(0x85), FPU_ADR(5), FPU_DLY(0x10), FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xC0),
		FPU_ADR_GEN,                FPU_CMD(0x01), FPU_CMD_DQS(0x85), FPU_ADR(5), FPU_DLY(0x10), FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xC0),*/
		FPU_ADR_GEN,                FPU_CMD(0x01), FPU_CMD_DQS(0x85), FPU_ADR(5), FPU_DLY(0x10), FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xC0),
		FPU_ADR_GEN,                FPU_CMD(0x01), FPU_CMD_DQS(0x85), FPU_ADR(5), FPU_DLY(0x10), FPU_CMD(0x1A), FPU_DLY(0x10), FPU_END,
	},
	/*  fpu_entry_fine_middle_85_11_1a_copyback  */
	{
		/*FPU_ADR_GEN,              FPU_CMD(0x02), FPU_CMD_DQS(0x85), FPU_ADR(5), FPU_DLY(0x10), FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xC0),
		FPU_ADR_GEN,                FPU_CMD(0x02), FPU_CMD_DQS(0x85), FPU_ADR(5), FPU_DLY(0x10), FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xC0),*/
		FPU_ADR_GEN,                FPU_CMD(0x02), FPU_CMD_DQS(0x85), FPU_ADR(5), FPU_DLY(0x10), FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xC0),
		FPU_ADR_GEN,                FPU_CMD(0x02), FPU_CMD_DQS(0x85), FPU_ADR(5), FPU_DLY(0x10), FPU_CMD(0x1A), FPU_DLY(0x10), FPU_END,
	},
	/*  fpu_entry_fine_upper_85_11_10_copyback  */
	{
		/*FPU_ADR_GEN,              FPU_CMD(0x03), FPU_CMD_DQS(0x85), FPU_ADR(5), FPU_DLY(0x10), FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xC0),
		FPU_ADR_GEN,                FPU_CMD(0x03), FPU_CMD_DQS(0x85), FPU_ADR(5), FPU_DLY(0x10), FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xC0),*/
		FPU_ADR_GEN,                FPU_CMD(0x03), FPU_CMD_DQS(0x85), FPU_ADR(5), FPU_DLY(0x10), FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xC0),
		FPU_ADR_GEN,                FPU_CMD(0x03), FPU_CMD_DQS(0x85), FPU_ADR(5), FPU_DLY(0x10), FPU_CMD_DQS(0x10), FPU_DLY(0x10), FPU_END,
	},
#endif /* OPT_SUPPORT_INTERNAL_COPYBACK */


	/*  fpu_entry_fsp_upper_prog_10  */
	{
		FPU_GC_BACKUP_P4K_PENDING_DELAY FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x10), FPU_END,
	},

	/*  fpu_entry_fsp_lower_prog_1A  */
	{
		FPU_GC_BACKUP_P4K_PENDING_DELAY FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD(0x1A), FPU_END,
	},
	/*  fpu_entry_fsp_middle_prog_1A  */
	{
		FPU_GC_BACKUP_P4K_PENDING_DELAY FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD(0x1A), FPU_END,
	},

	/*  fpu_entry_fsp_lower_prog_1A_F1  */
	{
		FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD(0x1A), FPU_DLY(0x10), FPU_CMD(0xF1), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_END,
	},
	/*  fpu_entry_fsp_middle_prog_1A_F1  */
	{
		FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD(0x1A), FPU_DLY(0x10), FPU_CMD(0xF1), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_END,
	},

	/*  fpu_entry_fsp_lower_prog_1A_F2  */
	{
		FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD(0x1A), FPU_DLY(0x10), FPU_CMD(0xF1), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_END,
	},
	/*  fpu_entry_fsp_middle_prog_1A_F2  */
	{
		FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD(0x1A), FPU_DLY(0x10), FPU_CMD(0xF1), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_END,
	},

	/*  fpu_entry_fsp_lower_prog_11  */
	{
		FPU_GC_BACKUP_P4K_PENDING_DELAY FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_NOP, FPU_END,
	},
	/*  fpu_entry_fsp_middle_prog_11  */
	{
		FPU_GC_BACKUP_P4K_PENDING_DELAY FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_NOP, FPU_END,
	},
	/*  fpu_entry_fsp_upper_prog_11  */
	{
		FPU_GC_BACKUP_P4K_PENDING_DELAY FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_NOP, FPU_END,
	},

	/*  fpu_entry_fsp_upper_prog_15  */
	{
		FPU_GC_BACKUP_P4K_PENDING_DELAY FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x15), FPU_END,
	},

	/*  fpu_entry_fsp_lower_prog_11_F1  */
	{
		FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0xF1), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_END,
	},
	/*  fpu_entry_fsp_middle_prog_11_F1  */
	{
		FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0xF1), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_END,
	},
	/*  fpu_entry_fsp_upper_prog_11_F1  */
	{
		FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0xF1), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_END,
	},

	/*  fpu_entry_fsp_lower_prog_11_F2  */
	{
		FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0xF2), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_END,
	},
	/*  fpu_entry_fsp_middle_prog_11_F2  */
	{
		FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0xF2), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_END,
	},
	/*  fpu_entry_fsp_upper_prog_11_F2  */
	{
		FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x11), FPU_DLY(0x10), FPU_CMD(0xF2), FPU_DLY(0x40), FPU_POL_MASK(0xC0), FPU_END,
	},

	/*
	 * Error Handle - Softbit
	 */

	/*  fpu_sandisk_test_mode_entry  */
	{
		FPU_CMD(0x5C), FPU_CMD(0xC5), FPU_CMD(0x55), FPU_ADR_1B(0x00), FPU_DAT_W_1B(0x01), FPU_END,

	},
	/*  fpu_entry_sndk_tlc_rr_pre  */
	{
		FPU_CMD(0x5C), FPU_CMD(0xC5), FPU_END,

	},

	/*  fpu_entry_tsb_2dtlc_rr  */
	{
		FPU_CMD(0x55), FPU_ADR_1B(0x04), FPU_DAT_W_1B(0x00), FPU_CMD(0x55), FPU_ADR_1B(0x05),
		FPU_DAT_W_1B(0x00), FPU_CMD(0x55), FPU_ADR_1B(0x06), FPU_DAT_W_1B(0x00), FPU_CMD(0x55),
		FPU_ADR_1B(0x07), FPU_DAT_W_1B(0x00), FPU_CMD(0x55), FPU_ADR_1B(0x08), FPU_DAT_W_1B(0x00),
		FPU_CMD(0x55), FPU_ADR_1B(0x09), FPU_DAT_W_1B(0x00), FPU_CMD(0x55), FPU_ADR_1B(0x0A),
		FPU_DAT_W_1B(0x00), FPU_DLY(0x30), FPU_CMD(0x5D), FPU_END,

	},
	/*  fpu_entry_tsb_3dtlc_rr  */
	{
		FPU_CMD(0x55), FPU_ADR_1B(0x03), FPU_DAT_W_1B(0x00), FPU_CMD(0x55), FPU_ADR_1B(0x04),
		FPU_DAT_W_1B(0x00), FPU_CMD(0x55), FPU_ADR_1B(0x05), FPU_DAT_W_1B(0x00), FPU_CMD(0x55),
		FPU_ADR_1B(0x06), FPU_DAT_W_1B(0x00), FPU_CMD(0x55), FPU_ADR_1B(0x07), FPU_DAT_W_1B(0x00),
		FPU_CMD(0x55), FPU_ADR_1B(0x08), FPU_DAT_W_1B(0x00), FPU_CMD(0x55), FPU_ADR_1B(0x09),
		FPU_DAT_W_1B(0x00), FPU_DLY(0x30), FPU_CMD(0x5D), FPU_END,

	},
	/*  fpu_sandisk_select_state_and_single_state_read  */
	{
		FPU_CMD(0x55), FPU_ADR_1B(0x01), FPU_DAT_W_1B(0x00), FPU_DLY(0x10),
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0xAE),
		FPU_DLY(0x10), FPU_END, FPU_NOP,
	},
	/* fpu_sandisk_select_state_and_5D_single_state_read */
	{
		FPU_CMD(0x55), FPU_ADR_1B(0x01), FPU_DAT_W_1B(0x00), FPU_DLY(0x10),
		FPU_ADR_GEN, FPU_CMD(0x5D), FPU_CMD(0x00), FPU_ADR(5),
		FPU_CMD(0xAE), FPU_DLY(0x10), FPU_END, FPU_NOP,
	},

	/*  fpu_sandisk_tlc_sb_read_lower  */
	{
		FPU_ADR_GEN, FPU_CMD(0xC2), FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_DLY(0x10), FPU_END,
	},
	/*  fpu_sandisk_tlc_sb_read_middle  */
	{
		FPU_ADR_GEN, FPU_CMD(0xC2), FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_DLY(0x10), FPU_END,
	},
	/*  fpu_sandisk_tlc_sb_read_upper  */
	{
		FPU_ADR_GEN, FPU_CMD(0xC2), FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_DLY(0x10), FPU_END,
	},

	/*  fpu_sandisk_tlc_bics3_sb_read  */
	{
		FPU_ADR_GEN, FPU_CMD(0x5D), FPU_CMD(0x00), FPU_CMD(0xC2), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x3C), FPU_DLY(0x10),
		FPU_CMD_DQS(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xE0), FPU_CMD(0x3F), FPU_DLY(0x10), FPU_END,
	},

	/*  fpu_entry_tsb_tlc_xnor  */
	{
		FPU_CMD(0xCB), FPU_CMD(0x70), FPU_DLY(0x60), FPU_POL_MASK(0xE0), FPU_CMD(0x3F),
		FPU_CMD_DQS(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xE0), FPU_END,

	},
	/*  fpu_entry_sndk_tlc_xnor  */
	{
		FPU_CMD(0x3F), FPU_CMD_DQS(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xE0), FPU_END,

	},

	/*  fpu_entry_tsb_tlc_manual_read_c  */
	{
		FPU_CMD(0x55), FPU_ADR_1B(0x01), FPU_DAT_W_1B(0x03), FPU_END,
	},
	/*  fpu_entry_tsb_tlc_manual_read_e  */
	{
		FPU_CMD(0x55), FPU_ADR_1B(0x01), FPU_DAT_W_1B(0x05), FPU_END,

	},
	/*  fpu_entry_tsb_tlc_sb_read  */
	{
		FPU_ADR_GEN, FPU_CMD(0x5D), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0xAE), FPU_DLY(0x10), FPU_END,

	},
	/*  fpu_entry_sndk_tlc_sb_read  */
	{
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0xAE), FPU_DLY(0x10), FPU_END,

	},
	/*  fpu_entry_tsb_tlc_rr_exit  */
	{
		FPU_CMD(0x55), FPU_ADR_1B(0x04), FPU_DAT_W_1B(0x00), FPU_CMD(0x55), FPU_ADR_1B(0x05),
		FPU_DAT_W_1B(0x00), FPU_CMD(0x55), FPU_ADR_1B(0x06), FPU_DAT_W_1B(0x00), FPU_CMD(0x55),
		FPU_ADR_1B(0x07), FPU_DAT_W_1B(0x00), FPU_CMD(0x55), FPU_ADR_1B(0x08), FPU_DAT_W_1B(0x00),
		FPU_CMD(0x55), FPU_ADR_1B(0x09), FPU_DAT_W_1B(0x00), FPU_CMD(0x55), FPU_ADR_1B(0x0A),
		FPU_DAT_W_1B(0x00), FPU_DLY(0x30), FPU_CMD(0xFF), FPU_DLY(0x10), FPU_END,
	},
	/*  fpu_entry_sbc  */
	{
		FPU_SBC(0x00), FPU_END,

	},
	//For E11 verification
	/*  fpu_entry_adg_test  */
	{
		FPU_ADR_GEN, FPU_ADR(0x05), FPU_END,
	},
	/*  fpu_entry_check_status_to_ready  */
	{
		FPU_CMD(0x70), FPU_DLY(0x10), FPU_POL_MASK(BIT6), FPU_NOP, FPU_END,
	},

	/*  fpu_entry_test_CA2_C00_A5_C30  */
	{
		/*0x0000*/ FPU_CMD(0xA2), FPU_CMD(0x00), FPU_ADR(0x05), FPU_CMD(0x30), FPU_NOP, FPU_NOP, FPU_NOP, FPU_END,
	},

	/*  fpu_entry_test_C05_A5_CE0_DR  */
	{
		/*0x0010*/	FPU_CMD(0x05), FPU_ADR(0x05), FPU_CMD(0xE0), FPU_DLY(0x10), FPU_DMA_R, FPU_NOP, FPU_NOP, FPU_END,
	},

	/*  fpu_reserved4  */
	{
		/*0x0020*/
		FPU_NOP, FPU_NOP, FPU_NOP, FPU_NOP,
		FPU_NOP, FPU_NOP, FPU_NOP, FPU_NOP
	},

	/*  fpu_reserved1  */
	{
		/*0x0030*/
		FPU_NOP, FPU_NOP, FPU_NOP, FPU_NOP,
		FPU_NOP, FPU_NOP, FPU_NOP, FPU_NOP
	},

	/*  fpu_entry_test_C70_POL_MK40_C00  */
	{
		/*0x0040*/	FPU_CMD(0x70), FPU_DLY(0x18), FPU_POL_MASK(0x40), FPU_CMD(0x00), FPU_NOP, FPU_NOP, FPU_NOP, FPU_END,
	},

	//below is testing usage
	/*  fpu_entry_test_CA2_C80_A5_DW_C10  */
	{
		/*0x0050*/	FPU_CMD(0xA2), FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_CMD_DQS(0x10), FPU_NOP, FPU_END,
	},

	/*  fpu_entry_test_C85_A5_DW  */
	{
		/*0x0060*/	FPU_CMD_DQS(0x85), FPU_DLY(0x10), FPU_ADR(5), FPU_DLY(0x10), FPU_DMA_W, FPU_NOP, FPU_NOP, FPU_END,
	},

	/*  fpu_entry_test_CA2_C60_A3_CD0  */
	{
		/*0x0070*/	FPU_CMD(0xA2), FPU_CMD(0x60), FPU_ADR(0x03), FPU_CMD(0xD0), FPU_DLY(0x10), FPU_NOP, FPU_NOP, FPU_END
	},

	/* fpu_entry_70_e0 */
	{
		FPU_CMD(0x70), FPU_DLY(0x60), FPU_POL_MASK(0xE0), FPU_END
	},

	/* fpu_set_feature*/
	{
		0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
		0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, FPU_END
	},
	/* fpu_get_feature */
	{
		0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, FPU_END
	},
	/* fpu_raw_dma_read */
	{
		FPU_ADR_GEN, FPU_CMD(0x05), FPU_ADR(5), FPU_CMD(0xE0),
		FPU_NOP, FPU_NOP, FPU_DLY(0x60), FPU_DMA_R_RAW(0x00),  // !!!  DON'T CHANGE THE POSITION OF "FPU_DMA_R_RAW(0x00)"  !!!
		FPU_END
	},

	/* fpu_tlc_read_lower */
	{
		FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_NOP, FPU_END
	},
	/* fpu_tlc_read_middle */
	{
		FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_NOP, FPU_END
	},
	/* fpu_tlc_read_upper */
	{
		FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_NOP, FPU_END
	},

	/* fpu_tlc_rr_lower */
	{
		FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x3C), FPU_NOP, FPU_END
	},
	/* fpu_tlc_rr_middle */
	{
		FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x3C), FPU_NOP, FPU_END
	},
	/* fpu_tlc_rr_upper */
	{
		FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x3C), FPU_NOP, FPU_END
	},

	/* fpu_sbc */
	{
		FPU_SBC(0x00), FPU_END
	},
	/* fpu_backup_restore_ibuf */
	{
		FPU_BR(0x00), FPU_BR(0x00), FPU_BR(0x00), FPU_BR(0x00), FPU_END
	},

	/* fpu_00_30_read */
	{
		FPU_ADR_GEN,  FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_NOP, FPU_END
	},
	/* fpu_ram_cmd */
	{
		FPU_ADR_GEN, FPU_CMD(0x05), FPU_ADR(5), FPU_CMD(0xE0), FPU_DMA_R_RAW(FPU_RAW_1ST_FRM | FPU_RAW_LOGIC_NOP), FPU_DMA_R_RAW(FPU_RAW_2ND_FRM | FPU_RAW_LOGIC_NOP), FPU_END
	},
	/* fpu_correct */
	{
		FPU_ADR_GEN, FPU_DMA_R_COR(0x00), FPU_END
	},
	/* fpu_reserved2 */
	{
		FPU_NOP, FPU_NOP, FPU_NOP, FPU_NOP,
		FPU_NOP, FPU_NOP, FPU_NOP, FPU_NOP,
		FPU_NOP
	},
#if RETRY_HARDBIT_FOR_SDK_EN
	/* fpu_tsb_bics3_sing_read */
	{
		0, 0, 0, 0, 0, 0, 0, FPU_END
	},
	/* fpu_tsb_tlc_xnor */
	{
		0, 0, 0, 0, 0, 0, 0, 0, FPU_END
	},
	/* fpu_tsb_tlc_hb_rr */
	{
		0, 0, 0, 0, 0, 0, 0, FPU_END
	},

	/* fpu_tsb_1st_relax_read_lower */
	{
		0, 0, 0, 0, 0, 0, 0, FPU_END
	},
	/* fpu_tsb_1st_relax_read_middle */
	{
		0, 0, 0, 0, 0, 0, 0, FPU_END
	},
	/* fpu_tsb_1st_relax_read_upper */
	{
		0, 0, 0, 0, 0, 0, 0, FPU_END
	},
	/* fpu_tsb_other_relax_read_lower */
	{
		0, 0, 0, 0, 0, 0, 0, FPU_END
	},
	/* fpu_tsb_other_relax_read_middle */
	{
		0, 0, 0, 0, 0, 0, 0, FPU_END
	},
	/* fpu_tsb_other_relax_read_upper */
	{
		0, 0, 0, 0, 0, 0, 0, FPU_END
	},
#else /* RETRY_HARDBIT_FOR_SDK_EN */
	/* fpu_tsb_bics3_sing_read */
	{
		FPU_CMD(0xC9), FPU_ADR_1B(0x00), FPU_CMD(0x00), FPU_ADR_GEN, FPU_ADR(5), FPU_CMD(0x30), FPU_NOP, FPU_END
	},
	/* fpu_tsb_tlc_xnor */
	{
		FPU_CMD(0xCB), FPU_CMD_DQS(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xE0), FPU_CMD(0x3F), FPU_CMD_DQS(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xE0),
		FPU_END
	},
	/* fpu_tsb_tlc_hb_rr */
	{
		FPU_CMD(0xD5), FPU_ADR_1B(0x00), FPU_ADR_1B(0x89), FPU_DAT_W_1B(0x00), FPU_DAT_W_1B(0x00), FPU_DAT_W_1B(0x00), FPU_DAT_W_1B(0x00), FPU_END
	},

	/* fpu_tsb_1st_relax_read_lower */
	{
		FPU_ADR_GEN, FPU_CMD(0x26), FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_NOP, FPU_END
	},
	/* fpu_tsb_1st_relax_read_middle */
	{
		FPU_ADR_GEN, FPU_CMD(0x26), FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_NOP, FPU_END
	},
	/* fpu_tsb_1st_relax_read_upper */
	{
		FPU_ADR_GEN, FPU_CMD(0x26), FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_NOP, FPU_END
	},
	/* fpu_tsb_other_relax_read_lower */
	{
		FPU_ADR_GEN, FPU_CMD(0xCD), FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_NOP, FPU_END
	},
	/* fpu_tsb_other_relax_read_middle */
	{
		FPU_ADR_GEN, FPU_CMD(0xCD), FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_NOP, FPU_END
	},
	/* fpu_tsb_other_relax_read_upper */
	{
		FPU_ADR_GEN, FPU_CMD(0xCD), FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_NOP, FPU_END
	},
#endif /* RETRY_HARDBIT_FOR_SDK_EN */
	/* fpu_C6_erase */
	{
		FPU_ADR_GEN, FPU_CMD(0xC6), FPU_CMD(0x60), FPU_ADR(3), FPU_CMD(0xD0), FPU_NOP, FPU_NOP,
#if NES_GEN1_EN
		/* C6 erase needs polling 0xE0 in NES mode, 2ms may timeout */
		FPU_CMD(0x70), FPU_DLY(0x10), FPU_POL_MASK(0xE0), FPU_DLY(0x10), FPU_NOP, FPU_NOP, FPU_NOP, FPU_NOP,
#endif /* NES_GEN1_EN */
		FPU_END
	},
	/*	fpu_entry_TSB_read_temperature_cmd */
	{
		FPU_CMD(0xB9), FPU_DLY(0x10), FPU_NOP, FPU_END
	},
	/* fpu_entry_TSB_read_temperature_DMA */
	{
		FPU_CMD(0x7C), FPU_DLY(0x10), FPU_DMA_R, FPU_END
	},

	/* fpu_entry_Sandisk_slc_no_dma_program */
	{
		WR_SLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD_DQS(0x80), FPU_ADR(5), FPU_DLY(0x10), FPU_CMD_DQS(0x10), FPU_END,
	},
#if RETRY_HARDBIT_FOR_SDK_EN
	/* fpu_sandisk_slc_dynamic_read */
	{
		0, 0, 0, 0, 0, 0, 0, FPU_END
	},
	/* fpu_sandisk_tlc_dynamic_read_lower */
	{
		0, 0, 0, 0, 0, 0, 0, FPU_END
	},
	/* fpu_sandisk_tlc_dynamic_read_middle */
	{
		0, 0, 0, 0, 0, 0, 0, FPU_END
	},
	/* fpu_sandisk_tlc_dynamic_read_upper */
	{
		0, 0, 0, 0, 0, 0, 0, FPU_END
	},
#else /* RETRY_HARDBIT_FOR_SDK_EN */
	/* fpu_sandisk_slc_dynamic_read */
	{
		FPU_ADR_GEN, FPU_CMD(0x5D), FPU_CMD(0xA2), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_NOP, FPU_END
	},
	/* fpu_sandisk_tlc_dynamic_read_lower */
	{
		FPU_ADR_GEN, FPU_CMD(0x5D), FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_NOP, FPU_END
	},
	/* fpu_sandisk_tlc_dynamic_read_middle */
	{
		FPU_ADR_GEN, FPU_CMD(0x5D), FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_NOP, FPU_END
	},
	/* fpu_sandisk_tlc_dynamic_read_upper */
	{
		FPU_ADR_GEN, FPU_CMD(0x5D), FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_NOP, FPU_END
	},
#endif /* RETRY_HARDBIT_FOR_SDK_EN */
	/*  BICS4_AIPR_FEATURE_EN */
	/*  fpu_entry_align */
	{
		FPU_NOP, FPU_NOP, FPU_END,
	},
	/*  fpu_entry_addr_gen_only */
	{
		FPU_ADR_GEN, FPU_NOP, FPU_NOP, FPU_END,
	},
	/* fpu_entry_tsb_bics4_aipr_slc_2p_read */
	{
		TOSHIBA_FAST_READ_PREFIX, WR_SLC_PREFIX, FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30),
		//FPU_ADR_GEN, FPU_CMD(0x78), FPU_ADR(3), FPU_DLY(0xC), FPU_NOP, FPU_POL_MASK(0x00),// FPU_ENTRY_READ_STATUS_78_C0_81_EXT
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, WR_SLC_PREFIX, FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_ADR_GEN, FPU_NOP, FPU_END,
	},
	/* fpu_entry_tsb_bics4_aipr_slc_2p_cache_read */
	{
		TOSHIBA_FAST_READ_PREFIX, WR_SLC_PREFIX, FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31),
		//FPU_ADR_GEN, FPU_CMD(0x78), FPU_ADR(3), FPU_DLY(0xC), FPU_NOP, FPU_POL_MASK(0x00),// FPU_ENTRY_READ_STATUS_78_C0_81_EXT
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, WR_SLC_PREFIX, FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_ADR_GEN, FPU_NOP, FPU_END,
	},


	//normal read
	/* fpu_entry_tsb_bics4_aipr_tlc_read_low_low */
	{
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30),
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_ADR_GEN, FPU_NOP, FPU_END,
	},
	/* fpu_entry_tsb_bics4_aipr_tlc_read_low_middle */
	{
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30),
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_ADR_GEN, FPU_NOP, FPU_END,
	},
	/* fpu_entry_tsb_bics4_aipr_tlc_read_low_upper */
	{
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30),
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_ADR_GEN, FPU_NOP, FPU_END,
	},

	/* fpu_entry_tsb_bics4_aipr_tlc_read_middle_low */
	{
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30),
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x01),  FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_ADR_GEN, FPU_NOP, FPU_END,
	},
	/* fpu_entry_tsb_bics4_aipr_tlc_read_middle_middle */
	{
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30),
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_ADR_GEN, FPU_NOP, FPU_END,
	},
	/* fpu_entry_tsb_bics4_aipr_tlc_read_middle_upper */
	{
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30),
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_ADR_GEN, FPU_NOP, FPU_END,
	},


	/* fpu_entry_tsb_bics4_aipr_tlc_read_upper_low */
	{
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30),
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_ADR_GEN, FPU_NOP, FPU_END,
	},
	/* fpu_entry_tsb_bics4_aipr_tlc_read_upper_middle */
	{
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30),
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_ADR_GEN, FPU_NOP, FPU_END,
	},
	/* fpu_entry_tsb_bics4_aipr_tlc_read_upper_upper */
	{
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30),
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_ADR_GEN, FPU_NOP, FPU_END,
	},

	//cache read
	/* fpu_entry_tsb_bics4_aipr_tlc_cache_read_low_low */
	{
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31),
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_ADR_GEN, FPU_NOP, FPU_END,
	},
	/* fpu_entry_tsb_bics4_aipr_tlc_cache_read_low_middle */
	{
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31),
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_ADR_GEN, FPU_NOP, FPU_END,
	},
	/* fpu_entry_tsb_bics4_aipr_tlc_cache_read_low_upper */
	{
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31),
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_ADR_GEN, FPU_NOP, FPU_END,
	},

	/* fpu_entry_tsb_bics4_aipr_tlc_cache_read_middle_low */
	{
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31),
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_ADR_GEN, FPU_NOP, FPU_END,
	},
	/* fpu_entry_tsb_bics4_aipr_tlc_cache_read_middle_middle */
	{
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31),
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_ADR_GEN, FPU_NOP, FPU_END,
	},
	/* fpu_entry_tsb_bics4_aipr_tlc_cache_read_middle_upper */
	{
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31),
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_ADR_GEN, FPU_NOP, FPU_END,
	},

	/* fpu_entry_tsb_bics4_aipr_tlc_cache_read_upper_low */
	{
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31),
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_ADR_GEN, FPU_NOP, FPU_END,
	},
	/* fpu_entry_tsb_bics4_aipr_tlc_cache_read_upper_middle */
	{
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31),
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_ADR_GEN, FPU_NOP, FPU_END,
	},
	/* fpu_entry_tsb_bics4_aipr_tlc_cache_read_upper_upper */
	{
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31),
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_ADR_GEN, FPU_NOP, FPU_END,
	},

	/* fpu_entry_tsb_bics4_aipr_slc_31_30_read */
	{
		TOSHIBA_FAST_READ_PREFIX, WR_SLC_PREFIX, FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31),
		//FPU_ADR_GEN, FPU_CMD(0x78), FPU_ADR(3), FPU_DLY(0xC), FPU_NOP, FPU_POL_MASK(0x00),// FPU_ENTRY_READ_STATUS_78_C0_81_EXT
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, WR_SLC_PREFIX, FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_ADR_GEN, FPU_NOP, FPU_END,
	},
	/* fpu_entry_tsb_bics4_aipr_slc_30_31_read */
	{
		TOSHIBA_FAST_READ_PREFIX, WR_SLC_PREFIX, FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30),
		//FPU_ADR_GEN, FPU_CMD(0x78), FPU_ADR(3), FPU_DLY(0xC), FPU_NOP, FPU_POL_MASK(0x00),// FPU_ENTRY_READ_STATUS_78_C0_81_EXT
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, WR_SLC_PREFIX, FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_ADR_GEN, FPU_NOP, FPU_END,
	},
	/* fpu_entry_tsb_bics4_aipr_slc_3f_31_read */
	{
		WR_SLC_PREFIX, FPU_ADR_GEN, FPU_CMD(0x78), FPU_ADR(3), FPU_CMD(0x3F),
		//FPU_ADR_GEN, FPU_CMD(0x78), FPU_ADR(3), FPU_DLY(0xC), FPU_NOP, FPU_POL_MASK(0x00),// FPU_ENTRY_READ_STATUS_78_C0_81_EXT
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, WR_SLC_PREFIX, FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_ADR_GEN, FPU_NOP, FPU_NOP, FPU_END,
	},
	/* fpu_entry_tsb_bics4_aipr_slc_31_3f_read */
	{
		TOSHIBA_FAST_READ_PREFIX, WR_SLC_PREFIX, FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31),
		//FPU_ADR_GEN, FPU_CMD(0x78), FPU_ADR(3), FPU_DLY(0xC), FPU_NOP, FPU_POL_MASK(0x00),// FPU_ENTRY_READ_STATUS_78_C0_81_EXT
		FPU_DLY(0xC),
		WR_SLC_PREFIX, FPU_ADR_GEN, FPU_CMD(0x78), FPU_ADR(3), FPU_CMD(0x3F), FPU_ADR_GEN, FPU_NOP, FPU_NOP, FPU_END,
	},

	/* fpu_entry_tsb_bics4_aipr_tlc_31_30_read_low_low */
	{
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31),
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_ADR_GEN, FPU_NOP, FPU_END,
	},
	/* fpu_entry_tsb_bics4_aipr_tlc_31_30_read_low_middle */
	{
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31),
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_ADR_GEN, FPU_NOP, FPU_END,
	},
	/* fpu_entry_tsb_bics4_aipr_tlc_31_30_read_low_upper */
	{
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31),
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_ADR_GEN, FPU_NOP, FPU_END,
	},

	/* fpu_entry_tsb_bics4_aipr_tlc_31_30_read_middle_low */
	{
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31),
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_ADR_GEN, FPU_NOP, FPU_END,
	},
	/* fpu_entry_tsb_bics4_aipr_tlc_31_30_read_middle_middle */
	{
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31),
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_ADR_GEN, FPU_NOP, FPU_END,
	},
	/* fpu_entry_tsb_bics4_aipr_tlc_31_30_read_middle_upper */
	{
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31),
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_ADR_GEN, FPU_NOP, FPU_END,
	},

	/* fpu_entry_tsb_bics4_aipr_tlc_31_30_read_upper_low */
	{
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31),
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_ADR_GEN, FPU_NOP, FPU_END,
	},
	/* fpu_entry_tsb_bics4_aipr_tlc_31_30_read_upper_middle */
	{
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31),
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_ADR_GEN, FPU_NOP, FPU_END,
	},
	/* fpu_entry_tsb_bics4_aipr_tlc_31_30_read_upper_upper */
	{
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31),
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30), FPU_ADR_GEN, FPU_NOP, FPU_END,
	},

	/* fpu_entry_tsb_bics4_aipr_tlc_30_31_read_low_low */
	{
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30),
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_ADR_GEN, FPU_NOP, FPU_END,
	},
	/* fpu_entry_tsb_bics4_aipr_tlc_30_31_read_low_middle */
	{
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30),
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_ADR_GEN, FPU_NOP, FPU_END,
	},
	/* fpu_entry_tsb_bics4_aipr_tlc_30_31_read_low_upper */
	{
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30),
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_ADR_GEN, FPU_NOP, FPU_END,
	},

	/* fpu_entry_tsb_bics4_aipr_tlc_30_31_read_middle_low */
	{
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30),
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_ADR_GEN, FPU_NOP, FPU_END,
	},
	/* fpu_entry_tsb_bics4_aipr_tlc_30_31_read_middle_middle */
	{
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30),
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_ADR_GEN, FPU_NOP, FPU_END,
	},
	/* fpu_entry_tsb_bics4_aipr_tlc_30_31_read_middle_upper */
	{
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30),
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_ADR_GEN, FPU_NOP, FPU_END,
	},

	/* fpu_entry_tsb_bics4_aipr_tlc_30_31_read_upper_low */
	{
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30),
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_ADR_GEN, FPU_NOP, FPU_END,
	},
	/* fpu_entry_tsb_bics4_aipr_tlc_30_31_read_upper_middle */
	{
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30),
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_ADR_GEN, FPU_NOP, FPU_END,
	},
	/* fpu_entry_tsb_bics4_aipr_tlc_30_31_read_upper_upper */
	{
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x30),
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_ADR_GEN, FPU_NOP, FPU_END,
	},

	/* fpu_entry_tsb_bics4_aipr_tlc_31_3f_read_low */
	{
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31),
		FPU_DLY(0xC),
		FPU_ADR_GEN, FPU_CMD(0x78), FPU_ADR(3), FPU_CMD(0x3F), FPU_ADR_GEN, FPU_NOP, FPU_NOP, FPU_NOP, FPU_END,
	},

	/* fpu_entry_tsb_bics4_aipr_tlc_31_3f_read_middle */
	{
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31),
		FPU_DLY(0xC),
		FPU_ADR_GEN, FPU_CMD(0x78), FPU_ADR(3), FPU_CMD(0x3F), FPU_ADR_GEN, FPU_NOP, FPU_NOP, FPU_NOP, FPU_END,
	},

	/* fpu_entry_tsb_bics4_aipr_tlc_31_3f_read_upper */
	{
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31),
		FPU_DLY(0xC),
		FPU_ADR_GEN, FPU_CMD(0x78), FPU_ADR(3), FPU_CMD(0x3F), FPU_ADR_GEN, FPU_NOP, FPU_NOP, FPU_NOP, FPU_END,
	},

	/* fpu_entry_tsb_bics4_aipr_tlc_3f_31_read_low */
	{
		FPU_ADR_GEN, FPU_CMD(0x78), FPU_ADR(3), FPU_CMD(0x3F), FPU_NOP,
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x01), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_ADR_GEN, FPU_NOP, FPU_NOP, FPU_END,
	},

	/* fpu_entry_tsb_bics4_aipr_tlc_3f_31_read_middle */
	{
		FPU_ADR_GEN, FPU_CMD(0x78), FPU_ADR(3), FPU_CMD(0x3F), FPU_NOP,
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x02), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_ADR_GEN, FPU_NOP, FPU_NOP, FPU_END,
	},

	/* fpu_entry_tsb_bics4_aipr_tlc_3f_31_read_upper */
	{
		FPU_ADR_GEN, FPU_CMD(0x78), FPU_ADR(3), FPU_CMD(0x3F), FPU_NOP,
		FPU_DLY(0xC),
		TOSHIBA_FAST_READ_PREFIX, FPU_ADR_GEN, FPU_CMD(0x03), FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x31), FPU_ADR_GEN, FPU_NOP, FPU_NOP, FPU_END,
	},

	/* fpu_entry_tsb_bics4_aipr_slc_1p_cache_end */
	{
		//No FPU_END
		RD_SLC_PREFIX,
	},
	/* fpu_entry_tsb_bics4_aipr_1p_cache_end */
	{
		FPU_ADR_GEN, FPU_CMD(0x78), FPU_ADR(3), FPU_CMD(0x3F), FPU_NOP, FPU_NOP, FPU_END,
	},

	/* fpu_entry_tsb_bics4_aipr_slc_2p_cache_end */
	{
		//No FPU_END
		RD_SLC_PREFIX,
	},
	/* fpu_entry_tsb_bics4_aipr_2p_cache_end */
	{
		FPU_ADR_GEN, FPU_CMD(0x78), FPU_ADR(3), FPU_CMD(0x3F),
		FPU_DLY(0xC),
		FPU_ADR_GEN, FPU_CMD(0x78), FPU_ADR(3), FPU_CMD(0x3F), FPU_ADR_GEN, FPU_NOP, FPU_NOP, FPU_END,
	},
	/*  BICS4_AIPR_FEATURE_EN */
	/* fpu_slc_sb_negative_read */
	{
		RD_SLC_PREFIX,
		FPU_ADR_GEN, FPU_CMD(0x00), FPU_ADR(5), FPU_CMD(0x3C), FPU_NOP, FPU_NOP, FPU_END,
	},
#if ICE_MODE_EN
	/* fpu_entry_dummy */
	{
		FPU_END,
	},
#endif

	/* fpu_entry_ctrl_re_low */
	{
		FPU_GPO(0x80), FPU_NOP, FPU_NOP, FPU_END,
	},

};
#endif /*((CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_2D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_TOSHIBA_3D_TLC) || (CONFIG_FLASH_TYPE == FLASH_TYPE_SANDISK_3D_TLC))*/

