#ifndef _I2CM_API_H_
#define _I2CM_API_H_

#include "i2cm_reg.h"
#include "aom/aom_api.h"

#define I2C_TRIGGER_TIMEOUT_MILLISECOND	10000
#define I2C_REG_GROUP_OFFSET	0x20

enum I2C_GROUP {
	I2C_GROUP_1 = 0,
	I2C_GROUP_2,
	I2C_GROUP_3,
	I2C_GROUP_4,
};

typedef struct i2c_ctrl_param {
	U8  ubGroup;
	U8  ubOp_cnt;
	U16 uwSlave_addr;
	U8  ubData_cnt;
	U8  ubMode;
	U8  ubRSV;
	U8  ubOp[8];
	U32 *ulData_addr;

} I2C_CTRL_PARAM, *I2C_CTRL_PARAM_PTR;

#define M_I2CM_GROUP_OFFSET(X)                  	((X) * I2C_REG_GROUP_OFFSET)
#define M_I2CM_GROUP_SLAVE_ADDR(GP,ADDR)    		(R16_I2CM[R16_I2CM_GP1_SLAVE_ADDR + (M_I2CM_GROUP_OFFSET(GP) >> 1)] = (ADDR) & 0x01FF)
#define	M_I2CM_GROUP_MASTER_CODE(GP,CODE)   		(R16_I2CM[R16_I2CM_GP1_MASTER_CODE + (M_I2CM_GROUP_OFFSET(GP) >> 1)] = (CODE) & 0xFF)
#define	M_I2CM_GROUP_OPERATION_REG(GP,ID,OP)		(R8_I2CM[R8_I2CM_GP1_OP_REG0 + M_I2CM_GROUP_OFFSET(GP) + (ID)] = (OP))
#define	M_I2CM_GROUP_CTRL(GP,MODE,DATA,OPERATION)	(R32_I2CM[R32_I2CM_GP1_CTRL + (M_I2CM_GROUP_OFFSET((GP))>>2)] = (((MODE)<<GP_MODE_SHIFT)|((DATA)<<GP_DATA_CNT_SHIFT)|((OPERATION)<<GP_OP_CNT_SHIFT)))
#define	M_I2CM_SET_BUF0(x)							(R8_I2CM[R8_I2CM_BUF0_REG] = (x))
#define	M_I2CM_GET_BUF0()							(R32_I2CM[R32_I2CM_BUF0_REG])
#define	M_I2CM_GET_BUF1()							(R32_I2CM[R32_I2CM_BUF1_REG])
#define M_I2CM_GET_START_BIT(Group_offset)		    (R32_I2CM[R32_I2CM_GP1_CTRL + (Group_offset >> 2)] & GP_I2C_START_BIT)
#define M_I2CM_SET_START_BIT(Group_offset)		    (R32_I2CM[R32_I2CM_GP1_CTRL + (Group_offset >> 2)] |= GP_I2C_START_BIT)

AOM_LPM U8 I2CScanSlaveTriggerProcess(I2C_CTRL_PARAM_PTR i2c_param);
AOM_LPM U8 I2CTriggerProcess(I2C_CTRL_PARAM_PTR i2c_param);
AOM_LPM U8 I2CIsAddrNACK(I2C_CTRL_PARAM_PTR i2c_param);
AOM_LPM U8 I2CIsDataNACK(I2C_CTRL_PARAM_PTR i2c_param);
AOM_LPM U8 I2CConfig(I2C_CTRL_PARAM_PTR i2c_param, U8 ubGroup, U16 uwSlave_addr, U8 ubMode, U8 ubData_cnt, U8 ubOp_cnt);
AOM_LPM U8 I2COperation(I2C_CTRL_PARAM_PTR i2c_param);
AOM_LPM U8 I2CDetectThermalSensor(void);
AOM_LPM U8 I2CDetectPMIC(void);

#endif /* _I2CM_API_H_ */
